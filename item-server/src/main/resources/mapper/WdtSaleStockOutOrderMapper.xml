<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtSaleStockOutOrderMapper">
    <select id="selectByStockOutOrderNo"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrder">
        select *
        from wdt_sale_stock_out_order
        where order_no = #{stockOutOrderNo}
        limit 1;
    </select>
    <select id="selectSalesOutStockPage"
            resultType="com.daddylab.supplier.item.application.salesOutStock.dto.SalesOutStockPageVO">
        select
        wssoo.stockout_id,
        wssoo.order_no,
        wssoo.warehouse_name,
        wssoo.logistics_name,
        wssoo.logistics_no,
        wssoo.shop_name,
        wssoo.consign_time,
        wssoo.status,
        wssoo.trade_no
        from wdt_sale_stock_out_order wssoo
        <where>
            <if test="query.orderNo != null and query.orderNo != ''">
                and wssoo.order_no = #{query.orderNo}
            </if>
            <if test="query.warehouseNo != null and query.warehouseNo != ''">
                and wssoo.warehouse_no = #{query.warehouseNo}
            </if>
            <if test="query.shopName != null and query.shopName != ''">
                and wssoo.shop_name = #{query.shopName}
            </if>

            <if test="query.tradeNos != null and query.tradeNos.size() > 0">
                and wssoo.trade_no in
                <foreach collection="query.tradeNos" item="tradeNo" open="(" separator="," close=")">
                    #{tradeNo}
                </foreach>
            </if>
            <if test="query.status != null and query.status != ''">
                and wssoo.status = #{query.status}
            </if>
            <if test="query.logisticsName != null and query.logisticsName != ''">
                and wssoo.logistics_name = #{query.logisticsName}
            </if>
            <if test="query.logisticsNo != null and query.logisticsNo != ''">
                and wssoo.logistics_no = #{query.logisticsNo}
            </if>
            <if test="query.stockOutIds != null and query.stockOutIds.size() > 0">
                and wssoo.stockout_id in
                <foreach collection="query.stockOutIds" item="stockOutId" open="(" separator="," close=")">
                    #{stockOutId}
                </foreach>
            </if>
        </where>
        order by wssoo.consign_time desc
    </select>

    <select id="selectSalesOutStockBaseInfo"
            resultType="com.daddylab.supplier.item.application.salesOutStock.dto.SalesOutStockDetailBaseVO">
        select wssoo.order_no,
               wssoo.warehouse_name,
               wssoo.shop_name,
               wssoo.logistics_name,
               wssoo.logistics_no,
               wssoo.consign_time,
               wssoo.trade_time,
               wssoo.pay_time,
               wssoo.status,
               wssoo.trade_no,
               wssoo.weight,
               wssoo.post_amount,
               wssoo.receivable,
               wssoo.goods_count,
               wssoo.block_reason,
               wo.checker_name,
               wo.check_time,
               wo.buyer_nick,
               wo.receiver_name,
               wo.receiver_mobile,
               wo.receiver_telno,
               wo.receiver_address,
               wo.receiver_zip,
               wo.buyer_message,
               wo.cs_remark
        from wdt_sale_stock_out_order wssoo
                 JOIN wdt_order wo ON wo.trade_no = wssoo.trade_no
        where wssoo.stockout_id = #{stockoutId}
    </select>

    <select id="selectSalesOutStockDetailList"
            resultType="com.daddylab.supplier.item.application.salesOutStock.dto.SalesOutStockDetailListVO">
        select spec_no,
        spec_name,
        goods_no,
        sell_price,
        goods_count,
        unit_name,
        total_amount,
        goods_name
        <!--           category.name as `category`,-->
        <!--           brand.name    as `brand`,-->
        <!--           item.name     as `itemName`,-->
        <!--           item.code     as `itemCode`,-->
        <!--            img.image_url as imgUrl-->
        from wdt_sale_stock_out_order_details wssood
        <!--            left join item item on wssood.goods_no = item.code-->
        <!--            left join category category on item.category_id = category.id-->
        <!--            left join brand brand on item.brand_id = brand.id-->
        <!--            left join item_image img on img.item_id = item.id-->
        where stockout_id = #{stockoutId}
        <!--        and item.is_del = 0 and category.is_del = 0 and brand.is_del = 0 and img.is_del = 0-->
        <!--        and img.type = 1 and img.is_main = 1-->
    </select>

    <select id="selectLogisticsName" resultType="java.lang.String">
        select logistics_name
        from (
        select logistics_name from wdt_sale_stock_out_order group by logistics_name) tmp
        <where>
            <if test="param.name != '' and param.name != null">
                logistics_name like CONCAT('%',#{param.name},'%')
            </if>
        </where>
    </select>

    <select id="countSalesOutStockPage" resultType="java.lang.Integer">
        SELECT COUNT(1)
        from wdt_sale_stock_out_order wssoo
        <where>
            <if test="query.orderNo != null and query.orderNo != ''">
                and wssoo.order_no = #{query.orderNo}
            </if>
            <if test="query.warehouseNo != null and query.warehouseNo != ''">
                and wssoo.warehouse_no = #{query.warehouseNo}
            </if>
            <if test="query.shopName != null and query.shopName != ''">
                and wssoo.shop_name = #{query.shopName}
            </if>

            <if test="query.tradeNos != null and query.tradeNos.size() > 0">
                and wssoo.trade_no in
                <foreach collection="query.tradeNos" item="tradeNo" open="(" separator="," close=")">
                    #{tradeNo}
                </foreach>
            </if>
            <if test="query.status != null and query.status != ''">
                and wssoo.status = #{query.status}
            </if>
            <if test="query.logisticsName != null and query.logisticsName != ''">
                and wssoo.logistics_name = #{query.logisticsName}
            </if>
            <if test="query.logisticsNo != null and query.logisticsNo != ''">
                and wssoo.logistics_no = #{query.logisticsNo}
            </if>
            <if test="query.stockOutIds != null and query.stockOutIds.size() > 0">
                and wssoo.stockout_id in
                <foreach collection="query.stockOutIds" item="stockOutId" open="(" separator="," close=")">
                    #{stockOutId}
                </foreach>
            </if>
        </where>
        limit 100000;
    </select>
</mapper>
