<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.UserVisitDailyMapper">
    <insert id="visitDaily">
        INSERT INTO `user_visit_daily` (`user_id`, `date`, created_at, created_uid) VALUE (#{userId},
                                                                                           #{time,jdbcType=TIMESTAMP},
                                                                                           unix_timestamp(),
                                                                                           #{userId})
        ON DUPLICATE KEY UPDATE `updated_at` = UNIX_TIMESTAMP()
    </insert>
</mapper>
