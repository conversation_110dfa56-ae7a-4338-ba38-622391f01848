<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseSingleSkuCombinationPriceMapper">
    <select id="getByTimeAndType"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseSingleSkuCombinationPrice">
        select *
        from purchase_single_sku_combination_price
        where is_del = 0
          and start_time &gt;= #{startTime}
          and end_time &lt;= #{endTime}
          and type = #{type}
    </select>
</mapper>
