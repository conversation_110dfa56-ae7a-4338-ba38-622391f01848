<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemProcurementMapper">


    <select id="selectOneByItemId"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemProcurement">
        SELECT * FROM item_procurement WHERE item_id = #{itemId} AND is_del = 0
    </select>

</mapper>
