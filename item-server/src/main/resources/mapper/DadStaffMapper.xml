<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DadStaffMapper">
    <select id="queryNickNameByLoginName" resultType="java.lang.String">
        select nickname
        from dad_staff
        where login_name = #{loginName}
    </select>
</mapper>
