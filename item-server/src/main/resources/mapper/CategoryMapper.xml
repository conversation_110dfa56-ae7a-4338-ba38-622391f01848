<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CategoryMapper">

    <select id="getLeafCategory"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Category">
        SELECT t2.*
        FROM item AS t1
                 INNER JOIN category AS t2 ON (t1.category_id = t2.id)
        WHERE t1.code = #{itemNo}
          AND t1.is_del = 0 AND t2.is_del = 0
    </select>
    <select id="getById" resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Category">
        SELECT * FROM category WHERE id = #{id} AND is_del = 0
    </select>

    <update id="resetPath">
        <if test="level == 1">
            UPDATE `category` SET `path` = `name` WHERE `level` = 1;
        </if>
        <if test="level > 1">
            UPDATE `category` `c1` JOIN `category` `c2` ON `c1`.`parent_id` = `c2`.`id` SET `c1`.`path` =
            CONCAT(`c2`.`path`, '/', `c1`.`name`) WHERE `c1`.`level` = #{level};
        </if>
    </update>

    <update id="replacePath">
        update category set path = replace(path, #{beforePath}, #{afterPath}) where path like concat(#{beforePath}, '%')
    </update>
</mapper>
