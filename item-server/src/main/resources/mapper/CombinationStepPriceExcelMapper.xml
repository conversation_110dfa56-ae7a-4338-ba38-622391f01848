<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CombinationStepPriceExcelMapper">
    <select id="queryPage"
            parameterType="com.daddylab.supplier.item.application.purchase.combinationPrice.PurchaseStepPricePageQuery"
            resultType="com.daddylab.supplier.item.application.purchase.combinationPrice.sheet.CombinationStepPriceExcelVo">
        SELECT cspe.*, sku.item_id AS itemId
        FROM combination_step_price_excel cspe
        LEFT JOIN item_sku sku ON sku.sku_code = cspe.sku_code
        WHERE cspe.is_del = 0
        AND sku.is_del = 0
        <if test="pageQuery.itemName != '' and  pageQuery.itemName != null">
            AND cspe.product_name LIKE CONCAT('%', #{pageQuery.itemName}, '%')
        </if>
        <if test="pageQuery.skuCode != '' and pageQuery.skuCode != null">
            AND cspe.sku_code = #{pageQuery.skuCode}
        </if>
        <if test="pageQuery.provider != '' and pageQuery.provider != null">
            AND cspe.supplier LIKE CONCAT('%', #{pageQuery.provider}, '%')
        </if>
        <if test="pageQuery.buyer != '' and pageQuery.buyer != null">
            AND cspe.purchaser LIKE CONCAT('%', #{pageQuery.buyer}, '%')
        </if>
        <if test="pageQuery.buyerId != '' and pageQuery.buyerId != null">
            AND cspe.purchaser LIKE CONCAT('%', #{pageQuery.buyerId}, '%')
        </if>
        <if test="pageQuery.favourableType != '' and pageQuery.favourableType != null">
            AND cspe.discount_type = #{pageQuery.favourableType}
        </if>
        <if test="pageQuery.platformType != '' and pageQuery.platformType != null">
            AND cspe.platform_name = #{pageQuery.platformType}
        </if>
        <if test="pageQuery.startTime != 0 and pageQuery.startTime != null">
            AND cspe.activity_start_time &gt;= #{pageQuery.startTime}
        </if>
        <if test="pageQuery.endTime != 0 and pageQuery.endTime != null">
            AND cspe.activity_end_time &lt;= #{pageQuery.endTime}
        </if>
        <if test="pageQuery.isActive != '' and pageQuery.isActive != null">
            AND cspe.is_activity_only = #{pageQuery.isActive}
        </if>
        <if test='pageQuery.activeType != "" and pageQuery.activeType != null and "无"!=pageQuery.activeType'>
            AND cspe.way = #{pageQuery.activeType}
        </if>
        <if test="pageQuery.priceCategory != null">
            <choose>
                <when test="pageQuery.priceCategory == '日常阶梯供价'">
                    AND cspe.activity_start_time is null and cspe.activity_end_time is null
                </when>
                <when test="pageQuery.priceCategory == '活动阶梯供价'">
                    AND cspe.activity_start_time is not null and cspe.activity_end_time is not null
                </when>
            </choose>
        </if>
        <if test="pageQuery.corpType != null and pageQuery.corpType.size() != 0">
            AND EXISTS (
            SELECT 1
            FROM biz_level_division bld
            WHERE
            bld.is_del = 0
            AND bld.type = 0
            AND bld.level = 0
            AND bld.biz_id = sku.item_id
            AND bld.level_val IN
            <foreach collection="pageQuery.corpType" item="val" open="(" separator="," close=")">
                #{val}
            </foreach>
            )
        </if>
        <if test="pageQuery.bizType != null and pageQuery.bizType.size() != 0">
            AND EXISTS (
            SELECT 1
            FROM biz_level_division bld
            WHERE
            bld.is_del = 0
            AND bld.type = 0
            AND bld.level = 1
            AND bld.biz_id = sku.item_id
            AND bld.level_val IN
            <foreach collection="pageQuery.bizType" item="val" open="(" separator="," close=")">
                #{val}
            </foreach>
            )
        </if>
        ORDER BY cspe.id DESC
    </select>
</mapper>
