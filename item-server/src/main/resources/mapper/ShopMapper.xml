<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ShopMapper">
    <sql id="whereOfBusinessLine">
        <!--@ignoreSql-->
        <if test="businessLine != null and businessLine.size() != 0">
            AND
            <foreach item="lineId" index="index" collection="businessLine" open="(" separator="OR" close=")">
                is_business_line${lineId} = 1
            </foreach>
        </if>
    </sql>
    <select id="dropDownList" resultType="com.daddylab.supplier.item.domain.shop.dto.ShopDropDownItem">
        SELECT `name`,`id`,`sn`,`business_line`,platform FROM shop
        <where>
            is_del = 0
            <if test="id != null and id > 0">
                AND id = #{id}
            </if>
            <if test="name != null and name != ''">
                AND `name` LIKE concat('%', #{name}, '%')
            </if>
            <include refid="whereOfBusinessLine"/>
            <if test="snList != null">
                AND sn in
                <foreach collection="snList" item="sn" open="(" separator="," close=")">
                    #{sn}
                </foreach>
            </if>
        </where>
        ORDER BY id LIMIT #{offset},#{pageSize}
    </select>

    <select id="queryShopList" resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Shop">
        SELECT `shop`.`id`,
        `shop`.`sn`,
        `shop`.`name`,
        `shop`.`account`,
        `shop`.`link`,
        `shop`.`logo`,
        `shop`.`platform`,
        `shop`.`status`,
        `shop`.`deleted_at`,
        `shop`.`created_at`,
        `shop`.`created_uid`,
        `shop`.`updated_at`,
        `shop`.`updated_uid`,
        `shop`.`is_del`,
        `shop`.business_line,
        `shop`.is_business_line0,
        `shop`.is_business_line1,
        `shop`.is_business_line2,
        `shop`.is_business_line3,
        `shop`.is_business_line3,
        `shop`.auto_allocate_inventory,
        `sis`.inventory_mode,
        `shop`.main_company
        FROM shop `shop` left join shop_inventory_setting `sis`
        on `shop`.id = `sis`.shop_id
        <where>
            <include refid="whereOfQueryShop"/>
            <include refid="whereOfBusinessLine"/>
        </where>
        ORDER BY `shop`.id DESC
        LIMIT #{offset},#{pageSize}
    </select>

    <sql id="whereOfQueryShop">
        <!--@sql SELECT * FROM shop
        LEFT JOIN shop_inventory_setting `sis` ON `shop`.id = `sis`.shop_id
        WHERE -->
        `shop`.`is_del` = 0
        <if test="id != null">
            AND `shop`.`id` = #{id}
        </if>
        <if test="name != null and name != ''">
            AND `shop`.`name` LIKE concat('%', #{name}, '%')
        </if>
        <if test="status != null">
            AND `shop`.`status` = #{status}
        </if>
        <if test="platform != null">
            AND `shop`.`platform` = #{platform}
        </if>
        <if test="sn != null and sn != ''">
            AND `shop`.`sn` = #{sn}
        </if>
        <if test="autoAllocateInventory != null">
            AND `shop`.`auto_allocate_inventory` = #{autoAllocateInventory}
        </if>
        <if test="inventoryMode != null">
            AND `sis`.`inventory_mode` = #{inventoryMode.value}
        </if>
        <if test="mainCompany != null and mainCompany != ''">
            AND `shop`.`main_company` = #{mainCompany}
        </if>
    </sql>

    <select id="countShopList" resultType="int">
        SELECT count(`shop`.`id`)
        FROM shop `shop` left join shop_inventory_setting `sis`
        on `shop`.id = `sis`.shop_id
        <where>
            <include refid="whereOfQueryShop"/>
            <include refid="whereOfBusinessLine"/>
        </where>
    </select>
</mapper>
