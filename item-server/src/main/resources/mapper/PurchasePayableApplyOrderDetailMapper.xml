<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchasePayableApplyOrderDetailMapper">
    <select id="countSkuCountByPurchasePayableOrderNo" resultType="java.lang.Integer">
        select count(1)
        from purchase_payable_apply_order_detail dd
                 inner join purchase_payable_apply_order oo on oo.no = dd.apply_order_no
            and dd.purchase_pay_order_no = #{no}
            and oo.status in (1, 2, 3, 4, 5)
            and dd.is_del = 0
            and oo.is_del = 0
    </select>
</mapper>
