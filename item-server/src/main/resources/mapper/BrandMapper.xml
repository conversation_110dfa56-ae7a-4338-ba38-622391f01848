<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.BrandMapper">

    <select id="dropDownList" resultType="com.daddylab.supplier.item.domain.brand.dto.BrandDropDownItem">
        SELECT `name`,`id`,`status` FROM brand
        <where>
            `is_del` = 0
            <if test="name != null and name != ''">
                AND `name` LIKE concat('%', #{name}, '%')
            </if>
            <if test="id != null and id != ''">
                AND `id` = #{id}
            </if>
            <include refid="whereOfBusinessLine"/>
        </where>
        LIMIT #{offset},#{pageSize}
    </select>

        <resultMap id="BrandListItem" type="com.daddylab.supplier.item.domain.brand.dto.BrandListItem" autoMapping="true">
        <id property="id" column="id"/>
        <collection property="providers" ofType="com.daddylab.supplier.item.domain.brand.vo.BrandProviderVo">
            <result property="providerId" column="provider_id"/>
            <result property="providerName" column="provider_name"/>
        </collection>
    </resultMap>

    <sql id="whereOfBrandAndProviderNotDel">
        <trim>
            AND `brand`.`is_del` = 0 AND (`brand_provider`.`id` IS NULL OR `brand_provider`.`is_del` = 0)
        </trim>
    </sql>

    <sql id="whereOfBrandNotDel">
        <trim>
            AND `brand`.`is_del` = 0
        </trim>
    </sql>

    <sql id="whereOfQueryBrandList">
        <if test="id != null and id > 0">
            AND `brand`.`id` = #{id}
        </if>
        <if test="name != null and name != ''">
            AND `brand`.`name` LIKE concat('%', #{name}, '%')
        </if>
        <if test="status != null">
            AND `brand`.`status` = #{status}
        </if>
        <if test="providerId != null and providerId > 0">
            AND `brand_provider`.`provider_id` = #{providerId}
        </if>
        <if test="sn != null and sn != ''">
            AND `brand`.`sn` = #{sn}
        </if>
    </sql>

    <select id="countOfQueryBrandList" resultType="integer">
        SELECT count(`brand`.`id`)
        FROM `brand`
        LEFT JOIN `brand_provider` ON `brand_provider`.`brand_id` = `brand`.`id`
        LEFT JOIN `provider` ON `brand_provider`.`provider_id` = `provider`.`id`
        <where>
            <include refid="whereOfBrandNotDel"/>
            <include refid="whereOfQueryBrandList"/>
            AND (`provider`.`id` IS NULL OR `provider`.`is_del` = 0)
        </where>
    </select>

    <sql id="whereOfBusinessLine">
        <!--@ignoreSql-->
        <if test="businessLine != null and businessLine.size() != 0">
                AND
                <foreach item="lineId" index="index" collection="businessLine" open="(" separator="OR" close=")">
                    `brand`.is_business_line${lineId} = 1
                </foreach>
            </if>
    </sql>
    <select id="queryBrandList" resultMap="BrandListItem">
        SELECT `brand`.`id`,
        `brand`.`sn`,
        `brand`.`name`,
        `brand`.`logo`,
        `brand`.`status`,
        `provider`.`id` AS `provider_id`,
        `provider`.`partner_provider_id` AS `partner_provider_id`,
        `provider`.`name` AS `provider_name`,
        `brand`.`business_line` as `businessLineStr`
        FROM `brand`
        LEFT JOIN `brand_provider` ON `brand_provider`.`brand_id` = `brand`.`id`
        LEFT JOIN `provider` ON `brand_provider`.`provider_id` = `provider`.`id`
        <where>
            <include refid="whereOfBrandAndProviderNotDel"/>
            <include refid="whereOfQueryBrandList"/>
            AND (`provider`.`id` IS NULL OR `provider`.`is_del` = 0)
            <include refid="whereOfBusinessLine"/>
        </where>
        ORDER BY `brand`.`id` DESC
        LIMIT #{offset},#{pageSize}
    </select>
</mapper>
