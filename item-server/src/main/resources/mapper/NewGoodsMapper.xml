<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.NewGoodsMapper">
    <select id="count" resultType="java.lang.Integer">
        SELECT count(1)
        <include refid="newGoods"/>
    </select>

    <select id="queryPage" resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods">
        select
        ng.*
        <include refid="newGoods"/>
        order by ng.created_at desc
        limit #{queryPage.offsetVal},#{queryPage.pageSize}
    </select>

    <sql id="newGoods">
        from new_goods ng
        where
        ng.is_del = 0
        <if test="queryPage.skuCode != null and queryPage.skuCode != ''">
            and ng.sku_code = #{queryPage.skuCode}
        </if>
        <if test="queryPage.name != null and queryPage.name != ''">
            and ng.name = #{queryPage.name}
        </if>
        <if test="queryPage.buyerId != null and queryPage.buyerId != ''">
            and ng.buyer_id = #{queryPage.buyerId}
        </if>
        <if test="queryPage.principalId != null  and queryPage.principalId != ''">
            and ng.principal_id = #{queryPage.principalId}
        </if>
        <if test="queryPage.startTime != null and queryPage.startTime != ''">
            AND ng.shelf_time &gt;= #{queryPage.startTime}
        </if>
        <if test="queryPage.endTime != null and queryPage.endTime != ''">
            AND ng.shelf_time &lt;= #{queryPage.endTime}
        </if>
    </sql>

    <select id="selectNoticeDOList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.NewGoodsQcDO">
        select
        item_id,
        qc_ids
        from
        new_goods
        where
        is_del = 0
        and item_id in
        <foreach item="itemId" index="index" collection="itemIdList" open="(" close=")" separator=",">
            #{itemId}
        </foreach>
    </select>

    <select id="countGroupBySpu" resultType="int">
        select count(1)
        from
    </select>

    <select id="selectDtoBatchBySkuCodes"
            resultType="com.daddylab.supplier.item.controller.item.dto.SkuWithNewGoodsDto">
        select
        sku_code as skuCode,
        daily_price as dailyPrice,
        active_price as activePrice,
        line_price as linePrice
        from new_goods
        where sku_code in
        <foreach item="skuCode" index="index" collection="skuCodes"
                 open="(" separator="," close=")">
            #{skuCode}
        </foreach>
        and is_del = 0
    </select>


    <sql id="wherePageQuery">
        from new_goods ng
        where
        ng.is_del = 0
        <if test="queryPage.skuCode != null and queryPage.skuCode != ''">
            and ng.sku_code = #{queryPage.skuCode}
        </if>
        <if test="queryPage.name != null and queryPage.name != ''">
            and ng.name = #{queryPage.name}
        </if>
        <if test="queryPage.buyerId != null and queryPage.buyerId != ''">
            and ng.buyer_id = #{queryPage.buyerId}
        </if>
        <if test="queryPage.principalId != null  and queryPage.principalId != ''">
            and ng.principal_id = #{queryPage.principalId}
        </if>
        <if test="queryPage.startTime != null and queryPage.startTime != ''">
            AND ng.shelf_time &gt;= #{queryPage.startTime}
        </if>
        <if test="queryPage.endTime != null and queryPage.endTime != ''">
            AND ng.shelf_time &lt;= #{queryPage.endTime}
        </if>
    </sql>

    <select id="getNewGoodsPrincipalsInfo" resultType="com.daddylab.supplier.item.application.saleItem.vo.NewGoodsPrincipalsInfoPO">
        SELECT `ng`.`item_id`                 AS `itemId`,
               ANY_VALUE(`ng`.`principal_id`) AS `principalId`,
               ANY_VALUE(`ng`.`legal_id`)     AS `legalId`,
               ANY_VALUE(`b`.`user_id`)       AS `buyerId`,
               ANY_VALUE(`ip`.`qc_ids`)       AS `qcIds`,
               COUNT(DISTINCT `ng`.`id`)      AS `newGoodsNum`
        FROM `item` `i`
                 LEFT JOIN
             `new_goods` `ng`
             ON `ng`.`item_id` = `i`.`id`
                 AND `ng`.`is_del` = 0
                 LEFT JOIN
             `item_procurement` `ip`
                 LEFT JOIN
             `buyer` `b`
             ON `ip`.`buyer_id` = `b`.`id`
             ON `i`.`id` = `ip`.`item_id`
        WHERE `i`.`id` IN
        <foreach item="item" index="index" collection="itemIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY `i`.`id`
        ;
    </select>

    <select id="getQcProcessors" resultType="com.daddylab.supplier.item.types.item.ItemQcProcessorsVO">
        select qc_ids as qcIds
        from item_procurement
        where item_id = #{itemId}
          and is_del = 0
        limit 1;
    </select>

    <select id="staticsCountByStatus" resultType="java.util.Map">
        select status, count(1)
        from new_goods
        where is_del = 0
        and updated_at between ${startTime} and ${endTime}
        and status in
        <foreach collection="statusList" index="status" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
        group by status;
    </select>

</mapper>
