<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AwsBusinessLogMapper">
    <select id="getByProcessInstId"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AwsBusinessLog">
        SELECT *
        FROM aws_business_log
        WHERE process_id = #{processInstId}
        ORDER BY id DESC
        LIMIT 1
    </select>
</mapper>
