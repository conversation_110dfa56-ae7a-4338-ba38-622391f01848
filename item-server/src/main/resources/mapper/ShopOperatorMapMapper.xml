<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ShopOperatorMapMapper">
    <select id="shopOperatorMapPageQuery"
            resultType="com.daddylab.supplier.item.application.shop.domain.ShopOperatorMapVO">
        SELECT `som`.`id`,
               `som`.`shop_id`,
               `som`.`operator_uid`,
               `s`.`name` AS `shop_name`,
               `s`.`sn`   AS `shop_no`,
               `s`.`business_line`
        FROM `shop_operator_map` `som`
                 JOIN `shop` `s` ON `s`.`id` = `som`.`shop_id` AND `s`.`is_del` = 0
        <where>
            som.`is_del` = 0
            <if test="name != null and name != ''">
                AND `s`.`name` LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="uid != null">
                AND `operator_uid` = #{uid}
            </if>
        </where>
        order by som.id desc
    </select>
</mapper>
