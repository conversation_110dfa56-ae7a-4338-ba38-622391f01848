<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseMapper">
    <select id="queryExport" resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Purchase">
        select
        purchase.*,
        FROM purchase as purchase
        <where>
            purchase.is_del = 0
            <if test="param.itemName != '' and null != param.itemName">
                item_name = #{param.itemName}
            </if>
            <if test="param.itemSku != '' and null != param.itemSku">
                and item_sku = #{param.itemSku}
            </if>
            <if test="param.provider != '' and null != param.provider">
                and provider = #{param.provider}
            </if>
            <if test="param.favourableType != '' and null != param.favourableType">
                and favourable_type = #{param.favourableType}
            </if>
            <if test="param.isActive != '' and null != param.isActive">
                and is_active = #{param.isActive}
            </if>
            <if test="param.platformType != '' and null != param.platformType">
                and platform_type = #{param.platformType}
            </if>
            <if test="param.activeType != '' and null != param.activeType">
                and active_type = #{param.activeType}
            </if>
            <if test="param.status != '' and null != param.status">
                and status = #{param.status}
            </if>
            <if test="param.month != '' and null != param.month">
                and month = #{param.month}
            </if>
            <if test="null != param.showAll and param.showAll == false and null != param.currentUserId and param.currentUserId != '' ">
                and buyer_id = #{param.currentUserId}
            </if>
            <if test="null != param.showAll and param.showAll == true and param.buyerId != null and param.buyerId != '' ">
                and buyer_id = #{param.BuyerId}
            </if>
            <if test="param.corpType != null and !param.corpType.empty">
                exists (
                select 1 from biz_level_division as bld
                AND bld.type = 0 AND bld.level = 0
                AND bld.level_val IN
                <foreach item="item" index="index" collection="param.corpType" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="param.bizType != null and !param.bizType.empty">
                exists (
                select 1 from biz_level_division as bld
                AND bld.type = 0 AND bld.level = 1
                AND bld.level_val IN
                <foreach item="item" index="index" collection="bizType" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        </where>
        order by purchase.sort desc
        limit #{param.offsetVal},#{param.pageSize}
    </select>

    <select id="countExport" resultType="java.lang.Integer">
        select count(1) from purchase
        <where>
            <if test="param.itemName != '' and null != param.itemName">
                item_name = #{param.itemName}
            </if>
            <if test="param.itemSku != '' and null != param.itemSku">
                and item_sku = #{param.itemSku}
            </if>
            <if test="param.provider != '' and null != param.provider">
                and provider = #{param.provider}
            </if>
            <if test="param.favourableType != '' and null != param.favourableType">
                and favourable_type = #{param.favourableType}
            </if>
            <if test="param.isActive != '' and null != param.isActive">
                and is_active = #{param.isActive}
            </if>
            <if test="param.platformType != '' and null != param.platformType">
                and platform_type = #{param.platformType}
            </if>
            <if test="param.activeType != '' and null != param.activeType">
                and active_type = #{param.activeType}
            </if>
            <if test="param.status != '' and null != param.status">
                and status = #{param.status}
            </if>
            <if test="param.month != '' and null != param.month">
                and month = #{param.month}
            </if>
            <if test="null != param.showAll and param.showAll == false and null != param.currentUserId and param.currentUserId != '' ">
                and buyer_id = #{param.currentUserId}
            </if>
            <if test="null != param.showAll and param.showAll == true and param.buyerId != null and param.buyerId != '' ">
                and buyer_id = #{param.buyerId}
            </if>
            <if test="param.businessLine != null and param.businessLine.size() > 0">
                and `business_line` in
                <foreach collection="param.businessLine" item="bl" open="(" separator="," close=")">
                    #{bl}
                </foreach>
            </if>

            and is_del = 0
        </where>
    </select>

    <select id="getPurchasePrice"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.PurchasePriceDO">
        select IFNULL(price_cost, 0) as priceCost,
               active_type,
               item_sku              as skuCode,
               platform_type,
               num_cost              as preferentialQuantity
        from purchase
        where is_del = 0
          and start_time &lt;= #{payTime}
          and end_time &gt;= #{payTime}
          and item_sku = #{skuCode}
          and (platform_type = #{platformType} or platform_type = 0)
        order by created_at desc
    </select>

    <update id="setDefaultSort">
        UPDATE purchase
        SET sort = id
        WHERE sort = 0;
    </update>

    <select id="getPurchasePriceByCode"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.PurchasePriceDO">
        select
        IFNULL(price_cost,0) as priceCost,active_type,item_sku as skuCode,platform_type,
        num_cost as preferentialQuantity,
        start_time as payStartTime,
        end_time as payEndTime,
        `month` as `month`
        from purchase
        where is_del = 0
        and item_sku = #{skuCode}
        and platform_type is not null
        and (start_time is not null or start_time != 0)
        and (end_time is not null or end_time != 0)
        and price_cost is not null
        order by platform_type asc , price_cost asc
        <!--        and `month` = #{payMonth}-->
    </select>

    <select id="getActivityQuantityPriceList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.PurchasePriceDO">
        select
        IFNULL(price_cost,0) as priceCost,active_type,item_sku as skuCode,platform_type,
        num_cost as preferentialQuantity,
        start_time as payStartTime,
        end_time as payEndTime,
        `month` as `month`
        from purchase
        where is_del = 0
        and month = #{month}
        <!--        平台类型不得为空-->
        and platform_type is not null
        <!--        开始和结束时间不得为空-->
        and (start_time is not null or start_time != 0)
        and (end_time is not null or end_time != 0)
        <!--        优惠价格不得为空-->
        and price_cost is not null
        <!--        优惠数量必须大于0-->
        and num_cost > 0;
    </select>

    <select id="page" resultType="com.daddylab.supplier.item.controller.purchase.dto.PurchaseVo">
        SELECT
        purchase.*
        FROM purchase purchase
        <where>
            <if test="itemName != null and itemName != ''">
                AND purchase.item_name LIKE CONCAT('%', #{itemName}, '%')
            </if>
            <if test="itemSku != null and itemSku != ''">
                AND purchase.item_sku = #{itemSku}
            </if>
            <if test="provider != null and provider != ''">
                AND purchase.provider LIKE CONCAT('%', #{provider}, '%')
            </if>
            <if test="favourableType != null">
                AND purchase.favourable_type = #{favourableType}
            </if>
            <if test="isActive != null">
                AND purchase.is_active = #{isActive}
            </if>
            <if test="platformType != null">
                AND 'purchase'.platform_type = #{platformType}
            </if>
            <if test="activeType != null">
                AND purchase.active_type = #{activeType}
            </if>
            <if test="status != null">
                AND purchase.status = #{status}
            </if>
            <if test="month != null">
                AND purchase.month = #{month}
            </if>
            <if test="!showAll and currentUserId != null">
                AND purchase.buyer_id = #{currentUserId}
            </if>
            <if test="showAll and buyerId != null">
                AND purchase.buyer_id = #{buyerId}
            </if>
            <if test="corpType != null and !corpType.empty">
                AND exists(
                SELECT 1 FROM
                biz_level_division bld
                INNER JOIN item_sku sku ON sku.item_id = bld.biz_id
                WHERE bld.type = 0 AND bld.level = 0
                AND bld.is_del = 0
                AND purchase.item_sku = sku.sku_code
                AND bld.level_val IN
                <foreach item="item" index="index" collection="corpType" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="bizType != null and !bizType.empty">
                AND exists(
                SELECT 1 FROM
                biz_level_division bld
                INNER JOIN item_sku sku ON sku.item_id = bld.biz_id
                WHERE bld.type = 0 AND bld.level = 1
                AND bld.is_del = 0
                AND purchase.item_sku = sku.sku_code
                AND bld.level_val IN
                <foreach item="item" index="index" collection="bizType" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            AND is_del = 0
        </where>
        ORDER BY purchase.sort DESC
    </select>
</mapper>
