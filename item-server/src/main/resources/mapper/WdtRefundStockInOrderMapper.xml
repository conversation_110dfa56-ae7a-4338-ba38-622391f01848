<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundStockInOrderMapper">
    <!--    <select id="selectStockInList"-->
    <!--            resultType="com.daddylab.supplier.item.application.purchase.order.factory.dto.StockInNumDO">-->
    <!--        select spec_no as skuCode, num-->
    <!--        from wdt_refund_stock_in_order_details-->
    <!--        where stockin_id in-->
    <!--              (select stockin_id-->
    <!--               from wdt_refund_stock_in_order-->
    <!--               where check_time <![CDATA[>=]]> #{start}-->
    <!--                 and check_time <![CDATA[<=]]> #{end})-->
    <!--        union all-->
    <!--        select spec_no as skuCode, num-->
    <!--        from wdt_pre_stock_in_order_details-->
    <!--        where stockin_no in-->
    <!--              (select stockin_no-->
    <!--               from wdt_pre_stock_in_order-->
    <!--               where check_time <![CDATA[>=]]> #{start}-->
    <!--                 and check_time <![CDATA[<=]]> #{end})-->
    <!--            limit #{offset}-->
    <!--            , #{size}-->
    <!--    </select>-->

    <!--<select id="selectRefundStockInCount" resultType="java.lang.Integer">
        select count(1)
        from wdt_refund_stock_in_order_details
        where stockin_id in
              (select stockin_id
               from wdt_refund_stock_in_order
               where check_time <![CDATA[>=]]> #{start}
                 and check_time <![CDATA[<=]]> #{end})
    </select>-->

    <!--<select id="selectRefundStockInList"
            resultType="com.daddylab.supplier.item.application.purchase.order.factory.dto.StockInNumDO">
        select wrsiod.spec_no as skuCode, wrsiod.num, wrsiod.stockin_id as stockInId,
            wrsio.warehouse_no
        from wdt_refund_stock_in_order_details wrsiod
        left join wdt_refund_stock_in_order wrsio
            on wrsiod.stockin_id = wrsio.stockin_id
        where wrsiod.stockin_id in
        <foreach collection="stockInIdList" item="stockInId" index="index"
                 open="(" close=")" separator=",">
            #{stockInId}
        </foreach>
    </select>-->

    <!--    <select id="selectRefundOrder"-->
    <!--            resultType="com.daddylab.supplier.item.application.purchase.order.factory.dto.OrderTradeTimeDO">-->
    <!--        select spec_no, trade_time-->
    <!--        from wdt_order_detail-->
    <!--        where src_tid in (-->
    <!--        select tid-->
    <!--        from wdt_refund_order_detail-->
    <!--        where rec_id in (select refund_order_id-->
    <!--                         from wdt_refund_stock_in_order_details_refund_order_details-->
    <!--        where stockin_id in-->
    <!--        <foreach collection="stockInIdList" item="stockInId" index="index"-->
    <!--                 open="(" close=")" separator=",">-->
    <!--            #{stockInId}-->
    <!--        </foreach>-->
    <!--        ));-->
    <!--    </select>-->

    <!--    <select id="selectPreStockInCount" resultType="java.lang.Integer">-->
    <!--        select count(1)-->
    <!--        from wdt_pre_stock_in_order_details-->
    <!--        where stockin_no in-->
    <!--              (select stockin_no-->
    <!--               from wdt_pre_stock_in_order-->
    <!--               where check_time <![CDATA[>=]]> #{start}-->
    <!--                 and check_time <![CDATA[<=]]> #{end})-->
    <!--    </select>-->

    <select id="selectPreStockInList"
            resultType="com.daddylab.supplier.item.application.purchase.order.factory.dto.StockInNumDO">
        select
            wpsiod.spec_no as skuCode, wpsiod.num,wpsio.warehouse_no
        from wdt_pre_stock_in_order_details wpsiod
        inner join wdt_pre_stock_in_order wpsio
            on wpsiod.stockin_no = wpsio.stockin_no
        where
            wpsio.check_time <![CDATA[>=]]> #{start}
            and wpsio.check_time <![CDATA[<=]]> #{end}
            and wpsio.warehouse_no not in
            <foreach collection="eliminateWarehouseNos" item="warehouseNo" open="(" separator="," close=")">
                #{warehouseNo}
            </foreach>
<!--            and wrsio.shop_no not in-->
<!--            <foreach collection="externalShopNos" item="shopNo" open="(" separator="," close=")">-->
<!--                #{shopNo}-->
<!--            </foreach>-->
    </select>

    <!--<select id="selectRefundStockInIdList" resultType="java.lang.Long">
        select stockin_id as stockInId
        from wdt_refund_stock_in_order_details
        where stockin_id in
              (
                select stockin_id
                from wdt_refund_stock_in_order
                where check_time <![CDATA[>=]]> #{start}
                and check_time <![CDATA[<=]]> #{end}
                and warehouse_no not in
                <foreach collection="eliminateWarehouseNos" item="warehouseNo" open="(" separator="," close=")">
                #{warehouseNo}
                </foreach>
              )
        group by stockin_id

    </select>-->

    <!--<select id="selectRefundStockInIdCount" resultType="java.lang.Integer">
        select count(1)
        from (select stockin_id
              from wdt_refund_stock_in_order_details
              where stockin_id in
                    (select stockin_id
                     from wdt_refund_stock_in_order
                     where check_time <![CDATA[>=]]> #{start}
                       and check_time <![CDATA[<=]]> #{end})
              group by stockin_id) tmp
    </select>-->

    <select id="selectRefundStockInList2"
            resultType="com.daddylab.supplier.item.application.purchase.order.factory.dto.StockInNumDO">
        select wrsiod.spec_no as skuCode, wrsiod.num, wrsiod.stockin_id as stockInId,
               wrsio.warehouse_no
        from wdt_refund_stock_in_order_details wrsiod
                 inner join wdt_refund_stock_in_order wrsio on wrsiod.stockin_id = wrsio.stockin_id
        where  check_time <![CDATA[>=]]> #{startTime}
          and check_time <![CDATA[<=]]> #{endTime}
        and wrsio.warehouse_no not in
        <foreach collection="eliminateWarehouseNos" item="warehouseNo" open="(" separator="," close=")">
            #{warehouseNo}
        </foreach>
        and wrsio.shop_no not in
        <foreach collection="externalShopNos" item="shopNo" open="(" separator="," close=")">
            #{shopNo}
        </foreach>
    </select>

    <select id="newRefundStockInList"
            resultType="com.daddylab.supplier.item.application.purchase.order.factory.dto.StockInDetailDO">
        select
            wrsiod.spec_no as skuCode,
            wrsiod.num,
            wrsiod.stockin_id,
            wrsio.warehouse_no,
            wod.trade_time,
            wod.platform_id,
            wod.suite_no,
            wrod.trade_no,
            wod.gift_type
        from wdt_refund_stock_in_order_details wrsiod
            inner join wdt_refund_stock_in_order wrsio on wrsiod.stockin_id = wrsio.stockin_id
            left join wdt_refund_stock_in_order_details_refund_order_details wrsiodrod
                    on wrsiod.rec_id = wrsiodrod.stockin_order_detail_id
            left join wdt_refund_order_detail wrod on wrod.rec_id = wrsiodrod.refund_order_id
            left join wdt_order_detail wod on wod.src_tid = wrod.tid
        where wrsio.check_time <![CDATA[>=]]> #{startTime}
        and wrsio.check_time <![CDATA[<=]]> #{endTime}
        and wrsio.warehouse_no not in
        <foreach collection="eliminateWarehouseNos" item="warehouseNo" open="(" separator="," close=")">
            #{warehouseNo}
        </foreach>
        and wrsio.shop_no not in
        <foreach collection="externalShopNos" item="shopNo" open="(" separator="," close=")">
            #{shopNo}
        </foreach>
        <if test="null != skuCode and '' != skuCode">
            and wrsiod.spec_no = #{skuCode}
        </if>
        group by wrsiod.id;
    </select>


    <select id="selectSalesInStockPage"
            resultType="com.daddylab.supplier.item.application.salesInStock.dto.SalesInStockPageVO">
        select  stockOrder.order_no,
                refundOrder.type,
                stockOrder.warehouse_name,
                stockOrder.operator_name,
                stockOrder.shop_name,
                stockOrder.refund_no,
                refundOrder.stockin_status,
                stockOrder.stockin_id
        from wdt_refund_stock_in_order stockOrder
                 join wdt_refund_order refundOrder on stockOrder.refund_no = refundOrder.refund_no
        <where>
            <if test="param.orderNo != null  and param.orderNo != '' ">
                stockOrder.order_no = #{param.orderNo}
            </if>
            <if test="param.warehouseNo != null  and param.warehouseNo != '' ">
                and stockOrder.warehouse_no = #{param.warehouseNo}
            </if>
            <if test="param.type != null">
                and refundOrder.type = #{param.type}
            </if>
            <if test="param.shopName != null and param.shopName != '' ">
                and stockOrder.shop_name = #{param.shopName}
            </if>
            <if test="param.stockinStatus != null">
                and refundOrder.stockin_status = #{param.stockinStatus}
            </if>
            <if test="param.refundNo != null and param.refundNo != '' ">
                and stockOrder.refund_no = #{param.refundNo}
            </if>
            <if test="param.status != null">
                and refundOrder.status = #{param.status}
            </if>
            <if test="param.tradeNo != null and param.tradeNo != '' ">
                and stockOrder.trade_no_list like concat('%',#{param.tradeNo},'%')
            </if>
            <if test="
                    (param.specCode != null and param.specCode != '') or
                    (param.goodsName != null and param.goodsName != '') or
                    (param.goodsNo != null and param.goodsNo != '')
            ">
                and stockOrder.stockin_id in (
                    select stockin_id
                    from wdt_refund_stock_in_order_details
                <where>
                    <if test="param.goodsNo != null and param.goodsNo != '' ">
                        goods_no = #{param.goodsNo}
                    </if>
                    <if test="param.specCode != null and param.specCode != '' ">
                        and spec_no = #{param.specCode}
                    </if>
                    <if test="param.goodsName != null and param.goodsName != ''">
                        and goods_name = #{param.goodsName}
                    </if>
                </where>
                )
            </if>
            <if test="param.tids !=null and param.tids.size() > 0">
                and refundOrder.tid_list in
                <foreach collection="param.tids" item="tid" open="(" separator="," close=")">
                    #{tid}
                </foreach>
            </if>
        </where>
        order by stockOrder.created_time desc
    </select>

    <select id="selectSalesInStockBase"
            resultType="com.daddylab.supplier.item.application.salesInStock.dto.SalesInStockBaseVO">
        select stockOrder.order_no,
            refundOrder.type,
            stockOrder.warehouse_name,
            stockOrder.operator_name,
            stockOrder.shop_name,
            FROM_UNIXTIME(CAST(`created_time` / 1000 AS unsigned)) as created_time,
            stockOrder.nick_name,
            stockOrder.check_time,
            stockOrder.remark
        from wdt_refund_stock_in_order stockOrder
        join wdt_refund_order refundOrder on stockOrder.refund_no = refundOrder.refund_no
        where stockOrder.order_no = #{orderNo}
    </select>

    <select id="selectSalesInStockRefundBase"
            resultType="com.daddylab.supplier.item.application.salesInStock.dto.SalesInStockRefundBaseVO">
        select refundOrder.refund_no,
               refundOrder.status,
               refundOrder.return_warehouse_no,
               refundOrder.trade_no_list,
               refundOrder.actual_refund_amount,
               refundOrder.return_goods_count,
               refundOrder.return_logistics_name,
               refundOrder.return_logistics_no,
               refundOrder.reason_name,
               refundOrder.remark,
               refundOrder.operator_name
        from wdt_refund_order refundOrder
        where refundOrder.refund_no = #{refundNo}
    </select>

    <select id="selectSalesInStockDetailList"
            resultType="com.daddylab.supplier.item.application.salesInStock.dto.SalesInStockDetailListVO">
        select spec_no,
               goods_name,
               spec_name,
               goods_no,
               right_num,
               num,
               defect,
               basic_unit_name,
               brand_name,
               prop2,
               remark
<!--                img.image_url as imgurl-->
        from wdt_refund_stock_in_order_details wrsiod
<!--            left join item item on item.code = wrsiod.goods_no-->
<!--            left join item_image img on item.id = img.item_id-->
        where stockin_id = #{stockInId}
<!--        and item.is_del = 0 and img.is_del = 0-->
<!--        and img.type = 1 and img.is_main = 1-->
    </select>

    <select id="selectOrderTradeTimeByStockInId2"
            resultType="com.daddylab.supplier.item.application.purchase.order.factory.dto.OrderTradeTimeDO">
        select wod.spec_no as skuCode
             , wod.trade_time
             , wod.platform_id
             , wod.warehouse_no
             , wod.suite_no
             , wrod.trade_no
        from wdt_order_detail wod
                 inner join wdt_refund_order_detail wrod on wod.src_tid = wrod.tid
                 inner join wdt_refund_stock_in_order_details_refund_order_details wrsiodrod
                            on wrsiodrod.stockin_id = wrod.rec_id
        where wrsiodrod.stockin_id = #{stockInId}
    </select>

    <!--<select id="selectOrderTradeTimeByStockInId"
            resultType="com.daddylab.supplier.item.application.purchase.order.factory.dto.OrderTradeTimeDO">
        select wdd.spec_no as skuCode
             , wdd.trade_time
             , wdd.platform_id
             , wdd.warehouse_no
             , tmp.trade_no
             , wdd.suite_no
        from wdt_order_detail wdd
                 inner join
             (select tid, trade_no
              from wdt_refund_order_detail
              where rec_id in (select refund_order_id
                               from wdt_refund_stock_in_order_details_refund_order_details
                               where stockin_id = #{stockInId})
             ) tmp
             on wdd.src_tid = tmp.tid
    </select>-->


</mapper>
