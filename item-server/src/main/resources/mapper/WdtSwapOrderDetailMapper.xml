<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtSwapOrderDetailMapper">
    <insert id="saveOrUpdateBatch">
        INSERT INTO wdt_swap_order_detail (id, refund_no, tid, oid, target_type, target_id, defect,
                                           goods_name, goods_no, spec_name, spec_code, merchant_no,
                                           price, total_amount, num, remark)
        VALUES
        <foreach collection="details" item="item" separator=",">
            (NULL, #{item.refundNo}, #{item.tid}, #{item.oid}, #{item.targetType}, #{item.targetId},
             #{item.defect},
             #{item.goodsName}, #{item.goodsNo}, #{item.specName}, #{item.specCode},
             #{item.merchantNo},
             #{item.price}, #{item.totalAmount}, #{item.num}, #{item.remark})
        </foreach>
        ON DUPLICATE KEY UPDATE `target_type`  = VALUES(`target_type`),
                                `target_id`    = VALUES(`target_id`),
                                `defect`       = VALUES(`defect`),
                                `goods_name`   = VALUES(`goods_name`),
                                `goods_no`     = VALUES(`goods_no`),
                                `spec_name`    = VALUES(`spec_name`),
                                `spec_code`    = VALUES(`spec_code`),
                                `merchant_no`  = VALUES(`merchant_no`),
                                `price`        = VALUES(`price`),
                                `total_amount` = VALUES(`total_amount`),
                                `num`          = VALUES(`num`),
                                `remark`       = VALUES(`remark`)
    </insert>
</mapper>
