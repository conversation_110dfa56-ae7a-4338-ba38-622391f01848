<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.HandingSheetItemSkuMapper">


    <select id="countItemSpuByHandingSheetId" resultType="int">
        select count(distinct id) from handing_sheet_item_sku where handing_sheet_id = #{handingSheetId} and is_del = 0;
    </select>
</mapper>
