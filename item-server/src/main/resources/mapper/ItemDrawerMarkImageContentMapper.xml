<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerMarkImageContentMapper">
    <select id="selectListIgnoreDeletedMarkByDrawerId"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMarkImageContent">
        SELECT *
        FROM `item_drawer_mark_image_content`
        WHERE `drawer_id` = #{drawerId};
    </select>
</mapper>
