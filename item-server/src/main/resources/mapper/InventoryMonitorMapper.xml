<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.InventoryMonitorMapper">

    <select id="selectAbsoluteThresholdAlertList"
            resultType="com.daddylab.supplier.item.application.stockSpec.InventoryMonitorAlertVO">
        select im.id as inventory_monitor_id,
               im.threshold_type,
               im.alert_threshold,
               im.warehouse_no,
               im.sku_no,
               wss.id as stock_spec_id,
               wss.stock_num,
               wss.available_stock
        from supplier.inventory_monitor im
                 left join supplier.wdt_stock_spec wss
                           on im.warehouse_no = wss.warehouse_no and im.sku_no = wss.spec_no and wss.is_del = 0
        where im.is_del = 0 and wss.defect = 0 and im.sku_no != ''
          and IFNULL(wss.available_stock, 0) <![CDATA[<=]]> im.alert_threshold
          and im.threshold_type = 1
          and im.alert_threshold >= 0
          and im.status = 1
        <if test="cursor != null">
            and im.id > #{cursor}
        </if>
        order by im.id
        limit #{limit}
        ;
    </select>
</mapper>
