<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DataFetchMapper">
    <select id="getRecordIdsBetween" resultType="java.lang.Long">
        SELECT id
        FROM data_fetch WHERE data_type = #{dataType.value}
        <if test="includeBoundary">
            AND fetch_point BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="!includeBoundary">
            <![CDATA[
            AND fetch_point > #{startTime}
            AND fetch_point < #{endTime}
            ]]>
        </if>
        AND is_del = 0
        ORDER BY fetch_point;
    </select>

    <delete id="deleteRecordsByIds">
        DELETE FROM data_fetch
        WHERE id IN
        <foreach collection="recordIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_del = 0
    </delete>

    <update id="lock">
        UPDATE data_fetch
        SET lock_time  = #{time},
            updated_at = unix_timestamp()
        WHERE id = #{id}
        <![CDATA[ AND (lock_time IS NULL OR lock_time < now()) ]]>
        AND is_del = 0
    </update>

    <update id="updateStateAndUnLock">
        UPDATE data_fetch
        SET lock_time  = NULL,
            status     = #{fetchSegment.status.value,jdbcType=INTEGER},
            err        = #{fetchSegment.err},
            data       = #{fetchSegment.dataJson,jdbcType=VARCHAR},
            updated_at = unix_timestamp()
        WHERE id = #{fetchSegment.id}
          AND is_del = 0
    </update>

    <update id="updateStateAndUnLockByRecord">
        UPDATE data_fetch
        SET lock_time  = NULL,
            status     = #{record.status},
            err        = #{record.err},
            data       = #{record.data},
            updated_at = unix_timestamp()
        WHERE id = #{record.id}
          AND is_del = 0
    </update>

    <select id="getEarliestRecord"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataFetch">
        SELECT *
        FROM data_fetch
        WHERE data_type = #{dataType.value}
          AND is_del = 0
          AND round = #{round}
          AND fetch_point IS NOT NULL
        ORDER BY fetch_point
        LIMIT 1
    </select>

    <select id="getLatestRecord"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataFetch">
        SELECT *
        FROM data_fetch
        WHERE data_type = #{dataType.value}
          AND is_del = 0
          AND round = #{round}
          AND fetch_point IS NOT NULL
        ORDER BY fetch_point DESC
        LIMIT 1
    </select>

    <resultMap id="FetchSegmentResultMap"
            type="com.daddylab.supplier.item.domain.dataFetch.FetchSegment">
        <id column="id" property="id"/>
        <result column="data_type" property="dataType"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="status" property="status"/>
        <result column="lock_time" property="lockTime"/>
        <result column="err" property="err"/>
        <result column="data" property="data"/>
    </resultMap>
    <select id="getWaitFetchSegments"
            resultMap="FetchSegmentResultMap">
        <bind name="limitPlus1" value="limit + 1"/>
        SELECT id,
               data_type,
               start_time,
               end_time,
               status,
               lock_time,
               err,
               data
        FROM (
        SELECT id,
               data_type,
               (SELECT `fetch_point`
                FROM `data_fetch` `t_start`
                WHERE `data_type` = #{dataType.value,jdbcType=INTEGER}
                  AND `t_start`.`fetch_point` <![CDATA[<]]> `t_end`.`fetch_point`
                  AND `t_start`.`is_del` = 0
                  AND round = #{round}
                ORDER BY `t_start`.`fetch_point` DESC
                LIMIT 1)     AS `start_time`,
               `fetch_point` AS `end_time`,
               `status`,
               `lock_time`,
               `err`,
               `data`
        FROM `data_fetch` `t_end`
        WHERE `data_type` = #{dataType.value,jdbcType=INTEGER}
          AND is_del = 0
          AND status = 0
          AND round = #{round}
          AND <![CDATA[ (lock_time IS NULL OR lock_time < now()) ]]>
          AND <![CDATA[ fetch_point <= #{fetchPointMax} ]]>
        ORDER BY `fetch_point`
        <if test="orderDesc">
            DESC
        </if>
        LIMIT #{limitPlus1}
        ) AS t_main
        WHERE start_time IS NOT NULL
        LIMIT #{limit}
    </select>

    <select id="getFullFetchRecord"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataFetch">
        SELECT *
        FROM data_fetch
        WHERE data_type = #{dataType.value}
          AND fetch_point IS NULL
          AND round = #{round}
          AND is_del = 0
    </select>

    <update id="updateLockTime">
        UPDATE data_fetch
        SET lock_time  = #{lockTime},
            updated_at = unix_timestamp()
        WHERE id = #{id}
          AND is_del = 0
    </update>

    <update id="updateData">
        UPDATE data_fetch
        SET data       = #{data},
            updated_at = unix_timestamp()
        WHERE id = #{id};
    </update>

    <select id="countWaitingSegments"
            resultType="Integer">
        SELECT IFNULL(count(*), 0) as count
        FROM data_fetch
        WHERE data_type = #{dataType.value} AND round = #{round}
        AND status = 0
        AND is_del = 0
    </select>

    <select id="getNthRecord"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataFetch">
        <bind name="offset" value="nth - 1"/>
        SELECT *
        FROM data_fetch
        WHERE data_type = #{dataType.value}
        AND is_del = 0
        AND round = #{round}
        AND fetch_point IS NOT NULL
        ORDER BY fetch_point
        <if test="desc">
            DESC
        </if>
        LIMIT #{offset}, 1
    </select>
</mapper>
