<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PaymentApplyOrderDetailMapper">
    <select id="getSumApplyAmount" resultType="java.math.BigDecimal">
        select ifnull(sum(paod.apply_amount), 0)
        from payment_apply_order_detail paod
        left join payment_apply_order pod
        on paod.payment_apply_order_id = pod.id
        where
        paod.is_del = 0 and pod.is_del = 0
        and paod.apply_related_no in
        <foreach collection="relateNos" item="relateNo" separator="," open="(" close=")">
            #{relateNo}
        </foreach>
        and pod.status in
        <foreach collection="statusList" item="status" separator="," open="(" close=")">
            #{status}
        </foreach>
    </select>

    <select id="getRelatedDetailDbIdByRelatedNo" resultType="java.lang.String">
        select paod.relate_detail_id
        from payment_apply_order_detail paod
        join payment_apply_order pao on paod.payment_apply_order_id = pao.id
        where paod.is_del = 0
        and pao.is_del = 0
        and paod.apply_related_no in
        <foreach collection="relateNos" item="relateNo" separator="," open="(" close=")">
            #{relateNo}
        </foreach>
        and pao.status in
        <foreach collection="statusList" item="status" separator="," open="(" close=")">
            #{status}
        </foreach>
    </select>

    <select id="getMatchedRelatedIdByStatus" resultType="java.lang.Long">
        select paod.apply_related_id
        from payment_apply_order_detail paod
        join payment_apply_order pao on paod.payment_apply_order_id = pao.id
        where paod.is_del = 0
        and pao.is_del = 0
        and pao.detail_source = #{detailSource}
        and pao.status in
        <foreach collection="statusList" item="status" separator="," open="(" close=")">
            #{status}
        </foreach>
        group by paod.apply_related_no
    </select>

    <select id="getPaymentStatusBySettlementNo"
            resultType="com.daddylab.supplier.item.application.order.settlement.dto.PaymentApplyStatusDto">
        select pao.id as paymentOrderId, pao.status as paymentStatus, osf.no as settlementNo
        from order_settlement_form osf
                 inner join payment_apply_order_detail paod on paod.apply_related_no = osf.no
                 left join payment_apply_order pao on pao.id = paod.payment_apply_order_id
        where osf.no = #{no}
          and paod.is_del = 0;
    </select>

    <select id="getWaitSubmitPaymentApplyOrderIdBySettlementOrderId" resultType="java.lang.Long">
        select oo.id
        from payment_apply_order_detail dd
                 inner join payment_apply_order oo on dd.payment_apply_order_id = oo.id
        where dd.is_del = 0
          and oo.is_del = 0
          and dd.apply_related_id = #{relatedId}
          and dd.detail_source = 1
          and oo.status = 0;
    </select>
</mapper>
