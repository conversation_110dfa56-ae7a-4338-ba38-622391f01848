<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockOutOrderDetailMapper">
    <select id="getTotalReturnQuantity" resultType="int">
        SELECT IFNULL(sum(real_return_quantity), 0)
        FROM stock_out_order_detail
        WHERE stock_out_order_id = #{id}
          AND is_del = 0
        GROUP BY stock_out_order_id
    </select>

    <select id="queryDetail" resultType="com.daddylab.supplier.item.controller.stockout.dto.StockOutOrderDetailVO">
        <include refid="selectSql"/>
        <where>
            sood.is_del= 0
            <if test="param.itemSkuCode != null and param.itemSkuCode != ''">
                and sood.item_sku_code LIKE concat('%', #{param.itemSkuCode}, '%')
            </if>
            <if test="param.itemName != null and param.itemName != ''">
                and sood.item_name LIKE concat('%', #{param.itemName}, '%')
            </if>
            <if test="param.warehouseNo != null and param.warehouseNo != ''">
                and sood.warehouse_no = #{param.warehouseNo}
            </if>
            <if test="idList != null and idList.size()>0 ">
                and sood.stock_out_order_id in
                <foreach collection="idList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

        </where>
    </select>

    <select id="getByStockOutOrderId"
            resultType="com.daddylab.supplier.item.controller.stockout.dto.StockOutOrderDetailVO">
        <include refid="selectSql"/>
        where sood.is_del = 0
        and sood.stock_out_order_id = #{id}
    </select>

    <sql id="selectSql">
        select sood.id,
               sood.stock_out_order_id   as stockOutOrderId,
               sood.item_sku_code        as itemSkuCode,
               sood.item_id              as itemId,
               sood.item_name            as itemName,
               sood.specifications,
               sood.stock_unit           as stockUnit,
               sood.valuation_unit       as valuationUnit,
               sood.valuation_quantity   as valuationQuantity,
               sood.replenish_quantity   as replenishQuantity,
               sood.deduction_quantity   as deductionQuantity,
               sood.return_quantity      as returnQuantity,
               sood.real_return_quantity as realReturnQuantity,
               wh.no                     as warehouseNo,
               wh.name                   as warehouseName,
               sood.return_reason        as returnReason,
               sood.is_gift              as isGift,
               sood.remark,
               sood.tax_price            as taxPrice,
               sood.tax_rate             as taxRate,
               sood.tax_quota            as taxQuota,
               sood.total_price_tax      as totalPriceTax,
               sood.after_tax_price      as afterTaxPrice,
               sood.after_tax_amount     as afterTaxAmount
        from stock_out_order_detail sood
                     left join (SELECT no, name FROM warehouse WHERE is_del = 0) wh
                on sood.warehouse_no = wh.no
    </sql>
</mapper>
