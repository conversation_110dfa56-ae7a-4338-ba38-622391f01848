<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderSettlementFormMapper">
    <select id="maxNo" resultType="java.lang.String">
        select `no`
        from order_settlement_form
        where `no` != ''
          and `no` is not null
          and `no` like concat(#{noPrefix}
            , '%')
        order by id desc
        limit 1
    </select>

    <select id="selectBill"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderSettlementForm">
        select *
        from order_settlement_form
        <where>
            is_del = 0
            and (`no` = '' or `no` is null)
            <if test="query.statusVal != null">
                and status = #{query.statusVal}
            </if>
            <if test="query.times != null and query.times.size() > 0">
                and settlement_start_date in
                <foreach collection="query.times" item="time" open="(" separator="," close=")">
                    #{time}
                </foreach>
            </if>
            <if test="query.queryWarehouseNos != null and query.queryWarehouseNos.size() > 0">
                and `warehouse_no` in
                <foreach collection="query.queryWarehouseNos" item="no" open="(" separator="," close=")">
                    #{no}
                </foreach>
            </if>
            <if test="query.businessLine != null and query.businessLine.size() > 0">
                and `business_line` in
                <foreach collection="query.businessLine" item="bl" open="(" separator="," close=")">
                    #{bl}
                </foreach>
            </if>
            <choose>
                <when test="query.selectBillIds != null and query.selectBillIds.size() > 0">
                    order by id in
                    <foreach collection="query.selectBillIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                    desc,settlement_start_date desc,id desc
                </when>
                <otherwise>
                    order by settlement_start_date desc,id desc
                </otherwise>
            </choose>
        </where>
    </select>

    <update id="recoverData">
        update order_settlement_form set is_del = 0
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="recoverDataDetail">
        update order_settlement_detail set is_del = 0
        where form_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getNoPayAndWaitPayFormId" resultType="java.lang.Long">
        select id
        from (select osf.id as id, group_concat(distinct pao.status) as tStatus
              from order_settlement_form osf
                       left join payment_apply_order_detail paod on osf.no = paod.apply_related_no
                       left join payment_apply_order pao on paod.payment_apply_order_id = pao.id
              where osf.is_del = 0
                and osf.no is not null
                and osf.no != ''
              group by osf.id) tmp
        where tStatus = '0'
           or tStatus is null;
    </select>

    <select id="getApplyingPayFormId" resultType="java.lang.Long">
        select id
        from (select osf.id as id, group_concat(distinct pao.status) as tStatus
              from order_settlement_form osf
                       left join payment_apply_order_detail paod on osf.no = paod.apply_related_no
                       left join payment_apply_order pao on paod.payment_apply_order_id = pao.id
              where osf.is_del = 0
                and osf.no is not null
                and osf.no != ''
              group by osf.id) tmp
        where tStatus is not null
          and tStatus != '4'
          and tStatus != '0'
    </select>

    <select id="getFinishPayFormId" resultType="java.lang.Long">
        select id
        from (select osf.id as id, group_concat(distinct pao.status) as tStatus
              from order_settlement_form osf
                       left join payment_apply_order_detail paod on osf.no = paod.apply_related_no
                       left join payment_apply_order pao on paod.payment_apply_order_id = pao.id
              where osf.is_del = 0
                and osf.no is not null
                and osf.no != ''
                and pao.id is not null
              group by osf.id) tmp
        where tStatus = '4';
    </select>
</mapper>
