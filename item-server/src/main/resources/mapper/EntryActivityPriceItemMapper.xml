<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.EntryActivityPriceItemMapper">
    <select id="pageQuery"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.EntryActivityPricePageDO">
        SELECT `eapi`.`id`,
        `month`,
        `eapi`.`item_code` AS `itemCode`,
        `eapi`.`item_id` AS `itemId`,
        `i`.`name` AS `itemName`,
        `p`.`name` AS `provider`,
        `p`.`id` AS `providerId`,
        `b`.`user_id` AS `buyerUid`,
        `eapi`.`status` AS `status`
        FROM `entry_activity_price_item` `eapi`
        LEFT JOIN `item` `i` ON `eapi`.`item_id` = `i`.`id`
        LEFT JOIN `provider` `p` ON `i`.`provider_id` = `p`.`id`
        LEFT JOIN `item_procurement` `ip` ON `i`.`id` = `ip`.`item_id`
        LEFT JOIN `buyer` `b` ON `ip`.`buyer_id` = `b`.`id`
        LEFT JOIN `item_sku` `is` ON `i`.`id` = `is`.`item_id`
        WHERE TRUE
        AND `eapi`.`is_del` = 0
        <if test="priceItemIdList != null">
            AND eapi.id IN
            <foreach item="item" index="index" collection="priceItemIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="itemName != null and itemName != ''">
            AND `i`.`name` = #{itemName}
        </if>
        <if test="itemCode != null and itemCode != ''">
            AND `i`.`code` = #{itemCode}
        </if>
        <if test="skuCode != null and skuCode != ''">
            AND EXISTS(SELECT *
            FROM `entry_activity_price_sku` `eaps`
            WHERE `eaps`.`price_item_id` = `eapi`.`id`
            AND `eaps`.`sku_code` = #{skuCode}
            AND `eaps`.`is_del` = 0)
        </if>
        <if test="providerId != null and providerId != 0">
            AND `p`.`id` = #{providerId}
        </if>
        <if test="providerIds != null and !providerIds.empty">
            AND p.id IN
            <foreach item="item" index="index" collection="providerIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="buyerUid != null and buyerUid != 0">
            AND `b`.`user_id` = #{buyerUid}
        </if>
        <if test="status != null">
            AND `eapi`.`status` = #{status}
        </if>
        <if test="statusList != null and !statusList.empty">
            AND `eapi`.`status` IN
            <foreach item="item" index="index" collection="statusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="month != null and month != 0">
            AND `eapi`.`month` = #{monthStr}
        </if>
        <if test="corpType != null and !corpType.empty">
            AND exists(
            select * from biz_level_division bld
            where bld.is_del = 0
            and bld.biz_id = i.id
            and bld.type = 0 AND bld.level = 0
            AND bld.level_val IN
            <foreach item="item" index="index" collection="corpType" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="bizType != null and !bizType.empty">
            AND exists(
            select * from biz_level_division bld
            where bld.is_del = 0
            and bld.biz_id = i.id
            and bld.type = 0 AND bld.level = 1
            AND bld.level_val IN
            <foreach item="item" index="index" collection="bizType" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY `eapi`.`id`
        ORDER BY `eapi`.`id` desc
    </select>

    <select id="itemInfo"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.EntryActivityPriceItemInfoDO">
        SELECT `i`.`id`      AS `itemId`,
               `i`.`name`    AS `itemName`,
               `p`.`name`    AS `provider`,
               `p`.`id`      AS `providerId`,
               `b`.`user_id` AS `buyer`
        FROM `item` `i`
                 LEFT JOIN `provider` `p` ON `i`.`provider_id` = `p`.`id`
                 LEFT JOIN `item_procurement` `ip` ON `i`.`id` = `ip`.`item_id`
                 LEFT JOIN `buyer` `b` ON `ip`.`buyer_id` = `b`.`id`
        WHERE TRUE
          AND `i`.`code` = #{itemCode}
          and i.`is_del` = 0
    </select>

    <select id="skuInfo"
            resultType="com.daddylab.supplier.item.application.entryActivityPrice.models.EntryActivityPriceSkuInfoVO">
        SELECT `is2`.`id` AS `skuId`, `is2`.`sku_code` AS `skuCode`, `is2`.`specifications` AS `specification`
        FROM `item` `i`
        JOIN `item_sku` `is2` ON `i`.`id` = `is2`.`item_id`
        WHERE i.`is_del` = 0
        AND is2.`is_del` = 0
        AND (`i`.`code` = #{itemCode} OR `i`.`provider_specified_code` = #{itemCode})
        <if test="skuCode != null and skuCode != ''">
            AND (`is2`.`sku_code` = #{skuCode} OR `is2`.`provider_specified_code` = #{skuCode})
        </if>
    </select>

    <select id="listConfirmedSkuBySkuIds"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceSku">
        select *
        from entry_activity_price_sku eaps
        join entry_activity_price_item eapi on eapi.id = eaps.price_item_id
        where status = 1
        and eaps.is_del = 0
        and eapi.is_del = 0
        and eaps.sku_id in
        <foreach item="item" index="index" collection="skuIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
