<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemLaunchStatsMapper">


    <select id="selectOneByItemId"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchStats">
        select * from item_launch_stats where item_id = #{itemId} order by id desc limit 1;
    </select>

    <update id="undelete">
        update item_launch_stats set is_del = 0, deleted_at = 0 where id in
        <foreach item="item" index="index" collection="collection" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>
