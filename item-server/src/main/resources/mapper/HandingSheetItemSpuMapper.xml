<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.HandingSheetItemSpuMapper">


    <select id="selectInvalidSpuIds" resultType="java.lang.Long">
        select spu.id
        from handing_sheet_item_spu spu
        left join handing_sheet_item_sku sku
        on spu.id = sku.handing_sheet_item_spu_id and sku.is_del = 0
        where spu.is_del = 0 and spu.handing_sheet_id = #{handingSheetId}
        group by spu.id
        having count(sku.id) = 0
    </select>
</mapper>
