<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailWrapperMapper">
    <!--    <update id="updateSingleSkuPrice">-->
    <!--        update wdt_order_detail_wrapper-->
    <!--        set price = #{price}-->
    <!--        where trade_no = #{tradeNo}-->
    <!--          and sku_code = #{skuCode}-->
    <!--          and type = #{type}-->
    <!--          and price != #{price}-->
    <!--        order by id asc-->
    <!--            limit #{limit}-->
    <!--    </update>-->

    <!--    <update id="updateRandomSkuPrice">-->
    <!--        update wdt_order_detail_wrapper-->
    <!--        set price = #{price}-->
    <!--        where trade_no = #{tradeNo}-->
    <!--        and sku_code in-->
    <!--        <foreach collection="skuCodes" item="skuCode" open="(" separator="," close=")">-->
    <!--            #{skuCode}-->
    <!--        </foreach>-->
    <!--        and type = #{type}-->
    <!--        and price != #{price}-->
    <!--        order by id asc-->
    <!--        limit #{limit}-->
    <!--    </update>-->

    <select id="skuRefundQuantityStatics"
            resultType="com.daddylab.supplier.item.application.order.settlement.dto.SkuRefundStaticDo">
        select sum(quantity) as refundQuantity,
        sku_code
        from wdt_order_detail_wrapper
        where type in (-1, -2)
        and sku_code in
        <foreach collection="skuCodes" open="(" separator="," close=")" item="skuCode">
            #{skuCode}
        </foreach>
        and provider_id = #{providerId,jdbcType=BIGINT}
        and warehouse_no = #{warehouseNo,jdbcType=VARCHAR}
        and operate_time = #{operateTime,jdbcType=VARCHAR}
        and is_del = 0
        group by sku_code;
    </select>

    <select id="selectListByParams"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper">
        SELECT
            id,
            is_del,
            created_at,
            created_uid,
            updated_at,
            updated_uid,
            deleted_at,
            sku_code,
            price,
            type,
            suite_no,
            num,
            trade_id,
            trade_no,
            rec_id,
            quantity,
            operate_time,
            is_gift,
            pay_time,
            platform_type,
            warehouse_no,
            provider_id,
            suite_db_id,
            refund_num,
            deliver_num
        FROM wdt_order_detail_wrapper
        WHERE is_del = 0
            AND id <![CDATA[>=]]> #{startId}
            AND id <![CDATA[<=]]> #{endId}
            <if test="warehouseNos != null and warehouseNos.size() > 0">
                AND warehouse_no IN
                <foreach collection="warehouseNos" item="no" open="(" separator="," close=")">
                    #{no}
                </foreach>
            </if>
            <if test="skuCodes != null and skuCodes.size() > 0">
                AND sku_code IN
                <foreach collection="skuCodes" item="skuCode" open="(" separator="," close=")">
                    #{skuCode}
                </foreach>
            </if>
            <if test="operateTimes != null and operateTimes.size() > 0">
                AND operate_time IN
                <foreach collection="operateTimes" item="time" open="(" separator="," close=")">
                    #{time}
                </foreach>
            </if>
            <if test="types != null and types.size() > 0">
                AND type IN
                <foreach collection="types" item="t" open="(" separator="," close=")">
                    #{t}
                </foreach>
            </if>
    </select>


    <select id="selectSkuDeliverNum"
            resultType="com.daddylab.supplier.item.application.order.settlement.dto.SkuDeliverNumDo">
        select sku_code as skuCode, deliver_num as deliverNum, price as price
        from wdt_order_detail_wrapper
        where is_del = 0
          and provider_id = #{providerId}
          and type = #{type}
          and warehouse_no = #{warehouseNo,jdbcType=VARCHAR}
          and operate_time = #{operateTime,jdbcType=VARCHAR}
        group by sku_code, price
    </select>

    <select id="selectStaticSkuCode" resultType="java.lang.String">
        select sku_code
        from wdt_order_detail_wrapper
        where is_del = 0
            and provider_id = #{providerId}
            and operate_time = #{operateTime,jdbcType=VARCHAR}
            and type in (1,2,-1,-2)
        group by sku_code
    </select>

    <select id="selectStaticInfo"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper">
        select *
        from wdt_order_detail_wrapper
        where is_del = 0
          and provider_id = #{providerId}
          and operate_time = #{operateTime,jdbcType=VARCHAR}
          and type in (1,2,-1,-2)
          and sku_code = #{skuCode,jdbcType=VARCHAR}
    </select>

    <select id="selectTotalStaticInfo"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper">
        select *
        from wdt_order_detail_wrapper
        where is_del = 0
          and provider_id = #{providerId}
          and operate_time = #{operateTime,jdbcType=VARCHAR}
          and type = 99
    </select>

    <delete id="deleteOldStatisticsData">
        delete
        from wdt_order_detail_wrapper
        where type = 99
          and operate_time = #{targetMonth}
    </delete>

    <!--<select id="getStockInTradeNoList" resultType="java.lang.String">
        select trade_no
        from wdt_order_detail_wrapper
        where type = -1
          and operate_time = #{targetMonth}
          and is_gift not in
          <foreach collection="giftTypes" item="giftType" open="(" separator="," close=")">
              #{giftType}
          </foreach>
        group by trade_no;
    </select>-->

    <select id="getEarliestPayTime" resultType="java.lang.Long">
        select pay_time
        from wdt_order_detail_wrapper
        where operate_time = #{operateTime}
          and type = 1
          and pay_time != ''
        order by pay_time asc
        limit 1;
    </select>

    <select id="getLatestPayTime" resultType="java.lang.Long">
        select pay_time
        from wdt_order_detail_wrapper
        where operate_time = #{operateTime}
          and type = 1
          and pay_time != ''
        order by pay_time desc
        limit 1;
    </select>
</mapper>
