<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OtherStockOutDetailMapper">
    <select id="getIdsByItem" resultType="java.lang.Long">
        SELECT DISTINCT order_id  FROM other_stock_out_detail os
        LEFT JOIN item i ON i.id = os.item_id
        <where>
            os.is_del = 0 AND i.is_del = 0
            <if test="skuCode != null and skuCode != ''">
                AND os.sku_code = #{skuCode}
            </if>
            <if test="code != null and code != ''">
                AND i.code = #{code}
            </if>
            <if test="itemId != null and itemId != 0">
                AND os.item_id = #{itemId}
            </if>
            <if test="brandId != null and brandId != 0">
                AND os.brand_id = #{brandId}
            </if>
            <if test="businessLine != null and businessLine.size() > 0">
                AND i.business_line in
                <foreach collection="businessLine" item="bl" open="(" separator="," close=")">
                    #{bl}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
