<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemStockMapper">

    <select id="getSkuStockChangeLogs"
            resultType="com.daddylab.supplier.item.domain.itemStock.vo.ItemStockChange">
        SELECT `item_stock_change_log`.`type`,
               `item_stock_change_log`.`change`,
               `item_stock_change_log`.`created_at`  AS `changedAt`,
               `item_stock_change_log`.`created_uid` AS `changeUserId`
        FROM `item_stock`
                 LEFT JOIN `item_stock_change_log` ON `item_stock`.`sku_id` = `item_stock_change_log`.`sku_id`
        WHERE `item_stock`.`item_id` = #{itemId}
          AND `item_stock`.`sku_id` = #{skuId}
          AND `item_stock`.`is_del` = 0
          AND `item_stock_change_log`.`is_del` = 0
    </select>

    <select id="getItemTotalStock" resultType="java.lang.Long">
        SELECT IFNULL(sum(`stock`), 0) AS `totalStock` FROM `item_stock` WHERE `item_id` = #{itemId}
    </select>

    <update id="stockIn">
        UPDATE `item_stock`
        SET `stock`       = `stock` + #{num},
            `updated_at`  = #{time},
            `updated_uid` = #{uid}
        WHERE `item_id` = #{itemId}
          AND `sku_id` = #{skuId}
        ;
    </update>


    <update id="stockOut">
        UPDATE `item_stock`
        SET `stock`       = `stock` - #{num},
            `updated_at`  = #{time},
            `updated_uid` = #{uid}
        WHERE `item_id` = #{itemId}
          AND `sku_id` = #{skuId}
          AND `stock` >= #{num}
        ;
    </update>

    <update id="stockSet">
        UPDATE `item_stock`
        SET `stock`       = #{num},
            `updated_at`  = #{time},
            `updated_uid` = #{uid}
        WHERE `item_id` = #{itemId}
          AND `sku_id` = #{skuId}
          AND `stock` != #{num}
        ;
    </update>

    <insert id="insertOrAddStock">
        INSERT INTO `item_stock` (`item_id`, `sku_id`, `stock`, `created_at`, `created_uid`, `updated_at`,
                                  `updated_uid`)
        VALUES (#{itemId}, #{skuId}, #{num}, #{time}, #{uid}, #{time}, #{uid})
        ON DUPLICATE KEY UPDATE `stock`       = `stock` + VALUES(`stock`),
                                `updated_at`  = VALUES(`updated_at`),
                                `updated_uid` = VALUES(`updated_uid`);
    </insert>

    <insert id="insertOrSetStock">
        INSERT INTO `item_stock` (`item_id`, `sku_id`, `stock`, `created_at`, `created_uid`, `updated_at`,
                                  `updated_uid`)
        VALUES (#{itemId}, #{skuId}, #{num}, #{time}, #{uid}, #{time}, #{uid})
        ON DUPLICATE KEY UPDATE `stock`       = VALUES(`stock`),
                                `updated_at`  = VALUES(`updated_at`),
                                `updated_uid` = VALUES(`updated_uid`);
    </insert>

    <select id="selectSkuStockNum" resultType="java.lang.Long">
        select
            IFNULL(stock,0)
        from
            item_stock stock
        where
            sku_id = (select id from item_sku where sku_code = #{skuCode} and is_del = 0)
            and is_del = 0
    </select>
</mapper>
