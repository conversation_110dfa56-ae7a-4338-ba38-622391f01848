<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemPriceMapper">

    <select id="getPrice" resultType="java.math.BigDecimal">
        select
            `price`
        from
            `item_price`
        where
            is_del = 0
            and `type` = #{type}
            and item_id = (select item_id from supplier.item_sku where sku_code = #{skuCode} and is_del = 0)
            and start_time &lt;= #{currentTime}
        order by start_time desc;
    </select>
</mapper>
