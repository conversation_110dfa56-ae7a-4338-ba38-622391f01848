<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
        namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseOrderMapper">
    <update id="autoUpdatePurchaseOrderState">
        update purchase_order po
        left join (
        SELECT SUM(IF(`siod`.`real_receipt_quantity` > `siod`.`receipt_quantity`,
        `siod`.`receipt_quantity`,
        `siod`.`real_receipt_quantity`)) AS `total_receipt_quantity`,
        `sio`.`purchase_order_id`
        FROM `stock_in_order` `sio`
        LEFT JOIN `stock_in_order_detail` `siod`
        ON `sio`.`id` = `siod`.`stock_in_order_id`
        WHERE `sio`.`state` IN (3, 21)
        AND `sio`.`purchase_order_id` = #{id}
        AND `sio`.`is_del` = 0 AND `siod`.`is_del` = 0
        ) agg_sio
        ON agg_sio.purchase_order_id = po.id
        set po.state = if(agg_sio.total_receipt_quantity >= po.total_purchase_quantity, 7, 6)
        where po.id = #{id}
        <!--        已完结，已关闭状态的采购单忽略-->
        and po.state not in (7, 8)
    </update>

    <select id="page"
            resultType="com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderPageVO">
        select po.`id`,
        po.`no`,
        po.`purchase_date` as dt,
        p.`id` as providerId,
        p.`name` as providerName,
        p.`partner_provider_id` as partnerProviderId,
        po.`type`,
        IFNULL(po.`total_purchase_quantity`, 0) as purchaseQuantity,
        po.`state`,
        IFNULL(b.`name`, '') as buyerUserName,
        po.business_line as businessLine,
        po.override_order_id as overrideOrderId
        from purchase_order po
        left join (select id, name, partner_provider_id from provider where is_del = 0) p on p.`id` = po.`provider_id`
        left join (select user_id, name from buyer where is_del = 0) b on b.`user_id` = po.`buyer_user_id`
        <where>
            po.is_del = 0
            <if test="param.ids != null and param.ids.size() > 0">
                and po.id IN
                <foreach item="item" index="index" collection="param.ids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.orderNo != null and param.orderNo != ''">
                and po.`no` = #{param.orderNo}
            </if>
            <if test="param.providerId != null and param.providerId != ''">
                and p.id = #{param.providerId}
            </if>
            <if test="param.type != null and param.type != '' and param.type != 0">
                and po.type = #{param.type}
            </if>
            <if test="param.payMode != null and param.payMode != '' and param.payMode != 0">
                and po.pay_type = #{param.payMode}
            </if>
            <if test="param.state != null and param.state != '' and param.state != 0">
                and po.state = #{param.state}
            </if>

            <if test="param.stateList != null and param.stateList.size() > 0">
                and po.state in
                <foreach collection="param.stateList" item="state" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>

            <if test="param.stockInState != null and param.stockInState != '' and param.stockInState != 0">
                and po.id in
                (select purchase_order_id from stock_in_order where is_del = 0 and state = #{param.stockInState})
            </if>

            <if test="param.buyerUserId != null and param.buyerUserId != '' and param.buyerUserId != 0">
                and b.user_id = #{param.buyerUserId}
            </if>

            <if test="param.startDt != null and param.startDt != '' and param.startDt != 0 and
            param.startEnd != null and param.startEnd != '' and param.startEnd != 0">
                and (po.purchase_date between #{param.startDt} and #{param.startEnd})
            </if>
            <if test="param.contractNo != null and param.contractNo != ''">
                and po.contract_no = #{param.contractNo}
            </if>
            <if test="param.organizationId != null and param.organizationId != ''">
                and po.organization_id = #{param.organizationId}
            </if>
            <if test="param.groupId != null and param.groupId != ''">
                and po.group_id = #{param.groupId}
            </if>
            <if test="param.warehouseNo != null and param.warehouseNo != ''">
                and po.id in (select purchase_order_id
                from purchase_order_detail
                where warehouse_no = #{param.warehouseNo} and is_del = 0)
            </if>
            <if test="param.payOrderNo != null and param.payOrderNo != ''">
                and po.id in (select sio.purchase_order_id
                from stock_in_order sio
                left join purchase_payable_order ppo ON ppo.related_order_id = sio.id
                where ppo.`no` = #{param.payOrderNo})
            </if>
            <if test="param.itemId != null and param.itemId != ''">
                and po.id in (select purchase_order_id
                from purchase_order_detail
                where purchase_order_detail.item_id = #{param.itemId}
                and is_del = 0)
            </if>
            <if test="param.skuCode != null and param.skuCode != ''">
                and po.id in (select purchase_order_id
                from purchase_order_detail
                where purchase_order_detail.item_sku_code = #{param.skuCode}
                and is_del = 0)
            </if>
            <if test="param.createdUidList != null and param.createdUidList.size() > 0">
                and po.created_uid in
                <foreach collection="param.createdUidList" item="createdUid" open="(" separator="," close=")">
                    #{createdUid}
                </foreach>
            </if>
            <if test="param.neCreatedUidList != null and param.neCreatedUidList.size() > 0">
                and po.created_uid not in
                <foreach collection="param.neCreatedUidList" item="neCreatedUid" open="(" separator="," close=")">
                    #{neCreatedUid}
                </foreach>
            </if>
            <if test="param.businessLine != null and param.businessLine.size() > 0">
                and po.business_line in
                <foreach collection="param.businessLine" item="bl" open="(" separator="," close=")">
                    #{bl}
                </foreach>
            </if>
            <if test="param.matchedPaymentStatusIdList != null and param.matchedPaymentStatusIdList.size() > 0">
                and po.id in
                <foreach collection="param.matchedPaymentStatusIdList" item="relatedId" open="(" separator=","
                         close=")">
                    #{relatedId}
                </foreach>
            </if>
        </where>
        order by po.id desc
        <!-- limit #{param.offset},#{param.pageSize} -->
    </select>
    <select id="pageCount" resultType="java.lang.Integer">
        select count(1)
        from purchase_order po
        left join (select id, name from provider where is_del = 0) p on p.`id` = po.`provider_id`
        left join (select user_id, name from buyer where is_del = 0) b on b.`user_id` = po.`buyer_user_id`
        left join (select `purchase_order_id`, `total_receipt_quantity`
        from stock_in_order
        where is_del = 0
        and state = 3) sto on sto.`purchase_order_id` = po.`id`
        <where>
            po.is_del = 0
            <if test="param.orderNo != null and param.orderNo != ''">
                and po.`no` = #{param.orderNo}
            </if>
            <if test="param.providerId != null and param.providerId != ''">
                and p.id = #{param.providerId}
            </if>
            <if test="param.type != null and param.type != '' and param.type != 0">
                and po.type = #{param.type}
            </if>
            <if test="param.payMode != null and param.payMode != '' and param.payMode != 0">
                and po.pay_type = #{param.payMode}
            </if>
            <if test="param.state != null and param.state != '' and param.state != 0">
                and po.state = #{param.state}
            </if>
            <if test="param.stateList != null and param.stateList.size() > 0">
                and po.state in
                <foreach collection="param.stateList" item="state" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
            <if test="param.stockInState != null and param.stockInState != '' and param.stockInState != 0">
                and po.id in
                (select purchase_order_id from stock_in_order where is_del = 0 and state = #{param.stockInState})
            </if>
            <if test="param.buyerUserId != null and param.buyerUserId != ''">
                and b.user_id = #{param.buyerUserId}
            </if>
            <if test="param.startDt != null and param.startDt != '' and param.startDt != 0 and
            param.startEnd != null and param.startEnd != '' and param.startEnd != 0">
                and (po.purchase_date between #{param.startDt} and #{param.startEnd})
            </if>
            <if test="param.contractNo != null and param.contractNo != ''">
                and po.contract_no = #{param.contractNo}
            </if>
            <if test="param.organizationId != null and param.organizationId != ''">
                and po.organization_id = #{param.organizationId}
            </if>
            <if test="param.groupId != null and param.groupId != ''">
                and po.group_id = #{param.groupId}
            </if>
            <if test="param.warehouseNo != null and param.warehouseNo != ''">
                and po.id in (select purchase_order_id
                from purchase_order_detail
                where warehouse_no = #{param.warehouseNo} and is_del = 0)
            </if>
            <if test="param.payOrderNo != null and param.payOrderNo != ''">
                and po.id in (select sio.purchase_order_id
                from stock_in_order sio
                left join purchase_payable_order ppo ON ppo.related_order_id = sio.id
                where ppo.`no` = #{param.payOrderNo})
            </if>
            <if test="param.itemId != null and param.itemId != ''">
                and po.id in (select purchase_order_id
                from purchase_order_detail
                where purchase_order_detail.item_id = #{param.itemId}
                and is_del = 0)
            </if>
            <if test="param.skuCode != null and param.skuCode != ''">
                and po.id in (select purchase_order_id
                from purchase_order_detail
                where purchase_order_detail.item_sku_code = #{param.skuCode}
                and is_del = 0)
            </if>
            <if test="param.createdUid != null and param.createdUid > 0">
                and po.created_uid = #{param.createdUid}
            </if>
        </where>
    </select>
    <select id="getView"
            resultType="com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderViewVO">
        select po.*,
               IFNULL(`org`.name, '') as `organizationName`,
               IFNULL(`gro`.name, '') as `groupName`,
               IFNULL(`pro`.name, '') as providerName,
               IFNULL(`buy`.name, '') as `buyerUserName`
        from purchase_order `po`
                 left join (select `id`, `name` from `organization` where is_del = 0) `org`
                           on po.organization_id = `org`.id
                 left join (select `id`, `name` from `erp_group` where is_del = 0) `gro`
                           on po.group_id = `gro`.id
                 left join (select `id`, `name` from `provider` where is_del = 0) pro
                           on po.provider_id = pro.id
                 left join (select `id`, `name`, `user_id` from buyer where is_del = 0) buy
                           on buy.user_id = po.buyer_user_id
        where `po`.id = #{id}
          and `po`.is_del = 0
    </select>

    <select id="getWarehouseNo" resultType="java.lang.String">
        select IFNULL(warehouse_no, '')
        from purchase_order_detail
        where item_sku_code = #{skuCode}
        order by updated_at desc
        limit 1
    </select>

    <update id="deleteSysOrder">
        update
            purchase_order
        set is_del = 1
        where is_del = 0
          and source = 1
          and type = 2
          and purchase_date
            between #{start} and #{end}
    </update>

    <select id="getById" resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrder">
        select *
        from purchase_order
        where id = #{id}
    </select>
    <select id="listBySkuCode"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrder">
        select * from purchase_order where id in
        (select purchase_order_id from purchase_order_detail where item_sku_code = #{skuNo} and is_del = 0)
        and is_del = 0;
    </select>
</mapper>
