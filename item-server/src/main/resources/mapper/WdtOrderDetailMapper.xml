<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailMapper">
    <insert id="saveOrUpdateBatch">
        INSERT INTO `wdt_order_detail` (`id`, `trade_id`, `rec_id`, `platform_id`, `src_oid`,
        `src_tid`, `gift_type`,
        `refund_status`, `guarantee_mode`, `platform_status`,
        `delivery_term`, `num`, `price`,
        `refund_num`, `order_price`, `share_price`, `adjust`,
        `discount`, `share_amount`,
        `tax_rate`, `goods_name`, `goods_no`, `spec_name`,
        `spec_no`, `spec_code`, `suite_no`,
        `suite_name`, `suite_num`, `suite_amount`, `suite_discount`,
        `api_goods_name`,
        `api_spec_name`, `api_goods_id`, `api_spec_id`,
        `commission`, `goods_type`, `from_mask`,
        `remark`, `modified`, `created`, `prop2`, `weight`,
        `img_url`, `trade_time`, `pay_time`,
        `warehouse_no`, `provider_id`, `deleted_at`)
        VALUES
        <foreach collection="orderDetails" item="item" separator=",">
            (NULL, #{item.tradeId}, #{item.recId}, #{item.platformId}, #{item.srcOid},
            #{item.srcTid}, #{item.giftType},
            #{item.refundStatus}, #{item.guaranteeMode}, #{item.platformStatus},
            #{item.deliveryTerm}, #{item.num}, #{item.price},
            #{item.refundNum}, #{item.orderPrice}, #{item.sharePrice}, #{item.adjust},
            #{item.discount}, #{item.shareAmount},
            #{item.taxRate}, #{item.goodsName}, #{item.goodsNo}, #{item.specName}, #{item.specNo},
            #{item.specCode}, #{item.suiteNo},
            #{item.suiteName}, #{item.suiteNum}, #{item.suiteAmount}, #{item.suiteDiscount},
            #{item.apiGoodsName},
            #{item.apiSpecName}, #{item.apiGoodsId}, #{item.apiSpecId}, #{item.commission},
            #{item.goodsType}, #{item.fromMask},
            #{item.remark}, #{item.modified}, #{item.created}, #{item.prop2}, #{item.weight},
            #{item.imgUrl}, #{item.tradeTime}, #{item.payTime},
            #{item.warehouseNo}, #{item.providerId}, 0)
        </foreach>
        ON DUPLICATE KEY UPDATE `trade_id` =
        VALUES (`trade_id`),
        `platform_id` =
        VALUES (`platform_id`),
        `src_oid` =
        VALUES (`src_oid`),
        `src_tid` =
        VALUES (`src_tid`),
        `gift_type` =
        VALUES (`gift_type`),
        `refund_status` =
        VALUES (`refund_status`),
        `guarantee_mode` =
        VALUES (`guarantee_mode`),
        `platform_status` =
        VALUES (`platform_status`),
        `delivery_term` =
        VALUES (`delivery_term`),
        `num` =
        VALUES (`num`),
        `price` =
        VALUES (`price`),
        `refund_num` =
        VALUES (`refund_num`),
        `order_price` =
        VALUES (`order_price`),
        `share_price` =
        VALUES (`share_price`),
        `adjust` =
        VALUES (`adjust`),
        `discount` =
        VALUES (`discount`),
        `share_amount` =
        VALUES (`share_amount`),
        `tax_rate` =
        VALUES (`tax_rate`),
        `goods_name` =
        VALUES (`goods_name`),
        `goods_no` =
        VALUES (`goods_no`),
        `spec_name` =
        VALUES (`spec_name`),
        `spec_no` =
        VALUES (`spec_no`),
        `spec_code` =
        VALUES (`spec_code`),
        `suite_no` =
        VALUES (`suite_no`),
        `suite_name` =
        VALUES (`suite_name`),
        `suite_num` =
        VALUES (`suite_num`),
        `suite_amount` =
        VALUES (`suite_amount`),
        `suite_discount` =
        VALUES (`suite_discount`),
        `api_goods_name` =
        VALUES (`api_goods_name`),
        `api_spec_name` =
        VALUES (`api_spec_name`),
        `api_goods_id` =
        VALUES (`api_goods_id`),
        `api_spec_id` =
        VALUES (`api_spec_id`),
        `commission` =
        VALUES (`commission`),
        `goods_type` =
        VALUES (`goods_type`),
        `from_mask` =
        VALUES (`from_mask`),
        `remark` =
        VALUES (`remark`),
        `modified` =
        VALUES (`modified`),
        `created` =
        VALUES (`created`),
        `prop2` =
        VALUES (`prop2`),
        `weight` =
        VALUES (`weight`),
        `img_url` =
        VALUES (`img_url`),
        `trade_time` =
        VALUES (`trade_time`),
        `pay_time` =
        VALUES (`pay_time`),
        `warehouse_no` =
        VALUES (`warehouse_no`),
        `provider_id` =
        VALUES (`provider_id`),
        `deleted_at` = 0
    </insert>

    <select id="getRefTradeIdsBySrcTids" resultType="java.lang.Long">
        SELECT DISTINCT trade_id
        FROM wdt_order_detail WHERE src_tid IN
        <foreach collection="srcTids" open="(" separator="," close=")" item="srcTid">
            #{srcTid}
        </foreach>
    </select>

    <!--    <update id="updateSkuPriceWithNewPrice">-->
    <!--        update-->
    <!--        wdt_order_detail_wrapper-->
    <!--        set price = #{newPrice}-->
    <!--        where id in-->
    <!--        <foreach collection="idList" open="(" separator="," close=")" item="bdId">-->
    <!--            #{bdId}-->
    <!--        </foreach>-->
    <!--    </update>-->

    <!--    <select id="selectNeedUpdatePriceIdList" resultType="java.lang.Long">-->
    <!--        select id-->
    <!--        from wdt_order_detail_wrapper-->
    <!--        where sku_code = #{skuCode}-->
    <!--        and operate_time = #{operateTime}-->
    <!--        and pay_time &gt;= #{startTime}-->
    <!--        and pay_time &lt;= #{endTime}-->
    <!--        and price &gt; #{newPrice}-->
    <!--        <if test="platformType != null and platformType != '' and platformType != 0">-->
    <!--            and platform_type = #{platformType}-->
    <!--        </if>-->
    <!--        and type = 1 order by pay_time asc-->
    <!--        <if test="limitSize != null and limitSize != ''">-->
    <!--            limit #{limitSize}-->
    <!--        </if>-->
    <!--    </select>-->

    <!--    <update id="updateSkuPriceWithNewPrice2">-->
    <!--        update-->
    <!--        wdt_order_detail_wrapper-->
    <!--        set price = #{newPrice}-->
    <!--        where sku_code = #{skuCode}-->
    <!--        and operate_time = #{operateTime}-->
    <!--        and pay_time &gt;= #{startTime}-->
    <!--        and pay_time &lt;= #{endTime}-->
    <!--        and price &gt; #{newPrice}-->
    <!--        <if test="platformType != null and platformType != '' and platformType != 0">-->
    <!--            and platform_type = #{platformType}-->
    <!--        </if>-->
    <!--        and type = 1-->
    <!--    </update>-->

    <!--    <select id="selectNeedUpdateComposePriceIdList" resultType="java.lang.Long">-->
    <!--        select id from wdt_order_detail_wrapper-->
    <!--        where sku_code = #{skuCode}-->
    <!--        and suite_no = #{suitNo}-->
    <!--        and operate_time = #{operateTime}-->
    <!--        and pay_time &gt;= #{startTime}-->
    <!--        and pay_time &lt;= #{endTime}-->
    <!--        <if test="platformType != null and platformType != '' and platformType != 0">-->
    <!--            and platform_type = #{platformType}-->
    <!--        </if>-->
    <!--        and price &gt; #{newPrice}-->
    <!--        order by pay_time asc-->
    <!--        <if test="limitSize != null and limitSize != ''">-->
    <!--            limit #{limitSize}-->
    <!--        </if>-->
    <!--    </select>-->

    <!--    <update id="updateComposePriceWithNewPrice2">-->
    <!--        update-->
    <!--        wdt_order_detail_wrapper-->
    <!--        set price = #{newPrice}-->
    <!--        where sku_code = #{skuCode}-->
    <!--        and suite_no = #{suitNo}-->
    <!--        and operate_time = #{operateTime}-->
    <!--        and pay_time &gt;= #{startTime}-->
    <!--        and pay_time &lt;= #{endTime}-->
    <!--        <if test="platformType != null and platformType != '' and platformType != 0">-->
    <!--            and platform_type = #{platformType}-->
    <!--        </if>-->
    <!--        and price &gt; #{newPrice}-->
    <!--    </update>-->

    <update id="updateStockOutAndInSingle">
        update wdt_order_detail_wrapper
        set price = #{param.priceCost}
        where sku_code = #{param.skuCode}
        and type = 1
        and pay_time &gt;= #{param.startTime}
        and pay_time &lt;= #{param.endTime}
        and operate_time = #{operateMonth}
        <if test="platformType != null and platformType != '' and platformType != 0">
            and platform_type = #{param.platformType}
        </if>
    </update>

    <update id="updateStockOutSingleWithQuantity">
        update wdt_order_detail_wrapper
        set price = #{param.priceCost}
        where sku_code = #{param.skuCode}
        and type = 1
        and pay_time &gt;= #{param.startTime}
        and pay_time &lt;= #{param.endTime}
        and operate_time = #{operateMonth}
        <if test="platformType != null and platformType != '' and platformType != 0">
            and platform_type = #{param.platformType}
        </if>
        order by pay_time asc limit #{param.numCost}
    </update>

    <!--    <update id="updateStockOutSuite">-->
    <!--        update wdt_order_detail_wrapper-->
    <!--        set price = #{skuNewCostPrice}-->
    <!--        where sku_code = #{skuCode}-->
    <!--        and suite_no = #{param.suiteNo}-->
    <!--        and type = 2-->
    <!--        and pay_time &gt;= #{param.startTime}-->
    <!--        and pay_time &lt;= #{param.endTime}-->
    <!--        and operate_time = #{operateMonth}-->
    <!--        <if test="platformType != null and platformType != '' and platformType != 0">-->
    <!--            and platform_type = #{param.platformType}-->
    <!--        </if>-->
    <!--    </update>-->

    <!--    <update id="updateStockOutSuiteWithQuantity">-->
    <!--        update wdt_order_detail_wrapper-->
    <!--        set price = #{skuNewCostPrice}-->
    <!--        where sku_code = #{skuCode}-->
    <!--        and suite_no = #{param.suiteNo}-->
    <!--        and type = 2-->
    <!--        and pay_time &gt;= #{param.startTime}-->
    <!--        and pay_time &lt;= #{param.endTime}-->
    <!--        and operate_time = #{operateMonth}-->
    <!--        <if test="platformType != null and platformType != '' and platformType != 0">-->
    <!--            and platform_type = #{param.platformType}-->
    <!--        </if>-->
    <!--        order by pay_time asc-->
    <!--        limit #{size};-->
    <!--    </update>-->

    <select id="selectStockOutSingleTimePurchase"
            resultType="com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.ActivityTimeSinglePriceDO">
        select distinct pp.price_cost,
                        wodw.sku_code,
                        pp.start_time,
                        pp.end_time,
                        pp.platform_type
        from wdt_order_detail_wrapper wodw
                 inner join purchase pp on pp.item_sku = wodw.sku_code
        where wodw.type = 1
          and wodw.pay_time &gt;= pp.start_time
          and wodw.pay_time &lt;= pp.end_time
          and wodw.operate_time = #{targetMonth}
          and pp.favourable_type = 1
          and pp.is_del = 0
          and (wodw.platform_type = pp.platform_type or pp.platform_type = 0)
    </select>

    <select id="selectSingleQuantityPurchase"
            resultType="com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.ActivityQuantitySingleDO">
        select distinct pp.price_cost,
                        wodw.sku_code,
                        pp.num_cost,
                        pp.start_time,
                        pp.end_time,
                        pp.platform_type
        from wdt_order_detail_wrapper wodw
                 inner join purchase pp on pp.item_sku = wodw.sku_code
        where wodw.type = 1
          and wodw.pay_time &gt;= pp.start_time
          and wodw.pay_time &lt;= pp.end_time
          and wodw.operate_time = #{targetMonth}
          and pp.favourable_type in (0, 2)
          and pp.is_del = 0
          and (wodw.platform_type = pp.platform_type or pp.platform_type = 0)
    </select>

    <select id="selectStockOutSuiteTimePurchase"
            resultType="com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.ActivityTimeSuitePriceDO">
        select distinct suite_no,
                        pp.price_cost,
                        pp.start_time,
                        pp.end_time,
                        pp.platform_type
        from wdt_order_detail_wrapper wodw
                 inner join purchase pp on pp.item_sku = wodw.suite_no
        where wodw.type = 2
          and wodw.pay_time &gt;= pp.start_time
          and wodw.pay_time &lt;= pp.end_time
          and wodw.operate_time = #{targetMonth}
          and pp.favourable_type = 1
          and pp.is_del = 0
          and (wodw.platform_type = pp.platform_type or pp.platform_type = 0)
        order by pp.price_cost desc
    </select>

    <select id="selectSuiteQuantityPurchase"
            resultType="com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.ActivityQuantitySuiteDO">
        select distinct pp.price_cost,
                        wodw.suite_no,
                        pp.num_cost,
                        pp.start_time,
                        pp.end_time,
                        pp.platform_type
        from wdt_order_detail_wrapper wodw
                 inner join purchase pp on pp.item_sku = wodw.suite_no
        where wodw.type = 2
          and wodw.pay_time &gt;= pp.start_time
          and wodw.pay_time &lt;= pp.end_time
          and wodw.operate_time = #{targetMonth}
          and pp.favourable_type in (0, 2)
          and pp.is_del = 0
          and (wodw.platform_type = pp.platform_type or pp.platform_type = 0)
    </select>

    <select id="selectSingleHistoryQuantityDO"
            resultType="com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.HistoryQuantitySingleDO">
        SELECT sum(wssood.goods_count) as sumNum,
               wssood.platform_id
        FROM wdt_sale_stock_out_order_details wssood
                 inner JOIN wdt_sale_stock_out_order wssoo
                            ON wssood.stockout_id = wssoo.stockout_id
        WHERE wssoo.trade_time &gt;= #{tradeStartTime}
          and wssoo.trade_time &lt;= #{tradeEndTime}
          and wssoo.consign_time &lt; #{consignLineTime}
          and wssood.spec_no = #{skuCode}
          and (wssood.suite_no is null or wssood.suite_no = '')
        group by wssood.platform_id
    </select>

    <select id="statisticsCountByTradeNo"
            resultType="com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.StatisticsQuantityByTradeNoDO">
        select sum(quantity) as sumNum, trade_no,group_concat(id) as ids
        from wdt_order_detail_wrapper
        where
        type = 1
        and operate_time = #{operateTime}
        and trade_no != ''
        <if test=" skuCode !='' and  skuCode != null ">
            and sku_code = #{skuCode}
        </if>
        <if test=" skuCodeList != null and skuCodeList.size()>0 ">
            and sku_code in
            <foreach collection="skuCodeList" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        <if test=" activityStartTime !='' and  activityStartTime != null and activityEndTime !='' and  activityEndTime != null ">
            and ( pay_time between #{activityStartTime} and #{activityEndTime} )
        </if>
        group by trade_no
    </select>

    <select id="getOneByTradeIdAndSpecNo"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetail">
        select *
        from wdt_order_detail
        where trade_id = #{tradeId}
          and spec_no = #{specNo}
        limit 1;
    </select>

    <select id="getProviderIdCount" resultType="java.lang.Integer">
        select count(1)
        from (select distinct provider_id
              from wdt_order_detail_wrapper
              where provider_id != 0
                and operate_time = #{operateTime}
                and is_del = 0
                and type > 0
              group by provider_id) tmp
    </select>

    <delete id="deleteByTypeAndMonth">
        delete
        from wdt_order_detail_wrapper
        where operate_time = #{operateTime}
          and type = #{type}
    </delete>

    <select id="selectTradeIdsBySrcTids" resultType="java.lang.Long">
        select distinct trade_id from wdt_order_detail where src_tid in
        <foreach item="item" index="index" collection="srcTids" open="(" separator="," close=")">
            #{item}
        </foreach>
        and (deleted_at = 0 or deleted_at is null)
    </select>

    <delete id="deleteByTradeIds">
        update wdt_order_detail set deleted_at = unix_timestamp() where (deleted_at = 0 OR deleted_at is null)
        and trade_id in
        <foreach item="item" index="index" collection="tradeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>
