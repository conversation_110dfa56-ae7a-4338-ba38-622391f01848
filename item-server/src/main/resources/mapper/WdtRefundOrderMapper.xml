<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundOrderMapper">
    <resultMap id="RefundOrderBaseInfoResultMap"
               type="com.daddylab.supplier.item.types.refundOrder.RefundOrderBaseInfo">
        <id column="id" property="id"/>
        <result column="refundOrderNo" property="refundOrderNo"/>
        <result column="srcOrderNos" property="srcOrderNos"/>
        <result column="wdtOrderNos" property="wdtOrderNos"/>
        <result column="wdtPlatformId" property="wdtPlatformId"/>
        <result column="createTime" property="createTime"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="shopNo" property="shopNo"/>
        <result column="shopName" property="shopName"/>
        <result column="buyerNickname" property="buyerNickname"/>
        <result column="returnWarehouseNo" property="returnWarehouseNo"/>
        <result column="returnWarehouseName" property="returnWarehouseName"/>
        <result column="returnLogisticsName" property="returnLogisticsName"/>
        <result column="returnLogisticsNo" property="returnLogisticsNo"/>
        <collection property="itemDetails"
                    ofType="com.daddylab.supplier.item.types.refundOrder.RefundOrderItem"
                    columnPrefix="refundItem.">
            <result column="id" property="id"/>
            <result column="recId" property="recId"/>
            <result column="itemId" property="itemId"/>
            <result column="itemCode" property="itemCode"/>
            <result column="itemName" property="itemName"/>
            <result column="itemMainImg" property="itemMainImg"/>
            <result column="platformItemName" property="platformItemName"/>
            <result column="platformItemSkuName" property="platformItemSkuName"/>
            <result column="specName" property="specName"/>
            <result column="specNo" property="specNo"/>
            <result column="brandName" property="brandName"/>
            <result column="orderQuantity" property="orderQuantity"/>
            <result column="price" property="price"/>
            <result column="refundQuantity" property="refundQuantity"/>
            <result column="refundAmount" property="refundAmount"/>
            <result column="totalAmount" property="totalAmount"/>
            <result column="rawRefundNos" property="rawRefundNos"/>
            <result column="srcOrderNo" property="srcOrderNo"/>
            <result column="srcOrderDetailNo" property="srcOrderDetailNo"/>
            <result column="wdtOrderNo" property="wdtOrderNo"/>
            <result column="warehouseNo" property="warehouseNo"/>
            <result column="undertakeType" property="undertakeType"/>
            <result column="undertakeAmount" property="undertakeAmount"/>
        </collection>
    </resultMap>

    <sql id="queryRefundOrderDetails_Cond">
        <!--@sql SELECT * FROM wdt_refund_order wro, wdt_refund_order_detail wrod, wdt_order_detail wod, item_sku `is`, item i, platform_item_sku pis, platform_item pi, shop s, brand b-->
        <!--@sql WHERE TRUE-->
        AND (wrod.deleted_at IS NULL OR wrod.deleted_at = 0)
        <if test="srcOrderNo != null and srcOrderNo != ''">
            AND wrod.tid = #{srcOrderNo}
        </if>
        <if test="srcOrderNoList != null and srcOrderNoList.size() > 0">
            AND wrod.tid IN (
            <foreach collection="srcOrderNoList" separator=","
                     item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="wdtOrderNo != null and wdtOrderNo != ''">
            AND wrod.trade_no = #{wdtOrderNo}
        </if>
        <if test="platform != null and platform.value == 0">
            AND wrod.platform_id NOT IN (
            <foreach collection="allPlatformIds" separator=","
                     item="item">
                #{item}
            </foreach>)
        </if>
        <if test="srcRefundOrderNos != null and srcRefundOrderNos != ''">
            and wrod.raw_refund_nos = #{srcRefundOrderNos}
        </if>
        <if test="brandNo != null and brandNo != ''">
            and b.sn = #{brandNo}
        </if>
        <if test="specNo != null and specNo != ''">
            and wrod.spec_no = #{specNo}
        </if>
        <if test="specName != null and specName != ''">
            and `wrod`.`spec_name` LIKE concat('%', #{specName}, '%')
        </if>
        <if test="itemNo != null and itemNo != ''">
            and (i.code = #{itemNo} OR i.`provider_specified_code` = #{itemNo})
        </if>
        <if test="itemName != null and itemName != ''">
            and wrod.`goods_name` LIKE concat('%', #{itemName}, '%')
        </if>
    </sql>

    <sql id="queryRefundOrderWithoutDetails_Cond">
        <!--@sql
        SELECT * FROM
        wdt_refund_order wro, wdt_refund_order_detail wrod, wdt_order_detail wod, item_sku `is`, item i, platform_item_sku pis, platform_item pi, shop s, brand b, `warehouse` return_w, `after_sales_receive` `asr`
         -->
        <!--@sql WHERE TRUE-->
        <if test="platform != null and platform.value != 0">
            and wro.platform_id = #{wdtPlatformId}
        </if>
        <if test="refundOrderNo != null and refundOrderNo != ''">
            AND wro.refund_no = #{refundOrderNo}
        </if>
        <if test="shopId != null">
            AND s.id = #{shopId}
        </if>
        <if test="shopNo != null">
            AND wro.shop_no = #{shopNo}
        </if>
        <if test="status != null and status.size() != 0">
            and wro.status IN
            <foreach item="statusValue" index="index" collection="status" open="(" separator=","
                     close=")">
                #{statusValue}
            </foreach>
        </if>
        <if test="receiveStates != null and receiveStates.size() != 0">
            and ( `asr`.`state` IN
            <foreach item="statusValue" index="index" collection="receiveStates" open="("
                     separator=","
                     close=")">
                #{statusValue}
            </foreach>
            <if test="receiveStates.contains(1)">
                OR `asr`.`state` IS NULL
            </if>
            )
        </if>
        <if test="type != null">
            and wro.type = #{type}
        </if>
        <if test="types != null and types.size() > 0">
            and wro.type IN
            <foreach item="item" index="index" collection="types" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="returnWarehouseNo != null and returnWarehouseNo != ''">
            and wro.return_warehouse_no = #{returnWarehouseNo}
        </if>
        <if test="returnWarehouseNos != null and returnWarehouseNos.size() != 0">
            and wro.return_warehouse_no IN
            <foreach item="returnWarehouseNo" index="index" collection="returnWarehouseNos"
                     open="(" separator="," close=")">
                #{returnWarehouseNo}
            </foreach>
        </if>
        <if test="returnWarehouseName != null and returnWarehouseName != ''">
            and return_w.`name` LIKE concat('%', #{returnWarehouseName}, '%')
        </if>
        <if test="returnLogisticsName != null and returnLogisticsName != ''">
            AND wro.`return_logistics_name` LIKE concat('%', #{returnLogisticsName}, '%')
        </if>
        <if test="returnLogisticsNo != null and returnLogisticsNo != ''">
            AND wro.`return_logistics_no` = #{returnLogisticsNo}
        </if>
        <if test="creatorName != null and creatorName != ''">
            and wro.operator_name = #{creatorName}
        </if>
        <if test="orderTimeStart != null and orderTimeStart > 0">
            and wro.created >= #{orderTimeStartObj}
        </if>
        <if test="orderTimeEnd != null and orderTimeEnd > 0">
            and wro.created <![CDATA[<=]]> #{orderTimeEndObj}
        </if>
    </sql>
    <sql id="refundOrderDetails_Join">
        <!--@sql SELECT * FROM wdt_refund_order wro -->
        LEFT JOIN `wdt_refund_order_detail` `wrod`
        ON `wro`.`refund_no` = `wrod`.`refund_no`
        LEFT JOIN `wdt_order_detail` `wod` ON `wrod`.`oid` = `wod`.`src_oid` and wod.deleted_at = 0
        LEFT JOIN `item_sku` `is` ON `is`.`sku_code` = `wrod`.`spec_no`
        LEFT JOIN `item` `i` ON `i`.`id` = `is`.`item_id`
        LEFT JOIN `brand` `b` ON `b`.`id` = `i`.`brand_id`
    </sql>
    <select id="queryRefundOrderCountWithoutDetailsCond" resultType="int">
        SELECT count(`wro`.`id`) AS cnt
        FROM `wdt_refund_order` `wro`
        <if test="shopId != null">
            LEFT JOIN `shop` `s` ON `s`.`sn` = `wro`.`shop_no`
        </if>
        <if test="receiveStates != null and receiveStates.size() != 0">
            LEFT JOIN `after_sales_receive` `asr` ON `asr`.`return_order_no` = wro.`refund_no`
        </if>
        <if test="returnWarehouseName != null and returnWarehouseName != '' or returnWarehouseNos != null and returnWarehouseNos.size() != 0">
            LEFT JOIN `warehouse` `return_w`
            ON `return_w`.`no` = `wro`.`return_warehouse_no` AND
            `return_w`.`is_del` = 0
        </if>
        <where>
            <include refid="queryRefundOrderWithoutDetails_Cond"/>
        </where>
    </select>
    <select id="queryRefundOrderCountWithDetailsCond" resultType="int"
            parameterType="com.daddylab.supplier.item.types.refundOrder.RefundOrderQuery">
        SELECT count(DISTINCT `wro`.`id`) cnt
        FROM `wdt_refund_order` `wro`
        LEFT JOIN `wdt_refund_order_detail` `wrod`
        ON `wro`.`refund_no` = `wrod`.`refund_no`
        <if test="shopId != null and shopId > 0">
            LEFT JOIN `shop` `s` ON `s`.`sn` = `wro`.`shop_no`
        </if>
        <if test="receiveStates != null and receiveStates.size() != 0">
            LEFT JOIN `after_sales_receive` `asr` ON `asr`.`return_order_no` = wro.`refund_no`
        </if>
        <if test="returnWarehouseName != null and returnWarehouseName != '' or returnWarehouseNos != null and returnWarehouseNos.size() != 0">
            LEFT JOIN `warehouse` `return_w`
            ON `return_w`.`no` = `wro`.`return_warehouse_no` AND `return_w`.`is_del` = 0
        </if>
        <if test="brandNo != null and brandNo != '' or itemNo != null and itemNo != ''">
            LEFT JOIN `item_sku` `is` ON `is`.`sku_code` = `wrod`.`spec_no`
            LEFT JOIN `item` `i` ON `i`.`id` = `is`.`item_id`
            LEFT JOIN `brand` `b` ON `b`.`id` = `i`.`brand_id`
        </if>
        <where>
            <include refid="queryRefundOrderWithoutDetails_Cond"/>
            <include refid="queryRefundOrderDetails_Cond"/>
        </where>
    </select>
    <select id="queryRefundOrderIdsWithDetailsCond" resultType="long"
            parameterType="com.daddylab.supplier.item.types.refundOrder.RefundOrderQuery">
        SELECT id FROM (
        SELECT `wro`.`id`, any_value(wro.created) AS created
        FROM `wdt_refund_order` `wro`
        LEFT JOIN `shop` `s` ON `s`.`sn` = `wro`.`shop_no`
        LEFT JOIN `after_sales_receive` `asr` ON `asr`.`return_order_no` = wro.`refund_no`
        LEFT JOIN `warehouse` `return_w`
        ON `return_w`.`no` = `wro`.`return_warehouse_no` AND `return_w`.`is_del` = 0
        <include refid="refundOrderDetails_Join"/>
        <where>
            <include refid="queryRefundOrderWithoutDetails_Cond"/>
            <include refid="queryRefundOrderDetails_Cond"/>
        </where>
        GROUP BY wro.id
        ORDER BY `created` DESC, id DESC
        LIMIT #{offset},#{pageSize}) t_data
    </select>
    <select id="queryRefundOrderIdsWithoutDetailsCond" resultType="java.lang.Long">
        SELECT `wro`.`id`
        FROM `wdt_refund_order` `wro`
        LEFT JOIN `shop` `s` ON `s`.`sn` = `wro`.`shop_no`
        LEFT JOIN `after_sales_receive` `asr` ON `asr`.`return_order_no` = wro.`refund_no`
        LEFT JOIN `warehouse` `return_w`
        ON `return_w`.`no` = `wro`.`return_warehouse_no` AND
        `return_w`.`is_del` = 0
        <where>
            <include refid="queryRefundOrderWithoutDetails_Cond"/>
        </where>
        ORDER BY wro.`created` DESC, wro.id DESC
        LIMIT #{offset},#{pageSize}
    </select>
    <sql id="queryRefundOrder_From">
        <!--@sql SELECT * -->
        FROM wdt_refund_order wro
        LEFT JOIN wdt_refund_order_detail wrod ON wro.refund_no = wrod.refund_no
        LEFT JOIN wdt_order_detail wod ON wrod.oid = wod.src_oid and (wod.deleted_at = 0 OR
        wod.deleted_at IS NULL)
        LEFT JOIN item_sku `is` ON `is`.sku_code = wrod.spec_no
        LEFT JOIN item i ON i.id = `is`.item_id
        LEFT JOIN shop s ON s.sn = wro.shop_no
        LEFT JOIN brand b ON b.id = i.brand_id
        LEFT JOIN `warehouse` `return_w`
        ON `return_w`.`no` = `return_warehouse_no` AND `return_w`.`is_del` = 0
        LEFT JOIN `after_sales_confirm_detail` `ascd`
        ON wrod.`rec_id` = `ascd`.`rec_id` AND `ascd`.`is_del` = 0
    </sql>
    <select id="queryRefundOrderByIds"
            resultMap="RefundOrderBaseInfoResultMap">
        SELECT wro.id,
        wro.refund_no AS refundOrderNo,
        wro.tid_list AS srcOrderNos,
        wro.trade_no_list AS wdtOrderNos,
        wrod.platform_id AS wdtPlatformId,
        UNIX_TIMESTAMP(wro.created) AS createTime,
        wro.type,
        wro.status,
        wro.shop_no AS shopNo,
        s.name AS shopName,
        wro.buyer_nick AS buyerNickname,
        wro.`return_warehouse_no` AS returnWarehouseNo,
        `return_w`.`name` AS returnWarehouseName,
        wro.`return_logistics_name` AS returnLogisticsName,
        wro.`return_logistics_no` AS returnLogisticsNo,
        wrod.id AS 'refundItem.id',
        wrod.`rec_id` AS 'refundItem.recId',
        i.id AS 'refundItem.itemId',
        i.code AS 'refundItem.itemCode',
        i.name AS 'refundItem.itemName',
        wrod.api_goods_name AS 'refundItem.platformItemName',
        wrod.api_spec_name AS 'refundItem.platformItemSkuName',
        wrod.spec_name AS 'refundItem.specName',
        wrod.spec_no AS 'refundItem.specNo',
        b.name AS 'refundItem.brandName',
        wrod.num AS 'refundItem.orderQuantity',
        wod.img_url AS 'refundItem.itemMainImg',
        wrod.price AS 'refundItem.price',
        wrod.refund_num AS 'refundItem.refundQuantity',
        wrod.refund_amount AS 'refundItem.refundAmount',
        wrod.total_amount AS `refundItem.totalAmount`,
        wrod.raw_refund_nos AS 'refundItem.rawRefundNos',
        wrod.`trade_no` AS 'refundItem.wdtOrderNo',
        wrod.`tid` AS 'refundItem.srcOrderNo',
        wrod.`oid` AS 'refundItem.srcOrderDetailNo',
        wod.`warehouse_no` AS 'refundItem.warehouseNo',
        `ascd`.`undertake_type` AS 'refundItem.undertakeType',
        `ascd`.`undertake_amount` AS 'refundItem.undertakeAmount'
        <include refid="queryRefundOrder_From"/>
        <where>
            wro.id IN
            <foreach collection="collection" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        ORDER BY wro.created DESC, wro.id DESC, wrod.`id`
    </select>

    <resultMap id="RefundOrderDetailResultMap"
               type="com.daddylab.supplier.item.types.refundOrder.RefundOrderDetail">
        <result column="id" property="id"/>
        <result column="refundOrderNo" property="refundOrderNo"/>
        <result column="srcOrderNos" property="srcOrderNos"/>
        <result column="wdtOrderNos" property="wdtOrderNos"/>
        <result column="status" property="status"/>
        <result column="platform" property="wdtPlatformId"/>
        <result column="type" property="type"/>
        <result column="createTime" property="createTime"/>
        <result column="creatorName" property="creatorName"/>
        <result column="remark" property="remark"/>
        <result column="shopId" property="shopId"/>
        <result column="shopName" property="shopName"/>
        <result column="shopNo" property="shopNo"/>
        <result column="buyerNickname" property="buyerNickname"/>
        <result column="buyerName" property="buyerName"/>
        <result column="buyerMobile" property="buyerMobile"/>
        <result column="returnLogisticsNo" property="returnLogisticsNo"/>
        <result column="returnLogisticsName" property="returnLogisticsName"/>
        <result column="returnWarehouseNo" property="returnWarehouseNo"/>
        <result column="returnWarehouseName" property="returnWarehouseName"/>
        <collection property="returnItemDetails"
                    ofType="com.daddylab.supplier.item.types.refundOrder.RefundOrderItem"
                    columnPrefix="refundItem.">
            <result column="id" property="id"/>
            <result column="recId" property="recId"/>
            <result column="itemId" property="itemId"/>
            <result column="itemCode" property="itemCode"/>
            <result column="itemName" property="itemName"/>
            <result column="itemMainImg" property="itemMainImg"/>
            <result column="specName" property="specName"/>
            <result column="platformItemName" property="platformItemName"/>
            <result column="platformItemSkuName" property="platformItemSkuName"/>
            <result column="specNo" property="specNo"/>
            <result column="brandName" property="brandName"/>
            <result column="orderQuantity" property="orderQuantity"/>
            <result column="price" property="price"/>
            <result column="refundQuantity" property="refundQuantity"/>
            <result column="refundAmount" property="refundAmount"/>
            <result column="totalAmount" property="totalAmount"/>
            <result column="rawRefundNos" property="rawRefundNos"/>
            <result column="srcOrderNo" property="srcOrderNo"/>
            <result column="srcOrderDetailNo" property="srcOrderDetailNo"/>
            <result column="wdtOrderNo" property="wdtOrderNo"/>
        </collection>
    </resultMap>
    <select id="queryRefundOrderDetail"
            resultMap="RefundOrderDetailResultMap">
        SELECT wro.id,
        wro.refund_no AS refundOrderNo,
        wro.tid_list AS srcOrderNos,
        wro.trade_no_list AS wdtOrderNos,
        unix_timestamp(wro.created) AS createTime,
        wro.type as type,
        wro.status AS status,
        wrod.platform_id AS platform,
        wro.operator_name as creatorName,
        wro.remark as remark,
        wro.type as type,
        wro.id as shopId,
        wro.shop_no as shopNo,
        s.name AS shopName,
        wro.buyer_nick as buyerNickname,
        wro.receiver_name as buyerName,
        receiver_telno as buyerMobile,
        return_warehouse_no as returnWarehouseNo,
        w.name as returnWarehouseName,
        return_logistics_name as returnLogisticsName,
        return_logistics_no as returnLogisticsNo,
        wrod.id as 'refundItem.id',
        wrod.`rec_id` AS 'refundItem.recId',
        i.id as 'refundItem.itemId',
        i.code AS 'refundItem.itemCode',
        i.name as 'refundItem.itemName',
        wrod.spec_name AS 'refundItem.specName',
        wrod.spec_no AS 'refundItem.specNo',
        wrod.api_goods_name AS 'refundItem.platformItemName',
        wrod.api_spec_name AS 'refundItem.platformItemSkuName',
        wod.img_url AS 'refundItem.itemMainImg',
        b.name as 'refundItem.brandName',
        wrod.num as 'refundItem.orderQuantity',
        wrod.price AS 'refundItem.price',
        wrod.refund_num AS 'refundItem.refundQuantity',
        wrod.refund_amount as 'refundItem.refundAmount',
        `wrod`.`total_amount` AS `refundItem.totalAmount`,
        wrod.raw_refund_nos as 'refundItem.rawRefundNos',
        wrod.tid AS 'refundItem.srcOrderNo',
        wrod.oid AS 'refundItem.srcOrderDetailNo',
        wrod.`trade_no` AS 'refundItem.wdtOrderNo'
        FROM wdt_refund_order wro
        left join wdt_refund_order_detail wrod ON wro.refund_no = wrod.refund_no
        left join wdt_order_detail wod ON wrod.oid = wod.src_oid and (wod.deleted_at = 0 OR
        wod.deleted_at IS NULL)
        LEFT JOIN item_sku `is` ON `is`.sku_code = wrod.spec_no
        LEFT JOIN item i ON i.id = `is`.item_id
        left join shop s ON s.sn = wro.shop_no
        left join brand b on b.id = i.brand_id
        left join warehouse w ON wro.return_warehouse_no = w.no
        <where>
            wro.refund_no = #{refundOrderNo}
        </where>
    </select>

    <resultMap id="RefundOrderItemResultMap"
               type="com.daddylab.supplier.item.types.refundOrder.RefundOrderItem">
        <id column="id" property="id"/>
        <result column="itemId" property="itemId"/>
        <result column="itemCode" property="itemCode"/>
        <result column="itemName" property="itemName"/>
        <result column="specName" property="specName"/>
        <result column="specNo" property="specNo"/>
        <result column="brandName" property="brandName"/>
        <result column="orderQuantity" property="orderQuantity"/>
        <result column="price" property="price"/>
        <result column="totalAmount" property="totalAmount"/>
        <result column="srcSwapOrderNo" property="srcSwapOrderNo"/>
    </resultMap>
    <select id="querySwapItems"
            resultMap="RefundOrderItemResultMap">
        SELECT wsod.id,
               i.id              as itemId,
               i.code            as itemCode,
               i.name            as itemName,
               b.name            as brandName,
               wsod.spec_name    as specName,
               wsod.merchant_no  as specNo,
               wsod.num          as orderQuantity,
               wsod.price        as price,
               wso.tid           as srcSwapOrderNo,
               wsod.total_amount as totalAmount
        FROM wdt_refund_order wro
                 INNER JOIN wdt_swap_order wso on wso.refund_no = wro.refund_no
                 INNER JOIN wdt_swap_order_detail wsod on wsod.refund_no = wro.refund_no
                 LEFT JOIN item_sku `is` ON `is`.sku_code = wsod.merchant_no
                 LEFT JOIN item i ON i.id = `is`.item_id
                 LEFT JOIN shop s ON s.sn = wro.shop_no
                 LEFT JOIN brand b on b.id = i.brand_id
                 LEFT JOIN warehouse w ON wro.return_warehouse_no = w.no
        WHERE wro.refund_no = #{refundOrderNo}
    </select>

    <insert id="saveOrUpdateBatch">
        INSERT INTO wdt_refund_order (id, refund_id, tid_list, refund_no, remark, type,
        stockin_status, flag_name, return_goods_count, receiver_telno,
        receiver_name, modified, note_count, shop_no, from_type,
        created, settle_time, return_logistics_no, trade_no_list,
        guarantee_refund_amount, return_goods_amount,
        return_logistics_name, reason_name, refund_reason, buyer_nick,
        operator_name, actual_refund_amount, revert_reason_name,
        return_warehouse_no, direct_refund_amount, receive_amount,
        customer_name, fenxiao_nick_name, status, shop_id, trade_id, check_time)
        VALUES
        <foreach collection="orders" separator="," item="item">
            (NULL, #{item.refundId}, #{item.tidList}, #{item.refundNo}, #{item.remark},
            #{item.type},
            #{item.stockinStatus}, #{item.flagName}, #{item.returnGoodsCount},
            #{item.receiverTelno},
            #{item.receiverName}, #{item.modified}, #{item.noteCount}, #{item.shopNo},
            #{item.fromType},
            #{item.created}, #{item.settleTime}, #{item.returnLogisticsNo}, #{item.tradeNoList},
            #{item.guaranteeRefundAmount}, #{item.returnGoodsAmount},
            #{item.returnLogisticsName}, #{item.reasonName}, #{item.refundReason},
            #{item.buyerNick},
            #{item.operatorName}, #{item.actualRefundAmount}, #{item.revertReasonName},
            #{item.returnWarehouseNo}, #{item.directRefundAmount}, #{item.receiveAmount},
            #{item.customerName}, #{item.fenxiaoNickName}, #{item.status}, #{item.shopId},
            #{item.tradeId}, #{item.checkTime})
        </foreach>
        ON DUPLICATE KEY UPDATE tid_list = VALUES(tid_list),
        refund_no = VALUES(refund_no),
        remark = VALUES(remark),
        type = VALUES(type),
        stockin_status = VALUES(stockin_status),
        flag_name = VALUES(flag_name),
        return_goods_count = VALUES(return_goods_count),
        receiver_telno = VALUES(receiver_telno),
        receiver_name = VALUES(receiver_name),
        modified = VALUES(modified),
        note_count = VALUES(note_count),
        shop_no = VALUES(shop_no),
        from_type = VALUES(from_type),
        created = VALUES(created),
        settle_time = VALUES(settle_time),
        return_logistics_no = VALUES(return_logistics_no),
        trade_no_list = VALUES(trade_no_list),
        guarantee_refund_amount = VALUES(guarantee_refund_amount),
        return_goods_amount = VALUES(return_goods_amount),
        return_logistics_name = VALUES(return_logistics_name),
        reason_name = VALUES(reason_name),
        refund_reason = VALUES(refund_reason),
        buyer_nick = VALUES(buyer_nick),
        operator_name = VALUES(operator_name),
        actual_refund_amount = VALUES(actual_refund_amount),
        revert_reason_name = VALUES(revert_reason_name),
        return_warehouse_no = VALUES(return_warehouse_no),
        direct_refund_amount = VALUES(direct_refund_amount),
        receive_amount = VALUES(receive_amount),
        customer_name = VALUES(customer_name),
        fenxiao_nick_name = VALUES(fenxiao_nick_name),
        status = VALUES(status),
        shop_id = VALUES(shop_id),
        trade_id = VALUES(trade_id),
        check_time = VALUES(check_time)
    </insert>


    <select id="getSkuRefundNumCurrentMonth2"
            resultType="com.daddylab.supplier.item.application.order.settlement.sys.SkuRefundDo">
        select wrod.refund_num,
        wrod.spec_no as skuCode,
        wrod.trade_no,
        wro.return_warehouse_no as warehouseNo,
        wo.shop_no
        <!--               wrapper.price-->
        from wdt_refund_order_detail wrod
        inner join wdt_refund_order wro on wro.refund_no = wrod.refund_no
        inner join wdt_order wo on wrod.trade_no = wo.trade_no
        <!--                 left join wdt_order_detail_wrapper wrapper on wrapper.trade_no = wrod.trade_no-->
        where wro.return_warehouse_no = #{warehouseNo,jdbcType=VARCHAR}
        and wro.check_time <![CDATA[ >= ]]> #{startTime}
        and wro.check_time <![CDATA[ <= ]]> #{endTime}
        and wo.trade_time <![CDATA[ >= ]]> #{startTime}
        and wo.trade_time <![CDATA[ <= ]]> #{endTime}
        and wro.status not in (10, 20)
        and wro.type = 2
        and wrod.deleted_at = 0
        group by wrod.spec_no,wrod.refund_no,wrod.trade_no
    </select>


    <select id="getSkuRefundNumCrossMonth2"
            resultType="com.daddylab.supplier.item.application.order.settlement.sys.SkuRefundDo">
        select
        wrod.refund_num,
        wrod.spec_no as skuCode,
        wrod.trade_no,
        wro.return_warehouse_no as warehouseNo,
        wo.shop_no
        from wdt_refund_order_detail wrod
        inner join wdt_refund_order wro on wro.refund_no = wrod.refund_no
        inner join wdt_order wo on wrod.trade_no = wo.trade_no
        <!--                 left join wdt_order_detail_wrapper wrapper on wrapper.trade_no = wrod.trade_no-->
        where wro.return_warehouse_no = #{warehouseNo,jdbcType=VARCHAR}
        and wro.check_time <![CDATA[ >= ]]> #{startTime}
        and wro.check_time <![CDATA[ <= ]]> #{endTime}
        and wo.trade_time <![CDATA[ < ]]> #{startTime}
        and wro.status not in (10,20)
        and wro.type = 2
        and wrod.deleted_at = 0
        group by wrod.spec_no,wrod.refund_no,wrod.trade_no
    </select>


    <select id="queryCheckedRefundOrderDetailsBatchBySrcOrderNo"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrderDetail">
        select wrod.* from wdt_refund_order_detail wrod join wdt_refund_order wro on wro.refund_no = wrod.refund_no
        where wrod.tid IN
        <foreach item="item" index="index" collection="srcOrderNos" open="(" separator="," close=")">
            #{item}
        </foreach>
        and wro.type = #{type} and wro.status >= 30
    </select>

    <select id="refundManageList"
            resultType="com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.excel.PurchaseBillRow">
        select
        wo.return_warehouse_no as warehouseNo,
        wo.return_logistics_no as logisticsNo,
        wo.return_logistics_name as logisticsCompany,
        concat(wo.reason_name,',',wo.refund_reason) as customerMsg,
        wod.goods_name as goodName,
        wod.suite_no as suitNo,
        wod.goods_no as goodCode,
        wod.tid as originalNo,
        wod.spec_no as erpCode,
        wod.trade_no as orderNo,
        wod.refund_num as erpQuantity,
        wod.remark as customerServiceMsg,
        worder.pay_time as payTime,
        worder.shop_name as shop,
        worder.trade_type as orderType,
        worder.consign_time as deliveryTime
        from
        wdt_refund_order_detail wod
        left join wdt_refund_order wo on wod.refund_no = wo.refund_no
        left join wdt_order worder on wod.trade_no = worder.trade_no
        where
        wo.check_time <![CDATA[ >= ]]> #{cmd.checkStart}
        and wo.check_time <![CDATA[ <= ]]> #{cmd.checkEnd}
        -- 剔除掉退款状态是 【已取消】和【待审核】状态的数据。
        and wo.status not in (10,20)
        and wo.type = 2
        <!--            and wo.return_warehouse_no IN-->
        <!--            <foreach collection="warehouseNos" item="warehouseNo" open="(" separator="," close=")">-->
        <!--                #{warehouseNo}-->
        <!--            </foreach>-->
        and wod.spec_no = #{cmd.skuCode}
    </select>

    <select id="refundManageList2"
            resultType="com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.excel.PurchaseBillRow">
        select
        wo.return_warehouse_no as warehouseNo,
        wo.return_logistics_no as logisticsNo,
        wo.return_logistics_name as logisticsCompany,
        worder.buyer_message as customerMsg,
        wod.goods_name as goodName,
        wod.suite_no as suitNo,
        wod.goods_no as goodCode,
        wod.tid as originalNo,
        wod.spec_no as erpCode,
        wod.trade_no as orderNo,
        wod.refund_num as erpQuantity,
        worder.cs_remark as customerServiceMsg,
        worder.pay_time as payTime,
        worder.shop_name as shop,
        worder.trade_type as orderType,
        worder.consign_time as deliveryTime,
        wo.refund_no as refundNo
        from
        wdt_refund_order_detail wod
        left join wdt_refund_order wo on wod.refund_no = wo.refund_no
        left join wdt_order worder on wod.trade_no = worder.trade_no
        where
        wo.check_time <![CDATA[ >= ]]> #{start}
        and wo.check_time <![CDATA[ <= ]]> #{end}
        -- 剔除掉退款状态是 【已取消】和【待审核】状态的数据。
        and wo.status not in (10,20)
        and wo.type = 2
        <if test="null != skuCodes and skuCodes.size() > 0">
            and wod.spec_no in
            <foreach collection="skuCodes" item="skuCode" open="(" separator="," close=")">
                #{skuCode}
            </foreach>
        </if>
    </select>

    <select id="refundManageList3"
            resultType="com.daddylab.supplier.item.application.order.settlement.dto.RefundManageExportSheet">
        select
        worder.shop_name as shop,
        worder.shop_no as shopNo,
        wo.return_warehouse_no as warehouseNo,
        wod.tid as originalNo,
        wod.trade_no as orderNo,
        wo.refund_no as refundNo,
        worder.stockout_no as stockOutOrderNo,
        wod.goods_name as goodName,
        wod.spec_no as erpCode,
        wod.goods_no as goodCode,
        wod.spec_name as specification,
        wod.refund_num as refundNum,
        wo.return_logistics_name as logisticsCompany,
        wo.return_logistics_no as logisticsNo,
        worder.trade_time as tradeTime,
        wod.total_amount as refundAmount,
        wod.refund_amount as actualRefundAmount,
        wod.suite_no as suiteNo,
        wod.suite_name as suiteName,
        worder.buyer_message as customerMsg,
        worder.cs_remark as customerServiceMsg,
        worder.print_remark as printRemark,
        worder.trade_type as tradeType,
        wo.status as status
        from wdt_refund_order_detail wod
        left join wdt_refund_order wo on wod.refund_no = wo.refund_no
        left join wdt_order worder on wod.trade_no = worder.trade_no
        where
        wod.deleted_at = 0
        and wo.check_time <![CDATA[ >= ]]> #{start}
        and wo.check_time <![CDATA[ <= ]]> #{end}
        and wo.status not in (10,20)
        and wo.type = 2
        and wod.spec_no in
        <foreach collection="skuCodes" item="skuCode" open="(" separator="," close=")">
            #{skuCode}
        </foreach>
        and wo.return_warehouse_no in
        <foreach collection="warehouseNos" item="warehouseNo" open="(" separator="," close=")">
            #{warehouseNo}
        </foreach>
    </select>

    <select id="listByTid"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrder">
        select *
        from wdt_refund_order wro
        where wro.refund_no in
              (select refund_no from wdt_refund_order_detail where tid = #{tid} and deleted_at = 0)
          and wro.deleted_at = 0
    </select>
</mapper>
