<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemOptimizeParticipantMapper">

    <select id="selectBatchByTypeAndUid"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemOptimizeParticipant">
        select * from item_optimize_participant where (type, uid) IN
        <foreach item="item" index="index" collection="collection" open="(" separator="," close=")">
            (#{item.type}, #{item.uid})
        </foreach>
    </select>
</mapper>
