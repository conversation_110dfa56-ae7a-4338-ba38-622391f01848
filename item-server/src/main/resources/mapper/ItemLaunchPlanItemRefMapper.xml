<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemLaunchPlanItemRefMapper">

    <sql id="Base_Query_ItemLaunchPlanItemRef_List">
        FROM item_launch_plan_item_ref as `ref`
        <if test="param.useItemTable">
            inner join item as item on (ref.item_id = item.id)
        </if>
        <where>
            ${ew.sqlSegment}
        </where>
    </sql>

    <select id="selectPageListCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        <include refid="Base_Query_ItemLaunchPlanItemRef_List" />
    </select>

    <select id="selectPageList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchPlanItemRef">
        SELECT *
        <include refid="Base_Query_ItemLaunchPlanItemRef_List" />
        ORDER BY id DESC
    </select>

    <select id="getLinkItemOnNum" resultType="java.lang.Long">
        select
            count(*)
        from
            item_launch_plan_item_ref as t1
            inner join item as t2 on (t1.item_id = t2.id and t2.`launch_status` = 7 and t2.is_del = 0)
        where t1.is_del = 0 and t1.plan_id = #{planId}
    </select>

    <select id="selectPrincipalByPlanId"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemLaunchPlanPrincipalDO">
        SELECT
            ilpir.plan_id,
            ilpir.item_id,
            ng.principal_id
        FROM
            item_launch_plan_item_ref ilpir
            LEFT JOIN new_goods ng ON ilpir.item_id = ng.item_id
        WHERE
            ilpir.plan_id = #{planId}
            AND ilpir.is_del = 0
            AND ng.is_del = 0
    </select>

    <select id="selectItemNameByRefIds" resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemLaunchPlanRefItemDO">
        select ref.id as refId, item.name as itemName
        from item_launch_plan_item_ref ref
                 left join item item
                           on ref.item_id = item.id
        where ref.is_del = 0
          and ref.id in
        <foreach collection="refIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="undelete">
        update item_launch_plan_item_ref set is_del = 0 where id IN
        <foreach item="item" index="index" collection="collection" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>
