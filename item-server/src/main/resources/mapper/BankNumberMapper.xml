<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.BankNumberMapper">


    <select id="bankNumberQuery" resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BankNumber">
        select * from bank_number
        <where>
            is_del = 0
            <if test="query.bankName != null and query.bankName != '' ">and match(bank_name) against(#{query.bankName} in natural language mode)</if>
        </where>
    </select>
</mapper>
