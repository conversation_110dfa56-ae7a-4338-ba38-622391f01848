<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundOrderAmountDetailMapper">
    <insert id="saveOrUpdateBatch">
        INSERT INTO wdt_refund_order_amount_detail (id, rec_id, refund_id, refund_no, refund_type,
                                                    is_return, refund_amount, receive_amount,
                                                    is_guarantee, account_id, pay_account,
                                                    account_name, account_bank, is_auto, remark)
        VALUES
        <foreach collection="details" item="item" separator=",">
            (#{item.id}, #{item.recId}, #{item.refundId}, #{item.refundNo}, #{item.refundType},
             #{item.isReturn}, #{item.refundAmount}, #{item.receiveAmount},
             #{item.isGuarantee}, #{item.accountId}, #{item.payAccount},
             #{item.accountName}, #{item.accountBank}, #{item.isAuto}, #{item.remark})
        </foreach>
        ON DUPLICATE KEY UPDATE `id`             = VALUES(`id`),
                                `rec_id`         = VALUES(`rec_id`),
                                `refund_id`      = VALUES(`refund_id`),
                                `refund_no`      = VALUES(`refund_no`),
                                `refund_type`    = VALUES(`refund_type`),
                                `is_return`      = VALUES(`is_return`),
                                `refund_amount`  = VALUES(`refund_amount`),
                                `receive_amount` = VALUES(`receive_amount`),
                                `is_guarantee`   = VALUES(`is_guarantee`),
                                `account_id`     = VALUES(`account_id`),
                                `pay_account`    = VALUES(`pay_account`),
                                `account_name`   = VALUES(`account_name`),
                                `account_bank`   = VALUES(`account_bank`),
                                `is_auto`        = VALUES(`is_auto`),
                                `remark`         = VALUES(`remark`)
    </insert>
</mapper>
