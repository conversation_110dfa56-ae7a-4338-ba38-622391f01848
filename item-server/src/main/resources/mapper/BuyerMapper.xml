<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.BuyerMapper">

    <select id="getKingDeeIdByItemId" resultType="java.lang.String">
        select
            IFNULL(king_dee_account,'')
        from buyer
        where id = (
            select buyer_id from item_procurement where item_id = #{itemId} and is_del = 0
            ) and is_del = 0
    </select>
</mapper>
