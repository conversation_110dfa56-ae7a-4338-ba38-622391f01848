<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderMapper">
    <select id="purchaseOrderList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.WdtOrderDetailDO">
        select
        wo.pay_time,
        wod.id,
        wod.spec_no,
        wod.num,
        wod.suite_no,
        wod.suite_num,
        wod.platform_id,
        wod.trade_id,
        wod.rec_id,
        wod.refund_num,
        wod.goods_name
        from wdt_order wo
        inner join
        (
        select
        id,
        trade_id,
        rec_id,
        spec_no,
        num,
        suite_no,
        suite_num,
        platform_id,
        refund_num,
        goods_name
        from wdt_order_detail
        where warehouse_no not in
        <foreach collection="eliminateWarehouseNos" item="no" open="(" separator="," close=")">
            #{no}
        </foreach>
        ) wod
        on wod.trade_id = wo.trade_id
        inner join
        (
        select trade_no
        from wdt_sale_stock_out_order wssoo FORCE INDEX (idx_consign_time)
        where wssoo.consign_time <![CDATA[>=]]> #{startDt}
        and wssoo.consign_time <![CDATA[<=]]> #{endDt}
        and wssoo.status >= 10
        ) wssoo
        on wssoo.trade_no = wo.trade_no
        where wo.deleted_at = 0
        group by wod.id
    </select>

    <select id="purchaseOrderCount"
            resultType="java.lang.Integer">
        select count(distinct wo.id)
        from wdt_order wo
        inner join
        (
        select trade_id
        from wdt_order_detail
        where warehouse_no not in
        <foreach collection="eliminateWarehouseNos" item="no" open="(" separator="," close=")">
            #{no}
        </foreach>
        ) wod
        on wod.trade_id = wo.trade_id
        inner join
        (
        select trade_no
        from wdt_sale_stock_out_order wssoo FORCE INDEX(idx_consign_time)
        where wssoo.consign_time <![CDATA[>=]]> #{startDt}
        and wssoo.consign_time<![CDATA[<=]]> #{endDt}
        and wssoo.status >= 10
        ) wssoo
        on wssoo.trade_no = wo.trade_no
        where wo.deleted_at = 0
    </select>

    <select id="orderSingleDetailCount" resultType="java.lang.Integer">
        select count(1)
        from wdt_order wo
        inner join
        (
        select trade_id,
        rec_id,
        spec_no,
        num,
        suite_no,
        suite_num,
        platform_id,
        warehouse_no,
        gift_type,
        provider_id
        from wdt_order_detail
        where
        <!--  子单状态 部分发货-->
        <!--                    platform_status >= 40-->
        <!--                      and platform_status != 90-->
        <!--            子单状态 不是 完成退款-->
        refund_status != 5
        and (suite_no = '' or suite_no is null)
        and warehouse_no not in


        <foreach collection="eliminateWarehouseNos" item="no" open="(" separator="," close=")">
            #{no}
        </foreach>
        ) wod on wod.trade_id = wo.trade_id
        where
        <!--        主单状态 已付款-->
        wo.trade_status >= 95
        <!--        发货时间-->
        and wo.consign_time <![CDATA[<=]]> #{endDt}
        and wo.consign_time >= #{startDt}
        and wo.deleted_at = 0
    </select>
    <select id="orderDetailBySuiteNo"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.WdtOrderDetailDO">
        select wo.pay_time,
               wod.spec_no,
               wod.num,
               wod.suite_no,
               wod.suite_num,
               wod.platform_id,
               wod.trade_id,
               wod.rec_id
        from wdt_order_detail wod
                 left join wdt_order wo on wo.trade_id = wod.trade_id
        where wod.suite_no = #{suiteNo}
          AND (wod.deleted_at = 0 OR wod.deleted_at IS NULL)
          AND (wo.deleted_at = 0 OR wo.deleted_at IS NULL)
    </select>
    <select id="getGroupSkuCode" resultType="java.lang.String">
        select sku_code as skuCode
        from wdt_order_detail_wrapper
        where operate_time = #{operateTime}
        group by sku_code
    </select>


    <resultMap id="OrderBaseInfoResultMap"
               type="com.daddylab.supplier.item.types.order.OrderBaseInfo">
        <result column="wdtOrderNo" property="wdtOrderNo"/>
        <result column="platform" property="wdtPlatformId"/>
        <result column="srcOrderNos" property="srcOrderNos"/>
        <result column="tradeStatus" property="tradeStatus"/>
        <result column="buyerNickname" property="buyerNickname"/>
        <result column="receiverName" property="receiverName"/>
        <result column="shopNo" property="shopNo"/>
        <result column="shopName" property="shopName"/>
        <result column="orderTime" property="orderTime"/>
        <result column="payTime" property="payTime"/>
        <collection property="orderItems" ofType="com.daddylab.supplier.item.types.order.OrderItem"
                    columnPrefix="detail.">
            <id column="id" property="id"/>
            <result column="specName" property="specName"/>
            <result column="specNo" property="specNo"/>
            <result column="price" property="price"/>
            <result column="quantity" property="quantity"/>
            <result column="platformStatus" property="platformStatus"/>
            <result column="itemId" property="itemId"/>
            <result column="itemNo" property="itemNo"/>
            <result column="itemName" property="itemName"/>
            <result column="platformItemId" property="platformItemId"/>
            <result column="platformItemName" property="platformItemName"/>
            <result column="platformSkuName" property="platformSkuName"/>
            <result column="outerPlatformItemId" property="outerPlatformItemId"/>
            <result column="outerPlatformItemNo" property="outerPlatformItemNo"/>
            <result column="outerPlatformSkuId" property="outerPlatformSkuId"/>
            <result column="outerPlatformSkuNo" property="outerPlatformSkuNo"/>
        </collection>
    </resultMap>
    <sql id="queryOrder_Cond">
        <!--@sql SELECT * FROM wdt_order wo WHERE -->
        (wo.deleted_at = 0 OR wo.deleted_at IS NULL)
        <if test="query.wdtOrderNo != null and query.wdtOrderNo != ''">
            AND wo.trade_no = #{query.wdtOrderNo}
        </if>
        <if test="query.shopNo != null and query.shopNo != ''">
            AND wo.shop_no = #{query.shopNo}
        </if>
        <if test="query.platform != null and query.platform.value != 0">
            AND wo.platform_id = #{query.wdtPlatformId}
        </if>
        <if test="query.platform != null and query.platform.value == 0">
            AND wo.platform_id NOT IN (
            <foreach collection="query.allPlatformIds" separator=","
                     item="item">
                #{item}
            </foreach>)
        </if>
        <if test="query.tradeStatus != null and !query.tradeStatus.empty">
            AND wo.trade_status IN
            <foreach collection="query.tradeStatus" item="status" open="(" separator=","
                     close=")">
                #{status}
            </foreach>
        </if>
        <if test="query.buyerNickname != null and query.buyerNickname != ''">
            AND wo.buyer_nick LIKE CONCAT('%', #{query.buyerNickname}, '%')
        </if>
        <if test="query.receiverName != null and query.receiverName != ''">
            AND wo.receiver_name = #{query.receiverName}
        </if>
        <if test="query.receiverMobile != null and query.receiverMobile != ''">
            AND wo.receiver_mobile = #{query.receiverMobile}
        </if>
        <if test="query.orderTimeStart != null">
            AND wo.trade_time >= #{query.orderTimeStartObj}
        </if>
        <if test="query.orderTimeEnd != null">
            <![CDATA[
            AND wo.trade_time <= #{query.orderTimeEndObj}
            ]]>
        </if>
        <if test="query.payTimeStart != null">
            AND wo.pay_time >= #{query.payTimeStartObj}
        </if>
        <if test="query.payTimeEnd != null">
            <![CDATA[
            AND wo.pay_time <= #{query.payTimeEndObj}
            ]]>
        </if>
    </sql>
    <sql id="queryOrderDetails_Cond">
        <!--@sql SELECT * FROM wdt_order wo, wdt_order_detail wod, item i, platform_item pi, provider p WHERE TRUE -->
        AND (wod.deleted_at IS NULL OR wod.deleted_at = 0)
        <if test="query.srcOrderNo != null and query.srcOrderNo != ''">
            AND wod.src_tid = #{query.srcOrderNo}
        </if>
        <if test="query.srcOrderNos != null and !query.srcOrderNos.empty">
            AND wod.src_tid IN
            <foreach collection="query.srcOrderNos" item="item" separator="," open="("
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.specNo != null and query.specNo != ''">
            AND wod.spec_no = #{query.specNo}
        </if>
        <if test="query.itemNo != null and query.itemNo != ''">
            AND wod.goods_no = #{query.itemNo}
        </if>
        <if test="query.itemName != null and query.itemName != ''">
            AND i.name LIKE CONCAT('%', #{query.itemName}, '%')
        </if>
        <if test="query.platformItemId != null and query.platformItemId != ''">
            AND pi.id = #{query.platformItemId}
        </if>
        <if test="query.platformItemName != null and query.platformItemName != ''">
            AND pi.goods_name LIKE CONCAT('%', #{query.platformItemName}, '%')
        </if>
        <if test="query.providerId != null and query.providerId != ''">
            AND p.id = #{query.providerId}
        </if>
    </sql>
    <select id="queryOrderCountWithoutDetailsCond" resultType="java.lang.Long">
        SELECT count(*) AS cnt FROM (
        SELECT wo.id
        FROM wdt_order wo
        <where>
            <include refid="queryOrder_Cond"/>
        </where>
        LIMIT #{query.countLimit}
        ) t
    </select>
    <select id="queryOrderCountWithDetailsCond" resultType="java.lang.Long">
        SELECT count(*) AS cnt FROM (
        SELECT DISTINCT wo.id
        FROM wdt_order wo
        LEFT JOIN wdt_order_detail wod
        ON wod.trade_id = wo.trade_id AND (wod.deleted_at = 0 OR
        wod.deleted_at IS NULL)
        <if test="query.itemName != null and query.itemName != ''">
            LEFT JOIN item_sku `is` ON `is`.sku_code = wod.spec_no
            LEFT JOIN item i ON i.id = `is`.item_id
        </if>
        <if test="query.platformItemId != null and query.platformItemId != '' or query.platformItemName != null and query.platformItemName != ''">
            LEFT JOIN platform_item_sku pis ON pis.outer_item_id = wod.api_goods_id AND
            pis.outer_sku_id = wod.api_spec_id AND
            pis.shop_no = wo.shop_no
            LEFT JOIN platform_item pi ON pi.id = pis.platform_item_id
        </if>
        <if test="query.providerId != null and query.providerId != ''">
            LEFT JOIN item_sku `is` ON `is`.sku_code = wod.spec_no
            LEFT JOIN item i ON i.id = `is`.item_id
            LEFT JOIN provider p ON i.provider_id = p.id
        </if>
        <where>
            <include refid="queryOrder_Cond"/>
            <include refid="queryOrderDetails_Cond"/>
        </where>
        LIMIT #{query.countLimit}
        ) t
    </select>
    <sql id="orderDetails_Join">
        <!--@sql SELECT * FROM wdt_order wo -->
        LEFT JOIN wdt_order_detail wod ON wod.trade_id = wo.trade_id AND (wod.deleted_at = 0 OR
        wod.deleted_at IS NULL)
        LEFT JOIN shop s ON s.sn = wo.shop_no
        LEFT JOIN item_sku `is` ON `is`.sku_code = wod.spec_no
        LEFT JOIN item i ON i.id = `is`.item_id
        LEFT JOIN platform_item_sku pis ON pis.outer_item_id = wod.api_goods_id AND
        pis.outer_sku_id = wod.api_spec_id AND
        pis.shop_no = wo.shop_no
        LEFT JOIN platform_item pi ON pi.id = pis.platform_item_id
        LEFT JOIN provider p ON i.provider_id = p.id
    </sql>
    <sql id="orderBaseInfo_Columns">
        <!--@sql SELECT -->
        wo.trade_no AS 'wdtOrderNo',
        wo.platform_id AS 'platform',
        wo.src_tids AS 'srcOrderNos',
        wo.trade_status AS 'tradeStatus',
        wo.buyer_nick AS 'buyerNickname',
        wo.receiver_name AS 'receiverName',
        wo.shop_no AS 'shopNo',
        wo.shop_name AS 'shopName',
        UNIX_TIMESTAMP(wo.trade_time) AS 'orderTime',
        UNIX_TIMESTAMP(wo.pay_time) AS 'payTime',
        wod.id AS 'detail.id',
        wod.spec_name AS 'detail.specName',
        wod.spec_no AS 'detail.specNo',
        wod.price AS 'detail.price',
        wod.num AS 'detail.quantity',
        wod.platform_status AS 'detail.platformStatus',
        i.id AS 'detail.itemId',
        wod.goods_no AS 'detail.itemNo',
        wod.goods_name AS 'detail.itemName',
        pi.id AS 'detail.platformItemId',
        pi.outer_item_code AS 'detail.outerPlatformItemNo',
        pis.outer_sku_code AS 'detail.outerPlatformSkuNo',
        wod.api_goods_id AS 'detail.outerPlatformItemId',
        wod.api_goods_name AS 'detail.platformItemName',
        wod.api_spec_id AS 'detail.outerPlatformSkuId',
        wod.api_spec_name AS 'detail.platformSkuName'
        <!--@sql FROM wdt_order wo, wdt_order_detail wod, item i, platform_item pi, platform_item_sku pis-->
    </sql>
    <select id="queryOrderIdsWithDetailsCond" resultType="long">
        SELECT id FROM ( SELECT wo.id, any_value(wo.trade_time) AS trade_time
        FROM wdt_order wo
        <include refid="orderDetails_Join"/>
        <where>
            <include refid="queryOrder_Cond"/>
            <include refid="queryOrderDetails_Cond"/>
        </where>
        GROUP BY wo.id
        ORDER BY trade_time DESC, id DESC
        LIMIT #{query.offset}, #{query.pageSize}) t_data
    </select>
    <select id="queryOrderIdsWithoutDetailsCond" resultType="long">
        SELECT wo.id
        FROM wdt_order wo
        <where>
            <include refid="queryOrder_Cond"/>
        </where>
        ORDER BY wo.trade_time DESC, wo.id DESC
        LIMIT #{query.offset}, #{query.pageSize}
    </select>
    <select id="queryOrderByIds" resultMap="OrderBaseInfoResultMap">
        SELECT
        <include refid="orderBaseInfo_Columns"/>
        FROM wdt_order wo
        LEFT JOIN wdt_order_detail wod
        ON wod.trade_id = wo.trade_id AND
        (wod.deleted_at = 0 OR wod.deleted_at IS NULL)
        LEFT JOIN shop s ON s.sn = wo.shop_no
        LEFT JOIN item_sku `is` ON `is`.sku_code = wod.spec_no
        LEFT JOIN item i ON i.id = `is`.item_id
        LEFT JOIN platform_item_sku pis ON pis.outer_item_id = wod.api_goods_id AND
        pis.outer_sku_id = wod.api_spec_id AND
        pis.shop_no = wo.shop_no
        LEFT JOIN platform_item pi ON pi.id = pis.platform_item_id
        LEFT JOIN provider p ON i.provider_id = p.id
        <where>
            wo.id IN
            <foreach item="id" index="index" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        ORDER BY wo.trade_time DESC, wod.created DESC
    </select>
    <resultMap id="OrderDetailResultMap" type="com.daddylab.supplier.item.types.order.OrderDetail">
        <result column="wdtOrderNo" property="wdtOrderNo"/>
        <result column="shopNo" property="shopNo"/>
        <result column="shopName" property="shopName"/>
        <result column="platformId" property="wdtPlatformId"/>
        <result column="srcOrderNos" property="srcOrderNos"/>
        <result column="tradeStatus" property="tradeStatus"/>
        <result column="orderTime" property="orderTime"/>
        <result column="payTime" property="payTime"/>
        <result column="warehouseNo" property="warehouseNo"/>
        <result column="warehouseName" property="warehouseName"/>
        <result column="logisticsName" property="logisticsName"/>
        <result column="logisticsNo" property="logisticsNo"/>
        <result column="buyerNickname" property="buyerNickname"/>
        <result column="receiverName" property="receiverName"/>
        <result column="receiverMobile" property="receiverMobile"/>
        <result column="receiverTel" property="receiverTel"/>
        <result column="receiverAddress" property="receiverAddress"/>
        <result column="receiverArea" property="receiverArea"/>
        <result column="receiverZip" property="receiverZip"/>
        <result column="buyerRemark" property="buyerRemark"/>
        <result column="csRemark" property="csRemark"/>
        <result column="totalPrice" property="totalPrice"/>
        <result column="discountAmount" property="discountAmount"/>
        <result column="postAmount" property="postAmount"/>
        <result column="receivableAmount" property="receivableAmount"/>
        <result column="providerId" property="providerId"/>
        <collection property="itemDetails" ofType="com.daddylab.supplier.item.types.order.OrderItem"
                    columnPrefix="detail.">
            <id column="id" property="id"/>
            <result column="specName" property="specName"/>
            <result column="specNo" property="specNo"/>
            <result column="price" property="price"/>
            <result column="quantity" property="quantity"/>
            <result column="platformStatus" property="platformStatus"/>
            <result column="itemId" property="itemId"/>
            <result column="itemNo" property="itemNo"/>
            <result column="itemName" property="itemName"/>
            <result column="imgUrl" property="itemMainImg"/>
            <result column="platformItemId" property="platformItemId"/>
            <result column="platformItemName" property="platformItemName"/>
            <result column="platformSkuName" property="platformSkuName"/>
            <result column="outerPlatformItemId" property="outerPlatformItemId"/>
            <result column="outerPlatformItemNo" property="outerPlatformItemNo"/>
            <result column="outerPlatformSkuId" property="outerPlatformSkuId"/>
            <result column="outerPlatformSkuNo" property="outerPlatformSkuNo"/>
        </collection>
    </resultMap>
    <select id="orderDetailQuery"
            resultMap="OrderDetailResultMap">
        SELECT wo.trade_no AS 'wdtOrderNo', wo.shop_no AS 'shopNo', wo.platform_id AS platformId,
        wo.src_tids AS 'srcOrderNos', wo.trade_status AS 'tradeStatus', wo.buyer_nick AS 'buyerNickname',
        wo.receiver_name AS 'receiverName', wo.receiver_mobile AS 'receiverMobile', wo.receiver_telno AS 'receiverTel',
        wo.receiver_area AS 'receiverArea',
        wo.receiver_address AS 'receiverAddress', wo.receiver_zip AS 'receiverZip', wo.buyer_message AS 'buyerRemark',
        wo.cs_remark AS 'csRemark', wo.shop_no AS 'shopNo', wo.shop_name AS 'shopName', UNIX_TIMESTAMP(wo.trade_time) AS
        'orderTime', UNIX_TIMESTAMP(wo.pay_time) AS 'payTime', wo.warehouse_no AS 'warehouseNo', w.name AS
        'warehouseName', wo.logistics_name AS 'logisticsName', wo.logistics_no AS 'logisticsNo', p.id AS 'providerId',
        wo.goods_amount AS 'totalPrice', wo.discount AS 'discountAmount', wo.post_amount AS 'postAmount', wo.receivable
        AS 'receivableAmount', wod.id AS 'detail.id', wod.spec_name AS 'detail.specName', wod.spec_no AS
        'detail.specNo', wod.price AS 'detail.price', wod.num AS 'detail.quantity', wod.platform_status AS
        'detail.platformStatus', i.id AS 'detail.itemId', wod.img_url AS 'detail.imgUrl', wod.goods_no AS
        'detail.itemNo', wod.goods_name AS 'detail.itemName', pi.id AS 'detail.platformItemId', pi.outer_item_code AS
        'detail.outerPlatformItemNo', pis.outer_sku_code AS 'detail.outerPlatformSkuNo', wod.api_goods_id AS
        'detail.outerPlatformItemId', wod.api_goods_name AS 'detail.platformItemName', wod.api_spec_id AS
        'detail.outerPlatformSkuId', wod.api_spec_name AS 'detail.platformSkuName'
        FROM wdt_order wo
                 LEFT JOIN wdt_order_detail wod
                           ON wod.trade_id = wo.trade_id and
                              (wod.deleted_at = 0 OR wod.deleted_at IS NULL)
                 LEFT JOIN item i ON i.code = wod.goods_no
                 LEFT JOIN platform_item_sku pis ON pis.outer_item_id = wod.api_goods_id AND
                                                    pis.outer_sku_id = wod.api_spec_id AND
                                                    pis.shop_no = wo.shop_no
                 LEFT JOIN platform_item pi ON pi.id = pis.platform_item_id
                 LEFT JOIN provider p ON i.provider_id = p.id
                 LEFT JOIN warehouse w ON w.no = wo.warehouse_no
        WHERE wo.trade_no = #{orderNo}
    </select>
    <resultMap id="OrderRefundDetailResultMap"
               type="com.daddylab.supplier.item.types.order.OrderRefundDetail">
        <result column="id" property="id"/>
        <result column="refundOrderNo" property="refundOrderNo"/>
        <result column="createTime" property="createTime"/>
        <result column="type" property="type"/>
        <collection property="items" ofType="com.daddylab.supplier.item.types.order.OrderRefundItem"
                    columnPrefix="item.">
            <result column="id" property="id"/>
            <result column="srcRefundOrderNos" property="srcRefundOrderNos"/>
            <result column="itemId" property="itemId"/>
            <result column="skuId" property="skuId"/>
            <result column="itemName" property="itemName"/>
            <result column="platformItemName" property="platformItemName"/>
            <result column="specName" property="specName"/>
            <result column="specNo" property="specNo"/>
            <result column="brandName" property="brandName"/>
            <result column="orderQuantity" property="orderQuantity"/>
            <result column="price" property="price"/>
            <result column="refundQuantity" property="refundQuantity"/>
            <result column="refundAmount" property="refundAmount"/>
        </collection>
    </resultMap>
    <select id="orderRefundItemsQuery"
            resultMap="OrderRefundDetailResultMap">
        SELECT wro.id,
               wro.refund_no               AS refundOrderNo,
               UNIX_TIMESTAMP(wro.created) AS createTime,
               wro.type,
               wrod.id AS 'item.id', wrod.raw_refund_nos AS 'item.srcRefundOrderNos', i.id AS 'item.itemId', i.name AS 'item.itemName', `is`.id AS 'item.skuId', wrod.api_goods_name AS 'item.platformItemName', wrod.spec_name AS 'item.specName', wrod.spec_no AS 'item.specNo', b.name AS 'item.brandName', wrod.num AS 'item.orderQuantity', wrod.price AS 'item.price', wrod.refund_num AS 'item.refundQuantity', wrod.refund_amount AS 'item.refundAmount'
        FROM wdt_refund_order wro
                 LEFT JOIN wdt_refund_order_detail wrod ON wrod.refund_no = wro.refund_no
                 LEFT JOIN item_sku `is` ON `is`.sku_code = wrod.spec_no
                 LEFT JOIN item i ON `is`.item_id = i.id
                 LEFT JOIN brand b ON b.id = i.brand_id
        WHERE wrod.trade_no = #{orderNo}
    </select>

    <select id="purchaseTabQuery"
            resultType="com.daddylab.supplier.item.types.order.PurchaseOrderTabVO">
        select wod.trade_id as wdtTradeId,
        wo.trade_status as tradeStatus,
        wo.shop_name as shop,
        UNIX_TIMESTAMP(wo.trade_time) as tradeTime,
        UNIX_TIMESTAMP(wo.pay_time) as payTime,
        UNIX_TIMESTAMP(wo.consign_time) as consignTime,
        wh.name as warehouse,
        wo.platform_id as wdtPlatformId,
        wod.goods_name as itemName,
        wod.spec_code as specifications,
        wod.spec_no as skuCode,
        wod.num as num
        from wdt_order_detail wod
        left join wdt_order wo on wod.trade_id = wo.trade_id
        left join warehouse wh on wh.no = wo.warehouse_no
        where (wo.consign_time between #{query.startDt} and #{query.endDt})
        and provider_id = #{query.providerId}
        and wod.deleted_at = 0
        and wo.deleted_at = 0
        <!--        limit #{query.offset},#{query.pageSize}-->
    </select>

    <insert id="saveOrUpdateBatch">
        INSERT INTO `wdt_order` (`id`, `trade_id`, `trade_no`, `platform_id`,
        `warehouse_type`, `src_tids`,
        `pay_account`, `trade_status`, `trade_type`,
        `delivery_term`, `receiver_ring`,
        `freeze_reason`, `refund_status`, `fenxiao_type`,
        `fenxiao_nick`, `trade_time`,
        `pay_time`, `consign_time`, `buyer_nick`,
        `receiver_name`, `receiver_province`,
        `receiver_city`, `receiver_district`,
        `receiver_address`, `receiver_mobile`,
        `receiver_telno`, `receiver_zip`, `receiver_area`,
        `receiver_dtb`,
        `to_deliver_time`, `bad_reason`, `logistics_no`,
        `buyer_message`, `cs_remark`,
        `remark_flag`, `print_remark`, `goods_type_count`,
        `goods_count`, `goods_amount`,
        `post_amount`, `other_amount`, `discount`, `receivable`,
        `cod_amount`,
        `ext_cod_fee`, `goods_cost`, `post_cost`, `weight`,
        `profit`, `tax`, `tax_rate`,
        `commission`, `invoice_type`, `invoice_title`,
        `invoice_content`, `salesman_name`,
        `checker_name`, `fchecker_name`, `checkouter_name`,
        `stockout_no`, `flag_name`,
        `trade_from`, `single_spec_no`, `raw_goods_count`,
        `raw_goods_type_count`,
        `currency`, `invoice_id`, `version_id`, `modified`,
        `created`, `id_card_type`,
        `id_card`, `shop_no`, `shop_name`, `shop_remark`,
        `warehouse_no`, `customer_name`,
        `customer_no`, `logistics_name`, `logistics_code`,
        `logistics_type_name`,
        `delay_to_time`, `trade_label`, `deleted_at`, `check_time`)
        VALUES
        <foreach collection="orders" item="order" separator=",">
            (NULL, #{order.tradeId}, #{order.tradeNo}, #{order.platformId}, #{order.warehouseType},
            #{order.srcTids},
            #{order.payAccount}, #{order.tradeStatus}, #{order.tradeType}, #{order.deliveryTerm},
            #{order.receiverRing},
            #{order.freezeReason}, #{order.refundStatus}, #{order.fenxiaoType},
            #{order.fenxiaoNick}, #{order.tradeTime},
            #{order.payTime}, #{order.consignTime}, #{order.buyerNick}, #{order.receiverName},
            #{order.receiverProvince},
            #{order.receiverCity}, #{order.receiverDistrict}, #{order.receiverAddress},
            #{order.receiverMobile},
            #{order.receiverTelno}, #{order.receiverZip}, #{order.receiverArea},
            #{order.receiverDtb},
            #{order.toDeliverTime}, #{order.badReason}, #{order.logisticsNo},
            #{order.buyerMessage}, #{order.csRemark},
            #{order.remarkFlag}, #{order.printRemark}, #{order.goodsTypeCount},
            #{order.goodsCount}, #{order.goodsAmount},
            #{order.postAmount}, #{order.otherAmount}, #{order.discount}, #{order.receivable},
            #{order.codAmount},
            #{order.extCodFee}, #{order.goodsCost}, #{order.postCost}, #{order.weight},
            #{order.profit}, #{order.tax}, #{order.taxRate},
            #{order.commission}, #{order.invoiceType}, #{order.invoiceTitle},
            #{order.invoiceContent}, #{order.salesmanName},
            #{order.checkerName}, #{order.fcheckerName}, #{order.checkouterName},
            #{order.stockoutNo}, #{order.flagName},
            #{order.tradeFrom}, #{order.singleSpecNo}, #{order.rawGoodsCount},
            #{order.rawGoodsTypeCount},
            #{order.currency}, #{order.invoiceId}, #{order.versionId}, #{order.modified},
            #{order.created}, #{order.idCardType},
            #{order.idCard}, #{order.shopNo}, #{order.shopName}, #{order.shopRemark},
            #{order.warehouseNo}, #{order.customerName},
            #{order.customerNo}, #{order.logisticsName}, #{order.logisticsCode},
            #{order.logisticsTypeName},
            #{order.delayToTime}, #{order.tradeLabel}, 0, #{order.checkTime})
        </foreach>
        ON DUPLICATE KEY UPDATE `trade_no` =
        VALUES (`trade_no`),
        `platform_id` =
        VALUES (`platform_id`),
        `warehouse_type` =
        VALUES (`warehouse_type`),
        `src_tids` =
        VALUES (`src_tids`),
        `pay_account` =
        VALUES (`pay_account`),
        `trade_status` =
        VALUES (`trade_status`),
        `trade_type` =
        VALUES (`trade_type`),
        `delivery_term` =
        VALUES (`delivery_term`),
        `receiver_ring` =
        VALUES (`receiver_ring`),
        `freeze_reason` =
        VALUES (`freeze_reason`),
        `refund_status` =
        VALUES (`refund_status`),
        `fenxiao_type` =
        VALUES (`fenxiao_type`),
        `fenxiao_nick` =
        VALUES (`fenxiao_nick`),
        `trade_time` =
        VALUES (`trade_time`),
        `pay_time` =
        VALUES (`pay_time`),
        `consign_time` =
        VALUES (`consign_time`),
        `buyer_nick` =
        VALUES (`buyer_nick`),
        `receiver_name` =
        VALUES (`receiver_name`),
        `receiver_province` =
        VALUES (`receiver_province`),
        `receiver_city` =
        VALUES (`receiver_city`),
        `receiver_district` =
        VALUES (`receiver_district`),
        `receiver_address` =
        VALUES (`receiver_address`),
        `receiver_mobile` =
        VALUES (`receiver_mobile`),
        `receiver_telno` =
        VALUES (`receiver_telno`),
        `receiver_zip` =
        VALUES (`receiver_zip`),
        `receiver_area` =
        VALUES (`receiver_area`),
        `receiver_dtb` =
        VALUES (`receiver_dtb`),
        `to_deliver_time` =
        VALUES (`to_deliver_time`),
        `bad_reason` =
        VALUES (`bad_reason`),
        `logistics_no` =
        VALUES (`logistics_no`),
        `buyer_message` =
        VALUES (`buyer_message`),
        `cs_remark` =
        VALUES (`cs_remark`),
        `remark_flag` =
        VALUES (`remark_flag`),
        `print_remark` =
        VALUES (`print_remark`),
        `goods_type_count` =
        VALUES (`goods_type_count`),
        `goods_count` =
        VALUES (`goods_count`),
        `goods_amount` =
        VALUES (`goods_amount`),
        `post_amount` =
        VALUES (`post_amount`),
        `other_amount` =
        VALUES (`other_amount`),
        `discount` =
        VALUES (`discount`),
        `receivable` =
        VALUES (`receivable`),
        `cod_amount` =
        VALUES (`cod_amount`),
        `ext_cod_fee` =
        VALUES (`ext_cod_fee`),
        `goods_cost` =
        VALUES (`goods_cost`),
        `post_cost` =
        VALUES (`post_cost`),
        `weight` =
        VALUES (`weight`),
        `profit` =
        VALUES (`profit`),
        `tax` =
        VALUES (`tax`),
        `tax_rate` =
        VALUES (`tax_rate`),
        `commission` =
        VALUES (`commission`),
        `invoice_type` =
        VALUES (`invoice_type`),
        `invoice_title` =
        VALUES (`invoice_title`),
        `invoice_content` =
        VALUES (`invoice_content`),
        `salesman_name` =
        VALUES (`salesman_name`),
        `checker_name` =
        VALUES (`checker_name`),
        `fchecker_name` =
        VALUES (`fchecker_name`),
        `checkouter_name` =
        VALUES (`checkouter_name`),
        `stockout_no` =
        VALUES (`stockout_no`),
        `flag_name` =
        VALUES (`flag_name`),
        `trade_from` =
        VALUES (`trade_from`),
        `single_spec_no` =
        VALUES (`single_spec_no`),
        `raw_goods_count` =
        VALUES (`raw_goods_count`),
        `raw_goods_type_count` =
        VALUES (`raw_goods_type_count`),
        `currency` =
        VALUES (`currency`),
        `invoice_id` =
        VALUES (`invoice_id`),
        `version_id` =
        VALUES (`version_id`),
        `modified` =
        VALUES (`modified`),
        `created` =
        VALUES (`created`),
        `id_card_type` =
        VALUES (`id_card_type`),
        `id_card` =
        VALUES (`id_card`),
        `shop_no` =
        VALUES (`shop_no`),
        `shop_name` =
        VALUES (`shop_name`),
        `shop_remark` =
        VALUES (`shop_remark`),
        `warehouse_no` =
        VALUES (`warehouse_no`),
        `customer_name` =
        VALUES (`customer_name`),
        `customer_no` =
        VALUES (`customer_no`),
        `logistics_name` =
        VALUES (`logistics_name`),
        `logistics_code` =
        VALUES (`logistics_code`),
        `logistics_type_name` =
        VALUES (`logistics_type_name`),
        `delay_to_time` =
        VALUES (`delay_to_time`),
        `trade_label` =
        VALUES (`trade_label`),
        `deleted_at` = 0,
        `check_time` = VALUES (`check_time`)
        ;
    </insert>

    <select id="orderSuiteDetailList" resultType="java.lang.Long">
        select wod.trade_id
        <!--            GROUP_CONCAT(wod.id) as wdtOrderDetailDbIdList-->
        from wdt_order wo
        inner join (
        select id,
        trade_id,
        suite_no
        from wdt_order_detail
        where
        <!--                    platform_status >= 40 -->
        <!--                    and platform_status != 90 -->
        refund_status != 5
        and suite_no != ''
        and warehouse_no not in


        <foreach collection="eliminateWarehouseNos" item="no" open="(" separator="," close=")">
            #{no}
        </foreach>
        ) wod on wod.trade_id = wo.trade_id
        where
        <!--        主单状态 已付款-->
        wo.trade_status >= 95
        <!--        发货时间-->
        and wo.consign_time <![CDATA[>=]]> #{startDt}
        and wo.consign_time <![CDATA[<=]]> #{endDt}
        and wo.deleted_at = 0
        <!--        group by wod.trade_id-->
        <!--  limit #{offset},#{size} -->
    </select>

    <!--    <select id="orderSuiteDetailCount" resultType="java.lang.Integer">-->
    <!--        select-->
    <!--            count(1)-->
    <!--        from (-->
    <!--        select wod.trade_id,-->
    <!--               GROUP_CONCAT(wod.id)-->
    <!--        from wdt_order wo FORCE INDEX (idx_trade_status_consign_time)-->
    <!--                 inner join (-->
    <!--            select id,-->
    <!--                   trade_id,-->
    <!--                   suite_no-->
    <!--            from wdt_order_detail-->
    <!--            where platform_status >= 40-->
    <!--              and platform_status != 90-->
    <!--              and refund_status != 5-->
    <!--              and suite_no != ''-->
    <!--        ) wod on wod.trade_id = wo.trade_id-->
    <!--        where-->
    <!--        &lt;!&ndash;        主单状态 已付款&ndash;&gt;-->
    <!--        wo.trade_status > 12-->
    <!--        &lt;!&ndash;        发货时间&ndash;&gt;-->
    <!--        and wo.consign_time <![CDATA[>=]]> #{startDt}-->
    <!--        and wo.consign_time <![CDATA[<=]]> #{endDt}-->
    <!--        group by wod.trade_id-->
    <!--        ) tmp-->
    <!--    </select>-->

    <select id="getByDetailDbId"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.WdtOrderDetailDO">
        select wo.pay_time,
               wod.spec_no,
               wod.num,
               wod.suite_no,
               wod.suite_num,
               wod.platform_id,
               wod.trade_id,
               wod.rec_id,
               wod.warehouse_no,
               wod.gift_type,
               wod.provider_id,
               wod.refund_num
        from wdt_order wo
                 inner join (select trade_id,
                                    rec_id,
                                    spec_no,
                                    num,
                                    suite_no,
                                    suite_num,
                                    platform_id,
                                    warehouse_no,
                                    gift_type,
                                    provider_id,
                                    refund_num
                             from wdt_order_detail
                             where id = #{id}
                               and deleted_at = 0) wod on wod.trade_id = wo.trade_id
    </select>

    <select id="orderSingleDetailListByDbIds"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.WdtOrderDetailDO">
        select wo.pay_time,
        wod.spec_no,
        wod.num,
        wod.suite_no,
        wod.suite_num,
        wod.platform_id,
        wod.trade_id,
        wod.rec_id,
        wod.warehouse_no,
        wod.gift_type,
        wod.provider_id
        from
        wdt_order wo
        inner join (
        select trade_id,
        rec_id,
        spec_no,
        num,
        suite_no,
        suite_num,
        platform_id,
        warehouse_no,
        gift_type,
        provider_id
        from wdt_order_detail
        where id in
        <foreach collection="idList" item="index" open="(" close=")" separator=",">
            #{index}
        </foreach>
        and deleted_at = 0
        ) wod on wod.trade_id = wo.trade_id
    </select>

    <select id="isSameProvider" resultType="java.lang.Long">
        select provider_id
        from wdt_order_detail
        where id in
        <foreach collection="idList" item="index" open="(" close=")" separator=",">
            #{index}
        </foreach>
        group by provider_id
    </select>

    <select id="purchaseTabCount" resultType="java.lang.Integer">
        select count(1)
        from wdt_order_detail wod
                 left join wdt_order wo on wod.trade_id = wo.trade_id
                 left join warehouse wh on wh.no = wo.warehouse_no
        where (wo.consign_time between #{query.startDt} and #{query.endDt})
          and provider_id = #{query.providerId}
          and wod.deleted_at = 0
          and wo.deleted_at = 0
    </select>

    <select id="queryOrderSensitiveInfoBatchBySrcOrderNo"
            resultType="com.daddylab.supplier.item.domain.order.OrderSensitiveInfo">
        SELECT wod.src_tid AS orderNo,
        IFNULL(wo.receiver_mobile, wo.receiver_telno) AS receiverPhone,
        wo.shop_name AS shopName,
        wo.receiver_name AS receiverName,
        wo.receiver_address AS receiverAddress
        FROM wdt_order wo
        JOIN wdt_order_detail wod ON wo.trade_id = wod.trade_id
        WHERE (wod.deleted_at IS NULL OR wod.deleted_at = 0)
        AND (wo.deleted_at = 0 OR wo.deleted_at IS NULL)
        AND wod.src_tid IN
        <foreach collection="collection" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <update id="cleanOrders">
        UPDATE wdt_order wo LEFT JOIN wdt_order_detail wod
        ON wo.trade_id = wod.trade_id and
        (wod.deleted_at = 0 OR wod.deleted_at IS NULL )
        SET wo.deleted_at = #{currentTime}
        WHERE wod.id IS NULL
        AND wo.trade_id IN
        <foreach collection="tradeIds" item="tradeId" open="(" separator="," close=")">
            #{tradeId}
        </foreach>
    </update>

    <select id="selectDeletedTradeIdList"
            resultType="long">
        select trade_id from wdt_order where deleted_at > 0 AND trade_id IN
        <foreach collection="tradeIds" item="tradeId" open="(" separator="," close=")">
            #{tradeId}
        </foreach>
    </select>

    <select id="wmsStockOutDetailList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.WdtOrderDetailDO">
        SELECT wssood.spec_no AS specNo,
        wssood.goods_count AS num,
        wssoo.trade_time AS payTime,
        wssoo.trade_no,
        wssoo.warehouse_no,
        wssood.gift_type,
        wssood.suite_no AS suiteNo,
        wssoo.platform_id
        FROM wdt_sale_stock_out_order wssoo
        JOIN wdt_sale_stock_out_order_details wssood
        ON wssood.stockout_id = wssoo.stockout_id
        <where>
            <if test="startDt != '' and startDt != null and endDt != '' and endDt != null">
                wssoo.consign_time <![CDATA[>=]]> #{startDt}
                and wssoo.consign_time <![CDATA[<=]]> #{endDt}
            </if>
            <if test="eliminateWarehouseNos != null and eliminateWarehouseNos.size > 0">
                and wssoo.warehouse_no not in
                <foreach collection="eliminateWarehouseNos" item="warehouseNo" open="(" separator="," close=")">
                    #{warehouseNo}
                </foreach>
            </if>
            <if test="externalShopNos != null and externalShopNos.size > 0">
                and wssoo.shop_no not in
                <foreach collection="externalShopNos" item="shopNo" open="(" separator="," close=")">
                    #{shopNo}
                </foreach>
            </if>
            <if test="skuCode != null and skuCode != ''">
                AND wssood.spec_no = #{skuCode}
            </if>
            <if test="tradeNo != null and tradeNo != ''">
                and wssoo.trade_no = #{tradeNo}
            </if>
        </where>
    </select>

    <select id="wmsStockOutDetailList2"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.WdtOrderDetailDO">
        SELECT wssood.spec_no AS specNo,
        wssood.goods_count AS num,
        wssoo.trade_time AS payTime,
        wssoo.trade_no,
        wssoo.warehouse_no,
        wssoo.consign_time,
        wod.suite_no,
        wod.platform_id
        FROM wdt_sale_stock_out_order wssoo
        INNER JOIN wdt_sale_stock_out_order_details wssood
        ON wssood.stockout_id = wssoo.stockout_id
        LEFT JOIN wdt_order wo ON wo.trade_no = wssoo.trade_no
        LEFT JOIN wdt_order_detail wod ON wod.trade_id = wo.trade_id
        WHERE wssoo.consign_time <![CDATA[>=]]> #{startDt}
        AND wssoo.consign_time <![CDATA[<=]]> #{endDt}
        AND wssoo.warehouse_no NOT IN
        <foreach collection="eliminateWarehouseNos" item="warehouseNo" open="(" separator=","
                 close=")">
            #{warehouseNo}
        </foreach>
        <if test="skuCode != null and skuCode != ''">
            AND wssood.spec_no = #{skuCode}
        </if>
        GROUP BY wssoo.trade_no;
    </select>


    <select id="wmsStockOutTradeDetailList"
            resultType="com.daddylab.supplier.item.application.purchase.order.factory.dto.SkuTradeBO">
        select
        wo.trade_no,wod.suite_no,wod.platform_id,wod.spec_no as skuCode
        from wdt_order wo
        inner join wdt_order_detail wod on wod.trade_id = wo.trade_id
        where
        wod.warehouse_no not in
        <foreach collection="eliminateWarehouseNos" item="warehouseNo" open="(" separator="," close=")">
            #{warehouseNo}
        </foreach>
        and
        wo.trade_no in
        <foreach collection="tradeNoList" item="tradeNo" open="(" separator="," close=")">
            #{tradeNo}
        </foreach>
        and wod.spec_no = #{specNo}
    </select>

    <select id="selectIdBySrcTids" resultType="java.lang.Long">
        select id from wdt_order
        where
        platform_id = #{platformId}
        and src_tids
        in
        <foreach collection="srcTids" item="tid" open="(" separator="," close=")">
            #{tid}
        </foreach>
    </select>

    <select id="tradeNos" resultType="java.lang.String">
        select trade_no from wdt_order
        where
        id in
        <foreach collection="wdtIds" item="wdtId" open="(" separator="," close=")">
            #{wdtId}
        </foreach>
    </select>

    <select id="selectOrderWarehouseByOrderNos" resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.OrderWarehouseVO">
        select src_tid as orderNo, warehouse_no, warehouse.name as warehouseName, spec_no as skuCode, spec_name as
        skuName from wdt_order_detail wod
        left join warehouse on wod.warehouse_no = warehouse.no
        where wod.src_tid in
        <foreach item="item" index="index" collection="collection" open="(" separator="," close=")">
            #{item}
        </foreach>
        and wod.deleted_at = 0;
    </select>

    <select id="selectOrderDetailPrimaryIdObjs"
            resultType="com.daddylab.supplier.item.types.order.OrderDetailPrimaryIdObj">
        select wo.id as orderId, wo.trade_id as tradeId, wod.id as orderDetailId, wod.rec_id as orderDetailRecId, wo.deleted_at as deletedAt, wod.deleted_at as detailDeletedAt
        from wdt_order wo left join wdt_order_detail wod on wo.trade_id = wod.trade_id
        where wo.trade_id in
        <foreach item="item" index="index" collection="tradeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteByTradeIds">
        update wdt_order set deleted_at = unix_timestamp() where trade_id in
        <foreach item="item" index="index" collection="tradeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and (deleted_at = 0 OR deleted_at is null);
    </delete>

    <select id="queryWdtOrderLogisticSheet"
            resultType="com.daddylab.supplier.item.controller.test.dto.WdtOrderLogisticDto">
        select wo.src_tids,
        wo.trade_no,
        wo.shop_name,
        wo.pay_time,
        wo.consign_time,
        from_unixtime(odt.signing_time) as signingTime,
        wo.trade_status,
        wssor.warehouse_no,
        wssor.warehouse_name,
        wo.logistics_name,
        wo.logistics_no,
        kc.tracklist
        from wdt_order wo
        left join wdt_sale_stock_out_order wssor on wo.stockout_no = wssor.order_no
        left join order_logistics_trace odt on wo.trade_no = odt.wdt_trade_no
        left join kdy_callback kc on odt.callback_id = kc.id
        where wo.pay_time <![CDATA[>=]]> #{start}
        and wo.pay_time <![CDATA[<=]]> #{end}
    </select>
    <select id="listBySrcTid"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrder">
        select *
        from wdt_order
        where trade_id in (select trade_id from wdt_order_detail where src_tid = #{srcTid} and deleted_at = 0)
          and deleted_at = 0;
    </select>
</mapper>
