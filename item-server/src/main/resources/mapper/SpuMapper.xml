<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.SpuMapper">
    <sql id="filters_ItemReferenceQuery">
        <!--@sql SELECT * FROM spu,item i,item_procurement ip,buyer b2,item_launch_plan_item_ref plan_ref WHERE -->
        spu.is_del = 0
        <if test="query.itemCode != null and query.itemCode != ''">
            AND (spu.`item_code` = #{query.itemCode} OR
                 i.`provider_specified_code` = #{query.itemCode})
        </if>
        <if test="query.itemName != null and query.itemName != ''">
            AND i.`name` LIKE CONCAT('%', #{query.itemName}, '%')
        </if>
        <if test="query.brandId != null">
            AND i.brand_id = #{query.brandId}
        </if>
        <if test="query.categoryId != null">
            AND i.category_id = #{query.categoryId}
        </if>
        <if test="query.providerId != null">
            AND i.provider_id = #{query.providerId}
        </if>
        <if test="query.buyerUid != null">
            AND b2.user_id = #{query.buyerUid}
        </if>
        <if test="query.delivery != null">
            <if test="query.delivery == 2">
                AND ip.`delivery` IN ('0,1', '1,0')
            </if>
            <if test="query.delivery != 2">
                AND ip.delivery = #{query.delivery}
            </if>
        </if>
        <if test="query.itemStatus != null">
            AND i.status = #{query.itemStatus}
        </if>
        <if test="query.shelfStartTime != null and query.shelfStartTime > 0 and query.shelfEndTime != null and query.shelfEndTime > 0">
            AND i.estimate_sale_time between #{query.shelfStartTime} and #{query.shelfEndTime}
        </if>
        <include refid="filters_itemBusinessLine"/>
    </sql>

    <sql id="filters_itemBusinessLine">
        /*@BizScope_expression@*/
    </sql>
    <select id="itemReferenceSpuQueryCount" resultType="long">
        SELECT COUNT(DISTINCT `spu`.`id`)
        FROM `spu`
                 JOIN `item` `i` ON `i`.`id` = `spu`.`item_id` AND `i`.`is_del` = 0
                 LEFT JOIN `item_procurement` `ip`
                           ON `i`.`id` = `ip`.`item_id` AND `ip`.`is_del` = 0
                 LEFT JOIN `buyer` `b2` ON `ip`.`buyer_id` = `b2`.`id` AND `b2`.`is_del` = 0
                 LEFT JOIN `provider` `p` ON `i`.`provider_id` = `p`.`id` AND `p`.`is_del` = 0
        <where>
            <include refid="filters_ItemReferenceQuery"/>
        </where>
    </select>

    <select id="itemReferenceSpuQuery"
            resultType="com.daddylab.supplier.item.application.itemReference.types.ItemReferenceSpu">
        SELECT ANY_VALUE(`t1`.`itemCode`)              AS `itemCode`,
               ANY_VALUE(`t1`.`itemId`)                AS `itemId`,
               ANY_VALUE(`t1`.`itemName`)              AS `itemName`,
               ANY_VALUE(`t1`.`partnerProviderItemSn`) AS `partnerProviderItemSn`,
               COUNT(`sku`.`id`)                       AS `skuNum`,
               ANY_VALUE(`t1`.`categoryId`)            AS `categoryId`,
               ANY_VALUE(`t1`.`categoryPath`)          AS `categoryPath`,
               ANY_VALUE(`t1`.`brandId`)               AS `brandId`,
               ANY_VALUE(`t1`.`brandName`)             AS `brandName`,
               ANY_VALUE(`t1`.`providerId`)            as `providerId`,
               ANY_VALUE(`t1`.`partnerProviderId`)     as `partnerProviderId`,
               ANY_VALUE(`t1`.`providerName`)          as `provider`,
               ANY_VALUE(`t1`.`buyerUid`)              AS `buyerUid`,
               ANY_VALUE(`t1`.`delivery`)              AS `delivery`,
               ANY_VALUE(`t1`.`taxRate`)               AS `taxRate`,
               ANY_VALUE(`t1`.`purchaseRate`)          AS `purchaseRate`,
               ANY_VALUE(`t1`.`taxRateCode`)           AS `taxRateCode`,
               ANY_VALUE(`t1`.`itemStatus`)            AS `itemStatus`,
               ANY_VALUE(`t1`.`launchTime`)            AS `launchTime`,
               ANY_VALUE(`t1`.`businessLine`)          AS `businessLine`
        FROM (SELECT `spu`.`item_code`              AS `itemCode`,
                     `spu`.item_id                  AS `itemId`,
                     `i`.`name`                     AS `itemName`,
                     `i`.`partner_provider_item_sn` AS `partnerProviderItemSn`,
                     `i`.`category_id`              AS `categoryId`,
                     `c`.`path`                     AS `categoryPath`,
                     `i`.`brand_id`                 AS `brandId`,
                     `b`.`name`                     AS `brandName`,
                     `i`.`provider_id`              as `providerId`,
                     `p`.`partner_provider_id`      as `partnerProviderId`,
                     `p`.`name`                     as `providerName`,
                     `b2`.`user_id`                 AS `buyerUid`,
                     `ip`.`delivery`                AS `delivery`,
                     `ip`.`rate`                    AS `taxRate`,
                     `ip`.`purchase_rate`           AS `purchaseRate`,
                     `ip`.`tax_rate_code`           AS `taxRateCode`,
                     `i`.`status`                   AS `itemStatus`,
                     `i`.`estimate_sale_time`       AS `launchTime`,
                     i.business_line                AS `businessLine`
              FROM `spu`
                       JOIN `item` `i` ON `i`.`id` = `spu`.`item_id` AND `i`.`is_del` = 0
                       LEFT JOIN `category` `c` ON `i`.`category_id` = `c`.`id` AND `c`.`is_del` = 0
                       LEFT JOIN `brand` `b` ON `i`.`brand_id` = `b`.`id` AND `b`.`is_del` = 0
                       LEFT JOIN `item_procurement` `ip`
                                 ON `i`.`id` = `ip`.`item_id` AND `ip`.`is_del` = 0
                       LEFT JOIN `buyer` `b2` ON `ip`.`buyer_id` = `b2`.`id` AND `b2`.`is_del` = 0
                       LEFT JOIN `provider` `p` ON `i`.`provider_id` = `p`.`id` AND `p`.`is_del` = 0
        <where>
            <include refid="filters_ItemReferenceQuery"/>
        </where>
        ORDER BY `spu`.`created_at` DESC
            LIMIT #{query.offset}, #{query.pageSize}) `t1`
            LEFT JOIN `sku` ON `sku`.`item_code` = `t1`.`itemCode` AND `sku`.`is_del` = 0
        GROUP BY `t1`.`itemCode`
        ;
    </select>

    <sql id="filters_ItemReferenceSkuQuery">
        <if test="query.skuCode != null and query.skuCode != ''">
            AND sku.`sku_code` = #{query.skuCode}
        </if>
    </sql>

    <select id="itemReferenceSkuQueryCount" resultType="long">
        SELECT count(sku.id)
        FROM `spu`
                 JOIN `item` `i` ON `i`.`id` = `spu`.`item_id` AND `i`.`is_del` = 0
                 LEFT JOIN `sku` ON `sku`.`item_code` = `spu`.`item_code` AND `sku`.`is_del` = 0
                 LEFT JOIN `item_procurement` `ip`
                           ON `i`.`id` = `ip`.`item_id` AND `ip`.`is_del` = 0
                 LEFT JOIN `buyer` `b2` ON `ip`.`buyer_id` = `b2`.`id` AND `b2`.`is_del` = 0
        <where>
            <include refid="filters_ItemReferenceQuery"/>
            <include refid="filters_ItemReferenceSkuQuery"/>
        </where>
    </select>

    <select id="itemReferenceSkuQuery"
            resultType="com.daddylab.supplier.item.application.itemReference.types.ItemReferenceSku">
        SELECT `sku`.`sku_code`               AS `skuCode`,
               `i`.`id`                     AS `itemId`,
               `i`.`name`                     AS `itemName`,
               `spu`.`item_code`              AS `itemCode`,
               `i`.`partner_provider_item_sn` AS `partnerProviderItemSn`,
               `i`.`category_id`              AS `categoryId`,
               `c`.`path`                     AS `categoryPath`,
               `i`.`brand_id`                 AS `brandId`,
               `b`.`name`                     AS `brandName`,
               ANY_VALUE(`p`.`id`)            as `providerId`,
               ANY_VALUE(`p`.`name`)          as `provider`,
               ANY_VALUE(`p`.`partner_provider_id`)     as `partnerProviderId`,
               `b2`.`user_id`                 AS `buyerUid`,
               `ip`.`delivery`                AS `delivery`,
               `ip`.`rate`                    AS `taxRate`,
               `is2`.`cost_price`             AS `costPrice`,
               `is2`.`sale_price`             AS `salePrice`,
               `i`.`status`                   AS `itemStatus`,
               `ng`.`line_price`              AS `linePrice`,
               `ng`.`active_price`            AS `activePrice`,
               `i`.`estimate_sale_time`       AS `launchTime`,
                i.business_line               AS `businessLine`,
                `is2`.platform_commission     AS `platformCommission`,
               `is2`.`contract_sale_price`        AS `contractSalePrice`
        FROM `spu`
                 JOIN `item` `i` ON `i`.`id` = `spu`.`item_id` AND `i`.`is_del` = 0
                 LEFT JOIN `sku` ON `sku`.`item_code` = `spu`.`item_code` AND `sku`.`is_del` = 0
                 LEFT JOIN `item_sku` `is2`
                           ON `is2`.`sku_code` = `sku`.`sku_code` AND `is2`.`is_del` = 0
                 LEFT JOIN `category` `c` ON `i`.`category_id` = `c`.`id` AND `c`.`is_del` = 0
                 LEFT JOIN `brand` `b` ON `i`.`brand_id` = `b`.`id` AND `b`.`is_del` = 0
                 LEFT JOIN `item_procurement` `ip`
                           ON `i`.`id` = `ip`.`item_id` AND `ip`.`is_del` = 0
                 LEFT JOIN `buyer` `b2` ON `ip`.`buyer_id` = `b2`.`id` AND `b2`.`is_del` = 0
                 LEFT JOIN `new_goods` `ng`
                           ON `ng`.`sku_code` = `sku`.`sku_code` AND `ng`.`is_del` = 0
                 LEFT JOIN `provider` `p` ON `i`.`provider_id` = `p`.`id` AND `p`.`is_del` = 0
        <where>
            <include refid="filters_ItemReferenceQuery"/>
            <include refid="filters_ItemReferenceSkuQuery"/>
        </where>
        LIMIT #{query.offset},#{query.pageSize}
        ;
    </select>

    <insert id="saveSpuAndIgnoreIfExist">
        INSERT IGNORE `spu` (`item_code`, `item_id`, `created_at`, `created_uid`, `updated_at`,
                             `updated_uid`, `is_del`, `deleted_at`) VALUE
        <foreach item="item" index="index" collection="itemCodes" open="" separator="," close="">
            (#{item}, 0, unix_timestamp(), #{uid}, 0, 0, 0, 0)
        </foreach>
    </insert>

    <update id="updateItemSkuRef">
        INSERT INTO `sku` (`item_code`, `sku_code`, `created_at`, `created_uid`)
        VALUES
        <foreach item="item" index="index" collection="refs" open="" separator="," close="">
            (#{item.itemCode}, #{item.skuCode}, unix_timestamp(), #{uid})
        </foreach>
        ON DUPLICATE KEY UPDATE `item_code`   = VALUES(`item_code`),
                                `updated_at`  = unix_timestamp(),
                                `updated_uid` = #{uid};
    </update>

    <update id="updateSpuState">
        UPDATE `spu` LEFT JOIN (SELECT `item_code`,
                                       count(*)                  as cnt,
                                       min(`item_sku`.`item_id`) as itemId
                                FROM `sku`
                                         JOIN `item_sku` ON `item_sku`.`sku_code` = sku.`sku_code` WHERE
            sku.`item_code` IN
        <foreach item="item" index="index" collection="itemCodes" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND sku.`is_del` = 0 AND `item_sku`.`is_del` = 0
        GROUP BY `item_code`) `skuStats` ON `skuStats`.`item_code` = `spu`.`item_code`
        SET `spu`.`item_id`     = IF(spu.`item_id` = 0, IFNULL(skuStats.`itemId`, 0),
                                     spu.`item_id`),
            `spu`.`updated_at`  = UNIX_TIMESTAMP(),
            `spu`.`updated_uid` = #{uid},
            `spu`.`is_del`      = IF(`skuStats`.`cnt` > 0, 0, 1)
        WHERE spu.`item_code` IN
        <foreach item="item" index="index" collection="itemCodes" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <insert id="saveNewItem">
        INSERT IGNORE `spu` (`item_code`, `item_id`, `created_at`)
        SELECT IF(`provider_specified_code` != '', `provider_specified_code`, `code`),
               `id`,
               `created_at`
        FROM `item`
        WHERE `is_del` = 0
          AND `code` IN
        <foreach item="item" index="index" collection="itemCodes" open="(" separator="," close=")">
            #{item}
        </foreach>
        ;
    </insert>

    <insert id="saveNewItemSku">
        INSERT IGNORE `sku` (`item_code`, `sku_code`, `created_at`, `created_uid`, `updated_at`,
        `updated_uid`, `is_del`, `deleted_at`)
        SELECT
        <choose>
            <when test="adjustItemCode != null and adjustItemCode != ''">
                #{adjustItemCode}
            </when>
            <otherwise>
                <!--@ignoreSql-->
                item.`code`
            </otherwise>
        </choose>
        ,
        IF(`item_sku`.`provider_specified_code` != '', `item_sku`.`provider_specified_code`,
        `item_sku`.`sku_code`),
        UNIX_TIMESTAMP(),
        #{uid},
        0,
        0,
        0,
        0
        FROM `item_sku`
        JOIN `item` ON `item_sku`.`item_id` = `item`.`id` WHERE `code` IN
        <foreach item="item" index="index" collection="itemCodes" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND `item`.`is_del` = 0
        AND `item_sku`.`is_del` = 0
    </insert>

    <delete id="removeInvalidItemSku">
        UPDATE `sku`
        SET `is_del`     = 1,
            `deleted_at` = unix_timestamp()
        WHERE `sku_code` IN (SELECT `sku_code`
                             FROM item,
                                  `item_sku` WHERE `item`.id = `item_sku`.`item_id`
                                               AND `item`.`code` IN
        <foreach item="item" index="index" collection="itemCodes" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND (`item_sku`.`is_del` = 1 OR `item_sku`.`provider_specified_code` != '' AND
                                        `item_sku`.`provider_specified_code` !=
                                        `item_sku`.`sku_code`))
          AND `is_del` = 0
    </delete>

    <select id="refBatchQueryBySkuCodes"
            resultType="com.daddylab.supplier.item.application.itemReference.types.SpuSkuRef">
        SELECT `item_code` AS `itemCode`, `sku_code` AS `skuCode`
        FROM `sku` WHERE `is_del` = 0
                     AND `sku_code` IN
        <foreach item="item" index="index" collection="skuCodes" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getAdjustItemCode" resultType="java.lang.String">
        SELECT `sku`.`item_code`
        FROM `item`
        JOIN `item_sku` ON `item`.`id` = `item_sku`.`item_id`
        JOIN `sku` ON `sku`.`sku_code` = `item_sku`.`sku_code` OR `sku`.`sku_code` =
        `item_sku`.`provider_specified_code`
        WHERE `item`.`code` = `item_sku`.`sku_code` AND `item`.`code` = #{itemCode}
        AND `item`.`is_del` = 0
        AND `item_sku`.`is_del` = 0
        AND `sku`.`is_del` = 0
        LIMIT 1
    </select>
</mapper>
