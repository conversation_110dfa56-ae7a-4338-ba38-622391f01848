<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerModuleAuditTaskMapper">



    <select id="selectByItemIdAndRound"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAuditTask">
        select * from item_drawer_module_audit_task where item_id = #{itemId} and round = #{round} and type =
        #{type.value} order by id
    </select>

    <update id="undelete">
        update item_drawer_module_audit_task set is_del = 0 where id in
        <foreach item="item" index="index" collection="collection" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>
