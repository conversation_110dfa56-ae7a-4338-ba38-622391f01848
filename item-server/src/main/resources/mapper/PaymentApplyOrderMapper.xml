<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PaymentApplyOrderMapper">
    <select id="getMaxNo" resultType="java.lang.String">
        select no
        from payment_apply_order
        where no like CONCAT(#{prefix}, '%')
        order by id desc
        limit 1;
    </select>
</mapper>
