<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemOptimizeMapper">


    <select id="selectDataField" resultType="com.daddylab.supplier.item.types.itemOptimize.ItemOptimizePersistData">
        select data -> '$.#{fieldName}' as #{fieldName} from item_optimize where id = #{id}
    </select>

    <update id="updateSubmitMetadata">
        UPDATE `item_optimize`
        <set>
            `submit_at` = IF(`submit_at` != 0, `submit_at`, #{time}),
            `submit_uid` = IF(`submit_uid` != 0, `submit_uid`, #{userId}),
            `last_submit_at` = #{time},
            `last_submit_uid` = #{userId},
            data = json_merge_patch(data, '{"disableRollback": false}'),
            <if test="isReview != null and isReview">
                review_num = review_num + 1,
            </if>
        </set>
        WHERE id = #{id};
    </update>
</mapper>
