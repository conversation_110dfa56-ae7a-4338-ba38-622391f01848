<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.HandingSheetItemMapper">

    <select id="countItemNumByHandingSheetId" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT item_id)
        FROM handing_sheet_item
        WHERE handing_sheet_id = #{handingSheetId} AND is_del = 0
    </select>

    <sql id = "Base_Query">
        FROM handing_sheet_item
        <where>
            ${ew.sqlSegment}
        </where>
    </sql>

    <sql id = "Base_Audit_Query">
        FROM handing_sheet_item AS hsi
        INNER JOIN item_procurement AS ip ON (hsi.item_id = ip.item_id)
        INNER JOIN buyer AS b ON (ip.buyer_id = b.id)
        <where>
            ${ew.sqlSegment}
        </where>
    </sql>

    <select id="selectPageListCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        <include refid="Base_Query"></include>
    </select>

    <select id="selectPageList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetItem">
        SELECT *
        <include refid="Base_Query"></include>
        ORDER BY id DESC
    </select>

    <select id="selectListByHandingSheetIdAndBuyerUid"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetItem">
        SELECT t1.*
        FROM handing_sheet_item AS t1
        INNER JOIN item_procurement AS t2 ON (t1.item_id = t2.item_id)
        INNER JOIN buyer AS t3 ON (t2.buyer_id = t3.id)
        WHERE t3.user_id = #{buyerUid}
        AND t1.handing_sheet_id = #{handingSheetId}
        AND t1.is_del = 0 AND t2.is_del = 0 AND t3.is_del = 0
    </select>

    <select id="listPassedColumnByHandingSheetId" resultType="java.lang.Boolean">
        SELECT is_passed
        FROM handing_sheet_item
        WHERE handing_sheet_id = #{handingSheetId} AND is_del = 0
    </select>

    <select id="selectPageListCountForAudit" resultType="java.lang.Long">
        SELECT COUNT(hsi.id)
        <include refid="Base_Audit_Query" />
    </select>

    <select id="selectPageListForAudit"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetItem">
        SELECT hsi.*
        <include refid="Base_Audit_Query" />
    </select>
</mapper>
