<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemSkuAttrRefMapper">


    <select id="getSkuAttrList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuAttrRefDO">
        select
        <!--        skuId-->
        item_sku_id as skuId,
        <!--        商品属性 item_id-->
        ia.attr_id as attrId,
        <!--        属性名-->
        ca.name as attrName,
        <!--        属性值-->
        ia.attr_value as attrValue,
        ia.id as itemAttrDbId
        from
        ( select * from item_sku_attr_ref where is_del = 0 )kar
        left join (select * from item_attr where is_del = 0 ) ia on kar.item_attr_id = ia.id
        left join (select * from category_attr where is_del = 0 ) ca on ia.attr_id = ca.id
        where
        kar.item_sku_id in
        <foreach collection="list" item="skuIdList" index="index"
                 open="(" close=")" separator=",">
            #{skuIdList}
        </foreach>
    </select>
    <select id="getSkuAttrListByCode"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuAttrRefDO">
        select
        <!--        skuId-->
        item_sku_id as skuId,
        <!--        商品属性 item_id-->
        ia.attr_id as attrId,
        <!--        属性名-->
        ca.name as attrName,
        <!--        属性值-->
        ia.attr_value as attrValue,
        ia.id as itemAttrDbId
        from
        ( select * from item_sku_attr_ref where is_del = 0 )kar
        left join (select * from item_attr where is_del = 0 ) ia on kar.item_attr_id = ia.id
        left join (select * from category_attr where is_del = 0 ) ca on ia.attr_id = ca.id
        where
        kar.item_sku_id in
        (select id from item_sku where is_del = 0 and sku_code = #{skuCode})
    </select>

    <select id="getSkuAttrListByItemId"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuAttrRefDO">
        select
            item_sku_id as skuId,
            ia.attr_id as attrId,
            ca.name as attrName,
            ia.attr_value as attrValue,
            ia.id as itemAttrDbId
        from
                ( select * from item_sku_attr_ref where is_del = 0 )kar
                    left join (select * from item_attr where is_del = 0 ) ia on kar.item_attr_id = ia.id
                    left join (select * from category_attr where is_del = 0 ) ca on ia.attr_id = ca.id
        where
            kar.item_id = #{itemId}

    </select>
</mapper>
