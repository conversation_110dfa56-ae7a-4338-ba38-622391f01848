<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemTrainingMaterialsMapper">

    <select id="staticsToBeQcAudit"
            resultType="com.daddylab.supplier.item.application.message.wechat.v2.statics.QwMsgQcStaticsBo">
        select ip.qc_ids as userIds, group_concat(ii.item_id) as itemIds
        from item_training_materials ii
                 left join item_procurement ip on ii.item_id = ip.item_id
        where ii.status = 3
          and ii.is_del = 0
          and ip.is_del = 0
        group by ip.qc_ids;
    </select>
</mapper>
