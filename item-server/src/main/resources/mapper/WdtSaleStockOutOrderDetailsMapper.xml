<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtSaleStockOutOrderDetailsMapper">

    <select id="getStockOutId" resultType="java.lang.Long">
        select stockout_id from wdt_sale_stock_out_order_details
        <where>
            <if test="specCode != null and specCode != ''">
                and wssood.spec_no = #{specCode}
            </if>
            <if test="goodsName != null and goodsName != ''">
                and wssood.goods_name = #{goodsName}
            </if>
            <if test="goodsNo != null and goodsNo != ''">
                and wssood.goods_no = #{goodsNo}
            </if>
        </where>
    </select>
</mapper>
