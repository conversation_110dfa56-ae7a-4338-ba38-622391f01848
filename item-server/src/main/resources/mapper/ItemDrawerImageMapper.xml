<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerImageMapper">

    <select id="selectSortedListByDrawerId"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerImage">
        SELECT * FROM item_drawer_image WHERE drawer_id = #{drawerId} AND is_del = 0 ORDER BY sort
    </select>
</mapper>
