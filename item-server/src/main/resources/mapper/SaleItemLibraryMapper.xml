<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.SaleItemLibraryMapper">

    <select id="count" resultType="java.lang.Integer">
        SELECT count(1)
        <include refid="saleItem"/>
    </select>

    <select id="queryPage" resultType="com.daddylab.supplier.item.application.saleItem.vo.SaleItemLibraryVO">
        select
            sil.*,
            ng.shipment_aging,
            ng.daily_price,
            ng.active_content,
            ng.daily_price,
            ng.active_content,
            b.name
        <include refid="saleItem"/>
        order by sil.created_at desc
        limit #{queryPage.offset},#{queryPage.pageSize}
    </select>



    <sql id="saleItem">
        from `sale_item_library` sil  LEFT JOIN `new_goods` `ng` ON `sil`.`item_sku_code` = `ng`.`sku_code`
        left join item_procurement ip on ip.item_id = ng.item_id
        left join buyer b ON ip.buyer_id = b.id
        WHERE `sil`.`is_del` = 0
        <if test ="queryPage.itemSkuCode != null and queryPage.itemSkuCode != '' "  >
            and sil.item_sku_code = #{queryPage.itemSkuCode}
        </if>
        <if test = "queryPage.goodsName != null  and queryPage.goodsName != ''">
            and sil.goods_name like concat('%', #{queryPage.goodsName}, '%')
        </if>
        <if test = "queryPage.buyerName != null  and queryPage.buyerName != ''">
            and b.name = #{queryPage.buyerName}
        </if>
        <if test = "queryPage.itemId != null  and queryPage.itemId != ''">
            and (sil.member_store_item_id = #{queryPage.itemId} or sil.applets_item_id = #{queryPage.itemId}
            or sil.beauty_store_item_id = #{queryPage.itemId} or sil.maternity_baby_item_id =#{queryPage.itemId}
            or sil.woto_buy_item_id = #{queryPage.itemId} or sil.tik_tok_item_id = #{queryPage.itemId})
        </if>
    </sql>
</mapper>
