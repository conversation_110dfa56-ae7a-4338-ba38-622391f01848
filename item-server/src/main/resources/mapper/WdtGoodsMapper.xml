<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtGoodsMapper">
    <insert id="saveOrUpdateBatch">
        INSERT INTO wdt_goods (id, goods_id, goods_no, goods_name, short_name, alias, goods_type,
                               spec_count, brand_name, brand_id, remark, prop1, prop2, prop3, prop4,
                               prop5, prop6, origin, class_name, unit_name, aux_unit_name,
                               flag_name, deleted, goods_modified, goods_created, created_at,
                               created_uid, updated_at, updated_uid, is_del)
        VALUES
        <foreach collection="goods" item="item" open="" separator="," close="">
            (null, #{item.goodsId}, #{item.goodsNo}, #{item.goodsName}, #{item.shortName},
             #{item.alias}, #{item.goodsType},
             #{item.specCount}, #{item.brandName}, #{item.brandId}, #{item.remark}, #{item.prop1},
             #{item.prop2}, #{item.prop3}, #{item.prop4},
             #{item.prop5}, #{item.prop6}, #{item.origin}, #{item.className}, #{item.unitName},
             #{item.auxUnitName},
             #{item.flagName}, #{item.deleted}, #{item.goodsModified}, #{item.goodsCreated},
             #{item.createdAt}, #{item.createdUid}, #{item.updatedAt}, #{item.updatedUid}, #{item.isDel})
        </foreach>
        ON DUPLICATE KEY UPDATE goods_no       = VALUES(goods_no),
                                goods_name     = VALUES(goods_name),
                                short_name     = VALUES(short_name),
                                alias          = VALUES(alias),
                                goods_type     = VALUES(goods_type),
                                spec_count     = VALUES(spec_count),
                                brand_name     = VALUES(brand_name),
                                brand_id       = VALUES(brand_id),
                                remark         = VALUES(remark),
                                prop1          = VALUES(prop1),
                                prop2          = VALUES(prop2),
                                prop3          = VALUES(prop3),
                                prop4          = VALUES(prop4),
                                prop5          = VALUES(prop5),
                                prop6          = VALUES(prop6),
                                origin         = VALUES(origin),
                                class_name     = VALUES(class_name),
                                unit_name      = VALUES(unit_name),
                                aux_unit_name  = VALUES(aux_unit_name),
                                flag_name      = VALUES(flag_name),
                                deleted        = VALUES(deleted),
                                goods_modified = VALUES(goods_modified),
                                goods_created  = VALUES(goods_created),
                                updated_uid    = VALUES(updated_uid),
                                updated_at     = VALUES(updated_at),
                                is_del         = VALUES(is_del)
    </insert>
</mapper>
