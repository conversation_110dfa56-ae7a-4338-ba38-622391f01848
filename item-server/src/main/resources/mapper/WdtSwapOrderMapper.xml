<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtSwapOrderMapper">
    <insert id="saveOrUpdateBatch">
        INSERT INTO wdt_swap_order (id, refund_no, tid, shop_no, shop_name, warehouse_no)
        VALUES
        <foreach collection="orders" item="item" separator=",">
            (NULL, #{item.refundNo}, #{item.tid}, #{item.shopNo}, #{item.shopName},
             #{item.warehouseNo})
        </foreach>
        ON DUPLICATE KEY UPDATE `tid`          = VALUES(`tid`),
                                `shop_no`      = VALUES(`shop_no`),
                                `shop_name`    = VALUES(`shop_name`),
                                `warehouse_no` = VALUES(`warehouse_no`)
    </insert>
</mapper>
