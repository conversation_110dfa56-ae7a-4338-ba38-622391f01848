<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OffShelfItemMapper">
    <select id="selectOffShelfItemVOList"
            resultType="com.daddylab.supplier.item.application.offShelf.dto.OffShelfItemVO">
        SELECT `osi`.`id`,
               `osi`.`item_id`,
               `osi`.`item_code`,
               `i`.`name`               AS `itemName`,
               `i`.`status`,
               `i`.`estimate_sale_time` AS `shelfTime`,
               `i`.`partner_provider_item_sn`,
               `i`.`business_lines`     AS `businessLine`,
               `osi`.`remark`
        FROM `off_shelf_item` `osi`
                 JOIN `item` `i` ON `osi`.`item_id` = `i`.`id`
        WHERE `osi`.`off_shelf_info_id` = #{id}
          AND `osi`.`is_del` = 0
    </select>
</mapper>
