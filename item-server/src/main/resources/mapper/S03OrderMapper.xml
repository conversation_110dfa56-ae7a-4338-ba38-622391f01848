<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.S03OrderMapper">

<select id="selectBuyerName" resultType="java.lang.String">
    SELECT receiver_name
    FROM s03_order
    WHERE receiver_name IS NOT NULL and receiver_name != ''
    <if test="name != '' and name != null ">
        AND receiver_name like  concat('%',#{name},'%')
    </if>
    group by receiver_name
        limit 0,#{size};
    </select>
</mapper>
