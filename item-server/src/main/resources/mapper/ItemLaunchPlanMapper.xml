<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemLaunchPlanMapper">

    <sql id = "Base_Query_ItemLaunchPlan_List">
        FROM item_launch_plan
        <where>
            ${ew.sqlSegment}
        </where>
    </sql>

    <select id="selectPageList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchPlan">
        SELECT *
        <include refid="Base_Query_ItemLaunchPlan_List" />
        ORDER BY id DESC
    </select>

    <select id="selectPageListCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        <include refid="Base_Query_ItemLaunchPlan_List" />
    </select>

    <select id="nameDropDownList"
            resultType="com.daddylab.supplier.item.controller.item.dto.PlanNameDropDownVo">
        select
        `plan_name` as `name`,
        `id` as `id`
        from item_launch_plan
        <where>
            is_del = 0
            <if test="name != null and name != ''">
                and `plan_name` like concat('%', #{name}, '%')
            </if>
        </where>
        limit #{offset}, #{pageSie}
    </select>

    <select id="launchTimeDownList"
            resultType="com.daddylab.supplier.item.controller.item.dto.LaunchTimeDropDownVo">
        select
            distinct FROM_UNIXTIME(launch_time, '%Y-%m-%d') as launchDate
        from item_launch_plan
        where is_del = 0
        order by launchDate desc
    </select>

    <select id="launchPlanDownList"
            resultType="com.daddylab.supplier.item.controller.item.dto.LaunchTimeDropDownVo">
        select
            FROM_UNIXTIME(launch_time, '%Y-%m-%d') as launchDate,
            launch_time as launchTime,
            plan_name as planName,
            id as planId,
            business_line as businessLine
        from item_launch_plan
        where is_del = 0 order by id desc
        limit #{offset}, #{pageSie}
    </select>

    <select id="getPlanByItemId"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchPlan">
        select
            t2.*
        from item_launch_plan_item_ref as t1
        inner join item_launch_plan as t2 on (t1.plan_id = t2.id and t2.is_del = 0)
        where t1.is_del = 0 and t1.item_id = #{itemId}
    </select>

    <update id="updateBatchPlanRefCount">
        UPDATE `item_launch_plan` `ilp`
        SET `ilp`.`item_num` = (SELECT COUNT(`ilpir`.`id`)
                                FROM `item_launch_plan_item_ref` `ilpir`
                                WHERE `ilp`.`id` = `ilpir`.`plan_id`
                                  AND `ilpir`.`is_del` = 0)
        WHERE `ilp`.`id` IN
        <foreach item="item" index="index" collection="planIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="queryItemIdByLaunchTime" resultType="java.lang.Long">
        select item_id from item_launch_plan_item_ref ref
                                inner join item_launch_plan plan on ref.plan_id = plan.id
            where ref.is_del = 0 and plan.is_del = 0
        order by launch_time desc limit #{offset},#{size}
    </select>
</mapper>
