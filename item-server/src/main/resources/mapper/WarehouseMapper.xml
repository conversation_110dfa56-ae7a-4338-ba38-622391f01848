<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WarehouseMapper">
    <select id="queryWarehouseMapByNo"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.WarehouseDO">
        select
        no,name
        from
        warehouse
        where is_del = 0
        <if test=" list != null and list.size() > 0 ">
            and no in
            <foreach collection="list" item="no" open="(" separator="," close=")">
                #{no}
            </foreach>
        </if>
    </select>

    <select id="queryBySkuCodeAndIsGift"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse">
        select *
        from warehouse
        where no =
              (select warehouse_no
               from purchase_order_detail
               where is_del = 0
                 and item_sku_code = #{skuCode}
                 and tax_price = #{taxPrice}
                 and is_gift = #{isGift})
          and state = 0
          and is_del = 0
    </select>


    <update id="updateWarehouseRatio">
        update warehouse
        set version         = #{version},
            inventory_ratio = #{newRatio}
        where no = #{warehouseNo}
    </update>
</mapper>
