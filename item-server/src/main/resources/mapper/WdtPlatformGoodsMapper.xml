<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtPlatformGoodsMapper">
    <insert id="saveOrUpdateBatch">
        INSERT INTO wdt_platform_goods (id, shop_id, shop_no, shop_name, goods_name, spec_name,
                                        spec_outer_id, outer_id, goods_id, spec_id, rec_id,
                                        is_deleted, price, stock_num, status, is_auto_match,
                                        match_target_type, hold_stock, hold_stock_type,
                                        is_auto_listing, is_auto_delisting, modified, merchant_no,
                                        merchant_name, merchant_code, created_at, created_uid,
                                        updated_at, updated_uid)
        VALUES
        <foreach collection="goods" item="item" separator=",">
            (#{item.id}, #{item.shopId}, #{item.shopNo}, #{item.shopName}, #{item.goodsName},
             #{item.specName},
             #{item.specOuterId}, #{item.outerId}, #{item.goodsId}, #{item.specId}, #{item.recId},
             #{item.isDeleted}, #{item.price}, #{item.stockNum}, #{item.status},
             #{item.isAutoMatch},
             #{item.matchTargetType}, #{item.holdStock}, #{item.holdStockType},
             #{item.isAutoListing}, #{item.isAutoDelisting}, #{item.modified}, #{item.merchantNo},
             #{item.merchantName}, #{item.merchantCode}, #{item.createdAt}, #{item.createdUid},
             #{item.updatedAt}, #{item.updatedUid})
        </foreach>
        ON DUPLICATE KEY UPDATE `shop_id`           = VALUES(`shop_id`),
                                `shop_no`           = VALUES(`shop_no`),
                                `shop_name`         = VALUES(`shop_name`),
                                `goods_name`        = VALUES(`goods_name`),
                                `spec_name`         = VALUES(`spec_name`),
                                `spec_outer_id`     = VALUES(`spec_outer_id`),
                                `outer_id`          = VALUES(`outer_id`),
                                `goods_id`          = VALUES(`goods_id`),
                                `spec_id`           = VALUES(`spec_id`),
                                `rec_id`            = VALUES(`rec_id`),
                                `is_deleted`        = VALUES(`is_deleted`),
                                `price`             = VALUES(`price`),
                                `stock_num`         = VALUES(`stock_num`),
                                `status`            = VALUES(`status`),
                                `is_auto_match`     = VALUES(`is_auto_match`),
                                `match_target_type` = VALUES(`match_target_type`),
                                `hold_stock`        = VALUES(`hold_stock`),
                                `hold_stock_type`   = VALUES(`hold_stock_type`),
                                `is_auto_listing`   = VALUES(`is_auto_listing`),
                                `is_auto_delisting` = VALUES(`is_auto_delisting`),
                                `modified`          = VALUES(`modified`),
                                `merchant_no`       = VALUES(`merchant_no`),
                                `merchant_name`     = VALUES(`merchant_name`),
                                `merchant_code`     = VALUES(`merchant_code`),
                                `updated_at`        = unix_timestamp(),
                                `updated_uid`       = VALUES(`updated_uid`)
    </insert>
</mapper>
