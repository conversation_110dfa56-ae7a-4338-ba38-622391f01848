<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OperateLogMapper">


    <insert id="copyOperateLogs">
        insert into operate_log ( target_type, target_id, msg, data, operator_id, deleted_at, created_at,
        created_uid, updated_at, updated_uid, is_del)
        select target_type, #{targetId}, msg, data, operator_id, deleted_at, created_at, created_uid,
        updated_at, updated_uid, is_del
        from operate_log where target_type = #{type.value} and target_id = #{sourceId} and is_del = 0
        order by id
    </insert>
</mapper>
