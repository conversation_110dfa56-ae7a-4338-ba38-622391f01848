<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
  namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockInOrderMapper">
  <select id="selectByNo" resultType="long">
    SELECT sio.id
    FROM supplier.stock_in_order sio
    WHERE sio.no = #{no} and sio.is_del = 0
  </select>

  <select id="selectStockInOrderAndDetailByNo"
    resultType="com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndDetailQuery">
    SELECT siod.item_sku_code         AS 'itemSkuCode',
           siod.item_name             AS 'itemName',
           siod.specifications        AS 'specifications',
           sio.purchase_quantity      AS 'purchaseQuantity',
           sio.total_receipt_quantity AS 'totalReceiptQuantity',
           siod.tax_quota             AS 'taxQuota',
           siod.after_tax_amount      AS 'afterTaxAmount'
    FROM supplier.stock_in_order sio
    LEFT JOIN supplier.stock_in_order_detail siod
    ON sio.id = siod.stock_in_order_id
    WHERE sio.no = #{no}
    AND sio.is_del = 0
  </select>

  <sql id="whereIsQueryStockInOrder">
    is_del = 0
    <if test="no != null">AND sio.no = #{no}</if>
    <if test="providerId != null">AND sio.provider_id = #{providerId}</if>
    <if test="organizationId != null">AND sio.organization_id = #{organizationId}</if>
    <if test="purchaseOrderNo != null">AND sio.purchase_order_no = #{purchaseOrderNo}</if>
    <if test="state != null">AND sio.state = #{state}</if>
    <if test="receiptTimeStart != null and receiptTimeEnd != null">AND sio.receipt_time between #{receiptTimeStart} and #{receiptTimeEnd} </if>
    <if test="buyerUserId != null">AND sio.buyer_user_id = #{buyerUserId}</if>
    <if test="purchaseOrganizationId != null">AND sio.purchase_group_id = #{purchaseOrganizationId} </if>
<!--    <if test="createdUid != null">AND created_uid = #{createdUid}</if>-->
    <if test="businessLine != null and businessLine.size() > 0">
      AND sio.business_line in
      <foreach collection="businessLine" item="bl" open="(" separator="," close=")">
        #{bl}
      </foreach>
    </if>
    <!--&lt;!&ndash;    非管理员和财务人员，无法看到对冲单据&ndash;&gt;
    <if test="seeHedge != null and !seeHedge">AND sio.hedge_status != 10</if>-->
    <if test="createdUidList != null and createdUidList.size() > 0">
      AND sio.created_uid in
      <foreach collection="createdUidList" item="createdUid" open="(" separator="," close=")">
        #{createdUid}
      </foreach>
    </if>
    <if test="neCreatedUidList != null and neCreatedUidList.size() > 0">
      AND sio.created_uid not in
      <foreach collection="neCreatedUidList" item="neCreatedUid" open="(" separator="," close=")">
        #{neCreatedUid}
      </foreach>
    </if>
  </sql>

  <sql id="whereIsQueryStockInOrderDetail">
    is_del = 0
    <if test="itemSkuCode != null">AND siod.item_sku_code = #{itemSkuCode}</if>
    <if test="itemName != null">AND siod.item_name = #{itemName}</if>
<!--    <if test="createdUid != null">AND created_uid = #{createdUid}</if>-->
    <if test="warehouseNo != null">AND siod.warehouse_no = #{warehouseNo}</if>
    <if test="createdUidList != null and createdUidList.size() > 0">
      AND siod.created_uid in
      <foreach collection="createdUidList" item="createdUid" open="(" separator="," close=")">
        #{createdUid}
      </foreach>
    </if>
    <if test="neCreatedUidList != null and neCreatedUidList.size() > 0">
      AND siod.created_uid not in
      <foreach collection="neCreatedUidList" item="neCreatedUid" open="(" separator="," close=")">
        #{neCreatedUid}
      </foreach>
    </if>

  </sql>

  <select id="queryCount" resultType="int">
    SELECT count(sio.id) FROM supplier.stock_in_order sio
    <where>
      <include refid="whereIsQueryStockInOrder"/>
    </where>
  </select>

  <select id="queryStockInOrderList"
    resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder">
    SELECT distinct sios.*
    FROM
      (
        SELECT * FROM supplier.stock_in_order sio <where><include refid="whereIsQueryStockInOrder"/></where>
      ) sios
    RIGHT JOIN
      (
        SELECT * FROM supplier.stock_in_order_detail siod <where><include refid="whereIsQueryStockInOrderDetail"/></where>
      ) siods
      ON sios.id = siods.stock_in_order_id
    ORDER BY sios.created_at desc
  </select>

  <select id="queryStockInOrderList2"
          resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.StockInOrderQueryDto">
    select
        sio2.id as stockInOrderId,
        GROUP_CONCAT(siod2.id) as stockInOrderDetailIds
    from (select id from stock_in_order sio <where><include refid="whereIsQueryStockInOrder"/></where> ) as sio2
    inner join (select id,stock_in_order_id from stock_in_order_detail siod  <where><include refid="whereIsQueryStockInOrderDetail"/></where>) as siod2
    on sio2.id = siod2.stock_in_order_id
    group by sio2.id desc
    limit #{offsetVal},#{pageSize}
  </select>

  <select id="countStockInOrderList2" resultType="java.lang.Integer">
    select count(1)
    from (
           select distinct sio2.id
           from (select id from stock_in_order sio <where><include refid="whereIsQueryStockInOrder"/></where> ) as sio2
                  inner join (select id,stock_in_order_id from stock_in_order_detail siod <where><include refid="whereIsQueryStockInOrderDetail"/></where> ) as siod2
                on sio2.id = siod2.stock_in_order_id
         ) tmp;
  </select>

  <update id="updateByStockInOrderId" parameterType="long">
    UPDATE supplier.stock_in_order sio
    SET sio.is_del = 1
    WHERE sio.id = #{id}
  </update>

  <update id="updateWdtPurchaseOrderNo">
    UPDATE supplier.stock_in_order sio
    SET wdt_storage_no = #{wdtPurchaseOrderNo}
    WHERE sio.id = #{id} and sio.is_del = 0
  </update>

  <sql id="exportWhere">
    <where>
      sio.is_del = 0 AND siod.is_del = 0
      <if test="no != null">AND sio.no = #{no}</if>
      <if test="providerId != null">AND sio.provider_id LIKE concat('%', #{providerId}, '%')</if>
      <if test="organizationId != null">AND sio.organization_id= #{organizationId}</if>
      <if test="purchaseOrderNo != null">AND sio.purchase_order_no= #{purchaseOrderNo}</if>
      <if test="state != null">AND sio.state= #{state}</if>
      <if test="receiptTimeStart != null">AND sio.receipt_time between #{receiptTimeStart} and #{receiptTimeEnd} </if>
      <if test="itemSkuCode != null">AND siod.item_sku_code = #{itemSkuCode}</if>
      <if test="itemName != null">AND siod.item_name = #{itemName}</if>
      <if test="buyerUserId != null">AND sio.buyer_user_id LIKE concat('%', #{buyerUserId}, '%') </if>
      <if test="purchaseOrganizationId != null">AND purchase_organization_id = #{purchaseOrganizationId}</if>
      <if test="warehouseNo != null">AND siod.warehouse_no LIKE concat('%', #{warehouseNo}, '%')</if>
<!--      <if test="createdUid != null">AND sio.created_uid = #{createdUid}</if>-->
      <if test="businessLine != null and businessLine.size() > 0">
        AND sio.business_line in
        <foreach collection="businessLine" item="bl" open="(" separator="," close=")">
            #{bl}
        </foreach>
      </if>
      <if test="createdUidList != null and createdUidList.size() > 0">
        AND sio.created_uid in
        <foreach collection="createdUidList" item="createdUid" open="(" separator="," close=")">
          #{createdUid}
        </foreach>
      </if>
      <if test="neCreatedUidList != null and neCreatedUidList.size() > 0">
        AND sio.created_uid not in
        <foreach collection="neCreatedUidList" item="neCreatedUid" open="(" separator="," close=")">
          #{neCreatedUid}
        </foreach>
      </if>
    </where>
  </sql>
  <select id="queryExport" resultType="com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetailSheet">
    SELECT
    sio.`no` AS 'no',
    o.name AS 'purchaseOrganization',
    p.name AS 'provider',
    sio.purchase_department_id AS 'purchaseDepartment',
    eg.name AS 'purchaseGroup',
    sio.buyer_user_id AS 'buyerUser',
    sio.purchase_order_no AS 'purchaseOrderNo',
    siod.item_sku_code AS 'itemSkuCode',
    siod.item_name AS 'itemName',
    siod.specifications AS 'specifications',
    siod.unit AS 'unit',
    siod.receipt_quantity AS 'receiptQuantity',
    siod.real_receipt_quantity AS 'realReceiptQuantity',
    FROM_UNIXTIME(sio.receipt_time) AS 'receiptTime',
    w.name AS 'warehouseNo',
    IF(siod.is_gift = 1, '是', '否') as isGift,
    siod.tax_price AS 'taxPrice',
    siod.tax_rate AS 'taxRate',
    siod.total_price_tax AS 'totalPriceTax',
    siod.tax_amount AS 'taxQuota',
    siod.after_tax_price AS 'afterTaxPrice',
    siod.after_tax_amount AS 'afterTaxAmount',
    siod.purchase_quantity AS 'purchaseQuantity',
    sio.remark as 'remark',
    sio.business_line as 'businessLine'
    FROM supplier.stock_in_order sio
    LEFT JOIN supplier.stock_in_order_detail siod ON sio.id = siod.stock_in_order_id
    LEFT JOIN (SELECT * FROM supplier.provider WHERE is_del = 0) p ON p.id = sio.provider_id
    LEFT JOIN (SELECT * FROM supplier.erp_group WHERE is_del =0 ) eg ON eg.id = sio.purchase_group_id
    LEFT JOIN (SELECT * FROM supplier.organization WHERE is_del = 0) o ON o.id = sio.purchase_organization_id
    LEFT JOIN (SELECT * FROM supplier.warehouse WHERE is_del = 0 ) w ON w.no = siod.warehouse_no
    <include refid="exportWhere"/>
    order by sio.created_at desc
    limit #{offsetVal},#{pageSize}
  </select>

  <select id="selectExportCount" resultType="int">
    SELECT
    count(1)
    FROM supplier.stock_in_order sio
    left join supplier.stock_in_order_detail siod
    ON sio.id = siod.stock_in_order_id
    <include refid="exportWhere"/>
  </select>

  <select id="selectStockInOrderByNo"
    resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder">
    SELECT *
    FROM supplier.stock_in_order sio
    WHERE sio.`no` = #{no} and sio.is_del = 0
  </select>

  <select id="getListByPurchaseOrderId"
    resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrderDetail">
    select `detail`.*
    from supplier.stock_in_order_detail `detail`
    left join supplier.stock_in_order `order` on `order`.id = `detail`.stock_in_order_id
    where `order`.purchase_order_id = #{id}
      and `order`.is_del = 0
      and `detail`.is_del = 0
  </select>

  <select id="getApplyQuantity" resultType="java.lang.Integer">
    select
        IFNULL(sum(`detail`.receipt_quantity),0)
    from supplier.stock_in_order_detail `detail`
           left join supplier.stock_in_order `order` on `order`.id = `detail`.stock_in_order_id
    where
      `order`.purchase_order_id = #{id}
      and `order`.is_del = 0
      and `detail`.is_del = 0
      and `detail`.item_sku_code = #{skuCode}
      and `is_gift` = #{isGift}
      and `order`.state != 1 and `order`.state != 4
  </select>

  <select id="listFinishStockInOrder" resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder">
    select
        sio.id,
        sio.buyer_user_id,
        sio.created_uid
    from supplier.stock_in_order sio
    left join supplier.purchase_order po on sio.purchase_order_id = po.id
    where sio.state = 3
      and sio.receipt_time between #{startTime} and #{endTIme}
      and po.type = 2
      and sio.is_del = 0
      and po.is_del = 0
  </select>

  <select id="sumReceiptQuantity"
          resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.StockInOrderDO">
    SELECT
      purchase_order_id,
      SUM(`total_receipt_quantity`) as receiptQuantity
    FROM supplier.stock_in_order
      WHERE is_del = 0 AND state IN (21, 3)
    AND purchase_order_id in
    <foreach item="item" index="index" collection="list" open="(" close=")" separator=",">
      #{item}
    </foreach>
    group by purchase_order_id
  </select>

  <select id="sumHadAndWillReceiptQuantity"
          resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.StockInOrderDO">
    SELECT
      purchase_order_id,
      SUM(`total_receipt_quantity`) as receiptQuantity
    FROM supplier.stock_in_order
    WHERE is_del = 0 AND state in (2, 21, 3)
    AND purchase_order_id in
    <foreach item="item" index="index" collection="list" open="(" close=")" separator=",">
      #{item}
    </foreach>
    group by purchase_order_id
  </select>

  <select id="selectSyncKingDeeNo" resultType="java.lang.String">
    SELECT
      sio.NO
    FROM
      stock_in_order sio INNER JOIN purchase_order po ON sio.purchase_order_id = po.id
    WHERE
<!--      关联到的采购单类型为工厂采购即推送金蝶-->
      po.type = 2
<!--      入库单本身的状态为已入库-->
      and sio.state = 3
      and sio.updated_at between #{startDt} and #{endDt}
  </select>

  <select id="getApplyQuantities"
          resultType="com.daddylab.supplier.item.types.stockInOrder.StockInOrderDetailApplyQuantity">
    select
    `detail`.item_sku_code as itemSkuCode, is_gift as isGift, IFNULL(sum(`detail`.receipt_quantity),0) as quantity
    from supplier.stock_in_order_detail `detail`
    left join supplier.stock_in_order `order` on `order`.id = `detail`.stock_in_order_id
    where
    `order`.purchase_order_id = #{id}
    and `order`.is_del = 0
    and `detail`.is_del = 0
    and `order`.state != 1 and `order`.state != 4
    group by `detail`.item_sku_code, is_gift
  </select>


</mapper>
