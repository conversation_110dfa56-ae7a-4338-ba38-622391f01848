<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtProviderGoodsMapper">
    <insert id="saveOrUpdateBatch">
        INSERT INTO wdt_provider_goods (id, provider_no, spec_no, spec_name, goods_name,
                                        last_purchase_time, origin, short_name, goods_no, spec_code,
                                        class_name, brand_name, is_master, base_unit_name,
                                        provider_goods_no, stock_num, to_purchase_num,
                                        subscribe_num, order_num, sending_num, avaliable_num,
                                        img_url, purchase_num, purchase_arrive_num, discount, price,
                                        retail_price, last_price, last_second_price, lowest_price,
                                        today_num, num_7days, num_14days, num_month, num_all,
                                        unit_name, unit_ratio, min_purchase_num, tax_rate,
                                        is_disabled, prop1, prop2, prop3, prop4, prop5, prop6,
                                        remark, created_at, created_uid, updated_at, updated_uid)
        VALUES
        <foreach collection="goods" item="item" separator=",">
            (null, #{item.providerNo}, #{item.specNo}, #{item.specName}, #{item.goodsName},
             #{item.lastPurchaseTime}, #{item.origin}, #{item.shortName}, #{item.goodsNo},
             #{item.specCode},
             #{item.className}, #{item.brandName}, #{item.isMaster}, #{item.baseUnitName},
             #{item.providerGoodsNo}, #{item.stockNum}, #{item.toPurchaseNum},
             #{item.subscribeNum}, #{item.orderNum}, #{item.sendingNum}, #{item.avaliableNum},
             #{item.imgUrl}, #{item.purchaseNum}, #{item.purchaseArriveNum}, #{item.discount},
             #{item.price},
             #{item.retailPrice}, #{item.lastPrice}, #{item.lastSecondPrice}, #{item.lowestPrice},
             #{item.todayNum}, #{item.num7days}, #{item.num14days}, #{item.numMonth},
             #{item.numAll},
             #{item.unitName}, #{item.unitRatio}, #{item.minPurchaseNum}, #{item.taxRate},
             #{item.isDisabled}, #{item.prop1}, #{item.prop2}, #{item.prop3}, #{item.prop4},
             #{item.prop5}, #{item.prop6},
             #{item.remark},
             #{item.createdAt},
             #{item.createdUid}, #{item.updatedAt}, #{item.updatedUid})
        </foreach>
        ON DUPLICATE KEY UPDATE provider_no         = VALUES(provider_no),
                                spec_no             = VALUES(spec_no),
                                spec_name           = VALUES(spec_name),
                                goods_name          = VALUES(goods_name),
                                last_purchase_time  = VALUES(last_purchase_time),
                                origin              = VALUES(origin),
                                short_name          = VALUES(short_name),
                                goods_no            = VALUES(goods_no),
                                spec_code           = VALUES(spec_code),
                                class_name          = VALUES(class_name),
                                brand_name          = VALUES(brand_name),
                                is_master           = VALUES(is_master),
                                base_unit_name      = VALUES(base_unit_name),
                                provider_goods_no   = VALUES(provider_goods_no),
                                stock_num           = VALUES(stock_num),
                                to_purchase_num     = VALUES(to_purchase_num),
                                subscribe_num       = VALUES(subscribe_num),
                                order_num           = VALUES(order_num),
                                sending_num         = VALUES(sending_num),
                                avaliable_num       = VALUES(avaliable_num),
                                img_url             = VALUES(img_url),
                                purchase_num        = VALUES(purchase_num),
                                purchase_arrive_num = VALUES(purchase_arrive_num),
                                discount            = VALUES(discount),
                                price               = VALUES(price),
                                retail_price        = VALUES(retail_price),
                                last_price          = VALUES(last_price),
                                last_second_price   = VALUES(last_second_price),
                                lowest_price        = VALUES(lowest_price),
                                today_num           = VALUES(today_num),
                                num_7days           = VALUES(num_7days),
                                num_14days          = VALUES(num_14days),
                                num_month           = VALUES(num_month),
                                num_all             = VALUES(num_all),
                                unit_name           = VALUES(unit_name),
                                unit_ratio          = VALUES(unit_ratio),
                                min_purchase_num    = VALUES(min_purchase_num),
                                tax_rate            = VALUES(tax_rate),
                                is_disabled         = VALUES(is_disabled),
                                prop1               = VALUES(prop1),
                                prop2               = VALUES(prop2),
                                prop3               = VALUES(prop3),
                                prop4               = VALUES(prop4),
                                prop5               = VALUES(prop5),
                                prop6               = VALUES(prop6),
                                remark              = VALUES(remark),
                                updated_at          = VALUES(updated_at),
                                updated_uid         = VALUES(updated_uid)
    </insert>
</mapper>
