<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtGoodsSpecMapper">
    <insert id="saveOrUpdateBatch">
        INSERT INTO wdt_goods_spec (id, goods_id, spec_id, spec_no, spec_code, barcode, spec_name,
                                    lowest_price, retail_price, wholesale_price, member_price,
                                    market_price, validity_days, sales_days, receive_days, weight,
                                    length, width, height, sn_type, is_lower_cost, is_not_use_air,
                                    wms_process_mask, tax_rate, large_type, goods_label, deleted,
                                    remark, spec_modified, spec_created, prop1, prop2, prop3, prop4,
                                    prop5, prop6, custom_price1, custom_price2, img_url,
                                    spec_unit_name, spec_aux_unit_name)
        VALUES
        <foreach collection="specs" item="item" separator=",">
            (null, #{item.goodsId}, #{item.specId}, #{item.specNo}, #{item.specCode},
             #{item.barcode}, #{item.specName},
             #{item.lowestPrice}, #{item.retailPrice}, #{item.wholesalePrice}, #{item.memberPrice},
             #{item.marketPrice}, #{item.validityDays}, #{item.salesDays}, #{item.receiveDays},
             #{item.weight},
             #{item.length}, #{item.width}, #{item.height}, #{item.snType}, #{item.isLowerCost},
             #{item.isNotUseAir},
             #{item.wmsProcessMask}, #{item.taxRate}, #{item.largeType}, #{item.goodsLabel},
             #{item.deleted},
             #{item.remark}, #{item.specModified}, #{item.specCreated}, #{item.prop1},
             #{item.prop2}, #{item.prop3}, #{item.prop4},
             #{item.prop5}, #{item.prop6}, #{item.customPrice1}, #{item.customPrice2},
             #{item.imgUrl},
             #{item.specUnitName}, #{item.specAuxUnitName})
        </foreach>
        ON DUPLICATE KEY UPDATE goods_id           = VALUES(goods_id),
                                spec_no            = VALUES(spec_no),
                                spec_code          = VALUES(spec_code),
                                barcode            = VALUES(barcode),
                                spec_name          = VALUES(spec_name),
                                lowest_price       = VALUES(lowest_price),
                                retail_price       = VALUES(retail_price),
                                wholesale_price    = VALUES(wholesale_price),
                                member_price       = VALUES(member_price),
                                market_price       = VALUES(market_price),
                                validity_days      = VALUES(validity_days),
                                sales_days         = VALUES(sales_days),
                                receive_days       = VALUES(receive_days),
                                weight             = VALUES(weight),
                                length             = VALUES(length),
                                width              = VALUES(width),
                                height             = VALUES(height),
                                sn_type            = VALUES(sn_type),
                                is_lower_cost      = VALUES(is_lower_cost),
                                is_not_use_air     = VALUES(is_not_use_air),
                                wms_process_mask   = VALUES(wms_process_mask),
                                tax_rate           = VALUES(tax_rate),
                                large_type         = VALUES(large_type),
                                goods_label        = VALUES(goods_label),
                                deleted            = VALUES(deleted),
                                remark             = VALUES(remark),
                                spec_modified      = VALUES(spec_modified),
                                spec_created       = VALUES(spec_created),
                                prop1              = VALUES(prop1),
                                prop2              = VALUES(prop2),
                                prop3              = VALUES(prop3),
                                prop4              = VALUES(prop4),
                                prop5              = VALUES(prop5),
                                prop6              = VALUES(prop6),
                                custom_price1      = VALUES(custom_price1),
                                custom_price2      = VALUES(custom_price2),
                                img_url            = VALUES(img_url),
                                spec_unit_name     = VALUES(spec_unit_name),
                                spec_aux_unit_name = VALUES(spec_aux_unit_name)
    </insert>
</mapper>
