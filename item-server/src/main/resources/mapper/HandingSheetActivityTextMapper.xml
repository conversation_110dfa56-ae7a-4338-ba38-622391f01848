<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.HandingSheetActivityTextMapper">

    <sql id = "Base_Query">
        FROM handing_sheet_activity_text
        <where>
            ${ew.sqlSegment}
        </where>
    </sql>

    <select id="selectPageList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetActivityText">
        SELECT * <include refid="Base_Query" />
        ORDER BY updated_at DESC, id DESC
    </select>

    <select id="selectPageListCount" resultType="java.lang.Long">
        SELECT COUNT(*) <include refid="Base_Query" />
    </select>
</mapper>
