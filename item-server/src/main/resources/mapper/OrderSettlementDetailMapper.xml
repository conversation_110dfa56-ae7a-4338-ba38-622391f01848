<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderSettlementDetailMapper">
    <select id="getSettlementSummarize"
            resultType="com.daddylab.supplier.item.application.order.settlement.dto.DetailSubtotalDo">
        select sum(temporary_quantity)            as temporaryQuantity,
               sum(deliver_quantity)              as deliverQuantity,
               sum(current_month_refund_quantity) as currentMonthRefundQuantity,
               sum(cross_month_refund_quantity)   as crossMonthRefundQuantity
        from order_settlement_detail
        where form_id = #{formId}
          and is_del = 0;
    </select>

    <select id="getQuantityStatic"
            resultType="com.daddylab.supplier.item.application.order.settlement.dto.DetailQuantityStaticDo">
        select sum(temporary_quantity) as temporaryQuantitySum,
        sum(settlement_quantity) as settlementQuantitySum,
        form_id
        from order_settlement_detail
        where form_id in
        <foreach collection="formIds" item="formId" open="(" separator="," close=")">
            #{formId}
        </foreach>
        and is_del = 0
        group by form_id;
    </select>
</mapper>
