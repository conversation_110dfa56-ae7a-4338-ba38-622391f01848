<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderLogisticsTraceMapper">
    <select id="scanToTraceOrderList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrder">
        SELECT wo.*
        FROM `wdt_order` `wo`
        LEFT JOIN `order_logistics_trace` olt ON `wo`.`trade_id` = olt.`wdt_trade_id`
        WHERE olt.`id` IS NULL AND wo.`modified` >= #{timeCursor}
        <!--        AND wo.trade_status >= 30 AND wo.trade_status <![CDATA[ <= ]]> 95 -->
        <!--  订单状态选择，待处理预订单、待客审 、已审核、已发货、成本确认、已过账、已完成 -->
        AND wo.trade_status in (25,30,55,95,96,101,110)
        AND (wo.deleted_at = 0 OR wo.deleted_at IS NULL)
        AND (`wo`.refund_status IS NULL OR `wo`.refund_status = 0)
        ORDER BY wo.`modified`
        LIMIT #{limit}
    </select>

    <update id="updateStatusToBatch">
        update `order_logistics_trace`
        set trace_status = #{to},
            updated_at   = unix_timestamp()
        where trace_status = #{from}
        limit #{limit}
    </update>
</mapper>
