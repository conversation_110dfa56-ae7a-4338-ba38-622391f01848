<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderDeliveryTraceMapper">


    <resultMap id="CountGroupResultMap" type="com.daddylab.supplier.item.types.CountGroup">
        <id column="id" property="id" javaType="long"/>
        <result column="count" property="count" javaType="int"/>
    </resultMap>
    <select id="countByOrderPersonal" resultMap="CountGroupResultMap">
        select order_personnel_id as id, count(*) as count
        from order_delivery_trace where is_del = 0
        group by order_personnel_id;
    </select>


</mapper>
