<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ChoreMapper">
    <delete id="deleteLogByCreatedAt">
        DELETE
        FROM `log`
        WHERE `created_at` <![CDATA[<]]> #{createdAt} ORDER BY `created_at`
        LIMIT #{limit}
    </delete>


    <select id="selectOrderLogisticsAbnormalByCreatedAtAndStatusClosed" resultType="java.lang.Long">
        SELECT `id`
        FROM `order_logistics_abnormality`
        WHERE `created_at` <![CDATA[<]]> #{createdAt}
          AND `abnormal_status` = 0
            LIMIT #{limit}
    </select>

    <delete id="deleteOrderLogisticsAbnormalByIds">
        DELETE
        FROM `order_logistics_abnormality`
        WHERE `id` IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteOrderLogisticsAbnormalLogByAbnormalIds">
        DELETE
        FROM `order_logistics_abnormality_log`
        WHERE `abnormality_id` IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteOrderLogisticsAbnormalLogByCreatedAt">
        DELETE
        FROM `order_logistics_abnormality_log`
        WHERE `created_at` <![CDATA[<]]> #{createdAt} LIMIT #{limit}
    </delete>

    <delete id="deleteOrderLogisticsAbnormalLogByIds">
        DELETE
        FROM `order_logistics_abnormality_log` WHERE `id` IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>