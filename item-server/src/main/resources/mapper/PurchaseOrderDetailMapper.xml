<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseOrderDetailMapper">

    <select id="detailExportCount" resultType="java.lang.Integer">
        select
           count(1)
        from purchase_order_detail pod
        left join (select `no`,`name` from warehouse where is_del = 0 )`ws` ON `ws`.no = pod.`warehouse_no`
        left join purchase_order po ON pod.purchase_order_id = po.id
        left join provider p ON p.`id` = po.`provider_id`
        left join item `it` ON `it`.`id` = pod.`item_id`
        left join ( select user_id, `name` from buyer where is_del = 0 ) b on b.`user_id` = po.`buyer_user_id`
        <where>
            pod.`is_del` = 0 and po.`is_del` = 0 and `it`.is_del = 0
            <if test="param.orderNo != null and param.orderNo != '' ">
                and po.`no` = #{param.orderNo}
            </if>
            <if test="param.providerId != null and param.providerId != '' ">
                and p.id = #{param.providerId}
            </if>
            <if test="param.type != null and param.type != '' and param.type != 0 ">
                and po.type = #{param.type}
            </if>
            <if test="param.payMode != null and param.payMode != '' and param.payMode != 0 ">
                and po.pay_type = #{param.payMode}
            </if>
            <if test="param.state != null and param.state != '' and param.state != 0 ">
                and po.state = #{param.state}
            </if>
            <if test="param.stockInState != null and param.stockInState != '' and param.stockInState != 0 ">
                and po.stock_in_state = #{param.stockInState}
            </if>
            <if test="param.buyerUserId != null and param.buyerUserId != ''">
                and b.user_id = #{param.buyerUserId}
            </if>
            <if test="param.startDt != null and param.startDt != '' and param.startDt != 0 and
                      param.startEnd != null and param.startEnd != '' and param.startEnd != 0 ">
                and (po.purchase_date between #{param.startDt} and #{param.startEnd} )
            </if>
            <if test="param.contractNo != null and param.contractNo != ''">
                and po.contract_no = #{param.contractNo}
            </if>
            <if test="param.organizationId != null and param.organizationId != ''">
                and po.organization_id = #{param.organizationId}
            </if>
            <if test="param.groupId != null and param.groupId != ''">
                and po.group_id = #{param.groupId}
            </if>
            <if test="param.payOrderNo != null and param.payOrderNo != ''">
                and pod.purchase_order_id in (
                    select
                        sio.purchase_order_id
                    from
                        stock_in_order sio
                    left join purchase_payable_order ppo ON ppo.related_order_id = sio.id
                    where ppo.`no` = #{param.payOrderNo}
                )
            </if>
            <if test="param.itemId != null and param.itemId != ''">
                and pod.item_id = #{param.itemId}
            </if>
            <if test="param.skuCode != null and param.skuCode != ''">
                and pod.item_sku_code = #{param.skuCode}
            </if>
            <if test="param.createdUidList != null and param.createdUidList.size() > 0">
                and po.created_uid in
                <foreach collection="param.createdUidList" item="createdUid" open="(" separator="," close=")">
                    #{createdUid}
                </foreach>
            </if>
            <if test="param.neCreatedUidList != null and param.neCreatedUidList.size() > 0">
                and po.created_uid not in
                <foreach collection="param.neCreatedUidList" item="neCreatedUid" open="(" separator="," close=")">
                    #{neCreatedUid}
                </foreach>
            </if>
            <if test="param.businessLine != null and param.businessLine.size() > 0">
                and po.business_line in
                <foreach collection="param.businessLine" item="bl" open="(" separator="," close=")">
                    #{bl}
                </foreach>
            </if>
            <if test="param.matchedPaymentStatusIdList != null and param.matchedPaymentStatusIdList.size() > 0">
                and po.id in
                <foreach collection="param.matchedPaymentStatusIdList" item="relatedId" open="(" separator=","
                         close=")">
                    #{relatedId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="detailExport"
            resultType="com.daddylab.supplier.item.domain.exportTask.dto.purchase.PurchaseOrderDetailSheet">
        select
            po.`no`,
            FROM_UNIXTIME(po.`purchase_date`, '%Y-%m-%d') as purchaseDate,
            IF(po.`type` = 1, '标准采购', '工厂采购') as purchaseType,
            `organization`.`name` as `organization`,
            po.`contract_no`,
            p.`name` as providerName,
            IF(po.`arrival_type` = 1, '一次性到货', '分批到货') as arrivalType,
                IF(po.`pay_type` = 1, '款到发货', IF(po.pay_type = 2, '预付10%', IF(po.pay_type = 3, '预付20%',
                IF(po.pay_type = 4, '预付30%',
                IF(po.pay_type = 5, '预付40%',
                IF(po.pay_type = 6, '预付50%',
                IF(po.pay_type = 7, '预付60%',
                IF(po.pay_type = 8, '货到付款', IF(po.pay_type = 9, '月结', ''))))))))) as payType,
            po.`department_id` as departmentId,
            `erpGroup`.`name` as erpGroup,
            b.`name` as buyerName,
            pod.`item_sku_code`,
            `it`.`name` as itemName,
            pod.`specifications`,
            pod.`purchase_quantity`,
            pod.`tax_price`,
            pod.`tax_rate`,
            pod.`tax_quota`,
            pod.`total_price_tax`,
            pod.`after_tax_price`,
            pod.`after_tax_amount`,
            IF(pod.`is_gift` = 1, '是', '否') as gift,
            `ws`.`name` as warehouse,
            pod.`remark`,
            po.business_line,
            it.id as itemId
        from purchase_order_detail pod
        left join (select `no`,`name` from warehouse where is_del = 0 )`ws` ON `ws`.no = pod.`warehouse_no`
        left join purchase_order po ON pod.purchase_order_id = po.id
        left join provider p ON p.`id` = po.`provider_id`
        left join item `it` ON `it`.`id` = pod.`item_id`
        left join ( select user_id, `name` from buyer where is_del = 0 ) b on b.`user_id` = po.`buyer_user_id`
        left join (select id,`name` from organization where is_del = 0 and state = 1 and type = 2) `organization` on po.organization_id = `organization`.id
        left join (select id,`name` from erp_group where is_del = 0 and state = 1) `erpGroup` on po.group_id = `erpGroup`.id
        <where>
            pod.`is_del` = 0 and po.`is_del` = 0 and `it`.is_del = 0
            <if test="param.ids != null and param.ids.size() > 0">
                and po.id IN
                <foreach item="item" index="index" collection="param.ids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.orderNo != null and param.orderNo != '' ">
                and po.`no` = #{param.orderNo}
            </if>
            <if test="param.providerId != null and param.providerId != '' ">
                and p.id = #{param.providerId}
            </if>
            <if test="param.type != null and param.type != '' and param.type != 0 ">
                and po.type = #{param.type}
            </if>
            <if test="param.payMode != null and param.payMode != '' and param.payMode != 0 ">
                and po.pay_type = #{param.payMode}
            </if>
            <if test="param.state != null and param.state != '' and param.state != 0 ">
                and po.state = #{param.state}
            </if>
            <if test="param.stockInState != null and param.stockInState != '' and param.stockInState != 0 ">
                and po.stock_in_state = #{param.stockInState}
            </if>
            <if test="param.buyerUserId != null and param.buyerUserId != ''">
                and b.user_id = #{param.buyerUserId}
            </if>
            <if test="param.startDt != null and param.startDt != '' and param.startDt != 0 and
                      param.startEnd != null and param.startEnd != '' and param.startEnd != 0 ">
                and (po.purchase_date between #{param.startDt} and #{param.startEnd} )
            </if>
            <if test="param.contractNo != null and param.contractNo != ''">
                and po.contract_no = #{param.contractNo}
            </if>
            <if test="param.organizationId != null and param.organizationId != ''">
                and po.organization_id = #{param.organizationId}
            </if>
            <if test="param.groupId != null and param.groupId != ''">
                and po.group_id = #{param.groupId}
            </if>
            <if test="param.payOrderNo != null and param.payOrderNo != ''">
                and pod.purchase_order_id in (
                    select
                        sio.purchase_order_id
                    from
                        stock_in_order sio
                    left join purchase_payable_order ppo ON ppo.related_order_id = sio.id
                    where ppo.`no` = #{param.payOrderNo}
                    )
            </if>
            <if test="param.itemId != null and param.itemId != ''">
                and pod.item_id = #{param.itemId}
            </if>
            <if test="param.skuCode != null and param.skuCode != ''">
                and pod.item_sku_code = #{param.skuCode}
            </if>
            <if test="param.createdUidList != null and param.createdUidList.size() > 0">
                and po.created_uid in
                <foreach collection="param.createdUidList" item="createdUid" open="(" separator="," close=")">
                    #{createdUid}
                </foreach>
            </if>
            <if test="param.neCreatedUidList != null and param.neCreatedUidList.size() > 0">
                and po.created_uid not in
                <foreach collection="param.neCreatedUidList" item="neCreatedUid" open="(" separator="," close=")">
                    #{neCreatedUid}
                </foreach>
            </if>
            <if test="param.businessLine != null and param.businessLine.size() > 0">
                and po.business_line in
                <foreach collection="param.businessLine" item="bl" open="(" separator="," close=")">
                    #{bl}
                </foreach>
            </if>
            <if test="param.matchedPaymentStatusIdList != null and param.matchedPaymentStatusIdList.size() > 0">
                and po.id in
                <foreach collection="param.matchedPaymentStatusIdList" item="relatedId" open="(" separator=","
                         close=")">
                    #{relatedId}
                </foreach>
            </if>
        </where>
        order by po.created_at
        limit #{offset},#{size}
    </select>
</mapper>
