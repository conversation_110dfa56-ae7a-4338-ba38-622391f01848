<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemLaunchModuleAdviceMapper">
    <select id="queryLatestAuditAdvicesBatch"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchModuleAdvice">
        SELECT `ilma`.*
        FROM `item_drawer_module_audit` `idma`
                 JOIN `item_launch_module_advice` `ilma` ON ilma.`item_id` = idma.`round`
        WHERE idma.`is_del` = 0
          AND ilma.`is_del` = 0
          AND idma.`item_id` IN
        <foreach item="item" index="index" collection="collection" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
