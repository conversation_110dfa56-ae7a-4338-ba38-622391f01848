<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerMapper">
    <select id="getItemLaunchStatus" resultType="java.lang.Integer">
        SELECT item.launch_status
        FROM item
                 join item_drawer ON item.id = item_drawer.item_id
        WHERE item_drawer.id = #{itemDrawerId};
    </select>


</mapper>
