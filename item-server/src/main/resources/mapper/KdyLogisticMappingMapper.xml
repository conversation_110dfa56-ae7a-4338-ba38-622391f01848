<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.KdyLogisticMappingMapper">


    <select id="inferStdName" resultType="java.lang.String">
        SELECT DISTINCT `std_name`
        FROM `kdy_logistic_mapping`
        WHERE MATCH(`std_name`) AGAINST(#{logisticsName} IN NATURAL LANGUAGE MODE)
        LIMIT 3
    </select>
</mapper>
