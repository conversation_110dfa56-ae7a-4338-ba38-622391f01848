<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.HandingSheetMapper">

    <sql id="Base_Query">
        FROM handing_sheet
        <where>
            ${ew.sqlSegment}
        </where>
    </sql>

    <sql id="Base_Query_With_Status">
        FROM handing_sheet
        <where>
            <if test="state != null">
                `state` = #{state} AND /*这里是注释内容*/
            </if>
            ${ew.sqlSegment}
        </where>
    </sql>

    <update id="updateStatusByHandingSheetId">
        UPDATE handing_sheet
        <set>
            `state` = #{newStatus},
            <if test="newStatus == 2">submit_at = unix_timestamp(),</if>
        </set>
        WHERE `state` = #{oldStatus}
        AND id = #{handingSheetId}
        AND is_del = 0
    </update>

    <update id="updateToProcessing">
        UPDATE handing_sheet SET `state` = 4
        WHERE `state` = 3 AND start_time &lt;= #{nowSec} AND end_time &gt; #{nowSec}
    </update>

    <update id="updateToExpired">
        UPDATE handing_sheet SET `state` = 5
        WHERE end_time &lt;= #{nowSec} AND `state` != 1 /*产品要求说：待提交状态不可流转为已过期状态*/
    </update>

    <select id="selectPageListCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        <include refid="Base_Query" />
    </select>

    <select id="selectPageListCountWithStatus" resultType="java.lang.Long">
        SELECT COUNT(*)
        <include refid="Base_Query_With_Status" />
    </select>

    <select id="selectPageList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheet">
        SELECT *
        <include refid="Base_Query" />
        ORDER BY id DESC
    </select>

    <select id="getByHandingSheetItemId"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheet">
        SELECT t2.*
        FROM handing_sheet_item AS t1
        INNER JOIN handing_sheet AS t2 ON (t1.handing_sheet_id = t2.id)
        WHERE t1.id = #{handingSheetItemId} AND t1.is_del = 0 AND t2.is_del = 0
    </select>

    <select id="getByHandingSheetActivityTextId"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheet">
        SELECT t2.*
        FROM handing_sheet_activity_text AS t1
        INNER JOIN handing_sheet AS t2 ON (t1.handing_sheet_id = t2.id)
        WHERE t1.id = #{handingSheetActivityTextId} AND t1.is_del = 0 AND t2.is_del = 0
    </select>
</mapper>
