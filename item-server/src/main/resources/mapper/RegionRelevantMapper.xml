<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.RegionRelevantMapper">
    <select id="getNameByCode"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.RegionRelevantDo">
        select code,name
        from region_relevant
        where is_del = 0
        and code in
        <foreach collection="codes" item="code" separator="," open="(" close=")">
            #{code}
        </foreach>
    </select>
</mapper>
