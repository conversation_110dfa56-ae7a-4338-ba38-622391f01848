<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OffShelfInfoMapper">
    <select id="page" resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OffShelfInfo">
        SELECT *
        FROM `off_shelf_info` `osi`
        <where>
            is_del = 0
            <if test="id != null">
                AND `id` = #{id}
            </if>
            AND EXISTS(SELECT `osi2`.`id`
                       FROM `off_shelf_item` `osi2`
                                JOIN `item` ON `osi2`.`item_id` = `item`.`id`
            <where>
                `off_shelf_info_id` = `osi`.`id`
                  and osi2.is_del = 0
                <if test="itemId != null">
                    AND `item_id` = #{itemId}
                </if>
                <if test="itemCode != null and itemCode != ''">
                    AND `item`.`code` = #{itemCode}
                </if>
                <if test="partnerProviderItemSn != null and partnerProviderItemSn != ''">
                    AND `item`.`partner_provider_item_sn` = #{partnerProviderItemSn}
                </if>
                <if test="brandId != null">
                    AND `item`.`brand_id` = #{brandId}
                </if>
                <if test="categoryId != null">
                    AND `item`.`category_id` = #{categoryId}
                </if>
                <if test="itemName != null and itemName != ''">
                    AND `item`.`name` LIKE CONCAT('%', #{itemName}, '%')
                </if>
                /*@BizScope_expression@*/
            </where>
            )
            <if test="no != null and no != ''">
                AND `no` = #{no}
            </if>
            <if test="status != null">
                AND `status` = #{status}
            </if>
            <if test="applyTimeStart != null">
                AND `created_at` >= #{applyTimeStart}
            </if>
            <if test="applyTimeEnd != null">
                AND `created_at` <![CDATA[<=]]> #{applyTimeEnd}
            </if>
            <if test="applicantUid != null">
                AND `created_uid` = #{applicantUid}
            </if>
            <if test="operatorUid != null">
                AND FIND_IN_SET(#{operatorUid}, `operator_uid`) > 0
            </if>
            <if test="reasonType != null">
                AND FIND_IN_SET(#{reasonType}, `reason_type`) > 0
            </if>
            <if test="urgentLevel != null">
                AND urgent_level = #{urgentLevel}
            </if>
        </where>
        order by `id` desc
    </select>
</mapper>
