<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemCodeRefMapper">
    <delete id="clearAll">
        TRUNCATE `item_code_ref`;
    </delete>

    <insert id="scanItemCode">
        INSERT IGNORE `item_code_ref` (`code`, `type`, `item_id`)
        SELECT `code`, 1, id
        FROM `item`
        WHERE code != ''
          AND is_del = 0;
    </insert>

    <insert id="scanProviderSpecifiedCode">
        INSERT IGNORE `item_code_ref` (`code`, `type`, `item_id`)
        SELECT `provider_specified_code`, 2, id
        FROM `item`
        WHERE `provider_specified_code` != ''
          AND is_del = 0;
    </insert>

    <insert id="scanPsysCode">
        INSERT IGNORE `item_code_ref` (`code`, `type`, `item_id`)
        SELECT `partner_provider_item_sn`, 3, id
        FROM `item`
        WHERE `partner_provider_item_sn` != ''
          AND is_del = 0;
    </insert>

    <insert id="scanSkuCode">
        INSERT IGNORE `item_code_ref` (`code`, `type`, `item_id`, sku_id)
        SELECT `sku_code`, 4, `item_id`, id
        FROM `item_sku`
        WHERE sku_code != ''
          AND is_del = 0
          AND EXISTS(SELECT id FROM `item` WHERE id = item_id AND is_del = 0);
    </insert>

    <insert id="scanSkuProviderSpecifiedCode">
        INSERT IGNORE `item_code_ref` (`code`, `type`, `item_id`, sku_id)
        SELECT `provider_specified_code`, 5, item_id, id
        FROM `item_sku`
        WHERE `provider_specified_code` != ''
          AND is_del = 0
          AND EXISTS(SELECT id FROM `item` WHERE id = item_id AND is_del = 0);
    </insert>

    <insert id="scanRefItemCode">
        INSERT IGNORE `item_code_ref` (`code`, `type`, `item_id`)
        SELECT `item_code`, 6, `item_id` FROM `spu` WHERE `is_del` = 0;
    </insert>

    <insert id="scanRefSkuCode1">
        INSERT IGNORE `item_code_ref` (`code`, `type`, `item_id`, `sku_id`)
        SELECT sku.`sku_code`, 7, `item_id`, `item_sku`.id
        FROM `sku`
                 JOIN `item_sku` ON sku.`sku_code` = `item_sku`.`sku_code`
        WHERE sku.`is_del` = 0
          AND `item_sku`.`is_del` = 0;
    </insert>
    <insert id="scanRefSkuCode2">
        INSERT IGNORE `item_code_ref` (`code`, `type`, `item_id`, `sku_id`)
        SELECT sku.`sku_code`, 7, `item_id`, `item_sku`.id
        FROM `sku`
                 JOIN `item_sku` ON sku.`sku_code` = `item_sku`.`provider_specified_code`
        WHERE sku.`is_del` = 0
          AND `item_sku`.`is_del` = 0;
    </insert>
</mapper>
