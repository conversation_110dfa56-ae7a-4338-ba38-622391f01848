<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderLogisticsAbnormalityLogMapper">
    <sql id="logisticExceptionType_Conditions">
        <!--@sql # for auto complete
        select * from order_logistics_abnormality ola where TRUE
        -->

        <if test="query.logisticsExceptionType != null and query.logisticsExceptionType > 0">
            and ola.abnormal_type = #{query.logisticsExceptionType}
        </if>
        <if test="query.logisticsExceptionType2 != null and !query.logisticsExceptionType2.empty">
            and ola.abnormal_type2 IN
            <foreach item="item" index="index" collection="query.logisticsExceptionType2" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </sql>
    <sql id="logisticExceptionSearch_Conditions">
        <!--@sql # for auto complete
        select * from order_logistics_abnormality ola where TRUE
        -->
        and ola.is_del = 0
        <if test="query.logisticsNo != null and !query.logisticsNo.empty">
            and ola.logistics_no = #{query.logisticsNo}
        </if>
        <if test="query.logisticsCompany != null and !query.logisticsCompany.empty">
            and ola.logistics_company_name = #{query.logisticsCompany}
        </if>
        <if test="query.logisticsStatus != null">
            and ola.logistics_status = #{query.logisticsStatus}
        </if>
        <if test="query.abnormalStatus != null">
            and ola.abnormal_status = #{query.abnormalStatus,jdbcType=INTEGER}
        </if>
        <if test="query.stockoutWarehouseNo != null and !query.stockoutWarehouseNo.empty">
            and ola.stockout_warehouse_no = #{query.stockoutWarehouseNo,jdbcType=VARCHAR}
        </if>
        <if test="query.stockoutNo != null and !query.stockoutNo.empty">
            and ola.stockout_no = #{query.stockoutNo}
        </if>
        <if test="query.platform != null">
            and ola.platform = #{query.platform}
        </if>
        <if test="query.srcOrderNo != null and !query.srcOrderNo.empty">
            and ola.src_order_no = #{query.srcOrderNo}
        </if>
        <if test="query.shopId != null">
            and ola.shop_id = #{query.shopId}
        </if>
        <if test="query.payTimeStart != null and query.payTimeStart > 0">
            and ola.pay_time >= #{query.payTimeStart}
        </if>
        <if test="query.payTimeEnd != null and query.payTimeEnd > 0">
            and ola.pay_time <![CDATA[<=]]> #{query.payTimeEnd}
        </if>
        <if test="query.deliveryTimeStart != null and query.deliveryTimeStart > 0">
            and ola.consign_time >= #{query.deliveryTimeStart}
        </if>
        <if test="query.deliveryTimeEnd != null and query.deliveryTimeEnd > 0">
            and ola.consign_time <![CDATA[<=]]> #{query.deliveryTimeEnd}
        </if>
        <if test="query.orderTimeStart != null and query.orderTimeStart > 0">
            and ola.trade_time >= #{query.orderTimeStart}
        </if>
        <if test="query.orderTimeEnd != null and query.orderTimeEnd > 0">
            and ola.trade_time <![CDATA[<=]]> #{query.orderTimeEnd}
        </if>
        <if test="query.orderAmountMin != null">
            and ola.order_amount > #{query.orderAmountMin}
        </if>
        <if test="query.orderAmountMax != null">
            and ola.order_amount <![CDATA[<=]]> #{query.orderAmountMax}
        </if>
        <if test="query.csUserId != null">
            and exists(select 1
                       from order_logistics_abnormality_cs as cs
                       where cs.abnormality_id = ola.id
                         and cs.cs_user_id = #{query.csUserId})
        </if>
        <if test="query.distributeStatus == 0">
            and not exists(select id from order_logistics_abnormality_cs where abnormality_id = ola.id and is_del = 0)
        </if>
        <if test="query.distributeStatus == 1">
            and exists(select id from order_logistics_abnormality_cs where abnormality_id = ola.id and is_del = 0)
        </if>
    </sql>

    <sql id="logisticException_Conditions">
        <include refid="logisticExceptionSearch_Conditions"/>
        <include refid="logisticExceptionType_Conditions"/>
    </sql>

    <select id="selectAbnormalityCountObjs"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.AbnormalityCountObj">
        select abnormal_type, abnormal_type2, count(*) as count
        from order_logistics_abnormality ola
        <where>
            <include refid="logisticExceptionSearch_Conditions"/>
        </where>
        group by abnormal_type, abnormal_type2;
    </select>

    <sql id="order_Conditions">
        <!--@sql select * from order_logistics_abnormality ola where true-->
    </sql>
    <select id="selectAbnormalityList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.AbnormalityListObj">
        select ola.id             as abnormality_id,
               olt.logistics_no,
               olt.logistics_name as logistics_company_name,
               ola.consign_time,
               olt.wdt_trade_no,
               wo.trade_id        as wdt_trade_id,
               wo.trade_status,
               wo.stockout_no,
               olt.src_order_no,
               wo.receiver_name,
               wo.receiver_address,
               wo.receiver_area,
               wo.receiver_mobile,
               olt.callback_id,
               olt.trace_source,
               ola.logistics_status,
               olt.track_time,
               wo.warehouse_no,
               ola.abnormal_status,
               ola.activate_warning,
               ola.platform,
               ola.shop_id
        from order_logistics_abnormality ola
                 join order_logistics_trace olt on olt.id = ola.trace_id
                 join wdt_order wo on wo.trade_id = olt.wdt_trade_id
        <where>
            <include refid="logisticException_Conditions"/>
        </where>
        limit #{query.offset}, #{query.pageSize}
    </select>

    <select id="selectAbnormalityListCount" resultType="java.lang.Integer">
        select count(*)
        from order_logistics_abnormality ola
                 join order_logistics_trace olt on olt.id = ola.trace_id
                 join wdt_order wo on wo.trade_id = olt.wdt_trade_id
                 left join kdy_callback kc on olt.callback_id != 0 and kc.id = olt.callback_id
        <where>
            <include refid="logisticException_Conditions"/>
        </where>
    </select>

    <select id="selectAbnormalityPOList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsAbnormality">
        select ola.*
        from order_logistics_abnormality ola
        <where>
            <include refid="logisticException_Conditions"/>
        </where>
        limit #{query.offset}, #{query.pageSize}
    </select>
</mapper>
