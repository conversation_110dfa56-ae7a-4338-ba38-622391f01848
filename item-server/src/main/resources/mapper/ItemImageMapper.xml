<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemImageMapper">

<select id="selectMainImg"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemMainImgDO">
    select
        i.code as itemCode,
        item_id as itemId,
        image_url as mainImgUrl
    from item_image
             join item i on item_image.item_id = i.id
    where item_image.type = 1
        and item_image.is_main = 1
        and item_image.is_del = 0
        and i.code in
        <foreach collection="itemCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
</mapper>
