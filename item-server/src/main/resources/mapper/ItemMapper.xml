<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemMapper">
    <select id="dropDownList" resultType="com.daddylab.supplier.item.controller.item.dto.NameDropDownVo">
        select `name`,`id` from item
        <where>
            is_del = 0
            <if test="name != null and name != ''">
                and `name` like concat('%', #{name}, '%')
            </if>
            <if test="useForItemLaunchPlan != null and 'true'.toString() == useForItemLaunchPlan.toString()">
                and partner_provider_item_sn != '' and launch_status = 1 and status IN (0, 1)
            </if>
            <if test="providerId != null">
                and provider_id = #{providerId}
            </if>
        </where>
        limit #{offset},#{size}
    </select>

    <sql id="Base_Query_Item">
        FROM
        ( select * from item
        <where>
            is_del = 0
            <!--            商品id查询-->
            <if test="param.itemId != null and param.itemId != '' ">
                and id = #{param.itemId}
            </if>
            <!--            商品id查询（批量）-->
            <if test="param.itemIds != null and !param.itemIds.empty ">
                and id IN
                <foreach item="item" index="index" collection="param.itemIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!--            商品名称模糊查询-->
            <if test="param.itemName != null and param.itemName != ''">
                and `name` like concat('%', #{param.itemName}, '%')
            </if>
            <!--            商品编码查询-->
            <if test="param.code != null and param.code != ''">
                and (code = #{param.code} OR provider_specified_code = #{param.code})
            </if>
            <!--            商品sku查询-->
            <if test="param.sku != null and param.sku != ''">
                and id in (select item_id from item_sku where is_del = 0 and (sku_code LIKE concat(#{param.sku}, '%') or
                provider_specified_code = #{param.sku}) )
            </if>
            <!--            商品其他系统关联编号查询-->
            <if test="param.partnerCode != null and param.partnerCode != ''">
                and partner_provider_item_sn = UPPER(#{param.partnerCode})
            </if>
            <!--            商品上新时间查询-->
            <if test="param.shelfStartTime != null and 0 != param.shelfStartTime and param.shelfEndTime != null and 0 != param.shelfEndTime ">
                and (estimate_sale_time between #{param.shelfStartTime} and #{param.shelfEndTime} )
            </if>
            <!--            商品状态查询-->
            <if test="param.itemStatus != null">
                and status = #{param.itemStatus}
            </if>
            <if test="param.itemStatusList != null and param.itemStatusList.size() > 0">
                and status IN
                <foreach item="item" index="index" collection="param.itemStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <!--上新计划选择商品约束：1、必须关联P系统商品款号 2、未跟其他上新计划关联（即上新状态为待选择）3、后端商品状态为待上架和在售中-->
            <if test="param.useForItemLaunchPlan">
                and partner_provider_item_sn != '' and launch_status = 1 and status IN (0, 1)
            </if>

            <!--  -->
            <if test="param.useForHandingSheet">
                and status = 1
            </if>
            <!-- 根据仓库编号查询-->
            <if test="param.warehouseNo != null and param.warehouseNo != ''">
                and warehouse_no = #{param.warehouseNo}
            </if>
            <!--            &lt;!&ndash; 根据合作模式/业务线查询&ndash;&gt;-->
            <!--            <if test="param.businessLine != null">-->
            <!--                <if test="param.businessLine != null and param.businessLine.size() > 0">-->
            <!--                    AND-->
            <!--                    <foreach item="lineId" index="index" collection="param.businessLine" open="(" separator="OR" close=")">-->
            <!--                        is_business_line${lineId} = 1-->
            <!--                    </foreach>-->
            <!--                </if>-->
            <!--            </if>-->

            <if test="param.delivery != null">
                <if test="param.delivery != null">
                    <if test="param.delivery != 2">
                        AND exists(select id from item_procurement where is_del = 0 and item_id = item.id and
                        find_in_set(#{param.delivery}, delivery))
                    </if>
                </if>
            </if>
            <if test="param.tag != null and param.tag != ''">
                AND exists(select id from item_tag where is_del = 0 and item_id = item.id and tag_id = #{param.tag})
            </if>

            <if test="param.offShelfStartTime != null and 0 != param.offShelfStartTime and
                    param.offShelfEndTime != null and 0 != param.offShelfEndTime ">
                and (down_frame_time between #{param.offShelfStartTime} and #{param.offShelfEndTime} )
            </if>
        </where>
        ) i
        LEFT JOIN (select id,path from category where is_del = 0) c ON i.category_id = c.id
        LEFT JOIN (select id,`name` from brand where is_del =0 ) b ON i.brand_id = b.id
        LEFT JOIN (select item_id,buyer_id from item_procurement where is_del = 0 ) ip ON i.id = ip.item_id
        LEFT JOIN (select id,name,user_id from buyer where is_del = 0 ) buy ON buy.id = ip.buyer_id
        <!--        LEFT JOIN (select item_id,price from item_price where is_del = 0 and type = 0 and is_main = 1-->
        <!--            order by start_time desc,created_at desc limit 1) p1 on i.id = p1.item_id-->
        LEFT JOIN (select item_id,price from item_price where is_del = 0 and type = 1 and is_main = 1 ) p2 on i.id =
        p2.item_id
        <!--        LEFT JOIN (select item_id,image_url from item_image where is_del = 0 and is_main = 1) image on image.item_id = i.id-->
        <where>
            <!--            商品品类id查询-->
            <if test="param.categoryId != null and param.categoryId != '' ">
                c.id = #{param.categoryId}
            </if>
            <!--            商品品牌id查询-->
            <if test="param.brandId != null and param.brandId != '' ">
                and b.id = #{param.brandId}
            </if>
            <!--            商品品牌名称模糊查询-->
            <if test="param.brandName != null and param.brandName != ''">
                and b.`name` like concat('%', #{param.brandName}, '%')
            </if>
            <!--            商品采购员id查询-->
            <if test="param.buyerUserId != null and param.buyerUserId != '' ">
                and buy.user_id = #{param.buyerUserId}
            </if>
            <if test="param.providerId != null and param.providerId != '' ">
                and i.provider_id = #{param.providerId}
            </if>
        </where>
    </sql>

    <select id="queryPage" resultType="com.daddylab.supplier.item.controller.item.dto.ItemPageVo">
        SELECT
        i.id,
        IF(i.provider_specified_code != '', i.provider_specified_code, i.code) AS `code`,
        i.`name` AS `name`,
        i.`status` AS `status`,
        i.category_id AS categoryId,
        i.brand_id AS brandId,
        i.provider_id AS providerId,
        i.estimate_sale_time AS shelfTime,
        i.partner_provider_item_sn AS partnerCode,
        i.`down_frame_time`,
        i.`down_frame_reason`,
        c.path AS categoryPath,
        b.`name` AS brandName,
        IFNULL(p2.price,'') AS `salePrice`,
        i.status as `status`,
        i.business_line,
        i.business_lines as businessLines
        FROM
        ( select * from item
        <where>
            is_del = 0
            <!--            商品id查询-->
            <if test="param.itemId != null and param.itemId != '' ">
                and id = #{param.itemId}
            </if>
            <!--            商品id查询（批量）-->
            <if test="param.itemIds != null and !param.itemIds.empty ">
                and id IN
                <foreach item="item" index="index" collection="param.itemIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!--            商品名称模糊查询-->
            <if test="param.itemName != null and param.itemName != ''">
                and `name` like concat('%', #{param.itemName}, '%')
            </if>
            <!--            商品编码查询-->
            <if test="param.code != null and param.code != ''">
                and (code = #{param.code} OR provider_specified_code = #{param.code})
            </if>
            <!--            商品sku查询-->
            <if test="param.sku != null and param.sku != ''">
                and id in (select item_id from item_sku where is_del = 0 and (sku_code LIKE concat(#{param.sku}, '%') or
                provider_specified_code = #{param.sku}) )
            </if>
            <!--            商品其他系统关联编号查询-->
            <if test="param.partnerCode != null and param.partnerCode != ''">
                and partner_provider_item_sn = UPPER(#{param.partnerCode})
            </if>
            <!--            商品上新时间查询-->
            <if test="param.shelfStartTime != null and 0 != param.shelfStartTime and param.shelfEndTime != null and 0 != param.shelfEndTime ">
                and (estimate_sale_time between #{param.shelfStartTime} and #{param.shelfEndTime} )
            </if>
            <!--            商品状态查询-->
            <if test="param.itemStatus != null">
                and status = #{param.itemStatus}
            </if>
            <if test="param.itemStatusList != null and param.itemStatusList.size() > 0">
                and status IN
                <foreach item="item" index="index" collection="param.itemStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <!--上新计划选择商品约束：1、必须关联P系统商品款号 2、未跟其他上新计划关联（即上新状态为待选择）3、后端商品状态为待上架和在售中-->
            <if test="param.useForItemLaunchPlan">
                and partner_provider_item_sn != '' and launch_status = 1 and status IN (0, 1)
            </if>

            <!--  -->
            <if test="param.useForHandingSheet">
                and status = 1
            </if>
            <!-- 根据仓库编号查询-->
            <if test="param.warehouseNo != null and param.warehouseNo != ''">
                and warehouse_no = #{param.warehouseNo}
            </if>
            <!-- 根据合作模式/业务线查询-->
            <!--            <if test="param.businessLine != null and param.businessLine.size() != 0">-->
            <!--                AND-->
            <!--                <foreach item="lineId" index="index" collection="param.businessLine" open="(" separator="OR" close=")">-->
            <!--                    is_business_line${lineId} = 1-->
            <!--                </foreach>-->
            <!--            </if>-->
            <if test="param.delivery != null">
                <if test="param.delivery != 2">
                    AND exists(select id from item_procurement where is_del = 0 and item_id = item.id and
                    find_in_set(#{param.delivery}, delivery))
                </if>
            </if>
            <if test="param.tag != null and param.tag != ''">
                AND exists(select id from item_tag where is_del = 0 and item_id = item.id and tag_id = #{param.tag})
            </if>

            <if test="param.offShelfStartTime != null and 0 != param.offShelfStartTime and
                param.offShelfEndTime != null and 0 != param.offShelfEndTime ">
                and (down_frame_time between #{param.offShelfStartTime} and #{param.offShelfEndTime} )
            </if>
        </where>
        ) i
        LEFT JOIN (select id,path from category where is_del = 0) c ON i.category_id = c.id
        LEFT JOIN (select id,`name` from brand where is_del =0 ) b ON i.brand_id = b.id
        LEFT JOIN (select item_id,buyer_id from item_procurement where is_del = 0 ) ip ON i.id = ip.item_id
        LEFT JOIN (select id,name,user_id from buyer where is_del = 0 ) buy ON buy.id = ip.buyer_id
        LEFT JOIN (select item_id,price from item_price where is_del = 0 and type = 1 and is_main = 1 ) p2 on i.id =
        p2.item_id
        <where>
            <!--            商品品类id查询-->
            <if test="param.categoryId != null and param.categoryId != '' ">
                c.id = #{param.categoryId}
            </if>
            <!--            商品品牌id查询-->
            <if test="param.brandId != null and param.brandId != '' ">
                and b.id = #{param.brandId}
            </if>
            <!--            商品品牌名称模糊查询-->
            <if test="param.brandName != null and param.brandName != ''">
                and b.`name` like concat('%', #{param.brandName}, '%')
            </if>
            <!--            商品采购员id查询-->
            <if test="param.buyerUserId != null and param.buyerUserId != '' ">
                and buy.user_id = #{param.buyerUserId}
            </if>
            <if test="param.providerId != null and param.providerId != '' ">
                and i.provider_id = #{param.providerId}
            </if>
        </where>
        order by i.created_at desc, i.id desc limit #{param.offsetVal}, #{param.pageSize}
    </select>


    <select id="queryPageCount" resultType="java.lang.Integer">
        SELECT
        count(*)
        <include refid="Base_Query_Item"/>
    </select>

    <sql id="selectItemBaseData">
        select i.`id`                     as `id`,
               i.`name`                   as `name`,
               i.provider_specified_code  as specialCode,
               i.partner_provider_item_sn as partnerCode,
               i.business_line            AS businessLine,
               i.business_lines           AS businessLines,
               i.status                   as itemStatus,
               i.status_remark            as statusReason,
               i.code                     as `code`,
               i.is_gift                  as isGift,
               i.parent_item_id           as parentItemId,
               i.parent_code              as parentCode,
               b.name                     as brandName,
               i.brand_id                 as brandId,
               c.path                     as categoryPath,
               i.category_id              as categoryId,
               p.name                     as providerName,
               p.partner_provider_id      as partnerProviderId,
               i.provider_id              as providerId,
               buy.name                   as buyerName,
               buy.user_id                as buyerUserId,
               ip.delivery                as delivery,
               ip.qc_ids                  as qcIds,
               i.`short_name`             as shortName,
               i.down_frame_time          as downFrameTime,
               i.down_frame_reason        as downFrameReason
        from (select * from item where is_del = 0) i
                 left join (select * from brand where is_del = 0) b on i.brand_id = b.id
                 left join (select * from category where is_del = 0) c on i.category_id = c.id
                 left join (select * from provider where is_del = 0) p on i.provider_id = p.id
                 left join (select * from item_procurement where is_del = 0) ip on i.id = ip.item_id
                 left join (select * from buyer where is_del = 0) buy on buy.id = ip.buyer_id
    </sql>
    <select id="queryDetailBase"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemBaseDO">
        <include refid="selectItemBaseData"/>
        where i.id = #{itemId}
    </select>

    <sql id="itemBaseColumns">
        `i`.`id` AS `id`,
        `i`.`name` AS `name`,
        `i`.`short_name` AS shortName,
        `i`.`provider_specified_code` AS `specialCode`,
        `i`.`partner_provider_item_sn` AS `partnerCode`,
        `i`.`status` AS `itemStatus`,
        `i`.`status_remark` AS `statusReason`,
        `i`.`code` AS `code`,
        `i`.`is_gift` AS `isGift`,
        `i`.`parent_item_id` AS `parentItemId`,
        `i`.`parent_code` AS `parentCode`,
        `b`.`name` AS `brandName`,
        `i`.`brand_id` AS `brandId`,
        `c`.`path` AS `categoryPath`,
        `i`.`category_id` AS `categoryId`,
        `p`.`name` AS `providerName`,
        `i`.`provider_id` AS `providerId`,
        `buy`.`name` AS `buyerName`,
        `buy`.`user_id` AS `buyerUserId`,
        `ip`.`delivery` AS `delivery`,
    </sql>
    <sql id="mainItemImageColumns">
        `ii`.`mainImgId` AS `mainImgId`,
        `ii`.`mainImgUrl` AS `mainImgUrl`
    </sql>
    <sql id="itemBaseFrom">
        FROM
            (SELECT * FROM `item` WHERE `is_del` = 0)
            `i`
        LEFT JOIN (SELECT * FROM `brand` WHERE `is_del` = 0) `b` ON `i`.`brand_id` = `b`.`id`
        LEFT JOIN (SELECT * FROM `category` WHERE `is_del` = 0) `c` ON `i`.`category_id` = `c`.`id`
        LEFT JOIN (SELECT * FROM `provider` WHERE `is_del` = 0) `p` ON `i`.`provider_id` = `p`.`id`
        LEFT JOIN (SELECT * FROM `item_procurement` WHERE `is_del` = 0) `ip` ON `i`.`id` = `ip`.`item_id`
        LEFT JOIN (SELECT * FROM `buyer` WHERE `is_del` = 0) `buy` ON `buy`.`id` = `ip`.`buyer_id`
    </sql>
    <select id="queryDetailBaseWithImage"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemBaseDO">
        <trim prefix="SELECT" suffixOverrides=",">
            <include refid="itemBaseColumns"/>
            <include refid="mainItemImageColumns"/>
        </trim>
        <include refid="itemBaseFrom"/>
        LEFT JOIN (SELECT `item_id`, ANY_VALUE(`id`) `mainImgId`, ANY_VALUE(`image_url`) `mainImgUrl` FROM `item_image`
        WHERE `item_id` = #{itemId} AND `type` = 1 AND `is_main` = 1 AND
        `is_del` = 0 LIMIT 1) `ii` ON `ii`.`item_id` = `i`.`id`
        WHERE i.id = #{itemId}
    </select>
    <select id="queryDetailBaseWithImageByIds"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemBaseDO">
        <trim prefix="SELECT" suffixOverrides=",">
            <include refid="itemBaseColumns"/>
            <include refid="mainItemImageColumns"/>
        </trim>
        <include refid="itemBaseFrom"/>
        LEFT JOIN (SELECT `item_id`, ANY_VALUE(`id`) `mainImgId`, ANY_VALUE(`image_url`) `mainImgUrl` FROM `item_image`
        WHERE `item_id` IN
        <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
        AND `type` = 1 AND `is_main` = 1 AND `is_del` = 0 GROUP BY `item_id`) `ii` ON `ii`.`item_id` = `i`.`id`
        <where>
            `i`.`id` IN
            <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </where>
    </select>

    <select id="queryItemSimple" resultType="com.daddylab.supplier.item.controller.item.dto.ItemSimpleViewVo">
        select
        i.id AS id,
        i.`name` AS `name`,
        i.code AS code,
        b.`name` AS brandName,
        c.`name` AS categoryName,
        ii.image_url AS imageUrl
        from (select * from item where is_del = 0 ) i
        left join (select * from brand where is_del = 0 ) b on i.brand_id = b.id
        left join (select * from category where is_del = 0 ) c on i.category_id = c.id
        left join (select * from item_image where is_del = 0 and is_main = 1 ) ii on ii.item_id = i.id
        <if test="param.type == 1">
            where i.name like concat('%', #{param.query}, '%')
        </if>
        <if test="param.type == 2">
            where i.code like concat('%', #{param.query}, '%')
        </if>
        <if test="param.type == 3">
            where i.id = #{param.query}
        </if>
    </select>

    <select id="countExportSku" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM
        item_sku
        WHERE
        item_id in
        (
        SELECT
        i.id
        FROM
        ( select * from item
        <where>
            is_del = 0
            <!--            商品id查询-->
            <if test="param.itemId != null and param.itemId != '' ">
                and id = #{param.itemId}
            </if>
            <!--            商品名称模糊查询-->
            <if test="param.itemName != null and param.itemName != '' ">
                and `name` LIKE concat('%', #{param.itemName}, '%')
            </if>
            <!--            商品编码查询-->
            <if test="param.code != null and param.code != '' ">
                and `code` = #{param.code}
            </if>
            <!--            商品其他系统关联编号查询-->
            <if test="param.partnerCode != null and param.partnerCode != ''">
                and partner_provider_item_sn = #{param.partnerCode}
            </if>
            <!--            商品上新时间查询-->
            <if test="param.shelfStartTime != null and 0 != param.shelfStartTime and param.shelfEndTime != null and 0 != param.shelfEndTime ">
                and (estimate_sale_time between #{param.shelfStartTime} and #{param.shelfEndTime} )
            </if>

            <if test="param.offShelfStartTime != null and 0 != param.offShelfStartTime and
                param.offShelfEndTime != null and 0 != param.offShelfEndTime ">
                and (down_frame_time between #{param.offShelfStartTime} and #{param.offShelfEndTime} )
            </if>
            <!--            <if test="param.businessLine != null and param.businessLine.size() > 0">-->
            <!--                AND-->
            <!--                <foreach item="lineId" index="index" collection="param.businessLine" open="(" separator="OR" close=")">-->
            <!--                    is_business_line${lineId} = 1-->
            <!--                </foreach>-->
            <!--            </if>-->
        </where>
        ) i
        LEFT JOIN (select id,path from category where is_del = 0) c ON i.category_id = c.id
        LEFT JOIN (select id,`name` from brand where is_del =0 ) b ON i.brand_id = b.id
        LEFT JOIN (select item_id,buyer_id from item_procurement where is_del = 0 ) ip ON i.id = ip.item_id
        LEFT JOIN (select id,name,user_id from buyer where is_del = 0 ) buyer ON buyer.id = ip.buyer_id
        LEFT JOIN (select item_id,price from item_price where is_del = 0 and type = 1) p1 on i.id = p1.item_id
        LEFT JOIN (select item_id,price from item_price where is_del = 0 and type = 2) p2 on i.id = p2.item_id
        <where>
            <!--            商品品类id查询-->
            <if test="param.categoryId != null and param.categoryId != '' ">
                c.id = #{param.categoryId}
            </if>
            <!--            商品品牌id查询-->
            <if test="param.brandId != null and param.brandId != '' ">
                and b.id = #{param.brandId}
            </if>
            <!--            商品品牌名称模糊查询-->
            <if test="param.brandName != null and param.brandName != ''">
                and b.`name` like "%"{param.brandName}"%"
            </if>
            <!--            商品采购员id查询-->
            <if test="param.buyerUserId != null and param.buyerUserId != '' ">
                and buyer.user_id = #{param.buyerUserId}
            </if>

            <!--            商品供应商id查询-->
            <if test="param.providerId != null and param.providerId != ''">
                and i.provider_id = #{param.providerId}
            </if>
        </where>
        )
        <!--                    商品sku查询-->
        <if test="param.sku != null and param.sku != ''">
            and sku_code = #{param.sku}
        </if>
    </select>

    <!--    <select id="queryExportItem"-->
    <!--            resultType="com.daddylab.supplier.item.domain.exportTask.dto.item.ExportItemDto">-->
    <!--        SELECT-->
    <!--        i.id AS itemId,-->
    <!--        i.code AS code,-->
    <!--        i.`name` AS `name`,-->
    <!--        c.`path` AS category,-->
    <!--        b.`name` AS brand,-->
    <!--        procurementPrice.price AS `procurement`,-->
    <!--        salesPrice.`price` AS salesPrice,-->
    <!--        linePrice.`price` AS `linePrice`,-->
    <!--        activityPrice.`price` AS `activityPrice`,-->
    <!--        lowestPrice.`price` AS `lowestPrice`,-->
    <!--        p.`name` AS `provider`,-->
    <!--        buyer.`name` AS `buyer`,-->
    <!--        IF(i.estimate_sale_time = 0 ,'',from_unixtime(i.estimate_sale_time,'%Y-%m-%d')) AS estimateSaleTime,-->
    <!--        IF( ip.delivery = '0,1','仓库发货+工厂发货',IF(ip.delivery = '0','仓库发货','工厂发货')) AS delivery,-->
    <!--        op.`name` AS mainRunner-->
    <!--        FROM-->
    <!--        ( select id,code,name,category_id,brand_id,provider_id,estimate_sale_time from item-->
    <!--        <where>-->
    <!--            is_del = 0-->
    <!--            &lt;!&ndash;            商品id查询&ndash;&gt;-->
    <!--            <if test="param.itemId != null and param.itemId != '' ">-->
    <!--                and id = #{param.itemId}-->
    <!--            </if>-->
    <!--            &lt;!&ndash;            商品编码查询&ndash;&gt;-->
    <!--            <if test="param.code != null and param.code != ''">-->
    <!--                and code = #{param.code}-->
    <!--            </if>-->
    <!--            &lt;!&ndash;            商品其他系统关联编号查询&ndash;&gt;-->
    <!--            <if test="param.partnerCode != null and param.partnerCode != ''">-->
    <!--                and partner_provider_item_sn = #{param.partnerCode}-->
    <!--            </if>-->
    <!--            &lt;!&ndash;            商品上新时间查询&ndash;&gt;-->
    <!--            <if test="param.shelfStartTime != null and 0 != param.shelfStartTime and param.shelfEndTime != null and 0 != param.shelfEndTime ">-->
    <!--                and ( estimate_sale_time between #{param.shelfStartTime} and #{param.shelfEndTime} )-->
    <!--            </if>-->
    <!--            &lt;!&ndash;            商品名称模糊查询&ndash;&gt;-->
    <!--            <if test="param.itemName != null and param.itemName != ''">-->
    <!--                and `name` like concat('%', #{param.itemName}, '%')-->
    <!--            </if>-->
    <!--        </where>-->
    <!--        ) i-->
    <!--        LEFT JOIN (select * from category where is_del = 0 ) c ON i.category_id = c.id-->
    <!--        LEFT JOIN (select * from brand where is_del = 0 ) b ON i.brand_id = b.id-->
    <!--        LEFT JOIN (select * from provider where is_del = 0 ) p ON i.provider_id = p.id-->
    <!--        LEFT JOIN (select item_id,price from item_price where is_del = 0 and type = 0) procurementPrice on i.id =-->
    <!--        procurementPrice.item_id-->
    <!--        LEFT JOIN (select item_id,price from item_price where is_del = 0 and type = 1) salesPrice on i.id =-->
    <!--        salesPrice.item_id-->
    <!--        LEFT JOIN (select item_id,price from item_price where is_del = 0 and type = 2) linePrice on i.id =-->
    <!--        linePrice.item_id-->
    <!--        LEFT JOIN (select item_id,price from item_price where is_del = 0 and type = 3) activityPrice on i.id =-->
    <!--        activityPrice.item_id-->
    <!--        LEFT JOIN (select item_id,price from item_price where is_del = 0 and type = 4) lowestPrice on i.id =-->
    <!--        lowestPrice.item_id-->
    <!--        LEFT JOIN (select name,item_id from item_operator where is_head = 1 and is_del = 0) op ON i.id = op.item_id-->
    <!--        LEFT JOIN (select item_id,buyer_id,delivery from item_procurement where is_del = 0) ip ON i.id = ip.item_id-->
    <!--        LEFT JOIN (select name,id,user_id from buyer where is_del = 0) buyer ON buyer.id = ip.buyer_id-->
    <!--        <where>-->
    <!--            &lt;!&ndash;            商品品类id查询&ndash;&gt;-->
    <!--            <if test="param.categoryId != null and param.categoryId != '' ">-->
    <!--                and c.id = #{param.categoryId}-->
    <!--            </if>-->
    <!--            &lt;!&ndash;            商品品牌id查询&ndash;&gt;-->
    <!--            <if test="param.brandId != null and param.brandId != '' ">-->
    <!--                and b.id = #{param.brandId}-->
    <!--            </if>-->
    <!--            &lt;!&ndash;            商品品牌名称模糊查询&ndash;&gt;-->
    <!--            <if test="param.brandName != null and param.brandName != ''">-->
    <!--                and b.`name` like "%"{param.brandName}"%"-->
    <!--            </if>-->
    <!--            &lt;!&ndash;            商品采购员id查询&ndash;&gt;-->
    <!--            <if test="param.buyerUserId != null and param.buyerUserId != '' ">-->
    <!--                and buyer.user_id = #{param.buyerUserId}-->
    <!--            </if>-->
    <!--            &lt;!&ndash;            商品供应商id查询&ndash;&gt;-->
    <!--            <if test="param.providerId != null and param.providerId != '' ">-->
    <!--                and i.provider_id = #{param.providerId}-->
    <!--            </if>-->
    <!--        </where>-->
    <!--        order by i.id DESC-->
    <!--    </select>-->

    <!--    <select id="queryExportItemNoPrice"-->
    <!--            resultType="com.daddylab.supplier.item.domain.exportTask.dto.item.ExportItemNoPriceDto">-->
    <!--        SELECT-->
    <!--        i.id AS itemId,-->
    <!--        i.code AS code,-->
    <!--        i.`name` AS `name`,-->
    <!--        c.`path` AS category,-->
    <!--        b.`name` AS brand,-->
    <!--        p.`name` AS `provider`,-->
    <!--        buyer.`name` AS `buyer`,-->
    <!--        IF(i.estimate_sale_time = 0 ,'',from_unixtime(i.estimate_sale_time,'%Y-%m-%d')) AS estimateSaleTime,-->
    <!--        IF( ip.delivery = '0,1','仓库发货+工厂发货',IF(ip.delivery = '0','仓库发货','工厂发货')) AS delivery,-->
    <!--        op.`name` AS mainRunner-->
    <!--        FROM-->
    <!--        ( select * from item-->
    <!--        <where>-->
    <!--            is_del = 0-->
    <!--            &lt;!&ndash;            商品id查询&ndash;&gt;-->
    <!--            <if test="param.itemId != null and param.itemId != '' ">-->
    <!--                and id = #{param.itemId}-->
    <!--            </if>-->
    <!--            &lt;!&ndash;            商品名称模糊查询&ndash;&gt;-->
    <!--            <if test="param.itemName != null and param.itemName != ''">-->
    <!--                and `name` like concat('%', #{param.itemName}, '%')-->
    <!--            </if>-->
    <!--            &lt;!&ndash;            商品编码查询&ndash;&gt;-->
    <!--            <if test="param.code != null and param.code != ''">-->
    <!--                and code = #{param.code}-->
    <!--            </if>-->
    <!--            &lt;!&ndash;            商品其他系统关联编号查询&ndash;&gt;-->
    <!--            <if test="param.partnerCode != null and param.partnerCode != ''">-->
    <!--                and partner_provider_item_sn = #{param.partnerCode}-->
    <!--            </if>-->
    <!--            &lt;!&ndash;            商品上新时间查询&ndash;&gt;-->
    <!--            <if test="param.shelfStartTime != null and 0 != param.shelfStartTime and param.shelfEndTime != null and 0 != param.shelfEndTime ">-->
    <!--                and (estimate_sale_time between #{param.shelfStartTime} and #{param.shelfEndTime} )-->
    <!--            </if>-->
    <!--        </where>-->
    <!--        ) i-->
    <!--        LEFT JOIN (select * from category where is_del = 0 ) c ON i.category_id = c.id-->
    <!--        LEFT JOIN (select * from brand where is_del = 0 ) b ON i.brand_id = b.id-->
    <!--        LEFT JOIN (select * from provider where is_del = 0 ) p ON i.provider_id = p.id-->
    <!--        LEFT JOIN (select name,item_id from item_operator where is_head = 1 and is_del = 0) op ON i.id = op.item_id-->
    <!--        LEFT JOIN (select item_id,buyer_id,delivery from item_procurement where is_del = 0) ip ON i.id = ip.item_id-->
    <!--        LEFT JOIN (select name,id,user_id from buyer where is_del = 0) buyer ON buyer.id = ip.buyer_id-->
    <!--        <where>-->
    <!--            &lt;!&ndash;            商品品类id查询&ndash;&gt;-->
    <!--            <if test="param.categoryId != null and param.categoryId != '' ">-->
    <!--                and c.id = #{param.categoryId}-->
    <!--            </if>-->
    <!--            &lt;!&ndash;            商品品牌id查询&ndash;&gt;-->
    <!--            <if test="param.brandId != null and param.brandId != '' ">-->
    <!--                and b.id = #{param.brandId}-->
    <!--            </if>-->
    <!--            &lt;!&ndash;            商品品牌名称模糊查询&ndash;&gt;-->
    <!--            <if test="param.brandName != null and param.brandName != ''">-->
    <!--                and b.`name` like "%"{param.brandName}"%"-->
    <!--            </if>-->
    <!--            &lt;!&ndash;            商品采购员id查询&ndash;&gt;-->
    <!--            <if test="param.buyerUserId != null and param.buyerUserId != '' ">-->
    <!--                and buyer.user_id = #{param.buyerUserId}-->
    <!--            </if>-->
    <!--            &lt;!&ndash;            商品供应商id查询&ndash;&gt;-->
    <!--            <if test="param.providerId != null and param.providerId != '' ">-->
    <!--                and i.provider_id = #{param.providerId}-->
    <!--            </if>-->
    <!--        </where>-->
    <!--        order by i.id DESC-->
    <!--    </select>-->


    <select id="queryExportSku"
            resultType="com.daddylab.supplier.item.domain.exportTask.dto.item.ExportSkuDto">
        select
        sku.provider_specified_code as designatedCode,
        sku.id AS id,
        sku.item_id as itemId,
        sku.created_uid,
        sku.sku_code AS sku,
        sku.cost_price AS costPrice,
        sku.sale_price AS salePrice,
        sku.platform_commission as platformCommission,
        sku.`contract_sale_price` as contractSalePrice,
        sku.specifications,
        sku.tax_rate as taxRate,
        sku.purchase_tax_rate as purchaseTaxRate,
        sku.goods_type,
        tmp.*
        from
        (select * from item_sku where is_del = 0
        <if test="param.sku != null and param.sku != ''">
            and sku_code = #{param.sku}
        </if>
        ) sku
        right join
        (
        SELECT
        i.id,
        i.`name` AS `name`,
        i.`partner_provider_item_sn` AS `partnerProviderItemSn`,
        i.`business_line` AS `businessLine`,
        i.`business_lines` AS `businessLines`,
        i.`code` AS `code`,
        i.`status` AS `status`,
        i.`down_frame_time`,
        i.`down_frame_reason`,
        b.`name` AS `brand`,
        c.`path` AS `category`,
        p.`name` AS provider,
        buyer.name AS buyer,
        IF( procurement.delivery = '0,1','仓库发货+工厂发货',IF(procurement.delivery = '0','仓库发货','工厂发货')) AS
        delivery
        FROM
        ( select * from item
        <where>
            is_del = 0
            <!--            商品id查询-->
            <if test="param.itemId != null and param.itemId != '' ">
                and id = #{param.itemId}
            </if>
            <!--            商品名称模糊查询-->
            <if test="param.itemName != null and param.itemName != ''">
                and `name` like concat('%', #{param.itemName}, '%')
            </if>
            <!--            商品编码查询-->
            <if test="param.code != null and param.code != ''">
                and `code` = #{param.code}
            </if>
            <!--            商品其他系统关联编号查询-->
            <if test="param.partnerCode != null and param.partnerCode != ''">
                and partner_provider_item_sn = #{param.partnerCode}
            </if>
            <!--            商品上新时间查询-->
            <if test="param.shelfStartTime != null and 0 != param.shelfStartTime and param.shelfEndTime != null and 0 != param.shelfEndTime ">
                and (estimate_sale_time between #{param.shelfStartTime} and #{param.shelfEndTime} )
            </if>
            <!--            <if test="param.businessLine != null and param.businessLine.size() > 0">-->
            <!--                AND-->
            <!--                <foreach item="lineId" index="index" collection="param.businessLine" open="(" separator="OR" close=")">-->
            <!--                    is_business_line${lineId} = 1-->
            <!--                </foreach>-->
            <!--            </if>-->

            <if test="param.offShelfStartTime != null and 0 != param.offShelfStartTime and
                param.offShelfEndTime != null and 0 != param.offShelfEndTime ">
                and (down_frame_time between #{param.offShelfStartTime} and #{param.offShelfEndTime} )
            </if>
        </where>
        ) i
        LEFT JOIN ( SELECT id,name FROM brand WHERE is_del = 0 ) b ON i.brand_id = b.id
        LEFT JOIN ( SELECT id,name,path FROM category WHERE is_del = 0 ) c ON i.category_id = c.id
        LEFT JOIN ( SELECT id, NAME FROM provider WHERE is_del = 0 ) p ON i.provider_id = p.id
        LEFT JOIN (SELECT item_id,buyer_id,delivery FROM item_procurement WHERE is_del = 0 ) procurement ON i.id =
        procurement.item_id
        LEFT JOIN (SELECT id,name,user_id FROM buyer WHERE is_del = 0 ) buyer ON buyer.id = procurement.buyer_id
        <where>
            <!--            商品品类id查询-->
            <if test="param.categoryId != null and param.categoryId != '' ">
                c.id = #{param.categoryId}
            </if>
            <!--            商品品牌id查询-->
            <if test="param.brandId != null and param.brandId != '' ">
                and b.id = #{param.brandId}
            </if>
            <!--            商品品牌名称模糊查询-->
            <if test="param.brandName != null and param.brandName != ''">
                and b.`name` like concat('%', #{param.brandName}, '%')
            </if>
            <!--            商品采购员id查询-->
            <if test="param.buyerUserId != null and param.buyerUserId != '' ">
                and buyer.user_id = #{param.buyerUserId}
            </if>
            <!--            商品供应商id查询-->
            <if test="param.providerId != null and param.providerId != '' ">
                and i.provider_id = #{param.providerId}
            </if>
        </where>
        ) tmp on tmp.id = sku.item_id WHERE sku.id is not null
        order by sku.id desc
        limit #{param.offsetVal},#{param.size}
    </select>

    <select id="queryExportSkuNoPrice"
            resultType="com.daddylab.supplier.item.domain.exportTask.dto.item.ExportSkuNoPriceDto">
        select
        sku.id AS id,
        sku.sku_code AS sku,
        tmp.*
        from
        (select * from item_sku where is_del = 0
        <if test="param.sku != null and param.sku != ''">
            and sku_code = #{param.sku}
        </if>
        ) sku
        right join
        (
        SELECT
        i.id,
        i.`name` AS `name`,
        i.`code` AS `code`,
        b.`name` AS `brand`,
        c.`path` AS `category`,
        p.`name` AS provider,
        buyer.name AS `buyer`,
        IF( procurement.delivery = '0,1','仓库发货+工厂发货',IF(procurement.delivery = '0','仓库发货','工厂发货')) AS
        delivery
        FROM
        ( select * from item
        <where>
            is_del = 0
            <!--            商品id查询-->
            <if test="param.itemId != null and param.itemId != '' ">
                and id = #{param.itemId}
            </if>
            <!--            商品名称模糊查询-->
            <if test="param.itemName != null and param.itemName != ''">
                and `name` like concat('%', #{param.itemName}, '%')
            </if>
            <!--            商品编码查询-->
            <if test="param.code != null and param.code != ''">
                and `code` = #{param.code}
            </if>
            <!--            商品其他系统关联编号查询-->
            <if test="param.partnerCode != null and param.partnerCode != ''">
                and partner_provider_item_sn = #{param.partnerCode}
            </if>
            <!--            商品上新时间查询-->
            <if test="param.shelfStartTime != null and 0 != param.shelfStartTime and param.shelfEndTime != null and 0 != param.shelfEndTime ">
                and (estimate_sale_time between #{param.shelfStartTime} and #{param.shelfEndTime} )
            </if>
        </where>
        ) i
        LEFT JOIN ( SELECT id,name FROM brand WHERE is_del = 0 ) b ON i.brand_id = b.id
        LEFT JOIN ( SELECT id,name,path FROM category WHERE is_del = 0 ) c ON i.category_id = c.id
        LEFT JOIN ( SELECT id, NAME FROM provider WHERE is_del = 0 ) p ON i.provider_id = p.id
        LEFT JOIN (SELECT item_id,buyer_id,delivery FROM item_procurement WHERE is_del = 0 ) procurement ON i.id =
        procurement.item_id
        LEFT JOIN (SELECT id,name,user_id FROM buyer WHERE is_del = 0 ) buyer ON buyer.id = procurement.buyer_id
        <where>
            <!--            商品品类id查询-->
            <if test="param.categoryId != null and param.categoryId != '' ">
                c.id = #{param.categoryId}
            </if>
            <!--            商品品牌id查询-->
            <if test="param.brandId != null and param.brandId != '' ">
                and b.id = #{param.brandId}
            </if>
            <!--            商品品牌名称模糊查询-->
            <if test="param.brandName != null and param.brandName != ''">
                and b.`name` like concat('%', #{param.brandName}, '%')
            </if>
            <!--            商品采购员id查询-->
            <if test="param.buyerUserId != null and param.buyerUserId != '' ">
                and buyer.user_id = #{param.buyerUserId}
            </if>
            <!--            商品供应商id查询-->
            <if test="param.providerId != null and param.providerId != '' ">
                and i.provider_id = #{param.providerId}
            </if>
        </where>
        ) tmp on tmp.id = sku.item_id where sku.id is not null
        order by sku.id desc
        limit #{param.offsetVal},#{param.size}
    </select>

    <select id="queryExportItem"
            resultType="com.daddylab.supplier.item.domain.exportTask.dto.item.ExportItemDto">
        SELECT
        i.id AS itemId,
        i.partner_provider_item_sn as partnerProviderItemSn,
        <!--        i.business_line as businessLine,-->
        i.status as status,
        i.created_uid AS createdUid,
        i.code AS code,
        i.`down_frame_time`,
        i.`down_frame_reason`,
        i.`name` AS `name`,
        c.`path` AS category,
        b.`name` AS brand,
        procurementPrice.price AS `procurement`,
        salesPrice.`price` AS salesPrice,
        linePrice.`price` AS `linePrice`,
        activityPrice.`price` AS `activityPrice`,
        lowestPrice.`price` AS `lowestPrice`,
        p.`name` AS `provider`,
        buyer.`name` AS `buyer`,
        IF(i.estimate_sale_time = 0 ,'',from_unixtime(i.estimate_sale_time,'%Y-%m-%d')) AS estimateSaleTime,
        IF( ip.delivery = '0,1','仓库发货+工厂发货',IF(ip.delivery = '0','仓库发货','工厂发货')) AS delivery,
        op.`name` AS mainRunner,
        os1.otherSales AS otherPrice,
        os2.otherProcurement AS otherProcurement,
        os3.otherRunner AS otherRunner,
        skuCountTmp.skuCount,
        ip.rate,
        ip.purchase_rate as purchaseRate
        FROM
        ( select id,
        created_uid,
        code,
        name,
        category_id,
        brand_id,
        provider_id,
        estimate_sale_time,
        partner_provider_item_sn,
        status,
        business_line,
        down_frame_time,
        down_frame_reason
        from item
        <where>
            is_del = 0
            <!--            商品id查询-->
            <if test="param.itemId != null and param.itemId != '' ">
                and id = #{param.itemId}
            </if>
            <!--            商品编码查询-->
            <if test="param.code != null and param.code != ''">
                and code = #{param.code}
            </if>
            <!--            商品其他系统关联编号查询-->
            <if test="param.partnerCode != null and param.partnerCode != ''">
                and partner_provider_item_sn = #{param.partnerCode}
            </if>
            <!--            商品上新时间查询-->
            <if test="param.shelfStartTime != null and 0 != param.shelfStartTime and param.shelfEndTime != null and 0 != param.shelfEndTime ">
                and ( estimate_sale_time between #{param.shelfStartTime} and #{param.shelfEndTime} )
            </if>
            <!--            商品名称模糊查询-->
            <if test="param.itemName != null and param.itemName != ''">
                and `name` like concat('%', #{param.itemName}, '%')
            </if>

            <if test="param.offShelfStartTime != null and 0 != param.offShelfStartTime and
            param.offShelfEndTime != null and 0 != param.offShelfEndTime ">
                and (down_frame_time between #{param.offShelfStartTime} and #{param.offShelfEndTime} )
            </if>
            <!--            <if test="param.businessLine != null and param.businessLine.size() > 0">-->
            <!--                AND-->
            <!--                <foreach item="lineId" index="index" collection="param.businessLine" open="(" separator="OR" close=")">-->
            <!--                    is_business_line${lineId} = 1-->
            <!--                </foreach>-->
            <!--            </if>-->
        </where>
        ) i
        LEFT JOIN (select count(1) as skuCount,item_id from item_sku where is_del = 0 group by item_id ) skuCountTmp on
        skuCountTmp.item_id = i.id
        LEFT JOIN (select * from category where is_del = 0 ) c ON i.category_id = c.id
        LEFT JOIN (select * from brand where is_del = 0 ) b ON i.brand_id = b.id
        LEFT JOIN (select * from provider where is_del = 0 ) p ON i.provider_id = p.id

        <!-- 采购价格 -->
        LEFT JOIN item_price procurementPrice ON i.id = procurementPrice.item_id
        AND procurementPrice.is_del = 0
        AND procurementPrice.type = 0
        AND procurementPrice.id = (
        SELECT MAX(id)
        FROM item_price
        WHERE item_id = i.id
        AND is_del = 0
        AND type = 0
        AND custom_name = '采购成本'
        )

        <!-- 销售价格 -->
        LEFT JOIN item_price salesPrice ON i.id = salesPrice.item_id
        AND salesPrice.is_del = 0
        AND salesPrice.type = 1
        AND salesPrice.start_time = (
        SELECT MAX(start_time)
        FROM item_price
        WHERE item_id = i.id
        AND is_del = 0
        AND type = 1
        )

        <!-- 划线价格 -->
        LEFT JOIN item_price linePrice ON i.id = linePrice.item_id
        AND linePrice.is_del = 0
        AND linePrice.type = 2
        AND linePrice.id = (
        SELECT MAX(id)
        FROM item_price
        WHERE item_id = i.id
        AND is_del = 0
        AND type = 2
        )

        <!-- 活动价格 -->
        LEFT JOIN item_price activityPrice ON i.id = activityPrice.item_id
        AND activityPrice.is_del = 0
        AND activityPrice.type = 3
        AND activityPrice.id = (
        SELECT MAX(id)
        FROM item_price
        WHERE item_id = i.id
        AND is_del = 0
        AND type = 3
        )

        <!-- 最低价格 -->
        LEFT JOIN item_price lowestPrice ON i.id = lowestPrice.item_id
        AND lowestPrice.is_del = 0
        AND lowestPrice.type = 4
        AND lowestPrice.id = (
        SELECT MAX(id)
        FROM item_price
        WHERE item_id = i.id
        AND is_del = 0
        AND type = 4
        )

        LEFT JOIN (select name,item_id from item_operator where is_head = 1 and is_del = 0) op ON i.id = op.item_id
        LEFT JOIN (select item_id,buyer_id,delivery,rate,purchase_rate from item_procurement where is_del = 0) ip ON
        i.id = ip.item_id
        LEFT JOIN (select name,id,user_id from buyer where is_del = 0) buyer ON buyer.id = ip.buyer_id
        LEFT JOIN
        (
        SELECT IFNULL(GROUP_CONCAT(tmp.otherSales), '') AS otherSales,tmp.item_id
        FROM (
        SELECT CONCAT_WS(
        ",",
        CONCAT_WS('-', custom_name, CONCAT_WS('',price,'元'),if(start_time != 0 and end_time != 0,
        CONCAT_WS('至',if(start_time = 0,'',from_unixtime(start_time,'%Y%m%d')),
        if(end_time = 0,'',from_unixtime(end_time,'%Y%m%d'))),''))
        ) AS otherSales,
        item_id as item_id
        FROM (
        SELECT item_id, custom_name, price, start_time, end_time FROM item_price WHERE is_del = 0 AND type = 5 AND rang
        = 2
        ) tmp
        ) tmp
        group by tmp.item_id
        ) os1 ON i.id = os1.item_id
        LEFT JOIN
        (
        SELECT IFNULL(GROUP_CONCAT(tmp.otherProcurement), '') AS otherProcurement, tmp.item_id
        FROM (
        SELECT CONCAT_WS(
        ",",
        CONCAT_WS('-', custom_name, CONCAT_WS('',price,'元'),if(start_time != 0 and end_time != 0,
        CONCAT_WS('至',if(start_time = 0,'',from_unixtime(start_time,'%Y%m%d')),
        if(end_time = 0,'',from_unixtime(end_time,'%Y%m%d'))),''))
        ) AS otherProcurement,
        item_id as item_id
        FROM (
        SELECT item_id, custom_name, price, start_time, end_time FROM item_price WHERE type = 5 AND is_del = 0 AND rang
        = 1
        ) tmp
        ) tmp
        group by tmp.item_id
        ) os2 ON i.id = os2.item_id
        LEFT JOIN
        (
        SELECT IFNULL(GROUP_CONCAT(tmp.otherRunner), '') AS otherRunner, tmp.item_id AS item_id
        FROM (
        SELECT
        CONCAT_WS('-', custom_name, `name`) AS otherRunner, item_id AS item_id
        FROM (
        SELECT custom_name, `name` ,item_id FROM item_operator WHERE is_del = 0 AND is_head = 0
        ) tmp
        ) tmp
        group by tmp.item_id
        ) os3 ON i.id = os3.item_id
        <where>
            <!--            商品品类id查询-->
            <if test="param.categoryId != null and param.categoryId != '' ">
                and c.id = #{param.categoryId}
            </if>
            <!--            商品品牌id查询-->
            <if test="param.brandId != null and param.brandId != '' ">
                and b.id = #{param.brandId}
            </if>
            <!--            商品品牌名称模糊查询-->
            <if test="param.brandName != null and param.brandName != ''">
                and b.`name` like "%"{param.brandName}"%"
            </if>
            <!--            商品采购员id查询-->
            <if test="param.buyerUserId != null and param.buyerUserId != '' ">
                and buyer.user_id = #{param.buyerUserId}
            </if>
            <!--            商品供应商id查询-->
            <if test="param.providerId != null and param.providerId != '' ">
                and i.provider_id = #{param.providerId}
            </if>
        </where>
        order by i.id DESC
        limit #{param.offsetVal},#{param.size}
    </select>

    <select id="queryExportItemNoPrice"
            resultType="com.daddylab.supplier.item.domain.exportTask.dto.item.ExportItemNoPriceDto">
        SELECT
        i.id AS itemId,
        i.code AS code,
        i.`name` AS `name`,
        c.`path` AS category,
        b.`name` AS brand,
        p.`name` AS `provider`,
        buyer.`name` AS `buyer`,
        IF(i.estimate_sale_time = 0 ,'',from_unixtime(i.estimate_sale_time,'%Y-%m-%d')) AS estimateSaleTime,
        IF( ip.delivery = '0,1','仓库发货+工厂发货',IF(ip.delivery = '0','仓库发货','工厂发货')) AS delivery,
        op.`name` AS mainRunner,
        <!--            os1.otherSales AS otherSales,-->
        <!--            os2.otherProcurement AS otherProcurement,-->
        os3.otherRunner AS otherRunner,
        skuCountTmp.skuCount
        FROM
        ( select * from item
        <where>
            is_del = 0
            <!--            商品id查询-->
            <if test="param.itemId != null and param.itemId != '' ">
                and id = #{param.itemId}
            </if>
            <!--            商品名称模糊查询-->
            <if test="param.itemName != null and param.itemName != ''">
                and `name` like concat('%', #{param.itemName}, '%')
            </if>
            <!--            商品编码查询-->
            <if test="param.code != null and param.code != ''">
                and code = #{param.code}
            </if>
            <!--            商品其他系统关联编号查询-->
            <if test="param.partnerCode != null and param.partnerCode != ''">
                and partner_provider_item_sn = #{param.partnerCode}
            </if>
            <!--            商品上新时间查询-->
            <if test="param.shelfStartTime != null and 0 != param.shelfStartTime and param.shelfEndTime != null and 0 != param.shelfEndTime ">
                and (estimate_sale_time between #{param.shelfStartTime} and #{param.shelfEndTime} )
            </if>
        </where>
        ) i
        LEFT JOIN (select count(1) as skuCount,item_id from item_sku where is_del = 0 group by item_id ) skuCountTmp on
        skuCountTmp.item_id = i.id
        LEFT JOIN (select * from category where is_del = 0 ) c ON i.category_id = c.id
        LEFT JOIN (select * from brand where is_del = 0 ) b ON i.brand_id = b.id
        LEFT JOIN (select * from provider where is_del = 0 ) p ON i.provider_id = p.id
        LEFT JOIN (select name,item_id from item_operator where is_head = 1 and is_del = 0) op ON i.id = op.item_id
        LEFT JOIN (select item_id,buyer_id,delivery from item_procurement where is_del = 0) ip ON i.id = ip.item_id
        LEFT JOIN (select name,id,user_id from buyer where is_del = 0) buyer ON buyer.id = ip.buyer_id
        <!--        LEFT JOIN (-->
        <!--                    SELECT IFNULL(GROUP_CONCAT(tmp.otherSales), '') AS otherSales,tmp.item_id-->
        <!--                    FROM (-->
        <!--                        SELECT CONCAT_WS('-', custom_name, price) AS otherSales,-->
        <!--                            item_id                            as item_id-->
        <!--                            FROM (-->
        <!--                                SELECT item_id, custom_name, price FROM item_price WHERE is_del = 0 AND type = 5 AND rang = 2-->
        <!--                            ) tmp-->
        <!--                        ) tmp-->
        <!--                        erpGroup by tmp.item_id-->
        <!--                    )  os1 ON i.id = os1.item_id-->
        <!--        LEFT JOIN-->
        <!--                (-->
        <!--                SELECT IFNULL(GROUP_CONCAT(tmp.otherProcurement), '') AS otherProcurement, tmp.item_id-->
        <!--                FROM (-->
        <!--                    SELECT CONCAT_WS('-', custom_name, price) AS otherProcurement, item_id as item_id-->
        <!--                        FROM (-->
        <!--                            SELECT item_id, custom_name, price FROM item_price WHERE type = 5 AND is_del = 0 AND rang = 1-->
        <!--                        ) tmp-->
        <!--                    ) tmp-->
        <!--                    erpGroup by tmp.item_id-->
        <!--                )  os2 ON i.id = os2.item_id-->
        LEFT JOIN
        (
        SELECT IFNULL(GROUP_CONCAT(tmp.otherRunner), '') AS otherRunner, tmp.item_id AS item_id
        FROM (
        SELECT
        CONCAT_WS('-', custom_name, `name`) AS otherRunner, item_id AS item_id
        FROM (
        SELECT custom_name, `name` ,item_id FROM item_operator WHERE is_del = 0 AND is_head = 0
        ) tmp
        ) tmp
        group by tmp.item_id
        ) os3 ON i.id = os3.item_id
        <where>
            <!--            商品品类id查询-->
            <if test="param.categoryId != null and param.categoryId != '' ">
                and c.id = #{param.categoryId}
            </if>
            <!--            商品品牌id查询-->
            <if test="param.brandId != null and param.brandId != '' ">
                and b.id = #{param.brandId}
            </if>
            <!--            商品品牌名称模糊查询-->
            <if test="param.brandName != null and param.brandName != ''">
                and b.`name` like "%"{param.brandName}"%"
            </if>
            <!--            商品采购员id查询-->
            <if test="param.buyerUserId != null and param.buyerUserId != '' ">
                and buyer.user_id = #{param.buyerUserId}
            </if>
            <!--            商品供应商id查询-->
            <if test="param.providerId != null and param.providerId != '' ">
                and i.provider_id = #{param.providerId}
            </if>
        </where>
        order by i.id DESC
        limit #{param.offsetVal},#{param.size}
    </select>

    <select id="countExportItem" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        ( select id,code,name,category_id,brand_id,provider_id,estimate_sale_time from item
        <where>
            is_del = 0
            <!--            商品id查询-->
            <if test="param.itemId != null and param.itemId != '' ">
                and id = #{param.itemId}
            </if>
            <!--            商品编码查询-->
            <if test="param.code != null and param.code != ''">
                and code = #{param.code}
            </if>
            <!--            商品其他系统关联编号查询-->
            <if test="param.partnerCode != null and param.partnerCode != ''">
                and partner_provider_item_sn = #{param.partnerCode}
            </if>
            <!--            商品上新时间查询-->
            <if test="param.shelfStartTime != null and 0 != param.shelfStartTime and param.shelfEndTime != null and 0 != param.shelfEndTime ">
                and ( estimate_sale_time between #{param.shelfStartTime} and #{param.shelfEndTime} )
            </if>
            <!--            商品名称模糊查询-->
            <if test="param.itemName != null and param.itemName != ''">
                and `name` like concat('%', #{param.itemName}, '%')
            </if>
        </where>
        ) i
        LEFT JOIN (select * from category where is_del = 0 ) c ON i.category_id = c.id
        LEFT JOIN (select * from brand where is_del = 0 ) b ON i.brand_id = b.id
        LEFT JOIN (select * from provider where is_del = 0 ) p ON i.provider_id = p.id
        LEFT JOIN (select item_id,price from item_price where is_del = 0 and type = 0) procurementPrice on i.id =
        procurementPrice.item_id
        LEFT JOIN (select item_id,price from item_price where is_del = 0 and type = 1) salesPrice on i.id =
        salesPrice.item_id
        LEFT JOIN (select item_id,price from item_price where is_del = 0 and type = 2) linePrice on i.id =
        linePrice.item_id
        LEFT JOIN (select item_id,price from item_price where is_del = 0 and type = 3) activityPrice on i.id =
        activityPrice.item_id
        LEFT JOIN (select item_id,price from item_price where is_del = 0 and type = 4) lowestPrice on i.id =
        lowestPrice.item_id
        LEFT JOIN (select name,item_id from item_operator where is_head = 1 and is_del = 0) op ON i.id = op.item_id
        LEFT JOIN (select item_id,buyer_id,delivery from item_procurement where is_del = 0) ip ON i.id = ip.item_id
        LEFT JOIN (select name,id,user_id from buyer where is_del = 0) buyer ON buyer.id = ip.buyer_id
        <where>
            <!--            商品品类id查询-->
            <if test="param.categoryId != null and param.categoryId != '' ">
                and c.id = #{param.categoryId}
            </if>
            <!--            商品品牌id查询-->
            <if test="param.brandId != null and param.brandId != '' ">
                and b.id = #{param.brandId}
            </if>
            <!--            商品品牌名称模糊查询-->
            <if test="param.brandName != null and param.brandName != ''">
                and b.`name` like "%"{param.brandName}"%"
            </if>
            <!--            商品采购员id查询-->
            <if test="param.buyerUserId != null and param.buyerUserId != '' ">
                and buyer.user_id = #{param.buyerUserId}
            </if>
            <!--            商品供应商id查询-->
            <if test="param.providerId != null and param.providerId != '' ">
                and i.provider_id = #{param.providerId}
            </if>
        </where>
    </select>
    <select id="countExportItemNoPrice" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        ( select * from item
        <where>
            is_del = 0
            <!--            商品id查询-->
            <if test="param.itemId != null and param.itemId != '' ">
                and id = #{param.itemId}
            </if>
            <!--            商品名称模糊查询-->
            <if test="param.itemName != null and param.itemName != ''">
                and `name` like concat('%', #{param.itemName}, '%')
            </if>
            <!--            商品编码查询-->
            <if test="param.code != null and param.code != ''">
                and code = #{param.code}
            </if>
            <!--            商品其他系统关联编号查询-->
            <if test="param.partnerCode != null and param.partnerCode != ''">
                and partner_provider_item_sn = #{param.partnerCode}
            </if>
            <!--            商品上新时间查询-->
            <if test="param.shelfStartTime != null and 0 != param.shelfStartTime and param.shelfEndTime != null and 0 != param.shelfEndTime ">
                and (estimate_sale_time between #{param.shelfStartTime} and #{param.shelfEndTime} )
            </if>
        </where>
        ) i
        LEFT JOIN (select * from category where is_del = 0 ) c ON i.category_id = c.id
        LEFT JOIN (select * from brand where is_del = 0 ) b ON i.brand_id = b.id
        LEFT JOIN (select * from provider where is_del = 0 ) p ON i.provider_id = p.id
        LEFT JOIN (select name,item_id from item_operator where is_head = 1 and is_del = 0) op ON i.id = op.item_id
        LEFT JOIN (select item_id,buyer_id,delivery from item_procurement where is_del = 0) ip ON i.id = ip.item_id
        LEFT JOIN (select name,id,user_id from buyer where is_del = 0) buyer ON buyer.id = ip.buyer_id
        <where>
            <!--            商品品类id查询-->
            <if test="param.categoryId != null and param.categoryId != '' ">
                and c.id = #{param.categoryId}
            </if>
            <!--            商品品牌id查询-->
            <if test="param.brandId != null and param.brandId != '' ">
                and b.id = #{param.brandId}
            </if>
            <!--            商品品牌名称模糊查询-->
            <if test="param.brandName != null and param.brandName != ''">
                and b.`name` like "%"{param.brandName}"%"
            </if>
            <!--            商品采购员id查询-->
            <if test="param.buyerUserId != null and param.buyerUserId != '' ">
                and buyer.user_id = #{param.buyerUserId}
            </if>
            <!--            商品供应商id查询-->
            <if test="param.providerId != null and param.providerId != '' ">
                and i.provider_id = #{param.providerId}
            </if>
        </where>
    </select>

    <select id="getPurchaseBySku"
            resultType="com.daddylab.supplier.item.domain.purchase.dto.PurchaseItem">
        select isku.item_id              AS itemId,
               i.code                    AS itemCode,
               i.provider_specified_code as providerSpecifiedCode,
               i.code                    as spuCode,
               i.name                    AS itemName,
               p.id                      AS providerId,
               p.name                    AS providerName,
               b.user_id                 AS buyerId,
               b.name                    AS buyerName,
               ip.delivery               AS delivery,
               i.business_line           AS businessLine
        from `item_sku` isku
                 LEFT JOIN item i on i.id = isku.item_id
                 LEFT JOIN item_procurement ip ON ip.item_id = isku.item_id
                 LEFT JOIN provider p ON p.id = ip.provider_id
                 LEFT JOIN buyer b ON b.id = ip.buyer_id
        WHERE isku.sku_code = #{skuCode}
    </select>

    <select id="getNewGoodsById"
            resultType="com.daddylab.supplier.item.application.saleItem.vo.NewGoodsVo">
        SELECT i.name               as name,
               i.estimate_sale_time as shelfTime,
               c.name               as categoryName,
               b.name               as brandName,
               iet.from_area        as shipmentArea,
               iet.express_company  as logistics,
               iet.area             as expressTemplate,
               io.name              as producerName,
               ip.delivery          as shipmentType,
               bu.user_id           as buyerId
        FROM item i
                 LEFT JOIN item_express_template iet ON iet.item_id = i.id
                 LEFT JOIN category c ON c.id = i.category_id
                 LEFT JOIN brand b ON b.id = i.brand_id
                 LEFT JOIN item_operator io ON io.item_id = i.id
                 LEFT JOIN item_procurement ip ON ip.item_id = i.id
                 LEFT JOIN buyer bu ON bu.id = ip.buyer_id
        WHERE i.id = #{id}
          AND i.is_del = 0
        limit 1
    </select>

    <select id="selectBatchBuyersByItemIds"
            resultType="com.daddylab.supplier.item.controller.item.dto.ItemBuyerDto">
        select
        t1.item_id as itemId,
        t1.qc_ids as qcUserIds,
        t2.user_id as buyerUserId,
        t2.`name` as buyerUserName
        from
        item_procurement as t1
        inner join buyer as t2 on (t1.buyer_id = t2.id)
        where t1.item_id in
        <foreach item="itemId" index="index" collection="itemIds"
                 open="(" separator="," close=")">
            #{itemId}
        </foreach>
        and t1.is_del = 0
    </select>

    <select id="selectBatchDtosByItemIds"
            resultType="com.daddylab.supplier.item.controller.item.dto.ItemWithLaunchPlanDto">
        select
        t1.id as itemId,
        t1.business_line,
        t1.name as itemName,
        t2.path as categoryPath
        from
        item as t1
        left join category as t2 on (t1.category_id = t2.id)
        where t1.id in
        <foreach item="itemId" index="index" collection="itemIds"
                 open="(" separator="," close=")">
            #{itemId}
        </foreach>
        and t1.is_del = 0
    </select>

    <select id="selectBatchPrincipalUsersByItemIds"
            resultType="com.daddylab.supplier.item.controller.item.dto.ItemPrincipalDto">
        select
        principal_id as principalUserId,
        item_id as itemId
        from
        new_goods
        where item_id in
        <foreach item="itemId" index="index" collection="itemIds"
                 open="(" separator="," close=")">
            #{itemId}
        </foreach>
        and is_del = 0
    </select>

    <select id="selectItemBaseInfo"
            resultType="com.daddylab.supplier.item.controller.item.dto.ItemSkuDetailVo">
        SELECT t1.id            AS itemId,
               t1.`code`        AS itemNo,
               t2.standard_name AS itemStandardName
        FROM item AS t1
                 LEFT JOIN item_drawer AS t2 ON (t1.id = t2.item_id AND t2.is_del = 0)
        WHERE t1.is_del = 0
          AND t1.id = #{itemId}
        LIMIT 1
    </select>

    <select id="selectSkuDetails"
            resultType="com.daddylab.supplier.item.controller.item.dto.ItemSkuDetailVo$SkuDetail">
        SELECT t1.sku_code       AS itemSkuNo,
               t1.id             AS itemSkuId,
               t1.specifications AS itemSkuName,
               t2.daily_price    AS dailyPrice,
               t2.active_price   AS activePrice,
               t1.cost_price     AS costPrice
        FROM item_sku AS t1
                 LEFT JOIN new_goods AS t2 ON (t1.sku_code = t2.sku_code AND t2.is_del = 0)
        WHERE t1.is_del = 0
          AND t1.item_id = #{itemId}
    </select>

    <select id="getItemCategoryPath" resultType="java.lang.String">
        SELECT t2.path
        FROM item AS t1
                 INNER JOIN category AS t2 ON (t1.category_id = t2.id)
        WHERE t1.code = #{itemNo}
          AND t1.is_del = 0
          AND t2.is_del = 0
    </select>

    <select id="selectItemAttrDtosByItemId"
            resultType="com.daddylab.supplier.item.controller.item.dto.AttrDto">
        SELECT t2.`name`     AS `key`,
               t1.attr_value AS `value`,
               t3.url        AS imageUrl,
               t3.filename   AS imageFileName
        FROM item_attr AS t1
                 INNER JOIN category_attr AS t2 ON (t1.attr_id = t2.id)
                 LEFT JOIN item_drawer_image AS t3 ON (t1.id = t3.item_attr_id AND t3.is_del = 0 AND t3.type = 5)
        where t1.item_id = #{id}
          AND t1.is_del = 0
          AND t2.is_del = 0
    </select>

    <select id="selectIdModelByCodeBatchIncludeDeleted"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item">
        SELECT id, code, `provider_specified_code`, `is_del`
        FROM `item` WHERE `code` IN
        <foreach item="item" index="index" collection="collection" open="(" separator="," close=")">
            #{item}
        </foreach>
        OR `provider_specified_code` IN
        <foreach item="item" index="index" collection="collection" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectSkuIdModelByCodeBatchIncludeDeleted"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku">
        SELECT id, item_id, sku_code, `provider_specified_code`, `is_del`
        FROM `item_sku` WHERE `sku_code` IN
        <foreach item="item" index="index" collection="collection" open="(" separator="," close=")">
            #{item}
        </foreach>
        OR `provider_specified_code` IN
        <foreach item="item" index="index" collection="collection" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectSkuByCodeBatchIncludeDeleted"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku">
        SELECT *
        FROM `item_sku` WHERE `sku_code` IN
        <foreach item="item" index="index" collection="collection" open="(" separator="," close=")">
            #{item}
        </foreach>
        OR `provider_specified_code` IN
        <foreach item="item" index="index" collection="collection" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectItemInfoForSalesStockBizDO"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemInfoForSalesStockBizDO">
        select category.name as `category`,
        brand.name as `brand`,
        item.name as `itemName`,
        item.code as `itemCode`,
        img.image_url as imgUrl
        from item item
        left join (select name, id from category where is_del = 0) category on item.category_id = category.id
        left join (select name, id from brand where is_del = 0) brand on item.brand_id = brand.id
        left join (select item_id, image_url from item_image where is_del = 0 and type = 1 and is_main = 1) img
        on img.item_id = item.id
        where item.code in
        <foreach collection="itemCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>
    <select id="getItemBuyerAllocStatByBuyerIds"
            resultType="com.daddylab.supplier.item.types.item.ItemBuyerAllocStat">
        select b.user_id as userId, i.business_line as businessLine, count(i.id) as itemCount
        from item as i, item_procurement as ip, buyer as b where i.id = ip.item_id and ip.buyer_id = b.id
        and i.is_del = 0
        and b.user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        group by b.user_id, i.business_line
    </select>

    <update id="deleteInvalidItemAttrs">
        UPDATE `item_attr` `attr` JOIN (SELECT `attr`.`id`, ANY_VALUE(`attr`.`attr_value`) AS `attr_value`
                                        FROM `item_attr` `attr`
                                                 LEFT JOIN `item_sku_attr_ref` `attr_ref`
                                                           ON `attr_ref`.`item_id` = `attr`.`item_id` AND
                                                              `attr_ref`.`item_attr_id` = `attr`.`id` AND
                                                              `attr_ref`.`is_del` = 0
                                        WHERE `attr`.`is_del` = 0
                                          AND `attr`.`item_id` = #{itemId}
                                        GROUP BY `attr`.`id`
                                        HAVING COUNT(`attr_ref`.`id`) = 0) `t1` ON `t1`.`id` = `attr`.`id`
        SET `is_del`     = 1,
            `deleted_at` = UNIX_TIMESTAMP()
        WHERE `attr`.`is_del` = 0
          AND `attr`.`item_id` = #{itemId}
        ;
    </update>

    <select id="staticsItemToBeImprove"
            resultType="com.daddylab.supplier.item.application.message.wechat.v2.statics.QwMsgStaticsBo">
        select bb.user_id as userId, GROUP_CONCAT(ii.id) as itemIds
        from item ii
                 left join item_procurement ip on ii.id = ip.item_id
                 left join buyer bb on ip.buyer_id = bb.id
        where ii.launch_status = 2
          and ii.is_del = 0
          and ip.is_del = 0
          and bb.is_del = 0
        group by bb.user_id;
    </select>

    <select id="staticsItemToBeDesign"
            resultType="com.daddylab.supplier.item.application.message.wechat.v2.statics.QwMsgStaticsBo">
        select principal_id as userId, GROUP_CONCAT(ii.id) as itemIds
        from item ii
                 left join new_goods ng on ii.id = ng.item_id
        where ii.launch_status = 3
          and ii.is_del = 0
          and ng.is_del = 0
        group by ng.principal_id;
    </select>

    <select id="staticsItemToBeLegalAudit"
            resultType="com.daddylab.supplier.item.application.message.wechat.v2.statics.QwMsgStaticsBo">
        select ng.legal_id as userId, GROUP_CONCAT(ii.id) as itemIds
        from item ii
                 left join new_goods ng on ii.id = ng.item_id
        where ii.audit_status in (0, 5)
          and ii.launch_status = 4
          and ii.is_del = 0
          and ng.is_del = 0
        group by ng.legal_id;
    </select>

    <select id="staticsItemToBeQcAudit"
            resultType="com.daddylab.supplier.item.application.message.wechat.v2.statics.QwMsgQcStaticsBo">
        select ip.qc_ids as userIds, GROUP_CONCAT(ii.id) as itemIds
        from item ii
                 left join item_procurement ip on ii.id = ip.item_id
        where ii.audit_status = 10
          and ii.launch_status = 4
          and ii.is_del = 0
          and ip.is_del = 0
        group by ip.qc_ids;
    </select>

    <select id="staticsItemToBeEdit"
            resultType="com.daddylab.supplier.item.application.message.wechat.v2.statics.QwMsgStaticsBo">
        select ng.principal_id as userId, GROUP_CONCAT(ii.id) as itemIds
        from item ii
                 left join new_goods ng on ii.id = ng.item_id
        where ii.launch_status = 5
          and ii.is_del = 0
          and ng.is_del = 0
        group by ng.principal_id;
    </select>

    <select id="staticsItemLegalByItemIds"
            resultType="com.daddylab.supplier.item.application.message.wechat.v2.statics.QwMsgStaticsBo">
        select ng.legal_id as userId ,GROUP_CONCAT(ii.id) as itemIds
        from item ii
        left join new_goods ng on ii.id = ng.item_id
        where
        ii.id in
        <foreach collection="itemIds" index="itemId" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
        and ii.is_del = 0
        and ng.is_del = 0
        group by ng.legal_id;
    </select>

    <select id="staticsItemQcByItemIds"
            resultType="com.daddylab.supplier.item.application.message.wechat.v2.statics.QwMsgQcStaticsBo">
        select ip.qc_ids as userIds, GROUP_CONCAT(ii.id) as itemIds
        from item ii
        left join item_procurement ip on ii.id = ip.item_id
        where
        ii.id in
        <foreach collection="itemIds" index="itemId" item="itemId" open="(" separator="," close=")">
            #{itemId}
        </foreach>
        and ii.is_del = 0
        and ip.is_del = 0
        group by ip.qc_ids;
    </select>

    <select id="selectByLaunchStatus" resultType="com.daddylab.supplier.item.types.item.ItemLaunchStatusObj">
        SELECT `i`.`id` as item_id,
        `i`.`code`,
        `i`.`name`,
        `i`.`launch_status`,
        `i`.`audit_status`,
        i1.`id` AS 'merge_item_id',
        i1.`launch_status` AS 'merge_launch_status',
        i1.`audit_status` AS 'merge_audit_status'
        FROM `item` `i`
        LEFT JOIN `item_drawer_merge_item` `idmi` ON `i`.`id` = `idmi`.`item_id` AND idmi.is_del = 0
        LEFT JOIN `item_drawer_merge` `idm` ON `idmi`.`merge_id` = `idm`.`id` AND idm.is_del = 0
        LEFT JOIN `item` i1 ON i1.`id` = `idm`.`item_id` AND i1.is_del = 0
        WHERE (`i`.`launch_status` = #{launchStatus} AND idmi.`id` IS NULL <if test="auditStatus != null">
        AND `i`.`audit_status` = #{auditStatus}
    </if>)
        OR (i1.`launch_status` = #{launchStatus} <if test="auditStatus != null">
        AND i1.`audit_status` = #{auditStatus}
    </if>)
        AND i.is_del = 0
        ;
    </select>

    <select id="selectByLiveVerbalTrickStatus"
            resultType="com.daddylab.supplier.item.types.item.ItemLiveVerbalTrickStatusObj">
        SELECT `i`.`id`                         AS `item_id`,
               `i`.`code`,
               `i`.`name`,
               idlv.`id`                        AS `live_verbal_trick_id`,
               idlv.`live_verbal_trick_status`  AS `live_verbal_trick_status`,
               `i1`.`id`                        AS 'merge_item_id',
               idlv1.`id`                       AS `merge_live_verbal_trick_id`,
               idlv1.`live_verbal_trick_status` as `merge_live_verbal_trick_status`
        FROM `item_drawer_live_verbal` `idlv`
                 JOIN `item` i ON idlv.`item_id` = i.id
                 LEFT JOIN `item_drawer_merge_item` `idmi` ON `i`.`id` = `idmi`.`item_id` AND `idmi`.`is_del` = 0
                 LEFT JOIN `item_drawer_merge` `idm` ON `idmi`.`merge_id` = `idm`.`id` AND `idm`.`is_del` = 0
                 LEFT JOIN `item` `i1` ON `i1`.`id` = `idm`.`item_id` AND `i1`.`is_del` = 0
                 LEFT JOIN `item_drawer_live_verbal` `idlv1` ON idlv1.`item_id` = i1.id AND `idlv1`.`is_del` = 0
        WHERE idlv.is_del = 0
          AND ((idmi.`id` IS NULL AND idlv.`live_verbal_trick_status` = #{liveVerbalTrickStatus}) OR
               (idlv1.`live_verbal_trick_status` = #{liveVerbalTrickStatus}))
          AND EXISTS(SELECT id FROM `new_goods` `ng` WHERE ng.`item_id` = i.`id` AND ng.`is_del` = 0)
    </select>

    <select id="selectByMaterialsStatus"
            resultType="com.daddylab.supplier.item.types.item.ItemTrainingMaterialsStatusObj">
        SELECT `i`.`id`        AS `item_id`,
               `i`.`code`,
               `i`.`name`,
               `idm`.`item_id` AS `merge_item_id`,
               `itm`.`status`  AS `materials_status`,
               `itm2`.`id`     AS `merge_materials_id`,
               `itm2`.`status` AS `merge_materials_status`
        FROM `item_training_materials` `itm`
                 LEFT JOIN `item` `i` ON `itm`.`item_id` = `i`.`id` AND `i`.`is_del` = 0
                 LEFT JOIN `item_drawer_merge_item` `idmi` ON `i`.`id` = `idmi`.`item_id` AND `idmi`.`is_del` = 0
                 LEFT JOIN `item_drawer_merge` `idm` ON `idmi`.`merge_id` = `idm`.`id` AND `idm`.`is_del` = 0
                 LEFT JOIN `item_training_materials` `itm2`
                           ON `itm2`.`item_id` = `idm`.`item_id` AND `itm2`.`is_del` = 0
        WHERE EXISTS(SELECT `id` FROM `new_goods` `ng` WHERE `ng`.`item_id` = `i`.`id` AND `ng`.`is_del` = 0)
          AND ((idm.`id` IS NULL AND `itm`.`status` = #{materialsStatus}) OR `itm2`.`status` = #{materialsStatus})
        ;
    </select>
</mapper>


