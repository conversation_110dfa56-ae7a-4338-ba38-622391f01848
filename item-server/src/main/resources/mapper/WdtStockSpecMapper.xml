<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtStockSpecMapper">


    <select id="statistics"
            resultType="com.daddylab.supplier.item.types.stockSpec.WarehouseStockSpecStatistic">
        select warehouse_no, count(distinct goods_no) as spuNum,
        count(distinct spec_no) as skuNum,
        sum(stock_num) as totalStock,
        sum(available_send_stock) as totalAvailableSendStock,
        sum(available_stock) as totalAvailableStock,
        sum(purchase_num) as totalPurchaseOnWayNum,
        sum(wms_sync_stock) as totalWmsSyncStock
        from wdt_stock_spec_rt
        <where>
            defect = 0 and available_stock > 0
            <if test="param1.skuCode != null and param1.skuCode != ''">and spec_no = #{query.skuCode}</if>
            <if test="query.itemCode != null and query.itemCode != ''">and goods_no = #{query.itemCode}</if>
            <if test="param1.itemName != null and param1.itemName != ''">and goods_name like concat('%',
                #{query.itemName},'%')
            </if>
            <if test="query.warehouseNos != null">
                and warehouse_no in
                <foreach item="item" index="index" collection="query.warehouseNos" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by warehouse_no;
    </select>

    <select id="shopRealWarehouseStockQuery"
            resultType="com.daddylab.supplier.item.types.stockSpec.ShopRealWarehouseStockSpecVO">
        SELECT `si`.`id`                  AS `shopInventoryId`
             , `si`.`shop_id`             AS `shopId`
             , `s`.`sn`                   AS `shopNo`
             , `sis`.`inventory_mode`     AS `shopInventoryMode`
             , `si`.`warehouse_no`        AS `warehouseNo`
             , `si`.`warehouse_name`      AS `warehouseName`
             , `w`.`is_virtual_warehouse` AS `isVirtualWarehouse`
             , `wss`.`goods_no`           AS `goodsNo`
             , `wss`.`spec_no`            AS `specNo`
             , `wss`.`available_stock`    AS `availableStock`
             , `wgils`.`lock_num`         AS `availableStockLockNum`
             , `wgils`.`lock_ratio`       AS `availableStockLockRatio`
             , `si`.`inventory_ratio`     AS `inventoryRatio`
             , `sig`.`inventory_ratio`    AS `skuInventoryRatio`
             , `sig`.`inventory_ratio2`   AS `skuInventoryRatio2`
             , `sig`.`inventory_lock_num` AS `skuInventoryLockNum`
             , IFNULL(`wss`.`defect`, 0)  AS `defect`
        FROM `shop` `s`
                 JOIN `shop_inventory_setting` `sis` ON `s`.`id` = `sis`.`shop_id` AND `sis`.`is_del` = 0
                 JOIN `shop_inventory` `si` ON `si`.`shop_id` = `s`.`id` AND `s`.`is_del` = 0
                 JOIN `warehouse` `w` ON `si`.`warehouse_no` = `w`.`no` AND `w`.`is_del` = 0
                 LEFT JOIN `wdt_stock_spec` `wss` ON `wss`.`warehouse_no` = `si`.`warehouse_no` AND `wss`.`is_del` = 0
                 LEFT JOIN `shop_inventory_goods` `sig` ON `si`.`shop_id` = `sig`.`shop_id` AND `si`.`warehouse_no` =
                                                                                                `sig`.`warehouse_no` AND
                                                           `wss`.`spec_no` = `sig`.`sku_no` AND `sig`.`is_del` = 0
                 LEFT JOIN `warehouse_goods_inventory_lock_statics` `wgils`
                           ON `wgils`.`warehouse_no` = `wss`.`warehouse_no` AND `wgils`.`sku_no` = `wss`.`spec_no` AND
                              `wgils`.`is_del` = 0
        <where>
            `si`.`is_del` = 0
            <if test="query.shopNo != null and query.shopNo != ''">
                AND `s`.`sn` = #{query.shopNo}
            </if>
            <if test="query.specNos != null">
                AND `wss`.`spec_no` IN
                <foreach item="item" index="index" collection="query.specNos" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.goodsNos != null">
                AND `wss`.`goods_no` IN
                <foreach item="item" index="index" collection="query.goodsNos" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        LIMIT #{query.offset}, #{query.pageSize}
        ;
    </select>

    <select id="shopVirtualWarehouseStockQuery"
            resultType="com.daddylab.supplier.item.types.stockSpec.ShopVirtualWarehouseStockSpecVO">
        SELECT `si`.`id`                      `shopInventoryId`
             , `si`.`shop_id`              AS `shopId`
             , `s`.`sn`                    AS `shopNo`
             , `sis`.`inventory_mode`      AS `shopInventoryMode`
             , `si`.`warehouse_no`         AS `warehouseNo`
             , `si`.`warehouse_name`       AS `warehouseName`
             , 1                           AS `isVirtualWarehouse`
             , `vw`.`inventory_mode`       AS `warehouseInventoryMode`
             , `sig`.`spu_no`              AS `goodsNo`
             , `sig`.`sku_no`              AS `specNo`
             , `si`.`inventory_ratio`      AS `inventoryRatio`
             , `sig`.`inventory_ratio`     AS `skuInventoryRatio`
             , `sig`.`inventory_ratio2`    AS `skuInventoryRatio2`
             , `sig`.`inventory_lock_num`  AS `skuInventoryLockNum`
             , `vwi`.`warehouse_no`        AS `realWarehouseNo`
             , `vwi`.`warehouse_name`      AS `realWarehouseName`
             , `wss`.`available_stock`     AS `availableStock`
             , IFNULL(`wss`.`defect`, 0)   AS `defect`
             , `wgils1`.`lock_num`         AS `availableStockLockNum`
             , `wgils1`.`lock_ratio`       AS `availableStockLockRatio`
             , `wgils2`.`lock_num`         AS `realAvailableStockLockNum`
             , `wgils2`.`lock_ratio`       AS `realAvailableStockLockRatio`
             , `vwi`.`inventory_ratio`     AS `virtualInventoryRatio`
             , `vwig`.`inventory_ratio`    AS `virtualSkuInventoryRatio`
             , `vwig`.`inventory_ratio2`   AS `virtualSkuInventoryRatio2`
             , `vwig`.`inventory_lock_num` AS `virtualSkuInventoryLockNum`
        FROM `shop` `s`
                 JOIN `shop_inventory_setting` `sis` ON `s`.`id` = `sis`.`shop_id`
                 JOIN `shop_inventory` `si` ON `si`.`shop_id` = `s`.`id` AND `s`.`is_del` = 0
                 LEFT JOIN `shop_inventory_goods` `sig`
                           ON `si`.`shop_id` = `sig`.`shop_id` AND `si`.`warehouse_no` = `sig`.`warehouse_no`
                               AND `sig`.`is_del` = 0
                 LEFT JOIN `warehouse_goods_inventory_lock_statics` `wgils1`
                           ON `wgils1`.`warehouse_no` = `sig`.`warehouse_no` AND `wgils1`.`sku_no` = `sig`.`sku_no` AND
                              `wgils1`.`is_del` = 0
                 JOIN `virtual_warehouse` `vw`
                      ON `vw`.`no` = `si`.`warehouse_no` AND `vw`.`is_del` = 0 AND `vw`.`status` = 0
                 LEFT JOIN `virtual_warehouse_inventory` `vwi`
                           ON `vw`.`id` = `vwi`.`virtual_warehouse_id` AND `vwi`.`is_del` = 0 AND
                              `vw`.`inventory_mode` = 0
                 LEFT JOIN `virtual_warehouse_inventory_goods` `vwig` ON
                    `vw`.`no` = `vwig`.`virtual_warehouse_no` AND `vwig`.`warehouse_no` = `vwi`.`warehouse_no` AND
                    `vwig`.`sku_no` = `sig`.`sku_no` AND `vwig`.`is_del` = 0
                 LEFT JOIN `wdt_stock_spec` `wss`
                           ON `wss`.`warehouse_no` = `vwi`.`warehouse_no` AND `vwig`.`sku_no` = `wss`.`spec_no` AND
                              `wss`.`is_del` = 0
                 LEFT JOIN `warehouse_goods_inventory_lock_statics` `wgils2`
                           ON `wgils2`.`warehouse_no` = `wss`.`warehouse_no` AND
                              `wgils2`.`sku_no` = `wss`.`spec_no` AND `wgils2`.`is_del` = 0
        <where>
            `si`.`is_del` = 0
            <if test="query.shopNo != null and query.shopNo != ''">
                AND `s`.`sn` = #{query.shopNo}
            </if>
            <if test="query.specNos != null">
                AND `wss`.`spec_no` IN
                <foreach item="item" index="index" collection="query.specNos" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.goodsNos != null">
                AND `wss`.`goods_no` IN
                <foreach item="item" index="index" collection="query.goodsNos" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        LIMIT #{query.offset}, #{query.pageSize}
        ;
    </select>
</mapper>
