<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.MessageMapper">

<!--    <select id="pageMessage"-->
<!--            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Message">-->
<!--        select-->
<!--            m.*-->
<!--        from message m left join message_config mc on m.config_id = mc.id-->
<!--        <where>-->
<!--            m.is_del = 0 and mc.is_del = 0-->
<!--            <if test="0 == pushType">-->
<!--                and mc.push_default = 1-->
<!--            </if>-->
<!--            <if test="1 == pushType">-->
<!--                and mc.push_text = 1-->
<!--            </if>-->
<!--            <if test="2 == pushType">-->
<!--                and mc.push_mail = 1-->
<!--            </if>-->
<!--&lt;!&ndash;            强提醒 ，获取未读取状态的消息 &ndash;&gt;-->
<!--            <if test="3 == pushType">-->
<!--                and mc.push_remind = 1-->
<!--                and m.state = 0-->
<!--            </if>-->
<!--            <if test="recipientId != null and recipientId != ''">-->
<!--                and m.recipient_id = #{recipientId}-->
<!--            </if>-->
<!--        </where>-->
<!--        order by m.created_at desc limit #{offset},#{size}-->
<!--    </select>-->

<!--    <select id="pageMessageCount" resultType="java.lang.Integer">-->
<!--        select-->
<!--            count(1)-->
<!--        from message m left join message_config mc on m.config_id = mc.id-->
<!--        <where>-->
<!--            m.is_del = 0 and mc.is_del = 0-->
<!--            <if test="0 == pushType">-->
<!--                and mc.push_default = 1-->
<!--            </if>-->
<!--            <if test="1 == pushType">-->
<!--                and mc.push_text = 1-->
<!--            </if>-->
<!--            <if test="2 == pushType">-->
<!--                and mc.push_mail = 1-->
<!--            </if>-->
<!--            &lt;!&ndash;            强提醒 ，获取未读取状态的消息 &ndash;&gt;-->
<!--            <if test="3 == pushType">-->
<!--                and mc.push_remind = 1-->
<!--                and m.state = 0-->
<!--            </if>-->
<!--            <if test="recipientId != null and recipientId != ''">-->
<!--                and m.recipient_id = #{recipientId}-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->
</mapper>
