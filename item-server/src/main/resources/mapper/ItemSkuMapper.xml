<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemSkuMapper">



<select id="getWarehouseNoList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuWarehouseNoDO">
    select
        sku_code as skuCode,
        IFNULL(warehouse_no,'') as warehouseNo
    from
        item_sku
    where is_del = 0
        and sku_code in
        <foreach item="code" index="index" collection="list" open="(" close=")" separator=",">
            #{code}
        </foreach>
</select>



<select id="getIsGiftList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuIsGiftDO">
    select
        sku.sku_code,
        ii.is_gift
    from
        item_sku sku inner join item ii on sku.item_id = ii.id
    where
        sku.is_del = 0 and ii.is_del = 0
        and sku.sku_code in
        <foreach item="code" index="index" collection="list" open="(" close=")" separator=",">
            #{code}
        </foreach>
</select>

    <select id="selectBatchDtosBySkuIds"
            resultType="com.daddylab.supplier.item.controller.item.dto.SkuWithLaunchPlanDto">
        select
            t1.id as skuId,
            t1.specifications as skuName,
            t1.cost_price as costPrice,
            t1.sale_price as salePrice,
            t2.stock as stock
        from
            item_sku as t1
            left join item_stock as t2 on (t1.id = t2.sku_id)
        where t1.id in
        <foreach item="skuId" index="index" collection="skuIds"
                 open="(" separator="," close=")">
            #{skuId}
        </foreach>
        and t1.is_del = 0
    </select>

    <select id="getItemAndSkuCodeInfoBatch"
            resultType="com.daddylab.supplier.item.domain.item.data.ItemAndSkuCodeInfo">
        SELECT `item`.`id`                          AS `itemId`,
               `item`.`code`                        AS `itemCode`,
               `item`.`provider_specified_code`     AS `itemProviderSpecifiedCode`,
               `item_sku`.`id`                      AS `skuId`,
               `item_sku`.`sku_code`                AS `skuCode`,
               `item_sku`.`provider_specified_code` AS `providerSpecifiedCode`
        FROM `item`,
             `item_sku` WHERE `item`.`id` = `item_sku`.`item_id`
                          AND `item`.`is_del` = 0
                          AND `item_sku`.`is_del` = 0
                          AND (`item_sku`.`sku_code` IN
        <foreach item="item" index="index" collection="collection" open="(" separator="," close=")">
            #{item}
        </foreach>
        OR `item_sku`.`provider_specified_code` IN
        <foreach item="item" index="index" collection="collection" open="(" separator="," close=")">
            #{item}
        </foreach>)
    </select>

    <select id="getSkuCodes" resultType="java.lang.String">
        SELECT sku_code
        FROM item,
             item_sku
        WHERE item.id = item_sku.item_id
          AND item.is_del = 0
          AND item_sku.is_del = 0
          AND item.code = #{itemCode}
    </select>

    <select id="getItemAndSkuCodeInfoBatchByItemNos"
            resultType="com.daddylab.supplier.item.domain.item.data.ItemAndSkuCodeInfo">
        SELECT item.id                          as itemId,
               item.code                        as itemCode,
               item_sku.id                      as skuId,
               item_sku.sku_code                as skuCode,
               item_sku.provider_specified_code as providerSpecifiedCode
        FROM item,
             item_sku
        WHERE item.id = item_sku.item_id
          AND item.is_del = 0
          AND item_sku.is_del = 0
          AND item.code IN
        <foreach item="item" index="index" collection="itemCodes" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

<select id="selectPrice" resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuPriceDO">
    select sku_code,cost_price from item_sku
    where is_del = 0
    and
    (
    sku_code in
    <foreach item="item" index="index" collection="skuCodeList" open="(" separator="," close=")">
        #{item}
    </foreach>
    or
    provider_specified_code in
    <foreach item="item" index="index" collection="skuCodeList" open="(" separator="," close=")">
        #{item}
    </foreach>
    )
    </select>

    <select id="selectSkuAttrDtosBySkuId" resultType="com.daddylab.supplier.item.controller.item.dto.AttrDto">
        SELECT
            t3.`name` AS `key`,
            t2.attr_value AS `value`,
            t4.url AS imageUrl,
            t4.filename AS imageFileName
        FROM item_sku_attr_ref AS t1
        INNER JOIN item_attr AS t2 ON (t1.item_attr_id = t2.id)
        INNER JOIN category_attr AS t3 ON (t2.attr_id = t3.id)
        LEFT JOIN item_drawer_image AS t4 ON (t2.id = t4.item_attr_id AND t4.is_del = 0 AND t4.type = 5)
        WHERE t1.item_sku_id = #{skuId}
          AND t1.is_del = 0 AND t2.is_del = 0 AND t3.is_del = 0
    </select>

    <select id="selectSkuName"
                resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuNameDo">
        select
            if(sku.provider_specified_code != '' and sku.provider_specified_code is not null, sku.provider_specified_code,sku.sku_code) as skuCode,
            iitem.name
        from item_sku sku
        inner join item iitem on sku.item_id = iitem.id
        where
        sku.is_del = 0 and iitem.is_del = 0
        and (
            sku.sku_code in
            <foreach item="skuCode" index="index" collection="skuCodes" open="(" separator="," close=")">
                #{skuCode}
            </foreach>
            or
            sku.provider_specified_code in
            <foreach item="skuCode" index="index" collection="skuCodes" open="(" separator="," close=")">
                #{skuCode}
            </foreach>
        )
    </select>

    <select id="getSkuBusinessLine" resultType="java.lang.Integer">
<!--        select IFNULL(i.business_lines, 0)-->
<!--        from item_sku sku-->
<!--                 inner join item i on sku.item_id = i.id-->
<!--        where sku.is_del = 0-->
<!--          and (sku.sku_code = #{skuCode,jdbcType=VARCHAR} or sku.provider_specified_code = #{skuCode,jdbcType=VARCHAR})-->
        SELECT
            bld.level_val
        FROM
            item_sku sku
        INNER JOIN item item ON sku.item_id = item.id
        LEFT JOIN biz_level_division bld ON bld.biz_id = item.id
        WHERE
        sku.is_del = 0
        AND item.is_del = 0
        AND bld.is_del = 0
        AND bld. `type` = 0
        AND bld. `level` = 0
        AND sku.sku_code = #{skuCode}
    </select>

    <select id="getSkuBrandId" resultType="java.lang.Long">
        select spu.brand_id
        from item_sku sku
                 inner join item spu on sku.item_id = spu.id
        where sku.is_del = 0
        and sku.sku_code = #{skuCode}
    </select>
</mapper>
