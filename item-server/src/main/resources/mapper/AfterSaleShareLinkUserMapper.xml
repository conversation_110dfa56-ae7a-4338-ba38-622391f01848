<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AfterSaleShareLinkUserMapper">
    <!--    <select id="countByLinkId" resultType="com.daddylab.supplier.item.application.afterSaleLink.dto.LinkCountDto">-->
    <!--        select link_id, count(1)-->
    <!--        from after_sale_share_link_user-->
    <!--        where link_id in-->
    <!--        <foreach collection="linkIds" item="linkId" open="(" separator="," close=")">-->
    <!--            #{linkId}-->
    <!--        </foreach>-->
    <!--        group by link_id;-->
    <!--    </select>-->

    <select id="countByLinkId" resultType="com.daddylab.supplier.item.application.afterSaleLink.dto.LinkCountDto">
        select link_id as id, count(1) as num
        from after_sale_share_link_user
        where link_id in
        <foreach collection="linkIds" item="linkId" open="(" separator="," close=")">
            #{linkId}
        </foreach>
        group by link_id;
    </select>
</mapper>
