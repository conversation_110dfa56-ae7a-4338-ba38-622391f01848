<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtGoodsSnMapper">
    <insert id="saveGoodsSnBatch">
        INSERT IGNORE `wdt_goods_sn` (`id`, `rec_id`, `sn`, `spec_no`, `stockout_no`, `stockin_no`,
                                      `src_order_no`, `src_order_type`, `created`)
        VALUES
        <foreach item="item" index="index" collection="items" open="" separator="," close="">
            (#{item.id}, #{item.recId}, #{item.sn}, #{item.specNo}, #{item.stockoutNo},
             #{item.stockinNo},
             #{item.srcOrderNo}, #{item.srcOrderType}, #{item.created})
        </foreach>
    </insert>

    <select id="openGoodsSnQuery"
            parameterType="com.daddylab.supplier.item.types.goodsSn.OpenGoodsSnQuery"
            resultType="com.daddylab.supplier.item.types.goodsSn.OpenGoodsSnDto">
        SELECT `wgs`.`id`,
               `wgs`.`rec_id`         AS `recId`,
               `wgs`.`sn`,
               `wgs`.`spec_no`        AS `specNo`,
               `wgs`.`stockout_no`    AS `stockoutNo`,
               `wgs`.`stockin_no`     AS `stockinNo`,
               `wo`.`src_tids`        AS `srcOrderNo`,
               `wgs`.`src_order_type` AS `srcOrderType`,
               `wgs`.`created`,
               `wo`.`shop_no`         AS `shopNo`,
               `wo`.`shop_name`       AS `shopName`
        FROM `wdt_goods_sn` `wgs`
                 LEFT JOIN
             `wdt_order` `wo`
             ON `wo`.`trade_no` = `wgs`.`src_order_no`
        <where>
            <if test="empty">
                FALSE
            </if>
            <if test="snList != null and snList.size() != 0">
                AND `wgs`.`sn` IN
                <foreach item="item" index="index" collection="snList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="shopNo != null and shopNo != ''">
                AND wo.shop_no = #{shopNo}
            </if>
            <if test="timeStart != null and timeEnd != null">
                AND `wgs`.`created` BETWEEN #{timeStart} AND #{timeEnd}
            </if>
        </where>
    </select>
</mapper>
