<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemWarnMapper">

    <select id="selectToWarnUnmatchSkuList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku">
        SELECT pis.*
        FROM `platform_item_sku` `pis`
        <if test="skipWarned">
        LEFT JOIN `platform_item_warn` `piw`
            ON `pis`.`id` = `piw`.`platform_item_sku_id` AND piw.`warn_type` = 1 AND piw.is_del = 0 and piw.status = 0
        </if>
        WHERE pis.is_del = 0
        AND pis.item_match = 0
        <if test="skipWarned">
            AND piw.`id` is NULL
        </if>
        AND pis.id > #{cursorId}
        ORDER BY pis.id
        LIMIT #{limit}
        ;
    </select>

    <select id="selectToWarnStockSkuList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku">
        SELECT `pis`.*
        FROM `platform_item_sku` `pis`
        LEFT JOIN
        `platform_item_sku_inventory_setting` `pisis`
        ON `pis`.`outer_sku_id` = `pisis`.`outer_sku_id` AND `pis`.`shop_no` = `pisis`.`shop_no` AND
        `pis`.`outer_item_id` = `pisis`.`outer_item_id`
        LEFT JOIN shop s on s.sn = pis.shop_no
        LEFT JOIN shop_inventory_setting sis on sis.`shop_no` = s.`sn`
        <if test="skipWarned">
        LEFT JOIN `platform_item_warn` `piw` ON `pis`.`id` = `piw`.`platform_item_sku_id` AND piw.`warn_type` = 2 and piw.status = 0 and piw.is_del = 0
        </if>
        WHERE IFNULL(pis.`stock`, 0) <![CDATA[<=]]> IF(pisis.`safety_threshold` > -1, pisis.`safety_threshold`, IF(sis.safety_threshold > -1, sis.safety_threshold, -1))
        <if test="skipWarned">
            AND piw.`id` is NULL
        </if>
        AND pis.id > #{cursorId} AND pis.`is_del` = 0 AND pis.`status` = 1
        ORDER BY pis.id
        LIMIT #{limit}
        ;
    </select>

    <insert id="saveOrUpdateBatch">
        insert into platform_item_warn (platform_item_id, platform_item_sku_id, warn_type, status, created_at,
        created_uid, updated_at, updated_uid)
        values
        <foreach item="item" index="index" collection="warns" open="" separator="," close="">
            (#{item.platformItemId}, #{item.platformItemSkuId}, #{item.warnType.value}, #{item.status.value},
            #{item.createdAt}, 0, #{item.updatedAt}, 0)
        </foreach>
        on duplicate key update status = values(status), updated_at = values(updated_at), updated_uid = 0;
    </insert>

    <update id="autoHandleExpired">
        update platform_item_warn set status = 1, updated_at = unix_timestamp(), updated_uid = 0
        where updated_at <![CDATA[<]]> #{time} and is_del = 0;
    </update>

</mapper>
