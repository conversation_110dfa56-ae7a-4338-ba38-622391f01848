<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AfterSalesAbnormalInfoMapper">
    <select id="pageQueryOrderByCreatedAt"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.AfterSalesLogisticsDO">
        select distinct logistics_no from
            after_sales_abnormal_info
        <where>
            is_del = 0
            and state != 4
            <if test="logisticsNo != null and logisticsNo != ''">
                and logistics_no = #{logisticsNo}
            </if>
            <if test="logisticsName != null and logisticsName != ''">
                and logistics_name like concat('%',#{logisticsName},'%')
            </if>
            <if test="warehouseNo != null and warehouseNo != ''">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="createdAtStart != null and createdAtStart != 0 and createdAtEnd != null and createdAtEnd != 0">
                and (created_at between #{createdAtStart}
                and #{createdAtEnd})
            </if>
            <if test="itemName != null and itemName != ''">
                and item_name like concat('%', #{itemName},'%')
            </if>
            <if test="receiveStatus != null">
                and return_order_no in
                (select return_order_no from after_sales_receive where receive_result = #{receiveStatus}
                and is_del = 0)
            </if>
            <if test="partnerProviderId != null">
                and partner_provider_id = #{partnerProviderId}
            </if>
<!--            如果是erp系统的查询。已转寄和已处理的数据都不展示-->
            <if test="partnerProviderId == null">
                and state != 3
            </if>
            <if test="abnormalState != null">
                and state = #{abnormalState}
            </if>
        </where>
        order by id desc
    </select>

    <select id="pageQueryOrderByLogisticsNo"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.AfterSalesPageDO">
        select distinct
        info.id,
        info.logistics_no,
        info.logistics_name,
        info.item_name,
        info.remark,
        info.quantity,
        info.state,
        from_unixtime(info.created_at) as createdAtStr,
        warehouse.warehouse_name,
        warehouse.warehouse_no,
        info.partner_provider_name as providerName,
        infoImage.images as images
        from after_sales_abnormal_info info
        left join (select related_id, group_concat(image) as images
                from after_sales_info_image
                where is_del = 0
                group by related_id) infoImage on info.id = infoImage.related_id
        left join (select warehouse_no, warehouse_name
                from warehouse_after_sales_address warehouse
                where is_del = 0) warehouse on info.warehouse_no = warehouse.warehouse_no
        where info.is_del = 0
        and info.state != 4
        <if test=" logisticsNoList != null and logisticsNoList.size()>0 ">
            and info.logistics_no in
            <foreach collection="logisticsNoList" item="no" open="(" separator="," close=")">
                #{no}
            </foreach>
        </if>
    </select>

    <select id="exportList" resultType="com.daddylab.supplier.item.application.afterSales.AbnormalExportSheet">
        select logistics_no,
        logistics_name,
        item_name,
        quantity,
        state,
        from_unixtime(created_at) as createdAtStr,
        remark
        from after_sales_abnormal_info
        <where>
            is_del = 0
            and state != 4
            <if test="logisticsNo != null and logisticsNo != ''">
                and logistics_no = #{logisticsNo}
            </if>
            <if test="logisticsName != null and logisticsName != ''">
                and logistics_name = #{logisticsName}
            </if>
            <if test="warehouseNo != null and warehouseNo != ''">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="createdAtStart != null and createdAtStart != 0 and createdAtEnd != null and createdAtEnd != 0">
                and (created_at between #{createdAtStart}
                and #{createdAtEnd})
            </if>
            <if test="itemName != null and itemName != ''">
                and item_name = #{itemName}
            </if>
            <if test="receiveStatus != null">
                and return_order_no in
                (select return_order_no from after_sales_receive where receive_result = #{receiveStatus}
                and is_del = 0)
            </if>
            <if test="partnerProviderId != null">
                and partner_provider_id = #{partnerProviderId}
            </if>
        </where>
        order by created_at desc,logistics_no
    </select>

    <select id="exportCount" resultType="java.lang.Integer">
        select count(1)
        from after_sales_abnormal_info
        <where>
            is_del = 0
            and state != 4
            <if test="logisticsNo != null and logisticsNo != ''">
                and logistics_no = #{logisticsNo}
            </if>
            <if test="logisticsName != null and logisticsName != ''">
                and logistics_name = #{logisticsName}
            </if>
            <if test="warehouseNo != null and warehouseNo != ''">
                and warehouse_no = #{warehouseNo}
            </if>
            <if test="createdAtStart != null and createdAtStart != 0 and createdAtEnd != null and createdAtEnd != 0">
                and (created_at between #{createdAtStart}
                and #{createdAtEnd})
            </if>
            <if test="itemName != null and itemName != ''">
                and item_name = #{itemName}
            </if>
            <if test="receiveStatus != null">
                and return_order_no in
                (select return_order_no from after_sales_receive where receive_result = #{receiveStatus}
                and is_del = 0)
            </if>
            <if test="partnerProviderId != null">
                and partner_provider_id = #{partnerProviderId}
            </if>
        </where>
    </select>
</mapper>
