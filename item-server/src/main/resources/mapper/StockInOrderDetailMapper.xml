<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockInOrderDetailMapper">
    <select id="selectByStockInOrderDetailId"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrderDetail">
        SELECT *
        FROM stock_in_order_detail siod
        WHERE siod.stock_in_order_id = #{id}
          and siod.is_del = 0
    </select>

    <sql id="whereIsStockInOrderDetail">
        siod.is_del = 0
        <if test="itemSkuCode != null">
            AND siod.item_sku_code = #{itemSkuCode}
        </if>
        <if test="itemName != null">
            AND siod.item_name = #{itemName}
        </if>
    </sql>

    <select id="selectByItemSkuCodeAndItemName"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrderDetail">
        SELECT * FROM stock_in_order_detail siod
        <where>
            <include refid="whereIsStockInOrderDetail"/>
        </where>
    </select>
    <update id="delByStockInOrderDetailId" parameterType="long">
        UPDATE stock_in_order_detail siod
        SET siod.is_del = 1
        WHERE siod.id = #{id}
    </update>

    <update id="batchUpdateStockInNum">
        UPDATE stock_in_order_detail siod
        SET real_receipt_quantity = case item_sku_code
        <foreach collection="map" open="" index="skuNo" item="num" close="">
            when #{skuNo} then #{num}
        </foreach>
        end
        WHERE siod.item_sku_code IN
        <foreach collection="map" open="(" index="skuNo" item="num" close=")" separator=",">
            #{skuNo}
        </foreach>
    </update>
    <update id="updateDetailInfoByInOrderIdAndSku">
        UPDATE supplier.stock_in_order_detail siod
        set siod.real_receipt_quantity = #{intValue}
        where siod.stock_in_order_id = #{id}
          and siod.is_del = 0
          and siod.item_sku_code = #{specNo}
    </update>
    <select id="getStockInOrderSkuStock" resultType="java.lang.Integer">
        SELECT
            SUM(siod.real_receipt_quantity)
        FROM
            stock_in_order sio
        LEFT JOIN
            stock_in_order_detail siod ON sio.id = siod.stock_in_order_id
        WHERE
            sio.is_del = 0
            AND siod.is_del = 0
            AND sio.state in (3,21)
            AND sio.is_sync_wdt = #{isSyncWdt}
            AND sio.purchase_order_id = #{purchaseOrderId}
            AND siod.item_sku_code = #{itemSkuCode}
            AND siod.warehouse_no = #{warehouseNo}
            <if test="isGift != null">
                AND siod.is_gift = #{isGift}
            </if>
        GROUP BY
            sio.purchase_order_id
    </select>
    <select id="getStockInOrderDetail"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrderDetail">
        SELECT siod.*
        FROM stock_in_order sio
                     LEFT JOIN stock_in_order_detail siod ON sio.id = siod.stock_in_order_id
        WHERE sio.is_del = 0
          AND siod.is_del = 0
          AND sio.state in (3,21)
          AND sio.purchase_order_id = #{purchaseOrderId}
          AND siod.item_sku_code = #{itemSkuCode}
    </select>

    <select id="getDetailByPurchaseOrderId"
            resultType="com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderDetailVo">
        SELECT sum(siod.real_receipt_quantity) as real_receipt_quantity,
               w.`no`                          as warehouseNo,
               w.NAME                          as stock,
               min(siod.item_sku_code)         as itemSkuCode
        FROM stock_in_order sio
                     LEFT JOIN stock_in_order_detail siod ON sio.id = siod.stock_in_order_id
                     LEFT JOIN warehouse w ON siod.warehouse_no = w.NO
        WHERE sio.is_del = 0
          AND siod.is_del = 0
          AND sio.state in (3,21)
          AND sio.purchase_order_id = #{param.purchaseOrderId}
        GROUP BY siod.item_sku_code, w.`no`
        limit #{param.offset},#{param.pageSize}
    </select>

    <select id="getDetailCountByPurchaseOrderId" resultType="java.lang.Integer">
        SELECT count(0)
        FROM (SELECT siod.item_sku_code
              FROM stock_in_order sio
                           LEFT JOIN stock_in_order_detail siod ON sio.id = siod.stock_in_order_id
              WHERE sio.is_del = 0
                AND siod.is_del = 0
                AND sio.state = 3
                AND sio.purchase_order_id = #{purchaseOrderId}
              GROUP BY siod.item_sku_code
                     ) as temp
    </select>
</mapper>
