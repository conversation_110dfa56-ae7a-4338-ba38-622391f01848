<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockOutOrderMapper">
    <select id="pageQuery" resultType="com.daddylab.supplier.item.controller.stockout.dto.StockOutOrderVO">
        select soo.id,soo.no,
        p.id as providerId,
        p.name as providerName,
        p.partner_provider_id as partnerProviderId,
        soo.total_return_quantity as totalReturnQuantity,
        soo.return_type as returnType,
        soo.return_mode as returnMode,
        soo.created_uid as createdUid,
        soo.created_at as createdAt,
        soo.state,
        soo.business_line
        from stock_out_order soo left join ( SELECT id, name, partner_provider_id FROM provider WHERE is_del = 0 ) p on
        soo.provider_id =
        p.id
        <include refid="queryStock"/>
        order by soo.created_at desc
        limit #{param.offset},#{param.pageSize}
    </select>

    <select id="pageCount" resultType="java.lang.Integer">
        select count(1)
        from stock_out_order soo left join ( SELECT id, name FROM provider WHERE is_del = 0 ) p on soo.provider_id =
        p.id
        <include refid="queryStock"/>
    </select>

    <select id="countExport" resultType="java.lang.Integer">
        select count(*)
        from stock_out_order_detail sood
        where sood.is_del = 0 and stock_out_order_id in (select soo.id from stock_out_order soo
        <include refid="queryStock"/>
        )
    </select>

    <!--    导出查询-->
    <select id="queryExport" resultType="com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderSheet">
        select
        soo.no as no,
        p.name as provider,
        o.name AS purchaseOrganization,
        soo.return_type as returnType,
        soo.return_mode as returnMode,
        sood.item_sku_code as itemSkuCode,
        sood.item_name as itemName,
        sood.specifications as specifications,
        sood.stock_unit as stockUnit,
        sood.return_quantity as returnQuantity,
        wh.name AS stock,
        sood.valuation_unit as valuationUnit,
        sood.valuation_quantity as valuationQuantity,
        sood.replenish_quantity as replenishQuantity,
        sood.deduction_quantity as deductionQuantity,
        sood.return_reason as returnReason,
        IF(sood.is_gift = 1, '是', '否') as isGift,
        sood.remark as remark,
        sood.tax_price as taxPrice,
        CAST(sood.tax_rate * 100 as DECIMAL(10,0) ) as taxRate,
        sood.tax_quota as taxQuota,
        sood.total_price_tax as totalPriceTax,
        sood.after_tax_price as afterTaxPrice,
        sood.after_tax_amount as afterTaxAmount,
        soo.business_line
        FROM stock_out_order soo
        LEFT JOIN ( SELECT * FROM stock_out_order_detail WHERE is_del = 0 ) sood ON soo.id = sood.stock_out_order_id
        LEFT JOIN ( SELECT id, name FROM provider WHERE is_del = 0 ) p ON soo.provider_id = p.id
        LEFT JOIN ( SELECT id, name FROM organization WHERE is_del = 0 ) o ON soo.purchase_organization_id = o.id
        LEFT JOIN ( SELECT no, name FROM warehouse WHERE is_del = 0 ) wh ON sood.warehouse_no = wh.no
        <include refid="queryStock"/>
        <if test="param.warehouseNo != null and param.warehouseNo != ''">
            AND sood.warehouse_no = #{param.warehouseNo}
        </if>
        <if test="param.itemSkuCode != null and param.itemSkuCode != ''">
            AND sood.item_sku_code LIKE concat('%', #{param.itemSkuCode}, '%')
        </if>
        <if test="param.itemName != null and param.itemName != ''">
            AND sood.item_name LIKE concat('%', #{param.itemName}, '%')
        </if>
        <if test="param.businessLine != null and param.businessLine.size() > 0">
            AND soo.business_line in
            <foreach collection="param.businessLine" item="bl" open="(" separator="," close=")">
                #{bl}
            </foreach>
        </if>
        order by soo.created_at desc
        limit #{param.offsetVal},#{param.pageSize}
    </select>

    <select id="getStockOutOrderById"
            resultType="com.daddylab.supplier.item.controller.stockout.dto.StockOutOrderViewVO">
        select soo.id,
               soo.no,
               soo.purchase_order_no        as purchaseOrderNo,
               soo.purchase_order_id        as purchaseOrderId,
               p.name                       as providerName,
               o.name                       as purchaseOrganizationName,
               soo.organization_id          as organizationId,
               oz.name                      as organizationName,
               soo.purchase_organization_id as purchaseOrganizationId,
               soo.total_return_quantity    as totalReturnQuantity,
               soo.return_type              as returnType,
               soo.return_mode              as returnMode,
               soo.created_uid              as createdUid,
               soo.created_at               as createdAt,
               soo.is_sync_wdt              as isSyncWdt,
               soo.approval_at              as approvalAt,
               soo.approval_id              as approvalId,
               soo.state,
               soo.remark,
               soo.provider_id              as providerId,
               soo.sync_state               as syncState,
               soo.sync_msg                 as syncMsg,
               soo.business_line            as businessLine,
               soo.hedge_status             as hedgeStatus,
               soo.workbench_process_id,
               soo.outbound_time
        from stock_out_order soo
                 left join (SELECT id, name FROM provider WHERE is_del = 0) p on soo.provider_id = p.id
                 left join (SELECT id, name FROM organization WHERE is_del = 0) o
                           on soo.purchase_organization_id = o.id
                 left join (SELECT id, name FROM organization WHERE is_del = 0) oz
                           on soo.purchase_organization_id = oz.id
        where soo.is_del = 0
          and soo.id = #{id}
    </select>

    <update id="updateApprovalById">
        update stock_out_order
        set state       = #{state},
            updated_at  = now(),
            updated_uid = #{updatedAt},
            approval_at = #{approvalAt}
        where id = #{id}
          AND is_del = 0
    </update>

    <select id="getStockOutOrderItemSum" resultType="java.lang.Integer">
        SELECT
        SUM(IFNULL(sood.return_quantity,0))
        FROM
        stock_out_order soo
        LEFT JOIN
        stock_out_order_detail sood ON soo.id = sood.stock_out_order_id
        WHERE
        soo.is_del = 0
        AND sood.is_del = 0
        AND soo.is_sync_wdt = #{isSyncWdt}
        AND soo.purchase_order_id = #{purchaseOrderId}
        AND sood.item_sku_code = #{itemSkuCode}
        AND sood.warehouse_no = #{warehouseNo}
        AND soo.state != 5
        <if test="stockOutOrderId != null">
            AND soo.id != #{stockOutOrderId}
        </if>
        <if test="isGift != null">
            AND sood.is_gift = #{isGift}
        </if>
        GROUP BY
        soo.purchase_order_id
    </select>

    <update id="updateWdtPurchaseOrderNo">
        UPDATE stock_out_order
        SET wdt_storage_no = #{wdtPurchaseOrderNo}
        WHERE id = #{id}
    </update>


    <sql id="queryStock">
        <where>
            soo.is_del = 0
            <if test="param.no != null and param.no != ''">
                and soo.no LIKE concat('%', #{param.no}, '%')
            </if>
            <if test="param.noList != null and param.noList.size() >0 ">
                and soo.no in
                <foreach collection="param.noList" item="no" open="(" separator="," close=")">
                    #{no}
                </foreach>
            </if>
            <if test="param.providerId != null and param.providerId != ''">
                and soo.provider_id = #{param.providerId}
            </if>
            <if test="param.returnType != null and param.returnType != ''">
                and soo.return_type = #{param.returnType}
            </if>
            <if test="param.returnMode != null and param.returnMode != ''">
                and soo.return_mode = #{param.returnMode}
            </if>
            <if test="param.state != null and param.state != ''">
                and soo.state = #{param.state}
            </if>
            <if test="param.organizationId != null and param.organizationId != ''">
                and soo.organization_id = #{param.organizationId}
            </if>
            <if test="param.createdUidList != null and param.createdUidList.size()>0 ">
                and soo.created_uid in
                <foreach collection="param.createdUidList" item="createdId" open="(" separator="," close=")">
                    #{createdId}
                </foreach>
            </if>
            <if test="param.neCreatedUidList != null and param.neCreatedUidList.size()>0 ">
                and soo.created_uid not in
                <foreach collection="param.neCreatedUidList" item="neCreatedUid" open="(" separator="," close=")">
                    #{neCreatedUid}
                </foreach>
            </if>
            <if test="param.createdAtStart != null and param.createdAtStart != ''">
                and soo.created_at &gt;= #{param.createdAtStart}
            </if>
            <if test="param.createdAtEnd != null and param.createdAtEnd != ''">
                and soo.created_at &lt;= #{param.createdAtEnd}
            </if>
            <if test="param.purchaseOrderNo != null and param.purchaseOrderNo != ''">
                and soo.purchase_order_no LIKE concat('%', #{param.purchaseOrderNo}, '%')
            </if>
            <if test="param.idList != null and param.idList.size()>0 ">
                and soo.id in
                <foreach collection="param.idList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="param.businessLine != null and param.businessLine.size()>0 ">
                and soo.business_line in
                <foreach collection="param.businessLine" item="bl" open="(" separator="," close=")">
                    #{bl}
                </foreach>
            </if>
            <if test="param.outboundTimeStart != null and param.outboundTimeEnd != ''">
                and soo.outbound_Time between #{param.outboundTimeStart} and #{param.outboundTimeEnd}
            </if>
        </where>
    </sql>
</mapper>
