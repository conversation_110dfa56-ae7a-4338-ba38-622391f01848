<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemSkuInventorySettingMapper">
    <insert id="saveOrUpdateBatchByOuterId">
        INSERT INTO `platform_item_sku_inventory_setting` (`created_at`, `created_uid`, `updated_at`, `updated_uid`,
                                                           `outer_item_id`, `outer_sku_id`, `inventory_ratio`,
                                                           `platform`, `shop_no`, `sku_code`,
                                                           `combination_code`
        <if test="models[0].type != null">
            , `type`
        </if>)
    VALUES
        <foreach item="item" index="index" collection="models" open="" separator="," close="">
            (
            <choose>
                <when test="item.createdAt != null">
                    #{item.createdAt},
                </when>
                <otherwise>
                    <!--@ignoreSql-->
                    UNIX_TIMESTAMP(),
                </otherwise>
            </choose>
            <choose>
                <when test="item.createdUid != null">
                    #{item.createdUid},
                </when>
                <otherwise>
                    <!--@ignoreSql-->
                    0,
                </otherwise>
            </choose>
            <choose>
                <when test="item.updatedAt != null">
                    #{item.updatedAt},
                </when>
                <otherwise>
                    <!--@ignoreSql-->
                    UNIX_TIMESTAMP(),
                </otherwise>
            </choose>
            <choose>
                <when test="item.updatedUid != null">
                    #{item.updatedUid},
                </when>
                <otherwise>
                    <!--@ignoreSql-->
                    0,
                </otherwise>
            </choose>
            #{item.outerItemId},
            #{item.outerSkuId}, #{item.inventoryRatio}, #{item.platform},
            #{item.shopNo},
            #{item.skuCode}, #{item.combinationCode}
            <if test="item.type != null">
                , #{item.type}
            </if>
            )
        </foreach>
        ON DUPLICATE KEY
        UPDATE `updated_at`      = VALUES(`updated_at`)
             , `updated_uid`     = VALUES(`updated_uid`)
             , `inventory_ratio` = VALUES(`inventory_ratio`)
             , `platform`        = VALUES(`platform`)
             , `shop_no`         = VALUES(`shop_no`)
        <if test="models[0].type != null">
            , `type` = VALUES(`type`)
        </if>
        ;
    </insert>
</mapper>
