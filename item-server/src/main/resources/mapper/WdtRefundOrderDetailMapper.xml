<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundOrderDetailMapper">
    <insert id="saveOrUpdateBatch">
        INSERT INTO wdt_refund_order_detail (id, refund_no, rec_id, oid, trade_order_id,
                                             platform_id, tid, trade_no, num, price, original_price,
                                             refund_num, total_amount, refund_amount, is_guarantee,
                                             goods_no, goods_name, spec_name, spec_no, goods_id,
                                             spec_id, sys_goods_id, sys_spec_id, spec_code, barcode,
                                             stockin_num, remark, api_spec_name, api_goods_name,
                                             modified, suite_no, suite_name, raw_refund_nos)
        VALUES
        <foreach collection="details" item="item" separator=",">
            (NULL, #{item.refundNo}, #{item.recId}, #{item.oid}, #{item.tradeOrderId},
             #{item.platformId}, #{item.tid}, #{item.tradeNo}, #{item.num}, #{item.price},
             #{item.originalPrice},
             #{item.refundNum}, #{item.totalAmount}, #{item.refundAmount}, #{item.isGuarantee},
             #{item.goodsNo}, #{item.goodsName}, #{item.specName}, #{item.specNo}, #{item.goodsId},
             #{item.specId}, #{item.sysGoodsId}, #{item.sysSpecId}, #{item.specCode},
             #{item.barcode},
             #{item.stockinNum}, #{item.remark}, #{item.apiSpecName}, #{item.apiGoodsName},
             #{item.modified}, #{item.suiteNo}, #{item.suiteName}, #{item.rawRefundNos})
        </foreach>
        ON DUPLICATE KEY UPDATE refund_no      = VALUES(refund_no),
                                oid            = VALUES(oid),
                                trade_order_id = VALUES(trade_order_id),
                                platform_id    = VALUES(platform_id),
                                tid            = VALUES(tid),
                                trade_no       = VALUES(trade_no),
                                num            = VALUES(num),
                                price          = VALUES(price),
                                original_price = VALUES(original_price),
                                refund_num     = VALUES(refund_num),
                                total_amount   = VALUES(total_amount),
                                refund_amount  = VALUES(refund_amount),
                                is_guarantee   = VALUES(is_guarantee),
                                goods_no       = VALUES(goods_no),
                                goods_name     = VALUES(goods_name),
                                spec_name      = VALUES(spec_name),
                                spec_no        = VALUES(spec_no),
                                goods_id       = VALUES(goods_id),
                                spec_id        = VALUES(spec_id),
                                sys_goods_id   = VALUES(sys_goods_id),
                                sys_spec_id    = VALUES(sys_spec_id),
                                spec_code      = VALUES(spec_code),
                                barcode        = VALUES(barcode),
                                stockin_num    = VALUES(stockin_num),
                                remark         = VALUES(remark),
                                api_spec_name  = VALUES(api_spec_name),
                                api_goods_name = VALUES(api_goods_name),
                                modified       = VALUES(modified),
                                suite_no       = VALUES(suite_no),
                                suite_name     = VALUES(suite_name),
                                raw_refund_nos = VALUES(raw_refund_nos)
    </insert>

    <select id="selectRefundList"
            resultType="com.daddylab.supplier.item.application.purchase.order.factory.dto.WdtRefundManageDO">
        select
            wrod.spec_no as skuCode,
            wo.trade_time ,
            wo.platform_id,
            wrod.refund_num,
            wrod.refund_no,
            wo.trade_no,
            wrod.suite_no,
            wro.return_warehouse_no as warehouseNo
        from wdt_refund_order_detail wrod
        left join wdt_refund_order wro on wrod.refund_no = wro.refund_no
        left join wdt_order wo on wrod.trade_no = wo.trade_no
        where wro.check_time <![CDATA[>=]]> #{startDt}
        and wro.check_time <![CDATA[<=]]> #{endDt}
        -- 剔除掉退款状态是 【已取消】和【待审核】状态的数据。
        and wro.status not in (10, 20)
        -- 类型退货
        and wro.type = 2
        and wro.shop_no not in
        <foreach collection="eliminateWarehouseNos" item="warehouseNo" open="(" separator="," close=")">
            #{warehouseNo}
        </foreach>
        and wro.return_warehouse_no not in
        <foreach collection="externalShopNos" item="shopNo" open="(" separator="," close=")">
            #{shopNo}
        </foreach>
        <if test="skuCode != null and skuCode != ''">
            AND wssood.spec_no = #{skuCode}
        </if>
    </select>
</mapper>
