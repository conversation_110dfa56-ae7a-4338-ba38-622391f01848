<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DadListItemMapper">
    <delete id="deleteData">
        delete from dad_list_item
        where erp_item_code in
        <foreach collection="itemCodeList" item="itemCode" open="(" separator="," close=")">
            #{itemCode}
        </foreach>
    </delete>

    <delete id="deleteAllData">
        delete from dad_list_item
        where  id > 0
    </delete>
</mapper>
