<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemMapper">

<select id="countOnSalePlatformItem" resultType="Long">
    SELECT COUNT(DISTINCT `pi`.`id`) AS `onSaleCount`
    FROM `item` `i`
             JOIN `item_sku` `is2` ON `i`.`id` = `is2`.`item_id`
             JOIN `platform_item_sku` `pis` ON `pis`.`sku_code` = `is2`.`sku_code`
             JOIN `platform_item` `pi` ON `pis`.`platform_item_id` = `pi`.`id`
    WHERE `i`.`id` = #{itemId}
    <if test="platform != null">
        AND pis.platform = #{platform}
    </if>
      AND `pi`.`status` = 1
      AND `pi`.`is_del` = 0
      AND `pis`.`is_del` = 0
      AND `i`.`is_del` = 0
      AND `is2`.`is_del` = 0
    ;
    </select>

    <select id="shopSnList" resultType="java.lang.String">
        select distinct shop_no from platform_item;
    </select>
</mapper>
