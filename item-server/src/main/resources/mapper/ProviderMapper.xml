<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ProviderMapper">
    <select id="dropDownList" resultType="com.daddylab.supplier.item.controller.provider.dto.ProviderDropDownVo">
        SELECT p.`name`,p.`id`,p.`status`,fi.`tax_rate` as rate,p.`main_charger_user_id` , p.`second_charger_user_id`,
            p.`partner_provider_id`
        from provider p
        left join finance_info fi on p.id = fi.provider_id
        <where>
            p.`is_del` = 0
            <if test="name != null and name != ''">
                and p.`name` like concat('%', #{name}, '%')
            </if>
<!--            <if test="businessLine != null and businessLine.size() != 0">-->
<!--                AND-->
<!--                <foreach item="lineId" index="index" collection="businessLine" open="(" separator="OR" close=")">-->
<!--                    is_business_line${lineId} = 1-->
<!--                </foreach>-->
<!--            </if>-->
        </where>
        limit #{offset},#{size}
    </select>
    <select id="getListBySkuCodeList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuCodeProviderDO">
        select
        sku.sku_code as skuCode,
        ii.provider_id as providerId
        from
        item_sku sku
        left join item ii on sku.item_id = ii.id
        where
        sku.is_del = 0
        and sku.sku_code in
        <foreach item="code" index="index" collection="list" open="(" close=")" separator=",">
            #{code}
        </foreach>
    </select>

    <select id="getProviderById" resultType="com.daddylab.supplier.item.controller.provider.dto.ProviderVO">
        SELECT p.id                        AS id,
               p.NAME                      AS NAME,
               p.contact                   AS contact,
               p.contact_mobile            AS contactMobile,
               p.unify_social_credit_codes AS unifySocialCreditCodes,
               p.province_code             AS provinceCode,
               p.city_code                 AS cityCode,
               p.area_code                 AS areaCode,
               p.address                   AS address,
               p.STATUS                    AS STATUS,
               p.main_charger_user_id,
               p.second_charger_user_id,
               IFNULL(p.type, 1)           AS type,
               p.partner_provider_id       AS partnerProviderId,
               p.provider_no               AS providerNo,
               fi.invoice_type             AS invoiceType,
               fi.currency                 AS currency,
               fi.tax_type                 AS taxType,
               fi.tax_rate                 AS taxRate,
               p.mall_shop_id              AS mallShopId,
               p.business_line             AS businessLine
        FROM provider p
                 LEFT JOIN (SELECT id, provider_id, invoice_type, currency, tax_type, tax_rate
                            FROM finance_info
                            WHERE is_del = 0) fi ON p.id = fi.provider_id
        WHERE p.is_del = 0
          AND p.id = #{id}
    </select>

    <select id="verifySkuCodeInTargetProvider" resultType="java.lang.String">
        select
        sku_code
        from
        item_sku sku
        inner join item i on sku.item_id = i.id
        where
        sku.is_del = 0
        and i.is_del = 0
        and i.provider_id = #{providerId}
        and sku.sku_code in
        <foreach item="code" index="index" collection="list" open="(" close=")" separator=",">
            #{code}
        </foreach>
    </select>

    <select id="getProviderPOByNoIncludeLogicDeleted"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider">
        SELECT *
        FROM provider
        WHERE provider_no = #{providerNo}
        LIMIT 1
    </select>

    <select id="getProviderBankInfoDtoList"
            resultType="com.daddylab.supplier.item.controller.test.dto.ProviderExportInfoDto">
        select pp.name
        , pp.unify_social_credit_codes
        , pp.main_charger_user_id
        , pp.second_charger_user_id
        <!--             , ba.bank_card-->
        <!--             , ba.bank_deposit-->
        <!--             , ba.bank_no-->
        , pp.status
        , pp.type
        , pp.business_line
        from `provider` as pp
        left join bank_account ba on pp.id = ba.provider_id
        where pp.is_del = 0
        and pp.created_at >= **********
    </select>
</mapper>
