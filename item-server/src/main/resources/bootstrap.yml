spring:
  profiles:
    active: '@profileActive@'
  application:
    name: ms-supplier-item
  cloud:
    nacos:
      config:
        server-addr: ${nacos.server}
        username: ${nacos.username}
        password: ${nacos.password}
        name: ${spring.application.name}
        file-extension: yaml
        namespace: ${spring.profiles.active}
        group: ${spring.application.name}
        max-retry: 3
        config-retry-time: 3000
        refresh-enabled: true

#        server-addr: 172.16.12.151:8848,172.16.12.193:8848,172.16.12.172:8848
#        username: nacos
#        password: 82CVXbpDsTabVS
#        name: ms-supplier-item
#        file-extension: yaml
#        namespace: test
#        group: ms-supplier-item
#        max-retry: 3
#        config-retry-time: 3000
#        refresh-enabled: true

server:
  servlet:
    encoding:
      charset: UTF-8
      force-response: true
      force-request: false
dev:
  passwords:
    - f9dd696f5884ae4b479039acac824f36

management:
  health:
    mail:
      enabled: false

logging:
  level:
    com:
      alibaba:
        nacos: info
        cloud:
          nacos: info

# 计算图像识别
cv:
  subscriptionKey: 78df0f545e2d4322a514c1a306245ce6
  endpoint: https://daddylab-test.cognitiveservices.azure.cn/

# 流程引擎配置
flowable:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************
    username: root
    password: P@ssw0rd
  database-schema-update: false
  eventregistry:
    enabled: false
  idm:
    enabled: false
  http:
    connect-timeout: 5s
    socket-timeout: 15s
