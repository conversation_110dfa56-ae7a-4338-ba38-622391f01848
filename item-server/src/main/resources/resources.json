{"msg": "success", "code": 200, "data": [{"id": 6, "parent_id": 0, "system_id": 6, "front_url": "", "component": "", "api_url": "", "name": "ERP系统", "code": "", "type": "s", "hide": 0, "sort": 0, "icon": "", "updated_at": 0, "created_at": 0, "deleted_at": 0}, {"id": 2860, "parent_id": 6, "system_id": 6, "front_url": "/commodity-management", "component": "", "api_url": "", "name": "商品中心", "code": "", "type": "l", "hide": 0, "sort": 0, "icon": "", "updated_at": 1636701976, "created_at": 1636701976, "deleted_at": 0}, {"id": 2862, "parent_id": 2860, "system_id": 6, "front_url": "/back-end-good", "component": "", "api_url": "", "name": "后端商品", "code": "", "type": "l", "hide": 0, "sort": 0, "icon": "", "updated_at": 1638435453, "created_at": 1638435453, "deleted_at": 0}, {"id": 2864, "parent_id": 2860, "system_id": 6, "front_url": "/category-list", "component": "", "api_url": "", "name": "品类列表", "code": "", "type": "l", "hide": 0, "sort": 0, "icon": "", "updated_at": 1638435509, "created_at": 1638435509, "deleted_at": 0}, {"id": 2866, "parent_id": 2860, "system_id": 6, "front_url": "/brand-list", "component": "", "api_url": "", "name": "品牌列表", "code": "", "type": "l", "hide": 0, "sort": 0, "icon": "", "updated_at": 1638435517, "created_at": 1638435517, "deleted_at": 0}, {"id": 2867, "parent_id": 2860, "system_id": 6, "front_url": "/supplier-list", "component": "", "api_url": "", "name": "供应商列表", "code": "", "type": "l", "hide": 0, "sort": 0, "icon": "", "updated_at": 1638435550, "created_at": 1638435550, "deleted_at": 0}, {"id": 2870, "parent_id": 2860, "system_id": 6, "front_url": "/shop-list", "component": "", "api_url": "", "name": "店铺列表", "code": "", "type": "l", "hide": 0, "sort": 0, "icon": "", "updated_at": 1638435580, "created_at": 1638435580, "deleted_at": 0}, {"id": 2871, "parent_id": 2862, "system_id": 6, "front_url": "/add", "component": "", "api_url": "", "name": "新建商品页面", "code": "", "type": "p", "hide": 0, "sort": 0, "icon": "", "updated_at": 1638435464, "created_at": 1638435464, "deleted_at": 0}, {"id": 2874, "parent_id": 2862, "system_id": 6, "front_url": "/edit", "component": "", "api_url": "", "name": "编辑商品页面", "code": "", "type": "p", "hide": 0, "sort": 0, "icon": "", "updated_at": 1638435472, "created_at": 1638435472, "deleted_at": 0}, {"id": 2877, "parent_id": 2862, "system_id": 6, "front_url": "/review", "component": "", "api_url": "", "name": "查看商品页面", "code": "", "type": "p", "hide": 0, "sort": 0, "icon": "", "updated_at": 1638435480, "created_at": 1638435480, "deleted_at": 0}, {"id": 2880, "parent_id": 2862, "system_id": 6, "front_url": "", "component": "", "api_url": "/item/pageQuery", "name": "商品列表", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636702313, "created_at": 1636702313, "deleted_at": 0}, {"id": 2881, "parent_id": 2862, "system_id": 6, "front_url": "", "component": "", "api_url": "/item/export", "name": "商品导出", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636702347, "created_at": 1636702347, "deleted_at": 0}, {"id": 2883, "parent_id": 2860, "system_id": 6, "front_url": "", "component": "", "api_url": "/exportTask/excelList", "name": "导出任务列表", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703433, "created_at": 1636703433, "deleted_at": 0}, {"id": 2886, "parent_id": 2860, "system_id": 6, "front_url": "", "component": "", "api_url": "/exportTask/excelUrl", "name": "导出 excel", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636702444, "created_at": 1636702444, "deleted_at": 0}, {"id": 2888, "parent_id": 2862, "system_id": 6, "front_url": "", "component": "", "api_url": "/item/viewBase", "name": "查看商品信息", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636702476, "created_at": 1636702476, "deleted_at": 0}, {"id": 2890, "parent_id": 2862, "system_id": 6, "front_url": "", "component": "", "api_url": "/item/saveBase", "name": "新增or编辑", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636702795, "created_at": 1636702795, "deleted_at": 0}, {"id": 2892, "parent_id": 2862, "system_id": 6, "front_url": "", "component": "", "api_url": "/item/viewPrice", "name": "查看价格信息", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636702819, "created_at": 1636702819, "deleted_at": 0}, {"id": 2893, "parent_id": 2862, "system_id": 6, "front_url": "", "component": "", "api_url": "/item/expressTemplate", "name": "查看快递模版", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636702837, "created_at": 1636702837, "deleted_at": 0}, {"id": 2896, "parent_id": 2862, "system_id": 6, "front_url": "", "component": "", "api_url": "/item/operateLogs", "name": "操作记录", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636702851, "created_at": 1636702851, "deleted_at": 0}, {"id": 2899, "parent_id": 2862, "system_id": 6, "front_url": "", "component": "", "api_url": "/item/viewRunning", "name": "查看运营信息", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636702887, "created_at": 1636702887, "deleted_at": 0}, {"id": 2901, "parent_id": 2862, "system_id": 6, "front_url": "", "component": "", "api_url": "/item/updateRunning", "name": "编辑运营信息", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636702906, "created_at": 1636702906, "deleted_at": 0}, {"id": 2903, "parent_id": 2862, "system_id": 6, "front_url": "", "component": "", "api_url": "/item/queryPartnerItem", "name": "查看合作伙伴系统商品", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703075, "created_at": 1636703075, "deleted_at": 0}, {"id": 2905, "parent_id": 2862, "system_id": 6, "front_url": "", "component": "", "api_url": "/item/confirmQueryPartnerItem", "name": "确定合作伙伴系统商品", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703096, "created_at": 1636703096, "deleted_at": 0}, {"id": 2906, "parent_id": 2864, "system_id": 6, "front_url": "", "component": "", "api_url": "/category/add", "name": "添加品类", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703213, "created_at": 1636703213, "deleted_at": 0}, {"id": 2909, "parent_id": 2864, "system_id": 6, "front_url": "", "component": "", "api_url": "/category/delete", "name": "删除品类", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703228, "created_at": 1636703228, "deleted_at": 0}, {"id": 2910, "parent_id": 2864, "system_id": 6, "front_url": "", "component": "", "api_url": "/category/update", "name": "更新品类", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703244, "created_at": 1636703244, "deleted_at": 0}, {"id": 2913, "parent_id": 2864, "system_id": 6, "front_url": "", "component": "", "api_url": "/category/addAttr", "name": "添加属性", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703259, "created_at": 1636703259, "deleted_at": 0}, {"id": 2916, "parent_id": 2864, "system_id": 6, "front_url": "", "component": "", "api_url": "/category/updateAttr", "name": "更新属性", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703278, "created_at": 1636703278, "deleted_at": 0}, {"id": 2917, "parent_id": 2864, "system_id": 6, "front_url": "", "component": "", "api_url": "/category/deleteAttr", "name": "删除属性", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703294, "created_at": 1636703294, "deleted_at": 0}, {"id": 2918, "parent_id": 2864, "system_id": 6, "front_url": "", "component": "", "api_url": "/category/viewList", "name": "获取类目列表", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703316, "created_at": 1636703316, "deleted_at": 0}, {"id": 2919, "parent_id": 2864, "system_id": 6, "front_url": "", "component": "", "api_url": "/category/viewAttrList", "name": "获取属性列表", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703339, "created_at": 1636703339, "deleted_at": 0}, {"id": 2922, "parent_id": 2860, "system_id": 6, "front_url": "", "component": "", "api_url": "/user/staffDropDownList", "name": "获取员工列表", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703402, "created_at": 1636703402, "deleted_at": 0}, {"id": 2923, "parent_id": 2860, "system_id": 6, "front_url": "", "component": "", "api_url": "/file/uploadImage", "name": "图片上传", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703418, "created_at": 1636703418, "deleted_at": 0}, {"id": 2925, "parent_id": 2860, "system_id": 6, "front_url": "", "component": "", "api_url": "/exportTask/clearTaskList", "name": "清空导出任务列表", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703442, "created_at": 1636703442, "deleted_at": 0}, {"id": 2926, "parent_id": 2866, "system_id": 6, "front_url": "", "component": "", "api_url": "/brand/dropDownBrandList", "name": "品牌搜索", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703481, "created_at": 1636703481, "deleted_at": 0}, {"id": 2928, "parent_id": 2866, "system_id": 6, "front_url": "", "component": "", "api_url": "/brand/createOrUpdateBrand", "name": "创建品牌", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703551, "created_at": 1636703551, "deleted_at": 0}, {"id": 2929, "parent_id": 2866, "system_id": 6, "front_url": "", "component": "", "api_url": "/brand/deleteBrand", "name": "品牌删除", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703571, "created_at": 1636703571, "deleted_at": 0}, {"id": 2930, "parent_id": 2866, "system_id": 6, "front_url": "", "component": "", "api_url": "/brand/getBrandDetail", "name": "品牌详情", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703603, "created_at": 1636703603, "deleted_at": 0}, {"id": 2932, "parent_id": 2866, "system_id": 6, "front_url": "", "component": "", "api_url": "/brand/queryBrandList", "name": "品牌列表", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636703664, "created_at": 1636703664, "deleted_at": 0}, {"id": 2935, "parent_id": 2866, "system_id": 6, "front_url": "", "component": "", "api_url": "/brand/operateLogs", "name": "操作记录", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636704163, "created_at": 1636704163, "deleted_at": 0}, {"id": 2939, "parent_id": 2866, "system_id": 6, "front_url": "", "component": "", "api_url": "/brand/importExcel", "name": "品牌导入", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636704336, "created_at": 1636704336, "deleted_at": 0}, {"id": 2942, "parent_id": 2866, "system_id": 6, "front_url": "", "component": "", "api_url": "/brand/createExportBrandTask", "name": "品牌导出任务", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636704351, "created_at": 1636704351, "deleted_at": 0}, {"id": 2943, "parent_id": 2862, "system_id": 6, "front_url": "", "component": "", "api_url": "/item/nameDropDown", "name": "商品名模糊搜索", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636704403, "created_at": 1636704403, "deleted_at": 0}, {"id": 2946, "parent_id": 2862, "system_id": 6, "front_url": "", "component": "", "api_url": "/item/buyerDropDown", "name": "采购员模糊搜索", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1636704429, "created_at": 1636704429, "deleted_at": 0}, {"id": 2961, "parent_id": 2867, "system_id": 6, "front_url": "/add", "component": "", "api_url": "", "name": "新增供应商页面", "code": "", "type": "p", "hide": 0, "sort": 0, "icon": "", "updated_at": 1638435558, "created_at": 1638435558, "deleted_at": 0}, {"id": 2963, "parent_id": 2867, "system_id": 6, "front_url": "/edit", "component": "", "api_url": "", "name": "编辑供应商页面", "code": "", "type": "p", "hide": 0, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 2965, "parent_id": 2867, "system_id": 6, "front_url": "/detail", "component": "", "api_url": "", "name": "查看供应商页面", "code": "", "type": "p", "hide": 0, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 2967, "parent_id": 2867, "system_id": 6, "front_url": "", "component": "", "api_url": "/provider/contactList", "name": "联系人模糊搜索", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 2970, "parent_id": 2867, "system_id": 6, "front_url": "", "component": "", "api_url": "/provider/addOrUpdate", "name": "新增供应商", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 2973, "parent_id": 2867, "system_id": 6, "front_url": "", "component": "", "api_url": "/provider/remove", "name": "删除供应商", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 2975, "parent_id": 2867, "system_id": 6, "front_url": "", "component": "", "api_url": "/provider/view", "name": "详情接口", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 2976, "parent_id": 2867, "system_id": 6, "front_url": "", "component": "", "api_url": "/provider/fuzzyQuery", "name": "查询合作伙伴系统", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 2978, "parent_id": 2867, "system_id": 6, "front_url": "", "component": "", "api_url": "/region/getRegionList", "name": "省市区级联", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 2984, "parent_id": 2867, "system_id": 6, "front_url": "", "component": "", "api_url": "/provider/operateLogs", "name": "操作记录", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 2986, "parent_id": 2870, "system_id": 6, "front_url": "/add", "component": "", "api_url": "", "name": "新增店铺页面", "code": "", "type": "p", "hide": 0, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 2989, "parent_id": 2870, "system_id": 6, "front_url": "/edit", "component": "", "api_url": "", "name": "编辑店铺页面", "code": "", "type": "p", "hide": 0, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 2992, "parent_id": 2870, "system_id": 6, "front_url": "/detail", "component": "", "api_url": "", "name": "查看店铺页面", "code": "", "type": "p", "hide": 0, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 2995, "parent_id": 2870, "system_id": 6, "front_url": "", "component": "", "api_url": "/shop/dropDownShopList", "name": "名称搜索", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1637029512, "created_at": 1637029512, "deleted_at": 0}, {"id": 2998, "parent_id": 2870, "system_id": 6, "front_url": "", "component": "", "api_url": "/shop/createOrUpdateShop", "name": "创建店铺", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1637029531, "created_at": 1637029531, "deleted_at": 0}, {"id": 3001, "parent_id": 2870, "system_id": 6, "front_url": "", "component": "", "api_url": "/shop/getShopDetail", "name": "店铺详情", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1637029545, "created_at": 1637029545, "deleted_at": 0}, {"id": 3004, "parent_id": 2870, "system_id": 6, "front_url": "", "component": "", "api_url": "/shop/queryShopList", "name": "店铺列表", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1637029561, "created_at": 1637029561, "deleted_at": 0}, {"id": 3007, "parent_id": 2870, "system_id": 6, "front_url": "", "component": "", "api_url": "/shop/operateLogs", "name": "操作记录", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1637029573, "created_at": 1637029573, "deleted_at": 0}, {"id": 3048, "parent_id": 3015, "system_id": 6, "front_url": "/purchase-price", "component": "", "api_url": "", "name": "采购价格", "code": "", "type": "l", "hide": 0, "sort": 0, "icon": "", "updated_at": 1638435795, "created_at": 1638435795, "deleted_at": 0}, {"id": 3051, "parent_id": 3048, "system_id": 6, "front_url": "", "component": "", "api_url": "/purchase/viewList", "name": "查看采购列表", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1637399938, "created_at": 1637399938, "deleted_at": 0}, {"id": 3054, "parent_id": 3048, "system_id": 6, "front_url": "", "component": "", "api_url": "/purchase/importExcel", "name": "采购导入", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1637399954, "created_at": 1637399954, "deleted_at": 0}, {"id": 3057, "parent_id": 3048, "system_id": 6, "front_url": "", "component": "", "api_url": "/purchase/exportPurchaseList", "name": "采购导出", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1637399968, "created_at": 1637399968, "deleted_at": 0}, {"id": 3060, "parent_id": 3048, "system_id": 6, "front_url": "", "component": "", "api_url": "/purchase/createOrUpdatePurchase", "name": "新增或编辑", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1637400028, "created_at": 1637400028, "deleted_at": 0}, {"id": 3063, "parent_id": 3048, "system_id": 6, "front_url": "", "component": "", "api_url": "/purchaseTable/getMonth", "name": "获取月份", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1637400040, "created_at": 1637400040, "deleted_at": 0}, {"id": 3066, "parent_id": 3048, "system_id": 6, "front_url": "", "component": "", "api_url": "/purchase/delete", "name": "删除价格", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 3069, "parent_id": 3048, "system_id": 6, "front_url": "", "component": "", "api_url": "/purchase/reset", "name": "重置状态", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 3072, "parent_id": 3015, "system_id": 6, "front_url": "", "component": "", "api_url": "/purchase/providerList", "name": "供应商下拉选择", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 3075, "parent_id": 3048, "system_id": 6, "front_url": "", "component": "", "api_url": "/purchase/doSort", "name": "拖拽排序", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 3078, "parent_id": 3048, "system_id": 6, "front_url": "", "component": "", "api_url": "/purchaseTable/selectByMonth", "name": "根据月份查权限", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 3081, "parent_id": 3048, "system_id": 6, "front_url": "", "component": "", "api_url": "/purchaseTable/update", "name": "控制编辑权限", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 3188, "parent_id": 3187, "system_id": 6, "front_url": "/list", "component": "", "api_url": "", "name": "平台商品列表页面", "code": "", "type": "p", "hide": 0, "sort": 0, "icon": "", "updated_at": 1640058450, "created_at": 1640058450, "deleted_at": 0}, {"id": 3195, "parent_id": 3193, "system_id": 6, "front_url": "/info-setting", "component": "", "api_url": "", "name": "消息设置", "code": "", "type": "l", "hide": 0, "sort": 0, "icon": "", "updated_at": 1640249219, "created_at": 1640249219, "deleted_at": 0}, {"id": 3197, "parent_id": 3184, "system_id": 6, "front_url": "", "component": "", "api_url": "/platformItemWarn/page", "name": "平台商品预警列表", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1640679652, "created_at": 1640679652, "deleted_at": 0}, {"id": 3201, "parent_id": 3184, "system_id": 6, "front_url": "", "component": "", "api_url": "/platformItemWarn/handle", "name": "平台商品预警处理", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1640754595, "created_at": 1640754595, "deleted_at": 0}, {"id": 3204, "parent_id": 3187, "system_id": 6, "front_url": "", "component": "", "api_url": "/platformItem/page", "name": "平台商品分页查询", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1640754728, "created_at": 1640754728, "deleted_at": 0}, {"id": 3207, "parent_id": 3187, "system_id": 6, "front_url": "", "component": "", "api_url": "/platformItem/detail", "name": "获取平台商品详情数据", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1640754743, "created_at": 1640754743, "deleted_at": 0}, {"id": 3210, "parent_id": 3187, "system_id": 6, "front_url": "", "component": "", "api_url": "/platformItem/sync", "name": "同步", "code": "", "type": "a", "hide": 1, "sort": 0, "icon": "", "updated_at": 1640754785, "created_at": 1640754785, "deleted_at": 0}, {"id": 3227, "parent_id": 3195, "system_id": 6, "front_url": "", "component": "", "api_url": "/msg/config", "name": "消息配置", "code": "", "type": "a", "hide": 0, "sort": 0, "icon": "", "updated_at": 1640831525, "created_at": 1640831525, "deleted_at": 0}, {"id": 3230, "parent_id": 3195, "system_id": 6, "front_url": "", "component": "", "api_url": "/msg/saveConfig", "name": "保存消息配置", "code": "", "type": "a", "hide": 0, "sort": 0, "icon": "", "updated_at": 1640831553, "created_at": 1640831553, "deleted_at": 0}, {"id": 19680, "parent_id": 19677, "system_id": 6, "front_url": "/list", "component": "", "api_url": "", "name": "组合商品列表页面", "code": "", "type": "p", "hide": 0, "sort": 0, "icon": "", "updated_at": 1641365664, "created_at": 1641365664, "deleted_at": 0}, {"id": 19683, "parent_id": 19677, "system_id": 6, "front_url": "/add", "component": "", "api_url": "", "name": "新建组合商品页面", "code": "", "type": "p", "hide": 0, "sort": 0, "icon": "", "updated_at": 1641365721, "created_at": 1641365721, "deleted_at": 0}, {"id": 19686, "parent_id": 19677, "system_id": 6, "front_url": "/edit", "component": "", "api_url": "", "name": "编辑组合商品页面", "code": "", "type": "p", "hide": 0, "sort": 0, "icon": "", "updated_at": 1641365760, "created_at": 1641365760, "deleted_at": 0}, {"id": 19689, "parent_id": 19677, "system_id": 6, "front_url": "/detail", "component": "", "api_url": "", "name": "查看组合商品页面", "code": "", "type": "p", "hide": 0, "sort": 0, "icon": "", "updated_at": 1641365796, "created_at": 1641365796, "deleted_at": 0}, {"id": 79628, "parent_id": 79625, "system_id": 6, "front_url": "/order-list", "component": "", "api_url": "", "name": "订单列表", "code": "", "type": "l", "hide": 0, "sort": 0, "icon": "", "updated_at": 1648091557, "created_at": 1648091557, "deleted_at": 0}, {"id": 79631, "parent_id": 79625, "system_id": 6, "front_url": "/order-detail", "component": "", "api_url": "", "name": "订单详情", "code": "", "type": "l", "hide": 1, "sort": 0, "icon": "", "updated_at": 1648091594, "created_at": 1648091594, "deleted_at": 0}, {"id": 79634, "parent_id": 79625, "system_id": 6, "front_url": "/return-list", "component": "", "api_url": "", "name": "退换列表", "code": "", "type": "l", "hide": 0, "sort": 0, "icon": "", "updated_at": 1648091903, "created_at": 1648091903, "deleted_at": 0}, {"id": 79637, "parent_id": 79625, "system_id": 6, "front_url": "/return-detail", "component": "", "api_url": "", "name": "退换详情", "code": "", "type": "l", "hide": 1, "sort": 0, "icon": "", "updated_at": 1648091661, "created_at": 1648091661, "deleted_at": 0}, {"id": 2951, "parent_id": 2862, "system_id": 6, "front_url": "", "component": "", "api_url": "savePrice", "name": "编辑价格", "code": "", "type": "a", "hide": 1, "sort": 1, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 2954, "parent_id": 2866, "system_id": 6, "front_url": "/add", "component": "", "api_url": "", "name": "新增品牌页面", "code": "", "type": "p", "hide": 0, "sort": 1, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 3008, "parent_id": 2867, "system_id": 6, "front_url": "", "component": "", "api_url": "/provider/dropDown", "name": "供应商选择", "code": "", "type": "a", "hide": 1, "sort": 1, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 3015, "parent_id": 6, "system_id": 6, "front_url": "/commodity-purchase", "component": "", "api_url": "", "name": "采购中心", "code": "", "type": "l", "hide": 0, "sort": 1, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 3084, "parent_id": 3048, "system_id": 6, "front_url": "", "component": "", "api_url": "/purchase/viewList/all", "name": "查看采购列表全部数据", "code": "", "type": "a", "hide": 1, "sort": 1, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 3184, "parent_id": 2860, "system_id": 6, "front_url": "/platform-goods-alarm-list", "component": "", "api_url": "", "name": "平台商品预警", "code": "", "type": "l", "hide": 0, "sort": 1, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 3212, "parent_id": 3187, "system_id": 6, "front_url": "", "component": "", "api_url": "/platformItem/otherPlatform/page", "name": "其他平台商品", "code": "", "type": "a", "hide": 1, "sort": 1, "icon": "", "updated_at": 1640762893, "created_at": 1640762893, "deleted_at": 0}, {"id": 27939, "parent_id": 19677, "system_id": 6, "front_url": "", "component": "", "api_url": "/combination/pageItem", "name": "组合商品列表", "code": "", "type": "a", "hide": 1, "sort": 1, "icon": "", "updated_at": 1642064401, "created_at": 1642064401, "deleted_at": 0}, {"id": 2953, "parent_id": 2862, "system_id": 6, "front_url": "", "component": "", "api_url": "savesSpecial", "name": "编辑指定编码", "code": "", "type": "a", "hide": 1, "sort": 2, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 2956, "parent_id": 2866, "system_id": 6, "front_url": "/edit", "component": "", "api_url": "", "name": "编辑品牌页面", "code": "", "type": "p", "hide": 0, "sort": 2, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 3010, "parent_id": 2867, "system_id": 6, "front_url": "", "component": "", "api_url": "/provider/viewList", "name": "供应商列表", "code": "", "type": "a", "hide": 1, "sort": 2, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 3087, "parent_id": 3048, "system_id": 6, "front_url": "", "component": "", "api_url": "/purchase/initiate", "name": "发起供应商确认", "code": "", "type": "a", "hide": 1, "sort": 2, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 3187, "parent_id": 2860, "system_id": 6, "front_url": "/platform-goods", "component": "", "api_url": "", "name": "平台商品", "code": "", "type": "l", "hide": 0, "sort": 2, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 3193, "parent_id": 6, "system_id": 6, "front_url": "/system-settings", "component": "", "api_url": "", "name": "系统设置", "code": "", "type": "l", "hide": 0, "sort": 2, "icon": "", "updated_at": **********, "created_at": **********, "deleted_at": 0}, {"id": 3215, "parent_id": 3187, "system_id": 6, "front_url": "", "component": "", "api_url": "/platformItem/dropdown", "name": "平台商品下拉列表", "code": "", "type": "a", "hide": 1, "sort": 2, "icon": "", "updated_at": 1640766365, "created_at": 1640766365, "deleted_at": 0}, {"id": 27940, "parent_id": 19677, "system_id": 6, "front_url": "", "component": "", "api_url": "/combination/save", "name": "保存组合商品", "code": "", "type": "a", "hide": 1, "sort": 2, "icon": "", "updated_at": 1642064592, "created_at": 1642064592, "deleted_at": 0}, {"id": 2958, "parent_id": 2866, "system_id": 6, "front_url": "/detail", "component": "", "api_url": "", "name": "查看品牌页面", "code": "", "type": "p", "hide": 0, "sort": 3, "icon": "", "updated_at": 1638435543, "created_at": 1638435543, "deleted_at": 0}, {"id": 3013, "parent_id": 2862, "system_id": 6, "front_url": "", "component": "", "api_url": "/item/simpleQuery", "name": "查询商品简易信息", "code": "", "type": "a", "hide": 1, "sort": 3, "icon": "", "updated_at": 1637048697, "created_at": 1637048697, "deleted_at": 0}, {"id": 3092, "parent_id": 3048, "system_id": 6, "front_url": "", "component": "", "api_url": "/purchase/getPurchaseDetail", "name": "查看采购数据详情", "code": "", "type": "a", "hide": 1, "sort": 3, "icon": "", "updated_at": 1637805672, "created_at": 1637805672, "deleted_at": 0}, {"id": 3198, "parent_id": 6, "system_id": 6, "front_url": "", "component": "", "api_url": "/msg/page", "name": "系统强提醒", "code": "", "type": "a", "hide": 0, "sort": 3, "icon": "", "updated_at": 1640831569, "created_at": 1640831569, "deleted_at": 0}, {"id": 19677, "parent_id": 2860, "system_id": 6, "front_url": "/composite-goods", "component": "", "api_url": "", "name": "组合商品", "code": "", "type": "l", "hide": 0, "sort": 3, "icon": "", "updated_at": 1641365599, "created_at": 1641365599, "deleted_at": 0}, {"id": 27942, "parent_id": 19677, "system_id": 6, "front_url": "", "component": "", "api_url": "/combination/pageSku", "name": "sku 明细列表", "code": "", "type": "a", "hide": 1, "sort": 3, "icon": "", "updated_at": 1642064619, "created_at": 1642064619, "deleted_at": 0}, {"id": 3042, "parent_id": 2862, "system_id": 6, "front_url": "/list", "component": "", "api_url": "", "name": "后端商品列表页面", "code": "", "type": "p", "hide": 0, "sort": 4, "icon": "", "updated_at": 1638435497, "created_at": 1638435497, "deleted_at": 0}, {"id": 3094, "parent_id": 3048, "system_id": 6, "front_url": "", "component": "", "api_url": "/purchase/getAccess", "name": "查看凭证", "code": "", "type": "a", "hide": 1, "sort": 4, "icon": "", "updated_at": 1637805898, "created_at": 1637805898, "deleted_at": 0}, {"id": 3218, "parent_id": 6, "system_id": 6, "front_url": "", "component": "", "api_url": "/msg/removeAll", "name": "清除全部消息", "code": "", "type": "a", "hide": 0, "sort": 4, "icon": "", "updated_at": 1640831574, "created_at": 1640831574, "deleted_at": 0}, {"id": 27943, "parent_id": 19677, "system_id": 6, "front_url": "", "component": "", "api_url": "/combination/dropDownName", "name": "组合商品下拉搜索", "code": "", "type": "a", "hide": 1, "sort": 4, "icon": "", "updated_at": 1642064656, "created_at": 1642064656, "deleted_at": 0}, {"id": 3097, "parent_id": 3048, "system_id": 6, "front_url": "", "component": "", "api_url": "/purchase/getStayConfirm", "name": "查看待确认商品", "code": "", "type": "a", "hide": 1, "sort": 5, "icon": "", "updated_at": 1637806193, "created_at": 1637806193, "deleted_at": 0}, {"id": 3221, "parent_id": 6, "system_id": 6, "front_url": "", "component": "", "api_url": "/msg/allRead", "name": "一键已读", "code": "", "type": "a", "hide": 0, "sort": 5, "icon": "", "updated_at": 1640831560, "created_at": 1640831560, "deleted_at": 0}, {"id": 27945, "parent_id": 19677, "system_id": 6, "front_url": "", "component": "", "api_url": "/combination/export", "name": "组合商品导出", "code": "", "type": "a", "hide": 1, "sort": 5, "icon": "", "updated_at": 1642064696, "created_at": 1642064696, "deleted_at": 0}, {"id": 3102, "parent_id": 3048, "system_id": 6, "front_url": "", "component": "", "api_url": "/purchase/excelTemplate", "name": "下载模版", "code": "", "type": "a", "hide": 1, "sort": 6, "icon": "", "updated_at": 1637895861, "created_at": 1637895861, "deleted_at": 0}, {"id": 3224, "parent_id": 6, "system_id": 6, "front_url": "", "component": "", "api_url": "/msg/read", "name": "读取一条消息", "code": "", "type": "a", "hide": 0, "sort": 6, "icon": "", "updated_at": 1640831492, "created_at": 1640831492, "deleted_at": 0}, {"id": 27946, "parent_id": 19677, "system_id": 6, "front_url": "", "component": "", "api_url": "/combination/view", "name": "组合商品详情", "code": "", "type": "a", "hide": 1, "sort": 6, "icon": "", "updated_at": 1642064724, "created_at": 1642064724, "deleted_at": 0}, {"id": 27948, "parent_id": 19677, "system_id": 6, "front_url": "", "component": "", "api_url": "/combination/viewPrice", "name": "组合商品价格详情", "code": "", "type": "a", "hide": 1, "sort": 7, "icon": "", "updated_at": 1642064747, "created_at": 1642064747, "deleted_at": 0}, {"id": 29307, "parent_id": 6, "system_id": 6, "front_url": "", "component": "", "api_url": "/msg/countNoRead", "name": "统计未读消息数量", "code": "", "type": "a", "hide": 0, "sort": 7, "icon": "", "updated_at": 1642990466, "created_at": 1642990466, "deleted_at": 0}, {"id": 29297, "parent_id": 19677, "system_id": 6, "front_url": "", "component": "", "api_url": "/combination/delete", "name": "删除组合商品", "code": "", "type": "a", "hide": 1, "sort": 8, "icon": "", "updated_at": 1642399153, "created_at": 1642399153, "deleted_at": 0}, {"id": 79625, "parent_id": 6, "system_id": 6, "front_url": "/order-management", "component": "", "api_url": "", "name": "订单管理", "code": "", "type": "l", "hide": 0, "sort": 8, "icon": "", "updated_at": 1648091850, "created_at": 1648091850, "deleted_at": 0}, {"id": 29300, "parent_id": 19677, "system_id": 6, "front_url": "", "component": "", "api_url": "/combination/operateLogs", "name": "操作记录", "code": "", "type": "a", "hide": 1, "sort": 9, "icon": "", "updated_at": 1642399193, "created_at": 1642399193, "deleted_at": 0}, {"id": 59362, "parent_id": 19677, "system_id": 6, "front_url": "", "component": "", "api_url": "/combination/calculatePrice", "name": "计算价格", "code": "", "type": "a", "hide": 1, "sort": 10, "icon": "", "updated_at": 1643182127, "created_at": 1643182127, "deleted_at": 0}]}