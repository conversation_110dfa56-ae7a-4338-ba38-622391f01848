<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false" scanPeriod="10 seconds">

    <contextName>logback-spring</contextName>
    <springProperty scope="context" name="activeProfile" source="spring.profiles.active"
            defaultValue="local"/>

    <!-- 日志最大保留时间 15天 -->
    <property name="maxHistory" value="15"/>


    <turboFilter class="com.daddylab.supplier.item.infrastructure.logback.LogInterceptor"/>

    <conversionRule conversionWord="clr"
            converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
            converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
            converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="oEex"
            converterClass="com.daddylab.supplier.item.infrastructure.logback.OneLineExtendedThrowableProxyConverter"/>


    <property name="LOG.PATTERN"
            value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{80} - %msg%n"/>
    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
            value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %replace(%tid){&apos;TID:&apos;,&apos;&apos;} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
    <property name="CONSOLE_LOG_JSON"
            value='{"logTime":"%d{yyyy-MM-dd HH:mm:ss.SSS}","profile":"${activeProfile}","thread":"%thread","codeLine":"%line","pid":"${PID:- }","level": "%-5level","className": "%logger{80}","tid":"%replace(%tid){&apos;TID:&apos;,&apos;&apos;}","sid":"","msg":"%msg","e":"%oEex"}%n'/>
    <!--<property name="CONSOLE_LOG_JSON"-->
    <!--        value='{"logTime":"%d{yyyy-MM-dd HH:mm:ss.SSS}","profile":"${activeProfile}","thread":"%thread","codeLine":"%line","pid":"${PID:- }","level": "%-5level","className": "%logger{80}","tid":"%replace(%tid){&apos;TID:&apos;,&apos;&apos;}","sid":"","msg":"%replace(%msg){&apos;\n&apos;,&apos;&#x000D;&apos;}","e":"%replace(%xException){&apos;\n&apos;,&apos;&#x000D;&apos;}"}%n'/>-->

    <!-- 日志文件写入位置-->
    <property name="LOGGING.PATH" value="/tmp/logs"/>

    <!-- 日志最大保留时间 15天 -->
    <property name="maxHistory" value="15"/>

    <!--输出到控制台-->
    <appender name="CONSOLE_LOCAL" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="CONSOLE_DEV" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>${CONSOLE_LOG_JSON}</pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="CONSOLE_GRAY" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>${CONSOLE_LOG_JSON}</pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="CONSOLE_PROD" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>${CONSOLE_LOG_JSON}</pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="FILE_DEBUG_ALL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOGGING.PATH}/log-debug.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOGGING.PATH}/total/debug/log-debug-%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>

        <!-- 追加方式记录日志 -->
        <append>true</append>

        <!-- 日志文件的格式 -->
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            </layout>
        </encoder>

    </appender>

    <appender name="FILE_INFO_ALL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOGGING.PATH}/log-info.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOGGING.PATH}/total/info/log-info-%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>

        <!-- 追加方式记录日志 -->
        <append>true</append>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            </layout>
        </encoder>

        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <!-- 过滤的日志级别 -->
            <level>info</level>
        </filter>
    </appender>

    <logger name="org.springframework" level="WARN" additivity="true"/>
    <!--打印sql和耗时-->
    <logger name="jdbc.sqltiming" level="INFO" additivity="true"/>
    <!--打印sql结果，并显示成表-->
    <logger name="jdbc.resultsettable" level="INFO" additivity="true"/>
    <!--以下为排除项：不打印连接信息等等-->
    <logger name="jdbc.sqlonly" level="WARN" additivity="true"/>
    <logger name="jdbc.audit" level="WARN" additivity="true"/>
    <logger name="jdbc.resultset" level="WARN" additivity="true"/>
    <logger name="jdbc.connection" level="WARN" additivity="true"/>
    <!--    <logger name="com.alibaba.druid" level="WARN" additivity="true"/>-->
    <logger name="com.egzosn.pay" level="DEBUG" additivity="true"/>
    <!--数据库日志-->
    <logger name="com.apache.ibatis" level="DEBUG"/>
    <logger name="java.sql.Connection" level="DEBUG"/>
    <logger name="java.sql.Statement" level="DEBUG"/>
    <!--应用日志-->
    <logger name="com.daddylab.supplier.item" level="INFO" additivity="true"/>
    <logger name="dataFetch" level="INFO" additivity="true"/>
    <logger name="WdtAPILogProxy" level="INFO" additivity="true"/>
    <logger name="reactor.netty" level="INFO" additivity="true"/>

<!--    <root level="debug">-->
<!--        <appender-ref ref="CONSOLE_LOCAL"/>-->
<!--    </root>-->

    <springProfile name="local">
        <root level="info">
            <appender-ref ref="CONSOLE_LOCAL"/>
        </root>
    </springProfile>
    <springProfile name="dev">
        <root level="info">
            <if condition='isDefined("HOME") &amp;&amp; property("HOME").contains("/Users/<USER>")'>
                <then>
                    <appender-ref ref="CONSOLE_LOCAL"/>
                </then>
                <else>
                    <appender-ref ref="CONSOLE_DEV"/>
                </else>
            </if>
            <appender-ref ref="FILE_DEBUG_ALL"/>
        </root>
    </springProfile>

    <springProfile name="test">
        <root level="info">
            <if condition='isDefined("HOME") &amp;&amp; property("HOME").contains("/Users/<USER>")'>
                <then>
                    <appender-ref ref="CONSOLE_LOCAL"/>
                </then>
                <else>
                    <appender-ref ref="CONSOLE_DEV"/>
                </else>
            </if>
            <appender-ref ref="FILE_INFO_ALL"/>
        </root>
    </springProfile>

    <springProfile name="pre">
        <root level="info">
            <appender-ref ref="CONSOLE_DEV"/>
            <appender-ref ref="FILE_DEBUG_ALL"/>
        </root>
    </springProfile>

    <springProfile name="gray">
        <root level="info">
            <appender-ref ref="CONSOLE_GRAY"/>
            <appender-ref ref="FILE_DEBUG_ALL"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <root level="info">
            <appender-ref ref="CONSOLE_PROD"/>
            <appender-ref ref="FILE_DEBUG_ALL"/>
        </root>
    </springProfile>
</configuration>
