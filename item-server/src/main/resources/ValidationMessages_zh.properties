javax.validation.constraints.AssertFalse.message     = \u5fc5\u987b\u4e3a false
javax.validation.constraints.AssertTrue.message      = \u5fc5\u987b\u4e3a true
javax.validation.constraints.DecimalMax.message      = \u5fc5\u987b\u5c0f\u4e8e ${inclusive == true ? 'or equal to ' : ''}{value}
javax.validation.constraints.DecimalMin.message      = \u5fc5\u987b\u5927\u4e8e ${inclusive == true ? 'or equal to ' : ''}{value}
javax.validation.constraints.Digits.message          = \u6570\u5b57\u503c\u8d85\u51fa\u4e86\u8fb9\u754c\uff08\u671f\u671b <{integer} digits>.<{fraction} digits>\uff09
javax.validation.constraints.Email.message           = \u5fc5\u987b\u4e3a\u683c\u5f0f\u89c4\u8303\u7684\u7535\u5b50\u90ae\u4ef6\u5730\u5740
javax.validation.constraints.Future.message          = \u5fc5\u987b\u662f\u672a\u6765\u7684\u65e5\u671f
javax.validation.constraints.FutureOrPresent.message = \u5fc5\u987b\u662f\u73b0\u5728\u6216\u5c06\u6765\u7684\u65e5\u671f
javax.validation.constraints.Max.message             = \u5fc5\u987b\u5c0f\u4e8e\u6216\u7b49\u4e8e {value}
javax.validation.constraints.Min.message             = \u5fc5\u987b\u5927\u4e8e\u6216\u7b49\u4e8e {value}
javax.validation.constraints.Negative.message        = \u5fc5\u987b\u5c0f\u4e8e 0
javax.validation.constraints.NegativeOrZero.message  = \u5fc5\u987b\u5c0f\u4e8e\u6216\u7b49\u4e8e 0
javax.validation.constraints.NotBlank.message        = \u4e0d\u5f97\u4e3a\u7a7a\u767d
javax.validation.constraints.NotEmpty.message        = \u4e0d\u5f97\u4e3a\u7a7a
javax.validation.constraints.NotNull.message         = \u4e0d\u5f97\u4e3a null
javax.validation.constraints.Null.message            = \u5fc5\u987b\u4e3a null
javax.validation.constraints.Past.message            = \u5fc5\u987b\u662f\u8fc7\u53bb\u7684\u65e5\u671f
javax.validation.constraints.PastOrPresent.message   = \u5fc5\u987b\u662f\u8fc7\u53bb\u6216\u73b0\u5728\u7684\u65e5\u671f
javax.validation.constraints.Pattern.message         = \u5fc5\u987b\u4e0e "{regexp}" \u5339\u914d
javax.validation.constraints.Positive.message        = \u5fc5\u987b\u5927\u4e8e 0
javax.validation.constraints.PositiveOrZero.message  = \u5fc5\u987b\u5927\u4e8e\u6216\u7b49\u4e8e 0
javax.validation.constraints.Size.message            = \u5927\u5c0f\u5fc5\u987b\u5728 {min} \u548c {max} \u4e4b\u95f4

org.hibernate.validator.constraints.CreditCardNumber.message        = \u65e0\u6548\u4fe1\u7528\u5361\u5361\u53f7
org.hibernate.validator.constraints.Currency.message                = \u65e0\u6548\u8d27\u5e01\uff08\u5fc5\u987b\u4e3a {value} \u4e4b\u4e00\uff09
org.hibernate.validator.constraints.EAN.message                     = \u65e0\u6548 {type} \u6761\u5f62\u7801
org.hibernate.validator.constraints.Email.message                   = \u7535\u5b50\u90ae\u4ef6\u5730\u5740\u683c\u5f0f\u4e0d\u89c4\u8303
org.hibernate.validator.constraints.ISBN.message                    = \u65e0\u6548 ISBN
org.hibernate.validator.constraints.Length.message                  = \u957f\u5ea6\u5fc5\u987b\u4ecb\u4e8e {min} \u4e0e {max} \u4e4b\u95f4
org.hibernate.validator.constraints.CodePointLength.message         = \u957f\u5ea6\u5fc5\u987b\u4ecb\u4e8e {min} \u4e0e {max} \u4e4b\u95f4
org.hibernate.validator.constraints.LuhnCheck.message               = ${validatedValue} \u7684\u6821\u9a8c\u4f4d\u65e0\u6548\uff0cLuhn Modulo 10 \u6821\u9a8c\u548c\u5931\u8d25
org.hibernate.validator.constraints.Mod10Check.message              = ${validatedValue} \u7684\u6821\u9a8c\u4f4d\u65e0\u6548\uff0cModulo 10 \u6821\u9a8c\u548c\u5931\u8d25
org.hibernate.validator.constraints.Mod11Check.message              = ${validatedValue} \u7684\u6821\u9a8c\u4f4d\u65e0\u6548\uff0cModulo 11 \u6821\u9a8c\u548c\u5931\u8d25
org.hibernate.validator.constraints.ModCheck.message                = ${validatedValue} \u7684\u6821\u9a8c\u4f4d\u65e0\u6548\uff0c{modType} \u6821\u9a8c\u548c\u5931\u8d25
org.hibernate.validator.constraints.NotBlank.message                = \u53ef\u80fd\u4e0d\u4e3a\u7a7a
org.hibernate.validator.constraints.NotEmpty.message                = \u53ef\u80fd\u4e0d\u4e3a\u7a7a
org.hibernate.validator.constraints.ParametersScriptAssert.message  = \u811a\u672c\u8868\u8fbe\u5f0f "{script}" \u672a\u6c42\u503c\u4e3a true
org.hibernate.validator.constraints.Range.message                   = \u5fc5\u987b\u4ecb\u4e8e {min} \u4e0e {max} \u4e4b\u95f4
org.hibernate.validator.constraints.SafeHtml.message                = \u53ef\u80fd\u5177\u6709\u4e0d\u5b89\u5168\u7684 HTML \u5185\u5bb9
org.hibernate.validator.constraints.ScriptAssert.message            = \u811a\u672c\u8868\u8fbe\u5f0f "{script}" \u672a\u6c42\u503c\u4e3a true
org.hibernate.validator.constraints.UniqueElements.message          = \u5fc5\u987b\u4ec5\u5305\u542b\u552f\u4e00\u5143\u7d20
org.hibernate.validator.constraints.URL.message                     = \u5fc5\u987b\u4e3a\u6709\u6548 URL

org.hibernate.validator.constraints.br.CNPJ.message                 = \u65e0\u6548\u5df4\u897f\u4f01\u4e1a\u7eb3\u7a0e\u4eba\u767b\u8bb0\u53f7 (CNPJ)
org.hibernate.validator.constraints.br.CPF.message                  = \u65e0\u6548\u5df4\u897f\u4e2a\u4eba\u7eb3\u7a0e\u4eba\u767b\u8bb0\u53f7 (CPF)
org.hibernate.validator.constraints.br.TituloEleitoral.message      = \u65e0\u6548\u5df4\u897f\u6295\u7968\u4eba\u8eab\u4efd\u8bc1\u53f7

org.hibernate.validator.constraints.pl.REGON.message                = \u65e0\u6548\u6ce2\u5170\u7eb3\u7a0e\u4eba\u8bc6\u522b\u53f7 (REGON)
org.hibernate.validator.constraints.pl.NIP.message                  = \u65e0\u6548 VAT \u8bc6\u522b\u53f7 (NIP)
org.hibernate.validator.constraints.pl.PESEL.message                = \u65e0\u6548\u6ce2\u5170\u8eab\u4efd\u8bc1\u53f7 (PESEL)

org.hibernate.validator.constraints.time.DurationMax.message        = \u5fc5\u987b\u77ed\u4e8e ${inclusive == true ? ' or equal to' : ''}${days == 0 ? '' : days == 1 ? ' 1 day' : ' ' += days += ' days'}${hours == 0 ? '' : hours == 1 ? ' 1 hour' : ' ' += hours += ' hours'}${minutes == 0 ? '' : minutes == 1 ? ' 1 minute' : ' ' += minutes += ' minutes'}${seconds == 0 ? '' : seconds == 1 ? ' 1 second' : ' ' += seconds += ' seconds'}${millis == 0 ? '' : millis == 1 ? ' 1 milli' : ' ' += millis += ' millis'}${nanos == 0 ? '' : nanos == 1 ? ' 1 nano' : ' ' += nanos += ' nanos'}
org.hibernate.validator.constraints.time.DurationMin.message        = \u5fc5\u987b\u957f\u4e8e ${inclusive == true ? ' or equal to' : ''}${days == 0 ? '' : days == 1 ? ' 1 day' : ' ' += days += ' days'}${hours == 0 ? '' : hours == 1 ? ' 1 hour' : ' ' += hours += ' hours'}${minutes == 0 ? '' : minutes == 1 ? ' 1 minute' : ' ' += minutes += ' minutes'}${seconds == 0 ? '' : seconds == 1 ? ' 1 second' : ' ' += seconds += ' seconds'}${millis == 0 ? '' : millis == 1 ? ' 1 milli' : ' ' += millis += ' millis'}${nanos == 0 ? '' : nanos == 1 ? ' 1 nano' : ' ' += nanos += ' nanos'}
