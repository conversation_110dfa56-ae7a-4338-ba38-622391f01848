#是否启用自动检测功能，如：用户自定义词典、停用词词典
auto.detect=false
#词典机制实现类，词首字索引式前缀树
dic.class=org.apdplat.word.dictionary.impl.DictionaryTrie
#前缀树词首字索引分配空间大小，如过小则会导致碰撞增加，减小查询性能
dictionary.trie.index.size=24000
#双数组前缀树，速度稍快一些，内存占用稍少一些
#但功能有限，不支持动态增减单个词条，也不支持批量增减词条
#只支持先clear()后addAll()的动态改变词典方式
# dic.class=org.apdplat.word.dictionary.impl.DoubleArrayDictionaryTrie
#双数组前缀树预先分配空间大小，如不够则逐渐递增10%
# double.array.dictionary.trie.size=2600000
#词典，多个词典之间逗号分隔开
#如：dic.path=classpath:dic.txt,classpath:custom_dic,d:/dic_more.txt,d:/DIC,D:/DIC2
#自动检测词库变化，包含类路径下的文件和文件夹、非类路径下的绝对路径和相对路径
#HTTP资源：dic.path=http://localhost:8080/word_web/resources/dic.txt
dic.path=classpath:dic.txt
#是否利用多核提升分词速度
parallel.seg=true
#词性标注数据：part.of.speech.dic.path=http://localhost:8080/word_web/resources/part_of_speech_dic.txt
part.of.speech.dic.path=classpath:part_of_speech_dic.txt
#词性说明数据：part.of.speech.des.path=http://localhost:8080/word_web/resources/part_of_speech_des.txt
part.of.speech.des.path=classpath:part_of_speech_des.txt
#二元模型路径
#HTTP资源：bigram.path=http://localhost:8080/word_web/resources/bigram.txt
bigram.path=classpath:bigram.txt
bigram.double.array.trie.size=5300000
#三元模型路径
#HTTP资源：trigram.path=http://localhost:8080/word_web/resources/trigram.txt
trigram.path=classpath:trigram.txt
trigram.double.array.trie.size=9800000
#是否启用ngram模型，以及启用哪个模型
#可选值有：no(不启用)、bigram(二元模型)、trigram(三元模型)
#如不启用ngram模型
#则双向最大匹配算法、双向最大最小匹配算法退化为：逆向最大匹配算法
#则双向最小匹配算法退化为：逆向最小匹配算法
ngram=bigram
#停用词词典，多个词典之间逗号分隔开
#如：stopwords.path=classpath:stopwords.txt，classpath:custom_stopwords_dic，d:/stopwords_more.txt，d:/STOPWORDS，d:/STOPWORDS2
#自动检测词库变化，包含类路径下的文件和文件夹、非类路径下的绝对路径和相对路径
#HTTP资源：stopwords.path=http://localhost:8080/word_web/resources/stopwords.txt
stopwords.path=classpath:stopwords.txt
#用于分割词的标点符号，目的是为了加速分词，只能为单字符
#HTTP资源：punctuation.path=http://localhost:8080/word_web/resources/punctuation.txt
punctuation.path=classpath:punctuation.txt
#分词时截取的字符串的最大长度
intercept.length=16
#百家姓，用于人名识别
#HTTP资源：surname.path=http://localhost:8080/word_web/resources/surname.txt
surname.path=classpath:surname.txt
#数量词
#HTTP资源：quantifier.path=http://localhost:8080/word_web/resources/quantifier.txt
quantifier.path=classpath:quantifier.txt
#是否启用人名自动识别功能
person.name.recognize=true
#是否保留空白字符
keep.whitespace=false
#是否保留标点符号，标点符号的定义见文件：punctuation.txt
keep.punctuation=false
#将最多多少个词合并成一个
word.refine.combine.max.length=3
#对分词结果进行微调的配置文件
word.refine.path=classpath:word_refine.txt
#同义词词典
word.synonym.path=classpath:word_synonym.txt
#反义词词典
word.antonym.path=classpath:word_antonym.txt
#lucene、solr、elasticsearch、luke等插件是否启用标注
tagging.pinyin.full=false
tagging.pinyin.acronym=false
tagging.synonym=false
tagging.antonym=false
#是否启用识别工具，来识别文本（英文单词、数字、时间等）
recognition.tool.enabled=true
#如果你想知道word分词器的词典中究竟加载了哪些词
#可在配置项dic.dump.path中指定一个文件路径
#word分词器在加载词典的时候，顺便会把词典的内容写到指定的文件路径
#可指定相对路径或绝对路径
#如：
#dic.dump.path=dic.dump.txt
#dic.dump.path=dic.dump.txt
#dic.dump.path=/Users/<USER>/dic.dump.txt
# dic.dump.path=
#redis服务，用于实时检测HTTP资源变更
#redis主机
# redis.host=localhost
#redis端口
# redis.port=6379