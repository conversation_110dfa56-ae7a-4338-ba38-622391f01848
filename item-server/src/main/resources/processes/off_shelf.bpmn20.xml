<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
  <signal id="offShelfFinishSignal" name="商品下架流程结束信号"/>
  <signal id="offShelfRefuseSignal" name="商品下架流程审核拒绝信号"/>
  <process id="offShelf" name="商品下架流程" isExecutable="true">
    <startEvent id="startEvent"/>
    <userTask id="toApproveTask" name="待审核" flowable:assignee="${approveUser}"/>
    <exclusiveGateway id="approvalGateway" name="审核网关"/>
    <userTask id="toProcessTask" name="待处理">
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="processBindingIds" flowable:elementVariable="processBindingId"/>
    </userTask>
    <endEvent id="endEvent"/>
    <sequenceFlow id="sid-ed06b968-b4df-4145-9df4-c8a705554de8" sourceRef="approvalGateway" targetRef="toProcessTask" name="审核同意">
      <conditionExpression xsi:type="tFormalExpression">${approval=='agree'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-1478b3de-d799-464f-b76a-3de0fbe1551e" sourceRef="approvalGateway" targetRef="refuseEvent" name="审核拒绝">
      <conditionExpression xsi:type="tFormalExpression">${approval=='refuse'}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-a80b9fda-32c4-466c-a47f-f28469300553" sourceRef="toProcessTask" targetRef="finishEvent"/>
    <intermediateCatchEvent id="finishEvent" name="结束事件">
      <signalEventDefinition signalRef="offShelfFinishSignal"/>
    </intermediateCatchEvent>
    <intermediateThrowEvent id="refuseEvent" name="拒绝事件">
      <signalEventDefinition signalRef="offShelfRefuseSignal"/>
    </intermediateThrowEvent>
    <serviceTask id="noticeApplyTask" flowable:delegateExpression="${offShelfNoticeDelegate}" name="下架申请知会"/>
    <sequenceFlow id="sid-a1c8723a-a78d-4a1c-8b8e-c7a029a0d5c0" sourceRef="finishEvent" targetRef="endEvent"/>
    <sequenceFlow id="sid-0294053f-9996-4ca8-9e81-f6c9a491649e" sourceRef="startEvent" targetRef="toApproveTask"/>
    <sequenceFlow id="sid-d5b3a2fd-324b-41a4-94bb-09e2d389a698" sourceRef="toApproveTask" targetRef="approvalGateway"/>
    <serviceTask id="noticeFinishTask" flowable:delegateExpression="${offShelfNoticeDelegate}" name="结果通知"/>
    <sequenceFlow id="sid-aee5a759-fefe-4adb-acd4-0ce08b4543f7" sourceRef="toProcessTask" targetRef="noticeFinishTask"/>
    <sequenceFlow id="sid-317a1a2a-d510-410d-90d5-a96ac32a64e8" sourceRef="startEvent" targetRef="noticeApplyTask"/>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_off_shelf_001">
    <bpmndi:BPMNPlane bpmnElement="offShelf" id="BPMNPlane_off_shelf_001">
      <bpmndi:BPMNShape id="shape-a96870d3-67e5-4dc8-94b7-f2ad4ce027a1" bpmnElement="startEvent">
        <omgdc:Bounds x="-330.0" y="-10.0" width="30.0" height="30.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape-3b61367a-823b-40c7-98af-6df35d3573f7" bpmnElement="toApproveTask">
        <omgdc:Bounds x="-245.0" y="-35.0" width="100.0" height="80.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape-b0558215-d317-4e4a-8064-fc7eb58354f4" bpmnElement="approvalGateway">
        <omgdc:Bounds x="-55.0" y="-15.0" width="40.0" height="40.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape-e5797675-cee9-4dcd-9850-24ddaa161704" bpmnElement="toProcessTask">
        <omgdc:Bounds x="105.0" y="-35.0" width="100.0" height="80.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape-b1f52690-e61d-4370-8c82-8b28222c240f" bpmnElement="endEvent">
        <omgdc:Bounds x="415.0" y="-10.0" width="30.0" height="30.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="edge-c2b0a618-8fd2-429d-b8e7-4f1c1a40d802" bpmnElement="sid-ed06b968-b4df-4145-9df4-c8a705554de8">
        <omgdi:waypoint x="-15.0" y="5.0"/>
        <omgdi:waypoint x="105.0" y="5.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge-86c93743-4e45-4caa-a769-a7849757a166" bpmnElement="sid-1478b3de-d799-464f-b76a-3de0fbe1551e">
        <omgdi:waypoint x="-35.0" y="25.0"/>
        <omgdi:waypoint x="-35.0" y="95.0"/>
        <omgdi:waypoint x="304.99997" y="95.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge-4416b779-7523-4c5a-a2e0-4f1216a2cdee" bpmnElement="sid-a80b9fda-32c4-466c-a47f-f28469300553">
        <omgdi:waypoint x="205.0" y="5.0"/>
        <omgdi:waypoint x="304.99994" y="5.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="shape-7fcba36f-0c1f-4909-bf10-d7151eab78ec" bpmnElement="finishEvent">
        <omgdc:Bounds x="304.99994" y="-10.0" width="30.0" height="30.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape-74f38289-adec-471e-80d1-1bb1d5987e5b" bpmnElement="refuseEvent">
        <omgdc:Bounds x="304.99997" y="79.99999" width="30.0" height="30.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="shape-d9f4ea2d-d8a9-4949-b2b7-3bde09e5c09c" bpmnElement="noticeApplyTask">
        <omgdc:Bounds x="-365.0" y="-165.0" width="100.0" height="80.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="edge-6d6965d3-8b17-4c02-9adc-3741a7db7f40" bpmnElement="sid-a1c8723a-a78d-4a1c-8b8e-c7a029a0d5c0">
        <omgdi:waypoint x="334.99994" y="5.0"/>
        <omgdi:waypoint x="415.0" y="5.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge-41cd54c4-f1f2-4a7d-808b-3d0a9d5f1286" bpmnElement="sid-0294053f-9996-4ca8-9e81-f6c9a491649e">
        <omgdi:waypoint x="-300.0" y="5.0"/>
        <omgdi:waypoint x="-245.0" y="5.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge-7c7faeb6-fa27-465b-8b87-d1704c23e675" bpmnElement="sid-d5b3a2fd-324b-41a4-94bb-09e2d389a698">
        <omgdi:waypoint x="-145.0" y="5.0"/>
        <omgdi:waypoint x="-55.0" y="5.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="shape-ac7c5708-0da0-42c2-b80a-82491e0cb3ce" bpmnElement="noticeFinishTask">
        <omgdc:Bounds x="270.0" y="-165.0" width="100.0" height="80.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="edge-7005443a-1b78-45dd-907b-f7271735e79d" bpmnElement="sid-aee5a759-fefe-4adb-acd4-0ce08b4543f7">
        <omgdi:waypoint x="155.0" y="-35.0"/>
        <omgdi:waypoint x="155.0" y="-125.0"/>
        <omgdi:waypoint x="270.0" y="-124.99999"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="edge-c256e253-e6f4-429e-a59e-877b08ce4c2f" bpmnElement="sid-317a1a2a-d510-410d-90d5-a96ac32a64e8">
        <omgdi:waypoint x="-315.0" y="-10.0"/>
        <omgdi:waypoint x="-315.0" y="-85.0"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
