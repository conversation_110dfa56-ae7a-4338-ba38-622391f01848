package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerImageTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import com.daddylab.supplier.item.types.recognitionTask.ItemDrawerRecognitionTaskStatus;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 抽屉图片表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemDrawerRecognitionTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 模块
     */
    private String module;

    /**
     * 识别字段
     */
    private String field;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 当前是第几轮审核
     */
    private Integer round;

    /**
     * 抽屉图片id
     */
    private Long drawerImageId;

    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * 抽屉图片类型 1-抽屉商品 2-抽屉详情页 3-规格图片 4-主图视频 5-属性图片
     */
    private ItemDrawerImageTypeEnum imageType;

    /**
     * 状态 0 失败 1 准备 2 等待敏感词检查 3 敏感词检测完成
     */
    private ItemDrawerRecognitionTaskStatus status;

    /**
     * 敏感词检查内容
     */
    private String content;

    /**
     * 图片识别结果
     */
    private String cvResult;

    /**
     * 敏感词命中结果（JSON）
     */
    private String hitResult;

    /**
     * 日志
     */
    private String log;

    /**
     * 失败次数
     */
    private Integer failCount;

    /**
     * 审批类型 1 商品资料 2 直播话术
     */
    private ItemAuditType type;


}
