package com.daddylab.supplier.item.controller.shop;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.application.shop.InventoryAllocShopBizService;
import com.daddylab.supplier.item.application.shop.ShopBizService;
import com.daddylab.supplier.item.application.shop.domain.ShopOperatorMapVO;
import com.daddylab.supplier.item.application.shop.dto.InventoryAllocShopCmd;
import com.daddylab.supplier.item.application.shop.dto.InventoryAllocShopQuery;
import com.daddylab.supplier.item.application.shop.dto.InventoryAllocShopVo;
import com.daddylab.supplier.item.common.domain.dto.IdCmd;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.shop.dto.*;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import javax.inject.Inject;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11 9:56 上午
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/shop")
@Api(value = "店铺相关api", tags = "店铺相关API")
public class ShopController {

  @Autowired private ShopBizService shopBizService;

  @Inject private OperateLogBizService operateLogBizService;

  @Autowired InventoryAllocShopBizService inventoryAllocShopBizService;

  @Resource ShopGateway shopGateway;

  @PostMapping(value = "/dropDownShopList")
  @ApiOperation("店铺下拉列表")
  public MultiResponse<ShopDropDownItem> dropDownShopList(
      @RequestBody @Valid ShopDropDownQuery query) {
    return shopBizService.dropDownList(query);
  }

  @PutMapping(value = "/createOrUpdateShop")
  @ApiOperation("店铺创建/更新")
  public Response createOrUpdateShop(@RequestBody @Valid CreateOrUpdateShopCmd cmd) {
    return shopBizService.createOrUpdateShop(cmd);
  }

  @PostMapping(value = "/queryShopList")
  @ApiOperation("店铺列表")
  public PageResponse<ShopListItem> queryShopList(@RequestBody @Valid ShopQuery shopQuery) {
    return shopBizService.pageQueryShopList(shopQuery);
  }

  @PostMapping(value = "/getShopDetail")
  @ApiOperation("店铺详情")
  public SingleResponse<ShopDetail> getShop(@RequestBody @Valid IdCmd cmd) {
    return shopBizService.getShopDetail(cmd.getId());
  }

  @GetMapping(value = "/operateLogs")
  @ApiOperation("操作日志")
  public Response operateLogs(Long targetId) {
    return operateLogBizService.getOperateLogs(OperateLogTarget.SHOP, targetId);
  }

  // ------------- -----------------------------------------------------------------

  @PostMapping(value = "/offShelfMappingQuery")
  @ApiOperation("下架管理-店铺/运营映射查询")
  public PageResponse<ShopOperatorMapVO> offShelfMappingQuery(
      @RequestBody OffShelfShopOperatorPageQuery queryPage) {
    return shopBizService.offShelfShopOperatorMappingPage(queryPage);
  }

  @PostMapping(value = "/offShelfMappingSave")
  @ApiOperation("下架管理-店铺/运营映射保存编辑")
  public SingleResponse<Boolean> offShelfMappingSave(
      @RequestBody @Validated OffShelfShopOperatorCmd cmd) {
    return shopBizService.offShelfShopOperatorMappingSave(cmd);
  }

  @GetMapping(value = "/offShelfMappingRemove")
  @ApiOperation("下架管理-店铺/运营映射移除")
  public SingleResponse<Boolean> offShelfMappingRemove(Long id) {
    return shopBizService.offShelfShopOperatorMappingRemove(id);
  }

  // --------------- 数据脚本 ---------------------------

  /*@GetMapping(value = "/preDataHandler")
  @ApiOperation("3.4.3迭代，历史数据处理脚本")
  @Auth(noAuth = true)
  public Response preDataHandler(Long shopId) {
      return shopGateway.preDataProcessing(shopId);
  }*/

  // ------------------- 店铺库存配置接口 -------------------------

  @PostMapping(value = "/stock/page")
  @ApiOperation("需库存同步店铺列表")
  public PageResponse<InventoryAllocShopVo> stockPage(@RequestBody InventoryAllocShopQuery query) {
    return inventoryAllocShopBizService.page(query);
  }

  @GetMapping(value = "/stock/delete")
  @ApiOperation("需库存同步店铺删除")
  public SingleResponse<Boolean> stockDelete(Long id) {
    return SingleResponse.of(inventoryAllocShopBizService.delete(id));
  }

  @PostMapping(value = "/stock/save")
  @ApiOperation("编辑库存同步列表")
  public Response saveOrUpdate(@RequestBody InventoryAllocShopCmd cmd) {
    inventoryAllocShopBizService.saveOrUpdate(cmd);
    return Response.buildSuccess();
  }

  @GetMapping(value = "/stock/operateLog")
  @ApiOperation("店铺库存同步设置操作日志")
  public MultiResponse<OperateLog> stockOperateLog() {
    return operateLogBizService.getOperateLogs(OperateLogTarget.INVENTORY_ALLOC_SHOP, 0L);
  }
}
