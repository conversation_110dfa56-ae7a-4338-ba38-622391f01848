package com.daddylab.supplier.item.controller.open.nuonuo;

import cn.hutool.crypto.digest.DigestUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.NonNull;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> up
 * @date 2024年08月01日 10:09 AM
 */
public class NuoNuoOpenApiConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new HandlerInterceptor() {
            @Override
            public boolean preHandle(@NonNull HttpServletRequest request,
                                     @NonNull HttpServletResponse response, @NonNull Object handler) {
                final String openAuth = request.getHeader("OPEN-AUTH");
                return StringUtil.isNotBlank(openAuth) && StringUtil
                        .equals(openAuth, "67156af00ba8419ba8691588526dd400");
            }
        }).order(Ordered.HIGHEST_PRECEDENCE).addPathPatterns("open/c/api/invoice/**");
    }

    public static void main(String[] args) {
        System.out.println(DigestUtil.md5Hex("nuonuo_dadylab"));
        // 67156af00ba8419ba8691588526dd400
    }
}
