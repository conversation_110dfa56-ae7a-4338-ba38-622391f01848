package com.daddylab.supplier.item.infrastructure.gatewayimpl.nuonuo;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AllChannelBillData;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NuoNuoInvoiceProcessRecord;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NuoNuoInvoiceRequest;

import java.math.BigDecimal;
import java.util.List;

public interface NuoNuoOpenGateway {

    /**
     * 诺税通saas请求开具发票接口
     */
    NuoNuoInvoiceProcessRecord requestBillingNew(NuoNuoInvoiceRequest request,
                                                 List<AllChannelBillData> dataList,
                                                 BigDecimal definitelyCorrect);


    /**
     * 诺税通saas红字专用发票信息表申请接口
     *
     * @param orderNo
     */
    NuoNuoInvoiceProcessRecord requestCreditNoteApply(String orderNo);


    /**
     * 诺税通saas发票详情查询接口
     *
     * @param serialNos
     * @param orderNo
     * @return
     */
    NuoNuoInvoiceProcessRecord queryInvoiceResult(List<String> serialNos, String orderNo);

}
