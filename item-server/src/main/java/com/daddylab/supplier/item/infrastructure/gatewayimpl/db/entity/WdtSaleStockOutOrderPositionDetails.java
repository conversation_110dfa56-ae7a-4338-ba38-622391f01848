package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 旺店通销售出库单出库货位明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtSaleStockOutOrderPositionDetails implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 出库单ID
     */
    private Long stockoutId;

    /**
     * 货位id
     */
    private Integer positionId;

    /**
     * 货位号
     */
    private String positionNo;

    /**
     * 有效期
     */
    private LocalDateTime expireDate;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 当前货位出库货品总量
     */
    private BigDecimal positionGoodsCount;


}
