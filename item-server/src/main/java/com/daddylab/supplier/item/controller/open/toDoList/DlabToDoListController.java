package com.daddylab.supplier.item.controller.open.toDoList;

import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.toDoList.ImportItemExcelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR> up
 * @date 2024年10月11日 6:18 PM
 */
@Slf4j
@Api(value = "老爸清单相关接口", tags = "老爸清单相关接口")
@RestController
@RequestMapping("/open/toDoList")
public class DlabToDoListController {

    @Resource
    ImportItemExcelService importItemExcelService;

    @PostMapping(value = "/importExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("导入商品Excel")
//    @Auth(noAuth = true)
    public Response itemImportExcel(@RequestParam("file") MultipartFile file) {
        try {
            importItemExcelService.run(file.getInputStream());
        } catch (Exception e) {
            log.error("老爸清单相关接口-导入商品Excel error", e);
        }
        return Response.buildSuccess();
    }

    @GetMapping(value = "/delete")
    public Response delete() {
        importItemExcelService.deleteAllData();
        return Response.buildSuccess();
    }

}
