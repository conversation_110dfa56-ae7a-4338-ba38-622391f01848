package com.daddylab.supplier.item.controller.item.dto.detail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13 4:28 下午
 * @description
 */
@Builder
@Data
@ApiModel("商品详情-sku返回封装")
public class ItemDetailSkuVo {

    List<AttrVo> attrList;
    @ApiModelProperty("skuId")
    private Long skuId;
    @ApiModelProperty("skuCode")
    private String skuCode;
    @ApiModelProperty("成本")
    private String procurementPrices;
    @ApiModelProperty("售价")
    private String salesPrices;
    @ApiModelProperty("仓库库存")
    private Integer stockCount;

    @Data
    @ApiModel("sku属性封装")
    public static class AttrVo {
        @ApiModelProperty("skuId")
        private Long skuId;
        @ApiModelProperty("属性id")
        private Long attrId;
        @ApiModelProperty("属性名")
        private String attrName;
        @ApiModelProperty("属性值")
        private String attrValue;
    }
}
