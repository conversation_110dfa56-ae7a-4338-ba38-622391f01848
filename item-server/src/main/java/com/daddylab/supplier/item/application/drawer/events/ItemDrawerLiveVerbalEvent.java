package com.daddylab.supplier.item.application.drawer.events;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @class ItemDrawerMergeEvent.java
 * @description 描述类的作用
 * @date 2024-04-09 15:28
 */
@Getter
public class ItemDrawerLiveVerbalEvent extends ApplicationEvent {

    private final Long liveVerbalId;

    public ItemDrawerLiveVerbalEvent(Object source, Long liveVerbalId) {
        super(source);
        this.liveVerbalId  = liveVerbalId;
    }
}
