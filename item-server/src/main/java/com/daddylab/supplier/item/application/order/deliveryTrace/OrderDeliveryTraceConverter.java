package com.daddylab.supplier.item.application.order.deliveryTrace;

import com.daddylab.supplier.item.common.trans.TimeTransMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderDeliveryTrace;
import com.daddylab.supplier.item.types.order.OrderDeliveryTracePageVO;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2023/8/22
 */
@Mapper(uses = TimeTransMapper.class)
public interface OrderDeliveryTraceConverter {
    OrderDeliveryTraceConverter SHARE = Mappers.getMapper(OrderDeliveryTraceConverter.class);

    @Mappings({
            @Mapping(target = "orderPersonnel", ignore = true),
            @Mapping(target = "platform", ignore = true)
    })
    OrderDeliveryTracePageVO po2pageVO(OrderDeliveryTrace po);
}
