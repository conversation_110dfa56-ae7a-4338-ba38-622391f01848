package com.daddylab.supplier.item.application.payment.wrietoff;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.ProcessRequest;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.SkuUnitDto;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PurchaseOrderState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
import com.daddylab.supplier.item.infrastructure.kingdee.dto.SubmitAndAuditRes;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLock;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2024年01月11日 4:32 PM
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class WriteOffBizServiceImpl implements WriteOffBizService {

    final IPaymentApplyOrderService iPaymentApplyOrderService;
    final IPaymentApplyOrderDetailService iPaymentApplyOrderDetailService;
    final IOrderSettlementFormService iOrderSettlementFormService;
    final IOrderSettlementDetailService iOrderSettlementDetailService;
    final IPurchaseOrderService iPurchaseOrderService;
    final IPurchaseOrderDetailService iPurchaseOrderDetailService;
    final IPaymentOrderWriteOffLogService iPaymentOrderWriteOffLogService;
    final IPurchasePayableOrderService iPurchasePayableOrderService;
    final IStockInOrderService iStockInOrderService;
    final IStockOutOrderService iStockOutOrderService;
    final ReqTemplate reqTemplate;
    final IProviderService iProviderService;

    final WriteOffConfig writeOffConfig;
    final ReverseOldStockOrderProcess reverseOldStockOrderProcess;
    final GenerateFixedPurchaseProcess generateFixedPurchaseProcess;
    final SupplementSalesOrderProcess supplementSalesOrderProcess;


    /*
     * 1.1 对旧单据生成逆向出入库单并且同步到金蝶。
     * 1.2.针对修正后的数据，生成新的采购订单。
     * 1.3.新的采购订单生成新的出入库单同步到金蝶
     *
     * 2.1 暂估数据和修正数据之前的差额，生成销售出库/退料单。
     * 2.2 生成的销售出库/退料单 数据同步到金蝶。
     */

    /**
     * 冲销主流程入口
     *
     * @param paymentApplyOrderId 付款单 ID
     */
    @DistributedLock
    @Override
    public void mainProcess(Long paymentApplyOrderId, Boolean manual, Boolean mockSync) {
        Boolean initiation = writeOffConfig.isInitiation();
        if (!initiation) {
            return;
        }

        PaymentApplyOrder paymentApplyOrder = iPaymentApplyOrderService.getById(paymentApplyOrderId);
        Assert.notNull(paymentApplyOrder, "付款单id非法.id:" + paymentApplyOrderId);

        // 收款单位.杭州认养一头牛生物科技有限公司/五常市浩海水稻种植农民专业合作社 特殊处理
        Long payeeUnit = paymentApplyOrder.getPayeeUnit();
        if (payeeUnit == 130 || payeeUnit == 46) {
            Provider byId = iProviderService.getById(payeeUnit);
            String name = Objects.isNull(byId) ? payeeUnit.toString() : byId.getName();
            String msg = StrUtil.format("付款单收款单位:{},不触发冲销流程.付款单编码:{}.id:{}", name, paymentApplyOrder.getNo(), paymentApplyOrderId);
            Alert.text(MessageRobotCode.GLOBAL, "[付款冲销]:" + msg);
            return;
        }

        Integer detailSource = paymentApplyOrder.getDetailSource();
        if (detailSource != 1) {
            String msg = StrUtil.format("付款单明细来源为采购单,不触发冲销流程.付款单编码:{}.id:{}", paymentApplyOrder.getNo(), paymentApplyOrderId);
            log.info(msg);
            Alert.text(MessageRobotCode.GLOBAL, "[付款冲销]:" + msg);
            return;
        }

        checkParam(paymentApplyOrder, manual);

        List<PaymentApplyOrderDetail> paymentApplyOrderDetails = iPaymentApplyOrderDetailService.getByPaymentApplyOrderId(paymentApplyOrderId);
        Map<Long, List<Long>> listMap = paymentApplyOrderDetails.stream().collect(
                        Collectors.groupingBy(
                                PaymentApplyOrderDetail::getApplyRelatedId,
                                Collectors.mapping(detail -> ListUtil.of(detail.getRelateDetailId().split(StrUtil.COMMA)), Collectors.toList())
                        )
                ).entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .flatMap(list -> list.stream().map(Long::parseLong))
                                .collect(Collectors.toList())
                ));
        listMap.forEach((settlementId, settlementDetailIdList) -> {
            OrderSettlementForm orderSettlementForm = iOrderSettlementFormService.getById(settlementId);
            List<OrderSettlementDetail> settlementDetails = iOrderSettlementDetailService.lambdaQuery().in(OrderSettlementDetail::getId, settlementDetailIdList).list();
            // sku冲销资质检查
            settlementDetails = settlementDetails.stream().filter(val -> val.getHadWriteOff() == 0).collect(Collectors.toList());
            if (CollUtil.isEmpty(settlementDetails)) {
                String msg = StrUtil.format("此付款结算单无需冲销,付款单号:{},id:{},结算单号:{},结算单 SKU 均已发触发过冲销流程",
                        paymentApplyOrder.getNo(), paymentApplyOrder.getId(), orderSettlementForm.getNo());
                log.info(msg);
                Alert.text(MessageRobotCode.GLOBAL, "[付款冲销]:" + msg);
                return;
            }
            List<String> purchaseOrderNos = Arrays.stream(orderSettlementForm.getPurchaseOrderNo().split(StrUtil.COMMA)).collect(Collectors.toList());
            List<PurchaseOrder> purchaseOrderList = iPurchaseOrderService.lambdaQuery().in(PurchaseOrder::getNo, purchaseOrderNos).orderByAsc(PurchaseOrder::getId).list();
            List<Long> purchaseOrderIds = purchaseOrderList.stream().map(PurchaseOrder::getId).collect(Collectors.toList());
            List<PurchaseOrderDetail> purchaseOrderDetailList = iPurchaseOrderDetailService.lambdaQuery().in(PurchaseOrderDetail::getPurchaseOrderId, purchaseOrderIds).list();

            // 对应付单和付款单的明细差异（包括数量和金额），应付单<-出入库单<-采购单。
            // 这里简化，直接拿采购单明细和结算单的结算明细对比做判断差异。
            List<SkuUnitDto> sortedList1 = getSortedList1(purchaseOrderDetailList);
            List<SkuUnitDto> sortedList2 = getSortedList2(settlementDetails);
            String s1 = SecureUtil.md5(JsonUtil.toJson(sortedList1));
            String s2 = SecureUtil.md5(JsonUtil.toJson(sortedList2));
            boolean isEquals = s1.equals(s2);

            // 需要进行冲销操作
            if (!isEquals) {
                log.info("此付款结算单冲销开始.结算单号:{},付款单号:{},付款单id:{}", orderSettlementForm.getNo(), paymentApplyOrder.getNo(), paymentApplyOrder.getId());
                List<String> syncStockInOrderNos = new LinkedList<>();
                List<String> syncStockOutOrderNos = new LinkedList<>();
                List<SkuUnitDto> diffSkuUnitDto = getDiffSkuUnitDto(sortedList1, sortedList2);
                PaymentOrderWriteOffLog diffSkuLog = createTraceLog(paymentApplyOrder, orderSettlementForm, "冲销流程.SKU明细数量差列表:" + JsonUtil.toJson(diffSkuUnitDto));
                iPaymentOrderWriteOffLogService.save(diffSkuLog);

                List<WriteOffProcess> writeOffProcesses = buildProcessList(paymentApplyOrder, orderSettlementForm,
                        purchaseOrderList, diffSkuUnitDto, mockSync, manual, syncStockInOrderNos, syncStockOutOrderNos);
                Boolean processResFlag = processDispatcher(writeOffProcesses, paymentApplyOrder, orderSettlementForm);
                String msg = StrUtil.format("此付款结算单冲销结束,付款单号:{},id:{},结算单号:{},结果:{}",
                        paymentApplyOrder.getNo(), paymentApplyOrder.getId(), orderSettlementForm.getNo(), processResFlag);
                log.info(msg);

                // 冲销成功，刷新 SKU 冲销资质
                if (processResFlag) {
                    updateSkuWriteOffFlag(settlementDetails);
                }
                // 流程中创建的出入库单已经同步到金蝶，但是状态为待创建，需要二次触发审核
                submitStockOrder(processResFlag, mockSync, syncStockInOrderNos, syncStockOutOrderNos, paymentApplyOrder, orderSettlementForm);
                String logUrl = getLogUrl(paymentApplyOrderId);
                Alert.text(MessageRobotCode.GLOBAL, "[付款冲销]:" + msg + StrUtil.CR + "[冲销日志]:" + logUrl);
            } else {
                // 完结对应的采购单
                iPurchaseOrderService.lambdaUpdate()
                        .set(PurchaseOrder::getState, PurchaseOrderState.FINISHED.getValue())
                        .in(PurchaseOrder::getId, purchaseOrderIds)
                        .update();
                String msg = StrUtil.format("此付款结算单无需处理,付款单号:{},id:{},结算单号:{}",
                        paymentApplyOrder.getNo(), paymentApplyOrder.getId(), orderSettlementForm.getNo());
                Alert.text(MessageRobotCode.GLOBAL, "[付款冲销]:" + msg);
            }
        });
    }

    public List<SkuUnitDto> getSortedList1(List<PurchaseOrderDetail> purchaseOrderDetailList) {
        List<SkuUnitDto> collect = purchaseOrderDetailList.stream().map(val -> {
            SkuUnitDto skuUnitDto = new SkuUnitDto();
            skuUnitDto.setSkuCode(val.getItemSkuCode());
            skuUnitDto.setNum(val.getPurchaseQuantity());
            skuUnitDto.setPrice(val.getTaxPrice().setScale(2, RoundingMode.HALF_UP));
            return skuUnitDto;
        }).collect(Collectors.toList());
        List<SkuUnitDto> reduce = reduce(collect);
        return reduce.stream().sorted(Comparator.comparing(SkuUnitDto::getSkuCode)).collect(Collectors.toList());
    }

    private List<SkuUnitDto> getSortedList2(List<OrderSettlementDetail> orderSettlementDetails) {
        List<SkuUnitDto> collect = orderSettlementDetails.stream().map(val -> {
            SkuUnitDto skuUnitDto = new SkuUnitDto();
            skuUnitDto.setSkuCode(val.getSkuCode());
            skuUnitDto.setNum(val.getSettlementQuantity());
            skuUnitDto.setPrice(val.getSettlementPrice().setScale(2, RoundingMode.HALF_UP));
            return skuUnitDto;
        }).collect(Collectors.toList());
        List<SkuUnitDto> reduce = reduce(collect);
        return reduce.stream().sorted(Comparator.comparing(SkuUnitDto::getSkuCode)).collect(Collectors.toList());
    }

    private List<SkuUnitDto> reduce(List<SkuUnitDto> list) {
        return list.stream()
                .collect(Collectors.toMap(
                        orderSettlementDetail -> orderSettlementDetail.getSkuCode() + "_" + orderSettlementDetail.getPrice(),
                        a -> a,
                        (o1, o2) -> {
                            o1.setNum(o1.getNum() + o2.getNum());
                            o1.setSkuCode(o1.getSkuCode());
                            o1.setPrice(o1.getPrice());
                            return o1;
                        })).values()
                .stream().sorted(Comparator.comparing(SkuUnitDto::getSkuCode)).collect(Collectors.toList());
    }

    private List<SkuUnitDto> getDiffSkuUnitDto(List<SkuUnitDto> pList, List<SkuUnitDto> rList) {
        List<SkuUnitDto> list = new LinkedList<>();
        Map<Object, List<SkuUnitDto>> pMap = pList.stream().collect(Collectors.groupingBy(skuUnitDto -> skuUnitDto.getSkuCode() + skuUnitDto.getPrice()));

        for (SkuUnitDto skuUnitDto : rList) {
            String key = skuUnitDto.getSkuCode() + skuUnitDto.getPrice();
            List<SkuUnitDto> pValList = pMap.get(key);
            if (CollUtil.isEmpty(pValList)) {
                list.add(skuUnitDto);
            } else {
                SkuUnitDto pVal = pValList.get(0);
                SkuUnitDto newOne = new SkuUnitDto();
                newOne.setSkuCode(skuUnitDto.getSkuCode());
                newOne.setNum(skuUnitDto.getNum() - pVal.getNum());
                newOne.setPrice(pVal.getPrice());
                list.add(newOne);
                pMap.remove(key);
            }
        }
        if (CollUtil.isNotEmpty(pMap)) {
            for (List<SkuUnitDto> value : pMap.values()) {
                SkuUnitDto skuUnitDto = value.get(0);
                SkuUnitDto newOne = new SkuUnitDto();
                newOne.setSkuCode(skuUnitDto.getSkuCode());
                newOne.setNum(-skuUnitDto.getNum());
                newOne.setPrice(skuUnitDto.getPrice());
                list.add(newOne);
            }
        }

        return list.stream().filter(val -> val.getNum() != 0).collect(Collectors.toList());
    }


    private void updateSkuWriteOffFlag(List<OrderSettlementDetail> list) {
        list.forEach(val -> val.setHadWriteOff(1));
        iOrderSettlementDetailService.updateBatchById(list);
    }

    private void submitStockOrder(Boolean processResFlag, Boolean mockSync,
                                  List<String> syncStockInOrderNos, List<String> syncStockOutOrderNos,
                                  PaymentApplyOrder paymentApplyOrder, OrderSettlementForm orderSettlementForm) {
        // 如果冲销完毕，过程中产生的出入库单数据提交审核到金蝶
        if (processResFlag) {
            if (!mockSync) {
                log.info("冲销流程,待提交审核到金蝶的采购入库单:{},退料出库单:{}", JsonUtil.toJson(syncStockInOrderNos), JsonUtil.toJson(syncStockOutOrderNos));
                submitStockOrderToKingDee(syncStockInOrderNos, ApiEnum.SAVE_STOCK_IN_ORDER, paymentApplyOrder, orderSettlementForm);
                submitStockOrderToKingDee(syncStockOutOrderNos, ApiEnum.SAVE_STOCK_OUT_ORDER, paymentApplyOrder, orderSettlementForm);
            }
        }
    }

    private String getLogUrl(Long paymentApplyOrderId) {
        String activeProfile = SpringUtil.getActiveProfile();
        if (activeProfile.equals("local") || activeProfile.equals("test")) {
            return "http://api4j.dlab.cn/supplier/item/payment/order/getWriteOffLog?id=" + paymentApplyOrderId;
        }
        if (activeProfile.equals("gray") || activeProfile.equals("prod")) {
            return "https://supplierapi-gray.daddylab.com/supplier/item/payment/order/getWriteOffLog?id=" + paymentApplyOrderId;
        }
        return StrUtil.EMPTY;
    }

    private void submitStockOrderToKingDee(List<String> nos, ApiEnum apiEnum,
                                           PaymentApplyOrder paymentApplyOrder, OrderSettlementForm orderSettlementForm) {
        if (CollUtil.isNotEmpty(nos)) {
            SubmitAndAuditRes submitAndAuditRes = reqTemplate.submitAndAuditStockOrder(apiEnum.getFormId(), nos);
            String format = StrUtil.format("冲销结束,付款单号:{},{},{}提交审核到金蝶,res:{}",
                    paymentApplyOrder.getNo(), JsonUtil.toJson(nos), apiEnum.getDesc(), JsonUtil.toJson(submitAndAuditRes));
            PaymentOrderWriteOffLog stepFirstLog = createTraceLog(paymentApplyOrder, orderSettlementForm, format);
            iPaymentOrderWriteOffLogService.save(stepFirstLog);
        }
        /*else {
            PaymentOrderWriteOffLog log = createTraceLog(paymentApplyOrder, orderSettlementForm,
                    StrUtil.format("此付款单结算和采购数据存在差异,冲销结束,竟然没有{}提交审核到金蝶？出现异常了", apiEnum.getDesc()));
            iPaymentOrderWriteOffLogService.save(log);
        }*/
    }


    /**
     * 付款单参数检查
     *
     * @param paymentApplyOrder 付款单
     * @param manual            是否手动，如果不是手动触发冲销流程，检查付款单财务审核时间和付款单状态
     */
    private void checkParam(PaymentApplyOrder paymentApplyOrder, Boolean manual) {
        String no = paymentApplyOrder.getNo();
        boolean b1 = paymentApplyOrder.getHadWriteOff() == 0;
        boolean b2 = true;
        if (!manual) {
            b2 = Objects.nonNull(paymentApplyOrder.getFinancialAuditTime());
        }
        boolean bb = b1 && b2;
        if (!bb) {
            // [付款冲销]:付款单冲销流程触发失败.编码:FKD202402211001,历史冲销次数:0,付款状态:FINISH,财务审核时间:1708483037
            String error = StrUtil.format("付款单冲销流程触发失败.编码:{},历史冲销次数:{},付款状态:{},财务审核时间:{}",
                    no, paymentApplyOrder.getHadWriteOff(), paymentApplyOrder.getStatus(), paymentApplyOrder.getFinancialAuditTime());
            log.error("付款单冲销流程触发失败,id:{},no:{},error:{}", paymentApplyOrder.getId(), paymentApplyOrder.getNo(), error);
            Alert.text(MessageRobotCode.GLOBAL, "[付款冲销]:" + error);
        }
        Assert.state(bb, "[付款冲销]前置状态检查失败");
    }

    private List<WriteOffProcess> buildProcessList(PaymentApplyOrder paymentApplyOrder, OrderSettlementForm orderSettlementForm,
                                                   List<PurchaseOrder> purchaseOrderList, List<SkuUnitDto> diffSkuUnitDto,
                                                   Boolean mockSync, Boolean manual,
                                                   List<String> syncStockInOrderNos, List<String> syncStockOutOrderNos) {
        List<WriteOffProcess> list = new ArrayList<>();

        ProcessRequest reverseRequest = ProcessRequest.builder()
                .purchaseOrderList(purchaseOrderList)
                .stepLog(createTraceLog(paymentApplyOrder, orderSettlementForm, StrUtil.EMPTY))
                .mockSync(mockSync)
                .syncStockInOrderNos(syncStockInOrderNos)
                .syncStockOutOrderNos(syncStockOutOrderNos)
                .build();
        this.reverseOldStockOrderProcess.setProcessRequest(reverseRequest);
        list.add(this.reverseOldStockOrderProcess);

        ProcessRequest fixedPurchaseRequest = ProcessRequest.builder()
                .purchaseOrderList(purchaseOrderList)
                .orderSettlementForm(orderSettlementForm)
                .stepLog(createTraceLog(paymentApplyOrder, orderSettlementForm, StrUtil.EMPTY))
                .mockSync(mockSync)
                .syncStockInOrderNos(syncStockInOrderNos)
                .syncStockOutOrderNos(syncStockOutOrderNos)
                .build();
        this.generateFixedPurchaseProcess.setProcessRequest(fixedPurchaseRequest);
        list.add(this.generateFixedPurchaseProcess);

        ProcessRequest salesOrderRequest = ProcessRequest.builder()
                .orderSettlementForm(orderSettlementForm)
                .diffSkuUnitDtoList(diffSkuUnitDto)
                .stepLog(createTraceLog(paymentApplyOrder, orderSettlementForm, StrUtil.EMPTY))
                .mockSync(mockSync)
                .build();
        if (manual) {
            salesOrderRequest.setAuditDate(DateUtil.currentTime());
        } else {
            salesOrderRequest.setAuditDate(paymentApplyOrder.getFinancialAuditTime());
        }
        this.supplementSalesOrderProcess.setProcessRequest(salesOrderRequest);
        list.add(this.supplementSalesOrderProcess);

        return list.stream().sorted(Comparator.comparing(val -> val.getClass().getAnnotation(Order.class).value()))
                .collect(Collectors.toList());
    }

    private Boolean processDispatcher(List<WriteOffProcess> processList, PaymentApplyOrder paymentApplyOrder, OrderSettlementForm orderSettlementForm) {
        PaymentOrderWriteOffLog stepFirstLog = createTraceLog(paymentApplyOrder, orderSettlementForm, "冲销检查结果为true,流程起步");
        iPaymentOrderWriteOffLogService.save(stepFirstLog);

        Boolean processRes = processHandler(processList, 0);

        PaymentOrderWriteOffLog stepLastLog = createTraceLog(paymentApplyOrder, orderSettlementForm, "冲销检查结果为true,流程结束.res:" + processRes);
        iPaymentOrderWriteOffLogService.save(stepLastLog);
        return processRes;
    }

    private PaymentOrderWriteOffLog createTraceLog(PaymentApplyOrder paymentApplyOrder, OrderSettlementForm orderSettlementForm, String traceMsg) {

        PaymentOrderWriteOffLog writeOffLog = new PaymentOrderWriteOffLog();
        writeOffLog.setPaymentApplyOrderId(paymentApplyOrder.getId());
        writeOffLog.setPaymentApplyOrderNo(paymentApplyOrder.getNo());
        writeOffLog.setDetailSource(1);
        writeOffLog.setSubOrderId(orderSettlementForm.getId());
        writeOffLog.setSubOrderNo(orderSettlementForm.getNo());
        writeOffLog.setTraceMsg(traceMsg);
        return writeOffLog;
    }

    private Boolean processHandler(List<WriteOffProcess> processList, Integer index) {
        if (index >= processList.size()) {
            return true;
        }
        WriteOffProcess writeOffProcess = processList.get(index);
        if (Objects.nonNull(writeOffProcess)) {
            if (writeOffProcess.doProcess()) {
                index++;
                return processHandler(processList, index);
            }
        }
        return false;
    }

}
