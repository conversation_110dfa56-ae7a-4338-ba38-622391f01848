

package com.daddylab.supplier.item.controller.test.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2024年09月04日 3:59 PM
 */
@Data
public class LogisticsSheet {
    /**
     * 物流单号、物流公司、发货时间、物流状态、异常类型、订单编号、订单状态、商品名称、颜色/规格、数量、处理人、处理时间
     */

    @ExcelProperty("物流单号")
    private String logisticNo;
    @ExcelProperty("物流公司")
    private String logisticCompany;
    @ExcelProperty("发货时间")
    private String consignTime;
    @ExcelProperty("物流状态")
    private String logisticStatus;
    @ExcelProperty("异常类型")
    private String exceptionType;
    @ExcelProperty("异常状态")
    private String exceptionStatus;
    @ExcelProperty("订单编号")
    private String orderNo;
    @ExcelProperty("订单状态")
    private String orderStatus;
    @ExcelProperty("商品信息")
    private String itemInfo;
    @ExcelProperty("处理人")
    private String operator;
    @ExcelProperty("处理时间")
    private String operateTime;

    @ExcelProperty("处理次数")
    private Long handlerNum = 0L;

}
