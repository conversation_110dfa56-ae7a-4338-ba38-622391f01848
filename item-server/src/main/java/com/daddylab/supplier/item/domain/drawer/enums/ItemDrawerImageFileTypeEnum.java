package com.daddylab.supplier.item.domain.drawer.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.Getter;

/**
 * Class  ItemDrawerImageTypeEnum
 *
 * @Date 2022/6/1上午11:32
 * <AUTHOR>
 */
@Getter
public enum ItemDrawerImageFileTypeEnum implements IEnum<Integer> {

    /**
     * 文件类型 1-图片 2-视频
     */
    IMAGE(1,"图片"),
    VIDEO(2, "视频");

    ItemDrawerImageFileTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
    private final Integer value;
    private final String desc;
}
