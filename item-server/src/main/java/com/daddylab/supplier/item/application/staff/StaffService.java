package com.daddylab.supplier.item.application.staff;

import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.domain.staff.vo.DepartmentHeadBriefVO;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Class  DadStaffService
 *
 * @Date 2022/6/1上午10:20
 * <AUTHOR>
 */
public interface StaffService {

    DepartmentHeadBriefVO getStaffDepartmentHeadBriefVO(Long userId);

    Optional<StaffBrief> getStaffBrief(Long userId);

    /**
     * 根据userId 获取职工简单信息
     *
     * @param userId 用户ID
     * @param cache 是否从缓存中读取
     */
    Optional<StaffBrief> getStaffBrief(Long userId, boolean cache);

    /**
     * 获取职工简单信息
     *
     * @param userIds 用户ID
     * @return
     */
    List<StaffBrief> getStaffBriefList(List<Long> userIds);

    /**
     * 根据userId 获取职工信息
     *
     * @param userId
     * @return
     */
    DadStaffVO getStaff(Long userId);

    /**
     * 获取职工信息
     *
     * @param userIds
     * @return
     */
    List<DadStaffVO> getStaffList(List<Long> userIds);

    /**
     * 生成映射
     * key: userId
     * val: DadStaffVO
     *
     * @param userIds
     * @return
     */
    Map<Long, DadStaffVO> getStaffMap(List<Long> userIds);

    /**
     * 根据登录名获取员工信息
     *
     * @param loginNames 登录名
     */
    List<DadStaffVO> getStaffListByLoginName(List<String> loginNames);
    Optional<StaffBrief> getStaffByLoginName(String loginName);
    Optional<StaffBrief> getStaffByNickName(String nickName);

    List<StaffBrief> getStaffListByNickName(Collection<String> nickNames);


    /**
     * 获取所有的上级名称
     *
     * @param id
     * @return
     */
    String getAllParentDeptName(Long id, String delimiter);
}
