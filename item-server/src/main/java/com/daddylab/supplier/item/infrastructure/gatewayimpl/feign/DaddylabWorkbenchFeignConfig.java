package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import feign.RequestInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

/**
 * 工作台请求拦截器
 *
 * <AUTHOR> up
 * @date 2024年04月22日 2:14 PM
 */
public class DaddylabWorkbenchFeignConfig {

    @Value("${ms-workbench-server.authorization:c65vSKX4MUVEWRaGzmu3p7IbeJtoTZf9}")
    private String authorization;

    @Bean
    public RequestInterceptor customRequestInterceptor() {
        return template -> template.header("Authorization", authorization);
    }

}
