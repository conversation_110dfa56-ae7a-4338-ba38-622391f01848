package com.daddylab.supplier.item.application.item.combinationItem.dto.cmd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.Id;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/11 10:44 上午
 * @description
 */
@Data
@ApiModel("组合商品的单品sku对象入参")
public class
ComposeSkuCmd {

    @ApiModelProperty("skuId")
    @DiffIgnore
    private Long skuId;

    @Id
    @ApiModelProperty("skuCode")
    @NotBlank(message = "skuCode不得为空")
    private String skuCode;

    @ApiModelProperty("单品数量")
    @NotNull(message = "单品数量不得为空")
    private Integer count;

    @ApiModelProperty("条码")
    private String barCode = "";

    @ApiModelProperty("成本金额占比")
    @NotNull(message = "成本金额占比不得为空")
    private BigDecimal costProportion;

    @ApiModelProperty("销售金额占比")
    @NotNull(message = "销售金额占比不得为空")
    private BigDecimal salesProportion;

    @ApiModelProperty("sku的商品id不得为空")
    @NotNull(message = "sku的商品id不得为空")
    private Long itemId;

    @ApiModelProperty("sku的商品code")
    @NotBlank(message = "sku的商品code不得为空")
    private String itemCode;



}
