package com.daddylab.supplier.item.controller.orders;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.shortlink.feign.dto.Result;
import com.daddylab.supplier.item.controller.orders.dto.UserInfoDecryptRequest;
import com.daddylab.supplier.item.controller.orders.dto.UserInfoDecryptResult;
import com.daddylab.supplier.item.infrastructure.userInfoCrypto.UserInfoCrypto;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/4/15
 */
@RequestMapping("/userInfoCrypt")
@RestController
public class UserInfoCryptController {

  @Autowired UserInfoCrypto userInfoCrypto;

  @RequestMapping(value = "/decrypt", method = RequestMethod.POST)
  @ApiOperation(value = "用户信息解密")
  public SingleResponse<UserInfoDecryptResult> decrypt(
      @RequestBody UserInfoDecryptRequest request) {
    final String clearText = userInfoCrypto.decrypt(request.getCipherText());
    final UserInfoDecryptResult userInfoDecryptResult = new UserInfoDecryptResult();
    userInfoDecryptResult.setClearText(clearText);
    return SingleResponse.of(userInfoDecryptResult);
  }
}
