package com.daddylab.supplier.item.application.purchase.order.factory;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.message.QyMsgConfig;
import com.daddylab.supplier.item.application.item.combinationItem.CombinationItemBizService;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.WrapperType;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.infrastructure.config.QyWeixinDaddyErpProperties;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CombinationItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemSkuMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.QyWeixinFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.SendMsgQyWeixinResult;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.SendQyWeixinMsgParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @auIhor seven up
 * @date 2022年11月11日 11:27 AM
 */
@Component
@Slf4j
public class CommonUtil {

    @Resource
    IWdtOrderDetailWrapperErrorService iWdtOrderDetailWrapperErrorService;

    @Resource
    IItemSkuService iItemSkuService;

    @Resource
    ItemSkuMapper itemSkuMapper;

    @Resource
    IItemSkuPriceService iItemSkuPriceService;

    @Resource
    ItemGateway itemGateway;

    @Resource
    CombinationItemMapper combinationItemMapper;

    @Resource
    MsgSender msgSender;

    @Resource
    IWdtOrderDetailWrapperService iWdtOrderDetailWrapperService;

    @Resource
    ItemSkuGateway itemSkuGateway;

    @Resource
    CombinationItemBizService combinationItemBizService;


    public void addErrorLog(String time, String code, String tradeNo, String errorMsg) {
        WdtOrderDetailWrapper.Builder builder = WdtOrderDetailWrapper.Builder.builder();
        builder.operateTime(time).skuCode(code).type(WrapperType.ERROR);
        WdtOrderDetailWrapper wrapper = builder.build();
        WdtOrderDetailWrapperError error = WdtOrderDetailWrapperError.ofWrapper(wrapper, errorMsg);
        error.setTradeNo(tradeNo);
        iWdtOrderDetailWrapperErrorService.asyncSave(error);
    }

    /**
     * 将wdt的platformId转为erp的店铺id,数据存在wrapper中。
     * <p>
     * 活动价格价中平台类型定义
     * 0:ALL 1:淘宝 2:抖店 3:小红书 4:自研电商 5:有赞 6:快手
     * <p>
     * <p>
     * wdt中 平台id定义
     * .put(1, Platform.TAOBAO)
     * .put(17, Platform.YOUZAN)
     * .put(69, Platform.DOUDIAN)
     * .put(75, Platform.KUAISHOU)
     * .put(56, Platform.XIAOHONGSHU)
     * .put(127, Platform.LAOBASHOP)
     */
    public Integer getErpPurchasePricePlatformType(Integer wdtPlatformId) {
        if (Objects.isNull(wdtPlatformId)) {
            return 0;
        }
        if (wdtPlatformId == 127) {
            return 4;
        }
        if (wdtPlatformId == 56) {
            return 3;
        }
        if (wdtPlatformId == 75) {
            return 6;
        }
        if (wdtPlatformId == 69) {
            return 2;
        }
        if (wdtPlatformId == 17) {
            return 5;
        }
        if (wdtPlatformId == 1) {
            return 1;
        }
        return 0;
    }

    /**
     * 根据wdt商家编码查询erp库的sku
     *
     * @param wdtSpecCode
     * @return
     */
    public ItemSku getItemSku0(String wdtSpecCode) {
        List<ItemSku> list = iItemSkuService.lambdaQuery()
                .eq(ItemSku::getSkuCode, wdtSpecCode)
                .or()
                .eq(ItemSku::getProviderSpecifiedCode, wdtSpecCode)
                .orderByDesc(ItemSku::getId)
                .last("limit 1")
                .list();
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }


    public Item getItem0(Long itemId) {
        return itemGateway.getItem(itemId);
    }


    /**
     * 根据下单时间计算成本
     *
     * @param code    兼容skuCode和combinationCode
     * @param payTime
     * @return
     */
    public BigDecimal getCostPriceByPayTime(String code, Long payTime) {
        List<ItemSkuPrice> list = iItemSkuPriceService.lambdaQuery()
                .eq(ItemSkuPrice::getSkuCode, code)
                .eq(ItemSkuPrice::getType, 0)
                .le(ItemSkuPrice::getStartTime, payTime)
                .orderByDesc(ItemSkuPrice::getId)
                .select(ItemSkuPrice::getPrice).last("limit 1").list();
        if (CollUtil.isEmpty(list)) {
            return null;
        } else {
            return list.get(0).getPrice();
        }
    }

    public Long getProviderId(ItemSku itemSku) {
        return Objects.nonNull(itemSku.getProviderId()) && itemSku.getProviderId() != 0 ? itemSku.getProviderId() :
                itemGateway.getProviderIdBySkuCode(itemSku.getSkuCode());
    }

    /**
     * 系统采购单
     *
     * @param operateTime
     * @param users
     */
    public void remindPurchaseOrder(String operateTime, List<String> users) {
        users.forEach(user -> {
            WechatMsg wechatMsg = new WechatMsg();
            wechatMsg.setTitle("\uD83E\uDD17尊敬的陛下~~~");
            wechatMsg.setContent(StrUtil.format("月份：{}，系统采购单已经处理完成，请您查验", operateTime));
            wechatMsg.setRecipient(user);
            wechatMsg.setLink("");
            sendMsgWithTextSpecialVersion(wechatMsg);
        });
    }

    public void remind(String operateTime, List<String> users, String msg) {
        users.forEach(user -> {
            WechatMsg wechatMsg = new WechatMsg();
            wechatMsg.setTitle("\uD83E\uDD17尊敬的陛下~~~");
            wechatMsg.setContent(StrUtil.format("月份：{}。", operateTime) + msg);
            wechatMsg.setRecipient(user);
            wechatMsg.setLink("");
            sendMsgWithTextSpecialVersion(wechatMsg);
        });
    }


    /**
     * 提醒出入库单据
     *
     * @param operateTime
     * @param users
     */
    public void remindStockOrder(String operateTime, List<String> users) {
        users.forEach(user -> {
            WechatMsg wechatMsg = new WechatMsg();
            wechatMsg.setTitle("\uD83E\uDD17尊敬的陛下~~~");
            wechatMsg.setContent(StrUtil.format("月份：{}，系统出入库单已经生成，并且同步金蝶完成，请您查验", operateTime));
            wechatMsg.setRecipient(user);
            wechatMsg.setLink("");
            sendMsgWithTextSpecialVersion(wechatMsg);

        });
    }

    /**
     * 提醒系统结算账单
     *
     * @param operateTime
     * @param url
     * @param users
     */
    public void remainBill(String operateTime, String url, List<String> users, Long totalMinutes) {
        users.forEach(val -> {
            WechatMsg wechatMsg = new WechatMsg();
            wechatMsg.setTitle("\uD83E\uDD17尊敬的陛下~~~");
            wechatMsg.setContent(StrUtil.format("月份：{}，系统采购单账单数据生成完毕(耗时:{}分钟)，点击可直接下载", operateTime, totalMinutes));
            wechatMsg.setRecipient(val);
            wechatMsg.setLink(url);
            sendMsgWithTextSpecialVersion(wechatMsg);
        });
    }

    /**
     * 待点击链接的消息提醒
     *
     * @param operateTime
     * @param url
     * @param users
     * @param msg
     */
    public void remainWithUrl(String operateTime, String url, List<String> users, String msg) {
        users.forEach(val -> {
            WechatMsg wechatMsg = new WechatMsg();
            wechatMsg.setTitle("\uD83E\uDD17尊敬的陛下~~~");
            wechatMsg.setContent(StrUtil.format("月份：{}。{}", operateTime, msg));
            wechatMsg.setRecipient(val);
            wechatMsg.setLink(url);
            sendMsgWithTextSpecialVersion(wechatMsg);
        });
    }


    /**
     * 提醒结算单据自动生成
     *
     * @param operateTime
     * @param users
     */
    public void remainOrderSettlement(String operateTime, List<String> users) {
        users.forEach(val -> {
            WechatMsg wechatMsg = new WechatMsg();
            wechatMsg.setTitle("\uD83E\uDD17尊敬的陛下~~~");
            wechatMsg.setContent(StrUtil.format("月份：{}，系统结算单据自动生成完毕", operateTime));
            wechatMsg.setRecipient(val);
            wechatMsg.setLink("");
            sendMsgWithTextSpecialVersion(wechatMsg);
        });
    }

    public Integer getSkuBusinessLine(String skuCode) {
        return itemSkuGateway.getSkuBusinessLine(skuCode);
    }

    public Integer getCombinationBusinessLine(Long combinationId) {
        return combinationItemBizService.getBusinessLine(combinationId);
    }

    public Integer getItemBusinessLine(Long itemId){
        return itemGateway.getBusinessLine(itemId);
    }

    /**
     * 判断组合装下属的单品是否全部来自于同一个供应商
     *
     * @param combinationItemCode 组合装编码
     * @return true or false
     */
    public Boolean allComposeFromSameProvider(String combinationItemCode) {
        List<Long> composeSkuProviderIds = combinationItemMapper.getComposeSkuProviderIds(combinationItemCode);
        return CollUtil.isNotEmpty(composeSkuProviderIds) && composeSkuProviderIds.size() == 1;
    }

    /**
     * 判断 这个sku是否属于这个单品。
     *
     * @param skuId
     * @param combinationItemId
     * @return
     */
    public Boolean skuBelongToCombination(Long skuId, Long combinationItemId) {
        return combinationItemMapper.skuBelongToCombination(skuId, combinationItemId) > 0;
    }

    /**
     * 拆分wrapper，根据补全数量和剩余数量拆成两条数据。
     *
     * @param oldWrapper               旧的wrapper
     * @param oldWrapperUpdateQuantity 旧的wrapper需要更新的数量
     * @param newWrapperQuantity       优惠价的新建wrapper的数量
     * @return 补全数量的新的wrapper的ID。
     */
    @Transactional(rollbackFor = Exception.class)
    public void splitWrapper(WdtOrderDetailWrapper oldWrapper, Integer oldWrapperUpdateQuantity,
                             Integer newWrapperQuantity, BigDecimal newPrice) {
        WdtOrderDetailWrapper complementOne = buildComplementWrapper(oldWrapper, newWrapperQuantity, newPrice);
        iWdtOrderDetailWrapperService.save(complementOne);

        oldWrapper.setQuantity(oldWrapperUpdateQuantity);
        iWdtOrderDetailWrapperService.updateById(oldWrapper);
    }

    private WdtOrderDetailWrapper buildComplementWrapper(WdtOrderDetailWrapper source, Integer q, BigDecimal price) {
        WdtOrderDetailWrapper target = new WdtOrderDetailWrapper();
        target.setSkuCode(source.getSkuCode());
        target.setPrice(price);
        target.setType(source.getType());
        target.setSuiteNo(source.getSuiteNo());
        target.setNum(source.getNum());
        target.setTradeId(source.getTradeId());
        target.setTradeNo(source.getTradeNo());
        target.setRecId(source.getRecId());
        target.setQuantity(q);
        target.setOperateTime(source.getOperateTime());
        target.setIsGift(source.getIsGift());
        target.setPayTime(source.getPayTime());
        target.setPlatformType(source.getPlatformType());
        target.setWarehouseNo(source.getWarehouseNo());
        target.setProviderId(source.getProviderId());
        target.setSuiteDbId(source.getSuiteDbId());
        target.setRefundNum(source.getRefundNum());
        target.setDeliverNum(source.getDeliverNum());
        target.setBusinessLine(source.getBusinessLine());
        return target;
    }


    @Autowired
    private QyWeixinFeignClient qyWeixinFeignClient;

    @Autowired
    private QyWeixinService qyWeixinService;

    @Autowired
    IWechatMsgService iWechatMsgService;

    @Autowired
    QyWeixinDaddyErpProperties qyWeixinDaddyErpProperties;

    @Autowired
    QyMsgConfig qyMsgConfig;

    public void sendMsgWithTextSpecialVersion(WechatMsg wechatMsg) {
        try {
            String token = qyWeixinService.getTokenWithCache();
            SendQyWeixinMsgParam param = new SendQyWeixinMsgParam();

            SendQyWeixinMsgParam.Text text = new SendQyWeixinMsgParam.Text();
            String content =
                    StringUtils.hasText(wechatMsg.getTitle())
                            ? (wechatMsg.getTitle() + "\n" + wechatMsg.getContent())
                            : wechatMsg.getContent();
            String link = wechatMsg.getLink();
            String s;
            if (StringUtils.hasText(link)) {
                s = "<a href=" + link + ">" + content + "</a>";
            } else {
                s = content;
            }
            text.setContent(s);

            param.setText(text);
            param.setAgentid(qyWeixinDaddyErpProperties.getAgentid());
            param.setMsgtype("text");
            param.setTouser(wechatMsg.getRecipient());
            final SendMsgQyWeixinResult sendMsgQyWeixinResult = qyWeixinFeignClient.sendMessage(token, param);
            if (!sendMsgQyWeixinResult.isSendSuccess()) {
                log.error("发送文本消息失败.id:{},res:{}", wechatMsg.getId(), sendMsgQyWeixinResult.getErrmsg());
            }
        } catch (Exception e) {
            log.error("发送文本消息失败,id:{}", wechatMsg.getId(), e);
        }
    }
}
