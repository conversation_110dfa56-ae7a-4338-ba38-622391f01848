package com.daddylab.supplier.item.infrastructure.config;

import com.google.common.collect.Lists;
import java.util.List;
import java.util.Map;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 动态刷新配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/30 10:20 上午
 * @description
 */
@Component
@RefreshScope
@Data
public class RefreshConfig {

  /** 拦截器白名单 */
  @Value("${sys.login.whiteuri:}")
  private String whiteUri;

  @Value("${sys.mock.text:1}")
  private String mockText;

  @Value("${sys.mock.mail:1}")
  private String mockMail;

  @Value("${sys.disablePlatformItemSync:false}")
  @Deprecated
  private Boolean disablePlatformItemSync;

  @Value("${sys.disablePlatformItemSyncMessage:false}")
  private Boolean disablePlatformItemSyncMessage;

  @Value("${sys.fetchWdtPlatformItemByQimen:false}")
  private Boolean fetchWdtPlatformItemByQimen;

  @Value("${sys.fetchWdtOrderByQimen:true}")
  private Boolean fetchWdtOrderByQimen;

  @Value("${sys.fetchWdtRefundOrderByQimen:true}")
  private Boolean fetchWdtRefundOrderByQimen;

  @Value("${sys.fetchWdtSaleStockOutOrderConfigs:0}")
  private String fetchWdtSaleStockOutOrderConfigs;

  @Value("${kingdee.sync:0}")
  private String kingDeeSync;

  /** MQ消息消费重复检查时间范围（默认600秒内发生重复拒绝执行） */
  @Value("${sys.mqRepeatConsumeCheckTime:600}")
  private Integer mqRepeatConsumeCheckTime;

  @Value("${aws.mock:0}")
  private String awsMock;

  @Value("${sys.itemPushToWdt:false}")
  private Boolean itemPushToWdt;

  @Value("${sys.refundOrderQueryTimeStartLimitForPSys:}")
  private String refundOrderQueryTimeStartLimitForPSys;

  /** 新品商品修改会触发重新审核的状态配置 */
  @Value("${sys.statusOfTriggersReAudit:5,7}")
  private List<Integer> statusOfTriggersReAudit;

  /** 新品商品修改会触发重新审核的状态配置【针对直播话术单独处理】 */
  @Value("${sys.statusOfTriggersReAuditForLiveVerbalTrick:6,5,7}")
  private List<Integer> statusOfTriggersReAuditForLiveVerbalTrick;

  /** 当商品抽屉图元信息丢失时，是否跳过检查 */
  @Value("${sys.skipCheckWhenDrawerImageMetaMissing:true}")
  private Boolean skipCheckWhenDrawerImageMetaMissing;

  /** 商品同步小程序时是否对 URL 进行编码 */
  @Value("${sys.encodeUrlForSyncMiniItem:false}")
  private Boolean encodeUrlForSyncMiniItem;

  /** 小程序跳转地址-商品详情 */
  @Value(
      "${sys.wxMiniItemDetailUrl:https://cloud1-6g2z6yh8b087381b-1305265685.tcloudbaseapp.com/?t=pd&p=%s}")
  private String wxMiniItemDetailUrl;

  /** 默认商家后台店铺ID */
  @Value("${sys.defaultMallShopId:0}")
  private Long defaultMallShopId;

  /** 启用多店版本 */
  @Value("${sys.enableMultiShopVersion:false}")
  private Boolean enableMultiShopVersion;

  /** ERP系统域名 */
  @Value("${sys.domain:https://erp-ol.daddylab.com/erp}")
  private String domain;

  /** ERP系统域名 */
  @Value("${sys.apiDomain:http://127.0.0.1:8086/supplier/item/}")
  private String apiDomain;

  /** 订单发货跟踪消息提醒是否开启真实发送 */
  @Value("${sys.orderDeliveryTraceMsgRealSend:false}")
  private Boolean orderDeliveryTraceMsgRealSend;

  /** 客服售后登记数据拉取是否详细记录数据来源 */
  @Value("${sys.afterSalesRegisterFetchTraceDetails:true}")
  private Boolean afterSalesRegisterFetchTraceDetails;

  /** 导出文件在上传前是否保存到本地 */
  @Value("${sys.exportFileSaveLocal:false}")
  private Boolean exportFileSaveLocal;

  /** CV任务信号量 PERMIT */
  @Value("${sys.cvSemaphore:1}")
  private Integer cvSemaphore;

  @Value("${sys.internalAuth:}")
  private String internalAuth;

  /** 禁用缓存 */
  @Value("${sys.disableCache:false}")
  private boolean disableCache;

  /**
   * 同步项目拓展信息的商品类目
   *
   * @deprecated 不用了，以P系统的拓展信息为准，有就推
   */
  @Value("${sys.categoriesOfSyncItemExtendInfo:个护美妆}")
  private List<String> categoriesOfSyncItemExtendInfo;

  /** 是否禁用授权检查 */
  @Value("${sys.authorizationCheckDisabled:false}")
  private boolean authorizationCheckDisabled;

  /**
   * 权限过滤
   */
  private Map<Long, List<String>> aclResourceFilter;


}
