package com.daddylab.supplier.item.domain.dataFetch;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2022/4/24
 */
public interface DataFetchManager {

    /**
     * 启动
     */
    void start();

    /**
     * 暂停
     */
    void pause();

    /**
     * 恢复
     */
    void resume();

    /**
     * 停止
     */
    void stop();

    /**
     * 指定时间段内重新拉取
     *
     * @param fetchDataType 数据类型
     * @param startTime     开始时间
     * @param endTime       结束时间
     */
    void reFetch(FetchDataType fetchDataType, LocalDateTime startTime, LocalDateTime endTime);

}
