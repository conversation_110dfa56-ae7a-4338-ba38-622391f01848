package com.daddylab.supplier.item.application.itemOptimize;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.types.itemOptimize.*;

/**
 * <AUTHOR>
 * @since 2023/10/19
 */
public interface ItemOptimizePlanBizService {
    PageResponse<ItemOptimizePlanBaseInfo> query(ItemOptimizePlanQuery query);

    void recount(Long planId);

    Response delete(Long id);

    SingleResponse<ItemOptimizePlanDetail> queryDetail(ItemOptimizePlanDetailQuery query);

    SingleResponse<Long> save(ItemOptimizePlanSaveCmd saveCmd);

    SingleResponse<ItemOptimizePlanItem> match(ItemOptimizePlanItemMatchQuery query);
}
