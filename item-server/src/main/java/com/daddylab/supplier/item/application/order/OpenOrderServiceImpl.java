package com.daddylab.supplier.item.application.order;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.cola.dto.PageResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderDetailService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.order.OpenOrderQuery;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/12/1
 */
@AllArgsConstructor
@Service
public class OpenOrderServiceImpl implements OpenOrderService {

    public static final int PAGE_SIZE_LIMIT = 2500;
    private final IWdtOrderService wdtOrderService;
    private final IWdtOrderDetailService wdtOrderDetailService;

    @Override
    public PageResponse<WdtOrder> orderQuery(OpenOrderQuery orderQuery) {
        if (!orderQuery.pageSizeLe(PAGE_SIZE_LIMIT)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM,
                                                    "分页大小太大了，不能大于" + PAGE_SIZE_LIMIT);
        }
        boolean consignTimeFilter = false;
        final Long consignTimeStart = orderQuery.getConsignTimeStart();
        final Long consignTimeEnd = orderQuery.getConsignTimeEnd();
        if (consignTimeStart != null || consignTimeEnd != null) {
            if (!(consignTimeStart != null && consignTimeEnd != null)) {
                throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM,
                                                        "发货时间筛选必须同时传入开始时间和结束时间");
            }
            consignTimeFilter = true;
        }
        boolean payTimeFilter = false;
        final Long payTimeStart = orderQuery.getPayTimeStart();
        final Long payTimeEnd = orderQuery.getPayTimeEnd();
        if (payTimeStart != null || payTimeEnd != null) {
            if (!(payTimeStart != null && payTimeEnd != null)) {
                throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM,
                                                        "支付时间筛选必须同时传入开始时间和结束时间");
            }
            payTimeFilter = true;
        }
        if (payTimeFilter && consignTimeFilter) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM,
                                                    "不能同时给定支付时间和发货时间范围筛选");
        }
        Set<Long> filteredTradeIdsBySrcTids = Collections.emptySet();
        if (CollectionUtil.isNotEmpty(orderQuery.getSrcOrderNos())) {
            filteredTradeIdsBySrcTids = wdtOrderDetailService.lambdaQuery()
                                                             .in(WdtOrderDetail::getSrcTid, orderQuery.getSrcOrderNos())
                                                             .select(WdtOrderDetail::getTradeId)
                                                             .list()
                                                             .stream()
                                                             .map(WdtOrderDetail::getTradeId)
                                                             .collect(Collectors.toSet());
            if (filteredTradeIdsBySrcTids.isEmpty()) {
                return ResponseFactory.emptyPage();
            }
        }
        final LambdaQueryChainWrapper<WdtOrder> orderQueryWrapper = wdtOrderService.lambdaQuery();
        orderQueryWrapper
                .in(CollectionUtil.isNotEmpty(filteredTradeIdsBySrcTids),
                    WdtOrder::getTradeId,
                    filteredTradeIdsBySrcTids)
                .in(CollectionUtil.isNotEmpty(orderQuery.getTradeStatus()),
                    WdtOrder::getTradeStatus,
                    orderQuery.getTradeStatus())
                .ge(NumberUtil.isPositive(payTimeStart),
                    WdtOrder::getPayTime,
                    DateUtil.toLocalDateTime(payTimeStart))
                .le(NumberUtil.isPositive(payTimeEnd),
                    WdtOrder::getPayTime,
                    DateUtil.toLocalDateTime(payTimeEnd))
                .eq(StringUtil.isNotBlank(orderQuery.getShopNo()), WdtOrder::getShopNo, orderQuery.getShopNo())
                .eq(StringUtil.isNotBlank(orderQuery.getFreezeReason()),
                    WdtOrder::getFreezeReason,
                    orderQuery.getFreezeReason())
                .in(CollectionUtil.isNotEmpty(orderQuery.getTradeType()),
                    WdtOrder::getTradeType,
                    orderQuery.getTradeType())
                .eq(StringUtil.isNotBlank(orderQuery.getTradeLabel()),
                    WdtOrder::getTradeLabel,
                    orderQuery.getTradeLabel())
                .ge(NumberUtil.isPositive(consignTimeStart),
                    WdtOrder::getConsignTime,
                    DateUtil.toLocalDateTime(consignTimeStart))
                .le(NumberUtil.isPositive(consignTimeEnd),
                    WdtOrder::getConsignTime,
                    DateUtil.toLocalDateTime(consignTimeEnd));
        if (consignTimeFilter) {
            orderQueryWrapper.orderByAsc(WdtOrder::getConsignTime);
        } else if (payTimeFilter) {
            orderQueryWrapper.orderByDesc(WdtOrder::getPayTime);
        }
        final IPage<WdtOrder> page = orderQueryWrapper
                .page(orderQuery.getPage());
        final List<WdtOrder> records = page.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            return ResponseFactory.ofPage(page);
        }
        final List<Long> tradeIds = records.stream().map(WdtOrder::getTradeId).distinct()
                                           .collect(Collectors.toList());
        final List<WdtOrderDetail> detailList = wdtOrderDetailService.lambdaQuery()
                                                                     .in(WdtOrderDetail::getTradeId, tradeIds).list();
        final Map<Long, List<WdtOrderDetail>> detailGroup = detailList.stream()
                                                                      .collect(Collectors.groupingBy(WdtOrderDetail::getTradeId));
        records.forEach(record -> record.setOrderDetails(
                detailGroup.getOrDefault(record.getTradeId(), Collections.emptyList())));
        return ResponseFactory.ofPage(page);
    }
}
