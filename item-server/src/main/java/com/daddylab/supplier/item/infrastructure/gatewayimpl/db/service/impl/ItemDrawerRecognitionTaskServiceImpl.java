package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerRecognitionTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerRecognitionTaskMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerRecognitionTaskService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 抽屉图片表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Service
public class ItemDrawerRecognitionTaskServiceImpl extends DaddyServiceImpl<ItemDrawerRecognitionTaskMapper, ItemDrawerRecognitionTask> implements IItemDrawerRecognitionTaskService {

}
