package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesRegister;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AfterSalesRegisterMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesRegisterService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 客服售后登记表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@Service
public class AfterSalesRegisterServiceImpl extends DaddyServiceImpl<AfterSalesRegisterMapper, AfterSalesRegister> implements IAfterSalesRegisterService {

}
