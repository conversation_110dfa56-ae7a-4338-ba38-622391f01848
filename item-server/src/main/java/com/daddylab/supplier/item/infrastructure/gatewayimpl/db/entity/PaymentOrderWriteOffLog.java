package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 付款单冲销流程日志
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PaymentOrderWriteOffLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 采购管理-付款申请单id
     */
    private Long paymentApplyOrderId;

    /**
     * 采购管理-付款申请单no
     */
    private String paymentApplyOrderNo;

    /**
     * 付款明细来源。0：采购单，1：结算单
     */
    private Integer detailSource;

    /**
     * 子单据的id
     */
    private Long subOrderId;

    /**
     * 子单据的编码
     */
    private String subOrderNo;

    /**
     * 轨迹详情
     */
    private String traceMsg;


}
