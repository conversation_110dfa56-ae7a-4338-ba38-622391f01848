package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.domain.brand.dto.BrandDropDownItem;
import com.daddylab.supplier.item.domain.brand.dto.BrandListItem;
import com.daddylab.supplier.item.domain.brand.dto.BrandQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Brand;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 品牌 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Repository
public interface BrandMapper extends DaddyBaseMapper<Brand> {
    /**
     * 查询品牌列表
     */
    List<BrandListItem> queryBrandList(BrandQuery query);

    /**
     * 查询品牌总数
     */
    Integer countOfQueryBrandList(BrandQuery query);

    /** 品牌名称下拉选查询 */
    List<BrandDropDownItem> dropDownList(
            @Param("id") Long id,
            @Param("name") String name,
            @Param("offset") Integer offset,
            @Param("pageSize") Integer pageSize,
            @Param("businessLine") List<Integer> businessLine);
}
