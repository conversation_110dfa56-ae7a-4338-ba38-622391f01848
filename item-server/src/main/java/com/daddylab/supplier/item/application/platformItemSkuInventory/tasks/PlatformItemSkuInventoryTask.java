package com.daddylab.supplier.item.application.platformItemSkuInventory.tasks;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.platformItemSkuInventory.PlatformItemSkuInventoryBizService;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @class PlatformItemSkuInventoryTask.java
 * @description 平台商品同步库存
 * @date 2024-03-13 16:04
 */
@Component
public class PlatformItemSkuInventoryTask {

    @Autowired
    PlatformItemSkuInventoryBizService platformItemSkuInventoryBizService;

    @XxlJob("PlatformItemSkuInventoryTask-fullDoseSyncStock")
    public void fullDoseSyncStock() {
        final String jobParam = XxlJobHelper.getJobParam();
        Platform platform = null;
        if (StrUtil.isNotBlank(jobParam)) {
            platform = Platform.valueOf(jobParam);
        }
        platformItemSkuInventoryBizService.fullDoseSync(platform);
    }

    /**
     * 每一分钟执行一次(对历史数据进行更新)
     *
     * @date 2024/3/14 11:44
     * <AUTHOR>
     */
    @XxlJob("PlatformItemSkuInventoryTask-syncStock")
    public void syncStock() {
        platformItemSkuInventoryBizService.syncStock(DateUtil.currentSeconds());
    }
}
