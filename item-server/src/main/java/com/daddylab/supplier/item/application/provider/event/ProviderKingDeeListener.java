//package com.daddylab.supplier.item.application.provider.event;
//
//import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
//import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeGateway;
//import org.springframework.beans.factory.annotation.Autowired;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @date 2021/11/3 1:36 下午
// * @description
// */
//@EventBusListener
//public class ProviderKingDeeListener {
//
//    @Autowired
//    KingDeeGateway kingDeeGateway;
//
//
//
////    @Subscribe
////    public void listener(ProviderKingDeeEvent event) {
////        final KingDeeConfig kingDeeConfig = SpringUtil.getBean("kingDeeConfig");
////        final KingDeeEvent.CRUD crud = event.getCrud();
////
////        final ProviderApiReq req = event.buildReq();
////
////        if (KingDeeEvent.CRUD.UPDATE.equals(crud)) {
////
////        }
////        if (KingDeeEvent.CRUD.ADD.equals(crud)) {
////            return ProviderApiReq.getAddReq(provider, kingDeeConfig.getCreateOrgId(), kingDeeConfig.getUseOrgId());
////        }
////        if (KingDeeEvent.CRUD.DEL.equals(crud)) {
////            return ProviderApiReq.getDelReq(provider, kingDeeConfig.getCreateOrgId(), kingDeeConfig.getUseOrgId());
////        }
////        throw ExceptionPlusFactory.bizException("供应商事件构造，操作类型非法");
//
//
////    }
//}
