//package com.daddylab.supplier.item.application.item.job;
//
//import cn.hutool.core.collection.CollUtil;
//import com.daddylab.job.core.context.XxlJobHelper;
//import com.daddylab.job.core.handler.annotation.XxlJob;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
//import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
//import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
//import com.daddylab.supplier.item.infrastructure.kingdee.dto.KingDeeSkuResp;
//import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//import org.springframework.util.StopWatch;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Optional;
//
///**
// * <AUTHOR> up
// * @date 2022年07月28日 2:42 PM
// */
//@Slf4j
//@Service
//public class ClearSkuCostPriceJob {
//
//    @Resource
//    ReqTemplate reqTemplate;
//
//    @Resource
//    IItemSkuService iItemSkuService;
//
//    @Resource
//    KingDeeTemplate kingDeeTemplate;
//
//    @XxlJob("clearSkuCostPriceJob")
//    public void run() throws Exception {
//        StopWatch stopWatch = new StopWatch();
//        stopWatch.start();
//
//        long startRow = 0L;
//        long limit = 1000L;
//        List<KingDeeSkuResp> list = reqTemplate.queryZeroStockCostPriceSkuList(startRow, limit);
//        while (CollUtil.isNotEmpty(list)) {
//            list.forEach(kingDeeSkuResp -> {
//                String code = kingDeeSkuResp.getCode();
//                Optional<ItemSku> itemSkuOptional = iItemSkuService.lambdaQuery()
//                        .ne(ItemSku::getKingDeeId, "")
//                        .eq(ItemSku::getSkuCode, code).select().oneOpt();
//                itemSkuOptional.ifPresent(val -> {
//                    kingDeeTemplate.handler(ApiEnum.UPDATE_SKU, val.getId(), val.getKingDeeId());
//                });
//            });
//
//            startRow = startRow + 1;
//            startRow = startRow * limit;
//            list = reqTemplate.queryZeroStockCostPriceSkuList(startRow, limit);
//        }
//
//        stopWatch.stop();
//        XxlJobHelper.log("----- 将物料采购成本数据迁移到库存采购成本字段结束。耗时:{}秒 ------ ", stopWatch.getTotalTimeSeconds());
//    }
//
//}
