package com.daddylab.supplier.item.controller.afterSalesRegister;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSalesRegister.AfterSalesRegisterBizService;
import com.daddylab.supplier.item.application.afterSalesRegister.AfterSalesRegisterPageQuery;
import com.daddylab.supplier.item.application.afterSalesRegister.AfterSalesRegisterPageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/8/21
 */
@Slf4j
@RestController
@RequestMapping("/afterSalesRegister")
@Api(value = "售后登记相关API", tags = "售后登记相关API")
public class AfterSalesRegisterController {

    @Resource
    AfterSalesRegisterBizService afterSalesRegisterBizService;


    @ResponseBody
    @ApiOperation(value = "售后登记分页查询")
    @PostMapping("/pageQuery")
    public PageResponse<AfterSalesRegisterPageVO> pageQuery(
            @RequestBody @Validated AfterSalesRegisterPageQuery query) {
//        query.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(query.getBusinessLine()));
        return afterSalesRegisterBizService.pageQuery(query);
    }

    @ResponseBody
    @ApiOperation(value = "售后登记导出")
    @PostMapping("/export")
    public SingleResponse<Boolean> export(
            @RequestBody @Validated AfterSalesRegisterPageQuery query) {
        return afterSalesRegisterBizService.export(query);
    }

    @ResponseBody
    @ApiOperation(value = "店铺下拉")
    @GetMapping("/shopDropdown")
    public MultiResponse<String> shopDropdown(
            @RequestParam(required = false, value = "店铺名称模糊搜索") String name) {
        return afterSalesRegisterBizService.shopDropdown(name);
    }



}
