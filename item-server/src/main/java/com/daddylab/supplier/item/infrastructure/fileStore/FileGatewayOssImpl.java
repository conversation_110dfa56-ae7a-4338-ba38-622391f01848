package com.daddylab.supplier.item.infrastructure.fileStore;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.URLUtil;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.model.ObjectMetadata;
import com.daddylab.supplier.item.domain.fileStore.constant.FileType;
import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.ImageStub;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.fileStore.vo.VideoStub;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.File;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IFileService;
import com.daddylab.supplier.item.infrastructure.oss.OssGateway;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/10/12
 */
@Component
@Qualifier(StorageService.OSS)
public class FileGatewayOssImpl extends FileGatewayBaseImpl {
    @Autowired OssGateway ossGateway;

    @Autowired
    IFileService iFileService;

    @Override
    protected ImageStub uploadImage0(UploadFileAction action, File file) {
        final String url =
                ossGateway.put(
                        action.isPrivateFile(), action.getDestPath(), action.getInputStream());
        final String path = URLUtil.getPath(url);
        final Map<String, String> imageInfo = ossGateway.imageInfo(action.isPrivateFile(), path);
        file.setType(FileType.IMAGE);
        file.setUrl(url);
        file.setFullName(action.getFileName());
        file.setMd5(action.getMd5());
        file.setAliOss(true);
        file.setPrivate(action.isPrivateFile());
        file.setMime(FileUtil.getMimeType(imageInfo.get("Format")));
        file.setWidth(Integer.parseInt(imageInfo.get("ImageWidth")));
        file.setHeight(Integer.parseInt(imageInfo.get("ImageHeight")));
        file.setMeta(JSON.toJSONString(imageInfo));
        return FileTypeMapper.INST.imageStubFromFile(file);
    }

    @Override
    protected FileStub uploadFile0(UploadFileAction action, File file) {
        final String url =
                ossGateway.put(
                        action.isPrivateFile(), action.getDestPath(), action.getInputStream());
        final String path = URLUtil.getPath(url);
        final ObjectMetadata metadata = ossGateway.metadata(action.isPrivateFile(), path);
        file.setType(FileType.UNKNOWN);
        file.setUrl(url);
        file.setFullName(action.getFileName());
        file.setMd5(action.getMd5());
        file.setAliOss(true);
        file.setPrivate(action.isPrivateFile());
        file.setMime(metadata.getContentType());
        file.setMeta(JSON.toJSONString(metadata.getRawMetadata()));
        return FileTypeMapper.INST.fileStubFromFile(file);
    }

    @Override
    protected VideoStub uploadVideo0(UploadFileAction action, File file) {
        final String url =
                ossGateway.put(
                        action.isPrivateFile(), action.getDestPath(), action.getInputStream());
        final String path = URLUtil.getPath(url);
        final ObjectMetadata metadata = ossGateway.metadata(action.isPrivateFile(), path);
        file.setType(FileType.VIDEO);
        file.setUrl(url);
        file.setFullName(action.getFileName());
        file.setMd5(action.getMd5());
        file.setAliOss(true);
        file.setPrivate(action.isPrivateFile());
        file.setMime(metadata.getContentType());
        file.setMeta(JSON.toJSONString(metadata.getRawMetadata()));
        return FileTypeMapper.INST.videoStubFromFile(file);
    }

    @Override
    public String getAuthorizedUrl(String url) {
        if (url.startsWith("http")) {
            return ossGateway.generatePresignedUrl(true, URLUtil.getPath(url));
        } else {
            return ossGateway.generatePresignedUrl(true, url);
        }
    }
}
