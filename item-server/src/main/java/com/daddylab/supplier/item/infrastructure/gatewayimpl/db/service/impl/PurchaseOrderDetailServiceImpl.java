package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseOrderDetailService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 采购订单明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Service
public class PurchaseOrderDetailServiceImpl extends DaddyServiceImpl<PurchaseOrderDetailMapper, PurchaseOrderDetail> implements IPurchaseOrderDetailService {

    @Autowired
    PurchaseOrderDetailMapper purchaseOrderDetailMapper;

    @Override
    public List<PurchaseOrderDetail> getDetailList(Long purchaseOrderId) {
        LambdaQueryWrapper<PurchaseOrderDetail> objectLambdaQueryWrapper = Wrappers.lambdaQuery();
        objectLambdaQueryWrapper.eq(PurchaseOrderDetail::getPurchaseOrderId,purchaseOrderId);
        return this.list(objectLambdaQueryWrapper);
    }


    @Override
    public List<String> getSkuCodeList(Long purchaseOrderId) {
        LambdaQueryWrapper<PurchaseOrderDetail> objectLambdaQueryWrapper = Wrappers.lambdaQuery();
        objectLambdaQueryWrapper.eq(PurchaseOrderDetail::getPurchaseOrderId,purchaseOrderId);
        List<PurchaseOrderDetail> list = this.list(objectLambdaQueryWrapper);
        if(CollectionUtils.isEmpty(list)){
            return new LinkedList<>();
        }
        return list.stream().map(PurchaseOrderDetail::getItemSkuCode).collect(Collectors.toList());
    }

    @Override
    public Boolean haveDetailList(Long purchaseOrderId) {
        LambdaQueryWrapper<PurchaseOrderDetail> objectLambdaQueryWrapper = Wrappers.lambdaQuery();
        objectLambdaQueryWrapper.eq(PurchaseOrderDetail::getPurchaseOrderId,purchaseOrderId);
        return this.count(objectLambdaQueryWrapper) > 0;
    }
}
