package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/24 14:14
 * @Description: 盘货表活动状态
 */
@Getter
@AllArgsConstructor
public enum HandingSheetStateEnum implements IEnum<Integer> {
    /**
     * 待提交
     */
    TO_BE_SUBMIT(1, "待提交"),
    /**
     * 待审核
     */
    TO_BE_AUDITED(2, "待审核"),
    /**
     * 已过审
     */
    AUDITED(3, "已过审"),
    /**
     * 进行中
     */
    PROCESSING(4, "进行中"),
    /**
     * 已过期
     */
    EXPIRED(5, "已过期")
    ;

    private Integer value;
    private String desc;

    public static HandingSheetStateEnum of(Integer value) {
        for (HandingSheetStateEnum stateEnum : HandingSheetStateEnum.values()) {
            if (stateEnum.getValue().equals(value)) {
                return stateEnum;
            }
        }
        return null;
    }
}
