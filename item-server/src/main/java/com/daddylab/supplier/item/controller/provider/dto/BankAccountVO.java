package com.daddylab.supplier.item.controller.provider.dto;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/4/19 10:25
 * @description bankAccountVO
 */
@Data
public class BankAccountVO implements Serializable {

    private static final Long serialVersionUID = 1L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("银行账号")
    @NotBlank(message = "银行账号不得为空")
    private String bankCard;

    @ApiModelProperty("开户行")
    @NotBlank(message = "开户行不得为空")
    private String bankDeposit;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("开户行行号")
    @NotBlank(message = "开户行行号")
    private String bankNo;

}
