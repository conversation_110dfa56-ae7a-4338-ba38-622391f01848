package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayableApplyOrderStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 采购付款申请单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PurchasePayableApplyOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 创建者（关联入库单/出库单/其他应付的创建人）
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 编码
     */
    private String no;

    /**
     * 申请日期。
     */
    private Long applyDate;

//    /**
//     * 关联到的原应付单编号
//     */
//    private String relatedPayableOrderNos;

    /**
     * 供应商id
     */
    private Long providerId;

    /**
     * 供应商名称
     */
    private String providerName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态。1。保存（待审核)
     */
    private PayableApplyOrderStatus status;

    /**
     * 原本累计金额
     */
    private BigDecimal sourcePayTotalAmount;

    /**
     * 修正后的金额
     */
    private BigDecimal fixedTotalAmount;


    private Long additionalId;



}
