package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Category;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CategoryMapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 后台类目 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface ICategoryService extends IDaddylabServicePlus<Category, CategoryMapper> {


    Boolean isSyncKingDee(Long categoryId);

    /**
     * 获取商品类目名称集合（有序：一级/二级/...）
     *
     * @param itemNo 商品编码
     * @return  List<String>
     */
    List<String> getCategoryNames(String itemNo);

    Map<Long,Category> batchQueryById(Collection<Long> categoryIds);
}
