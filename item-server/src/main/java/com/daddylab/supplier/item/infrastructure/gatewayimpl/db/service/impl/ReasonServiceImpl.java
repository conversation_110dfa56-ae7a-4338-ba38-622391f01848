package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Reason;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ReasonMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IReasonService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 原因管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@Service
public class ReasonServiceImpl extends DaddyServiceImpl<ReasonMapper, Reason> implements IReasonService {
}
