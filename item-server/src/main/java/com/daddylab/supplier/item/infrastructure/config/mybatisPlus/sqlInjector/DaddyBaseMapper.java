package com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/25 2:36 下午
 * @description
 */
public interface DaddyBaseMapper<T> extends MPJBaseMapper<T> {

    int deleteByIdWithTime(Serializable id);

    int deleteByMapWithTime(@Param("cm") Map<String, Object> columnMap);

    int deleteWithTime(@Param("ew") Wrapper<T> wrapper);

    int deleteBatchIdsWithTime(@Param("coll") Collection<? extends Serializable> idList);


}
