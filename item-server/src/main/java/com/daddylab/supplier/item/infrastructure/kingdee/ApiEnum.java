package com.daddylab.supplier.item.infrastructure.kingdee;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> up
 * @date 2022/4/2 9:59 上午
 */
@Getter
@AllArgsConstructor
public enum ApiEnum {

    /**
     * 保存物料分组，自动提交审核
     * 编辑直接修改，无需反审核
     */
    GROUP_SAVE_CATEGORY("物料分组同步", "BD_MATERIAL"),
    GROUP_DELETE_CATEGORY("删除物料分组", "BD_MATERIAL"),

    // ------------------ 保存，自动提交审核 --------------------
    SAVE_SHOP("店铺同步", "BD_Customer"),
    SAVE_PROVIDER("供应商同步", "BD_Supplier"),
    SAVE_SKU("物料明细同步", "BD_MATERIAL"),
    SAVE_STOCK_IN_ORDER("采购入库单同步", "STK_InStock"),
    SAVE_STOCK_OUT_ORDER("退料申请单同步", "PUR_MRB"),

    // ------------------ 更新也需要先反审核。参数的生成和add做了兼容。 ----------------------
    UPDATE_SHOP("店铺编辑", "BD_Customer"),
    UPDATE_PROVIDER("供应商编辑", "BD_Supplier"),
    UPDATE_SKU("物料明细编辑", "BD_MATERIAL"),
    UPDATE_STOCK_IN_ORDER("采购入库单编辑", "STK_InStock"),
    UPDATE_STOCK_OUT_ORDER("退料申请单编辑", "PUR_MRB"),

    // ------------------ 同步删除需要，先进行反审核 --------------------
    DELETE_PROVIDER("删除供应商", "BD_Supplier"),
    DELETE_STOCK_IN_ORDER("删除采购入库单", "STK_InStock"),
    DELETE_STOCK_OUT_ORDER("删除退料申请单", "PUR_MRB"),

    SAL_OUTSTOCK("供应链-销售管理-销售出库单", "SAL_OUTSTOCK"),

    SAL_RETURNSTOCK("供应链-销售管理-销售退货单", "SAL_RETURNSTOCK"),


    QUERY_PRICE_CATEGORY("供应链-采购管理-采购价目表查询", "PUR_PriceCategory"),
    SAVE_PRICE_CATEGORY("供应链-采购管理-采购价目表保存", "PUR_PriceCategory"),

    SAVE_PAY_BILL("财务会计-出纳管理-付款单", "AP_PAYBILL");


    private final String desc;
    private final String formId;


}
