package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年04月22日 11:55 AM
 */
@Data
public class PurchaseOrderProcessForm {

    private Long buyerId;
    private String contractNo;
    private Integer deliveryMethod;
    private String dept;
    private Long loginUserId;
    private String no;
    private Integer payMethod;
    private String processInstId;
    private String providerName;
    private Long purchaseDate;
    private String purchaseOrg;
    private String remark;
    private Integer type;

    private List<PurchaseOrderDetailFormList> purchaseOrderDetailFormList;
}
