package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopOperatorMap;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ShopOperatorMapMapper;
import java.util.List;

/**
 * <p>
 * 下架管理-店铺/运营映射关联 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-01
 */
public interface IShopOperatorMapService extends IDaddylabServicePlus<ShopOperatorMap, ShopOperatorMapMapper> {

    List<ShopOperatorMap> listByShopIds(List<Long> selectShopIds);
}
