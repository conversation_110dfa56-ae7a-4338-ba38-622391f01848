package com.daddylab.supplier.item.application.afterSaleLogistics;

import com.daddylab.supplier.item.application.afterSaleLogistics.dto.LogisticExceptionRes;
import com.daddylab.supplier.item.application.afterSaleLogistics.monitor.OrderLogisticsExceptionMonitor;
import com.daddylab.supplier.item.application.afterSaleLogistics.monitor.ProcessContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsTrace;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> up
 * @date 2024年05月22日 3:50 PM
 */
@Service
@Slf4j
public class LogisticsExceptionBizService {
  @Resource private ApplicationContext applicationContext;

  @Autowired List<OrderLogisticsExceptionMonitor> monitorList;

  LogisticExceptionRes exceptionHandler(ProcessContext context) {
    LogisticExceptionRes processRes = new LogisticExceptionRes();
    processRes.setRootException(LogisticsRootException.NORMAL);
    for (OrderLogisticsExceptionMonitor monitor : this.monitorList) {
      processRes = monitor.process(context);
      if (!LogisticsRootException.NORMAL.equals(processRes.getRootException())) {
        log.debug("[物流异常]发现异常件:{} 异常:{}", context, processRes);
        return processRes;
      }
    }
    return processRes;
  }

  public LogisticExceptionRes exceptionHandler(Long turnTimestamp, OrderLogisticsTrace trace) {
    return exceptionHandler(new ProcessContext(turnTimestamp, trace));
  }
}
