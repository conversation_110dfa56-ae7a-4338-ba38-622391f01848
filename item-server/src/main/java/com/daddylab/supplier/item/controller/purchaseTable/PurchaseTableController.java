package com.daddylab.supplier.item.controller.purchaseTable;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.purchase.purchaseTable.PurchaseTableBizService;
import com.daddylab.supplier.item.controller.purchaseTable.dto.PurchaseTableCmd;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @ClassName PurchaseTableController.java
 * @description 采购权限
 * @createTime 2021年11月16日 21:12:00
 */
@Slf4j
@Api(value = "采购相关API", tags = "采购相关API")
@RestController
@RequestMapping("/purchaseTable")
public class PurchaseTableController {

    @Autowired
    private PurchaseTableBizService purchaseTableBizService;

    @ResponseBody
    @ApiOperation(value = "根据月份查当前权限")
    @GetMapping("/selectByMonth")
    public SingleResponse<Integer> selectByMonth(String month) {
        return purchaseTableBizService.selectStatusByMonth(month);
    }


    @ResponseBody
    @ApiOperation(value = "查询有数据的月份数据")
    @GetMapping("/getMonth")
    public MultiResponse<String> getMonth() {
        return purchaseTableBizService.getMonth();
    }

    @ResponseBody
    @ApiOperation(value = "修改采购权限")
    @PostMapping("/update")
    public Response update(@RequestBody @Validated PurchaseTableCmd cmd) {
        return purchaseTableBizService.update(cmd);
    }


}
