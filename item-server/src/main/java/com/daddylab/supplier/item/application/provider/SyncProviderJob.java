package com.daddylab.supplier.item.application.provider;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IProviderService;
import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.kingdee.dto.KingDeeProviderResp;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> up
 * @date 2022/5/17 8:43 下午
 */
@Component
@Slf4j
public class SyncProviderJob {

    @Autowired
    IProviderService iProviderService;

    @Autowired
    KingDeeTemplate kingDeeTemplate;

    @Autowired
    ReqTemplate reqTemplate;

    private String generateProvider(Provider provider) {
        String firstLetter = PinyinUtil.getFirstLetter(provider.getName(), "").toUpperCase();
        return "G" + firstLetter + provider.getUnifySocialCreditCodes() + "-" + RandomUtil.randomNumbers(4);
    }

    /**
     * 间隔5分钟执行。
     */
    @XxlJob("syncProviderJob")
    public void syncProvider() {


        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            int size = 100;
            int index = 0;

            Page<Provider> page = new Page<>(index, size);

            LambdaQueryWrapper<Provider> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Provider::getKingDeeId, "").eq(Provider::getIsDel, 0);

            PageInfo<Provider> pageInfo = PageHelper.startPage(index, size).doSelectPageInfo(() -> iProviderService.list(wrapper));

            int pageNum = pageInfo.getPages();

            while (pageNum > 0) {
                List<Long> errorIdList = new LinkedList<>();

                for (Provider record : pageInfo.getList()) {
                    try {
                        Optional<KingDeeProviderResp> optional = reqTemplate.queryProvider(record.getName());
                        optional.ifPresent(var -> {
                            if (StringUtil.isNotBlank(var.getKingDeeId()) && StringUtil.isNotBlank(var.getKingDeeNo())) {
                                // FIXME 是否需要更新编号
                                iProviderService.setKingDee(var.getKingDeeNo(), var.getKingDeeId(), record.getId());
                            }
                        });
                        // 金蝶中此供应商不存在。
                        optional.orElseGet(() -> {
                            kingDeeTemplate.handler(ApiEnum.SAVE_PROVIDER, record.getId(), "");
                            return null;
                        });
                    } catch (Exception e) {
                        log.error("供应商同步到KingDee异常,id:{}", record.getId(), e);
                        errorIdList.add(record.getId());
                    }
                }

                if (CollectionUtils.isNotEmpty(errorIdList)) {
                    Alert.text(MessageRobotCode.GLOBAL, "provider同步KingDee异常,id:" + StrUtil.join(",", errorIdList));
                }

                pageNum = pageNum - 1;

                index = index + 1;
                pageInfo = PageHelper.startPage(index, size).doSelectPageInfo(() -> iProviderService.list(wrapper));
            }
        });


    }

    public static void main(String[] args) {
        boolean isMatch = ReUtil.isMatch("^[\\u4E00-\\u9FA5A-Za-z0-9]+$", "测试供应商5.3_H4_Mw_kA");
        System.out.println(isMatch);

    }
}
