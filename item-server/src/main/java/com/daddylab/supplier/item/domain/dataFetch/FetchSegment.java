package com.daddylab.supplier.item.domain.dataFetch;

import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2022/4/28
 */
@ToString
@Data
@EqualsAndHashCode(of = {"dataType", "startTime", "endTime"})
public class FetchSegment {

    /**
     * 主键
     */
    private Long id;
    /**
     * 数据类型 1:旺店平台商品数据同步 2:系统平台商品 3:...
     */
    private FetchDataType dataType;
    /**
     * 拉取段的开始时间
     */
    private LocalDateTime startTime;
    /**
     * 拉取段的结束时间
     */
    private LocalDateTime endTime;
    /**
     * 拉取状态
     */
    private FetchStatus status;
    /**
     * 当前同步任务开始执行时添加独占锁，锁定当前记录直到超时，超时后锁定失效可以被重试
     */
    private LocalDateTime lockTime;
    /**
     * 异常原因
     */
    private String err;
    /**
     * 拓展字段，额外数据（执行过程中认为有必要记录的运行信息）
     */
    private RunData data;

    public void setId(Long id) {
        this.id = id;
    }

    public RunData getData() {
        if (data == null) {
            data = new RunData();
        }
        return data;
    }

    public String getDataJson() {
        return JsonUtil.toJson(getData());
    }

    public void setData(String data) {
        this.data = JsonUtil.parse(data, RunData.class);
    }

}
