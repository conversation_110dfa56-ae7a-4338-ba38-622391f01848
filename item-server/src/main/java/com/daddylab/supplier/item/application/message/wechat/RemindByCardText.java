//package com.daddylab.supplier.item.application.message.wechat;
//
//import java.lang.annotation.*;
//
///**
// * 企业微信(卡片消息)。暂时够用，类型暂时不拓展了。
// * 推送提醒
// *
// * <AUTHOR> up
// */
//@Target({ElementType.TYPE, ElementType.METHOD})
//@Retention(RetentionPolicy.RUNTIME)
//@Documented
//public @interface RemindByCardText {
//
//    /**
//     * 消息接收者的企业微信ID
//     *
//     * @return 多个qwUserId, 英文逗号隔开。
//     */
//    String[] recipient();
//
//    /**
//     * @return 卡片消息标题
//     */
//    String title();
//
//    /**
//     * @return 卡片消息内容，注意内容255个字节，多余的内容会直接截断
//     */
//    String content();
//
//    /**
//     * @return 附属可点击的链接
//     */
//    String linke() default "";
//
//
//
//}
