package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSaleForwardingRegister;

/**
 * <p>
 * 售后转寄登记 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
public interface IAfterSaleForwardingRegisterService extends IDaddyService<AfterSaleForwardingRegister> {

}
