package com.daddylab.supplier.item.application.platformItem.data;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Value;

/**
 * <AUTHOR>
 * @since 2024/3/22
 */
@Value()
public class PlatformItemOpLogId {
    Platform platform;
    String outerItemId;

    @JsonCreator
    public static PlatformItemOpLogId of(@JsonProperty("platform") Platform platform, @JsonProperty("outerItemId") String outerItemId) {
        return new PlatformItemOpLogId(platform, outerItemId);
    }

    public String toString() {
        return platform.name() + ":" + outerItemId;
    }
}
