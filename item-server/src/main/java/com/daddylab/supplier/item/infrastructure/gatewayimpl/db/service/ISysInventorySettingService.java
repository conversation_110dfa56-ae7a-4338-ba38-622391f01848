package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.SysInventorySetting;

/**
 * <p>
 * 系统库存同步配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface ISysInventorySettingService extends IDaddyService<SysInventorySetting> {

    SysInventorySetting getSysInventorySetting();

}
