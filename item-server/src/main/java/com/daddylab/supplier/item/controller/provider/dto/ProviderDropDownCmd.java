package com.daddylab.supplier.item.controller.provider.dto;

import com.alibaba.cola.dto.Command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11 10:25 上午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("供应商下拉请求参数")
public class ProviderDropDownCmd extends Command {
    private static final long serialVersionUID = 1675371508536998150L;

    @ApiModelProperty("非必填。供应商Id")
    private Long id;

    @ApiModelProperty("非必填。供应商名字模糊查询")
    private String name;

    private Integer pageIndex = 1;

    private Integer pageSize = 10;

    @ApiModelProperty("合作模式（业务线）筛选（多选）")
    private List<Integer> businessLine;
}
