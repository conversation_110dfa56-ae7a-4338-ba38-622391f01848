package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 无需转寄类型 0:全部 1:自有仓库 2:影响销售 3:其他
 *
 * <AUTHOR>
 * @since 2025/4/14
 */
@Getter
@AllArgsConstructor
public enum AfterSaleForwardingNeedNotForwardTypeEnum implements IIntegerEnum {
  ALL(0, "全部"),
  OWN_WAREHOUSE(1, "自有仓库"),
  AFFECT_SALES(2, "影响销售"),
  OTHER(3, "其他"),
  LARGE_CARGO(4, "大件"),
  AMOUNT_LT5(5, "金额小于5元"),
  ;

  private final Integer value;
  private final String desc;
}
