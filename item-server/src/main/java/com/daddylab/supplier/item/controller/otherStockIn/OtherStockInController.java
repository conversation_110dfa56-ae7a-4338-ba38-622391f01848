package com.daddylab.supplier.item.controller.otherStockIn;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.otherStockIn.OtherStockInBizService;
import com.daddylab.supplier.item.application.warehouse.WarehouseBizService;
import com.daddylab.supplier.item.common.OtherStockInOutConfig;
import com.daddylab.supplier.item.controller.otherStockIn.dto.OtherStockInQueryPage;
import com.daddylab.supplier.item.controller.otherStockIn.dto.OtherStockInVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @ClassName otherStockInController.java
 * @description 其他入库单
 * @createTime 2022年03月30日 15:44:00
 */
@Slf4j
@Api(value = "其他入库单API", tags = "其他入库单API")
@RestController
@RequestMapping("/otherStockIn")
public class OtherStockInController {

    @Autowired
    private OtherStockInBizService otherStockInBizService;
    @Autowired
    private WarehouseBizService warehouseBizService;
    @Autowired
    private OtherStockInOutConfig otherStockInOutConfig;

    @ResponseBody
    @ApiOperation(value = "其他入库单分页")
    @PostMapping("/viewList")
    public PageResponse<OtherStockInVo> viewList(@RequestBody OtherStockInQueryPage queryPage) {
        return otherStockInBizService.queryPage(queryPage);
    }

    @ResponseBody
    @ApiOperation(value = "根据id查询其他入库单详情信息")
    @GetMapping("/getById")
    public SingleResponse<OtherStockInVo> getById(@ApiParam("其他入库单id") Long id) {
        return otherStockInBizService.getById(id);
    }

    @ResponseBody
    @ApiOperation(value = "出入库仓库列表")
    @GetMapping("/getWarehouse")
    public MultiResponse<Warehouse> getById() {
        List<String> warehouseNos = otherStockInOutConfig.getWarehouseNos();
        ArrayList<Warehouse> list = Lists.newArrayList();
        warehouseNos.forEach( warehouseNo ->{
            Optional<Warehouse> warehouse = warehouseBizService.getWarehouse(warehouseNo);
            warehouse.ifPresent(list::add);
        });
        return MultiResponse.of(list);
    }


}
