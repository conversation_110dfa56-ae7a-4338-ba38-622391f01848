package com.daddylab.supplier.item.application.afterSaleLogistics.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/23
 */
@Data
public class AfterSaleLogisticsCloseCmd {
  @ApiModelProperty(value = "异常id")
  private Long id;

  @ApiModelProperty(value = "异常ids")
  private List<Long> ids;

  @ApiModelProperty(value = "关闭原因")
  private String reason;

  @ApiModelProperty(value = "备注")
  private String remark;
}
