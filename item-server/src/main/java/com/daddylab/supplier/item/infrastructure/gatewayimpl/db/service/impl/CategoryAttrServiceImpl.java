package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CategoryAttr;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CategoryAttrMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICategoryAttrService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 类目属性 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class CategoryAttrServiceImpl extends DaddyServiceImpl<CategoryAttrMapper, CategoryAttr> implements ICategoryAttrService {

}
