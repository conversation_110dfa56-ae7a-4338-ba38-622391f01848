package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2021/12/30
 */
@Getter
@AllArgsConstructor
public enum PlatformItemType implements IEnum<Integer> {
    /**
     * 自营
     */
    PROPRIETARY(1, "自营"),
    /**
     * 代销
     */
    PROXY(2, "代销");
    final public Integer value;
    final public String desc;
}
