package com.daddylab.supplier.item.application.refundOrder;

import cn.hutool.core.collection.CollectionUtil;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.wms.GoodsSNAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.dto.GoodsSNQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.dto.GoodsSNQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.dto.GoodsSNQueryWithDetailResponse.Sn;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtGoodsSn;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtGoodsSnMapper;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/2/13
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class WdtGoodsSnFetcher implements PageFetcher {

    private final WdtGateway wdtGateway;
    private final WdtGoodsSnMapper wdtGoodsSnMapper;

    @Override
    public int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
        return query(startTime, endTime, 1, 1, true).getTotalCount();
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, long pageIndex, long pageSize,
            RunContext runContext) {
        final GoodsSNQueryWithDetailResponse response = query(startTime, endTime,
                (int) pageSize,
                (int) pageIndex, false);
        handleResponse(response);
    }

    private GoodsSNQueryWithDetailResponse query(LocalDateTime startTime,
            LocalDateTime endTime, int pageSize, int pageNo, boolean calcTotal) {
        try {
            final GoodsSNAPI api = wdtGateway.getAPI(GoodsSNAPI.class);
            final Pager pager = new Pager(pageSize, pageNo - 1, calcTotal);

            final GoodsSNQueryWithDetailParams params = new GoodsSNQueryWithDetailParams();
            params.setStartTime(DateUtil.format(startTime));
            params.setEndTime(DateUtil.format(endTime));
            return api.queryWithDetail(params, pager);
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapFetchException(e, fetchDataType());

        }
    }

    private void handleResponse(GoodsSNQueryWithDetailResponse response) {
        final List<Sn> snList = response.getSnList();
        if (CollectionUtil.isEmpty(snList)) {
            return;
        }
        wdtGoodsSnMapper.saveGoodsSnBatch(Assembler.INST.goodsSnDtoToPoBatch(snList));
    }

    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.WDT_GOODS_SN;
    }

    @Mapper()
    public interface Assembler {

        Assembler INST = Mappers.getMapper(Assembler.class);

        default LocalDateTime toLocalDateTime(String time) {
            return DateUtil.parseCompatibility(time);
        }

        WdtGoodsSn goodsSnDtoToPo(GoodsSNQueryWithDetailResponse.Sn item);

        List<WdtGoodsSn> goodsSnDtoToPoBatch(Collection<GoodsSNQueryWithDetailResponse.Sn> items);
    }

}
