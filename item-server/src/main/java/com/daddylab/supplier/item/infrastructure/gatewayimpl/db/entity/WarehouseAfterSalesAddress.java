package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.Id;
import org.javers.core.metamodel.annotation.PropertyName;

import java.io.Serializable;

/**
 * <p>
 * 仓库售后退回地址
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WarehouseAfterSalesAddress implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Id
    private Long id;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    @DiffIgnore
    private Long deletedAt;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @DiffIgnore
    private Long createdAt;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    @DiffIgnore
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    @DiffIgnore
    private Long updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    @DiffIgnore
    private Long updatedUid;

    /**
     * 是否删除
     */
    @TableLogic
    @DiffIgnore
    private Integer isDel;

    /**
     * 供应商编号
     */
    @DiffIgnore
    private String providerNo;

    /**
     * 仓库编号
     */
    @DiffIgnore
    private String warehouseNo;

    /**
     * 退回地址
     */
    @PropertyName("售后地址")
    private String fullAddress;

    /**
     * 退货联系人
     */
    @PropertyName("售后联系人")
    private String contacts;

    /**
     * 退货联系电话
     */
    @PropertyName("联系电话")
    private String tel;

    @DiffIgnore
    private Long partnerProviderId;

    @DiffIgnore
    private String warehouseName;

    @PropertyName("备注")
    private String remark;

    @DiffIgnore
    private String provinceCode;

    /**
     * 联系地址 市
     */
    @DiffIgnore
    private String cityCode;

    /**
     * 联系地址 区/县
     */
    @DiffIgnore
    private String areaCode;



}
