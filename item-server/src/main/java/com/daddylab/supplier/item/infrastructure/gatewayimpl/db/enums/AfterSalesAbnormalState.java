package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 售后异常件状态
 *
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum AfterSalesAbnormalState implements IEnum<Integer> {
    /**
     * 不要下划线
     */
    WAIT_HANDLE(1, "待处理"),
    WAIT_SEND_NO(2, "待转寄"),
    HAD_SEND_NO(3, "已转寄"),
    HAD_HANDLE(4,"已处理");

    @EnumValue
    private final Integer value;
    private final String desc;

}
