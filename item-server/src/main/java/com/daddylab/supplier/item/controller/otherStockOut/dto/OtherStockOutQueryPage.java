package com.daddylab.supplier.item.controller.otherStockOut.dto;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OtherStockOutQueryPage.java
 * @description
 * @createTime 2022年04月01日 16:26:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("其他出库单分页查询请求实体")
public class OtherStockOutQueryPage extends PageQuery {
    private static final long serialVersionUID = 1931835755019692237L;

    @ApiModelProperty(value = "其他出库单Id")
    private Long id;

    @ApiModelProperty(value = "出库单号")
    private String orderNo;

    @ApiModelProperty(value = "仓库 1:全部 2:袋鼠仓（海销） 3:金诺仓")
    private Integer warehouse;

    @ApiModelProperty(value = "仓库编号")
    private String warehouseNo;

    @ApiModelProperty(value = "状态 0:全部 1:待处理 2:已取消 3:未确认 4:待审核 5:拣货中 6:已完成")
    private Integer status;

    @ApiModelProperty(value = "其他出库原因 0:全部 1:无 2:退货少件出库 3:退件破损出库 4:工厂虚拟出库 5:线下下单 6:客服需求 7:调拨出库 8:临期出库 9:破损出库")
    private Integer otherReason;

    @ApiModelProperty(value = "商品id")
    private Long itemId;

    @ApiModelProperty(value = "商品SKU")
    private Long itemSku;

    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "品牌id")
    private Long brandId;

    @ApiModelProperty(value = "开始时间")
    private Long startTime;

    @ApiModelProperty(value = "结束时间")
    private Long endTime;

    private List<Integer> businessLine;
}
