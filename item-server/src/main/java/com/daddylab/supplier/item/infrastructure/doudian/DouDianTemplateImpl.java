package com.daddylab.supplier.item.infrastructure.doudian;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.application.drawer.ItemDrawerService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerImageTypeEnum;
import com.daddylab.supplier.item.infrastructure.doudian.sdk.product_listV2.ProductListV2ParamPatch;
import com.daddylab.supplier.item.infrastructure.doudian.sdk.product_listV2.ProductListV2RequestPatch;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuAttrRefDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemSkuAttrRefMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemResp;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.doudian.open.api.material_batchUploadImageSync.MaterialBatchUploadImageSyncRequest;
import com.doudian.open.api.material_batchUploadImageSync.MaterialBatchUploadImageSyncResponse;
import com.doudian.open.api.material_batchUploadImageSync.data.SuccessMapItem;
import com.doudian.open.api.material_batchUploadImageSync.param.MaterialBatchUploadImageSyncParam;
import com.doudian.open.api.material_batchUploadImageSync.param.MaterialsItem;
import com.doudian.open.api.material_batchUploadVideoAsync.MaterialBatchUploadVideoAsyncRequest;
import com.doudian.open.api.material_batchUploadVideoAsync.MaterialBatchUploadVideoAsyncResponse;
import com.doudian.open.api.material_batchUploadVideoAsync.param.MaterialBatchUploadVideoAsyncParam;
import com.doudian.open.api.material_queryMaterialDetail.MaterialQueryMaterialDetailRequest;
import com.doudian.open.api.material_queryMaterialDetail.MaterialQueryMaterialDetailResponse;
import com.doudian.open.api.material_queryMaterialDetail.data.VideoInfo;
import com.doudian.open.api.material_queryMaterialDetail.param.MaterialQueryMaterialDetailParam;
import com.doudian.open.api.material_uploadImageSync.MaterialUploadImageSyncRequest;
import com.doudian.open.api.material_uploadImageSync.MaterialUploadImageSyncResponse;
import com.doudian.open.api.material_uploadImageSync.param.MaterialUploadImageSyncParam;
import com.doudian.open.api.material_uploadVideoAsync.MaterialUploadVideoAsyncRequest;
import com.doudian.open.api.material_uploadVideoAsync.MaterialUploadVideoAsyncResponse;
import com.doudian.open.api.material_uploadVideoAsync.param.MaterialUploadVideoAsyncParam;
import com.doudian.open.api.product_detail.ProductDetailRequest;
import com.doudian.open.api.product_detail.ProductDetailResponse;
import com.doudian.open.api.product_detail.data.CategoryDetail;
import com.doudian.open.api.product_detail.data.ProductDetailData;
import com.doudian.open.api.product_detail.param.ProductDetailParam;
import com.doudian.open.api.product_editV2.ProductEditV2Request;
import com.doudian.open.api.product_editV2.param.ProductEditV2Param;
import com.doudian.open.api.product_getCatePropertyV2.ProductGetCatePropertyV2Request;
import com.doudian.open.api.product_getCatePropertyV2.ProductGetCatePropertyV2Response;
import com.doudian.open.api.product_getCatePropertyV2.data.DataItem;
import com.doudian.open.api.product_getCatePropertyV2.data.OptionsItem;
import com.doudian.open.api.product_getCatePropertyV2.data.ProductGetCatePropertyV2Data;
import com.doudian.open.api.product_getCatePropertyV2.param.ProductGetCatePropertyV2Param;
import com.doudian.open.api.product_listV2.ProductListV2Request;
import com.doudian.open.api.product_listV2.ProductListV2Response;
import com.doudian.open.api.product_listV2.data.ProductListV2Data;
import com.doudian.open.api.product_listV2.param.ProductListV2Param;
import com.doudian.open.api.product_qualificationConfig.ProductQualificationConfigRequest;
import com.doudian.open.api.product_qualificationConfig.ProductQualificationConfigResponse;
import com.doudian.open.api.product_qualificationConfig.data.ProductQualificationConfigData;
import com.doudian.open.api.product_qualificationConfig.param.ProductQualificationConfigParam;
import com.doudian.open.api.sku_list.SkuListRequest;
import com.doudian.open.api.sku_list.SkuListResponse;
import com.doudian.open.api.sku_list.param.SkuListParam;
import com.doudian.open.api.sku_stockNum.SkuStockNumRequest;
import com.doudian.open.api.sku_stockNum.SkuStockNumResponse;
import com.doudian.open.api.sku_stockNum.data.SkuStockNumData;
import com.doudian.open.api.sku_stockNum.param.SkuStockNumParam;
import com.doudian.open.api.sku_syncStockBatch.SkuSyncStockBatchRequest;
import com.doudian.open.api.sku_syncStockBatch.SkuSyncStockBatchResponse;
import com.doudian.open.api.sku_syncStockBatch.param.SkuSyncListItem;
import com.doudian.open.api.sku_syncStockBatch.param.SkuSyncStockBatchParam;
import com.doudian.open.core.AccessToken;
import com.doudian.open.core.DoudianOpResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RCountDownLatch;
import org.redisson.api.RSemaphore;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isNotEmpty;

/**
 * <AUTHOR> up
 * @date 2022年09月13日 1:54 PM
 */
@Slf4j
@Service
public class DouDianTemplateImpl implements DouDianTemplate {

    @Resource
    DouDianCommon common;

    @Resource
    IItemDrawerService iItemDrawerService;

    @Resource
    IThirdPlatformSyncLogService iThirdPlatformSyncLogService;

    @Resource
    ItemSkuAttrRefMapper itemSkuAttrRefMapper;

    @Resource
    IItemDrawerImageService iItemDrawerImageService;

    @Resource
    IItemSkuService iItemSkuService;

    @Resource
    INewGoodsService iNewGoodsService;

    @Resource
    ItemDrawerService itemDrawerService;

    @Resource
    ItemDrawerService itemDrawerAppService;

    @Resource
    IThirdPlatformSyncExternalTaskService iThirdPlatformSyncExternalTaskService;

    @Resource
    IItemService iItemService;

    @Resource
    IPlatformItemSkuService iPlatformItemSkuService;

    private final static String SUCCESS_CODE = "10000";

    @Resource
    RedissonClient redissonClient;


    private static void stringTrim(SkuAttrRefDO val) {
        String str = StrUtil.isNotBlank(val.getAttrValue()) ? val.getAttrValue().trim() : "";
        val.setAttrValue(str);
    }

    /**
     * @param itemId
     * @param productId
     */
    @Override
    public Resp<DouDianSyncBO> productEdit(Long itemId, Long productId, Integer syncType) {
        String activeProfile = SpringUtil.getActiveProfile();
        if ("dev".equals(activeProfile) || "local".equals(activeProfile) || "test".equals(activeProfile)) {
            productId = 3432968689782237306L;
        }

        // 获取同步请求所需参数
        PreHandleRes requiredParam;
        try {
            Resp<PreHandleRes> preHandleRes = exitPreHandle(itemId, productId, syncType);
            log.info("[抖店]编辑同步前置处理res，itemId:{},productId:{},syncType:{},res:{}", itemId, productId, syncType,
                    JsonUtil.toJson(preHandleRes));
            if (Boolean.FALSE.equals(preHandleRes.getIsSuccess())) {
                return new Resp.Builder<DouDianSyncBO>().isSuccess(false).error(preHandleRes.getError()).build();
            }
            requiredParam = preHandleRes.getData();
        } catch (Exception e) {
            log.error("[抖店]编辑同步前置处理异常，itemId:{},productId:{},syncType:{}", itemId, productId, syncType, e);
            return new Resp.Builder<DouDianSyncBO>().isSuccess(false).error("编辑同步前置处理异常。" + e.getMessage()).build();
        }

        ProductEditV2Request request = new ProductEditV2Request();
        ProductEditV2Param param = request.getParam();
        // 抖店编辑同步
        if (syncType.equals(ThirdPlatformSyncType.DOUDIAN_EDIT.getValue())) {
            param.setProductId(productId);
            param.setCommit(true);
        }
        // 抖店全量同步
        if (syncType.equals(ThirdPlatformSyncType.DOUDIAN_NEW.getValue())) {
            param.setProductId(productId);
            // 商品名称
            param.setName(requiredParam.getItemDrawer().getTbTitle());
            // sku详情
            param.setSpecPrices(requiredParam.getSpecPrice());
            // 商品包含的规格。
            // data example : 颜色|红色,黑色,白色  尺码|S,M   产地|北京
            param.setSpecs(String.join("^", requiredParam.getSpecList()));
            // 主图视频ID
            if (isNotEmpty(requiredParam.getMainVideoId())) {
                param.setMaterialVideoId(requiredParam.getMainVideoId());
            }
            // 当日现货发送
            param.setDeliveryDelayDay(9999L);
            param.setPresellType(0L);
            if (StrUtil.isNotBlank(requiredParam.getProductFormatNewJsonStr())) {
                param.setProductFormatNew(requiredParam.getProductFormatNewJsonStr());
            }
            param.setCommit(true);
        }

        // 抖店同步人参数能处理的都处理完成，等待素材中心的异步处理结果，再发起同步请求
        DouDianSyncBO bo = new DouDianSyncBO();
        bo.setProductEditV2Param(param);
        bo.setItemId(itemId);
        bo.setSyncType(syncType);
        // 保存请求数据，等异步任务更新完成之后再处理，生成一条同步进行中的日志
        iThirdPlatformSyncLogService.save(
                ThirdPlatformSyncLog.builder()
                        .syncId(requiredParam.getSyncId()).platformType(PlatformType.DOU_DIAN)
                        .itemId(itemId).req(JsonUtil.toJson(bo))
                        .itemCode(requiredParam.getItemCode())
                        .errorLevel(PlatformSyncErrorLevel.NONE)
                        .build()
        );
        return new Resp.Builder<DouDianSyncBO>().isSuccess(true).error("正在同步中").data(bo)
                .syncCode(requiredParam.getItemCode() + "_" + requiredParam.getSyncId()).build();
    }

    @Data
    private static class PreHandleRes {
        AccessToken accessToken;
        Long syncId;
        ItemDrawer itemDrawer;
        List<SkuAttrRefDO> skuAttrList;
        List<ItemSku> skuList;
        String specPrice;
        List<String> specList;
        String mainVideoId;
        private String productFormatNewJsonStr;
        private String itemCode;
    }

    private Resp<PreHandleRes> exitPreHandle(Long itemId, Long productId, Integer syncType) {
        // 基本信息校验。
        Assert.notNull(itemId, "itemId必须存在");
        Assert.notNull(productId, "抖店产品id必须存在");
        Assert.notNull(syncType, "抖店同步类型必须存在");
        Item item = iItemService.getById(itemId);
        Assert.notNull(item, "itemId非法");
        long syncId = System.currentTimeMillis() / 1000;

        Optional<ItemDrawer> itemDrawerOptional = Optional.ofNullable(itemDrawerAppService.getMergeMainItemDrawer(itemId));
        if (!itemDrawerOptional.isPresent()) {
            return new Resp.Builder<PreHandleRes>().isSuccess(false).error("商品抽屉信息不存在。itemId:" + itemId)
                    .build();
        }
        ItemDrawer itemDrawer = itemDrawerOptional.get();

        // token处理
        AccessToken accessToken = common.getAccessToken();
        if (!accessToken.isSuccess()) {
            return new Resp.Builder<PreHandleRes>().isSuccess(false)
                    .error("抖店token获取失败" + accessToken.getMsg() + "，" + accessToken.getSubMsg()).build();
        }

        // 查询商品的媒体信息，添加主图验证，抖店支持多张主图。主图尺寸比例必须是1:1
        List<ItemDrawerImage> allItemDrawerImageList = iItemDrawerImageService.lambdaQuery()
                .eq(ItemDrawerImage::getDrawerId, itemDrawer.getId())
                .select().list();
        List<String> errorImageFileList = allItemDrawerImageList.stream()
                .filter(val ->
                        val.getType().equals(ItemDrawerImageTypeEnum.ITEM) && val.getProportion().compareTo(BigDecimal.ONE) != 0)
                .map(ItemDrawerImage::getFilename).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(errorImageFileList)) {
            return new Resp.Builder<PreHandleRes>().isSuccess(false)
                    .error("以下主图尺寸比非1:1。file:" + String.join("；", errorImageFileList))
                    .build();
        }

        // 处理同步sku的规格信息
        List<ItemSku> skuList = iItemSkuService.lambdaQuery().eq(ItemSku::getItemId, itemId).select().list();
        List<Long> skuIdList = skuList.stream().map(ItemSku::getId).collect(Collectors.toList());
        // 根据规格属性的dbId顺序排列
        List<SkuAttrRefDO> skuAttrList = itemSkuAttrRefMapper.getSkuAttrList(skuIdList).stream()
                .sorted(Comparator.comparing(SkuAttrRefDO::getItemAttrDbId))
                .peek(DouDianTemplateImpl::stringTrim)
                .collect(Collectors.toList());
        Set<String> allAttrNameSet = skuAttrList.stream().map(SkuAttrRefDO::getAttrName).collect(Collectors.toSet());
        if (allAttrNameSet.size() > 3) {
            return new Resp.Builder<PreHandleRes>().isSuccess(false).error("sku规格种类数量超过3个，抖店同步不支持").build();
        }

        // 如果是全量同步，获取处理sku规格价格等详细信息
        String specPrice = "";
        List<String> specList = new LinkedList<>();
        if (syncType.equals(ThirdPlatformSyncType.DOUDIAN_NEW.getValue())) {
            specList = getSpec(skuAttrList);
            Resp<String> specsPriceRes = getSpecsPrice(skuList, skuAttrList, specList);
            if (Boolean.FALSE.equals(specsPriceRes.getIsSuccess())) {
                return new Resp.Builder<PreHandleRes>().isSuccess(false).error("参数异常。" + specsPriceRes.getError()).build();
            } else {
                specPrice = specsPriceRes.getData();
            }
        }

        // 同步上传视频
        String mainVideoId = mainVideo(allItemDrawerImageList, itemId, accessToken);

        // 异步上传主图和详情图
        Resp<List<SyncItemRes>> mainImageResp = asyncImageUploadDouDian(allItemDrawerImageList, ItemDrawerImageTypeEnum.ITEM,
                itemId, accessToken);
        if (Boolean.FALSE.equals(mainImageResp.getIsSuccess())) {
            return new Resp.Builder<PreHandleRes>().isSuccess(false)
                    .error("主图上传异常。" + mainImageResp.getError()).build();
        }
        Resp<List<SyncItemRes>> descriptionImageResp = asyncImageUploadDouDian(allItemDrawerImageList, ItemDrawerImageTypeEnum.DETAIL,
                itemId, accessToken);
        if (Boolean.FALSE.equals(descriptionImageResp.getIsSuccess())) {
            return new Resp.Builder<PreHandleRes>().isSuccess(false)
                    .error("详情图上传异常。" + descriptionImageResp.getError()).build();
        }
        afterUploadImageHandler(mainImageResp.getData(), syncId, ThirdPlatformSyncExternalTaskType.DOUDIAN_MAIN);
        afterUploadImageHandler(descriptionImageResp.getData(), syncId, ThirdPlatformSyncExternalTaskType.DOUDIAN_DESC);

        // 拼接处理商品属性参数
        String productFormatNew = productFormatNew(productId.toString(), accessToken, itemId, null, itemDrawer);

        PreHandleRes res = new PreHandleRes();
        res.setAccessToken(accessToken);
        res.setSyncId(syncId);
        res.setMainVideoId(mainVideoId);
        res.setItemDrawer(itemDrawer);
        res.setSkuAttrList(skuAttrList);
        res.setSkuList(skuList);
        res.setSpecPrice(specPrice);
        res.setSpecList(specList);
        res.setProductFormatNewJsonStr(productFormatNew);
        res.setItemCode(item.getCode());
        return new Resp.Builder<PreHandleRes>().isSuccess(true).data(res).error("抖店编辑同步，前置处理完成").build();
    }

    private void afterUploadImageHandler(List<SyncItemRes> materialsIdList, Long syncId, ThirdPlatformSyncExternalTaskType type) {
        if (CollUtil.isNotEmpty(materialsIdList)) {
            List<ThirdPlatformSyncExternalTask> externalTaskList = materialsIdList.stream().
                    sorted(Comparator.comparing(SyncItemRes::getSort)).map(val -> {
                        ThirdPlatformSyncExternalTask thirdPlatformSyncExternalTask = new ThirdPlatformSyncExternalTask();
                        thirdPlatformSyncExternalTask.setSyncId(syncId);
                        thirdPlatformSyncExternalTask.setType(type);
                        thirdPlatformSyncExternalTask.setTaskId(val.getMaterialId());
                        thirdPlatformSyncExternalTask.setState(ThirdPlatformSyncExternalTaskState.WAITING);
                        return thirdPlatformSyncExternalTask;
                    }).collect(Collectors.toList());
            iThirdPlatformSyncExternalTaskService.saveBatch(externalTaskList);
        }
    }

    /**
     * 异步上传图片到抖店。
     * //        // 规格抽屉详情图
     * //        String descriptionImage;
     * //        // 规格图片 img_url,img_url,img_url
     * //        String specPicImage;
     * //        // 主图
     * //        String picImage;
     *
     * @param itemDrawerImageList
     * @param imageTypeEnum
     * @param itemId
     * @param accessToken
     * @return
     */
    private Resp<List<SyncItemRes>> asyncImageUploadDouDian(List<ItemDrawerImage> itemDrawerImageList, ItemDrawerImageTypeEnum imageTypeEnum,
                                                            Long itemId, AccessToken accessToken) {
        List<ItemDrawerImage> specImageList = getTargetImageList(itemDrawerImageList, imageTypeEnum);
        if (CollUtil.isEmpty(specImageList)) {
            Resp.Builder<List<SyncItemRes>> builder = new Resp.Builder<>();
            return builder.isSuccess(true)
                    .error(imageTypeEnum.getDesc() + "为空，无需上传")
                    .build();
        }
        return syncItemDrawerImageList(specImageList, imageTypeEnum, itemId, accessToken);
    }

    /**
     * 主图视频上传处理，这是同步处理，如果上传成功就给，不成功就不传
     * 不要影响整体流程
     *
     * @param itemDrawerImageList
     * @param itemId
     * @param accessToken
     * @return
     */
    private String mainVideo(List<ItemDrawerImage> itemDrawerImageList, Long itemId, AccessToken accessToken) {
        // 主图视频ID
        List<ItemDrawerImage> mainVideoList = itemDrawerImageList.stream().filter(val -> val.getType()
                        .equals(ItemDrawerImageTypeEnum.MAIN_IMAGE_VIDEO) && val.getExt().equals("mp4"))
                .sorted(Comparator.comparing(ItemDrawerImage::getSort))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(mainVideoList)) {
            return syncItemDrawerVideo(mainVideoList.get(0), itemId, accessToken);
        }
        return "";
    }


    private List<ItemDrawerImage> getTargetImageList(List<ItemDrawerImage> list, ItemDrawerImageTypeEnum itemDrawerImageTypeEnum) {
        List<ItemDrawerImage> targetList = list.stream().filter(val -> val.getType().equals(itemDrawerImageTypeEnum))
                .collect(Collectors.toList());
        return CollUtil.isNotEmpty(targetList) && targetList.size() > 50 ?
                targetList.stream().sorted(Comparator.comparing(ItemDrawerImage::getSort))
                        .collect(Collectors.toList()).subList(0, 50)
                : targetList;
    }

    // ----------------------------------------- 手动分割线 ---------------------------------------------

    /**
     * 颜色|红色,黑色^尺码|S,M
     * 抖店最多支持三种规格。
     *
     * @param skuAttrList
     * @return
     */
    private List<String> getSpec(List<SkuAttrRefDO> skuAttrList) {
        Map<String, List<SkuAttrRefDO>> collect =
                skuAttrList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(()
                                -> new TreeSet<>(Comparator.comparing(SkuAttrRefDO::getItemAttrDbId))), ArrayList::new))
                        .stream().collect(Collectors.groupingBy(SkuAttrRefDO::getAttrName));
//        Map<String, List<SkuAttrRefDO>> collect = skuAttrList.stream().collect(Collectors.groupingBy(SkuAttrRefDO::getAttrName));

        List<SpecBO> result = new LinkedList<>();
        collect.forEach((attrName, list) -> {
            Set<String> collect1 = list.stream().map(val -> val.getAttrValue().trim()).collect(Collectors.toSet());
            String join = String.join(",", collect1);
            String one = attrName + "|" + join;

            SpecBO bo = new SpecBO();
            bo.setSpec(one);
            bo.setCount(list.size());
            result.add(bo);
        });
        // 规格种类最多的作为主规格。
        return result.stream().sorted(Comparator.comparing(SpecBO::getCount).reversed()).map(SpecBO::getSpec)
                .collect(Collectors.toList());
    }

    @Data
    private static class SpecBO {
        private String spec;
        private Integer count;
    }

    /**
     * 拼接抖店需要的sku规格属性信息
     *
     * @param specList
     * @return
     */
    public Resp<String> getSpecsPrice(List<ItemSku> skuList, List<SkuAttrRefDO> skuAttrList, List<String> specList) {

        Map<Long, List<SkuAttrRefDO>> skuIdAttrListMap = skuAttrList.stream()
                .collect(Collectors.groupingBy(SkuAttrRefDO::getSkuId));

        Map<String, List<String>> specNameAndSpecValListMap = new HashMap<>(4);
        specList.forEach(val -> {
            String[] split = val.split("\\|");
            List<String> of = ListUtil.of(split[1].split(","));
            specNameAndSpecValListMap.put(split[0], of);
        });

        List<SpecPrice> list = new LinkedList<>();
        List<String> errorSkuList = new LinkedList<>();
        skuList.forEach(val -> {
            Optional<NewGoods> optionalNewGoods = iNewGoodsService.lambdaQuery().eq(NewGoods::getSkuCode, val.getSkuCode())
                    .select().oneOpt();
            if (!optionalNewGoods.isPresent()) {
                return;
            }
            NewGoods newGoods = optionalNewGoods.get();
            BigDecimal dailyPrice = newGoods.getDailyPrice();
            BigDecimal price = dailyPrice.multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
            if (price.compareTo(BigDecimal.ZERO) == 0) {
                errorSkuList.add(val.getSkuCode());
                return;
            }
            String skuCode = val.getSkuCode();
            String specDetailName1 = "";
            String specDetailName2 = "";
            String specDetailName3 = "";
            List<SkuAttrRefDO> orDefault = skuIdAttrListMap.get(val.getId());
            // 此sku拥有的规格值
            Set<String> skuHaveAttrValList = orDefault.stream().map(SkuAttrRefDO::getAttrValue).collect(Collectors.toSet());

            if (specNameAndSpecValListMap.size() == 1) {
                specDetailName1 = getTargetIndexSpec(specList, specNameAndSpecValListMap, skuHaveAttrValList, 0);
            }
            if (specNameAndSpecValListMap.size() == 2) {
                specDetailName1 = getTargetIndexSpec(specList, specNameAndSpecValListMap, skuHaveAttrValList, 0);
                specDetailName2 = getTargetIndexSpec(specList, specNameAndSpecValListMap, skuHaveAttrValList, 1);
            }
            if (specNameAndSpecValListMap.size() == 3) {
                specDetailName1 = getTargetIndexSpec(specList, specNameAndSpecValListMap, skuHaveAttrValList, 0);
                specDetailName2 = getTargetIndexSpec(specList, specNameAndSpecValListMap, skuHaveAttrValList, 1);
                specDetailName3 = getTargetIndexSpec(specList, specNameAndSpecValListMap, skuHaveAttrValList, 2);
            }

            SpecPrice one = new SpecPrice();
            one.setSpecDetailName1(specDetailName1);
            one.setSpecDetailName2(specDetailName2);
            one.setSpecDetailName3(specDetailName3);
//            one.setStockNum(0);
            one.setPrice(price);
            one.setCode(skuCode);
//            one.setStepStockNum(0);
//            one.setSupplierId();
//            one.setOuterSkuId();
//            one.setDeliveryInfos();、
            list.add(one);
        });

        Resp.Builder<String> builder = new Resp.Builder<>();
        if (CollUtil.isNotEmpty(errorSkuList)) {
            return builder.isSuccess(false).error("sku产品日销价格不得为0或者空。errorSkuCode:" + String.join(",", errorSkuList))
                    .build();
        }

        return builder.isSuccess(true).data(JsonUtil.toJson(list)).build();
    }

    private String getTargetIndexSpec(List<String> specList, Map<String, List<String>> specNameAndSpecValListMap,
                                      Set<String> skuHaveAttrValList, Integer index) {
        String specDetailName = "";
        // 取第一个规格
        String targetSpec = specList.get(index).split("\\|")[0];
        // 第一个规格的规格值列表
        List<String> targetSpecValList = specNameAndSpecValListMap.get(targetSpec);
        // specDetailName1 = 取属于第一个规格的规格值。要一一对应,否则留空
        Optional<String> first = skuHaveAttrValList.stream().filter(targetSpecValList::contains).findFirst();
        if (first.isPresent()) {
            specDetailName = first.get();
        }
        return specDetailName;

    }


    /**
     * 素材中心请求id生成规则统一
     *
     * @param itemDrawerImage
     * @param itemId
     * @return
     */
    private String generateMaterialRequestId(ItemDrawerImage itemDrawerImage, Long itemId) {
        return "MR_" + itemId + "_" + itemDrawerImage.getId();
    }

    private String generateMaterialFileName(ItemDrawerImage itemDrawerImage, Long itemId, Boolean isImage) {
        if (isImage) {
            return "FI_" + itemId + "_" + itemDrawerImage.getId();
        } else {
            return "FV_" + itemId + "_" + itemDrawerImage.getId();
        }
    }

    // ----------------------------------------- 手动分割线 ---------------------------------------------

    /**
     * 批量上传图片到素材中心
     *
     * @param list   ItemDrawerImage中详情图片
     * @param itemId 商品id
     * @return // {
     * //    "data": {
     * //        "successMap": {
     * //            "538_1403": {
     * //                "materialId": "71452889270330166070798",
     * //                "name": "image_363442_1403_1",
     * //                "folderId": "0",
     * //                "originUrl": "https://cdn-test.daddylab.com/Upload/supplier/item/image/1WRf6xeeyBn324dcba14ea27cada03577891b238468b-1663149046015.jpeg",
     * //                "byteUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/ALSUvYM_m_2864bb6bc16646f71441e3af23072e7c_sx_26453_www200-200",
     * //                "auditStatus": 1,
     * //                "isNew": true
     * //            }
     * //        },
     * //        "failedMap": {}
     * //    },
     * //    "logId": "20220920105052010208018167004BC6B3",
     * //    "code": "10000",
     * //    "msg": "success",
     * //    "subCode": "",
     * //    "subMsg": "",
     * //    "originResponse": "{\"code\":10000,\"data\":{\"failed_map\":{},\"success_map\":{\"538_1403\":{\"AuditStatus\":1,\"ByteUrl\":\"https://p3-aio.ecombdimg.com/obj/ecom-shop-material/ALSUvYM_m_2864bb6bc16646f71441e3af23072e7c_sx_26453_www200-200\",\"FolderId\":\"0\",\"IsNew\":true,\"MaterialId\":\"71452889270330166070798\",\"Name\":\"image_363442_1403_1\",\"OriginUrl\":\"https://cdn-test.daddylab.com/Upload/supplier/item/image/1WRf6xeeyBn324dcba14ea27cada03577891b238468b-1663149046015.jpeg\",\"QualifyStatus\":0,\"QualifyStatusDesc\":\"\"}}},\"log_id\":\"20220920105052010208018167004BC6B3\",\"msg\":\"success\",\"sub_code\":\"\",\"sub_msg\":\"\"}",
     * //    "success": true
     * //}
     */
    private Resp<List<SyncItemRes>> syncItemDrawerImageList(List<ItemDrawerImage> list, ItemDrawerImageTypeEnum imageTypeEnum,
                                                            Long itemId, AccessToken accessToken) {
        String prefix = imageTypeEnum.getDesc();

        List<MaterialsItem> photoList = list.stream().map(val -> {
            MaterialsItem materialsItem = new MaterialsItem();
            materialsItem.setRequestId(generateMaterialRequestId(val, itemId));
            // fixme 文件夹id，0为素材中心根目录。若想创建文件夹，请参考：https://ehome.bytedance.net/djt/apiManage/doc/preview/946?doc=true
            // 暂时不区分
            materialsItem.setFolderId("0");
            materialsItem.setMaterialType("photo");
            // 图片名称转
            materialsItem.setName(generateMaterialFileName(val, itemId, true));
            materialsItem.setUrl(val.getUrl());
            return materialsItem;
        }).collect(Collectors.toList());
        Map<String, Long> urlSortNoMap = list.stream().collect(Collectors.toMap(ItemDrawerImage::getUrl, ItemDrawerImage::getSort));

        try {
            MaterialBatchUploadImageSyncResponse response = batchUploadImageAsync0(photoList, accessToken);
            if (!response.isSuccess()) {
                iThirdPlatformSyncLogService.asyncSaveDouDianLog(itemId, prefix + "【抖店】批量上传图片到素材中心失败。"
                        + response.getMsg() + "，" + response.getSubMsg(), PlatformSyncErrorLevel.WARN);
                Resp.Builder<List<SyncItemRes>> builder = new Resp.Builder<>();
                return builder.isSuccess(false)
                        .error(prefix + "【抖店】批量上传图片到素材中心失败。" + response.getMsg() + "，" + response.getSubMsg())
                        .build();
            } else {
                // 发起素材同步任务成功，返回素材id,后期根据素材id取查询抖店素材url
                Map<String, SuccessMapItem> successMap = response.getData().getSuccessMap();
                List<SyncItemRes> materialIdList = successMap.values().stream()
                        .map(val -> {
                            SyncItemRes res = new SyncItemRes();
                            res.setMaterialId(val.getMaterialId());
                            res.setSort(urlSortNoMap.get(val.getOriginUrl()));
                            return res;
                        }).sorted(Comparator.comparing(SyncItemRes::getSort)).collect(Collectors.toList());
                Resp.Builder<List<SyncItemRes>> builder = new Resp.Builder<>();
                return builder.isSuccess(true)
                        .error("上传任务发起成功")
                        .data(materialIdList)
                        .build();
            }
        } catch (Exception e) {
            log.error(prefix + "【抖店】批量上传图片到素材中心异常，itemId:{}", itemId, e);
            iThirdPlatformSyncLogService.asyncSaveDouDianLog(itemId, prefix + "【抖店】批量上传图片到素材中心异常。" + e.getMessage(), PlatformSyncErrorLevel.WARN);
            Resp.Builder<List<SyncItemRes>> builder = new Resp.Builder<>();
            return builder.isSuccess(false)
                    .error(prefix + "【抖店】批量上传图片到素材中心异常。" + e.getMessage())
                    .build();
        }

    }


    /**
     * 素材中心同步创建图片素材接口
     * 1. 一次上传的数量限制50张图片；
     * 2. 图片大小限10M
     * 3. 素材中心会对素材进行异步审核，对于审核失败的素材，素材中心会在一定时间内将其删除。
     *
     * @param list
     * @return
     */
    private MaterialBatchUploadImageSyncResponse batchUploadImageAsync0(List<MaterialsItem> list, AccessToken accessToken) {
        MaterialBatchUploadImageSyncRequest request = new MaterialBatchUploadImageSyncRequest();
        MaterialBatchUploadImageSyncParam param = request.getParam();
        param.setMaterials(list);

        return request.execute(accessToken);
    }


    /**
     * 素材中心同步创建图片素材接口
     * 1、如果一次性要上传多个素材，请使用批量上传图片接口：https://ehome.bytedance.net/djt/apiManage/doc/preview/1616?doc=true
     * 2、图片大小限3M，支持的格式有：jpeg/png/jpg；
     * 3、素材中心会对素材进行异步审核，对于审核失败的素材，素材中心会在一定时间内将其删除，届时将不可访问。
     *
     * @param folderId 素材所在文件夹id，0-素材中心的根目录；其他值-表示对应的文件夹id；
     * @param fileUrl
     * @param fileName
     * @return
     */
    private MaterialUploadImageSyncResponse uploadImageAsync0(String folderId, String fileUrl, String fileName, AccessToken accessToken) {
        MaterialUploadImageSyncRequest request = new MaterialUploadImageSyncRequest();
        MaterialUploadImageSyncParam param = request.getParam();
        param.setFolderId(folderId);
        param.setUrl(fileUrl);
        param.setMaterialName(fileName);
        return request.execute(accessToken);
    }


    // ----------------------------------------- 手动分割线 ---------------------------------------------


    /**
     * //{
     * //    "data": {
     * //        "materialId": "71453012866896202570798",
     * //        "folderId": "0",  素材所在文件夹id，0-素材中心的根目录
     * //        "isNew": true,
     * //        "auditStatus": 1  素材审核状态 0-下载中 1-等待审核 2-审核中 3-通过 4-拒绝
     * //    },
     * //    "logId": "202209201138330101581651361B029C14",
     * //    "code": "10000",
     * //    "msg": "success",
     * //    "subCode": "",
     * //    "subMsg": "",
     * //    "originResponse": "{\"code\":10000,\"data\":{\"audit_status\":1,\"folder_id\":\"0\",\"is_new\":true,\"material_id\":\"71453012866896202570798\"},\"log_id\":\"202209201138330101581651361B029C14\",\"msg\":\"success\",\"sub_code\":\"\",\"sub_msg\":\"\"}",
     * //    "success": true
     * //}
     *
     * @param itemDrawerImage
     * @param itemId
     * @return
     */
    private String syncItemDrawerVideo(ItemDrawerImage itemDrawerImage, Long itemId, AccessToken accessToken) {
        String materialVideoId = "";

        try {
            String videoName = generateMaterialFileName(itemDrawerImage, itemId, false);
            MaterialUploadVideoAsyncRequest materialUploadVideoAsyncRequest = uploadVideoAsync0("0", itemDrawerImage.getUrl(), videoName);
            log.info("【抖店】上传视频到素材请求参数，itemId:{},param:{}", itemId, JsonUtil.toJson(materialUploadVideoAsyncRequest));
            MaterialUploadVideoAsyncResponse response = materialUploadVideoAsyncRequest.execute(accessToken);
            log.info("【抖店】上传视频到素材中心响应，itemId:{},response:{}", itemId, JsonUtil.toJson(response));

            if (!response.isSuccess()) {
                iThirdPlatformSyncLogService.asyncSaveDouDianLog(0L, itemId, "【抖店】上传视频到素材中心失败。" +
                                response.getMsg() + "，" + response.getSubMsg(), PlatformSyncErrorLevel.WARN,
                        JsonUtil.toJson(materialUploadVideoAsyncRequest.getParam()), JsonUtil.toJson(response));
            } else {

                MaterialQueryMaterialDetailRequest request2 = new MaterialQueryMaterialDetailRequest();
                MaterialQueryMaterialDetailParam param = request2.getParam();
                param.setMaterialId(response.getData().getMaterialId());
                MaterialQueryMaterialDetailResponse response2 = request2.execute(accessToken);
                log.info("【抖店】根据视频素材id查视频素材详情响应，itemId:{},response:{}", itemId, JsonUtil.toJson(response2));
                if (!response2.isSuccess()) {
                    iThirdPlatformSyncLogService.asyncSaveDouDianLog(0L, itemId, "【抖店】根据视频素材id查视频素材详情响应失败。" +
                                    response.getMsg() + "，" + response.getSubMsg(), PlatformSyncErrorLevel.WARN,
                            JsonUtil.toJson(request2.getParam()), JsonUtil.toJson(response2));
                } else {
                    try {
                        VideoInfo videoInfo = response2.getData().getMaterialInfo().getVideoInfo();
                        materialVideoId = videoInfo.getVid();
                    } catch (Exception e) {
                        iThirdPlatformSyncLogService.asyncSaveDouDianLog(0L, itemId, "【抖店】根据视频素材id查视频素材详情，响应解析异常" +
                                        e.getMessage(), PlatformSyncErrorLevel.WARN,
                                JsonUtil.toJson(request2.getParam()), JsonUtil.toJson(response2));
                    }
                }
            }
        } catch (Exception e) {
            log.error("【抖店】上传视频到素材中心异常，itemId:{}", itemId, e);
            iThirdPlatformSyncLogService.asyncSaveDouDianLog(itemId, "【抖店】上传视频到素材中心异常。" + e.getMessage(), PlatformSyncErrorLevel.WARN);
        }

        return materialVideoId;
    }

    /**
     * 1. 一次上传视频的数量限制50个；
     * 2. 每个视频大小限200M；
     * 3. 接口同步接受上传视频的请求，异步处理：视频的下载、格式校验、审核等过程；
     * 4. 视频的审核时间根据素材中心素材数量而定，大部分视频在1分钟内能审核完，小部分视频审核时间会达到天级别，请耐心等待
     * 5. 审核拒绝的视频将在15日内被定期清理，请慎用。
     * 其他问题请参考：https://op.jinritemai.com/docs/guide-docs/171/1719
     *
     * @return
     */
    public MaterialBatchUploadVideoAsyncResponse batchUploadVideoAsync0(List<com.doudian.open.api.material_batchUploadVideoAsync.param.MaterialsItem> list
            , AccessToken accessToken) {
        MaterialBatchUploadVideoAsyncRequest request = new MaterialBatchUploadVideoAsyncRequest();
        MaterialBatchUploadVideoAsyncParam param = request.getParam();
        param.setMaterials(list);
        return request.execute(accessToken);
    }

    /**
     * 1. 视频大小限200M;
     * 2. 接口同步接受上传视频的请求，异步处理：视频的下载、格式校验、审核等过程；
     * 3. 视频的审核时间根据素材中心素材数量而定，大部分视频在1分钟内能审核完，小部分视频审核时间会达到天级别，请耐心等待；
     * 4. 审核拒绝的视频将在15日内被定期清理，请慎用。
     * 其他问题请参考：https://op.jinritemai.com/docs/guide-docs/171/1719
     *
     * @param folderId  文件夹id,为0不区分。全部放在素材中心根目录
     * @param videoUrl  不解释
     * @param videoName 例子 xxx女星.map4
     * @return
     */
    private MaterialUploadVideoAsyncRequest uploadVideoAsync0(String folderId, String videoUrl, String videoName) {
        MaterialUploadVideoAsyncRequest request = new MaterialUploadVideoAsyncRequest();
        MaterialUploadVideoAsyncParam param = request.getParam();
        param.setFolderId(folderId);
        param.setUrl(videoUrl);
        // 视频名称，不得超过50个字符，最好带上后缀
        param.setName(videoName);
        return request;
//        return request.execute(common.getAccessToken());
        // 响应示例
        //{
        //  "data": {
        //    "audit_status": "1",
        //    "folder_id": "0",
        //    "is_new": "true",
        //    "material_id": "7000254886243811628"
        //  },
        //  "code": 10000,
        //  "msg": "success",
        //  "sub_code": "",
        //  "sub_msg": ""
        //}
    }

    // ----------------------------------------- 手动分割线 ---------------------------------------------


    private Long getCategoryId(Long productId, AccessToken accessToken) {
        ProductDetailResponse category0 = getCategory0(productId, accessToken);
        if (category0.isSuccess()) {
            CategoryDetail categoryDetail = category0.getData().getCategoryDetail();
            Long fourthCid = categoryDetail.getFourthCid();
            if (Objects.nonNull(fourthCid)) {
                return fourthCid;
            }
            Long thirdCid = categoryDetail.getThirdCid();
            if (Objects.nonNull(thirdCid)) {
                return thirdCid;
            }
            Long secondCid = categoryDetail.getSecondCid();
            if (Objects.nonNull(secondCid)) {
                return secondCid;
            }
            Long firstCid = categoryDetail.getFirstCid();
            if (Objects.nonNull(firstCid)) {
                return firstCid;
            }
            return 0L;
        } else {
            return 0L;
        }
    }

    private ProductDetailResponse getCategory0(Long productId, AccessToken accessToken) {
        ProductDetailRequest request = new ProductDetailRequest();
        ProductDetailParam param = request.getParam();
        param.setProductId(productId.toString());
        return request.execute(accessToken);
    }


    /**
     * 查询素材中心素材信息
     *
     * @param materialId  素材中心id
     * @param accessToken 抖店token
     * @return
     */
    @Override
    public MaterialQueryMaterialDetailResponse getMaterial0(String materialId, AccessToken accessToken) {
        MaterialQueryMaterialDetailRequest request = new MaterialQueryMaterialDetailRequest();
        MaterialQueryMaterialDetailParam param = request.getParam();
        param.setMaterialId(materialId);
        return request.execute(accessToken);
    }


    // -------------- 我是分割线 -------------

    private final static List<String> MATCH_CATEGOTY_PROPERTY_LIST = new ArrayList<>();

    static {
        MATCH_CATEGOTY_PROPERTY_LIST.add("品牌");
        MATCH_CATEGOTY_PROPERTY_LIST.add("产品名称");
        MATCH_CATEGOTY_PROPERTY_LIST.add("保质期");
        MATCH_CATEGOTY_PROPERTY_LIST.add("规格类型");
        MATCH_CATEGOTY_PROPERTY_LIST.add("适合肤质");
        MATCH_CATEGOTY_PROPERTY_LIST.add("功效");
        MATCH_CATEGOTY_PROPERTY_LIST.add("产地");
        MATCH_CATEGOTY_PROPERTY_LIST.add("备案/批准文号");
        MATCH_CATEGOTY_PROPERTY_LIST.add("是否为特殊用品化妆品");
        MATCH_CATEGOTY_PROPERTY_LIST.add("生产企业名称");
    }

    private final static String REGEX = "[^\u4e00-\u9fa5]";

    @Data
    private static class AttrDO {
        private Object value;
        private String name;
        @JsonProperty("diy_type")
        private Long diyType;
    }

    public String productFormatNew(String productId, AccessToken accessToken, Long itemId, String json, ItemDrawer itemDrawer) {
        Map<String, List<AttrDO>> resMap = new HashMap<>(16);

        CategoryDetail categoryDetail = queryProductCategory(productId, accessToken);
        List<Long> cidList = ListUtil.of(categoryDetail.getFirstCid(), categoryDetail.getSecondCid(), categoryDetail.getThirdCid(), categoryDetail.getFourthCid());
        List<Long> cleanCidList = cidList.stream().filter(val -> val > 0).collect(Collectors.toList());

        Set<DataItem> matchDateItemSet = new HashSet<>();
        cleanCidList.forEach(cleanCid -> {
            ProductGetCatePropertyV2Data productGetCatePropertyV2Data = queryCategoryAttributeList(cleanCid, accessToken);
            List<DataItem> data = productGetCatePropertyV2Data.getData();
            matchDateItemSet.addAll(data);
        });

        PartnerItemResp partnerItemResp = StrUtil.isNotBlank(json) ? JsonUtil.parse(json, PartnerItemResp.class)
                : itemDrawerService.psysItemInfo(itemId).getData();
        if (Objects.isNull(partnerItemResp)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.API_REQ_ERROR, "【抖店】商品属性P系统查询返回为空");
        }
        String shelfLife;
        String netContent;
        List<ItemSku> itemSkuList = iItemSkuService.lambdaQuery()
                .eq(ItemSku::getItemId, itemId)
                .ne(ItemSku::getProps, "")
                .list();
        if (CollUtil.isNotEmpty(itemSkuList)) {
            LinkedHashMap<String, Object> props = itemSkuList.get(0).getProps();
            if (Objects.nonNull(props.get("shelfLife"))) {
                shelfLife = (String) props.get("shelfLife");
            } else {
                shelfLife = "30天";
            }
            if (Objects.nonNull(props.get("netContent"))) {
                netContent = (String) props.get("netContent");
            } else {
                netContent = "100";
            }
        } else {
            shelfLife = "30天";
            netContent = "100";
        }

        matchDateItemSet.forEach(
                dataItem -> {
                    if (dataItem.getPropertyName().equals("产品净含量")) {
                        AttrDO attrDO = new AttrDO();
                        attrDO.setValue(0);
                        attrDO.setName(netContent);
                        attrDO.setDiyType(dataItem.getDiyType());
                        resMap.put(dataItem.getPropertyId().toString(), ListUtil.of(attrDO));
                    }
                    if (dataItem.getPropertyName().equals("产地")) {
                        AttrDO attrDO = new AttrDO();
                        attrDO.setValue(0);
                        attrDO.setName("中国大陆");
                        attrDO.setDiyType(dataItem.getDiyType());
                        resMap.put(dataItem.getPropertyId().toString(), ListUtil.of(attrDO));
                    }
                    if (dataItem.getPropertyName().equals("规格类型")) {
                        AttrDO attrDO = new AttrDO();
                        attrDO.setValue(0);
                        attrDO.setName("正常规格");
                        attrDO.setDiyType(dataItem.getDiyType());
                        resMap.put(dataItem.getPropertyId().toString(), ListUtil.of(attrDO));
                    }
                    if (dataItem.getPropertyName().equals("保质期")) {
                        AttrDO attrDO = new AttrDO();
                        attrDO.setValue(0);
                        attrDO.setName(shelfLife);
                        attrDO.setDiyType(dataItem.getDiyType());
                        resMap.put(dataItem.getPropertyId().toString(), ListUtil.of(attrDO));
                    }
                    if (dataItem.getPropertyName().equals("产品名称")) {
                        if (StrUtil.isNotBlank(itemDrawer.getStandardName())) {
                            AttrDO attrDO = new AttrDO();
                            attrDO.setValue(0);
                            attrDO.setName(itemDrawer.getStandardName());
                            attrDO.setDiyType(dataItem.getDiyType());
                            resMap.put(dataItem.getPropertyId().toString(), ListUtil.of(attrDO));
                        }
                    }
                    if (dataItem.getPropertyName().equals("备案/批准文号")) {
                        if (StrUtil.isNotBlank(partnerItemResp.getProductFilingNo())) {
                            AttrDO attrDO = new AttrDO();
                            attrDO.setValue(0);
                            attrDO.setName(partnerItemResp.getProductFilingNo());
                            attrDO.setDiyType(dataItem.getDiyType());
                            resMap.put(dataItem.getPropertyId().toString(), ListUtil.of(attrDO));
                        }
                    }
                    if (dataItem.getPropertyName().equals("产品执行的标准编号")) {
                        // ignore
                    }
                    if (dataItem.getPropertyName().equals("功效")) {
                        List<String> productFilingEfficiency =
                                partnerItemResp.getProductFilingEfficiency();
                        if (CollUtil.isNotEmpty(productFilingEfficiency)) {
                            productFilingEfficiency =
                                    productFilingEfficiency.stream()
                                            .map(val -> val.replaceAll(REGEX, ""))
                                            .distinct()
                                            .collect(Collectors.toList());
                            List<String> finalProductFilingEfficiency = productFilingEfficiency;
                            List<OptionsItem> matchedOption =
                                    dataItem.getOptions().stream()
                                            .filter(
                                                    optionsItem ->
                                                            finalProductFilingEfficiency.contains(
                                                                    optionsItem.getName()))
                                            .collect(Collectors.toList());
                            if (CollUtil.isNotEmpty(matchedOption)) {
                                if (matchedOption.size() > 5) {
                                    matchedOption = matchedOption.subList(0, 5);
                                }
                                List<AttrDO> attrList =
                                        matchedOption.stream()
                                                .map(
                                                        optionsItem -> {
                                                            AttrDO attrDO = new AttrDO();
                                                            attrDO.setValue(0);
                                                            attrDO.setName(optionsItem.getName());
                                                            attrDO.setDiyType(
                                                                    dataItem.getDiyType());
                                                            return attrDO;
                                                        })
                                                .collect(Collectors.toList());
                                resMap.put(dataItem.getPropertyId().toString(), attrList);
                            }
                        }
                    }
                    if (dataItem.getPropertyName().equals("防晒指数")) {
                        String sunProtectionExponent = partnerItemResp.getSunProtectionExponent();
                        if (StrUtil.isNotBlank(sunProtectionExponent)) {
                            AttrDO attrDO = new AttrDO();
                            attrDO.setValue(0);
                            attrDO.setName(partnerItemResp.getSunProtectionExponent());
                            attrDO.setDiyType(dataItem.getDiyType());
                            resMap.put(dataItem.getPropertyId().toString(), ListUtil.of(attrDO));
                        }
                    }
                    if (dataItem.getPropertyName().equals("适用人群")) {
                        List<String> applicablePerson = partnerItemResp.getApplicablePerson();
                        if (CollUtil.isNotEmpty(applicablePerson)) {
                            List<OptionsItem> matchedOption =
                                    dataItem.getOptions().stream()
                                            .filter(
                                                    optionsItem ->
                                                            applicablePerson.contains(
                                                                    optionsItem.getName()))
                                            .collect(Collectors.toList());
                            if (CollUtil.isNotEmpty(matchedOption)) {
                                List<AttrDO> attrList =
                                        matchedOption.stream()
                                                .map(
                                                        optionsItem -> {
                                                            AttrDO attrDO = new AttrDO();
                                                            attrDO.setValue(0);
                                                            attrDO.setName(optionsItem.getName());
                                                            attrDO.setDiyType(
                                                                    dataItem.getDiyType());
                                                            return attrDO;
                                                        })
                                                .collect(Collectors.toList());
                                resMap.put(dataItem.getPropertyId().toString(), attrList);
                            }
                        }
                    }
                    if (dataItem.getPropertyName().equals("适合肤质")) {
                        String applicableSkinType = partnerItemResp.getApplicableSkinType();
                        if (CollectionUtil.isNotEmpty(partnerItemResp.getApplicableSkinTypeNew())) {
                            final List<AttrDO> attrDOS =
                                    partnerItemResp.getApplicableSkinTypeNew().stream()
                                            .map(
                                                    v -> {
                                                        AttrDO attrDO = new AttrDO();
                                                        attrDO.setValue(0);
                                                        attrDO.setName(v);
                                                        attrDO.setDiyType(dataItem.getDiyType());
                                                        return attrDO;
                                                    })
                                            .collect(Collectors.toList());
                            resMap.put(dataItem.getPropertyId().toString(), attrDOS);
                        } else if (StrUtil.isNotBlank(applicableSkinType)) {
                            AttrDO attrDO = new AttrDO();
                            attrDO.setValue(0);
                            attrDO.setName(partnerItemResp.getApplicableSkinType());
                            attrDO.setDiyType(dataItem.getDiyType());
                            resMap.put(dataItem.getPropertyId().toString(), ListUtil.of(attrDO));
                        }
                    }
                    if (dataItem.getPropertyName().equals("是否为特殊用途化妆品")) {
                        String isSpecialUseCosmetics = partnerItemResp.getIsSpecialUseCosmetics();
                        if (StrUtil.isNotBlank(isSpecialUseCosmetics)) {
                            AttrDO attrDO = new AttrDO();
                            attrDO.setValue(0);
                            attrDO.setName(partnerItemResp.getIsSpecialUseCosmetics());
                            attrDO.setDiyType(dataItem.getDiyType());
                            resMap.put(dataItem.getPropertyId().toString(), ListUtil.of(attrDO));
                        }
                    }
                    if (dataItem.getPropertyName().equals("注册人/备案人的名称")) {
                        String domesticResponsiblePerson =
                                partnerItemResp.getDomesticResponsiblePerson();
                        if (StrUtil.isNotBlank(domesticResponsiblePerson)) {
                            AttrDO attrDO = new AttrDO();
                            attrDO.setValue(0);
                            attrDO.setName(partnerItemResp.getDomesticResponsiblePerson());
                            attrDO.setDiyType(dataItem.getDiyType());
                            resMap.put(dataItem.getPropertyId().toString(), ListUtil.of(attrDO));
                        }
                    }
                    if (dataItem.getPropertyName().equals("注册人/备案人的地址")) {
                        String domesticResponsiblePersonAddr =
                                partnerItemResp.getDomesticResponsiblePersonAddr();
                        if (StrUtil.isNotBlank(domesticResponsiblePersonAddr)) {
                            AttrDO attrDO = new AttrDO();
                            attrDO.setValue(0);
                            attrDO.setName(partnerItemResp.getDomesticResponsiblePersonAddr());
                            attrDO.setDiyType(dataItem.getDiyType());
                            resMap.put(dataItem.getPropertyId().toString(), ListUtil.of(attrDO));
                        }
                    }
                    if (dataItem.getPropertyName().equals("生产企业名称")) {
                        String companyName = partnerItemResp.getCompanyName();
                        if (StrUtil.isNotBlank(companyName)) {
                            AttrDO attrDO = new AttrDO();
                            attrDO.setValue(0);
                            attrDO.setName(partnerItemResp.getCompanyName());
                            attrDO.setDiyType(dataItem.getDiyType());
                            resMap.put(dataItem.getPropertyId().toString(), ListUtil.of(attrDO));
                        }
                    }
                    if (dataItem.getPropertyName().equals("生产企业地址")) {
                        String factoryAddress = partnerItemResp.getFactoryAddress();
                        if (StrUtil.isNotBlank(factoryAddress)) {
                            AttrDO attrDO = new AttrDO();
                            attrDO.setValue(0);
                            attrDO.setName(partnerItemResp.getCompanyName());
                            attrDO.setDiyType(dataItem.getDiyType());
                            resMap.put(dataItem.getPropertyId().toString(), ListUtil.of(attrDO));
                        }
                    }
                });
        String res = CollUtil.isNotEmpty(resMap) ? JsonUtil.toJson(resMap) : "";
        log.info("【抖店】商品属性请求参数拼接，itemId:{},res:{}", itemId, res);
        return res;
    }


    /**
     * 根据商品ID查询商品类目ID信息
     *
     * @param productId   抖点商品id
     * @param accessToken
     * @return
     */
    public CategoryDetail queryProductCategory(String productId, AccessToken accessToken) {
        ProductDetailRequest request = new ProductDetailRequest();
        ProductDetailParam param = request.getParam();
        param.setProductId(productId);
        ProductDetailResponse execute = request.execute(accessToken);
        String code = execute.getCode();
        if (!SUCCESS_CODE.equals(code)) {
            throw new RuntimeException(execute.getMsg());
        }
        ProductDetailData data = execute.getData();
        return data.getCategoryDetail();
    }


    /**
     * 根据属性ID获取资质信息
     *
     * @param douDianCategoryId
     * @param accessToken
     * @return
     */
    @Deprecated
    public ProductQualificationConfigData qualificationConfigByCategoryId(Long douDianCategoryId, AccessToken accessToken) {
        if (douDianCategoryId == 0L) {
            return null;
        }
        ProductQualificationConfigRequest request = new ProductQualificationConfigRequest();
        ProductQualificationConfigParam param = request.getParam();
        param.setCategoryId(douDianCategoryId);
        ProductQualificationConfigResponse execute = request.execute(accessToken);
        String code = execute.getCode();
        if (!SUCCESS_CODE.equals(code)) {
            throw new RuntimeException(execute.getMsg());
        }
        return execute.getData();
    }

    /**
     * 获取类目id的属性列表
     *
     * @param categoryId
     * @param accessToken
     */
    public ProductGetCatePropertyV2Data queryCategoryAttributeList(Long categoryId, AccessToken accessToken) {
        ProductGetCatePropertyV2Request request = new ProductGetCatePropertyV2Request();
        ProductGetCatePropertyV2Param param = request.getParam();
        param.setCategoryLeafId(categoryId);
        ProductGetCatePropertyV2Response response = request.execute(accessToken);
        String code = response.getCode();
        if (!SUCCESS_CODE.equals(code)) {
            throw new RuntimeException(response.getMsg() + "。" + response.getSubMsg());
        }
        return response.getData();
    }

    public static void main(String[] args) {
        String input = "01 爽身夫妇";
        String regex = "[^\u4e00-\u9fa5]";
        String result = input.replaceAll(regex, "");
        System.out.println(result);

    }

    // ---------------------------- 抖店库存相关处理 --------------


    @Override
    public DoudianProductWithSkuListVO getProductWithSkuListVO(String shopId, Long productId) {
        final com.doudian.open.api.product_listV2.data.DataItem dataItem = queryProductById(shopId, productId);
        if (dataItem == null) {
            return null;
        }
        final DoudianProductWithSkuListVO doudianProductWithSkuListVO = new DoudianProductWithSkuListVO();
        doudianProductWithSkuListVO.setProduct(dataItem);
        final SkuListResponse skuListResponse = querySkuList(shopId, productId);
        final List<com.doudian.open.api.sku_list.data.DataItem> dataItems = Optional.ofNullable(skuListResponse)
                                                                                    .map(DoudianOpResponse::getData)
                                                                                    .orElseGet(Collections::emptyList);

        doudianProductWithSkuListVO.setSkuList(dataItems);

        return doudianProductWithSkuListVO;
    }

    @Override
    public ProductListV2Response batchQueryProduct(Long pageIndex, Long pageSize) {
        ProductListV2Request request = new ProductListV2Request();
        ProductListV2Param param = request.getParam();
        // 商品在店铺中状态: 0-在线；1-下线；2-删除
//        param.setStatus(0L);
        // 商品审核状态: 1-未提交；2-待审核；3-审核通过；4-审核未通过；5-封禁；7-审核通过待上架
//        param.setCheckStatus(3L);
        // 商品类型；0-普通；1-新客商品；3-虚拟；6-玉石闪购；7-云闪购 ；127-其他类型；
//        param.setProductType(0L);
        // 商品创建开始时间，unix时间戳，单位：秒；
//        param.setStartTime(1619161933L);
//        param.setEndTime(1619162000L);
        // 商品更新开始时间，unix时间戳，单位：秒
//        param.setUpdateStartTime(1619161933L);
//        param.setUpdateEndTime(1619161933L);
        param.setPage(pageIndex);
        param.setSize(pageSize);
        // 小时达商家使用的门店id，即时零售单店版
//        param.setStoreId(123456L);
        // 商品标题，支持模糊匹配
//        param.setName("标题");
        // 商品id，最大支持传入100个；
//        param.setProductId(3600137140018749665);
        // 是否使用游标，true 使用，false 不使用；使用游标能遍历全量商品，不会受到只能查询到10000个数据的影响。 特别说明：使用游标查询时，page字段失效，只会查询当前游标后面size条数据；
        // 根据获取查询数据为空来判断本次遍历结束；查询过程中数据发生变更，可能重复、遗漏数据；需要保证每次查询的筛选条件一致。
//        param.setUseCursor(true);
        // 游标id，结合use_cursor使用（use_cursor=true）;首次查询时不需要传值，每次查询结果会返回cursor_id，下一次查询时复制给该字段。当查询返回数据为空时，结束遍历。
//        param.setCursorId("WzE2ODI1Nzc4MjksMTc2NDMxMDczMDU3MDg0M10=");
        // 能否参加搭配
//        param.setCanCombineProduct(true);
        return request.execute(common.getAccessToken());

    }

    @Override
    public com.doudian.open.api.product_listV2.data.DataItem queryProductById(String shopId, Long productId) {
        ProductListV2RequestPatch request = new ProductListV2RequestPatch();
        ProductListV2ParamPatch param = request.getParam();

        param.setPage(1L);
        param.setSize(10L);
        param.setProductId(Collections.singletonList(productId));

        final ProductListV2Response productListV2Response = request.execute(common.getAccessToken(shopId));
        return Optional.ofNullable(productListV2Response)
                       .map(DoudianOpResponse::getData)
                       .map(ProductListV2Data::getData)
                       .filter(CollUtil::isNotEmpty)
                       .map(dataItems -> dataItems.get(0))
                       .orElse(null);
    }

    @Override
    public SkuListResponse querySkuList(Long productId) {
        SkuListRequest request = new SkuListRequest();
        SkuListParam param = request.getParam();
        param.setProductId(productId);
        return request.execute(common.getAccessToken());
    }

    @Override
    public SkuListResponse querySkuList(String shopId, Long productId) {
        SkuListRequest request = new SkuListRequest();
        SkuListParam param = request.getParam();
        param.setProductId(productId);
        return request.execute(common.getAccessToken(shopId));
    }

    @Override
    public SkuStockNumResponse querySkuStock(Long douDianSkuId) {
        SkuStockNumRequest request = new SkuStockNumRequest();
        SkuStockNumParam param = request.getParam();
        param.setSkuId(douDianSkuId);
        return request.execute(common.getAccessToken());
    }

    @Override
    public DouDianSkuStockResp queryBatchSkuStock(Long pageIndex, Long pageSize) {
        Page<PlatformItemSku> page = PageHelper.startPage(Math.toIntExact(pageIndex), Math.toIntExact(pageSize))
                .doSelectPage(() -> iPlatformItemSkuService.list());

        RSemaphore semaphore = redissonClient.getSemaphore("queryBatchSkuStock");
        int maxParallelThreads = 50;
        semaphore.trySetPermits(maxParallelThreads);
        RCountDownLatch countDownLatch = redissonClient.getCountDownLatch("queryBatchSkuStockLatch");
        countDownLatch.trySetCount(pageSize);

        CopyOnWriteArrayList<SkuStockNumDto> skuStockNumDtoList = new CopyOnWriteArrayList<>();
        page.forEach(platformItemSku -> {
            ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
                try {
                    semaphore.acquire();
                    SkuStockNumResponse skuStockNumResponse = querySkuStock(Long.valueOf(platformItemSku.getOuterSkuId()));
                    if (skuStockNumResponse.isSuccess()) {
                        SkuStockNumData data = skuStockNumResponse.getData();
                        Long skuId = data.getSkuId();
                        // 占用库存
                        Long preholdStockNum = data.getPreholdStockNum();
                        SkuStockNumDto skuStockNumDto = new SkuStockNumDto();
                        skuStockNumDto.setOutSkuId(skuId);
                        skuStockNumDto.setStockNum(Math.toIntExact(preholdStockNum));
                        skuStockNumDto.setOuterItemId(platformItemSku.getOuterItemId());
                        skuStockNumDtoList.add(skuStockNumDto);
                    }
                } catch (Exception e) {
                    log.error("queryBatchSkuStock error", e);
                } finally {
                    semaphore.release();
                    countDownLatch.countDown();
                }
            });
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                log.error("queryBatchSkuStock await error", e);
            }
        });

        DouDianSkuStockResp douDianSkuStockResp = new DouDianSkuStockResp();
        douDianSkuStockResp.setSkuStockNumDtoList(skuStockNumDtoList);
        douDianSkuStockResp.setTotal(page.getTotal());
        douDianSkuStockResp.setPageSize(page.getPageSize());
        douDianSkuStockResp.setPageIndex(page.getPageNum());
        return douDianSkuStockResp;
    }

    @Override
    public SkuSyncStockBatchResponse syncStockBatch(String token, Long productId, List<DouDianSkuStockParam> paramList) {
        SkuSyncStockBatchRequest request = new SkuSyncStockBatchRequest();
        SkuSyncStockBatchParam param = request.getParam();
        param.setProductId(productId);
        param.setIncremental(false);

        List<SkuSyncListItem> skuSyncListItemList = paramList.stream().map(val -> {
            SkuSyncListItem skuSyncListItem = new SkuSyncListItem();
            skuSyncListItem.setSkuId(val.getDouDianSkuId());
            skuSyncListItem.setSkuType(Long.valueOf(val.getSkuType()));
            skuSyncListItem.setStockNum(val.getStockNum());
            skuSyncListItem.setStepStockNum(val.getStepStockNum());
            return skuSyncListItem;
        }).collect(Collectors.toList());
        param.setSkuSyncList(skuSyncListItemList);
        final SkuSyncStockBatchResponse skuSyncStockBatchResponse = request.execute(AccessToken.wrap(token, ""));
        if (skuSyncStockBatchResponse.isSuccess()) {
            log.info("[抖店][库存推送][推送成功]{}", param);
        } else {
            log.error("[抖店][库存推送][推送失败]{} <<{}<<", param, skuSyncStockBatchResponse);
        }
        return skuSyncStockBatchResponse;
    }

    @Override
    public ProductListV2Response batchQueryProduct(String shopId, Long pageIndex, Long pageSize) {
        ProductListV2Request request = new ProductListV2Request();
        ProductListV2Param param = request.getParam();
        param.setPage(pageIndex);
        param.setSize(pageSize);
        return request.execute(common.getAccessToken(shopId));

    }

    @Override
    public DoudianProductWithSkuListResp queryProductWithSkuList(String shopId, long current, long size) {
        final ProductListV2Response productListV2Response = batchQueryProduct(shopId, current, size);
        final ThreadPoolTaskExecutor threadPool = ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL);

        final List<com.doudian.open.api.product_listV2.data.DataItem> products = Optional
                .ofNullable(productListV2Response)
                .map(DoudianOpResponse::getData)
                .map(ProductListV2Data::getData)
                .orElseGet(Collections::emptyList);
        if (products.isEmpty()) {
            return null;
        }

        final ArrayList<CompletableFuture<?>> cfs = new ArrayList<>();
        final List<DoudianProductWithSkuListVO> doudianProductWithSkuListVOS = products.stream().map(v -> {
            final DoudianProductWithSkuListVO doudianProductWithSkuListVO = new DoudianProductWithSkuListVO();
            doudianProductWithSkuListVO.setProduct(v);
            if (v != null) {
                cfs.add(CompletableFuture.runAsync(() -> {
                            final SkuListResponse skuListResponse = querySkuList(shopId, v.getProductId());
                            final List<com.doudian.open.api.sku_list.data.DataItem> dataItems = Optional.ofNullable(skuListResponse)
                                                                                                        .map(DoudianOpResponse::getData)
                                                                                                        .orElseGet(Collections::emptyList);
                            doudianProductWithSkuListVO.setSkuList(dataItems);
                        },
                        threadPool));
            }
            return doudianProductWithSkuListVO;
        }).collect(Collectors.toList());
        CompletableFuture.allOf(cfs.stream().filter(Objects::nonNull).toArray(CompletableFuture[]::new)).join();
        final DoudianProductWithSkuListResp doudianProductWithSkuListResp = new DoudianProductWithSkuListResp();
        doudianProductWithSkuListResp.setProducts(doudianProductWithSkuListVOS);
        doudianProductWithSkuListResp.setTotal(productListV2Response.getData().getTotal());
        doudianProductWithSkuListResp.setPageSize((int) size);
        doudianProductWithSkuListResp.setPageIndex((int) current);

        return doudianProductWithSkuListResp;
    }

    @Override
    public DoudianProductWithSkuListResp queryProductWithSkuList(String shopId, LocalDateTime updateTimeStart, LocalDateTime updateTimeEnd, long current, long size) {
        ProductListV2Request request = new ProductListV2Request();
        ProductListV2Param param = request.getParam();
        param.setUpdateStartTime(updateTimeStart.atZone(ZoneId.systemDefault()).toEpochSecond());
        param.setUpdateEndTime(updateTimeEnd.atZone(ZoneId.systemDefault()).toEpochSecond());
        param.setPage(current);
        param.setSize(size);
        final ProductListV2Response productListV2Response = request.execute(common.getAccessToken(shopId));

        final List<com.doudian.open.api.product_listV2.data.DataItem> products = Optional
                .ofNullable(productListV2Response)
                .map(DoudianOpResponse::getData)
                .map(ProductListV2Data::getData)
                .orElseGet(Collections::emptyList);
        if (products.isEmpty()) {
            return null;
        }

        final ThreadPoolTaskExecutor threadPool = ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL);

        final ArrayList<CompletableFuture<?>> cfs = new ArrayList<>();
        final List<DoudianProductWithSkuListVO> doudianProductWithSkuListVOS = products.stream().map(v -> {
            final DoudianProductWithSkuListVO doudianProductWithSkuListVO = new DoudianProductWithSkuListVO();
            doudianProductWithSkuListVO.setProduct(v);
            if (v != null) {
                cfs.add(CompletableFuture.runAsync(() -> {
                            final SkuListResponse skuListResponse = querySkuList(shopId, v.getProductId());
                            final List<com.doudian.open.api.sku_list.data.DataItem> dataItems = Optional.ofNullable(skuListResponse)
                                                                                                        .map(DoudianOpResponse::getData)
                                                                                                        .orElseGet(Collections::emptyList);
                            doudianProductWithSkuListVO.setSkuList(dataItems);
                        },
                        threadPool));
            }

            return doudianProductWithSkuListVO;
        }).collect(Collectors.toList());
        CompletableFuture.allOf(cfs.stream().filter(Objects::nonNull).toArray(CompletableFuture[]::new)).join();
        final DoudianProductWithSkuListResp doudianProductWithSkuListResp = new DoudianProductWithSkuListResp();
        doudianProductWithSkuListResp.setProducts(doudianProductWithSkuListVOS);
        doudianProductWithSkuListResp.setTotal(productListV2Response.getData().getTotal());
        doudianProductWithSkuListResp.setPageSize((int) size);
        doudianProductWithSkuListResp.setPageIndex((int) current);

        return doudianProductWithSkuListResp;
    }
}

