package com.daddylab.supplier.item.infrastructure.config.event;

import cn.hutool.extra.spring.SpringUtil;
import com.google.common.eventbus.AsyncEventBus;
import com.google.common.eventbus.EventBus;

public class EventBusUtil {
    public static void  post(Object event) {
        final Object syncEventBus = SpringUtil.getBean("syncEventBus");
        EventBus bus = (EventBus) syncEventBus;
        bus.post(event);
    }

    public static void post(Object event, boolean isAsync) {
        if (isAsync) {
            final Object syncEventBus = SpringUtil.getBean("asyncEventBus");
            AsyncEventBus bus = (AsyncEventBus) syncEventBus;
            bus.post(event);
        } else {
            post(event);
        }
    }
}
