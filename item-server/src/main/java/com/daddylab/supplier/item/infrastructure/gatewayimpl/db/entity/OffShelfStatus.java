package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum OffShelfStatus implements IIntegerEnum {

    /**
     * 0待提交，1待审核，11已撤回，2待处理，3已完成，31已拒绝
     */
    WAIT_SUBMIT(0, "待提交"),
    WAIT_AUDIT(1, "待审核"),
    RETRACT(11, "已撤回"),
    WAIT_HANDLE(2, "待处理"),
    FINISH(3, "已完成"),
    REFUSE(31, "已拒绝");


    @EnumValue
    private final Integer value;
    private final String desc;


}
