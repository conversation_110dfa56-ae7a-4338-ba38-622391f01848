package com.daddylab.supplier.item.controller.purchase.dto;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * <AUTHOR>
 * @ClassName PurchaseQueryCmd.java
 * @description
 * @createTime 2021年11月11日 17:23:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("采购价格分页查询请求实体")
public class PurchaseQueryPage extends PageQuery {

    private static final long serialVersionUID = -2066394722903442194L;

    @ApiModelProperty(value = "月份")
    private String month;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "商品sku")
    private String itemSku;

    @ApiModelProperty(value = "供应商")
    private String provider;

    @ApiModelProperty(value = "采购负责人")
    private Long buyerId;

    private Long currentUserId;

    @ApiModelProperty(value = "优惠类型 0:货抵款 1:按时间供价 2:当月优惠件数")
    private Integer favourableType;

    @ApiModelProperty(value = "是否纯活动商品 0:否 1:是")
    private Integer isActive;

    @ApiModelProperty(value = "平台名称 0:ALL 1:淘宝 2:抖店 3:小红书 4:自研电商 5:有赞 6:快手")
    private Integer platformType;

    @ApiModelProperty(value = "方式 0:大促 1:直播 2:无")
    private Integer activeType;

    @ApiModelProperty(value = "0:待确认 1:已确认 2:存在异议")
    private Integer status;

    private Boolean showAll;

    private Long offsetVal;

    @ApiModelProperty(value = "合作方。0:电商。1:绿色家装。")
    private List<Integer> corpType;

    @ApiModelProperty(value = "业务类型")
    private List<Integer> bizType;
}

