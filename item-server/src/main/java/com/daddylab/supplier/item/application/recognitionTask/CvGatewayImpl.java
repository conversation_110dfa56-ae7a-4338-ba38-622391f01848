package com.daddylab.supplier.item.application.recognitionTask;

import com.daddylab.supplier.item.infrastructure.cv.CVUtil;
import com.microsoft.azure.cognitiveservices.vision.computervision.ComputerVisionClient;
import com.microsoft.azure.cognitiveservices.vision.computervision.models.ReadOperationResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import rx.Single;

/**
 * <AUTHOR>
 * @since 2022/12/8
 */
@Service
@RequiredArgsConstructor
public class CvGatewayImpl implements CvGateway {
    private final ComputerVisionClient computerVisionClient;

    @Override
    public Single<ReadOperationResult> imageVision(String url) {
        return CVUtil.imageVision(computerVisionClient, url);
    }

    @Override
    public Single<ReadOperationResult> imageVision(byte[] data) {
        return CVUtil.imageVision(computerVisionClient, data);
    }
}
