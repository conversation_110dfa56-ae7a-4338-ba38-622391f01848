package com.daddylab.supplier.item.domain.otherPayDetail.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName PayType.java
 * @description 其他应付类型枚举
 * @createTime 2022年03月25日 15:29:00
 */
@Getter
@AllArgsConstructor
public enum PayType implements IEnum<Integer> {
    TOTAL(0, "合计"),
    FREIGHT(1, "运费"),
    AFTER_SALES(2, "售后"),
    TIKTOK_FINE(3, "抖音扣款"),
    SELLER_FINE(4, "商家处罚"),
    OTHER(5,"其他");
    final public Integer value;
    final public String desc;
}
