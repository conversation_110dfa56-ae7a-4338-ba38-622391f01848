package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Role;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.RoleMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IRoleService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 角色 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-18
 */
@Service
@DS("authDb")
public class RoleServiceImpl extends DaddyServiceImpl<RoleMapper, Role> implements IRoleService {

}
