package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> up
 * @date 2022年10月18日 11:30 AM
 */
@Getter
@AllArgsConstructor
public enum AfterSalesReceiveResult implements IEnum<Integer> {

    /**
     * 不要下划线
     */
    UNKNOWN(0, "未知"),
    GOOD(1, "货品完好"),
    SLIGHT(2, "轻微可入库"),
    BROKEN(3, "破损不可入库"),
    QUANTITY_WRONG(4, "数量不一致"),
    ITEM_WRONG(5, "商品不一致");

    @EnumValue
    private final Integer value;
    private final String desc;
}
