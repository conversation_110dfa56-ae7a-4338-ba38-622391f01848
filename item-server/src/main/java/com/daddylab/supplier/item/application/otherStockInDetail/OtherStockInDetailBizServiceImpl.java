package com.daddylab.supplier.item.application.otherStockInDetail;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockInDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OtherStockInDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOtherStockInDetailService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName OtherStockInDetailServiceImpl.java
 * @description
 * @createTime 2022年03月30日 17:03:00
 */
@Slf4j
@Service
public class OtherStockInDetailBizServiceImpl implements OtherStockInDetailBizService {

    @Autowired
    private IOtherStockInDetailService iOtherStockInDetailService;

    @Autowired
    private OtherStockInDetailMapper otherStockInDetailMapper;

    @Autowired
    private ItemGateway itemGateway;

    @Autowired
    private ItemSkuGateway itemSkuGateway;


    @Override
    public List<Long> getByItemId(Long itemId) {
        QueryWrapper<OtherStockInDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(Objects.nonNull(itemId),OtherStockInDetail::getItemId,itemId);
        return iOtherStockInDetailService.list(queryWrapper).stream().map(OtherStockInDetail::getOrderId).collect(Collectors.toList());

    }

    @Override
    public List<Long> getByBrandId(Long brandId) {
        QueryWrapper<OtherStockInDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(Objects.nonNull(brandId),OtherStockInDetail::getBrandId,brandId);
        return iOtherStockInDetailService.list(queryWrapper).stream().map(OtherStockInDetail::getOrderId).collect(Collectors.toList());
    }

    @Override
    public List<OtherStockInDetail> getByOrderId(Long id) {
        LambdaQueryWrapper<OtherStockInDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OtherStockInDetail::getOrderId,id);
        return iOtherStockInDetailService.list(queryWrapper);
    }

    @Override
    public List<Long> getIdsByItem(Long sku, String code, Long itemId, Long brandId, Collection<Integer> businessLines) {
        String skuCode = "";
        Optional<ItemSku> itemSku = itemSkuGateway.getByItemSkuId(sku);
        if (itemSku.isPresent()){
            skuCode = itemSku.get().getSkuCode();
        }

        return otherStockInDetailMapper.getIdsByItem(skuCode, code, itemId, brandId, businessLines);
    }


}
