package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Spu;

/**
 * <p>
 * 标准销售单元（由于金蝶缺少SPU，导致我们存在大量历史商品被拆分为"散装"的SKU，这张表的目的就是将这些散装的SKU重新聚合） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
public interface ISpuService extends IDaddyService<Spu> {

}
