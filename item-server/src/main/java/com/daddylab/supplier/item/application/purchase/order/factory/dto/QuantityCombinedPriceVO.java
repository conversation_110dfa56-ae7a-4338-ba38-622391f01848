package com.daddylab.supplier.item.application.purchase.order.factory.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022年10月25日 5:23 PM
 */
@Data
public class QuantityCombinedPriceVO {

    /**
     * 满足条件的价格构成,价格编码code:此价格编码的响应数量
     */
    private List<SpecVO> specList;

    /**
     * 满足贪心算原则的价格构成解的价格金额最大值。
     */
    private BigDecimal greedMaxAmount;

    /**
     * 请求数量
     */
    private Integer reqQuantity;


    @Data
    @AllArgsConstructor
    public static class SpecVO {
        private QuantityCombinedPriceBO priceBo;
        private Integer caseQuantity;
    }


}
