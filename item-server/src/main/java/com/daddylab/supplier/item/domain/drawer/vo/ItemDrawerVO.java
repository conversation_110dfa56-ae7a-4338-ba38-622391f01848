package com.daddylab.supplier.item.domain.drawer.vo;

import cn.hutool.core.collection.CollectionUtil;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.LaunchItemType;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.itemDrawer.ItemDrawerModuleInfoVO;
import com.daddylab.supplier.item.types.recognitionTask.ItemDrawerRecognitionResults;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.javers.core.metamodel.annotation.PropertyName;

/**
 * Class  ItemDrawerForm
 *
 * @Date 2022/5/31下午4:18
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ItemDrawerVO", description = "商品抽屉VO")
public class ItemDrawerVO implements Serializable {

    @ApiModelProperty(value = "抽屉id")
    private Long id;

    @ApiModelProperty(value = "商品ID")
    private Long itemId;

    @ApiModelProperty(value = "采购品名")
    private String itemName;

    @ApiModelProperty(value = "关联供应商系统商品款号")
    private String partnerProviderItemSn;

    @ApiModelProperty(value = "产品标准名")
    private String standardName;

    @ApiModelProperty(value = "淘宝标题")
    private String tbTitle;

    @ApiModelProperty(value = "小程序标题")
    private String miniTitle;

    @ApiModelProperty(value = "抖音标题")
    private String douTitle;

    @ApiModelProperty(value = "首页文案")
    private String homeCopy;

    @ApiModelProperty(value = "上架计划id")
    private Long planId;

    @ApiModelProperty(value = "上架计划名称")
    private String planName;

    @ApiModelProperty(value = "上架计划时间")
    private String launchDate;

    @ApiModelProperty("上新计划合作模式")
    private Integer launchBusinessLine;

    @ApiModelProperty(value = "商品流程状态 1-待选择 2-待完善 3-待设计 4-待审核 5-待上架 6-待修改 7-已上架 8-已下架")
    private Integer itemLaunchStatus;

    @ApiModelProperty(value = "商品图片")
    private List<ItemDrawerImageVO> images;

    @ApiModelProperty(value = "商品图片（其他尺寸）")
    private List<ItemDrawerImageVO> otherImages;

    @ApiModelProperty(value = "详情图片")
    private List<ItemDrawerImageVO> detailImages;

    @ApiModelProperty(value = "主图视频")
    private List<ItemDrawerImageVO> mainImageVideo;

    @ApiModelProperty(value = "规格图片")
    private List<ItemDrawerSkuImage> skuImages;

    @ApiModelProperty(value = "是否添加规格图片")
    @JsonProperty
    public Boolean getSkuImagesAdded() {
        return skuImages != null && skuImages.stream()
                .anyMatch(v -> StringUtil.isNotEmpty(v.getUrl()));
    }

    @ApiModelProperty(value = "属性图片")
    private List<ItemDrawerAttrImages> attrImages;

    @ApiModelProperty(value = "是否添加属性图片")
    @JsonProperty
    public Boolean getAttrImagesAdded() {
        return attrImages != null && attrImages.stream().anyMatch(
                ItemDrawerAttrImages::getImagesAdded);
    }

    @ApiModelProperty(value = "流程详情iframe地址")
    private String processIframeUrl;

    @ApiModelProperty(value = "产品负责人")
    private Long principalId;

    @ApiModelProperty(value = "采购负责人")
    private Long buyerId;

    @ApiModelProperty(value = "QC负责人Ids")
    private String qcIds;

    @ApiModelProperty(value = "采购负责人")
    private DadStaffVO buyer;

    @ApiModelProperty(value = "产品负责人")
    private DadStaffVO principal;

    @ApiModelProperty(value = "QC负责人Ids")
    private List<DadStaffVO> qcs = Collections.emptyList();

    @ApiModelProperty(value = "商品上新进度节点信息")
    private List<ItemLaunchProgressNode> launchProcessNodes;

//    @ApiModelProperty(value = "直播话术进度节点信息")
//    private List<ItemLaunchProgressNode> progressNodesForLiveVerbalTrick;

    @JsonProperty
    @ApiModelProperty(value = "0:非待审核状态 1:法务审核 2:QC审核")
    public Integer getAuditStatus() {
        final Integer itemLaunchStatus = getItemLaunchStatus();
        if (!ItemLaunchStatus.TO_BE_AUDITED.getValue().equals(
                itemLaunchStatus) || CollectionUtil.isEmpty(launchProcessNodes)) {
            return 0;
        }
        final List<ItemLaunchProgressNode> auditNodes = launchProcessNodes.stream()
                .filter(v -> StringUtil.contain(v.getTitle(), "审核"))
                .collect(Collectors.toList());
        final ItemLaunchProgressNode legalAuditNode = auditNodes.stream().findFirst().orElse(null);
        //法务审核没有发完成就是法务审核，已完成就是QC审核
        return legalAuditNode != null && legalAuditNode.getOk() ? 2 : 1;
    }

    @ApiModelProperty("抖音链接")
    private String douLink;

    @ApiModelProperty("淘宝链接")
    private String tbLink;

    @ApiModelProperty("小程序链接")
    private String wechatLink;

    @ApiModelProperty("小红书链接")
    private String miniRedBookLink;

    @ApiModelProperty("商品类型 INNER 内部 OUTSOURCE 外包")
    private LaunchItemType type;

//    @ApiModelProperty("直播话术")
//    private String liveVerbalTrick;
//
//    @ApiModelProperty("直播图片")
//    private List<ItemDrawerImageVO> liveImages;

    @ApiModelProperty("商品抽屉模块信息")
    private List<ItemDrawerModuleInfoVO> modules;

    @ApiModelProperty("当前状态是否可撤回")
    private Boolean canRollback = false;

//    @ApiModelProperty("当前直播话术状态是否可撤回")
//    private Boolean canRollbackOfLiveVerbalTrick = false;

    @ApiModelProperty("识别结果（可能为 NULL）")
    private ItemDrawerRecognitionResults recognitionResults;

//    @ApiModelProperty("直播话术模块信息")
//    private List<LiveVerbalTrickModuleInfo> liveVerbalTrickModuleInfos;

    @ApiModelProperty("待修改提交状态 1:产品组已提交，等待协同用户提交 2:协同用户已提交，等待产品组提交")
    private Integer launchSubmitStatus;

    @ApiModelProperty("待修改阶段协同用户")
    private DadStaffVO coopUser;

    @ApiModelProperty("是否展示商品拓展参数信息")
    private Boolean showExtendInfo;

    @ApiModelProperty("抖音链接内容")
    private String douYinLinkContent;

    @ApiModelProperty("快手链接内容")
    private String kuaiShouLinkContent;

    @ApiModelProperty("直播话术相关信息")
    private List<LiveVerBalTrickVO> liveVerBalTrickVos;

    @ApiModelProperty(value = "合并上新商品ID")
    private List<Long> mergeItemIds = Collections.emptyList();

    @ApiModelProperty(value = "合并上新商品信息")
    private List<SimpleItemVo> mergeItemList = Collections.emptyList();

    @ApiModelProperty(value = "原始商品ID")
    private Long rawItemId;

    @ApiModelProperty(value = "合作方-业务类型")
    private List<CorpBizTypeDTO> corpBizType;

    @PropertyName("商品下架时间")
    private Long downFrameTime;

    @PropertyName("商品下架原因")
    private String downFrameReason;

    @ApiModelProperty(value = "是否可还原到下架前状态")
    private boolean canRecoverBeforeStatus;
}
