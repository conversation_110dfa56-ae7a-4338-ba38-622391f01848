package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 旺店通预入库单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtPreStockInOrderDetails implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 入库单号
     */
    private String stockinNo;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 货品编号
     */
    private String goodsNo;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 商家编码
     */
    private String specNo;

    /**
     * 入库数量
     */
    private BigDecimal num;

    /**
     * 辅助数量
     */
    private BigDecimal num2;

    /**
     * 预期数量
     */
    private BigDecimal expectNum;

    /**
     * 有效期
     */
    private String expireDate;

    /**
     * 生产日期
     */
    private LocalDateTime productionDate;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 总重量
     */
    private BigDecimal goodsWeight;

    /**
     * 是否残次品
     */
    private Boolean defect;

    /**
     * 单位换算系数
     */
    private BigDecimal unitRatio;

    /**
     * 保质期(天数)
     */
    private Integer validityDays;

    /**
     * 需要质检的数量
     */
    private BigDecimal needInspectNum;

    /**
     * 辅助单位名称
     */
    private String unitName;

    /**
     * 辅助单位名称
     */
    private String auxUnitName;


}
