package com.daddylab.supplier.item.controller.item.dto.partner;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @ClassName PartnerPersonCmd.java
 * @description
 * @createTime 2022年05月31日 17:11:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("查修合作伙伴系统的人员，请求参数")
public class PartnerPersonCmd  extends Command {
    private static final long serialVersionUID = -3241030148800380802L;

    @ApiModelProperty("搜索类型 1 采购负责人 2 QC负责人 3 技术负责人")
    private Integer role_type;
}
