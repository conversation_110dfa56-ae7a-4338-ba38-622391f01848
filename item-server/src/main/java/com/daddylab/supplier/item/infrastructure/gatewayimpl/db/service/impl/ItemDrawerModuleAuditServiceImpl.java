package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAudit;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerModuleAuditMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerModuleAuditService;
import lombok.NonNull;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 商品库抽屉模块审核 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
@Service
public class ItemDrawerModuleAuditServiceImpl extends
        DaddyServiceImpl<ItemDrawerModuleAuditMapper, ItemDrawerModuleAudit> implements
        IItemDrawerModuleAuditService {


    public ItemDrawerModuleAudit getItemDrawerModuleAuditCreateIfNotExits0(ItemAuditType type, Long liveVerbalTrickId, Long itemId) {
        final LambdaQueryChainWrapper<ItemDrawerModuleAudit> queryWrapper = lambdaQuery()
                .eq(ItemDrawerModuleAudit::getItemId, itemId)
                .eq(ItemDrawerModuleAudit::getType, type);
        if (ItemAuditType.LIVE_VERBAL_TRICK.equals(type)) {
            if (Objects.nonNull(liveVerbalTrickId) && liveVerbalTrickId != 0) {
                queryWrapper.eq(ItemDrawerModuleAudit::getLiveVerbalTrickId, liveVerbalTrickId);
            } else {
                queryWrapper.isNull(ItemDrawerModuleAudit::getLiveVerbalTrickId);
            }
        }
        final Optional<ItemDrawerModuleAudit> moduleAuditOptional = queryWrapper
                .oneOpt();
        return moduleAuditOptional.orElseGet(() -> {
            final ItemDrawerModuleAudit v = new ItemDrawerModuleAudit();
            v.setItemId(itemId);
            v.setType(type);
            v.setRound(0);
            v.setAuditStatus(ItemAuditStatus.NONE);
            v.setBasicInfo(false);
            v.setImgInfo(false);
            v.setTextAndAttr(false);
            v.setLiveVerbalTrick(false);
            v.setLiveVerbalTrickId(liveVerbalTrickId);
            save(v);
            return v;
        });
    }

    @Override
    public ItemDrawerModuleAudit getItemDrawerModuleAuditCreateIfNotExits(ItemAuditType type, Long liveVerbalTrickId, Long itemId) {
        return getItemDrawerModuleAuditCreateIfNotExits0(type, liveVerbalTrickId, itemId);
    }

    @Override
    public ItemDrawerModuleAudit getItemDrawerModuleAuditCreateIfNotExits(ItemAuditType type, Long itemId) {
        return getItemDrawerModuleAuditCreateIfNotExits0(type, null, itemId);
    }

    @Override
    public @NonNull Optional<ItemDrawerModuleAudit> getItemDrawerModuleAudit(ItemAuditType type, Long itemId) {
        return getItemDrawerModuleAudit0(type, null, itemId);
    }

    @Override
    public Optional<ItemDrawerModuleAudit> getItemDrawerModuleAudit(ItemAuditType type, Long liveVerbalTrickId, Long itemId) {
        return getItemDrawerModuleAudit0(type, liveVerbalTrickId, itemId);

    }

    private Optional<ItemDrawerModuleAudit> getItemDrawerModuleAudit0(ItemAuditType type, Long liveVerbalTrickId, Long itemId) {
        if (Objects.nonNull(liveVerbalTrickId) && liveVerbalTrickId != 0) {
            return lambdaQuery()
                    .eq(ItemDrawerModuleAudit::getItemId, itemId)
                    .eq(ItemDrawerModuleAudit::getLiveVerbalTrickId, liveVerbalTrickId)
                    .eq(ItemDrawerModuleAudit::getType, type)
                    .oneOpt();
        }
        return lambdaQuery()
                .eq(ItemDrawerModuleAudit::getItemId, itemId)
                .isNull(ItemDrawerModuleAudit::getLiveVerbalTrickId)
                .eq(ItemDrawerModuleAudit::getType, type)
                .oneOpt();
    }
}
