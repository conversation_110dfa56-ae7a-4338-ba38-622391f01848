package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* <p>
    * 订单物流异常客服表
    * </p>
*
* <AUTHOR>
* @since 2025-01-02
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    public class OrderLogisticsAbnormalityCs implements Serializable {

    private static final long serialVersionUID = 1L;

            /**
            * id
            */
            @TableId(value = "id", type = IdType.AUTO)
    private Long id;

            /**
            * 创建时间createAt
            */
            @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

            /**
            * 创建人updateUser
            */
            @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

            /**
            * 更新时间updateAt
            */
            @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

            /**
            * 更新人updateUser
            */
            @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;

            /**
            * 是否已删除
            */
        @TableLogic(value = "0", delval = "id")
    private Long isDel;

            /**
            * 删除时间
            */
            @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

            /**
            * 异常ID
            */
    private Long abnormalityId;

            /**
            * 客服ID
            */
    private Long csUserId;


}
