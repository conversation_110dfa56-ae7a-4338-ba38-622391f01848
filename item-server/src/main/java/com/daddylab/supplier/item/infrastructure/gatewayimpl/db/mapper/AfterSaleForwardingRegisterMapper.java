package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSaleForwardingRegister;

/**
 * <p>
 * 售后转寄登记 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
public interface AfterSaleForwardingRegisterMapper extends DaddyBaseMapper<AfterSaleForwardingRegister> {

}
