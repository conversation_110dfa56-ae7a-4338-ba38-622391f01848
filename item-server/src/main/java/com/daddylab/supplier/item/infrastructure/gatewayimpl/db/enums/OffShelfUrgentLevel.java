package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/12/11
 */

@RequiredArgsConstructor
@Getter
public enum OffShelfUrgentLevel implements IIntegerEnum {
    EMERGENCY(1, "十分紧急"),
    NERVOUS(2, "紧急"),
    NORMAL(3, "一般"),
    ;
    private final Integer value;
    private final String desc;
}
