package com.daddylab.supplier.item.application.platformItem.tasks;

import cn.hutool.core.util.StrUtil;
import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.domain.platformItem.service.PlatformItemSyncService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemSkuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * 检查平台商品已失效任务（长时间未更新的商品说明可能在平台已删除，做删除处理）
 *
 * <AUTHOR>
 * @since 2024/4/19
 */
@Service
@Slf4j
public class CheckPlatformItemInvalidTask {

    @Autowired
    private IPlatformItemService platformItemService;
    @Autowired
    private IPlatformItemSkuService platformItemSkuService;

    @Autowired
    private PlatformItemSyncService platformItemSyncService;

    @XxlJob("CheckPlatformItemInvalidTask:check")
    public void check() {
        int days = 21;
        final String jobParam = XxlJobHelper.getJobParam();
        if (StrUtil.isNotBlank(jobParam)) {
            days = Integer.parseInt(jobParam);
        }
        final Long invalidTime = LocalDateTime.now().minusDays(days)
                                              .atZone(ZoneId.systemDefault()).toEpochSecond();
        log.info("[检查平台商品失效任务]开始执行，任务参数: days={} invalidTime={}", days, invalidTime);

        final int num = platformItemSyncService.deleteInvalidPlatformItem(null, invalidTime);

        log.info("[检查平台商品失效任务]执行完成，删除数量: {}", num);
    }
}
