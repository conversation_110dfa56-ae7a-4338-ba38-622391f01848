package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 淘宝订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class S01Order implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 有赞:订单号/淘宝:订单编号/抖店:主订单编号/快手:订单号
     */
    private String orderNo;

    /**
     * 有赞:订单状态/淘宝:订单状态/抖店:订单状态/快手:订单状态/享测小程序:订单状态
     */
    private String orderStatus;

    /**
     * 有赞:订单创建时间/淘宝:订单创建时间/抖店:订单创建时间/快手:订单创建时间/享测小程序:下单时间
     */
    private String createTime;

    /**
     * 有赞:买家付款时间/淘宝:订单付款时间/抖店:订单支付时间/快手:付款时间
     */
    private String payTime;

    /**
     * 有赞:归属店铺/淘宝:店铺名称
     */
    private String shopName;

    /**
     * 有赞:订单实付金额/淘宝:买家实际支付金额/抖店:实付款(订单实付金额+运费)/快手:实付款（货款不含运费）
     */
    private BigDecimal payAmount;

    /**
     * 有赞:收货人/淘宝:收货人姓名/抖店:收件人/快手:收件人/享测小程序:姓名
     */
    private String receiverName;

    /**
     * 有赞:收货人手机号/淘宝:联系手机/抖店:收件人手机号/快手:收件人电话/享测小程序:联系电话
     */
    private String receiverPhone;

    /**
     * 有赞:收货人省份/抖店:省/快手:省
     */
    private String receiverProvince;

    /**
     * 有赞:收货人城市/抖店:市/快手:市
     */
    private String receiverCity;

    /**
     * 有赞:收货人地区/抖店:区/快手:区
     */
    private String receiverRegion;

    /**
     * 有赞:详细收货地址/淘宝:收货地址/抖店:收件地址/快手:收件地址/享测小程序:详细地址
     */
    private String receiverAddr;

    /**
     * 有赞:买家姓名/淘宝:买家会员名
     */
    private String memberName;

    /**
     * 淘宝:总金额
     */
    private BigDecimal totalAmount;

    private LocalDateTime importTime;

    /**
     * 解密信息验证  0 解密正常，1 解密待验证
     */
    private Integer flag;

    /**
     * 是否需要发货，0 不用发货，1 正常发货
     */
    private Integer isDelivery;


}
