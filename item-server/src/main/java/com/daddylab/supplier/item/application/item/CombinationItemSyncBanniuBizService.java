package com.daddylab.supplier.item.application.item;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CombinationItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ComposeSku;

import io.swagger.annotations.ApiParam;

import java.util.List;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @since 2023/9/4
 */
public interface CombinationItemSyncBanniuBizService {
    void syncBatchByCombinationItemIds(@ApiParam("组合商品ID") @NotEmpty List<Long> combinationItemIds);

    void syncOneByCombinationItemId(Long combinationItemId);

    void syncOneByCombinationItemModel(
            CombinationItem combinationItem, List<ComposeSku> composeSkus);

    default void syncAll() {
        syncAll(Integer.MAX_VALUE);
    }

    void syncAll(int limit);
}
