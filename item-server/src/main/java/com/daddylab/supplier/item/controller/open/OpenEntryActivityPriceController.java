package com.daddylab.supplier.item.controller.open;

import com.alibaba.cola.dto.MultiResponse;
import com.daddylab.supplier.item.application.entryActivityPrice.OpenEntryActivityPriceService;
import com.daddylab.supplier.item.application.entryActivityPrice.models.OpenEntryActivityPriceQuery;
import com.daddylab.supplier.item.application.entryActivityPrice.models.OpenEntryActivityPriceVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/11/15
 */
@RestController
@RequestMapping("/open/entryActivityPrice")
public class OpenEntryActivityPriceController {
    @Resource
    private OpenEntryActivityPriceService openEntryActivityPriceService;

    @RequestMapping(value = "/query", method = RequestMethod.POST)
    public MultiResponse<OpenEntryActivityPriceVO> query(@RequestBody @Validated OpenEntryActivityPriceQuery query) {
        return MultiResponse.of(openEntryActivityPriceService.query(query));
    }
}
