package com.daddylab.supplier.item.application.message.param;

import com.alibaba.cola.dto.Command;
import com.daddylab.supplier.item.application.message.vo.MsgRecipientVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessageOperationType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 4:57 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("保存消息配置入参")
public class ConfigCmd extends Command {


    private static final long serialVersionUID = 561210357737526902L;

    @ApiModelProperty(value = "消息配置id", required = true)
    @NotNull(message = "消息配置id不得为空")
    private Long id;

    @ApiModelProperty(value = "消息配置是否生效", required = true)
    private Boolean canEffect;

    @ApiModelProperty(value = "操作类型", required = true)
    private MessageOperationType operationType;

    @ApiModelProperty(value = "是否系统默认推送", required = true)
    private Boolean pushDefault;

    @ApiModelProperty(value = "是否系统强提醒", required = true)
    private Boolean pushRemind;

    @ApiModelProperty(value = "是否短信推送", required = true)
    private Boolean pushText;

    @ApiModelProperty(value = "是否邮件推送", required = true)
    private Boolean pushMail;

    @ApiModelProperty(value = "消息接收者Id。不得超过100", required = true)
    @Size(max = 100)
    private List<MsgRecipientVO> recipients;

}
