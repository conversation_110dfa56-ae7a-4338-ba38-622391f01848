package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Additional;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AdditionalMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAdditionalService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 附件列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Service
public class AdditionalServiceImpl extends DaddyServiceImpl<AdditionalMapper, Additional> implements IAdditionalService {

}
