package com.daddylab.supplier.item.application.afterSalesRegister;


import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesRegister;

import lombok.Builder;
import lombok.Singular;
import lombok.ToString;
import lombok.Value;

import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @since 2023/8/24
 */
@Value
@Builder
@ToString(of = {"banniuSheet", "banniuField"})
public class FieldMapping<V, R> {
    String banniuSheet;
    String[] banniuField;
    Function<V, R> map;
    @Singular List<BiConsumer<AfterSalesRegister, R>> fieldSetters;
    Predicate<AfterSalesRegister> filter;

    public static FieldMappingBuilder<String, String> builderForStr(
            String banniuSheet, String... banniuField) {
        return new FieldMappingBuilder<String, String>()
                .map(Object::toString)
                .banniuSheet(banniuSheet)
                .banniuField(banniuField);
    }

    public static <V, R> FieldMappingBuilder<V, R> builderWithConvert(
            String banniuSheet, String... banniuField) {
        return new FieldMappingBuilder<V, R>().banniuSheet(banniuSheet).banniuField(banniuField);
    }

    public boolean testSheet(String banniuSheet) {
        return "*".equals(this.banniuSheet) || this.banniuSheet.equals(banniuSheet);
    }

    @SuppressWarnings("unchecked")
    public void set(AfterSalesRegister o, Object v) {
        if (filter != null && !filter.test(o)) {
            return;
        }
        final R mappedV = map.apply((V) v);
        if (mappedV == null) {
            return;
        }
        for (BiConsumer<AfterSalesRegister, R> fieldSetter : fieldSetters) {
            fieldSetter.accept(o, mappedV);
        }
    }
}
