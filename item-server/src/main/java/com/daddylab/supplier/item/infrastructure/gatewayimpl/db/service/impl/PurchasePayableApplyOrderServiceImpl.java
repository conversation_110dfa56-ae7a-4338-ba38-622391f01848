package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableApplyOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchasePayableApplyOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchasePayableApplyOrderService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 采购付款申请单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Service
public class PurchasePayableApplyOrderServiceImpl extends DaddyServiceImpl<PurchasePayableApplyOrderMapper, PurchasePayableApplyOrder> implements IPurchasePayableApplyOrderService {

}
