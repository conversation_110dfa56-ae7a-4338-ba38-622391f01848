package com.daddylab.supplier.item.controller.purchase.dto.order;

import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrderDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022/4/21 1:46 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("采购订单详情返回封装")
public class PurchaseOrderDetailVO extends PurchaseOrderDetail {

    private static final long serialVersionUID = -248353975578396410L;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("商品名称")
    private String itemName;

    @ApiModelProperty("实际入库数量")
    private Integer realStockInQuantity;

    @ApiModelProperty("申请入库数量")
    private Integer applyStockQuantity;

//    @ApiModelProperty("付款申请单数量")
//    private Integer paymentQuantity;

    private Long itemSkuId;

    @ApiModelProperty("合作方+业务类型")
    private List<CorpBizTypeDTO> corpBizType;

//    @ApiModelProperty("业务类型")
//    private List<Integer> bizType;

//    @ApiModelProperty("是否存在付款申请。1存在。0不存在")
//    private Integer isPayApplied;


}
