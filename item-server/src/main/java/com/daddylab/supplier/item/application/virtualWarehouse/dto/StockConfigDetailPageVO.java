/*
package com.daddylab.supplier.item.application.virtualWarehouse.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

*/
/**
 * <AUTHOR> up
 * @date 2024年03月01日 3:42 PM
 *//*

@Data
public class StockConfigDetailPageVO {
    */
/** 商品SKU *//*

    @ApiModelProperty("商品SKU")
    String skuCode;
    */
/** 商品SPU *//*

    @ApiModelProperty("商品SPU")
    String spuCode;
    */
/** 条码 *//*

    @ApiModelProperty("条码")
    String barCode;
    */
/** 商品SPU名称 *//*

    @ApiModelProperty("商品SPU名称")
    String spuName;
    */
/** 商品SKU名称 *//*

    @ApiModelProperty("商品SKU名称")
    String skuName;
    */
/** 品牌ID *//*

    @ApiModelProperty("品牌ID")
    Long brandId;
    */
/**
     * 品牌编号
     *//*

    @ApiModelProperty("品牌编号")
    String brandNo;
    */
/** 品牌 *//*

    @ApiModelProperty("品牌")
    String brandName;
    */
/** 品类ID *//*

    @ApiModelProperty("品类ID")
    Long categoryId;
    */
/** 品类 *//*

    @ApiModelProperty("品类")
    String categoryName;
    */
/** 合作模式 *//*

    @ApiModelProperty("合作模式")
    List<Integer> businessLine;
    */
/** 仓库ID *//*

    @ApiModelProperty("仓库ID")
    Long warehouseId;
    */
/** 仓库编码 *//*

    @ApiModelProperty("仓库编码")
    String warehouseNo;
    */
/** 仓库 *//*

    @ApiModelProperty("仓库")
    String warehouseName;

    */
/** 可发货库存 *//*

    @ApiModelProperty("可发库存(实仓)")
    BigDecimal availableSendStock;

    @ApiModelProperty("可发库存(虚拟仓)")
    BigDecimal availableSendStockVirtual;

    */
/** 可用库存 *//*

    @ApiModelProperty("可用库存(实仓)")
    BigDecimal availableStock;

    @ApiModelProperty("可用库存(虚拟仓)")
    BigDecimal availableStockVirtual;

    @ApiModelProperty("库存设置")
    Integer inventoryRatio;

    private Integer isVirtualWarehouse;



}
*/
