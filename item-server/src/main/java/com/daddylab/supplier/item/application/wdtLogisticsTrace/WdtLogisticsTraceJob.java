package com.daddylab.supplier.item.application.wdtLogisticsTrace;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.job.extend.autoRegister.XxlJobAutoRegister;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtLogisticsTrace;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtLogisticsTraceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/20
 */
@Component
@Slf4j
public class WdtLogisticsTraceJob {
  @Autowired private IWdtLogisticsTraceService wdtLogisticsTraceService;

  @XxlJob("WdtLogisticsTraceJob:removeDuplicateRecords")
  @XxlJobAutoRegister(cron = "0 0 0 1 1 ? 1970", jobDesc = "旺店通物流轨迹-移除重复记录", author = "徴乌")
  public void removeDuplicateRecords() {
    long cursorId = 0L;
    final HashSet<String> alreadyProcessed = new HashSet<>();
    final AtomicLong count = new AtomicLong();
    for (; ; ) {
      final List<WdtLogisticsTrace> list =
          wdtLogisticsTraceService
              .lambdaQuery()
              .gt(WdtLogisticsTrace::getId, cursorId)
              .orderByAsc(WdtLogisticsTrace::getId)
              .select(WdtLogisticsTrace::getId, WdtLogisticsTrace::getLogisticsNo)
              .last("limit 10000")
              .list();
      if (list.isEmpty()) {
        break;
      }
      cursorId = list.stream().map(WdtLogisticsTrace::getId).max(Long::compareTo).orElse(0L);
      for (WdtLogisticsTrace wdtLogisticsTrace : list) {
        count.incrementAndGet();
        final String logisticsNo = wdtLogisticsTrace.getLogisticsNo();
        if (alreadyProcessed.contains(logisticsNo)) {
          continue;
        }
        final List<WdtLogisticsTrace> tracesThisLogisticsNo =
            wdtLogisticsTraceService
                .lambdaQuery()
                .eq(WdtLogisticsTrace::getLogisticsNo, logisticsNo)
                .select(WdtLogisticsTrace::getId)
                .list();
        if (tracesThisLogisticsNo.size() > 1) {
          tracesThisLogisticsNo.stream()
              .max(Comparator.comparing(WdtLogisticsTrace::getId))
              .map(WdtLogisticsTrace::getId)
              .ifPresent(
                  maxId -> {
                    final List<Long> removeIds =
                        tracesThisLogisticsNo.stream()
                            .map(WdtLogisticsTrace::getId)
                            .collect(Collectors.toList());
                    removeIds.remove(maxId);
                    wdtLogisticsTraceService.removeByIds(removeIds);
                    log.debug("[旺店通物流轨迹-移除重复记录]已清理:{} 移除ID:{}", logisticsNo, removeIds);
                  });
        } else {
          log.debug("[旺店通物流轨迹-移除重复记录]无需清理:{}", logisticsNo);
        }
        alreadyProcessed.add(logisticsNo);
      }
    }
    log.debug("[旺店通物流轨迹-移除重复记录]清理完成，共计处理 {} 条记录，{} 个单号", count.get(), alreadyProcessed.size());
  }
}
