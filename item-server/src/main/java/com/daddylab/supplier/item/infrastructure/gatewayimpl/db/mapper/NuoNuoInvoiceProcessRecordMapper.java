package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NuoNuoInvoiceProcessRecord;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;

/**
 * <p>
 * 用户诺诺开票流程记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
public interface NuoNuoInvoiceProcessRecordMapper extends DaddyBaseMapper<NuoNuoInvoiceProcessRecord> {

}
