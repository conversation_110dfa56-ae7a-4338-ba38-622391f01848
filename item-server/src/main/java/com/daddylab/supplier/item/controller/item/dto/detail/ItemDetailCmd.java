package com.daddylab.supplier.item.controller.item.dto.detail;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/12 7:54 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("查询商品详情请求参数封装")
public class ItemDetailCmd extends Command {

    private static final long serialVersionUID = -1235292453447703694L;

    @ApiModelProperty("商品id")
    private Long id;
    @ApiModelProperty("商品编码")
    private String code;
    @ApiModelProperty("商品sku编号")
    private String skuCode;
    @ApiModelProperty("商品品牌id")
    private Long brandId;
    @ApiModelProperty("商品品类id")
    private Long categoryId;
    @ApiModelProperty("商品供应商id")
    private Long providerId;
    @ApiModelProperty("商品第三方关联编号")
    private String partnerCode;
}
