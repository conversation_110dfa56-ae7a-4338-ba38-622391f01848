package com.daddylab.supplier.item.infrastructure.winrobot360.types;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import java.lang.reflect.Field;
import java.util.ArrayList;
import lombok.SneakyThrows;

/**
 * <AUTHOR>
 * @since 2022/9/21
 */
public class TypeFactory {

    public static final ImmutableMap<Class<?>, ParamType> TYPE_MAPPING = ImmutableMap.<Class<?>, ParamType>builder()
            .put(CharSequence.class, ParamType.STR)
            .put(Integer.class, ParamType.INT)
            .put(Long.class, ParamType.INT)
            .put(Float.class, ParamType.FLOAT)
            .put(Double.class, ParamType.FLOAT)
            .put(Boolean.class, ParamType.BOOL)
            .build();

    @SneakyThrows
    public static TaskStartParam buildTaskStartParam(String scheduleUuid,
            String robotUuid, Object robotParamModel) {
        final TaskStartParam taskStartParam = new TaskStartParam();
        taskStartParam.setScheduleUuid(scheduleUuid);
        final ArrayList<ScheduleRelaParam> scheduleRelaParams = Lists.newArrayList();
        taskStartParam.setScheduleRelaParams(scheduleRelaParams);

        final ScheduleRelaParam scheduleRelaParam = new ScheduleRelaParam();
        scheduleRelaParam.setRobotUuid(robotUuid);
        scheduleRelaParams.add(scheduleRelaParam);

        final ArrayList<Param> robotParams = Lists.newArrayList();
        scheduleRelaParam.setParams(robotParams);

        for (Field field : ReflectUtil.getFields(robotParamModel.getClass())) {
            final Param param = new Param();
            param.setName(StrUtil.toUnderlineCase(field.getName()));
            final Object value = field.get(robotParamModel);
            final ParamType type = TYPE_MAPPING.getOrDefault(value.getClass(), ParamType.STR);
            param.setType(type.name().toLowerCase());
            if (type == ParamType.STR) {
                if (CharSequence.class.isAssignableFrom(field.getType())) {
                    param.setValue(value.toString());
                } else {
                    param.setValue(JsonUtil.toJson(value));
                }
            } else {
                param.setValue(value);
            }
            robotParams.add(param);
        }
        return taskStartParam;
    }
}
