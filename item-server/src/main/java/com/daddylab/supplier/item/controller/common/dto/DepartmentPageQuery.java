package com.daddylab.supplier.item.controller.common.dto;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> up
 * @date 2022/3/31 11:48 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DepartmentPageQuery extends PageQuery {

    private static final long serialVersionUID = -1619829724681020835L;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("状态。0全部。1.正常，2.停用")
    private Integer state;
}
