package com.daddylab.supplier.item.infrastructure.gatewayimpl.stockInOrder;

import com.daddylab.supplier.item.domain.stockInOrder.gateway.StockInOrderGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockInOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 采购入库网关实现
 * <AUTHOR>
 * @date 2022/3/24 17:36
 **/
@Component
public class StockInOrderGatewayGatewayImpl implements StockInOrderGateway {
    @Autowired
    private StockInOrderMapper stockInOrderMapper;
}
