package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/5/24 11:45
 * @description StockOutOrderSyncState
 */
@Getter
@AllArgsConstructor
public enum StockOutOrderSyncState {

    /**
     * 同步状态
     */
    SUCCESS(1, "同步成功"),

    WDT_ERROR(2, "同步旺店通失败"),

    KING_DEE_ERROR(3, "同步金蝶失败"),
    ;

    private final Integer value;
    private final String desc;



}
