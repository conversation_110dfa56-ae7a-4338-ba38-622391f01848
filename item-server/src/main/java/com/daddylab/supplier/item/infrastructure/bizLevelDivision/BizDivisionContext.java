package com.daddylab.supplier.item.infrastructure.bizLevelDivision;

import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BizUnionTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/8
 */
@Data
@Accessors(chain = true)
public class BizDivisionContext {
    /**
     * 业务类型枚举
     */
    private BizUnionTypeEnum type;

    /**
     * 业务ID字段名
     */
    private String bizIdRef;

    /**
     * 用户授权域
     */
    private Set<DivisionLevelValueEnum> scopes;

    /**
     * 参数选择域
     */
    private Set<DivisionLevelValueEnum> selectedScopes;

    public Set<DivisionLevelValueEnum> getEffectScopes() {
        final LinkedHashSet<DivisionLevelValueEnum> effectScopes = new LinkedHashSet<>();
        if (scopes == null) {
            return effectScopes;
        }
        if (selectedScopes != null) {
            boolean cooperationSelected = false;
            for (DivisionLevelValueEnum selectedScope : selectedScopes) {
                if (selectedScope == null) {
                    continue;
                }
                if (selectedScope.getDivisionLevel() == DivisionLevelEnum.COOPERATION) {
                    cooperationSelected = true;
                    if (scopes.contains(selectedScope)) {
                        effectScopes.add(selectedScope);
                    }
                } else {
                    effectScopes.add(selectedScope);
                }
            }
            if (!cooperationSelected) {
                effectScopes.addAll(scopes);
            }
        } else {
            effectScopes.addAll(scopes);
        }
        return effectScopes;
    }

    private static final ThreadLocal<BizDivisionContext> CONTEXT_HOLDER = new ThreadLocal<>();

    public static void setCurrentContext(BizDivisionContext bizDivisionContext) {
        CONTEXT_HOLDER.set(bizDivisionContext);
    }

    public static BizDivisionContext getCurrentContext() {
        return CONTEXT_HOLDER.get();
    }

    public static void removeCurrentContext() {
        CONTEXT_HOLDER.remove();
    }

    public static BizDivisionContext createContext(BizUnionTypeEnum type) {
        return new BizDivisionContext().setType(type);
    }

    public static <T> T invoke(BizUnionTypeEnum type, Supplier<T> invocation) {
        return invoke(createContext(type), invocation);
    }

    public static <T> T invoke(BizUnionTypeEnum type, UnaryOperator<BizDivisionContext> contextOp, Supplier<T> invocation) {
        return invoke(contextOp.apply(createContext(type)), invocation);
    }

    public static <T> T invoke(BizDivisionContext context, Supplier<T> invocation) {
        BizDivisionContext.setCurrentContext(context);
        try {
            return invocation.get();
        } finally {
            BizDivisionContext.removeCurrentContext();
        }
    }
}
