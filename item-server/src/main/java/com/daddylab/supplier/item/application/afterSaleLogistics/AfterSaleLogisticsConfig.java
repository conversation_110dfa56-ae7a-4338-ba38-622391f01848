package com.daddylab.supplier.item.application.afterSaleLogistics;

import cn.hutool.core.collection.ListUtil;

import java.util.List;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsTrace;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/5/22
 */
@ConfigurationProperties(prefix = "after-sale-logistics")
@Configuration
@RefreshScope
@Data
public class AfterSaleLogisticsConfig {
  /** 订单扫描起始时间 */
  private String scanStartTime = "2024-06-01 00:00:00";

  /** 最大跟踪订单数量限制，-1为不限制 */
  private int traceLimit = -1;

  /** [订单扫描任务]单次执行数量限制 */
  private int scanLimit = 100;

  /** [物流订阅任务]单次执行数量限制 */
  private int subscribeLimit = 100;

  /** 是否订阅小程序商城订单 */
  private boolean subscribeMallOrder;

  /** 回调超时，可能因为各种原因导致订阅失败，这里设置一个超时时间，如果超过这个时间还未收到物流信息，则认为物流信息订阅失败 */
  private int callbackTimeout = 172800;

  /** 延迟订阅最大等待时间（默认1天半） */
  private int delayTimeout = 129600;

  private String sendLimitTime = "16";

  private String oneDayLimit = "15";

  private String twoDaysLimit = "20";

  /** 过滤商品名称 */
  private List<String> filterGoodsNames = ListUtil.of("活动优惠券");

  /** 过滤商品编号 */
  private List<String> filterGoodsNos;

  /** 保税仓编码 虚拟链接仓、虚拟暂存仓、切换系统仓 */
  private List<String> bondedWarehouseCode = ListUtil.of("CK000348", "XNZC", "001");

  /** 疑难件筛选关键词 */
  private List<String> difficultShortStatus;

  /** 虽然派送中，但是可以转为待收件 判断关键词 */
  private List<String> receiveShortStatus;

  /** 启用旺店通物流轨迹查询 */
  private boolean wdtLogisticsTraceEnabled = true;

  /** 旺店通无法跟踪物流轨迹的物流公司 */
  private List<String> wdtLogisticsCompanyFilterList = ListUtil.of("韵达");

  /** 异常扫描线程数量 */
  private int abnormalScanThreads = 3;

  /** 跟踪超时时间，单位秒，长时间没有任何更新，直接关闭跟踪 */
  private int trackTimeout = 33 * 86400;

  public boolean isUseWdtLogisticsTrace(OrderLogisticsTrace orderLogisticsTrace) {
    return wdtLogisticsTraceEnabled
        && orderLogisticsTrace.getLogisticsName() != null
        && getWdtLogisticsCompanyFilterList().stream()
            .noneMatch(orderLogisticsTrace.getLogisticsName()::contains);
  }

  /**
   * 是否停止跟踪已完成的订单
   */
  private boolean stopTraceForOrderFinished;
}
