package com.daddylab.supplier.item.infrastructure.third.config;

import com.pdd.pop.sdk.http.PopAccessTokenClient;
import com.pdd.pop.sdk.http.PopHttpClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/4/17
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "pdd")
@RefreshScope
public class PddConfig {
    private String uri = "https://gw-api.pinduoduo.com/api/router";
    private String appid = "9eab18a6279b4b7d9626f430ad892c3d";
    private String secret = "4000f9d12b03e9f90bc3c32b4955ba26b7143403";

    public PopHttpClient getHttpClient() {
        return new PopHttpClient(appid, secret);
    }

    public PopAccessTokenClient getAccessTokenClient() {
        return new PopAccessTokenClient(appid, secret);
    }
}
