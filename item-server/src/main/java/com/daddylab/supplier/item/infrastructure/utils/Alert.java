package com.daddylab.supplier.item.infrastructure.utils;

import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.alert.IAlert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskRejectedException;

@Slf4j
public class Alert {

    private static IAlert alert = null;

    private static IAlert getAlert() {
        if (alert == null) {
            alert = ApplicationContextUtil.getBean("MessageRobotAlertAgentImpl", IAlert.class);
        }
        return alert;
    }

    /**
     * 发送普通文本消息
     *
     * @param target  发送到哪儿
     * @param message 消息体
     * @return 是否发送成功
     */
    public static boolean text(Object target, String message) {
        try {
            return getAlert().text(target, message);
        } catch (Exception e) {
            log.error("发送预警信息异常", e);
            return false;
        }
    }

    /**
     * 发送普通文本消息
     *
     * @param target  发送到哪儿
     * @param message 消息体
     * @param async   是否异步发送
     * @return 是否发送成功
     */
    public static boolean text(Object target, String message, Boolean async) {
        if (Boolean.TRUE.equals(async)) {
            try {
                ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
                    text(target, message);
                });
            } catch (TaskRejectedException exception) {
                log.error("预警提交至线程池失败");
                return false;
            }
            return true;
        } else {
            return text(target, message);
        }
    }

    /**
     * 发送Markdown消息
     *
     * @param target  发送到哪儿
     * @param message 消息体
     * @return 是否发送成功
     */
    public static boolean markdown(Object target, String message) {
        try {
            return getAlert().markdown(target, message);
        } catch (Exception e) {
            log.error("发送预警信息异常", e);
            return false;
        }
    }

    /**
     * 发送Markdown消息
     *
     * @param target  发送到哪儿
     * @param message 消息体
     * @param async   是否异步发送
     * @return 是否发送成功
     */
    public static boolean markdown(Object target, String message, Boolean async) {
        if (Boolean.TRUE.equals(async)) {
            try {
                ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
                    markdown(target, message);
                });
            } catch (TaskRejectedException exception) {
                log.error("预警提交至线程池失败");
                return false;
            }
            return true;
        } else {
            return markdown(target, message);
        }
    }
}
