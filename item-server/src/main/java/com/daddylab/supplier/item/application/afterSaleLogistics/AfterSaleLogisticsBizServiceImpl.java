package com.daddylab.supplier.item.application.afterSaleLogistics;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.daddylab.supplier.item.application.afterSaleLogistics.domain.LogisticsTraceConvert;
import com.daddylab.supplier.item.application.afterSaleLogistics.domain.LogisticsTraceData;
import com.daddylab.supplier.item.application.afterSaleLogistics.dto.*;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.AbnormalStatus;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.AbnormalityLogType;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.LogisticsTraceDataSource;
import com.daddylab.supplier.item.application.kuaidaoyun.KdyBizService;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.application.warehouse.WarehouseGateway;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.ResponseAssert;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.exportTask.ExportManager;
import com.daddylab.supplier.item.domain.order.OrderSensitiveInfo;
import com.daddylab.supplier.item.domain.order.OrderSensitiveInfoRepository;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.AbnormalityCountObj;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.AbnormalityListObj;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.CloseReasonType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.limit.RateLimiterAccess;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> up
 * @date 2024年05月23日 10:46 AM
 */
@Service
@Slf4j
public class AfterSaleLogisticsBizServiceImpl implements AfterSaleLogisticsBizService {

  @Resource IOrderLogisticsAbnormalityService iOrderLogisticsAbnormalityService;

  @Resource IKdyLogisticMappingService kdyLogisticMappingService;

  @Resource IOrderLogisticsAbnormalityLogService abnormalityLogService;

  @Resource IOrderLogisticsAbnormalityAlertService orderLogisticsAbnormalityAlertService;

  @Resource WarehouseGateway warehouseGateway;

  @Resource IWdtOrderDetailService wdtOrderDetailService;

  @Resource IWdtOrderService wdtOrderService;

  @Autowired private OrderSensitiveInfoRepository orderSensitiveInfoRepository;

  @Autowired IOrderLogisticsTraceService iOrderLogisticsTraceService;

  @Autowired RedissonClient redissonClient;

  @Autowired private LogisticsExceptionBizService logisticsExceptionBizService;

  @Autowired RateLimiterAccess rateLimiterAccess;

  @Autowired
  IWarehouseLogisticsAbnormalityStaticsService warehouseLogisticsAbnormalityStaticsService;

  @Autowired AfterSaleLogisticsConfig afterSaleLogisticsConfig;

  @Autowired KdyBizService kdyBizService;

  @Autowired IWdtLogisticsTraceService wdtLogisticsTraceService;

  @Autowired private IKdyCallbackService kdyCallbackService;

  @Autowired private AfterSaleLogisticsConfig config;

  @Autowired private IOrderLogisticsAbnormalityCsService orderLogisticsAbnormalityCsService;
  @Autowired private StaffService staffService;
  @Autowired private ExportManager exportManager;
  @Autowired private ShopGateway shopGateway;
  @Autowired private AbnormalScanService abnormalScanService;
  @Autowired private IWarehouseService iWarehouseService;

  @Override
  public Response distributeCS(DistributeCSRequest request) {
    final List<Long> csUserIds = request.getCsUserIds();
    final List<Long> ids = request.getIds();
    final List<OrderLogisticsAbnormality> orderLogisticsAbnormalities =
        iOrderLogisticsAbnormalityService.listByIds(ids);
    if (orderLogisticsAbnormalities.isEmpty()) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未找到指定物流异常记录");
    }
    for (OrderLogisticsAbnormality orderLogisticsAbnormality : orderLogisticsAbnormalities) {
      orderLogisticsAbnormalityCsService.distribute(orderLogisticsAbnormality.getId(), csUserIds);
    }
    return Response.buildSuccess();
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Response followUp(AfterSaleLogisticsFollowUpCmd cmd) {
    final ArrayList<Long> ids = new ArrayList<>();
    Optional.ofNullable(cmd.getId()).ifPresent(ids::add);
    Optional.ofNullable(cmd.getIds()).ifPresent(ids::addAll);
    Assert.notEmpty(ids, "请至少选中一条记录");
    for (Long id : ids) {
      final OrderLogisticsAbnormality abnormality = iOrderLogisticsAbnormalityService.getById(id);
      if (abnormality.getAbnormalStatus() == AbnormalStatus.CLOSED) {
        continue;
      }
      final String remark = Optional.ofNullable(cmd.getRemark()).orElse("无");

      abnormality.setAbnormalStatus(AbnormalStatus.FOLLOWING);
      final Integer handleCount = abnormality.getHandleCount();
      abnormality.setHandleCount(handleCount + 1);
      abnormality.setRemark(remark);
      iOrderLogisticsAbnormalityService.updateById(abnormality);

      final OrderLogisticsAbnormalityLog abnormalityLog = new OrderLogisticsAbnormalityLog();
      abnormalityLog.setAbnormalityId(id);
      abnormalityLog.setLogType(AbnormalityLogType.COUNT);
      abnormalityLog.setAbnormalType(0);
      abnormalityLog.setAbnormalType2(0);
      abnormalityLog.setMsg(String.format("第%s次跟进处理，备注：%s", abnormality.getHandleCount(), remark));
      abnormalityLogService.save(abnormalityLog);

      final Long abnormalityId = abnormality.getId();
      orderLogisticsAbnormalityCsService.distribute(
          abnormalityId, Collections.singletonList(UserContext.getUserId()), true);

      // 减少异常预警统计
      LocalDate today = LocalDate.now(ZoneId.systemDefault());
      String dayStr = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
      String key = "logistic_" + abnormality.getStockoutWarehouseNo() + "_" + dayStr;
      RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
      if (Objects.nonNull(atomicLong)) {
        final long l = atomicLong.decrementAndGet();
        if (l <= 0) {
          redissonClient.getKeys().delete(key);
        }
      }
      ;
    }
    return Response.buildSuccess();
  }

  @Override
  public Response export(AfterSaleLogisticsPageQuery query) {
    final LoadingCache<Long, Optional<Shop>> shopGetter =
        Caffeine.newBuilder().build(shopGateway::queryShopByIdOpt);
    exportManager.export(
        ExportTaskType.AFTER_SALES_LOGISTICS_ABNORMAL,
        AfterSaleLogisticsExportVO.class,
        new ExportManager.ExportHandler() {
          @Override
          public PageResponse<?> handle(int pageIndex) {
            query.setPageIndex(pageIndex);
            query.setPageSize(100);
            final List<OrderLogisticsAbnormality> orderLogisticsAbnormalities =
                abnormalityLogService.getDaddyBaseMapper().selectAbnormalityPOList(query);

            final List<Long> abnormalityIds =
                orderLogisticsAbnormalities.stream()
                    .map(OrderLogisticsAbnormality::getId)
                    .collect(Collectors.toList());
            final List<OrderLogisticsAbnormalityCs> abnormalityCsList =
                orderLogisticsAbnormalityCsService.listByAbnormalityIds(abnormalityIds);
            final List<Long> wdtTradeIds =
                orderLogisticsAbnormalities.stream()
                    .map(OrderLogisticsAbnormality::getWdtTradeId)
                    .collect(Collectors.toList());
            final List<WdtOrderDetail> wdtOrderDetails =
                wdtOrderDetailService.listByTradeIds(wdtTradeIds);

            final ArrayList<AfterSaleLogisticsExportVO> afterSaleLogisticsExportVOS =
                new ArrayList<>();
            for (OrderLogisticsAbnormality po : orderLogisticsAbnormalities) {
              final AfterSaleLogisticsExportVO afterSaleLogisticsExportVO =
                  new AfterSaleLogisticsExportVO();
              afterSaleLogisticsExportVO.setSrcOrderNo(po.getSrcOrderNo());
              final Platform platform = Platform.of(po.getPlatform());
              if (platform != null) {
                afterSaleLogisticsExportVO.setPlatform(platform.getDesc());
              }
              @SuppressWarnings("DataFlowIssue")
              final Optional<Shop> shop =
                  Optional.ofNullable(po.getShopId()).flatMap(shopGetter::get);
              afterSaleLogisticsExportVO.setShop(shop.map(Shop::getName).orElse(null));
              afterSaleLogisticsExportVO.setOrderTime(DateUtil.format(po.getTradeTime()));
              afterSaleLogisticsExportVO.setDeliveryTime(DateUtil.format(po.getConsignTime()));
              afterSaleLogisticsExportVO.setStockOutNo(po.getStockoutNo());
              afterSaleLogisticsExportVO.setStockOutWarehouse(po.getStockoutWarehouseNo());
              afterSaleLogisticsExportVO.setLogisticsCompanyName(po.getLogisticsCompanyName());
              afterSaleLogisticsExportVO.setLogisticsNo(po.getLogisticsNo());
              if (po.getLogisticsStatus() != null) {
                afterSaleLogisticsExportVO.setLogisticsStatus(po.getLogisticsStatus().getDesc());
              }
              final SingleResponse<LogisticsTraceData> trackList =
                  getTrackList(po.getTraceSource(), po.getCallbackId());
              if (trackList.isSuccess() && trackList.getData() != null) {
                if (trackList.getData().getTrackList() != null
                    && !trackList.getData().getTrackList().isEmpty()) {
                  afterSaleLogisticsExportVO.setLogisticsTrace(
                      trackList.getData().getTrackList().get(0).getTrackStatus());
                }
              }
              if (po.getAbnormalStatus() != null) {
                afterSaleLogisticsExportVO.setAbnormalStatus(po.getAbnormalStatus().getDesc());
              }
              if (po.getAbnormalType2() != null) {
                final LogisticsException logisticsException =
                    LogisticsException.valueOf(po.getAbnormalType2());
                if (logisticsException != null) {
                  afterSaleLogisticsExportVO.setAbnormalType(logisticsException.getDesc());
                }
              }
              final List<OrderLogisticsAbnormalityCs> csList1 =
                  abnormalityCsList.stream()
                      .filter(v -> v.getAbnormalityId().equals(po.getId()))
                      .collect(Collectors.toList());
              afterSaleLogisticsExportVO.setDistributeStatus(csList1.isEmpty() ? "未分配" : "已分配");
              if (!csList1.isEmpty()) {
                final List<StaffBrief> staffBriefList =
                    staffService.getStaffBriefList(
                        csList1.stream()
                            .map(OrderLogisticsAbnormalityCs::getCsUserId)
                            .collect(Collectors.toList()));
                afterSaleLogisticsExportVO.setCsUsers(
                    staffBriefList.stream()
                        .map(StaffBrief::getNickname)
                        .collect(Collectors.joining(",")));
              }
              afterSaleLogisticsExportVO.setHandleCount(String.valueOf(po.getHandleCount()));
              afterSaleLogisticsExportVO.setHandleTime(DateUtil.format(po.getHandleTime()));
              afterSaleLogisticsExportVO.setIsCloseWarning(
                  po.getActivateWarning() == 0 ? "是" : "否");
              afterSaleLogisticsExportVO.setCloseTime(DateUtil.format(po.getCloseTime()));
              afterSaleLogisticsExportVO.setCloseReason(po.getCloseReason());
              afterSaleLogisticsExportVO.setCloseRemark(po.getCloseRemark());
              final String skuNames =
                  wdtOrderDetails.stream()
                      .filter(v -> v.getTradeId().equals(po.getWdtTradeId()))
                      .map(WdtOrderDetail::getGoodsName)
                      .collect(Collectors.joining("、"));
              afterSaleLogisticsExportVO.setSkuNames(skuNames);
              afterSaleLogisticsExportVOS.add(afterSaleLogisticsExportVO);
            }
            return PageResponse.of(
                afterSaleLogisticsExportVOS, -1, query.getPageSize(), query.getPageIndex());
          }
        },
        new BiConsumer<ExportTask, ExcelWriterBuilder>() {
          @Override
          public void accept(ExportTask exportTask, ExcelWriterBuilder excelWriterBuilder) {
            exportTask.setParams(JsonUtil.toJson(query));
          }
        });
    return Response.buildSuccess();
  }

  @Override
  public SingleResponse<AfterSaleLogisticsPageVo> page(AfterSaleLogisticsPageQuery query) {
    final PageResponse<AfterSaleLogisticsPageVoItem> emptyPageResponse =
        ResponseFactory.ofPage(
            Collections.emptyList(), 0, query.getPageIndex(), query.getPageSize());
    final AfterSaleLogisticsPageVo afterSaleLogisticsPageVo = new AfterSaleLogisticsPageVo();
    afterSaleLogisticsPageVo.setPageData(emptyPageResponse);
    setUpStatisticData(query, afterSaleLogisticsPageVo);
    final List<AbnormalityListObj> abnormalityListObjs =
        abnormalityLogService.getDaddyBaseMapper().selectAbnormalityList(query);
    if (abnormalityListObjs.isEmpty()) {
      return SingleResponse.of(afterSaleLogisticsPageVo);
    }
    final Integer count =
        abnormalityLogService.getDaddyBaseMapper().selectAbnormalityListCount(query);
    final List<Long> traceIds =
        abnormalityListObjs.stream()
            .map(AbnormalityListObj::getWdtTradeId)
            .collect(Collectors.toList());
    if (traceIds.isEmpty()) {
      return SingleResponse.of(afterSaleLogisticsPageVo);
    }
    final List<WdtOrderDetail> allOrderDetails =
        wdtOrderDetailService.lambdaQuery().in(WdtOrderDetail::getTradeId, traceIds).list();
    final Map<Long, List<WdtOrderDetail>> orderDetailsGroup =
        allOrderDetails.stream().collect(Collectors.groupingBy(WdtOrderDetail::getTradeId));

    final List<Long> abnormalityIds =
        abnormalityListObjs.stream()
            .map(AbnormalityListObj::getAbnormalityId)
            .collect(Collectors.toList());
    final List<OrderLogisticsAbnormalityLog> abnormalityLogs =
        abnormalityLogService
            .lambdaQuery()
            .in(OrderLogisticsAbnormalityLog::getAbnormalityId, abnormalityIds)
            .list();
    final Map<Long, List<OrderLogisticsAbnormalityLog>> logGroup =
        abnormalityLogs.stream()
            .collect(Collectors.groupingBy(OrderLogisticsAbnormalityLog::getAbnormalityId));
    final List<String> srcOrderNos =
        abnormalityListObjs.stream()
            .map(AbnormalityListObj::getSrcOrderNo)
            .collect(Collectors.toList());
    final Map<String, OrderSensitiveInfo> orderSensitiveInfoMapFromBigdata =
        orderSensitiveInfoRepository.getOrderSensitiveInfoBatch(srcOrderNos);

    final List<OrderLogisticsAbnormalityCs> csList =
        orderLogisticsAbnormalityCsService
            .lambdaQuery()
            .in(OrderLogisticsAbnormalityCs::getAbnormalityId, abnormalityIds)
            .list();

    final List<AfterSaleLogisticsPageVoItem> logisticsPageVoItems =
        abnormalityListObjs.stream()
            .map(
                obj -> {
                  final AfterSaleLogisticsPageVoItem itemVO = new AfterSaleLogisticsPageVoItem();
                  itemVO.setId(obj.getAbnormalityId());
                  itemVO.setLogisticsNo(obj.getLogisticsNo());
                  itemVO.setLogisticsCompany(obj.getLogisticsCompanyName());
                  itemVO.setConsignTime(
                      StringUtil.equals("0", obj.getConsignTime()) ? "" : obj.getConsignTime());
                  final List<WdtOrderDetail> wdtOrderDetails =
                      orderDetailsGroup.getOrDefault(obj.getWdtTradeId(), ListUtil.empty());
                  final ArrayList<AfterSaleLogisticsPageVoItemGoodsInfo> goodsInfos =
                      new ArrayList<>();
                  for (WdtOrderDetail wdtOrderDetail : wdtOrderDetails) {
                    final AfterSaleLogisticsPageVoItemGoodsInfo goodsInfo =
                        new AfterSaleLogisticsPageVoItemGoodsInfo();
                    goodsInfo.setSkuCode(wdtOrderDetail.getSpecNo());
                    goodsInfo.setItemName(wdtOrderDetail.getGoodsName());
                    goodsInfo.setSpecifications(wdtOrderDetail.getSpecName());
                    goodsInfo.setPrice(wdtOrderDetail.getOrderPrice().toPlainString());
                    goodsInfo.setItemQuantity(wdtOrderDetail.getNum().intValue());
                    goodsInfo.setImgUrl(wdtOrderDetail.getImgUrl());
                    goodsInfos.add(goodsInfo);
                  }
                  itemVO.setGoodsInfos(goodsInfos);
                  // 快递状态 0 待发货，1 已发货，2 已揽收，3 运输中，4 派送中，5 待取件，6 未签收，7 代签收，8 已签收
                  Integer logisticsStatus = obj.getLogisticsStatus();
                  // 只要物流单号不为空，就认为是已发货
                  if (logisticsStatus == 0 && StringUtil.isNotBlank(obj.getLogisticsNo())) {
                    logisticsStatus = 1;
                  }
                  itemVO.setLogisticStatus(logisticsStatus);
                  itemVO.setLogisticTraceTime(obj.getTrackTime());
                  itemVO.setLogisticCallbackId(obj.getCallbackId());
                  itemVO.setWdtTradeNo(obj.getWdtTradeNo());
                  itemVO.setSrcTidNo(obj.getSrcOrderNo());
                  itemVO.setStockoutNo(obj.getStockoutNo());
                  itemVO.setWarehouseNo(obj.getWarehouseNo());
                  itemVO.setTradeStatus(obj.getTradeStatus());
                  itemVO.setAbnormalStatus(obj.getAbnormalStatus());
                  itemVO.setActivateWarning(obj.getActivateWarning() != 0);
                  final Optional<Warehouse> warehouse =
                      warehouseGateway.getWarehouse(obj.getWarehouseNo());
                  warehouse.map(Warehouse::getName).ifPresent(itemVO::setWarehouse);
                  itemVO.setReceiveName(obj.getReceiverName());
                  itemVO.setReceiveTel(obj.getReceiverMobile());
                  itemVO.setReceiveAddress(obj.getReceiverAddress());
                  itemVO.setReceiveArea(obj.getReceiverArea());
                  final OrderSensitiveInfo orderSensitiveInfo =
                      orderSensitiveInfoMapFromBigdata.get(obj.getSrcOrderNo());
                  if (orderSensitiveInfo != null) {
                    itemVO.setReceiveName(orderSensitiveInfo.getReceiverName());
                    itemVO.setReceiveTel(orderSensitiveInfo.getReceiverPhone());
                    itemVO.setReceiveAddress(orderSensitiveInfo.getReceiverAddr());
                  }
                  final List<OrderLogisticsAbnormalityLog> orderLogisticsAbnormalityLogs =
                      logGroup.getOrDefault(obj.getAbnormalityId(), Collections.emptyList());
                  List<ExceptionLogDto> exceptionLogDtos =
                      orderLogisticsAbnormalityLogs.stream()
                          .sorted(
                              Comparator.comparing(OrderLogisticsAbnormalityLog::getCreatedAt)
                                  .reversed())
                          .map(
                              v -> {
                                final ExceptionLogDto exceptionLogDto = new ExceptionLogDto();
                                exceptionLogDto.setType(v.getLogType().getValue());
                                exceptionLogDto.setMsg(v.getMsg());

                                final StaffBrief staffBrief =
                                    StaffAssembler.INST.toStaffBrief(v.getCreatedUid());
                                exceptionLogDto.setOperatorNick(staffBrief.getNickname());
                                exceptionLogDto.setTime(v.getCreatedAt());

                                return exceptionLogDto;
                              })
                          .collect(Collectors.toList());
                  // 列表上展示最近的人工处理情况，系统处理列表上不展示，查看更多中展示
                  exceptionLogDtos =
                      exceptionLogDtos.stream()
                          .filter(
                              v ->
                                  !v.getOperatorNick()
                                      .contains(StaffBrief.systemUser().getNickname()))
                          .collect(Collectors.toList());
                  itemVO.setExceptionLogList(exceptionLogDtos);

                  final List<OrderLogisticsAbnormalityCs> csList1 =
                      csList.stream()
                          .filter(cs -> cs.getAbnormalityId().equals(obj.getAbnormalityId()))
                          .collect(Collectors.toList());
                  itemVO.setDistributeStatus(!csList1.isEmpty() ? 1 : 0);
                  final List<Long> csUserIds =
                      csList1.stream()
                          .map(OrderLogisticsAbnormalityCs::getCsUserId)
                          .collect(Collectors.toList());
                  final List<StaffBrief> csUsers = staffService.getStaffBriefList(csUserIds);
                  itemVO.setCsUsers(
                      csUsers.stream().map(StaffBrief::getNickname).collect(Collectors.toList()));

                  return itemVO;
                })
            .collect(Collectors.toList());
    final PageResponse<AfterSaleLogisticsPageVoItem> pageResponse =
        ResponseFactory.ofPage(
            logisticsPageVoItems, count, query.getPageIndex(), query.getPageSize());
    afterSaleLogisticsPageVo.setPageData(pageResponse);
    return SingleResponse.of(afterSaleLogisticsPageVo);
  }

  private void setUpStatisticData(
      AfterSaleLogisticsPageQuery query, AfterSaleLogisticsPageVo afterSaleLogisticsPageVo) {
    final List<AbnormalityCountObj> abnormalityCountObjs =
        abnormalityLogService.getDaddyBaseMapper().selectAbnormalityCountObjs(query);
    int consignExceptionNum = 0;
    ArrayList<ExceptionDto> consignExceptionList = new ArrayList<>();
    afterSaleLogisticsPageVo.setConsignExceptionList(consignExceptionList);

    int receiveExceptionNum = 0;
    ArrayList<ExceptionDto> receiveExceptionList = new ArrayList<>();
    afterSaleLogisticsPageVo.setReceiveExceptionList(receiveExceptionList);

    int sendExceptionNum = 0;
    ArrayList<ExceptionDto> sendExceptionList = new ArrayList<>();
    afterSaleLogisticsPageVo.setSendExceptionList(sendExceptionList);

    int distributeExceptionNum = 0;
    ArrayList<ExceptionDto> distributeExceptionList = new ArrayList<>();
    afterSaleLogisticsPageVo.setDistributeExceptionList(distributeExceptionList);

    int difficultExceptionNum = 0;
    ArrayList<ExceptionDto> difficultExceptionList = new ArrayList<>();
    afterSaleLogisticsPageVo.setDifficultExceptionList(difficultExceptionList);

    for (LogisticsException exception : LogisticsException.values()) {
      final ExceptionDto exceptionDto = new ExceptionDto();
      exceptionDto.setType(exception.getValue());
      final int sum =
          abnormalityCountObjs.stream()
              .filter(v -> Objects.equals(v.getAbnormalType2(), exception.getValue()))
              .mapToInt(AbnormalityCountObj::getCount)
              .sum();
      exceptionDto.setNum(sum);

      if (LogisticsRootException.CONSIGN_EXCEPTION == exception.getRoot()) {
        consignExceptionNum += sum;
        consignExceptionList.add(exceptionDto);
      }
      if (LogisticsRootException.RECEIVE_EXCEPTION == exception.getRoot()) {
        receiveExceptionNum += sum;
        receiveExceptionList.add(exceptionDto);
      }
      if (LogisticsRootException.SEND_EXCEPTION == exception.getRoot()) {
        sendExceptionNum += sum;
        sendExceptionList.add(exceptionDto);
      }
      if (LogisticsRootException.DISTRIBUTE_EXCEPTION == exception.getRoot()) {
        distributeExceptionNum += sum;
        distributeExceptionList.add(exceptionDto);
      }
      if (LogisticsRootException.DIFFICULT_EXCEPTION == exception.getRoot()) {
        difficultExceptionNum += sum;
        difficultExceptionList.add(exceptionDto);
      }
    }
    afterSaleLogisticsPageVo.setConsignExceptionNum(consignExceptionNum);
    afterSaleLogisticsPageVo.setReceiveExceptionNum(receiveExceptionNum);
    afterSaleLogisticsPageVo.setSendExceptionNum(sendExceptionNum);
    afterSaleLogisticsPageVo.setDistributeExceptionNum(distributeExceptionNum);
    afterSaleLogisticsPageVo.setDifficultExceptionNum(difficultExceptionNum);
  }

  /**
   * @deprecated 已弃用，使用“跟进处理”接口替代
   */
  @Override
  @Transactional(rollbackFor = Exception.class)
  public Response remark(AfterSaleLogisticsRemarkCmd cmd) {
    final ArrayList<Long> ids = new ArrayList<>();
    Optional.ofNullable(cmd.getId()).ifPresent(ids::add);
    Optional.ofNullable(cmd.getIds()).ifPresent(ids::addAll);
    Assert.notEmpty(ids, "请至少选中一条记录");

    for (Long id : ids) {
      final OrderLogisticsAbnormalityLog abnormalityLog = new OrderLogisticsAbnormalityLog();
      abnormalityLog.setAbnormalityId(id);
      abnormalityLog.setLogType(AbnormalityLogType.REMARK);
      abnormalityLog.setAbnormalType(0);
      abnormalityLog.setAbnormalType2(0);
      abnormalityLog.setMsg("添加备注");
      abnormalityLogService.save(abnormalityLog);

      iOrderLogisticsAbnormalityService
          .lambdaUpdate()
          .eq(OrderLogisticsAbnormality::getId, id)
          .set(OrderLogisticsAbnormality::getRemark, cmd.getRemark())
          .update();
    }
    return Response.buildSuccess();
  }

  @Override
  public SingleResponse<String> getRemark(AfterSaleLogisticsRemarkQuery query) {
    final String remark =
        Optional.ofNullable(iOrderLogisticsAbnormalityService.getById(query.getId()))
            .map(OrderLogisticsAbnormality::getRemark)
            .orElse(null);
    return SingleResponse.of(remark);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Response open(AfterSaleLogisticsOpenCmd cmd) {
    final ArrayList<Long> ids = new ArrayList<>();
    Optional.ofNullable(cmd.getId()).ifPresent(ids::add);
    Optional.ofNullable(cmd.getIds()).ifPresent(ids::addAll);
    Assert.notEmpty(ids, "请至少选中一条记录");

    final List<OrderLogisticsAbnormality> orderLogisticsAbnormalities =
        iOrderLogisticsAbnormalityService.listByIds(ids);
    final ArrayList<OrderLogisticsAbnormality> triggerAbnormalities =
        new ArrayList<>(orderLogisticsAbnormalities.size());
    for (OrderLogisticsAbnormality abnormality : orderLogisticsAbnormalities) {
      final Long id = abnormality.getId();
      if (abnormality.getActivateWarning() == 1) {
        continue;
      }
      abnormality.setActivateWarning(1);
      abnormality.setRemark(null);
      iOrderLogisticsAbnormalityService.updateById(abnormality);

      final OrderLogisticsAbnormalityLog abnormalityLog = new OrderLogisticsAbnormalityLog();
      abnormalityLog.setAbnormalityId(id);
      abnormalityLog.setLogType(AbnormalityLogType.OPEN_TRACE);
      abnormalityLog.setAbnormalType(0);
      abnormalityLog.setAbnormalType2(0);
      abnormalityLog.setMsg("开启预警");
      abnormalityLogService.save(abnormalityLog);

      iOrderLogisticsTraceService.openTrace(abnormality.getTraceId());

      triggerAbnormalities.add(abnormality);
    }

    if (!triggerAbnormalities.isEmpty()) {
      final List<Long> traceIds =
          triggerAbnormalities.stream()
              .map(OrderLogisticsAbnormality::getTraceId)
              .collect(Collectors.toList());
      final AbnormalScanCmd abnormalScanCmd = new AbnormalScanCmd();
      abnormalScanCmd.setTraceIds(traceIds);

      abnormalScanService.abnormalScan(abnormalScanCmd);
    }
    return Response.buildSuccess();
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Response close(AfterSaleLogisticsCloseCmd cmd) {
    final ArrayList<Long> ids = new ArrayList<>();
    Optional.ofNullable(cmd.getId()).ifPresent(ids::add);
    Optional.ofNullable(cmd.getIds()).ifPresent(ids::addAll);
    Assert.notEmpty(ids, "请至少选中一条记录");

    for (Long id : ids) {
      final OrderLogisticsAbnormality abnormality = iOrderLogisticsAbnormalityService.getById(id);
      if (abnormality.getActivateWarning() == 0) {
        continue;
      }
      final String reason = Optional.ofNullable(cmd.getReason()).orElse("无");
      final String remark = Optional.ofNullable(cmd.getRemark()).orElse("无");

      abnormality.setActivateWarning(0);
      abnormality.setAbnormalStatus(0);
      abnormality.setCloseReason(reason);
      abnormality.setCloseRemark(remark);
      iOrderLogisticsAbnormalityService.updateById(abnormality);

      final OrderLogisticsAbnormalityLog abnormalityLog = new OrderLogisticsAbnormalityLog();
      abnormalityLog.setAbnormalityId(id);
      abnormalityLog.setLogType(AbnormalityLogType.CLOSE_TRACE);
      abnormalityLog.setAbnormalType(0);
      abnormalityLog.setAbnormalType2(0);
      abnormalityLog.setMsg(String.format("关闭预警，原因：%s；备注：%s;", reason, remark));
      abnormalityLogService.save(abnormalityLog);

      iOrderLogisticsTraceService.closeTrace(
          abnormality.getTraceId(), CloseReasonType.MANUAL_CLOSE);
    }
    return Response.buildSuccess();
  }

  @Data
  @AllArgsConstructor
  private static class TimeDto {
    String dayStr;
    Long time;
  }

  // 优化后方法
  public Map<Warehouse, Long> batchGetCounts(List<Warehouse> warehouses, TimeDto dayDto) {
    final String keySuffix = "_" + dayDto.getDayStr();
    RBatch batch = redissonClient.createBatch();

    // 批量准备所有请求
    Map<Warehouse, RFuture<Long>> futureMap =
        warehouses.stream()
            .collect(
                Collectors.toMap(
                    Function.identity(),
                    warehouse -> {
                      String key = "logistic_" + warehouse.getNo() + keySuffix;
                      return batch.getAtomicLong(key).getAsync();
                    }));

    // 批量执行
    batch.execute();

    // 处理结果
    return futureMap.entrySet().stream()
        .filter(
            entry -> {
              try {
                return entry.getValue().get() > 10;
              } catch (Exception e) {
                return false;
              }
            })
        .collect(
            Collectors.toMap(
                Map.Entry::getKey,
                entry -> {
                  try {
                    return entry.getValue().get();
                  } catch (Exception e) {
                    return 0L;
                  }
                }));
  }


  @Override
  public PageResponse<AfterSaleLogisticsBatchWarnVO> batchWarnList(
      AfterSaleLogisticsBatchWarnQuery query) {

    List<AfterSaleLogisticsBatchWarnVO> resList = new LinkedList<>();

    ZoneId zone = ZoneId.systemDefault();
    LocalDate today = LocalDate.now(zone);
    List<TimeDto> dayStrList =
        IntStream.range(0, 7)
            .mapToObj(today::minusDays)
            .map(
                date ->
                    new TimeDto(
                        date.format(DateTimeFormatter.BASIC_ISO_DATE),
                        date.atStartOfDay(ZoneOffset.ofHours(8)).toEpochSecond()))
            .collect(Collectors.toList());
    final List<Warehouse> warehouseList = iWarehouseService.lambdaQuery().list();
    Assert.notEmpty(warehouseList, "仓库列表为空");

    for (TimeDto dayDto : dayStrList) {

      final Map<Warehouse, Long> resMap = batchGetCounts(warehouseList, dayDto);
      resMap.forEach(
          (warehouse, count) -> {
            AfterSaleLogisticsBatchWarnVO vo = new AfterSaleLogisticsBatchWarnVO();
            vo.setTime(dayDto.getTime());
            vo.setWarehouseNo(warehouse.getNo());
            vo.setWarehouseName(warehouse.getName());
            vo.setMsg(String.format("24小时内，异常订单【%s】单", count));
            vo.setCount(count);
            resList.add(vo);
          });
    }

    if (CollectionUtils.isEmpty(resList)) {
      return PageResponse.of(new LinkedList<>(), 0, query.getPageSize(), query.getPageIndex());
    }

    int fromIndex = Math.max(Math.max(0, query.getPageIndex() - 1) * query.getPageSize(), 0);
    int toIndex = Math.min(fromIndex + query.getPageSize(), resList.size());
    return PageResponse.of(
        resList.subList(fromIndex, toIndex),
        resList.size(),
        query.getPageSize(),
        query.getPageIndex());
  }

  /**
   * @return
   */
  @Override
  public String getAbnormalStaticsMsg() {
    List<AbnormalityCountObj> abnormalityCountObjs =
        abnormalityLogService
            .getDaddyBaseMapper()
            .selectAbnormalityCountObjs(new AfterSaleLogisticsPageQuery());
    int consignNum = 0;
    int receiveNum = 0;
    int sendNum = 0;
    int distributeNum = 0;
    int difficultNum = 0;
    for (AbnormalityCountObj countObj : abnormalityCountObjs) {
      if (countObj.getAbnormalType().equals(LogisticsRootException.CONSIGN_EXCEPTION.getValue())) {
        consignNum = consignNum + countObj.getCount();
      }
      if (countObj.getAbnormalType().equals(LogisticsRootException.RECEIVE_EXCEPTION.getValue())) {
        receiveNum = receiveNum + countObj.getCount();
      }
      if (countObj.getAbnormalType().equals(LogisticsRootException.SEND_EXCEPTION.getValue())) {
        sendNum = sendNum + countObj.getCount();
      }
      if (countObj
          .getAbnormalType()
          .equals(LogisticsRootException.DISTRIBUTE_EXCEPTION.getValue())) {
        distributeNum = distributeNum + countObj.getCount();
      }
      if (countObj
          .getAbnormalType()
          .equals(LogisticsRootException.DIFFICULT_EXCEPTION.getValue())) {
        difficultNum = difficultNum + countObj.getCount();
      }
    }
    String msgTemplate =
        "订单物流异常：截止{}，发货异常订单共{}单，揽收异常订单共{}单，发运异常订单共{}单，物流派件异常订单共{}单，疑难件订单共{}单，" + "请及时查看并处理！";
    return StrUtil.format(
        msgTemplate,
        DateUtil.format(DateUtil.currentTime()),
        consignNum,
        receiveNum,
        sendNum,
        distributeNum,
        difficultNum);
  }

  @Override
  public MultiResponse<String> logisticsCompanyList(String company) {
    final List<KdyLogisticMapping> list = kdyLogisticMappingService.list();
    return MultiResponse.of(
        list.stream().map(KdyLogisticMapping::getStdName).distinct().collect(Collectors.toList()));
  }

  // ---------------------------------------------

  @Override
  public SingleResponse<LogisticsTraceData> getTrackList(Long id) {
    final OrderLogisticsAbnormality abnormality = iOrderLogisticsAbnormalityService.getById(id);
    Assert.notNull(abnormality, "售后物流异常记录查询异常");
    final SingleResponse<LogisticsTraceData> logisticsTraceData =
        getTrackList(abnormality.getTraceSource(), abnormality.getCallbackId());
    if (logisticsTraceData != null) return logisticsTraceData;
    return SingleResponse.of(null);
  }

  @Override
  public SingleResponse<LogisticsTraceData> getTrackList(
      LogisticsTraceDataSource traceSource, Long callbackId) {
    if (callbackId == null || callbackId == 0L) {
      return SingleResponse.of(null);
    }
    if (Objects.equals(traceSource, LogisticsTraceDataSource.KUAIDAOYUN)) {
      final SingleResponse<KdyCallback> callbackSingleResponse =
          kdyBizService.getCallback(callbackId);
      ResponseAssert.assertJust(callbackSingleResponse);
      final KdyCallback callback = callbackSingleResponse.getData();
      final LogisticsTraceData logisticsTraceData =
          LogisticsTraceConvert.INSTANCE.kdyCallbackToLogisticsTraceData(callback);
      Collections.reverse(logisticsTraceData.getTrackList());
      return SingleResponse.of(logisticsTraceData);
    }
    if (Objects.equals(traceSource, LogisticsTraceDataSource.WDT)) {
      final WdtLogisticsTrace wdtLogisticsTrace = wdtLogisticsTraceService.getById(callbackId);
      final LogisticsTraceData logisticsTraceData =
          LogisticsTraceConvert.INSTANCE.wdtLogisticsTraceToLogisticsTraceData(wdtLogisticsTrace);
      Collections.reverse(logisticsTraceData.getTrackList());
      return SingleResponse.of(logisticsTraceData);
    }
    throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "不支持的数据来源");
  }

  @Override
  public boolean matchLogisticsTrace(OrderLogisticsTrace trace) {
    if (trace.getCallbackId() == 0L && StrUtil.isNotBlank(trace.getLogisticsNo())) {
      if (config.isUseWdtLogisticsTrace(trace)) {
        final Long wdtCallbackId =
            wdtLogisticsTraceService.mapIdByLogisticsNo(trace.getLogisticsNo());
        if (wdtCallbackId != null && wdtCallbackId > 0) {
          trace.setCallbackId(wdtCallbackId);
          trace.setTraceSource(LogisticsTraceDataSource.WDT);
          return true;
        }
      }
      if (trace.getTraceSource() == LogisticsTraceDataSource.KUAIDAOYUN) {
        final Long kdyCallbackId = kdyCallbackService.selectIdByLogisticsNo(trace.getLogisticsNo());
        if (kdyCallbackId != null && kdyCallbackId > 0) {
          trace.setCallbackId(kdyCallbackId);
          return true;
        }
      }
    }
    return false;
  }

  @Override
  public boolean filter(WdtOrder wdtOrder, List<WdtOrderDetail> orderDetails) {
    if (!filterFreezeReason(wdtOrder) || !filterWarehouse(wdtOrder)) {
      log.debug("物流异常-扫描订单: 过滤仓库、冻结 tradeId: {}", wdtOrder.getTradeId());
      return false;
    }
    if (!filterOrderDetails(orderDetails)) {
      log.debug("物流异常-扫描订单: 订单明细过滤 tradeId: {}", wdtOrder.getTradeId());
      return false;
    }
    final String logisticsTypeName = wdtOrder.getLogisticsTypeName();
    if (!filterLogisticsCompany(logisticsTypeName)) {
      log.debug("物流异常-扫描订单: 物流[{}]不做跟踪 tradeId: {}", logisticsTypeName, wdtOrder.getTradeId());
      return false;
    }
    return true;
  }

  private boolean filterLogisticsCompany(String logisticsName) {
    return logisticsName == null || Stream.of("顺丰", "丰网").noneMatch(logisticsName::contains);
  }

  private boolean filterOrderDetails(List<WdtOrderDetail> orderDetails) {
    return CollectionUtils.isNotEmpty(orderDetails)
        && orderDetails.stream()
            .anyMatch(((Predicate<WdtOrderDetail>) this::filterGoodsName).and(this::filterGoodsNo));
  }

  private boolean filterWarehouse(WdtOrder wdtOrder) {
    return !config.getBondedWarehouseCode().contains(wdtOrder.getWarehouseNo());
  }

  private static boolean filterFreezeReason(WdtOrder wdtOrder) {
    return StrUtil.isBlank(wdtOrder.getFreezeReason())
        || StrUtil.equals("无", wdtOrder.getFreezeReason().trim());
  }

  private boolean filterGoodsNo(WdtOrderDetail v) {
    final String specNo = v.getSpecNo();
    return !config.getFilterGoodsNos().contains(specNo);
  }

  private boolean filterGoodsName(WdtOrderDetail orderDetail) {
    final String goodsName = orderDetail.getGoodsName();
    return config.getFilterGoodsNames().stream().noneMatch(goodsName::contains);
  }
}
