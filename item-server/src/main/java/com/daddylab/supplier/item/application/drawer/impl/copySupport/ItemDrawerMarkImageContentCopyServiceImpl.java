package com.daddylab.supplier.item.application.drawer.impl.copySupport;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.drawer.ItemDrawerCopyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMarkImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMarkImageContent;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerMarkImageContentService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerMarkImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @class ItemDrawerImageCopyServiceImpl.java
 * @description 描述类的作用
 * @date 2024-04-09 16:35
 */
@Service
public class ItemDrawerMarkImageContentCopyServiceImpl implements ItemDrawerCopyService {

    @Autowired
    private IItemDrawerMarkImageService itemDrawerMarkImageService;
    @Autowired
    private IItemDrawerMarkImageContentService itemDrawerMarkImageContentService;

    @Override
    public void handler(ItemDrawer sourceDrawer, ItemDrawer targetDrawer) {
        // 删除
        List<ItemDrawerMarkImage> itemDrawerMarks = getItemDrawerMarkImage(targetDrawer.getId());
        if (!itemDrawerMarks.isEmpty()) {
            List<Long> markImageIds = itemDrawerMarks.stream().map(ItemDrawerMarkImage::getId).collect(Collectors.toList());
            itemDrawerMarkImageService.removeByIdsWithTime(markImageIds);
        }
        // 新增
        List<ItemDrawerMarkImage> sourceItemDrawerMarks = getItemDrawerMarkImage(sourceDrawer.getId());
        if (!sourceItemDrawerMarks.isEmpty()) {
            List<Long> markImageIds = sourceItemDrawerMarks.stream().map(ItemDrawerMarkImage::getId).collect(Collectors.toList());
            List<ItemDrawerMarkImageContent> itemDrawerMarkImageContentList = getItemDrawerMarkImageContent(markImageIds);
            Map<Long, List<ItemDrawerMarkImageContent>> markImageContentMap = itemDrawerMarkImageContentList.stream().collect(Collectors.groupingBy(ItemDrawerMarkImageContent::getMarkImageId, Collectors.toList()));

            for (ItemDrawerMarkImage sourceItemDrawerMark : sourceItemDrawerMarks) {
                List<ItemDrawerMarkImageContent> itemDrawerMarkImageContents = markImageContentMap.get(sourceItemDrawerMark.getId());
                sourceItemDrawerMark.setDrawerId(targetDrawer.getId());
                itemDrawerMarkImageService.save(sourceItemDrawerMark);

                List<ItemDrawerMarkImageContent> drawerMarkImageContents = itemDrawerMarkImageContents.stream().peek(itemDrawerMarkImageContent -> {
                    itemDrawerMarkImageContent.setMarkImageId(sourceItemDrawerMark.getId());
                    itemDrawerMarkImageContent.setDrawerId(sourceItemDrawerMark.getDrawerId());
                }).collect(Collectors.toList());
                itemDrawerMarkImageContentService.saveBatch(drawerMarkImageContents);
            }
        }
    }

    List<ItemDrawerMarkImage> getItemDrawerMarkImage(Long itemDrawerId) {
        LambdaQueryWrapper<ItemDrawerMarkImage> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ItemDrawerMarkImage::getDrawerId, itemDrawerId);
        return itemDrawerMarkImageService.list(queryWrapper);
    }

    List<ItemDrawerMarkImageContent> getItemDrawerMarkImageContent(List<Long> markImageIds) {
        LambdaQueryWrapper<ItemDrawerMarkImageContent> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ItemDrawerMarkImageContent::getMarkImageId, markImageIds);
        return itemDrawerMarkImageContentService.list(queryWrapper);
    }
}
