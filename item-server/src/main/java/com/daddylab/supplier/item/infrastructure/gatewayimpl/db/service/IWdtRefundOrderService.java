package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrder;
import java.util.List;

/**
 * <p>
 * 旺店通退换单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
public interface IWdtRefundOrderService extends IDaddyService<WdtRefundOrder> {

    List<WdtRefundOrder> listByTid(String tid);
}
