package com.daddylab.supplier.item.application.region;


import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.domain.region.vo.Region;

/**
 * <p>
 * 地区关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
public interface RegionBizService {

    MultiResponse<Region> getRegionList();

    MultiResponse<Region> getRegionList(String parentCode);

    MultiResponse<Region> getBigRegionList();
    
    SingleResponse<AddressParseResult> parseAddress(String address);
    
    Response clearRegionTreeCache();

}
