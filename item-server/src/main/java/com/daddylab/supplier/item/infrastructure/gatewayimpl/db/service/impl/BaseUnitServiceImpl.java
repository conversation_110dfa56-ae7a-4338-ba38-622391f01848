package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BaseUnit;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.BaseUnitMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBaseUnitService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 基础单位表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Service
public class BaseUnitServiceImpl extends DaddyServiceImpl<BaseUnitMapper, BaseUnit> implements IBaseUnitService {

    @Override
    public String getKingDeeNum(String unitName) {
        List<BaseUnit> list = this.lambdaQuery().eq(BaseUnit::getName, unitName)
                .ne(BaseUnit::getKingDeeNum, "")
                .orderByDesc(BaseUnit::getCreatedAt)
                .last("limit 1")
                .select().list();
        if(CollUtil.isNotEmpty(list)){
            return list.get(0).getKingDeeNum();
        }
        return "";
    }
}
