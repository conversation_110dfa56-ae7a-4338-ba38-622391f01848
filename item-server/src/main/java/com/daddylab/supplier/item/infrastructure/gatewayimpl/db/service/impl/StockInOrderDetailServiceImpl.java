package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockInOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockInOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockInOrderDetailService;
import com.daddylab.supplier.item.types.stockInOrder.StockInOrderDetailApplyQuantity;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 采购入库明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Service
@RequiredArgsConstructor
public class StockInOrderDetailServiceImpl extends DaddyServiceImpl<StockInOrderDetailMapper, StockInOrderDetail> implements IStockInOrderDetailService {

    final StockInOrderMapper stockInOrderMapper;

    @Override
    public List<StockInOrderDetail> getListByPurchaseOrderId(Long purchaseOrderId) {
        return stockInOrderMapper.getListByPurchaseOrderId(purchaseOrderId);
    }

    @Override
    public Integer getApplyQuantity(Long purchaseOrderId, String skuCode, Boolean isGift) {
        return stockInOrderMapper.getApplyQuantity(purchaseOrderId, skuCode,isGift);
    }

    @Override
    public List<StockInOrderDetailApplyQuantity> getApplyQuantities(Long purchaseOrderId) {
        return stockInOrderMapper.getApplyQuantities(purchaseOrderId);
    }

    @Override
    public List<StockInOrderDetail> getListByOrderId(Long stockInOrderId) {
        LambdaQueryWrapper<StockInOrderDetail> objectLambdaQueryWrapper = Wrappers.lambdaQuery();
        objectLambdaQueryWrapper.eq(StockInOrderDetail::getStockInOrderId,stockInOrderId);
        return this.list(objectLambdaQueryWrapper);
    }
}
