package com.daddylab.supplier.item.application.drawer;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.domain.drawer.form.ItemDrawerMarkImageForm;
import com.daddylab.supplier.item.domain.drawer.vo.ItemDrawerMarkImageVO;

/**
 * Class  ItemDrawerMarkImageService
 *
 * @Date 2022/6/1下午2:34
 * <AUTHOR>
 */
public interface ItemDrawerMarkImageService {


    /**
     * 获取抽屉标记详情
     *
     * @param drawerId
     * @return
     */
    SingleResponse<ItemDrawerMarkImageVO> detail(Long drawerId);

    /**
     * 添加标记内容
     *
     * @param itemDrawerMarkImageForm
     * @param userId
     * @return
     */
    SingleResponse<Long> add(ItemDrawerMarkImageForm itemDrawerMarkImageForm, Long userId);

    /**
     * 编辑标记内容
     *
     * @param itemDrawerMarkImageForm
     * @param userId
     * @return
     */
    SingleResponse<Boolean> edit(ItemDrawerMarkImageForm itemDrawerMarkImageForm, Long userId);

    /**
     * 删除标记
     *
     * @param id
     * @return
     */
    Boolean deleteByDrawerId(Long id);

    /**
     * 清空图片标记
     *
     * @param itemId 商品ID
     * @param cleanAll 清空全部（否则只清除最后一条）
     * @param userId 操作用户
     */
    boolean clean(Long itemId, Boolean cleanAll, Long userId);

}
