package com.daddylab.supplier.item.controller.purchase.dto.order;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022/4/25 5:48 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("采购详情列表分页查询请求实体")
public class PurchaseOrderDetailPageQuery extends PageQuery {

    @ApiModelProperty(value = "采购单id")
    private Long purchaseOrderId;

    @ApiModelProperty(value = "采购单id(List)")
    private List<Long> purchaseOrderIds;

}
