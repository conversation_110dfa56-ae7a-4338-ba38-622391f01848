package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.controller.stockout.dto.StockOutOrderVO;
import com.daddylab.supplier.item.controller.stockout.dto.StockOutOrderViewVO;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderStockQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockOutOrder;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderQuery;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderSheet;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 退料出库表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-24
 */
public interface StockOutOrderMapper extends DaddyBaseMapper<StockOutOrder> {

    List<StockOutOrderVO> pageQuery(@Param("param") StockOutOrderQuery stockOutOrderQuery);

    Integer pageCount(@Param("param") StockOutOrderQuery stockOutOrderQuery);

    Integer countExport(@Param("param") StockOutOrderQuery stockOutOrderQuery);

    List<StockOutOrderSheet> queryExport(@Param("param") StockOutOrderQuery query);

    void updateApprovalById(StockOutOrder stockOutOrder);

    StockOutOrderViewVO getStockOutOrderById(Long id);

    Integer getStockOutOrderItemSum(StockOutOrderStockQuery stockQuery);

    void updateWdtPurchaseOrderNo(@Param("id") Long id, @Param("wdtPurchaseOrderNo") String wdtPurchaseOrderNo);
}
