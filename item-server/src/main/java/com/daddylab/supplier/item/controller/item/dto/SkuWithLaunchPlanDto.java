package com.daddylab.supplier.item.controller.item.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: <PERSON>
 * @Date: 2022/6/3 13:55
 * @Description: 上新计划关联的 sku 信息
 */
@Data
public class SkuWithLaunchPlanDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long skuId;
    private String skuName;
    private BigDecimal costPrice;
    private BigDecimal salePrice;
    private Integer stock;
}
