package com.daddylab.supplier.item.application.refundOrder;

import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrder;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/28
 */
public interface WdtRefundOrderDataSyncService {

    void saveOrUpdateByApiQueryResponse(RefundSearchResponse response);

    void saveOrUpdateBatch(List<WdtRefundOrder> wdtRefundOrders);

    void saveOrUpdateBatchSpecifiedByRefundNo(String refundNo);
}
