package com.daddylab.supplier.item.infrastructure.third.redbook.domain.query;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @class SkuPageQuery.java
 * @description 描述类的作用
 * @date 2024-02-28 16:21
 */
@NoArgsConstructor
@Data
public class SkuPageQuery {
    /**
     * skuId，使用id查询其他条件可以不填
     */
    private String id;
    /**
     * 商品创建时间开始时间，Unix-Time时间戳
     */
    private Long createTimeFrom;
    /**
     * 商品创建时间结束时间，Unix-Time时间戳
     */
    private Long createTimeTo;
    /**
     * 商品更新时间开始时间，Unix-Time时间戳
     */
    private Long updateTimeFrom;
    /**
     * 商品更新时间结束时间，Unix-Time时间戳
     */
    private Long updateTimeTo;
    /**
     * 是否在架上
     */
    private Boolean buyAble;
    /**
     * 库存大于等于某数
     */
    private Integer stockGte;
    /**
     * 库存小于等于某数
     */
    private Integer stockLte;
    /**
     * 返回页码 默认 1，页码从 1 开始 PS：当前采用分页返回，数量和页数会一起传，如果不传，则采用 默认值
     */
    private Integer pageNo;
    /**
     * 返回数量，默认50最大100
     */
    private Integer pageSize;
    /**
     * 商品条码
     */
    private String barcode;
    /**
     * 小红书编码,即将废弃
     */
    private String skuCode;
    /**
     * 只返回单品类型的商品
     */
    private Boolean singlePackOnly;
    /**
     * 查询起始商品id，全店商品时间倒序
     */
    private String lastId;
    /**
     * 不传返回全部，传true只返回渠道商品，传false只返回非渠道商品
     */
    private Boolean isChannel;

    public static SkuPageQuery of(Integer pageNo, Integer pageSize) {
        SkuPageQuery skuPageQuery = new SkuPageQuery();
        skuPageQuery.setPageNo(pageNo);
        skuPageQuery.setPageSize(pageSize);
        return skuPageQuery;
    }

    public static SkuPageQuery of(String skuId) {
        SkuPageQuery skuPageQuery = new SkuPageQuery();
        skuPageQuery.setId(skuId);
        return skuPageQuery;
    }
}
