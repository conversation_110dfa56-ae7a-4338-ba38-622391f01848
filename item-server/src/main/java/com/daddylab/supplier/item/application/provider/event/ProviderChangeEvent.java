package com.daddylab.supplier.item.application.provider.event;

import com.daddylab.supplier.item.application.common.event.EntityChangeEvent;
import com.daddylab.supplier.item.common.domain.EntityChange;
import org.javers.core.diff.Diff;

public class ProviderChangeEvent <T> extends EntityChangeEvent<T> {
    public ProviderChangeEvent(Long operatorId, Long targetId, EntityChange<T> entityChange) {
        super(operatorId, targetId, entityChange);
    }

    public static <T> ProviderChangeEvent<T> ofNew(Long operatorId, Long targetId, T newEntity) {
        return new ProviderChangeEvent<>(operatorId, targetId, EntityChange.ofAdd(newEntity));
    }

    public static <T> EntityChangeEvent<T> ofUpdate(Long operatorId, Long targetId, Diff diff, T oldEntity, T newEntity) {
        return new ProviderChangeEvent<>(operatorId, targetId, EntityChange.ofUpdate(diff, oldEntity, newEntity));
    }

    public static <T> EntityChangeEvent<T> ofUpdate(Long operatorId, Long targetId, EntityChange<T> entityChange) {
        return new ProviderChangeEvent<>(operatorId, targetId, entityChange);
    }

    public static <T> EntityChangeEvent<T> ofRemove(Long operatorId, Long targetId, T oldEntity) {
       return new ProviderChangeEvent<>(operatorId, targetId, EntityChange.ofRemove(oldEntity));
    }
}
