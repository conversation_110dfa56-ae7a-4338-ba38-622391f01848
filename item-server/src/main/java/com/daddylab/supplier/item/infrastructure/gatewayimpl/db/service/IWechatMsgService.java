package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatMsg;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WechatMsgMapper;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-03
 */
public interface IWechatMsgService extends IDaddylabServicePlus<WechatMsg, WechatMsgMapper> {


    @Transactional(rollbackFor = Throwable.class)
    boolean updateState(List<Long> ids, int from, int to);
}
