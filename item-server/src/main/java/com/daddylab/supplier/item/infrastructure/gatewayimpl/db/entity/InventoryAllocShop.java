package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryAllocShopStatus;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存分配店铺设置
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class InventoryAllocShop implements Serializable {

  private static final long serialVersionUID = 1L;

  /** id */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /** 创建时间 */
  @TableField(fill = FieldFill.INSERT)
  private Long createdAt;

  /** 创建人 */
  @TableField(fill = FieldFill.INSERT)
  private Long createdUid;

  /** 更新时间 */
  @TableField(fill = FieldFill.INSERT_UPDATE)
  private Long updatedAt;

  /** 更新人 */
  @TableField(fill = FieldFill.INSERT_UPDATE)
  private Long updatedUid;

  /** 是否已删除 */
  @TableLogic(value = "0", delval = "id")
  private Long isDel;

  /** 删除时间 */
  @TableField(fill = FieldFill.DEFAULT)
  private Long deletedAt;

  /** 店铺ID */
  private Long shopId;

  private String shopNo;

  /** 库存分配权重 */
  private Integer inventoryWeight;

  /** 状态 1:启用 2:禁用 3:平台无法同步 */
  private InventoryAllocShopStatus status;

  /** 低库存预警 */
  private Integer warnThreshold;

  /** 下次库存自动分配时间 */
  private Long nextAllocTime;

  /** 库存分配周期 */
  private Integer allocInterval;

  /** 下次库存同步时间 */
  private Long nextSyncTime;

  /** 库存同步周期 */
  private Integer syncInterval;

  /** 上次库存自动分配时间 */
  private Long lastAllocTime;

  /** 上次库存同步时间 */
  private Long lastSyncTime;
  
  /**
   * 获取有效的库存预警值
   * @param inventoryAlloc 平台商品库存分配店铺设置
   */
  public Integer getEffectiveWarnThreshold(InventoryAlloc inventoryAlloc) {
    if (inventoryAlloc != null) {
      return inventoryAlloc.getWarnThreshold();
    }
    if (warnThreshold != null) {
      return warnThreshold;
    }
    return null;
  }
}
