package com.daddylab.supplier.item.application.item.combinationItem.dto.query;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/12 4:30 下午
 * @description
 */
@Data
@ApiModel("组合商品下拉选模糊查询入参")
public class CombinationItemNamePageQuery extends PageQuery {


    private static final long serialVersionUID = 1325667877015501950L;

    private String name;
}
