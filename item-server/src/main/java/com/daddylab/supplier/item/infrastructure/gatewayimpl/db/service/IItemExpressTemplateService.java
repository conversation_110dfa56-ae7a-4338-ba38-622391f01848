package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemExpressTemplate;

/**
 * <p>
 * 快递模板 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface IItemExpressTemplateService extends IDaddyService<ItemExpressTemplate> {

}
