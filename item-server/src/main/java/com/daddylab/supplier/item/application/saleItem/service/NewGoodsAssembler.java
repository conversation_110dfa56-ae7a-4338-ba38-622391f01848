package com.daddylab.supplier.item.application.saleItem.service;

import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsPrincipalsInfo;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsPrincipalsInfoPO;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.assembler.CommonAssembler;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2023/11/2
 */
@Mapper(uses = {StaffAssembler.class, CommonAssembler.class})
public interface NewGoodsAssembler {
    NewGoodsAssembler INST = Mappers.getMapper(NewGoodsAssembler.class);

    @Mapping(target = "qcUsers", source = "qcIds")
    @Mapping(target = "principalUsers", source = "principalId")
    @Mapping(target = "legalUsers", source = "legalId")
    @Mapping(target = "buyerUsers", source = "buyerId")
    NewGoodsPrincipalsInfo mapNewGoodsPrincipalsInfo(NewGoodsPrincipalsInfoPO po);

    NewGoods copy(NewGoods newGoods);
}
