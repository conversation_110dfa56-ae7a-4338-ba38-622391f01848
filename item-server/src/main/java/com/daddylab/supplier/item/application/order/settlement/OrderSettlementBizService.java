package com.daddylab.supplier.item.application.order.settlement;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.order.settlement.dto.*;
import com.daddylab.supplier.item.application.payment.dto.PaymentDetailAmountVO;

import java.io.InputStream;
import java.util.List;

/**
 * 订单结算相关业务逻辑
 *
 * <AUTHOR> up
 * @date 2023年08月04日 11:14 AM
 */
public interface OrderSettlementBizService {

  /**
   * 新增结算/结算单数据 分页查询
   *
   * @param query
   * @return
   */
  PageResponse<SysBillPageVo> sysBillPageQuery(SysBillPageQuery query);

  /**
   * 系统结算单批量处理，自动结算
   *
   * @param query
   * @return
   */
  com.alibaba.cola.dto.Response sysBillBatchSettlement(SysBillPageQuery query);

  /**
   * 新增结算/结算单数据 下载明细，导出指定操作时间和仓库的账单数据。
   *
   * @param cmd
   */
  void exportBillData(ExportSysBillCmd cmd);

  /**
   * 展示订单结算的一些细节
   *
   * @param cmd
   * @return
   */
  FormDetailVo viewFormInfo(FormDetailCmd cmd);

  /**
   * 采购单结算，详情页面，excel表格数据，分页返回
   *
   * @param query
   * @return
   */
  PageResponse<FormDetailPageVo> viewFormDetailPage(FormDetailPageQuery query);

  /**
   * 点击保存 结算表单数据
   *
   * @param cmd
   */
  void save(SettlementSaveCmd cmd);

  /**
   * 结算单详情页面[导出] 数据源是页面中的在线Excel中的数据。
   *
   * @param id
   */
  void exportDetailExcel(Long id);

  /**
   * 【导出结算】结算单，发货明细，退货明细，售后明细
   *
   * @param id
   */
  void exportSettlement(Long id);

  /**
   * 批量导出 【导出结算】结算单，发货明细，退货明细，售后明细 plus版本
   *
   * @param cmd 结算单筛选条件封装
   */
  void exportSettlementPlus(BatchExportCmd cmd);

  /**
   * 结算单详情页面[导入] 数据体现在页面中的在线Excel中
   *
   * @param inputStream
   * @param id
   */
  Response importDetailExcel(InputStream inputStream, Long id);

  /**
   * 采购结算单 分页查询
   *
   * @param query
   * @return
   */
  PageResponse<SettlementOrderPageVo> settlementPageQuery(SettlementOrderPageQuery query);

  /**
   * 导出指定结算日期的退换管理明细
   *
   * @param settlementDate
   */
  void exportRefundInfo(Long settlementDate);

  /**
   * 根据结算单号删除单据
   *
   * @param nos
   */
  void deleteOrderForm(List<String> nos);

  /**
   * 申请删除 如果不存在关联的付款单，直接删除 如果存在，弹出提示文案，生成【删除凭证】，等待确认删除操作的发起。
   *
   * @param id
   */
  SingleResponse<String> deleteApply(Long id);

  /**
   * 确认删除 针对存在关联付款单的情况下的删除。 确保此单据有【删除凭证】即确保返回过删除确认文案。
   *
   * @param id
   * @return
   */
  SingleResponse<Boolean> confirmDelete(Long id);

  /**
   * 获取此结算单可申请付款的金额。 结算金额-所有已申请付款金额
   *
   * @param id
   * @return
   */
  SingleResponse<PaymentDetailAmountVO> getPayAmount(Long id, List<Long> choosedDetailIdList);

  /**
   * 刷新结算周期
   *
   * @param remainDetailIds 删除的结算明细 ID
   * @return
   */
  SingleResponse<String> refreshSettlementDate(List<Long> remainDetailIds);


}
