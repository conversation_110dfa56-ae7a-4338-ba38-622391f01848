package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Category;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CategoryMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICategoryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * 后台类目 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class CategoryServiceImpl extends DaddyServiceImpl<CategoryMapper, Category> implements ICategoryService {

    @Autowired
    private CategoryMapper categoryMapper;

    @Override
    public Boolean isSyncKingDee(Long categoryId) {
        Category byId = this.getById(categoryId);
        return Objects.nonNull(byId) && StringUtils.isNotBlank(byId.getKingDeeId());
    }

    @Override
    public List<String> getCategoryNames(String itemNo) {
        if (!org.springframework.util.StringUtils.hasText(itemNo)) {
            return new ArrayList<>();
        }
        // 先找出叶子类目
        Category leafCategory = categoryMapper.getLeafCategory(itemNo);
        if (leafCategory == null) {
            return new ArrayList<>();
        }
        List<String> list = new ArrayList<>();
        list.add(leafCategory.getName());

        // 递归到根类目
        getById(leafCategory.getParentId(), list, 0);

        // 整理 list
        Collections.reverse(list);
        return list;
    }

    private final static int maxDepth = 10;

    private void getById(Long id, List<String> list, int depth) {
        if (depth > maxDepth) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "类目递归层级过深");
        }

        Category category = categoryMapper.getById(id);
        // 递归结束
        if (category == null) {
            return;
        }
        list.add(category.getName());

        // 继续递归
        getById(category.getParentId(), list, ++depth);
    }

    @Override
    public Map<Long, Category> batchQueryById(Collection<Long> categoryIds) {
        if (CollUtil.isEmpty(categoryIds)) {
            return new HashMap<>();
        }

        return this.lambdaQuery()
                .in(Category::getId, categoryIds)
                .list().stream().collect(Collectors.toMap(Category::getId, Function.identity()));
    }
}
