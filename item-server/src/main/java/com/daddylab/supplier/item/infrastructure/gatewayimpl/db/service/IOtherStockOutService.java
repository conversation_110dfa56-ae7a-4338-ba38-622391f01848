package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockOut;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 其他出库单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
public interface IOtherStockOutService extends IDaddyService<OtherStockOut> {

}
