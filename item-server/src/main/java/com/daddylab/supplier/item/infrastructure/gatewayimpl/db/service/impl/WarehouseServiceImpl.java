package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.controller.common.dto.WarehousePageQuery;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.WarehouseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WarehouseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWarehouseService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 仓库列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-24
 */
@Service
public class WarehouseServiceImpl extends DaddyServiceImpl<WarehouseMapper, Warehouse> implements IWarehouseService {

    @Autowired
    WarehouseMapper warehouseMapper;

    @Resource
    @Qualifier("UserGatewayCacheImpl")
    UserGateway userGateway;

    @Override
    public Page<Warehouse> pageQuery(WarehousePageQuery query) {
        LambdaQueryWrapper<Warehouse> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StringUtils.hasText(query.getNo()), Warehouse::getNo, query.getNo())
                .in(CollUtil.isNotEmpty(query.getNos()), Warehouse::getNo, query.getNos())
                .eq((Objects.nonNull(query.getState()) && query.getState() > 0), Warehouse::getState, query.getState())
                .like(Objects.nonNull(query.getOrderPersonnelId()), Warehouse::getOrderPersonnel, query.getOrderPersonnelId())
                .eq(query.getType() != null && query.getType() != 0,
                        Warehouse::getIsVirtualWarehouse,
                        Objects.equals(query.getType(), 2) ? 1 : 0)
                .eq(Objects.nonNull(query.getWmsType()), Warehouse::getWmsType, query.getWmsType())
                .like(Objects.nonNull(query.getAfterSaleStaffId()), Warehouse::getAfterSaleStaffStr, query.getAfterSaleStaffId())
                .orderByDesc(Warehouse::getId);
        final HashSet<String> names = new HashSet<>();
        names.add(query.getName());
        if (CollUtil.isNotEmpty(query.getNames())) {
            names.addAll(query.getNames());
        }
        final List<String> filterNames = names.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (!filterNames.isEmpty()) {
            queryWrapper.and(q -> {
                for (String name : filterNames) {
                    q.or().like(Warehouse::getName, name);
                }
            });
        }

        final List<Integer> businessLine = query.getBusinessLine();
        if (CollUtil.isNotEmpty(businessLine)) {
            queryWrapper.and(qx -> {
                for (int i = 0; i < businessLine.size(); i++) {
                    Integer line = businessLine.get(i);
                    if (line == 0) {
                        qx.eq(Warehouse::getIsBusinessLine0, 1);
                    }
                    if (line == 1) {
                        qx.eq(Warehouse::getIsBusinessLine1, 1);
                    }
                    if (line == 2) {
                        qx.eq(Warehouse::getIsBusinessLine2, 1);
                    }
                    if (line == 3) {
                        qx.eq(Warehouse::getIsBusinessLine3, 1);
                    }
                    if (i != businessLine.size() - 1) {
                        qx.or();
                    }
                }
            });
        }

        Page<Warehouse> page = new Page<>(query.getPageIndex(), query.getPageSize());
        page(page, queryWrapper);
        return page;
    }

    @Override
    public Optional<Warehouse> queryWarehouseByNo(String warehouseNo) {
        return lambdaQuery().eq(Warehouse::getNo, warehouseNo).oneOpt();
    }

    @Override
    public List<WarehouseDO> queryWarehouseMapByNo(Collection<String> noList) {
        return warehouseMapper.queryWarehouseMapByNo(noList);
    }

    @Override
    public Optional<Warehouse> queryBySkuCodeAndIsGift(String skuCode, Integer isGift, BigDecimal taxPrice) {
        BigDecimal bigDecimal = taxPrice.setScale(6, RoundingMode.DOWN);
        List<Warehouse> warehouse = warehouseMapper.queryBySkuCodeAndIsGift(skuCode, isGift, bigDecimal);
        if (CollectionUtils.isEmpty(warehouse)) {
            return Optional.of(warehouse.get(0));
        }
        return Optional.empty();
    }

    @Override
    public List<String> getOrderPersonnelNickName(String warehouseNo) {
        Optional<Warehouse> optionalWarehouse = queryWarehouseByNo(warehouseNo);
        if (!optionalWarehouse.isPresent()) {
            return new LinkedList<>();
        }

        Warehouse warehouse = optionalWarehouse.get();
        String orderPersonnel = warehouse.getOrderPersonnel();
        if (StringUtils.isEmpty(orderPersonnel) || orderPersonnel.length() == 1) {
            return new LinkedList<>();
        }
        List<Long> collect = Arrays.stream(orderPersonnel.split(",")).map(Long::valueOf).collect(Collectors.toList());
        Map<Long, StaffInfo> longStaffInfoMap = userGateway.batchQueryStaffInfoByIds(collect);
        return longStaffInfoMap.values().stream().map(StaffInfo::getNickname).collect(Collectors.toList());
    }

    @Override
    public Map<String, List<String>> getOrderPersonnelNickName(Collection<String> warehouseNos) {
        // once db
        warehouseNos = warehouseNos.stream().distinct().collect(Collectors.toList());
        List<Warehouse> list = this.lambdaQuery().in(Warehouse::getNo, warehouseNos).list();
        Map<String, Warehouse> map = list.stream().collect(Collectors.toMap(Warehouse::getNo, v -> v, (a, b) -> a));

        Set<Long> userIds = list.stream().filter(val -> StringUtils.hasText(val.getOrderPersonnel()) && (val.getOrderPersonnel().length() > 1))
                .map(val ->
                        Arrays.stream(val.getOrderPersonnel().split(","))
                                .map(Long::valueOf).collect(Collectors.toList()))
                .flatMap(List::stream).collect(Collectors.toSet());
        // once cache
        Map<Long, StaffInfo> userIdInfoMap = userGateway.batchQueryStaffInfoByIds(new LinkedList<>(userIds));

        return warehouseNos.stream().collect(Collectors.toMap(no -> no, no -> {
            Warehouse warehouse = map.get(no);
            if (Objects.isNull(warehouse)) {
                return new LinkedList<>();
            } else {
                String orderPersonnel = warehouse.getOrderPersonnel();
                if (StringUtils.hasText(orderPersonnel) && orderPersonnel.length() > 1) {
                    List<String> nameList = new LinkedList<>();
                    String[] split = orderPersonnel.split(",");
                    for (String s : split) {
                        StaffInfo staffInfo = userIdInfoMap.get(Long.valueOf(s));
                        if (Objects.nonNull(staffInfo)) {
                            nameList.add(staffInfo.getNickname());
                        }
                    }
                    return nameList;
                } else {
                    return new LinkedList<>();
                }
            }
        }));

    }

    @Override
    public List<String> getWarehouseNoByOrderPersonnel(List<Long> orderPersonnelIds) {
        List<String> warehouseNoList = new LinkedList<>();
        if (CollUtil.isNotEmpty(orderPersonnelIds)) {
            LambdaQueryChainWrapper<Warehouse> wrapper = this.lambdaQuery();
            int size = orderPersonnelIds.size();
            for (int i = 0; i < size; i++) {
                wrapper.like(Warehouse::getOrderPersonnel, orderPersonnelIds.get(i));
                if (i != size - 1) {
                    wrapper.or();
                }
            }
            List<String> collect = wrapper.select(Warehouse::getNo).list().stream().map(Warehouse::getNo)
                    .distinct().collect(Collectors.toList());
            warehouseNoList.addAll(collect);
        }
        return warehouseNoList;
    }

    @Override
    public Map<String, String> getNameAndNo(Collection<String> nos) {
        return this.lambdaQuery().in(Warehouse::getNo, nos).list().stream()
                .collect(Collectors.toMap(Warehouse::getNo, Warehouse::getName, (a, b) -> a));
    }

    @Override
    public Collection<String> warehouseIdsToNos(List<Long> warehouseIds) {
        return warehouseIdsToNosMap(warehouseIds).values();
    }

    @Override
    public Map<Long, String> warehouseIdsToNosMap(List<Long> warehouseIds) {
        return lambdaQuery().in(Warehouse::getId, warehouseIds)
                .select(Warehouse::getId, Warehouse::getNo)
                .list()
                .stream()
                .collect(Collectors.toMap(Warehouse::getId, Warehouse::getNo));
    }

    @Override
    public List<Warehouse> getBatchByNos(List<String> warehouseNos) {
        if (CollUtil.isEmpty(warehouseNos)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(Warehouse::getNo, warehouseNos).list();
    }

    @Override
    public Integer remainWarehouseRatio(String warehouseNo, Integer thisRatio) {
        int i = 0;
        Optional<Warehouse> warehouse = queryWarehouseByNo(warehouseNo);
        if (warehouse.isPresent()) {
            i = warehouse.get().getInventoryRatio();
        }
        return Math.max((i - thisRatio), 0);
    }

    @Override
    public List<Warehouse> listByName(String warehouseName) {
        return lambdaQuery().eq(Warehouse::getName, warehouseName).list();
    }
}
