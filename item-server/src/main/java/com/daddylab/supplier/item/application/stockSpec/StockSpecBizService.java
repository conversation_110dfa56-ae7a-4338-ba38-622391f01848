package com.daddylab.supplier.item.application.stockSpec;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;
import com.daddylab.supplier.item.types.BatchResultStat;
import com.daddylab.supplier.item.types.stockSpec.*;
import com.daddylab.supplier.item.types.stockStatistic.WdtStockSpecStatisticQuery;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/3/1
 */
public interface StockSpecBizService {
    PageResponse<StockSpecVO> query(StockSpecQuery query);

    PageResponse<AvailableStockSpecVO> availableStockQuery(StockSpecQuery query);
    
    List<AvailableStockSpecVO> getAvailableStocks(String skuCode);
    
    List<AvailableStockSpecVO> getAvailableStocks(Collection<String> skuCodes);

    SingleResponse<BatchResultStat> setInventoryMonitor(SetInventoryMonitorCmd cmd);

    SingleResponse<WarehouseStockSpecStatistic> statistics(String warehouseNo);

    SingleResponse<Map<String, WarehouseStockSpecStatistic>> statistics(List<String> warehouseNos);

    SingleResponse<Map<String, WarehouseStockSpecStatistic>> statistics(WdtStockSpecStatisticQuery query);

    SingleResponse<ExportTask> export(StockSpecQuery query);

    Response fetchBySpecNos(Collection<String> specNos);

    Response runInventoryMonitor();
}
