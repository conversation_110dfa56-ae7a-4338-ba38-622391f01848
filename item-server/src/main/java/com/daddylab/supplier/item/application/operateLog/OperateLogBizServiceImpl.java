package com.daddylab.supplier.item.application.operateLog;

import com.alibaba.cola.dto.MultiResponse;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import java.util.List;

@Service
public class OperateLogBizServiceImpl implements OperateLogBizService {
    @Inject
    OperateLogDomainService operateLogDomainService;

    @Override
    public MultiResponse<OperateLog> getOperateLogs(OperateLogTarget target, Object targetId) {
        return MultiResponse.of(operateLogDomainService.getOperateLogs(target, targetId));
    }

    @Override
    public MultiResponse<OperateLog> getOperateLogs(List<OperateLogTarget> target, Object targetId) {
        return MultiResponse.of(operateLogDomainService.getOperateLogs(target, targetId));
    }

}
