package com.daddylab.supplier.item.application.salesInStock;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.SalesOrderDto;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.SkuUnitDto;
import com.daddylab.supplier.item.application.salesInStock.dto.SalesInStockDetailCmd;
import com.daddylab.supplier.item.application.salesInStock.dto.SalesInStockDetailVO;
import com.daddylab.supplier.item.application.salesInStock.dto.SalesInStockPageQuery;
import com.daddylab.supplier.item.application.salesInStock.dto.SalesInStockPageVO;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年10月23日 9:47 AM
 */
public interface SalesInStockBizService {

    PageResponse<SalesInStockPageVO> queryPage(SalesInStockPageQuery pageQuery);

    SingleResponse<SalesInStockDetailVO> viewDetail(SalesInStockDetailCmd cmd);


    /**
     * 根据结算单明细生成销售退货单，并且同步到金蝶
     *
     * @param details
     * @return
     */
    SingleResponse<SalesOrderDto> generateOrderBySettlement(String warehouseNo, List<SkuUnitDto> salesReturnList,
                                                            Long auditDate,Boolean mockSync);

}
