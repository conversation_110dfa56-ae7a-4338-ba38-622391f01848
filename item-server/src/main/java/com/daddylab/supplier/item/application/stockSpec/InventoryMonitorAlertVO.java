package com.daddylab.supplier.item.application.stockSpec;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2025/1/3
 */
@Data
@EqualsAndHashCode(of = {"warehouseNo", "skuNo"})
public class InventoryMonitorAlertVO {
  private Long inventoryMonitorId;
  private String thresholdType;
  private Integer alertThreshold;
  private Long stockSpecId;
  private String warehouseNo;
  private String skuNo;
  private BigDecimal stockNum;
  private BigDecimal availableStock;
}
