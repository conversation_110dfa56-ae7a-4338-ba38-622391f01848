package com.daddylab.supplier.item.infrastructure.rocketmq.delay;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2025年06月25日 10:37 AM
 */
@Data
@Builder
public class DelayMsgBizDto {

    private PropertiesDTO properties;
    /**
     * 接收mq
     */
    private String topic;
    private String body;
    private String keys;
    private String tags;

    @AllArgsConstructor
    @Data
    public static class PropertiesDTO {
        /**
         * 期待投递时间
         */
        private Long expectTime;

        private String source;

    }



}
