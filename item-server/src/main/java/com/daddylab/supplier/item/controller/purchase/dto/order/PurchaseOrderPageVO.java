package com.daddylab.supplier.item.controller.purchase.dto.order;

import com.daddylab.supplier.item.controller.purchase.dto.PurchaseDetailLineVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022/3/25 10:08 上午
 */
@Data
@ApiModel("采购订单分页查询(明细模式关)返回封装")
public class PurchaseOrderPageVO implements Serializable {

    private static final long serialVersionUID = 5272448883639578061L;

    @ApiModelProperty(value = "采购订单id")
    private Long id;
    @ApiModelProperty(value = "采购单号")
    private String no;
    @ApiModelProperty(value = "采购日期")
    private Long dt;
    @ApiModelProperty(value = "供应商ID")
    private Long providerId;
    @ApiModelProperty(value = "供应商名称")
    private String providerName;
    @ApiModelProperty(value = "P系统供应商ID")
    private Long partnerProviderId;
    @ApiModelProperty("是否黑名单 0否 1是")
    private Integer isBlacklist;
    @ApiModelProperty(value = "采购类型")
    private Integer type;
    @ApiModelProperty(value = "采购数量")
    private Integer purchaseQuantity;
    @ApiModelProperty(value = "已入库数量")
    private Integer stockQuantity;
    @ApiModelProperty(value = "状态")
    private Integer state;
    @ApiModelProperty(value = "采购员名称")
    private String buyerUserName;

    @ApiModelProperty(value = "已入库数量+待入库数量")
    private Integer hadAndWillStockQuantity;

    @ApiModelProperty(value = "明细列表")
    List<PurchaseDetailLineVO> lineList = new LinkedList<>();

    private Integer businessLine;

    private Long overrideOrderId;

    @ApiModelProperty(value = "是否允许选择")
    private Boolean canStockChoose;



}
