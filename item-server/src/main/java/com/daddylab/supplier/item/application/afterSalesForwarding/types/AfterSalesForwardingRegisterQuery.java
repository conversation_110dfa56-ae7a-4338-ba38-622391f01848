package com.daddylab.supplier.item.application.afterSalesForwarding.types;


import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/5/24
 */
@Data
public class AfterSalesForwardingRegisterQuery extends PageQuery {
    private static final long serialVersionUID = -5942988518631984093L;

    @ApiModelProperty(value = "登记ID", notes = "登记ID")
    private List<Long> ids;

    @ApiModelProperty(value = "物流单号", notes = "物流单号")
    private String deliveryNo;

    @ApiModelProperty(value = "商品名称", notes = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "商品SKU", notes = "商品SKU编码")
    private String skuCode;

    @ApiModelProperty(value = "登记时间-开始时间")
    private Long regTimeStart;

    @ApiModelProperty(value = "登记时间-结束时间")
    private Long regTimeEnd;

    @ApiModelProperty(value = "订单编号", notes = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "转寄单号", notes = "转寄单号")
    private String stockoutNo;

    @ApiModelProperty(value = "转寄单号", notes = "转寄单号")
    private String forwardingDeliveryNo;

    @ApiModelProperty(value = "转寄仓库", notes = "转寄仓库")
    private String stockoutWarehouseNo;

    @ApiModelProperty(value = "处理状态", notes = "处理状态 0:待处理 1:待转寄 2:已转寄 3:无需转寄")
    private Integer status;

    @ApiModelProperty(value = "转寄地址信息", notes = "支持按照联系人、联系电话、转寄地址模糊搜索")
    private String addressInfo;

    @ApiModelProperty(value = "操作人类型", notes = "操作人类型 0:内部员工 1:外部用户")
    private Integer operatorType;

    @ApiModelProperty(value = "操作人ID", notes = "操作人ID")
    private Long operatorId;

    /**
     * 异常说明
     */
    @ApiModelProperty(value = "异常说明")
    private String abnormalDescription;

    @ApiModelProperty(value = "店铺编号", notes = "店铺编号")
    private String shopNo;
    /**
     * 客服备注
     */
    @ApiModelProperty(value = "客服备注")
    private String csRemark;

    /**
     * 无需转寄类型
     */
    @ApiModelProperty(value = "无需转寄类型", notes = "无需转寄类型 0:全部 1:自有仓库 2:影响销售 3:其他")
    private Integer needNotForwardType;

}
