package com.daddylab.supplier.item.application.saleItem.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022年08月02日 2:41 PM
 */
@Data
@ApiModel("新品商品返回实体(spu维度分割)")
public class NewGoodsGroupVO {

    @ApiModelProperty("新品商品spu返回")
    private NewGoodsSpuVO spu;

    @ApiModelProperty("新品商品skuList返回")
    private List<NewGoodsSkuVO> skuList;

}
