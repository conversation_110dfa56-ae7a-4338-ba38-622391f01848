package com.daddylab.supplier.item.application.saleItem.converter;

import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * Class  NewGoodsConverter
 *
 * @Date 2022/6/7下午3:43
 * <AUTHOR>
 */
@Mapper
public interface NewGoodsConverter {
    NewGoodsConverter INSTANCE = Mappers.getMapper(NewGoodsConverter.class);

}
