package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.SysVariable;
import com.daddylab.supplier.item.types.SysVariableKey;

import java.util.Optional;

/**
 * <p>
 * 系统变量 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
public interface ISysVariableService extends IDaddyService<SysVariable> {

    Optional<String> getValue(String key);

    default Optional<String> getValue(SysVariableKey key) {
        return getValue(key.getValue());
    }

    void setValue(String key, String value);

    default void setValue(SysVariableKey key, String value) {
        setValue(key.getValue(), value);
    }
}
