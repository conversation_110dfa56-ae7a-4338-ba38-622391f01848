package com.daddylab.supplier.item.application.purchasePayable.applyPayAudit.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayApplyAuditStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2023年02月17日 4:49 PM
 */
@Data
@ApiModel("申请付款审核流程节点操作信息")
public class AuditProcessNodeInfoVo {

    @ApiModelProperty("节点id")
    private Long nodeId;

    @ApiModelProperty("操作人角色名称")
    private String roleName;

    @ApiModelProperty("操作人用户id")
    private Long userId;

    @ApiModelProperty("操作人用户名称")
    private String userName;

    @ApiModelProperty("是否自动审核")
    public Integer autoAudit;

    @ApiModelProperty("审核操作结果")
    private PayApplyAuditStatus auditStatus;

    @ApiModelProperty("审核意见")
    private String opinion;


}
