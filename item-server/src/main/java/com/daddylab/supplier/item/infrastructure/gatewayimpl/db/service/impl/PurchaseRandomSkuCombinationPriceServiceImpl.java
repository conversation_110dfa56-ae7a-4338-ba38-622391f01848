package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseRandomSkuCombinationPrice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseRandomSkuCombinationPriceMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseRandomSkuCombinationPriceService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 任意sku组合采购价格信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-21
 */
@Service
public class PurchaseRandomSkuCombinationPriceServiceImpl extends DaddyServiceImpl<PurchaseRandomSkuCombinationPriceMapper, PurchaseRandomSkuCombinationPrice> implements IPurchaseRandomSkuCombinationPriceService {

}
