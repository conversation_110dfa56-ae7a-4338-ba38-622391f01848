package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/10
 */
@Repository
public interface ChoreMapper {
    int deleteLogByCreatedAt(@Param("createdAt") Long createdAt, @Param("limit") int limit);


    List<Long> selectOrderLogisticsAbnormalByCreatedAtAndStatusClosed(@Param("createdAt") Long createdAt, @Param("limit") int limit);

    int deleteOrderLogisticsAbnormalByIds(@Param("ids") Collection<Long> ids);

    int deleteOrderLogisticsAbnormalLogByAbnormalIds(@Param("ids") Collection<Long> ids);

    int deleteOrderLogisticsAbnormalLogByCreatedAt(@Param("createdAt") Long createdAt, @Param("limit") int limit);

    int deleteOrderLogisticsAbnormalLogByIds(@Param("ids") List<Long> ids);
}
