package com.daddylab.supplier.item.controller.dev;

import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.tmpJob.NewGoodsDataProcessJob;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2021/12/31
 */

@Api()
@Slf4j
@RestController
@RequestMapping("/dev/newGoodsDataProcessJob")
public class NewGoodsDataProcessJobController {

    @Autowired
    private NewGoodsDataProcessJob newGoodsDataProcessJob;


    @PostMapping(value = "/importList", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response importList(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        newGoodsDataProcessJob.importList(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/importTaobaoExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response importTaobaoExcel(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        newGoodsDataProcessJob.importTaobaoExcel(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/importRedBookExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response importRedBookExcel(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        newGoodsDataProcessJob.importRedBookExcel(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/importDouyinExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response importDouyinExcel(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        newGoodsDataProcessJob.importDouyinExcel(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/importKuaishouExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response importKuaishouExcel(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        newGoodsDataProcessJob.importKuaishouExcel(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/importImageExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response importImageExcel(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        newGoodsDataProcessJob.importImageExcel(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/importImageExcel2", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response importImageExcel2(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        newGoodsDataProcessJob.importImageExcel2(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/watermarkHandle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response watermarkHandle(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        newGoodsDataProcessJob.watermarkHandle(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/preHandle", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response preHandle() throws IOException {
        newGoodsDataProcessJob.preHandle();
        return Response.buildSuccess();
    }

    @PostMapping(value = "/planHandle", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response planHandle() throws IOException {
        newGoodsDataProcessJob.planHandle();
        return Response.buildSuccess();
    }

    @PostMapping(value = "/mergeHandle", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response mergeHandle() throws IOException {
        newGoodsDataProcessJob.mergeHandle();
        return Response.buildSuccess();
    }

    @PostMapping(value = "/recover", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response recover(@RequestBody NewGoodsDataProcessJob.RecoverCmd cmd) throws IOException {
        newGoodsDataProcessJob.recover(cmd);
        return Response.buildSuccess();
    }

    @PostMapping(value = "/recoverBatch", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response recoverBatch(@RequestBody NewGoodsDataProcessJob.RecoverBatchCmd cmd) throws IOException {
        newGoodsDataProcessJob.recoverBatch(cmd);
        return Response.buildSuccess();
    }

    @PostMapping(value = "/statusHandle", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response statusHandle() throws IOException {
        newGoodsDataProcessJob.statusHandle();
        return Response.buildSuccess();
    }

    @PostMapping(value = "/trainingMaterialsHandleImport", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response trainingMaterialsHandleImport(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        newGoodsDataProcessJob.trainingMaterialsHandleImport(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/trainingMaterialsSync", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response trainingMaterialsSync(@ApiParam(name = "file", value = "文件", required = true)
                                          @RequestParam("file") MultipartFile file) throws IOException {
        newGoodsDataProcessJob.trainingMaterialsSync(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/trainingMaterialsImageLogic", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response trainingMaterialsImageLogic(@ApiParam(name = "file", value = "文件", required = true)
                                                @RequestParam("file") MultipartFile file) throws IOException {
        newGoodsDataProcessJob.trainingMaterialsImageLogic(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/materialsToBeUpdateAndSyncWiki", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response trainingMaterialsImageLogic() throws IOException {
        newGoodsDataProcessJob.materialsToBeUpdateAndSyncWiki();
        return Response.buildSuccess();
    }

    @PostMapping(value = "/migrateLVT", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Response migrateLVT(@RequestBody NewGoodsDataProcessJob.MigrateLVTParams params) throws IOException {
        newGoodsDataProcessJob.migrateLVT(params);
        return Response.buildSuccess();
    }


}