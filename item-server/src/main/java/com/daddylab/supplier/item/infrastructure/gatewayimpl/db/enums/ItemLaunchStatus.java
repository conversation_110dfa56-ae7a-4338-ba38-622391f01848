package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <PERSON>
 * @Date: 2022/6/3 10:35
 * @Description: 请描述下这个类
 * <p>
 * 待选择 -> 待完善 -> 待设计 -> 待审核 -> 待修改 -> 待上架 -> 已上架
 */
@Getter
@AllArgsConstructor
public enum ItemLaunchStatus implements IEnum<Integer> {
    /**
     * 不可选择
     */
    NON_SELECTIVITY(0, "不可选择"),
    TO_BE_SELECTED(1, "待选择"),
    /**
     * 待完善
     */
    TO_BE_IMPROVED(2, "待完善"),
    /**
     * 待设计
     */
    TO_BE_DESIGNED(3, "待设计"),
    /**
     * 待审核
     */
    TO_BE_AUDITED(4, "待审核"),
    /**
     * 待修改
     */
    TO_BE_UPDATED(6, "待修改"),
    /**
     * 待上架
     */
    TO_BE_RELEASED(5, "待上架"),
    /**
     * 已上架
     */
    HAS_BE_RELEASED(7, "已上架"),
    /**
     * 已下架
     */
    DOWN(8, "已下架"),
    ;

    private final Integer value;
    private final String desc;

    public static ItemLaunchStatus of(Integer value) {
        for (ItemLaunchStatus itemLaunchStatus : ItemLaunchStatus.values()) {
            if (itemLaunchStatus.getValue().equals(value)) {
                return itemLaunchStatus;
            }
        }
        return null;
    }
}
