package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NuoNuoInvoiceProcessRecord;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.NuoNuoInvoiceProcessRecordMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INuoNuoInvoiceProcessRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户诺诺开票流程记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Service
public class NuoNuoInvoiceProcessRecordServiceImpl extends DaddyServiceImpl<NuoNuoInvoiceProcessRecordMapper, NuoNuoInvoiceProcessRecord> implements INuoNuoInvoiceProcessRecordService {


    @Override
    public List<NuoNuoInvoiceProcessRecord> getByStatus(String orderNo, Integer status) {
        return this.lambdaQuery()
                .eq(NuoNuoInvoiceProcessRecord::getOrderNo, orderNo)
                .eq(NuoNuoInvoiceProcessRecord::getStatus, status)
                .orderByDesc(NuoNuoInvoiceProcessRecord::getId)
                .list();
    }
}
