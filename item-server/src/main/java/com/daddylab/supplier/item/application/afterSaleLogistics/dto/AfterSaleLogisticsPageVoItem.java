package com.daddylab.supplier.item.application.afterSaleLogistics.dto;

import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2024年05月23日 9:44 AM
 */
@Data
@ApiModel("订单物流异常查询返回封装")
public class AfterSaleLogisticsPageVoItem {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty(value = "异常状态", notes = "异常状态 0:已关闭 1:待跟进 2:已跟进")
    private Integer abnormalStatus;

    @ApiModelProperty(value = "是否开启预警")
    private Boolean activateWarning;


    // -----------------

    @ApiModelProperty("物流单号")
    private String logisticsNo;
    @ApiModelProperty("物流公司")
    private String logisticsCompany;
    @ApiModelProperty("发货时间")
    private String consignTime;

    // ---------------------

    private List<AfterSaleLogisticsPageVoItemGoodsInfo> goodsInfos;

    // ---------------

    @ApiModelProperty(value = "物流状态",notes = "快递状态 0 待发货，1 已发货，2 已揽收，3 运输中，4 派送中，5 待取件，6 未签收，7 代签收，8 已签收")
    private Integer logisticStatus;
    @ApiModelProperty("物流状态更新时间")
    private Long logisticTraceTime;
    @ApiModelProperty("物流回调ID")
    private Long logisticCallbackId;

    @ApiModelProperty("旺店通订单单号")
    private String wdtTradeNo;
    @ApiModelProperty("订单原始单号")
    private String srcTidNo;
    @ApiModelProperty("订单状态（旺店通）参考订单管理页面")
    private Integer tradeStatus;
    @ApiModelProperty("出库单号")
    private String stockoutNo;
    @ApiModelProperty("仓库名称")
    private String warehouse;
    @ApiModelProperty("仓库编号")
    private String warehouseNo;

    // -------------------------

    @ApiModelProperty("收件人名称")
    private String receiveName;
    @ApiModelProperty("收件人电弧")
    private String receiveTel;
    @ApiModelProperty("收件人地址")
    private String receiveAddress;
    @ApiModelProperty("收件人地区")
    private String receiveArea;
    public String getReceiveAddress() {
        return Stream.of(receiveArea, receiveAddress).filter(StringUtil::isNotBlank).collect(Collectors.joining(" "));
    }

    // -------------

    @ApiModelProperty("异常信息简单日志")
    private List<ExceptionLogDto> exceptionLogList;

    @ApiModelProperty("分配状态")
    private Integer distributeStatus;

    @ApiModelProperty(value = "负责客服", notes = "负责客服花名列表")
    private List<String> csUsers;

}
