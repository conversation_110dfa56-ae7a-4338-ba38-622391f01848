package com.daddylab.supplier.item.application.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.daddylab.supplier.item.domain.user.dto.StaffDropBuyerQuery;
import com.daddylab.supplier.item.domain.user.dto.StaffDropDownItem;
import com.daddylab.supplier.item.domain.user.dto.StaffDropDownQuery;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffListQuery;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import org.apache.commons.collections4.multiset.HashMultiSet;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class UserBizServiceImpl implements UserBizService {
    @Resource(name = "UserGatewayCacheImpl")
    UserGateway userGateway;

    @Override
    public MultiResponse<StaffDropDownItem> staffDropDownList(StaffDropDownQuery query) {
        if (Objects.nonNull(query)) {
            if (StringUtil.isNotBlank(query.getName())) {
                boolean isSys = query.getName().contains("系统");
                if (isSys) {
                    return MultiResponse.of(ListUtil.of(new StaffDropDownItem(-1L, "系统", "系统")));
                }
            }
        }

        final StaffListQuery staffListQuery = new StaffListQuery();
        staffListQuery.setName(query.getName());
        staffListQuery.setNickname(query.getNickname());
        staffListQuery.setPageIndex(query.getPageIndex());
        staffListQuery.setPageSize(query.getPageSize());
        staffListQuery.setUserId(query.getUserId());
        final String dept = query.getDept();
        final List<String> filterDeptList = new ArrayList<>();
        if (StringUtil.isNotBlank(dept)) {
            if (StringUtil.contains(dept, ",")) {
                filterDeptList.addAll(StringUtil.splitTrim(dept, ","));
            } else {
                filterDeptList.add(dept);
            }
        }
        final List<String> filterExactDeptList = new ArrayList<>();
        final String exactDept = query.getExactDept();
        if (StringUtil.isNotBlank(exactDept)) {
            if (StringUtil.contains(exactDept, ",")) {
                filterExactDeptList.addAll(StringUtil.splitTrim(exactDept, ","));
            } else {
                filterExactDeptList.add(exactDept);
            }
        }
        //假如测试环境针对某个部门过滤员工，强行把测试组加进去方便测试操作
        if ((CollUtil.isNotEmpty(filterDeptList) || CollUtil.isNotEmpty(filterExactDeptList))
                && ApplicationContextUtil.isActiveProfile("test", "dev")) {
            filterDeptList.add("质控组");
            filterDeptList.add("后端组");
        }
        staffListQuery.setDepts(filterDeptList);
        staffListQuery.setExactDepts(filterExactDeptList);
        final List<StaffInfo> staffInfos = userGateway.queryStaffList(staffListQuery);
        List<StaffDropDownItem> staffInfoDownItems = staffInfos.stream()
                                                               .map((staffInfo -> {
                                                                   StaffDropDownItem staffDropDownItem = new StaffDropDownItem();
                                                                   staffDropDownItem.setStaffId(staffInfo.getUserId());
                                                                   staffDropDownItem.setStaffName(staffInfo.getUserName());
                                                                   staffDropDownItem.setStaffNickName(staffInfo.getNickname());
                                                                   staffDropDownItem.setDept(staffInfo.getDept());
                                                                   return staffDropDownItem;
                                                               }))
                                                               .collect(Collectors.toList());

        return MultiResponse.of(filterStaffNicknameConflict(staffInfoDownItems));
    }

    private static List<StaffDropDownItem> filterStaffNicknameConflict(List<StaffDropDownItem> staffInfoDownItems) {
        final HashMultiSet<String> conflictNicknameReg = new HashMultiSet<>();
        final HashMap<Long, Integer> ordMap = new HashMap<>();
        for (StaffDropDownItem staffInfoDownItem : staffInfoDownItems) {
            final String nickName = staffInfoDownItem.getStaffNickName();
            conflictNicknameReg.add(nickName);
            ordMap.put(staffInfoDownItem.getStaffId(), conflictNicknameReg.getCount(nickName));
        }
        for (StaffDropDownItem staffInfoDownItem : staffInfoDownItems) {
            final String nickName = staffInfoDownItem.getStaffNickName();
            final int count = conflictNicknameReg.getCount(nickName);
            final Integer ord = ordMap.get(staffInfoDownItem.getStaffId());
            if (count > 1) {
                staffInfoDownItem.setStaffNickName(nickName + ord);
            }
        }
        return staffInfoDownItems;
    }

    @Override
    public MultiResponse<StaffDropDownItem> staffDropDownBuyerList(StaffDropBuyerQuery query) {
        final StaffListQuery StaffListQuery = new StaffListQuery();
        StaffListQuery.setNickname(query.getName());
        StaffListQuery.setPageIndex(query.getPageIndex());
        StaffListQuery.setPageSize(query.getPageSize());
        final List<StaffInfo> staffInfos = userGateway.queryStaffList(StaffListQuery);
        final List<StaffDropDownItem> staffDropDownItems = staffInfos.stream()
                                                                     .map((staffInfo -> new StaffDropDownItem(staffInfo.getUserId(),
                                                                                                              staffInfo.getUserName(),
                                                                                                              staffInfo.getNickname())))
                                                                     .collect(Collectors.toList());
        return MultiResponse.of(filterStaffNicknameConflict(staffDropDownItems));
    }

}
