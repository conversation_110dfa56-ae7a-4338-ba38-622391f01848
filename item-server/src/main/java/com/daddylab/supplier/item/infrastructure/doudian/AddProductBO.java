package com.daddylab.supplier.item.infrastructure.doudian;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> up
 * @date 2022年09月13日 1:58 PM
 */
@Data
public class AddProductBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    @TableLogic
    private Integer isDel;

    private Long itemId;
    private String itemCode;



}
