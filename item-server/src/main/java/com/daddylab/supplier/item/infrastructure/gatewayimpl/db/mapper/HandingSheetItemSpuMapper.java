package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetItemSpu;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 盘货表关联的商品（V2.4.5新版盘货表） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
public interface HandingSheetItemSpuMapper extends DaddyBaseMapper<HandingSheetItemSpu> {

    List<Long> selectInvalidSpuIds(@Param("handingSheetId") Long handingSheetId);
}
