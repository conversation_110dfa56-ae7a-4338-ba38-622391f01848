package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/1/4
 */
@AllArgsConstructor
@Getter
public enum DataSource implements IEnum<Integer> {
    /**
     * 后台创建
     */
    SELF(0, "后台创建"),
    /**
     * 旺店通
     */
    WDT(1, "旺店通"),

    ;
    private final Integer value;
    private final String desc;
}
