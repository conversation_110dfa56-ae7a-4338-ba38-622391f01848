/*
package com.daddylab.supplier.item.controller.stock;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.virtualWarehouse.StockConfigDetailBizService;
import com.daddylab.supplier.item.application.virtualWarehouse.VirtualWarehouseBizService;
import com.daddylab.supplier.item.application.virtualWarehouse.dto.*;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.WarehouseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

*/
/**
 * <AUTHOR> up
 * @date 2024年03月04日 3:06 PM
 *//*

@RestController
@RequestMapping("/stock/vWarehouse/")
@Api(value = "库存管理/虚拟仓", tags = "库存管理/虚拟仓相关 API")
@RequiredArgsConstructor
public class VirtualWarehouseController {

    final VirtualWarehouseBizService virtualWarehouseBizService;
    final StockConfigDetailBizService stockConfigDetailBizService;
    final OperateLogGateway operateLogGateway;

    @ApiOperation(value = "保存虚拟仓")
    @PostMapping(value = "/save")
    public Response save(@RequestBody @Validated VirtualWarehouseSaveCmd cmd) {
        virtualWarehouseBizService.save(cmd);
        return Response.buildSuccess();
    }

    @ApiOperation(value = "虚拟仓库分页列表")
    @PostMapping(value = "/page")
    public PageResponse<VirtualWarehousePageVo> page(@RequestBody @Validated VirtualWarehousePageQuery pageQuery) {
        return virtualWarehouseBizService.page(pageQuery);
    }

    @ApiOperation(value = "虚拟仓库详情")
    @GetMapping(value = "/view")
    public SingleResponse<VirtualWarehouseViewVo> view(@ApiParam(value = "id") Long id) {
        return virtualWarehouseBizService.view(id);
    }

    @ApiOperation(value = "更新仓库库存占比")
    @GetMapping(value = "/updateInventoryRatio")
    public SingleResponse<Boolean> updateInventoryRatio(@ApiParam(value = "id") Long id,
                                                        @ApiParam(value = "inventoryRatio") Integer inventoryRatio) {
        return virtualWarehouseBizService.updateInventoryRatio(id, inventoryRatio);
    }

    @ApiOperation(value = "虚拟仓库导出")
    @PostMapping(value = "/export")
    public Response export(@RequestBody VirtualWarehousePageQuery pageQuery) {
        return virtualWarehouseBizService.export(pageQuery);
    }

    @ApiOperation(value = "操作记录查询")
    @GetMapping(value = "/operateLog")
    public MultiResponse<OperateLog> operateLog(@ApiParam(value = "id") Long id) {
        return virtualWarehouseBizService.operateLog(id);
    }

    @ApiOperation(value = "实仓列表查询")
    @GetMapping(value = "/realWarehouseNos")
    public MultiResponse<WarehouseDO> realWarehouseNos(@ApiParam(value = "id") Long id) {
        return virtualWarehouseBizService.warehouseNos(id);
    }


    @ApiOperation(value = "SKU库存提示语")
    @PostMapping(value = "/skuPrompt")
    public Response export(@RequestBody VwStockConfigDetailCmd vwStockConfigDetailCmd) {
        return stockConfigDetailBizService.checkSkuRatio(vwStockConfigDetailCmd);
    }


    // ------------- 库存明细list -----------

    @ApiOperation(value = "库存设置明细列表")
    @PostMapping(value = "/stockDetail")
    public PageResponse<StockConfigDetailPageVO> stockDetail(@RequestBody @Validated StockConfigDetailPageQuery pageQuery) {
        return stockConfigDetailBizService.page(pageQuery);
    }

    @ApiOperation(value = "单据更新库存明细列表")
    @PostMapping(value = "/updateStockDetail")
    public Response updateStockDetail(@RequestBody @Validated StockConfigDetailSaveCmd cmd) {
        return stockConfigDetailBizService.updateStockConfigDetail(cmd.getSaveList());
    }

    // ------------------- 数据脚本接口 --------------------

    */
/*@ApiOperation(value = "3.4.3迭代，前置数据处理接口")
    @GetMapping(value = "/preDataHandler")
    @Auth(noAuth = true)
    public Response preDataHandler() {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, virtualWarehouseBizService::preDataProcessing);
        return Response.buildSuccess();
    }*//*


    @ApiOperation(value = "清除测试虚拟仓")
    @GetMapping(value = "/clearTestData")
    @Auth(noAuth = true)
    public Response clearTestData(String vwNo) {
        virtualWarehouseBizService.clearTestData(vwNo);
        return Response.buildSuccess();
    }

    @ApiOperation(value = "重新计算虚拟仓/店铺的一级/二级占比的统计数据（锁定库存模式除外）")
    @GetMapping(value = "/recalculate")
    @Auth(noAuth = true)
    public Response recalculate() {
        virtualWarehouseBizService.recalculateGlobalRatioExclusionLock();
        return Response.buildSuccess();
    }

    @ApiOperation(value = "清空库存锁定统计表")
    @GetMapping(value = "/removeLockStatics")
    @Auth(noAuth = true)
    public Response removeLockStatics() {
        virtualWarehouseBizService.removeLockStatics();
        return Response.buildSuccess();
    }

}
*/
