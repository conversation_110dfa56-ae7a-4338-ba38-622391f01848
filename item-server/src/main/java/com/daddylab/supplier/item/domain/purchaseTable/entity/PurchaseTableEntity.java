package com.daddylab.supplier.item.domain.purchaseTable.entity;

import com.daddylab.supplier.item.common.trans.PurchaseTableTransMapper;
import com.daddylab.supplier.item.controller.purchaseTable.dto.PurchaseTableCmd;
import com.daddylab.supplier.item.domain.purchaseTable.gateway.PurchaseTableGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseTable;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName PurchaseTableEntity.java
 * @description
 * @createTime 2021年11月16日 21:19:00
 */
@Data
public class PurchaseTableEntity {
    /**
     * 品类
     */
    private PurchaseTable purchaseTable;

    /**
     * 父品类
     */
    private PurchaseTable parentPurchaseTable;


    public static PurchaseTableEntity buildEntity(PurchaseTableCmd cmd, PurchaseTableGateway purchaseTableGateway) {

        PurchaseTableEntity purchaseTableEntity = new PurchaseTableEntity();
        final PurchaseTable purchaseTable = PurchaseTableTransMapper.INSTANCE.cmdToDo(cmd);
        purchaseTableEntity.setPurchaseTable(purchaseTable);
        return purchaseTableEntity;

    }
}
