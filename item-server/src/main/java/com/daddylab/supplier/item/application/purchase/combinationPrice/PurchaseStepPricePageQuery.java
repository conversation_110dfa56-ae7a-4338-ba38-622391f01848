package com.daddylab.supplier.item.application.purchase.combinationPrice;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年10月08日 1:56 PM
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseStepPricePageQuery extends PageQuery {

  private static final long serialVersionUID = -204551058360245353L;

  @ApiModelProperty(value = "商品名称")
  private String itemName;

  @ApiModelProperty(value = "商品编码")
  private String skuCode;

  @ApiModelProperty(value = "供应商名称")
  private String provider;

  @ApiModelProperty(value = "采购负责人")
  private String buyer;

  @ApiModelProperty(value = "采购负责人id", notes = "兼容")
  private String buyerId;

  @ApiModelProperty(value = "优惠类型 0:货抵款 1:按时间供价 2:当月优惠件数")
  private String favourableType;

  /** 是否纯活动商品 0:否 1:是 */
  @ApiModelProperty(value = "是否纯活动商品 0:否 1:是")
  private String isActive;

  /** 平台名称 0:ALL 1:淘宝 2:抖店 3:小红书 4:自研电商 5:有赞 6:快手 */
  @ApiModelProperty(value = "平台名称 0:ALL 1:淘宝 2:抖店 3:小红书 4:自研电商 5:有赞 6:快手")
  private String platformType;

  /** 方式 0:大促 1:直播 2:无 */
  @ApiModelProperty(value = "方式 0:大促 1:直播 2:无")
  private String activeType;

  @ApiModelProperty(value = "价格类型")
  private String priceCategory;

  @ApiModelProperty("合作方")
  private List<Integer> corpType;

  @ApiModelProperty("业务类型")
  private List<Integer> bizType;

  private Long startTime;
  private Long endTime;
}
