package com.daddylab.supplier.item.infrastructure.spring.web;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.URLUtil;
import com.daddylab.supplier.item.application.auth.ExternalUserLoginLogic;
import com.daddylab.supplier.item.domain.auth.gateway.LoginGateway;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingResponseWrapper;
import org.springframework.web.util.WebUtils;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.util.Enumeration;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * copy org.springframework.web.filter.AbstractRequestLoggingFilter
 *
 * <AUTHOR>
 * @since 2022/10/12
 */
@Slf4j
public class CustomRequestLoggingFilter extends OncePerRequestFilter {

    /**
     * The default value prepended to the log message written <i>before</i> a request is processed.
     */
    public static final String DEFAULT_BEFORE_MESSAGE_PREFIX = "Before request [";

    /**
     * The default value appended to the log message written <i>before</i> a request is processed.
     */
    public static final String DEFAULT_BEFORE_MESSAGE_SUFFIX = "]";

    /**
     * The default value prepended to the log message written <i>after</i> a request is processed.
     */
    public static final String DEFAULT_AFTER_MESSAGE_PREFIX = "After request [";

    /**
     * The default value appended to the log message written <i>after</i> a request is processed.
     */
    public static final String DEFAULT_AFTER_MESSAGE_SUFFIX = "]";

    /**
     * The default value prepended to the log message written <i>after</i> a request is exception.
     */
    public static final String DEFAULT_AFTER_EXCEPTION_PREFIX = "After exception [";

    /**
     * The default value appended to the log message written <i>after</i> a request is exception.
     */
    public static final String DEFAULT_AFTER_EXCEPTION_SUFFIX = "]";

    private static final int DEFAULT_MAX_PAYLOAD_LENGTH = 2048;
    public static final String REQUEST_LOG_PREFIX = "web.api";

    @Nullable
    private LoginGateway loginGateway;

    private ExternalUserLoginLogic externalUserLoginLogic;

    @Nullable
    private Predicate<String> headerPredicate;

    private int maxPayloadLength = DEFAULT_MAX_PAYLOAD_LENGTH;

    private String beforeMessagePrefix = DEFAULT_BEFORE_MESSAGE_PREFIX;

    private String beforeMessageSuffix = DEFAULT_BEFORE_MESSAGE_SUFFIX;

    private String afterMessagePrefix = DEFAULT_AFTER_MESSAGE_PREFIX;

    private String afterMessageSuffix = DEFAULT_AFTER_MESSAGE_SUFFIX;

    private String afterExceptionPrefix = DEFAULT_AFTER_EXCEPTION_PREFIX;

    private String afterExceptionSuffix = DEFAULT_AFTER_EXCEPTION_SUFFIX;

    public void setLoginGateway(@Nullable LoginGateway loginGateway) {
        this.loginGateway = loginGateway;
    }

    public void setExternalUserLoginLogic(ExternalUserLoginLogic externalUserLoginLogic) {
        this.externalUserLoginLogic = externalUserLoginLogic;
    }

    private Logger getLogger(String uri) {
        return LoggerFactory.getLogger((REQUEST_LOG_PREFIX + uri).replaceAll("/", "."));
    }

    private Logger getLogger(HttpServletRequest request) {
        return getLogger(request.getRequestURI());
    }

    /**
     * Return whether the query string should be included in the log message.
     */
    protected boolean isIncludeQueryString(HttpServletRequest request) {
        return true;
    }

    /**
     * Return whether the client address and session id should be included in the log message.
     */
    protected boolean isIncludeClientInfo(HttpServletRequest request) {
        return true;
    }

    /**
     * Return whether the request payload (body) should be included in the log message.
     *
     * @since 3.0
     */
    protected boolean isIncludePayload(HttpServletRequest request) {
        return getLogger(request).isInfoEnabled();
    }

    /**
     * Return whether the request headers should be included in the log message.
     *
     * @since 4.3
     */
    protected boolean isIncludeHeaders(HttpServletRequest request) {
        return getLogger(request).isDebugEnabled();
    }

    /**
     * @param headerPredicate the predicate to use
     * @since 5.2
     */
    public void setHeaderPredicate(@Nullable Predicate<String> headerPredicate) {
        this.headerPredicate = headerPredicate;
    }

    /**
     * The configured {@link #setHeaderPredicate(Predicate) headerPredicate}.
     *
     * @since 5.2
     */
    @Nullable
    protected Predicate<String> getHeaderPredicate() {
        return this.headerPredicate;
    }

    /**
     * Set the maximum length of the payload body to be included in the log message. Default is 50
     * characters.
     *
     * @since 3.0
     */
    public void setMaxPayloadLength(int maxPayloadLength) {
        Assert.isTrue(maxPayloadLength >= 0,
                "'maxPayloadLength' should be larger than or equal to 0");
        this.maxPayloadLength = maxPayloadLength;
    }

    /**
     * Return the maximum length of the payload body to be included in the log message.
     *
     * @since 3.0
     */
    protected int getMaxPayloadLength() {
        return this.maxPayloadLength;
    }

    /**
     * Return the maximum length of the payload body to be included in the log message.
     *
     * @since 3.0
     */
    protected int getMaxPayloadLength(HttpServletRequest request, byte[] buf) {
        return getLogger(request).isDebugEnabled() ? buf.length
                : Math.min(buf.length, getMaxPayloadLength());
    }

    /**
     * Set the value that should be prepended to the log message written
     * <i>before</i> a request is processed.
     */
    public void setBeforeMessagePrefix(String beforeMessagePrefix) {
        this.beforeMessagePrefix = beforeMessagePrefix;
    }

    /**
     * Set the value that should be appended to the log message written
     * <i>before</i> a request is processed.
     */
    public void setBeforeMessageSuffix(String beforeMessageSuffix) {
        this.beforeMessageSuffix = beforeMessageSuffix;
    }

    /**
     * Set the value that should be prepended to the log message written
     * <i>after</i> a request is processed.
     */
    public void setAfterMessagePrefix(String afterMessagePrefix) {
        this.afterMessagePrefix = afterMessagePrefix;
    }

    /**
     * Set the value that should be appended to the log message written
     * <i>after</i> a request is processed.
     */
    public void setAfterMessageSuffix(String afterMessageSuffix) {
        this.afterMessageSuffix = afterMessageSuffix;
    }

    public void setAfterExceptionPrefix(String afterExceptionPrefix) {
        this.afterExceptionPrefix = afterExceptionPrefix;
    }

    public void setAfterExceptionSuffix(String afterExceptionSuffix) {
        this.afterExceptionSuffix = afterExceptionSuffix;
    }

    /**
     * The default value is "false" so that the filter may log a "before" message at the start of
     * request processing and an "after" message at the end from when the last asynchronously
     * dispatched thread is exiting.
     */
    @Override
    protected boolean shouldNotFilterAsyncDispatch() {
        return false;
    }

    /**
     * Forwards the request to the next filter in the chain and delegates down to the subclasses to
     * perform the actual request logging both before and after the request is processed.
     *
     * @see #beforeRequest
     * @see #afterRequest
     */
    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request,
            @NonNull HttpServletResponse response,
            @NonNull FilterChain filterChain)
            throws ServletException, IOException {

        boolean isFirstRequest = !isAsyncDispatch(request);
        HttpServletRequest requestToUse = request;

        if (isIncludePayload(request) && isFirstRequest
                && !(request instanceof RepeatableReadRequestWrapper)) {
            requestToUse = new RepeatableReadRequestWrapper(request);
        }
        HttpServletResponse responseToUse = response;
        if (!(response instanceof ContentCachingResponseWrapper)) {
            responseToUse = new ContentCachingResponseWrapper(response);
        }

        boolean shouldLog = shouldLog(requestToUse);
        if (shouldLog && isFirstRequest) {
            beforeRequest(requestToUse, getBeforeMessage(requestToUse));
        }
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Throwable ex = null;
        try {
            filterChain.doFilter(requestToUse, responseToUse);
        } catch (Throwable throwable) {
            ex = throwable;
        } finally {
            final Object exceptionSaveInAttr = requestToUse.getAttribute(WebUtils.ERROR_EXCEPTION_ATTRIBUTE);
            if (exceptionSaveInAttr instanceof Throwable) {
                ex = (Throwable) exceptionSaveInAttr;
            }
            stopWatch.stop();
            if (shouldLog && !isAsyncStarted(requestToUse)) {
                if (ex == null) {
                    afterRequest(requestToUse, responseToUse,
                            getAfterMessage(requestToUse, responseToUse, stopWatch));
                } else {
                    afterException(requestToUse, responseToUse,
                            getAfterExceptionMessage(requestToUse, responseToUse, ex, stopWatch));
                }
            }
        }
    }

    /**
     * Get the message to write to the log before the request.
     *
     * @see #createMessage
     */
    private String getBeforeMessage(HttpServletRequest request) {
        return createMessage(request, this.beforeMessagePrefix, this.beforeMessageSuffix);
    }

    /**
     * Create a log message for the given request, prefix and suffix.
     * <p>If {@code includeQueryString} is {@code true}, then the inner part
     * of the log message will take the form {@code request_uri?query_string}; otherwise the message
     * will simply be of the form {@code request_uri}.
     * <p>The final message is composed of the inner part as described and
     * the supplied prefix and suffix.
     */
    protected String createMessage(HttpServletRequest request, String prefix, String suffix) {
        StringBuilder msg = new StringBuilder();
        msg.append(prefix);
        msg.append(request.getMethod()).append(" ");
        msg.append(request.getRequestURI());

        if (isIncludeQueryString(request)) {
            String queryString = request.getQueryString();
            if (queryString != null) {
                msg.append('?').append(StringEscapeUtils.escapeJson(queryString));
            }
        }

        if (isIncludeClientInfo(request)) {
            String client = request.getRemoteAddr();
            if (StringUtils.hasLength(client)) {
                msg.append(", client=").append(client);
            }
            if (loginGateway != null) {
                try {
                    if (loginGateway.isLogin()) {
                        final Long loginId = loginGateway.getLoginId();
                        if (loginId != null) {
                            msg.append(", user=").append(loginId);
                        }
                    }
                } catch (Exception ignored) {
                }
            }
            if (externalUserLoginLogic != null && externalUserLoginLogic.isLogin()) {
                msg.append(", external_user=").append(externalUserLoginLogic.getLoginId());
            }
        }

        if (isIncludeHeaders(request)) {
            HttpHeaders headers = new ServletServerHttpRequest(request).getHeaders();
            if (getHeaderPredicate() != null) {
                Enumeration<String> names = request.getHeaderNames();
                while (names.hasMoreElements()) {
                    String header = names.nextElement();
                    if (!getHeaderPredicate().test(header)) {
                        headers.set(header, "masked");
                    }
                }
            }
            msg.append(", headers=").append(StringEscapeUtils.escapeJson(headers.toString()));
        }

        if (isIncludePayload(request)) {
            String payload = getMessagePayload(request);
            if (payload != null) {
                msg.append(", payload='").append(StringEscapeUtils.escapeJson(payload)).append("'");
            }
        }

        msg.append(suffix);
        return msg.toString();
    }

    /**
     * Get the message to write to the log after the request.
     *
     * @see #createMessage
     */
    private String getAfterMessage(HttpServletRequest request, HttpServletResponse response,
            StopWatch stopWatch) {
        return createAfterMessage(request, response, this.afterMessagePrefix,
                this.afterMessageSuffix, stopWatch);
    }

    private String createAfterMessage(HttpServletRequest request, HttpServletResponse response,
            String prefix, String suffix, StopWatch stopWatch) {
        final StringBuilder msg = new StringBuilder();
        msg.append(prefix)
                .append(response.getStatus())
                .append(" ")
                .append(request.getMethod())
                .append(" ")
                .append(request.getRequestURI())
                .append(" ");

        if (isIncludeQueryString(request)) {
            String queryString = request.getQueryString();
            if (queryString != null) {
                msg.append('?').append(StringEscapeUtils.escapeJson(queryString));
            }
        }

        if (isIncludeClientInfo(request)) {
            String client = request.getRemoteAddr();
            if (StringUtils.hasLength(client)) {
                msg.append(", client=").append(client);
            }
            if (loginGateway != null) {
                try {
                    if (loginGateway.isLogin()) {
                        final Long loginId = loginGateway.getLoginId();
                        if (loginId != null) {
                            msg.append(", user=").append(loginId);
                        }
                    }
                } catch (Exception ignored) {
                }
            }
        }

        msg.append(", time=").append(stopWatch.getTotalTimeMillis()).append("ms");
        msg.append(", response='")
                .append(StringEscapeUtils.escapeJson(getResponsePayload(request, response))).append("'");
        msg.append(suffix);
        return msg.toString();
    }

    private String getAfterExceptionMessage(HttpServletRequest request,
            HttpServletResponse response, Throwable ex, StopWatch stopWatch) {
        final StringBuilder msg = new StringBuilder();
        msg.append(afterExceptionPrefix)
                .append(request.getMethod())
                .append(" ")
                .append(request.getRequestURI());

        msg.append(", time=").append(stopWatch.getTotalTimeMillis()).append("ms");
        msg.append(", exception=")
                .append(ExceptionUtil.stacktraceToOneLineString(ex));
        msg.append(afterExceptionSuffix);
        return msg.toString();
    }

    /**
     * Extracts the message payload portion of the message created by
     * {@link #createMessage(HttpServletRequest, String, String)} when
     * {@link #isIncludePayload(HttpServletRequest)} returns true.
     *
     * @since 5.0.3
     */
    @Nullable
    protected String getMessagePayload(HttpServletRequest request) {
        RepeatableReadRequestWrapper wrapper =
                WebUtils.getNativeRequest(request, RepeatableReadRequestWrapper.class);
        if (wrapper != null) {
            try {
                byte[] buf;
                if (Optional.ofNullable(wrapper.getContentType()).filter(StringUtil::isNotBlank)
                        .map(
                                MediaType::parseMediaType).filter(
                                MediaType.APPLICATION_FORM_URLENCODED::includes).isPresent()) {
                    final Charset charset = Charset.forName(wrapper.getCharacterEncoding());
                    buf = URLUtil.buildQuery(wrapper.getParameterMap(), charset).getBytes(charset);
                } else if (wrapper.isHandle()) {
                    buf = IoUtil.readBytes(wrapper.getInputStream());
                } else {
                    return null;
                }
                if (buf.length > 0) {
                    final int theMaxPayloadLength = getMaxPayloadLength(request, buf);
                    int length = Math.min(buf.length, theMaxPayloadLength);
                    final String ellipsis = theMaxPayloadLength < buf.length ? "..." : "";
                    try {
                        return new String(buf, 0, length, wrapper.getCharacterEncoding())
                                + ellipsis;
                    } catch (UnsupportedEncodingException ex) {
                        return "[unknown]";
                    }
                }
            } catch (IOException ignored) {
            }
        }
        return null;
    }

    /**
     * Extracts the message payload portion of the message created by
     * {@link #createMessage(HttpServletRequest, String, String)} when
     * {@link #isIncludePayload(HttpServletRequest)} returns true.
     *
     * @since 5.0.3
     */
    @Nullable
    protected String getResponsePayload(HttpServletRequest request, HttpServletResponse response) {
        ContentCachingResponseWrapper wrapper =
                WebUtils.getNativeResponse(response, ContentCachingResponseWrapper.class);
        if (wrapper != null) {
            byte[] buf = wrapper.getContentAsByteArray();
            if (buf.length > 0) {
                final int theMaxPayloadLength = getMaxPayloadLength(request, buf);
                int length = Math.min(buf.length, theMaxPayloadLength);
                final String ellipsis = theMaxPayloadLength < buf.length ? "..." : "";
                try {
                    return new String(buf, 0, length, wrapper.getCharacterEncoding()) + ellipsis;
                } catch (UnsupportedEncodingException ex) {
                    return "[unknown]";
                } finally {
                    try {
                        wrapper.copyBodyToResponse();
                    } catch (IOException ignored) {
                    }
                }
            }
        }
        return null;
    }


    /**
     * Determine whether to call the {@link #beforeRequest}/{@link #afterRequest} methods for the
     * current request, i.e. whether logging is currently active (and the log message is worth
     * building).
     * <p>The default implementation always returns {@code true}. Subclasses may
     * override this with a log level check.
     *
     * @param request current HTTP request
     * @return {@code true} if the before/after method should get called; {@code false} otherwise
     * @since 4.1.5
     */
    protected boolean shouldLog(HttpServletRequest request) {
        return true;
    }

    /**
     * Concrete subclasses should implement this method to write a log message
     * <i>before</i> the request is processed.
     *
     * @param request current HTTP request
     * @param message the message to log
     */
    protected void beforeRequest(HttpServletRequest request, String message) {
        final Logger logger = getLogger(request);
        if (logger.isDebugEnabled()) {
            logger.debug(message);
        } else if (logger.isInfoEnabled()) {
            logger.info(message);
        }
    }

    /**
     * Concrete subclasses should implement this method to write a log message
     * <i>after</i> the request is processed.
     *
     * @param request  current HTTP request
     * @param response current HTTP response
     * @param message  the message to log
     */
    protected void afterRequest(HttpServletRequest request, HttpServletResponse response,
            String message) {
        final Logger logger = getLogger(request);
        if (logger.isDebugEnabled()) {
            logger.debug(message);
        } else if (logger.isInfoEnabled()) {
            logger.info(message);
        }
    }

    /**
     * Concrete subclasses should implement this method to write a log message
     * <i>after</i> the request is error.
     *
     * @param request  current HTTP request
     * @param response current HTTP response
     * @param message  the message to log
     */
    private void afterException(HttpServletRequest request, HttpServletResponse response,
            String message) {
        final Logger logger = getLogger(request);
        if (logger.isErrorEnabled()) {
            logger.error(message);
        }
    }
}
