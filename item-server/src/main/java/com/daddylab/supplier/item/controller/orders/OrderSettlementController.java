package com.daddylab.supplier.item.controller.orders;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.application.order.settlement.OrderSettlementBizService;
import com.daddylab.supplier.item.application.order.settlement.dto.*;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Collections;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> up
 * @date 2023年08月15日 5:07 PM
 */
@Slf4j
@Api(value = "订单结算相关API", tags = "订单结算相关API")
@RestController
@RequestMapping("/orderSettlement")
public class OrderSettlementController {

  @Resource OrderSettlementBizService orderSettlementBizService;

  @Resource OperateLogBizService operateLogBizService;

  @ApiOperation(value = "查询结算单基础信息")
  @PostMapping("/viewFormInfo")
  public SingleResponse<FormDetailVo> viewFormInfo(@RequestBody FormDetailCmd cmd) {
    return SingleResponse.of(orderSettlementBizService.viewFormInfo(cmd));
  }

  @ApiOperation(value = "查询系统结算单列表")
  @PostMapping("/sysBillPageQuery")
  public PageResponse<SysBillPageVo> sysBillPageQuery(@RequestBody SysBillPageQuery pageQuery) {
    return orderSettlementBizService.sysBillPageQuery(pageQuery);
  }

  @ApiOperation(value = "系统结算单批量处理")
  @PostMapping("/sysBillBatchSettlement")
  public com.alibaba.cola.dto.Response sysBillBatchSettlement(
      @RequestBody SysBillPageQuery pageQuery) {
    return orderSettlementBizService.sysBillBatchSettlement(pageQuery);
  }

  @ApiOperation(value = "查询结算单订货明细excel")
  @PostMapping("/viewFormDetail")
  public PageResponse<FormDetailPageVo> viewFormDetail(@RequestBody FormDetailPageQuery pageQuery) {
    return orderSettlementBizService.viewFormDetailPage(pageQuery);
  }

  @ApiOperation(value = "保存结算单数据")
  @PostMapping("/save")
  public SingleResponse<Boolean> save(@Validated @RequestBody SettlementSaveCmd cmd) {
    orderSettlementBizService.save(cmd);
    return SingleResponse.of(true);
  }

  @ApiOperation(value = "查询已结算结算单列表")
  @PostMapping("/page")
  public PageResponse<SettlementOrderPageVo> page(@RequestBody SettlementOrderPageQuery pageQuery) {
    return orderSettlementBizService.settlementPageQuery(pageQuery);
  }

  @ApiOperation("查询结算单操作日志")
  @GetMapping(value = "/operateLogs")
  public Response operateLogs(
      @ApiParam(value = "结算单id", required = true) @RequestParam Long formId) {
    return operateLogBizService.getOperateLogs(
        Collections.singletonList(OperateLogTarget.ORDER_SETTLEMENT), formId);
  }

  @ApiOperation("获取结算单付款申请金额")
  @GetMapping(value = "/getPayAmount")
  public Response getPayAmount(
      @ApiParam(value = "结算单id", required = true) @RequestParam Long formId) {
    return orderSettlementBizService.getPayAmount(formId, null);
  }

  @ApiOperation("刷新结算时间")
  @PostMapping(value = "/refreshDate")
  public SingleResponse<String> refreshDate(@RequestBody RefreshDataCmd refreshDataCmd) {
    return orderSettlementBizService.refreshSettlementDate(refreshDataCmd.getDetailIds());
  }

  // ----------------------------- 我是分割线 --------------------------------

  @ApiOperation(value = "【导出结算明细】（3sheet合一）")
  @PostMapping("/exportSysBill")
  public SingleResponse<Boolean> exportSysBill(@RequestBody ExportSysBillCmd cmd) {
    orderSettlementBizService.exportBillData(cmd);
    return SingleResponse.of(true);
  }

  @ApiOperation(value = "【导出】结算单订货明细Excel")
  @GetMapping("/exportExcel")
  public SingleResponse<Boolean> exportExcel(
      @ApiParam(value = "结算单id", required = true) @RequestParam Long formId) {
    orderSettlementBizService.exportDetailExcel(formId);
    return SingleResponse.of(true);
  }

  @ApiOperation(value = "【导入】结算单订货明细Excel")
  @PostMapping("/importExcel")
  public Response importExcel(
      @ApiParam(value = "明细Excel", required = true) @RequestParam MultipartFile file,
      @ApiParam(value = "结算单id", required = true) @RequestParam Long formId)
      throws IOException {
    InputStream inputStream = file.getInputStream();
    return orderSettlementBizService.importDetailExcel(inputStream, formId);
  }

  @ApiOperation(value = "【导出结算】结算综合数据导出（4sheet合一）")
  @GetMapping("/exportSettlement")
  public SingleResponse<Boolean> exportSettlement(
      @ApiParam(value = "结算单id", required = true) @RequestParam Long formId) {
    orderSettlementBizService.exportSettlement(formId);
    return SingleResponse.of(true);
  }

  @ApiOperation(value = "批量【导出结算】结算综合数据导出（4sheet合一）")
  @PostMapping("/batchExportSettlement")
  public SingleResponse<Boolean> batchExportSettlement(@RequestBody BatchExportCmd cmd) {
    orderSettlementBizService.exportSettlementPlus(cmd);
    return SingleResponse.of(true);
  }

  // ------------------------------- 我是分割线 ------------------------------

  @ApiOperation(value = "申请删除结算单据")
  @GetMapping("/delApply")
  public SingleResponse<String> delApply(
      @ApiParam(value = "结算单id", required = true) @RequestParam Long formId) {
    return orderSettlementBizService.deleteApply(formId);
  }

  @ApiOperation(value = "确认删除结算单据")
  @GetMapping("/delConfirm")
  public Response delConfirm(
      @ApiParam(value = "结算单id", required = true) @RequestParam Long formId) {
    orderSettlementBizService.confirmDelete(formId);
    return Response.buildSuccess();
  }

  // ------------------------------ 我是分割线，下面是脚本，修锅类接口 ------------------------------

  /**
   * 导出指定结算周期内的所有的退换明细
   *
   * @param time
   * @return
   */
  @GetMapping("/exportRefund")
  @Auth(noAuth = true)
  public SingleResponse<Boolean> exportRefund(@RequestParam Long time) {
    orderSettlementBizService.exportRefundInfo(time);
    return SingleResponse.of(true);
  }

  /**
   * 删除指定编号的结算单信息
   *
   * @param no
   * @return
   */
  @GetMapping("/deleteOrderForm")
  @Auth(noAuth = true)
  public SingleResponse<Boolean> deleteOrderForm(@RequestParam String no) {
    Assert.hasText(no, "结算单好不得为空");
    orderSettlementBizService.deleteOrderForm(Arrays.asList(no.split(StrUtil.COMMA)));
    return SingleResponse.of(true);
  }
}
