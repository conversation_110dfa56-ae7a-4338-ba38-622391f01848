package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import lombok.Data;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2024/7/4
 */
@Data
public class PlatformSkuRefQuery {
    private String shopNo;
    private Collection<String> outerSkuCodes;
    private PlatformItemStatus status;
}
