package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 商品库抽屉模块审核统计数据
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemDrawerModuleAuditStats implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 商品ID
     */
    private Long itemId;

    /**
     * 第几轮审核
     */
    private Integer round;

    /**
     * 法务审核UID
     */
    private Long legalAuditUid;

    /**
     * 法务审核开始时间
     */
    private Long legalAuditStartTime;

    /**
     * 法务审核结束时间
     */
    private Long legalAuditEndTime;

    /**
     * 法务审核话费时间
     */
    private Long legalAuditCostTime;

    /**
     * QC审核UID
     */
    private Long qcAuditUid;

    /**
     * QC审核开始时间
     */
    private Long qcAuditStartTime;

    /**
     * QC审核结束时间
     */
    private Long qcAuditEndTime;

    /**
     * QC审核话费时间
     */
    private Long qcAuditCostTime;

    /**
     * 审批类型 1 商品资料 2 直播话术
     */
    private ItemAuditType type;


    /**
     * 待修改UID
     */
    private Long modifyUid;

    /**
     * 待修改开始时间
     */
    private Long modifyStartTime;

    /**
     * 待修改结束时间
     */
    private Long modifyEndTime;

    /**
     * 待修改话费时间
     */
    private Long modifyCostTime;

}
