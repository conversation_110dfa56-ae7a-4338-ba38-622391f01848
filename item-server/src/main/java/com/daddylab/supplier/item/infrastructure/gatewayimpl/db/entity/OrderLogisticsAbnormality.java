package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.AbnormalStatus;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.ErpLogisticsStatus;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.LogisticsTraceDataSource;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单物流异常记录
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrderLogisticsAbnormality implements Serializable {

  private static final long serialVersionUID = 1L;

  /** id */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /** 创建时间createAt */
  @TableField(fill = FieldFill.INSERT)
  private Long createdAt;

  /** 创建人updateUser */
  @TableField(fill = FieldFill.INSERT)
  private Long createdUid;

  /** 更新时间updateAt */
  @TableField(fill = FieldFill.UPDATE)
  private Long updatedAt;

  /** 更新人updateUser */
  @TableField(fill = FieldFill.UPDATE)
  private Long updatedUid;

  /** 是否已删除 */
  @TableLogic private Long isDel;

  /** 删除时间 */
  @TableField(fill = FieldFill.DEFAULT)
  private Long deletedAt;

  /** 平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城 8:拼多多... */
  private Integer platform;

  /** 物流公司 */
  private String logisticsCompanyName;

  /** 物流单号 */
  private String logisticsNo;

  /** 快递状态 0 待发货，1 已发货，2 已揽收，3 运输中，4 派送中，5 待取件，6 未签收，7 代签收，8 已签收,-10 疑难件 */
  private ErpLogisticsStatus logisticsStatus;

  public void setLogisticsStatus(ErpLogisticsStatus logisticsStatus) {
    this.logisticsStatus = logisticsStatus;
  }

  public void setLogisticsStatus(Integer logisticsStatus) {
    this.logisticsStatus = IEnum.getEnumByValue(ErpLogisticsStatus.class, logisticsStatus);
  }

  /** 异常状态 0:已关闭 1:进行中 2:已处理 */
  private AbnormalStatus abnormalStatus;

  public void setAbnormalStatus(Integer abnormalStatus) {
    this.abnormalStatus = IEnum.getEnumByValue(AbnormalStatus.class, abnormalStatus);
  }

  public void setAbnormalStatus(AbnormalStatus abnormalStatus) {
    this.abnormalStatus = abnormalStatus;
  }

  /** 出库仓库编号 */
  private String stockoutWarehouseNo;

  /** 出库单号 */
  private String stockoutNo;

  /** 原始单号 */
  private String srcOrderNo;

  /** 是否开启预警 */
  private Integer activateWarning;

  /** 物流信息回调ID */
  private Long callbackId;

  /**
   * 物流跟踪，数据源
   *
   * @see com.daddylab.supplier.item.application.afterSaleLogistics.enums.LogisticsTraceDataSource
   */
  private LogisticsTraceDataSource traceSource;

  /** 处理次数 */
  private Integer handleCount;

  /** 备注 */
  private String remark;

  /** 订单物流跟踪ID */
  private Long traceId;

  /** 最新的异常日志ID */
  private Long lastExceptionLogId;

  /**
   * 异常类型 1 发货即将超时16h，2 发货超时24h，3 发货超时48h...
   *
   * @see com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsRootException
   */
  private Integer abnormalType;

  /**
   * 异常类型2
   *
   * @see com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsException
   */
  private Integer abnormalType2;

  /** 处理时间，最后一次处理时间 */
  private Long handleTime;

  /** 关闭时间 */
  private Long closeTime;

  /** 关闭原因 */
  private String closeReason;

  /** 关闭备注 */
  private String closeRemark;

  /** 旺店通交易ID */
  private Long wdtTradeId;

  /** 店铺ID */
  private Long shopId;

  /** 支付时间 */
  private Long payTime;

  /** 发货时间 */
  private Long consignTime;

  /** 交易时间 */
  private Long tradeTime;

  /** 订单金额 */
  private BigDecimal orderAmount;
}
