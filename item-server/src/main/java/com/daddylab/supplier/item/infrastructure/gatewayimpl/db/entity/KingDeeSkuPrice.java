package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class KingDeeSkuPrice implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String skuCode;

    private Long kingDeeBuyerId;

    private String costPrice;


}
