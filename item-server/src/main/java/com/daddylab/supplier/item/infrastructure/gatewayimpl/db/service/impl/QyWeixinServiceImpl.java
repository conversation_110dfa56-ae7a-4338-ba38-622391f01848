package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.QyWeixinDaddyErpProperties;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.QyWeixinService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.QyWeixinFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.GetTokenQyWeixinResult;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * @Author: <PERSON>
 * @Date: 2022/6/1 15:42
 * @Description: QyWeixinService 的实现类
 */
@Service
public class QyWeixinServiceImpl implements QyWeixinService {

    @Autowired
    private QyWeixinFeignClient qyWeixinFeignClient;
    @Autowired
    private QyWeixinDaddyErpProperties qyWxDaddyErpProperties;

    @Override
    public String getTokenWithCache() {
        String corpid = qyWxDaddyErpProperties.getCorpid();
        String corpsecret = qyWxDaddyErpProperties.getCorpsecret();

        String cachePrefix = "daddyErp:qywxaccesstoken:";
        String cacheKey = cachePrefix + corpid + ":" + corpsecret;

        String token = RedisUtil.get(cacheKey);
        if (StringUtils.isNotBlank(token)) {
            return token;
        }

        GetTokenQyWeixinResult result = qyWeixinFeignClient.getToken(corpid, corpsecret);
        // 企业微信的 token 过期时间是 7200 秒，这里有意略短于 7200 秒
        RedisUtil.set(cacheKey, result.getAccessToken(), 7000, TimeUnit.SECONDS);
        return result.getAccessToken();
    }
}
