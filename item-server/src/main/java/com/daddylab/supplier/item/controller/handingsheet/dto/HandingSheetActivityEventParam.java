package com.daddylab.supplier.item.controller.handingsheet.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/24 15:26
 * @Description: 盘货表关联的活动力度信息
 */
@Data
@ApiModel("盘货表活动力度入参")
public class HandingSheetActivityEventParam implements Serializable {
    private static final long serialVersionUID = 1L;

    // 编辑时不看 handingSheetActivityEventId，直接删掉原有的，然后插入最新的
//    @ApiModelProperty(value = "盘货表和活动力度关联关系ID（新增的项不传，编辑项传）")
//    private Long handingSheetActivityEventId;

    @ApiModelProperty(value = "满x元")
    private BigDecimal reachedAmount;

    @ApiModelProperty(value = "满y元")
    private BigDecimal reducedAmount;
}
