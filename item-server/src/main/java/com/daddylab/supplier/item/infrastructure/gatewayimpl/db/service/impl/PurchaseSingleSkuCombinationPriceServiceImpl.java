package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseSingleSkuCombinationPrice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseSingleSkuCombinationPriceMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseSingleSkuCombinationPriceService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 单sku纬度的组合采购价格信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-21
 */
@Service
public class PurchaseSingleSkuCombinationPriceServiceImpl extends DaddyServiceImpl<PurchaseSingleSkuCombinationPriceMapper, PurchaseSingleSkuCombinationPrice> implements IPurchaseSingleSkuCombinationPriceService {

}
