package com.daddylab.supplier.item.application.purchase.combinationPrice;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/** 1活动SKU多件供价。2活动SPU多件供价。3日常SKU多件供价。4日常SPU多件供价。 */
@AllArgsConstructor
@Getter
public enum PriceType implements IEnum<Integer> {
  SKU_ACTIVITY(1, "活动SKU多件供价"),
  SPU_ACTIVITY(2, "活动SPU多件供价"),
  SKU_NORMAL(3, "日常SKU多件供价"),
  SPU_NORMAL(4, "日常SPU多件供价");

  private final Integer value;
  private final String desc;
}
