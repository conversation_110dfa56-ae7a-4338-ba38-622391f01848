package com.daddylab.supplier.item.controller.payment;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.application.payment.PaymentOrderBizService;
import com.daddylab.supplier.item.application.payment.dto.*;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentOrderWriteOffLog;
import io.swagger.annotations.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> up
 * @date 2023年11月22日 1:42 PM
 */
@Slf4j
@Api(value = "付款单相关API", tags = "付款单相关API")
@RestController
@RequestMapping("/payment/order")
@RequiredArgsConstructor
public class PaymentOrderController {

  @Resource PaymentOrderBizService paymentOrderBizService;

  @Resource OperateLogBizService operateLogBizService;

  @ResponseBody
  @ApiOperation(value = "付款单列表分页查询")
  @PostMapping("/page")
  public PageResponse<PaymentPageVo> page(@Validated @RequestBody PaymentPageQuery query) {
    query.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(query.getBusinessLine()));
    return paymentOrderBizService.page(query);
  }

  @ResponseBody
  @ApiOperation(value = "导出")
  @PostMapping("/export")
  public Response export(@RequestBody PaymentPageQuery query) {
    query.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(query.getBusinessLine()));
    return paymentOrderBizService.export(query);
  }

  @ResponseBody
  @ApiOperation(value = "查看付款单详情")
  @GetMapping("/view")
  @ApiImplicitParam(name = "id", dataType = "long", value = "付款单id")
  public SingleResponse<PaymentApplyOrderViewVo> view(Long id) {
    return paymentOrderBizService.view(id);
  }

  @ResponseBody
  @ApiOperation(value = "查看付款单详情-付款明细列表")
  @GetMapping("/viewList")
  @ApiImplicitParam(name = "id", dataType = "long", value = "付款单id")
  public MultiResponse<PaymentApplyDetailVo> viewDetail(Long id) {
    return paymentOrderBizService.viewDetail(id);
  }

  @ResponseBody
  @ApiOperation(value = "付款单保存")
  @PostMapping("/save")
  public SingleResponse<Long> save(@Valid @RequestBody PaymentApplyOrderSaveCmd cmd) {
    return paymentOrderBizService.save(cmd);
  }

  @ResponseBody
  @ApiOperation(value = "移除付款单")
  @GetMapping("/remove")
  @ApiImplicitParam(name = "id", dataType = "long", value = "付款单id")
  public SingleResponse<Boolean> remove(Long id) {
    return paymentOrderBizService.remove(id);
  }

  @ResponseBody
  @ApiOperation(value = "付款单同步到金蝶（必须是同步异常的付款单）")
  @GetMapping("/syncKingDee")
  @Auth(noAuth = true)
  @ApiImplicitParam(name = "id", dataType = "long", value = "付款单id")
  public Response syncKingDee(Long id) {
    return paymentOrderBizService.sync(id);
  }

  @ResponseBody
  @ApiOperation(value = "提交付款申请单")
  @GetMapping("/submit")
  @ApiImplicitParam(name = "id", dataType = "long", value = "付款单id")
  public Response submit(Long id) {
    return paymentOrderBizService.submit(id);
  }

  @ApiOperation(value = "操作记录")
  @GetMapping(value = "/getLog")
  public MultiResponse<OperateLog> getOperateLogs(@RequestParam @ApiParam("付款单id") Long targetId) {
    return operateLogBizService.getOperateLogs(OperateLogTarget.PAYMENT_APPLY_ORDER, targetId);
  }

  @ResponseBody
  @ApiOperation(value = "刷新应付金额")
  @PostMapping("/refreshRightAmount")
  public SingleResponse<PaymentDetailAmountVO> refreshRightAmount(
      @Validated @RequestBody RefreshRightAmountCmd cmd) {
    return paymentOrderBizService.refreshRightAmount(cmd);
  }

  @ResponseBody
  @ApiOperation(value = "修改采购员")
  @GetMapping("/updateBuyer")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "id", dataType = "long", value = "付款单id"),
    @ApiImplicitParam(name = "userId", dataType = "long", value = "采购员id")
  })
  public Response updateBuyer(Long id, Long userId) {
    return paymentOrderBizService.updateBuyer(id, userId);
  }

  @ResponseBody
  @ApiOperation(value = "打印付款单")
  @PostMapping("/print")
  public MultiResponse<PrintOrderVo> print(@Validated @RequestBody PrintCmd cmd) {
    return paymentOrderBizService.print(cmd);
  }

  @ResponseBody
  @ApiOperation(value = "撤回审核")
  @GetMapping("/rollbackAudit")
  public Response rollbackAudit(Long id) {
    return paymentOrderBizService.rollbackAudit(id);
  }

  // ------------------------ 下面是脚本类接口 -------------------------------------

  @GetMapping("/getWriteOffLog")
  @ApiOperation(value = "获取付款单冲销日志")
  @Auth(noAuth = true)
  public MultiResponse<PaymentOrderWriteOffLog> getWriteOffLog(@RequestParam Long id) {
    return paymentOrderBizService.getWriteOffLog(id);
  }

  @GetMapping("/manualWriteOff")
  @ApiOperation(value = "手动触发付款单冲销")
  @Auth(noAuth = true)
  public MultiResponse<String> manualWriteOff(@RequestParam Long id) {
    return paymentOrderBizService.manualWriteOff(id);
  }

  @GetMapping("/rollbackSku")
  @ApiOperation(value = "回滚SKU 的冲销状态")
  @Auth(noAuth = true)
  public Response rollbackSku(@RequestParam Long id) {
    return paymentOrderBizService.rollbackSku(id);
  }

  @ApiOperation(value = "编码相关的支付单信息")
  @GetMapping(value = "/relatedNoInfo")
  @Auth(noAuth = true)
  public SingleResponse<String> relatedNoInfo(@RequestParam String no) {
    return paymentOrderBizService.getPaymentInfoByRelatedNo(no);
  }

  // -------------------------------------------------------------

}
