package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 消息机器人维护表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MessageRobot implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 机器人类型 1:企业微信群机器人
     */
    private Integer type;

    /**
     * 唯一标识
     */
    private String code;

    /**
     * 机器人名称
     */
    private String name;

    /**
     * 模板
     */
    private String template;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 机器人推送webhook
     */
    private String webhook;

    /**
     * 发送频率限制，单位为次，0:不限制
     */
    private Integer rateLimit;

    /**
     * 发送频率限制周期，单位为秒，0:不限制
     */
    private Integer rateInterval;

    /**
     * 负责人
     */
    private String maintainer;

    /**
     * 状态 0:禁用 1:启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;


}
