package com.daddylab.supplier.item.infrastructure.common;

import com.daddylab.supplier.item.infrastructure.enums.IStringEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/5/29
 */
@AllArgsConstructor
@Getter
public enum LoginType implements IStringEnum {
  DEFAULT("login", "内部员工登录（默认）"),
  EXTERNAL_USER("external", "外部用户"),
  ;

  private final String value;
  private final String desc;

  public static LoginType of(Integer value) {
     for (LoginType loginType : LoginType.values()) {
       if (loginType.ordinal() == value) {
         return loginType;
       }
     }
     return null;
  }
}
