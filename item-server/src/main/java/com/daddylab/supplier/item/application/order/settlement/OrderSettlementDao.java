package com.daddylab.supplier.item.application.order.settlement;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderSettlementDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderSettlementForm;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OrderSettlementStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderSettlementDetailService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderSettlementFormService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2023年08月10日 3:50 PM
 */
@Component
public class OrderSettlementDao {

    @Resource
    IOrderSettlementFormService iOrderSettlementFormService;

    @Resource
    IOrderSettlementDetailService iOrderSettlementDetailService;

    @Resource
    OperateLogDomainService operateLogDomainService;


    @Transactional(rollbackFor = Exception.class)
    public void addOrderSettlement(OrderSettlementForm addForm, List<OrderSettlementDetail> addDetailList, String addLog,
                                   List<Long> updateIds) {
        iOrderSettlementFormService.save(addForm);
        Long id = addForm.getId();

        if (CollUtil.isNotEmpty(addDetailList)) {
            iOrderSettlementDetailService.saveBatch(
                    addDetailList.stream().peek(val -> val.setFormId(id)).collect(Collectors.toList()));
        }

        operateLogDomainService.addOperatorLog(UserContext.getUserId(), OperateLogTarget.ORDER_SETTLEMENT, addForm.getId(), addLog);

        // 将系统生成的源结算单据变为已结算，不允许这些单据再进行结算操作。
        if (CollUtil.isNotEmpty(updateIds)) {
            iOrderSettlementFormService.lambdaUpdate()
                    .set(OrderSettlementForm::getStatus, OrderSettlementStatus.CONFIRMED)
                    .in(OrderSettlementForm::getId, updateIds).update();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateOrderSettlement(OrderSettlementForm updateForm, List<OrderSettlementDetail> addDetailList,
                                      List<OrderSettlementDetail> updateList, List<Long> removeIds, List<String> updateLogs) {
        iOrderSettlementFormService.updateById(updateForm);

        if (CollUtil.isNotEmpty(addDetailList)) {
            iOrderSettlementDetailService.saveBatch(addDetailList);
        }
        if (CollUtil.isNotEmpty(updateList)) {
            iOrderSettlementDetailService.updateBatchById(updateList);
        }
        if (CollUtil.isNotEmpty(removeIds)) {
            iOrderSettlementDetailService.lambdaUpdate().in(OrderSettlementDetail::getId, removeIds).remove();
        }
        if (CollUtil.isNotEmpty(updateLogs)) {
            String logStr = String.join("。", updateLogs);
            operateLogDomainService
                    .addOperatorLog(UserContext.getUserId(), OperateLogTarget.ORDER_SETTLEMENT, updateForm.getId(), logStr);

        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void saveExcelInfo(Long id, String staticDoStr, List<OrderSettlementDetail> details) {
        iOrderSettlementDetailService.lambdaUpdate().eq(OrderSettlementDetail::getFormId, id).remove();

        OrderSettlementForm orderSettlementForm = iOrderSettlementFormService.getById(id);
        orderSettlementForm.setStaticInfo(staticDoStr);
        int version = Objects.isNull(orderSettlementForm.getVersion()) ? 1 : orderSettlementForm.getVersion();
        orderSettlementForm.setVersion(version + 1);
        iOrderSettlementFormService.updateById(orderSettlementForm);

        if (CollUtil.isNotEmpty(details)) {
            iOrderSettlementDetailService.saveBatch(details);
        }

        operateLogDomainService.addOperatorLog(UserContext.getUserId(), OperateLogTarget.ORDER_SETTLEMENT, id, "人工导入Excel");
    }

    /**
     * @param form0       结算单据
     * @param oldRelateId 老的账单id
     * @param newDetails  新保存的明细
     */
    @Transactional(rollbackFor = Exception.class)
    public void settlementChangeUpdate(OrderSettlementForm form0, List<OrderSettlementDetail> newDetails,
                                       String oldRelateId, List<Long> newBillIds) {
        // 修改旧的结算单据。已结算->待结算
        if (StringUtils.hasText(oldRelateId)) {
            List<Long> oldIds = Arrays.stream(oldRelateId.split(",")).map(Long::valueOf).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(oldIds)) {
                iOrderSettlementFormService.lambdaUpdate().set(OrderSettlementForm::getStatus, OrderSettlementStatus.WAIT_CONFIRM)
                        .in(OrderSettlementForm::getId, oldIds).update();
            }
        }
        // 移除结算单旧的明细
        iOrderSettlementDetailService.lambdaUpdate().eq(OrderSettlementDetail::getFormId, form0.getId()).remove();

        // 保存新的关联关系
        iOrderSettlementFormService.updateById(form0);
        // 保存新的结算单明细
        if (CollUtil.isNotEmpty(newDetails)) {
            iOrderSettlementDetailService.saveBatch(newDetails);
        }
        if (CollUtil.isNotEmpty(newBillIds)) {
            iOrderSettlementFormService.lambdaUpdate()
                    .set(OrderSettlementForm::getStatus, OrderSettlementStatus.CONFIRMED)
                    .in(OrderSettlementForm::getId, newBillIds).update();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void remove(Long id) {
        QueryWrapper<OrderSettlementDetail> wrapper = new QueryWrapper<>();
        wrapper.eq("form_id", id);
        iOrderSettlementDetailService.removeWithTime(wrapper);
        boolean b = iOrderSettlementFormService.removeByIdWithTime(id);
        if (b) {
            operateLogDomainService.addOperatorLog(UserContext.getUserId(), OperateLogTarget.ORDER_SETTLEMENT, id, "删除结算单");
        }
    }

}
