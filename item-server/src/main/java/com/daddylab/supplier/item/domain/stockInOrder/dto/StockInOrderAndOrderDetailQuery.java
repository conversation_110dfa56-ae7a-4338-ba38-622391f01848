package com.daddylab.supplier.item.domain.stockInOrder.dto;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("采购入库单以及明细列表返回数据模型")
public class StockInOrderAndOrderDetailQuery extends PageQuery {
    private Long offsetVal;
    @ApiModelProperty(value = "入库单号")
    private String no;
    @ApiModelProperty(value = "供应商id")
    private Long providerId;
    @ApiModelProperty(value = "收料组织id")
    private Long organizationId;
    @ApiModelProperty(value = "采购单号")
    private String purchaseOrderNo;
    @ApiModelProperty(value = "入库状态")
    private Integer state;
    @ApiModelProperty(value = "入库开始日期")
    private Long receiptTimeStart;
    @ApiModelProperty(value = "入库结束日期")
    private Long receiptTimeEnd;
    @ApiModelProperty(value = "商品sku")
    private String itemSkuCode;
    @ApiModelProperty(value = "商品名称")
    private String itemName;
    @ApiModelProperty(value = "采购员")
    private Long buyerUserId;
    @ApiModelProperty(value = "采购组")
    private Long purchaseOrganizationId;
    @ApiModelProperty(value = "入库仓库")
    private String warehouseNo;
    @ApiModelProperty("创建人")
    private List<Long> createdUidList;
    private List<Long> neCreatedUidList;

    private List<Integer> businessLine;
}
