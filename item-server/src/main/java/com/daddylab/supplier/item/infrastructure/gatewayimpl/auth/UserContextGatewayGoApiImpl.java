package com.daddylab.supplier.item.infrastructure.gatewayimpl.auth;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.auth.PermRefreshManager;
import com.daddylab.supplier.item.domain.auth.entity.SysMenu;
import com.daddylab.supplier.item.domain.auth.entity.SysResource;
import com.daddylab.supplier.item.domain.auth.enums.ResourceType;
import com.daddylab.supplier.item.domain.auth.factories.MenuFactory;
import com.daddylab.supplier.item.domain.auth.trans.AuthAssembler;
import com.daddylab.supplier.item.domain.auth.types.SimpleRole;
import com.daddylab.supplier.item.domain.auth.types.UserAccessControlInfo;
import com.daddylab.supplier.item.domain.departments.DepartmentGateway;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.accessControl.*;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.goclient.Rows;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/3/30
 */
@Slf4j
@Service("UserContextGatewayGoApiImpl")
@Primary
public class UserContextGatewayGoApiImpl extends UserContextGatewayImplBase {
    private final UserGateway userGateway;
    private final AccessControlAPI accessControlAPI;
    private final Long systemId;
    private List<CasbinPolicy> casbinPolicies;
    private List<SysResource> resources;
    private LocalDateTime lastApiQueryTime;
    private final DepartmentGateway departmentGateway;

    public UserContextGatewayGoApiImpl(PermRefreshManager permRefreshManager,
                                       UserGateway userGateway,
                                       AccessControlAPI accessControlAPI,
                                       @Value("${org.casbin.dom:6}") Long systemId,
                                       RedissonClient redissonClient, DepartmentGateway departmentGateway) {
        super(permRefreshManager, redissonClient);

        this.userGateway = userGateway;
        this.accessControlAPI = accessControlAPI;
        this.systemId = systemId;
        this.departmentGateway = departmentGateway;
    }

    @Override
    UserContext.UserInfo getUserContext0(long userId) {
        UserContext.UserInfo userinfo;
        final UserContext.UserInfo.UserInfoBuilder builder = UserContext.UserInfo.builder();
        builder.userId(userId);

        //查询当前用户信息
        final StaffInfo staffInfo = userGateway.queryStaffInfoById(userId);
        if (staffInfo == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未查询到当前身份的用户信息");
        }
        //真实姓名
        builder.userName(staffInfo.getUserName());
        //花名
        builder.nickname(staffInfo.getNickname());
        //邮件地址
        builder.mail(staffInfo.getEmail());
        //登录名
        builder.loginName(staffInfo.getLoginName());
        //手机号
        builder.mobileNum(staffInfo.getMobile());

        //查询用户有权限访问的资源
        final UserAccessControlInfo userAccessControlInfo = getUserAcInfo(userId);
        builder.resourceCodes(userAccessControlInfo.getCodesOfResourcesOwned());
        builder.roles(userAccessControlInfo.getRoles());
        builder.roleIds(userAccessControlInfo.getRoles().stream().map(SimpleRole::getRoleId).collect(Collectors.toList()));
        builder.menus(userAccessControlInfo.getMenus());
        builder.queryTime(LocalDateTime.now());
        builder.deptId(userAccessControlInfo.getDeptId());
        builder.dept(userAccessControlInfo.getDept());
        builder.topDeptId(userAccessControlInfo.getTopDeptId());
        builder.topDept(userAccessControlInfo.getTopDept());

        userinfo = builder.build();
        return userinfo;
    }

    private UserAccessControlInfo getUserAcInfo(Long userId) {
        final UserAccessControlInfo userAccessControlInfo = new UserAccessControlInfo(userId);

        final StaffAcInfoPageQuery staffAcInfoQuery = new StaffAcInfoPageQuery(1, 1);
        staffAcInfoQuery.setStatus(1);
        staffAcInfoQuery.setUserId(userId);
        final Rsp<Rows<StaffAcInfo>> staffAcInfoListRsp = accessControlAPI.staffAcInfoPageQuery(staffAcInfoQuery);
        if (!staffAcInfoListRsp.isSuccess()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "从权限系统查询用户权限信息失败");
        }
        final List<StaffAcInfo> staffAcInfos = staffAcInfoListRsp.getData().getRows();
        if (CollUtil.isEmpty(staffAcInfos)) {
            return userAccessControlInfo;
        }
        final StaffAcInfo staffAcInfo = staffAcInfos.get(0);

        userAccessControlInfo.setDeptId(staffAcInfo.getDeptId());
        userAccessControlInfo.setDept(staffAcInfo.getDept());

        final Optional<Dept> topDepartment = departmentGateway.getTopDepartment(staffAcInfo.getDeptId());
        userAccessControlInfo.setTopDeptId(topDepartment.map(Dept::getId).orElse(0L));
        userAccessControlInfo.setTopDept(topDepartment.map(Dept::getName).orElse(""));

        //是否需要刷新权限策略
        refreshPoliciesAndResourcesIfNecessary();

        final List<Role> staffAcInfoRoles = staffAcInfo.getSysRoles().stream().filter(sysRoles -> Objects.equals(sysRoles.getSysId(), systemId))
                .findFirst().map(StaffAcInfo.SysRoles::getRoles).orElse(Collections.emptyList());
        if (CollUtil.isEmpty(staffAcInfoRoles)) {
            return userAccessControlInfo;
        }

        //提取角色ID
        final List<Long> roleIds = staffAcInfoRoles.stream()
                .map(Role::getRoleId)
                .collect(Collectors.toList());

        //构造角色
        final List<SimpleRole> roles = staffAcInfoRoles.stream()
                .map(role -> new SimpleRole(role.getRoleId(), role.getRoleName()))
                .collect(Collectors.toList());
        userAccessControlInfo.setRoles(roles);

        //CasbinRule表中角色有前缀r
        final List<String> casbinRoleIds = roleIds.stream().map(it -> "r" + it)
                .collect(Collectors.toList());

        //角色配置的资源
        final List<CasbinPolicy> casbinPoliciesMatched = casbinPolicies.stream()
                .filter(casbinPolicy -> Objects.equals(casbinPolicy.getV1(), String.valueOf(systemId))
                        && casbinRoleIds.contains(casbinPolicy.getV0()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(casbinPoliciesMatched)) {
            return userAccessControlInfo;
        }

        final List<Long> pageResourcesCodeOwned = new ArrayList<>();
        final List<String> apiResourcesCodeOwned = new ArrayList<>();
        for (CasbinPolicy casbinRule : casbinPoliciesMatched) {
            if (ReUtil.isMatch("^\\d+$", casbinRule.getV2())) {
                pageResourcesCodeOwned.add(Long.parseLong(casbinRule.getV2()));
            } else {
                apiResourcesCodeOwned.add(casbinRule.getV2());
            }
        }

        final List<SysResource> resourcesOwned = resources.stream()
                .filter(it -> pageResourcesCodeOwned.contains(it.getId()) || apiResourcesCodeOwned.contains(it.getApiUrl()))
                .collect(Collectors.toList());
        final Map<ResourceType, List<String>> codesOfResourcesOwned = extractCodesGroupingByType(resourcesOwned);
        userAccessControlInfo.setCodesOfResourcesOwned(codesOfResourcesOwned);

        final ImmutableList<String> frontResourcesType = ImmutableList.of(
                ResourceType.PAGE.getValue(),
                ResourceType.LEFT_MENU.getValue(),
                ResourceType.BUTTON.getValue(),
                ResourceType.MENU.getValue()
        );
        //筛选出用户拥有的前端资源
        final List<SysResource> frontResourcesOwned = resources.stream()
                .filter(it -> frontResourcesType.contains(it.getType()))
                .collect(Collectors.toList());
        final List<SysMenu> menus = MenuFactory.buildMenuList(systemId, codesOfResourcesOwned, frontResourcesOwned);
        userAccessControlInfo.setMenus(menus);

        return userAccessControlInfo;
    }

    private void refreshPoliciesAndResourcesIfNecessary() {
        if (casbinPolicies == null || permRefreshManager.isNeedRefreshPerm(lastApiQueryTime)) {
            final Rsp<List<CasbinPolicy>> casbinPoliciesRsp = accessControlAPI.casbinPolicies(systemId);
            if (!casbinPoliciesRsp.isSuccess()) {
                throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "从权限系统查询权限策略失败");
            }
            casbinPolicies = casbinPoliciesRsp.getData();
            final Rsp<List<Resource>> resourcesRsp = accessControlAPI.resourcesQuery(systemId);
            if (!resourcesRsp.isSuccess()) {
                throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "从权限系统查询系统资源失败");
            }
            resources = AuthAssembler.INSTANCE.acResourcesToSysResources(resourcesRsp.getData());
            lastApiQueryTime = LocalDateTime.now();
        }
    }


}
