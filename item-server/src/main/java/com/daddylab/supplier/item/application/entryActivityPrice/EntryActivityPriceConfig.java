package com.daddylab.supplier.item.application.entryActivityPrice;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/10/6
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "entry-activity-price")
@RefreshScope
public class EntryActivityPriceConfig {
    private String confirmUrl = "https://p.dlab.cn/erp/?id=%s";
    private String tokenKey = "123123666";
    private boolean smsEnabled;
    private boolean emailEnabled;
}
