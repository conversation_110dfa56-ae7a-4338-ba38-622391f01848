package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.UserVisitDaily;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.UserVisitDailyMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IUserVisitDailyService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户每日访问记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@Service
public class UserVisitDailyServiceImpl extends DaddyServiceImpl<UserVisitDailyMapper, UserVisitDaily> implements IUserVisitDailyService {

}
