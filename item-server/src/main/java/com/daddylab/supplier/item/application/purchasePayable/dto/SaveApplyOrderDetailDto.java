package com.daddylab.supplier.item.application.purchasePayable.dto;

import com.daddylab.supplier.item.application.purchasePayable.vo.ApplyPayOrderDetailListVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年02月14日 4:43 PM
 */
@Data
@ApiModel("保存采购付款申请单sku列表数据请求参数")
public class SaveApplyOrderDetailDto extends ApplyPayOrderDetailListVO {

    @ApiModelProperty("sku修正数量")
    private Integer fixedQuantity;

    @ApiModelProperty("sku修正金额")
    private BigDecimal fixedTotalAmount;

    private String errorLog;

    private Integer isCustomize;
}
