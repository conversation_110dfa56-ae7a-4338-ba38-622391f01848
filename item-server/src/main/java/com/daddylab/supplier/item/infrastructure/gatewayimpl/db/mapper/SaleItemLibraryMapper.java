package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.saleItem.query.SaleItemLibraryQueryPage;
import com.daddylab.supplier.item.application.saleItem.vo.SaleItemLibraryVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.SaleItemLibrary;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 销售商品库 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Repository
public interface SaleItemLibraryMapper extends DaddyBaseMapper<SaleItemLibrary> {

    int count(@Param("queryPage") SaleItemLibraryQueryPage queryPage);
    List<SaleItemLibraryVO> queryPage(@Param("queryPage") SaleItemLibraryQueryPage queryPage);
}
