package com.daddylab.supplier.item.application.drawer.impl.copySupport;

import com.daddylab.supplier.item.application.drawer.ItemDrawerCopyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerLiveVerbalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @class ItemDrawerImageCopyServiceImpl.java
 * @description 描述类的作用
 * @date 2024-04-09 16:35
 * @deprecated 由于抽屉审核计划复制时需要获取到新老直播话术记录的ID映射，所以将这边的逻辑复制到那边完成
 */
@Service
public class ItemDrawerLiveVerbalCopyServiceImpl implements ItemDrawerCopyService {

    @Autowired
    private IItemDrawerLiveVerbalService itemDrawerLiveVerbalService;
    @Resource
    private ItemDrawerImageCopyServiceImpl itemDrawerImageCopyService;

    @Override
    public void handler(ItemDrawer sourceDrawer, ItemDrawer targetDrawer) {
//        // 删除
//        List<ItemDrawerLiveVerbal> targetLiveVerbals = itemDrawerLiveVerbalService.getByItemId(targetDrawer.getItemId());
//        List<Long> deleteIds = targetLiveVerbals.stream().map(ItemDrawerLiveVerbal::getId).collect(Collectors.toList());
//        itemDrawerLiveVerbalService.removeByIdsWithTime(deleteIds);
//
//        // 新增
//        List<ItemDrawerLiveVerbal> sourceLiveVerbals = itemDrawerLiveVerbalService.getByItemId(sourceDrawer.getItemId());
//        List<ItemDrawerLiveVerbal> newSourceLiveVerbals = sourceLiveVerbals.stream().peek(liveVerbal -> {
//            liveVerbal.setItemDrawerId(targetDrawer.getId());
//            liveVerbal.setItemId(targetDrawer.getItemId());
//        }).collect(Collectors.toList());
//        itemDrawerLiveVerbalService.saveBatch(newSourceLiveVerbals);
//
//        // 直播话术的图片信息拷贝
//        itemDrawerImageCopyService.handler0(sourceDrawer, targetDrawer, Boolean.TRUE);
    }
}
