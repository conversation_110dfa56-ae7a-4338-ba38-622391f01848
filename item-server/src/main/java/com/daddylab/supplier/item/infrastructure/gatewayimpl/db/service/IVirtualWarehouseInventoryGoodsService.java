//package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;
//
//import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.VirtualWarehouseInventoryGoods;
//
//import java.util.List;
//
///**
// * <p>
// * 虚拟仓库存信息（商品纬度） 服务类
// * </p>
// *
// * <AUTHOR>
// * @since 2024-02-29
// */
//public interface IVirtualWarehouseInventoryGoodsService extends IDaddyService<VirtualWarehouseInventoryGoods> {
//
//    List<VirtualWarehouseInventoryGoods> selectByVirtualWarehouseIdsAndSkuNos(List<Long> virtualWarehouseIds, List<String> skuCodes);
//}
