package com.daddylab.supplier.item.controller.saleItem;

import cn.hutool.http.HttpUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.saleItem.dto.SaleItemLibraryCmd;
import com.daddylab.supplier.item.application.saleItem.query.SaleItemLibraryQueryPage;
import com.daddylab.supplier.item.application.saleItem.service.SaleItemLibraryBizService;
import com.daddylab.supplier.item.application.saleItem.vo.SaleItemLibraryVO;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.dto.IdCmd;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseOperate;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.utils.HttpHeaderUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Api(value = "销售商品库API", tags = "销售商品库API")
@RestController
@RequestMapping("/salesItem/library")
public class SaleItemLibraryController {

    @Autowired
    private SaleItemLibraryBizService saleItemLibraryBizService;


    @ResponseBody
    @ApiOperation(value = "分页查询销售商品库商品")
    @PostMapping("/page")
    public PageResponse<SaleItemLibraryVO> queryPage(@RequestBody SaleItemLibraryQueryPage queryPage) {
        if(StringUtils.isNotEmpty(queryPage.getItemId())){
            queryPage.setItemId(queryPage.getItemId().trim());
        }
        if(StringUtils.isNotEmpty(queryPage.getItemSkuCode())){
            queryPage.setItemSkuCode(queryPage.getItemSkuCode().trim());
        }
        if(StringUtils.isNotEmpty(queryPage.getGoodsName())){
            queryPage.setGoodsName(queryPage.getGoodsName().trim());
        }
        if(StringUtils.isNotEmpty(queryPage.getBuyerName())){
            queryPage.setBuyerName(queryPage.getBuyerName().trim());
        }
        return saleItemLibraryBizService.queryPage(queryPage);
    }

    @ResponseBody
    @ApiOperation(value = "根据skuCode查询数据")
    @GetMapping("/getBySkuCode")
    public SingleResponse<SaleItemLibraryVO> getBySkuCode(@RequestParam("skuCode") String skuCode) {
        return saleItemLibraryBizService.getBySkuCode(skuCode);
    }


    @ApiOperation(value = "新增/修改")
    @PostMapping("/createOrUpdate")
    public Response createOrUpdate(@RequestBody @Validated SaleItemLibraryCmd cmd) {
        return saleItemLibraryBizService.createOrUpdate(cmd);
    }


    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public Response delete(@RequestBody IdCmd idCmd) {
        return saleItemLibraryBizService.delete(idCmd.getId());
    }


    @GetMapping(value = "/excelTemplate")
    @ApiOperation("下载Excel模板")
    @Auth(noAuth = true)
    public void excelTemplate(HttpServletResponse response) {
        try {
            HttpHeaderUtil.setXlsxAttachmentHeaders(response, "销售商品库模板");
            HttpUtil.download(saleItemLibraryBizService.getExcelTemplateUrl(), response.getOutputStream(), false);
        } catch (IOException e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.FILE_UPLOAD_ERROR, "下载文件失败，输出流写入异常");
        }
    }


    @ApiOperation(value = "导入")
    @PostMapping(value = "/importExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public SingleResponse<PurchaseOperate> importExcel(@RequestParam("file") MultipartFile file,
                                                       @RequestParam("confirm") Boolean confirm) {
        try {
            return saleItemLibraryBizService.importExcel(file.getInputStream(), confirm);
        } catch (IOException e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.FILE_UPLOAD_ERROR, "导入Excel失败，读取文件异常");
        }
    }


    @ApiOperation(value = "导出")
    @PostMapping("/exportExcel")
    public Response exportExcel(@RequestBody SaleItemLibraryQueryPage queryPage) {
        saleItemLibraryBizService.exportExcel(queryPage);
        return Response.buildSuccess();
    }
}
