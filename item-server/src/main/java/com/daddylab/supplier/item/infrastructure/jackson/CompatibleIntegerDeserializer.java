package com.daddylab.supplier.item.infrastructure.jackson;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.NumberDeserializers;
import com.fasterxml.jackson.databind.deser.std.StdScalarDeserializer;

import java.io.IOException;

/**
 * 在Jackson标准整形反序列化器的基础上增加一些兼容性能力
 *
 * <AUTHOR>
 */
public class CompatibleIntegerDeserializer extends StdScalarDeserializer<Integer> {
    NumberDeserializers.IntegerDeserializer stdDeserializer;

    public CompatibleIntegerDeserializer() {
        super(Integer.class);
        stdDeserializer = new NumberDeserializers.IntegerDeserializer(Integer.class, null);
    }

    @Override
    public Integer deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        JsonToken t = p.getCurrentToken();
        if (t == JsonToken.VALUE_TRUE) {
            return 1;
        }
        if (t == JsonToken.VALUE_FALSE) {
            return 0;
        }
        return stdDeserializer.deserialize(p, ctxt);
    }
}
