package com.daddylab.supplier.item.application.drawer;

import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
import com.daddylab.supplier.item.types.itemDrawer.ItemLaunchAuditTerminalEvent;
import com.google.common.eventbus.Subscribe;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
@EventBusListener(value = "itemDrawerAuditTerminalEventListener")
public class ItemDrawerAuditTerminalEventListener {


    @Subscribe
    public void listener(ItemLaunchAuditTerminalEvent event) {

    }

}
