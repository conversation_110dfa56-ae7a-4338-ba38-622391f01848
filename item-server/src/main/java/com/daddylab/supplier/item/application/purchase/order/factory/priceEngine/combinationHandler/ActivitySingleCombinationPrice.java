package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.combinationHandler;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.BaseProcess;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.PriceProcessor;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseSingleSkuCombinationPrice;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Semaphore;

/**
 * 活动价-单sku纬度，任意数量组合价
 * 只处理单品
 *
 * <AUTHOR> up
 * @date 2022年10月24日 10:46 AM
 */
@Service
@Slf4j
public class ActivitySingleCombinationPrice extends BaseProcess implements PriceProcessor {

    private final Semaphore semaphore = new Semaphore(4);


    @Override
    public Boolean doPriceProcess(TimeBO timeBO) {
        log.info("ActivitySingleCombinationPrice doPriceProcess start");

        List<PurchaseSingleSkuCombinationPrice> list = getSingleCombinationPriceByType(timeBO, 2);
        if (CollUtil.isEmpty(list)) {
            log.info("ActivitySingleCombinationPrice doPriceProcess finish.purchase price is empty");
            return true;
        }

        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        // activity code && activity code prices
        list.forEach(entity -> ThreadUtil.execute(PoolEnum.PURCHASE_POOL, () -> {
            try {
                semaphore.acquire();
                log.info("ActivitySingleCombinationPrice doPriceProcess start skuCode:{}", entity.getCode());
                singleCombinationPriceHandler(entity, timeBO.getOperateMonth(),2);
                log.info("ActivitySingleCombinationPrice doPriceProcess finish skuCode:{}", entity.getCode());
            } catch (Exception e) {
                log.error("ActivitySingleCombinationPrice doPriceProcess fail price:{}", JsonUtil.toJson(entity), e);
            } finally {
                semaphore.release();
                countDownLatch.countDown();
            }
        }));

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("[{}] ActivitySingleCombinationPrice doPriceProcess await InterruptedException", timeBO.getOperateMonth(), e);
        }

        log.info("ActivitySingleCombinationPrice doPriceProcess finish");
        return true;
    }
}
