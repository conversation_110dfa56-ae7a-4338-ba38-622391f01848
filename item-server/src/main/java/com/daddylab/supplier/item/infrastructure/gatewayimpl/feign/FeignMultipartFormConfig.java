package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;

import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @since 2023/11/27
 */
@Configuration
public class FeignMultipartFormConfig {
    @Bean
    @Primary
    public Encoder multipartFormEncoder() {
        return new SpringFormEncoder(
                new SpringEncoder(
                        () ->
                                new HttpMessageConverters(
                                        new RestTemplate().getMessageConverters())));
    }
}
