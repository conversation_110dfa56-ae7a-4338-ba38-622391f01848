package com.daddylab.supplier.item.application.platformItemSkuInventory.support;

import com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params.PlatformSkuSyncInfo;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.third.config.PddConfig;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.pdd.pop.sdk.http.PopBaseHttpResponse;
import com.pdd.pop.sdk.http.PopHttpClient;
import com.pdd.pop.sdk.http.api.pop.request.PddGoodsDetailGetRequest;
import com.pdd.pop.sdk.http.api.pop.request.PddGoodsListGetRequest;
import com.pdd.pop.sdk.http.api.pop.response.PddGoodsDetailGetResponse;
import com.pdd.pop.sdk.http.api.pop.response.PddGoodsListGetResponse;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.FlowBuilder;
import org.springframework.batch.core.job.flow.Flow;
import org.springframework.batch.core.job.flow.support.SimpleFlow;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode.GLOBAL;

/**
 * <AUTHOR>
 * @since 2024/4/17
 */
@Service
@Slf4j
public class PddItemSyncServiceImpl extends
        AbstractPlatformItemSkuSync<PddItemSyncServiceImpl.PddGoodsItemContainer, List<PlatformSkuSyncInfo>> {
    @Autowired
    private PddConfig pddConfig;
    
    @Override
    public void fullDoseSync() {
        try {
            final PopHttpClient httpClient = pddConfig.getHttpClient();
            final List<ShopAuthorization> shopAuthorizations = shopAuthorizationService.listAuthorizations(
                    Platform.PDD);
            log.info("[平台商品同步][拼多多]开始同步，店铺={}",
                    shopAuthorizations.stream().map(ShopAuthorization::getSn).collect(
                            Collectors.toList()));
            final String platform = defaultType().getDesc();
            final SimpleAsyncTaskExecutor executor =
                    new SimpleAsyncTaskExecutor(defaultType().name() + "-fullDoseSync");
            executor.setConcurrencyLimit(4);
            final ThreadPoolTaskExecutor threadPool = ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL);
            final ArrayList<Flow> stepFlows = new ArrayList<>();
            for (ShopAuthorization shopAuthorization : shopAuthorizations) {
                if (shopAuthorization.isExpired()) {
                    Alert.text(GLOBAL, "拼多多店铺授权已过期，店铺编码：" + shopAuthorization.getSn());
                    continue;
                }
                final String shopNo = shopAuthorization.getSn();
                final ItemReader<PddItemSyncServiceImpl.PddGoodsItemContainer> itemReader = getItemReader(iPage -> {
                    try {
                        final PddGoodsListGetRequest request = new PddGoodsListGetRequest();
                        request.setPage((int) iPage.getCurrent());
                        request.setPageSize((int) iPage.getSize());
                        final PddGoodsListGetResponse pddGoodsListGetResponse = httpClient.syncInvoke(request,
                                shopAuthorization.getAccessToken());
                        final PopBaseHttpResponse.ErrorResponse errorResponse =
                                pddGoodsListGetResponse.getErrorResponse();
                        if (errorResponse != null) {
                            throw new RuntimeException(String.format("%s %s",
                                    errorResponse.getSubCode(),
                                    errorResponse.getSubMsg()));
                        }
                        final PddGoodsListGetResponse.GoodsListGetResponse goodsListGetResponse =
                                pddGoodsListGetResponse.getGoodsListGetResponse();
                        final ArrayList<CompletableFuture<?>> completableFutures = new ArrayList<>();
                        final List<PddGoodsListGetResponse.GoodsListGetResponseGoodsListItem> goodsList =
                                goodsListGetResponse.getGoodsList();
                        final PddGoodsItemContainer[] pddGoodsItemContainers =
                                new PddGoodsItemContainer[goodsList.size()];
                        for (int i = 0; i < goodsList.size(); i++) {
                            int index = i;
                            final PddGoodsListGetResponse.GoodsListGetResponseGoodsListItem goodsListItem =
                                    goodsList.get(index);
                            final CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                                final PddGoodsItemContainer pddGoodsDetail =
                                        getPddGoodsDetail(goodsListItem.getGoodsId(),
                                                shopAuthorization);
                                pddGoodsItemContainers[index] = pddGoodsDetail;
                            }, threadPool);
                            completableFutures.add(future);
                        }
                        
                        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();
                        final ArrayList<PddGoodsItemContainer> records = new ArrayList<>(Arrays.asList(
                                pddGoodsItemContainers));
                        iPage.setRecords(records);
                        iPage.setTotal(goodsListGetResponse.getTotalCount());
                        return iPage;
                    } catch (Exception e) {
                        log.error("[拼多多][商品列表请求][店铺{}][第{}页]{}",
                                shopNo,
                                iPage.getCurrent(),
                                e.getMessage(),
                                e);
                        throw ExceptionPlusFactory.bizException(ErrorCode.PDD_ERROR,
                                "请求商品列表请求接口异常:" + e.getMessage());
                    }
                }, 100);
                final Step step = buildStep(itemReader, platformItemSyncConfig.getChunkSize(), shopNo);
                final SimpleFlow shopFlow = new FlowBuilder<SimpleFlow>(platform + shopNo).start(step).build();
                stepFlows.add(shopFlow);
            }
            final SimpleFlow flow = new FlowBuilder<SimpleFlow>(defaultType().name() + "-fullDoseSync")
                    .split(executor)
                    .add(stepFlows.toArray(new Flow[0]))
                    .next(stepBuilderFactory.get(String.format("[%s]-StatisticsStep", defaultType().getDesc()))
                            .tasklet(getStatisticsTasklet())
                            .build())
                    .build();
            final Job job = jobBuilderFactory.get(PLATFORM_ITEM_SKU_SYNC_JOB + "-" + defaultType().name())
                    .start(flow)
                    .build()
                    .build();
            JobParametersBuilder parametersBuilder = new JobParametersBuilder();
            parametersBuilder.addDate("date", new Date());
            JobExecution run = jobLauncher.run(job, parametersBuilder.toJobParameters());
            log.info("[抖店平台商品同步]任务执行结果={}", run.getStatus());
        } catch (Exception e) {
            log.error("[抖店平台商品同步]任务运行异常", e);
        }
        
    }
    
    @Override
    public Platform defaultType() {
        return Platform.PDD;
    }
    
    @Data
    public static class PddGoodsItemContainer {
        private PddGoodsDetailGetResponse.GoodsDetailGetResponse goodsDetail;
        private String shopNo;
    }
    
    @Override
    public ItemProcessor<PddGoodsItemContainer, List<PlatformSkuSyncInfo>> getItemProcess() {
        return PddItemSyncServiceImpl::convertPlatformSkuSyncInfos;
    }
    
    @NonNull
    private static ArrayList<PlatformSkuSyncInfo> convertPlatformSkuSyncInfos(
            PddGoodsItemContainer pddGoodsItemContainer) {
        final PddGoodsDetailGetResponse.GoodsDetailGetResponse goodsDetail = pddGoodsItemContainer.getGoodsDetail();
        final String shopNo = pddGoodsItemContainer.getShopNo();
        final ArrayList<PlatformSkuSyncInfo> platformSkuSyncInfos = new ArrayList<>();
        for (PddGoodsDetailGetResponse.GoodsDetailGetResponseSkuListItem detailSkuItem : goodsDetail.getSkuList()) {
            final PlatformSkuSyncInfo skuSyncInfo = new PlatformSkuSyncInfo();
            skuSyncInfo.setPlatform(Platform.PDD);
            skuSyncInfo.setShopNo(shopNo);
            skuSyncInfo.setOuterSkuId(detailSkuItem.getSkuId().toString());
            skuSyncInfo.setOuterItemId(goodsDetail.getGoodsId().toString());
            skuSyncInfo.setOuterSkuCode(detailSkuItem.getOutSkuSn());
            skuSyncInfo.setOuterItemCode(goodsDetail.getOuterGoodsId());
            skuSyncInfo.setGoodsStock(null);
            skuSyncInfo.setStock(detailSkuItem.getQuantity());
            skuSyncInfo.setPrice(BigDecimal.valueOf(detailSkuItem.getPrice())
                    .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
            //拼多多商品状态 1:上架，2：下架，3：售罄 4：已删除
            //平台商品状态 0下架 1上架（在售中）2 待上架(未上架)
            final Integer status = goodsDetail.getStatus();
            skuSyncInfo.setStatus(status == 1 && detailSkuItem.getIsOnsale() == 1 ? 1 : 0);
            skuSyncInfo.setGoodsStatus(status == 1 ? 1 : 0);
            skuSyncInfo.setGoodsName(goodsDetail.getGoodsName());
            skuSyncInfo.setSpecName(detailSkuItem.getSpec()
                    .stream()
                    .map(PddGoodsDetailGetResponse.GoodsDetailGetResponseSkuListItemSpecItem::getSpecName)
                    .collect(Collectors.joining("|")));
            skuSyncInfo.setSkuNum(goodsDetail.getSkuList().size());
            platformSkuSyncInfos.add(skuSyncInfo);
        }
        return platformSkuSyncInfos;
    }
    
    
    @Override
    public ItemWriter<List<PlatformSkuSyncInfo>> getItemWriter() {
        return platformParams -> {
            for (List<PlatformSkuSyncInfo> platformParamDatums : platformParams) {
                saveSkuSyncInfo(platformParamDatums);
            }
        };
    }
    
    
    @Override
    protected List<PlatformSkuSyncInfo> getSkuSyncInfos(String shopNo, String itemId) {
        final ShopAuthorization shopAuthorization = shopAuthorizationService
                .getByShopNo(shopNo)
                .filter(ShopAuthorization::isNotExpired)
                .orElseThrow(() -> ExceptionPlusFactory.bizException(ErrorCode.PDD_ERROR,
                        String.format("店铺%s授权过期", shopNo)));
        return getPlatformSkuSyncInfos(Long.parseLong(itemId), shopAuthorization);
    }
    
    @NonNull
    private ArrayList<PlatformSkuSyncInfo> getPlatformSkuSyncInfos(Long itemId, ShopAuthorization shopAuthorization) {
        final PddGoodsItemContainer pddGoodsDetail = getPddGoodsDetail(itemId, shopAuthorization);
        return convertPlatformSkuSyncInfos(pddGoodsDetail);
    }
    
    private PddGoodsItemContainer getPddGoodsDetail(Long itemId, ShopAuthorization shopAuthorization) {
        final String shopNo = shopAuthorization.getSn();
        try {
            final PopHttpClient httpClient = pddConfig.getHttpClient();
            final PddGoodsDetailGetRequest request = new PddGoodsDetailGetRequest();
            request.setGoodsId(itemId);
            
            final PddGoodsDetailGetResponse pddGoodsDetailGetResponse = httpClient.syncInvoke(request,
                    shopAuthorization.getAccessToken());
            final PopBaseHttpResponse.ErrorResponse errorResponse = pddGoodsDetailGetResponse.getErrorResponse();
            if (errorResponse != null) {
                throw new RuntimeException(String.format("%s %s",
                        errorResponse.getSubCode(),
                        errorResponse.getSubMsg()));
            }
            final PddGoodsItemContainer pddGoodsItemContainer = new PddGoodsItemContainer();
            pddGoodsItemContainer.setGoodsDetail(pddGoodsDetailGetResponse.getGoodsDetailGetResponse());
            pddGoodsItemContainer.setShopNo(shopNo);
            
            return pddGoodsItemContainer;
        } catch (Exception e) {
            log.error("[拼多多][商品详情请求][店铺{}][商品{}]{}", shopNo, itemId, e.getMessage(), e);
            throw ExceptionPlusFactory.bizException(ErrorCode.PDD_ERROR,
                    "请求拼多多商品详情接口异常:" + e.getMessage());
        }
    }
}
