package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/25 6:10 下午
 * @description
 */
@AllArgsConstructor
@Getter
public enum ExportTaskStatus implements IEnum<Integer> {

    /**
     * 仓库发货
     */
    WAIT(0, "初始化"),

    /**
     * 2:工厂发货
     */
    RUNNING(1, "进行中"),

    SUCCESS(2, "完成"),

    FAIL(-1, "失败");

    @EnumValue
    private Integer value;

    private String desc;

    public static ExportTaskStatus convert(Integer val) {
        return IEnum.getEnumByValue(ExportTaskStatus.class, val);
    }


}
