package com.daddylab.supplier.item.application.third;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.domain.third.dto.AccessTokenForm;

/**
 * <AUTHOR>
 * @class ThirdAccessTokenService.java
 * @description 第三方的accessToken获取
 * @date 2024-02-27 17:31
 */
public interface ThirdAccessTokenBizService {

    /**
     * 设置保存accessToken
     *
     * @param accessTokenForm AccessTokenForm
     * @return com.alibaba.cola.dto.SingleResponse<java.lang.Boolean>
     * @date 2024/2/27 17:41
     * <AUTHOR>
     */
    SingleResponse<Boolean> saveAccessToken(AccessTokenForm accessTokenForm);
}
