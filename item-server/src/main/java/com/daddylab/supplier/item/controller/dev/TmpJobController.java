package com.daddylab.supplier.item.controller.dev;

import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.drawer.ItemDrawerImportMallServiceImpl;
import com.daddylab.supplier.item.application.drawer.ItemLaunchLegalIdFetchTask;
import com.daddylab.supplier.item.application.drawer.NewGoodsAutoCreateTask;
import com.daddylab.supplier.item.application.item.tasks.ImportItemCategoryTask;
import com.daddylab.supplier.item.application.item.tasks.ItemCategoryPushWdtService;
import com.daddylab.supplier.item.application.tmpJob.AfterSalesWarehouseAddressJob;
import com.daddylab.supplier.item.application.tmpJob.ImportCommissionJob;
import com.daddylab.supplier.item.application.tmpJob.ModifyCategory240320Job;
import com.daddylab.supplier.item.common.domain.dto.GenericIdsBody;
import com.daddylab.supplier.item.common.domain.dto.IdsCmd;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.tmp.BatchOffShelfJob;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import java.io.IOException;
import java.io.InputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2021/12/31
 */

@Api(tags = {"临时任务"})
@Slf4j
@RestController
@RequestMapping("/dev/tmpJob")
public class TmpJobController {

    @PostMapping(value = "/itemLaunchLegalIdFetchTask")
    public Response itemLaunchAuditStatusFetchTask() {
        new Thread(RunnableWrapper.of(() -> {
            try {
                ApplicationContextUtil.getBean(ItemLaunchLegalIdFetchTask.class).run();
            } catch (Exception e) {
                log.error("补偿新品商品法务信息失败", e);
            }
        })).start();
        return Response.buildSuccess();
    }

    @PostMapping(value = "/importItemCategoryTask", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response importItemCategoryTask(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        final InputStream inputStream;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            return Response.buildFailure(ErrorCode.FILE_UPLOAD_ERROR.getCode(),
                    "导入商品关联类目失败，读取文件流异常");
        }
        new Thread(RunnableWrapper.of(() -> {
            try {
                ApplicationContextUtil.getBean(ImportItemCategoryTask.class)
                        .importExcel(inputStream);
            } catch (Exception e) {
                log.error("导入商品关联类目失败，{}", e.getMessage(), e);
            }
        })).start();
        return Response.buildSuccess();
    }

    @PostMapping(value = "/fixImportItemCategoryTask")
    public Response importItemCategoryTask(@RequestBody IdsCmd cmd) {
        new Thread(RunnableWrapper.of(() -> {
            try {
                ApplicationContextUtil.getBean(ImportItemCategoryTask.class).fix(cmd.getIds());
            } catch (Exception e) {
                log.error("修复导入商品关联类目失败，{}", e.getMessage(), e);
            }
        })).start();
        return Response.buildSuccess();
    }

    @PostMapping(value = "/itemCategoryPushWdt/pushAll")
    public Response itemCategoryPushWdtAll() {
        new Thread(RunnableWrapper.of(() -> {
            try {
                ApplicationContextUtil.getBean(ItemCategoryPushWdtService.class).pushAll();
            } catch (Exception e) {
                log.error("推送全部商品类目至旺店通失败，{}", e.getMessage(), e);
            }
        })).start();
        return Response.buildSuccess();
    }

    @PostMapping(value = "/itemCategoryPushWdt/pushBatchByCode")
    public Response itemCategoryPushWdtBatchByCode(@RequestBody GenericIdsBody<String> cmd) {
        new Thread(RunnableWrapper.of(() -> {
            try {
                ApplicationContextUtil.getBean(ItemCategoryPushWdtService.class)
                        .pushBatchByCode(cmd.getIds());
            } catch (Exception e) {
                log.error("批量推送商品类目至旺店通失败，{}", e.getMessage(), e);
            }
        })).start();
        return Response.buildSuccess();
    }

    @PostMapping(value = "/itemDrawer/importItemImageAndVideo", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response importItemImageAndVideo(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        ApplicationContextUtil.getBean(ItemDrawerImportMallServiceImpl.class)
                .importItemImageAndVideo(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/itemDrawer/importVideoCover", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response importVideoCover(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        ApplicationContextUtil.getBean(ItemDrawerImportMallServiceImpl.class)
                .importVideoCover(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/itemDrawer/importItemDetailImage", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response importItemDetailImage(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        ApplicationContextUtil.getBean(ItemDrawerImportMallServiceImpl.class)
                .importItemDetailImage(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/itemDrawer/importAttrImage", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response importAttrImage(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        ApplicationContextUtil.getBean(ItemDrawerImportMallServiceImpl.class)
                .importAttrImage(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/newGoods/autoCreateByExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response autoCreateNewGoodsByExcel(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        ApplicationContextUtil.getBean(NewGoodsAutoCreateTask.class)
                              .importExcel(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/item/modifyCategory240320Job", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response modifyCategory240320Job(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        ApplicationContextUtil.getBean(ModifyCategory240320Job.class)
                              .handle(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/afterSalesWarehouseAddressImport", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response afterSalesWarehouseAddressImport(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        ApplicationContextUtil.getBean(AfterSalesWarehouseAddressJob.class)
                .importAddress(file.getInputStream());
        return Response.buildSuccess();
    }


    @PostMapping(value = "/importCommission", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response importCommission(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        ApplicationContextUtil.getBean(ImportCommissionJob.class)
                              .imports(file.getInputStream());
        return Response.buildSuccess();
    }

    @PostMapping(value = "/batchOffShelf", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response batchOffShelf(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) throws IOException {
        ApplicationContextUtil.getBean(BatchOffShelfJob.class)
                .processOffShelf(file);
        return Response.buildSuccess();
    }


}