package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.EntryActivityPriceSkuMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IEntryActivityPriceSkuService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 入驻活动价格(SKU纬度) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-04
 */
@Service
public class EntryActivityPriceSkuServiceImpl extends DaddyServiceImpl<EntryActivityPriceSkuMapper, EntryActivityPriceSku> implements IEntryActivityPriceSkuService {

    @Override
    public List<EntryActivityPriceSku> findTimeIntersectionSkuList(String skuCode, Long activeStart, Long activeEnd) {
        return lambdaQuery()
                .eq(EntryActivityPriceSku::getSkuCode, skuCode)
                .and(q -> q.ge(EntryActivityPriceSku::getActiveStart, activeStart)
                           .le(EntryActivityPriceSku::getActiveEnd, activeStart)
                           .or()
                           .ge(EntryActivityPriceSku::getActiveStart, activeEnd)
                           .le(EntryActivityPriceSku::getActiveEnd, activeEnd))
                .list();
    }
}
