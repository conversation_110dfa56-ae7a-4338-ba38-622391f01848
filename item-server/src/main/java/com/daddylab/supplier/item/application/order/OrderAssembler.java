package com.daddylab.supplier.item.application.order;

import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailResponse.Order;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetail;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.types.order.OrderQuery;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.slf4j.Logger;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2022/4/18
 */
@Mapper
public interface OrderAssembler {

    OrderAssembler INST = Mappers.getMapper(OrderAssembler.class);
    Logger log = org.slf4j.LoggerFactory.getLogger("OrderAssembler");

    OrderQuery copy(OrderQuery orderQuery);

    @Mapping(target = "orderDetails", ignore = true)
    @Mapping(target = "deletedAt", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "checkTime", source = "checkTime")
    WdtOrder apiOrderDtoToPo(Order orderDTO);

    @Mapping(target = "warehouseNo", ignore = true)
    @Mapping(target = "tradeTime", ignore = true)
    @Mapping(target = "payTime", ignore = true)
    @Mapping(target = "deletedAt", ignore = true)
    @Mapping(target = "providerId", ignore = true)
    @Mapping(target = "id", ignore = true)
    WdtOrderDetail apiOrderDetailDtoToPo(Order.Detail orderDetailDTO);

    default LocalDateTime toLocalDateTime(String time) {
        try {
            return DateUtil.toLocalDateTime(time);
        } catch (Exception e) {
            log.error("解析日期字符串异常：" + e.getMessage() + " for " + time, e);
            return null;
        }
    }

}
