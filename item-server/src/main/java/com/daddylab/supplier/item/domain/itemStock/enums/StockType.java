package com.daddylab.supplier.item.domain.itemStock.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum StockType implements IIntegerEnum {
    /**
     * 库存量
     */
    STOCK(1, "库存量"),

    /**
     * 外部库存
     */
    WMS_SYNC_STOCK(2, "外部WMS同步时库存"),

    /**
     *
     */
    WMS_STOCK_DIFF(3, "外部WMS同步时,与系统库存的差"),

    /**
     * 未付款量
     */
    UNPAY_NUM(4, "未付款量"),

    /**
     * 预订单量
     */
    SUBSCRIBE_NUM(5, "预订单量"),

    /**
     * 待审核量
     */
    ORDER_NUM(6, "待审核量"),

    /**
     * 待发货量
     */
    SENDING_NUM(7, "待发货量"),

    /**
     * 采购在途量
     */
    PURCHASE_NUM(8, "采购在途量"),

    /**
     * 调拨在途量
     */
    TRANSFER_NUM(9, "调拨在途量"),

    /**
     * 待采购量
     */
    TO_PURCHASE_NUM(10, "待采购量"),

    /**
     * 采购到货量
     */
    PURCHASE_ARRIVE_NUM(11, "采购到货量"),

    /**
     * 外部WMS同步时占用库存
     */
    WMS_PREEMPTY_STOCK(12, "外部WMS同步时占用库存"),

    /**
     * 可发库存
     */
    AVAILABLE_SEND_STOCK(13, "可发库存"),

    /**
     * 部分付款库存
     */
    PART_PAID_NUM(13, "部分付款库存"),

    /**
     * 销售换货在途量
     */
    REFUND_EXCH_NUM(14, "销售换货在途量"),

    /**
     * 销售换货在途量
     */
    REFUND_NUM(15, "销售换货在途量"),

    /**
     * 销售退货在途量
     */
    REFUND_ONWAY_NUM(16, "销售退货在途量"),

    /**
     * 采购换货量
     */
    RETURN_EXCH_NUM(17, "采购换货量"),

    /**
     * 采购退货量
     */
    RETURN_NUM(18, "采购退货量"),

    /**
     * 采购换货在途量
     */
    RETURN_ONWAY_NUM(19, "采购换货在途量"),

    /**
     * 待调拨量
     */
    TO_TRANSFER_NUM(20, "待调拨量"),

    /**
     * 外部WMS同步时,占用库存差
     */
    WMS_PREEMPTY_DIFF(21, "外部WMS同步时,占用库存差"),

    /**
     * 库存锁定量
     */
    LOCK_NUM(22, "库存锁定量"),

    ;
    final private Integer value;
    final private String desc;
}
