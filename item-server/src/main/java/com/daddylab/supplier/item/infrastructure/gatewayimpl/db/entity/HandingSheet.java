package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 盘货表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class HandingSheet implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 表格名称
     */
    private String sheetName;

    /**
     * 开始时间，时间戳，单位秒
     */
    private Long startTime;

    /**
     * 开始时间，时间戳，单位秒
     */
    private Long endTime;

    /**
     * 所属平台，多个用逗号隔开（0-其他，1-淘宝，2-有赞...）
     */
    private String platform;

    /**
     * 标签
     */
    private String label;

    /**
     * 附件
     */
    private String attachment;

    /**
     * 类型
     */
    private Integer activityType;

    /**
     * 状态（1-待提交，2-待审核，3-已过审，4-进行中，5-已过期）
     */
    private Integer state;

    /**
     * 提交审核时间
     */
    private Long submitAt;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人uid
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人uid
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除：0否1是
     */
    @TableLogic
    private Integer isDel;


}
