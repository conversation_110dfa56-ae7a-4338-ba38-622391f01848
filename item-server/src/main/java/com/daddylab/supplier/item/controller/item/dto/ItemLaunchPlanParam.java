package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotNull;

/**
 * @Author: <PERSON>
 * @Date: 2022/5/31 16:00
 * @Description: 请描述下这个类
 */
@Data
@ApiModel("增加商品上新计划参数")
public class ItemLaunchPlanParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "计划ID（新增时不需要，修改时需要）")
    private Long planId;

    @ApiModelProperty(value = "计划名称")
    private String planName;

    @ApiModelProperty(value = "上新时间（时间戳，单位秒）")
    private Long launchTime;

    @ApiModelProperty(value = "商品上新计划关联的商品信息参数集合")
    List<ItemLaunchPlanLinkItemParam> linkItems;
    
    @ApiModelProperty("合作模式")
    @NotNull
    private Integer businessLine;
}
