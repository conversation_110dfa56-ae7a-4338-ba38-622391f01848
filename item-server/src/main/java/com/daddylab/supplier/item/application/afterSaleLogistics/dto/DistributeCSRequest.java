package com.daddylab.supplier.item.application.afterSaleLogistics.dto;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/29
 */
@Data
public class DistributeCSRequest {
  @ApiModelProperty(value = "异常id")
  @NotEmpty
  List<Long> ids;

  @ApiModelProperty(value = "客服id")
  @NotEmpty
  List<Long> csUserIds;
}
