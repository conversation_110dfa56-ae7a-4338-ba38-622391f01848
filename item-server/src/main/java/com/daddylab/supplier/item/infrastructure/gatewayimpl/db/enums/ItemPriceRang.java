package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品价格作用范围
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/15 5:04 下午
 * @description
 */
@Getter
@AllArgsConstructor
public enum ItemPriceRang implements IEnum {

    /**
     * 1:作用于采购成本的价格
     */
    PROCUREMENT(1, "作用于成本采购的价格"),
    /**
     * 2:作用于日常销售的价格
     */
    SALES(2, "作用于日常销售的价格");

    @EnumValue
    private Integer value;

    private String desc;

}
