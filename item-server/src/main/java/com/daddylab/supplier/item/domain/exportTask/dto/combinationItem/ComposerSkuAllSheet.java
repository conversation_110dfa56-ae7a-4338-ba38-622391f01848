package com.daddylab.supplier.item.domain.exportTask.dto.combinationItem;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/13 2:00 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ColumnWidth(40)
public class ComposerSkuAllSheet extends ExportSheet {

    @ExcelIgnore
    private Long combinationId;

    @ExcelProperty(value = "组合商品名称")
    protected String combinationName;

    @ExcelProperty(value = "组合编码")
    protected String combinationCode;

    @ExcelProperty(value = "条码")
    protected String combinationBarCode;

    @ExcelProperty(value = "重量")
    protected BigDecimal combinationWeight;

    @ExcelProperty(value = "组合商品采购成本")
    protected BigDecimal itemProcurementPrice;

    @ExcelProperty(value = "组合商品日常销售价")
    protected BigDecimal itemSalesPrice;

    @ExcelProperty(value = "单品商品编码")
    protected String itemCode;

    @ExcelProperty(value = "单品商品SKU")
    protected String skuCode;

    @ExcelProperty(value = "单品数量")
    protected String count;

    @ExcelProperty(value = "单品采购成本")
    protected BigDecimal skuCostPrice;

    @ExcelProperty(value = "单品日常销售价")
    protected BigDecimal skuSalesPrice;

    @ExcelProperty(value = "采购成本占比")
    protected BigDecimal costProportion;

    @ExcelProperty(value = "销售占比")
    protected BigDecimal saleProportion;

    @ExcelProperty(value = "单品库存")
    protected String stockCount;

    /**
     * 平台佣金
     */
    @ExcelProperty(value = "平台佣金")
    private BigDecimal platformCommission;

    /**
     * 合同销售价
     */
    @ExcelProperty(value = "合同销售价")
    private BigDecimal contractSalePrice;

    /**
     * 平台佣金
     */
    @ExcelProperty(value = "单品平台佣金")
    private BigDecimal skuPlatformCommission;

    /**
     * 合同销售价
     */
    @ExcelProperty(value = "单品合同销售价")
    private BigDecimal skuContractSalePrice;

    @ExcelProperty(value = "合作方")
    private String corpType;

    @ExcelProperty(value = "业务类型")
    private String bizType;


}
