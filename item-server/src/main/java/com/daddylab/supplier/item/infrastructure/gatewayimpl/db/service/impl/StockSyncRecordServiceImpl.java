package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockSyncRecord;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockSyncRecordMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockSyncRecordService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 库存同步记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
@Service
public class StockSyncRecordServiceImpl extends DaddyServiceImpl<StockSyncRecordMapper, StockSyncRecord> implements IStockSyncRecordService {

}
