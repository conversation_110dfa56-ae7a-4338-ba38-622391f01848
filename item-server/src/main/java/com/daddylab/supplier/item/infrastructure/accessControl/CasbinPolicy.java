package com.daddylab.supplier.item.infrastructure.accessControl;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CasbinPolicy implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonAlias("Ptype")
    private String ptype;

    @JsonAlias("V0")
    private String v0;

    @JsonAlias("V1")
    private String v1;

    @JsonAlias("V2")
    private String v2;

    @JsonAlias("V3")
    private String v3;

    @JsonAlias("V4")
    private String v4;

    @JsonAlias("V5")
    private String v5;


}
