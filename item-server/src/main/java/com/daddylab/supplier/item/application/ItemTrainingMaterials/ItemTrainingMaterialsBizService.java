package com.daddylab.supplier.item.application.ItemTrainingMaterials;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTrainingMaterials;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTrainingMaterialsProcess;
import com.daddylab.supplier.item.types.itemTrainingMaterials.*;
import com.daddylab.supplier.item.types.process.TaskUrgeCmd;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;

import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @since 2024/4/11
 */
public interface ItemTrainingMaterialsBizService {
    SingleResponse<ItemTrainingMaterialsDrawer> drawer(Long itemId);

    ItemTrainingMaterials getMaterialsCreateIfNotExits(Long itemId);

    ProcessInstance createProcessInstance(ItemTrainingMaterialsProcess itemTrainingMaterialsProcess);

    Response save(ItemTrainingMaterialsSaveCmd cmd);

    Response urge(Long userId, TaskUrgeCmd cmd);

    Response complete(TaskCompleteCmd cmd);

    Response rollback(RollbackCmd cmd);

    Response rollback2(RollbackCmd cmd);

    Response terminal(TerminalCmd cmd);

    Response terminalBatch(TerminalBatchCmd cmd);

    Response submitReview(Long userId, MaterialsSubmitReviewCmd cmd);

    MultiResponse<ItemTrainingMaterialsRecordVO> historyRecordsQuery(ItemTrainingMaterialsRecordQuery query);

    void notifyLegalProcess(ItemTrainingMaterialsProcess materialsProcess);

    void notifyLegalProcess(ItemTrainingMaterialsProcess materialsProcess, boolean isReview);

    void notifyQcProcess(ItemTrainingMaterialsProcess materialsProcess);

    void notifySubmitReview(ItemTrainingMaterialsProcess materialsProcess);

    void notifyModifyProcess(ItemTrainingMaterialsProcess materialsProcess);

    void notifyModifyProcessed(ItemTrainingMaterialsProcess materialsProcess);

    Response getOperateLogs(Long itemId);

    void predicateComplete(Long userId, Long itemId, Predicate<Task> predicate, String comment);

    void syncContentToWiki(Long itemId);
}
