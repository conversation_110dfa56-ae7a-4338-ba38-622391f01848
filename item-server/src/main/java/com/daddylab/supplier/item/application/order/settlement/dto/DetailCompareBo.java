//package com.daddylab.supplier.item.application.order.settlement.dto;
//
//import lombok.Data;
//import org.javers.core.metamodel.annotation.Id;
//import org.javers.core.metamodel.annotation.PropertyName;
//
///**
// * <AUTHOR> up
// * @date 2024年06月03日 7:23 PM
// */
//@Data
//public class DetailCompareBo {
//
//    @Id
//    private String skuCode;
//
//    @PropertyName("发货数量")
//    private Integer deliveryNum;
//
//    @PropertyName("当月退货数量")
//    private Integer currentRefundNum;
//
//    @PropertyName("跨越退货数量")
//    private Integer crossRefundNum;
//
//    @PropertyName("结算金额")
//    private String settlement;
//
//    @PropertyName("运费售后分摊金额")
//    private String afterSaleCost;
//
//    @PropertyName("备注")
//    private String remark;
//}
