package com.daddylab.supplier.item.application.afterSaleLink;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import cn.hutool.jwt.JWT;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.daddylab.supplier.item.application.afterSaleLink.dto.*;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.domain.smsAuth.SmsAuthService;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSaleShareLink;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSaleShareLinkUser;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AfterSaleShareLinkUserMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSaleShareLinkService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSaleShareLinkUserService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLock;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.net.URLCodec;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2024年05月11日 10:10 AM
 */
@Service
@AllArgsConstructor
@Slf4j
public class AfterSaleShareLinkBizServiceImpl implements AfterSaleShareLinkBizService {

    public static final String SCOPE = "AFTER_SALE_SHARE_LINK";
    final IAfterSaleShareLinkService afterSaleShareLinkService;
    final IAfterSaleShareLinkUserService afterSaleShareLinkUserService;
    final AfterSaleShareLinkUserMapper afterSaleShareLinkUserMapper;
    final UserGateway userGateway;
    final OperateLogGateway operateLogGateway;
    final Environment environment;
    final SmsAuthService smsAuthService;

    private final static String AES_KEY = "qaz123xde4ijnmko";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<String> saveLink(AfterSaleShareLinkCmd cmd) {
        String returnRes;

        boolean forbidden = Objects.equals(cmd.getForbidden(), 1);
        if (!forbidden) {
            List<Long> telList = cmd.getTelList();
            Assert.state(CollectionUtils.isNotEmpty(telList), "手机号不得为空");
            boolean notRepeat = telList.size() == (telList.stream().distinct().count());
            Assert.state(notRepeat, "手机号码不得重复");
        }

        Long id = cmd.getId();
        boolean isAdd = Objects.isNull(id);
        if (isAdd) {
            AfterSaleShareLink afterSaleShareLink = new AfterSaleShareLink();
            afterSaleShareLink.setStatus(Objects.isNull(cmd.getStatus()) ? 0 : cmd.getStatus());
            afterSaleShareLink.setValidityPeriod(cmd.getValidityPeriod());
            afterSaleShareLink.setAlwaysValidity(cmd.getAlwaysValidity());
            afterSaleShareLink.setNo(afterSaleShareLinkService.getNewOnNo());
            afterSaleShareLink.setName(cmd.getName());
            if (StringUtils.isBlank(cmd.getParams())) {
                afterSaleShareLink.setLinkParams(new JSONObject());
            } else {
                afterSaleShareLink.setLinkParams(JSONObject.parseObject(cmd.getParams(), JSONObject.class));
            }
            if (cmd.getAlwaysValidity() == 0) {
                long epochSecond = LocalDateTime.now().plusDays(cmd.getValidityPeriod()).atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
                afterSaleShareLink.setExpiredTimePoint(epochSecond);
            }
            afterSaleShareLinkService.save(afterSaleShareLink);
            Long linkId = afterSaleShareLink.getId();

            JSONObject jsonObject = returnJsonObject(afterSaleShareLink.getStatus(), afterSaleShareLink.getAlwaysValidity(),
                    afterSaleShareLink.getValidityPeriod(), LocalDateTime.now());
            jsonObject.put("linkId", linkId);
            String encryptStr = encrypt(jsonObject.toString(), AES_KEY);
            URLCodec codec = new URLCodec();
            try {
                encryptStr = codec.encode(encryptStr, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            String link = environment.getProperty("afterSale-external-sharing-link.url", "www.baidu.com") + "?param=" + encryptStr;
            afterSaleShareLink.setLinkVal(link);
            afterSaleShareLinkService.updateById(afterSaleShareLink);

            addUserInfo(cmd.getTelList(), linkId);
            operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.AFTER_SALE_SHARE_LINK, linkId, "新增链接", null);

            returnRes = encryptStr;
        } else {
            returnRes = updateAfterSaleInfo(cmd);
        }
        return SingleResponse.of(returnRes);
    }

    public static void main(String[] args) throws Exception {


//        String encryptedText = "ItrbA0uuI6vWe3ud4CBMwr5WNvTlA3yFZzSq5wg3hzAm8Bo5pwo0lLip%2F74BjIp8mDFIWpUEainN9vfV6WWgww%3D%3D";
//        URLCodec urlCodec = new URLCodec();
//        String decode = urlCodec.decode(encryptedText);
//        String key = "qaz123xde4ijnmko";
//        SymmetricCrypto aes = new SymmetricCrypto("AES", key.getBytes(StandardCharsets.UTF_8));
//        byte[] decryptedBytes = aes.decrypt(Base64.getDecoder().decode(decode));
//        System.out.println(StrUtil.str(decryptedBytes, StandardCharsets.UTF_8));


    }

    public static String encrypt(String plainText, String key) {
        cn.hutool.crypto.symmetric.SymmetricCrypto aes = new cn.hutool.crypto.symmetric.SymmetricCrypto("AES", key.getBytes(StandardCharsets.UTF_8));
        byte[] encryptedBytes = aes.encrypt(plainText.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    public static String decrypt(String encryptedText, String key) {
        SymmetricCrypto aes = new SymmetricCrypto("AES", key.getBytes(StandardCharsets.UTF_8));
        byte[] decryptedBytes = aes.decrypt(Base64.getDecoder().decode(encryptedText));
        return StrUtil.str(decryptedBytes, StandardCharsets.UTF_8);
    }

    private JSONObject returnJsonObject(Integer status, Integer alwaysValidity, Integer validityPeriod, LocalDateTime startDateTime) {
        JSONObject jsonObject = JSONObject.of("status", status);
        if (alwaysValidity == 0) {
            Assert.notNull(validityPeriod, "既然不是长期有效的链接，请给一个有效期限（单位天）");
            long expireTime = startDateTime.plusDays(validityPeriod).atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
            jsonObject.put("expireTime", expireTime);
        }
        if (alwaysValidity == 1) {
            long expireTime = startDateTime.plusYears(999).atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
            jsonObject.put("expireTime", expireTime);
        }
        return jsonObject;
    }

    @DistributedLock
    private String updateAfterSaleInfo(AfterSaleShareLinkCmd cmd) {
        Long id = cmd.getId();
        AfterSaleShareLink oldOne = afterSaleShareLinkService.getById(id);
        Assert.notNull(oldOne, "ID非法，查不到售后链接信息。" + id);

        if (Objects.equals(cmd.getForbidden(), 1)) {
            oldOne.setStatus(1);
            JSONObject jsonObject = returnJsonObject(1, oldOne.getAlwaysValidity(), oldOne.getValidityPeriod(), DateUtil.parseTimeStamp(oldOne.getCreatedAt()));
            jsonObject.put("linkId", oldOne.getId());
            String encryptStr = encrypt(jsonObject.toString(), AES_KEY);
            URLCodec codec = new URLCodec();
            try {
                encryptStr = codec.encode(encryptStr, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            String link = environment.getProperty("afterSale-external-sharing-link.url", "www.baidu.com") + "?param=" + encryptStr;
            oldOne.setLinkVal(link);
            afterSaleShareLinkService.updateById(oldOne);
            operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.AFTER_SALE_SHARE_LINK, id, "禁用链接", null);
            return "";
        }

        LinkCompareBo oldLinkCompareBo = convertEntityToCompareBo(oldOne);
        List<Long> oldTelList = afterSaleShareLinkUserService.lambdaQuery().eq(AfterSaleShareLinkUser::getLinkId, id).list().stream().map(AfterSaleShareLinkUser::getTel).collect(Collectors.toList());

        JSONObject jsonObject = returnJsonObject(cmd.getStatus(), cmd.getAlwaysValidity(), cmd.getValidityPeriod(), DateUtil.parseTimeStamp(oldOne.getCreatedAt()));
        jsonObject.put("linkId", oldOne.getId());
        String encryptStr = encrypt(jsonObject.toString(), AES_KEY);
        URLCodec codec = new URLCodec();
        try {
            encryptStr = codec.encode(encryptStr, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        String link = environment.getProperty("afterSale-external-sharing-link.url", "www.baidu.com") + "?param=" + encryptStr;
        oldOne.setLinkVal(link);
        oldOne.setStatus(cmd.getStatus());
        oldOne.setValidityPeriod(cmd.getValidityPeriod());
        oldOne.setAlwaysValidity(cmd.getAlwaysValidity());
        oldOne.setName(cmd.getName());
        if (StringUtils.isBlank(cmd.getParams())) {
            oldOne.setLinkParams(new JSONObject());
        } else {
            oldOne.setLinkParams(JSONObject.parseObject(cmd.getParams(), JSONObject.class));
        }
        LinkCompareBo nowLinkCompareBo = convertEntityToCompareBo(oldOne);
        if (cmd.getAlwaysValidity() == 0) {
            long epochSecond = LocalDateTime.now().plusDays(cmd.getValidityPeriod()).atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
            oldOne.setExpiredTimePoint(epochSecond);
        }
        afterSaleShareLinkService.updateById(oldOne);

        String log = DiffUtil.getSimpleDiffLog(DiffUtil.diff(oldLinkCompareBo, nowLinkCompareBo), LinkCompareBo.class);
        operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.AFTER_SALE_SHARE_LINK, id, log, null);
        DiffUtil.LangDiffRes<Long> longLangDiffRes = DiffUtil.diffLangObjList(oldTelList, cmd.getTelList(), Long.class);
        List<String> logList = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(longLangDiffRes.getAddList())) {
            logList.add("添加手机号码。" + StringUtil.join(",", longLangDiffRes.getAddList()));
        }
        if (CollectionUtils.isNotEmpty(longLangDiffRes.getRemoveList())) {
            logList.add("移除手机号码。" + StringUtil.join(",", longLangDiffRes.getRemoveList()));
        }
        if (CollectionUtils.isNotEmpty(logList)) {
            operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.AFTER_SALE_SHARE_LINK, id, StringUtil.join("。", logList), null);
        }

        afterSaleShareLinkUserService.lambdaUpdate().eq(AfterSaleShareLinkUser::getLinkId, id).remove();
        addUserInfo(cmd.getTelList(), id);

        return encryptStr;
    }

    private LinkCompareBo convertEntityToCompareBo(AfterSaleShareLink linkOne) {
        LinkCompareBo linkCompareBo = new LinkCompareBo();
        linkCompareBo.setId(linkOne.getId());
        linkCompareBo.setStatus(0 == linkOne.getStatus() ? "正常" : "禁用");
        linkCompareBo.setName(linkOne.getName());
        String cc = "";
        if (linkOne.getAlwaysValidity() == 1) {
            cc = "长期有效";
        } else {
            cc = linkOne.getValidityPeriod() + "天";
        }
        linkCompareBo.setValidity(cc);

        return linkCompareBo;
    }

    private Long calculateExpirationTime(Integer validityPeriod) {
        LocalDateTime localDateTime = LocalDateTime.now().plusDays(validityPeriod);
        return localDateTime.atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
    }

    private void addUserInfo(List<Long> telList, Long linkId) {
        if (CollectionUtils.isNotEmpty(telList)) {
            List<AfterSaleShareLinkUser> newUserList = telList.stream().map(val -> {
                AfterSaleShareLinkUser user = new AfterSaleShareLinkUser();
                user.setTel(val);
                user.setLinkId(linkId);
                return user;
            }).collect(Collectors.toList());
            afterSaleShareLinkUserService.saveBatch(newUserList);
        }
    }

    // ------------------------------------------------

    private LambdaQueryChainWrapper<AfterSaleShareLink> pageLambdaQuery(AfterSaleShareLinkQuery query, List<Long> finalMatchedLinkId) {
        LambdaQueryChainWrapper<AfterSaleShareLink> lambdaQueryChainWrapper = afterSaleShareLinkService.lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getNo()), AfterSaleShareLink::getNo, query.getNo())
                .like(StringUtils.isNotBlank(query.getName()), AfterSaleShareLink::getName, query.getName())

                .in(CollectionUtils.isNotEmpty(finalMatchedLinkId), AfterSaleShareLink::getId, finalMatchedLinkId)
                .in(CollectionUtils.isNotEmpty(query.getCreatedUidList()), AfterSaleShareLink::getCreatedUid, query.getCreatedUidList())
                .le(Objects.nonNull(query.getEndTime()), AfterSaleShareLink::getCreatedAt, query.getEndTime())
                .ge(Objects.nonNull(query.getStartTime()), AfterSaleShareLink::getCreatedAt, query.getStartTime())
                .orderByDesc(AfterSaleShareLink::getId);
        if (Objects.nonNull(query.getStatus())) {
            if (query.getStatus() == 0) {
                lambdaQueryChainWrapper.eq(AfterSaleShareLink::getStatus, 0)
                        .and(wrapper -> wrapper.ge(AfterSaleShareLink::getExpiredTimePoint, DateUtil.currentTime())
                                .or().eq(AfterSaleShareLink::getAlwaysValidity, 1));
            }
            if (query.getStatus() == 1) {
                lambdaQueryChainWrapper.eq(AfterSaleShareLink::getStatus, 1);
            }
            if (query.getStatus() == 2) {
                lambdaQueryChainWrapper.le(AfterSaleShareLink::getExpiredTimePoint, DateUtil.currentTime());
            }
        }

        return lambdaQueryChainWrapper;
    }

    @Override
    public PageResponse<AfterSaleShareLinkVO> pageQuery(AfterSaleShareLinkQuery query) {
        List<Long> matchedLinkId = new LinkedList<>();
        if (StringUtils.isNotBlank(query.getTel())) {
            matchedLinkId = afterSaleShareLinkUserService.lambdaQuery().eq(AfterSaleShareLinkUser::getTel, query.getTel())
                    .select(AfterSaleShareLinkUser::getLinkId).list().stream().map(AfterSaleShareLinkUser::getLinkId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(matchedLinkId)) {
                return ResponseFactory.emptyPage();
            }
        }
        List<Long> finalMatchedLinkId = matchedLinkId;
        Page<AfterSaleShareLink> page = PageHelper.startPage(query.getPageIndex(), query.getPageSize())
                .doSelectPage(() -> pageLambdaQuery(query, finalMatchedLinkId).list());
        if (CollectionUtils.isEmpty(page.getResult())) {
            return ResponseFactory.emptyPage();
        }

        List<Long> thisPageLinkIds = page.getResult().stream().map(AfterSaleShareLink::getId).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        List<LinkCountDto> countList = afterSaleShareLinkUserMapper.countByLinkId(thisPageLinkIds);
        Map<Long, Integer> userCountMap = countList.stream().collect(Collectors.toMap(LinkCountDto::getId, LinkCountDto::getNum));
        List<Long> thisPageCreatedUids = page.stream().map(AfterSaleShareLink::getCreatedUid).distinct().collect(Collectors.toList());
        Map<Long, StaffInfo> createdUidMap = userGateway.batchQueryStaffInfoByIds(thisPageCreatedUids);

        List<AfterSaleShareLinkVO> voList = page.getResult().stream().map(val -> {
            AfterSaleShareLinkVO vo = new AfterSaleShareLinkVO();
            vo.setId(val.getId());
            vo.setNo(val.getNo());
            vo.setName(val.getName());
            vo.setSharePersonNum(userCountMap.getOrDefault(val.getId(), 0));
            vo.setAlwaysValidity(val.getAlwaysValidity());
            vo.setValidityPeriod(val.getValidityPeriod());
            if (val.getAlwaysValidity() == 0) {
                int status = val.getStatus() == 1 ? 1 : (DateUtil.currentTime() > val.getExpiredTimePoint() ? 2 : 0);
                vo.setStatus(status);
            } else {
                vo.setStatus(val.getStatus());
            }
            vo.setCreatedName(createdUidMap.getOrDefault(val.getCreatedUid(), new StaffInfo()).getNickname());
            vo.setCreatedAt(DateUtil.format(val.getCreatedAt()));
            vo.setLinkVal(val.getLinkVal());
            return vo;
        }).collect(Collectors.toList());

        return PageResponse.of(voList, (int) page.getTotal(), page.getPageSize(), page.getPageNum());
    }

    // ------------------------------------------------


    @Override
    public SingleResponse<AfterSaleShareLinkViewVO> view(Long id) {
        AfterSaleShareLink link = afterSaleShareLinkService.getById(id);
        Assert.notNull(link, "id非法" + id);

        AfterSaleShareLinkViewVO v = new AfterSaleShareLinkViewVO();
        v.setId(id);
        v.setStatus(link.getStatus());
        v.setName(link.getName());
        v.setValidityPeriod(link.getValidityPeriod());
        v.setAlwaysValidity(link.getAlwaysValidity());
        List<Long> telList = afterSaleShareLinkUserService.lambdaQuery().eq(AfterSaleShareLinkUser::getLinkId, id).list()
                .stream().map(AfterSaleShareLinkUser::getTel).collect(Collectors.toList());
        v.setTelList(telList);
        v.setLinkVal(link.getLinkVal());
        Long createdUid = link.getCreatedUid();
        StaffInfo staffInfo = userGateway.queryStaffInfoById(createdUid);
        v.setCreatedName(Objects.isNull(staffInfo) ? "" : staffInfo.getNickname());
        v.setCreatedAt(DateUtil.format(link.getCreatedAt(), DateUtil.DEFAULT_FORMAT));

        return SingleResponse.of(v);
    }

    @Override
    public SingleResponse<Boolean> delete(Long id) {
        afterSaleShareLinkService.removeById(id);
        return SingleResponse.of(true);
    }

    @Override
    public MultiResponse<LinkCreatorDto> linkCreator() {
        List<Long> uidList = afterSaleShareLinkService.lambdaQuery()
                .select(AfterSaleShareLink::getCreatedUid)
                .groupBy(AfterSaleShareLink::getCreatedUid)
                .list().stream().map(AfterSaleShareLink::getCreatedUid).collect(Collectors.toList());
        List<LinkCreatorDto> res = new LinkedList<>();
        if (CollectionUtils.isEmpty(uidList)) {
            return MultiResponse.of(res);
        }
        Map<Long, StaffInfo> longStaffInfoMap = userGateway.batchQueryStaffInfoByIds(uidList);
        longStaffInfoMap.forEach((uid, vo) -> {
            LinkCreatorDto dto = new LinkCreatorDto();
            dto.setUId(uid);
            dto.setName(vo.getNickname());
            res.add(dto);
        });
        return MultiResponse.of(res);
    }

    @Override
    public Response sendCode(Long shareLinkId, String phone) {
        checkLinkUserAuthorization(shareLinkId, phone);
        final String smsCode = smsAuthService.getSmsCode(phone, SCOPE);
        log.info("[AfterSaleShareLink]SendSmsCode {} To {}", smsCode, phone);
        return Response.buildSuccess();
    }

    private void checkLinkUserAuthorization(Long shareLinkId, String phone) {
        afterSaleShareLinkUserService.lambdaQuery()
                .eq(AfterSaleShareLinkUser::getLinkId, shareLinkId)
                .eq(AfterSaleShareLinkUser::getTel, phone)
                .oneOpt()
                .orElseThrow(() -> ExceptionPlusFactory.bizException(
                        ErrorCode.SMS_AUTH_ERROR, "未授权"));
    }

    @Override
    public SingleResponse<String> token(Long shareLinkId, String phone, String code) {
        if (!smsAuthService.verifyCode(phone, code, SCOPE)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.SMS_AUTH_ERROR, "短信验证码已失效");
        }
        final byte[] keyBytes = AES_KEY.getBytes(StandardCharsets.UTF_8);
        final String token = JWT.create()
                .setKey(keyBytes)
                .setIssuer("erp")
                .setPayload("linkId", shareLinkId)
                .setPayload("phone", phone)
                .setExpiresAt(new Date(System.currentTimeMillis() + 7200 * 1000))
                .sign();
        return SingleResponse.of(token);
    }

    @Override
    public SingleResponse<AfterSaleShareLink> checkToken(String token) {
        if (StringUtils.isBlank(token)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.NO_LOGIN, "分享链接访问令牌为空");
        }
        final byte[] keyBytes = AES_KEY.getBytes(StandardCharsets.UTF_8);
        final JWT jwt = JWT.of(token).setKey(keyBytes);
        if (!jwt.validate(0)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.NO_LOGIN, "分享链接访问令牌已失效");
        }
        final Long linkId = jwt.getPayloads().getLong("linkId");
        final AfterSaleShareLink link = afterSaleShareLinkService.getById(linkId);
        Assert.notNull(link, "token非法");
        if (link.getStatus() == 1 || (link.getAlwaysValidity() == 0 && link.getExpiredTimePoint() < DateUtil.currentTime())) {
            throw ExceptionPlusFactory.bizException(ErrorCode.NO_ACCESS, "分享链接已失效");
        }
        return SingleResponse.of(link);
    }
}
