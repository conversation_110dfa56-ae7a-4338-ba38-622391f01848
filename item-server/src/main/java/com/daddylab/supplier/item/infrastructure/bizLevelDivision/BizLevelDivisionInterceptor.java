package com.daddylab.supplier.item.infrastructure.bizLevelDivision;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BizUnionTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.FromItem;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.core.ResolvableType;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.InvocationTargetException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Intercepts({
  @Signature(
      type = StatementHandler.class,
      method = "prepare",
      args = {Connection.class, Integer.class})
})
public class BizLevelDivisionInterceptor implements InnerInterceptor {

  public static final String BIZ_SCOPE_EXPRESSION_HINT = "/*@BizScope_expression@*/";
  public static final String BIZ_SCOPE_EXPRESSION_HINT_REGEX = "/\\*@BizScope_expression@\\*/";

  @Override
  public boolean willDoQuery(
      Executor executor,
      MappedStatement ms,
      Object parameter,
      RowBounds rowBounds,
      ResultHandler resultHandler,
      BoundSql boundSql)
      throws SQLException {
    final BizDivisionContext context = BizDivisionContext.getCurrentContext();
    if (context == null) {
      return true;
    }
    if (context.getScopes() == null) {
      context.setScopes(UserPermissionJudge.getDivisionLevelPermissions());
    }
    if (context.getSelectedScopes() == null) {
      final Map<?, ?> parameterMap;
      if (parameter instanceof Map) {
        parameterMap = (Map<?, ?>) parameter;
      } else {
        parameterMap = Collections.singletonMap("param", parameter);
      }
      final Set<DivisionLevelValueEnum> selectedScopes =
          getDivisionLevelValuesParameter(parameterMap, "corpType", "bizType", "runningMode");
      context.setSelectedScopes(selectedScopes);
    }
    final Set<DivisionLevelValueEnum> effectScopes = context.getEffectScopes();
    log.debug(
        "[BizLevelDivisionInterceptor]scopes:{} selected:{} effected:{}",
        context.getScopes(),
        context.getSelectedScopes(),
        effectScopes);
    PluginUtils.MPBoundSql mpBs = PluginUtils.mpBoundSql(boundSql);
    processSQL(mpBs, context);
    return true;
  }

  private Set<DivisionLevelValueEnum> getDivisionLevelValuesParameter(
      Map<?, ?> parameterMap, String... keys) {
    final LinkedHashSet<DivisionLevelValueEnum> scopes = new LinkedHashSet<>();
    Stream.of(keys)
        .forEach(
            key -> {
              final Collection<DivisionLevelValueEnum> selectedScopes =
                  getDivisionLevelValuesParameter(parameterMap, key);
              if (!selectedScopes.isEmpty()) {
                scopes.addAll(selectedScopes);
              }
            });
    return scopes;
  }

  @SuppressWarnings("unchecked")
  private Collection<DivisionLevelValueEnum> getDivisionLevelValuesParameter(
      Map<?, ?> parameterMap, String key) {
    final ArrayList<DivisionLevelValueEnum> selectedScopes = new ArrayList<>();
    if (parameterMap.containsKey(key)) {
      final Object parameterValue = parameterMap.get(key);
      if (parameterValue != null) {
        final ResolvableType resolvableType = ResolvableType.forInstance(parameterValue);
        if (resolvableType.isAssignableFrom(Collection.class)) {
          final ResolvableType genericType0 = resolvableType.getGeneric(0);
          if (genericType0.isAssignableFrom(DivisionLevelValueEnum.class)) {
            selectedScopes.addAll((Collection<DivisionLevelValueEnum>) parameterValue);
          }
          if (genericType0.isAssignableFrom(Integer.class)) {
            selectedScopes.addAll(
                DivisionLevelValueEnum.valueOf((Collection<Integer>) parameterValue));
          }
        }
      }
    } else {
      for (Object pv : parameterMap.values()) {
        if (pv == null) {
          continue;
        }
        final Class<?> parameterClazz = pv.getClass();
        if (parameterClazz.getName().contains("daddylab")) {
          ReflectionUtils.doWithMethods(
              parameterClazz,
              m -> {
                try {
                  final String methodName = StrUtil.upperFirstAndAddPre(key, "get");
                  if (methodName.equals(m.getName()) && m.getParameterCount() == 0) {
                    final ResolvableType resolvableType =
                        ResolvableType.forType(m.getGenericReturnType());
                    final Class<?> rawClass = resolvableType.getRawClass();
                    if (rawClass != null && Collection.class.isAssignableFrom(rawClass)) {
                      final ResolvableType genericType0 = resolvableType.getGeneric(0);
                      final Class<?> genericType0RawClass =
                          Objects.requireNonNull(genericType0.getRawClass());
                      if (DivisionLevelValueEnum.class.isAssignableFrom(genericType0RawClass)) {
                        Optional.ofNullable((Collection<DivisionLevelValueEnum>) m.invoke(pv))
                            .ifPresent(selectedScopes::addAll);
                      }
                      if (Integer.class.isAssignableFrom(genericType0RawClass)) {
                        Optional.ofNullable((Collection<Integer>) m.invoke(pv))
                            .map(DivisionLevelValueEnum::valueOf)
                            .ifPresent(selectedScopes::addAll);
                      }
                    }
                  }
                } catch (InvocationTargetException e) {
                  log.error("[BizLevelDivisionInterceptor]getDivisionLevelValuesParameter", e);
                }
              });
        }
      }
    }
    return selectedScopes;
  }

  private void processSQL(PluginUtils.MPBoundSql mpBs, BizDivisionContext context) {
    final String originalSql = mpBs.sql();
    try {
      Statement statement = CCJSqlParserUtil.parse(originalSql);
      if (!(statement instanceof Select)) {
        return;
      }

      Select selectStatement = (Select) statement;
      PlainSelect plainSelect = (PlainSelect) selectStatement.getSelectBody();

      final String tableRef = getTableName(selectStatement, true);

      // 获取当前线程上下文中的权限信息
      String permissionCondition = getCurrentPermissionCondition(context, tableRef);

      log.debug("[BizLevelDivisionInterceptor]permission segment: {}", permissionCondition);

      final String modifiedSql;
      if (originalSql.contains(BIZ_SCOPE_EXPRESSION_HINT)) {
        modifiedSql =
            originalSql.replaceAll(BIZ_SCOPE_EXPRESSION_HINT_REGEX, " AND " + permissionCondition);
      } else {
        // 解析权限条件
        Expression permissionExpression = CCJSqlParserUtil.parseCondExpression(permissionCondition);
        Expression where = plainSelect.getWhere();

        // 组合原有的 where 条件和权限条件
        if (where != null) {
          plainSelect.setWhere(new AndExpression(where, permissionExpression));
        } else {
          plainSelect.setWhere(permissionExpression);
        }
        modifiedSql = statement.toString();
      }

      if (modifiedSql == null || modifiedSql.equals(originalSql)) {
        return;
      }
      log.debug(
          "[BizLevelDivisionInterceptor]SQL modified from [{}] to [{}]",
          prettyPrint(originalSql),
          prettyPrint(modifiedSql));
      mpBs.sql(modifiedSql);
    } catch (Exception e) {
      log.error(
          "[BizLevelDivisionInterceptor]SQL permission process error, original SQL: {}",
          originalSql,
          e);
    }
  }

  private static String prettyPrint(String originalSql) {
    return originalSql.replaceAll("\n", "").replaceAll("\t", "").replaceAll(" +", " ").trim();
  }

  public static String getTableName(Select selectStatement, boolean withAlias) {
    PlainSelect plainSelect = (PlainSelect) selectStatement.getSelectBody();
    final FromItem fromItem = plainSelect.getFromItem();
    final Alias alias = fromItem.getAlias();
    if (alias != null && withAlias) {
      return alias.getName();
    }
    if (fromItem instanceof Table) {
      Table table = (Table) fromItem;
      return table.getName();
    }
    return fromItem.toString();
  }

  private String getCurrentPermissionCondition(BizDivisionContext context, String tableRef) {
    final Set<DivisionLevelValueEnum> effectScopes = context.getEffectScopes();
    if (effectScopes == null
        || effectScopes.isEmpty()
        || effectScopes.stream()
            .noneMatch(scope -> scope.getDivisionLevel() == DivisionLevelEnum.COOPERATION)) {
      return "FALSE";
    }
    final BizUnionTypeEnum type = context.getType();
    final String bizIdRef =
        Optional.ofNullable(context.getBizIdRef())
            .filter(StrUtil::isNotBlank)
            .orElse(tableRef + ".id");
    final Set<DivisionLevelValueEnum> applyScopes = effectScopes;
    final ArrayList<String> expressions = new ArrayList<>();
    applyScopes.stream()
        .collect(Collectors.groupingBy(DivisionLevelValueEnum::getDivisionLevel))
        .forEach(
            (divisionLevelEnum, divisionLevelValueEnums) -> {
              final ArrayList<String> values = new ArrayList<>();
              for (DivisionLevelValueEnum applyScope : divisionLevelValueEnums) {
                values.add(
                    "("
                        + applyScope.getDivisionLevel().getValue().toString()
                        + ","
                        + applyScope.getValue()
                        + ")");
              }
              expressions.add(
                  String.format(
                      "EXISTS(SELECT * FROM `biz_level_division` `bld` WHERE `bld`.`is_del` = 0 AND `bld`.`type` = %s AND `bld`.`biz_id` = %s AND (`bld`.`level`, `bld`.`level_val`) IN (%s))",
                      type.getValue(), bizIdRef, String.join(",", values)));
            });
    return String.join(" AND ", expressions);
  }
}
