package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 合作模式。0:电商。1:老爸抽检。2:绿色家装。3:商家入驻
 * "/DIAN_SHANG",
 * "/SHANG_JIA_RU_ZHU",
 * "/LV_SE_JIA_ZHUANG",
 */
@Getter
@AllArgsConstructor
public enum BusinessLine implements IIntegerEnum {

    /**
     * 消除黄线
     */
    DIAN_SHANG(0, "电商", true),
    CHOU_JIAN(1, "老爸抽检", false),
    LV_SE_JIA_ZHUANG(2, "绿色家装", true),
    SHANG_JIA_RU_ZHU(3, "商家入驻", false);


    @EnumValue
    private final Integer value;
    private final String desc;
    private final boolean enable;

    public static List<Integer> allBusinessLineValues() {
        return Arrays.stream(BusinessLine.values()).map(BusinessLine::getValue).collect(Collectors.toList());
    }


}
