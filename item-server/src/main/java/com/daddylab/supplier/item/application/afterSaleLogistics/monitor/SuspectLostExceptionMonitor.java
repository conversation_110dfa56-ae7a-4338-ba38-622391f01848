//package com.daddylab.supplier.item.application.afterSaleLogistics.monitor;
//
//import com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsException;
//import com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsRootException;
//import com.daddylab.supplier.item.application.afterSaleLogistics.dto.LogisticExceptionRes;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsTrace;
//import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
//import org.springframework.core.annotation.Order;
//import org.springframework.stereotype.Component;
//
//import java.util.concurrent.TimeUnit;
//
///**
// * <AUTHOR> up
// * @date 2024年06月19日 4:47 PM
// */
//@Order(5)
//@Component
//public class SuspectLostExceptionMonitor implements OrderLogisticsExceptionMonitor{
//
//    @Override
//    public LogisticExceptionRes process(Long currentTurnTime, OrderLogisticsTrace trace) {
//        Long gap = DateUtil.calculateDifference(currentTurnTime, trace.getTrackTime(), TimeUnit.DAYS);
//        LogisticExceptionRes res = new LogisticExceptionRes();
//        if(gap >= 7){
//            res.setRootException(LogisticsRootException.TRANSFER_EXCEPTION);
//            res.setSubException(LogisticsException.TRANSFER_SUSPECT_LOST);
//            return res;
//        }
//        res.setRootException(LogisticsRootException.NORMAL);
//        return res;
//    }
//}
