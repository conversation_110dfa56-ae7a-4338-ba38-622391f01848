package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom;

import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2024年04月14日 11:28 AM
 */
@Data
public class EditSkuRatioDto {

    /**
     * 仓库编码
     */
    String warehouseNo;

    /**
     * sku编码
     */
    String skuNo;

    /**
     * 实际场景下，此仓库此SKU此时的新占比
     */
    Integer newVal;

    /**
     * 实际场景下，此仓库此SKU过去式占比
     */
    Integer oldVal;


    public static EditSkuRatioDto of(String warehouseNo, String skuNo, Integer oldVal, Integer newVal) {
        EditSkuRatioDto editSkuRatioDto = new EditSkuRatioDto();
        editSkuRatioDto.setWarehouseNo(warehouseNo);
        editSkuRatioDto.setSkuNo(skuNo);
        editSkuRatioDto.setOldVal(oldVal);
        editSkuRatioDto.setNewVal(newVal);

        return editSkuRatioDto;
    }

}
