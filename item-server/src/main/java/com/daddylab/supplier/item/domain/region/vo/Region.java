package com.daddylab.supplier.item.domain.region.vo;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.domain.region.enums.RegionLevel;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Optional;
import java.util.Stack;

@Data
@Accessors(chain = true)
@ToString(exclude = {"parent"})
@JsonAutoDetect(getterVisibility = JsonAutoDetect.Visibility.NONE)
public class Region {
    /**
     * 地区代码
     */
    @JsonProperty
    private String code;

    /**
     * 名称
     */
    @JsonProperty
    private String name;

    /**
     * 等级
     */
    @JsonProperty
    private RegionLevel level;

    /**
     * 排序
     */
    @JsonProperty
    private Integer sort;

    /**
     * 下级地区代码
     */
    @JsonProperty
    private List<Region> children;

    /**
     * 上级地区ID
     */
    @JsonProperty
    private Long parentId;

    /**
     * 上级地区编码
     */
    @JsonProperty
    private String parentCode;

    /**
     * 上级地区
     */
    private Region parent;

    /**
     * 根据名称查询下级区域
     */
    public Region getChildrenByName(String name) {
        if (CollUtil.isNotEmpty(children)) {
            if (StringUtil.endWithAny(name, "省", "市", "区", "县")) {
                name = name.substring(0, name.length() - 1);
            }
            for (Region child : children) {
                if (StringUtil.isNotBlank(child.getName())
                        && child.getName().contains(name)) {
                    return child;
                }
            }
        }
        return null;
    }

    public boolean hasChildren() {
        return CollUtil.isNotEmpty(children);
    }

    public Region getRoot() {
        Region root = this;
        while (root.getParent() != null) {
            root = root.getParent();
        }
        return root;
    }

    public Optional<Region> getAncestor(RegionLevel level) {
        Region region = this;
        while (true) {
            if (region.isLevel(level)) {
                return Optional.of(region);
            }
            if (region.getParent() == null) {
                return Optional.empty();
            }
            region = region.getParent();
        }
    }

    public boolean isLevel(RegionLevel level) {
        return this.level == level;
    }

    public String getPath() {
        Region region = this;
        final Stack<String> path = new Stack<>();
        path.add(region.name);
        while (region.getParent() != null) {
            region = region.getParent();
            path.add(region.name);
        }
        return String.join("-", path);
    }
    
    public List<Region> getRegionPath() {
        Region region = this;
        final Stack<Region> path = new Stack<>();
        path.add(region);
        while (region.getParent() != null) {
            region = region.getParent();
            path.add(region);
        }
        return path;
    }
}
