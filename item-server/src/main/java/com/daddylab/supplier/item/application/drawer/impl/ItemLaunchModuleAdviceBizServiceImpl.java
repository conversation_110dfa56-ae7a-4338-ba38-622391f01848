package com.daddylab.supplier.item.application.drawer.impl;

import com.daddylab.supplier.item.application.drawer.ItemLaunchModuleAdviceBizService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchModuleAdvice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerModuleAuditService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemLaunchModuleAdviceService;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerModuleId;

import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/11/14
 */
@Service
@RequiredArgsConstructor
public class ItemLaunchModuleAdviceBizServiceImpl implements ItemLaunchModuleAdviceBizService {

    private final IItemLaunchModuleAdviceService itemLaunchModuleAdviceService;
    private final IItemDrawerModuleAuditService itemDrawerModuleAuditService;

    @Override
    public Map<ItemDrawerModuleId, List<ItemLaunchModuleAdvice>> getModuleAdvices(Long itemId, Integer round) {
        final List<ItemLaunchModuleAdvice> advicePoList = itemLaunchModuleAdviceService.lambdaQuery()
                .eq(ItemLaunchModuleAdvice::getItemId, itemId)
                .eq(ItemLaunchModuleAdvice::getRound, round).list();
        return advicePoList.stream().filter(v -> StringUtil.isNotBlank(v.getAdvice()))
                .collect(Collectors.groupingBy(ItemLaunchModuleAdvice::getModule));
    }

    @Override
    public Map<ItemDrawerModuleId, Map<Integer, List<ItemLaunchModuleAdvice>>> getModuleAdvicesAllRound(
            Long itemId) {
        final List<ItemLaunchModuleAdvice> advicePoList =
                itemLaunchModuleAdviceService
                        .lambdaQuery()
                        .eq(ItemLaunchModuleAdvice::getItemId, itemId)
                        .eq(ItemLaunchModuleAdvice::getTemporary,0)
                        .list();
        return advicePoList.stream()
                .collect(
                        Collectors.groupingBy(
                                ItemLaunchModuleAdvice::getModule,
                                Collectors.groupingBy(ItemLaunchModuleAdvice::getRound)));
    }

    @Override
    public Map<Integer, List<ItemLaunchModuleAdvice>> getModuleAdvicesAllRoundByLiveVerbalTrickId(Long itemId, Long verbalTrickId) {
        final List<ItemLaunchModuleAdvice> advicePoList =
                itemLaunchModuleAdviceService
                        .lambdaQuery()
                        .eq(ItemLaunchModuleAdvice::getItemId, itemId)
                        .eq(ItemLaunchModuleAdvice::getLiveVerbalTrickId, verbalTrickId)
                        .list();
        return advicePoList.stream().collect(Collectors.groupingBy(ItemLaunchModuleAdvice::getRound));

    }
}
