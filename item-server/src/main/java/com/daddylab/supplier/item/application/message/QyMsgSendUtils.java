package com.daddylab.supplier.item.application.message;

import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MsgTemplateCode;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/5/15
 */
public class QyMsgSendUtils {
    public static long send(RemindType remindType, MsgTemplateCode code, Map<String, Object> variables) {
        return SpringUtil.getBean(QyMsgSendService.class).send(remindType, code, variables);
    }

}
