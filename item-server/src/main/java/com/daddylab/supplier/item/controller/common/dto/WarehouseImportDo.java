package com.daddylab.supplier.item.controller.common.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.springframework.util.Assert;

import java.util.Optional;

/**
 * <AUTHOR> up
 * @date 2024年07月22日 2:39 PM
 */
@Data
public class WarehouseImportDo {

    @ExcelProperty("*仓库编码")
    private String no;

    @ExcelProperty("*仓库名称")
    private String name;

    @ExcelProperty("售后客服")
    private String customerService;

    @ExcelProperty("*售后联系人")
    private String concat;

    @ExcelProperty("*联系电话")
    private String concatTel;

    @ExcelProperty("*售后地址(省市区+详细地址)")
    private String address;

    @ExcelProperty("备注")
    private String remark;

    public void verify() {
        Assert.hasText(no, "仓库编号不得为空");
        Assert.hasText(name, "仓库名称不得为空");
        Assert.hasText(concat, "联系人不得为空");
        Assert.hasText(concatTel, "联系电话不得为空");
        Assert.hasText(address, "售后地址不得为空");

        Assert.isTrue(concatTel.matches("\\d+"), "此联系人电话输入非法." + concatTel);

        this.no = Optional.ofNullable(this.no).map(String::trim).orElse("");
        this.name = Optional.ofNullable(this.name).map(String::trim).orElse("");
        this.customerService = Optional.ofNullable(this.customerService).map(String::trim).orElse("");
        this.concat = Optional.ofNullable(this.concat).map(String::trim).orElse("");
        this.concatTel = Optional.ofNullable(this.concatTel).map(String::trim).orElse("");
        this.address = Optional.ofNullable(this.address).map(String::trim).orElse("");
        this.remark = Optional.ofNullable(this.remark).map(String::trim).orElse("");

    }

}
