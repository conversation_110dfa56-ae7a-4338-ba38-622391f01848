package com.daddylab.supplier.item.application.entryActivityPrice;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTPayload;
import cn.hutool.jwt.JWTUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.daddylab.supplier.item.application.entryActivityPrice.models.*;
import com.daddylab.supplier.item.application.shortlink.ShortLinkService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.ResponseAssert;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.domain.exportTask.ExportManager;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.smsAuth.SmsAuthService;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.email.EmailService;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.EntryActivityPriceItemInfoDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.EntryActivityPricePageDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.PartnerContacts;
import com.daddylab.supplier.item.infrastructure.submail.SubMailService;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/10/6
 */
@Service
@Slf4j
public class EntryActivityPriceServiceImpl implements EntryActivityPriceService {

    @Resource
    IEntryActivityPriceItemService entryActivityPriceItemService;
    @Resource
    IEntryActivityPriceSkuService entryActivityPriceSkuService;
    @Resource
    IEntryActivityPriceConfirmService entryActivityPriceConfirmService;
    @Resource
    IEntryActivityPriceDissentService entryActivityPriceDissentService;
    @Resource
    IItemSkuService itemSkuService;
    @Resource
    IItemService itemService;
    @Resource
    IItemImageService itemImageService;
    @Resource
    ShortLinkService shortLinkService;
    @Resource
    EntryActivityPriceConfig entryActivityPriceConfig;
    @Resource
    SmsAuthService smsAuthService;
    @Resource
    private SubMailService subMailService;
    @Resource
    private EmailService emailService;

    @Resource
    private IBizLevelDivisionService iBizLevelUnionService;

    @Override
    public PageResponse<EntryActivityPricePageVO> page(EntryActivityPricePageQry qry) {
        final PageInfo<EntryActivityPricePageDO> pageInfo =
                PageHelper.startPage(qry.getPageIndex(), qry.getPageSize())
                        .doSelectPageInfo(() -> entryActivityPriceItemService.getBaseMapper().pageQuery(qry));
        final List<EntryActivityPricePageDO> list = pageInfo.getList();
        if (list.isEmpty()) {
            return PageResponse.of(new ArrayList<>(), 0, 0, 0);
        }
        final List<Long> itemIds =
                list.stream().map(EntryActivityPricePageDO::getItemId).collect(Collectors.toList());
        final List<Long> priceItemIds =
                list.stream().map(EntryActivityPricePageDO::getId).collect(Collectors.toList());
        final List<EntryActivityPriceSku> skuList =
                entryActivityPriceSkuService
                        .lambdaQuery()
                        .in(EntryActivityPriceSku::getPriceItemId, priceItemIds)
                        .list();
        final List<Long> skuIds =
                skuList.stream().map(EntryActivityPriceSku::getSkuId).collect(Collectors.toList());
        if (skuIds.isEmpty()) {
            return PageResponse.of(new ArrayList<>(), 0, 0, 0);
        }
        final List<ItemSku> itemSkus = itemSkuService.listByIds(skuIds);
        final Map<Long, String> itemMainImageToMap = itemImageService.getItemMainImageToMap(itemIds);
        final Map<Long, ItemSku> itemSkuMap =
                itemSkus.stream().collect(Collectors.toMap(ItemSku::getId, Function.identity()));
        final ArrayList<EntryActivityPricePageVO> entryActivityPricePageVOS = new ArrayList<>();

        final Set<Long> itemIdSet = list.stream().map(EntryActivityPricePageDO::getItemId).collect(Collectors.toSet());
        final Map<Long, List<CorpBizTypeDTO>> itemDivisionMap = iBizLevelUnionService.queryByItemId(itemIdSet);

        for (EntryActivityPricePageDO entryActivityPricePageDO : list) {
            EntryActivityPricePageVO vo =
                    EntryActivityPriceConvert.INSTANCE.toPageVO(entryActivityPricePageDO);
            entryActivityPricePageVOS.add(vo);
            vo.setImgUrl(itemMainImageToMap.get(vo.getItemId()));
            final List<EntryActivityPriceSku> priceSkus =
                    skuList.stream()
                            .filter(v -> Objects.equals(v.getPriceItemId(), vo.getId()))
                            .collect(Collectors.toList());
            final ArrayList<EntryActivityPriceSkuVO> priceSkuVOS = new ArrayList<>();
            for (EntryActivityPriceSku priceSku : priceSkus) {
                final EntryActivityPriceSkuVO entryActivityPriceSkuVO = new EntryActivityPriceSkuVO();
                entryActivityPriceSkuVO.setId(priceSku.getId());
                entryActivityPriceSkuVO.setSkuCode(priceSku.getSkuCode());
                entryActivityPriceSkuVO.setSkuId(priceSku.getSkuId());
                final Optional<ItemSku> itemSku = Optional.ofNullable(itemSkuMap.get(priceSku.getSkuId()));
                entryActivityPriceSkuVO.setSpecification(
                        itemSku.map(ItemSku::getSpecifications).orElse(""));
                entryActivityPriceSkuVO.setActiveStart(priceSku.getActiveStart());
                entryActivityPriceSkuVO.setActiveEnd(priceSku.getActiveEnd());
                entryActivityPriceSkuVO.setContractSalePrice(priceSku.getContractSalePrice());
                entryActivityPriceSkuVO.setPlatformCommission(priceSku.getPlatformCommission());
                entryActivityPriceSkuVO.setActivityRemark(priceSku.getActivityRemark());
                priceSkuVOS.add(entryActivityPriceSkuVO);
            }
            vo.setSkuList(priceSkuVOS);

            vo.setCorpBizType(itemDivisionMap.getOrDefault(entryActivityPricePageDO.getItemId(),Collections.emptyList()));
        }

        return PageResponse.of(
                entryActivityPricePageVOS,
                (int) pageInfo.getTotal(),
                pageInfo.getPageSize(),
                pageInfo.getPageNum());
    }

    @Override
    public SingleResponse<EntryActivityPriceItemInfoVO> itemInfo(EntryActivityPriceItemInfoQry qry) {
        final List<EntryActivityPriceItemInfoDO> entryActivityPriceItemInfoDOS =
                entryActivityPriceItemService.getBaseMapper().itemInfo(qry);
        if (entryActivityPriceItemInfoDOS.isEmpty()) {
            return SingleResponse.of(null);
        }
        final EntryActivityPriceItemInfoVO itemInfoVO =
                EntryActivityPriceConvert.INSTANCE.toItemInfoVO(entryActivityPriceItemInfoDOS.get(0));
        final ItemImage itemMainImage = itemImageService.getItemMainImage(itemInfoVO.getItemId());
        if (itemMainImage != null) {
            itemInfoVO.setImgUrl(itemMainImage.getImageUrl());
        }
        final EntryActivityPriceSkuInfoQry skuInfoQry = new EntryActivityPriceSkuInfoQry();
        skuInfoQry.setItemCode(qry.getItemCode());
        final List<EntryActivityPriceSkuInfoVO> entryActivityPriceItemSkuInfoDOS =
                entryActivityPriceItemService.getBaseMapper().skuInfo(skuInfoQry);
        itemInfoVO.setSkuList(entryActivityPriceItemSkuInfoDOS);

        final Map<Long, List<CorpBizTypeDTO>> divisionMap = iBizLevelUnionService.queryByItemId(ListUtil.of(itemInfoVO.getItemId()));
        itemInfoVO.setCorpBizType(divisionMap.getOrDefault(itemInfoVO.getItemId(),new LinkedList<>()));

        return SingleResponse.of(itemInfoVO);
    }

    @Override
    public SingleResponse<EntryActivityPriceSkuInfoVO> skuInfo(EntryActivityPriceSkuInfoQry qry) {
        final List<EntryActivityPriceSkuInfoVO> entryActivityPriceItemInfoDOS =
                entryActivityPriceItemService.getBaseMapper().skuInfo(qry);
        if (entryActivityPriceItemInfoDOS.isEmpty()) {
            return SingleResponse.of(null);
        }
        return SingleResponse.of(entryActivityPriceItemInfoDOS.get(0));
    }

    @Override
    public Response importExcel(InputStream inputStream) {
        final List<EntryActivityPricePageImportVO> objects =
                EasyExcel.read(inputStream)
                        .head(EntryActivityPricePageImportVO.class)
                        .ignoreEmptyRow(true)
                        .doReadAllSync();

        final EntryActivityPriceSaveReq cmd = new EntryActivityPriceSaveReq();
        final ArrayList<EntryActivityPricePageVO> itemList = Lists.newArrayList();
        final Map<String, List<EntryActivityPricePageImportVO>> group =
                objects.stream()
                        .filter(v -> StringUtil.isNotBlank(v.getItemCode()))
                        .collect(Collectors.groupingBy(EntryActivityPricePageImportVO::getItemCode));
        group.forEach(
                (itemCode, importVOS) -> {
                    final EntryActivityPricePageVO entryActivityPricePageVO = new EntryActivityPricePageVO();
                    if (importVOS.isEmpty()) {
                        return;
                    }
                    entryActivityPricePageVO.setItemCode(itemCode);
                    final Item item = itemService.getByMixedCode(itemCode);
                    if (item == null) {
                        throw ExceptionPlusFactory.bizException(
                                ErrorCode.DATA_NOT_FOUND, String.format("商品编码[%s]不存在", itemCode));
                    }
                    entryActivityPricePageVO.setItemId(item.getId());
                    entryActivityPricePageVO.setItemName(item.getName());
                    entryActivityPricePageVO.setProviderId(item.getProviderId());
                    final ArrayList<EntryActivityPriceSkuVO> priceSkuVOS = Lists.newArrayList();
                    entryActivityPricePageVO.setSkuList(priceSkuVOS);
                    for (EntryActivityPricePageImportVO object : importVOS) {
                        final String activeTime = object.getActiveTime();
                        final String skuCode = object.getSkuCode();
                        if (StringUtil.isBlank(skuCode)) {
                            throw ExceptionPlusFactory.bizException(
                                    ErrorCode.VERIFY_PARAM, String.format("商品[%s]商品SKU为空", itemCode));
                        }
                        if (StringUtil.isBlank(activeTime)) {
                            throw ExceptionPlusFactory.bizException(
                                    ErrorCode.VERIFY_PARAM, String.format("商品SKU[%s]活动时间为空", skuCode));
                        }
                        ParseActiveTimeResult parseActiveTimeResult = parseActiveTime(activeTime);
                        if (parseActiveTimeResult == null) {
                            throw ExceptionPlusFactory.bizException(
                                    ErrorCode.VERIFY_PARAM, String.format("商品SKU[%s]活动时间解析异常", skuCode));
                        }
                        entryActivityPricePageVO.setMonth(parseActiveTimeResult.getStartTS());
                        final EntryActivityPriceSkuVO entryActivityPriceSkuVO = new EntryActivityPriceSkuVO();
                        entryActivityPriceSkuVO.setActiveStart(parseActiveTimeResult.startTS);
                        entryActivityPriceSkuVO.setActiveEnd(parseActiveTimeResult.endTS);
                        entryActivityPriceSkuVO.setSkuCode(object.getSkuCode());
                        final ItemSku itemSku =
                                itemSkuService
                                        .getByMixCode(object.getSkuCode())
                                        .orElseThrow(
                                                () ->
                                                        ExceptionPlusFactory.bizException(
                                                                ErrorCode.DATA_NOT_FOUND, "商品SKU不存在:" + object.getSkuCode()));
                        entryActivityPriceSkuVO.setSkuId(itemSku.getId());
                        entryActivityPriceSkuVO.setSpecification(itemSku.getSpecifications());
                        entryActivityPriceSkuVO.setContractSalePrice(
                                new BigDecimal(object.getContractSalePrice()));
                        entryActivityPriceSkuVO.setPlatformCommission(
                                new BigDecimal(object.getPlatformCommission()));
                        entryActivityPriceSkuVO.setActivityRemark(object.getActivityRemark());
                        priceSkuVOS.add(entryActivityPriceSkuVO);
                    }
                    itemList.add(entryActivityPricePageVO);
                });
        cmd.setItemList(itemList);

        return save(cmd);
    }

    private ParseActiveTimeResult parseActiveTime(String activeTime) {
        final List<String> separators0 = Arrays.asList("~", "～", "-");
        final ArrayList<String> separators = new ArrayList<>();
        for (String separator : separators0) {
            separators.add(separator);
            separators.add(" " + separator + " ");
            separators.add(" " + separator);
            separators.add(separator + " ");
        }
        for (String sep : separators) {
            final int indexOfSep = activeTime.indexOf(sep);
            if (indexOfSep > 0) {
                final String startTimeStr = activeTime.substring(0, indexOfSep);
                final String endTimeStr = activeTime.substring(indexOfSep + 1);
                final Long startTS = DateUtil.parseDateTime2timestamp(startTimeStr);
                final Long endTS = DateUtil.parseDateTime2timestamp(endTimeStr);
                return new ParseActiveTimeResult(startTS, endTS);
            }
        }
        return null;
    }

    @Data
    public static class ParseActiveTimeResult {
        public final Long startTS;
        public final Long endTS;
    }

    @Resource
    ExportManager exportManager;

    @Override
    public Response exportExcel(EntryActivityPricePageQry query) {
        exportManager.export(
                ExportTaskType.ENTRY_ACTIVITY_PRICE,
                EntryActivityPricePageExportVO.class,
                pageIndex -> {
                    query.setPageIndex(pageIndex);
                    final PageResponse<EntryActivityPricePageVO> page = page(query);
                    return ResponseFactory.ofPageFlatmap(
                            page,
                            v -> {
                                final ArrayList<EntryActivityPricePageExportVO> entryActivityPricePageExportVOS =
                                        new ArrayList<>();
                                for (EntryActivityPriceSkuVO entryActivityPriceSkuVO : v.getSkuList()) {
                                    final EntryActivityPricePageExportVO exportVO =
                                            new EntryActivityPricePageExportVO();
                                    exportVO.setSkuCode(entryActivityPriceSkuVO.getSkuCode());
                                    exportVO.setSpecification(entryActivityPriceSkuVO.getSpecification());
                                    exportVO.setActiveTime(
                                            DateUtil.format(entryActivityPriceSkuVO.getActiveStart())
                                                    + "~"
                                                    + DateUtil.format(entryActivityPriceSkuVO.getActiveEnd()));
                                    exportVO.setContractSalePrice(
                                            entryActivityPriceSkuVO.getContractSalePrice().toPlainString());
                                    exportVO.setPlatformCommission(
                                            entryActivityPriceSkuVO.getPlatformCommission().toPlainString());
                                    exportVO.setItemCode(v.getItemCode());
                                    exportVO.setItemName(v.getItemName());
                                    exportVO.setProvider(v.getProvider());
                                    exportVO.setBuyer(v.getBuyer().getNickname());
                                    exportVO.setActivityRemark(entryActivityPriceSkuVO.getActivityRemark());
                                    switch (v.getStatus()) {
                                        case -1:
                                            exportVO.setStatus("待发起");
                                            break;
                                        case 0:
                                            exportVO.setStatus("待确认");
                                            break;
                                        case 1:
                                            exportVO.setStatus("已确认");
                                            break;
                                        case 2:
                                            exportVO.setStatus("存在异议");
                                            break;
                                    }

                                    // 合作方层级导出
                                    final String coryValStr = v.getCorpBizType().stream()
                                            .map(CorpBizTypeDTO::getCorpType)
                                            .map(val -> IEnum.getEnumByValue(DivisionLevelValueEnum.class, val).getDesc())
                                            .collect(Collectors.joining(","));
                                    exportVO.setCoryTypeVal(coryValStr);

                                    entryActivityPricePageExportVOS.add(exportVO);
                                }
                                return entryActivityPricePageExportVOS.stream();
                            });
                },
                (t, e) -> {
                    t.setParams(JsonUtil.toJson(query));
                });
        return Response.buildSuccess();
    }

    @Override
    public Response save(EntryActivityPriceSaveReq cmd) {
        if (cmd.getItemList().isEmpty()) {
            return Response.buildSuccess();
        }
        for (EntryActivityPricePageVO entryActivityPricePageVO : cmd.getItemList()) {
            final EntryActivityPriceItem entryActivityPriceItem =
                    EntryActivityPriceConvert.INSTANCE.toItemPO(entryActivityPricePageVO);
            if (entryActivityPriceItem.getId() != null) {
                entryActivityPriceItem.setStatus(null);
            }
            checkConstraint(entryActivityPriceItem);
            entryActivityPriceItemService.saveOrUpdate(entryActivityPriceItem);
            if (CollectionUtil.isNotEmpty(entryActivityPricePageVO.getSkuList())) {
                final ArrayList<EntryActivityPriceSku> entryActivityPriceSkus = new ArrayList<>();
                for (EntryActivityPriceSkuVO entryActivityPriceSkuVO :
                        entryActivityPricePageVO.getSkuList()) {
                    final EntryActivityPriceSku entryActivityPriceSku =
                            EntryActivityPriceConvert.INSTANCE.toSkuPO(entryActivityPriceSkuVO);
                    entryActivityPriceSku.setPriceItemId(entryActivityPriceItem.getId());
                    entryActivityPriceSku.setItemId(entryActivityPriceItem.getItemId());
                    entryActivityPriceSku.setItemCode(entryActivityPriceItem.getItemCode());
                    entryActivityPriceSkus.add(entryActivityPriceSku);
                }
                checkConstraint(entryActivityPriceSkus);
                entryActivityPriceSkuService.saveOrUpdateBatch(entryActivityPriceSkus);
            }
            final List<EntryActivityPriceSku> skuList =
                    entryActivityPriceSkuService
                            .lambdaQuery()
                            .eq(EntryActivityPriceSku::getPriceItemId, entryActivityPriceItem.getId())
                            .list();
            final List<EntryActivityPriceSku> noMatchSkus =
                    skuList.stream()
                            .filter(v -> !Objects.equals(v.getItemId(), entryActivityPriceItem.getItemId()))
                            .collect(Collectors.toList());
            if (!noMatchSkus.isEmpty()) {
                final List<Long> noMatchSkuIds =
                        noMatchSkus.stream().map(EntryActivityPriceSku::getId).collect(Collectors.toList());
                entryActivityPriceSkuService.removeByIds(noMatchSkuIds);
            }
        }
        return Response.buildSuccess();
    }

    private void checkConstraint(EntryActivityPriceItem entryActivityPriceItem) {
        final List<EntryActivityPriceItem> duplicateItems =
                entryActivityPriceItemService
                        .lambdaQuery()
                        .eq(EntryActivityPriceItem::getMonth, entryActivityPriceItem.getMonth())
                        .eq(EntryActivityPriceItem::getItemId, entryActivityPriceItem.getItemId())
                        .ne(
                                entryActivityPriceItem.getId() != null,
                                EntryActivityPriceItem::getId,
                                entryActivityPriceItem.getId())
                        .list();
        if (!duplicateItems.isEmpty()) {
            throw ExceptionPlusFactory.bizException(
                    ErrorCode.VERIFY_PARAM,
                    String.format("当前商品[%s]此月已存在入驻活动，请确认或修改后填写", entryActivityPriceItem.getItemCode()));
        }
    }

    private void checkConstraint(ArrayList<EntryActivityPriceSku> entryActivityPriceSkus) {
        entryActivityPriceSkus.stream()
                .collect(Collectors.groupingBy(EntryActivityPriceSku::getSkuCode))
                .forEach(
                        (skuCode, items) -> {
                            if (items.size() > 1) {
                                throw ExceptionPlusFactory.bizException(
                                        ErrorCode.VERIFY_PARAM, String.format("商品SKU[%s]存在重复，请确认或修改后填写", skuCode));
                            }
                        });
        for (EntryActivityPriceSku activityPriceSku : entryActivityPriceSkus) {
            if (activityPriceSku.getActiveStart() > activityPriceSku.getActiveEnd()) {
                throw ExceptionPlusFactory.bizException(
                        ErrorCode.VERIFY_PARAM,
                        String.format("商品SKU[%s]活动时间区间有误，请确认", activityPriceSku.getSkuCode()));
            }
            final List<EntryActivityPriceSku> timeIntersectionList =
                    entryActivityPriceSkuService.findTimeIntersectionSkuList(
                            activityPriceSku.getSkuCode(),
                            activityPriceSku.getActiveStart(),
                            activityPriceSku.getActiveEnd());
            if (!timeIntersectionList.isEmpty()) {
                final String[][] timeIntersection =
                        timeIntersectionList.stream()
                                .map(
                                        v ->
                                                new String[]{
                                                        DateUtil.format(v.getActiveStart()), DateUtil.format(v.getActiveEnd())
                                                })
                                .toArray(String[][]::new);
                throw ExceptionPlusFactory.bizException(
                        ErrorCode.VERIFY_PARAM,
                        String.format(
                                "当前商品SKU[%s]此活动时间已存在，请确认或修改后填写:%s",
                                activityPriceSku.getSkuCode(), Arrays.toString(timeIntersection)));
            }
        }
    }

    @Override
    public Response delete(EntryActivityPriceDeleteReq cmd) {
        final List<Long> idList = cmd.getIdList();
        final List<EntryActivityPriceSku> entryActivityPriceSkus =
                entryActivityPriceSkuService.listByIds(idList);
        entryActivityPriceSkuService.removeByIds(idList);
        final List<Long> itemIds =
                entryActivityPriceSkus.stream()
                        .map(EntryActivityPriceSku::getItemId)
                        .collect(Collectors.toList());
        final List<EntryActivityPriceSku> skuList =
                entryActivityPriceSkuService
                        .lambdaQuery()
                        .in(EntryActivityPriceSku::getItemId, itemIds)
                        .list();
        final HashSet<Long> removedItemIds = new HashSet<>();
        for (EntryActivityPriceSku activityPriceSkus : entryActivityPriceSkus) {
            final List<EntryActivityPriceSku> priceSkus =
                    skuList.stream()
                            .filter(v -> Objects.equals(v.getPriceItemId(), activityPriceSkus.getPriceItemId()))
                            .collect(Collectors.toList());
            final Long priceItemId = activityPriceSkus.getPriceItemId();
            if (priceSkus.isEmpty() && !removedItemIds.contains(priceItemId)) {
                entryActivityPriceItemService.removeById(priceItemId);
                removedItemIds.add(priceItemId);
            }
        }
        return Response.buildSuccess();
    }

    @Override
    public SingleResponse<PrepareStartConfirmationResult> prepareStartConfirmation(
            PrepareStartConfirmationReq req) {
        final PrepareStartConfirmationResult prepareStartConfirmationResult =
                new PrepareStartConfirmationResult();
        final EntryActivityPricePageQry qry = new EntryActivityPricePageQry();
        qry.setPriceItemIdList(req.getIdList());
        final List<Integer> statusList =
                Arrays.asList(
                        EntryActivityPriceStatus.TO_BE_STARTED.getValue(),
                        EntryActivityPriceStatus.HAS_DISSENT.getValue());
        qry.setStatusList(statusList);
        qry.setPageSize(9999);
        final PageResponse<EntryActivityPricePageVO> page = page(qry);
        ResponseAssert.assertJust(page);
        if (page.getData().isEmpty()) {
            return SingleResponse.of(prepareStartConfirmationResult);
        }
        List<ConfirmationViewItemVO> confirmationViewItemVOList =
                EntryActivityPriceConvert.INSTANCE.toConfirmationViewItemVOList(page.getData());
        final List<Long> providerIds =
                confirmationViewItemVOList.stream()
                        .map(ConfirmationViewItemVO::getProviderId)
                        .collect(Collectors.toList());
        final EntryActivityPricePageQry qryForProvider = new EntryActivityPricePageQry();
        qryForProvider.setProviderIds(providerIds);
        qryForProvider.setStatusList(Arrays.asList(EntryActivityPriceStatus.TO_BE_STARTED.getValue()));
        qry.setPageSize(9999);
        final PageResponse<EntryActivityPricePageVO> pageForOtherThisProvider = page(qryForProvider);
        List<ConfirmationViewItemVO> otherToSelectList =
                EntryActivityPriceConvert.INSTANCE.toConfirmationViewItemVOList(
                        pageForOtherThisProvider.getData());
        confirmationViewItemVOList.forEach(otherToSelectList::remove);
        prepareStartConfirmationResult.setItemList(confirmationViewItemVOList);
        prepareStartConfirmationResult.setToSelectList(otherToSelectList);
        return SingleResponse.of(prepareStartConfirmationResult);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Response startConfirmation(StartConfirmationReq req) {
        if (CollectionUtil.isEmpty(req.getIdList())) {
            throw ExceptionPlusFactory.bizException(
                    ErrorCode.VERIFY_PARAM, "请选择需要发起确认的商品");
        }
        final List<Integer> toConfirmStatusList =
                Arrays.asList(
                        EntryActivityPriceStatus.TO_BE_STARTED.getValue(),
                        EntryActivityPriceStatus.HAS_DISSENT.getValue(),
                        EntryActivityPriceStatus.CONFIRMED.getValue());
        final List<EntryActivityPriceItem> entryActivityPriceItems =
                entryActivityPriceItemService
                        .lambdaQuery()
                        .in(EntryActivityPriceItem::getStatus, toConfirmStatusList)
                        .in(EntryActivityPriceItem::getId, req.getIdList())
                        .list();
        if (entryActivityPriceItems.isEmpty()) {
            throw ExceptionPlusFactory.bizException(
                    ErrorCode.VERIFY_PARAM, "选择的商品状态不可确认");
        }
        if (req.isIncludeOtherSameProvider()) {
            final List<Long> toConfirmSelectIds =
                    entryActivityPriceItems.stream()
                            .map(EntryActivityPriceItem::getId)
                            .collect(Collectors.toList());
            final List<Long> providerIds =
                    entryActivityPriceItems.stream()
                            .map(EntryActivityPriceItem::getProviderId)
                            .filter(NumberUtil::isPositive)
                            .collect(Collectors.toList());
            final List<EntryActivityPriceItem> entryActivityPriceItemsOther =
                    entryActivityPriceItemService
                            .lambdaQuery()
                            .in(
                                    EntryActivityPriceItem::getStatus,
                                    Arrays.asList(EntryActivityPriceStatus.TO_BE_STARTED.getValue()))
                            .in(EntryActivityPriceItem::getProviderId, providerIds)
                            .notIn(EntryActivityPriceItem::getId, toConfirmSelectIds)
                            .list();
            if (!entryActivityPriceItemsOther.isEmpty()) {
                entryActivityPriceItems.addAll(entryActivityPriceItemsOther);
            }
        }
        final Map<Long, List<EntryActivityPriceItem>> itemsGroup =
                entryActivityPriceItems.stream()
                        .collect(Collectors.groupingBy(EntryActivityPriceItem::getProviderId));
        itemsGroup.forEach(
                (providerId, items) -> {
                    // 待发起的商品发起确认
                    final List<EntryActivityPriceItem> toStartConfirmList =
                            items.stream()
                                    .filter(
                                            v -> EntryActivityPriceStatus.TO_BE_STARTED.getValue().equals(v.getStatus()))
                                    .collect(Collectors.toList());
                    if (!toStartConfirmList.isEmpty()) {
                        final EntryActivityPriceConfirm activityPriceConfirm = new EntryActivityPriceConfirm();
                        activityPriceConfirm.setShortLink("");
                        activityPriceConfirm.setProviderId(providerId);
                        entryActivityPriceConfirmService.save(activityPriceConfirm);
                        final String confirmLink =
                                String.format(
                                        entryActivityPriceConfig.getConfirmUrl(), activityPriceConfirm.getId());
                        final String confirmShortLink = shortLinkService.generate(confirmLink);
                        activityPriceConfirm.setShortLink(confirmShortLink);
                        entryActivityPriceConfirmService.updateById(activityPriceConfirm);

                        entryActivityPriceItemService.updateBatchById(
                                toStartConfirmList.stream()
                                        .map(
                                                v -> {
                                                    final EntryActivityPriceItem entryActivityPriceItem =
                                                            new EntryActivityPriceItem();
                                                    entryActivityPriceItem.setId(v.getId());
                                                    entryActivityPriceItem.setConfirmId(activityPriceConfirm.getId());
                                                    entryActivityPriceItem.setStatus(
                                                            EntryActivityPriceStatus.TO_BE_CONFIRMED.getValue());
                                                    v.setConfirmId(activityPriceConfirm.getId());
                                                    v.setStatus(EntryActivityPriceStatus.TO_BE_CONFIRMED.getValue());
                                                    return entryActivityPriceItem;
                                                })
                                        .collect(Collectors.toList()));
                    }
                    // 存在异议的商品重新发起确认
                    final List<EntryActivityPriceItem> dissentList =
                            items.stream()
                                    .filter(
                                            v -> EntryActivityPriceStatus.HAS_DISSENT.getValue().equals(v.getStatus()))
                                    .collect(Collectors.toList());
                    if (!dissentList.isEmpty()) {
                        entryActivityPriceItemService.updateBatchById(
                                dissentList.stream()
                                        .map(
                                                v -> {
                                                    final EntryActivityPriceItem entryActivityPriceItem =
                                                            new EntryActivityPriceItem();
                                                    entryActivityPriceItem.setId(v.getId());
                                                    entryActivityPriceItem.setStatus(
                                                            EntryActivityPriceStatus.TO_BE_CONFIRMED.getValue());
                                                    v.setStatus(EntryActivityPriceStatus.TO_BE_CONFIRMED.getValue());
                                                    return entryActivityPriceItem;
                                                })
                                        .collect(Collectors.toList()));
                    }

                    // 已确认的商品重新也可以重新发起确认
                    final List<EntryActivityPriceItem> confirmedList =
                            items.stream()
                                    .filter(v -> EntryActivityPriceStatus.CONFIRMED.getValue().equals(v.getStatus()))
                                    .collect(Collectors.toList());
                    if (!confirmedList.isEmpty()) {
                        entryActivityPriceItemService.updateBatchById(
                                confirmedList.stream()
                                        .map(
                                                v -> {
                                                    final EntryActivityPriceItem entryActivityPriceItem =
                                                            new EntryActivityPriceItem();
                                                    entryActivityPriceItem.setId(v.getId());
                                                    entryActivityPriceItem.setStatus(
                                                            EntryActivityPriceStatus.TO_BE_CONFIRMED.getValue());
                                                    v.setStatus(EntryActivityPriceStatus.TO_BE_CONFIRMED.getValue());
                                                    return entryActivityPriceItem;
                                                })
                                        .collect(Collectors.toList()));
                    }
                });
        try {
            sendMsg(entryActivityPriceItems);
        } catch (Exception e) {
            log.error("[入驻活动价格][商品价格确认] 发送消息失败");
        }
        return Response.buildSuccess();
    }

    @Value
    public static class SendMsgKey {
        String mobileOrEmail;
        String url;
    }

    private void sendMsg(List<EntryActivityPriceItem> entryActivityPriceItems) {
        if (entryActivityPriceItems.isEmpty()) {
            log.warn("[入驻活动价格][商品价格确认]没有需要发送的入驻活动商品");
            return;
        }
        final String template = "老爸评测：尊敬的商家入驻合作供应商，当前合作价格发生变动，请及时查看并确认！\n\n" + "价格确认请点击：%s";
        final List<Long> itemIds =
                entryActivityPriceItems.stream()
                        .map(EntryActivityPriceItem::getItemId)
                        .collect(Collectors.toList());
        final List<Long> confirmIds =
                entryActivityPriceItems.stream()
                        .map(EntryActivityPriceItem::getConfirmId)
                        .collect(Collectors.toList());
        final List<EntryActivityPriceConfirm> entryActivityPriceConfirms =
                entryActivityPriceConfirmService.listByIds(confirmIds);
        final Map<Long, EntryActivityPriceConfirm> confirmMap =
                entryActivityPriceConfirms.stream()
                        .collect(Collectors.toMap(EntryActivityPriceConfirm::getId, Function.identity()));
        final List<Item> items = itemService.listByIds(itemIds);
        final Map<Long, Item> itemMap =
                items.stream().collect(Collectors.toMap(Entity::getId, Function.identity()));
        final HashMap<String, PartnerContacts> contactMap = new HashMap<>();
        final HashSet<SendMsgKey> smsMsgs = new HashSet<>();
        final HashSet<SendMsgKey> mailMsgs = new HashSet<>();
        for (EntryActivityPriceItem entryActivityPriceItem : entryActivityPriceItems) {
            final Long itemId = entryActivityPriceItem.getItemId();
            final Item item = itemMap.get(itemId);
            if (item == null) {
                log.error(
                        "[入驻活动价格][商品价格确认]商品不存在，忽略发送消息 entryActivityPriceItemId={} itemId={}",
                        entryActivityPriceItem.getId(),
                        itemId);
                continue;
            }
            final EntryActivityPriceConfirm activityPriceConfirm =
                    confirmMap.get(entryActivityPriceItem.getConfirmId());
            if (activityPriceConfirm == null) {
                log.error(
                        "[入驻活动价格][商品价格确认]确认记录不存在，忽略发送消息 entryActivityPriceItemId={} confirmId={}",
                        entryActivityPriceItem.getId(),
                        entryActivityPriceItem.getConfirmId());
                continue;
            }
            final String partnerProviderItemSn = item.getPartnerProviderItemSn();
            if (StringUtil.isBlank(partnerProviderItemSn)) {
                log.error(
                        "[入驻活动价格][商品价格确认]商品不存在供应商联系人信息，忽略发送消息 entryActivityPriceItemId={} confirmId={}",
                        entryActivityPriceItem.getId(),
                        entryActivityPriceItem.getConfirmId());
                continue;
            }
            try {
                final PartnerContacts contacts =
                        contactMap.computeIfAbsent(
                                partnerProviderItemSn, v -> providerGateway.getPartnerContactByItemNo(v));
                log.info("[入驻活动价格][商品价格确认]获取供应商联系人成功:{} contacts:{} entryActivityPriceItemId={} confirmId={}", confirmIds, contacts, entryActivityPriceItem.getId(), entryActivityPriceItem.getConfirmId());
                if (StringUtil.isNotBlank(contacts.getMobile())) {
                    smsMsgs.add(new SendMsgKey(contacts.getMobile(), activityPriceConfirm.getShortLink()));
                }
                if (StringUtil.isNotBlank(contacts.getEmail())) {
                    mailMsgs.add(new SendMsgKey(contacts.getEmail(), activityPriceConfirm.getShortLink()));
                }
            } catch (Exception e) {
                log.error(
                        "[入驻活动价格][商品价格确认]获取供应商联系人异常:{} partnerProviderItemSn:{} entryActivityPriceItemId={} confirmId={}",
                        confirmIds,
                        partnerProviderItemSn,
                        entryActivityPriceItem.getId(), entryActivityPriceItem.getConfirmId(),
                        e);
            }
        }
        if (!smsMsgs.isEmpty()) {
            smsMsgs.forEach(
                    v -> {
                        final String msg = String.format(template, v.getUrl());
                        try {
                            if (ApplicationContextUtil.isActiveProfile("prod", "gray")
                                    && entryActivityPriceConfig.isSmsEnabled()) {
                                log.info("[入驻活动价格][商品价格确认]发送短信 -> {}: {}", v.getMobileOrEmail(), msg);
                                subMailService.send(v.getMobileOrEmail(), msg);
                                log.info("[入驻活动价格][商品价格确认]发送短信，成功 -> {}: {}", v.getMobileOrEmail(), msg);
                            } else {
                                Alert.text(
                                        MessageRobotCode.NOTICE,
                                        String.format("模拟发送短信 -> %s : %s", v.getMobileOrEmail(), msg));
                            }
                        } catch (Exception e) {
                            log.error("[入驻活动价格][商品价格确认]发送短信失败: {}", msg, e);
                        }
                    });
        } else {
            log.warn("[入驻活动价格][商品价格确认]没有需要发送的短信:{}", confirmIds);
        }
        if (!mailMsgs.isEmpty()) {
            mailMsgs.forEach(
                    v -> {
                        final String msg = String.format(template, v.getUrl());
                        try {
                            if (ApplicationContextUtil.isActiveProfile("prod", "gray")
                                    && entryActivityPriceConfig.isEmailEnabled()) {
                                log.info("[入驻活动价格][商品价格确认]发送邮件 -> {}: {}", v.getMobileOrEmail(), msg);
                                emailService.sendSimpleMail(v.getMobileOrEmail(), "入驻活动价格确认", msg);
                                log.info("[入驻活动价格][商品价格确认]发送邮件，成功 -> {}: {}", v.getMobileOrEmail(), msg);
                            } else {
                                Alert.text(
                                        MessageRobotCode.NOTICE,
                                        String.format("模拟发送邮件 -> %s : %s", v.getMobileOrEmail(), msg));
                            }
                        } catch (Exception e) {
                            log.error("[入驻活动价格][商品价格确认]发送邮件失败: {}", msg, e);
                        }
                    });
        } else {
            log.warn("[入驻活动价格][商品价格确认]没有需要发送的邮件:{}", confirmIds);
        }
    }

    @Override
    public SingleResponse<ConfirmationViewVO> confirmationView(ConfirmationViewReq req) {
        final Long confirmId = req.getConfirmId();
        final EntryActivityPriceConfirm confirm = entryActivityPriceConfirmService.getById(confirmId);
        Objects.requireNonNull(confirm);
        final List<EntryActivityPriceItem> list =
                entryActivityPriceItemService
                        .lambdaQuery()
                        .eq(EntryActivityPriceItem::getConfirmId, confirm.getId())
                        .list();
        if (list.isEmpty()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "当前链接没有待确认的商品");
        }
        final List<Long> priceItemIds =
                list.stream().map(EntryActivityPriceItem::getId).collect(Collectors.toList());
        final List<EntryActivityPricePageVO> entryActivityPricePageVOS =
                listByPriceItemIds(priceItemIds);
        final List<ConfirmationViewItemVO> confirmationViewItemVOList =
                EntryActivityPriceConvert.INSTANCE.toConfirmationViewItemVOList(entryActivityPricePageVOS);
        final List<EntryActivityPriceDissent> entryActivityPriceDissents =
                entryActivityPriceDissentService
                        .lambdaQuery()
                        .in(
                                EntryActivityPriceDissent::getPriceItemId,
                                confirmationViewItemVOList.stream()
                                        .map(ConfirmationViewItemVO::getId)
                                        .collect(Collectors.toList()))
                        .list();
        entryActivityPriceDissents.stream()
                .collect(Collectors.groupingBy(EntryActivityPriceDissent::getPriceItemId))
                .forEach(
                        (priceItemId, dissents) -> {
                            confirmationViewItemVOList.stream()
                                    .filter(v -> Objects.equals(v.getId(), priceItemId))
                                    .forEach(
                                            v ->
                                                    v.setDissentList(
                                                            dissents.stream()
                                                                    .map(
                                                                            vv -> {
                                                                                final ConfirmationViewItemDissentVO
                                                                                        confirmationViewItemDissentVO =
                                                                                        new ConfirmationViewItemDissentVO();
                                                                                confirmationViewItemDissentVO.setId(vv.getId());
                                                                                confirmationViewItemDissentVO.setCreatedAt(
                                                                                        vv.getCreatedAt());
                                                                                confirmationViewItemDissentVO.setDissentReason(
                                                                                        vv.getDissentReason());
                                                                                return confirmationViewItemDissentVO;
                                                                            })
                                                                    .collect(Collectors.toList())));
                        });
        final ConfirmationViewVO confirmationViewVO = new ConfirmationViewVO();
        confirmationViewVO.setItemList(confirmationViewItemVOList);
        return SingleResponse.of(confirmationViewVO);
    }

    private List<EntryActivityPricePageVO> listByPriceItemId(Long priceItemId) {
        return listByPriceItemIds(Collections.singletonList(priceItemId));
    }

    private List<EntryActivityPricePageVO> listByPriceItemIds(List<Long> priceItemIds) {
        final EntryActivityPricePageQry qry = new EntryActivityPricePageQry();
        qry.setPriceItemIdList(priceItemIds);
        qry.setPageSize(9999);
        final PageResponse<EntryActivityPricePageVO> page = page(qry);
        return page.getData();
    }

    @Override
    public Response confirm(ConfirmReq req) {
        final List<Long> idList = req.getIdList();
        final List<EntryActivityPriceItem> entryActivityPriceItems =
                entryActivityPriceItemService.listByIds(idList);
        for (EntryActivityPriceItem entryActivityPriceItem : entryActivityPriceItems) {
            if (entryActivityPriceItem.getStatus() == 1) {
                throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前商品合作价格已确认");
            }
            entryActivityPriceItem.setStatus(1);
        }
        entryActivityPriceItemService.updateBatchById(entryActivityPriceItems);
        return Response.buildSuccess();
    }

    @Override
    public Response dissent(DissentReq req) {
        final Long priceItemId = req.getPriceItemId();
        final EntryActivityPriceItem priceItem = entryActivityPriceItemService.getById(priceItemId);
        Objects.requireNonNull(priceItem, "入驻活动价格商品ID无效");
        priceItem.setStatus(2);
        entryActivityPriceItemService.updateById(priceItem);

        final EntryActivityPriceDissent entryActivityPriceDissent = new EntryActivityPriceDissent();
        entryActivityPriceDissent.setPriceItemId(priceItemId);
        entryActivityPriceDissent.setDissentReason(req.getDissentNote());
        entryActivityPriceDissentService.save(entryActivityPriceDissent);
        return Response.buildSuccess();
    }

    @Override
    public Response updateDissent(DissentReq req) {
        final Long dissentId = Objects.requireNonNull(req.getId());
        final EntryActivityPriceDissent dissent = entryActivityPriceDissentService.getById(dissentId);
        Objects.requireNonNull(dissent);
        dissent.setDissentReason(req.getDissentNote());
        entryActivityPriceDissentService.updateById(dissent);
        return Response.buildSuccess();
    }

    @Override
    public SingleResponse<ViewVoucherVO> viewVoucher(ViewVoucherReq req) {
        final Long id = req.getPriceItemId();
        final EntryActivityPriceItem priceItem = entryActivityPriceItemService.getById(id);
        Objects.requireNonNull(priceItem);

        final Long confirmId = priceItem.getConfirmId();
        if (confirmId == null || confirmId == 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "未发起确认");
        }
        final EntryActivityPriceConfirm confirm = entryActivityPriceConfirmService.getById(confirmId);
        Objects.requireNonNull(confirm);

        final ViewVoucherVO viewVoucherVO = new ViewVoucherVO();
        final String shortLink = confirm.getShortLink();
        viewVoucherVO.setConfirmLink(shortLink);
        final List<EntryActivityPricePageVO> entryActivityPricePageVOS = listByPriceItemId(id);
        final List<ConfirmationViewItemVO> confirmationViewItemVOList =
                EntryActivityPriceConvert.INSTANCE.toConfirmationViewItemVOList(entryActivityPricePageVOS);
        final List<Long> priceItemIds =
                confirmationViewItemVOList.stream()
                        .map(ConfirmationViewItemVO::getId)
                        .collect(Collectors.toList());
        final List<EntryActivityPriceDissent> entryActivityPriceDissents =
                entryActivityPriceDissentService
                        .lambdaQuery()
                        .in(EntryActivityPriceDissent::getPriceItemId, priceItemIds)
                        .list();
        confirmationViewItemVOList.forEach(
                vo -> {
                    final List<EntryActivityPriceDissent> activityPriceDissents =
                            entryActivityPriceDissents.stream()
                                    .filter(v -> v.getPriceItemId() == vo.getId())
                                    .collect(Collectors.toList());
                    vo.setDissentList(
                            activityPriceDissents.stream()
                                    .map(
                                            vv -> {
                                                final ConfirmationViewItemDissentVO confirmationViewItemDissentVO =
                                                        new ConfirmationViewItemDissentVO();
                                                confirmationViewItemDissentVO.setCreatedAt(vv.getCreatedAt());
                                                confirmationViewItemDissentVO.setDissentReason(vv.getDissentReason());
                                                return confirmationViewItemDissentVO;
                                            })
                                    .collect(Collectors.toList()));
                });
        viewVoucherVO.setItemList(confirmationViewItemVOList);

        return SingleResponse.of(viewVoucherVO);
    }

    @Resource
    ProviderGateway providerGateway;

    @Override
    public Response sendCode(SendCodeReq req) {
        final EntryActivityPriceConfirm confirm =
                entryActivityPriceConfirmService.getById(req.getConfirmId());
        Objects.requireNonNull(confirm, "确认链接无效");

        final Long providerId = confirm.getProviderId();
        final Provider provider = providerGateway.getById(providerId);

        final ArrayList<String> mobiles = new ArrayList<>();
        if (StringUtil.isNotBlank(provider.getContactMobile())) {
            mobiles.add(provider.getContactMobile());
        }
        if (provider.getPartnerProviderId() != null && provider.getPartnerProviderId() > 0) {
            final List<PartnerContacts> partnerContactList =
                    providerGateway.getPartnerContactList(provider.getPartnerProviderId());
            if (CollectionUtil.isNotEmpty(partnerContactList)) {
                partnerContactList.stream()
                        .map(PartnerContacts::getMobile)
                        .filter(StringUtil::isNotBlank)
                        .forEach(mobiles::add);
            }
        }
        log.info("[入驻活动价] 手机号校验,有效手机号:{}", mobiles);
        if (!mobiles.contains(req.getMobile())) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "手机号不匹配");
        }

        final String smsCode =
                smsAuthService.getSmsCode(req.getMobile(), "entryActivityPrice:" + confirm.getId());
        log.info("smsCode:{}", smsCode);
        return Response.buildSuccess();
    }

    @Override
    public SingleResponse<String> token(TokenReq req) {
        final EntryActivityPriceConfirm confirm =
                entryActivityPriceConfirmService.getById(req.getConfirmId());
        Objects.requireNonNull(confirm);

        final Long providerId = confirm.getProviderId();
        final boolean verify =
                smsAuthService.verifyCode(
                        req.getMobile(), req.getCode(), "entryActivityPrice:" + confirm.getId());
        if (!verify) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "验证码错误");
        }
        final HashMap<String, Object> map = new HashMap<>();
        map.put("confirmId", req.getConfirmId());
        map.put("providerId", providerId);
        map.put(JWTPayload.ISSUER, "erp");
        map.put(JWTPayload.ISSUED_AT, DateUtil.currentTime());
        map.put(JWTPayload.EXPIRES_AT, DateUtil.currentTime() + 7200);
        final String token =
                JWTUtil.createToken(
                        map, entryActivityPriceConfig.getTokenKey().getBytes(StandardCharsets.UTF_8));
        return SingleResponse.of(token);
    }

    @Override
    public Response checkToken(String token) {
        final JWT jwt = JWTUtil.parseToken(token);
        jwt.setKey(entryActivityPriceConfig.getTokenKey().getBytes(StandardCharsets.UTF_8));
        if (jwt.validate(0)) {
            return Response.buildSuccess();
        }
        return Response.buildFailure(ErrorCode.OPERATION_REJECT.getCode(), "token无效");
    }
}
