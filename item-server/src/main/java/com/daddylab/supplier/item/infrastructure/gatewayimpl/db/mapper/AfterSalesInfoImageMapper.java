package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesInfoImage;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;

/**
 * <p>
 * 售后异常件信息图片表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-17
 */
public interface AfterSalesInfoImageMapper extends DaddyBaseMapper<AfterSalesInfoImage> {

}
