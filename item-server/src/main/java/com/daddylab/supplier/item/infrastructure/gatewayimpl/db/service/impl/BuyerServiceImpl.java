package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Buyer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.BuyerMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBuyerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-06
 */
@Service
public class BuyerServiceImpl extends DaddyServiceImpl<BuyerMapper, Buyer> implements IBuyerService {

    @Autowired
    BuyerMapper buyerMapper;

    @Override
    public String getKingDeeIdByItemId(Long itemId) {
        return buyerMapper.getKingDeeIdByItemId(itemId);
    }
}
