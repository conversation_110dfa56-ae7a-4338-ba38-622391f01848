package com.daddylab.supplier.item.infrastructure.qimen.wdt;

import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.sales.TradeQueryAPI;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryGetTradeMergedLogParams;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryGetTradeMergedLogResponse;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryHistoryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryHistoryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.daddylab.supplier.item.infrastructure.qimen.QimenConfig;
import com.daddylab.supplier.item.infrastructure.qimen.QimenWdtSalesTradeQueryAPI;
import com.qimencloud.api.QimenCloudClient;
import com.qimencloud.api.scene3ldsmu02o9.request.WdtSalesTradequeryQuerywithdetailRequest;
import com.qimencloud.api.scene3ldsmu02o9.response.WdtSalesTradequeryQuerywithdetailResponse;
import com.taobao.api.ApiException;

/**
 * <AUTHOR>
 * @since 2022/1/17
 */
public class TradeQueryAPIQimenImpl extends WdtAPIQimenImplBase implements TradeQueryAPI {

    QimenWdtSalesTradeQueryAPI qimenAPI;

    public TradeQueryAPIQimenImpl(QimenCloudClient qimenClient,
            QimenConfig qimenConfig,
            WdtConfig wdtConfig,
            int configIndex
    ) {
        super(qimenClient, qimenConfig, wdtConfig, configIndex);
    }

    @Override
    protected void initApiInstance(QimenCloudClient qimenClient, QimenConfig qimenConfig,
            WdtConfig wdtConfig, int configIndex) {
        qimenAPI = new QimenWdtSalesTradeQueryAPI(qimenClient, qimenConfig, wdtConfig, configIndex);
    }

    @Override
    public TradeQueryQueryWithDetailResponse queryWithDetail(TradeQueryQueryWithDetailParams params,
            Pager pager) throws WdtErpException {
        try {
            final WdtSalesTradequeryQuerywithdetailRequest.Params qimenParams = new WdtSalesTradequeryQuerywithdetailRequest.Params();
            qimenParams.setEndTime(params.getEndTime());
            qimenParams.setLogisticsNo(params.getLogisticsNo());
            qimenParams.setShopNo(params.getShopNo());
            qimenParams.setSrcTid(params.getSrcTid());
            qimenParams.setStartTime(params.getStartTime());
            qimenParams.setStatus(params.getStatus());
            qimenParams.setTradeNo(params.getTradeNo());
            qimenParams.setWarehouseNo(params.getWarehouseNo());

            final WdtSalesTradequeryQuerywithdetailResponse wdtSalesTradequeryQuerywithdetailResponse = qimenAPI
                    .salesTradeQueryWithDetail(qimenParams, pager.getPageNo() + 1,
                            pager.getPageSize());
            return checkAndReturnData(wdtSalesTradequeryQuerywithdetailResponse,
                    TradeQueryQueryWithDetailResponse.class);
        } catch (ApiException exception) {
            throw transformException(exception);
        }
    }

    @Override
    public TradeQueryQueryHistoryWithDetailResponse queryHistoryWithDetail(
            TradeQueryQueryHistoryWithDetailParams params, Pager pager) throws WdtErpException {
        throw new UnsupportedOperationException("暂未实现");
    }

    @Override
    public TradeQueryGetTradeMergedLogResponse getTradeMergedLog(
            TradeQueryGetTradeMergedLogParams params) throws WdtErpException {
        throw new UnsupportedOperationException("暂未实现");
    }
}
