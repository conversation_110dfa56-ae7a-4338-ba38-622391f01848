package com.daddylab.supplier.item.infrastructure.config;

import com.daddylab.supplier.item.domain.common.enums.Platform;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Author: <PERSON> @Date: 2022/9/1 11:07 @Description: 商品外链配置
 */
@Component
@Data
@ConfigurationProperties(prefix = "item.outlink")
public class ItemOutlinkProperties {
    private String baseTaobaoLink = "https://item.taobao.com/item.htm?id=%s";
    private String baseDouyinLink = "https://haohuo.jinritemai.com/views/product/detail?id=%s";
    private String baseMiniProgramLink =
            "http://cloud1-6g2z6yh8b087381b-1305265685.tcloudbaseapp.com?t=pd&p=%s";
    private String baseKuaishouLink =
            "https://s.kwaixiaodian.com/zone/goods/config/release/detail?itemId=%s";

    public String getLink(Platform platform, String outerItemId) {
        switch (platform) {
            case TAOBAO:
                return String.format(baseTaobaoLink, outerItemId);
            case DOUDIAN:
                return String.format(baseDouyinLink, outerItemId);
            case KUAISHOU:
                return String.format(baseKuaishouLink, outerItemId);
            case LAOBASHOP:
                return String.format(baseMiniProgramLink, outerItemId);
            default:
                return "";
        }
    }
}
