package com.daddylab.supplier.item.domain.dataFetch;

import cn.hutool.core.lang.Dict;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import java.io.Serializable;
import java.util.function.Consumer;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/4/27
 */
@JsonInclude(Include.NON_NULL)
@Data
public class RunData implements Serializable {

    private static final long serialVersionUID = 1L;
    @JsonIgnore
    private transient Consumer<RunData> persistCallback;

    private Integer failCount;
    private Integer total;
    private Integer maxPage;
    private Integer pageIndex;
    private Integer pageSize;
    private Boolean reverseFetch;
    private Boolean fetched;
    private Integer fetchCount;
    private Dict extras;

    public RunData() {
    }

    public Dict getExtras() {
        if (extras == null) {
            extras = new Dict();
        }
        return extras;
    }

    public Boolean getFetched() {
        return fetched != null && fetched;
    }

    public void addPageIndex() {
        pageIndex++;
    }

    public void subtractPageIndex() {
        pageIndex--;
    }

    public void addFailCount() {
        if (failCount == null) {
            failCount = 0;
        }
        failCount++;
    }

    public void addFetchCount() {
        if (fetchCount == null) {
            fetchCount = 0;
        }
        fetchCount++;
    }

    public void setPersistCallback(Consumer<RunData> callback) {
        persistCallback = callback;
    }

    public void persist() {
        if (persistCallback == null) {
            throw new UnsupportedOperationException("尚未设置持久化回调");
        }
        persistCallback.accept(this);
    }
}
