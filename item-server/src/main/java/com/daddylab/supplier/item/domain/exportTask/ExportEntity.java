package com.daddylab.supplier.item.domain.exportTask;

import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.io.File;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;

/**
 * T baseMapper 返回数据格式。R 实际导出数据格式
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/19 2:14 下午
 * @description
 */
@Slf4j
@Deprecated
public class ExportEntity<T, R> {
    //    private final Integer exportTotal;
//    private final ExportTask exportTask;
    private final ExcelWriter excelWriter;
    private WriteSheet writeSheet;
    private final static Long SHEET_SIZE = 65535L;
    private Integer sheetNo = 0;
    private Long sheetCount = 0L;
    private Long writeCount = 0L;
    private final Integer pageQueryWaitingTime = 5;
    private final File excelFile;
    private List<Callable<List<T>>> queryCallableList;


    @SneakyThrows
    public ExportEntity(ExportTask exportTask, Integer exportTotal, Class<? extends ExportSheet> exportDataClass) {
//        this.exportTotal = exportTotal;
//        this.exportTask = exportTask;
        this.excelFile = new File(exportTask.getName() + ".xlsx ");
        this.excelWriter = EasyExcel.write(excelFile, exportDataClass).build();
        this.writeSheet = EasyExcel.writerSheet(sheetNo).build();

        // mock
        exportTask.setProgress(BigDecimal.ZERO);
    }

    public void setQueryCallableList(List<Callable<List<T>>> queryCallableList) {
        this.queryCallableList = queryCallableList;
    }

    @Deprecated
    public void export() {
        ThreadPoolTaskExecutor exportExecutor = SpringUtil.getBean("exportExecutor");
        try {
            List<Future<List<T>>> futures = exportExecutor.getThreadPoolExecutor().invokeAll(queryCallableList);
            for (Future<List<T>> future : futures) {
                List<T> ts = future.get();
                this.writeSheet = EasyExcel.writerSheet(sheetNo).build();

                excelWriter.write(ts, writeSheet);
                sheetNo = sheetNo + 1;
            }
        } catch (Exception e) {
            log.error("Export failed.", e);
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "导出失败，" + e.getMessage());
        } finally {
            excelWriter.finish();
        }
    }


    @Deprecated
    public void export(Function<T, R> resultFunction) {
        ThreadPoolTaskExecutor exportExecutor = SpringUtil.getBean("exportExecutor");
        try {
            List<Future<List<T>>> futures = exportExecutor.getThreadPoolExecutor().invokeAll(queryCallableList);
            for (Future<List<T>> future : futures) {
                List<T> ts = future.get();
                log.info("------- count:{} -----", ts.size());
                this.writeSheet = EasyExcel.writerSheet(sheetNo).build();

                List<R> rList = new LinkedList<>();
                for (T t : ts) {
                    rList.add(resultFunction.apply(t));
                }
                excelWriter.write(rList, writeSheet);
                sheetNo = sheetNo + 1;
            }
        } catch (Exception e) {
            log.error("Export failed.", e);
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "导出失败");
        } finally {
            excelWriter.finish();
        }
    }

    public void export2(Function<Collection<T>, Collection<R>> resultFunction) {
        ThreadPoolTaskExecutor exportExecutor = SpringUtil.getBean("exportExecutor");
        try {
            List<Future<List<T>>> futures = exportExecutor.getThreadPoolExecutor().invokeAll(queryCallableList);
            for (Future<List<T>> future : futures) {
                List<T> ts = future.get();
                log.info("------- count:{} -----", ts.size());
                this.writeSheet = EasyExcel.writerSheet(sheetNo).build();
                final Collection<R> rList = resultFunction.apply(ts);
                excelWriter.write(rList, writeSheet);
                sheetNo = sheetNo + 1;
            }
        } catch (Exception e) {
            log.error("Export failed.", e);
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "导出失败");
        } finally {
            excelWriter.finish();
        }
    }


    public File getExcelFile() {
        return excelFile;
    }


    @Deprecated
    public static ExportTask buildEntity(ExportTaskType type) {
        ExportTask exportTask = new ExportTask();
        exportTask.setStatus(ExportTaskStatus.RUNNING);
        exportTask.setName(type.getDesc() + "-" + DateUtil.date().toString("yyyyMMddHHmmss"));
        exportTask.setType(type);
        return exportTask;
    }


}
