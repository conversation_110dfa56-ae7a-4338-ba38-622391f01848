package com.daddylab.supplier.item.application.offShelf;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.offShelf.dto.*;

import java.util.Map;

public interface OffShelfBizService {
    PageResponse<OffShelfPageVO> page(OffShelfPageQuery query);

    SingleResponse<Long> export(OffShelfPageQuery query);

    SingleResponse<OffShelfFormVO> view(Long id);

    SingleResponse<Long> save(OffShelfFormCmd cmd);

    SingleResponse<Boolean> submit(Long id);

    SingleResponse<Boolean> delete(OffShelfDeleteCmd cmd);

    SingleResponse<Boolean> revoke(OffShelfRevokeCmd cmd);

    SingleResponse<Boolean> approve(OffShelfApproveCmd cmd);

    SingleResponse<Map<Long, String>> batchApprove(OffShelfBatchApproveCmd cmd);

    SingleResponse<Boolean> processFeedback(OffShelfProcessFeedbackCmd cmd);

    MultiResponse<OffShelfBatchProcessFeedbackItem> batchProcessGetItems(OffShelfBatchProcessGetItemsCmd cmd);

    SingleResponse<Boolean> batchProcessFeedback(OffShelfBatchProcessFeedbackCmd cmd);

    Response noticeToApprove(Long id);

    Response noticeRevoked(Long id);

    Response noticeAuditRevoked(Long id);

    Response noticeToProcess(Long id);

    Response noticeRefused(Long id);

    Response noticeFinished(Long id);

    Response urge(OffShelfUrgeCmd cmd);

    Response executeOffShelf(Long id);


}
