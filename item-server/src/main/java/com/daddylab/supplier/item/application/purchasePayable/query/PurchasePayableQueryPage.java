package com.daddylab.supplier.item.application.purchasePayable.query;

import com.alibaba.cola.dto.PageQuery;
import com.daddylab.supplier.item.application.purchasePayable.enums.PurchasePayableTimeTypeEnum;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("采购应付单分页查询请求")
public class PurchasePayableQueryPage extends PageQuery {

    private static final long serialVersionUID = -3764871918495714718L;
    
    @ApiModelProperty(value = "应付单号")
    private String no;

    /**
     * {@link PurchaseTypeEnum}
     */
    @ApiModelProperty(value = "应付类型。1采购入库应付、2采退出库应付、3其他应付")
    private Integer type;

    @ApiModelProperty(value = "供应商id")
    private Long providerId;

    @ApiModelProperty(value = "采购组织id")
    private Long organizationId;

    @ApiModelProperty(value = "关联单据(指采购入库单号/采退出库单号)")
    private List<String> relatedOrderNoList;

    @ApiModelProperty(value = "查询开始时间，秒级时间戳，时间的零点")
    private Long queryStartDate;

    @ApiModelProperty(value = "查询结束时间，秒级时间戳，时间的最后一秒")
    private Long queryEndDate;

    /**
     * {@link PurchasePayableTimeTypeEnum}
     */
    @ApiModelProperty(value = "0:本期应付， 不传查询全部")
    private Integer purchasePayableTimeType;

    @ApiModelProperty(value = "采购单ID")
    private Long purchaseId;

    /**
     * 关联的入库单/出库单/其他应付的采购员
     */
    private Long buyerId;

    private List<Integer> businessLine;

//    private Boolean seeHedge;
}
