package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderService;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;

/**
 * 旺店通订单数据 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Service
public class WdtOrderServiceImpl extends DaddyServiceImpl<WdtOrderMapper, WdtOrder>
        implements IWdtOrderService {
    @Override
    public Optional<WdtOrder> getByTradeId(Long wdtTradeId) {
        return lambdaQuery().eq(WdtOrder::getTradeId, wdtTradeId).oneOpt();
    }

    @Override
    public List<WdtOrder> listBySrcTid(String srcTid) {
        return getDaddyBaseMapper().listBySrcTid(srcTid);
    }
}
