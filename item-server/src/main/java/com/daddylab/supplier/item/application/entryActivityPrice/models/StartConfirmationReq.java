package com.daddylab.supplier.item.application.entryActivityPrice.models;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/4
 */
@Data
public class StartConfirmationReq {

    @ApiModelProperty("确认发起的商品ID列表")
    @NotEmpty
    private List<Long> idList;

    @ApiModelProperty("是否包含来自同一个供应商的其他待确认商品")
    private boolean includeOtherSameProvider;
}
