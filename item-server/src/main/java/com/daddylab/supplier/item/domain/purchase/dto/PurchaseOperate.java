package com.daddylab.supplier.item.domain.purchase.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.javers.core.metamodel.annotation.Entity;

/**
 * <AUTHOR>
 * @ClassName PurchaseOperate.java
 * @description 采购操作实体
 * @createTime 2021年11月16日 15:00:00
 */
@Entity
@Data
public class PurchaseOperate {

    @ApiModelProperty(value = "导入总数量")
    private Integer count;

}
