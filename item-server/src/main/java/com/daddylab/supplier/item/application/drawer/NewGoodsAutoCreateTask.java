package com.daddylab.supplier.item.application.drawer;

import com.alibaba.cola.exception.BizException;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/13
 */
@Service
@Slf4j
@RefreshScope
@ConfigurationProperties(prefix = "new-goods-auto-create")
public class NewGoodsAutoCreateTask {

    /**
     * 小程序商品SKU	当前上架状态	历史是否上过架	是否组合码
     */
    @Data
    public static class ExcelRow {
        @ExcelProperty(value = "小程序商品SKU")
        private String skuCode;
        @ExcelProperty(value = "当前上架状态")
        private String listingStatus;
        @ExcelProperty(value = "历史是否上过架")
        private String hasHistoryEverBeenListed;
        @ExcelProperty(value = "是否组合码")
        private String isCombinationCode;
        @ExcelProperty(value = "结果")
        private String result;
    }

    @Autowired
    private INewGoodsService newGoodsService;
    @Autowired
    private IItemService itemService;
    @Autowired
    private IItemSkuService itemSkuService;
    @Autowired
    private FileGateway fileGateway;
    @Autowired
    private IItemLaunchPlanItemRefService itemLaunchPlanItemRefService;
    @Autowired
    private IItemDrawerService itemDrawerService;

    private Long planId = 398L;

    public void importExcel(InputStream inputStream) {
        try {
            final List<ExcelRow> objects = EasyExcel.read(inputStream).head(ExcelRow.class).doReadAllSync();
            final String outputPath = String.format("/tmp/新品商品自动创建-%s", DateUtil.formatNow());
            final ExcelWriter outputExcelWriter = EasyExcel.write(outputPath)
                                                           .head(ExcelRow.class)
                                                           .build();
            final WriteSheet outputWriteSheet = EasyExcel.writerSheet(0).build();

            final List<ItemLaunchPlanItemRef> itemLaunchPlanItemRefs = itemLaunchPlanItemRefService.lambdaQuery()
                                                                                                   .eq(ItemLaunchPlanItemRef::getPlanId,
                                                                                                           planId)
                                                                                                   .select(ItemLaunchPlanItemRef::getItemId)
                                                                                                   .list();
            final Set<Long> refItemIds = itemLaunchPlanItemRefs.stream()
                                                               .map(ItemLaunchPlanItemRef::getItemId)
                                                               .collect(Collectors.toSet());
            final HashSet<Long> itemIdsOfHasDrawer = new HashSet<>();
            int continuousEmpty = 0;
            for (ExcelRow object : objects) {
                if (StringUtil.isBlank(object.getSkuCode())) {
                    continuousEmpty++;
                    outputExcelWriter.write(Collections.singletonList(object), outputWriteSheet);
                    if (continuousEmpty >= 3) {
                        break;
                    }
                    continue;
                } else {
                    continuousEmpty = 0;
                }
                try {
                    if (!"否".equals(object.getIsCombinationCode())) {
                        object.setResult("无需处理");
                    } else {
                        final String skuCode = object.getSkuCode().trim();
                        final List<NewGoods> existsNewGoodsList = newGoodsService.selectBatchBySkuCodes(Collections.singletonList(
                                skuCode));
                        if (!existsNewGoodsList.isEmpty()) {
                            object.setResult("新品商品已存在");
                        } else {
                            final ItemSku itemSku = itemSkuService.getByMixCode(skuCode)
                                                                  .orElseThrow(() -> ExceptionPlusFactory.bizException(
                                                                          ErrorCode.DATA_NOT_FOUND,
                                                                          "商品规格编码未在后端商品找到"));

                            final Item item = itemService.getById(itemSku.getItemId());
                            final NewGoods newGoods = new NewGoods();
                            newGoods.setItemId(itemSku.getItemId());
                            newGoods.setSkuCode(itemSku.getSkuCode());
                            newGoods.setName(item.getName());
                            newGoodsService.save(newGoods);

                            if (!itemIdsOfHasDrawer.contains(item.getId())) {
                                if (!itemDrawerService.getByItemId(item.getId()).isPresent()) {
                                    final ItemDrawer itemDrawer = new ItemDrawer();
                                    itemDrawer.setItemId(item.getId());
                                    itemDrawerService.save(itemDrawer);
                                }
                                itemIdsOfHasDrawer.add(item.getId());
                            }

                            if (!refItemIds.contains(item.getId())) {
                                final ItemLaunchPlanItemRef itemLaunchPlanItemRef = new ItemLaunchPlanItemRef();
                                itemLaunchPlanItemRef.setPlanId(planId);
                                itemLaunchPlanItemRef.setItemId(item.getId());
                                itemLaunchPlanItemRef.setItemCode(item.getCode());
                                itemLaunchPlanItemRef.setSortNo(refItemIds.size() + 1);
                                itemLaunchPlanItemRef.setItemType(1);

                                itemLaunchPlanItemRefService.save(itemLaunchPlanItemRef);
                                refItemIds.add(item.getId());
                            }
                            object.setResult("创建成功");
                        }
                    }
                    outputExcelWriter.write(Collections.singletonList(object), outputWriteSheet);
                } catch (BizException e) {
                    object.setResult(e.getMessage());
                    outputExcelWriter.write(Collections.singletonList(object), outputWriteSheet);
                }
            }
            outputExcelWriter.finish();
            final FileStub fileStub = fileGateway.uploadFile(UploadFileAction.ofFile(new File(outputPath)));
            log.info("【新品商品自动创建】处理完成，结果已写入：{}", fileGateway.getAuthorizedUrl(fileStub.getPath()));
        } catch (Exception e) {
            log.error("【新品商品自动创建】处理异常，{}", e.getMessage(), e);
        }
    }
}
