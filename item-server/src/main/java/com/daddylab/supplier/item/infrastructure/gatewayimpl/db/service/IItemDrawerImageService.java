package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerImage;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

import java.util.List;

/**
 * <p>
 * 抽屉图片表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
public interface IItemDrawerImageService extends IDaddyService<ItemDrawerImage> {
    /**
     * 由抽屉 ID 查询集合
     *
     * @param drawerId 抽屉 ID
     * @return List<ItemDrawerImage>
     */
    List<ItemDrawerImage> selectSortedListByDrawerId(Long drawerId);

    List<ItemDrawerImage> queryDrawerImages(Long itemDrawerId, Boolean isLiveVerbal);
}
