package com.daddylab.supplier.item.controller.stockout.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/4/8 11:28
 * @description StockOutOrderRevocationDTO
 */
@Data
@ApiModel(value = "id对象")
@EqualsAndHashCode(callSuper = true)
public class StockOutOrderIdDTO extends Command {

    /**
     * 出库id
     */
    @NotNull(message = "出库id不能为空")
    @ApiModelProperty(value = "出库id")
    private Long stockOutOrderId;


}
