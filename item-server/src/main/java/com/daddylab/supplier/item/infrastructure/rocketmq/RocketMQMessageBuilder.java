package com.daddylab.supplier.item.infrastructure.rocketmq;

import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

/**
 * <AUTHOR>
 * @since 2022/2/17
 */
public class RocketMQMessageBuilder<T> {
    private final MessageBuilder<T> builder;

    private RocketMQMessageBuilder(MessageBuilder<T> builder) {
        this.builder = builder;
    }

    public static <T> RocketMQMessageBuilder<T> withBuilder(MessageBuilder<T> builder) {
        return new RocketMQMessageBuilder<>(builder);
    }

    public static <T> RocketMQMessageBuilder<T> withPayload(T payload) {
        return new RocketMQMessageBuilder<>(MessageBuilder.withPayload(payload));
    }

    public RocketMQMessageBuilder<T> setKeys(String keys) {
        this.builder.setHeader(RocketMQHeaders.KEYS, keys);
        return this;
    }

    public MessageBuilder<T> unwrap() {
        return this.builder;
    }

    public Message<T> build() {
        return this.builder.build();
    }
}
