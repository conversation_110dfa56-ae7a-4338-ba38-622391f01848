package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 2月份客服登记差异表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TmpAfterSalesSupplement implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 结算周期
     */
    private String settlementPeriod;

    /**
     * 合作模式
     */
    private String cooperationMode;

    /**
     * 付款时间
     */
    private String paymentTime;

    /**
     * 店铺
     */
    private String store;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品规格
     */
    private String productSpecification;

    /**
     * 售后数量
     */
    private String afterSalesQuantity;

    /**
     * 发货仓
     */
    private String deliveryWarehouse;

    /**
     * 工厂/仓库承担运费
     */
    private String factoryWarehouseBearsFreight;

    /**
     * 工厂/仓库承担货款
     */
    private String factoryWarehouseBearsPayment;

    /**
     * 工厂/仓库承担其他补偿
     */
    private String factoryWarehouseBearsOtherCompensation;

    /**
     * 体验基金（优惠券）
     */
    private String experienceFundCoupon;

    /**
     * 体验基金（现金）
     */
    private String experienceFundCash;

    /**
     * 售后处理意见
     */
    private String afterSalesHandlingOpinion;

    /**
     * 体验金承担原因(选项1级标题)
     */
    private String experienceFundBearingReasonOption1;

    /**
     * 体验金承担原因(选项2级标题)
     */
    private String experienceFundBearingReasonOption2;

    /**
     * 体验金承担原因(选项3级标题)
     */
    private String experienceFundBearingReasonOption3;

    /**
     * 问题描述(选项1级标题)
     */
    private String problemDescriptionOption1;

    /**
     * 问题描述(选项2级标题)
     */
    private String problemDescriptionOption2;

    /**
     * 问题描述(选项3级标题)
     */
    private String problemDescriptionOption3;

    /**
     * 相关图片
     */
    private String relatedImages;


}
