package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.payment.PaymentOrderBizServiceImpl;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentApplyOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PaymentOrderStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PaymentApplyOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPaymentApplyOrderDetailService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPaymentApplyOrderService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 采购管理-付款申请单-付款明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@Service
public class PaymentApplyOrderDetailServiceImpl extends DaddyServiceImpl<PaymentApplyOrderDetailMapper, PaymentApplyOrderDetail> implements IPaymentApplyOrderDetailService {


    @Resource
    IPaymentApplyOrderService iPaymentApplyOrderService;

    @Resource
    PaymentApplyOrderDetailMapper paymentApplyOrderDetailMapper;

    @Override
    public List<Long> getAppliedDetailIdList(Collection<String> applyRelatedNos) {
        List<Integer> statusList = PaymentOrderBizServiceImpl.LOCKED_STATUS_LIST.stream().map(PaymentOrderStatus::getValue).collect(Collectors.toList());
        List<String> relatedDetailDbIdByRelatedNo = paymentApplyOrderDetailMapper.getRelatedDetailDbIdByRelatedNo(applyRelatedNos, statusList);
        List<Long> empty = new LinkedList<>();
        return relatedDetailDbIdByRelatedNo.stream().map(val -> {
            if (StrUtil.isNotBlank(val)) {
                return Arrays.stream(val.split(StrUtil.COMMA)).map(Long::valueOf).collect(Collectors.toList());
            }
            return empty;
        }).flatMap(List::stream).collect(Collectors.toList());
    }

    @Override
    public List<PaymentApplyOrderDetail> getByPaymentApplyOrderId(Long paymentOrderId) {
        return this.lambdaQuery().eq(PaymentApplyOrderDetail::getPaymentApplyOrderId,paymentOrderId).list();
    }
}
