package com.daddylab.supplier.item.application.otherStockOut;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.application.otherStockIn.OtherStockInState;
import com.daddylab.supplier.item.application.otherStockOutDetail.OtherStockOutDetailBizService;
import com.daddylab.supplier.item.application.reason.ReasonBizService;
import com.daddylab.supplier.item.application.warehouse.WarehouseGateway;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.OtherStockOutDetailTransMapper;
import com.daddylab.supplier.item.common.trans.OtherStockOutTransMapper;
import com.daddylab.supplier.item.controller.otherStockOut.dto.OtherStockOutQueryPage;
import com.daddylab.supplier.item.controller.otherStockOut.dto.OtherStockOutVo;
import com.daddylab.supplier.item.controller.otherStockOutDetail.dto.OtherStockOutDetailVo;
import com.daddylab.supplier.item.domain.brand.entity.BrandEntity;
import com.daddylab.supplier.item.domain.brand.gateway.BrandGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOtherStockOutService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @ClassName OtherStockOutBizServiceImpl.java
 * @description
 * @createTime 2022年04月01日 16:24:00
 */
@Slf4j
@Service
public class OtherStockOutBizServiceImpl implements OtherStockOutBizService {

    @Autowired
    private ItemSkuGateway itemSkuGateway;
    @Autowired
    private OtherStockOutDetailBizService otherStockOutDetailBizService;
    @Autowired
    private ItemGateway itemGateway;
    @Autowired
    private IOtherStockOutService iOtherStockOutService;
    @Autowired
    private BrandGateway brandGateway;
    @Autowired
    private WarehouseGateway warehouseGateway;
    @Autowired
    private ReasonBizService reasonBizService;

    @Override
    public PageResponse<OtherStockOutVo> queryPage(OtherStockOutQueryPage queryPage) {
        queryPage.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(queryPage.getBusinessLine()));
        if (CollUtil.isEmpty(queryPage.getBusinessLine())) {
            return PageResponse.of(new ArrayList<>(), 0, queryPage.getPageSize(), queryPage.getPageIndex());
        }

        List<Long> orderIds = otherStockOutDetailBizService.getIdsByItem(queryPage.getItemSku(), queryPage.getItemCode(),
                queryPage.getItemId(), queryPage.getBrandId(), queryPage.getBusinessLine());
        if (CollUtil.isEmpty(orderIds)) {
            return PageResponse.of(queryPage.getPageSize(), queryPage.getPageIndex());
        }
        LambdaQueryWrapper<OtherStockOut> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(StringUtils.isNotBlank(queryPage.getOrderNo()), OtherStockOut::getOrderNo, queryPage.getOrderNo())
                .eq(StringUtils.isNotBlank(queryPage.getWarehouseNo()), OtherStockOut::getWarehouseNo, queryPage.getWarehouseNo())
                .in(CollectionUtils.isNotEmpty(orderIds), OtherStockOut::getId, orderIds)
                .ge(Objects.nonNull(queryPage.getStartTime()), OtherStockOut::getCreatedAt, queryPage.getStartTime())
                .le(Objects.nonNull(queryPage.getEndTime()), OtherStockOut::getCreatedAt, queryPage.getEndTime());
        if (Objects.nonNull(queryPage.getStatus()) && !Objects.equals(queryPage.getStatus(), OtherStockInState.ALL.getValue())) {
            queryWrapper.eq(OtherStockOut::getStatus, queryPage.getStatus());
        }
        queryWrapper.eq(Objects.nonNull(queryPage.getOtherReason()), OtherStockOut::getOtherReason, queryPage.getOtherReason());
        queryWrapper.orderByDesc(OtherStockOut::getCreatedAt);
        Page<OtherStockOut> pageReq = new Page<>(queryPage.getPageIndex(), queryPage.getPageSize());
        Page<OtherStockOut> page = iOtherStockOutService.page(pageReq, queryWrapper);

        List<OtherStockOutVo> otherStockOutVos = OtherStockOutTransMapper.INSTANCE.doToVos(page.getRecords());
        otherStockOutVos.forEach(otherStockOutVo -> {
            List<OtherStockOutDetail> byOrderId = otherStockOutDetailBizService.getByOrderId(otherStockOutVo.getId());
            Integer total = byOrderId.stream().mapToInt(OtherStockOutDetail::getCount).sum();
            otherStockOutVo.setItemCount(total);
            otherStockOutVo.setKindCount(byOrderId.size());
            //仓库名称
            Optional<Warehouse> warehouse = warehouseGateway.getWarehouse(otherStockOutVo.getWarehouseNo());
            warehouse.ifPresent(value -> otherStockOutVo.setWarehouseName(value.getName()));
            Reason reason = reasonBizService.getById(Long.valueOf(otherStockOutVo.getOtherReason()));
            if (Objects.nonNull(reason)) {
                otherStockOutVo.setOtherReasonName(reason.getReason());
            }
        });
        return PageResponse.of(otherStockOutVos, (int) page.getTotal(), queryPage.getPageSize(), queryPage.getPageIndex());
    }

    @Override
    public SingleResponse<OtherStockOutVo> getById(Long id) {
        OtherStockOut otherStockOut = iOtherStockOutService.getById(id);
        if (Objects.isNull(otherStockOut)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查不到该记录");
        }
        OtherStockOutVo otherStockOutVo = OtherStockOutTransMapper.INSTANCE.doToVo(otherStockOut);
        List<OtherStockOutDetail> detailList = otherStockOutDetailBizService.getByOrderId(id);
        Integer total = detailList.stream().mapToInt(OtherStockOutDetail::getCount).sum();
        otherStockOutVo.setItemCount(total);
        otherStockOutVo.setKindCount(detailList.size());
        Reason reason = reasonBizService.getById(Long.valueOf(otherStockOutVo.getOtherReason()));
        if (Objects.nonNull(reason)) {
            otherStockOutVo.setOtherReasonName(reason.getReason());
        }
        Optional<Warehouse> warehouse = warehouseGateway.getWarehouse(otherStockOutVo.getWarehouseNo());
        warehouse.ifPresent(value -> otherStockOutVo.setWarehouseName(value.getName()));
        List<OtherStockOutDetailVo> otherStockOutDetailVos = OtherStockOutDetailTransMapper.INSTANCE.otherDbToVos(detailList);

        otherStockOutDetailVos.forEach(otherStockOutDetailVo -> {
            Item item = itemGateway.getItem(otherStockOutDetailVo.getItemId());
            otherStockOutDetailVo.setItemName(item.getName());
            otherStockOutDetailVo.setItemCode(item.getCode());
            Optional<ItemSku> byItemSkuId = itemSkuGateway.getByItemSkuId(otherStockOutDetailVo.getSkuId());
            byItemSkuId.ifPresent(itemSku -> otherStockOutDetailVo.setSkuName(itemSku.getSkuCode()));
            //获取规格名称
            if (byItemSkuId.isPresent()) {
                String skuAttrListStr = itemSkuGateway.getSkuAttrListStr(byItemSkuId.get().getSkuCode());
                if (Objects.nonNull(skuAttrListStr)) {
                    otherStockOutDetailVo.setSkuAttrRefDOS(skuAttrListStr);
                }
            }
            BrandEntity brand = brandGateway.getBrand(otherStockOutDetailVo.getBrandId());
            if (Objects.nonNull(brand)) {
                otherStockOutDetailVo.setBrandName(brand.getName());
            }
        });

        otherStockOutVo.setStockOutDetailVos(otherStockOutDetailVos);
        return SingleResponse.of(otherStockOutVo);
    }

    private boolean check(Long sku, String code, Long itemId, Long brandId) {
        if (Objects.nonNull(sku) || StringUtils.isNotBlank(code) || Objects.nonNull(itemId) || Objects.nonNull(brandId)) {
            return true;
        } else {
            return false;
        }
    }
}
