package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockIn;

/**
 * <p>
 * 其他入库单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-30
 */
public interface IOtherStockInService extends IDaddyService<OtherStockIn> {

}
