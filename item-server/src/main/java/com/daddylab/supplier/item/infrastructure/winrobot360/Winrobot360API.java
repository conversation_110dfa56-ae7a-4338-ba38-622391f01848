package com.daddylab.supplier.item.infrastructure.winrobot360;

import com.daddylab.supplier.item.infrastructure.winrobot360.types.GenericRsp;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TaskQueryParam;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TaskQueryRspData;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TaskStartParam;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TaskStartRspData;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TaskStopReq;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TokenCreateRspData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2022/9/16
 */
@FeignClient(name = "Winrobot360API", url = "${winrobot360.url}")
public interface Winrobot360API {

    @PostMapping(path = "/oapi/token/v2/token/create", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    GenericRsp<TokenCreateRspData> tokenCreate(@RequestParam("accessKeyId") String accessKeyId,
            @RequestParam("accessKeySecret") String accessKeySecret);

    @PostMapping(path = "/oapi/dispatch/v2/task/start", produces = MediaType.APPLICATION_JSON_VALUE)
    GenericRsp<TaskStartRspData> taskStart(@RequestHeader("authorization") String token,
            @RequestBody TaskStartParam param);

    @PostMapping(path = "/oapi/dispatch/v2/task/query", produces = MediaType.APPLICATION_JSON_VALUE)
    GenericRsp<TaskQueryRspData> taskQuery(@RequestHeader("authorization") String token,
            @RequestBody TaskQueryParam param);

    @PostMapping(path = "/oapi/dispatch/v2/task/query", produces = MediaType.APPLICATION_JSON_VALUE)
    GenericRsp<Void> taskStop(@RequestHeader("authorization") String token,
            @RequestBody TaskStopReq param);

}
