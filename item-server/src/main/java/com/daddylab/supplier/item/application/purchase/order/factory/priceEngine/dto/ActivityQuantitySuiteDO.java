package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2022年11月16日 2:38 PM
 */
@Data
public class ActivityQuantitySuiteDO {

    /**
     * sku编码
     */
    private String suiteNo;

    /**
     * 优惠价格
     */
    private BigDecimal priceCost;

    /**
     * 优惠数量
     */
    private BigDecimal numCost;
    private Long startTime;
    private Long endTime;

    private Integer platformType;
}
