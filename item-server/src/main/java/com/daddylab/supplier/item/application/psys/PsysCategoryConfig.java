package com.daddylab.supplier.item.application.psys;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2023/12/22
 */
@Configuration
@ConfigurationProperties(prefix = "psys.category")
@RefreshScope
@Data
public class PsysCategoryConfig {
    /** 美妆、护肤、彩妆 几个类目的ID */
    private int[][] categoryIdsOfBeautyAndSkincare =
            new int[][] {
                new int[] {1, 22},
                new int[] {1, 23},
                new int[] {1, 24},
            };

    public boolean isBeautyAndSkincareCategory(int[] categoryIds) {
        return Arrays.stream(categoryIdsOfBeautyAndSkincare)
                .anyMatch(ids -> Arrays.equals(ids, categoryIds));
    }
}
