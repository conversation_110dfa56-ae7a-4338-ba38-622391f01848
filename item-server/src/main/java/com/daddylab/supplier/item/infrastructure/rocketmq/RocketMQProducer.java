package com.daddylab.supplier.item.infrastructure.rocketmq;

import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.core.task.TaskRejectedException;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * @Author: Wang Xiang
 * @Date: 2021/8/4 13:52
 * @Description: 发送 mq 消息服务类
 */
@Component
@Slf4j
public class RocketMQProducer implements Serializable {


    /**
     * 发送消息默认超时时间，单位：毫秒
     */
    private static final Long DEFAULT_SEND_MSG_TIMEOUT = 3000L;
    private static final long serialVersionUID = 1L;

    private final RocketMQTemplate rocketMQTemplate;
    private final StandardEnvironment environment;
    private ObjectProvider<MessageFallback> messageFallbackProvider;

    public RocketMQProducer(RocketMQTemplate rocketMQTemplate,
                            StandardEnvironment environment) {
        this.rocketMQTemplate = rocketMQTemplate;
        this.environment = environment;
    }

    @Autowired
    public void setMessageFallbackProvider(
            ObjectProvider<MessageFallback> messageFallbackProvider) {
        this.messageFallbackProvider = messageFallbackProvider;
    }

    /**
     * 同步发送消息，默认超时时间3秒
     *
     * @param payload 消息负载（业务对象）
     * @param topic   发送到哪个TOPIC
     * @param tag     附加tag
     * @param keys    业务唯一键（便于跟踪这条消息，如果确定不需要，就传空字符串）
     * @return 发送成功还是失败
     */
    public boolean syncSend(Object payload, String topic, String tag, String keys) {
        final Message<Object> message = createMessage(payload, keys);
        try {
            topic = resolve(topic);
            final String destination = destination(topic, tag);
            SendResult sendResult = rocketMQTemplate
                    .syncSend(destination, message, DEFAULT_SEND_MSG_TIMEOUT);
            return processSendResult(payload, topic, tag, keys, sendResult);
        } catch (Exception e) {
            return processSendException(payload, topic, tag, keys, e);
        }
    }

    /**
     * 同步发送消息
     *
     * @param payload Object
     * @param topic String
     * @date 2024/2/29 17:08
     * <AUTHOR>
     */
    public void syncSend(Object payload, String topic) {
        syncSend(payload, topic, "*", "");
    }

    /**
     * 异步发送消息
     *
     * @param payload 消息负载
     * @param topic   主题
     * @param tag     标签
     * @param keys    消息Key
     */
    public void asyncSend(Object payload, String topic, String tag, String keys) {
        asyncSend(payload, topic, tag, keys, null, null);
    }

    public void asyncSend(Object payload, String topic, String tag, String keys, Integer delayLevel) {
        asyncSend(payload, topic, tag, keys, null, null, delayLevel);
    }

    public void asyncSend(Object payload, String topic, String tag, String keys,
                          Consumer<SendResult> successCallback, Consumer<Throwable> exceptionCallback,
                          Integer delayLevel) {
        final Message<Object> message = createMessage(payload, keys);
        topic = resolve(topic);
        String finalTopic = topic;
        rocketMQTemplate.asyncSend(destination(topic, tag), message, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                if (successCallback != null) {
                    successCallback.accept(sendResult);
                }
            }

            @Override
            public void onException(Throwable throwable) {
                alert(payload, finalTopic, tag, keys, throwable, null);
                if (exceptionCallback != null) {
                    exceptionCallback.accept(throwable);
                }
            }
        }, DEFAULT_SEND_MSG_TIMEOUT, delayLevel);
    }

    /**
     * 异步发送消息
     *
     * @param payload           消息负载
     * @param topic             主题
     * @param tag               标签
     * @param keys              消息Key
     * @param successCallback   成功回调
     * @param exceptionCallback 异常回调
     */
    public void asyncSend(Object payload, String topic, String tag, String keys,
                          Consumer<SendResult> successCallback, Consumer<Throwable> exceptionCallback) {
        final Message<Object> message = createMessage(payload, keys);
        topic = resolve(topic);
        String finalTopic = topic;
        rocketMQTemplate.asyncSend(destination(topic, tag), message, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                if (successCallback != null) {
                    successCallback.accept(sendResult);
                }
            }

            @Override
            public void onException(Throwable throwable) {
                alert(payload, finalTopic, tag, keys, throwable, null);
                if (exceptionCallback != null) {
                    exceptionCallback.accept(throwable);
                }
            }
        });
    }

    /**
     * 处理消息降级
     *
     * @param payload    消息负载
     * @param topic      主题
     * @param tag        标签
     * @param keys       消息Key
     * @param sendResult 发送结果
     * @param throwable  异常
     * @return 是否降级成功
     */
    private boolean processMessageFallback(Object payload, String topic, String tag, String keys,
                                           SendResult sendResult,
                                           Throwable throwable) {
        final String logFormat = "Message fallback {}, topic={}, tag={}, keys={}, payload={}";
        return Optional.ofNullable(messageFallbackProvider.getIfAvailable())
                .map(messageFallback -> {
                    final boolean fallbackResult = messageFallback
                            .fallback(payload, topic, tag, keys, sendResult, throwable);
                    if (fallbackResult) {
                        log.info(logFormat, "success", topic, tag, keys, payload);
                    } else {
                        log.warn(logFormat, "fail", topic, tag, keys, payload);
                    }
                    return fallbackResult;
                }).orElse(false);
    }

    /**
     * 处理发送异常（降级、日志、报警）
     *
     * @param payload 消息负载
     * @param topic   主题
     * @param tag     标签
     * @param keys    消息key
     * @param e       异常
     */
    private boolean processSendException(Object payload, String topic, String tag, String keys,
                                         Exception e) {
        log.error("Message send exception, topic={}, tag={}, keys={}, payload={}", topic, tag, keys,
                payload, e);

        //如果消息降级成功不返回失败
        if (processMessageFallback(payload, topic, tag, keys, null, e)) {
            return true;
        }
        alert(payload, topic, tag, keys, e, null);
        return false;
    }

    /**
     * 处理发送结果（降级、日志、报警）
     *
     * @param payload    消息负载
     * @param topic      主题
     * @param tag        标签
     * @param keys       消息key
     * @param sendResult 发送结果
     */
    protected boolean processSendResult(Object payload, String topic, String tag, String keys,
                                        SendResult sendResult) {
        if (sendResult.getSendStatus() == SendStatus.SEND_OK) {
            log.info("Message send ok, topic={}, tag={}, keys={}, msgId={}, payload={}",
                    topic, tag,
                    keys, sendResult.getMsgId(), StrUtil.maxLength(JsonUtil.toJson(payload), 100));
            return true;
        }
        //如果消息降级成功不返回失败
        if (processMessageFallback(payload, topic, tag, keys, sendResult, null)) {
            return true;
        }
        log.warn(
                "Message send not ok, sendStatus={}, topic={}, tag={}, keys={}, msgId={}, payload={}",
                sendResult.getSendStatus(), topic, tag, keys, sendResult.getMsgId(), payload);
        alert(payload, topic, tag, keys, null, sendResult);
        return false;
    }

    @NonNull
    private String resolve(String value) {
        return this.environment.resolvePlaceholders(value);
    }

    /**
     * 报警逻辑
     *
     * @param payload    消息负载
     * @param topic      主题
     * @param tag        标签
     * @param keys       消息key
     * @param e          异常
     * @param sendResult 发送结果
     */
    private void alert(Object payload, String topic, String tag, String keys, Throwable e,
                       SendResult sendResult) {
        try {
            ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
                Alert.text(MessageRobotCode.GLOBAL,
                        StringUtil
                                .format("RocketMQ发送消息异常, topic={}, tag={}, keys={}, payload={}, ex={}",
                                        topic, tag, keys, payload,
                                        sendResult != null ? sendResult.getSendStatus()
                                                : ExceptionUtil.getSimpleStackString(e)));
            });
        } catch (TaskRejectedException exception) {
            log.error("预警提交至线程池失败");
        }
    }

    /**
     * 封装消息
     *
     * @param payload 负载
     * @param keys    key
     * @return 消息对象
     */
    protected Message<Object> createMessage(Object payload, String keys) {
        return RocketMQMessageBuilder.withPayload(payload).setKeys(keys).build();
    }

    /**
     * 组装消息路由
     *
     * @param topic 主题
     * @param tag   标签
     * @return 路由
     */
    protected final String destination(String topic, String tag) {
        StringBuilder builder = new StringBuilder();
        builder.append(topic);
        if (StringUtils.isNotBlank(tag)) {
            builder.append(":").append(tag);
        }
        final String destination = builder.toString();
        return resolve(destination);
    }


}
