package com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt;

import com.google.gson.*;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @since 2022/1/18
 */
public class JsonUtil {
    private static final Gson gson;

    static {
        gson = (new GsonBuilder())
                .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
                .registerTypeAdapter(Boolean.TYPE, BooleanJsonDeserializer.INSTANCE)
                .registerTypeAdapter(Boolean.class, BooleanJsonDeserializer.INSTANCE)
                .create();
    }

    public static  <T> T fromJson(String json, Type typeOfT) throws JsonSyntaxException {
        return gson.fromJson(json, typeOfT);
    }

    /**
     * copy from wdtsdk
     */
    private static class BooleanJsonDeserializer implements JsonDeserializer<Boolean> {
        static final BooleanJsonDeserializer INSTANCE = new BooleanJsonDeserializer();

        private BooleanJsonDeserializer() {
        }

        @Override
        public Boolean deserialize(JsonElement json, Type type,
                                   JsonDeserializationContext context) throws JsonParseException {
            if (json.isJsonPrimitive()) {
                JsonPrimitive primitive = (JsonPrimitive) json;
                if (primitive.isBoolean()) {
                    return primitive.getAsBoolean();
                }

                if (primitive.isNumber()) {
                    Number num = primitive.getAsNumber();
                    if (!(num instanceof Integer) && !(num instanceof Long) && !(num instanceof Short) && !(num instanceof Byte)) {
                        return num.doubleValue() != 0.0D;
                    }

                    return num.intValue() != 0;
                }
            }

            return !json.isJsonNull();
        }
    }
}
