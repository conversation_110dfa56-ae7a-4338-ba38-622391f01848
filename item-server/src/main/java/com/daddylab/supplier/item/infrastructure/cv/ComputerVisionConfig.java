package com.daddylab.supplier.item.infrastructure.cv;

import com.microsoft.azure.cognitiveservices.vision.computervision.ComputerVisionClient;
import com.microsoft.azure.cognitiveservices.vision.computervision.ComputerVisionManager;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2022/11/28
 */
@ConfigurationProperties(prefix = "cv")
@Configuration
@Data
public class ComputerVisionConfig {

    private String subscriptionKey;
    private String endpoint;

    @Bean
    public ComputerVisionClient computerVisionClient() {
        return ComputerVisionManager.authenticate(subscriptionKey).withEndpoint(endpoint);
    }
}
