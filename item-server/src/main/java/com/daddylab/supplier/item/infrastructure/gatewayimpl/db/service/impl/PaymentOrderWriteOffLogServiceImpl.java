package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentOrderWriteOffLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PaymentOrderWriteOffLogMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPaymentOrderWriteOffLogService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 付款单冲销流程日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-18
 */
@Service
public class PaymentOrderWriteOffLogServiceImpl extends DaddyServiceImpl<PaymentOrderWriteOffLogMapper, PaymentOrderWriteOffLog> implements IPaymentOrderWriteOffLogService {

}
