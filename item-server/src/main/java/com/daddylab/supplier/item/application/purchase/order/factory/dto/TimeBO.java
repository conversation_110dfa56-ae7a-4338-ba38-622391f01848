package com.daddylab.supplier.item.application.purchase.order.factory.dto;

import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;

/**
 * <AUTHOR> up
 * @date 2022年08月31日 6:23 PM
 */
@Data
public class TimeBO {
    /**
     * yyyyMM
     */
    private String operateMonth;
    /**
     * 2023-08-01 00:00:00
     */
    private String monthStart;
    /**
     * 2023-08-31 23:59:59
     */
    private String monthEnd;
    /**
     * 月份第一天00.00.00时间戳。10位
     */
    private Long monthStartTime;
    /**
     * 月份最后一天23.59.59时间戳。10位
     */
    private Long monthEndTime;
    /**
     * 这个月开始时间
     */
    private LocalDateTime startLocalDateTime;
    /**
     * 这个月结束时间
     */
    private LocalDateTime endLocalDateTime;
    /**
     * yyyy/MM
     */
    private String purchaseStyleMonth;

    /**
     * 自然月份最后一天的0点，10位时间戳，。一般是作为采购单的采购时间
     */
    private Long purchaseDateTime;

    public TimeBO(String operateMonth, String monthStart, String monthEnd) {
        this.operateMonth = operateMonth;
        this.monthStart = monthStart;
        this.monthEnd = monthEnd;
    }

    public TimeBO() {
    }


    public static TimeBO of(String month) {
        LocalDateTime jobTargetMonth = DateUtil.parse(month, "yyyyMM");
        LocalDate localDate = jobTargetMonth.toLocalDate();
        LocalDate firstDay = LocalDate.of(localDate.getYear(), localDate.getMonth(), 1);
        LocalDate lastDay = localDate.with(TemporalAdjusters.lastDayOfMonth());
        String siftStart = firstDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 00:00:00";
        String siftEnd = lastDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 23:59:59";
        TimeBO timeBO = new TimeBO(month, siftStart, siftEnd);
        LocalDateTime start = LocalDateTime.parse(siftStart, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime end = LocalDateTime.parse(siftEnd, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        timeBO.setStartLocalDateTime(start);
        timeBO.setEndLocalDateTime(end);
        long startSecond = start.atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
        long endSecond = end.atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
        timeBO.setMonthStartTime(startSecond);
        timeBO.setMonthEndTime(endSecond);
        String purchaseMonth = month.substring(0, 4) + "/" + month.substring(4, 6);
        timeBO.setPurchaseStyleMonth(purchaseMonth);
        timeBO.setPurchaseDateTime(DateUtil.getSysPurchaseTime(month));
        return timeBO;
    }

    public static void main(String[] args) {
        TimeBO of = TimeBO.of("202310");
        System.out.println(of.getPurchaseDateTime());
    }

//    /**
//     * 开始时间和结束时间必须是在同一个月。
//     *
//     * @param start yyyy-MM-dd HH:mm:ss
//     * @param end   yyyy-MM-dd HH:mm:ss
//     * @return
//     */
//    public static TimeBO of(String start, String end) {
//        String s1 = start.replaceAll("-", "").substring(0, 6);
//        String s2 = end.replaceAll("-", "").substring(0, 6);
//        Assert.isTrue(s1.equals(s2), "不允许跨月");
//
//        TimeBO timeBO = new TimeBO();
//        timeBO.setOperateMonth(s1);
//        timeBO.setMonthStart(start);
//        timeBO.setMonthEnd(end);
//        timeBO.setMonthStartTime(DateUtil.parseTime(start, "yyyy-MM-dd HH:mm:ss"));
//        timeBO.setMonthEndTime(DateUtil.parseTime(end, "yyyy-MM-dd HH:mm:ss"));
//        timeBO.setPurchaseStyleMonth(s1.substring(0, 4) + "/" + s1.substring(4, 6));
//        return timeBO;
//    }


}
