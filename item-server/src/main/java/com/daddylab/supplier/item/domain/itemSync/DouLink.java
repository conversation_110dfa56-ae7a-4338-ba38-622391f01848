package com.daddylab.supplier.item.domain.itemSync;

import cn.hutool.core.net.url.UrlQuery;
import com.alibaba.cola.exception.BizException;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/11/15
 */
public class DouLink {

    /**
     * <a href="https://haohuo.jinritemai.com/ecommerce/trade/detail/index.html?id=3581979923847470754&origin_type=604">example</a>
     */
    private final URL url;

    private final String goodsId;

    public DouLink(String url) {
        try {
            if (!StringUtil.startWith(url, "http://") || !StringUtil.startWith(url, "https://")) {
                url = "http://" + url;
            }
            this.url = new URL(url);
            final UrlQuery urlQuery = UrlQuery.of(this.url.getQuery(), Charset.defaultCharset(),
                    true);
            this.goodsId = Optional.ofNullable(urlQuery.get("id")).map(CharSequence::toString)
                    .orElse("");
        } catch (MalformedURLException e) {
            throw new BizException(ErrorCode.VERIFY_PARAM.getCode(),
                    "URL格式错误:" + e.getMessage());
        }
    }

    public URL getUrl() {
        return url;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public static boolean checkUrl(String url) {
        try {
            new DouLink(url);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

}
