package com.daddylab.supplier.item.application.afterSaleLogistics.domain;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/23
 */
@Data
public class LogisticsTraceItem {
  private Long trackDate;
  private String shortStatus;
  private String trackStatus;

  public boolean containKeyword(String keyword) {
    return shortStatus != null && shortStatus.contains(keyword)
        || trackStatus != null && trackStatus.contains(keyword);
  }
}
