package com.daddylab.supplier.item.domain.drawer.enums;

import java.io.Serializable;
import java.util.Objects;
import lombok.Getter;

/**
 * Class  ItemDrawerChangeEnum
 *
 * @Date 2022/6/3上午9:13
 * <AUTHOR>
 */
@Getter
public enum ItemDrawerChangeAuthEnum implements Serializable {
    /**
     * 字段类型
     */
    BUYER("NP_Buyer", "采购负责人权限"),
    PRINCIPAL("NP_Principal", "产品负责人权限"),
    QC("NP_QC", "Qc负责人权限"),
    TAOBAO("NP_Taobao", "淘系组权限"),
    WEIXIN("NP_Weixin", "微信生态组权限"),
    DOUYIN("NP_Douyin", "抖音渠道负责人权限"),
    DETAIL_IMAGES("NP_UploadDetailImages", "详情图上传"),
    IMAGE_MAR("/item-drawer-mark/add", "图片标注权限(法务)"),
    LIVE_VERBAL_TRICK("NP_LIVE_VERBAL_TRICK", "直播话术权限"),
    ;


    ItemDrawerChangeAuthEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
    private final String value;
    private final String desc;

    public static ItemDrawerChangeAuthEnum of(String value) {
        for (ItemDrawerChangeAuthEnum itemDrawerChangeEnum : ItemDrawerChangeAuthEnum.values()) {
            if (Objects.equals(value, itemDrawerChangeEnum.getValue())) {
                return itemDrawerChangeEnum;
            }
        }
        return null;
    }
}
