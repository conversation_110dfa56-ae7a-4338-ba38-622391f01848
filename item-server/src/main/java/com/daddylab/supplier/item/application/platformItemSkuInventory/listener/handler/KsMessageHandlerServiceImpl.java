package com.daddylab.supplier.item.application.platformItemSkuInventory.listener.handler;

import com.daddylab.supplier.item.application.platformItemSkuInventory.PlatformItemSkuSyncService;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemSkuInventoryService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopAuthorizationService;
import com.daddylab.supplier.item.infrastructure.third.annotations.KsHandlerMapping;
import com.daddylab.supplier.item.infrastructure.third.enums.KsMessageEnum;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.IKsMessageHandlerService;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.domain.dto.*;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.impl.KuaiShouServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @class RedBookMessageHandlerServiceImpl.java
 * @description 消息处理handler
 * @date 2024-03-01 15:55
 */
@Slf4j
@Service
public class KsMessageHandlerServiceImpl implements IKsMessageHandlerService {

    @Autowired
    KuaiShouServiceFactory kuaiShouServiceFactory;

    @Autowired
    IShopAuthorizationService shopAuthorizationService;

    @Autowired
    List<PlatformItemSkuSyncService> platformItemSkuSyncServiceList;

    @KsHandlerMapping(KsMessageEnum.ITEM_CREATE)
    public void itemCreate(KsItemCreateDTO ksItemCreateDTO) {
        log.info("[快手][商品创建事件触发] ksItemCreateDTO={}", ksItemCreateDTO);

        syncItem(ksItemCreateDTO.getItemId());
    }

    private void syncItem(Long itemId) {
        platformItemSkuSyncServiceList.stream().filter(service -> service.defaultType() == Platform.KUAISHOU)
                .forEach(service -> {
                    for (ShopAuthorization shopAuthorization : shopAuthorizationService.listNotExpiredAuthorizations(
                            Platform.KUAISHOU)) {
                        service.syncItem(shopAuthorization.getSn(), String.valueOf(itemId));
                    }
                });
    }

    @KsHandlerMapping(KsMessageEnum.ITEM_AUDIT_STATUS_CHANGE)
    public void ksItemAuditStatusChange(KsItemAuditStatusChangeDTO ksItemAuditStatusChangeDTO) {
        log.info("[快手][商品审核状态变更消息] ksItemAuditStatusChangeDTO={}", ksItemAuditStatusChangeDTO);
        syncItem(ksItemAuditStatusChangeDTO.getItemId());

    }

    @KsHandlerMapping(KsMessageEnum.ITEM_OFFLINE_STATUS_CHANGE)
    public void ksItemOfflineStatusChange(KsItemOfflineStatusChangeDTO ksItemOfflineStatusChangeDTO) {
        log.info("[快手][商品上下线状态变更消息] ksItemOfflineStatusChangeDTO={}", ksItemOfflineStatusChangeDTO);
        syncItem(ksItemOfflineStatusChangeDTO.getItemId());

    }

    @KsHandlerMapping(KsMessageEnum.ITEM_PRICE_CHANGE)
    public void ksItemPriceChange(KsItemPriceChangeDTO priceChangeDTO) {
        log.info("[快手][商品价格变更消息] priceChangeDTO={}", priceChangeDTO);
        syncItem(priceChangeDTO.getItemId());

    }

    @KsHandlerMapping(KsMessageEnum.ITEM_SKU_CHANGE)
    public void skuChange(KsSkuChangeDTO skuChangeDTO) {
        log.info("[快手][sku变更事件触发] skuChangeDTO={}", skuChangeDTO);
        syncItem(skuChangeDTO.getItemId());

    }

    @KsHandlerMapping(KsMessageEnum.ITEM_SKU_STOCK_CHANGE)
    public void skuStockChange(KsSkuChangeDTO skuChangeDTO) {
        log.info("[快手][商品sku库存数量变更事件触发] skuChangeDTO={}", skuChangeDTO);
        syncItem(skuChangeDTO.getItemId());
    }

    @KsHandlerMapping(KsMessageEnum.ITEM_SKU_STOCK_ZERO)
    public void skuStockZero(KsSkuStockZeroDTO skuStockZeroDTO) {
        log.info("[快手][商品SKU库存数量为0变更消息] skuStockZeroDTO={}", skuStockZeroDTO);
        syncItem(skuStockZeroDTO.getItemId());
    }

    @KsHandlerMapping(KsMessageEnum.ITEM_DEFAULT)
    @Override
    public void handler(KsMessageDTO ksMessageDTO) {
        log.info("[快手][公共消息事件处理方法] ksMessageDTO={}", ksMessageDTO);
    }
}
