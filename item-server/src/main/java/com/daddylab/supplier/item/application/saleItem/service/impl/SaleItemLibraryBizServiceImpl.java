package com.daddylab.supplier.item.application.saleItem.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.platformItem.PlatformItemBizService;
import com.daddylab.supplier.item.application.platformItem.data.PlatformItemDetail;
import com.daddylab.supplier.item.application.platformItem.data.PlatformItemDetailSku;
import com.daddylab.supplier.item.application.platformItem.query.PlatformItemPageQuery;
import com.daddylab.supplier.item.application.platformItem.query.PlatformItemQuery;
import com.daddylab.supplier.item.application.saleItem.SaleItemErrorCode;
import com.daddylab.supplier.item.application.saleItem.ShopSnData;
import com.daddylab.supplier.item.application.saleItem.dto.SaleItemLibraryCmd;
import com.daddylab.supplier.item.application.saleItem.dto.SaleItemLibrarySheet;
import com.daddylab.supplier.item.application.saleItem.query.SaleItemLibraryQueryPage;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.service.SaleItemLibraryBizService;
import com.daddylab.supplier.item.application.saleItem.vo.ErrorMsgVO;
import com.daddylab.supplier.item.application.saleItem.vo.SaleItemLibraryVO;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.SaleItemLibraryTransMapper;
import com.daddylab.supplier.item.domain.auth.enums.ResourceType;
import com.daddylab.supplier.item.domain.exportTask.ExportDomainService;
import com.daddylab.supplier.item.domain.exportTask.ExportEntityPlus;
import com.daddylab.supplier.item.domain.exportTask.ExportTaskGateway;
import com.daddylab.supplier.item.domain.exportTask.ExportType;
import com.daddylab.supplier.item.domain.platformItem.service.event.SyncSaleItemLibraryEvent;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseOperate;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.SaleItemLibrary;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.SaleItemLibraryMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemSkuService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ISaleItemLibraryService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 销售商品库
 */
@Slf4j
@Service
public class SaleItemLibraryBizServiceImpl implements SaleItemLibraryBizService {

    @Autowired
    private PlatformItemBizService platformItemBizService;

    @Autowired
    private IPlatformItemSkuService platformItemSkuService;

    @Autowired
    private IPlatformItemService platformItemService;

    @Autowired
    private ISaleItemLibraryService saleItemLibraryService;

    @Autowired
    private SaleItemLibraryMapper saleItemLibraryMapper;

    @Autowired
    private ExportTaskGateway exportTaskGateway;

    @Autowired
    private ExportDomainService exportDomainService;

    @Autowired
    private NewGoodsBizService newGoodsBizService;

    @Value("${files.import-sale-item-library-template}")
    private String excelTemplateUrl;

    @Value("${sale-item-library-item-link}")
    private String saleItemLibraryItemLink;

    @Autowired
    private ShopSnData shopSnData;

    @Override
    public PageResponse<SaleItemLibraryVO> queryPage(SaleItemLibraryQueryPage queryPage) {
        log.info("分页查询销售商品库商品 queryPage:{}", queryPage);

        List<SaleItemLibraryVO> saleItemLibraryVOS = Lists.newArrayList();
        int count = saleItemLibraryMapper.count(queryPage);
        if(count > 0){
            saleItemLibraryVOS = saleItemLibraryMapper.queryPage(queryPage);
            saleItemLibraryVOS.forEach(saleItemLibraryVO ->{
                if(Objects.nonNull(saleItemLibraryVO.getMemberStoreItemId())){
                    PlatformItem platformItem = getPlatformItem(saleItemLibraryVO.getMemberStoreItemId());
                    if(Objects.nonNull(platformItem)){
                        PlatformItemDetail platformItemDetail = platformItemBizService.detail(platformItem.getId()).getData();
                        assembleSaleItemLibraryVO(saleItemLibraryVO, platformItemDetail);
                        if(StringUtil.isNotBlank(saleItemLibraryVO.getMemberStoreItemId())){
                            saleItemLibraryVO.setItemLink(String.format(saleItemLibraryItemLink, saleItemLibraryVO.getMemberStoreItemId()));
                            saleItemLibraryVO.setItemStatus(platformItemDetail.getStatus());
                        }
                    }
                }
            });
        }
        return PageResponse.of(saleItemLibraryVOS,  count, queryPage.getPageSize(), queryPage.getPageIndex());
    }



    @Override
    public SingleResponse<SaleItemLibraryVO> getBySkuCode(String skuCode) {
        log.info("销售商品库商品, 根据skuCode查询数据 skuCode:{}", skuCode);

        SaleItemLibraryVO saleItemLibraryVO = getSaleItemLibraryVO(skuCode, null);
        return SingleResponse.of(saleItemLibraryVO);
    }

    @Override
    public Response createOrUpdate(SaleItemLibraryCmd cmd) {
        log.info("销售商品库商品新增/修改 cmd:{}", cmd);
        Assert.notNull(cmd, "入参不得为空");
        Assert.isTrue(StringUtils.isNotBlank(cmd.getItemSkuCode()), "商品编码不能为空");

        SaleItemLibrarySheet saleItemLibrarySheet = SaleItemLibraryTransMapper.INSTANCE.cmdToSheet(cmd);
        checkNull(saleItemLibrarySheet);

        SaleItemLibraryVO saleItemLibraryVO = getSaleItemLibraryVO(cmd.getItemSkuCode(), cmd.getId());
        SaleItemLibrary saleItemLibrary = SaleItemLibraryTransMapper.INSTANCE.voToDo(saleItemLibraryVO, cmd);
        saleItemLibraryService.saveOrUpdate(saleItemLibrary);
        return Response.buildSuccess();
    }


    @Override
    public Response delete(Long id) {
        log.info("销售商品库商品 删除 id:{}", id);
        Assert.notNull(id, "id入参不得为空");

        saleItemLibraryService.removeById(id);
        return Response.buildSuccess();
    }

    @Override
    public String getExcelTemplateUrl() {
        return excelTemplateUrl;
    }

    @Override
    public SingleResponse<PurchaseOperate> importExcel(InputStream inputStream, Boolean confirm) {
        List<SaleItemLibrarySheet> exportItems = EasyExcel.read(inputStream)
                .headRowNumber(1)
                .head(SaleItemLibrarySheet.class).sheet().doReadSync();

        if (CollectionUtil.isEmpty(exportItems)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "导入Excel文件为空");
        }
       return importSaleItems(exportItems, confirm);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exportExcel(SaleItemLibraryQueryPage queryPage) {
        final ExportTask exportTask = ExportEntityPlus.save(ExportTaskType.SALE_ITEM_LIBRARY);
        exportTask.setId(exportTaskGateway.saveOrUpdateExportTask(exportTask));
        exportDomainService.export(queryPage, exportTask , ExportType.SALE_ITEM_LIBRARY, SaleItemLibrarySheet.class);
    }


    @Override
    public void synchronizeItemName(SyncSaleItemLibraryEvent syncSaleItemLibraryEvent) {

        QueryWrapper<SaleItemLibrary> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StringUtils.isNotBlank(syncSaleItemLibraryEvent.getItemSkuCode()), SaleItemLibrary::getItemSkuCode, syncSaleItemLibraryEvent.getItemSkuCode());
        final List<SaleItemLibrary> list = saleItemLibraryService.list(queryWrapper);
        if(CollectionUtil.isEmpty(list)){
            return;
        }

        final SaleItemLibrary saleItemLibrary = list.stream().findFirst().get();
        if(Objects.equals(syncSaleItemLibraryEvent.getGoodsName(), saleItemLibrary.getGoodsName())){
            return;
        }

        LambdaUpdateWrapper<SaleItemLibrary> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(SaleItemLibrary::getGoodsName, syncSaleItemLibraryEvent.getGoodsName()).eq(SaleItemLibrary::getItemSkuCode, syncSaleItemLibraryEvent.getItemSkuCode());
        saleItemLibraryService.update(updateWrapper);
    }


    /**
     * 查询商品信息（存在不同的平台，会有多条, 不同的商品也有可能相同的sku）
     */
    private List<PlatformItemSku> listPlatformItemSku(String skuCode){
        QueryWrapper<PlatformItemSku> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StringUtils.isNotBlank(skuCode), PlatformItemSku::getSkuCode, skuCode);
        return platformItemSkuService.list(queryWrapper);
    }


    private void assembleSaleItemLibraryVO(SaleItemLibraryVO saleItemLibraryVO, PlatformItemDetail platformItemDetail){
        List<PlatformItemDetailSku> skus = platformItemDetail.getSkus();
        if(CollectionUtil.isNotEmpty(skus)){
            PlatformItemDetailSku platformItemDetailSku = skus.stream().filter(i -> Objects.equals(i.getSkuCode(), saleItemLibraryVO.getItemSkuCode())).findFirst().orElse(null);
            if(Objects.nonNull(platformItemDetailSku)){
                saleItemLibraryVO.setSpecification(platformItemDetailSku.getSpecName());
            }
        }
        NewGoods newGoods = newGoodsBizService.getBySkuCode(saleItemLibraryVO.getItemSkuCode());
        saleItemLibraryVO.setSaleItemLibraryVO(saleItemLibraryVO, platformItemDetail.getItemId(), platformItemDetail.getItemCode(), newGoods);
    }


    private SaleItemLibraryVO getSaleItemLibraryVO(String skuCode, Long id){
        List<PlatformItemSku> platformItemSkus = checkItemSku(skuCode, id);

        //同一个店铺不同的商品有可能有同样的SkuCode, 取最新修改的那一条  key:店铺编号
        Map<String, PlatformItemSku> map = platformItemSkus.stream().collect(
                Collectors.toMap(PlatformItemSku::getShopNo, Function.identity(), (c1, c2) -> DateUtil.toTime(c1.getModified()) > DateUtil.toTime(c2.getModified()) ? c1 : c2));
        List<PlatformItemSku> list = new ArrayList<>(map.values());
        List<Long> platformItemIds = list.stream().map(PlatformItemSku::getPlatformItemId).collect(Collectors.toList());
        SaleItemLibraryVO saleItemLibraryVO = new SaleItemLibraryVO();
        saleItemLibraryVO.setItemSkuCode(skuCode);
        for (Long platformItemId: platformItemIds) {
             PlatformItemDetail  platformItemDetail = platformItemBizService.detail(platformItemId).getData();
             if(Objects.nonNull(platformItemDetail)){
                 assembleSaleItemLibraryVO(saleItemLibraryVO, platformItemDetail);
                 saleItemLibraryVO.setPlatformItemId(platformItemDetail, saleItemLibraryVO, shopSnData, saleItemLibraryItemLink);
             }
        }
        return saleItemLibraryVO;
    }


    /**
     * 根据会员店商品Id查询商品信息
     * @param memberStoreItemId  会员店商品ID
     */
    private PlatformItem getPlatformItem(String memberStoreItemId){
        PlatformItemPageQuery pageQuery = new PlatformItemPageQuery();
        pageQuery.setShopNo(shopSnData.getMemberStoreSn());
        pageQuery.setOuterItemId(memberStoreItemId);
        IPage<PlatformItem> page = platformItemService.pageQuery(pageQuery);
        return page.getRecords().stream().findFirst().orElse(null);
    }


    private List<PlatformItemSku> checkItemSku(String skuCode, Long id){
        Map<String, SaleItemLibrary> skuCodeSaleItemLibraryMap = batchQueryBySkuCode(ListUtil.of(skuCode));
        if(!skuCodeSaleItemLibraryMap.isEmpty()){
            if(Objects.isNull(id)){
                throw ExceptionPlusFactory.bizException(SaleItemErrorCode.ITEM_SKU_CODE_EXIST.getCode(), SaleItemErrorCode.ITEM_SKU_CODE_EXIST.getMsg());
            }
            checkPermission(id);
            final SaleItemLibrary saleItemLibrary = saleItemLibraryService.getById(id);
            if(!Objects.equals(skuCode, saleItemLibrary.getItemSkuCode())){
                throw ExceptionPlusFactory.bizException(SaleItemErrorCode.ITEM_SKU_CODE_EXIST.getCode(), SaleItemErrorCode.ITEM_SKU_CODE_EXIST.getMsg());
            }
        }

        List<PlatformItemSku> platformItemSkus = listPlatformItemSku(skuCode);
        if (CollectionUtil.isEmpty(platformItemSkus)) {
            throw ExceptionPlusFactory.bizException(SaleItemErrorCode.ITEM_NULL.getCode(), SaleItemErrorCode.ITEM_NULL.getMsg());
        }


        return platformItemSkus;
    }


    private SingleResponse importSaleItems(List<SaleItemLibrarySheet> exportSaleItems, Boolean confirm) {
        int batchSize = 500;
        Map<String, Boolean> duplicateSkuCodeMap = new HashMap<>(batchSize);
        Set<String> repeatSkuCode = Sets.newHashSet();
        Set<String> errorSkuCode = Sets.newHashSet();
        Set<String> existSkuCode = Sets.newHashSet();
        Map<String, Long> existSkuCodeMap = new HashMap<>();
        for (List<SaleItemLibrarySheet> saleItemLibrarySheets : CollUtil.split(exportSaleItems, batchSize)) {
            List<String> skuCodes = saleItemLibrarySheets.stream().map(SaleItemLibrarySheet::getItemSkuCode).collect(Collectors.toList());
            Map<String, SaleItemLibrary> existSkuCodeList = batchQueryBySkuCode(skuCodes);

            for (SaleItemLibrarySheet saleItemLibrarySheet : saleItemLibrarySheets) {
                checkNull(saleItemLibrarySheet);

                if (duplicateSkuCodeMap.getOrDefault(saleItemLibrarySheet.getItemSkuCode(), false)) {
                    log.error("当前表格存在相同的商品编码：{}", saleItemLibrarySheet.getItemSkuCode());
                    repeatSkuCode.add(saleItemLibrarySheet.getItemSkuCode());
                }

                List<PlatformItemSku> platformItemSkus = listPlatformItemSku(saleItemLibrarySheet.getItemSkuCode());
                if(CollectionUtil.isEmpty(platformItemSkus)){
                    log.error("后端商品不存在：{}", saleItemLibrarySheet.getItemSkuCode());
                    errorSkuCode.add(saleItemLibrarySheet.getItemSkuCode());
                    continue;
                }
                SaleItemLibrary existSaleItemLibrary = existSkuCodeList.get(saleItemLibrarySheet.getItemSkuCode());
                if (Objects.nonNull(existSaleItemLibrary)) {
                    log.error("数据库已存在数据：{}", saleItemLibrarySheet.getItemSkuCode());
                    existSkuCode.add(saleItemLibrarySheet.getItemSkuCode());
                    existSkuCodeMap.put(saleItemLibrarySheet.getItemSkuCode(), existSaleItemLibrary.getId());
                }
                duplicateSkuCodeMap.put(saleItemLibrarySheet.getItemSkuCode(), true);
            }

            if(!repeatSkuCode.isEmpty() || !errorSkuCode.isEmpty()){
                ErrorMsgVO errorMsgVO = new ErrorMsgVO();
                if(!repeatSkuCode.isEmpty()){
                    errorMsgVO.setRepeatSkuCode(repeatSkuCode);
                }
                if(!errorSkuCode.isEmpty()){
                    errorMsgVO.setErrorSkuCode(errorSkuCode);
                }
                SingleResponse singleResponse = new SingleResponse();
                singleResponse.setSuccess(false);
                singleResponse.setErrCode(SaleItemErrorCode.IMPORT_SALE_ITEM_ERROR.getCode());
                singleResponse.setErrMessage(SaleItemErrorCode.IMPORT_SALE_ITEM_ERROR.getMsg());
                singleResponse.setData(errorMsgVO);
                return singleResponse;
            }

            if(!existSkuCode.isEmpty() && !confirm){
                ErrorMsgVO errorMsgVO = new ErrorMsgVO();
                errorMsgVO.setExistSkuCode(existSkuCode);

                SingleResponse singleResponse = new SingleResponse();
                singleResponse.setSuccess(false);
                singleResponse.setErrCode(SaleItemErrorCode.IMPORT_SALE_ITEM_EXIST.getCode());
                singleResponse.setErrMessage(SaleItemErrorCode.IMPORT_SALE_ITEM_EXIST.getMsg());
                singleResponse.setData(errorMsgVO);
                return singleResponse;
            }

            if (CollectionUtil.isNotEmpty(saleItemLibrarySheets)){
                List<SaleItemLibrary> saleItemLibraryList = SaleItemLibraryTransMapper.INSTANCE.listSheetCmdToDo(saleItemLibrarySheets);
                saleItemLibraryList.forEach(e -> e.setId(existSkuCodeMap.get(e.getItemSkuCode())));
                saleItemLibraryService.saveOrUpdateBatch(saleItemLibraryList);
                duplicateSkuCodeMap.clear();
            }
        }


        PurchaseOperate purchaseOperate = new PurchaseOperate();
        purchaseOperate.setCount(exportSaleItems.size());
        return SingleResponse.of(purchaseOperate);
    }


    /**
     * 根据SkuCode 查询数据库存在的商品
     */
    private Map<String, SaleItemLibrary> batchQueryBySkuCode(List<String> skuCodes) {
        QueryWrapper<SaleItemLibrary> query = new QueryWrapper<>();
        query.lambda().in(SaleItemLibrary::getItemSkuCode, skuCodes);
        return saleItemLibraryService.list(query).stream().collect(Collectors.toMap(SaleItemLibrary::getItemSkuCode, Function.identity()));
    }

    private void checkPermission(Long saleItemLibraryId){
        boolean permission = false;
        if(!UserContext.hasPermission(GlobalConstant.SALE_ITEM_LIBRARY_UPDATE)){
            //查询
            SaleItemLibrary saleItemLibrary = saleItemLibraryService.getById(saleItemLibraryId);
            if(Objects.equals(saleItemLibrary.getCreatedUid(), UserContext.getUserId())){
                permission = true;
            }
        }else {
            permission = true;
        }
        if(!permission){
            throw ExceptionPlusFactory.bizException("您没有权限编辑该商品");
        }
    }


    private void checkNull(SaleItemLibrarySheet saleItemLibrarySheet){
        boolean flag = StringUtil.isBlank(saleItemLibrarySheet.getItemSkuCode()) || StringUtil.isBlank(saleItemLibrarySheet.getCategory())
                || StringUtil.isBlank(saleItemLibrarySheet.getPlatformPrice()) || StringUtil.isBlank(saleItemLibrarySheet.getPlatformActivityContent())
                || StringUtil.isBlank(saleItemLibrarySheet.getPlatformCost()) || StringUtil.isBlank(saleItemLibrarySheet.getRemark())
                || StringUtil.isBlank(saleItemLibrarySheet.getPlatformActivityCode());
        if (flag) {
            throw ExceptionPlusFactory.bizException(SaleItemErrorCode.IMPORT_SALE_ITEM_HIATUS.getCode(),
                    SaleItemErrorCode.IMPORT_SALE_ITEM_HIATUS.getMsg());
        }
    }
}
