package com.daddylab.supplier.item.application.saleItem.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2023年12月12日 3:52 PM
 */
@Data
@ApiModel("直播话术返回封装")
public class LiveVerbalTrickVO {

    private Long id;

    @ApiModelProperty("直播话术名称")
    private String name;

    @ApiModelProperty("直播话术状态 0:未进入审核流程/未知 5:待法务审核 10:待QC审核 100:审核完成")
    private Integer liveVerbalTrickStatus;

    private Long updatedAt;

}
