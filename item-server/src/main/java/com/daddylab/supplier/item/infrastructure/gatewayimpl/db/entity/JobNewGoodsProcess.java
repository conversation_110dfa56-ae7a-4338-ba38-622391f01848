package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 新品商品数据处理（临时任务）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class JobNewGoodsProcess implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /**
     * 是否删除：0否1是
     */
    @TableLogic(value = "0", delval = "id")
    private Long isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 过滤原因
     */
    private String filterReason;

    /**
     * 原始数据行号
     */
    private Integer rowId;

    /**
     * 导入原始数据
     */
    private String importData;

    /**
     * 小程序商品ID
     */
    private Long mallItemId;

    /**
     * 商品编码
     */
    private String itemNo;

    /**
     * P系统款号
     */
    private String partnerItemNo;

    /**
     * ERP后端商品ID
     */
    private Long itemId;

    /**
     * ERP后端商品标准编码
     */
    private String itemCode;

    /**
     * ERP商品抽屉ID
     */
    private Long drawerId;

    /**
     * 上新计划处理 0 未处理 1 添加到上新计划 2 已存在上新计划
     */
    private Integer planHandled;

    /**
     * 计划处理日志
     */
    private String planHandleLog;

    /**
     * 计划ID
     */
    private Long planId;

    /**
     * 图片已处理
     */
    private Integer imgHandled;

    /**
     * 图片备份ID
     */
    private Long imgBackupId;

    /**
     * 图片处理日志
     */
    private String imgHandleLog;

    /**
     * [淘宝]数据已处理
     */
    private Integer taobaoHandled;

    /**
     * [淘宝]数据备份ID
     */
    private Long taobaoBackupId;

    /**
     * [淘宝]处理日志
     */
    private String taobaoHandleLog;

    /**
     * [小红书]数据已处理
     */
    private Integer xiaohongshuHandled;

    /**
     * [小红书]数据备份ID
     */
    private Long xiaohongshuBackupId;

    /**
     * [小红书]处理日志
     */
    private String xiaohongshuHandleLog;

    /**
     * [抖音]数据已处理
     */
    private Integer douyinHandled;

    /**
     * [抖音]数据备份ID
     */
    private Long douyinBackupId;

    /**
     * [抖音]处理日志
     */
    private String douyinHandleLog;

    /**
     * [快手]数据已处理
     */
    private Integer kuaishouHandled;

    /**
     * [快手]数据备份ID
     */
    private Long kuaishouBackupId;

    /**
     * [快手]处理日志
     */
    private String kuaishouHandleLog;

    /**
     * 是否已合并：0否 1是 2无需合并 3合并异常
     */
    private Integer merged;

    /**
     * 合并到哪个商品（商品编码）
     */
    private String mergeTo;

    /**
     * 合并处理日志
     */
    private String mergeLog;

    /**
     * 商品参照库SPU编码
     */
    private String refSpuCode;

    /**
     * 水印图片已处理
     */
    private Integer watermarkImgHandled;

    /**
     * 水印图片处理日志
     */
    private String watermarkImgHandleLog;

    /**
     * 水印图片备份ID
     */
    private Long watermarkImgBackupId;

    /**
     * 图片已处理
     */
    private Integer imgHandled2;

    /**
     * 图片备份ID
     */
    private Long imgBackupId2;

    /**
     * 图片处理日志
     */
    private String imgHandleLog2;


}
