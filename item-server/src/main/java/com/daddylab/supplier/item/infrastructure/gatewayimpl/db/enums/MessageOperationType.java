package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 4:16 下午
 * @description
 */
@Getter
@AllArgsConstructor
public enum MessageOperationType implements IEnum<Integer> {

    /**
     * 不要黄线
     */
    ADD_BACK_ITEM(0, "新增后端商品"),
    EDIT_BACK_ITEM(1, "编辑后端商品"),
    WARN_PLATFORM_ITEM(2, "预警平台商品"),
    SYS_PURCHASE_ORDER(3, "系统生成采购单"),
    ;
    @EnumValue
    private final Integer value;
    private final String desc;


}
