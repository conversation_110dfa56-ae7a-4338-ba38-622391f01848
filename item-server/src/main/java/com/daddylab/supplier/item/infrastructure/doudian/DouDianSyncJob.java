package com.daddylab.supplier.item.infrastructure.doudian;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ThirdPlatformSync;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ThirdPlatformSyncExternalTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ThirdPlatformSyncLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IThirdPlatformSyncExternalTaskService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IThirdPlatformSyncLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IThirdPlatformSyncService;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.doudian.open.api.material_queryMaterialDetail.MaterialQueryMaterialDetailResponse;
import com.doudian.open.api.material_queryMaterialDetail.data.MaterialInfo;
import com.doudian.open.api.material_queryMaterialDetail.data.MaterialQueryMaterialDetailData;
import com.doudian.open.api.product_editV2.ProductEditV2Request;
import com.doudian.open.api.product_editV2.ProductEditV2Response;
import com.doudian.open.api.product_editV2.param.ProductEditV2Param;
import com.doudian.open.core.AccessToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.daddylab.supplier.item.infrastructure.utils.StringUtil.format;

/**
 * <AUTHOR> up
 * @date 2022年12月06日 6:58 PM
 */
@Component
@Slf4j
public class DouDianSyncJob {

    @Resource
    IThirdPlatformSyncLogService iThirdPlatformSyncLogService;

    @Resource
    IThirdPlatformSyncService iThirdPlatformSyncService;

    @Resource
    IThirdPlatformSyncExternalTaskService thirdPlatformSyncExternalTaskService;

    @Resource
    DouDianTemplate douDianTemplate;

    @Resource
    DouDianCommon common;


    @XxlJob("douDianSyncJob")
    public void run() {

        // 找到状态是等待执行中的，同步平台是抖店的，itemId信息
        List<ThirdPlatformSync> list = iThirdPlatformSyncService.lambdaQuery()
                .eq(ThirdPlatformSync::getPlatformType, PlatformType.DOU_DIAN)
                .eq(ThirdPlatformSync::getState, ThirdPlatformSyncState.RUNNING)
                .select().list();
        if (CollUtil.isEmpty(list)) {
            return;
        }

        // 找到这些itemId的同步日志。
        List<ThirdPlatformSyncLog> waitingItemLogList = iThirdPlatformSyncLogService.lambdaQuery()
                .eq(ThirdPlatformSyncLog::getPlatformType, PlatformType.DOU_DIAN)
                .in(ThirdPlatformSyncLog::getItemId, list.stream().map(ThirdPlatformSync::getItemId)
                        .collect(Collectors.toList()))
                .eq(ThirdPlatformSyncLog::getErrorLevel, PlatformSyncErrorLevel.NONE)
                .ne(ThirdPlatformSyncLog::getReq, "")
                .select().list();
        log.info("[DouDianSyncJob]定时轮询图片异步上传结果。待查询处理图片的syncIds:{}", JsonUtil.toJson(waitingItemLogList.stream()
                .map(ThirdPlatformSyncLog::getSyncId).collect(Collectors.toList())));
        if (CollUtil.isEmpty(waitingItemLogList)) {
            return;
        }

        // 获取抖店token
        AccessToken accessToken = common.getAccessToken();
        // 轮训，查看每一个item的异步任务处理情况
        waitingItemLogList.forEach(thisItemWaitingLog -> {
            // 每一个item的下属所有异步同步任务，都会分配同一个唯一id
            Long thisItemSyncId = thisItemWaitingLog.getSyncId();

            // 查询这个item同步任务关联到的每一个异步素材上传任务结果。并且更新任务结果
            List<ThirdPlatformSyncExternalTask> thisItemTaskList = thirdPlatformSyncExternalTaskService.lambdaQuery()
                    .eq(ThirdPlatformSyncExternalTask::getSyncId, thisItemSyncId)
                    .eq(ThirdPlatformSyncExternalTask::getState, ThirdPlatformSyncExternalTaskState.WAITING)
                    .select().list();
            List<ThirdPlatformSyncExternalTask> updateByTaskResultList = new LinkedList<>();
            thisItemTaskList.forEach(task -> {
                log.info("[DouDianSyncJob]定时轮询图片异步上传结果。待处理syncId:{}，taskId:{}", thisItemSyncId, task.getTaskId());
                ThirdPlatformSyncExternalTask handingTask = taskHandler(task, accessToken, thisItemWaitingLog.getItemId());
                updateByTaskResultList.add(handingTask);
            });
            if (CollUtil.isNotEmpty(updateByTaskResultList)) {
                thirdPlatformSyncExternalTaskService.updateBatchById(updateByTaskResultList);
            }

            // 查询这个item下属所有的抖店素材异步上传任务是否全部为完成，如果全部完成，那就item同步，没有继续等待
            Integer allCount = thirdPlatformSyncExternalTaskService.lambdaQuery()
                    // 每一个item的同步任务，都会分配一个唯一的syncId
                    .eq(ThirdPlatformSyncExternalTask::getSyncId, thisItemSyncId)
                    .select().count();
            Integer finishCount = thirdPlatformSyncExternalTaskService.lambdaQuery()
                    // 每一个item的同步任务，都会分配一个唯一的syncId
                    .eq(ThirdPlatformSyncExternalTask::getSyncId, thisItemSyncId)
                    .ne(ThirdPlatformSyncExternalTask::getState, ThirdPlatformSyncExternalTaskState.WAITING)
                    .select().count();
            // 等待状态任务为0，全部都处理过了，开始同步item到抖店。
            if (Objects.equals(allCount, finishCount)) {
                syncItem(thisItemWaitingLog, accessToken);
            }
        });
    }

    /**
     * 异步任务处理
     *
     * @param task
     * @param accessToken
     * @param itemId
     * @return
     */
    private ThirdPlatformSyncExternalTask taskHandler(ThirdPlatformSyncExternalTask task, AccessToken accessToken
            , Long itemId) {
        MaterialQueryMaterialDetailResponse material0;
        try {
            // 查询每一个素材上传任务的完成情况。
            material0 = douDianTemplate.getMaterial0(task.getTaskId(), accessToken);
            log.info("[DouDianSyncJob]定时轮询图片异步上传结果。taskId:{},material0:{}", task.getTaskId(), JsonUtil.toJson(material0));
            // 请求成功，并且审核成功，保存此素材信息对应的抖店url
            if (material0.isSuccess()) {
                MaterialQueryMaterialDetailData data = material0.getData();
                if (Objects.nonNull(data)) {
                    if (Objects.nonNull(data.getMaterialInfo())) {
                        MaterialInfo materialInfo = data.getMaterialInfo();
                        if (StrUtil.isNotBlank(materialInfo.getByteUrl())) {
                            task.setData(materialInfo.getByteUrl());
                            task.setState(ThirdPlatformSyncExternalTaskState.FINISH);
                            return task;
                        }
                    }
                }
            }
            // 请求失败，保存下任务失败信息
            else {
                // 记录异常日志
                iThirdPlatformSyncLogService.asyncSaveDouDianLog(itemId,
                        format("异步上传图片到素材中心。结果查询失败。{},{},{}", task.getType().getDesc(),
                                task.getTaskId(), material0.getMsg() + material0.getSubMsg()),
                        PlatformSyncErrorLevel.WARN);
                task.setError(material0.getCode() + "。" + material0.getMsg() + "。" + material0.getSubMsg());
                task.setState(ThirdPlatformSyncExternalTaskState.ERROR);
                return task;
            }
        } catch (Exception e) {
            log.error("查询抖店素材异步上传结果异常,itemId:{},taskId:{}", itemId, task.getTaskId(), e);
            iThirdPlatformSyncLogService.asyncSaveDouDianLog(itemId,
                    format("查询抖店素材异步上传结果异常。{},{},{}", task.getType().getDesc(), task.getTaskId(), e.getMessage()),
                    PlatformSyncErrorLevel.WARN);
            task.setError("查询抖店素材异步上传结果异常。" + e.getMessage());
            task.setState(ThirdPlatformSyncExternalTaskState.ERROR);
            return task;
        }

        task.setError("抖店素材异步上传相应结果格式混乱,material0:" + JsonUtil.toJson(material0));
        task.setState(ThirdPlatformSyncExternalTaskState.ERROR);
        return task;
    }

    private final static String ERROR_FLAG = "查询商品信息失败";

    /**
     * 商品同步处理
     *
     * @param syncLog
     * @param accessToken
     */
    public void syncItem(ThirdPlatformSyncLog syncLog, AccessToken accessToken) {
        try {
            DouDianSyncBO douDianSyncBO = JsonUtil.parse(syncLog.getReq(), DouDianSyncBO.class);
            assert douDianSyncBO != null;
            ProductEditV2Param param = douDianSyncBO.getProductEditV2Param();
            Integer syncType = douDianSyncBO.getSyncType();
            if (Objects.isNull(syncType)) {
                return;
            }
            if (syncType.equals(ThirdPlatformSyncType.DOUDIAN_EDIT.getValue())) {
                param.setPic(getByteUrl(ThirdPlatformSyncExternalTaskType.DOUDIAN_MAIN, syncLog.getSyncId()));
                param.setDescription(getByteUrl(ThirdPlatformSyncExternalTaskType.DOUDIAN_DESC, syncLog.getSyncId()));
            }
            if (syncType.equals(ThirdPlatformSyncType.DOUDIAN_NEW.getValue())) {
                param.setPic(getByteUrl(ThirdPlatformSyncExternalTaskType.DOUDIAN_MAIN, syncLog.getSyncId()));
                param.setDescription(getByteUrl(ThirdPlatformSyncExternalTaskType.DOUDIAN_DESC, syncLog.getSyncId()));
                param.setSpecPic(getByteUrl(ThirdPlatformSyncExternalTaskType.DOUDIAN_SPEC, syncLog.getSyncId()));
            }

            ProductEditV2Request request = new ProductEditV2Request();
            request.setParam(param);
            ProductEditV2Response response = request.execute(accessToken);
            syncLog.setReq(JsonUtil.toJson(param));
            syncLog.setResp(JsonUtil.toJson(response));
            syncLog.setError(response.getMsg() + response.getSubMsg());
            if (!response.isSuccess()) {
                syncLog.setErrorLevel(PlatformSyncErrorLevel.ERROR);
                // [2023-01-03 七喜] 包装下报错信息
                String error = "【抖店】调用商品编辑同步接口失败。" + response.getSubMsg();
                if (response.getSubMsg().equals(ERROR_FLAG)) {
                    error = error + "。请检查填写链接是否正确。";
                }
                // 跟那个心商品同步任务。
                iThirdPlatformSyncService.lambdaUpdate()
                        .eq(ThirdPlatformSync::getItemCode, syncLog.getItemCode() + "_" + syncLog.getSyncId())
                        .set(ThirdPlatformSync::getState, ThirdPlatformSyncState.ERROR)
                        .set(ThirdPlatformSync::getError, error)
                        .update();
            } else {
                syncLog.setErrorLevel(PlatformSyncErrorLevel.NONE);
                // 更新同步状态，同步完成
                iThirdPlatformSyncService.lambdaUpdate()
                        .eq(ThirdPlatformSync::getItemCode, syncLog.getItemCode() + "_" + syncLog.getSyncId())
                        .set(ThirdPlatformSync::getError, response.getSubMsg() + "。" + response.getMsg())
                        .set(ThirdPlatformSync::getState, ThirdPlatformSyncState.FINISH)
                        .update();
            }
            iThirdPlatformSyncLogService.updateById(syncLog);
        } catch (Exception e) {
            log.error("【抖店】调用商品编辑接口异常，itemId:{},syncId:{}", syncLog.getItemId(), syncLog.getSyncId(), e);
            iThirdPlatformSyncService.lambdaUpdate()
                    .eq(ThirdPlatformSync::getItemCode, syncLog.getItemCode() + "_" + syncLog.getSyncId())
                    .set(ThirdPlatformSync::getState, ThirdPlatformSyncState.ERROR)
                    .set(ThirdPlatformSync::getError, "【抖店】调用商品编辑同步接口异常。请联系二柴🐶")
                    .update();
        }
    }


    /**
     * pic是主图，|分割。
     * description 详情图，| 分割
     * specPic 规格图 , 分割。
     *
     * @param taskType
     * @param syncId
     * @return
     */
    public String getByteUrl(ThirdPlatformSyncExternalTaskType taskType, Long syncId) {
        List<ThirdPlatformSyncExternalTask> list = thirdPlatformSyncExternalTaskService.lambdaQuery()
                .eq(ThirdPlatformSyncExternalTask::getSyncId, syncId)
                .eq(ThirdPlatformSyncExternalTask::getType, taskType)
                .orderByAsc(ThirdPlatformSyncExternalTask::getId)
                .select(ThirdPlatformSyncExternalTask::getData, ThirdPlatformSyncExternalTask::getState)
                .list();
        List<String> finishDateList = list.stream().filter(val -> val.getState().equals(ThirdPlatformSyncExternalTaskState.FINISH))
                .map(ThirdPlatformSyncExternalTask::getData).collect(Collectors.toList());

        if (CollUtil.isEmpty(finishDateList)) {
            return "";
        }
        if (ThirdPlatformSyncExternalTaskType.DOUDIAN_MAIN.equals(taskType)
                || ThirdPlatformSyncExternalTaskType.DOUDIAN_DESC.equals(taskType)) {
            return StrUtil.join("|", finishDateList);
        }
        if (ThirdPlatformSyncExternalTaskType.DOUDIAN_SPEC.equals(taskType)) {
            return StrUtil.join(",", finishDateList);
        }
        return "";
    }

    public static void main(String[] args) {
        String json = "{\"productEditV2Param\":{\"productId\":3586053558451234171,\"productType\":null,\"categoryLeafId\":null,\"productFormat\":null,\"name\":\"老爸评测山楂鸡内金软膏滋补营养消食积食调理工厂发货\",\"recommendRemark\":null,\"pic\":null,\"description\":null,\"payType\":null,\"deliveryMethod\":null,\"cdfCategory\":null,\"reduceType\":null,\"assocIds\":null,\"freightId\":null,\"weight\":null,\"weightUnit\":null,\"deliveryDelayDay\":null,\"presellType\":null,\"presellDelay\":null,\"presellEndTime\":null,\"supply7dayReturn\":null,\"mobile\":null,\"commit\":true,\"brandId\":null,\"remark\":null,\"outProductId\":null,\"qualityList\":null,\"specName\":null,\"specs\":\"颜色|/^规格|168克（12克*14）\",\"specPrices\":\"[{\\\"spec_detail_name1\\\":\\\"/\\\",\\\"spec_detail_name2\\\":\\\"168克（12克*14）\\\",\\\"spec_detail_name3\\\":\\\"\\\",\\\"stock_num\\\":null,\\\"price\\\":5800,\\\"code\\\":\\\"100080901\\\",\\\"step_stock_num\\\":null,\\\"supplier_id\\\":null,\\\"outer_sku_id\\\":null,\\\"delivery_infos\\\":null}]\",\"specPic\":null,\"maximumPerOrder\":null,\"limitPerBuyer\":null,\"minimumPerOrder\":null,\"productFormatNew\":null,\"spuId\":null,\"appointDeliveryDay\":null,\"thirdUrl\":null,\"extra\":null,\"src\":null,\"outerProductId\":null,\"standardBrandId\":null,\"needCheckOut\":null,\"poiResource\":null,\"forceUseQualityList\":null,\"carVinCode\":null,\"presellConfigLevel\":null,\"needRechargeMode\":null,\"accountTemplateId\":null,\"presellDeliveryType\":null,\"whiteBackGroundPicUrl\":null,\"longPicUrl\":null,\"afterSaleService\":null,\"sellChannel\":null,\"startSaleType\":null,\"delayRule\":null,\"materialVideoId\":null,\"pickupMethod\":null,\"sizeInfoTemplateId\":null,\"substituteGoodsUrl\":null,\"saleChannelType\":null,\"namePrefix\":null,\"storeId\":null,\"mainProductId\":null,\"saleLimitId\":null},\"itemId\":52261,\"syncType\":31}";
        DouDianSyncBO douDianSyncBO = JsonUtil.parse(json, DouDianSyncBO.class);
        System.out.println(douDianSyncBO.getItemId());

    }
}
