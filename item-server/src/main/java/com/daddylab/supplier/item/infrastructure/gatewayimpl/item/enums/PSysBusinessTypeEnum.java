package com.daddylab.supplier.item.infrastructure.gatewayimpl.item.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ItemBusinessTypeSupplierCooperate           = 1   // 电商-商品合作
 * ItemBusinessTypeSupplierMerchantSettlement  = 2   // 电商-商家入驻
 * ItemBusinessTypeSupplierDadList             = 4   // 电商-老爸清单
 * ItemBusinessTypeSupplierDadCheck            = 8   // 电商-老爸抽检
 * ItemBusinessTypeGreenHomeCooperate          = 16  // 绿色家装-商品合作
 * ItemBusinessTypeGreenHomeMerchantSettlement = 32  // 绿色家装-商家入驻
 * ItemBusinessTypeGreenHomeDadList            = 64  // 绿色家装-老爸清单
 * ItemBusinessTypeGreenHomeDadCheck           = 128 // 绿色家装-老爸抽检
 * ItemBusinessTypeCps                         = 256 // CPS合作品
 *
 * <AUTHOR>
 * @since 2025/2/17
 */
@AllArgsConstructor
@Getter
public enum PSysBusinessTypeEnum implements IIntegerEnum {
    ItemBusinessTypeAll(0, "全部", null),
    ItemBusinessTypeSupplierCooperate(1, "电商-商品合作", PSysCooperatorEnum.ItemCooperatorSupplier),
    ItemBusinessTypeSupplierMerchantSettlement(2, "电商-商家入驻", PSysCooperatorEnum.ItemCooperatorSupplier),
    ItemBusinessTypeSupplierDadList(4, "电商-老爸清单", PSysCooperatorEnum.ItemCooperatorSupplier),
    ItemBusinessTypeSupplierDadCheck(8, "电商-老爸抽检", PSysCooperatorEnum.ItemCooperatorSupplier),
    ItemBusinessTypeGreenHomeCooperate(16, "绿色家装-商品合作", PSysCooperatorEnum.ItemCooperatorGreenHome),
    ItemBusinessTypeGreenHomeMerchantSettlement(32, "绿色家装-商家入驻", PSysCooperatorEnum.ItemCooperatorGreenHome),
    ItemBusinessTypeGreenHomeDadList(64, "绿色家装-老爸清单", PSysCooperatorEnum.ItemCooperatorGreenHome),
    ItemBusinessTypeGreenHomeDadCheck(128, "绿色家装-老爸抽检", PSysCooperatorEnum.ItemCooperatorGreenHome),
    ItemBusinessTypeCps(256, "CPS合作品", null);
    private final Integer value;
    private final String desc;
    private final PSysCooperatorEnum cooperator;

    public static PSysBusinessTypeEnum valueOf(Integer value) {
        for (PSysBusinessTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}
