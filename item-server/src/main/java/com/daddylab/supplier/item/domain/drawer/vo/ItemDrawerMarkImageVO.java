package com.daddylab.supplier.item.domain.drawer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * Class  ItemDrawerForm
 *
 * @Date 2022/5/31下午4:18
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ItemDrawerMarkImageVO", description = "商品抽屉图片标记VO")
public class ItemDrawerMarkImageVO implements Serializable {

    private static final long serialVersionUID = 3662789845299894442L;
    @ApiModelProperty(value = "标记id")
    private Long id;

    @ApiModelProperty(value = "抽屉id")
    private Long drawerId;

    @ApiModelProperty(value = "合成图片地址")
    private String url;

    /**
     * 标注元数据
     */
    @ApiModelProperty(value = "标注元数据")
    private String markMeta;

    @ApiModelProperty(value = "标注内容")
    private List<ItemDrawerMarkImageContentVO> itemDrawerMarkImageContents;
}
