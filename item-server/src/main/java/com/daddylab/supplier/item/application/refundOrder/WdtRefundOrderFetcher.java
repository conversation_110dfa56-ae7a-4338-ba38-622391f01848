package com.daddylab.supplier.item.application.refundOrder;

import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.RefundAPI;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/5/4
 */
@Component
@Slf4j
public class WdtRefundOrderFetcher implements PageFetcher {

    private final WdtGateway wdtGateway;
    private final WdtRefundOrderDataSyncService wdtRefundOrderDataSyncService;

    public WdtRefundOrderFetcher(WdtGateway wdtGateway,
            WdtRefundOrderDataSyncService wdtRefundOrderDataSyncService) {
        this.wdtGateway = wdtGateway;
        this.wdtRefundOrderDataSyncService = wdtRefundOrderDataSyncService;
    }

    @Override
    public int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
        return query(startTime, endTime, 1, 1, true).getTotalCount();
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, long pageIndex, long pageSize,
            RunContext runContext) {
        final RefundSearchResponse response = query(startTime, endTime,
                (int) pageSize,
                (int) pageIndex, false);
        handleResponse(response);
    }

    private RefundSearchResponse query(LocalDateTime startTime,
            LocalDateTime endTime, int pageSize, int pageNo, boolean calcTotal) {
        try {
            final RefundAPI api = wdtGateway.getQimenAPI(RefundAPI.class);
            final Pager pager = new Pager(pageSize, pageNo - 1, calcTotal);

            final RefundSearchParams params = new RefundSearchParams();
            params.setModifiedFrom(DateUtil.format(startTime));
            params.setModifiedTo(DateUtil.format(endTime));

            return api.search(params, pager);
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapFetchException(e, fetchDataType());

        }
    }

    private void handleResponse(RefundSearchResponse response) {
        wdtRefundOrderDataSyncService.saveOrUpdateByApiQueryResponse(response);
    }

    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.WDT_REFUND_ORDER;
    }

}
