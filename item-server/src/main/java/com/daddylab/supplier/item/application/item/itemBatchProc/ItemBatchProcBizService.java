package com.daddylab.supplier.item.application.item.itemBatchProc;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.types.itemBatchProc.*;

/**
 * <AUTHOR>
 * @since 2023/11/13
 */
public interface ItemBatchProcBizService {
    SingleResponse<Long> modifyBuyer(ModifyBuyerCmd cmd);

    SingleResponse<Long> modifyWarehouse(ModifyWarehouseCmd cmd);

    void scheduleTasks();


    SingleResponse<Long> modifyProvider(ModifyProviderCmd cmd);

    SingleResponse<Long> modifyStatus(ModifyStatusCmd cmd);
}
