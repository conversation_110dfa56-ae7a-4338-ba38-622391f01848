package com.daddylab.supplier.item.application.salesOutStock.dto;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;

/**
 * <AUTHOR> up
 * @date 2023年10月20日 2:39 PM
 */
@Data
public class SalesOutStockPageQuery extends PageQuery {

    private static final long serialVersionUID = -8440946067244494372L;

    @ApiModelProperty("出库单号")
    private String orderNo;

    @ApiModelProperty("仓库编码")
    private String warehouseNo;

    @ApiModelProperty("店铺名称")
    private String shopName;

    @ApiModelProperty("订单编号（旺）")
    private String tradeNo;

    @ApiModelProperty("出库状态")
    private Integer status;

    @ApiModelProperty("商品SKU")
    private String specCode;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("商品编码")
    private String goodsNo;

    @ApiModelProperty("物流公司")
    private String logisticsName;

    @ApiModelProperty("物流编码")
    private String logisticsNo;

    @ApiModelProperty("客户昵称")
    private BuyerNameDO buyerNameDO;

    private Collection<Long> wdtOrderIds;

    private Collection<Long> stockOutIds;

    private Collection<String> tradeNos;

}
