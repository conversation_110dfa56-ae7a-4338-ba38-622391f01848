package com.daddylab.supplier.item.application.purchasePayable.eventListeners;

import com.alibaba.fastjson.JSON;
import com.daddylab.supplier.item.application.common.event.StockInOrOutEvent;
import com.daddylab.supplier.item.application.purchasePayable.dto.PurchasePayableCmd;
import com.daddylab.supplier.item.application.purchasePayable.service.PurchasePayableBizService;
import com.daddylab.supplier.item.common.trans.PurchasePayableTransMapper;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;


@EventBusListener(value = "purchasePayableEventListener", mode = EventBusListener.MODE.ASYNC)
@Slf4j
public class PurchasePayableEventListener {

    @Autowired
    private PurchasePayableBizService purchasePayableBizService;

    @Subscribe
    public void listener(StockInOrOutEvent event) {
        log.info("生成应付单接收消息：{}", JSON.toJSONString(event));
        final PurchasePayableCmd cmd = PurchasePayableTransMapper.INSTANCE.eventToCmd(event);
        purchasePayableBizService.create(cmd);
    }
}
