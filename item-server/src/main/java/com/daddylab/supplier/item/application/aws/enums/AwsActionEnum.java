package com.daddylab.supplier.item.application.aws.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AwsActionEnum implements IEnum<String> {

    SUBMIT ("提交"),

    WITHDRAW ("作废"),

    AGREE ("同意"),

    DISAGREE ("拒绝"),

    UNDO_REDO ("撤销重办"),

    ACTIVATION("激活"),

    RE_SUBMIT("再次提交"),

    TERMINATE("终止"),


    ;

    private final String desc;

    @Override
    public String getValue() {
        return null;
    }

    public static AwsActionEnum getByDesc(String desc){
        for (AwsActionEnum awsActionEnum: values()) {
            if(Objects.equals(awsActionEnum.getDesc(), desc)){
                return awsActionEnum;
            }
        }
        return null;
    }
}
