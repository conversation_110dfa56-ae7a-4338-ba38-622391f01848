package com.daddylab.supplier.item.application.salesOutStock;


import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.SalesOrderDto;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.SkuUnitDto;
import com.daddylab.supplier.item.application.salesOutStock.dto.*;

import java.util.List;

public interface SalesOutStockBizService {

    /**
     * 客户昵称 下拉选列表。模糊查询。
     *
     * @param name
     * @return
     */
    MultiResponse<BuyerNameDO> getBuyerNickName(String name);

    /**
     * 模糊分页查询物流公司名称
     *
     * @param query
     * @return
     */
    PageResponse<String> getLogisticsName(LogisticsNamePageQuery query);


    // ----------- 我是业务操作分割线 ----------------------

    PageResponse<SalesOutStockPageVO> queryPage(SalesOutStockPageQuery query);

    SingleResponse<SalesOutStockDetailVO> viewDetail(Long stockoutId);


    /**
     * 根据结算单明细生成销售出库单，并且将此单据同步到金蝶
     * 结算10件，采购订单8件。差额2件。这两件需要再生成一笔销售出库单。
     *
     * @param details
     * @return
     */
    SingleResponse<SalesOrderDto> generateOrderBySettlement(String warehouseNo,
                                                            List<SkuUnitDto> salesOutList,
                                                            Long auditDate,Boolean mockSync);


}
