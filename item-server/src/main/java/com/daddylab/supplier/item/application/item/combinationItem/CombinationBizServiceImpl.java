package com.daddylab.supplier.item.application.item.combinationItem;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.application.bizLevelDivision.BizLevelDivisionConvert;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.CombinationItemSaveCmd;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.ComposeSkuCmd;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.ProportionCmd;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.ResetProportionBO;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.CombinationItemNamePageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.CombinationItemPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.*;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.CombinationItemTransMapper;
import com.daddylab.supplier.item.common.trans.SkuTransMapper;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.controller.item.dto.ItemAttrDto;
import com.daddylab.supplier.item.controller.item.dto.ItemBuyerDto;
import com.daddylab.supplier.item.controller.item.dto.SkuExtraPriceDto;
import com.daddylab.supplier.item.domain.exportTask.ExportDomainService;
import com.daddylab.supplier.item.domain.exportTask.ExportEntityPlus;
import com.daddylab.supplier.item.domain.exportTask.ExportTaskGateway;
import com.daddylab.supplier.item.domain.exportTask.ExportType;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import com.daddylab.supplier.item.domain.exportTask.dto.combinationItem.ComposeSkuBaseSheet;
import com.daddylab.supplier.item.domain.exportTask.dto.combinationItem.ComposerSkuAllSheet;
import com.daddylab.supplier.item.domain.exportTask.dto.combinationItem.ComposerSkuWithItemPriceSheet;
import com.daddylab.supplier.item.domain.exportTask.dto.combinationItem.ComposerSkuWithSkuPriceSheet;
import com.daddylab.supplier.item.domain.item.entity.CombinationPriceEntity;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.item.service.CombinationItemDomainService;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.bizLevelDivision.BizDivisionContext;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CombinationItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/7 10:00 上午
 * @description
 */
@Service
@Slf4j
public class CombinationBizServiceImpl implements CombinationItemBizService {

    @Autowired
    ItemSkuGateway itemSkuGateway;

    @Autowired
    CombinationItemDomainService combinationItemDomainService;

    @Autowired
    ICombinationItemService iCombinationItemService;

    @Autowired
    IComposeSkuService iComposeSkuService;

    @Autowired
    IBizLevelDivisionService bizLevelDivisionService;

    @Autowired
    @Qualifier("UserGatewayCacheImpl")
    UserGateway userGateway;

    @Autowired
    ExportDomainService exportDomainService;

    @Autowired
    ExportTaskGateway exportTaskGateway;

    @Autowired
    IPlatformItemService iPlatformItemService;

    @Autowired
    IItemSkuService iItemSkuService;

    @Autowired
    IItemService itemService;

    @Autowired
    IItemSkuPriceService iItemSkuPriceService;

    @Resource
    CombinationItemMapper combinationItemMapper;

    @Resource
    IBizLevelDivisionService iBizLevelDivisionService;
    @Autowired
    private PlatformItemMapper platformItemMapper;


    @Override
    public PageResponse<ComposeSkuVO> pageSku(ComposeSkuPageQuery query) {
        final List<ComposeSkuDO> skuCombinationDos = BizDivisionContext.invoke(
                BizUnionTypeEnum.SPU,
                c -> c.setBizIdRef("s.item_id"),
                () -> iCombinationItemService.listSkuDetail(query));

        final List<Long> skuIdList = skuCombinationDos.stream().map(ComposeSkuDO::getSkuId).collect(Collectors.toList());
        Map<Long, List<SkuAttrRefDO>> attrMap = getSkuAttrRefMap(skuIdList);

        List<ComposeSkuVO> voList = new LinkedList<>();
        skuCombinationDos.forEach(skuCombinationDO -> {
            final Long skuId = skuCombinationDO.getSkuId();
            final List<SkuAttrRefDO> orDefault = attrMap.getOrDefault(skuId, new LinkedList<>());
            final List<ItemAttrDto> itemAttrDtoList = SkuTransMapper.INSTANCE.toAttrDtos(orDefault);

            if (Boolean.TRUE.equals(query.getWithPrice())) {
                final ComposeSkuWithPriceVO skuListVO = SkuTransMapper.INSTANCE.toVoWithPrice(skuCombinationDO);
                skuListVO.setSpecifications(itemAttrDtoList);
                voList.add(skuListVO);
            } else {
                final ComposeSkuVO skuListBaseVO = SkuTransMapper.INSTANCE.toVoNoPrice(skuCombinationDO);
                skuListBaseVO.setSpecifications(itemAttrDtoList);
                voList.add(skuListBaseVO);
            }
        });

        // fill 商品采购人信息
        List<Long> itemIds = voList.stream().map(ComposeSkuVO::getItemId).distinct().collect(Collectors.toList());
        Map<Long, ItemBuyerDto> itemsBuyerMap = itemService.getItemsBuyerMap(itemIds);

        // SKU 业务类型信息
        final Map<Long, List<CorpBizTypeDTO>> divisionMap = iBizLevelDivisionService.queryByItemId(itemIds);

        final List<Long> skuIds = voList.stream().map(ComposeSkuVO::getSkuId).collect(Collectors.toList());
        final List<ItemSkuPrice> skuContractPriceList = CollUtil.isEmpty(skuIds) ? Collections.emptyList() : iItemSkuPriceService.listPriceBySkuIds(2, skuIds);
        final List<ItemSkuPrice> skuCommissionList = CollUtil.isEmpty(skuIds) ? Collections.emptyList() : iItemSkuPriceService.listPriceBySkuIds(2, skuIds);

        for (ComposeSkuVO vo : voList) {
            ItemBuyerDto itemBuyerDto = itemsBuyerMap.get(vo.getItemId());
            vo.setItemBuyerUserId(Optional.ofNullable(itemBuyerDto).map(ItemBuyerDto::getBuyerUserId).orElse(0L));
            StaffInfo staffInfo = userGateway.queryStaffInfoById(vo.getItemBuyerUserId());
            vo.setItemBuyerUserName(Objects.nonNull(staffInfo) ? staffInfo.getUserName() : "");
            vo.setItemBuyerNickName(Objects.nonNull(staffInfo) ? staffInfo.getNickname() : "");

            final List<CorpBizTypeDTO> corpBizTypeDTO = divisionMap.getOrDefault(vo.getItemId(), Collections.emptyList());
            vo.setCorpBizType(corpBizTypeDTO);

            //合同销售价
            final List<SkuExtraPriceDto> skuContractPrices = getItemSkuPrices(skuContractPriceList, vo.getSkuId());
            vo.setContractSalePrices(skuContractPrices);

            //平台佣金
            final List<SkuExtraPriceDto> platformCommissions = getItemSkuPrices(skuCommissionList, vo.getSkuId());
            vo.setPlatformCommissions(platformCommissions);
        }

        final Integer count = BizDivisionContext.invoke(
                BizUnionTypeEnum.SPU,
                c -> c.setBizIdRef("s.item_id"),
                () -> iCombinationItemService.countSkuDetail(query));
        return PageResponse.of(voList, count, query.getPageSize(), query.getPageIndex());
    }

    @NotNull
    private static List<SkuExtraPriceDto> getItemSkuPrices(List<ItemSkuPrice> skuPrices, Long skuId) {
        return skuPrices.stream()
                .filter(v -> skuId.equals(v.getSkuId()))
                .sorted(Comparator.comparing(ItemSkuPrice::getId).reversed())
                .map(v -> {
                    final SkuExtraPriceDto skuExtraPriceDto = new SkuExtraPriceDto();
                    skuExtraPriceDto.setVal(v.getPrice());
                    skuExtraPriceDto.setStartTime(v.getStartTime());
                    skuExtraPriceDto.setEndTime(v.getEndTime());
                    skuExtraPriceDto.setType(v.getType());
                    return skuExtraPriceDto;
                })
                .collect(Collectors.toList());
    }

    public Map<Long, List<SkuAttrRefDO>> getSkuAttrRefMap(List<Long> skuIdList) {
        final List<SkuAttrRefDO> skuAttrList = itemSkuGateway.getSkuAttrList(skuIdList);
        Map<Long, List<SkuAttrRefDO>> attrMap = new HashMap<>(8);
        for (SkuAttrRefDO skuAttrRefDo : skuAttrList) {
            final List<SkuAttrRefDO> varList = attrMap.getOrDefault(skuAttrRefDo.getSkuId(), new LinkedList<>());
            varList.add(skuAttrRefDo);
            attrMap.put(skuAttrRefDo.getSkuId(), varList);
        }
        return attrMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<Boolean> save(CombinationItemSaveCmd cmd) {
        log.info("CombinationItemBizService save param:{}", cmd);
        combinationItemDomainService.save(cmd);
        return SingleResponse.of(true);
    }

    @Override
    public SingleResponse<CombinationItemWithPriceVO> get(Long id, Boolean isEdit) {
        final CombinationItem combinationItem = iCombinationItemService.getById(id);
        Assert.notNull(combinationItem, "组合商品id非法");

        // 完成基本映射
        CombinationItemWithPriceVO combinationItemVO = CombinationItemTransMapper.INSTANCE.dbToVoWithPrice(combinationItem);
        // 关于价格权限的特殊处理
        if (Objects.nonNull(isEdit) && isEdit) {
            SingleResponse<CombinationItemPriceVO> price = getPrice(id);
            CombinationItemPriceVO data = price.getData();
            combinationItemVO.setProcurementPrices(data.getProcurementPrices());
            combinationItemVO.setSalesPrices(data.getSalesPrices());
            combinationItemVO.setContractSalePrices(data.getContractSalePrices());
            combinationItemVO.setPlatformCommissions(data.getPlatformCommissions());

        } else {
            // null在这里有业务上的语义，因为PM要求，编辑点开详情查询回填的时候页面也要看到价格，无视价格查看的权限,展示全部信息
            // 想在上一层页面（分页展示页面） 做查看价格的权限控制（编辑按钮展示控制）。但是详情页面又要做权限控制，只能接口层面做页面区分。
            combinationItemVO.setSalesPrices(new LinkedList<>());
            combinationItemVO.setProcurementPrices(new LinkedList<>());
            combinationItemVO.setContractSalePrices(new LinkedList<>());
            combinationItemVO.setPlatformCommissions(new LinkedList<>());
        }

        // 查询此数组商品的sku构成
        QueryWrapper<ComposeSku> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ComposeSku::getCombinationId, id);
        final List<ComposeSku> composeSkuList = iComposeSkuService.list(queryWrapper);
        if (CollUtil.isEmpty(composeSkuList)) {
            combinationItemVO.setComposeSkuList(new LinkedList<>());
        } else {
            // 此组合商品存在sku构成，查询构成sku的信息。
            final List<String> skuCodeList = composeSkuList.stream().map(ComposeSku::getSkuCode).collect(Collectors.toList());
            ComposeSkuPageQuery query = new ComposeSkuPageQuery();
            query.setSkuCodeList(skuCodeList);
            query.setWithPrice(true);
            query.setPageSize(1);
            query.setPageSize(9999);
            final PageResponse<ComposeSkuVO> response = pageSku(query);
            List<ComposeSkuVO> composeSkuVoList = response.getData();
            // 可能skuCode在单品表存在，但是在sku表中不存在。
            if (CollUtil.isEmpty(composeSkuVoList)) {
                combinationItemVO.setComposeSkuList(new LinkedList<>());
            } else {
                // 填充单品构成数量，两个金额占比
                Map<String, ComposeSku> skuCountMap = composeSkuList.stream().collect(Collectors.toMap(ComposeSku::getSkuCode, v -> v));
                composeSkuVoList.forEach(val -> {
                    // 系统skuCode取值失败，就用特殊skuCode
                    ComposeSku composeSku = skuCountMap.get(val.getSkuCode());
                    if (Objects.isNull(composeSku)) {
                        composeSku = skuCountMap.get(val.getSpecialSkuCode());
                    }

                    if (Objects.nonNull(composeSku)) {
                        val.setCount(composeSku.getCount());
                        val.setCostProportion(composeSku.getCostProportion());
                        val.setSalesProportion(composeSku.getSaleProportion());
                    }
                    if (StringUtils.hasText(val.getSpecialSkuCode())) {
                        val.setSkuCode(val.getSpecialSkuCode());
                    }
                });
                combinationItemVO.setComposeSkuList(composeSkuVoList);
            }
        }

        // 填充创建者和更新者。
        List<Long> uIdList = new LinkedList<>();
        if (Objects.nonNull(combinationItemVO.getCreatedUid()) && 0 != combinationItemVO.getCreatedUid()) {
            uIdList.add(combinationItemVO.getCreatedUid());
        }
        if (Objects.nonNull(combinationItemVO.getUpdatedUid()) && 0 != combinationItemVO.getUpdatedUid()) {
            uIdList.add(combinationItemVO.getUpdatedUid());
        }
        final Map<Long, StaffInfo> longStaffInfoMap = userGateway.batchQueryStaffInfoByIds(uIdList);
        final StaffInfo createdInfo = longStaffInfoMap.getOrDefault(combinationItemVO.getCreatedUid(), null);
        combinationItemVO.setCreatedName(Objects.isNull(createdInfo) ? "" : createdInfo.getNickname());
        final StaffInfo updatedInfo = longStaffInfoMap.getOrDefault(combinationItemVO.getUpdatedUid(), null);
        combinationItemVO.setUpdatedName(Objects.isNull(updatedInfo) ? "" : updatedInfo.getNickname());

        final List<BizLevelDivision> bizLevelDivisions = iBizLevelDivisionService.selectByTypeAndBizId(BizUnionTypeEnum.COMBINATION,
                id);
        combinationItemVO.setCorpType(BizLevelDivisionConvert.INSTANCE.toBizLevelIntegerList(bizLevelDivisions,
                DivisionLevelEnum.COOPERATION));
        combinationItemVO.setBizType(BizLevelDivisionConvert.INSTANCE.toBizLevelIntegerList(bizLevelDivisions,
                DivisionLevelEnum.BUSINESS_TYPE));

        IProviderService providerService = SpringUtil.getBean(IProviderService.class);
        final Long providerId = combinationItem.getProviderId();
        combinationItemVO.setProviderId(providerId);
        if (NumberUtil.isPositive(providerId)) {
            final Provider provider = providerService.getById(providerId);
            combinationItemVO.setProviderName(provider.getName());
        }
        return SingleResponse.of(combinationItemVO);
    }

    @Autowired
    OperateLogGateway operateLogGateway;

    @Override
    public SingleResponse<Boolean> delete(Long id) {

        CombinationItem combinationItem = iCombinationItemService.getById(id);
        String code = combinationItem.getCode();
        if (StringUtil.isNotBlank(code)) {
            LambdaQueryWrapper<PlatformItem> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(PlatformItem::getItemCode, code);
            List<PlatformItem> list = iPlatformItemService.list(queryWrapper);
            boolean haveRelationWithPlatformItem = CollUtil.isNotEmpty(list);
            if (haveRelationWithPlatformItem) {
                String platformGoodNames = list.stream().map(PlatformItem::getGoodsName).collect(Collectors.joining(","));
                throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "此组合商品存在和平台商品的联系，无法删除。平台商品：" + platformGoodNames);
            }
        }

        iCombinationItemService.removeByIdWithTime(id);

        operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.COMBINATION_ITEM,
                id, "删除组合装商品", null);

        return SingleResponse.of(true);
    }


    @Override
    public SingleResponse<CombinationItemPriceVO> getPrice(Long id) {
        final CombinationItem combinationItem = iCombinationItemService.getById(id);
        Assert.notNull(combinationItem, "组合商品id非法");
        CombinationItemPriceVO vo = new CombinationItemPriceVO();

        OneTypePriceVO vo1 = new OneTypePriceVO();
        vo1.setPrice(combinationItem.getProcurementPrice());
        vo1.setPriceStart("2022-01-01 00:00:00");
        vo1.setId(0L);
        List<OneTypePriceVO> list1 = new LinkedList<>();
        list1.add(vo1);

        OneTypePriceVO vo2 = new OneTypePriceVO();
        vo2.setPrice(combinationItem.getSalesPrice());
        vo2.setPriceStart("2022-01-01 00:00:00");
        vo2.setId(0L);
        List<OneTypePriceVO> list2 = new LinkedList<>();
        list2.add(vo2);

        OneTypePriceVO vo3 = new OneTypePriceVO();
        vo3.setPrice(combinationItem.getSalesPrice());
        vo3.setPriceStart("2022-01-01 00:00:00");
        vo3.setId(0L);
        List<OneTypePriceVO> list3 = new LinkedList<>();
        list3.add(vo3);

        OneTypePriceVO vo4 = new OneTypePriceVO();
        vo4.setPrice(combinationItem.getSalesPrice());
        vo4.setPriceStart("2022-01-01 00:00:00");
        vo4.setId(0L);
        List<OneTypePriceVO> list4 = new LinkedList<>();
        list4.add(vo4);

        List<ItemSkuPrice> list = iItemSkuPriceService.lambdaQuery().eq(ItemSkuPrice::getSkuCode, combinationItem.getCode())
                .select().list();
        if (CollUtil.isNotEmpty(list)) {
            List<OneTypePriceVO> costList = list.stream().filter(val -> 0 == val.getType())
                    .map(val -> {
                        OneTypePriceVO one = new OneTypePriceVO();
                        one.setPrice(val.getPrice());
                        one.setPriceStart(DateUtil.parseTimeStamp(val.getStartTime(), DateUtil.DEFAULT_FORMAT));
                        one.setId(val.getId());
                        return one;
                    }).collect(Collectors.toList());
            list1.addAll(costList);

            List<OneTypePriceVO> salesList = list.stream().filter(val -> 1 == val.getType())
                    .map(val -> {
                        OneTypePriceVO one = new OneTypePriceVO();
                        one.setPrice(val.getPrice());
                        one.setPriceStart(DateUtil.parseTimeStamp(val.getStartTime(), DateUtil.DEFAULT_FORMAT));
                        one.setId(val.getId());
                        return one;
                    }).collect(Collectors.toList());
            list2.addAll(salesList);

            list3.addAll(list.stream().filter(val -> 4 == val.getType())
                    .map(val -> {
                        OneTypePriceVO one = new OneTypePriceVO();
                        one.setPrice(val.getPrice());
                        one.setPriceStart(DateUtil.parseTimeStamp(val.getStartTime(), DateUtil.DEFAULT_FORMAT));
                        one.setId(val.getId());
                        return one;
                    }).collect(Collectors.toList()));

            list4.addAll(list.stream().filter(val -> 5 == val.getType())
                    .map(val -> {
                        OneTypePriceVO one = new OneTypePriceVO();
                        one.setPrice(val.getPrice());
                        one.setPriceStart(DateUtil.parseTimeStamp(val.getStartTime(), DateUtil.DEFAULT_FORMAT));
                        one.setId(val.getId());
                        return one;
                    }).collect(Collectors.toList()));

        }

        vo.setProcurementPrices(list1.stream().sorted(Comparator.comparing(OneTypePriceVO::getId).reversed()).collect(Collectors.toList()));
        vo.setSalesPrices(list2.stream().sorted(Comparator.comparing(OneTypePriceVO::getId).reversed()).collect(Collectors.toList()));
        vo.setContractSalePrices(list3.stream().sorted(Comparator.comparing(OneTypePriceVO::getId).reversed()).collect(Collectors.toList()));
        vo.setPlatformCommissions(list4.stream().sorted(Comparator.comparing(OneTypePriceVO::getId).reversed()).collect(Collectors.toList()));

        return SingleResponse.of(vo);
    }

    @Override
    public PageResponse<CombinationPageVO> pageItem(CombinationItemPageQuery query) {
        query.setCorpType(UserPermissionJudge.filterCorpType(query.getCorpType()));
        if (StringUtil.isNotBlank(query.getCode())) {
            query.setCode(query.getCode().toUpperCase());
        }

//         兼容sku指定编码查询
        String skuCode = query.getSkuCode();
        if (StringUtils.hasText(skuCode)) {
            ItemSku itemSku = itemSkuGateway.getBySkuCode(skuCode);
            if (Objects.nonNull(itemSku)) {
                query.setSkuCodes(ListUtil.of(itemSku.getSkuCode(), itemSku.getProviderSpecifiedCode()));
            } else {
                return PageResponse.of(new LinkedList<>(), 0, query.getPageSize(), query.getPageIndex());
            }
        }

        final List<CombinationDO> combinationDoList = iCombinationItemService.listItem(query);
        final List<Long> userIdList = combinationDoList.stream().map(CombinationDO::getCreatedUid).distinct().collect(Collectors.toList());
        final Map<Long, StaffInfo> longStaffInfoMap = userGateway.batchQueryStaffInfoByIds(userIdList);

        final List<Long> combinationIds = combinationDoList.stream().map(CombinationDO::getId).collect(Collectors.toList());
        final List<BizLevelDivision> bizLevelDivisions = bizLevelDivisionService.selectByTypeAndBizIds(BizUnionTypeEnum.COMBINATION,
                combinationIds);

        final List<CombinationPageVO> combinationPageVOList = combinationDoList.stream().map(combinationDO -> {
            final CombinationPageVO combinationPageVO = CombinationItemTransMapper.INSTANCE.doToPageVo(combinationDO);
            combinationPageVO.setSkuShortList(iComposeSkuService.getShortVo(combinationPageVO.getId()));
            final StaffInfo staffInfo = longStaffInfoMap.get(combinationDO.getCreatedUid());
            String name = Objects.isNull(staffInfo) ? "" : staffInfo.getNickname();
            combinationPageVO.setCreateName(name);
            combinationPageVO.setBusinessLine(combinationDO.getBusinessLine());
            final List<BizLevelDivision> theBizLevelDivisions = bizLevelDivisions
                    .stream()
                    .filter(biz -> Objects.equals(combinationDO.getId(), biz.getBizId()))
                    .collect(Collectors.toList());
            combinationPageVO.setCorpType(BizLevelDivisionConvert.INSTANCE.toBizLevelIntegerList(theBizLevelDivisions,
                    DivisionLevelEnum.COOPERATION));
            combinationPageVO.setBizType(BizLevelDivisionConvert.INSTANCE.toBizLevelIntegerList(theBizLevelDivisions,
                    DivisionLevelEnum.BUSINESS_TYPE));
            return combinationPageVO;
        }).collect(Collectors.toList());

        final Integer count = iCombinationItemService.countListItem(query);
        return PageResponse.of(combinationPageVOList, count, query.getPageSize(), query.getPageIndex());
    }

    @Override
    public PageResponse<CombinationItemNameVO> pageItemName(CombinationItemNamePageQuery pageQuery) {
        QueryWrapper<CombinationItem> wrapper = new QueryWrapper<>();
        wrapper.lambda().like(CombinationItem::getName, pageQuery.getName());
        IPage<CombinationItem> iPage = new Page<>(pageQuery.getPageIndex(), pageQuery.getPageSize());
        final IPage<CombinationItem> page = BizDivisionContext.invoke(BizUnionTypeEnum.COMBINATION,
                () -> iCombinationItemService.page(iPage,
                        wrapper));
        final List<CombinationItemNameVO> combinationItemNameList = CombinationItemTransMapper.INSTANCE.dbToNameVos(page.getRecords());
        return PageResponse.of(combinationItemNameList, (int) page.getTotal(), pageQuery.getPageSize(), pageQuery.getPageIndex());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void export(CombinationItemPageQuery query) {
        query.setCorpType(UserPermissionJudge.filterCorpType(query.getCorpType()));
        boolean canViewComposerSkuPrice = UserPermissionJudge.canViewComposerSkuPrice();
        boolean canViewCombinationItemPrice = UserPermissionJudge.canViewCombinationItemPrice();
        Class<? extends ExportSheet> sheetClass = getSheetClass(canViewComposerSkuPrice, canViewCombinationItemPrice);
        Function<T, ExportSheet> exportFunc = getExportFunc(canViewComposerSkuPrice, canViewCombinationItemPrice);

        ExportTask exportTask = ExportEntityPlus.save(ExportTaskType.COMBINATION_ITEM);
        exportDomainService.export(query, exportTask, ExportType.COMBINATION_ITEM, sheetClass, exportFunc);
    }

    private Class<? extends ExportSheet> getSheetClass(Boolean canViewComposerSkuPrice, boolean canViewCombinationItemPrice) {
        if (!canViewComposerSkuPrice && !canViewCombinationItemPrice) {
            return ComposeSkuBaseSheet.class;
        }
        if (canViewComposerSkuPrice && canViewCombinationItemPrice) {
            return ComposerSkuAllSheet.class;
        }
        if (canViewComposerSkuPrice) {
            return ComposerSkuWithSkuPriceSheet.class;
        }
        return ComposerSkuWithItemPriceSheet.class;
    }

    private <t> Function<t, ExportSheet> getExportFunc(Boolean canViewComposerSkuPrice, boolean canViewCombinationItemPrice) {

        if (!canViewComposerSkuPrice && !canViewCombinationItemPrice) {
            return t -> {
                ComposerSkuAllSheet skuAllSheet = (ComposerSkuAllSheet) t;
                return CombinationItemTransMapper.INSTANCE.allSheetToBaseSheet(skuAllSheet);
            };
        }
        if (canViewComposerSkuPrice && canViewCombinationItemPrice) {
            return t -> {
                return (ComposerSkuAllSheet) t;
            };
        }
        if (canViewComposerSkuPrice) {
            return t -> {
                ComposerSkuAllSheet skuAllSheet = (ComposerSkuAllSheet) t;
                return CombinationItemTransMapper.INSTANCE.allSheetToSkuPriceSheet(skuAllSheet);
            };
        }
        return t -> {
            ComposerSkuAllSheet skuAllSheet = (ComposerSkuAllSheet) t;
            return CombinationItemTransMapper.INSTANCE.allSheetToItemPriceSheet(skuAllSheet);
        };
    }

    @Override
    public SingleResponse<Map<String, BigDecimal>> calculatePrice(List<ComposeSkuCmd> list) {
        Map<String, BigDecimal> map = new HashMap<>(2);

        Map<Long, Integer> reqSkuIdCountMap = list.stream().collect(Collectors.toMap(ComposeSkuCmd::getSkuId, ComposeSkuCmd::getCount));
        CombinationPriceEntity entity = new CombinationPriceEntity(reqSkuIdCountMap, iItemSkuService);

        map.put("procurement", entity.getProcurementPrice());
        map.put("sales", entity.getSalesPrice());

        return SingleResponse.of(map);
    }

    private List<ProportionVO> calculateProportion0(List<ProportionCmd> cmd) {
        BigDecimal saleTotal = cmd.stream().map(proportionCmd ->
                        proportionCmd.getSalePrice().multiply(new BigDecimal(proportionCmd.getCount())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal costTotal = cmd.stream().map(proportionCmd ->
                        proportionCmd.getCostPrice().multiply(new BigDecimal(proportionCmd.getCount())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        List<ProportionCmd> cSaleList = cmd.stream().filter(val -> BigDecimal.ZERO.compareTo(val.getSalePrice()) != 0)
                .collect(Collectors.toList());
        Map<String, BigDecimal> saleMap = workedPropertionMap(cSaleList, saleTotal, 1);
        boolean costConvertSales = CollUtil.isEmpty(cSaleList);

        List<ProportionCmd> cCostList = cmd.stream().filter(val -> BigDecimal.ZERO.compareTo(val.getCostPrice()) != 0)
                .collect(Collectors.toList());
        Map<String, BigDecimal> costMap = workedPropertionMap(cCostList, costTotal, 2);

        return cmd.stream().map(val -> {
            ProportionVO vo = new ProportionVO();
            vo.setSkuCode(val.getSkuCode());
            vo.setCostProportion(costMap.getOrDefault(val.getSkuCode(), BigDecimal.ZERO));
            if (costConvertSales) {
                vo.setSalesProportion(vo.getCostProportion());
            } else {
                vo.setSalesProportion(saleMap.getOrDefault(val.getSkuCode(), BigDecimal.ZERO));
            }
            return vo;
        }).collect(Collectors.toList());
    }

    private Map<String, BigDecimal> workedPropertionMap(List<ProportionCmd> withPriceList, BigDecimal total, Integer type) {
        boolean isTotalZero = BigDecimal.ZERO.compareTo(total) == 0;
        BigDecimal eachResTotal = BigDecimal.ZERO;
        Map<String, BigDecimal> resMap = new HashMap<>(withPriceList.size());

        if (CollUtil.isEmpty(withPriceList)) {
            return resMap;
        }
        for (int i = 0; i < withPriceList.size() - 1; i++) {
            ProportionCmd cmd = withPriceList.get(i);
            if (isTotalZero) {
                resMap.put(cmd.getSkuCode(), BigDecimal.ZERO);
                eachResTotal = eachResTotal.add(BigDecimal.ZERO);
            } else {
                BigDecimal u;
                if (type == 1) {
                    u = cmd.getSalePrice().multiply(new BigDecimal(cmd.getCount()));
                } else if (type == 2) {
                    u = cmd.getCostPrice().multiply(new BigDecimal(cmd.getCount()));
                } else {
                    u = BigDecimal.ZERO;
                }
                BigDecimal res = u.divide(total, 6, RoundingMode.DOWN);
                resMap.put(cmd.getSkuCode(), res);
                eachResTotal = eachResTotal.add(res);
            }
        }
        ProportionCmd lastOne = withPriceList.get(withPriceList.size() - 1);
        resMap.put(lastOne.getSkuCode(), BigDecimal.ONE.subtract(eachResTotal));

        return resMap;
    }


    @Override
    public List<ProportionVO> calculateProportion(List<ProportionCmd> cmd) {
        return calculateProportion0(cmd);

    }


    @Override
    public void resetProportion(List<ResetProportionBO> resetProportionBOList) {
        log.info("combinationItemBizService resetProportion,skuCode:{}", resetProportionBOList);
        List<String> resetProportionSkuCodes = resetProportionBOList.stream().map(ResetProportionBO::getSkuCode).collect(Collectors.toList());
        List<ComposeSkuWithPriceDO> composeSkuList = combinationItemMapper.selectSameGroupSkuBySkuCode(resetProportionSkuCodes);
        if (CollUtil.isEmpty(composeSkuList)) {
            return;
        }
        // 根据组合装id进行分割
        Map<Long, List<ComposeSkuWithPriceDO>> groupByCombinationIdMap = composeSkuList.stream().collect(Collectors.groupingBy(ComposeSkuWithPriceDO::getCombinationId));
        Map<String, ResetProportionBO> paramBoMap = resetProportionBOList.stream().collect(Collectors.toMap(ResetProportionBO::getSkuCode, v -> v));

        groupByCombinationIdMap.forEach((combinationId, skuList) -> {
            // 重新计算sku的金额占比。
            List<ProportionCmd> paramList = skuList.stream().map(val -> {
                ProportionCmd cmd = new ProportionCmd();
                cmd.setSkuCode(val.getSkuCode());
                cmd.setCount(val.getCount());
                ResetProportionBO bo = paramBoMap.get(val.getSkuCode());
                if (Objects.nonNull(bo)) {
                    cmd.setCostPrice(bo.getNewCostPrice());
                    cmd.setSalePrice(bo.getNewSalesPrice());
                } else {
                    cmd.setCostPrice(val.getCostPrice());
                    cmd.setSalePrice(val.getSalePrice());
                }
                return cmd;
            }).collect(Collectors.toList());
            List<ProportionVO> proportionResultList = calculateProportion(paramList);

            // -------------- 记录日志，做组合装数据更新 -----------
            Map<String, ComposeSkuWithPriceDO> composeSkuMap = skuList.stream().collect(Collectors.toMap(ComposeSkuWithPriceDO::getSkuCode, v -> v));
            List<String> logList = new LinkedList<>();
            // 根据计算结果，更新组合装信息
            proportionResultList.forEach(newResult -> {
                ComposeSkuWithPriceDO oldInfoDO = composeSkuMap.get(newResult.getSkuCode());
                // 成本占比或者销售占比发生变化，就更新。
                BigDecimal oldCost = Objects.isNull(oldInfoDO.getCostProportion()) ? BigDecimal.ZERO : oldInfoDO.getCostProportion();
                BigDecimal oldSales = Objects.isNull(oldInfoDO.getSaleProportion()) ? BigDecimal.ZERO : oldInfoDO.getSaleProportion();
                boolean needUpdate = false;
                if (oldCost.compareTo(newResult.getCostProportion()) != 0) {
                    ResetProportionBO orDefault = paramBoMap.getOrDefault(newResult.getSkuCode(), null);
                    String logStr;
                    if (Objects.nonNull(orDefault)) {
                        String ss = orDefault.getOldCostPrice() + " -> " + orDefault.getNewCostPrice();
                        logStr = "sku：" + newResult.getSkuCode() + "发生了成本价格变动，" + ss + "，占比：" + oldCost + " -> " + newResult.getCostProportion();
                    } else {
                        logStr = "sku：" + newResult.getSkuCode() + "发生了成本价格变动，占比：" + oldCost + " -> " + newResult.getCostProportion();
                    }
                    logList.add(logStr);
                    needUpdate = true;
                }
                if (oldSales.compareTo(newResult.getSalesProportion()) != 0) {
                    ResetProportionBO orDefault = paramBoMap.getOrDefault(newResult.getSkuCode(), null);
                    String logStr;
                    if (Objects.nonNull(orDefault)) {
                        String ss = orDefault.getOldSalesPrice() + " -> " + orDefault.getNewSalesPrice();
                        logStr = "sku：" + newResult.getSkuCode() + "发生了销售价格变动，" + ss + ",占比：" + oldSales + " -> " + newResult.getSalesProportion();
                    } else {
                        logStr = "sku：" + newResult.getSkuCode() + "发生了销售价格变动，占比：" + oldSales + " -> " + newResult.getSalesProportion();
                    }
                    logList.add(logStr);
                    needUpdate = true;
                }
                if (needUpdate) {
                    combinationItemMapper.updateSkuProportion(newResult.getCostProportion(), newResult.getSalesProportion()
                            , newResult.getSkuCode(), combinationId);
                }
            });

            // 添加系统自动刷新金额占比的日志
            if (CollUtil.isNotEmpty(logList)) {
                operateLogGateway.addOperatorLog(0L, OperateLogTarget.COMBINATION_ITEM, combinationId
                        , String.join("。", logList), null);
            }
        });
    }

    /**
     * 按照原本的逻辑，如果价格金额是0，那么前N-1个单品的金额占比是0，最后一个单品金额占比是1，
     * 针对销售金额占比，如果出现上述情况，直接copy成本金额占比，因为业务只维护了成本价，几乎不维护销售价。
     *
     * @param proportionResultList
     */
    private List<ProportionVO> saleProportionZeroHandler(List<ProportionVO> proportionResultList) {
        long count = proportionResultList.stream().filter(val -> val.getSalesProportion().compareTo(BigDecimal.ZERO) == 0).count();
        boolean b = count == proportionResultList.size() - 1;
        long count1 = proportionResultList.stream().filter(val -> val.getSalesProportion().compareTo(BigDecimal.ONE) == 0).count();
        boolean b1 = count1 == 1;

        if (b && b1) {
            proportionResultList.forEach(val -> val.setSalesProportion(val.getCostProportion()));
        }
        return proportionResultList;
    }

    @Override
    public Map<String, List<String>> getRepeatCode() {
        return null;
    }

    @Override
    public Integer getBusinessLine(Long id) {
        final List<Integer> levelValList = iBizLevelDivisionService.lambdaQuery()
                .eq(BizLevelDivision::getType, BizUnionTypeEnum.COMBINATION)
                .eq(BizLevelDivision::getBizId, id)
                .eq(BizLevelDivision::getLevel, DivisionLevelEnum.COOPERATION)
                .list().stream().map(val -> val.getLevelVal().getValue()).distinct().collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(levelValList)) {
            if (levelValList.size() > 1) {
                return 0;
            }
            return levelValList.get(0);
        }
        return 0;
    }
}
