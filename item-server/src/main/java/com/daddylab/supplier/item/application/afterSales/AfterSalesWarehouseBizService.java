package com.daddylab.supplier.item.application.afterSales;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.daddylab.supplier.item.types.warehouse.EditWarehouseAfterSalesAddressCmd;

import java.io.InputStream;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 售后管理/退货仓库服务
 *
 * <AUTHOR>
 * @since 2022/10/20
 */
public interface AfterSalesWarehouseBizService {

    /**
     * 售后退货仓库导入
     *
     * @param inputStream 上传文件流
     * @return 响应结果
     */
    Response importExcel(InputStream inputStream);

    /**
     * 获取P系统供应商ID对应的退货仓库信息
     *
     * @param partnerProviderId P系统供应商ID
     * @return 退货仓库编号
     */
    MultiResponse<WarehouseAfterSalesAddressVO> getPartnerProviderReturnWarehouseAddressVOs(
            Long partnerProviderId);

    Optional<WarehouseAfterSalesAddressVO> getWarehouseAfterSalesAddressVO(String warehouseNo);

    List<WarehouseAfterSalesAddressVO> getWarehouseAfterSalesAddressList(String warehouseNo);

    List<WarehouseAfterSalesAddressVO> getWarehouseAfterSalesAddressVO(
            Collection<String> warehouseNos);

    boolean isPartnerProviderWarehouse(
            Long partnerProviderId, String warehouseNo);


    Response editWarehouseAfterSalesInfo(EditWarehouseAfterSalesAddressCmd cmd);

    Response editWarehouseAfterSalesInfoList(Warehouse warehouse, List<Long> deleteIds, List<WarehouseAfterSalesAddressVO> cmdList);

    Map<String, List<String>> queryWarehouseAfterSaleInfo(List<String> warehouseNos);


}
