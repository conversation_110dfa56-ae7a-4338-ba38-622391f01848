package com.daddylab.supplier.item.domain.stockInOrder.dto;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/25 18:24
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("查询采购入库单参数模型")
public class StockInOrderQuery extends PageQuery {
    /**
     * 入库单号
     **/
    @ApiModelProperty(value = "入库单号")
    private String no;
    /**
     * 供应商id
     **/
    @ApiModelProperty(value = "供应商id")
    private Long providerId;
    /**
     * 收料组织id
     **/
    @ApiModelProperty(value = "收料组织id")
    private Long organizationId;
    /**
     * 采购单号
     **/
    @ApiModelProperty(value = "采购单号")
    private String purchaseOrderNo;
    /**
     * 入库状态
     **/
    @ApiModelProperty(value = "入库状态")
    private Integer state;
    /**
     * 入库日期
     **/
    @ApiModelProperty(value = "入库开始日期")
    private Long receiptTimeStart;
    @ApiModelProperty(value = "入库结束日期")
    private Long receiptTimeEnd;
    /**
     * 商品sku
     **/
    @ApiModelProperty(value = "商品sku")
    private String itemSkuCode;
    /**
     * 商品名称
     **/
    @ApiModelProperty(value = "商品名称")
    private String itemName;
    /**
     * 采购员
     **/
    @ApiModelProperty(value = "采购员")
    private Long buyerUserId;
    /**
     * 采购组
     **/
    @ApiModelProperty(value = "采购组")
    private Long purchaseOrganizationId;
    /**
     * 入库仓库
     **/
    @ApiModelProperty(value = "入库仓库")
    private String warehouseNo;
    /**
     * 明细状态
     **/
    @ApiModelProperty(value = "明细模式")
    private Boolean isDetailMode;

    private List<Long> createdUidList;
    private List<Long> neCreatedUidList;


    private Integer offsetVal;

    @ApiModelProperty(value = "合作模式。0:电商。1:老爸抽检。2:绿色家装。3:商家入驻")
    private List<Integer> businessLine;


}
