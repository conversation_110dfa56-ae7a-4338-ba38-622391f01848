package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 旺店通退换单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtRefundOrderDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 退换单号
     */
    private String refundNo;

    /**
     * 明细唯一键
     */
    private Integer recId;

    /**
     * 原始子单号
     */
    private String oid;

    /**
     * 订单id
     */
    private Integer tradeOrderId;

    /**
     * 平台id，见附件
     */
    private Integer platformId;

    /**
     * 原始单号
     */
    private String tid;

    /**
     * 系统订单编号
     */
    private String tradeNo;

    /**
     * 实际发出数量
     */
    private BigDecimal num;

    /**
     * 价格, 系统订单明细分摊后价格
     */
    private BigDecimal price;

    /**
     * 原价, 系统订单明细成交价
     */
    private BigDecimal originalPrice;

    /**
     * 退款数量
     */
    private BigDecimal refundNum;

    /**
     * 退款货品总额, 系统订单明细的分摊后金额
     */
    private BigDecimal totalAmount;

    /**
     * 已经退款的总金额
     */
    private BigDecimal refundAmount;

    /**
     * 是否担保退款
     */
    private Boolean isGuarantee;

    /**
     * 货品编号
     */
    private String goodsNo;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 商家编码
     */
    private String specNo;

    /**
     * 平台货品id
     */
    private String goodsId;

    /**
     * 平台规格id
     */
    private String specId;

    /**
     * 系统货品id（旺店通货品档案中货品维度自增生成的id）
     */
    private Integer sysGoodsId;

    /**
     * 系统规格id（旺店通货品档案中单品维度自增生成的id）
     */
    private Integer sysSpecId;

    /**
     * 规格码
     */
    private String specCode;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 退货入库数量
     */
    private BigDecimal stockinNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 平台规格名称
     */
    private String apiSpecName;

    /**
     * 平台货品名称
     */
    private String apiGoodsName;

    /**
     * 最后修改时间
     */
    private LocalDateTime modified;

    /**
     * 组合装编号（单据中若不包含组合装，该字段不返回）
     */
    private String suiteNo;

    /**
     * 组合装名称 （单据中若不包含组合装，该字段不返回）
     */
    private String suiteName;

    /**
     * 多个原始退款单号之间使用英文逗号隔开
     */
    private String rawRefundNos;

    /**
     * 删除时间
     */
    private Long deletedAt;

}
