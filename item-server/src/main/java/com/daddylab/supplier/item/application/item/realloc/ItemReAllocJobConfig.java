package com.daddylab.supplier.item.application.item.realloc;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/11/13
 */
@ConfigurationProperties(prefix = "item-realloc")
@Data
@Configuration
public class ItemReAllocJobConfig {
    /**
     * 不同合作模式对应的通知人
     */
    private Map<Integer, String> notifyUsers;
}
