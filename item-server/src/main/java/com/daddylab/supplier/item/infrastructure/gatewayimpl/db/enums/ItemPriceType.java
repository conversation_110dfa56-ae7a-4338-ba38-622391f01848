package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 价格类型 1:采购成本 2:日常销售价 3:划线价格 4:产品活动价 5:渠道最低价 6:自定义价格
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13 3:18 下午
 * @description
 */
@Getter
@AllArgsConstructor
public enum ItemPriceType implements IEnum {

    /**
     * 采购成本
     */
    PROCUREMENT(0, "采购成本"),

    /**
     * 日常售价
     */
    DAILY(1, "产品日销价"),

    /**
     * 划线价格
     */
    LINE(2, "划线价格"),

    ACTIVITY(3, "活动价格"),

    CHANNEL_LOWEST(4, "渠道最低价"),

    CUSTOMIZE(5, "自定义价格");


    @EnumValue
    private final Integer value;

    private final String desc;

}
