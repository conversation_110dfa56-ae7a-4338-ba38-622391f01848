package com.daddylab.supplier.item.application.item.combinationItem.dto.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2022年11月04日 3:23 PM
 */
@ApiModel("组合装-金额占比计算返回封装")
@Data
public class ProportionVO {

    @ApiModelProperty("skuCode")
    private String skuCode;

    @ApiModelProperty("成本金额占比")
    private BigDecimal costProportion;

    @ApiModelProperty("销售金额占比")
    private BigDecimal salesProportion;

}
