package com.daddylab.supplier.item.application.recognitionTask;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAudit;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerRecognitionTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerModuleAuditService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerRecognitionTaskService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerReviewThesaurusAPI;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ReqData;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ReviewAddHitNumParam;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ReviewThesaurus;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.infrastructure.separator.CommonSeparatorTemplate;
import com.daddylab.supplier.item.infrastructure.timing.StopWatch;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.recognitionTask.ItemDrawerRecognitionHit;
import com.daddylab.supplier.item.types.recognitionTask.ItemDrawerRecognitionHitLevel;
import com.daddylab.supplier.item.types.recognitionTask.ItemDrawerRecognitionTaskStatus;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.*;
import java.util.Map.Entry;

/**
 * 商品上新抽屉敏感词识别任务
 *
 * <AUTHOR>
 * @since 2022/12/5
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ItemRecognitionKeywordTask {

    private final IItemDrawerRecognitionTaskService itemDrawerRecognitionTaskService;
    private final IItemDrawerModuleAuditService itemDrawerModuleAuditService;
    private final CommonSeparatorTemplate commonSeparatorTemplate;
    private final PartnerReviewThesaurusAPI partnerReviewThesaurusAPI;
    private final IItemService itemService;

    @XxlJob("ItemRecognitionKeywordTask")
    public void doTask() throws InterruptedException {
        final List<ItemDrawerModuleAudit> auditList =
                itemDrawerModuleAuditService
                        .lambdaQuery()
                        .ne(ItemDrawerModuleAudit::getAuditStatus, ItemAuditStatus.NONE)
                        .eq(ItemDrawerModuleAudit::getRecognitionStatus, false)
                        .list();
        log.info("抽屉敏感词识别开始 total={}", auditList.size());
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        for (ItemDrawerModuleAudit itemDrawerModuleAudit : auditList) {
            final List<ItemDrawerRecognitionTask> itemDrawerRecognitionTasks =
                    itemDrawerRecognitionTaskService
                            .lambdaQuery()
                            .eq(
                                    ItemDrawerRecognitionTask::getItemId,
                                    itemDrawerModuleAudit.getItemId())
                            .list();
            for (ItemDrawerRecognitionTask itemDrawerRecognitionTask : itemDrawerRecognitionTasks) {
                if (itemDrawerRecognitionTask.getStatus()
                        != ItemDrawerRecognitionTaskStatus.WAIT_CHECK) {
                    continue;
                }
                final Item item = itemService.getById(itemDrawerRecognitionTask.getItemId());
                if (item == null) {
                    log.error(
                            "【抽屉敏感词识别】异常，后端商品查询为空 taskId={} itemId={}",
                            itemDrawerRecognitionTask.getId(),
                            itemDrawerRecognitionTask.getItemId());
                    continue;
                }
                final Rsp<List<ReviewThesaurus>> reviewThesaurusesResp =
                        partnerReviewThesaurusAPI.reviewThesaurusAll(
                                item.getPartnerProviderItemSn());
                if (!reviewThesaurusesResp.getFlag()) {
                    log.error(
                            "【抽屉敏感词识别】审核词库查询异常 taskId={} err={}",
                            itemDrawerRecognitionTask.getId(),
                            reviewThesaurusesResp.getMsg());
                    continue;
                }
                final List<ReviewThesaurus> reviewThesauruses = reviewThesaurusesResp.getData();
                Map<String, Set<ReviewThesaurus>> keywordMap = Maps.newHashMap();
                for (ReviewThesaurus reviewThesaurus : reviewThesauruses) {
                    for (String keyword : reviewThesaurus.getContext()) {
                        keywordMap
                                .computeIfAbsent(keyword, k -> new LinkedHashSet<>())
                                .add(reviewThesaurus);
                    }
                }
                final StringJoiner logs =
                        new StringJoiner(
                                ";",
                                Optional.ofNullable(itemDrawerRecognitionTask.getLog()).orElse(""),
                                "");
                logs.add(DateUtil.formatNow());
                try {
                    final String content = itemDrawerRecognitionTask.getContent();
                    Set<String> keywordHits = commonSeparatorTemplate.segWithStopWords(content);
                    Map<Integer, Integer> addHitNumStat = Maps.newHashMap();
                    Set<ItemDrawerRecognitionHit> itemDrawerRecognitionHits =
                            Sets.newLinkedHashSet();
                    for (String keyword : keywordHits) {
                        final Set<ReviewThesaurus> thesauruses =
                                keywordMap.getOrDefault(keyword, Collections.emptySet());
                        for (ReviewThesaurus thesaurus : thesauruses) {
                            final ItemDrawerRecognitionHit recognitionHit =
                                    new ItemDrawerRecognitionHit();
                            recognitionHit.setHitLevel(
                                    thesaurus.getUseType() == 1
                                            ? ItemDrawerRecognitionHitLevel.LIMIT
                                            : ItemDrawerRecognitionHitLevel.FORBID);
                            recognitionHit.setKeyword(keyword);
                            recognitionHit.setEntryName(thesaurus.getName());
                            recognitionHit.setEntryCategory(thesaurus.getCategory());
                            itemDrawerRecognitionHits.add(recognitionHit);
                            addHitNumStat.compute(
                                    thesaurus.getId(), (k, v) -> v == null ? 1 : v + 1);
                        }
                    }
                    updateTask(
                            itemDrawerRecognitionTask,
                            JsonUtil.toJson(itemDrawerRecognitionHits),
                            ItemDrawerRecognitionTaskStatus.FINISHED,
                            logs.toString());
                    if (!addHitNumStat.isEmpty()) {
                        final ArrayList<ReviewAddHitNumParam> reviewAddHitNumParams =
                                Lists.newArrayList();
                        for (Entry<Integer, Integer> entry : addHitNumStat.entrySet()) {
                            final ReviewAddHitNumParam reviewAddHitNumParam =
                                    new ReviewAddHitNumParam();
                            reviewAddHitNumParam.setId(entry.getKey());
                            reviewAddHitNumParam.setHitNum(entry.getValue());
                            reviewAddHitNumParams.add(reviewAddHitNumParam);
                        }
                        partnerReviewThesaurusAPI.reviewAddHitNum(
                                new ReqData<>(reviewAddHitNumParams));
                    }
                } catch (Exception e) {
                    logs.add("敏感词识别异常:" + e.getMessage());
                    log.error(
                            "敏感词识别异常:{} taskId={}",
                            e.getMessage(),
                            itemDrawerRecognitionTask.getId(),
                            e);
                    updateTask(
                            itemDrawerRecognitionTask,
                            null,
                            ItemDrawerRecognitionTaskStatus.FAIL,
                            logs.toString());
                }
            }
            if (itemDrawerRecognitionTasks.stream()
                    .allMatch(
                            task ->
                                    task.getStatus() == ItemDrawerRecognitionTaskStatus.FINISHED
                                            || task.getStatus()
                                                    == ItemDrawerRecognitionTaskStatus.FAIL)) {
                updateAuditRecognitionStatus(itemDrawerModuleAudit);
            }
        }
        stopWatch.stop();
        log.info("抽屉敏感词识别完成 total={} time={}ms", auditList.size(), stopWatch.getTotalTimeMillis());
    }

    private void updateAuditRecognitionStatus(ItemDrawerModuleAudit itemDrawerModuleAudit) {
        itemDrawerModuleAuditService
                .lambdaUpdate()
                .set(ItemDrawerModuleAudit::getRecognitionStatus, true)
                .eq(ItemDrawerModuleAudit::getId, itemDrawerModuleAudit.getId())
                .update();
        Alert.text(
                MessageRobotCode.NOTICE,
                StringUtil.format("商品内容识别完成 商品ID:{}", itemDrawerModuleAudit.getItemId()));
    }

    private void updateTask(
            ItemDrawerRecognitionTask itemDrawerRecognitionTask,
            String hitResults,
            ItemDrawerRecognitionTaskStatus status,
            String msg) {
        itemDrawerRecognitionTask.setStatus(status);
        itemDrawerRecognitionTask.setHitResult(hitResults);
        itemDrawerRecognitionTask.setLog(msg);
        itemDrawerRecognitionTaskService
                .lambdaUpdate()
                .eq(ItemDrawerRecognitionTask::getId, itemDrawerRecognitionTask.getId())
                .set(ItemDrawerRecognitionTask::getStatus, status)
                .set(
                        StringUtil.isNotBlank(hitResults),
                        ItemDrawerRecognitionTask::getHitResult,
                        hitResults)
                .set(
                        !StringUtil.equals(msg, itemDrawerRecognitionTask.getLog()),
                        ItemDrawerRecognitionTask::getLog,
                        msg)
                .update();
    }
}
