package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DadPost;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DadPostMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDadPostService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 岗位表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
@DS("authDb")
@Service
public class DadPostServiceImpl extends DaddyServiceImpl<DadPostMapper, DadPost> implements IDadPostService {

}
