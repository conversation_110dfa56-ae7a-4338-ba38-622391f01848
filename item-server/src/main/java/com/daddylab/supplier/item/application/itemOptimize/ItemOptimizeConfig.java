package com.daddylab.supplier.item.application.itemOptimize;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/12/14
 *
 */
@Data
@ConfigurationProperties(prefix = "item-optimize")
@RefreshScope
@Configuration
public class ItemOptimizeConfig {
    private String processDefKey = "item_optimize_0.0.2";
}
