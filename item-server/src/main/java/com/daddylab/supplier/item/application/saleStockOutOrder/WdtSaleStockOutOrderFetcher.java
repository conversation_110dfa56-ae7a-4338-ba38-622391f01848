package com.daddylab.supplier.item.application.saleStockOutOrder;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.StockoutSalesAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutSalesQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutSalesQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutSalesQueryWithDetailResponse.Order.Details.PositionDetails;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.trans.TimeCompatibilityTransMapper;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrderDetails;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrderLogistics;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrderPositionDetails;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.google.common.collect.Lists;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/8/18
 */
@Component
@Slf4j
public class WdtSaleStockOutOrderFetcher implements PageFetcher {

    private final WdtGateway wdtGateway;
    private final WdtSaleStockOutOrderRepository wdtSaleStockOutOrderRepository;
    private final RefreshConfig refreshConfig;


    public WdtSaleStockOutOrderFetcher(WdtGateway wdtGateway,
            WdtSaleStockOutOrderRepository wdtSaleStockOutOrderRepository,
            RefreshConfig refreshConfig) {
        this.wdtGateway = wdtGateway;
        this.wdtSaleStockOutOrderRepository = wdtSaleStockOutOrderRepository;
        this.refreshConfig = refreshConfig;
    }

    @Override
    public int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
        return query(startTime, endTime, 1, 1, true).getTotalCount();
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, long pageIndex, long pageSize,
            RunContext runContext) {
        final Duration duration = LocalDateTimeUtil.between(startTime, endTime);
        if (duration.isNegative() || duration.isZero()
                || duration.compareTo(Duration.ofHours(1)) > 0) {
            throw new IllegalArgumentException("拉取时间最大范围为一个小时");
        }
        final StockoutSalesQueryWithDetailResponse response = query(startTime, endTime,
                ((int) pageSize),
                ((int) pageIndex), false);
        handleResponse(response);
    }

    public int[] getFetchWdtSaleStockOutOrderConfigs() {
        return StrUtil.splitToInt(
                Optional.ofNullable(refreshConfig.getFetchWdtSaleStockOutOrderConfigs())
                        .filter(StringUtil::isNotBlank).orElse("0")
                , ',');
    }

    private StockoutSalesQueryWithDetailResponse query(
            LocalDateTime startTime, LocalDateTime endTime, int pageSize, int pageNo,
            boolean calcTotal) {
        final StockoutSalesQueryWithDetailResponse response = new StockoutSalesQueryWithDetailResponse();
        AtomicInteger totalCount = new AtomicInteger();
        List<StockoutSalesQueryWithDetailResponse.Order> orders = Collections.synchronizedList(
                Lists.newArrayList());
        final int[] fetchWdtSaleStockOutOrderConfigs = getFetchWdtSaleStockOutOrderConfigs();
        CompletableFuture<?>[] futures = new CompletableFuture[fetchWdtSaleStockOutOrderConfigs.length];
        for (int i = 0; i < fetchWdtSaleStockOutOrderConfigs.length; i++) {
            final int fetchWdtSaleStockOutOrderConfig = fetchWdtSaleStockOutOrderConfigs[i];
            futures[i] = CompletableFuture.runAsync(() -> {
                try {
                    final Pager pager = new Pager(pageSize, pageNo - 1, calcTotal);
                    final StockoutSalesAPI api = wdtGateway.getQimenAPI(StockoutSalesAPI.class,
                            fetchWdtSaleStockOutOrderConfig, true);
                    final StockoutSalesQueryWithDetailParams params = new StockoutSalesQueryWithDetailParams();
                    params.setStartTime(DateUtil.format(startTime));
                    params.setEndTime(DateUtil.format(endTime));
                    params.setStatusType(3);
                    params.setStatus("5,10,50,51,52,53,54,58,60,61,63,65,70,73,75,77,79,90,110,-1");
                    final StockoutSalesQueryWithDetailResponse responseOnce = api.queryWithDetail(
                            params, pager);
                    if (CollectionUtil.isEmpty(responseOnce.getOrder())) {
                        return;
                    }
                    orders.addAll(responseOnce.getOrder());
                    totalCount.addAndGet(responseOnce.getTotalCount());
                } catch (Throwable e) {
                    throw WdtExceptions.wrapFetchException(e, fetchDataType());
                }
            }, ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL));
        }
        CompletableFuture.allOf(futures).join();
        response.setOrder(orders);
        response.setTotalCount(totalCount.intValue());
        return response;
    }

    private void handleResponse(StockoutSalesQueryWithDetailResponse response) {
        if (CollectionUtil.isEmpty(
                response.getOrder()
        )) {
            return;
        }

        final List<WdtSaleStockOutOrder> wdtSaleStockOutOrders = response.getOrder().stream()
                .map(Assembler.INST::orderToPo).collect(Collectors.toList());

        for (WdtSaleStockOutOrder wdtSaleStockOutOrder : wdtSaleStockOutOrders) {
            wdtSaleStockOutOrderRepository.saveOrUpdateWdtSaleStockOutOrderWithDetails(
                    wdtSaleStockOutOrder);
        }
    }


    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.WDT_SALE_STOCK_OUT_ORDER;
    }

    /**
     * <AUTHOR>
     * @since 2022/4/18
     */
    @Mapper(uses = {TimeCompatibilityTransMapper.class})
    public interface Assembler {

        Assembler INST = Mappers.getMapper(Assembler.class);

        @Mapping(target = "id", ignore = true)
        WdtSaleStockOutOrder orderToPo(StockoutSalesQueryWithDetailResponse.Order data);

        @Mapping(target = "id", ignore = true)
        @Mapping(target = "positionDetailsList", expression = "java(orderPositionDetailsToPos(data.getStockoutId(), data.getPositionDetailsList()))")
        WdtSaleStockOutOrderDetails orderDetailsToPo(
                StockoutSalesQueryWithDetailResponse.Order.Details data);

        @Mapping(target = "id", ignore = true)
        WdtSaleStockOutOrderPositionDetails orderPositionDetailsToPo(Integer stockoutId,
                StockoutSalesQueryWithDetailResponse.Order.Details.PositionDetails positionDetails);

        default List<WdtSaleStockOutOrderPositionDetails> orderPositionDetailsToPos(
                Integer stockoutId,
                List<StockoutSalesQueryWithDetailResponse.Order.Details.PositionDetails> positionDetailsList) {
            if (positionDetailsList == null) {
                return null;
            }

            List<WdtSaleStockOutOrderPositionDetails> list1 = new ArrayList<>(
                    positionDetailsList.size());
            for (PositionDetails positionDetails : positionDetailsList) {
                list1.add(orderPositionDetailsToPo(stockoutId, positionDetails));
            }

            return list1;
        }

        @Mapping(target = "id", ignore = true)
        WdtSaleStockOutOrderLogistics orderLogisticsToPo(
                StockoutSalesQueryWithDetailResponse.Order.Logistics data);


    }
}
