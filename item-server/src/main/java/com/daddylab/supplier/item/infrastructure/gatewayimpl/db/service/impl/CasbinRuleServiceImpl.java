package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CasbinRule;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CasbinRuleMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICasbinRuleService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Service
@DS("authDb")
public class CasbinRuleServiceImpl extends DaddyServiceImpl<CasbinRuleMapper, CasbinRule> implements ICasbinRuleService {

}
