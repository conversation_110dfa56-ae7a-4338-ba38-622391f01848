package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.entryActivityPrice.models.EntryActivityPriceItemInfoQry;
import com.daddylab.supplier.item.application.entryActivityPrice.models.EntryActivityPricePageQry;
import com.daddylab.supplier.item.application.entryActivityPrice.models.EntryActivityPriceSkuInfoQry;
import com.daddylab.supplier.item.application.entryActivityPrice.models.EntryActivityPriceSkuInfoVO;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.EntryActivityPriceItemInfoDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.EntryActivityPricePageDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceSku;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 入驻活动价格(商品纬度) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-04
 */
public interface EntryActivityPriceItemMapper extends DaddyBaseMapper<EntryActivityPriceItem> {

    List<EntryActivityPricePageDO> pageQuery(EntryActivityPricePageQry qry);

    List<EntryActivityPriceItemInfoDO> itemInfo(EntryActivityPriceItemInfoQry qry);

    List<EntryActivityPriceSkuInfoVO> skuInfo(EntryActivityPriceSkuInfoQry qry);

    List<EntryActivityPriceSku> listConfirmedSkuBySkuIds(@Param("skuIds") Collection<Long> skuIds);
}
