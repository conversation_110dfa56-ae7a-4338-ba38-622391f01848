package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerImage;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 抽屉图片表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
public interface ItemDrawerImageMapper extends DaddyBaseMapper<ItemDrawerImage> {

    List<ItemDrawerImage> selectSortedListByDrawerId(@Param("drawerId") Long drawerId);
}
