package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/11/7
 */
@Slf4j
@Component
public class PartnerOpenFallbackFactory implements FallbackFactory<PartnerOpenFeignClient> {

    @Override
    public PartnerOpenFeignClient create(Throwable cause) {
        return partnerToken -> {
            log.error("从合作伙伴系统获取供应商身份信息异常，token={}", partnerToken, cause);
            final Rsp<PartnerProviderResp> rsp = new Rsp<>();
            rsp.setCode(1);
            rsp.setFlag(false);
            rsp.setMsg("从合作伙伴系统获取供应商身份信息异常");
            rsp.setData(null);
            return rsp;
        };
    }
}
