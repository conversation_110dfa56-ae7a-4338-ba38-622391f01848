package com.daddylab.supplier.item.domain.provider.gateway;

import com.daddylab.supplier.item.controller.provider.dto.PartnerProviderPageQuery;
import com.daddylab.supplier.item.controller.provider.dto.ProviderShopDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuCodeProviderDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.PartnerContacts;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;

import javax.annotation.Nullable;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/28 2:33 下午
 * @description
 */
public interface ProviderGateway {

    Provider getById(Long providerId);

    boolean allExist(List<Long> providerIds);

    List<PartnerProviderResp> partnerQuery(PartnerProviderPageQuery partnerPageQuery);
    Map<Long, PartnerProviderResp> partnerBatchQueryByIds(Collection<Long> partnerProviderIds);

    default Optional<PartnerProviderResp> partnerQueryById(@Nullable Long partnerProviderId) {
        return Optional.ofNullable(
                partnerBatchQueryByIds(Collections.singletonList(partnerProviderId))
                        .get(partnerProviderId));
    }

    Optional<PartnerProviderResp> partnerQueryByProviderId(Long providerId);

    List<Provider> queryBatchByIds(List<Long> providerIds);

    PartnerProviderResp partnerQuery(String partnerToken);

    Long saveOrUpdate(Provider provider);

    Provider getOne(String name, String code);

    Boolean isRepeat(String name);

    Provider getByNo(String no);

    Boolean canRemove(Long id);

    void setKingDeeIdAndNo(Long id, String kingDeeId);

    void removeKingDeeId(Long id);

    List<SkuCodeProviderDO> getIdBySkuCode(List<String> skuCodeList);

    void remove(Long id);

    ProviderShopDetail queryShopDetail(String mallShopId);

    Long getMallShopId(Long providerId);

    List<PartnerContacts> getPartnerContactList(Long partnerProviderId);

    PartnerContacts getPartnerContactByItemNo(String partnerItemNo);
}
