//package com.daddylab.supplier.item.application.item.job;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.StrUtil;
//import com.daddylab.job.core.handler.annotation.XxlJob;
//import com.daddylab.supplier.item.common.enums.PoolEnum;
//import com.daddylab.supplier.item.common.util.ThreadUtil;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Buyer;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.BuyerMapper;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBuyerService;
//import com.daddylab.supplier.item.infrastructure.kingdee.dto.KingDeeSkuResp;
//import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR> up
// * @date 2022年08月03日 11:46 PM
// */
//@Slf4j
//@Service
//public class ClearSkuBuyerInfoJob {
//
//    @Autowired
//    IBuyerService buyerService;
//
//    @Autowired
//    ReqTemplate reqTemplate;
//
//    @Autowired
//    BuyerMapper buyerMapper;
//
//    @XxlJob("clearSkuBuyerInfoJob")
//    public void run() {
//        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
//            try {
//                // 构造采购员信息缓存, dbId和金蝶内联Id的相互映射
//                Map<Long, String> buyerDbIdAndKingDeeMapIdMap = buyerService.lambdaQuery().ne(Buyer::getKingDeeId, "")
//                        .ne(Buyer::getKingDeeId, "0")
//                        .isNotNull(Buyer::getKingDeeId)
//                        .ne(Buyer::getKingDeeMapId, "")
//                        .select().list()
//                        .stream().collect(Collectors.toMap(Buyer::getId, Buyer::getKingDeeMapId));
//                Map<String, Long> kingDeeMapIdAndBuyerDbIdMap = buyerDbIdAndKingDeeMapIdMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));
//
//                long start = 0L;
//                long limit = 1500L;
//                List<KingDeeSkuResp> kingDeeSkuRespList = reqTemplate.querySkuList(start, limit);
//                while (CollUtil.isNotEmpty(kingDeeSkuRespList)) {
//
//                    kingDeeSkuRespList.forEach(kingDeeSkuResp -> {
//                        // 查询skuCode对应的采购员的dbId;
//                        String skuCode = kingDeeSkuResp.getCode();
//                        Long buyerDbId = buyerMapper.getBuyerIdBySkuCode(skuCode);
//                        if (Objects.nonNull(buyerDbId)) {
//                            // 此sku的采购员的金蝶内联id
//                            String erpKingDeeMapId = buyerDbIdAndKingDeeMapIdMap.get(buyerDbId);
//
//                            // 金蝶api接口查询返回的此sku对应的采购员的金蝶内联id
//                            Long realBuyerKingDeeMapId = kingDeeSkuResp.getBuyerKingDeeId();
//                            // 如果两者数据不一致，准备拿金蝶api接口响应的返回数据覆盖掉erp的数据。
//                            if (Objects.nonNull(realBuyerKingDeeMapId) && StrUtil.isNotBlank(erpKingDeeMapId)) {
//                                if (erpKingDeeMapId.equals(realBuyerKingDeeMapId.toString())) {
//                                    // 查看此金蝶内联id是否在erp库中存在。
//                                    Long awaitBuyerDbId = kingDeeMapIdAndBuyerDbIdMap.get(realBuyerKingDeeMapId.toString());
//                                    // 如果存在则数据覆盖，以金蝶api接口返回为准。
//                                    if (Objects.nonNull(awaitBuyerDbId)) {
//                                        buyerMapper.updateBuyerBySkuCode(awaitBuyerDbId, skuCode);
//                                    }
//                                }
//                            }
//                        }
//                    });
//
//                    start = limit + start;
//                    kingDeeSkuRespList = reqTemplate.querySkuList(start, limit);
//
//                }
//            } catch (Exception e) {
//                log.error("clearSkuBuyerInfoJob error", e);
//            }
//        });
//    }
//}