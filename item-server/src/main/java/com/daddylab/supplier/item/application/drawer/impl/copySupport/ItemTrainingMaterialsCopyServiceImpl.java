package com.daddylab.supplier.item.application.drawer.impl.copySupport;

import com.daddylab.supplier.item.application.ItemTrainingMaterials.ItemTrainingMaterialsAssembler;
import com.daddylab.supplier.item.application.ItemTrainingMaterials.ItemTrainingMaterialsBizService;
import com.daddylab.supplier.item.application.ItemTrainingMaterials.ItemTrainingMaterialsConfig;
import com.daddylab.supplier.item.application.drawer.ItemDrawerCopyService;
import com.daddylab.supplier.item.application.process.ProcessBizService;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemTrainingMaterialsProcessRecordService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemTrainingMaterialsProcessService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemTrainingMaterialsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOperateLogService;
import com.daddylab.supplier.item.types.itemTrainingMaterials.ItemTrainingMaterialsProcessData;
import com.daddylab.supplier.item.types.itemTrainingMaterials.ItemTrainingMaterialsStatus;
import com.daddylab.supplier.item.types.itemTrainingMaterials.MaterialsProcessDataKey;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/4/22
 */
@Service
@Slf4j
public class ItemTrainingMaterialsCopyServiceImpl implements ItemDrawerCopyService {
    @Autowired
    private ItemTrainingMaterialsBizService itemTrainingMaterialsBizService;
    @Autowired
    private IItemTrainingMaterialsService itemTrainingMaterialsService;
    @Autowired
    private IItemTrainingMaterialsProcessService itemTrainingMaterialsProcessService;
    @Autowired
    private IItemTrainingMaterialsProcessRecordService itemTrainingMaterialsProcessRecordService;
    @Autowired
    private IOperateLogService operateLogService;
    @Autowired
    @Lazy
    private ProcessEngine processEngine;
    @Autowired
    @Lazy
    private ProcessBizService processBizService;
    @Autowired
    private ItemTrainingMaterialsConfig materialsConfig;

    @Override
    public void handler(ItemDrawer sourceDrawer, ItemDrawer targetDrawer) {
        final Long sourceItemId = sourceDrawer.getItemId();
        final Long targetItemId = targetDrawer.getItemId();

        final ItemTrainingMaterials sourceMaterials = itemTrainingMaterialsBizService.getMaterialsCreateIfNotExits(
                sourceItemId);
        final ItemTrainingMaterials targetMaterials = itemTrainingMaterialsBizService.getMaterialsCreateIfNotExits(
                targetItemId);

        if (sourceMaterials == null || targetMaterials == null) {
            log.error("[培训材料复制]源数据或者目标数据未找到 sourceItemId={} targetItemId={}",
                    sourceItemId,
                    targetItemId);
            return;
        }

        final Long sourceMaterialsId = sourceMaterials.getId();
        final Long targetMaterialsId = targetMaterials.getId();

        //删除目标商品原先的培训材料
        itemTrainingMaterialsService.removeByIdWithTime(targetMaterialsId);

        //复制源培训材料
        final ItemTrainingMaterials copyMaterials = ItemTrainingMaterialsAssembler.instance.copy(sourceMaterials);
        copyMaterials.setId(null);
        copyMaterials.setItemId(targetItemId);
        itemTrainingMaterialsService.save(copyMaterials);

        final Long copyMaterialsId = copyMaterials.getId();

        //查询源目标材料关联的流程
        final List<ItemTrainingMaterialsProcess> sourceMaterialsProcesses = itemTrainingMaterialsProcessService
                .lambdaQuery()
                .eq(ItemTrainingMaterialsProcess::getItemTrainingMaterialsId, sourceMaterialsId)
                .list();
        if (!sourceMaterialsProcesses.isEmpty()) {
            for (ItemTrainingMaterialsProcess sourceMaterialsProcess : sourceMaterialsProcesses) {
                final ItemTrainingMaterialsProcess copyProcess = ItemTrainingMaterialsAssembler.instance.copy(
                        sourceMaterialsProcess);
                copyProcess.setItemTrainingMaterialsId(copyMaterialsId);
                copyProcess.setId(null);
                itemTrainingMaterialsProcessService.save(copyProcess);

                if (Objects.equals(sourceMaterialsProcess.getId(), copyMaterials.getProcessId())) {
                    copyMaterials.setProcessId(copyProcess.getId());
                    itemTrainingMaterialsService.updateById(copyMaterials);

                    try {
                        final ProcessInstance processInstance = itemTrainingMaterialsBizService.createProcessInstance(
                                copyProcess);
                        final ItemTrainingMaterialsStatus status = copyProcess.getStatus();
                        switch (status) {
                            case TO_BE_LEGAL_REVIEW:
                            case TO_BE_QC_REVIEW:
                            case TO_BE_MODIFY:
                            case FINISHED:
                                processBizService.moveTo(0L, processInstance.getId(), status.getDesc());
                        }
                        if (status == ItemTrainingMaterialsStatus.TO_BE_LEGAL_REVIEW) {
                            final ItemTrainingMaterialsProcessData processData = copyProcess.getProcessData();
                            if (processData != null) {
                                final Long legalUserId = processData.getLong(MaterialsProcessDataKey.legalProcessor.name());
                                if (legalUserId != null) {
                                    final List<Task> tasks = processBizService.tasksByProcInstId(processInstance.getId());
                                    for (Task task : tasks) {
                                        try {
                                            processBizService.claim(legalUserId, task.getId());
                                        } catch (Exception e) {
                                            log.error(
                                                    "[培训材料复制]复制流程，认领异常 taskId:{} userId:{} sourceMaterialId:{} targetMaterialId:{}",
                                                    task.getId(),
                                                    legalUserId,
                                                    sourceMaterialsId,
                                                    targetMaterialsId,
                                                    e);
                                        }
                                    }
                                }
                            }
                        }
                        copyProcess.setProcessInstId(processInstance.getId());
                        itemTrainingMaterialsProcessService.updateById(copyProcess);
                    } catch (Exception e) {
                        log.error("[培训材料复制]复制流程异常 sourceMaterialId:{} targetMaterialId:{}",
                                sourceMaterialsId,
                                targetMaterialsId,
                                e);
                    }
                }

                final List<ItemTrainingMaterialsProcessRecord> materialsProcessRecords =
                        itemTrainingMaterialsProcessRecordService
                                .lambdaQuery()
                                .eq(ItemTrainingMaterialsProcessRecord::getProcessId, sourceMaterialsProcess.getId())
                                .list();
                final ArrayList<ItemTrainingMaterialsProcessRecord> copyProcessRecords = new ArrayList<>();
                for (ItemTrainingMaterialsProcessRecord processRecord : materialsProcessRecords) {
                    final ItemTrainingMaterialsProcessRecord copyProcessRecord = ItemTrainingMaterialsAssembler.instance.copy(
                            processRecord);
                    copyProcessRecord.setItemTrainingMaterialsId(copyMaterialsId);
                    copyProcessRecord.setProcessId(copyProcess.getId());
                    copyProcessRecord.setId(null);
                    copyProcessRecords.add(copyProcessRecord);
                }
                itemTrainingMaterialsProcessRecordService.saveBatch(copyProcessRecords);
            }
        }

        //复制操作记录
        operateLogService.getDaddyBaseMapper()
                         .copyOperateLogs(OperateLogTarget.ITEM_TRAINING_MATERIALS, sourceMaterialsId, copyMaterialsId);
        operateLogService.getDaddyBaseMapper()
                         .copyOperateLogs(OperateLogTarget.ITEM_DRAWER, sourceDrawer.getId(), targetDrawer.getId());

        //新增复制操作的记录
        final OperateLog operateLog = new OperateLog();
        operateLog.setTargetType(OperateLogTarget.ITEM_TRAINING_MATERIALS);
        operateLog.setTargetId(String.valueOf(copyMaterialsId));
        operateLog.setMsg(String.format("从商品%s复制培训材料", sourceItemId));
        operateLog.setData("");
        operateLogService.save(operateLog);
    }


}
