package com.daddylab.supplier.item.controller.handingsheet.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/9/5 13:31
 * @Description: 到手价VO
 */
@Data
@ApiModel("ArrivalPriceVo(到手价VO)")
public class ArrivalPriceVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("价格")
    private List<Price> priceList;

    @Data
    @ApiModel("ArrivalPriceVo-Price")
    public static class Price implements Serializable {
        private static final long serialVersionUID = 1L;

        public Price() {
        }

        public Price(BigDecimal activePrice, BigDecimal arrivalPrice) {
            this.activePrice = activePrice;
            this.arrivalPrice = arrivalPrice;
        }

        @ApiModelProperty("活动价")
        private BigDecimal activePrice;

        @ApiModelProperty("到手价")
        private BigDecimal arrivalPrice;
    }
}
