/*
package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

*/
/**
 * <p>
 * 虚拟仓库存信息（商品纬度）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 *//*

@Data
@EqualsAndHashCode(callSuper = false)
public class VirtualWarehouseInventoryGoods implements Serializable {

    private static final long serialVersionUID = 1L;

    */
/**
     * id
     *//*

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    */
/**
     * 创建时间createAt
     *//*

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    */
/**
     * 创建人updateUser
     *//*

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    */
/**
     * 更新时间updateAt
     *//*

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    */
/**
     * 更新人updateUser
     *//*

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    */
/**
     * 是否已删除
     *//*

    @TableLogic
    private Integer isDel;

    */
/**
     * 删除时间
     *//*

    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    */
/**
     * 实体仓仓库编号
     *//*

    private String warehouseNo;

    */
/**
     * SPU编码
     *//*

    private String spuNo;

    */
/**
     * SKU编码
     *//*

    private String skuNo;

    */
/**
     * 库存占比
     *//*

    private Integer inventoryRatio;

    */
/**
     * 虚拟仓ID
     *//*

    private Long virtualWarehouseId;

    */
/**
     * 虚拟仓编号
     *//*

    private String virtualWarehouseNo;

    */
/**
     * 虚拟仓库存信息id
     *//*

    private Long virtualWarehouseInventoryId;

    */
/**
     * 排除被锁定的库存占比后换算的占比
     *//*

    private BigDecimal inventoryRatio2;

    */
/**
     * 库存锁定数量
     *//*

    private Integer inventoryLockNum;

    // 100.
    // 40 60
    // 40 ..... 60/100=0.6 0.6*60=36%;


}
*/
