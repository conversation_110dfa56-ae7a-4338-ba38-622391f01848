package com.daddylab.supplier.item.application.aws.event;

import com.actionsoft.sdk.service.model.HistoryTaskInstance;
import com.actionsoft.sdk.service.model.TaskInstance;
import com.daddylab.supplier.item.application.aws.enums.AwsActionEnum;
import com.daddylab.supplier.item.application.aws.enums.AwsControlStateEnum;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class AwsChangeEvent {

    @ApiModelProperty(value = "业务ID")
    private Long businessId;

    @ApiModelProperty(value = "业务类型")
    private PurchaseTypeEnum type;

    @ApiModelProperty(value = "审核状态")
    private AwsControlStateEnum awsControlStateEnum;

    @ApiModelProperty(value = "审核动作")
    private AwsActionEnum awsActionEnum;

    @ApiModelProperty(value = "审批通过后的当前活跃任务列表")
    private List<TaskInstance> taskInstances;

    @ApiModelProperty(value = "审批通过后的历史任务列表")
    private List<HistoryTaskInstance> historyTaskInstances;

    public AwsChangeEvent() {

    }

    public static AwsChangeEvent ofNotice(Long businessId, PurchaseTypeEnum type,
            AwsControlStateEnum awsControlStateEnum, AwsActionEnum awsActionEnum,
            List<TaskInstance> taskInstances, List<HistoryTaskInstance> historyTaskInstances) {
        AwsChangeEvent awsChangeEvent = new AwsChangeEvent();
        awsChangeEvent.setBusinessId(businessId);
        awsChangeEvent.setType(type);
        awsChangeEvent.setAwsControlStateEnum(awsControlStateEnum);
        awsChangeEvent.setAwsActionEnum(awsActionEnum);
        awsChangeEvent.setTaskInstances(taskInstances);
        awsChangeEvent.setHistoryTaskInstances(historyTaskInstances);
        return awsChangeEvent;
    }
}
