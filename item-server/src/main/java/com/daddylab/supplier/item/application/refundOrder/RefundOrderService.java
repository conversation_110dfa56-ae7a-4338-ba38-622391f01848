package com.daddylab.supplier.item.application.refundOrder;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSales.AfterSalesEditReturnLogisticsCmd;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderBaseInfo;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderDetail;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderQuery;

/**
 * <AUTHOR>
 * @since 2022/4/17
 */
public interface RefundOrderService {

    PageResponse<RefundOrderBaseInfo> refundOrderQuery(RefundOrderQuery query);

    SingleResponse<RefundOrderDetail> refundOrderDetail(String refundOrderNo);

    Response editReturnLogistics(AfterSalesEditReturnLogisticsCmd cmd);

}
