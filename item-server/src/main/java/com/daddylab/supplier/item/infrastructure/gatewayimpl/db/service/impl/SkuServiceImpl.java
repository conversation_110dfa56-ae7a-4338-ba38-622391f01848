package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Sku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.SkuMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ISkuService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * SKU 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
@Service
public class SkuServiceImpl extends DaddyServiceImpl<SkuMapper, Sku> implements ISkuService {

}
