package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WarehouseAfterSalesAddress;

import java.util.List;

/**
 * <p>
 * 仓库售后退回地址 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
public interface IWarehouseAfterSalesAddressService extends IDaddyService<WarehouseAfterSalesAddress> {

    List<String> warehouseNo(Long partnerProviderId);





}
