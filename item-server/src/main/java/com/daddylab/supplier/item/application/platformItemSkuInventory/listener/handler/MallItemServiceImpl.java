package com.daddylab.supplier.item.application.platformItemSkuInventory.listener.handler;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.application.platformItemSkuInventory.PlatformItemSkuSyncService;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemSkuInventoryService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.ArkSailorItemFeignClient;
import com.daddylab.supplier.item.infrastructure.third.mallItem.MallItemService;
import com.daddylab.supplier.item.infrastructure.third.mallItem.domain.dto.MallItemChangeDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @class MallItemServiceImpl.java
 * @description 描述类的作用
 * @date 2024-03-06 09:59
 */
@Service
public class MallItemServiceImpl implements MallItemService {

    @Autowired
    private IPlatformItemSkuInventoryService platformItemSkuInventoryService;
    @Autowired
    private ArkSailorItemFeignClient itemFeignClient;

    @Autowired
    private List<PlatformItemSkuSyncService> platformItemSkuSyncServiceList;

    private void syncItem(Long itemId) {
        platformItemSkuSyncServiceList.stream().filter(service -> service.defaultType() == Platform.LAOBASHOP)
                                      .forEach(service -> {
                                          service.syncItem(null, String.valueOf(itemId));
                                      });
    }

    @Override
    public void handler(MallItemChangeDTO mallItemChangeDTO) {
        if (mallItemChangeDTO.getItemId() == null || CollUtil.isEmpty(mallItemChangeDTO.getSkuIds())) {
            return;
        }

        syncItem(mallItemChangeDTO.getItemId());
    }
}
