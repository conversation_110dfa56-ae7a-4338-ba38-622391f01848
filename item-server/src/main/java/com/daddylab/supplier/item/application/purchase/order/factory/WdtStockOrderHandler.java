package com.daddylab.supplier.item.application.purchase.order.factory;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.*;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.BaseProcess;
import com.daddylab.supplier.item.common.ExternalShopWdtNo;
import com.daddylab.supplier.item.common.OtherStockInOutConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.PurchasePriceDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.WdtOrderDetailDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICombinationItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderDetailWrapperErrorService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderDetailWrapperService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isBlank;
import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;

/**
 * wdt订单数据处理，最终洗出货品的数量和单价。
 * 最终生成 wdt_order_detail_wrapper 和 wdt_order_detail_wrapper_error
 *
 * <AUTHOR> up
 * @date 2022年08月16日 2:57 PM
 */
@Service
@Slf4j
public class WdtStockOrderHandler {

    @Resource
    PurchaseMapper purchaseMapper;

    @Resource
    ICombinationItemService iCombinationItemService;

    @Resource
    IWdtOrderDetailWrapperService iWdtOrderDetailWrapperService;

    @Resource
    IWdtOrderDetailWrapperErrorService iWdtOrderDetailWrapperErrorService;

    @Resource
    WdtOrderMapper wdtOrderMapper;

    @Resource
    OtherStockInOutConfig otherStockInOutConfig;

    @Resource
    ExternalShopWdtNo externalShopWdtNo;

    @Resource
    WdtRefundStockInOrderMapper wdtRefundStockInOrderMapper;

    @Resource
    WdtOrderDetailMapper wdtOrderDetailMapper;

    @Resource
    CommonUtil commonUtil;

    @Resource
    CommonCalculator commonCalculator;

    @Resource
    BaseProcess baseProcess;

    @Resource
    SkuPriceExplainMapper skuPriceExplainMapper;

    @Resource
    IItemSkuService iItemSkuService;

    @Resource
    WdtOrderDetailWrapperMapper wdtOrderDetailWrapperMapper;


    public void runClean(TimeBO timeBO) {
        // 如果数据发生重跑，删除指定月份的数据。
        wdtOrderDetailMapper.deleteExpireWrapper(timeBO.getOperateMonth());
        wdtOrderDetailMapper.deleteExpireWrapperError(timeBO.getOperateMonth());
        skuPriceExplainMapper.deleteExpireData(timeBO.getOperateMonth());

        mainStockOutRun(timeBO);

        mainPreStockInRun(timeBO);
        mainRefundStockInRun(timeBO, null);
    }

    // ------------------------------------------------------------------


    public void restartWdtOrderDetailWrapper(RestartWrapperCmd cmd) {
        Optional<WdtOrderDetailWrapper> opt = iWdtOrderDetailWrapperService.lambdaQuery()
                .eq(StrUtil.isNotBlank(cmd.getTradeNo()), WdtOrderDetailWrapper::getTradeNo, cmd.getTradeNo())
                .eq(StrUtil.isNotBlank(cmd.getOperateTime()), WdtOrderDetailWrapper::getOperateTime, cmd.getOperateTime())
                .eq(Objects.nonNull(cmd.getType()), WdtOrderDetailWrapper::getType, cmd.getType())
                .eq(StrUtil.isNotBlank(cmd.getSkuCode()), WdtOrderDetailWrapper::getSkuCode, cmd.getSkuCode())
                .oneOpt();
        Assert.state(opt.isPresent(), "重跑数据必须存在");
        WdtOrderDetailWrapper orderDetailWrapper = opt.get();
        iWdtOrderDetailWrapperService.removeByIdWithTime(orderDetailWrapper.getId());

        List<WdtOrderDetailDO> wdtOrderDetailDos = wdtOrderMapper.wmsStockOutDetailList(null, null, null,
                otherStockInOutConfig.getWarehouseNos(), externalShopWdtNo.getShopNos(), cmd.getTradeNo());
        Assert.isTrue(CollUtil.isNotEmpty(wdtOrderDetailDos), "订单原始数据不得为空");
        wdtOrderDetailDos.forEach(val -> wdtOrderDetailDoHandler(val, cmd.getOperateTime()));
    }


    /**
     * 销售出库明细单的处理总入口
     * 按月根据天分割轮训处理。
     *
     * @param timeBo
     */
    private void mainStockOutRun(TimeBO timeBo) {

        LocalDateTime start = timeBo.getStartLocalDateTime();
        LocalDateTime end = timeBo.getEndLocalDateTime();
        while (start.isBefore(end)) {

            LocalDate localDate = start.toLocalDate();
            String format = localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String s1 = format + " 00:00:00";
            String s2 = format + " 23:59:59";

            try {
                List<WdtOrderDetailDO> wdtOrderDetailList = wdtOrderMapper.wmsStockOutDetailList(null, s1, s2,
                        otherStockInOutConfig.getWarehouseNos(), externalShopWdtNo.getShopNos(), StrUtil.EMPTY);
                wdtOrderDetailList.forEach(val -> wdtOrderDetailDoHandler(val, timeBo.getOperateMonth()));
                log.info("------- 【{}-{}】工厂采购单数据清洗，销售出库单数据处理完成，-------", s1, s2);
            } catch (Exception e) {
                log.error("----- 【{}-{}】工厂采购单,销售出库单数据清洗异常  -------", s1, s2, e);
            } finally {
                start = start.plusDays(1);
            }
        }

        log.info("------- 工厂采购单,销售出库单数据清洗完成 month:{} -------", timeBo.getOperateMonth());
    }

    /**
     * 销售出库单，分为单品和组合装。
     * 根据查询得到的wdt出库明细数据，走两个分支来处理数据
     *
     * @param wdtOrderDetailDo
     * @param jobTargetMonthStr
     */
    private void wdtOrderDetailDoHandler(WdtOrderDetailDO wdtOrderDetailDo, String jobTargetMonthStr) {
        if (StrUtil.isBlank(wdtOrderDetailDo.getTradeNo())) {
            return;
        }

        int goodCount = wdtOrderDetailDo.getNum().intValue();
        long payTime = wdtOrderDetailDo.getPayTime().toInstant(ZoneOffset.of("+8")).getEpochSecond();
        int platformType = commonUtil.getErpPurchasePricePlatformType(wdtOrderDetailDo.getPlatformId());
        String suiteNo = wdtOrderDetailDo.getSuiteNo();

        // 剔除品牌，老爸评测 brand id 195,老爸家装 brand id 1413
        Long skuBrandId = iItemSkuService.getSkuBrandId(wdtOrderDetailDo.getSpecNo());
        if (skuBrandId.equals(195L) || skuBrandId.equals(1413L)) {
            return;
        }

        if (StrUtil.isNotBlank(suiteNo)) {
            Boolean sameProviderId = commonUtil.allComposeFromSameProvider(suiteNo);
            // 组合装下属单品必须来自同一个供应商
            if (sameProviderId) {
                // 组合装处理逻辑。
                suiteStockOutHandler(wdtOrderDetailDo, goodCount, jobTargetMonthStr, payTime, platformType);
            } else {
                ItemSku itemSku = commonUtil.getItemSku0(wdtOrderDetailDo.getSpecNo());
                singleStockOutHandler(itemSku, goodCount, jobTargetMonthStr, payTime, platformType, wdtOrderDetailDo.getTradeNo(),
                        wdtOrderDetailDo.getWarehouseNo(), wdtOrderDetailDo.getGiftType(), suiteNo);
            }
        } else {
            ItemSku itemSku = commonUtil.getItemSku0(wdtOrderDetailDo.getSpecNo());
            singleStockOutHandler(itemSku, goodCount, jobTargetMonthStr, payTime, platformType, wdtOrderDetailDo.getTradeNo(),
                    wdtOrderDetailDo.getWarehouseNo(), wdtOrderDetailDo.getGiftType(), StrUtil.EMPTY);
        }
    }

    public void suiteStockOutHandler(WdtOrderDetailDO wdtOrderDetailDo, int goodCount, String jobTargetMonthStr, long payTime, int platformType) {

        CombinationItem combinationItem = iCombinationItemService.getByItemCode(wdtOrderDetailDo.getSuiteNo());
        ItemSku itemSku = commonUtil.getItemSku0(wdtOrderDetailDo.getSpecNo());

        if (Objects.isNull(combinationItem) || Objects.isNull(itemSku)) {
            if (Objects.isNull(combinationItem) && Objects.isNull(itemSku)) {
                return;
            }
            if (Objects.isNull(combinationItem)) {
                singleStockOutHandler(itemSku, goodCount, jobTargetMonthStr, payTime, platformType, wdtOrderDetailDo.getTradeNo(),
                        wdtOrderDetailDo.getWarehouseNo(), wdtOrderDetailDo.getGiftType(), wdtOrderDetailDo.getSuiteNo());
                commonUtil.addErrorLog(jobTargetMonthStr, wdtOrderDetailDo.getSpecNo(), wdtOrderDetailDo.getTradeNo(),
                        "在wdt中来自于组合装拆分，但是这个组合装不在erp中。sku当做普通单品处理了。组合装编码：" + wdtOrderDetailDo.getSuiteNo());
                return;
            }
            commonUtil.addErrorLog(jobTargetMonthStr, wdtOrderDetailDo.getSpecNo(), wdtOrderDetailDo.getTradeNo(),
                    "在wdt中来自于组合装拆分，但是这个sku在erp不存在。skuCode：" + wdtOrderDetailDo.getSpecNo());
            return;
        }

        boolean skuBelongToCombination = commonUtil.skuBelongToCombination(itemSku.getId(), combinationItem.getId());
        if (!skuBelongToCombination) {
            singleStockOutHandler(itemSku, goodCount, jobTargetMonthStr, payTime, platformType, wdtOrderDetailDo.getTradeNo(),
                    wdtOrderDetailDo.getWarehouseNo(), wdtOrderDetailDo.getGiftType(), wdtOrderDetailDo.getSuiteNo());
            commonUtil.addErrorLog(jobTargetMonthStr, wdtOrderDetailDo.getSpecNo(), wdtOrderDetailDo.getTradeNo(),
                    "在wdt中来自于组合装拆分，但是sku不属于这个组合装。sku当做普通单品处理了。组合装编码："
                            + wdtOrderDetailDo.getSuiteNo() + "，skuCode:" + itemSku.getSkuCode());
            return;
        }

        // 查询组合装的价格，根据下单时间划分
        BigDecimal costPriceByPayTime = commonUtil.getCostPriceByPayTime(wdtOrderDetailDo.getSuiteNo(), payTime);
        BigDecimal suitPrice = Objects.isNull(costPriceByPayTime) ? combinationItem.getProcurementPrice() : costPriceByPayTime;
        if (Objects.isNull(suitPrice)) {
            singleStockOutHandler(itemSku, goodCount, jobTargetMonthStr, payTime, platformType, wdtOrderDetailDo.getTradeNo(),
                    wdtOrderDetailDo.getWarehouseNo(), wdtOrderDetailDo.getGiftType(), wdtOrderDetailDo.getSuiteNo());
            commonUtil.addErrorLog(jobTargetMonthStr, wdtOrderDetailDo.getSpecNo(), wdtOrderDetailDo.getTradeNo(),
                    "此sku来自于erp组合装：" + wdtOrderDetailDo.getSuiteNo() + "，但是这个组合的日常成本价不存在。sku当普通单品处理");
            return;
        }
        // 计算下次组合装下，各个单品的均摊价格
        List<CommonCalculator.SkuPriceUnderSuiteNo> composeSkuUnitPrices = commonCalculator
                .getSkuCostPriceUnderSuiteNo(combinationItem.getCode(), suitPrice);
        Optional<CommonCalculator.SkuPriceUnderSuiteNo> thisSkuUnitPrice = composeSkuUnitPrices.stream()
                .filter(val -> isNotBlank(itemSku.getSkuCode()) && itemSku.getSkuCode().equals(val.getSkuCode())).findFirst();
        // 组合装价格均摊下来，发现没有此sku的价格
        if (!thisSkuUnitPrice.isPresent()) {
            commonUtil.addErrorLog(jobTargetMonthStr, wdtOrderDetailDo.getSpecNo(), wdtOrderDetailDo.getTradeNo(),
                    "在wdt中来自于erp的组合装拆分，但是这个sku不是此组合装的构成单品。sku当做普通单品处理了。");
            singleStockOutHandler(itemSku, goodCount, jobTargetMonthStr, payTime, platformType, wdtOrderDetailDo.getTradeNo(),
                    wdtOrderDetailDo.getWarehouseNo(), wdtOrderDetailDo.getGiftType(), wdtOrderDetailDo.getSuiteNo());
            return;
        }
        // 仓库是否存在
        String warehouseNo = isNotBlank(wdtOrderDetailDo.getWarehouseNo()) ? wdtOrderDetailDo.getWarehouseNo() : itemSku.getWarehouseNo();
        if (isBlank(warehouseNo)) {
            commonUtil.addErrorLog(jobTargetMonthStr, wdtOrderDetailDo.getSpecNo(), wdtOrderDetailDo.getTradeNo(),
                    "此sku来自于erp组合装：" + wdtOrderDetailDo.getSuiteNo() + "，仓库信息在wdt和erp均为空。当单品处理");
            singleStockOutHandler(itemSku, goodCount, jobTargetMonthStr, payTime, platformType, wdtOrderDetailDo.getTradeNo(),
                    wdtOrderDetailDo.getWarehouseNo(), wdtOrderDetailDo.getGiftType(), StrUtil.EMPTY);
            return;
        }

//        Integer businessLine = commonUtil.getCombinationBusinessLine(combinationItem.getId());
        // 2025-05-19 SKU的合作方取值只受SKU本身合作方的影响
        Integer businessLine = commonUtil.getSkuBusinessLine(itemSku.getSkuCode());
        BigDecimal finalSkuPrice = thisSkuUnitPrice.get().getUnitPrice();
        WdtOrderDetailWrapper.Builder builder = WdtOrderDetailWrapper.Builder.builder();
        builder.operateTime(jobTargetMonthStr).skuCode(wdtOrderDetailDo.getSpecNo()).type(WrapperType.STOCK_OUT_COMBINATION)
                .price(finalSkuPrice).warehouseNo(warehouseNo).providerId(itemSku.getProviderId()).quantity(goodCount)
                .gift(wdtOrderDetailDo.getGiftType()).businessLine(businessLine)
                .tradeNo(wdtOrderDetailDo.getTradeNo()).payTime(payTime).platformType(platformType)
                .suiteNo(wdtOrderDetailDo.getSuiteNo());
        iWdtOrderDetailWrapperService.save(builder.build());

        baseProcess.saveSkuPriceLog(wdtOrderDetailDo.getSpecNo(), finalSkuPrice, jobTargetMonthStr, "组合装成本均摊。" + combinationItem.getCode());
    }

    /**
     * wdt销售出库单明细处理，单品处理
     *
     * @param itemSku           erpSku
     * @param orderQuantity     出库单sku数量
     * @param jobTargetMonthStr 采购自动结算任务执行时间 yyyyMM
     * @param payTime           下单支付时间
     * @param platformType      平台类型（erp平台类型）
     * @param tradeNo           wdt交易单号 例如：JY2323122131312
     * @param wmsWarehouseNo    仓库编号，wdt和erp,编号一致
     * @param giftType          0非赠品 1自动赠送 2手工赠送 4周期购赠送 8平台赠送
     * @param suiteNo           针对组合装拆出来的单品，保留组合装编码
     */
    public void singleStockOutHandler(ItemSku itemSku, Integer orderQuantity, String jobTargetMonthStr, Long payTime
            , Integer platformType, String tradeNo, String wmsWarehouseNo, Integer giftType, String suiteNo) {
        if (Objects.isNull(itemSku) || orderQuantity <= 0) {
            return;
        }

        BigDecimal skuCostPriceByPayTime = commonUtil.getCostPriceByPayTime(itemSku.getSkuCode(), payTime);
        BigDecimal finalSkuPrice = Objects.isNull(skuCostPriceByPayTime) ? itemSku.getCostPrice() : skuCostPriceByPayTime;
        Long providerId = commonUtil.getProviderId(itemSku);
        String warehouseNo = isNotBlank(wmsWarehouseNo) ? wmsWarehouseNo : itemSku.getWarehouseNo();
        Integer businessLine = commonUtil.getSkuBusinessLine(itemSku.getSkuCode());

        boolean errorPrice = Objects.isNull(finalSkuPrice);
        boolean errorProviderId = Objects.isNull(providerId) || 0 == providerId;
        boolean errorWarehouseNo = isBlank(warehouseNo);
        if (errorPrice || errorProviderId || errorWarehouseNo) {
            String error = String.format("价格：%s，供应商Id：%s，仓库编号：%s，数量：%s", finalSkuPrice, providerId, warehouseNo, orderQuantity);
            commonUtil.addErrorLog(jobTargetMonthStr, itemSku.getSkuCode(), tradeNo, "单品基础数据异常。" + error);
        } else {
            // 这里依据订单数量进行拆分，是为了后面计算活动价做准备。
            // 存在以数量维度的优惠活动价，比如月度前60件，特价30元。前N条订单中的数量加起来不一定能凑60，还要拆订单，干脆这里直接拆掉订单数量。
            // ps 组合商品拆出来的单品处理同理
            WdtOrderDetailWrapper.Builder builder = WdtOrderDetailWrapper.Builder.builder()
                    .operateTime(jobTargetMonthStr).skuCode(itemSku.getPurchaseOrderSkuCode()).gift(giftType)
                    .type(WrapperType.STOCK_OUT_SINGLE).price(finalSkuPrice).warehouseNo(warehouseNo)
                    .providerId(providerId).quantity(orderQuantity).tradeNo(tradeNo).payTime(payTime).platformType(platformType)
                    .suiteNo(suiteNo).businessLine(businessLine);
            iWdtOrderDetailWrapperService.save(builder.build());
        }
    }


// ----------------- 基于wdt退换管理的退货处理逻辑 -----------------
/*    @Resource
    WdtRefundOrderDetailMapper wdtRefundOrderDetailMapper;

    private void refundManage(TimeBO timeBo) {
        LocalDateTime start = timeBo.getStartLocalDateTime();
        LocalDateTime end = timeBo.getEndLocalDateTime();

        while (start.isBefore(end)) {

            LocalDate localDate = start.toLocalDate();
            String format = localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String s1 = format + " 00:00:00";
            String s2 = format + " 23:59:59";
            log.info("------- 【{}】工厂采购单数据清洗，采购退换管理数据处理开始 -------", localDate);

            try {
                // 查询到满足条件的采购退换管理数，
                refundStockInIdHandler(timeBo.getOperateMonth(), s1, s2,
                        otherStockInOutConfig.getWarehouseNos(), externalShopWdtNo.getShopNos());
                log.info("------- 【{}】工厂采购单数据清洗，采购退换管理数据处理结束 -------", localDate);
            } catch (Exception e) {
                log.error("----- 【{}】工厂采购单数据清洗,采购退换管理数据处理异常  -------", localDate, e);
            } finally {
                start = start.plusDays(1);
            }

        }
    }

    private void refundManageHandler(String start, String end, TimeBO timeBo) {
        List<WdtRefundManageDO> wdtRefundManageDOList = wdtRefundOrderDetailMapper.selectRefundList(
                start, end, otherStockInOutConfig.getWarehouseNos(), externalShopWdtNo.getShopNos());
        log.info("------- 工厂采购单数据清洗，采购退换管理数据处理开始，数量:{},start:{},end:{}-------",
                wdtRefundManageDOList.size(), start, end);
        if (CollUtil.isEmpty(wdtRefundManageDOList)) {
            return;
        }

        wdtRefundManageDOList.forEach(wdtRefundManageDO -> {
            boolean isSuite = StrUtil.isNotBlank(wdtRefundManageDO.getSuiteNo());
            boolean sameProviderId = commonUtil.allComposeFromSameProvider(wdtRefundManageDO.getSuiteNo());
            if (isSuite && sameProviderId) {
                suiteRefundHandler(wdtRefundManageDO, timeBo.getOperateMonth());
            } else {
                singleRefundHandler(wdtRefundManageDO, timeBo.getOperateMonth());
            }
        });
    }

    private void suiteRefundHandler(WdtRefundManageDO wdtRefundManageDO, String operateTime) {
        CombinationItem combinationItem = iCombinationItemService.getByItemCode(wdtRefundManageDO.getSuiteNo());
        ItemSku itemSku = commonUtil.getItemSku0(wdtRefundManageDO.getSkuCode());

        if (Objects.isNull(combinationItem) || Objects.isNull(itemSku)) {
            if (Objects.isNull(combinationItem) && Objects.isNull(itemSku)) {
                return;
            }
            if (Objects.isNull(combinationItem)) {
                singleRefundHandler(wdtRefundManageDO, operateTime);
                commonUtil.addErrorLog(operateTime, wdtRefundManageDO.getSkuCode(), wdtRefundManageDO.getTradeNo(),
                        "在wdt中来自于组合装拆分，但是这个组合装不在erp中。sku当做普通单品处理了。组合装编码：" + wdtRefundManageDO.getSuiteNo());
                return;
            }
            commonUtil.addErrorLog(operateTime, wdtRefundManageDO.getSkuCode(), wdtRefundManageDO.getTradeNo(),
                    "在wdt中来自于组合装拆分，但是这个sku在erp不存在。skuCode：" + wdtRefundManageDO.getSkuCode());
            return;
        }
        boolean skuBelongToCombination = commonUtil.skuBelongToCombination(itemSku.getId(), combinationItem.getId());
        if (!skuBelongToCombination) {
            singleRefundHandler(wdtRefundManageDO, operateTime);
            commonUtil.addErrorLog(operateTime, wdtRefundManageDO.getSkuCode(), wdtRefundManageDO.getTradeNo(),
                    "在wdt中来自于组合装拆分，但是sku不属于这个组合装。sku当做普通单品处理了。" +
                            "组合装编码：" + wdtRefundManageDO.getSuiteNo() + "，skuCode:" + wdtRefundManageDO.getSkuCode());
            return;
        }

        // 查询组合装的价格，根据下单时间划分。 计算下次组合装下，各个单品的均摊价格
        BigDecimal costPriceByPayTime = commonUtil.getCostPriceByPayTime(combinationItem.getCode(), wdtRefundManageDO.getTradeTime() / 1000);
        BigDecimal suitPrice = Objects.isNull(costPriceByPayTime) ? combinationItem.getProcurementPrice() : costPriceByPayTime;
        if (Objects.isNull(suitPrice)) {
            suitPrice = BigDecimal.ZERO;
        }
        List<CommonCalculator.SkuPriceUnderSuiteNo> composeSkuUnitPrices = commonCalculator
                .getSkuCostPriceUnderSuiteNo(combinationItem.getCode(), suitPrice);
        Optional<CommonCalculator.SkuPriceUnderSuiteNo> thisSkuUnitPrice = composeSkuUnitPrices.stream()
                .filter(val -> isNotBlank(itemSku.getSkuCode())
                        && (itemSku.getSkuCode().equals(val.getSkuCode()) || itemSku.getPurchaseOrderSkuCode().equals(val.getSkuCode())))
                .findFirst();
        BigDecimal finalSkuPrice = thisSkuUnitPrice.isPresent() ? thisSkuUnitPrice.get().getUnitPrice() : BigDecimal.ZERO;

        String warehouseNo = isNotBlank(wdtRefundManageDO.getWarehouseNo()) ? wdtRefundManageDO.getWarehouseNo() : itemSku.getWarehouseNo();
        if (isBlank(warehouseNo)) {
            commonUtil.addErrorLog(operateTime, wdtRefundManageDO.getSkuCode(), wdtRefundManageDO.getTradeNo(),
                    "此sku来自于erp组合装：" + wdtRefundManageDO.getSuiteNo() + "，仓库信息在wdt和erp均为空。当单品处理");
            singleRefundHandler(wdtRefundManageDO, operateTime);
            return;
        }

        // 最终组合装生成wrapper数据。
        List<WdtOrderDetailWrapper> list = new LinkedList<>();
        WdtOrderDetailWrapper.Builder builder = WdtOrderDetailWrapper.Builder.builder();
        builder.operateTime(operateTime).skuCode(wdtRefundManageDO.getSkuCode()).type(WrapperType.REFUND_COMBINATION)
                .price(finalSkuPrice).warehouseNo(warehouseNo).providerId(itemSku.getProviderId())
                .quantity(wdtRefundManageDO.getRefundNum().intValue())
                .gift(1).businessLine(combinationItem.getBusinessLine())
                .tradeNo(wdtRefundManageDO.getTradeNo()).payTime(wdtRefundManageDO.getTradeTime())
                .platformType(wdtRefundManageDO.getPlatformId())
                .suiteNo(wdtRefundManageDO.getSuiteNo());
        list.add(builder.build());
        iWdtOrderDetailWrapperService.saveBatch(list);

        baseProcess.saveSkuPriceLog(wdtRefundManageDO.getSkuCode(), finalSkuPrice, operateTime,
                "组合装成本均摊。" + combinationItem.getCode());
    }

    private void singleRefundHandler(WdtRefundManageDO wdtRefundManageDO, String operateTime) {
        String code = wdtRefundManageDO.getSkuCode();
        ItemSku itemSku0 = commonUtil.getItemSku0(code);
        if (Objects.isNull(itemSku0)) {
            return;
        }
        BigDecimal costPriceByPayTime = commonUtil.getCostPriceByPayTime(code, (wdtRefundManageDO.getTradeTime() / 1000));
        BigDecimal finalSkuPrice = Objects.isNull(costPriceByPayTime) ? itemSku0.getCostPrice() : costPriceByPayTime;
        Long providerId = commonUtil.getProviderId(itemSku0);
        String warehouseNo = isNotBlank(wdtRefundManageDO.getWarehouseNo()) ? wdtRefundManageDO.getWarehouseNo() : itemSku0.getWarehouseNo();
        Integer businessLine = commonUtil.getSkuBusinessLine(itemSku0.getSkuCode());

        List<WdtOrderDetailWrapper> list = new LinkedList<>();
        WdtOrderDetailWrapper.Builder builder = WdtOrderDetailWrapper.Builder.builder()
                .operateTime(operateTime)
                .skuCode(itemSku0.getPurchaseOrderSkuCode())
                // 退换管理中的sku无法区分赠品类型，统一正品处理，等后续人工介入
                .gift(0)
                .type(WrapperType.REFUND_SINGLE).price(finalSkuPrice).warehouseNo(warehouseNo)
                .providerId(providerId).quantity(wdtRefundManageDO.getRefundNum().intValue())
                .tradeNo(wdtRefundManageDO.getTradeNo())
                .payTime(wdtRefundManageDO.getTradeTime())
                .platformType(wdtRefundManageDO.getPlatformId())
                .businessLine(businessLine);
        list.add(builder.build());
        iWdtOrderDetailWrapperService.saveBatch(list);
    }*/
// ----------------- 基于wdt退换管理的退货处理逻辑 结束-----------------


    /**
     * 销售退货入库单处理入口
     *
     * @param timeBo
     */
    private void mainPreStockInRun(TimeBO timeBo) {
        log.info("---- 工厂采购单。wms入库单数据清洗开始 ----");

        // 预入库
        try {
            preStockInRun(timeBo);
        } catch (Exception e) {
            log.error("---- 处理退货入库单异常 ------ ", e);
        }
    }

    /**
     * 预入库处理
     *
     * @param timeBO
     */
    public void preStockInRun(TimeBO timeBO) {
        List<StockInNumDO> stockInNumDOList = wdtRefundStockInOrderMapper.selectPreStockInList(
                timeBO.getMonthStart(), timeBO.getMonthEnd(), otherStockInOutConfig.getWarehouseNos());
        if (CollUtil.isEmpty(stockInNumDOList)) {
            return;
        }

        List<WdtOrderDetailWrapper> list = new LinkedList<>();
        for (StockInNumDO stockInNumDo : stockInNumDOList) {
            ItemSku itemSku0 = commonUtil.getItemSku0(stockInNumDo.getSkuCode());
            if (Objects.nonNull(itemSku0)) {
                Long itemId = itemSku0.getItemId();
                Item item0 = commonUtil.getItem0(itemId);
                if (Objects.nonNull(item0)) {

                    Long brandId = Objects.isNull(item0.getBrandId()) ? 0 : item0.getBrandId();
                    // 剔除品牌，老爸仓库 id 195,老爸家装 id 1413
                    if (brandId.equals(1413L) || brandId.equals(195L)) {
                        continue;
                    }

                    WdtOrderDetailWrapper.Builder builder = WdtOrderDetailWrapper.Builder.builder();

                    builder.skuCode(stockInNumDo.getSkuCode())
                            .quantity(stockInNumDo.getNum().intValue())
                            // fixme 预入库这边的sku退货价格，要用哪个值才能作取价格的入参呢?
                            .price(itemSku0.getCostPrice())
                            .operateTime(timeBO.getOperateMonth())
                            .type(WrapperType.STOCK_IN_PRE).build();
                    WdtOrderDetailWrapper wrapper = builder.build();
                    wrapper.setWarehouseNo(stockInNumDo.getWarehouseNo());
                    wrapper.setProviderId(item0.getProviderId());
                    wrapper.setBusinessLine(commonUtil.getItemBusinessLine(itemId));

                    log.error("[系统采购单-预退货入库单]预入库价格按日常价处理，operateTime:{},skuCode:{},num:{}",
                            timeBO.getOperateMonth(), stockInNumDo.getSkuCode(), stockInNumDo.getNum().intValue());
                    list.add(wrapper);
                }
            }
        }

        iWdtOrderDetailWrapperService.saveBatch(list);
    }

    /*public void mainRefundStockInRun(TimeBO timeBo) {
        // 销售退货入库
        LocalDateTime start = timeBo.getStartLocalDateTime();
        LocalDateTime end = timeBo.getEndLocalDateTime();
        while (start.isBefore(end)) {

            LocalDate localDate = start.toLocalDate();
            String format = localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String s1 = format + " 00:00:00";
            String s2 = format + " 23:59:59";

            try {
                log.info("------- 【{}】工厂采购数据清洗，销售退货入库数据处理开始 -------", localDate);
                // 销售退货入库单，v1.0
//                 refundStockInIdHandler(timeBo.getOperateMonth(), s1, s2,otherStockInOutConfig.getWarehouseNos(), externalShopWdtNo.getShopNos());
                // 销售退货入库单，v2.0
                refundStockInIdHandlerPlus(timeBo.getOperateMonth(), s1, s2, otherStockInOutConfig.getWarehouseNos(),
                        externalShopWdtNo.getShopNos(), StrUtil.EMPTY);
                log.info("------- 【{}-{}】工厂采购数据清洗，销售退货入库数据处理结束 -------", s1, s2);
            } catch (Exception e) {
                log.error("----- 【{}-{}】工厂采购数据清洗,销售退货入库数据处理异常  -------", s1, s2, e);
            } finally {
                start = start.plusDays(1);
            }
        }
    }*/

    public void mainRefundStockInRun(TimeBO timeBo, String skuCode) {
        // 销售退货入库
        try {
            String startTime = DateUtil.format(timeBo.getStartLocalDateTime(), "yyyy-MM-dd") + " 00:00:00";
            String endTime = DateUtil.format(timeBo.getStartLocalDateTime().plusMonths(1), "yyyy-MM-dd") + " 00:00:00";
            refundStockInIdHandlerPlus(timeBo.getOperateMonth(), startTime, endTime, otherStockInOutConfig.getWarehouseNos(),
                    externalShopWdtNo.getShopNos(), skuCode);
        } catch (Exception e) {
            log.error("mainRefundStockInRun error", e);
        }
    }


    // ------- 销售退货入库处理开始 -----

    public void refundStockInIdHandlerPlus(String targetTime, String startTime, String endTime,
                                           List<String> ignoreWarehouseNoList, List<String> externalShopNos,
                                           String skuCode) {
        log.info("refundStockInIdHandlerPlus param:{},{},{},{},{},skuCode:{}",
                targetTime, startTime, endTime, JsonUtil.toJson(ignoreWarehouseNoList), JsonUtil.toJson(externalShopNos),
                skuCode);
        List<StockInDetailDO> stockInDetailDos = wdtRefundStockInOrderMapper.newRefundStockInList(startTime, endTime,
                ignoreWarehouseNoList, externalShopNos, skuCode);
        for (StockInDetailDO stockInDetailDo : stockInDetailDos) {
            WdtOrderDetailWrapper wrapper = new WdtOrderDetailWrapper();
            try {
                ItemSku itemSku0 = commonUtil.getItemSku0(stockInDetailDo.getSkuCode());
                if (Objects.isNull(itemSku0)) {
                    continue;
                }
                // 剔除品牌，老爸仓库 id 195,老爸家装 id 1413
                Long skuBrandId = iItemSkuService.getSkuBrandId(stockInDetailDo.getSkuCode());
                if (skuBrandId.equals(1413L) || skuBrandId.equals(195L)) {
                    continue;
                }
                Integer businessLine = commonUtil.getSkuBusinessLine(stockInDetailDo.getSkuCode());
                Integer platformType = commonUtil.getErpPurchasePricePlatformType(stockInDetailDo.getPlatformId());

                wrapper.setSkuCode(stockInDetailDo.getSkuCode());
                wrapper.setType(WrapperType.STOCK_IN_REFUND.getValue());
                wrapper.setSuiteNo(stockInDetailDo.getSuiteNo());
                wrapper.setTradeId(stockInDetailDo.getStockInId());
                wrapper.setTradeNo(stockInDetailDo.getTradeNo());
                wrapper.setQuantity(stockInDetailDo.getNum().intValue());
                wrapper.setOperateTime(targetTime);
                wrapper.setIsGift(stockInDetailDo.getGiftType());
                wrapper.setPlatformType(platformType);
                wrapper.setWarehouseNo(isBlank(stockInDetailDo.getWarehouseNo()) ? itemSku0.getWarehouseNo() : stockInDetailDo.getWarehouseNo());
                wrapper.setProviderId(itemSku0.getProviderId());
                wrapper.setBusinessLine(businessLine);

                Date payTime = stockInDetailDo.getTradeTime();
                // 没有查到关联订单，没有查到订单的下单时间，走商品档案维护的默认成本价格
                if (Objects.isNull(payTime)) {
                    wrapper.setPrice(itemSku0.getCostPrice());
                } else {
                    long payTimeStamp = payTime.getTime();
                    wrapper.setPayTime(payTimeStamp);
                    // 关联到订单，获取下单时间，根据时间成本获取sku成本价格。
                    BigDecimal skuCostPriceByPayTime = commonUtil.getCostPriceByPayTime(itemSku0.getSkuCode(), payTimeStamp);
                    BigDecimal skuCostPrice = Objects.isNull(skuCostPriceByPayTime) ? itemSku0.getCostPrice() : skuCostPriceByPayTime;
                    // 再结合组合装，再结合时间纬度的采购活动优惠价，计算出一个sku成本价
                    BigDecimal refundPrice = getRefundPrice(stockInDetailDo.getSuiteNo(), stockInDetailDo.getSkuCode(), itemSku0.getSkuCode(),
                            skuCostPrice, platformType, payTimeStamp);
                    wrapper.setPrice(refundPrice);
                }
                wdtOrderDetailWrapperMapper.insert(wrapper);
            } catch (Exception e) {
                log.error("refundStockInIdHandlerPlus error,wrapper:{}", JsonUtil.toJson(wrapper), e);
            }
        }
        log.info("refundStockInIdHandlerPlus saveList:{},start:{},end:{}", stockInDetailDos.size(), startTime, endTime);
    }


    // -------------- 基于wdt销售退换单的退货处理 v1.0 --------------------
    private void refundStockInIdHandler(String targetTime, String startTime, String endTime,
                                        List<String> ignoreWarehouseNoList, List<String> externalShopNos) {

        List<StockInNumDO> stockInDOList = wdtRefundStockInOrderMapper.selectRefundStockInList2(startTime, endTime,
                ignoreWarehouseNoList, externalShopNos);
        if (CollUtil.isEmpty(stockInDOList)) {
            return;
        }
        List<StockInNumDO> finalStockInDOList = stockInDOList.stream().peek(val -> val.setItemSku(commonUtil.getItemSku0(val.getSkuCode())))
                .filter(val -> Objects.nonNull(val.getItemSku())).collect(Collectors.toList());

        finalStockInDOList.stream().collect(Collectors.groupingBy(StockInNumDO::getStockInId))
                .forEach((stockInId, list) -> {
                    // 一个stockInId可能对应多个list
                    // list中包含了skuCode和退款入库的数量，一个退换入库单中可能存在多个sku。
                    // 根据共同的入库单id关联到下单的时间。计算sku在下单时间的成本价。
                    List<OrderTradeTimeDO> orderTradeTimeDOList = wdtRefundStockInOrderMapper.selectOrderTradeTimeByStockInId2(stockInId);
                    oneRefundStockInIdHandler(stockInId, targetTime, list, orderTradeTimeDOList);
                });

    }

    private void oneRefundStockInIdHandler(Long stockInId, String targetTime, List<StockInNumDO> numList, List<OrderTradeTimeDO> skuTradeTimeList) {

        // 如果这批销售退货入库单的交易信息查询为空。走日常价退。
        if (CollUtil.isEmpty(skuTradeTimeList)) {
            List<WdtOrderDetailWrapper> list = new LinkedList<>();
            numList.forEach(val -> {
                WdtOrderDetailWrapper.Builder builder = WdtOrderDetailWrapper.Builder.builder();
                builder.skuCode(val.getSkuCode()).quantity(val.getNum().intValue())
                        // fixme 销售退货入库这边的sku退货价格，因为查不到交易信息，无法找到下单时间，暂时走日常成本价记录
                        .price(val.getItemSku().getCostPrice())
                        .operateTime(targetTime)
                        .type(WrapperType.STOCK_IN_REFUND).build();
                WdtOrderDetailWrapper wrapper = builder.build();
                String warehouseNo = isNotBlank(val.getWarehouseNo()) ? val.getWarehouseNo() : val.getItemSku().getWarehouseNo();
                wrapper.setWarehouseNo(warehouseNo);
                wrapper.setProviderId(val.getItemSku().getProviderId());
                // 将入库单id存入tradeId字段。
                wrapper.setTradeId(stockInId);

                log.error("[系统采购单-销售退货入库单]交易信息查询为空，退货入库价格按日常价处理，operateTime:{},skuCode:{},num:{}",
                        targetTime, val.getSkuCode(), val.getNum().intValue());

                list.add(wrapper);
            });
            iWdtOrderDetailWrapperService.saveBatch(list);
            return;
        }

        List<WdtOrderDetailWrapper> list = new LinkedList<>();
        Map<String, List<OrderTradeTimeDO>> tradeTimeMap = skuTradeTimeList.stream().collect(Collectors.groupingBy(OrderTradeTimeDO::getSkuCode));

        numList.forEach(stockInNumDO -> {
            WdtOrderDetailWrapper.Builder builder = WdtOrderDetailWrapper.Builder.builder();
            WdtOrderDetailWrapper wrapper = builder
                    .skuCode(stockInNumDO.getSkuCode())
                    .quantity(stockInNumDO.getNum().intValue())
                    .operateTime(targetTime)
                    .type(WrapperType.STOCK_IN_REFUND).build();
            wrapper.setProviderId(stockInNumDO.getItemSku().getProviderId());
            String warehouseNo = isBlank(stockInNumDO.getItemSku().getWarehouseNo()) ?
                    stockInNumDO.getWarehouseNo() : stockInNumDO.getItemSku().getWarehouseNo();
            wrapper.setWarehouseNo(warehouseNo);
            wrapper.setTradeNo(stockInId.toString());

            String skuCode = stockInNumDO.getSkuCode();
            List<OrderTradeTimeDO> orderTradeTimeDOList = tradeTimeMap.get(skuCode);
            // 没有此sku对应的下单时间。
            if (CollUtil.isEmpty(orderTradeTimeDOList)) {
                wrapper.setPrice(stockInNumDO.getItemSku().getCostPrice());
                // fixme
                log.error("[系统采购单-销售退货入库单]交易信息查询为空，退货入库价格按日常价处理，operateTime:{},skuCode:{},num:{}",
                        targetTime, stockInNumDO.getSkuCode(), stockInNumDO.getNum().intValue());

            }
            // 存在下单时间,即存在交易信息
            else {
                OrderTradeTimeDO tradeTimeDO;
                if (orderTradeTimeDOList.size() > 1) {
                    tradeTimeDO = orderTradeTimeDOList.stream().max(Comparator.comparing(OrderTradeTimeDO::getTradeTime)).get();
                } else {
                    tradeTimeDO = orderTradeTimeDOList.get(0);
                }
                wrapper.setSuiteNo(tradeTimeDO.getSuiteNo());
                wrapper.setTradeNo(tradeTimeDO.getTradeNo());

                Date tradeTime = tradeTimeDO.getTradeTime();
                if (Objects.isNull(tradeTime)) {
                    String errorMsg = "skuCode:" + stockInNumDO.getSkuCode() + "，refundStockInId:" + stockInId + "，" +
                            "原始订单下单时间为空";
                    WdtOrderDetailWrapperError error = WdtOrderDetailWrapperError.ofWrapper(builder.build(), errorMsg);
                    iWdtOrderDetailWrapperErrorService.asyncSave(error);
                    return;
                }
                wrapper.setPayTime(tradeTime.getTime());

                Integer platformId = tradeTimeDO.getPlatformId();
                if (Objects.isNull(platformId)) {
                    String errorMsg = "skuCode:" + stockInNumDO.getSkuCode() + "，refundStockInId:" + stockInId + "，" +
                            "原始订单平台为空";
                    WdtOrderDetailWrapperError error = WdtOrderDetailWrapperError.ofWrapper(builder.build(), errorMsg);
                    iWdtOrderDetailWrapperErrorService.asyncSave(error);
                    return;
                }

                Integer platformType = commonUtil.getErpPurchasePricePlatformType(platformId);

                // 根据下单时间查询sku的价格
                BigDecimal skuCostPriceByPayTime = commonUtil.getCostPriceByPayTime(stockInNumDO.getItemSku().getSkuCode(), tradeTime.getTime());
                BigDecimal skuCostPrice = Objects.isNull(skuCostPriceByPayTime) ? stockInNumDO.getItemSku().getCostPrice()
                        : skuCostPriceByPayTime;
                // 计算退款的额金额
                wrapper.setPrice(getRefundPrice(tradeTimeDO.getSuiteNo(), stockInNumDO.getSkuCode(), stockInNumDO.getItemSku().getSkuCode()
                        , skuCostPrice, platformType, tradeTime.getTime()));
            }

            list.add(wrapper);
        });

        iWdtOrderDetailWrapperService.saveBatch(list);
    }
    // -------------- 基于wdt销售退换单的退货处理 v1.0 结束--------------------


    /**
     * 计算销售入库单的价格，以日常价为基础，结合时间维度的采购活动优惠价。
     *
     * @param suiteNo
     * @param wdtSkuCode    旺店通商家编码
     * @param erpSysSkuCode erp系统sku编码
     * @param skuCostPrice
     * @param platformType
     * @param tradeTime
     * @return
     */
    private BigDecimal getRefundPrice(String suiteNo, String wdtSkuCode, String erpSysSkuCode, BigDecimal skuCostPrice, Integer platformType, Long tradeTime) {
        BigDecimal refundPrice;
        // 组合装的处理
        if (isNotBlank(suiteNo)) {
            // 查询此组合商品
            List<CombinationItem> list1 = iCombinationItemService.lambdaQuery()
                    .eq(CombinationItem::getCode, suiteNo)
                    .orderByDesc(CombinationItem::getId)
                    .select().list();
            if (CollUtil.isEmpty(list1)) {
                // 组合商品不存在，按照单品日常价算退货价格
                refundPrice = skuCostPrice;
            } else {
                // 看是否存在此组合装的采购活动价（时间维度）
                CombinationItem combinationItem = list1.get(0);
                List<PurchasePriceDO> purchasePrice = purchaseMapper.getPurchasePrice(suiteNo, tradeTime, platformType);
                BigDecimal combinationItemCostPrice;
                if (CollUtil.isEmpty(purchasePrice)) {
                    combinationItemCostPrice = combinationItem.getProcurementPrice();
                } else {
                    combinationItemCostPrice = purchasePrice.get(0).getPriceCost();
                }

                List<CommonCalculator.SkuPriceUnderSuiteNo> skuCostPriceUnderSuiteNo = commonCalculator
                        .getSkuCostPriceUnderSuiteNo(combinationItem.getCode(), combinationItemCostPrice);
                Optional<CommonCalculator.SkuPriceUnderSuiteNo> first = skuCostPriceUnderSuiteNo.stream()
                        .filter(val -> val.getSkuCode().equals(erpSysSkuCode)).findFirst();
                if (first.isPresent()) {
                    refundPrice = first.get().getUnitPrice();
                } else {
                    refundPrice = skuCostPrice;
                }
            }

        } else {
            List<PurchasePriceDO> purchasePrice = purchaseMapper.getPurchasePrice(wdtSkuCode, tradeTime, platformType);
            if (CollUtil.isNotEmpty(purchasePrice)) {
                PurchasePriceDO purchasePriceDo = purchasePrice.get(0);
                refundPrice = purchasePriceDo.getPriceCost();
            } else {
                refundPrice = skuCostPrice;
            }
        }
        return refundPrice;
    }


}
