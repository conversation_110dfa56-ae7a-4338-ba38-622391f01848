package com.daddylab.supplier.item.infrastructure.utils;

import cn.hutool.core.collection.ListUtil;
import com.daddylab.supplier.item.common.domain.EntityChange;
import com.daddylab.supplier.item.infrastructure.diff.CollChange;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopPrincipal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.javers.core.Javers;
import org.javers.core.JaversBuilder;
import org.javers.core.MappingStyle;
import org.javers.core.diff.Change;
import org.javers.core.diff.Diff;
import org.javers.core.diff.DiffBuilder;
import org.javers.core.diff.ListCompareAlgorithm;
import org.javers.core.diff.changetype.NewObject;
import org.javers.core.diff.changetype.ObjectRemoved;
import org.javers.core.diff.changetype.PropertyChange;
import org.javers.core.diff.changetype.ValueChange;
import org.javers.core.diff.changetype.container.*;
import org.javers.core.diff.custom.CustomValueComparator;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Collections.emptyList;

/**
 * {@linke https://javers.org/documentation/diff-configuration/} 列表比较算法说明： -
 * ListCompareAlgorithm.AS_SET 将列表当作集合处理，不关心元素顺序 - ListCompareAlgorithm.SIMPLE
 * 线性比较，速度很快，逐个比较对应未知的元素是否发生变化 - ListCompareAlgorithm.LEVENSHTEIN_DISTANCE 最小编辑距离算法，但是比较耗费性能
 */
@Slf4j
public class DiffUtil {

  private static final Javers fieldJavers =
      JaversBuilder.javers()
          .withListCompareAlgorithm(ListCompareAlgorithm.SIMPLE)
          .withMappingStyle(MappingStyle.FIELD) // 直接访问字段获取属性
          .registerValue(ShopPrincipal.class)
          .registerValue(BigDecimal.class, new BigDecimalComparatorWithFixedEquals())
          .withPrettyPrint(false)
          .build();

  private static final Javers beanJavers =
      JaversBuilder.javers()
          .withListCompareAlgorithm(ListCompareAlgorithm.SIMPLE)
          .withMappingStyle(MappingStyle.BEAN) // 使用Getter方法获取属性
          .registerValue(BigDecimal.class, new BigDecimalComparatorWithFixedEquals())
          .withPrettyPrint(false)
          .build();

  private DiffUtil() {}

  public static DiffUtil getInstance() {
    return INSTANCE.INSTANCE.getUtil();
  }

  /**
   * 请注意，对于实体和值对象集合时，比较结果返回实体的 GlobalId 对象
   *
   * @param left 原始集合
   * @param right 变更集合
   * @param itemClass 集合类型
   */
  @SuppressWarnings("rawtypes")
  private static <T> Optional<CollChange<T>> diffCollection(
      Collection<T> left, Collection<T> right, Class<T> itemClass) {
    final Diff diff = fieldJavers.compareCollections(left, right, itemClass);
    final List<CollectionChange> setChanges = diff.getChangesByType(CollectionChange.class);
    final CollectionChange setChange = setChanges.size() > 0 ? setChanges.get(0) : null;
    return Optional.of(
        new CollChange<>(
            left,
            right,
            setChange != null ? getAddedValues(diff, setChange.getAddedValues()) : emptyList(),
            setChange != null ? getRemovedValues(diff, setChange.getRemovedValues()) : emptyList(),
            getUpdatedValues(left, right, diff),
            diff));
  }

  /**
   * 请注意，对于实体和值对象集合时，比较结果返回实体的 GlobalId 对象
   *
   * @param left 原始集合
   * @param right 变更集合
   * @param itemClass 集合类型
   */
  public static <T> Optional<CollChange<T>> diffSet(Set<T> left, Set<T> right, Class<T> itemClass) {
    return diffCollection(left, right, itemClass);
  }

  /**
   * 请注意，对于实体和值对象集合时，比较结果返回实体的 GlobalId 对象
   *
   * @param left 原始集合
   * @param right 变更集合
   * @param itemClass 集合类型
   */
  public static <T> Optional<CollChange<T>> diffList(
      List<T> left, List<T> right, Class<T> itemClass) {
    return diffCollection(left, right, itemClass);
  }

  /**
   * 请注意，对于实体和值对象集合时，比较结果返回实体的 GlobalId 对象
   *
   * @param left 原始集合
   * @param right 变更集合
   * @param itemClass 集合类型
   */
  public static <T> Optional<CollChange<T>> diffList(
      Collection<T> left, Collection<T> right, Class<T> itemClass) {
    final Diff diff = fieldJavers.compareCollections(left, right, itemClass);
    final List<ListChange> listChanges = diff.getChangesByType(ListChange.class);
    final ListChange listChange = listChanges.size() > 0 ? listChanges.get(0) : null;
    return Optional.of(
        new CollChange<>(
            left,
            right,
            listChange != null ? getAddedValues(diff, listChange.getAddedValues()) : emptyList(),
            listChange != null
                ? getRemovedValues(diff, listChange.getRemovedValues())
                : emptyList(),
            getUpdatedValues(left, right, diff),
            diff));
  }

  @NonNull
  private static <T> List<Pair<T, T>> getUpdatedValues(
      Collection<T> left, Collection<T> right, Diff diff) {
    final HashSet<Object> added = new HashSet<>();
    final ArrayList<Pair<T, T>> updatedValues = new ArrayList<>();
    diff.getChangesByType(PropertyChange.class)
        .forEach(
            v -> {
              if (v.getClass() == ValueChange.class) {
                final Object affectedObject = v.getAffectedObject().orElse(null);
                if (affectedObject != null && !added.contains(affectedObject)) {
                  final T left1 =
                      left.stream().filter(lv -> lv.equals(affectedObject)).findAny().orElse(null);
                  final T right1 =
                      right.stream().filter(lv -> lv.equals(affectedObject)).findAny().orElse(null);
                  updatedValues.add(Pair.of(left1, right1));
                  added.add(affectedObject);
                }
              }
            });
    return updatedValues;
  }

  public static Diff diff(Object oldObj, Object newObj, boolean useGetter) {
    try {
      if (useGetter) {
        return beanJavers.compare(oldObj, newObj);
      } else {
        return fieldJavers.compare(oldObj, newObj);
      }
    } catch (Exception e) {
      log.error("Diff Error: {}", e.getMessage(), e);
      return emptyDiff();
    }
  }

  public static Diff diff(Object oldObj, Object newObj) {
    return diff(oldObj, newObj, false);
  }

  @NonNull
  public static <T> List<T> getAddedValues(Diff diff, List<?> ids) {
    final List<NewObject> newObjectChanges = diff.getChangesByType(NewObject.class);
    return ids.stream()
        .map(
            v -> {
              //noinspection unchecked
              return (T)
                  newObjectChanges.stream()
                      .filter(c -> Objects.equals(c.getAffectedGlobalId(), v))
                      .findFirst()
                      .flatMap(Change::getAffectedObject)
                      .orElse(v);
            })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  @NonNull
  public static <T> List<T> getRemovedValues(Diff diff, List<?> ids) {
    final List<ObjectRemoved> objectRemovedChanges = diff.getChangesByType(ObjectRemoved.class);
    return ids.stream()
        .map(
            v -> {
              //noinspection unchecked
              return (T)
                  objectRemovedChanges.stream()
                      .filter(c -> Objects.equals(c.getAffectedGlobalId(), v))
                      .findFirst()
                      .flatMap(Change::getAffectedObject)
                      .orElse(v);
            })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  public static Diff emptyDiff() {
    return DiffBuilder.empty();
  }

  public static <T> EntityChange<T> diffEntity(T oldEntity, T newEntity) {
    if (oldEntity == null) {
      return EntityChange.ofAdd(newEntity);
    } else {
      return EntityChange.ofUpdate(diff(oldEntity, newEntity), oldEntity, newEntity);
    }
  }

  public static String toJson(Object diff) {
    return fieldJavers.getJsonConverter().toJson(diff);
  }

  public static <T> T fromJson(String diff, Class<T> expectedType) {
    return fieldJavers.getJsonConverter().fromJson(diff, expectedType);
  }

  /**
   * list obj 比较
   *
   * @param oldList
   * @param newList
   * @param clazz
   */
  public static <T> ObjListDiff diffObjList(List<T> oldList, List<T> newList, Class<T> clazz) {
    return diffObjList(oldList, newList, clazz, false);
  }

  /**
   * list obj 比较
   *
   * @param oldList
   * @param newList
   * @param clazz
   * @param useGetter 使用该模式需要将 Javers 相关的注解都加到 getter 方法上，否则不生效
   */
  public static <T> ObjListDiff diffObjList(
      List<T> oldList, List<T> newList, Class<T> clazz, Boolean useGetter) {
    final JaversBuilder javersBuilder = JaversBuilder.javers();
    javersBuilder
        .registerValue(BigDecimal.class, new BigDecimalComparatorWithFixedEquals())
        .withListCompareAlgorithm(ListCompareAlgorithm.AS_SET);
    if (useGetter) {
      javersBuilder.withMappingStyle(MappingStyle.BEAN); // 使用Getter方法获取属性
    }
    Javers javers = javersBuilder.build();
    Diff diff = javers.compareCollections(oldList, newList, clazz);

    HashSet<Object> addId = new HashSet<>();
    HashSet<Object> removeId = new HashSet<>();
    Map<Object, Set<ChangePropertyObj>> changeMap = new HashMap<>(16);
    diff.groupByObject()
        .forEach(
            (it) -> {
              it.getNewObjects()
                  .forEach(
                      (c) -> {
                        addId.add(c.getAffectedLocalId());
                      });
              it.getObjectsRemoved()
                  .forEach(
                      (c) -> {
                        removeId.add(c.getAffectedLocalId());
                      });
              it.getPropertyChanges()
                  .forEach(
                      (c) -> {
                        boolean pa =
                            c instanceof ValueChange
                                && (!addId.contains(c.getAffectedLocalId()))
                                && (!removeId.contains(c.getAffectedLocalId()));
                        if (pa) {
                          Object affectedLocalId = c.getAffectedLocalId();
                          Set<ChangePropertyObj> orDefault =
                              changeMap.getOrDefault(affectedLocalId, new HashSet<>());
                          ChangePropertyObj property =
                              new ChangePropertyObj(c.getPropertyName(), c.getLeft(), c.getRight());
                          orDefault.add(property);
                          changeMap.put(affectedLocalId, orDefault);
                        }
                      });
            });
    return new ObjListDiff(addId, removeId, changeMap);
  }

  @Data
  public static class LangDiffRes<T> {
    private List<T> addList;
    private List<T> removeList;
  }

  public static <T> LangDiffRes<T> diffLangObjList(
      List<T> oldList, List<T> newList, Class<T> clazz) {
    final JaversBuilder javersBuilder = JaversBuilder.javers();
    javersBuilder
        .registerValue(BigDecimal.class, new BigDecimalComparatorWithFixedEquals())
        .withListCompareAlgorithm(ListCompareAlgorithm.AS_SET);
    Javers javers = javersBuilder.build();
    Diff diff = javers.compareCollections(oldList, newList, clazz);

    LangDiffRes<T> langDiffRes = new LangDiffRes<>();
    List<T> addList = new LinkedList<>();
    List<T> removeList = new LinkedList<>();

    for (Change change : diff.getChanges()) {
      ListChange listChange = (ListChange) change;
      List<ContainerElementChange> changes = listChange.getChanges();
      for (ContainerElementChange elementChange : changes) {
        if (elementChange instanceof ValueRemoved) {
          ValueRemoved vr = (ValueRemoved) elementChange;
          removeList.add((T) vr.getValue());
        }
        if (elementChange instanceof ValueAdded) {
          ValueAdded vr = (ValueAdded) elementChange;
          addList.add((T) vr.getValue());
        }
      }
    }
    langDiffRes.setAddList(addList);
    langDiffRes.setRemoveList(removeList);

    return langDiffRes;
  }

  /**
   * 获取简单的变更日志
   *
   * @param diff Diff
   * @param beanClass beanClass
   * @return String
   */
  public static String getSimpleDiffLog(Diff diff, Class<?> beanClass) {
    return getSimpleDiffLog(diff, beanClass, Collections.emptyList());
  }

  /**
   * 获取简单的变更日志
   *
   * @param diff Diff
   * @param beanClass beanClass
   * @param otherProps 其他字段变更
   * @return String
   */
  public static String getSimpleDiffLog(
      Diff diff, Class<?> beanClass, @NonNull List<String> otherProps) {
    final Map<String, String> propertyNameMap = BeanUtil.getReadabilityPropertyNames(beanClass);
    final List<String> modifyProps =
        diff.getChangesByType(PropertyChange.class).stream()
            .map(PropertyChange::getPropertyName)
            .map(key -> propertyNameMap.getOrDefault(key, key))
            .collect(Collectors.toList());
    modifyProps.addAll(otherProps);
    if (modifyProps.isEmpty()) {
      return "数据无变更";
    }
    return String.format("修改了%s", String.join("、", modifyProps));
  }

  /**
   * 获取详细的变更日志
   *
   * @param diff Diff
   * @param beanClass beanClass
   * @return String
   */
  public static String getVerboseDiffLog(Diff diff, Class<?> beanClass, String suffix) {
    return getVerboseDiffLog(
        diff, beanClass, Collections.emptyList(), Collections.emptyMap(), suffix);
  }

  /**
   * 获取详细的变更日志
   *
   * @param diff Diff
   * @param beanClass beanClass
   * @return String
   */
  public static String getVerboseDiffLog(
      Diff diff, Class<?> beanClass, String prefix, String suffix) {
    final Map<String, String> propertyNameMap = BeanUtil.getReadabilityPropertyNames(beanClass);
    return getVerboseDiffLog(
        diff, propertyNameMap, Collections.emptyList(), Collections.emptyMap(), prefix, suffix);
  }

  /**
   * 获取详细的变更日志
   *
   * @param diff Diff
   * @param beanClass beanClass
   * @param otherProps 其他字段变更
   * @param printers 日志打印格式化
   * @param suffix 后缀
   * @return String
   */
  public static String getVerboseDiffLog(
      Diff diff,
      Class<?> beanClass,
      @NonNull List<String> otherProps,
      @NonNull Map<Object, Function<Object, String>> printers,
      String suffix) {
    final Map<String, String> propertyNameMap = BeanUtil.getReadabilityPropertyNames(beanClass);
    return getVerboseDiffLog(diff, propertyNameMap, otherProps, printers, suffix);
  }

  private static final Map<Object, Function<Object, String>> defaultPrinters = new HashMap<>();

  static {
    defaultPrinters.put(
        Boolean.class, v -> Optional.ofNullable(v).map(vv -> (boolean) v ? "是" : "否").orElse("-"));
    defaultPrinters.put(
        boolean.class, v -> Optional.ofNullable(v).map(vv -> (boolean) v ? "是" : "否").orElse("-"));
  }

  /**
   * 获取详细的变更日志
   *
   * @param diff Diff
   * @param propertyNameMap 属性名称字典
   * @param otherProps 其他字段变更
   * @param printers 日志打印格式化
   * @param suffix 后缀
   * @return String
   */
  public static String getVerboseDiffLog(
      Diff diff,
      Map<String, String> propertyNameMap,
      @NonNull List<String> otherProps,
      @NonNull Map<Object, Function<Object, String>> printers,
      String suffix) {
    return getVerboseDiffLog(diff, propertyNameMap, otherProps, printers, null, suffix);
  }

  /**
   * 获取详细的变更日志
   *
   * @param diff Diff
   * @param propertyNameMap 属性名称字典
   * @param otherProps 其他字段变更
   * @param printers 日志打印格式化
   * @param prefix 前缀
   * @param suffix 后缀
   * @return String
   */
  public static String getVerboseDiffLog(
      Diff diff,
      Map<String, String> propertyNameMap,
      @NonNull List<String> otherProps,
      @NonNull Map<Object, Function<Object, String>> printers,
      String prefix,
      String suffix) {
    final HashMap<Object, Function<Object, String>> printersMap = new HashMap<>(printers);
    printersMap.putAll(defaultPrinters);
    final List<String> modifyProps =
        diff.getChangesByType(PropertyChange.class).stream()
            .map(
                propertyChange -> {
                  String originalPropertyName = propertyChange.getPropertyName();
                  final String propertyName =
                      propertyNameMap.getOrDefault(originalPropertyName, originalPropertyName);

                  final Optional<? extends Class<?>> classOptional =
                      Stream.of(propertyChange.getLeft(), propertyChange.getRight())
                          .filter(Objects::nonNull)
                          .map(Object::getClass)
                          .findFirst();
                  Function<Object, String> printer = printersMap.get(originalPropertyName);
                  if (printer == null && classOptional.isPresent()) {
                    printer = printersMap.get(classOptional.get());
                  }
                  if (printer == null) {
                    printer = v -> Optional.ofNullable(v).map(Objects::toString).orElse("-");
                  }
                  return String.format(
                      "将 “%s” 从 “%s” 改为 “%s”",
                      propertyName,
                      printer.apply(propertyChange.getLeft()),
                      printer.apply(propertyChange.getRight()));
                })
            .collect(Collectors.toList());
    modifyProps.addAll(otherProps);
    if (modifyProps.isEmpty()) {
      modifyProps.add("数据无变更");
    }
    final StringBuilder sb = new StringBuilder();
    if (prefix != null && !prefix.isEmpty()) {
      sb.append(prefix);
    }
    sb.append(String.join("，", modifyProps));
    if (suffix != null && !suffix.isEmpty()) {
      sb.append(suffix);
    }
    return sb.toString();
  }

  private enum INSTANCE {
    /** 消除黄线 */
    INSTANCE;

    private final DiffUtil util;

    INSTANCE() {
      this.util = new DiffUtil();
    }

    private DiffUtil getUtil() {
      return this.util;
    }
  }

  public static class BigDecimalComparatorWithFixedEquals
      implements CustomValueComparator<BigDecimal> {

    @Override
    public boolean equals(BigDecimal a, BigDecimal b) {
      return a.compareTo(b) == 0;
    }

    @Override
    public String toString(BigDecimal bigDecimal) {
      return bigDecimal.toString();
    }
  }

  @Data
  @AllArgsConstructor
  public static class ObjListDiff {

    private HashSet<Object> addIdSet;
    private HashSet<Object> removeIdSet;
    private Map<Object, Set<ChangePropertyObj>> changeMap;
  }

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class ChangePropertyObj {

    private String property;
    private Object oldVal;
    private Object newVal;
  }

  public static void main(String[] args) {
    List<Long> l1 = ListUtil.of(1L, 2L);
    List<Long> l2 = ListUtil.of(2L, 3L);

    LangDiffRes<Long> longLangDiffRes = DiffUtil.diffLangObjList(l1, l2, Long.class);
    System.out.println(JsonUtil.toJson(longLangDiffRes));
  }
}
