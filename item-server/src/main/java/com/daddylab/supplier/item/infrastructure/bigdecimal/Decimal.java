package com.daddylab.supplier.item.infrastructure.bigdecimal;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/7/1
 */
public class Decimal {
    public static final RoundingMode MODE = RoundingMode.HALF_UP;
    public static final int SCALE = 6;
    private BigDecimal value;

    private Decimal() {
        this(BigDecimal.ZERO);
    }

    private Decimal(BigDecimal value) {
        this.value = Optional.ofNullable(value).orElse(BigDecimal.ZERO);
    }

    private Decimal(Number value) {
        this(ofBigDecimal(value));
    }

    private static BigDecimal ofBigDecimal(Number value) {
        return new BigDecimal(value.toString());
    }

    public static Decimal of() {
        return new Decimal();
    }

    public static Decimal of(Decimal value) {
        return value;
    }

    public static Decimal of(BigDecimal value) {
        return new Decimal(value);
    }

    public static Decimal of(Number value) {
        return new Decimal(value);
    }

    public Decimal scale(int newScale) {
        return scale(newScale, MODE);
    }

    public Decimal scale(int newScale, RoundingMode roundingMode) {
        this.value = value.setScale(newScale, roundingMode);
        return this;
    }

    public BigDecimal asBigDecimal() {
        return value;
    }

    public BigInteger asBigInteger() {
        return value.toBigInteger();
    }
    public Integer asInteger() {
        return value.intValue();
    }

    public Float asFloat() {
        return value.floatValue();
    }

    public double asDouble() {
        return value.doubleValue();
    }

    @Override
    public String toString() {
        return value.toPlainString();
    }

    public Decimal add(Number number) {
        if (number == null) {
            return this;
        }
        this.value = this.value.add(ofBigDecimal(number));
        return this;
    }

    public Decimal subtract(Number number) {
        if (number == null) {
            return this;
        }
        this.value = this.value.subtract(ofBigDecimal(number));
        return this;
    }

    public Decimal multiply(Number number) {
        if (number == null) {
            return this;
        }

        this.value = this.value.multiply(ofBigDecimal(number));
        return this;
    }

    public Decimal divide(Number number) {
        if (number == null) {
            return this;
        }

        return divide(number, SCALE, MODE);
    }

    public Decimal divide(Number number, int newScale, RoundingMode roundingMode) {
        if (number == null) {
            return this;
        }

        this.value = this.value.divide(ofBigDecimal(number), newScale, roundingMode);
        return this;
    }

    public Decimal add(Decimal number) {
        return this.add(number.value);
    }

    public Decimal subtract(Decimal number) {
        return this.subtract(number.value);
    }

    public Decimal multiply(Decimal number) {
        return this.multiply(number.value);
    }

    public Decimal divide(Decimal number) {
        return this.divide(number.value);
    }

    public Decimal divide(Decimal number, int newScale, RoundingMode roundingMode) {
        return this.divide(number.value, newScale, roundingMode);
    }
}
