package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockInDetail;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 其他入库单详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-30
 */
public interface IOtherStockInDetailService extends IDaddyService<OtherStockInDetail> {

}
