package com.daddylab.supplier.item.infrastructure.third.mallItem;

import com.daddylab.supplier.item.infrastructure.third.mallItem.domain.dto.MallItemChangeDTO;

/**
 * <AUTHOR>
 * @class MallItemService.java
 * @description 自研商城商品服务
 * @date 2024-03-06 09:59
 */
public interface MallItemService {

    /**
     * 变更事件处理
     *
     * @param mallItemChangeDTO MallItemChangeDTO
     * @date 2024/3/6 10:08
     * <AUTHOR>
     */
    void handler(MallItemChangeDTO mallItemChangeDTO);
}
