package com.daddylab.supplier.item.infrastructure.gatewayimpl.refundStockInOrder;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.refundStockInOrder.WdtRefundStockInOrderRepository;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundStockInOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundStockInOrderDetails;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundStockInOrderDetailsRefundOrderDetails;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtRefundStockInOrderDetailsRefundOrderDetailsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtRefundStockInOrderDetailsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtRefundStockInOrderService;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2022/8/19
 */
@Repository
public class WdtRefundStockInOrderRepositoryImpl implements WdtRefundStockInOrderRepository {

    private final IWdtRefundStockInOrderService wdtRefundStockInOrderService;
    private final IWdtRefundStockInOrderDetailsService wdtRefundStockInOrderDetailsService;
    private final IWdtRefundStockInOrderDetailsRefundOrderDetailsService wdtRefundStockInOrderDetailsRefundOrderDetailsService;

    public WdtRefundStockInOrderRepositoryImpl(
            IWdtRefundStockInOrderService wdtRefundStockInOrderService,
            IWdtRefundStockInOrderDetailsService wdtRefundStockInOrderDetailsService,
            IWdtRefundStockInOrderDetailsRefundOrderDetailsService wdtRefundStockInOrderDetailsRefundOrderDetailsService) {
        this.wdtRefundStockInOrderService = wdtRefundStockInOrderService;
        this.wdtRefundStockInOrderDetailsService = wdtRefundStockInOrderDetailsService;
        this.wdtRefundStockInOrderDetailsRefundOrderDetailsService = wdtRefundStockInOrderDetailsRefundOrderDetailsService;
    }

    @Transactional
    @Override
    public void saveOrUpdateWdtPreStockInOrderWithDetails(
            WdtRefundStockInOrder wdtRefundStockInOrder) {
        if (wdtRefundStockInOrder == null) {
            return;
        }

        saveOrUpdateWdtRefundStockInOrder(wdtRefundStockInOrder);
        saveOrUpdateWdtPreStockInOrderDetails(wdtRefundStockInOrder);
        saveOrUpdateWdtPreStockInOrderDetailsRefundOrderDetails(wdtRefundStockInOrder);
    }

    private void saveOrUpdateWdtPreStockInOrderDetailsRefundOrderDetails(
            WdtRefundStockInOrder wdtRefundStockInOrder) {
        final LambdaQueryWrapper<WdtRefundStockInOrderDetailsRefundOrderDetails> removeRefundOrderDetailsCond = Wrappers.lambdaQuery();
        removeRefundOrderDetailsCond.eq
                (
                        WdtRefundStockInOrderDetailsRefundOrderDetails::getStockinId,
                        wdtRefundStockInOrder.getStockinId());
        wdtRefundStockInOrderDetailsRefundOrderDetailsService.remove(removeRefundOrderDetailsCond);

        final List<WdtRefundStockInOrderDetails> detailsList = wdtRefundStockInOrder.getDetailsList();
        final List<WdtRefundStockInOrderDetailsRefundOrderDetails> refundOrderDetailsList = detailsList.stream()
                .flatMap(v -> v.getRefundOrderDetailList().stream()).collect(Collectors.toList());
        wdtRefundStockInOrderDetailsRefundOrderDetailsService.saveBatch(refundOrderDetailsList);
    }

    private void saveOrUpdateWdtPreStockInOrderDetails(
            WdtRefundStockInOrder wdtRefundStockInOrder) {
        final List<WdtRefundStockInOrderDetails> wdtRefundStockInOrderDetailsLocal = wdtRefundStockInOrderDetailsService.lambdaQuery()
                .eq(WdtRefundStockInOrderDetails::getStockinId,
                        wdtRefundStockInOrder.getStockinId())
                .select(WdtRefundStockInOrderDetails::getRecId, WdtRefundStockInOrderDetails::getId)
                .list();

        final List<WdtRefundStockInOrderDetails> detailsList = wdtRefundStockInOrder.getDetailsList();
        if (CollectionUtil.isEmpty(detailsList) && CollectionUtil.isEmpty(
                wdtRefundStockInOrderDetailsLocal)) {
            return;
        }

        final Map<Long, Long> detailsRecIdsToLocalIdMap = wdtRefundStockInOrderDetailsLocal.stream()
                .collect(
                        Collectors.toMap(WdtRefundStockInOrderDetails::getRecId,
                                WdtRefundStockInOrderDetails::getId));

        final List<Long> orderDetailsRecIdsRemote = detailsList.stream()
                .map(WdtRefundStockInOrderDetails::getRecId).collect(
                        Collectors.toList());

        final List<Long> orderDetailsRecIdsLocal
                = wdtRefundStockInOrderDetailsLocal.stream()
                .map(WdtRefundStockInOrderDetails::getRecId).collect(
                        Collectors.toList());

        final Collection<Long> needAddedDetailsRecIds = CollectionUtil.subtract(
                orderDetailsRecIdsRemote,
                orderDetailsRecIdsLocal);

        final Collection<Long> needRemovedDetailsRecIds = CollectionUtil.subtract(
                orderDetailsRecIdsLocal, orderDetailsRecIdsRemote);

        final Collection<Long> needUpdatedDetailsRecIds = CollectionUtil.intersection(
                orderDetailsRecIdsRemote, orderDetailsRecIdsLocal
        );

        if (CollectionUtil.isNotEmpty(needAddedDetailsRecIds)) {
            final List<WdtRefundStockInOrderDetails> needAddedDetails = detailsList
                    .stream().filter(v -> needAddedDetailsRecIds.contains(v.getRecId()))
                    .collect(Collectors.toList());
            wdtRefundStockInOrderDetailsService.saveBatch(needAddedDetails);

        }
        if (CollectionUtil.isNotEmpty(needRemovedDetailsRecIds)) {
            wdtRefundStockInOrderDetailsService.removeByIds(
                    needRemovedDetailsRecIds.stream().map(detailsRecIdsToLocalIdMap::get)
                            .filter(Objects::nonNull).collect(Collectors.toList())
            );
        }
        if (CollectionUtil.isNotEmpty(needUpdatedDetailsRecIds)) {
            final List<WdtRefundStockInOrderDetails> needUpdatedDetails = detailsList
                    .stream().filter(v -> needUpdatedDetailsRecIds.contains(v.getRecId()))
                    .peek(v -> v.setId(detailsRecIdsToLocalIdMap.get(v.getRecId())))
                    .collect(Collectors.toList());
            wdtRefundStockInOrderDetailsService.updateBatchById(needUpdatedDetails);
        }
    }

    private void saveOrUpdateWdtRefundStockInOrder(WdtRefundStockInOrder wdtRefundStockInOrder) {
        final Long id = wdtRefundStockInOrderService.lambdaQuery()
                .eq(WdtRefundStockInOrder::getStockinId,
                        wdtRefundStockInOrder.getStockinId())
                .select(WdtRefundStockInOrder::getId).oneOpt()
                .map(WdtRefundStockInOrder::getId)
                .orElse(null);
        wdtRefundStockInOrder.setId(id);
        if (Objects.isNull(wdtRefundStockInOrder.getId())) {
            wdtRefundStockInOrderService.save(wdtRefundStockInOrder);
        } else {
            wdtRefundStockInOrderService.updateById(wdtRefundStockInOrder);
        }
    }
}
