package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <p>
 * sku价格说明表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SkuPriceExplain implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * sku编码
     */
    private String skuCode;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 操作时间
     */
    private String operateTime;

    /**
     * 说明
     */
    private String remark;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SkuPriceExplain explain = (SkuPriceExplain) o;
        return Objects.equals(skuCode, explain.skuCode) && Objects.equals(price, explain.price) && Objects.equals(operateTime, explain.operateTime) && Objects.equals(remark, explain.remark);
    }

    @Override
    public int hashCode() {
        return Objects.hash(skuCode, price, operateTime, remark);
    }

    @Override
    public String toString() {
        return "系统时间:" + operateTime + ",描述:" + remark;
    }
}
