package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Organization;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrganizationMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrganizationService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 * 采购组织表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@Service
public class OrganizationServiceImpl extends DaddyServiceImpl<OrganizationMapper, Organization> implements IOrganizationService {

    @Override
    public Boolean isSyncKingDeeOrg(Long id) {
        Organization organization = this.getById(id);
        return Objects.nonNull(organization) && !"0".equals(organization.getKingDeeId());
    }
}
