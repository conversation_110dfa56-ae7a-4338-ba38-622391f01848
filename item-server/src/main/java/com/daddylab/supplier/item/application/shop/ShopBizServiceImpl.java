package com.daddylab.supplier.item.application.shop;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.application.bizLevelDivision.BizLevelDivisionConvert;
import com.daddylab.supplier.item.application.shop.domain.ShopOperatorMapVO;
import com.daddylab.supplier.item.application.shop.event.ShopChangeEvent;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.shop.dto.*;
import com.daddylab.supplier.item.domain.shop.entity.ShopEntity;
import com.daddylab.supplier.item.domain.shop.enums.ShopStatus;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.domain.shop.trans.ShopTransMapper;
import com.daddylab.supplier.item.domain.shop.vo.ShopPrincipalVo;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.bizLevelDivision.BizDivisionContext;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BizUnionTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBizLevelDivisionService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopOperatorMapService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ISysInventorySettingService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLock;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.inject.Inject;
import javax.inject.Named;
import lombok.NonNull;
import org.javers.core.diff.Diff;
import org.javers.core.diff.changetype.PropertyChange;
import org.javers.core.diff.changetype.container.CollectionChange;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ShopBizServiceImpl implements ShopBizService {

  @Inject private ShopGateway shopGateway;

  @Inject
  @Named("UserGatewayCacheImpl")
  private UserGateway userGatewayCacheImpl;

  @Resource private ISysInventorySettingService iSysInventorySettingService;

  @Resource IShopOperatorMapService iShopOperatorMapService;

  @Resource private StaffService staffService;

  @Resource IBizLevelDivisionService bizLevelDivisionService;

  @Override
  public MultiResponse<ShopDropDownItem> dropDownList(ShopDropDownQuery query) {
    return MultiResponse.of(shopGateway.dropDownList(query));
  }

  @Override
  public MultiResponse<ShopListItem> queryShopList(ShopQuery shopQuery) {
    final List<Shop> shops = shopGateway.queryShopList(shopQuery);
    final List<ShopListItem> shopListItems = assembleShopListItems(shops);
    return MultiResponse.of(shopListItems);
  }

  @Override
  public PageResponse<ShopListItem> pageQueryShopList(ShopQuery shopQuery) {
    final Page<Shop> shopPage =
        BizDivisionContext.invoke(
            BizUnionTypeEnum.SHOP, () -> shopGateway.pageQueryShopList(shopQuery));
    final List<ShopListItem> shopListItems = assembleShopListItems(shopPage.getRecords());
    return PageResponse.of(
        shopListItems,
        (int) shopPage.getTotal(),
        (int) shopPage.getSize(),
        (int) shopPage.getCurrent());
  }

  private List<ShopListItem> assembleShopListItems(List<Shop> shops) {
    final List<Long> shopIds = shops.stream().map(Shop::getId).collect(Collectors.toList());
    final List<ShopPrincipal> shopPrincipals = shopGateway.batchQueryShopPrincipals(shopIds);
    final List<Long> staffIds =
        shopPrincipals.stream().map(ShopPrincipal::getStaffId).collect(Collectors.toList());
    final Map<Long, StaffInfo> staffInfoMap =
        userGatewayCacheImpl.batchQueryStaffInfoByIds(staffIds);
    final Map<Long, List<ShopPrincipal>> shopPrincipalsGroup =
        shopPrincipals.stream().collect(Collectors.groupingBy(ShopPrincipal::getShopId));
    return shops.stream()
        .map(
            shop -> {
              final List<ShopPrincipalVo> shopPrincipalsThisShop =
                  shopPrincipalsGroup.getOrDefault(shop.getId(), Collections.emptyList()).stream()
                      .map(
                          shopPrincipal ->
                              ShopPrincipalVo.of(
                                  shopPrincipal.getStaffId(),
                                  Optional.ofNullable(staffInfoMap.get(shopPrincipal.getStaffId()))
                                      .map(StaffInfo::getNickname)
                                      .orElse("")))
                      .collect(Collectors.toList());
              return ShopTransMapper.INSTANCE.toShopListItem(shop, shopPrincipalsThisShop);
            })
        .collect(Collectors.toList());
  }

  @Override
  public SingleResponse<ShopDetail> getShopDetail(Long id) {
    final ShopEntity shopEntity = shopGateway.getShopEntity(id);
    if (shopEntity == null)
      throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "未查询到ID为" + id + "的店铺");
    return SingleResponse.of(toShopDetail(shopEntity));
  }

  private ShopDetail toShopDetail(ShopEntity shopEntity) {
    final ArrayList<ShopPrincipalVo> shopPrincipalVos = new ArrayList<>();
    if (shopEntity.getPrincipals() != null) {
      final Map<Long, StaffInfo> staffInfos =
          userGatewayCacheImpl.batchQueryStaffInfoByIds(shopEntity.getPrincipals());
      for (Long principalId : shopEntity.getPrincipals()) {
        shopPrincipalVos.add(
            ShopPrincipalVo.of(
                principalId,
                Optional.ofNullable(staffInfos.get(principalId))
                    .map(StaffInfo::getNickname)
                    .orElse("")));
      }
    }
    ShopDetail shopDetail = ShopTransMapper.INSTANCE.toShopDetail(shopEntity, shopPrincipalVos);

    List<SysInventorySetting> list = iSysInventorySettingService.list();
    if (CollUtil.isNotEmpty(list)) {
      SysInventorySetting sysInventorySetting = iSysInventorySettingService.list().get(0);
      shopDetail.setPermitShopSyncFreqSetting(sysInventorySetting.getPermitShopSyncFreqSetting());
    }

    final List<BizLevelDivision> bizLevelDivisions =
        bizLevelDivisionService.selectByTypeAndBizId(BizUnionTypeEnum.SHOP, shopEntity.getId());
    shopDetail.setCorpType(
        BizLevelDivisionConvert.INSTANCE.toBizLevelIntegerList(
            bizLevelDivisions, DivisionLevelEnum.COOPERATION));
    shopDetail.setBusinessLine(shopDetail.getCorpType());
    shopDetail.setRunningMode(
        BizLevelDivisionConvert.INSTANCE.toBizLevelIntegerList(
            bizLevelDivisions, DivisionLevelEnum.RUNNING_MODEL));
    return shopDetail;
  }

  @Override
  public Response updateShopStatus(Long id, ShopStatus status) {
    shopGateway.updateShopStatus(id, status);
    return Response.buildSuccess();
  }

  @Override
  public Response deleteShop(Long id) {
    shopGateway.deleteShop(id);
    return Response.buildSuccess();
  }

  @SuppressWarnings("unchecked")
  @Override
  @DistributedLock
  @Transactional
  public SingleResponse<ShopDetail> createOrUpdateShop(CreateOrUpdateShopCmd cmd) {
    ShopEntity shop;
    if (Objects.isNull(cmd.getId())) {
      shop = ShopTransMapper.INSTANCE.toShopEntity(cmd);
      // 新增时生成编号
      shop.setSn(generateShopSn());
      // 持久化
      shopGateway.createOrUpdateShop(shop);
      // 店铺负责人
      shopGateway.addShopPrincipals(
          shop.getId(), Optional.ofNullable(cmd.getPrincipals()).orElse(Collections.emptyList()));
      // 店铺库存信息
//      shopGateway.saveShopInventorySetting(shop.getId(), shop.getSn(), cmd);

      EventBusUtil.post(ShopChangeEvent.ofNew(UserContext.getUserId(), shop.getId(), shop));
    } else {
      final ShopEntity oldShop = shopGateway.getShopEntity(cmd.getId());
      shop = ShopTransMapper.INSTANCE.copy(oldShop);
      ShopTransMapper.INSTANCE.copyFromCmd(shop, cmd);
      shopGateway.createOrUpdateShop(shop);

      // 店铺库存信息
//      shopGateway.saveShopInventorySetting(shop.getId(), shop.getSn(), cmd);

      final Diff diff = DiffUtil.diff(oldShop, shop);
      final List<PropertyChange> principalsChanges = diff.getPropertyChanges("店铺负责人");
      if (!principalsChanges.isEmpty()) {
        final CollectionChange principalsChange = (CollectionChange) principalsChanges.get(0);

        final List<Long> addedPrincipals = (List<Long>) principalsChange.getAddedValues();
        if (!addedPrincipals.isEmpty()) {
          shopGateway.addShopPrincipals(shop.getId(), addedPrincipals);
        }

        final List<Long> removedPrincipals = (List<Long>) principalsChange.getRemovedValues();
        if (!removedPrincipals.isEmpty()) {
          shopGateway.removeShopPrincipals(shop.getId(), removedPrincipals);
        }
      }

      EventBusUtil.post(
          ShopChangeEvent.ofUpdate(UserContext.getUserId(), shop.getId(), diff, oldShop, shop));
    }
    final List<DivisionLevelValueEnum> divisionLevelValueEnums =
        DivisionLevelValueEnum.valueOf(cmd.getCorpType(), shop.getRunningMode());
    bizLevelDivisionService.savePlainLevels(
        BizUnionTypeEnum.SHOP,
        shop.getId(), shop.getSn(),
        divisionLevelValueEnums,
        Arrays.asList(DivisionLevelEnum.COOPERATION, DivisionLevelEnum.RUNNING_MODEL));

    return SingleResponse.of(toShopDetail(shop));
  }

  @NonNull
  private String generateShopSn() {
    return "D" + StringUtil.format("{0,number,0000}", shopGateway.countShop() + 1);
  }

  @Override
  public PageResponse<ShopOperatorMapVO> offShelfShopOperatorMappingPage(
      OffShelfShopOperatorPageQuery queryPage) {
    final PageInfo<ShopOperatorMapVO> pageInfo =
        PageHelper.startPage(queryPage.getPageIndex(), queryPage.getPageSize())
            .doSelectPageInfo(
                () ->
                    iShopOperatorMapService
                        .getDaddyBaseMapper()
                        .shopOperatorMapPageQuery(queryPage));
    for (ShopOperatorMapVO shopOperatorMapVO : pageInfo.getList()) {
      staffService
          .getStaffBrief(shopOperatorMapVO.getOperatorUid())
          .map(StaffBrief::getNickname)
          .ifPresent(shopOperatorMapVO::setOperatorName);
    }
    return PageResponse.of(
        pageInfo.getList(),
        (int) pageInfo.getTotal(),
        queryPage.getPageIndex(),
        queryPage.getPageSize());
  }

  @Override
  public SingleResponse<Boolean> offShelfShopOperatorMappingSave(OffShelfShopOperatorCmd cmd) {
    try {
      ShopOperatorMap map = new ShopOperatorMap();
      map.setShopId(cmd.getShopId());
      map.setOperatorUid(cmd.getOperatorUid());
      if (Objects.nonNull(cmd.getId()) && cmd.getId() != 0) {
        map.setId(cmd.getId());
      }
      iShopOperatorMapService.saveOrUpdate(map);
      return SingleResponse.of(true);
    } catch (DuplicateKeyException e) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DUPLICATE_DATA, "店铺已添加，请刷新页面后重试");
    }
  }

  @Override
  public SingleResponse<Boolean> offShelfShopOperatorMappingRemove(Long id) {
    iShopOperatorMapService.removeById(id);
    return SingleResponse.of(true);
  }
}
