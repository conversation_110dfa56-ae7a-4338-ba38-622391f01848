package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/11 10:03 上午
 * @description
 */
@Getter
public enum CombinationItemType implements IEnum {

    COMMON(0,"普通件"),

    COMMON_BIG(1,"普通大件"),

    INDEPENDENT(2,"独立件");

    @EnumValue
    private Integer value;
    private String desc;

    CombinationItemType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
