package com.daddylab.supplier.item.application.platformItem;

import com.daddylab.supplier.item.controller.open.PlatformItemOpenQuery;
import com.daddylab.supplier.item.domain.platformItem.vo.PlatformItemOpenVO;

import java.util.List;

public interface PlatformItemOpenService {
    /**
     * 查询平台商品数据（带规格）
     *
     * @param query 供应商商品编码
     * @return PO
     */
    List<PlatformItemOpenVO> openListQuery(PlatformItemOpenQuery query);
}
