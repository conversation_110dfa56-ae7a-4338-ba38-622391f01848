package com.daddylab.supplier.item.application.stockOutOrder;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuVO;
import com.daddylab.supplier.item.controller.purchase.dto.order.CallBackOrderStateCmd;
import com.daddylab.supplier.item.controller.stockout.dto.*;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockOutOrder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/24 17:55
 * @description StockOutOrderBizService
 */
public interface StockOutOrderBizService {

    /**
     * 分页查询出库单
     */
    PageResponse<StockOutOrderVO> pageStockOutOrder(StockOutOrderQuery stockOutOrderQuery);

    /**
     * 创建或更新出库单
     */
    Response createOrUpdateStockOutOrder(StockOutOrderCmd stockOutOrderCmd);

    /**
     * 删除出库单
     */
    Response deleteStockOutOrder(Long id);

    /**
     * 根据出库单id查询出库单详情
     */
    SingleResponse<StockOutOrderViewVO> getStockOutOrder(Long id);

    /**
     * 取消出库单
     */
    Response cancelStockOutOrder(StockOutOrderIdDTO stockOutOrderIdDTO);

    /**
     * 撤回出库单
     */
    Response revocationStockOutOrder(StockOutOrderIdDTO stockOutOrderIdDTO);

    /**
     * 导出
     */
    Response exportExcel(StockOutOrderQuery stockOutOrderQuery);

    /**
     * 手动出库
     */
    Response manualStockOutOrder(Long stockOutOrderId);

    /**
     * 商品库存列表
     */
    PageResponse<ComposeSkuVO> pageSku(ComposeSkuPageQuery query);

    /**
     * @return
     */
    List<RemindOutQuantityVO> queryRemindStockOutQuantity(StockOutQuantityCmd cmd);

    // ---------------------------------    内部调用 -------------------------------

    /**
     * 根据采购单id获取出库单id列表
     *
     * @param purchaseOrderId 采购单id
     * @return 出库单id列表
     */
    List<StockOutOrder> getStockOutOrderByPurchaseOrderId(Long purchaseOrderId);

    /**
     * 修改状态
     */
    void updateState(Long stockOutOrderId, Integer state);

    SingleResponse<String> syncStockOutOrder(StockOutOrderCmd stockOutOrderCmd,Boolean mockSync);

    /**
     * 获取待办列表
     * @return 待办单据ID
     */
    MultiResponse<Long> getTodoList();

    void toDoSummaryReminder(String qwUserId, List<Long> purchaseOrderIds);

    Response callBackOrderState(CallBackOrderStateCmd cmd);


    void stockOutOrderSyncWDT(StockOutOrder stockOutOrder);

}
