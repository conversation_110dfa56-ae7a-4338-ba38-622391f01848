package com.daddylab.supplier.item.application.purchasePayable.vo;

import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PurchasePayableOrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel("采购应付单返回结果")
public class PurchasePayableVO implements Serializable {

    private static final long serialVersionUID = -2747087868184137048L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "应付单号")
    private String no;

    /**
     * {@link PurchaseTypeEnum}
     */
    @ApiModelProperty(value = "应付类型: 1采购入库应付、2采退出库应付、3其他应付")
    private Integer type;

    @ApiModelProperty(value = "应付类型名称")
    private String typeName;

    @ApiModelProperty(value = "业务日期")
    private Long bizDate;

    @ApiModelProperty(value = "供应商id")
    private Long providerId;

    @ApiModelProperty(value = "供应商名称")
    private String providerName;

    @ApiModelProperty("是否黑名单 0否 1是")
    private Integer isBlacklist;

    @ApiModelProperty(value = "商品种类数量")
    private Integer kindQuantity;

    @ApiModelProperty(value = "商品总数")
    private Integer totalItemQuantity;

    @ApiModelProperty(value = "价税合计")
    private BigDecimal totalPriceTax;

    @ApiModelProperty(value = "采购组织id")
    private Long organizationId;

    @ApiModelProperty(value = "采购组织名称")
    private String organizationName;

    @ApiModelProperty(value = "关联单据(指采购入库单号/采退出库单号)")
    private String relatedOrderNo;

    @ApiModelProperty(value = "关联单据(指采购入库单ID/采退出库单ID)")
    private Long relatedOrderId;

    @ApiModelProperty("是否还可以生成付款申请单")
    private Boolean canApplyPay;

//    @ApiModelProperty("是否是冲销单据")
//    private Integer isHedgeData;

    private Integer businessLine;


    @ApiModelProperty("DEFAULT，WRITE_OFF 作废，REVERSE_FIXED 冲销对冲单据.FIXED_ORDER:冲销,新生成的修正的单据")
    private PurchasePayableOrderStatus status;


}
