package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.daddylab.supplier.item.domain.dataFetch.RunData;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据同步记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataFetch implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据类型 1:旺店平台商品数据同步 2:系统平台商品 3:...
     */
    private Integer dataType;

    /**
     * 同步时间点（当前记录对应的同步时间范围是从当前记录的同步时间点到下一条记录的同步时间点）
     */
    private LocalDateTime fetchPoint;

    /**
     * 当前同步任务开始执行时添加独占锁，锁定当前记录直到超时，超时后锁定失效可以被重试
     */
    private LocalDateTime lockTime;

    /**
     * 状态 0:未同步完成 1:同步完成 2:同步异常（多次同步失败）
     */
    private Integer status;

    /**
     * 异常原因
     */
    private String err;

    /**
     * 拓展字段，额外数据（执行过程中认为有必要记录的运行信息）
     */
    private String data;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 当前拉取周期
     */
    private Integer round;

    public RunData parseRunData() {
        RunData runData;
        if (StrUtil.isNotBlank(getData())) {
            try {
                runData = JsonUtil.parse(getData(), RunData.class);
            } catch (IllegalStateException e) {
                runData = new RunData();
            }
        } else {
            runData = new RunData();
        }
        return runData;
    }
}
