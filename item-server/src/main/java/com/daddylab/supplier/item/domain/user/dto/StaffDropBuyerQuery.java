package com.daddylab.supplier.item.domain.user.dto;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @ClassName StaffDropBuyerQuery.java
 * @description
 * @createTime 2022年05月11日 10:04:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("采购花名下拉列表查询")
public class StaffDropBuyerQuery extends PageQuery {
    private static final long serialVersionUID = -671542255509512696L;

    @ApiModelProperty("非必填。如果存在值，搜索(花名)")
    private String name;

}
