package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 旺店通预入库单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtPreStockInOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 入库单号
     */
    private String stockinNo;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 源单号
     */
    private String srcOrderNo;

    /**
     * 源单号类型
     */
    private Integer srcOrderType;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 物流名称
     */
    private String logisticsName;

    /**
     * 状态 10:已取消;20编辑中;30:待审核;80:已完成
     */
    private Integer status;

    /**
     * 货品数量
     */
    private BigDecimal goodsCount;

    /**
     * 货品种类数
     */
    private Integer goodsTypeCount;

    /**
     * 便签数量
     */
    private Integer noteCount;

    /**
     * 退货人姓名
     */
    private String senderName;

    /**
     * 退货人手机
     */
    private String senderMobile;

    /**
     * 客户网名
     */
    private String buyerNick;

    /**
     * 标记名称
     */
    private String flagName;

    /**
     * 入库人姓名
     */
    private String operatorName;

    /**
     * 审核员姓名
     */
    private String checkerName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核时间
     */
    private LocalDateTime checkTime;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    @TableField(exist = false)
    private List<WdtPreStockInOrderDetails> detailList;


}
