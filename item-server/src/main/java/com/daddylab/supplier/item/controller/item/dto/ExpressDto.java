package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/20 6:45 下午
 * @description
 */
@Data
@ApiModel("快递模板")
public class ExpressDto {

    @ApiModelProperty("模板id。新建不填，编辑必填")
    Long id;

    @ApiModelProperty("始发地")
    String fromArea;

    @ApiModelProperty("快递公司")
    String expressCompany;

    @ApiModelProperty("包邮区")
    String freeArea;

    @ApiModelProperty("不包邮区")
    String chargeArea;

    @ApiModelProperty("快递模板")
    String area;

    @ApiModelProperty("备注")
    String remark;

}
