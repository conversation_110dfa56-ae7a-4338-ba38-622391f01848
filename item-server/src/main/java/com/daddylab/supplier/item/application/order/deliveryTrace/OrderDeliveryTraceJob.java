package com.daddylab.supplier.item.application.order.deliveryTrace;

import cn.hutool.core.util.StrUtil;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.domain.wdt.consts.PlatformMap;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderDeliveryTraceMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.timing.StopWatch;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.types.CountGroup;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderDeliveryTraceJob {
    public static final int BATCH_SIZE = 10000;
    private final IOrderDeliveryTraceService orderDeliveryTraceService;
    private final OrderDeliveryTraceMapper orderDeliveryTraceMapper;
    private final IWdtOrderService wdtOrderService;
    private final IWarehouseService warehouseService;
    private final StaffService staffService;
    private final MsgSender msgSender;
    private final IWechatMsgService wechatMsgService;
    private final RefreshConfig refreshConfig;

    @XxlJob("OrderDeliveryTraceJob:notice")
    public void notice() {
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start("query");
        final List<CountGroup<Long>> countGroups = orderDeliveryTraceMapper.countByOrderPersonal();
        stopWatch.stop();
        final List<Long> orderPersonalIds =
                countGroups.stream().map(CountGroup::getId).distinct().collect(Collectors.toList());
        final Map<Long, DadStaffVO> staffMap = staffService.getStaffMap(orderPersonalIds);
        log.info("【订单发货跟踪 NOTICE】查询完成，筛选结果={} time={}", countGroups, stopWatch);

        stopWatch.start("send");
        for (CountGroup<Long> countGroup : countGroups) {
            final Long personalId = countGroup.getId();
            final DadStaffVO dadStaffVO = staffMap.get(personalId);
            if (dadStaffVO == null) {
                log.error("【订单发货跟踪 NOTICE】员工信息查询异常 orderPersonalId:{}", personalId);
                continue;
            }
            final WechatMsg wechatMsg = new WechatMsg();
            wechatMsg.setTitle(
                    String.format("今日统计【已审核，未发货】状态订单共%s单，请联系仓库发货！", countGroup.getCount()));
            wechatMsg.setContent("您有一个新的待办任务，请尽快处理！");
            wechatMsg.setLink(
                    String.format(
                            refreshConfig.getDomain()
                                    + "/order-management/tracking-list?orderPersonnelId=%s&orderPersonnel=%s",
                            personalId,
                            dadStaffVO.getNickname()));
            wechatMsg.setState(0);
            wechatMsg.setRecipient(dadStaffVO.getQwUserId());
            wechatMsg.setType(1);
            wechatMsg.setRequirement(personalId.toString());
            wechatMsgService.save(wechatMsg);
            if (refreshConfig.getOrderDeliveryTraceMsgRealSend()) {
                msgSender.send(wechatMsg);
            }
        }
        stopWatch.stop();
        log.info("【订单发货跟踪 NOTICE】发送完成，筛选结果={} time={}", countGroups, stopWatch);
    }

    @XxlJob("OrderDeliveryTraceJob:filter")
    public void filter() {
        final StopWatch stopWatch = new StopWatch();

        stopWatch.start("prepare");
        final List<Warehouse> warehouses = warehouseService.list();
        final Map<String, Warehouse> warehousesMap =
                warehouses.stream()
                        .collect(Collectors.toMap(Warehouse::getNo, Function.identity()));
        final LocalDateTime now = LocalDateTime.now();
        final LocalDateTime time24hourAgo = now.minusDays(1);
        final LocalDateTime time7dayAgo = now.minusDays(7).with(LocalTime.MIN);
        final LocalDateTime time1monthAgo = now.minusMonths(1);
        stopWatch.stop();

        log.info("【订单发货跟踪】开始筛选 time:{}", stopWatch);
        stopWatch.start("filter");

        int pageNum = 1;
        while (true) {
            final StopWatch stopWatch1 = new StopWatch();
            stopWatch1.start("query");
            final LambdaQueryChainWrapper<WdtOrder> queryWrapper = wdtOrderService.lambdaQuery();
            final Page<WdtOrder> page = new Page<>(pageNum++, BATCH_SIZE, false);
            queryWrapper
                    // 审核超过24个小时
                    .le(WdtOrder::getCheckTime, time24hourAgo)
                    // 未发货/已审核
                    .eq(WdtOrder::getTradeStatus, 55)
                    // 下单时间在7天内 或者 审核时间在一个月之内
                    .and(
                            sub ->
                                    sub.ge(WdtOrder::getPayTime, time7dayAgo)
                                            .or()
                                            .ge(WdtOrder::getCheckTime, time1monthAgo))
                    .page(page);
            stopWatch1.stop();
            final List<WdtOrder> list = page.getRecords();
            log.info(
                    "【订单发货跟踪】查询耗时 time:{} count:{} page:{} limit:{}",
                    stopWatch1,
                    list.size(),
                    page.getCurrent(),
                    BATCH_SIZE);

            if (list.isEmpty()) {
                break;
            }

            stopWatch1.start("handle");
            for (WdtOrder wdtOrder : list) {
                final OrderDeliveryTrace exists =
                        orderDeliveryTraceService
                                .lambdaQuery()
                                .eq(OrderDeliveryTrace::getTradeNo, wdtOrder.getTradeNo())
                                .one();
                final OrderDeliveryTrace orderDeliveryTrace = new OrderDeliveryTrace();

                final long timestamp = DateUtil.getNowSecond();
                if (exists == null) {
                    orderDeliveryTrace.setCreatedAt(timestamp);
                    orderDeliveryTrace.setUpdatedAt(timestamp);
                    orderDeliveryTrace.setOrderNo(wdtOrder.getSrcTids());
                    orderDeliveryTrace.setTradeNo(wdtOrder.getTradeNo());
                    orderDeliveryTrace.setShopNo(wdtOrder.getShopNo());
                    orderDeliveryTrace.setShopName(wdtOrder.getShopName());
                    orderDeliveryTrace.setPlatform(
                            PlatformMap.mapPlatform(wdtOrder.getPlatformId()));
                    final String warehouseNo = wdtOrder.getWarehouseNo();
                    orderDeliveryTrace.setWarehouseNo(warehouseNo);
                    final Optional<Warehouse> warehouseOptional =
                            Optional.ofNullable(warehousesMap.get(warehouseNo));
                    orderDeliveryTrace.setWarehouseName(
                            warehouseOptional.map(Warehouse::getName).orElse(""));
                    orderDeliveryTrace.setOrderPersonnelId(
                            warehouseOptional
                                    .map(Warehouse::getOrderPersonnel)
                                    .flatMap(
                                            v ->
                                                    StrUtil.split(v, ',', true, true).stream()
                                                            .map(Long::parseLong)
                                                            .findFirst())
                                    .orElse(0L));
                    orderDeliveryTrace.setCheckTime(wdtOrder.getCheckTime());
                    orderDeliveryTraceService.save(orderDeliveryTrace);
                } else {
                    orderDeliveryTrace.setId(exists.getId());
                    orderDeliveryTrace.setUpdatedAt(timestamp);
                    orderDeliveryTraceService.updateById(orderDeliveryTrace);
                }
            }
            stopWatch1.stop();

            log.info(
                    "【订单发货跟踪】处理耗时 time:{} count:{} page:{} limit:{}",
                    stopWatch1,
                    list.size(),
                    page.getCurrent(),
                    BATCH_SIZE);

            if (!page.hasNext()) {
                break;
            }
        }
        stopWatch.stop();
        log.info("【订单发货跟踪】筛选完成 time:{}", stopWatch);

        stopWatch.start("clean");
        orderDeliveryTraceService
                .lambdaUpdate()
                .le(
                        OrderDeliveryTrace::getUpdatedAt,
                        LocalDateTime.now()
                                .with(LocalTime.MIN)
                                .atZone(ZoneId.systemDefault())
                                .toEpochSecond())
                .remove();
        stopWatch.stop();
        log.info("【订单发货跟踪】清理完成 time:{}", stopWatch);
    }
}
