package com.daddylab.supplier.item.infrastructure.validators.link;

import static cn.hutool.core.collection.CollUtil.distinct;
import static cn.hutool.core.collection.CollUtil.join;
import static cn.hutool.core.collection.CollUtil.reverse;

import cn.hutool.core.util.ReflectUtil;

import com.daddylab.supplier.item.infrastructure.utils.StringUtil;

import io.reactivex.rxjava3.core.Flowable;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;

import lombok.NonNull;

import org.apache.commons.compress.utils.Lists;
import org.hibernate.validator.internal.engine.path.NodeImpl;
import org.hibernate.validator.internal.engine.path.PathImpl;
import org.springframework.core.annotation.AnnotationUtils;

import java.lang.reflect.Executable;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

import javax.validation.ConstraintViolation;
import javax.validation.ElementKind;
import javax.validation.Path;

/**
 * <AUTHOR>
 * @since 2022/12/13
 */
public class ValidateHelper {

    public static final String PARAMETER_PROMPT = "";
    public static final String PARAMETER_PREFIX = "[";
    public static final String PARAMETER_SUFFIX = "]";
    public static final String SEPARATOR = "; ";

    public static <T extends ConstraintViolation<?>> String constraintsToOneLine(
            Set<T> constraintViolations) {
        final StringBuilder msg = new StringBuilder();
        final HashSet<String> errors = new HashSet<>();
        for (ConstraintViolation<?> constraintViolation : constraintViolations) {
            final String message = constraintViolation.getMessage();
            final Path propertyPath = constraintViolation.getPropertyPath();
            String propertyDesc = propertyPath.toString();
            if (propertyPath instanceof PathImpl) {
                List<String> propertyDescTmp = Lists.newArrayList();
                NodeImpl leafNode = ((PathImpl) propertyPath).getLeafNode();
                if (leafNode.getKind() == ElementKind.PROPERTY) {
                    while (leafNode.getParent() != null) {
                        if (leafNode.getKind() == ElementKind.PROPERTY) {
                            final NodeImpl parent = leafNode.getParent();
                            final Object parentBean = parent.getValue();
                            final Class<?> parentClass =
                                    parentBean != null
                                            ? parentBean.getClass()
                                            : constraintViolation.getRootBeanClass();
                            if (parentClass == null) {
                                break;
                            }
                            String propertyName =
                                    getApiModelPropertyValue(leafNode.getName(), parentClass);
                            // final String leafNodeStr = leafNode.toString();
                            // final int indexOfLeftBracket = leafNodeStr.indexOf("[");
                            // if (indexOfLeftBracket > -1) {
                            //    propertyName =
                            //            propertyName + leafNodeStr.substring(indexOfLeftBracket);
                            // }
                            propertyDescTmp.add(propertyName);
                        }
                        leafNode = leafNode.getParent();
                    }
                    final String leafProperty = propertyDescTmp.get(0);
                    propertyDesc = join(reverse(distinct(propertyDescTmp)), "->");
                    final String msgPart =
                            PARAMETER_PROMPT
                                    + PARAMETER_PREFIX
                                    + propertyDesc
                                    + PARAMETER_SUFFIX
                                    + filterDuplicateFieldNameInMsg(message, leafProperty)
                                    + SEPARATOR;
                    if (errors.add(msgPart)) {
                        msg.append(msgPart);
                    }
                } else if (leafNode.getKind() == ElementKind.PARAMETER) {
                    final int parameterIndex = leafNode.getParameterIndex();
                    final NodeImpl methodNode = leafNode.getParent();
                    final String parameterDesc =
                            Flowable.just(constraintViolation.getRootBeanClass())
                                    .concatMap(
                                            rootBeanClass -> {
                                                final ArrayList<Method> methods = new ArrayList<>();
                                                methods.add(
                                                        ReflectUtil.getMethodByName(
                                                                rootBeanClass,
                                                                methodNode.getName()));
                                                for (Class<?> anInterface :
                                                        rootBeanClass.getInterfaces()) {
                                                    methods.add(
                                                            ReflectUtil.getMethodByName(
                                                                    anInterface,
                                                                    methodNode.getName()));
                                                }
                                                return Flowable.fromStream(methods.stream());
                                            })
                                    .map(Executable::getParameters)
                                    .filter(parameters -> parameters.length > parameterIndex)
                                    .map(parameters -> parameters[parameterIndex])
                                    .map(
                                            parameter ->
                                                    Optional.ofNullable(
                                                            AnnotationUtils.getAnnotation(
                                                                    parameter, ApiParam.class)))
                                    .filter(Optional::isPresent)
                                    .map(Optional::get)
                                    .map(
                                            annotation ->
                                                    !annotation.value().isEmpty()
                                                            ? annotation.value()
                                                            : annotation.name())
                                    .firstElement()
                                    .blockingGet(propertyDesc);
                    final String msgPart =
                            PARAMETER_PROMPT
                                    + PARAMETER_PREFIX
                                    + parameterDesc
                                    + PARAMETER_SUFFIX
                                    + message
                                    + SEPARATOR;
                    if (errors.add(msgPart)) {
                        msg.append(msgPart);
                    }
                } else {
                    final String msgPart =
                            PARAMETER_PROMPT
                                    + PARAMETER_PREFIX
                                    + propertyDesc
                                    + PARAMETER_SUFFIX
                                    + message
                                    + SEPARATOR;
                    if (errors.add(msgPart)) {
                        msg.append(msgPart);
                    }
                }
            } else {
                final String propertyPathStr = propertyPath.toString();
                if (propertyPathStr.contains(".")) {
                    final List<String> propertyPathSplits = StringUtil.split(propertyPathStr, ".");
                    propertyDesc = propertyPathSplits.get(propertyPathSplits.size() - 1);
                }
                final Object leafBean = constraintViolation.getLeafBean();
                final String propertyName = getApiModelPropertyValue(propertyDesc, leafBean);
                final String msgPart =
                        PARAMETER_PROMPT
                                + PARAMETER_PREFIX
                                + propertyName
                                + PARAMETER_SUFFIX
                                + filterDuplicateFieldNameInMsg(message, propertyName)
                                + SEPARATOR;
                if (errors.add(msgPart)) {
                    msg.append(msgPart);
                }
            }
        }
        return msg.toString();
    }

    @NonNull
    private static String getApiModelPropertyValue(String fieldName, Class<?> beanClass) {
        final Field field = ReflectUtil.getField(beanClass, fieldName);
        if (field == null) {
            return fieldName;
        }
        return Optional.ofNullable(field.getAnnotation(ApiModelProperty.class))
                .map(anno -> !anno.name().isEmpty() ? anno.name() : anno.value())
                .map(
                        v -> {
                            final int indexOfSpace = v.indexOf(" ");
                            if (indexOfSpace > -1) {
                                return v.substring(0, indexOfSpace);
                            }
                            return v;
                        })
                .orElse(fieldName);
    }

    @NonNull
    private static String getApiModelPropertyValue(String fieldName, Object bean) {
        return getApiModelPropertyValue(fieldName, bean.getClass());
    }

    private static String filterDuplicateFieldNameInMsg(String msg, String fieldName) {
        if (msg.contains(fieldName)) {
            return msg.replaceFirst(fieldName, "");
        }
        return msg;
    }
}
