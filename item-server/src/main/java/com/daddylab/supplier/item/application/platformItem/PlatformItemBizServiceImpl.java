package com.daddylab.supplier.item.application.platformItem;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.daddylab.ark.sailor.item.domain.query.item.ItemPageQuery;
import com.daddylab.ark.sailor.item.domain.vo.item.ItemListVO;
import com.daddylab.ark.sailor.item.domain.vo.item.ItemSkuVO;
import com.daddylab.supplier.item.application.message.QyMsgSendService;
import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.application.platformItem.config.PlatformItemSyncConfig;
import com.daddylab.supplier.item.application.platformItem.data.*;
import com.daddylab.supplier.item.application.platformItem.query.OtherPlatformItemPageQuery;
import com.daddylab.supplier.item.application.platformItem.query.PlatformItemDropDownPageQuery;
import com.daddylab.supplier.item.application.platformItem.query.PlatformItemPageQuery;
import com.daddylab.supplier.item.application.platformItem.trans.PlatformItemTransMapper;
import com.daddylab.supplier.item.application.platformItemSkuInventory.PlatformItemSkuSyncService;
import com.daddylab.supplier.item.application.shop.ShopBizService;
import com.daddylab.supplier.item.application.stockSpec.ShopStockSpecBizService;
import com.daddylab.supplier.item.application.stockSpec.StockSpecBizService;
import com.daddylab.supplier.item.common.ErrorChecker;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.controller.item.ArkSailorItemBizService;
import com.daddylab.supplier.item.controller.platformItem.PlatformItemSyncCmd;
import com.daddylab.supplier.item.domain.category.CategoryUtils;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.messageRobot.gateway.MessageRobotGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.platformItem.service.PlatformItemSyncService;
import com.daddylab.supplier.item.domain.shop.dto.ShopDropDownItem;
import com.daddylab.supplier.item.domain.shop.dto.ShopDropDownQuery;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.domain.wdt.service.PlatformItemFetchService;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.enums.ArkItemStatusEnum;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemBaseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryAllocShopStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemImageType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MsgTemplateCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.item.ItemListWithSkuVO;
import com.daddylab.supplier.item.types.platformItem.SelectPlatformItemQuery;
import com.daddylab.supplier.item.types.platformItem.SelectPlatformItemSkuVO;
import com.daddylab.supplier.item.types.platformItem.SelectPlatformItemVO;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.apache.commons.collections4.multimap.HashSetValuedHashMap;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.daddylab.supplier.item.infrastructure.utils.MPUtil.limit;

/**
 * <AUTHOR>
 * @since 2021/12/28
 */
@Service
@Slf4j
public class PlatformItemBizServiceImpl implements PlatformItemBizService {
    private static final List<Pattern> LINK_PATTERNS = ImmutableList.<Pattern>builder()
            .add(Pattern.compile("\\s*https?://item\\.taobao\\.com/item\\.htm\\?id=(\\w+).*"))
            .add(Pattern.compile("\\s*https?://item\\.taobao\\.com/item\\.htm\\?.+&id=(\\w+).*")).build();
    
    @Autowired
    IPlatformItemService platformItemService;
    @Autowired
    IPlatformItemSkuService platformItemSkuService;
    @Autowired
    ItemGateway itemGateway;
    @Autowired
    WdtGateway wdtGateway;
    @Autowired
    ShopGateway shopGateway;
    @Autowired
    IItemService itemService;
    @Autowired
    IItemSkuService itemSkuService;
    @Autowired
    IPlatformItemWarnService platformItemWarnService;
    @Autowired
    PlatformItemSyncService platformItemSyncService;
    @Autowired
    PlatformItemSyncConfig platformItemSyncConfig;
    @Autowired
    PlatformItemFetchService platformItemFetchService;
    @Autowired
    ArkSailorItemBizService arkSailorItemBizService;
    @Autowired
    PlatformItemInventorySettingBizService platformItemInventorySettingBizService;
    @Autowired
    ShopStockSpecBizService shopStockSpecBizService;
    @Autowired
    ICombinationItemService combinationItemService;
    @Autowired
    IComposeSkuService composeSkuService;
    @Autowired
    ObjectProvider<List<PlatformItemSkuSyncService>> platformItemSkuSyncServiceProvider;
    @Autowired
    IShopAuthorizationService shopAuthorizationService;
    @Autowired
    IInventoryAllocService inventoryAllocService;
    @Autowired
    InventoryAllocBizService inventoryAllocBizService;
    @Autowired
    IInventoryAllocShopService inventoryAllocShopService;
    @Autowired
    PlatformItemStockSyncBizService platformItemStockSyncBizService;
    @Autowired
    IWdtStockSpecRtService wdtStockSpecRtService;
    @Autowired
    IWdtStockSpecService wdtStockSpecService;
    @Autowired
    IInventoryMonitorService inventoryMonitorService;
    @Autowired
    StockSpecBizService stockSpecBizService;
    @Autowired
    OperateLogDomainService operateLogDomainService;
    @Autowired ShopBizService shopBizService;
    @Autowired
    IShopService shopService;
    @Autowired
    private QyMsgSendService qyMsgSendService;
    @Autowired
    private MessageRobotGateway messageRobotGateway;
    
    @Override
    public PageResponse<PlatformItemListItem> pageQuery(PlatformItemPageQuery pageQuery) {
        IPage<PlatformItem> page = platformItemService.pageQuery(pageQuery);
        final List<PlatformItem> platformItems = page.getRecords();
        final List<PlatformItemListItem> returnRecords = toPlatformItemListItems(platformItems);
        return PageResponse.of(returnRecords, (int) page.getTotal(), (int) page.getPages(), (int) page.getCurrent());
    }
    
    @Override
    public PageResponse<PlatformItemListItem> otherPlatformItemPageQuery(OtherPlatformItemPageQuery pageQuery) {
        final PlatformItem platformItem = getPlatformItem(pageQuery.getPlatformItemId());
        final IPage<PlatformItem> page = pageQuery.getPage();
        otherPlatformItemsPageQuery(platformItem, page);
        final List<PlatformItemListItem> otherPlatformItemListItems = toPlatformItemListItems(page.getRecords());
        return PageResponse.of(otherPlatformItemListItems, (int) page.getTotal(), (int) page.getPages(),
                (int) page.getCurrent());
    }
    
    @NonNull
    private List<PlatformItemListItem> toPlatformItemListItems(List<PlatformItem> platformItems) {
        if (CollUtil.isEmpty(platformItems)) {
            return Collections.emptyList();
        }
        final List<Long> itemIds = platformItems.stream().map(PlatformItem::getItemId).collect(Collectors.toList());
        Map<Long, ItemBaseDO> itemBaseInfoMap = Collections.emptyMap();
        if (!itemIds.isEmpty()) {
            itemBaseInfoMap = itemGateway.getBaseInfoWithImage(itemIds).stream()
                    .collect(Collectors.toMap(ItemBaseDO::getId, Function.identity()));
        }
        final List<PlatformItemListItem> returnRecords = Lists.newArrayListWithCapacity(platformItems.size());
        final List<Long> platformItemIds = platformItems.stream().map(PlatformItem::getId).collect(Collectors.toList());
        final List<PlatformItemSku> platformItemSkuList = platformItemSkuService.listByPlatformItemId(platformItemIds);
        for (PlatformItem record : platformItems) {
            final PlatformItemListItem platformItemListItem = new PlatformItemListItem();
            platformItemListItem.setId(record.getId());
            platformItemListItem.setItemCode(record.getItemCode());
            platformItemListItem.setItemId(record.getItemId());
            platformItemListItem.setGoodsName(record.getGoodsName());
            platformItemListItem.setShopName(record.getShopName());
            platformItemListItem.setShopId(record.getShopId());
            platformItemListItem.setPlatform(record.getPlatform());
            platformItemListItem.setPlatformIcon(record.getPlatform() != null ? record.getPlatform().getIcon() : "");
            platformItemListItem.setStatus(record.getStatus());
            platformItemListItem.setOuterItemId(record.getOuterItemId());
            platformItemListItem.setOuterItemCode(record.getOuterItemCode());
            platformItemListItem.setPrice(record.getPrice());
            platformItemListItem.setStock(record.getStock());
            platformItemListItem.setSkuNum(record.getSkuNum());
            final Optional<ItemBaseDO> itemBaseDo = Optional.ofNullable(itemBaseInfoMap.get(record.getItemId()));
            itemBaseDo.ifPresent(it -> {
                platformItemListItem.setItemImage(it.getMainImgUrl());
                platformItemListItem.setCategoryId(it.getCategoryId());
                platformItemListItem.setCategoryName(CategoryUtils.getLastCategoryName(it.getCategoryPath()));
            });
            final List<PlatformItemSku> platformItemSkus =
                    platformItemSkuList.stream().filter(v -> v.getPlatformItemId().equals(record.getId()))
                            .collect(Collectors.toList());
            platformItemListItem.setStock(0);
            platformItemListItem.setAvailableStock(0);
            platformItemListItem.setSyncStock(0);
            if (!platformItemSkus.isEmpty()) {
                final List<PlatformItemDetailSku> platformItemDetailSkuList = detailSkuList(record);
                platformItemListItem.setStock(Math.max(0,
                        platformItemDetailSkuList.stream().filter(v -> Objects.nonNull(v.getStock()))
                                .mapToInt(PlatformItemDetailSku::getStock).sum()));
                platformItemListItem.setSyncStock(Math.max(0,
                        platformItemDetailSkuList.stream().filter(v -> Objects.nonNull(v.getSyncStock()))
                                .mapToInt(PlatformItemDetailSku::getSyncStock).sum()));
                platformItemListItem.setAvailableStock(Math.max(0,
                        platformItemDetailSkuList.stream().filter(v -> Objects.nonNull(v.getTotalStock()))
                                .mapToInt(PlatformItemDetailSku::getTotalStock).sum()));
                platformItemListItem.setSkuNum(platformItemSkus.size());
            }
            platformItemListItem.setSyncEnabled(record.getSyncEnabled());
            platformItemListItem.setLockEnabled(record.getLockEnabled());
            PlatformItemSyncConfig.ItemTypeWeightConfig itemTypeWeightConfig =
                    platformItemSyncConfig.getItemTypeWeightConfig(record);
            platformItemListItem.setLinkType(itemTypeWeightConfig.getType());
            returnRecords.add(platformItemListItem);
        }
        return returnRecords;
    }
    
    @Override
    public SingleResponse<PlatformItemDetail> detail(Long id) {
        final PlatformItem platformItem = getPlatformItem(id);
        ItemBaseDO itemBaseData = getItemBaseData(platformItem);
        final List<String> itemImageUrls = itemGateway.getImageUrlList(platformItem.getItemId(), ItemImageType.COMMON);
        final PlatformItemDetail platformItemDetail = getPlatformItemDetail(platformItem, itemBaseData, itemImageUrls);
        List<PlatformItemDetailSku> platformItemDetailSkus = detailSkuList(id);
        platformItemDetail.setSkus(platformItemDetailSkus);
        return SingleResponse.of(platformItemDetail);
    }
    
    private PlatformItemDetail getPlatformItemDetail(
            PlatformItem platformItem, ItemBaseDO itemBaseData, List<String> itemImageUrls) {
        InventoryAllocShop inventoryAllocShop =
                inventoryAllocShopService.getByShopNo(platformItem.getShopNo());
        platformItem.setSyncEnabledByShopSetting(inventoryAllocShop);
        final PlatformItemDetail platformItemDetail =
                PlatformItemTransMapper.INSTANCE.toPlatformItemDetail(
                        platformItem, itemBaseData, itemImageUrls);
        platformItemDetail.setShopSyncEnabled(
                inventoryAllocShop != null && inventoryAllocShop.getStatus() != InventoryAllocShopStatus.FORBIDDEN);
        PlatformItemSyncConfig.ItemTypeWeightConfig itemTypeWeightConfig =
                platformItemSyncConfig.getItemTypeWeightConfig(platformItem);
        platformItemDetail.setLinkType(itemTypeWeightConfig.getType());
        return platformItemDetail;
    }
    
    @Override
    public List<PlatformItemDetailSku> detailSkuList(Long platformItemId) {
        final PlatformItem platformItem = getPlatformItem(platformItemId);
        return detailSkuList(platformItem);
    }
    
    private List<PlatformItemDetailSku> detailSkuList(PlatformItem platformItem) {
        final List<PlatformItemSku> platformItemSkus =
                platformItemSkuService.listByPlatformItemId(platformItem.getId());
        return assembleDetailSkuList(platformItemSkus);
    }
    
    @NotNull
    private List<PlatformItemDetailSku> assembleDetailSkuList(List<PlatformItemSku> platformItemSkus) {
        if (platformItemSkus.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> pisSkuCodes =
                platformItemSkus.stream().map(PlatformItemSku::getOuterSkuCode).distinct().collect(Collectors.toList());
        List<CombinationItem> combinationItems = combinationItemService.listByCombinationCode(pisSkuCodes);
        List<String> allSkuCodes = Stream.of(pisSkuCodes,
                        combinationItems.stream().map(CombinationItem::getCode).collect(Collectors.toList()))
                .flatMap(Collection::stream)
                .filter(StringUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        List<InventoryAlloc> allInventoryAllocList = inventoryAllocService.listBySkuCode(allSkuCodes);
        List<WarehouseInventoryDetailItemVO> warehouseInventoryDetailItemVOS = warehouseInventoryDetail(pisSkuCodes);
        Map<String, List<WarehouseInventoryDetailItemVO>> warehouseInventoryDetailSkuGroupedMap =
                warehouseInventoryDetailItemVOS.stream()
                        .collect(Collectors.groupingBy(WarehouseInventoryDetailItemVO::getSkuCode));
        List<Long> platformItemIds = platformItemSkus.stream().map(PlatformItemSku::getPlatformItemId).distinct()
                .collect(Collectors.toList());
        List<Long> allPlatformItemIds = Stream.concat(platformItemIds.stream(),
                        allInventoryAllocList.stream().map(InventoryAlloc::getPlatformItemId)).distinct()
                .collect(Collectors.toList());
        List<PlatformItem> platformItems = platformItemService.listByIds(allPlatformItemIds);
        Map<Long, PlatformItem> platformItemMap =
                platformItems.stream().collect(Collectors.toMap(PlatformItem::getId, Function.identity()));
        List<String> shopNos = platformItems.stream().map(PlatformItem::getShopNo).collect(Collectors.toList());
        List<InventoryAllocShop> inventoryAllocShops = inventoryAllocShopService.listByShopNo(shopNos);
        Map<String, InventoryAllocShop> inventoryAllocShopMap = inventoryAllocShops.stream()
                .collect(Collectors.toMap(InventoryAllocShop::getShopNo, Function.identity()));
        return platformItemSkus.stream().map(platformItemSku -> {
            List<String> calculations = new ArrayList<>();
            String skuCode = platformItemSku.getOuterSkuCode();
            final PlatformItemDetailSku detailSku = new PlatformItemDetailSku();
            detailSku.setId(platformItemSku.getId());
            detailSku.setPlatform(platformItemSku.getPlatform());
            detailSku.setPlatformItemId(platformItemSku.getPlatformItemId());
            detailSku.setSkuCode(platformItemSku.getSkuCode());
            detailSku.setSkuId(platformItemSku.getSkuId());
            detailSku.setStatus(platformItemSku.getStatus());
            detailSku.setOuterItemId(platformItemSku.getOuterItemId());
            detailSku.setOuterItemCode(platformItemSku.getOuterItemCode());
            detailSku.setOuterSkuId(platformItemSku.getOuterSkuId());
            detailSku.setOuterSkuCode(skuCode);
            detailSku.setPrice(platformItemSku.getPrice());
            detailSku.setStock(platformItemSku.getStock());
            detailSku.setSpecName(platformItemSku.getSpecName());
            detailSku.setLastSyncStock(platformItemSku.getLastSyncStock());
            detailSku.setLastSyncTime(platformItemSku.getLastSyncTime());
            InventoryAlloc inventoryAlloc =
                    allInventoryAllocList.stream().filter(v -> v.getPlatformItemSkuId().equals(platformItemSku.getId()))
                            .filter(InventoryAlloc::isMainRecord).findFirst().orElse(null);
            if (inventoryAlloc != null) {
                detailSku.setSyncStock(inventoryAlloc.getEffectiveNum());
                detailSku.setWarnThreshold(inventoryAlloc.getWarnThreshold());
                detailSku.setInventoryAllocId(inventoryAlloc.getId());
                calculations.add("## 同步库存计算");
                calculations.addAll(new ArrayList<>(StringUtil.split(inventoryAlloc.getCalculation(), ";")));
            }
            InventoryAllocShop inventoryAllocShop = inventoryAllocShopMap.get(platformItemSku.getShopNo());
            if (inventoryAllocShop != null) {
                if (detailSku.getWarnThreshold() == null) {
                    detailSku.setWarnThreshold(inventoryAllocShop.getWarnThreshold());
                }
            }
            
            calculations.add("## 占用库存计算");
            List<InventoryAlloc> otherLinkInventoryAllocs = allInventoryAllocList.stream()
                    .filter(v -> !v.getPlatformItemSkuId().equals(platformItemSku.getId()) &&
                            v.getSkuCode().equals(skuCode) && v.getEffectiveNum() != null).collect(Collectors.toList());
            int otherLinkOccupyStock =
                    otherLinkInventoryAllocs.stream().mapToInt(InventoryAlloc::getEffectiveNum).sum();
            detailSku.setOccupyStock(otherLinkOccupyStock);
            if (!otherLinkInventoryAllocs.isEmpty()) {
                calculations.add("其他链接占用库存 = " +
                        otherLinkInventoryAllocs.stream().map(InventoryAlloc::getEffectiveNum).map(Objects::toString)
                                .collect(Collectors.joining(" + ")) + " = " + otherLinkOccupyStock);
            }
            Predicate<CombinationItem> isThisCombinationItem =
                    v -> v.getCode().equals(skuCode);
            CombinationItem combinationItem =
                    combinationItems.stream().filter(isThisCombinationItem).findFirst().orElse(null);
            detailSku.setIsCombinationItem(combinationItem != null);
            detailSku.setCombinationId(combinationItem != null ? combinationItem.getId() : 0L);
            
            calculations.add("## 总库存计算");
            List<WarehouseInventoryDetailItemVO> warehouseInventoryDetailItems =
                    warehouseInventoryDetailSkuGroupedMap.getOrDefault(skuCode, Collections.emptyList());
            for (WarehouseInventoryDetailItemVO warehouseInventoryDetailItem : warehouseInventoryDetailItems) {
                calculations.add(warehouseInventoryDetailItem.getAllocableStockCalculation());
                if (warehouseInventoryDetailItem.getComposeSkus() != null) {
                    for (WarehouseInventoryDetailItemComposeSkuVO composeSkuVO : warehouseInventoryDetailItem.getComposeSkus()) {
                        calculations.add(composeSkuVO.getAllocableStockCalculation());
                    }
                }
            }
            BigDecimal warehouseAllocableStockTotal =
                    warehouseInventoryDetailItems.stream().map(WarehouseInventoryDetailItemVO::getAllocableStock)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
            StringJoiner warehouseAllocableStockTotalCalculation = new StringJoiner(" = ");
            warehouseAllocableStockTotalCalculation.add("总库存");
            if (warehouseInventoryDetailItems.size() > 1) {
                warehouseAllocableStockTotalCalculation.add(
                        warehouseInventoryDetailItems.stream().map(WarehouseInventoryDetailItemVO::getAllocableStock)
                                .map(BigDecimal::toPlainString).collect(Collectors.joining(" + ")));
            }
            warehouseAllocableStockTotalCalculation.add(warehouseAllocableStockTotal.toPlainString());
            calculations.add(warehouseAllocableStockTotalCalculation.toString());
            detailSku.setTotalStock(warehouseAllocableStockTotal.intValue());
            detailSku.setCalculations(calculations);
            return detailSku;
        }).collect(Collectors.toList());
    }
    
    @Override
    public SingleResponse<PlatformItemDetail> detail(Platform platform, String outerItemId) {
        final PlatformItem platformItem = platformItemService.lambdaQuery().eq(PlatformItem::getPlatform, platform)
                .eq(PlatformItem::getOuterItemId, outerItemId).last(limit(1)).one();
        
        if (platformItem == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未找到指定平台商品");
        }
        return detail(platformItem.getId());
    }
    
    @NonNull
    private PlatformItem getPlatformItem(Long id) {
        final PlatformItem platformItem = platformItemService.getById(id);
        if (Objects.isNull(platformItem)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未找到指定平台商品");
        }
        return platformItem;
    }
    
    @Nullable
    private ItemBaseDO getItemBaseData(PlatformItem platformItem) {
        if (!NumberUtil.isPositive(platformItem.getItemId())) {
            return null;
        }
        return itemGateway.getBaseInfoWithImage(platformItem.getItemId());
    }
    
    private void otherPlatformItemsPageQuery(PlatformItem platformItem, IPage<PlatformItem> page) {
        // 未查询到平台商品或者平台商品未匹配到后端商品，直接返回空
        if (Objects.isNull(platformItem) || !NumberUtil.isPositive(platformItem.getItemId())) {
            return;
        }
        platformItemService.lambdaQuery().eq(PlatformItem::getItemId, platformItem.getItemId())
                .ne(PlatformItem::getPlatform, platformItem.getPlatform()).ne(PlatformItem::getId, platformItem.getId())
                .page(page);
    }
    
    @Override
    public SingleResponse<PlatformItemSyncResult> sync(PlatformItemSyncCmd cmd) {
        final List<String> goodsIds = filterGoodsIds(cmd.getIds());
        if (goodsIds.isEmpty()) {
            return SingleResponse.of(PlatformItemSyncResult.of(goodsIds, Collections.emptyList()));
        }
        if (goodsIds.size() > 10) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "同步商品数量不能超过10个");
        }
        List<PlatformItem> platformItems = platformItemService.listByOuterItemId(goodsIds);
        Map<String, PlatformItem> platformItemsMap =
                platformItems.stream().collect(Collectors.toMap(PlatformItem::getOuterItemId, Function.identity()));
        if (cmd.getPlatform() == Platform.TAOBAO || cmd.getPlatform() == Platform.YOUZAN) {
            for (String goodsId : goodsIds) {
                platformItemSyncService.syncWdtPlatformGoods(goodsId);
            }
        }
        for (PlatformItemSkuSyncService platformItemSkuSyncService : Objects.requireNonNull(
                platformItemSkuSyncServiceProvider.getIfAvailable())) {
            if (platformItemSkuSyncService.defaultType() == cmd.getPlatform()) {
                for (String goodsId : goodsIds) {
                    PlatformItem platformItemExists = platformItemsMap.get(goodsId);
                    if (platformItemExists != null) {
                        if (!platformItemSkuSyncService.syncItem(platformItemExists.getShopNo(), goodsId)) {
                            throw ExceptionPlusFactory.bizException(ErrorCode.PLATFORM_ITEM_SYNC_ERROR,
                                    "平台商品同步失败，未查询到商品");
                        }
                    } else {
                        if (cmd.getPlatform() == Platform.LAOBASHOP) {
                            platformItemSkuSyncService.syncItem(null, goodsId);
                        } else {
                            boolean syncSuccess = false;
                            List<ShopAuthorization> shopAuthorizations =
                                    shopAuthorizationService.listNotExpiredAuthorizations(
                                            cmd.getPlatform());
                            for (ShopAuthorization shopAuthorization : shopAuthorizations) {
                                try {
                                    if (platformItemSkuSyncService.syncItem(shopAuthorization.getSn(), goodsId)) {
                                        syncSuccess = true;
                                        break;
                                    }
                                } catch (Exception e) {
                                    log.error("[平台商品同步][手动同步]首次同步，尝试遍历店铺:{}，商品:{}，同步异常:{}",
                                            shopAuthorization.getSn(), goodsId,
                                            e.getMessage(), e);
                                }
                            }
                            if (!syncSuccess) {
                                throw ExceptionPlusFactory.bizException(ErrorCode.PLATFORM_ITEM_SYNC_ERROR,
                                        "平台商品同步失败，请检查店铺是否授权");
                            }
                        }
                    }
                }
            }
        }
        return SingleResponse.of(PlatformItemSyncResult.of(Collections.emptyList(), Collections.emptyList()));
    }
    
    private List<String> filterGoodsIds(List<String> goodsIdsOrLinks) {
        try {
            List<String> goodsIds = Lists.newArrayListWithCapacity(goodsIdsOrLinks.size());
            l1:
            for (String item : goodsIdsOrLinks) {
                for (String sep : Arrays.asList("、", ",", "，")) {
                    if (item.contains(sep)) {
                        final List<String> strings = StrUtil.splitTrim(item, sep);
                        if (!strings.isEmpty()) {
                            final List<String> subFilterResult = filterGoodsIds(strings);
                            goodsIds.addAll(subFilterResult);
                        }
                        continue l1;
                    }
                }
                String id = null;
                if (StringUtil.startWith(item, "http")) {
                    for (Pattern linkPattern : LINK_PATTERNS) {
                        final Matcher matcher = linkPattern.matcher(item);
                        if (matcher.matches()) {
                            id = matcher.group(1);
                            break;
                        }
                    }
                    if (Objects.isNull(id)) {
                        throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "无法识别的链接 " + item);
                    }
                } else {
                    id = item;
                }
                goodsIds.add(id);
            }
            return goodsIds;
        } catch (Exception e) {
            log.error("[手动同步平台商品]商品ID、链接解析异常，不支持的格式:{} for:{}", e.getMessage(), goodsIdsOrLinks,
                    e);
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "商品ID、链接解析异常，不支持的格式");
        }
    }
    
    @Override
    public PageResponse<PlatformItemDropDownItem> dropDownList(PlatformItemDropDownPageQuery pageQuery) {
        final IPage<PlatformItem> page = platformItemService.lambdaQuery()
                .like(StringUtil.isNotBlank(pageQuery.getName()), PlatformItem::getGoodsName, pageQuery.getName())
                .select(PlatformItem::getId, PlatformItem::getGoodsName).page(pageQuery.getPage());
        final List<PlatformItem> platformItems = page.getRecords();
        final List<PlatformItemDropDownItem> dropDownItems =
                platformItems.stream().map(it -> new PlatformItemDropDownItem(it.getId(), it.getGoodsName()))
                        .collect(Collectors.toList());
        return PageResponse.of(dropDownItems, (int) page.getTotal(), (int) page.getPages(), (int) page.getCurrent());
    }
    
    @Override
    public PageResponse<SelectPlatformItemVO> hibridSelectPlatformItem(SelectPlatformItemQuery query) {
        final List<Platform> platforms = query.getPlatforms().stream().map(v -> IEnum.getEnumByValue(Platform.class, v))
                .collect(Collectors.toList());
        Assert.notEmpty(platforms, "选择平台不能为空");
        if (platforms.contains(Platform.LAOBASHOP)) {
            final ItemPageQuery itemPageQuery = new ItemPageQuery();
            itemPageQuery.setName(query.getGoodsName());
            itemPageQuery.setItemNo(query.getGoodsNo());
            final LinkedHashSet<Long> itemIds = new LinkedHashSet<>();
            Optional.ofNullable(query.getOuterItemId()).filter(StringUtil::isNotBlank).map(Long::parseLong)
                    .ifPresent(itemIds::add);
            Optional.ofNullable(query.getOuterItemIds()).ifPresent(
                    v -> v.stream().filter(StringUtil::isNotBlank).map(Long::parseLong).forEach(itemIds::add));
            if (!itemIds.isEmpty()) {
                itemPageQuery.setItemIds(new ArrayList<>(itemIds));
            }
            itemPageQuery.setTabShelfStatusIn(query.getStatusIn());
            itemPageQuery.setCurrent(query.getPageIndex());
            itemPageQuery.setSize(query.getPageSize());
            
            final PageResponse<ItemListWithSkuVO> itemListWithSkuVOPageResponse =
                    arkSailorItemBizService.itemWithSkuPageQuery(itemPageQuery);
            ErrorChecker.checkAndThrowIfError(itemListWithSkuVOPageResponse);
            
            final List<ItemListWithSkuVO> data = itemListWithSkuVOPageResponse.getData();
            final List<String> itemCodes =
                    data.stream().map(v -> v.getItem().getItemNo()).distinct().collect(Collectors.toList());
            
            final HashSetValuedHashMap<String, SelectPlatformItemSkuVO> skuCollector = new HashSetValuedHashMap<>();
            for (ItemListWithSkuVO itemWithSkuVO : data) {
                final ItemListVO item = itemWithSkuVO.getItem();
                final String itemNo = item.getItemNo();
                
                for (ItemSkuVO sku : itemWithSkuVO.getSkus()) {
                    final SelectPlatformItemSkuVO selectPlatformItemSkuVO = new SelectPlatformItemSkuVO();
                    selectPlatformItemSkuVO.setOuterSkuCode(sku.getSkuNo());
                    selectPlatformItemSkuVO.setSpecifications(sku.getName());
                    
                    skuCollector.put(itemNo, selectPlatformItemSkuVO);
                }
            }
            
            final List<PlatformItemSku> platformItemSkuList = itemCodes.isEmpty() ? Collections.emptyList() :
                    platformItemSkuService.lambdaQuery().in(PlatformItemSku::getOuterItemCode, itemCodes).list();
            
            for (PlatformItemSku platformItemSku : platformItemSkuList) {
                final SelectPlatformItemSkuVO selectPlatformItemSkuVO = new SelectPlatformItemSkuVO();
                selectPlatformItemSkuVO.setOuterSkuCode(platformItemSku.getOuterSkuCode());
                selectPlatformItemSkuVO.setSpecifications(platformItemSku.getSpecName());
                
                skuCollector.put(platformItemSku.getOuterItemCode(), selectPlatformItemSkuVO);
            }
            
            return ResponseFactory.ofPage(itemListWithSkuVOPageResponse, datum -> {
                final SelectPlatformItemVO selectPlatformItemVO = new SelectPlatformItemVO();
                final ItemListVO item = datum.getItem();
                selectPlatformItemVO.setOuterItemId(String.valueOf(item.getId()));
                selectPlatformItemVO.setGoodsName(item.getName());
                selectPlatformItemVO.setGoodsNo(item.getItemNo());
                final String mainImage = item.getMainImage();
                if (StringUtil.isNotEmpty(mainImage)) {
                    final JSONObject mainImageJSON = JSON.parseObject(mainImage);
                    if (mainImageJSON != null) {
                        selectPlatformItemVO.setGoodsImage(mainImageJSON.getString("url"));
                    }
                }
                selectPlatformItemVO.setPlatform(Platform.LAOBASHOP.getValue());
                String status = ArkItemStatusEnum.getStatusByItem(item).getValue();
                selectPlatformItemVO.setStatus(status);
                
                final HashSet<SelectPlatformItemSkuVO> skus = new HashSet<>();
                Optional.ofNullable(item.getItemNo()).map(Object::toString).map(skuCollector::get)
                        .ifPresent(skus::addAll);
                selectPlatformItemVO.setSkuNum(skus.size());
                selectPlatformItemVO.setSkus(new ArrayList<>(skus));
                return selectPlatformItemVO;
            });
        } else {
            final PlatformItemPageQuery pageQuery = new PlatformItemPageQuery();
            final LinkedHashSet<String> outerItemIdsForQuery = new LinkedHashSet<>();
            Optional.ofNullable(query.getOuterItemId()).filter(StringUtil::isNotBlank)
                    .ifPresent(outerItemIdsForQuery::add);
            Optional.ofNullable(query.getOuterItemIds())
                    .ifPresent(v -> v.stream().filter(StringUtil::isNotBlank).forEach(outerItemIdsForQuery::add));
            pageQuery.setOuterItemIds(outerItemIdsForQuery);
            pageQuery.setPlatforms(platforms);
            pageQuery.setItemCode(query.getGoodsNo());
            if (CollectionUtil.isNotEmpty(query.getStatusIn())) {
                final List<PlatformItemStatus> platformItemStatusEnums = query.getStatusIn().stream()
                        .map(status -> IEnum.getEnumByName(PlatformItemStatus.class, status))
                        .collect(Collectors.toList());
                pageQuery.setStatusIn(platformItemStatusEnums);
            }
            pageQuery.setItemName(query.getGoodsName());
            pageQuery.setPageIndex(query.getPageIndex());
            pageQuery.setPageSize(query.getPageSize());
            
            final PageResponse<PlatformItemListItem> platformItemListItemPageResponse = pageQuery(pageQuery);
            ErrorChecker.checkAndThrowIfError(platformItemListItemPageResponse);
            
            final HashSetValuedHashMap<String, SelectPlatformItemSkuVO> skuCollector = new HashSetValuedHashMap<>();
            final List<PlatformItemListItem> data = platformItemListItemPageResponse.getData();
            final List<Long> itemIds = data.stream().map(PlatformItemListItem::getItemId).filter(NumberUtil::isPositive)
                    .collect(Collectors.toList());
            final List<String> outerItemIds =
                    data.stream().map(PlatformItemListItem::getOuterItemId).filter(StringUtil::isNotBlank)
                            .collect(Collectors.toList());
            
            final List<PlatformItemSku> platformItemSkuList =
                    itemIds.isEmpty() && outerItemIds.isEmpty() ? Collections.emptyList() :
                            platformItemSkuService.lambdaQuery()
                                    .in(!itemIds.isEmpty(), PlatformItemSku::getItemId, itemIds).or()
                                    .in(!outerItemIds.isEmpty(), PlatformItemSku::getOuterItemId, outerItemIds).list();
            
            for (PlatformItemSku platformItemSku : platformItemSkuList) {
                final SelectPlatformItemSkuVO selectPlatformItemSkuVO = new SelectPlatformItemSkuVO();
                selectPlatformItemSkuVO.setOuterSkuCode(platformItemSku.getOuterSkuCode());
                selectPlatformItemSkuVO.setSpecifications(platformItemSku.getSpecName());
                
                Optional.ofNullable(platformItemSku.getItemId()).filter(NumberUtil::isPositive)
                        .ifPresent(id -> skuCollector.put(String.valueOf(id), selectPlatformItemSkuVO));
                Optional.ofNullable(platformItemSku.getOuterItemId()).filter(StringUtil::isNotBlank)
                        .ifPresent(id -> skuCollector.put(id, selectPlatformItemSkuVO));
            }
            
            final ArrayList<Long> mallItemIds = new ArrayList<>();
            final HashMap<Long, Long> mallItemIdToItemIdMap = new HashMap<>();
            for (PlatformItemSku platformItemSku : platformItemSkuList) {
                if (platformItemSku.getPlatform() == Platform.LAOBASHOP) {
                    try {
                        final long mallItemId = Long.parseLong(platformItemSku.getOuterItemId());
                        if (NumberUtil.isPositive(mallItemId)) {
                            mallItemIds.add(mallItemId);
                            mallItemIdToItemIdMap.put(mallItemId, platformItemSku.getItemId());
                        }
                    } catch (NumberFormatException ignored) {
                    }
                }
            }
            
            final MultiResponse<ItemSkuVO> itemSkuVOMultiResponse =
                    arkSailorItemBizService.itemSkuQueryByItemIds(mallItemIds);
            ErrorChecker.checkAndThrowIfError(itemSkuVOMultiResponse);
            
            for (ItemSkuVO mallItemSkuVO : itemSkuVOMultiResponse.getData()) {
                final Long mallItemId = mallItemSkuVO.getItemId();
                final Long itemId = mallItemIdToItemIdMap.get(mallItemId);
                if (Objects.nonNull(itemId)) {
                    final SelectPlatformItemSkuVO selectPlatformItemSkuVO = new SelectPlatformItemSkuVO();
                    selectPlatformItemSkuVO.setOuterSkuCode(mallItemSkuVO.getSkuNo());
                    selectPlatformItemSkuVO.setSpecifications(mallItemSkuVO.getName());
                    
                    skuCollector.put(itemId.toString(), selectPlatformItemSkuVO);
                }
            }
            
            return ResponseFactory.ofPage(platformItemListItemPageResponse, datum -> {
                final SelectPlatformItemVO selectPlatformItemVO = new SelectPlatformItemVO();
                selectPlatformItemVO.setOuterItemId(datum.getOuterItemId());
                selectPlatformItemVO.setPlatform(datum.getPlatform().getValue());
                selectPlatformItemVO.setPlatformItemId(datum.getId());
                selectPlatformItemVO.setGoodsName(datum.getGoodsName());
                selectPlatformItemVO.setGoodsNo(datum.getItemCode());
                selectPlatformItemVO.setGoodsImage(datum.getItemImage());
                selectPlatformItemVO.setStatus(datum.getStatus().toString());
                
                final HashSet<SelectPlatformItemSkuVO> skus = new HashSet<>();
                Optional.ofNullable(datum.getItemId()).map(Object::toString).map(skuCollector::get)
                        .ifPresent(skus::addAll);
                Optional.ofNullable(datum.getOuterItemId()).map(Object::toString).map(skuCollector::get)
                        .ifPresent(skus::addAll);
                selectPlatformItemVO.setSkuNum(skus.size());
                selectPlatformItemVO.setSkus(new ArrayList<>(skus));
                return selectPlatformItemVO;
            });
        }
    }
    
    @Override
    public void inventorySyncSwitch(InventorySyncSwitchCmd cmd) {
        for (Long platformItemId : cmd.getPlatformItemIds()) {
            PlatformItem platformItem = platformItemService.getById(platformItemId);
            if (platformItem == null) {
                throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "平台商品不存在");
            }
            
            // 记录操作前状态
            boolean beforeSyncEnabled = platformItem.getSyncEnabled() != null ? platformItem.getSyncEnabled() : false;
            String syncAction = cmd.getEnable() ? "开启" : "关闭";
            
            if (beforeSyncEnabled != cmd.getEnable()) {
                platformItem.setSyncEnabled(cmd.getEnable());
                platformItemService.updateById(platformItem);
                
                // 记录操作日志
                String logMessage = String.format("【库存同步】%s：%s",
                        syncAction, platformItem.getGoodsName());
                
                operateLogDomainService.addOperatorLog(
                        UserContext.getUserId(),
                        OperateLogTarget.PLATFORM_ITEM,
                        platformItemId,
                        logMessage
                );
            }
        }
    }
    
    @Override
    public void inventoryLockSwitch(InventoryLockSwitchCmd cmd) {
        for (Long platformItemId : cmd.getPlatformItemIds()) {
            PlatformItem platformItem = platformItemService.getById(platformItemId);
            if (platformItem == null) {
                throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "平台商品不存在");
            }
            
            // 记录操作前状态
            boolean beforeLockEnabled = platformItem.getLockEnabled() != null ? platformItem.getLockEnabled() : false;
            String lockAction = cmd.getLock() ? "开启" : "关闭";
            
            platformItem.setLockEnabled(cmd.getLock());
            platformItemService.updateById(platformItem);
            
            inventoryAllocService.lambdaUpdate().eq(InventoryAlloc::getPlatformItemId, platformItemId)
                    .set(InventoryAlloc::getLockEnabled, cmd.getLock())
                    .update();
            
            // 记录操作日志
            if (beforeLockEnabled != cmd.getLock()) {
                String logMessage = String.format("【库存锁定】%s：%s",
                        lockAction, platformItem.getGoodsName());
                
                operateLogDomainService.addOperatorLog(
                        UserContext.getUserId(),
                        OperateLogTarget.PLATFORM_ITEM,
                        platformItemId,
                        logMessage
                );
            }
        }
    }
    
    @Override
    public InventoryOccupyDetailVO inventoryOccupyDetail(InventoryOccupyDetailQuery query) {
        String paramSkuCode = query.getSkuCode();
        Optional<ItemSku> itemSkuOptional = itemSkuService.getByMixCode(paramSkuCode);
        InventoryOccupyDetailVO inventoryOccupyDetailVO = new InventoryOccupyDetailVO();
        inventoryOccupyDetailVO.setSkuCode(paramSkuCode);
        inventoryOccupyDetailVO.setSpecification(itemSkuOptional.map(ItemSku::getSpecifications).orElse(""));
        inventoryOccupyDetailVO.setItems(Collections.emptyList());
        List<InventoryAlloc> inventoryAllocs = getAllInventoryAllocs(paramSkuCode);
        if (inventoryAllocs.isEmpty()) {
            return inventoryOccupyDetailVO;
        }
        List<Long> platformItemSkuIds = inventoryAllocs.stream().map(InventoryAlloc::getPlatformItemSkuId).distinct()
                .collect(Collectors.toList());
        List<PlatformItemSku> platformItemSkus = platformItemSkuService.listByIds(platformItemSkuIds);
        List<PlatformItemDetailSku> platformItemDetailSkus = assembleDetailSkuList(platformItemSkus);
        List<Long> platformItemIds = platformItemDetailSkus.stream().map(PlatformItemDetailSku::getPlatformItemId)
                .distinct().collect(Collectors.toList());
        List<PlatformItem> platformItems = platformItemService.listByIds(platformItemIds);
        Map<Long, PlatformItem> platformItemMap =
                platformItems.stream().collect(Collectors.toMap(PlatformItem::getId, Function.identity()));
        List<InventoryOccupyItemVO> inventoryOccupyItemVOS = new ArrayList<>();
        List<Long> excludePlatformItemIds =
                Optional.ofNullable(query.getExcludePlatformItemIds()).orElse(Collections.emptyList());
        for (PlatformItemDetailSku detailSku : platformItemDetailSkus) {
            if (!excludePlatformItemIds.isEmpty() &&
                    query.getExcludePlatformItemIds().contains(detailSku.getPlatformItemId())) {
                continue;
            }
            PlatformItem platformItem = platformItemMap.get(detailSku.getPlatformItemId());
            if (platformItem == null) {
                continue;
            }
            InventoryOccupyItemVO inventoryOccupyItemVO = new InventoryOccupyItemVO();
            inventoryOccupyItemVO.setPlatformItemId(detailSku.getPlatformItemId());
            inventoryOccupyItemVO.setPlatformItemSkuId(detailSku.getId());
            inventoryOccupyItemVO.setOuterItemId(detailSku.getOuterItemId());
            inventoryOccupyItemVO.setOuterSkuId(detailSku.getOuterSkuId());
            inventoryOccupyItemVO.setPlatformItemName(platformItem.getGoodsName());
            inventoryOccupyItemVO.setPlatform(detailSku.getPlatform());
            inventoryOccupyItemVO.setSkuCode(detailSku.getSkuCode());
            inventoryOccupyItemVO.setOuterSkuCode(detailSku.getOuterSkuCode());
            inventoryOccupyItemVO.setIsCombinationItem(detailSku.getIsCombinationItem());
            inventoryOccupyItemVO.setSyncStock(detailSku.getSyncStock());
            inventoryOccupyItemVO.setTotalStock(detailSku.getTotalStock());
            inventoryOccupyItemVO.setLockEnabled(platformItem.getLockEnabled());
            inventoryOccupyItemVO.setCalculations(detailSku.getCalculations());
            inventoryOccupyItemVOS.add(inventoryOccupyItemVO);
        }
        inventoryOccupyDetailVO.setItems(inventoryOccupyItemVOS);
        return inventoryOccupyDetailVO;
    }
    
    @NotNull
    private List<InventoryAlloc> getAllInventoryAllocs(String skuCode) {
        CombinationItem combinationItem = combinationItemService.getByItemCode(skuCode);
        List<InventoryAlloc> inventoryAllocs =
                inventoryAllocService.lambdaQuery().eq(InventoryAlloc::getSkuCode, skuCode).list();
        if (combinationItem != null) {
            List<ComposeSku> composeSkus = composeSkuService.selectByCombinationId(combinationItem.getId());
            List<String> composeSkuCodes =
                    composeSkus.stream().map(ComposeSku::getSkuCode).collect(Collectors.toList());
            List<CombinationItem> combinationItems = combinationItemService.listBySkuCode(composeSkuCodes);
            List<String> combinationCodes =
                    combinationItems.stream().map(CombinationItem::getCode).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(combinationCodes)) {
                inventoryAllocs.addAll(
                        inventoryAllocService.lambdaQuery().in(InventoryAlloc::getSkuCode, combinationCodes).list());
            }
            if (CollectionUtil.isNotEmpty(composeSkuCodes)) {
                inventoryAllocs.addAll(
                        inventoryAllocService.lambdaQuery().in(InventoryAlloc::getSkuCode, composeSkuCodes).list());
            }
        } else {
            List<String> suiteNos =
                    inventoryAllocs.stream().map(InventoryAlloc::getSuiteNo).filter(StringUtil::isNotEmpty)
                            .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(suiteNos)) {
                inventoryAllocs.addAll(
                        inventoryAllocService.lambdaQuery().in(InventoryAlloc::getSkuCode, suiteNos).list());
                inventoryAllocs.addAll(
                        inventoryAllocService.lambdaQuery().in(InventoryAlloc::getSuiteNo, suiteNos).list());
            }
        }
        return inventoryAllocs.stream().distinct().collect(Collectors.toList());
    }
    
    @Override
    public WarehouseInventoryDetailVO warehouseInventoryDetail(WarehouseInventoryDetailQuery query) {
        WarehouseInventoryDetailVO warehouseInventoryDetailVO = new WarehouseInventoryDetailVO();
        String skuCode = query.getSkuCode();
        warehouseInventoryDetailVO.setSkuCode(skuCode);
        CombinationItem combinationItem = combinationItemService.getByItemCode(skuCode);
        if (combinationItem != null) {
            List<ComposeSku> composeSkus = composeSkuService.selectByCombinationId(combinationItem.getId());
            warehouseInventoryDetailVO.setSpecification(combinationItem.getName());
            List<WarehouseInventoryDetailItemVO> warehouseInventoryDetailItemVOS =
                    warehouseInventoryDetail(
                            composeSkus.stream().map(ComposeSku::getSkuCode).collect(Collectors.toList()));
            warehouseInventoryDetailVO.setItems(warehouseInventoryDetailItemVOS);
        } else {
            Optional<ItemSku> itemSkuOptional = itemSkuService.getByMixCode(skuCode);
            warehouseInventoryDetailVO.setSpecification(itemSkuOptional.map(ItemSku::getSpecifications).orElse(""));
            List<WarehouseInventoryDetailItemVO> warehouseInventoryDetailItemVOS =
                    warehouseInventoryDetail(Collections.singletonList(skuCode));
            warehouseInventoryDetailVO.setItems(warehouseInventoryDetailItemVOS);
        }
        return warehouseInventoryDetailVO;
    }
    
    
    public List<WarehouseInventoryDetailItemVO> warehouseInventoryDetail(Collection<String> specNos) {
        if (CollectionUtil.isEmpty(specNos)) {
            return Collections.emptyList();
        }
        specNos = specNos.stream().distinct().collect(Collectors.toList());
        ArrayList<WarehouseInventoryDetailItemVO> items = Lists.newArrayList();
        List<CombinationItem> combinationItems = combinationItemService.listByCombinationCode(specNos);
        List<Long> combinationIds =
                combinationItems.stream().map(CombinationItem::getId).collect(Collectors.toList());
        List<ComposeSku> allComposeSkus = composeSkuService.selectByCombinationIds(combinationIds);
        Map<Long, List<ComposeSku>> composeSkuGroupedMap =
                allComposeSkus.stream().collect(Collectors.groupingBy(ComposeSku::getCombinationId));
        List<String> allComposeSkuCodes =
                allComposeSkus.stream().map(ComposeSku::getSkuCode).collect(Collectors.toList());
        List<String> allSkuCodes = Stream.of(specNos, allComposeSkuCodes)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        List<WdtStockSpec> allStockSpecs =
                allSkuCodes.isEmpty() ? Collections.emptyList() :
                        wdtStockSpecService
                                .lambdaQuery()
                                .in(WdtStockSpec::getSpecNo, allSkuCodes)
                                .eq(WdtStockSpec::getDefect, 0)
                                .list();
        List<InventoryMonitor> allInventoryMonitors =
                allSkuCodes.isEmpty() ? Collections.emptyList() :
                        inventoryMonitorService
                                .lambdaQuery()
                                .in(InventoryMonitor::getSkuNo, allSkuCodes)
                                .eq(InventoryMonitor::getThresholdType, 1)
                                .eq(InventoryMonitor::getStatus, 1)
                                .list();
        for (String skuCode : specNos) {
            CombinationItem combinationItem =
                    combinationItems.stream()
                            .filter(v -> v.getCode().equals(skuCode))
                            .findFirst()
                            .orElse(null);
            if (combinationItem != null) {
                List<ComposeSku> composeSkus = composeSkuGroupedMap.get(combinationItem.getId());
                List<String> composeSkuCodes =
                        composeSkus.stream().map(ComposeSku::getSkuCode).collect(Collectors.toList());
                List<WdtStockSpec> stockSpecRts =
                        allStockSpecs.stream()
                                .filter(v -> composeSkuCodes.contains(v.getSpecNo()))
                                .collect(Collectors.toList());
                Map<String, List<WdtStockSpec>> stockSpecRtsGroup =
                        stockSpecRts.stream().collect(Collectors.groupingBy(WdtStockSpec::getWarehouseNo));
                List<InventoryMonitor> inventoryMonitors =
                        allInventoryMonitors.stream()
                                .filter(v -> composeSkuCodes.contains(v.getSkuNo()))
                                .collect(Collectors.toList());
                stockSpecRtsGroup.forEach(
                        (warehouseNo, group) -> {
                            WarehouseInventoryDetailItemVO
                                    warehouseInventoryDetailItemVO = new WarehouseInventoryDetailItemVO();
                            warehouseInventoryDetailItemVO.setWarehouseNo(warehouseNo);
                            warehouseInventoryDetailItemVO.setSkuCode(skuCode);
                            warehouseInventoryDetailItemVO.setWarehouseName(group.get(0).getWarehouseName());
                            ArrayList<WarehouseInventoryDetailItemComposeSkuVO> composeSkuInventoryItems =
                                    Lists.newArrayList();
                            warehouseInventoryDetailItemVO.setComposeSkus(composeSkuInventoryItems);
                            warehouseInventoryDetailItemVO.setAvailableStock(BigDecimal.ZERO);
                            warehouseInventoryDetailItemVO.setWarnStock(BigDecimal.ZERO);
                            warehouseInventoryDetailItemVO.setAllocableStock(BigDecimal.ZERO);
                            for (ComposeSku composeSku : composeSkus) {
                                WarehouseInventoryDetailItemComposeSkuVO warehouseInventoryDetailItemComposeSkuVO =
                                        new WarehouseInventoryDetailItemComposeSkuVO();
                                warehouseInventoryDetailItemComposeSkuVO.setSuiteNo(skuCode);
                                String composeSkuCode = composeSku.getSkuCode();
                                warehouseInventoryDetailItemComposeSkuVO.setSkuCode(composeSkuCode);
                                BigDecimal composeSkuTotalStock = BigDecimal.ZERO;
                                BigDecimal composeSkuAvailableStock = BigDecimal.ZERO;
                                BigDecimal composeSkuWarnStock = BigDecimal.ZERO;
                                List<WdtStockSpec> composeSkuStockSpecs =
                                        group.stream()
                                                .filter(stockSpec -> stockSpec.getSpecNo()
                                                        .equals(composeSkuCode))
                                                .collect(Collectors.toList());
                                BigDecimal composeNum = new BigDecimal(composeSku.getCount());
                                warehouseInventoryDetailItemComposeSkuVO.setAllocableStockCalculation(
                                        String.format("组合装单品[%s|%s]: 可分配库存 = 0", skuCode, composeSkuCode));
                                if (!composeSkuStockSpecs.isEmpty()) {
                                    List<InventoryMonitor> composeSkuInventoryMonitors =
                                            inventoryMonitors.stream()
                                                    .filter(
                                                            monitor ->
                                                                    monitor.getWarehouseNo().equals(warehouseNo)
                                                                            && monitor.getSkuNo()
                                                                            .equals(composeSkuCode))
                                                    .collect(Collectors.toList());
                                    int composeSkuWarnStockInt =
                                            composeSkuInventoryMonitors.stream()
                                                    .mapToInt(InventoryMonitor::getAlertThreshold)
                                                    .sum();
                                    composeSkuWarnStock = new BigDecimal(composeSkuWarnStockInt);
                                    composeSkuWarnStock =
                                            composeSkuWarnStock.divide(composeNum, 0, RoundingMode.DOWN);
                                    BigDecimal availableStock =
                                            composeSkuStockSpecs.stream()
                                                    .map(WdtStockSpec::getAvailableStock)
                                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                                    composeSkuAvailableStock =
                                            availableStock.divide(composeNum, 0, RoundingMode.DOWN);
                                    composeSkuTotalStock = composeSkuAvailableStock.subtract(composeSkuWarnStock);
                                    warehouseInventoryDetailItemComposeSkuVO.setAllocableStockCalculation(
                                            String.format(
                                                    "组合装单品[%s|%s]: 可分配库存 = 可用库存 - 警戒库存 = %s - %s = %s",
                                                    skuCode,
                                                    composeSkuCode,
                                                    composeSkuAvailableStock, composeSkuWarnStock,
                                                    composeSkuTotalStock));
                                }
                                warehouseInventoryDetailItemComposeSkuVO.setAllocableStock(composeSkuTotalStock);
                                warehouseInventoryDetailItemComposeSkuVO.setAvailableStock(composeSkuAvailableStock);
                                warehouseInventoryDetailItemComposeSkuVO.setWarnStock(composeSkuWarnStock);
                                composeSkuInventoryItems.add(warehouseInventoryDetailItemComposeSkuVO);
                            }
                            composeSkuInventoryItems.stream()
                                    .min(Comparator.comparing(
                                            WarehouseInventoryDetailItemComposeSkuVO::getAllocableStock))
                                    .ifPresent(
                                            v -> {
                                                warehouseInventoryDetailItemVO.setAvailableStock(v.getAvailableStock());
                                                warehouseInventoryDetailItemVO.setWarnStock(v.getWarnStock());
                                                warehouseInventoryDetailItemVO.setAllocableStock(v.getAllocableStock());
                                            });
                            warehouseInventoryDetailItemVO.setAllocableStockCalculation(
                                    String.format(
                                            "组合装[%s]: 组合装可分配库存 = MIN(单品可分配库存) = MIN(%s)",
                                            skuCode,
                                            composeSkuInventoryItems.stream()
                                                    .map(WarehouseInventoryDetailItemComposeSkuVO::getAllocableStock)
                                                    .map(BigDecimal::toPlainString)
                                                    .collect(Collectors.joining(", "))));
                        });
            } else {
                List<WdtStockSpec> stockSpecRts =
                        allStockSpecs.stream()
                                .filter(v -> v.getSpecNo().equals(skuCode))
                                .collect(Collectors.toList());
                List<InventoryMonitor> inventoryMonitors =
                        allInventoryMonitors.stream()
                                .filter(v -> v.getSkuNo().equals(skuCode))
                                .collect(Collectors.toList());
                Map<String, InventoryMonitor> inventoryMonitorMap =
                        inventoryMonitors.stream()
                                .collect(Collectors.toMap(InventoryMonitor::getWarehouseNo, Function.identity()));
                stockSpecRts.forEach(
                        v -> {
                            WarehouseInventoryDetailItemVO
                                    warehouseInventoryDetailItemVO = new WarehouseInventoryDetailItemVO();
                            warehouseInventoryDetailItemVO.setWarehouseNo(v.getWarehouseNo());
                            warehouseInventoryDetailItemVO.setSkuCode(skuCode);
                            warehouseInventoryDetailItemVO.setWarehouseName(v.getWarehouseName());
                            warehouseInventoryDetailItemVO.setAvailableStock(
                                    v.getAvailableStock().setScale(0, RoundingMode.HALF_UP));
                            warehouseInventoryDetailItemVO.setWarnStock(BigDecimal.ZERO);
                            InventoryMonitor inventoryMonitor = inventoryMonitorMap.get(v.getWarehouseNo());
                            if (inventoryMonitor != null) {
                                warehouseInventoryDetailItemVO.setWarnStock(
                                        BigDecimal.valueOf(inventoryMonitor.getAlertThreshold()));
                            }
                            warehouseInventoryDetailItemVO.setAllocableStock(
                                    warehouseInventoryDetailItemVO
                                            .getAvailableStock()
                                            .subtract(warehouseInventoryDetailItemVO.getWarnStock())
                                            .setScale(0, RoundingMode.HALF_UP));
                            warehouseInventoryDetailItemVO.setAllocableStockCalculation(
                                    String.format(
                                            "单品[%s]: 可分配库存 = 可用库存 - 警戒库存 = %s - %s = %s",
                                            skuCode,
                                            warehouseInventoryDetailItemVO.getAvailableStock(),
                                            warehouseInventoryDetailItemVO.getWarnStock(),
                                            warehouseInventoryDetailItemVO.getAllocableStock()));
                            items.add(warehouseInventoryDetailItemVO);
                        });
            }
        }
        return items;
    }
    
    
    @Override
    public void syncInventory(SyncInventoryCmd cmd) {
        List<Long> platformItemIds = new ArrayList<>();
        if (cmd.getPlatformItemId() != null) {
            platformItemIds.add(cmd.getPlatformItemId());
        }
        if (cmd.getPlatformItemIds() != null) {
            platformItemIds.addAll(cmd.getPlatformItemIds());
        }
        if (platformItemIds.isEmpty()) {
            return;
        }
        for (Long platformItemId : platformItemIds) {
            platformItemStockSyncBizService.syncStock(platformItemId);
            operateLogDomainService.addOperatorLog(
                    UserContext.getUserId(),
                    OperateLogTarget.PLATFORM_ITEM,
                    platformItemId,
                    "【库存同步】实时同步库存到平台"
            );
        }
    }
    
    @Override
    public void editSyncInventory(EditSyncInventoryCmd cmd) {
        List<Long> platformItemSkuIds = cmd.getItems().stream().map(EditSyncInventoryCmd.Item::getPlatformItemSkuId)
                .collect(Collectors.toList());
        if (platformItemSkuIds.isEmpty()) {
            return;
        }
        List<PlatformItemSku> platformItemSkus = platformItemSkuService.listByIds(platformItemSkuIds);
        if (platformItemSkus.isEmpty()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "平台商品规格数据异常");
        }
        Map<Long, PlatformItemSku> platformItemSkuMap =
                platformItemSkus.stream().collect(Collectors.toMap(PlatformItemSku::getId, Function.identity()));
        List<String> skuCodes =
                platformItemSkus.stream().map(PlatformItemSku::getOuterSkuCode).distinct().collect(Collectors.toList());
        List<Long> platformItemIds =
                platformItemSkus.stream().map(PlatformItemSku::getPlatformItemId).collect(Collectors.toList());
        List<PlatformItem> platformItems = platformItemService.listByIds(platformItemIds);
        if (platformItems.isEmpty()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "平台商品数据异常");
        }
        inventoryAllocShopService.setPlatformItemListSyncEnabled(platformItems);
        Map<Long, PlatformItem> allPlatformItemMap =
                platformItems.stream().collect(Collectors.toMap(PlatformItem::getId, Function.identity()));
        List<CombinationItem> allCombinationItems = combinationItemService.listByCombinationCode(skuCodes);
        Map<String, CombinationItem> allCombinationItemMap =
                allCombinationItems.stream().collect(Collectors.toMap(CombinationItem::getCode, Function.identity()));
        List<Long> allCombinationItemIds =
                allCombinationItems.stream().map(CombinationItem::getId).collect(Collectors.toList());
        List<ComposeSku> allComposeSkus = composeSkuService.selectByCombinationIds(allCombinationItemIds);
        List<String> allComposeSkuCodes =
                allComposeSkus.stream().map(ComposeSku::getSkuCode).collect(Collectors.toList());
        List<String> allSkuCodes = Stream.of(skuCodes, allComposeSkuCodes)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        List<InventoryAlloc> allInventoryAllocs = inventoryAllocService.listBySkuCode(allSkuCodes);
        
        List<InventoryAlloc> toUpdate = new ArrayList<>();
        List<InventoryAlloc> newRecords = new ArrayList<>();
        List<InventoryAlloc> toRemoved = new ArrayList<>();
        MultiValuedMap<Long, String> changeDetails = new ArrayListValuedHashMap<>();
        
        for (EditSyncInventoryCmd.Item item : cmd.getItems()) {
            PlatformItemSku platformItemSku = platformItemSkuMap.get(item.getPlatformItemSkuId());
            if (platformItemSku == null) {
                throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "平台商品规格数据异常");
            }
            PlatformItem platformItem = allPlatformItemMap.get(platformItemSku.getPlatformItemId());
            if (platformItem == null) {
                throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "平台商品数据异常");
            }
            List<InventoryAlloc> pisInventoryAllocs =
                    allInventoryAllocs.stream()
                            .filter(v -> v.getPlatformItemSkuId().equals(item.getPlatformItemSkuId()))
                            .collect(Collectors.toList());
            String skuCode = platformItemSku.getOuterSkuCode();
            Integer syncStock = item.getSyncStock();
            if (!pisInventoryAllocs.isEmpty()) {
                Predicate<InventoryAlloc> isMainRecord = InventoryAlloc::isMainRecord;
                List<InventoryAlloc> mainInventoryAllocs =
                        pisInventoryAllocs.stream().filter(isMainRecord).collect(Collectors.toList());
                if (mainInventoryAllocs.size() > 1) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND,
                            "平台商品规格库存分配数据异常，存在重复分配记录");
                }
                InventoryAlloc inventoryAlloc = mainInventoryAllocs.get(0);
                Integer effectiveNum0 = inventoryAlloc.getEffectiveNum();
                if (!effectiveNum0.equals(syncStock)) {
                    changeDetails.put(inventoryAlloc.getPlatformItemId(), String.format("SKU[%s]同步库存从[%d]改为[%d]",
                            inventoryAlloc.getSkuCode(), effectiveNum0, syncStock));
                    
                    inventoryAlloc.setEffectiveNum(syncStock);
                    toUpdate.add(inventoryAlloc);
                    
                    if (StringUtil.isNotEmpty(inventoryAlloc.getSuiteNo())) {
                        CombinationItem combinationItem = allCombinationItemMap.get(skuCode);
                        List<InventoryAlloc> inventoryAllocDetails =
                                pisInventoryAllocs.stream().filter(isMainRecord.negate()).collect(Collectors.toList());
                        
                        // 组合装明细发生变动
                        if (inventoryAllocDetails.size() != allComposeSkus.size()) {
                            toRemoved.addAll(inventoryAllocDetails);
                            for (ComposeSku composeSku : allComposeSkus) {
                                InventoryAlloc inventoryAllocDetail = new InventoryAlloc();
                                inventoryAllocDetail.setPlatformItemId(platformItemSku.getPlatformItemId());
                                inventoryAllocDetail.setPlatformItemSkuId(platformItemSku.getId());
                                inventoryAllocDetail.setShopNo(platformItemSku.getShopNo());
                                inventoryAllocDetail.setSkuCode(composeSku.getSkuCode());
                                inventoryAllocDetail.setSuiteNo(combinationItem.getCode());
                                inventoryAllocDetail.setEffectiveNum(syncStock * composeSku.getCount());
                                inventoryAllocDetail.setCalculation("人工分配");
                                newRecords.add(inventoryAllocDetail);
                            }
                        } else {
                            for (ComposeSku composeSku : allComposeSkus) {
                                for (InventoryAlloc inventoryAllocDetail : inventoryAllocDetails) {
                                    if (inventoryAllocDetail.getSkuCode().equals(composeSku.getSkuCode())) {
                                        inventoryAllocDetail.setEffectiveNum(
                                                syncStock * composeSku.getCount());
                                        inventoryAllocDetail.setCalculation("人工分配");
                                        toUpdate.add(inventoryAllocDetail);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                InventoryAlloc inventoryAlloc = new InventoryAlloc();
                inventoryAlloc.setPlatformItemId(platformItemSku.getPlatformItemId());
                inventoryAlloc.setPlatformItemSkuId(platformItemSku.getId());
                inventoryAlloc.setShopNo(platformItemSku.getShopNo());
                inventoryAlloc.setSkuCode(skuCode);
                inventoryAlloc.setEffectiveNum(syncStock);
                newRecords.add(inventoryAlloc);
                
                // 记录变更详情
                changeDetails.put(inventoryAlloc.getPlatformItemId(),
                        String.format("SKU[%s]同步库存手动分配为[%d]", inventoryAlloc.getSkuCode(),
                                syncStock));
                
                CombinationItem combinationItem = allCombinationItemMap.get(platformItemSku.getOuterSkuCode());
                if (combinationItem != null) {
                    inventoryAlloc.setSuiteNo(combinationItem.getCode());
                    inventoryAlloc.setCalculation("人工分配");
                    
                    List<ComposeSku> composeSkus =
                            allComposeSkus.stream().filter(v -> v.getCombinationId().equals(combinationItem.getId()))
                                    .collect(Collectors.toList());
                    if (!composeSkus.isEmpty()) {
                        for (ComposeSku composeSku : composeSkus) {
                            InventoryAlloc inventoryAllocDetail = new InventoryAlloc();
                            inventoryAllocDetail.setPlatformItemId(platformItemSku.getPlatformItemId());
                            inventoryAllocDetail.setPlatformItemSkuId(platformItemSku.getId());
                            inventoryAllocDetail.setShopNo(platformItemSku.getShopNo());
                            inventoryAllocDetail.setSkuCode(composeSku.getSkuCode());
                            inventoryAllocDetail.setSuiteNo(combinationItem.getCode());
                            inventoryAllocDetail.setEffectiveNum(syncStock * composeSku.getCount());
                            inventoryAllocDetail.setCalculation("人工分配");
                            newRecords.add(inventoryAllocDetail);
                        }
                    }
                }
            }
        }
        
        if (!newRecords.isEmpty()) {
            allInventoryAllocs.addAll(newRecords);
        }
        if (!toRemoved.isEmpty()) {
            allInventoryAllocs.removeAll(toRemoved);
        }
        
        List<PlatformItem> syncEnabledPlatformItems =
                platformItems.stream().filter(PlatformItem::getSyncEnabled).collect(Collectors.toList());
        if (!syncEnabledPlatformItems.isEmpty()) {
            List<WarehouseInventoryDetailItemVO> warehouseInventoryDetailItemVOS = warehouseInventoryDetail(skuCodes);
            Map<String, List<WarehouseInventoryDetailItemVO>> warehouseInventoryDetailSkuGroupedMap =
                    warehouseInventoryDetailItemVOS.stream()
                            .collect(Collectors.groupingBy(WarehouseInventoryDetailItemVO::getSkuCode));
            
            for (PlatformItem platformItem : platformItems) {
                for (PlatformItemSku platformItemSku : platformItemSkus) {
                    if (!platformItemSku.getPlatformItemId().equals(platformItem.getId())) {
                        continue;
                    }
                    String skuCode = platformItemSku.getOuterSkuCode();
                    List<InventoryAlloc> allocs =
                            allInventoryAllocs.stream().filter(v -> v.getSkuCode().equals(skuCode))
                                    .collect(Collectors.toList());
                    int totalAllocNum =
                            allocs.stream().filter(v -> Objects.nonNull(v.getEffectiveNum()))
                                    .mapToInt(InventoryAlloc::getEffectiveNum).sum();
                    List<WarehouseInventoryDetailItemVO> warehouseInventoryDetailItems =
                            warehouseInventoryDetailSkuGroupedMap.getOrDefault(skuCode, Collections.emptyList());
                    BigDecimal totalAllocableStock =
                            warehouseInventoryDetailItems.stream().filter(v -> v.getSkuCode().equals(skuCode))
                                    .map(WarehouseInventoryDetailItemVO::getAllocableStock)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (totalAllocNum > totalAllocableStock.intValue()) {
                        throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM,
                                String.format("商品SKU %s 库存分配总数 %s 大于总库存 %s", skuCode,
                                        totalAllocNum, totalAllocableStock));
                    }
                }
            }
            
        }
        if (!newRecords.isEmpty()) {
            inventoryAllocService.saveBatch(newRecords);
        }
        if (!toUpdate.isEmpty()) {
            inventoryAllocService.updateBatchById(toUpdate);
        }
        if (!toRemoved.isEmpty()) {
            inventoryAllocService.removeByIdsWithTime(
                    toRemoved.stream().map(InventoryAlloc::getId).collect(Collectors.toList()));
        }
        
        // 按平台商品分组记录操作日志
        changeDetails.asMap().forEach((platformItemId, details) -> {
            String logMessage = String.format("【库存分配】修改：%s", String.join("; ", details));
            
            operateLogDomainService.addOperatorLog(
                    UserContext.getUserId(),
                    OperateLogTarget.PLATFORM_ITEM,
                    platformItemId,
                    logMessage
            );
        });
        
    }
    
    @Override
    public void editWarnThreshold(EditWarnThresholdCmd cmd) {
        List<Long> platformItemSkuIds = cmd.getItems().stream().map(EditWarnThresholdCmd.Item::getPlatformItemSkuId)
                .collect(Collectors.toList());
        if (platformItemSkuIds.isEmpty()) {
            return;
        }
        List<InventoryAlloc> inventoryAllocs = inventoryAllocService.listByPlatformItemSkuId(platformItemSkuIds);
        
        List<InventoryAlloc> updated = new ArrayList<>();
        List<InventoryAlloc> added = new ArrayList<>();
        MultiValuedMap<Long, String> changeDetails = new ArrayListValuedHashMap<>();
        
        for (EditWarnThresholdCmd.Item item : cmd.getItems()) {
            boolean newRecord = true;
            for (InventoryAlloc inventoryAlloc : inventoryAllocs) {
                if (inventoryAlloc.getPlatformItemSkuId().equals(item.getPlatformItemSkuId())) {
                    if (!Objects.equals(inventoryAlloc.getWarnThreshold(), item.getWarnThreshold())) {
                        changeDetails.put(inventoryAlloc.getPlatformItemId(),
                                String.format("商品SKU %s 低预警库存从 %d 改为 %d",
                                        inventoryAlloc.getSkuCode(),
                                        inventoryAlloc.getWarnThreshold(),
                                        item.getWarnThreshold()));
                        
                        inventoryAlloc.setWarnThreshold(item.getWarnThreshold());
                        updated.add(inventoryAlloc);
                    }
                    newRecord = false;
                    break;
                }
            }
            if (newRecord) {
                InventoryAlloc newAlloc = new InventoryAlloc();
                PlatformItemSku pis = platformItemSkuService.getById(item.getPlatformItemSkuId());
                if (pis == null) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "平台商品规格不存在");
                }
                Long platformItemId = pis.getPlatformItemId();
                newAlloc.setPlatformItemId(platformItemId);
                newAlloc.setPlatformItemSkuId(pis.getId());
                newAlloc.setSkuCode(pis.getOuterSkuCode());
                newAlloc.setShopNo(pis.getShopNo());
                newAlloc.setWarnThreshold(item.getWarnThreshold());
                added.add(newAlloc);
                changeDetails.put(platformItemId,
                        String.format("商品SKU %s 低预警库存设置为 %d",
                                newAlloc.getSkuCode(),
                                newAlloc.getWarnThreshold()));
            }
        }
        
        if (!updated.isEmpty()) {
            inventoryAllocService.updateBatchById(updated);
        }
        if (!added.isEmpty()) {
            inventoryAllocService.saveBatch(added);
        }
        
        // 按平台商品分组记录操作日志
        changeDetails.asMap().forEach((platformItemId, details) -> {
            String logMessage = String.format("【低预警库存】设置：%s", String.join("; ", details));
            
            operateLogDomainService.addOperatorLog(
                    UserContext.getUserId(),
                    OperateLogTarget.PLATFORM_ITEM,
                    platformItemId,
                    logMessage
            );
        });
        
        sendPlatformItemLowStockAlert(inventoryAllocs);
    }
    
    @Override
    public MultiResponse<ShopDropDownItem> dropDownShopList(ShopDropDownQuery query) {
        List<String> shopSnList = platformItemService.getDaddyBaseMapper().shopSnList();
        query.setSnList(shopSnList);
        return shopBizService.dropDownList(query);
    }
    
    @Override
    public void sendPlatformItemLowStockAlert(PlatformItem platformItem, PlatformItemSku platformItemSku) {
        InventoryAllocShop inventoryAllocShop = inventoryAllocShopService.getByShopNo(platformItemSku.getShopNo());
        if (inventoryAllocShop == null) {
            return;
        }
        InventoryAlloc inventoryAlloc = inventoryAllocService.getByPlatformItemSkuId(platformItemSku.getId());
        sendPlatformItemLowStockAlert(inventoryAlloc, inventoryAllocShop, platformItem, platformItemSku);
    }
    
    @Override
    public void sendPlatformItemLowStockAlert(List<InventoryAlloc> inventoryAllocs) {
        //发送平台商品低库存预警
        for (InventoryAlloc inventoryAlloc : inventoryAllocs) {
            sendPlatformItemLowStockAlert(inventoryAlloc);
        }
    }
    
    @Override
    public void sendPlatformItemLowStockAlert(InventoryAlloc inventoryAlloc) {
        PlatformItem platformItem = platformItemService.getById(inventoryAlloc.getPlatformItemId());
        PlatformItemSku platformItemSku = platformItemSkuService.getById(inventoryAlloc.getPlatformItemSkuId());
        InventoryAllocShop inventoryAllocShop = inventoryAllocShopService.getByShopNo(inventoryAlloc.getShopNo());
        sendPlatformItemLowStockAlert(inventoryAlloc, inventoryAllocShop, platformItem, platformItemSku);
    }
    
    private void sendPlatformItemLowStockAlert(InventoryAlloc inventoryAlloc, InventoryAllocShop inventoryAllocShop,
                                               PlatformItem platformItem, PlatformItemSku platformItemSku) {
        if (platformItem == null || platformItemSku == null) {
            return;
        }
        if (platformItemSku.getStock() <= inventoryAllocShop.getEffectiveWarnThreshold(inventoryAlloc)) {
            return;
        }
        String webhook = platformItemSyncConfig.getLowInventoryAlertWebhook()
                .getOrDefault(platformItem.getPlatform().name(),
                        platformItemSyncConfig.getLowInventoryAlertWebhookDefault());
        if (StringUtil.isEmpty(webhook)) {
            log.warn("[平台商品低库存预警] Webhook 未配置，跳过发送");
            return;
        }
        Optional<Shop> shopOptional = shopService.getBySn(platformItemSku.getShopNo());
        HashMap<String, Object> variables = new HashMap<>();
        variables.put("platformName", platformItem.getPlatform().getDesc());
        variables.put("platformItemName", platformItem.getGoodsName());
        variables.put("outerItemId", platformItem.getOuterItemId());
        variables.put("outerSkuCode", platformItemSku.getOuterSkuCode());
        variables.put("skuName", platformItemSku.getSpecName());
        variables.put("stock", platformItemSku.getStock());
        variables.put("shopName", shopOptional.map(Shop::getName).orElse(platformItemSku.getShopNo()));
        variables.put("pid", inventoryAlloc.getPlatformItemId());
        variables.put("receiver", webhook);
        qyMsgSendService.send(RemindType.WEBHOOK, MsgTemplateCode.PLATFORM_ITEM_LOW_STOCK_ALERT, variables);
        log.info("[平台商品低库存预警]已发送，平台商品ID:{}，平台商品SKU ID:{}，库存:{}",
                inventoryAlloc.getPlatformItemId(),
                inventoryAlloc.getPlatformItemSkuId(), platformItemSku.getStock());
    }
}
