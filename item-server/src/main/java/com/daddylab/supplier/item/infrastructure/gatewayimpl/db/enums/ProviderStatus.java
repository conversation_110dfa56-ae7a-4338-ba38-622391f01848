package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/28 10:28 上午
 * @description
 */
@Getter
@AllArgsConstructor
public enum ProviderStatus implements IEnum<Integer> {

    All(0,"全部"),
    COOPERATION(1, "合作中"),
    STOP_COOPERATION(2, "停止合作"),

    ;
    @EnumValue
    private final Integer value;
    private final String desc;

}
