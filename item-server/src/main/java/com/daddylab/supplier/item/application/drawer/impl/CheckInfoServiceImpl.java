package com.daddylab.supplier.item.application.drawer.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.drawer.CheckInfoService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.CommonCheckInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemReq;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemResp;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/1/3
 */
@Service
@RequiredArgsConstructor
public class CheckInfoServiceImpl implements CheckInfoService {

    private final IItemService itemService;
    private final PartnerFeignClient partnerFeignClient;

    @Override
    public SingleResponse<CommonCheckInfo> getCommonCheckInfo(Long itemId) {
        final String partnerProviderItemSn = itemService.getPartnerProviderItemSn(itemId);
        if (StringUtil.isBlank(partnerProviderItemSn)) {
            return SingleResponse.of(null);
        }

        final PartnerItemReq req = new PartnerItemReq();
        req.setSearchType(1);
        req.setContext(partnerProviderItemSn);
        req.setPageIndex(1);
        req.setPageSize(10);
        final Rsp<List<PartnerItemResp>> itemQueryRsp = partnerFeignClient.itemQuery(req);
        if (Objects.isNull(itemQueryRsp) || CollUtil.isEmpty(itemQueryRsp.getData())) {
            throw ExceptionPlusFactory
                    .bizException(ErrorCode.DATA_NOT_FOUND,
                            "商品可能关联了无效的P系统商品款号：" + partnerProviderItemSn + "，未能在P系统查询到对应商品信息");
        }

        final Integer partnerItemId = itemQueryRsp.getData().get(0).getId();
        final Rsp<CommonCheckInfo> commonCheckInfoRsp = partnerFeignClient.commonCheckInfo(
                (long) partnerItemId);
        if (!commonCheckInfoRsp.getFlag()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "从P系统查询检测报告失败");
        }
        return SingleResponse.of(commonCheckInfoRsp.getData());
    }
}
