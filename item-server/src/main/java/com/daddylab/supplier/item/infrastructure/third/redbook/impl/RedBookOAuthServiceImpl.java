package com.daddylab.supplier.item.infrastructure.third.redbook.impl;

import com.daddylab.supplier.item.infrastructure.third.config.RedBookConfig;
import com.daddylab.supplier.item.infrastructure.third.redbook.RedBookOAuthService;
import com.xiaohongshu.fls.opensdk.client.OauthClient;
import com.xiaohongshu.fls.opensdk.entity.oauth.request.GetAccessTokenRequest;
import com.xiaohongshu.fls.opensdk.entity.oauth.request.RefreshTokenRequest;
import com.xiaohongshu.fls.opensdk.entity.oauth.response.GetAccessTokenResponse;
import com.xiaohongshu.fls.opensdk.entity.oauth.response.RefreshTokenResponse;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/4/9
 */
@Service
public class RedBookOAuthServiceImpl implements RedBookOAuthService {
    private final RedBookConfig redBookConfig;
    private final OauthClient oauthClient;

    public RedBookOAuthServiceImpl(RedBookConfig redBookConfig) {
        this.redBookConfig = redBookConfig;
        oauthClient = new OauthClient(redBookConfig.getApiUrl(),
                redBookConfig.getAppId(),
                redBookConfig.getVersion(),
                redBookConfig.getAppSecret());

    }

    @Override
    public String getAuthorizationUrl(String redirectUri, String state) {
        return String.format(
                "https://ark.xiaohongshu.com/ark/authorization?appId=%s&redirectUri=%s&state=%s",
                redBookConfig.getAppId(),
                redirectUri,
                state);
    }

    @Override
    public GetAccessTokenResponse getAccessToken(String code) {
        try {
            final GetAccessTokenRequest request = new GetAccessTokenRequest();
            request.setCode(code);

            return oauthClient.execute(request).getData();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public RefreshTokenResponse refreshToken(String refreshToken) {
        try {
            final RefreshTokenRequest request = new RefreshTokenRequest();
            request.setRefreshToken(refreshToken);
            return oauthClient.execute(request).getData();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
