package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.ComposeSkuCmd;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.Id;
import org.javers.core.metamodel.annotation.PropertyName;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ComposeSku implements Serializable {

    private static final long serialVersionUID = 1L;

    @DiffIgnore
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @DiffIgnore
    private Long deletedAt;

    @DiffIgnore
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    @DiffIgnore
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    @DiffIgnore
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    @DiffIgnore
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    @DiffIgnore
    @TableLogic
    private Integer isDel;

    /**
     * 组合商品的id
     */
    @DiffIgnore
    private Long combinationId;

    /**
     * 构成组合商品的skuId
     */
    @DiffIgnore
    private Long skuId;

    /**
     * 构成组合商品的skuCode
     */
    @PropertyName("单品sku编码")
    @Id
    private String skuCode;

    @DiffIgnore
    private Long itemId;

    /**
     * 构成组合商品的sku的itemCode
     */
    @DiffIgnore
    private String itemCode;

    @PropertyName("单品sku数量")
    private Integer count;

    /**
     * 金额占比
     */
//    @Deprecated
//    @DiffIgnore
//    @PropertyName("单品金额占比")
//    private String amountProportion;

    @PropertyName("成本金额占比")
    private BigDecimal costProportion;

    @PropertyName("销售金额占比")
    private BigDecimal saleProportion;

    public Boolean isChange(ComposeSkuCmd composeSkuCmd) {
        if (composeSkuCmd.getSkuId().equals(this.getSkuId())) {
            if (this.getCount().equals(composeSkuCmd.getCount())) {
                return false;
            }
        }
        return true;
    }


}
