package com.daddylab.supplier.item.application.auth;

import com.daddylab.supplier.item.common.domain.dto.Command;
import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import com.daddylab.supplier.item.infrastructure.bizLevelDivision.BizDivisionContext;
import com.daddylab.supplier.item.infrastructure.common.ExternalUserContext;
import com.daddylab.supplier.item.infrastructure.common.LoginType;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @since 2024/5/29
 */
@ControllerAdvice
@Slf4j
public class UserRequestControllerAdvice extends RequestBodyAdviceAdapter {
    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    @Override
    public @NonNull Object afterBodyRead(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
;
        if (body instanceof Command) {
            Command command = (Command) body;
            if (UserContext.getUserId() != null) {
                command.setCurrentUserId(UserContext.getUserId());
            }
            if (ExternalUserContext.get() != null) {
                command.setCurrentUserId(ExternalUserContext.get().getId());
                command.setLoginType(LoginType.EXTERNAL_USER);
            }
            log.debug("[UserRequestControllerAdvice]: {}", command);
        }
        if (body instanceof PageQuery) {
            PageQuery query = (PageQuery) body;
            if (UserContext.getUserId() != null) {
                query.setCurrentUserId(UserContext.getUserId());
            }
            if (ExternalUserContext.get() != null) {
                query.setCurrentUserId(ExternalUserContext.get().getId());
                query.setLoginType(LoginType.EXTERNAL_USER);
            }
            log.debug("[UserRequestControllerAdvice]: {}", query);
        }
        return body;
    }
}
