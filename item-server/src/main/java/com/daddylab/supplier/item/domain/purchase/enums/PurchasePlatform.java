package com.daddylab.supplier.item.domain.purchase.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName PurchasePlatform.java
 * @description
 * @createTime 2021年11月19日 22:00:00
 */
@Getter
@AllArgsConstructor
public enum PurchasePlatform implements IEnum<Integer> {
    ALL(0, "ALL"),
    TAOBAO(1, "淘宝"),
    DOUDIAN(2, "抖店"),
    REDBOOK(3, "小红书"),
    LAOBASHOP(4, "自研电商"),
    YOUZAN(5, "有赞"),
    KUAISHOU(6, "快手");
    final public Integer value;
    final public String desc;

}
