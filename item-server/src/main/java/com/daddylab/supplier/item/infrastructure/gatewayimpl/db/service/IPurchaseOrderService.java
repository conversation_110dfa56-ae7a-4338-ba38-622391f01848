package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderPageQuery;
import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderPageVO;
import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderViewVO;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PurchaseOrderState;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 采购订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
public interface IPurchaseOrderService extends IDaddyService<PurchaseOrder> {

    List<PurchaseOrderPageVO> page(PurchaseOrderPageQuery pageQuery);

    /**
     * 获取此采购单下最新入库单的入库状态
     *
     * @param purchaseId
     * @return
     */
    Integer getNewestStockInState(Long purchaseId);

    PurchaseOrderViewVO getView(Long id);

    /**
     * 和采购单关联的入库单编号
     *
     * @param purchaseOrderId 采购单id
     * @param stockInState    入库单状态
     * @return 关联到的入库单编号
     */
    List<String> getStockInOrderNoByState(Long purchaseOrderId, List<Integer> stockInState);

    void deleteWithNo(String no);

    boolean isOwnOrder(Long purchaseOrderId);

    Optional<PurchaseOrder> getLatestCode(String code);

    PurchaseOrder getByNo(String no);

    void updateStatus(Collection<String> nos, PurchaseOrderState state);

//    void writeOffOrders(Collection<String> nos);

    void deleteRelatedOrders(String purchaseOrderNo);

    String getOverrideNo(String oldNo);

    String getWarehouseNo(Long purchaseOrderId);

    List<PurchaseOrder> listBySkuCode(String skuNo);
}
