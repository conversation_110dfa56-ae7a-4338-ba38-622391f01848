package com.daddylab.supplier.item.application.afterSaleLogistics.domain;

import com.daddylab.mall.wdtsdk.apiv2.statistic.dto.SearchLogisticsTraceResponse;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.ErpLogisticsStatus;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.WdtLogisticsStatus;
import com.daddylab.supplier.item.common.trans.TimeTransMapper;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.KdyCallback;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtLogisticsTrace;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.KdyLogisticsStatus;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @since 2024/12/23
 */
@Mapper(uses = {TimeTransMapper.class})
public interface LogisticsTraceConvert {
  LogisticsTraceConvert INSTANCE =
      org.mapstruct.factory.Mappers.getMapper(LogisticsTraceConvert.class);

  @Mapping(target = "receiverAddress", ignore = true)
  @Mapping(target = "status", source = "status")
  @Mapping(target = "trackList", source = "tracklist.trackList")
  @Mapping(target = "trackTime", source = "trackTimeSeconds")
  LogisticsTraceData kdyCallbackToLogisticsTraceData(KdyCallback kdyCallback);

  @Mapping(target = "receiverAddress", source = "receiverArea")
  @Mapping(target = "trackList", source = "detailListObj")
  @Mapping(target = "status", source = "logisticsStatus")
  @Mapping(target = "trackTime", source = "logisticsTime")
  LogisticsTraceData wdtLogisticsTraceToLogisticsTraceData(WdtLogisticsTrace wdtLogisticsTrace);

  @Mapping(target = "trackStatus", source = "acceptStation")
  @Mapping(target = "trackDate", source = "acceptTime")
  @Mapping(
      target = "shortStatus",
      expression = "java(toWdtLogisticsStatusDesc(detailListItem.getTraceStatus()))")
  LogisticsTraceItem wdtDetailListItemToLogisticsTraceItem(
      SearchLogisticsTraceResponse.DetailListItem detailListItem);

  default WdtLogisticsStatus toWdtLogisticsStatus(Integer enumValue) {
    return IEnum.getEnumByValue(WdtLogisticsStatus.class, enumValue);
  }

  default String toWdtLogisticsStatusDesc(Integer enumValue) {
    return IEnum.getEnumOptByValue(WdtLogisticsStatus.class, enumValue)
        .map(IEnum::getDesc)
        .orElse("");
  }

  default ErpLogisticsStatus kdyLogisticsStatusToErpLogisticsStatus(KdyLogisticsStatus status) {
    if (status == null) {
      return ErpLogisticsStatus.NONE;
    }
    switch (status) {
      case COUNTER_SIGNED:
        return ErpLogisticsStatus.COUNTER_SIGNED;
      case SIGNED:
        return ErpLogisticsStatus.SIGNED;
      case UNSIGNED:
      case UNSIGNED_AFTER_SIX_DAYS:
        return ErpLogisticsStatus.UNSIGNED;
      case NO_RECORD_AFTER_TWO_DAYS:
      case NO_PROGRESS_AFTER_THREE_DAYS:
        return ErpLogisticsStatus.IN_TRANSIT;
      case PROBLEM:
        return ErpLogisticsStatus.DIFFICULT;
      case DELIVERING:
        return ErpLogisticsStatus.DELIVERING;
      case NONE:
      default:
        return ErpLogisticsStatus.NONE;
    }
  }

  default ErpLogisticsStatus wdtLogisticsStatusToErpLogisticsStatus(WdtLogisticsStatus status) {
    if (status == null) {
      return ErpLogisticsStatus.NONE;
    }
    switch (status) {
      case WAIT_PICKUP:
        return ErpLogisticsStatus.WAIT_FOR_SEND;
      case ALREADY_PICKUP:
        return ErpLogisticsStatus.PICKUP;
      case IN_TRANSIT:
        return ErpLogisticsStatus.IN_TRANSIT;
      case WAIT_DISTRIBUTION:
        return ErpLogisticsStatus.DELIVERING;
      case ALREADY_SIGNED:
        return ErpLogisticsStatus.SIGNED;
      case REJECTION:
      case ALREADY_HANDLED:
      case RETURNING:
      case ALREADY_RETURNED:
      case PROBLEM:
      case INTERCEPT_SUCCESS:
      case INTERCEPT_FAIL:
        return ErpLogisticsStatus.DIFFICULT;
      case WAIT_QUERY:
      case UNSUBSCRIBED:
      case PUSH_FAIL:
      default:
        return ErpLogisticsStatus.NONE;
    }
  }
}
