package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 采购入库单状态 1.待提交、2.待入库、3.已入库、4.已取消
 * 2022-06-20 新增状态 21.部分入库
 *
 * <AUTHOR> up
 * @date 2022/3/25 10:44 下午
 */
@Getter
@AllArgsConstructor
public enum StockInState implements IEnum {


    /**
     * 消除黄线
     */
    WAIT_SUBMIT(1, "待提交"),
    WAIT_IN(2, "待入库"),

    /**
     * 2022-06-20 新增状态 部分入库
     */
    PART_IN(21, "部分入库"),

    HAVE_IN(3, "已入库"),
    CANCEL(4, "已取消"),


    DEFAULT(0, "默认"),
    REVERSE_FIXED(8, "冲销流程,此单据为原单据的逆向单据"),
    WRITE_OFF(9, "冲销流程,原单据冲销作废"),
    FIX_ORDER(10,"冲销流程,原单据的修正单据")

    ;

    @EnumValue
    private final Integer value;
    private final String desc;
}
