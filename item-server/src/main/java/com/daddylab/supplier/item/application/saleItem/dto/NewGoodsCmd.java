package com.daddylab.supplier.item.application.saleItem.dto;

import com.alibaba.cola.dto.Command;
import com.daddylab.supplier.item.infrastructure.domain.PropertyAlias;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.Id;
import org.javers.core.metamodel.annotation.PropertyName;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName NewGoodsCmd.java
 * @description
 * @createTime 2022年04月18日 16:34:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("新品商品新增实体")
public class NewGoodsCmd extends Command {
    private static final long serialVersionUID = -3631173817215641033L;

    @ApiModelProperty(value = "id")
    @Id
    private Long id;

    @Id
    public Long getId() {
        return id;
    }

    @ApiModelProperty(value = "上新计划ID")
    @PropertyName("上新时间")
    @DiffIgnore
    private Long planId;

    @PropertyName("上新时间")
    @DiffIgnore
    public Long getPlanId() {
        return planId == null ? 0L : planId;
    }

    @ApiModelProperty(value = "产品负责人id")
    @PropertyName("产品负责人")
    @DiffIgnore
    private Long principalId;

    @PropertyName("产品负责人")
    @DiffIgnore
    public Long getPrincipalId() {
        return principalId == null ? 0L : principalId;
    }

    @ApiModelProperty(value = "新品活动开始周期")
    @PropertyName("新品活动开始周期")
    @PropertyAlias("activePeriod")
    private Long activePeriodStart;

    @PropertyName("新品活动开始周期")
    public Long getActivePeriodStart() {
        return activePeriodStart;
    }

    @ApiModelProperty(value = "新品活动结束周期")
    @PropertyName("新品活动结束周期")
    @PropertyAlias("activePeriod")
    private Long activePeriodEnd;

    @PropertyName("新品活动结束周期")
    public Long getActivePeriodEnd() {
        return activePeriodEnd;
    }

    @ApiModelProperty(value = "新品活动周期是否长期有效")
    @PropertyName("新品活动周期是否长期有效")
    @PropertyAlias("activePeriod")
    private Boolean isLongTerm;

    @PropertyName("新品活动周期是否长期有效")
    public Boolean getIsLongTerm() {
        return isLongTerm;
    }

    @ApiModelProperty(value = "是否支持7天无理由退换 0:是 1:否")
    @PropertyName("是否支持7天无理由退换")
    private Integer noReason;

    @PropertyName("是否支持7天无理由退换")
    public Integer getNoReason() {
        return noReason;
    }

    @ApiModelProperty(value = "产品划线价")
    @PropertyName("产品划线价")
    private BigDecimal linePrice;

    @PropertyName("产品划线价")
    public BigDecimal getLinePrice() {
        return linePrice;
    }

    @ApiModelProperty(value = "产品日销价")
    @PropertyName("产品日销价")
    private BigDecimal dailyPrice;

    @ApiModelProperty(value = "日常活动机制")
    @PropertyName("日常活动机制")
    private String dailyActivities;

    @PropertyName("日常活动机制")
    public String getDailyActivities() {
        return dailyActivities;
    }

    @PropertyName("产品日销价")
    public BigDecimal getDailyPrice() {
        return dailyPrice;
    }

    @ApiModelProperty(value = "S级/新品活动价")
    @PropertyName("S级/新品活动价")
    private BigDecimal activePrice;

    @PropertyName("S级/新品活动价")
    public BigDecimal getActivePrice() {
        return activePrice;
    }

    @ApiModelProperty(value = "S级/新品活动机制")
    @PropertyName("S级/新品活动机制")
    private String activeContent;

    @PropertyName("S级/新品活动机制")
    public String getActiveContent() {
        return activeContent;
    }

    @ApiModelProperty(value = "渠道活动最低价")
    @PropertyName("渠道活动最低价")
    private BigDecimal channelLowest;

    @PropertyName("渠道活动最低价")
    public BigDecimal getChannelLowest() {
        return channelLowest;
    }

    @ApiModelProperty(value = "新品直播机制")
    @PropertyName("新品直播机制")
    private String liveActive;

    @PropertyName("新品直播机制")
    public String getLiveActive() {
        return liveActive;
    }

    @ApiModelProperty(value = "是否参与满减 0:是 1:否")
    @PropertyName("是否参与满减")
    private Integer isReduce;

    @PropertyName("是否参与满减")
    public Integer getIsReduce() {
        return isReduce;
    }

    @ApiModelProperty(value = "发货类型 1:工厂发货 2:仓库发货")
    @PropertyName("发货类型")
    private Integer shipmentType;

    @PropertyName("发货类型")
    public Integer getShipmentType() {
        return shipmentType;
    }

    @ApiModelProperty(value = "发货地")
    @PropertyName("发货地")
    private String shipmentArea;

    @PropertyName("发货地")
    public String getShipmentArea() {
        return shipmentArea;
    }

    @ApiModelProperty(value = "48小时发货 0:是 1:否")
    @PropertyName("是否48小时发货")
    private Integer shipmentAging;

    @PropertyName("是否48小时发货")
    public Integer getShipmentAging() {
        return shipmentAging;
    }

    @ApiModelProperty(value = "物流")
    @PropertyName("物流")
    private String logistics;

    @PropertyName("物流")
    public String getLogistics() {
        return logistics;
    }

    @ApiModelProperty(value = "快递模板")
    @PropertyName("快递模板")
    private String expressTemplate;

    @PropertyName("快递模板")
    public String getExpressTemplate() {
        return expressTemplate;
    }

    @ApiModelProperty(value = "备注")
    @PropertyName("备注")
    private String remark;

    @PropertyName("备注")
    public String getRemark() {
        return remark;
    }

    @ApiModelProperty(value = "是否叠加券")
    @PropertyName("是否叠加券")
    private Integer isCoupon;

    @PropertyName("是否叠加券")
    public Integer getIsCoupon() {
        return isCoupon;
    }

    @DiffIgnore
    @NotBlank(message = "更新类型不得为空。你要更新的是spu还是sku呢？？你得选一个")
    public String updateType;

    @DiffIgnore
    public Long itemId;

    @PropertyName("运营意见反馈")
    @Size(max = 2048)
    private String runFeedback;

    @PropertyName("运营意见反馈")
    public String getRunFeedback() {
        return runFeedback;
    }

    @ApiModelProperty("A级活动售价")
    @PropertyName("A级活动售价")
    @Size(max = 200)
    private String aLevelActivityPrice;

    @PropertyName("A级活动售价")
    public String getALevelActivityPrice() {
        return aLevelActivityPrice;
    }

    @PropertyName("A级活动赠品")
    public String getALevelActivityGift() {
        return aLevelActivityGift;
    }

    @PropertyName("A级活动直播价")
    public String getALevelActivityLivePrice() {
        return aLevelActivityLivePrice;
    }

    @PropertyName("A级活动直播赠品")
    public String getALevelActivityLiveGift() {
        return aLevelActivityLiveGift;
    }

    @PropertyName("S级大促售价")
    public String getSLevelPromotePrice() {
        return sLevelPromotePrice;
    }

    @PropertyName("S级大促机制")
    public String getSLevelPromoteRule() {
        return sLevelPromoteRule;
    }

    @PropertyName("S级一口价/直播价")
    public String getSLevelPromoteLivePrice() {
        return sLevelPromoteLivePrice;
    }

    @PropertyName("S级一口价活动机制/直播机制")
    public String getSLevelPromoteLiveRule() {
        return sLevelPromoteLiveRule;
    }

    @ApiModelProperty("A级活动赠品")
    @PropertyName("A级活动赠品")
    @Size(max = 200)
    private String aLevelActivityGift;

    @ApiModelProperty("A级活动直播价")
    @PropertyName("A级活动直播价")
    @Size(max = 200)
    private String aLevelActivityLivePrice;

    @ApiModelProperty("A级活动直播赠品")
    @PropertyName("A级活动直播赠品")
    @Size(max = 200)
    private String aLevelActivityLiveGift;

    @ApiModelProperty("S级大促售价")
    @PropertyName("S级大促售价")
    @Size(max = 200)
    private String sLevelPromotePrice;

    @ApiModelProperty("S级大促机制")
    @PropertyName("S级大促机制")
    @Size(max = 200)
    private String sLevelPromoteRule;

    @ApiModelProperty("S级一口价/直播价")
    @PropertyName("S级一口价/直播价")
    @Size(max = 200)
    private String sLevelPromoteLivePrice;

    @ApiModelProperty("S级一口价活动机制/直播机制")
    @PropertyName("S级一口价活动机制/直播机制")
    @Size(max = 200)
    private String sLevelPromoteLiveRule;

}
