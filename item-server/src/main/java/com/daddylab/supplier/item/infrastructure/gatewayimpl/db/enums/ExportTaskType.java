package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/13 11:47 下午
 * @description
 */
@Getter
@AllArgsConstructor
public enum ExportTaskType implements IEnum<Integer> {

    /**
     * 后端商品导出任务。（包括根据sku维度和商品维度）
     */
    BACK_ITEM(0, "后端商品"),

    BACK_ITEM_SKU(1, "后端商品sku"),

    COMBINATION_ITEM(2, "组合商品"),

    BRAND(3, "品牌"),

    PURCHASE(4, "采购"),

    PURCHASE_ORDER(5, "采购订单明细"),

    STOCK_IN_ORDER(6, "采购入库"),

    STOCK_OUT_ORDER(7, "退料出库"),

    SALE_ITEM_LIBRARY(8, "销售商品库"),

    NEW_GOODS(9, "新品商品库"),

    HANDING_SHEET(10, "盘货表"),
    HANDING_SHEET_ITEMS(11, "盘货表关联的商品"),
    ITEM_REFERENCE_SPU(12, "商品参照表SPU"),
    ITEM_REFERENCE_SKU(13, "商品参照表SKU"),

    AFTER_SALES_ABNORMAL(14, "异常件信息"),

    REFUND_ORDER(15, "退换单"),

    SETTLEMENT_BILL(16, "采购结算-结算明细导出3合一"),
    SETTLEMENT_DETAIL(17, "采购结算-订单明细导出"),
    AFTER_SALES_REGISTER(18, "客服售后登记表"),
    ORDER_DELIVERY_TRACE(19, "订单发货跟踪"),


    ALL_SETTLEMENT_INFO(20, "采购结算-结算导出4合一"),

    ALL_SETTLEMENT_INFO_ZIP(21, "批量采购结算导出zip"),
    PURCHASE_STEP_PRICE(23, "采购阶梯价格"),

    PAYMENT_APPLY_ORDER(24, "付款申请单导出"),

    PURCHASE_ORDER_DETAIL(25, "采购订单明细"),
    STOCK_SPEC(26, "库存管理"),

    VIRTUAL_WAREHOUSE(27, "虚拟仓"),

    WAREHOUSE(28, "仓库列表"),
    AFTER_SALES_FORWARDING(29, "客服转寄登记"),
    ENTRY_ACTIVITY_PRICE(30, "入驻活动价"),

    OFF_SHELF(31, "商品下架"),

    AFTER_SALES_LOGISTICS_ABNORMAL(32, "物流异常信息"),

    PURCHASE_CONTACT(33,"采购合同")


    ;

    @EnumValue
    private final Integer value;

    private final String desc;


    public static ItemDelivery convert(Integer type) {
        return IEnum.getEnumByValue(ItemDelivery.class, type);
    }
}
