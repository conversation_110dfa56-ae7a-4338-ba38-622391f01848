package com.daddylab.supplier.item.application.shop.domain;

import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/12/5
 */
@Data
public class ShopOperatorMapVO {
    Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 运营负责人ID
     */
    private Long operatorUid;

    /**
     * 运营负责人姓名
     */
    private String operatorName;

    private String shopNo;

    private String shopName;

    private List<Integer> businessLine;

    public void setBusinessLine(String businessLine) {
        this.businessLine = Stream.of(businessLine.split(",")).map(Integer::parseInt).collect(Collectors.toList());
    }
}
