package com.daddylab.supplier.item.infrastructure.winrobot360;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2022/9/16
 */
@ConfigurationProperties(prefix = "winrobot360")
@Configuration
@Data
@RefreshScope
public class Winrobot360Config {

    private String url;
    private String accessKeyId;
    private String accessKeySecret;
    private String scheduleUuid;
    private String editTbGoodsRobotUuid;
    /**
     * 影刀任务并行数
     */
    private Integer concurrentTaskNum = 1;

}
