package com.daddylab.supplier.item.application.platformItemSkuInventory.domain.dto;

import com.daddylab.supplier.item.application.platformItemSkuInventory.domain.constants.PlatformItemEventType;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @class PlatformItemSkuInventoryChangeDto.java
 * @description 描述类的作用
 * @date 2024-03-15 17:40
 */
@Data
@AllArgsConstructor(staticName = "of")
@NoArgsConstructor
public class PlatformItemChangeEvent implements Serializable {
    private static final long serialVersionUID = -7671547108748852513L;
    private Platform platform;
    private String shopNo;
    private PlatformItemEventType type;
    private List<ChangeItem> items;

    @Data
    public static class ChangeItem {
        private String itemId;
        private List<String> skuIds;
    }
}
