package com.daddylab.supplier.item.application.platformItem;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.goods.ApiGoodsAPI;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.UploadApiSpecNoParam;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.UploadApiSpecNoResponse;
import com.daddylab.supplier.item.application.platformItem.data.PlatformItemWarnListItem;
import com.daddylab.supplier.item.application.platformItem.data.PlatformItemWarnWithItemData;
import com.daddylab.supplier.item.application.platformItem.query.PlatformItemWarnPageQuery;
import com.daddylab.supplier.item.application.platformItem.trans.PlatformItemTransMapper;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemBaseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemWarnMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemSkuService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemWarnService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.github.yulichang.toolkit.MPJWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2021/12/28
 */
@Service
@Slf4j
public class PlatformItemWarnBizServiceImpl implements PlatformItemWarnBizService {

    @Autowired IPlatformItemWarnService platformItemWarnService;
    @Autowired PlatformItemMapper platformItemMapper;
    @Autowired PlatformItemWarnMapper platformItemWarnMapper;
    @Autowired IPlatformItemService platformItemService;
    @Autowired IPlatformItemSkuService platformItemSkuService;
    @Autowired ItemGateway itemGateway;
    @Autowired ItemSkuGateway itemSkuGateway;

    @Qualifier("UserGatewayCacheImpl")
    @Autowired
    UserGateway userGateway;

    @Autowired ShopGateway shopGateway;
    @Autowired WdtGateway wdtGateway;

    private static void setBusinessLineFilter(
            MPJLambdaWrapper<PlatformItemWarn> queryWrapper, List<Integer> businessLine) {
        queryWrapper.and(
                q -> {
                    for (Integer line : businessLine) {
                        switch (line) {
                            case 0:
                                q.or().eq(Shop::getIsBusinessLine0, 1);
                                break;
                            case 1:
                                q.or().eq(Shop::getIsBusinessLine1, 1);
                                break;
                            case 2:
                                q.or().eq(Shop::getIsBusinessLine2, 1);
                                break;
                            case 3:
                                q.or().eq(Shop::getIsBusinessLine3, 1);
                                break;
                        }
                    }
                });
    }

    @Override
    public PageResponse<PlatformItemWarnListItem> pageQuery(PlatformItemWarnPageQuery pageQuery) {
        pageQuery.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(pageQuery.getBusinessLine()));
        if (CollUtil.isEmpty(pageQuery.getBusinessLine())) {
            return ResponseFactory.emptyPage();
        }
        List<Long> filterShopIds = null;
        if (!pageQuery.isShowAll()) {
            final List<Long> principalShopIds = shopGateway.selectPrincipalShopIds(pageQuery.getCurrentUserId());
            if (principalShopIds.isEmpty()) {
                return ResponseFactory.emptyPage();
            }
            filterShopIds = principalShopIds;
        }
        final IPage<PlatformItemWarnWithItemData> page = pageQuery.getPage();
        final MPJLambdaWrapper<PlatformItemWarn> joinQuery = MPJWrappers.lambdaJoin();
        //noinspection unchecked
        joinQuery
                .innerJoin(
                        PlatformItem.class,
                        PlatformItem::getId,
                        PlatformItemWarn::getPlatformItemId)
                .innerJoin(
                        PlatformItemSku.class,
                        PlatformItemSku::getId,
                        PlatformItemWarn::getPlatformItemSkuId)
                .leftJoin(Shop.class, Shop::getSn, PlatformItem::getShopNo)
                .and(
                        q -> {
                            setBusinessLineFilter(q, pageQuery.getBusinessLine());
                            q.or().isNull(Shop::getBusinessLine);
                        })
                .eq(PlatformItemWarn::getStatus, pageQuery.getWarnStatus())
                .eq(
                        Objects.nonNull(pageQuery.getPlatform()),
                        PlatformItem::getPlatform,
                        pageQuery.getPlatform())
                .eq(
                        NumberUtil.isPositive(pageQuery.getItemId()),
                        PlatformItem::getItemId,
                        pageQuery.getItemId())
                .eq(
                        NumberUtil.isPositive(pageQuery.getPlatformItemId()),
                        PlatformItem::getId,
                        pageQuery.getPlatformItemId())
                .eq(
                        StringUtil.isNotBlank(pageQuery.getOuterItemId()),
                        PlatformItem::getOuterItemId,
                        pageQuery.getOuterItemId())
                .eq(
                        NumberUtil.isPositive(pageQuery.getShopId()),
                        PlatformItem::getShopId,
                        pageQuery.getShopId())
                .in(Objects.nonNull(filterShopIds), PlatformItem::getShopId, filterShopIds)
                .eq(
                        Objects.nonNull(pageQuery.getItemStatus()),
                        PlatformItem::getStatus,
                        pageQuery.getItemStatus())
                .selectAll(PlatformItemWarn.class)
                .selectAs(PlatformItemWarn::getStatus, PlatformItemWarnWithItemData::getWarnStatus)
                .selectAll(PlatformItem.class)
                .selectAs(
                        PlatformItem::getStatus,
                        PlatformItemWarnWithItemData::getPlatformItemStatus)
                .selectAs(PlatformItem::getModified, PlatformItemWarnWithItemData::getModified)
                .selectAs(PlatformItemSku::getSkuId, PlatformItemWarnWithItemData::getSkuId)
                .selectAs(PlatformItemSku::getSkuCode, PlatformItemWarnWithItemData::getSkuCode)
                .orderByDesc(PlatformItemWarn::getUpdatedAt, PlatformItemWarn::getId);
        platformItemWarnMapper.selectJoinPage(page, PlatformItemWarnWithItemData.class, joinQuery);
        final List<PlatformItemWarnWithItemData> records = page.getRecords();
        if (records.isEmpty()) {
            return ResponseFactory.emptyPage(pageQuery);
        }

        // 提取后端商品ID，查询后端商品基本信息
        final List<Long> itemIds =
                records.stream()
                        .map(PlatformItemWarnWithItemData::getItemId)
                        .filter(NumberUtil::isPositive)
                        .distinct()
                        .collect(Collectors.toList());
        Map<Long, ItemBaseDO> itemBaseInfoMap = Collections.emptyMap();
        if (!itemIds.isEmpty()) {
            itemBaseInfoMap =
                    itemGateway.getBaseInfoWithImage(itemIds).stream()
                            .collect(Collectors.toMap(ItemBaseDO::getId, Function.identity()));
        }

        // 查询操作人信息
        final List<Long> operatorIds =
                records.stream()
                        .map(PlatformItemWarnWithItemData::getUpdatedUid)
                        .filter(NumberUtil::isPositive)
                        .distinct()
                        .collect(Collectors.toList());

        // 查询店铺信息
        final List<Long> shopIds =
                records.stream()
                        .map(PlatformItemWarnWithItemData::getShopId)
                        .filter(NumberUtil::isPositive)
                        .distinct()
                        .collect(Collectors.toList());
        // 查询店铺负责人
        final List<ShopPrincipal> shopPrincipals = shopGateway.batchQueryShopPrincipals(shopIds);
        final Map<Long, Long> shopPrincipalIdsMap =
                shopPrincipals.stream()
                        .collect(
                                Collectors.toMap(
                                        ShopPrincipal::getShopId,
                                        ShopPrincipal::getStaffId,
                                        (a, b) -> a));
        final List<Long> allStaffIds =
                Stream.concat(operatorIds.stream(), shopPrincipalIdsMap.values().stream())
                        .filter(NumberUtil::isPositive)
                        .distinct()
                        .collect(Collectors.toList());
        // 查询店铺负责人员工信息
        final Map<Long, StaffInfo> allStaffInfos =
                userGateway.batchQueryStaffInfoByIds(allStaffIds);
        Map<Long, ItemBaseDO> finalItemBaseInfoMap = itemBaseInfoMap;
        final List<PlatformItemWarnListItem> platformItemWarnListItems =
                records.stream()
                        .map(
                                platformItemWarnWithItemData -> {
                                    final Optional<ItemBaseDO> itemBaseData =
                                            Optional.ofNullable(
                                                            platformItemWarnWithItemData
                                                                    .getItemId())
                                                    .map(finalItemBaseInfoMap::get);
                                    final Optional<StaffInfo> operator =
                                            Optional.ofNullable(
                                                            platformItemWarnWithItemData
                                                                    .getUpdatedUid())
                                                    .map(allStaffInfos::get);
                                    final StaffInfo shopPrincipal =
                                            Optional.ofNullable(
                                                            platformItemWarnWithItemData
                                                                    .getShopId())
                                                    .map(shopPrincipalIdsMap::get)
                                                    .map(allStaffInfos::get)
                                                    .orElse(null);
                                    return PlatformItemTransMapper.INSTANCE
                                            .toPlatformItemWarnListItem(
                                                    platformItemWarnWithItemData,
                                                    itemBaseData.orElse(null),
                                                    operator.orElse(null),
                                                    shopPrincipal);
                                })
                        .collect(Collectors.toList());
        return PageResponse.of(
                platformItemWarnListItems,
                (int) page.getTotal(),
                (int) page.getSize(),
                (int) page.getCurrent());
    }

    @Override
    public Response handle(Long id) {
        final PlatformItemWarn platformItemWarn = platformItemWarnService.getById(id);
        if (Objects.isNull(platformItemWarn)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未找到指定平台商品警告");
        }

        platformItemWarn.setStatus(PlatformItemWarnStatus.HANDLED);
        platformItemWarnService.updateById(platformItemWarn);
        return Response.buildSuccess();
    }

    @Override
    public Response handleBatch(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Response.buildSuccess();
        }
        platformItemWarnService.lambdaUpdate().in(PlatformItemWarn::getId, ids)
                               .set(PlatformItemWarn::getStatus, PlatformItemWarnStatus.HANDLED)
                               .set(PlatformItemWarn::getUpdatedUid, UserContext.getUserId())
                               .set(PlatformItemWarn::getUpdatedAt, DateUtil.currentTime())
                               .update();
        return Response.buildSuccess();
    }

    @Override
    @XxlJob("autoHandleWarn")
    public void autoHandleWarn() {
        int pageIndex = 1;
        int pageSize = 1000;
        final LambdaQueryChainWrapper<PlatformItemWarn> query =
                platformItemWarnService
                        .lambdaQuery()
                        .eq(PlatformItemWarn::getStatus, PlatformItemWarnStatus.UNHANDLED);
        final Integer count = query.count();
        final double maxPage = Math.ceil(count / ((float) pageSize));
        log.info("自动处理平台商品警告 待处理警告共计：{}", count);
        while (pageIndex <= maxPage) {
            log.info("自动处理平台商品警告 {}/{}", pageIndex, maxPage);
            final Page<PlatformItemWarn> page = new Page<>(pageIndex, pageSize, false);
            query.page(page);
            final List<PlatformItemWarn> platformItemWarns = page.getRecords();
            if (platformItemWarns.isEmpty()) {
                break;
            }
            for (PlatformItemWarn platformItemWarn : platformItemWarns) {
                if (platformItemWarn.getWarnType() == PlatformItemWarnType.SKU_NOT_MATCH) {
                    autoHandleNotMatch(platformItemWarn);
                }
            }
            pageIndex++;
        }
    }

    @Override
    public Response uploadPlatformSpecNo(Long id, String specNo) {
        final ApiGoodsAPI api = wdtGateway.getAPI(ApiGoodsAPI.class);
        final UploadApiSpecNoParam param = new UploadApiSpecNoParam();
        final PlatformItemSku platformItemSku =
                platformItemSkuService
                        .lambdaQuery()
                        .eq(PlatformItemSku::getId, id)
                        .oneOpt()
                        .orElseThrow(
                                () ->
                                        ExceptionPlusFactory.bizException(
                                                ErrorCode.DATA_NOT_FOUND, "平台商品不存在"));
        param.setPtGoodsId(platformItemSku.getOuterItemId());
        param.setPtSpecId(platformItemSku.getOuterSkuId());
        param.setSpecNo(specNo);
        param.setShopNo(platformItemSku.getShopNo());
        try {
            final UploadApiSpecNoResponse uploadApiSpecNoResponse = api.uploadApiSpecNo(param);
            return Response.buildSuccess();
        } catch (WdtErpException e) {
            return Response.buildFailure(ErrorCode.GATEWAY_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 自动处理后端商品未匹配的警告，若当前商品已经匹配到了，就将平台商品报警移除
     *
     * @param platformItemWarn 平台商品报警
     * @deprecated 现在由 PlatformItemWarnTask 自动处理失效警告
     */
    private void autoHandleNotMatch(PlatformItemWarn platformItemWarn) {
        final Long platformItemSkuId = platformItemWarn.getPlatformItemSkuId();
        final Optional<PlatformItemSku> platformItemSkuOptional =
                platformItemSkuService
                        .lambdaQuery()
                        .eq(PlatformItemSku::getId, platformItemSkuId)
                        .oneOpt();
        // 如果对应的平台商品规格已经被删除，将这条警告也删除掉
        if (!platformItemSkuOptional.isPresent()) {
            log.debug(
                    "自动处理平台商品警告：由于平台商品规格已经被删除，移除警告记录，{}({})",
                    platformItemWarn.getPlatformItemId(),
                    platformItemWarn.getPlatformItemSkuId());
            platformItemWarnService.removeById(platformItemWarn.getId());
            return;
        }
        final PlatformItemSku platformItemSku = platformItemSkuOptional.get();
        // 如果平台商品已经关联了SKU，将警告移除
        if (NumberUtil.isPositive(platformItemSku.getSkuId())) {
            log.debug(
                    "自动处理平台商品警告：已匹配后端商品 {}({})",
                    platformItemWarn.getPlatformItemId(),
                    platformItemWarn.getPlatformItemSkuId());
            platformItemWarnService.removeById(platformItemWarn.getId());
        }
        // 如果平台商品已经关联了组合装，将警告移除
        if (NumberUtil.isPositive(platformItemSku.getCombinationItemId())) {
            log.debug(
                    "自动处理平台商品警告：已匹配组合装 {}({})",
                    platformItemWarn.getPlatformItemId(),
                    platformItemWarn.getPlatformItemSkuId());
            platformItemWarnService.removeById(platformItemWarn.getId());
        }
    }
}
