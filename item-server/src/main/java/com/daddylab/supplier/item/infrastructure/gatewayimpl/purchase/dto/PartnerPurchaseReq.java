package com.daddylab.supplier.item.infrastructure.gatewayimpl.purchase.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName PartnerPurchaseReq.java
 * @description
 * @createTime 2021年11月18日 15:45:00
 */
@Data
public class PartnerPurchaseReq implements Serializable {

    private static final long serialVersionUID = 7864441429402342311L;

    private String organizationName;
    private String itemNames;
    private Integer pageSize;


}
