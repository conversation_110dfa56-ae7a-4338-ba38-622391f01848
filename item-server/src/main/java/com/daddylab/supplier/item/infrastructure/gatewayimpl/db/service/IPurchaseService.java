package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.controller.purchase.dto.PurchaseQueryPage;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Purchase;

/**
 * <p>
 * 采购确认 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
public interface IPurchaseService extends IDaddyService<Purchase> {

    Integer countExport(PurchaseQueryPage param);

    /**
     * wdt订单明细在 采购价格表的取值
     *
     * @param skuCode      wdt商家编码。wdt组合商品编码
     * @param payTime      支付时间
     * @param platformType 平台类型
     * @return
     */
//    BigDecimal getCostPriceForWdtDetail(String skuCode, Long payTime, Integer platformType);

}
