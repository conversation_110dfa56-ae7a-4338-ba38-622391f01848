package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayApplyAuditStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 申请付款单审核流程表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PayApplyAuditProcess implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 创建者（关联入库单/出库单/其他应付的创建人）
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 申请付款id
     */
    private Long applyOrderId;

    /**
     * 审核序列
     */
    private Integer seq;

    /**
     * 节点角色审核信息详情id
     */
    private Long nodeId;

    /**
     * 此节点审核状态。1:过审。-1:拒绝。0:未处理
     */
    private PayApplyAuditStatus nodeStatus;

    private Integer autoAudit;


}
