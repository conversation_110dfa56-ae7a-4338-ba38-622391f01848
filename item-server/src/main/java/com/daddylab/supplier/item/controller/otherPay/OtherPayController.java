package com.daddylab.supplier.item.controller.otherPay;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.application.otherPay.OtherPayBizService;
import com.daddylab.supplier.item.common.domain.dto.IdCmd;
import com.daddylab.supplier.item.controller.otherPay.dto.OtherPayCmd;
import com.daddylab.supplier.item.controller.otherPay.dto.OtherPayQueryPage;
import com.daddylab.supplier.item.controller.otherPay.dto.OtherPayVo;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.otherPay.dto.OtherAuditDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

//import com.daddylab.supplier.item.application.aws.dto.AwsApprovalNodeDTO;

/**
 * <AUTHOR>
 * @ClassName OtherPayController.java
 * @description 其他应付单
 * @createTime 2022年03月24日 15:36:00
 */
@Slf4j
@Api(value = "其他应付单API", tags = "其他应付单API")
@RestController
@RequestMapping("/otherPay")
public class OtherPayController {

    @Autowired
    private OtherPayBizService otherPayBizService;
    @Autowired
    private OperateLogBizService operateLogBizService;

    @ResponseBody
    @ApiOperation(value = "其他应付单分页")
    @PostMapping("/viewList")
    public PageResponse<OtherPayVo> viewList(@RequestBody OtherPayQueryPage otherPayQueryPage) {
        return otherPayBizService.queryPage(otherPayQueryPage);
    }

    @ResponseBody
    @ApiOperation(value = "根据id查询应付单信息")
    @GetMapping("/getById")
    public SingleResponse<OtherPayVo> getById(@ApiParam("其他应付单id") Long id) {
        return otherPayBizService.getById(id);
    }

    @ResponseBody
    @ApiOperation(value = "新增或修改或提交应付单")
    @PostMapping("/createOrUpdate")
    public Response update(@RequestBody @Validated OtherPayCmd cmd) {
        return otherPayBizService.createOrUpdateOtherPay(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "删除应付单")
    @PostMapping("/delete")
    public Response delete(@RequestBody IdCmd idCmd) {
        return otherPayBizService.delete(idCmd.getId());
    }

    @ResponseBody
    @ApiOperation(value = "撤回应付单")
    @PostMapping("/revocation")
    public Response revocation(@RequestBody IdCmd idCmd) {
        return otherPayBizService.revocation(idCmd.getId());
    }

    @ResponseBody
    @ApiOperation(value = "审核应付单")
    @PostMapping("/audit")
    public Response audit(@RequestBody @Valid OtherAuditDto otherAuditDto) {
        return otherPayBizService.audit(otherAuditDto);
    }

    @ResponseBody
    @ApiOperation(value = "反审核应付单")
    @PostMapping("/unAudit")
    public Response unAudit(@RequestBody IdCmd idCmd) {
        return otherPayBizService.unAudit(idCmd.getId());
    }

    @ApiOperation(value = "操作记录")
    @GetMapping(value = "/getLog")
    public Response getOperateLogs(Long targetId) {
        return operateLogBizService.getOperateLogs(OperateLogTarget.OTHER_PAY, targetId);
    }

}
