package com.daddylab.supplier.item.application.item;

import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.item.syncBanniu.ItemSyncBanniuException;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.application.warehouse.WarehouseGateway;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.thread.ThreadExecutors;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.banniu.BanniuCommonService;
import com.daddylab.supplier.item.domain.banniu.BanniuMiniService;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.item.gateway.BuyerGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemProcurementGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemBanniuRefType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemLaunchPlanService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl.ItemBanniuRefServiceImpl;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.types.banniu.ShippingWarehouseTask;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Flowable;
import io.reactivex.rxjava3.internal.functions.Functions;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.multiset.HashMultiSet;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.backoff.BackOffExecution;
import org.springframework.util.backoff.ExponentialBackOff;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Nullable;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/9/4
 */
@Service
@RequiredArgsConstructor
@Slf4j
@RequestMapping
@Validated
public class ItemSyncBanniuBizServiceImpl implements ItemSyncBanniuBizService {
    private final ItemGateway itemGateway;
    private final ItemSkuGateway itemSkuGateway;
    private final CategoryGateway categoryGateway;
    private final ItemProcurementGateway itemProcurementGateway;
    private final WarehouseGateway warehouseGateway;
    private final BanniuMiniService banniuMiniService;
    private final ItemBanniuRefServiceImpl itemBanniuRefService;
    private final RefreshConfig refreshConfig;
    private final BanniuShippingWarehouseConfig banniuShippingWarehouseConfig;
    private final IItemLaunchPlanService itemLaunchPlanService;
    private final BuyerGateway buyerGateway;
    private final StaffService staffService;
    private final BanniuCommonService banniuCommonService;

    @Override
    @RequestMapping(method = RequestMethod.POST, path = "/dev/itemSyncBanniu/syncBatchByItemIds")
    @ResponseBody
    public void syncBatchByItemIds(@ApiParam("后端商品ID") @RequestBody @NotEmpty List<Long> itemIds) {
        for (Long itemId : itemIds) {
            try {
                syncOneByItemId(itemId);
            } catch (Exception e) {
                log.error("【班牛发货仓同步|后端商品|批量ID同步】同步异常:{}", itemId, e);
            }
        }
    }

    @Override
    @SneakyThrows
    public void syncOneByItemId(Long itemId) {
        final Item item = itemGateway.getItem(itemId);
        Assert.notNull(item, "后端商品未找到");
        syncOneByItemModel(item);
    }

    private void syncOneByItemModel(Item item) {
        final Long itemId = item.getId();
        final List<ItemBanniuRef> refs =
                itemBanniuRefService
                        .lambdaQuery()
                        .eq(ItemBanniuRef::getType, ItemBanniuRefType.ITEM)
                        .eq(ItemBanniuRef::getFirstId, itemId)
                        .list();

        final Optional<ItemProcurement> itemProcurementOpt =
                Optional.ofNullable(itemProcurementGateway.getProcurementByItemId(itemId));

        final Optional<Category> categoryOpt =
                Optional.ofNullable(item.getCategoryId()).map(categoryGateway::getById);
        final String category =
                categoryOpt
                        .map(Category::getPath)
                        .map(
                                path ->
                                        StrUtil.split(path, '/', true, true).stream()
                                                .limit(2)
                                                .collect(Collectors.joining("/")))
                        .orElse("无");

        final Warehouse warehouse =
                Optional.ofNullable(item.getWarehouseNo())
                        .flatMap(warehouseGateway::getWarehouse)
                        .orElseThrow(
                                () ->
                                        ExceptionPlusFactory.bizException(
                                                ErrorCode.DATA_NOT_FOUND,
                                                "商品关联的仓库不存在，itemId:" + item.getId()));

        final Map<Long, Long> estimateSaleTimeBatch =
                itemLaunchPlanService.getEstimateSaleTimeBatch(Collections.singletonList(itemId));
        final LocalDateTime estimateSaleTime =
                Optional.ofNullable(estimateSaleTimeBatch.get(itemId))
                        .map(DateUtil::toLocalDateTime)
                        .orElse(null);

        Optional<DadStaffVO> buyerBanniuUserOptional =
                itemProcurementOpt
                        .map(ItemProcurement::getBuyerId)
                        .flatMap(buyerGateway::buyer)
                        .map(Buyer::getUserId)
                        .map(staffService::getStaff);

        final List<Long> orderPersonnelIds =
                StrUtil.splitTrim(warehouse.getOrderPersonnel(), ',').stream()
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
        final Map<Long, DadStaffVO> staffMap = staffService.getStaffMap(orderPersonnelIds);
        final String orderPersonnelNicks =
                staffMap.values().stream()
                        .map(DadStaffVO::getNickname)
                        .collect(Collectors.joining(","));

        final List<ItemSku> skuList = itemSkuGateway.getSkuList(itemId);
        if (skuList.isEmpty()) {
            log.warn("【班牛发货仓同步｜后端商品】商品无规格 itemId={}", itemId);
            return;
        }

        final ThreadPoolTaskExecutor threadPool = ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL);
        final CountDownLatch countDownLatch = new CountDownLatch(skuList.size());
        final ArrayList<ItemSyncBanniuException.ErrorItem> errorItems = new ArrayList<>();
        for (ItemSku itemSku : skuList) {
            final Integer banniuId = getBanniuId(refs, itemSku);
            final ShippingWarehouseTask shippingWarehouseTask = new ShippingWarehouseTask();
            final LocalDateTime now = LocalDateTime.now();
            if (banniuId == null) {
                shippingWarehouseTask.setCreateTime(now);
                shippingWarehouseTask.setCreatorUserId(
                        banniuShippingWarehouseConfig.getSysUserId());
            } else {
                shippingWarehouseTask.setUpdateTime(now);
            }
            if (!banniuShippingWarehouseConfig.getNotUpdateCompleteUserId()) {
                shippingWarehouseTask.setCompleteUserId(shippingWarehouseTask.getCreatorUserId());
            }
            shippingWarehouseTask.setCompleteTime(now);
            shippingWarehouseTask.setShelfTime(estimateSaleTime);
            shippingWarehouseTask.setBuyerUser(
                    buyerBanniuUserOptional.map(DadStaffVO::getNickname).orElse(null));
            shippingWarehouseTask.setCategory(category);
            shippingWarehouseTask.setItemName(item.getName());
            shippingWarehouseTask.setItemSpec(itemSku.getSpecifications());
            shippingWarehouseTask.setItemCode(itemSku.getSupplierCode());
            shippingWarehouseTask.setWarehouse(warehouse.getName());
            shippingWarehouseTask.setSpuCode(item.getSupplierCode());
            shippingWarehouseTask.setSpuName(item.getName());
            shippingWarehouseTask.setOrderUser(orderPersonnelNicks);
            shippingWarehouseTask.setGoodsType(itemSku.getGoodsType().getDesc());
            if (banniuId == null) {
                CompletableFuture.supplyAsync(
                                () ->
                                        banniuMiniService.taskCreate(
                                                banniuShippingWarehouseConfig
                                                        .getShippingWarehouseProjectId(),
                                                banniuShippingWarehouseConfig.getSysUserId(),
                                                shippingWarehouseTask),
                                threadPool)
                        .whenComplete(
                                (r, e) -> {
                                    countDownLatch.countDown();
                                    final ItemBanniuRef itemBanniuRef = new ItemBanniuRef();
                                    itemBanniuRef.setBanniuId(String.valueOf(r));
                                    itemBanniuRef.setType(ItemBanniuRefType.ITEM);
                                    itemBanniuRef.setFirstId(String.valueOf(item.getId()));
                                    itemBanniuRef.setSecondId(String.valueOf(itemSku.getId()));
                                    if (e == null) {
                                        try {
                                            itemBanniuRefService.save(itemBanniuRef);
                                        } catch (Exception ex) {
                                            log.error(
                                                    "【班牛发货仓同步｜后端商品】保存关联关系失败 itemId={} skuId={} {}",
                                                    item.getId(),
                                                    itemSku.getId(),
                                                    shippingWarehouseTask,
                                                    ex);
                                            errorItems.add(
                                                    new ItemSyncBanniuException.ErrorItem(
                                                            item.getId(),
                                                            itemSku.getId(),
                                                            "保存关联关系失败",
                                                            true));
                                        }
                                    } else {
                                        log.error(
                                                "【班牛发货仓同步｜后端商品】同步失败 itemId={} skuId={} {}",
                                                item.getId(),
                                                itemSku.getId(),
                                                shippingWarehouseTask,
                                                e);
                                        errorItems.add(
                                                new ItemSyncBanniuException.ErrorItem(
                                                        item.getId(),
                                                        itemSku.getId(),
                                                        "保存关联关系失败",
                                                        true));
                                    }
                                });
            } else {
                CompletableFuture.runAsync(
                                () ->
                                        banniuMiniService.taskUpdate(
                                                banniuShippingWarehouseConfig
                                                        .getShippingWarehouseProjectId(),
                                                banniuShippingWarehouseConfig.getSysUserId(),
                                                shippingWarehouseTask,
                                                banniuId),
                                threadPool)
                        .whenComplete(
                                (r, e) -> {
                                    if (e != null) {
                                        log.error(
                                                "【班牛发货仓同步｜后端商品】更新失败 itemSkuId:{} {} {}",
                                                itemSku.getId(),
                                                banniuId,
                                                shippingWarehouseTask,
                                                e);
                                        errorItems.add(
                                                new ItemSyncBanniuException.ErrorItem(
                                                        item.getId(), itemSku.getId(), e));
                                    }
                                    countDownLatch.countDown();
                                });
            }
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException ignored) {
        }
        if (!errorItems.isEmpty()) {
            throw new ItemSyncBanniuException(ItemBanniuRefType.COMBINATION_ITEM, errorItems);
        }
    }

    @Override
    public void syncAll() {
        syncAll(Integer.MAX_VALUE);
    }

    @Override
    public void syncAll(int limit) {
        final HashMultiSet<String> stats = new HashMultiSet<>();
        final ExecutorService executorService =
                ThreadExecutors.newBlockOfferExecutor("item-sync-banniu", 8, 8);
        final List<Item> itemList = itemGateway.getItemList();
        final ExponentialBackOff backOff = new ExponentialBackOff(2000, 2);
        backOff.setMaxElapsedTime(15_000);
        final LoadingCache<Object, BackOffExecution> backOffs =
                Caffeine.newBuilder().build(k -> backOff.start());
        int count = 0;
        for (Item item : itemList) {
            if (++count > limit) {
                break;
            }
            final Long id = item.getId();
            log.debug("【班牛发货仓同步｜全量同步后端商品】处理中，itemId:{}", id);
            Completable.fromCompletionStage(
                            CompletableFuture.runAsync(
                                            () -> syncOneByItemModel(item), executorService)
                                    .thenRun(
                                            () -> {
                                                stats.add("同步成功");
                                                log.debug("【班牛发货仓同步｜全量同步后端商品】同步完成，itemId:{}", id);
                                            }))
                    .doOnError(
                            e -> {
                                log.error("【班牛发货仓同步｜全量同步后端商品】同步异常，itemId:{}", id, e);
                                stats.add("同步失败");
                            })
                    .retryWhen(
                            throwableObservable ->
                                    throwableObservable.flatMap(
                                            e -> {
                                                if (!(e instanceof IOException
                                                        | e instanceof ItemSyncBanniuException)) {
                                                    return Flowable.error(e);
                                                }
                                                final BackOffExecution backOffExecution =
                                                        Objects.requireNonNull(backOffs.get(id));
                                                final long nextBackOff =
                                                        backOffExecution.nextBackOff();
                                                if (nextBackOff > 0) {
                                                    stats.add("失败重试");
                                                    log.error(
                                                            "【班牛发货仓同步｜全量同步后端商品】同步异常，{}ms 后重试，itemId:{}",
                                                            id,
                                                            nextBackOff,
                                                            e);
                                                    return Flowable.timer(
                                                            nextBackOff, TimeUnit.MILLISECONDS);
                                                }
                                                return Flowable.error(
                                                        new RuntimeException("重试未成功:" + id));
                                            }))
                    .subscribe(Functions.EMPTY_ACTION, Functions.emptyConsumer());
        }
        executorService.shutdown();
        try {
            executorService.awaitTermination(30, TimeUnit.SECONDS);
        } catch (InterruptedException ignored) {
        }
        log.info("【班牛发货仓同步｜全量同步后端商品】同步完成 {}", stats);
    }

    @Nullable
    private static Integer getBanniuId(List<ItemBanniuRef> refs, ItemSku itemSku) {
        return refs.stream()
                .filter(ref -> ref.getSecondId().equals(itemSku.getId().toString()))
                .findAny()
                .map(ItemBanniuRef::getBanniuId)
                .map(Integer::parseInt)
                .orElse(null);
    }
}
