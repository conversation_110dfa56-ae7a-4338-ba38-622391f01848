package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> up
 * @date 2023年12月05日 10:13 AM
 */
@AllArgsConstructor
@Getter
public enum PayApplyStatus implements IEnum<Integer> {
    /**
     *
     */
    WAIT(0, "待申请"),
    APPLYING(1, "申请中"),
    FINISHED(2, "已完成");


    @EnumValue
    private final Integer value;
    private final String desc;
}
