package com.daddylab.supplier.item.controller.test.dto;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.metadata.data.WriteCellData;

/**
 * <AUTHOR> up
 * @date 2024年01月17日 10:14 AM
 */
public class ProviderStatusConvert implements Converter<Integer> {

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) throws Exception {
        Integer value = context.getValue();
        // 1:合作 2停用
        if (1 == value) {
            return new WriteCellData<>("合作");
        } else {
            return new WriteCellData<>("停用");
        }
    }
}
