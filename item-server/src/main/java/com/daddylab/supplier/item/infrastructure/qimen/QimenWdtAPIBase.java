package com.daddylab.supplier.item.infrastructure.qimen;

import cn.hutool.core.util.ReflectUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig.Config;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.qimencloud.api.QimenCloudClient;
import com.taobao.api.ApiException;
import com.taobao.api.BaseTaobaoRequest;
import com.taobao.api.TaobaoResponse;
import java.lang.reflect.InvocationTargetException;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/1/17
 */
public class QimenWdtAPIBase {

    private final QimenCloudClient qimenClient;
    private final QimenConfig qimenConfig;
    private final WdtConfig wdtConfig;
    private final int configIndex;

    public QimenWdtAPIBase(QimenCloudClient qimenClient,
            QimenConfig qimenConfig,
            WdtConfig wdtConfig, int configIndex) {
        this.qimenClient = qimenClient;
        this.qimenConfig = qimenConfig;
        this.wdtConfig = wdtConfig;
        this.configIndex = configIndex;
    }

    @SuppressWarnings({"unchecked"})
    public <T extends TaobaoResponse> T execute(
            Object params) throws ApiException {
        return execute(params, null, null);
    }

    @SuppressWarnings({"unchecked"})
    public <T extends TaobaoResponse> T execute(
            Object params,
            Long pageIndex,
            Long pageSize) throws ApiException {
        final Class<?> paramsClass = params.getClass();
        final Class<?> requestClass = paramsClass.getEnclosingClass();
        return (T) execute(requestClass, params, pageIndex, pageSize);
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    public <T extends TaobaoResponse> T execute(Class requestClass,
            Object params,
            Long pageIndex,
            Long pageSize) throws ApiException {
        final Config config = wdtConfig.getConfigs().get(configIndex);

        try {
            if (!BaseTaobaoRequest.class.isAssignableFrom(requestClass) || BaseTaobaoRequest.class
                    .equals(requestClass)) {
                throw new RuntimeException("请求类不是淘宝请求子类");
            }
            final BaseTaobaoRequest taobaoRequest = (BaseTaobaoRequest) requestClass.newInstance();
            final String requestClassName = requestClass.getName();
            ReflectUtil.getMethodByName(requestClass, "setDatetime")
                    .invoke(taobaoRequest, DateUtil.formatNow());
            if (pageIndex != null || pageSize != null) {
                final String pagerClassName = requestClassName + "$Pager";
                Class<?> pagerClass = Class.forName(pagerClassName);
                Object pager = pagerClass.newInstance();
                ReflectUtil.getMethodByName(pagerClass, "setPageSize").invoke(pager, pageSize);
                ReflectUtil.getMethodByName(pagerClass, "setPageNo").invoke(pager, pageIndex);
                ReflectUtil.getMethod(requestClass, "setPager", pagerClass)
                        .invoke(taobaoRequest, pager);
            }
            final String paramsClassName = requestClassName + "$Params";
            final Class<?> paramsClass = Class.forName(paramsClassName);
            ReflectUtil.getMethod(requestClass, "setParams", paramsClass)
                    .invoke(taobaoRequest, params);
            ReflectUtil.getMethodByName(requestClass, "setWdtAppkey")
                    .invoke(taobaoRequest, config.getKey());
            ReflectUtil.getMethodByName(requestClass, "putOtherTextParam")
                    .invoke(taobaoRequest, "wdt3_customer_id",
                            Optional.ofNullable(config.getSid1()).orElse(config.getSid()));
            ReflectUtil.getMethodByName(requestClass, "setWdtSalt")
                    .invoke(taobaoRequest, config.getSalt());
            ReflectUtil.getMethodByName(requestClass, "setWdtSign").invoke(taobaoRequest,
                    Utils.getQimenCustomWdtSign(taobaoRequest, config.getRealSecret()));
            ReflectUtil.getMethodByName(requestClass, "setTargetAppKey")
                    .invoke(taobaoRequest, qimenConfig.getWdtTargetKey());
            return (T) qimenClient.execute(taobaoRequest);
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException | InvocationTargetException e) {
            throw new ApiException("构造请求对象失败", e);
        }
    }

}
