package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherPayDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OtherPayDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOtherPayDetailService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 其他应付支付明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Service
public class OtherPayDetailServiceImpl extends DaddyServiceImpl<OtherPayDetailMapper, OtherPayDetail> implements IOtherPayDetailService {

}
