package com.daddylab.supplier.item.infrastructure.logback;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.LoggingEvent;
import ch.qos.logback.classic.turbo.TurboFilter;
import ch.qos.logback.core.spi.FilterReply;

import cn.hutool.core.collection.ConcurrentHashSet;

import com.alibaba.cola.dto.DTO;
import com.daddylab.supplier.item.infrastructure.timing.StopWatch;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;

import org.apache.commons.collections4.MultiSet;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.commons.text.translate.AggregateTranslator;
import org.apache.commons.text.translate.CharSequenceTranslator;
import org.apache.commons.text.translate.EntityArrays;
import org.apache.commons.text.translate.LookupTranslator;
import org.slf4j.Marker;

import java.util.*;

public class LogInterceptor extends TurboFilter {

    final ConcurrentHashSet<Class<?>> disabledClasses = new ConcurrentHashSet<>();

    public static final Map<CharSequence, CharSequence> ESCAPE_MAP = new HashMap<>();

    static {
        ESCAPE_MAP.put("\"", "\\\"");
        ESCAPE_MAP.put("\\", "\\\\");
    }

    final CharSequenceTranslator escaper =
            new AggregateTranslator(
                    new LookupTranslator(Collections.unmodifiableMap(ESCAPE_MAP)),
                    new LookupTranslator(EntityArrays.JAVA_CTRL_CHARS_ESCAPE));

    @Override
    public FilterReply decide(
            Marker marker,
            Logger logger,
            Level level,
            String format,
            Object[] params,
            Throwable t) {
        boolean logChanged = false;

        if (params != null) {
            for (int i = 0; i < params.length; i++) {
                Object obj = params[i];
                if (isMatchClass(obj)) {
                    try {
                        if (obj instanceof Enum) {
                            params[i] = ((Enum<?>) obj).name();
                        } else {
                            params[i] = StringEscapeUtils.escapeJava(JsonUtil.objToStr(obj));
                        }
                        logChanged = true;
                    } catch (Exception e) {
                        disableClass(obj);
                        logger.warn("Class:{} JSON序列化失败,暂时对其禁用序列化逻辑", obj.getClass().getName());
                    }
                } else if (obj instanceof CharSequence) {
                    final CharSequence str = (CharSequence) obj;
                    if (str.length() >= 2
                            && (str.charAt(0) == '{' && str.charAt(str.length() - 1) == '}'
                                    || str.charAt(0) == '['
                                            && str.charAt(str.length() - 1) == ']')) {
                        params[i] = escaper.translate(str.toString());
                        logChanged = true;
                    }
                }
            }

            if (logChanged) {
                addLoggingEvent(marker, logger, level, format, params, t);
                return FilterReply.DENY;
            }

            return FilterReply.NEUTRAL;
        }

        return FilterReply.NEUTRAL;
    }

    private void addLoggingEvent(
            Marker marker,
            Logger logger,
            Level level,
            String format,
            Object[] params,
            Throwable t) {
        if (logger.isEnabledFor(level)) {
            LoggingEvent le = new LoggingEvent(Logger.FQCN, logger, level, format, t, params);
            le.setMarker(marker);
            logger.callAppenders(le);
        }
    }

    private boolean isMatchClass(Object obj) {
        return obj != null
                && !isDisabledClass(obj)
                && !(obj instanceof Throwable)
                && !(obj instanceof StopWatch)
                && ((obj instanceof Collection && !(obj instanceof MultiSet))
                        || obj instanceof DTO
                        || Optional.of(obj)
                                .map(Object::getClass)
                                .map(Class::getPackage)
                                .map(Package::getName)
                                .filter(v -> v.startsWith("com.daddylab"))
                                .isPresent());
    }

    private void disableClass(Object obj) {
        disabledClasses.add(obj.getClass());
    }

    private boolean isDisabledClass(Object obj) {
        return disabledClasses.contains(obj.getClass());
    }
}
