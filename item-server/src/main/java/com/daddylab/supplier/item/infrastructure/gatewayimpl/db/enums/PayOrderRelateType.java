package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 修复应付单过程中，生成的新应付单数据和原单的关联关系枚举
 */
@AllArgsConstructor
@Getter
public enum PayOrderRelateType implements IEnum<Integer> {
    /**
     *
     */
    ALL_HEDGE(1, "全部冲销生成的应付单"),
    FIXED(2, "根据修复数据新生成的应付单");


    @EnumValue
    private final Integer value;
    private final String desc;
}
