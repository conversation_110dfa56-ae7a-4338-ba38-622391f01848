package com.daddylab.supplier.item.domain.user.dto;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("员工下拉列表查询")
public class StaffDropDownQuery extends PageQuery {
    private static final long serialVersionUID = -8999817112047436223L;

    @ApiModelProperty("非必填。如果存在值，搜索（真实姓名）")
    private String name;

    @ApiModelProperty("非必填。如果存在值，搜索（花名）")
    private String nickname;

    @ApiModelProperty("部门名称（支持多个，英文逗号分割）递归查询（包含子部门成员）")
    private String dept;

    @ApiModelProperty("部门名称（支持多个，英文逗号分割）非递归查询（仅包含当前部门直属成员）")
    private String exactDept;

    @ApiModelProperty("用户id")
    private Long userId;
}
