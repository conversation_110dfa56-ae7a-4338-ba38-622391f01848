package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 旺店通其他入库单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtOtherStockInOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 入库单ID
     */
    private Long stockinId;

    /**
     * 入库单号
     */
    private String orderNo;

    /**
     * 10=已取消，20=编辑中，30=待审核/待处理，37=待质检 ，40=质检待确认，80=已完成
     */
    private Integer status;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 入库时间
     */
    private LocalDateTime stockinTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 入库原因
     */
    private String reason;

    /**
     * 入库单备注
     */
    private String remark;

    /**
     * 货品总数
     */
    private BigDecimal goodsCount;

    /**
     * 物流类型，点击查看 物流代码表 （单据没有设置物流公司时 不返回）
     */
    private Integer logisticsType;

    /**
     * 审核时间
     */
    private LocalDateTime checkTime;

    /**
     * 其它入库业务单号
     */
    private String srcOrderNo;

    /**
     * 操作员（操作员为系统时不返回）
     */
    private String operatorName;

    /**
     * 总成本价
     */
    private BigDecimal totalPrice;


}
