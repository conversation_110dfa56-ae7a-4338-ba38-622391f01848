package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AwsBusinessLog;

/**
 * <p>
 * 炎黄盈动业务日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
public interface IAwsBusinessLogService extends IDaddyService<AwsBusinessLog> {

    /**
     * 根据业务id查询对应流程id
     * @param businessId
     * @return
     */
    String getProcessIdByBusinessId(PurchaseTypeEnum typeEnum, Long businessId);


    /**
     * 根据流程实例id查询
     *
     * @param processInstId
     * @return
     */
    AwsBusinessLog getByProcessInstId(String processInstId);

    /**
     * 根据流程实例ID移除记录
     *
     * @param processInstId 流程实例ID
     */
    default void removeByProcessInstId(String processInstId) {
        final LambdaQueryWrapper<AwsBusinessLog> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AwsBusinessLog::getProcessId, processInstId);
        remove(queryWrapper);
    }
}
