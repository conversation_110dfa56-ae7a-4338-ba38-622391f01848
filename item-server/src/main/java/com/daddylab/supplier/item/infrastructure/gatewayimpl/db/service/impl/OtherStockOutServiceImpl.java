package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockOut;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OtherStockOutMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOtherStockOutService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 其他出库单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@Service
public class OtherStockOutServiceImpl extends DaddyServiceImpl<OtherStockOutMapper, OtherStockOut> implements IOtherStockOutService {

}
