package com.daddylab.supplier.item.domain.category;

import com.daddylab.supplier.item.infrastructure.utils.StringUtil;

public class CategoryUtils {

    public static String getLastCategoryName(String categoryPath) {
        if (StringUtil.isBlank(categoryPath)) {
            return "";
        }
        String categoryName = categoryPath;
        final int lastIndexOfEle = categoryName.lastIndexOf('/');
        if (lastIndexOfEle > -1) {
            categoryName = categoryName.substring(lastIndexOfEle + 1);
        }
        return categoryName;
    }
}
