package com.daddylab.supplier.item.application.drawer.impl.copySupport;

import cn.hutool.core.map.MapBuilder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.drawer.ItemDrawerCopyService;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemImageService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemLaunchPlanService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemProcurementService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @className ItemDrawerBaseCopyServiceImpl
 * <AUTHOR>
 * @date 2024/4/16 13:41
 * @description: 商品基础信息复制
 */
@Service
public class ItemDrawerBaseCopyServiceImpl implements ItemDrawerCopyService {

    @Autowired private IItemLaunchPlanService itemLaunchPlanService;
    @Autowired private NewGoodsBizService newGoodsBizService;
    @Autowired private IItemProcurementService itemProcurementService;
    @Autowired private IItemImageService iItemImageService;
    @Autowired private IItemService itemService;
    @Autowired private OperateLogDomainService operateLogDomainService;

    @Override
    public void handler(ItemDrawer sourceDrawer, ItemDrawer targetDrawer) {
        copyItemStatus(sourceDrawer, targetDrawer);
        // 产品负责人， 法务
        NewGoods sourceNewGoods = newGoodsBizService.getNewGoods(sourceDrawer.getItemId());
        if (Objects.nonNull(sourceNewGoods)) {
            NewGoods targetNewGoods = newGoodsBizService.getNewGoods(targetDrawer.getItemId());
            if (Objects.nonNull(targetNewGoods)) {
                targetNewGoods.setPrincipalId(sourceNewGoods.getPrincipalId());
                targetNewGoods.setLegalId(sourceNewGoods.getLegalId());
                newGoodsBizService.updateById(targetNewGoods);
            }
        }
        // qc负责人
        ItemProcurement itemProcurement =
                itemProcurementService.getByItemId(sourceDrawer.getItemId());
        if (Objects.nonNull(itemProcurement)) {
            ItemProcurement targetItemProcurement =
                    itemProcurementService.getByItemId(targetDrawer.getItemId());
            if (Objects.nonNull(targetItemProcurement)) {
                targetItemProcurement.setQcIds(itemProcurement.getQcIds());
                targetItemProcurement.setBuyerId(itemProcurement.getBuyerId());
                itemProcurementService.updateById(targetItemProcurement);
            }
        }
        // 商品图片信息(图片不合并)
        //        List<ItemImage> targetItemImages = getItemImage(targetDrawer.getItemId());
        //        if (!targetItemImages.isEmpty()) {
        //            List<Long> ids =
        // targetItemImages.stream().map(ItemImage::getId).collect(Collectors.toList());
        //            iItemImageService.removeByIds(ids);
        //        }
        //        List<ItemImage> sourceItemImages = getItemImage(sourceDrawer.getItemId());
        //        if (!sourceItemImages.isEmpty()) {
        //            List<ItemImage> newImageList = sourceItemImages.stream().peek(sourceItemImage
        // -> sourceItemImage.setItemId(targetDrawer.getItemId())).collect(Collectors.toList());
        //            iItemImageService.saveBatch(newImageList);
        //        }
    }

    List<ItemImage> getItemImage(Long itemId) {
        return iItemImageService.list(
                Wrappers.<ItemImage>lambdaQuery().eq(ItemImage::getItemId, itemId));
    }

    public void copyItemStatus(ItemDrawer sourceDrawer, ItemDrawer targetDrawer) {
        // 商品的状态
        Item sourceItem = itemService.getById(sourceDrawer.getItemId());
        if (Objects.nonNull(sourceItem)) {
            Item targetItem = itemService.getById(targetDrawer.getItemId());
            if (Objects.nonNull(targetItem)) {
                operateLogDomainService.addOperatorLog(
                        UserContext.getUserId(),
                        OperateLogTarget.ITEM_STATUS,
                        targetDrawer.getItemId(),
                        String.format(
                                "因商品合并上新，将商品 %s 状态复制到当前商品 %s",
                                sourceItem.getSupplierCode(), targetItem.getSupplierCode()),
                        MapBuilder.create()
                                .put("sourceItem", sourceItem)
                                .put("targetItem", targetItem)
                                .build());
                targetItem.setLaunchStatus(sourceItem.getLaunchStatus());
                targetItem.setAuditStatus(sourceItem.getAuditStatus());
                targetItem.setBeforeLaunchStatus(sourceItem.getBeforeLaunchStatus());
                targetItem.setStatus(sourceItem.getStatus());
                targetItem.setDownFrameTime(sourceItem.getDownFrameTime());
                targetItem.setDownFrameReason(sourceItem.getDownFrameReason());
                itemService.updateById(targetItem);
            }
        }
    }
}
