package com.daddylab.supplier.item.controller.handingsheet.dto;

import com.alibaba.cola.dto.PageQuery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/24 14:04
 * @Description: 盘货表分页查询参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("盘货表分页查询参数")
public class HandingSheetPageQuery extends PageQuery {

    @ApiModelProperty(value = "盘货表名称")
    private String name;

    /**
     * {@link com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.HandingSheetStateEnum}
     */
    @ApiModelProperty(value = "状态（1-待提交，2-待审核，3-已过审，4-进行中，5-已过期，不传默认为全部）")
    private Integer state;

    /**
     * {@link com.daddylab.supplier.item.domain.common.enums.Platform}
     */
    @ApiModelProperty(value = "所属平台（0-其他，1-淘宝，2-有赞，3-抖店，4-快手小店，5-小红书，7-小程序）")
    private Integer platformVal;

    @ApiModelProperty(value = "活动开始时间（时间戳，单位秒）")
    private Long startTime;

    @ApiModelProperty(value = "活动结束时间（时间戳，单位秒）")
    private Long endTime;

    @ApiModelProperty(value = "创建人 uid")
    private Long creatorUid;

    private Long sheetId;
}
