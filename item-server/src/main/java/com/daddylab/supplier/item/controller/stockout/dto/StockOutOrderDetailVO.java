package com.daddylab.supplier.item.controller.stockout.dto;

import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/28 16:17
 * @description StockOutOrderDetailVO
 */
@Data
@ApiModel("出库明细详情")
public class StockOutOrderDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "出库单id")
    private Long stockOutOrderId;

    @ApiModelProperty(value = "商品id")
    private Long itemId;

    @ApiModelProperty(value = "商品skuCode")
    private String itemSkuCode;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "规格")
    private String specifications;

    @ApiModelProperty(value = "库存单位")
    private String stockUnit;

    @ApiModelProperty(value = "计价单位")
    private String valuationUnit;

    @ApiModelProperty(value = "计价数量")
    private Integer valuationQuantity;

    @ApiModelProperty(value = "补料数量")
    private Integer replenishQuantity;

    @ApiModelProperty(value = "扣款数量")
    private Integer deductionQuantity;

    @ApiModelProperty(value = "出库数量")
    private Integer returnQuantity;

    @ApiModelProperty(value = "实际出库数量")
    private Integer realReturnQuantity;

    @ApiModelProperty(value = "仓库编号")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "退料原因")
    private String returnReason;

    @ApiModelProperty(value = "是否是赠品,0不是。1是")
    private Integer isGift;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxQuota;

    @ApiModelProperty(value = "价税合计")
    private BigDecimal totalPriceTax;

    @ApiModelProperty(value = "税后单价")
    private BigDecimal afterTaxPrice;

    @ApiModelProperty(value = "税后金额")
    private BigDecimal afterTaxAmount;

    @ApiModelProperty("合作方+业务类型")
    private List<CorpBizTypeDTO> corpBizType;



}
