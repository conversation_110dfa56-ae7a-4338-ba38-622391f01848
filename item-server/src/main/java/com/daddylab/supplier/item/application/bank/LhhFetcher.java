package com.daddylab.supplier.item.application.bank;

import ch.qos.logback.classic.Level;

import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.apache.hc.client5.http.fluent.Content;
import org.apache.hc.client5.http.fluent.Request;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.HttpHeaders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2023/11/21
 */
@Slf4j
public class LhhFetcher {

    public static void main(String[] args) {
        final Logger logger = LoggerFactory.getLogger("org.apache.hc.client5.http");
        ((ch.qos.logback.classic.Logger) logger).setLevel(Level.INFO);
        final LhhFetcher lhhFetcher = new LhhFetcher();
        lhhFetcher.fetch(
                10000,
                page -> {
                    System.out.printf(
                            "第%s页数据 当前页数量:%s 总数:%s 总页数:%s\n",
                            page.getNumber(),
                            page.getNumberOfElements(),
                            page.getTotalElements(),
                            page.getTotalPages());
                });
    }

    public void fetch(int pageSize, Consumer<Page<LhhDataModel>> consumer) {
        Pageable pageRequest = PageRequest.of(0, pageSize);
        while (true) {
            final Page<LhhDataModel> page = query(pageRequest);
            if (page.getContent().isEmpty()) {
                break;
            }
            consumer.accept(page);
            if (!page.hasNext()) {
                break;
            }
            pageRequest = pageRequest.next();
        }
    }

    @SneakyThrows
    private Page<LhhDataModel> query(Pageable pageRequest) {
        final HashMap<String, Object> body = new HashMap<>();
        body.put("current", pageRequest.getPageNumber() + 1);
        body.put("size", pageRequest.getPageSize());
        Content content =
                Request.post("https://www.lianhanghao.com/api/bank/lhhTableData")
                        .addHeader(
                                HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.toString())
                        .addHeader("Accept-Encoding", "gzip")
                        .bodyString(JsonUtil.toJson(body), ContentType.APPLICATION_JSON)
                        .execute()
                        .returnContent();
        final TypeReference<LhhResponse<LhhResponseData>> typeReference =
                new TypeReference<LhhResponse<LhhResponseData>>() {};
        final LhhResponse<LhhResponseData> responseData =
                JsonUtil.parse(content.toString(), typeReference.getType());
        Assert.notNull(responseData, "接口响应数据为空");
        return new PageImpl<>(
                responseData.getData().getData(),
                pageRequest,
                responseData.getData().getTotalNum());
    }
}
