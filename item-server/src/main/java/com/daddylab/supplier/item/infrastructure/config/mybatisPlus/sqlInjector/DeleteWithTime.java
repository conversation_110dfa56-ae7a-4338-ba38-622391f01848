package com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector;

import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/25 2:18 下午
 * @description
 */
public class DeleteWithTime extends AbstractMethod {

    private static final String METHOD = "deleteWithTime";

    public DeleteWithTime() {
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        SqlMethod sqlMethod = SqlMethod.LOGIC_DELETE;
        String sql;
        SqlSource sqlSource;
        if (tableInfo.isWithLogicDelete()) {
            sql = String.format(sqlMethod.getSql(), tableInfo.getTableName(), this.sqlLogicSet(tableInfo) + ",deleted_at = unix_timestamp()", this.sqlWhereEntityWrapper(true, tableInfo), this.sqlComment());
            sqlSource = this.languageDriver.createSqlSource(this.configuration, sql, modelClass);
            return this.addUpdateMappedStatement(mapperClass, modelClass, METHOD, sqlSource);
        }
        return null;
//        else {
//            sqlMethod = SqlMethod.DELETE;
//            sql = String.format(sqlMethod.getSql(), tableInfo.getTableName(), this.sqlWhereEntityWrapper(true, tableInfo), this.sqlComment());
//            sqlSource = this.languageDriver.createSqlSource(this.configuration, sql, modelClass);
//            return this.addDeleteMappedStatement(mapperClass, this.getMethod(sqlMethod), sqlSource);
//        }
    }
}
