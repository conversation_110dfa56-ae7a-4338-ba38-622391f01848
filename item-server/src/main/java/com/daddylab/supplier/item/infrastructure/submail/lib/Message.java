package com.daddylab.supplier.item.infrastructure.submail.lib;


import com.daddylab.supplier.item.infrastructure.submail.config.AppConfig;
import com.daddylab.supplier.item.infrastructure.submail.lib.base.Sender;

import java.util.Map;

/**
 * 短信api，定义发送HTTP请求消息模式。
 *
 * @Auther: <EMAIL>
 * @version: 1.0.0
 * @Date: 2021/04/16/4:01 下午
 */
public class Message extends Sender {

	private static final String API_SEND = "https://api-v4.mysubmail.com/sms/send";
	private static final String API_XSEND = "https://api-v4.mysubmail.com/sms/xsend";


	public Message(AppConfig config) {
		this.config = config;
	}

	/**
	 * 发送请求数据到服务器,数据由两部分组成,其中一个是原始数据，另一个是签名
	 */
	@Override
	public String send(Map<String, Object> data) {
		return request(API_SEND, data);
	}

	@Override
	public String xsend(Map<String, Object> data) {
		return request(API_XSEND, data);
	}

}
