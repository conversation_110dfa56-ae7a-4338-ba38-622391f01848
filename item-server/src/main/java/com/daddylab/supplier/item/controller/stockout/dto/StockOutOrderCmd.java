package com.daddylab.supplier.item.controller.stockout.dto;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.Command;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/25 11:50
 * @description StockOutOrderCmd
 */
@ApiModel(value = "新增出库单")
@Data
@EqualsAndHashCode(callSuper = true)
public class StockOutOrderCmd extends Command {

    @ApiModelProperty(value = "出库单id")
    private Long id;

    /**
     * 采购单id
     */
    @ApiModelProperty(value = "采购单id")
    private Long purchaseOrderId;

    /**
     * 采购单号
     */
    @ApiModelProperty(value = "采购单号")
    private String purchaseOrderNo;

    /**
     * 供应商id
     */
    @NotNull(message = "供应商不能为空")
    @ApiModelProperty(value = "供应商id")
    private Long providerId;

    /**
     * 采购组织id
     */
    @NotNull(message = "采购组织不能为空")
    @ApiModelProperty(value = "采购组织id")
    private Long purchaseOrganizationId;

    /**
     * 退料组织id
     */
    @NotNull(message = "退料组织不能为空")
    @ApiModelProperty(value = "退料组织id")
    private Long organizationId;

    /**
     * 退料类型(1:库存退料)
     */
    @NotNull(message = "退料类型不能为空")
    @ApiModelProperty(value = "退料类型(1:库存退料)")
    private Integer returnType;

    /**
     * 退料方式(1.退料补料。2.退料并扣款)
     */
    @NotNull(message = "退料方式不能为空")
    @ApiModelProperty(value = "退料方式(1.退料补料。2.退料并扣款)")
    private Integer returnMode;

    /**
     * 出库状态:1待提交、2待审核、3待出库、4已出库、5已取消、6审核拒绝、7撤回审核
     */
    @NotNull(message = "出库状态不能为空")
    @ApiModelProperty(value = "出库状态:1待提交、2待审核、3待出库、4已出库、5已取消、6审核拒绝、7撤回审核")
    private Integer state;

    /**
     * 备注
     */
    @Length(max = 200, message = "备注限制200字符")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 是否同步网点通.0不同步。1同步
     */
    @NotNull(message = "是否推送旺店通不能为空")
    @ApiModelProperty(value = "是否同步网点通.0不同步。1同步")
    private Integer isSyncWdt;

    /**
     * 出库明细
     */
    @ApiModelProperty(value = "出库明细")
    @Valid
    private List<StockOutOrderDetailCmd> stockOutOrderDetailList;

    private Long approvalAt;

    /**
     * 当此数据为冲销单据请求参数时。
     * 原入库单编码
     */
    private String reverseOldNo;
    private Long reverseOldId;

    @NotNull(message = "合作模式不得为空")
    @ApiModelProperty(value = "合作模式")
    private Integer businessLine;

    @ApiModelProperty("出库时间")
    private Long outboundTime;


    public static StockOutOrderCmd of(PurchaseOrder purchaseOrder, List<PurchaseOrderDetail> detailList) {
        StockOutOrderCmd cmd = new StockOutOrderCmd();
        cmd.setPurchaseOrderId(purchaseOrder.getId());
        cmd.setPurchaseOrderNo(purchaseOrder.getNo());
        cmd.setProviderId(purchaseOrder.getProviderId());
        cmd.setPurchaseOrganizationId(purchaseOrder.getOrganizationId());
        cmd.setOrganizationId(purchaseOrder.getOrganizationId());
        cmd.setReturnType(1);
        cmd.setReturnMode(2);
        cmd.setState(4);
        cmd.setRemark("系统采购单");
        cmd.setIsSyncWdt(0);
        cmd.setApprovalAt(DateUtil.currentTime());
        cmd.setBusinessLine(purchaseOrder.getBusinessLine());

        List<StockOutOrderDetailCmd> list = new LinkedList<>();
        for (PurchaseOrderDetail purchaseOrderDetail : detailList) {
            StockOutOrderDetailCmd cmd1 = new StockOutOrderDetailCmd();
            cmd1.setItemId(purchaseOrderDetail.getItemId());
            cmd1.setItemSkuCode(purchaseOrderDetail.getItemSkuCode());
            IItemService itemService = SpringUtil.getBean(IItemService.class);
            Item item = itemService.getById(purchaseOrderDetail.getItemId());
            cmd1.setItemName(item.getName());
            cmd1.setSpecifications(purchaseOrderDetail.getSpecifications());
            cmd1.setStockUnit(purchaseOrderDetail.getUnit());
            cmd1.setValuationUnit(purchaseOrderDetail.getUnit());
            cmd1.setReturnQuantity(Math.abs(purchaseOrderDetail.getPurchaseQuantity()));
            cmd1.setRealReturnQuantity(Math.abs(purchaseOrderDetail.getPurchaseQuantity()));
            cmd1.setWarehouseNo(purchaseOrderDetail.getWarehouseNo());
            cmd1.setReturnReason("系统采购单");
            cmd1.setIsGift(purchaseOrderDetail.getIsGift());
            cmd1.setRemark("系统采购单");
            cmd1.setTaxPrice(purchaseOrderDetail.getTaxPrice());
            cmd1.setTaxRate(purchaseOrderDetail.getTaxRate());
            list.add(cmd1);
        }

        cmd.setStockOutOrderDetailList(list);

        return cmd;
    }

    public static StockOutOrderCmd ofByStockInOrder(PurchaseOrder purchaseOrder, Map<String, Long> itemCodeAndIdMap
            , List<StockInOrderDetail> stockInOrderDetailList, String remark) {

        StockOutOrderCmd cmd = new StockOutOrderCmd();
        cmd.setPurchaseOrderId(purchaseOrder.getId());
        cmd.setPurchaseOrderNo(purchaseOrder.getNo());
        cmd.setProviderId(purchaseOrder.getProviderId());
        cmd.setPurchaseOrganizationId(purchaseOrder.getOrganizationId());
        cmd.setOrganizationId(purchaseOrder.getOrganizationId());
        cmd.setReturnType(1);
        cmd.setReturnMode(2);
        cmd.setState(1);
        cmd.setRemark(remark);
        cmd.setIsSyncWdt(0);
        cmd.setApprovalAt(DateUtil.currentTime());

        List<StockOutOrderDetailCmd> list = new LinkedList<>();
        for (StockInOrderDetail stockInOrderDetail : stockInOrderDetailList) {
            StockOutOrderDetailCmd cmd1 = new StockOutOrderDetailCmd();
            cmd1.setItemId(itemCodeAndIdMap.getOrDefault(stockInOrderDetail.getItemSkuCode(), 0L));
            cmd1.setItemSkuCode(stockInOrderDetail.getItemSkuCode());
            cmd1.setItemName(stockInOrderDetail.getItemName());
            cmd1.setSpecifications(stockInOrderDetail.getSpecifications());
            cmd1.setStockUnit(stockInOrderDetail.getUnit());
            cmd1.setValuationUnit(stockInOrderDetail.getUnit());
            cmd1.setReturnQuantity(stockInOrderDetail.getRealReceiptQuantity());
            cmd1.setWarehouseNo(stockInOrderDetail.getWarehouseNo());
            cmd1.setReturnReason("系统采购单");
            cmd1.setIsGift(stockInOrderDetail.getIsGift());
            cmd1.setRemark("系统采购单");
            cmd1.setTaxPrice(stockInOrderDetail.getTaxPrice());
            cmd1.setTaxRate(stockInOrderDetail.getTaxRate());
            cmd1.setRealReturnQuantity(stockInOrderDetail.getRealReceiptQuantity());
            list.add(cmd1);
        }

        cmd.setStockOutOrderDetailList(list);

        return cmd;
    }


}
