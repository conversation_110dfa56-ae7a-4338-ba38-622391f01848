package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> up
 * @date 2025年04月27日 2:19 PM
 */
@AllArgsConstructor
@Getter
public enum PayPurposeEnum implements IEnum<Integer> {

  //    0：采购付款（默认），1：预付款

  PURCHASE_PAYMENT(0, "仅支持采购付款（默认）"),
  PREPAYMENT(1, "仅支持预付款"),
  ALL(9, "支持全部付款方式");

  @EnumValue private final Integer value;
  private final String desc;
}
