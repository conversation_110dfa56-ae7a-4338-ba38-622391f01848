package com.daddylab.supplier.item.application.item;

import io.swagger.annotations.ApiParam;

import java.util.List;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @since 2023/9/4
 */
public interface ItemSyncBanniuBizService {
    void syncBatchByItemIds(@ApiParam("后端商品ID") @NotEmpty List<Long> itemIds);

    void syncOneByItemId(Long itemId);

    void syncAll();

    void syncAll(int limit);
}
