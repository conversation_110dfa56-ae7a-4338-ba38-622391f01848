package com.daddylab.supplier.item.infrastructure.oss;

import cn.hutool.core.util.StrUtil;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;

import lombok.Data;
import lombok.Getter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * <AUTHOR>
 * @since 2023/10/11
 */
@Data
@ConfigurationProperties(prefix = "oss")
@RefreshScope
public class OssConfig {
    private String endpoint;
    private String accessKeyId;
    private String accessKeySecret;
    private String publicBucket;
    private String privateBucket;
    private String publicUrl;

    public void setPublicUrl(String publicUrl) {
        this.publicUrl = StrUtil.addSuffixIfNot(publicUrl, "/");
    }

    private String privateUrl;

    public void setPrivateUrl(String privateUrl) {
        this.privateUrl = StrUtil.addSuffixIfNot(privateUrl, "/");
    }

    private String prefixDir;

    public void setPrefixDir(String prefixDir) {
        this.prefixDir = StrUtil.addSuffixIfNot(StrUtil.addPrefixIfNot(prefixDir, "/"), "/");
    }

    private int defaultSignExpireSeconds = 600;

    @Getter(lazy = true)
    private final OSS ossClient = buildOssClient();

    public OSS buildOssClient() {
        return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
    }
}
