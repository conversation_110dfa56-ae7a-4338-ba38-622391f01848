package com.daddylab.supplier.item.application.itemReference.types;

import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemDelivery;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;
import java.util.Objects;

@Data
public class ItemReferenceSpu {

    @ApiModelProperty("商品ID")
    private Long itemId;

    @ApiModelProperty("商品编码")
    private String itemCode;

    @ApiModelProperty("商品名称")
    private String itemName;

    @ApiModelProperty("品类ID")
    private Long categoryId;

    @ApiModelProperty("品类")
    private String categoryPath;

    @ApiModelProperty("关联款号")
    private String partnerProviderItemSn;

    @ApiModelProperty("SKU数量")
    private Long skuNum;

    @ApiModelProperty("品牌ID")
    private Long brandId;

    @ApiModelProperty("品牌")

    private String brandName;

    @ApiModelProperty("供应商ID")
    private Long providerId;

    @ApiModelProperty("P系统供应商ID")
    private Long partnerProviderId;

    @ApiModelProperty("供应商")
    private String provider;

    @ApiModelProperty("采购员用户ID")
    private Long buyerUid;

    @ApiModelProperty("采购员花名")
    private String buyerNick;

    @ApiModelProperty("采购员真名")
    private String buyerName;

    @ApiModelProperty("发货渠道")
    private String delivery;

    @JsonProperty
    public String getDelivery() {
        if (Objects.nonNull(delivery) && delivery.contains(",")) {
            return ItemDelivery.ALL.getValue().toString();
        }
        return delivery;
    }

    @ApiModelProperty(value = "税率", notes = "销售税率")
    private String taxRate;

    @ApiModelProperty(value = "采购税率")
    private String purchaseRate;

    @ApiModelProperty(value = "税率编码")
    private String taxRateCode;

    @ApiModelProperty("商品状态 0:待上架 1:已上架 2:下架")
    private Long itemStatus;

    @ApiModelProperty("上架日期（时间戳）")
    private Long launchTime;

    @ApiModelProperty("上架日期")
    @JsonProperty
    public String getLaunchDate() {
        if (NumberUtil.isPositive(launchTime)) {
            return DateUtil.formatDate(launchTime);
        }
        return "";
    }

    @ApiModelProperty("仓库库存")
    private Long stock;

    @ApiModelProperty("合作模式（业务线）")
    private Integer businessLine;

    @ApiModelProperty("是否黑名单 0否 1是")
    private Integer isBlacklist;

    @ApiModelProperty("合作方业务类型")
    private List<CorpBizTypeDTO> corpBizType;
}