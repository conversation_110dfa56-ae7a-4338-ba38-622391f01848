package com.daddylab.supplier.item.application.salesOutStock.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2023年10月20日 2:33 PM
 */
@Data
@ApiModel("销售出库单列表返回")
public class SalesOutStockPageVO {

    @ApiModelProperty("出库单id")
    private Long stockoutId;

    @ApiModelProperty("出库单号")
    private String orderNo;

    @ApiModelProperty("仓库")
    private String warehouseName;

    @ApiModelProperty("店铺")
    private String shopName;

    @ApiModelProperty("物流公司")
    private String logisticsName;

    @ApiModelProperty("物流单号")
    private String logisticsNo;

    @ApiModelProperty("发货时间")
    private String consignTime;

    private Integer status;
    @ApiModelProperty("状态")
    private String statusStr;

    @ApiModelProperty("订单编号（旺）")
    private String tradeNo;


//    wssoo.order_no,
//    wssoo.warehouse_name,
//    wssoo.logistics_name,
//    wssoo.logistics_no,
//    wssoo.shop_name,
//    wssoo.consign_time,
//    wssoo.status,
//    wssoo.trade_no,
//    wssood.spec_code,
//    wssood.goods_name
}
