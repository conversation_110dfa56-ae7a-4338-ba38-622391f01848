package com.daddylab.supplier.item.application.afterSaleLink;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSaleLink.dto.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSaleShareLink;

public interface AfterSaleShareLinkBizService {

    SingleResponse<String> saveLink(AfterSaleShareLinkCmd cmd);

    PageResponse<AfterSaleShareLinkVO> pageQuery(AfterSaleShareLinkQuery query);

    SingleResponse<AfterSaleShareLinkViewVO> view(Long id);

    SingleResponse<Boolean> delete(Long id);

    MultiResponse<LinkCreatorDto> linkCreator();

    Response sendCode(Long shareLinkId, String phone);

    SingleResponse<String> token(Long shareLinkId, String phone, String code);

    SingleResponse<AfterSaleShareLink> checkToken(String token);
}
