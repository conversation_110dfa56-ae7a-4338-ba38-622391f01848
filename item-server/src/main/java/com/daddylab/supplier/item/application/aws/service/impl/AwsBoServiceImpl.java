package com.daddylab.supplier.item.application.aws.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.actionsoft.bpms.api.OpenApiClient;
import com.actionsoft.sdk.service.response.StringResponse;
import com.daddylab.supplier.item.application.aws.AwsConstant;
import com.daddylab.supplier.item.application.aws.AwsOpenApiClient;
import com.daddylab.supplier.item.application.aws.service.AwsBoService;
import com.daddylab.supplier.item.application.aws.types.ActOrgExtDeptBO;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/11
 */
@Service
public class AwsBoServiceImpl implements AwsBoService {
    @Resource
    private AwsOpenApiClient awsOpenApiClient;

    @Override
    public ActOrgExtDeptBO getActOrgExtDeptBO(String departmentID) {
        final OpenApiClient openApiClient = awsOpenApiClient.getOpenApiClient();
        final HashMap<String, Object> params = new HashMap<>();
        params.put("boName", "BO_ACT_ORG_EXT_DEPT");
        params.put("querys", new String[][]{
                new String[]{"DEPARTMENTID=", departmentID}
        });
        params.put("firstRow", 0);
        params.put("rowCount", 10);
        final StringResponse response = openApiClient.exec(AwsConstant.BO_QUERY, params, StringResponse.class);
        if (!response.isSuccess()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "从炎黄获取部门管理拓展信息BO异常");
        }
        final List<ActOrgExtDeptBO> actOrgExtDeptBOS = JsonUtil.parseList(response.getData(), ActOrgExtDeptBO.class);
        if (CollectionUtil.isEmpty(actOrgExtDeptBOS)) {
            return null;
        }
        return actOrgExtDeptBOS.get(0);
    }
}
