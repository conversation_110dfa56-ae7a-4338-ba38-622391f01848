package com.daddylab.supplier.item.application.offShelf.dto;

import com.daddylab.supplier.item.types.process.ProcessAdvice;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/10
 */
@Data
public class OffShelfApproveCmd {
    private Long id;
    private ProcessAdvice advice;
    private List<Long> shopIds;
    private Boolean selectAllShop;
    private List<Long> operatorUid;
    private String remark;
}
