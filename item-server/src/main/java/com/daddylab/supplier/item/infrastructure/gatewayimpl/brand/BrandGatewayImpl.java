package com.daddylab.supplier.item.infrastructure.gatewayimpl.brand;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.goods.GoodsBrandAPI;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsBrandSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsBrandSearchResponse;
import com.daddylab.supplier.item.application.auth.BusinessLineFilterHelper;
import com.daddylab.supplier.item.application.provider.ProviderBizService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.EntityChange;
import com.daddylab.supplier.item.common.enums.EnableStatusEnum;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.brand.dto.BrandDropDownItem;
import com.daddylab.supplier.item.domain.brand.dto.BrandDropDownQuery;
import com.daddylab.supplier.item.domain.brand.dto.BrandListItem;
import com.daddylab.supplier.item.domain.brand.dto.BrandQuery;
import com.daddylab.supplier.item.domain.brand.entity.BrandEntity;
import com.daddylab.supplier.item.domain.brand.gateway.BrandGateway;
import com.daddylab.supplier.item.domain.brand.trans.BrandTransMapper;
import com.daddylab.supplier.item.domain.brand.vo.BrandProviderVo;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Brand;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BrandProvider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.BrandMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBrandProviderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBrandService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.utils.LogicDeleteUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.google.common.collect.Lists;
import lombok.NonNull;
import org.javers.core.diff.Diff;
import org.javers.core.diff.changetype.container.CollectionChange;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.inject.Inject;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class BrandGatewayImpl implements BrandGateway {
    @Autowired
    IBrandService brandService;
    @Inject
    BrandMapper brandMapper;
    @Inject
    IBrandProviderService brandProviderService;
    @Autowired
    ProviderGateway providerGateway;
    @Autowired
    ProviderBizService providerBizService;
    @Autowired
    WdtGateway wdtGateway;

    @Override
    public Long id(String sn) {
        QueryWrapper<Brand> query = new QueryWrapper<>();
        query.lambda().eq(Brand::getSn, sn);
        final Brand one = brandService.getOne(query);
        return Objects.isNull(one) ? 0L : one.getId();
    }

    @Override
    public boolean existSameName(String name, Long selfId) {
        return brandService.lambdaQuery()
                .eq(Brand::getName, name)
                .ne(Objects.nonNull(selfId), Brand::getId, selfId)
                .last("LIMIT 1").count() > 0;
    }

    @Override
    public boolean existSameName(String name) {
        return existSameName(name, null);
    }

    @Override
    public boolean existSn(String sn) {
        return brandService.lambdaQuery()
                .eq(Brand::getSn, sn)
                .count() > 0;
    }

    @Override
    public boolean existRelateBrandWithProvider(Long providerId) {
        return brandProviderService.lambdaQuery().eq(BrandProvider::getProviderId, providerId).last("LIMIT 1").count() > 0;
    }

    @Override
    public boolean removeRelateBrandForProvider(Long providerId) {
        final LambdaQueryWrapper<BrandProvider> removeQuery = Wrappers.lambdaQuery();
        removeQuery.eq(BrandProvider::getProviderId, providerId);
        return brandProviderService.remove(removeQuery);
    }

    @Override
    public Long existSameNameThenReturnId(String name) {
        return getBrandId(name);
    }

    @Override
    public Brand existSameNameThenReturnPo(String name) {
        QueryWrapper<Brand> query = new QueryWrapper<>();
        query.lambda().eq(Brand::getName, name);
        return brandService.getOne(query);
    }

    @Override
    public Map<String, Brand> batchQueryBrandByName(List<String> names) {
        QueryWrapper<Brand> query = new QueryWrapper<>();
        query.lambda().in(Brand::getName, names);
        return brandService.list(query).stream().collect(Collectors.toMap(Brand::getName, Function.identity()));
    }

    @Override
    public Map<Long, Brand> batchQueryBrandById(Collection<Long> ids) {
        QueryWrapper<Brand> query = new QueryWrapper<>();
        query.lambda().in(Brand::getId, ids);
        return brandService.list(query).stream().collect(Collectors.toMap(Brand::getId, Function.identity()));
    }

    @Override
    public Map<String, Brand> batchQueryBrandByCode(List<String> codes) {
        QueryWrapper<Brand> query = new QueryWrapper<>();
        query.lambda().in(Brand::getSn, codes);
        return brandService.list(query).stream().collect(Collectors.toMap(Brand::getName, Function.identity()));
    }

    @Override
    public boolean updatePo(Brand brand) {
        return brandService.updateById(brand);
    }

    @Override
    @SuppressWarnings("unchecked")
    public boolean updateEntity(EntityChange<BrandEntity> entityChange) {
        if (!entityChange.getDiff().hasChanges()) {
            return true;
        }
        final BrandEntity newEntity = entityChange.getNewEntity();
        final Brand brandPo = BrandTransMapper.INSTANCE.toBrandPo(newEntity);
        brandService.updateById(brandPo);

        final Diff diff = entityChange.getDiff();
        if (!diff.getPropertyChanges("所属供应商").isEmpty()) {
            final CollectionChange providersChange = ((CollectionChange) diff.getPropertyChanges("所属供应商").get(0));

            if (!providersChange.getAddedValues().isEmpty()) {
                final List<Long> addedProviders = (List<Long>) providersChange.getAddedValues();

                final boolean allExist = providerGateway.allExist(addedProviders);
                if (!allExist) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "存在供应商ID无效");
                }

                final List<BrandProvider> brandProviderPos = addedProviders.stream().map(providerId -> {
                    final BrandProvider brandProvider = new BrandProvider();
                    brandProvider.setBrandId(brandPo.getId());
                    brandProvider.setProviderId(providerId);
                    return brandProvider;
                }).collect(Collectors.toList());
                brandProviderService.saveBatch(brandProviderPos);
            }

            if (!providersChange.getRemovedValues().isEmpty()) {
                final List<Long> removedProviders = (List<Long>) providersChange.getRemovedValues();
                final LambdaQueryWrapper<BrandProvider> queryRemovedProvider = Wrappers.lambdaQuery();
                queryRemovedProvider.eq(BrandProvider::getBrandId, newEntity.getId())
                        .in(BrandProvider::getProviderId, removedProviders);
                brandProviderService.remove(queryRemovedProvider);
            }
        }
        return true;
    }

    @Override
    public boolean batchSaveOrUpdateBrandPo(List<Brand> brandPos) {
        return brandService.saveOrUpdateBatch(brandPos);
    }

    @Override
    public boolean createEntity(BrandEntity brand) {
        final Brand brandPo = BrandTransMapper.INSTANCE.toBrandPo(brand);
        brandService.saveOrUpdate(brandPo);
        brand.setId(brandPo.getId());

        final boolean allExist = providerGateway.allExist(brand.getProviderIds());
        if (!allExist) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "存在供应商ID无效");
        }

        final List<BrandProvider> brandProviderPos = brand.getProviderIds().stream()
                .map(providerId -> new BrandProvider().setProviderId(providerId).setBrandId(brandPo.getId()))
                .collect(Collectors.toList());
        if (!brandProviderPos.isEmpty()) {
            brandProviderService.saveBatch(brandProviderPos);
        }
        return true;
    }

    @Override
    public Long getBrandId(String name) {
        QueryWrapper<Brand> query = new QueryWrapper<>();
        query.lambda().eq(Brand::getName, name);
        final Brand one = brandService.getOne(query);
        return Objects.isNull(one) ? 0L : one.getId();
    }

    @Override
    public Long syncWdtBrand(String name) {
        try {
            final GoodsBrandAPI api = wdtGateway.getAPI(GoodsBrandAPI.class);
            final Pager pager = new Pager(10, 0, false);
            final GoodsBrandSearchParams params = new GoodsBrandSearchParams();
            params.setBrandName(name);

            final GoodsBrandSearchResponse searchResponse = api.search(params, pager);
            if (CollectionUtil.isEmpty(searchResponse.getDetailList())) {
                return 0L;
            }
            final ArrayList<Brand> brands = new ArrayList<>();
            for (GoodsBrandSearchResponse.Detail detail : searchResponse.getDetailList()) {
                final EnableStatusEnum status =
                        detail.getIsDisabled() != null && detail.getIsDisabled() == 0
                                ? EnableStatusEnum.ON : EnableStatusEnum.OFF;
                final Brand brand = new Brand();
                brand.setSn(detail.getBrandNo());
                brand.setName(detail.getBrandName());
                brand.setLogo("");
                brand.setStatus(status);
                brands.add(brand);
            }
            brandService.saveBatch(brands);
            return brands.stream().map(Entity::getId).findFirst().orElse(0L);
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapBizException(e);
        }
    }

    /**
     * @implNote 使用该方法请不要使用分页参数，这个查询使用分页参数的情况下无法准确保证返回的记录数量
     */
    @Override
    public List<BrandListItem> queryBrandList(BrandQuery brandQuery) {
        return brandMapper.queryBrandList(brandQuery);
    }

    @Override
    public Page<BrandListItem> pageQueryBrandList(BrandQuery brandQuery) {
        final Page<Brand> page = new Page<>(brandQuery.getPageIndex(), brandQuery.getPageSize());
        final Page<BrandListItem> emptyPage = new Page<BrandListItem>(page.getCurrent(), page.getSize(), page.getTotal()).setRecords(Collections.emptyList());
        final QueryWrapper<Brand> queryWrapper = Wrappers.query();
        final LambdaQueryWrapper<Brand> query = queryWrapper.lambda();

        BusinessLineFilterHelper.setBusinessLinesFilter(queryWrapper, brandQuery.getBusinessLine());

        //针对品牌ID精确查询
        if (NumberUtil.isPositive(brandQuery.getId())) {
            query.eq(Brand::getId, brandQuery.getId());
        }

        //根据供应商来过滤
        if (NumberUtil.isPositive(brandQuery.getProviderId())) {
            //查询所有跟这个供应商关联的品牌ID
            List<Long> relateProviderBrandIds = getRelateProviderBrandIds(brandQuery.getProviderId());

            //如果这个供应商没有关联品牌，直接返回空集
            if (relateProviderBrandIds.isEmpty()) {
                return emptyPage;
            }

            //如果供应商关联品牌ID中不包含选中的品牌ID，直接返回空
            if (NumberUtil.isPositive(brandQuery.getId()) && !relateProviderBrandIds.contains(brandQuery.getId())) {
                return emptyPage;
            }
            query.in(Brand::getId, relateProviderBrandIds);
        }

        //品牌名称
        query.like(Objects.nonNull(brandQuery.getName()), Brand::getName, brandQuery.getName());

        //品牌状态
        query.eq(Objects.nonNull(brandQuery.getStatus()), Brand::getStatus, brandQuery.getStatus());

        //品牌编号
        query.eq(Objects.nonNull(brandQuery.getSn()), Brand::getSn, brandQuery.getSn());

        //根据ID倒序排序
        query.orderByDesc(Brand::getId);

        //查询分页数据
        brandService.page(page, query);

        //转化为列表数据
        final List<BrandListItem> brandListItems = toBrandListItems(page.getRecords());
        return new Page<BrandListItem>(page.getCurrent(), page.getSize(), page.getTotal()).setRecords(brandListItems);
    }

    private List<BrandListItem> toBrandListItems(List<Brand> brands) {
        final List<Long> brandIds = brands.stream().map(Brand::getId).collect(Collectors.toList());
        final List<BrandProvider> brandProviders = getBrandProvidersByBrandIds(brandIds);
        final List<Long> providerIds = brandProviders.stream().map(BrandProvider::getProviderId).distinct().collect(Collectors.toList());
        final List<Provider> providers = providerBizService.batchQueryProvider(providerIds);
        final Map<Long, Provider> providerMap = providers.stream().collect(Collectors.toMap(Entity::getId, Function.identity()));
        final Map<Long, List<BrandProvider>> brandProvidersGroup = brandProviders.stream().collect(Collectors.groupingBy(BrandProvider::getBrandId));

        final ArrayList<BrandListItem> results = Lists.newArrayListWithCapacity(brands.size());
        for (Brand brand : brands) {
            final List<BrandProvider> brandProvidersThisBrand = brandProvidersGroup.get(brand.getId());
            final BrandListItem brandListItem =
                    BrandTransMapper.INSTANCE.toBrandListItem(
                            brand,
                            brandProvidersThisBrand != null
                                    ? brandProvidersThisBrand.stream()
                                    .map(
                                            brandProvider -> {
                                                final BrandProviderVo brandProviderVo =
                                                        new BrandProviderVo();
                                                brandProviderVo.setProviderId(
                                                        brandProvider.getProviderId());
                                                final Optional<Provider> provider = Optional.ofNullable(
                                                        providerMap.get(
                                                                brandProvider
                                                                        .getProviderId()));

                                                brandProviderVo.setProviderName(provider.map(Provider::getName).orElse(""));
                                                brandProviderVo.setPartnerProviderId(provider.map(Provider::getPartnerProviderId).orElse(0L));
                                                return brandProviderVo;
                                            })
                                    .collect(Collectors.toList())
                                    : Collections.emptyList());
            results.add(brandListItem);
        }
        return results;
    }

    private List<BrandProvider> getBrandProvidersByBrandIds(List<Long> brandIds) {
        if (CollUtil.isEmpty(brandIds)) return Collections.emptyList();
        return brandProviderService.lambdaQuery()
                .in(BrandProvider::getBrandId, brandIds)
                .select(BrandProvider::getProviderId, BrandProvider::getBrandId)
                .list();
    }

    @NonNull
    private List<Long> getRelateProviderBrandIds(Long providerId) {
        return brandProviderService.lambdaQuery()
                .eq(BrandProvider::getProviderId, providerId)
                .select(BrandProvider::getBrandId)
                .groupBy(BrandProvider::getBrandId)
                .list()
                .stream().map(BrandProvider::getBrandId).collect(Collectors.toList());
    }

    @Override
    public BrandEntity getBrand(Long id) {
        if (Objects.isNull(id)) return null;
        final Brand brand = brandService.getById(id);
        if (Objects.isNull(brand)) return null;
        final List<Long> providerIds = brandProviderService.lambdaQuery()
                .select(BrandProvider::getProviderId).eq(BrandProvider::getBrandId, id).list()
                .stream().map(BrandProvider::getProviderId).collect(Collectors.toList());
        return BrandTransMapper.INSTANCE.toBrandEntity(brand, providerIds);
    }

    @Override
    public void updateBrandStatus(Long id, EnableStatusEnum status) {
        brandService.lambdaUpdate().set(Brand::getStatus, status).eq(Brand::getId, id).update();
    }

    @Override
    public void deleteBrand(Long id) {
        final Brand brand = brandService.getById(id);
        if (Objects.isNull(brand)) return;

        LambdaUpdateWrapper<Brand> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Brand::getIsDel, 1).set(Brand::getName, LogicDeleteUtil.appendLogicDeleteSuffix(brand.getName()))
                .set(Brand::getSn, LogicDeleteUtil.appendLogicDeleteSuffix(brand.getSn()));
        updateWrapper.eq(Brand::getId, brand.getId());
        brandService.update(updateWrapper);

//        brand.setIsDel(1);
//        brand.setName(LogicDeleteUtil.appendLogicDeleteSuffix(brand.getName()));
//        brand.setSn(LogicDeleteUtil.appendLogicDeleteSuffix(brand.getSn()));
//        brandMapper.updateById(brand);

        final LambdaQueryWrapper<BrandProvider> removeBrandProviderQuery = Wrappers.lambdaQuery();
        brandProviderService.remove(removeBrandProviderQuery.eq(BrandProvider::getBrandId, id));

    }

    @Override
    public List<BrandDropDownItem> dropDownList(BrandDropDownQuery query) {
        int offset =
                ((0 == query.getPageIndex() ? 1 : query.getPageIndex()) - 1) * query.getPageSize();
        return brandMapper.dropDownList(
                query.getId(),
                query.getName(),
                offset,
                query.getPageSize(),
                query.getBusinessLine());
    }

    @Override
    public int getNextSerialNum() {
        final long epochSecond = LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toEpochSecond();
        return brandService.lambdaQuery().gt(Brand::getCreatedAt, epochSecond).count() + 1;
    }

    @Override
    public Brand getBrandById(Long id) {
        return brandService.getById(id);
    }

    @Override
    public Map<Long, String> names(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        QueryWrapper<Brand> query = new QueryWrapper<>();
        query.lambda().in(Brand::getId, ids).select(Brand::getId, Brand::getName);
        return brandService.list(query).stream()
                .collect(Collectors.toMap(Brand::getId, Brand::getName));
    }
}
