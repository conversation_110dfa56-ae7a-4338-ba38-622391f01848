package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtGoodsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtGoodsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-24
 */
@Service
public class WdtGoodsServiceImpl extends DaddyServiceImpl<WdtGoodsMapper, WdtGoods> implements IWdtGoodsService {

}
