package com.daddylab.supplier.item.infrastructure.timing;

import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/12/7
 */
@Aspect
@Slf4j
@Component
public class TimingAspect {

    @Pointcut("@annotation(annotation)")
    public void timingPointCut(TimingWatch annotation) {
    }

    @Around(value = "timingPointCut(annotation)", argNames = "pjp,annotation")
    public Object around(ProceedingJoinPoint pjp, TimingWatch annotation) throws Throwable {
        StopWatch stopWatch = getStopWatch(pjp, annotation);
        stopWatch.start();
        try {
            return pjp.proceed(pjp.getArgs());
        } catch (InvocationTargetException e) {
            throw e.getCause();
        } finally {
            stopWatch.stopThenOutput();
        }
    }

    @NonNull
    private StopWatch getStopWatch(ProceedingJoinPoint pjp, TimingWatch annotation) {
        final Method method = ((MethodSignature) pjp.getSignature()).getMethod();
        String args = Arrays.stream(pjp.getArgs()).map(Object::toString).collect(Collectors.joining(","));
        final int argsMaxLen = 100;
        if (args.length() > argsMaxLen) {
            args = args.substring(0, argsMaxLen) + "...";
        }
        String name = StringUtil.format("{}({})", method.getName(), args);
        return new StopWatch(name, ofOptions(annotation));
    }

    private Options ofOptions(TimingWatch annotation) {
        return new Options()
                .setName(annotation.name())
                .setUnit(annotation.unit())
                .setOutput(annotation.output())
                .setLevel(annotation.level());
    }
}
