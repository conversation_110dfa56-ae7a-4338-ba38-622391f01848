package com.daddylab.supplier.item.application.itemSync.types;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Positive;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/9/20
 */
@Data
public class ItemSyncTbParam {

    @NotBlank(message = "淘宝标题不能为空")
    @ApiModelProperty("淘宝标题")
    private String tbTitle;

    @NotBlank(message = "商品编号不能为空")
    @ApiModelProperty("商品编号")
    private String itemCode;

    @Positive(message = "商品ID不能为空")
    @ApiModelProperty("商品ID")
    private Long itemId;

    @Positive(message = "SKU数量不合法")
    @ApiModelProperty("SKU数量")
    private Integer skuNum;

    @NotBlank(message = "淘宝链接不能为空")
    @ApiModelProperty("淘宝链接")
    private String link;
}
