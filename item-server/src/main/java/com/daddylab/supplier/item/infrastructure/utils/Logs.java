package com.daddylab.supplier.item.infrastructure.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.event.Level;

import java.time.Duration;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/2/18
 */
public class Logs {

  public static final int DEFAULT_VAR_MAX_LEN = 220;

  private static final LoadingCache<String, Logger> logs = Caffeine.newBuilder()
                                                                   .expireAfterAccess(Duration.ofSeconds(60))
                                                                   .build(LoggerFactory::getLogger);

  public static class LogVar {
    @Getter private final String name;
    private final Object value;
    @Getter private Integer maxLen;
    @Getter private boolean toJson;

    public LogVar(String name, Object value, Integer maxLen) {
      this(name, value, maxLen, false);
    }

    public LogVar(String name, Object value, Integer maxLen, boolean toJson) {
      this.name = name == null ? "" : name;
      this.value = value;
      this.maxLen = maxLen == null || maxLen <= 0 ? DEFAULT_VAR_MAX_LEN : maxLen;
      this.toJson = toJson;
    }

    public LogVar maxLen(int maxLen) {
      this.maxLen = maxLen;
      return this;
    }

    public LogVar asJson() {
      return asJson(true);
    }

    public LogVar asJson(boolean asJson) {
      toJson = asJson;
      return this;
    }

    public String getValue() {
      if (value == null) {
        return null;
      }
      String varStr = toJson ? JSON.toJSONString(value) : value.toString();
      boolean isColl = false;
      int size = 0;
      if (value instanceof Collection) {
        isColl = true;
        size = ((Collection<?>) value).size();
      }
      if (varStr.length() > maxLen) {
        varStr = StrUtil.subWithLength(varStr,
                0,
                maxLen) + "..." + (isColl ? " Total of " + size : "");
      }
      return varStr;
    }

    @Override
    public String toString() {
      if (!name.isEmpty()) {
        return name + "=" + getValue();
      } else {
        return getValue();
      }
    }
  }

  public static String val(Object value) {
    return val(value, false);
  }

  public static String val(Object value, boolean toJson) {
    return val(value, DEFAULT_VAR_MAX_LEN, toJson);
  }

  public static String val(Object value, Integer maxLen, boolean toJson) {
    return new LogVar(null, value, maxLen, toJson).getValue();
  }

  public static LogVar var(String name, Object value) {
    return var(name, value, false);
  }

  public static LogVar var(String name, Object value, boolean toJson) {
    return var(name, value, DEFAULT_VAR_MAX_LEN, toJson);
  }

  public static LogVar var(String name, Object value, Integer maxLen, boolean toJson) {
    return new LogVar(name, value, maxLen, toJson);
  }

  public static Object[] vars(Object[] args) {
    return Arrays.stream(args)
        .map(
            arg -> {
              if (!(arg instanceof LogVar) && !(arg instanceof Throwable)) {
                return val(arg);
              }
              return arg;
            })
        .toArray();
  }

  public static void log(Class<?> loggerClazz, Level level, String format, Object... vars) {
    log(logs.get(loggerClazz.getName()), level, format, vars);
  }

  public static void log(String logger, Level level, String format, Object... vars) {
    log(logs.get(logger), level, format, vars);
  }

  public static void log(Logger logger, Level level, String format, Object... vars) {
    final List<LogVar> logVars =
        Arrays.stream(vars)
            .filter(var -> var instanceof LogVar)
            .map(var -> (LogVar) var)
            .collect(Collectors.toList());
    if (!logVars.isEmpty()) {
      final StringBuilder sb = new StringBuilder(" ");
      for (LogVar logVar : logVars) {
        if (!StrUtil.isEmpty(logVar.getName())) {
          sb.append(logVar.getName()).append("=").append("{}").append(" ");
        } else {
          sb.append("{}").append(" ");
        }
      }
      format += sb;
      vars =
          Arrays.stream(vars)
              .map(
                  var -> {
                    if (var instanceof LogVar) {
                      return ((LogVar) var).getValue();
                    }
                    return var;
                  })
              .toArray();
    }
    switch (level) {
      case TRACE:
        logger.trace(format, vars);
        break;
      case DEBUG:
        logger.debug(format, vars);
        break;
      case INFO:
        logger.info(format, vars);
        break;
      case WARN:
        logger.warn(format, vars);
        break;
      case ERROR:
        logger.error(format, vars);
        break;
      default:
        throw new IllegalArgumentException("invalid level");
    }
  }
}
