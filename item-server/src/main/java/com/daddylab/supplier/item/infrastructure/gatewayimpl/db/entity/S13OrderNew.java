package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 小红书订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class S13OrderNew implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 包裹号
     */
    private String packageId;

    /**
     * 母单号
     */
    private String orderId;

    /**
     * 包裹状态
     */
    private String packageStatus;

    /**
     * 售后状态
     */
    private String packageAfterSalesStatus;

    /**
     * 申请取消状态
     */
    private String cancelStatus;

    /**
     * 创建时间
     */
    private String createdTime;

    /**
     * 支付时间
     */
    private String paidTime;

    /**
     * 包裹发货时间
     */
    private String deliveryTime;

    /**
     * 包裹取消时间
     */
    private String cancelTime;

    /**
     * 包裹完成时间
     */
    private String finishTime;

    /**
     * 承诺最晚发货时间
     */
    private String promiseLastDeliveryTime;

    /**
     * 物流方案id
     */
    private String planInfoId;

    /**
     * 物流方案名称
     */
    private String planInfoName;

    /**
     * 收件人省份
     */
    private String receiverProvinceName;

    /**
     * 收件人城市
     */
    private String receiverCityName;

    /**
     * 收件人区县名称
     */
    private String receiverDistrictName;

    /**
     * 用户备注
     */
    private String customerRemark;

    /**
     * 商家标记备注
     */
    private String sellerRemark;

    /**
     * 预售最早发货时间
     */
    private String presaleDeliveryStartTime;

    /**
     * 预售最晚发货时间
     */
    private String presaleDeliveryEndTime;

    /**
     * 原始关联包裹号(退换包裹的原包裹)
     */
    private String originalPackageId;

    /**
     * 订单商品总净重 单位g
     */
    private String totalNetWeightAmount;

    /**
     * 订单支付金额(包含运费)
     */
    private BigDecimal totalPayAmount;

    /**
     * 运费金额
     */
    private BigDecimal totalShippingFree;

    /**
     * 快递单号
     */
    private String expressTrackingNo;

    /**
     * 快递公司编码
     */
    private String expressCompanyCode;

    /**
     * 收件人姓名
     */
    private String receiverName;

    /**
     * 收件人电话
     */
    private String receiverPhone;

    /**
     * 收件人地址
     */
    private String receiverAddress;

    /**
     * 三方保税节点
     */
    private String boundExtendInfo;

    /**
     * 小包转运节点
     */
    private String transferExtendInfo;

    /**
     * 收件人姓名+手机+地址等计算得出，用来查询收件人详情
     */
    private String openAddressId;

    /**
     * 拆包信息节点
     */
    private String simpleDeliveryPackageList;

    /**
     * 物流模式
     */
    private String logistics;

    private LocalDateTime importTime;


}
