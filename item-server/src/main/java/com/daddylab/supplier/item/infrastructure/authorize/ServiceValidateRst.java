package com.daddylab.supplier.item.infrastructure.authorize;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public class ServiceValidateRst {
    @JsonProperty("id")
    Long userId;
    @JsonProperty("sessionid")
    String sessionId;
    @JsonProperty("staffid")
    String staffId;
}
