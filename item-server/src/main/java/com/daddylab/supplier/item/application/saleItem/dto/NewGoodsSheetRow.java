package com.daddylab.supplier.item.application.saleItem.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @ClassName NewGoodsSheet.java
 * @description
 * @createTime 2022年04月20日 17:38:00
 */
@Data
@HeadRowHeight(20)
@ContentRowHeight(16)
@EqualsAndHashCode(of = {"skuCode"})
public class NewGoodsSheetRow implements Serializable {
    private static final long serialVersionUID = -752114031599220390L;

    @ExcelProperty(value = "商品SKU")
    @ColumnWidth(50)
    private String skuCode;

    @ExcelProperty(value = "产品负责人")
    @ColumnWidth(50)
    private String principal;

    @ExcelProperty(value = "上架日期")
    @ColumnWidth(50)
    private String launchDate;

    @ExcelProperty(value = "名称")
    @ColumnWidth(50)
    private String name;

    @ExcelProperty(value = "采购员")
    @ColumnWidth(50)
    private String buyer;
}
