package com.daddylab.supplier.item.controller.test.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2024年02月20日 5:24 PM
 */
@Data
public class SettlementStatisticDto {

    @ExcelProperty("结算周期")
    private String date;

    @ExcelProperty("订单员")
    private String name;

    @ExcelProperty("仓库数量")
    private Integer warehouseCount;

    @ExcelProperty("结算单数量")
    private Integer orderCount;

    @ExcelProperty("结算单修改数量")
    private Integer manualOrderCount;

    @ExcelProperty("差异总数量")
    private Integer totalDiffCount;

    @ExcelProperty("总差异金额")
    private BigDecimal totalDiffAmount;

//    private BigDecimal  unitPriceTotal;


}
