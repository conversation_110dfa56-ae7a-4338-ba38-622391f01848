package com.daddylab.supplier.item.infrastructure.third.consts;

/**
 * <AUTHOR>
 * @class ThirdRedisConstants.java
 * @description 描述类的作用
 * @date 2024-02-27 17:42
 */
public interface ThirdRedisConstants {

    /**
     * redis key
     */
    public static String THIRD_ACCESS_TOKEN_KEY = "erp:thirdAccessTokenKey:%s_%s";

    public static String KS_YD_TOKEN_FRESH_LIMIT_KEY = "ms-supplier-item:ks_rate_limiter";

    public static String RED_BOOK_YD_TOKEN_FRESH_LIMIT_KEY = "ms-supplier-item:redBook_rate_limiter";
}
