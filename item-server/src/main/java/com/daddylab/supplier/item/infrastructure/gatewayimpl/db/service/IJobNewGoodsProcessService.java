package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.JobNewGoodsProcess;

/**
 * <p>
 * 新品商品数据处理（临时任务） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
public interface IJobNewGoodsProcessService extends IDaddyService<JobNewGoodsProcess> {

}
