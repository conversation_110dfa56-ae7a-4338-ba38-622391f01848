package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;

/**
 * <p>
 * 供应商 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface IProviderService extends IDaddyService<Provider> {


    Boolean isSyncKingDee(Long providerId);

    void setKingDee(String kingDeeNo,String kingDeeId,Long id);

}
