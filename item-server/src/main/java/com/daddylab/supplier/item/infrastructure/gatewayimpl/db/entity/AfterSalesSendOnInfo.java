package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 售后异常转寄信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AfterSalesSendOnInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 异常件信息id
     */
    private Long abnormalInfoId;

    /**
     * 关联的退换单号
     */
    private String returnOrderNo;

    /**
     * 物流名称
     */
    private String logisticsName;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 重量（kg）
     */
    private String weight;

    /**
     * 备注
     */
    private String remark;

    /**
     * 转寄收件方仓库
     */
    private String sender;

    /**
     * 转寄收件方电话
     */
    private String senderTel;

    /**
     * 转寄收件方地址
     */
    private String senderAddress;

    /**
     * 转寄收件方仓库编号
     */
    private String senderNo;

    /**
     * 寄送方仓库编号
     */
    private String fromWarehouseNo;

    /**
     * 寄送方联系电话
     */
    private String fromTel;




}
