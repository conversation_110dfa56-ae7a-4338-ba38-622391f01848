package com.daddylab.supplier.item.application.afterSaleLogistics.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/12/23
 */
@RequiredArgsConstructor
@Getter
public enum LogisticsTraceDataSource implements IIntegerEnum {
    KUAIDAOYUN(0, "快刀云"),
    WDT(1, "旺店通"),
    ;
    private final Integer value;
    private final String desc;
}
