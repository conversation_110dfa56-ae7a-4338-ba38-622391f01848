package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PaymentOrderStatus;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffIgnore;

/**
 * 采购管理-付款申请单
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PaymentApplyOrder implements Serializable {

  private static final long serialVersionUID = 1L;

  /** id */
  @TableId(value = "id", type = IdType.AUTO)
  @DiffIgnore
  private Long id;

  /** 创建时间 */
  @TableField(fill = FieldFill.INSERT)
  @DiffIgnore
  private Long createdAt;

  /** 更新时间 */
  @TableField(fill = FieldFill.UPDATE)
  @DiffIgnore
  private Long updatedAt;

  /** 创建者 */
  @TableField(fill = FieldFill.INSERT)
  @DiffIgnore
  private Long createdUid;

  /** 更新者 */
  @TableField(fill = FieldFill.UPDATE)
  @DiffIgnore
  private Long updatedUid;

  /** 删除时间 */
  @TableField(fill = FieldFill.DEFAULT)
  @DiffIgnore
  private Long deletedAt;

  /** 是否删除 */
  @TableLogic @DiffIgnore private Integer isDel;

  /** 编码 */
  @DiffIgnore private String no;

  /** 合作模式 */
  private Integer businessLine;

  /** 采购类型。0：标准采购，1：工厂代发 */
  private Integer purchaseType;

  /** 币别。0：人命币。1：美元 */
  private Integer currencyType;

  /** 采购组织。默认：杭州老爸电商科技有限公司 */
  private String purchaseOrg;

  /** 付款组织。默认：杭州老爸电商科技有限公司 */
  private String payOrg;

  /** 往来(交易)单位ID */
  private Long tradeUnit;

  /** 收款单位ID，同上 */
  private Long payeeUnit;

  /** 收款单位银行账号 */
  private String payeeBankCardNo;

  /** 收款单位开户行行号 */
  private String payeeBankNo;

  /** 收款单位开户行 */
  private String payeeBank;

  /** 采购员。系统登录人用户ID */
  private Long buyerId;

  /** 期望付款日期。10位时间戳 */
  private Long expectedPayTime;

  /** 申请付款日期。10维时间戳 */
  private Long applyTime;

  /** 备注 */
  private String remark;

  /** 付款明细来源。0：采购单，1：结算单 */
  private Integer detailSource;

  /** 付款用途。0：采购付款（默认），1：预付款 */
  private Integer payPurpose;

  /** 付款比例。必须整数，直接为百分比值 */
  private Integer payProportions;

  /** 其他金额扣款 */
  private BigDecimal otherChargeback;

  /** 总申请付款金额 */
  private BigDecimal totalApplyAmount;

  /** 上传附件ID，英文逗号隔开。上传发票 */
  private String additionalIds;

  /** 总实际付款金额 */
  private BigDecimal realPayAmount;

  /** 付款申请关联单据编码。采购单号/结算单号 */
  /*
  private String applyRelatedNo;

  */
  /** 申请付款金额 */
  /*
  private BigDecimal applyAmount;

  */
  /** 申请付款金额备注 */
  /*
  private String applyAmountRemark;*/

  /** 状态 */
  private PaymentOrderStatus status;

  //    /**
  //     * 应付金额
  //     */
  //    private BigDecimal rightAmount;

  /** OA流程SummaryId */
  private String oaProcessSummaryId;

  /** OA流程AffairId */
  private String oaProcessAffairId;

  /** 最后审核时间 */
  private Long lastAuditTime;

  /** 最后审核人 */
  private Long lastAuditUid;

  /** 最后审核状态 1 同意 2 拒绝 */
  private Integer lastAuditStatus;

  /** 付款状态回传时间 */
  private Long paymentCallbackTime;

  /** 付款出纳 */
  private Long paymentCashier;

  /** 付款状态 */
  private Integer paymentStatus;

  /** 是否发生过冲销， 理论上只允许冲销1次 */
  private Integer hadWriteOff;

  /** 财务审核时间 */
  private Long financialAuditTime;

  /** OA审核拒绝下的节点坐标 */
  private Integer rejectIndex;

  private Long submitUid;

  /** 我方银行账号 */
  private String ourAccountNumber;

  /** 我方账户名称 */
  private String ourAccountName;

  /** 我方开户行 */
  private String ourBankName;
}
