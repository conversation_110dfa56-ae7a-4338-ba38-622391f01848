package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemCodeRef;

/**
 * <p>
 * 各种商品编码与后端商品的关联记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
public interface ItemCodeRefMapper extends DaddyBaseMapper<ItemCodeRef> {
    void clearAll();
    int scanItemCode();
    int scanProviderSpecifiedCode();
    int scanPsysCode();
    int scanSkuCode();
    int scanSkuProviderSpecifiedCode();
    int scanRefItemCode();
    int scanRefSkuCode1();
    int scanRefSkuCode2();

}
