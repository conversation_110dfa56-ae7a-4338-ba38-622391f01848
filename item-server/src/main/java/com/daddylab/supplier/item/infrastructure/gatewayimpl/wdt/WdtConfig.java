package com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "wdt")
@Component
@Data
@RefreshScope
public class WdtConfig {

    @Deprecated
    private String key;
    @Deprecated
    private String sid;
    @Deprecated
    private String url;
    @Deprecated
    private String secret;

    //以上字段为了兼容暂时保留，以下面的配置为准

    /**
     * 旺店通接口访问配置
     */
    private List<Config> configs;

    @Data
    @ApiModel("旺店通配置")
    public static class Config {

        private String key;
        private String sid;
        /**
         * 特殊场景使用：通过奇门调用旺店通的测试环境，SID需要使用另一个
         */
        private String sid1;
        private String url;
        private String secret;

        @ApiModelProperty(hidden = true)
        private boolean asDefault;

        public String getRealSecret() {
            return secret.split(":")[0];
        }

        public String getSalt() {
            return secret.split(":")[1];
        }
    }
}
