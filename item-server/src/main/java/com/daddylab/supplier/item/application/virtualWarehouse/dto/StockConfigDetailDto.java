/*
package com.daddylab.supplier.item.application.virtualWarehouse.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

*/
/**
 * <AUTHOR> up
 * @date 2024年03月15日 11:01 AM
 *//*

@Data

public class StockConfigDetailDto {

    @ApiModelProperty("库存明细分类。1虚拟仓.2店铺 二选一就行")
    @NotNull
    private Integer saveType;

    @ApiModelProperty("根数据id.虚拟仓 id,店铺 id,二选一")
    @NotNull
    private Long rootId;

    @ApiModelProperty("库存明细-仓库,虚拟仓或者是实仓")
    private String upperWarehouseNo;

    @ApiModelProperty("实仓编码")
    @NotBlank
    private String warehouseNo;

    @ApiModelProperty("sku编码")
    @NotBlank
    private String skuCode;

    @ApiModelProperty("spu编码")
    private String spuCode;

    @ApiModelProperty("库存占比")
    @NotNull
    private Integer inventoryRatio;

}
*/
