package com.daddylab.supplier.item.domain.drawer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Class  ItemDrawerForm
 *
 * @Date 2022/5/31下午4:18
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ItemDrawerImageVO", description = "商品抽屉详情图片VO")
public class ItemDrawerImageVO implements Serializable {

    @ApiModelProperty(value = "图片id")
    private Long id;

    @ApiModelProperty(value = "图片(视频)地址")
    private String url;

    @ApiModelProperty(value = "文件类型 1-图片 2-视频")
    private Integer fileType;

    @ApiModelProperty(value = "视频首图地址")
    private String firstImageUrl;

    @ApiModelProperty(value = "(视频)图片名称")
    private String filename;

    @ApiModelProperty(value = "文件拓展")
    private String ext;
}
