package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组合多件供价（excel）
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CombinationStepPriceExcel implements Serializable {

  private static final long serialVersionUID = 1L;

  @ApiModelProperty("SPU编码")
  @ExcelProperty("SPU编码")
  private String spuCode;

  @ApiModelProperty("商家编码")
  @ExcelProperty("商家编码")
  @NotBlank(message = "skuCode不能为空")
  private String skuCode;

  @ApiModelProperty("商品名称")
  @ExcelProperty("商品名称")
  private String productName;

  @ApiModelProperty("商品规格")
  @ExcelProperty("商品规格")
  private String productSpec;

  @ApiModelProperty("商品规格数量")
  @ExcelProperty("商品规格数量")
  private String productSpecQuantity;

  @ApiModelProperty("是否纯活动商品")
  @ExcelProperty("是否纯活动商品")
  private String isActivityOnly;

  @ApiModelProperty("日常供价无优惠")
  @ExcelProperty("日常供价无优惠")
  private String regularPrice;

  @ApiModelProperty("工厂/仓库")
  @ExcelProperty("工厂/仓库")
  private String factoryOrWarehouse;

  @ApiModelProperty("供应商")
  @ExcelProperty("供应商")
  private String supplier;

  @ApiModelProperty("采购负责人")
  @ExcelProperty("采购负责人")
  private String purchaser;

  @ApiModelProperty("优惠类型")
  @ExcelProperty("优惠类型")
  private String discountType;

  @ApiModelProperty("平台名称")
  @ExcelProperty("平台名称")
  private String platformName;

  @ApiModelProperty("定价方式")
  @ExcelProperty("定价方式")
  private String priceType;

  @ApiModelProperty("活动开始时间(10位时间戳)")
  private Long activityStartTime;

  @ExcelProperty("活动开始时间")
  @TableField(exist = false)
  private String activityStartTimeStr;

  @ApiModelProperty("活动结束时间(10位时间戳)")
  private Long activityEndTime;

  @ExcelProperty("活动开始时间")
  @TableField(exist = false)
  private String activityEndTimeStr;

  @ApiModelProperty("订单拍下份数")
  @ExcelProperty("订单拍下份数")
  private String orderQuantity;

  @ApiModelProperty("实发单品数量")
  @ExcelProperty("实发单品数量")
  private String deliveredQuantity;

  @ApiModelProperty("按价格优惠结算成本")
  @ExcelProperty("按价格优惠结算成本")
  private String discountPriceCost;

  @ApiModelProperty("按数量优惠件数")
  @ExcelProperty("按数量优惠件数")
  private String discountQuantity;

  @ApiModelProperty("供价优惠内容")
  @ExcelProperty("供价优惠内容")
  private String discountContent;

  @ApiModelProperty("备注说明")
  @ExcelProperty("备注说明")
  private String remark;

  @ApiModelProperty("方式")
  @ExcelProperty("方式")
  private String way;

  @ApiModelProperty("主键ID")
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  @ApiModelProperty("创建时间戳")
  @TableField(fill = FieldFill.INSERT)
  private Long createdAt;

  @ApiModelProperty("更新时间戳")
  @TableField(fill = FieldFill.INSERT_UPDATE)
  private Long updatedAt;

  @ApiModelProperty("创建人ID")
  @TableField(fill = FieldFill.INSERT)
  private Long createdUid;

  @ApiModelProperty("更新人ID")
  @TableField(fill = FieldFill.INSERT_UPDATE)
  private Long updatedUid;

  @ApiModelProperty("删除时间戳")
  @TableField(fill = FieldFill.DEFAULT)
  private Long deletedAt;

  @ApiModelProperty("逻辑删除标识（0:未删除, id:已删除）")
  @TableLogic(value = "0", delval = "id")
  private Integer isDel;
}
