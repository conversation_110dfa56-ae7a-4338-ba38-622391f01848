package com.daddylab.supplier.item.application.purchasePayable.applyPayAudit.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayApplyAuditStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年02月16日 10:01 AM
 */
@Data
@ApiModel("申请付款审核流程返回响应")
public class AuditProcessNodVo {

    @ApiModelProperty("此节点审核流程序列号")
    private Integer seq;

    @ApiModelProperty("此节点id")
    private Long nodeId;

    @ApiModelProperty("此节点审核状态")
    private PayApplyAuditStatus nodeStatus;

    @ApiModelProperty("此节点审核人员操作信息")
    private List<AuditProcessNodeInfoVo> nodeInfoVoList;

//    @ApiModelProperty("此节点是否允许存在【回退到上一级】按钮。1：允许。0：不允许")
//    private Integer rollbackPreviousNode;

    @ApiModelProperty("此节点是否允许存在【回退到此节点】按钮。1：允许。0：不允许")
    private Integer rollbackDedicatedNodes;

    @ApiModelProperty("此节点是否系统自动审核。1：自动审核。0：手动审核")
    private Integer autoAudit;

    private String roleName;

}
