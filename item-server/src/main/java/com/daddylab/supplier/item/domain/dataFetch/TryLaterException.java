package com.daddylab.supplier.item.domain.dataFetch;

/**
 * <AUTHOR>
 * @since 2022/4/29
 */
public class TryLaterException extends FetchException {

    private static final long serialVersionUID = 1L;

    private Integer tryLaterSeconds;

    public TryLaterException(FetchDataType fetchDataType) {
        super(fetchDataType);
    }

    public TryLaterException(FetchDataType fetchDataType, Integer tryLaterSeconds) {
        super(fetchDataType);
        this.tryLaterSeconds = tryLaterSeconds;
    }

    @Override
    public String getMessage() {
        if (tryLaterSeconds != null) {
            return super.getMessage() + ":上游网关暂时无法拉取数据，请" + tryLaterSeconds + "秒后重试";
        } else {
            return super.getMessage() + ":上游网关暂时无法拉取数据，请稍后重试";
        }
    }

    public Integer getTryLaterSeconds() {
        return tryLaterSeconds;
    }
}
