package com.daddylab.supplier.item.application.entryActivityPrice;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.TemporalAccessorUtil;
import com.daddylab.supplier.item.application.entryActivityPrice.models.ConfirmationViewItemVO;
import com.daddylab.supplier.item.application.entryActivityPrice.models.EntryActivityPriceItemInfoVO;
import com.daddylab.supplier.item.application.entryActivityPrice.models.EntryActivityPricePageVO;
import com.daddylab.supplier.item.application.entryActivityPrice.models.EntryActivityPriceSkuVO;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.EntryActivityPriceItemInfoDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.EntryActivityPricePageDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceSku;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/6
 */
@Mapper(uses = {StaffAssembler.class})
public interface EntryActivityPriceConvert {
    EntryActivityPriceConvert INSTANCE = org.mapstruct.factory.Mappers.getMapper(EntryActivityPriceConvert.class);

    @Mapping(target = "skuList", ignore = true)
    @Mapping(target = "buyer", source = "buyerUid")
    @Mapping(target = "corpType", ignore = true)
    @Mapping(target = "bizType", ignore = true)
    EntryActivityPricePageVO toPageVO(EntryActivityPricePageDO pageDO);

    EntryActivityPriceItemInfoVO toItemInfoVO(EntryActivityPriceItemInfoDO o);

    @Mapping(target = "status", ignore = true)
    @Mapping(target = "confirmId", ignore = true)
    EntryActivityPriceItem toItemPO(EntryActivityPricePageVO entryActivityPricePageVO);

    default Long monthStrToLong(String month) {
        return TemporalAccessorUtil.toEpochMilli(DatePattern.NORM_MONTH_FORMATTER.parse(month)) / 1000;
    }

    default String monthLongToStr(Long month) {
        return DatePattern.NORM_MONTH_FORMATTER.format(DateUtil.toLocalDateTime(month));
    }

    EntryActivityPriceSku toSkuPO(EntryActivityPriceSkuVO entryActivityPriceSkuVO);

    List<ConfirmationViewItemVO> toConfirmationViewItemVOList(List<EntryActivityPricePageVO> data);

}
