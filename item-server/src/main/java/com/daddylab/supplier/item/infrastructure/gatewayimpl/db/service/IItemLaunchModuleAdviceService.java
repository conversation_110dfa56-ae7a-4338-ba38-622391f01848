package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchModuleAdvice;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 商品库模块审批修改建议 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
public interface IItemLaunchModuleAdviceService extends IDaddyService<ItemLaunchModuleAdvice> {

}
