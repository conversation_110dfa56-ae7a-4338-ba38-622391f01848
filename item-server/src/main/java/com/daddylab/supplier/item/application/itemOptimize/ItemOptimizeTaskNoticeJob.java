package com.daddylab.supplier.item.application.itemOptimize;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemOptimize;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatMsg;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemOptimizeService;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizeStatus;

import lombok.RequiredArgsConstructor;

import org.apache.commons.collections4.ListValuedMap;
import org.apache.commons.collections4.MultiMapUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/2
 */
@Service
@RequiredArgsConstructor
public class ItemOptimizeTaskNoticeJob {
    private final IItemOptimizeService itemOptimizeService;
    private final RefreshConfig refreshConfig;
    private final MsgSender msgSender;

    @XxlJob("ItemOptimizeTaskNoticeJob")
    public void notice() {
        noticeQc();
        noticeLegal();
    }

    private void noticeQc() {
        final List<ItemOptimize> pendingForQcList =
                itemOptimizeService
                        .lambdaQuery()
                        .eq(
                                ItemOptimize::getStatus,
                                ItemOptimizeStatus.PENDING_QC_REVIEW.getValue())
                        .list();
        if (!pendingForQcList.isEmpty()) {
            final ListValuedMap<StaffBrief, ItemOptimize> msgGroup =
                    MultiMapUtils.newListValuedHashMap();
            for (ItemOptimize itemOptimize : pendingForQcList) {
                for (StaffBrief qcUser : itemOptimize.getData().getQcUsers()) {
                    msgGroup.put(qcUser, itemOptimize);
                }
            }
            for (StaffBrief qcUser : msgGroup.keys()) {
                final List<ItemOptimize> itemOptimizes = msgGroup.get(qcUser);
                final WechatMsg wechatMsg = new WechatMsg();
                wechatMsg.setTitle(
                        String.format("商品优化中共 %s 款商品资料待审核，请尽快给到审核意见和建议！", itemOptimizes.size()));
                wechatMsg.setContent(String.format("共 %s 款商品优化待QC审核", itemOptimizes.size()));
                final String url =
                        String.format(
                                refreshConfig.getDomain()
                                        + "/operation-management/optimize-item/list?qcId=%s&status=%s",
                                qcUser.getUserId(),
                                ItemOptimizeStatus.PENDING_QC_REVIEW.getValue());
                wechatMsg.setLink(url);
                wechatMsg.setRecipient(qcUser.getQwUserId());
                wechatMsg.setType(1);

                msgSender.send(wechatMsg);
            }
        }
    }

    private void noticeLegal() {
        final List<ItemOptimize> pendingForLegalList =
                itemOptimizeService
                        .lambdaQuery()
                        .eq(
                                ItemOptimize::getStatus,
                                ItemOptimizeStatus.PENDING_LEGAL_REVIEW.getValue())
                        .list();
        if (!pendingForLegalList.isEmpty()) {
            final ListValuedMap<StaffBrief, ItemOptimize> msgGroup =
                    MultiMapUtils.newListValuedHashMap();
            for (ItemOptimize itemOptimize : pendingForLegalList) {
                for (StaffBrief legalUser : itemOptimize.getData().getLegalUsers()) {
                    msgGroup.put(legalUser, itemOptimize);
                }
            }
            for (StaffBrief legalUser : msgGroup.keys()) {
                final List<ItemOptimize> itemOptimizes = msgGroup.get(legalUser);
                final WechatMsg wechatMsg = new WechatMsg();
                wechatMsg.setTitle(
                        String.format("商品优化中共 %s 款商品资料待审核，请尽快给到审核意见和建议！", itemOptimizes.size()));
                wechatMsg.setContent(String.format("共 %s 款商品优化待法务审核", itemOptimizes.size()));
                final String url =
                        String.format(
                                refreshConfig.getDomain()
                                        + "/operation-management/optimize-item/list?legalId=%s&status=%s",
                                legalUser.getUserId(),
                                ItemOptimizeStatus.PENDING_LEGAL_REVIEW.getValue());
                wechatMsg.setLink(url);
                wechatMsg.setRecipient(legalUser.getQwUserId());
                wechatMsg.setType(1);

                msgSender.send(wechatMsg);
            }
        }
    }
}
