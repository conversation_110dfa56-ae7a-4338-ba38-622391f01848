package com.daddylab.supplier.item.infrastructure.third.kuaishou.domain.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @class KsItemCreate.java
 * @description 描述类的作用
 * @date 2024-03-01 17:53
 */
@NoArgsConstructor
@Data
public class KsItemOfflineStatusChangeDTO {

    /**
     * 快手商品id
     */
    private Long itemId;
    /**
     * 商家id
     */
    private Integer sellerId;
    /**
     * 商品上下架状态，0: 下架； 1: 上架。
     */
    private Integer status;
    /**
     * 原商品上下架状态
     */
    private Integer beforeStatus;
    /**
     * 原商品上下架状态
     */
    private Integer updateTime;
}
