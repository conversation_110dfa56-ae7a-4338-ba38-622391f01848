package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.daddylab.supplier.item.common.enums.EnableStatusEnum;
import com.daddylab.supplier.item.infrastructure.domain.Entity;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 品牌
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Brand extends Entity {
    private static final long serialVersionUID = 7116007718558322591L;
    /**
     * 品牌编号
     */
    private String sn;

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 品牌LOGO
     */
    private String logo;

    /**
     * 状态 0:停用 1:正常
     */
    private EnableStatusEnum status;

    /**
     * 业务线
     */
    private String businessLine;

    public String getBusinessLine() {
        return DivisionLevelValueEnum.filterCorpType(businessLine);
    }
}
