package com.daddylab.supplier.item.controller.purchase.dto.order;

import com.daddylab.supplier.item.controller.item.dto.ItemAttrDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022/4/22 2:13 下午
 */
@Data
@ApiModel("采购业态，商品列表")
public class PurchaseOrderSkuListVO implements Serializable {

    private static final long serialVersionUID = 3551655682845938082L;

    @ApiModelProperty("商品skuId")
    protected Long skuId;

    @ApiModelProperty("商品skuCode")
    protected String skuCode;

    @ApiModelProperty("商品id")
    protected Long itemId;

    @ApiModelProperty("商品编码")
    protected String itemCode;

    @ApiModelProperty("商品名称")
    protected String itemName;

    @ApiModelProperty("商品品牌")
    protected String brandName;

    @ApiModelProperty("商品规格")
    protected List<ItemAttrDto> specifications;

    @ApiModelProperty("商品品类")
    protected String category;

    @ApiModelProperty("商品主图")
    protected String itemImage;

    @ApiModelProperty("采购成本")
    private BigDecimal procurement;

    @ApiModelProperty("日常销售价")
    private BigDecimal sales;

    @ApiModelProperty("sku库存")
    protected Long stockCount;

    @ApiModelProperty("仓库编号")
    protected String warehouseNo;

    @ApiModelProperty("仓库")
    protected String warehouseName;

    @ApiModelProperty("税率")
    protected BigDecimal taxRate;

    @ApiModelProperty("是否赠品")
    protected Integer isGift;

}
