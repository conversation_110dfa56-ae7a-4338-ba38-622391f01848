package com.daddylab.supplier.item.domain.otherPay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @ClassName OtherAuditDto.java
 * @description
 * @createTime 2022年04月11日 14:45:00
 */
@ApiModel
@Data
public class OtherAuditDto {

    @NotNull(message = "应付单id不能为空")
    @ApiModelProperty(name = "应付单id", required = true)
    private Long id;

    @NotNull(message = "审核状态不能为空")
    @ApiModelProperty(name = "审核状态 0:已完成 1:待提交 2:待审核 3:审核通过 4:审核拒绝", required = true)
    private Integer auditStatus;

    @ApiModelProperty("审核意见")
    private String msg;
}
