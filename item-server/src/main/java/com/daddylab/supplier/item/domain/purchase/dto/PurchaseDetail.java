package com.daddylab.supplier.item.domain.purchase.dto;

import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.javers.core.metamodel.annotation.Entity;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName PurchaseDetail.java
 * @description 采购详情
 * @createTime 2021年11月12日 16:26:00
 */
@Entity
@Data
public class PurchaseDetail {

    /**
     * 采购id
     */
    @ApiModelProperty(value = "采购id")
    private Long id;

    /**
     * 商品sku
     */
    @ApiModelProperty(value = "商品sku")
    private String itemSku;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    /**
     * 商品规格
     */
    @ApiModelProperty(value = "商品规格")
    private String itemSpecs;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String provider;

    /**
     * 日常供价(无优惠)
     */
    @ApiModelProperty(value = "日常供价(无优惠)")
    private String usualPrice;

    /**
     * 优惠类型 0:货抵款 1:按时间供价 2:当月优惠件数
     */
    @ApiModelProperty(value = "优惠类型 0:货抵款 1:按时间供价 2:当月优惠件数")
    private Integer favourableType;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    private Long startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    private Long endTime;

    /**
     * 订单拍下份数
     */
    @ApiModelProperty(value = "订单拍下份数")
    private Long orderCount;

    /**
     * 实发单品数量
     */
    @ApiModelProperty(value = "实发单品数量")
    private Long finalCount;

    /**
     * 按价格优惠结算成本/元
     */
    @ApiModelProperty(value = "按价格优惠结算成本/元")
    private String priceCost;

    /**
     * 按数量优惠结算成本/元
     */
    @ApiModelProperty(value = "按数量优惠结算成本/元")
    private String numCost;

    /**
     * 供价优惠内容
     */
    @ApiModelProperty(value = "供价优惠内容")
    private String content;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态 0:待确认 1:已确认 2:存在异议")
    private Integer status;

    /**
     * 供应商确认链接
     */
    @ApiModelProperty(value = "供应商确认链接")
    private String path;

    /**
     * 供应商确认链接
     */
    @ApiModelProperty(value = "活动时间")
    private String activeTime;

    /**
     * 供应商确认链接
     */
    @ApiModelProperty(value = "异议列表")
    private List<com.daddylab.supplier.item.controller.purchaseDissent.dto.PurchaseDissentVo> purchaseDissentVoList;

    public BigDecimal usualPrice (BigDecimal usualPrice){
        return  new BigDecimal(NumberUtil.format(usualPrice));
    }

}
