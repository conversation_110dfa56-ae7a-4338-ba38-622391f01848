package com.daddylab.supplier.item.domain.banniu;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;

import wjb.open.api.ApiException;
import wjb.open.api.response.v2.banniu.SellerListResponse;
import wjb.open.api.response.v2.banniu.UserListResponse;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/8/25
 */
public interface BanniuCommonService {
    /**
     * 获取班牛店铺列表
     *
     * @return 店铺列表
     * @throws ApiException 班牛异常
     */
    List<SellerListResponse.Seller> shops();

    /**
     * 获取班牛用户列表
     *
     * @return 用户列表
     * @throws ApiException 班牛异常
     */
    List<UserListResponse.User> users();

    /**
     * 班牛用户查询
     *
     * @param userId 班牛用户ID
     */
     Optional<UserListResponse.User> user(long userId);

    /**
     * 班牛用户查询
     * @param user 用户昵称
     */
     Optional<UserListResponse.User> user(String user);


    /**
     * 同步班牛采购员信息
     *
     * @throws ApiException
     */
    void syncBuyer() throws ApiException;

    void syncItem(Item item) throws ApiException;

}
