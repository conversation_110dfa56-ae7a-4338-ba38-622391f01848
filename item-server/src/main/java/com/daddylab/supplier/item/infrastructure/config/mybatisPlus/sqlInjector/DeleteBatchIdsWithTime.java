package com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector;

import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlScriptUtils;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/25 3:18 下午
 * @description
 */
public class DeleteBatchIdsWithTime extends AbstractMethod {

    private static final String METHOD = "deleteBatchIdsWithTime";

    public DeleteBatchIdsWithTime() {
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        SqlMethod sqlMethod = SqlMethod.LOGIC_DELETE_BATCH_BY_IDS;
        String sql;
        SqlSource sqlSource;
        if (tableInfo.isWithLogicDelete()) {
            sql = String.format(sqlMethod.getSql(), tableInfo.getTableName(), this.sqlLogicSet(tableInfo) + ",deleted_at = unix_timestamp()", tableInfo.getKeyColumn(), SqlScriptUtils.convertForeach("#{item}", "coll", (String) null, "item", ","), tableInfo.getLogicDeleteSql(true, true));
            sqlSource = this.languageDriver.createSqlSource(this.configuration, sql, Object.class);
            return this.addUpdateMappedStatement(mapperClass, modelClass, METHOD, sqlSource);
        }
        return null;
//        else {
//            sqlMethod = SqlMethod.DELETE_BATCH_BY_IDS;
//            sql = String.format(sqlMethod.getSql(), tableInfo.getTableName(), tableInfo.getKeyColumn(), SqlScriptUtils.convertForeach("#{item}", "coll", (String) null, "item", ","));
//            sqlSource = this.languageDriver.createSqlSource(this.configuration, sql, Object.class);
//            return this.addDeleteMappedStatement(mapperClass, this.getMethod(sqlMethod), sqlSource);
//        }
    }
}
