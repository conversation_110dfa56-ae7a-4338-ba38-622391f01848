package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BankAccount;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.BankAccountMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBankAccountService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 供应商账户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-20
 */
@Service
public class BankAccountServiceImpl extends DaddyServiceImpl<BankAccountMapper, BankAccount> implements IBankAccountService {

    @Override
    public List<BankAccount> getAccountByProviderId(Long providerId) {
        LambdaQueryWrapper<BankAccount> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BankAccount::getProviderId, providerId);
        queryWrapper.orderByDesc(BankAccount::getUpdatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public Map<Long, List<BankAccount>> getAccountBatchByProviderIds(Collection<Long> providerId) {
        if(CollUtil.isEmpty(providerId)){
            return new HashMap<>(4);
        }
        LambdaQueryWrapper<BankAccount> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(BankAccount::getProviderId, providerId);
        List<BankAccount> list = this.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            return list.stream().collect(
                    Collectors.groupingBy(
                            BankAccount::getProviderId,
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    bankAccounts -> {
                                        bankAccounts.sort(
                                                Comparator.comparing((Function<BankAccount, Long>) bankAccount -> {
                                                    if (Objects.nonNull(bankAccount.getUpdatedAt())) {
                                                        return bankAccount.getUpdatedAt();
                                                    } else if (Objects.nonNull(bankAccount.getCreatedAt())) {
                                                        return bankAccount.getCreatedAt();
                                                    } else {
                                                        return bankAccount.getId();
                                                    }
                                                }).reversed());
                                        return bankAccounts;
                                    })));
        } else {
            return Collections.emptyMap();
        }
    }

    @Override
    public void removeByProviderId(Long providerId) {
        LambdaQueryWrapper<BankAccount> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BankAccount::getProviderId, providerId);
        this.remove(queryWrapper);
    }
}
