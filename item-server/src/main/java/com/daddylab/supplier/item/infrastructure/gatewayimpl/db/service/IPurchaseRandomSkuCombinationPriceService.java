package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseRandomSkuCombinationPrice;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 任意sku组合采购价格信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-21
 */
public interface IPurchaseRandomSkuCombinationPriceService extends IDaddyService<PurchaseRandomSkuCombinationPrice> {

}
