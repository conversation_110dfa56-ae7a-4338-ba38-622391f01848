//package com.daddylab.supplier.item.application.wdt;
//
//import com.alibaba.cola.dto.Response;
//import com.daddylab.job.core.context.XxlJobHelper;
//import com.daddylab.supplier.item.application.message.event.SysPurchaseOrderEvent;
//import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizService;
//import com.daddylab.supplier.item.application.stockInOrder.StockInOrderBizService;
//import com.daddylab.supplier.item.common.OtherStockInOutConfig;
//import com.daddylab.supplier.item.common.enums.ErrorCode;
//import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderCmd;
//import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
//import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetail;
//import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrder;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseOrderMapper;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailMapper;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderMapper;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseOrderService;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderDetailWrapperService;
//import com.daddylab.supplier.item.infrastructure.utils.Alert;
//import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
//import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.time.LocalDateTime;
//import java.time.ZoneId;
//import java.time.ZoneOffset;
//import java.time.format.DateTimeFormatter;
//import java.time.temporal.TemporalAdjusters;
//import java.util.*;
//import java.util.stream.Collectors;
//
//
///**
// * <AUTHOR> up
// * @date 2022/4/13 4:47 下午
// */
//@Service
//@Slf4j
//public class WdtBizService {
//
//    private final static Integer QUERY_ORDER_SIZE = 3000;
//
//    @Autowired
//    WdtOrderMapper wdtOrderMapper;
//
//    @Autowired
//    WdtOrderDetailMapper wdtOrderDetailMapper;
//
////    @Autowired
////    WdtOrderCleaner wdtOrderCleaner;
//
//    @Autowired
//    PurchaseOrderBizService purchaseOrderBizService;
//
//    @Autowired
//    IWdtOrderDetailWrapperService iWdtOrderDetailWrapperService;
//
//    @Autowired
//    PurchaseOrderMapper purchaseOrderMapper;
//
//    @Autowired
//    IPurchaseOrderService iPurchaseOrderService;
//
//    @Autowired
//    StockInOrderBizService stockInOrderBizService;
//
//    @Autowired
//    OtherStockInOutConfig otherStockInOutConfig;
//
//    private static final String KEY = "wdt.order.wrapper.finish.";
//
//
////    public void cleaningOrder(String operateTime) throws Exception {
////        LocalDateTime operate = DateUtil.parse(operateTime, "yyyyMM");
////        String startDt = DateUtil.getFirstDayOfMonth(operate).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:dd"));
////        String endDt = DateUtil.getFirstDayOfMonth(operate.plusMonths(1)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:dd"));
////        StopWatch stopWatch = new StopWatch();
////        stopWatch.start();
////
////        wdtOrderDetailMapper.deleteWrapper(operateTime);
////        RedisUtil.del(KEY + operateTime);
////
////        CountDownLatch countDownLatch = new CountDownLatch(2);
////
////        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
////            try {
////                wdtOrderSingleWrapperHandler(startDt, endDt, operateTime, otherStockInOutConfig.getWarehouseNos());
////            } catch (Exception e) {
////                log.error("清洗单品商品订单操作异常", e);
////            } finally {
////                countDownLatch.countDown();
////            }
////        });
////
////        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
////            try {
////                wdtOrderSuiteWrapperHandler(startDt, endDt, operateTime, otherStockInOutConfig.getWarehouseNos());
////            } catch (Exception e) {
////                log.error("清洗组合商品订单操作异常", e);
////            } finally {
////                countDownLatch.countDown();
////            }
////        });
////        countDownLatch.await();
////
////        stopWatch.stop();
////        Integer count = iWdtOrderDetailWrapperService.lambdaQuery().eq(WdtOrderDetailWrapper::getOperateTime, operateTime)
////                .select().count();
////        Alert.text(MessageRobotCode.GLOBAL,
////                "从wdt订单中清洗出商品信息完成，商品信息合计条数:" + count + ",耗时:" + stopWatch.getTotalTimeSeconds() + "秒。");
////        // 处理结束，记录标记
////        RedisUtil.set(KEY + operateTime, "1", 1, TimeUnit.DAYS);
////    }
////
////
////    /**
////     * 处理单商品订单
////     *
////     * @param startDtStr WDT订单发货开始时间 yyyy-MM-dd HH:mm:ss
////     * @param endDtStr   WDT订单发货结束时间 yyyy-MM-dd HH:mm:ss
////     */
////    private void wdtOrderSingleWrapperHandler(String startDtStr, String endDtStr, String operateTime,
////                                              List<String> eliminateWarehouseNos) {
////        Integer count = wdtOrderMapper.orderSingleDetailCount(startDtStr, endDtStr, eliminateWarehouseNos);
////        int cycles = count % QUERY_ORDER_SIZE == 0 ? count / QUERY_ORDER_SIZE : (count / QUERY_ORDER_SIZE) + 1;
////
////        for (int i = 0; i < cycles; i++) {
////            int offset = i * QUERY_ORDER_SIZE;
////            List<Long> errorWdtTradeIdList = saveBatchSingleWrapper(startDtStr, endDtStr, operateTime, eliminateWarehouseNos, offset);
////            if (CollectionUtils.isNotEmpty(errorWdtTradeIdList)) {
////                Alert.text(MessageRobotCode.GLOBAL, "wdt订单信息处理异常," + JsonUtil.toJson(errorWdtTradeIdList));
////            }
////        }
////    }
////
////    /**
////     * 单品订单数据清洗
////     *
////     * @param startDtStr
////     * @param endDtStr
////     * @param operateTime
////     * @param offset      处理数据量
////     */
////    private List<Long> saveBatchSingleWrapper(String startDtStr, String endDtStr, String operateTime,
////                                              List<String> eliminateWarehouseNos, Integer offset) {
//////        List<WdtOrderDetailDO> wdtOrderDetailDOList = wdtOrderMapper.orderSingleDetailList(startDtStr, endDtStr
//////                , eliminateWarehouseNos, offset, QUERY_ORDER_SIZE);
//////        return wdtOrderCleaner.completionSingleWrapper(wdtOrderDetailDOList, operateTime);
////        return null;
////    }
////
////    /**
////     * 处理组合商品订单
////     *
////     * @param startDtStr WDT订单发货开始时间 yyyy-MM-dd HH:mm:ss
////     * @param endDtStr   WDT订单发货结束时间 yyyy-MM-dd HH:mm:ss
////     */
////    private void wdtOrderSuiteWrapperHandler(String startDtStr, String endDtStr, String operateTime
////            , List<String> eliminateWarehouseNos) {
////
////
////        List<Long> tradeIdList = wdtOrderMapper.orderSuiteDetailList(startDtStr, endDtStr, eliminateWarehouseNos);
////        Set<Long> clearingTradeIdSet = new HashSet<>(tradeIdList);
////        wdtOrderCleaner.completionSuiteWrapper(clearingTradeIdSet, operateTime);
////
////
////    }
//
//    // ----------------------------------------------- 手动分割线  -----------------------------------------------
//
//    // ----------------------------------------------- 第二手动分割线  -----------------------------------------------
//
//    /**
//     * 系统生成工厂采购单。
//     *
//     * @param jobRunningTime
//     * @return
//     */
//    public Response saveSysPurchaseOrder(String jobRunningTime) {
//
//        LocalDateTime jobRunningLdt = DateUtil.parse(jobRunningTime, "yyyyMM");
//        String targetOperateTime = jobRunningLdt.plusMonths(-1).format(DateTimeFormatter.ofPattern("yyyyMM"));
//        // 财务要求。采购日志为上一个月的最后一天。
//        LocalDateTime lastMonthLastDate = jobRunningLdt.plusMonths(-1).with(TemporalAdjusters.lastDayOfMonth());
//        long purchaseDate = lastMonthLastDate.toEpochSecond(ZoneOffset.of("+8"));
//
////        String s = RedisUtil.get(KEY + targetOperateTime);
////        if (StringUtils.isBlank(s)) {
////            Alert.text(MessageRobotCode.GLOBAL, "wdt订单清洗任务没有完成，请稍后再尝试生成系统采购单");
////            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "wdt订单清洗任务没有完成，请稍后再尝试生成系统采购单");
////        }
//
//        // 逻辑删除6月份系统生成的采购单。重新系统生成。
//        Long start = DateUtil.getFirstDayOfMonth(jobRunningLdt).atZone(ZoneId.systemDefault()).toEpochSecond();
//        Long end = DateUtil.getFirstDayOfMonth(jobRunningLdt).plusMonths(1).atZone(ZoneId.systemDefault()).toEpochSecond();
//        purchaseOrderMapper.deleteSysOrder(start, end);
//
//        List<Long> errorProviderIdList = new LinkedList<>();
//        // 根据供应商的不同，开始分割商品信息，最终自动生成采购单
////        List<Long> providerIdList = wdtOrderDetailMapper.getProviderIdList(targetOperateTime);
//        List<Long> providerIdList = new LinkedList<>();
//        providerIdList.forEach(providerId -> {
//            try {
//                List<WdtOrderDetailWrapper> thisProviderWrapperList = iWdtOrderDetailWrapperService.lambdaQuery().eq(WdtOrderDetailWrapper::getOperateTime, targetOperateTime)
//                        .eq(WdtOrderDetailWrapper::getProviderId, providerId).select().list();
//
//                // 区分出单品和组合商品。然后进行聚合累加
//                List<WdtOrderDetailWrapper> singleList = thisProviderWrapperList.stream().filter(v -> StringUtils.isBlank(v.getSuiteNo())).collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(singleList)) {
//                    Map<WdtOrderDetailWrapper.SingleKey, WdtOrderDetailWrapper> singleKeyWdtOrderDetailWrapperMap = cutSingle(singleList);
//                    buildPurchaseOrder(providerId, new LinkedList<>(singleKeyWdtOrderDetailWrapperMap.values()), purchaseDate);
//                }
//                List<WdtOrderDetailWrapper> suiteList = thisProviderWrapperList.stream().filter(v -> StringUtils.isNotBlank(v.getSuiteNo())).collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(suiteList)) {
//                    Map<WdtOrderDetailWrapper.SuiteKey, WdtOrderDetailWrapper> suiteKeyWdtOrderDetailWrapperMap = cutSuite(suiteList);
//                    buildPurchaseOrder(providerId, new LinkedList<>(suiteKeyWdtOrderDetailWrapperMap.values()), purchaseDate);
//                }
//            } catch (Exception e) {
//                log.error("自动生成采购单异常。providerId:{}", providerId, e);
//                errorProviderIdList.add(providerId);
//            }
//        });
//
//        EventBusUtil.post(SysPurchaseOrderEvent.build(LocalDateTime.now().plusMonths(-1).getMonth().getValue()), true);
//
//        if (CollectionUtils.isNotEmpty(errorProviderIdList)) {
//            return Response.buildFailure(ErrorCode.SYS_ERROR.getCode(), JsonUtil.toJson(errorProviderIdList));
//        }
//        return Response.buildSuccess();
//
//    }
//
//    private void buildPurchaseOrder(Long providerId, List<WdtOrderDetailWrapper> wrapperList, Long purchaseDate) {
//        PurchaseOrderCmd cmd = PurchaseOrderCmd.of(providerId, wrapperList, purchaseDate);
//        purchaseOrderBizService.sysSave(cmd);
//    }
//
//
//    public Map<WdtOrderDetailWrapper.SingleKey, WdtOrderDetailWrapper> cutSingle(List<WdtOrderDetailWrapper> list) {
//        Map<WdtOrderDetailWrapper.SingleKey, WdtOrderDetailWrapper> map = new HashMap<>(32);
//
//        for (WdtOrderDetailWrapper wrapper : list) {
//            WdtOrderDetailWrapper.SingleKey singleKey = new WdtOrderDetailWrapper.SingleKey(wrapper.getSkuCode(), wrapper.getIsGift());
//
//            WdtOrderDetailWrapper wrapper1 = map.get(singleKey);
//            if (Objects.isNull(wrapper1)) {
//                map.put(singleKey, wrapper);
//            } else {
//                wrapper.setQuantity(wrapper.getQuantity() + wrapper1.getQuantity());
//                map.put(singleKey, wrapper);
//            }
//        }
//        return map;
//    }
//
//    public Map<WdtOrderDetailWrapper.SuiteKey, WdtOrderDetailWrapper> cutSuite(List<WdtOrderDetailWrapper> list) {
//        Map<WdtOrderDetailWrapper.SuiteKey, WdtOrderDetailWrapper> map = new HashMap<>(32);
//
//        for (WdtOrderDetailWrapper wrapper : list) {
//            WdtOrderDetailWrapper.SuiteKey suiteKey = new WdtOrderDetailWrapper.SuiteKey(wrapper.getSkuCode(), wrapper.getIsGift());
//
//            WdtOrderDetailWrapper wrapper1 = map.get(suiteKey);
//            if (Objects.isNull(wrapper1)) {
//                map.put(suiteKey, wrapper);
//            } else {
//                wrapper.setQuantity(wrapper.getQuantity() + wrapper1.getQuantity());
//                map.put(suiteKey, wrapper);
//            }
//        }
//        return map;
//    }
//
//
//    /**
//     * 有系统生成的工厂采购单，自动生成入库单
//     *
//     * @param jobMothStart
//     * @param jobMonthEnd
//     */
//    public void generateStockInOrder(Long jobMothStart, Long jobMonthEnd) {
//        List<PurchaseOrder> sysAddOrderList = iPurchaseOrderService.lambdaQuery().between(PurchaseOrder::getCreatedAt, jobMothStart, jobMonthEnd)
//                .eq(PurchaseOrder::getCreatedUid, 0L).eq(PurchaseOrder::getSource, 1)
//                .eq(PurchaseOrder::getType, 2).select().list();
//        List<Long> errorIdList = new LinkedList<>();
//
//        for (PurchaseOrder purchaseOrder : sysAddOrderList) {
//            try {
//                StockInOrderAndOrderDetail detail = StockInOrderAndOrderDetail.ofPurchaseOrder(purchaseOrder);
//                Response response = stockInOrderBizService.createStockInOrder(detail);
//                if (!response.isSuccess()) {
//                    errorIdList.add(purchaseOrder.getId());
//                    XxlJobHelper.log("采购单自动生成入库单失败,purchaseOrderId:{},error:{}", purchaseOrder.getId(), response.getErrMessage());
//                }
//            } catch (Exception e) {
//                errorIdList.add(purchaseOrder.getId());
//                XxlJobHelper.log("采购单创建完成，系统生成入库单异常，purchaseOrderId:{}", purchaseOrder.getId(), e);
//            }
//        }
//
//        if (CollectionUtils.isNotEmpty(errorIdList)) {
//            Alert.text(MessageRobotCode.GLOBAL, "采购单自动生成入库单失败or异常，以下purchaseOrderId:"
//                    + JsonUtil.toJson(errorIdList));
//        }
//    }
//
//
//}
