package com.daddylab.supplier.item.controller.handingsheet.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/24 13:52
 * @Description: 店铺活动富文本内容分页展示 VO
 */
@Data
@ApiModel("店铺活动富文本内容分页展示 VO")
public class HandingSheetActivityTextPageVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "店铺活动富文本内容ID")
    private Long handingSheetActivityTextId;

    @ApiModelProperty(value = "富文本内容")
    private String content;

    @ApiModelProperty(value = "最后编辑人花名")
    private String lastEditorNickName;

    @ApiModelProperty(value = "最后编辑人企微ID")
    private String lastEditorQwId;

    @ApiModelProperty(value = "最后编辑时间（时间戳，单位秒）")
    private Long lastEditTime;

    @ApiModelProperty(value = "活动名")
    private String activityName;

    @ApiModelProperty(value = "创建人uid")
    private Long creatorUid;
}
