package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseTable;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseTableMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseTableService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 采购权限 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
@Service
public class PurchaseTableServiceImpl extends DaddyServiceImpl<PurchaseTableMapper, PurchaseTable> implements IPurchaseTableService {

}
