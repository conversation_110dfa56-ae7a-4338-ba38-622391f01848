package com.daddylab.supplier.item.application.salesOutStock.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年10月20日 5:14 PM
 */
@ApiModel("销售出库单详情返回")
@Data
public class SalesOutStockDetailVO {

    @ApiModelProperty("销售出库单基础信息")
    SalesOutStockDetailBaseVO baseInfo;

    @ApiModelProperty("销售出库单明细")
    private List<SalesOutStockDetailListVO> lists;

    @ApiModelProperty("销售出库单明细-商品数量")
    private Integer listTotalQuantity;

    @ApiModelProperty("销售出库单明细-应收金额")
    private BigDecimal listTotalAmount;


}
