package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockIn;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OtherStockInMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOtherStockInService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 其他入库单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-30
 */
@Service
public class OtherStockInServiceImpl extends DaddyServiceImpl<OtherStockInMapper, OtherStockIn> implements IOtherStockInService {

}
