package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerLiveVerbal;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerLiveVerbalMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerLiveVerbalService;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 新品商品-商品抽屉-直播话术审核表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Service
public class ItemDrawerLiveVerbalServiceImpl
        extends DaddyServiceImpl<ItemDrawerLiveVerbalMapper, ItemDrawerLiveVerbal>
        implements IItemDrawerLiveVerbalService {

    @Override
    public List<ItemDrawerLiveVerbal> getByItemId(Long itemId) {
        return this.lambdaQuery()
                   .eq(ItemDrawerLiveVerbal::getItemId, itemId)
                   .orderByAsc(ItemDrawerLiveVerbal::getCreatedAt)
                   .list();
    }

    @Override
    public String getName(boolean hasOldData, ItemDrawerLiveVerbal liveVerbal) {
        final List<ItemDrawerLiveVerbal> liveVerbals = getByItemId(liveVerbal.getItemId());
        return getName(hasOldData, liveVerbal, liveVerbals);
    }

    @Override
    public String getName(boolean hasOldData, ItemDrawerLiveVerbal liveVerbal, List<ItemDrawerLiveVerbal> liveVerbals) {
        if (!NumberUtil.isPositive(liveVerbal.getId())) {
            return "话术1";
        }
        final int index = liveVerbals.indexOf(liveVerbal);
        return Optional.ofNullable(liveVerbal.getName())
                .filter(StringUtil::isNotBlank)
                .orElse("话术" + (index + (hasOldData ? 2 : 1)));
    }
}
