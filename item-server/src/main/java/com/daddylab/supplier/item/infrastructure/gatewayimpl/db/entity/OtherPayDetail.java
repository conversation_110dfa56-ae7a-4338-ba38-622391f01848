package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 其他应付支付明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OtherPayDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联的其他应付单id
     */
    private Long otherPayId;

    /**
     * 其他应付类型，0.合计。1.运费。2.售后。3.抖音扣款。4.商家处罚。5.其他
     */
    private Integer payType;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 业务日期
     */
    private Long businessAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    @TableLogic
    private Integer isDel;

    private Long deletedAt;


}
