package com.daddylab.supplier.item.application.item.combinationItem.dto.vo;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.CombinationItemType;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/12 5:24 下午
 * @description
 */
@Data
@ApiModel("组合商品分页响应")
public class CombinationPageVO {

    private Long id;

    @ApiModelProperty("组合编码")
    private String code;

    @ApiModelProperty("组合装名称")
    private String name;

    @ApiModelProperty("组成sku的简信")
    private List<ComposeSkuShortVO> skuShortList;

    @ApiModelProperty("属性")
    private CombinationItemType type;

    @ApiModelProperty("创建人")
    private String createName;

    @ApiModelProperty("创建时间")
    private Long createdAt;

    @ApiModelProperty("合作模式（业务线）")
    private Integer businessLine;

    @ApiModelProperty("合作方")
    private List<Integer> corpType;

    @ApiModelProperty("业务类型")
    private List<Integer> bizType;

}
