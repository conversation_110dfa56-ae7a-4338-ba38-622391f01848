package com.daddylab.supplier.item.application.salesInStock.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年10月23日 1:58 PM
 */
@Data
public class SalesInStockDetailListVO {
    @ApiModelProperty("商品SKU")
    private String specNo;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("商品规格")
    private String specName;

    @ApiModelProperty("商品编号")
    private String goodsNo;

    @ApiModelProperty("预期入库数量")
    private BigDecimal rightNum;

    @ApiModelProperty("数量")
    private BigDecimal num;

    @ApiModelProperty("是否残次品")
    private Integer defect;

    @ApiModelProperty("单位")
    private String basicUnitName;

    @ApiModelProperty("品牌")
    private String brandName;

    @ApiModelProperty("吊牌")
    private String label;

    @ApiModelProperty("条码")
    private String prop2;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("商品图片")
    private String imgUrl;

    /**
     * todo 在销退入库的相关表中，找不到重量相关的字段，只有wdt_order中出现了重量，但是是预估总重量，没有根据商品拆分之后的重量标识
     */
    @ApiModelProperty("重量")
    private BigDecimal weight = BigDecimal.ZERO;

}
