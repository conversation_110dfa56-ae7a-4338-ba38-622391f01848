package com.daddylab.supplier.item.application.saleItem.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel("销售商品库商品入参")
public class SaleItemLibraryCmd implements Serializable {

    private static final long serialVersionUID = -3551556342132004716L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "日常编码")
    @NotBlank(message = "日常编码不能为空")
    private String itemSkuCode;

    @ApiModelProperty(value = "行业类目")
    @NotNull(message = "行业类目不能为空")
    private String category;

    @ApiModelProperty(value = "直播/短视频/社群价格")
    @NotNull(message = "直播/短视频/社群价格")
    private String platformPrice;


    @ApiModelProperty(value = "直播/短视频/社群活动内容")
    @NotNull(message = "直播/短视频/社群活动内容")
    private String platformActivityContent;


    @ApiModelProperty(value = "直播/短视频/社群成本")
    @NotNull(message = "直播/短视频/社群成本")
    private String platformCost;

    @ApiModelProperty(value = "直播/短视频/社群活动编码")
    @NotNull(message = "直播/短视频/社群活动编码")
    private String platformActivityCode;

    @ApiModelProperty(value = "特殊备注（仅限某渠道、库存限量等)")
    @NotNull(message = "特殊备注（仅限某渠道、库存限量等)")
    private String remark;
}
