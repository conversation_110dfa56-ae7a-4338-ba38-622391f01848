package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BrandProvider;

/**
 * <p>
 * 品牌供应商 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
public interface IBrandProviderService extends IDaddyService<BrandProvider> {

}
