package com.daddylab.supplier.item.infrastructure.third.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @class RedBookMessageEnum.java
 * @description 快手事件类型
 * @date 2024-03-01 15:44
 */
@AllArgsConstructor
@Getter
public enum KsMessageEnum implements IEnum<String> {

    ITEM_DEFAULT("default", "公共消息"),
    ITEM_CREATE("kwaishop_item_addItem", "新增商品消息"),
    ITEM_AUDIT_STATUS_CHANGE("kwaishop_item_auditStatusChange", "商品审核状态变更消息"),
    ITEM_CID_CHANGE("kwaishop_item_cidChange", "商品类目id变更消息"),
    ITEM_LIMIT_STRATEGY_CHANGE("kwaishop_item_limitStrategyChange", "商品限购策略变更消息"),
    ITEM_OFFLINE_STATUS_CHANGE("kwaishop_item_offlineStatusChange", "商品上下线状态变更消息"),
    ITEM_PRICE_CHANGE("kwaishop_item_priceChange", "商品价格变更消息"),
    ITEM_SKU_CHANGE("kwaishop_item_skuChange", "商品sku变更消息"),
    ITEM_SKU_STOCK_CHANGE("kwaishop_item_sku_stockChange", "商品SKU库存数量变更消息"),
    ITEM_SKU_STOCK_ZERO("kwaishop_item_sku_stockZero", "商品SKU库存数量为0变更消息"),
    ITEM_STATUS_CHANGE("kwaishop_item_statusChange", "商品状态变更消息"),
    ITEM_STOCK_MODEL_CHANGE("kwaishop_item_stockModelChange", "商品库存模式切换消息"),
    ITEM_STOCK_ZERO("kwaishop_item_stockZero", "商品库存数量变更为0消息"),
    ITEM_TITLE_CHANGE("kwaishop_item_titleChange", "商品名称变更消息"),
    ;

    private final String value;
    private final String desc;

    public static KsMessageEnum ofValue(String value) {
        for (KsMessageEnum ksMessageEnum : KsMessageEnum.values()) {
            if (ksMessageEnum.getValue().equals(value)) {
                return ksMessageEnum;
            }
        }
        return null;
    }
}
