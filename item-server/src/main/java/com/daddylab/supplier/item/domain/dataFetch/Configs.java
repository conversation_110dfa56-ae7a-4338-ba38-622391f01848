package com.daddylab.supplier.item.domain.dataFetch;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.LinkedHashSet;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/4/29
 */
@EnableConfigurationProperties(Configs.class)
@ConfigurationProperties(prefix = "data-fetcher")
@Data
@Configuration
@RefreshScope
public class Configs {

    /**
     * 是否禁用
     */
    private boolean disabled = false;

    /**
     * 最大实例数
     */
    private int maxInstanceNum = 1;

    /**
     * 每次调度后休眠多长时间（单位毫秒）
     */
    private int sleepMillisAfterPerDispatch = 10;

    /**
     * 强制指定 leaderId
     */
    private int forceLeaderId = -1;

    /**
     * 配置列表
     */
    private LinkedHashSet<FetcherConfig> fetcherConfigs;

    public LinkedHashSet<FetcherConfig> getFetcherConfigs() {
        return fetcherConfigs.stream()
                             .filter(it -> it.getFetchDataType() != null)
                             .collect(Collectors.toCollection(LinkedHashSet::new));
    }

    public Optional<FetcherConfig> getFetcherConfig(FetchDataType fetchDataType) {
        return fetcherConfigs.stream()
                .filter(fetcherConfig -> Objects
                        .equals(fetcherConfig.getFetchDataType(), fetchDataType))
                .findAny();
    }
}
