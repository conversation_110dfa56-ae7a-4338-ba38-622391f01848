package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemInventorySetting;

/**
 * <p>
 * 平台商品库存设置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface IPlatformItemInventorySettingService extends IDaddyService<PlatformItemInventorySetting> {

     PlatformItemInventorySetting getByOuterItemId(Platform platform, String shopNo, String outerItemId);

}
