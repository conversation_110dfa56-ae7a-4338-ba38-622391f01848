package com.daddylab.supplier.item.application.offShelf.dto;

import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OffShelfStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OffShelfReasonType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OffShelfUrgentLevel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/5
 */
@Data
public class OffShelfFormVO {

    private Long id;

    @ApiModelProperty(value = "紧急程度", notes = "1十分紧急，2紧急，3一般")
    private OffShelfUrgentLevel urgentLevel;

    @ApiModelProperty(value = "下架理由", notes = "0店铺销量，1客诉问题，2检测不合格，3舆情问题，4商务合作问题，5其他")
    private List<OffShelfReasonType> reasonType;

    @ApiModelProperty("原因描述")
    private String reasonTxt;

    @ApiModelProperty("原因文件数组")
    private List<OffShelfFile> reasonFile;

    @ApiModelProperty("下架商品")
    private List<OffShelfItemVO> itemList;

    @ApiModelProperty(value = "状态", notes = "0待提交，1待审核，11已撤回，2待处理，3已完成，31已拒绝")
    private OffShelfStatus status;

    @ApiModelProperty("流程节点")
    private OffShelfProcessInfoVO processInfo;

    @ApiModelProperty("接收审核信息")
    private OffShelfApproveInfoVO approveInfo;

    @ApiModelProperty("处理反馈")
    private List<OffShelfFeedbackVO> feedbackList;

    @ApiModelProperty("申请人")
    private StaffBrief applicant;

    @ApiModelProperty("申请时间")
    private Long applyTime;

    @ApiModelProperty("流程编号")
    private String no;
}
