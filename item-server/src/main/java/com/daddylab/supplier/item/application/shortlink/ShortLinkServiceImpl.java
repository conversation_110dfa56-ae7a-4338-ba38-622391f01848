package com.daddylab.supplier.item.application.shortlink;

import com.daddylab.supplier.item.application.shortlink.feign.ShortLinkFeignClient;
import com.daddylab.supplier.item.application.shortlink.feign.dto.GenerateParams;
import com.daddylab.supplier.item.application.shortlink.feign.dto.Result;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;

@Service
public class ShortLinkServiceImpl implements ShortLinkService {
    @Autowired
    ShortLinkFeignClient shortLinkFeignClient;

    /**
     * 配置一个短链接缓存
     */
    LoadingCache<String, String> shortLinkCache = Caffeine.newBuilder()
            .maximumSize(5000)
            .expireAfterWrite(Duration.ofSeconds(86400))
            .build(this::doGenerate);

    public void invalidCache() {
        shortLinkCache.invalidateAll();
    }

    @Override
    public String generate(String link) {
        return shortLinkCache.get(link);
    }

    private String doGenerate(String link) {
        final GenerateParams params = GenerateParams.ofLink(link);
        final Result<String> generate = shortLinkFeignClient.generate(params);
        if (!generate.getFlag()) {
            throw new ShortLinkSysException(generate.getMsg());
        }
        return generate.getData();
    }
}
