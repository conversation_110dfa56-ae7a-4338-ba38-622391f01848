package com.daddylab.supplier.item.application.purchase.order.factory.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年11月07日 5:41 PM
 */
@Data
public class WdtRefundManageDO {
    private String skuCode;

    /**
     * 下单时间 （毫 秒级时间戳，例如：1631861379000）
     */
    private Long tradeTime;

    private Integer platformId;
    private BigDecimal refundNum;

    /**
     * 退换单号
     * TKxxxxx
     */
    private String refundNo;

    /**
     * 交易单号
     * JYxXXXXXXX
     */
    private String tradeNo;

    private String suiteNo;

    private String warehouseNo;

}
