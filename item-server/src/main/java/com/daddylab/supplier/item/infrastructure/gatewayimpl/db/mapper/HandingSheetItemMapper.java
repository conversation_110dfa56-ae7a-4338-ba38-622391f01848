package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetItem;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 盘货表关联的商品 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
public interface HandingSheetItemMapper extends DaddyBaseMapper<HandingSheetItem> {

    Long countItemNumByHandingSheetId(@Param("handingSheetId") Long handingSheetId);

    long selectPageListCount(@Param(Constants.WRAPPER) Wrapper<HandingSheetItem> queryWrapper);

    IPage<HandingSheetItem> selectPageList(@Param("page") Page<HandingSheetItem> page,
                                           @Param(Constants.WRAPPER) Wrapper<HandingSheetItem> queryWrapper);

    List<HandingSheetItem> selectListByHandingSheetIdAndBuyerUid(@Param("handingSheetId") Long handingSheetId,
                                                                 @Param("buyerUid") Long buyerUid);

    List<Boolean> listPassedColumnByHandingSheetId(@Param("handingSheetId") Long handingSheetId);

    long selectPageListCountForAudit(@Param(Constants.WRAPPER) QueryWrapper<HandingSheetItem> queryWrapper);

    IPage<HandingSheetItem> selectPageListForAudit(@Param("page") Page<HandingSheetItem> page,
                                                   @Param(Constants.WRAPPER) QueryWrapper<HandingSheetItem> queryWrapper);
}
