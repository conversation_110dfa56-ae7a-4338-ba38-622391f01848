package com.daddylab.supplier.item.application.itemReference.types;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("商品参照表查询")
public class ItemReferenceQuery extends PageQuery {


    private static final long serialVersionUID = -6584523472881912657L;

    @ApiModelProperty("商品名称")
    @Size(max = 128, message = "商品名称长度不得大于{max}个字符")
    private String itemName;

    @ApiModelProperty("商品编码")
    @Size(max = 64, message = "商品编码不得大于{max}个字符")
    private String itemCode;

    @ApiModelProperty("商品SKU")
    @Size(max = 64, message = "商品SKU不得大于{max}个字符")
    private String skuCode;

    @ApiModelProperty("品类ID")
    @Positive(message = "品类ID应该是一个正整数")
    private Long categoryId;

    @ApiModelProperty("品牌ID")
    @Positive(message = "品牌ID应该是一个正整数")
    private Long brandId;

    @ApiModelProperty("供应商ID")
    @Positive(message = "供应商ID应该是一个正整数")
    private Long providerId;

    @ApiModelProperty("采购员用户ID")
    @Positive(message = "采购员用户ID应该是一个正整数")
    private Long buyerUid;

    @ApiModelProperty("发货渠道 0:仓库发货 1:工厂发货 2:全部")
    @PositiveOrZero(message = "发货渠道参数不合法")
    private Long delivery;

    @ApiModelProperty("商品状态 0:待上架 1:已上架 2:下架")
    @PositiveOrZero(message = "商品状态参数不合法")
    private Long itemStatus;

    @ApiModelProperty("预计上架时间")
    @Positive(message = "上架时间不合法")
    private Long shelfStartTime;

    @ApiModelProperty("预计上架时间")
    @Positive(message = "上架时间不合法")
    private Long shelfEndTime;

    @ApiModelProperty("合作模式（业务线）筛选（多选）")
    private List<Integer> businessLine;

    @ApiModelProperty("合作方")
    private List<Integer> corpType;

    @ApiModelProperty("业务类型")
    private List<Integer> bizType;
}