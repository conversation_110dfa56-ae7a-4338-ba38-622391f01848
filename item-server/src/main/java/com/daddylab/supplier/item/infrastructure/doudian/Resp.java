package com.daddylab.supplier.item.infrastructure.doudian;

import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2022年09月23日 10:49 AM
 */
@Data
public class Resp<T> {

    private Boolean isSuccess;
    private String error;
    private T data;
    private String syncCode;

    public static class Builder<T>{
        private Boolean isSuccess;
        private String error;
        private T data;
        private String syncCode;

        public Builder<T> isSuccess(Boolean isSuccess){
            this.isSuccess = isSuccess;
            return this;
        }

        public Builder<T> error(String error){
            this.error = error;
            return this;
        }

        public Builder<T> data(T data){
            this.data = data;
            return this;
        }

        public Builder<T> syncCode(String syncCode){
            this.syncCode = syncCode;
            return this;
        }

        public Resp.Builder<T> builder() {
            return new Resp.Builder<T>();
        }

        public Resp<T> build(){
            Resp<T> resp = new Resp<>();
            resp.setIsSuccess(this.isSuccess);
            resp.setError(this.error);
            resp.setData(this.data);
            resp.setSyncCode(this.syncCode);
            return resp;
        }

    }

}
