package com.daddylab.supplier.item.application.item.combinationItem.dto.query;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/7 10:17 上午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("组合商品sku列表查询参数")
public class ComposeSkuPageQuery extends PageQuery {

    private static final long serialVersionUID = -7985474475751252115L;
    @ApiModelProperty("商品sku")
    private String skuCode;

    @ApiModelProperty("商品id")
    private Long itemId;

    @ApiModelProperty("商品编码")
    private String itemCode;

    @ApiModelProperty("品类id")
    private Long categoryId;

    @ApiModelProperty("品牌id")
    private Long brandId;

    private Integer offsetVal;

    private List<Long> skuIdList;

    private Boolean withPrice = false;

    @ApiModelProperty("采购单id")
    private Long purchaseOrderId;

    private List<String> skuCodeList;

    @ApiModelProperty("供应商id")
    private Long providerId;

    @ApiModelProperty(value = "是否同步旺店通.0不同步。1同步")
    private Integer isSyncWdt;

    @ApiModelProperty("合作方")
    private List<Integer> businessLine;

    /**
     * 商品状态0:待上架.1:在售中.2:已下架
     */
    @ApiModelProperty("商品状态 0:待上架 1:在售中 2:已下架")
    private List<Integer> itemStatus;

    @ApiModelProperty("合作方")
    private List<Integer> corpType;

    @ApiModelProperty("业务类型")
    private List<Integer> bizType;




}
