package com.daddylab.supplier.item.controller.open;

import com.alibaba.cola.dto.MultiResponse;
import com.daddylab.supplier.item.application.platformItem.PlatformItemOpenService;
import com.daddylab.supplier.item.domain.platformItem.vo.PlatformItemOpenVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/6/14
 */
@Slf4j
@RestController
@RequestMapping("/open/api/platformItem")
@Api(value = "平台商品开放接口", tags = "平台商品开放接口")
public class OpenPlatformItemController {

    @Autowired PlatformItemOpenService platformItemOpenService;

    @ApiOperation("获取平台商品数据")
    @PostMapping("/query")
    public MultiResponse<PlatformItemOpenVO> query(
            @Validated @RequestBody PlatformItemOpenQuery query) {

        return MultiResponse.of(platformItemOpenService.openListQuery(query));
    }
}
