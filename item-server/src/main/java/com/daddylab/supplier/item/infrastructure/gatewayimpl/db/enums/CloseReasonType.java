package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> up
 * @date 2024年06月11日 4:25 PM
 */
@Getter
@AllArgsConstructor
public enum CloseReasonType implements IIntegerEnum {

    /**
     *
     */
    NONE(0, "无"),
    ORDER_REFUND(1, "订单存在退款"),
    ORDER_REJECT(2, "订单被驳回"),
    TRANS_FINISH(3, "已送达"),
    LOST(4, "疑似丢件"),
    CALLBACK_TIMEOUT(5, "超时无轨迹回调"),
    SUBSCRIBE_FAIL(6, "订阅失败"),
    SUBSCRIBE_NULL(6, "订阅为空"),
    DIFFICULT(7,"疑难件"),
    ORDER_DELETED(8, "订单被删除"),
    ORDER_FREEZED(9, "订单被冻结"),
    RECOVER(10, "从异常状态中恢复"),
    SF(11, "顺丰物流不做跟踪"),
    WAREHOUSE_FILTER(12, "过滤仓库"),
    ORDER_FINISHED(13, "订单已完成"),
    HANDLE_TIMEOUT(14, "超时未处理"),
    ORDER_STATUS(15, "订单状态无需跟踪"),
    MANUAL_CLOSE(16, "手动关闭"),
    ;


    @EnumValue
    private final Integer value;

    private final String desc;


}
