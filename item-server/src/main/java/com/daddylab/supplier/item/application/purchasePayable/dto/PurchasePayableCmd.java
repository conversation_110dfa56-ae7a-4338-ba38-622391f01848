package com.daddylab.supplier.item.application.purchasePayable.dto;

import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;


@Data
@ApiModel("采购应付单创建")
public class PurchasePayableCmd implements Serializable {

    private static final long serialVersionUID = 3391889997076231560L;

    /**
     *  应付类型。 {@link PurchaseTypeEnum}
     */
    private Integer type;

    /**
     * 关联单据(指采购入库单ID/采退出库单ID)
     */
    private Long relatedOrderId;

    /**
     * 关联的入库单/出库单/其他应付的采购员
     */
    private Long buyerId;

    /**
     * 创建人
     */
    private Long createdUid;

}
