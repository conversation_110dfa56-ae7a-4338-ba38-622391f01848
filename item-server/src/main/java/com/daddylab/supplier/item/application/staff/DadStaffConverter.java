package com.daddylab.supplier.item.application.staff;

import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DadStaff;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Class  DadStaffConverter
 *
 * @Date 2022/6/1上午10:23
 * <AUTHOR>
 */
@Mapper
public interface DadStaffConverter {
    DadStaffConverter INSTANCE = Mappers.getMapper(DadStaffConverter.class);

    /**
     * model转vo
     *
     * @param dadStaff
     * @return
     */
    @Mappings(
            @Mapping(source = "uid", target = "userId")
    )
    public DadStaffVO dadStaffToVo(DadStaff dadStaff);

    /**
     * model转vo
     *
     * @param dadStaffList
     * @return
     */
    public List<DadStaffVO> dadStaffListToVo(List<DadStaff> dadStaffList);

}
