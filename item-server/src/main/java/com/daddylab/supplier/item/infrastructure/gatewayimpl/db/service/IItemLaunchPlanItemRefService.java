package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchPlanItemRef;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemLaunchPlanItemRefMapper;

import java.util.List;

/**
 * <p>
 * 商品上新计划和商品的关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */
public interface IItemLaunchPlanItemRefService extends IDaddylabServicePlus<ItemLaunchPlanItemRef, ItemLaunchPlanItemRefMapper> {
    /**
     * 查询（by planId）
     *
     * @param planId 上新计划 ID
     * @return List<ItemLaunchPlanItemRef>
     */
    List<ItemLaunchPlanItemRef> selectListByPlanId(Long planId);

    /**
     * 批量查询（by ids）
     *
     * @param ids 主键集合
     * @return List<ItemLaunchPlanItemRef>
     */
    List<ItemLaunchPlanItemRef> selectBatchByIds(List<Long> ids);

    /**
     * 删除
     *
     * @param id 主键
     */
    void deleteById(Long id);

    /**
     * 获取上新计划关联的商品中已上架的商品数量
     *
     * @param planId 上新计划 ID
     * @return long
     */
    Long getLinkItemOnNum(Long planId);

    /**
     * 获取商品和上新计划关联关系
     *
     * @param itemId
     * @return
     */
    ItemLaunchPlanItemRef getItemLaunchPlanItemRef(Long itemId);

    /**
     * 获取商品数量
     *
     * @param planId
     * @return
     */
    Integer getItemNum(Long planId);
}
