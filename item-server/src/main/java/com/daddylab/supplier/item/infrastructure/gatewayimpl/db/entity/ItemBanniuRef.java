package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemBanniuRefType;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 班牛数据关联记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemBanniuRef implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /**
     * 是否逻辑删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Integer deletedAt;

    /**
     * 班牛ID
     */
    private String banniuId;

    /**
     * 类型 1后端商品 2组合装商品
     */
    private ItemBanniuRefType type;

    /**
     * 商品ID
     */
    private String firstId;

    /**
     * 商品ID（二级ID）
     */
    private String secondId;


}
