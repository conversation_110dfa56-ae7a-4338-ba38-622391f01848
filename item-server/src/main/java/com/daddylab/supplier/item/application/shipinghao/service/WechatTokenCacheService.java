package com.daddylab.supplier.item.application.shipinghao.service;

import com.daddylab.supplier.item.application.shipinghao.dto.WechatAccessTokenDto;

/**
 * 微信Token缓存服务接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface WechatTokenCacheService {
    
    /**
     * 获取缓存的访问令牌
     * 
     * @return 访问令牌信息，如果缓存不存在或已过期则返回null
     */
    WechatAccessTokenDto getCachedAccessToken();
    
    /**
     * 缓存访问令牌
     * 
     * @param accessToken 访问令牌信息
     */
    void cacheAccessToken(WechatAccessTokenDto accessToken);
    
    /**
     * 清除缓存的访问令牌
     */
    void clearCachedAccessToken();
    
    /**
     * 检查访问令牌是否有效
     * 
     * @return true表示有效，false表示无效或不存在
     */
    boolean isAccessTokenValid();
    
    /**
     * 获取访问令牌字符串（如果缓存有效）
     * 
     * @return 访问令牌字符串，如果无效则返回null
     */
    String getValidAccessToken();
} 