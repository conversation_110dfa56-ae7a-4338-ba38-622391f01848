package com.daddylab.supplier.item.application.criticalPush;

import cn.hutool.core.lang.func.Func;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQConsumerBase;
import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQConsumerGroup;
import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQTopic;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import java.lang.reflect.InvocationTargetException;
import javax.annotation.Nullable;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/2/18
 */
@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = MQConsumerGroup.CRITICAL_PUSH, topic = MQTopic.CRITICAL_PUSH, consumeThreadMax = 2)
public class CriticalPushConsumer extends RocketMQConsumerBase<CriticalPushMsg> implements
        ApplicationContextAware {

    private final CriticalPushManager criticalPushManager;
    ApplicationContext applicationContext;

    public CriticalPushConsumer(RedissonClient redissonClient,
            StandardEnvironment environment,
            RefreshConfig refreshConfig,
            @Qualifier("CriticalPushManagerImpl")
                    CriticalPushManager criticalPushManager
    ) {
        super(redissonClient, environment, refreshConfig);
        this.criticalPushManager = criticalPushManager;
    }

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext)
            throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    protected void handle(MessageExt msg, @Nullable CriticalPushMsg body) {
        if (body == null) {
            log.error("消息体为空 msg:{}", msg);
            return;
        }
        criticalPushManager.push(body.getSourceType(), body.getSourceId(), body.getTargetType(),
                (VarargsFunctionCallback<Object, Object>) delegate -> {
                    delegate.call((Func<Object, Object>) parameters -> {
                        try {
                            PushManagerAspect.nextCallRealMethod();
                            return body.getMethodInvocationDescriptor()
                                    .invoke(applicationContext);
                        } catch (ClassNotFoundException | NoSuchMethodException | InstantiationException | IllegalAccessException e) {
                            throw new RuntimeException("目标方法反射异常:" + JsonUtil.toJson(body), e);
                        } catch (InvocationTargetException e) {
                            throw new RuntimeException(
                                    "目标方法执行异常:" + convertInvocationTargetException(e),
                                    e.getTargetException());
                        }
                    }, body.getMethodInvocationDescriptor().getParsedInvocationArgs());
                }, body.getPushId());

    }

    private String convertInvocationTargetException(InvocationTargetException e) {
        final Throwable targetException = e.getTargetException();
        if (targetException != null && StrUtil.isNotBlank(targetException.getMessage())) {
            return targetException.getMessage();
        }
        return "";
    }
}
