package com.daddylab.supplier.item.application.drawer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.ReUtil;
import com.daddylab.supplier.item.application.drawer.ItemDrawerImportMallGateway.Image;
import com.daddylab.supplier.item.common.error.HandleException;
import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerImageTypeEnum;
import com.daddylab.supplier.item.infrastructure.utils.LogUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.event.Level;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/9/26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ItemDrawerImportMallServiceImpl {

    private final ItemDrawerImportMallGateway itemDrawerImportMallGateway;

    public void importItemImageAndVideo(InputStream inputStream) {
        final CsvReadConfig config = CsvReadConfig.defaultConfig().setContainsHeader(true);
        final CsvReader reader = CsvUtil.getReader(config);
        final ObjectMapper objectMapper = new ObjectMapper();

        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        log.info("开始导入商品主图、主图视频");
        reader.read(new InputStreamReader(inputStream), row -> {
            //id	item_no	image	description_images	video
            final String itemCode = unwrapValue(row.get(1));
            final String imageStr = unwrapValue(row.get(3));
            final String videoStr = unwrapValue(row.get(4));
            if (StringUtil.isBlank(itemCode)) {
                return;
            }
            try {
                final List<Long> itemIds = itemDrawerImportMallGateway.getItemIds(itemCode);
                for (Long itemId : itemIds) {
                    if (!itemDrawerImportMallGateway.isMatchItem(itemCode, itemId)) {
                        log.warn("商品不在处理范围内:{}", itemCode);
                        return;
                    }
                    final Long itemDrawerId = itemDrawerImportMallGateway.getItemDrawerId(itemId)
                            .orElseThrow(() -> new HandleException("商品抽屉不存在", Level.WARN));
                    if (itemDrawerImportMallGateway.hasItemDrawerImage(itemDrawerId,
                            ItemDrawerImageTypeEnum.ITEM)) {
                        log.warn("商品已上传过抽屉图片 itemCode:{} itemId:{}", itemCode, itemId);
                        continue;
                    }
                    try {
                        List<ItemDrawerImportMallGateway.Image> imageList = Lists.newArrayList();
                        final JsonNode imageJsonNode = objectMapper.readTree(imageStr);
                        if (imageJsonNode.isArray()) {
                            for (JsonNode jsonNode : imageJsonNode) {
                                final String url = Optional.ofNullable(jsonNode.get("url"))
                                        .map(JsonNode::asText).orElse(null);
                                final String fileName = Optional.ofNullable(
                                                jsonNode.get("fileName"))
                                        .map(JsonNode::asText).orElse(null);
                                if (StringUtil.isBlank(url)) {
                                    continue;
                                }
                                imageList.add(ItemDrawerImportMallGateway.Image.of(url, fileName));
                            }
                        }
                        if (imageList.isEmpty()) {
                            log.warn("商品无可导入的主图 itemCode:{} itemId:{}", itemCode, itemId);
                        } else {
                            log.info("商品导入主图 itemCode:{} itemId:{} images:{}",
                                    itemCode, itemId,
                                    imageList.stream().map(Image::getUrl)
                                            .collect(Collectors.toList())
                            );
                            itemDrawerImportMallGateway.saveBatchItemDrawerMainImages(itemDrawerId,
                                    imageList);
                        }
                    } catch (JsonProcessingException e) {
                        log.warn("图片串为不合法的JSON:{} itemCode={} json={}", e.getMessage(), itemCode,
                                imageStr);
                    }
                    try {
                        final JsonNode jsonNode = objectMapper.readTree(videoStr);
                        if (jsonNode.isObject() && !jsonNode.isEmpty()) {
                            final String url = Optional.ofNullable(jsonNode.get("url"))
                                    .map(JsonNode::asText).orElse(null);
                            final String fileName = Optional.ofNullable(jsonNode.get("fileName"))
                                    .map(JsonNode::asText).orElse(null);
                            final String cover = Optional.ofNullable(jsonNode.get("cover"))
                                    .map(JsonNode::asText).orElse(null);
                            if (StringUtil.isNotBlank(url)) {
                                log.info("商品导入视频 itemCode:{} itemId:{} url:{} cover:{}", itemCode,
                                        itemId, url, cover);
                                itemDrawerImportMallGateway.saveBatchItemDrawerMainVideo(
                                        itemDrawerId, url,
                                        fileName, cover);
                            } else {
                                log.warn("商品视频URL为空 itemCode:{} itemId:{}", itemCode, itemId);
                            }
                        } else {
                            log.warn("商品无可导入的视频 itemCode:{} itemId:{}", itemCode, itemId);
                        }
                    } catch (JsonProcessingException e) {
                        log.warn("视频字符串为不合法的JSON:{} itemCode:{} json={}", e.getMessage(), itemCode,
                                videoStr);
                    }
                }
            } catch (Throwable e) {
                if (e instanceof HandleException) {
                    final HandleException he = (HandleException) e;
                    LogUtil.log(log, he.getLevel(), "处理异常:{}:{}", he.getMessage(), he.getData());
                } else {
                    log.error("处理数据导入遇到未知异常:{} itemCode:{} row:{}", e.getMessage(), itemCode, row,
                            e);
                    throw e;
                }
            }

        });

        stopWatch.stop();
        log.info("导入商品主图、主图视频完成 耗时:{}ms", stopWatch.getTotalTimeMillis());
    }

    public void importVideoCover(InputStream inputStream) {
        final CsvReadConfig config = CsvReadConfig.defaultConfig().setContainsHeader(true);
        final CsvReader reader = CsvUtil.getReader(config);
        final ObjectMapper objectMapper = new ObjectMapper();

        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        log.info("开始导入主图视频封面");
        reader.read(new InputStreamReader(inputStream), row -> {
            //id	item_no	image	description_images	video
            final String itemCode = unwrapValue(row.get(1));
            final String imageStr = unwrapValue(row.get(3));
            final String videoStr = unwrapValue(row.get(4));
            if (StringUtil.isBlank(itemCode)) {
                return;
            }
            try {
                final List<Long> itemIds = itemDrawerImportMallGateway.getItemIds(itemCode);
                for (Long itemId : itemIds) {
                    if (!itemDrawerImportMallGateway.isMatchItem(itemCode, itemId)) {
                        log.warn("商品不在处理范围内:{}", itemCode);
                        return;
                    }
                    final Long itemDrawerId = itemDrawerImportMallGateway.getItemDrawerId(itemId)
                            .orElseThrow(() -> new HandleException("商品抽屉不存在", Level.WARN));
                    try {
                        final JsonNode jsonNode = objectMapper.readTree(videoStr);
                        if (jsonNode.isObject() && !jsonNode.isEmpty()) {
                            final String url = Optional.ofNullable(jsonNode.get("url"))
                                    .map(JsonNode::asText).orElse(null);
                            final String fileName = Optional.ofNullable(jsonNode.get("fileName"))
                                    .map(JsonNode::asText).orElse(null);
                            final String cover = Optional.ofNullable(jsonNode.get("cover"))
                                    .map(JsonNode::asText).orElse(null);
                            if (StringUtil.isNotBlank(url)) {
                                log.info("商品更新视频封面 itemCode:{} itemId:{} url:{} cover:{}", itemCode,
                                        itemId, url, cover);
                                itemDrawerImportMallGateway.updateItemDrawerVideoCover(
                                        itemDrawerId, url, cover);
                            }
                        }
                    } catch (JsonProcessingException e) {
                        log.warn("视频字符串为不合法的JSON:{} itemCode:{} json={}", e.getMessage(), itemCode,
                                videoStr);
                    }
                }
            } catch (Throwable e) {
                if (e instanceof HandleException) {
                    final HandleException he = (HandleException) e;
                    LogUtil.log(log, he.getLevel(), "处理异常:{}:{}", he.getMessage(), he.getData());
                } else {
                    log.error("处理数据导入遇到未知异常:{} itemCode:{} row:{}", e.getMessage(), itemCode, row,
                            e);
                    throw e;
                }
            }

        });

        stopWatch.stop();
        log.info("导入商品视频封面完成 耗时:{}ms", stopWatch.getTotalTimeMillis());
    }

    private String unwrapValue(String value) {
        if (StringUtil.isBlank(value)) {
            return "";
        }
        return StringEscapeUtils.unescapeJava(value);
    }

    public void importItemDetailImage(InputStream inputStream) {

        final CsvReadConfig config = CsvReadConfig.defaultConfig().setContainsHeader(true);
        final CsvReader reader = CsvUtil.getReader(config);

        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        log.info("开始导入商品详情图片");
        reader.read(new InputStreamReader(inputStream), row -> {
            //item_id item_no base_detail
            final String itemCode = unwrapValue(row.get(1));
            final String baseDetail = unwrapValue(row.get(2));
            if (StringUtil.isBlank(itemCode)) {
                return;
            }
            if (StringUtil.isBlank(baseDetail)) {
                return;
            }
            try {
                final List<Long> itemIds = itemDrawerImportMallGateway.getItemIds(itemCode);
                if (CollectionUtil.isEmpty(itemIds)) {
                    log.warn("商品编码未匹配到商品 itemCode:{}", itemCode);
                    return;
                }
                for (Long itemId : itemIds) {
                    if (!itemDrawerImportMallGateway.isMatchItem(itemCode, itemId)) {
                        log.warn("商品不在处理范围内 itemCode:{} itemId:{}", itemCode, itemId);
                        continue;
                    }
                    final Long itemDrawerId = itemDrawerImportMallGateway.getItemDrawerId(itemId)
                            .orElseThrow(() -> new HandleException("商品抽屉不存在", Level.WARN));
                    if (itemDrawerImportMallGateway.hasItemDrawerImage(itemDrawerId,
                            ItemDrawerImageTypeEnum.DETAIL)) {
                        log.warn("商品已上传过抽屉图片 itemCode:{} itemId:{}", itemCode, itemId);
                        continue;
                    }
                    final List<String> imgSrcList = ReUtil.findAll("<img\\s+src=\"(.+?)\"",
                            baseDetail,
                            1);
                    if (imgSrcList.isEmpty()) {
                        log.warn("解析详情图片为空 itemCode:{} itemId:{}", itemCode, itemId);
                    } else {
                        List<ItemDrawerImportMallGateway.Image> imgList = Lists.newArrayList();
                        for (String url : imgSrcList) {
                            imgList.add(ItemDrawerImportMallGateway.Image.of(url));
                        }
                        log.info("商品导入详情图片 itemCode:{} itemId:{} images:{}", itemCode, itemId,
                                imgSrcList);
                        itemDrawerImportMallGateway.saveBatchItemDrawerDetailImages(itemDrawerId,
                                imgList);
                    }
                }
            } catch (Throwable e) {
                if (e instanceof HandleException) {
                    final HandleException he = (HandleException) e;
                    LogUtil.log(log, he.getLevel(), "处理异常:{}:{}", he.getMessage(), he.getData());
                } else {
                    log.error("处理数据导入遇到未知异常:{} itemCode:{}", e.getMessage(), itemCode, e);
                    throw e;
                }
            }
        });

        stopWatch.stop();
        log.info("导入商品详情图片结束 耗时:{}ms", stopWatch.getTotalTimeMillis());
    }


    public void importAttrImage(InputStream inputStream) {

        final CsvReadConfig config = CsvReadConfig.defaultConfig().setContainsHeader(true);
        final CsvReader reader = CsvUtil.getReader(config);
        final ObjectMapper objectMapper = new ObjectMapper();

        //item_no	name	spec_value	spec_image
        Function<CsvRow, String> getItemCode = row -> unwrapValue(row.get(0));
        Function<CsvRow, String> getSpecName = row -> unwrapValue(row.get(1));
        Function<CsvRow, String> getSpecValue = row -> unwrapValue(row.get(2));
        Function<CsvRow, String> getSpecImage = row -> unwrapValue(row.get(3));

        final List<CsvRow> allRows = reader.read(new InputStreamReader(inputStream))
                .getRows();
        final Map<String, List<CsvRow>> dataGroup = allRows.stream()
                .filter(v -> StringUtil.isNotBlank(getItemCode.apply(v)))
                .collect(Collectors.groupingBy(getItemCode));

        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        log.info("开始导入属性图片");
        for (Entry<String, List<CsvRow>> itemGroupEntry : dataGroup.entrySet()) {
            final String itemCode = itemGroupEntry.getKey();
            try {
                final List<CsvRow> rows = itemGroupEntry.getValue();
                final List<Long> itemIds = itemDrawerImportMallGateway.getItemIds(itemCode);
                if (CollectionUtil.isEmpty(itemIds)) {
                    log.warn("商品编码未匹配到商品 itemCode:{}", itemCode);
                    continue;
                }
                for (Long itemId : itemIds) {
                    if (!itemDrawerImportMallGateway.isMatchItem(itemCode, itemId)) {
                        log.warn("商品不在处理范围内 itemCode:{}", itemCode);
                        continue;
                    }
                    final Long itemDrawerId = itemDrawerImportMallGateway.getItemDrawerId(itemId)
                            .orElseThrow(() -> new HandleException("商品抽屉不存在", Level.WARN));
                    if (itemDrawerImportMallGateway.hasItemDrawerImage(itemDrawerId,
                            ItemDrawerImageTypeEnum.ATTR)) {
                        log.warn("商品已上传过抽屉图片 itemCode:{} itemId:{}", itemCode, itemId);
                        continue;
                    }
                    List<ItemDrawerImportMallGateway.Image> images = Lists.newArrayList();
                    for (CsvRow row : rows) {
                        final String attrName = getSpecName.apply(row);
                        final String attrValue = getSpecValue.apply(row);
                        final String specImage = getSpecImage.apply(row);

                        final JsonNode imageJsonNode;
                        try {
                            imageJsonNode = objectMapper.readTree(specImage);
                        } catch (JsonProcessingException e) {
                            log.warn(
                                    "属性图片非合法JSON itemCode:{} itemId:{} specName:{} specValue:{} specImage:{}",
                                    itemCode, itemId, attrName, attrValue, specImage);
                            continue;
                        }
                        final String fileName = Optional.ofNullable(imageJsonNode.get("fileName"))
                                .map(JsonNode::asText).orElse(null);
                        final String url = Optional.ofNullable(imageJsonNode.get("url"))
                                .map(JsonNode::asText).orElse(null);
                        if (StringUtil.isBlank(url)) {
                            log.warn(
                                    "图片URL为空 itemCode:{} itemId:{} specName:{} specValue:{} specImage:{}",
                                    itemCode, itemId,
                                    attrName, attrValue, specImage);
                            continue;
                        }
                        final Optional<Long> mapAttrValueToIdOpt = itemDrawerImportMallGateway.mapAttrValueToId(
                                itemCode, attrName, attrValue);
                        if (!mapAttrValueToIdOpt.isPresent()) {
                            log.warn(
                                    "未匹配到属性 itemCode:{} itemId:{} specName:{} specValue:{} specImage:{}",
                                    itemCode, itemId,
                                    attrName, attrValue, specImage);
                            continue;
                        }
                        images.add(ItemDrawerImportMallGateway.Image.of(url, fileName,
                                mapAttrValueToIdOpt.get()));
                    }
                    if (images.isEmpty()) {
                        log.warn("可导入的商品图片列表为空 itemCode:{} itemId:{}", itemCode, itemId);
                    } else {
                        log.info("商品导入属性图片 itemCode:{} itemId:{} images:{}", itemCode, itemId,
                                images);
                        itemDrawerImportMallGateway.saveBatchItemDrawerAttrImages(itemDrawerId,
                                images);
                    }
                }
            } catch (Throwable e) {
                if (e instanceof HandleException) {
                    final HandleException he = (HandleException) e;
                    LogUtil.log(log, he.getLevel(), "处理异常:{}:{}", he.getMessage(), he.getData());
                } else {
                    log.error("处理数据导入遇到未知异常:{} itemCode:{}", e.getMessage(), itemCode, e);
                    throw e;
                }
            }

        }

        stopWatch.stop();
        log.info("导入属性图片完成 耗时:{}ms", stopWatch.getTotalTimeMillis());
    }

}
