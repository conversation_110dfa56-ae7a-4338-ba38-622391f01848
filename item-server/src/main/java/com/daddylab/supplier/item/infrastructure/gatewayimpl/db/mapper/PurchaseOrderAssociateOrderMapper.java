package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrderAssociateOrder;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;

/**
 * <p>
 * 采购订单关联的系统订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
public interface PurchaseOrderAssociateOrderMapper extends DaddyBaseMapper<PurchaseOrderAssociateOrder> {

}
