package com.daddylab.supplier.item.infrastructure.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class DateUtil {

  public static final String DEFAULT_FORMAT = "yyyy-MM-dd HH:mm:ss";

  public static final String DEFAULT_DATE = "yyyyMMdd";

  public static final String DEFAULT_TIME = "HH:mm";
  private static final Map<TimeUnit, String> timeUnitAbbrevs;

  /** 日期模式缓存 */
  static LoadingCache<String, DateTimeFormatter> dateTimeFormatters =
      Caffeine.newBuilder().build(DateTimeFormatter::ofPattern);

  static {
    timeUnitAbbrevs =
        ImmutableMap.<TimeUnit, String>builder()
            .put(TimeUnit.NANOSECONDS, "ns")
            .put(TimeUnit.MICROSECONDS, "μs")
            .put(TimeUnit.MILLISECONDS, "ms")
            .put(TimeUnit.SECONDS, "s")
            .put(TimeUnit.MINUTES, "m")
            .put(TimeUnit.HOURS, "h")
            .put(TimeUnit.DAYS, "d")
            .build();
  }

  /** 当前时间戳（毫秒） */
  public static Long currentTimeMillis() {
    return System.currentTimeMillis();
  }

  /** 当前时间戳（秒） */
  public static Long currentTime() {
    return System.currentTimeMillis() / 1000;
  }

  /**
   * 将特定格式的日期字符串解析为时间戳
   *
   * @param date 特定格式的日期字符串
   * @param pattern 日期格式
   * @see DatePattern
   */
  public static Long parseTime(String date, String pattern) {
    return toTime(parse(date, pattern));
  }

  /**
   * 将标准格式（yyyy-MM-dd HH:mm:ss）的日期字符串解析为时间戳
   *
   * @param date 特定格式的日期字符串
   */
  public static Long parseTime(String date) {
    if (StringUtil.isNotBlank(date)) {
      return parseTime(date.trim(), DatePattern.NORM_DATETIME_PATTERN);
    }
    return null;
  }

  /** 解析日期字符串 */
  public static LocalDateTime parse(String date) {
    return parse(date, DatePattern.NORM_DATETIME_PATTERN);
  }

  /**
   * 日期格式兼容性解析，解析成功转为时间戳
   *
   * @param time 日期字符串
   */
  public static Long parseToTimestampCompatibility(String time) {
    return toEpochSecond(parseCompatibility(time));
  }

  /**
   * 日期格式兼容性解析
   *
   * @param time 日期字符串
   */
  public static LocalDateTime parseCompatibility(String time) {
    if (StrUtil.isBlank(time) || "0".equals(time)) {
      return null;
    } else if (ReUtil.isMatch("\\d{10}|\\d{13}", time)) {
      return parseTimeStamp(time);
    } else if (ReUtil.isMatch("\\d{6}", time)) {
      return parse(time, DatePattern.PURE_TIME_PATTERN);
    } else if (ReUtil.isMatch("\\d{8}", time)) {
      return parse(time, DatePattern.PURE_DATE_PATTERN);
    } else if (ReUtil.isMatch("\\d{14}", time)) {
      return parse(time, DatePattern.PURE_DATETIME_PATTERN);
    } else if (ReUtil.isMatch("\\d{17}", time)) {
      return parse(time, DatePattern.PURE_DATETIME_MS_PATTERN);
    } else if (ReUtil.isMatch("\\d{4}\\.\\d{2}\\.\\d{2}", time)) {
      return parse(time, "yyyy.MM.dd");
    } else if (ReUtil.isMatch("\\d{4}\\年\\d{2}\\月\\d{2}日", time)) {
      return parse(time, "yyyy年MM月dd日");
    } else if (ReUtil.isMatch("\\d{4}\\年\\d{1,2}\\月\\d{1,2}日", time)) {
      return parse(time, "yyyy年M月d日");
    } else if (ReUtil.isMatch("\\d{4}\\.\\d{1,2}\\.\\d{1,2}", time)) {
      return parse(time, "yyyy.M.d");
    } else if (ReUtil.isMatch("\\d{4}/\\d{2}/\\d{2}", time)) {
      return parse(time, "yyyy/MM/dd");
    } else if (ReUtil.isMatch("\\d{4}/\\d{1,2}/\\d{1,2}", time)) {
      return parse(time, "yyyy/M/d");
    } else if (ReUtil.isMatch("\\d{4}\\.\\d{1,2}\\.\\d{1,2} \\d{1,2}:\\d{1,2}:\\d{1,2}", time)) {
      return parse(time, "yyyy.M.d H:m:s");
    } else if (ReUtil.isMatch("\\d{4}\\.\\d{1,2}\\.\\d{1,2} \\d{1,2}点\\d{1,2}分\\d{1,2}秒", time)) {
      return parse(time, "yyyy.M.d H点m分s秒");
    } else if (ReUtil.isMatch("\\d{4}/\\d{1,2}/\\d{1,2} \\d{1,2}:\\d{1,2}:\\d{1,2}", time)) {
      return parse(time, "yyyy/M/d H:m:s");
    } else if (ReUtil.isMatch("\\d{4}/\\d{1,2}/\\d{1,2} \\d{1,2}点\\d{1,2}分\\d{1,2}秒", time)) {
      return parse(time, "yyyy/M/d H点m分s秒");
    } else if (ReUtil.isMatch("\\d{4}-\\d{1,2}-\\d{1,2} \\d{1,2}:\\d{1,2}", time)) {
      return parse(time, "yyyy-M-d H:m");
    } else if (ReUtil.isMatch("\\d{4}-\\d{2}-\\d{2}", time)) {
      return parse(time, DatePattern.NORM_DATE_PATTERN);
    } else if (ReUtil.isMatch("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}", time)) {
      return parse(time, DatePattern.NORM_DATETIME_PATTERN);
    } else if (ReUtil.isMatch("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}", time)) {
      return parse(time, DatePattern.NORM_DATETIME_MINUTE_PATTERN);
    } else if (ReUtil.isMatch("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\\.\\d{3}", time)) {
      return parse(time, DatePattern.NORM_DATETIME_MS_PATTERN);
    } else if (ReUtil.isMatch("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2},\\d{3}", time)) {
      return parse(time, DatePattern.ISO8601_PATTERN);
    } else if (ReUtil.isMatch(
        "[a-zA-Z]{3} [a-zA-Z]{3} \\d{2} \\d{2}:\\d{2}:\\d{2} CST \\d{4}", time)) {
      return parse(time, DatePattern.JDK_DATETIME_PATTERN);
    } else if (ReUtil.isMatch("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}", time)) {
      return parse(time, DatePattern.UTC_SIMPLE_PATTERN);
    } else if (ReUtil.isMatch("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{3}", time)) {
      return parse(time, DatePattern.UTC_SIMPLE_MS_PATTERN);
    } else if (ReUtil.isMatch("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z", time)) {
      return parse(time, DatePattern.UTC_PATTERN);
    } else if (ReUtil.isMatch("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{3}Z", time)) {
      return parse(time, DatePattern.UTC_MS_PATTERN);
    } else if (ReUtil.isMatch("\\d+", time)) {
      return parseTimeStamp(time);
    } else {
      throw new UnsupportedOperationException("未兼容的日期格式:" + time);
    }
  }

  public static void main(String[] args) {
    System.out.println(targetDayZeroTime(1706770852L));
  }

  public static LocalDateTime parse(String date, String pattern) {
    if (pattern == null) {
      pattern = DatePattern.NORM_DATETIME_PATTERN;
    }
    return cn.hutool.core.date.DateUtil.parseLocalDateTime(date, pattern);
  }

  /**
   * @return 返回表示当前时间的LocalDateTime
   */
  public static LocalDateTime now() {
    return LocalDateTime.now();
  }

  /**
   * 获取当前的时间 s
   *
   * @return java.lang.Long
   */
  public static Long getNowSecond() {
    return System.currentTimeMillis() / 1000;
  }

  /**
   * @return 返回表示当前时间的LocalDateTime
   */
  public static LocalDate today() {
    return LocalDate.now();
  }

  /**
   * @return 返回表示当前时间的LocalDateTime
   */
  public static LocalDateTime todayZero() {
    return LocalDate.now().atStartOfDay();
  }

  /**
   * 日期转换时间戳（10位）
   *
   * @param date
   * @return
   */
  public static long getTimestamp(String date, String format) {
    if (Objects.isNull(date)) {
      return 0;
    }
    LocalDateTime parse = LocalDateTime.parse(date, DateTimeFormatter.ofPattern(format));
    return LocalDateTime.from(parse).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
        / 1000;
  }

  /** 获取今天零点的时间戳 */
  public static long todayZeroTime() {
    return toTime(today().atStartOfDay());
  }

  public static long targetDayZeroTime(Long timeStamp) {
    LocalDateTime localDateTime = parseTimeStamp(timeStamp).toLocalDate().atStartOfDay();
    return toTime(localDateTime);
  }

  /** 获取今天日期的字符串表示 默认格式：yyyy-MM-dd */
  public static String formatToday() {
    return formatNow(DatePattern.NORM_DATE_PATTERN);
  }

  /** 获取今天日期加时间的字符串表示 yyyy-MM-dd HH:mm:ss */
  public static String formatNow() {
    return format(now(), DatePattern.NORM_DATETIME_PATTERN);
  }

  /** 获取今天时间的字符串表示 */
  public static String formatNow(String pattern) {
    return format(now(), pattern);
  }

  /**
   * @return 返回给定时间当天的最后一秒
   */
  public static LocalDateTime toLastSecondOfDay(LocalDateTime dateTime) {
    return toZero(dateTime).plusDays(1).minusSeconds(1);
  }

  /**
   * @return 返回给定时间的零时
   */
  public static LocalDateTime toZero(LocalDateTime dateTime) {
    return dateTime.withHour(0).withMinute(0).withSecond(0).withNano(0);
  }

  /**
   * @return 返回当月第一天
   */
  public static LocalDateTime getFirstDayOfMonth() {
    return getFirstDayOfMonth(LocalDateTime.now());
  }

  /**
   * @param dateTime 给定一个时间点
   * @return 返回指定时间点的当月一号
   */
  public static LocalDateTime getFirstDayOfMonth(LocalDateTime dateTime) {
    return toZero(dateTime.with(TemporalAdjusters.firstDayOfMonth()));
  }

  /**
   * @return 返回当月最后一天
   */
  public static LocalDateTime getLastDayOfMonth() {
    return getLastDayOfMonth(LocalDateTime.now());
  }

  /**
   * @param dateTime 给定一个时间点
   * @return 返回当月最后一天
   */
  public static LocalDateTime getLastDayOfMonth(LocalDateTime dateTime) {
    return toZero(dateTime.with(TemporalAdjusters.lastDayOfMonth()));
  }

  /**
   * alias for toEpochSecond(java.time.LocalDateTime)
   *
   * @return 返回给定LocalDateTime对应的时间戳
   */
  public static long toTime(LocalDateTime dateTime) {
    return toEpochSecond(dateTime);
  }

  public static long toTime(LocalDate date) {
    return toEpochSecond(date.atStartOfDay());
  }

  public static long toTime(Date date) {
    return toEpochSecond(date);
  }

  /**
   * @return 返回给定LocalDateTime对应的时间戳
   */
  public static long toEpochSecond(LocalDateTime dateTime) {
    if (Objects.isNull(dateTime)) {
      return 0;
    }
    return dateTime.atZone(ZoneId.systemDefault()).toEpochSecond();
  }

  /**
   * @param dateTime 时间对象
   * @param defaultValue 当给定时间对象是无效值的时候，返回默认值
   * @return 返回给定LocalDateTime对应的时间戳
   */
  public static long toEpochSecond(LocalDateTime dateTime, long defaultValue) {
    if (Objects.isNull(dateTime)) {
      return defaultValue;
    }
    return dateTime.atZone(ZoneId.systemDefault()).toEpochSecond();
  }

  /**
   * @return 返回给定LocalDateTime对应的时间戳
   */
  public static long toEpochSecond(Date date) {
    if (Objects.isNull(date)) {
      return 0;
    }
    return date.toInstant().getEpochSecond();
  }

  /**
   * @return 返回当月第一天的时间戳
   */
  public static long getFirstDayTimeOfMonth() {
    return toTime(getFirstDayOfMonth());
  }

  /**
   * @return 返回当月一号00:00:00的时间戳
   */
  public static long getLastDayTimeOfMonth() {
    return toTime(getLastDayOfMonth());
  }

  /**
   * @return 返回当月最后一天23:59:59的时间戳
   */
  public static long getLastSecondTimeOfLastDayOfMonth() {
    return toTime(toLastSecondOfDay(getLastDayOfMonth()));
  }

  /**
   * @return 当前日期往前推1个月
   */
  public static LocalDateTime lastMonth() {
    return now().minusMonths(1);
  }

  /**
   * @return 当前日期往前推1个月
   */
  public static LocalDate lastMonthDate() {
    return today().minusMonths(1);
  }

  /**
   * @return 当前日期往前推1个月，根据给定格式返回日期表达
   */
  public static String lastMonthDate(String pattern) {
    return formatDate(lastMonthDate(), pattern);
  }

  /** 时间戳转LocalDateTime */
  public static LocalDateTime toLocalDateTime(Long time) {
    if (time == null || time == 0) {
      return null;
    }
    return LocalDateTime.ofInstant(Instant.ofEpochSecond(time), ZoneId.systemDefault());
  }

  /**
   * 解析时间字符串转化为LocalDateTime（兼容毫秒时间戳和秒级时间戳）
   *
   * @param time 时间字符串
   */
  public static LocalDateTime toLocalDateTime(String time) {
    return toLocalDateTime(time, null);
  }

  /**
   * 解析时间字符串转化为LocalDateTime（兼容毫秒时间戳和秒级时间戳）
   *
   * @param time 时间字符串
   * @param pattern 日期格式
   */
  public static LocalDateTime toLocalDateTime(String time, String pattern) {
    if (time == null || StrUtil.isBlank(time)) {
      return null;
    }
    if (ReUtil.isMatch("\\d+", time)) {
      return parseTimeStamp(Long.parseLong(time));
    } else {
      return parse(time, pattern == null ? DatePattern.NORM_DATETIME_PATTERN : pattern);
    }
  }

  /** 毫秒时间戳转LocalDateTime */
  public static LocalDateTime millisToLocalDateTime(Long millis) {
    if (millis == null || millis == 0) {
      return null;
    }
    return LocalDateTime.ofInstant(Instant.ofEpochMilli(millis), ZoneId.systemDefault());
  }

  /** 毫秒时间戳转LocalDateTime */
  public static LocalDateTime millisToLocalDateTime(String millis) throws NumberFormatException {
    if (StringUtil.isBlank(millis)) {
      return null;
    }
    long millisLong = Long.parseLong(millis);
    return LocalDateTime.ofInstant(Instant.ofEpochMilli(millisLong), ZoneId.systemDefault());
  }

  /** 格式化时间 */
  public static String format(Date dateTime) {
    return cn.hutool.core.date.DateUtil.format(dateTime, DatePattern.NORM_DATETIME_PATTERN);
  }

  /** 格式化时间 */
  public static String format(Date dateTime, String pattern) {
    return cn.hutool.core.date.DateUtil.format(dateTime, pattern);
  }

  /** 格式化时间 */
  public static String format(LocalDateTime dateTime) {
    return format(dateTime, DatePattern.NORM_DATETIME_PATTERN);
  }

  /** 格式化日期 */
  public static String formatDate(LocalDate date) {
    return formatDate(date, DatePattern.NORM_DATE_PATTERN);
  }

  /** 格式化日期 */
  public static String formatDate(Long timestamp) {
    final LocalDateTime localDateTime = toLocalDateTime(timestamp);
    if (localDateTime == null) {
      return "";
    }
    return formatDate(localDateTime.toLocalDate(), DatePattern.NORM_DATE_PATTERN);
  }

  public static String formatDate(Date date) {
    return cn.hutool.core.date.DateUtil.format(date, DatePattern.NORM_DATE_PATTERN);
  }

  /** 格式化日期 */
  public static String formatDate(LocalDate date, String pattern) {
    if (date == null) {
      return null;
    }
    if (pattern == null) {
      pattern = DatePattern.NORM_DATETIME_PATTERN;
    }
    return date.format(Objects.requireNonNull(dateTimeFormatters.get(pattern)));
  }

  /**
   * 格式化时间
   *
   * @see DatePattern
   */
  public static String format(LocalDateTime dateTime, String pattern) {
    return cn.hutool.core.date.DateUtil.format(dateTime, pattern);
  }

  /** 格式化时间戳，默认格式化为yyyy-MM-dd HH:mm:ss */
  public static String format(Long time) {
    if (Objects.isNull(time) || time == 0) {
      return "";
    }
    return format(toLocalDateTime(time));
  }

  /**
   * 格式化时间
   *
   * @see DatePattern
   */
  public static String format(Long time, String pattern) {
    return format(toLocalDateTime(time), pattern);
  }

  /** 获取时间单位英文缩写 */
  public static String abbrev(TimeUnit timeUnit) {
    return timeUnitAbbrevs.get(timeUnit);
  }

  public static String parseTimeStamp(Long timeStamp, String pattern) {
    Date d;
    if (String.valueOf(timeStamp).length() == 13) {
      d = new Date(timeStamp);
    } else if (String.valueOf(timeStamp).length() == 10) {
      d = new Date(Long.parseLong(timeStamp + "000"));
    } else {
      return "";
    }
    SimpleDateFormat sf = new SimpleDateFormat(pattern);
    return sf.format(d);
  }

  public static LocalDateTime parseTimeStamp(String timeStamp) {
    if (timeStamp.length() == 10) {
      return LocalDateTime.ofInstant(
          Instant.ofEpochSecond(Long.parseLong(timeStamp)), ZoneId.systemDefault());
    } else if (timeStamp.length() == 13) {
      return LocalDateTime.ofInstant(
          Instant.ofEpochMilli(Long.parseLong(timeStamp)), ZoneId.systemDefault());
    } else {
      throw new IllegalStateException("illegal timeStamp");
    }
  }

  public static LocalDateTime parseTimeStamp(Long timeStamp) {
    if (Objects.isNull(timeStamp)) {
      throw new IllegalStateException("timeStamp cannot be empty");
    }

    String s = cn.hutool.core.util.NumberUtil.toStr(timeStamp);
    Instant instant;
    if (s.length() == 10) {
      instant = Instant.ofEpochSecond(timeStamp);
    } else if (s.length() == 13) {
      instant = Instant.ofEpochMilli(timeStamp);
    } else {
      throw new IllegalStateException("illegal timeStamp");
    }
    ZoneId zone = ZoneId.systemDefault();
    return LocalDateTime.ofInstant(instant, zone);
  }

  public static Instant toInstant(LocalDateTime localDateTime) {
    return localDateTime.atZone(ZoneId.systemDefault()).toInstant();
  }

  /**
   * 获取0点的时间戳
   *
   * @param tmp 10位
   * @return
   */
  public static Long getTimeStampOfZero(Long tmp) {
    Instant instant = Instant.ofEpochSecond(tmp);
    ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(instant, ZoneId.systemDefault());
    LocalDateTime localDateTime = zonedDateTime.toLocalDate().atStartOfDay();
    return localDateTime.atZone(ZoneId.systemDefault()).toEpochSecond();
  }

  /**
   * 获取系统采购单的采购日期。targetMonth月份的最后一天的0点。返回10位时间戳。
   *
   * @param targetMonth
   * @return
   */
  public static Long getSysPurchaseTime(String targetMonth) {
    LocalDateTime localDateTime = parse(targetMonth, "yyyyMM");
    LocalDateTime lastDayOfMonth = getLastDayOfMonth(localDateTime);
    return lastDayOfMonth.toInstant(ZoneOffset.of("+8")).getEpochSecond();
  }

  /**
   * 解析时间字符串然后转化为时间戳
   *
   * @param datetime 时间字符串
   * @return 时间戳
   */
  public static Long parseDateTime2timestamp(String datetime) {
    return toTime(parse(datetime));
  }

  /**
   * 时间转换处理。 demo: ["2023-01-01 00:00:00","2023-02-01 00:00:00","2023-03-01 00:00:00","2023-07-01
   * 00:00:00","2023-08-01 00:00:00","2023-10-01 00:00:00"] 转为
   * 2023-01~2023-03、2023-07~2023-08、2023-10
   *
   * @return
   */
  public static String polymerizationTime(List<LocalDateTime> startDateList) {
    String rr;
    // ------ 分割线 -----------
    if (CollUtil.isEmpty(startDateList)) {
      rr = "暂无数据";
    } else {
      if (startDateList.size() == 1) {
        rr = startDateList.get(0).format(DatePattern.NORM_MONTH_FORMATTER);
      } else {
        startDateList =
            startDateList.stream()
                .distinct()
                .sorted(
                    (o1, o2) -> {
                      if (o1.isBefore(o2)) {
                        return -1;
                      } else if (o1.isAfter(o2)) {
                        return 1;
                      } else {
                        return 0;
                      }
                    })
                .collect(Collectors.toList());
        List<List<LocalDateTime>> container = new LinkedList<>();
        LocalDateTime st = startDateList.get(0);
        List<LocalDateTime> oc = new ArrayList<>();
        oc.add(st);
        for (int i = 1; i < startDateList.size(); i++) {
          LocalDateTime md = st.plusMonths(1);
          LocalDateTime ind = startDateList.get(i);
          if (ind.isEqual(md)) {
            oc.add(ind);
            st = ind;
          } else {
            container.add(new ArrayList<>(oc));
            oc.clear();
            st = ind;
            oc.add(st);
          }
        }
        if (CollUtil.isNotEmpty(oc)) {
          container.add(oc);
        }
        Set<String> cycleList = new HashSet<>();
        container.forEach(
            ll -> {
              if (ll.size() > 1) {
                String f1 = ll.get(0).format(DatePattern.NORM_MONTH_FORMATTER);
                String f2 = ll.get(ll.size() - 1).format(DatePattern.NORM_MONTH_FORMATTER);
                cycleList.add(f1 + "~" + f2);
              } else {
                cycleList.add(ll.get(0).format(DatePattern.NORM_MONTH_FORMATTER));
              }
            });
        rr = CollUtil.join(cycleList, "、");
      }
    }
    return rr;
  }

  /**
   * 计算两个时间点之间的时间差
   *
   * @param source
   * @param beReduced
   * @param timeUnit
   * @return 整除之后的绝对值
   */
  public static Long calculateDifference(Long source, Long beReduced, TimeUnit timeUnit) {
    Instant instant1 = Instant.ofEpochSecond(source);
    Instant instant2 = Instant.ofEpochSecond(beReduced);
    Duration duration = Duration.between(instant1, instant2);
    switch (timeUnit) {
      case MINUTES:
        return Math.abs(duration.toMinutes());
      case HOURS:
        return Math.abs(duration.toHours());
      case DAYS:
        return Math.abs(duration.toDays());
      default:
        throw new IllegalArgumentException("不支持的时间单位: " + timeUnit);
    }
  }

  public static Boolean isTimestampInTargetDay(Long timestamp, LocalDate localDate) {
    ZoneId zoneId = ZoneId.systemDefault();
    ZonedDateTime startOfDay = localDate.atStartOfDay(zoneId);
    ZonedDateTime enOfDay = localDate.plusDays(1).atStartOfDay(zoneId).minusNanos(1);
    Instant timestampInstant = Instant.ofEpochSecond(timestamp);
    ZonedDateTime timestampDateTime = timestampInstant.atZone(zoneId);
    return (timestampDateTime.isAfter(startOfDay) || timestampDateTime.isEqual(startOfDay))
        && (timestampDateTime.isBefore(enOfDay) || timestampDateTime.isEqual(enOfDay));
  }

  public static Long currentDayStart() {
    // 获取当前日期
    LocalDate currentDate = LocalDate.now();
    LocalDateTime startOfDay = LocalDateTime.of(currentDate, LocalTime.MIN);
    return startOfDay.atZone(ZoneId.systemDefault()).toEpochSecond();
  }

  public static Long currentDayEnd() {
    LocalDate currentDate = LocalDate.now();
    LocalDateTime endOfDay = LocalDateTime.of(currentDate, LocalTime.MAX);
    return endOfDay.atZone(ZoneId.systemDefault()).toEpochSecond();
  }
}
