package com.daddylab.supplier.item.infrastructure.utils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Optional;

public class NumberUtil extends cn.hutool.core.util.NumberUtil {

    private static final int DEFAULT_SCALE = 2;
    private static final int DEFAULT_ROUND_MODE = BigDecimal.ROUND_HALF_UP;

    public static boolean equals(Integer num1, Integer num2) {
        return num1 != null && num1.compareTo(num2) == 0;
    }

    public static boolean isGreaterThan(Integer num1, Integer num2) {
        return (num1 != null ? num1 : 0) > (num2 != null ? num2 : 0);
    }

    public static boolean isGreaterThanOrEqual(Integer num1, Integer num2) {
        return (num1 != null ? num1 : 0) >= (num2 != null ? num2 : 0);
    }

    public static boolean isLessThan(Integer num1, Integer num2) {
        return (num1 != null ? num1 : 0) < (num2 != null ? num2 : 0);
    }

    public static boolean isLessThanOrEqual(Integer num1, Integer num2) {
        return (num1 != null ? num1 : 0) <= (num2 != null ? num2 : 0);
    }

    public static boolean equals(Long num1, Long num2) {
        return num1 != null && num1.compareTo(num2) == 0;
    }

    public static boolean isGreaterThan(Long num1, Long num2) {
        return (num1 != null ? num1 : 0) > (num2 != null ? num2 : 0);
    }

    public static boolean isGreaterThanOrEqual(Long num1, Long num2) {
        return (num1 != null ? num1 : 0) >= (num2 != null ? num2 : 0);
    }

    public static boolean isLessThan(Long num1, Long num2) {
        return (num1 != null ? num1 : 0) < (num2 != null ? num2 : 0);
    }

    public static boolean isLessThanOrEqual(Long num1, Long num2) {
        return (num1 != null ? num1 : 0) <= (num2 != null ? num2 : 0);
    }

    public static boolean equals(BigDecimal num1, BigDecimal num2) {
        return num1 != null && num1.compareTo(num2) == 0;
    }

    public static boolean isGreaterThan(BigDecimal num1, BigDecimal num2) {
        return (num1 != null ? num1 : BigDecimal.ZERO).compareTo(num2 != null ? num2 : BigDecimal.ZERO) > 0;
    }

    public static boolean isGreaterThanOrEqual(BigDecimal num1, BigDecimal num2) {
        return (num1 != null ? num1 : BigDecimal.ZERO).compareTo(num2 != null ? num2 : BigDecimal.ZERO) >= 0;
    }

    public static boolean isLessThan(BigDecimal num1, BigDecimal num2) {
        return (num1 != null ? num1 : BigDecimal.ZERO).compareTo(num2 != null ? num2 : BigDecimal.ZERO) < 0;
    }

    public static boolean isLessThanOrEqual(BigDecimal num1, BigDecimal num2) {
        return (num1 != null ? num1 : BigDecimal.ZERO).compareTo(num2 != null ? num2 : BigDecimal.ZERO) <= 0;
    }

    public static boolean isPositive(BigDecimal num) {
        return num != null && num.compareTo(BigDecimal.ZERO) > 0;
    }

    public static boolean isPositive(Long num) {
        return num != null && num.compareTo(0L) > 0;
    }

    public static boolean isPositive(Integer num) {
        return num != null && num.compareTo(0) > 0;
    }

    public static boolean isNegative(BigDecimal num) {
        return num != null && num.compareTo(BigDecimal.ZERO) < 0;
    }

    public static boolean isNegative(Long num) {
        return num != null && num.compareTo(0L) < 0;
    }

    public static boolean isNegative(Integer num) {
        return num != null && num.compareTo(0) < 0;
    }

    public static boolean isNegativeOrNull(BigDecimal num) {
        return num == null || num.compareTo(BigDecimal.ZERO) < 0;
    }

    public static boolean isNegativeOrNull(Long num) {
        return num == null || num.compareTo(0L) < 0;
    }

    public static boolean isNegativeOrNull(Integer num) {
        return num == null || num.compareTo(0) < 0;
    }

    public static boolean isZero(Object num) {
        if (num instanceof BigDecimal) {
            return isZero(((BigDecimal) num));
        } else if (num instanceof BigInteger) {
            return isZero((BigInteger) num);
        }  else if (num instanceof Long) {
            return isZero((Long) num);
        } else if (num instanceof Integer) {
            return isZero((Integer) num);
        } else {
            throw new IllegalArgumentException("当前方法未实现该数据类型");
        }
    }

    public static boolean isZero(BigInteger num) {
        return num != null && num.compareTo(BigInteger.ZERO) == 0;
    }

    public static boolean isZero(BigDecimal num) {
        return num != null && num.compareTo(BigDecimal.ZERO) == 0;
    }

    public static boolean isZero(Long num) {
        return num != null && num.compareTo(0L) == 0;
    }

    public static boolean isZero(Integer num) {
        return num != null && num.compareTo(0) == 0;
    }

    public static boolean isZeroOrNull(Object num) {
        return num == null || isZero(num);
    }

    public static boolean isZeroOrNull(BigDecimal num) {
        return num == null || num.compareTo(BigDecimal.ZERO) == 0;
    }

    public static boolean isZeroOrNull(Long num) {
        return num == null || num.compareTo(0L) == 0;
    }

    public static boolean isZeroOrNull(Integer num) {
        return num == null || num.compareTo(0) == 0;
    }

    public static String format(BigDecimal decimal, int scale, int round) {
        return Optional.ofNullable(decimal).orElse(BigDecimal.ZERO).setScale(scale, round)
                .toPlainString();
    }

    public static String format(BigDecimal decimal, int scale) {
        return Optional.ofNullable(decimal).orElse(BigDecimal.ZERO)
                .setScale(scale, DEFAULT_ROUND_MODE).toPlainString();
    }

    public static String format(BigDecimal decimal) {
        return Optional.ofNullable(decimal).orElse(BigDecimal.ZERO)
                .setScale(DEFAULT_SCALE, DEFAULT_ROUND_MODE).toPlainString();
    }

    public static BigDecimal scale(BigDecimal decimal) {
        return decimal != null ? decimal.setScale(DEFAULT_SCALE, DEFAULT_ROUND_MODE) : null;
    }

    public static BigDecimal scale(BigDecimal decimal, int scale) {
        return decimal != null ? decimal.setScale(scale, DEFAULT_ROUND_MODE) : null;
    }

    public static BigDecimal scale(BigDecimal decimal, int scale, int mode) {
        return decimal != null ? decimal.setScale(scale, mode) : null;
    }

    public static Integer convertInt(String num){
        BigDecimal bigDecimal = new BigDecimal(num);
        return bigDecimal.intValue();
    }

}
