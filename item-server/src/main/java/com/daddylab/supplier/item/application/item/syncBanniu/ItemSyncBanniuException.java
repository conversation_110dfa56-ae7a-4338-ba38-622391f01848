package com.daddylab.supplier.item.application.item.syncBanniu;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemBanniuRefType;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;

import lombok.Getter;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/12
 */
public class ItemSyncBanniuException extends RuntimeException {
    private static final long serialVersionUID = 27777795223361294L;
    @Getter private final ItemBanniuRefType type;
    @Getter private final List<ErrorItem> errors;

    @Getter
    public static class ErrorItem {
        Long firstId;
        Long secondId;
        Throwable e;
        String errmsg;
        boolean canRetry;

        public ErrorItem(
                Long firstId, Long secondId, Throwable e, String errmsg, boolean canRetry) {
            this.firstId = firstId;
            this.secondId = secondId;
            this.e = e;
            this.errmsg = errmsg;
            this.canRetry = canRetry;
        }

        public ErrorItem(Long firstId, Long secondId, Throwable e) {
            this(firstId, secondId, e, null, e instanceof IOException);
        }

        public ErrorItem(Long firstId, Long secondId, Throwable e, boolean canRetry) {
            this(firstId, secondId, e, null, canRetry);
        }

        public ErrorItem(Long firstId, Long secondId, String errmsg) {
            this(firstId, secondId, errmsg, false);
        }

        public ErrorItem(Long firstId, Long secondId, String errmsg, boolean canRetry) {
            this(firstId, secondId, null, errmsg, canRetry);
        }

        public String getErrmsg() {
            if (e != null) {
                return e.getMessage();
            }
            return errmsg;
        }
    }

    public ItemSyncBanniuException(ItemBanniuRefType type, List<ErrorItem> errors) {
        this.type = type;
        this.errors = errors;
    }

    @Override
    public String getMessage() {
        return String.format(
                "班牛发货仓同步异常，%s个错误项:%s", this.errors.size(), JsonUtil.toJson(this.errors));
    }

    public boolean isCanRetry() {
        return this.errors.stream().anyMatch(ErrorItem::isCanRetry);
    }
}
