package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: <PERSON>
 * @Date: 2022/9/19 13:36
 * @Description: 电商商品服务（ark-sailor-item）统一返回模型
 */
@Data
public class ArkSailorItemResponse<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final Integer SUCCESS_CODE = 200;

    @ApiModelProperty(value = "状态码")
    private Integer code;

    @ApiModelProperty(value = "描述信息")
    private String msg;

    @ApiModelProperty(value = "返回时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date time;

    @ApiModelProperty(value = "返回数据")
    private T data;
}
