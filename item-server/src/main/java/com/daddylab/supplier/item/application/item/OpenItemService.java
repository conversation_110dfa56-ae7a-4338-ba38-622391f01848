package com.daddylab.supplier.item.application.item;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.controller.provider.dto.ProviderVO;
import com.daddylab.supplier.item.controller.category.dto.CategoryQueryCmd;
import com.daddylab.supplier.item.controller.category.dto.CategoryVo;
import com.daddylab.supplier.item.types.item.OpenItemPageQuery;
import com.daddylab.supplier.item.types.item.PartnerRelatedItemsQuery;
import com.daddylab.supplier.item.types.item.TbIdToMallIdMappingQuery;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/6/20
 */
public interface OpenItemService {

    MultiResponse<PartnerRelatedItems> partnerRelatedItemsQuery(PartnerRelatedItemsQuery query);

    PageResponse<OpenItem> pageQuery(OpenItemPageQuery query);

    SingleResponse<Map<String, Collection<Long>>> tbIdToMallIdMapping(TbIdToMallIdMappingQuery query);

    SingleResponse<ProviderVO> queryProviderByItemId(Long id);

    MultiResponse<CategoryVo> queryCategoryList(CategoryQueryCmd categoryPageQuery);
}
