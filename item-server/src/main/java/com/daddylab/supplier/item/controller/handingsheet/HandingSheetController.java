package com.daddylab.supplier.item.controller.handingsheet;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.handingsheet.HandingSheetV2BizService;
import com.daddylab.supplier.item.controller.handingsheet.dto.*;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.HandingSheetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/24 13:48
 * @Description: 盘货表相关API
 */
@Api(value = "盘货表相关API", tags = "盘货表相关API")
@RestController
@RequestMapping("/handing-sheet")
public class HandingSheetController {
    @Autowired
    private HandingSheetService handingSheetService;

    @Autowired
    private HandingSheetV2BizService handingSheetV2Service;

    @ResponseBody
    @ApiOperation("盘货表分页查询")
    @GetMapping("/pageQuery")
    public PageResponse<HandingSheetPageVo> pageQuery(HandingSheetPageQuery query) {
        return handingSheetService.pageQuery(query);
    }

    @ResponseBody
    @ApiOperation("不同状态盘货表的数量")
    @GetMapping("/status/count")
    public SingleResponse<HandingSheetStatusCountVo> statusCount(HandingSheetPageQuery query) {
        return handingSheetService.statusCount(query);
    }

    @ResponseBody
    @ApiOperation("盘货表关联的商品导出")
    @GetMapping("/exportItemInfo")
    public SingleResponse<Boolean> exportItemInfo(HandingSheetItemPageQuery pageQuery) {
        handingSheetV2Service.exportItemInfoExcel(pageQuery);
        return SingleResponse.of(true);
    }

    /**
     * @deprecated
     */
    @ResponseBody
    @ApiOperation("新增或编辑盘货表")
    @PostMapping("/saveOrEdit")
    public SingleResponse<Long> saveOrEdit(@RequestBody @Valid HandingSheetParam param) {
        param.setAudit(false);
        Long handingSheetId = handingSheetService.saveOrEdit(param);
        return SingleResponse.of(handingSheetId);
    }

    @ResponseBody
    @ApiOperation("执行审核")
    @PostMapping("/doAudit")
    public SingleResponse<Long> doAudit(@RequestBody @Valid HandingSheetParam param) {
        param.setAudit(true);
        Long handingSheetId = handingSheetService.doAudit(param);
        return SingleResponse.of(handingSheetId);
    }

    @ResponseBody
    @ApiOperation("删除盘货表")
    @PostMapping("/delete")
    public SingleResponse<String> delete(@RequestBody HandingSheetDelParam param) {
        handingSheetService.delete(param);
        return SingleResponse.of("OK");
    }

    /**
     * @deprecated
     */
    @ResponseBody
    @ApiOperation("编辑盘货表商品")
    @PostMapping("/updateHandingSheetItem")
    public SingleResponse<String> updateHandingSheetItem(@RequestBody HandingSheetItemUpdateParam param) {
        handingSheetService.updateHandingSheetItem(param);
        return SingleResponse.of("OK");
    }

    @ResponseBody
    @ApiOperation("盘货表查看-基础信息")
    @GetMapping("/sheetBaseInfo")
    public SingleResponse<HandingSheetBaseVo> getBaseInfo(@ApiParam("盘货表主键") Long id) {
        return handingSheetService.getBaseInfo(id);
    }

    /**
     * @deprecated
     */
    @ResponseBody
    @ApiOperation("盘货表查看-关联商品信息分页查询")
    @GetMapping("/sheetItemInfoPage")
    public PageResponse<HandingSheetItemVo> sheetItemInfoPage(HandingSheetItemPageQuery query) {
        return handingSheetService.sheetItemInfoPage(query);
    }

    @ResponseBody
    @ApiOperation("提交审核")
    @PostMapping("/submitAudit")
    public SingleResponse<String> submitAudit(@RequestBody HandingSheetSubmitAuditParam param) {
        handingSheetService.submitAudit(param);
        return SingleResponse.of("OK");
    }

    @ResponseBody
    @ApiOperation("撤销审核")
    @PostMapping("/cancelAudit")
    public SingleResponse<String> cancelAudit(@RequestBody HandingSheetCancelAuditParam  param) {
        handingSheetService.cancelAudit(param);
        return SingleResponse.of("OK");
    }

    @ResponseBody
    @ApiOperation("新增或编辑店铺活动富文本内容")
    @PostMapping("/saveOrUpdateActivityText")
    public SingleResponse<String> saveOrUpdateActivityText(@RequestBody @Valid HandingSheetActivityTextParam param) {
        handingSheetService.saveOrUpdateActivityText(param);
        return SingleResponse.of("OK");
    }

    @ResponseBody
    @ApiOperation("删除店铺活动富文本内容")
    @PostMapping("/deleteActivityText")
    public SingleResponse<String> deleteActivityText(@RequestBody HandingSheetActivityTextDelParam param) {
        param.setCurrentUserId(UserContext.getUserId());
        handingSheetService.deleteActivityText(param);
        return SingleResponse.of("OK");
    }

    @ResponseBody
    @ApiOperation("店铺活动富文本内容分页展示")
    @GetMapping("/pageQueryActivityText")
    public PageResponse<HandingSheetActivityTextPageVo> pageQueryActivityText(HandingSheetActivityTextPageQuery pageQuery) {
        return handingSheetService.pageQueryActivityText(pageQuery);
    }

    /**
     * @deprecated
     */
    @ResponseBody
    @ApiOperation("刷新价格")
    @PostMapping("/refreshPrice")
    public SingleResponse<ArrivalPriceVo> refreshPrice(@RequestBody HandingSheetRefreshPriceParam param) {
        return handingSheetService.refreshPrice(param);
    }
}
