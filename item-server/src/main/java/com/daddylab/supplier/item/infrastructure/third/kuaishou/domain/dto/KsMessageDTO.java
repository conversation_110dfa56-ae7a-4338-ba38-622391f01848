package com.daddylab.supplier.item.infrastructure.third.kuaishou.domain.dto;

import liquibase.pro.packaged.S;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @class KsMessageDTO.java
 * @description 描述类的作用
 * @date 2024-02-29 17:01
 */
@Data
public class KsMessageDTO implements Serializable {
    /**
     * 消息唯一id
     */
    private String eventId;
    /**
     * 业务id如订单id、退款单id、商品id
     */
    private Long bizId;
    /**
     * 商家id
     */
    private Long userId;
    /**
     * 商家openId
     */
    private String openId;
    /**
     * 应用Id
     */
    private String appKey;
    /**
     * 消息标示
     */
    private String event;
    /**
     * 消息内容，业务内容Json串
     */
    private String info;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 是否心跳测试事件
     */
    private Boolean test;
}
