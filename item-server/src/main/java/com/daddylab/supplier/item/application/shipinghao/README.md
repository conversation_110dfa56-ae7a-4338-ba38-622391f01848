# 微信商品同步功能

## 功能概述

本模块实现了从微信小店拉取商品信息的功能，支持全量同步和增量更新，并集成了Redis缓存机制来优化AccessToken的管理。

## 主要功能

1. **获取访问令牌** - 调用微信API获取访问令牌，支持Redis缓存
2. **获取商品列表** - 分页获取微信小店的商品列表
3. **获取商品详情** - 获取单个商品的详细信息
4. **批量获取商品详情** - 异步批量获取多个商品的详细信息
5. **全量同步** - 同步所有商品信息
6. **增量同步** - 根据时间戳同步更新的商品信息
7. **Token缓存管理** - 自动缓存AccessToken，避免频繁调用微信API

## 技术架构

### 目录结构

```
shipinghao/
├── config/
│   └── WechatConfig.java              # 微信配置类
├── controller/
│   └── WechatProductController.java    # 微信商品控制器
├── dto/
│   ├── WechatAccessTokenDto.java       # 访问令牌DTO
│   ├── WechatApiResponseDto.java       # API响应DTO
│   ├── WechatProductDetailDto.java     # 商品详情DTO
│   └── WechatProductListDto.java       # 商品列表DTO
├── service/
│   ├── WechatApiService.java           # 微信API服务接口
│   ├── WechatProductSyncService.java   # 商品同步服务接口
│   └── impl/
│       ├── WechatApiServiceImpl.java   # 微信API服务实现（含Redis缓存）
│       └── WechatProductSyncServiceImpl.java # 商品同步服务实现
└── README.md                           # 说明文档
```

### 核心组件

1. **WechatConfig** - 配置微信API相关参数
2. **WechatApiService** - 封装微信API调用，集成Redis缓存
3. **WechatProductSyncService** - 商品同步业务逻辑
4. **WechatProductController** - 提供REST API接口

## Redis缓存机制

### AccessToken缓存策略

- **缓存Key**: `wechat:access_token`
- **过期时间**: 7000秒（比微信的7200秒稍短，确保安全）
- **缓存策略**: 
  1. 优先从Redis获取AccessToken
  2. 如果Redis中没有或已过期，则调用微信API获取
  3. 获取成功后自动缓存到Redis

### 缓存流程

```
1. 检查Redis缓存
   ↓
2. 缓存存在且有效？ → 是 → 返回缓存的Token
   ↓ 否
3. 调用微信API获取新Token
   ↓
4. 获取成功？ → 是 → 缓存到Redis（7000秒过期）
   ↓ 否
5. 返回null
```

## 配置说明

在 `application.yml` 中添加以下配置：

```yaml
# Redis配置
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

# 微信配置
wechat:
  shop:
    app-id: your_app_id
    secret: your_secret
    access-token-url: https://api.weixin.qq.com/shop/account/get_access_token
    product-list-url: https://api.weixin.qq.com/shop/product/list
    product-detail-url: https://api.weixin.qq.com/shop/product/get
    connect-timeout: 10000
    read-timeout: 30000
```

## API接口

### 1. 获取访问令牌

```http
GET /api/wechat/product/token
```

### 2. 清除访问令牌缓存

```http
DELETE /api/wechat/product/token/cache
```

### 3. 全量同步商品

```http
POST /api/wechat/product/sync/all
```

### 4. 增量同步商品

```http
POST /api/wechat/product/sync/incremental?lastUpdateTime=*************
```

### 5. 获取商品列表

```http
GET /api/wechat/product/list?offset=0&limit=10
```

**参数说明：**
- `offset`: 偏移量，默认0
- `limit`: 每页数量，默认10，最大30（系统会自动限制）

### 6. 获取商品详情

```http
GET /api/wechat/product/detail/{productId}
```

### 7. 批量获取商品详情

```http
POST /api/wechat/product/details
Content-Type: application/json

["product_id_1", "product_id_2"]
```

## API参数限制

### 微信商品列表API限制

- **page_size**: 每页数量，默认10，最大30
- **data_type**: 数据类型，固定为1（只获取线上数据）
- **offset**: 偏移量，从0开始

系统会自动处理这些限制：
1. 如果请求的limit超过30，会自动调整为30
2. 如果请求的limit为null，会使用默认值10
3. 所有API调用都会记录实际使用的参数值

## 使用示例

### Java代码调用

```java
@Autowired
private WechatProductSyncService wechatProductSyncService;

@Autowired
private WechatApiService wechatApiService;

// 获取访问令牌（自动使用Redis缓存）
WechatAccessTokenDto accessToken = wechatApiService.getAccessToken();

// 清除访问令牌缓存
wechatApiService.clearCachedAccessToken();

// 获取商品列表
WechatProductListDto productList = wechatProductSyncService.getProductList(0, 10);

// 获取商品详情
WechatProductDetailDto productDetail = wechatProductSyncService.getProductDetail("product_id");

// 全量同步
boolean result = wechatProductSyncService.syncAllProducts();

// 增量同步
boolean result = wechatProductSyncService.syncIncrementalProducts(System.currentTimeMillis() - 86400000);
```

### 测试

运行测试类 `WechatProductSyncTest` 来验证功能：

```bash
mvn test -Dtest=WechatProductSyncTest
```

## 特性说明

1. **Redis缓存** - AccessToken自动缓存，避免频繁调用微信API
2. **异步处理** - 批量获取商品详情使用异步方式，提高性能
3. **错误处理** - 完善的异常处理和日志记录
4. **分页支持** - 支持分页获取商品列表
5. **增量同步** - 支持基于时间戳的增量同步
6. **配置化** - 所有API地址和参数都可通过配置文件调整
7. **缓存管理** - 提供清除缓存的接口，便于调试和管理

## 注意事项

1. **Redis依赖** - 需要确保Redis服务正常运行
2. **API限制** - 微信API有调用频率限制，建议合理控制调用频率
3. **Token管理** - 访问令牌自动缓存7000秒，无需手动管理
4. **数据存储** - 目前商品详情只打印日志，未存储到数据库
5. **Mock配置** - 当前使用Mock的APPID和SECRET，实际使用时需要替换为真实值
6. **分页限制** - 微信商品列表API的page_size参数最大值为30，系统会自动限制不超过此值

## 扩展功能

1. **数据存储** - 可以添加数据库存储功能
2. **定时同步** - 可以添加定时任务进行自动同步
3. **缓存机制** - 可以扩展Redis缓存到其他数据
4. **监控告警** - 可以添加同步状态监控和异常告警
5. **Token刷新** - 可以添加Token自动刷新机制

## 依赖说明

- Spring Boot 2.x
- Spring Data Redis
- OkHttp 4.x
- FastJSON 2.x
- Lombok
- Spring Web

## 开发说明

1. 所有代码都遵循Spring Boot最佳实践
2. 使用Lombok简化代码
3. 完善的日志记录
4. 统一的异常处理
5. 清晰的代码注释
6. Redis缓存优化AccessToken管理 