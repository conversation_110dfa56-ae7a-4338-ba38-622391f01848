package com.daddylab.supplier.item.application.purchasePayable.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("采购应付凭证返回结果")
public class PurchasePayableVoucherVO implements Serializable {

    private static final long serialVersionUID = -8284959365833649938L;

    @ApiModelProperty(value = "生成日期")
    private Long createDate;

    @ApiModelProperty(value = "供应商名称")
    private String providerName;

    @ApiModelProperty(value = "采购组织名称")
    private String organizationName;

    @ApiModelProperty(value = "商品种类数量")
    private Integer kindQuantity;

    @ApiModelProperty(value = "商品总数")
    private Integer totalItemQuantity;

    @ApiModelProperty(value = "价税合计")
    private BigDecimal totalPriceTax;

    @ApiModelProperty(value = "税后金额")
    private BigDecimal totalAfterTaxItemAmount;

    @ApiModelProperty(value = "税额")
    private BigDecimal totalTaxAmount;

    @ApiModelProperty(value = "商品明细")
    private List<PurchasePayableVoucherItemDetailsVO> list;
}

