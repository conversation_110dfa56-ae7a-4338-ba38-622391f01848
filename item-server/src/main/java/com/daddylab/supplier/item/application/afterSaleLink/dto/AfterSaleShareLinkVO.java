package com.daddylab.supplier.item.application.afterSaleLink.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2024年05月11日 2:14 PM
 */
@Data
@ApiModel("链接返回封装")
public class AfterSaleShareLinkVO {

    @ApiModelProperty("链接ID")
    private Long id;

    @ApiModelProperty("链接编码")
    private String no;

    @ApiModelProperty("链接名称")
    private String name;

    @ApiModelProperty("分享人数")
    private Integer sharePersonNum;

    @ApiModelProperty("1：长期有效。0：非长期有效")
    private Integer alwaysValidity;

    @ApiModelProperty("有效期（单位天）")
    private Integer validityPeriod;

    @ApiModelProperty("状态。0正常，1禁用")
    private Integer status;

    @ApiModelProperty("创建者名称")
    private String createdName;

    @ApiModelProperty("创建时间")
    private String createdAt;

    @ApiModelProperty("分享的链接值")
    private String linkVal;


}
