package com.daddylab.supplier.item.infrastructure.third.kuaishou.domain.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @class KsItemCreate.java
 * @description 描述类的作用
 * @date 2024-03-01 17:53
 */
@NoArgsConstructor
@Data
public class KsSkuStockZeroDTO implements Serializable {

    /**
     * 店铺ID
     */
    private Integer sellerId;
    /**
     * 商品ID
     */
    private Long itemId;
    /**
     * 快手skuId
     */
    private Integer skuId;
    /**
     * sku当前库存
     */
    private Integer skuStock;
    /**
     * sku变更前库存
     */
    private Integer beforeSkuStock;
    /**
     * 更新时间，有可能乱序, 更新时间判断消息顺序
     */
    private Long updateTime;
}
