package com.daddylab.supplier.item.infrastructure.config.mybatisPlus;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusPropertiesCustomizer;
import com.baomidou.mybatisplus.core.injector.ISqlInjector;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.daddylab.supplier.item.infrastructure.bizLevelDivision.BizLevelDivisionInterceptor;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddylabSqlInjector;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizePersistData;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.github.pagehelper.PageInterceptor;

import org.apache.ibatis.reflection.factory.DefaultObjectFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.List;
import java.util.Properties;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/14 10:44 上午
 * @description
 */
@Configuration
@MapperScan("com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper")
@TableFieldsTypeHandlerScan("com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity")
@EnableTransactionManagement
public class MybatisPlusConfig {

    /**
     * 序列化枚举值为数据库存储值,重写toString方法
     *
     * @return
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer customizer() {
        return builder ->
                builder.featuresToEnable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING);
    }

    /**
     * 新的分页插件,一缓和二缓遵循mybatis的规则,需要设置 MybatisConfiguration#useDeprecatedExecutor = false
     * 避免缓存出现问题(该属性会在旧插件移除后一同移除)
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 添加数据权限拦截器
        final BizLevelDivisionInterceptor bizLevelDivisionInterceptor = new BizLevelDivisionInterceptor();
        interceptor.addInnerInterceptor(bizLevelDivisionInterceptor);

        final PaginationInnerInterceptor paginationInnerInterceptor =
                new PaginationInnerInterceptor(DbType.MYSQL);
        // 设置请求的页面大于最大页后操作， true调回到首页
        paginationInnerInterceptor.setOverflow(true);
        // 设置最大单页限制数量，默认 500 条，-1 不受限制
        paginationInnerInterceptor.setMaxLimit(-1L);
        interceptor.addInnerInterceptor(paginationInnerInterceptor);
        
        return interceptor;
    }

    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return configuration -> {
            configuration.setUseDeprecatedExecutor(false);

            // 自定义的对象工厂实现DI
            //            configuration.setObjectFactory(new ObjectFactory());

            // 注册自己的枚举类型处理器
            configuration.getTypeHandlerRegistry().register(IEnum.class, IEnumTypeHandler.class);
            // configuration.getTypeHandlerRegistry().register(JsonModel.class,
            // JacksonTypeHandler.class);
            configuration
                    .getTypeHandlerRegistry()
                    .register(ItemOptimizePersistData.class, JacksonTypeHandler.class);
            JacksonTypeHandler.setObjectMapper(JsonUtil.MAPPER);

            // Mybatis原生分页拦截器
            final PageInterceptor pageInterceptor = new PageInterceptor();
            final Properties pageProperties = new Properties();
            pageProperties.setProperty("reasonable", "true");
            pageInterceptor.setProperties(pageProperties);
            configuration.addInterceptor(pageInterceptor);

            // 去除sql中多余的空格
            configuration.setShrinkWhitespacesInSql(true);
        };
    }

    @Bean
    public MybatisPlusPropertiesCustomizer mybatisPlusPropertiesCustomizer() {
        return (properties) -> {
            // 设定全局的逻辑删除
            properties
                    .getGlobalConfig()
                    .getDbConfig()
                    .setLogicDeleteField("isDel")
                    .setLogicDeleteValue("1")
                    .setLogicNotDeleteValue("0");
        };
    }

    private static class ObjectFactory extends DefaultObjectFactory {
        @Override
        public <T> T create(Class<T> type) {
            final T o = super.create(type);
            ApplicationContextUtil.autowire(o);
            return o;
        }

        @Override
        public <T> T create(
                Class<T> type, List<Class<?>> constructorArgTypes, List<Object> constructorArgs) {
            final T o = super.create(type, constructorArgTypes, constructorArgs);
            ApplicationContextUtil.autowire(o);
            return o;
        }
    }

    @Bean
    public ISqlInjector iSqlInjector() {
        return new DaddylabSqlInjector();
    }
}
