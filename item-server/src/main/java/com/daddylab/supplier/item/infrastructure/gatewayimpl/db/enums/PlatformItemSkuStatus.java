package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 平台商品SKU状态
 *
 * <p>1:在售 0:已下架
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@AllArgsConstructor
public enum PlatformItemSkuStatus implements IEnum<Integer> {
    /**
     * 已删除
     */
    DELETED(0, "删除"),
    /**
     * 在售
     */
    ON_SALE(1, "在售"),
    /**
     * 下架
     */
    SALE_OUT(2, "下架"),
    
    
    WAIT_SALE(3,"待上架");
    
    ;

    @EnumValue private final Integer value;
    private final String desc;
}
