package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSaleForwardingNeedNotForwardTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSaleForwardingRegisterStatusEnum;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 售后转寄登记
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AfterSaleForwardingRegister implements Serializable {

  private static final long serialVersionUID = 1L;

  /** id */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /** 创建时间createAt */
  @TableField(fill = FieldFill.INSERT)
  private Long createdAt;

  /** 创建人updateUser */
  @TableField(fill = FieldFill.INSERT)
  private Long createdUid;

  /** 更新时间updateAt */
  @TableField(fill = FieldFill.UPDATE)
  private Long updatedAt;

  /** 更新人updateUser */
  @TableField(fill = FieldFill.UPDATE)
  private Long updatedUid;

  /** 是否已删除 */
  @TableLogic(value = "0", delval = "id")
  private Long isDel;

  /** 删除时间 */
  @TableField(fill = FieldFill.DEFAULT)
  private Long deletedAt;

  /** 快递单号 */
  private String deliveryNo;

  /** 处理状态 0:待处理 1:待转寄 2:已转寄 3:无需转寄 */
  private AfterSaleForwardingRegisterStatusEnum status;

  /** 商品名称 */
  private String itemName;

  /** 商品数量 */
  private Integer itemNum;

  /** 商品SKU */
  private String skuCode;

  /** 规格名称 */
  private String skuName;

  /** 是否完好 */
  private Boolean intact;

  /** 异常说明 */
  private String abnormalDescription;

  /** 影响销售凭证 */
  private String affectsSalesVouchers;

  /** 原始单号 */
  private String orderNo;

  /** 出库单号 */
  private String stockoutNo;

  /** 出库仓库 */
  private String stockoutWarehouse;

  /** 出库仓库编号 */
  private String stockoutWarehouseNo;

  /** 转寄地址 */
  private String forwardingAddress;

  /** 转寄单号 */
  private String forwardingDeliveryNo;

  /** 重量 */
  private String weight;

  /** 纸箱型号 */
  private String cartonModel;

  /** 操作人ID */
  private Long operatorId;

  /** 操作人类型 */
  private Integer operatorType;

  /** 是否为系统自动处理 */
  private Boolean autoHandle;

  /** 客服备注 */
  private String csRemark;

  /** 无需转寄类型 */
  private AfterSaleForwardingNeedNotForwardTypeEnum needNotForwardType;

  /**
   * 店铺编号
   */
  private String shopNo;
}
