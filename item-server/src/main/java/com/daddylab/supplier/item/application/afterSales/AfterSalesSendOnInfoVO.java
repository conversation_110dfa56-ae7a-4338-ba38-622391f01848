package com.daddylab.supplier.item.application.afterSales;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Collection;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/25
 */
@Data
@ApiModel("售后转寄信息VO")
public class AfterSalesSendOnInfoVO {

    /**
     * 物流名称
     **/
    @ApiModelProperty("物流名称")
    private String logisticsName;

    /**
     * 物流单号
     **/
    @ApiModelProperty("物流单号")
    private String logisticsNo;

    /**
     * 重量（kg）
     **/
    @ApiModelProperty("重量（kg）")
    private String weight;

    /**
     * 备注
     **/
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 转寄人（转寄到的目标仓库）
     **/
    @ApiModelProperty("转寄人（转寄到的目标仓库）")
    private String sender;

    /**
     * 转寄人电话（转寄到的目标仓库）
     **/
    @ApiModelProperty("转寄人电话（转寄到的目标仓库）")
    private String senderTel;

    /**
     * 转寄人地址（转寄到的目标仓库）
     **/
    @ApiModelProperty("转寄人地址（转寄到的目标仓库）")
    private String senderAddress;

    /**
     * 转寄方仓库编号（转寄到的目标仓库）
     **/
    @ApiModelProperty("转寄方仓库编号（转寄到的目标仓库）")
    private String senderNo;

    /**
     * 寄送方仓库编号
     **/
    @ApiModelProperty("寄送方仓库编号")
    private String fromWarehouseNo;

    /**
     * 寄送方仓库名称
     **/
    @ApiModelProperty("寄送方仓库名称")
    private String fromWarehouse;

    /**
     * 寄送方联系电话
     */
    @ApiModelProperty("寄送方联系电话")
    private String fromTel;

    /**
     * 转寄图片
     */
    @ApiModelProperty("转寄图片")
    private Collection<String> images;

    /**
     * 转寄时间
     */
    @ApiModelProperty("转寄时间")
    private Long sendOnTime;
}
