package com.daddylab.supplier.item.domain.shop.dto;

import com.alibaba.cola.dto.Command;
//import com.daddylab.supplier.item.application.virtualWarehouse.dto.InventoryGoodsSaveCmd;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.shop.enums.ShopStatus;
import com.daddylab.supplier.item.infrastructure.validators.link.HttpURL;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "创建或者更新店铺")
public class CreateOrUpdateShopCmd extends Command {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称")
    @NotNull(message = "店铺名称不能为空")
    @NotBlank(message = "店铺名称不能为空")
    @Size(message = "店铺名称长度应该在{min}-{max}个字符", min = 1, max = 50)
    private String name;

    /**
     * 店铺账号
     */
    @ApiModelProperty(value = "店铺账号")
    @NotNull(message = "店铺账号不能为空")
    @NotBlank(message = "店铺账号不能为空")
    @Size(message = "店铺账号长度应该在{min}-{max}个字符", min = 1, max = 132)
    private String account;

    /**
     * 店铺链接
     */
    @ApiModelProperty(value = "店铺链接")
    @Size(message = "店铺链接长度应该在{min}-{max}个字符", min = 1, max = 32)
    @HttpURL
    private String link;

    /**
     * 店铺LOGO
     */
    @ApiModelProperty(value = "店铺LOGO")
    @Size(message = "店铺LOGO长度应该在{min}-{max}个字符", max = 512)
    @HttpURL
    private String logo;

    /**
     * 平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城
     */
    @ApiModelProperty(value = "平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城")
    @NotNull(message = "平台不能为空")
    private Platform platform;

    /**
     * 状态 0:关闭 1:准备中 2:营业
     */
    @ApiModelProperty(value = "状态 0:关闭 1:准备中 2:营业")
    @NotNull(message = "请选择店铺状态")
    private ShopStatus status;

    /**
     * 店铺负责人
     */
    @NotNull(message = "店铺负责人不能为Null，如果为空请传一个空数组")
    private List<Long> principals;

    @ApiModelProperty("合作方")
    private List<Integer> corpType;

    @ApiModelProperty("运营模式")
    private List<Integer> runningMode;


    // ------------- 库存设置相关参数 ----------------

//    @NotNull
//    @ApiModelProperty("是否开启自动分配库存.0否,1是")
//    private Integer autoAllocateInventory;
//
//    @ApiModelProperty("设置自动同步库存频次.单位秒")
//    private Integer syncFrequency;
//
//    @ApiModelProperty("设置安全库存")
//    private Integer safetyThreshold;
//
//    @ApiModelProperty("实体仓库存占比设置")
//    private List<ShopWarehouseInventoryDto> shopStockDtoList;
//
//    @ApiModelProperty("实体下属明细的库存设置列表")
//    private List<InventoryGoodsSaveCmd> inventoryGoodsSaveCmdList;
//
//    @ApiModelProperty("库存模式 SHARED:(0,共享),LOCK:(1,锁定)")
//    @NotNull(message = "库存模式参数不得为空")
//    private InventoryMode inventoryMode;

    @ApiModelProperty("店铺主体公司")
    private String mainCompany;


}
