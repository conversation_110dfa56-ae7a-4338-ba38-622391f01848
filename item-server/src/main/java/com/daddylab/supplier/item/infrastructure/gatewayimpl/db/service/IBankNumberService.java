package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BankNumber;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.BankNumberMapper;

/**
 * <p>
 * 银行联行号 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
public interface IBankNumberService extends IDaddylabServicePlus<BankNumber, BankNumberMapper> {

}
