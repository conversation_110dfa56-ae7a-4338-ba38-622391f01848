package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AwsBusinessLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AwsBusinessLogMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAwsBusinessLogService;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 炎黄盈动业务日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
@Service
public class AwsBusinessLogServiceImpl extends DaddyServiceImpl<AwsBusinessLogMapper, AwsBusinessLog> implements IAwsBusinessLogService {

    @Override
    public String getProcessIdByBusinessId(PurchaseTypeEnum typeEnum, Long businessId) {
        LambdaQueryWrapper<AwsBusinessLog> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AwsBusinessLog::getBusinessId, businessId)
                .eq(AwsBusinessLog::getType, typeEnum.getValue())
                .orderByDesc(AwsBusinessLog::getId).last("LIMIT 1");
        List<AwsBusinessLog> list = this.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        return list.get(0).getProcessId();
    }

    @Override
    public AwsBusinessLog getByProcessInstId(String processInstId) {
        return ((AwsBusinessLogMapper) getBaseMapper()).getByProcessInstId(processInstId);
    }
}
