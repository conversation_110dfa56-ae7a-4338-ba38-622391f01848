package com.daddylab.supplier.item.application.drawer;

import com.actionsoft.bpms.api.ApiException;
import com.actionsoft.sdk.service.model.HistoryTaskInstance;
import com.actionsoft.sdk.service.model.TaskInstance;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.aws.service.AwsApprovalFlowService;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAwsBusinessLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INewGoodsService;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil.ChangePropertyObj;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Sets;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/10/7
 */
@Service
@Slf4j
@AllArgsConstructor
public class ItemLaunchLegalIdFetchTask {

    private final IItemService itemService;
    private final IAwsBusinessLogService awsBusinessLogService;
    private final AwsApprovalFlowService awsApprovalFlowService;
    private final IItemDrawerService itemDrawerService;
    private final INewGoodsService newGoodsService;
    private final StaffService staffService;
    private final OperateLogDomainService operateLogDomainService;

    @XxlJob("ItemLaunchAuditStatusFetchTask")
    public void run() {
        final List<NewGoods> list = newGoodsService.lambdaQuery().eq(NewGoods::getLegalId, 0)
                .list();
        Set<Long> itemHandled = Sets.newHashSet();
        log.info("新品商品法务ID补偿:法务ID为空的共计{}", list.size());
        int i = 0;
        for (NewGoods newGoods : list) {
            i++;
            if (itemHandled.contains(newGoods.getItemId())) {
                log.info("新品商品法务ID补偿:处理进度({}/{}, newGoodsId={}) skip", i, list.size(),
                        newGoods.getId());
                continue;
            } else {
                log.info("新品商品法务ID补偿:处理进度({}/{}, newGoodsId={})", i, list.size(), newGoods.getId());
            }
            itemHandled.add(newGoods.getItemId());
            final ItemDrawer itemDrawer = itemDrawerService.lambdaQuery()
                    .eq(ItemDrawer::getItemId, newGoods.getItemId()).one();
            if (Objects.isNull(itemDrawer)) {
                log.warn("新品商品法务ID补偿:商品抽屉不存在 itemId:{}", newGoods.getItemId());
                continue;
            }
            final String processIdByBusinessId = awsBusinessLogService.getProcessIdByBusinessId(
                    PurchaseTypeEnum.ITEM_LIBRARY_AUDIT, itemDrawer.getId());
            if (StringUtil.isBlank(processIdByBusinessId)) {
                log.warn("新品商品法务ID补偿:炎黄审批流ID为空 itemId:{}", newGoods.getItemId());
                continue;
            }
            final String legalLoginName;
            try {
                final List<HistoryTaskInstance> historyTaskInstances = awsApprovalFlowService.historyTaskQuery(
                        processIdByBusinessId);
                final Optional<HistoryTaskInstance> hisLegalTaskOptional = historyTaskInstances
                        .stream().filter(v -> v.getTitle().contains("法务") && !v.getControlState()
                                .equals("delete")).findFirst();
                legalLoginName = hisLegalTaskOptional.map(TaskInstance::getTarget)
                        .orElseGet(() -> {
                            final List<TaskInstance> taskInstances = awsApprovalFlowService.taskQuery(
                                    processIdByBusinessId);
                            return taskInstances.stream()
                                    .filter(v -> v.getTitle().contains("法务") && !v.getControlState()
                                            .equals("delete")).findFirst()
                                    .map(TaskInstance::getTarget).orElse(null);
                        });
            } catch (Throwable e) {
                if (e instanceof ApiException) {
                    log.error("新品商品法务ID补偿:炎黄影动API返回异常:{} itemId:{}", e.getMessage(),
                            newGoods.getItemId());
                } else {
                    log.error("新品商品法务ID补偿:炎黄影动API系统异常:{} itemId:{}", e.getMessage(),
                            newGoods.getItemId(), e);
                }
                continue;
            }
            if (StringUtil.isBlank(legalLoginName)) {
                log.warn("新品商品法务ID补偿:未找到法务信息 itemId:{}", newGoods.getItemId());
                continue;
            }
            final List<DadStaffVO> staffList = staffService.getStaffListByLoginName(
                    Collections.singletonList(legalLoginName));
            final Long legalId = staffList.stream()
                    .filter(v -> v.getLoginName().equals(legalLoginName)).findFirst()
                    .map(DadStaffVO::getUserId).orElse(0L);
            if (!NumberUtil.isPositive(legalId)) {
                log.error("新品商品法务ID补偿:未能根据法务登录名查询到法务ID itemId:{} loginName:{}",
                        newGoods.getItemId(),
                        legalLoginName);
                continue;
            }
            newGoodsService.lambdaUpdate()
                    .set(NewGoods::getLegalId, legalId)
                    .eq(NewGoods::getItemId, newGoods.getItemId())
                    .update();
            operateLogDomainService.addOperatorLog(0L, OperateLogTarget.NEW_GOODS_SPU,
                    newGoods.getItemId(), "系统补偿法务ID",
                    ImmutableList.of(new ChangePropertyObj("legalId", 0L, legalId)));
            log.info("新品商品法务ID补偿:已补偿法务ID itemId:{} processId:{}", newGoods.getItemId(),
                    processIdByBusinessId);
        }
    }

}
