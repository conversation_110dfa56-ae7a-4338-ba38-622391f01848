package com.daddylab.supplier.item.application.afterSales;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesAbnormalState;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2022年10月17日 9:58 AM
 */
@Data
@ApiModel("异常件信息分页查询了列表")
public class AfterSalesPageQuery extends PageQuery {

    private static final long serialVersionUID = -9119012497295260048L;
    @ApiModelProperty("物流编号")
    private String logisticsNo;

    @ApiModelProperty("物流名称")
    private String logisticsName;

    @ApiModelProperty("商品名称")
    private String itemName;

    @ApiModelProperty("创建时间")
    private Long createdAtStart;

    @ApiModelProperty("创建时间")
    private Long createdAtEnd;

    @ApiModelProperty("收货仓库")
    private String warehouseNo;

    @ApiModelProperty("收货结果 1:货品完好 2:微损可入库 3:破损不可入库 4:数量不一致 5:商品不一致")
    private Integer receiveStatus;

    @ApiModelProperty("P系统供应商id。前端忽略")
    private Long partnerProviderId;

    @ApiModelProperty("异常件状态")
    private AfterSalesAbnormalState abnormalState;

}
