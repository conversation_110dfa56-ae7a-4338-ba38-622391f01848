package com.daddylab.supplier.item.application.afterSaleLogistics.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 快递状态 0 待发货，1 已发货，2 已揽收，3 运输中，4 派送中，5 待取件，6 未签收，7 代签收，8 已签收,-10 疑难件
 *
 * <AUTHOR>
 * @since 2024/12/18
 */
@RequiredArgsConstructor
@Getter
@ApiModel(description = "快递状态")
public enum ErpLogisticsStatus implements IIntegerEnum {
  @ApiModelProperty(value = "未获取")
  NONE(-1, "未获取"),
  @ApiModelProperty(value = "待发货")
  WAIT_FOR_SEND(0, "待发货"),
  @ApiModelProperty(value = "已发货")
  SENT(1, "已发货"),
  @ApiModelProperty(value = "已揽收")
  PICKUP(2, "已揽收"),
  @ApiModelProperty(value = "运输中")
  IN_TRANSIT(3, "运输中"),
  @ApiModelProperty(value = "派送中")
  DELIVERING(4, "派送中"),
  @ApiModelProperty(value = "待取件")
  WAIT_FOR_PICKUP(5, "待取件"),
  @ApiModelProperty(value = "未签收")
  UNSIGNED(6, "未签收"),
  @ApiModelProperty(value = "代签收")
  COUNTER_SIGNED(7, "代签收"),
  @ApiModelProperty(value = "已签收")
  SIGNED(8, "已签收"),
  @ApiModelProperty(value = "疑难件")
  DIFFICULT(-10, "疑难件");
  private final Integer value;
  private final String desc;
}
