package com.daddylab.supplier.item.application.drawer;

import cn.hutool.core.collection.CollectionUtil;
import com.actionsoft.sdk.service.model.AbstTaskInstanceModel;
import com.actionsoft.sdk.service.model.HistoryTaskInstance;
import com.actionsoft.sdk.service.model.TaskInstance;
import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.aws.service.AwsApprovalFlowService;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.domain.item.gateway.ItemProcurementGateway;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AwsBusinessLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemApprovalAdvice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAudit;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAuditStats;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAuditTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchModuleAdvice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemProcurement;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemApprovalAdviceType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAwsBusinessLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemApprovalAdviceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerModuleAuditService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerModuleAuditStatsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerModuleAuditTaskService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemLaunchModuleAdviceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INewGoodsService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerAuditTaskState;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerModuleId;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemLaunchProcessNodeId;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Lists;
import java.sql.Timestamp;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/11/22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ItemDrawerAuditMigrateTask {

    public static final List<Long> LEGAL_IDS = Arrays.asList(
            8333565L,//艾琳
            7734895L,//程程
            7734897L,//萝贝
            7735377L //靖玮
    );
    private final LoadingCache<String, List<TaskInstance>> taskCache = Caffeine.newBuilder()
            .expireAfterWrite(
                    Duration.ofSeconds(60)).build(this::getTaskInstances);

    private final IItemService itemService;
    private final IItemDrawerService itemDrawerService;
    private final IItemDrawerModuleAuditStatsService itemDrawerModuleAuditStatsService;
    private final IItemDrawerModuleAuditService itemDrawerModuleAuditService;

    private final IItemDrawerModuleAuditTaskService itemDrawerModuleAuditTaskService;
    private final ItemProcurementGateway itemProcurementGateway;
    private final IAwsBusinessLogService awsBusinessLogService;
    private final AwsApprovalFlowService awsApprovalFlowService;
    private final StaffService staffService;
    private final INewGoodsService newGoodsService;
    private final IItemLaunchModuleAdviceService itemLaunchModuleAdviceService;
    private final IItemApprovalAdviceService itemApprovalAdviceService;


    @XxlJob("ItemDrawerAuditMigrateTask")
    @Trace(operationName = "ItemDrawerAuditMigrateTask")
    public void doTask() {
        final List<AwsBusinessLog> list;
        final String jobParam = XxlJobHelper.getJobParam();
        if (StringUtil.isNotBlank(jobParam)) {
            final List<Long> paramIds = JsonUtil.parse(jobParam, new TypeReference<List<Long>>() {
            }.getType());
            if (CollectionUtil.isEmpty(paramIds)) {
                log.warn("参数为空");
                return;
            }
            final List<Long> paramDrawerIds = itemDrawerService.lambdaQuery()
                    .in(ItemDrawer::getItemId, paramIds).select(Entity::getId).list().stream()
                    .map(Entity::getId).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(paramDrawerIds)) {
                log.warn("根据入参商品ID未能查询到对应的抽屉ID");
                return;
            }
            list = awsBusinessLogService.lambdaQuery()
                    .in(AwsBusinessLog::getBusinessId, paramDrawerIds)
                    .eq(AwsBusinessLog::getType, PurchaseTypeEnum.ITEM_LIBRARY_AUDIT.getValue())
                    .list();
        } else {
            list = awsBusinessLogService.lambdaQuery()
                    .eq(AwsBusinessLog::getType, PurchaseTypeEnum.ITEM_LIBRARY_AUDIT.getValue())
                    .list();
        }
        for (AwsBusinessLog awsBusinessLog : list) {
            final Long drawerId = awsBusinessLog.getBusinessId();
            final ItemDrawer itemDrawer = itemDrawerService.getById(drawerId);
            if (Objects.isNull(itemDrawer)) {
                log.warn("商品未找到抽屉数据，可能已删除 drawerId={}", drawerId);
                continue;
            }
            final Long itemId = itemDrawer.getItemId();
            final String processId = awsBusinessLog.getProcessId();

            final List<ItemLaunchModuleAdvice> itemLaunchModuleAdvices = itemLaunchModuleAdviceService.lambdaQuery()
                    .eq(ItemLaunchModuleAdvice::getRound, 1)
                    .eq(ItemLaunchModuleAdvice::getItemId, itemId).list();

            final List<ItemApprovalAdvice> itemApprovalAdvices = itemApprovalAdviceService.lambdaQuery()
                    .eq(ItemApprovalAdvice::getItemId, itemId).list();

            //法务【标题、文案】审批意见老数据 迁移至【文案&属性】模块审批意见
            handleAdviceMigrate(itemId, itemLaunchModuleAdvices, itemApprovalAdvices,
                    ItemLaunchProcessNodeId.LEGAL,
                    Pair.of(Arrays.asList(ItemApprovalAdviceType.TITLE_ADVICE,
                                    ItemApprovalAdviceType.COPY_ADVICE),
                            ItemDrawerModuleId.TEXT_AND_ATTR));
            //QC【标题、文案】审批意见老数据 迁移至【文案&属性】模块审批意见
            handleAdviceMigrate(itemId, itemLaunchModuleAdvices, itemApprovalAdvices,
                    ItemLaunchProcessNodeId.QC,
                    Pair.of(Arrays.asList(ItemApprovalAdviceType.TITLE_ADVICE,
                                    ItemApprovalAdviceType.COPY_ADVICE),
                            ItemDrawerModuleId.TEXT_AND_ATTR));
            //法务【主图意见】审批意见老数据 迁移至【图片信息】模块审批意见
            handleAdviceMigrate(itemId, itemLaunchModuleAdvices, itemApprovalAdvices,
                    ItemLaunchProcessNodeId.LEGAL,
                    Pair.of(Collections.singletonList(ItemApprovalAdviceType.MAIN_IMAGE_ADVICE),
                            ItemDrawerModuleId.IMG_INFO));
            //QC【主图意见】审批意见老数据 迁移至【图片信息】模块审批意见
            handleAdviceMigrate(itemId, itemLaunchModuleAdvices, itemApprovalAdvices,
                    ItemLaunchProcessNodeId.QC,
                    Pair.of(Collections.singletonList(ItemApprovalAdviceType.MAIN_IMAGE_ADVICE),
                            ItemDrawerModuleId.IMG_INFO));

            final Optional<ItemDrawerModuleAudit> itemDrawerModuleAuditOptional = itemDrawerModuleAuditService.lambdaQuery()
                    .eq(ItemDrawerModuleAudit::getItemId, itemId).oneOpt();
            if (itemDrawerModuleAuditOptional.isPresent()) {
                log.info("商品已存在新版本审核数据，跳过处理 itemId={}", itemId);
                continue;
            }

            final List<NewGoods> newGoods = newGoodsService.lambdaQuery()
                    .eq(NewGoods::getItemId, itemId).list();

            if (CollectionUtil.isEmpty(newGoods)) {
                log.warn("商品未找到新品商品数据 itemId={}", itemId);
                continue;
            }

            //法务负责人
            Long legalId = newGoods.stream().map(NewGoods::getLegalId)
                    .filter(NumberUtil::isPositive).findAny().orElse(0L);

            //QC负责人
            final ItemProcurement itemProcurement = itemProcurementGateway.getProcurementByItemId(
                    itemId);
            Long qcId = Optional.ofNullable(itemProcurement).map(ItemProcurement::getQcIds)
                    .filter(StringUtil::isNotBlank)
                    .map(v -> StringUtil.splitTrim(v, ",").stream().map(Long::parseLong)
                            .collect(Collectors.toList()))
                    .flatMap(qcIds -> qcIds.stream().findFirst())
                    .orElse(0L);

            long legalAuditStart = 0L;
            long legalAuditEnd = 0L;
            long qcAuditStart = 0L;
            long qcAuditEnd = 0L;

            try {
                final Optional<TaskInstance> legalTaskOptional = getTaskInstance(processId, "法务");
                if (!legalTaskOptional.isPresent()) {
                    log.error("审核数据迁移：炎黄数据异常，法务审核任务未找到 itemId={} progressId={}", itemId,
                            processId);
                    continue;
                }
                final TaskInstance legalTask = legalTaskOptional.get();

                //从炎黄任务获取法务审批人登录名
                final String legalLoginName = legalTask.getTarget();
                final Optional<Long> awsLegalIdOptional = staffService.getStaffListByLoginName(
                                Collections.singletonList(legalLoginName)).stream().findAny()
                        .map(DadStaffVO::getUserId);
                if (awsLegalIdOptional.isPresent()) {
                    legalId = awsLegalIdOptional.get();
                }

                final Function<Timestamp, Long> timestampLongFunction = v -> v.toInstant()
                        .getEpochSecond();

                //法务审核开始时间
                legalAuditStart = Optional.ofNullable(legalTask.getBeginTime())
                        .map(timestampLongFunction).orElse(0L);

                //法务审核结束时间
                legalAuditEnd = Optional.of(legalTask)
                        .filter(v -> v instanceof HistoryTaskInstance)
                        .map(v -> ((HistoryTaskInstance) v).getEndTime())
                        .map(timestampLongFunction)
                        .orElse(0L);

                final Optional<TaskInstance> qcTaskOptional = getTaskInstance(processId, "QC");
                if (qcTaskOptional.isPresent()) {
                    //QC审核节点经办人
                    final Optional<Long> awsQcIdOptional = qcTaskOptional.map(
                                    TaskInstance::getTarget)
                            .flatMap(v -> staffService.getStaffListByLoginName(
                                            Collections.singletonList(v)).stream().findAny()
                                    .map(DadStaffVO::getUserId));
                    if (awsQcIdOptional.isPresent()) {
                        qcId = awsQcIdOptional.get();
                    }

                    //QC审核开始时间
                    qcAuditStart = qcTaskOptional.map(AbstTaskInstanceModel::getBeginTime)
                            .map(timestampLongFunction).orElse(0L);

                    //QC审核结束时间
                    qcAuditEnd = qcTaskOptional
                            .filter(v -> v instanceof HistoryTaskInstance)
                            .map(v -> ((HistoryTaskInstance) v).getEndTime()).map(
                                    timestampLongFunction).orElse(0L);
                }
            } catch (Throwable e) {
                log.error("审核数据迁移:炎黄影动API返回异常:{} itemId:{}", e.getMessage(),
                        itemId);
                continue;
            }

            final ItemDrawerModuleAudit itemDrawerModuleAudit = new ItemDrawerModuleAudit();
            itemDrawerModuleAudit.setItemId(itemId);
            itemDrawerModuleAudit.setImgInfo(true);
            itemDrawerModuleAudit.setTextAndAttr(true);
            itemDrawerModuleAudit.setLiveVerbalTrick(false);
            itemDrawerModuleAudit.setDetails(true);
            itemDrawerModuleAudit.setRound(1);
            itemDrawerModuleAudit.setAuditStatus(qcAuditEnd > 0 ? ItemAuditStatus.FINISHED
                    : (legalAuditEnd > 0 ? ItemAuditStatus.WAIT_QC_AUDIT
                            : ItemAuditStatus.WAIT_LEGAL_AUDIT));
            itemDrawerModuleAuditService.save(itemDrawerModuleAudit);
            log.info("商品审核主记录已创建 itemId={}", itemId);

            final ItemDrawerModuleAuditTask legalTask = new ItemDrawerModuleAuditTask();
            legalTask.setItemId(itemId);
            legalTask.setNode(ItemLaunchProcessNodeId.LEGAL.getValue());
            legalTask.setRound(itemDrawerModuleAudit.getRound());
            legalTask.setAuditStatus(legalAuditEnd > 0 ? ItemDrawerAuditTaskState.DONE
                    : ItemDrawerAuditTaskState.PENDING);
            legalTask.setProcessorId(legalId);
            legalTask.setAuditAt(qcAuditEnd);
            Optional.of(legalAuditStart).filter(NumberUtil::isPositive)
                    .ifPresent(legalTask::setCreatedAt);
            Optional.of(legalAuditEnd).filter(NumberUtil::isPositive)
                    .ifPresent(legalTask::setUpdatedAt);
            itemDrawerModuleAuditTaskService.save(legalTask);
            log.info("商品法务审核任务数据已创建 itemId={} auditStatus={} legalId={} legalTaskId={}",
                    itemId, legalTask.getAuditStatus().getDesc(), legalId,
                    legalTask.getId());

            if (legalAuditEnd > 0) {
                final ItemDrawerModuleAuditTask qcTask = new ItemDrawerModuleAuditTask();
                qcTask.setItemId(itemId);
                qcTask.setNode(ItemLaunchProcessNodeId.QC.getValue());
                qcTask.setRound(itemDrawerModuleAudit.getRound());
                qcTask.setAuditStatus(qcAuditEnd > 0 ? ItemDrawerAuditTaskState.DONE
                        : ItemDrawerAuditTaskState.PENDING);
                qcTask.setAuditAt(qcAuditEnd);
                qcTask.setProcessorId(qcId);
                Optional.of(qcAuditStart).filter(NumberUtil::isPositive)
                        .ifPresent(qcTask::setCreatedAt);
                Optional.of(qcAuditEnd).filter(NumberUtil::isPositive)
                        .ifPresent(qcTask::setUpdatedAt);
                itemDrawerModuleAuditTaskService.save(qcTask);
                log.info("商品QC审核任务已创建 itemId={} qcId={} qcTask={}", itemId, qcId,
                        qcTask.getId());
            }

            final ItemDrawerModuleAuditStats moduleAuditStats = itemDrawerModuleAuditStatsService.getModuleAuditStatsCreateIfNotExists(
                    ItemAuditType.ITEM_MATERIAL, itemId, itemDrawerModuleAudit.getRound());
            moduleAuditStats.setLegalAuditUid(legalId);
            moduleAuditStats.setLegalAuditStartTime(legalAuditStart);
            moduleAuditStats.setLegalAuditEndTime(legalAuditEnd);
            moduleAuditStats.setLegalAuditCostTime(Math.max(0, (long) NumberUtil.sub(
                    legalAuditEnd,
                    legalAuditStart
            )));

            if (legalAuditEnd > 0) {
                moduleAuditStats.setQcAuditUid(qcId);
                moduleAuditStats.setQcAuditStartTime(qcAuditStart);
                moduleAuditStats.setQcAuditEndTime(qcAuditEnd);
                moduleAuditStats.setQcAuditCostTime(
                        Math.max(0, (long) NumberUtil.sub(qcAuditEnd, qcAuditStart))
                );
            }
            itemDrawerModuleAuditStatsService.updateById(moduleAuditStats);
            log.info("商品模块审核统计数据已更新 itemId={} model={}", itemId, moduleAuditStats);
        }
    }

    private void handleAdviceMigrate(Long itemId,
            List<ItemLaunchModuleAdvice> itemLaunchModuleAdvices,
            List<ItemApprovalAdvice> itemApprovalAdvices, ItemLaunchProcessNodeId node,
            Pair<List<ItemApprovalAdviceType>, ItemDrawerModuleId> mapping) {
        if (CollectionUtil.isEmpty(itemApprovalAdvices)) {
            return;
        }
        final List<ItemApprovalAdvice> legalApprovalAdvices = itemApprovalAdvices.stream()
                .filter(v -> (
                        mapping.getLeft().contains(
                                IEnum.getEnumByValue(ItemApprovalAdviceType.class, v.getType()))
                )).filter(v -> {
                    if (node == ItemLaunchProcessNodeId.LEGAL) {
                        return LEGAL_IDS.contains(v.getCreatedUid());
                    } else {
                        return !LEGAL_IDS.contains(v.getCreatedUid());
                    }
                }).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(legalApprovalAdvices)) {
            return;
        }

        final Optional<ItemLaunchModuleAdvice> legalTextAndAttrAdviceOptional = itemLaunchModuleAdvices.stream()
                .filter(v -> v.getNode() == node
                        && v.getModule() == mapping.getRight()).findFirst();

        final ItemLaunchModuleAdvice legalTextAndAttrAdviceRecord = legalTextAndAttrAdviceOptional.orElseGet(
                () -> {
                    final ItemLaunchModuleAdvice itemLaunchModuleAdvice = new ItemLaunchModuleAdvice();
                    itemLaunchModuleAdvice.setItemId(itemId);
                    itemLaunchModuleAdvice.setRound(1);
                    itemLaunchModuleAdvice.setNode(node);
                    itemLaunchModuleAdvice.setModule(mapping.getRight());
                    return itemLaunchModuleAdvice;
                });

        final String hisLegalAdviceContent = legalApprovalAdvices.stream()
                .map(ItemApprovalAdvice::getAdvice)
                .collect(Collectors.joining("、"));
        if (!StringUtil.contains(legalTextAndAttrAdviceRecord.getAdvice(), hisLegalAdviceContent)) {
            legalTextAndAttrAdviceRecord.setAdvice(
                    StringUtil.format("{} <br/> {}", hisLegalAdviceContent,
                            Optional.ofNullable(legalTextAndAttrAdviceRecord.getAdvice())
                                    .orElse("")));
            if (NumberUtil.isZeroOrNull(legalTextAndAttrAdviceRecord.getCreatedAt())) {
                legalTextAndAttrAdviceRecord.setCreatedAt(
                        legalApprovalAdvices.stream()
                                .map(ItemApprovalAdvice::getCreatedAt)
                                .min(Comparator.comparingLong(v -> v))
                                .orElse(DateUtil.currentTime()));
            }
            if (NumberUtil.isZeroOrNull(legalTextAndAttrAdviceRecord.getCreatedUid())) {
                legalTextAndAttrAdviceRecord.setCreatedUid(
                        legalApprovalAdvices.stream().map(ItemApprovalAdvice::getCreatedUid)
                                .findFirst()
                                .orElse(0L));
            }
            itemLaunchModuleAdviceService.saveOrUpdate(legalTextAndAttrAdviceRecord);
        }
    }

    private Optional<TaskInstance> getTaskInstance(String processId, String type) {
        final List<TaskInstance> taskInstances = taskCache.get(processId);
        assert taskInstances != null;
        return taskInstances.stream()
                .filter(v -> v.getTitle().contains(type) && !v.getControlState()
                        .equals("delete")).findFirst();
    }

    private List<TaskInstance> getTaskInstances(String processId) {
        final ArrayList<TaskInstance> taskInstances = Lists.newArrayList();
        Optional.ofNullable(awsApprovalFlowService.taskQuery(
                processId)).ifPresent(taskInstances::addAll);
        Optional.ofNullable(awsApprovalFlowService.historyTaskQuery(
                processId)).ifPresent(taskInstances::addAll);
        return taskInstances;
    }
}
