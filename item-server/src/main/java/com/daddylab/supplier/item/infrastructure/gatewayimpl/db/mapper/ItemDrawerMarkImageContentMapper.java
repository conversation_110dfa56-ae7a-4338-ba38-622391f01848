package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMarkImageContent;
import java.util.List;

/**
 * <p>
 * 标注图片内容 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
public interface ItemDrawerMarkImageContentMapper extends
        DaddyBaseMapper<ItemDrawerMarkImageContent> {

    /**
     * 查询抽屉ID关联的标记内容列表，忽略逻辑删除标记
     *
     * @param drawerId 抽屉ID
     */
    List<ItemDrawerMarkImageContent> selectListIgnoreDeletedMarkByDrawerId(Long drawerId);

}
