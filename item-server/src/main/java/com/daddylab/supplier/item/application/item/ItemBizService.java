package com.daddylab.supplier.item.application.item;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.ark.sailor.item.domain.vo.category.CategoryListVO;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuVO;
import com.daddylab.supplier.item.controller.item.ShortSkuVO;
import com.daddylab.supplier.item.controller.item.dto.*;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailPriceVo;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailRunningVo;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailVo;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemCmd;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemConfirmCmd;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemConfirmVo;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemVo;
import com.daddylab.supplier.item.domain.platformItem.service.event.PlatformItemUpdateEvent;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemResp;
import com.daddylab.supplier.item.types.item.MiniProgramCategoryListQuery;
import com.daddylab.supplier.item.types.partner.CooperateModeNotify;
import com.daddylab.supplier.item.types.partner.QcChangeNotify;

import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/9 3:01 下午
 * @description
 */
public interface ItemBizService {

  /**
   * 商品名字下拉选
   *
   * @param name 商品名字模糊查询
   * @return
   */
  MultiResponse<NameDropDownVo> nameDropDownList(
      String name,
      Integer pageIndex,
      Integer pageSize,
      Boolean useForItemLaunchPlan,
      Long providerId);

  /**
   * 商品列表分页查询
   *
   * @param pageQuery 分页查询封装
   * @return 页显示封装
   */
  PageResponse<ItemPageVo> queryPage(ItemPageQuery pageQuery);

  /**
   * 查询商品简介信息
   *
   * @param cmd
   * @return
   */
  MultiResponse<ItemSimpleViewVo> querySimple(ItemSimpleViewCmd cmd);

  /**
   * 查看商品详情
   *
   * @param itemId 商品id
   * @return com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailVo
   */
  SingleResponse<ItemDetailVo> queryDetail(Long itemId);

  /**
   * 查看商品运营信息
   *
   * @param itemId
   * @return
   */
  SingleResponse<ItemDetailRunningVo> queryRunningDetail(Long itemId);

  /**
   * 查看商品价格信息
   *
   * @param itemId
   * @return
   */
  SingleResponse<ItemDetailPriceVo> queryPrice(Long itemId);

  /**
   * 删除价格
   *
   * @param priceId item_price id
   * @return
   */
  Response removePrice(Long priceId);

  /**
   * 新建商品
   *
   * @param cmd 商品新建入参
   */
  Response saveItem(SaveItemCmd cmd) throws Exception;

  /**
   * 查询商品快递模板
   *
   * @param itemId 商品id
   * @return com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemExpressTemplate
   */
  MultiResponse<ItemExpressVo> getItemExpress(Long itemId);

  PageResponse<ItemSkuSpecVo> getSpecList(ItemSpecPageQuery query);

  Response updateRunning(EditRunningCmd cmd);

  /**
   * 导出商品
   *
   * @param cmd 导出商品入参
   * @return ok
   */
  Response exportItem(ExportCmd cmd);

  /**
   * 查询供应商系统的商品
   *
   * @param cmd
   * @return
   */
  MultiResponse<PartnerItemVo> queryPartnerItem(PartnerItemCmd cmd);

  /**
   * 确定 供应商系统的商品，选中后，后端自动新建，品牌，供应商信息，
   *
   * @param cmd
   */
  SingleResponse<PartnerItemConfirmVo> confirmPartnerItem(PartnerItemConfirmCmd cmd);

  /** 是否存在品牌关联商品 */
  boolean existBrandAssociativeItem(Long brandId);

  /** 查询商品明细列表（出库） */
  PageResponse<ComposeSkuVO> pageSku(ComposeSkuPageQuery query);

  /**
   * 商品状态变更
   *
   * @param itemId
   * @param itemLaunchStatus
   */
  Boolean updateLaunchStatus(Long itemId, ItemLaunchStatus itemLaunchStatus);

  /** 商品审核状态变更 */
  Boolean updateAuditStatus(Long itemId, ItemAuditStatus itemAuditStatus);

  /**
   * 更新商品状态 CAS
   *
   * @param itemId 商品ID
   * @param itemStatus 更新商品状态
   * @param expectStatus 期望当前商品状态
   * @return 是否更新成功
   */
  boolean updateStatusById(Long itemId, ItemStatus itemStatus, ItemStatus expectStatus);

  boolean updateStatusById(Long itemId, ItemStatus itemStatus);

  /**
   * 更新商品上新时间
   *
   * @param itemIds 商品ID
   * @param launchTime 上新时间
   */
  Boolean updateLaunchTime(List<Long> itemIds, Long launchTime);

  /**
   * 重置后端商品的上新状态到待选择，且清空预计上新时间
   *
   * @param itemIds 商品ID
   */
  Boolean resetLaunchState(List<Long> itemIds);

  /**
   * 合作伙伴系统商品QC负责人修改通知
   *
   * @param qcChangeNotify 合作伙伴系统通知
   */
  void qcChange(QcChangeNotify qcChangeNotify);

  //    /**
  //     * 重新将满足条件的sku推送到金蝶
  //     *
  //     * @param restartSyncSkuCmd 推送目标筛选参数
  //     * @return 推送成本的sku数量
  //     */
  //    void restartSyncSku(RestartSyncSkuCmd restartSyncSkuCmd);
  //
  //    void importExcelForProvider(@Param("inputStream") InputStream inputStream);

  MultiResponse<ItemSkuPriceVO> skuPriceList(String skuCode, Integer type);

  MultiResponse<ItemPriceVO> itemCostPriceList(Long itemId, String priceName);

  Boolean deletePartnerSn(Long itemId);

  /**
   * 获取商品的规格信息
   *
   * @param itemId 商品 ID
   * @return SingleResponse<ItemSkuDetailVo>
   */
  SingleResponse<ItemSkuDetailVo> getItemSkuDetail(Long itemId);

  PageResponse<ItemMatchMiniProgramVo> itemMatchMiniProgramV1(ItemMatchMiniProgramParam param);

  /**
   * 商品匹配小程序查询
   *
   * @param param 参数
   * @return PageResponse<ItemMatchMiniProgramVo>
   */
  PageResponse<ItemMatchMiniProgramVo> itemMatchMiniProgram(ItemMatchMiniProgramParam param);

  /**
   * 商品同步到小程序
   *
   * @param params 参数
   * @return SingleResponse<Boolean>
   */
  SingleResponse<Boolean> itemSyncMiniProgram(List<ItemSyncMiniProgramParam> params);

  Map<Long, PartnerItemResp> queryPartnerItemBatch(List<Long> itemIds);

  default Optional<PartnerItemResp> queryPartnerItemByItemId(Long itemId) {
    return Optional.ofNullable(
        queryPartnerItemBatch(Collections.singletonList(itemId)).get(itemId));
  }

  SingleResponse<ShortSkuVO> querySku(String skuCode);

  SingleResponse<Boolean> deleteSku(String codes);

  SingleResponse<Boolean> deleteSku(List<String> codes);

  SingleResponse<Boolean> deleteSkuByIds(List<Long> skuIds);

  /** 将P系统中的商品技术类目数据，同步到ERP对应的商品中 */
  void queryAllItemPartnerSysType();

  void syncPsysItemInfo();

  /**
   * 修改商品状态
   *
   * @param itemId 商品ID
   * @param newStatus 新商品状态
   * @param remark 操作备注
   */
  Response updateStatus(
      Long itemId, ItemStatus newStatus, Long downFrameTime, String downFrameReason, String remark);

  void listenPlatformItemUpdate(PlatformItemUpdateEvent event);

  MultiResponse<CategoryListVO> getMiniProgramCategoryList(
      MiniProgramCategoryListQuery categoryQuery);

  void cooperateMode(CooperateModeNotify notify);

  PageResponse<SpuWindowPageVo> spuWindowsPageQuery(SpuWindowPageQuery pageQuery);

  void notifyBuyerOrQcChange(Long itemId);

  void importExcel(InputStream inputStream);
}
