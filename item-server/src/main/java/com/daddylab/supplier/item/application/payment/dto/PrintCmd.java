package com.daddylab.supplier.item.application.payment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年04月23日 2:52 PM
 */
@Data
@ApiModel("打印付款单请求参数封装")
public class PrintCmd {

    @ApiModelProperty("付款单ID")
    @NotEmpty(message = "付款单ID数组不得为空")
    List<Long> paymentOrderIds;
}
