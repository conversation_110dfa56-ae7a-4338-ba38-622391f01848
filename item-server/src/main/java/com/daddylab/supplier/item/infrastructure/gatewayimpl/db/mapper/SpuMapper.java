package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceQuery;
import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceSku;
import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceSpu;
import com.daddylab.supplier.item.application.itemReference.types.SpuSkuRef;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Spu;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 标准销售单元（由于金蝶缺少SPU，导致我们存在大量历史商品被拆分为"散装"的SKU，这张表的目的就是将这些散装的SKU重新聚合） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
public interface SpuMapper extends DaddyBaseMapper<Spu> {

    /**
     * 商品参照表SPU维度查询计数
     *
     * @param query 查询条件封装
     * @return 总数
     */
    long itemReferenceSpuQueryCount(@Param("query") ItemReferenceQuery query);

    /**
     * 商品参照表SPU维度查询
     *
     * @param query 查询条件封装
     * @return 商品参照表SPU
     */
    List<ItemReferenceSpu> itemReferenceSpuQuery(@Param("query") ItemReferenceQuery query);

    /**
     * 商品参照表SKU维度查询计数
     *
     * @param query 查询条件封装
     * @return 总数
     */
    long itemReferenceSkuQueryCount(@Param("query") ItemReferenceQuery query);

    /**
     * 商品参照表SKU维度查询
     *
     * @param query 查询条件封装
     * @return 商品参照表SKU
     */
    List<ItemReferenceSku> itemReferenceSkuQuery(@Param("query") ItemReferenceQuery query);

    /**
     * 根据SKU编码批量查询与SPU的关联关系
     *
     * @param skuCodes SKU编码
     * @return 关联关系
     */
    List<SpuSkuRef> refBatchQueryBySkuCodes(@Param("skuCodes") Collection<String> skuCodes);

    /**
     * 保存 SPU 编码，如果已经存在则跳过
     *
     * @param itemCodes 商品编码
     * @param uid       操作用户ID
     * @return 更新记录数量
     */
    int saveSpuAndIgnoreIfExist(@Param("itemCodes") Collection<String> itemCodes,
            @Param("uid") Long uid);

    /**
     * 重新关联SPU和SKU
     *
     * @param refs SKU和SPU的关联关系
     * @param uid  操作用户ID
     * @return 更新记录数量
     */
    int updateItemSkuRef(@Param("refs") Collection<SpuSkuRef> refs, @Param("uid") Long uid);

    /**
     * 更新SPU状态（将不存在关联SKU的SPU删除）
     *
     * @param itemCodes 商品编码集合
     * @param uid       操作用户ID
     * @return 变更记录数量
     */
    int updateSpuState(@Param("itemCodes") Collection<String> itemCodes, @Param("uid") Long uid);

    /**
     * 同步后端商品新增的SKU保存到SKU表
     *
     * @param itemCodes 商品编码
     * @param uid       操作用户ID
     * @return 变更记录数量
     */
    int saveNewItem(@Param("itemCodes") Collection<String> itemCodes,
            @Param("uid") Long uid);

    /**
     * 同步后端商品新增的SKU保存到SKU表
     *
     * @param itemCodes 商品编码
     * @param uid       操作用户ID
     * @param adjustItemCode 调整SKU关联的SPU编码
     * @return 变更记录数量
     */
    int saveNewItemSku(@Param("itemCodes") Collection<String> itemCodes,
            @Param("uid") Long uid, @Param("adjustItemCode") String adjustItemCode);

    /**
     * 移除掉已经失效的SKU编码
     *
     * @param itemCodes 商品编码
     * @param uid       操作用户ID
     * @return 变更记录数量
     */
    int removeInvalidItemSku(@Param("itemCodes") Collection<String> itemCodes,
            @Param("uid") Long uid);

    /**
     * 如果一个商品是历史遗留商品，那么它可能在参照库调整过SPU编码，这种商品在新增SKU时需获取到调整后的SPU编码再特殊处理
     * @param itemCode 后端商品编码
     * @return 调整编码
     */
    String getAdjustItemCode(@Param("itemCode") String itemCode);

}
