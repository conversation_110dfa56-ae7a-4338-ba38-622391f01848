package com.daddylab.supplier.item.application.afterSaleLogistics.monitor;

import static com.daddylab.supplier.item.common.GlobalConstant.LOGISTICS_REMOTE_DISTRICTS;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsException;
import com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsRootException;
import com.daddylab.supplier.item.application.afterSaleLogistics.domain.LogisticsTraceData;
import com.daddylab.supplier.item.application.afterSaleLogistics.domain.LogisticsTraceItem;
import com.daddylab.supplier.item.application.afterSaleLogistics.dto.LogisticExceptionRes;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.ErpLogisticsStatus;
import com.daddylab.supplier.item.domain.region.gateway.RegionGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IKdyCallbackService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 24/48/72/96小时，此时时间和最新一条物流轨迹时间 偏远地区（加1天）：新疆、西藏、内蒙古、青海、宁夏、甘肃、海南、云南； 疑似丢件（物流停滞7天没有更新快递轨迹）
 *
 * <AUTHOR> up
 * @date 2024年05月22日 10:03 AM
 */
@Order(MonitorOrder.SendExceptionMonitor)
@Slf4j
@Component
@AllArgsConstructor
public class SendExceptionMonitor implements OrderLogisticsExceptionMonitor {

  private final IWdtOrderService wdtOrderService;
  private final IKdyCallbackService iKdyCallbackService;
  private final RegionGateway regionGateway;
  private static final String PICK_UP_SHORT_CODE = "收件";

  @Override
  public LogisticExceptionRes process(ProcessContext context) {
    final Long currentTurnTime = context.getTimestamp();
    LogisticExceptionRes res = new LogisticExceptionRes();

    final LogisticsTraceData logisticsTraceData = context.getLogisticsTraceData();
    final ErpLogisticsStatus revisedLogisticsStatus = context.getRevisedLogisticsStatus();
    if (logisticsTraceData != null
            && Arrays.asList(
                    ErpLogisticsStatus.SENT,
                    ErpLogisticsStatus.PICKUP,
                    ErpLogisticsStatus.IN_TRANSIT,
                    ErpLogisticsStatus.DELIVERING,
                    ErpLogisticsStatus.DIFFICULT)
            .contains(revisedLogisticsStatus)) {
      if (CollUtil.isNotEmpty(logisticsTraceData.getTrackList())) {
        boolean inRemote = inRemoteProvince(logisticsTraceData);
        final LogisticsException logisticsException =
                calculationTimeInterval(currentTurnTime, logisticsTraceData.getTrackTime(), inRemote);
        if (Objects.nonNull(logisticsException)) {
          res.setRootException(LogisticsRootException.SEND_EXCEPTION);
          res.setSubException(logisticsException);
          return res;
        }
      }
    }
    res.setRootException(LogisticsRootException.NORMAL);
    return res;
  }

  private Boolean inRemoteProvince(LogisticsTraceData logisticsTraceData) {
    if (logisticsTraceData != null) {
      for (String keyword : LOGISTICS_REMOTE_DISTRICTS) {
        if (StringUtils.contains(logisticsTraceData.getReceiverAddress(), keyword)) {
          return true;
        }
        for (LogisticsTraceItem logisticsTraceItem : logisticsTraceData.getTrackList()) {
          if (logisticsTraceItem.getTrackStatus().contains(keyword)) {
            return true;
          }
        }
      }
    }
    return false;
  }

  private static LogisticsException calculationTimeInterval(
      Long currentTime, Long maxTimestamp, Boolean inRemote) {
    LogisticsException exceed = null;

    final long aLong = DateUtil.calculateDifference(currentTime, maxTimestamp, TimeUnit.HOURS);
    if (aLong > 168) {
      exceed = LogisticsException.SEND_7D;
      return exceed;
    } else {
      if (inRemote) {
        if (aLong > 96) {
          exceed = LogisticsException.SEND_96H;
          return exceed;
        }
        if (aLong > 72) {
          exceed = LogisticsException.SEND_72H;
          return exceed;
        }
        if (aLong > 48) {
          exceed = LogisticsException.SEND_48H;
          return exceed;
        }
      } else {
        if (aLong > 72) {
          exceed = LogisticsException.SEND_72H;
          return exceed;
        }
        if (aLong > 48) {
          exceed = LogisticsException.SEND_48H;
          return exceed;
        }
        if (aLong > 24) {
          exceed = LogisticsException.SEND_24H;
          return exceed;
        }
      }
    }
    return exceed;
  }
}
