package com.daddylab.supplier.item.application.entryActivityPrice;

import cn.hutool.core.collection.CollectionUtil;
import com.daddylab.supplier.item.application.entryActivityPrice.models.OpenEntryActivityPriceQuery;
import com.daddylab.supplier.item.application.entryActivityPrice.models.OpenEntryActivityPriceVO;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CombinationItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/11/15
 */
@Service
public class OpenEntryActivityPriceServiceImpl implements OpenEntryActivityPriceService {
  @Resource private IEntryActivityPriceSkuService entryActivityPriceSkuService;
  @Resource private IEntryActivityPriceItemService entryActivityPriceItemService;
  @Resource private IItemSkuService itemSkuService;
  @Resource private IItemService itemService;
  @Resource private ICombinationItemService combinationItemService;

  @Override
  public List<OpenEntryActivityPriceVO> query(OpenEntryActivityPriceQuery query) {
    if (CollectionUtil.isEmpty(query.getSkuCodes())) {
      return Collections.emptyList();
    }
    final ArrayList<OpenEntryActivityPriceVO> openEntryActivityPriceVOS = new ArrayList<>();
    final List<ItemSku> itemSkus = itemSkuService.selectByMixCodes(query.getSkuCodes());
    if (!CollectionUtil.isEmpty(itemSkus)) {
      final List<Long> skuIds = itemSkus.stream().map(ItemSku::getId).collect(Collectors.toList());
      final List<Long> itemIds =
          itemSkus.stream().map(ItemSku::getItemId).distinct().collect(Collectors.toList());
      final List<Item> items = itemService.listByIds(itemIds);
      final Map<Long, Item> itemMap =
          items.stream().collect(Collectors.toMap(Entity::getId, Function.identity()));
      final List<EntryActivityPriceSku> entryActivityPriceSkus =
          entryActivityPriceItemService.listConfirmedSkuBySkuIds(skuIds);
      for (String skuCode : query.getSkuCodes()) {
        for (final ItemSku itemSku : itemSkus) {
          if (skuCode.equals(itemSku.getSkuCode())
              || skuCode.equals(itemSku.getProviderSpecifiedCode())) {
            final OpenEntryActivityPriceVO openEntryActivityPriceVO =
                new OpenEntryActivityPriceVO();
            openEntryActivityPriceVO.setIsCombinationItem(false);
            final Item item = itemMap.get(itemSku.getItemId());
            if (item == null) {
              continue;
            }
            openEntryActivityPriceVO.setItemCode(item.getCode());
            openEntryActivityPriceVO.setSkuCode(skuCode);
            openEntryActivityPriceVO.setPlatformCommission(itemSku.getPlatformCommission());
            openEntryActivityPriceVO.setContractSalePrice(itemSku.getContractSalePrice());
            final List<OpenEntryActivityPriceVO.EntryActivityPrice> entryActivityPrices =
                entryActivityPriceSkus.stream()
                    .filter(v -> v.getSkuId().equals(itemSku.getId()))
                    .map(
                        v -> {
                          final OpenEntryActivityPriceVO.EntryActivityPrice entryActivityPrice =
                              new OpenEntryActivityPriceVO.EntryActivityPrice();
                          entryActivityPrice.setActiveStart(v.getActiveStart());
                          entryActivityPrice.setActiveEnd(v.getActiveEnd());
                          entryActivityPrice.setPlatformCommission(v.getPlatformCommission());
                          entryActivityPrice.setContractSalePrice(v.getContractSalePrice());
                          entryActivityPrice.setActivityRemark(v.getActivityRemark());
                          return entryActivityPrice;
                        })
                    .collect(Collectors.toList());
            openEntryActivityPriceVO.setEntryActivityPrices(entryActivityPrices);
            openEntryActivityPriceVOS.add(openEntryActivityPriceVO);
            break;
          }
        }
      }
    }
    final List<CombinationItem> combinationItems =
        combinationItemService.listByCombinationCode(query.getSkuCodes());
    if (!combinationItems.isEmpty()) {
      for (CombinationItem combinationItem : combinationItems) {
        final OpenEntryActivityPriceVO openEntryActivityPriceVO = new OpenEntryActivityPriceVO();
        openEntryActivityPriceVO.setItemCode(combinationItem.getCode());
        openEntryActivityPriceVO.setSkuCode(combinationItem.getCode());
        openEntryActivityPriceVO.setPlatformCommission(combinationItem.getPlatformCommission());
        openEntryActivityPriceVO.setContractSalePrice(combinationItem.getContractSalePrice());
        openEntryActivityPriceVO.setIsCombinationItem(true);
        openEntryActivityPriceVO.setEntryActivityPrices(Collections.emptyList());
        openEntryActivityPriceVOS.add(openEntryActivityPriceVO);
      }
    }
    return openEntryActivityPriceVOS;
  }
}
