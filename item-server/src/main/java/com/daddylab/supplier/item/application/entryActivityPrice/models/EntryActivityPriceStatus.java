package com.daddylab.supplier.item.application.entryActivityPrice.models;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/10/24
 */
@AllArgsConstructor
@Getter
public enum EntryActivityPriceStatus implements IIntegerEnum {
    //状态 -1 待发起 0 待确认 1 已确认 2 存在异议
    TO_BE_STARTED(-1, "待发起"),
    TO_BE_CONFIRMED(0, "待确认"),
    CONFIRMED(1, "已确认"),
    HAS_DISSENT(2, "存在异议");

    private final Integer value;
    private final String desc;
}
