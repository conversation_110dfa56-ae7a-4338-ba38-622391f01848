package com.daddylab.supplier.item.application.saleItem;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * nacos 里配置的数据解析
 */
@Data
@Component
public class ShopSnData {

    @Value("${shop.sn.memberStore}")
    private String memberStoreSn;

    @Value("${shop.sn.applets}")
    private String appletsSn;

    @Value("${shop.sn.beautyStore}")
    private String beautyStoreSn;

    @Value("${shop.sn.maternityBaby}")
    private String maternityBabySn;

    @Value("${shop.sn.wotoBuy}")
    private String wotoBuySn;

    @Value("${shop.sn.tikTok}")
    private String tikTokSn;
}
