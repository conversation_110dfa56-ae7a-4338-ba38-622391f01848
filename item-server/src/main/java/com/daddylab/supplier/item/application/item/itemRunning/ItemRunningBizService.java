package com.daddylab.supplier.item.application.item.itemRunning;

import com.daddylab.supplier.item.application.item.itemRunning.vo.ItemRunningVo;
import com.daddylab.supplier.item.controller.item.dto.EditRunningCmd;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ItemRunningBizService.java
 * @description
 * @createTime 2022年04月29日 16:52:00
 */
public interface ItemRunningBizService {

    /**
     * 保存或修改
     * @param cmd
     */
    void insert(EditRunningCmd cmd);

    List<ItemRunningVo> getList(Long itemId);
}
