package com.daddylab.supplier.item.application.message.wechat.v2.statics;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.drawer.ItemDrawerMergeBizService;
import com.daddylab.supplier.item.application.itemOptimize.ItemOptimizeBizService;
import com.daddylab.supplier.item.application.message.QyMsgSendService;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsPrincipalsInfo;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MsgTemplateCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemTrainingMaterialsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.item.ItemLaunchStatusObj;
import com.daddylab.supplier.item.types.item.ItemLiveVerbalTrickStatusObj;
import com.daddylab.supplier.item.types.item.ItemTrainingMaterialsStatusObj;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizeStatus;
import com.daddylab.supplier.item.types.itemTrainingMaterials.ItemTrainingMaterialsStatus;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListValuedMap;
import org.apache.commons.collections4.MultiMapUtils;
import org.apache.commons.collections4.multimap.HashSetValuedHashMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2024年05月14日 2:13 PM
 */
@AllArgsConstructor
@Service
@Slf4j
public class StaticsMsgBizServiceImpl implements StaticsMsgBizService {

    final IMsgTemplateService msgTemplateService;
    final NewGoodsBizService newGoodsBizService;
    final IWechatMsgService iWechatMsgService;
    final ItemOptimizeBizService itemOptimizeBizService;
    final StaffService staffService;
    final QyMsgSendService qyMsgSendService;
    final Environment environment;
    final MsgSender msgSender;
    final IItemOptimizeService itemOptimizeService;
    final ItemMapper itemMapper;
    final IItemSkuService iItemSkuService;
    final IItemDrawerLiveVerbalService iItemDrawerLiveVerbalService;
    final IItemDrawerService itemDrawerService;
    final ItemTrainingMaterialsMapper itemTrainingMaterialsMapper;
    final IItemTrainingMaterialsService iItemTrainingMaterialsService;
    final HandingSheetService handingSheetService;

    @Autowired
    private ItemDrawerMergeBizService itemDrawerMergeBizService;

    private List<Long> M001() {
        List<QwMsgStaticsBo> userIdAndItemIdsMap = itemMapper.staticsItemToBeImprove();
        List<Map<String, Object>> paramMaps = new LinkedList<>();
        List<Long> ids = new LinkedList<>();

        userIdAndItemIdsMap.forEach(bo -> {
            Long userId = bo.getUserId();
            String itemIds = bo.getItemIds();

            Optional<StaffBrief> staffBrief = staffService.getStaffBrief(userId);
            if (!staffBrief.isPresent()) {
                return;
            }
            if (StrUtil.isBlank(itemIds)) {
                return;
            }
            List<Long> itemIdList = Arrays.stream(itemIds.split(StrUtil.COMMA)).map(Long::valueOf).distinct().collect(Collectors.toList());
            Integer skuCount = iItemSkuService.lambdaQuery().in(ItemSku::getItemId, itemIdList).count();
            Map<String, Object> fillParamMap = new HashMap<>(4);
            fillParamMap.put("c1", itemIdList.size());
            fillParamMap.put("c2", skuCount);
            fillParamMap.put("采购负责人", staffBrief.get().getQwUserId());
            fillParamMap.put("lp", "status=2&itemId=" + itemIds);
            paramMaps.add(fillParamMap);
        });

        for (Map<String, Object> paramMap : paramMaps) {
            long id = qyMsgSendService.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.M001, paramMap);
            ids.add(id);
        }
        return ids;
    }

    private List<Long> M002() {
        List<QwMsgStaticsBo> userIdAndItemIdsMap = itemMapper.staticsItemToBeDesign();
        List<Map<String, Object>> paramMaps = new LinkedList<>();
        List<Long> ids = new LinkedList<>();

        userIdAndItemIdsMap.forEach(bo -> {
            Long userId = bo.getUserId();
            String itemIds = bo.getItemIds();

            Optional<StaffBrief> staffBrief = staffService.getStaffBrief(userId);
            if (!staffBrief.isPresent()) {
                return;
            }
            if (StrUtil.isBlank(itemIds)) {
                return;
            }
            List<Long> itemIdList = Arrays.stream(itemIds.split(StrUtil.COMMA)).map(Long::valueOf).distinct().collect(Collectors.toList());
            Map<String, Object> fillParamMap = new HashMap<>(4);
            fillParamMap.put("c1", itemIdList.size());
            fillParamMap.put("产品负责人", staffBrief.get().getQwUserId());
            fillParamMap.put("lp", "status=3&principalId=" + userId);
            paramMaps.add(fillParamMap);
        });

        for (Map<String, Object> paramMap : paramMaps) {
            long id = qyMsgSendService.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.M002, paramMap);
            ids.add(id);
        }
        return ids;

    }

    private List<Long> M003() {
        final List<ItemLaunchStatusObj> itemLaunchStatusObjs = itemMapper.selectByLaunchStatus(ItemLaunchStatus.TO_BE_AUDITED.getValue(),
                ItemAuditStatus.WAIT_LEGAL_AUDIT.getValue());
        final HashSetValuedHashMap<StaffBrief, ItemLaunchStatusObj> group = new HashSetValuedHashMap<>();
        for (ItemLaunchStatusObj itemLaunchStatusObj : itemLaunchStatusObjs) {
            final Long itemId = itemLaunchStatusObj.getItemId();
            final Long mergeItemId = itemLaunchStatusObj.getMergeItemId();
            final Long useItemId = Optional.ofNullable(mergeItemId).orElse(itemId);
            try {
                final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo = newGoodsBizService.getNewGoodsPrincipalsInfo(
                        useItemId).getData();
                if (CollectionUtil.isNotEmpty(newGoodsPrincipalsInfo.getLegalUsers())) {
                    for (StaffBrief user : newGoodsPrincipalsInfo.getLegalUsers()) {
                        group.put(user, itemLaunchStatusObj);
                    }
                }
            } catch (Exception e) {
                log.warn("[企微汇总消息][M003]查询新品商品负责人信息异常 item={}", itemLaunchStatusObj);
            }
        }
        List<Long> ids = new LinkedList<>();
        for (Map.Entry<StaffBrief, Collection<ItemLaunchStatusObj>> entry : group.asMap()
                .entrySet()) {
            final StaffBrief user = entry.getKey();
            final Collection<ItemLaunchStatusObj> items = entry.getValue();
            Map<String, Object> paramMap = new HashMap<>(4);
            paramMap.put("c1", items.size());
            paramMap.put("法务负责人", user.getQwUserId());
            paramMap.put("lp",
                    "status=41&itemId=" + items.stream()
                            .map(ItemLaunchStatusObj::getItemId)
                            .map(String::valueOf)
                            .collect(Collectors.joining(StrUtil.COMMA)));
            long id = qyMsgSendService.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.M003, paramMap);
            ids.add(id);
        }
        return ids;
    }

    private List<Long> M004() {
        final List<ItemLaunchStatusObj> itemLaunchStatusObjs = itemMapper.selectByLaunchStatus(ItemLaunchStatus.TO_BE_AUDITED.getValue(),
                ItemAuditStatus.WAIT_QC_AUDIT.getValue());
        final HashSetValuedHashMap<StaffBrief, ItemLaunchStatusObj> group = new HashSetValuedHashMap<>();
        for (ItemLaunchStatusObj itemLaunchStatusObj : itemLaunchStatusObjs) {
            final Long itemId = itemLaunchStatusObj.getItemId();
            final Long mergeItemId = itemLaunchStatusObj.getMergeItemId();
            final Long useItemId = Optional.ofNullable(mergeItemId).orElse(itemId);
            try {
                final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo = newGoodsBizService.getNewGoodsPrincipalsInfo(
                        useItemId).getData();
                if (CollectionUtil.isNotEmpty(newGoodsPrincipalsInfo.getQcUsers())) {
                    for (StaffBrief user : newGoodsPrincipalsInfo.getQcUsers()) {
                        group.put(user, itemLaunchStatusObj);
                    }
                }
            } catch (Exception e) {
                log.warn("[企微汇总消息][M004]查询新品商品负责人信息异常 item={}", itemLaunchStatusObj);
            }
        }
        List<Long> ids = new LinkedList<>();
        for (Map.Entry<StaffBrief, Collection<ItemLaunchStatusObj>> entry : group.asMap()
                .entrySet()) {
            final StaffBrief user = entry.getKey();
            final Collection<ItemLaunchStatusObj> items = entry.getValue();
            Map<String, Object> paramMap = new HashMap<>(4);
            paramMap.put("c1", items.size());
            paramMap.put("QC负责人", user.getQwUserId());
            paramMap.put("lp", "status=42&itemId=" + items.stream()
                    .map(ItemLaunchStatusObj::getItemId)
                    .map(String::valueOf)
                    .collect(Collectors.joining(StrUtil.COMMA)));
            long id = qyMsgSendService.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.M004, paramMap);
            ids.add(id);
        }
        return ids;
    }

    private List<Long> M005() {
        List<QwMsgStaticsBo> userIdAndItemIdsMap = itemMapper.staticsItemToBeEdit();
        List<Map<String, Object>> paramMaps = new LinkedList<>();
        List<Long> ids = new LinkedList<>();

        userIdAndItemIdsMap.forEach(bo -> {
            Long userId = bo.getUserId();
            String itemIds = bo.getItemIds();

            Optional<StaffBrief> staffBrief = staffService.getStaffBrief(userId);
            if (!staffBrief.isPresent()) {
                return;
            }
            if (StrUtil.isBlank(itemIds)) {
                return;
            }
            List<Long> itemIdList = Arrays.stream(itemIds.split(StrUtil.COMMA)).map(Long::valueOf).distinct().collect(Collectors.toList());
            Map<String, Object> fillParamMap = new HashMap<>(4);
            fillParamMap.put("c1", itemIdList.size());
            fillParamMap.put("产品负责人", staffBrief.get().getQwUserId());
            fillParamMap.put("产品组主管", environment.getProperty("msg-config.principalManager"));
            fillParamMap.put("lp", "status=5&itemId=" + itemIdList.stream().map(String::valueOf).collect(Collectors.joining(StrUtil.COMMA)));
            paramMaps.add(fillParamMap);
        });

        for (Map<String, Object> paramMap : paramMaps) {
            long id = qyMsgSendService.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.M005, paramMap);
            ids.add(id);
        }
        return ids;
    }

    // ------------------------------------------------------------------------------------------------------------------

    private Set<Long> queryItemIdsByStatus(ItemAuditStatus status) {
        Set<Long> matchedIdSet = new HashSet<>();
        List<Long> itemIdList = iItemDrawerLiveVerbalService.lambdaQuery()
                .eq(ItemDrawerLiveVerbal::getLiveVerbalTrickStatus, status)
                .select(ItemDrawerLiveVerbal::getItemId).list().stream()
                .map(ItemDrawerLiveVerbal::getItemId).collect(Collectors.toList());
        matchedIdSet.addAll(itemIdList);
        List<Long> itemIdList2 = itemDrawerService.lambdaQuery()
                .eq(ItemDrawer::getLiveVerbalTrickStatus, status).
                select(ItemDrawer::getItemId).list().stream().map(ItemDrawer::getItemId).collect(Collectors.toList());
        matchedIdSet.addAll(itemIdList2);

        final List<Long> mergeItemIds = itemDrawerMergeBizService.mainItemIdsToMergeItemIds(matchedIdSet);
        matchedIdSet.addAll(mergeItemIds);

        return matchedIdSet;
    }

    private List<Long> M006() {
        final List<ItemLiveVerbalTrickStatusObj> itemLiveVerbalTrickStatusObjs = itemMapper.selectByLiveVerbalTrickStatus(
                ItemAuditStatus.WAIT_LEGAL_AUDIT.getValue());
        final HashSetValuedHashMap<StaffBrief, ItemLiveVerbalTrickStatusObj> group = new HashSetValuedHashMap<>();
        for (ItemLiveVerbalTrickStatusObj itemLiveVerbalTrickStatusObj : itemLiveVerbalTrickStatusObjs) {
            final Long itemId = itemLiveVerbalTrickStatusObj.getItemId();
            final Long mergeItemId = itemLiveVerbalTrickStatusObj.getMergeItemId();
            final Long useItemId = Optional.ofNullable(mergeItemId).orElse(itemId);
            try {
                final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo = newGoodsBizService.getNewGoodsPrincipalsInfo(
                        useItemId).getData();
                final List<StaffBrief> users = newGoodsPrincipalsInfo.getLegalUsers();
                if (CollectionUtil.isNotEmpty(users)) {
                    for (StaffBrief user : users) {
                        group.put(user, itemLiveVerbalTrickStatusObj);
                    }
                }
            } catch (Exception e) {
                log.warn("[企微汇总消息][M006]查询新品商品负责人信息异常 item={}", itemLiveVerbalTrickStatusObj);
            }
        }

        final ArrayList<Long> ids = new ArrayList<>();
        for (Map.Entry<StaffBrief, Collection<ItemLiveVerbalTrickStatusObj>> entry : group.asMap()
                .entrySet()) {
            final StaffBrief user = entry.getKey();
            final Collection<ItemLiveVerbalTrickStatusObj> items = entry.getValue();
            final List<Long> distinctItemIds
                    = items.stream()
                    .map(ItemLiveVerbalTrickStatusObj::getItemId)
                    .distinct().collect(Collectors.toList());
            Map<String, Object> paramMap = new HashMap<>(4);
            paramMap.put("c1", distinctItemIds.size());
            paramMap.put("法务负责人", user.getQwUserId());
            paramMap.put("lp",
                    "liveVerbalTrickStatus=5&itemId=" +
                            distinctItemIds.stream().map(String::valueOf).collect(Collectors.joining(StrUtil.COMMA)));

            long id = qyMsgSendService.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.M006, paramMap);
            ids.add(id);
        }
        return ids;
    }

    private List<Long> M007() {
        final List<ItemLiveVerbalTrickStatusObj> itemLiveVerbalTrickStatusObjs = itemMapper.selectByLiveVerbalTrickStatus(
                ItemAuditStatus.WAIT_QC_AUDIT.getValue());
        final HashSetValuedHashMap<StaffBrief, ItemLiveVerbalTrickStatusObj> group = new HashSetValuedHashMap<>();
        for (ItemLiveVerbalTrickStatusObj itemLiveVerbalTrickStatusObj : itemLiveVerbalTrickStatusObjs) {
            final Long itemId = itemLiveVerbalTrickStatusObj.getItemId();
            final Long mergeItemId = itemLiveVerbalTrickStatusObj.getMergeItemId();
            final Long useItemId = Optional.ofNullable(mergeItemId).orElse(itemId);
            try {
                final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo = newGoodsBizService.getNewGoodsPrincipalsInfo(
                        useItemId).getData();
                final List<StaffBrief> users = newGoodsPrincipalsInfo.getQcUsers();
                if (CollectionUtil.isNotEmpty(users)) {
                    for (StaffBrief user : users) {
                        group.put(user, itemLiveVerbalTrickStatusObj);
                    }
                }
            } catch (Exception e) {
                log.warn("[企微汇总消息][M007]查询新品商品负责人信息异常 item={}", itemLiveVerbalTrickStatusObj);
            }
        }

        final ArrayList<Long> ids = new ArrayList<>();
        for (Map.Entry<StaffBrief, Collection<ItemLiveVerbalTrickStatusObj>> entry : group.asMap()
                .entrySet()) {
            final StaffBrief user = entry.getKey();
            final Collection<ItemLiveVerbalTrickStatusObj> items = entry.getValue();
            final List<Long> distinctItemIds
                    = items.stream()
                    .map(ItemLiveVerbalTrickStatusObj::getItemId)
                    .distinct().collect(Collectors.toList());
            Map<String, Object> paramMap = new HashMap<>(4);
            paramMap.put("c1", distinctItemIds.size());
            paramMap.put("QC负责人", user.getQwUserId());
            paramMap.put("lp",
                    "liveVerbalTrickStatus=10&itemId=" +
                            distinctItemIds.stream().map(String::valueOf).collect(Collectors.joining(StrUtil.COMMA)));
            long id = qyMsgSendService.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.M007, paramMap);
            ids.add(id);
        }
        return ids;
    }

    public static List<QwMsgStaticsBo> convert(List<QwMsgQcStaticsBo> paramList) {
        Map<Long, Set<String>> map = new HashMap<>(32);
        paramList.forEach(val -> {
            String userIds = val.getUserIds();
            String itemIds = val.getItemIds();
            Set<String> itemIdSet = Arrays.stream(itemIds.split(StrUtil.COMMA)).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            List<Long> userIdList = Arrays.stream(userIds.split(StrUtil.COMMA)).filter(StringUtils::isNotBlank).map(Long::valueOf).distinct().collect(Collectors.toList());
            for (Long userId : userIdList) {
                Set<String> itemIdList = map.get(userId);
                if (CollectionUtils.isEmpty(itemIdList)) {
                    map.put(userId, itemIdSet);
                } else {
                    itemIdList.addAll(itemIdSet);
                }
            }
        });
        List<QwMsgStaticsBo> list = new LinkedList<>();
        map.forEach((userId, set) -> {
            if (CollectionUtils.isNotEmpty(set)) {
                QwMsgStaticsBo bo = new QwMsgStaticsBo();
                bo.setUserId(userId);
                bo.setItemIds(String.join(StrUtil.COMMA, set));
                list.add(bo);
            }
        });
        return list;
    }

    public static void main(String[] args) {
        List<QwMsgQcStaticsBo> paramList = new LinkedList<>();
        QwMsgQcStaticsBo b1 = new QwMsgQcStaticsBo();
        b1.setUserIds("9703910");
        b1.setItemIds("51871");
        QwMsgQcStaticsBo b2 = new QwMsgQcStaticsBo();
        b2.setUserIds("9703910,");
        b2.setItemIds("54589");
        paramList.add(b1);
        paramList.add(b2);

        List<QwMsgStaticsBo> convert = convert(paramList);
        System.out.println(JsonUtil.toJson(convert));

    }


    // --------------------------------------------------------------------------------------------------------------------------------------------


    private List<Long> M008() {
        final List<ItemTrainingMaterialsStatusObj> itemTrainingMaterialsStatusObjs = itemMapper.selectByMaterialsStatus(
                ItemTrainingMaterialsStatus.TO_BE_QC_REVIEW.getValue());
        final HashSetValuedHashMap<StaffBrief, ItemTrainingMaterialsStatusObj> group = new HashSetValuedHashMap<>();
        for (ItemTrainingMaterialsStatusObj itemTrainingMaterialsStatusObj : itemTrainingMaterialsStatusObjs) {
            final Long itemId = itemTrainingMaterialsStatusObj.getItemId();
            final Long mergeItemId = itemTrainingMaterialsStatusObj.getMergeItemId();
            final Long useItemId = Optional.ofNullable(mergeItemId).orElse(itemId);
            try {
                final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo = newGoodsBizService.getNewGoodsPrincipalsInfo(
                        useItemId).getData();
                final List<StaffBrief> users = newGoodsPrincipalsInfo.getQcUsers();
                if (CollectionUtil.isNotEmpty(users)) {
                    for (StaffBrief user : users) {
                        group.put(user, itemTrainingMaterialsStatusObj);
                    }
                }
            } catch (Exception e) {
                log.warn("[企微汇总消息][M008]查询新品商品负责人信息异常 item={}", itemTrainingMaterialsStatusObj);
            }
        }
        List<Long> ids = new LinkedList<>();
        for (Map.Entry<StaffBrief, Collection<ItemTrainingMaterialsStatusObj>> entry : group.asMap()
                .entrySet()) {
            final StaffBrief user = entry.getKey();
            final Collection<ItemTrainingMaterialsStatusObj> items = entry.getValue();
            Map<String, Object> paramMap = new HashMap<>(4);
            paramMap.put("c1", items.size());
            paramMap.put("QC负责人", user.getQwUserId());
            paramMap.put("lp",
                    "materialsStatus=3&itemId=" + items.stream()
                            .map(ItemTrainingMaterialsStatusObj::getItemId)
                            .map(String::valueOf)
                            .collect(Collectors.joining(",")));
            long id = qyMsgSendService.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.M008, paramMap);
            ids.add(id);
        }
        return ids;
    }

    private List<Long> M009() {
        Integer count = iItemTrainingMaterialsService.lambdaQuery().eq(ItemTrainingMaterials::getStatus, ItemTrainingMaterialsStatus.TO_BE_MODIFY)
                .count();
        Map<String, Object> fillParamMap = new HashMap<>(4);
        fillParamMap.put("c1", count);
        fillParamMap.put("指定客服", environment.getProperty("msg-config.customerService"));
        fillParamMap.put("lp", "materialsStatus=4");

        long id = qyMsgSendService.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.M009, fillParamMap);
        return Collections.singletonList(id);
    }

    // --------------------------------------------------------------------------------------------------------------------------------------------

    private List<Long> itemOptimize(ItemOptimizeStatus status, MsgTemplateCode code) {
        List<Long> ids = new LinkedList<>();

        final List<ItemOptimize> pendingForQcList =
                itemOptimizeService
                        .lambdaQuery()
                        .eq(ItemOptimize::getStatus, status.getValue())
                        .list();
        if (!pendingForQcList.isEmpty()) {
            final ListValuedMap<StaffBrief, ItemOptimize> msgGroup =
                    MultiMapUtils.newListValuedHashMap();
            if (ItemOptimizeStatus.PENDING_QC_REVIEW.equals(status)) {
                for (ItemOptimize itemOptimize : pendingForQcList) {
                    for (StaffBrief qcUser : itemOptimize.getData().getQcUsers()) {
                        msgGroup.put(qcUser, itemOptimize);
                    }
                }
            }
            if (ItemOptimizeStatus.PENDING_LEGAL_REVIEW.equals(status)) {
                for (ItemOptimize itemOptimize : pendingForQcList) {
                    for (StaffBrief legalUser : itemOptimize.getData().getLegalUsers()) {
                        msgGroup.put(legalUser, itemOptimize);
                    }
                }
            }

            List<Map<String, Object>> paramMaps = new LinkedList<>();
            for (StaffBrief staffBrief : msgGroup.keys()) {
                final List<ItemOptimize> itemOptimizes = msgGroup.get(staffBrief);
                String itemIds = itemOptimizes.stream().map(val -> String.valueOf(val.getItemId())).distinct().collect(Collectors.joining(StrUtil.COMMA));
                if (StringUtils.isNotBlank(staffBrief.getQwUserId())) {
                    Map<String, Object> fillParamMap = new HashMap<>(4);
                    fillParamMap.put("t1", itemOptimizes.size());
                    fillParamMap.put("c1", itemOptimizes.size());
                    if (ItemOptimizeStatus.PENDING_QC_REVIEW.equals(status)) {
                        fillParamMap.put("QC负责人", staffBrief.getQwUserId());
                        fillParamMap.put("lp", "status=" + status.getValue() + "&itemId=" + itemIds);
                    }
                    if (ItemOptimizeStatus.PENDING_LEGAL_REVIEW.equals(status)) {
                        fillParamMap.put("法务负责人", staffBrief.getQwUserId());
                        fillParamMap.put("lp", "status=" + status.getValue() + "&itemId=" + itemIds);
                    }
                    paramMaps.add(fillParamMap);
                }
            }

            for (Map<String, Object> paramMap : paramMaps) {
                long id = qyMsgSendService.send(RemindType.TO_DO_REMINDER, code, paramMap);
                ids.add(id);
            }
        }
        return ids;
    }


    private List<Long> M010() {
        return itemOptimize(ItemOptimizeStatus.PENDING_QC_REVIEW, MsgTemplateCode.M010);
    }

    private List<Long> M011() {
        return itemOptimize(ItemOptimizeStatus.PENDING_LEGAL_REVIEW, MsgTemplateCode.M011);
    }

    @Override
    public void handle(String templateCode) {
        CopyOnWriteArrayList<Long> weChatIds = new CopyOnWriteArrayList<>();

        List<String> reqCodes;
        if (StringUtils.isNotBlank(templateCode)) {
            reqCodes = Arrays.stream(templateCode.split(StrUtil.COMMA)).collect(Collectors.toList());
        } else {
            reqCodes = Arrays.stream(Objects.requireNonNull(environment.getProperty("msg-config.staticsMsgCodes")).split(StrUtil.COMMA)).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(reqCodes)) {
            List<CompletableFuture<Void>> futures = new LinkedList<>();
            for (String code : reqCodes) {
                CompletableFuture<Void> voidCompletableFuture = CompletableFuture.runAsync(() -> {
                    try {
                        Method method = this.getClass().getDeclaredMethod(code);
                        method.setAccessible(true);
                        List<Long> wechatIds = (List<Long>) method.invoke(this);
                        weChatIds.addAll(wechatIds);
                    } catch (Exception e) {
                        log.error("企微10AM统计消息推送异常.msgCode:{}", code, e);
                    }
                });
                futures.add(voidCompletableFuture);
            }
            CompletableFuture<?>[] array = futures.toArray(new CompletableFuture<?>[0]);
            CompletableFuture.allOf(array).join();
        }

        log.info("企微10AM统计消息推送.size:{}", weChatIds.size());

    }


}
