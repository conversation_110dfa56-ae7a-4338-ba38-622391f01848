package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> up
 * @date 2024年07月01日 10:20 AM
 */
@AllArgsConstructor
@Getter
public enum VWarehouseStatusMonitor implements IIntegerEnum {

    /**
     * 虚拟仓状态改变
     */
    NO_CHANGE(0, "状态无变化"),
    RUN_TO_FORBID(1, "状态运行变为禁止"),
    FORBID_TO_RUN(2, "状态禁止变为运行");

    private final Integer value;
    private final String desc;
}
