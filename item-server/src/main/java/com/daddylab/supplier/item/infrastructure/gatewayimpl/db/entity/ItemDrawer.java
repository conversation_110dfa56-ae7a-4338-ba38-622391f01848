package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.LaunchItemType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品抽屉表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemDrawer extends Entity {

    private static final long serialVersionUID = 1L;

    /**
     * 产品标准名
     */
    private String standardName;

    /**
     * 淘宝标题
     */
    private String tbTitle;

    /**
     * 小程序标题
     */
    private String miniTitle;

    /**
     * 首页文案
     */
    private String homeCopy;

    /**
     * 上架时间
     */
    private Long shelfTime;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 抖音链接
     */
    private String douLink;

    /**
     * 抖音链接商品ID
     */
    private String douId;

    private String wechatId;

    /**
     * 淘宝链接
     */
    private String tbLink;

    /**
     * 微信小程序链接
     */
    private String wechatLink;

    /**
     * 淘宝链接商品ID
     */
    private String tbId;

    /**
     * 商品上新类型（外包、内部）
     */
    private LaunchItemType type;

    /**
     * 直播话术
     */
    private String liveVerbalTrick;

    /**
     * 直播话术状态
     */
    private ItemAuditStatus liveVerbalTrickStatus;

    /**
     * 待修改提交状态 1:产品组已提交，等待协同用户提交 2:协同用户已提交，等待产品组提交
     */
    private Integer launchSubmitStatus;

    /**
     * 待修改阶段协同用户ID
     */
    private Long coopUid;

    /**
     * 抖音标题
     */
    private String douTitle;

    /**
     * 小红薯ID
     */
    private String miniRedBookId;

    /**
     * 小红书链接
     */
    private String miniRedBookLink;

    /**
     * 抖音链接内容
     */
    private String douYinLinkContent;

    /**
     * 快手链接内容
     */
    private String kuaiShouLinkContent;
}
