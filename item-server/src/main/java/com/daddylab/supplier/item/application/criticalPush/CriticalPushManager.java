package com.daddylab.supplier.item.application.criticalPush;

import java.io.Serializable;

/**
 * 关键推送管理器
 *
 * <AUTHOR>
 * @since 2022/4/15
 */
public interface CriticalPushManager extends Serializable {

    /**
     * 数据推送统一管理（适配动态参数有返回值）
     *
     * @param sourceType 数据推送源
     * @param sourceId   来源ID
     * @param targetType 推送目标
     * @param callback   执行状态回调
     * @throws CriticalPushException 推送失败，上层必须要处理这个异常
     */
    default <P, R> void push(SourceType sourceType, long sourceId, TargetType targetType,
            VarargsFunctionCallback<P, R> callback) throws CriticalPushException {
        push(sourceType, sourceId, targetType, callback, null);
    }

    /**
     * 数据推送统一管理（适配动态参数有返回值）
     *
     * @param sourceType 数据推送源
     * @param sourceId   来源ID
     * @param targetType 推送目标
     * @param callback   执行状态回调
     * @param pushId     推送记录ID，携带此参数说明是重试
     * @throws CriticalPushException 推送失败，上层必须要处理这个异常
     */
    <P, R> void push(SourceType sourceType, long sourceId, TargetType targetType,
            VarargsFunctionCallback<P, R> callback, Long pushId)
            throws CriticalPushException;
}
