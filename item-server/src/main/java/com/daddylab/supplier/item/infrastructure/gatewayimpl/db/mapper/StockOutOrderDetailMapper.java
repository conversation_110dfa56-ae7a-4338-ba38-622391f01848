package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.controller.stockout.dto.StockOutOrderDetailVO;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockOutOrderDetail;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 退料出库明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
public interface StockOutOrderDetailMapper extends DaddyBaseMapper<StockOutOrderDetail> {

    int getTotalReturnQuantity(Long id);

    List<StockOutOrderDetailVO> queryDetail(@Param("param") StockOutOrderQuery stockOutOrderQuery,@Param("idList") List<Long> idList);

    List<StockOutOrderDetailVO> getByStockOutOrderId(Long stockOutOrderId);
}
