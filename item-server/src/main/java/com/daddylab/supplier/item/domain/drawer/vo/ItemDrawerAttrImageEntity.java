package com.daddylab.supplier.item.domain.drawer.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.javers.core.metamodel.annotation.Entity;
import org.javers.core.metamodel.annotation.Id;

/**
 * <AUTHOR>
 * @since 2022/9/21
 */
@Data
@EqualsAndHashCode(of = {"id"})
@Entity
@AllArgsConstructor
@NoArgsConstructor
public class ItemDrawerAttrImageEntity {

    @Id
    private Long id;
    private String attrValue;
    private String url;
}
