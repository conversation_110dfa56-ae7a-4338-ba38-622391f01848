package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2024年04月22日 2:26 PM
 */
@NoArgsConstructor
@Data
public class StockOutOrderProcessDetailFormList {


    private Long businessId;
    private Integer deductionQuantity;
    private Integer isGift;
    private String itemName;
    private Integer num;
    private Integer realNum;
    private String remark;
    private Integer replenishQuantity;
    private String returnReason;
    private String skuCode;
    private String specifications;
    private BigDecimal taxPrice;
    private BigDecimal totalPriceTax;
    private String unit;
    private Integer valuationQuantity;
    private String valuationUnit;
    private String wareHouseName;
}
