package com.daddylab.supplier.item.application.recognitionTask;

import cn.hutool.core.collection.CollectionUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerRecognitionTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerRecognitionTaskService;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.recognitionTask.ItemDrawerRecognitionTaskStatus;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/12/8
 */
@Service
@RequiredArgsConstructor
public class ItemRecognitionTaskGatewayImpl implements ItemRecognitionTaskGateway {

    private final IItemDrawerRecognitionTaskService itemDrawerRecognitionTaskService;

    @Override
    public List<ItemDrawerRecognitionTask> getWaitImageCvRecognitionTasks() {
        return itemDrawerRecognitionTaskService.lambdaQuery()
                .eq(ItemDrawerRecognitionTask::getStatus,
                        ItemDrawerRecognitionTaskStatus.PREPARE)
                .ne(ItemDrawerRecognitionTask::getImageUrl, "").list();
    }

    @Override
    public void updateTaskCvResult(ItemDrawerRecognitionTask itemDrawerRecognitionTask,
            String content,
            String cvResult,
            ItemDrawerRecognitionTaskStatus status, List<String> logs) {
        //最多重试三次，三次失败后再将状态修改为【失败】
        int failCount;
        if (status == ItemDrawerRecognitionTaskStatus.FAIL) {
            failCount = itemDrawerRecognitionTask.getFailCount() + 1;
            if (failCount < 3) {
                status = null;
            }
        } else {
            //状态变更重置失败次数
            failCount = 0;
        }
        itemDrawerRecognitionTaskService.lambdaUpdate()
                .eq(ItemDrawerRecognitionTask::getId, itemDrawerRecognitionTask.getId())
                .set(Objects.nonNull(status), ItemDrawerRecognitionTask::getStatus, status)
                .set(ItemDrawerRecognitionTask::getFailCount, failCount)
                .set(StringUtil.isNotEmpty(content), ItemDrawerRecognitionTask::getContent, content)
                .set(StringUtil.isNotEmpty(cvResult), ItemDrawerRecognitionTask::getCvResult,
                        cvResult)
                .set(CollectionUtil.isNotEmpty(logs), ItemDrawerRecognitionTask::getLog,
                        Optional.ofNullable(logs).map(v -> {
                            if (StringUtil.isNotEmpty(itemDrawerRecognitionTask.getLog())) {
                                return itemDrawerRecognitionTask.getLog() + ";"
                                        + String.join(";", v);
                            }
                            return String.join(";", v);
                        }).orElse(""))
                .update();
    }
}
