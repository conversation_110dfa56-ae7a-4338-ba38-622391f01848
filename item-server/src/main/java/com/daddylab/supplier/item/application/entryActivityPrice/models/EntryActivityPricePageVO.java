package com.daddylab.supplier.item.application.entryActivityPrice.models;

import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/4
 */
@Data
public class EntryActivityPricePageVO {
    /**
     * 入驻活动价格主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @Positive
    private Long id;

    @ApiModelProperty(value = "所属年月")
    @Positive
    private Long month;

    @NotBlank
    private String itemCode;

    @Positive
    private Long itemId;
    /**
     * 商品名称（纯展示）
     */
    private String itemName;
    /**
     * 商品图片（纯展示）
     */
    private String imgUrl;
    /**
     * 供应商名称（纯展示）
     */
    private String provider;
    /**
     * 供应商id
     */
    @Positive
    private Long providerId;
    /**
     * 采购人员(纯展示)
     */
    private StaffBrief buyer;
    /**
     * 确认状态
     */
    @ApiModelProperty(value = "状态", notes = "状态 -1 待发起 0 待确认 1 已确认 2 存在异议")
    private Integer status;

    @Valid
    @NotEmpty
    private List<EntryActivityPriceSkuVO> skuList;

    @ApiModelProperty(value = "合作方 层级")
    private List<Integer> corpType;

    @ApiModelProperty(value = "业务类型 层级")
    private List<Integer> bizType;

    @ApiModelProperty(value = "合作方+业务类型")
    private List<CorpBizTypeDTO> corpBizType;

}
