package com.daddylab.supplier.item.infrastructure.third.consumer;

import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQGroup;
import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQTopic;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.domain.dto.KsMessageDTO;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.impl.KsMessageHandlerSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @class KSConsumer.java
 * @description 描述类的作用
 * @date 2024-02-29 17:10
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MQTopic.THIRD_CALLBACK_MESSAGE_KS_TOPIC, consumerGroup = MQGroup.THIRD_CALLBACK_MESSAGE_KS_GROUP,
        messageModel = MessageModel.CLUSTERING)
public class KSCallbackMessageConsumer implements RocketMQListener<KsMessageDTO> {

    @Autowired
    KsMessageHandlerSupport ksMessageHandlerSupport;


    @Override
    public void onMessage(KsMessageDTO message) {
        log.info("[快手回调消息] 接受到 message={}", message);
        ksMessageHandlerSupport.dispatcher(message);
    }
}
