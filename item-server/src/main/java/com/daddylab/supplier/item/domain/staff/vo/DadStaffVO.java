package com.daddylab.supplier.item.domain.staff.vo;

import lombok.Data;

/**
 * Class  DadStaffVO
 *
 * @Date 2022/6/1上午10:19
 * <AUTHOR>
 */
@Data
public class DadStaffVO {

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 姓名
     */
    private String realName;

    /**
     * 花名
     */
    private String nickname;

    /**
     * 登录名
     */
    private String loginName;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 联系电话
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 入职日期
     */
    private Long hireDate;

    /**
     * 状态 1在职
     */
    private Integer status;

    /**
     * 来源
     */
    private String ref;

    /**
     * 工号
     */
    private Long employeeId;

    /**
     * 工号
     */
    private String code;

    /**
     * 是否外部人员
     */
    private Integer isExternal;

    /**
     * oa ID
     */
    private Long oaId;

    /**
     * 删除时间
     */
    private Long deletedAt;

    /**
     * 岗位id
     */
    private Long postId;
    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 部门名 (全部层级)
     */
    private String deptName;
    /**
     * 企微用户id
     */
    private String qwUserId;
}
