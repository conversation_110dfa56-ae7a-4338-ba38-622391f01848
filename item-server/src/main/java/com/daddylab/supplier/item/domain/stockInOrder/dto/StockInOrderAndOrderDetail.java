package com.daddylab.supplier.item.domain.stockInOrder.dto;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockInState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICombinationItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLockKey;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.annotation.Nullable;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

@Data
@ApiModel("采购入库单创建与更新数据模型")
public class StockInOrderAndOrderDetail {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 是否已删除
     */
    @ApiModelProperty(value = "是否已删除")
    private Integer isDel;
    /**
     * 入库单号
     */
    @ApiModelProperty(value = "入库单号")
    private String no;
    /**
     * 关联采购单号
     */
    @ApiModelProperty(value = "关联采购单号")
    @DistributedLockKey
    private String purchaseOrderNo;
    /**
     * 关联采购单id
     */
    @ApiModelProperty(value = "关联采购单id")
    private Long purchaseOrderId;
    /**
     * 供应商id
     */
    @ApiModelProperty(value = "供应商id")
    private Long providerId;

    @ApiModelProperty("是否黑名单 0否 1是")
    private Integer isBlacklist;
    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String provider;
    /**
     * 收料(入库)日期
     */
    @ApiModelProperty(value = "实际入库日期")
    private Long receiptTime;
    /**
     * 交货(入库)总数量
     */
    @ApiModelProperty(value = "交货(入库)总数量")
    private Integer totalReceiptQuantity;
    /**
     * 采购数量
     */
    @ApiModelProperty(value = "采购数量")
    private Integer purchaseQuantity;
    /**
     * 入库状态，1.待提交、2.待入库、3.已入库、4.已取消
     */
    @ApiModelProperty(value = "入库状态，1.待提交、2.待入库、3.已入库、4.已取消")
    private Integer state;
    /**
     * 收料组织id
     */
    @ApiModelProperty(value = "收料组织id")
    private Long organizationId;
    /**
     * 采购员id
     */
    @ApiModelProperty(value = "采购员id")
    private Long buyerUserId;
    /**
     * 采购组织id
     */
    @ApiModelProperty(value = "采购组织id")
    private Long purchaseOrganizationId;
    /**
     * 采购部门id
     */
    @ApiModelProperty(value = "采购部门id")
    private Long purchaseDepartmentId;
    /**
     * 采购组id
     */
    @ApiModelProperty(value = "采购组id")
    private Long purchaseGroupId;
    /**
     * 入库仓库名称
     */
    private String warehouseName;
    /**
     * 入库仓库编号
     */
    @ApiModelProperty(value = "入库仓库编号")
    private String warehouseNo;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否同步旺店通，0不推送。1推送
     */
    @ApiModelProperty(value = "是否同步旺店通，0不推送。1推送")
    private Integer isSyncWdt;
    /**
     * 同步旺店通情况下，旺店通返回的入库单号
     */
    @ApiModelProperty(value = "同步旺店通情况下，旺店通返回的入库单号")
    private String wdtStorageNo;
    /**
     * 金蝶同步ID
     **/
    @ApiModelProperty(value = "金蝶同步ID")
    private String kingDeeId;
    /**
     * 收料明细
     */
    @ApiModelProperty(value = "收料明细")
    private List<StockInOrderDetail> stockInOrderDetails;
    /**
     * 操作类型 0 保存 1 提交 2 手动入库
     */
    @ApiModelProperty(value = "操作类型 0 保存 1 提交 2 手动入库")
    private Integer operationType;

    private Integer businessLine;

    /**
     * 冲销状态
     */
    @ApiModelProperty(value = "操作类型 DEFAULT:默认.REVERSE_FIXED:逆向对冲单据.WRITE_OFF:原单据作废.FIX_ORDER:冲销流程,原单据的修正单据")
    private StockInState hedgeStatus;

    /**
     * 当此数据为冲销单据请求参数时。
     * 原退料出库单编码
     */
    private String reverseOldNo;
    private Long reverseOldId;


    public static StockInOrderAndOrderDetail ofStockOutOrder(
            PurchaseOrder purchaseOrder,
            StockOutOrder stockOutOrder,
            List<StockOutOrderDetail> details, @Nullable PartnerProviderResp partnerProviderResp,
            String remark) {
        StockInOrderAndOrderDetail stockInOrder = new StockInOrderAndOrderDetail();
        stockInOrder.setPurchaseOrderNo(purchaseOrder.getNo());
        stockInOrder.setPurchaseOrderId(purchaseOrder.getId());
        stockInOrder.setProviderId(purchaseOrder.getProviderId());
        final Optional<PartnerProviderResp> providerRespOptional =
                Optional.ofNullable(partnerProviderResp);
        stockInOrder.setIsBlacklist(
                providerRespOptional.map(PartnerProviderResp::getIsBlacklist).orElse(0));
        stockInOrder.setReceiptTime(DateUtil.currentTime());
        stockInOrder.setPurchaseQuantity(purchaseOrder.getTotalPurchaseQuantity());
        stockInOrder.setTotalReceiptQuantity(purchaseOrder.getTotalPurchaseQuantity());
        stockInOrder.setState(3);
        stockInOrder.setOrganizationId(purchaseOrder.getOrganizationId());
        stockInOrder.setBuyerUserId(-1L);
        stockInOrder.setPurchaseOrganizationId(purchaseOrder.getOrganizationId());
        stockInOrder.setPurchaseDepartmentId(0L);
        stockInOrder.setPurchaseGroupId(0L);
        stockInOrder.setIsSyncWdt(0);
        stockInOrder.setOperationType(1);
        stockInOrder.setRemark(remark);

        List<StockInOrderDetail> stockInOrderDetails = new LinkedList<>();
        details.forEach(stockOutOrderDetail -> {
            Integer purchaseQuantity = Math.abs(stockOutOrderDetail.getRealReturnQuantity());
            StockInOrderDetail stockInOrderDetail = new StockInOrderDetail();
            stockInOrderDetail.setItemSkuCode(stockOutOrderDetail.getItemSkuCode());
            String itemSkuCode = stockOutOrderDetail.getItemSkuCode();
            // 考虑组合商品
            if (itemSkuCode.startsWith("MU")) {
                ICombinationItemService combinationItemService = SpringUtil.getBean(ICombinationItemService.class);
                CombinationItem combinationItem = combinationItemService.getByItemCode(itemSkuCode);
                stockInOrderDetail.setItemName(combinationItem.getName());
            } else {
                ItemSkuGateway itemSkuGateway = SpringUtil.getBean(ItemSkuGateway.class);
                ItemSku itemSku = itemSkuGateway.getBySkuCode(stockOutOrderDetail.getItemSkuCode());
                ItemGateway itemGateway = SpringUtil.getBean(ItemGateway.class);
                Item item = itemGateway.getItem(itemSku.getItemId());
                stockInOrderDetail.setItemName(item.getName());
                stockInOrderDetail.setSpecifications(itemSkuGateway.getSkuAttrListStr(stockOutOrderDetail.getItemSkuCode()));
                stockInOrderDetail.setUnit(itemSku.getUnit());
            }
            stockInOrderDetail.setReceiptQuantity(purchaseQuantity);
            // 系统自动生成的入库单，实际入库数量=采购单数量
            stockInOrderDetail.setRealReceiptQuantity(purchaseQuantity);
            stockInOrderDetail.setStockState(StockInState.WAIT_SUBMIT.getValue());
            stockInOrderDetail.setWarehouseNo(stockOutOrderDetail.getWarehouseNo());
            stockInOrderDetail.setIsGift(stockOutOrderDetail.getIsGift());
            stockInOrderDetail.setTaxPrice(stockOutOrderDetail.getTaxPrice());
            stockInOrderDetail.setTaxRate(stockOutOrderDetail.getTaxRate());
            stockInOrderDetail.setPurchaseQuantity(purchaseQuantity);
            stockInOrderDetail.setRemark("冲销自动生成采购入库单明细");
            stockInOrderDetails.add(stockInOrderDetail);
        });
        stockInOrder.setStockInOrderDetails(Lists.newArrayList(stockInOrderDetails));

        return stockInOrder;
    }


    public static StockInOrderAndOrderDetail ofPurchaseOrder(PurchaseOrder purchaseOrder, List<PurchaseOrderDetail> detailList,
                                                             Boolean isHedge, @Nullable PartnerProviderResp partnerProviderResp) {

        StockInOrderAndOrderDetail stockInOrder = new StockInOrderAndOrderDetail();
        stockInOrder.setPurchaseOrderNo(purchaseOrder.getNo());
        stockInOrder.setPurchaseOrderId(purchaseOrder.getId());
        stockInOrder.setProviderId(purchaseOrder.getProviderId());
        final Optional<PartnerProviderResp> providerRespOptional =
                Optional.ofNullable(partnerProviderResp);
        stockInOrder.setIsBlacklist(
                providerRespOptional.map(PartnerProviderResp::getIsBlacklist).orElse(0));
        stockInOrder.setReceiptTime(purchaseOrder.getPurchaseDate());
        stockInOrder.setPurchaseQuantity(purchaseOrder.getTotalPurchaseQuantity());
        stockInOrder.setTotalReceiptQuantity(purchaseOrder.getTotalPurchaseQuantity());
        stockInOrder.setState(3);
        stockInOrder.setOrganizationId(purchaseOrder.getOrganizationId());
        stockInOrder.setBuyerUserId(-1L);
        stockInOrder.setPurchaseOrganizationId(purchaseOrder.getOrganizationId());
        stockInOrder.setPurchaseDepartmentId(0L);
        stockInOrder.setPurchaseGroupId(0L);
        stockInOrder.setIsSyncWdt(0);
        stockInOrder.setOperationType(1);
        stockInOrder.setBusinessLine(purchaseOrder.getBusinessLine());
        if (isHedge) {
            stockInOrder.setRemark("生成冲销入库单，原出库单关联的采购单号:" + purchaseOrder.getNo());
        } else {
            stockInOrder.setRemark("自动生成采购入库单");
        }

        List<StockInOrderDetail> stockInOrderDetails = new LinkedList<>();
        detailList.forEach(purchaseOrderDetail -> {
            Integer purchaseQuantity = purchaseOrderDetail.getPurchaseQuantity();
            if (isHedge) {
                purchaseQuantity = Math.abs(purchaseQuantity);
            }

            StockInOrderDetail stockInOrderDetail = new StockInOrderDetail();
            stockInOrderDetail.setItemSkuCode(purchaseOrderDetail.getItemSkuCode());
            String itemSkuCode = purchaseOrderDetail.getItemSkuCode();
            // 考虑组合商品
            if (itemSkuCode.startsWith("MU")) {
                ICombinationItemService combinationItemService = SpringUtil.getBean(ICombinationItemService.class);
                CombinationItem combinationItem = combinationItemService.getByItemCode(itemSkuCode);
                stockInOrderDetail.setItemName(combinationItem.getName());
            } else {
                ItemSkuGateway itemSkuGateway = SpringUtil.getBean(ItemSkuGateway.class);
                ItemSku itemSku = itemSkuGateway.getBySkuCode(purchaseOrderDetail.getItemSkuCode());
                ItemGateway itemGateway = SpringUtil.getBean(ItemGateway.class);
                Item item = itemGateway.getItem(itemSku.getItemId());
                stockInOrderDetail.setItemName(item.getName());
                stockInOrderDetail.setSpecifications(itemSkuGateway.getSkuAttrListStr(purchaseOrderDetail.getItemSkuCode()));
            }
            stockInOrderDetail.setUnit(purchaseOrderDetail.getUnit());
            stockInOrderDetail.setReceiptQuantity(purchaseQuantity);
            // 系统自动生成的入库单，实际入库数量=采购单数量
            stockInOrderDetail.setRealReceiptQuantity(purchaseQuantity);
            stockInOrderDetail.setStockState(StockInState.WAIT_SUBMIT.getValue());
            stockInOrderDetail.setWarehouseNo(purchaseOrderDetail.getWarehouseNo());
            stockInOrderDetail.setIsGift(purchaseOrderDetail.getIsGift());
            stockInOrderDetail.setTaxPrice(purchaseOrderDetail.getTaxPrice());
            stockInOrderDetail.setTaxRate(purchaseOrderDetail.getTaxRate());
            stockInOrderDetail.setPurchaseQuantity(purchaseQuantity);
            stockInOrderDetail.setRemark("自动生成采购入库单明细");
            stockInOrderDetails.add(stockInOrderDetail);
        });
        stockInOrder.setStockInOrderDetails(Lists.newArrayList(stockInOrderDetails));

        return stockInOrder;
    }


}
