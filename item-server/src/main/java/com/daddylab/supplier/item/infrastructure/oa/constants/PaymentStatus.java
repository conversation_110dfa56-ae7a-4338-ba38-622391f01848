package com.daddylab.supplier.item.infrastructure.oa.constants;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * OA支付状态枚举
 *
 * <AUTHOR>
 * @since 2023/11/23
 */
@RequiredArgsConstructor
@Getter
public enum PaymentStatus implements IEnum<Long> {
    PAYMENT_SUCCESSFUL(-7787722448457350501L, "付款成功"),
    PAYMENT_TO_BE_SUBMITTED(3774171079321600355L, "付款待提交"),
    IN_THE_PAYMENT_PROCESS(2761429546314518108L, "付款流程中"),
    WAITING_FOR_BANK_RESULTS(-3390025052419045470L, "等待银行结果"),
    PAYMENT_DELETED(-6961217234282395739L, "付款已删除"),
    PAYMENT_DOES_NOT_EXIST(1224098917296457449L, "付款不存在"),
    PAYMENT_HAS_BEEN_BACK(3023509932396856099L, "付款已打回"),
    PAYMENT_FAILED(8881915083509351400L, "付款失败"),
    OTHER_STATUSES(9083445423497300536L, "其他状态"),
    ;
    @JsonValue private final Long value;
    private final String desc;
}
