package com.daddylab.supplier.item.infrastructure.schedule;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2022/1/7
 */
@Data
public class TaskInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    private TaskStatus taskStatus = TaskStatus.DISABLE;
    private Boolean executing = false;
    private LocalDateTime lastExecuteTime;
    private LocalDateTime lastExecuteTimeEnd;
}
