package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.JobNewGoodsProcess;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.JobNewGoodsProcessMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IJobNewGoodsProcessService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 新品商品数据处理（临时任务） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Service
public class JobNewGoodsProcessServiceImpl extends DaddyServiceImpl<JobNewGoodsProcessMapper, JobNewGoodsProcess> implements IJobNewGoodsProcessService {

}
