package com.daddylab.supplier.item.infrastructure.separator;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> up
 * @date 2022年11月28日 11:24 AM
 */
@Slf4j
@Component
public class WordSegmenterRunner implements ApplicationListener<ContextRefreshedEvent> {


    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
//        ThreadUtil.execute(() -> {
//            if (ApplicationContextUtil.isActiveProfile("prod", "gray")) {
//                WordSegmenter.seg("预热");
//            }
//        });
    }
}
