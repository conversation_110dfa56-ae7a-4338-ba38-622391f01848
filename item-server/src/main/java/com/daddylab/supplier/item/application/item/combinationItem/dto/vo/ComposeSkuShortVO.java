package com.daddylab.supplier.item.application.item.combinationItem.dto.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/12 5:32 下午
 * @description
 */
@Data
@ApiModel("组合分页响应-组成sku简信")
public class ComposeSkuShortVO {

    @ApiModelProperty("skuCode")
    private String skuCode;

    @ApiModelProperty("sku数量")
    private Long count;

    @ApiModelProperty("成本价")
    private BigDecimal costPrice;

    /**
     * 单品成本占比
     */
    private BigDecimal costProportion;


    public String toStr() {
        return skuCode + "*" + count;
    }

}
