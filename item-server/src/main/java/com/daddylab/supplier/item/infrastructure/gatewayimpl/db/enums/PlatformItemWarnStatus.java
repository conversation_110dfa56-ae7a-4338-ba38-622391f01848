package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 平台商品预警状态
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@AllArgsConstructor
public enum PlatformItemWarnStatus implements IEnum<Integer> {
    /**
     * 待处理
     */
    UNHANDLED(0, "待处理"),
    /**
     * 已处理
     */
    HANDLED(1, "已处理");

    @EnumValue
    private final Integer value;
    private final String desc;

}
