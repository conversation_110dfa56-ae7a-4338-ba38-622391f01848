package com.daddylab.supplier.item.application.platformItem.data;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ShopInventorySpecAllocVO {
    private String shopNo;
    private String skuCode;
    private Integer inventoryWeight;
    private Integer totalWeight;
    private BigDecimal allocableStock;
    private List<WarehouseInventoryDetailItemVO> warehouseSpecInventories;
}
