package com.daddylab.supplier.item.domain.third.dto;

import com.alibaba.fastjson2.JSONObject;
import com.daddylab.supplier.item.infrastructure.third.enums.AccessTokenTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @class AccessTokenForm.java
 * @description token
 * @date 2024-02-27 17:34
 */
@Data
public class AccessTokenForm implements Serializable {

    private static final long serialVersionUID = 3005100153718707601L;
    @NotEmpty(message = "token不能为空")
    private String accessToken;

    /**
     * token JSON
     */
    private JSONObject data;

    @NotNull(message = "类型不能为空")
    private AccessTokenTypeEnum type;

    @NotEmpty(message = "店铺编号")
    private String shopSn;
}
