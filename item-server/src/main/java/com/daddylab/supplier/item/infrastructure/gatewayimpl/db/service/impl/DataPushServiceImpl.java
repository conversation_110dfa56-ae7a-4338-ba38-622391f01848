package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataPush;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DataPushMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDataPushService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据推送记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Service
public class DataPushServiceImpl extends DaddyServiceImpl<DataPushMapper, DataPush> implements IDataPushService {

}
