package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemMainImgDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemImage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 商品图片 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Repository
public interface ItemImageMapper extends DaddyBaseMapper<ItemImage> {

    List<ItemMainImgDO> selectMainImg(@Param("itemCodes") Collection<String> itemCodes);

}
