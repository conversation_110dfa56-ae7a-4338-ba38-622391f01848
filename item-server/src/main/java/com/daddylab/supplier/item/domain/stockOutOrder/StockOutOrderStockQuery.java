package com.daddylab.supplier.item.domain.stockOutOrder;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/5/27 11:53
 * @description StockOutOrderStockQuery
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StockOutOrderStockQuery {

    private Long stockOutOrderId;

    /**
     * 关联采购单id
     */
    private Long purchaseOrderId;

    /**
     * 出库仓库
     */
    private String warehouseNo;

    /**
     * 商品SKU
     */
    private String itemSkuCode;

    /**
     * 同步旺店通
     */
    private Integer isSyncWdt;

    /**
     * 是否是赠品
     */
    private Integer isGift;

}
