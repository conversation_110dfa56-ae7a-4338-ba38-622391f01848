package com.daddylab.supplier.item.application.platformItem.tasks;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.job.extend.autoRegister.XxlJobAutoRegister;
import com.daddylab.supplier.item.application.platformItem.PlatformItemInventorySettingBizService;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemSkuService;
import com.daddylab.supplier.item.infrastructure.timing.StopWatch;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.platformItem.AutoInventoryRatioCmd;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/4
 */
@Service
@Slf4j
public class PlatformItemInventorySettingTask {
    @Autowired
    private PlatformItemInventorySettingBizService platformItemInventorySettingBizService;
    @Autowired
    private IPlatformItemSkuService platformItemSkuService;

    @XxlJob("autoInventoryRatio")
    @XxlJobAutoRegister(cron = "0 0 0 1 1 ? 2000", author = "徵乌", jobDesc = "自动分配库存比例")
    public void autoInventoryRatio() {
        try {
            final AutoInventoryRatioJobParam jobParam = getJobParam();
            final boolean debug = jobParam != null && jobParam.isDebug();
            final StopWatch stopWatch = new StopWatch();
            stopWatch.start("autoInventoryRatio");
            final HashSet<AutoInventoryRatioKey> handled = new HashSet<>();
            if (jobParam != null && CollectionUtil.isNotEmpty(jobParam.getSkuCodes())) {
                final AutoInventoryRatioCmd cmd = new AutoInventoryRatioCmd();
                cmd.setShopNo(jobParam.getShopNo());
                cmd.setSkuCodes(jobParam.getSkuCodes());
                cmd.setDebug(jobParam.isDebug());

                platformItemInventorySettingBizService.autoInventoryRatio(cmd);
                handled.addAll(jobParam.getSkuCodes().stream()
                                       .map(v -> new AutoInventoryRatioKey(jobParam.getShopNo(), v))
                                       .collect(Collectors.toSet()));
                log.info("autoInventoryRatio: handled={}", handled.size());
                stopWatch.stopThenOutput();
                return;
            }
            int current = 1;
            final int size = 100;
            final LambdaQueryWrapper<PlatformItemSku> queryWrapper = Wrappers.lambdaQuery();
            if (jobParam != null && StringUtil.isNotBlank(jobParam.getShopNo())) {
                queryWrapper.eq(PlatformItemSku::getShopNo, jobParam.getShopNo());
            }
            while (true) {
                if (Thread.currentThread().isInterrupted()) {
                    log.warn("autoInventoryRatio: interrupted");
                    break;
                }
                final Page<PlatformItemSku> page = new Page<>(current, size, false);
                log.info("autoInventoryRatio: current={}, size={}, offset={}", current, size, page.offset());
                platformItemSkuService.page(page, queryWrapper);
                final List<PlatformItemSku> records = page.getRecords();
                if (records.isEmpty()) {
                    break;
                }
                current++;
                final Map<String, List<PlatformItemSku>> recordsGroup = records.stream()
                                                                               .filter(v -> StringUtil.isNotBlank(
                                                                                       v.getOuterSkuCode()))
                                                                               .collect(Collectors.groupingBy(
                                                                                       PlatformItemSku::getShopNo));
                recordsGroup.forEach((shopNo, list) -> {
                    final AutoInventoryRatioCmd cmd = new AutoInventoryRatioCmd();
                    cmd.setDebug(debug);
                    cmd.setShopNo(shopNo);
                    final List<String> skuCodes = list.stream()
                                                      .map(PlatformItemSku::getOuterSkuCode)
                                                      .distinct()
                                                      .filter(v -> !handled.contains(
                                                              new AutoInventoryRatioKey(shopNo, v)))
                                                      .collect(Collectors.toList());
                    if (!skuCodes.isEmpty()) {
                        cmd.setSkuCodes(skuCodes);

                        platformItemInventorySettingBizService.autoInventoryRatio(cmd);
                        handled.addAll(skuCodes.stream()
                                               .map(v -> new AutoInventoryRatioKey(shopNo, v))
                                               .collect(Collectors.toSet()));
                    }
                });
                final long totalTimeMillis = stopWatch.getTotalTimeMillis();
                final int checkInterval = 1000 * 10;
                if (totalTimeMillis > checkInterval && totalTimeMillis % checkInterval < 100) {
                    stopWatch.output();
                    ThreadUtil.sleep(100);
                }
            }
            log.info("autoInventoryRatio: handled={}", handled.size());
            stopWatch.stopThenOutput();
        } catch (Throwable e) {
            log.error("autoInventoryRatio error", e);
            XxlJobHelper.handleFail(ExceptionUtil.stacktraceToString(e));
        }

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AutoInventoryRatioKey {
        private String shopNo;
        private String skuCode;
    }

    @Data
    public static class AutoInventoryRatioJobParam {
        private String shopNo;
        private List<String> skuCodes;
        private boolean debug;
    }

    private AutoInventoryRatioJobParam getJobParam() {
        return Optional.ofNullable(XxlJobHelper.getJobParam())
                       .filter(StringUtil::isNotBlank)
                       .map(v -> JsonUtil.parse(v, AutoInventoryRatioJobParam.class))
                       .orElse(null);
    }
}
