package com.daddylab.supplier.item.domain.purchaseDissent.entity;

import com.daddylab.supplier.item.common.trans.PurchaseDissentTransMapper;
import com.daddylab.supplier.item.controller.purchaseDissent.dto.PurchaseDissentCmd;
import com.daddylab.supplier.item.domain.purchaseDissent.gateway.PurchaseDissentGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseDissent;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName PurchaseDissentEntity.java
 * @description
 * @createTime 2021年11月17日 14:06:00
 */
@Data
public class PurchaseDissentEntity {
    /**
     * 品类
     */
    private PurchaseDissent purchaseDissent;

    /**
     * 父品类
     */
    private PurchaseDissent parentPurchaseDissent;

    public static PurchaseDissentEntity buildEntity(PurchaseDissentCmd cmd, PurchaseDissentGateway purchaseDissentGateway) {
        PurchaseDissentEntity purchaseDissentEntity = new PurchaseDissentEntity();
        final PurchaseDissent purchaseDissent = PurchaseDissentTransMapper.INSTANCE.cmdToDo(cmd);
        purchaseDissentEntity.setPurchaseDissent(purchaseDissent);
        return purchaseDissentEntity;

    }
}
