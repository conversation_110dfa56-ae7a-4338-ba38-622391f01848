package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 数据推送记录详细日志
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataPushLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 推送记录ID
     */
    private Long pushId;

    /**
     * 请求参数
     */
    private String req;

    /**
     * 响应结果
     */
    private String resp;

    /**
     * 异常信息
     */
    private String err;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新人id
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Integer isDel;


}
