package com.daddylab.supplier.item.application.process;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.types.process.ProcessBusinessType;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/10/20
 */
public interface ProcessBizService {
    Response claim(Long userId, String taskId);

    Response complete(Long userId, String taskId, String comment);

    Response forceComplete(Long userId, String taskId, String comment);

    Response forceComplete(Long userId, String taskId, String comment, Map<String, Object> variables);

    Optional<Task> task(String taskId);

    List<Task> tasksByProcInstId(String processInstId);

    List<HistoricTaskInstance> historicTasksByProcInstId(String processInstId);

    Response moveTo(Long userId, String processInstId, String activityName);

    Response terminal(Long userId, String processInstId, String reason);

    SingleResponse<ProcessInstance> create(Long userId, String processDefKey, ProcessBusinessType businessType, Long businessId, String name, String owner, Map<String, Object> variables);

    SingleResponse<ProcessInstance> createToActivity(Long userId, String processDefKey, ProcessBusinessType businessType, Long businessId, String name, String owner, Map<String, Object> variables, String startActivityName);

    <T> T getVariable(String executionId, String name, Class<T> clazz);

    List<Task> tasksByProcDefId(String processDefId, String taskDefId);

    ProcessInstance processInstance(String processInstanceId);

    void saveTask(Task task);
}
