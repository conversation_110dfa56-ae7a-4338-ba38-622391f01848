package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 旺店通订单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtOrderDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单唯一键
     */
    private Long tradeId;

    /**
     * 订单明细唯一键
     */
    private Long recId;

    /**
     * 平台ID
     */
    private Integer platformId;

    /**
     * 原始子订单号
     */
    private String srcOid;

    /**
     * 原始订单号
     */
    private String srcTid;

    /**
     * 是否是赠品 0、非赠品 1、自动赠送 2、手工赠送 4、周期购赠送 8、平台赠送
     */
    private Integer giftType;

    /**
     * 退款状态: 0、无退款 1、已取消 2、已申请退款 3、待退款，平台已退款,系统未退款,但订单已审核,不能直接改成,除非驳回 4、等待取消,平台上取消退款,系统已退款,但订单已审核,不能直接改成,除非驳回 5、退款成功 6、未付款关闭，未付款的取消了
     */
    private Integer refundStatus;

    /**
     * 退款模式: 1、担保 2、非担保 3 、在线非担保
     */
    private Integer guaranteeMode;

    /**
     * 如果没有对应的原始单明细,则返回0. 平台状态: 10、未确认 20、待尾款 30、待发货 40、部分发货 50、已发货 60、已签收 70、已完成 80、已退款 90、已关闭
     */
    private Integer platformStatus;

    /**
     * 发货条件:1、款到发货 2、货到付款(包含部分货到付款) 3、分期付款
     */
    private Integer deliveryTerm;

    /**
     * 货品数量
     */
    private BigDecimal num;

    /**
     * 标价，手工新建时使用货品属性中的“零售价”
     */
    private BigDecimal price;

    /**
     * 售后退款数量
     */
    private BigDecimal refundNum;

    /**
     * 成交价,原始单折扣及分摊之后的价格
     */
    private BigDecimal orderPrice;

    /**
     * 进入ERP后再次调整的价格，默认值与order_price一致
     */
    private BigDecimal sharePrice;

    /**
     * 手工调整价,正数为加价,负数为减价,暂未处理
     */
    private BigDecimal adjust;

    /**
     * 总折扣金额
     */
    private BigDecimal discount;

    /**
     * 分摊后合计应收=share_price*num
     */
    private BigDecimal shareAmount;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 货品编码
     */
    private String goodsNo;

    /**
     * 规格名
     */
    private String specName;

    /**
     * 商家编码
     */
    private String specNo;

    /**
     * 规格码
     */
    private String specCode;

    /**
     * 组合装商家编码
     */
    private String suiteNo;

    /**
     * 如果是组合装拆分的，此为组合装名称
     */
    private String suiteName;

    /**
     * 组合装数量,不受拆分合并影响
     */
    private BigDecimal suiteNum;

    /**
     * 组合装分摊后总价
     */
    private BigDecimal suiteAmount;

    /**
     * 组合装优惠
     */
    private BigDecimal suiteDiscount;

    /**
     * 平台货品名称
     */
    private String apiGoodsName;

    /**
     * 平台规格名
     */
    private String apiSpecName;

    /**
     * 平台货品id
     */
    private String apiGoodsId;

    /**
     * 平台规格id
     */
    private String apiSpecId;

    /**
     * 佣金
     */
    private BigDecimal commission;

    /**
     * 货品类 型 1销售商品 2原材料 3包装 4周转材料 5虚拟商品 6固定资产 0其它
     */
    private Integer goodsType;

    /**
     * 订单内部来源: 1、手机 2、聚划算
     */
    private Integer fromMask;

    /**
     * 子单备注
     */
    private String remark;

    /**
     * 修改时间 （毫秒级时间戳，例如：1631861379000）
     */
    private LocalDateTime modified;

    /**
     * 创建时间 （毫秒级时间戳，例如：1631861379000）
     */
    private LocalDateTime created;

    /**
     * 自定义属性2
     */
    private String prop2;

    /**
     * 子单预估总重量
     */
    private BigDecimal weight;

    /**
     * 图片路径
     */
    private String imgUrl;

    /**
     * 下单时间 （毫 秒级时间戳，例如：1631861379000）
     */
    private LocalDateTime tradeTime;

    /**
     * 支付时间，例如：2020-10-19 00:00:00
     */
    private LocalDateTime payTime;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 供应商ID
     */
    private Long providerId;

    /**
     * 删除时间
     */
    private Long deletedAt;


}
