package com.daddylab.supplier.item.infrastructure.config.batch.reader.mybatis;

import com.daddylab.supplier.item.infrastructure.config.batch.reader.AbstractIdPagingItemReader;
import com.daddylab.supplier.item.infrastructure.config.batch.reader.BaseModelInterface;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.batch.MyBatisPagingItemReader;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

import static org.springframework.util.Assert.notNull;
import static org.springframework.util.ClassUtils.getShortName;

/**
 * Class  PagingItemReader
 * 自定义分页（id分页）
 *
 * @Date 2021/11/8上午9:51
 * <AUTHOR>
 */
public class MybatisPagingItemReader<T extends BaseModelInterface> extends AbstractIdPagingItemReader<T> {


    private String queryId;

    private SqlSessionFactory sqlSessionFactory;

    private SqlSessionTemplate sqlSessionTemplate;

    private Map<String, Object> parameterValues;

    public MybatisPagingItemReader() {
        setName(getShortName(MyBatisPagingItemReader.class));
    }

    /**
     * Public setter for {@link SqlSessionFactory} for injection purposes.
     *
     * @param sqlSessionFactory
     *          a factory object for the {@link SqlSession}.
     */
    public void setSqlSessionFactory(SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionFactory = sqlSessionFactory;
    }

    /**
     * Public setter for the statement id identifying the statement in the SqlMap configuration file.
     *
     * @param queryId
     *          the id for the statement
     */
    public void setQueryId(String queryId) {
        this.queryId = queryId;
    }

    /**
     * The parameter values to be used for the query execution.
     *
     * @param parameterValues
     *          the values keyed by the parameter named used in the query string.
     */
    public void setParameterValues(Map<String, Object> parameterValues) {
        this.parameterValues = parameterValues;
    }

    /**
     * Check mandatory properties.
     *
     * @see org.springframework.beans.factory.InitializingBean#afterPropertiesSet()
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        super.afterPropertiesSet();
        notNull(sqlSessionFactory, "A SqlSessionFactory is required.");
        notNull(queryId, "A queryId is required.");
    }

    @Override
    protected void doReadPage() {
        if (sqlSessionTemplate == null) {
            sqlSessionTemplate = new SqlSessionTemplate(sqlSessionFactory, ExecutorType.BATCH);
        }
        Map<String, Object> parameters = new HashMap<>();
        if (parameterValues != null) {
            parameters.putAll(parameterValues);
        }
        parameters.put("_page", getPage());
        parameters.put("_id", getMaxId());
        parameters.put("_pagesize", getPageSize());
        parameters.put("_skiprows", getPage() * getPageSize());
        if (results == null) {
            results = new CopyOnWriteArrayList<>();
        } else {
            results.clear();
        }
        results.addAll(sqlSessionTemplate.selectList(queryId, parameters));
    }

    @Override
    protected void doJumpToPage(int itemIndex) {
        // Not Implemented
    }
}
