package com.daddylab.supplier.item.controller.open;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.third.ThirdAccessTokenBizService;
import com.daddylab.supplier.item.domain.third.dto.AccessTokenForm;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * <AUTHOR>
 * @class AccessTokenController.java
 * @description 描述类的作用
 * @date 2024-02-27 17:28
 */
@Slf4j
@Api(value = "售后管理开放接口", tags = "售后管理开放接口")
@RestController
@RequestMapping("/open/api/third")
public class OpenAccessTokenController {

    @Resource
    private ThirdAccessTokenBizService thirdAccessTokenBizService;

    @PostMapping("/accessToken")
    public SingleResponse<Boolean> accessToken(@Valid @RequestBody AccessTokenForm accessTokenForm) {
        return  thirdAccessTokenBizService.saveAccessToken(accessTokenForm);
    }
}
