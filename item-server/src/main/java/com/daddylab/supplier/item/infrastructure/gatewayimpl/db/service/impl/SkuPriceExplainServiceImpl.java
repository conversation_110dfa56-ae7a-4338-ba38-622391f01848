package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.SkuPriceExplain;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.SkuPriceExplainMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ISkuPriceExplainService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * sku价格说明表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@Service
public class SkuPriceExplainServiceImpl extends DaddyServiceImpl<SkuPriceExplainMapper, SkuPriceExplain> implements ISkuPriceExplainService {

}
