package com.daddylab.supplier.item.domain.order;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 大数据订单隐私数据
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrderSensitiveInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    //订单号
    private String orderNo;
    //收货手机号
    private String receiverPhone;
    //店铺名称
    private String shopName;
    //收货人姓名
    private String receiverName;
    //收货详细地址
    private String receiverAddr;

}
