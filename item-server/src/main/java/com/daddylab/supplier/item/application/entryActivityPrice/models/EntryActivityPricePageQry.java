package com.daddylab.supplier.item.application.entryActivityPrice.models;

import cn.hutool.core.date.DatePattern;
import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/4
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EntryActivityPricePageQry extends PageQuery {
    private static final long serialVersionUID = 8990702863241094466L;

    private List<Long> priceItemIdList;
    private String itemName;
    private String skuCode;
    private String itemCode;
    private Long buyerUid;
    private Long providerId;
    private List<Long> providerIds;
    @ApiModelProperty(value = "确认状态", notes = "确认状态:-1 待发起 0 待确认 1 已确认 2 存在异议")
    private Integer status;
    @ApiModelProperty(value = "确认状态", notes = "确认状态:-1 待发起 0 待确认 1 已确认 2 存在异议")
    private List<Integer> statusList;
    private Long month;

    public String getMonthStr() {
        return DateUtil.format(month, DatePattern.NORM_MONTH_PATTERN);
    }

    @ApiModelProperty(value = "合作方")
    private List<Integer> corpType;

    @ApiModelProperty(value = "业务类型")
    private List<Integer> bizType;

}
