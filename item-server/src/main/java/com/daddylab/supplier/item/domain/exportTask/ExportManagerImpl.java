package com.daddylab.supplier.item.domain.exportTask;

import cn.hutool.core.date.DatePattern;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.daddylab.supplier.item.common.ErrorChecker;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.infrastructure.common.ExternalUserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/3/4
 */
@Component
@Slf4j
public class ExportManagerImpl implements ExportManager {
  @Autowired private ExportTaskGateway exportTaskGateway;

  @Autowired FileGateway fileGateway;

  @Override
  public ExportTask export(ExportTaskType type, Class<?> clazz, ExportHandler exportHandler) {
    return export(type, exportHandler, (task, excelBuilder) -> excelBuilder.head(clazz));
  }

  @Override
  public ExportTask export(
      ExportTaskType type,
      Class<?> clazz,
      ExportHandler exportHandler,
      BiConsumer<ExportTask, ExcelWriterBuilder> customizer) {
    return export(
        type,
        exportHandler,
        (task, excelBuilder) -> {
          excelBuilder.head(clazz);
          customizer.accept(task, excelBuilder);
        });
  }

  @Override
  public ExportTask export(ExportTaskType type, List<String> head, ExportHandler exportHandler) {
    return export(
        type,
        exportHandler,
        (task, excelBuilder) -> {
          excelBuilder.head(
              head.stream().map(Collections::singletonList).collect(Collectors.toList()));
        });
  }

  @Override
  public ExportTask exportMultiRowHead(
      ExportTaskType type, List<List<String>> head, ExportHandler exportHandler) {
    return export(
        type,
        exportHandler,
        (task, excelWriterBuilder) -> {
          excelWriterBuilder.head(head);
        });
  }

  @Override
  public ExportTask export(
      ExportTaskType type,
      ExportHandler exportHandler,
      BiConsumer<ExportTask, ExcelWriterBuilder> customizer) {
    ExportTask exportTask = newExportTask(type);
    final Long taskId = exportTask.getId();
    final Runnable runnable =
        () -> {
          try {
            Path tempFilePath = getTempFilePath(type, exportTask);
            final OutputStream fileOutputStream = Files.newOutputStream(tempFilePath);
            final ExcelWriterBuilder excelWriterBuilder =
                EasyExcel.write(fileOutputStream).useDefaultStyle(false);
            if (customizer != null) {
              customizer.accept(exportTask, excelWriterBuilder);
            }

            final ExcelWriter excelWriter = excelWriterBuilder.build();
            final WriteSheet sheet = EasyExcel.writerSheet(0).sheetName("工作簿1").build();

            int pageIndex = 1;
            do {
              final PageResponse<?> pageResponse = exportHandler.handle(pageIndex);
              if (pageResponse.getTotalCount() == 0) {
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "导出任务查询过程出现异常");
              }
              ErrorChecker.checkAndThrowIfError(pageResponse);

              excelWriter.write(pageResponse.getData(), sheet);

              if (pageResponse.getTotalCount() > 0) {
                final BigDecimal progress =
                    new BigDecimal(pageIndex * 100)
                        .divide(
                            new BigDecimal(pageResponse.getTotalPages()), 6, RoundingMode.FLOOR);
                exportTask.setProgress(progress);
              } else {
                exportTask.setProgress(new BigDecimal(pageIndex));
              }
              exportTaskGateway.updateProgress(exportTask);
              if (pageResponse.isEmpty()
                  || pageResponse.getTotalCount() > 0 && pageIndex >= pageResponse.getTotalPages()
                  || pageResponse.getPageIndex() < pageIndex) {
                break;
              }
              pageIndex++;
            } while (true);
            excelWriter.finish();
            fileOutputStream.close();
            uploadCloud(exportTask, tempFilePath);
          } catch (Exception e) {
            log.error("【导出管理器】{} 导出异常 导出任务ID={}", exportTask.getName(), taskId, e);
            exportTask.setStatus(ExportTaskStatus.FAIL);
            exportTask.setError(
                StringUtil.truncateByMaxBytes(ExceptionUtil.getSimpleStackString(e), 20000));
          } finally {
            exportTaskGateway.saveOrUpdateExportTask(exportTask);
          }
        };
    ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(RunnableWrapper.of(runnable));
    return exportTask;
  }

  private Path getTempFilePath(ExportTaskType type, ExportTask exportTask) {
    final String pathStr =
        String.format("/tmp/库存管理/%s/%s.xlsx", type.getDesc(), exportTask.getName());
    cn.hutool.core.io.FileUtil.mkParentDirs(pathStr);
    try {
      return Files.createFile(Paths.get(pathStr));
    } catch (IOException e) {
      log.error("[导出]临时文件创建异常:{}", pathStr, e);
      throw ExceptionPlusFactory.bizException(ErrorCode.FILE_ERROR, "创建临时文件失败");
    }
  }

  @NonNull
  private ExportTask newExportTask(ExportTaskType type) {
    ExportTask exportTask = new ExportTask();
    exportTask.setStatus(ExportTaskStatus.RUNNING);
    exportTask.setName(type.getDesc() + DateUtil.formatNow(DatePattern.PURE_DATETIME_PATTERN));
    exportTask.setType(type);
    ExternalUserContext.getOptional()
        .ifPresent(
            externalUser -> {
              exportTask.setExternalUserId(externalUser.getId());
            });
    exportTaskGateway.saveOrUpdateExportTask(exportTask);
    return exportTask;
  }

  private void uploadCloud(ExportTask exportTask, Path tempFilePath) throws IOException {
    UploadFileAction action =
        UploadFileAction.ofInputStream(
            Files.newInputStream(tempFilePath), exportTask.getName() + ".xlsx");
    final String url = fileGateway.uploadFile(action).getUrl();
    exportTask.setDownloadUrl(url);
    exportTask.setStatus(ExportTaskStatus.SUCCESS);
    log.info(
        "【导出管理器】{} 导出成功 文件URL={} 导出任务ID={}",
        exportTask.getName(),
        exportTask.getDownloadUrl(),
        exportTask.getId());
  }
}
