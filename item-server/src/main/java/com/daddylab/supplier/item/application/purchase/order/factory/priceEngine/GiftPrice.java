package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine;

import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年02月27日 2:25 PM
 */
@Slf4j
@Service
public class GiftPrice extends BaseProcess implements PriceProcessor {

    /**
     * 仅手工赠送和自动赠送的成本判断为0元
     */

    @Override
    public Boolean doPriceProcess(TimeBO timeBO) {

        Integer count = iWdtOrderDetailWrapperService.lambdaQuery()
                .eq(WdtOrderDetailWrapper::getOperateTime, timeBO.getOperateMonth())
                // 0:表示非赠品，其他字段表示各种类型的赠品
                .in(WdtOrderDetailWrapper::getIsGift, FREE_GIFT_TYPE).count();
        log.info("GiftPrice handler size:{}",count);
        if (count <= 0) {
            return true;
        }

        try {
            List<WdtOrderDetailWrapper> list = iWdtOrderDetailWrapperService.lambdaQuery()
                    .eq(WdtOrderDetailWrapper::getOperateTime, timeBO.getOperateMonth())
                    .in(WdtOrderDetailWrapper::getIsGift, FREE_GIFT_TYPE).list();
            for (WdtOrderDetailWrapper one : list) {
                saveSkuPriceLog(one.getSkuCode(), BigDecimal.ZERO, timeBO.getOperateMonth(), "赠品成本为0元");
            }
            iWdtOrderDetailWrapperService.lambdaUpdate()
                    .set(WdtOrderDetailWrapper::getPrice, BigDecimal.ZERO)
                    .eq(WdtOrderDetailWrapper::getOperateTime, timeBO.getOperateMonth())
                    // 0:表示非赠品，其他字段表示各种类型的赠品
                    .in(WdtOrderDetailWrapper::getIsGift, FREE_GIFT_TYPE)
                    .update();
        } catch (Exception e) {
            log.error("GiftPrice doPriceProcess fail", e);
        }
        return true;
    }
}
