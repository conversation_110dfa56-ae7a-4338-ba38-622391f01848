package com.daddylab.supplier.item.controller.item;

import com.alibaba.cola.dto.PageResponse;
import com.daddylab.ark.sailor.item.domain.query.item.ItemPageQuery;
import com.daddylab.supplier.item.types.item.ItemListWithSkuVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024/1/22
 */
@RestController("/ark-mall-item")
@Api(value = "小程序商城商品API", tags = "小程序商城商品API")
public class ArkSailorItemController {

    @Autowired
    private ArkSailorItemBizService arkSailorItemBizService;

    @ApiOperation(value = "商品分页查询", notes = "商品分页查询", httpMethod = "POST")
    @PostMapping("/itemPageQuery")
    PageResponse<ItemListWithSkuVO> itemPageQuery(@RequestBody ItemPageQuery query) {
        return arkSailorItemBizService.itemWithSkuPageQuery(query);
    }

}
