package com.daddylab.supplier.item.application.payment;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/11/24
 */
@Data
@ConfigurationProperties(prefix = "payment-apply")
@Configuration
@RefreshScope
public class PaymentApplyConfig {

    /**
     * OA审批流程模板编码
     */
    private String oaProcessTemplateCode = "CGFK_TEST";

    /**
     * 停止接收OA状态回调
     */
    private boolean suppressOaCallback;
}
