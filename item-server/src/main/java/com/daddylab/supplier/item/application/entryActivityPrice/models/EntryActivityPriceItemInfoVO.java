package com.daddylab.supplier.item.application.entryActivityPrice.models;

import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/4
 */
@Data
public class EntryActivityPriceItemInfoVO {
    private Long itemId;
    private String itemName;
    private String imgUrl;
    private String provider;
    private Long providerId;
    private StaffBrief buyer;
    private List<EntryActivityPriceSkuInfoVO> skuList;

    @ApiModelProperty(value = "合作方+业务类型")
    private List<CorpBizTypeDTO> corpBizType;
}

