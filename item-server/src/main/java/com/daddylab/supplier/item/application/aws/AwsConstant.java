package com.daddylab.supplier.item.application.aws;

/**
 * aws process 接口方法常量
 */
public class AwsConstant {

    //创建流程实例
    public static final String PROCESS_CREATE = "process.create";

    //检查流程是否处理挂起状态
    public static final String PROCESS_SUSPEND_CHECK = "process.suspend.check";

    //流程挂起
    public static final String PROCESS_SUSPEND = "process.suspend";

    //流程从挂起中恢复
    public static final String PROCESS_RESUME = "process.resume";

    // 通过ID启动流程
    public static final String PROCESS_START = "process.start";

    //绑定数据
    public static final String BO_CREATE = "bo.create";

    //（批量）根据流程实例更新BO数据
    public static final String BO_FIELD_UPDATE = "bo.field.update";

    //删除与某流程实例相关的BO表记录
    public static final String BO_REMOVE_BINDID = "bo.remove.bindId";

    //查询BO
    public static final String BO_QUERY = "bo.query";

    // 获得一个流程实例对象
    public static final String PROCESS_INST_GET = "process.inst.get";

    // 尚未结束的流程 重置流程到第一个节点
    public static final String PROCESS_RESTART = "process.restart";

    //查询留言审批记录
    public static final String PROCESS_COMMENTS_GET = "process.comments.get";

    //已经结束的流程 重置流程到第一个节点
    public static final String  PROCESS_REACTIVATE = "process.reactivate";

    //验证流程是否结束
    public static final String PROCESS_END_CHECK = "process.end.check";

    //终止流程
    public static final String PROCESS_TERMINATE = "process.terminate";

    //查询当前任务（只返回20条）
    public static final String TASK_QUERY = "task.query";

    //查询当前任务（分页）
    public static final String TASK_QUERY_PAGE = "task.query.page";

    // 查询历史任务
    public static final String  TASK_HISTORY_QUERY = "task.history.query";

    //为任务提交审批留言
    public static final String TASK_COMMENT_COMMIT = "task.comment.commit";

    //提交任务
    public static final String TASK_COMPLETE = "task.complete";

    // 将待办任务委派给其他人处理，不会产生新任务，该操作更改任务执行人信息
    public static final String TASK_DELEGATE = "task.delegate";

    // 获得指定节点路由方案配置的参与者。该方法调用路由方案接口的getHumanPerformer（执行人）+getPotentialOwner（候选人 ），然后去重
    public static final String TASK_PARTICIPANTS_GET = "task.participants.get";
}
