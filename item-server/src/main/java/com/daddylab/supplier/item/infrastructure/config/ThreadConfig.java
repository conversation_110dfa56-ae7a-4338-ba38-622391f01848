package com.daddylab.supplier.item.infrastructure.config;

import com.daddylab.supplier.item.infrastructure.common.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.task.TaskSchedulerBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.Optional;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/30 11:47 上午
 * @description
 */
@Slf4j
@Configuration
public class ThreadConfig {

    public ThreadConfig() {
    }

    @Bean("taskScheduler")
    public ThreadPoolTaskScheduler taskScheduler() {
        final ThreadPoolTaskScheduler taskScheduler = new TaskSchedulerBuilder()
                .threadNamePrefix("task-scheduler-")
                .poolSize(3)
                .build();
        taskScheduler.initialize();
        return taskScheduler;
    }

    @Bean("eventExecutor")
    public ThreadPoolTaskExecutor eventExecutor() {
        //线程池实例
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();

        /**
         * 初始化线程池
         */
        threadPoolTaskExecutor.setCorePoolSize(32);
        threadPoolTaskExecutor.setMaxPoolSize(64);
        threadPoolTaskExecutor.setKeepAliveSeconds(10);
        threadPoolTaskExecutor.setQueueCapacity(512);
        threadPoolTaskExecutor.setThreadNamePrefix("event-pool-");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskExecutor.setAwaitTerminationSeconds(60);
        threadPoolTaskExecutor.setTaskDecorator(TaskDecorator.INSTANCE);

        /**
         * 拒绝策略
         * ThreadPoolExecutor.AbortPolicy:丢弃任务并抛出RejectedExecutionException异常。
         * ThreadPoolExecutor.DiscardPolicy：也是丢弃任务，但是不抛出异常。
         * ThreadPoolExecutor.DiscardOldestPolicy：丢弃队列最前面的任务，然后重新尝试执行任务（重复此过程）
         * ThreadPoolExecutor.CallerRunsPolicy：由调用线程处理该任务
         */
//        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());

        // 初始化
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("commonExecutor")
    public ThreadPoolTaskExecutor commonExecutor() {
        //线程池实例
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();

        /**
         * 初始化线程池
         */
        threadPoolTaskExecutor.setCorePoolSize(32);
        threadPoolTaskExecutor.setMaxPoolSize(64);
        threadPoolTaskExecutor.setKeepAliveSeconds(10);
        threadPoolTaskExecutor.setQueueCapacity(1024);
        threadPoolTaskExecutor.setThreadNamePrefix("common-pool");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskExecutor.setAwaitTerminationSeconds(60);
        threadPoolTaskExecutor.setTaskDecorator(TaskDecorator.INSTANCE);

        /**
         * 拒绝策略
         * ThreadPoolExecutor.AbortPolicy:丢弃任务并抛出RejectedExecutionException异常。
         * ThreadPoolExecutor.DiscardPolicy：也是丢弃任务，但是不抛出异常。
         * ThreadPoolExecutor.DiscardOldestPolicy：丢弃队列最前面的任务，然后重新尝试执行任务（重复此过程）
         * ThreadPoolExecutor.CallerRunsPolicy：由调用线程处理该任务
         */
//        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());

        // 初始化
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("exportExecutor")
    public ThreadPoolTaskExecutor exportExecutor() {

        //线程池实例
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();

        /**
         * 初始化线程池
         */
        threadPoolTaskExecutor.setCorePoolSize(8);
        threadPoolTaskExecutor.setMaxPoolSize(16);
        threadPoolTaskExecutor.setKeepAliveSeconds(10);
        threadPoolTaskExecutor.setQueueCapacity(512);
        threadPoolTaskExecutor.setThreadNamePrefix("export-pool-");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskExecutor.setAwaitTerminationSeconds(60);
        threadPoolTaskExecutor.setTaskDecorator(TaskDecorator.INSTANCE);

        /**
         * 拒绝策略
         * ThreadPoolExecutor.AbortPolicy:丢弃任务并抛出RejectedExecutionException异常。
         * ThreadPoolExecutor.DiscardPolicy：也是丢弃任务，但是不抛出异常。
         * ThreadPoolExecutor.DiscardOldestPolicy：丢弃队列最前面的任务，然后重新尝试执行任务（重复此过程）
         * ThreadPoolExecutor.CallerRunsPolicy：由调用线程处理该任务
         */
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());

        // 初始化
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("syncExecutor")
    public ThreadPoolTaskExecutor syncExecutor() {
        //线程池实例
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();

        /**
         * 初始化线程池
         */
        threadPoolTaskExecutor.setCorePoolSize(8);
        threadPoolTaskExecutor.setMaxPoolSize(16);
        threadPoolTaskExecutor.setKeepAliveSeconds(10);
        threadPoolTaskExecutor.setQueueCapacity(1024);
        threadPoolTaskExecutor.setThreadNamePrefix("common-biz-Pool");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskExecutor.setAwaitTerminationSeconds(60);
        threadPoolTaskExecutor.setTaskDecorator(TaskDecorator.INSTANCE);

        /**
         * 拒绝策略
         * ThreadPoolExecutor.AbortPolicy:丢弃任务并抛出RejectedExecutionException异常。
         * ThreadPoolExecutor.DiscardPolicy：也是丢弃任务，但是不抛出异常。
         * ThreadPoolExecutor.DiscardOldestPolicy：丢弃队列最前面的任务，然后重新尝试执行任务（重复此过程）
         * ThreadPoolExecutor.CallerRunsPolicy：由调用线程处理该任务
         */
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());

        // 初始化
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("jobExecutor")
    public ThreadPoolTaskExecutor jobExecutor() {
        //线程池实例
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();

        /**
         * 初始化线程池
         */
        threadPoolTaskExecutor.setCorePoolSize(32);
        threadPoolTaskExecutor.setMaxPoolSize(64);
        threadPoolTaskExecutor.setKeepAliveSeconds(10);
        threadPoolTaskExecutor.setQueueCapacity(1024);
        threadPoolTaskExecutor.setThreadNamePrefix("job-pool");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskExecutor.setAwaitTerminationSeconds(60);
        threadPoolTaskExecutor.setTaskDecorator(TaskDecorator.INSTANCE);


        /**
         * 拒绝策略
         * ThreadPoolExecutor.AbortPolicy:丢弃任务并抛出RejectedExecutionException异常。
         * ThreadPoolExecutor.DiscardPolicy：也是丢弃任务，但是不抛出异常。
         * ThreadPoolExecutor.DiscardOldestPolicy：丢弃队列最前面的任务，然后重新尝试执行任务（重复此过程）
         * ThreadPoolExecutor.CallerRunsPolicy：由调用线程处理该任务
         */
//        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());

        // 初始化
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("purchaseExecutor")
    public ThreadPoolTaskExecutor purchaseExecutor() {
        //线程池实例
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();

        /**
         * 初始化线程池
         */
        threadPoolTaskExecutor.setCorePoolSize(64);
        threadPoolTaskExecutor.setMaxPoolSize(128);
        threadPoolTaskExecutor.setKeepAliveSeconds(10);
        threadPoolTaskExecutor.setQueueCapacity(1024);
        threadPoolTaskExecutor.setThreadNamePrefix("purchase-pool");
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskExecutor.setAwaitTerminationSeconds(60);
        threadPoolTaskExecutor.setTaskDecorator(TaskDecorator.INSTANCE);

        /**
         * 拒绝策略
         * ThreadPoolExecutor.AbortPolicy:丢弃任务并抛出RejectedExecutionException异常。
         * ThreadPoolExecutor.DiscardPolicy：也是丢弃任务，但是不抛出异常。
         * ThreadPoolExecutor.DiscardOldestPolicy：丢弃队列最前面的任务，然后重新尝试执行任务（重复此过程）
         * ThreadPoolExecutor.CallerRunsPolicy：由调用线程处理该任务
         */
//        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());

        // 初始化
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }


    @Bean("singleExecutor")
    public ExecutorService singleExecutor() {
        return Executors.newSingleThreadExecutor();
    }

    public static void main(String[] args) {
        // 创建只允许一个线程执行的线程池
        ExecutorService executor = Executors.newSingleThreadExecutor();
        // 提交任务到线程池
        executor.submit(new MyTask("Task 1"));
        executor.submit(new MyTask("Task 2"));
        executor.submit(new MyTask("Task 3"));
        // 关闭线程池
        executor.shutdown();
    }

    static class MyTask implements Runnable {
        private String name;

        public MyTask(String name) {
            this.name = name;
        }

        @Override
        public void run() {
            try {
                TimeUnit.SECONDS.sleep(3);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            System.out.println("Executing " + name + " on thread " + Thread.currentThread().getName());
            // 执行任务的逻辑
        }
    }


    public static class TaskDecorator implements org.springframework.core.task.TaskDecorator {
        public static TaskDecorator INSTANCE = new TaskDecorator();

        @Override
        public Runnable decorate(Runnable runnable) {
            final Optional<UserContext.UserInfo> userInfo = UserContext.getUserInfo();
            return () -> {
                userInfo.ifPresent(UserContext::setUserHolder);
                try {
                    runnable.run();
                } catch (Exception e) {
                    log.error("异步任务执行异常", e);
                } finally {
                    UserContext.remove();
                }
            };
        }
    }
}
