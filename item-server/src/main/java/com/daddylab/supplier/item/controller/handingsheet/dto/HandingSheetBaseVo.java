package com.daddylab.supplier.item.controller.handingsheet.dto;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.HandingSheetActivityTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/24 15:20
 * @Description: 盘货表基础信息VO
 */
@Data
@ApiModel("盘货表基础信息VO")
public class HandingSheetBaseVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "盘货表主键ID")
    private Long id;

    @ApiModelProperty(value = "表格名称")
    private String name;

    @ApiModelProperty(value = "活动开始时间（时间戳，单位秒）")
    private Long startTime;

    @ApiModelProperty(value = "活动结束时间（时间戳，单位秒）")
    private Long endTime;

    /**
     * {@link Platform}
     */
    @ApiModelProperty(value = "所属平台（0-其他，1-淘宝，2-有赞，3-抖店，4-快手小店，5-小红书，7-小程序）")
    private List<Integer> platformVals;

    @ApiModelProperty(value = "活动标签")
    private String label;

    @ApiModelProperty(value = "活动成员（职员）")
    private List<DadStaffVO> staffs;

    @ApiModelProperty(value = "附件 json 格式", example = "[{\"1\":\"www.baidu.com\"},{\"2\":\"www.ailibaba.com\"}]")
    private String attachments;

    /**
     * {@link HandingSheetActivityTypeEnum}
     */
    @ApiModelProperty(value = "活动类型（1-循环满减，2-阶梯满减）")
    private Integer activityType;

    @ApiModelProperty(value = "活动力度")
    private List<HandingSheetActivityEventVo> activityEvents;

    /**
     * 状态（1-待提交，2-待审核，3-已过审，4-进行中，5-已过期）
     */
    @ApiModelProperty(value = "状态（1-待提交，2-待审核，3-已过审，4-进行中，5-已过期）")
    private Integer state;

}
