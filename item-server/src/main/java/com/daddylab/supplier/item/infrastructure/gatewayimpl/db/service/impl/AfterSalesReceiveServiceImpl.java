package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.daddylab.supplier.item.application.afterSales.AfterSalesReceiveInfoVO;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesReceive;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesImageType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesReceiveResult;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesReceiveState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AfterSalesReceiveMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesInfoImageService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesReceiveService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWarehouseService;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 售后单登记收货 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
@Service
@AllArgsConstructor
public class AfterSalesReceiveServiceImpl extends
        DaddyServiceImpl<AfterSalesReceiveMapper, AfterSalesReceive> implements
        IAfterSalesReceiveService {

    private final IAfterSalesInfoImageService afterSalesInfoImageService;
    private final IWarehouseService warehouseService;

    @Override
    public Map<String, AfterSalesReceiveState> queryReceiveStateBatch(
            Collection<String> refundOrderNos) {
        if (CollectionUtil.isEmpty(refundOrderNos)) {
            return Collections.emptyMap();
        }
        final Map<String, AfterSalesReceiveState> initReceiveStateMap = refundOrderNos.stream()
                .collect(Collectors.toMap(Function.identity(), v -> AfterSalesReceiveState.WAIT,
                        (a, b) -> a));
        lambdaQuery()
                .in(AfterSalesReceive::getReturnOrderNo, refundOrderNos).list()
                .forEach(v -> initReceiveStateMap.put(v.getReturnOrderNo(), v.getState()));
        return initReceiveStateMap;
    }

    @Override
    public Optional<AfterSalesReceiveInfoVO> getAfterSalesReceiveInfoVO(String refundOrderNo) {

        final Optional<AfterSalesReceive> afterSalesReceiveOptional = lambdaQuery().eq(
                AfterSalesReceive::getReturnOrderNo, refundOrderNo).oneOpt();
        if (!afterSalesReceiveOptional.isPresent()) {
            return Optional.empty();
        }
        final AfterSalesReceive afterSalesReceive = afterSalesReceiveOptional.get();
        if (afterSalesReceive.getForceConfirm()) {
            return Optional.empty();
        }
        final AfterSalesReceiveInfoVO afterSalesReceiveInfoVO = new AfterSalesReceiveInfoVO();
        final String returnWarehouseNo = afterSalesReceive.getReturnWarehouseNo();
        afterSalesReceiveInfoVO.setReturnWarehouseNo(returnWarehouseNo);
        afterSalesReceiveInfoVO.setReturnWarehouse(
                warehouseService.queryWarehouseByNo(returnWarehouseNo).map(
                        Warehouse::getName).orElse(""));
        afterSalesReceiveInfoVO.setReceiveTime(afterSalesReceive.getCreatedAt());
        afterSalesReceiveInfoVO.setReceiveResult(afterSalesReceive.getReceiveResult());
        afterSalesReceiveInfoVO.setRemark(afterSalesReceive.getRemark());
        afterSalesReceiveInfoVO.setState(afterSalesReceive.getState());

        final List<String> imageUrls = afterSalesInfoImageService.getImageUrlsRelateIdAndType(
                afterSalesReceive.getId(), AfterSalesImageType.RECEIVE);
        afterSalesReceiveInfoVO.setImages(imageUrls);
        return Optional.of(afterSalesReceiveInfoVO);
    }

    @Override
    public boolean confirmReceive(String refundOrderNo) {
        return lambdaUpdate().eq(AfterSalesReceive::getReturnOrderNo, refundOrderNo)
                .set(AfterSalesReceive::getState, AfterSalesReceiveState.CONFIRMED)
                .eq(AfterSalesReceive::getState,
                        AfterSalesReceiveState.RECEIVED).update();
    }

    @Override
    public boolean forceConfirmReceive(String refundOrderNo, Boolean update) {
        if (update) {
            return lambdaUpdate().eq(AfterSalesReceive::getReturnOrderNo, refundOrderNo)
                    .set(AfterSalesReceive::getForceConfirm, true)
                    .set(AfterSalesReceive::getState, AfterSalesReceiveState.CONFIRMED)
                    .eq(AfterSalesReceive::getState,
                            AfterSalesReceiveState.WAIT).update();
        } else {
            final AfterSalesReceive afterSalesReceive = new AfterSalesReceive();
            afterSalesReceive.setReturnOrderNo(refundOrderNo);
            afterSalesReceive.setReturnWarehouseNo("");
            afterSalesReceive.setReceiveResult(AfterSalesReceiveResult.UNKNOWN);
            afterSalesReceive.setRemark("");
            afterSalesReceive.setState(AfterSalesReceiveState.CONFIRMED);
            afterSalesReceive.setForceConfirm(true);
            save(afterSalesReceive);
            return true;
        }
    }
}
