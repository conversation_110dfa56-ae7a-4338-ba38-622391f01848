package com.daddylab.supplier.item.application.purchase.order;

import static com.daddylab.supplier.item.common.GlobalConstant.PURCHASE_ORDER_SUFFIX;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.actionsoft.sdk.service.model.TaskInstance;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.application.aws.event.AwsChangeEventListener;
import com.daddylab.supplier.item.application.aws.service.AwsApprovalFlowService;
import com.daddylab.supplier.item.application.item.combinationItem.CombinationItemBizService;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuVO;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.order.OrderService;
import com.daddylab.supplier.item.application.payment.dto.PaymentDetailAmountVO;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.PurchasePaymentDetailVo;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.application.system.BaseInfoService;
import com.daddylab.supplier.item.application.warehouse.WarehouseGateway;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.DetailModelType;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.common.trans.PurchaseOrderTransMapper;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseDetailLineVO;
import com.daddylab.supplier.item.controller.purchase.dto.order.*;
import com.daddylab.supplier.item.domain.auth.enums.ResourceType;
import com.daddylab.supplier.item.domain.base.ErpGroupGateway;
import com.daddylab.supplier.item.domain.daddylabWorkbench.DaddylabWorkbenchGateway;
import com.daddylab.supplier.item.domain.departments.DepartmentGateway;
import com.daddylab.supplier.item.domain.exportTask.ExportDomainService;
import com.daddylab.supplier.item.domain.exportTask.ExportEntityPlus;
import com.daddylab.supplier.item.domain.exportTask.ExportType;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import com.daddylab.supplier.item.domain.exportTask.dto.purchase.PurchaseOrderDetailSheet;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.item.gateway.BuyerGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.bpm.BpmClient;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CombinationNameDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.StockInOrderDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.WarehouseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.BpmFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerContract;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerContractQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.OASealProcessForm;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.OASealProcessForm.DataForm;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.OaGenerateUrlReq;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLock;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLockKey;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.daddylab.supplier.item.types.stockInOrder.StockInOrderDetailApplyQuantity;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.fasterxml.jackson.databind.JsonNode;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.javers.core.diff.Diff;
import org.javers.core.diff.changetype.PropertyChange;
import org.javers.core.diff.changetype.ValueChange;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * <AUTHOR> up
 * @date 2022/3/25 10:05 上午
 */
@Slf4j
@Service
public class PurchaseOrderBizServiceImpl implements PurchaseOrderBizService {

  @Autowired IPurchaseOrderService iPurchaseOrderService;

  @Autowired IPurchaseOrderDetailService iPurchaseOrderDetailService;

  @Autowired IStockInOrderDetailService iStockInOrderDetailService;

  @Autowired IStockInOrderService iStockInOrderService;

  @Autowired OperateLogGateway operateLogGateway;

  @Autowired ProviderGateway providerGateway;

  @Autowired
  @Qualifier("UserGatewayCacheImpl")
  UserGateway userGateway;

  @Autowired ExportDomainService exportDomainService;

  @Autowired BaseInfoService baseInfoService;

  @Autowired IWarehouseService iWarehouseService;

  @Autowired ItemGateway itemGateway;

  @Autowired CombinationItemBizService combinationItemBizService;

  @Autowired ICombinationItemService iCombinationItemService;

  @Autowired BuyerGateway buyerGateway;

  @Autowired AwsApprovalFlowService awsApprovalFlowService;

  @Autowired IAwsBusinessLogService iAwsBusinessLogService;

  @Autowired DepartmentGateway departmentGateway;

  @Autowired WarehouseGateway warehouseGateway;

  @Autowired IStockOutOrderService iStockOutOrderService;

  @Resource IStockOutOrderDetailService iStockOutOrderDetailService;

  @Autowired IPurchasePayableOrderService iPurchasePayableOrderService;

  @Autowired OrderService orderService;

  @Autowired IProviderService iProviderService;

  @Autowired ErpGroupGateway erpGroupGateway;

  @Autowired ItemSkuGateway itemSkuGateway;

  @Autowired IItemSkuService iItemSkuService;

  @Resource IPaymentApplyOrderService iPaymentApplyOrderService;

  @Resource IPaymentApplyOrderDetailService iPaymentApplyOrderDetailService;

  @Resource FileGateway fileGateway;

  @Resource IExportTaskService iExportTaskService;

  @Resource MsgSender msgSender;

  @Resource RefreshConfig refreshConfig;

  @Resource StaffService staffService;

  @Resource DaddylabWorkbenchGateway daddylabWorkbenchGateway;

  @Resource AwsChangeEventListener awsChangeEventListener;

  @Resource IBizLevelDivisionService iBizLevelDivisionService;

  @Resource IItemService iItemService;

  @Autowired PartnerFeignClient partnerFeignClient;

  @Autowired BpmClient bpmClient;

  @Autowired BpmFeignClient bpmFeignClient;

  private void setPermission(PurchaseOrderPageQuery query) {
    // 采购订单三波权限
    // 1.只能看到自己创建的
    // 2.能看到全部业务人员创建订单
    // 3.能看到系统创建订单
    boolean seeAllOrder = UserPermissionJudge.canViewAllPurchaseOrder();
    boolean seeSysOrder =
        UserContext.hasPermission(GlobalConstant.VIEW_PURCHASE_ORDER_WRITE_OFF, ResourceType.API);
    if (!seeAllOrder && !seeSysOrder) {
      query.setCreatedUidList(ListUtil.of(UserContext.getUserId()));
    }
    if (!seeAllOrder && seeSysOrder) {
      query.setCreatedUidList(ListUtil.of(UserContext.getUserId(), 0L));
    }
    if (seeAllOrder && !seeSysOrder) {
      query.setNeCreatedUidList(ListUtil.of(0L));
    }
  }

  @Override
  public PageResponse<PurchaseOrderPageVO> page(PurchaseOrderPageQuery query) {
    setPermission(query);

    if (Objects.nonNull(query.getPaymentOrderStatus())) {
      List<Long> matchedRelatedNoByStatus =
          iPaymentApplyOrderService.getMatchedRelatedIdByStatus(
              ListUtil.of(query.getPaymentOrderStatus()), 0);
      if (CollUtil.isEmpty(matchedRelatedNoByStatus)) {
        return ResponseFactory.emptyPage();
      }
      query.setMatchedPaymentStatusIdList(matchedRelatedNoByStatus);
    }

    query.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(query.getBusinessLine()));

    PageInfo<PurchaseOrderPageVO> pageInfo =
        PageHelper.startPage(query.getPageIndex(), query.getPageSize())
            .doSelectPageInfo(() -> iPurchaseOrderService.page(query));

    if (pageInfo.getSize() > 0) {
      List<PurchaseOrderPageVO> list = pageInfo.getList();
      List<Long> idList =
          list.stream().map(PurchaseOrderPageVO::getId).collect(Collectors.toList());

      // 已入库状态的入库单的入库总数量
      List<StockInOrderDO> stockInOrderDOS = iStockInOrderService.receiptQuantity(idList);
      Map<Long, Integer> idQuantityMap =
          stockInOrderDOS.stream()
              .collect(
                  Collectors.toMap(
                      StockInOrderDO::getPurchaseOrderId,
                      StockInOrderDO::getReceiptQuantity,
                      (a, b) -> a));

      //  待入库+已入库数量
      List<StockInOrderDO> applyStockInDOS2 =
          iStockInOrderService.sumHadAndWillReceiptQuantity(idList);
      Map<Long, Integer> idQuantityMap2 =
          applyStockInDOS2.stream()
              .collect(
                  Collectors.toMap(
                      StockInOrderDO::getPurchaseOrderId,
                      StockInOrderDO::getReceiptQuantity,
                      (a, b) -> a));

      List<Long> partnerProviderIds =
          list.stream()
              .map(PurchaseOrderPageVO::getPartnerProviderId)
              .distinct()
              .collect(Collectors.toList());
      final Map<Long, PartnerProviderResp> longPartnerProviderRespMap =
          providerGateway.partnerBatchQueryByIds(partnerProviderIds);

      final ArrayList<CompletableFuture<Void>> completableFutures = new ArrayList<>();
      final Semaphore semaphore = new Semaphore(50);
      final ThreadPoolTaskExecutor threadPool = ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL);
      pageInfo
          .getList()
          .forEach(
              v -> {
                if (query.getIsDetailMode()) {
                  try {
                    if (semaphore.tryAcquire(30, TimeUnit.SECONDS)) {
                      final CompletableFuture<Void> future =
                          CompletableFuture.supplyAsync(() -> addDetailLine(v.getId()), threadPool)
                              .thenAccept(v::setLineList)
                              .thenRun(semaphore::release);
                      completableFutures.add(future);
                    }
                  } catch (Exception e) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.SYSTEM_BUSY);
                  }
                }
                // 已入库数量
                v.setStockQuantity(idQuantityMap.getOrDefault(v.getId(), 0));
                //                 待入库+已入库数量
                v.setHadAndWillStockQuantity(idQuantityMap2.getOrDefault(v.getId(), 0));
                if (v.getType() == 2 && StringUtils.isBlank(v.getBuyerUserName())) {
                  v.setBuyerUserName("系统");
                }
                final Optional<PartnerProviderResp> partnerProviderRespOptional =
                    Optional.ofNullable(longPartnerProviderRespMap.get(v.getPartnerProviderId()));
                v.setIsBlacklist(
                    partnerProviderRespOptional.map(PartnerProviderResp::getIsBlacklist).orElse(0));

                v.setCanStockChoose(completedQuantityVerification(v.getId()).getData());
              });
      if (!completableFutures.isEmpty()) {
        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();
      }
    }

    baseInfoService.setOpenDetailModel(DetailModelType.PURCHASE_ORDER, query.getIsDetailMode());

    return ResponseFactory.ofPage(pageInfo);
  }

  @Override
  public SingleResponse<Map<Integer, String>> payCondition() {
    Map<Integer, String> resultMap = new HashMap<>(OrderMap.PAY_MAP);
    // V4.1.2 款到发货 废弃
    resultMap.remove(1);
    return SingleResponse.of(resultMap);
  }

  /**
   * 添加明细模式需要展示的 明细行数据
   *
   * @param purchaseOrderId
   */
  private List<PurchaseDetailLineVO> addDetailLine(Long purchaseOrderId) {
    List<PurchaseDetailLineVO> resultList = new LinkedList<>();
    // 采购单详情列表
    List<PurchaseOrderDetail> detailList =
        iPurchaseOrderDetailService.getDetailList(purchaseOrderId);

    // 仓库详情
    Map<String, String> warehouseMap = new HashMap<>();
    List<WarehouseDO> warehouseDOS =
        iWarehouseService.queryWarehouseMapByNo(
            detailList.stream()
                .map(PurchaseOrderDetail::getWarehouseNo)
                .collect(Collectors.toList()));
    if (CollectionUtils.isNotEmpty(warehouseDOS)) {
      warehouseMap =
          warehouseDOS.stream().collect(Collectors.toMap(WarehouseDO::getNo, WarehouseDO::getName));
    }

    // 采购单和入库单关联，映射
    Map<PurchaseOrderDetail.KeyDto, List<StockInOrderDetail>> stockInOrderMap =
        purchaseOrderDetailStockInMap(purchaseOrderId);
    boolean isRelatedStockInOrder = MapUtils.isNotEmpty(stockInOrderMap);

    // 商品名称
    Map<Long, String> itemNameMap =
        itemGateway.getItemNameMap(
            detailList.stream().map(PurchaseOrderDetail::getItemId).collect(Collectors.toList()));
    List<String> combinationCodeList =
        detailList.stream()
            .map(PurchaseOrderDetail::getItemSkuCode)
            .filter(val -> val.startsWith("MU"))
            .collect(Collectors.toList());
    Map<String, String> combinationItemNameMap = new HashMap<>();
    if (CollectionUtils.isNotEmpty(combinationCodeList)) {
      List<CombinationNameDO> combinationNameDOS =
          iCombinationItemService.queryNameList(combinationCodeList);
      if (CollectionUtils.isNotEmpty(combinationNameDOS)) {
        combinationItemNameMap =
            combinationNameDOS.stream()
                .collect(Collectors.toMap(CombinationNameDO::getCode, CombinationNameDO::getName));
      }
    }

    final List<StockInOrderDetailApplyQuantity> applyQuantities =
        iStockInOrderDetailService.getApplyQuantities(purchaseOrderId);
    for (PurchaseOrderDetail orderDetail : detailList) {
      PurchaseOrderDetail.KeyDto keyDto =
          new PurchaseOrderDetail.KeyDto(
              orderDetail.getItemSkuCode(), orderDetail.getIsGift(), orderDetail.getTaxPrice());
      List<StockInOrderDetail> skuStockInOrderList = stockInOrderMap.get(keyDto);
      if (isRelatedStockInOrder && CollectionUtils.isNotEmpty(skuStockInOrderList)) {
        PurchaseDetailLineVO vo = new PurchaseDetailLineVO();
        vo.setSkuCode(orderDetail.getItemSkuCode());
        vo.setIsGift(orderDetail.getIsGift() == 1);
        String code = orderDetail.getItemSkuCode();
        if (code.startsWith("MU")) {
          vo.setItemName(combinationItemNameMap.getOrDefault(code, ""));
        } else {
          vo.setItemName(itemNameMap.getOrDefault(orderDetail.getItemId(), ""));
        }
        // 采购单数量
        vo.setPurchaseQuantity(orderDetail.getPurchaseQuantity());
        // 累计入库数量
        vo.setAddUpStockQuantity(
            skuStockInOrderList.stream()
                .mapToInt(StockInOrderDetail::getRealReceiptQuantity)
                .sum());
        // 申请入库数量：采购入库单申请入库的商品数量-(已取消和待提交)
        final Integer applyQuantity =
            applyQuantities.stream()
                .filter(
                    quantity ->
                        quantity.getItemSkuCode().equals(orderDetail.getItemSkuCode())
                            && quantity.getIsGift().equals(orderDetail.getIsGift()))
                .findFirst()
                .map(StockInOrderDetailApplyQuantity::getQuantity)
                .orElse(0);
        vo.setApplyStockQuantity(applyQuantity);
        // 剩余入库数量：采购单数量-累计入库数量
        vo.setRemainStockQuantity(vo.getPurchaseQuantity() - vo.getAddUpStockQuantity());
        vo.setWarehouseNo(orderDetail.getWarehouseNo());
        vo.setWarehouseName(warehouseMap.getOrDefault(orderDetail.getWarehouseNo(), ""));
        resultList.add(vo);
      } else {
        PurchaseDetailLineVO vo = new PurchaseDetailLineVO();
        vo.setSkuCode(orderDetail.getItemSkuCode());
        String code = orderDetail.getItemSkuCode();
        if (code.startsWith("MU")) {
          vo.setItemName(combinationItemNameMap.getOrDefault(code, ""));
        } else {
          vo.setItemName(itemNameMap.getOrDefault(orderDetail.getItemId(), ""));
        }
        vo.setPurchaseQuantity(orderDetail.getPurchaseQuantity());
        vo.setApplyStockQuantity(0);
        vo.setRemainStockQuantity(orderDetail.getPurchaseQuantity());
        vo.setAddUpStockQuantity(0);
        vo.setIsGift(orderDetail.getIsGift() == 1);
        vo.setWarehouseNo(orderDetail.getWarehouseNo());
        vo.setWarehouseName(warehouseMap.getOrDefault(orderDetail.getWarehouseNo(), ""));
        resultList.add(vo);
      }
    }
    return resultList;
  }

  /**
   * 采购单名字和入库的映射关系
   *
   * @param purchaseOrderId
   * @return
   */
  private Map<PurchaseOrderDetail.KeyDto, List<StockInOrderDetail>> purchaseOrderDetailStockInMap(
      Long purchaseOrderId) {
    // 采购单和入库单关联，映射
    List<StockInOrderDetail> stockInOrderDetailList =
        iStockInOrderDetailService.getListByPurchaseOrderId(purchaseOrderId);
    if (CollectionUtils.isNotEmpty(stockInOrderDetailList)) {
      return cutStockInDetailList(stockInOrderDetailList);
    }
    return new HashMap<>();
  }

  /**
   * 切割入库单 根据skuCode+isGift组成唯一
   *
   * @param stockInOrderDetails
   * @return
   */
  private Map<PurchaseOrderDetail.KeyDto, List<StockInOrderDetail>> cutStockInDetailList(
      List<StockInOrderDetail> stockInOrderDetails) {
    Map<PurchaseOrderDetail.KeyDto, List<StockInOrderDetail>> map = new LinkedHashMap<>();
    for (StockInOrderDetail stockInOrderDetail : stockInOrderDetails) {
      PurchaseOrderDetail.KeyDto idDto =
          new PurchaseOrderDetail.KeyDto(
              stockInOrderDetail.getItemSkuCode(),
              stockInOrderDetail.getIsGift(),
              stockInOrderDetail.getTaxPrice());
      List<StockInOrderDetail> orDefault = map.getOrDefault(idDto, new LinkedList<>());
      orDefault.add(stockInOrderDetail);
      map.put(idDto, orDefault);
    }
    return map;
  }

  @Override
  public PageResponse<ComposeSkuVO> pageSku(ComposeSkuPageQuery query) {
    query.setWithPrice(true);

    //        Long nowTime = System.currentTimeMillis() / 1000;
    //        composeSkuVOPageResponse.getData().forEach(vo -> {
    //            ComposeSkuWithPriceVO priceVO = (ComposeSkuWithPriceVO) vo;
    //            priceVO.setProcurement(itemPriceGateway.getCostPrice(vo.getSkuCode(), nowTime));
    //        });

    if (CollUtil.isNotEmpty(query.getBusinessLine()) && CollUtil.isEmpty(query.getCorpType())) {
      query.setCorpType(query.getBusinessLine());
    }

    return combinationItemBizService.pageSku(query);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public SingleResponse<Long> save(PurchaseOrderCmd cmd) {
    cmd.initialization();
    return SingleResponse.of(saveHandler(cmd, false));
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public SingleResponse<Long> sysSave(PurchaseOrderCmd cmd) {
    log.info(
        "purchaseOrderBizService save order providerId:{},businessLine:{},buyerId:{}",
        cmd.getProviderId(),
        cmd.getBusinessLine(),
        cmd.getBuyerUserId());
    // 新建系统采购单
    Long purchaseOrderId = saveHandler(cmd, true);

    // 系统生成的入库单自动更新为审核通过
    iPurchaseOrderService
        .lambdaUpdate()
        .set(PurchaseOrder::getState, PurchaseOrderState.WAIT_FINISH.getValue())
        .eq(PurchaseOrder::getId, purchaseOrderId)
        .update();

    return SingleResponse.of(purchaseOrderId);
  }

  public Long saveHandler(PurchaseOrderCmd cmd, Boolean isSys) {
    boolean isAdd = Objects.isNull(cmd.getId());
    PurchaseOrder newOrder;
    List<PurchaseOrderDetail> newDetailList;
    Long returnId;

    if (1 == cmd.getType()) {
      saveBuyer(cmd.getBuyerUserId(), cmd.getBuyerUserName());
    }
    if (2 == cmd.getType()) {
      if (!isSys) {
        Assert.state(UserPermissionJudge.isLegal(), "没有权限，只有财务人员和管理员才能操作工厂代发采购订单");
        saveBuyer(cmd.getBuyerUserId(), cmd.getBuyerUserName());
      }
    }

    try {
      newOrder = PurchaseOrderTransMapper.INSTANCE.cmdToDb(cmd);
      newDetailList =
          PurchaseOrderTransMapper.INSTANCE.detailCmdToDetailDbs(cmd.getDetailCmdList());

      if (isAdd) {
        returnId = addOrder(newOrder, newDetailList, isSys);
      } else {
        // 用户角色仅仅包含财务主管
        Boolean financialExecutive = UserPermissionJudge.isFinancialExecutive();
        if (financialExecutive) {
          Assert.state(
              iPurchaseOrderService.isOwnOrder(cmd.getId()), "当前用户角色只是财务主管。财务主管不允许编辑他人采购单");
        }
        verifyEditOperate(cmd.getId());
        updateOrder(cmd.getId(), newOrder, newDetailList);
        returnId = cmd.getId();
      }
    } catch (Exception e) {
      if (isAdd) {
        RedisUtil.decrement(PURCHASE_ORDER_SUFFIX, 1);
      }
      log.error("保存采购订单异常", e);
      throw e;
    }
    return returnId;
  }

  /**
   * 新建采购入库单（提交后），不允许修改采购单
   *
   * @param purchaseOrderId
   * @return
   */
  private void verifyEditOperate(Long purchaseOrderId) {
    List<String> stockInOrderNo =
        iPurchaseOrderService.getStockInOrderNoByState(
            purchaseOrderId,
            ListUtil.of(StockInState.WAIT_IN.getValue(), StockInState.HAVE_IN.getValue()));
    Assert.state(
        CollectionUtils.isEmpty(stockInOrderNo),
        "采购单能关联到已提交的入库单，" + StrUtil.join(",", stockInOrderNo) + "。不允许修改。");
  }

  private void saveBuyer(Long userId, String name) {
    buyerGateway.saveOrUpdateBuyer(userId, name);
  }

  private Long addOrder(
      PurchaseOrder newOrder, List<PurchaseOrderDetail> newDetailList, Boolean isSys) {
    newOrder.setState(PurchaseOrderState.WAIT_SUBMIT.getValue());
    //        newOrder.setStockInState(StockInState.WAIT_SUBMIT.getValue());
    iPurchaseOrderService.save(newOrder);

    Long purchaseId = newOrder.getId();
    newDetailList.forEach(v -> v.setPurchaseOrderId(purchaseId));
    iPurchaseOrderDetailService.saveBatch(newDetailList);

    String name = isSys ? "系统" : UserContext.getNickName();
    String operationLog = buildAddLog(name, newOrder);
    operateLogGateway.addOperatorLog(
        0L, OperateLogTarget.PURCHASE_ORDER, purchaseId, operationLog, "");
    return newOrder.getId();
  }

  private void updateOrder(
      Long purchaseId, PurchaseOrder newOrder, List<PurchaseOrderDetail> newDetailList) {
    PurchaseOrder oldOrder = iPurchaseOrderService.getById(purchaseId);
    Assert.notNull(oldOrder, "更新采购订单失败，id查询为空，id:" + purchaseId);
    List<PurchaseOrderDetail> oldDetailList = iPurchaseOrderDetailService.getDetailList(purchaseId);

    List<Long> deleteIdList = getNeedDeleteId(newDetailList, oldDetailList);
    if (CollectionUtils.isNotEmpty(deleteIdList)) {
      iPurchaseOrderDetailService.removeByIdsWithTime(deleteIdList);
    }

    boolean needReAudit = isNeedReAudit(newOrder, newDetailList, oldOrder, oldDetailList);
    orderStateHandler(newOrder, oldOrder, needReAudit);

    iPurchaseOrderService.updateById(newOrder);
    newDetailList.forEach(v -> v.setPurchaseOrderId(purchaseId));
    iPurchaseOrderDetailService.saveOrUpdateBatch(newDetailList);

    String operationLog =
        buildEditLog(newOrder, newDetailList, oldOrder, oldDetailList, needReAudit);
    if (StringUtils.isNotBlank(operationLog)) {
      operateLogGateway.addOperatorLog(
          UserContext.getUserId(),
          OperateLogTarget.PURCHASE_ORDER,
          purchaseId,
          UserContext.getNickName() + "，" + operationLog,
          "");
    }
  }

  /**
   * 获取需要删除的订单的id集合
   *
   * @param newDetailList
   * @param oldDetailList
   * @return
   */
  private List<Long> getNeedDeleteId(
      List<PurchaseOrderDetail> newDetailList, List<PurchaseOrderDetail> oldDetailList) {
    if (CollectionUtils.isEmpty(newDetailList)) {
      if (CollectionUtils.isNotEmpty(oldDetailList)) {
        return oldDetailList.stream().map(PurchaseOrderDetail::getId).collect(Collectors.toList());
      }
    }
    if (CollectionUtils.isEmpty(oldDetailList)) {
      return new LinkedList<>();
    }

    List<Long> oldIdList =
        oldDetailList.stream().map(PurchaseOrderDetail::getId).collect(Collectors.toList());
    List<Long> newIdList =
        newDetailList.stream().map(PurchaseOrderDetail::getId).collect(Collectors.toList());
    return oldIdList.stream().filter(v -> !newIdList.contains(v)).collect(Collectors.toList());
  }

  /**
   * 订单状态处理。
   *
   * @param newOrder
   * @param oldOrder
   * @param needReAudit
   */
  private void orderStateHandler(
      PurchaseOrder newOrder, PurchaseOrder oldOrder, Boolean needReAudit) {
    newOrder.setState(oldOrder.getState());
    // 判断是否需要重新审核
    //        if (needReAudit &&
    // !PurchaseOrderState.WAIT_SUBMIT.getValue().equals(oldOrder.getState())) {
    //            newOrder.setState(PurchaseOrderState.WAIT_CHECK.getValue());
    //        } else {
    //            newOrder.setState(oldOrder.getState());
    //        }
    // 关联最新一张下属入库单的状态
    Integer newestStockInState = iPurchaseOrderService.getNewestStockInState(oldOrder.getId());
    newOrder.setStockInState(newestStockInState);
  }

  /**
   * 将此编辑入参和之前的老数据对比，判断此次编辑操作是否需要重新审核 若编辑修改了相关字段，采购单则自动重新审核； 相关字段：采购组织、付款方式、采购部门、供应商、--
   * 采购数量、含税单价、税率、商品SKU、
   *
   * @param newDetailList
   * @param oldDetailList
   */
  public Boolean isNeedReAudit(
      PurchaseOrder newOrder,
      List<PurchaseOrderDetail> newDetailList,
      PurchaseOrder oldOrder,
      List<PurchaseOrderDetail> oldDetailList) {
    if (CollectionUtils.isEmpty(oldDetailList) && CollectionUtils.isEmpty(newDetailList)) {
      return false;
    }
    // 采购订单是否变化
    boolean isOrderChange =
        !newOrder.getOrganizationId().equals(oldOrder.getOrganizationId())
            || !newOrder.getPayType().equals(oldOrder.getPayType())
            || !newOrder.getDepartmentId().equals(oldOrder.getDepartmentId())
            || !newOrder.getProviderId().equals(oldOrder.getProviderId());
    if (isOrderChange) {
      return true;
    }
    // 采购订单明细发生 增删变动。
    List<Long> collect =
        oldDetailList.stream().map(PurchaseOrderDetail::getId).collect(Collectors.toList());
    List<Long> collect1 =
        newDetailList.stream().map(PurchaseOrderDetail::getId).collect(Collectors.toList());
    Diff diff = DiffUtil.diff(collect, collect1);
    if (!diff.getChanges().isEmpty()) {
      return true;
    }
    // 比较采购订单明细内容，是否发生变化。根据id一一对应
    for (PurchaseOrderDetail oldDetail : oldDetailList) {
      Long id = oldDetail.getId();
      List<PurchaseOrderDetail> newList =
          newDetailList.stream().filter(v -> v.getId().equals(id)).collect(Collectors.toList());
      if (CollectionUtils.isNotEmpty(newList)) {
        PurchaseOrderDetail newDetail = newList.get(0);
        Boolean changeForState = oldDetail.isChange(newDetail);
        if (changeForState) {
          return true;
        }
      }
    }
    return false;
  }

  @Override
  public SingleResponse<PurchaseOrderViewVO> view(Long purchaseId) {
    // 查询订单
    PurchaseOrderViewVO viewVO = iPurchaseOrderService.getView(purchaseId);
    Assert.notNull(viewVO, "采购订单id非法");

    Provider provider = providerGateway.getById(viewVO.getProviderId());
    Assert.notNull(provider, "供应商id非法，查询为空");
    viewVO.setProviderName(provider.getName());

    Map<Long, StaffInfo> longStaffInfoMap =
        userGateway.batchQueryStaffInfoByIds(
            ListUtil.of(viewVO.getCreatedUid(), viewVO.getBuyerUserId()));
    longStaffInfoMap.forEach(
        (userId, staffInfo) -> {
          if (viewVO.getCreatedUid().equals(userId)) {
            if (Objects.nonNull(staffInfo)) {
              viewVO.setCreatedName(staffInfo.getNickname());
            } else {
              viewVO.setCreatedName((viewVO.getType() == 2 ? "系统" : "创建者无名"));
            }
          }
          if (viewVO.getBuyerUserId().equals(userId)) {
            if (Objects.nonNull(staffInfo)) {
              viewVO.setBuyerUserName(staffInfo.getNickname());
            } else {
              viewVO.setBuyerUserName((viewVO.getType() == 2 ? "系统" : "创建者无名"));
            }
          }
        });

    viewVO.setDepartmentName(
        Objects.nonNull(viewVO.getDepartmentId())
            ? departmentGateway.name(viewVO.getDepartmentId())
            : "");

    // 查询关联关系
    List<Long> longs = iStockInOrderService.relatedIdList(purchaseId);
    viewVO.setRelatedStockInOrder(CollectionUtils.isNotEmpty(longs));
    viewVO.setRelatedInPayOrder(iPurchasePayableOrderService.related(longs, 1));
    List<Long> longs1 = iStockOutOrderService.relatedIdList(purchaseId);
    viewVO.setRelatedStockOutOrder(CollectionUtils.isNotEmpty(longs1));
    viewVO.setRelatedOutPayOrder(iPurchasePayableOrderService.related(longs1, 2));
    if (viewVO.getType() == 2) {
      //            viewVO.setRelatedOrder(orderService.related(purchaseId));
      viewVO.setRelatedOrder(false);
    } else {
      viewVO.setRelatedOrder(false);
    }

    viewVO.setTotalPurchaseTaxAmount(
        viewVO.getTotalPurchaseTaxAmount().setScale(2, RoundingMode.HALF_UP));
    viewVO.setTotalPurchaseAmount(
        viewVO.getTotalPurchaseAmount().setScale(2, RoundingMode.HALF_UP));
    viewVO.setTotalTaxAmount(viewVO.getTotalTaxAmount().setScale(2, RoundingMode.HALF_UP));

    final Optional<PartnerProviderResp> partnerProviderResp =
        providerGateway.partnerQueryById(provider.getPartnerProviderId());
    viewVO.setIsBlacklist(partnerProviderResp.map(PartnerProviderResp::getIsBlacklist).orElse(0));

    return SingleResponse.of(viewVO);
  }

  @Override
  public PageResponse<PurchaseOrderDetailVO> pageView(PurchaseOrderDetailPageQuery query) {
    LambdaQueryWrapper<PurchaseOrderDetail> wrapper = Wrappers.lambdaQuery();
    wrapper
        .eq(
            Objects.nonNull(query.getPurchaseOrderId()),
            PurchaseOrderDetail::getPurchaseOrderId,
            query.getPurchaseOrderId())
        .in(
            CollUtil.isNotEmpty(query.getPurchaseOrderIds()),
            PurchaseOrderDetail::getPurchaseOrderId,
            query.getPurchaseOrderIds())
        .orderByAsc(PurchaseOrderDetail::getSortId);

    IPage<PurchaseOrderDetail> reqPage = new Page<>(query.getPageIndex(), query.getPageSize());
    IPage<PurchaseOrderDetail> page = iPurchaseOrderDetailService.page(reqPage, wrapper);
    if (page.getTotal() == 0) {
      return PageResponse.of(
          new LinkedList<>(), (int) page.getTotal(), query.getPageSize(), query.getPageIndex());
    }

    Map<String, String> warehouseMap = new HashMap<>(10);
    List<String> noList =
        page.getRecords().stream()
            .map(PurchaseOrderDetail::getWarehouseNo)
            .collect(Collectors.toList());
    if (CollectionUtils.isNotEmpty(noList)) {
      List<WarehouseDO> warehouseDOList = iWarehouseService.queryWarehouseMapByNo(noList);
      warehouseMap =
          warehouseDOList.stream()
              .collect(Collectors.toMap(WarehouseDO::getNo, WarehouseDO::getName, (a, b) -> a));
    }

    // 单品商品名称
    List<Long> itemIdList =
        page.getRecords().stream().map(PurchaseOrderDetail::getItemId).collect(Collectors.toList());
    Map<Long, String> itemNameMap = itemGateway.getItemNameMap(itemIdList);
    // 组合商品名称
    List<String> combinationCodeList =
        page.getRecords().stream()
            .map(PurchaseOrderDetail::getItemSkuCode)
            .filter(val -> val.startsWith("MU"))
            .collect(Collectors.toList());
    Map<String, String> combinationItemNameMap = new HashMap<>();
    if (CollectionUtils.isNotEmpty(combinationCodeList)) {
      List<CombinationNameDO> combinationNameDOS =
          iCombinationItemService.queryNameList(combinationCodeList);
      if (CollectionUtils.isNotEmpty(combinationNameDOS)) {
        combinationItemNameMap =
            combinationNameDOS.stream()
                .collect(Collectors.toMap(CombinationNameDO::getCode, CombinationNameDO::getName));
      }
    }

    // 业务模式处理
    final Map<Long, List<CorpBizTypeDTO>> divisionMap =
        iBizLevelDivisionService.queryByItemId(itemIdList);

    // 查询采购单是否存在付款申请行为，如果存在付款申请行为，如果存在，标识出对应的明细ID。
    //        List<Long> appliedDetailIdList = new LinkedList<>();
    //        List<String> purchaseOrderNos = iPurchaseOrderService.lambdaQuery()
    //                .eq(Objects.nonNull(query.getPurchaseOrderId()), PurchaseOrder::getId,
    // query.getPurchaseOrderId())
    //                .in(CollUtil.isNotEmpty(query.getPurchaseOrderIds()), PurchaseOrder::getId,
    // query.getPurchaseOrderIds())
    //
    // .select(PurchaseOrder::getNo).list().stream().map(PurchaseOrder::getNo).collect(Collectors.toList());
    //        if (CollUtil.isNotEmpty(purchaseOrderNos)) {
    //            appliedDetailIdList =
    // iPaymentApplyOrderDetailService.getAppliedDetailIdList(purchaseOrderNos);
    //        }

    // 采购单明细和入库单的映射关系
    Map<PurchaseOrderDetail.KeyDto, List<StockInOrderDetail>> keyDtoListMap =
        purchaseOrderDetailStockInMap(query.getPurchaseOrderId());

    List<PurchaseOrderDetailVO> detailVOList =
        PurchaseOrderTransMapper.INSTANCE.detailToDetailVOs(page.getRecords());
    Set<String> skuCodes =
        detailVOList.stream()
            .map(PurchaseOrderDetailVO::getItemSkuCode)
            .collect(Collectors.toSet());
    Map<String, List<ItemSku>> itemSkuMap =
        iItemSkuService.lambdaQuery().in(ItemSku::getSkuCode, skuCodes).list().stream()
            .collect(Collectors.groupingBy(ItemSku::getSkuCode));
    for (PurchaseOrderDetailVO detailVO : detailVOList) {
      detailVO.setWarehouseName(warehouseMap.getOrDefault(detailVO.getWarehouseNo(), ""));
      String itemSkuCode = detailVO.getItemSkuCode();
      if (itemSkuCode.startsWith("MU")) {
        detailVO.setItemName(combinationItemNameMap.getOrDefault(itemSkuCode, ""));
      } else {
        detailVO.setItemName(itemNameMap.getOrDefault(detailVO.getItemId(), ""));
      }
      PurchaseOrderDetail.KeyDto keyDto =
          new PurchaseOrderDetail.KeyDto(
              detailVO.getItemSkuCode(), detailVO.getIsGift(), detailVO.getTaxPrice());
      List<StockInOrderDetail> stockInOrderDetailList = keyDtoListMap.get(keyDto);
      if (CollectionUtils.isNotEmpty(stockInOrderDetailList)) {
        detailVO.setRealStockInQuantity(
            stockInOrderDetailList.stream()
                .mapToInt(StockInOrderDetail::getRealReceiptQuantity)
                .sum());
      } else {
        detailVO.setRealStockInQuantity(0);
      }
      detailVO.setApplyStockQuantity(
          iStockInOrderDetailService.getApplyQuantity(
              query.getPurchaseOrderId(), detailVO.getItemSkuCode(), detailVO.getIsGift() == 1));

      // 插入skuId
      // ItemSku itemSku = itemSkuGateway.getBySkuCode(detailVO.getItemSkuCode());
      // detailVO.setItemSkuId(Objects.isNull(itemSku) ? 0L : itemSku.getId());
      // [20230906-七喜] 小改动，主要是在展示订单详情的时候，用sku绑定的单位来展示
      List<ItemSku> orDefault =
          itemSkuMap.getOrDefault(detailVO.getItemSkuCode(), new ArrayList<>());
      if (CollUtil.isNotEmpty(orDefault)) {
        detailVO.setUnit(orDefault.get(0).getUnit());
        detailVO.setItemSkuId(orDefault.get(0).getId());
      }

      detailVO.setCorpBizType(divisionMap.getOrDefault(detailVO.getItemId(), new LinkedList<>()));

      //            final List<BizLevelDivision> divisions = ;
      //            if (CollUtil.isEmpty(divisions)) {
      //                detailVO.setCorpBizType(new LinkedList<>());
      ////                detailVO.setBizType(new LinkedList<>());
      //            } else {
      //
      ////                final List<Integer> levelVal = divisions.stream()
      ////                        .filter(val ->
      // val.getLevel().equals(DivisionLevelEnum.BUSINESS_TYPE))
      ////                        .map(val ->
      // val.getLevelVal().getValue()).distinct().collect(Collectors.toList());
      ////                detailVO.setBizType(levelVal);
      //            }

      //            if (appliedDetailIdList.contains(detailVO.getId())) {
      //                detailVO.setIsPayApplied(1);
      //            } else {
      //                detailVO.setIsPayApplied(0);
      //            }

    }

    return PageResponse.of(
        detailVOList, (int) page.getTotal(), query.getPageSize(), query.getPageIndex());
  }

  @Override
  public PageResponse<PurchasePaymentDetailVo> paymentDetailPage(
      PurchasePaymentDetailPageQuery pageQuery) {
    PageInfo<PurchaseOrderDetail> pageInfo =
        PageHelper.startPage(pageQuery.getPageIndex(), pageQuery.getPageSize())
            .doSelectPageInfo(
                () -> {
                  LambdaQueryChainWrapper<PurchaseOrderDetail> wrapper =
                      iPurchaseOrderDetailService
                          .lambdaQuery()
                          .eq(
                              PurchaseOrderDetail::getPurchaseOrderId,
                              pageQuery.getPurchaseOrderId());
                  if (CollUtil.isNotEmpty(pageQuery.getDetailIds())) {
                    wrapper.in(PurchaseOrderDetail::getId, pageQuery.getDetailIds());
                  }
                  wrapper.list();
                });
    if (pageInfo.getList().isEmpty()) {
      return PageResponse.of(
          new LinkedList<>(),
          (int) pageInfo.getTotal(),
          pageQuery.getPageSize(),
          pageQuery.getPageIndex());
    }

    List<PurchaseOrderDetail> list = pageInfo.getList();
    List<Long> itemIdList =
        list.stream().map(PurchaseOrderDetail::getItemId).distinct().collect(Collectors.toList());
    Map<Long, String> itemNameMap =
        iItemService.lambdaQuery().in(Item::getId, itemIdList).list().stream()
            .collect(Collectors.toMap(Item::getId, Item::getName));

    final Map<Long, List<CorpBizTypeDTO>> divisionMap =
        iBizLevelDivisionService.queryByItemId(itemIdList);

    List<PurchasePaymentDetailVo> voList =
        list.stream()
            .map(
                val -> {
                  PurchasePaymentDetailVo vo = new PurchasePaymentDetailVo();
                  vo.setDetailId(val.getId());
                  vo.setSkuCode(val.getItemSkuCode());
                  vo.setSkuName(itemNameMap.getOrDefault(val.getItemId(), ""));
                  vo.setSpecifications(val.getSpecifications());
                  vo.setPrice(val.getTaxPrice().setScale(2, RoundingMode.HALF_UP));
                  vo.setQuantity(val.getPurchaseQuantity());
                  vo.setAmount(val.getTotalPriceTax());
                  vo.setCorpBizType(divisionMap.get(val.getItemId()));
                  return vo;
                })
            .collect(Collectors.toList());

    return PageResponse.of(
        voList, (int) pageInfo.getTotal(), pageQuery.getPageSize(), pageQuery.getPageIndex());
  }

  //    /**
  //     * 帮我计算出每个SKU的实际入库数量和单价。单价 = （入库的总价 - 出库的总价）/（实际的数量-采购的数量）
  //     *
  //     * @param stockInOrderMap,stockOutOrderMap
  //     * @return
  //     */
  //    public Map<String, RealDataDto> realSkuPaymentData(RealDataMap stockInOrderMap, RealDataMap
  // stockOutOrderMap) {
  //        Map<String, RealDataDto> resMap = new HashMap<>();
  //
  //        Map<String, Integer> stockInQuantityMap = stockInOrderMap.getQuantityMap();
  //        Map<String, Integer> stockOutQuantityMap = stockOutOrderMap.getQuantityMap();
  //        Map<String, BigDecimal> stockInAmountMap = stockInOrderMap.getAmountMap();
  //        Map<String, BigDecimal> stockOutAmountMap = stockOutOrderMap.getAmountMap();
  //
  //        stockInQuantityMap.forEach((key, value) -> {
  //            BigDecimal stockInAmount = stockInAmountMap.get(key);
  //            Integer stockOutQuantity = stockOutQuantityMap.get(key);
  //            BigDecimal stockOutAmount = stockOutAmountMap.get(key);
  //
  //            if ((Objects.nonNull(stockInAmount) &&
  //                    Objects.nonNull(stockOutQuantity) &&
  //                    Objects.nonNull(stockOutAmount))) {
  //                int realQuantity = value - stockOutQuantity;
  //                if (realQuantity != 0) {
  //                    BigDecimal price = stockInAmount.subtract(stockOutAmount)
  //                            .divide(new BigDecimal(realQuantity), 2, RoundingMode.HALF_UP);
  //                    resMap.put(key, new RealDataDto(realQuantity, price));
  //                }
  //            }
  //        });
  //
  //        return resMap;
  //    }

  @Data
  @AllArgsConstructor
  public static class RealDataDto {
    Integer quantity;
    BigDecimal price;
  }

  @Override
  public SingleResponse<Boolean> delete(Long purchaseId) {
    iPurchaseOrderService.removeByIdWithTime(purchaseId);
    return SingleResponse.of(true);
  }

  @Override
  public MultiResponse<OperateLog> logList(Long purchaseId) {
    List<OperateLog> operateLogs =
        operateLogGateway.getOperateLogs(OperateLogTarget.PURCHASE_ORDER, purchaseId);
    return MultiResponse.of(operateLogs);
  }

  DecimalFormat format = new DecimalFormat("0.00");

  @Override
  public SingleResponse<Boolean> export(PurchaseOrderPageQuery query) {
    ExportTask exportTask = ExportEntityPlus.save(ExportTaskType.PURCHASE_ORDER);
    setPermission(query);
    query.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(query.getBusinessLine()));
    exportDomainService.export(
        query,
        exportTask,
        ExportType.PURCHASE_ORDER_DETAIL,
        PurchaseOrderDetailSheet.class,
        (Function<PurchaseOrderDetailSheet, ExportSheet>)
            t -> {
              t.setDepartment(departmentGateway.name(t.getDepartmentId()));
              t.setTaxRate(NumberUtil.decimalFormat("#.##%", new BigDecimal(t.getTaxRate())));
              t.setTotalPriceTax(
                  NumberUtil.decimalFormat("0.00", new BigDecimal(t.getTotalPriceTax())));
              t.setTaxQuota(NumberUtil.decimalFormat("0.00", new BigDecimal(t.getTaxQuota())));
              t.setAfterTaxAmount(
                  NumberUtil.decimalFormat("0.00", new BigDecimal(t.getAfterTaxAmount())));

              final DivisionLevelValueEnum corpTypeEnum =
                  DivisionLevelValueEnum.valueOf(t.getBusinessLine());
              t.setCorpType(Objects.nonNull(corpTypeEnum) ? corpTypeEnum.getDesc() : "");

              final Map<Long, String> longStringMap =
                  iBizLevelDivisionService.queryBizTypeByItemId(ListUtil.of(t.getItemId()));
              final String orDefault = longStringMap.getOrDefault(t.getItemId(), "");
              t.setBizType(orDefault);

              return t;
            });
    return SingleResponse.of(true);
  }

  @Override
  public SingleResponse<Boolean> exportContact(Long id) {

    ExportTask task = ExportEntityPlus.save(ExportTaskType.PURCHASE_CONTACT);

    ThreadUtil.execute(
        PoolEnum.COMMON_POOL,
        () -> {
          final PurchaseOrderViewVO order = view(id).getData();
          PurchaseOrderDetailPageQuery query = new PurchaseOrderDetailPageQuery();
          query.setPurchaseOrderId(id);
          final List<PurchaseOrderDetailVO> orderDetailList = pageView(query).getData();
          String safeFileName = order.getNo() + "_contact_" + DateUtil.currentTime() + ".docx";
          java.io.File docFile = new java.io.File(safeFileName);
          try {
            writeInfoFile(order, orderDetailList, docFile, "/template/采购订单合同.docx");
            final String url = exportDomainService.uploadFile(docFile);
            task.setStatus(ExportTaskStatus.SUCCESS);
            task.setDownloadUrl(url);
          } catch (Exception e) {
            log.error("生成采购订单合同异常，purchaseOrderId:{}", id, e);
            operateLogGateway.addOperatorLog(
                0L, OperateLogTarget.PURCHASE_ORDER, id, StrUtil.format("生成采购订单合同文件异常"), null);
            FileUtil.del(docFile);
            task.setError(e.getMessage());
            task.setStatus(ExportTaskStatus.FAIL);
            task.setError(e.getMessage());
          } finally {
            iExportTaskService.updateById(task);
          }
        });

    return SingleResponse.of(true);
  }

  @Override
  public SingleResponse<Boolean> submit(Long purchaseId) {
    PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(purchaseId);
    Assert.notNull(purchaseOrder, "采购订单id非法，查询为空");
    // 采购明细列表不为空
    Assert.state(iPurchaseOrderDetailService.haveDetailList(purchaseId), "采购明细列表为空，不允许提交");

    // 必须是系统或者法务才能提交
    if (purchaseOrder.getType() == 2) {
      Assert.state(UserPermissionJudge.isLegal(), "没有权限，只有财务和管理员才能操作工厂代发采购订单");
    }

    // 审核撤回 或者 审核拒绝 状态重新提交流程
    //        if (PurchaseOrderState.REJECT_AUDIT.getValue().equals(purchaseOrder.getState()) ||
    //                PurchaseOrderState.WITHDRAW_AUDIT.getValue().equals(purchaseOrder.getState()))
    // {
    //            // String processId =
    // iAwsBusinessLogService.getProcessIdByBusinessId(PurchaseTypeEnum.PURCHASE_ORDER, purchaseId);
    //            String processId = purchaseOrder.getWorkbenchProcessId();
    //            Assert.notNull(processId, "流程实例查询异常");
    //
    //            //如果流程是处理被挂起的状态下，将其从挂起中恢复
    //            if (awsApprovalFlowService.processSuspendCheck(processId)) {
    //                awsApprovalFlowService.processResume(processId);
    //            }
    //
    //            //重新提交更新 BO 数据
    //            awsApprovalFlowService.boFieldUpdate(
    //                    "BO_DLAB_ERP_CGD_NEW",
    //                    processId,
    //                    "AMOUNT",
    //                    purchaseOrder.getTotalPurchaseTaxAmount());
    //
    //            //提交当前流程节点
    //            awsApprovalFlowService.submit(processId);
    //
    //            // 来不及等bpm系统的消息回调了，直接将状态改为待审核。
    //            purchaseOrder.setState(PurchaseOrderState.WAIT_CHECK.getValue());
    //            iPurchaseOrderService.updateById(purchaseOrder);
    //
    //            noticePurchaseOrderCurrentProcessors(purchaseOrder,
    // awsApprovalFlowService.taskQuery(processId));
    //
    //            return SingleResponse.of(true);
    //        }

    // 审核通过的采购单，不允许在提交
    if (PurchaseOrderState.PASS_AUDIT.getValue().equals(purchaseOrder.getState())) {
      throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "提采购单已审核通过，不允许提交");
    }
    // 审核关闭的采购单，不允许在提交
    if (PurchaseOrderState.CLOSED.getValue().equals(purchaseOrder.getState())) {
      throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "提采购单已关闭，不允许提交");
    }

    // 开启标准审核流程
    //        final Map<String, Map<String, Object>> boData =
    //                MapUtil.<String, Map<String, Object>>builder()
    //                        .put(
    //                                "BO_DLAB_ERP_CGD_NEW",
    //                                MapUtil.<String, Object>builder()
    //                                        .put(
    //                                                "AMOUNT",
    //                                                purchaseOrder.getTotalPurchaseTaxAmount())
    //                                        .build())
    //                        .build();
    //        final String processId = awsApprovalFlowService.createAndStartWithData(
    //                purchaseId,
    //                PurchaseTypeEnum.PURCHASE_ORDER,
    //                "采购订单审核流程",
    //                boData);

    SingleResponse<String> processIdRes =
        daddylabWorkbenchGateway.syncPurchaseOrder(purchaseId, UserContext.getUserId());
    if (!processIdRes.isSuccess()) {
      throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, processIdRes.getErrMessage());
    }
    String processId = processIdRes.getData();

    // 订单状态改为待审核
    iPurchaseOrderService
        .lambdaUpdate()
        .set(PurchaseOrder::getState, PurchaseOrderState.WAIT_CHECK.getValue())
        .eq(PurchaseOrder::getId, purchaseOrder.getId())
        .update();

    noticePurchaseOrderCurrentProcessors(
        purchaseOrder, awsApprovalFlowService.taskQuery(processId));

    operateLogGateway.addOperatorLog(
        UserContext.getUserId(), OperateLogTarget.PURCHASE_ORDER, purchaseId, "提交单据", null);

    return SingleResponse.of(true);
  }

  @Override
  public SingleResponse<Boolean> withdrawAudit(Long purchaseId) {
    PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(purchaseId);
    Assert.notNull(purchaseOrder, "采购单id非法，查询为空");
    if (purchaseOrder.getType() == 2) {
      Assert.state(UserPermissionJudge.isLegal(), "没有权限，只有财务人员或者管理员才能操作工厂代发采购订单");
    }

    final Long providerId = purchaseOrder.getProviderId();
    final Provider provider = providerGateway.getById(providerId);
    Assert.notNull(provider, "采购单关联供应商数据异常");

    String processId =
        iAwsBusinessLogService.getProcessIdByBusinessId(
            PurchaseTypeEnum.PURCHASE_ORDER, purchaseId);
    Assert.hasText(processId, "采购单id非法，或者状态非法，流程id查询为空");
    PurchaseOrderState oldState =
        IEnum.getEnumByValue(PurchaseOrderState.class, purchaseOrder.getState());

    final List<TaskInstance> taskInstances = awsApprovalFlowService.taskQuery(processId);
    awsApprovalFlowService.processRestart(processId);

    // 流程重启后挂起流程，避免用户手动在别的系统提交，造成状态不同步
    awsApprovalFlowService.processSuspend(processId);

    purchaseOrder.setState(PurchaseOrderState.WITHDRAW_AUDIT.getValue());
    iPurchaseOrderService.updateById(purchaseOrder);

    if (!taskInstances.isEmpty()) {
      sendNotice(
          taskInstances,
          String.format(
              "%s %s 采购单 %s 审核撤回",
              UserContext.getNickName(), provider.getName(), purchaseOrder.getNo()),
          "点击查看详情",
          refreshConfig.getDomain()
              + String.format(
                  "/commodity-purchase/purchase-order/review?id=%s", purchaseOrder.getId()));
    }

    StringBuilder log = new StringBuilder();
    log.append("修改订单状态，")
        .append(oldState.getDesc())
        .append("改为")
        .append(PurchaseOrderState.WITHDRAW_AUDIT.getDesc());
    if (StringUtils.isNotBlank(log)) {
      operateLogGateway.addOperatorLog(
          UserContext.getUserId(),
          OperateLogTarget.PURCHASE_ORDER,
          purchaseId,
          UserContext.getNickName() + "，" + log,
          "");
    }

    return SingleResponse.of(true);
  }

  private void sendNotice(
      List<TaskInstance> taskInstances, String title, String content, String link) {
    final List<String> currentProcessors =
        taskInstances.stream().map(TaskInstance::getTarget).collect(Collectors.toList());
    final List<DadStaffVO> currentProcessorStaffVOs =
        staffService.getStaffListByLoginName(currentProcessors);

    final String recipients =
        currentProcessorStaffVOs.stream()
            .map(DadStaffVO::getQwUserId)
            .collect(Collectors.joining(","));
    final WechatMsg wechatMsg = new WechatMsg();
    wechatMsg.setTitle(title);
    wechatMsg.setContent(content);
    wechatMsg.setLink(link);
    wechatMsg.setRecipient(recipients);
    wechatMsg.setType(1);
    msgSender.send(wechatMsg);
  }

  @Override
  public void noticePurchaseOrderCurrentProcessors(
      PurchaseOrder purchaseOrder, List<TaskInstance> taskInstances) {
    //        return;

    final Long createdUid = purchaseOrder.getCreatedUid();
    final Optional<Provider> providerOptional =
        Optional.ofNullable(providerGateway.getById(purchaseOrder.getProviderId()));

    final DadStaffVO createdUser = staffService.getStaff(createdUid);
    final List<String> currentProcessors =
        taskInstances.stream()
            .map(TaskInstance::getTarget)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());
    if (currentProcessors.isEmpty()) {
      log.warn("【采购单｜流程通知】通知异常，当前事项未分配执行人 {}", purchaseOrder.getNo());
      return;
    }
    // 如果是电商和商家入驻审核完成，通知大肖
    if (purchaseOrder.getBusinessLine().equals(0) || purchaseOrder.getBusinessLine().equals(3)) {
      currentProcessors.add("you.xiao");
    }

    final List<DadStaffVO> currentProcessorStaffVOs =
        staffService.getStaffListByLoginName(currentProcessors);

    final String recipients =
        currentProcessorStaffVOs.stream()
            .map(DadStaffVO::getQwUserId)
            .collect(Collectors.joining(","));
    final WechatMsg wechatMsg = new WechatMsg();
    wechatMsg.setTitle(
        String.format(
            "%s %s 采购单 %s 提交审核",
            createdUser.getNickname(),
            providerOptional.map(Provider::getName).orElse("?"),
            purchaseOrder.getNo()));
    wechatMsg.setContent("您有一个审核任务，请及时查看");
    final String link =
        refreshConfig.getDomain()
            + String.format(
                "/commodity-purchase/purchase-order/review?id=%s", purchaseOrder.getId());
    wechatMsg.setLink(link);
    wechatMsg.setRecipient(recipients);
    wechatMsg.setType(1);

    IWechatMsgService iWechatMsgService = SpringUtil.getBean(IWechatMsgService.class);
    iWechatMsgService.save(wechatMsg);

    msgSender.send(wechatMsg);
  }

  @Override
  public void toDoSummaryReminder(String qwUserId, List<Long> purchaseOrderIds) {
    final WechatMsg wechatMsg = new WechatMsg();
    wechatMsg.setTitle(String.format("【评测ERP】中共 %s 个采购订单待审核处理！", purchaseOrderIds.size()));
    wechatMsg.setContent("您有一个新的待办，请尽快处理");
    final String link =
        refreshConfig.getDomain()
            + String.format(
                "/commodity-purchase/purchase-order/list?ids=%s",
                purchaseOrderIds.stream().map(Object::toString).collect(Collectors.joining(",")));
    wechatMsg.setLink(link);
    wechatMsg.setRecipient(qwUserId);
    wechatMsg.setType(1);
    msgSender.send(wechatMsg);
  }

  @Override
  public SingleResponse<Boolean> urgent(Long purchaseId) {
    final PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(purchaseId);
    Assert.notNull(purchaseOrder, "采购单数据异常");

    String processId =
        iAwsBusinessLogService.getProcessIdByBusinessId(
            PurchaseTypeEnum.PURCHASE_ORDER, purchaseId);
    Assert.notNull(processId, "采购单流程数据异常");

    final Provider provider = providerGateway.getById(purchaseOrder.getProviderId());
    Assert.notNull(provider, "采购单供应商数据异常");

    final List<TaskInstance> taskInstances = awsApprovalFlowService.taskQuery(processId);
    final String link =
        refreshConfig.getDomain()
            + String.format(
                "/commodity-purchase/purchase-order/review?id=%s", purchaseOrder.getId());
    sendNotice(
        taskInstances,
        String.format(
            "%s 提醒您处理采购单：%s %s，请查看后处理",
            UserContext.getNickName(), provider.getName(), purchaseOrder.getNo()),
        "点击查看详情",
        link);
    return SingleResponse.of(true);
  }

  @Override
  public void updateState(Long purchaseOrderId, Integer purchaseStatus) {
    PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(purchaseOrderId);
    Assert.notNull(purchaseOrder, "id非法，采购订单查询为空");
    PurchaseOrderState oldState =
        IEnum.getEnumByValue(PurchaseOrderState.class, purchaseOrder.getState());
    PurchaseOrderState newState = IEnum.getEnumByValue(PurchaseOrderState.class, purchaseStatus);

    if (purchaseOrder.getState().equals(purchaseStatus)) {
      return;
    }

    // 标准采购的订单
    if (purchaseOrder.getType() == 1) {
      if (PurchaseOrderState.FINISHED.getValue().equals(purchaseStatus)) {
        Assert.state(
            Objects.equals(purchaseOrder.getState(), PurchaseOrderState.WAIT_FINISH.getValue())
                || Objects.equals(
                    purchaseOrder.getState(), PurchaseOrderState.PASS_AUDIT.getValue()),
            "当前订单必须是待完结状态或者审核通过状态");
        // 采购实际入库数量 <= 采购申请数量
        List<StockInOrderDetail> listByPurchaseOrderId =
            iStockInOrderDetailService.getListByPurchaseOrderId(purchaseOrderId);
        int totalRealReceiptQuantity =
            listByPurchaseOrderId.stream()
                .mapToInt(StockInOrderDetail::getRealReceiptQuantity)
                .sum();
        Assert.state(
            totalRealReceiptQuantity <= purchaseOrder.getTotalPurchaseQuantity(),
            "无法完结。完结条件是采购实际入库数量必须小于等于采购申请数量");
        // 入库单都已经入库
        long nonWarehousingCount = iStockInOrderService.noeWarehousingCount(purchaseOrderId);
        Assert.state(nonWarehousingCount == 0, "无法完结。此采购单下尚存在待提交或者待入库的入库单");
        //                String processIdByBusinessId =
        // iAwsBusinessLogService.getProcessIdByBusinessId(PurchaseTypeEnum.PURCHASE_ORDER,
        // purchaseOrderId);
        //                awsApprovalFlowService.processTerminate(processIdByBusinessId);
      }

      // 如果此采购订单已经发起了OA盖章流程，关闭订单的同时，终止OA流程。
      boolean b1 = purchaseStatus.equals(PurchaseOrderState.CLOSED.getValue());
      boolean b2 = StrUtil.isNotBlank(purchaseOrder.getOaSealProcessId());
      if (b1 && b2) {
        Map<String, Object> reqMap = new HashMap<>(2);
        reqMap.put("affairId", purchaseOrder.getOaSealProcessId());
        reqMap.put("member", "ban.dai");
        final String bpmToken = bpmClient.getUserOaToken("ban.dai");
        final Boolean res = bpmFeignClient.stop(JsonUtil.toJson(reqMap), bpmToken);

        operateLogGateway.addOperatorLog(
            UserContext.getUserId(),
            OperateLogTarget.PURCHASE_ORDER,
            purchaseOrderId,
            "发起终止OA盖章流程请求，响应：" + res,
            "");
      }
    }

    purchaseOrder.setState(purchaseStatus);
    iPurchaseOrderService.updateById(purchaseOrder);

    operateLogGateway.addOperatorLog(
        UserContext.getUserId(),
        OperateLogTarget.PURCHASE_ORDER,
        purchaseOrderId,
        "订单状态，" + oldState.getDesc() + "改为" + newState.getDesc(),
        "");
  }

  // --------------------------------------------------------------------------------------------------------------

  private static final String GIFT_PROPERTY = "是否是赠品";

  public String buildAddLog(String userName, PurchaseOrder purchaseOrder) {
    return "【" + userName + "】新增采购单，单号：" + purchaseOrder.getNo() + "。";
  }

  private String buildEditOrderLog(PurchaseOrder newOrder, PurchaseOrder oldOrder) {
    // 比较订单
    String orderLog = "";
    Diff diff = DiffUtil.diff(oldOrder, newOrder);
    if (diff.hasChanges()) {
      StringBuilder s1 = new StringBuilder();
      diff.groupByObject()
          .forEach(
              it -> {
                final List<PropertyChange> propertyChanges = it.getPropertyChanges();
                for (PropertyChange propertyChange : propertyChanges) {
                  ValueChange change = (ValueChange) propertyChange;
                  switch (change.getPropertyName()) {
                    case "付款方式":
                      {
                        String left = OrderMap.PAY_MAP.get(change.getLeft());
                        String right = OrderMap.PAY_MAP.get(change.getRight());
                        s1.append("【")
                            .append(change.getPropertyName())
                            .append("】")
                            .append("从")
                            .append("\"")
                            .append(left)
                            .append("\"")
                            .append("改为")
                            .append("\"")
                            .append(right)
                            .append("\"")
                            .append(";");
                        break;
                      }
                    case "到货方式":
                      {
                        //                        到货方式。1一次性到货。2分批到货
                        String left = "1".equals(change.getLeft().toString()) ? "一次性到货" : "分批到货";
                        String right = "1".equals(change.getRight().toString()) ? "一次性到货" : "分批到货";
                        s1.append("【")
                            .append(change.getPropertyName())
                            .append("】")
                            .append("从")
                            .append("\"")
                            .append(left)
                            .append("\"")
                            .append("改为")
                            .append("\"")
                            .append(right)
                            .append("\"")
                            .append(";");
                        break;
                      }
                    case "采购状态":
                      PurchaseOrderState oldState =
                          IEnum.getEnumByValue(PurchaseOrderState.class, change.getLeft());
                      PurchaseOrderState newState =
                          IEnum.getEnumByValue(PurchaseOrderState.class, change.getRight());
                      s1.append("【")
                          .append(change.getPropertyName())
                          .append("】")
                          .append("从")
                          .append("\"")
                          .append(oldState.getDesc())
                          .append("\"")
                          .append("改为")
                          .append("\"")
                          .append(newState.getDesc())
                          .append("\"")
                          .append(";");
                      break;
                    case "供应商":
                      Provider oldVal =
                          iProviderService.getById(Long.valueOf(change.getLeft().toString()));
                      Provider newVal =
                          iProviderService.getById(Long.valueOf(change.getRight().toString()));
                      s1.append("【")
                          .append(change.getPropertyName())
                          .append("】")
                          .append("从")
                          .append("\"")
                          .append(oldVal.getName())
                          .append("\"")
                          .append("改为")
                          .append("\"")
                          .append(newVal.getName())
                          .append("\"")
                          .append(";");
                      break;
                    case "采购日期":
                      String oldDate =
                          DateUtil.format(Long.valueOf(change.getLeft().toString()), "yyyy-MM-dd");
                      String newDate =
                          DateUtil.format(Long.valueOf(change.getRight().toString()), "yyyy-MM-dd");
                      s1.append("【")
                          .append(change.getPropertyName())
                          .append("】")
                          .append("从")
                          .append("\"")
                          .append(oldDate)
                          .append("\"")
                          .append("改为")
                          .append("\"")
                          .append(newDate)
                          .append("\"")
                          .append(";");
                      break;
                    case "采购员":
                      StaffInfo oldBuyer =
                          userGateway.queryStaffInfoById(Long.valueOf(change.getLeft().toString()));
                      StaffInfo newBuyer =
                          userGateway.queryStaffInfoById(
                              Long.valueOf(change.getRight().toString()));
                      s1.append("【")
                          .append(change.getPropertyName())
                          .append("】")
                          .append("从")
                          .append("\"")
                          .append(oldBuyer.getNickname())
                          .append("\"")
                          .append("改为")
                          .append("\"")
                          .append(newBuyer.getNickname())
                          .append("\"")
                          .append(";");
                      break;
                    case "采购部门":
                      String oldDepartment =
                          departmentGateway.name(Long.valueOf(change.getLeft().toString()));
                      String newDepartment =
                          departmentGateway.name(Long.valueOf(change.getRight().toString()));
                      s1.append("【")
                          .append(change.getPropertyName())
                          .append("】")
                          .append("从")
                          .append("\"")
                          .append(oldDepartment)
                          .append("\"")
                          .append("改为")
                          .append("\"")
                          .append(newDepartment)
                          .append("\"")
                          .append(";");
                      break;
                    case "采购组":
                      Optional<ErpGroup> oldGroup =
                          erpGroupGateway.id(Long.valueOf(change.getLeft().toString()));
                      Optional<ErpGroup> newGroup =
                          erpGroupGateway.id(Long.valueOf(change.getRight().toString()));
                      if (oldGroup.isPresent() && newGroup.isPresent()) {
                        s1.append("【")
                            .append(change.getPropertyName())
                            .append("】")
                            .append("从")
                            .append("\"")
                            .append(oldGroup.get().getName())
                            .append("\"")
                            .append("改为")
                            .append("\"")
                            .append(newGroup.get().getName())
                            .append("\"")
                            .append(";");
                        break;
                      }
                    default:
                      s1.append("【")
                          .append(change.getPropertyName())
                          .append("】")
                          .append("从")
                          .append("\"")
                          .append(change.getLeft())
                          .append("\"")
                          .append("改为")
                          .append("\"")
                          .append(change.getRight())
                          .append("\"")
                          .append(";");
                      break;
                  }
                }
              });
      orderLog = "订单编辑，" + s1.substring(0, s1.length() - 1) + "。";
    }
    return orderLog;
  }

  private String buildEditOrderDetailLog(
      List<PurchaseOrderDetail> newDetailList,
      List<PurchaseOrderDetail> oldDetailList,
      Boolean isNeedReAudit) {
    // 比较采购明细
    DiffUtil.ObjListDiff objListDiff =
        DiffUtil.getInstance().diffObjList(oldDetailList, newDetailList, PurchaseOrderDetail.class);

    StringBuilder s2 = new StringBuilder();
    String addSkuLog = "";
    HashSet<Object> addIdSet = objListDiff.getAddIdSet();
    if (CollectionUtils.isNotEmpty(addIdSet)) {
      s2.append("新增sku：");
      addIdSet.forEach(it -> s2.append(it.toString()).append("，"));
      addSkuLog = s2.substring(0, s2.length() - 1) + ";";
    }

    StringBuilder s3 = new StringBuilder();
    String removeSkuLog = "";
    HashSet<Object> removeIdSet = objListDiff.getRemoveIdSet();
    if (CollectionUtils.isNotEmpty(removeIdSet)) {
      s3.append("删除sku：");
      removeIdSet.forEach(it -> s3.append(it.toString()).append("，"));
      removeSkuLog = s3.substring(0, s3.length() - 1) + ";";
    }

    StringBuilder s4 = new StringBuilder();
    objListDiff
        .getChangeMap()
        .forEach(
            (o, changePropertyObjList) -> {
              s4.append("编辑sku：").append(o.toString()).append(";");
              StringBuilder ss4 = new StringBuilder();
              changePropertyObjList.forEach(
                  it -> {
                    if (GIFT_PROPERTY.equals(it.getProperty())) {
                      String oldV = "0".equals(it.getOldVal().toString()) ? "否" : "是";
                      String newV = "0".equals(it.getNewVal().toString()) ? "否" : "是";
                      ss4.append("【是否是赠品】从").append(oldV).append("改为").append(newV).append(";");
                    } else if ("税率".equals(it.getProperty())) {
                      ss4.append("【税率】从")
                          .append(
                              NumberUtil.decimalFormat(
                                  "#.##%", new BigDecimal(it.getOldVal().toString())))
                          .append("改为")
                          .append(
                              NumberUtil.decimalFormat(
                                  "#.##%", new BigDecimal(it.getNewVal().toString())))
                          .append(";");
                    } else if ("仓库编号".equals(it.getProperty())) {
                      ss4.append("【仓库】从");
                      warehouseGateway
                          .getWarehouse(it.getOldVal().toString())
                          .ifPresent(warehouse -> ss4.append(warehouse.getName()));
                      ss4.append("改为");
                      warehouseGateway
                          .getWarehouse(it.getNewVal().toString())
                          .ifPresent(warehouse -> ss4.append(warehouse.getName()));
                      ss4.append(";");
                    } else if ("含税单价".equals(it.getProperty())) {
                      ss4.append("【含税单价】从")
                          .append(
                              NumberUtil.decimalFormat(
                                  "#.##", new BigDecimal(it.getOldVal().toString())))
                          .append("改为")
                          .append(
                              NumberUtil.decimalFormat(
                                  "#.##", new BigDecimal(it.getNewVal().toString())))
                          .append(";");
                    } else {
                      ss4.append("【")
                          .append(it.getProperty())
                          .append("】从")
                          .append("\"")
                          .append(it.getOldVal())
                          .append("\"")
                          .append("改为")
                          .append("\"")
                          .append(it.getNewVal())
                          .append("\"")
                          .append(";");
                    }
                  });
              s4.append(ss4.substring(0, ss4.length() - 1)).append(";");
            });
    String changeSkuLog = s4.toString();
    if (isNeedReAudit) {
      changeSkuLog = changeSkuLog + "需要重新提交审核;";
    }

    return addSkuLog + removeSkuLog + changeSkuLog;
  }

  public String buildEditLog(
      PurchaseOrder newOrder,
      List<PurchaseOrderDetail> newDetailList,
      PurchaseOrder oldOrder,
      List<PurchaseOrderDetail> oldDetailList,
      Boolean isNeedReAudit) {
    String s = buildEditOrderLog(newOrder, oldOrder);
    String s1 = buildEditOrderDetailLog(newDetailList, oldDetailList, isNeedReAudit);
    return s + s1;
  }

  @Override
  public SingleResponse<PaymentDetailAmountVO> getRightPayAmount(
      Long id, List<Long> choosedDetailIdList, Boolean advancePayment) {
    return getRightPayAmountPlus(id, choosedDetailIdList, advancePayment);
  }

  @Override
  public SingleResponse<PaymentDetailAmountVO> getRightPayAmount(
      Long id, List<Long> choosedDetailIdList) {
    final PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(id);
    boolean advancePayment = OrderMap.PAY_PROPORTION.containsKey(purchaseOrder.getPayType());
    return getRightPayAmountPlus(id, choosedDetailIdList, advancePayment);
  }

  @Deprecated
  public SingleResponse<PaymentDetailAmountVO> getRightPayAmount0(
      Long id, List<Long> choosedDetailIdList) {
    // 采购单总额。
    BigDecimal orderAmount =
        iPurchaseOrderDetailService
            .lambdaQuery()
            .eq(PurchaseOrderDetail::getPurchaseOrderId, id)
            .in(
                CollUtil.isNotEmpty(choosedDetailIdList),
                PurchaseOrderDetail::getId,
                choosedDetailIdList)
            .select(PurchaseOrderDetail::getTotalPriceTax)
            .list()
            .stream()
            .map(PurchaseOrderDetail::getTotalPriceTax)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    BigDecimal totalStockInAmount;
    List<StockInOrder> stockInOrderList =
        iStockInOrderService
            .lambdaQuery()
            .eq(StockInOrder::getPurchaseOrderId, id)
            // 已入库
            .in(StockInOrder::getState, ListUtil.of(3))
            .list();
    List<StockInOrderDetail> stockInOrderDetailList = new ArrayList<>();
    if (CollUtil.isEmpty(stockInOrderList)) {
      totalStockInAmount = BigDecimal.ZERO;
    } else {
      List<Long> stockInOrderIdList =
          stockInOrderList.stream().map(StockInOrder::getId).collect(Collectors.toList());
      stockInOrderDetailList =
          iStockInOrderDetailService
              .lambdaQuery()
              .in(StockInOrderDetail::getStockInOrderId, stockInOrderIdList)
              .list();
      totalStockInAmount =
          stockInOrderDetailList.stream()
              .map(StockInOrderDetail::getTotalPriceTax)
              .reduce(BigDecimal::add)
              .orElse(BigDecimal.ZERO);
    }

    // 此采购订单出库单总金额
    BigDecimal totalStockOutAmount = BigDecimal.ZERO;
    List<StockOutOrder> stockOutOrderList =
        iStockOutOrderService
            .lambdaQuery()
            .eq(StockOutOrder::getPurchaseOrderId, id)
            // 已出库
            .in(StockOutOrder::getState, ListUtil.of(4))
            .list();
    List<StockOutOrderDetail> stockOutOrderDetailList = new LinkedList<>();
    if (CollUtil.isNotEmpty(stockOutOrderList)) {
      List<Long> stockOutOrderIdList =
          stockOutOrderList.stream().map(StockOutOrder::getId).collect(Collectors.toList());
      stockOutOrderDetailList =
          iStockOutOrderDetailService
              .lambdaQuery()
              .in(StockOutOrderDetail::getStockOutOrderId, stockOutOrderIdList)
              .list();
      if (CollUtil.isNotEmpty(stockOutOrderDetailList)) {
        totalStockOutAmount =
            stockOutOrderDetailList.stream()
                .map(StockOutOrderDetail::getTotalPriceTax)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
      }
    }

    // 此采购单已申请的付款单金额
    BigDecimal paymentAmount = BigDecimal.ZERO;
    PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(id);
    if (Objects.nonNull(purchaseOrder)) {
      String no = purchaseOrder.getNo();
      paymentAmount = iPaymentApplyOrderService.getAppliedPaymentAmountByRelatedNo(ListUtil.of(no));
    }

    // 这笔订单剩余可付款金额。订单总额-已申请付款金额
    BigDecimal remainCanPayAmount = orderAmount.subtract(paymentAmount);
    if (remainCanPayAmount.compareTo(BigDecimal.ZERO) <= 0) {
      String tip =
          StrUtil.format(
              "此采购单可申请付款金额总计:{},已申请付款金额:{},剩余可付款金额:{},剩余可付款金额必须大于0",
              orderAmount.setScale(2, RoundingMode.DOWN),
              paymentAmount.setScale(2, RoundingMode.DOWN),
              remainCanPayAmount.setScale(2, RoundingMode.DOWN));
      return com.alibaba.cola.dto.SingleResponse.buildFailure(
          ErrorCode.BUSINESS_OPERATE_ERROR.getCode(), tip);
    }

    // 这订单单应付金额。已入库总金额-已出库总金额-已申请付款金额
    BigDecimal rightAmount =
        totalStockInAmount.subtract(totalStockOutAmount).subtract(paymentAmount);
    // 针对部分SKU申请付款的情况，更新应付金额
    if (CollUtil.isNotEmpty(choosedDetailIdList)) {
      List<PurchaseOrderDetail> chooseDetailList =
          iPurchaseOrderDetailService
              .lambdaQuery()
              .in(PurchaseOrderDetail::getId, choosedDetailIdList)
              .list();
      Assert.isTrue(CollUtil.isNotEmpty(chooseDetailList), "部分SKU申请付款的情况下，明细ID非法，明细列表为空");
      List<String> choosedKeyList =
          chooseDetailList.stream()
              .map(val -> val.getItemSkuCode() + "_" + val.getTaxPrice())
              .collect(Collectors.toList());

      List<StockInOrderDetail> chooseStockInOrderDetail =
          stockInOrderDetailList.stream()
              .filter(
                  stockInOrderDetail -> {
                    String ss =
                        stockInOrderDetail.getItemSkuCode()
                            + "_"
                            + stockInOrderDetail.getTaxPrice();
                    return choosedKeyList.contains(ss);
                  })
              .collect(Collectors.toList());

      BigDecimal chooseStockInAmount =
          chooseStockInOrderDetail.stream()
              .map(StockInOrderDetail::getTotalPriceTax)
              .reduce(BigDecimal::add)
              .orElse(BigDecimal.ZERO);

      BigDecimal chooseStockOutAmount = BigDecimal.ZERO;
      if (CollUtil.isNotEmpty(stockOutOrderDetailList)) {
        List<StockOutOrderDetail> chooseStockOutOrderDetail =
            stockOutOrderDetailList.stream()
                .filter(
                    stockOutOrderDetail -> {
                      String ss =
                          stockOutOrderDetail.getItemSkuCode()
                              + "_"
                              + stockOutOrderDetail.getTaxPrice();
                      return choosedKeyList.contains(ss);
                    })
                .collect(Collectors.toList());
        chooseStockOutAmount =
            chooseStockOutOrderDetail.stream()
                .map(StockOutOrderDetail::getTotalPriceTax)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
      }

      rightAmount = chooseStockInAmount.subtract(chooseStockOutAmount).subtract(paymentAmount);
      orderAmount =
          chooseDetailList.stream()
              .map(PurchaseOrderDetail::getTotalPriceTax)
              .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    PaymentDetailAmountVO vv = new PaymentDetailAmountVO();
    vv.setRightAmount(rightAmount.setScale(2, RoundingMode.DOWN));
    vv.setOrderAmount(orderAmount.setScale(2, RoundingMode.DOWN));
    return SingleResponse.of(vv);
  }

  @Data
  public static class SkuStockDto {
    /** SKU编码 */
    private String skuCode;

    /** 实际出/入库数量 */
    private Integer quantity;

    /** 此SKU价税合计 */
    private BigDecimal totalAmount;
  }

  @Override
  public Map<String, SkuStockDto> skuRealStockInMap(Long purchaseOrderId) {
    Map<String, SkuStockDto> map = new HashMap<>();

    List<Long> stockInOrderIds =
        iStockInOrderService
            .lambdaQuery()
            .eq(StockInOrder::getPurchaseOrderId, purchaseOrderId)
            .eq(StockInOrder::getState, 3)
            .list()
            .stream()
            .map(StockInOrder::getId)
            .collect(Collectors.toList());

    if (CollUtil.isNotEmpty(stockInOrderIds)) {
      map =
          iStockInOrderDetailService
              .lambdaQuery()
              .in(StockInOrderDetail::getStockInOrderId, stockInOrderIds)
              .list()
              .stream()
              .map(
                  val -> {
                    SkuStockDto skuStockDto = new SkuStockDto();
                    skuStockDto.setSkuCode(val.getItemSkuCode());
                    skuStockDto.setQuantity(
                        Objects.isNull(val.getRealReceiptQuantity())
                            ? 0
                            : val.getRealReceiptQuantity());
                    skuStockDto.setTotalAmount(val.getTotalPriceTax());
                    return skuStockDto;
                  })
              .collect(
                  Collectors.toMap(
                      SkuStockDto::getSkuCode,
                      a -> a,
                      (o1, o2) -> {
                        o1.setQuantity(o1.getQuantity() + o2.getQuantity());
                        o1.setTotalAmount(o1.getTotalAmount().add(o2.getTotalAmount()));
                        return o1;
                      }));
    }

    return map;
  }

  @Override
  public Map<String, SkuStockDto> skuRealStockOutMap(Long purchaseOrderId) {
    Map<String, SkuStockDto> map = new HashMap<>();

    List<Long> stockOutOrderIds =
        iStockOutOrderService
            .lambdaQuery()
            .in(StockOutOrder::getPurchaseOrderId, purchaseOrderId)
            .eq(StockOutOrder::getState, 4)
            .select(StockOutOrder::getId)
            .list()
            .stream()
            .map(StockOutOrder::getId)
            .collect(Collectors.toList());

    if (CollUtil.isNotEmpty(stockOutOrderIds)) {
      map =
          iStockOutOrderDetailService
              .lambdaQuery()
              .in(StockOutOrderDetail::getStockOutOrderId, stockOutOrderIds)
              .list()
              .stream()
              .map(
                  val -> {
                    SkuStockDto skuStockDto = new SkuStockDto();
                    skuStockDto.setSkuCode(val.getItemSkuCode());
                    skuStockDto.setQuantity(
                        Objects.isNull(val.getRealReturnQuantity())
                            ? 0
                            : val.getRealReturnQuantity());
                    skuStockDto.setTotalAmount(val.getTotalPriceTax());
                    return skuStockDto;
                  })
              .collect(
                  Collectors.toMap(
                      SkuStockDto::getSkuCode,
                      a -> a,
                      (o1, o2) -> {
                        o1.setQuantity(o1.getQuantity() + o2.getQuantity());
                        o1.setTotalAmount(o1.getTotalAmount().add(o2.getTotalAmount()));
                        return o1;
                      }));
    }

    return map;
  }

  public SingleResponse<PaymentDetailAmountVO> getRightPayAmountPlus(
      Long id, List<Long> choosedDetailIdList, Boolean advancePayment) {

    // 采购单总付款金额
    BigDecimal orderAmount;
    Map<String, SkuStockDto> skuRealStockInMap = new HashMap<>();
    Map<String, SkuStockDto> skuRealStockOutMap = new HashMap<>();

    final PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(id);

    // 针对采购单时预付款的情况
    //        boolean advancePayment = Objects.nonNull(purchaseOrder.getPayType()) &&
    // OrderMap.PAY_PROPORTION.containsKey(purchaseOrder.getPayType());
    if (advancePayment) {
      orderAmount = new BigDecimal(purchaseOrder.getTotalPurchaseTaxAmount());
      //            orderAmount =
      // OrderMap.PAY_PROPORTION.get(purchaseOrder.getPayType()).multiply(totalPurchaseAmount);
    } else {
      skuRealStockInMap = skuRealStockInMap(id);
      skuRealStockOutMap = skuRealStockOutMap(id);
      // 采购单总付款金额
      orderAmount =
          skuRealStockInMap.values().stream()
              .map(PurchaseOrderBizServiceImpl.SkuStockDto::getTotalAmount)
              .reduce(BigDecimal.ZERO, BigDecimal::add)
              .subtract(
                  skuRealStockOutMap.values().stream()
                      .map(PurchaseOrderBizServiceImpl.SkuStockDto::getTotalAmount)
                      .reduce(BigDecimal.ZERO, BigDecimal::add));
    }

    // 采购单已经付款金额
    BigDecimal paymentAmount = alreadyPayAmount(id);
    // 订单剩余付款金额
    BigDecimal rightAmount = orderAmount.subtract(paymentAmount);
    // 订单剩余付款金额不能小于0，如果小于0，取0
    rightAmount = rightAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : rightAmount;

    if (CollUtil.isEmpty(choosedDetailIdList)) {
      PaymentDetailAmountVO vv = new PaymentDetailAmountVO();
      vv.setPaidAmount(paymentAmount);
      vv.setRightAmount(rightAmount.setScale(2, RoundingMode.DOWN));
      vv.setOrderAmount(orderAmount.setScale(2, RoundingMode.DOWN));
      setPayPurpose(purchaseOrder.getNo(), vv);
      return SingleResponse.of(vv);
    }

    final List<PurchaseOrderDetail> detailList =
        iPurchaseOrderDetailService
            .lambdaQuery()
            .in(PurchaseOrderDetail::getId, choosedDetailIdList)
            .list();
    Assert.isTrue(CollUtil.isNotEmpty(detailList), "部分SKU申请付款的情况下，明细ID非法，明细列表为空");
    List<String> matchedSkuKeys =
        detailList.stream()
            .map(PurchaseOrderDetail::getItemSkuCode)
            .distinct()
            .collect(Collectors.toList());

    // fixme
    // 针对部分SKU申请付款的情况，更新应付金额，从SKU维度重新计算，这里其实有问题
    // 设 采购单总共有，A,B,C 三个SKU，价税合计分别是 10,20,30。总付款金额即为 60。
    // 第一次付款A，B。最大可付款金额为30，实际付款25
    // 第二次付款A，C。最大可付款金额为 30 + X，这里的X需要根据上次一的付款明细中计算，计算出A的剩余可付款金额。那在第一次实际付款明细中，A的实际付款金额要按什么规则算呢？
    // 如果粗糙一点来计算，保证最大冗余。第二次付款操作时，单据整体剩余可付款金额 = 最大可付款金额 = 60 - 25 = 35，
    //                  直接粗糙的计算，A+C 应付 10+30=40，40 >
    // 35，这明显是不合理的，所以这边加个判断，如果A+C的合计金额大于剩余可付款金额，则取剩余可付款金额。

    // 这批SKU总付款金额
    AtomicReference<BigDecimal> chooseSkuOrderAmount = new AtomicReference<>(BigDecimal.ZERO);
    if (advancePayment) {
      detailList.stream()
          .map(PurchaseOrderDetail::getTotalPriceTax)
          .reduce(BigDecimal::add)
          .ifPresent(chooseSkuOrderAmount::set);
    } else {
      BigDecimal skuInAmount = BigDecimal.ZERO;
      BigDecimal skuOutAmount = BigDecimal.ZERO;
      for (String skuKey : matchedSkuKeys) {
        if (Objects.nonNull(skuRealStockInMap.get(skuKey))) {
          skuInAmount = skuInAmount.add(skuRealStockInMap.get(skuKey).getTotalAmount());
        }
        if (Objects.nonNull(skuRealStockOutMap.get(skuKey))) {
          skuOutAmount = skuOutAmount.add(skuRealStockOutMap.get(skuKey).getTotalAmount());
        }
      }
      chooseSkuOrderAmount.set(skuInAmount.subtract(skuOutAmount));
    }

    // 这批SKU付款金额应该小于订单整体剩余可付金额。
    BigDecimal skuAmount =
        chooseSkuOrderAmount.get().compareTo(rightAmount) >= 0
            ? rightAmount
            : chooseSkuOrderAmount.get();

    // 这批SKU的订单金额和应付金额都是这批SKU的应付金额。
    PaymentDetailAmountVO vv = new PaymentDetailAmountVO();
    vv.setPaidAmount(BigDecimal.ZERO);
    vv.setRightAmount(skuAmount.setScale(2, RoundingMode.DOWN));
    vv.setOrderAmount(skuAmount.setScale(2, RoundingMode.DOWN));
    // 判断采购单是否允许进行预付款。 若业务先采购付款，则不允许进行预付款了！ 此采购单已经存在采购付款类型的付款单，则不允许进行预付款
    setPayPurpose(purchaseOrder.getNo(), vv);

    return SingleResponse.of(vv);
  }

  @Override
  public PayPurposeEnum getPayPurpose(String purchaseOrderNo) {
    final List<Long> orderIds =
        iPaymentApplyOrderDetailService
            .lambdaQuery()
            .eq(PaymentApplyOrderDetail::getApplyRelatedNo, purchaseOrderNo)
            .eq(PaymentApplyOrderDetail::getDetailSource, 0)
            .list()
            .stream()
            .map(PaymentApplyOrderDetail::getPaymentApplyOrderId)
            .collect(Collectors.toList());

    if (CollUtil.isNotEmpty(orderIds)) {
      final List<PaymentApplyOrder> list =
          iPaymentApplyOrderService.lambdaQuery().in(PaymentApplyOrder::getId, orderIds).list();
      // 此采购单如果之前存在了采购付款，则后续只允许进行采购付款，不允许再进行其他类型的付款
      long count = list.stream().filter(val -> val.getPayPurpose().equals(0)).count();
      if (count > 0) {
        return PayPurposeEnum.PURCHASE_PAYMENT;
      }
    }
    return PayPurposeEnum.ALL;
  }

  private void setPayPurpose(String purchaseOrderNo, PaymentDetailAmountVO vo) {
    final List<Long> orderIds =
        iPaymentApplyOrderDetailService
            .lambdaQuery()
            .eq(PaymentApplyOrderDetail::getApplyRelatedNo, purchaseOrderNo)
            .eq(PaymentApplyOrderDetail::getDetailSource, 0)
            .list()
            .stream()
            .map(PaymentApplyOrderDetail::getPaymentApplyOrderId)
            .collect(Collectors.toList());

    if (CollUtil.isNotEmpty(orderIds)) {
      final List<PaymentApplyOrder> list =
          iPaymentApplyOrderService.lambdaQuery().in(PaymentApplyOrder::getId, orderIds).list();
      // 此采购单如果之前存在了采购付款，则后续只允许进行采购付款，不允许再进行其他类型的付款
      long count = list.stream().filter(val -> val.getPayPurpose().equals(0)).count();
      if (count > 0) {
        vo.setSupportPayPurpose(PayPurposeEnum.PURCHASE_PAYMENT);
      }

      final int oldAdvanceRatio =
          list.stream()
              .filter(val -> val.getPayPurpose().equals(1))
              .mapToInt(PaymentApplyOrder::getPayProportions)
              .sum();
      vo.setMaxAdvanceRatio(100 - oldAdvanceRatio);
      return;
    }
    vo.setSupportPayPurpose(PayPurposeEnum.ALL);
    vo.setMaxAdvanceRatio(100);
  }

  @Override
  public BigDecimal alreadyPayAmount(Long purchaseId) {
    // 采购单已经付款金额
    BigDecimal paymentAmount = BigDecimal.ZERO;
    PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(purchaseId);
    if (Objects.nonNull(purchaseOrder)) {
      String no = purchaseOrder.getNo();
      paymentAmount = iPaymentApplyOrderService.getAppliedPaymentAmountByRelatedNo(ListUtil.of(no));
    }
    return paymentAmount;
  }

  //    @Override
  //    public Boolean isAllPayment(Long purchaseOrderId) {
  //        Map<String, BigDecimal> realPayAmountEverySkuMap =
  // realPayAmountEverySku(purchaseOrderId);
  //        BigDecimal orderMaxPaymentAmount =
  // realPayAmountEverySkuMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
  //        BigDecimal alreadyPayAmount = alreadyPayAmount(purchaseOrderId);
  //        return alreadyPayAmount.compareTo(orderMaxPaymentAmount) >= 0;
  //    }

  @Override
  public Response exportDetail(List<Long> purchaseOrderIdList) {
    Assert.isTrue(CollUtil.isNotEmpty(purchaseOrderIdList), "采购订单ID不得为空");

    ExportTask exportTask = ExportTask.initTask("采购订单word", ExportTaskType.PURCHASE_ORDER_DETAIL);
    iExportTaskService.save(exportTask);

    ThreadUtil.execute(
        PoolEnum.COMMON_POOL,
        () -> {
          List<java.io.File> successFiles = new LinkedList<>();
          for (Long orderId : purchaseOrderIdList) {
            try {
              SingleResponse<PurchaseOrderViewVO> view = view(orderId);
              PurchaseOrderViewVO viewVO = view.getData();
              PurchaseOrderDetailPageQuery pageQuery = new PurchaseOrderDetailPageQuery();
              pageQuery.setPurchaseOrderId(orderId);
              pageQuery.setPageSize(9999);
              PageResponse<PurchaseOrderDetailVO> pageView = pageView(pageQuery);
              List<PurchaseOrderDetailVO> dataList = pageView.getData();
              java.io.File docFile =
                  new java.io.File(viewVO.getNo() + "_" + DateUtil.currentTime() + ".docx");

              writeInfoFile(viewVO, dataList, docFile, "/template/采购订单导出模板.docx");
              successFiles.add(docFile);
            } catch (Exception e) {
              log.error("导出采购订单word异常", e);
            }
          }

          if (CollUtil.isEmpty(successFiles)) {
            exportTask.setStatus(ExportTaskStatus.FAIL);
            exportTask.setError("导出采购订单word异常，没有成功的文件");
            iExportTaskService.updateById(exportTask);
            return;
          }

          if (successFiles.size() == 1) {
            try {
              UploadFileAction uploadFileAction = UploadFileAction.ofFile(successFiles.get(0));
              FileStub fileStub = fileGateway.uploadFile(uploadFileAction);
              exportTask.setDownloadUrl(fileStub.getUrl());
              exportTask.setStatus(ExportTaskStatus.SUCCESS);
            } catch (Exception e) {
              log.error("导出采购订单word，上传文件异常", e);
              exportTask.setStatus(ExportTaskStatus.FAIL);
              exportTask.setError(e.getMessage());
            } finally {
              FileUtil.del(successFiles.get(0));
              iExportTaskService.updateById(exportTask);
            }
            return;
          }

          if (!successFiles.isEmpty()) {
            try {
              String zipFilePath =
                  "purchase_zip" + java.io.File.separator + RandomUtil.randomString(4) + ".zip";
              java.io.File zipFile = new java.io.File(zipFilePath);
              java.io.File zip = ZipUtil.zip(zipFile, false, successFiles.toArray(new File[] {}));

              UploadFileAction uploadFileAction = UploadFileAction.ofFile(zip);
              FileStub fileStub = fileGateway.uploadFile(uploadFileAction);
              exportTask.setDownloadUrl(fileStub.getUrl());
              exportTask.setStatus(ExportTaskStatus.SUCCESS);
            } catch (Exception e) {
              log.error("导出采购订单word，zip文件处理异常", e);
              exportTask.setStatus(ExportTaskStatus.FAIL);
              exportTask.setError(e.getMessage());
            } finally {
              FileUtil.del(successFiles.get(0));
              iExportTaskService.updateById(exportTask);
            }
          }
        });

    return Response.buildSuccess();
  }

  @Override
  public void taskTimeoutAutoAgreeReminder(PurchaseOrder purchaseOrder, TaskInstance taskInstance) {
    final Provider provider = providerGateway.getById(purchaseOrder.getProviderId());
    Assert.notNull(provider, "采购单供应商数据异常");

    final Long createdUid = purchaseOrder.getCreatedUid();
    final DadStaffVO createdUser = staffService.getStaff(createdUid);

    final String link =
        refreshConfig.getDomain()
            + String.format(
                "/commodity-purchase/purchase-order/review?id=%s", purchaseOrder.getId());
    final WechatMsg wechatMsg = new WechatMsg();
    wechatMsg.setTitle(
        String.format(
            "%s %s 采购单 %s 超时未审核，已为您自动审核通过！",
            createdUser.getNickname(), provider.getName(), purchaseOrder.getNo()));
    wechatMsg.setContent("点击查看详情");
    wechatMsg.setLink(link);
    final String target = taskInstance.getTarget();
    final StaffBrief staffBrief =
        staffService
            .getStaffByLoginName(target)
            .orElseThrow(
                () -> ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "审核任务办理人信息查询异常"));
    wechatMsg.setRecipient(staffBrief.getQwUserId());
    wechatMsg.setType(1);
    msgSender.send(wechatMsg);
  }

  public void writeInfoFile(
      PurchaseOrderViewVO viewVO,
      List<PurchaseOrderDetailVO> dataList,
      java.io.File docFile,
      String path)
      throws Exception {
    final HashMap<String, Object> model = new HashMap<>();
    model.put("采购单号", viewVO.getNo());
    model.put("采购组织", viewVO.getOrganizationName());
    model.put("到货方式", viewVO.getArrivalType() == 1 ? "一次性到货" : "分批到货");
    model.put(
        "采购日期",
        DateUtil.parseTimeStamp(viewVO.getPurchaseDate(), DatePattern.NORM_DATETIME_PATTERN));
    model.put("采购部门", viewVO.getDepartmentName());
    model.put("付款方式", getPayType(viewVO.getPayType()));
    model.put("供应商", viewVO.getProviderName());
    model.put("采购员", viewVO.getBuyerUserName());
    model.put("价税合计", viewVO.getTotalPurchaseTaxAmount());
    model.put("供应商名称", viewVO.getProviderName());

    final ArrayList<Map<String, Object>> details = getMaps(dataList);
    model.put("商品明细", details);

    if (path.contains("采购订单导出模板")) {
      model.put("创建人", viewVO.getCreatedName());
      model.put(
          "创建时间",
          DateUtil.parseTimeStamp(viewVO.getCreatedAt(), DatePattern.NORM_DATETIME_PATTERN));
    }

    model.put("仓库收货地址", "");
    if (CollectionUtils.isNotEmpty(dataList)) {
      String warehouseNo = dataList.get(0).getWarehouseNo();
      List<Warehouse> list =
          iWarehouseService.lambdaQuery().eq(Warehouse::getNo, warehouseNo).last("limit 1").list();
      if (CollUtil.isNotEmpty(list)) {
        Warehouse warehouse = list.get(0);
        String format = "仓库收件地址：{}。{}。{}";
        String tel = StrUtil.isBlank(warehouse.getTel()) ? "" : warehouse.getTel();
        String phone = StrUtil.isBlank(warehouse.getPhone()) ? "" : warehouse.getPhone();
        String str =
            StrUtil.format(
                format,
                StrUtil.isBlank(warehouse.getContacts()) ? "" : warehouse.getContacts(),
                tel + "/" + phone,
                StrUtil.isBlank(warehouse.getFullAddress()) ? "" : warehouse.getFullAddress());
        model.put("仓库收货地址", str);
      }
    }

    final Configure config =
        Configure.builder().bind("商品明细", new LoopRowTableRenderPolicy()).build();
    ClassPathResource classPathResource = new ClassPathResource(path);
    InputStream templateInput = classPathResource.getInputStream();
    XWPFTemplate template = XWPFTemplate.compile(templateInput, config).render(model);
    template.writeToFile(docFile.getPath());
  }

  @NotNull
  private static ArrayList<Map<String, Object>> getMaps(List<PurchaseOrderDetailVO> dataList) {
    final ArrayList<Map<String, Object>> details = new ArrayList<>();
    for (PurchaseOrderDetailVO orderDetailVO : dataList) {
      HashMap<String, Object> map =
          new HashMap<String, Object>(16) {

            private static final long serialVersionUID = 1029421217995899782L;

            {
              put("商品SKU", orderDetailVO.getItemSkuCode());
              put("条码", orderDetailVO.getBarCode());
              put("商品名称", orderDetailVO.getItemName());
              put("规格名称", orderDetailVO.getSpecifications());
              put("单位", orderDetailVO.getUnit());
              put("数量", orderDetailVO.getPurchaseQuantity());
              put("单价", orderDetailVO.getTaxPrice());
              put("金额", orderDetailVO.getTotalPriceTax());
              put("仓库", orderDetailVO.getWarehouseName());
              //                    put("价税合计", orderDetailVO.getTotalPriceTax());
              put("备注", orderDetailVO.getRemark());
            }
          };
      details.add(map);
    }
    return details;
  }

  public void appendStringListToWord(File filePath, List<String> appendList) throws Exception {
    FileInputStream fis = new FileInputStream(filePath);
    XWPFDocument document = new XWPFDocument(fis);
    fis.close();

    XWPFParagraph paragraph = document.createParagraph();
    for (String appendStr : appendList) {
      XWPFRun run = paragraph.createRun();
      run.setFontSize(8);
      run.setText(appendStr);
      run.addBreak();
    }

    // 将修改后的文档写回文件
    FileOutputStream fos = new FileOutputStream(filePath);
    document.write(fos);
    fos.close();
  }

  /**
   * 1:款到发货、2:预付10%、3:预付20%、4:预付30%、5:预付40%、6:预付50%、7:预付60%、8:货到付款、9:月结
   *
   * @param payType
   * @return
   */
  private String getPayType(Integer payType) {
    String res = "";
    switch (payType) {
      case 1:
        res = "款到发货";
        break;
      case 2:
        res = "预付10%";
        break;
      case 3:
        res = "预付20%";
        break;
      case 4:
        res = "预付30%";
        break;
      case 5:
        res = "预付40%";
        break;
      case 6:
        res = "预付50%";
        break;
      case 7:
        res = "预付60%";
        break;
      case 8:
        res = "货到付款";
        break;
      case 9:
        res = "月结";
        break;
      default:
    }
    return res;
  }

  @Override
  public PurchaseOrder generateByPaymentApplyOrder(
      PaymentApplyOrder order, List<PaymentApplyOrderDetail> paymentApplyOrderDetailList) {

    return null;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public SingleResponse<Boolean> updateTaxData(UpdateTaxDataCmd updateTaxDataCmd) {
    PurchaseOrderDetail purchaseOrderDetail =
        iPurchaseOrderDetailService.getById(updateTaxDataCmd.getDetailId());
    Assert.state(Objects.nonNull(purchaseOrderDetail), "明细 ID 不得为空");
    PurchaseOrder purchaseOrder =
        iPurchaseOrderService.getById(purchaseOrderDetail.getPurchaseOrderId());
    Assert.state(Objects.nonNull(purchaseOrder), "采购订单不得为空");

    String itemSkuCode = purchaseOrderDetail.getItemSkuCode();
    ItemSku itemSku = itemSkuGateway.getBySkuCode(itemSkuCode);
    BigDecimal oldTaxRate = itemSku.getPurchaseTaxRate();
    BigDecimal taxRate = updateTaxDataCmd.getTaxRate();
    if (taxRate.compareTo(oldTaxRate) != 0) {
      itemSku.setPurchaseTaxRate(taxRate);
      iItemSkuService.updateById(itemSku);
    }

    // 税额+税后金额=价税合计
    BigDecimal add = updateTaxDataCmd.getTaxQuota().add(updateTaxDataCmd.getAfterTaxAmount());
    boolean b = add.compareTo(purchaseOrderDetail.getTotalPriceTax()) == 0;
    if (!b) {
      String format =
          StrUtil.format(
              "税额+税后金额不等于价税合计.税额:{},税后金额:{},加税合计:{}",
              updateTaxDataCmd.getTaxQuota(),
              updateTaxDataCmd.getAfterTaxAmount(),
              purchaseOrderDetail.getTotalPriceTax());
      throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, format);
    }

    purchaseOrderDetail.setTaxRate(taxRate);
    purchaseOrderDetail.setTaxQuota(updateTaxDataCmd.getTaxQuota());
    purchaseOrderDetail.setAfterTaxAmount(updateTaxDataCmd.getAfterTaxAmount());
    iPurchaseOrderDetailService.updateById(purchaseOrderDetail);

    // 刷新订单明细
    List<PurchaseOrderDetail> detailList =
        iPurchaseOrderDetailService
            .lambdaQuery()
            .eq(PurchaseOrderDetail::getPurchaseOrderId, purchaseOrderDetail.getPurchaseOrderId())
            .list();
    BigDecimal totalTaxQuota =
        detailList.stream()
            .map(PurchaseOrderDetail::getTaxQuota)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    purchaseOrder.setTotalTaxAmount(totalTaxQuota.toString());
    BigDecimal totalAfterTaxAmount =
        detailList.stream()
            .map(PurchaseOrderDetail::getAfterTaxAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    purchaseOrder.setTotalPurchaseAmount(totalAfterTaxAmount.toString());
    iPurchaseOrderService.updateById(purchaseOrder);

    return SingleResponse.of(true);
  }

  @Override
  public MultiResponse<Long> getTodoList() {
    final List<Long> todoList =
        awsApprovalFlowService.getTodoList(
            PurchaseTypeEnum.PURCHASE_ORDER.getValue(), UserContext.getLoginName());
    if (todoList.isEmpty()) {
      return MultiResponse.of(todoList);
    }
    final List<Long> todoAuditList =
        iPurchaseOrderService
            .lambdaQuery()
            .eq(PurchaseOrder::getState, PurchaseOrderState.WAIT_CHECK)
            .in(PurchaseOrder::getId, todoList)
            .select(PurchaseOrder::getId)
            .list()
            .stream()
            .map(PurchaseOrder::getId)
            .collect(Collectors.toList());
    return MultiResponse.of(todoAuditList);
  }

  @Override
  public Response callBackOrderState(CallBackOrderStateCmd cmd) {
    log.info("purchase order callBackOrderState param:{}", JsonUtil.toJson(cmd));

    List<AwsBusinessLog> list =
        iAwsBusinessLogService
            .lambdaQuery()
            .eq(AwsBusinessLog::getProcessId, cmd.getProcessInstId())
            .eq(AwsBusinessLog::getType, PurchaseTypeEnum.PURCHASE_ORDER.getValue())
            .list();
    if (CollUtil.isEmpty(list)) {
      return Response.buildFailure(ErrorCode.PROCESS_ERROR.getCode(), "审核数据查询为空");
    }
    AwsBusinessLog awsBusinessLog = list.get(0);
    Long businessId = awsBusinessLog.getBusinessId();
    PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(businessId);

    String callBackLog = "老爸工作台回调，";
    // O:废弃/删除。1:审核拒绝。2:审核完成。3:审核撤销。4:审核同意
    if (cmd.getCallBackState() == 0) {
      iPurchaseOrderService
          .lambdaUpdate()
          .eq(PurchaseOrder::getId, businessId)
          .set(PurchaseOrder::getState, PurchaseOrderState.WAIT_SUBMIT.getValue())
          .set(PurchaseOrder::getWorkbenchProcessId, StrUtil.EMPTY)
          .update();
      callBackLog = callBackLog + "审核流废弃/删除";
    }
    if (cmd.getCallBackState() == 1) {
      iPurchaseOrderService
          .lambdaUpdate()
          .eq(PurchaseOrder::getId, businessId)
          .set(PurchaseOrder::getState, PurchaseOrderState.REJECT_AUDIT.getValue())
          .update();
      callBackLog = callBackLog + "审核拒绝";
      // 企微通知采购单流程审核通过
      awsChangeEventListener.noticePurchaseOrderProcessComplete(purchaseOrder, false);
    }
    if (cmd.getCallBackState() == 2) {
      iPurchaseOrderService
          .lambdaUpdate()
          .eq(PurchaseOrder::getId, businessId)
          .set(PurchaseOrder::getState, PurchaseOrderState.PASS_AUDIT.getValue())
          .update();
      callBackLog = callBackLog + "审核完成";
      // 企微通知采购单流程审核通过
      awsChangeEventListener.noticePurchaseOrderProcessComplete(purchaseOrder, true);
      // 发起OA盖章流程
      ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL)
          .execute(
              () -> {
                try {
                  startOASealProcess(businessId);
                } catch (Exception e) {
                  log.error("【系统】发起OA盖章流程异常.bizId:{}", businessId, e);
                  operateLogGateway.addOperatorLog(
                      0L, OperateLogTarget.PURCHASE_ORDER, businessId, "发起OA盖章流程异常", null);
                }
              });
    }
    if (cmd.getCallBackState() == 3) {
      iPurchaseOrderService
          .lambdaUpdate()
          .eq(PurchaseOrder::getId, businessId)
          .set(PurchaseOrder::getState, PurchaseOrderState.WITHDRAW_AUDIT.getValue())
          .update();
      callBackLog = callBackLog + "审核撤销";
    }
    if (cmd.getCallBackState() == 4) {
      if (PurchaseOrderState.PASS_AUDIT.getValue().equals(purchaseOrder.getState())) {
        callBackLog = callBackLog + "审核同意(surplus)";
      } else {
        noticePurchaseOrderCurrentProcessors(
            purchaseOrder, awsApprovalFlowService.taskQuery(cmd.getProcessInstId()));
        callBackLog = callBackLog + "审核同意";
      }
    }
    // O:废弃/删除。1:审核拒绝/审核撤销。2:审核完成
    operateLogGateway.addOperatorLog(
        0L, OperateLogTarget.PURCHASE_ORDER, businessId, callBackLog, null);

    return Response.buildSuccess();
  }

  @Override
  public SingleResponse<Boolean> isExistStockOrder(PurchaseOrderIdCmd cmd) {
    boolean b1 =
        iStockInOrderService
                .lambdaQuery()
                .in(StockInOrder::getPurchaseOrderId, cmd.getIds())
                .count()
            > 0;
    boolean b2 =
        iStockOutOrderService
                .lambdaQuery()
                .in(StockOutOrder::getPurchaseOrderId, cmd.getIds())
                .count()
            > 0;
    // 当采购单存在下游的出入库单，才允许在页面付款用途上选择采购付款。
    return SingleResponse.of(b1 || b2);
  }

  @Override
  public SingleResponse<Boolean> completedQuantityVerification(Long purchaseId) {

    // 这种判断只针对 已完结 状态的采购单
    PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(purchaseId);
    if (Objects.equals(purchaseOrder.getState(), PurchaseOrderState.FINISHED.getValue())) {
      // 采购数量1.1倍
      final int maxQuantity =
          new BigDecimal(purchaseOrder.getTotalPurchaseQuantity())
              .multiply(new BigDecimal("1.1"))
              .setScale(0, RoundingMode.HALF_UP)
              .intValue();

      final Map<String, SkuStockDto> inMap = skuRealStockInMap(purchaseId);
      final int inSum = inMap.values().stream().mapToInt(SkuStockDto::getQuantity).sum();
      final Map<String, SkuStockDto> outMap = skuRealStockOutMap(purchaseId);
      final int outSum = outMap.values().stream().mapToInt(SkuStockDto::getQuantity).sum();

      return SingleResponse.of((inSum - outSum) < maxQuantity);
    }

    return SingleResponse.of(true);
  }

  @Override
  @DistributedLock(searchKey = DistributedLock.SearchKeyStrategy.PARAMETER)
  public void startOASealProcess(@DistributedLockKey Long purchaseOrderId) {

    final SingleResponse<PurchaseOrderViewVO> view = view(purchaseOrderId);
    PurchaseOrderDetailPageQuery detailPageQuery = new PurchaseOrderDetailPageQuery();
    detailPageQuery.setPurchaseOrderId(purchaseOrderId);
    final PageResponse<PurchaseOrderDetailVO> detailView = pageView(detailPageQuery);

    if (!view.isSuccess() || !detailView.isSuccess()) {
      operateLogGateway.addOperatorLog(
          0L,
          OperateLogTarget.PURCHASE_ORDER,
          purchaseOrderId,
          StrUtil.format("采购单信息查询失败，无法发起OA盖章流程，采购单ID：{}", purchaseOrderId),
          null);
      return;
    }

    final PurchaseOrderViewVO order = view.getData();
    if (StrUtil.isNotBlank(order.getOaSealProcessId())) {
      operateLogGateway.addOperatorLog(
          0L,
          OperateLogTarget.PURCHASE_ORDER,
          purchaseOrderId,
          StrUtil.format("此采购单已经发起OA盖章流程，无需重复发起。已发起的事件ID:{}", order.getOaSealProcessId()),
          null);
      return;
    }

    final List<PurchaseOrderDetailVO> orderDetailList = detailView.getData();

    boolean isManualOrder = order.getType().equals(1);
    boolean isCreatedByBuyer = order.getBuyerUserId() != 0L && order.getCreatedUid() > 0L;
    boolean containsCustomizedProducts;
    boolean auditPass = order.getState().equals(3);

    final Set<Long> itemIdSet =
        orderDetailList.stream().map(PurchaseOrderDetail::getItemId).collect(Collectors.toSet());
    Map<Long, List<CorpBizTypeDTO>> divisionMap = iBizLevelDivisionService.queryByItemId(itemIdSet);
    containsCustomizedProducts =
        divisionMap.values().stream()
            .flatMap(List::stream)
            .anyMatch(
                division -> division.containBizType(DivisionLevelValueEnum.B_CUSTOMIZED_PRODUCTS));

    if (!(isManualOrder && isCreatedByBuyer && containsCustomizedProducts && auditPass)) {
      operateLogGateway.addOperatorLog(
          0L,
          OperateLogTarget.PURCHASE_ORDER,
          purchaseOrderId,
          "发起盖章流程必须同时满足以下条件。1.标准采购单。2.存在业务采购员。3.存在订制品。4.通过审核。",
          null);
      return;
    }

    final DadStaffVO staff = staffService.getStaff(order.getBuyerUserId());
    if (Objects.isNull(staff) || Objects.isNull(staff.getOaId())) {
      operateLogGateway.addOperatorLog(
          0L,
          OperateLogTarget.PURCHASE_ORDER,
          purchaseOrderId,
          StrUtil.format("采购员不存在OA用户信息，无法发起OA盖章流程，采购员：{}", order.getBuyerUserId()),
          null);
      return;
    }
    PartnerContractQuery contractQuery = new PartnerContractQuery();
    contractQuery.setContractNo(order.getContractNo());
    final Rsp<List<PartnerContract>> listRsp = partnerFeignClient.contractQuery(contractQuery);
    boolean b =
        listRsp.isSuccess()
            && CollUtil.isNotEmpty(listRsp.getData())
            && listRsp.getData().stream()
                .anyMatch(val -> StrUtil.isNotBlank(val.getContractDesc()));
    if (!b) {
      operateLogGateway.addOperatorLog(
          0L,
          OperateLogTarget.PURCHASE_ORDER,
          purchaseOrderId,
          StrUtil.format("此合同内容信息不得为空。无法发起OA盖章流程，合同编码：{}", order.getContractNo()),
          null);
      return;
    }
    final PartnerContract partnerContract = listRsp.getData().get(0);
    String safeFileName = order.getNo() + "_oa_" + DateUtil.currentTime() + ".docx";
    java.io.File docFile = new java.io.File(safeFileName);
    try {
      writeInfoFile(order, orderDetailList, docFile, "/template/采购订单合同.docx");
    } catch (Exception e) {
      log.error("生成采购订单合同异常，purchaseOrderId:{}", purchaseOrderId, e);
      operateLogGateway.addOperatorLog(
          0L,
          OperateLogTarget.PURCHASE_ORDER,
          purchaseOrderId,
          StrUtil.format("生成采购订单合同文件异常，无法发起OA盖章流程"),
          null);
      FileUtil.del(docFile);
      return;
    }

    final String bpmToken = bpmClient.getUserOaToken(staff.getLoginName());
    if (StringUtils.isBlank(bpmToken)) {
      operateLogGateway.addOperatorLog(
          0L,
          OperateLogTarget.PURCHASE_ORDER,
          purchaseOrderId,
          StrUtil.format("获取bpm token失败，无法发起OA盖章流程。"),
          null);
      return;
    }
    String dmpFileUrl;
    CustomMultipartFile multipartFile = new CustomMultipartFile(docFile);
    String dmpResStr = "";
    try {
      dmpResStr = bpmFeignClient.uploadAttachment(bpmToken, multipartFile);
      JsonNode dmpResNode = JsonUtil.parse(dmpResStr);
      assert dmpResNode != null;
      dmpFileUrl = dmpResNode.get("atts").get(0).get("fileUrl").asText();
      Assert.hasText(dmpFileUrl, "合同文件上传到OA的附件URL不得为空");
    } catch (Exception e) {
      log.error("上传合同附件到bmp异常，无法发起OA盖章流程。oaRes:{}", dmpResStr, e);
      operateLogGateway.addOperatorLog(
          0L,
          OperateLogTarget.PURCHASE_ORDER,
          purchaseOrderId,
          StrUtil.format("上传合同附件到bmp异常，无法发起OA盖章流程。"),
          null);
      return;
    } finally {
      FileUtil.del(docFile);
      multipartFile.deleteFile();
    }

    final String reqJson =
        buildOaRequestParam(order, partnerContract, staff.getOaId().toString(), dmpFileUrl);
    final String bpmResponseStr = bpmFeignClient.pushForm(reqJson, bpmToken);
    String affairId;
    try {
      JsonNode bpmJsonNode = JsonUtil.parse(bpmResponseStr);
      assert bpmJsonNode != null;
      String bizNodeJsonStr = bpmJsonNode.get("data").get("app_bussiness_data").asText();
      JsonNode bizNode = JsonUtil.parse(bizNodeJsonStr);
      assert bizNode != null;
      affairId = bizNode.get("affairId").asText();
      Assert.hasText(affairId, "OA流程事件ID不得为空");
      iPurchaseOrderService
          .lambdaUpdate()
          .set(PurchaseOrder::getOaSealProcessId, affairId)
          .eq(PurchaseOrder::getId, purchaseOrderId)
          .update();
      operateLogGateway.addOperatorLog(
          0L,
          OperateLogTarget.PURCHASE_ORDER,
          purchaseOrderId,
          StrUtil.format("发起OA盖章流程成功，事件ID：{}", affairId),
          null);
    } catch (Exception e) {
      log.error(
          "reqJson:{},bpmResponseStr:{},purchaseOrderId:{}",
          reqJson,
          bpmResponseStr,
          purchaseOrderId);
      operateLogGateway.addOperatorLog(
          0L,
          OperateLogTarget.PURCHASE_ORDER,
          purchaseOrderId,
          StrUtil.format("发起OA盖章流程失败，OA响应：{}", bpmResponseStr),
          null);
    }
  }

  public String buildOaRequestParam(
      PurchaseOrderViewVO order, PartnerContract contract, String odId, String dmpFileUrl) {

    DataForm.MainData.MainForm mainForm = getMainForm(order, odId);

    DataForm.MainData.SubForm subForm = new DataForm.MainData.SubForm();
    subForm.setStartTime(DateUtil.format(contract.getStartTime(), "yyyy-MM-dd"));
    subForm.setDesc(contract.getContractDesc());
    subForm.setEndTime(DateUtil.format(contract.getEndTime(), "yyyy-MM-dd"));

    final String format1 = DateUtil.format(order.getPurchaseDate(), "yyyyMM");
    final String sort = order.getNo().substring(order.getNo().length() - 3);
    subForm.setNo("DKDD" + format1 + sort);
    subForm.setAmount(order.getTotalPurchaseTaxAmount());

    DataForm.MainData.ThirdAttachments thirdAttachments = new DataForm.MainData.ThirdAttachments();
    thirdAttachments.setFileUrl(dmpFileUrl);

    // -------------

    DataForm.MainData mainData = new DataForm.MainData();
    mainData.setMainForm(mainForm);
    mainData.setSubForms(ListUtil.of(subForm));
    mainData.setThirdAttachments(ListUtil.of(thirdAttachments));

    DataForm dataForm = new DataForm();
    dataForm.setData(mainData);
    dataForm.setTemplateCode(bpmClient.getTemplateCode());

    OASealProcessForm form = new OASealProcessForm();
    form.setData(dataForm);

    final String json = JsonUtil.toJson(form);
    log.info("OaRequestParam form:{}", json);
    return json;
  }

  @NotNull
  private DataForm.MainData.MainForm getMainForm(PurchaseOrderViewVO order, String odId) {
    DataForm.MainData.MainForm mainForm = new DataForm.MainData.MainForm();
    mainForm.setConcatType2(bpmClient.getContractType2());
    mainForm.setConcatType3(bpmClient.getContractType3());
    mainForm.setOutSystemId(order.getNo() + "_" + RandomUtil.randomNumbers(6));
    mainForm.setBizName(order.getProviderName());
    mainForm.setOperateId(odId);
    if (order.getOrganizationName().contains("老爸电商")) {
      mainForm.setCompanyName("-6050678774602315586");
    } else {
      mainForm.setCompanyName("-4212392206471508587");
    }
    return mainForm;
  }

  @Override
  public SingleResponse<String> oaSealUrl(Long purchaseOrderId) {
    final PurchaseOrder order = iPurchaseOrderService.getById(purchaseOrderId);
    final String oaSealProcessId = order.getOaSealProcessId();
    if (StrUtil.isEmpty(oaSealProcessId)) {
      return SingleResponse.of("");
    }
    OaGenerateUrlReq req = new OaGenerateUrlReq();
    req.setUserId(order.getBuyerUserId());
    req.setAffairId(oaSealProcessId);
    final Rsp<String> stringRsp = partnerFeignClient.generateOaUrl(req);
    log.info("id:{},oaSealUrlRes:{}", purchaseOrderId, JsonUtil.toJson(stringRsp));
    if (!stringRsp.isSuccess()) {
      return SingleResponse.of("");
    }
    if (StrUtil.isEmpty(stringRsp.getData())) {
      return SingleResponse.of("");
    }
    return SingleResponse.of(stringRsp.getData());
  }

  @Override
  public SingleResponse<Boolean> containsCustomizedProducts(Long purchaseOrderId) {
    final List<PurchaseOrderDetail> list =
        iPurchaseOrderDetailService
            .lambdaQuery()
            .eq(PurchaseOrderDetail::getPurchaseOrderId, purchaseOrderId)
            .list();
    if (CollUtil.isNotEmpty(list)) {
      final Set<Long> itemIdSet =
          list.stream().map(PurchaseOrderDetail::getItemId).collect(Collectors.toSet());
      Map<Long, List<CorpBizTypeDTO>> divisionMap =
          iBizLevelDivisionService.queryByItemId(itemIdSet);
      boolean b =
          divisionMap.values().stream()
              .flatMap(List::stream)
              .anyMatch(
                  division ->
                      division.containBizType(DivisionLevelValueEnum.B_CUSTOMIZED_PRODUCTS));
      return SingleResponse.of(b);
    }
    return SingleResponse.of(false);
  }

  public static void main(String[] args) {
    String json =
        "{\n"
            + "    \"n_a_s\": 1,\n"
            + "    \"atts\": [\n"
            + "        {\n"
            + "            \"id\": null,\n"
            + "            \"reference\": \"1\",\n"
            + "            \"subReference\": \"1\",\n"
            + "            \"category\": 0,\n"
            + "            \"type\": 0,\n"
            + "            \"filename\": \"CG2505270001_oa_1750387852.docx\",\n"
            + "            \"mimeType\": \"application/octet-stream\",\n"
            + "            \"createdate\": \"2025-06-20 10:50\",\n"
            + "            \"size\": \"12022\",\n"
            + "            \"description\": null,\n"
            + "            \"fileUrl\": \"2595045078442527081\",\n"
            + "            \"extension\": \"docx\",\n"
            + "            \"icon\": \"doc.gif\",\n"
            + "            \"iconFont\": \"doc\",\n"
            + "            \"genesisId\": null,\n"
            + "            \"sort\": 0,\n"
            + "            \"officeTransformEnable\": \"disable\",\n"
            + "            \"obsObjectKey\": \"\",\n"
            + "            \"v\": \"b38fa225ec8253a33e9b79da6ed2cfa2\",\n"
            + "            \"extraMap\": {},\n"
            + "            \"new\": true\n"
            + "        }\n"
            + "    ]\n"
            + "}";
    JsonNode dmpResNode = JsonUtil.parse(json);
    System.out.println(dmpResNode.get("atts").get(0).get("fileUrl").asText());
  }
}
