package com.daddylab.supplier.item.application.refundOrder;

import com.alibaba.cola.dto.Response;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.RawRefundAPI;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RawRefundSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RawRefundSearchResponse;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RawRefundSearchResponse.Order;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RawRefundUploadOrder;
import com.daddylab.supplier.item.application.afterSales.AfterSalesEditReturnLogisticsCmd;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesReturnLogisticsInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesReturnLogisticsInfoService;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderDetail;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderItem;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2022/11/7
 */
@Service
@AllArgsConstructor
public class RefundOrderEditReturnLogisticsTxService {

    IAfterSalesReturnLogisticsInfoService afterSalesReturnLogisticsInfoService;
    WdtGateway wdtGateway;

    @Transactional(rollbackFor = Throwable.class)
    public Response editReturnLogistics(AfterSalesEditReturnLogisticsCmd cmd,
            RefundOrderDetail refundOrderDetail) {
        final AfterSalesReturnLogisticsInfo afterSalesReturnLogisticsInfo = afterSalesReturnLogisticsInfoService.lambdaQuery()
                .eq(AfterSalesReturnLogisticsInfo::getReturnOrderNo, cmd.getReturnOrderNo())
                .oneOpt().orElseGet(AfterSalesReturnLogisticsInfo::new);
        afterSalesReturnLogisticsInfo.setReturnLogisticsNo(cmd.getReturnLogisticsNo());
        afterSalesReturnLogisticsInfo.setReturnLogisticsName(cmd.getReturnLogisticsName());
        afterSalesReturnLogisticsInfo.setReturnOrderNo(cmd.getReturnOrderNo());
        afterSalesReturnLogisticsInfo.setReturnWarehouseNo(cmd.getReturnWarehouseNo());
        afterSalesReturnLogisticsInfo.setFullAddress(cmd.getFullAddress());
        afterSalesReturnLogisticsInfo.setContacts(cmd.getContacts());
        afterSalesReturnLogisticsInfo.setTel(cmd.getTel());
        final boolean saveOrUpdate = afterSalesReturnLogisticsInfoService.saveOrUpdate(
                afterSalesReturnLogisticsInfo);
        if (!saveOrUpdate) {
            return Response.buildSuccess();
        }

        final RawRefundAPI rawRefundAPI = wdtGateway.getAPI(RawRefundAPI.class);
        final RawRefundAPI rawRefundQimenAPI =
                ApplicationContextUtil.isActiveProfile("prod", "gray") ? wdtGateway.getQimenAPI(
                        RawRefundAPI.class) : rawRefundAPI;

        final ArrayList<RawRefundUploadOrder> orderList = Lists.newArrayList();
        try {
            final RawRefundSearchParams params = new RawRefundSearchParams();
            final LocalDateTime endTime = DateUtil.now().plusDays(1).toLocalDate().atStartOfDay();
            final LocalDateTime startTime = endTime.minusDays(30);
            params.setStartTime(DateUtil.format(startTime));
            params.setEndTime(DateUtil.format(endTime));
            params.setTimeType(1);
            params.setTid(refundOrderDetail.getSrcOrderNos());
            final RawRefundSearchResponse searchResponse = rawRefundQimenAPI.search(params,
                    new Pager(100, 0, true));
            final Map<String, Order> rawRefundMap = searchResponse.getOrder().stream()
                    .collect(Collectors.toMap(Order::getOid, Function.identity(),
                            (a, b) -> a.getRefundId().compareTo(b.getRefundId()) > 0 ? a : b));
            for (RefundOrderItem returnItemDetail : refundOrderDetail.getReturnItemDetails()) {
                final Order rawRefundOrder = rawRefundMap.get(
                        returnItemDetail.getSrcOrderDetailNo());
                final RawRefundUploadOrder rawRefundUploadOrder = new RawRefundUploadOrder();
                if (Objects.nonNull(rawRefundOrder)) {
                    rawRefundUploadOrder.setRefundNo(rawRefundOrder.getRefundNo());
                    rawRefundUploadOrder.setNum(rawRefundOrder.getNum());
                    rawRefundUploadOrder.setTid(rawRefundOrder.getTid());
                    rawRefundUploadOrder.setOid(rawRefundOrder.getOid());
                    rawRefundUploadOrder.setType(rawRefundOrder.getType().intValue());
                    rawRefundUploadOrder.setStatus(rawRefundOrder.getStatus().intValue());
                    rawRefundUploadOrder.setRefundVersion("1");
                    rawRefundUploadOrder.setRefundAmount(rawRefundOrder.getRefundAmount());
                    rawRefundUploadOrder.setActualRefundAmount(
                            rawRefundOrder.getActualRefundAmount());
                    rawRefundUploadOrder.setTitle(rawRefundOrder.getTitle());
                    rawRefundUploadOrder.setLogisticsName(cmd.getReturnLogisticsName());
                    rawRefundUploadOrder.setLogisticsNo(cmd.getReturnLogisticsNo());
                    rawRefundUploadOrder.setBuyerNick(rawRefundOrder.getBuyerNick());
                    rawRefundUploadOrder.setRefundTime(rawRefundOrder.getRefundTime());
                    rawRefundUploadOrder.setCurrentPhaseTimeout(
                            rawRefundOrder.getCurrentPhaseTimeout());
                    rawRefundUploadOrder.setIsAftersale(rawRefundOrder.getIsAftersale());
                    rawRefundUploadOrder.setReason(rawRefundOrder.getReason());
                    rawRefundUploadOrder.setPrice(rawRefundOrder.getPrice());
                    rawRefundUploadOrder.setPayAccount(rawRefundOrder.getPayAccount());
                    rawRefundUploadOrder.setPayNo(rawRefundOrder.getPayNo());
                    rawRefundUploadOrder.setRemark(rawRefundOrder.getRemark());
                    rawRefundUploadOrder.setTotalAmount(rawRefundOrder.getTotalAmount());
                    rawRefundUploadOrder.setSpecNo(rawRefundOrder.getSpecNo());
                    rawRefundUploadOrder.setSpecId(rawRefundOrder.getSpecId());
                    rawRefundUploadOrder.setGoodsNo(rawRefundOrder.getGoodsNo());
                    rawRefundUploadOrder.setGoodsId(rawRefundOrder.getGoodsId());
                } else {
                    rawRefundUploadOrder.setRefundNo(refundOrderDetail.getRefundOrderNo());
                    rawRefundUploadOrder.setNum(
                            BigDecimal.valueOf(returnItemDetail.getRefundQuantity()));
                    rawRefundUploadOrder.setTid(returnItemDetail.getSrcOrderNo());
                    rawRefundUploadOrder.setOid(returnItemDetail.getSrcOrderDetailNo());
                    rawRefundUploadOrder.setType(refundOrderDetail.getType());
                    //【必填】平台状态 1取消退款,2已申请退款,3等待退货,4等待收货,5退款成功
                    rawRefundUploadOrder.setStatus(2);
                    rawRefundUploadOrder.setRefundVersion("1");
                    rawRefundUploadOrder.setRefundAmount(refundOrderDetail.getTotalRefundAmount());
                    rawRefundUploadOrder.setActualRefundAmount(returnItemDetail.getRefundAmount());
                    rawRefundUploadOrder.setTitle(
                            returnItemDetail.getItemName() + " " + returnItemDetail.getSpecName());
                    rawRefundUploadOrder.setLogisticsName(cmd.getReturnLogisticsName());
                    rawRefundUploadOrder.setLogisticsNo(cmd.getReturnLogisticsNo());
                    rawRefundUploadOrder.setBuyerNick(refundOrderDetail.getBuyerNickname());
                    rawRefundUploadOrder.setRefundTime(
                            DateUtil.format(refundOrderDetail.getCreateTime()));
                    rawRefundUploadOrder.setCurrentPhaseTimeout("");
                    rawRefundUploadOrder.setIsAftersale(refundOrderDetail.getType() != 1);
                    rawRefundUploadOrder.setReason("");
                }
                orderList.add(rawRefundUploadOrder);
            }
            rawRefundAPI.upload(refundOrderDetail.getShopNo(), orderList);
        } catch (WdtErpException e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, e.getMessage());
        }
        return Response.buildSuccess();
    }
}
