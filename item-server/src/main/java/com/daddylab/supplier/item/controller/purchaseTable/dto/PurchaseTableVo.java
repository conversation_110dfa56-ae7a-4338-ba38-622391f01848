package com.daddylab.supplier.item.controller.purchaseTable.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName PurchaseTableVo.java
 * @description
 * @createTime 2021年11月16日 21:36:00
 */
@Data
@ApiModel("采购权限返回实体")
public class PurchaseTableVo implements Serializable {

    private static final long serialVersionUID = 7342822060969780755L;
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 0:允许编辑 1:不允许编辑
     */
    @ApiModelProperty(value = "0:允许编辑 1:不允许编辑")
    private Integer isEdit;

    /**
     * 0:没有处理活动商品 1:处理过活动商品
     */
    @ApiModelProperty(value = "0:没有处理活动商品 1:处理过活动商品")
    private Integer isDeal;
}
