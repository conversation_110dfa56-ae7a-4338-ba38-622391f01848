package com.daddylab.supplier.item.application.purchase.order;

import com.actionsoft.sdk.service.model.TaskInstance;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.aws.AwsOpenApiClient;
import com.daddylab.supplier.item.application.aws.service.AwsProcessClient;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PurchaseOrderState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAwsBusinessLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseOrderService;
import com.daddylab.supplier.item.infrastructure.utils.ObjectUtil;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/1/4
 */
@Service
@Slf4j
@AllArgsConstructor
public class PurchaseOrderSummaryReminderTask {
    private final IPurchaseOrderService purchaseOrderService;
    private final IAwsBusinessLogService awsBusinessLogService;
    private final AwsOpenApiClient awsOpenApiClient;
    private final PurchaseOrderBizService purchaseOrderBizService;
    private final StaffService staffService;

    @XxlJob("PurchaseOrderSummaryReminderTask")
    public void doTask() {
        final List<PurchaseOrder> list =
                purchaseOrderService
                        .lambdaQuery()
                        .eq(PurchaseOrder::getState, PurchaseOrderState.WAIT_CHECK.getValue())
                        .list();
        final AwsProcessClient awsProcessClient = awsOpenApiClient.getAwsProcessClient();
        final ArrayList<TaskInstance> allTaskInstances = new ArrayList<>();
        final HashMap<String, PurchaseOrder> stringPurchaseOrderHashMap = new HashMap<>();
        for (PurchaseOrder purchaseOrder : list) {
            final String processInstId =
                    awsBusinessLogService.getProcessIdByBusinessId(
                            PurchaseTypeEnum.PURCHASE_ORDER, purchaseOrder.getId());
            if (ObjectUtil.isEmpty(processInstId)) {
                continue;
            }
            final List<TaskInstance> taskInstances = awsProcessClient.taskQuery(processInstId);
            allTaskInstances.addAll(taskInstances);
            for (TaskInstance taskInstance : taskInstances) {
                stringPurchaseOrderHashMap.put(taskInstance.getId(), purchaseOrder);
            }
        }
        log.info("【采购单待办汇总提醒】待提醒任务共计:{}", allTaskInstances.size());
        if (!allTaskInstances.isEmpty()) {
            final Map<String, List<TaskInstance>> taskGroup =
                    allTaskInstances.stream()
                            .collect(Collectors.groupingBy(TaskInstance::getTarget));
            log.info("【采购单待办汇总提醒】按照办理人汇总 {} {}", taskGroup.keySet().size(), taskGroup.keySet());
            for (Map.Entry<String, List<TaskInstance>> group : taskGroup.entrySet()) {
                final Optional<StaffBrief> staffOptional =
                        staffService.getStaffByLoginName(group.getKey());
                if (!staffOptional.isPresent()) {
                    log.warn("【采购单待办汇总提醒】查询员工数据异常:{}", group.getKey());
                    continue;
                }
                final StaffBrief staffBrief = staffOptional.get();
                final Set<PurchaseOrder> purchaseOrders = new LinkedHashSet<>();
                for (TaskInstance taskInstance : group.getValue()) {
                    final PurchaseOrder purchaseOrder =
                            stringPurchaseOrderHashMap.get(taskInstance.getId());
                    purchaseOrders.add(purchaseOrder);
                }
                final List<Long> purchaseOrderIds =
                        purchaseOrders.stream()
                                .map(PurchaseOrder::getId)
                                .distinct()
                                .collect(Collectors.toList());
                purchaseOrderBizService.toDoSummaryReminder(
                        staffBrief.getQwUserId(), purchaseOrderIds);
                log.info("【采购单待办汇总提醒】发送成功:{} 采购单:{}", group.getKey(), purchaseOrderIds);
            }
        }
        log.info("【采购单待办汇总提醒】END");
    }
}
