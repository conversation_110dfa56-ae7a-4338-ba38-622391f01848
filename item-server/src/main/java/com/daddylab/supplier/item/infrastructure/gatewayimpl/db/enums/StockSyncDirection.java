package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/3/22
 */
@AllArgsConstructor
@Getter
public enum StockSyncDirection implements IIntegerEnum {
    DOWNLOAD(0, "下载"),
    UPLOAD(1, "上传"),
    ;
    private final Integer value;
    private final String desc;
}
