package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtGoodsSn;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtGoodsSnMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtGoodsSnService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ERP序列号管理页面的数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
@Service
public class WdtGoodsSnServiceImpl extends DaddyServiceImpl<WdtGoodsSnMapper, WdtGoodsSn> implements IWdtGoodsSnService {

}
