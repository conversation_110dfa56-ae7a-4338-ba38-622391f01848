package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.application.salesOutStock.dto.*;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 旺店通销售出库单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@DS("master")
public interface WdtSaleStockOutOrderMapper extends DaddyBaseMapper<WdtSaleStockOutOrder> {

    List<WdtSaleStockOutOrder> selectByStockOutOrderNo(@Param("stockOutOrderNo") String stockOutOrderNo);

    /**
     * 库存管理-销售出库。列表查询
     *
     * @param query
     * @return
     */
    List<SalesOutStockPageVO> selectSalesOutStockPage(@Param("query") SalesOutStockPageQuery query);

    Integer countSalesOutStockPage(@Param("query") SalesOutStockPageQuery query);

    /**
     * 模糊查询物流公司信息
     *
     * @param query
     * @return
     */
    List<String> selectLogisticsName(@Param("param") LogisticsNamePageQuery query);

    /**
     * 查询销售出库单的基础信息
     *
     * @param id
     * @return
     */
    SalesOutStockDetailBaseVO selectSalesOutStockBaseInfo(@Param("stockoutId") Long stockoutId);

    List<SalesOutStockDetailListVO> selectSalesOutStockDetailList(@Param("stockoutId") Long stockoutId);


}
