package com.daddylab.supplier.item.application.item.tasks;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerChangeEnum;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil.ChangePropertyObj;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.google.common.collect.ImmutableList;
import java.io.InputStream;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MultiMapUtils;
import org.apache.commons.collections4.SetValuedMap;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/8/17
 */
@Component
@Slf4j
public class ImportItemStdNameTask {

    @Data
    @HeadRowHeight(20)
    @ContentRowHeight(16)
    @EqualsAndHashCode(of = {"skuCode"})
    public static class ImportRow implements Serializable {

        private static final long serialVersionUID = -752114031599220390L;

        @ExcelProperty(value = "商品SKU")
        @ColumnWidth(50)
        private String skuCode;

        @ExcelProperty(value = "名称")
        @ColumnWidth(50)
        private String name;
    }


    private final IItemSkuService itemSkuService;
    private final IItemDrawerService itemDrawerService;

    private final OperateLogDomainService operateLogDomainService;

    public ImportItemStdNameTask(IItemSkuService itemSkuService,
            IItemDrawerService itemDrawerService,
            OperateLogDomainService operateLogDomainService) {
        this.itemDrawerService = itemDrawerService;
        this.itemSkuService = itemSkuService;
        this.operateLogDomainService = operateLogDomainService;
    }

    public void importExcel(InputStream inputStream, Integer sheetNo) {
        List<ImportRow> sheetRowsRaw = EasyExcel.read(inputStream)
                .headRowNumber(1)
                .head(ImportRow.class).sheet(sheetNo).doReadSync();
        final List<ImportRow> sheetRows = sheetRowsRaw.stream().distinct()
                .collect(Collectors.toList());

        log.info("开始处理商品标准名导入，总计:{}", sheetRows.size());

        final SetValuedMap<String, Object> runStat = MultiMapUtils.newSetValuedHashMap();

        for (int i = 0; i < sheetRows.size(); i++) {
            final ImportRow sheetRow = sheetRows.get(i);
            if (Objects.isNull(sheetRow.getSkuCode()) && Objects.isNull(sheetRow.getName())) {
                continue;
            }

            final String skuCode = sheetRow.getSkuCode();
            if (StringUtil.isBlank(skuCode)) {
                log.error("第{}行规格编码为空", i + 2);
                runStat.put("规格编码为空", skuCode);
                continue;
            }
            final String name = sheetRow.getName();
            if (StringUtil.isBlank(name)) {
                log.error("导入名称为空 规格编码:{}", skuCode);
                runStat.put("导入名称为空", skuCode);
                continue;
            }
            final ItemSku itemSku = itemSkuService.lambdaQuery()
                    .eq(ItemSku::getSkuCode, skuCode).oneOpt().orElse(null);
            if (itemSku == null) {
                log.error("规格编码无效:{}", skuCode);
                runStat.put("规格编码无效", skuCode);
                continue;
            }
            final Long itemId = itemSku.getItemId();
            final ItemDrawer itemDrawer = itemDrawerService.lambdaQuery()
                    .eq(ItemDrawer::getItemId, itemId)
                    .oneOpt().orElseGet(() -> {
                        final ItemDrawer newItemDrawer = new ItemDrawer();
                        newItemDrawer.setItemId(itemId);
                        return newItemDrawer;
                    });
            if (StringUtil.isBlank(itemDrawer.getStandardName())) {
                itemDrawer.setStandardName(name);
                itemDrawerService.saveOrUpdate(itemDrawer);
                operateLogDomainService.addOperatorLog(0L, OperateLogTarget.ITEM_DRAWER,
                        itemDrawer.getId(), "系统导入商品标准名", ImmutableList.of(new ChangePropertyObj(
                                ItemDrawerChangeEnum.STANDARD_NAME.getDesc(), null, name)));
                log.info("商品标准名已导入 商品ID:{} 标准名:{}", itemId, name);
                runStat.put("商品标准名已导入", itemId);
            } else {
                log.info("商品标准名无需处理 商品ID:{}", itemId);
                runStat.put("商品标准名无需处理", itemId);
            }
        }
        log.info("商品标准名导入处理完成 {}", runStat);
        MDC.clear();
    }

}
