package com.daddylab.supplier.item.controller.handingsheet.dto;


import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

import static com.fasterxml.jackson.annotation.JsonProperty.Access.READ_ONLY;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/24 15:59
 * @Description: 请描述下这个类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("盘货表关联商品分页查询参数")
public class HandingSheetItemPageQuery extends PageQuery {
    private static final long serialVersionUID = -3631325046039504947L;
    @ApiModelProperty(value = "盘货表主键ID")
    private Long handingSheetId;

    @ApiModelProperty(value = "是否为审核查看", notes = "（v2版本废弃）")
    private Boolean queryForAudit;

    @ApiModelProperty(value = "刷新价格", notes = "v2")
    private Boolean refreshPrice;

    @ApiModelProperty(value = "状态", notes = "待确认还是已确认（v2版本新增）")
    private Boolean isPassed;

    @ApiModelProperty(value = "采购员", notes = "（v2版本新增）")
    private Long buyerUserId;

    @ApiModelProperty(value = "是否拥有全部数据权限", hidden = true)
    @JsonProperty(access = READ_ONLY)
    private boolean hasAllDataPermission;

    @ApiModelProperty(value = "规格编码")
    private List<String> skuCodes;

    public void validate() {
        if (handingSheetId == null && (skuCodes == null || skuCodes.isEmpty())) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "盘货表查询，查询条件范围过大");
        }
    }
}
