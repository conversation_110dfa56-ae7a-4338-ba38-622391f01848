package com.daddylab.supplier.item.application.message.wechat;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.controller.item.dto.ItemBuyerDto;
import com.daddylab.supplier.item.domain.item.gateway.ItemProcurementGateway;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemLaunchPlanMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerAuditTaskState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;

/**
 * <AUTHOR> up
 * @date 2022年11月14日 9:46 AM
 */
@Component
@Slf4j
public class MsgBodyUtil {

    @Resource
    ItemMapper itemMapper;

    @Resource
    IItemService iItemService;

    @Resource
    ItemLaunchPlanMapper itemLaunchPlanMapper;

    @Resource
    INewGoodsService iNewGoodsService;

    @Resource
    StaffService staffService;

    @Resource
    IItemLaunchPlanService iItemLaunchPlanService;

    @Resource
    IItemSkuService iItemSkuService;

    @Resource
    ItemProcurementGateway itemProcurementGateway;

    @Resource
    IItemDrawerModuleAuditTaskService iItemDrawerModuleAuditTaskService;


    public MsgBodyBO getMsgBody(MsgEventType msgEventType,
                                List<Long> itemIdList,
                                MsgEvent.ItemLaunchPlaneOperator operator,
                                MsgEvent.PrincipalChangeInfo changeInfo,
                                String liveVerbalTrickName) {
        switch (msgEventType) {
            //  「待完善」-「待设计」,产品负责人
            case IMPROVE_TO_DESIGN:
                return improveToDesign(itemIdList);
            //  「待设计」-「法务审核」,法务
            case DESIGN_TO_AUDIT_LEGAL:
                return designToAuditLegal(itemIdList);
            //  「法务审核」- 「QC审核」,QC负责人
            case DESIGN_TO_AUDIT_QC:
                return designToAuditQc(itemIdList);
            // 待审核 - 待修改。产品负责人+小高。
            case AUDIT_TO_MODIFY:
                return auditToModify(itemIdList);
            // 上新计划完成提交。产品组+设计组
            case PLAN_SUBMIT:
                return planSubmit(operator);
            case PLAN_INFO_CHANGE:
                return planInfoChange(operator);
            //  上架后某一字段发生变更,采购+QC+产品负责人+平台运营
            case ONE_FIELD_CHANGE:
                return oneFieldChange(itemIdList);
            //  修改产品负责人
            case CHARGER_CHANGE:
                return principalChange(itemIdList, changeInfo);
            case LIVE_SPEECH:
                return livaSpeech(itemIdList, liveVerbalTrickName);
            case LIVE_SPEECH_AUDIT_LEGAL:
                return livaSpeechSubmit(itemIdList, liveVerbalTrickName);
            case LIVE_SPEECH_AUDIT_QC:
                return liveSpeechAuditQc(itemIdList, liveVerbalTrickName);
            case SHELF_ITEM_INFO_CHANG:
                return shelfItemInfoChange(itemIdList, changeInfo);
            default:
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "非法企微事件触发类型");
        }
    }

    private String getItemLaunchTime(Long itemId) {
        if (Objects.isNull(itemId)) {
            return "未知日期";
        }
        ItemLaunchPlan planByItemId = itemLaunchPlanMapper.getPlanByItemId(itemId);
        if (Objects.isNull(planByItemId)) {
            return "未知日期";
        }
        Long launchTime = planByItemId.getLaunchTime();
        return DateUtil.parseTimeStamp(launchTime, "yyyy-MM-dd");
    }

    private String getItemName(Long itemId) {
        if (Objects.isNull(itemId)) {
            return "未知商品名称";
        }
        Item item = iItemService.getById(itemId);
        if (Objects.isNull(item)) {
            return "未知商品名称";
        } else {
            return item.getName();
        }
    }

    private String getNickNameByUserId(Long userId) {
        String nickName;
        DadStaffVO staff = staffService.getStaff(userId);
        if (Objects.isNull(staff)) {
            nickName = "无名氏";
        } else {
            if (StrUtil.isBlank(staff.getNickname())) {
                nickName = "花名不存在";
            } else {
                nickName = staff.getNickname();
            }
        }
        return nickName;
    }

    private Map<Long, String> getNickNameByUserIds(List<Long> userIdList) {
        List<DadStaffVO> staffList = staffService.getStaffList(userIdList);
        if (CollUtil.isEmpty(staffList)) {
            return new HashMap<>();
        }
        return staffList.stream().collect(Collectors.toMap(DadStaffVO::getUserId, DadStaffVO::getNickname));
    }


    /**
     * title:「采购负责人花名」已完善「商品名称」商品信息，请确认并准备物料
     * content: 上新日期「上新计划日期」
     *
     * @return
     */
    private MsgBodyBO improveToDesign(List<Long> itemIds) {
        String buyerNickName, itemName;

        itemName = getItemName(itemIds.get(0));

        List<ItemBuyerDto> itemBuyerDtoList = itemMapper.selectBatchBuyersByItemIds(itemIds);
        if (CollUtil.isEmpty(itemBuyerDtoList)) {
            buyerNickName = "未知采购员";
        } else {
            buyerNickName = itemBuyerDtoList.get(0).getBuyerUserName();
        }

        String launchTime = getItemLaunchTime(itemIds.get(0));

        MsgBodyBO bo = new MsgBodyBO();
        bo.setTitle(String.format("%s已完善%s商品信息，请确认并准备物料", buyerNickName, itemName));
        bo.setContent(String.format("上新日期%s", launchTime));
        return bo;
    }

    /**
     * 「产品负责人花名」已提交「商品名称」信息审核，请认领后处理
     * 上新日期「上新计划日期」
     *
     * @param itemIds
     * @return
     */
    private MsgBodyBO designToAuditLegal(List<Long> itemIds) {
        String produceNickName;

        List<NewGoods> newGoodsList = iNewGoodsService.lambdaQuery().eq(NewGoods::getItemId, itemIds.get(0)).select().list();
        if (CollUtil.isEmpty(newGoodsList)) {
            produceNickName = "未知产品负责人";
        } else {
            Long principalId = newGoodsList.get(0).getPrincipalId();
            DadStaffVO staff = staffService.getStaff(principalId);
            produceNickName = staff.getNickname();
        }

        MsgBodyBO bo = new MsgBodyBO();
        bo.setTitle(String.format("%s已提交%s信息审核，请认领后处理", produceNickName, getItemName(itemIds.get(0))));
        bo.setContent(String.format("上新日期%s", getItemLaunchTime(itemIds.get(0))));
        return bo;
    }

    /**
     * 「法务负责人花名」已流转「商品名称」信息审核，请查看后处理
     * 上新日期「上新计划日期」
     *
     * @param itemIds
     * @return
     */
    private MsgBodyBO designToAuditQc(List<Long> itemIds) {
        String legalNickName;

        List<NewGoods> newGoodsList = iNewGoodsService.lambdaQuery().eq(NewGoods::getItemId, itemIds.get(0)).select().list();
        if (CollUtil.isEmpty(newGoodsList)) {
            legalNickName = "未知法务";
        } else {
            Long legalId = newGoodsList.get(0).getLegalId();
            DadStaffVO staff = staffService.getStaff(legalId);
            legalNickName = Objects.isNull(staff) ? "未知法务" : staff.getNickname();
        }

        MsgBodyBO bo = new MsgBodyBO();
        bo.setTitle(String.format("%s已流转%s信息审核，请查看后处理", legalNickName, getItemName(itemIds.get(0))));
        bo.setContent(String.format("上新日期%s", getItemLaunchTime(itemIds.get(0))));
        return bo;
    }

    /**
     * 「商品名称」已产出修改意见，请修改后重新维护资料
     * 上新日期「上新计划日期」
     *
     * @param itemIds
     * @return
     */
    private MsgBodyBO auditToModify(List<Long> itemIds) {
        MsgBodyBO bo = new MsgBodyBO();
        bo.setTitle(String.format("%s已产出修改意见，请修改后重新维护资料", getItemName(itemIds.get(0))));
        bo.setContent(String.format("上新日期%s", getItemLaunchTime(itemIds.get(0))));
        return bo;
    }

    /**
     * 提交人花名」已提交「上新活动名称（上新日期）」设计文案
     * 请点击查看
     *
     * @return
     */
    private MsgBodyBO planSubmit(MsgEvent.ItemLaunchPlaneOperator operator) {
        MsgBodyBO bo = new MsgBodyBO();
        bo.setTitle(getPlanTitle("%s已提交%s（%s）设计文案", getNickNameByUserId(operator.getOperateUserId()), operator.getPlanId()));
        bo.setContent("请点击查看");
        return bo;
    }

    private String getPlanTitle(String format, String name, Long planId) {
        String planName, planTime;
        ItemLaunchPlan itemLaunchPlan = iItemLaunchPlanService.getById(planId);
        if (Objects.isNull(itemLaunchPlan)) {
            planName = "未知上新活动";
            planTime = "未知上新日期";
        } else {
            planName = itemLaunchPlan.getPlanName();
            planTime = Objects.nonNull(itemLaunchPlan.getLaunchTime()) ?
                    DateUtil.parseTimeStamp(itemLaunchPlan.getLaunchTime(), "yyyy-MM-dd")
                    : "不存在上新日期";
        }
        return String.format(format, name, planName, planTime);
    }


    /**
     * 「修改人花名」修改了「上新活动名称（上新日期）」设计文案
     * <p>
     * 「商品名称」的「首页文案/上新价」修改为「修改后文案」
     * 「商品名称」的「首页文案/上新价」修改为「修改后文案」
     *
     * @param operator
     * @return
     */
    private MsgBodyBO planInfoChange(MsgEvent.ItemLaunchPlaneOperator operator) {
        MsgBodyBO bo = new MsgBodyBO();
        bo.setTitle(getPlanTitle("%s已修改了%s（%s）设计文案", getNickNameByUserId(operator.getOperateUserId()), operator.getPlanId()));
        bo.setContent(operator.getChangeLog());
        return bo;
    }

    /**
     * 共「9」款商品上架后信息发生变化，点击查看
     * 共「9」款商品「23」个sku变化
     *
     * @param itemIds
     * @return
     */
    private MsgBodyBO oneFieldChange(List<Long> itemIds) {
        MsgBodyBO bo = new MsgBodyBO();
        bo.setTitle(String.format("共%d款商品上架后信息发生变化，点击查看", itemIds.size()));

        Integer count = iItemSkuService.lambdaQuery().in(ItemSku::getItemId, itemIds).select().count();
        bo.setContent(String.format("共%d款商品%d个sku变化", itemIds.size(), count));
        return bo;
    }

    /**
     * 修改者」已将「商品名称商品名称…」等「3」款商品负责人修改为你
     * 点击查看
     *
     * @param itemIdList
     * @param changeInfo
     * @return
     */
    private MsgBodyBO principalChange(List<Long> itemIdList, MsgEvent.PrincipalChangeInfo changeInfo) {
        DadStaffVO staff = staffService.getStaff(changeInfo.getChangeOperateUserId());
        String nickname = staff.getNickname();
        String itemNames = iItemService.lambdaQuery().in(Item::getId, itemIdList).select().list()
                .stream().map(Item::getName).collect(Collectors.joining(","));

        MsgBodyBO bo = new MsgBodyBO();
        bo.setTitle(String.format("%s已将%s等%d款商品负责人修改为你", nickname, itemNames, itemIdList.size()));
        bo.setContent("点击查看");
        return bo;
    }

    /**
     * 拼接直播话术通过审核时，发送的信息
     * 「商品名称」直播话术已被审核，点击查看
     * 法务审核人：XX  技术审核人：XX
     *
     * @param itemIdList
     * @param liveVerbalTrickName
     * @return
     */
    private MsgBodyBO livaSpeech(List<Long> itemIdList, String liveVerbalTrickName) {
        Long itemId = itemIdList.get(0);

        String legalNickName = getLegalNickName(itemId);
        String qcNickName = "未知QC";
        ItemProcurement itemProcurement = itemProcurementGateway.getItemProcurement(itemId);
        if (Objects.nonNull(itemProcurement)) {
            String qcIds = itemProcurement.getQcIds();
            if (isNotBlank(qcIds)) {
                List<Long> qcIdList = Arrays.stream(qcIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
                Map<Long, String> nickNameByUserIds = getNickNameByUserIds(qcIdList);
                qcNickName = StrUtil.join(",", nickNameByUserIds.values());
            }
        }

        Item item = iItemService.getById(itemId);
        String itemName = Objects.isNull(item) ? "未知商品（id：" + itemId + "）" : item.getName();

        MsgBodyBO bo = new MsgBodyBO();
        bo.setTitle(StrUtil.format("{}直播话术（{}）已被审核，点击查看", itemName, liveVerbalTrickName));
        bo.setContent(StrUtil.format("法务审核人：{}  技术审核人：{}", legalNickName, qcNickName));
        return bo;
    }

    private String getLegalNickName(Long itemId) {
        String legalNickName = "未知法务";
        List<NewGoods> newGoodsList = iNewGoodsService.lambdaQuery().eq(NewGoods::getItemId, itemId).select().list();
        if (CollUtil.isNotEmpty(newGoodsList)) {
            Long legalId = newGoodsList.get(0).getLegalId();
            DadStaffVO staff = staffService.getStaff(legalId);
            legalNickName = Objects.isNull(staff) ? "未知法务" : staff.getNickname();
        }
        return legalNickName;
    }

    /**
     * 直播话术审核提交
     * 「提交人花名」已提交「商品名称」直播话术，请查看后处理
     * 上新日期「上新计划日期」
     *
     * @param itemIdList
     * @param liveVerbalTrickName
     * @return
     */
    private MsgBodyBO livaSpeechSubmit(List<Long> itemIdList, String liveVerbalTrickName) {
        String submitName = getLiveSpeechSubmitName(itemIdList);
        String itemName = "商品名称";
        String launchTime = "未知上新日期";
        if (CollUtil.isNotEmpty(itemIdList)) {
            Long itemId = itemIdList.get(0);
            Item item = iItemService.getById(itemId);
            itemName = Objects.isNull(item) ? "未知商品（id：" + itemId + "）" : item.getName();

            ItemLaunchPlan planByItemId = itemLaunchPlanMapper.getPlanByItemId(itemId);
            if (Objects.nonNull(planByItemId)) {
                launchTime = DateUtil.parseTimeStamp(planByItemId.getLaunchTime(), DateUtil.DEFAULT_FORMAT);
            } else {
                launchTime = "商品已从上新计划中移除";
            }
        }

        MsgBodyBO bo = new MsgBodyBO();
        bo.setTitle(StrUtil.format("{}已提交{}直播话术（{}），请查看后处理", submitName, itemName, liveVerbalTrickName));
        bo.setContent(StrUtil.format("上新日期:{}", launchTime));
        return bo;
    }

    private String getLiveSpeechSubmitName(List<Long> itemIdList) {
        String submitName = "未知提交人";
        if (CollUtil.isNotEmpty(itemIdList)) {
            Long itemId = itemIdList.get(0);
            List<ItemDrawerModuleAuditTask> list = iItemDrawerModuleAuditTaskService.lambdaQuery()
                    .eq(ItemDrawerModuleAuditTask::getItemId, itemId)
                    .eq(ItemDrawerModuleAuditTask::getType, ItemAuditType.LIVE_VERBAL_TRICK)
                    .eq(ItemDrawerModuleAuditTask::getAuditStatus, ItemDrawerAuditTaskState.PENDING)
                    .orderByDesc(ItemDrawerModuleAuditTask::getId).list();
            if (CollUtil.isNotEmpty(list)) {
                Long createdUid = list.get(0).getCreatedUid();
                if (Objects.nonNull(createdUid)) {
                    submitName = getNickNameByUserId(createdUid);
                }
            }
        }
        return submitName;
    }

    /**
     * 「法务负责人花名」已流转「商品名称」直播话术，请查看后处理
     * 话术提交人：「提交人花名」
     *
     * @return
     */
    private MsgBodyBO liveSpeechAuditQc(List<Long> itemIdList, String liveVerbalTrickName) {
        if (CollUtil.isEmpty(itemIdList)) {
            return new MsgBodyBO();
        }
        Long itemId = itemIdList.get(0);
        String legalNickName = getLegalNickName(itemId);
        Item item = iItemService.getById(itemId);
        String itemName = Objects.isNull(item) ? "未知商品（id：" + itemId + "）" : item.getName();
        String liveSpeechSubmitName = getLiveSpeechSubmitName(itemIdList);

        MsgBodyBO msgBodyBO = new MsgBodyBO();
        msgBodyBO.setTitle(StrUtil.format("{}已流转{}直播话术（{}），请查看后处理", legalNickName, itemName, liveVerbalTrickName));
        msgBodyBO.setContent(StrUtil.format("话术提交人：{}", liveSpeechSubmitName));
        return msgBodyBO;
    }

    private MsgBodyBO shelfItemInfoChange(List<Long> itemIdList, MsgEvent.PrincipalChangeInfo changeInfo) {
        if (CollUtil.isEmpty(itemIdList)) {
            return new MsgBodyBO();
        }

        Long itemId = itemIdList.get(0);
        Item item = iItemService.getById(itemId);
        Integer launchStatus = item.getLaunchStatus();
        String status = launchStatus.equals(ItemLaunchStatus.TO_BE_RELEASED.getValue()) ? "待上架" : "已上架";

        Long changeOperateUserId = changeInfo.getChangeOperateUserId();
        String nickName = getNickNameByUserId(changeOperateUserId);

        MsgBodyBO msgBodyBO = new MsgBodyBO();
        msgBodyBO.setTitle(StrUtil.format("{}修改了【{}】商品【{}】{} 信息",
                nickName, status, item.getCode(), changeInfo.getChangeLog()));
        msgBodyBO.setContent(StrUtil.format("请点击查看"));
        return msgBodyBO;
    }

}
