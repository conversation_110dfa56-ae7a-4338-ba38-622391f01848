package com.daddylab.supplier.item.controller.stock;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.stock.SysInventorySettingBizService;
import com.daddylab.supplier.item.types.stock.SysInventorySettingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/3/1
 */
@RestController
@RequestMapping("/system/inventory-setting/")
@Api(value = "系统设置/库存更新设置", tags = "系统设置/库存更新设置")
public class SysInventorySettingController {
    @Resource
    SysInventorySettingBizService sysInventorySettingBizService;

    @ApiOperation(value = "保存")
    @PostMapping(value = "/save")
    public Response save(@Validated @RequestBody SysInventorySettingVO vo) {
        return sysInventorySettingBizService.save(vo);
    }

    @ApiOperation(value = "查看")
    @PostMapping(value = "/view")
    public SingleResponse<SysInventorySettingVO> view() {
        return sysInventorySettingBizService.view();
    }
}
