package com.daddylab.supplier.item.application.salesInStock.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2023年10月23日 11:25 AM
 */
@Data
public class SalesInStockBaseVO {

    @ApiModelProperty("入库单号")
    private String orderNo;

    @ApiModelProperty("类型。1:退款;2:退货;3:换货;4:退款不退货")
    private Integer type;

    @ApiModelProperty("实际入库仓")
    private String warehouseName;

    @ApiModelProperty("入库人")
    private String operatorName;

    @ApiModelProperty("店铺")
    private String shopName;

    @ApiModelProperty("制单时间")
    private String createdTime;

    @ApiModelProperty("客户昵称")
    private String nickName;

    @ApiModelProperty("审核人")
    private String checkName;

    @ApiModelProperty("审核时间")
    private String checkTime;

    @ApiModelProperty("备注")
    private String remark;

}
