package com.daddylab.supplier.item.application.provider;

import cn.hutool.core.collection.CollUtil;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.controller.provider.dto.PartnerProviderPageQuery;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IProviderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/10/10
 */
@Component
@AllArgsConstructor
@Slf4j
public class SyncPsysMallShopIdJob {
    private final ProviderGateway providerGateway;
    private final IProviderService providerService;

    @XxlJob("SyncPsysMallShopIdJob")
    public void execute() {
        final String job = "SyncPsysMallShopIdJob";
        final List<Provider> list =
                providerService.lambdaQuery().eq(Provider::getMallShopId, "0").list();
        log.info("【{}】正在同步 MallShopId，待同步的供应商数量={}", job, list.size());
        int count = 0;
        for (Provider provider : list) {
            log.debug("【{}】正在同步 MallShopId，当前正在同步={}", job, provider.getName());
            final PartnerProviderPageQuery partnerPageQuery = new PartnerProviderPageQuery();
            partnerPageQuery.setName(provider.getName());
            final List<PartnerProviderResp> partnerProviderResps =
                    providerGateway.partnerQuery(partnerPageQuery);
            log.debug(
                    "【{}】正在同步 MallShopId，P系统查询结果={}，供应商名称={}",
                    job,
                    partnerProviderResps,
                    provider.getName());
            if (CollUtil.isEmpty(partnerProviderResps)) {
                log.debug("【{}】正在同步 MallShopId，P系统查询为空，供应商名称={}", job, provider.getName());
                continue;
            }
            final Optional<PartnerProviderResp> partnerProviderRespOpt =
                    partnerProviderResps.stream()
                            .filter(v -> v.getShopId() != null && v.getShopId() > 0)
                            .findFirst();
            if (!partnerProviderRespOpt.isPresent()) {
                log.debug("【{}】正在同步 MallShopId，P系统查询结果无店铺ID，供应商名称={}", job, provider.getName());
                continue;
            }
            final PartnerProviderResp partnerProviderResp = partnerProviderRespOpt.get();
            final Long partnerProviderId = provider.getPartnerProviderId();
            if (partnerProviderId != null
                    && partnerProviderId != 0
                    && !partnerProviderId.equals((long) partnerProviderResp.getId())) {
                log.debug("【{}】正在同步 MallShopId，供应商名称={}，P系统供应商ID不匹配", job, provider.getName());
                continue;
            }
            final Provider providerToUpdate = new Provider();
            providerToUpdate.setId(provider.getId());
            providerToUpdate.setMallShopId(String.valueOf(partnerProviderResp.getShopId()));
            providerToUpdate.setPartnerProviderId((long) partnerProviderResp.getId());
            // providerToUpdate.setUnifySocialCreditCodes(partnerProviderResp.getOrganizationNo());
            providerService.updateById(providerToUpdate);
            count++;
            log.info(
                    "【{}】成功同步 MallShopId，provider={}({})，mallShopId={}",
                    job,
                    provider.getName(),
                    provider.getId(),
                    providerToUpdate.getMallShopId());
        }
        log.info("【{}】MallShopId 同步完成，成功数量={}", job, count);
    }
}
