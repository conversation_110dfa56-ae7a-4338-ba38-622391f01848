package com.daddylab.supplier.item.application.payment.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年11月21日 10:59 AM
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("付款申请单保存编辑参数")
@Data
public class PaymentApplyOrderSaveCmd extends Command {


    private static final long serialVersionUID = -3588691127665500246L;

    @ApiModelProperty("付款申请单ID")
    private Long id;

    @ApiModelProperty("合作模式")
    private Integer businessLine;

    @ApiModelProperty("采购类型。0：标准采购，1：工厂代发")
    private Integer purchaseType;

    @ApiModelProperty("币别。0：人命币，1：美元")
    private Integer currencyType;

    @ApiModelProperty("采购组织。填103，杭州老爸电商科技有限公司")
    private String purchaseOrg;

    @ApiModelProperty("付款组织。填103，杭州老爸电商科技有限公司")
    private String payOrg;

    @ApiModelProperty("往来单位，供应商ID")
    private Long tradeUnit;

    @ApiModelProperty("收款单位，供应商ID")
    private Long payeeUnit;

    @ApiModelProperty("收款单位银行账号")
    private String payeeBankCardNo;

    @ApiModelProperty("收款单位开户行行号")
    private String payeeBankNo;

    @ApiModelProperty("收款单位开户行")
    private String payeeBank;

    @ApiModelProperty("采购员用户ID")
    @NotNull(message = "采购员用户ID不能为空，请选择采购员")
    private Long buyerId;

    @ApiModelProperty("期望付款日期，10位时间戳")
    private Long expectedPayTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("付款类型。0：采购付款（默认），1：预付款")
    private Integer payPurpose;

    @ApiModelProperty("付款比例。去掉%之后的整数，0～100")
    private Integer payProportions;

    @ApiModelProperty("其他金额")
    private BigDecimal otherChargeback;

    @ApiModelProperty("总申请付款金额")
    private BigDecimal totalApplyAmount;

    @ApiModelProperty("实付金额")
    private BigDecimal realPayAmount;

    @ApiModelProperty("附件IDList")
    private List<Long> additionalId;

    @ApiModelProperty("付款明细来源。0：采购单，1：结算单")
    private Integer detailSource;

    // --------------- 付款明细 请求参数 ----------------

    @ApiModelProperty("付款明细列表请求参数")
    List<PaymentApplyOrderDetailSaveCmd> detailSaveCmdList;


}
