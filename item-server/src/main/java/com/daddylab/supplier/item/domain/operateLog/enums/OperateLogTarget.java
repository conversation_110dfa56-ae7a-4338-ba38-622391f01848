package com.daddylab.supplier.item.domain.operateLog.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum OperateLogTarget implements IEnum<Integer> {
  /*
   消除黄线
  */
  ITEM(1, "商品"),
  BRAND(2, "品牌"),
  PROVIDER(3, "供应商"),
  SHOP(4, "店铺"),
  PURCHASE(5, "采购"),
  COMBINATION_ITEM(6, "组合商品"),

  OTHER_PAY(7, "其他应付单"),
  PURCHASE_ORDER(8, "采购订单"),
  STOCK_ORDER(9, "采购入库"),
  STOCK_OUT_ORDER(10, "退料出库"),
  NEW_GOODS(11, "新品商品SKU"),
  ITEM_DRAWER(12, "商品抽屉"),
  NEW_GOODS_SPU(13, "新品商品SPU"),
  HANDING_SHEET_ITEM_PRICE(14, "盘货表商品价格"),

  ITEM_PRICE(15, "商品价格"),

  // 检测到商品存在在售平台商品，系统自动将商品状态修改为【在售中】
  ITEM_UP(16, "商品上架"),
  // 商品状态被手动修改为【已下架（永久）】和【已废弃】
  ITEM_DOWN(17, "商品下架"),
  // 商品因无在售平台商品，系统自动将状态设置为【待上架】，这种动作业务上定义为【商品临时下架】
  ITEM_DOWN_TEMPORARY(18, "商品临时下架"),
  // 暂未分类的其他商品状态变化
  ITEM_STATUS(19, "商品状态"),

  COMBINATION_ITEM_PRICE(61, "组合商品价格记录"),

  ORDER_SETTLEMENT(20, "订单结算"),
  ITEM_OPTIMIZE_PLAN(21, "商品优化计划"),
  ITEM_OPTIMIZE(22, "商品优化"),

  PAYMENT_APPLY_ORDER(23, "采购管理，付款申请单"),
  WDT_STOCK_SPEC(24, "旺店通库存管理"),

  HANDING_SHEET(24, "盘货表"),
  HANDING_SHEET_ITEM(25, "盘货表商品"),
  HANDING_SHEET_ITEM_SPU(26, "盘货表商品SPU"),

  VIRTUAL_WAREHOUSE(27, "虚拟仓"),
  PLATFORM_ITEM(28, "平台商品"),
  ITEM_TRAINING_MATERIALS(29, "商品培训资料"),

  AFTER_SALE_FORWARDING(30, "售后转寄"),

  AFTER_SALE_SHARE_LINK(31, "售后管理-链接授权"),

  NEW_GOODS_PRICE(32, "新品商品价格"),
  OFF_SHELF(33, "商品下架流程"),

  WAREHOUSE(34, "仓库操作日志"),

  INVENTORY_ALLOC_SHOP(35,"店铺库存更新设置"),
  ;

  private final Integer value;
  private final String desc;
}
