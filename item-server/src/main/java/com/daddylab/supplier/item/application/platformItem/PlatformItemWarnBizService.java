package com.daddylab.supplier.item.application.platformItem;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.platformItem.data.PlatformItemWarnListItem;
import com.daddylab.supplier.item.application.platformItem.query.PlatformItemWarnPageQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/27
 */
public interface PlatformItemWarnBizService {

    PageResponse<PlatformItemWarnListItem> pageQuery(PlatformItemWarnPageQuery pageQuery);

    Response handle(Long id);

    void autoHandleWarn();

    /**
     * 平台商品 商家编码回传
     *
     * @param id     平台商品ID
     * @param specNo 商家编码
     * @return 响应 成功与否
     */
    Response uploadPlatformSpecNo(Long id, String specNo);

    Response handleBatch(List<Long> ids);
}
