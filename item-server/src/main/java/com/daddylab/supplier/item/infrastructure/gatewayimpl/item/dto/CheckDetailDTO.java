package com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Class  CheckInfo
 *
 * @Date 2022/6/3上午11:19
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class CheckDetailDTO {

    @JsonProperty("id")
    private Long id;
    @JsonProperty("item_id")
    private Long itemId;
    @JsonProperty("item_name")
    private String itemName;
    @JsonProperty("item_type")
    private ItemType itemType;
    @JsonProperty("check_type")
    private Integer checkType;
    @JsonProperty("organization_name")
    private String organizationName;
    @JsonProperty("agent_company")
    private String agentCompany;
    @JsonProperty("report_start_time")
    private Integer reportStartTime;
    @JsonProperty("report_end_time")
    private Integer reportEndTime;
    @JsonProperty("background")
    private String background;
    @JsonProperty("reports")
    private List<Reports> reports;
    @JsonProperty("report_content")
    private String reportContent;
    @JsonProperty("result")
    private Integer result;
    @JsonProperty("opinion")
    private String opinion;
    @JsonProperty("is_hide")
    private Integer isHide;
    @JsonProperty("updated_at")
    private Long updatedAt;
    @JsonProperty("updated_uid")
    private Long updatedUid;
    @JsonProperty("updated_uname")
    private String updatedUname;
    @JsonProperty("is_qc_charge_person")
    private Boolean isQcChargePerson;

    @NoArgsConstructor
    @Data
    public static class Reports {

        @JsonProperty("id")
        private Long id;
        @JsonProperty("check_id")
        private Long checkId;
        @JsonProperty("report_no")
        private String reportNo;
        @JsonProperty("check_company")
        private String checkCompany;
        @JsonProperty("fee")
        private BigDecimal fee;
        @JsonProperty("fee_voucher")
        private List<?> feeVoucher;
        @JsonProperty("files")
        private List<Files> files;
        @JsonProperty("status")
        private Integer status;
        @JsonProperty("is_repeat")
        private Integer isRepeat;

        @NoArgsConstructor
        @Data
        public static class Files {

            @JsonProperty("id")
            private Long id;
            @JsonProperty("report_id")
            private Long reportId;
            @JsonProperty("name")
            private String name;
            @JsonProperty("pdf_url")
            private String pdfUrl;
            @JsonProperty("img_urls")
            private List<String> imgUrls;
            @JsonProperty("status")
            private Integer status;
        }
    }

    @Data
    public static class ItemType {

        @JsonProperty("first_category_id")
        private Integer firstCategoryId;

        @JsonProperty("second_category_id")
        private Integer secondCategoryId;

        private String name;
    }

}
