package com.daddylab.supplier.item.application.itemCodeRef;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.job.extend.autoRegister.XxlJobAutoRegister;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemCodeRefService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/7/10
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ItemCodeRefJob {
    private final IItemCodeRefService itemCodeRefService;

    @XxlJob(value = "itemCodeRefJob::refresh")
    @XxlJobAutoRegister(cron = "0 0 2 * * ?", jobDesc = "刷新商品编码关联记录", author = "徵乌")
    public void refresh() {
        final int refreshCount = itemCodeRefService.refresh();
        log.info("refresh itemCodeRef count: {}", refreshCount);
    }
}
