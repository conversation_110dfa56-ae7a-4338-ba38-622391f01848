package com.daddylab.supplier.item.application.platformItem.data;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/3/14
 */
@Getter
@RequiredArgsConstructor
public enum PlatformItemInventorySettingType implements IIntegerEnum {
    AUTO_ALLOCATE_PROPORTIONALLY(1, "按比例自动分配库存"),
    LOCK_INVENTORY(2, "锁定库存");
    private final Integer value;
    private final String desc;


}
