package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ThirdPlatformSync;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ThirdPlatformSyncMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IThirdPlatformSyncService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 第三方平台同步信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@Service
public class ThirdPlatformSyncServiceImpl extends DaddyServiceImpl<ThirdPlatformSyncMapper, ThirdPlatformSync> implements IThirdPlatformSyncService {

}
