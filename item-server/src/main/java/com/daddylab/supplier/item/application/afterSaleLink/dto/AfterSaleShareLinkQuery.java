package com.daddylab.supplier.item.application.afterSaleLink.dto;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年05月11日 3:50 PM
 */
@Data
@ApiModel("售后分享链接查询参数封装")
public class AfterSaleShareLinkQuery extends PageQuery {

    private static final long serialVersionUID = -5596874206872478090L;

    @ApiModelProperty("编码、ID")
    private String no;

    @ApiModelProperty("链接名字")
    private String name;

    @ApiModelProperty("手机号码")
    private String tel;

    @ApiModelProperty("创建者、分享者用户ID")
    private List<Long> createdUidList;

    @ApiModelProperty("链接开始时间，时间戳，秒")
    private Long startTime;

    @ApiModelProperty("链接结束时间，时间戳，秒")
    private Long endTime;

    @ApiModelProperty("状态。0正常，1禁用，2已到期")
    private Integer status;

}
