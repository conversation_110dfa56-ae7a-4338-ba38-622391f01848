package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.application.afterSales.AfterSalesSendOnInfoVO;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesSendOnInfo;
import java.util.List;

/**
 * <p>
 * 售后异常转寄信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-17
 */
public interface IAfterSalesSendOnInfoService extends IDaddyService<AfterSalesSendOnInfo> {

    /**
     * 查询售后转寄信息视图对象
     *
     * @param refundOrderNo 退换单号
     */
    List<AfterSalesSendOnInfoVO> getAfterSalesSendOnInfoVOS(String refundOrderNo);
}
