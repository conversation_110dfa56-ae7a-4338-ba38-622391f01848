package com.daddylab.supplier.item.application.chore;

import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.job.extend.autoRegister.XxlJobAutoRegister;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsAbnormality;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsAbnormalityLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ChoreMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsAbnormalityLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsAbnormalityService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/12/10
 */
@Component
@Slf4j
public class ChoreTask {
  @Data
  public static class CleanParams {
    Long createdAt =
        DateUtil.toTime(LocalDateTime.now().minusMonths(1).truncatedTo(ChronoUnit.DAYS));
    int limit = 1000;
    int totalLimit = 100_0000;
  }

  @NonNull
  private static CleanParams cleanParams() {
    return Optional.ofNullable(XxlJobHelper.getJobParam())
        .map(v -> JsonUtil.parse(v, CleanParams.class))
        .orElseGet(CleanParams::new);
  }

  @XxlJob("ChoreTask:cleanLog")
  @XxlJobAutoRegister(cron = "0 0 2 * * ? *", author = "徴乌", jobDesc = "清理LOG")
  public void cleanLog() {
    try {
      cleanLog(cleanParams());
    } catch (Exception e) {
      log.error("[清理LOG]异常", e);
    }
  }

  public void cleanLog(CleanParams params) {
    final ChoreMapper choreMapper = SpringUtil.getBean(ChoreMapper.class);
    int deleteTotal = 0;
    for (; ; ) {
      final int n = choreMapper.deleteLogByCreatedAt(params.getCreatedAt(), params.getLimit());
      deleteTotal += n;
      log.info("[删除LOG]删除 {} 条", n);
      if (n == 0) {
        break;
      }
      if (deleteTotal >= params.getTotalLimit()) {
        break;
      }
    }
    log.info("[删除LOG]总计删除 {} 条", deleteTotal);
  }

  @XxlJob("ChoreTask:deleteOrderLogisticsAbnormalJob")
  @XxlJobAutoRegister(cron = "0 0 3 * * ? *", author = "徴乌", jobDesc = "清理物流异常")
  public void deleteOrderLogisticsAbnormalJob() {
    try {
      deleteOrderLogisticsAbnormal(cleanParams());
    } catch (Exception e) {
      log.error("[清理物流异常]异常", e);
    }
  }

  public void deleteOrderLogisticsAbnormal(CleanParams params) {
    final ChoreMapper choreMapper = SpringUtil.getBean(ChoreMapper.class);
    int deleteTotal1 = 0;
    int deleteTotal2 = 0;
    for (; ; ) {
      final List<Long> ids =
          choreMapper.selectOrderLogisticsAbnormalByCreatedAtAndStatusClosed(
              params.getCreatedAt(), params.getLimit());
      final int n = ids.size();
      deleteTotal1 += n;
      log.info("[删除物流异常]删除 {} 条", n);
      if (n == 0) {
        break;
      }
      choreMapper.deleteOrderLogisticsAbnormalByIds(ids);
      final int n2 = choreMapper.deleteOrderLogisticsAbnormalLogByAbnormalIds(ids);
      log.info("[删除物流异常]删除异常日志 {} 条", n2);
      deleteTotal2 += n2;
      if (deleteTotal2 >= params.getTotalLimit()) {
        break;
      }
    }
    log.info("[删除LOG]总计删除物流异常 {} 条，异常日志 {} 条", deleteTotal1, deleteTotal2);
  }

  @XxlJob("ChoreTask:deleteOrderLogisticsAbnormalLog")
  @XxlJobAutoRegister(cron = "0 0 5 * * ? *", author = "徴乌", jobDesc = "清理物流异常日志")
  public void deleteOrderLogisticsAbnormalLog() {
    try {
      deleteOrderLogisticsAbnormalLog(cleanParams());
    } catch (Exception e) {
      log.error("[清理物流异常日志]异常", e);
    }
  }

  public void deleteOrderLogisticsAbnormalLog(CleanParams params) {
    final ChoreMapper choreMapper = SpringUtil.getBean(ChoreMapper.class);
    int deleteTotal1 = 0;
    for (; ; ) {
      final int n =
          choreMapper.deleteOrderLogisticsAbnormalLogByCreatedAt(
              params.getCreatedAt(), params.getLimit());
      deleteTotal1 += n;
      log.info("[清理物流异常日志]删除 {} 条", n);
      if (n == 0) {
        break;
      }
      if (deleteTotal1 >= params.getTotalLimit()) {
        break;
      }
    }
    log.info("[清理物流异常日志]总计删除物异常日志 {} 条", deleteTotal1);
  }

  @XxlJob("ChoreTask:deleteOrderLogisticsAbnormalInvalidLog")
  @XxlJobAutoRegister(cron = "0 0 5 * * ? *", author = "徴乌", jobDesc = "清理物流异常日志")
  public void deleteOrderLogisticsAbnormalInvalidLog() {
    try {
      deleteOrderLogisticsAbnormalInvalidLog(cleanParams());
    } catch (Exception e) {
      log.error("[清理失效物流异常日志]异常", e);
    }
  }

  public void deleteOrderLogisticsAbnormalInvalidLog(CleanParams params) {
    final IOrderLogisticsAbnormalityLogService orderLogisticsAbnormalityLogService =
        SpringUtil.getBean(IOrderLogisticsAbnormalityLogService.class);
    final IOrderLogisticsAbnormalityService orderLogisticsAbnormalityService =
        SpringUtil.getBean(IOrderLogisticsAbnormalityService.class);
    final ChoreMapper choreMapper = SpringUtil.getBean(ChoreMapper.class);
    int deleteTotal1 = 0;
    long cursorId = 0L;
    for (; ; ) {
      final List<OrderLogisticsAbnormalityLog> list =
          orderLogisticsAbnormalityLogService
              .lambdaQuery()
              .select(
                  OrderLogisticsAbnormalityLog::getId,
                  OrderLogisticsAbnormalityLog::getAbnormalityId)
              .gt(OrderLogisticsAbnormalityLog::getId, cursorId)
              .orderByAsc(OrderLogisticsAbnormalityLog::getId)
              .last("limit " + params.getLimit())
              .list();
      if (list.isEmpty()) {
        break;
      }
      cursorId = list.get(list.size() - 1).getId();
      final Set<Long> abnormalityIds =
          list.stream()
              .map(OrderLogisticsAbnormalityLog::getAbnormalityId)
              .collect(Collectors.toSet());
      final Set<Long> validAbnormalityIds =
          orderLogisticsAbnormalityService
              .lambdaQuery()
              .select(OrderLogisticsAbnormality::getId)
              .in(OrderLogisticsAbnormality::getId, abnormalityIds)
              .list()
              .stream()
              .map(OrderLogisticsAbnormality::getId)
              .collect(Collectors.toSet());
      final List<Long> invalidIds =
          list.stream()
              .filter(e -> !validAbnormalityIds.contains(e.getAbnormalityId()))
              .map(OrderLogisticsAbnormalityLog::getId)
              .collect(Collectors.toList());
      final int n = choreMapper.deleteOrderLogisticsAbnormalLogByIds(invalidIds);
      log.info("[清理失效物流异常日志]删除 {} 条", n);
      deleteTotal1 += n;
      if (deleteTotal1 >= params.getTotalLimit()) {
        break;
      }
    }
    log.info("[清理失效物流异常日志]总计删除物异常日志 {} 条", deleteTotal1);
  }
}
