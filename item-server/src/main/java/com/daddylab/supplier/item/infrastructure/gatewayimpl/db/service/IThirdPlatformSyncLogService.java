package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ThirdPlatformSyncLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformSyncErrorLevel;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-13
 */
public interface IThirdPlatformSyncLogService extends IDaddyService<ThirdPlatformSyncLog> {

    void asyncSaveDouDianLog(Long itemId, String error, PlatformSyncErrorLevel errorLevel);

    void asyncSaveDouDianLog(Long syncId, Long itemId, String error, PlatformSyncErrorLevel errorLevel,
                             String req, String res);

}
