package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 新品商品表的发货类型
 */
@Getter
@AllArgsConstructor
public enum ShipmentType implements IEnum<Integer> {


    /**
     * 工厂发货
     */
    FACTORY(1, "工厂发货"),

    /**
     * 仓库发货
     */
    WAREHOUSE(2, "仓库发货"),

    /**
     * 自营发货（仓库发货的别名）
     */
    SELF_OPERATED(2, "自营发货"),

    ;

    @EnumValue
    private final Integer value;

    private final String desc;


    public static ShipmentType convert(Integer value) {
        return IEnum.getEnumByValue(ShipmentType.class, value);
    }

    public static ShipmentType convert(String value) {
        return IEnum.getEnumByValue(ShipmentType.class, Integer.parseInt(value));
    }

}
