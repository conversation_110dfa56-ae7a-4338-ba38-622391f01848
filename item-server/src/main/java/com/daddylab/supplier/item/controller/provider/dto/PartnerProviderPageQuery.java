package com.daddylab.supplier.item.controller.provider.dto;

import com.alibaba.cola.dto.PageQuery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/8 2:33 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("根据供应商名字模糊查询合作伙伴系统")
public class PartnerProviderPageQuery extends PageQuery {

    private static final long serialVersionUID = 8744464380374909888L;

    @ApiModelProperty("供应商名字")
    @NotBlank(message = "供应商名字不得为空")
    private String name;

    @ApiModelProperty("P系统供应商ID（批量查询）")
    private List<Long> partnerProviderIds;

}
