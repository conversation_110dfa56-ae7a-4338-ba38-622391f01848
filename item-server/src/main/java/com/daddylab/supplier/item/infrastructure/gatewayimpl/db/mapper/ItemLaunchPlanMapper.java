package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.controller.item.dto.LaunchTimeDropDownVo;
import com.daddylab.supplier.item.controller.item.dto.PlanNameDropDownVo;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchPlan;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 商品上新计划 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */
public interface ItemLaunchPlanMapper extends DaddyBaseMapper<ItemLaunchPlan> {

    IPage<ItemLaunchPlan> selectPageList(@Param("page") Page<ItemLaunchPlan> page,
            @Param(Constants.WRAPPER) Wrapper<ItemLaunchPlan> wrapper);

    Long selectPageListCount(@Param(Constants.WRAPPER) Wrapper<ItemLaunchPlan> wrapper);

    List<PlanNameDropDownVo> nameDropDownList(@Param("name") String name,
            @Param("offset") Integer offset, @Param("pageSie") Integer pageSie);

    List<LaunchTimeDropDownVo> launchTimeDownList();

    List<LaunchTimeDropDownVo> launchPlanDownList(@Param("offset") Integer offset,
            @Param("pageSie") Integer pageSie);

    ItemLaunchPlan getPlanByItemId(@Param("itemId") Long itemId);

    /**
     * 批量更新商品数量统计
     *
     * @param planIds 商品ID
     * @return 更新计划数量
     */
    int updateBatchPlanRefCount(@Param("planIds") Collection<Long> planIds);

    List<Long> queryItemIdByLaunchTime(@Param("offset") Integer offset,@Param("size") Integer size);

}
