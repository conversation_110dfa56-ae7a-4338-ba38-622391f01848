package com.daddylab.supplier.item.controller.user;

import com.alibaba.cola.dto.MultiResponse;
import com.daddylab.supplier.item.application.user.UserBizService;
import com.daddylab.supplier.item.domain.user.dto.StaffDropBuyerQuery;
import com.daddylab.supplier.item.domain.user.dto.StaffDropDownItem;
import com.daddylab.supplier.item.domain.user.dto.StaffDropDownQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.inject.Inject;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/user")
@Api(value = "用户相关API", tags = "用户相关API")
public class UserController {
    @Inject
    UserBizService userBizService;

    @GetMapping(value = "/staffDropDownList")
    @ApiOperation("员工下拉列表")
    public MultiResponse<StaffDropDownItem> staffDropDownList(@Valid StaffDropDownQuery query) {
        return userBizService.staffDropDownList(query);
    }

    /**
     * @deprecated
     */
    @GetMapping(value = "/staffDropDownBuyerList")
    @ApiOperation("员工花名下拉列表")
    public MultiResponse<StaffDropDownItem> staffDropDownBuyerList(@Valid StaffDropBuyerQuery query) {
        return userBizService.staffDropDownBuyerList(query);
    }


}
