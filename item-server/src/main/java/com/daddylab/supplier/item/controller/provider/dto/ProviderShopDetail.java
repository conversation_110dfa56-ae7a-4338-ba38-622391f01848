package com.daddylab.supplier.item.controller.provider.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> up
 * @date 2023年07月25日 4:14 PM
 */
@NoArgsConstructor
@Data
public class ProviderShopDetail {


    @SerializedName("shopId")
    private Long shopId;
    @SerializedName("shopName")
    private String shopName;
    @SerializedName("shopNo")
    private String shopNo;
    @SerializedName("description")
    private Object description;
    @SerializedName("status")
    private Integer status;
    @SerializedName("logoUrl")
    private String logoUrl;
    @SerializedName("sellerName")
    private String sellerName;
}
