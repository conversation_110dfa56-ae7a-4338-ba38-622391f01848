package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DataSource;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.PropertyName;
import org.javers.core.metamodel.annotation.TypeName;

import java.util.Arrays;
import java.util.Objects;

/**
 * 商品
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TypeName("商品")
public class Item extends Entity {

    private static final long serialVersionUID = 3153088210367162458L;

    /** 商品名称 */
    @PropertyName("商品名称")
    private String name;

    /** 关联供应商系统商品款号 */
    @PropertyName("关联供应商系统商品款号")
    private String partnerProviderItemSn;

    /** 品类ID */
    @PropertyName("品类")
    private Long categoryId;

    /** 品牌ID */
    @PropertyName("品牌")
    private Long brandId;

    /** 供应商ID */
    @PropertyName("供应商")
    private Long providerId;

    /** 供货指定编码 */
    @PropertyName("供货指定编码")
    private String providerSpecifiedCode;

    /** 供货编码，不存在供货指定编码时，取商品编码 */
    public String getSupplierCode() {
        return providerSpecifiedCode != null && !providerSpecifiedCode.isEmpty()
                ? providerSpecifiedCode
                : code;
    }

    /** 商品状态 0:待上架 1:在售中 2:已下架 3:废弃 4:已下架(永久) */
    @PropertyName("商品状态")
    private Integer status;

    /** {@link com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus} */
    @PropertyName("商品上新状态")
    private Integer launchStatus;

    public void setLaunchStatus(Integer launchStatus) {
        if (this.launchStatus != null
                && !Arrays.asList(ItemLaunchStatus.DOWN.getValue()).contains(launchStatus)) {
            this.beforeLaunchStatus = this.launchStatus;
        }
        this.launchStatus = launchStatus;
    }

    @PropertyName("商品审核状态 0:未进入审核流程/未知 5:待法务审核 10:待QC审核 100:审核完成")
    private Integer auditStatus;

    /** 状态备注 */
    @PropertyName("状态备注")
    private String statusRemark;

    /** 预计上架时间 */
    @PropertyName("预计上架时间")
    private Long estimateSaleTime;

    /** 系统生成的商品编码 */
    @DiffIgnore private String code;

    /** 此商品是否是赠品 */
    @PropertyName("是否赠品")
    private Integer isGift;

    /** 如果是赠品，关联的主商品id */
    @PropertyName("关联主商品")
    private Long parentItemId;

    /** 关联主商品CODE */
    @PropertyName("关联主商品CODE")
    @DiffIgnore
    private String parentCode;

    /** 删除时间 */
    @DiffIgnore private Long deletedAt;

    /** 数据来源 */
    @DiffIgnore private DataSource source;

    @PropertyName("仓库编码")
    private String warehouseNo;

    /** 是否已同步到旺店通 */
    @DiffIgnore private Boolean isWdtSynced;

    /** 商品在旺店通是否已经被删除 */
    @DiffIgnore private Boolean isDeletedInWdt;

    /** 上架时间 */
    @DiffIgnore private Long upFrameTime;

    @PropertyName("商品下架时间")
    private Long downFrameTime;

    @PropertyName("商品下架原因")
    private String downFrameReason;

    /** 首次上架时间 */
    @PropertyName("首次上架时间")
    private Long firstUpFrameTime;

    /** 业务线 */
    @DiffIgnore private Integer businessLine;

    /** 业务线改为多选,出于兼容考虑,选择新增字段 */
    @DiffIgnore private String businessLines;

    public String getBusinessLines() {
        return DivisionLevelValueEnum.filterCorpType(businessLines);
    }

    public String oldBusinessLines() {
        return businessLines;
    }

    /** P 系统上商品技术类目。1 日化 2 食品 3 纺织鞋类 4 玩具 5 电器 6轻工百货 */
    @DiffIgnore private Integer partnerSysType;

    /** 商品简称 */
    @PropertyName("商品简称")
    private String shortName;

    /** 前商品上新状态 */
    private Integer beforeLaunchStatus;
}
