package com.daddylab.supplier.item.infrastructure.timing;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 受到SpringAop代理实现的约束，使用场景比较有限，很多地方都无法生效
 *
 * <AUTHOR>
 * @since 2021/12/7
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TimingWatch {
    /**
     * 名称，不传默认取方法名加参数
     */
    String name() default "";

    /**
     * 时间单位，默认毫秒
     */
    TimeUnit unit() default TimeUnit.MILLISECONDS;

    /**
     * 默认输出到标准输出
     */
    Output output() default Output.LOG;

    /**
     * 如果输出到日志，默认的日志级别是DEBUG
     */
    Level level() default Level.DEBUG;
}
