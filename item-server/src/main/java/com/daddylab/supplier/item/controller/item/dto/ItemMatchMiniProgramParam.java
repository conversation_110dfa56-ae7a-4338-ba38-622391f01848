package com.daddylab.supplier.item.controller.item.dto;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/9/16 09:42
 * @Description: 商品匹配小程序入参
 */
@Data
@ApiModel("ItemMatchMiniProgramParam-商品匹配小程序入参")
public class ItemMatchMiniProgramParam extends PageQuery {

    @ApiModelProperty(value = "商品匹配小程序入参")
    @NotEmpty(message = "商品信息不能为空")
    List<ItemMatchMiniProgramInternalParam> params;

    @Data
    @ApiModel("ItemMatchMiniProgramInternalParam-商品匹配小程序入参")
    public static class ItemMatchMiniProgramInternalParam implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "商品编号")
        private String itemNo;
        @ApiModelProperty(value = "商品标准名")
        private String itemStandardName;
        @ApiModelProperty(value = "sku编号")
        private List<String> skuNos;
        @ApiModelProperty(value = "小程序店铺ID")
        private Long mallShopId;
    }
}
