package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSaleShareLink;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AfterSaleShareLinkMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSaleShareLinkService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

import static com.daddylab.supplier.item.common.GlobalConstant.AFTER_SALE_SHARE_LINK_NO_PREFIX;

/**
 * <p>
 * 售后分享链接信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
@Service
public class AfterSaleShareLinkServiceImpl extends DaddyServiceImpl<AfterSaleShareLinkMapper, AfterSaleShareLink> implements IAfterSaleShareLinkService {

    @Resource
    AfterSaleShareLinkMapper afterSaleShareLinkMapper;

    @Override
    public String getNewOnNo() {
        Long newId = afterSaleShareLinkMapper.getNewOne();
        newId = Objects.isNull(newId) ? 0 : newId;
        long integer = newId + 1;
        String format = String.format("%04d", integer);
        String yyyy = DateUtil.format(LocalDateTime.now(), "yyyy").substring(2, 4);
        return AFTER_SALE_SHARE_LINK_NO_PREFIX + yyyy + format;
    }

    public static void main(String[] args) {
        String newOne = "L240004";
        String s = newOne.replaceAll(AFTER_SALE_SHARE_LINK_NO_PREFIX, StringUtils.EMPTY);
        Integer integer = NumberUtil.convertInt(s);
        String format = String.format("%04d", integer + 1);
        String yyyy = DateUtil.format(LocalDateTime.now(), "yyyy").substring(2, 4);
        String aa = AFTER_SALE_SHARE_LINK_NO_PREFIX + yyyy + format;
        System.out.println(aa);
    }

}
