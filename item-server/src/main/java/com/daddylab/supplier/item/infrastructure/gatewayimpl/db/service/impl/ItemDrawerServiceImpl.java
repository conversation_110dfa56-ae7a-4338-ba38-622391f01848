package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.LaunchItemType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerService;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Optional;

/**
 * <p>
 * 商品抽屉表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Service
public class ItemDrawerServiceImpl extends DaddyServiceImpl<ItemDrawerMapper, ItemDrawer> implements
        IItemDrawerService {

    @Autowired
    private ItemDrawerMapper itemDrawerMapper;

    private final LoadingCache<Long, Long> itemDrawerIdCacheByItemId = Caffeine.newBuilder()
                                                                               .expireAfterWrite(Duration.ofMinutes(1))
                                                                               .build(itemId -> getByItemId(itemId).map(
                                                                                       Entity::getId).orElse(null));

    @Override
    public ItemDrawer getById(Long id) {
        return itemDrawerMapper.selectById(id);
    }


    @Override
    public Optional<ItemDrawer> getByItemId(Long itemId) {
        final LambdaQueryWrapper<ItemDrawer> query = Wrappers.lambdaQuery();
        query.eq(ItemDrawer::getItemId, itemId).last("limit 1");
        return Optional.ofNullable(itemDrawerMapper.selectOne(query));
    }

    @Override
    public ItemDrawer getOrCreate(Long itemId) {
        return getByItemId(itemId).orElseGet(() -> {
            ItemDrawer itemDrawer = new ItemDrawer();
            itemDrawer.setStandardName("");
            itemDrawer.setTbTitle("");
            itemDrawer.setMiniTitle("");
            itemDrawer.setHomeCopy("");
            itemDrawer.setShelfTime(0L);
            itemDrawer.setItemId(itemId);
            itemDrawer.setType(LaunchItemType.INNER);
            save(itemDrawer);
            return itemDrawer;
        });
    }

    @Override
    public Long getItemDrawerIdByItemId(Long itemId, Boolean noCache) {
        if (noCache) {
            itemDrawerIdCacheByItemId.invalidate(itemId);
        }
        return itemDrawerIdCacheByItemId.get(itemId);
    }

    @Override
    public Optional<ItemDrawer> getFirstByWechatId(String wechatId) {
        return lambdaQuery().eq(ItemDrawer::getWechatId, wechatId).last("limit 1").oneOpt();
    }
}
