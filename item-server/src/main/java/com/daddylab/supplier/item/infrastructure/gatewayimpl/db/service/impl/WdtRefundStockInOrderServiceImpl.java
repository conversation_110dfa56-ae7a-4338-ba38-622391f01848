package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundStockInOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundStockInOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtRefundStockInOrderService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 旺店通退货入库单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Service
public class WdtRefundStockInOrderServiceImpl extends DaddyServiceImpl<WdtRefundStockInOrderMapper, WdtRefundStockInOrder> implements IWdtRefundStockInOrderService {

}
