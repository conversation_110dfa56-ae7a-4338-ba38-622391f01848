package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSyncLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemSyncLogMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSyncLogService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品同步日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class ItemSyncLogServiceImpl extends DaddyServiceImpl<ItemSyncLogMapper, ItemSyncLog> implements IItemSyncLogService {

}
