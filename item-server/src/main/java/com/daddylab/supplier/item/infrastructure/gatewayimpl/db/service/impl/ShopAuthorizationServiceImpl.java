package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ShopAuthorizationMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopAuthorizationService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validator;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <p>
 * 店铺授权 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Service
public class ShopAuthorizationServiceImpl extends DaddyServiceImpl<ShopAuthorizationMapper, ShopAuthorization> implements IShopAuthorizationService {

    @Autowired
    private Validator validator;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateShopAuthorization(@Nonnull ShopAuthorization shopAuthorization) {
        final Set<ConstraintViolation<ShopAuthorization>> validate = validator.validate(shopAuthorization);
        if (!validate.isEmpty()) {
            throw new ConstraintViolationException(validate);
        }
        final Optional<ShopAuthorization> shopAuthorizationPO = getByShopNo(shopAuthorization.getSn());
        shopAuthorizationPO.map(ShopAuthorization::getId).ifPresent(shopAuthorization::setId);
        saveOrUpdate(shopAuthorization);
    }

    @Override
    public Optional<ShopAuthorization> getByShopNo(String shopNo) {
        return lambdaQuery().eq(ShopAuthorization::getSn, shopNo)
                            .oneOpt();
    }

    @Override
    public List<ShopAuthorization> listNotExpiredAuthorizations(Platform platform) {
        return lambdaQuery().eq(ShopAuthorization::getPlatform, platform)
                            .ge(ShopAuthorization::getExpiredAt, DateUtil.currentTime())
                            .list();
    }
    
    @Override
    public List<ShopAuthorization> listAuthorizations(Platform platform) {
        return lambdaQuery().eq(ShopAuthorization::getPlatform, platform)
                .list();
    }
    
    @Override
    public List<ShopAuthorization> listAuthorizationsSoonToExpire(Platform platform, int remainTtl) {
        return lambdaQuery().eq(ShopAuthorization::getPlatform, platform)
                .le(ShopAuthorization::getExpiredAt, DateUtil.currentTime() + remainTtl)
                .list();
    }


}
