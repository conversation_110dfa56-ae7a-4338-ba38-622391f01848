package com.daddylab.supplier.item.controller.platformItem;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.platformItem.PlatformItemWarnBizService;
import com.daddylab.supplier.item.application.platformItem.data.PlatformItemWarnListItem;
import com.daddylab.supplier.item.application.platformItem.query.PlatformItemWarnPageQuery;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.domain.dto.GenericIdBody;
import com.daddylab.supplier.item.common.domain.dto.GenericIdsBody;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 2:23 下午
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/platformItemWarn")
@Api(tags = {"平台商品警告API"})
public class PlatformItemWarnController {
    @Autowired
    PlatformItemWarnBizService platformItemWarnBizService;

    @PostMapping("/page")
    @ResponseBody
    @ApiOperation(value = "平台商品警告分页查询")
    public PageResponse<PlatformItemWarnListItem> pageQuery(@Validated @RequestBody PlatformItemWarnPageQuery pageQuery) {
        pageQuery.setCurrentUserId(UserContext.getUserId());
        pageQuery.setShowAll(UserPermissionJudge.hasApiPermission(GlobalConstant.PLATFORM_WARN_VIEW_ALL_PERMISSIONS));
        return platformItemWarnBizService.pageQuery(pageQuery);
    }

    @PostMapping("/handle")
    @ResponseBody
    @ApiOperation(value = "平台商品警告处理")
    public Response handle(@Validated @RequestBody GenericIdBody<Long> body) {
        return platformItemWarnBizService.handle(body.getId());
    }

    @PostMapping("/handleBatch")
    @ResponseBody
    @ApiOperation(value = "平台商品警告批量处理")
    public Response handleBatch(@Validated @RequestBody GenericIdsBody<Long> body) {
        return platformItemWarnBizService.handleBatch(body.getIds());
    }

    @PostMapping("/uploadPlatformSpecNo")
    @ResponseBody
    @ApiOperation(value = "平台商品商家编码回传")
    public Response uploadPlatformSpecNo(@Validated @RequestBody UploadPlatformSpecNoCmd cmd) {
        return platformItemWarnBizService.uploadPlatformSpecNo(cmd.getId(), cmd.getSpecNo());
    }

}
