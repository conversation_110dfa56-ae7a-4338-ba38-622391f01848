//package com.daddylab.supplier.item.infrastructure.jaeger;
//
//import io.jaegertracing.internal.MDCScopeManager;
//import io.opentracing.contrib.java.spring.jaeger.starter.TracerBuilderCustomizer;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.annotation.Bean;
//
//
//@Configuration
//@Slf4j
//public class JaegerLogConfig {
//    @Bean
//    public TracerBuilderCustomizer mdcBuilderCustomizer() {
//        return builder -> builder.withScopeManager(new MDCScopeManager.Builder().build());
//    }
//}
