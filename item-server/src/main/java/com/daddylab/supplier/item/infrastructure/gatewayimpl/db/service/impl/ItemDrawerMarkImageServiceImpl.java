package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMarkImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerMarkImageMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerMarkImageService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 标注图片 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Service
public class ItemDrawerMarkImageServiceImpl extends DaddyServiceImpl<ItemDrawerMarkImageMapper, ItemDrawerMarkImage> implements IItemDrawerMarkImageService {

}
