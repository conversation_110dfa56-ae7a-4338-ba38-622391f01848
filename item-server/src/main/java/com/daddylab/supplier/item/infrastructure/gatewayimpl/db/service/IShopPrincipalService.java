package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopPrincipal;

/**
 * <p>
 * 店铺负责人 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface IShopPrincipalService extends IDaddyService<ShopPrincipal> {

}
