package com.daddylab.supplier.item.application.platformItem.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.PositiveOrZero;

/**
 * <AUTHOR>
 * @since 2024/3/8
 */
@ApiModel("平台商品库存设置视图模型")
@Data
public class PlatformItemInventorySettingVO {

    /**
     * 类型 1:按比例自动分配库存 2:锁定库存
     */
    @ApiModelProperty("类型 1:按比例自动分配库存 2:锁定库存")
    private PlatformItemInventorySettingType type;

    /**
     * 库存占比
     */
    @ApiModelProperty("库存占比")
    @Min(0)
    @Max(100)
    private Integer inventoryRatio;

    public Integer getInventoryRatio() {
        return type == PlatformItemInventorySettingType.AUTO_ALLOCATE_PROPORTIONALLY ? inventoryRatio : 0;
    }

    /**
     * 锁定库存
     */
    @ApiModelProperty("锁定库存")
    @PositiveOrZero
    private Integer lockNum;

    public Integer getLockNum() {
        return type == PlatformItemInventorySettingType.LOCK_INVENTORY ? lockNum : 0;
    }
}
