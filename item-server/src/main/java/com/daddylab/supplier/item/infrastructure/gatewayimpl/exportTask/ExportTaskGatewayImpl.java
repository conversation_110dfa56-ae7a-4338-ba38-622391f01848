package com.daddylab.supplier.item.infrastructure.gatewayimpl.exportTask;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.domain.exportTask.ExportTaskGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IExportTaskService;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.inject.Inject;
import java.util.Objects;

@Component
public class ExportTaskGatewayImpl implements ExportTaskGateway {
    @Inject
    private IExportTaskService iExportTaskService;

    @Override
    public Long saveOrUpdateExportTask(ExportTask exportTask) {
        if (Objects.nonNull(exportTask.getId())) {
            iExportTaskService.updateById(exportTask);
            return exportTask.getId();
        }
        Assert.hasText(exportTask.getName(), "任务名称不得为空");
        iExportTaskService.save(exportTask);
        return exportTask.getId();
    }

    @Override
    public void removeExpireTask(Long expireLimitDate) {
        LambdaQueryWrapper<ExportTask> wrapper = Wrappers.lambdaQuery();
        wrapper.le(ExportTask::getCreatedAt,expireLimitDate);
        iExportTaskService.remove(wrapper);
    }

    @Override
    public void updateProgress(ExportTask exportTask) {
        final ExportTask updateModel = new ExportTask();
        updateModel.setId(exportTask.getId());
        updateModel.setProgress(exportTask.getProgress());
        iExportTaskService.updateById(updateModel);
    }

}
