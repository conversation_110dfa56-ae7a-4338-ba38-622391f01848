package com.daddylab.supplier.item.application.entryActivityPrice.models;

import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/4
 */
@Data
@EqualsAndHashCode(of = {"id"})
public class ConfirmationViewItemVO {
    private Long id;
    private String itemCode;
    private Long itemId;
    private String itemName;
    private String imgUrl;
    private String provider;
    private Long providerId;
    private StaffBrief buyer;
    @ApiModelProperty(value = "状态", notes = "状态 -1 待发起 0 待确认 1 已确认 2 存在异议")
    private Integer status;
    private List<ConfirmationViewItemSkuVO> skuList;
    private List<ConfirmationViewItemDissentVO> dissentList;
}
