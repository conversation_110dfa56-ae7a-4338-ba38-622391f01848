package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 订单物流异常记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrderLogisticsAbnormalityAlert implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Long isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 预警类型 1: 24小时内异常订单量超限，2: 48小时内异常订单量超限
     */
    private Integer alertType;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 预警数值
     */
    private Integer alertNum;


    private Integer isActive;



}
