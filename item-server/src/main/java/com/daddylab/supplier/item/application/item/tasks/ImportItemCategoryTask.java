package com.daddylab.supplier.item.application.item.tasks;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.daddylab.supplier.item.application.category.CategoryBizService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.controller.category.dto.CategoryTreeNode;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Sku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOperateLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ISkuService;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil.ChangePropertyObj;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MultiMapUtils;
import org.apache.commons.collections4.SetValuedMap;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/8/17
 */
@Component
@Slf4j
@AllArgsConstructor
public class ImportItemCategoryTask {

    @Data
    @HeadRowHeight(20)
    @ContentRowHeight(16)
    @EqualsAndHashCode(of = {"itemCode"})
    public static class ImportRow implements Serializable {

        private static final long serialVersionUID = -752114031599220390L;

        @ExcelProperty(value = "商品编码SPU")
        @ColumnWidth(50)
        private String itemCode;

        @ExcelProperty(value = "品类")
        @ColumnWidth(50)
        private String category;
    }


    private final IItemService itemService;
    private final IItemSkuService itemSkuService;
    private final ISkuService skuService;
    private final CategoryBizService categoryBizService;
    private final CategoryGateway categoryGateway;

    private final OperateLogDomainService operateLogDomainService;
    private final IOperateLogService operateLogService;

    private String joinPath(CategoryTreeNode treeNode) {
        List<String> paths = Lists.newLinkedList();
        paths.add(treeNode.getName());
        CategoryTreeNode parent = treeNode.getParent();
        while (parent != null) {
            paths.add(parent.getName());
            parent = parent.getParent();
        }
        Collections.reverse(paths);
        return StringUtil.trimStart(String.join("/", paths), "新类目/");
    }

    private Map<String, CategoryTreeNode> flatCategoryTreeNodePath(
            List<CategoryTreeNode> categoryTreeNodes) {
        final HashMap<String, CategoryTreeNode> objectObjectHashMap = Maps.newHashMapWithExpectedSize(
                countNodeNumberRecursive(categoryTreeNodes));
        flatCategoryTreeNodePath(objectObjectHashMap, categoryTreeNodes);
        return objectObjectHashMap;
    }

    private int countNodeNumberRecursive(List<CategoryTreeNode> categoryTreeNodes) {
        final int count = categoryTreeNodes.size();
        if (count == 0) {
            return 0;
        }
        return count + countNodeNumberRecursive(
                categoryTreeNodes.stream().flatMap(v -> v.getChildren().stream())
                        .collect(Collectors.toList()));
    }

    private void flatCategoryTreeNodePath(Map<String, CategoryTreeNode> flatPaths,
            List<CategoryTreeNode> categoryTreeNodes) {
        if (categoryTreeNodes.isEmpty()) {
            return;
        }
        categoryTreeNodes.forEach(node -> flatPaths.put(joinPath(node), node));
        flatCategoryTreeNodePath(flatPaths,
                categoryTreeNodes.stream().flatMap(v -> v.getChildren().stream()).collect(
                        Collectors.toList()));
    }

    @Trace(operationName = "ImportItemCategoryTask")
    public void importExcel(InputStream inputStream) {
        List<ImportRow> sheetRowsRaw = EasyExcel.read(inputStream)
                .headRowNumber(1)
                .head(ImportRow.class).sheet(0).doReadSync();
        final List<ImportRow> sheetRows = sheetRowsRaw.stream().distinct()
                .collect(Collectors.toList());

        log.info("开始处理商品品类导入，总计:{}", sheetRows.size());

        Set<String> itemHandled = Sets.newHashSet();
        final SetValuedMap<String, Object> runStat = MultiMapUtils.newSetValuedHashMap();

        final List<CategoryTreeNode> categoryTreeTops = categoryBizService.categoryTree()
                .getData().getTreeNodes();
        final Map<String, CategoryTreeNode> flatCategoryTreeNodePaths = flatCategoryTreeNodePath(
                categoryTreeTops);

        for (int i = 0; i < sheetRows.size(); i++) {
            final ImportRow sheetRow = sheetRows.get(i);
            if (StringUtil.isBlank(sheetRow.getItemCode()) && StringUtil.isBlank(
                    sheetRow.getCategory())) {
                continue;
            }

            final String itemCode = sheetRow.getItemCode();
            if (StringUtil.isBlank(itemCode)) {
                log.warn("第{}行商品编码为空", i + 2);
                continue;
            }

            if (itemHandled.contains(itemCode)) {
                log.info("商品编码已处理:{}", itemCode);
                continue;
            }
            itemHandled.add(itemCode);

            final String category = sheetRow.getCategory();
            if (StringUtil.isBlank(category)) {
                log.warn("品类为空 商品编码:{}", itemCode);
                continue;
            }

            final List<Sku> skus = skuService.lambdaQuery()
                    .eq(Sku::getItemCode, itemCode).list();
            if (skus.isEmpty()) {
                log.error("商品不存在 商品编码:{}", itemCode);
                continue;
            }
            final List<String> skuCodes = skus.stream().map(Sku::getSkuCode).distinct()
                    .collect(Collectors.toList());
            final List<ItemSku> itemSkus = itemSkuService.lambdaQuery()
                    .in(ItemSku::getSkuCode, skuCodes).or()
                    .in(ItemSku::getProviderSpecifiedCode, skuCodes).list();
            if (itemSkus.isEmpty()) {
                log.error("未找到关联的后端商品规格记录 商品编码:{}", itemCode);
                continue;
            }
            final List<Long> itemIds = itemSkus.stream().map(ItemSku::getItemId).distinct()
                    .collect(Collectors.toList());
            final List<Item> items = itemService.lambdaQuery().in(Entity::getId, itemIds).list();
            if (items.isEmpty()) {
                log.error("未找到关联的后端商品记录 商品编码:{}", itemCode);
                continue;
            }
            for (Item item : items) {
                final Long oldCategoryId = item.getCategoryId();

                final CategoryTreeNode categoryTreeNode = flatCategoryTreeNodePaths.get(category);
                if (categoryTreeNode == null) {
                    log.error("品类不存在 商品编码:{} 品类名称:{}", itemCode, category);
                    runStat.put("不存在的品类", category);
                    runStat.put("品类不存在的商品", itemCode);
                    continue;
                }
                final Long newCategoryId = categoryTreeNode.getId();
                if (Objects.equals(newCategoryId, oldCategoryId)) {
                    log.info("商品当前品类与导入品类相同，无需处理 商品编码:{} 品类名称:{}", itemCode, category);
                    continue;
                }

                try {
                    if (categoryGateway.modifyItemCategory(item.getId(), newCategoryId)) {
                        log.info("商品品类已导入 商品ID:{} 新的品类:{} 旧的品类ID:{}", item.getId(), category,
                                oldCategoryId);
                    } else {
                        log.warn("商品当前品类与导入品类相同，未更新成功 商品编码:{} 品类名称:{}", itemCode, category);
                    }
                } catch (Exception e) {
                    if (e instanceof BizException) {
                        log.error("商品品类导入失败，业务检查异常：{}", e.getMessage());
                    } else {
                        log.error("商品品类导入失败，未知异常：{}", e.getMessage(), e);
                    }
                }
            }
        }
        log.info("商品品类导入处理完成 {}", runStat);
    }

    @Trace(operationName = "ImportItemCategoryTask.fix")
    public void fix() {
        fix(Collections.emptyList());
    }

    @Trace(operationName = "ImportItemCategoryTask.fix")
    public void fix(List<Long> itemIds) {
        final List<OperateLog> logs = operateLogService.lambdaQuery()
                .eq(OperateLog::getTargetType, OperateLogTarget.ITEM)
                .in(CollectionUtil.isNotEmpty(itemIds), OperateLog::getTargetId, itemIds)
                .eq(OperateLog::getMsg, "系统导入品类").list();
        for (OperateLog operateLog : logs) {
            final Long itemId = Long.parseLong(operateLog.getTargetId());
            final String data = operateLog.getData();
            try {
                final List<ChangePropertyObj> changes = JsonUtil.parse(data,
                        new TypeReference<List<ChangePropertyObj>>() {
                        });
                if (Objects.isNull(changes)) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "日志变更数据解析失败");
                }
                final ChangePropertyObj categoryChange = changes.stream().findAny()
                        .orElseThrow(() -> ExceptionPlusFactory.bizException(
                                ErrorCode.DATA_NOT_FOUND, "未找到类目变更明细数据"));
                final Long categoryId = Long.parseLong(categoryChange.getOldVal().toString());
                final Long newCategoryId = Long.parseLong(categoryChange.getNewVal().toString());
                categoryGateway.modifyItemAndSkuAttr(itemId, categoryId,
                        newCategoryId);
                log.info("商品品类变更补偿完成，itemId={}, oldCategoryId={}，newCategoryId={}", itemId,
                        categoryId, newCategoryId);
            } catch (BizException e) {
                log.error("商品品类变更补偿失败，itemId={}，data={}，业务异常：{}", itemId, data, e.getMessage());
            } catch (Throwable e) {
                log.error("商品品类变更补偿失败，itemId={}，data={}，未知异常：{}", itemId, data, e.getMessage(), e);
            }
        }
    }

}
