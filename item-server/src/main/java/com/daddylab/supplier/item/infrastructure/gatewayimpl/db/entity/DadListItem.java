package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
* <p>
    * 老爸清单
    * </p>
*
* <AUTHOR>
* @since 2024-10-11
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    public class DadListItem implements Serializable {

    private static final long serialVersionUID = 1L;

            /**
            * id
            */
            @TableId(value = "id", type = IdType.AUTO)
    private Long id;

            /**
            * p系统商品id
            */
    private Long pItemId;

            /**
            * p系统商品编号
            */
    private String pItemNo;

            /**
            * ERP商品id
            */
    private Long erpItemId;

            /**
            * ERP商品编码
            */
    private String erpItemCode;

            /**
            * 电商商品id
            */
    private Long mallItemId;

            /**
            * 1白名单 2老爸抽检
            */
    private Integer moduleType;

            /**
            * 商品二级品类（搜索用）
            */
    private String secondCategoryName;

            /**
            * 商品完整品类
            */
    private String category;

            /**
            * 商品名称
            */
    private String name;

            /**
            * 品牌
            */
    private String brand;

            /**
            * 商品图片（主图就是第一张）
            */
    private String itemImg;

            /**
            * 商品spec数据[{"color":"红色","spec":"50g"}]
            */
    private String specInfo;

            /**
            * 技术总结
            */
    private String technicalSummary;

            /**
            * 商品成分
            */
    private String composition;

            /**
            * 通用检测项目id
            */
    private Long commonCheckProjectId;

            /**
            * 最后报告时间
            */
    private Long lastCheckAt;

            /**
            * 检测合格项目
            */
    private String projectInfo;

            /**
            * 供应商名称
            */
    private String organizationName;

            /**
            * 供应商统一信用代码
            */
    private String organizationNo;

            /**
            * 供应链角色1 生产商 2 经销商 3 品牌方 4 代理商
            */
    private Integer organizationRole;

            /**
            * 省
            */
    private String province;

            /**
            * 市
            */
    private String city;

            /**
            * 区
            */
    private String area;

            /**
            * 通讯地址
            */
    private String addr;

            /**
            * 联系人
            */
    private String partner;

            /**
            * 创建时间
            */
            @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

            /**
            * 创建人
            */
            @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

            /**
            * 更新时间
            */
            @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

            /**
            * 更新人
            */
            @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;

            /**
            * 删除时间
            */
            @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

            /**
            * 删除人
            */
    private Long deletedUid;


}
