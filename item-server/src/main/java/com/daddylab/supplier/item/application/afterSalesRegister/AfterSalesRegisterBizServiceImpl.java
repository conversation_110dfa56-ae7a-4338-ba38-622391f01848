package com.daddylab.supplier.item.application.afterSalesRegister;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.daddylab.supplier.item.application.warehouse.WarehouseGateway;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.domain.banniu.BanniuCommonService;
import com.daddylab.supplier.item.domain.exportTask.ExportTaskGateway;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesRegisterService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBizLevelDivisionService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICombinationItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.ObjectUtil;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import wjb.open.api.response.v2.banniu.SellerListResponse;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/21
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AfterSalesRegisterBizServiceImpl implements AfterSalesRegisterBizService {
  private final IAfterSalesRegisterService afterSalesRegisterService;
  private final ExportTaskGateway exportTaskGateway;
  private final FileGateway fileGateway;
  private final WarehouseGateway warehouseGateway;
  private final IBizLevelDivisionService iBizLevelDivisionService;
  private final IItemSkuService iItemSkuService;
  private final ICombinationItemService iCombinationItemService;

  @Override
  public PageResponse<AfterSalesRegisterPageVO> pageQuery(AfterSalesRegisterPageQuery query) {
    final LambdaQueryChainWrapper<AfterSalesRegister> queryWrapper =
        afterSalesRegisterService.lambdaQuery();
    queryWrapper.eq(
        StrUtil.isNotEmpty(query.getOrderNo()), AfterSalesRegister::getOrderNo, query.getOrderNo());
    queryWrapper.eq(
        StrUtil.isNotEmpty(query.getItemCode()),
        AfterSalesRegister::getItemCode,
        query.getItemCode());
    queryWrapper.like(
        StrUtil.isNotEmpty(query.getItemName()),
        AfterSalesRegister::getItemName,
        query.getItemName());
    queryWrapper.eq(
        StrUtil.isNotEmpty(query.getShopName()),
        AfterSalesRegister::getShopName,
        query.getShopName());
    queryWrapper.ge(
        !Objects.isNull(query.getPayTimeStart()),
        AfterSalesRegister::getPayTime,
        DateUtil.toLocalDateTime(query.getPayTimeStart()));
    queryWrapper.le(
        !Objects.isNull(query.getPayTimeEnd()),
        AfterSalesRegister::getPayTime,
        DateUtil.toLocalDateTime(query.getPayTimeEnd()));
    //        queryWrapper.in(CollUtil.isNotEmpty(query.getBusinessLine()),
    //                AfterSalesRegister::getBusinessLine, query.getBusinessLine());
    Set<String> warehousesFilterSet = new HashSet<>();
    if (StrUtil.isNotBlank(query.getWarehouse())) {
      warehousesFilterSet.add(query.getWarehouse());
    }
    if (ObjectUtil.isNotEmpty(query.getWarehouseNos())) {
      final List<String> warehouseNamesByNos =
          warehouseGateway.getWarehouseListByNos(query.getWarehouseNos()).stream()
              .map(Warehouse::getName)
              .collect(Collectors.toList());
      if (warehouseNamesByNos.isEmpty()) {
        return ResponseFactory.emptyPage();
      }
      warehousesFilterSet.addAll(warehouseNamesByNos);
    }
    if (CollUtil.isNotEmpty(query.getOrderPersonnelIds())) {
      final List<String> warehouseNamesByOPIDS =
          warehouseGateway
              .getWarehouseListByOrderPersonnelIds(query.getOrderPersonnelIds())
              .stream()
              .map(Warehouse::getName)
              .collect(Collectors.toList());
      if (warehouseNamesByOPIDS.isEmpty()) {
        return ResponseFactory.emptyPage();
      }
      if (CollUtil.isNotEmpty(warehousesFilterSet)) {
        Collection<String> intersection =
            CollUtil.intersection(warehousesFilterSet, warehouseNamesByOPIDS);
        if (CollUtil.isEmpty(intersection)) {
          return ResponseFactory.emptyPage();
        }
        warehousesFilterSet = new HashSet<>(intersection);
      } else {
        warehousesFilterSet.addAll(warehouseNamesByOPIDS);
      }
    }
    if (CollUtil.isNotEmpty(warehousesFilterSet)) {
      queryWrapper.in(AfterSalesRegister::getWarehouse, warehousesFilterSet);
    }
    if (Objects.nonNull(query.getSettleTimeStart()) && Objects.nonNull(query.getSettleTimeEnd())) {
      final LocalDateTime settleTimeStart =
          LocalDateTimeUtil.of(Instant.ofEpochSecond(query.getSettleTimeStart()))
              .with(TemporalAdjusters.firstDayOfMonth());
      final LocalDateTime settleTimeEnd =
          LocalDateTimeUtil.of(Instant.ofEpochSecond(query.getSettleTimeEnd()))
              .with(TemporalAdjusters.lastDayOfMonth());
      List<String> settleDurations = getSettleDurations(settleTimeStart, settleTimeEnd);
      queryWrapper.in(
          !CollUtil.isEmpty(settleDurations),
          AfterSalesRegister::getSettleDuration,
          settleDurations);
    }
    if (query.getSettleDurations() != null) {
      List<String> settleDurations = new ArrayList<>();
      for (long settleDuration : query.getSettleDurations()) {
        final LocalDateTime durationStart = DateUtil.toLocalDateTime(settleDuration);
        settleDurations.add(durationStart.format(DatePattern.SIMPLE_MONTH_FORMATTER));
      }
      queryWrapper.in(
          !CollUtil.isEmpty(settleDurations),
          AfterSalesRegister::getSettleDuration,
          settleDurations);
    }
    final IPage<AfterSalesRegister> page = query.getPage();

    if (CollUtil.isNotEmpty(query.getCorpType())) {
      String corpTypeStr =
          query.getCorpType().stream().map(String::valueOf).collect(Collectors.joining(","));
      queryWrapper.exists(
          "SELECT 1 FROM item AS item INNER JOIN biz_level_division AS bld ON item.id = bld.biz_id WHERE bld.type = 0 AND bld.level = 0 AND bld.level_val in ("
              + corpTypeStr
              + ") AND item.code = after_sales_register.item_code "
              + "UNION ALL "
              + "SELECT 1 FROM combination_item AS combination_item INNER JOIN biz_level_division AS bld ON combination_item.id = bld.biz_id WHERE bld.type = 1 AND bld.level = 0 AND bld.level_val in ("
              + corpTypeStr
              + ") AND combination_item.code = after_sales_register.item_code ");
    }
    if (CollUtil.isNotEmpty(query.getBizType())) {
      String bizTypeStr =
          query.getBizType().stream().map(String::valueOf).collect(Collectors.joining(","));
      queryWrapper.exists(
          " SELECT 1 FROM item AS item INNER JOIN biz_level_division AS bld ON item.id = bld.biz_id WHERE bld.type = 0 AND bld.level = 1 AND bld.level_val in ("
              + bizTypeStr
              + ") AND item.code = after_sales_register.item_code "
              + "UNION ALL "
              + "SELECT 1 FROM combination_item AS combination_item INNER JOIN biz_level_division AS bld ON combination_item.id = bld.biz_id WHERE bld.type = 1 AND bld.level = 1 AND bld.level_val in ("
              + bizTypeStr
              + ") AND combination_item.code = after_sales_register.item_code");
    }

    queryWrapper.orderByDesc(AfterSalesRegister::getSettleDuration);
    queryWrapper.orderByDesc(AfterSalesRegister::getPayTime);
    queryWrapper.page(page);
    PageResponse<AfterSalesRegisterPageVO> pageResponse =
        ResponseFactory.ofPage(
            page, AfterSalesRegisterBizServiceImpl::assembleAfterSalesRegisterPageVo);

    Set<String> warehouses =
        pageResponse.getData().stream()
            .map(AfterSalesRegisterPageVO::getWarehouse)
            .collect(Collectors.toSet());
    if (CollUtil.isNotEmpty(warehouses)) {
      Map<String, String> map = warehouseGateway.getOrderPersonnelByNames(warehouses);
      pageResponse
          .getData()
          .forEach(
              val -> val.setOrderPersonnel(map.getOrDefault(val.getWarehouse(), StrUtil.EMPTY)));
    }

    // 业务层级处理
    final List<AfterSalesRegisterPageVO> data = pageResponse.getData();
    if (CollUtil.isNotEmpty(data)) {

      // 单品处理
      final List<AfterSalesRegisterPageVO> skuList =
          data.stream()
              .filter(val -> val.getItemCode() != null && !val.getItemCode().startsWith("MU"))
              .collect(Collectors.toList());
      if (CollUtil.isNotEmpty(skuList)) {
        final List<String> skuCodeList =
            skuList.stream()
                .map(AfterSalesRegisterPageVO::getItemCode)
                .distinct()
                .collect(Collectors.toList());
        Map<String, List<CorpBizTypeDTO>> divisionMap = new HashMap<>(16);
        Map<String, String> divisionStrMap = new HashMap<>(16);

        final Map<String, List<CorpBizTypeDTO>> stringListMap =
            iBizLevelDivisionService.queryBySkuCode(skuCodeList);
        divisionMap.putAll(stringListMap);
        final Map<String, String> stringStringMap =
            iBizLevelDivisionService.queryBizTypeBySkuCode(skuCodeList);
        divisionStrMap.putAll(stringStringMap);

        for (AfterSalesRegisterPageVO vo : skuList) {
          vo.setCorpBizType(divisionMap.get(vo.getItemCode()));
          vo.setCorpBizTypeStr(divisionStrMap.get(vo.getItemCode()));
        }
      }

      // 组合装处理
      final List<AfterSalesRegisterPageVO> combintionItemList =
          data.stream()
              .filter(val -> val.getItemCode() != null && val.getItemCode().startsWith("MU"))
              .distinct()
              .collect(Collectors.toList());
      if (CollUtil.isNotEmpty(combintionItemList)) {
        final List<String> combintionItemCodeList =
            combintionItemList.stream()
                .map(AfterSalesRegisterPageVO::getItemCode)
                .distinct()
                .collect(Collectors.toList());
        final Map<String, Long> cCodeAndIdMap =
            iCombinationItemService
                .lambdaQuery()
                .in(CombinationItem::getCode, combintionItemCodeList)
                .list()
                .stream()
                .collect(Collectors.toMap(CombinationItem::getCode, CombinationItem::getId));

        final List<BizLevelDivision> list =
            iBizLevelDivisionService
                .lambdaQuery()
                .in(BizLevelDivision::getBizId, cCodeAndIdMap.values())
                .eq(BizLevelDivision::getType, BizUnionTypeEnum.COMBINATION)
                .list();
        if (CollUtil.isNotEmpty(list)) {
          Map<Long, List<Integer>> combiantionCorpTypeMap =
              list.stream()
                  .filter(val -> val.getLevel().equals(DivisionLevelEnum.COOPERATION))
                  .collect(
                      Collectors.groupingBy(
                          BizLevelDivision::getBizId,
                          Collectors.mapping(
                              s -> s.getLevelVal().getValue(), Collectors.toList())));
          Map<Long, List<Integer>> combiantionBizTypeMap =
              list.stream()
                  .filter(val -> val.getLevel().equals(DivisionLevelEnum.BUSINESS_TYPE))
                  .collect(
                      Collectors.groupingBy(
                          BizLevelDivision::getBizId,
                          Collectors.mapping(
                              s -> s.getLevelVal().getValue(), Collectors.toList())));
          for (AfterSalesRegisterPageVO vo : combintionItemList) {
            final Long combinationId = cCodeAndIdMap.get(vo.getItemCode());
            if (Objects.nonNull(combinationId)) {
              vo.setCorpType(
                  combiantionCorpTypeMap.getOrDefault(combinationId, Collections.emptyList()));
              vo.setBizType(
                  combiantionBizTypeMap.getOrDefault(combinationId, Collections.emptyList()));
              final String format =
                  String.format(
                      "合作方：%s；业务类型：%s",
                      vo.getCorpType().stream()
                          .map(val -> DivisionLevelValueEnum.valueOf(val).getDesc())
                          .collect(Collectors.joining(",")),
                      vo.getBizType().stream()
                          .map(val -> DivisionLevelValueEnum.valueOf(val).getDesc())
                          .collect(Collectors.joining(",")));
              vo.setCorpBizTypeStr(format);
            }
          }
        }
      }
    }

    return pageResponse;
  }

  @NonNull
  private static List<String> getSettleDurations(
      LocalDateTime settleTimeStart, LocalDateTime settleTimeEnd) {
    List<String> settleDurations = new ArrayList<>();
    final long monthDiff = ChronoUnit.MONTHS.between(settleTimeStart, settleTimeEnd) + 1;
    for (long i = 0; i < monthDiff; i++) {
      final LocalDateTime monthLocalTime = settleTimeStart.plusMonths(i).withDayOfMonth(1);
      settleDurations.add(monthLocalTime.format(DatePattern.SIMPLE_MONTH_FORMATTER));
    }
    return settleDurations;
  }

  @NonNull
  private static AfterSalesRegisterPageVO assembleAfterSalesRegisterPageVo(AfterSalesRegister po) {
    final AfterSalesRegisterPageVO afterSalesRegisterPageVO =
        AfterSalesRegisterConverter.SHARE.po2pageVo(po);
    final YearMonth yearMonth =
        YearMonth.parse(po.getSettleDuration(), DatePattern.SIMPLE_MONTH_FORMATTER);
    final DateTimeFormatter normDateFormatter = DatePattern.NORM_DATE_FORMATTER;
    final String settleDurationDateStart = yearMonth.atDay(1).format(normDateFormatter);
    final String settleDurationDateEnd = yearMonth.atEndOfMonth().format(normDateFormatter);
    afterSalesRegisterPageVO.setSettleTimeEnd(settleDurationDateEnd);
    afterSalesRegisterPageVO.setSettleTimeStart(settleDurationDateStart);
    final DateTimeFormatter normMonthFormatter = DatePattern.NORM_MONTH_FORMATTER;
    afterSalesRegisterPageVO.setSettleMonth(yearMonth.format(normMonthFormatter));

    //        final String itemCode = po.getItemCode();
    //        if(itemCode.startsWith("MU")){
    //            final List<BizLevelDivision> list = iBizLevelDivisionService.lambdaQuery()
    //                    .eq(BizLevelDivision::getBizCode, po.getItemCode())
    //                    .eq(BizLevelDivision::getType, BizUnionTypeEnum.COMBINATION)
    //                    .list();
    //
    //        }

    //        Integer businessLine = po.getBusinessLine();
    // 业务线字段改造
    //        DivisionLevelValueEnum valueEnum = IEnum.getEnumByValue(DivisionLevelValueEnum.class,
    // businessLine);
    //        afterSalesRegisterPageVO.setBusinessLineStr(Objects.isNull(valueEnum) ? "" :
    // valueEnum.getDesc());
    //        BusinessLine enumByValue = IEnum.getEnumByValue(BusinessLine.class, businessLine);
    //        afterSalesRegisterPageVO.setBusinessLineStr(Objects.isNull(enumByValue) ? "" :
    // enumByValue.getDesc());

    return afterSalesRegisterPageVO;
  }

  @Override
  public SingleResponse<Boolean> export(AfterSalesRegisterPageQuery query) {
    //
    // query.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(query.getBusinessLine()));
    //        Assert.isTrue(CollUtil.isNotEmpty(query.getBusinessLine()), "无数据权限，无法导出");

    ExportTask exportTask = new ExportTask();
    exportTask.setStatus(ExportTaskStatus.RUNNING);
    exportTask.setName("客服售后登记表-" + DateUtil.formatNow(DatePattern.PURE_DATETIME_PATTERN));
    exportTask.setType(ExportTaskType.AFTER_SALES_REGISTER);
    final Long taskId = exportTaskGateway.saveOrUpdateExportTask(exportTask);
    exportTask.setId(taskId);

    ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL)
        .execute(
            () -> {
              try (final ByteArrayOutputStream byteArrayOutputStream =
                  new ByteArrayOutputStream()) {
                query.setPageSize(999999);
                final PageResponse<AfterSalesRegisterPageVO> response = pageQuery(query);
                final List<AfterSalesRegisterPageVO> data = response.getData();
                EasyExcel.write(byteArrayOutputStream, AfterSalesRegisterPageVO.class)
                    .sheet("工作表1")
                    .doWrite(data);

                UploadFileAction action =
                    UploadFileAction.ofInputStream(
                        new ByteArrayInputStream(byteArrayOutputStream.toByteArray()),
                        exportTask.getName() + ".xlsx");
                final String downloadUrl = fileGateway.uploadFile(action).getUrl();

                exportTask.setStatus(ExportTaskStatus.SUCCESS);
                exportTask.setDownloadUrl(downloadUrl);
              } catch (Exception e) {
                log.error("导出【客服售后登记表】异常", e);
                exportTask.setError(e.getMessage());
                exportTask.setStatus(ExportTaskStatus.FAIL);
                exportTask.setError(e.getMessage());
              } finally {
                exportTaskGateway.saveOrUpdateExportTask(exportTask);
              }
            });
    return SingleResponse.of(true);
  }

  @Override
  public MultiResponse<String> shopDropdown(String name) {
    final List<String> shopList = Objects.requireNonNull(shopNamesCache.get(""), "店铺列表查询异常");
    if (StrUtil.isBlank(name)) {
      return MultiResponse.of(shopList);
    }
    final List<String> shopNamesMatched =
        shopList.stream()
            .filter(v -> v.contains(name) || name.contains(v))
            .collect(Collectors.toList());
    return MultiResponse.of(shopNamesMatched);
  }

  private final LoadingCache<String, List<String>> shopNamesCache =
      Caffeine.newBuilder().expireAfterWrite(600, TimeUnit.SECONDS).build(k -> this.getShopNames());

  private final BanniuCommonService banniuCommonService;

  @NonNull
  private List<String> getShopNames() {
    return banniuCommonService.shops().stream()
        .map(SellerListResponse.Seller::getTitle)
        .collect(Collectors.toList());
  }
}
