package com.daddylab.supplier.item.application.aws.service.impl;

import com.daddylab.supplier.item.application.aws.service.AwsBusinessFinishPredicate;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.domain.otherPay.enums.OtherPayStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockOutOrderState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherPay;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockOutOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PurchaseOrderState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OtherPayMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockOutOrderMapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022/6/28
 */
@Component
public class AwsBusinessFinishPredicateImpl implements AwsBusinessFinishPredicate {

    @Autowired
    ItemDrawerMapper itemDrawerMapper;

    @Autowired
    PurchaseOrderMapper purchaseOrderMapper;

    @Autowired
    StockOutOrderMapper stockOutOrderMapper;

    @Autowired
    OtherPayMapper otherPayMapper;

    @Override
    public Boolean isFinished(Long businessId, PurchaseTypeEnum type) {
        switch (type) {
            case PURCHASE_ORDER:
            case SYSTEM_PURCHASE_ORDER:
                final PurchaseOrder purchaseOrder = purchaseOrderMapper.selectById(businessId);
                if (purchaseOrder != null) {
                    //不是待审核则认为流程已结束
                    return !PurchaseOrderState.WAIT_CHECK.getValue()
                            .equals(purchaseOrder.getState());
                }
            case OUT_STOCK_PAYABLE:
                final StockOutOrder stockOutOrder = stockOutOrderMapper.selectById(businessId);
                if (stockOutOrder != null) {
                    //不是待审核则认为流程已结束
                    return !StockOutOrderState.NO_AUDIT.getValue().equals(stockOutOrder.getState());
                }
            case OTHER_PAYABLE:
                final OtherPay otherPay = otherPayMapper.selectById(businessId);
                if (otherPay != null) {
                    //不是待审核则认为流程已结束
                    return !OtherPayStatus.WAIT_AUDIT.getValue().equals(otherPay.getStatus());
                }
            case ITEM_LIBRARY_AUDIT:
                final Integer itemLaunchStatus = itemDrawerMapper.getItemLaunchStatus(businessId);
                if (itemLaunchStatus != null) {
                    //状态不是待审核、待修改，则认为流程已结束
                    //Ver1.6.3(2021-08-05):流程变更，现在待修改移动到待审核状态之后，所以现在不是待审核即可以是为流程结束
                    return !Objects.equals(ItemLaunchStatus.TO_BE_AUDITED.getValue(), itemLaunchStatus);
                }
        }
        return null;
    }

    @Override
    public Boolean isHidden(Long businessId, PurchaseTypeEnum type) {
        switch (type) {
            case PURCHASE_ORDER:
            case SYSTEM_PURCHASE_ORDER:
                final PurchaseOrder purchaseOrder = purchaseOrderMapper.selectById(businessId);
                if (purchaseOrder != null) {
                    return PurchaseOrderState.REJECT_AUDIT
                            .getValue()
                            .equals(purchaseOrder.getState());
                }
        }
        return false;
    }
}
