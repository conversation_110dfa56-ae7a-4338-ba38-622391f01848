package com.daddylab.supplier.item.infrastructure.gatewayimpl.user;

import com.alibaba.cola.dto.PageQuery;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StaffInfoQuery extends PageQuery {

    private static final long serialVersionUID = 2987545622729034091L;

    @JsonProperty("user_ids")
    private List<Long> userIds;

    @JsonProperty("real_name")
    private String realName;
}
