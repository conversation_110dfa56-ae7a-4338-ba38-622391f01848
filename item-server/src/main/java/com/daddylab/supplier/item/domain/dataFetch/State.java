package com.daddylab.supplier.item.domain.dataFetch;

/**
 * <AUTHOR>
 * @since 2022/5/11
 */
enum State {
    /**
     * 初始状态
     */
    INITIAL,

    /**
     * 初始化中
     */
    INITIALIZE,

    /**
     * 运行中
     */
    RUNNING,

    /**
     * 暂停
     */
    PAUSE,

    /**
     * 因当前实例已超过配置的最大实例数导致初始化挂起
     */
    SUSPEND_INITIALIZE,

    /**
     * 因为减少了最大实例数配置导致当前运行中的实例挂起
     */
    SUSPEND_FOR_SCALE,

    /**
     * 因配置修改为禁用而暂时挂起
     */
    SUSPEND_FOR_DISABLED,

    /**
     * 因注册ID丢失挂起
     */
    SUSPEND_FOR_MISSING_REGISTER,

    /**
     * 已停止
     */
    STOPPED
}
