package com.daddylab.supplier.item.controller.purchaseDissent.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @ClassName PurchaseDissentCmd.java
 * @description
 * @createTime 2021年11月17日 13:55:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("采购异议新增实体")
public class PurchaseDissentCmd extends Command {
    private static final long serialVersionUID = -8408609056804984163L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 采购id
     */
    @ApiModelProperty(value = "采购id")
    private Long purchaseId;

    /**
     * 异议内容
     */
    @ApiModelProperty(value = "异议内容")
    private String content;

    /**
     * token
     */
    @ApiModelProperty(value = "token")
    private String token;

    /**
     * code
     */
    @ApiModelProperty(value = "code")
    private String code;
}
