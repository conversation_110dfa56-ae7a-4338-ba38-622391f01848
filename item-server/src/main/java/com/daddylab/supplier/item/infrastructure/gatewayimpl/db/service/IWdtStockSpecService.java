package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtStockSpec;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtStockSpecMapper;
import com.daddylab.supplier.item.types.stockSpec.WarehouseStockSpecStatistic;
import com.daddylab.supplier.item.types.stockStatistic.WdtStockSpecStatisticQuery;

import java.util.Map;
import java.util.Optional;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface IWdtStockSpecService extends IDaddylabServicePlus<WdtStockSpec, WdtStockSpecMapper> {

    Optional<WdtStockSpec> selectOneByWarehouseNoAndSpecNo(String warehouseNo, String skuNo);

    Map<String, WarehouseStockSpecStatistic> statistics(WdtStockSpecStatisticQuery query);

}
