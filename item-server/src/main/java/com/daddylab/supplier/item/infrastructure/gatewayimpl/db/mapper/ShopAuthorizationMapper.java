package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;

/**
 * <p>
 * 店铺授权 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
public interface ShopAuthorizationMapper extends DaddyBaseMapper<ShopAuthorization> {

}
