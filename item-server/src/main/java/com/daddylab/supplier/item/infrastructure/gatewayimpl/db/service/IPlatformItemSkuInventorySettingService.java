package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSkuInventorySetting;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemSkuInventorySettingMapper;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 平台商品SKU库存设置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface IPlatformItemSkuInventorySettingService extends IDaddylabServicePlus<PlatformItemSkuInventorySetting, PlatformItemSkuInventorySettingMapper> {

    int saveOrUpdateBatchByOuterId(Collection<PlatformItemSkuInventorySetting> platformItemSkuInventorySettings);

    List<PlatformItemSkuInventorySetting> selectByShopNoAndSkuCode(String shopNo, String outerSkuCode);
}
