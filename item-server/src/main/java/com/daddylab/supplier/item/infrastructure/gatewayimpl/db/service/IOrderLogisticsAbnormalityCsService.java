package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsAbnormalityCs;
import java.util.List;

/**
 * 订单物流异常客服表 服务类
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
public interface IOrderLogisticsAbnormalityCsService
    extends IDaddyService<OrderLogisticsAbnormalityCs> {

  List<OrderLogisticsAbnormalityCs> listByAbnormalityId(Long abnormalityId);

  List<OrderLogisticsAbnormalityCs> listByAbnormalityIds(List<Long> abnormalityIds);

  /**
   * 分配客服（默认非初始化分配）
   *
   * @param abnormalId 异常ID
   * @param csUserIds 客服ID
   */
  void distribute(Long abnormalId, List<Long> csUserIds);

  /**
   * 分配客服
   *
   * @param abnormalId 异常ID
   * @param csUserIds 客服ID
   * @param init 是否为初始化分配，若是，则仅在当前未分配客服时继续修改
   */
  void distribute(Long abnormalId, List<Long> csUserIds, boolean init);
}
