package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ReturnModeEnum {

    /**
     * 退料方式
     */
    RETURN_AND_REPLENISH(1, "退料补料"),
    RETURN_AND_DEDUCT_MONEY(2, "退料并扣款"),
    ;

    private final Integer value;
    private final String desc;

    public static String getDesc(Integer type) {
        for (ReturnModeEnum returnModeEnum : ReturnModeEnum.values()) {
            if (returnModeEnum.getValue().equals(type)) {
                return returnModeEnum.getDesc();
            }
        }
        return "";
    }

}
