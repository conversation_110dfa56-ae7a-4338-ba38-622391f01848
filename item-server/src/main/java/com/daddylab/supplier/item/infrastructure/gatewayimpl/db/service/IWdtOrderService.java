package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderMapper;

import java.util.List;
import java.util.Optional;

/**
 * 旺店通订单数据 服务类
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
public interface IWdtOrderService extends IDaddylabServicePlus<WdtOrder, WdtOrderMapper> {
    Optional<WdtOrder> getByTradeId(Long wdtTradeId);

    List<WdtOrder> listBySrcTid(String srcTid);
}
