package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;

import javax.annotation.Nullable;
import java.util.Optional;

/**
 * <p>
 * 商品抽屉表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
public interface IItemDrawerService extends IDaddyService<ItemDrawer> {

    /**
     * 主键查询
     *
     * @param id 主键
     * @return ItemDrawer
     */
    ItemDrawer getById(Long id);

    default Long getItemDrawerIdByItemId(Long itemId) {
        return getItemDrawerIdByItemId(itemId, false);
    }

    Optional<ItemDrawer> getByItemId(Long itemId);

    ItemDrawer getOrCreate(Long itemId);

    @Nullable
    Long getItemDrawerIdByItemId(Long itemId, Boolean noCache);
    
    Optional<ItemDrawer> getFirstByWechatId(String wechatId);
}
