package com.daddylab.supplier.item.application.salesInStock.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年10月23日 11:25 AM
 */
@Data
@ApiModel("销退入库单详情返回封装")
public class SalesInStockDetailVO {

    @ApiModelProperty("销退入库单基础信息")
    private SalesInStockBaseVO baseVO;

    @ApiModelProperty("退换单基础信息")
    private SalesInStockRefundBaseVO refundBaseVO;

    @ApiModelProperty("销退入库单-入库单明细")
    private List<SalesInStockDetailListVO> baseListVO;

}
