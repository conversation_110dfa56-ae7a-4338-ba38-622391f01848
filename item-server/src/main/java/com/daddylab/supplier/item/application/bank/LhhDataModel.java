package com.daddylab.supplier.item.application.bank;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LhhDataModel {
    private String address;
    private String createTime;
    private Integer bankId;
    private String hanghao;
    private String tel;
    private Integer id;
    private String bankname;
    private Integer areaPid;
    private Integer areaId;
}
