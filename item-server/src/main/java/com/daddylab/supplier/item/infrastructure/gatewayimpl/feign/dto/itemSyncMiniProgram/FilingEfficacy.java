package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.itemSyncMiniProgram;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/11/3
 */
@Data
public class FilingEfficacy {
    @ApiModelProperty(value = "产品备案功效")
    private String efficacy;
    @ApiModelProperty(value = "防晒指数")
    private String sunProtectionIndex;
    @ApiModelProperty(value = "PA值")
    private String paValue;
    @ApiModelProperty(value = "序号")
    private Integer sort;
}
