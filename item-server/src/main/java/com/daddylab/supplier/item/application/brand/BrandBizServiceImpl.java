package com.daddylab.supplier.item.application.brand;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.goods.GoodsAPI;
import com.daddylab.mall.wdtsdk.apiv2.goods.GoodsBrandAPI;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsBrandSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsBrandSearchResponse;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsPushGoodsInfo;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsPushSpecInfo;
import com.daddylab.supplier.item.application.brand.event.BrandChangeEvent;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.provider.ProviderBizService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.EntityChange;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.EnableStatusEnum;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.trans.CommaSerialTransMapper;
import com.daddylab.supplier.item.common.trans.PageTransMapper;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.brand.dto.*;
import com.daddylab.supplier.item.domain.brand.entity.BrandEntity;
import com.daddylab.supplier.item.domain.brand.gateway.BrandGateway;
import com.daddylab.supplier.item.domain.brand.trans.BrandTransMapper;
import com.daddylab.supplier.item.domain.brand.vo.BrandProviderVo;
import com.daddylab.supplier.item.domain.exportTask.ExportTaskGateway;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.functions.Function4;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Brand;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BusinessLine;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLock;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.inject.Inject;
import java.io.*;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/9 4:37 下午
 * @description
 */
@Service
@Slf4j
public class BrandBizServiceImpl implements BrandBizService {

  @Autowired ProviderBizService providerBizService;
  @Inject private BrandGateway brandGateway;
  @Inject private ItemBizService itemBizService;
  @Inject private OperateLogDomainService operateLogDomainService;

  @Value("${files.import-brand-template}")
  private String excelTemplateUrl;

  @Inject private ExportTaskGateway exportTaskGateway;
  @Inject private FileGateway fileGateway;

  @Autowired private WdtGateway wdtGateway;

  @Autowired private ProviderGateway providerGateway;

  private final CommaSerialTransMapper commaSerialTransMapper =
      Mappers.getMapper(CommaSerialTransMapper.class);

  @Override
  public String getExcelTemplateUrl() {
    return StringUtil.isNotBlank(excelTemplateUrl)
        ? excelTemplateUrl
        : "https://cdn-test.daddylab.com/Upload/supplier/item/品牌导入导出模板-1636097871934.xlsx";
  }

  @Override
  public MultiResponse<BrandDropDownItem> dropDownList(BrandDropDownQuery query) {
    query.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(query.getBusinessLine()));
    if (CollUtil.isEmpty(query.getBusinessLine())) {
      return MultiResponse.of(new ArrayList<>());
    }
    List<BrandDropDownItem> brandDropDownItems = brandGateway.dropDownList(query);
    for (BrandDropDownItem brandDropDownItem : brandDropDownItems) {
      brandDropDownItem.setStandard(
          EnableStatusEnum.ON.equals(brandDropDownItem.getStatus()) ? "ON" : "OFF");
    }
    return MultiResponse.of(brandDropDownItems);
  }

  @Override
  public MultiResponse<BrandListItem> queryBrandList(BrandQuery brandQuery) {
    brandQuery.setBusinessLine(
        UserPermissionJudge.filterBusinessLinePerm(brandQuery.getBusinessLine()));
    if (CollUtil.isEmpty(brandQuery.getBusinessLine())) {
      return MultiResponse.of(Collections.emptyList());
    }
    final List<BrandListItem> data = brandGateway.queryBrandList(brandQuery);
    fillIsBlacklist(data);
    return MultiResponse.of(data);
  }

  private void fillIsBlacklist(List<BrandListItem> data) {
    if (CollectionUtil.isEmpty(data)) {
      return;
    }
    final List<BrandProviderVo> brandProviderVos =
        data.stream().flatMap(x -> x.getProviders().stream()).collect(Collectors.toList());
    fillIsBlacklistForBrandProviderVos(brandProviderVos);
  }

  private void fillIsBlacklistForBrandProviderVos(List<BrandProviderVo> brandProviderVos) {
    final Set<Long> partnerProviderIds =
        brandProviderVos.stream()
            .map(BrandProviderVo::getPartnerProviderId)
            .collect(Collectors.toSet());
    if (!partnerProviderIds.isEmpty()) {
      final Map<Long, PartnerProviderResp> partnerProviderMap =
          providerGateway.partnerBatchQueryByIds(partnerProviderIds);
      Consumer<BrandProviderVo> setIsBlacklist =
          brandProviderVo ->
              brandProviderVo.setIsBlacklist(
                  Optional.ofNullable(brandProviderVo.getPartnerProviderId())
                      .filter(
                          com.daddylab.supplier.item.infrastructure.utils.NumberUtil::isPositive)
                      .map(partnerProviderMap::get)
                      .map(PartnerProviderResp::getIsBlacklist)
                      .orElse(0));
      brandProviderVos.forEach(setIsBlacklist);
    }
  }

  @Override
  @SuppressWarnings("unchecked")
  public PageResponse<BrandListItem> pageQueryBrandList(BrandQuery brandQuery) {
    brandQuery.setBusinessLine(
        UserPermissionJudge.filterBusinessLinePerm(brandQuery.getBusinessLine()));
    if (CollUtil.isEmpty(brandQuery.getBusinessLine())) {
      return ResponseFactory.emptyPage();
    }
    final Page<BrandListItem> page = brandGateway.pageQueryBrandList(brandQuery);
    fillIsBlacklist(page.getRecords());
    return PageTransMapper.INSTANCE.toPageResponse(page);
  }

  @Override
  public SingleResponse<BrandEntity> getBrand(Long id) {
    return Optional.ofNullable(brandGateway.getBrand(id))
        .map(SingleResponse::of)
        .orElseThrow(() -> ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "品牌不存在"));
  }

  @Override
  public SingleResponse<BrandDetail> getBrandDetail(Long id) {
    return Optional.ofNullable(brandGateway.getBrand(id))
        .map(this::toBrandDetail)
        .map(SingleResponse::of)
        .orElseThrow(() -> ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "品牌不存在"));
  }

  @Override
  public Response updateBrandStatus(Long id, EnableStatusEnum status) {
    brandGateway.updateBrandStatus(id, status);
    return Response.buildSuccess();
  }

  private void checkNameConflict(BrandEntity brandEntity, Long exceptId) {
    if (brandGateway.existSameName(brandEntity.getName(), exceptId)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "品牌名称重复");
    }
  }

  @Override
  public Response deleteBrand(Long id) {
    if (!canDelete(id)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "存在关联商品，不允许被删除");
    }
    brandGateway.deleteBrand(id);
    return Response.buildSuccess();
  }

  private BrandDetail toBrandDetail(BrandEntity brandEntity) {
    final List<Long> providerIds = brandEntity.getProviderIds();
    final List<Provider> providers = providerBizService.batchQueryProvider(providerIds);
    final Map<Long, Provider> providerMap =
        providers.stream().collect(Collectors.toMap(Entity::getId, Function.identity()));
    final ArrayList<BrandProviderVo> brandProviderVos = new ArrayList<>();
    for (Long providerId : providerIds) {
      final Provider provider = providerMap.get(providerId);
      if (provider == null) {
        continue;
      }
      final BrandProviderVo brandProviderVo = new BrandProviderVo();
      brandProviderVo.setProviderId(providerId);
      brandProviderVo.setProviderName(provider.getName());
      brandProviderVo.setPartnerProviderId(provider.getPartnerProviderId());
      brandProviderVos.add(brandProviderVo);
    }
    fillIsBlacklistForBrandProviderVos(brandProviderVos);
    return BrandTransMapper.INSTANCE.toBrandDetail(brandEntity, brandProviderVos);
  }

  @Override
  @Transactional
  // 直接加个粒度比较大的锁来避免品牌编号的竞争，因为预期调用频率比较低，这是出于实现方便考虑，后面再考虑优化
  @DistributedLock
  public SingleResponse<BrandDetail> createOrUpdateBrand(CreateOrUpdateBrandCmd cmd) {
    final String brandSn = syncBrandForWdt(cmd.getName());
    cmd.setSn(brandSn);
    final EntityChange<BrandEntity> brandChange;
    if (Objects.isNull(cmd.getId())) {
      final BrandEntity brand = BrandTransMapper.INSTANCE.toBrandEntity(cmd);
      checkNameConflict(brand, null);
      brandGateway.createEntity(brand);
      brandChange = EntityChange.ofAdd(brand);
    } else {
      final BrandEntity oldBrand = brandGateway.getBrand(cmd.getId());
      final BrandEntity newBrand = BrandTransMapper.INSTANCE.copy(oldBrand);
      BrandTransMapper.INSTANCE.copyFromCmd(newBrand, cmd);
      checkNameConflict(newBrand, oldBrand.getId());
      brandChange = DiffUtil.diffEntity(oldBrand, newBrand);
      brandGateway.updateEntity(brandChange);
    }
    final BrandEntity brand = brandChange.getNewEntity();
    if (brandChange.isAdd()) {
      EventBusUtil.post(BrandChangeEvent.ofNew(UserContext.getUserId(), brand.getId(), brand));
    } else {
      EventBusUtil.post(
          BrandChangeEvent.ofUpdate(UserContext.getUserId(), brand.getId(), brandChange));
    }
    return SingleResponse.of(toBrandDetail(brand));
  }

  private String syncBrandForWdt(String name) {
    try {
      final GoodsAPI api = wdtGateway.getAPI(GoodsAPI.class);
      final GoodsPushGoodsInfo goodsInfo = new GoodsPushGoodsInfo();
      goodsInfo.setGoodsNo("SP1690441366");
      goodsInfo.setGoodsName("特殊商品用于同步品牌（请勿删除）");
      goodsInfo.setBrandName(name);
      goodsInfo.setAutoCreateBc(true);
      final ArrayList<GoodsPushSpecInfo> specInfoList = new ArrayList<>();
      final GoodsPushSpecInfo goodsPushSpecInfo = new GoodsPushSpecInfo();
      goodsPushSpecInfo.setSpecNo("SP1690441366-01");
      goodsPushSpecInfo.setSpecName("特殊商品用于同步品牌（请勿删除）");
      specInfoList.add(goodsPushSpecInfo);
      api.push(goodsInfo, specInfoList);

      final GoodsBrandAPI brandAPI = wdtGateway.getAPI(GoodsBrandAPI.class);
      final Pager pager = new Pager(1, 0, false);
      final GoodsBrandSearchParams params = new GoodsBrandSearchParams();
      params.setBrandName(name);
      final GoodsBrandSearchResponse brandSearchResponse = brandAPI.search(params, pager);
      Assert.notEmpty(brandSearchResponse.getDetailList(), "推送品牌失败");
      return brandSearchResponse.getDetailList().get(0).getBrandNo();
    } catch (WdtErpException e) {
      throw WdtExceptions.wrapBizException(e);
    }
  }

  @Override
  public String generateBrandSn(String name) {
    return "B"
        + DateUtil.formatNow(DatePattern.PURE_DATE_PATTERN)
        + StringUtil.format("{0,number,00}", brandGateway.getNextSerialNum());
  }

  // 不存在关联商品则可以删除
  @Override
  public boolean canDelete(Long id) {
    return !itemBizService.existBrandAssociativeItem(id);
  }

  @Override
  public boolean importExcel(InputStream inputStream) {
    final List<WdtBrandSheetItem> exportItems =
        EasyExcel.read(inputStream)
            .headRowNumber(3) // Excel模板数据从第三行开始
            .head(WdtBrandSheetItem.class)
            .sheet()
            .doReadSync();
    if (exportItems.isEmpty()) {
      throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "导入Excel文件为空");
    }
    handleImportItems(exportItems);
    return true;
  }

  // 品牌编号  品牌名称	备注	是否停用	最后修改时间	创建时间
  @Override
  public boolean importWdtCsv(InputStream inputStream) {
    try {
      final CsvReadConfig csvReadConfig = new CsvReadConfig();
      csvReadConfig.setContainsHeader(true);
      csvReadConfig.setSkipEmptyRows(true);
      csvReadConfig.setErrorOnDifferentFieldCount(true);
      csvReadConfig.setHeaderAlias(
          ImmutableMap.of(
              "品牌编号", "sn",
              "品牌名称", "name",
              "是否停用", "isDisable"));
      final CsvReader reader = CsvUtil.getReader(csvReadConfig);
      final BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
      final List<WdtBrandSheetItem> wdtBrandSheetItems = new ArrayList<>();
      reader.read(
          bufferedReader,
          row -> {
            final WdtBrandSheetItem wdtBrandSheetItem = row.toBean(WdtBrandSheetItem.class);
            wdtBrandSheetItems.add(wdtBrandSheetItem);
          });
      handleImportItems(wdtBrandSheetItems);
      return true;
    } catch (Exception e) {
      log.error("导入旺店通CSV文件异常", e);
      return false;
    }
  }

  private void handleImportItems(List<WdtBrandSheetItem> exportItems) {
    final int batchSize = 500;
    final ArrayList<Brand> brandBuf = new ArrayList<>(batchSize);
    final Map<String, Boolean> addSnMap = new HashMap<>(batchSize);
    final Map<String, Boolean> addNameMap = new HashMap<>(batchSize);
    final ArrayList<OperateLog> operateLogBuf = new ArrayList<>(batchSize);
    final Function4<Long, OperateLogTarget, Long, String, OperateLog> operateLogBuild =
        (Long operatorId, OperateLogTarget target, Long targetId, String msg) -> {
          final OperateLog operateLog = new OperateLog();
          operateLog.setTargetType(target);
          operateLog.setTargetId(String.valueOf(targetId));
          operateLog.setMsg(msg);
          operateLog.setOperatorId(operatorId);
          return operateLog;
        };

    for (List<WdtBrandSheetItem> wdtBrandSheetItems : CollUtil.split(exportItems, batchSize)) {
      final List<String> names =
          wdtBrandSheetItems.stream().map(WdtBrandSheetItem::getName).collect(Collectors.toList());
      final Map<String, Brand> existBrands = brandGateway.batchQueryBrandByName(names);

      for (WdtBrandSheetItem exportItem : wdtBrandSheetItems) {
        if (StringUtil.isBlank(exportItem.getName())) {
          log.warn("名称为空 {}", exportItem);
          continue;
        }

        String businessLinesStr = "";
        final List<DivisionLevelValueEnum> divisionLevelValueEnums =
            DivisionLevelValueEnum.valueOfCorpTypeNameCommaStr(exportItem.getBusinessLine());
        if (divisionLevelValueEnums != null) {
          businessLinesStr =
              divisionLevelValueEnums.stream()
                  .map(DivisionLevelValueEnum::getValue)
                  .map(Objects::toString)
                  .collect(Collectors.joining(","));
        }

        if (addSnMap.getOrDefault(exportItem.getSn(), false)) {
          log.info("当前表格存在编号相同的品牌 {} {}", exportItem.getName(), exportItem.getSn());
          continue;
        }

        if (addNameMap.getOrDefault(exportItem.getName(), false)) {
          log.info("当前表格存在名称相同的品牌 {} {}", exportItem.getName(), exportItem.getSn());
          continue;
        }

        final EnableStatusEnum itemStatus =
            StringUtil.equals(exportItem.getIsDisable(), "是")
                ? EnableStatusEnum.OFF
                : EnableStatusEnum.ON;
        final Brand existBrand = existBrands.get(exportItem.getName());
        if (existBrand != null) {

          final ArrayList<Object> changeItems = CollUtil.newArrayList();
          Brand updateBrand = new Brand();
          updateBrand.setId(existBrand.getId());
          updateBrand.setBusinessLine(businessLinesStr);

          if (brandGateway.existSn(exportItem.getSn())) {
            log.info(
                "存在编号相同的品牌 {} {} {}", existBrand.getId(), existBrand.getName(), existBrand.getSn());
            continue;
          }

          if (!StringUtil.equals(exportItem.getSn(), existBrand.getSn())) {
            changeItems.add("品牌编号");
            updateBrand.setSn(exportItem.getSn());
          }

          if (!Objects.equals(itemStatus, existBrand.getStatus())) {
            changeItems.add("状态");
            updateBrand.setStatus(itemStatus);
          }

          if (changeItems.isEmpty()) {
            log.info(
                "品牌数据无变更 {} {} {}", existBrand.getId(), existBrand.getName(), existBrand.getSn());
            continue;
          }

          brandBuf.add(updateBrand);
          addSnMap.put(updateBrand.getSn(), true);
          addNameMap.put(updateBrand.getName(), true);
          operateLogBuf.add(
              operateLogBuild.apply(
                  UserContext.getUserId(),
                  OperateLogTarget.BRAND,
                  existBrand.getId(),
                  "通过文件导入修改了" + Joiner.on(',').join(changeItems)));
        } else {

          if (brandGateway.existSn(exportItem.getSn())) {
            log.info("存在编号相同的品牌 {} {}", exportItem.getName(), exportItem.getSn());
            continue;
          }

          final Brand brand = new Brand();
          brand.setSn(exportItem.getSn());
          brand.setName(exportItem.getName());
          brand.setStatus(itemStatus);
          brand.setBusinessLine(businessLinesStr);
          brandBuf.add(brand);

          log.info("新增品牌 {} {}", brand.getName(), brand.getSn());

          addSnMap.put(brand.getSn(), true);
          addNameMap.put(brand.getName(), true);

          operateLogBuf.add(
              operateLogBuild.apply(
                  UserContext.getUserId(), OperateLogTarget.BRAND, brand.getId(), "通过文件导入新增了品牌"));
        }
      }

      if (!brandBuf.isEmpty()) {
        brandGateway.batchSaveOrUpdateBrandPo(brandBuf);
        brandBuf.clear();
      }

      if (!operateLogBuf.isEmpty()) {
        operateLogDomainService.batchAddOperateLog(operateLogBuf);
        operateLogBuf.clear();
      }
    }
  }

  @Override
  public void exportExcel(BrandQuery brandQuery, OutputStream outputStream) {
    final List<WdtBrandSheetItem> wdtBrandSheetItems = queryWdtBrandExportList(brandQuery);
    EasyExcel.write(outputStream, WdtBrandSheetItem.class)
        .sheet("工作表1")
        .doWrite(wdtBrandSheetItems);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public long exportExcel(BrandQuery brandQuery) {
    // 初始化一条导出任务
    ExportTask exportTask = new ExportTask();
    exportTask.setStatus(ExportTaskStatus.RUNNING);
    exportTask.setName("品牌导出-" + DateUtil.formatNow(DatePattern.PURE_DATETIME_PATTERN));
    exportTask.setType(ExportTaskType.BRAND);
    final Long taskId = exportTaskGateway.saveOrUpdateExportTask(exportTask);
    exportTask.setId(taskId);

    brandQuery.setBusinessLine(
        UserPermissionJudge.filterBusinessLinePerm(brandQuery.getBusinessLine()));

    ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL)
        .execute(
            () -> {
              try (final ByteArrayOutputStream byteArrayOutputStream =
                  new ByteArrayOutputStream()) {
                brandQuery.setPageSize(999999);
                final List<WdtBrandSheetItem> wdtBrandSheetItems =
                    queryWdtBrandExportList(brandQuery);
                EasyExcel.write(byteArrayOutputStream, WdtBrandSheetItem.class)
                    .sheet("工作表1")
                    .doWrite(wdtBrandSheetItems);

                UploadFileAction action =
                    UploadFileAction.ofInputStream(
                        new ByteArrayInputStream(byteArrayOutputStream.toByteArray()),
                        exportTask.getName() + ".xlsx");
                final String downloadUrl = fileGateway.uploadFile(action).getUrl();

                exportTask.setStatus(ExportTaskStatus.SUCCESS);
                exportTask.setDownloadUrl(downloadUrl);
              } catch (Exception e) {
                log.error("导出品牌异常", e);
                exportTask.setError(e.getMessage());
                exportTask.setStatus(ExportTaskStatus.FAIL);
                exportTask.setError(e.getMessage());
              } finally {
                exportTaskGateway.saveOrUpdateExportTask(exportTask);
              }
            });
    return taskId;
  }

  private List<WdtBrandSheetItem> queryWdtBrandExportList(BrandQuery brandQuery) {
    final List<BrandListItem> brandListItems = brandGateway.queryBrandList(brandQuery);
    return brandListItems.stream()
        .map(
            (brandListItem) -> {
              final WdtBrandSheetItem wdtBrandSheetItem = new WdtBrandSheetItem();
              wdtBrandSheetItem.setSn(brandListItem.getSn());
              wdtBrandSheetItem.setName(brandListItem.getName());
              wdtBrandSheetItem.setIsDisable(
                  brandListItem.getStatus() == EnableStatusEnum.OFF ? "是" : "否");
              wdtBrandSheetItem.setBusinessLine(
                  brandListItem.getBusinessLine().stream()
                      .map(
                          l ->
                              IEnum.getEnumOptByValue(BusinessLine.class, l)
                                  .filter(BusinessLine::isEnable)
                                  .map(BusinessLine::getDesc)
                                  .orElse(""))
                      .filter(StringUtil::isNotEmpty)
                      .collect(Collectors.joining(",")));
              return wdtBrandSheetItem;
            })
        .collect(Collectors.toList());
  }
}
