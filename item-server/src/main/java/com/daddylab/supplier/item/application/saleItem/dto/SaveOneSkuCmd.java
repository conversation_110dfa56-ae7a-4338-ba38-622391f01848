package com.daddylab.supplier.item.application.saleItem.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> up
 * @date 2022年08月03日 1:51 PM
 */
@Data
@ApiModel("保存某sku得编辑请求封装")
public class SaveOneSkuCmd {

    @NotNull(message = "新品商品id不得为空")
    private Long newGoodsDbId;

    @ApiModelProperty("划线价格")
    private String linePrice;

    @ApiModelProperty("日常价格")
    private String dailyPrice;

    @ApiModelProperty("活动价格")
    private String activePrice;

    @ApiModelProperty("活动开始时间")
    private Long activePeriodStart;

    @ApiModelProperty("活动结束时间")
    private Long activePeriodEnd;

    @ApiModelProperty("新品直播机制")
    private String liveActive;

    @ApiModelProperty("新品活动机制")
    private String activeContent;

    @ApiModelProperty("是否可叠加证券")
    private Integer isCoupon;

    @ApiModelProperty("是否参与满减")
    private Integer isReduce;

    @ApiModelProperty("渠道最低价")
    private String channelLowest;

    @ApiModelProperty("渠道最低价")
    private Boolean isLongTerm;

}
