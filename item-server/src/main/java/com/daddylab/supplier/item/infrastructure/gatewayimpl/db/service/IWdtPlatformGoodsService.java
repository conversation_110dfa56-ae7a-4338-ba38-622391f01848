package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtPlatformGoods;

/**
 * <p>
 * 旺店通系统数据-平台商品 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
public interface IWdtPlatformGoodsService extends IDaddyService<WdtPlatformGoods> {
}
