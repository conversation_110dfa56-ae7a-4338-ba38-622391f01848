package com.daddylab.supplier.item.application.entryActivityPrice.models;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/10/4
 */
@Data
public class ConfirmationViewItemSkuVO {
    private Long id;
    private String skuCode;
    private Long skuId;
    private String specification;
    private Long activeStart;
    private Long activeEnd;
    private BigDecimal contractSalePrice;
    private BigDecimal platformCommission;
}
