package com.daddylab.supplier.item.domain.brand.trans;

import com.daddylab.supplier.item.common.trans.CommaSerialTransMapper;
import com.daddylab.supplier.item.common.trans.CommonEnumTransMapper;
import com.daddylab.supplier.item.domain.brand.dto.BrandDetail;
import com.daddylab.supplier.item.domain.brand.dto.BrandDropDownItem;
import com.daddylab.supplier.item.domain.brand.dto.BrandListItem;
import com.daddylab.supplier.item.domain.brand.dto.CreateOrUpdateBrandCmd;
import com.daddylab.supplier.item.domain.brand.entity.BrandEntity;
import com.daddylab.supplier.item.domain.brand.vo.BrandProviderVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Brand;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BrandProvider;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(uses = {
        CommonEnumTransMapper.class,
        CommaSerialTransMapper.class
})
public interface BrandTransMapper {
    BrandTransMapper INSTANCE = Mappers.getMapper(BrandTransMapper.class);

    BrandDropDownItem toDropDownItem(BrandEntity brand);

    Brand toBrandPo(CreateOrUpdateBrandCmd cmd);

    Brand toBrandPo(BrandEntity brandEntity);

    @Mapping(target = "providerIds", source = "providerIds")
    @Mapping(expression = "java(commaSerialTransMapper.strToIntegerList(brandPo.getBusinessLine()))", target = "businessLine")
    BrandEntity toBrandEntity(Brand brandPo, List<Long> providerIds);

    @Mapping(target = "updatedUid", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "isDel", ignore = true)
    @Mapping(target = "createdUid", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "providerIds", source = "providers")
    BrandEntity toBrandEntity(CreateOrUpdateBrandCmd cmd);

    BrandEntity copy(BrandEntity entity);

    @Mapping(target = "providerIds", source = "providers")
    BrandEntity copyFromCmd(@MappingTarget BrandEntity entity, CreateOrUpdateBrandCmd cmd);

    BrandProvider toBrandProviderPoWithBrandId(BrandProviderVo providers, Long brandId);

    @Mappings({
            @Mapping(target = "status", expression = "java(com.daddylab.supplier.item.infrastructure.enums.IEnum.getEnumByValue(com.daddylab.supplier.item.common.enums.EnableStatusEnum.class,cmd.getStatus()))"),
            @Mapping(target = "createdAt", ignore = true),
            @Mapping(target = "createdUid", ignore = true),
            @Mapping(target = "isDel", ignore = true),
            @Mapping(target = "updatedAt", ignore = true),
            @Mapping(target = "updatedUid", ignore = true)
    })
    Brand toBrand(CreateOrUpdateBrandCmd cmd);

    @Mapping(source = "providers", target = "providers")
    @Mapping(source = "brandEntity.businessLine", target = "businessLine")
    BrandDetail toBrandDetail(BrandEntity brandEntity, List<BrandProviderVo> providers);

    @Mapping(source = "providers", target = "providers")
    @Mapping(expression = "java(commaSerialTransMapper.strToIntegerList(brand.getBusinessLine()))", target = "businessLine")
    BrandListItem toBrandListItem(Brand brand, List<BrandProviderVo> providers);

    BrandProviderVo toBrandProviderVo(BrandProvider brandProvider, String providerName);

}
