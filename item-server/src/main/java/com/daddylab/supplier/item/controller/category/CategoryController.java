package com.daddylab.supplier.item.controller.category;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.category.CategoryBizService;
import com.daddylab.supplier.item.controller.category.dto.CategoryCmd;
import com.daddylab.supplier.item.controller.category.dto.CategoryQueryCmd;
import com.daddylab.supplier.item.controller.category.dto.CategoryTreeNode;
import com.daddylab.supplier.item.controller.category.dto.CategoryVo;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CategoryAttr;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.MultiSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/27 3:58 下午
 * @description
 */
@Api(value = "CategoryController", tags = "品类相关 API")
@RestController
@RequestMapping("/category")
public class CategoryController {

    @Autowired
    private CategoryBizService categoryBizService;

    @ResponseBody
    @ApiOperation(value = "获取所有品类完整树形结构")
    @GetMapping("/categoryTree")
    public MultiResponse<CategoryTreeNode> categoryTree() {
        return MultiResponse.of(categoryBizService.categoryTree().getData().getTreeNodes());
    }

    @ResponseBody
    @ApiOperation(value = "搜索查看品类列表")
    @PostMapping("/viewList")
    public MultiResponse<CategoryVo> viewList(@RequestBody CategoryQueryCmd categoryPageQuery) {
        return categoryBizService.queryList(categoryPageQuery);
    }

    @ResponseBody
    @ApiOperation(value = "创建品类")
    @PostMapping("/add")
    public SingleResponse<Long> add(@RequestBody CategoryCmd categoryCmd) {
        return categoryBizService.add(categoryCmd);
    }

    @ResponseBody
    @ApiOperation(value = "修改品类")
    @GetMapping("/update")
    public Response update(@ApiParam("品类id") Long id, @ApiParam("品类修改的名字") String name) {
        return categoryBizService.update(id, name);
    }

    @ResponseBody
    @ApiOperation(value = "删除品类")
    @GetMapping("/delete")
    public Response delete(@ApiParam("品类id") Long id) {
        return categoryBizService.delete(id);
    }

    @ResponseBody
    @ApiOperation(value = "展示品类属性列表")
    @GetMapping("/viewAttrList")
    public MultiResponse<CategoryAttr> viewAttrList(@ApiParam("品类id") Long id) {
        return categoryBizService.getAttr(id);
    }

    @ResponseBody
    @ApiOperation(value = "添加品类属性")
    @GetMapping("/addAttr")
    public Response addAttr(@ApiParam("品类id") Long id, @ApiParam("属性名称") String name) {
        return categoryBizService.addAttr(id, name);
    }

    @ResponseBody
    @ApiOperation(value = "修改品类属性")
    @GetMapping("/updateAttr")
    public Response updateAttr(@ApiParam("属性id") Long attrId, @ApiParam("属性名称") String name) {
        return categoryBizService.updateAttr(attrId, name);
    }

    @ResponseBody
    @ApiOperation(value = "删除品类属性")
    @GetMapping("/deleteAttr")
    public Response deleteAttr(@ApiParam("属性id") Long attrId) {
        return categoryBizService.deleteAttr(attrId);
    }

    @ResponseBody
    @ApiOperation(value = "缓存驱逐")
    @PostMapping("/cacheEvictForCategoryTree")
    @Auth("/category/update")
    public Response cacheEvictForCategoryTree() {
        return categoryBizService.cacheEvictForCategoryTree();
    }

    @ResponseBody
    @ApiOperation(value = "重置类目路径")
    @PostMapping("/resetPath")
    @Auth("/category/update")
    public Response resetPath() {
        return categoryBizService.resetPath();
    }

    @PostMapping(value = "/modifyItemCategory")
    @ApiOperation("后端商品修改商品类目")
    @Auth("/category/update")
    public Response modifyItemCategory(Long itemId, Long newCategoryId) {
        return categoryBizService.modifyItemCategory(itemId, newCategoryId);
    }

    @PostMapping(value = "/migrateItem")
    @ApiOperation("迁移类目商品")
    @Auth("/category/update")
    public SingleResponse<MultiSet<String>> migrateItem(Long fromCategoryId, Long newCategoryId) {
        return categoryBizService.migrateItem(fromCategoryId, newCategoryId);
    }


}
