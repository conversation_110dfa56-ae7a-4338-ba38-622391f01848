package com.daddylab.supplier.item.application.purchasePayable.query;

import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel("采购应付单凭证查询参数")
public class PurchasePayableVoucherQuery implements Serializable {

    private static final long serialVersionUID = -8473568990941695096L;

    @ApiModelProperty(value = "供应商id")
    @NotNull(message = "供应商不能为空")
    private Long providerId;

    @ApiModelProperty(value = "采购组织id")
    @NotNull(message = "采购组织不能为空")
    private Long organizationId;


    @ApiModelProperty(value = "查询时间,查询月份的第一天秒级时间戳")
    @NotNull(message = "查询时间不能为空")
    private Long queryDate;

    /**
     * {@link PurchaseTypeEnum}
     */
    @ApiModelProperty(value = "应付类型: 1采购入库应付、2采退出库应付、3其他应付，如果查询全部，则不传")
    private Integer type;

    /**
     * 关联的入库单/出库单/其他应付的采购员
     */
    private Long buyerId;
}
