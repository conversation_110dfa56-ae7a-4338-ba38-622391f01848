package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemProcurement;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemProcurementMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemProcurementService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品采购设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class ItemProcurementServiceImpl extends DaddyServiceImpl<ItemProcurementMapper, ItemProcurement> implements IItemProcurementService {
    @Autowired
    private ItemProcurementMapper itemProcurementMapper;

    @Override
    public ItemProcurement getByItemId(Long itemId) {
        if (itemId == null || itemId <= 0) {
            return null;
        }
        return itemProcurementMapper.selectOneByItemId(itemId);
    }

    @Override
    public boolean setBuyerId(Long itemId, Long buyerId) {
        final ItemProcurement itemProcurement = new ItemProcurement();
        itemProcurement.setBuyerId(buyerId);
        return lambdaUpdate().eq(ItemProcurement::getItemId, itemId).update(itemProcurement);
    }
}
