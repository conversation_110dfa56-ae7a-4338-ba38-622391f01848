package com.daddylab.supplier.item.infrastructure.gatewayimpl.user;

import com.daddylab.supplier.item.infrastructure.accessControl.StaffAcInfo;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface UserTransMapper {
    UserTransMapper INSTANCE = Mappers.getMapper(UserTransMapper.class);

    /**
     * 用户服务返回的员工列表转化为我们自定义的员工信息对象列表
     *
     * @param staffListItems 用户服务返回的员工列表
     * @return 我们自定义的员工信息对象列表
     */
    @IterableMapping(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
    List<StaffInfo> toStaffInfoList(List<StaffListItem> staffListItems);

    /**
     * 用户服务返回的员工列表项转化为我们自定义的员工信息对象
     *
     * @param staffListItem 用户服务返回的员工列表项
     * @return 我们自定义的员工信息对象
     */
    @Mapping(target = "userName", source = "realname")
    @Mapping(target = "userId", source = "uid")
    StaffInfo toStaffInfo(StaffListItem staffListItem);

    /**
     * 权限系统返回的用户信息转为员工信息对象
     *
     * @param staffAcInfoList 权限系统返回的用户信息
     * @return 自定义的员工信息对象列表
     */
    List<StaffInfo> staffAcInfoListToStaffInfoList(List<StaffAcInfo> staffAcInfoList);

    /**
     * 权限系统返回的用户信息转为员工信息对象
     *
     * @param staffAcInfo 权限系统返回的用户信息
     * @return 自定义的员工信息对
     */
    @Mapping(target = "userName", source = "realname")
    @Mapping(target = "userId", source = "uid")
    StaffInfo staffAcInfoToStaffInfo(StaffAcInfo staffAcInfo);
}
