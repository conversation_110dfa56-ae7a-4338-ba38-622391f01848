package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetActivityEvent;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 盘货表对应的活动力度 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
public interface HandingSheetActivityEventMapper extends DaddyBaseMapper<HandingSheetActivityEvent> {

    void deleteBySheetId(@Param("handingSheetId") Long handingSheetId);
}
