package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import com.daddylab.supplier.item.controller.provider.dto.ProviderShopDetail;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR> up
 * @date 2023年07月25日 4:22 PM
 */
@FeignClient(name = "gatewayFeignClient", url = "${gateway.url}")
public interface ApiGatewayFeignClient {

    /**
     * 根据shopId查询店铺详情
     *
     * @param mallShopId 店铺ID
     * @return 响应数据模型
     */
    @GetMapping(path = "/mall/openapi/daddylab/mall/erp/shop/getDetailByShopId")
    Rsp<ProviderShopDetail> queryShopDetail(@RequestParam("shopId") Long mallShopId);

}
