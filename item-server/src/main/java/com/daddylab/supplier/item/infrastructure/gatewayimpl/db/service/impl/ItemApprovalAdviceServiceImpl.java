package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemApprovalAdvice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemApprovalAdviceMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemApprovalAdviceService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品审批建议 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
@Service
public class ItemApprovalAdviceServiceImpl extends
        DaddyServiceImpl<ItemApprovalAdviceMapper, ItemApprovalAdvice> implements
        IItemApprovalAdviceService {
}
