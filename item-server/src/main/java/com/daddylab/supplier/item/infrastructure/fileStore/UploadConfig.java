package com.daddylab.supplier.item.infrastructure.fileStore;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "upload")
public class UploadConfig
        implements com.daddylab.supplier.item.domain.fileStore.config.UploadConfig {
    private String defaultDir;
    private String publicDir;
    private boolean preserveName = true;
    private boolean disableCache = false;
    private String imageStoreSvc = StorageService.UPYUN;
    private String videoStoreSvc = StorageService.UPYUN;
    private String fileStoreSvc = StorageService.OSS;
}
