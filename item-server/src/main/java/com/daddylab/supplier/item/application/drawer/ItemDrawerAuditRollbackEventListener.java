package com.daddylab.supplier.item.application.drawer;

import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAuditStats;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerModuleAuditStatsService;
import com.daddylab.supplier.item.types.itemDrawer.ItemLaunchAuditRollbackEvent;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemLaunchProcessNodeId;
import com.google.common.eventbus.Subscribe;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
@EventBusListener(value = "itemDrawerAuditRollbackEventListener")
public class ItemDrawerAuditRollbackEventListener {

    private final IItemDrawerModuleAuditStatsService itemDrawerModuleAuditStatsService;


    @Subscribe
    public void listener(ItemLaunchAuditRollbackEvent event) {
        Long itemId = event.getItemId();

        final ItemDrawerModuleAuditStats moduleAuditStats = itemDrawerModuleAuditStatsService.getModuleAuditStatsCreateIfNotExists(
                event.getType(), itemId, event.getRound());
        itemDrawerModuleAuditStatsService.removeByIdWithTime(moduleAuditStats.getId());
        moduleAuditStats.setId(null);
        moduleAuditStats.setItemId(itemId);
        if (event.getToNodeId().ordinal() <= ItemLaunchProcessNodeId.LEGAL.ordinal()) {
            moduleAuditStats.setLegalAuditUid(0L);
            moduleAuditStats.setLegalAuditStartTime(0L);
            moduleAuditStats.setLegalAuditEndTime(0L);
            moduleAuditStats.setLegalAuditCostTime(0L);
        }
        if (event.getToNodeId().ordinal() <= ItemLaunchProcessNodeId.QC.ordinal()) {
            moduleAuditStats.setQcAuditUid(0L);
            moduleAuditStats.setQcAuditStartTime(0L);
            moduleAuditStats.setQcAuditEndTime(0L);
            moduleAuditStats.setQcAuditCostTime(0L);
        }
        itemDrawerModuleAuditStatsService.save(moduleAuditStats);
    }

}
