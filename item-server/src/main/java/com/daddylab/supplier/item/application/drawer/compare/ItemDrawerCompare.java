package com.daddylab.supplier.item.application.drawer.compare;

import com.daddylab.supplier.item.domain.drawer.vo.ItemDrawerAttrImages;
import com.daddylab.supplier.item.domain.drawer.vo.ItemDrawerSkuImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.LaunchItemType;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Class  ItemDrawerCompare
 *
 * @Date 2022/6/2下午10:02
 * <AUTHOR>
 */
@Data
public class ItemDrawerCompare implements Serializable {

    private String standardName;

    private String tbTitle;

    private String miniTitle;

    private String douTitle;

    private String homeCopy;

    private Long planId;

    private List<String> images;

    private List<String> otherImages;

    private List<String> detailImages;

    private List<String> mainImageVideo;

    private List<ItemDrawerSkuImage> skuImages;

    private List<ItemDrawerAttrImages> attrImages;

    private Long buyerId;

    private Long principalId;

    private String qcIds;

    public void setQcIds(String qcIds) {
        this.qcIds = qcIds;
        if (StringUtil.isNotBlank(this.qcIds) && !StringUtil.endWith(this.qcIds, ",")) {
            this.qcIds = this.qcIds + ",";
        }
    }


    /**
     * 抖音链接
     */
    private String douLink;

    /**
     * 淘宝链接
     */
    private String tbLink;

    /**
     * 微信链接
     */
    private String wechatLink;

    /**
     * 商品类型 INNER 内部 OUTSOURCE 外包
     */
    private LaunchItemType type;

    /**
     * 直播话术
     */
    private String liveVerbalTrick;

    private String douYinLinkContent;

    private String kuaiShouLinkContent;

    private String miniRedBookLink;
}
