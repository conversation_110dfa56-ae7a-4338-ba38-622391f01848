package com.daddylab.supplier.item.infrastructure.gatewayimpl.auth;

import cn.dev33.satoken.stp.StpUtil;
import com.daddylab.supplier.item.domain.auth.gateway.LoginGateway;
import org.springframework.stereotype.Component;

@Component
public class LoginGatewayImpl implements LoginGateway {
    @Override
    public void login(Long id) {
        StpUtil.login(id);
    }

    @Override
    public boolean isLogin() {
        return StpUtil.isLogin();
    }

    @Override
    public Long getLoginId() {
        final Object loginId = StpUtil.getLoginId();
        if (loginId instanceof String) {
            return Long.parseLong(((String) loginId));
        } else if (loginId instanceof Long) {
            return (Long) loginId;
        } else if (loginId instanceof Integer) {
            return ((Integer) loginId).longValue();
        } {
            return 0L;
        }
    }

    @Override
    public String getTokenValue() {
        return StpUtil.getTokenValue();
    }

    @Override
    public Long getTokenTimeout() {
        return StpUtil.getTokenTimeout();
    }

    @Override
    public Long getLoginIdByToken(String tokenValue) {
        return (Long) StpUtil.getLoginIdByToken(tokenValue);
    }

    @Override
    public void logout() {
        StpUtil.logout();
    }

    @Override
    public void logoutByTokenValue(String tokenValue) {
        StpUtil.logoutByTokenValue(tokenValue);
    }

    @Override
    public void logoutByLoginId(Object loginId) {
        StpUtil.logoutByLoginId(loginId);
    }
}
