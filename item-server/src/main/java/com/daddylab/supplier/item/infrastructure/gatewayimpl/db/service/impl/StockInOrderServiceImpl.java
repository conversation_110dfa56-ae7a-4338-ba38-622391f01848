package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.StockInOrderDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockInState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockInOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockInOrderService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 采购入库表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Service
public class StockInOrderServiceImpl extends DaddyServiceImpl<StockInOrderMapper, StockInOrder> implements IStockInOrderService {

    @Autowired
    StockInOrderMapper stockInOrderMapper;

    @Override
    public void setKingDeeId(Long id, String kingDeeId) {
        LambdaUpdateWrapper<StockInOrder> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(StockInOrder::getKingDeeId, kingDeeId).eq(StockInOrder::getId, id);
        this.update(updateWrapper);
    }

    @Override
    public void removeKingDeeId(Long id) {
        LambdaUpdateWrapper<StockInOrder> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(StockInOrder::getKingDeeId, null).eq(StockInOrder::getId, id);
        this.update(updateWrapper);
    }

    @Override
    public Integer stateCount(StockInState stockInState, Long purchaseOrderId) {
        LambdaUpdateWrapper<StockInOrder> wrapper = Wrappers.lambdaUpdate();
        wrapper.set(StockInOrder::getState, stockInState.getValue()).eq(StockInOrder::getPurchaseOrderId, purchaseOrderId);
        return this.count(wrapper);
    }

    @Override
    public List<StockInOrderDO> receiptQuantity(List<Long> purchaseOrderIdList) {
        return stockInOrderMapper.sumReceiptQuantity(purchaseOrderIdList);
    }

    @Override
    public List<StockInOrderDO> sumHadAndWillReceiptQuantity(List<Long> purchaseOrderIdList) {
        return stockInOrderMapper.sumHadAndWillReceiptQuantity(purchaseOrderIdList);
    }

    @Override
    public Boolean related(Long purchaseOrderId) {
        return this.lambdaQuery().eq(StockInOrder::getPurchaseOrderId, purchaseOrderId).count() > 0;
    }

    @Override
    public List<Long> relatedIdList(Long purchaseOrderId) {
        LambdaQueryWrapper<StockInOrder> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StockInOrder::getPurchaseOrderId, purchaseOrderId);
        List<StockInOrder> list = this.list(wrapper);
        if(CollectionUtils.isEmpty(list)){
            return new LinkedList<>();
        }
        return list.stream().map(StockInOrder::getId).collect(Collectors.toList());
    }

    @Override
    public Integer noeWarehousingCount(Long purchaseOrderId) {
        LambdaQueryWrapper<StockInOrder> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StockInOrder::getPurchaseOrderId, purchaseOrderId).in(StockInOrder::getState, ListUtil.of(StockInState.WAIT_SUBMIT.getValue(),StockInState.WAIT_IN.getValue()));
        return this.count(wrapper);
    }

    @Override
    public void updateStatusByPurchaseOrderNos(Collection<String> purchaseOrderNos, StockInState state) {
        this.lambdaUpdate().set(StockInOrder::getState,state).in(StockInOrder::getPurchaseOrderNo,purchaseOrderNos).update();
    }

//    @Override
//    public void updateStatus(String no, StockInState status) {
//        this.lambdaUpdate().set(StockInOrder::getState,status.getValue()).eq(StockInOrder::getNo,no).update();
//    }
}
