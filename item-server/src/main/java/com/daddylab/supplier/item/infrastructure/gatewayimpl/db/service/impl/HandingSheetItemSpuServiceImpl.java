package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetItemSpu;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.HandingSheetItemSpuMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IHandingSheetItemSpuService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 盘货表关联的商品（V2.4.5新版盘货表） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Service
public class HandingSheetItemSpuServiceImpl extends DaddyServiceImpl<HandingSheetItemSpuMapper, HandingSheetItemSpu> implements
                                                                                                                     IHandingSheetItemSpuService {

    @Override
    public List<Long> selectInvalidSpuIds(Long handingSheetId) {

        return getDaddyBaseMapper().selectInvalidSpuIds(handingSheetId);
    }


}
