package com.daddylab.supplier.item.application.afterSalesForwarding.types;

import com.daddylab.supplier.item.common.domain.dto.Command;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/27
 */
@Data
public class AfterSalesForwardingRegisterNoNeedToForwardCmd extends Command {
    private static final long serialVersionUID = 6103170351762100006L;

    @ApiModelProperty(value = "登记ID")
    private List<Long> ids;

    @ApiModelProperty(value = "无需转寄类型", notes = "无需转寄类型 0:全部 1:自有仓库 2:影响销售 3:其他")
    private Integer needNotForwardType;

    @ApiModelProperty(value = "客服备注")
    private String csRemark;
}
