package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PayApplyAuditProcessNode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PayApplyAuditProcessNodeMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPayApplyAuditProcessNodeService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 申请付款单审核流节点审核详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Service
public class PayApplyAuditProcessNodeServiceImpl extends DaddyServiceImpl<PayApplyAuditProcessNodeMapper, PayApplyAuditProcessNode> implements IPayApplyAuditProcessNodeService {

}
