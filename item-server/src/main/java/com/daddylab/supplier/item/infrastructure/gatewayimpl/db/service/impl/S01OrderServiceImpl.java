package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.S01Order;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.S01OrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IS01OrderService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 淘宝订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Service
public class S01OrderServiceImpl extends DaddyServiceImpl<S01OrderMapper, S01Order> implements IS01OrderService {

}
