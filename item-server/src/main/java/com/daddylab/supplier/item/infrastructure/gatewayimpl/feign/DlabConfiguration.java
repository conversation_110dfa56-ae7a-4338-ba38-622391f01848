package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.codec.Decoder;
import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;
import lombok.NonNull;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import java.util.ArrayList;
import java.util.List;

public class DlabConfiguration {

    @Autowired
    private ObjectFactory<HttpMessageConverters> messageConverters;

    public ObjectFactory<HttpMessageConverters> feignHttpMessageConverter() {
        final HttpMessageConverters httpMessageConverters = new HttpMessageConverters(new JavaMappingJackson2HttpMessageConverter());
        return () -> httpMessageConverters;
    }

    public static class JavaMappingJackson2HttpMessageConverter extends MappingJackson2HttpMessageConverter {
        JavaMappingJackson2HttpMessageConverter() {
            super(buildObjectMapper());

            List<MediaType> mediaTypes = new ArrayList<>();
            mediaTypes.add(MediaType.valueOf(MediaType.ALL_VALUE + ";charset=UTF-8"));
            setSupportedMediaTypes(mediaTypes);

        }

        @NonNull
        private static ObjectMapper buildObjectMapper() {
            final ObjectMapper objectMapper = Jackson2ObjectMapperBuilder.json().build();
            objectMapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
            return objectMapper;
        }
    }
    @Bean
    public Encoder feignFormEncoder() {
        return new SpringFormEncoder(new SpringEncoder(messageConverters));
    }

    @Bean
    public Decoder feignDecoder() {
        return new ResponseEntityDecoder(new SpringDecoder(feignHttpMessageConverter()));
    }





}
