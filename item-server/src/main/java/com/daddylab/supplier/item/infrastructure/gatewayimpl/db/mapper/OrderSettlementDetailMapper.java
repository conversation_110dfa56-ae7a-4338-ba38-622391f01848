package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.order.settlement.dto.DetailQuantityStaticDo;
import com.daddylab.supplier.item.application.order.settlement.dto.DetailSubtotalDo;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderSettlementDetail;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderSettlementDetail;

/**
 * <p>
 * 订单结算/订货明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
public interface OrderSettlementDetailMapper extends DaddyBaseMapper<OrderSettlementDetail> {

    DetailSubtotalDo getSettlementSummarize(@Param("formId") Long formId);

    List<DetailQuantityStaticDo> getQuantityStatic(@Param("formIds")Collection<Long> formIds);

}
