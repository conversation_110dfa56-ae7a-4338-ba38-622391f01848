package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundStockInOrderDetailsRefundOrderDetails;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundStockInOrderDetailsRefundOrderDetailsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtRefundStockInOrderDetailsRefundOrderDetailsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 旺店通退货入库单明细与退换单明细的关联关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Service
public class WdtRefundStockInOrderDetailsRefundOrderDetailsServiceImpl extends DaddyServiceImpl<WdtRefundStockInOrderDetailsRefundOrderDetailsMapper, WdtRefundStockInOrderDetailsRefundOrderDetails> implements IWdtRefundStockInOrderDetailsRefundOrderDetailsService {

}
