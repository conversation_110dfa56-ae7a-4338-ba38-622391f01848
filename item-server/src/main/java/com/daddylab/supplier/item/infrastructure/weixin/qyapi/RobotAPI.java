package com.daddylab.supplier.item.infrastructure.weixin.qyapi;

import com.daddylab.supplier.item.infrastructure.utils.FileUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.File;
import java.io.IOException;
import java.util.Base64;
import java.util.List;

@FeignClient(url = "https://qyapi.weixin.qq.com/cgi-bin/webhook", name = "WxRobotAPI")
public interface RobotAPI {

    @RequestMapping(path = "/send", method = RequestMethod.POST)
    Result send(@RequestParam("key") String key, @RequestBody Message message);

    @Data
    class Result {
        Integer errcode;
        String errmsg;
    }

    abstract class Message {
        final String msgtype;

        public Message(String msgtype) {
            this.msgtype = msgtype;
        }

        public String getMsgtype() {
            return msgtype;
        }
    }

    @Getter
    class TextMessage extends Message {
        final Text text;

        public TextMessage(Text text) {
            super("text");
            this.text = text;
        }

        public static TextMessage ofContent(String content) {
            final Text text = new Text();
            text.setContent(content);
            return new TextMessage(text);
        }

        public static TextMessage ofContentAndMentionedList(String content, List<String> mentionedList) {
            final Text text = new Text();
            text.setContent(content);
            text.setMentionedList(mentionedList);
            return new TextMessage(text);
        }

        public static TextMessage ofContentAndMentionedMobileList(String content, List<String> mentionedMobileList) {
            final Text text = new Text();
            text.setContent(content);
            text.setMentionedMobileList(mentionedMobileList);
            return new TextMessage(text);
        }

        @Data
        static
        class Text {
            String content;
            @JsonProperty("mentioned_list")
            List<String> mentionedList; //@用户在企业微信内的userId（建议用手机号比较方便）
            @JsonProperty("mentioned_mobile_list")
            List<String> mentionedMobileList; //根据手机号@用户
        }
    }

    @Getter
    class MarkdownMessage extends Message {
        final Markdown markdown;

        public MarkdownMessage(Markdown markdown) {
            super("markdown");
            this.markdown = markdown;
        }

        public static MarkdownMessage ofMarkdown(String markdown) {
            return new MarkdownMessage(new Markdown(markdown));
        }

        @Data
        static class Markdown {
            final String content;
        }
    }

    @Getter
    class ImageMessage extends Message {
        final Image image;

        public ImageMessage(Image image) {
            super("image");
            this.image = image;
        }

        public static ImageMessage ofImageFile(File image) throws IOException {
            final byte[] fileBytes = FileUtil.readBytes(image);
            final String base64 = Base64.getEncoder().encodeToString(fileBytes);
            final String hex = DigestUtils.md5DigestAsHex(fileBytes);
            return new ImageMessage(new Image(base64, hex));
        }

        public static ImageMessage ofImageFile(String path) throws IOException {
            return ofImageFile(new File(path));
        }

        @Data
        static class Image {
            final String base64;
            final String md5;
        }
    }

    @Getter
    class NewsMessage extends Message {
        final News news;

        public NewsMessage(News news) {
            super("news");
            this.news = news;
        }

        public static NewsMessage ofArticles(List<News.Article> articles) {
            return new NewsMessage(new News(articles));
        }

        @Data
        static class News {
            final List<Article> articles;

            @Data
            static class Article {
                String title;
                String description;
                String url;
                String picurl;
            }
        }

    }


}
