package com.daddylab.supplier.item.infrastructure.qimen.wdt;

import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.RefundAPI;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchHistoryParams;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchHistoryResponse;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.daddylab.supplier.item.infrastructure.qimen.QimenConfig;
import com.daddylab.supplier.item.infrastructure.qimen.QimenWdtRefundAPI;
import com.qimencloud.api.QimenCloudClient;
import com.qimencloud.api.scene3ldsmu02o9.request.WdtAftersalesRefundRefundSearchRequest.Params;
import com.qimencloud.api.scene3ldsmu02o9.response.WdtAftersalesRefundRefundSearchResponse;
import com.taobao.api.ApiException;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2022/1/17
 */
public class RefundAPIQimenImpl extends WdtAPIQimenImplBase implements RefundAPI {

    QimenWdtRefundAPI qimenAPI;

    public RefundAPIQimenImpl(QimenCloudClient qimenClient,
            QimenConfig qimenConfig,
            WdtConfig wdtConfig,
            int configIndex
    ) {
        super(qimenClient, qimenConfig, wdtConfig, configIndex);
    }

    @Override
    protected void initApiInstance(QimenCloudClient qimenClient, QimenConfig qimenConfig,
            WdtConfig wdtConfig, int configIndex) {
        qimenAPI = new QimenWdtRefundAPI(qimenClient, qimenConfig, wdtConfig, configIndex);
    }

    @Override
    public RefundSearchResponse search(RefundSearchParams params, Pager pager)
            throws WdtErpException {
        try {
            final Params qimenParams =
                    Assembler.INST.toQimenParams(params);
            final WdtAftersalesRefundRefundSearchResponse response = qimenAPI
                    .afterSalesRefundSearch(qimenParams, pager.getPageNo() + 1,
                            pager.getPageSize());
            return checkAndReturnData(response, RefundSearchResponse.class);
        } catch (ApiException exception) {
            throw transformException(exception);
        }
    }

    @Override
    public RefundSearchHistoryResponse searchHistory(RefundSearchHistoryParams params, Pager pager)
            throws WdtErpException {
        throw new UnsupportedOperationException("暂未实现");

    }

    @Mapper
    interface Assembler {

        Assembler INST = Mappers.getMapper(Assembler.class);

        Params toQimenParams(RefundSearchParams params);
    }
}
