package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.order.settlement.dto.SysBillPageQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderSettlementForm;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 订单结算表单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
public interface OrderSettlementFormMapper extends DaddyBaseMapper<OrderSettlementForm> {

    /**
     * 获取库中ID最大的编码
     *
     * @return
     */
    String maxNo(@Param("noPrefix") String noPrefix);

    List<OrderSettlementForm> selectBill(@Param("query") SysBillPageQuery query);

    void recoverData(@Param("ids") Collection<Long> ids);

    void recoverDataDetail(@Param("ids") Collection<Long> ids);

    /**
     * 获取 付款状态是为提交或者没有发起付款的 结算单ID
     * @return
     */
    List<Long> getNoPayAndWaitPayFormId();

    List<Long> getApplyingPayFormId();

    List<Long> getFinishPayFormId();


}
