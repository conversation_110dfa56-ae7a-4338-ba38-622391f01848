package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryAlloc;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryAllocShop;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.InventoryAllocShopMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IInventoryAllocShopService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 库存分配店铺设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
public class InventoryAllocShopServiceImpl extends
        DaddyServiceImpl<InventoryAllocShopMapper, InventoryAllocShop> implements IInventoryAllocShopService {
    
    @Override
    public InventoryAllocShop getByShopNo(String shopNo) {
        return lambdaQuery().eq(InventoryAllocShop::getShopNo, shopNo).one();
    }
    
    @Override
    public List<InventoryAllocShop> listByShopNo(Collection<String> shopNos) {
        if (CollUtil.isEmpty(shopNos)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(InventoryAllocShop::getShopNo, shopNos).list();
    }
    
    public List<InventoryAllocShop> listByShopNo(List<String> shopNos) {
        if (CollUtil.isEmpty(shopNos)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(InventoryAllocShop::getShopNo, shopNos).list();
    }
    
    @Override
    public void setPlatformItemListSyncEnabled(List<PlatformItem> platformItems) {
        if (CollUtil.isEmpty(platformItems)) {
            return;
        }
        List<String> shopNos =
                platformItems.stream().map(PlatformItem::getShopNo).distinct().collect(Collectors.toList());
        List<InventoryAllocShop> inventoryAllocShops = listByShopNo(shopNos);
        for (PlatformItem platformItem : platformItems) {
            platformItem.setSyncEnabledByShopSetting(
                    inventoryAllocShops.stream().filter(v -> v.getShopNo().equals(platformItem.getShopNo())).findFirst()
                            .orElse(null));
        }
    }
    
}
