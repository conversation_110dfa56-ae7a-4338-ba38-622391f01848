package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtGoods implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一键
     */
    private Integer goodsId;

    /**
     * 货品编号
     */
    private String goodsNo;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 简称
     */
    private String shortName;

    /**
     * 货品别名
     */
    private String alias;

    /**
     * 货品类别 0：其它 1：销售货品 2：原材料 3：包装物 4：周转材料 5：虚拟商品 6：固定资产 8：分装箱
     */
    private String goodsType;

    /**
     * 规格数
     */
    private Integer specCount;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 品牌id
     */
    private Integer brandId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 自定义属性1
     */
    private String prop1;

    /**
     * 自定义属性2
     */
    private String prop2;

    /**
     * 自定义属性3
     */
    private String prop3;

    /**
     * 自定义属性4
     */
    private String prop4;

    /**
     * 自定义属性5
     */
    private String prop5;

    /**
     * 自定义属性6
     */
    private String prop6;

    /**
     * 产地
     */
    private String origin;

    /**
     * 分类名称
     */
    private String className;

    /**
     * 基本单位
     */
    private String unitName;

    /**
     * 辅助单位
     */
    private String auxUnitName;

    /**
     * 标记名称
     */
    private String flagName;

    /**
     * 0代表未删除 大于0代表已删除
     */
    private Integer deleted;

    /**
     * 最后修改时间
     */
    private LocalDateTime goodsModified;

    /**
     * 创建时间, 时间格式 YYYY-MM-DD HH:MM:SS
     */
    private LocalDateTime goodsCreated;

    @TableField(exist = false)
    private List<WdtGoodsSpec> specs;

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt = 0L;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt = 0L;

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid = 0L;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid = 0L;

    @TableLogic
    private Integer isDel = 0;


}
