package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrderDetails;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 旺店通销售出库单详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
public interface WdtSaleStockOutOrderDetailsMapper extends DaddyBaseMapper<WdtSaleStockOutOrderDetails> {

    List<Long> getStockOutId(@Param("specCode") String specCode, @Param("goodsName") String goodsName, @Param("goodsNo") String goodsNo);

}
