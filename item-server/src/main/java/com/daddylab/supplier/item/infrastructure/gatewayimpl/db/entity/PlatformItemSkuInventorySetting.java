package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.application.platformItem.data.PlatformItemInventorySettingType;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 平台商品SKU库存设置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false, of = {"shopNo", "outerItemId", "outerSkuId", "skuCode", "combinationCode"})
public class PlatformItemSkuInventorySetting implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic(value = "0", delval = "id")
    private Integer isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 平台
     */
    private Platform platform;

    /**
     * 店铺编号
     */
    private String shopNo;

    /**
     * 平台商品ID
     */
    private String outerItemId;

    /**
     * 平台商品SKU ID
     */
    private String outerSkuId;

    /**
     * 库存占比
     */
    private Integer inventoryRatio;

    /**
     * 锁定库存
     */
    private Integer lockNum;

    /**
     * 安全库存
     */
    private Integer safetyThreshold;

    /**
     * 类型 1:按比例自动分配库存 2:锁定库存
     */
    private PlatformItemInventorySettingType type;

    /**
     * SKU编码
     */
    private String skuCode;

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
        this.combinationCode = "";
    }

    /**
     * 组合装编码
     */
    private String combinationCode;


}
