package com.daddylab.supplier.item.controller.open;

import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizService;
import com.daddylab.supplier.item.application.stockOutOrder.StockOutOrderBizService;
import com.daddylab.supplier.item.controller.purchase.dto.order.CallBackOrderStateCmd;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/open/purchase")
@Api(value = "采购管理开放接口", tags = "采购管理开放接口")
public class OpenPurchaseController {

    @Resource
    PurchaseOrderBizService purchaseOrderBizService;

    @Resource
    StockOutOrderBizService stockOutOrderBizService;

    @ApiOperation("采购单状态回调")
    @PostMapping("/orderStatus")
    public Response updateState(@Validated @RequestBody CallBackOrderStateCmd cmd) {
        return purchaseOrderBizService.callBackOrderState(cmd);
    }

    @ApiOperation("退料单状态回调")
    @PostMapping("/stockOutStatus")
    public Response stockOutStatus(@Validated @RequestBody CallBackOrderStateCmd cmd) {
        return stockOutOrderBizService.callBackOrderState(cmd);
    }


}
