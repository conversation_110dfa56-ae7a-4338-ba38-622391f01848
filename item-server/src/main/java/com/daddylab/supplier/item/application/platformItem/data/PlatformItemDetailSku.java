package com.daddylab.supplier.item.application.platformItem.data;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.PositiveOrZero;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/12/27
 */
@Data
@ApiModel("平台商品详情SKU")
public class PlatformItemDetailSku {

    /**
     * id
     */
	@ApiModelProperty("id")
    private Long id;
    
    /**
     * 平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城
     */
    @ApiModelProperty("平台")
    private Platform platform;
    
    /**
     * 平台商品ID
     */
    @ApiModelProperty("平台商品ID")
    private Long platformItemId;

    /**
     * sku系统编码
     */
	@ApiModelProperty("sku系统编码")
    private String skuCode;

    /**
     * 匹配到的我们自己的SKU ID
     */
	@ApiModelProperty("匹配到的我们自己的SKU ID")
    private Long skuId;

    /**
     * 平台商品状态
     */
	@ApiModelProperty("平台商品状态")
    private PlatformItemStatus status;
    
    /**
     * 平台货品ID
     */
    @ApiModelProperty("平台货品ID")
    private String outerItemId;
    
    /**
     * 平台货品编码
     */
    @ApiModelProperty("平台货品编码")
    private String outerItemCode;

    /**
     * 外部平台SKU ID
     */
	@ApiModelProperty("外部平台SKU ID")
    private String outerSkuId;

    /**
     * 外部平台SKU编码
     */
	@ApiModelProperty("外部平台SKU编码")
    private String outerSkuCode;

    /**
     * 平台售价
     */
	@ApiModelProperty("平台售价")
    private BigDecimal price;
    
    /**
     * 平台库存
     */
	@ApiModelProperty("平台库存")
    private Integer stock;

    /**
     * 规格名称
     */
	@ApiModelProperty("规格名称")
    private String specName;

    /**
     * 同步库存
     */
    @ApiModelProperty("同步库存")
    @PositiveOrZero
    private Integer syncStock;

    /**
     * 同步库存
     */
    @ApiModelProperty("上次同步库存")
    private Integer lastSyncStock;
    
    /**
     * 上次同步时间
     */
    @ApiModelProperty("上次同步时间")
    private Long lastSyncTime;
    
    /**
     * 库存分配记录ID
     */
    @ApiModelProperty("库存分配记录ID")
    private Long inventoryAllocId;

    /**
     * 占用库存
     */
    @ApiModelProperty("占用库存")
    private Integer occupyStock;

    /**
     * 总库存
     */
    @ApiModelProperty("总库存")
    private Integer totalStock;

    /**
     * 低库存预警
     */
    @ApiModelProperty("低库存预警")
    private Integer warnThreshold;

    /**
     * 是否为组合品
     */
    @ApiModelProperty("是否为组合品")
    private Boolean isCombinationItem;

    /**
     * 组合装ID
     */
    @ApiModelProperty("组合装ID")
    private Long combinationId;
    
    /**
     * 计算过程
     */
    @ApiModelProperty("计算过程")
    private List<String> calculations;
}
