package com.daddylab.supplier.item.domain.message.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 4:38 下午
 * @description
 */
@ApiModel("消息接收者")
@Data
public class MsgRecipientObj {
    /**
     * 用户编号
     */
    @ApiModelProperty(value = "用户id", required = true)
    Long staffId;

    /**
     * 用户名称
     */
    @ApiModelProperty("用户名字")
    String staffName;
}
