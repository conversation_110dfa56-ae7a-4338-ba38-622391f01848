package com.daddylab.supplier.item.infrastructure.third.redbook.impl;

import com.alibaba.cola.exception.BizException;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.winrobot.WinrobotTokenManager;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.third.base.AccessTokenService;
import com.daddylab.supplier.item.infrastructure.third.config.RedBookConfig;
import com.daddylab.supplier.item.infrastructure.third.consts.ThirdRedisConstants;
import com.daddylab.supplier.item.infrastructure.third.redbook.RedBookService;
import com.daddylab.supplier.item.infrastructure.utils.Logs;
import com.daddylab.supplier.item.infrastructure.winrobot360.Winrobot360API;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TaskStartParam;
import com.xiaohongshu.fls.opensdk.client.InventoryClient;
import com.xiaohongshu.fls.opensdk.client.OauthClient;
import com.xiaohongshu.fls.opensdk.client.OrderClient;
import com.xiaohongshu.fls.opensdk.client.ProductClient;
import com.xiaohongshu.fls.opensdk.entity.BaseRequest;
import com.xiaohongshu.fls.opensdk.entity.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.slf4j.event.Level;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @class RedBookAbstract.java
 * @description 描述类的作用
 * @date 2024-02-28 16:50
 */
@Slf4j
public abstract class RedBookAbstract implements InitializingBean, RedBookService {
    @Autowired
    protected RedBookMessageHandlerSupport redBookMessageHandlerSupport;

    OauthClient oauthClient;
    InventoryClient inventoryClient;
    ProductClient productClient;
    OrderClient orderClient;
    @Autowired
    private RedBookConfig redBookConfig;
    @Autowired
    private AccessTokenService accessTokenService;
    @Autowired
    private Winrobot360API winrobot360Api;
    @Autowired
    private WinrobotTokenManager winrobotTokenManager;
    RRateLimiter rateLimiter;
    @Autowired
    private RedissonClient redissonClient;

    final static String TOKEN_INVALID_CODE = "401";


    private ShopAuthorization shopAuthorization;

    @Override
    public void setAuthorization(ShopAuthorization shopAuthorization) {
        this.shopAuthorization = shopAuthorization;
    }

    @Override
    public ShopAuthorization getAuthorization() {
        return shopAuthorization;
    }

    /**
     * 执行接口调用
     *
     * @param supplier Supplier<BaseResponse<T>>
     * @return T
     * @date 2024/2/28 17:48
     * <AUTHOR>
     */
    <T> T executeApiResponse(BaseRequest baseRequest, Supplier<BaseResponse<T>> supplier) {
        BaseResponse<T> baseResponse = null;
        try {
            baseResponse = supplier.get();
            Logs.log(baseRequest.getClass(),
                    getLogLevel(baseResponse),
                    "[调用小红书接口][{}]", baseRequest.getMethod(),
                    Logs.var("request", baseRequest),
                    Logs.var("response", baseResponse, true));
            if (baseResponse.isSuccess()) {
                return baseResponse.getData();
            }
            // token失效的情况处理
            if (TOKEN_INVALID_CODE.equals(baseResponse.getCode())) {
                throw ExceptionPlusFactory.bizException(ErrorCode.SHOP_AUTHORIZATION_EXPIRED);
            }
            throw ExceptionPlusFactory.bizException(ErrorCode.RED_BOOK_ERROR, baseResponse.getMsg());
        } catch (Exception e) {
            if (e instanceof BizException) {
                throw e;
            }
            Logs.log(baseRequest.getClass(),
                    Level.ERROR,
                    "[调用小红书接口][{}]", baseRequest.getMethod(),
                    Logs.var("request", baseRequest),
                    Logs.var("response", baseResponse, true), e);
            throw ExceptionPlusFactory.bizException(ErrorCode.RED_BOOK_ERROR, Optional.ofNullable(baseResponse).map(
                    BaseResponse::getMsg).orElse(""));
        }
    }

    private Level getLogLevel(BaseResponse<?> response) {
        if (!response.isSuccess()) {
            return Level.ERROR;
        }
        return Level.INFO;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        oauthClient = new OauthClient(redBookConfig.getApiUrl(),
                redBookConfig.getAppId(),
                redBookConfig.getVersion(),
                redBookConfig.getAppSecret());
        inventoryClient = new InventoryClient(redBookConfig.getApiUrl(),
                redBookConfig.getAppId(),
                redBookConfig.getVersion(),
                redBookConfig.getAppSecret());
        productClient = new ProductClient(redBookConfig.getApiUrl(),
                redBookConfig.getAppId(),
                redBookConfig.getVersion(),
                redBookConfig.getAppSecret());
        orderClient = new OrderClient(redBookConfig.getApiUrl(),
                redBookConfig.getAppId(),
                redBookConfig.getVersion(),
                redBookConfig.getAppSecret());
        rateLimiter = redissonClient.getRateLimiter(ThirdRedisConstants.RED_BOOK_YD_TOKEN_FRESH_LIMIT_KEY);
        rateLimiter.trySetRate(RateType.OVERALL, 1, 1, RateIntervalUnit.MINUTES);
    }

    /**
     * 执行影刀任务
     *
     * @date 2024/2/29 09:38
     * <AUTHOR>
     */
    void runTask() {
        if (rateLimiter.tryAcquire()) {
            winrobot360Api.taskStart(winrobotTokenManager.token(), TaskStartParam.of(redBookConfig.getScheduleUuid()));
        }
    }
}
