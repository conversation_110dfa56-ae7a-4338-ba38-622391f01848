package com.daddylab.supplier.item.application.purchasePayable.vo;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableOrderRelation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2023年02月27日 11:01 AM
 */
@Data
public class ApplyPayOrderRelationVO extends PurchasePayableOrderRelation {

    @ApiModelProperty("冲销过程中生成的新的应付单编码")
    private String relatePurchasePayableOrderNo;
}
