package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.order.settlement.dto.PaymentApplyStatusDto;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentApplyOrderDetail;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 采购管理-付款申请单-付款明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
public interface PaymentApplyOrderDetailMapper extends DaddyBaseMapper<PaymentApplyOrderDetail> {

    BigDecimal getSumApplyAmount(@Param("relateNos") Collection<String> relateNos, @Param("statusList") Collection<Integer> statusList);

    List<String> getRelatedDetailDbIdByRelatedNo(@Param("relateNos") Collection<String> relateNos, @Param("statusList") Collection<Integer> statusList);

    List<Long> getMatchedRelatedIdByStatus(@Param("detailSource") Integer detailSource, @Param("statusList") Collection<Integer> statusList);

    List<PaymentApplyStatusDto> getPaymentStatusBySettlementNo(@Param("no") String no);

    /**
     * 根据结算单id查询是否存在关联的待提交状态的付款单
     *
     * @param relatedId
     * @return
     */
    List<Long> getWaitSubmitPaymentApplyOrderIdBySettlementOrderId(@Param("relatedId") Long relatedId);

}
