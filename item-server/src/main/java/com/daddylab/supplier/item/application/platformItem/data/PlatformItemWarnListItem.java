package com.daddylab.supplier.item.application.platformItem.data;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2021/12/27
 */
@Data
@ApiModel("平台商品警告列表项")
public class PlatformItemWarnListItem {

    /**
     * id
     */
	@ApiModelProperty("id")
    private Long id;

    /**
     * 平台商品ID
     */
    @ApiModelProperty("平台商品ID")
	private Long platformItemId;

    /**
     * 平台商品SKU ID
     */
    @ApiModelProperty("平台商品SKU ID")
    private Long platformItemSkuId;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String itemName;

    /**
     * 商品图片
     */
	@ApiModelProperty("商品图片")
    private String itemImage;

    /**
     * 商品编码
     */
	@ApiModelProperty("商品编码")
    private String itemCode;

    /**
     * sku系统编码
     */
	@ApiModelProperty("sku系统编码")
    private String skuCode;

    /**
     * 匹配到的我们自己的商品ID
     */
	@ApiModelProperty("匹配到的我们自己的商品ID")
    private Long itemId;

    /**
     * 匹配到的我们自己的SKU ID
     */
	@ApiModelProperty("匹配到的我们自己的SKU ID")
    private Long skuId;

    /**
     * 店铺名称
     */
	@ApiModelProperty("店铺名称")
    private String shopName;

    /**
     * 匹配到的我们自己的店铺ID
     */
	@ApiModelProperty("匹配到的我们自己的店铺ID")
    private Long shopId;

    /**
     * 店铺编号
     */
    @ApiModelProperty("店铺编号")
    private String shopNo;

    /**
     * 店铺负责人ID
     */
    @ApiModelProperty("店铺负责人ID")
    private Long shopPrincipalId;

    /**
     * 店铺负责人姓名
     */
    @ApiModelProperty("店铺负责人姓名")
    private String shopPrincipalName;

    /**
     * 平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城
     */
	@ApiModelProperty("平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城")
    private Platform platform;

    /**
     * 平台ICON
     */
    @ApiModelProperty("平台ICON")
    private String platformIcon;

    /**
     * 状态 0:未处理 1:已处理
     */
    private PlatformItemWarnStatus warnStatus;

    /**
     * 1:在售 0:已下架
     */
	@ApiModelProperty("1:在售 0:已下架")
    private PlatformItemStatus status;

    /**
     * 外部平台商品ID
     */
	@ApiModelProperty("外部平台商品ID")
    private String outerItemId;

    /**
     * 外部平台商品编码
     */
	@ApiModelProperty("外部平台商品编码")
    private String outerItemCode;

    /**
     * 最后修改时间
     */
    @ApiModelProperty("最后修改时间")
    private LocalDateTime modified;

    /**
     * 预警类型
     */
    @ApiModelProperty("预警类型")
    private PlatformItemWarnType warnType;

    /**
     * 预警提示
     */
    @ApiModelProperty("预警提示")
    private String warnDesc;

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    private LocalDateTime operateTime;

    /**
     * 操作人ID
     */
    @ApiModelProperty("操作人ID")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty("操作人姓名")
    private String operatorName;

}
