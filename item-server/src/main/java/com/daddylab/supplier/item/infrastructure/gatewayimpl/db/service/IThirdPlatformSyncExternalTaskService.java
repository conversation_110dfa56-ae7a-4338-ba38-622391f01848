package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ThirdPlatformSyncExternalTask;

/**
 * <p>
 * 第三方平台同步外部任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-20
 */
public interface IThirdPlatformSyncExternalTaskService extends IDaddyService<ThirdPlatformSyncExternalTask> {

}
