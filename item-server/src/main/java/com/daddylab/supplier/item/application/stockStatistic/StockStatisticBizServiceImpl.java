package com.daddylab.supplier.item.application.stockStatistic;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.PageResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.VirtualWarehouseInventory;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IVirtualWarehouseInventoryService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWarehouseService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtStockSpecService;
import com.daddylab.supplier.item.types.stockSpec.WarehouseStockSpecStatistic;
import com.daddylab.supplier.item.types.stockStatistic.StockStatisticPageQuery;
import com.daddylab.supplier.item.types.stockStatistic.StockStatisticPageVO;
import com.daddylab.supplier.item.types.stockStatistic.WdtStockSpecStatisticQuery;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/3/1
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class StockStatisticBizServiceImpl implements StockStatisticBizService {
    private final IWarehouseService warehouseService;
    private final IWdtStockSpecService wdtStockSpecService;
//    private final IVirtualWarehouseInventoryService iVirtualWarehouseInventoryService;

    @Override
    public PageResponse<StockStatisticPageVO> query(StockStatisticPageQuery query) {
        if (CollUtil.isEmpty(query.getBusinessLine())) {
            return ResponseFactory.emptyPage();
        }

        final Page<Warehouse> warehousePage = warehouseService.pageQuery(query);
        final List<Warehouse> records = warehousePage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return ResponseFactory.emptyPage();
        }

        Set<String> pageWarehouseNos = records.stream().map(Warehouse::getNo).collect(Collectors.toSet());
        final WdtStockSpecStatisticQuery wdtStockSpecStatisticQuery = new WdtStockSpecStatisticQuery();
        wdtStockSpecStatisticQuery.setItemCode(query.getItemCode());
        wdtStockSpecStatisticQuery.setSkuCode(query.getSkuCode());
        wdtStockSpecStatisticQuery.setItemName(query.getItemName());
        wdtStockSpecStatisticQuery.setWarehouseNos(new ArrayList<>(pageWarehouseNos));

        final Map<String, WarehouseStockSpecStatistic> statistics = wdtStockSpecService.statistics(
                wdtStockSpecStatisticQuery);

        return ResponseFactory.ofPage(warehousePage, pageVo -> {
            final Optional<WarehouseStockSpecStatistic> warehouseStockSpecStatistic = Optional.ofNullable(statistics.get(
                    pageVo.getNo()));
            final StockStatisticPageVO stockStatisticPageVO = new StockStatisticPageVO();
            stockStatisticPageVO.setWarehouseNo(pageVo.getNo());
            stockStatisticPageVO.setWarehouseName(pageVo.getName());
            stockStatisticPageVO.setWarehouseVersion(pageVo.getVersion());
            stockStatisticPageVO.setBusinessLine(pageVo.getBusinessLineList());
            stockStatisticPageVO.setState(pageVo.getState());
            stockStatisticPageVO.setSpuNum(warehouseStockSpecStatistic.map(WarehouseStockSpecStatistic::getSpuNum)
                    .orElse(0));
            stockStatisticPageVO.setSkuNum(warehouseStockSpecStatistic.map(WarehouseStockSpecStatistic::getSkuNum)
                    .orElse(0));
            stockStatisticPageVO.setStockNum(warehouseStockSpecStatistic.map(WarehouseStockSpecStatistic::getTotalStock)
                    .map(BigDecimal::intValue)
                    .orElse(0));
            stockStatisticPageVO.setAvailableSendNum(warehouseStockSpecStatistic.map(WarehouseStockSpecStatistic::getTotalAvailableSendStock)
                    .map(BigDecimal::intValue)
                    .orElse(0));
            stockStatisticPageVO.setAvailableNum(warehouseStockSpecStatistic.map(WarehouseStockSpecStatistic::getTotalAvailableStock)
                    .map(BigDecimal::intValue)
                    .orElse(0));
            stockStatisticPageVO.setPurchaseOnWayNum(warehouseStockSpecStatistic.map(WarehouseStockSpecStatistic::getTotalPurchaseOnWayNum)
                    .map(BigDecimal::intValue)
                    .orElse(0));
            stockStatisticPageVO.setExternalNum(warehouseStockSpecStatistic.map(WarehouseStockSpecStatistic::getTotalWmsSyncStock)
                    .map(
                            BigDecimal::intValue)
                    .orElse(0));
            return stockStatisticPageVO;
        });
    }

//    @Override
//    public PageResponse<StockStatisticPageVO>
//    query2(StockStatisticPageQuery query) {
//        if (CollUtil.isEmpty(query.getBusinessLine())) {
//            return ResponseFactory.emptyPage();
//        }
//
//        boolean needFilter = false;
//        final WdtStockSpecStatisticQuery wdtStockSpecStatisticQuery = new WdtStockSpecStatisticQuery();
//        if (StrUtil.isNotBlank(query.getItemCode()) || StrUtil.isNotBlank(query.getSkuCode()) || StrUtil.isNotBlank(query.getItemName())) {
//            needFilter = true;
//            List<String> warehouseNos = wdtStockSpecService.lambdaQuery()
//                    .eq(StrUtil.isNotBlank(query.getItemCode()), WdtStockSpec::getGoodsNo, query.getItemCode())
//                    .eq(StrUtil.isNotBlank(query.getSkuCode()), WdtStockSpec::getSpecNo, query.getSkuCode())
//                    .like(StrUtil.isNotBlank(query.getItemName()), WdtStockSpec::getGoodsName, query.getItemName())
//                    .eq(WdtStockSpec::getDefect,0)
//                    .select(WdtStockSpec::getWarehouseNo).groupBy(WdtStockSpec::getWarehouseNo).list()
//                    .stream().map(WdtStockSpec::getWarehouseNo).collect(Collectors.toList());
//            wdtStockSpecStatisticQuery.setWarehouseNos(warehouseNos);
//        }
//        final Map<String, WarehouseStockSpecStatistic> statistics = wdtStockSpecService.statistics(wdtStockSpecStatisticQuery);
//        if (CollUtil.isEmpty(statistics)) {
//            return ResponseFactory.emptyPage();
//        }
//        Set<String> statisticsWarehouseNos = statistics.keySet();
//
//
//        if (CollUtil.isNotEmpty(query.getNos()) || StrUtil.isNotBlank(query.getNo())) {
//            if (needFilter) {
//                List<String> w1 = new ArrayList<>();
//                if (CollUtil.isNotEmpty(query.getNos())) {
//                    w1.addAll(query.getNos());
//                }
//                if (StrUtil.isNotBlank(query.getNo())) {
//                    w1.add(query.getNo());
//                }
//                Collection<String> intersection = CollectionUtils.intersection(w1, statisticsWarehouseNos);
//                if (CollUtil.isEmpty(intersection)) {
//                    return ResponseFactory.emptyPage();
//                }
//                query.setNos(new ArrayList<>(intersection));
//            }
//        } else {
//            if (needFilter) {
//                query.setNos(new ArrayList<>(statisticsWarehouseNos));
//            }
//        }
//
//        query.setState(query.getState());
//        query.setType(query.getType());
//
//        final Page<Warehouse> warehousePage = warehouseService.pageQuery(query);
//        final List<Warehouse> records = warehousePage.getRecords();
//        if (CollUtil.isEmpty(records)) {
//            return ResponseFactory.emptyPage();
//        }
//
//        return ResponseFactory.ofPage(warehousePage, pageVo -> {
//            Optional<WarehouseStockSpecStatistic> warehouseStockSpecStatistic = Optional.ofNullable(statistics.get(pageVo.getNo()));
//            final StockStatisticPageVO stockStatisticPageVO = new StockStatisticPageVO();
//            boolean isVw = pageVo.getIsVirtualWarehouse() == 1;
//            stockStatisticPageVO.setWarehouseNo(pageVo.getNo());
//            stockStatisticPageVO.setWarehouseName(pageVo.getName());
//            stockStatisticPageVO.setWarehouseVersion(pageVo.getVersion());
//            stockStatisticPageVO.setBusinessLine(pageVo.getBusinessLineList());
//            stockStatisticPageVO.setState(pageVo.getState());
//
//            if (isVw) {
//                String vwWarehouseNo = stockStatisticPageVO.getWarehouseNo();
//                List<String> realWarehouseNos = iVirtualWarehouseInventoryService.lambdaQuery().eq(VirtualWarehouseInventory::getVirtualWarehouseNo, vwWarehouseNo).list().stream()
//                        .map(VirtualWarehouseInventory::getWarehouseNo).collect(Collectors.toList());
//                Map<String, WarehouseStockSpecStatistic> vwWarehouseStatics = new HashMap<>(32);
//                if(CollUtil.isNotEmpty(realWarehouseNos)){
//                    final WdtStockSpecStatisticQuery thisVwQuery = new WdtStockSpecStatisticQuery();
//                    thisVwQuery.setWarehouseNos(realWarehouseNos);
//                    vwWarehouseStatics = wdtStockSpecService.statistics(thisVwQuery);
//                }
//
//                Collection<WarehouseStockSpecStatistic> values = vwWarehouseStatics.values();
//                if (CollUtil.isNotEmpty(values)) {
//                    Integer getSpuNum = vwWarehouseStatics.values().stream().mapToInt(WarehouseStockSpecStatistic::getSpuNum).sum();
//                    stockStatisticPageVO.setSpuNum(getSpuNum);
//                    Integer getSkuNum = vwWarehouseStatics.values().stream().mapToInt(WarehouseStockSpecStatistic::getSkuNum).sum();
//                    stockStatisticPageVO.setSkuNum(getSkuNum);
//                    BigDecimal getTotalStock = vwWarehouseStatics.values().stream().map(WarehouseStockSpecStatistic::getTotalStock).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    stockStatisticPageVO.setStockNum(getTotalStock.intValue());
//                    BigDecimal getTotalAvailableSendStock = vwWarehouseStatics.values().stream().map(WarehouseStockSpecStatistic::getTotalAvailableSendStock).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    stockStatisticPageVO.setAvailableSendNum(getTotalAvailableSendStock.intValue());
//                    BigDecimal getTotalAvailableStock = vwWarehouseStatics.values().stream().map(WarehouseStockSpecStatistic::getTotalAvailableStock).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    stockStatisticPageVO.setAvailableNum(getTotalAvailableStock.intValue());
//                    BigDecimal getTotalPurchaseOnWayNum = vwWarehouseStatics.values().stream().map(WarehouseStockSpecStatistic::getTotalPurchaseOnWayNum).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    stockStatisticPageVO.setPurchaseOnWayNum(getTotalPurchaseOnWayNum.intValue());
//                    BigDecimal getTotalWmsSyncStock = vwWarehouseStatics.values().stream().map(WarehouseStockSpecStatistic::getTotalWmsSyncStock).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    stockStatisticPageVO.setExternalNum(getTotalWmsSyncStock.intValue());
//                } else {
//                    stockStatisticPageVO.setSpuNum(0);
//                    stockStatisticPageVO.setSkuNum(0);
//                    stockStatisticPageVO.setStockNum(0);
//                    stockStatisticPageVO.setAvailableSendNum(0);
//                    stockStatisticPageVO.setAvailableNum(0);
//                    stockStatisticPageVO.setPurchaseOnWayNum(0);
//                    stockStatisticPageVO.setExternalNum(0);
//                }
//            }
//            else{
//                stockStatisticPageVO.setSpuNum(warehouseStockSpecStatistic.map(WarehouseStockSpecStatistic::getSpuNum)
//                        .orElse(0));
//                stockStatisticPageVO.setSkuNum(warehouseStockSpecStatistic.map(WarehouseStockSpecStatistic::getSkuNum)
//                        .orElse(0));
//                stockStatisticPageVO.setStockNum(warehouseStockSpecStatistic.map(WarehouseStockSpecStatistic::getTotalStock)
//                        .map(BigDecimal::intValue)
//                        .orElse(0));
//                stockStatisticPageVO.setAvailableSendNum(warehouseStockSpecStatistic.map(WarehouseStockSpecStatistic::getTotalAvailableSendStock)
//                        .map(BigDecimal::intValue)
//                        .orElse(0));
//                stockStatisticPageVO.setAvailableNum(warehouseStockSpecStatistic.map(WarehouseStockSpecStatistic::getTotalAvailableStock)
//                        .map(BigDecimal::intValue)
//                        .orElse(0));
//                stockStatisticPageVO.setPurchaseOnWayNum(warehouseStockSpecStatistic.map(WarehouseStockSpecStatistic::getTotalPurchaseOnWayNum)
//                        .map(BigDecimal::intValue)
//                        .orElse(0));
//                stockStatisticPageVO.setExternalNum(warehouseStockSpecStatistic.map(WarehouseStockSpecStatistic::getTotalWmsSyncStock)
//                        .map(BigDecimal::intValue)
//                        .orElse(0));
//            }
//            return stockStatisticPageVO;
//        });
//    }
}
