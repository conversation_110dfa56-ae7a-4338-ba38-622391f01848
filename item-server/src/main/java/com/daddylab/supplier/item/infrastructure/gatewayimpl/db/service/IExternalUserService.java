package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExternalUser;

/**
 * <p>
 * 外部账号管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
public interface IExternalUserService extends IDaddyService<ExternalUser> {

}
