package com.daddylab.supplier.item.application.afterSales.impl;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSales.AfterSalesAuthBizService;
import com.daddylab.supplier.item.application.afterSales.AfterSalesReceiveBizService;
import com.daddylab.supplier.item.application.afterSales.AfterSalesReceiveCmd;
import com.daddylab.supplier.item.common.ErrorChecker;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesReceive;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesImageType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesReceiveState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesInfoImageService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesReceiveService;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2022/10/21
 */
@Service
@AllArgsConstructor
@Slf4j
public class AfterSalesReceiveBizServiceImpl implements AfterSalesReceiveBizService {

    private final IAfterSalesReceiveService afterSalesReceiveService;
    private final AfterSalesAuthBizService afterSalesAuthBizService;
    private final IAfterSalesInfoImageService afterSalesInfoImageService;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Response receive(AfterSalesReceiveCmd cmd, Long partnerProviderId) {
        final Optional<AfterSalesReceive> afterSalesReceiveOptional = afterSalesReceiveService.lambdaQuery()
                .eq(AfterSalesReceive::getReturnOrderNo, cmd.getReturnOrderNo()).oneOpt();
        if (afterSalesReceiveOptional.isPresent()) {
            return Response.buildFailure(ErrorCode.BUSINESS_OPERATE_ERROR.getCode(), "当前单据已登记收货");
        }
        final SingleResponse<Boolean> allowAccessRefundOrder = afterSalesAuthBizService.isAllowAccessRefundOrder(
                partnerProviderId, cmd.getReturnOrderNo());
        ErrorChecker.checkAndThrowIfError(allowAccessRefundOrder, allowAccess -> allowAccess,
                ErrorCode.BUSINESS_OPERATE_ERROR, "您无权访问该退换单");
        final AfterSalesReceive afterSalesReceive = new AfterSalesReceive();
        afterSalesReceive.setReturnOrderNo(cmd.getReturnOrderNo());
        afterSalesReceive.setReturnWarehouseNo(cmd.getReturnWarehouseNo());
        afterSalesReceive.setReceiveResult(cmd.getReceiveResult());
        afterSalesReceive.setRemark(Optional.ofNullable(cmd.getRemark()).orElse(""));
        afterSalesReceive.setState(AfterSalesReceiveState.RECEIVED);
        afterSalesReceiveService.save(afterSalesReceive);
        afterSalesInfoImageService.saveImageByType(afterSalesReceive.getId(), cmd.getImageUrls(),
                AfterSalesImageType.RECEIVE);
        return Response.buildSuccess();
    }
}
