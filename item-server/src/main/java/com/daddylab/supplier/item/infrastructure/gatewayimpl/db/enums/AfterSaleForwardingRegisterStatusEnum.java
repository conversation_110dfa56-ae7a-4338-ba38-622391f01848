package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/5/27
 */
@Getter
@AllArgsConstructor
public enum AfterSaleForwardingRegisterStatusEnum implements IIntegerEnum {
    TO_BE_HANDE(0, "待处理"),
    TO_BE_FORWARD(1, "待转寄"),
    FORWARDED(2, "已转寄"),
    NO_NEED_TO_FORWARD(3, "无需转寄"),
    ;
    private final Integer value;
    private final String desc;
}
