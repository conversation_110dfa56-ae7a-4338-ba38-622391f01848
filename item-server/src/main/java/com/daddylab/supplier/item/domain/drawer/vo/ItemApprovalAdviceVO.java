package com.daddylab.supplier.item.domain.drawer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 *
 */
@Data
@ApiModel(value = "ItemApprovalAdvicesVO", description = "商品审批意见VO")
public class ItemApprovalAdviceVO implements Serializable {

    private static final long serialVersionUID = -8739591832696445681L;
    @ApiModelProperty("审批意见ID")
    private Long id;

    @ApiModelProperty("审批意见内容")
    private String advice;

}
