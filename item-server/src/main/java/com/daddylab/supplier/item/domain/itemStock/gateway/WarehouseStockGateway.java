package com.daddylab.supplier.item.domain.itemStock.gateway;

import com.daddylab.supplier.item.domain.itemStock.enums.StockType;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface WarehouseStockGateway {

    /**
     * SKU库存
     *
     * @param skuNo SKU编号
     * @return 库存数量 { 仓库编号: 库存数 }
     */
    Map<String, BigDecimal> getStock(String skuNo);

    /**
     * SKU库存
     *
     * @param skuNos SKU编号
     * @return 库存数量 { SKU编码: { 仓库编号: 库存数 } }
     */
    Map<String, Map<String, BigDecimal>> getStock(Collection<String> skuNos);

    /**
     * 指定仓库SKU库存
     *
     * @param skuNo       SKU编号
     * @param warehouseNo 仓库编号
     * @return 库存数量
     */
    BigDecimal getStock(String skuNo, String warehouseNo);

    /**
     * 指定仓库SKU库存
     *
     * @param skuNos      SKU编号
     * @param warehouseNo 仓库编号
     * @return 库存数量 {SKU编码:库存}
     */
    Map<String, BigDecimal> getStock(Collection<String> skuNos, String warehouseNo);

    /**
     * 指定仓库SKU库存
     *
     * @param skuNos       SKU编号
     * @param warehouseNos 仓库编号
     * @return 库存数量 {SKU编码:库存}
     */
    Map<String, Map<String, BigDecimal>> getStock(Collection<String> skuNos,
            Collection<String> warehouseNos);

    /**
     * SKU库存总和（所有仓库）
     *
     * @param skuNo SKU编号
     * @return 库存数量
     */
    BigDecimal getStockSum(String skuNo);

    /**
     * SKU库存总和（所有仓库）
     *
     * @param skuNos SKU编号
     * @return 库存数量 { SKU编号: 库存数 }
     */
    Map<String, BigDecimal> getStockSum(Collection<String> skuNos);

    /**
     * SKU库存总和（指定仓库集合）
     *
     * @param skuNos SKU编号
     * @return 库存数量 { SKU编号: 库存数 }
     */
    Map<String, BigDecimal> getStockSum(Collection<String> skuNos,
            Collection<String> warehouseNos);

    /**
     * SKU库存明细
     *
     * @param skuNo SKU编号
     * @return 库存数量 { 仓库编号: { 库存类型: 库存数 } }
     */
    Map<String, Map<StockType, BigDecimal>> getStockDetail(String skuNo);

    /**
     * SKU库存明细
     *
     * @param skuNos SKU编号
     * @return 库存数量 { SKU编码: { 仓库编号: { 库存类型: 库存数 } } }
     */
    Map<String, Map<String, Map<StockType, BigDecimal>>> getStockDetail(Collection<String> skuNos);

    /**
     * 获取商品库存（所有SKU库存之和）
     *
     * @param itemNo      商品编码
     * @param warehouseNo 仓库编号
     */
    BigDecimal getItemStockSum(String itemNo, String warehouseNo);

    /**
     * 获取商品库存（所有SKU库存之和）
     *
     * @param itemNos     商品编码
     * @param warehouseNo 仓库编号
     */
    default Map<String, BigDecimal> getItemStockSum(Collection<String> itemNos,
            String warehouseNo) {
        return getItemStockSum(itemNos, Collections.singletonList(warehouseNo));
    }

    /**
     * 批量获取指定商品编码商品的总库存，当给定仓库编号集合不为空时，只统计指定仓库内的库存
     *
     * @param itemNos      商品编码集合
     * @param warehouseNos 仓库编码集合
     * @return { 商品编码: 总库存 }
     */
    Map<String, BigDecimal> getItemStockSum(Collection<String> itemNos,
            Collection<String> warehouseNos);

    /**
     * 获取商品库存（所有SKU库存之和）
     *
     * @param itemNo 商品编号
     */
    BigDecimal getItemStockSum(String itemNo);

    /**
     * 批量查询商品总库存
     *
     * @param itemNos 商品编码
     * @return map { 商品编码: 商品总库存 }
     */
    Map<String, BigDecimal> getItemStockSum(Collection<String> itemNos);
}
