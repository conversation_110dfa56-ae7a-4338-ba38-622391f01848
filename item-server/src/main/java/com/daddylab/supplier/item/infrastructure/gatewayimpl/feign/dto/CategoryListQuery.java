package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 类目列表查询参数
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/4/21 4:46 下午
 */
@Data
@ApiModel(value = "类目列表查询参数", description = "类目列表查询参数")
public class CategoryListQuery {

    @ApiModelProperty(value = "类目ID")
    private Long categoryId;

    @ApiModelProperty(value = "卖家id", hidden = true)
    private Long sellerId;

    @NotNull(message = "父级类目id不能为空")
    @ApiModelProperty(value = "父级类目id 一级类目填 0", required = true)
    private Long parentId;

    @ApiModelProperty(value = "名称")
    private String name;
}
