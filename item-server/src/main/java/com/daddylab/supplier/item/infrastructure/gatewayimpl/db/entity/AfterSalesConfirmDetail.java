package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesConfirmUndertakeType;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 售后单销退确认单据明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AfterSalesConfirmDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 关联的退换单号
     */
    private String returnOrderNo;

    /**
     * 旺店通退换明细唯一键
     */
    private Integer recId;

    /**
     * 货品编号
     */
    private String goodsNo;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 商家编码
     */
    private String specNo;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 承担类型 1:按百分比 2:按金额
     */
    private AfterSalesConfirmUndertakeType undertakeType;

    /**
     * 承担金额/占比（按金额时单位为元）
     */
    private BigDecimal undertakeAmount;


}
