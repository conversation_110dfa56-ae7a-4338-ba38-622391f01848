package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.domain.itemStock.vo.ItemStockChange;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemStock;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 商品库存 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Repository
public interface ItemStockMapper extends DaddyBaseMapper<ItemStock> {
    /**
     * 获取库存变更日志
     *
     * @param itemId 商品ID
     * @param skuId  skuId
     */
    List<ItemStockChange> getSkuStockChangeLogs(@Param("itemId") Long itemId, @Param("skuId") Long skuId);

    /**
     * 入库
     *
     * @param itemId 商品ID
     * @param skuId  skuId
     * @param num    入库数量
     * @param uid    操作人ID
     * @param time   操作时间
     * @return 变更行数
     */
    int stockIn(@Param("itemId") long itemId, @Param("skuId") long skuId, @Param("num") int num
            , @Param("uid") long uid, @Param("time") long time);

    /**
     * 设定库存
     *
     * @param itemId 商品ID
     * @param skuId  skuId
     * @param num    库存数量
     * @param uid    操作人ID
     * @param time   操作时间
     * @return 变更行数（库存数量与当前数量相同或者库存记录不存在时返回0）
     */
    int stockSet(@Param("itemId") long itemId, @Param("skuId") long skuId, @Param("num") int num
            , @Param("uid") long uid, @Param("time") long time);

    /**
     * 出库
     *
     * @param itemId 商品ID
     * @param skuId  skuId
     * @param num    出库数量
     * @param uid    操作人ID
     * @param time   操作时间
     * @return 变更行数
     */
    int stockOut(@Param("itemId") long itemId, @Param("skuId") long skuId, @Param("num") int num
            , @Param("uid") long uid, @Param("time") long time);

    /**
     * 库存记录不存在时插入，如果已经存在则增加库存
     *
     * @param itemId 商品ID
     * @param skuId  skuId
     * @param num    插入库存记录时初始库存数量/或者记录已经存在时增加的库存数量
     * @param uid    操作用户ID
     * @param time   操作时间
     * @return 变更行数（新增记录返回1，修改记录返回2，数据无修改返回0）
     */
    int insertOrAddStock(@Param("itemId") long itemId, @Param("skuId") long skuId, @Param("num") int num
            , @Param("uid") long uid, @Param("time") long time);

    /**
     * 库存记录不存在时插入，如果已经存在则设置库存
     *
     * @param itemId 商品ID
     * @param skuId  skuId
     * @param num    插入库存记录时初始库存数量/或者记录已经存在时增加的库存数量
     * @param uid    操作用户ID
     * @param time   操作时间
     * @return 变更行数（新增记录返回1，修改记录返回2，数据无修改返回0）
     */
    int insertOrSetStock(@Param("itemId") long itemId, @Param("skuId") long skuId, @Param("num") int num
            , @Param("uid") long uid, @Param("time") long time);

    /**
     * 获取商品所有SKU加起来的总库存
     *
     * @param itemId 商品ID
     * @return 总库存
     */
    long getItemTotalStock(@Param("itemId") long itemId);

    /**
     * 查询sku在wdt的库存
     *
     * @param skuCode
     * @return
     */
    Long selectSkuStockNum(@Param("skuCode") String skuCode);
}
