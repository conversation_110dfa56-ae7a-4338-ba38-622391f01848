/*
package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.VirtualWarehouseInventory;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.VirtualWarehouseInventoryMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IVirtualWarehouseInventoryService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

*/
/**
 * <p>
 * 虚拟仓库存信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 *//*

@Service
public class VirtualWarehouseInventoryServiceImpl extends DaddyServiceImpl<VirtualWarehouseInventoryMapper, VirtualWarehouseInventory> implements IVirtualWarehouseInventoryService {

    @Override
    public List<VirtualWarehouseInventory> selectByVirtualWarehouseNos(List<String> virtualWarehouseNos) {
        if (virtualWarehouseNos == null || virtualWarehouseNos.isEmpty()) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(VirtualWarehouseInventory::getWarehouseNo,
                        virtualWarehouseNos)
                .list();
    }
}
*/
