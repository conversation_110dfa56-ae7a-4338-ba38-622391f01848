package com.daddylab.supplier.item.application.otherStockOut;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.controller.otherStockOut.dto.OtherStockOutQueryPage;
import com.daddylab.supplier.item.controller.otherStockOut.dto.OtherStockOutVo;

/**
 * <AUTHOR>
 * @ClassName OtherStockOutBizService.java
 * @description
 * @createTime 2022年04月01日 16:24:00
 */
public interface OtherStockOutBizService {

    /**
     * 分页查询
     * @param queryPage
     * @return
     */
    PageResponse<OtherStockOutVo> queryPage(OtherStockOutQueryPage queryPage);

    SingleResponse<OtherStockOutVo> getById(Long id);
}
