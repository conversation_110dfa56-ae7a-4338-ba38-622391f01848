package com.daddylab.supplier.item.infrastructure.schedule;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ScheduleSemaphore {
    /**
     *
     * @return 返回允许同时调度的任务进程数量
     */
    int value() default 1;

    /**
     * 连续acquire失败若干次，说明可能出现上次执行未成功释放信号量的情况，需要人工检查
     */
    int acquireFailAlarmThreshold() default 10;
}
