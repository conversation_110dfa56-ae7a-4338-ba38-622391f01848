package com.daddylab.supplier.item.controller.open;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.application.item.OpenItem;
import com.daddylab.supplier.item.application.item.OpenItemService;
import com.daddylab.supplier.item.application.item.PartnerRelatedItems;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.controller.common.dto.WarehousePageQuery;
import com.daddylab.supplier.item.controller.provider.dto.ProviderVO;
import com.daddylab.supplier.item.controller.category.dto.CategoryQueryCmd;
import com.daddylab.supplier.item.controller.category.dto.CategoryVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWarehouseService;
import com.daddylab.supplier.item.types.item.OpenItemPageQuery;
import com.daddylab.supplier.item.types.item.PartnerRelatedItemsQuery;
import com.daddylab.supplier.item.types.item.TbIdToMallIdMappingQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/12/1
 */
@Slf4j
@RestController
@RequestMapping("/open/api/item")
@Api(value = "商品数据开放接口", tags = "商品数据开放接口")
public class OpenItemController {

    @Resource
    OpenItemService openItemService;
    
    @Resource IWarehouseService warehouseService;


    @ApiOperation("P系统款号关联商品查询")
    @PostMapping("/partnerRelatedItemsQuery")
    public MultiResponse<PartnerRelatedItems> partnerRelatedItemsQuery(
            @Validated
            @RequestBody PartnerRelatedItemsQuery query) {
        return openItemService.partnerRelatedItemsQuery(query);
    }

    @ApiOperation("后端商品分页查询")
    @PostMapping("/pageQuery")
    public PageResponse<OpenItem> pageQuery(
            @Validated
            @RequestBody OpenItemPageQuery query) {
        return openItemService.pageQuery(query);
    }

    @ApiOperation("淘宝ID映射到小程序ID")
    @PostMapping("/tbIdToMallIdMapping")
    public SingleResponse<Map<String, Collection<Long>>> tbIdToMallIdMapping(@Validated
                                                                             @RequestBody TbIdToMallIdMappingQuery query) {
        return openItemService.tbIdToMallIdMapping(query);
    }

    @ApiOperation("开发商品类目接口")
    @PostMapping("/category/viewList")
    public MultiResponse<CategoryVo> categoryViewList(@Validated @RequestBody CategoryQueryCmd query) {
        return openItemService.queryCategoryList(query);
    }

    @ApiOperation("ERP供应商信息查询")
    @GetMapping("/provider")
    public SingleResponse<ProviderVO> provider(@RequestParam Long id) {
        return openItemService.queryProviderByItemId(id);
    }
    
    @ApiOperation(value = "仓库分页列表")
    @ResponseBody
    @PostMapping("/warehouseQuery")
    public PageResponse<Warehouse> warehouse(@RequestBody WarehousePageQuery query) {
        return ResponseFactory.ofPage(warehouseService.pageQuery(query));
    }



}
