package com.daddylab.supplier.item.controller.otherStockIn.dto;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OtherStockInQueryPage.java
 * @description
 * @createTime 2022年03月31日 11:20:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("其他入库单分页查询请求实体")
public class OtherStockInQueryPage  extends PageQuery {

    private static final long serialVersionUID = 1931835755019692237L;

    @ApiModelProperty(value = "其他入库单Id")
    private Long id;

    @ApiModelProperty(value = "入库单号")
    private String orderNo;

    @ApiModelProperty(value = "仓库 1袋鼠仓库(海销)")
    private Integer warehouse;

    @ApiModelProperty(value = "仓库编号")
    private String warehouseNo;

    @ApiModelProperty(value = "状态 0:全部 1:待处理 2:已取消 3:编辑中 4:待审核 5:待质检 6:质检待确认 7:已完成")
    private Integer status;

    @ApiModelProperty(value = "其他入库原因 0:全部 1:无 2:工厂WMS仓入库 3:调拨入库 4:退货入库 5:搬仓入库")
    private Integer otherReason;

    @ApiModelProperty(value = "商品id")
    private Long itemId;

    @ApiModelProperty(value = "商品SKU")
    private Long itemSku;

    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "品牌id")
    private Long brandId;

    @ApiModelProperty(value = "开始时间")
    private Long startTime;

    @ApiModelProperty(value = "结束时间")
    private Long endTime;

    private List<Integer> businessLine;
}
