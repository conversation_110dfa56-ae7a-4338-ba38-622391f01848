package com.daddylab.supplier.item.infrastructure.time;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @since 2024/6/7
 */
public class TimeWindows {

    public static LocalDateTime calculateWindowTime(CalcArgs args) {
        final LocalDateTime now = args.getNow();
        if (args.getSyncFrequency() <= 0) {
            return now;
        }
        final LocalDateTime timeZero = now.withDayOfYear(1)
                                          .truncatedTo(ChronoUnit.DAYS)
                                          .plusSeconds(args.getStartTimeOffset());
        final long secondsFromZero = ChronoUnit.SECONDS.between(timeZero, now);
        return timeZero.plusSeconds(((long) Math.floor(secondsFromZero / (float) args.getSyncFrequency()) + args.getWindowOffset()) * args.getSyncFrequency());
    }
}
