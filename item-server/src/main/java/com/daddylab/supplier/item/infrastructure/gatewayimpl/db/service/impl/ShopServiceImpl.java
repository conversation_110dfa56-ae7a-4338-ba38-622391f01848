package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Shop;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ShopMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopService;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <p>
 * 店铺 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class ShopServiceImpl extends DaddyServiceImpl<ShopMapper, Shop> implements IShopService {

    @Override
    public Optional<Shop> getBySn(String shopNo) {
        return lambdaQuery().eq(Shop::getSn, shopNo).last("limit 1").oneOpt();
    }
    
    @Override
    public Optional<Shop> getByAccountId(String accountId) {
        return lambdaQuery().eq(Shop::getAccount, accountId).last("limit 1").oneOpt();
    }
}
