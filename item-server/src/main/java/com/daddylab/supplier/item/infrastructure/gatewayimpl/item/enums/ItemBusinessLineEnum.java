package com.daddylab.supplier.item.infrastructure.gatewayimpl.item.enums;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @class ItemBusinessLineEnum.java
 * @description 描述类的作用
 * @date 2024-04-10 17:09
 */
@Getter
@AllArgsConstructor
public enum ItemBusinessLineEnum implements IEnum<Integer> {
    MALL(0, "电商"),
    DAD_CHECK(1, "老爸抽检"),
    GREEN_HOME(2, "绿色家装"),
    SHOP_INCOME(3, "商家入驻"),

    ;

    @EnumValue
    final private Integer value;
    final private String name;

    @Override
    public String getDesc() {
        return name;
    }

    final public static Map<Integer, ItemBusinessLineEnum> mappingForPsysCooperateMode = new HashMap<>();
    final public static Map<ItemBusinessLineEnum, Integer> mappingForToCooperateMode = new HashMap<>();

    static {
        mappingForPsysCooperateMode.put(1, ItemBusinessLineEnum.MALL);
        mappingForPsysCooperateMode.put(2, ItemBusinessLineEnum.SHOP_INCOME);
        mappingForPsysCooperateMode.put(4, ItemBusinessLineEnum.GREEN_HOME);

        mappingForToCooperateMode.putAll(MapUtil.inverse(mappingForPsysCooperateMode));
    }
}
