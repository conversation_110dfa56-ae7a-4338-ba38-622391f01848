package com.daddylab.supplier.item.application.message.wechat;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022/6/2 1:54 下午
 */
@Data
public class MsgEvent {


    /**
     * 触发企微通知的商品ids
     * 上新流。一般新品审核流程触发。
     */
    private List<Long> itemIdList;

    /**
     * 企微通知类型
     */
    private MsgEventType msgEventType;

    /**
     * 上新流，上新计划相关企微通知事件封装
     */
    private ItemLaunchPlaneOperator planOperator;

    /**
     * 直播话术ID
     */
    private Long liveVerbalTrickId;

    /**
     * 直播话术名称
     */
    private String liveVerbalTrickName;

    @Data
    public static class ItemLaunchPlaneOperator {

        /**
         * 计划操作人
         * 可以是提交人，也可以是修改人
         */
        Long operateUserId;
        /**
         * 计划id
         */
        Long planId;

        /**
         * 修改情况下的修改日志。demo : 「商品名称」的「首页文案/上新价」修改为「修改后文案」
         */
        String changeLog;

        public ItemLaunchPlaneOperator(Long operateUserId, Long planId, String changeLog) {
            this.operateUserId = operateUserId;
            this.planId = planId;
            this.changeLog = changeLog;
        }



    }
    /**
     * 上新流，修改负责人信息。新的负责人,修改操作触发人。
     */
    private PrincipalChangeInfo changeInfo;


    @Data
    public static class PrincipalChangeInfo {
        /**
         * 新负责人
         */
        private Long newPrincipalId;

        /**
         * 修改操作发起人
         */
        private Long changeOperateUserId;

        private String changeLog;

        public PrincipalChangeInfo(Long newPrincipalId, Long changeOperateUserId) {
            this.newPrincipalId = newPrincipalId;
            this.changeOperateUserId = changeOperateUserId;
        }

        public PrincipalChangeInfo(Long newPrincipalId, Long changeOperateUserId, String changeLog) {
            this.newPrincipalId = newPrincipalId;
            this.changeOperateUserId = changeOperateUserId;
            this.changeLog = changeLog;
        }
    }


    // ---------------------------------

    public static MsgEvent buildBySubmitPlan(Long planId, Long operatorId) {
        MsgEvent event = new MsgEvent();
        event.setItemIdList(null);
        event.setMsgEventType(MsgEventType.PLAN_SUBMIT);
        event.setPlanOperator(new ItemLaunchPlaneOperator(operatorId, planId, ""));
        event.setChangeInfo(null);
        return event;
    }

    public static MsgEvent buildByPlanChange(Long planId, Long operatorId, String log) {
        MsgEvent event = new MsgEvent();
        event.setItemIdList(null);
        event.setMsgEventType(MsgEventType.PLAN_INFO_CHANGE);
        event.setPlanOperator(new ItemLaunchPlaneOperator(operatorId, planId, log));
        event.setChangeInfo(null);
        return event;
    }

    public static MsgEvent of(List<Long> itemIdList, MsgEventType type) {
        MsgEvent event = new MsgEvent();
        event.setItemIdList(itemIdList);
        event.setMsgEventType(type);
        event.setPlanOperator(null);
        event.setChangeInfo(null);
        return event;
    }

    public static MsgEvent ofLiveVerbalTrickAuditCompleted(Long itemId, MsgEventType eventType, Long liveVerbalTrickId, String liveVerbalTrickName) {
        MsgEvent event = new MsgEvent();
        event.setItemIdList(Collections.singletonList(itemId));
        event.setMsgEventType(eventType);
        event.setLiveVerbalTrickId(liveVerbalTrickId);
        event.setLiveVerbalTrickName(liveVerbalTrickName);
        return event;
    }

    /**
     * 构建负责人改变事件
     *
     * @param newPrincipalId      新的负责人
     * @param changeOperateUserId 此改变操作人
     * @param itemIdList          商品id
     * @return
     */
    public static MsgEvent buildByPrincipalChange(Long newPrincipalId, Long changeOperateUserId, List<Long> itemIdList) {
        MsgEvent event = new MsgEvent();
        event.setItemIdList(itemIdList);
        event.setMsgEventType(MsgEventType.CHARGER_CHANGE);
        event.setPlanOperator(null);
        event.setChangeInfo(new PrincipalChangeInfo(newPrincipalId, changeOperateUserId));
        return event;
    }

    /**
     * 商品上架后某个信息发生改变
     *
     * @param itemIdList
     * @return
     */
    public static MsgEvent buildByOneFieldChange(List<Long> itemIdList) {
        MsgEvent event = new MsgEvent();
        event.setItemIdList(itemIdList);
        event.setMsgEventType(MsgEventType.ONE_FIELD_CHANGE);
        event.setPlanOperator(null);
        event.setChangeInfo(null);
        return event;
    }

    /**
     * 待上架，已上架 商品发生修改，发送的通知。
     *
     * @param itemId
     * @param operatorUserId
     * @return
     */
    public static MsgEvent buildShelfItemChange(List<Long> itemIds, Long operatorUserId, String logs) {
        MsgEvent event = new MsgEvent();
        event.setItemIdList(itemIds);
        event.setMsgEventType(MsgEventType.SHELF_ITEM_INFO_CHANG);
        event.setPlanOperator(null);
        event.setChangeInfo(new PrincipalChangeInfo(null, operatorUserId, logs));
        return event;
    }


}
