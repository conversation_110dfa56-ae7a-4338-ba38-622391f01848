package com.daddylab.supplier.item.domain.stockInOrder.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("采购入库单查看明细返回数据模型")
public class StockInOrderDetailVo {
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 是否已删除
     */
    @ApiModelProperty(value = "是否已删除")
    private Integer isDel;
    /**
     * 关联采购入库单id
     **/
    @ApiModelProperty(value = "关联采购入库单id")
    private Long stockInOrderId;
    /**
     * 商品skuCode
     **/
    @ApiModelProperty(value = "商品skuCode")
    private String itemSkuCode;
    /**
     * 商品名称
     **/
    @ApiModelProperty(value = "商品名称")
    private String itemName;
    /**
     * 规格
     **/
    @ApiModelProperty(value = "规格")
    private String specifications;
    /**
     * 收料单位 （默认为“个”）可直接同步采购单中的【采购单位】
     **/
    @ApiModelProperty(value = "收料单位 （默认为“个”）可直接同步采购单中的【采购单位】")
    private String unit;
    /**
     * 交货数量 同步采购单中采购数量
     **/
    @ApiModelProperty(value = "交货数量 同步采购单中采购数量")
    private Integer receiptQuantity;
    /**
     * 实际入仓数量(旺店通回填)
     **/
    @ApiModelProperty(value = "实际入仓数量(旺店通回填)")
    private Integer realReceiptQuantity;
    /**
     * 库存状态
     **/
    @ApiModelProperty(value = "库存状态")
    private Integer stockState;
    /**
     * 仓库id 默认同步采购单中，预计入仓仓库，可根据实际入仓仓库修改
     **/
    @ApiModelProperty(value = "仓库id 默认同步采购单中，预计入仓仓库，可根据实际入仓仓库修改")
    private String warehouseNo;
    /**
     * 仓库 (前端)
     **/
    @ApiModelProperty(value = "仓库")
    private String stock;
    /**
     * 是否是赠品,0不是,1是。 同步采购单中，可修改
     **/
    @ApiModelProperty(value = "是否是赠品,0不是,1是。 同步采购单中，可修改")
    private Integer isGift;
    /**
     * 含税单价
     **/
    @ApiModelProperty(value = "含税单价")
    private BigDecimal taxPrice;
    /**
     * 含税总额
     **/
    @ApiModelProperty(value = "含税总额")
    private BigDecimal taxAmount;
    /**
     * 税率(实际计算值，非百分比)
     **/
    @ApiModelProperty(value = "税率(实际计算值，非百分比)")
    private BigDecimal taxRate;
    /**
     * 税额
     **/
    @ApiModelProperty(value = "税额")
    private BigDecimal taxQuota;
    /**
     * 价税合计 单价（税前）* 采购数量
     **/
    @ApiModelProperty(value = "价税合计")
    private BigDecimal totalPriceTax;
    /**
     * 税后单价 单价（税前）* 税率
     **/
    @ApiModelProperty(value = "税后单价")
    private BigDecimal afterTaxPrice;
    /**
     * 税后金额 税后单价 * 采购数量
     **/
    @ApiModelProperty(value = "税后金额")
    private BigDecimal afterTaxAmount;
    /**
     * 采购数量
     **/
    @ApiModelProperty(value = "采购数量")
    private Integer purchaseQuantity;
    /**
     * 备注
     **/
    @ApiModelProperty(value = "备注")
    private String remark;


    @ApiModelProperty("合作方+业务类型")
    private List<CorpBizTypeDTO> corpBizType;
}
