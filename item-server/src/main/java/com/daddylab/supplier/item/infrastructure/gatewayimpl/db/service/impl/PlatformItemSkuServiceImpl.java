package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.daddylab.supplier.item.domain.platformItem.vo.ListOuterSkuCodeQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.PlatformSkuRefQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemSkuMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemSkuService;
import com.daddylab.supplier.item.types.platformItem.ComposeSkuPlatformItem;
import com.daddylab.supplier.item.types.platformItem.PlatformSkuOfSameSkuCombinationItem;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 平台商品（投放到其他平台的商品）SKU维度 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Service
public class PlatformItemSkuServiceImpl
        extends DaddyServiceImpl<PlatformItemSkuMapper, PlatformItemSku>
        implements IPlatformItemSkuService {

    @Override
    public List<PlatformItemSku> platformSkuQuery(PlatformSkuRefQuery query) {
        final LambdaQueryChainWrapper<PlatformItemSku> queryWrapper = lambdaQuery();
        queryWrapper.in(PlatformItemSku::getOuterSkuCode, query.getOuterSkuCodes());
        queryWrapper.eq(PlatformItemSku::getShopNo, query.getShopNo());
        if (Objects.nonNull(query.getStatus())) {
            queryWrapper.eq(PlatformItemSku::getStatus, query.getStatus());
        }
        return queryWrapper.list();
    }

    @Override
    public List<ComposeSkuPlatformItem> platformSkuQueryOfComposeSku(PlatformSkuRefQuery query) {
        return getDaddyBaseMapper().platformSkuQueryOfComposeSku(query);
    }

    @Override
    public List<PlatformSkuOfSameSkuCombinationItem> platformSkuQueryOfSameSkuCombinationItem(
            PlatformSkuRefQuery query) {
        return getDaddyBaseMapper().platformSkuQueryOfSameSkuCombinationItem(query);
    }

    @Override
    public List<PlatformItemSku> listByPlatformItemId(Collection<Long> platformItemIds) {
        return lambdaQuery().in(PlatformItemSku::getPlatformItemId, platformItemIds).list();
    }

    @Override
    public List<PlatformItemSku> listByPlatformItemId(Long platformItemId) {
        return lambdaQuery().eq(PlatformItemSku::getPlatformItemId, platformItemId).list();
    }
    
    @Override
    public List<PlatformItemSku> listByOuterSkuIds(Collection<String> outerSkuIds) {
        return lambdaQuery().in(PlatformItemSku::getOuterSkuId, outerSkuIds).list();
    }
    
    @Override
    public List<String> listOuterSkuCode(ListOuterSkuCodeQuery query) {
        return baseMapper.listOuterSkuCode(query);
    }
}
