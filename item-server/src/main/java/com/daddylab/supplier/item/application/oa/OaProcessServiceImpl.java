package com.daddylab.supplier.item.application.oa;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.daddylab.supplier.item.infrastructure.oa.*;
import com.daddylab.supplier.item.infrastructure.utils.FileUtil;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@RequiredArgsConstructor
@Service
public class OaProcessServiceImpl implements OaProcessService {
    public static final String DEFAULT_USER = "ban.dai";
    private final OaClient oaClient;
    private final DiskFileItemFactory diskFileItemFactory = new DiskFileItemFactory();

    @Override
    public OaResponse<OaProcessStartResponse> processStart(
            String loginName,
            String templateCode,
            boolean draft,
            String subject,
            List<String> attachments,
            Object formData) {
        final String token = oaClient.token(loginName);
        final OaProcessStartRequestData request = new OaProcessStartRequestData();
        request.setTemplateCode(templateCode);
        request.setDraft(draft ? "1" : "0");
        request.setSubject(subject);
        if (CollectionUtil.isNotEmpty(attachments)) {
            throw new IllegalArgumentException("暂不支持附件上传");
        }
        request.setData(formData);

        final OaProcessStartRequest oaProcessStartRequest = new OaProcessStartRequest();
        oaProcessStartRequest.setData(request);
        return oaClient.processStart(token, oaProcessStartRequest);
    }

    @Override
    public boolean processCancel(String summaryId, String affairId, String loginName) {
        final OaProcessCancelRequest request = new OaProcessCancelRequest();
        request.setSummaryId(summaryId);
        request.setAffairId(affairId);
        request.setMember(loginName);

        final String token = getToken(loginName);
        return oaClient.processCancel(token, request);
    }

    @Override
    public boolean processStop(String affairId, String loginName, String comment) {
        final OaProcessStopRequest request = new OaProcessStopRequest();
        request.setAffairId(affairId);
        request.setMember(loginName);
        request.setRepealComment(comment);

        final String token = getToken(loginName);
        return oaClient.processStop(token, request);
    }

    @Override
    public OaUploadAttachmentResponse uploadAttachment(String loginName, String fileUrl)
            throws IOException {
        final String token = getToken(loginName);
        final String mimeType = FileUtil.getMimeType(fileUrl);
        final String name = getName(fileUrl);
        final FileItem fileItem = diskFileItemFactory.createItem("file", mimeType, false, name);
        final OutputStream outputStream = fileItem.getOutputStream();
        HttpUtil.download(fileUrl, outputStream, true);
        final CommonsMultipartFile commonsMultipartFile = new CommonsMultipartFile(fileItem);
        return oaClient.uploadAttachment(token, commonsMultipartFile);
    }

    @NonNull
    private static String getName(String fileUrl) {
        String name = FileNameUtil.getName(fileUrl);
        final int indexOfMark = name.indexOf("?");
        if (indexOfMark > -1) {
            name = name.substring(0, indexOfMark);
        }
        return name;
    }

    private String getToken(String loginName) {
        return oaClient.token(
                Optional.ofNullable(loginName)
                        .filter(StrUtil::isNotBlank)
                        .orElse(DEFAULT_USER));
    }
}
