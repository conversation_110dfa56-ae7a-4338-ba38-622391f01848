package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.KingDeeSkuProvider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.KingDeeSkuProviderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IKingDeeSkuProviderService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-01
 */
@Service
public class KingDeeSkuProviderServiceImpl extends DaddyServiceImpl<KingDeeSkuProviderMapper, KingDeeSkuProvider> implements IKingDeeSkuProviderService {

}
