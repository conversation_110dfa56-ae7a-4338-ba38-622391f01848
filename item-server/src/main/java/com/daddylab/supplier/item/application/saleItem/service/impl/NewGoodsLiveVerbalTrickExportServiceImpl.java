package com.daddylab.supplier.item.application.saleItem.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.img.FontUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.poi.word.Word07Writer;
import cn.hutool.poi.word.WordUtil;
import com.alibaba.cola.dto.PageResponse;
import com.daddylab.supplier.item.application.saleItem.query.NewGoodsQueryPage;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsLiveVerbalTrickExportService;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsGroupVO;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsSpuVO;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsVo;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.exportTask.ExportTaskGateway;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchModuleAdvice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemLaunchModuleAdviceMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerModuleId;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemLaunchProcessNodeId;
import com.google.common.collect.Sets;
import java.awt.Font;
import java.io.File;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/11/9
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class NewGoodsLiveVerbalTrickExportServiceImpl implements
        NewGoodsLiveVerbalTrickExportService {

    private final NewGoodsBizService newGoodsBizService;
    private final IItemDrawerService itemDrawerService;
    private final ExportTaskGateway exportTaskGateway;
    private final FileGateway fileGateway;
    private final ItemLaunchModuleAdviceMapper itemLaunchModuleAdviceMapper;

    @Resource(name = "UserGatewayCacheImpl")
    private UserGateway userGateway;

    @Override
    public void export(NewGoodsQueryPage queryPage) {
        ExportTask exportTask = new ExportTask();
        exportTask.setStatus(ExportTaskStatus.RUNNING);
        exportTask.setName("直播话术导出-" + DateUtil.formatNow(DatePattern.PURE_DATETIME_PATTERN));
        exportTask.setType(ExportTaskType.NEW_GOODS);
        final Long taskId = exportTaskGateway.saveOrUpdateExportTask(exportTask);
        exportTask.setId(taskId);

        log.info("直播话术导出 taskId={} name={} params={}", taskId, exportTask.getName(),
                queryPage);

        final Runnable runnable = () -> {
            try {
                queryPage.setPageIndex(1);
                queryPage.setPageSize(999999);
                PageResponse<NewGoodsGroupVO> response = newGoodsBizService.queryPageGroupBySpu(
                        queryPage);
                final File file = FileUtil.file("/tmp/" + exportTask.getName() + ".docx");
                final Word07Writer writer = WordUtil.getWriter(file);
                final Font h1Font = FontUtil.createFont(Font.SANS_SERIF, 16);
                final Font bodyFont = FontUtil.createFont(Font.SERIF, 12);
                final Font adviceFont = FontUtil.createFont(Font.DIALOG, 9);
                Set<Long> itemIdExported = Sets.newHashSet();
                for (NewGoodsGroupVO datum : response.getData()) {
                    NewGoodsSpuVO spu = datum.getSpu();
                    final Long itemId = spu.getItemId();
                    if (itemIdExported.contains(itemId)) {
                        continue;
                    }
                    itemIdExported.add(itemId);
                    final Optional<ItemDrawer> itemDrawerOptional = itemDrawerService.lambdaQuery()
                            .eq(ItemDrawer::getItemId, itemId).oneOpt();
                    final String liveVerbalTrick = itemDrawerOptional.map(
                            ItemDrawer::getLiveVerbalTrick).orElse("暂无直播话术");
                    writer.addText(h1Font, Optional.ofNullable(spu.getStandardName())
                            .filter(StringUtil::isNotBlank).orElse(spu.getName()));
                    writer.addText(bodyFont, liveVerbalTrick);

                    final List<ItemLaunchModuleAdvice> itemLaunchModuleAdvices = itemLaunchModuleAdviceMapper.queryLatestAuditAdvicesBatch(
                            Collections.singletonList(itemId));
                    final Optional<ItemLaunchModuleAdvice> legalAdvice = itemLaunchModuleAdvices.stream()
                            .filter(v -> v.getNode() == ItemLaunchProcessNodeId.LEGAL
                                    && v.getModule() == ItemDrawerModuleId.LIVE_VERBAL_TRICK)
                            .findFirst();
                    if (Objects.nonNull(spu.getLegal())) {
                        writer.addText(adviceFont, StringUtil.format("{}：{}",
                                spu.getLegal().getNickname(),
                                legalAdvice.map(ItemLaunchModuleAdvice::getAdvice).orElse("无")));
                    } else {
                        writer.addText(adviceFont, "无法务审批意见");
                    }
                    final Optional<ItemLaunchModuleAdvice> qcAdvice = itemLaunchModuleAdvices.stream()
                            .filter(v -> v.getNode() == ItemLaunchProcessNodeId.QC
                                    && v.getModule() == ItemDrawerModuleId.LIVE_VERBAL_TRICK)
                            .findFirst();
                    if (CollectionUtil.isNotEmpty(spu.getQcs())) {
                        writer.addText(adviceFont, StringUtil.format("{}：{}",
                                spu.getQcs().stream().findFirst().map(DadStaffVO::getNickname)
                                        .orElse(""),
                                qcAdvice.map(ItemLaunchModuleAdvice::getAdvice).orElse("无")));
                    } else {
                        writer.addText(adviceFont, "无QC审批意见");
                    }
                    writer.addText(adviceFont, "");
                }
                writer.flush();

                UploadFileAction action = UploadFileAction.ofFile(
                        "/" + exportTask.getName() + ".docx",
                        file);
                final String downloadUrl = fileGateway.uploadFile(action).getUrl();
                log.info("直播话术导出成功，下载链接：{}", downloadUrl);

                exportTask.setStatus(ExportTaskStatus.SUCCESS);
                exportTask.setDownloadUrl(downloadUrl);
            } catch (Exception e) {
                log.error("直播话术导出异常 taskId={} name={} params={}", taskId, exportTask.getName(),
                        queryPage, e);
                exportTask.setStatus(ExportTaskStatus.FAIL);
                exportTask.setError(StringUtil.truncateByMaxBytes(e.getMessage(), 20000));
            } finally {
                try {
                    exportTaskGateway.saveOrUpdateExportTask(exportTask);
                } catch (Exception e) {
                    log.error("直播话术导出，更新导出记录失败 task:{}", exportTask);
                }
            }
        };
        ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(RunnableWrapper.of(runnable));
    }
}
