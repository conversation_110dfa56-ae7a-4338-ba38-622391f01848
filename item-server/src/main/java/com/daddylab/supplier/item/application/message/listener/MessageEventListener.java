package com.daddylab.supplier.item.application.message.listener;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.application.message.event.AbstractMessageEvent;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
import com.daddylab.supplier.item.domain.message.dto.MsgFillObj;
import com.daddylab.supplier.item.domain.message.dto.MsgRecipientObj;
import com.daddylab.supplier.item.domain.message.entity.MessageEntity;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.email.EmailService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Log;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Message;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.MessageConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ILogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IMessageConfigService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IMessageService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.submail.SubMailService;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/22 2:31 下午
 * @description
 */
@Slf4j
@EventBusListener(value = "messageEventListener", mode = EventBusListener.MODE.ASYNC)
public class MessageEventListener {

    @Autowired
    IMessageConfigService iMessageConfigService;

    @Autowired
    IMessageService iMessageService;

    @Autowired
    ILogService iLogService;

    @Resource
    EmailService emailService;

    @Autowired
    SubMailService subMailService;

    @Autowired
    @Qualifier("UserGatewayCacheImpl")
    UserGateway userGateway;

    @Autowired
    RefreshConfig refreshConfig;

    @Subscribe
    public void listener(AbstractMessageEvent event) {
        if (Objects.isNull(event)) {
            log.error("receive event is null");
            return;
        }
        final Optional<MessageConfig> configOptional = iMessageConfigService.getByOperationType(event.getOperationType(), true);
        if (!configOptional.isPresent()) {
            log.warn("query configuration according to operation type,result is null,operationType:{}", event.getOperationType());
            return;
        }

        configOptional.ifPresent(config -> {
            // 消息处理，存库，按需推送相应的消息。
            if ((config.getPushDefault() > 0) || (config.getPushRemind() > 0) || (config.getPushText() > 0) || (config.getPushMail() > 0)) {
                handleMessage(config, event);
            }
        });
    }

    private void handleMessage(MessageConfig config, AbstractMessageEvent event) {
        final String recipients = config.getRecipients();
        if (StringUtils.isBlank(recipients)) {
            log.warn("message recipients is empty");
            return;
        }

        final List<MsgRecipientObj> recipientList = JsonUtil.parse(recipients, new TypeReference<List<MsgRecipientObj>>() {
        });
        if (CollUtil.isEmpty(recipientList)) {
            log.warn("message recipient list is empty");
            return;
        }

        MessageEntity messageEntity = new MessageEntity(config);
        final List<MsgFillObj> messageFillObjList = event.getFillObjList();

        // message 数据入库
        List<Message> messageList = new LinkedList<>();
        recipientList.forEach(recipient -> {
            if (Objects.nonNull(recipient.getStaffId())) {
                messageList.add(messageEntity.getMessage(messageFillObjList, recipient.getStaffId()));
            }
        });
        iMessageService.saveBatch(messageList);

        // 是否需要对外推送
        if ((config.getPushText() > 0 || config.getPushMail() > 0)) {
            // 查询出接收者的手机号，邮件。
            List<Long> recipientUserIdList = recipientList.stream().map(MsgRecipientObj::getStaffId).collect(Collectors.toList());
            Map<Long, StaffInfo> longStaffInfoMap = userGateway.batchQueryStaffInfoByIds(recipientUserIdList);

            for (Map.Entry<Long, StaffInfo> entry : longStaffInfoMap.entrySet()) {
                // 如果需要发送短信
                if (config.getPushText() > 0) {
                    String textContent = messageEntity.getTextContent(config.getTemplate(), messageFillObjList, event.getPushLink());
                    sendMobileText(entry.getValue().getMobile(), textContent);
                }
                // 如果需要发送邮件
                if (config.getPushMail() > 0) {
                    String emailContent = messageEntity.getEmailContent(config.getTemplate(), messageFillObjList);
                    sendEmail(entry.getValue().getEmail(), emailContent, event.getEmailSubject(), event.getPushLink());
                }
            }
        }
    }

    private void sendMobileText(String mobile, String content) {
        // 模拟发短信
        if ("1".equals(refreshConfig.getMockText())) {
            return;
        }
        if (StringUtil.isBlank(mobile)) {
            return;
        }

        try {
            subMailService.send(mobile, content);
        } catch (Exception e) {
            log.error("后端商品消息发送短信失败，mobile:{},content:{}", mobile, content);
            handlePushError(content, 11, e);
        }
    }

    private void sendEmail(String email, String content, String subject, String pushLink) {
        if ("1".equals(refreshConfig.getMockMail())) {
            return;
        }
        if (StringUtil.isBlank(email)) {
            return;
        }

        try {
            emailService.sendUrlMail(email, subject, content, pushLink);
        } catch (Exception e) {
            log.error("后端商品消息发送邮件失败，email:{},content:{}", email, content, e);
            handlePushError(content, 12, e);
        }

    }

    /**
     * 记录对外消息推送失败的情况
     */
    private void handlePushError(String content, Integer type, Throwable error) {
        Log log = new Log();
        log.setReq(content);
        log.setType(type);
        log.setError(JsonUtil.toJson(error));
        iLogService.save(log);

    }






}
