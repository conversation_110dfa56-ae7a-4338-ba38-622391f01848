package com.daddylab.supplier.item.domain.drawer.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;

/**
 *
 * @className LaunchReviewForm
 * <AUTHOR>
 * @date 2024/4/15 13:41
 * @description: TODO 
 */
@NoArgsConstructor
@Data
@ApiModel(value = "LaunchReviewForm-发起复审", description = "发起复审")
public class LaunchReviewForm implements Serializable {

    @NotNull
    @ApiModelProperty(value = "商品ID")
    private Long itemId;

    @NotNull
    @ApiModelProperty(value = "tab类型 1-商品资料 2-直播话术 3-培训资料")
    private Integer tabType;
}
