package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.KingDeeSkuPrice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.KingDeeSkuPriceMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IKingDeeSkuPriceService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Service
public class KingDeeSkuPriceServiceImpl extends DaddyServiceImpl<KingDeeSkuPriceMapper, KingDeeSkuPrice> implements IKingDeeSkuPriceService {

}
