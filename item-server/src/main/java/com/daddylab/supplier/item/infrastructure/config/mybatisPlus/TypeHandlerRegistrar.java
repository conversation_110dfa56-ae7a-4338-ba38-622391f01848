package com.daddylab.supplier.item.infrastructure.config.mybatisPlus;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.UnknownTypeHandler;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;
import org.springframework.util.ClassUtils;
import org.springframework.util.ReflectionUtils;

/**
 * <AUTHOR>
 * @since 2023/11/7
 */
@Slf4j
public class TypeHandlerRegistrar implements ImportBeanDefinitionRegistrar, BeanFactoryAware {

  public static final String BEAN_NAME = "MybatisTypeHandlerRegistrar";
  private BeanFactory beanFactory;

  @Override
  public void setBeanFactory(@NonNull BeanFactory beanFactory) throws BeansException {
    this.beanFactory = beanFactory;
  }

  @Value
  public static class TypeHandlerRegistration {
    Class<?> javaType;
    Class<?> typeHandlerClass;
  }

  @RequiredArgsConstructor
  private static class MybatisTypeHandlerConfigurer implements ConfigurationCustomizer {
    private final Set<TypeHandlerRegistration> typeHandlerRegistrations;

    @Override
    public void customize(MybatisConfiguration configuration) {
      for (TypeHandlerRegistration typeHandlerRegistration : typeHandlerRegistrations) {
        configuration
            .getTypeHandlerRegistry()
            .register(
                typeHandlerRegistration.getJavaType(),
                typeHandlerRegistration.getTypeHandlerClass());
      }
    }
  }

  @Override
  @SneakyThrows
  public void registerBeanDefinitions(
      @NonNull AnnotationMetadata importingClassMetadata,
      @NonNull BeanDefinitionRegistry registry) {
    MetadataReaderFactory metadataReaderFactory = beanFactory.getBean(MetadataReaderFactory.class);
    final PathMatchingResourcePatternResolver pathMatchingResourcePatternResolver =
        new PathMatchingResourcePatternResolver();
    ImportBeanDefinitionRegistrar.super.registerBeanDefinitions(importingClassMetadata, registry);

    AnnotationAttributes handlerScanAttrs =
        AnnotationAttributes.fromMap(
            importingClassMetadata.getAnnotationAttributes(
                TableFieldsTypeHandlerScan.class.getName()));

    final HashSet<TypeHandlerRegistration> typeHandlerRegistrations = new HashSet<>();
    if (handlerScanAttrs != null) {
      final String[] packages = handlerScanAttrs.getStringArray("value");
      for (String packagePattern : packages) {
        final String locationPattern =
            ResourcePatternResolver.CLASSPATH_ALL_URL_PREFIX
                + ClassUtils.convertClassNameToResourcePath(packagePattern)
                + "/**/*.class";
        log.info("mybatis type handler scan: {}", locationPattern);
        final Resource[] resources =
            pathMatchingResourcePatternResolver.getResources(locationPattern);
        for (Resource resource : resources) {
          final MetadataReader metadataReader = metadataReaderFactory.getMetadataReader(resource);
          final Class<?> aClass =
              ClassUtils.forName(
                  metadataReader.getClassMetadata().getClassName(), getClass().getClassLoader());
          log.debug("mybatis type handler scan class: {}", aClass.getSimpleName());
          ReflectionUtils.doWithFields(
              aClass,
              field -> {
                final TableField annotation = field.getAnnotation(TableField.class);
                if (annotation != null && annotation.typeHandler() != UnknownTypeHandler.class) {
                  final Class<?> type = field.getType();
                  if (!Collection.class.isAssignableFrom(type)) {
                    typeHandlerRegistrations.add(
                        new TypeHandlerRegistration(type, annotation.typeHandler()));
                  }
                }
              });
        }
      }
      log.info("mybatis type handler auto register: {}", typeHandlerRegistrations);
      BeanDefinitionBuilder builder =
          BeanDefinitionBuilder.genericBeanDefinition(MybatisTypeHandlerConfigurer.class);
      builder.addConstructorArgValue(typeHandlerRegistrations);
      registry.registerBeanDefinition(BEAN_NAME, builder.getBeanDefinition());
    }
  }
}
