package com.daddylab.supplier.item.application.order.settlement.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年11月09日 2:05 PM
 */
@Data
public class RefundManageExportSheet {

    @ExcelProperty("店铺")
    private String shop;

    @ExcelProperty("店铺编码")
    private String shopNo;

    @ExcelProperty("退换仓库编号")
    private String warehouseNo;

    /**
     * 次字段需要后期二次查选转换
     */
    @ExcelProperty("退换仓库")
    private String warehouse;

    @ExcelProperty("原始单号")
    private String originalNo;

    @ExcelProperty("（旺）订单编号")
    private String orderNo;

    @ExcelProperty("退换单号")
    private String refundNo;

    @ExcelProperty("出库单编号")
    private String stockOutOrderNo;

    @ExcelProperty("货品名称")
    private String goodName;

    @ExcelProperty("商家编码")
    private String erpCode;

    @ExcelProperty("货品编号")
    private String goodCode;

    @ExcelProperty("规格名称")
    private String specification;

    @ExcelProperty("退款数量")
    private BigDecimal refundNum;

    @ExcelProperty("物流公司")
    private String logisticsCompany;

    @ExcelProperty("物流编号")
    private String logisticsNo;

    @ExcelProperty("下单时间")
    private String tradeTime;

    @ExcelProperty("退款金额")
    private BigDecimal refundAmount;

    @ExcelProperty("实际退款")
    private BigDecimal actualRefundAmount;

    @ExcelProperty("组合装编码")
    private String suiteNo;

    @ExcelProperty("组合装名称")
    private String suiteName;

    @ExcelProperty("（旺）订单类型")
    private String tradeTypeStr;
    @ExcelIgnore
    private Integer tradeType;

    @ExcelIgnore
    private Integer status;
    @ExcelProperty("退换状态")
    private String statusStr;

    @ExcelProperty("买家留言")
    @ColumnWidth(100)
    private String customerMsg;

    @ExcelProperty("客服备注")
    @ColumnWidth(100)
    private String customerServiceMsg;

    @ExcelProperty("打印备注")
    @ColumnWidth(100)
    private String printRemark;


}
