package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSkuPrice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemSkuPriceMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuPriceService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 商品sku价格 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-07
 */
@Service
public class ItemSkuPriceServiceImpl extends DaddyServiceImpl<ItemSkuPriceMapper, ItemSkuPrice> implements IItemSkuPriceService {


    @Override
    public List<ItemSkuPrice> listPriceBySkuIds(Integer type, Collection<Long> skuIds) {
        return lambdaQuery().eq(ItemSkuPrice::getType, type)
                            .in(ItemSkuPrice::getSkuId, skuIds)
                            .orderByDesc(ItemSkuPrice::getId)
                            .list();
    }
}
