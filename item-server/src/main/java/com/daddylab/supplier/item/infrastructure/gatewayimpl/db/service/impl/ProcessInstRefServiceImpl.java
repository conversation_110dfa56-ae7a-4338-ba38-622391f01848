package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ProcessInstRef;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ProcessInstRefMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IProcessInstRefService;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.process.ProcessBusinessType;
import com.daddylab.supplier.item.types.process.ProcessType;
import org.springframework.stereotype.Service;

/**
 * 流程业务关联 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Service
public class ProcessInstRefServiceImpl
        extends DaddyServiceImpl<ProcessInstRefMapper, ProcessInstRef>
        implements IProcessInstRefService {
    @Override
    public void saveProcessInstRef(
            Long businessId,
            String processInstId,
            ProcessBusinessType processBusinessType,
            ProcessType processType,
            Object data) {
        final ProcessInstRef processInstRef = new ProcessInstRef();
        processInstRef.setProcessInstId(processInstId);
        processInstRef.setType(processBusinessType.getValue());
        processInstRef.setBusinessId(businessId);
        processInstRef.setProcessType(processType.getValue());
        processInstRef.setData(data != null ? JsonUtil.toJson(data) : null);
        save(processInstRef);
    }
}
