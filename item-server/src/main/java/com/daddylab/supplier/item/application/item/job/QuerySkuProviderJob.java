//package com.daddylab.supplier.item.application.item.job;
//
//import cn.hutool.core.collection.CollUtil;
//import com.daddylab.job.core.context.XxlJobHelper;
//import com.daddylab.job.core.handler.annotation.XxlJob;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.KingDeeSkuProvider;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IKingDeeSkuProviderService;
//import com.daddylab.supplier.item.infrastructure.kingdee.dto.KingDeeSkuResp;
//import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.LinkedList;
//import java.util.List;
//
///**
// * <AUTHOR> up
// * @date 2022年08月01日 10:06 AM
// */
//@Slf4j
//@Service
//public class QuerySkuProviderJob {
//
//    @Resource
//    ReqTemplate reqTemplate;
//
//    @Resource
//    IKingDeeSkuProviderService iKingDeeSkuProviderService;
//
//    @XxlJob("querySkuProviderJob")
//    public void querySkuProviderJob() throws Exception {
//        XxlJobHelper.log("-------- querySkuProviderJob start ---------");
//        long start = 0;
//        long limit = 500;
//
//        List<KingDeeSkuResp> kingDeeSkuRespList = reqTemplate.querySkuProviderList(start, limit);
//        while (CollUtil.isNotEmpty(kingDeeSkuRespList)) {
//            XxlJobHelper.log("-------- querySkuProviderJob one cycle size:{} ---------", kingDeeSkuRespList.size());
//
//            List<KingDeeSkuProvider> list = new LinkedList<>();
//            kingDeeSkuRespList.forEach(kingDeeSkuResp -> {
//                KingDeeSkuProvider one = new KingDeeSkuProvider();
//                one.setProviderNo(kingDeeSkuResp.getProviderNo());
//                one.setSkuCode(kingDeeSkuResp.getCode());
//                one.setSkuName(kingDeeSkuResp.getName());
//                list.add(one);
//            });
//
//            iKingDeeSkuProviderService.saveBatch(list);
//
//            start = start + limit;
//            kingDeeSkuRespList = reqTemplate.querySkuProviderList(start, limit);
//        }
//
//        XxlJobHelper.log("-------- querySkuProviderJob end ---------");
//    }
//}
