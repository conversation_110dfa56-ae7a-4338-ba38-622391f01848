package com.daddylab.supplier.item.application.platformItem.query;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.PositiveOrZero;

/**
 * <AUTHOR>
 * @since 2021/12/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("其他平台商品查询")
public class OtherPlatformItemPageQuery extends PageQuery {
    private static final long serialVersionUID = -7283155238919206389L;
    @PositiveOrZero(message = "平台商品ID不能为空")
    @ApiModelProperty("平台商品ID")
    Long platformItemId;
}
