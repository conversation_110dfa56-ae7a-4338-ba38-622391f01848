package com.daddylab.supplier.item.application.criticalPush;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataPush;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDataPushService;
import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQProducer;
import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQTopic;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import java.util.Objects;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/4/16
 */
@Component("AsyncCriticalPushManagerImpl")
@Slf4j
public class AsyncCriticalPushManagerImpl implements AsyncCriticalPushManager {

    private static final long serialVersionUID = 1L;
    private final RocketMQProducer rocketMQProducer;
    private final IDataPushService dataPushService;

    public AsyncCriticalPushManagerImpl(
            RocketMQProducer rocketMQProducer,
            IDataPushService dataPushService) {
        this.rocketMQProducer = rocketMQProducer;
        this.dataPushService = dataPushService;
    }

    @Override
    public void push(SourceType sourceType, long sourceId, TargetType targetType,
            MethodInvocationDescriptor mid, Long pushId) throws CriticalPushException {
        sendMQ(sourceType, sourceId, targetType, mid, pushId);
    }

    private <P, R> void sendMQ(SourceType sourceType, long sourceId, TargetType targetType,
            MethodInvocationDescriptor mid, Long pushId) {
        if (Objects.isNull(pushId) || pushId == 0) {
            pushId = generatePushId(sourceType, sourceId, targetType);
        }
        final CriticalPushMsg payload = getPayload(sourceType, sourceId, targetType, mid,
                pushId);
        if (!rocketMQProducer
                .syncSend(payload,
                        MQTopic.CRITICAL_PUSH, "", "")) {
            throw new CriticalPushException(pushId, "MQ消息发送失败:" + JsonUtil.toJson(payload));
        }
    }

    private Long generatePushId(SourceType sourceType, long sourceId, TargetType targetType) {
        DataPush record = new DataPush();
        record.setSourceType(sourceType.getValue());
        record.setSourceId(sourceId);
        record.setTargetType(targetType.getValue());
        record.setFailCount(0);
        record.setState(PushState.INIT.getValue());
        dataPushService.save(record);
        return record.getId();
    }

    @NonNull
    private CriticalPushMsg getPayload(SourceType sourceType, long sourceId,
            TargetType targetType, MethodInvocationDescriptor mid, Long pushId) {
        return new CriticalPushMsg(
                sourceType,
                sourceId,
                targetType,
                mid,
                pushId
        );
    }
}
