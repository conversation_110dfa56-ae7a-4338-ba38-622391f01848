package com.daddylab.supplier.item.controller.purchaseTable.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @ClassName PurchaseTableCmd.java
 * @description 采购权限新增实体
 * @createTime 2021年11月16日 21:20:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("采购权限新增实体")
public class PurchaseTableCmd extends Command {

    private static final long serialVersionUID = -1676130279878303697L;


    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 0:允许编辑 1:不允许编辑
     */
    @ApiModelProperty(value = "0:允许编辑 1:不允许编辑")
    private Integer isEdit;

    /**
     * 0:没有处理活动商品 1:处理过活动商品
     */
    @ApiModelProperty(value = "0:没有处理活动商品 1:处理过活动商品")
    private Integer isDeal;
}
