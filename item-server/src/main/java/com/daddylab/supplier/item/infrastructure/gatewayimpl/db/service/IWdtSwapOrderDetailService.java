package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSwapOrderDetail;

/**
 * <p>
 * 旺店通换出单明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
public interface IWdtSwapOrderDetailService extends IDaddyService<WdtSwapOrderDetail> {

}
