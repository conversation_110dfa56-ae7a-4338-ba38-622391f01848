package com.daddylab.supplier.item.infrastructure.config;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.env.PropertySourceLoader;
import org.springframework.cloud.bootstrap.config.PropertySourceLocator;
import org.springframework.core.Ordered;
import org.springframework.core.env.CompositePropertySource;
import org.springframework.core.env.Environment;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.SpringFactoriesLoader;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/21
 */
@Component
@Slf4j
public class LocalPropertySourceLocator implements PropertySourceLocator, Ordered {
    private final List<PropertySourceLoader> propertySourceLoaders;
    private final ResourceLoader resourceLoader;

    public LocalPropertySourceLocator(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
        this.propertySourceLoaders =
                SpringFactoriesLoader.loadFactories(
                        PropertySourceLoader.class, getClass().getClassLoader());
    }

    @Override
    @SneakyThrows
    public PropertySource<?> locate(Environment environment) {
        final Resource resource = resourceLoader.getResource("classpath:/application-local.yaml");
        if (resource.exists()) {
            for (PropertySourceLoader propertySourceLoader : propertySourceLoaders) {
                if (canLoadFileExtension(propertySourceLoader, resource.getFilename())) {
                    String name = String.format("localConfig: [%s]", resource.getDescription());
                    final List<PropertySource<?>> propertySources =
                            propertySourceLoader.load(name, resource);
                    CompositePropertySource composite = new CompositePropertySource("LocalConfig");
                    composite.getPropertySources().addAll(propertySources);
                    log.info("local config loaded: {}", composite);
                    return composite;
                }
            }
        }
        return null;
    }

    private boolean canLoadFileExtension(PropertySourceLoader loader, String name) {
        return Arrays.stream(loader.getFileExtensions())
                .anyMatch((fileExtension) -> StringUtils.endsWithIgnoreCase(name, fileExtension));
    }

    @Override
    public int getOrder() {
        return -1;
    }
}
