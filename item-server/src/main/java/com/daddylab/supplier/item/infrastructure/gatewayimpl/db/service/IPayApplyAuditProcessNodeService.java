package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PayApplyAuditProcessNode;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 申请付款单审核流节点审核详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
public interface IPayApplyAuditProcessNodeService extends IDaddyService<PayApplyAuditProcessNode> {

}
