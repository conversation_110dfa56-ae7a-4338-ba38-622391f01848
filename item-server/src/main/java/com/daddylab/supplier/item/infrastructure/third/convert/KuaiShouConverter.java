package com.daddylab.supplier.item.infrastructure.third.convert;

import com.daddylab.supplier.item.infrastructure.third.kuaishou.domain.query.KsItemPageQuery;
import com.kuaishou.merchant.open.api.request.item.OpenItemListGetRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @class KuaiShouConverter.java
 * @description 描述类的作用
 * @date 2024-02-29 10:46
 */
@Mapper
public interface KuaiShouConverter {

    KuaiShouConverter INSTANCE = Mappers.getMapper(KuaiShouConverter.class);


    @Mappings(
            @Mapping(source = "itemId", target = "kwaiItemId")
    )
    OpenItemListGetRequest itemPageQueryToOpenItemListGetReques(KsItemPageQuery itemPageQuery);
}
