package com.daddylab.supplier.item.application.openwdt;

import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig.Config;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtGatewayImpl;
import com.daddylab.supplier.item.infrastructure.qimen.wdt.QimenApiFactory;
import java.util.Collections;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import lombok.NonNull;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/7/8
 */
@Service
public class OpenWdtServiceImpl implements OpenWdtService {

    final ConcurrentMap<Config, WdtGateway> wdtGatewayStore = new ConcurrentHashMap<>();
    final QimenApiFactory qimenApiFactory;

    public OpenWdtServiceImpl(
            QimenApiFactory qimenApiFactory) {
        this.qimenApiFactory = qimenApiFactory;
    }

    @Override
    public <T> T getAPI(Class<T> apiClass, Config config, boolean qimen, boolean enableLog) {
        final WdtGateway wdtGateway = getWdtGateway(
                config);
        return qimen ? wdtGateway.getQimenAPI(apiClass, 0, enableLog)
                : wdtGateway.getAPI(apiClass, 0, enableLog);
    }

    @NonNull
    private WdtGateway getWdtGateway(Config config) {
        return wdtGatewayStore.computeIfAbsent(config, this::createWdtGateway);
    }

    @NonNull
    private WdtGateway createWdtGateway(Config config) {
        final WdtConfig wdtConfig = new WdtConfig();
        wdtConfig.setConfigs(Collections.singletonList(config));
        return new WdtGatewayImpl(wdtConfig, qimenApiFactory);
    }
}
