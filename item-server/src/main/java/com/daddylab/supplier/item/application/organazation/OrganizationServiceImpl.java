package com.daddylab.supplier.item.application.organazation;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Organization;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrganizationService;
import com.daddylab.supplier.item.types.DropdownItem;
import com.daddylab.supplier.item.types.DropdownQuery;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @since 2022/4/1
 */
@Service("organizationServiceImplTwo")
@RequiredArgsConstructor
public class OrganizationServiceImpl implements OrganizationService {
    final private IOrganizationService organizationService;

    @Override
    public PageResponse<DropdownItem<Long>> orgDropdownQuery(DropdownQuery query) {
        final IPage<Organization> page = query.getPage();
        organizationService.page(
                page,
                organizationService.lambdaQuery()
                        .like(StrUtil.isNotBlank(query.getName()), Organization::getName, query.getName())
                        .select(Organization::getName, Organization::getId)
        );
        return ResponseFactory.ofPage(page, this::toDropdownItem);
    }

    @Override
    public SingleResponse<Organization> getById(Long id) {
        Assert.notNull(id, "id不得为空");
        return SingleResponse.of(organizationService.getById(id));
    }



    @NonNull
    private DropdownItem<Long> toDropdownItem(Organization it) {
        return new DropdownItem<>(it.getId(), it.getName());
    }
}
