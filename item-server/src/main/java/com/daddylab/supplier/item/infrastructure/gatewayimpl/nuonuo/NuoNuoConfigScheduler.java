/*
package com.daddylab.supplier.item.infrastructure.gatewayimpl.nuonuo;

import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import nuonuo.open.sdk.NNOpenSDK;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

*/
/**
 * <AUTHOR> up
 * @date 2024年07月26日 2:55 PM
 *//*

@Component
@Slf4j
public class NuoNuoConfigScheduler {

    @Resource
    NuoNuoConfig nuoNuoConfig;

    private static final String NUO_NUO_TOKEN_KEY = "nuo_nuo_token";

    @Scheduled(fixedRate = 23 * 60 * 60 * 1000)
    public void run() {
        try {
            final String defaultToken = nuoNuoConfig.getDefaultToken();
            if (StrUtil.isBlank(defaultToken)) {
                String token = RedisUtil.get(NUO_NUO_TOKEN_KEY);
                if (StrUtil.isBlank(token)) {
                    String json = NNOpenSDK.getIntance().getMerchantToken(nuoNuoConfig.getAppKey(), nuoNuoConfig.getAppSecret());
                    TokenAccessRes resDto = JsonUtil.parse(json, TokenAccessRes.class);
                    assert resDto != null;
                    token = resDto.getAccessToken();
                    Assert.hasText(token, "诺税通访问令牌返回不得为空");
                    RedisUtil.set(NUO_NUO_TOKEN_KEY, token, 23, TimeUnit.HOURS);
                }
            } else {
                RedisUtil.set(NUO_NUO_TOKEN_KEY, defaultToken);
            }
        } catch (Exception e) {
            log.error("诺税通访问令牌处理异常", e);
            Alert.text(MessageRobotCode.GLOBAL, "诺税通访问令牌处理异常");
        }
    }


}*/
