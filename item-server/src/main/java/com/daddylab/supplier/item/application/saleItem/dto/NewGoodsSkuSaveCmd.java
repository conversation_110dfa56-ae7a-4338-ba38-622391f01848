package com.daddylab.supplier.item.application.saleItem.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2022年08月05日 2:20 PM
 */
@Data
@ApiModel("更新sku请求参数封装")
public class NewGoodsSkuSaveCmd {

    private Long newGoodsDbId;

    private Long itemId;

    @ApiModelProperty(value = "sku编码")
    private String skuCode;

    @ApiModelProperty(value = "颜色/规格")
    private String specs;

    @ApiModelProperty(value = "产品划线价")
    private BigDecimal linePrice;

    @ApiModelProperty(value = "产品日销价")
    private BigDecimal dailyPrice;

    @ApiModelProperty(value = "日常活动机制")
    private String dailyActivities;

    @ApiModelProperty(value = "新品活动价")
    private BigDecimal activePrice;

    @ApiModelProperty(value = "新品活动开始周期")
    private Long activePeriodStart;

    @ApiModelProperty(value = "新品活动结束周期")
    private Long activePeriodEnd;

    @ApiModelProperty(value = "新品活动机制")
    private String activeContent;

    @ApiModelProperty(value = "新品直播机制")
    private String liveActive;

    @ApiModelProperty(value = "是否参与满减 0:是 1:否")
    private Integer isReduce;

    @ApiModelProperty(value = "是否叠加券")
    private Integer isCoupon;

    @ApiModelProperty(value = "渠道活动最低价")
    private BigDecimal channelLowest;

    @ApiModelProperty(value = "新品活动周期是否长期有效")
    private Boolean isLongTerm;

    @ApiModelProperty(name = "运营意见反馈")
    private String runFeedback;


    @ApiModelProperty("A级活动售价")
    private String aLevelActivityPrice;

    @ApiModelProperty("A级活动赠品")
    private String aLevelActivityGift;

    @ApiModelProperty("A级活动直播价")
    private String aLevelActivityLivePrice;

    @ApiModelProperty("A级活动直播赠品")
    private String aLevelActivityLiveGift;

    @ApiModelProperty("S级大促售价")
    private String sLevelPromotePrice;

    @ApiModelProperty("S级大促机制")
    private String sLevelPromoteRule;

    @ApiModelProperty("S级大促直播价")
    private String sLevelPromoteLivePrice;

    @ApiModelProperty("S级大促直播机制")
    private String sLevelPromoteLiveRule;

    public NewGoodsCmd buildCmd(){
        NewGoodsCmd cmd = new NewGoodsCmd();
        cmd.setId(this.newGoodsDbId);
        cmd.setActivePeriodStart(activePeriodStart);
        cmd.setActivePeriodEnd(activePeriodEnd);
        cmd.setIsLongTerm(isLongTerm);
        cmd.setLinePrice(linePrice);
        cmd.setDailyPrice(dailyPrice);
        cmd.setActivePrice(activePrice);
        cmd.setActiveContent(activeContent);
        cmd.setChannelLowest(channelLowest);
        cmd.setLiveActive(liveActive);
        cmd.setIsReduce(isReduce);
        cmd.setIsCoupon(isCoupon);
        cmd.setRunFeedback(runFeedback);
        return cmd;

    }
}
