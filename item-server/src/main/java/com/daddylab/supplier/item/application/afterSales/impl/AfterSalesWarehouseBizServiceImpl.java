package com.daddylab.supplier.item.application.afterSales.impl;

import static com.daddylab.supplier.item.infrastructure.utils.MPUtil.limit;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.daddylab.supplier.item.application.afterSales.AfterSalesWarehouseBizService;
import com.daddylab.supplier.item.application.afterSales.WarehouseAfterSalesAddressVO;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.controller.provider.dto.PartnerProviderPageQuery;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.region.gateway.RegionGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WarehouseAfterSalesAddress;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IProviderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWarehouseAfterSalesAddressService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWarehouseService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.daddylab.supplier.item.types.warehouse.EditWarehouseAfterSalesAddressCmd;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.javers.core.metamodel.annotation.Id;
import org.javers.core.metamodel.annotation.PropertyName;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/10/21
 */
@Service
@Slf4j
@AllArgsConstructor
public class AfterSalesWarehouseBizServiceImpl implements AfterSalesWarehouseBizService {

  private final IWarehouseAfterSalesAddressService warehouseAfterSalesAddressService;
  private final IWarehouseService warehouseService;
  private final IProviderService providerService;
  private final ProviderGateway providerGateway;
  private final RegionGateway regionGateway;
  private final OperateLogDomainService operateLogDomainService;

  @Data
  public static class ImportRow {

    /** 统一社会信用代码 */
    @ExcelProperty("P系统信用代码")
    private String unifySocialCreditCode;

    /** 仓库编号 */
    @ExcelProperty("仓库编号")
    private String warehouseNo;

    /** 退回地址 */
    @ExcelProperty("退货地址")
    private String fullAddress;

    /** 退货联系人 */
    @ExcelProperty("售后联系人")
    private String contacts;

    /** 退货联系电话 */
    @ExcelProperty("售后联系方式")
    private String tel;
  }

  private boolean isInvalidImportValue(String value) {
    return value == null || ExcelUtil.isNA(value) || StringUtil.isBlank(value) || "0".equals(value);
  }

  @Override
  public Response importExcel(InputStream inputStream) {
    final List<ImportRow> rows =
        EasyExcel.read(inputStream).head(ImportRow.class).sheet(0).doReadSync();
    if (CollectionUtil.isEmpty(rows)) {
      return Response.buildFailure(ErrorCode.BUSINESS_OPERATE_ERROR.getCode(), "导入文件数据为空");
    }
    int i = 0;
    for (ImportRow row : rows) {
      i++;
      if (isInvalidImportValue(row.getWarehouseNo())
          || isInvalidImportValue(row.getUnifySocialCreditCode())) {
        log.warn("第{}/{}行:仓库编号和社会信用代码不完整", i, rows.size());
        continue;
      }
      if (StringUtil.isBlank(row.getFullAddress())
          || StringUtil.isBlank(row.getTel())
          || StringUtil.isBlank(row.getContacts())) {
        log.warn("第{}/{}行:联系人、联系电话、地址不完整", i, rows.size());
        continue;
      }
      final Optional<Provider> providerOptional =
          providerService
              .lambdaQuery()
              .eq(Provider::getUnifySocialCreditCodes, row.getUnifySocialCreditCode())
              .last(limit(1))
              .oneOpt();
      if (!providerOptional.isPresent()) {
        log.error("第{}/{}行:未找到供应商 社会信用代码={}", i, rows.size(), row.getUnifySocialCreditCode());
        continue;
      }
      final Provider provider = providerOptional.get();

      // 如果供应商的P系统供应商ID为空尝试补全
      if (NumberUtil.isZeroOrNull(provider.getPartnerProviderId())) {
        try {
          final PartnerProviderPageQuery partnerPageQuery = new PartnerProviderPageQuery();
          final String name = provider.getName();
          partnerPageQuery.setName(getProviderNameKeyword(provider));
          final List<PartnerProviderResp> partnerProviders =
              providerGateway.partnerQuery(partnerPageQuery);
          final Optional<PartnerProviderResp> partnerProviderOptional =
              Optional.ofNullable(partnerProviders)
                  .filter(CollectionUtil::isNotEmpty)
                  .flatMap(
                      pps ->
                          pps.stream()
                              .filter(
                                  v ->
                                      StringUtil.equals(
                                          v.getOrganizationNo(),
                                          provider.getUnifySocialCreditCodes()))
                              .findFirst());
          if (!partnerProviderOptional.isPresent()) {
            log.error(
                "第{}/{}行:未能查询到匹配的合作伙伴系统供应商记录 供应商名称={} 社会信用代码={}",
                i,
                rows.size(),
                name,
                row.getUnifySocialCreditCode());
            continue;
          }
        } catch (Exception e) {
          log.error(
              "第{}/{}行:查询到匹配的合作伙伴系统供应商记录异常 供应商名称={} 社会信用代码={} 错误消息={}",
              i,
              rows.size(),
              provider.getName(),
              row.getUnifySocialCreditCode(),
              e.getMessage(),
              e);
          continue;
        }
      }

      final Optional<Warehouse> warehouseOptional =
          warehouseService.lambdaQuery().eq(Warehouse::getNo, row.getWarehouseNo()).oneOpt();
      if (!warehouseOptional.isPresent()) {
        log.error("第{}/{}行:未找到仓库 仓库编号={}", i, rows.size(), row.getWarehouseNo());
        continue;
      }
      final Warehouse warehouse = warehouseOptional.get();

      final WarehouseAfterSalesAddress warehouseAfterSalesAddress =
          warehouseAfterSalesAddressService
              .lambdaQuery()
              .eq(WarehouseAfterSalesAddress::getWarehouseNo, warehouse.getNo())
              .oneOpt()
              .orElseGet(
                  () -> {
                    final WarehouseAfterSalesAddress po = new WarehouseAfterSalesAddress();
                    po.setWarehouseNo(warehouse.getNo());
                    return po;
                  });
      warehouseAfterSalesAddress.setWarehouseName(warehouse.getName());
      if (Objects.isNull(warehouseAfterSalesAddress.getId())
          || !StringUtil.equals(
              provider.getProviderNo(), warehouseAfterSalesAddress.getProviderNo())
          || !Objects.equals(
              provider.getPartnerProviderId(), warehouseAfterSalesAddress.getPartnerProviderId())) {
        warehouseAfterSalesAddress.setProviderNo(provider.getProviderNo());
        warehouseAfterSalesAddress.setPartnerProviderId(provider.getPartnerProviderId());
      }
      warehouseAfterSalesAddress.setFullAddress(row.getFullAddress());
      warehouseAfterSalesAddress.setContacts(row.getContacts());
      warehouseAfterSalesAddress.setTel(row.getTel());
      warehouseAfterSalesAddressService.saveOrUpdate(warehouseAfterSalesAddress);
      log.info("第{}/{}行:导入成功 data={}", i, rows.size(), warehouseAfterSalesAddress);
    }
    return Response.buildSuccess();
  }

  private String getProviderNameKeyword(Provider provider) {
    final String name = provider.getName();
    if (StringUtil.isBlank(name)) {
      return name;
    }
    if (name.length() < 4) {
      return name;
    }
    return name.substring(2, 4);
  }

  @Override
  public MultiResponse<WarehouseAfterSalesAddressVO> getPartnerProviderReturnWarehouseAddressVOs(
      Long partnerProviderId) {
    final List<WarehouseAfterSalesAddressVO> warehouseAfterSalesAddressVOS =
        warehouseAfterSalesAddressService
            .lambdaQuery()
            .eq(WarehouseAfterSalesAddress::getPartnerProviderId, partnerProviderId)
            .list()
            .stream()
            .map(this::getWarehouseAfterSalesAddressVO)
            .collect(Collectors.toList());
    return MultiResponse.of(warehouseAfterSalesAddressVOS);
  }

  @Override
  public Optional<WarehouseAfterSalesAddressVO> getWarehouseAfterSalesAddressVO(
      String warehouseNo) {
    return warehouseAfterSalesAddressService
        .lambdaQuery()
        .eq(WarehouseAfterSalesAddress::getWarehouseNo, warehouseNo)
        .last(MPUtil.limit(1))
        .oneOpt()
        .map(this::getWarehouseAfterSalesAddressVO);
  }

  @Override
  public List<WarehouseAfterSalesAddressVO> getWarehouseAfterSalesAddressList(String warehouseNo) {
    return getWarehouseAfterSalesAddressVO(Collections.singletonList(warehouseNo));
  }

  @Override
  public List<WarehouseAfterSalesAddressVO> getWarehouseAfterSalesAddressVO(
      Collection<String> warehouseNos) {
    if (CollectionUtils.isEmpty(warehouseNos)) {
      return Collections.emptyList();
    }
    return warehouseAfterSalesAddressService
        .lambdaQuery()
        .in(WarehouseAfterSalesAddress::getWarehouseNo, warehouseNos)
        .ne(WarehouseAfterSalesAddress::getFullAddress, "")
        .isNotNull(WarehouseAfterSalesAddress::getFullAddress)
        .list()
        .stream()
        .filter(v -> StringUtil.isNotBlank(v.getFullAddress()))
        .map(this::getWarehouseAfterSalesAddressVO)
        .collect(Collectors.toList());
  }

  @Override
  public boolean isPartnerProviderWarehouse(Long partnerProviderId, String warehouseNo) {
    return warehouseAfterSalesAddressService
            .lambdaQuery()
            .eq(WarehouseAfterSalesAddress::getPartnerProviderId, partnerProviderId)
            .eq(WarehouseAfterSalesAddress::getWarehouseNo, warehouseNo)
            .count()
        > 0;
  }

  @Override
  public Response editWarehouseAfterSalesInfo(EditWarehouseAfterSalesAddressCmd cmd) {
    Optional<Warehouse> warehouseOpt = warehouseService.queryWarehouseByNo(cmd.getWarehouseNo());
    final String warehouseName =
        warehouseOpt
            .map(Warehouse::getName)
            .orElseThrow(
                () -> ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "仓库不存在"));
    final WarehouseAfterSalesAddress warehouseAfterSalesAddress =
        warehouseAfterSalesAddressService
            .lambdaQuery()
            .eq(WarehouseAfterSalesAddress::getWarehouseNo, cmd.getWarehouseNo())
            .oneOpt()
            .orElseGet(WarehouseAfterSalesAddress::new);
    warehouseAfterSalesAddress.setWarehouseNo(cmd.getWarehouseNo());
    warehouseAfterSalesAddress.setFullAddress(cmd.getFullAddress());
    warehouseAfterSalesAddress.setContacts(
        StringUtil.isBlank(cmd.getContacts()) ? "" : cmd.getContacts());
    warehouseAfterSalesAddress.setTel(StringUtil.isBlank(cmd.getTel()) ? "" : cmd.getTel());
    warehouseAfterSalesAddress.setWarehouseName(warehouseName);
    warehouseAfterSalesAddressService.saveOrUpdate(warehouseAfterSalesAddress);
    return Response.buildSuccess();
  }

  @NonNull
  private WarehouseAfterSalesAddressVO getWarehouseAfterSalesAddressVO(
      WarehouseAfterSalesAddress v) {
    final WarehouseAfterSalesAddressVO warehouseAfterSalesAddressVO =
        new WarehouseAfterSalesAddressVO();
    warehouseAfterSalesAddressVO.setId(v.getId());
    warehouseAfterSalesAddressVO.setWarehouseNo(v.getWarehouseNo());
    warehouseAfterSalesAddressVO.setFullAddress(v.getFullAddress());
    warehouseAfterSalesAddressVO.setContacts(v.getContacts());
    warehouseAfterSalesAddressVO.setTel(v.getTel());
    warehouseAfterSalesAddressVO.setPartnerProviderId(v.getPartnerProviderId());
    warehouseAfterSalesAddressVO.setWarehouseName(v.getWarehouseName());
    warehouseAfterSalesAddressVO.setProvinceCode(v.getProvinceCode());
    warehouseAfterSalesAddressVO.setCityCode(v.getCityCode());
    warehouseAfterSalesAddressVO.setAreaCode(v.getAreaCode());
    warehouseAfterSalesAddressVO.setRemark(v.getRemark());
    warehouseAfterSalesAddressVO.setAddressCodeStr(
        regionGateway.getCodeStr(v.getProvinceCode(), v.getCityCode(), v.getAreaCode()));
    return warehouseAfterSalesAddressVO;
  }

  private WarehouseAfterSalesAddress convertVoToEntity(
      String warehouseNo, WarehouseAfterSalesAddressVO vo) {
    WarehouseAfterSalesAddress warehouseAfterSalesAddress = new WarehouseAfterSalesAddress();
    warehouseAfterSalesAddress.setWarehouseNo(warehouseNo);
    warehouseAfterSalesAddress.setFullAddress(vo.getFullAddress());
    warehouseAfterSalesAddress.setContacts(vo.getContacts());
    warehouseAfterSalesAddress.setTel(vo.getTel());
    warehouseAfterSalesAddress.setWarehouseName(vo.getWarehouseName());
    warehouseAfterSalesAddress.setRemark(vo.getRemark());
    warehouseAfterSalesAddress.setProvinceCode(vo.getProvinceCode());
    warehouseAfterSalesAddress.setCityCode(vo.getCityCode());
    warehouseAfterSalesAddress.setAreaCode(vo.getAreaCode());
    return warehouseAfterSalesAddress;
  }

  @Data
  private static class WarehouseCompareBo {
    @Id private Long id;

    @PropertyName("售后客服")
    private String customerName;

    @PropertyName("售后联系人")
    private String contactName;

    @PropertyName("联系电话")
    private String tel;

    @PropertyName("售后地址")
    private String address;

    @PropertyName("备注")
    private String remark;
  }

  @Override
  public Response editWarehouseAfterSalesInfoList(
      Warehouse warehouse, List<Long> deleteIds, List<WarehouseAfterSalesAddressVO> cmdList) {
    String warehouseNo = warehouse.getNo();

    // 删除
    if (CollectionUtils.isNotEmpty(deleteIds)) {
      final List<WarehouseAfterSalesAddress> list =
          warehouseAfterSalesAddressService
              .lambdaQuery()
              .in(WarehouseAfterSalesAddress::getId, deleteIds)
              .list();
      final String contacts =
          list.stream()
              .map(WarehouseAfterSalesAddress::getContacts)
              .collect(Collectors.joining("，"));
      operateLogDomainService.addOperatorLog(
          UserContext.getUserId(),
          OperateLogTarget.WAREHOUSE,
          warehouse.getId(),
          "删除售后信息，售后联系人：" + contacts,
          null);
      warehouseAfterSalesAddressService.removeByIds(deleteIds);
    }

    // 新增
    List<WarehouseAfterSalesAddressVO> addList =
        cmdList.stream().filter(val -> Objects.isNull(val.getId())).collect(Collectors.toList());
    List<WarehouseAfterSalesAddress> addEntityList =
        addList.stream()
            .map(val -> convertVoToEntity(warehouseNo, val))
            .collect(Collectors.toList());
    if (CollectionUtils.isNotEmpty(addEntityList)) {
      warehouseAfterSalesAddressService.saveBatch(addEntityList);
      final String contacts =
          addEntityList.stream()
              .map(WarehouseAfterSalesAddress::getContacts)
              .collect(Collectors.joining("，"));
      operateLogDomainService.addOperatorLog(
          UserContext.getUserId(),
          OperateLogTarget.WAREHOUSE,
          warehouse.getId(),
          "新增售后信息，售后联系人：" + contacts,
          null);
    }

    List<WarehouseAfterSalesAddressVO> updateList =
        cmdList.stream().filter(val -> Objects.nonNull(val.getId())).collect(Collectors.toList());
    if (!updateList.isEmpty()) {
      final List<Long> oldIds =
              updateList.stream().map(WarehouseAfterSalesAddressVO::getId).collect(Collectors.toList());
      final List<WarehouseAfterSalesAddress> oldEntityList =
              warehouseAfterSalesAddressService
                      .lambdaQuery()
                      .in(WarehouseAfterSalesAddress::getId, oldIds)
                      .list();
      List<WarehouseAfterSalesAddress> updateEntity =
              updateList.stream()
                      .map(
                              val -> {
                                WarehouseAfterSalesAddress warehouseAfterSalesAddress =
                                        convertVoToEntity(warehouseNo, val);
                                warehouseAfterSalesAddress.setId(val.getId());
                                return warehouseAfterSalesAddress;
                              })
                      .collect(Collectors.toList());
      if (CollectionUtils.isNotEmpty(updateEntity)) {
        warehouseAfterSalesAddressService.updateBatchById(updateEntity);

        final DiffUtil.ObjListDiff objListDiff =
                DiffUtil.diffObjList(oldEntityList, updateEntity, WarehouseAfterSalesAddress.class);
        final Map<Object, Set<DiffUtil.ChangePropertyObj>> changeMap = objListDiff.getChangeMap();
        List<String> logs = new LinkedList<>();
        changeMap.forEach(
                (k, v) -> {
                  for (DiffUtil.ChangePropertyObj changePropertyObj : v) {
                    final String format =
                            StringUtil.format(
                                    "{}从{}改为{}",
                                    changePropertyObj.getProperty(),
                                    changePropertyObj.getOldVal(),
                                    changePropertyObj.getNewVal());
                    logs.add(format);
                  }
                });
        if (CollectionUtils.isNotEmpty(logs)) {
          operateLogDomainService.addOperatorLog(
                  UserContext.getUserId(),
                  OperateLogTarget.WAREHOUSE,
                  warehouse.getId(),
                  "编辑售后信息，" + String.join("；", logs),
                  null);
        }
      }
    }

    //        List<Long> reqIdList =
    // cmdList.stream().map(WarehouseAfterSalesAddressVO::getId).collect(Collectors.toList());
    //        List<WarehouseAfterSalesAddress> oldList =
    // warehouseAfterSalesAddressService.lambdaQuery().eq(WarehouseAfterSalesAddress::getWarehouseNo, warehouse).list();
    //        List<Long> oldIdList =
    // oldList.stream().map(WarehouseAfterSalesAddress::getId).collect(Collectors.toList());
    //        Collection<Long> intersection = CollectionUtils.intersection(oldIdList, reqIdList);
    //
    //        oldIdList.removeAll(intersection);
    //        if (CollectionUtils.isNotEmpty(oldIdList)) {
    //
    // warehouseAfterSalesAddressService.lambdaUpdate().in(WarehouseAfterSalesAddress::getId,
    // oldIdList).remove();
    //        }
    //        reqIdList.removeAll(intersection);
    //        if (CollectionUtils.isNotEmpty(reqIdList)) {
    //            List<WarehouseAfterSalesAddress> saveList = cmdList.stream().filter(val ->
    // reqIdList.contains(val.getId()))
    //                    .map(this::convertVoToEntity).collect(Collectors.toList());
    //            warehouseAfterSalesAddressService.saveBatch(saveList);
    //        }
    //        if (CollectionUtils.isNotEmpty(intersection)) {
    //            Map<Long, WarehouseAfterSalesAddressVO> reqMap =
    // cmdList.stream().collect(Collectors.toMap(WarehouseAfterSalesAddressVO::getId,
    // Function.identity()));
    //            Map<Long, WarehouseAfterSalesAddress> oldMap =
    // oldList.stream().collect(Collectors.toMap(WarehouseAfterSalesAddress::getId,
    // Function.identity()));
    //            List<WarehouseAfterSalesAddress> updateList = intersection.stream().map(val -> {
    //                WarehouseAfterSalesAddress oldOne = oldMap.get(val);
    //                WarehouseAfterSalesAddressVO reqOne = reqMap.get(val);
    //                WarehouseAfterSalesAddress newOne = convertVoToEntity(reqOne);
    //                newOne.setId(oldOne.getId());
    //                return newOne;
    //            }).collect(Collectors.toList());
    //            warehouseAfterSalesAddressService.updateBatchById(updateList);
    //        }

    return Response.buildSuccess();
  }

  @Override
  public Map<String, List<String>> queryWarehouseAfterSaleInfo(List<String> warehouseNos) {
    if (CollectionUtils.isEmpty(warehouseNos)) {
      return new HashMap<>();
    }

    List<WarehouseAfterSalesAddress> list =
        warehouseAfterSalesAddressService
            .lambdaQuery()
            .in(WarehouseAfterSalesAddress::getWarehouseNo, warehouseNos)
            .list();
    if (CollectionUtils.isEmpty(list)) {
      return new HashMap<>();
    }
    return list.stream()
        .collect(
            Collectors.groupingBy(
                WarehouseAfterSalesAddress::getWarehouseNo,
                Collectors.mapping(
                    warehouseAfterSalesAddress -> {
                      String provinceCode = warehouseAfterSalesAddress.getProvinceCode();
                      String cityCode = warehouseAfterSalesAddress.getCityCode();
                      String areaCode = warehouseAfterSalesAddress.getAreaCode();
                      String codeStr = regionGateway.getCodeStr(provinceCode, cityCode, areaCode);
                      return StrUtil.format(
                          "联系人：{},联系电话：{},售后地址：{}",
                          warehouseAfterSalesAddress.getContacts(),
                          warehouseAfterSalesAddress.getTel(),
                          codeStr + " " + warehouseAfterSalesAddress.getFullAddress());
                    },
                    Collectors.toList())));
  }
}
