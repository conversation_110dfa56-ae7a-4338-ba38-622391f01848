package com.daddylab.supplier.item.domain.exportTask.dto.item;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/26 11:12 下午
 * @description
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
public class ExportSkuDto extends ExportSheet {

  @ExcelIgnore private Long id;

  @ExcelIgnore private Long itemId;

  @ExcelIgnore private Long createdUid;

  @ExcelProperty("商品sku")
  private String sku;

  @ExcelProperty("商品指定编码")
  private String designatedCode;

  @ExcelProperty("颜色/规格")
  private String specifications;

  @ExcelProperty("商品名称")
  private String name;

  @ExcelProperty("商品品类")
  private String category;

  @ExcelProperty("商品品牌")
  private String brand;

  @ExcelProperty("商品编码")
  private String code;

  @ExcelProperty("成本价格")
  private String costPrice;

  @ExcelProperty("销售价格")
  private String salePrice;

  @ExcelProperty("供应商")
  private String provider;

  @ExcelProperty("采购员")
  private String buyer;

  @ExcelProperty("发货渠道")
  private String delivery;

  @ExcelIgnore private Integer status;

  @ExcelProperty("商品状态")
  private String statusStr;

  @ExcelProperty("商品款号")
  String partnerProviderItemSn;

  @ExcelProperty("合作方/业务类型")
  private String corpBizType;

  @ExcelProperty("合同销售价")
  private String contractSalePrice;

  @ExcelProperty("佣金比例")
  String platformCommission;

  @ExcelProperty("采购税率")
  private String purchaseTaxRate;

  @ExcelProperty("销售税率")
  private String taxRate;

  @ExcelProperty("货品类型")
  private String goodsTypeStr;

  @ExcelIgnore private Integer goodsType;

  @ExcelProperty("商品下架时间")
  private String downFrameTimeStr;

  @ExcelIgnore private Long downFrameTime;

  @ExcelProperty("商品下架原因")
  private String downFrameReason;


}
