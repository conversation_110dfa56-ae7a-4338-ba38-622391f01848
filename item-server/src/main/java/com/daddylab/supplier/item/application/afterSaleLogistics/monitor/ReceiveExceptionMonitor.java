package com.daddylab.supplier.item.application.afterSaleLogistics.monitor;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsException;
import com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsRootException;
import com.daddylab.supplier.item.application.afterSaleLogistics.dto.LogisticExceptionRes;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsTrace;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IKdyCallbackService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 24/48小时； 从发货时间到当前时间，超24/48小时都没第一条物流轨迹
 *
 * <AUTHOR> up
 * @date 2024年05月21日 9:58 AM
 */
@Order(MonitorOrder.ReceiveExceptionMonitor)
@Slf4j
@Component
@AllArgsConstructor
public class ReceiveExceptionMonitor implements OrderLogisticsExceptionMonitor {

  private final IKdyCallbackService iKdyCallbackService;

  @Override
  public LogisticExceptionRes process(ProcessContext context) {
    final OrderLogisticsTrace trace = context.getTrace();
    final Long currentTurnTime = context.getTimestamp();
    LogisticExceptionRes res = new LogisticExceptionRes();

    final boolean noLogisticsTrack = trace.getCallbackId() == 0;
    final boolean isConsigned = NumberUtil.isPositive(trace.getConsignTime());
    boolean isReceive = NumberUtil.isPositive(trace.getPickingUpTime());
    if (context.getLogisticsTraceData() != null
        && CollUtil.isNotEmpty(context.getLogisticsTraceData().getTrackList())) {
      final boolean containKeyword =
          context.getLogisticsTraceData().getTrackList().stream()
              .anyMatch(
                  track ->
                      Stream.of(
                              "揽收", "收件", "取件", "运输", "在途", "配送", "集散", "中心", "转运", "派件", "签收",
                              "派送", "代收", "代签", "收件", "退回", "疑难", "问题", "家门", "菜鸟", "驿站", "门卫",
                              "前台", "超市", "暂放")
                          .anyMatch(track::containKeyword));
      if (containKeyword) {
        isReceive = true;
      }
    }
    if (isConsigned && (noLogisticsTrack || !isReceive)) {
      long aLong =
          DateUtil.calculateDifference(currentTurnTime, trace.getConsignTime(), TimeUnit.HOURS);
      if (aLong >= 48) {
        res.setRootException(LogisticsRootException.RECEIVE_EXCEPTION);
        res.setSubException(LogisticsException.RECEIVE_72H);
        return res;
      }
      if (aLong >= 24) {
        res.setRootException(LogisticsRootException.RECEIVE_EXCEPTION);
        res.setSubException(LogisticsException.RECEIVE_24H);
        return res;
      }
    }

    res.setRootException(LogisticsRootException.NORMAL);
    return res;
  }
}
