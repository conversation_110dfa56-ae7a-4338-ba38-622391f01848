package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AllChannelBillData;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AllChannelBillDataMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAllChannelBillDataService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 全渠道开票数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Service
public class AllChannelBillDataServiceImpl extends DaddyServiceImpl<AllChannelBillDataMapper, AllChannelBillData> implements IAllChannelBillDataService {

}
