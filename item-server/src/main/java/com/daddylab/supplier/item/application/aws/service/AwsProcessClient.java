package com.daddylab.supplier.item.application.aws.service;

import com.actionsoft.bpms.api.ApiException;
import com.actionsoft.sdk.service.model.HistoryTaskInstance;
import com.actionsoft.sdk.service.model.TaskInstance;
import com.actionsoft.sdk.service.model.TaskQueryModel;
import com.actionsoft.sdk.service.response.process.ProcessInstResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/8/24
 */
public interface AwsProcessClient {

    String processCreate(String processDefId, String title, String uid) throws ApiException;

    void processStart(String processInstId) throws ApiException;

    void boCreate(String boName, String processInstId, Map<String, Object> data,
            String uid);

    void boFieldUpdate(String boName, String processInstId, String fieldName, Object value);

    List<Map<String, Object>> boQuery(String boName, Object querys, String selectClause,
            String orderBy,
            Integer firstRow, Integer rowCount);

    void boRemoveBindId(String boName, String processInstId);

    ProcessInstResponse processInstGet(String processInstId) throws ApiException;

    void processRestart(String processInstId) throws ApiException;

    void processReactive(String processInstId, String targetActivityId,
            Boolean isClearHistory, String uid,
            String targetUID, String reactivateReason) throws ApiException;

    Boolean processEndCheck(String processInstId);

    Boolean processTerminate(String processInstId, String uid) throws ApiException;

    List<TaskInstance> taskQuery(String processInstId) throws ApiException;

    List<TaskInstance> taskQuery(TaskQueryModel model) throws ApiException;

    List<HistoryTaskInstance> historyTaskQuery(String processInstId) throws ApiException;

    /**
     * 为任务提交一个审批留言到历史记录
     *
     * @param taskInstId 任务实例Id
     * @param user 执行人账户，如果该账户与任务的target不同，被记录为代理人操作
     * @param actionName 审核动作名，通常是审核菜单名称，如果不传入，审批记录该部分会显示“-”
     * @param commentMsg 留言内容
     * @param isIgnoreDefaultSetting 是否忽略节点设置的不显示审核菜单选项
     */
    void taskCommentCommit(String taskInstId, String user, String actionName, String commentMsg,
            Boolean isIgnoreDefaultSetting);

    void taskComplete(String taskInstId, String uid);

    boolean taskDelegate(String taskInstId, String uid, String targetUid, String reason)
            throws ApiException;

    String taskParticipantsGet(String uid, String processInstId, String taskInstId, String nextUserTaskDefId);
}
