package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockInDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 其他入库单详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-30
 */
@Repository
public interface OtherStockInDetailMapper extends DaddyBaseMapper<OtherStockInDetail> {

    List<Long> getIdsByItem(@Param("skuCode") String skuCode, @Param("code") String code,
                            @Param("itemId") Long itemId, @Param("brandId") Long brandId,
                            @Param("businessLine") Collection<Integer> businessLine);

}
