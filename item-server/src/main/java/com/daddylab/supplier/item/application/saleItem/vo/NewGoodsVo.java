package com.daddylab.supplier.item.application.saleItem.vo;

import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName NewGoodsVo.java
 * @description
 * @createTime 2022年04月18日 16:34:00
 */
@Data
@ApiModel("新品商品返回实体")
public class NewGoodsVo implements Serializable {
    private static final long serialVersionUID = -1200415708549781152L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "sku编码")
    private String skuCode;

    @ApiModelProperty(value = "sku指定编码")
    private String specialSkuCode;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "商品图片")
    private String image;

    @ApiModelProperty(value = "颜色/规格")
    private String specs;

    @ApiModelProperty(value = "采购成本")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "品类id")
    private Long categoryId;

    @ApiModelProperty(value = "品类")
    private String category;

    @ApiModelProperty(value = "品牌id")
    private Long brandId;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "预计上架日期")
    private Long shelfTime;

    @ApiModelProperty(value = "上新时间")
    @JsonProperty
    public String getLaunchDate() {
        if (shelfTime != null) {
            return DateUtil.formatDate(shelfTime);
        }
        return "";
    }

    @ApiModelProperty(value = "上新计划ID")
    private Long planId;

    @ApiModelProperty(value = "上新计划名称")
    private String planName;

    @ApiModelProperty(value = "产品标准名")
    private String standardName;

    @ApiModelProperty(value = "新品活动开始周期")
    private Long activePeriodStart;

    @ApiModelProperty(value = "新品活动结束周期")
    private Long activePeriodEnd;

    @ApiModelProperty(value = "新品活动周期是否长期有效")
    private Boolean isLongTerm;

    @ApiModelProperty(value = "是否支持7天无理由退换 0:是 1:否")
    private Integer noReason;

    @ApiModelProperty(value = "划线价")
    private BigDecimal linePrice;

    public BigDecimal getLinePrice() {
        return NumberUtil.scale(linePrice);
    }

    @ApiModelProperty(value = "产品日销价")
    private BigDecimal dailyPrice;

    public BigDecimal getDailyPrice() {
        return NumberUtil.scale(dailyPrice);
    }

    @ApiModelProperty(value = "日常活动")
    private String dailyActivities;

    @ApiModelProperty(value = "产品活动价")
    private BigDecimal activePrice;

    public BigDecimal getActivePrice() {
        return NumberUtil.scale(activePrice);
    }

    @ApiModelProperty(value = "新品活动机制")
    private String activeContent;

    @ApiModelProperty(value = "渠道活动最低价")
    private BigDecimal channelLowest;

    public BigDecimal getChannelLowest() {
        return NumberUtil.scale(channelLowest);
    }

    @ApiModelProperty(value = "直播活动机制")
    private String liveActive;

    @ApiModelProperty(value = "是否参与满减 0:是 1:否")
    private Integer isReduce;

    @ApiModelProperty(value = "发货类型 0:仓库发货 1:工厂发货")
    private Integer shipmentType;

    @ApiModelProperty(value = "发货地")
    private String shipmentArea;

    @ApiModelProperty(value = "48小时发货 0:是 1:否")
    private Integer shipmentAging;

    @ApiModelProperty(value = "物流")
    private String logistics;

    @ApiModelProperty(value = "快递模板")
    private String expressTemplate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "商品id")
    private Long itemId;

    private String itemCode;

    @ApiModelProperty(value = "状态 1待选择 2待完善 3待设计 41待法务审核 42待QC审核 5待修改 6待上架 7已上架")
    private Integer status;

    @ApiModelProperty("商品审核状态 0:未进入审核流程/未知 5:待法务审核 10:待QC审核 100:审核完成")
    private Integer auditStatus;

    @ApiModelProperty(value = "状态 1待选择 2待完善 3待设计 41待法务审核 42待QC审核 5待修改 6待上架 7已上架")
    @JsonProperty
    public Integer getStatus() {
        return !ItemLaunchStatus.TO_BE_AUDITED.getValue().equals(status) ? status
                : (ItemAuditStatus.WAIT_QC_AUDIT.getValue().equals(auditStatus) ? 42 : 41);
    }

    @ApiModelProperty(value = "是否叠加券")
    private Integer isCoupon;

    @ApiModelProperty(value = "采购负责人id")
    private Long buyerId;

    @ApiModelProperty(value = "产品负责人id")
    private Long principalId;

    @ApiModelProperty(value = "法务负责人id")
    private Long legalId;

    @ApiModelProperty(value = "法务负责人信息")
    private DadStaffVO legal;

    @ApiModelProperty(value = "QC负责人ids")
    private String qcIds;

    public List<Long> getQcIdList() {
        return StringUtil.splitTrim(qcIds, ",").stream().map(Long::parseLong).collect(Collectors.toList());
    }

    @ApiModelProperty(value = "采购负责人信息")
    private DadStaffVO buyer;

    @ApiModelProperty(value = "产品负责人信息")
    private DadStaffVO principal;

    @ApiModelProperty(value = "产品负责人信息")
    private List<DadStaffVO> qcs = Collections.emptyList();

    @ApiModelProperty(value = "首页文案")
    private String homeCopy;

    @ApiModelProperty("商品类型 1 内部 2 外包")
    private Integer itemType;

    @ApiModelProperty("P系统技术类目 1 日化 2 食品 3 纺织鞋类 4 玩具 5 电器 6轻工百货")
    private Integer partnerSysType;

    @ApiModelProperty(value = "单买到手价")
    private BigDecimal singleBuyPrice;

    @ApiModelProperty("（直播话术状态 0:未进入审核流程/未知 5:待法务审核 10:待QC审核 100:审核完成）")
    private Integer liveVerbalTrickStatus;

    private String liveVerbalTrick;

    @ApiModelProperty("直播话术（新版）")
    private List<LiveVerbalTrickVO> liveVerbalTrickVos;

    @ApiModelProperty("直播话术新版标识")
    private Integer newLiveVerbalTrickFlag;

    @ApiModelProperty("运营意见反馈")
    private String runFeedback;

    private String aLevelActivityPrice;

    private String aLevelActivityGift;

    private String aLevelActivityLivePrice;

    private String aLevelActivityLiveGift;

    private String sLevelPromotePrice;

    private String sLevelPromoteRule;

    private String sLevelPromoteLivePrice;

    private String sLevelPromoteLiveRule;

    private String wechatId;
    private String vipShopId;

    @ApiModelProperty("0供应商 1老爸抽检 2绿色家装 3商家入驻")
    private Integer businessLine;

    @ApiModelProperty(value = "合作模式（多选）", notes = "1供应商 2老爸抽检 3绿色家装 4商家入驻")
    private String businessLines;

    /**
     * 商品SKU额外拓展属性
     */
    @ApiModelProperty("商品SKU额外拓展属性")
    private LinkedHashMap<String, Object> props;

    @ApiModelProperty("商品拓展属性（键名）")
    private List<String> propKeys;

    private String miniRedBookId;

    private String douDianId;

    @ApiModelProperty("是否黑名单 0否 1是")
    private Integer isBlacklist;

    @ApiModelProperty("是否老爸抽检 0否 1是")
    private Integer isDadCheck;

    @ApiModelProperty("培训资料状态 0 待提交 1 已提交 2 待法务审核 3 待QC审核 4 待修改 5 已完成")
    private Integer materialsStatus;

    @ApiModelProperty("P系统款号")
    private String partnerProviderItemSn;

    @ApiModelProperty(value = "商品标签", notes = "DAD_SAMPLING_INSPECTION 老爸抽检, CUSTOMIZED_PRODUCTS 定制品")
    List<String> tags;

    @ApiModelProperty(value = "合同销售价")
    private BigDecimal contractSalePrice;

    @ApiModelProperty(value = "平台佣金")
    private BigDecimal platformCommission;

    @ApiModelProperty(value ="合作方-业务类型")
    private List<CorpBizTypeDTO> corpBizType;

    @ApiModelProperty("下架时间")
    private Long downFrameTime;

    @ApiModelProperty("下架理由")
    private String downFrameReason;

}
