package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.daddylab.mall.wdtsdk.apiv2.statistic.dto.SearchLogisticsTraceResponse;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.WdtLogisticsStatus;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 旺店通物流轨迹
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtLogisticsTrace implements Serializable {

  private static final long serialVersionUID = 1L;

  /** 物流轨迹信息唯一键 */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 物流单状态 0:待查询 1:待取件 2:已取件 3:在途中 4:待配送 5:已签收 6:拒收 7:已处理 8:未订阅(不支持物流) 9:退件中 10:已退签 11:问题件 12:拦截成功
   * 13:拦截失败 99:推送失败
   */
  private WdtLogisticsStatus logisticsStatus;

  /** 原始单号(平台订单号，如果有多个以英文逗号分隔 且以增序排列，不重复，过长将被裁剪) */
  private String srcTids;

  /** 发货条件 1:款到发货 2:货到付款 3:"" 4:挂账 */
  private Integer deliveryTerm;

  /** 面单状态 0:待分配 1:已分配 2:已完成 3:有故障 4:回收失败 5:待回收 6:已回收 7:"" 8:手动作废 */
  private Integer mdStatus;

  /** 物流公司主键 */
  private Integer logisticsId;

  /** 物流单号 */
  private String logisticsNo;

  /** 物流公司编号 ERP内手动维护的物流公司编号 */
  private String logisticsCompanyNo;

  /** 物流追踪标记名称 */
  private String flagName;

  /** 标记id */
  private Integer flagId;

  /** 平台id */
  private Integer platformId;

  /** 店铺id */
  private Integer shopId;

  /** 店铺编号 */
  private String shopNo;

  /** 出库单号 */
  private String stockoutNo;

  /** 订单编号 */
  private String tradeNo;

  /** 订单类型 1:销售订单 */
  private Integer orderType;

  /** 仓库id */
  private Integer warehouseId;

  /** 仓库编号 */
  private String warehouseNo;

  /** 物流时效 已签收情况下，为签收时间与揽收时间差 */
  private String logisticsEfficiency;

  /** 中转时效 已揽收/在途中情况下，为当前时间与上一次物流轨迹 变化时间之差 */
  private Integer transitEfficiency;

  /** 发货时间 */
  private LocalDateTime consignTime;

  /** 在物流平台，物流轨迹更新时间 yyyy-MM-dd HH:mm:ss格式 */
  private LocalDateTime logisticsTime;

  /** 数据最后修改时间 yyyy-MM-dd HH:mm:ss格式 */
  private LocalDateTime modified;

  /** 揽收时间 */
  private LocalDateTime pickTime;

  /** 签收时间 */
  private LocalDateTime signedTime;

  /** 付款时间 */
  private LocalDateTime payTime;

  /** 递交时间 */
  private LocalDateTime deliveryTime;

  /** 回传时间 */
  private LocalDateTime syncTime;

  /** 审核时间 */
  private LocalDateTime checkTime;

  /** 备注 */
  private String remark;

  /** 仓库名称 */
  private String warehouseName;

  /** 店铺名称 */
  private String shopName;

  /** 省市区 订单上的省市区 */
  private String receiverArea;

  /** 应收 订单上的应收 */
  private String receivable;

  /** 退款状态 */
  private Integer refundStatus;

  /** 发货时效，发货时间-审核时间的小时数 */
  private Integer consignEfficiency;

  /** 揽收时效，揽收时间-支付时间的小时数 */
  private String pickPrescription;

  /** 文档上没有 */
  private Integer status;

  /** 物流详情，以JSON格式存储对应DetailListItem的相关数据 */
  private String detailList;

  public List<SearchLogisticsTraceResponse.DetailListItem> getDetailListObj() {
    return JsonUtil.parseList(detailList, SearchLogisticsTraceResponse.DetailListItem.class);
  }
}
