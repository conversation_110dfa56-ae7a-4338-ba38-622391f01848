//package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;
//
//import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.VirtualWarehouseInventory;
//
//import java.util.List;
//
///**
// * <p>
// * 虚拟仓库存信息 服务类
// * </p>
// *
// * <AUTHOR>
// * @since 2024-02-29
// */
//public interface IVirtualWarehouseInventoryService extends IDaddyService<VirtualWarehouseInventory> {
//
//    List<VirtualWarehouseInventory> selectByVirtualWarehouseNos(List<String> virtualWarehouseNos);
//}
