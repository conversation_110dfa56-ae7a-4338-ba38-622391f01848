package com.daddylab.supplier.item.infrastructure.rocketmq.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.Getter;

/**
 * @Author: <PERSON>
 * @Date: 2021/8/9 10:08
 * @Description: RocketMQ 延迟消息的延迟等级枚举
 * 目前 RocketMQ 延迟消息只支持 “1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h”，18 个 level；将来如果支持更多的等级
 * 都会放到这个枚举中统一维护。
 */
@Getter
public enum RocketMQDelayLevelEnum implements IEnum<Integer> {
    /**
     * 延迟级别
     */
    LEVEL_01(1, "延迟1s"),
    LEVEL_02(2, "延迟5s"),
    LEVEL_03(3, "延迟10s"),
    LEVEL_04(4, "延迟30s"),
    LEVEL_05(5, "延迟1m"),
    LEVEL_06(6, "延迟2m"),
    LEVEL_07(7, "延迟3m"),
    LEVEL_08(8, "延迟4m"),
    LEVEL_09(9, "延迟5m"),
    LEVEL_10(10, "延迟6m"),
    LEVEL_11(11, "延迟7m"),
    LEVEL_12(12, "延迟8m"),
    LEVEL_13(13, "延迟9m"),
    LEVEL_14(14, "延迟10m"),
    LEVEL_15(15, "延迟20m"),
    LEVEL_16(16, "延迟30m"),
    LEVEL_17(17, "延迟1h"),
    LEVEL_18(18, "延迟2h"),
    ;
    final private Integer value;
    final private String desc;

    RocketMQDelayLevelEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
