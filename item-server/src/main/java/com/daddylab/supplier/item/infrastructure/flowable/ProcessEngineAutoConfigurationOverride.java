package com.daddylab.supplier.item.infrastructure.flowable;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zaxxer.hikari.HikariDataSource;

import org.flowable.common.engine.api.async.AsyncTaskExecutor;
import org.flowable.common.engine.impl.cfg.IdGenerator;
import org.flowable.common.spring.AutoDeploymentStrategy;
import org.flowable.engine.ProcessEngine;
import org.flowable.http.common.api.client.FlowableHttpClient;
import org.flowable.job.service.impl.asyncexecutor.AsyncExecutor;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.flowable.spring.boot.*;
import org.flowable.spring.boot.app.FlowableAppProperties;
import org.flowable.spring.boot.condition.ConditionalOnProcessEngine;
import org.flowable.spring.boot.eventregistry.FlowableEventRegistryProperties;
import org.flowable.spring.boot.idm.FlowableIdmProperties;
import org.flowable.spring.boot.process.FlowableProcessProperties;
import org.flowable.spring.boot.process.Process;
import org.flowable.spring.boot.process.ProcessAsync;
import org.flowable.spring.boot.process.ProcessAsyncHistory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncListenableTaskExecutor;
import org.springframework.transaction.PlatformTransactionManager;

import java.io.IOException;
import java.util.List;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @since 2023/10/23
 */
@Configuration
@ConditionalOnProcessEngine
@AutoConfigureBefore(ProcessEngineAutoConfiguration.class)
@EnableConfigurationProperties(FlowableDataSourceProperties.class)
public class ProcessEngineAutoConfigurationOverride implements ApplicationContextAware {

    protected final FlowableProperties flowableProperties;
    protected final FlowableProcessProperties processProperties;
    protected final FlowableAppProperties appProperties;
    protected final FlowableIdmProperties idmProperties;
    protected final FlowableEventRegistryProperties eventProperties;
    protected final FlowableMailProperties mailProperties;
    protected final FlowableHttpProperties httpProperties;
    protected final FlowableAutoDeploymentProperties autoDeploymentProperties;

    public ProcessEngineAutoConfigurationOverride(
            FlowableProperties flowableProperties,
            FlowableProcessProperties processProperties,
            FlowableAppProperties appProperties,
            FlowableIdmProperties idmProperties,
            FlowableEventRegistryProperties eventProperties,
            FlowableMailProperties mailProperties,
            FlowableHttpProperties httpProperties,
            FlowableAutoDeploymentProperties autoDeploymentProperties) {

        this.flowableProperties = flowableProperties;
        this.processProperties = processProperties;
        this.appProperties = appProperties;
        this.idmProperties = idmProperties;
        this.eventProperties = eventProperties;
        this.mailProperties = mailProperties;
        this.httpProperties = httpProperties;
        this.autoDeploymentProperties = autoDeploymentProperties;
    }

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    DataSource flowableDataSource(FlowableDataSourceProperties flowableDataSourceProperties) {
        final HikariDataSource hikariDataSource = new HikariDataSource();
        hikariDataSource.setUsername(flowableDataSourceProperties.getUsername());
        hikariDataSource.setPassword(flowableDataSourceProperties.getPassword());
        hikariDataSource.setJdbcUrl(flowableDataSourceProperties.getUrl());
        hikariDataSource.setDriverClassName(flowableDataSourceProperties.getDriverClassName());
        return hikariDataSource;
    }
    
    @Bean
    public SpringProcessEngineConfiguration springProcessEngineConfiguration(
            FlowableDataSourceProperties flowableDataSourceProperties,
            PlatformTransactionManager platformTransactionManager,
            ObjectProvider<ObjectMapper> objectMapperProvider,
            @Process ObjectProvider<IdGenerator> processIdGenerator,
            ObjectProvider<IdGenerator> globalIdGenerator,
            @ProcessAsync ObjectProvider<AsyncExecutor> asyncExecutorProvider,
            @Qualifier("applicationTaskExecutor")
                    ObjectProvider<AsyncListenableTaskExecutor> applicationTaskExecutorProvider,
            @ProcessAsyncHistory ObjectProvider<AsyncExecutor> asyncHistoryExecutorProvider,
            ObjectProvider<AsyncListenableTaskExecutor> taskExecutor,
            @Process ObjectProvider<AsyncListenableTaskExecutor> processTaskExecutor,
            @Qualifier("flowableAsyncTaskInvokerTaskExecutor")
                    ObjectProvider<AsyncTaskExecutor> asyncTaskInvokerTaskExecutor,
            ObjectProvider<FlowableHttpClient> flowableHttpClient,
            ObjectProvider<List<AutoDeploymentStrategy<ProcessEngine>>>
                    processEngineAutoDeploymentStrategies)
            throws IOException {
        final ProcessEngineAutoConfiguration processEngineAutoConfiguration =
                new ProcessEngineAutoConfiguration(
                        flowableProperties,
                        processProperties,
                        appProperties,
                        idmProperties,
                        eventProperties,
                        mailProperties,
                        httpProperties,
                        autoDeploymentProperties);
        applicationContext
                .getAutowireCapableBeanFactory()
                .autowireBean(processEngineAutoConfiguration);
        
        return processEngineAutoConfiguration.springProcessEngineConfiguration(
                flowableDataSource(flowableDataSourceProperties),
                platformTransactionManager,
                objectMapperProvider,
                processIdGenerator,
                globalIdGenerator,
                asyncExecutorProvider,
                applicationTaskExecutorProvider,
                asyncHistoryExecutorProvider,
                taskExecutor,
                processTaskExecutor,
                asyncTaskInvokerTaskExecutor,
                flowableHttpClient,
                processEngineAutoDeploymentStrategies);
    }
}
