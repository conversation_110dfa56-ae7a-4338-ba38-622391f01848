package com.daddylab.supplier.item.application.afterSaleLogistics;

import cn.hutool.core.thread.NamedThreadFactory;
import cn.hutool.core.util.IdUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSaleLogistics.dto.AbnormalScanCmd;
import com.daddylab.supplier.item.application.kuaidaoyun.KdyBizService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsTrace;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsAbnormalityLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsAbnormalityService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsTraceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtLogisticsTraceService;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

/**
 * <AUTHOR>
 * @since 2024/12/25
 */
@Service
@Slf4j
public class AbnormalScanServiceImpl implements AbnormalScanService {

  @Resource IOrderLogisticsAbnormalityService abnormalityService;

  @Resource IOrderLogisticsAbnormalityLogService abnormalityLogService;

  @Autowired IOrderLogisticsTraceService orderLogisticsTraceService;

  @Autowired RedissonClient redissonClient;

  @Autowired private LogisticsExceptionBizService logisticsExceptionBizService;

  @Autowired private AfterSaleLogisticsBizService afterSaleLogisticsBizService;

  @Autowired AfterSaleLogisticsConfig afterSaleLogisticsConfig;

  @Autowired KdyBizService kdyBizService;

  @Autowired IWdtLogisticsTraceService wdtLogisticsTraceService;

  @Override
  public SingleResponse<String> abnormalScan(AbnormalScanCmd cmd) {
    final String taskId = IdUtil.fastUUID();
    final ScanContext context = new ScanContext();
    context.setTaskId(taskId);
    context.setParam(cmd);
    final RLock lock = redissonClient.getLock("abnormalScan");
    if (cmd.isForceUnlock()) {
      lock.forceUnlock();
    }
    if (lock.tryLock()) {
      try {
        final NamedThreadFactory threadFactory = new NamedThreadFactory("abnormalScan", true);
        final ExecutorService executorService =
            Executors.newFixedThreadPool(
                afterSaleLogisticsConfig.getAbnormalScanThreads(), threadFactory);
        final Scheduler scheduler = Schedulers.fromExecutorService(executorService);

        stream(context)
            .publishOn(scheduler, 100)
            .publish()
            .autoConnect()
            .doOnNext(this.abnormalScanHandler::handle)
            .onErrorContinue(
                (e, o) -> {
                  log.error("[物流异常][全局扫描]扫描过程中出现异常:{} o:{}", context, o, e);
                })
            .doOnComplete(
                () -> {
                  log.info("[物流异常][全局扫描]扫描完成:{}", context);
                })
            .subscribe();
        executorService.shutdown();
        log.info("[物流异常][全局扫描]关闭线程池，等待线程池剩余任务处理:{}", context);
        final boolean terminated = executorService.awaitTermination(60, TimeUnit.SECONDS);
        log.info("[物流异常][全局扫描]完全终止:{} terminated:{}", context, terminated);
      } catch (InterruptedException e) {
        log.error("[物流异常][全局扫描]扫描等待被中断:{}", context, e);
        throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "全局异常扫描被中断");
      } finally {
        lock.unlock();
        log.info("[物流异常][全局扫描]解锁成功:{}", context);
      }
    } else {
      throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "已有正在进行中的异常扫描任务");
    }
    return SingleResponse.of(taskId);
  }

  @Data
  static class ScanContext {
    private String taskId;
    private AbnormalScanCmd param;
  }

  private Flux<OrderLogisticsTrace> stream(ScanContext context) {
    return Flux.create(
        sink -> {
          final AbnormalScanCmd scanCmd = context.getParam();
          AtomicLong cursor = new AtomicLong();
          while (true) {
            List<OrderLogisticsTrace> traces =
                orderLogisticsTraceService.scanOpen(
                    scanCmd.getTraceTime(), cursor.get(), 100, scanCmd.getTraceIds());
            log.debug("[物流异常][全局扫描]扫描到{}条记录 C:{}", traces.size(), cursor.get());
            if (traces.isEmpty()) {
              break;
            } else {
              cursor.set(
                  traces.stream()
                      .max(Comparator.comparing(OrderLogisticsTrace::getId))
                      .get()
                      .getId());
              for (OrderLogisticsTrace trace : traces) {
                sink.next(trace);
              }
            }
          }
          sink.complete();
        });
  }

  @Autowired private AbnormalScanHandler abnormalScanHandler;
}
