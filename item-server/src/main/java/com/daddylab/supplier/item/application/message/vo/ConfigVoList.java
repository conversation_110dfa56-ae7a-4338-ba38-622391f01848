package com.daddylab.supplier.item.application.message.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/31 11:39 上午
 * @description
 */
@Data
@ApiModel("消息返回列表")
public class ConfigVoList implements Serializable {


    private static final long serialVersionUID = -649648893276539830L;

    @ApiModelProperty("后端商品变动消息配置")
    private List<MessageConfigVO> backItemList;

    @ApiModelProperty("平台商品预警消息配置")
    private List<MessageConfigVO> platformWarnList;
}
