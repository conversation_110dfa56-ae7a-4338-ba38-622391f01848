/*
package com.daddylab.supplier.item.application.virtualWarehouse;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.stockSpec.StockSpecBizService;
import com.daddylab.supplier.item.application.virtualWarehouse.dto.*;
import com.daddylab.supplier.item.application.warehouse.WarehouseBizService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.VirtualWarehouseTransMapper;
import com.daddylab.supplier.item.domain.exportTask.ExportManager;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.EditSkuRatioDto;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.WarehouseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.VirtualWarehouseInventoryGoodsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.VirtualWarehouseInventoryMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.VirtualWarehouseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.stockSpec.AvailableStockSpecVO;
import com.daddylab.supplier.item.types.stockSpec.StockSpecQuery;
import com.daddylab.supplier.item.types.stockSpec.StockSpecVO;
import com.daddylab.supplier.item.types.stockSpec.WarehouseStockSpecStatistic;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

*/
/**
 * <AUTHOR> up
 * @date 2024年03月01日 10:13 AM
 *//*

@Service
@Slf4j
@RequiredArgsConstructor
public class VirtualWarehouseBizServiceImpl implements VirtualWarehouseBizService {

    final IVirtualWarehouseService iVirtualWarehouseService;
    final IVirtualWarehouseInventoryService iVirtualWarehouseInventoryService;
    final IVirtualWarehouseInventoryGoodsService iVirtualWarehouseInventoryGoodsService;
    final VirtualWarehouseMapper virtualWarehouseMapper;
    final VirtualWarehouseInventoryGoodsMapper virtualWarehouseInventoryGoodsMapper;
    final StockSpecBizService stockSpecBizService;
    final IShopInventoryService shopInventoryService;
    final IShopInventoryGoodsService shopInventoryGoodsService;
    final IWarehouseGoodsInventoryLockStaticsService goodsInventoryLockStaticsService;
    final WarehouseBizService warehouseBizService;

    final UserGateway userGateway;
    final ExportManager exportManager;
    final OperateLogGateway operateLogGateway;
    final IWarehouseService iWarehouseService;
    final RedissonClient redissonClient;
    final IWarehouseGoodsInventoryStaticsService iWarehouseGoodsInventoryStaticsService;
    final IWdtStockSpecService iWdtStockSpecService;

    private final static Integer MAX_PARALLEL = 10;
    private static final ExecutorService PAGE_EXECUTOR = Executors.newFixedThreadPool(MAX_PARALLEL);
    private static final ExecutorService SAVE_EXECUTOR = Executors.newFixedThreadPool(MAX_PARALLEL);
    private static final BigDecimal ONE_HUNDRED = new BigDecimal("100");
    private static final Integer ONE_HUNDRED_INT = 100;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(VirtualWarehouseSaveCmd saveCmd) {
        Assert.isTrue(saveCmd.getName().length() <= 100, "虚拟仓名称不得超过30个字符");
        Map<String, Long> warehouseCountMap = saveCmd.getDetailSaveCmdList().stream().map(VirtualWarehouseDetailSaveCmd::getWarehouseNo)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        warehouseCountMap.forEach((k, v) -> {
            if (v > 1) {
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "仓库编码：" + k + "只允许出现一次");
            }
        });

        if (Objects.isNull(saveCmd.getId())) {
            addVirtualWarehouse(saveCmd);
            return;
        }


        VirtualWarehouse oldVirtualWarehouse = iVirtualWarehouseService.getById(saveCmd.getId());
        Assert.notNull(oldVirtualWarehouse, "虚拟仓ID非法");

        RLock lock = redissonClient.getLock("update_vw_" + oldVirtualWarehouse.getNo());
        boolean b = lock.tryLock();
        if (b) {
            try {
                updateVirtualWarehouse(saveCmd, oldVirtualWarehouse);
            } catch (Exception e) {
                log.error("更新虚拟仓异常", e);
                throw e;
            } finally {
                lock.unlock();
            }
        } else {
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "此单据正在更新，请稍后再试");
        }
    }

    */
/**
     * 前置检查
     * 业务线检查
     *
     * @param saveCmd
     *//*

    private void businessLineCheck(VirtualWarehouseSaveCmd saveCmd) {
//        Integer businessLine = saveCmd.getBusinessLine();
//        List<VirtualWarehouseDetailSaveCmd> detailSaveCmdList = saveCmd.getDetailSaveCmdList();
//        List<String> warehouseNos = detailSaveCmdList.stream().map(VirtualWarehouseDetailSaveCmd::getWarehouseNo).collect(Collectors.toList());
//        List<Warehouse> warehouseList = iWarehouseService.getBatchByNos(warehouseNos);
//        for (Warehouse warehouse : warehouseList) {
//            List<Integer> businessLineList = warehouse.getBusinessLineList();
//            if (!businessLineList.contains(businessLine)) {
//                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, warehouse.getNo() + "，此仓库业务线非法");
//            }
//        }
        return;
    }

    */
/**
     * 新建虚拟仓逻辑入口
     *
     * @param saveCmd
     *//*

    private void addVirtualWarehouse(VirtualWarehouseSaveCmd saveCmd) {
        businessLineCheck(saveCmd);

        VirtualWarehouse virtualWarehouse = saveVirtualWarehouse(saveCmd, null);
        iVirtualWarehouseService.save(virtualWarehouse);

        saveWarehouseInventoryAndGoodsInventoryHandler(virtualWarehouse.getId(), virtualWarehouse.getNo(),
                saveCmd.getDetailSaveCmdList(), saveCmd.getInventoryGoodsSaveCmdList(),
                saveCmd.getInventoryMode(), null, null, saveCmd.getStatus());

        // 新增系统列表中，仓库数据。
        addNewWarehouse(virtualWarehouse);

        operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.VIRTUAL_WAREHOUSE, virtualWarehouse.getId(), "新增虚拟仓", null);
    }

    */
/**
     * 在系统仓库档案中，添加虚拟仓
     *
     * @param virtualWarehouse
     *//*

    private void addNewWarehouse(VirtualWarehouse virtualWarehouse) {
        Integer count = iWarehouseService.lambdaQuery().eq(Warehouse::getNo, virtualWarehouse.getNo()).count();
        if (count == 0) {
            Warehouse warehouse = new Warehouse();
            warehouse.setNo(virtualWarehouse.getNo());
            warehouse.setName(virtualWarehouse.getName());
            warehouse.setWmsType(1);
            warehouse.setContacts(StrUtil.EMPTY);
            warehouse.setTel(StrUtil.EMPTY);
            warehouse.setPhone(StrUtil.EMPTY);
            warehouse.setProvince(StrUtil.EMPTY);
            warehouse.setCity(StrUtil.EMPTY);
            warehouse.setDistrict(StrUtil.EMPTY);
            warehouse.setAddress(StrUtil.EMPTY);
            warehouse.setState(1);
            warehouse.setZip(StrUtil.EMPTY);
            warehouse.setRemark("内部虚拟仓");
            warehouse.setAppKey(StrUtil.EMPTY);
            warehouse.setOrderPersonnel(StrUtil.EMPTY);
            warehouse.setBusinessLine(virtualWarehouse.getBusinessLine().toString());
            warehouse.setBusinessLineWithVal(virtualWarehouse.getBusinessLine());
            warehouse.setIsVirtualWarehouse(1);
            iWarehouseService.save(warehouse);
        }
    }

    */
/**
     * 更新虚拟仓逻辑入口
     *
     * @param saveCmd
     *//*

    private void updateVirtualWarehouse(VirtualWarehouseSaveCmd saveCmd, VirtualWarehouse oldVirtualWarehouse) {
        businessLineCheck(saveCmd);

        Long id = saveCmd.getId();
        VirtualWarehouseCompareBo oldOne = getCompareBo(oldVirtualWarehouse);
        List<VirtualWarehouseInventoryCompareBo> oldDetailCompareBos = getDetailCompareBo(oldVirtualWarehouse.getId());
        List<VirtualWarehouseGoodsCompareBo> oldSkuCompareBoLists = getSkuCompareBo(oldVirtualWarehouse.getId());
        List<Long> oldInventoryIds = iVirtualWarehouseInventoryService.lambdaQuery()
                .eq(VirtualWarehouseInventory::getVirtualWarehouseId, oldVirtualWarehouse.getId())
                .select(VirtualWarehouseInventory::getId).list().stream().map(VirtualWarehouseInventory::getId).collect(Collectors.toList());
        final InventoryMode oldInventoryMode = oldVirtualWarehouse.getInventoryMode();
        final Integer oldStatus = oldVirtualWarehouse.getStatus();
        // 编辑虚拟仓档案
        VirtualWarehouse updateVirtualWarehouse = saveVirtualWarehouse(saveCmd, oldVirtualWarehouse);
        int newStatus = saveCmd.getStatus();
        iVirtualWarehouseService.updateById(updateVirtualWarehouse);
        // 如果虚拟仓状态改变，更新仓库列表中，对应仓库的状态
        if (oldStatus != newStatus) {
            iWarehouseService.lambdaUpdate()
                    .set(Warehouse::getState, saveCmd.getStatus() + 1)
                    .eq(Warehouse::getNo, oldVirtualWarehouse.getNo())
                    .update();
        }
        // 监控库存模式变化
        InventoryModelMonitor modeMonitor = InventoryModelMonitor.NO_CHANGE;
        if (InventoryMode.LOCK.equals(oldInventoryMode) && InventoryMode.SHARED.equals(saveCmd.getInventoryMode())) {
            modeMonitor = InventoryModelMonitor.LOCK_TO_SHARE;
        }
        if (InventoryMode.SHARED.equals(oldInventoryMode) && InventoryMode.LOCK.equals(saveCmd.getInventoryMode())) {
            modeMonitor = InventoryModelMonitor.SHARE_TO_LOCK;
        }
        // 监控状态变化
        VWarehouseStatusMonitor statusMonitor = VWarehouseStatusMonitor.NO_CHANGE;
        if (oldStatus == 0 && newStatus == 1) {
            statusMonitor = VWarehouseStatusMonitor.RUN_TO_FORBID;
        }
        if (oldStatus == 1 && newStatus == 0) {
            statusMonitor = VWarehouseStatusMonitor.FORBID_TO_RUN;
        }

        // ------------------------ 具体逻辑处理 ------------------------------------

        // 移除一级/二级占比相关配置
        List<Long> reqIdList = saveCmd.getDetailSaveCmdList().stream().map(VirtualWarehouseDetailSaveCmd::getId).collect(Collectors.toList());
        oldInventoryIds.removeAll(reqIdList);
        List<Long> removeIdList = new LinkedList<>(oldInventoryIds);
        removeVirtualWarehouseAndGoodsInventory(updateVirtualWarehouse.getId(), removeIdList, modeMonitor, statusMonitor,
                oldStatus, oldInventoryMode);

        // 编辑一级/二级占比相关配置

        saveWarehouseInventoryAndGoodsInventoryHandler(oldVirtualWarehouse.getId(), oldVirtualWarehouse.getNo(),
                saveCmd.getDetailSaveCmdList(), saveCmd.getInventoryGoodsSaveCmdList(),
                saveCmd.getInventoryMode(), statusMonitor, modeMonitor, saveCmd.getStatus());

        // 构建比较对象，生成日志
        VirtualWarehouseCompareBo updateOne = getCompareBo(updateVirtualWarehouse);
        List<VirtualWarehouseInventoryCompareBo> updateDetailCompareBos = getDetailCompareBo(saveCmd.getDetailSaveCmdList());
        List<VirtualWarehouseGoodsCompareBo> updateSkuCompareBos = getSkuCompareBo(saveCmd.getInventoryGoodsSaveCmdList());

        // 操作记录
        String s1 = virtualWarehouseLog(oldOne, updateOne);
        if (StrUtil.isNotBlank(s1)) {
            operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.VIRTUAL_WAREHOUSE, updateVirtualWarehouse.getId(), s1, null);
        }
        List<String> s2 = virtualWarehouseInventoryLog(oldDetailCompareBos, updateDetailCompareBos);
        if (CollUtil.isNotEmpty(s2)) {
            operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.VIRTUAL_WAREHOUSE, updateVirtualWarehouse.getId(), StrUtil.join("。", s2), null);
        }
        List<String> s3 = virtualWarehouseSkuLog(oldSkuCompareBoLists, updateSkuCompareBos);
        if (CollUtil.isNotEmpty(s3)) {
            operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.VIRTUAL_WAREHOUSE, updateVirtualWarehouse.getId(), StrUtil.join("。", s3), null);
        }

    }

    */
/**
     * 保存虚拟仓当基本档案数据
     *
     * @param saveCmd
     * @param oldVirtualWarehouse
     * @return
     *//*

    private VirtualWarehouse saveVirtualWarehouse(VirtualWarehouseSaveCmd saveCmd, VirtualWarehouse oldVirtualWarehouse) {
        if (Objects.isNull(saveCmd.getId())) {
            VirtualWarehouse virtualWarehouse = new VirtualWarehouse();
            virtualWarehouse.setNo(iVirtualWarehouseService.getNewNo());
            virtualWarehouse.setName(saveCmd.getName());
            virtualWarehouse.setStatus(saveCmd.getStatus());
            virtualWarehouse.setBusinessLine(saveCmd.getBusinessLine());
            virtualWarehouse.setDescription(saveCmd.getDescription());
            virtualWarehouse.setInventoryMode(saveCmd.getInventoryMode());
            virtualWarehouse.setInventoryMode(saveCmd.getInventoryMode());
            return virtualWarehouse;
        } else {
            oldVirtualWarehouse.setName(saveCmd.getName());
            oldVirtualWarehouse.setStatus(saveCmd.getStatus());
            oldVirtualWarehouse.setBusinessLine(saveCmd.getBusinessLine());
            oldVirtualWarehouse.setDescription(saveCmd.getDescription());
            oldVirtualWarehouse.setInventoryMode(saveCmd.getInventoryMode());
            oldVirtualWarehouse.setInventoryMode(saveCmd.getInventoryMode());
            return oldVirtualWarehouse;
        }
    }


    // ------------------ 虚拟仓数据日志数据处理 -------------------------------

    private VirtualWarehouseCompareBo getCompareBo(VirtualWarehouse virtualWarehouse) {
        VirtualWarehouseCompareBo compareBo = new VirtualWarehouseCompareBo();
        compareBo.setNo(virtualWarehouse.getNo());
        compareBo.setName(virtualWarehouse.getName());
        compareBo.setDesc(virtualWarehouse.getDescription());
        BusinessLine businessLine = IEnum.getEnumByValue(BusinessLine.class, virtualWarehouse.getBusinessLine());
        compareBo.setBusinessLine(businessLine.getDesc());
        String status = virtualWarehouse.getStatus() == 0 ? "正常" : "停用";
        compareBo.setStatus(status);
        compareBo.setMode(virtualWarehouse.getInventoryMode().getDesc());
        return compareBo;
    }

    private List<VirtualWarehouseInventoryCompareBo> getDetailCompareBo(Long vwId) {
        List<VirtualWarehouseInventory> list = iVirtualWarehouseInventoryService.lambdaQuery().eq(VirtualWarehouseInventory::getVirtualWarehouseId, vwId).list();
        return list.stream().map(val -> {
            VirtualWarehouseInventoryCompareBo compareBo = new VirtualWarehouseInventoryCompareBo();
            compareBo.setName(val.getWarehouseName());
            compareBo.setInventory(val.getInventoryRatio());
            return compareBo;
        }).collect(Collectors.toList());
    }

    private List<VirtualWarehouseInventoryCompareBo> getDetailCompareBo(List<VirtualWarehouseDetailSaveCmd> cmds) {
        return cmds.stream().map(val -> {
            VirtualWarehouseInventoryCompareBo compareBo = new VirtualWarehouseInventoryCompareBo();
            compareBo.setName(val.getWarehouseName());
            compareBo.setInventory(val.getInventoryRatio());
            return compareBo;
        }).collect(Collectors.toList());
    }

    private List<VirtualWarehouseGoodsCompareBo> getSkuCompareBo(Long vwId) {
        List<VirtualWarehouseInventoryGoods> list = iVirtualWarehouseInventoryGoodsService.lambdaQuery().eq(VirtualWarehouseInventoryGoods::getVirtualWarehouseId, vwId).list();
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(val -> {
            VirtualWarehouseGoodsCompareBo compareBo = new VirtualWarehouseGoodsCompareBo();
            compareBo.setSkuCode(val.getWarehouseNo() + "-" + val.getSkuNo());
            compareBo.setInventory(val.getInventoryRatio());
            return compareBo;
        }).collect(Collectors.toList());
    }

    private List<VirtualWarehouseGoodsCompareBo> getSkuCompareBo(List<InventoryGoodsSaveCmd> cmds) {
        if (CollUtil.isEmpty(cmds)) {
            return new LinkedList<>();
        }
        return cmds.stream().map(val -> {
            VirtualWarehouseGoodsCompareBo compareBo = new VirtualWarehouseGoodsCompareBo();
            compareBo.setSkuCode(val.getWarehouseNo() + "-" + val.getSkuNo());
            compareBo.setInventory(val.getInventoryRatio());
            return compareBo;
        }).collect(Collectors.toList());
    }

    private String virtualWarehouseLog(VirtualWarehouseCompareBo oldOne, VirtualWarehouseCompareBo updateOne) {
        DiffUtil.ObjListDiff objListDiff = DiffUtil.diffObjList(ListUtil.of(oldOne), ListUtil.of(updateOne), VirtualWarehouseCompareBo.class);
        AtomicReference<String> msg = new AtomicReference<>("");
        objListDiff.getChangeMap().forEach((k, v) -> {
            String m1 = "基本信息修改：" + k + ",";
            List<String> pList = new LinkedList<>();
            for (DiffUtil.ChangePropertyObj changePropertyObj : v) {
                String m2 = StrUtil.format("{}从{}修改为{}", changePropertyObj.getProperty(), changePropertyObj.getOldVal(), changePropertyObj.getNewVal());
                pList.add(m2);
            }
            msg.set(m1 + StrUtil.join(StrUtil.COMMA, pList) + "。");
        });
        return msg.get();
    }

    private List<String> virtualWarehouseInventoryLog(List<VirtualWarehouseInventoryCompareBo> oList, List<VirtualWarehouseInventoryCompareBo> nList) {
        List<String> msgs = new LinkedList<>();

        DiffUtil.ObjListDiff objListDiff = DiffUtil.diffObjList(oList, nList, VirtualWarehouseInventoryCompareBo.class);
        HashSet<Object> addIdSet = objListDiff.getAddIdSet();
        if (CollUtil.isNotEmpty(addIdSet)) {
            msgs.add("新增仓库：" + StrUtil.join(StrUtil.COMMA, addIdSet));
        }
        HashSet<Object> removeIdSet = objListDiff.getRemoveIdSet();
        if (CollUtil.isNotEmpty(removeIdSet)) {
            msgs.add("移除仓库：" + StrUtil.join(StrUtil.COMMA, removeIdSet));
        }
        Map<Object, Set<DiffUtil.ChangePropertyObj>> changeMap = objListDiff.getChangeMap();
        changeMap.forEach((k, v) -> {
            String m1 = "仓库修改：" + k + ",";
            List<String> pList = new LinkedList<>();
            for (DiffUtil.ChangePropertyObj changePropertyObj : v) {
                String m2;
                if (changePropertyObj.getProperty().contains("库存占比")) {
                    m2 = StrUtil.format("{}从{}%修改为{}%", changePropertyObj.getProperty(), changePropertyObj.getOldVal(), changePropertyObj.getNewVal());
                } else {
                    m2 = StrUtil.format("{}从{}修改为{}", changePropertyObj.getProperty(), changePropertyObj.getOldVal(), changePropertyObj.getNewVal());
                }
                pList.add(m2);
            }
            msgs.add(m1 + StrUtil.join(StrUtil.COMMA, pList) + "。");
        });

        return msgs;
    }

    private List<String> virtualWarehouseSkuLog(List<VirtualWarehouseGoodsCompareBo> oList, List<VirtualWarehouseGoodsCompareBo> nList) {
        List<String> msgs = new LinkedList<>();
        if (CollUtil.isEmpty(nList)) {
            return msgs;
        }

        DiffUtil.ObjListDiff objListDiff = DiffUtil.diffObjList(oList, nList, VirtualWarehouseGoodsCompareBo.class);

        Map<Object, Set<DiffUtil.ChangePropertyObj>> changeMap = objListDiff.getChangeMap();
        changeMap.forEach((k, v) -> {
            String m1 = "sku修改：" + k + ",";
            List<String> pList = new LinkedList<>();
            for (DiffUtil.ChangePropertyObj changePropertyObj : v) {
                String m2 = StrUtil.format("{}从{}%修改为{}%", changePropertyObj.getProperty(), changePropertyObj.getOldVal(), changePropertyObj.getNewVal());
                pList.add(m2);
            }
            msgs.add(m1 + StrUtil.join(StrUtil.COMMA, pList) + "。");
        });

        return msgs;
    }

    // ------------------ 虚拟仓数据日志数据处理 End -------------------------------

    @Data
    private static class NeedDealRatioRes {
        Boolean needDealWarehouseRatio = false;
        Boolean needDealSkuRatio = false;
        Boolean needDealLockRatio = false;
    }


    */
/*@Deprecated
    private NeedDealRatioRes getNeedDealRatioRes(Integer oldVwStatus,
                                                 VWarehouseStatusMonitor statusMonitor,
                                                 InventoryMode oldInventoryMode,
                                                 InventoryModelMonitor modelMonitor
    ) {
        boolean needDealWarehouseRatio = false;
        boolean needDealSkuRatio = false;
        boolean needDealLockRatio = false;
        // 1.状态(0正常,1停用)，2.模式
        // 1.1 一直停用，1.2 一直运行，1.3 停用变为运行，1.4 运行变停用
        // 2.1 一直独占。2.2 一直共享，2.3 独占变共享。  2.4 共享变独占
        // 16种情况分别处理和判断

        // 1.1 停用不变
        if (VWarehouseStatusMonitor.NO_CHANGE.equals(statusMonitor) && oldVwStatus == 1) {
            // do nothing
        }
        // 1.2 一直运行
        if (VWarehouseStatusMonitor.NO_CHANGE.equals(statusMonitor) && oldVwStatus == 0) {
            // 2.1 一直独占
            if (InventoryModelMonitor.NO_CHANGE.equals(modelMonitor) && InventoryMode.LOCK.equals(oldInventoryMode)) {
                needDealWarehouseRatio = true;
                needDealSkuRatio = true;
                needDealLockRatio = true;
            }
            // 2.2 一直共享
            if (InventoryModelMonitor.NO_CHANGE.equals(modelMonitor) && InventoryMode.SHARED.equals(oldInventoryMode)) {
                needDealWarehouseRatio = true;
                needDealSkuRatio = true;
            }
            // 2.3 共享变独占
            if (InventoryModelMonitor.SHARE_TO_LOCK.equals(oldInventoryMode)) {
                needDealWarehouseRatio = true;
                needDealSkuRatio = true;
            }
            // 2.3 独占变共享
            if (InventoryModelMonitor.LOCK_TO_SHARE.equals(oldInventoryMode)) {
                needDealWarehouseRatio = true;
                needDealSkuRatio = true;
                needDealLockRatio = true;
            }
        }
        // 1.3 停用变为运行
        if (VWarehouseStatusMonitor.FORBID_TO_RUN.equals(statusMonitor)) {
            // do nothing
        }
        // 1.4 运行变停用
        if (VWarehouseStatusMonitor.RUN_TO_FORBID.equals(statusMonitor)) {
            // 2.1 一直独占
            if (InventoryModelMonitor.NO_CHANGE.equals(modelMonitor) && InventoryMode.LOCK.equals(oldInventoryMode)) {
                needDealWarehouseRatio = true;
                needDealSkuRatio = true;
                needDealLockRatio = true;
            }
            // 2.2 一直共享
            if (InventoryModelMonitor.NO_CHANGE.equals(modelMonitor) && InventoryMode.SHARED.equals(oldInventoryMode)) {
                needDealWarehouseRatio = true;
                needDealSkuRatio = true;
            }
            // 2.3 共享变独占
            if (InventoryModelMonitor.SHARE_TO_LOCK.equals(oldInventoryMode)) {
                needDealWarehouseRatio = true;
                needDealSkuRatio = true;
            }
            // 2.3 独占变共享
            if (InventoryModelMonitor.LOCK_TO_SHARE.equals(oldInventoryMode)) {
                needDealWarehouseRatio = true;
                needDealSkuRatio = true;
                needDealLockRatio = true;
            }
        }

        NeedDealRatioRes res = new NeedDealRatioRes();
        res.setNeedDealWarehouseRatio(needDealWarehouseRatio);
        res.setNeedDealSkuRatio(needDealSkuRatio);
        res.setNeedDealLockRatio(needDealLockRatio);
        return res;
    }*//*



    private NeedDealRatioRes getNeedDealRatioRes(Integer oldVwStatus,
                                                 VWarehouseStatusMonitor statusMonitor,
                                                 InventoryMode oldInventoryMode,
                                                 InventoryModelMonitor modelMonitor) {
        boolean needDealWarehouseRatio = false;
        boolean needDealSkuRatio = false;
        boolean needDealLockRatio = false;

        // 1. 状态(0正常, 1停用)
        // 1.1 一直停用，1.2 一直运行，1.3 停用变为运行，1.4 运行变停用
        // 2. 模式
        // 2.1 一直独占，2.2 一直共享，2.3 独占变共享，2.4 共享变独占
        // 16种情况分别处理和判断

        // 1.2 一直运行
        if (VWarehouseStatusMonitor.NO_CHANGE.equals(statusMonitor) && oldVwStatus == 0) {
            // 2.1 一直独占或2.2 一直共享
            if (InventoryModelMonitor.NO_CHANGE.equals(modelMonitor)) {
                needDealWarehouseRatio = true;
                needDealSkuRatio = true;
                // 2.1 一直独占
                if (InventoryMode.LOCK.equals(oldInventoryMode)) {
                    needDealLockRatio = true;
                }
            }
            // 2.3 共享变独占或2.4 独占变共享
            else if (InventoryModelMonitor.SHARE_TO_LOCK.equals(modelMonitor) ||
                    InventoryModelMonitor.LOCK_TO_SHARE.equals(modelMonitor)) {
                needDealWarehouseRatio = true;
                needDealSkuRatio = true;
                needDealLockRatio = true;
            }
        }
        // 1.4 运行变停用
        else if (VWarehouseStatusMonitor.RUN_TO_FORBID.equals(statusMonitor)) {
            // 2.1 一直独占或2.2 一直共享
            if (InventoryModelMonitor.NO_CHANGE.equals(modelMonitor)) {
                needDealWarehouseRatio = true;
                needDealSkuRatio = true;
                // 2.1 一直独占
                if (InventoryMode.LOCK.equals(oldInventoryMode)) {
                    needDealLockRatio = true;
                }
            }
            // 2.3 共享变独占或2.4 独占变共享
            else if (InventoryModelMonitor.SHARE_TO_LOCK.equals(modelMonitor) ||
                    InventoryModelMonitor.LOCK_TO_SHARE.equals(modelMonitor)) {
                needDealWarehouseRatio = true;
                needDealSkuRatio = true;
                needDealLockRatio = true;
            }
        }

        NeedDealRatioRes res = new NeedDealRatioRes();
        res.setNeedDealWarehouseRatio(needDealWarehouseRatio);
        res.setNeedDealSkuRatio(needDealSkuRatio);
        res.setNeedDealLockRatio(needDealLockRatio);
        return res;
    }


    */
/**
     * 更新虚拟仓
     * 移除虚拟仓下某些实体仓数据，移除这些实仓的一级占比和二级占比数据
     *
     * @param vwId
     * @param removeIdList
     * @param modelMonitor
     *//*

    private void removeVirtualWarehouseAndGoodsInventory(Long vwId, List<Long> removeIdList,
                                                         InventoryModelMonitor modelMonitor,
                                                         VWarehouseStatusMonitor statusMonitor,
                                                         Integer oldVwStatus,
                                                         InventoryMode oldInventoryMode) {
        if (CollUtil.isEmpty(removeIdList)) {
            return;
        }

        final NeedDealRatioRes needDealRatioRes = getNeedDealRatioRes(oldVwStatus, statusMonitor, oldInventoryMode, modelMonitor);

        // 不需要考虑虚拟仓本身的状态变化，移除操作一律将库存占比改为0
        List<VirtualWarehouseInventory> removeList = iVirtualWarehouseInventoryService.selectBatchIds(removeIdList);
        for (VirtualWarehouseInventory inventory : removeList) {

            // 移除一级占比
            if (needDealRatioRes.getNeedDealWarehouseRatio()) {
                iWarehouseGoodsInventoryStaticsService.addWarehouseRatio(inventory.getWarehouseNo(), 0, inventory.getInventoryRatio());
            }

            List<VirtualWarehouseInventoryGoods> goodsList = iVirtualWarehouseInventoryGoodsService.lambdaQuery()
                    .eq(VirtualWarehouseInventoryGoods::getVirtualWarehouseInventoryId, inventory.getId()).list();
            List<EditSkuRatioDto> editSkuRatioDtoList = new LinkedList<>();
            List<LockSkuRatioDto> lockSkuRatioDtoList = new LinkedList<>();
            for (VirtualWarehouseInventoryGoods goodsOne : goodsList) {
                // 移除二级占比，全部置为0
                if (needDealRatioRes.getNeedDealSkuRatio()) {
                    final EditSkuRatioDto editSkuRatioDto = EditSkuRatioDto.of(inventory.getWarehouseNo(), goodsOne.getSkuNo(),
                            goodsOne.getInventoryRatio(), 0);
                    editSkuRatioDtoList.add(editSkuRatioDto);
                }

                // 处理独占占比，全部置为0
                if (needDealRatioRes.getNeedDealLockRatio()) {
                    final LockSkuRatioDto lockSkuRatioDto = LockSkuRatioDto.of(inventory.getWarehouseNo(), goodsOne.getSkuNo(),
                            goodsOne.getInventoryRatio(), 0, goodsOne.getInventoryLockNum(), 0);
                    lockSkuRatioDtoList.add(lockSkuRatioDto);
                }
            }

            iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(editSkuRatioDtoList);
            goodsInventoryLockStaticsService.handlerBatch(lockSkuRatioDtoList);

            if (needDealRatioRes.getNeedDealLockRatio()) {
                warehouseBizService.refreshSharedGoodsInventoryRatio(ListUtil.of(inventory.getVirtualWarehouseId()),
                        null, inventory.getWarehouseNo());
            }
        }

        iVirtualWarehouseInventoryService.removeByIds(removeIdList);
        iVirtualWarehouseInventoryGoodsService.lambdaUpdate()
                .in(VirtualWarehouseInventoryGoods::getVirtualWarehouseInventoryId, removeIdList)
                .remove();

        String removeMsg = removeList.stream().map(VirtualWarehouseInventory::getWarehouseName).collect(Collectors.joining(StrUtil.COMMA));
        operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.VIRTUAL_WAREHOUSE, vwId, "移除仓库：" + removeMsg, null);
    }

    */
/**
     * 一级占比、二级占比，统一处理主入口
     *
     * @param id                        虚拟仓ID
     * @param no                        虚拟仓编码
     * @param detailSaveCmdList         一级实仓占比请求参数
     * @param inventoryGoodsSaveCmdList 二级SKU占比请求参数
     * @param inventoryMode             虚拟仓模式
     * @param statusMonitor             虚拟仓状态监控
     * @param modelMonitor              虚拟仓库存模式监控
     *//*

    private void saveWarehouseInventoryAndGoodsInventoryHandler(Long id, String no,
                                                                List<VirtualWarehouseDetailSaveCmd> detailSaveCmdList,
                                                                List<InventoryGoodsSaveCmd> inventoryGoodsSaveCmdList,
                                                                InventoryMode inventoryMode,
                                                                VWarehouseStatusMonitor statusMonitor,
                                                                InventoryModelMonitor modelMonitor,
                                                                Integer reqVwStatus) {
        if (CollUtil.isEmpty(detailSaveCmdList)) {
            return;
        }
        Map<String, VirtualWarehouseDetailSaveCmd> warehouseReqMap = detailSaveCmdList.stream()
                .collect(Collectors.toMap(VirtualWarehouseDetailSaveCmd::getWarehouseNo, Function.identity()));
        Map<String, List<InventoryGoodsSaveCmd>> warehouseGoodsReqMap =
                CollUtil.isNotEmpty(inventoryGoodsSaveCmdList)
                        ? inventoryGoodsSaveCmdList.stream()
                        .collect(Collectors.groupingBy(InventoryGoodsSaveCmd::getWarehouseNo))
                        : new HashMap<>(258);

        warehouseReqMap.forEach((warehouseNo, warehouseReq) -> {
            try {
                final VirtualWarehouseDetailSaveCmd warehouseSaveCmd = warehouseReqMap.get(warehouseNo);
                boolean isAdd = Objects.isNull(warehouseSaveCmd.getId()) || Objects.equals(warehouseSaveCmd.getId(), 0L);
                if (isAdd) {
                    final VirtualWarehouseInventoryRes inventoryRes = addWarehouseInfoAndInventory(id, no, inventoryMode, warehouseSaveCmd);
                    final List<InventoryGoodsSaveCmd> goodsSaveCmdList = warehouseGoodsReqMap.getOrDefault(warehouseNo, new LinkedList<>());
                    addWarehouseGoodsInventory(id, no, inventoryRes, goodsSaveCmdList, inventoryMode);
                } else {
                    final VirtualWarehouseInventoryRes inventoryRes = editWarehouseInfoAndInventory(warehouseSaveCmd, statusMonitor,
                            inventoryMode, reqVwStatus);
                    final List<InventoryGoodsSaveCmd> goodsSaveCmdList = warehouseGoodsReqMap.getOrDefault(warehouseNo, new LinkedList<>());
                    editWarehouseGoodsInventory(inventoryRes, goodsSaveCmdList, modelMonitor, statusMonitor, inventoryMode, reqVwStatus);
                }
            } catch (Exception e) {
                log.error("saveWarehouseInventoryAndGoodsInventoryHandler error.warehouseNo:{},req:{}",
                        warehouseNo, JsonUtil.toJson(warehouseReq), e);
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "保存虚拟仓库存数据异常。" + e.getMessage());
            }

        });
    }

    */
/**
     * 编辑一级占比
     *
     * @param reqWarehouse
     * @return
     *//*

    private VirtualWarehouseInventoryRes editWarehouseInfoAndInventory(VirtualWarehouseDetailSaveCmd reqWarehouse,
                                                                       VWarehouseStatusMonitor statusMonitor,
                                                                       InventoryMode reqMode, Integer reqVwStatus) {
        Assert.notNull(reqWarehouse.getId(), "此仓库配置明细ID为空。no:" + reqWarehouse.getWarehouseNo());
        final Long id = reqWarehouse.getId();
        final VirtualWarehouseInventory oldVwInventory = iVirtualWarehouseInventoryService.getById(id);
        Assert.notNull(oldVwInventory, "此仓库配置明细不存在。no:" + reqWarehouse.getWarehouseNo());

        final Integer oldRatio = oldVwInventory.getInventoryRatio();
        final Integer newRatio = reqWarehouse.getInventoryRatio();
        oldVwInventory.setInventoryMode(reqMode);

        // 虚拟仓状态，运行改为禁用，释放一级占比库存
        if (VWarehouseStatusMonitor.RUN_TO_FORBID.equals(statusMonitor)) {
            iWarehouseGoodsInventoryStaticsService.addWarehouseRatio(oldVwInventory.getWarehouseNo(), 0, oldRatio);
        }
        // 虚拟仓状态,禁用改为运行，抢占一级占比库存
        else if (VWarehouseStatusMonitor.FORBID_TO_RUN.equals(statusMonitor)) {
            iWarehouseGoodsInventoryStaticsService.addWarehouseRatio(oldVwInventory.getWarehouseNo(), newRatio, 0);
        }
        // 虚拟仓状态不变,并且是运行状态
        else if (VWarehouseStatusMonitor.NO_CHANGE.equals(statusMonitor) && reqVwStatus == 0) {
            iWarehouseGoodsInventoryStaticsService.addWarehouseRatio(oldVwInventory.getWarehouseNo(), newRatio, oldRatio);
        }

        oldVwInventory.setInventoryRatio(newRatio);
        iVirtualWarehouseInventoryService.updateById(oldVwInventory);

        VirtualWarehouseInventoryRes res = new VirtualWarehouseInventoryRes();
        res.setVirtualWarehouseInventory(oldVwInventory);
        res.setOldWarehouseInventory(oldRatio);
        res.setNewWarehouseInventory(newRatio);
        res.setInventoryChange(!oldRatio.equals(newRatio));

        return res;
    }

    */
/**
     * 新增一级占比
     *
     * @param vwId
     * @param vwNo
     * @param reqWarehouse
     * @return
     *//*

    private VirtualWarehouseInventoryRes addWarehouseInfoAndInventory(Long vwId, String vwNo, InventoryMode inventoryMode,
                                                                      VirtualWarehouseDetailSaveCmd reqWarehouse) {

        VirtualWarehouseInventory inventory = new VirtualWarehouseInventory();
        inventory.setVirtualWarehouseId(vwId);
        inventory.setVirtualWarehouseNo(vwNo);
        inventory.setWarehouseNo(reqWarehouse.getWarehouseNo());
        inventory.setWarehouseName(reqWarehouse.getWarehouseName());
        inventory.setInventoryRatio(reqWarehouse.getInventoryRatio());
        inventory.setInventoryMode(inventoryMode);

        iWarehouseGoodsInventoryStaticsService.addWarehouseRatio(inventory.getWarehouseNo(), inventory.getInventoryRatio(), 0);

        iVirtualWarehouseInventoryService.save(inventory);

        VirtualWarehouseInventoryRes res = new VirtualWarehouseInventoryRes();
        res.setVirtualWarehouseInventory(inventory);
        res.setOldWarehouseInventory(0);
        res.setNewWarehouseInventory(inventory.getInventoryRatio());
        res.setInventoryChange(true);

        return res;
    }

    */
/**
     * 单一实仓 新增二级占比
     * <p>
     * 1.保存新建虚拟仓的二级占比，SKU维度。
     * 2 如果是锁定状态，记下锁定数量，刷新其他虚拟仓和店铺中的占比。
     *
     * @param inventoryRes          一级占比处理结果
     * @param reqGoodsInventoryList 二级占比请求参数
     * @param inventoryMode         库存模式监控
     *//*

    private void addWarehouseGoodsInventory(Long vwId, String vwNo, VirtualWarehouseInventoryRes inventoryRes,
                                            List<InventoryGoodsSaveCmd> reqGoodsInventoryList,
                                            InventoryMode inventoryMode) {

        VirtualWarehouseInventory virtualWarehouseInventory = inventoryRes.getVirtualWarehouseInventory();
        String warehouseNo = virtualWarehouseInventory.getWarehouseNo();
        List<WdtStockSpec> thisWarehouseAllSkuList = iWdtStockSpecService.lambdaQuery()
                .eq(WdtStockSpec::getWarehouseNo, warehouseNo)
                .eq(WdtStockSpec::getDefect, 0)
                .list()
                .stream().distinct().collect(Collectors.toList());
        // 此仓库下没有正品的SKU，直接返回
        if (CollUtil.isEmpty(thisWarehouseAllSkuList)) {
            return;
        }
        Map<String, InventoryGoodsSaveCmd> reqGoodsInventoryMap = new HashMap<>(128);
        if (CollUtil.isNotEmpty(reqGoodsInventoryList)) {
            reqGoodsInventoryMap = reqGoodsInventoryList.stream()
                    .collect(Collectors.toMap(InventoryGoodsSaveCmd::getSkuNo, Function.identity()));
        }
        boolean isAddLockData = InventoryMode.LOCK.equals(inventoryMode);
        Map<String, BigDecimal> allSkuAvailableStockMap = isAddLockData ?
                allSkuAvailableStockMap(warehouseNo, null)
                : new HashMap<>(128);

        List<VirtualWarehouseInventoryGoods> addGoodsList = new LinkedList<>();
        List<EditSkuRatioDto> addEditSkuRatioDtoList = new LinkedList<>();
        List<LockSkuRatioDto> addLockRatioDtoList = new LinkedList<>();

        // ------------------------- prepare data finish ----------------------

        for (WdtStockSpec oneSku : thisWarehouseAllSkuList) {
            VirtualWarehouseInventoryGoods goods = new VirtualWarehouseInventoryGoods();
            goods.setWarehouseNo(warehouseNo);
            goods.setSpuNo(StrUtil.isBlank(oneSku.getGoodsNo()) ? StrUtil.EMPTY : oneSku.getGoodsNo());
            goods.setSkuNo(oneSku.getSpecNo());
            InventoryGoodsSaveCmd goodsSaveCmd = reqGoodsInventoryMap.get(goods.getSkuNo());
            if (Objects.nonNull(goodsSaveCmd)) {
                goods.setInventoryRatio(goodsSaveCmd.getInventoryRatio());
            } else {
                goods.setInventoryRatio(inventoryRes.getNewWarehouseInventory());
            }
            goods.setInventoryRatio2(new BigDecimal(goods.getInventoryRatio()));
            goods.setVirtualWarehouseId(vwId);
            goods.setVirtualWarehouseNo(vwNo);
            goods.setVirtualWarehouseInventoryId(inventoryRes.getVirtualWarehouseInventory().getId());
            // 锁定模式
            if (isAddLockData) {
                BigDecimal skuAvailableStock = allSkuAvailableStockMap.getOrDefault(goods.getSkuNo(), BigDecimal.ZERO);
                int inventoryLockNum = (new BigDecimal(goods.getInventoryRatio()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP))
                        .multiply(skuAvailableStock).setScale(0, RoundingMode.HALF_UP).intValue();
                goods.setInventoryLockNum(inventoryLockNum);
                goods.setInventoryRatio2(BigDecimal.valueOf(goods.getInventoryRatio()));

                final LockSkuRatioDto lockSkuRatioDto = LockSkuRatioDto.of(warehouseNo, goods.getSkuNo(),
                        0, goods.getInventoryRatio(), 0, goods.getInventoryLockNum());
                addLockRatioDtoList.add(lockSkuRatioDto);
            }
            addGoodsList.add(goods);

            EditSkuRatioDto editSkuRatioDto = EditSkuRatioDto.of(warehouseNo, goods.getSkuNo(), 0, goods.getInventoryRatio());
            addEditSkuRatioDtoList.add(editSkuRatioDto);
        }

        // 锁定的占比处理成功
        if (isAddLockData) {
            goodsInventoryLockStaticsService.handlerBatch(addLockRatioDtoList);
        }
        // 二级占比是否能抢占成功
        iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(addEditSkuRatioDtoList);

        if (CollUtil.isNotEmpty(addGoodsList)) {
            iVirtualWarehouseInventoryGoodsService.saveBatch(addGoodsList);
        }

        // --------------------------- add lock data need to refresh other ratio -------------------

        if (isAddLockData) {
            warehouseBizService.refreshSharedGoodsInventoryRatio(ListUtil.of(vwId), null, warehouseNo);
        }
    }

    */
/**
     * 编辑二级占比
     *
     * @param inventoryRes           一级占比处理结果
     * @param reqGoodsInventoryList  二级占比请求参数
     * @param modelMonitor           库存模式监控
     * @param warehouseStatusMonitor 虚拟仓状态监控
     *//*

    private void editWarehouseGoodsInventory(VirtualWarehouseInventoryRes inventoryRes,
                                             List<InventoryGoodsSaveCmd> reqGoodsInventoryList,
                                             InventoryModelMonitor modelMonitor,
                                             VWarehouseStatusMonitor warehouseStatusMonitor,
                                             InventoryMode inventoryMode,
                                             Integer vwStatus) {

        VirtualWarehouseInventory virtualWarehouseInventory = inventoryRes.getVirtualWarehouseInventory();
        String warehouseNo = virtualWarehouseInventory.getWarehouseNo();
        final List<VirtualWarehouseInventoryGoods> goodsList = iVirtualWarehouseInventoryGoodsService.lambdaQuery()
                .eq(VirtualWarehouseInventoryGoods::getVirtualWarehouseId, virtualWarehouseInventory.getVirtualWarehouseId())
                .list();
        // 没有占比值的变化
        boolean b1 = CollUtil.isEmpty(goodsList) && (CollUtil.isEmpty(reqGoodsInventoryList) && !inventoryRes.getInventoryChange());
        // 虚拟仓状态，库存模式 都没有发生变化
        boolean b2 = InventoryModelMonitor.NO_CHANGE.equals(modelMonitor) && VWarehouseStatusMonitor.NO_CHANGE.equals(warehouseStatusMonitor);
        if (b1 && b2) {
            return;
        }

        Map<String, InventoryGoodsSaveCmd> reqGoodsInventoryMap = reqGoodsInventoryList.stream()
                .collect(Collectors.toMap(InventoryGoodsSaveCmd::getSkuNo, Function.identity()));

        // 虚拟仓状态发生改变。禁止 ----> 运行,需要进行库存抢占
        boolean forbidToRun = VWarehouseStatusMonitor.FORBID_TO_RUN.equals(warehouseStatusMonitor);
        // 虚拟仓状态发生改变。运行 ----> 禁止,释放库存
        boolean runToForbid = VWarehouseStatusMonitor.RUN_TO_FORBID.equals(warehouseStatusMonitor);
        // 库存模式发生改变。需要抢占独占库存
        boolean shareToLock = InventoryModelMonitor.SHARE_TO_LOCK.equals(modelMonitor);
        // 库存模式发生改变。需要释放独占库存
        boolean lockToShare = InventoryModelMonitor.LOCK_TO_SHARE.equals(modelMonitor);
        // 编辑锁定模式的数据
        boolean editLockData = InventoryModelMonitor.NO_CHANGE.equals(modelMonitor) && InventoryMode.LOCK.equals(inventoryMode);

        Map<String, BigDecimal> allSkuAvailableStockMap = shareToLock || editLockData ? allSkuAvailableStockMap(warehouseNo,
                goodsList.stream()
                        .map(VirtualWarehouseInventoryGoods::getSkuNo)
                        .distinct().collect(Collectors.toList())) : new HashMap<>();
        List<VirtualWarehouseInventoryGoods> updateGoodsList = new LinkedList<>();
        List<EditSkuRatioDto> editSkuRatioDtoList = new LinkedList<>();
        List<LockSkuRatioDto> lockSkuRatioDtoList = new LinkedList<>();

        for (VirtualWarehouseInventoryGoods goodsOne : goodsList) {
            int setInventoryRatio = inventoryRes.getNewWarehouseInventory();
            BigDecimal setInventoryRatio2 = new BigDecimal(inventoryRes.getNewWarehouseInventory());
            final InventoryGoodsSaveCmd goodsSaveCmd = reqGoodsInventoryMap.get(goodsOne.getSkuNo());
            if (Objects.nonNull(goodsSaveCmd)) {
                setInventoryRatio = goodsSaveCmd.getInventoryRatio();
            }

            // 处理一般占比。这块之和仓库状态有关联
            EditSkuRatioDto editSkuRatioDto = null;
            if (forbidToRun) {
                editSkuRatioDto = EditSkuRatioDto.of(warehouseNo, goodsOne.getSkuNo(), 0, setInventoryRatio);
            } else if (runToForbid) {
                editSkuRatioDto = EditSkuRatioDto.of(warehouseNo, goodsOne.getSkuNo(), goodsOne.getInventoryRatio(), 0);
            }
            // 状态没有发生改变，并且一直是正常状态
            else {
                if (vwStatus == 0) {
                    editSkuRatioDto = EditSkuRatioDto.of(warehouseNo, goodsOne.getSkuNo(), goodsOne.getInventoryRatio(), setInventoryRatio);
                }
            }
            editSkuRatioDtoList.add(editSkuRatioDto);

            // 处理锁定占比
            if (shareToLock) {
                final BigDecimal availableStock = allSkuAvailableStockMap.getOrDefault(goodsOne.getSkuNo(), BigDecimal.ZERO);
                final Integer lockNum = calculateLockNum(setInventoryRatio, availableStock);
                final LockSkuRatioDto lockSkuRatioDto = LockSkuRatioDto.of(warehouseNo, goodsOne.getSkuNo(),
                        0, setInventoryRatio, 0, lockNum);
                lockSkuRatioDtoList.add(lockSkuRatioDto);
                goodsOne.setInventoryLockNum(lockNum);
                setInventoryRatio2 = BigDecimal.valueOf(setInventoryRatio);
            }
            if (lockToShare) {
                int lockNum = 0;
                final LockSkuRatioDto lockSkuRatioDto = LockSkuRatioDto.of(warehouseNo, goodsOne.getSkuNo(),
                        goodsOne.getInventoryRatio(), 0, goodsOne.getInventoryLockNum(), lockNum);
                lockSkuRatioDtoList.add(lockSkuRatioDto);
                goodsOne.setInventoryLockNum(lockNum);
                setInventoryRatio2 = BigDecimal.valueOf(setInventoryRatio);
            }
            // 编辑锁定模式的数据
            if (editLockData) {
                // 状态从禁止变为运行，抢占锁定占比
                if (forbidToRun) {
                    final BigDecimal availableStock = allSkuAvailableStockMap.getOrDefault(goodsOne.getSkuNo(), BigDecimal.ZERO);
                    int lockNum = calculateLockNum(setInventoryRatio, availableStock);
                    final LockSkuRatioDto lockSkuRatioDto = LockSkuRatioDto.of(warehouseNo, goodsOne.getSkuNo(),
                            0, setInventoryRatio, 0, lockNum);
                    lockSkuRatioDtoList.add(lockSkuRatioDto);
                    goodsOne.setInventoryLockNum(lockNum);
                    setInventoryRatio2 = BigDecimal.valueOf(setInventoryRatio);
                }
                // 状态从禁止变为运行，释放锁定占比
                else if (runToForbid) {
                    int lockNum = 0;
                    final LockSkuRatioDto lockSkuRatioDto = LockSkuRatioDto.of(warehouseNo, goodsOne.getSkuNo(),
                            setInventoryRatio, 0, goodsOne.getInventoryLockNum(), lockNum);
                    lockSkuRatioDtoList.add(lockSkuRatioDto);
                    goodsOne.setInventoryLockNum(lockNum);
                    setInventoryRatio2 = BigDecimal.valueOf(setInventoryRatio);
                }
                // 状态没有发生变化,
                else {
                    // 并且状态一直是正常状态。
                    if (vwStatus == 0) {
                        final BigDecimal availableStock = allSkuAvailableStockMap.getOrDefault(goodsOne.getSkuNo(), BigDecimal.ZERO);
                        int lockNum = calculateLockNum(setInventoryRatio, availableStock);
                        final LockSkuRatioDto lockSkuRatioDto = LockSkuRatioDto.of(warehouseNo, goodsOne.getSkuNo(),
                                goodsOne.getInventoryRatio(), setInventoryRatio, goodsOne.getInventoryLockNum(), lockNum);
                        lockSkuRatioDtoList.add(lockSkuRatioDto);
                        goodsOne.setInventoryLockNum(lockNum);
                        setInventoryRatio2 = BigDecimal.valueOf(setInventoryRatio);
                    }
                }
            }

            goodsOne.setInventoryRatio(setInventoryRatio);
            goodsOne.setInventoryRatio2(setInventoryRatio2);
            updateGoodsList.add(goodsOne);
        }

        // 正常占比处理成功
        editSkuRatioDtoList = editSkuRatioDtoList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(editSkuRatioDtoList);
        // 锁定占比处理成功
        goodsInventoryLockStaticsService.handlerBatch(lockSkuRatioDtoList);

        if (CollUtil.isNotEmpty(updateGoodsList)) {
            iVirtualWarehouseInventoryGoodsService.updateBatchById(updateGoodsList);
        }

        // 刷新其他数据的共享占比
        if (shareToLock || lockToShare || editLockData) {
            warehouseBizService.refreshSharedGoodsInventoryRatio(ListUtil.of(virtualWarehouseInventory.getVirtualWarehouseId()),
                    null, warehouseNo);
        }

    }


    */
/**
     * 根据仓库和SKU编码获取 SKU和实际库存 映射
     *
     * @param warehouseNo
     * @param allSkuCodes
     * @return
     *//*

    private Map<String, BigDecimal> allSkuAvailableStockMap(String warehouseNo, List<String> allSkuCodes) {
        StockSpecQuery stockSpecQuery = new StockSpecQuery();
        stockSpecQuery.setWarehouseNos(ListUtil.of(warehouseNo));
        if (CollUtil.isNotEmpty(allSkuCodes)) {
            stockSpecQuery.setSkuCodes(allSkuCodes);
        }
        stockSpecQuery.setPageIndex(1);
        stockSpecQuery.setPageSize(9999);
        stockSpecQuery.setBusinessLine(ListUtil.of(0, 1, 2, 3));
        PageResponse<AvailableStockSpecVO> stockSpecVoPageResponse = stockSpecBizService.availableStockQuery(stockSpecQuery);
        Assert.isTrue(stockSpecVoPageResponse.isSuccess(), "查询下属SKU可用库存统计的数据失败，仓库：" + warehouseNo);
        return stockSpecVoPageResponse.getData().stream()
                .collect(Collectors.toMap(AvailableStockSpecVO::getSkuCode,
                        AvailableStockSpecVO::getAvailableStock
                        , (a, b) -> a));
    }

    */
/**
     * 数量计算
     *
     * @param ratioVal
     * @param stockNum
     * @return
     *//*

    private Integer calculateLockNum(Integer ratioVal, BigDecimal stockNum) {
        return new BigDecimal(ratioVal).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                .multiply(stockNum).setScale(0, RoundingMode.HALF_UP).intValue();
    }


    // ------------------------- 一级二级占比处理流程，旧版本，废弃，暂时保留 ----------------------------------

    */
/**
     * 一般流程处理 ，二级占比(sku占比)处理。
     *
     * @param allGoodsList
     * @param warehouseNo
     * @param inventoryChange
     * @param newWarehouseInventory
     * @param reqGoodsInventoryList
     *//*

    @Deprecated
    private void basicsSaveWarehouseGoodsInventory(List<VirtualWarehouseInventoryGoods> allGoodsList, String warehouseNo,
                                                   Boolean inventoryChange, Integer newWarehouseInventory,
                                                   List<InventoryGoodsSaveCmd> reqGoodsInventoryList, InventoryMode inventoryMode
    ) {

        Map<String, Integer> allSkuAvailableStockMap = new HashMap<>(128);
        if (InventoryMode.LOCK.equals(inventoryMode)) {
            StockSpecQuery stockSpecQuery = new StockSpecQuery();
            stockSpecQuery.setWarehouseNos(ListUtil.of(warehouseNo));
            List<String> allSkuCodes = allGoodsList.stream()
                    .map(VirtualWarehouseInventoryGoods::getSkuNo).distinct().collect(Collectors.toList());
            stockSpecQuery.setSkuCodes(allSkuCodes);
            PageResponse<StockSpecVO> stockSpecVoPageResponse = stockSpecBizService.query(stockSpecQuery);
            Assert.isTrue(stockSpecVoPageResponse.isSuccess(), "查询下属SKU库存数据失败，仓库：" + warehouseNo);
            allSkuAvailableStockMap = stockSpecVoPageResponse.getData().stream()
                    .collect(Collectors.toMap(StockSpecVO::getSkuCode, val -> val.getAvailableStock().intValue()));
        }

        // Q 两个变量 1.仓库库存占比是否发生变化(1,0)。2.是否有需要特殊处理的SKU占比(1,0)。下面分4种情况分别做处理
        // Q10.仓库占比发生了变化，没有指定SKU占比
        if (inventoryChange && CollUtil.isEmpty(reqGoodsInventoryList)) {
            warehouseGoodsInventory10(allGoodsList, warehouseNo, newWarehouseInventory, inventoryMode, allSkuAvailableStockMap);
        }
        // Q11.仓库占比发生了变化，修改了一些SKU占比。
        if (inventoryChange && !CollUtil.isEmpty(reqGoodsInventoryList)) {
            warehouseGoodsInventory11(allGoodsList, warehouseNo, newWarehouseInventory, reqGoodsInventoryList);
        }
        // Q00.仓库占比没有发生变化，没有指定的SKU占比，数据不变
        if (!inventoryChange && CollUtil.isEmpty(reqGoodsInventoryList)) {
            // SKU 级别的占比不发生任何变化，暂时预留逻辑
            warehouseGoodsInventory00();
        }
        // Q01.仓库占比没有发生变化，修改了一些SKU占比
        if (!inventoryChange && !CollUtil.isEmpty(reqGoodsInventoryList)) {
            warehouseGoodsInventory01(allGoodsList, warehouseNo, reqGoodsInventoryList);
        }
    }

    @Deprecated
    private void warehouseGoodsInventory10(List<VirtualWarehouseInventoryGoods> allGoodsList, String warehouseNo,
                                           Integer newWarehouseInventory, InventoryMode inventoryMode,
                                           Map<String, Integer> allSkuAvailableStockMap) {

        List<EditSkuRatioDto> editSkuRatioDtoList = new LinkedList<>();
        List<VirtualWarehouseInventoryGoods> updateList = new LinkedList<>();
        boolean isLock = InventoryMode.LOCK.equals(inventoryMode);

        for (VirtualWarehouseInventoryGoods goodsOne : allGoodsList) {
            EditSkuRatioDto editSkuRatioDto = new EditSkuRatioDto();
            editSkuRatioDto.setWarehouseNo(warehouseNo);
            editSkuRatioDto.setSkuNo(goodsOne.getSkuNo());
            editSkuRatioDto.setOldVal(goodsOne.getInventoryRatio());
            editSkuRatioDto.setNewVal(newWarehouseInventory);
            editSkuRatioDtoList.add(editSkuRatioDto);

            if (isLock) {
                Integer skuAvailableStock = allSkuAvailableStockMap.getOrDefault(goodsOne.getSkuNo(), 0);
                int inventoryLockNum = (new BigDecimal(newWarehouseInventory).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP))
                        .multiply(new BigDecimal(skuAvailableStock)).setScale(0, RoundingMode.HALF_UP).intValue();
                goodsOne.setInventoryLockNum(inventoryLockNum);
            }

            goodsOne.setInventoryRatio(newWarehouseInventory);
            updateList.add(goodsOne);
        }
        iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(editSkuRatioDtoList);

        if (CollUtil.isNotEmpty(updateList)) {
            iVirtualWarehouseInventoryGoodsService.updateBatchById(updateList);
        }
    }

    @Deprecated
    private void warehouseGoodsInventory11(List<VirtualWarehouseInventoryGoods> allGoodsList, String warehouseNo,
                                           Integer newWarehouseInventory, List<InventoryGoodsSaveCmd> reqGoodsInventoryList) {

        Map<String, InventoryGoodsSaveCmd> newSkuMap = reqGoodsInventoryList.stream()
                .collect(Collectors.toMap(InventoryGoodsSaveCmd::getSkuNo, Function.identity()));
        List<EditSkuRatioDto> editSkuRatioDtoList = new LinkedList<>();
        List<VirtualWarehouseInventoryGoods> updateGoodsList = new LinkedList<>();

        for (VirtualWarehouseInventoryGoods goodsOne : allGoodsList) {
            EditSkuRatioDto editSkuRatioDto = new EditSkuRatioDto();
            editSkuRatioDto.setWarehouseNo(warehouseNo);
            editSkuRatioDto.setSkuNo(goodsOne.getSkuNo());
            editSkuRatioDto.setOldVal(goodsOne.getInventoryRatio());
            InventoryGoodsSaveCmd reqGoodsCmd = newSkuMap.get(goodsOne.getSkuNo());
            if (Objects.nonNull(reqGoodsCmd)) {
                editSkuRatioDto.setNewVal(reqGoodsCmd.getInventoryRatio());
                goodsOne.setInventoryRatio(reqGoodsCmd.getInventoryRatio());
            } else {
                editSkuRatioDto.setNewVal(newWarehouseInventory);
                goodsOne.setInventoryRatio(newWarehouseInventory);
            }
            editSkuRatioDtoList.add(editSkuRatioDto);
            updateGoodsList.add(goodsOne);
        }

        if (CollUtil.isNotEmpty(editSkuRatioDtoList)) {
            iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(editSkuRatioDtoList);
        }
        if (CollUtil.isNotEmpty(updateGoodsList)) {
            iVirtualWarehouseInventoryGoodsService.updateBatchById(updateGoodsList);
        }

    }

    @Deprecated
    private void warehouseGoodsInventory00() {
        // nothing
    }

    @Deprecated
    private void warehouseGoodsInventory01(List<VirtualWarehouseInventoryGoods> allGoodsList, String warehouseNo,
                                           List<InventoryGoodsSaveCmd> reqGoodsInventoryList) {

        Map<String, InventoryGoodsSaveCmd> newSkuMap = reqGoodsInventoryList.stream()
                .collect(Collectors.toMap(InventoryGoodsSaveCmd::getSkuNo, Function.identity()));
        List<EditSkuRatioDto> editSkuRatioDtoList = new LinkedList<>();
        List<VirtualWarehouseInventoryGoods> updateGoodsList = new LinkedList<>();

        for (VirtualWarehouseInventoryGoods goodsOne : allGoodsList) {
            InventoryGoodsSaveCmd goodsSaveCmd = newSkuMap.get(goodsOne.getSkuNo());
            if (Objects.nonNull(goodsSaveCmd)) {
                EditSkuRatioDto editSkuRatioDto = new EditSkuRatioDto();
                editSkuRatioDto.setWarehouseNo(warehouseNo);
                editSkuRatioDto.setSkuNo(goodsOne.getSkuNo());
                editSkuRatioDto.setOldVal(goodsOne.getInventoryRatio());
                editSkuRatioDto.setNewVal(goodsSaveCmd.getInventoryRatio());
                editSkuRatioDtoList.add(editSkuRatioDto);

                goodsOne.setInventoryRatio(goodsSaveCmd.getInventoryRatio());
                updateGoodsList.add(goodsOne);
            }
        }

        if (CollUtil.isNotEmpty(editSkuRatioDtoList)) {
            iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(editSkuRatioDtoList);
        }
        if (CollUtil.isNotEmpty(updateGoodsList)) {
            iVirtualWarehouseInventoryGoodsService.updateBatchById(updateGoodsList);
        }

    }
    // ------------------------- 一级二级占比处理流程，旧版本，废弃，暂时保留 End----------------------------------


    @Override
    public PageResponse<VirtualWarehousePageVo> page(VirtualWarehousePageQuery pageQuery) {
        if (Objects.nonNull(pageQuery.getInventoryMode())) {
            pageQuery.setModeVal(pageQuery.getInventoryMode().getValue());
        }
        Page<VirtualWarehouse> virtualWarehouses = PageHelper.startPage(pageQuery.getPageIndex(), pageQuery.getPageSize())
                .doSelectPage(() -> virtualWarehouseMapper.page(pageQuery));
        if (virtualWarehouses.getTotal() == 0) {
            return ResponseFactory.emptyPage();
        }
        List<VirtualWarehouse> virtualWarehouseList = virtualWarehouses.getResult();

        List<Long> vwIds = virtualWarehouseList.stream().map(VirtualWarehouse::getId).collect(Collectors.toList());
        List<VirtualWarehouseStaticsDto> virtualWarehouseStaticsDtoList = virtualWarehouseInventoryGoodsMapper.quantityStatics(vwIds);
        Map<Long, Integer> warehouseCountMap = virtualWarehouseStaticsDtoList.stream().collect(Collectors
                .toMap(VirtualWarehouseStaticsDto::getVwId, VirtualWarehouseStaticsDto::getCount, (a, b) -> a));
        Integer openDetailModel = pageQuery.getOpenDetailModel();

        List<VirtualWarehousePageVo> collect = virtualWarehouseList.stream()
                .map(virtualWarehouse -> CompletableFuture.supplyAsync(() ->
                        singleHandler(virtualWarehouse, openDetailModel, warehouseCountMap), PAGE_EXECUTOR)
                )
                .map(future -> {
                    try {
                        return future.get(20, TimeUnit.SECONDS);
                    } catch (InterruptedException | ExecutionException | TimeoutException e) {
                        log.error("查选虚拟仓列表异常", e);
                        throw new RuntimeException("查选虚拟仓列表异常." + e.getMessage());
                    }
                }).collect(Collectors.toList());
        return PageResponse.of(collect, (int) virtualWarehouses.getTotal(), virtualWarehouses.getPageSize(), virtualWarehouses.getPageNum());
    }

    private VirtualWarehousePageVo singleHandler(VirtualWarehouse virtualWarehouse, Integer
            openDetailModel, Map<Long, Integer> warehouseCountMap) {
        VirtualWarehousePageVo res = new VirtualWarehousePageVo();
        List<VirtualWarehouseInventory> virtualWarehouseInventories = iVirtualWarehouseInventoryService.lambdaQuery()
                .eq(VirtualWarehouseInventory::getVirtualWarehouseId, virtualWarehouse.getId())
                .list();
        Map<String, WarehouseStockSpecStatistic> statisticMap = new HashMap<>(8);
        if (CollUtil.isNotEmpty(virtualWarehouseInventories)) {
            List<String> warehouseNos = virtualWarehouseInventories.stream().map(VirtualWarehouseInventory::getWarehouseNo).collect(Collectors.toList());
            SingleResponse<Map<String, WarehouseStockSpecStatistic>> statistics = stockSpecBizService.statistics(warehouseNos);
            statisticMap = statistics.getData();
        }

        res.setVirtualWarehouseId(virtualWarehouse.getId());
        res.setVirtualWarehouseNo(virtualWarehouse.getNo());
        res.setVirtualWarehouseName(virtualWarehouse.getName());
        res.setStatus(virtualWarehouse.getStatus());
        res.setBusinessLine(virtualWarehouse.getBusinessLine());
        res.setWarehouseQuantity(warehouseCountMap.getOrDefault(virtualWarehouse.getId(), 0));
        res.setSpuQuantity(statisticMap.values().stream().mapToInt(WarehouseStockSpecStatistic::getSpuNum).sum());
        res.setDetailVoList(ListUtil.empty());
        if (Objects.equals(openDetailModel, 1) && CollUtil.isNotEmpty(virtualWarehouseInventories)) {
            res.setDetailVoList(virtualWarehousePageDetailVo(statisticMap, virtualWarehouseInventories));
        }
        res.setInventoryMode(virtualWarehouse.getInventoryMode());
        return res;
    }

    private List<VirtualWarehousePageDetailVo> virtualWarehousePageDetailVo
            (Map<String, WarehouseStockSpecStatistic> statisticMap,
             List<VirtualWarehouseInventory> virtualWarehouseInventories
            ) {
        return virtualWarehouseInventories.stream().map(val -> {
            VirtualWarehousePageDetailVo detailVo = new VirtualWarehousePageDetailVo();
            detailVo.setId(val.getId());
            detailVo.setWarehouseName(val.getWarehouseName());
            detailVo.setWarehouseNo(val.getWarehouseNo());
            detailVo.setInventoryRatio(val.getInventoryRatio());
            WarehouseStockSpecStatistic warehouseStockSpecStatistic = statisticMap.get(val.getWarehouseNo());
            if (Objects.nonNull(warehouseStockSpecStatistic)) {
                BigDecimal divide = new BigDecimal(val.getInventoryRatio()).divide(new BigDecimal(100), 2, RoundingMode.DOWN);
                detailVo.setAvailableStock(divide.multiply(warehouseStockSpecStatistic.getTotalStock()).setScale(0, RoundingMode.HALF_UP));
                detailVo.setAvailableSendStock(divide.multiply(warehouseStockSpecStatistic.getTotalAvailableSendStock()).setScale(0, RoundingMode.HALF_UP));
                detailVo.setUsableStock(divide.multiply(warehouseStockSpecStatistic.getTotalAvailableStock()).setScale(0, RoundingMode.HALF_UP));
            }
            Optional<Warehouse> warehouse = iWarehouseService.lambdaQuery().eq(Warehouse::getNo, val.getWarehouseNo()).oneOpt();
            warehouse.ifPresent(value -> detailVo.setWarehouseVersion(value.getVersion()));
            return detailVo;
        }).collect(Collectors.toList());
    }


    @Override
    public SingleResponse<VirtualWarehouseViewVo> view(Long id) {
        VirtualWarehouse virtualWarehouse = iVirtualWarehouseService.getById(id);
        Assert.notNull(virtualWarehouse, "虚拟仓查询为空,id非法");

        List<VirtualWarehouseInventory> virtualWarehouseInventories = iVirtualWarehouseInventoryService.lambdaQuery()
                .eq(VirtualWarehouseInventory::getVirtualWarehouseId, virtualWarehouse.getId())
                .list();
        int warehouseQuantity = virtualWarehouseInventories.size();
        VirtualWarehouseViewVo virtualWarehouseViewVo = VirtualWarehouseTransMapper.INSTANCE.entityToViewVo(virtualWarehouse);
        virtualWarehouseViewVo.setWarehouseQuantity(warehouseQuantity);

        StaffInfo staffInfo;
        if (Objects.equals(virtualWarehouse.getUpdatedUid(), 0L)) {
            staffInfo = userGateway.queryStaffInfoById(virtualWarehouse.getCreatedUid());
        } else {
            staffInfo = userGateway.queryStaffInfoById(virtualWarehouse.getUpdatedUid());
        }
        virtualWarehouseViewVo.setUpdatedName(Objects.isNull(staffInfo) ? "" : staffInfo.getNickname());

        String updateTime;
        if (Objects.equals(virtualWarehouse.getUpdatedAt(), 0L)) {
            updateTime = DateUtil.format(virtualWarehouse.getCreatedAt());
        } else {
            updateTime = DateUtil.format(virtualWarehouse.getUpdatedAt());
        }
        virtualWarehouseViewVo.setUpdatedAt(updateTime);

        List<String> warehouseNos = virtualWarehouseInventories.stream().map(VirtualWarehouseInventory::getWarehouseNo).collect(Collectors.toList());
        SingleResponse<Map<String, WarehouseStockSpecStatistic>> statistics = stockSpecBizService.statistics(warehouseNos);
        List<VirtualWarehousePageDetailVo> virtualWarehousePageDetailVos = virtualWarehousePageDetailVo(statistics.getData(), virtualWarehouseInventories);
        virtualWarehouseViewVo.setDetailVoList(virtualWarehousePageDetailVos);

        return SingleResponse.of(virtualWarehouseViewVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<Boolean> updateInventoryRatio(Long id, Integer inventoryRatio) {
        Assert.notNull(id, "库存配置ID不得为空");
        Assert.notNull(inventoryRatio, "库存占比不得为空");
        VirtualWarehouseInventory virtualWarehouseInventory = iVirtualWarehouseInventoryService.getById(id);
        Assert.notNull(virtualWarehouseInventory, "库存配置ID非法");
        VirtualWarehouse virtualWarehouse = iVirtualWarehouseService.getById(virtualWarehouseInventory.getVirtualWarehouseId());
        Assert.notNull(virtualWarehouse, "库存配置ID非法");
        int oldInventoryRatio = virtualWarehouseInventory.getInventoryRatio();

        virtualWarehouseInventory.setInventoryRatio(inventoryRatio);
        iVirtualWarehouseInventoryService.updateById(virtualWarehouseInventory);

        VirtualWarehouseInventoryRes inventoryRes = new VirtualWarehouseInventoryRes();
        inventoryRes.setVirtualWarehouseInventory(virtualWarehouseInventory);
        inventoryRes.setOldWarehouseInventory(oldInventoryRatio);
        inventoryRes.setNewWarehouseInventory(inventoryRatio);
        inventoryRes.setInventoryChange(oldInventoryRatio != inventoryRatio);
        editWarehouseGoodsInventory(inventoryRes,
                new LinkedList<>(), InventoryModelMonitor.NO_CHANGE, VWarehouseStatusMonitor.NO_CHANGE,
                virtualWarehouseInventory.getInventoryMode(), virtualWarehouse.getStatus());

        return SingleResponse.of(true);
    }


    @Override
    public Response export(VirtualWarehousePageQuery pageQuery) {
        ExportManager.ExportHandler exportHandler = pageIndex -> {
            VirtualWarehousePageQuery query = new VirtualWarehousePageQuery();
            query.setPageIndex(pageIndex);
            try {
                return exportVirtualWarehouse(query);
            } catch (Exception e) {
                log.error("虚拟仓导出过程异常.query:{}", JsonUtil.toJson(query), e);
                return ResponseFactory.emptyPage();
            }
        };
        exportManager.export(ExportTaskType.VIRTUAL_WAREHOUSE, VirtualWarehouseSheet.class, exportHandler);
        return Response.buildSuccess();
    }


    private PageResponse<VirtualWarehouseSheet> exportVirtualWarehouse(VirtualWarehousePageQuery pageQuery) {
        pageQuery.setPageSize(100);
        pageQuery.setOpenDetailModel(0);
        PageResponse<VirtualWarehousePageVo> vwPageResponse = page(pageQuery);
        Assert.state(vwPageResponse.isSuccess(), "导出数据查询不得为空");
        if (vwPageResponse.getTotalCount() == 0) {
            return ResponseFactory.emptyPage();
        }
        List<VirtualWarehousePageVo> data = vwPageResponse.getData();

        List<Long> wmIds = data.stream().map(VirtualWarehousePageVo::getVirtualWarehouseId).collect(Collectors.toList());
        List<VirtualWarehouseInventory> virtualWarehouseInventoryList = iVirtualWarehouseInventoryService.lambdaQuery()
                .in(VirtualWarehouseInventory::getVirtualWarehouseId, wmIds).list();
        List<Long> vwInIds = virtualWarehouseInventoryList.stream().map(VirtualWarehouseInventory::getId).collect(Collectors.toList());
        Map<Long, List<VirtualWarehouseInventory>> vwIdAndVwInMap = virtualWarehouseInventoryList.stream()
                .collect(Collectors.groupingBy(VirtualWarehouseInventory::getVirtualWarehouseId));

        List<VirtualWarehouseInventoryGoods> goodsInventoryList = iVirtualWarehouseInventoryGoodsService.lambdaQuery()
                .in(VirtualWarehouseInventoryGoods::getVirtualWarehouseInventoryId, vwInIds).list();

        List<VirtualWarehouseSheet> sheetList = new LinkedList<>();
        vwIdAndVwInMap.forEach((vwId, vwInList) -> {
            List<Long> vwInIdList = vwInList.stream().map(VirtualWarehouseInventory::getId).collect(Collectors.toList());
            List<VirtualWarehouseInventoryGoods> thisVwInGoodsInventoryList = goodsInventoryList.stream()
                    .filter(val -> vwInIdList.contains(val.getVirtualWarehouseInventoryId())).collect(Collectors.toList());
            List<VirtualWarehouseSheet> oneVwExportSheet = oneVirtualWarehouseExportHandler(vwInList, thisVwInGoodsInventoryList);
            if (CollUtil.isNotEmpty(oneVwExportSheet)) {
                sheetList.addAll(oneVwExportSheet);
            }
        });

        return PageResponse.of(sheetList, vwPageResponse.getTotalCount(), vwPageResponse.getPageSize(), vwPageResponse.getPageIndex());
    }

    private List<VirtualWarehouseSheet> oneVirtualWarehouseExportHandler
            (List<VirtualWarehouseInventory> virtualWarehouseInventoryList,
             List<VirtualWarehouseInventoryGoods> goodsInventoryList) {
        List<String> realWarehouseNos = virtualWarehouseInventoryList.stream().map(VirtualWarehouseInventory::getWarehouseNo).collect(Collectors.toList());
        Map<String, VirtualWarehouseInventory> virtualWarehouseInventoryMap = virtualWarehouseInventoryList.stream()
                .collect(Collectors.toMap(VirtualWarehouseInventory::getWarehouseNo, Function.identity()));

        Map<Long, List<VirtualWarehouseInventoryGoods>> vwInIdAndGoodsInventoryMap = goodsInventoryList.stream()
                .collect(Collectors.groupingBy(VirtualWarehouseInventoryGoods::getVirtualWarehouseInventoryId));

        StockSpecQuery query = new StockSpecQuery();
        query.setWarehouseNos(realWarehouseNos);
        query.setPageSize(9999);
        query.setBusinessLine(ListUtil.of(BusinessLine.DIAN_SHANG.getValue(), BusinessLine.CHOU_JIAN.getValue(),
                BusinessLine.LV_SE_JIA_ZHUANG.getValue(), BusinessLine.SHANG_JIA_RU_ZHU.getValue()));
        PageResponse<StockSpecVO> pageResponse = stockSpecBizService.query(query);
        if (!pageResponse.isSuccess()) {
            log.error("虚拟仓导出，数据查询异常。{}", pageResponse.getErrMessage());
            return new LinkedList<>();
        }
        List<StockSpecVO> stockSpecVOList = pageResponse.getData();
        if (CollUtil.isEmpty(stockSpecVOList)) {
            return new LinkedList<>();
        }

        return stockSpecVOList.stream().map(stockSpecVO -> {
            VirtualWarehouseSheet virtualWarehouseSheet = new VirtualWarehouseSheet();
            virtualWarehouseSheet.setWarehouseName(stockSpecVO.getWarehouseName());
            virtualWarehouseSheet.setSkuNo(stockSpecVO.getSkuCode());
            virtualWarehouseSheet.setSpuNo(stockSpecVO.getSpuCode());
            virtualWarehouseSheet.setBarCode(stockSpecVO.getBarCode());
            virtualWarehouseSheet.setItemName(stockSpecVO.getSpuName());
            virtualWarehouseSheet.setSpecName(stockSpecVO.getSkuName());
            virtualWarehouseSheet.setBrand(stockSpecVO.getBrandName());
            virtualWarehouseSheet.setCategory(stockSpecVO.getCategoryName());

            AtomicReference<Integer> inventoryRatio = new AtomicReference<>(0);
            VirtualWarehouseInventory inventoryVo = virtualWarehouseInventoryMap.get(stockSpecVO.getWarehouseNo());
            if (Objects.nonNull(inventoryVo)) {
                inventoryRatio.set(inventoryVo.getInventoryRatio());
                Long inventoryVoId = inventoryVo.getId();
                List<VirtualWarehouseInventoryGoods> goodsList = vwInIdAndGoodsInventoryMap.get(inventoryVoId);
                if (CollUtil.isNotEmpty(goodsList)) {
                    goodsList.stream().filter(goodsVo -> goodsVo.getSkuNo().equals(stockSpecVO.getSkuCode())).findFirst()
                            .ifPresent(goodsVo -> inventoryRatio.set(goodsVo.getInventoryRatio()));
                }
            }
            virtualWarehouseSheet.setInventoryRatio(inventoryRatio.get().toString());
            BigDecimal inventoryRatioVal = new BigDecimal(inventoryRatio.get()).divide(ONE_HUNDRED, 2, RoundingMode.DOWN);

            virtualWarehouseSheet.setUsableStock(inventoryRatioVal.multiply(stockSpecVO.getAvailableStock()).setScale(0, RoundingMode.HALF_UP).toString());
            virtualWarehouseSheet.setAvailableSendStock(inventoryRatioVal.multiply(stockSpecVO.getAvailableSendStock()).setScale(0, RoundingMode.HALF_UP).toString());
            virtualWarehouseSheet.setAvailableStock(inventoryRatioVal.multiply(stockSpecVO.getStockNum()).setScale(0, RoundingMode.HALF_UP).toString());

            // 导出字段添加合作方
            virtualWarehouseSheet.setCoryType(stockSpecVO.getCoryTypeStr());

            return virtualWarehouseSheet;
        }).collect(Collectors.toList());
    }


    @Override
    public MultiResponse<OperateLog> operateLog(Long id) {
        List<OperateLog> operateLogs = operateLogGateway.getOperateLogs(OperateLogTarget.VIRTUAL_WAREHOUSE, id);
        return MultiResponse.of(operateLogs);
    }


    @Override
    public MultiResponse<WarehouseDO> warehouseNos(Long wvId) {
        List<WarehouseDO> collect = iVirtualWarehouseInventoryService.lambdaQuery().eq(VirtualWarehouseInventory::getVirtualWarehouseId, wvId)
                .list().stream().map(val -> {
                    WarehouseDO warehouseDO = new WarehouseDO();
                    warehouseDO.setNo(val.getWarehouseNo());
                    warehouseDO.setName(val.getWarehouseName());
                    return warehouseDO;
                }).collect(Collectors.toList());
        return MultiResponse.of(collect);
    }


    @Override
    public Response preDataProcessing() {
        final List<VirtualWarehouseInventory> virtualWarehouseInventoryList = iVirtualWarehouseInventoryService.lambdaQuery().list();
        if (CollUtil.isEmpty(virtualWarehouseInventoryList)) {
            return Response.buildSuccess();
        }

        List<VirtualWarehouseInventoryGoods> addGoodsList = new LinkedList<>();

        final List<Long> inventoryIdList = virtualWarehouseInventoryList.stream()
                .map(VirtualWarehouseInventory::getId).collect(Collectors.toList());
        final Map<Long, List<VirtualWarehouseInventoryGoods>> inventoryGoodsListMap = iVirtualWarehouseInventoryGoodsService.lambdaQuery()
                .in(VirtualWarehouseInventoryGoods::getVirtualWarehouseInventoryId, inventoryIdList)
                .list().stream().collect(Collectors.groupingBy(VirtualWarehouseInventoryGoods::getVirtualWarehouseInventoryId));

        virtualWarehouseInventoryList.forEach(inventory -> {
            List<WdtStockSpec> thisWarehouseAllSkuList = iWdtStockSpecService.lambdaQuery()
                    .eq(WdtStockSpec::getWarehouseNo, inventory.getWarehouseNo())
                    .eq(WdtStockSpec::getDefect, 0)
                    .list()
                    .stream().distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(thisWarehouseAllSkuList)) {
                List<WdtStockSpec> addSkuGoodsList = new LinkedList<>();
                final List<VirtualWarehouseInventoryGoods> goodsList = inventoryGoodsListMap.get(inventory.getId());
                final Set<String> goodsSkuCodeSet = CollUtil.isNotEmpty(goodsList) ? goodsList.stream()
                        .map(VirtualWarehouseInventoryGoods::getSkuNo).collect(Collectors.toSet())
                        : new LinkedHashSet<>();
                for (WdtStockSpec wdtStockSpec : thisWarehouseAllSkuList) {
                    if (!goodsSkuCodeSet.contains(wdtStockSpec.getSpecNo())) {
                        addSkuGoodsList.add(wdtStockSpec);
                    }
                }
                if (CollUtil.isNotEmpty(addSkuGoodsList)) {
                    final Set<VirtualWarehouseInventoryGoods> needAddGoodsSet = addSkuGoodsList.stream().map(wdtStockSpec -> {
                        VirtualWarehouseInventoryGoods goods = new VirtualWarehouseInventoryGoods();
                        goods.setWarehouseNo(inventory.getWarehouseNo());
                        goods.setSpuNo(wdtStockSpec.getGoodsNo());
                        goods.setSkuNo(wdtStockSpec.getSpecNo());
                        goods.setInventoryRatio(inventory.getInventoryRatio());
                        goods.setVirtualWarehouseId(inventory.getVirtualWarehouseId());
                        goods.setVirtualWarehouseNo(inventory.getVirtualWarehouseNo());
                        goods.setVirtualWarehouseInventoryId(inventory.getId());
                        return goods;
                    }).collect(Collectors.toSet());
                    addGoodsList.addAll(needAddGoodsSet);
                }
            }
        });

        if (CollUtil.isNotEmpty(addGoodsList)) {
            iVirtualWarehouseInventoryGoodsService.saveBatch(addGoodsList);
        }

        return Response.buildSuccess();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clearTestData(String vwNo) {
        final List<VirtualWarehouse> vwList = iVirtualWarehouseService.lambdaQuery()
                .eq(VirtualWarehouse::getNo, vwNo).list();
        if (CollUtil.isEmpty(vwList)) {
            return;
        }

        final VirtualWarehouse virtualWarehouse = vwList.get(0);


//        clearShopDateHandler(virtualWarehouse);

        // if this vw associated with a shop ,first remove the key relationship
        final List<ShopInventory> list = shopInventoryService.lambdaQuery()
                .eq(ShopInventory::getWarehouseNo, virtualWarehouse.getNo()).list();
        Assert.isTrue(CollUtil.isEmpty(list),
                StrUtil.format("please remove the association with shop.shopId:{}",
                        list.stream().map(val -> String.valueOf(val.getShopId())).collect(Collectors.joining(","))));

        // ------------------------

        cleanVwDataHandler(virtualWarehouse);
    }


    */
/**
     * clear shop data associated with virtualWarehouse
     *//*

    private void clearShopDateHandler(VirtualWarehouse virtualWarehouse) {
        // if this vw in some shop
        final List<ShopInventory> list = shopInventoryService.lambdaQuery()
                .eq(ShopInventory::getWarehouseNo, virtualWarehouse.getNo()).list();
        for (ShopInventory shopInventory : list) {
            iWarehouseGoodsInventoryStaticsService.addWarehouseRatio(virtualWarehouse.getNo(), 0, shopInventory.getInventoryRatio());

            final List<ShopInventoryGoods> goodsList = shopInventoryGoodsService.lambdaQuery()
                    .in(ShopInventoryGoods::getShopInventoryId, shopInventory.getId())
                    .list();
            List<EditSkuRatioDto> editSkuRatioDtoList = new LinkedList<>();
            List<LockSkuRatioDto> lockSkuRatioDtoList = new LinkedList<>();
            for (ShopInventoryGoods goods : goodsList) {
                final EditSkuRatioDto editSkuRatioDto = EditSkuRatioDto.of(virtualWarehouse.getNo(), goods.getSkuNo(),
                        goods.getInventoryRatio(), 0);
                editSkuRatioDtoList.add(editSkuRatioDto);
                final LockSkuRatioDto lockSkuRatioDto = LockSkuRatioDto.of(virtualWarehouse.getNo(), goods.getSkuNo(),
                        goods.getInventoryRatio(), 0, goods.getInventoryLockNum(), 0);
                lockSkuRatioDtoList.add(lockSkuRatioDto);
            }
            iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(editSkuRatioDtoList);
            goodsInventoryLockStaticsService.handlerBatch(lockSkuRatioDtoList);


            if (shopInventory.getInventoryMode().equals(InventoryMode.LOCK)) {
                warehouseBizService.refreshSharedGoodsInventoryRatio(null,
                        ListUtil.of(shopInventory.getShopId()), virtualWarehouse.getNo());
            }

            shopInventoryGoodsService.lambdaUpdate()
                    .in(ShopInventoryGoods::getShopInventoryId, shopInventory.getId())
                    .remove();

            shopInventoryService.removeById(shopInventory);
        }
    }


    */
/**
     * clear virtualWarehouse data
     *
     * @param virtualWarehouse
     *//*

    private void cleanVwDataHandler(VirtualWarehouse virtualWarehouse) {

        final List<VirtualWarehouseInventory> virtualWarehouseInventoryList = iVirtualWarehouseInventoryService.lambdaQuery()
                .eq(VirtualWarehouseInventory::getVirtualWarehouseId, virtualWarehouse.getId())
                .list();
        for (VirtualWarehouseInventory warehouseInventory : virtualWarehouseInventoryList) {
            iWarehouseGoodsInventoryStaticsService.addWarehouseRatio(warehouseInventory.getWarehouseNo(), 0, warehouseInventory.getInventoryRatio());
            // -----------  remove warehouse data finish ----------------

            final List<VirtualWarehouseInventoryGoods> goodsList = iVirtualWarehouseInventoryGoodsService.lambdaQuery()
                    .in(VirtualWarehouseInventoryGoods::getVirtualWarehouseInventoryId, warehouseInventory.getId())
                    .list();
            List<EditSkuRatioDto> editSkuRatioDtoList = new LinkedList<>();
            List<LockSkuRatioDto> lockSkuRatioDtoList = new LinkedList<>();
            for (VirtualWarehouseInventoryGoods goods : goodsList) {
                final EditSkuRatioDto editSkuRatioDto = EditSkuRatioDto.of(warehouseInventory.getWarehouseNo(), goods.getSkuNo(),
                        goods.getInventoryRatio(), 0);
                editSkuRatioDtoList.add(editSkuRatioDto);
                final LockSkuRatioDto lockSkuRatioDto = LockSkuRatioDto.of(warehouseInventory.getWarehouseNo(), goods.getSkuNo(),
                        goods.getInventoryRatio(), 0, goods.getInventoryLockNum(), 0);
                lockSkuRatioDtoList.add(lockSkuRatioDto);
            }
            iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(editSkuRatioDtoList);
            goodsInventoryLockStaticsService.handlerBatch(lockSkuRatioDtoList);

            // ---------- remove this warehouse all sku data finish --------------

            // if vw is lock mode,refresh other warehouse data
            if (InventoryMode.LOCK.equals(virtualWarehouse.getInventoryMode())) {
                warehouseBizService.refreshSharedGoodsInventoryRatio(ListUtil.of(virtualWarehouse.getId()),
                        null, warehouseInventory.getWarehouseNo());
            }

            iVirtualWarehouseInventoryGoodsService.lambdaUpdate()
                    .in(VirtualWarehouseInventoryGoods::getVirtualWarehouseInventoryId, warehouseInventory.getId())
                    .remove();
            iVirtualWarehouseInventoryService.removeById(warehouseInventory);
        }

        iWarehouseService.lambdaUpdate()
                .eq(Warehouse::getNo, virtualWarehouse.getNo())
                .remove();
        iVirtualWarehouseService.removeById(virtualWarehouse.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recalculateGlobalRatioExclusionLock() {
        // 清空一级占比，二级占比统计数据
        iWarehouseService.lambdaUpdate()
                .set(Warehouse::getInventoryRatio, 0)
                .ge(Warehouse::getId, 0)
                .update();
        iWarehouseGoodsInventoryStaticsService.lambdaUpdate()
                .ne(WarehouseGoodsInventoryStatics::getId, 0)
                .remove();

        List<EditSkuRatioDto> editSkuRatioDtoList = new LinkedList<>();

        // 重新计算虚拟仓部分数据
        final List<VirtualWarehouseInventory> virtualWarehouseInventoryList = iVirtualWarehouseInventoryService.lambdaQuery()
                .eq(VirtualWarehouseInventory::getInventoryMode, InventoryMode.SHARED)
                .list();
        for (VirtualWarehouseInventory virtualWarehouseInventory : virtualWarehouseInventoryList) {
            // 一级占比处理
            iWarehouseGoodsInventoryStaticsService.addWarehouseRatio(virtualWarehouseInventory.getWarehouseNo(),
                    virtualWarehouseInventory.getInventoryRatio(), 0);

            // 二级占比处理
            final List<VirtualWarehouseInventoryGoods> goodsList = iVirtualWarehouseInventoryGoodsService.lambdaQuery()
                    .eq(VirtualWarehouseInventoryGoods::getVirtualWarehouseInventoryId, virtualWarehouseInventory.getId())
                    .list();
            for (VirtualWarehouseInventoryGoods inventoryGoods : goodsList) {
                final EditSkuRatioDto editSkuRatioDto = EditSkuRatioDto.of(inventoryGoods.getWarehouseNo(), inventoryGoods.getSkuNo(),
                        0, inventoryGoods.getInventoryRatio());
                editSkuRatioDtoList.add(editSkuRatioDto);
            }
        }

        // 计算店铺部分数据
        final List<ShopInventory> shopInventoryList = shopInventoryService.lambdaQuery()
                .eq(ShopInventory::getInventoryMode, InventoryMode.SHARED)
                .list();
        for (ShopInventory shopInventory : shopInventoryList) {
            iWarehouseGoodsInventoryStaticsService.addWarehouseRatio(shopInventory.getWarehouseNo(),
                    shopInventory.getInventoryRatio(), 0);

            final List<ShopInventoryGoods> goodsList = shopInventoryGoodsService.lambdaQuery()
                    .eq(ShopInventoryGoods::getShopInventoryId, shopInventory.getId())
                    .list();
            for (ShopInventoryGoods shopInventoryGoods : goodsList) {
                final EditSkuRatioDto editSkuRatioDto = EditSkuRatioDto.of(shopInventoryGoods.getWarehouseNo(), shopInventoryGoods.getSkuNo(),
                        0, shopInventoryGoods.getInventoryRatio());
                editSkuRatioDtoList.add(editSkuRatioDto);
            }
        }

        iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(editSkuRatioDtoList);

    }

    @Override
    public void removeLockStatics() {
        goodsInventoryLockStaticsService.lambdaUpdate()
                .ge(WarehouseGoodsInventoryLockStatics::getId, 0L)
                .remove();
    }
}
*/
