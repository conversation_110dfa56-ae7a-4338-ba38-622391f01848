//package com.daddylab.supplier.item.application.message.wechat.v2.base;
//
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.cloud.context.config.annotation.RefreshScope;
//import org.springframework.context.annotation.Configuration;
//
///**
// * <AUTHOR> up
// * @date 2024年05月14日 7:11 PM
// */
//@Data
//@Configuration
//@ConfigurationProperties(prefix = "msg-config")
//@RefreshScope
//public class MsgParamConfig {
//
//    /**
//     * 运营管理-新品商品库-link
//     */
//    private String newGoodsLink;
//
//    /**
//     * 运营管理-商品优化-link
//     */
//    private String itemOptimizeLink;
//
//    private String
//}
