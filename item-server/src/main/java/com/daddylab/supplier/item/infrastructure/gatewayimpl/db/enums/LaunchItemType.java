package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品上新类型（外包、内部）
 *
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum LaunchItemType implements IEnum<Integer> {
    /**
     * 内部
     */
    INNER(1, "内部"),
    /**
     * 外包
     */
    OUTSOURCE(2, "外包"),
    ;

    @EnumValue
    private final Integer value;
    private final String desc;

}
