package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2022年11月16日 11:56 AM
 */
@Data
public class ActivityTimeSuitePriceDO {

    /**
     * 组合装编号
     */
    private String suiteNo;

    /**
     * 组合装成本价
     */
    private BigDecimal priceCost;

    /**
     * 此价开始执行时间，结束时间
     */
    private Long startTime;
    private Long endTime;

    private Integer platformType;
}
