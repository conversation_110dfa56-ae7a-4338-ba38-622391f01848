package com.daddylab.supplier.item.controller.provider.dto;

import com.daddylab.supplier.item.common.trans.CommaSerialTransMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ProviderStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ProviderType;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.PropertyName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/19 18:21
 * @description ProviderVO
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ProviderVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @PropertyName("供应商id")
    private Long id;

    /**
     * 供应商名称
     */
    @PropertyName("供应商名称")
    private String name;

    /**
     * 联系人
     */
    @PropertyName("联系人")
    private String contact;

    /**
     * 联系方式
     */
    @PropertyName("联系方式")
    private String contactMobile;

    /**
     * 统一社会信用代码
     */
    @PropertyName("统一社会信用代码")
    private String unifySocialCreditCodes;

    /**
     * 联系地址 省
     */
    @PropertyName("省")
    private String provinceCode;

    /**
     * 联系地址 市
     */
    @PropertyName("市")
    private String cityCode;

    /**
     * 联系地址 区/县
     */
    @PropertyName("区/县")
    private String areaCode;

    /**
     * 联系地址 区/县
     */
    @PropertyName("三级地区码转描述")
    private String codeStr;

    /**
     * 联系地址 详细地址
     */
    @PropertyName("详细地址")
    private String address;

    /**
     * 状态 1:合作 2:停用
     */
    @PropertyName("状态")
    private ProviderStatus status;

    /**
     * 供应商类型 1:自营 2:代发
     */
    @PropertyName("供应商类型")
    private ProviderType type;

    /**
     * 关联合作伙伴系统中的供应商ID，没有则为0
     */
    private Long partnerProviderId;

    @PropertyName("此供应商是否来自于合作伙伴系统")
    private Boolean isFromPartner;

    /**
     * 供应商编号
     */
    @PropertyName("供应商编号")
    private String providerNo;

    /**
     * 发票类型
     */
    @PropertyName("发票类型")
    private Integer invoiceType;

    /**
     * 结算币别
     */
    @PropertyName("结算币别")
    private Integer currency;

    /**
     * 税分类
     */
    @PropertyName("税分类")
    private Integer taxType;

    /**
     * 税率
     */
    @PropertyName("税率")
    private BigDecimal taxRate;

    /**
     * 银行账户
     */
    @PropertyName("银行账户")
    private List<BankAccountVO> bankAccountVOList;

    @PropertyName("负责人")
    private Long mainChargerUserId;

    @PropertyName("负责人")
    private String mainChargerName;


    @PropertyName("次要负责人")
    private Long secondChargerUserId;

    @PropertyName("次要负责人")
    private String secondChargerName;

    @DiffIgnore
    @ApiModelProperty("商家后台店铺ID")
    private String mallShopId;

    @ApiModelProperty("是否黑名单 0否 1是")
    private Integer isBlacklist;

    @ApiModelProperty("合作方")
    private List<Integer> corpType;

    @ApiModelProperty("业务类型")
    private List<Integer> bizType;

}
