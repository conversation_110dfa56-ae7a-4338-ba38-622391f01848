package com.daddylab.supplier.item.application.tmpJob;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CombinationItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BusinessLine;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICombinationItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemCodeRefService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.utils.IOUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/12/6
 */
@Service
@Slf4j
public class ImportCommissionJob {
  @Data
  public static class SkuSheetVO {

    @ExcelProperty("SKU编码")
    private String skuCode;

    @ExcelProperty("合同销售价")
    private String contractSalePrice;

    @ExcelProperty("平台佣金")
    private String platformCommission;

    @ExcelProperty("模式")
    private String businessLine;
  }

  @Resource IItemCodeRefService itemCodeRefService;
  @Resource private IItemSkuService itemSkuService;
  @Resource private IItemService itemService;
  @Resource private OperateLogDomainService operateLogDomainService;
  @Resource private ICombinationItemService combinationItemService;

  @SneakyThrows
  public void imports(InputStream inputStream) {
    final byte[] bytes = IOUtil.readBytes(inputStream);
    final List<SkuSheetVO> rows =
        EasyExcel.read(new ByteArrayInputStream(bytes))
            .head(SkuSheetVO.class)
            .ignoreEmptyRow(true)
            .sheet(0)
            .doReadSync();
    if (!rows.isEmpty()) {
      int i = 1;
      for (SkuSheetVO skuSheetVO : rows) {
        try {
          i++;
          final String skuCode = skuSheetVO.getSkuCode();
          if (StringUtil.isBlank(skuCode)) {
            log.warn("[导入佣金] 导入失败，SKU编码为空:{}", i);
            continue;
          }
          final CombinationItem combinationItem = combinationItemService.getByItemCode(skuCode);
          if (combinationItem != null) {
            log.info("[导入佣金][组合编码] 正在处理 {}:{}", i, JsonUtil.toJson(skuSheetVO));
            final ArrayList<String> logs = new ArrayList<>();
            final BusinessLine businessLine0 =
                Optional.ofNullable(combinationItem.getBusinessLine())
                    .map(mode -> IEnum.getEnumByValue(BusinessLine.class, mode))
                    .orElse(null);
            Optional.ofNullable(skuSheetVO.getBusinessLine())
                .filter(StringUtil::isNotBlank)
                .flatMap(
                    inputBusinessLine ->
                        Arrays.stream(inputBusinessLine.split("\\+"))
                            .filter(StringUtil::isNotBlank)
                            .map(mode -> IEnum.getEnumByDesc(BusinessLine.class, mode))
                            .findFirst())
                .ifPresent(
                    businessLine -> {
                      if (!businessLine.equals(businessLine0)) {
                        combinationItem.setBusinessLine(businessLine.getValue());
                        logs.add(
                            String.format(
                                "合作模式从 %s 改为 %s",
                                Optional.ofNullable(businessLine0)
                                    .map(BusinessLine::getDesc)
                                    .orElse("/"),
                                businessLine.getDesc()));
                      }
                    });

            final BigDecimal contractSalePrice0 = combinationItem.getContractSalePrice();
            if (StringUtil.isNotBlank(skuSheetVO.getContractSalePrice())) {
              final BigDecimal contractSalePrice =
                  new BigDecimal(skuSheetVO.getContractSalePrice());
              if (contractSalePrice0 == null
                  || contractSalePrice0.compareTo(contractSalePrice) != 0) {
                combinationItem.setContractSalePrice(contractSalePrice);
                logs.add(String.format("合同销售价从 %s 改为 %s", contractSalePrice0, contractSalePrice));
              }
            }

            final BigDecimal platformCommission0 = combinationItem.getPlatformCommission();
            if (StringUtil.isNotBlank(skuSheetVO.getPlatformCommission())) {
              final BigDecimal platformCommission =
                  new BigDecimal(StringUtil.removeSuffix(skuSheetVO.getPlatformCommission(), "%"));
              if (platformCommission0 == null
                  || platformCommission0.compareTo(platformCommission) != 0) {
                combinationItem.setPlatformCommission(platformCommission);
                logs.add(String.format("平台佣金从 %s 改为 %s", platformCommission0, platformCommission));
              }
            }
            if (!logs.isEmpty()) {
              combinationItemService.updateById(combinationItem);
              final String logMsg =
                  String.format(
                      "[导入佣金][组合编码]导入成功，将组合品 %s id=%s 的%s",
                      skuCode, combinationItem.getId(), String.join("、", logs));
              log.info(logMsg);
              operateLogDomainService.addOperatorLog(
                  0L, OperateLogTarget.COMBINATION_ITEM, combinationItem.getId(), logMsg);
            } else {
              log.info("[导入佣金][组合编码]组合品 {} id={} 无需更新", skuCode, combinationItem.getId());
            }
          } else {
            log.info("[导入佣金][单SKU] 正在处理 {}:{}", i, JsonUtil.toJson(skuSheetVO));
            final ItemSku itemSku = itemSkuService.getByMixCode(skuCode).orElse(null);
            if (itemSku == null) {
              log.warn("[导入佣金][单SKU] 导入失败，SKU编码={} 关联的SKU不存在", skuCode);
              continue;
            }
            final Long itemId = itemSku.getItemId();
            if (StringUtil.isNotBlank(skuSheetVO.getBusinessLine())) {
              final Item item = itemService.getById(itemId);
              final String inputBusinessLine = skuSheetVO.getBusinessLine();
              final List<BusinessLine> businessLineEnums =
                  Arrays.stream(inputBusinessLine.split("\\+"))
                      .filter(StringUtil::isNotBlank)
                      .map(mode -> IEnum.getEnumByDesc(BusinessLine.class, mode))
                      .collect(Collectors.toList());
              final String businessLines0 = item.getBusinessLines();
              item.setBusinessLine(businessLineEnums.get(0).getValue());
              final String businessLines =
                  businessLineEnums.stream()
                      .map(BusinessLine::getValue)
                      .map(Object::toString)
                      .collect(Collectors.joining(","));
              item.setBusinessLines(businessLines);
              itemService.updateById(item);
              final String itemOpLog =
                  String.format(
                      "[导入佣金][单SKU] 导入成功，商品ID=%s 合作模式从 %s 改为 %s",
                      itemId, businessLines0, businessLines);
              log.info(itemOpLog);
              operateLogDomainService.addOperatorLog(
                  0L, OperateLogTarget.ITEM, item.getId(), itemOpLog);
            }

            final BigDecimal contractSalePrice0 = itemSku.getContractSalePrice();
            final BigDecimal platformCommission0 = itemSku.getPlatformCommission();
            final ArrayList<String> logs = new ArrayList<>();
            if (StringUtil.isNotBlank(skuSheetVO.getContractSalePrice())) {
              final BigDecimal contractSalePrice =
                  new BigDecimal(skuSheetVO.getContractSalePrice());
              if (contractSalePrice0 == null
                  || contractSalePrice0.compareTo(contractSalePrice) != 0) {
                itemSku.setContractSalePrice(contractSalePrice);
                logs.add(
                    String.format(
                        "合同销售价从 %s 改为 %s", contractSalePrice0, itemSku.getContractSalePrice()));
              }
            }
            if (StringUtil.isNotBlank(skuSheetVO.getPlatformCommission())) {
              final BigDecimal platformCommission =
                  new BigDecimal(StringUtil.removeSuffix(skuSheetVO.getPlatformCommission(), "%"));
              if (platformCommission0 == null
                  || platformCommission0.compareTo(platformCommission) != 0) {
                itemSku.setPlatformCommission(platformCommission);
                logs.add(
                    String.format(
                        "平台佣金从 %s 改为 %s", platformCommission0, itemSku.getPlatformCommission()));
              }
            }
            if (!logs.isEmpty()) {
              itemSkuService.updateById(itemSku);
              final String skuOpLog =
                  String.format(
                      "[导入佣金][单SKU] 导入成功，将后端商品SKU %s skuId=%s 的%s",
                      itemSku.getSkuCode(), itemSku.getId(), String.join("、", logs));
              log.info(skuOpLog);
              operateLogDomainService.addOperatorLog(0L, OperateLogTarget.ITEM, itemId, skuOpLog);
            } else {
              log.warn(
                  "[导入佣金][单SKU] 后端商品SKU={} skuId={} 无需修改", itemSku.getSkuCode(), itemSku.getId());
            }
          }
        } catch (Exception e) {
          log.error("[导入佣金][单SKU] 导入异常", e);
        }
      }
    }
  }
}
