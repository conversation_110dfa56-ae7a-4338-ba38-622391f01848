package com.daddylab.supplier.item.application.item.tasks;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.goods.GoodsAPI;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsPushGoodsInfo;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsPushSpecInfo;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Category;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICategoryService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import java.io.Serializable;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

/**
 * <AUTHOR>
 * @since 2022/10/18
 */
@Service
@AllArgsConstructor
@Slf4j
public class ItemCategoryPushWdtService {

    private final IItemService itemService;
    private final IItemSkuService itemSkuService;
    private final ICategoryService categoryService;
    private final WdtGateway wdtGateway;
    private final WdtConfig wdtConfig;
    private final Cache<Integer, Boolean> temporaryDisabledConfigs = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofSeconds(30)).build();


    @Data
    @HeadRowHeight(20)
    @ContentRowHeight(16)
    @EqualsAndHashCode(of = {"itemCode"})
    public static class ImportRow implements Serializable {

        private static final long serialVersionUID = -752114031599220390L;

        @ExcelProperty(value = "商品编码SPU")
        @ColumnWidth(50)
        private String itemCode;
    }

    @Trace(operationName = "ItemCategoryPushWdtService.pushAll")
    public void pushAll() {
        final List<Item> items = itemService.lambdaQuery().list();
        push(items);
    }

    @Trace(operationName = "ItemCategoryPushWdtService.pushBatchByCode")
    public void pushBatchByCode(List<String> itemCodes) {
        push(itemService.lambdaQuery().in(Item::getCode, itemCodes).or()
                .in(Item::getProviderSpecifiedCode, itemCodes).list());
    }

    private void push(List<Item> items) {
        if (CollectionUtil.isEmpty(items)) {
            return;
        }
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        log.info("开始推送导入的商品编码的品类信息至旺店通 总计:{}", items.size());
        for (int i = 0; i < items.size(); i++) {
            Integer configIdxSelected = null;
            for (int configIdx = 0; configIdx < wdtConfig.getConfigs().size(); configIdx++) {
                if (Objects.nonNull(temporaryDisabledConfigs.getIfPresent(configIdx))) {
                    continue;
                }
                configIdxSelected = configIdx;
            }
            if (null == configIdxSelected) {
                log.info("当前无配置可用，线程暂时休眠30S");
                ThreadUtil.sleep(30 * 1000);
                continue;
            }
            final Item item = items.get(i);
            log.info("开始推送第{}/{}个商品，商品ID:{}，商品编码:{}", i + 1, items.size(), item.getId(),
                    item.getCode());
            final GoodsAPI api = wdtGateway.getAPI(GoodsAPI.class, configIdxSelected, true);
            final ArrayList<GoodsPushSpecInfo> specInfoList = Lists.newArrayList();
            final GoodsPushGoodsInfo goodsInfo = new GoodsPushGoodsInfo();
            goodsInfo.setGoodsNo(item.getSupplierCode());
            goodsInfo.setGoodsName(item.getName());
            final Category category = categoryService.getById(item.getCategoryId());
            if (Objects.isNull(category)) {
                log.error("未查询到商品关联品类，品类ID:{}，商品ID:{}", item.getCategoryId(), item.getId());
                continue;
            }
            goodsInfo.setClassName(category.getName());
            final List<ItemSku> itemSkuList = itemSkuService.lambdaQuery()
                    .eq(ItemSku::getItemId, item.getId()).list();
            if (itemSkuList.isEmpty()) {
                log.error("商品规格为空，商品ID:{}", item.getId());
                continue;
            }
            for (ItemSku sku : itemSkuList) {
                final GoodsPushSpecInfo specInfo = new GoodsPushSpecInfo();
                specInfo.setSpecNo(sku.getSupplierCode());
                specInfo.setSpecName(sku.getSpecifications());
                specInfoList.add(specInfo);
            }
            try {
                final Long wdtGoodsId = api.push(goodsInfo, specInfoList);
                log.info("推送成功:{}，商品ID:{}，商品编码:{}", wdtGoodsId, item.getId(), item.getCode());
            } catch (WdtErpException e) {
                log.error("商品推送品类至旺店通失败，错误:{}，商品ID:{}，商品编码:{}", e.getMessage(), item.getId(),
                        item.getCode());
                if (WdtExceptions.isLimitExceed(e)) {
                    i--;
                    temporaryDisabledConfigs.put(configIdxSelected, true);
                }
            }
        }
        stopWatch.stop();
        log.info("推送导入的商品编码的品类信息至旺店通完成 耗时:{}ms", stopWatch.getTotalTimeMillis());
    }
}
