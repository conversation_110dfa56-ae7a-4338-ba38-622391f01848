package com.daddylab.supplier.item.domain.brand.dto;

import com.alibaba.cola.dto.Command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11 9:57 上午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("品牌下拉列表查询")
public class BrandDropDownQuery extends Command {
    private static final long serialVersionUID = -8142107124771156292L;

    private Long id;

    @ApiModelProperty("非必填。如果存在值，搜索")
    private String name;

    private Integer pageIndex = 1;

    private Integer pageSize = 10;

    @ApiModelProperty("合作模式（业务线）筛选（多选）")
    private List<Integer> businessLine;
}
