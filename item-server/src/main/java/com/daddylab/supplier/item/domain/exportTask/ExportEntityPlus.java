package com.daddylab.supplier.item.domain.exportTask;

import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IExportTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.io.File;
import java.util.List;
import java.util.concurrent.*;
import java.util.function.Function;

/**
 * <AUTHOR> up
 * @date 2022/4/20 9:59 上午
 */
@Slf4j
public class ExportEntityPlus<T> {

    private ExcelWriter excelWriter;
    private WriteSheet writeSheet;
    private final static Long SHEET_SIZE = 65535L;
    private Integer sheetNo = 0;
    private Long sheetCount = 0L;
    private Long writeCount = 0L;
    private final Integer pageQueryWaitingTime = 30;
    private File excelFile;
    private List<Callable<List<T>>> queryCallableList;
    private ThreadPoolExecutor threadPoolExecutor;


    public ExportEntityPlus() {
    }

    public void init(ExportTask exportTask, Class<? extends ExportSheet> exportDataClass, ThreadPoolExecutor threadPoolExecutor) {
        this.excelFile = new File(exportTask.getName() + ".xlsx ");
        this.excelWriter = EasyExcel.write(this.excelFile, exportDataClass).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
        this.writeSheet = EasyExcel.writerSheet(this.sheetNo).build();
        this.threadPoolExecutor = threadPoolExecutor;
    }

    public void setCallableList(List<Callable<List<T>>> list) {
        this.queryCallableList = list;
    }

    public void export() {
        try {
            List<Future<List<T>>> futures = this.threadPoolExecutor.invokeAll(queryCallableList);
            futures.forEach(future -> {
                List<T> tList;
                try {
                    tList = future.get(pageQueryWaitingTime, TimeUnit.SECONDS);
                } catch (InterruptedException | ExecutionException | TimeoutException e) {
                    log.error("Paging query time is too long (default 5 seconds), and export failed.", e);
                    throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "查询等待超时");
                }

                writeCount = writeCount + tList.size();
                sheetCount = sheetCount + tList.size();
                if (writeCount >= SHEET_SIZE) {
                    sheetNo = sheetNo + 1;
                    this.writeSheet = EasyExcel.writerSheet(sheetNo).build();
                    sheetCount = 0L;
                }
                excelWriter.write(tList, writeSheet);
            });
        } catch (Exception e) {
            log.error("Export failed.", e);
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "导出失败，" + e.getMessage());
        } finally {
            excelWriter.finish();
        }
    }

    public void export(Function<T, ? extends ExportSheet> resultFunction) {
        ThreadPoolTaskExecutor exportExecutor = SpringUtil.getBean("exportExecutor");
        try {
            List<Future<List<T>>> futures = exportExecutor.getThreadPoolExecutor().invokeAll(queryCallableList);
            futures.forEach(future -> {
                CopyOnWriteArrayList<ExportSheet> sheetList = new CopyOnWriteArrayList<>();
                try {
                    List<T> dtoList = future.get(pageQueryWaitingTime, TimeUnit.SECONDS);
                    dtoList.forEach(t -> sheetList.add(resultFunction.apply(t)));
                } catch (Exception e) {
                    log.error("导出超时异常", e);
                    throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "查询等待超时，导出失败");
                }

                writeCount = writeCount + sheetList.size();
                sheetCount = sheetCount + sheetList.size();
                if (writeCount >= SHEET_SIZE) {
                    sheetNo = sheetNo + 1;
                    this.writeSheet = EasyExcel.writerSheet(sheetNo).build();
                    sheetCount = 0L;
                }
                excelWriter.write(sheetList, writeSheet);
            });
        } catch (Exception e) {
            log.error("Export failed.", e);
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "导出失败");
        } finally {
            excelWriter.finish();
        }
    }


    public File getExcelFile() {
        return excelFile;
    }


    public static ExportTask save(ExportTaskType type) {
        ExportTask exportTask = new ExportTask();
        exportTask.setStatus(ExportTaskStatus.RUNNING);
        exportTask.setName(type.getDesc() + "-" + DateUtil.date().toString("yyyyMMddHHmmss"));
        exportTask.setType(type);
        IExportTaskService exportTaskService = SpringUtil.getBean(IExportTaskService.class);
        exportTaskService.save(exportTask);
        return exportTask;
    }
}
