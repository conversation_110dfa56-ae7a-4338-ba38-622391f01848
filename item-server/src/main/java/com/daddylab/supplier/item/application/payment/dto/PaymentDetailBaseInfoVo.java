package com.daddylab.supplier.item.application.payment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年11月20日 5:39 PM
 */
@ApiModel("付款申请单明细基础信息")
@Data
public class PaymentDetailBaseInfoVo {

    @ApiModelProperty("付款明细ID")
    private Long id;

    @ApiModelProperty("关联单据编号")
    private String no;

    @ApiModelProperty("关联单据ID")
    private Long relateOrderId;

    @ApiModelProperty("应付金额")
    private BigDecimal rightAmount;

    @ApiModelProperty("申请付款金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("申请付款金额备注")
    private String applyAmountRemark;

    /**
     * 关联单据的总金额
     * 0.采购单为采购单金额。
     * 1.结算单为最终金额
     */
    private BigDecimal orderAmount;


    /**
     * 采购日期/结算周期
     */
    private String dateStr;

}
