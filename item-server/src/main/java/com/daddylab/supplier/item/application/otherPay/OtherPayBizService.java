package com.daddylab.supplier.item.application.otherPay;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.controller.otherPay.dto.OtherPayCmd;
import com.daddylab.supplier.item.controller.otherPay.dto.OtherPayQueryPage;
import com.daddylab.supplier.item.controller.otherPay.dto.OtherPayVo;
import com.daddylab.supplier.item.domain.otherPay.dto.OtherAuditDto;

//import com.daddylab.supplier.item.application.aws.dto.AwsApprovalNodeDTO;

/**
 * <AUTHOR>
 * @ClassName OtherPayBizService.java
 * @description 其他应付单
 * @createTime 2022年03月24日 15:41:00
 */
public interface OtherPayBizService {

    /**
     * 查询其他应付单列表
     *
     * @param otherPayQueryPage
     * @return
     */
    PageResponse<OtherPayVo> queryPage(OtherPayQueryPage otherPayQueryPage);

    /**
     * 修改其他应付
     *
     * @param cmd
     * @return
     */
    Response createOrUpdateOtherPay(OtherPayCmd cmd);


    /**
     * 根据id查询应对单信息
     * @param id
     * @return
     */
    SingleResponse<OtherPayVo> getById(Long id);

    /**
     * 删除
     * @param id
     * @return
     */
    Response delete (Long id);

    /**
     * 撤回
     * @param id
     * @return
     */
    Response revocation (Long id);


    /**
     * 审核
     * @param otherAuditDto
     * @return
     */
    Response audit(OtherAuditDto otherAuditDto);

    /**
     * 反审核
     * @param id
     * @return
     */
    Response unAudit(Long id);


    /**
     * 修改状态
     */
    void updateState(Long otherPayId, Integer state);

}
