package com.daddylab.supplier.item.infrastructure.bizLevelDivision;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BizUnionTypeEnum;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @since 2025/2/8
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
@Documented
@Inherited
@Deprecated
public @interface BizLevelFilter {
    BizUnionTypeEnum type();

    /**
     * 需应用权限拦截的业务表名
     */
    String value();

    String bizIdRef() default "";
}
