package com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

@Data
public class CheckProject {

    private Integer id;

    @JsonAlias("check_id")
    private Integer checkId;

    @JsonAlias("check_project")
    private String checkProject;

    @JsonAlias("check_category")
    private String checkCategory;

    @JsonAlias("check_result")
    private String checkResult;
}