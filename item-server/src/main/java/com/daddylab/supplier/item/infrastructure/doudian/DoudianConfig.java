package com.daddylab.supplier.item.infrastructure.doudian;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> up
 * @date 2022年09月13日 11:19 AM
 */
@Data
@Configuration
public class DoudianConfig {

    @Value("${douDian.appKey}")
    public String appKey;

    @Value("${douDian.appSecret}")
    public String appSecret;

    @Value("${douDian.shopId}")
    public String shopId;

    @Value("${douDian.httpClientReadTimeout:12000}")
    public String httpClientReadTimeout;

}
