package com.daddylab.supplier.item.controller.common.dto;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022/3/24 5:50 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("仓库列表查询参数")
public class WarehousePageQuery extends PageQuery {

    private static final long serialVersionUID = -4537269662340940513L;

    @ApiModelProperty("仓库编号")
    private String no;

    @ApiModelProperty("仓库名称")
    private String name;

    @ApiModelProperty("仓库名称（多选）")
    private List<String> names;

    @ApiModelProperty("状态。0全部。1.正常，2.停用")
    private Integer state;

    @ApiModelProperty("订单员userId")
    private Long orderPersonnelId;

    @ApiModelProperty("合作模式（业务线）筛选（多选）")
    private List<Integer> businessLine;

    @ApiModelProperty("仓库类型 0 全部 1 实体仓（默认） 2 虚拟仓")
    private Integer type = 1;

    @ApiModelProperty("仓库编号")
    private List<String> nos;

    @ApiModelProperty("1.内部、2.自流转、3.平台、4.京东沧海、5.抖音云仓、6分销委外")
    private Integer wmsType;

    @ApiModelProperty("售后客服用户ID")
    private Long afterSaleStaffId;



}
