package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 正式环境：https://daddylab.ik3cloud.com/k3cloud/
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/9 5:40 下午
 * @description
 */
@FeignClient(name = "kingDeeFeignClient", url = "${kingdee.url}")
public interface KingDeeFeignClient {

    @PostMapping(value = "Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser.common.kdsvc",
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @FeignLog(type = 1)
    String login(String json);

    @PostMapping(value = "Kingdee.BOS.WebApi.ServicesStub.AuthService.LoginBySign.common.kdsvc",
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @FeignLog(type = 1)
    String loginBySign(String json);

    @PostMapping(value = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.GroupSave.common.kdsvc",
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @FeignLog(type = 1)
    String saveGroup(String json, @RequestHeader("kdservice-sessionId") String session);

    @PostMapping(value = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.GroupDelete.common.kdsvc",
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @FeignLog(type = 1)
    String deleteGroup(String json, @RequestHeader("kdservice-sessionId") String session);

    @PostMapping(value = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save.common.kdsvc",
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @FeignLog(type = 1)
    String save(String json, @RequestHeader("kdservice-sessionId") String session);

    @PostMapping(value = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Delete.common.kdsvc",
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @FeignLog(type = 1)
    String delete(String json, @RequestHeader("kdservice-sessionId") String session);

    @PostMapping(value = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.UnAudit.common.kdsvc",
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @FeignLog(type = 1)
    String unAudit(String json, @RequestHeader("kdservice-sessionId") String session);

    @PostMapping(value = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit.common.kdsvc",
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @FeignLog(type = 1)
    String sumbit(String json, @RequestHeader("kdservice-sessionId") String session);

    @PostMapping(value = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Audit.common.kdsvc",
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @FeignLog(type = 1)
    String audit(String json, @RequestHeader("kdservice-sessionId") String session);

    @PostMapping(value = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc",
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    String queryBill(String json, @RequestHeader("kdservice-sessionId") String session);

    @PostMapping(value = "Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.QueryGroupInfo.common.kdsvc",
            consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @FeignLog(type = 1)
    String queryGroupInfo(String json, @RequestHeader("kdservice-sessionId") String session);

}
