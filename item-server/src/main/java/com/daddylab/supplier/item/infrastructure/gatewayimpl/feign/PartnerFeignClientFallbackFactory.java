package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/1/3
 */
@Component
@Slf4j
public class PartnerFeignClientFallbackFactory implements FallbackFactory<PartnerFeignClient> {

    private PartnerFeignClient fallbackClient;

    @Override
    public PartnerFeignClient create(Throwable cause) {
        log.error("P系统响应异常:" + cause.getMessage(), cause);
        if (fallbackClient == null) {
            fallbackClient = Mockito.mock(PartnerFeignClient.class, invocation -> {
                if (invocation.getMethod().getReturnType().equals(Rsp.class)) {
                    final Rsp<Object> rsp = new Rsp<>();
                    rsp.setCode(0);
                    rsp.setFlag(false);
                    rsp.setMsg("P系统响应异常");
                    rsp.setData(null);
                    return rsp;
                }
                return null;
            });
        }
        return fallbackClient;
    }
}
