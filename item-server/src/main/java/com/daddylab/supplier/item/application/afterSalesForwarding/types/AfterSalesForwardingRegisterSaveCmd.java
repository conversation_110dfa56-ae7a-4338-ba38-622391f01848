package com.daddylab.supplier.item.application.afterSalesForwarding.types;

import com.daddylab.supplier.item.common.domain.dto.Command;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/24
 */
@Data
public class AfterSalesForwardingRegisterSaveCmd extends Command {
    private static final long serialVersionUID = 5757844691198280837L;

    @NotEmpty
    @ApiModelProperty(value = "登记明细")
    private List<AfterSalesForwardingRegisterFormItemVO> registerList;
}
