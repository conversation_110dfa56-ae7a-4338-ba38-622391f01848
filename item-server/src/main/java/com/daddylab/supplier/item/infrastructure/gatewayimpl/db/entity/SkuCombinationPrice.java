package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2023年04月10日 9:56 AM
 */
@Data
public class SkuCombinationPrice {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 编码,单品为skuCode,组合装维组合编码
     */
    private String code;

    /**
     * 价格
     */
    private String priceInfo;



    /**
     * erp 的平台枚举值
     * 0:ALL 1:淘宝 2:抖店 3:小红书 4:自研电商 5:有赞 6:快手
     */
    private Integer platform;

    /**
     * 时间范围 开始时间
     */
    private Long startTime;

    /**
     * 时间范围 结束时间
     */
    private Long endTime;

    /**
     * 1.日常价格
     * 2.活动价格
     */
    private Integer priceType;

}
