package com.daddylab.supplier.item.application.provider;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.setting.PurchaseProviderAPI;
import com.daddylab.mall.wdtsdk.apiv2.setting.dto.PurchaseProviderPushProvider;
import com.daddylab.supplier.item.application.bizLevelDivision.BizLevelDivisionConvert;
import com.daddylab.supplier.item.application.criticalPush.ManagedPush;
import com.daddylab.supplier.item.application.criticalPush.SourceType;
import com.daddylab.supplier.item.application.criticalPush.TargetType;
import com.daddylab.supplier.item.application.provider.event.ProviderChangeEvent;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.trans.ProviderTransMapper;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.compensate.RestartSyncProviderCmd;
import com.daddylab.supplier.item.controller.provider.dto.*;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.region.entity.RegionTree;
import com.daddylab.supplier.item.domain.region.gateway.RegionGateway;
import com.daddylab.supplier.item.domain.region.service.RegionDomainService;
import com.daddylab.supplier.item.domain.region.vo.Region;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.bizLevelDivision.BizDivisionContext;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.events.SaveEvent;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ProviderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.assembler.CommonAssembler;
import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.javers.core.diff.Diff;
import org.javers.core.diff.changetype.PropertyChange;
import org.javers.core.diff.changetype.ValueChange;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/9 3:09 下午
 * @description
 */
@Service
@Slf4j
public class ProviderBizServiceImpl implements ProviderBizService {

  @Autowired private IProviderService iProviderService;

  @Autowired private ProviderMapper providerMapper;

  @Autowired ProviderGateway providerGateway;

  @Autowired RegionGateway regionGateway;

  @Autowired RegionDomainService regionDomainService;

  @Autowired IFinanceInfoService iFinanceInfoService;

  @Autowired IBankAccountService iBankAccountService;

  @Autowired KingDeeTemplate kingDeeTemplate;

  @Autowired OperateLogDomainService operateLogDomainService;

  @Autowired
  @Qualifier("UserGatewayCacheImpl")
  private UserGateway userGateway;

  @Autowired private RefreshConfig refreshConfig;

  @Autowired private IBizLevelDivisionService bizLevelDivisionService;
  @Autowired private IItemService itemService;

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Long saveOrUpdate(ProviderCmd cmd) {
    Long id;
    if (NumberUtil.isPositive(cmd.getId())) {
      id = updateProvider(cmd);
    } else {
      id = addProvider(cmd);
    }
    return id;
  }

  /**
   * 已经入库的provider
   *
   * @param provider
   */
  @ManagedPush(sourceType = SourceType.PROVIDER, targetType = TargetType.KINGDEE, idArgProp = "id")
  public void syncKingDee(Provider provider) {
    if (StringUtils.isBlank(provider.getKingDeeId())) {
      kingDeeTemplate.handler(ApiEnum.SAVE_PROVIDER, provider.getId(), "");
    } else {
      kingDeeTemplate.handler(ApiEnum.UPDATE_PROVIDER, provider.getId(), provider.getKingDeeId());
    }
    //        KingDeeRemoteTemplate remoteTemplate =
    // SpringUtil.getBean(KingDeeRemoteTemplate.class);
    //        KingDeeResponse kingDeeResponse;
    //        if (StringUtils.isBlank(provider.getKingDeeId())) {
    //            kingDeeTemplate.handler(ApiEnum.SAVE_PROVIDER, provider.getId(), "");
    //        } else {
    //            kingDeeTemplate.handler(ApiEnum.UPDATE_PROVIDER, provider.getId(),
    // provider.getKingDeeId());
    //        }
    //        log.info("---- kingDeeResponse :{} ----- ", kingDeeResponse);
    //        if (kingDeeResponse.getSuccess()) {
    //            provider.setKingDeeId(kingDeeResponse.getId());
    //            iProviderService.updateById(provider);
    //        } else {
    //            throw new RuntimeException(kingDeeResponse.getMsg());
    //        }
  }

  @Autowired private WdtGateway wdtGateway;

  @ManagedPush(sourceType = SourceType.PROVIDER, targetType = TargetType.WDT, idArgProp = "id")
  protected void updateToWdt(Provider provider) {
    try {
      final PurchaseProviderAPI api = wdtGateway.getAPI(PurchaseProviderAPI.class);
      final PurchaseProviderPushProvider providerPushParams = new PurchaseProviderPushProvider();
      providerPushParams.setProviderNo(provider.getProviderNo());
      providerPushParams.setProviderName(provider.getName());
      providerPushParams.setContact(provider.getContact());
      providerPushParams.setTelno("");
      providerPushParams.setMobile(provider.getContactMobile());
      providerPushParams.setFax("");
      providerPushParams.setQq("");
      providerPushParams.setZip("");
      providerPushParams.setWangwang("");
      providerPushParams.setEmail("");
      providerPushParams.setWebsite("");
      providerPushParams.setAddress(provider.getAddress());
      providerPushParams.setArriveCycleDays(0);
      providerPushParams.setRemark("");
      providerPushParams.setIsDisabled(0);
      providerPushParams.setAccountBankNo("");
      providerPushParams.setAccountBank("");
      providerPushParams.setCollectName("");
      try {
        providerPushParams.setProvince(
            Integer.parseInt(
                Objects.nonNull(provider.getProvinceCode()) ? provider.getProvinceCode() : "0"));
        providerPushParams.setCity(
            Integer.parseInt(
                Objects.nonNull(provider.getCityCode()) ? provider.getCityCode() : "0"));
        providerPushParams.setDistrict(
            Integer.parseInt(
                Objects.nonNull(provider.getAreaCode()) ? provider.getAreaCode() : "0"));
      } catch (NumberFormatException e) {
        log.warn("推送旺店通供应商信息时，省市区编码转化错误");
      }
      api.push(providerPushParams);
    } catch (WdtErpException e) {
      throw WdtExceptions.wrapBizException(e);
    }
  }

  @Transactional(rollbackFor = Exception.class)
  public Long addProvider(ProviderCmd cmd) {
    Boolean repeat = providerGateway.isRepeat(cmd.getName());
    if (repeat) {
      throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "供应商的名字不允许重复");
    }

    Provider provider = ProviderTransMapper.INSTANCE.cmdToDo(cmd);
    iProviderService.save(provider);
    provider.setProviderNo(generateProvider(provider.getId()));
    iProviderService.updateById(provider);

    saveFinanceInfo(cmd, provider.getId());
    saveBankAccountList(cmd, provider.getId());

    bizLevelDivisionService.savePlainLevels(
        BizUnionTypeEnum.PROVIDER,
        provider.getId(),
        provider.getProviderNo(),
        DivisionLevelValueEnum.valueOf(cmd.getCorpType()),
        Arrays.asList(DivisionLevelEnum.COOPERATION));

    operateLogDomainService.addOperatorLog(
        UserContext.getUserId(), OperateLogTarget.PROVIDER, provider.getId(), "添加供应商成功");

    (((ProviderBizServiceImpl) AopContext.currentProxy())).syncKingDee(provider);
    (((ProviderBizServiceImpl) AopContext.currentProxy())).updateToWdt(provider);

    EventBusUtil.post(
        ProviderChangeEvent.ofNew(UserContext.getUserId(), provider.getId(), provider));
    return provider.getId();
  }

  public FinanceInfo saveFinanceInfo(ProviderCmd cmd, Long providerId) {

    if (Objects.nonNull(cmd.getId())) {
      Optional<FinanceInfo> optional =
          iFinanceInfoService
              .lambdaQuery()
              .eq(FinanceInfo::getProviderId, providerId)
              .select()
              .oneOpt();
      if (optional.isPresent()) {
        FinanceInfo oldOne = optional.get();
        oldOne.setInvoiceType(cmd.getInvoiceType());
        oldOne.setCurrency(cmd.getCurrency());
        oldOne.setTaxType(cmd.getTaxType());
        oldOne.setTaxRate(cmd.getTaxRate());
        iFinanceInfoService.updateById(oldOne);
        return oldOne;
      }
    }

    FinanceInfo financeInfo = new FinanceInfo();
    financeInfo.setInvoiceType(cmd.getInvoiceType());
    financeInfo.setCurrency(cmd.getCurrency());
    financeInfo.setTaxType(cmd.getTaxType());
    financeInfo.setTaxRate(cmd.getTaxRate());
    financeInfo.setProviderId(providerId);
    iFinanceInfoService.save(financeInfo);
    return financeInfo;
  }

  public void saveBankAccountList(ProviderCmd cmd, Long providerId) {
    // 新增供应，添加银行卡信息
    if (Objects.isNull(cmd.getId())) {
      List<BankAccount> bankAccountList = new LinkedList<>();

      if (CollUtil.isNotEmpty(cmd.getBankAccountVOList())) {
        for (BankAccountVO bankAccountVO : cmd.getBankAccountVOList()) {
          BankAccount bankAccount = new BankAccount();
          bankAccount.setBankCard(bankAccountVO.getBankCard());
          bankAccount.setBankDeposit(bankAccountVO.getBankDeposit());
          bankAccount.setBankNo(bankAccountVO.getBankNo());
          bankAccount.setDescription(bankAccountVO.getDescription());
          bankAccount.setProviderId(providerId);
          bankAccountList.add(bankAccount);
        }
        iBankAccountService.saveBatch(bankAccountList);
      }
    }
    // 更新供应商，更新银行卡信息
    else {
      List<BankAccountVO> bankAccountVOList = cmd.getBankAccountVOList();
      if (!CollUtil.isEmpty(bankAccountVOList)) {
        Map<Long, BankAccountVO> bankAccountVoMap =
            bankAccountVOList.stream()
                .filter(reqOne -> Objects.nonNull(reqOne.getId()))
                .collect(Collectors.toMap(BankAccountVO::getId, v -> v, (a, b) -> a));
        List<BankAccount> oldList =
            iBankAccountService
                .lambdaQuery()
                .eq(BankAccount::getProviderId, cmd.getId())
                .select()
                .list();

        List<Long> reqIdList =
            cmd.getBankAccountVOList().stream()
                .map(BankAccountVO::getId)
                .collect(Collectors.toList());
        List<Long> oldIdList =
            oldList.stream().map(BankAccount::getId).collect(Collectors.toList());
        List<Long> needRemoveIdList =
            oldIdList.stream()
                .filter(oldId -> !reqIdList.contains(oldId))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(needRemoveIdList)) {
          iBankAccountService.removeByIds(needRemoveIdList);
        }

        List<BankAccountVO> addVOList =
            bankAccountVOList.stream()
                .filter(newVO -> !oldIdList.contains(newVO.getId()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(addVOList)) {
          List<BankAccount> bankAccountList = new LinkedList<>();
          addVOList.forEach(
              vo -> {
                BankAccount bankAccount = new BankAccount();
                bankAccount.setBankCard(vo.getBankCard());
                bankAccount.setBankDeposit(vo.getBankDeposit());
                bankAccount.setBankNo(vo.getBankNo());
                bankAccount.setDescription(vo.getDescription());
                bankAccount.setProviderId(providerId);
                bankAccountList.add(bankAccount);
              });
          iBankAccountService.saveBatch(bankAccountList);
        }

        List<BankAccount> updatedList =
            oldList.stream()
                .filter(oldOne -> reqIdList.contains(oldOne.getId()))
                .peek(
                    oldOne -> {
                      BankAccountVO bankAccountVO = bankAccountVoMap.get(oldOne.getId());
                      if (Objects.nonNull(bankAccountVO)) {
                        oldOne.setBankCard(bankAccountVO.getBankCard());
                        oldOne.setBankDeposit(bankAccountVO.getBankDeposit());
                        oldOne.setBankNo(bankAccountVO.getBankNo());
                        oldOne.setDescription(bankAccountVO.getDescription());
                      }
                    })
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(updatedList)) {
          iBankAccountService.updateBatchById(updatedList);
        }
      }
    }
  }

  private String generateProvider(Long providerId) {
    return "G00S" + String.format("%05d", providerId);
  }

  @Transactional(rollbackFor = Exception.class)
  public Long updateProvider(ProviderCmd cmd) {
    final Provider oldProvider = iProviderService.getById(cmd.getId());
    if (oldProvider == null) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "供应商不存在");
    }
    oldProvider.setCodeStr(
        regionGateway.getCodeStr(
            oldProvider.getProvinceCode(), oldProvider.getCityCode(), oldProvider.getAreaCode()));

    boolean canEdit = false;
    boolean editAllProvider = UserContext.hasPermission(GlobalConstant.PROVIDER_ALL_EDIT);
    boolean editAssignProvider = UserContext.hasPermission(GlobalConstant.PROVIDER_ASSIGN_EDIT);
    if (!editAllProvider && !editAssignProvider) {
      throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "没有编辑供应商的权限");
    }
    if (editAllProvider) {
      canEdit = true;
    }
    // 没有供应商全局编辑的权限
    else {
      // 供应商两种负责人信息是否为空，如果都为空，也可以编辑
      boolean mainChargerEmpty =
          (Objects.isNull(oldProvider.getMainChargerUserId())
              || 0 == oldProvider.getMainChargerUserId());
      boolean secondChargerEmpty =
          (Objects.isNull(oldProvider.getSecondChargerUserId())
              || 0 == oldProvider.getSecondChargerUserId());
      if (mainChargerEmpty && secondChargerEmpty) {
        canEdit = true;
      } else {
        // 当前操作者必须是此供应商两种操作人之一，才可以编辑
        Long userId = UserContext.getUserId();
        if (Objects.equals(oldProvider.getMainChargerUserId(), userId)
            || Objects.equals(oldProvider.getSecondChargerUserId(), userId)) {
          canEdit = true;
        }
      }
    }
    if (!canEdit) {
      throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "当前用户没有编辑此供应商的权限");
    }

    final Provider updateProvider = ProviderTransMapper.INSTANCE.cmdToDo(cmd);
    updateProvider.setKingDeeId(oldProvider.getKingDeeId());
    updateProvider.setProviderNo(oldProvider.getProviderNo());
    updateProvider.setCodeStr(cmd.getCodeStr());

    if (Objects.nonNull(cmd.getStatus())) {
      updateProvider.setStatus(IEnum.getEnumByValue(ProviderStatus.class, cmd.getStatus()));
    }
    boolean isNameChange = !oldProvider.getName().equals(cmd.getName());
    if (isNameChange) {
      Boolean repeat = providerGateway.isRepeat(cmd.getName());
      if (repeat) {
        throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "供应商重复，名字唯一");
      }
    }
    // 根据社会统一信用编码监控监控关联关系的变化，
    boolean isCodeChange =
        !oldProvider.getUnifySocialCreditCodes().equals(cmd.getUnifySocialCreditCodes());
    if (isCodeChange) {
      updateProvider.setPartnerProviderId(
          StringUtils.isBlank(cmd.getPartnerProviderId())
              ? 0L
              : Long.parseLong(cmd.getPartnerProviderId()));
    }
    if (CollUtil.isNotEmpty(cmd.getCorpType())) {
      updateProvider.setBusinessLine(cmd.getCorpType().stream().map(Objects::toString).collect(Collectors.joining(",")));
    }
    iProviderService.updateById(updateProvider);

    List<BankAccount> oldBankAccountList = iBankAccountService.getAccountByProviderId(cmd.getId());
    Optional<FinanceInfo> oldFinanceInfoOptional =
        iFinanceInfoService
            .lambdaQuery()
            .eq(FinanceInfo::getProviderId, cmd.getId())
            .select()
            .oneOpt();

    FinanceInfo updateFinanceInfo = saveFinanceInfo(cmd, cmd.getId());
    saveBankAccountList(cmd, cmd.getId());
    List<BankAccount> bankAccountList = iBankAccountService.getAccountByProviderId(cmd.getId());

    bizLevelDivisionService.savePlainLevels(
        BizUnionTypeEnum.PROVIDER,
        updateProvider.getId(),
        updateProvider.getProviderNo(),
        DivisionLevelValueEnum.valueOf(cmd.getCorpType()),
        Arrays.asList(DivisionLevelEnum.COOPERATION));

    (((ProviderBizServiceImpl) AopContext.currentProxy())).syncKingDee(updateProvider);
    (((ProviderBizServiceImpl) AopContext.currentProxy())).updateToWdt(updateProvider);

    String msg1 = providerEditLog(oldProvider, updateProvider);
    String msg2 = providerBankEditLog(oldBankAccountList, bankAccountList);
    String msg3 = financeInfoEditLog(oldFinanceInfoOptional, updateFinanceInfo);
    operateLogDomainService.addOperatorLog(
        UserContext.getUserId(),
        OperateLogTarget.PROVIDER,
        cmd.getId(),
        msg1 + "。" + msg2 + "。" + msg3);

    return updateProvider.getId();
  }

  /** 供应商金融信息编辑日志 */
  private String financeInfoEditLog(
      Optional<FinanceInfo> oldFinanceInfoOptional, FinanceInfo updateFinanceInfo) {
    if (!oldFinanceInfoOptional.isPresent()) {
      return "";
    }
    FinanceInfo oldFinanceInfo = oldFinanceInfoOptional.get();
    List<String> logList = new LinkedList<>();
    DiffUtil.diff(oldFinanceInfo, updateFinanceInfo)
        .groupByObject()
        .forEach(
            it -> {
              final List<PropertyChange> propertyChanges = it.getPropertyChanges();
              for (PropertyChange propertyChange : propertyChanges) {
                ValueChange change = (ValueChange) propertyChange;
                String changeStr;
                if (change.getPropertyName().equals("发票类型")) {
                  String left =
                      1 == Integer.parseInt(change.getLeft().toString()) ? "增值税专用发票" : "普通发票";
                  String right =
                      1 == Integer.parseInt(change.getRight().toString()) ? "增值税专用发票" : "普通发票";
                  changeStr = "发票类型：" + left + "->" + right;
                } else if (change.getPropertyName().equals("结算币别")) {
                  String left = 1 == Integer.parseInt(change.getLeft().toString()) ? "人民币" : "美金";
                  String right = 1 == Integer.parseInt(change.getRight().toString()) ? "人民币" : "美金";
                  changeStr = "结算币别：" + left + "->" + right;
                } else if (change.getPropertyName().equals("税分类")) {
                  String left =
                      1 == Integer.parseInt(change.getLeft().toString()) ? "一般纳税人" : "小规模纳税人";
                  String right =
                      1 == Integer.parseInt(change.getRight().toString()) ? "一般纳税人" : "小规模纳税人";
                  changeStr = "税分类：" + left + "->" + right;
                } else {
                  changeStr =
                      change.getPropertyName() + "：" + change.getLeft() + "->" + change.getRight();
                }
                logList.add(changeStr);
              }
            });
    return StrUtil.join("。", logList);
  }

  /**
   * 供应商银行信息编辑日志
   *
   * @param oldList
   * @param newList
   * @return
   */
  private String providerBankEditLog(List<BankAccount> oldList, List<BankAccount> newList) {
    DiffUtil.ObjListDiff objListDiff = DiffUtil.diffObjList(oldList, newList, BankAccount.class);
    StringBuilder editLog = new StringBuilder();

    String addLog = "";
    List<String> addLogList = new LinkedList<>();
    HashSet<Object> addIdSet = objListDiff.getAddIdSet();
    if (CollUtil.isNotEmpty(addIdSet)) {
      addIdSet.forEach(val -> addLogList.add(val.toString()));
      addLog = "新增银行账号，" + StrUtil.join(",", addLogList);
    }
    if (StrUtil.isNotBlank(addLog)) {
      editLog.append(addLog);
    }

    String removeLog = "";
    List<String> removeLogList = new LinkedList<>();
    HashSet<Object> removeIdSet = objListDiff.getRemoveIdSet();
    if (CollUtil.isNotEmpty(removeIdSet)) {
      removeIdSet.forEach(val -> removeLogList.add(val.toString()));
      removeLog = "移除银行账号，" + StrUtil.join(",", removeLogList);
    }
    if (StrUtil.isNotBlank(removeLog)) {
      editLog.append(removeLog);
    }

    List<String> changeLogList = new LinkedList<>();
    Map<Object, Set<DiffUtil.ChangePropertyObj>> changeMap = objListDiff.getChangeMap();
    if (CollUtil.isNotEmpty(changeMap)) {
      changeMap.forEach(
          (k, v) -> {
            List<String> oneChangeLogList = new LinkedList<>();
            v.forEach(
                val ->
                    oneChangeLogList.add(
                        val.getProperty() + "：" + val.getOldVal() + "->" + val.getNewVal()));
            String s = "银行账号：" + k + " 发生改变，" + StrUtil.join(",", oneChangeLogList);
            changeLogList.add(s);
          });
    }
    if (CollUtil.isNotEmpty(changeLogList)) {
      editLog.append(StrUtil.join("。", changeLogList));
    }

    return editLog.toString();
  }

  /**
   * 供应商基本信息操作日志
   *
   * @param oldOne
   * @param newOne
   * @return
   */
  private String providerEditLog(Provider oldOne, Provider newOne) {
    List<String> providerLog = new LinkedList<>();
    Diff diff = DiffUtil.diff(oldOne, newOne);
    boolean empty = diff.getChanges().isEmpty();
    if (!empty) {
      diff.groupByObject()
          .forEach(
              it -> {
                final List<PropertyChange> propertyChanges = it.getPropertyChanges();
                for (PropertyChange propertyChange : propertyChanges) {
                  ValueChange change = (ValueChange) propertyChange;
                  String stringBuilder;
                  if (change.getPropertyName().equals("状态")) {
                    ProviderStatus left = (ProviderStatus) change.getLeft();
                    ProviderStatus right = (ProviderStatus) change.getRight();
                    stringBuilder = "供应商状态从" + left.getDesc() + "改为" + right.getDesc();
                  } else if (change.getPropertyName().equals("供应商类型")) {
                    ProviderType left = (ProviderType) change.getLeft();
                    ProviderType right = (ProviderType) change.getRight();
                    stringBuilder = "供应商类型从" + left.getDesc() + "改为" + right.getDesc();
                  } else if (change.getPropertyName().equals("负责人")) {
                    long left = (long) change.getLeft();
                    long right = (long) change.getRight();
                    Map<Long, StaffInfo> longStaffInfoMap =
                        userGateway.batchQueryStaffInfoByIds(ListUtil.of(left, right));
                    String leftName =
                        Objects.nonNull(longStaffInfoMap.get(left))
                            ? longStaffInfoMap.get(left).getNickname()
                            : "无名氏";
                    String rightName =
                        Objects.nonNull(longStaffInfoMap.get(right))
                            ? longStaffInfoMap.get(right).getNickname()
                            : "无名氏";
                    stringBuilder = "负责人从" + leftName + "改为" + rightName;
                  } else if (change.getPropertyName().equals("次要负责人")) {
                    long left = (long) change.getLeft();
                    long right = (long) change.getRight();
                    Map<Long, StaffInfo> longStaffInfoMap =
                        userGateway.batchQueryStaffInfoByIds(ListUtil.of(left, right));
                    String leftName =
                        Objects.nonNull(longStaffInfoMap.get(left))
                            ? longStaffInfoMap.get(left).getNickname()
                            : "无名氏";
                    String rightName =
                        Objects.nonNull(longStaffInfoMap.get(right))
                            ? longStaffInfoMap.get(right).getNickname()
                            : "无名氏";
                    stringBuilder = "次要负责人" + leftName + "改为" + rightName;
                  } else {
                    String rightStr;
                    if (Objects.isNull(change.getRight())
                        || StrUtil.isBlank(change.getRight().toString())) {
                      rightStr = "空";
                    } else {
                      rightStr = change.getRight().toString();
                    }
                    stringBuilder = change.getPropertyName() + change.getLeft() + "改为" + rightStr;
                  }
                  providerLog.add(stringBuilder);
                }
              });
    }
    return StrUtil.join("。", providerLog);
  }

  private static void setBusinessLinesFilter(
      QueryWrapper<Provider> queryWrapper, List<Integer> businessLine) {
    if (CollectionUtil.isNotEmpty(businessLine)) {
      queryWrapper.and(
          q -> {
            for (Integer line : businessLine) {
              q.or().eq("is_business_line" + line, 1);
            }
          });
    }
  }

  @Override
  public PageResponse<ProviderViewVo> queryPage(ProviderPageQuery pageQuery) {
    QueryWrapper<Provider> queryWrapper = new QueryWrapper<>();
    queryWrapper
        .lambda()
        .like(StringUtils.isNotBlank(pageQuery.getName()), Provider::getName, pageQuery.getName());
    if (Objects.nonNull(pageQuery.getStatus()) && pageQuery.getStatus() > 0) {
      queryWrapper.lambda().eq(Provider::getStatus, pageQuery.getStatus());
    }
    if (Objects.nonNull(pageQuery.getType()) && pageQuery.getType() > 0) {
      queryWrapper.lambda().eq(Provider::getType, pageQuery.getType());
    }
    if (StringUtils.isNotBlank(pageQuery.getContact())) {
      queryWrapper.lambda().eq(Provider::getContact, pageQuery.getContact());
    }
    if (Objects.nonNull(pageQuery.getMainChargerUserId())) {
      queryWrapper.lambda().eq(Provider::getMainChargerUserId, pageQuery.getMainChargerUserId());
    }
    if (Objects.nonNull(pageQuery.getSecondChargerUserId())) {
      queryWrapper
          .lambda()
          .eq(Provider::getSecondChargerUserId, pageQuery.getSecondChargerUserId());
    }
    queryWrapper.lambda().orderByDesc(Provider::getCreatedAt);
    final Set<DivisionLevelValueEnum> selectedScope =
        new HashSet<>(
            DivisionLevelValueEnum.valueOf(pageQuery.getCorpType(), pageQuery.getBizType()));
    IPage<Provider> page = new Page<>(pageQuery.getPageIndex(), pageQuery.getPageSize());
    BizDivisionContext.invoke(
        BizUnionTypeEnum.PROVIDER,
        c -> c.setSelectedScopes(selectedScope),
        () -> iProviderService.page(page, queryWrapper));

    if (page.getTotal() == 0) {
      return ResponseFactory.emptyPage();
    }

    final Set<Long> providerIds =
        page.getRecords().stream().map(Entity::getId).collect(Collectors.toSet());
    final Map<Long, List<BankAccount>> bankAccountsMap =
        iBankAccountService.getAccountBatchByProviderIds(providerIds);
    List<Provider> records = page.getRecords();
    List<Long> userIdList = new LinkedList<>();
    records.forEach(
        val -> {
          userIdList.add(val.getSecondChargerUserId());
          userIdList.add(val.getMainChargerUserId());
        });
    Map<Long, StaffInfo> userStaffInfoMap =
        userGateway.batchQueryStaffInfoByIds(
            userIdList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList()));
    List<ProviderViewVo> providerViewVos = ProviderTransMapper.INSTANCE.entityToViewVos(records);

    for (ProviderViewVo provider : providerViewVos) {
      provider.setIsFromPartner(
          Objects.nonNull(provider.getPartnerProviderId()) && provider.getPartnerProviderId() != 0);
      provider.setCodeStr(
          regionGateway.getCodeStr(
              provider.getProvinceCode(), provider.getCityCode(), provider.getAreaCode()));
      final List<BankAccount> bankAccountList =
          bankAccountsMap.getOrDefault(provider.getId(), Collections.emptyList());
      List<BankAccountVO> bankAccountVOList =
          bankAccountList.stream()
              .map(ProviderTransMapper.INSTANCE::bankAccountToVO)
              .collect(Collectors.toList());
      provider.setBankAccountVOList(bankAccountVOList);

      StaffInfo mainStaffInfo = userStaffInfoMap.get(provider.getMainChargerUserId());
      if (Objects.nonNull(mainStaffInfo)) {
        provider.setMainChargerUserName(mainStaffInfo.getNickname());
      } else {
        StaffInfo secondStaffInfo = userStaffInfoMap.get(provider.getSecondChargerUserId());
        provider.setMainChargerUserName(
            Objects.isNull(secondStaffInfo) ? StrUtil.EMPTY : secondStaffInfo.getNickname());
      }
    }

    final List<Long> partnerProviderIds =
        page.getRecords().stream().map(Provider::getPartnerProviderId).collect(Collectors.toList());
    final PartnerProviderPageQuery partnerPageQuery = new PartnerProviderPageQuery();
    partnerPageQuery.setPartnerProviderIds(partnerProviderIds);
    partnerPageQuery.setPageIndex(1);
    partnerPageQuery.setPageSize(100);
    final List<PartnerProviderResp> partnerProviderResps =
        providerGateway.partnerQuery(partnerPageQuery);
    final Map<Integer, PartnerProviderResp> partnerProviderRespsMap =
        partnerProviderResps.stream()
            .collect(Collectors.toMap(PartnerProviderResp::getId, Function.identity()));
    for (ProviderViewVo provider : providerViewVos) {
      if (provider.getPartnerProviderId() > 0) {
        final Optional<PartnerProviderResp> partnerProviderResp =
            Optional.ofNullable(
                partnerProviderRespsMap.get(provider.getPartnerProviderId().intValue()));
        provider.setIsBlacklist(
            partnerProviderResp.map(PartnerProviderResp::getIsBlacklist).orElse(0));
      }
      final List<BankAccount> bankAccountList =
          bankAccountsMap.getOrDefault(provider.getId(), Collections.emptyList());
      List<BankAccountVO> bankAccountVOList =
          bankAccountList.stream()
              .map(ProviderTransMapper.INSTANCE::bankAccountToVO)
              .collect(Collectors.toList());
      provider.setBankAccountVOList(bankAccountVOList);
    }

    return PageResponse.of(
        providerViewVos, (int) page.getTotal(), pageQuery.getPageSize(), pageQuery.getPageIndex());
  }

  @Override
  public SingleResponse<ProviderVO> queryDetail(Long id) {
    ProviderVO providerVO = providerMapper.getProviderById(id);
    if (providerVO == null) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "供应商不存在");
    }

    List<BankAccount> bankAccountList = iBankAccountService.getAccountByProviderId(id);
    List<BankAccountVO> bankAccountVOList =
        bankAccountList.stream()
            .map(ProviderTransMapper.INSTANCE::bankAccountToVO)
            .collect(Collectors.toCollection(() -> new ArrayList<>(bankAccountList.size())));
    providerVO.setBankAccountVOList(bankAccountVOList);

    providerVO.setCodeStr(
        regionGateway.getCodeStr(
            providerVO.getProvinceCode(), providerVO.getCityCode(), providerVO.getAreaCode()));
    providerVO.setIsFromPartner(
        Objects.nonNull(providerVO.getPartnerProviderId())
            && providerVO.getPartnerProviderId() > 0);

    // 填充两个负责人的名字
    if (Objects.nonNull(providerVO.getMainChargerUserId())) {
      StaffInfo staffInfo = userGateway.queryStaffInfoById(providerVO.getMainChargerUserId());
      String name = Objects.nonNull(staffInfo) ? staffInfo.getNickname() : "";
      providerVO.setMainChargerName(name);
    }
    if (Objects.nonNull(providerVO.getSecondChargerUserId())) {
      StaffInfo staffInfo = userGateway.queryStaffInfoById(providerVO.getSecondChargerUserId());
      String name = Objects.nonNull(staffInfo) ? staffInfo.getNickname() : "";
      providerVO.setSecondChargerName(name);
    }

    providerVO.setIsBlacklist(
        providerGateway
            .partnerQueryById(providerVO.getPartnerProviderId())
            .map(PartnerProviderResp::getIsBlacklist)
            .orElse(0));

    final List<BizLevelDivision> bizLevelDivisions =
        bizLevelDivisionService.selectByTypeAndBizId(BizUnionTypeEnum.PROVIDER, id);
    providerVO.setCorpType(
        BizLevelDivisionConvert.INSTANCE.toBizLevelIntegerList(
            bizLevelDivisions, DivisionLevelEnum.COOPERATION));
    providerVO.setBizType(
        BizLevelDivisionConvert.INSTANCE.toBizLevelIntegerList(
            bizLevelDivisions, DivisionLevelEnum.BUSINESS_TYPE));

    return SingleResponse.of(providerVO);
  }

  @Override
  public SingleResponse<Map<Long, String>> batchQueryProviderName(List<Long> providerIds) {
    if (CollUtil.isEmpty(providerIds)) return SingleResponse.of(Collections.emptyMap());
    final List<Provider> providers =
        iProviderService
            .lambdaQuery()
            .in(Provider::getId, providerIds)
            .select(Provider::getId, Provider::getName)
            .list();
    final Map<Long, String> nameMap =
        providers.stream()
            .collect(Collectors.toMap(Provider::getId, Provider::getName, (a, b) -> b));
    return SingleResponse.of(nameMap);
  }

  @Override
  public List<Provider> batchQueryProvider(List<Long> providerIds) {
    if (CollUtil.isNotEmpty(providerIds)) {
      return iProviderService.listByIds(providerIds);
    }
    return Collections.emptyList();
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void remove(Long id) {
    log.info("移除供应商，参数id:{}", id);
    Provider provider = iProviderService.getById(id);
    Assert.notNull(provider, "供应商查询为空，id非法");

    if (!providerGateway.canRemove(id)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "此供应商存在关联数据，无法删除");
    }

    if (StringUtils.isNotBlank(provider.getKingDeeId())) {
      //            KingDeeRemoteTemplate remoteTemplate =
      // SpringUtil.getBean(KingDeeRemoteTemplate.class);
      //            KingDeeResponse kingDeeResponse =
      // remoteTemplate.deleteProvider(provider.getKingDeeId());
      //            if (!kingDeeResponse.getSuccess()) {
      //                throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR,
      // kingDeeResponse.getMsg());
      //            }
      kingDeeTemplate.handler(ApiEnum.DELETE_PROVIDER, provider.getId(), provider.getKingDeeId());
    }

    providerGateway.remove(id);
    iFinanceInfoService.removeByProviderId(id);
    iBankAccountService.removeByProviderId(id);

    //        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
    //            StopWatch stopWatch = new StopWatch();
    //            stopWatch.start();
    //
    //            if (StringUtils.isNotBlank(provider.getKingDeeId())) {
    //                // 耗时有13秒，16秒
    //                Response handler = kingDeeTemplate.handler(ApiEnum.DELETE_PROVIDER, id,
    // provider.getKingDeeId());
    //                if(handler.isSuccess()){
    //                    providerGateway.remove(id);
    //                }
    //            }else{
    //                providerGateway.remove(id);
    //            }
    //
    //            iFinanceInfoService.removeByProviderId(id);
    //            iBankAccountService.removeByProviderId(id);
    //
    //            stopWatch.stop();
    //            log.info("------- 移除供应商耗时time:{} ----- ", stopWatch.getTotalTimeSeconds());
    //        });
  }

  @Override
  public MultiResponse<ProviderDropDownVo> dropDownList(ProviderDropDownCmd cmd) {

    if (CollUtil.isEmpty(cmd.getBusinessLine())) {
      final List<Integer> collect =
          UserPermissionJudge.getUserBusinessLine().stream()
              .map(BusinessLine::getValue)
              .collect(Collectors.toList());
      cmd.setBusinessLine(collect);
    }

    if (Objects.nonNull(cmd.getId()) && 0 != cmd.getId()) {
      final Provider provider = iProviderService.getById(cmd.getId());
      Assert.notNull(provider, "供应商ID无效:{}", cmd.getId());
      ProviderDropDownVo vo = new ProviderDropDownVo();
      vo.setId(provider.getId());
      vo.setName(provider.getName());
      vo.setStatus(provider.getStatus());
      vo.setStandard(ProviderStatus.COOPERATION.equals(provider.getStatus()) ? "ON" : "OFF");
      vo.setPartnerProviderId(provider.getPartnerProviderId());

      Long chargerUserId =
          Objects.nonNull(provider.getMainChargerUserId())
              ? provider.getMainChargerUserId()
              : provider.getSecondChargerUserId();
      if (Objects.nonNull(chargerUserId)) {
        vo.setChargerUserId(chargerUserId);
        StaffInfo staffInfo = userGateway.queryStaffInfoById(chargerUserId);
        vo.setChargerUserName(Objects.nonNull(staffInfo) ? staffInfo.getNickname() : StrUtil.EMPTY);
      }
      return MultiResponse.of(Collections.singletonList(vo));
    }

    int index = 0 == cmd.getPageIndex() ? 1 : cmd.getPageIndex();
    int offset = (index - 1) * cmd.getPageSize();
    //    List<ProviderDropDownVo> providerDropDownVos =
    //        BizDivisionContext.invoke(
    //            BizUnionTypeEnum.PROVIDER,
    //            () ->
    //                providerMapper.dropDownList(
    //                    cmd.getName(), offset, cmd.getPageSize(), cmd.getBusinessLine()));
    List<ProviderDropDownVo> providerDropDownVos =
        providerMapper.dropDownList(
            cmd.getName(), offset, cmd.getPageSize(), cmd.getBusinessLine());

    List<Long> thisPageChargerUserIdList =
        providerDropDownVos.stream()
            .map(
                provider ->
                    Objects.nonNull(provider.getMainChargerUserId())
                        ? provider.getMainChargerUserId()
                        : provider.getSecondChargerUserId())
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
    Map<Long, StaffInfo> thisPageChargerUserInfoMap =
        userGateway.batchQueryStaffInfoByIds(thisPageChargerUserIdList);

    for (ProviderDropDownVo providerDropDownVo : providerDropDownVos) {
      providerDropDownVo.setStandard(
          ProviderStatus.COOPERATION.equals(providerDropDownVo.getStatus()) ? "ON" : "OFF");

      Long chargerUserId =
          Objects.nonNull(providerDropDownVo.getMainChargerUserId())
              ? providerDropDownVo.getMainChargerUserId()
              : providerDropDownVo.getSecondChargerUserId();
      providerDropDownVo.setChargerUserId(chargerUserId);
      StaffInfo staffInfo = thisPageChargerUserInfoMap.get(chargerUserId);
      providerDropDownVo.setChargerUserName(
          Objects.nonNull(staffInfo) ? staffInfo.getNickname() : StrUtil.EMPTY);
    }
    return MultiResponse.of(providerDropDownVos);
  }

  @Override
  public MultiResponse<PartnerProviderVo> fuzzyQuery(PartnerProviderPageQuery pageQuery) {
    final List<PartnerProviderResp> partnerProviderRespList =
        providerGateway.partnerQuery(pageQuery);

    List<PartnerProviderVo> partnerProviderVos = new LinkedList<>();
    for (PartnerProviderResp partnerProviderResp : partnerProviderRespList) {
      final PartnerProviderVo partnerProviderVo =
          ProviderTransMapper.INSTANCE.partnerRespToVo(partnerProviderResp);
      partnerProviderVo.setUname(partnerProviderResp.getUName());
      partnerProviderVos.add(partnerProviderVo);
    }

    final RegionTree regionTree = regionDomainService.getRegionTree();

    List<PartnerProviderVo> list = new LinkedList<>();
    for (PartnerProviderVo partnerProviderVo : partnerProviderVos) {
      final String province = partnerProviderVo.getProvince();
      final String city = partnerProviderVo.getCity();
      final String area = partnerProviderVo.getArea();

      final Optional<Region> areaRegion = regionTree.areaFuzzyQuery(province, city, area);
      final Optional<Region> cityRegion = areaRegion.map(Region::getParent);
      final Optional<Region> provinceRegion = cityRegion.map(Region::getParent);

      partnerProviderVo.setProvinceCode(provinceRegion.map(Region::getCode).orElse(""));
      partnerProviderVo.setProvince(provinceRegion.map(Region::getName).orElse(""));

      partnerProviderVo.setCityCode(cityRegion.map(Region::getCode).orElse(""));
      partnerProviderVo.setCity(cityRegion.map(Region::getName).orElse(""));

      partnerProviderVo.setAreaCode(areaRegion.map(Region::getCode).orElse(""));
      partnerProviderVo.setArea(areaRegion.map(Region::getName).orElse(""));

      list.add(partnerProviderVo);
    }
    return MultiResponse.of(list);
  }

  @Override
  public MultiResponse<String> contactList(String name) {
    QueryWrapper<Provider> queryWrapper = new QueryWrapper<>();
    if (StringUtils.isNotBlank(name)) {
      queryWrapper.lambda().like(Provider::getContact, name);
    }
    final List<Provider> list = iProviderService.list(queryWrapper);
    final List<String> collect =
        list.stream().map(Provider::getContact).distinct().collect(Collectors.toList());
    return MultiResponse.of(collect);
  }

  @Override
  public String queryPhoneByName(String name) {
    QueryWrapper<Provider> queryWrapper = new QueryWrapper<>();
    queryWrapper.lambda().eq(StringUtils.isNotBlank(name), Provider::getName, name);
    Provider provider = iProviderService.getOne(queryWrapper);
    return provider.getContactMobile();
  }

  @Override
  public List<Provider> queryProviderByName(String name) {
    QueryWrapper<Provider> queryWrapper = new QueryWrapper<>();
    queryWrapper.lambda().like(StringUtils.isNotBlank(name), Provider::getName, name);
    return iProviderService.list(queryWrapper);
  }

  private String logDiff(Diff diff) {
    return diff.getChangesByType(PropertyChange.class).stream()
        .filter(
            propertyChange ->
                propertyChange
                    .getAffectedObject()
                    .map(o -> o.getClass().equals(ProviderVO.class))
                    .orElse(false))
        .map(PropertyChange::getPropertyName)
        .distinct()
        .collect(Collectors.joining("、"));
  }

  @Override
  public void restartSyncProvider(RestartSyncProviderCmd cmd) {
    ThreadUtil.execute(
        PoolEnum.COMMON_POOL,
        () -> {
          List<Provider> list =
              iProviderService
                  .lambdaQuery()
                  .in(
                      StringUtils.isNotBlank(cmd.getNames()),
                      Provider::getName,
                      ListUtil.of(cmd.getNames().split(",")))
                  .select()
                  .list();

          if (CollectionUtils.isEmpty(list)) {
            Alert.text(MessageRobotCode.GLOBAL, "【KingDee供应商同步补偿】查询满足补偿条件的供应商数量为0");
            return;
          }

          List<String> errorNameList = new LinkedList<>();
          for (Provider provider : list) {
            Response response;
            if (StringUtils.isBlank(provider.getKingDeeId())) {
              response = kingDeeTemplate.handler(ApiEnum.SAVE_PROVIDER, provider.getId(), "");
            } else {
              response =
                  kingDeeTemplate.handler(
                      ApiEnum.UPDATE_PROVIDER, provider.getId(), provider.getKingDeeId());
            }
            if (!response.isSuccess()) {
              errorNameList.add(provider.getName());
            }
          }

          if (CollectionUtils.isNotEmpty(errorNameList)) {
            Alert.text(
                MessageRobotCode.GLOBAL,
                "【KingDee供应商同步补偿】以下供应商补偿失败，"
                    + StrUtil.join(",", errorNameList)
                    + "，失败数量："
                    + errorNameList.size()
                    + "。补偿成功数量："
                    + (list.size() - errorNameList.size()));
          } else {
            Alert.text(MessageRobotCode.GLOBAL, "【KingDee供应商同步补偿】全部补偿成功。补偿数量：" + list.size());
          }
        });
  }

  @Override
  public SingleResponse<ProviderShopDetail> queryShopDetail(String mallShopId) {
    if (!refreshConfig.getEnableMultiShopVersion()) {
      return SingleResponse.of(null);
    }
    return SingleResponse.of(providerGateway.queryShopDetail(mallShopId));
  }

  @Override
  public void saveOrUpdateBankInfo(Long id, String cardNo, String bank, String bankNo) {
    List<BankAccount> bankAccountList =
        iBankAccountService.lambdaQuery().eq(BankAccount::getProviderId, id).list();
    if (CollUtil.isEmpty(bankAccountList)) {
      addNewBankAmount(id, cardNo, bank, bankNo);
      operateLogDomainService.addOperatorLog(0L, OperateLogTarget.PROVIDER, id, "采购收款成功，新增收款银行信息");
      return;
    }
    List<BankAccount> collect =
        bankAccountList.stream()
            .filter(val -> val.getBankCard().equals(cardNo))
            .collect(Collectors.toList());
    if (CollUtil.isEmpty(collect)) {
      addNewBankAmount(id, cardNo, bank, bankNo);
      operateLogDomainService.addOperatorLog(0L, OperateLogTarget.PROVIDER, id, "采购收款成功，新增收款银行信息");
    } else {
      List<BankAccount> updateList = new LinkedList<>();
      for (BankAccount bankAccount : collect) {
        if (!bank.equals(bankAccount.getBankDeposit()) || !bankNo.equals(bankAccount.getBankNo())) {
          bankAccount.setBankDeposit(bank);
          bankAccount.setBankNo(bankNo);
          updateList.add(bankAccount);
        }
      }
      if (CollUtil.isNotEmpty(updateList)) {
        iBankAccountService.updateBatchById(updateList);
        operateLogDomainService.addOperatorLog(0L, OperateLogTarget.PROVIDER, id, "采购收款成功，更新银行信息");
      }
    }
  }

  private void addNewBankAmount(Long id, String cardNo, String bank, String bankNo) {
    BankAccount bankAccount = new BankAccount();
    bankAccount.setProviderId(id);
    bankAccount.setBankCard(cardNo);
    bankAccount.setBankDeposit(bank);
    bankAccount.setBankNo(bankNo);
    iBankAccountService.save(bankAccount);
  }

  @TransactionalEventListener(fallbackExecution = true)
  @Transactional(rollbackFor = Exception.class)
  public void onItemSaveEvent(SaveEvent<Item> saveEvent) {
    final Item item = saveEvent.getModel();
    if (item == null) {
      return;
    }
    final Long providerId = item.getProviderId();
    if (providerId == null || providerId == 0L) {
      return;
    }
    final Provider provider = providerMapper.selectById(providerId);
    if (provider == null) {
      return;
    }
    final List<Item> items =
        itemService.lambdaQuery().eq(Item::getProviderId, providerId).select(Entity::getId).list();
    if (items.isEmpty()) {
      return;
    }
    final List<Long> itemIds = items.stream().map(Entity::getId).collect(Collectors.toList());
    final List<DivisionLevelValueEnum> divisionLevelValueEnums =
        bizLevelDivisionService
            .lambdaQuery()
            .in(
                BizLevelDivision::getLevel,
                DivisionLevelEnum.BUSINESS_TYPE,
                DivisionLevelEnum.COOPERATION)
            .eq(BizLevelDivision::getType, BizUnionTypeEnum.SPU)
            .in(BizLevelDivision::getBizId, itemIds)
            .list()
            .stream()
            .map(BizLevelDivision::getLevelVal)
            .distinct()
            .collect(Collectors.toList());
    bizLevelDivisionService.savePlainLevels(
        BizUnionTypeEnum.PROVIDER,
        providerId,
        provider.getProviderNo(),
        divisionLevelValueEnums,
        Arrays.asList(DivisionLevelEnum.BUSINESS_TYPE, DivisionLevelEnum.COOPERATION));
  }

  @Override
  public Boolean batchChangeCharger(BatchChangeChargerCmd cmd) {
    final LambdaUpdateChainWrapper<Provider> wrapper =
        iProviderService.lambdaUpdate().in(Provider::getId, cmd.getProviderIds());
    if (cmd.getChargerType() == 1) {
      wrapper.set(Provider::getMainChargerUserId, cmd.getChargerUserId());
    }
    if (cmd.getChargerType() == 2) {
      wrapper.set(Provider::getSecondChargerUserId, cmd.getChargerUserId());
    }
    final boolean update = wrapper.update();

    cmd.getProviderIds()
        .forEach(
            id -> {
              String typeStr = cmd.getChargerType() == 1 ? "主负责人" : "次要负责人";
              final StaffInfo staffInfo = userGateway.queryStaffInfoById(cmd.getChargerUserId());
              String name = Objects.nonNull(staffInfo) ? staffInfo.getNickname() : "未知";
              operateLogDomainService.addOperatorLog(
                  UserContext.getUserId(),
                  OperateLogTarget.PROVIDER,
                  id,
                  "批量修改供应商（" + typeStr + "）负责人，新相关负责人：" + name);
            });
    return update;
  }
}
