package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemPrice;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 商品价格 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Repository
public interface ItemPriceMapper extends DaddyBaseMapper<ItemPrice> {

    /**
     * 查询价格
     *
     * @param skuCode
     * @param type       价格类型
     * @return
     */
    List<BigDecimal> getPrice(@Param("skuCode") String skuCode, @Param("type") Integer type, @Param("currentTime") Long currentTime);

}
