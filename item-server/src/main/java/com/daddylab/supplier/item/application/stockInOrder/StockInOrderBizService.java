package com.daddylab.supplier.item.application.stockInOrder;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuVO;
import com.daddylab.supplier.item.domain.stockInOrder.dto.*;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderStockQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrderDetail;

import java.util.List;

/**
 * 采购入库服务
 *
 * <AUTHOR>
 * @date 2022/3/24 18:25
 **/
public interface StockInOrderBizService {
    /**
     * 新增/提交采购入库单
     *
     * @param stockInOrderAndOrderDetail
     * @return com.alibaba.cola.dto.Response
     * <AUTHOR>
     * @date 2022/3/25 18:27
     **/
    SingleResponse<String> createStockInOrder(StockInOrderAndOrderDetail stockInOrderAndOrderDetail, Boolean isSys,Boolean mockSync);

    /**
     * 根据 系统采购单创建 采购入库单。
     * @param stockInOrderAndOrderDetail
     * @return
     */
//    SingleResponse<String> createdStockInOrderForSysPurchase(StockInOrderAndOrderDetail stockInOrderAndOrderDetail);

    /**
     * 分页查询采购入库单
     *
     * @param stockInOrderQuery
     * @return com.alibaba.cola.dto.PageResponse<com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder>
     * <AUTHOR>
     * @date 2022/3/25 18:27
     **/
    PageResponse<StockInOrderShowDetail> queryStockInOrderList(StockInOrderQuery stockInOrderQuery);

    /**
     * 删除采购入库单
     *
     * @param stockInOrderId
     * @return com.alibaba.cola.dto.Response
     * <AUTHOR>
     * @date 2022/3/29 18:39
     **/
    Response deleteStockInOrderService(Integer stockInOrderId);

    /**
     * 采购入库单导出
     *
     * @param stockInOrderAndOrderDetailQuery
     * <AUTHOR>
     * @date 2022/4/1 17:54
     **/
    void exportExcel(StockInOrderAndOrderDetailQuery stockInOrderAndOrderDetailQuery);

    /**
     * 查询炎黄盈动需要的字段
     *
     * @param no
     * @return com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetailQuery
     * <AUTHOR>
     * @date 2022/4/2 17:53
     * @deprecated 弃用
     **/
    @Deprecated
    List<StockInOrderAndDetailQuery> stockInOrderAndOrderDetailQuerylist(String no);

    /**
     * 查询炎黄盈动需要的字段
     *
     * @param id
     * @return com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetailQuery
     * <AUTHOR>
     * @date 2022/4/2 17:53
     **/
    StockInOrderAndOrderDetail stockInOrderAndOrderDetailQuery(Long id);

    /**
     * 根据入库单号查询订单以及明细
     *
     * @param no
     * @return com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetail
     * <AUTHOR>
     * @date 2022/4/12 15:18
     **/
    SingleResponse<StockInOrderAndOrderDetailVo> stockInOrderAndDetailAllInfoQueryByNo(String no);

    /**
     * 查询已经完成的入库单
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    List<StockInOrder> listFinishStockInOrder(Long startTime, Long endTime);

    /**
     * 查询已经入库商品的库存
     *
     * @param stockQuery 查询对象
     * @return 库存
     */
    Integer getStockInOrderSkuStock(StockOutOrderStockQuery stockQuery);

    /**
     * 采购单下指定商品SKU入库的信息，未入库返回空集合
     *
     * @param purchaseOrderId 采购单号
     * @param itemSkuCode     商品SKU编码
     */
    List<StockInOrderDetail> getHasStockInOrderDetail(Long purchaseOrderId, String itemSkuCode);

    /**
     * 采购单ID查询采购入库单列表
     *
     * @param purchaseOrderId
     * @return com.alibaba.cola.dto.Response
     * <AUTHOR>
     * @date 2022/4/21 14:54
     **/
    SingleResponse<List<StockInOrder>> stockInOrderList(Long purchaseOrderId);

    /**
     * 取消采购入库单
     *
     * @param stockInOrderId
     * @return com.alibaba.cola.dto.Response
     * <AUTHOR>
     * @date 2022/4/24 14:00
     **/
    Response cancelStockInOrderService(Integer stockInOrderId);

    /**
     * 查询采购单sku列表
     *
     * @param query
     * @return com.alibaba.cola.dto.PageResponse<com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuVO>
     * <AUTHOR>
     * @date 2022/5/26 12:18
     **/
    PageResponse<ComposeSkuVO> pagePurchaseOrderSku(ComposeSkuPageQuery query);


}
