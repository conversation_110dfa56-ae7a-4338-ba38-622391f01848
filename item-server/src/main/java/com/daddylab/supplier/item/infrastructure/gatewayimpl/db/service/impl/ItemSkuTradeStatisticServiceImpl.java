package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSkuTradeStatistic;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemSkuTradeStatisticMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuTradeStatisticService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品销售统计 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Service
public class ItemSkuTradeStatisticServiceImpl extends DaddyServiceImpl<ItemSkuTradeStatisticMapper, ItemSkuTradeStatistic> implements IItemSkuTradeStatisticService {

}
