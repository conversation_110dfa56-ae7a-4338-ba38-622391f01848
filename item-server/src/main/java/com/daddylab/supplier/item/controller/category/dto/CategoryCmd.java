package com.daddylab.supplier.item.controller.category.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/27 11:51 上午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("添加品类请求实体")
public class CategoryCmd extends Command {

    private static final long serialVersionUID = 6672324957270039923L;

    @ApiModelProperty(value = "父品类id，如果根品类，为空。如果未1级类目，此值为0")
    private Long parentId;

    @ApiModelProperty("品类名称")
    private String name;

    @ApiModelProperty(value = "品类等级1~5，1为顶级")
    @NotNull(message = "品类登记不得为空")
    private Integer level;


}
