package com.daddylab.supplier.item.application.purchasePayable.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年02月06日 3:57 PM
 */
@Data
@ApiModel("申请付款页面详情列表明细")
public class ApplyPayOrderDetailListVO {

    @ApiModelProperty("原应付单编号")
    private String purchasePayOrderNo;

    @ApiModelProperty("原应付单关联单据id")
    private Long relationOrderId;

    @ApiModelProperty("原应付单关联单据类型。1：采购入库。2：退料出库")
    private Integer relationOrderType;


    @ApiModelProperty("skuCode")
    private String skuCode;

    @ApiModelProperty("规格详情")
    private String specifications;

    private String itemName;

    @ApiModelProperty("数量")
    private Integer quantity;

    /**
     * 税率
     */
    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    /**
     * 含税价格，税前价格
     */
    @ApiModelProperty("含税价格，税前单价")
    private BigDecimal withTaxPrice;

    /**
     * 不含税价格，税后价格
     */
    @ApiModelProperty("不含税价格，税后单价")
    private BigDecimal withoutTaxPrice;

    /**
     * 总税额
     */
    @ApiModelProperty("总税额")
    private BigDecimal taxTotalAmount;

    /**
     * 价税合计
     */
    @ApiModelProperty("价税合计")
    private BigDecimal withTaxTotalAmount;

    private String warehouseNo;

    private Long id;
}
