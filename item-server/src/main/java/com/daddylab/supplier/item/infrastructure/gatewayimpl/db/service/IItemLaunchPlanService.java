package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.controller.item.ItemLaunchPlanTextParam;
import com.daddylab.supplier.item.controller.item.dto.*;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchPlan;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 商品上新计划 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */
public interface IItemLaunchPlanService extends IDaddyService<ItemLaunchPlan> {

    /**
     * 分页查询
     *
     * @param query 分页查询参数
     * @return PageResponse<ItemLaunchPlanPageVo>
     */
    PageResponse<ItemLaunchPlanPageVo> pageQuery(ItemLaunchPlanPageQuery query);

    /**
     * 获取上新计划基础信息
     *
     * @param planId 计划 ID
     * @return SingleResponse<ItemLaunchPlanBaseVo>
     */
    SingleResponse<ItemLaunchPlanBaseVo> getBaseInfo(Long planId);

    /**
     * 获取上新计划（通过商品 ID）
     *
     * @param itemId 商品 ID
     * @return ItemLaunchPlan
     */
    ItemLaunchPlan getPlanByItemId(Long itemId);

    /**
     * 获取商品和上新计划的映射
     * key：itemId
     * val：ItemLaunchPlan
     *
     * @param itemIds 商品 IDs
     * @return Map<Long, ItemLaunchPlan>
     */
//    Map<Long, ItemLaunchPlan> getPlanMapByItemIds(List<Long> itemIds);

    /**
     * 获取上新计划关联的商品信息
     *
     * @param pageQuery 分页查询参数
     * @return PageResponse<ItemLaunchPlanLinkItemVo>
     */
    PageResponse<ItemLaunchPlanLinkItemVo> pageQueryLinkItem(ItemLaunchPlanLinkItemPageQuery pageQuery);

    /**
     * 删除计划和商品关联关系
     *
     * @param planItemRefId 计划和商品关联关系 ID
     */
    void deletePlanItemRef(Long planItemRefId);

    /**
     * 删除上新计划
     *
     * @param planId 计划 ID
     */
    void deletePlan(Long planId);

    /**
     * 新增上新计划
     *
     * @param param 参数
     */
    void add(ItemLaunchPlanParam param);


    /**
     * 修改上新计划
     *
     * @param param 参数
     */
    void edit(ItemLaunchPlanParam param);

    /**
     * 修改上新计划商品列表的上新文案和上新价格
     *
     * @param param
     */
    void editItemText(ItemLaunchPlanTextParam param);


    /**
     * 上新计划名称模糊搜索
     *
     * @param name      上新计划名称
     * @param pageIndex 页码
     * @param pageSie   每页大小
     * @return MultiResponse<PlanNameDropDownVo>
     */
    MultiResponse<PlanNameDropDownVo> nameDropDownList(String name, Integer pageIndex, Integer pageSie);

    /**
     * 上新时间下拉框
     *
     * @param pageIndex 页码
     * @param pageSize  每页大小
     * @return MultiResponse<LaunchTimeDropDownVo>
     */
    MultiResponse<LaunchTimeDropDownVo> launchTimeDownList(Integer pageIndex, Integer pageSize);

    /**
     * 移除掉某个商品与上新计划的关联
     *
     * @param itemId 商品ID
     */
    void deleteRefByItemId(Long itemId);

    /**
     * 商品切换上新计划
     *
     * @param itemId 商品ID
     * @param planId 新的上新计划ID
     */
    void syncItemLaunchPlan(Long itemId, Long planId);

    /**
     * 批量查询商品上新时间
     *
     * @param itemIds 商品ID
     */
    Map<Long, Long> getEstimateSaleTimeBatch(List<Long> itemIds);

    /**
     * 根据上新时间批量创建不存在的上新计划然后返回对应日期的上新计划ID
     *
     * @param launchDateEpochs 上新日期（时间戳）
     */
    Map<Long, Long> saveBatchIfNotExistAndReturnPlanId(Collection<Long> launchDateEpochs);

    /**
     * 批量更新商品数量统计
     *
     * @param planIds 商品ID
     * @return 更新计划数量
     */
    int updateBatchPlanRefCount(Collection<Long> planIds);

    /**
     * 确认商品与上新计划的关联关系已建立
     *
     * @param planId 计划ID
     * @param itemId 商品ID
     * @return
     */
    boolean ensurePlanItemRef(Long planId, Long itemId, String itemCode, Long time);


    /**
     * 提交上新计划，发送创建消息。
     *
     * @param id 计划id
     * @return
     */
    SingleResponse<Boolean> submit(Long id);

    /**
     * 添加商品到上新计划
     * @param planId
     * @param items
     * @param notice
     */
    void addPlanItem(Long planId, List<ItemLaunchPlanLinkItemParam> items, boolean notice);
}
