package com.daddylab.supplier.item.application.salesInStock;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.SkuUnitDto;
import com.daddylab.supplier.item.application.salesInStock.dto.WdtStockInDto;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2024年01月22日 10:31 AM
 */
@Service
public class SalesInStockTransDao {

    @Resource
    IWarehouseService iWarehouseService;
    @Resource
    IWdtRefundStockInOrderService iWdtRefundStockInOrderService;
    @Resource
    IWdtRefundOrderService iWdtRefundOrderService;
    @Resource
    IItemSkuService iItemSkuService;
    @Resource
    IItemService itemService;
    @Resource
    IBrandService iBrandService;
    @Resource
    IWdtRefundStockInOrderDetailsService iWdtRefundStockInOrderDetailsService;


    @Transactional(rollbackFor = Exception.class)
    public void deleteWdtData(WdtStockInDto wdtStockInDto) {
        iWdtRefundStockInOrderService.removeByIdWithTime(wdtStockInDto.getStockOrderId());
        iWdtRefundOrderService.removeByIdWithTime(wdtStockInDto.getRefundOrderId());

        if (CollUtil.isNotEmpty(wdtStockInDto.getStockOrderDetailIds())) {
            iWdtRefundStockInOrderDetailsService.removeByIdsWithTime(wdtStockInDto.getStockOrderDetailIds());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public WdtStockInDto buildData(String warehouseNo, List<SkuUnitDto> salesReturnList, Map<String, Integer> salesReturnGoodMap) {
        WdtStockInDto wdtStockInDto = new WdtStockInDto();

        // SGRK+年+月+日+00001，如：SGRK2024010800001
        String orderNo = "SGRK" + DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN) + "00" + RandomUtil.randomNumbers(3);
        String refundNo = "TK" + DateUtil.currentTime();
        long stockInId = DateUtil.currentTime();

        WdtRefundStockInOrder stockOrder = new WdtRefundStockInOrder();
        stockOrder.setOrderNo(orderNo);
        Optional<Warehouse> warehouse = iWarehouseService.queryWarehouseByNo(warehouseNo);
        String warehouseName = warehouse.isPresent() ? warehouse.get().getName() : warehouseNo;
        stockOrder.setWarehouseName(warehouseName);
        stockOrder.setWarehouseNo(warehouseNo);
        stockOrder.setOperatorName("系统。冲销补偿");
        stockOrder.setShopName(StrUtil.EMPTY);
        stockOrder.setRefundNo(refundNo);
        stockOrder.setStockinId((int) stockInId);
        iWdtRefundStockInOrderService.save(stockOrder);
        wdtStockInDto.setStockOrderId(stockOrder.getId());

        WdtRefundOrder refundOrder = new WdtRefundOrder();
        refundOrder.setType(2);
        refundOrder.setStockinStatus(3);
        refundOrder.setRefundNo(refundNo);
        iWdtRefundOrderService.save(refundOrder);
        wdtStockInDto.setRefundOrderId(refundOrder.getId());

        List<String> skuCodes = salesReturnList.stream().map(SkuUnitDto::getSkuCode).collect(Collectors.toList());
        Map<String, ItemSku> skuCodeAndItemIdMap = iItemSkuService.lambdaQuery()
                .in(ItemSku::getSkuCode, skuCodes).list()
                .stream().collect(Collectors.toMap(ItemSku::getSkuCode, v -> v, (a, b) -> b));

        List<WdtRefundStockInOrderDetails> collect = salesReturnList.stream().map(val -> {
            WdtRefundStockInOrderDetails wdtRefundStockInOrderDetails = new WdtRefundStockInOrderDetails();
            wdtRefundStockInOrderDetails.setStockinId(stockInId);
            wdtRefundStockInOrderDetails.setSpecNo(val.getSkuCode());
            ItemSku itemSku = skuCodeAndItemIdMap.get(val.getSkuCode());
            Item item = itemService.getById(itemSku.getItemId());
            wdtRefundStockInOrderDetails.setGoodsName(item.getName());
            wdtRefundStockInOrderDetails.setSpecName(itemSku.getSpecifications());
            wdtRefundStockInOrderDetails.setGoodsNo(item.getCode());
//            Integer settlementQuantity = Objects.nonNull(val.getSettlementQuantity()) ? val.getSettlementQuantity() : 0;
//            Integer temporaryQuantity = Objects.nonNull(val.getTemporaryQuantity()) ? val.getTemporaryQuantity() : 0;
            wdtRefundStockInOrderDetails.setRightNum(new BigDecimal(val.getNum()));
            wdtRefundStockInOrderDetails.setNum(wdtRefundStockInOrderDetails.getRightNum());
            wdtRefundStockInOrderDetails.setDefect(false);
            wdtRefundStockInOrderDetails.setBasicUnitName(itemSku.getUnit());
            Brand brand = iBrandService.getById(item.getBrandId());
            if (Objects.isNull(brand)) {
                wdtRefundStockInOrderDetails.setBrandName("未知品牌名称");
            } else {
                wdtRefundStockInOrderDetails.setBrandName(brand.getName());
            }
            wdtRefundStockInOrderDetails.setProp2(StrUtil.EMPTY);
            wdtRefundStockInOrderDetails.setRemark("系统冲销补偿");

            salesReturnGoodMap.put(val.getSkuCode(), wdtRefundStockInOrderDetails.getRightNum().intValue());

            return wdtRefundStockInOrderDetails;
        }).collect(Collectors.toList());
        iWdtRefundStockInOrderDetailsService.saveBatch(collect);

        List<Long> collect1 = collect.stream().map(WdtRefundStockInOrderDetails::getId).collect(Collectors.toList());
        wdtStockInDto.setStockOrderDetailIds(collect1);

        wdtStockInDto.setOrderNo(orderNo);

        return wdtStockInDto;
    }


}
