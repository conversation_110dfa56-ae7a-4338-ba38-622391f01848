package com.daddylab.supplier.item.infrastructure.third.base;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;

/**
 * <AUTHOR>
 * @class IAccessTokenService.java
 * @description 描述类的作用
 * @date 2024-02-29 10:29
 */
public interface IShopAuthorizationAware {
    default String getAccessToken() {
        return getAuthorization().getAccessToken();
    }

    ShopAuthorization getAuthorization();

    void setAuthorization(ShopAuthorization shopAuthorization);

}
