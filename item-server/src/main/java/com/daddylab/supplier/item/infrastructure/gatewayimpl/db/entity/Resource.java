package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 资源
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Resource implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父级id
     */
    private Long parentId;

    /**
     * 表示所在系统
     */
    private Long systemId;

    /**
     * 前端url
     */
    private String frontUrl;

    private String component;

    /**
     * 后端url
     */
    private String apiUrl;

    /**
     * 角色描述
     */
    private String name;

    /**
     * 标签
     */
    private String code;

    /**
     * 菜单类型
     * @see com.daddylab.supplier.item.domain.auth.enums.ResourceType
     */
    private String type;

    /**
     * 是否隐藏 0显示 1隐藏
     */
    private Integer hide;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 图标
     */
    private String icon;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 是否已删除
     */
    private Long deletedAt;


}
