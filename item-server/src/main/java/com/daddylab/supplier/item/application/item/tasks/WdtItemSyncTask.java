package com.daddylab.supplier.item.application.item.tasks;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.api.goods.GoodsAPI;
import com.daddylab.mall.wdtsdk.api.goods.dto.GoodsSearchRequest;
import com.daddylab.mall.wdtsdk.api.goods.dto.GoodsSearchResponse;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.brand.gateway.BrandGateway;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DataSource;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemImageType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CategoryAttrMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CategoryMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtProviderGoodsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemImageService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.dataloader.DataLoader;
import org.dataloader.DataLoaderFactory;
import org.dataloader.DataLoaderOptions;
import org.dataloader.DataLoaderRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @since 2021/12/31
 * @deprecated 废弃
 */
@Component
@Slf4j
public class WdtItemSyncTask {

    final private RefreshConfig refreshConfig;
    final private WdtGateway wdtGateway;
    final private IItemService itemService;
    final private IItemSkuService itemSkuService;
    final private IItemImageService itemImageService;
    private final AtomicBoolean executing = new AtomicBoolean(false);
    private final ConcurrentMap<String, Long> itemIdMap = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, Long> itemSkuIdMap = new ConcurrentHashMap<>();
    private DataLoader<String, Long> itemIdLoader;
    private DataLoader<String, Long> itemSkuIdLoader;
    private DataLoaderRegistry dataLoaderRegistry;
    private ScheduledExecutorService scheduledExecutorService;
    @Autowired
    private CategoryMapper categoryMapper;
    @Autowired
    private CategoryAttrMapper categoryAttrMapper;
    @Autowired
    private BrandGateway brandGateway;
    @Autowired
    private WdtProviderGoodsMapper wdtProviderGoodsMapper;
    @Autowired
    private ProviderGateway providerGateway;

    @Autowired
    public WdtItemSyncTask(RefreshConfig refreshConfig,
            WdtGateway wdtGateway, IItemService itemService,
            IItemSkuService itemSkuService,
            IItemImageService itemImageService) {
        this.refreshConfig = refreshConfig;
        this.wdtGateway = wdtGateway;
        this.itemService = itemService;
        this.itemSkuService = itemSkuService;
        this.itemImageService = itemImageService;
    }

    /**
     * 清洗从旺店通拉取回来的数据，将清洗后的数据同步到我们自己的平台商品表（半个小时一次，时间跟）
     */
//    @Scheduled(cron = "-")
    @XxlJob("WdtItemSyncTask")
    public void doTask() {
        if (!executing.compareAndSet(false, true)) {
            log.warn("当前任务已在执行中");
            return;
        }

        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        initDataLoader();

        final GoodsAPI goodsAPI = wdtGateway.goodsAPI();
        LocalDateTime cursorTime = LocalDateTime.now();
        final DateTimeFormatter normDatetimeFormatter = DatePattern.NORM_DATETIME_FORMATTER;
        final int cumulativeEmptyThreshold = 6;
        int cumulativeEmptyCounter = 0;
        int totalCount = 0;
        while (true) {
            if (cumulativeEmptyCounter >= cumulativeEmptyThreshold) {
                log.info("连续" + cumulativeEmptyCounter + "次没有拉取到对应时段的数据，认为前面已经没有数据了，终止脚本");
                break;
            }
            LocalDateTime startTime = cursorTime.minusDays(30);
            LocalDateTime endTime = cursorTime;
            int pageSize = 100;
            int pageIndex = 0;
            while (true) {
                final Pager pager = new Pager(pageSize, pageIndex, false);
                final GoodsSearchRequest goodsSearchRequest = new GoodsSearchRequest();
                goodsSearchRequest.setStartTime(startTime.format(normDatetimeFormatter));
                goodsSearchRequest.setEndTime(endTime.format(normDatetimeFormatter));

                GoodsSearchResponse searchResponse;
                try {
                    searchResponse = goodsAPI.search(goodsSearchRequest, pager);
                    if (CollUtil.isEmpty(searchResponse.getGoodsInfos())) {
                        if (pageIndex == 0) {
                            cumulativeEmptyCounter++;
                        }
                        break;
                    }
                    cumulativeEmptyCounter = 0;
                    final int size = searchResponse.getGoodsInfos().size();
                    totalCount += size;
                    CompletableFuture<?>[] futures = new CompletableFuture[searchResponse
                            .getGoodsInfos().size()];
                    for (int i = 0; i < searchResponse.getGoodsInfos().size(); i++) {
                        final GoodsSearchResponse.GoodsSearchGoodsDto goodsInfo = searchResponse
                                .getGoodsInfos().get(i);
                        futures[i] = saveGoodsInfo(goodsInfo);
                    }
                    for (int i = 0; i < futures.length; i++) {
                        final GoodsSearchResponse.GoodsSearchGoodsDto goodsInfo = searchResponse
                                .getGoodsInfos().get(i);
                        final CompletableFuture<?> future = futures[i];
                        try {
                            future.join();
                        } catch (Exception e) {
                            log.error("保存商品信息时遇到异常 goodsInfo={}", goodsInfo, e);
                        }
                    }
                } catch (WdtErpException e) {
                    if (Optional.ofNullable(e.getCause()).map(Throwable::getMessage)
                            .filter(msg -> StringUtil.equals(msg, "超过每分钟最大调用频率限制,请稍后重试"))
                            .isPresent()) {
                        //被限频以后休眠30S再继续拉取
                        ThreadUtil.sleep(30000);
                        continue;
                    }
                }
                pageIndex++;
            }
            cursorTime = startTime;
        }

        cleanLoader();
        executing.compareAndSet(true, false);

        stopWatch.stop();
        log.info("同步后端商品数据完成，耗时{}ms，总计拉取{}", stopWatch.getTotalTimeMillis(), totalCount);
    }

    private CompletableFuture<Void> saveGoodsInfo(
            GoodsSearchResponse.GoodsSearchGoodsDto goodsInfo) {
        if (StringUtil.isBlank(goodsInfo.getGoodsNo())) {
            log.warn("数据缺少商品编号 goodsInfo={}", goodsInfo);
            return CompletableFuture.completedFuture(null);
        }
        return getItemIdLoader().load(goodsInfo.getGoodsNo()).thenAccept(itemId -> {
            if (itemId != null) {
                log.info("商品已存在 goodsNo={} existItemId={}", goodsInfo.getGoodsNo(), itemId);
                return;
            }

            final Item item = assembleItem(goodsInfo);
            final long itemIdCreatedByOthers;
            try {
                itemIdCreatedByOthers = saveItem(item);
                if (itemIdCreatedByOthers > 0) {
                    log.warn("商品在别的线程被创建 goodsNo={} existItemId={}", goodsInfo.getGoodsNo(),
                            itemIdCreatedByOthers);
                    return;
                }
            } catch (Exception e) {
                log.error("保存商品时遇到异常 item={}", item, e);
                return;
            }

            if (CollUtil.isNotEmpty(goodsInfo.getSpecDtos())) {
                List<ItemImage> newItemImages = Lists.newArrayList();
                List<ItemSku> newItemSkus = Lists.newArrayList();
                final CompletableFuture<?>[] futures = new CompletableFuture[goodsInfo.getSpecDtos()
                        .size()];
                for (int i = 0; i < goodsInfo.getSpecDtos().size(); i++) {
                    final GoodsSearchResponse.GoodsSearchSpecDto specDto = goodsInfo.getSpecDtos()
                            .get(i);
                    if (StringUtil.isBlank(specDto.getSpecNo())) {
                        continue;
                    }
                    futures[i] = getItemSkuIdLoader().load(specDto.getSpecNo())
                            .thenAccept(skuId -> {
                                if (skuId != null) {
                                    log.warn("SKU已存在 specNo={} itemId={} existItemSkuId={}",
                                            specDto.getSpecNo(), item.getId(), skuId);
                                    return;
                                }
                                final ItemSku itemSku = assembleItemSku(item, specDto);
                                newItemSkus.add(itemSku);
                                if (StringUtil.isNotBlank(specDto.getImgUrl())) {
                                    final ItemImage itemImage = new ItemImage();
                                    itemImage.setItemId(item.getId());
                                    itemImage.setImageUrl(specDto.getImgUrl());
                                    itemImage.setType(ItemImageType.COMMON);
                                    newItemImages.add(itemImage);
                                }
                            });
                }
                CompletableFuture.allOf(futures).thenRun(() -> {
                    try {
                        if (!newItemImages.isEmpty()) {
                            newItemImages.get(0).setIsMain(1);
                            itemImageService.saveBatch(newItemImages);
                        }
                        if (!newItemSkus.isEmpty()) {
                            for (ItemSku itemSku : newItemSkus) {
                                try {
                                    final long skuIdCreatedByOthers = saveSku(itemSku);
                                    if (skuIdCreatedByOthers != 0) {
                                        log.warn(
                                                "相同规格编码的SKU已经被创建了 skuCode={} skuIdCreatedByOthers={}",
                                                itemSku.getSkuCode(), skuIdCreatedByOthers);
                                    }
                                } catch (Exception e) {
                                    log.error("保存商品SKU时遇到异常 itemSku={}", itemSku, e);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("保存商品图片及SKU时遇到异常 itemId:{} itemImages:{} itemSkus:{}", itemId,
                                newItemImages, newItemSkus, e);
                    }
                }).join();
            }
        });

    }

    @NonNull
    private ItemSku assembleItemSku(Item item, GoodsSearchResponse.GoodsSearchSpecDto specDto) {
        final ItemSku itemSku = new ItemSku();
        itemSku.setItemId(item.getId());
        itemSku.setProviderSpecifiedCode("");
        itemSku.setCostPrice(specDto.getWholesalePrice());
        itemSku.setSalePrice(specDto.getRetailPrice());
        itemSku.setSkuCode(specDto.getSpecNo());
        itemSku.setKingDeeId("");
        itemSku.setBarCode(specDto.getBarcode());
        if (StringUtil.isNotBlank(specDto.getSpecCreated())) {
            try {
                itemSku.setCreatedAt(
                        DateUtil.toEpochSecond(DateUtil.parse(specDto.getSpecCreated())));
            } catch (Exception e) {
                itemSku.setCreatedAt(DateUtil.currentTime());
            }
        }
        if (StringUtil.isNotBlank(specDto.getSpecModified())) {
            try {
                itemSku.setUpdatedAt(Long.parseLong(specDto.getSpecModified()) / 1000);
            } catch (Exception e) {
                itemSku.setUpdatedAt(DateUtil.currentTime());
            }
        }
        return itemSku;
    }

    @NonNull
    private Item assembleItem(GoodsSearchResponse.GoodsSearchGoodsDto goodsInfo) {
        final Category category = getCategory(goodsInfo.getClassName());
        final Long brandId = brandGateway.getBrandId(goodsInfo.getBrandName());
        final Long providerId = queryProviderIdByWdtProviderGoods(goodsInfo);

        final Item item = new Item();
        item.setName(goodsInfo.getGoodsName());
        item.setShortName(goodsInfo.getShortName());
        item.setPartnerProviderItemSn("");
        item.setCategoryId(category != null ? category.getId() : 0L);
        item.setBrandId(brandId);
        item.setProviderId(providerId);
        item.setProviderSpecifiedCode("");
        item.setStatus(0);
        item.setStatusRemark(goodsInfo.getRemark());
        item.setEstimateSaleTime(0L);
        item.setCode(goodsInfo.getGoodsNo());
        item.setIsGift(0);
        item.setParentItemId(0L);
        item.setParentCode("");
        item.setDeletedAt(0L);
        if (StringUtil.isNotBlank(goodsInfo.getGoodsCreated())) {
            try {
                item.setCreatedAt(
                        DateUtil.toEpochSecond(DateUtil.parse(goodsInfo.getGoodsCreated())));
            } catch (Exception e) {
                item.setCreatedAt(DateUtil.currentTime());
            }
        }
        if (StringUtil.isNotBlank(goodsInfo.getGoodsModified())) {
            try {
                item.setUpdatedAt(Long.parseLong(goodsInfo.getGoodsModified()) / 1000);
            } catch (Exception e) {
                item.setUpdatedAt(DateUtil.currentTime());
            }
        }
        item.setSource(DataSource.WDT);
        return item;
    }

    private Long queryProviderIdByWdtProviderGoods(
            GoodsSearchResponse.GoodsSearchGoodsDto goodsInfo) {
        final LambdaQueryWrapper<WdtProviderGoods> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(WdtProviderGoods::getGoodsNo, goodsInfo.getGoodsNo());
        queryWrapper.orderByDesc(WdtProviderGoods::getCreatedAt);
        final WdtProviderGoods wdtProviderGoods = wdtProviderGoodsMapper
                .selectOne(queryWrapper);
        if (wdtProviderGoods == null) {
            return 0L;
        }
        final String providerNo = wdtProviderGoods.getProviderNo();
        final Provider provider = providerGateway.getByNo(providerNo);
        if (provider == null) {
            return 0L;
        }
        return provider.getId();
    }

    private Category getCategory(String className) {
        final LambdaQueryWrapper<Category> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Category::getName, className);
        return categoryMapper.selectOne(queryWrapper);
    }

    /**
     * 保存商品SKU
     *
     * @param itemSku SKU实体模型
     * @return 如果创建时SKU已存在，则直接返回ID
     */
    private long saveSku(ItemSku itemSku) {
        try {
            final Long skuId = itemSkuIdMap.computeIfAbsent(itemSku.getSkuCode(), skuCode -> {
                itemSkuService.save(itemSku);
                return itemSku.getId();
            });
            if (Objects.isNull(itemSku.getId())) {
                return skuId;
            }
            return 0;
        } catch (DuplicateKeyException ignored) {
            log.warn("保存商品SKU失败，SKU编码已经存在， itemSku={}", itemSku);
            return -1;
        }
    }

    /**
     * @param item 商品实体模型
     * @return 如果创建时商品已存在，则直接返回ID
     */
    private long saveItem(Item item) {
        while (true) {
            try {
                final Long itemId = itemIdMap.computeIfAbsent(item.getCode(), code -> {
                    itemService.save(item);
                    return item.getId();
                });
                if (Objects.isNull(item.getId())) {
                    return itemId;
                }
                return 0;
            } catch (DuplicateKeyException ignored) {
                //因为商品表有两个唯一约束，这边查询商品编码是否存在来区分
                final Item one = itemService.lambdaQuery().eq(Item::getCode, item.getCode()).one();
                if (Objects.isNull(one)) {
                    continue;
                }
                itemIdMap.put(item.getCode(), one.getId());
                return one.getId();
            }
        }
    }

    private Executor getExecutor() {
        return ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL);
    }

    private void cleanLoader() {
        this.scheduledExecutorService.shutdown();
        this.scheduledExecutorService = null;
        this.dataLoaderRegistry = null;
        itemIdLoader = null;
        itemSkuIdLoader = null;
    }

    private void dispatchLoader() {
        this.dataLoaderRegistry.dispatchAll();
    }

    private void initDataLoader() {
        if (dataLoaderRegistry == null) {
            scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();
            this.dataLoaderRegistry = DataLoaderRegistry.newRegistry()
                    .register("itemIdLoader", getItemIdLoader())
                    .register("itemSkuIdLoader", getItemSkuIdLoader())
                    .build();
            scheduledExecutorService.scheduleWithFixedDelay(() -> {
                this.dataLoaderRegistry.dispatchAll();
            }, 0, 100, TimeUnit.MILLISECONDS);
        }
    }

    private DataLoader<String, Long> getItemIdLoader() {
        if (itemIdLoader == null) {
            final DataLoaderOptions options = DataLoaderOptions.newOptions();
            options.setMaxBatchSize(100);
            return itemIdLoader = DataLoaderFactory.newMappedDataLoader(keys -> CompletableFuture
                    .supplyAsync(() -> batchGetItemIds(keys), getExecutor()), options);
        }
        return itemIdLoader;
    }

    private DataLoader<String, Long> getItemSkuIdLoader() {
        if (itemSkuIdLoader == null) {
            final DataLoaderOptions options = DataLoaderOptions.newOptions();
            options.setMaxBatchSize(100);
            return itemSkuIdLoader = DataLoaderFactory.newMappedDataLoader(keys -> CompletableFuture
                    .supplyAsync(() -> batchGetItemSkuIds(keys), getExecutor()), options);
        }
        return itemSkuIdLoader;
    }

    private Map<String, Long> batchGetItemSkuIds(Set<String> skuCodes) {
        final Set<String> missingKeys = Sets.newHashSet();
        final Map<String, Long> result = Maps.newHashMap();
        for (String skuCode : skuCodes) {
            final Long id = itemSkuIdMap.get(skuCode);
            if (Objects.nonNull(id)) {
                result.put(skuCode, id);
            } else {
                missingKeys.add(skuCode);
            }
        }
        if (!missingKeys.isEmpty()) {
            itemSkuService.lambdaQuery().select(ItemSku::getSkuCode, ItemSku::getId)
                    .in(ItemSku::getSkuCode, missingKeys).list().forEach(sku -> {
                result.put(sku.getSkuCode(), sku.getId());
                itemSkuIdMap.put(sku.getSkuCode(), sku.getId());
            });
        }
        return result;
    }

    private Map<String, Long> batchGetItemIds(Set<String> itemCodes) {
        final Set<String> missingKeys = Sets.newHashSet();
        final Map<String, Long> result = Maps.newHashMap();
        for (String itemCode : itemCodes) {
            final Long id = itemIdMap.get(itemCode);
            if (Objects.nonNull(id)) {
                result.put(itemCode, id);
            } else {
                missingKeys.add(itemCode);
            }
        }
        if (!missingKeys.isEmpty()) {
            itemService.lambdaQuery().select(Item::getId, Item::getCode)
                    .in(Item::getCode, itemCodes)
                    .list().forEach(item -> {
                result.put(item.getCode(), item.getId());
                itemIdMap.put(item.getCode(), item.getId());
            });
        }
        return result;
    }


}
