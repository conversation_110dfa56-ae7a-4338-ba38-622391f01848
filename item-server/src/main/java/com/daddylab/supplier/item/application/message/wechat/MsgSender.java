package com.daddylab.supplier.item.application.message.wechat;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.message.QyMsgConfig;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.config.QyWeixinDaddyErpProperties;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatMsg;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWechatMsgService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.QyWeixinService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.QyWeixinFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.SendMsgQyWeixinResult;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.SendQyWeixinMsgParam;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import io.jsonwebtoken.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2022/6/7 1:46 下午
 */
@Slf4j
@Service
public class MsgSender {

  @Autowired private QyWeixinFeignClient qyWeixinFeignClient;

  @Autowired private QyWeixinService qyWeixinService;

  @Autowired IWechatMsgService iWechatMsgService;

  @Autowired QyWeixinDaddyErpProperties qyWeixinDaddyErpProperties;

  @Autowired QyMsgConfig qyMsgConfig;

  public void sendByIdList(List<Long> idList) {
    List<WechatMsg> list =
        iWechatMsgService.lambdaQuery().in(WechatMsg::getId, idList).select().list();
    sendByList(list);
  }

  public void send(WechatMsg wechatMsg) {
    ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL)
        .execute(
            () -> {
              final boolean isProd = ApplicationContextUtil.isActiveProfile(qyMsgConfig.getRealSendEnvs());
              Integer state;
              try {
                if (isProd) {
                  state = sendTextCardMsg(wechatMsg);
                } else {
                  Alert.text(
                      MessageRobotCode.QY_MSG_MOCK,
                      String.format(
                          "模板编码：%s\n" + "标题: %s\n" + "内容: %s\n" + "接收人: %s\n" + "链接: %s",
                          wechatMsg.getTemplateCode(),
                          wechatMsg.getTitle(),
                          wechatMsg.getContent(),
                          wechatMsg.getRecipient(),
                          wechatMsg.getLink()));
                  state = 1;
                }
              } catch (Exception e) {
                state = -1;
              }
              wechatMsg.setState(state);
              iWechatMsgService.saveOrUpdate(wechatMsg);
            });
  }

  public void sendByList(List<WechatMsg> wechatMsgList) {
    List<WechatMsg> collect =
        wechatMsgList.stream()
            .peek(
                wechatMsg -> {
                  int state = sendTextCardMsg(wechatMsg);
                  wechatMsg.setState(state);
                })
            .collect(Collectors.toList());

    iWechatMsgService.saveOrUpdateBatch(collect);
  }

  public void sendText(WechatMsg wechatMsg) {
    Integer state = sendMsgWithText(wechatMsg);
    wechatMsg.setState(state);
    iWechatMsgService.updateById(wechatMsg);
  }

  /**
   * 发送卡片文本消息 TextCard
   *
   * @param wechatMsg
   * @return
   */
  @Retryable(
      value = {RuntimeException.class},
      maxAttempts = 3,
      backoff = @Backoff(delay = 10000, multiplier = 1.5))
  public Integer sendTextCardMsg(WechatMsg wechatMsg) {
    try {
      Assert.hasText(wechatMsg.getLink(), "文本卡片消息，链接不能为空");
      Assert.hasText(wechatMsg.getTitle(), "文本卡片消息，标题不能为空");
      Assert.hasText(wechatMsg.getContent(), "文本卡片消息，内容不能为空");
      String token = qyWeixinService.getTokenWithCache();
      SendQyWeixinMsgParam param = new SendQyWeixinMsgParam();

      String title = wechatMsg.getTitle();
      String desc = "<div class=\"normal\">" + wechatMsg.getContent() + "</div>";
      String link = wechatMsg.getLink();
      SendQyWeixinMsgParam.TextCard textCard = SendQyWeixinMsgParam.TextCard.of(title, desc, link);
      param.setTextCard(textCard);
      param.setAgentid(qyWeixinDaddyErpProperties.getAgentid());
      param.setMsgtype("textcard");
      param.setTouser(getRecipient(wechatMsg));
      final SendMsgQyWeixinResult sendMsgQyWeixinResult =
          qyWeixinFeignClient.sendMessage(token, param);
      if (!sendMsgQyWeixinResult.isSendSuccess()) {
        log.error(
            "发送企微卡片文本消息失败.id:{},res:{}", wechatMsg.getId(), sendMsgQyWeixinResult.getErrmsg());
        return -1;
      } else {
        return 1;
      }
    } catch (Exception e) {
      log.error("发送企微卡片文本消息异常.id:{}", wechatMsg.getId(), e);
      return -1;
      //            throw new RuntimeException("发送企微卡片文本消息失败.msg:" + wechatMsg.getMsgSummary());
    }
  }

  private String getRecipient(WechatMsg wechatMsg) {
    final String recipient = wechatMsg.getRecipient();
    if (CollectionUtil.isEmpty(qyMsgConfig.getWhiteList())) {
      return recipient;
    }
    return StringUtil.split(recipient, ",").stream()
        .filter(qyMsgConfig.getWhiteList()::contains)
        .collect(Collectors.joining(","));
  }

  @Recover
  public Integer recover(RuntimeException e) {
    Alert.text(MessageRobotCode.GLOBAL, e.getMessage());
    return -1;
  }

  /**
   * 发送普通文本消息
   *
   * @param wechatMsg
   * @return
   */
  public Integer sendMsgWithText(WechatMsg wechatMsg) {
    try {
      String token = qyWeixinService.getTokenWithCache();
      SendQyWeixinMsgParam param = new SendQyWeixinMsgParam();

      SendQyWeixinMsgParam.Text text = new SendQyWeixinMsgParam.Text();
      String content =
          StringUtils.hasText(wechatMsg.getTitle())
              ? (wechatMsg.getTitle() + "\n" + wechatMsg.getContent())
              : wechatMsg.getContent();
      String link = wechatMsg.getLink();
      String s;
      if (StringUtils.hasText(link)) {
        s = "<a href=" + link + ">" + content + "</a>";
      } else {
        s = content;
      }
      text.setContent(s);

      param.setText(text);
      param.setAgentid(qyWeixinDaddyErpProperties.getAgentid());
      param.setMsgtype("text");
      param.setTouser(getRecipient(wechatMsg));
      final SendMsgQyWeixinResult sendMsgQyWeixinResult =
          qyWeixinFeignClient.sendMessage(token, param);
      if (!sendMsgQyWeixinResult.isSendSuccess()) {
        log.error("发送文本消息失败.id:{},res:{}", wechatMsg.getId(), sendMsgQyWeixinResult.getErrmsg());
        return -1;
      } else {
        return 1;
      }
    } catch (Exception e) {
      log.error("发送文本消息失败,id:{}", wechatMsg.getId(), e);
      return -1;
    }
  }

  public void retryFailedList(Long start, Long end) {
    Map<String, List<WechatMsg>> collect =
        iWechatMsgService
            .lambdaQuery()
            .between(WechatMsg::getCreatedAt, start, end)
            .eq(WechatMsg::getState, -1)
            .list()
            .stream()
            .collect(Collectors.groupingBy(WechatMsg::getRecipient));
    collect.forEach(
        (k, list) -> {
          for (WechatMsg wechatMsg : list) {
            try {
              Integer state = sendTextCardMsg(wechatMsg);
              wechatMsg.setState(state);
              iWechatMsgService.updateById(wechatMsg);
            } catch (Exception e) {
              log.error("retry msg fail", e);
            } finally {
              try {
                TimeUnit.SECONDS.sleep(3);
              } catch (InterruptedException e) {
                log.error("retry msg sleep fail", e);
              }
            }
          }
          log.info("retry msg finished. user:{}.size:{}", k, list.size());
        });
    log.info("retry msg finished. all finished");
  }

  public void sendMsgToSevenUp(String msg) {
    WechatMsg wechatMsg = new WechatMsg();
    wechatMsg.setRecipient(GlobalConstant.SEVEN_UP + "," + GlobalConstant.YUN_TUN);
    wechatMsg.setContent(msg);
    wechatMsg.setTitle("\uD83E\uDD17尊敬的陛下~~~");
    wechatMsg.setLink("");
    sendMsgWithText(wechatMsg);
  }

  public void sendMsgToSomeone(String msg, Collection<String> qwUserIds) {
    WechatMsg wechatMsg = new WechatMsg();
    wechatMsg.setRecipient(String.join(StrUtil.COMMA, qwUserIds));
    wechatMsg.setContent(msg);
    wechatMsg.setTitle("\uD83E\uDD17尊敬的陛下~~~");
    wechatMsg.setLink("");
    sendMsgWithText(wechatMsg);
  }
}
