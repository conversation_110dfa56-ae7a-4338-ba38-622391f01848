package com.daddylab.supplier.item.controller.item.dto.detail;

import com.daddylab.supplier.item.common.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13 12:05 下午
 * @description
 */
@Data
@ApiModel("商品详情-价格部分返回封装")
public class ItemDetailPriceVo {

    /**
     * 采购价
     */
    List<ProcurementPrice> procurementPrices;

    /**
     * 售价
     */
    List<SalesPrice> salesPrices;


    @Data
    @ApiModel("采购价格封装")
    public static class ProcurementPrice {
        @ApiModelProperty("价格id")
        private Long id;
        @ApiModelProperty("采购价名称")
        private String name;
        @ApiModelProperty("采购价价格")
        private String price;
        @ApiModelProperty("备注")
        private String remark;
        @ApiModelProperty("是否是自定义价格")
        private Boolean isCustomize;

        private Long startTime;

        private Long endTime;

        private Long updatedAt;

        public Long getEndTime() {
            if (GlobalConstant.MAX_TIMESTAMP.equals(endTime)) {
                return null;
            }
            return endTime;
        }

        private Integer type;

        private Integer isLongTerm;

        private Integer isMain;

        private Long createdAt;
    }


    @Data
    @ApiModel("售价封装")
    public static class SalesPrice {
        @ApiModelProperty("价格id")
        private Long id;
        @ApiModelProperty("售价名称")
        private String name;
        @ApiModelProperty("售价价格")
        private String price;
        @ApiModelProperty("是否是自定义价格")
        private Boolean isCustomize;

        private Integer type;

        private Integer isLongTerm;

        private Integer isMain;

        private Long createdAt;

    }


}
