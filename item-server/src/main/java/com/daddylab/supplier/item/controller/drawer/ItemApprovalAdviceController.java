package com.daddylab.supplier.item.controller.drawer;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.drawer.ItemApprovalAdviceBizService;
import com.daddylab.supplier.item.common.domain.dto.IdCmd;
import com.daddylab.supplier.item.domain.drawer.vo.ItemApprovalAdvicesVO;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/10/28
 */
@Api(value = "商品审批意见API", tags = "商品审批意见API")
@RestController
@AllArgsConstructor
@RequestMapping("/item-approval-advice")
public class ItemApprovalAdviceController {

    private final ItemApprovalAdviceBizService itemApprovalAdviceBizService;

    /**
     * 新增审批意见
     *
     * @param addCmd 商品审核意见新增命令模型
     */
    @PostMapping("/add")
    SingleResponse<Long> add(@RequestBody @Validated AddCmd addCmd) {
        return itemApprovalAdviceBizService.add(addCmd.getItemId(), addCmd.getType(),
                addCmd.getContent());
    }

    /**
     * 移除审批意见
     *
     * @param adviceIdWrapper 审批意见ID
     */
    @PostMapping("/remove")
    Response remove(@RequestBody @Validated IdCmd adviceIdWrapper) {
        return itemApprovalAdviceBizService.remove(adviceIdWrapper.getId());
    }

    /**
     * 查询商品审批意见视图模型
     *
     * @param itemIdWrapper 商品ID命令模型
     */
    @GetMapping("getAllByItemId")
    SingleResponse<ItemApprovalAdvicesVO> getAllByItemId(
            @RequestBody @Validated IdCmd itemIdWrapper) {
        return itemApprovalAdviceBizService.getAllByItemId(itemIdWrapper.getId());
    }


    /**
     * 删除某个商品全部的审批意见
     *
     * @param itemIdWrapper 商品ID命令模型
     */
    @PostMapping("/deleteAllByItemId")
    public Response deleteAll(@RequestBody @Validated IdCmd itemIdWrapper) {
        return itemApprovalAdviceBizService.deleteAllByItemId(itemIdWrapper.getId());
    }
}
