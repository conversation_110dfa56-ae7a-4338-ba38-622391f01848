package com.daddylab.supplier.item.infrastructure.skywalking;

import java.io.IOException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * <AUTHOR>
 * @since 2022/7/13
 */
public class TraceFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
            FilterChain filterChain) throws ServletException, IOException {

        try {
            response.addHeader("Tid", TraceContext.traceId());
        } catch (Exception ignored) {
        }
        filterChain.doFilter(request, response);
    }
}
