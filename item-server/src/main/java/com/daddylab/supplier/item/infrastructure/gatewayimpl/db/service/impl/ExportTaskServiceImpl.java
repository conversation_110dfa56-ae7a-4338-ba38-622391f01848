package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ExportTaskMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IExportTaskService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@Service
public class ExportTaskServiceImpl extends DaddyServiceImpl<ExportTaskMapper, ExportTask> implements IExportTaskService {

}
