package com.daddylab.supplier.item.application.staff;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.aws.service.AwsBoService;
import com.daddylab.supplier.item.application.aws.types.ActOrgExtDeptBO;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.domain.staff.vo.DepartmentHeadBriefVO;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DadDept;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DadPost;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DadStaff;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDadDeptService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDadPostService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDadStaffService;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Class DadStaffServiceImpl @Date 2022/6/1上午10:20 <AUTHOR>
 */
@Transactional(propagation = Propagation.REQUIRES_NEW)
@Service
@DS("authDb")
public class StaffServiceImpl implements StaffService {

    @Autowired
    private IDadStaffService dadStaffService;
    @Autowired
    private IDadPostService dadPostService;
    @Autowired
    private IDadDeptService dadDeptService;
    @Autowired
    private AwsBoService awsBoService;

    LoadingCache<Long, Optional<StaffBrief>> staffBriefCache =
            Caffeine.newBuilder()
                    .expireAfterWrite(Duration.ofSeconds(600))
                    .build(this::getStaffBriefFromDb);

    LoadingCache<String, Optional<StaffBrief>> staffBriefCacheByNickname =
            Caffeine.newBuilder()
                    .expireAfterWrite(Duration.ofSeconds(600))
                    .build(this::selectStaffBriefByNickname);

    @Override
    public DepartmentHeadBriefVO getStaffDepartmentHeadBriefVO(Long userId) {
        final DadStaff dadStaff = Objects.requireNonNull(getDadStaff(userId), "员工信息查询异常:" + userId);
        final Long deptId = dadStaff.getDeptId();
        final DadDept dadDept = dadDeptService.getById(deptId);
        final ActOrgExtDeptBO actOrgExtDeptBO = awsBoService.getActOrgExtDeptBO(String.valueOf(dadDept.getOaId()));
        final DepartmentHeadBriefVO departmentHeadBriefVO = new DepartmentHeadBriefVO();
        if (actOrgExtDeptBO != null) {
            getStaffByLoginName(actOrgExtDeptBO.getBMKJID()).ifPresent(departmentHeadBriefVO::setAccountant);
            getStaffByLoginName(actOrgExtDeptBO.getBMZGID()).ifPresent(departmentHeadBriefVO::setLeader);
            getStaffByLoginName(actOrgExtDeptBO.getBMJLID()).ifPresent(departmentHeadBriefVO::setManager);
            getStaffByLoginName(actOrgExtDeptBO.getBMFGID()).ifPresent(departmentHeadBriefVO::setDivisionalHead);
        }
        return departmentHeadBriefVO;
    }

    private DadStaff getDadStaff(Long userId) {
        if (userId == null || userId == 0L) {
            return null;
        }
        return dadStaffService.lambdaQuery()
                .eq(DadStaff::getUid, userId)
                .last("limit 1")
                .oneOpt()
                .orElseThrow(() -> ExceptionPlusFactory.bizException(
                        ErrorCode.DATA_NOT_FOUND, "员工ID不存在:" + userId));
    }

    /**
     * 根据userId 获取职工简单信息
     *
     * @param userId 用户ID
     */
    @Override
    public Optional<StaffBrief> getStaffBrief(Long userId) {
        return getStaffBrief(userId, false);
    }

    @Override
    public Optional<StaffBrief> getStaffBrief(Long userId, boolean cache) {
        if (userId == null) {
            return Optional.empty();
        }
        if (userId == 0L) {
            return Optional.of(StaffBrief.systemUser());
        }
        return cache ? staffBriefCache.get(userId) : getStaffBriefFromDb(userId);
    }

    @NonNull
    private Optional<StaffBrief> getStaffBriefFromDb(Long userId) {
        if (userId == null) {
            return Optional.empty();
        }
        return dadStaffService
                .lambdaQuery()
                .eq(DadStaff::getUid, userId)
                .oneOpt()
                .map(this::convertStaffBrief);
    }

    @NonNull
    private StaffBrief convertStaffBrief(DadStaff v) {
        final StaffBrief staffBrief = new StaffBrief();
        staffBrief.setUserId(v.getUid());
        staffBrief.setRealName(v.getRealName());
        staffBrief.setNickname(v.getNickname());
        staffBrief.setQwUserId(v.getQwUserId());
        if (v.getPostId() != null && v.getPostId() > 0) {
            final DadPost post = dadPostService.getById(v.getPostId());
            staffBrief.setPost(Objects.isNull(post) ? "" : post.getName());
        }
        return staffBrief;
    }

    @Override
    public List<StaffBrief> getStaffBriefList(List<Long> userIds) {
        if (CollectionUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return dadStaffService.lambdaQuery().in(DadStaff::getUid, userIds).list().stream()
                .map(this::convertStaffBrief)
                .collect(Collectors.toList());
    }

    @Override
    public DadStaffVO getStaff(Long userId) {
        DadStaff dadStaff =
                dadStaffService.getOne(
                        new LambdaQueryWrapper<DadStaff>() {
                            {
                                eq(DadStaff::getUid, userId);
                                last("limit 1");
                            }
                        });
        if (Objects.isNull(dadStaff)) {
            return null;
        }

        DadStaffVO dadStaffVO = DadStaffConverter.INSTANCE.dadStaffToVo(dadStaff);
        // 岗位信息
        DadPost dadPost = dadPostService.getById(dadStaff.getPostId());
        if (Objects.nonNull(dadPost)) {
            dadStaffVO.setPostName(dadPost.getName());
        }
        // 部门信息
        dadStaffVO.setDeptName(getAllParentDeptName(dadStaff.getDeptId(), "-"));

        return dadStaffVO;
    }

    @Override
    public List<DadStaffVO> getStaffList(List<Long> userIds) {
        if (CollectionUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        List<DadStaff> dadStaffs =
                dadStaffService
                        .getBaseMapper()
                        .selectList(
                                new LambdaQueryWrapper<DadStaff>() {
                                    {
                                        in(DadStaff::getUid, userIds);
                                    }
                                });
        return getDadStaffVOS(dadStaffs);
    }

    @Override
    public Map<Long, DadStaffVO> getStaffMap(List<Long> userIds) {
        if (CollectionUtil.isEmpty(userIds)) {
            return new HashMap<>(1);
        }
        List<DadStaffVO> staffList = getStaffList(userIds);

        Map<Long, DadStaffVO> map = new HashMap<>((int) (staffList.size() / 0.75 + 1));
        for (DadStaffVO dadStaffVO : staffList) {
            map.put(dadStaffVO.getUserId(), dadStaffVO);
        }
        return map;
    }

    @NonNull
    private List<DadStaffVO> getDadStaffVOS(List<DadStaff> dadStaffs) {
        if (dadStaffs.isEmpty()) {
            return Collections.emptyList();
        }
        List<DadStaffVO> dadStaffVOS = DadStaffConverter.INSTANCE.dadStaffListToVo(dadStaffs);
        List<Long> postIds =
                dadStaffs.stream().map(DadStaff::getPostId).collect(Collectors.toList());
        List<DadPost> dadPosts = dadPostService.getBaseMapper().selectBatchIds(postIds);
        Map<Long, String> postNameMap =
                dadPosts.stream()
                        .collect(
                                Collectors.toMap(DadPost::getId, DadPost::getName, (k1, k2) -> k1));
        for (DadStaffVO dadStaffVO : dadStaffVOS) {
            if (postNameMap.containsKey(dadStaffVO.getPostId())) {
                dadStaffVO.setPostName(postNameMap.get(dadStaffVO.getPostId()));
            }
            dadStaffVO.setDeptName(getAllParentDeptName(dadStaffVO.getDeptId(), "-"));
        }
        return dadStaffVOS;
    }

    @Override
    public List<DadStaffVO> getStaffListByLoginName(List<String> loginNames) {
        loginNames = filterLoginNames(loginNames);
        if (CollectionUtil.isEmpty(loginNames)) {
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<DadStaff> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(DadStaff::getLoginName, loginNames);
        List<DadStaff> dadStaffs = dadStaffService.getBaseMapper().selectList(queryWrapper);
        return getDadStaffVOS(dadStaffs);
    }

    @NonNull
    private static List<String> filterLoginNames(List<String> loginNames) {
        return loginNames.stream()
                .filter(StringUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public Optional<StaffBrief> getStaffByLoginName(String loginName) {
        if (StringUtil.isBlank(loginName)) {
            return Optional.empty();
        }
        return dadStaffService
                .lambdaQuery()
                .eq(DadStaff::getLoginName, loginName)
                .last("limit 1")
                .oneOpt()
                .map(this::convertStaffBrief);
    }

    @Override
    public Optional<StaffBrief> getStaffByNickName(String nickName) {
        return staffBriefCacheByNickname.get(nickName);
    }

    @Override
    public List<StaffBrief> getStaffListByNickName(Collection<String> nickNames) {
        return dadStaffService
                .lambdaQuery()
                .in(DadStaff::getNickname, nickNames)
                .list().stream()
                .map(this::convertStaffBrief).collect(Collectors.toList());
    }

    @NonNull
    private Optional<StaffBrief> selectStaffBriefByNickname(String nickName) {
        if (StringUtil.isBlank(nickName)) {
            return Optional.empty();
        }
        return dadStaffService
                .lambdaQuery()
                .eq(DadStaff::getNickname, nickName)
                .last("limit 1")
                .oneOpt()
                .map(this::convertStaffBrief);
    }

    @Override
    public String getAllParentDeptName(Long id, String delimiter) {
        DadDept dadDept = dadDeptService.getById(id);
        List<String> deptNameList = new ArrayList<>();
        while (dadDept != null) {
            deptNameList.add(dadDept.getName());
            dadDept = dadDeptService.getById(dadDept.getPid());
        }
        Collections.reverse(deptNameList);
        return String.join(delimiter, deptNameList);
    }
}
