package com.daddylab.supplier.item.infrastructure.gatewayimpl.user;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@EqualsAndHashCode(of = "userId")
public class StaffInfo {

    /**
     * 员工ID
     */
    @JsonProperty("user_id")
    private Long userId;

    /**
     * 员工名称
     */
    @JsonProperty("real_name")
    private String userName;

    /**
     * 花名
     */
    private String nickname;

    /**
     * 员工手机号
     */
    @JsonProperty("mobile")
    private String mobile;

    @JsonProperty("email")
    private String email;

    @JsonProperty("login_name")
    private String loginName;

    /**
     * 状态 0:在职；1:离职
     */
    private Integer status;

    /**
     * 部门
     */
    private String dept;


}


