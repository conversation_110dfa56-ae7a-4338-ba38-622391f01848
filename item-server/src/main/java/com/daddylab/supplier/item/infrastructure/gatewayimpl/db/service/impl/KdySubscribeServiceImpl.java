package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.KdySubscribe;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.KdySubscribeMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IKdySubscribeService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 快刀云订阅记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Service
public class KdySubscribeServiceImpl extends DaddyServiceImpl<KdySubscribeMapper, KdySubscribe> implements IKdySubscribeService {

}
