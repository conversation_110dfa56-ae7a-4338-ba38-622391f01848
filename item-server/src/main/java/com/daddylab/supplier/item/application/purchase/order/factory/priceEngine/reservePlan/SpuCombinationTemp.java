package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.reservePlan;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.CommonCalculator;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.QuantityCombinedPriceBO;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.QuantityCombinedPriceVO;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.StatisticsQuantityByTradeNoDO;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseRandomSkuCombinationPrice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseRandomSkuCombinationPriceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderDetailWrapperService;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.hasBlank;
import static cn.hutool.core.text.CharSequenceUtil.isBlank;

/**
 * <AUTHOR> up
 * @date 2023年09月27日 5:17 PM
 */
@Slf4j
@Component
public class SpuCombinationTemp extends CombinationCalculateTemplate {

    @Resource
    IPurchaseRandomSkuCombinationPriceService purchaseRandomSkuCombinationPriceService;

    @Resource
    IWdtOrderDetailWrapperService iWdtOrderDetailWrapperService;

    @Resource
    WdtOrderDetailMapper wdtOrderDetailMapper;

    @Override
    protected void considerActivity(TimeBO timeBO) {
        log.info("ActivityRandomCombinationPrice doPriceProcess start");

        List<PurchaseRandomSkuCombinationPrice> list = getRandomCombinationPriceByType(timeBO, 2);
        if (CollUtil.isEmpty(list)) {
            log.info("ActivityRandomCombinationPrice doPriceProcess finish.purchase price is empty");
            return;
        }

        Semaphore semaphore = new Semaphore(4);
        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        list.forEach(val -> ThreadUtil.execute(PoolEnum.PURCHASE_POOL, () -> {
            try {
                semaphore.acquire();
                log.info("ActivityRandomCombinationPrice doPriceProcess start skuCode:{}", val.getCode());
                randomCombinationPriceHandler(val, timeBO.getOperateMonth(), 2);
                log.info("ActivityRandomCombinationPrice doPriceProcess finish skuCode:{}", val.getCode());
            } catch (Exception e) {
                log.error("ActivityRandomCombinationPrice doPriceProcess fail price:{}", JsonUtil.toJson(val), e);
            } finally {
                countDownLatch.countDown();
                semaphore.release();
            }
        }));

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("[{}] ActivityRandomCombinationPrice doPriceProcess await InterruptedException", timeBO.getOperateMonth(), e);
        }

        log.info("ActivityRandomCombinationPrice doPriceProcess finish");
    }

    @Override
    protected void considerDaily(TimeBO timeBO) {
        log.info("DailyRandomCombinationPrice doPriceProcess start");

        List<PurchaseRandomSkuCombinationPrice> list = getRandomCombinationPriceByType(timeBO, 1);
        if (CollUtil.isEmpty(list)) {
            log.info("DailySingleCombinationPrice doPriceProcess finish.purchase price is empty");
            return;
        }

        Semaphore semaphore = new Semaphore(4);
        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        list.forEach(val -> ThreadUtil.execute(PoolEnum.PURCHASE_POOL, () -> {
            try {
                semaphore.acquire();
                log.info("DailyRandomCombinationPrice doPriceProcess start skuCode:{}", val.getCode());
                randomCombinationPriceHandler(val, timeBO.getOperateMonth(), 1);
                log.info("DailyRandomCombinationPrice doPriceProcess finish skuCode:{}", val.getCode());
            } catch (Exception e) {
                log.error("DailyRandomCombinationPrice doPriceProcess fail price:{}", JsonUtil.toJson(val), e);
            } finally {
                countDownLatch.countDown();
                semaphore.release();
            }
        }));

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("[{}] DailyRandomCombinationPrice doPriceProcess await InterruptedException", timeBO.getOperateMonth(), e);
        }

        log.info("DailyRandomCombinationPrice doPriceProcess finish");
    }

    public List<PurchaseRandomSkuCombinationPrice> getRandomCombinationPriceByType(TimeBO timeBO, Integer priceType) {
        return purchaseRandomSkuCombinationPriceService.lambdaQuery()
                .eq(PurchaseRandomSkuCombinationPrice::getPriceType, priceType)
                .ge(priceType == 2, PurchaseRandomSkuCombinationPrice::getStartTime, timeBO.getMonthStartTime())
                .le(priceType == 2, PurchaseRandomSkuCombinationPrice::getEndTime, timeBO.getMonthEndTime())
                .select().list();
    }

    List<String> getSubCodeList(String codeStr) {
        if (isBlank(codeStr)) {
            return new LinkedList<>();
        }
        return Arrays.asList(codeStr.split("\\|"));
    }

    List<QuantityCombinedPriceBO> getPriceBoList(String priceInfo) {
        if (hasBlank(priceInfo)) {
            return new LinkedList<>();
        }
        return JsonUtil.parse(priceInfo, new TypeReference<List<QuantityCombinedPriceBO>>() {
        });
    }

    public void randomCombinationPriceHandler(PurchaseRandomSkuCombinationPrice priceEntity, String operateTime, Integer priceType) {
        List<String> skuCodeList = getSubCodeList(priceEntity.getCode());
        List<QuantityCombinedPriceBO> priceBoList = getPriceBoList(priceEntity.getPriceInfo());
        if (CollUtil.isEmpty(skuCodeList) || CollUtil.isEmpty(priceBoList)) {
            return;
        }
        Integer count = iWdtOrderDetailWrapperService.lambdaQuery()
                .eq(WdtOrderDetailWrapper::getOperateTime, operateTime)
                .eq(WdtOrderDetailWrapper::getType, 1)
                .in(WdtOrderDetailWrapper::getSkuCode, skuCodeList)
                .eq(priceEntity.getPlatform() != 0, WdtOrderDetailWrapper::getPlatformType, priceEntity.getPlatform())
                .ne(WdtOrderDetailWrapper::getTradeNo, "")
                .count();
        if (count <= 0) {
            return;
        }

        List<StatisticsQuantityByTradeNoDO> statisticsCountByTradeNoList;
        if (priceType == 1) {
            Set<BigDecimal> priceContainer = new HashSet<>();
            statisticsCountByTradeNoList = wdtOrderDetailMapper
                    .statisticsCountByTradeNo(null, skuCodeList, null, null, operateTime);
            updatePriceHandler(statisticsCountByTradeNoList, priceBoList, priceContainer);
        }
        if (priceType == 2) {
            statisticsCountByTradeNoList = wdtOrderDetailMapper
                    .statisticsCountByTradeNo(null, skuCodeList, priceEntity.getStartTime(), priceEntity.getEndTime(), operateTime);
            Set<BigDecimal> priceContainer = new HashSet<>();
            updatePriceHandler(statisticsCountByTradeNoList, priceBoList, priceContainer);
        }

    }

    private void updatePriceHandler(List<StatisticsQuantityByTradeNoDO> statisticsCountByTradeNoList,
                                    List<QuantityCombinedPriceBO> priceBoList, Set<BigDecimal> priceContainer) {
        // 数量-单价，首先根据组合价，初始化映射缓存
        Map<Integer, BigDecimal> priceCacheMap = new HashMap<>(8);
        priceBoList.forEach(val -> priceCacheMap.put(val.getQuantity(),
                val.getPrice().divide(new BigDecimal(val.getQuantity()), 6, RoundingMode.HALF_UP)));
        statisticsCountByTradeNoList.forEach(val -> {
            if (isBlank(val.getIds())) {
                return;
            }
            // 此笔订单交易下，sku累计数量
            int thisNum = val.getSumNum();
            // 是否存在此累计数量对应的组合价规格单价
            BigDecimal thisNumSpecPrice = priceCacheMap.get(thisNum);

            // 如果订单下的数量不是组合价的价格信息中对应的上的数量，需要采用贪心算法进行计算。
            if (Objects.isNull(thisNumSpecPrice)) {
                // wrapperIdList
                Queue<Long> idQueue = new LinkedList<>();
                for (String s : val.getIds().split(",")) {
                    idQueue.add(Long.valueOf(s));
                }
                // 贪心算法计算结果。
                QuantityCombinedPriceVO quantityCombinedPriceVO = CommonCalculator.stagedPrice0(priceBoList, thisNum);
                List<QuantityCombinedPriceVO.SpecVO> specList = quantityCombinedPriceVO.getSpecList();
                // 遍历算法计算结果，结果会返回所需要的各个组合价的数量，计算每一个组合价的单价，再根据总数量和组合数量，做更新。
                specList.forEach(priceResult -> {
                    BigDecimal unitPrice = priceResult.getPriceBo().getPrice()
                            .divide(new BigDecimal(priceResult.getPriceBo().getQuantity()), 6, RoundingMode.HALF_UP);
                    int updateCount = priceResult.getCaseQuantity() * priceResult.getPriceBo().getQuantity();
                    // 从ids中取出指定数量更新价格。
                    List<Long> updatePriceIdList = new LinkedList<>();
                    for (int i = 0; i < updateCount; i++) {
                        updatePriceIdList.add(idQueue.poll());
                    }
                    // 将目标id的价格进行更新
                    boolean update = iWdtOrderDetailWrapperService.lambdaUpdate()
                            .set(WdtOrderDetailWrapper::getPrice, unitPrice)
                            .in(WdtOrderDetailWrapper::getId, updatePriceIdList)
                            .update();
                    priceContainer.add(unitPrice);
                });
            } else {
                List<Long> idList = ListUtil.of(val.getIds().split(",")).stream().map(Long::valueOf).collect(Collectors.toList());
                boolean update = iWdtOrderDetailWrapperService.lambdaUpdate()
                        .set(WdtOrderDetailWrapper::getPrice, thisNumSpecPrice)
                        .in(WdtOrderDetailWrapper::getId, idList)
                        .update();
                priceContainer.add(thisNumSpecPrice);
            }
        });
    }
}
