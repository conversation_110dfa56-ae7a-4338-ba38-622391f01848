package com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.mall.wdtsdk.Client;
import com.daddylab.mall.wdtsdk.api.goods.GoodsAPI;
import com.daddylab.mall.wdtsdk.api.sales.StockSync;
import com.daddylab.mall.wdtsdk.api.setting.SettingAPI;
import com.daddylab.mall.wdtsdk.api.wms.StockAPI;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.RawRefundAPI;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.RefundAPI;
import com.daddylab.mall.wdtsdk.apiv2.sales.TradeQueryAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.PreStockinAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.StockinRefundAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.StockoutSalesAPI;
import com.daddylab.mall.wdtsdk.impl.ApiFactory;
import com.daddylab.mall.wdtsdk.impl.DefaultClient;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig.Config;
import com.daddylab.supplier.item.infrastructure.qimen.wdt.*;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import lombok.Value;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 旺店通通信网关实现
 *
 * <AUTHOR>
 */
@Component
public class WdtGatewayImpl implements WdtGateway {

    private static final Map<Class<?>, Class<? extends WdtAPIQimenImplBase>> qimenImplMap = ImmutableMap
            .<Class<?>, Class<? extends WdtAPIQimenImplBase>>builder()
            .put(GoodsAPI.class, GoodsAPIQimenImpl.class)
            .put(TradeQueryAPI.class, TradeQueryAPIQimenImpl.class)
            .put(RefundAPI.class, RefundAPIQimenImpl.class)
            .put(RawRefundAPI.class, RawRefundAPIQimenImpl.class)
            .put(StockoutSalesAPI.class, StockoutSalesAPIQimenImpl.class)
            .put(PreStockinAPI.class, PreStockinAPIQimenImpl.class)
            .put(StockinRefundAPI.class, StockinRefundAPIQimenImpl.class)
            .build();
    private final LoadingCache<ApiIdentity, Object> apiInstanceCachePool;
    private final LoadingCache<ApiIdentity, Object> qimenApiInstanceCachePool;
    private final List<Client> clients;
    @Autowired
    private final WdtConfig wdtConfig;
    @Autowired
    private final QimenApiFactory qimenApiFactory;

    public WdtGatewayImpl(WdtConfig wdtConfig,
            QimenApiFactory qimenApiFactory) {
        clients = wdtConfig.getConfigs().stream().map(config -> DefaultClient
                .get(config.getSid(), config.getUrl(), config.getKey(),
                        config.getSecret())).collect(Collectors.toList());
        this.wdtConfig = wdtConfig;
        this.qimenApiFactory = qimenApiFactory;
        this.apiInstanceCachePool = Caffeine.newBuilder().expireAfterAccess(10, TimeUnit.MINUTES)
                .build(this::createApiInstance);
        this.qimenApiInstanceCachePool = Caffeine.newBuilder()
                .expireAfterAccess(10, TimeUnit.MINUTES).build(this::createQimenApiInstance);
    }

    private Object createApiInstance(ApiIdentity id) {
        final Object api = ApiFactory.get(clients.get(id.getConfigIndex()), id.getApiClass());
        final Config config = this.wdtConfig.getConfigs().get(id.getConfigIndex());
        return id.isEnableLog() ? APILogProxy.proxy(api, id.getApiClass(), config) : api;
    }

    private Object createQimenApiInstance(ApiIdentity id) {
        final Object api = qimenApiFactory
                .getWdtAPIQimenImpl(id.getQimenApiImplClass(), wdtConfig, id.getConfigIndex());
        final Config config = this.wdtConfig.getConfigs().get(id.getConfigIndex());
        return id.isEnableLog() ? APILogProxy.proxy(api, id.getApiClass(), config) : api;
    }

    @SuppressWarnings("unchecked")
    private <T> T getApiInstance(int configIndex, Class<T> clazz, boolean enableLog) {
        return (T) apiInstanceCachePool
                .get(new ApiIdentity(configIndex, clazz, null, enableLog));
    }

    @SuppressWarnings("unchecked")
    private <T> T getQimenApiInstance(int configIndex, Class<T> clazz, boolean enableLog) {
        final Class<? extends WdtAPIQimenImplBase> qimenApiImplClass = qimenImplMap.get(clazz);
        if (qimenApiImplClass == null) {
            throw new RuntimeException(StrUtil.format("API类{}未配置奇门实现类", clazz.getName()));
        }
        return (T) qimenApiInstanceCachePool
                .get(new ApiIdentity(configIndex, clazz, qimenApiImplClass, enableLog));
    }

    private int defaultConfigIndex() {
        for (int i = 0; i < wdtConfig.getConfigs().size(); i++) {
            if (wdtConfig.getConfigs().get(i).isAsDefault()) {
                return i;
            }
        }
        return 0;
    }

    @Override
    public StockSync stockSyncAPI() {
        return getApiInstance(defaultConfigIndex(), StockSync.class, true);
    }

    @Override
    public StockAPI stockAPI() {
        return getApiInstance(defaultConfigIndex(), StockAPI.class, true);
    }

    @Override
    public GoodsAPI goodsAPI() {
        return getApiInstance(defaultConfigIndex(), GoodsAPI.class, true);
    }

    @Override
    public GoodsAPI qimenGoodsAPI(int configIndex) {
        return getQimenApiInstance(configIndex, GoodsAPI.class, true);
    }

    @Override
    public GoodsAPI qimenGoodsAPI() {
        return qimenGoodsAPI(defaultConfigIndex());
    }

    @Override
    public SettingAPI settingAPI() {
        return getApiInstance(defaultConfigIndex(), SettingAPI.class, true);
    }

    @Override
    public <T> T getAPI(Class<T> apiClass) {
        return getAPI(apiClass, 0, true);
    }

    @Override
    public <T> T getAPI(Class<T> apiClass, int configIndex, boolean enableLog) {
        return getApiInstance(configIndex, apiClass, true);
    }

    @Override
    public <T> T getAPI(Class<T> apiClass, String appKey, boolean enableLog) {
        int configIndex = 0;
        for (int i = 0; i < wdtConfig.getConfigs().size(); i++) {
            final Config config = wdtConfig.getConfigs().get(i);
            if (Objects.equals(config.getKey(), appKey)) {
                configIndex = i;
                break;
            }
        }
        return getApiInstance(configIndex, apiClass, true);
    }

    @Override
    public <T> T getQimenAPI(Class<T> apiClass) {
        return getQimenAPI(apiClass, 0, true);
    }

    @Override
    public int randomConfigIndex() {
        return RandomUtil.randomInt(wdtConfig.getConfigs().size());
    }

    @Override
    public <T> T getQimenAPI(Class<T> apiClass, int configIndex, boolean enableLog) {
        return getQimenApiInstance(configIndex, apiClass, enableLog);
    }

    @Value
    private static class ApiIdentity {

        int configIndex;
        Class<?> apiClass;
        Class<? extends WdtAPIQimenImplBase> qimenApiImplClass;
        boolean enableLog;
    }
}
