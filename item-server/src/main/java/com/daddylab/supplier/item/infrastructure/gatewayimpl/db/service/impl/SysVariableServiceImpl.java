package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.SysVariable;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.SysVariableMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ISysVariableService;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <p>
 * 系统变量 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
@Service
public class SysVariableServiceImpl extends DaddyServiceImpl<SysVariableMapper, SysVariable> implements ISysVariableService {

    @Override
    public Optional<String> getValue(String key) {
        return lambdaQuery().eq(SysVariable::getKey, key).oneOpt().map(SysVariable::getValue);
    }

    @Override
    public void setValue(String key, String value) {
        final boolean update = lambdaUpdate().eq(SysVariable::getKey, key).set(SysVariable::getValue, value).update();
        if (!update) {
            final SysVariable sysVariable = new SysVariable();
            sysVariable.setKey(key);
            sysVariable.setValue(value);
            save(sysVariable);
        }
    }
}
