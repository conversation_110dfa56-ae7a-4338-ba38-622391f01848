package com.daddylab.supplier.item.domain.drawer.form;

import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerMarkImageContentTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * Class  ItemDrawerForm
 *
 * @Date 2022/5/31下午4:18
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ItemDrawerMarkImageContentForm", description = "商品抽屉图片标记内容Form")
public class ItemDrawerMarkImageContentForm implements Serializable {

    @ApiModelProperty(value = "标注内容Id(新增时不传)")
    private Long id;

    @NotEmpty(message = "原标注内容不能为空")
    @ApiModelProperty(value = "原标注内容不能为空")
    private String key;

    @NotNull(message = "操作类型 不能为空")
    @ApiModelProperty(value = "操作类型 UPDATE-改为 DELETE-删除 CONFIRM-确认")
    private ItemDrawerMarkImageContentTypeEnum type;

    @ApiModelProperty(value = "标注后内容")
    private String value;

    /**
     * 标注内容位置信息
     */
    @ApiModelProperty(value = "标注内容位置信息")
    private String position;

    @ApiModelProperty(value = "标注内容图片标识")
    private String pidentifier;
}
