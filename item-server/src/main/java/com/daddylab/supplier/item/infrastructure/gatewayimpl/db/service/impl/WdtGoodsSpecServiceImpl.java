package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtGoodsSpec;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtGoodsSpecMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtGoodsSpecService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-24
 */
@Service
public class WdtGoodsSpecServiceImpl extends DaddyServiceImpl<WdtGoodsSpecMapper, WdtGoodsSpec> implements IWdtGoodsSpecService {

}
