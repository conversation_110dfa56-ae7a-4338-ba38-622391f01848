package com.daddylab.supplier.item.controller.afterSales;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.daddylab.supplier.item.application.afterSales.*;
import com.daddylab.supplier.item.application.refundOrder.RefundOrderService;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR> up
 * @date 2022年10月18日 6:05 PM
 */
@Slf4j
@RestController
@RequestMapping("/afterSales")
@Api(value = "售后管理相关API", tags = "售后管理相关API")
public class AfterSalesController {

    @Resource
    AfterSalesBizService afterSalesBizService;

    @Resource
    AfterSalesConfirmBizService afterSalesConfirmBizService;

    @Resource
    RefundOrderService refundOrderService;

    @Resource
    AfterSalesWarehouseBizService afterSalesWarehouseBizService;

    @ResponseBody
    @ApiOperation(value = "异常件关联售后单")
    @PostMapping("/relatedReturnOrder")
    public SingleResponse<Boolean> relatedReturnOrder(@RequestBody RelateCmd cmd) throws Exception{
        log.info("售后管理-异常件关联售后单参数，cmd:{}", cmd);
        return afterSalesBizService.relatedAbnormalInfo(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "异常件列表")
    @PostMapping("/pageQuery")
    public PageResponse<AfterSalesPageVO> pageQuery(@RequestBody @Validated AfterSalesPageQuery afterSalesPageQuery) {
        log.info("售后管理-异常件列表参数，query:{}", afterSalesPageQuery);
        return afterSalesBizService.pageQueryAbnormalInfo(afterSalesPageQuery);
    }

    @ResponseBody
    @ApiOperation(value = "异常件信息导出")
    @PostMapping("/export")
    public SingleResponse<Boolean> export(@RequestBody @Validated AfterSalesPageQuery afterSalesPageQuery) {
        log.info("售后管理-异常件信息导出参数，query:{}", afterSalesPageQuery);
        return afterSalesBizService.exportAbnormalInfo(afterSalesPageQuery);
    }

    @ApiOperation(value = "销退确认")
    @PostMapping("/confirm")
    public Response confirm(@RequestBody @Validated AfterSalesConfirmCmd cmd) {
        return afterSalesConfirmBizService.confirm(cmd);
    }

    @ApiOperation(value = "编辑退货物流")
    @PostMapping("/editReturnLogistics")
    public Response editReturnLogistics(@RequestBody @Validated AfterSalesEditReturnLogisticsCmd cmd) {
        return refundOrderService.editReturnLogistics(cmd);
    }

    @ApiOperation(value = "导入仓库售后地址")
    @PostMapping(value = "/importWarehouseAfterSalesAddress", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response importWarehouseAfterSalesAddress(
            @ApiParam(name = "file", value = "文件", required = true)
            @RequestParam("file") MultipartFile file) {
        try {
            return afterSalesWarehouseBizService.importExcel(file.getInputStream());
        } catch (IOException e) {
            throw new BizException(ErrorCode.FILE_UPLOAD_ERROR.getCode(), "导入仓库售后地址失败，读取上传文件失败");
        }
    }


}
