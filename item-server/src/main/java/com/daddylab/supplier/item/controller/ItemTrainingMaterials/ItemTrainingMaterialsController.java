package com.daddylab.supplier.item.controller.ItemTrainingMaterials;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.ItemTrainingMaterials.ItemTrainingMaterialsBizService;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.application.process.ProcessBizService;
import com.daddylab.supplier.item.common.domain.dto.IdCmd;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.types.itemTrainingMaterials.*;
import com.daddylab.supplier.item.types.process.TaskClaimCmd;
import com.daddylab.supplier.item.types.process.TaskUrgeCmd;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/ItemTrainingMaterials/")
@Api(value = "商品培训资料相关API", tags = "商品培训资料相关API")
public class ItemTrainingMaterialsController {

    @Autowired
    private ItemTrainingMaterialsBizService itemTrainingMaterialsBizService;
    @Autowired
    private ProcessBizService processBizService;
    @Autowired
    private OperateLogBizService operateLogBizService;

    @ApiOperation("商品培训资料抽屉")
    @PostMapping("/drawer")
    public SingleResponse<ItemTrainingMaterialsDrawer> drawer(
            @Validated @RequestBody IdCmd id) {
        return itemTrainingMaterialsBizService.drawer(id.getId());
    }

    @ApiOperation("保存商品培训资料抽屉")
    @PostMapping("/saveDrawer")
    public Response saveDrawer(
            @Validated @RequestBody ItemTrainingMaterialsSaveCmd cmd) {
        return itemTrainingMaterialsBizService.save(cmd);
    }

    @GetMapping(value = "/operateLogs")
    @ApiOperation("操作日志")
    public Response operateLogs(Long targetId) {
        return itemTrainingMaterialsBizService.getOperateLogs(targetId);
    }

    @GetMapping(value = "/historyRecords")
    @ApiOperation("历史记录")
    public MultiResponse<ItemTrainingMaterialsRecordVO> historyRecordsQuery(ItemTrainingMaterialsRecordQuery query) {
        return itemTrainingMaterialsBizService.historyRecordsQuery(query);
    }

    @ApiOperation("催办")
    @PostMapping("/process/urge")
    public Response urge(@Valid @RequestBody TaskUrgeCmd cmd) {
        return itemTrainingMaterialsBizService.urge(UserContext.getUserId(), cmd);
    }

    @ApiOperation("发起复审")
    @PostMapping("/process/submitReview")
    public Response review(@Valid @RequestBody MaterialsSubmitReviewCmd cmd) {
        return itemTrainingMaterialsBizService.submitReview(UserContext.getUserId(), cmd);
    }

    @ApiOperation("商品培训资料流程任务认领")
    @PostMapping("/process/claim")
    public Response claim(@Validated @RequestBody TaskClaimCmd cmd) {
        return processBizService.claim(UserContext.getUserId(), cmd.getTaskId());
    }

    @ApiOperation("商品培训资料流程任务提交")
    @PostMapping("/process/complete")
    public Response complete(@Validated @RequestBody TaskCompleteCmd cmd) {
        return itemTrainingMaterialsBizService.complete(cmd);
    }

    @ApiOperation("商品培训资料流程撤回")
    @PostMapping("/process/rollback")
    public Response rollback(@Validated @RequestBody RollbackCmd cmd) {
        return itemTrainingMaterialsBizService.rollback(cmd);
    }

    @ApiOperation("商品培训资料退回采购")
    @PostMapping("/process/rollback2")
    public Response rollback2(@Validated @RequestBody RollbackCmd cmd) {
        return itemTrainingMaterialsBizService.rollback2(cmd);
    }

    @ApiOperation("商品培训资料流程终止")
    @PostMapping("/process/terminal")
    @Auth("/ItemTrainingMaterials/process/rollback")
    public Response terminal(@Validated @RequestBody TerminalBatchCmd cmd) {
        return itemTrainingMaterialsBizService.terminalBatch(cmd);
    }


}
