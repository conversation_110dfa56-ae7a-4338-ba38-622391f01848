package com.daddylab.supplier.item.controller.handingsheet.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/24 15:09
 * @Description: 提交审核
 */
@Data
@ApiModel("提交审核")
public class HandingSheetSubmitAuditParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "盘货表主键ID")
    private Long id;
}
