package com.daddylab.supplier.item.application.stockSpec;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSON;
import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.job.extend.autoRegister.XxlJobAutoRegister;
import com.daddylab.supplier.item.application.warehouse.WarehouseBizService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.SysInventorySetting;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtStockSpec;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtStockSpecRt;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.TimeScheduleType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ISysInventorySettingService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ISysVariableService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtStockSpecRtService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtStockSpecService;
import com.daddylab.supplier.item.infrastructure.timeSchedule.TimeScheduleConfig;
import com.daddylab.supplier.item.infrastructure.timeSchedule.TimeScheduleManager;
import com.daddylab.supplier.item.infrastructure.utils.MPUtil;
import com.daddylab.supplier.item.types.SysVariableKey;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/7/1
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StockSpecRtSyncService {
    public static final int BATCH_SIZE = 100;
    private final TimeScheduleManager timeScheduleManager;
    private final IWdtStockSpecRtService wdtStockSpecRtService;
    private final IWdtStockSpecService wdtStockSpecService;
    private final ISysInventorySettingService sysInventorySettingService;
    private final ISysVariableService sysVariableService;
    private final WarehouseBizService warehouseBizService;

    @XxlJob("StockSpecRtSyncService::syncJob")
    @XxlJobAutoRegister(cron = "0 * * * * ? *", author = "徵乌", jobDesc = "旺店通实时库存APPLY")
    public void syncJob() {
        final StockSpecRtSyncJobParam jobParam = JSON.parseObject(XxlJobHelper.getJobParam(),
                StockSpecRtSyncJobParam.class);
        sync(jobParam);
    }


    @Data
    public static class StockSpecRtSyncJobParam {
        private boolean triggerEvent = true;
    }

    public void sync(StockSpecRtSyncJobParam jobParam) {
        final SysInventorySetting sysInventorySetting = sysInventorySettingService.getSysInventorySetting();
        final TimeScheduleConfig scheduleConfig = TimeScheduleConfig.builder()
                                                                    .type(TimeScheduleType.WDT_STOCK_SPEC_RT_APPLY)
                                                                    .syncFrequency(sysInventorySetting.getWdtSyncFrequency())
                                                                    .businessId("0")
                                                                    .build();
        timeScheduleManager.action(scheduleConfig, timeSchedule -> {
            long[] timeCursor = new long[]{sysVariableService.getValue(SysVariableKey.WDT_STOCK_SPEC_RT_APPLY_CURSOR)
                                                             .map(Long::parseLong).orElse(0L), 0};
            while (true) {
                final long scheduleTimeEpoch = LocalDateTimeUtil.toEpochMilli(timeSchedule.getScheduleTime()) / 1000;
                final List<WdtStockSpecRt> list = wdtStockSpecRtService
                        .lambdaQuery()
                        .le(WdtStockSpecRt::getUpdatedAt, scheduleTimeEpoch)
                        .and(q -> q.eq(WdtStockSpecRt::getUpdatedAt, timeCursor[0])
                                   .gt(WdtStockSpecRt::getId, timeCursor[1])
                                   .or().gt(WdtStockSpecRt::getUpdatedAt, timeCursor[0]))
                        .orderByAsc(WdtStockSpecRt::getUpdatedAt)
                        .orderByAsc(WdtStockSpecRt::getId)
                        .last(MPUtil.limit(BATCH_SIZE))
                        .list();
                log.info("旺店通实时库存APPLY, 游标:{} -> {}, 数量:{}",
                        Arrays.toString(timeCursor),
                        scheduleTimeEpoch,
                        list.size());
                if (list.isEmpty()) {
                    break;
                }
                final List<Long> ids = list.stream().map(WdtStockSpecRt::getId).collect(Collectors.toList());
                final Set<Long> localIds = getLocalIds(ids);
                final List<WdtStockSpec> models = list.stream()
                                                      .map(WdtStockSpecAssembler.INST::rtPoToPo)
                                                      .collect(Collectors.toList());
                final List<WdtStockSpec> toUpdateModels = new ArrayList<>();
                final List<WdtStockSpec> toSaveModels = new ArrayList<>();
                for (WdtStockSpec model : models) {
                    if (localIds.contains(model.getId())) {
                        toUpdateModels.add(model);
                    } else {
                        toSaveModels.add(model);
                    }
                }

                if (!toUpdateModels.isEmpty()) {
                    wdtStockSpecService.updateBatchById(toUpdateModels);
                }

                if (!toSaveModels.isEmpty()) {
                    wdtStockSpecService.saveBatch(toSaveModels);
                }

//                if (jobParam.isTriggerEvent()) {
//                    final StockSpecSaveBatchEvent event = new StockSpecSaveBatchEvent();
//                    event.setSaveModels(toSaveModels);
//                    event.setUpdateModels(toUpdateModels);
//                    EventBusUtil.post(event, true);
//                }

                final WdtStockSpecRt lastOne = list.get(list.size() - 1);
                final long timeCursor0 = timeCursor[0];
                timeCursor[0] = lastOne.getUpdatedAt();
                timeCursor[1] = lastOne.getId();
                if (timeCursor[0] != timeCursor0) {
                    sysVariableService.setValue(SysVariableKey.WDT_STOCK_SPEC_RT_APPLY_CURSOR,
                            String.valueOf(timeCursor[0]));
                }
            }
            timeCursor[0] = LocalDateTimeUtil.toEpochMilli(timeSchedule.getScheduleTime()) / 1000L;
            sysVariableService.setValue(SysVariableKey.WDT_STOCK_SPEC_RT_APPLY_CURSOR,
                    String.valueOf(timeCursor[0] + 1));
        });

    }

    @NonNull
    private Set<Long> getLocalIds(List<Long> ids) {
        return wdtStockSpecService.lambdaQuery()
                                  .in(WdtStockSpec::getId, ids)
                                  .list()
                                  .stream()
                                  .map(WdtStockSpec::getId)
                                  .collect(Collectors.toSet());
    }
}
