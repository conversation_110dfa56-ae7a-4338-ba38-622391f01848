package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.CommonCalculator;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.WrapperType;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.ActivityTimeSinglePriceDO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.ActivityTimeSuitePriceDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.SkuPriceExplain;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;

/**
 * 根据活动时间优惠
 * 处理单品和组合装
 *
 * <AUTHOR> up
 * @date 2022年10月21日 3:39 PM
 */
@Service
@Slf4j
public class ActivityTimePrice extends BaseProcess implements PriceProcessor {

    @Resource
    CommonCalculator commonCalculator;


    @Override
    public Boolean doPriceProcess(TimeBO timeBO) {
        log.info("ActivityTimePrice doPriceProcess start");

        try {
            stockSingle(timeBO, WrapperType.STOCK_OUT_SINGLE);
            stockOutSuite(timeBO);
        } catch (Exception e) {
            log.error("ActivityTimePrice doPriceProcess error", e);
        }
        log.info("ActivityTimePrice doPriceProcess finish");
        return true;
    }

    /**
     * 出库明细 单品处理。
     *
     * @param timeBO 存在采购活动价的sku编码信息。
     */
    public void stockSingle(TimeBO timeBO, WrapperType wrapperType) throws InterruptedException {
        // 拿两个线程出来进行处理
        // 以wrapper表的数据inner join采购价格表，捞出所有sku以及其在指定活动时间范围内的优惠采购成本价。
        List<ActivityTimeSinglePriceDO> list = wdtOrderDetailMapper.selectStockOutSingleTimePurchase(timeBO.getOperateMonth());
        if (CollUtil.isEmpty(list)) {
            return;
        }

        List<SkuPriceExplain> priceLogList = new LinkedList<>();

        list.forEach(val -> {
            try {
                boolean update = iWdtOrderDetailWrapperService.lambdaUpdate()
                        .set(WdtOrderDetailWrapper::getPrice, val.getPriceCost())
                        .eq(WdtOrderDetailWrapper::getSkuCode, val.getSkuCode())
                        .eq(WdtOrderDetailWrapper::getType, wrapperType.getValue())
                        .eq(val.getPlatformType() != 0, WdtOrderDetailWrapper::getPlatformType, val.getPlatformType())
                        .between(WdtOrderDetailWrapper::getPayTime, val.getStartTime(), val.getEndTime())
                        .update();
                if(update){
                    SkuPriceExplain explain = new SkuPriceExplain();
                    explain.setSkuCode(val.getSkuCode());
                    explain.setPrice(val.getPriceCost());
                    explain.setOperateTime(timeBO.getOperateMonth());
                    explain.setRemark("单品采购活动价时间优惠");
                    priceLogList.add(explain);
                }
            } catch (Exception e) {
                log.error("ActivityTimePrice doPriceProcess stockSingle update sku price fail.val:{},wrapperType:{}",
                        JsonUtil.toJson(val), wrapperType, e);
            }
        });

        if (CollUtil.isNotEmpty(priceLogList)) {
            iSkuPriceExplainService.saveBatch(priceLogList);
        }

    }

    /**
     * 出库明细 组合装处理。
     * 1.查询出满足活动时间的组合装活动价。重新均摊下属单品价格，更新单品价格。
     *
     * @param timeBO
     */
    public void stockOutSuite(TimeBO timeBO) throws InterruptedException {
        // 主处理逻辑和单品处理类似。
        List<ActivityTimeSuitePriceDO> list = wdtOrderDetailMapper.selectStockOutSuiteTimePurchase(timeBO.getOperateMonth());
        if (CollUtil.isEmpty(list)) {
            return;
        }

        List<SkuPriceExplain> priceLogList = new LinkedList<>();

        list.forEach(val -> {
            try {
                String suiteNo = val.getSuiteNo();
                BigDecimal activityCostPrice = val.getPriceCost();
                Long payStartTime = val.getStartTime();
                Long payEndTime = val.getEndTime();
                // 计算此组合装下构成单品的活动成本
                commonCalculator.getSkuCostPriceUnderSuiteNo(suiteNo, activityCostPrice).forEach(skuVo -> {
                    String skuCode = skuVo.getSkuCode();
                    BigDecimal needUpdatePrice = skuVo.getUnitPrice();
                    boolean update = iWdtOrderDetailWrapperService.lambdaUpdate()
                            .set(WdtOrderDetailWrapper::getPrice, needUpdatePrice)
                            .eq(WdtOrderDetailWrapper::getSuiteNo, suiteNo)
                            .eq(WdtOrderDetailWrapper::getSkuCode, skuCode)
                            .eq(WdtOrderDetailWrapper::getType, 2)
                            .eq(val.getPlatformType() != 0, WdtOrderDetailWrapper::getPlatformType, val.getPlatformType())
                            .between(WdtOrderDetailWrapper::getPayTime, payStartTime, payEndTime)
                            .update();
                    if (update) {
                        SkuPriceExplain explain = new SkuPriceExplain();
                        explain.setSkuCode(skuCode);
                        explain.setPrice(needUpdatePrice);
                        explain.setOperateTime(timeBO.getOperateMonth());
                        explain.setRemark("组合装采购活动价时间优惠");
                        priceLogList.add(explain);
                    }
                });
            } catch (Exception e) {
                log.error("ActivityTimePrice doPriceProcess tockOutSuite update sku price fail val:{}", val, e);
            }
        });

        if(CollUtil.isNotEmpty(priceLogList)){
            iSkuPriceExplainService.saveBatch(priceLogList);
        }
    }


}
