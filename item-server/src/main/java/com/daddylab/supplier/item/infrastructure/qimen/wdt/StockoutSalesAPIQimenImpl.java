package com.daddylab.supplier.item.infrastructure.qimen.wdt;

import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.StockoutSalesAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutSalesOnceWeighingByNoResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutSalesOnceWeighingResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutSalesQueryHistoryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutSalesQueryHistoryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutSalesQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutSalesQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutSalesSalesWeighingResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutSalesSearchLogisticsParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutSalesSearchLogisticsResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutSalesWeighingExtResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.daddylab.supplier.item.infrastructure.qimen.QimenConfig;
import com.daddylab.supplier.item.infrastructure.qimen.QimenStockoutSalesAPI;
import com.qimencloud.api.QimenCloudClient;
import com.qimencloud.api.scene3ldsmu02o9.request.WdtWmsStockoutSalesQuerywithdetailRequest.Params;
import com.qimencloud.api.scene3ldsmu02o9.response.WdtWmsStockoutSalesQuerywithdetailResponse;
import com.taobao.api.ApiException;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2022/1/17
 */
public class StockoutSalesAPIQimenImpl extends WdtAPIQimenImplBase implements StockoutSalesAPI {

    QimenStockoutSalesAPI qimenAPI;

    public StockoutSalesAPIQimenImpl(QimenCloudClient qimenClient,
            QimenConfig qimenConfig,
            WdtConfig wdtConfig,
            int configIndex
    ) {
        super(qimenClient, qimenConfig, wdtConfig, configIndex);
    }

    @Override
    protected void initApiInstance(QimenCloudClient qimenClient, QimenConfig qimenConfig,
            WdtConfig wdtConfig, int configIndex) {
        qimenAPI = new QimenStockoutSalesAPI(qimenClient, qimenConfig, wdtConfig, configIndex);
    }

    @Override
    public StockoutSalesQueryWithDetailResponse queryWithDetail(
            StockoutSalesQueryWithDetailParams params, Pager pager) throws WdtErpException {
        try {
            final Params qimenParams = new Params();
            qimenParams.setEndTime(params.getEndTime());
            qimenParams.setNeedSn(params.getNeedSn());
            qimenParams
                    .setPosition(params.getPosition() != null ? params.getPosition() != 0 : null);
            qimenParams.setShopNos(params.getShopNos());
            qimenParams.setSrcOrderNo(params.getSrcOrderNo());
            qimenParams.setStartTime(params.getStartTime());
            qimenParams.setStatus(params.getStatus());
            qimenParams.setStatusType(
                    params.getStatusType() != null ? Long.valueOf(params.getStatusType()) : null);
            qimenParams.setStockoutNo(params.getStockoutNo());
            qimenParams.setWarehouseNo(params.getWarehouseNo());

            final WdtWmsStockoutSalesQuerywithdetailResponse response = qimenAPI
                    .stockOutSalesQuery(qimenParams, pager.getPageNo() + 1,
                            pager.getPageSize());
            return checkAndReturnData(response,
                    StockoutSalesQueryWithDetailResponse.class);
        } catch (ApiException exception) {
            throw transformException(exception);
        }
    }

    @Override
    public StockoutSalesWeighingExtResponse weighingExt(String logisticsOrTradeNo,
            String packageBarcode, BigDecimal weight, Integer packagerId, Boolean force)
            throws WdtErpException {
        throw new UnsupportedOperationException("暂未实现");

    }

    @Override
    public StockoutSalesOnceWeighingResponse onceWeighing(String logisticsOrTradeNo,
            String packageBarcode, BigDecimal weight, Integer packagerId, String operateTableName,
            Boolean force) throws WdtErpException {
        throw new UnsupportedOperationException("暂未实现");

    }

    @Override
    public StockoutSalesQueryHistoryWithDetailResponse queryHistoryWithDetail(
            StockoutSalesQueryHistoryWithDetailParams params, Pager pager) throws WdtErpException {
        throw new UnsupportedOperationException("暂未实现");

    }

    @Override
    public StockoutSalesOnceWeighingByNoResponse onceWeighingByNo(String logisticsOrTradeNo,
            String packageBarcode, BigDecimal weight, String packagerNo, String operateTableName,
            Boolean force) throws WdtErpException {
        throw new UnsupportedOperationException("暂未实现");
    }

    @Override
    public StockoutSalesSalesWeighingResponse salesWeighing(String orderNo, BigDecimal weight,
            String packageBarcode, String packagerNo, Integer packagerId, String operatorTableName,
            Boolean force, BigDecimal length, BigDecimal width, BigDecimal height)
            throws WdtErpException {
        throw new UnsupportedOperationException("暂未实现");
    }

    @Override
    public StockoutSalesSearchLogisticsResponse searchLogistics(
            StockoutSalesSearchLogisticsParams params, Pager pager) throws WdtErpException {
        throw new UnsupportedOperationException("暂未实现");
    }
}
