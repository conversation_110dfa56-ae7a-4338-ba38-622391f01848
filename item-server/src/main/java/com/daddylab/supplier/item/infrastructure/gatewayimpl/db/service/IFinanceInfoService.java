package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.FinanceInfo;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 财务信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-20
 */
public interface IFinanceInfoService extends IDaddyService<FinanceInfo> {

    FinanceInfo getInfoByProviderId(Long providerId);

    void removeByProviderId(Long providerId);

}
