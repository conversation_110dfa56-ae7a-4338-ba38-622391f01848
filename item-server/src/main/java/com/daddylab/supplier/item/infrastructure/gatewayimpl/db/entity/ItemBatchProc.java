package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;

import lombok.*;

import java.io.Serializable;

/**
 * 商品批处理
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemBatchProc implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 类型 1:批量修改采购员 2:批量修改仓库 */
    private Integer type;

    /** 商品查询条件 */
    private String cmd;

    /** 状态 0 待执行 1 执行中 2 已执行 3 执行异常 */
    private Integer status;

    /** 处理锁，锁定当前记录直到超过该时间 */
    private Long lockUntil;

    /** 延长锁定时间 */
    @TableField(value = "lock_until", select = false, update = "%s + #{et.addLockUntil}")
    private Integer addLockUntil;

    /** 重试次数 */
    private Integer retryNum;

    /** 增加重试次数（注意，只能设为 true，如果不想增加，就不要设置这个字段） */
    @TableField(value = "retry_num", select = false, update = "%s + 1")
    private Boolean addRetryNum;

    public void setAddRetryNum() {
        this.addRetryNum = true;
    }

    /** 处理进度 0-100 */
    private Integer process;

    /** 日志 */
    @TableField(select = false, updateStrategy = FieldStrategy.NEVER)
    private String log = "[]";

    /** 追加日志 */
    @TableField(
            select = false,
            value = "log",
            update = "json_array_insert(%s, '$[0]', #{et.appendLog})")
    private String appendLog;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /** 创建人 */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /** 更新时间 */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /** 更新人 */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /** 是否删除：0否1是 */
    @TableLogic private Integer isDel;

    /** 删除时间 */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;
}
