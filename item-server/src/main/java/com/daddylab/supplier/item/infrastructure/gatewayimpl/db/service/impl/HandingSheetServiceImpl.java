package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.application.drawer.ItemDrawerService;
import com.daddylab.supplier.item.application.fold.FoldBizService;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.handingsheet.dto.*;
import com.daddylab.supplier.item.controller.item.dto.SkuWithNewGoodsDto;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.exportTask.ExportTaskGateway;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.config.QyWeixinDaddyErpProperties;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.domain.PropertyAlias;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.HandingSheetActivityTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.HandingSheetStateEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.HandingSheetMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.QyWeixinFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.SendMsgQyWeixinResult;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.SendQyWeixinMsgParam;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.types.handingsheet.HandingSheetItemSkuBizFields;
import com.daddylab.supplier.item.types.handingsheet.HandingSheetItemSkuPersistData;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 盘货表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
@Service
@Slf4j
public class HandingSheetServiceImpl extends DaddyServiceImpl<HandingSheetMapper, HandingSheet> implements HandingSheetService {
    private final int giftDescriptionLengthLimit = 50;
    private final int remarkLengthLimit = 100;

    @Autowired
    private HandingSheetMapper handingSheetMapper;
    @Autowired
    @Qualifier("UserGatewayCacheImpl")
    private UserGateway userGateway;
    @Autowired
    private HandingSheetItemService handingSheetItemService;
    @Autowired
    private IHandingSheetItemSpuService handingSheetItemSpuService;
    @Autowired
    private IHandingSheetItemSkuService handingSheetItemSkuService;

    @Autowired
    private HandingSheetActivityEventService handingSheetActivityEventService;
    @Autowired
    private HandingSheetStaffService handingSheetStaffService;
    @Autowired
    private StaffService staffService;
    @Autowired
    private ItemDrawerService itemDrawerService;
    @Autowired
    private IItemSkuService itemSkuService;
    @Autowired
    private INewGoodsService newGoodsService;
    @Autowired
    private HandingSheetActivityTextService handingSheetActivityTextService;
    @Autowired
    private ExportTaskGateway exportTaskGateway;
    @Autowired
    FileGateway fileGateway;
    @Autowired
    private FoldBizService foldService;
    @Autowired
    private OperateLogDomainService operateLogDomainService;
    @Autowired
    private IItemService itemService;
    @Autowired
    private QyWeixinFeignClient qyWeixinFeignClient;
    @Autowired
    private QyWeixinService qyWeixinService;
    @Autowired
    private QyWeixinDaddyErpProperties qyWeixinDaddyErpProperties;
    @Autowired
    private com.daddylab.supplier.item.infrastructure.config.HandingSheetProperties handingSheetProperties;

    @Autowired
    private MsgSender msgSender;
    @Autowired
    private RefreshConfig refreshConfig;

    @Override
    public PageResponse<HandingSheetPageVo> pageQuery(HandingSheetPageQuery pageQuery) {
        QueryWrapper<HandingSheet> queryWrapper = getQueryWrapper(pageQuery);

        // 不使用 mybatis-plus 自带的 count（理由是速度很慢）
        Page<HandingSheet> page = new Page<>();
        page.setSearchCount(false);
        page.setTotal(handingSheetMapper.selectPageListCount(queryWrapper));
        page.setCurrent(pageQuery.getPageIndex());
        page.setSize(pageQuery.getPageSize());

        IPage<HandingSheet> handingSheetPage = handingSheetMapper.selectPageList(page, queryWrapper);

        // to vo
        List<HandingSheetPageVo> vos = handingSheetPageToVo(handingSheetPage);
        return PageResponse.of(vos, (int) page.getTotal(), pageQuery.getPageSize(), pageQuery.getPageIndex());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(HandingSheetParam param) {
        return saveHandingSheet(param);
    }

    private Long saveHandingSheet(HandingSheetParam param) {
        checkForAdd(param);

        // insert handing_sheet
        HandingSheet handingSheet = new HandingSheet();
        handingSheet.setSheetName(param.getName());
        handingSheet.setStartTime(param.getStartTime());
        handingSheet.setEndTime(param.getEndTime());
        List<Integer> platformVals = param.getPlatformVals();
        handingSheet.setPlatform(listToString(platformVals));
        handingSheet.setLabel(param.getLabel());
        String attachments = param.getAttachments();
        if (StringUtils.hasText(attachments)) {
            handingSheet.setAttachment(attachments);
        }
        handingSheet.setActivityType(param.getActivityType());
        handingSheet.setState(HandingSheetStateEnum.TO_BE_SUBMIT.getValue());
        handingSheetMapper.insert(handingSheet);
        Long handingSheetId = handingSheet.getId();

        // insert handing_sheet_staff
        List<Long> staffUids = param.getStaffUids();
        List<HandingSheetStaff> sheetStaffs = new ArrayList<>();
        for (Long staffUid : staffUids) {
            HandingSheetStaff handingSheetStaff = new HandingSheetStaff();
            handingSheetStaff.setHandingSheetId(handingSheetId);
            handingSheetStaff.setUserId(staffUid);
            sheetStaffs.add(handingSheetStaff);
        }
        if (CollectionUtil.isNotEmpty(sheetStaffs)) {
            handingSheetStaffService.saveBatch(sheetStaffs);
        }

        // insert handing_sheet_activity_event
        List<HandingSheetActivityEventParam> activityEvents = param.getActivityEvents();
        List<HandingSheetActivityEvent> sheetActivityEvents = new ArrayList<>();
        for (HandingSheetActivityEventParam activityEvent : activityEvents) {
            HandingSheetActivityEvent sheetActivityEvent = new HandingSheetActivityEvent();
            sheetActivityEvent.setHandingSheetId(handingSheetId);
            sheetActivityEvent.setReachedAmount(activityEvent.getReachedAmount());
            sheetActivityEvent.setReducedAmount(activityEvent.getReducedAmount());
            sheetActivityEvents.add(sheetActivityEvent);
        }
        if (CollectionUtil.isNotEmpty(sheetActivityEvents)) {
            handingSheetActivityEventService.saveBatch(sheetActivityEvents);
        }

        // insert handing_sheet_item
        List<HandingSheetItemParam> itemInfos = param.getItemInfos();
        if (!param.isUseNewVersionView()) {
            List<HandingSheetItem> sheetItems = new ArrayList<>();
            for (HandingSheetItemParam itemInfo : itemInfos) {
                HandingSheetItem handingSheetItem = new HandingSheetItem();
                handingSheetItem.setHandingSheetId(handingSheetId);
                handingSheetItem.setItemNo(itemInfo.getItemNo());
                handingSheetItem.setItemSkuNo(itemInfo.getItemSkuNo());
                handingSheetItem.setItemId(itemInfo.getItemId());
                handingSheetItem.setItemSkuId(itemInfo.getItemSkuId());
                handingSheetItem.setActivePrice(itemInfo.getActivePrice());
                String giftDescription = itemInfo.getGiftDescription();
                if (StringUtils.hasText(giftDescription)) {
                    handingSheetItem.setGiftDescription(giftDescription);
                }
                String giftCode = itemInfo.getGiftCode();
                if (StringUtils.hasText(giftCode)) {
                    handingSheetItem.setGiftCode(giftCode);
                }
                String taobaoLink = itemInfo.getTaobaoLink();
                if (StringUtils.hasText(taobaoLink)) {
                    handingSheetItem.setTaobaoLink(taobaoLink);
                }
                String douyinLink = itemInfo.getDouyinLink();
                if (StringUtils.hasText(douyinLink)) {
                    handingSheetItem.setDouyinLink(douyinLink);
                }
                String miniProgramLink = itemInfo.getMiniProgramLink();
                if (StringUtils.hasText(miniProgramLink)) {
                    handingSheetItem.setMiniProgramLink(miniProgramLink);
                }
                String remark = itemInfo.getRemark();
                if (StringUtils.hasText(remark)) {
                    handingSheetItem.setRemark(remark);
                }
                sheetItems.add(handingSheetItem);
            }
            if (CollectionUtil.isNotEmpty(sheetItems)) {
                handingSheetItemService.saveBatch(sheetItems);
            }
        }

        return handingSheetId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrEdit(HandingSheetParam param) {
        Long id = param.getId();
        if (id == null) {
            // 新增
            return saveHandingSheet(param);
        } else {
            // 编辑
            return editHandingSheet(param);
        }
    }

    @Override
    public Long doAudit(HandingSheetParam param) {
        Long id = param.getId();
        if (id == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "盘货表ID不可缺失");
        } else {
            // 编辑
            return editHandingSheet(param);
        }
    }

    private Long editHandingSheet(HandingSheetParam param) {
        checkForEdit(param);
        final Long handingSheetId = param.getId();
        Boolean audit = param.getAudit();
        final HandingSheet oldHandingSheet = getById(handingSheetId);
        if (oldHandingSheet == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "找不到盘货表");
        }
        final List<HandingSheetStaff> oldHandingSheetStaffs = handingSheetStaffService.listBySheetId(handingSheetId);
        final List<HandingSheetActivityEvent> oldSheetActivityEvents = handingSheetActivityEventService.listByHandingSheetId(handingSheetId);
        List<HandingSheetItem> oldHandingSheetItems = handingSheetItemService.listBySheetId(handingSheetId);

        String paramSheetName = param.getName();
        Long paramStartTime = param.getStartTime();
        Long paramEndTime = param.getEndTime();
        List<Integer> paramPlatformVals = param.getPlatformVals();
        String paramLabel = param.getLabel();
        String paramAttachments = param.getAttachments();
        Integer paramActivityType = param.getActivityType();
        List<Long> paramStaffUids = param.getStaffUids();

        // update handing_sheet
        HandingSheet updateHandingSheet = new HandingSheet();
        updateHandingSheet.setId(handingSheetId);
        updateHandingSheet.setSheetName(paramSheetName);
        updateHandingSheet.setStartTime(paramStartTime);
        updateHandingSheet.setEndTime(paramEndTime);
        updateHandingSheet.setPlatform(listToString(paramPlatformVals));
        updateHandingSheet.setLabel(paramLabel);
        updateHandingSheet.setAttachment(paramAttachments);
        updateHandingSheet.setActivityType(paramActivityType);
        // 开始时间设置到未来，且状态为「进行中」，需要回退到「已过审」
        if (paramStartTime > DateUtil.getNowSecond()
                && Objects.equals(oldHandingSheet.getState(), HandingSheetStateEnum.PROCESSING.getValue())) {
            updateHandingSheet.setState(HandingSheetStateEnum.AUDITED.getValue());
        }
        handingSheetMapper.updateById(updateHandingSheet);

        // update handing_sheet_staff
        if (CollectionUtil.isEmpty(paramStaffUids)) {
            // 删除
            handingSheetStaffService.deleteBySheetId(handingSheetId);
        } else {
            // 删除原有的
            handingSheetStaffService.deleteBySheetId(handingSheetId);
            // 插入最新的
            List<HandingSheetStaff> sheetStaffs = new ArrayList<>();
            for (Long staffUid : paramStaffUids) {
                HandingSheetStaff handingSheetStaff = new HandingSheetStaff();
                handingSheetStaff.setHandingSheetId(handingSheetId);
                handingSheetStaff.setUserId(staffUid);
                sheetStaffs.add(handingSheetStaff);
            }
            if (CollectionUtil.isNotEmpty(sheetStaffs)) {
                handingSheetStaffService.saveBatch(sheetStaffs);
            }
        }

        // update handing_sheet_activity_event
        List<HandingSheetActivityEventParam> paramActivityEvents = param.getActivityEvents();
        if (CollectionUtil.isEmpty(paramActivityEvents)) {
            // 删除
            handingSheetActivityEventService.deleteBySheetId(handingSheetId);
        } else {
            // 删除原来的
            handingSheetActivityEventService.deleteBySheetId(handingSheetId);
            // 插入最新的
            List<HandingSheetActivityEvent> sheetActivityEvents = new ArrayList<>();
            for (HandingSheetActivityEventParam activityEvent : paramActivityEvents) {
                HandingSheetActivityEvent sheetActivityEvent = new HandingSheetActivityEvent();
                sheetActivityEvent.setHandingSheetId(handingSheetId);
                sheetActivityEvent.setReachedAmount(activityEvent.getReachedAmount());
                sheetActivityEvent.setReducedAmount(activityEvent.getReducedAmount());
                sheetActivityEvents.add(sheetActivityEvent);
            }
            if (CollectionUtil.isNotEmpty(sheetActivityEvents)) {
                handingSheetActivityEventService.saveBatch(sheetActivityEvents);
            }
        }

        // update handing_sheet_item
        List<HandingSheetItemParam> paramItemInfos = param.getItemInfos();
        Set<Long> delItemIds = new HashSet<>(16);

        //兼容逻辑，默认情况不去更新老的商品信息
        if (!param.isUseNewVersionView()) {
            updateOldItemInfos(handingSheetId, audit, oldHandingSheetItems, paramItemInfos, delItemIds);
        }

        // 审核编辑时，通过审核
        if (Boolean.TRUE.equals(audit)) {
            // 审核（更新时顺便审核）
            HandingSheet newestHandingSheet = getById(handingSheetId);
            if (Objects.equals(newestHandingSheet.getState(), HandingSheetStateEnum.TO_BE_AUDITED.getValue())) {
                audit(newestHandingSheet);
            }
        }

        // 异步发送企微消息
        final String currentNickName = UserContext.getNickName();
        ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
            try {
                // 盘货表基础信息变更发消息
                // 只有状态为「进行中」或「已过审」才会触发企微消息
                if (Objects.equals(oldHandingSheet.getState(), HandingSheetStateEnum.PROCESSING.getValue())
                        || Objects.equals(oldHandingSheet.getState(), HandingSheetStateEnum.AUDITED.getValue())) {
                    sendQwMsgWhenBaseInfoChanged(handingSheetId, oldHandingSheet, oldHandingSheetStaffs,
                            oldSheetActivityEvents, param, currentNickName);
                }
            } catch (Exception e) {
                log.error("盘货表基本信息变动发送企微消息发生了异常", e);
            }
            try {
                // 商品信息变更发消息
                // 只有状态为「进行中」或「已过审」才会触发企微消息
                // 2023-01-23 这段逻辑正常情况下应该不会运行了
                if (Objects.equals(oldHandingSheet.getState(), HandingSheetStateEnum.PROCESSING.getValue())
                        || Objects.equals(oldHandingSheet.getState(), HandingSheetStateEnum.AUDITED.getValue())) {
                    sendQwMsgWhenItemInfoChanged(oldHandingSheet, oldHandingSheetItems, paramItemInfos, delItemIds, currentNickName);
                }
            } catch (Exception e) {
                log.error("盘货表商品信息变动发送企微消息发生了异常", e);
            }
        });

        return handingSheetId;
    }

    private void updateOldItemInfos(Long handingSheetId, Boolean audit, List<HandingSheetItem> oldHandingSheetItems, List<HandingSheetItemParam> paramItemInfos, Set<Long> delItemIds) {
        List<Long> paramHandingSheetItemIds = paramItemInfos.stream()
                .map(HandingSheetItemParam::getHandingSheetItemId)
                .filter(id -> {
                    if (id == null) {
                        return false;
                    }
                    return true;
                })
                .distinct()
                .collect(Collectors.toList());

        // 审核状态下只能前端页面只能看到采购人为自己的商品，这样会导致误删
        if (!Boolean.TRUE.equals(audit)) {
            List<Long> handingSheetItemIdsOfNeedDel = new ArrayList<>();
            for (HandingSheetItem handingSheetItem : oldHandingSheetItems) {
                if (!paramHandingSheetItemIds.contains(handingSheetItem.getId())) {
                    handingSheetItemIdsOfNeedDel.add(handingSheetItem.getId());
                    delItemIds.add(handingSheetItem.getItemId());
                }
            }
            if (CollectionUtil.isNotEmpty(handingSheetItemIdsOfNeedDel)) {
                handingSheetItemService.deleteBatchIds(handingSheetItemIdsOfNeedDel);
            }
        }
        // 新增项或更新
        List<HandingSheetItem> addOrUpdateSheetItems = new ArrayList<>();
        for (HandingSheetItemParam paramItemInfo : paramItemInfos) {
            HandingSheetItem handingSheetItem = new HandingSheetItem();
            handingSheetItem.setId(paramItemInfo.getHandingSheetItemId());
            handingSheetItem.setHandingSheetId(handingSheetId);
            handingSheetItem.setItemNo(paramItemInfo.getItemNo());
            handingSheetItem.setItemSkuNo(paramItemInfo.getItemSkuNo());
            handingSheetItem.setItemId(paramItemInfo.getItemId());
            handingSheetItem.setItemSkuId(paramItemInfo.getItemSkuId());
            handingSheetItem.setActivePrice(paramItemInfo.getActivePrice());
            String giftDescription = paramItemInfo.getGiftDescription();
            if (StringUtils.hasText(giftDescription)) {
                handingSheetItem.setGiftDescription(giftDescription);
            }
            String giftCode = paramItemInfo.getGiftCode();
            if (StringUtils.hasText(giftCode)) {
                handingSheetItem.setGiftCode(giftCode);
            }
            String taobaoLink = paramItemInfo.getTaobaoLink();
            if (StringUtils.hasText(taobaoLink)) {
                handingSheetItem.setTaobaoLink(taobaoLink);
            }
            String douyinLink = paramItemInfo.getDouyinLink();
            if (StringUtils.hasText(douyinLink)) {
                handingSheetItem.setDouyinLink(douyinLink);
            }
            String miniProgramLink = paramItemInfo.getMiniProgramLink();
            if (StringUtils.hasText(miniProgramLink)) {
                handingSheetItem.setMiniProgramLink(miniProgramLink);
            }
            String remark = paramItemInfo.getRemark();
            if (StringUtils.hasText(remark)) {
                handingSheetItem.setRemark(remark);
            }
            addOrUpdateSheetItems.add(handingSheetItem);
        }
        if (CollectionUtil.isNotEmpty(addOrUpdateSheetItems)) {
            handingSheetItemService.saveOrUpdateBatch(addOrUpdateSheetItems);
        }
    }

    private void sendQwMsgWhenItemInfoChanged(HandingSheet oldHandingSheet, List<HandingSheetItem> oldHandingSheetItems,
                                              List<HandingSheetItemParam> paramItemInfos, Set<Long> delItemIds, String currentNickName) {
        Long handingSheetId = oldHandingSheet.getId();
        Map<Long, HandingSheetItem> map = handingSheetItemService.getMap(oldHandingSheetItems);
        Set<Long> newItemIds = new HashSet<>(16);
        Set<Long> updateItemIds = new HashSet<>(16);
        for (HandingSheetItemParam paramItemInfo : paramItemInfos) {
            Long handingSheetItemId = paramItemInfo.getHandingSheetItemId();
            if (handingSheetItemId == null) {
                newItemIds.add(paramItemInfo.getItemId());
            } else {
                HandingSheetItem oldHandingSheetItem = map.get(handingSheetItemId);
                if (oldHandingSheetItem == null) {
                    continue;
                }
                if (!Objects.equals(paramItemInfo.getGiftDescription(), oldHandingSheetItem.getGiftDescription())) {
                    updateItemIds.add(paramItemInfo.getItemId());
                }
                if (!Objects.equals(paramItemInfo.getGiftCode(), oldHandingSheetItem.getGiftCode())) {
                    updateItemIds.add(paramItemInfo.getItemId());
                }
                if (!Objects.equals(paramItemInfo.getTaobaoLink(), oldHandingSheetItem.getTaobaoLink())) {
                    updateItemIds.add(paramItemInfo.getItemId());
                }
                if (!Objects.equals(paramItemInfo.getDouyinLink(), oldHandingSheetItem.getDouyinLink())) {
                    updateItemIds.add(paramItemInfo.getItemId());
                }
                if (!Objects.equals(paramItemInfo.getMiniProgramLink(), oldHandingSheetItem.getMiniProgramLink())) {
                    updateItemIds.add(paramItemInfo.getItemId());
                }
                if (!Objects.equals(paramItemInfo.getRemark(), oldHandingSheetItem.getRemark())) {
                    updateItemIds.add(paramItemInfo.getItemId());
                }
                if (oldHandingSheetItem.getActivePrice().compareTo(paramItemInfo.getActivePrice()) != 0) {
                    updateItemIds.add(paramItemInfo.getItemId());
                }
            }
        }
        if (CollectionUtil.isNotEmpty(delItemIds)
                || CollectionUtil.isNotEmpty(newItemIds)
                || CollectionUtil.isNotEmpty(updateItemIds)) {
            int changedItemNum = delItemIds.size() + newItemIds.size() + updateItemIds.size();
            // 有商品发生变动，需要发送企微消息
            List<HandingSheetStaff> handingSheetStaffs = handingSheetStaffService.listBySheetId(handingSheetId);
            if (CollectionUtil.isEmpty(handingSheetStaffs)) {
                return;
            }
            List<Long> userIds = handingSheetStaffs.stream()
                    .map(HandingSheetStaff::getUserId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, DadStaffVO> staffMap = staffService.getStaffMap(userIds);
            // 对相关的每个用户发送企业微信消息
            for (Long userId : userIds) {
                DadStaffVO dadStaffVO = staffMap.get(userId);
                String qwUserId = Optional.ofNullable(dadStaffVO).map(DadStaffVO::getQwUserId).orElse("");
                if (StringUtils.hasText(qwUserId)) {
                    final String token = qyWeixinService.getTokenWithCache();
                    final SendQyWeixinMsgParam qwMsgParam = new SendQyWeixinMsgParam();
                    qwMsgParam.setTouser(qwUserId);
                    qwMsgParam.setAgentid(qyWeixinDaddyErpProperties.getAgentid());
                    qwMsgParam.setMsgtype("textcard");
                    SendQyWeixinMsgParam.TextCard textCard = SendQyWeixinMsgParam.TextCard.of(
                            currentNickName + "修改了" + oldHandingSheet.getSheetName() + "商品信息",
                            "共有" + changedItemNum + "个商品发生变动，请确认",
                            String.format(handingSheetProperties.getHandingSheetDetailUrl(), handingSheetId, oldHandingSheet.getState()));
                    qwMsgParam.setTextCard(textCard);
                    qyWeixinFeignClient.sendMessage(token, qwMsgParam);
                }
            }
        }
    }

    private void sendQwMsgWhenBaseInfoChanged(Long handingSheetId, HandingSheet oldHandingSheet, List<HandingSheetStaff> oldHandingSheetStaffs,
                                              List<HandingSheetActivityEvent> oldSheetActivityEvents, HandingSheetParam param, String currentNickName) {
        String paramSheetName = param.getName();
        Long paramStartTime = param.getStartTime();
        Long paramEndTime = param.getEndTime();
        List<Integer> paramPlatformVals = param.getPlatformVals();
        String paramLabel = param.getLabel();
        String paramAttachments = param.getAttachments();
        Integer paramActivityType = param.getActivityType();
        List<Long> paramStaffUids = param.getStaffUids();
        List<HandingSheetActivityEventParam> paramActivityEvents = param.getActivityEvents();

        boolean handingSheetBaseInfoUpdated = false;
        StringBuilder qwMsgSb = new StringBuilder();
        if (!Objects.equals(oldHandingSheet.getSheetName(), paramSheetName)) {
            handingSheetBaseInfoUpdated = true;
            qwMsgSb.append("表格名称由").append(oldHandingSheet.getSheetName()).append("变更为").append(paramSheetName)
                    .append("\n");
        }
        if (!Objects.equals(oldHandingSheet.getStartTime(), paramStartTime)) {
            handingSheetBaseInfoUpdated = true;
            qwMsgSb.append("活动开始时间由").append(DateUtil.format(oldHandingSheet.getStartTime())).append("变更为")
                    .append(DateUtil.format(paramStartTime)).append("\n");
        }
        if (!Objects.equals(oldHandingSheet.getEndTime(), paramEndTime)) {
            handingSheetBaseInfoUpdated = true;
            qwMsgSb.append("活动结束时间由").append(DateUtil.format(oldHandingSheet.getEndTime())).append("变更为")
                    .append(DateUtil.format(paramEndTime)).append("\n");
        }

        // 入参中的平台名称
        List<Integer> sortedParamPlatformVals = paramPlatformVals.stream()
                .sorted()
                .collect(Collectors.toList());
        StringBuilder sb1 = new StringBuilder();
        for (Integer platformVal : sortedParamPlatformVals) {
            Platform platformEnum = Platform.of(platformVal);
            if (platformEnum != null) {
                sb1.append(Objects.equals(platformEnum.getDesc(), "老爸商城") ? "小程序" : platformEnum.getDesc()).append(",");
            }
        }
        String paramPlatformNames = sb1.length() > 0 ? sb1.substring(0, sb1.length() - 1) : "";
        // oldHandingSheet 平台的名称
        List<Integer> sortedOldPlatformVals = Arrays.stream(oldHandingSheet.getPlatform().split(","))
                .map(Integer::valueOf).sorted()
                .collect(Collectors.toList());
        StringBuilder sb2 = new StringBuilder();
        for (Integer platformVal : sortedOldPlatformVals) {
            Platform platformEnum = Platform.of(platformVal);
            if (platformEnum != null) {
                sb2.append(Objects.equals(platformEnum.getDesc(), "老爸商城") ? "小程序" : platformEnum.getDesc()).append(",");
            }
        }
        String oldPlatformNames = sb2.length() > 0 ? sb2.substring(0, sb2.length() - 1) : "";
        if (!Objects.equals(paramPlatformNames, oldPlatformNames)) {
            handingSheetBaseInfoUpdated = true;
            qwMsgSb.append("所属平台由").append(oldPlatformNames).append("变更为").append(paramPlatformNames).append("\n");
        }
        if (!Objects.equals(paramLabel, oldHandingSheet.getLabel())) {
            handingSheetBaseInfoUpdated = true;
            qwMsgSb.append("活动标签由").append(oldHandingSheet.getLabel()).append("变更为").append(paramLabel).append("\n");
        }

        // 入参中的活动成员
        List<Long> sortedParamStaffUids = paramStaffUids.stream().sorted().collect(Collectors.toList());
        Map<Long, DadStaffVO> paramStaffMap = staffService.getStaffMap(sortedParamStaffUids);
        StringBuilder sb3 = new StringBuilder();
        for (Long staffUid : sortedParamStaffUids) {
            DadStaffVO dadStaffVO = paramStaffMap.get(staffUid);
            String nickName = Optional.ofNullable(dadStaffVO).map(DadStaffVO::getNickname).orElse("");
            if (StringUtils.hasText(nickName)) {
                sb3.append(nickName).append(",");
            }
        }
        String paramStaffNickNames = sb3.length() > 0 ? sb3.substring(0, sb3.length() - 1) : "";
        // oldHandingSheet 中的活动成员
        List<Long> sortedOldStaffUids = oldHandingSheetStaffs.stream()
                .map(HandingSheetStaff::getUserId)
                .sorted().collect(Collectors.toList());
        Map<Long, DadStaffVO> oldStaffMap = staffService.getStaffMap(sortedOldStaffUids);
        StringBuilder sb4 = new StringBuilder();
        for (Long staffUid : sortedOldStaffUids) {
            DadStaffVO dadStaffVO = paramStaffMap.get(staffUid);
            String nickName = Optional.ofNullable(dadStaffVO).map(DadStaffVO::getNickname).orElse("");
            if (StringUtils.hasText(nickName)) {
                sb4.append(nickName).append(",");
            }
        }
        String oldStaffNickNames = sb4.length() > 0 ? sb4.substring(0, sb4.length() - 1) : "";
        if (!Objects.equals(paramStaffNickNames, oldStaffNickNames)) {
            handingSheetBaseInfoUpdated = true;
            qwMsgSb.append("活动成员由").append(oldStaffNickNames).append("变更为").append(paramStaffNickNames).append("\n");
        }
        if (StringUtils.hasText(paramAttachments)
                && !Objects.equals(paramAttachments, oldHandingSheet.getAttachment())) {
            handingSheetBaseInfoUpdated = true;
            qwMsgSb.append("活动附件由").append(oldHandingSheet.getAttachment()).append("变更为").append(paramAttachments).append("\n");
        }
        if (!Objects.equals(paramActivityType, oldHandingSheet.getActivityType())) {
            handingSheetBaseInfoUpdated = true;
            HandingSheetActivityTypeEnum paramTypeEnum = HandingSheetActivityTypeEnum.of(paramActivityType);
            HandingSheetActivityTypeEnum oldTypeEnum = HandingSheetActivityTypeEnum.of(oldHandingSheet.getActivityType());
            String oldTypeDesc = Optional.ofNullable(oldTypeEnum).map(HandingSheetActivityTypeEnum::getDesc).orElse("未知类型");
            String paramTypeDesc = Optional.ofNullable(paramTypeEnum).map(HandingSheetActivityTypeEnum::getDesc).orElse("未知类型");
            qwMsgSb.append("活动类型").append(oldTypeDesc).append("变更为").append(paramTypeDesc).append("\n");
        }

        // 入参中的活动力度
        List<HandingSheetActivityEventParam> sortedParamActivityEvents = paramActivityEvents.stream()
                .sorted(Comparator.comparing(HandingSheetActivityEventParam::getReachedAmount))
                .collect(Collectors.toList());
        StringBuilder sb5 = new StringBuilder();
        for (HandingSheetActivityEventParam activityEvent : sortedParamActivityEvents) {
            BigDecimal reachedAmount = activityEvent.getReachedAmount();
            BigDecimal reducedAmount = activityEvent.getReducedAmount();
            sb5.append("满").append(reachedAmount).append("元，减").append(reducedAmount).append("元").append(",");
        }
        String paramActivityEventDesc = sb5.length() > 0 ? sb5.substring(0, sb5.length() - 1) : "";
        // oldHandingSheet 中的活动力度
        List<HandingSheetActivityEvent> sortedOldActivityEvents = oldSheetActivityEvents.stream()
                .sorted(Comparator.comparing(HandingSheetActivityEvent::getReachedAmount))
                .collect(Collectors.toList());
        StringBuilder sb6 = new StringBuilder();
        for (HandingSheetActivityEvent activityEvent : sortedOldActivityEvents) {
            BigDecimal reachedAmount = activityEvent.getReachedAmount();
            BigDecimal reducedAmount = activityEvent.getReducedAmount();
            sb6.append("满").append(reachedAmount).append("元，减").append(reducedAmount).append("元").append(",");
        }
        String oldActivityEventDesc = sb6.length() > 0 ? sb6.substring(0, sb6.length() - 1) : "";
        if (!Objects.equals(oldActivityEventDesc, paramActivityEventDesc)) {
            handingSheetBaseInfoUpdated = true;
            qwMsgSb.append("活动力度").append(oldActivityEventDesc).append("变更为").append(paramActivityEventDesc).append("\n");
        }
        if (handingSheetBaseInfoUpdated) {
            List<HandingSheetStaff> handingSheetStaffs = handingSheetStaffService.listBySheetId(handingSheetId);
            if (CollectionUtil.isEmpty(handingSheetStaffs)) {
                return;
            }
            List<Long> userIds = handingSheetStaffs.stream()
                    .map(HandingSheetStaff::getUserId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, DadStaffVO> staffMap = staffService.getStaffMap(userIds);
            // 对相关的每个用户发送企业微信消息
            for (Long userId : userIds) {
                DadStaffVO dadStaffVO = staffMap.get(userId);
                String qwUserId = Optional.ofNullable(dadStaffVO).map(DadStaffVO::getQwUserId).orElse("");
                if (StringUtils.hasText(qwUserId)) {
                    final String token = qyWeixinService.getTokenWithCache();
                    final SendQyWeixinMsgParam qwMsgParam = new SendQyWeixinMsgParam();
                    qwMsgParam.setTouser(qwUserId);
                    qwMsgParam.setAgentid(qyWeixinDaddyErpProperties.getAgentid());
                    qwMsgParam.setMsgtype("textcard");
                    SendQyWeixinMsgParam.TextCard textCard = SendQyWeixinMsgParam.TextCard.of(
                            currentNickName + "修改了" + oldHandingSheet.getSheetName() + "活动信息",
                            qwMsgSb.toString(),
                            String.format(handingSheetProperties.getHandingSheetDetailUrl(), handingSheetId, oldHandingSheet.getState()));
                    qwMsgParam.setTextCard(textCard);
                    qyWeixinFeignClient.sendMessage(token, qwMsgParam);
                }
            }
        }
    }

    @Override
    public SingleResponse<HandingSheetBaseVo> getBaseInfo(Long id) {
        HandingSheet handingSheet = handingSheetMapper.selectById(id);
        if (handingSheet == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "找不到盘货表");
        }

        // to vo
        HandingSheetBaseVo vo = toHandingSheetBaseVo(handingSheet);
        return SingleResponse.of(vo);
    }

    @Override
    public PageResponse<HandingSheetItemVo> sheetItemInfoPage(HandingSheetItemPageQuery pageQuery) {
        if (pageQuery.getHandingSheetId() == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "盘货表ID必传");
        }
        HandingSheet handingSheet = getById(pageQuery.getHandingSheetId());
        if (handingSheet == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "找不到盘货表");
        }
        IPage<HandingSheetItem> sheetItemPage;
        Page<HandingSheetItem> page = new Page<>();
        // 不使用 mybatis-plus 自带的 count（理由是速度很慢）
        page.setSearchCount(false);
        page.setCurrent(pageQuery.getPageIndex());
        page.setSize(pageQuery.getPageSize());
        if (Boolean.TRUE.equals(pageQuery.getQueryForAudit())) {
            QueryWrapper<HandingSheetItem> queryWrapper = getSheetItemQueryWrapperForAudit(pageQuery, UserContext.getUserId());
            page.setTotal(handingSheetItemService.selectPageListCountForAudit(queryWrapper));
            sheetItemPage = handingSheetItemService.selectPageListForAudit(page, queryWrapper);
        } else {
            QueryWrapper<HandingSheetItem> queryWrapper = getSheetItemQueryWrapper(pageQuery);
            page.setTotal(handingSheetItemService.selectPageListCount(queryWrapper));
            sheetItemPage = handingSheetItemService.selectPageList(page, queryWrapper);
        }
        // to vo
        List<HandingSheetItemVo> vos = toSheetItemVos(sheetItemPage, handingSheet);
        return PageResponse.of(vos, (int) page.getTotal(), pageQuery.getPageSize(), pageQuery.getPageIndex());
    }

    private QueryWrapper<HandingSheetItem> getSheetItemQueryWrapperForAudit(HandingSheetItemPageQuery pageQuery, Long currentUserId) {
        QueryWrapper<HandingSheetItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("hsi.is_del", 0);
        queryWrapper.eq("ip.is_del", 0);
        queryWrapper.eq("b.is_del", 0);
        queryWrapper.eq("hsi.handing_sheet_id", pageQuery.getHandingSheetId());
        queryWrapper.eq("b.user_id", currentUserId);
        return queryWrapper;
    }

    @Override
    public void delete(HandingSheetDelParam param) {
        Long handingSheetId = param.getId();
        if (handingSheetId == null || handingSheetId <= 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "参数不合法");
        }
        HandingSheet handingSheet = handingSheetMapper.selectById(handingSheetId);
        if (handingSheet == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "找不到盘货表");
        }
        if (!Objects.equals(handingSheet.getState(), HandingSheetStateEnum.TO_BE_SUBMIT.getValue())) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "只有待提交的盘货表才可以删除");
        }
        handingSheetMapper.deleteById(handingSheetId);
    }

    @Override
    public void cancelAudit(HandingSheetCancelAuditParam param) {
        Long handingSheetId = param.getId();
        if (handingSheetId == null || handingSheetId <= 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "参数不合法");
        }
        HandingSheet handingSheet = handingSheetMapper.selectById(handingSheetId);
        if (handingSheet == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "找不到盘货表");
        }
        if (!Objects.equals(handingSheet.getState(), HandingSheetStateEnum.TO_BE_AUDITED.getValue())) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "只有待审核的盘货表才可以撤销审核");
        }
        handingSheetMapper.updateStatusByHandingSheetId(handingSheetId, HandingSheetStateEnum.TO_BE_SUBMIT.getValue(),
                HandingSheetStateEnum.TO_BE_AUDITED.getValue());
    }

    @Override
    public void submitAudit(HandingSheetSubmitAuditParam param) {
        Long handingSheetId = param.getId();
        if (handingSheetId == null || handingSheetId <= 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "参数不合法");
        }
        HandingSheet handingSheet = handingSheetMapper.selectById(handingSheetId);
        if (handingSheet == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "找不到盘货表");
        }
        if (!Objects.equals(handingSheet.getState(), HandingSheetStateEnum.TO_BE_SUBMIT.getValue())) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "只有待提交的盘货表才可以提交审核");
        }
        // status to TO_BE_AUDITED
        handingSheetMapper.updateStatusByHandingSheetId(handingSheetId, HandingSheetStateEnum.TO_BE_AUDITED.getValue(),
                HandingSheetStateEnum.TO_BE_SUBMIT.getValue());

        final String handingSheetName = handingSheet.getSheetName();
        final String currentNickName = UserContext.getNickName();
        // 异步发企微的消息
        ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
            final List<HandingSheetItemSku> handingSheetItemSkus = handingSheetItemSkuService.selectByHandingSheetId(handingSheetId);
            if (CollectionUtil.isEmpty(handingSheetItemSkus)) {
                return;
            }
            final Map<StaffBrief, List<HandingSheetItemSku>> sheetItemsGroupByBuyer = handingSheetItemSkus.stream()
                    .filter(item -> Objects.nonNull(getBuyerFromSheetItemSku(item)))
                    .collect(Collectors.groupingBy(this::getBuyerFromSheetItemSku));

            for (Map.Entry<StaffBrief, List<HandingSheetItemSku>> entry : sheetItemsGroupByBuyer.entrySet()) {
                final StaffBrief buyer = entry.getKey();
                final List<HandingSheetItemSku> skus = entry.getValue();
                long itemCount = skus.size();

                final StaffBrief staffBrief = StaffAssembler.INST.toStaffBrief(buyer.getUserId());
                String qwUserId = staffBrief.getQwUserId();
                // 发送企微消息
                if (StringUtils.hasText(qwUserId)) {
                    final WechatMsg wechatMsg = new WechatMsg();
                    wechatMsg.setTitle(String.format("%s 提交了 %s 盘货表，请及时查看审核！", currentNickName, handingSheetName));
                    wechatMsg.setContent(String.format("共 %s 个商品待您确认！", itemCount));
                    wechatMsg.setLink(refreshConfig.getDomain() +
                            String.format("/operation-management/inventory/edit?id=%s&state=-1", handingSheetId));

                    wechatMsg.setRecipient(qwUserId);
                    wechatMsg.setType(1);
                    msgSender.send(wechatMsg);
                }
            }
        });
    }

    private StaffBrief getBuyerFromSheetItemSku(HandingSheetItemSku item) {
        return Optional.ofNullable(item.getData())
                .map(HandingSheetItemSkuPersistData::getBizFields)
                .map(HandingSheetItemSkuBizFields::getBuyer).orElse(null);
    }

//    @Override
//    public void audit(HandingSheetAuditParam param) {
//        Long handingSheetId = param.getId();
//        if (handingSheetId == null || handingSheetId <= 0) {
//            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "参数不合法");
//        }
//    }

    @Override
    public void addActivityText(HandingSheetActivityTextParam param) {
        Long handingSheetId = param.getHandingSheetId();
        if (handingSheetId == null || handingSheetId <= 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "参数不合法");
        }
        HandingSheet handingSheet = getById(handingSheetId);
        if (handingSheet == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "找不到盘货表");
        }
        if (Objects.equals(handingSheet.getState(), HandingSheetStateEnum.EXPIRED.getValue())) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "盘货表已过期，店铺活动内容不可操作");
        }
        String activityName = param.getActivityName();
        if (!StringUtils.hasText(activityName)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "活动名称必填");
        }
        int activityNameLengthLimit = 50;
        if (activityName.length() > activityNameLengthLimit) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "活动名称最多" + activityNameLengthLimit + "个字符");
        }

        HandingSheetActivityText activityText = new HandingSheetActivityText();
        activityText.setHandingSheetId(handingSheetId);
        activityText.setActivityName(activityName);
        activityText.setContent(param.getActivityContent());
        handingSheetActivityTextService.save(activityText);

        final String currentNickName = UserContext.getNickName();
        final Long finalHandingSheetId = handingSheetId;
        final String finalActivityName = activityName;
        // 异步发送企微消息
        ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
            List<HandingSheetStaff> handingSheetStaffs = handingSheetStaffService.listBySheetId(finalHandingSheetId);
            List<Long> userIds = handingSheetStaffs.stream()
                    .map(HandingSheetStaff::getUserId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, DadStaffVO> staffMap = staffService.getStaffMap(userIds);
            // 对相关的每个用户发送企业微信消息
            for (Long userId : userIds) {
                DadStaffVO dadStaffVO = staffMap.get(userId);
                String qwUserId = Optional.ofNullable(dadStaffVO).map(DadStaffVO::getQwUserId).orElse("");
                if (StringUtils.hasText(qwUserId)) {
                    final String token = qyWeixinService.getTokenWithCache();
                    final SendQyWeixinMsgParam qwMsgParam = new SendQyWeixinMsgParam();
                    qwMsgParam.setTouser(qwUserId);
                    qwMsgParam.setAgentid(qyWeixinDaddyErpProperties.getAgentid());
                    qwMsgParam.setMsgtype("textcard");
                    SendQyWeixinMsgParam.TextCard textCard = SendQyWeixinMsgParam.TextCard.of(
                            currentNickName + "新增了店铺活动信息",
                            "新增：" + finalActivityName,
                            handingSheetProperties.getHandingSheetActivityTabUrl());
                    qwMsgParam.setTextCard(textCard);
                    qyWeixinFeignClient.sendMessage(token, qwMsgParam);
                }
            }
        });
    }

    @Override
    public void saveOrUpdateActivityText(HandingSheetActivityTextParam param) {
        // check
        Long handingSheetId = param.getHandingSheetId();
        if (handingSheetId == null || handingSheetId <= 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "参数不合法");
        }
        HandingSheet handingSheet = getById(handingSheetId);
        if (handingSheet == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "找不到盘货表");
        }
        if (Objects.equals(handingSheet.getState(), HandingSheetStateEnum.EXPIRED.getValue())) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "盘货表已过期，店铺活动内容不可操作");
        }
        String activityName = param.getActivityName();
        if (!StringUtils.hasText(activityName)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "活动名称必填");
        }
        int activityNameLengthLimit = 50;
        if (activityName.length() > activityNameLengthLimit) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "活动名称最多" + activityNameLengthLimit + "个字符");
        }

        // 新增 or 更新
        boolean isUpdated = false;
        Long handingSheetActivityTextId = param.getHandingSheetActivityTextId();
        Long nowSecond = DateUtil.getNowSecond();
        if (handingSheetActivityTextId == null) {
            // 新增
            HandingSheetActivityText activityText = new HandingSheetActivityText();
            activityText.setHandingSheetId(handingSheetId);
            activityText.setActivityName(activityName);
            activityText.setContent(param.getActivityContent());
            activityText.setCreatedAt(nowSecond);
            activityText.setUpdatedAt(nowSecond);
            handingSheetActivityTextService.save(activityText);
        } else {
            // 更新
            HandingSheetActivityText activityText = new HandingSheetActivityText();
            activityText.setId(handingSheetActivityTextId);
            activityText.setHandingSheetId(handingSheetId);
            activityText.setActivityName(activityName);
            activityText.setContent(param.getActivityContent());
            activityText.setUpdatedAt(nowSecond);
            handingSheetActivityTextService.updateById(activityText);
            isUpdated = true;
        }

        final String currentNickName = UserContext.getNickName();
        final Long finalHandingSheetId = handingSheetId;
        final String finalActivityName = activityName;
        final boolean finalIsUpdated = isUpdated;
        final Integer finalHandingSheetState = handingSheet.getState();
        // 异步发送消息
        ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
            // 盘货物表关联的成员
            List<HandingSheetStaff> handingSheetStaffs = handingSheetStaffService.listBySheetId(finalHandingSheetId);
            if (CollectionUtil.isEmpty(handingSheetStaffs)) {
                return;
            }
            List<Long> userIds = handingSheetStaffs.stream()
                    .map(HandingSheetStaff::getUserId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, DadStaffVO> staffMap = staffService.getStaffMap(userIds);
            // 对相关的每个用户发送企业微信消息
            for (Long userId : userIds) {
                DadStaffVO dadStaffVO = staffMap.get(userId);
                String qwUserId = Optional.ofNullable(dadStaffVO).map(DadStaffVO::getQwUserId).orElse("");
                if (StringUtils.hasText(qwUserId)) {
                    final String token = qyWeixinService.getTokenWithCache();
                    final SendQyWeixinMsgParam qwMsgParam = new SendQyWeixinMsgParam();
                    qwMsgParam.setTouser(qwUserId);
                    qwMsgParam.setAgentid(qyWeixinDaddyErpProperties.getAgentid());
                    qwMsgParam.setMsgtype("textcard");

                    SendQyWeixinMsgParam.TextCard textCard;
                    String handingSheetActivityTabUrl = handingSheetProperties.getHandingSheetActivityTabUrl();
                    if (finalIsUpdated) {
                        textCard = SendQyWeixinMsgParam.TextCard.of(
                                currentNickName + "修改了店铺活动信息",
                                "修改：" + finalActivityName,
                                String.format(handingSheetActivityTabUrl, finalHandingSheetId, finalHandingSheetState));
                    } else {
                        textCard = SendQyWeixinMsgParam.TextCard.of(
                                currentNickName + "新增了店铺活动信息",
                                "新增：" + finalActivityName,
                                String.format(handingSheetActivityTabUrl, finalHandingSheetId, finalHandingSheetState));
                    }
                    qwMsgParam.setTextCard(textCard);
                    log.info("盘货表新增/修改活动信息，对{}发送企微消息", qwUserId);
                    SendMsgQyWeixinResult sendMsgQyWeixinResult = qyWeixinFeignClient.sendMessage(token, qwMsgParam);
                    log.info("盘货表新增/修改活动信息，对{}发送企微消息的结果是{}", qwUserId, JSONObject.toJSONString(sendMsgQyWeixinResult));
                }
            }
        });
    }

    public static void main(String[] args) {
        String s = "http://p.dlab.cn/erp/operation-management/inventory/edit?id=%s&state=%s";
        String format = String.format(s, 123, 3);
        System.out.println(format);
    }

    private void audit(HandingSheet handingSheet) {
        Long handingSheetId = handingSheet.getId();
        // 将采购人为当前用户且是此盘货表下的 handing_sheet_item 变为审核通过
        List<HandingSheetItem> handingSheetItems = handingSheetItemService
                .listByHandingSheetIdAndBuyerUid(handingSheetId, UserContext.getUserId());
        List<HandingSheetItem> updateHandingSheetItems = new ArrayList<>();
        for (HandingSheetItem handingSheetItem : handingSheetItems) {
            HandingSheetItem updateHandingSheetItem = new HandingSheetItem();
            updateHandingSheetItem.setId(handingSheetItem.getId());
            updateHandingSheetItem.setPassed(true);
            updateHandingSheetItems.add(updateHandingSheetItem);
        }
        if (CollectionUtil.isNotEmpty(updateHandingSheetItems)) {
            handingSheetItemService.updateBatchById(updateHandingSheetItems);
        }
        // 更新完后，查询此盘货下的 handing_sheet_item 是否都是已审核通过的。若是，需要需要更新 handing_sheet 状态
        List<Boolean> passedColumns = handingSheetItemService.listPassedColumnByHandingSheetId(handingSheetId);
        if (CollectionUtil.isNotEmpty(passedColumns)) {
            boolean allPassed = true;
            for (Boolean passedColumn : passedColumns) {
                if (!Boolean.TRUE.equals(passedColumn)) {
                    allPassed = false;
                    break;
                }
            }
            if (allPassed) {
                Long startTime = handingSheet.getStartTime();
                Long endTime = handingSheet.getEndTime();
                Long nowSecond = DateUtil.getNowSecond();
                if (nowSecond >= startTime && nowSecond < endTime) {
                    handingSheetMapper.updateStatusByHandingSheetId(handingSheetId,
                            HandingSheetStateEnum.PROCESSING.getValue(), HandingSheetStateEnum.TO_BE_AUDITED.getValue());
                } else {
                    handingSheetMapper.updateStatusByHandingSheetId(handingSheetId,
                            HandingSheetStateEnum.AUDITED.getValue(), HandingSheetStateEnum.TO_BE_AUDITED.getValue());
                }
            }
        }
    }

    @Override
    public void deleteActivityText(HandingSheetActivityTextDelParam param) {
        Long textId = param.getHandingSheetActivityTextId();
        if (textId == null || textId <= 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "参数不合法");
        }
        HandingSheet handingSheet = getByHandingSheetActivityTextId(textId);
        if (handingSheet == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "找不到盘货表");
        }
        if (Objects.equals(handingSheet.getState(), HandingSheetStateEnum.EXPIRED.getValue())) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "盘货表已过期，店铺活动内容不可操作");
        }
        HandingSheetActivityText activityText = handingSheetActivityTextService.getById(textId);
        if (activityText == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "店铺活动富文本内容找不到");
        }

        Long createdUid = activityText.getCreatedUid();
        if (!Objects.equals(createdUid, param.getCurrentUserId())) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "仅创建人可删除");
        }
        handingSheetActivityTextService.removeById(textId);
    }

    @Override
    public PageResponse<HandingSheetActivityTextPageVo> pageQueryActivityText(HandingSheetActivityTextPageQuery pageQuery) {
        QueryWrapper<HandingSheetActivityText> queryWrapper = getActivityTextQueryWrapper(pageQuery);

        // 不使用 mybatis-plus 自带的 count（理由是速度很慢）
        Page<HandingSheetActivityText> page = new Page<>();
        page.setSearchCount(false);
        page.setTotal(handingSheetActivityTextService.selectPageListCount(queryWrapper));
        page.setCurrent(pageQuery.getPageIndex());
        page.setSize(pageQuery.getPageSize());

        IPage<HandingSheetActivityText> activityTextPage = handingSheetActivityTextService.selectPageList(page, queryWrapper);

        List<HandingSheetActivityTextPageVo> vos = toActivityTextPageVo(activityTextPage);
        return PageResponse.of(vos, (int) page.getTotal(), pageQuery.getPageSize(), pageQuery.getPageIndex());
    }

    @Override
    public void exportExcel(HandingSheetPageQuery queryPage) {
        ExportTask exportTask = new ExportTask();
        exportTask.setStatus(ExportTaskStatus.RUNNING);
        exportTask.setName("盘货表导出-" + DateUtil.formatNow(DatePattern.PURE_DATETIME_PATTERN));
        exportTask.setType(ExportTaskType.HANDING_SHEET);
        final Long taskId = exportTaskGateway.saveOrUpdateExportTask(exportTask);
        exportTask.setId(taskId);

        ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
            try (final ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
                queryPage.setPageSize(999999);
                // 文件头
                final List<List<String>> head = exportHead();
                List<List<String>> distinctHead = head.stream().distinct().collect(Collectors.toList());
                log.info("正在进行盘货表导出，当前导出列：" + distinctHead);
                // 文件数据
                final List<List<String>> exportData = getExportData(queryPage);
                // 写入文件流
                EasyExcel.write(byteArrayOutputStream)
                        .useDefaultStyle(false)
                        .head(distinctHead)
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .sheet("工作表1")
                        .doWrite(exportData);
                // 上传文件
                UploadFileAction action = UploadFileAction.ofInputStream(
                        new ByteArrayInputStream(byteArrayOutputStream.toByteArray()),
                        exportTask.getName() + ".xlsx");
                final String downloadUrl = fileGateway.uploadFile(action).getUrl();
                log.info("盘货表导出成功，下载链接：{}", downloadUrl);

                exportTask.setStatus(ExportTaskStatus.SUCCESS);
                exportTask.setDownloadUrl(downloadUrl);
            } catch (Exception e) {
                log.error("盘货表导出异常", e);
                exportTask.setError(e.getMessage());
                exportTask.setStatus(ExportTaskStatus.FAIL);
                exportTask.setError(e.getMessage());
            } finally {
                exportTaskGateway.saveOrUpdateExportTask(exportTask);
            }
        });
    }

    /**
     * @param queryPage 查询参数
     * @deprecated 废弃，使用V2版本接口
     */
    @Override
    public void exportItemInfoExcel(HandingSheetItemPageQuery queryPage) {
        ExportTask exportTask = new ExportTask();
        exportTask.setStatus(ExportTaskStatus.RUNNING);
        exportTask.setName("盘货表关联商品导出-" + DateUtil.formatNow(DatePattern.PURE_DATETIME_PATTERN));
        exportTask.setType(ExportTaskType.HANDING_SHEET_ITEMS);
        final Long taskId = exportTaskGateway.saveOrUpdateExportTask(exportTask);
        exportTask.setId(taskId);

        ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
            try (final ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
                queryPage.setPageSize(999999);
                // 文件头
                final List<List<String>> head = exportHeadForHandingSheetItem();
                List<List<String>> distinctHead = head.stream().distinct().collect(Collectors.toList());
                log.info("正在进行盘货表商品导出，当前导出列：" + distinctHead);
                // 文件数据
                final List<List<String>> exportData = getExportDataForHandingSheetItem(queryPage);
                // 写入文件流
                EasyExcel.write(byteArrayOutputStream)
                        .useDefaultStyle(false)
                        .head(distinctHead)
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .sheet("工作表1")
                        .doWrite(exportData);
                // 上传文件
                UploadFileAction action = UploadFileAction.ofInputStream(
                        new ByteArrayInputStream(byteArrayOutputStream.toByteArray()),
                        exportTask.getName() + ".xlsx");
                final String downloadUrl = fileGateway.uploadFile(action).getUrl();
                log.info("盘货表商品导出成功，下载链接：{}", downloadUrl);
                // 更新导出任务
                exportTask.setStatus(ExportTaskStatus.SUCCESS);
                exportTask.setDownloadUrl(downloadUrl);
            } catch (Exception e) {
                log.error("盘货表关联商品导出异常", e);
                exportTask.setError(e.getMessage());
                exportTask.setStatus(ExportTaskStatus.FAIL);
                exportTask.setError(e.getMessage());
            } finally {
                exportTaskGateway.saveOrUpdateExportTask(exportTask);
            }
        });
    }

    private List<List<String>> getExportDataForHandingSheetItem(HandingSheetItemPageQuery queryPage) {
        PageResponse<HandingSheetItemVo> response = sheetItemInfoPage(queryPage);
        List<HandingSheetItemVo> pageVos = response.getData();
        List<HandingSheetItemExportVo> exportVos = new ArrayList<>();

        // to exportVos
        for (HandingSheetItemVo pageVo : pageVos) {
            HandingSheetItemExportVo exportVo = new HandingSheetItemExportVo();
            exportVo.setItemNo(pageVo.getItemNo());
            exportVo.setItemStandardName(pageVo.getItemStandardName());
            exportVo.setItemSkuNo(pageVo.getItemSkuNo());
            exportVo.setItemSkuName(pageVo.getItemSkuName());
            exportVo.setDailyPrice(pageVo.getDailyPrice());
            exportVo.setActivePrice(pageVo.getActivePrice());
            exportVo.setArrivalPrice(pageVo.getArrivalPrice());
            exportVo.setCostPrice(pageVo.getCostPrice());
            exportVo.setGrossProfitMargin(pageVo.getGrossProfitMargin());
            exportVo.setGiftDescription(pageVo.getGiftDescription());
            exportVo.setGiftCode(pageVo.getGiftCode());
            exportVo.setTaobaoLink(pageVo.getTaobaoLink());
            exportVo.setDouyinLink(pageVo.getDouyinLink());
            exportVo.setMiniProgramLink(pageVo.getMiniProgramLink());
            exportVo.setRemark(pageVo.getRemark());
            exportVos.add(exportVo);
        }

        final List<String> hiddenFields = getHiddenFields();
        final List<List<String>> data = new ArrayList<>();
        final Field[] fields = ReflectUtil.getFields(HandingSheetItemExportVo.class);
        for (HandingSheetItemExportVo exportVo : exportVos) {
            final ArrayList<String> row = new ArrayList<>();
            for (Field field : fields) {
                final String name = field.getName();
                if ((field.getModifiers() & Modifier.STATIC) > 0) {
                    continue;
                }
                final PropertyAlias propertyAlias = field.getAnnotation(PropertyAlias.class);
                if (hiddenFields.contains(name) || propertyAlias != null && hiddenFields
                        .contains(propertyAlias.value())) {
                    continue;
                }
                final Object fieldValue = BeanUtil.getFieldValue(exportVo, name);
                row.add(fieldValue != null ? fieldValue.toString() : "");
            }
            data.add(row);
        }
        return data;
    }

    private List<List<String>> exportHeadForHandingSheetItem() {
        final Field[] fields = ReflectUtil.getFields(HandingSheetItemExportVo.class);
        final ImmutableList.Builder<List<String>> headBuilder = ImmutableList.builder();
        final List<String> hiddenFields = getHiddenFields();
        for (Field field : fields) {
            final String name = field.getName();
            final PropertyAlias propertyAlias = field.getAnnotation(PropertyAlias.class);
            if (hiddenFields.contains(name) || propertyAlias != null && hiddenFields
                    .contains(propertyAlias.value())) {
                continue;
            }
            final ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
            if (annotation == null) {
                continue;
            }
            headBuilder.add(ImmutableList.of(annotation.value()[0]));
        }
        return headBuilder.build();
    }

    @Override
    public void updateHandingSheetItem(HandingSheetItemUpdateParam param) {
        Long handingSheetItemId = param.getHandingSheetItemId();
        if (handingSheetItemId == null || handingSheetItemId <= 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "盘货表和商品关联关系ID不可缺失");
        }
        HandingSheetItem oldHandingSheetItem = handingSheetItemService.getById(handingSheetItemId);
        if (oldHandingSheetItem == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "找不到盘货表商品");
        }
        BigDecimal activePrice = param.getActivePrice();
        String giftCode = param.getGiftCode();
        String remark = param.getRemark();
        String giftDescription = param.getGiftDescription();
        if (StringUtils.hasText(giftDescription) && giftDescription.length() > giftDescriptionLengthLimit) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "赠品机制最多" + giftDescriptionLengthLimit + "字符");
        }
        if (StringUtils.hasText(remark) && remark.length() > remarkLengthLimit) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "商品备注最多" + remarkLengthLimit + "字符");
        }
        if (activePrice != null && activePrice.compareTo(BigDecimal.ZERO) <= 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "活动价必须大于0");
        }
        HandingSheet handingSheet = getByHandingSheetItemId(handingSheetItemId);
        if (handingSheet == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "找不到盘货表");
        }
        Integer state = handingSheet.getState();
        if (Objects.equals(state, HandingSheetStateEnum.EXPIRED.getValue())) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "盘货表状态为已过期，不可编辑");
        }
        // 产品说可以编辑
//        if (Objects.equals(state, HandingSheetStateEnum.TO_BE_AUDITED.getValue())) {
//            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "盘货表状态为待审核，不可编辑");
//        }

        HandingSheetItem updateHandingSheetItem = new HandingSheetItem();
        updateHandingSheetItem.setId(handingSheetItemId);
        if (activePrice != null && activePrice.compareTo(BigDecimal.ZERO) > 0) {
            updateHandingSheetItem.setActivePrice(activePrice);
        }
        if (StringUtils.hasText(giftCode)) {
            updateHandingSheetItem.setGiftCode(giftCode);
        }
        if (StringUtils.hasText(remark)) {
            updateHandingSheetItem.setRemark(remark);
        }
        if (StringUtils.hasText(giftDescription)) {
            updateHandingSheetItem.setGiftDescription(giftDescription);
        }
        handingSheetItemService.updateById(updateHandingSheetItem);

        Long userId = UserContext.getUserId();
        String currentNickName = UserContext.getNickName();
        ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
            // 异步打日志
            // 只有已过审的盘货表才记录价格日志
            try {
                if (Objects.equals(handingSheet.getState(), HandingSheetStateEnum.AUDITED.getValue()) ||
                        Objects.equals(handingSheet.getState(), HandingSheetStateEnum.PROCESSING.getValue())) {
                    List<HandingSheetActivityEvent> sheetActivityEvents = handingSheetActivityEventService.listByHandingSheetId(handingSheet.getId());
                    BigDecimal arrivalPrice = handingSheetItemService.calcArrivalPrice(activePrice, sheetActivityEvents, handingSheet.getActivityType());
                    String data = "";
                    operateLogDomainService.addOperatorLog(userId, OperateLogTarget.HANDING_SHEET_ITEM_PRICE, oldHandingSheetItem.getItemId(),
                            handingSheet.getSheetName() + "\n" +
                                    "活动价：" + activePrice + "\n" +
                                    "单买到手价：" + arrivalPrice, data);
                }
            } catch (Exception e) {
                log.error("盘货表编辑商品价格，记录日志价格日志发生异常", e);
            }

            // 商品信息变更发消息
            // 只有状态为「进行中」或「已过审」才会触发企微消息
            if (Objects.equals(handingSheet.getState(), HandingSheetStateEnum.PROCESSING.getValue())
                    || Objects.equals(handingSheet.getState(), HandingSheetStateEnum.AUDITED.getValue())) {
                // 有商品发生变动，需要发送企微消息
                List<HandingSheetStaff> handingSheetStaffs = handingSheetStaffService.listBySheetId(handingSheet.getId());
                if (CollectionUtil.isEmpty(handingSheetStaffs)) {
                    return;
                }
                List<Long> userIds = handingSheetStaffs.stream()
                        .map(HandingSheetStaff::getUserId)
                        .distinct()
                        .collect(Collectors.toList());
                Map<Long, DadStaffVO> staffMap = staffService.getStaffMap(userIds);
                // 对相关的每个用户发送企业微信消息
                for (Long staffUId : userIds) {
                    DadStaffVO dadStaffVO = staffMap.get(staffUId);
                    String qwUserId = Optional.ofNullable(dadStaffVO).map(DadStaffVO::getQwUserId).orElse("");
                    if (StringUtils.hasText(qwUserId)) {
                        final String token = qyWeixinService.getTokenWithCache();
                        final SendQyWeixinMsgParam qwMsgParam = new SendQyWeixinMsgParam();
                        qwMsgParam.setTouser(qwUserId);
                        qwMsgParam.setAgentid(qyWeixinDaddyErpProperties.getAgentid());
                        qwMsgParam.setMsgtype("textcard");
                        SendQyWeixinMsgParam.TextCard textCard = SendQyWeixinMsgParam.TextCard.of(
                                currentNickName + "修改了" + handingSheet.getSheetName() + "商品信息",
                                "共有" + 1 + "个商品发生变动，请确认",
                                String.format(handingSheetProperties.getHandingSheetDetailUrl(), handingSheet.getId(),
                                        handingSheet.getState()));
                        qwMsgParam.setTextCard(textCard);
                        qyWeixinFeignClient.sendMessage(token, qwMsgParam);
                    }
                }
            }
        });
    }

    @Override
    public HandingSheet getByHandingSheetItemId(Long handingSheetItemId) {
        return handingSheetMapper.getByHandingSheetItemId(handingSheetItemId);
    }

    @Override
    public HandingSheet getByHandingSheetActivityTextId(Long handingSheetActivityTextId) {
        return handingSheetMapper.getByHandingSheetActivityTextId(handingSheetActivityTextId);
    }

    @Override
    public SingleResponse<ArrivalPriceVo> refreshPrice(HandingSheetRefreshPriceParam param) {
        List<BigDecimal> activePrices = param.getActivePrices();
        Integer activityType = param.getActivityType();
        List<HandingSheetActivityEventVo> activityEventVos = param.getActivityEvents();
        if (CollectionUtil.isEmpty(activityEventVos)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "请填写活动力度！");
        }
        List<HandingSheetActivityEvent> activityEvents = new ArrayList<>();
        for (HandingSheetActivityEventVo activityEventVo : activityEventVos) {
            HandingSheetActivityEvent activityEvent = new HandingSheetActivityEvent();
            activityEvent.setReachedAmount(activityEventVo.getReachedAmount());
            activityEvent.setReducedAmount(activityEventVo.getReducedAmount());
            activityEvents.add(activityEvent);
        }
        List<ArrivalPriceVo.Price> priceList = new ArrayList<>();
        for (BigDecimal activePrice : activePrices) {
            BigDecimal arrivalPrice = handingSheetItemService.calcArrivalPrice(activePrice, activityEvents, activityType);
            ArrivalPriceVo.Price price = new ArrivalPriceVo.Price(activePrice, arrivalPrice);
            priceList.add(price);
        }
        ArrivalPriceVo vo = new ArrivalPriceVo();
        vo.setPriceList(priceList);
        return SingleResponse.of(vo);
    }

    @Override
    public SingleResponse<HandingSheetStatusCountVo> statusCount(HandingSheetPageQuery query) {
        QueryWrapper<HandingSheet> queryWrapper = getQueryWrapper(query);
        // 全部
        long totalCount = handingSheetMapper
                .selectPageListCountWithStatus(queryWrapper, null);
        // 待提交
        long toBeSubmitCount = handingSheetMapper
                .selectPageListCountWithStatus(queryWrapper, HandingSheetStateEnum.TO_BE_SUBMIT.getValue());
        // 待审核
        long toBeAuditedCount = handingSheetMapper
                .selectPageListCountWithStatus(queryWrapper, HandingSheetStateEnum.TO_BE_AUDITED.getValue());
        // 已过审
        long auditedCount = handingSheetMapper
                .selectPageListCountWithStatus(queryWrapper, HandingSheetStateEnum.AUDITED.getValue());
        // 进行中
        long processingCount = handingSheetMapper
                .selectPageListCountWithStatus(queryWrapper, HandingSheetStateEnum.PROCESSING.getValue());
        // 已过期
        long expiredCount = handingSheetMapper
                .selectPageListCountWithStatus(queryWrapper, HandingSheetStateEnum.EXPIRED.getValue());
        HandingSheetStatusCountVo vo = new HandingSheetStatusCountVo();
        vo.setToBeSubmitCount(toBeSubmitCount);
        vo.setToBeAuditedCount(toBeAuditedCount);
        vo.setAuditedCount(auditedCount);
        vo.setProcessingCount(processingCount);
        vo.setExpiredCount(expiredCount);
        vo.setTotalCount(totalCount);
        return SingleResponse.of(vo);
    }

    private List<List<String>> getExportData(HandingSheetPageQuery queryPage) {
        PageResponse<HandingSheetPageVo> response = pageQuery(queryPage);
        List<HandingSheetPageVo> pageVos = response.getData();
        List<HandingSheetExportVo> exportVos = new ArrayList<>();

        // to exportVo
        for (HandingSheetPageVo pageVo : pageVos) {
            HandingSheetExportVo exportVo = new HandingSheetExportVo();
            exportVo.setName(pageVo.getName());
            List<Integer> platformVals = pageVo.getPlatformVals();
            if (CollectionUtil.isEmpty(platformVals)) {
                exportVo.setPlatforms("");
            } else {
                StringBuilder sb = new StringBuilder();
                for (Integer platformVal : platformVals) {
                    Platform platform = Platform.of(platformVal);
                    if (platform != null) {
                        String desc = platform.getDesc();
                        // 展示为「小程序」
                        sb.append(Objects.equals(desc, "老爸商城") ? "小程序" : desc).append(",");
                    }
                }
                exportVo.setPlatforms(sb.length() > 0 ? sb.substring(0, sb.length() - 1) : "");
            }
            Long startTime = pageVo.getStartTime();
            Long endTime = pageVo.getEndTime();
            if (startTime != null && startTime > 0
                    && endTime != null && endTime > 0) {
                exportVo.setTime(DateUtil.format(startTime) + "~" + DateUtil.format(endTime));
            } else {
                exportVo.setTime("");
            }
            List<String> activityEvents = pageVo.getActivityEvents();
            if (CollectionUtil.isEmpty(activityEvents)) {
                exportVo.setActivityEvents("");
            } else {
                StringBuilder sb = new StringBuilder();
                for (String activityEvent : activityEvents) {
                    sb.append(activityEvent).append("\n");
                }
                exportVo.setActivityEvents(sb.toString());
            }
            exportVo.setLabel(pageVo.getLabel());
            exportVo.setItemNum(pageVo.getItemNum());
            exportVo.setCreator(pageVo.getCreator());
            Long createAt = pageVo.getCreateAt();
            exportVo.setCreatedTime(DateUtil.format(createAt));
            exportVos.add(exportVo);
        }

        final List<String> hiddenFields = getHiddenFields();
        final List<List<String>> data = new ArrayList<>();
        final Field[] fields = ReflectUtil.getFields(HandingSheetExportVo.class);
        for (HandingSheetExportVo exportVo : exportVos) {
            final ArrayList<String> row = new ArrayList<>();
            for (Field field : fields) {
                final String name = field.getName();
                if ((field.getModifiers() & Modifier.STATIC) > 0) {
                    continue;
                }
                final PropertyAlias propertyAlias = field.getAnnotation(PropertyAlias.class);
                if (hiddenFields.contains(name) || propertyAlias != null && hiddenFields
                        .contains(propertyAlias.value())) {
                    continue;
                }
                final Object fieldValue = BeanUtil.getFieldValue(exportVo, name);
                row.add(fieldValue != null ? fieldValue.toString() : "");
            }
            data.add(row);
        }
        return data;
    }

    private List<List<String>> exportHead() {
        final Field[] fields = ReflectUtil.getFields(HandingSheetExportVo.class);
        final ImmutableList.Builder<List<String>> headBuilder = ImmutableList.builder();
        final List<String> hiddenFields = getHiddenFields();

        for (Field field : fields) {
            final String name = field.getName();
            final PropertyAlias propertyAlias = field.getAnnotation(PropertyAlias.class);
            if (hiddenFields.contains(name) || propertyAlias != null && hiddenFields
                    .contains(propertyAlias.value())) {
                continue;
            }
            final ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
            if (annotation == null) {
                continue;
            }
            headBuilder.add(ImmutableList.of(annotation.value()[0]));
        }
        return headBuilder.build();
    }

    @NonNull
    private List<String> getHiddenFields() {
        List<Fold> folds = foldService.getFoldByType(1);
        List<String> hiddenFields = Lists.newArrayList();

        if (CollUtil.isNotEmpty(folds)) {
            hiddenFields = folds.stream().filter(fold -> Objects.equals(fold.getStatus(), 1))
                    .map(Fold::getField).collect(Collectors.toList());
        }
        return hiddenFields;
    }

    private List<HandingSheetActivityTextPageVo> toActivityTextPageVo(IPage<HandingSheetActivityText> activityTextPage) {
        List<HandingSheetActivityText> activityTexts = activityTextPage.getRecords();

        List<HandingSheetActivityTextPageVo> vos = new ArrayList<>();
        for (HandingSheetActivityText activityText : activityTexts) {
            HandingSheetActivityTextPageVo vo = new HandingSheetActivityTextPageVo();
            vo.setContent(activityText.getContent());
            vo.setHandingSheetActivityTextId(activityText.getId());
            Long createdAt = activityText.getCreatedAt();
            Long updatedAt = activityText.getUpdatedAt();
            if (updatedAt != null && updatedAt > 0) {
                vo.setLastEditTime(updatedAt);
            } else {
                vo.setLastEditTime(createdAt);
            }
            vo.setActivityName(activityText.getActivityName());
            vo.setCreatorUid(activityText.getCreatedUid());

            Long createdUid = activityText.getCreatedUid();
            Long updatedUid = activityText.getUpdatedUid();
            Long userId;
            if (updatedUid != null && updatedUid > 0) {
                userId = updatedUid;
            } else {
                userId = createdUid;
            }
            DadStaffVO staff = staffService.getStaff(userId);
            vo.setLastEditorNickName(Optional.ofNullable(staff).map(DadStaffVO::getNickname).orElse(""));
            vo.setLastEditorQwId(Optional.ofNullable(staff).map(DadStaffVO::getQwUserId).orElse(""));
            vos.add(vo);
        }
        return vos;
    }

    private QueryWrapper<HandingSheetActivityText> getActivityTextQueryWrapper(HandingSheetActivityTextPageQuery pageQuery) {
        Long handingSheetId = pageQuery.getHandingSheetId();
        if (handingSheetId == null || handingSheetId <= 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "盘货表ID必传");
        }

        QueryWrapper<HandingSheetActivityText> queryWrapper = new QueryWrapper();
        queryWrapper.lambda()
                .eq(HandingSheetActivityText::getIsDel, 0)
                .eq(HandingSheetActivityText::getHandingSheetId, handingSheetId);
        return queryWrapper;
    }

    private List<HandingSheetItemVo> toSheetItemVos(IPage<HandingSheetItem> sheetItemPage, HandingSheet handingSheet) {
        if (sheetItemPage == null) {
            return new ArrayList<>();
        }
        List<HandingSheetActivityEvent> sheetActivityEvents = handingSheetActivityEventService.listByHandingSheetId(handingSheet.getId());
        List<HandingSheetItem> sheetItems = sheetItemPage.getRecords();
        List<Long> itemIds = sheetItems.stream()
                .map(HandingSheetItem::getItemId)
                .distinct()
                .collect(Collectors.toList());
        List<Long> itemSkuIds = sheetItems.stream()
                .map(HandingSheetItem::getItemSkuId)
                .distinct()
                .collect(Collectors.toList());
        List<String> skuCodes = sheetItems.stream()
                .map(HandingSheetItem::getItemSkuNo)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, String> standardNameMap = itemDrawerService.getStandardNamesByItemIds(itemIds);
        Map<Long, ItemSku> skuMap = itemSkuService.getSkuMap(itemSkuIds);
        Map<String, SkuWithNewGoodsDto> skuWithNewGoodsDtoMap = newGoodsService.skuCodeToDtoMap(skuCodes);

        List<HandingSheetItemVo> vos = new ArrayList<>();
        for (HandingSheetItem sheetItem : sheetItems) {
            HandingSheetItemVo vo = new HandingSheetItemVo();
            vo.setHandingSheetItemId(sheetItem.getId());
            vo.setItemNo(sheetItem.getItemNo());
            vo.setItemSkuNo(sheetItem.getItemSkuNo());
            vo.setItemId(sheetItem.getItemId());
            vo.setItemSkuId(sheetItem.getItemSkuId());

            String s = standardNameMap.get(sheetItem.getItemId());
            vo.setItemStandardName(s == null ? "" : s);
            ItemSku itemSku = skuMap.get(sheetItem.getItemSkuId());
            vo.setItemSkuName(Optional.ofNullable(itemSku).map(ItemSku::getSpecifications).orElse(""));

            SkuWithNewGoodsDto skuWithNewGoodsDto = skuWithNewGoodsDtoMap.get(sheetItem.getItemSkuNo());
            vo.setDailyPrice(Optional.ofNullable(skuWithNewGoodsDto).map(SkuWithNewGoodsDto::getDailyPrice).orElse(BigDecimal.ZERO));
            vo.setActivePrice(sheetItem.getActivePrice());
            BigDecimal costPrice = Optional.ofNullable(itemSku).map(ItemSku::getCostPrice).orElse(BigDecimal.ZERO);
            vo.setCostPrice(costPrice);
            // 到手价
            BigDecimal arrivalPrice = handingSheetItemService
                    .calcArrivalPrice(sheetItem.getActivePrice(), sheetActivityEvents, handingSheet.getActivityType());
            vo.setArrivalPrice(arrivalPrice);
            // 毛利率 = （最低到手价-采购成本）/ 最低到手价
            BigDecimal grossProfitMargin = getGrossProfiMargin(costPrice, arrivalPrice);
            vo.setGrossProfitMargin(grossProfitMargin);

            vo.setGiftDescription(sheetItem.getGiftDescription());
            vo.setGiftCode(sheetItem.getGiftCode());
            vo.setTaobaoLink(sheetItem.getTaobaoLink());
            vo.setDouyinLink(sheetItem.getDouyinLink());
            vo.setMiniProgramLink(sheetItem.getMiniProgramLink());
            vo.setRemark(sheetItem.getRemark());
            vos.add(vo);
        }
        return vos;
    }

    @Nullable
    private BigDecimal getGrossProfiMargin(BigDecimal costPrice, BigDecimal arrivalPrice) {
        BigDecimal grossProfitMargin;
        if (arrivalPrice.compareTo(BigDecimal.ZERO) <= 0) {
            grossProfitMargin = null;
        } else {
            BigDecimal subtract = arrivalPrice.subtract(costPrice);
            if (subtract.compareTo(BigDecimal.ZERO) <= 0) {
                grossProfitMargin = BigDecimal.ZERO;
            } else {
                grossProfitMargin = subtract.divide(arrivalPrice, 2, RoundingMode.DOWN);
            }
        }
        return grossProfitMargin;
    }

    private QueryWrapper<HandingSheetItem> getSheetItemQueryWrapper(HandingSheetItemPageQuery pageQuery) {
        QueryWrapper<HandingSheetItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(HandingSheetItem::getIsDel, 0)
                .eq(HandingSheetItem::getHandingSheetId, pageQuery.getHandingSheetId());
        return queryWrapper;
    }

    private HandingSheetBaseVo toHandingSheetBaseVo(HandingSheet handingSheet) {
        HandingSheetBaseVo vo = new HandingSheetBaseVo();
        vo.setId(handingSheet.getId());
        vo.setName(handingSheet.getSheetName());
        vo.setStartTime(handingSheet.getStartTime());
        vo.setEndTime(handingSheet.getEndTime());
        String platform = handingSheet.getPlatform();
        if (StringUtils.hasText(platform)) {
            vo.setPlatformVals(Arrays.stream(platform.split(","))
                    .map(Integer::valueOf)
                    .collect(Collectors.toList()));
        }
        vo.setLabel(handingSheet.getLabel());
        List<HandingSheetStaff> sheetStaffs = handingSheetStaffService.listBySheetId(handingSheet.getId());
        List<Long> staffUids = sheetStaffs.stream()
                .map(HandingSheetStaff::getUserId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(staffUids)) {
            vo.setStaffs(staffService.getStaffList(staffUids));
        }
        vo.setAttachments(handingSheet.getAttachment());
        vo.setActivityType(handingSheet.getActivityType());
        List<HandingSheetActivityEvent> sheetActivityEvents = handingSheetActivityEventService
                .listByHandingSheetId(handingSheet.getId());
        if (CollectionUtil.isNotEmpty(sheetActivityEvents)) {
            List<HandingSheetActivityEventVo> activityEventVos = new ArrayList<>();
            for (HandingSheetActivityEvent sheetActivityEvent : sheetActivityEvents) {
                HandingSheetActivityEventVo activityEventVo = new HandingSheetActivityEventVo();
                activityEventVo.setReachedAmount(sheetActivityEvent.getReachedAmount());
                activityEventVo.setReducedAmount(sheetActivityEvent.getReducedAmount());
                activityEventVos.add(activityEventVo);
            }
            vo.setActivityEvents(activityEventVos);
        }
        vo.setState(handingSheet.getState());
        return vo;
    }

    private void checkForEdit(HandingSheetParam param) {
        baseCheck(param);
        Long handingSheetId = param.getId();
        Boolean audit = param.getAudit();
        if (handingSheetId == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "编辑盘货表，主键ID不可缺失");
        }
        HandingSheet handingSheet = getById(handingSheetId);
        if (handingSheet == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "找不到盘货表");
        }
//        if (Objects.equals(handingSheet.getState(), HandingSheetStateEnum.EXPIRED.getValue())) {
//            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "盘货表已过期，不可编辑");
//        }
    }

    private void checkForAdd(HandingSheetParam param) {
        baseCheck(param);
    }

    private void baseCheck(HandingSheetParam param) {
        String name = param.getName();
        Long startTime = param.getStartTime();
        Long endTime = param.getEndTime();
        String label = param.getLabel();
        Integer activityType = param.getActivityType();
        HandingSheetActivityTypeEnum activityTypeEnum = HandingSheetActivityTypeEnum.of(activityType);
        if (activityTypeEnum == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "活动类型不合法");
        }
        List<Integer> platformVals = param.getPlatformVals();
        for (Integer platformVal : platformVals) {
            Platform platform = Platform.of(platformVal);
            if (platform == null) {
                throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "所属平台有不合法的值");
            }
        }

        int nameLengthLimit = 50;
        if (name.length() > nameLengthLimit) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "名称字符数超过限制（最多" + nameLengthLimit + "个）");
        }
        if (startTime >= endTime) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "开始时间必须小于结束时间");
        }
        int labelLengthLimit = 50;
        if (StringUtils.hasText(label) && label.length() > labelLengthLimit) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "标签字符数超过限制（最多" + labelLengthLimit + "个）");
        }

        // 活动力度校验
        List<HandingSheetActivityEventParam> activityEvents = param.getActivityEvents();
        for (HandingSheetActivityEventParam activityEvent : activityEvents) {
            BigDecimal reachedAmount = activityEvent.getReachedAmount();
            BigDecimal reducedAmount = activityEvent.getReducedAmount();
            if (reachedAmount == null
                    || reachedAmount.compareTo(BigDecimal.ZERO) <= 0
                    || reducedAmount == null
                    || reducedAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "活动力度金额数据不合法");
            }
            if (reachedAmount.compareTo(reducedAmount) <= 0) {
                throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "活动力度金额数据不合法");
            }
        }

        // 商品信息校验
        List<HandingSheetItemParam> itemInfos = param.getItemInfos();
        if (!param.isUseNewVersionView()) {
            for (HandingSheetItemParam itemInfo : itemInfos) {
                String itemNo = itemInfo.getItemNo();
                if (!StringUtils.hasText(itemNo)) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "商品编码必填");
                }
                Long itemId = itemInfo.getItemId();
                if (itemId == null) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "商品ID缺失");
                }
                String itemSkuNo = itemInfo.getItemSkuNo();
                if (!StringUtils.hasText(itemSkuNo)) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "规格编码必填");
                }
                Long itemSkuId = itemInfo.getItemSkuId();
                if (itemSkuId == null) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "商品规格ID缺失");
                }
                String giftDescription = itemInfo.getGiftDescription();
                if (StringUtils.hasText(giftDescription) && giftDescription.length() > giftDescriptionLengthLimit) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "赠品机制最多" + giftDescriptionLengthLimit + "字符");
                }
                String remark = itemInfo.getRemark();
                if (StringUtils.hasText(remark) && remark.length() > remarkLengthLimit) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "商品备注最多" + remarkLengthLimit + "字符");
                }
                BigDecimal activePrice = itemInfo.getActivePrice();
                if (activePrice == null || activePrice.compareTo(BigDecimal.ZERO) <= 0) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "活动价必须大于0");
                }
            }
        }

    }

    private List<HandingSheetPageVo> handingSheetPageToVo(IPage<HandingSheet> handingSheetPage) {
        List<HandingSheet> records = handingSheetPage.getRecords();

        List<HandingSheetPageVo> vos = new ArrayList<>();
        for (HandingSheet record : records) {
            Long handingSheetId = record.getId();
            Integer activityType = record.getActivityType();

            HandingSheetPageVo vo = new HandingSheetPageVo();
            vo.setId(handingSheetId);
            vo.setName(record.getSheetName());
            vo.setState(record.getState());
            String platform = record.getPlatform();
            if (StringUtils.hasText(platform)) {
                List<Integer> platformVals = Arrays.stream(platform.split(","))
                        .map(Integer::valueOf)
                        .collect(Collectors.toList());
                vo.setPlatformVals(platformVals);
            }
            vo.setStartTime(record.getStartTime());
            vo.setEndTime(record.getEndTime());
            vo.setLabel(record.getLabel());
            vo.setCreateAt(record.getCreatedAt());
            Long createdUid = record.getCreatedUid();
            if (createdUid != null && createdUid > 0) {
                StaffInfo staffInfo = userGateway.queryStaffInfoById(createdUid);
                vo.setCreator(Optional.ofNullable(staffInfo).map(StaffInfo::getNickname).orElse(""));
            }
            List<HandingSheetActivityEvent> sheetActivityEvents = handingSheetActivityEventService
                    .listByHandingSheetId(handingSheetId);
            List<String> activityEvents = convert(sheetActivityEvents, activityType);
            vo.setActivityEvents(activityEvents);
            vo.setItemNum((long) handingSheetItemSkuService.countItemSpuByHandingSheetId(handingSheetId));
            vos.add(vo);
        }
        return vos;
    }

    private List<String> convert(List<HandingSheetActivityEvent> sheetActivityEvents, Integer activityType) {
        if (CollectionUtil.isEmpty(sheetActivityEvents)) {
            return new ArrayList<>();
        }
        List<String> list = new ArrayList<>();
        for (HandingSheetActivityEvent sheetActivityEvent : sheetActivityEvents) {
            BigDecimal reachedAmount = sheetActivityEvent.getReachedAmount();
            if (reachedAmount == null || reachedAmount.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            BigDecimal reducedAmount = sheetActivityEvent.getReducedAmount();
            if (reducedAmount == null || reducedAmount.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            String des;
            if (Objects.equals(activityType, HandingSheetActivityTypeEnum.LOOP_FULL_REDUCTION.getValue())) {
                des = "每满" + reachedAmount + "-" + reducedAmount;
            } else {
                des = "满" + reachedAmount + "-" + reducedAmount;
            }
            list.add(des);
        }
        return list;
    }

    private QueryWrapper<HandingSheet> getQueryWrapper(HandingSheetPageQuery pageQuery) {
        String name = pageQuery.getName();
        Integer platformVal = pageQuery.getPlatformVal();
        Long startTime = pageQuery.getStartTime();
        Long endTime = pageQuery.getEndTime();
        Long creatorUid = pageQuery.getCreatorUid();
        Integer state = pageQuery.getState();
        final Long sheetId = pageQuery.getSheetId();

        QueryWrapper<HandingSheet> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(HandingSheet::getIsDel, 0)
                .like(StringUtils.hasText(name), HandingSheet::getSheetName, name)
                .like(Objects.nonNull(platformVal), HandingSheet::getPlatform, platformVal)
                .eq(Objects.nonNull(creatorUid), HandingSheet::getCreatedUid, creatorUid)
                .eq(Objects.nonNull(state), HandingSheet::getState, state)
                .eq(Objects.nonNull(sheetId), HandingSheet::getId, sheetId);

        boolean timeQuery = true;
        if (startTime == null || endTime == null) {
            timeQuery = false;
        }
        if (timeQuery) {
            // ((start_time >= ? AND start_time <= ?) OR (end_time >= ? AND end_time <= ?))
            queryWrapper.lambda().and(w1 -> {
                w1.or(w2 -> {
                    w2.ge(HandingSheet::getStartTime, startTime).le(HandingSheet::getStartTime, endTime);
                });
                w1.or(w2 -> {
                    w2.ge(HandingSheet::getEndTime, startTime).le(HandingSheet::getEndTime, endTime);
                });
            });
        }
        return queryWrapper;
    }

    private String listToString(List<?> list) {
        if (CollectionUtil.isEmpty(list)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        list.forEach(type -> {
            sb.append(type).append(",");
        });
        // 截除最后一个逗号
        return sb.length() > 0 ? sb.substring(0, sb.length() - 1) : "";
    }
}
