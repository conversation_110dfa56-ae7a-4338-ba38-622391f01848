package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NuoNuoInvoiceRequest;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;

/**
 * <p>
 * 用户诺诺开票请求参数 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
public interface NuoNuoInvoiceRequestMapper extends DaddyBaseMapper<NuoNuoInvoiceRequest> {

}
