package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTrainingMaterialsProcess;

/**
 * <p>
 * 新品商品培训资料处理流程 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
public interface IItemTrainingMaterialsProcessService extends IDaddyService<ItemTrainingMaterialsProcess> {

}
