package com.daddylab.supplier.item.controller.test.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2024年11月22日 2:12 PM
 */
@Data
public class StockExcelDto {

    @ExcelProperty("供应商")
    private String providerName;

    @ExcelProperty("仓库")
    private String warehouseName;

    @ExcelProperty("物料编码")
    private String skuCode;

    @ExcelProperty("税率")
    private String taxRate;

    @ExcelProperty("数量")
    private String num;

    @ExcelProperty("含税单价")
    private String priceWithTax;

    @ExcelProperty("金额")
    private String totalAmount;

    @ExcelProperty("仓库编码")
    private String warehouseNo;

    @ExcelProperty("供应商编码")
    private String providerNo;
}
