package com.daddylab.supplier.item.application.saleItem.service;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.saleItem.dto.DouDianSyncCmd;
import com.daddylab.supplier.item.application.saleItem.dto.NewGoodsCmd;
import com.daddylab.supplier.item.application.saleItem.dto.SkuCopyCmd;
import com.daddylab.supplier.item.application.saleItem.query.NewGoodsQueryPage;
import com.daddylab.supplier.item.application.saleItem.service.impl.GoodsImages;
import com.daddylab.supplier.item.application.saleItem.vo.LockResult;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsDownRequest;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsGroupVO;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsPrincipalsInfo;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsToBeReleasedRequest;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsVo;
import com.daddylab.supplier.item.domain.item.data.ItemLaunchPlanItemRefInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchPlan;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.types.BatchResult;
import org.apache.ibatis.annotations.Param;

import java.io.InputStream;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName NewGoodsBizService.java
 * @description
 * @createTime 2022年04月18日 16:31:00
 */
public interface NewGoodsBizService {

    SingleResponse<LockResult> lock(Long id);

    Response unlock(Long id);

    int count(NewGoodsQueryPage queryPage);

    List<GoodsImages> queryImages(NewGoodsQueryPage queryPage);

    NewGoodsVo getNewGoodsVoById(Long newGoodsId);

    PageResponse<NewGoodsGroupVO> queryPageGroupBySpu(NewGoodsQueryPage queryPage);

    /**
     * 修改新品商品
     * @param cmd
     * @return
     */
    /**
     * 修改新品商品
     *
     * @param cmd
     * @return
     */
    Response update(NewGoodsCmd cmd);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Response delete(Long id);

    /**
     * 导出
     *
     * @param newGoodsQueryPage
     * @return
     */
    void exportExcel(NewGoodsQueryPage newGoodsQueryPage);

    long exportImages(NewGoodsQueryPage newGoodsQueryPage);

    /**
     * 根据skuCode查询新品库的商品
     *
     * @param code
     * @return
     */
    NewGoods getBySkuCode(String code);

    /**
     * 上新计划同步新品商品库
     *
     * @param refInfos 关联信息
     */
    BatchResult<NewGoods> itemSync(List<ItemLaunchPlanItemRefInfo> refInfos, ItemLaunchPlan plan);

    /**
     * 根据itemId查询新品商品库
     *
     * @param itemId
     */
    List<NewGoods> listById(Long itemId);

//    /**
//     * 商品抽屉同步产品标准名到新品库
//     *
//     * @param itemId
//     * @param standardName
//     * @return
//     */
//    Boolean itemDrawerSyncStandardName(Long itemId, String standardName);

    /**
     * 根据itemId获取新品库
     *
     * @param itemId
     * @return
     */
    List<NewGoodsVo> getNewGoodsVO(Long itemId);

    /**
     * 获取新品数据
     *
     * @param itemId
     * @return
     */
    NewGoods getNewGoods(Long itemId);

    /**
     * 同步产品负责人
     *
     * @param itemId
     * @param principalId
     * @return
     */
    Boolean syncPrincipal(Long itemId, Long principalId);

    /**
     * 同步产品负责人
     *
     * @param itemId
     * @param qcIds
     * @return
     */
    Boolean syncQc(Long itemId, String qcIds);

    List<OperateLog> getOperateLogs(Long targetId);

    List<OperateLog> getPriceLog(Long targetId);

    SingleResponse<Boolean> copySku(SkuCopyCmd cmd);

    /**
     * 导入新品商品数据
     *
     * @param inputStream 文件上传流
     * @return
     */
    Response importExcel(@Param("inputStream") InputStream inputStream);

    void douDianSync(List<DouDianSyncCmd> list);

    /**
     * 获取新品商品负责人信息
     *
     * @param itemId 后端商品ID
     * @return NewGoodsPrincipalsInfo
     */
    SingleResponse<NewGoodsPrincipalsInfo> getNewGoodsPrincipalsInfo(Long itemId);

    MultiResponse<NewGoodsPrincipalsInfo> getNewGoodsPrincipalsInfoBatch(Collection<Long> itemIds);


    void updateById(NewGoods newGoods);


    Map<Integer,Integer> staticsItemCountByTimeAndStatus(Long startTime, Long endTime, List<ItemLaunchStatus> statusList);

    /**
     * 下架商品
     *
     * @param request
     * @return
     */
    Response down(NewGoodsDownRequest request);
}
