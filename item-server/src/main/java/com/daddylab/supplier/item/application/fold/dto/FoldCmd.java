package com.daddylab.supplier.item.application.fold.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @ClassName FoldCmd.java
 * @description
 * @createTime 2022年06月07日 16:33:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("折叠新增实体")
public class FoldCmd extends Command {

    private static final long serialVersionUID = 3207436191114075526L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "类型 1:新品商品")
    @NotNull(message = "类型不得为空")
    private Integer type;

    @ApiModelProperty(value = "列名")
    @NotBlank(message = "列名不得为空")
    private String field;

    @ApiModelProperty(value = "是否折叠 0:否 1:是")
    @NotNull(message = "是否折叠不得为空")
    private Integer status;

}
