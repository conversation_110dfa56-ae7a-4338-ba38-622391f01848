package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单结算-工厂，列表页面结算状态枚举
 *
 * <AUTHOR> up
 */
@Getter
@AllArgsConstructor
public enum OrderSettlementStatus implements IEnum<Integer> {
    /**
     * 消除波浪线
     */
    WAIT_CONFIRM(0, "待结算"),
    CONFIRMED(1, "已结算"),
    IN_AUDIT(2, "审核中"),
    AUDIT_REJECT(3, "审核拒绝"),
    FINISHED(4, "已完成"),
    FAIL(-9, "异常"),

    LOCKING(10,"数据锁定")

    ;



    @EnumValue
    private final Integer value;
    private final String desc;


}
