package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.SaleItemLibrary;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.SaleItemLibraryMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ISaleItemLibraryService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 销售商品库 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Service
public class SaleItemLibraryServiceImpl extends DaddyServiceImpl<SaleItemLibraryMapper, SaleItemLibrary> implements ISaleItemLibraryService {
}
