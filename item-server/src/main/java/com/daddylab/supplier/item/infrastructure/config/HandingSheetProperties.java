package com.daddylab.supplier.item.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/30 13:41
 * @Description: 盘货表相关配置
 */
@Component
@Data
@ConfigurationProperties(prefix = "handing.sheet")
@RefreshScope
public class HandingSheetProperties {
    /**
     * 盘货表详情 URL
     */
    private String handingSheetDetailUrl;
    /**
     * 盘货物表详情活动 tab URL
     */
    private String handingSheetActivityTabUrl;
    /**
     * 是否容忍盘货表SKU编码重复
     */
    private boolean tolerateForDuplicateSku;
    /**
     * 检查导入数据生成的临时ID是否重复
     */
    private boolean reportImportDuplicate;
    /**
     * 导入数据后是否直接保存
     */
    private boolean directSaveAfterImport;
}
