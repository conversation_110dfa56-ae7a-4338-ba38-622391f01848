package com.daddylab.supplier.item.controller.framework;

import com.daddylab.supplier.item.infrastructure.spring.converters.StringIEnumConverterFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@Slf4j
public class ConverterConfig implements WebMvcConfigurer {

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverterFactory(new StringIEnumConverterFactory());
    }

    //@Override
    //public void configurePathMatch(PathMatchConfigurer configurer) {
    //    //请求URI忽略大小写
    //    final AntPathMatcher antPathMatcher = new AntPathMatcher();
    //    antPathMatcher.setCaseSensitive(false);
    //    configurer.setPathMatcher(antPathMatcher);
    //}
}
