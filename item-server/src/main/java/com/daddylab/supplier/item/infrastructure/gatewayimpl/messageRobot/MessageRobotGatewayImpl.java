package com.daddylab.supplier.item.infrastructure.gatewayimpl.messageRobot;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.daddylab.supplier.item.domain.messageRobot.enums.Error;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.messageRobot.exceptions.MessageRobotException;
import com.daddylab.supplier.item.domain.messageRobot.gateway.MessageRobotGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.MessageRobot;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.MessageRobotMapper;
import com.daddylab.supplier.item.infrastructure.weixin.qyapi.RobotAPI;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-04
 */
@Service
public class MessageRobotGatewayImpl implements MessageRobotGateway {
    @Autowired
    private MessageRobotMapper messageRobotMapper;
    @Autowired
    private RobotAPI robotAPI;

    @Override
    public boolean sendText(MessageRobotCode code, String content) throws MessageRobotException {
        MessageRobot messageRobot = getRobotByCode(code);
        if (messageRobot == null) return false;
        sendText0(content, messageRobot);
        return true;
    }

    @Override
    public boolean sendText(String code, String content) throws MessageRobotException {
        MessageRobot messageRobot = getRobotByCode(code);
        if (messageRobot == null) return false;
        sendText0(content, messageRobot);
        return false;
    }

    private void sendText0(String content, MessageRobot messageRobot) throws MessageRobotException {
        final RobotAPI.Result send = robotAPI.send(
                messageRobot.getWebhook(),
                RobotAPI.TextMessage.ofContent(content)
        );
        checkError(send);
    }

    @Override
    public boolean sendMarkdown(String code, String markdown) throws MessageRobotException {
        MessageRobot messageRobot = getRobotByCode(code);
        if (messageRobot == null) return false;
        sendMarkdown0(markdown, messageRobot);
        return true;
    }
    
    @Override
    public boolean sendMarkdownWebhook(String webhook, String markdown) throws MessageRobotException {
        final RobotAPI.Result send = robotAPI.send(
                webhook,
                RobotAPI.MarkdownMessage.ofMarkdown(markdown)
        );
        checkError(send);
        return true;
    }
    
    @Override
    public boolean sendMarkdown(MessageRobotCode code, String markdown) throws MessageRobotException {
        MessageRobot messageRobot = getRobotByCode(code);
        if (messageRobot == null) return false;
        sendMarkdown0(markdown, messageRobot);
        return true;
    }

    private void sendMarkdown0(String markdown, MessageRobot messageRobot) throws MessageRobotException {
        final RobotAPI.Result send = robotAPI.send(
                messageRobot.getWebhook(),
                RobotAPI.MarkdownMessage.ofMarkdown(markdown)
        );
        checkError(send);
    }

    public MessageRobot getRobotByCode(Object code) {
        final LambdaQueryWrapper<MessageRobot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageRobot::getCode, code);
        return messageRobotMapper.selectOne(queryWrapper);
    }

    private void checkError(RobotAPI.Result send) throws MessageRobotException {
        if (!Objects.equals(send.getErrcode(), 0)) {
            if (send.getErrcode() == 45009) {//限制调用频率
                throw new MessageRobotException(Error.OUT_OF_FREQ_LIMIT);
            }
            if (send.getErrcode() == 45033) {//限制并发
                throw new MessageRobotException(Error.OUT_OF_CONCURRENT_LIMIT);
            }
            throw new MessageRobotException("消息机器人发送失败，Errcode:" + send.getErrcode() + " Errmsg:" + send.getErrcode());
        }
    }
}
