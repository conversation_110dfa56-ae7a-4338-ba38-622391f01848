package com.daddylab.supplier.item.controller.common.job;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWarehouseService;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
import com.daddylab.supplier.item.infrastructure.timing.StopWatch;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022/5/30 7:40 下午
 */
@Service
@Slf4j
public class SyncWarehouseJob {

    @Autowired
    IWarehouseService warehouseService;

    @Autowired
    ReqTemplate reqTemplate;

    /**
     * 间隔5分钟执行。
     */
//    @XxlJob("syncWarehouseJob")
    public void syncWarehouseJob() {

        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            XxlJobHelper.log("仓库数据同步到金蝶操作开始");

            int size = 100;
            int index = 0;

            PageInfo<Warehouse> pageInfo = PageHelper.startPage(index, size).doSelectPageInfo(() -> warehouseService.lambdaQuery()
                    .eq(Warehouse::getIsDel, 0).list());

            XxlJobHelper.log("仓库数据同步到金蝶操作。需要同步的数据总量:{}", pageInfo.getTotal());

            int pageNum = pageInfo.getPages();

            while (pageNum > 0) {
                List<String> errorNoList = new LinkedList<>();

                for (Warehouse record : pageInfo.getList()) {
                    try {
                        Boolean isExist = reqTemplate.warehouseExist(record.getNo());

                        if (!isExist) {
                            reqTemplate.saveWarehouse(record.getName(), record.getNo());
                        }

                    } catch (Exception e) {
                        log.error("仓库同步到KingDee异常,id:{},error:{}", record.getId(), e.getMessage(), e);
                        errorNoList.add(record.getNo());
                    }
                }

                if (CollectionUtils.isNotEmpty(errorNoList)) {
                    String join = CharSequenceUtil.join(",", errorNoList);
                    Alert.text(MessageRobotCode.GLOBAL, "warehouse同步KingDee异常，no:" + join);
                    XxlJobHelper.log("warehouse同步KingDee异常。{}", join);
                }

                pageNum = pageNum - 1;

                pageInfo = PageHelper.startPage(index + 1, size).doSelectPageInfo(() -> warehouseService.lambdaQuery()
                        .eq(Warehouse::getIsDel, 0).eq(Warehouse::getState, 1).list());
            }

            stopWatch.stop();
            XxlJobHelper.log("仓库数据同步到金蝶操作结束。耗时:{}", stopWatch.getTotalTimeSeconds());
        });
    }

}
