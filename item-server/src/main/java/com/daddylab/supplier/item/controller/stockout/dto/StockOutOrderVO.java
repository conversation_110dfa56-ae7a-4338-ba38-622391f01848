package com.daddylab.supplier.item.controller.stockout.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockOutOrderState;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/25 15:44
 * @description StockOutOrderVO
 */
@Data
@ApiModel("出库详情")
public class StockOutOrderVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "出库单号")
    private String no;

    @ApiModelProperty(value = "供应商名称")
    private String providerName;

    @ApiModelProperty(value = "供应商ID")
    private Long providerId;

    @ApiModelProperty(value = "P系统供应商ID")
    private Long partnerProviderId;


    @ApiModelProperty("是否黑名单 0否 1是")
    private Integer isBlacklist;

    @ApiModelProperty(value = "交货(出库)总数量")
    private Integer totalReturnQuantity;

    @ApiModelProperty(value = "退料类型(1:库存退料)")
    private Integer returnType;

    @ApiModelProperty(value = "退料方式(1.退料补料。2.退料并扣款)")
    private Integer returnMode;

    @ApiModelProperty(value = "创建人")
    private String createdName;

    @ApiModelProperty(value = "创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "状态(1待提交、2待审核、3待出库、4已出库、5已取消、6审核拒绝、7撤回审核)")
    private Integer state;

    @ApiModelProperty(value = "出库明细")
    private List<StockOutOrderDetailVO> stockOutOrderDetailList;

    // 创建人id
    private Long createdUid;

    private Integer businessLine;

    /**
     * 冲销状态
     */
    @ApiModelProperty(value = "操作类型 DEFAULT:默认，REVERSE_FIXED：逆向对冲单据，WRITE_OFF：原单据作废.FIX_ORDER:冲销流程,原单据修正单据")
    private StockOutOrderState hedgeStatus;
}
