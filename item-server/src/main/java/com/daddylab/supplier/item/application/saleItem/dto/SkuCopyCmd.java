package com.daddylab.supplier.item.application.saleItem.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022年08月02日 11:59 AM
 */
@Data
@ApiModel("新品商品，sku复制操作，请求参数")
public class SkuCopyCmd extends Command {

    private static final long serialVersionUID = 1878437034959529219L;

    @ApiModelProperty("数据源")
    @NotNull(message = "数据源不得为空")
    private String sourceSkuCode;

    @ApiModelProperty("目标对象")
    @NotEmpty(message = "复制目标对象不得为空")
    private List<String> targetSkuCodeList;

}
