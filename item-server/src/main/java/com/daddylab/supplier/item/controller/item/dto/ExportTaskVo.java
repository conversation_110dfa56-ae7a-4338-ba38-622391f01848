package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/25 5:50 下午
 * @description
 */
@Data
@ApiModel("导出任务列表展示返回")
public class ExportTaskVo implements Serializable {


    private static final long serialVersionUID = -263608801164465268L;

    @ApiModelProperty("任务id")
    private Long id;

    @ApiModelProperty("任务名称")
    private String name;

    @ApiModelProperty("任务状态")
    private String status;

    @ApiModelProperty("任务状态值")
    private String statusVal;

    @ApiModelProperty("可下载地址")
    private String downloadUrl;

    @ApiModelProperty("导出进度。非百分比")
    private BigDecimal progress;

}
