//package com.daddylab.supplier.item.application.afterSales;
//
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesAbnormalInfo;
//import org.apache.ibatis.annotations.Mapper;
//import org.mapstruct.factory.Mappers;
//
///**
// * <AUTHOR> up
// * @date 2022年10月20日 9:34 AM
// */
//@Mapper
//public interface AfterSalesAbnormalInfoMapper {
//
//    AfterSalesAbnormalInfoMapper INSTANCE = Mappers.getMapper(AfterSalesAbnormalInfoMapper.class);
//
//    AfterSalesAbnormalInfo cmdToEntity(AfterSalesAbnormalCmd cmd);
//}
