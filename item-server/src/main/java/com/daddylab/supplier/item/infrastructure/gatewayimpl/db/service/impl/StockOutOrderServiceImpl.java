package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockOutOrderState;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockOutOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockOutOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockOutOrderService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 退料出库表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-24
 */
@Service
public class StockOutOrderServiceImpl extends DaddyServiceImpl<StockOutOrderMapper, StockOutOrder> implements IStockOutOrderService {

    @Override
    public void setKingDeeId(Long id, String kingDeeId) {
        LambdaUpdateWrapper<StockOutOrder> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(StockOutOrder::getKingDeeId, kingDeeId).eq(StockOutOrder::getId, id);
        this.update(updateWrapper);
    }

    @Override
    public void removeKingDeeId(Long id) {
        LambdaUpdateWrapper<StockOutOrder> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(StockOutOrder::getKingDeeId, null).eq(StockOutOrder::getId, id);
        this.update(updateWrapper);
    }

    @Override
    public Boolean related(Long purchaseOrderId) {
        return this.lambdaQuery().eq(StockOutOrder::getPurchaseOrderId, purchaseOrderId).count() > 0;
    }

    @Override
    public List<Long> relatedIdList(Long purchaseOrderId) {
        LambdaQueryWrapper<StockOutOrder> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StockOutOrder::getPurchaseOrderId, purchaseOrderId);
        List<StockOutOrder> list = this.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new LinkedList<>();
        }
        return list.stream().map(StockOutOrder::getId).collect(Collectors.toList());
    }

    @Override
    public void updateStatusByPurchaseOrderNos(Collection<String> purchaseOrderNos, StockOutOrderState state) {
        this.lambdaUpdate().set(StockOutOrder::getState, state).in(StockOutOrder::getPurchaseOrderNo, purchaseOrderNos);
    }

//    @Override
//    public void updateStatus(String no, StockOutOrderState status) {
//        this.lambdaUpdate().set(StockOutOrder::getState, status.getValue()).eq(StockOutOrder::getNo, no).update();
//    }
}
