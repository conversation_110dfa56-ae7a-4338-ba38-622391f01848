package com.daddylab.supplier.item.application.drawer.impl.copySupport;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.drawer.ItemDrawerCopyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerRecognitionTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerRecognitionTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @class ItemDrawerRecognitionTaskCopyService.java
 * @description 描述类的作用
 * @date 2024-04-11 10:00
 */
@Service
public class ItemDrawerRecognitionTaskCopyServiceImpl implements ItemDrawerCopyService {

    @Autowired
    private IItemDrawerRecognitionTaskService itemDrawerRecognitionTaskService;

    @Override
    public void handler(ItemDrawer sourceDrawer, ItemDrawer targetDrawer) {
        // 删除
        List<ItemDrawerRecognitionTask> recognitionTasksList = getRecognitionTasksList(targetDrawer.getItemId());
        if (!recognitionTasksList.isEmpty()) {
            List<Long> recognitionTaskIds = recognitionTasksList.stream().map(ItemDrawerRecognitionTask::getId).collect(Collectors.toList());
            itemDrawerRecognitionTaskService.removeByIds(recognitionTaskIds);
        }
        // 新增
        List<ItemDrawerRecognitionTask> sourceRecognitionTasksList = getRecognitionTasksList(sourceDrawer.getItemId());
        if (!sourceRecognitionTasksList.isEmpty()) {
            List<ItemDrawerRecognitionTask> recognitionTasks = sourceRecognitionTasksList.stream().peek(recognitionTask -> {
                recognitionTask.setItemId(targetDrawer.getItemId());
            }).collect(Collectors.toList());
            itemDrawerRecognitionTaskService.saveBatch(recognitionTasks);
        }
    }

    List<ItemDrawerRecognitionTask> getRecognitionTasksList(Long itemId) {
        LambdaQueryWrapper<ItemDrawerRecognitionTask> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ItemDrawerRecognitionTask::getItemId, itemId);
        return itemDrawerRecognitionTaskService.list(queryWrapper);
    }
}
