package com.daddylab.supplier.item.controller.category.dto;

import cn.hutool.core.collection.CollectionUtil;

import com.daddylab.supplier.item.infrastructure.tree.Tree;
import com.fasterxml.jackson.annotation.JsonGetter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.*;

@Data
@EqualsAndHashCode(exclude = {"parent"})
@ApiModel("品类树型结构节点")
public class CategoryTreeNode implements Serializable, Tree<Long, CategoryTreeNode> {


    private static final long serialVersionUID = -709460237505415468L;
    @ApiModelProperty(value = "品类id", example = "1")
    private Long id;

    @ApiModelProperty("品类名称")
    private String name;

    @ApiModelProperty(value = "此品类根品类id", example = "1")
    private Long rootId;

    @ApiModelProperty(value = "此品类父品类id", example = "1")
    private Long parentId;

    @ApiModelProperty(value = "此品类层级", example = "1")
    private Integer level;

    @ApiModelProperty(value = "是否是最后一级")
    public Boolean getLastLevel() {
        return CollectionUtil.isEmpty(children);
    }

    @ApiModelProperty(value = "品类路径")
    private String path;

    private List<CategoryTreeNode> children;

    @Override
    public List<CategoryTreeNode> getChildren() {
        if (children == null) {
            children = new ArrayList<>();
        }
        return children;
    }

    @Override
    public Integer getSort() {
        return id.intValue();
    }

    @Override
    public void setSort(Integer sort) {

    }

    private CategoryTreeNode parent;

    /** 商品拓展属性 */
    private List<String> props = Collections.emptyList();

    @JsonGetter
    public List<String> getAllProps() {
        final Set<String> props = new LinkedHashSet<>(this.props);
        CategoryTreeNode p = this;
        while (p.getParent() != null) {
            p = p.getParent();
            if (CollectionUtil.isNotEmpty(p.getProps())) {
                props.addAll(p.getProps());
            }
        }
        return new ArrayList<>(props);
    }
}
