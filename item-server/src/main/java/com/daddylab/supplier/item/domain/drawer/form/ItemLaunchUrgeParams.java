package com.daddylab.supplier.item.domain.drawer.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 商品上新催办参数
 */
@Data
@ApiModel(value = "ItemLaunchUrgeParams", description = "商品上新催办参数")
public class ItemLaunchUrgeParams implements Serializable {

    private static final long serialVersionUID = 5846932721722595609L;

    @NotNull(message = "商品ID不能为空")
    @ApiModelProperty(value = "商品ID")
    private Long itemId;

    @NotEmpty(message = "办理人不能为空")
    @ApiModelProperty(value = "办理人用户ID")
    private List<Long> recipients;


}
