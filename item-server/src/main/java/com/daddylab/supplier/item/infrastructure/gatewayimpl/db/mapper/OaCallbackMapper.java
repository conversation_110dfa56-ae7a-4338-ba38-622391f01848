package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OaCallback;

/**
 * <p>
 * OA回调数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public interface OaCallbackMapper extends DaddyBaseMapper<OaCallback> {

}
