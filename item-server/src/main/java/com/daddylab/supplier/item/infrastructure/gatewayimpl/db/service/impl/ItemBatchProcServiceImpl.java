package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemBatchProc;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemBatchProcMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemBatchProcService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.LogUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.itemBatchProc.ItemBatchProcStatus;
import com.daddylab.supplier.item.types.itemBatchProc.ItemBatchProcType;

import org.slf4j.event.Level;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;

/**
 * 商品批处理 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@Service
public class ItemBatchProcServiceImpl extends DaddyServiceImpl<ItemBatchProcMapper, ItemBatchProc>
        implements IItemBatchProcService {

    @Override
    public long createTask(
            Object cmd,
            ItemBatchProcType itemBatchProcType,
            @Nullable Runnable synchronizedCallback) {
        final ItemBatchProc itemBatchProc = new ItemBatchProc();
        itemBatchProc.setType(itemBatchProcType.getValue());
        itemBatchProc.setCmd(JsonUtil.toJson(cmd));
        itemBatchProc.setStatus(ItemBatchProcStatus.TO_BE_EXECUTED.getValue());
        itemBatchProc.setProcess(0);
        if (synchronizedCallback != null) {
            synchronizedCallback.run();
        }
        save(itemBatchProc);
        return itemBatchProc.getId();
    }

    @Override
    public boolean lock(long id, long lockUtil) {
        final ItemBatchProc itemBatchProc = new ItemBatchProc();
        itemBatchProc.setLockUntil(lockUtil);
        return updateIfUnLock(id).update(itemBatchProc);
    }

    @Override
    public boolean setProcessingStatus(long id, long lockUtil) {
        return lambdaUpdate()
                .set(ItemBatchProc::getStatus, ItemBatchProcStatus.UNDER_EXECUTION.getValue())
                .set(ItemBatchProc::getLockUntil, lockUtil)
                .eq(ItemBatchProc::getId, id)
                .eq(ItemBatchProc::getStatus, ItemBatchProcStatus.TO_BE_EXECUTED.getValue())
                .update();
    }

    @Override
    public boolean setProcess(long id, int progress) {
        final ItemBatchProc itemBatchProc = new ItemBatchProc();
        itemBatchProc.setId(id);
        itemBatchProc.setProcess(progress);
        itemBatchProc.setAddLockUntil(300);
        return updateById(itemBatchProc);
    }

    @Override
    public boolean setCompleteStatus(long id, boolean success, String msg) {
        return lambdaUpdate()
                .set(
                        ItemBatchProc::getStatus,
                        success
                                ? ItemBatchProcStatus.EXECUTED.getValue()
                                : ItemBatchProcStatus.EXCEPTION.getValue())
                .setSql(
                        StringUtil.isNotBlank(msg),
                        String.format(
                                "log = json_array_insert(log, '$[0]', '%s')",
                                success
                                        ? LogUtil.simpleLogStr(Level.INFO, msg)
                                        : LogUtil.simpleLogStr(Level.ERROR, msg)))
                .set(ItemBatchProc::getLockUntil, 0L)
                .eq(ItemBatchProc::getId, id)
                .eq(ItemBatchProc::getStatus, ItemBatchProcStatus.UNDER_EXECUTION.getValue())
                .update();
    }

    @Override
    public boolean log(long id, Level level, String msg, Object... vars) {
        final ItemBatchProc itemBatchProc = new ItemBatchProc();
        itemBatchProc.setId(id);
        itemBatchProc.setAppendLog(LogUtil.simpleLogStr(level, msg, vars));
        return updateById(itemBatchProc);
    }

    @Override
    public boolean renewLock(long id, long lockUtil) {
        final ItemBatchProc itemBatchProc = new ItemBatchProc();
        itemBatchProc.setId(id);
        itemBatchProc.setLockUntil(lockUtil);
        return updateById(itemBatchProc);
    }

    @Override
    public boolean retry(long id) {
        final ItemBatchProc itemBatchProc = new ItemBatchProc();
        itemBatchProc.setAddRetryNum();
        itemBatchProc.setAppendLog(LogUtil.simpleLogStr(Level.ERROR, "超时重试"));
        itemBatchProc.setProcess(0);
        return updateById(itemBatchProc);
    }

    private LambdaUpdateChainWrapper<ItemBatchProc> updateIfUnLock(long id) {
        return lambdaUpdate()
                .eq(ItemBatchProc::getId, id)
                .and(
                        q ->
                                q.eq(ItemBatchProc::getLockUntil, 0L)
                                        .or()
                                        .le(ItemBatchProc::getLockUntil, DateUtil.currentTime()));
    }
}
