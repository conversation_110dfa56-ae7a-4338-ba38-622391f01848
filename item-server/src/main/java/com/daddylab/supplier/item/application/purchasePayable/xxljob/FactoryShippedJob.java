package com.daddylab.supplier.item.application.purchasePayable.xxljob;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.purchasePayable.dto.PurchasePayableCmd;
import com.daddylab.supplier.item.application.purchasePayable.service.PurchasePayableBizService;
import com.daddylab.supplier.item.application.stockInOrder.StockInOrderBizService;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
public class FactoryShippedJob {

    @Autowired
    private StockInOrderBizService stockInOrderBizService;

    @Autowired
    private PurchasePayableBizService purchasePayableBizService;

    /**
     * 工厂发货 自动生成采购应付单
     */
    @XxlJob("factoryShippedJob")
    public void factoryShippedJobHandler(){
        XxlJobHelper.log("XXL-JOB 启动");

        //获取当前月份
        LocalDateTime localDateTime = DateUtil.lastMonth();
        //获取上个月第一天和最后一天时间
        long startTime = DateUtil.toTime(DateUtil.getFirstDayOfMonth(localDateTime));
        long endTime = DateUtil.toTime(DateUtil.getLastDayOfMonth(localDateTime));
        List<StockInOrder> noList = stockInOrderBizService.listFinishStockInOrder(startTime,  endTime);
        XxlJobHelper.log("查询出符合要求的IDS: {}", JSON.toJSON(noList));
        if(CollectionUtil.isEmpty(noList)){
            return;
        }
        noList.stream().forEach(e -> {
            PurchasePayableCmd cmd = new PurchasePayableCmd();
            cmd.setType(PurchaseTypeEnum.IN_STOCK_PAYABLE.getValue());
            cmd.setRelatedOrderId(e.getId());
            cmd.setBuyerId(e.getBuyerUserId());
            cmd.setCreatedUid(e.getCreatedUid());
            purchasePayableBizService.create(cmd);
        });
    }
}
