package com.daddylab.supplier.item.controller.item.dto.detail;

import com.daddylab.supplier.item.controller.item.dto.ItemSkuListDto;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/12 7:56 下午
 * @description
 */
@Data
@ApiModel("商品详情返回封装")
public class ItemDetailVo implements Serializable {

    private static final long serialVersionUID = 7736027413848793319L;

    @ApiModelProperty("合作伙伴商品信息")
    private PartnerItemVo partnerItemVo;

    @ApiModelProperty("商品基础信息")
    private ItemDetailBaseVo itemDetailBaseVo;

    @ApiModelProperty("sku信息")
    private List<ItemSkuListDto> itemDetailSkuVoList;

    @ApiModelProperty("图片信息")
    private List<ItemDetailImageVo> itemDetailImageVoList;

    @ApiModelProperty("采购物流信息")
    private ItemDetailProcurementVo itemDetailProcurementVo;


}
