//package com.daddylab.supplier.item.application.purchasePayable.xxljob;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.StrUtil;
//import com.alibaba.cola.dto.MultiResponse;
//import com.alibaba.cola.dto.SingleResponse;
//import com.alibaba.cola.exception.ExceptionFactory;
//import com.daddylab.supplier.item.application.purchasePayable.dto.ArtificialPayOrderDetailSaveDto;
//import com.daddylab.supplier.item.application.purchasePayable.dto.ArtificialPayOrderSaveDto;
//import com.daddylab.supplier.item.application.purchasePayable.service.PurchasePayableBizService;
//import com.daddylab.supplier.item.common.enums.ErrorCode;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableApplyOrder;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableApplyOrderDetail;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableOrderRelation;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayOrderRelateType;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayableApplyOrderStatus;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchasePayableApplyOrderDetailService;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchasePayableApplyOrderService;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchasePayableOrderRelationService;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchasePayableOrderService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.List;
//import java.util.Objects;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR> up
// * @date 2023年02月20日 2:55 PM
// */
//@Service
//@Slf4j
//public class ApplyPayHandlerJob {
//
//    @Resource
//    IPurchasePayableApplyOrderService iPurchasePayableApplyOrderService;
//
//    @Resource
//    IPurchasePayableOrderService iPurchasePayableOrderService;
//
//    @Resource
//    PurchasePayableBizService purchasePayableBizService;
//
//    @Resource
//    IPurchasePayableApplyOrderDetailService iPurchasePayableApplyOrderDetailService;
//
//    @Resource
//    IPurchasePayableOrderRelationService iPurchasePayableOrderRelationService;
//
//    /**
//     * 申请府付款单ID
//     *
//     * @param payApplyOrderId
//     */
//    public void handler(Long payApplyOrderId) {
//        // 查询已经审核完成的付款申请单
//        List<PurchasePayableApplyOrder> list = iPurchasePayableApplyOrderService.lambdaQuery()
//                .eq(PurchasePayableApplyOrder::getStatus, PayableApplyOrderStatus.HAD_AUDIT)
//                .eq(Objects.nonNull(payApplyOrderId), PurchasePayableApplyOrder::getId, payApplyOrderId)
//                .list();
//        if (CollUtil.isEmpty(list)) {
//            return;
//        }
//
//        list.forEach(this::createdParam);
//
//    }
//
//    /**
//     * 单笔处理
//     *
//     * @param payableApplyOrder 付款申请单
//     */
//    private void createdParam(PurchasePayableApplyOrder payableApplyOrder) {
//        // 查询出付款申请单的详情。
//        Long payableApplyOrderId = payableApplyOrder.getId();
//        List<PurchasePayableApplyOrderDetail> detailList = iPurchasePayableApplyOrderDetailService.lambdaQuery()
//                .eq(PurchasePayableApplyOrderDetail::getApplyOrderId, payableApplyOrderId).list();
//
//        List<ArtificialPayOrderSaveDto> resList = new ArrayList<>();
//
//        // 应付单来源于入库单和出库单
//        // 出入库单来源于采购订单，采购订单已经按照供应商+仓库编号完成切割
//        detailList.stream().collect(Collectors.groupingBy(PurchasePayableApplyOrderDetail::getPurchasePayOrderNo))
//                .forEach((purchasePayOrderNo, list) -> {
//                    Long providerId = list.get(0).getProviderId();
//                    String warehouseNo = list.get(0).getWarehouseNo();
//
//                    ArtificialPayOrderSaveDto saveDto = new ArtificialPayOrderSaveDto();
//                    saveDto.setPayableApplyOrderNo(payableApplyOrder.getNo());
//                    saveDto.setSourcePurchasePayNo(purchasePayOrderNo);
//                    saveDto.setWarehouseNo(warehouseNo);
//                    saveDto.setProviderId(providerId);
//                    saveDto.setDetailSaveDtoList(list.stream().map(val -> {
//                        ArtificialPayOrderDetailSaveDto detailSaveDto = new ArtificialPayOrderDetailSaveDto();
//                        detailSaveDto.setSkuCode(val.getSkuCode());
//                        detailSaveDto.setFixedQuantity(val.getFixedQuantity());
//                        detailSaveDto.setFixedTotalAmount(val.getFixedTotalAmount());
//                        return detailSaveDto;
//                    }).collect(Collectors.toList()));
//
//                    resList.add(saveDto);
//                });
//
//        // 处理过程日志暂时不记录入库，打好日志标记，暂时查log找问题。
//        boolean isOk = true;
//        if (CollUtil.isNotEmpty(resList)) {
//            isOk = handler0(resList);
//        }
//
//        payableApplyOrder.setStatus(isOk ? PayableApplyOrderStatus.FINISH : PayableApplyOrderStatus.ERROR);
//        iPurchasePayableApplyOrderService.updateById(payableApplyOrder);
//
//    }
//
//    private Boolean handler0(List<ArtificialPayOrderSaveDto> list) {
//        boolean isOk = true;
//        for (ArtificialPayOrderSaveDto val : list) {
//            try {
//                // 1.冲销旧数据(逆向生成出入库单，推送金蝶)
//                SingleResponse<String> hedgeOldPayDataRes = purchasePayableBizService.hedgeOldPayData(val.getSourcePurchasePayNo());
//                if (hedgeOldPayDataRes.isSuccess()) {
//                    // 1.1 保存冲销单和原采购应付单的关联关系
//                    saveRelation(val.getSourcePurchasePayNo(), Collections.singletonList(hedgeOldPayDataRes.getData())
//                            , PayOrderRelateType.ALL_HEDGE);
//                } else {
//                    String error = StrUtil.format(
//                            "payableApplyOrder handler fail. " +
//                                    "hedgeOldPayData fail,payableApplyOrderNo:{},sourcePurchasePayNo:{},hedgeOldPayDataResStockNo:{},error:{}"
//                            , val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo(), hedgeOldPayDataRes.getData(), hedgeOldPayDataRes.getErrMessage()
//                    );
//                    log.error(error);
//                    isOk = false;
//                    continue;
//                }
//
//                // 2.根据修正数据重新生成推送出入库单（后续金蝶会自动根据推送的出库单生成应付单）
//                MultiResponse<String> fixRes = purchasePayableBizService.fixPurchasePayData(val);
//                if (fixRes.isSuccess()) {
//                    saveRelation(val.getSourcePurchasePayNo(), fixRes.getData(), PayOrderRelateType.FIXED);
//                } else {
//                    String error = StrUtil.format(
//                            "payableApplyOrder handler fail. " +
//                                    "fixPurchasePayData fail,payableApplyOrderNo:{},sourcePurchasePayNo:{},fixStockOrderRes:{},error:{}"
//                            , val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo(), fixRes.getData(), fixRes.getErrMessage()
//                    );
//                    log.error(error);
//                    saveErrorLog(val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo(), error);
//                    isOk = false;
//                }
//
//                log.info("payableApplyOrder handler success. payableApplyOrderNo:{},sourcePurchasePayNo:{}",
//                        val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo());
//            } catch (Exception e) {
//                log.error("payableApplyOrder handler exception. payableApplyOrderNo:{},sourcePurchasePayNo:{}"
//                        , val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo(), e);
//
//                String error = StrUtil.format(
//                        "payableApplyOrder handler exception. payableApplyOrderNo:{},sourcePurchasePayNo:{}"
//                        , val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo(), e.getMessage());
//                saveErrorLog(val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo(), error);
//                isOk = false;
//            }
//        }
//        return isOk;
//    }
//
//    /**
//     * 保存错误信息
//     *
//     * @param payableApplyOrderNo 申请付款单编号
//     * @param sourcePurchasePayNo 原采购应付单编号
//     * @param error               错误信息
//     */
//    private void saveErrorLog(String payableApplyOrderNo, String sourcePurchasePayNo, String error) {
//        iPurchasePayableApplyOrderDetailService.lambdaUpdate().set(PurchasePayableApplyOrderDetail::getErrorLog, error)
//                .eq(PurchasePayableApplyOrderDetail::getApplyOrderNo, payableApplyOrderNo)
//                .eq(PurchasePayableApplyOrderDetail::getPurchasePayOrderNo, sourcePurchasePayNo)
//                .update();
//    }
//
//    private void saveRelation(String oldPurchasePayableOrderNo, List<String> hedgeStockOrderNos, PayOrderRelateType type) {
//        List<PurchasePayableOrderRelation> collect = hedgeStockOrderNos.stream().map(val -> {
//            PurchasePayableOrderRelation relation = new PurchasePayableOrderRelation();
//            relation.setResourceNo(oldPurchasePayableOrderNo);
//            relation.setRelateStockOrderNo(val);
//            relation.setType(type);
//            return relation;
//        }).collect(Collectors.toList());
//
//        if (CollUtil.isNotEmpty(collect)) {
//            iPurchasePayableOrderRelationService.saveBatch(collect);
//        }
//    }
//
//    public Boolean compensateHedgeOldPayData(String payApplyOrderNo, String sourcePurchasePayOrderNo) {
//        List<PurchasePayableApplyOrder> list = iPurchasePayableApplyOrderService.lambdaQuery()
//                .eq(PurchasePayableApplyOrder::getNo, payApplyOrderNo).list();
//        if (CollUtil.isEmpty(list)) {
//            throw ExceptionFactory.bizException(ErrorCode.API_RESP_ERROR.getCode(), "申请应付单编号非法");
//        }
//        PurchasePayableApplyOrder payableApplyOrder = list.get(0);
//
//        List<PurchasePayableApplyOrderDetail> detailList = iPurchasePayableApplyOrderDetailService.lambdaQuery()
//                .eq(PurchasePayableApplyOrderDetail::getApplyOrderId, payableApplyOrder.getId())
//                .eq(PurchasePayableApplyOrderDetail::getPurchasePayOrderNo, sourcePurchasePayOrderNo)
//                .list();
//        if (CollUtil.isEmpty(detailList)) {
//            throw ExceptionFactory.bizException(ErrorCode.API_RESP_ERROR.getCode(), "申请应付单明细查询为空");
//        }
//
//        ArtificialPayOrderSaveDto saveDto = new ArtificialPayOrderSaveDto();
//        saveDto.setPayableApplyOrderNo(payableApplyOrder.getNo());
//        saveDto.setSourcePurchasePayNo(sourcePurchasePayOrderNo);
//        saveDto.setWarehouseNo(detailList.get(0).getWarehouseNo());
//        saveDto.setProviderId(payableApplyOrder.getProviderId());
//        saveDto.setDetailSaveDtoList(detailList.stream().map(val -> {
//            ArtificialPayOrderDetailSaveDto detailSaveDto = new ArtificialPayOrderDetailSaveDto();
//            detailSaveDto.setSkuCode(val.getSkuCode());
//            detailSaveDto.setFixedQuantity(val.getFixedQuantity());
//            detailSaveDto.setFixedTotalAmount(val.getFixedTotalAmount());
//            return detailSaveDto;
//        }).collect(Collectors.toList()));
//
//        return handler0(Collections.singletonList(saveDto));
//    }
//
//}
