package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.Id;
import org.javers.core.metamodel.annotation.PropertyName;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 订单结算/订货明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrderSettlementDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @DiffIgnore
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @DiffIgnore
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    @DiffIgnore
    private Long updatedAt;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    @DiffIgnore
    private Long createdUid;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    @DiffIgnore
    private Long updatedUid;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    @DiffIgnore
    private Long deletedAt;

    /**
     * 是否删除
     */
    @TableLogic
    @DiffIgnore
    private Integer isDel;

    /**
     * 订单结算表单Id
     */
    @DiffIgnore
    private Long formId;

    /**
     * 订单结算表单编码
     */
    @DiffIgnore
    private String formNo;

    /**
     * 结算月份开始日期
     */
    @DiffIgnore
    private Long settlementStartDate;

    /**
     * 结算月份结束日期
     */
    @DiffIgnore
    private Long settlementEndDate;

    /**
     * sku编码
     */
    @Id
    private String skuCode;

    /**
     * 暂估单价
     */
    @DiffIgnore
    private BigDecimal temporaryPrice;

    /**
     * 暂估数量
     */
    @DiffIgnore
    private Integer temporaryQuantity;

    /**
     * 发货数量(销售出库数量，不考虑入库，正向数据)
     */
    @PropertyName("发货数量")
    private Integer deliverQuantity;

    /**
     * 当月退货数量
     */
    @PropertyName("当月退货数量")
    private Integer currentMonthRefundQuantity;

    /**
     * 跨月退货数量
     */
    @PropertyName("跨月退货数量")
    private Integer crossMonthRefundQuantity;

    /**
     * 结算数量
     */
    @DiffIgnore
    private Integer settlementQuantity;

    /**
     * 结算单价
     */
    @PropertyName("结算单价")
    private BigDecimal settlementPrice;

    /**
     * 结算金额
     */
    @DiffIgnore
    private BigDecimal settlementAmount;

    /**
     * 运费以及售后分摊金额
     */
    @PropertyName("运费以及售后分摊金额")
    private BigDecimal afterSalesCost;

    /**
     * 最终金额
     */
    @DiffIgnore
    private BigDecimal finalAmount;

    /**
     * 备注说明
     */
    @PropertyName("备注说明")
    private String remark;

    /**
     * 来源
     */
    @DiffIgnore
    private String source;

    /**
     * 结算单冲销流程中，SKU 之允许冲销一次。
     */
    @DiffIgnore
    private Integer hadWriteOff;


}
