package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOtherStockInOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOtherStockInOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOtherStockInOrderService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 旺店通其他入库单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Service
public class WdtOtherStockInOrderServiceImpl extends DaddyServiceImpl<WdtOtherStockInOrderMapper, WdtOtherStockInOrder> implements IWdtOtherStockInOrderService {

}
