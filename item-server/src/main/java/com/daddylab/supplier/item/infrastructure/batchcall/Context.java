package com.daddylab.supplier.item.infrastructure.batchcall;

import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.google.common.collect.Maps;
import lombok.Getter;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.invoke.WrongMethodTypeException;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021/12/11
 */
@Getter
public class Context {
    final static Map<Object, Method> METHOD_CACHE = Maps.newHashMap();
    final static Map<Object, Method> BATCH_METHOD_CACHE = Maps.newHashMap();
    final ProceedingJoinPoint pjp;
    final BatchCall annotation;
    final Object arg;
    final Object bean;
    final Method method;
    final Method batchMethod;

    public Context(ProceedingJoinPoint pjp, BatchCall annotation, Object arg) {
        this.pjp = pjp;
        this.annotation = annotation;
        this.arg = arg;
        method = getMethod0();
        bean = getBean0();
        batchMethod = getBatchMethod0();
        checkMethod(method, batchMethod);
    }

    private Object getBean0() {
        Object bean;
        if (!annotation.qualifier().isEmpty()) {
            bean = ApplicationContextUtil.getBean(annotation.qualifier(), annotation.batchClass());
        } else {
            bean = ApplicationContextUtil.getBean(annotation.batchClass());
        }
        if (Objects.isNull(bean)) {
            throw new IllegalArgumentException("未在当前应用环境中找到指定组件");
        }
        return bean;
    }

    private void checkMethod(Method method, Method batchMethod) {
        if (!(method.getParameterCount() == 1
                && batchMethod.getParameterCount() == 1)) {

            throw new WrongMethodTypeException("代理方法或批量方法参数应该有且仅有一个");
        }

        final Class<?> parameterType = method.getParameterTypes()[0];
        final Class<?> returnType = method.getReturnType();

        final Class<?> batchParameterType = batchMethod.getParameterTypes()[0];
        if (!Collection.class.isAssignableFrom(batchParameterType)) {
            throw new WrongMethodTypeException("批量方法参数类型应为Collection类型");
        }

        final Class<?> batchReturnType = batchMethod.getReturnType();
        if (!Map.class.isAssignableFrom(batchReturnType)) {
            throw new WrongMethodTypeException("批量方法返回值类型应为Map类型");
        }

        final Type batchGenericParameterType = batchMethod.getGenericParameterTypes()[0];
        if (batchGenericParameterType instanceof ParameterizedType) {
            final Type batchActualTypeArgument = ((ParameterizedType) batchGenericParameterType).getActualTypeArguments()[0];
            if (!(batchActualTypeArgument instanceof Class) || batchActualTypeArgument.equals(parameterType)) {
                throw new WrongMethodTypeException("批量方法参数范型与代理方法不匹配");
            }
        }

        final Type batchMethodGenericReturnType = batchMethod.getGenericReturnType();
        if (batchMethodGenericReturnType instanceof ParameterizedType) {
            final Type[] actualTypeArguments = ((ParameterizedType) batchMethodGenericReturnType).getActualTypeArguments();
            if (!actualTypeArguments[0].equals(parameterType) || !actualTypeArguments[1].equals(returnType)) {
                throw new WrongMethodTypeException("批量方法返回值范型与代理方法不匹配");
            }
        }
    }

    private Method getMethod0() {
        synchronized (METHOD_CACHE) {
            return METHOD_CACHE.computeIfAbsent(pjp.getTarget(), k -> ((MethodSignature) pjp.getSignature()).getMethod());
        }
    }

    private Method getBatchMethod0() {
        synchronized (BATCH_METHOD_CACHE) {
            return BATCH_METHOD_CACHE.computeIfAbsent(pjp.getTarget(), k -> Arrays.stream(org.springframework.util.ReflectionUtils.getDeclaredMethods(bean.getClass()))
                    .filter(m -> m.getName().equals(annotation.batchMethod()))
                    .findAny().orElseThrow(() -> new IllegalArgumentException("Method:" + annotation.batchMethod() + " not found")));
        }
    }


}