//package com.daddylab.supplier.item.application.afterSaleLogistics.monitor;
//
//import com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsException;
//import com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsRootException;
//import com.daddylab.supplier.item.application.afterSaleLogistics.dto.LogisticExceptionRes;
//import com.daddylab.supplier.item.domain.region.gateway.RegionGateway;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.KdyCallback;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsTrace;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrder;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IKdyCallbackService;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderService;
//import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
//import com.daddylab.supplier.item.types.kuaidaoyun.KdyTrackItem;
//import com.daddylab.supplier.item.types.kuaidaoyun.KdyTrackList;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.core.annotation.Order;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.Objects;
//import java.util.Optional;
//import java.util.concurrent.TimeUnit;
//import java.util.concurrent.atomic.AtomicReference;
//
//import static com.daddylab.supplier.item.common.GlobalConstant.LOGISTICS_DEVELOPED_DISTRICTS;
//import static com.daddylab.supplier.item.common.GlobalConstant.LOGISTICS_REMOTE_DISTRICTS;
//
///**
// * 中转即将超时
// * 1，揽件与收货地址在江浙沪皖、京津冀区域内，在相应物流公司官网上，从首条分拨中心轨迹更新开始至派件信息之间，物流轨迹长时间未更新超过24小时,
// * 2，揽收与收货地址同省，且在非偏远地区、非江浙沪皖、非京津冀区域，在相应物流公司官网上从首条分拨中心轨迹更新开始至派件信息之间，物流轨迹长时间未更新超过24小时；
// * 3，揽件与收货地址跨省，且在非偏远地区、非江浙沪皖、非京津冀区域，在相应物流公司官网上从首条分拨中心轨迹更新开始至派件信息之间，物流轨迹长时间未更新超过48小时；
// * 4，揽件与收货地址任一在偏远地区，在相应物流公司官网上从首条分拨中心轨迹更新开始至派件信息之间，物流轨迹长时间未更新超过96小时；
// * *偏远地区：新疆、西藏、内蒙古、青海、宁夏、甘肃、海南、云南；
// * <p>
// * 中转超时
// * 1，揽件与收货地址在江浙沪皖、京津冀区域内，在相应物流公司官网上，从首条分拨中心轨迹更新开始至派件信息之间，物流轨迹长时间未更新超过48小时,
// * 2，揽收与收货地址同省，且在非偏远地区、非江浙沪皖、非京津冀区域，在相应物流公司官网上从首条分拨中心轨迹更新开始至派件信息之间，物流轨迹长时间未更新超过48小时；
// * 3，揽件与收货地址跨省，且在非偏远地区、非江浙沪皖、非京津冀区域，在相应物流公司官网上从首条分拨中心轨迹更新开始至派件信息之间，物流轨迹长时间未更新超过72小时；
// * 4，揽件与收货地址任一在偏远地区，在相应物流公司官网上从首条分拨中心轨迹更新开始至派件信息之间，物流轨迹长时间未更新超过120小时；
// * *偏远地区：新疆、西藏、内蒙古、青海、宁夏、甘肃、海南、云南
// *
// * <AUTHOR> up
// * @date 2024年05月22日 10:44 AM
// */
//@Order(4)
//@Slf4j
//@Component
//@AllArgsConstructor
//public class TransferExceptionMonitor implements OrderLogisticsExceptionMonitor {
//
//    final private IWdtOrderService wdtOrderService;
//    final private IKdyCallbackService iKdyCallbackService;
//    final private RegionGateway regionGateway;
//    private final static String PICK_UP_SHORT_CODE = "收件";
//
//    @Override
//    public LogisticExceptionRes process(Long currentTurnTime, OrderLogisticsTrace trace) {
//        LogisticExceptionRes res = new LogisticExceptionRes();
//
//        Long sendingTime = trace.getSendingTime();
//        if (Objects.nonNull(sendingTime) && sendingTime > 0) {
//            String receiveProvince = StringUtils.EMPTY;
//            String pickUpProvince = StringUtils.EMPTY;
//            Optional<KdyCallback> callbackOpt = iKdyCallbackService.lambdaQuery().eq(KdyCallback::getLogisticsNo, trace.getLogisticsNo()).oneOpt();
//            Optional<WdtOrder> orderOptional = wdtOrderService.lambdaQuery().eq(WdtOrder::getTradeNo, trace.getWdtTradeNo()).oneOpt();
//            if (callbackOpt.isPresent()) {
//                pickUpProvince = parsePickUpProvince(callbackOpt);
//            }
//            if (orderOptional.isPresent()) {
//                Integer receiverProvince = orderOptional.get().getReceiverProvince();
//                if (Objects.nonNull(receiverProvince)) {
//                    receiveProvince = regionGateway.getCodeByName(receiverProvince.toString());
//                }
//            }
//            boolean matchOption1 = inDevelopedProvince(receiveProvince) && inDevelopedProvince(pickUpProvince);
//            if (matchOption1) {
//                long gap = DateUtil.calculateDifference(currentTurnTime, trace.getTrackTime(), TimeUnit.HOURS);
//                if (gap >= 48) {
//                    res.setRootException(LogisticsRootException.TRANSFER_EXCEPTION);
//                    res.setSubException(LogisticsException.TRANSFER_ALREADY_OVERTIME);
//                    return res;
//                }
//                if (gap >= 24) {
//                    res.setRootException(LogisticsRootException.TRANSFER_EXCEPTION);
//                    res.setSubException(LogisticsException.TRANSFER_ABOUT_OVERTIME);
//                    return res;
//                }
//            }
//
//            boolean isSameProvince = (StringUtils.isNotBlank(receiveProvince) && StringUtils.isNotBlank(pickUpProvince))
//                    && receiveProvince.equals(pickUpProvince);
//            boolean matchOption2 = isSameProvince && notInRemoteProvince(receiveProvince) && notInDevelopedProvince(receiveProvince);
//            if (matchOption2) {
//                long gap = DateUtil.calculateDifference(currentTurnTime, trace.getTrackTime(), TimeUnit.HOURS);
//                if (gap >= 48) {
//                    res.setRootException(LogisticsRootException.TRANSFER_EXCEPTION);
//                    res.setSubException(LogisticsException.TRANSFER_ALREADY_OVERTIME);
//                    return res;
//                }
//                if (gap >= 24) {
//                    res.setRootException(LogisticsRootException.TRANSFER_EXCEPTION);
//                    res.setSubException(LogisticsException.TRANSFER_ABOUT_OVERTIME);
//                    return res;
//                }
//            }
//
//            boolean matchOption3 =
//                    StringUtils.isNotBlank(receiveProvince) && StringUtils.isNotBlank(pickUpProvince)
//                            && !receiveProvince.equals(pickUpProvince)
//                            && notInRemoteProvince(receiveProvince) && notInDevelopedProvince(receiveProvince)
//                            && notInRemoteProvince(pickUpProvince) && notInDevelopedProvince(pickUpProvince);
//            if (matchOption3) {
//                long gap = DateUtil.calculateDifference(currentTurnTime, trace.getTrackTime(), TimeUnit.HOURS);
//                if (gap >= 72) {
//                    res.setRootException(LogisticsRootException.TRANSFER_EXCEPTION);
//                    res.setSubException(LogisticsException.TRANSFER_ALREADY_OVERTIME);
//                    return res;
//                }
//                if (gap >= 48) {
//                    res.setRootException(LogisticsRootException.TRANSFER_EXCEPTION);
//                    res.setSubException(LogisticsException.TRANSFER_ABOUT_OVERTIME);
//                    return res;
//                }
//            }
//
//            boolean matchOption4 = inRemoteProvince(receiveProvince) || inRemoteProvince(pickUpProvince);
//            if (matchOption4) {
//                long gap = DateUtil.calculateDifference(currentTurnTime, trace.getTrackTime(), TimeUnit.HOURS);
//                if (gap >= 120) {
//                    res.setRootException(LogisticsRootException.TRANSFER_EXCEPTION);
//                    res.setSubException(LogisticsException.TRANSFER_ALREADY_OVERTIME);
//                    return res;
//                }
//                if (gap >= 96) {
//                    res.setRootException(LogisticsRootException.TRANSFER_EXCEPTION);
//                    res.setSubException(LogisticsException.TRANSFER_ABOUT_OVERTIME);
//                    return res;
//                }
//            }
//        }
//        res.setRootException(LogisticsRootException.NORMAL);
//        return res;
//    }
//
//
//
//    /**
//     * 在包邮区
//     *
//     * @param provinceName
//     * @return
//     */
//    private Boolean inDevelopedProvince(String provinceName) {
//        if (StringUtils.isBlank(provinceName)) {
//            return false;
//        }
//        for (String logisticsDevelopedDistrict : LOGISTICS_DEVELOPED_DISTRICTS) {
//            boolean contains = provinceName.contains(logisticsDevelopedDistrict);
//            if (contains) {
//                return true;
//            }
//        }
//        return false;
//    }
//
//    /**
//     * 不在包邮区
//     *
//     * @param provinceName
//     * @return
//     */
//    private Boolean notInDevelopedProvince(String provinceName) {
//        for (String logisticsDevelopedDistrict : LOGISTICS_DEVELOPED_DISTRICTS) {
//            if (provinceName.contains(logisticsDevelopedDistrict)) {
//                return false;
//            }
//        }
//        return true;
//    }
//
//
//    private Boolean notInRemoteProvince(String provinceName) {
//        for (String s : LOGISTICS_REMOTE_DISTRICTS) {
//            if (provinceName.contains(s)) {
//                return false;
//            }
//        }
//        return true;
//    }
//
//    private Boolean inRemoteProvince(String provinceName) {
//        if (StringUtils.isBlank(provinceName)) {
//            return false;
//        }
//        for (String s : LOGISTICS_REMOTE_DISTRICTS) {
//            if (provinceName.contains(s)) {
//                return true;
//            }
//        }
//        return false;
//    }
//}
