package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.PropertyName;
import org.javers.core.metamodel.annotation.TypeName;

import java.io.Serializable;

/**
 * 微信商品详情
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TypeName("微信商品详情")
public class WechatProductDetail extends Entity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 类型 daddylab/member
     */
    @PropertyName("类型")
    private String type;

    /**
     * 微信商品ID
     */
    @PropertyName("微信商品ID")
    private String productId;

    /**
     * 商品标题
     */
    @PropertyName("商品标题")
    private String title;

    /**
     * 商品副标题
     */
    @PropertyName("商品副标题")
    private String subTitle;

    /**
     * 商品主图
     */
    @PropertyName("商品主图")
    private String headImg;

    /**
     * 商品分类ID
     */
    @PropertyName("商品分类ID")
    private String categoryId;

    /**
     * 商品品牌ID
     */
    @PropertyName("商品品牌ID")
    private String brandId;

    /**
     * 商品状态 0:初始值 5:上架 6:回收站 9:彻底删除 11:自主下架 13:违规下架 14:保证金不足下架 15:品牌过期下架 20:商品被封禁
     */
    @PropertyName("商品状态")
    private Integer status;

    /**
     * 商品草稿状态 0:初始值 1:编辑中 2:审核中 3:审核失败 4:审核成功 7:商品异步提交上传中 8:商品异步提交上传失败
     */
    @PropertyName("商品草稿状态")
    private Integer editStatus;

    /**
     * 商品类型 0:普通商品 1:虚拟商品
     */
    @PropertyName("商品类型")
    private Integer productType;

    /**
     * 商品详情文本
     */
    @PropertyName("商品详情文本")
    private String descText;

    /**
     * 商品详情图片列表(JSON格式)
     */
    @PropertyName("商品详情图片列表")
    private String descImgs;

    /**
     * 商品属性(JSON格式)
     */
    @PropertyName("商品属性")
    private String attrs;

    /**
     * 商品规格(JSON格式)
     */
    @PropertyName("商品规格")
    private String skus;

    /**
     * 商品销售库存限制(JSON格式)
     */
    @PropertyName("商品销售库存限制")
    private String saleLimitInfo;

    /**
     * 尺码表(JSON格式)
     */
    @PropertyName("尺码表")
    private String sizeChart;

    /**
     * 同步时间
     */
    @PropertyName("同步时间")
    @DiffIgnore
    private Long syncTime;

    /**
     * 同步状态 0:失败 1:成功
     */
    @PropertyName("同步状态")
    @DiffIgnore
    private Integer syncStatus;

    /**
     * 同步错误信息
     */
    @PropertyName("同步错误信息")
    @DiffIgnore
    private String syncError;
} 