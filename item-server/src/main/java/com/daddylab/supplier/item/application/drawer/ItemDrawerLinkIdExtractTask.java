package com.daddylab.supplier.item.application.drawer;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.domain.itemSync.DouLink;
import com.daddylab.supplier.item.domain.itemSync.TaobaoGoodsDetailLink;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerService;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/11/15
 */
@AllArgsConstructor
@Service
@Slf4j
public class ItemDrawerLinkIdExtractTask {

    private final IItemDrawerService itemDrawerService;

    @XxlJob("ItemDrawerLinkIdExtractTask")
    public void doTask() {
        final List<ItemDrawer> list = itemDrawerService.lambdaQuery().ne(ItemDrawer::getDouLink, "")
                .or().ne(ItemDrawer::getTbLink, "").list();
        log.info("开始提取淘宝、抖店商品ID，总计={}", list.size());
        int i = 0;
        for (ItemDrawer itemDrawer : list) {
            i++;
            log.info("正在处理{}/{}", i, list.size());
            final ItemDrawer updateItemDrawer = new ItemDrawer();
            updateItemDrawer.setId(itemDrawer.getId());
            try {
                updateItemDrawer.setTbId(
                        new TaobaoGoodsDetailLink(itemDrawer.getTbLink()).getGoodsId());
            } catch (Exception e) {
                log.error("解析淘宝链接异常 商品ID={} {}", itemDrawer.getItemId(), e.getMessage());
            }
            try {
                updateItemDrawer.setDouId(
                        new DouLink(itemDrawer.getDouLink()).getGoodsId());
            } catch (Exception e) {
                log.error("解析抖店链接异常 商品ID={} {}", itemDrawer.getItemId(), e.getMessage());
            }
            if (StringUtil.isNotBlank(updateItemDrawer.getDouId()) || StringUtil.isNotBlank(
                    updateItemDrawer.getTbId())) {
                itemDrawerService.updateById(updateItemDrawer);
                log.info("更新淘宝、抖店链接成功 商品ID={} 淘宝ID={} 抖店ID={}", itemDrawer.getItemId(),
                        updateItemDrawer.getTbId(), updateItemDrawer.getDouId());
            } else {
                log.info("跳过更新 商品ID={}", itemDrawer.getItemId());
            }
        }
    }

}
