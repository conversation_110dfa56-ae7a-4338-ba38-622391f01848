package com.daddylab.supplier.item.application.wdt;

import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.purchase.ProviderGoodsAPI;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.ProviderGoodsUploadPurchaseProviderGoods;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import java.util.ArrayList;
import java.util.Map;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/5/31
 */
@Service
public class WdtPurchaseBizServiceImpl implements WdtPurchaseBizService {

    private final WdtGateway wdtGateway;

    public WdtPurchaseBizServiceImpl(
            WdtGateway wdtGateway) {
        this.wdtGateway = wdtGateway;
    }

    @Override
    public void confirmProviderGoodsValid(Map<String, String> providerGoods) {
        if (providerGoods.isEmpty()) {
            return;
        }
        try {
            final ProviderGoodsAPI api = wdtGateway.getAPI(ProviderGoodsAPI.class);
            final ArrayList<ProviderGoodsUploadPurchaseProviderGoods> purchaseProviderGoodsList = new ArrayList<>();
            providerGoods.forEach((providerNo, specNo) -> {
                final ProviderGoodsUploadPurchaseProviderGoods providerGoodsUploadPurchaseProviderGoods = new ProviderGoodsUploadPurchaseProviderGoods();
                providerGoodsUploadPurchaseProviderGoods.setProviderNo(providerNo);
                providerGoodsUploadPurchaseProviderGoods.setSpecNo(specNo);
                purchaseProviderGoodsList.add(providerGoodsUploadPurchaseProviderGoods);
            });
            api.upload(purchaseProviderGoodsList);
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapBizException(e);
        }
    }
}
