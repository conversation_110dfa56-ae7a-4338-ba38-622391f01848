package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetStaff;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 盘货表关联的职员 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
public interface HandingSheetStaffMapper extends DaddyBaseMapper<HandingSheetStaff> {

    void deleteBySheetId(@Param("handingSheetId") Long handingSheetId);
}
