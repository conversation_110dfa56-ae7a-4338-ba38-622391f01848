package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemStatus;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 查询商品基础信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/12 8:55 下午
 * @description
 */
@Data
public class ItemBaseDO implements Serializable {

    private static final long serialVersionUID = -3400039893089003529L;

    // 以下字段可以直接映射

    private Long id;
    private String name;
    private String code;
    private String specialCode;
    private String partnerCode;
    private Integer businessLine;
    private String businessLines;
    private String categoryPath;
    private Long categoryId;
    private String categoryIdPath;
    private String brandName;
    private Long brandId;
    private String providerName;
    private Long providerId;
    private Long partnerProviderId;
    private String buyerName;
    private String buyerUserId;
    private Integer isGift;
    private Long parentItemId;
    private String parentCode;
    private Long mainImgId;
    private String mainImgUrl;

    /**
     * 是否是仓库发货 0.仓库发货。1.工厂发货。（多选逗号分隔）
     */
    private String delivery;

    /**
     * 商品状态
     */
    private ItemStatus itemStatus;
    private String statusReason;

    /**
     * qcIds
     */
    private String qcIds;
    /**
     * 商品拓展属性
     */
    private List<String> props;

    /**
     * 商品标签
     */
    List<String> tags;

    /**
     * 商品简称
     */
    private String shortName;

    private Long downFrameTime;

    private String downFrameReason;

}
