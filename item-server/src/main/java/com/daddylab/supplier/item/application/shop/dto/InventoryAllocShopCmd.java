package com.daddylab.supplier.item.application.shop.dto;

import com.alibaba.cola.dto.Command;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryAllocShopStatus;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.swing.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> up
 * @date 2025年06月23日 6:01 PM
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InventoryAllocShopCmd extends Command {

  private static final long serialVersionUID = -7834845941181440110L;

  private List<InventoryAllocShopDto> dtoList;

  @ApiModelProperty("下次库存自动分配时间")
  private Long nextAllocTime;

  @ApiModelProperty("库存分配周期")
  private Integer allocInterval;

  @ApiModelProperty("下次库存同步时间")
  private Long nextSyncTime;

  @ApiModelProperty("'库存同步周期'")
  private Integer syncInterval;

  @Data
  public static class InventoryAllocShopDto {
    private Long id;
    private Long shopId;
    private String sn;

    @ApiModelProperty("库存占比")
    private Integer inventoryWeight;

    @ApiModelProperty("状态")
    private InventoryAllocShopStatus status;

    @ApiModelProperty("低库存警戒")
    private Integer warnThreshold;
  }
}
