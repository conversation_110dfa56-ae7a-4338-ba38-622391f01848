package com.daddylab.supplier.item.application.item;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.goods.GoodsAPI;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsQueryWithSpecParams;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsQueryWithSpecResponse;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsQueryWithSpecResponse.Goods;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsQueryWithSpecResponse.Goods.Spec;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtGoodsSpec;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtGoodsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtGoodsSpecMapper;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import java.time.LocalDateTime;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/12/31
 */
@Component
@Slf4j
public class WdtGoodsFetcher implements PageFetcher {


    @Autowired
    private WdtGateway wdtGateway;
    @Autowired
    private WdtGoodsMapper wdtGoodsMapper;
    @Autowired
    private WdtGoodsSpecMapper wdtGoodsSpecMapper;

    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.WDT_GOODS;
    }

    @Override
    public int getTotal() {
        return query(null, null, 1, 1, true).getTotalCount();
    }

    @Override
    public int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
        return query(startTime, endTime, 1, 1, true).getTotalCount();
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, long pageIndex, long pageSize,
            RunContext runContext) {
        final GoodsQueryWithSpecResponse response = query(startTime, endTime, ((int) pageIndex),
                ((int) pageSize), false);
        handleResponse(response);
    }

    @Override
    public void fetch(long pageIndex, long pageSize, RunContext runContext) {
        final GoodsQueryWithSpecResponse response = query(null, null, ((int) pageIndex),
                ((int) pageSize), false);
        handleResponse(response);
    }

    public int fetchByQuery(GoodsQueryWithSpecParams params) {
        try {
            int pageSize = 100;
            final GoodsAPI api = wdtGateway.getAPI(GoodsAPI.class);
            final GoodsQueryWithSpecResponse totalResponse = api
                    .queryWithSpec(params, new Pager(1, 0, true));
            final Integer totalCount = totalResponse.getTotalCount();
            if (NumberUtil.isZeroOrNull(totalCount)) {
                return 0;
            }
            final int maxPage = (int) Math.ceil(totalCount / (float) pageSize);
            int pageIndex = maxPage;
            do {
                Pager pager = new Pager(pageSize, pageIndex - 1, true);
                final GoodsQueryWithSpecResponse response = api
                        .queryWithSpec(params, pager);
                handleResponse(response);
            } while (--pageIndex > 0);
            return totalCount;
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapFetchException(e, fetchDataType());
        }
    }

    public GoodsQueryWithSpecResponse query(LocalDateTime startTime, LocalDateTime endTime,
            int pageIndex, int pageSize, boolean calcTotal) {
        try {
            final GoodsAPI api = wdtGateway.getAPI(GoodsAPI.class);
            Pager pager = new Pager(pageSize, pageIndex - 1, calcTotal);
            final GoodsQueryWithSpecParams params = new GoodsQueryWithSpecParams();
            params.setHideDeleted(false);

            if (startTime != null) {
                params.setStartTime(DateUtil.format(startTime));
            }
            if (endTime != null) {
                params.setEndTime(DateUtil.format(endTime));
            }
            return api.queryWithSpec(params, pager);
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapFetchException(e, fetchDataType());
        }
    }

    private void handleResponse(GoodsQueryWithSpecResponse response) {
        if (CollUtil.isEmpty(response.getGoodsList())) {
            return;
        }
        final ArrayList<WdtGoods> wdtGoodsList = new ArrayList<>();
        final ArrayList<WdtGoodsSpec> wdtGoodsSpecsList = new ArrayList<>();
        final Long currentTime = DateUtil.currentTime();
        for (Goods goods : response.getGoodsList()) {
            final WdtGoods wdtGoods = Assembler.INST.apiGoodsToPo(goods);
            //创建时间只有首次插入的时候写入，后续不会更改
            wdtGoods.setCreatedAt(currentTime);
            wdtGoods.setUpdatedAt(currentTime);
            wdtGoodsList.add(wdtGoods);

            for (Spec spec : goods.getSpecList()) {
                final WdtGoodsSpec wdtGoodsSpec = Assembler.INST.apiGoodsSpecToPo(spec);
                wdtGoodsSpec.setGoodsId(wdtGoods.getGoodsId());
                wdtGoodsSpec.setCreatedAt(currentTime);
                wdtGoodsSpec.setUpdatedAt(currentTime);
                wdtGoodsSpecsList.add(wdtGoodsSpec);
            }
        }

        if (!wdtGoodsList.isEmpty()) {
            wdtGoodsMapper.saveOrUpdateBatch(wdtGoodsList);
        }
        if (!wdtGoodsSpecsList.isEmpty()) {
            wdtGoodsSpecMapper.saveOrUpdateBatch(wdtGoodsSpecsList);
        }
    }

    @Mapper(imports = {DateUtil.class, DatePattern.class})
    public interface Assembler {

        Assembler INST = Mappers.getMapper(Assembler.class);

        @Mapping(target = "id", ignore = true)
        @Mapping(target = "brandId", ignore = true)
        @Mapping(target = "specs", ignore = true)
        @Mapping(target = "goodsModified", expression = "java(DateUtil.parseCompatibility(goods.getGoodsModified()))")
        @Mapping(target = "goodsCreated", expression = "java(DateUtil.parseCompatibility(goods.getGoodsCreated()))")
        @Mapping(target = "createdUid", ignore = true)
        @Mapping(target = "createdAt", ignore = true)
        @Mapping(target = "updatedUid", ignore = true)
        @Mapping(target = "updatedAt", ignore = true)
        @Mapping(target = "isDel", ignore = true)
        WdtGoods apiGoodsToPo(Goods goods);

        @Mapping(target = "id", ignore = true)
        @Mapping(target = "customPrice2", ignore = true)
        @Mapping(target = "customPrice1", ignore = true)
        @Mapping(target = "specModified", expression = "java(DateUtil.parseCompatibility(spec.getSpecModified()))")
        @Mapping(target = "specCreated", expression = "java(DateUtil.parseCompatibility(spec.getSpecCreated()))")
        @Mapping(target = "createdUid", ignore = true)
        @Mapping(target = "createdAt", ignore = true)
        @Mapping(target = "updatedUid", ignore = true)
        @Mapping(target = "updatedAt", ignore = true)
        @Mapping(target = "isDel", ignore = true)
        WdtGoodsSpec apiGoodsSpecToPo(Goods.Spec spec);
    }

}
