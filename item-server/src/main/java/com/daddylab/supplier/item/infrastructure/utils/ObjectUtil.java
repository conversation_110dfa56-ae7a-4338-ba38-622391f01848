package com.daddylab.supplier.item.infrastructure.utils;


import cn.hutool.core.bean.BeanDesc;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.PropDesc;
import cn.hutool.core.collection.CollectionUtil;

import java.util.Collection;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2022/1/5
 */
public class ObjectUtil extends cn.hutool.core.util.ObjectUtil {

    public static <T, R> R safeGet(T object, Function<T, R> function) {
        return safeGet(object, function, null);
    }

    public static <T, R> R safeGet(T object, Function<T, R> function, R defaultValue) {
        if (Objects.isNull(object)) {
            return defaultValue;
        }
        return function.apply(object);
    }

    /**
     * @param obj     判定目标
     * @param lenient 是否使用宽松的判定条件（数字为零是算作空、字符串为空串时算空）
     * @return 是否为空
     */
    public static boolean isEmpty(Object obj, Boolean lenient) {
        if (lenient && obj instanceof Number) {
            return NumberUtil.isZeroOrNull(obj);
        } else if (lenient && obj instanceof CharSequence) {
            return StringUtil.isBlank((CharSequence) obj);
        } else {
            return ObjectUtil.isEmpty(obj);
        }
    }

    /**
     * @param obj     判定目标
     * @param lenient 是否使用宽松的判定条件（数字为零是算作空、字符串为空串时算空）
     * @return 是否不为空
     */
    public static boolean isNotEmpty(Object obj, Boolean lenient) {
        return !isEmpty(obj, lenient);
    }

    public static <T, V> void setValueIfNotEmpty(
            T object, BiConsumer<T, V> setter, V value) {
        if (value instanceof CharSequence && StringUtil.isNotBlank((CharSequence) value)) {
            setter.accept(object, value);
        }
        if (value != null) {
            setter.accept(object, value);
        }
    }

    public static <T, V, P> void setValueIfNotEmpty(
            T object, BiConsumer<T, V> setter, P value, Function<P, V> valueMapping) {
        if (value instanceof CharSequence && StringUtil.isNotBlank((CharSequence) value)) {
            setter.accept(object, valueMapping.apply(value));
        }
        if (value != null) {
            setter.accept(object, valueMapping.apply(value));
        }
    }

    public static <T, V> void setValueIfNotEmpty(String description,
                                                 T object, BiConsumer<T, V> setter, V value) {
        try {
            setValueIfNotEmpty(object, setter, value);
        } catch (Exception e) {
            throw new IllegalArgumentException(String.format("参数'%s'异常", description));
        }
    }

    public static <T, V, P> void setValueIfNotEmpty(String description,
                                                    T object,
                                                    BiConsumer<T, V> setter,
                                                    P value,
                                                    Function<P, V> valueMapping) {
        try {
            setValueIfNotEmpty(object, setter, value, valueMapping);
        } catch (Exception e) {
            throw new IllegalArgumentException(String.format("参数'%s'异常:%s", description, e.getMessage()), e);
        }
    }


    public static boolean isDirty(Object obj, Collection<String> includeProperties) {
        final BeanDesc beanDesc = BeanUtil.getBeanDesc(obj.getClass());
        for (PropDesc prop : beanDesc.getProps()) {
            if (includeProperties.contains(prop.getFieldName())) {
                final Object value = prop.getValue(obj);
                if (value instanceof CharSequence) {
                    return ((CharSequence) value).length() > 0;
                }
                return value != null;
            }
        }
        return false;
    }

    public static <T, R> Optional<R> getPropertyOfFirstMember(Collection<T> collection, Function<T, R> function) {
        return Optional.ofNullable(collection)
                       .filter(CollectionUtil::isNotEmpty)
                       .flatMap(items -> items.stream().map(function).filter(Objects::nonNull).findFirst());
    }
}
