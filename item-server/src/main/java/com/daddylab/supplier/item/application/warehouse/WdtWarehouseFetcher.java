package com.daddylab.supplier.item.application.warehouse;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.setting.WarehouseAPI;
import com.daddylab.mall.wdtsdk.apiv2.setting.dto.WarehouseQueryWarehouseParams;
import com.daddylab.mall.wdtsdk.apiv2.setting.dto.WarehouseQueryWarehouseResponse;
import com.daddylab.mall.wdtsdk.apiv2.setting.dto.WarehouseQueryWarehouseResponse.Details;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.domain.dataFetch.CustomFetcher;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.config.WarehouseChangeRecipientConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatMsg;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WarehouseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWechatMsgService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig.Config;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.types.warehouse.WmsType;
import com.daddylab.supplier.item.types.warehouse.WmsTypeMap;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 旺店通仓库同步
 *
 * <AUTHOR>
 * @since 2021/04/22
 */
@Component
@Slf4j
@AllArgsConstructor
public class WdtWarehouseFetcher implements CustomFetcher {

    final private WdtGateway wdtGateway;
    final private WarehouseMapper warehouseMapper;
    final private WdtConfig wdtConfig;

    final WarehouseChangeRecipientConfig warehouseChangeRecipientConfig;
    final MsgSender msgSender;
    final IWechatMsgService iWechatMsgService;


//    public WdtWarehouseFetcher(WdtGateway wdtGateway,
//            WarehouseMapper warehouseMapper,
//            WdtConfig wdtConfig) {
//        this.wdtGateway = wdtGateway;
//        this.warehouseMapper = warehouseMapper;
//        this.wdtConfig = wdtConfig;
//    }

    @Override
    public void fetch(RunContext runContext) {
        fetch0(null, null);
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, RunContext runContext) {
        fetch0(startTime, startTime);
    }

    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.WDT_WAREHOUSE;
    }

    public void fetch0(LocalDateTime startTime, LocalDateTime endTime) {
        for (int i = 0; i < wdtConfig.getConfigs().size(); i++) {
            final Config config = wdtConfig.getConfigs().get(i);
            log.info("同步仓库：正在同步账号：{}", config.getKey());

            final WarehouseAPI api = wdtGateway.getAPI(WarehouseAPI.class, i, false);
            try {
                final WarehouseQueryWarehouseResponse countResponse = query(api, startTime, endTime,
                        1, 1, true);
                final Integer totalCount = countResponse.getTotalCount();
                if (NumberUtil.isZeroOrNull(totalCount)) {
                    continue;
                }

                int pageSize = 1000;
                int maxPage = (int) Math.ceil(totalCount / (float) pageSize);
                for (int pageNo = maxPage; pageNo > 0; pageNo--) {
                    final WarehouseQueryWarehouseResponse response = query(api, startTime, endTime,
                            pageNo, pageSize, false);
                    handleResponse(config, response);
                }
            } catch (Throwable e) {
                log.error("旺店通同步仓库异常", e);
                Alert.text(MessageRobotCode.GLOBAL,
                        "旺店通同步仓库异常:" + e.getMessage() + "，异常信息：\n" + ExceptionUtil
                                .stacktraceToString(e));
            }
        }
    }

    private WarehouseQueryWarehouseResponse query(WarehouseAPI api,
                                                  LocalDateTime startTime, LocalDateTime endTime, int pageNo, int pageSize,
                                                  boolean calcTotal)
            throws WdtErpException {
        final WarehouseQueryWarehouseParams params = new WarehouseQueryWarehouseParams();
        params.setHideDelete(0);
        if (startTime != null) {
            params.setStartTime(DateUtil.format(startTime));
        }
        if (endTime != null) {
            params.setEndTime(DateUtil.format(endTime));
        }
        final Pager pager = new Pager(pageSize, pageNo - 1, calcTotal);
        return api.queryWarehouse(params, pager);
    }

    private void handleResponse(Config config, WarehouseQueryWarehouseResponse response) {
        if (response.getDetails().isEmpty()) {
            return;
        }
        final Set<String> warehouseNos = response.getDetails().stream()
                .map(Details::getWarehouseNo).collect(
                        Collectors.toSet());
        final LambdaQueryWrapper<Warehouse> query = Wrappers
                .lambdaQuery();
        query.in(Warehouse::getNo, warehouseNos);
        final Map<String, Warehouse> warehouseMap = warehouseMapper.selectList(query)
                .stream().collect(Collectors.toMap(Warehouse::getNo, Function.identity()));
        for (Details detail : response.getDetails()) {
            boolean updateName = false;
            String updateNameLog = "";
            boolean newAdd = false;

            final String warehouseNo = detail.getWarehouseNo();
            Warehouse warehouse = warehouseMap.get(warehouseNo);
            if (warehouse == null) {
                warehouse = new Warehouse();
                warehouse.setNo(detail.getWarehouseNo());
                newAdd = true;
            } else {
                if (!warehouse.getName().equals(detail.getName())) {
                    updateName = true;
                    updateNameLog = String.format("仓库名称发生改变，%s改为%s", warehouse.getName(), detail.getName());
                }
            }
            final WmsType wmsType = WmsTypeMap.mapWmsType(detail.getType());
            warehouse.setName(detail.getName());
            warehouse.setWmsType(wmsType != null ? wmsType.getValue() : 0);
            warehouse.setContacts(detail.getContact());
            warehouse.setTel(detail.getTelno());
            warehouse.setPhone(detail.getMobile());
            warehouse.setAddress(detail.getAddress());
            warehouse.setProvince(detail.getProvince());
            warehouse.setCity(detail.getCity());
            warehouse.setDistrict(detail.getDistrict());
            warehouse.setState(detail.getIsDisabled() != null && detail.getIsDisabled() ? 2 : 1);
            warehouse.setZip(detail.getZip());
            warehouse.setRemark(detail.getRemark());
            warehouse.setAppKey(config.getKey());

            if (warehouse.getId() != null) {
                warehouseMapper.updateById(warehouse);
            } else {
                warehouseMapper.insert(warehouse);
                newAdd = true;
            }

            // 【2024-0-08 七喜】 新增仓库，消息通知指定客服：紫芋、闪电、杨洋
            if (updateName || newAdd) {
                String name = warehouseChangeRecipientConfig.getLoginName();
                if (StringUtils.isNotBlank(name)) {
                    WechatMsg wechatMsg = new WechatMsg();
                    wechatMsg.setTitle("仓库信息发生改变/新增");
                    String content = updateName ? updateNameLog : "新增仓库" + warehouse.getName();
                    wechatMsg.setContent(content);
                    wechatMsg.setState(0);
                    wechatMsg.setRecipient(name);
                    wechatMsg.setType(1);
                    String link;
                    if(SpringUtil.getActiveProfile().equals("gray")){
                        link = "https://p-gray.daddylab.com/erp/system-settings/warehouse-list/list";
                    }
                    else if(SpringUtil.getActiveProfile().equals("prod")){
                        link = "https://erp-ol.daddylab.com/erp/system-settings/warehouse-list";
                    }else{
                        link = "http://p.dlab.cn/erp/system-settings/warehouse-list/list";
                    }
                    wechatMsg.setLink(link);
                    iWechatMsgService.save(wechatMsg);
                }
            }
        }
    }

}
