package com.daddylab.supplier.item.controller.open;

import com.alibaba.cola.dto.MultiResponse;
import com.daddylab.supplier.item.application.goodsSn.OpenGoodsSnService;
import com.daddylab.supplier.item.types.goodsSn.OpenGoodsSnDto;
import com.daddylab.supplier.item.types.goodsSn.OpenGoodsSnQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/12/1
 */
@Slf4j
@RestController
@RequestMapping("/open/api/goodsSn")
@Api(value = "ERP序列号管理页面的数据", tags = "ERP序列号管理页面的数据")
public class OpenGoodsSnController {

    @Resource
    OpenGoodsSnService openGoodsSnService;

    @ApiOperation("订单查询")
    @PostMapping("/query")
    public MultiResponse<OpenGoodsSnDto> query(
            @Validated
            @RequestBody OpenGoodsSnQuery query) {
        return openGoodsSnService.query(query);
    }

}
