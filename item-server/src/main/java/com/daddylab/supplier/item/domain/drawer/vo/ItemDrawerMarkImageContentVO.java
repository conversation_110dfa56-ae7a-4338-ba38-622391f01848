package com.daddylab.supplier.item.domain.drawer.vo;

import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerMarkImageContentTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * Class  ItemDrawerForm
 *
 * @Date 2022/5/31下午4:18
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ItemDrawerMarkImageContentVO", description = "商品抽屉图片标记内容VO")
public class ItemDrawerMarkImageContentVO implements Serializable {

    @ApiModelProperty(value = "标记id")
    private Long id;

    @ApiModelProperty(value = "标注原内容")
    private String key;

    @ApiModelProperty(value = "操作类型 UPDATE-改为 DELETE-删除")
    private ItemDrawerMarkImageContentTypeEnum type;

    @ApiModelProperty(value = "标注后内容")
    private String value;

    @ApiModelProperty(value = "标注用户ID")
    private Long markUid;

    @ApiModelProperty(value = "标注用户花名")
    private String markUserNick;

    @ApiModelProperty(value = "标注用户姓名")
    private String markUserName;

    @ApiModelProperty("标注时间")
    private Long markTime;

    /**
     * 标注内容位置信息
     */
    @ApiModelProperty("标注内容位置信息")
    private String position;

    @ApiModelProperty("标注内容图片标识")
    private String pidentifier;
}
