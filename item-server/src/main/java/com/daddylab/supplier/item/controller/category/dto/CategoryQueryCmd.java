package com.daddylab.supplier.item.controller.category.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/28 2:11 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("品类分页查询请求实体")
public class CategoryQueryCmd extends Command {

    private static final long serialVersionUID = -7275754962580863538L;
    @ApiModelProperty(value = "父品类id。")
    private Long parentId;

    @ApiModelProperty(value = "品类等级。根品类level=1。必填。")
    @NotNull(message = "品类等级不得为空")
    private Integer level;

    @ApiModelProperty(value = "品类名称，模糊查询。非必填。")
    private String name;

    private Long id;

}
