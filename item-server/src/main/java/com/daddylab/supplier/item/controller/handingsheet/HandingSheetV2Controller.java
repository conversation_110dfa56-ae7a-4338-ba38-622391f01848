package com.daddylab.supplier.item.controller.handingsheet;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.handingsheet.HandingSheetV2BizService;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.domain.dto.GenericIdBody;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.CommonEnumTransMapper;
import com.daddylab.supplier.item.controller.handingsheet.dto.HandingSheetItemPageQuery;
import com.daddylab.supplier.item.controller.handingsheet.dto.HandingSheetParam;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.infrastructure.utils.HttpHeaderUtil;
import com.daddylab.supplier.item.infrastructure.utils.ObjectUtil;
import com.daddylab.supplier.item.types.handingsheet.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/22
 */
@Api(value = "盘货表相关API V2", tags = "盘货表相关API V2")
@RestController
@RequestMapping("/handing-sheet/v2")
public class HandingSheetV2Controller {

    @Autowired
    private HandingSheetV2BizService handingSheetV2BizService;
    @Autowired
    private OperateLogBizService operateLogBizService;

    @ApiOperation("商品信息分页查询")
    @GetMapping("/itemQuery")
    public PageResponse<HandingSheetItemVO> itemQuery(HandingSheetItemPageQuery query) {
        query.setCurrentUserId(UserContext.getUserId());
        query.setHasAllDataPermission(UserContext.hasPermission(GlobalConstant.HANDING_SHEET_ITEM_ALL));
        return handingSheetV2BizService.handingSheetItemQuery(query);
    }

    @ApiOperation("选择商品后带出相关信息")
    @PostMapping("/bringOut")
    public MultiResponse<HandingSheetItemVO> bringOut(@RequestBody HandingSheetItemBringOutCmd cmd) {
        return handingSheetV2BizService.bringOut(cmd);
    }

    @ApiOperation("填写SKU编码后带出相关信息")
    @GetMapping("/bringOutForSkuCode")
    @Auth(noAuth = true)
    public SingleResponse<HandingSheetItemSkuBringOutVO> bringOutForSkuCode(
            @ApiParam("SKU编码") String skuCode,
            @ApiParam("盘货表所属平台") @RequestParam(required = false) String platforms) {
        final List<Integer> platformIds = ofPlatformIds(platforms);
        return handingSheetV2BizService.bringOutForSkuCode(skuCode, platformIds);
    }

    @ApiOperation("修改平台商品ID后刷新视图")
    @PostMapping("/refreshForModifyOuterId")
    public MultiResponse<HandingSheetItemVO> refreshForModifyOuterId(@RequestBody List<HandingSheetItemVO> views) {
        return handingSheetV2BizService.refreshItemView(views);
    }


    @ApiOperation("新增或编辑盘货表")
    @PostMapping("/saveOrEdit")
    public SingleResponse<Long> saveOrEdit(@RequestBody @Valid HandingSheetParam param) {
        param.setAudit(false);
        Long handingSheetId = handingSheetV2BizService.saveOrEdit(param);
        return SingleResponse.of(handingSheetId);
    }

    @ApiOperation("确认")
    @PostMapping("/confirm")
    public Response confirm(@RequestBody @Valid HandingSheetConfirmCmd param) {
        handingSheetV2BizService.confirm(param);
        return Response.buildSuccess();
    }


    @ApiOperation("价格同步到新品库")
    @PostMapping("/syncBackPriceToNewGoods")
    public SingleResponse<Boolean> syncBackPriceToNewGoods(@Validated @RequestBody GenericIdBody<Long> body) {
        return handingSheetV2BizService.syncBackPriceToNewGoods(body.getId());
    }

    @GetMapping(value = "/excelTemplate")
    @ApiOperation("下载Excel模板")
    @Auth(noAuth = true)
    public void excelTemplate(@ApiParam("盘货表所属平台") String platforms, HttpServletResponse response) {
        final List<Integer> platformIds = ofPlatformIds(platforms);
        handingSheetV2BizService.excelTemplate(platformIds, response);
        HttpHeaderUtil.setXlsxAttachmentHeaders(response, "盘货表导入模板");
    }

    @NonNull
    private static List<Integer> ofPlatformIds(String platforms) {
        return CommonEnumTransMapper.INSTANCE.platformIdListStrToPlatformIdList(platforms);
    }

    @PostMapping(value = "/importExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("导入Excel")
    public MultiResponse<HandingSheetItemVO> importExcel(
            @ApiParam(name = "file", value = "文件", required = true) @RequestParam("file")
            MultipartFile file,
            @ApiParam("盘货表所属平台") @RequestParam(value = "platforms") String platforms,
            @ApiParam(value = "盘货表主键ID") @RequestParam(value = "id", required = false) Long id) {
        try {
            final List<Integer> platformIds = ofPlatformIds(platforms);
            return MultiResponse.of(handingSheetV2BizService.importExcel(id, platformIds, file.getInputStream()));
        } catch (IOException e) {
            throw ExceptionPlusFactory.bizException(
                    ErrorCode.FILE_UPLOAD_ERROR, "导入Excel失败，读取文件异常");
        }
    }

    @PostMapping(value = "/copyInfo")
    @ApiOperation("复制信息")
    public Response copyInfo(@RequestBody @Validated HandingSheetCopyInfoCmd cmd) {
        handingSheetV2BizService.copyInfo(cmd.getHandingSheetId(), cmd.getSourceId(), cmd.getTargetIds());
        return Response.buildSuccess();
    }

    @GetMapping(value = "/operateLogs")
    @ApiOperation("操作日志")
    public MultiResponse<OperateLog> operateLogs(
            @ApiParam("所获取操作日志对象的ID") Long targetId,
            @ApiParam(value = "日志对象类型 1:盘货表 2:盘货表商品", allowableValues = "1,2", defaultValue = "1")
            Integer type) {
        return handingSheetV2BizService.operateLogs(targetId, ObjectUtil.defaultIfNull(type, 1));
    }
}
