package com.daddylab.supplier.item.domain.drawer.enums;

import lombok.Getter;

import java.io.Serializable;
import java.util.Objects;

/**
 * Class  ItemDrawerChangeEnum
 *
 * @Date 2022/6/3上午9:13
 * <AUTHOR>
 */
@Getter
public enum ItemDrawerChangeEnum implements Serializable {
    /**
     * 字段类型
     */
    STANDARD_NAME("standardName","产品标准名"),
    TB_TITLE("tbTitle", "淘宝标题"),
    MINI_TITLE("miniTitle", "小程序标题"),
    DOU_TITLE("douTitle", "抖音标题"),
    HOME_COPY("homeCopy", "首页文案"),
    PLAN_ID("planId", "上架id"),
    IMAGES("images", "商品图片"),
    OTHER_IMAGES("otherImages", "商品图片（其他尺寸）"),
    DETAIL_IMAGES("detailImages", "详情图片"),
    SKU_IMAGES("skuImages", "规格图片"),//废弃
    ATTR_IMAGES("attrImages", "属性图片"),//新的规格图片（与商品属性关联）
    MAIN_IMAGE_VIDEO("mainImageVideo", "主图视频"),

    TITLE_ADVICE("titleAdvice", "标题建议"),
    COPY_ADVICE("copyAdvice", "文案建议"),
    MAIN_IMAGE_ADVICE("mainImageAdvice", "主图建议"),

    BUYER("buyerId", "采购负责人"),
    PRINCIPAL("principalId", "产品负责人"),
    QC("qcIds", "Qc负责人"),

    DOU_LINK("douLink", "抖音链接"),
    TB_LINK("tbLink", "淘宝链接"),
    WECHAT_LINK("wechatLink", "微信链接"),
    TYPE("type", "商品类型"),
    LIVE_VERBAL_TRICK("liveVerbalTrick", "直播话术"),
    LIVE_IMAGE("liveImage", "直播图片"),
    DOU_YIN_LINK_CONTENT("douYinLinkContent","抖音链接内容"),
    KUAI_SHOU_LINK_CONTENT("kuaiShouLinkContent","抖音链接内容"),
    MINI_RED_BOOK_LINK("miniRedBookLink","小红书链接")
    ;

    ItemDrawerChangeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
    private final String value;
    private final String desc;

    public static  ItemDrawerChangeEnum of(String value) {
        for (ItemDrawerChangeEnum itemDrawerChangeEnum : ItemDrawerChangeEnum.values()) {
            if (Objects.equals(value, itemDrawerChangeEnum.getValue())) {
                return itemDrawerChangeEnum;
            }
        }
        return null;
    }

    public static  ItemDrawerChangeEnum ofDesc(String value) {
        for (ItemDrawerChangeEnum itemDrawerChangeEnum : ItemDrawerChangeEnum.values()) {
            if (Objects.equals(value, itemDrawerChangeEnum.getDesc())) {
                return itemDrawerChangeEnum;
            }
        }
        return null;
    }
}
