package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 采购组织表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Organization implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    @TableLogic
    private Integer isDel;

    /**
     * 名称
     */
    private String name;

    /**
     * 状态。1.正常，2.停用
     */
    private Integer state;

    /**
     * 金蝶同步id
     */
    private String kingDeeId;

    /**
     * 1:收料组织。2：采购组织
     */
    private Integer type;

}
