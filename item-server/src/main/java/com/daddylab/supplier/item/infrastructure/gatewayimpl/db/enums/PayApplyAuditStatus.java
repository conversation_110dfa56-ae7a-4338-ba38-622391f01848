package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum PayApplyAuditStatus implements IEnum<Integer> {
    /**
     *
     */
    WAITING(0, "等待审核"),
    PASS(1, "审核通过"),
    REJECT(-1, "审核拒绝");


    @EnumValue
    private final Integer value;
    private final String desc;
}
