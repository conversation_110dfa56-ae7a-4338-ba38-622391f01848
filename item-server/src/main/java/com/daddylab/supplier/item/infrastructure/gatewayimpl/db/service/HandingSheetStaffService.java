package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetStaff;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

import java.util.List;

/**
 * <p>
 * 盘货表关联的职员 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
public interface HandingSheetStaffService extends IDaddyService<HandingSheetStaff> {
    /**
     * 查询
     *
     * @param handingSheetId 盘货表 ID
     * @return List<HandingSheetStaff>
     */
    List<HandingSheetStaff> listBySheetId(Long handingSheetId);

    /**
     * 删除
     *
     * @param handingSheetId 盘货表 ID
     */
    void deleteBySheetId(Long handingSheetId);
}
