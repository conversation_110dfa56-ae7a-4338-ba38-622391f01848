package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockOutOrderDetail;

import java.util.List;

/**
 * <p>
 * 退料出库明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
public interface IStockOutOrderDetailService extends IDaddyService<StockOutOrderDetail> {

    List<StockOutOrderDetail> getListByOrderId(Long stockOutOrderId);

    List<StockOutOrderDetail> getDetailByStockIdAndSku(Long stockOutOrderId, String itemSkuCode);
}
