package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetActivityEvent;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

import java.util.List;

/**
 * <p>
 * 盘货表对应的活动力度 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
public interface HandingSheetActivityEventService extends IDaddyService<HandingSheetActivityEvent> {
    /**
     * 查询盘货表的活动力度数据
     *
     * @param handingSheetId 盘货表 ID
     * @return List<HandingSheetActivityEvent>
     */
    List<HandingSheetActivityEvent> listByHandingSheetId(Long handingSheetId);

    /**
     * 删除
     *
     * @param handingSheetId 盘货表 ID
     */
    void deleteBySheetId(Long handingSheetId);
}
