package com.daddylab.supplier.item.infrastructure.kingdee.req;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Dict;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 财务会计-出纳管理-付款单
 *
 * <AUTHOR> up
 * @date 2023年11月17日 1:56 PM
 */
@Data
public class PaymentBillDto {


    /**
     * 单据类型
     * 默认为 采购业务付款单
     */
    @JsonProperty("FBillTypeID")
    private Dict type = Dict.create().set("FNUMBER", "FKDLX01_SYS");

    /**
     * 收款单位类型，默认为供应商
     */
    @JsonProperty("FRECTUNITTYPE")
    private String payeeType = "BD_Supplier";

    /**
     * 收款单位/组织。
     * "FRECTUNIT": {
     * "FNumber": ""
     * },
     * 供应商编码
     */
    @JsonProperty("FRECTUNIT")
    private Dict payeeOrg;


    /**
     * 付款组织。默认老爸电商科技有限公司
     */
    @JsonProperty("FPAYORGID")
    private Dict paymentOrg = Dict.create().set("FNumber", "103");

    /**
     * 结算组织
     */
    @JsonProperty("FSETTLEORGID")
    private Dict settlementOrg = Dict.create().set("FNumber", "103");

    /**
     * 业务日期
     * 例子。"FDATE": "2023-11-17 00:00:00",
     */
    @JsonProperty("FDATE")
    private String date = "";

    /**
     * 币别
     * 默认人民币
     */
    @JsonProperty("FCURRENCYID")
    private Dict currencyType = Dict.create().set("FNumber", "PRE001");

    /**
     * 采购组织
     * "FPURCHASEORGID": {
     * "FNumber": "103"
     * },
     */
    @JsonProperty("FPURCHASEORGID")
    private Dict purchaseOrg = Dict.create().set("FNumber", "103");

    /**
     * 往来单位类型。默认供应商
     * "FCONTACTUNITTYPE": "BD_Supplier"
     */
    @JsonProperty("FCONTACTUNITTYPE")
    private String contactOrgType = "BD_Supplier";

    /**
     * 往来单位。同付款单位
     * "FCONTACTUNIT": {
     * "FNumber": ""
     * },
     */
    @JsonProperty("FCONTACTUNIT")
    private Dict contactOrg;

    /**
     * 采购部门，默认电商。
     * BM000143 大电商部门
     * BM000176 绿色家装
     * "FPURCHASEDEPTID": {
     * "FNumber": ""
     * },
     */
    @JsonProperty("FPURCHASEDEPTID")
    private Dict purchaseDept = Dict.create().set("FNumber", "BM000143");


    /**
     * 明细
     */
    @JsonProperty("FPAYBILLENTRY")
    private List<PaymentBillDetailDto> detailList;


    public static void main(String[] args) {
        PaymentBillDto paymentBillDto = new PaymentBillDto();
        PaymentBillDetailDto paymentBillDetailDto = new PaymentBillDetailDto();
        paymentBillDto.setDetailList(ListUtil.of(paymentBillDetailDto));
        System.out.println(JsonUtil.toJson(paymentBillDto));


    }

}
