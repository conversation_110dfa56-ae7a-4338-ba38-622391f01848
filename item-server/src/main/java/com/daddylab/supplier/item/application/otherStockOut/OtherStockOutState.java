package com.daddylab.supplier.item.application.otherStockOut;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.google.common.collect.ImmutableMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/4/24
 */
@AllArgsConstructor
@Getter
public enum OtherStockOutState implements IEnum<Integer> {
    UNMAPPING(-1, "未映射"),
    ALL(0, "全部"),
    WAIT_PROCESS(1, "待处理"),
    CANCELED(2, "已取消"),
    UNCONFIRMED(3, "未确认"),
    WAIT_AUDIT(4, "待审核"),
    PICKING(5, "捡货中"),
    FINISHED(6, "已完成"),
    ;
    //旺店通其他出库业务单据状态：其他出库单状态 5: 已取消 48: 未确认 50: 待审核 65: 待处理 77: 拣货中 110: 已完成
    //系统状态 0:全部 1:待处理 2:已取消 3:编辑中 4:待审核 5:待质检 6:质检待确认 7:已完成
    public static final Map<Integer, OtherStockOutState> MAP = ImmutableMap
            .<Integer, OtherStockOutState>builder()
            .put(5, OtherStockOutState.CANCELED)
            .put(48, OtherStockOutState.UNCONFIRMED)
            .put(50, OtherStockOutState.WAIT_AUDIT)
            .put(65, OtherStockOutState.WAIT_PROCESS)
            .put(77, OtherStockOutState.PICKING)
            .put(110, OtherStockOutState.FINISHED)
            .build();
    private final Integer value;
    private final String desc;
}
