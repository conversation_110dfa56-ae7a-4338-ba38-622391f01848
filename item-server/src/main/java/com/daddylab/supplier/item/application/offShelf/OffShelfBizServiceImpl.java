package com.daddylab.supplier.item.application.offShelf;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.message.QyMsgSendService;
import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.application.offShelf.dto.*;
import com.daddylab.supplier.item.application.process.ProcessBizService;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsDownRequest;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsPrincipalsInfo;
import com.daddylab.supplier.item.application.staff.BusinessHeadConfig;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.ResponseAssert;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.domain.exportTask.ExportManager;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.staff.vo.DepartmentHeadBriefVO;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.bizLevelDivision.BizDivisionContext;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLock;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.process.*;
import com.daddylab.supplier.item.types.process.assembler.TaskAssembler;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> up
 * @date 2024年11月04日 3:39 PM
 */
@Slf4j
@Service
public class OffShelfBizServiceImpl implements OffShelfBizService {

    @Resource IOffShelfInfoService iOffShelfInfoService;
    @Resource IOffShelfItemService iOffShelfItemService;
    @Resource IOffShelfFeedbackService offShelfFeedbackService;
    @Resource ProcessBizService processBizService;
    @Resource OffShelfConfig offShelfConfig;
    @Resource QyMsgSendService qyMsgSendService;
    @Resource StaffService staffService;
    @Resource NewGoodsBizService newGoodsBizService;
    @Resource BusinessHeadConfig businessHeadConfig;
    @Resource ExportManager exportManager;
    @Resource IShopOperatorMapService shopOperatorMapService;
    @Resource IShopService shopService;
    @Resource IBizLevelDivisionService bizLevelDivisionService;
    @Resource ItemBizService itemBizService;

    @Override
    public PageResponse<OffShelfPageVO> page(OffShelfPageQuery query) {
        final PageInfo<OffShelfInfo> page =
                BizDivisionContext.invoke(
                        BizUnionTypeEnum.SPU,
                        c -> c.setBizIdRef("item.id"),
                        () -> iOffShelfInfoService.page(query));
        return ResponseFactory.ofPage(page, this::toOffShelfPageVO);
    }

    @NotNull
    private OffShelfPageVO toOffShelfPageVO(OffShelfInfo item) {
        final OffShelfPageVO offShelfPageVO = new OffShelfPageVO();
        offShelfPageVO.setId(item.getId());
        offShelfPageVO.setNo(item.getNo());
        offShelfPageVO.setStatus(item.getStatus());
        offShelfPageVO.setReasonTxt(item.getReasonTxt());
        offShelfPageVO.setReasonType(getReasonTypeEnumList(item));
        offShelfPageVO.setUrgentLevel(item.getUrgentLevel());
        getApplicant(item).ifPresent(offShelfPageVO::setApplicant);
        getApplyTime(item).ifPresent(offShelfPageVO::setApplyTime);
        offShelfPageVO.setItemList(Lists.newArrayList());
        final List<Long> operatorUids =
                Arrays.stream(item.getOperatorUid().split(","))
                        .filter(v -> !v.isEmpty())
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
        List<StaffBrief> staffBriefList = staffService.getStaffBriefList(operatorUids);
        List<OffShelfFeedback> offShelfFeedbacks =
                offShelfFeedbackService
                        .lambdaQuery()
                        .eq(OffShelfFeedback::getOffShelfInfoId, item.getId())
                        .list();
        Set<Long> processedUids =
                offShelfFeedbacks.stream()
                        .filter(v -> v.getFeedback() > 0)
                        .map(OffShelfFeedback::getProcessUid)
                        .collect(Collectors.toSet());
        staffBriefList.removeIf(v -> processedUids.contains(v.getUserId()));
        offShelfPageVO.setOperatorUsers(staffBriefList);
        offShelfPageVO.setItemList(getOffShelfItemVOList(item.getId()));
        return offShelfPageVO;
    }

    private static @NonNull Optional<Long> getApplyTime(OffShelfInfo item) {
        return Stream.of(item.getSubmitTime(), item.getCreatedAt())
                .filter(v -> v != null && v > 0)
                .findFirst();
    }

    private @NonNull Optional<StaffBrief> getApplicant(OffShelfInfo offShelfInfo) {
        return Stream.of(offShelfInfo.getSubmitUid(), offShelfInfo.getCreatedUid())
                .filter(v -> v != null && v > 0)
                .map(staffService::getStaffBrief)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .findFirst();
    }

    @Override
    public SingleResponse<Long> export(OffShelfPageQuery query) {
        final ExportTask exportTask =
                exportManager.export(
                        ExportTaskType.OFF_SHELF,
                        OffShelfPageExportVO.class,
                        pageIndex -> {
                            query.setPageIndex(pageIndex);
                            return ResponseFactory.ofPage(
                                    page(query),
                                    offShelfPageVO -> {
                                        final OffShelfPageExportVO offShelfPageExportVO =
                                                new OffShelfPageExportVO();
                                        offShelfPageExportVO.setId(offShelfPageVO.getId());
                                        offShelfPageExportVO.setNo(offShelfPageVO.getNo());
                                        Optional.ofNullable(offShelfPageVO.getStatus())
                                                .map(OffShelfStatus::getDesc)
                                                .ifPresent(offShelfPageExportVO::setStatus);
                                        offShelfPageExportVO.setReasonTxt(
                                                offShelfPageVO.getReasonTxt());
                                        Optional.ofNullable(offShelfPageVO.getReasonType())
                                                .map(
                                                        v ->
                                                                v.stream()
                                                                        .map(
                                                                                OffShelfReasonType
                                                                                        ::getDesc)
                                                                        .collect(
                                                                                Collectors.joining(
                                                                                        ",")))
                                                .ifPresent(offShelfPageExportVO::setReasonType);
                                        Optional.ofNullable(offShelfPageVO.getItemList())
                                                .map(
                                                        v ->
                                                                v.stream()
                                                                        .map(
                                                                                OffShelfItemVO
                                                                                        ::getItemCode)
                                                                        .collect(
                                                                                Collectors.joining(
                                                                                        "/")))
                                                .ifPresent(offShelfPageExportVO::setItemCode);
                                        Optional.ofNullable(offShelfPageVO.getUrgentLevel())
                                                .map(OffShelfUrgentLevel::getDesc)
                                                .ifPresent(offShelfPageExportVO::setUrgentLevel);
                                        Optional.ofNullable(offShelfPageVO.getApplicant())
                                                .map(StaffBrief::getNickname)
                                                .ifPresent(offShelfPageExportVO::setApplicant);
                                        offShelfPageExportVO.setApplyTime(
                                                DateUtil.format(offShelfPageVO.getApplyTime()));
                                        Optional.ofNullable(offShelfPageVO.getOperatorUsers())
                                                .map(
                                                        v ->
                                                                v.stream()
                                                                        .map(
                                                                                StaffBrief
                                                                                        ::getNickname)
                                                                        .collect(
                                                                                Collectors.joining(
                                                                                        ",")))
                                                .ifPresent(offShelfPageExportVO::setOperatorUsers);
                                        return offShelfPageExportVO;
                                    });
                        });
        return SingleResponse.of(exportTask.getId());
    }

    @Override
    public SingleResponse<OffShelfFormVO> view(Long id) {
        final OffShelfInfo offShelfInfo = getOffShelfInfo(id);

        final OffShelfFormVO offShelfFormVO = new OffShelfFormVO();
        offShelfFormVO.setId(offShelfInfo.getId());
        offShelfFormVO.setUrgentLevel(offShelfInfo.getUrgentLevel());
        offShelfFormVO.setReasonType(getReasonTypeEnumList(offShelfInfo));
        offShelfFormVO.setReasonTxt(offShelfInfo.getReasonTxt());
        offShelfFormVO.setReasonFile(
                JsonUtil.parseList(offShelfInfo.getReasonFile(), OffShelfFile.class));
        final List<OffShelfItemVO> offShelfItemVOS = getOffShelfItemVOList(offShelfInfo.getId());

        offShelfFormVO.setItemList(offShelfItemVOS);

        final List<HistoricTaskInstance> tasks =
                getProcessHisTasks(offShelfInfo.getProcessInstId());
        offShelfFormVO.setProcessInfo(getProcessNodes(offShelfInfo, offShelfItemVOS, tasks));

        final List<OffShelfFeedback> feedbacks =
                offShelfFeedbackService
                        .lambdaQuery()
                        .eq(OffShelfFeedback::getOffShelfInfoId, offShelfInfo.getId())
                        .list();
        final ArrayList<OffShelfFeedbackVO> feedbackVOList = new ArrayList<>();
        for (OffShelfFeedback feedback : feedbacks) {
            final OffShelfFeedbackVO feedbackVO = new OffShelfFeedbackVO();
            feedbackVO.setFeedbackId(feedback.getId());
            feedbackVO.setProcessUid(feedback.getProcessUid());
            feedbackVO.setProcessUser(StaffAssembler.INST.toStaffBrief(feedback.getProcessUid()));
            final Optional<HistoricTaskInstance> task =
                    tasks.stream().filter(v -> v.getId().equals(feedback.getTaskId())).findFirst();
            task.ifPresent(v -> feedbackVO.setProcessTime(DateUtil.toTime(v.getEndTime())));
            feedbackVO.setTaskId(feedback.getTaskId());
            feedbackVO.setFeedback(feedback.getFeedback());
            feedbackVO.setLink(feedback.getLink());
            feedbackVO.setRemark(feedback.getRemark());
            feedbackVOList.add(feedbackVO);
        }
        offShelfFormVO.setFeedbackList(feedbackVOList);
        final OffShelfApproveInfoVO approveInfo = new OffShelfApproveInfoVO();
        approveInfo.setSelectAllShop(offShelfInfo.getSelectAll());
        approveInfo.setAdvice(
                offShelfInfo.getStatus() == OffShelfStatus.WAIT_HANDLE
                                || offShelfInfo.getStatus() == OffShelfStatus.FINISH
                        ? ProcessAdvice.AGREE
                        : (offShelfInfo.getStatus() == OffShelfStatus.REFUSE
                                ? ProcessAdvice.REFUSE
                                : ProcessAdvice.NONE));
        final List<Long> selectShopIds =
                Optional.ofNullable(offShelfInfo.getShopId())
                        .filter(x -> !"*".equals(x))
                        .map(
                                x ->
                                        Arrays.stream(x.split(","))
                                                .map(Long::parseLong)
                                                .collect(Collectors.toList()))
                        .orElseGet(Collections::emptyList);
        final List<ShopOperatorMap> shopOperators =
                shopOperatorMapService.listByShopIds(selectShopIds);
        final List<Shop> selectShops =
                !selectShopIds.isEmpty()
                        ? shopService.listByIds(selectShopIds)
                        : Collections.emptyList();
        final List<Long> selectShopOperatorIds =
                shopOperators.stream()
                        .map(ShopOperatorMap::getOperatorUid)
                        .collect(Collectors.toList());
        final List<StaffBrief> selectShopStaffInfos =
                staffService.getStaffBriefList(selectShopOperatorIds);
        final ArrayList<OffShelfApproveInfoVO.ShopInfoVO> shopInfoVOS = new ArrayList<>();
        for (ShopOperatorMap shopOperator : shopOperators) {
            final OffShelfApproveInfoVO.ShopInfoVO shopInfoVO =
                    new OffShelfApproveInfoVO.ShopInfoVO();
            shopInfoVO.setShopId(shopOperator.getShopId());
            final Optional<Shop> shopOpt =
                    selectShops.stream()
                            .filter(v -> Objects.equals(v.getId(), shopOperator.getShopId()))
                            .findFirst();
            shopInfoVO.setShopName(shopOpt.map(Shop::getName).orElse("?"));
            final Optional<StaffBrief> staffBrief =
                    selectShopStaffInfos.stream()
                            .filter(
                                    v ->
                                            Objects.equals(
                                                    v.getUserId(), shopOperator.getOperatorUid()))
                            .findFirst();
            shopInfoVO.setOperatorNick(staffBrief.map(StaffBrief::getNickname).orElse("?"));
            shopInfoVO.setOperatorUid(shopOperator.getOperatorUid());
            shopInfoVOS.add(shopInfoVO);
        }
        approveInfo.setShops(shopInfoVOS);
        final List<Long> selectOperatorUids =
                Arrays.stream(offShelfInfo.getOperatorUid().split(","))
                        .filter(v -> !v.isEmpty())
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
        final List<StaffBrief> selectOperators = staffService.getStaffBriefList(selectOperatorUids);
        approveInfo.setOperators(
                selectOperators.stream()
                        .map(
                                v -> {
                                    OffShelfApproveInfoVO.OperatorInfoVO operatorVO =
                                            new OffShelfApproveInfoVO.OperatorInfoVO();
                                    operatorVO.setOperatorUid(v.getUserId());
                                    operatorVO.setOperatorNick(v.getNickname());
                                    return operatorVO;
                                })
                        .collect(Collectors.toList()));
        approveInfo.setApproveRemark(offShelfInfo.getApproveRemark());
        offShelfFormVO.setApproveInfo(approveInfo);
        offShelfFormVO.setStatus(offShelfInfo.getStatus());

        getApplicant(offShelfInfo).ifPresent(offShelfFormVO::setApplicant);
        getApplyTime(offShelfInfo).ifPresent(offShelfFormVO::setApplyTime);
        offShelfFormVO.setNo(offShelfInfo.getNo());

        return SingleResponse.of(offShelfFormVO);
    }

    private OffShelfProcessInfoVO getProcessNodes(
            OffShelfInfo offShelfInfo,
            List<OffShelfItemVO> offShelfItemVOS,
            List<HistoricTaskInstance> tasks) {
        final OffShelfProcessInfoVO offShelfProcessInfoVO = new OffShelfProcessInfoVO();
        final List<HistoricTaskInstance> toApproveTasks =
                tasks.stream().filter(v -> "待审核".equals(v.getName())).collect(Collectors.toList());
        final ArrayList<ProcessNode> processNodes = new ArrayList<>();
        final ProcessNode submitNode = new ProcessNode("待提交");
        submitNode.setCompleteTime(offShelfInfo.getSubmitTime());
        submitNode.setIsCurrentNode(offShelfInfo.getStatus() == OffShelfStatus.WAIT_SUBMIT);
        submitNode.setOk(
                offShelfInfo.getStatus().getValue() > OffShelfStatus.WAIT_SUBMIT.getValue());
        if (offShelfInfo.getSubmitUid() != null && offShelfInfo.getSubmitUid() > 0) {
            staffService
                    .getStaffBrief(offShelfInfo.getSubmitUid())
                    .map(Collections::singletonList)
                    .ifPresent(submitNode::setProcessors);
        }
        processNodes.add(submitNode);

        final ProcessNode approveNode = new ProcessNode("待审核", getProcessTasks(toApproveTasks));
        approveNode.setIsCurrentNode(offShelfInfo.getStatus() == OffShelfStatus.WAIT_AUDIT);
        approveNode.setOk(
                offShelfInfo.getStatus().getValue() > OffShelfStatus.WAIT_AUDIT.getValue());
        approveNode.setProcessors(
                staffService.getStaffBriefList(getApproveUserIds(offShelfItemVOS)));
        processNodes.add(approveNode);
        final ProcessNode processNode = new ProcessNode("待处理");
        final List<HistoricTaskInstance> toProcessTasks =
                tasks.stream().filter(v -> "待处理".equals(v.getName())).collect(Collectors.toList());
        processNode.setTasks(getProcessTasks(toProcessTasks));
        processNode.setIsCurrentNode(offShelfInfo.getStatus() == OffShelfStatus.WAIT_HANDLE);
        processNode.setOk(
                offShelfInfo.getStatus().getValue() > OffShelfStatus.WAIT_HANDLE.getValue());
        processNodes.add(processNode);

        if (offShelfInfo.getStatus() == OffShelfStatus.REFUSE) {
            final ProcessNode refuseNode = new ProcessNode("已拒绝");
            refuseNode.setIsCurrentNode(true);
            refuseNode.setOk(true);
            processNodes.add(refuseNode);
        } else if (offShelfInfo.getStatus() == OffShelfStatus.RETRACT) {
            final ProcessNode revokeNode = new ProcessNode("已撤回");
            revokeNode.setIsCurrentNode(true);
            revokeNode.setOk(true);
            processNodes.add(revokeNode);
        } else {
            final ProcessNode finishNode = new ProcessNode("已完成");
            finishNode.setIsCurrentNode(offShelfInfo.getStatus() == OffShelfStatus.FINISH);
            finishNode.setOk(
                    Objects.equals(
                            offShelfInfo.getStatus().getValue(), OffShelfStatus.FINISH.getValue()));
            processNodes.add(finishNode);
        }

        offShelfProcessInfoVO.setNodes(processNodes);
        final ArrayList<ProcessActivity> activities = new ArrayList<>();
        for (HistoricTaskInstance task : tasks) {
            final ProcessActivity processActivity = TaskAssembler.INST.historicTaskToActivity(task);
            activities.add(processActivity);
        }
        offShelfProcessInfoVO.setActivities(activities);
        return offShelfProcessInfoVO;
    }

    @NonNull
    private static List<OffShelfReasonType> getReasonTypeEnumList(OffShelfInfo offShelfInfo) {
        return StringUtil.split(offShelfInfo.getReasonType(), ",").stream()
                .map(v -> IEnum.getEnumByValue(OffShelfReasonType.class, v))
                .collect(Collectors.toList());
    }

    @NonNull
    private OffShelfInfo getOffShelfInfo(Long id) {
        return Objects.requireNonNull(iOffShelfInfoService.getById(id), "下架流程不存在");
    }

    private List<HistoricTaskInstance> getProcessHisTasks(String processInstId) {
        if (StringUtil.isBlank(processInstId)) {
            return Collections.emptyList();
        }
        return processBizService.historicTasksByProcInstId(processInstId);
    }

    @NonNull
    private static List<ProcessTask> getProcessTasks(
            List<HistoricTaskInstance> historicTaskInstances) {
        return historicTaskInstances.stream()
                .map(TaskAssembler.INST::historicTaskToProcessTask)
                .collect(Collectors.toList());
    }

    @DistributedLock
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SingleResponse<Long> save(OffShelfFormCmd cmd) {
        Long f = Objects.isNull(cmd.getId()) ? add(cmd) : update(cmd);
        return SingleResponse.of(f);
    }

    public Long add(OffShelfFormCmd cmd) {
        OffShelfInfo info = new OffShelfInfo();
        info.setUrgentLevel(cmd.getUrgentLevel());
        info.setReasonType(
                cmd.getReasonType().stream()
                        .map(OffShelfReasonType::getValue)
                        .map(Objects::toString)
                        .collect(Collectors.joining(",")));
        info.setReasonTxt(cmd.getReasonTxt());
        info.setReasonFile(JsonUtil.toJson(cmd.getReasonFile()));
        Long maxId = iOffShelfInfoService.getMaxId();
        long noSuffix = Objects.isNull(maxId) ? 1 : maxId + 1;
        info.setNo("SPXJ" + DateUtil.format(LocalDateTime.now(), "yyyy") + noSuffix);
        info.setStatus(OffShelfStatus.WAIT_SUBMIT);
        info.setOperatorUid("");
        info.setSelectAll(true);
        iOffShelfInfoService.save(info);

        final Long id = info.getId();

        final List<OffShelfItem> itemList =
                cmd.getItemCmdList().stream()
                        .map(
                                val -> {
                                    OffShelfItem item = new OffShelfItem();
                                    item.setOffShelfInfoId(id);
                                    item.setItemId(val.getItemId());
                                    item.setItemCode(val.getItemCode());
                                    item.setRemark(val.getRemark());
                                    return item;
                                })
                        .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(itemList)) {
            iOffShelfItemService.saveBatch(itemList);
        }
        operateLogDomainService.addOperatorLog(
                UserContext.getUserId(), OperateLogTarget.OFF_SHELF, info.getId(), "新增商品下架流程");

        return info.getId();
    }

    @DistributedLock
    public Long update(OffShelfFormCmd cmd) {
        final ArrayList<String> updateProperties = new ArrayList<>();
        final ArrayList<String> goodsLog = new ArrayList<>();
        final OffShelfInfo info = iOffShelfInfoService.getById(cmd.getId());
        if (cmd.getUrgentLevel() != info.getUrgentLevel()) {
            info.setUrgentLevel(cmd.getUrgentLevel());
            updateProperties.add("紧急程度");
        }
        final String reasonType =
                cmd.getReasonType().stream()
                        .map(OffShelfReasonType::getValue)
                        .map(Objects::toString)
                        .collect(Collectors.joining(","));
        if (!info.getReasonType().equals(reasonType)) {
            info.setReasonType(reasonType);
            updateProperties.add("下架原因");
        }
        if (!info.getReasonTxt().equals(cmd.getReasonTxt())) {
            info.setReasonTxt(cmd.getReasonTxt());
            updateProperties.add("下架理由");
        }
        final String reasonFile = JsonUtil.toJson(cmd.getReasonFile());
        if (!info.getReasonFile().equals(reasonFile)) {
            info.setReasonFile(reasonFile);
        }
        // 如果是拒绝或者撤回，则重新提交
        if (info.getStatus() == OffShelfStatus.REFUSE
                || info.getStatus() == OffShelfStatus.RETRACT) {
            info.setStatus(OffShelfStatus.WAIT_SUBMIT);
        }
        iOffShelfInfoService.updateById(info);

        final List<Long> itemIdList =
                cmd.getItemCmdList().stream()
                        .map(OffShelfItemCmd::getId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
        final List<OffShelfItem> itemList =
                iOffShelfItemService
                        .lambdaQuery()
                        .eq(OffShelfItem::getOffShelfInfoId, cmd.getId())
                        .list();
        final List<Long> oldIdList =
                itemList.stream().map(OffShelfItem::getId).collect(Collectors.toList());
        final Collection<Long> needRemoveIds = CollectionUtils.subtract(oldIdList, itemIdList);
        if (CollUtil.isNotEmpty(needRemoveIds)) {
            iOffShelfItemService.removeByIdsWithTime(needRemoveIds);
            for (Long needRemoveId : needRemoveIds) {
                for (OffShelfItem offShelfItem : itemList) {
                    if (needRemoveId.equals(offShelfItem.getId())) {
                        goodsLog.add("移除商品'" + offShelfItem.getItemCode() + "'");
                        break;
                    }
                }
            }
        }
        for (OffShelfItem offShelfItem : itemList) {
            for (OffShelfItemCmd itemCmd : cmd.getItemCmdList()) {
                if (offShelfItem.getId().equals(itemCmd.getId())) {
                    if (Objects.equals(offShelfItem.getRemark(), itemCmd.getRemark())) {
                        continue;
                    }
                    offShelfItem.setRemark(offShelfItem.getRemark());
                    goodsLog.add("修改商品'" + offShelfItem.getItemCode() + "'备注");
                    break;
                }
            }
            iOffShelfItemService.updateBatchById(itemList);
        }

        final List<OffShelfItemCmd> needAddCmdList =
                cmd.getItemCmdList().stream()
                        .filter(val -> Objects.isNull(val.getId()))
                        .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(needAddCmdList)) {
            final List<OffShelfItem> needAddItemList =
                    needAddCmdList.stream()
                            .map(
                                    val -> {
                                        OffShelfItem item = new OffShelfItem();
                                        item.setOffShelfInfoId(info.getId());
                                        item.setItemId(val.getItemId());
                                        item.setItemCode(val.getItemCode());
                                        item.setRemark(val.getRemark());
                                        goodsLog.add("新增商品'" + item.getItemCode() + "'");
                                        return item;
                                    })
                            .collect(Collectors.toList());
            iOffShelfItemService.saveBatch(needAddItemList);
        }

        if (!updateProperties.isEmpty() || !goodsLog.isEmpty()) {
            final ArrayList<String> logs = new ArrayList<>();
            if (!updateProperties.isEmpty()) {
                logs.add("修改了" + String.join("、", updateProperties));
            }
            if (!goodsLog.isEmpty()) {
                logs.addAll(goodsLog);
            }
            operateLogDomainService.addOperatorLog(
                    UserContext.getUserId(),
                    OperateLogTarget.OFF_SHELF,
                    cmd.getId(),
                    String.join("，", logs));
        }

        return info.getId();
    }

    @Override
    public SingleResponse<Boolean> submit(Long id) {
        final OffShelfInfo offShelfInfo = getOffShelfInfo(id);
        if (offShelfInfo.getStatus() != OffShelfStatus.WAIT_SUBMIT
                && offShelfInfo.getStatus() != OffShelfStatus.RETRACT) {
            throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "当前状态无法提交");
        }
        final Long userId = UserContext.getUserId();
        final String userIdString = userId.toString();
        final HashMap<String, Object> variables = new HashMap<>();
        final List<OffShelfItemVO> offShelfItemVOS = getOffShelfItemVOList(id);
        if (offShelfItemVOS.isEmpty()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "请添加下架商品");
        }
        offShelfInfo.setStatus(OffShelfStatus.WAIT_AUDIT);
        offShelfInfo.setSubmitTime(DateUtil.currentTime());
        offShelfInfo.setSubmitUid(userId);
        offShelfInfo.setApproveTime(0L);
        offShelfInfo.setApproveUid(0L);
        offShelfInfo.setApproveRemark("");
        iOffShelfInfoService.updateById(offShelfInfo);
        final List<Long> approveUserIds = getApproveUserIds(offShelfItemVOS);
        variables.put("approveUser", approveUserIds.get(0).toString());
        final SingleResponse<ProcessInstance> response =
                processBizService.create(
                        userId,
                        offShelfConfig.getProcessDefId(),
                        ProcessBusinessType.OFF_SHELF,
                        id,
                        "商品下架流程:" + id,
                        userIdString,
                        variables);
        ResponseAssert.assertJust(response);
        offShelfInfo.setProcessInstId(response.getData().getId());
        iOffShelfInfoService.updateById(offShelfInfo);
        return SingleResponse.of(true);
    }

    @Override
    public SingleResponse<Boolean> delete(OffShelfDeleteCmd cmd) {
        final OffShelfInfo offShelfInfo = getOffShelfInfo(cmd.getId());
        if (offShelfInfo.getStatus() != OffShelfStatus.WAIT_SUBMIT
                && offShelfInfo.getStatus() != OffShelfStatus.RETRACT) {
            throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "当前状态无法删除");
        }
        iOffShelfInfoService.removeByIdWithTime(cmd.getId());
        operateLogDomainService.addOperatorLog(
                UserContext.getUserId(), OperateLogTarget.OFF_SHELF, offShelfInfo.getId(), "删除");
        return SingleResponse.of(true);
    }

    @Override
    public SingleResponse<Boolean> revoke(OffShelfRevokeCmd cmd) {
        final OffShelfInfo offShelfInfo = getOffShelfInfo(cmd.getId());
        if (offShelfInfo.getStatus() == OffShelfStatus.WAIT_AUDIT) {
            offShelfInfo.setStatus(OffShelfStatus.RETRACT);
            iOffShelfInfoService.updateById(offShelfInfo);
            if (StringUtil.isNotBlank(offShelfInfo.getProcessInstId())) {
                processBizService.terminal(
                        UserContext.getUserId(), offShelfInfo.getProcessInstId(), "撤回");
            }
            removeFeedbacks(offShelfInfo.getId());
            operateLogDomainService.addOperatorLog(
                    UserContext.getUserId(),
                    OperateLogTarget.OFF_SHELF,
                    offShelfInfo.getId(),
                    "撤回");
            return SingleResponse.of(true);
        }
        if (offShelfInfo.getStatus() == OffShelfStatus.WAIT_HANDLE) {
            if (StringUtil.isNotBlank(offShelfInfo.getProcessInstId())) {
                processBizService.moveTo(
                        UserContext.getUserId(), offShelfInfo.getProcessInstId(), "待审核");
            }
            offShelfInfo.setStatus(OffShelfStatus.WAIT_AUDIT);
            iOffShelfInfoService.updateById(offShelfInfo);
            removeFeedbacks(offShelfInfo.getId());
            operateLogDomainService.addOperatorLog(
                    UserContext.getUserId(),
                    OperateLogTarget.OFF_SHELF,
                    offShelfInfo.getId(),
                    "撤回审核");
            noticeAuditRevoked(offShelfInfo.getId());
            return SingleResponse.of(true);
        }
        throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "当前状态无法撤回");
    }

    private void removeFeedbacks(Long offShelfInfoId) {
        List<OffShelfFeedback> offShelfFeedbacks =
                offShelfFeedbackService
                        .lambdaQuery()
                        .eq(OffShelfFeedback::getOffShelfInfoId, offShelfInfoId)
                        .list();
        if (!offShelfFeedbacks.isEmpty()) {
            offShelfFeedbackService.removeByIds(
                    offShelfFeedbacks.stream()
                            .map(OffShelfFeedback::getId)
                            .collect(Collectors.toList()));
        }
    }

    @NonNull
    private List<Long> getApproveUserIds(List<OffShelfItemVO> offShelfItemVOS) {
        if (offShelfItemVOS.isEmpty()) {
            return Collections.emptyList();
        }
        final Set<DivisionLevelValueEnum> businessLines =
                offShelfItemVOS.stream()
                        .filter(v -> v.getCorpBizType() != null)
                        .flatMap(v -> v.getCorpBizType().stream().map(CorpBizTypeDTO::getCorpType))
                        .map(DivisionLevelValueEnum::valueOf)
                        .collect(Collectors.toSet());
        final ArrayList<Long> approveUserIds = new ArrayList<>();
        for (DivisionLevelValueEnum businessLineEnum : businessLines) {
            final List<Long> operationHeadUserIds =
                    businessHeadConfig.getOperationHeads().get(businessLineEnum.name());
            if (CollUtil.isNotEmpty(operationHeadUserIds)) {
                approveUserIds.addAll(operationHeadUserIds);
            }
        }
        if (approveUserIds.isEmpty()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.PROCESS_ERROR, "未配置审批人");
        }
        return approveUserIds;
    }

    @Override
    @DSTransactional
    public SingleResponse<Boolean> approve(OffShelfApproveCmd cmd) {
        if (cmd.getAdvice() == ProcessAdvice.NONE) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "请给出审批意见");
        }
        final OffShelfInfo offShelfInfo = getOffShelfInfo(cmd.getId());
        if (offShelfInfo.getStatus() != OffShelfStatus.WAIT_AUDIT) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前状态无法审批");
        }
        final String processInstId = offShelfInfo.getProcessInstId();
        final Task task =
                processBizService.tasksByProcInstId(processInstId).stream()
                        .filter(v -> "待审核".equals(v.getName()))
                        .findFirst()
                        .orElseThrow(
                                () ->
                                        ExceptionPlusFactory.bizException(
                                                ErrorCode.OPERATION_REJECT, "审批任务已完成"));

        offShelfInfo.setApproveRemark(cmd.getRemark());
        offShelfInfo.setApproveUid(UserContext.getUserId());
        offShelfInfo.setApproveTime(DateUtil.currentTime());
        offShelfInfo.setSelectAll(cmd.getSelectAllShop());

        final ArrayList<OffShelfFeedback> offShelfFeedbacks = new ArrayList<>();
        if (cmd.getAdvice() == ProcessAdvice.AGREE) {
            final List<Long> shopIds =
                    Optional.ofNullable(cmd.getShopIds()).orElse(Collections.emptyList()).stream()
                            .filter(NumberUtil::isPositive)
                            .collect(Collectors.toList());
            offShelfInfo.setShopId(
                    shopIds.stream().map(Objects::toString).collect(Collectors.joining(",")));
            final Set<Long> operatorIds =
                    Optional.ofNullable(cmd.getOperatorUid())
                            .orElseGet(Collections::emptyList)
                            .stream()
                            .filter(NumberUtil::isPositive)
                            .collect(Collectors.toCollection(LinkedHashSet::new));
            if (CollUtil.isEmpty(operatorIds)) {
                throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "请选择下架运营");
            }
            offShelfInfo.setOperatorUid(
                    operatorIds.stream().map(Objects::toString).collect(Collectors.joining(",")));
            iOffShelfInfoService.updateById(offShelfInfo);
            for (Long uid : operatorIds) {
                if (!NumberUtil.isPositive(uid)) {
                    continue;
                }
                final OffShelfFeedback offShelfFeedback = new OffShelfFeedback();
                offShelfFeedback.setOffShelfInfoId(offShelfInfo.getId());
                offShelfFeedback.setProcessUid(uid);
                offShelfFeedbacks.add(offShelfFeedback);
            }
            offShelfFeedbackService.saveBatch(offShelfFeedbacks);
        } else {
            iOffShelfInfoService.updateById(offShelfInfo);
        }

        final HashMap<String, Object> variables = new HashMap<>();
        final List<Long> feedbackIds =
                offShelfFeedbacks.stream()
                        .map(OffShelfFeedback::getId)
                        .collect(Collectors.toList());
        variables.put("processBindingIds", feedbackIds);
        variables.put("approval", cmd.getAdvice().getValue());
        processBizService.forceComplete(UserContext.getUserId(), task.getId(), null, variables);
        operateLogDomainService.addOperatorLog(
                UserContext.getUserId(),
                OperateLogTarget.OFF_SHELF,
                offShelfInfo.getId(),
                "审核完成:" + cmd.getAdvice().getDesc());
        return SingleResponse.of(true);
    }

    @Override
    public SingleResponse<Map<Long, String>> batchApprove(OffShelfBatchApproveCmd cmd) {
        HashMap<Long, String> results = new HashMap<>();
        for (Long id : cmd.getIds()) {
            OffShelfApproveCmd offShelfApproveCmd = new OffShelfApproveCmd();
            offShelfApproveCmd.setId(id);
            offShelfApproveCmd.setAdvice(cmd.getAdvice());
            offShelfApproveCmd.setRemark(cmd.getRemark());
            offShelfApproveCmd.setSelectAllShop(cmd.getSelectAllShop());
            offShelfApproveCmd.setShopIds(cmd.getShopIds());
            offShelfApproveCmd.setOperatorUid(cmd.getOperatorUid());
            try {
                approve(offShelfApproveCmd);
                results.put(id, "操作成功");
            } catch (Exception e) {
                results.put(id, "操作失败");
                log.error("[商品下架-批量审核]审核异常 id={}", id, e);
            }
        }
        return SingleResponse.of(results);
    }

    @Resource OperateLogDomainService operateLogDomainService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<Boolean> processFeedback(OffShelfProcessFeedbackCmd cmd) {
        final OffShelfFeedback feedback = offShelfFeedbackService.getById(cmd.getFeedbackId());
        if (feedback == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "反馈异常");
        }
        if (feedback.getFeedback() != 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "反馈已完成");
        }
        final String taskId = feedback.getTaskId();
        if (StringUtil.isBlank(taskId)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "反馈异常，没有绑定流程任务");
        }
        feedback.setFeedback(cmd.getFeedback());
        feedback.setLink(cmd.getLink());
        feedback.setRemark(cmd.getRemark());
        offShelfFeedbackService.updateById(feedback);
        operateLogDomainService.addOperatorLog(
                UserContext.getUserId(),
                OperateLogTarget.OFF_SHELF,
                feedback.getOffShelfInfoId(),
                "处理完成，已提交反馈");

        processBizService
                .task(taskId)
                .ifPresent(
                        task -> {
                            processBizService.forceComplete(
                                    UserContext.getUserId(), task.getId(), null);
                        });
        return SingleResponse.of(true);
    }

    @Override
    public MultiResponse<OffShelfBatchProcessFeedbackItem> batchProcessGetItems(
            OffShelfBatchProcessGetItemsCmd cmd) {
        List<OffShelfFeedback> offShelfFeedbacks =
                offShelfFeedbackService
                        .lambdaQuery()
                        .in(OffShelfFeedback::getOffShelfInfoId, cmd.getIds())
                        .eq(OffShelfFeedback::getProcessUid, cmd.getCurrentUserId())
                        .list();
        if (offShelfFeedbacks.isEmpty()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "没有需要处理的反馈");
        }
        List<Long> offShelfInfoIds =
                offShelfFeedbacks.stream()
                        .map(OffShelfFeedback::getOffShelfInfoId)
                        .collect(Collectors.toList());
        List<OffShelfInfo> offShelfInfos = iOffShelfInfoService.listByIds(offShelfInfoIds);
        ArrayList<OffShelfBatchProcessFeedbackItem> offShelfBatchProcessFeedbackItems =
                new ArrayList<>();
        for (OffShelfFeedback offShelfFeedback : offShelfFeedbacks) {
            for (OffShelfInfo offShelfInfo : offShelfInfos) {
                if (offShelfFeedback.getOffShelfInfoId().equals(offShelfInfo.getId())) {
                    OffShelfBatchProcessFeedbackItem offShelfBatchProcessFeedbackItem =
                            new OffShelfBatchProcessFeedbackItem();
                    offShelfBatchProcessFeedbackItem.setFeedbackId(offShelfFeedback.getId());
                    offShelfBatchProcessFeedbackItem.setOffShelfId(offShelfInfo.getId());
                    offShelfBatchProcessFeedbackItem.setNo(offShelfInfo.getNo());
                    offShelfBatchProcessFeedbackItem.setStatus(offShelfInfo.getStatus());
                    offShelfBatchProcessFeedbackItem.setReasonTxt(offShelfInfo.getReasonTxt());
                    offShelfBatchProcessFeedbackItem.setReasonType(
                            getReasonTypeEnumList(offShelfInfo));
                    offShelfBatchProcessFeedbackItem.setUrgentLevel(offShelfInfo.getUrgentLevel());
                    offShelfBatchProcessFeedbackItem.setItemList(
                            getOffShelfItemVOList(offShelfInfo.getId()));
                    offShelfBatchProcessFeedbackItem.setLink("");
                    offShelfBatchProcessFeedbackItems.add(offShelfBatchProcessFeedbackItem);
                    break;
                }
            }
        }
        return MultiResponse.of(offShelfBatchProcessFeedbackItems);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<Boolean> batchProcessFeedback(OffShelfBatchProcessFeedbackCmd cmd) {
        for (OffShelfBatchProcessFeedbackItem offShelfBatchProcessFeedbackItem :
                cmd.getItemList()) {
            OffShelfProcessFeedbackCmd feedbackCmd = new OffShelfProcessFeedbackCmd();
            feedbackCmd.setFeedbackId(offShelfBatchProcessFeedbackItem.getFeedbackId());
            feedbackCmd.setFeedback(cmd.getFeedback());
            feedbackCmd.setLink(offShelfBatchProcessFeedbackItem.getLink());
            feedbackCmd.setRemark(cmd.getRemark());
            processFeedback(feedbackCmd);
        }
        return SingleResponse.of(true);
    }

    public List<StaffBrief> getReceivers(
            OffShelfInfo offShelfInfo, List<OffShelfItemVO> offShelfItemVos) {
        // 小高/惊蛰，发起人主管及经理、采购及采购主管、技术、技术主管、技术经理（方方）、商品业务线对应经理（电商/商家入驻-温蒂，绿色家装-明丽）、魏老爸
        final Set<StaffBrief> receivers = new LinkedHashSet<>();
        final Long createdUid = offShelfInfo.getCreatedUid();
        final StaffBrief applicant =
                staffService
                        .getStaffBrief(createdUid)
                        .orElseThrow(
                                () ->
                                        ExceptionPlusFactory.bizException(
                                                ErrorCode.BUSINESS_OPERATE_ERROR, "申请人用户信息异常"));
        receivers.add(applicant);
        final DepartmentHeadBriefVO staffDepartmentHeadBriefVO =
                staffService.getStaffDepartmentHeadBriefVO(createdUid);
        receivers.add(staffDepartmentHeadBriefVO.getLeader());
        receivers.add(staffDepartmentHeadBriefVO.getManager());
        final List<Long> itemIds =
                offShelfItemVos.stream()
                        .map(OffShelfItemVO::getItemId)
                        .collect(Collectors.toList());
        final MultiResponse<NewGoodsPrincipalsInfo> newGoodsPrincipalsInfoBatch =
                newGoodsBizService.getNewGoodsPrincipalsInfoBatch(itemIds);
        ResponseAssert.assertJust(newGoodsPrincipalsInfoBatch);
        final List<NewGoodsPrincipalsInfo> newGoodsPrincipalsInfo =
                newGoodsPrincipalsInfoBatch.getData();
        for (NewGoodsPrincipalsInfo principalsInfo : newGoodsPrincipalsInfo) {
            final List<StaffBrief> buyerUsers = principalsInfo.getBuyerUsers();
            if (CollUtil.isNotEmpty(buyerUsers)) {
                for (StaffBrief buyerUser : buyerUsers) {
                    final DepartmentHeadBriefVO theHeadBriefVO =
                            staffService.getStaffDepartmentHeadBriefVO(buyerUser.getUserId());
                    receivers.add(buyerUser);
                    receivers.add(theHeadBriefVO.getLeader());
                }
            }
            final List<StaffBrief> qcUsers = principalsInfo.getQcUsers();
            if (CollUtil.isNotEmpty(qcUsers)) {
                for (StaffBrief qcUser : qcUsers) {
                    final DepartmentHeadBriefVO theHeadBriefVO =
                            staffService.getStaffDepartmentHeadBriefVO(qcUser.getUserId());
                    receivers.add(qcUser);
                    receivers.add(theHeadBriefVO.getLeader());
                    receivers.add(theHeadBriefVO.getManager());
                }
            }
        }
        final Set<DivisionLevelValueEnum> businessLines =
                offShelfItemVos.stream()
                        .filter(v -> v.getCorpBizType() != null)
                        .flatMap(v -> v.getCorpBizType().stream().map(CorpBizTypeDTO::getCorpType))
                        .map(DivisionLevelValueEnum::valueOf)
                        .collect(Collectors.toSet());
        for (DivisionLevelValueEnum businessLineEnum : businessLines) {
            final List<Long> managerUserIds =
                    businessHeadConfig.getBusinessLineManagers().get(businessLineEnum.name());
            if (CollUtil.isNotEmpty(managerUserIds)) {
                receivers.addAll(staffService.getStaffBriefList(managerUserIds));
            }
            final List<Long> operationHeadUserIds =
                    businessHeadConfig.getOperationHeads().get(businessLineEnum.name());
            if (CollUtil.isNotEmpty(operationHeadUserIds)) {
                final List<StaffBrief> operationHeadUsers =
                        staffService.getStaffBriefList(operationHeadUserIds);
                receivers.addAll(operationHeadUsers);
            }
        }
        staffService.getStaffBrief(businessHeadConfig.getBoss()).ifPresent(receivers::add);
        return new ArrayList<>(receivers);
    }

    @Override
    public Response noticeToApprove(Long id) {
        final OffShelfInfo offShelfInfo = getOffShelfInfo(id);
        final List<OffShelfItemVO> offShelfItemVOS = getOffShelfItemVOList(id);
        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("紧急程度", offShelfInfo.getUrgentLevel().getDesc());
        final StaffBrief creator = StaffAssembler.INST.toStaffBrief(offShelfInfo.getCreatedUid());
        variables.put("发起人花名", creator.getNickname());
        variables.put("提交时间", DateUtil.format(offShelfInfo.getSubmitTime()));
        variables.put(
                "商品名称",
                offShelfItemVOS.stream()
                        .map(OffShelfItemVO::getItemName)
                        .collect(Collectors.joining("、")));
        final List<StaffBrief> receivers = getReceivers(offShelfInfo, offShelfItemVOS);
        variables.put(
                "接收者",
                receivers.stream().map(StaffBrief::getQwUserId).collect(Collectors.joining(",")));
        variables.put("ID", id);
        qyMsgSendService.send(
                RemindType.TO_DO_REMINDER, MsgTemplateCode.OFF_SHELF_TO_APPROVE, variables);
        return Response.buildSuccess();
    }

    @Override
    public Response noticeRevoked(Long id) {
        final OffShelfInfo offShelfInfo = getOffShelfInfo(id);
        final List<OffShelfItemVO> offShelfItemVOS = getOffShelfItemVOList(id);
        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("紧急程度", offShelfInfo.getUrgentLevel().getDesc());
        final StaffBrief creator = StaffAssembler.INST.toStaffBrief(offShelfInfo.getCreatedUid());
        variables.put("发起人花名", creator.getNickname());
        variables.put("提交时间", DateUtil.format(offShelfInfo.getSubmitTime()));
        variables.put(
                "商品名称",
                offShelfItemVOS.stream()
                        .map(OffShelfItemVO::getItemName)
                        .collect(Collectors.joining("、")));
        final List<StaffBrief> receivers = getReceivers(offShelfInfo, offShelfItemVOS);
        variables.put(
                "接收者",
                receivers.stream().map(StaffBrief::getQwUserId).collect(Collectors.joining(",")));
        variables.put("ID", id);
        qyMsgSendService.send(
                RemindType.TO_DO_REMINDER, MsgTemplateCode.OFF_SHELF_REVOKED, variables);
        return Response.buildSuccess();
    }

    @Override
    public Response noticeAuditRevoked(Long id) {
        final OffShelfInfo offShelfInfo = getOffShelfInfo(id);
        final List<OffShelfItemVO> offShelfItemVOS = getOffShelfItemVOList(id);
        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("紧急程度", offShelfInfo.getUrgentLevel().getDesc());
        final StaffBrief creator = StaffAssembler.INST.toStaffBrief(offShelfInfo.getCreatedUid());
        variables.put("发起人花名", creator.getNickname());
        variables.put("提交时间", DateUtil.format(offShelfInfo.getSubmitTime()));
        variables.put(
                "商品名称",
                offShelfItemVOS.stream()
                        .map(OffShelfItemVO::getItemName)
                        .collect(Collectors.joining("、")));
        final List<StaffBrief> receivers = getReceivers(offShelfInfo, offShelfItemVOS);
        variables.put(
                "接收者",
                receivers.stream().map(StaffBrief::getQwUserId).collect(Collectors.joining(",")));
        variables.put("ID", id);
        qyMsgSendService.send(
                RemindType.TO_DO_REMINDER, MsgTemplateCode.OFF_SHELF_AUDIT_REVOKED, variables);
        return Response.buildSuccess();
    }

    @Override
    public Response noticeToProcess(Long id) {
        final OffShelfInfo offShelfInfo = getOffShelfInfo(id);
        final List<OffShelfItemVO> offShelfItemVOS = getOffShelfItemVOList(id);
        final List<OffShelfFeedback> feedbacks =
                offShelfFeedbackService
                        .lambdaQuery()
                        .eq(OffShelfFeedback::getOffShelfInfoId, id)
                        .list();
        final List<Long> processUserIds =
                feedbacks.stream()
                        .map(OffShelfFeedback::getProcessUid)
                        .collect(Collectors.toList());
        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("紧急程度", offShelfInfo.getUrgentLevel().getDesc());
        final StaffBrief creator = StaffAssembler.INST.toStaffBrief(offShelfInfo.getCreatedUid());
        variables.put("发起人花名", creator.getNickname());
        final Optional<StaffBrief> approveUser =
                staffService.getStaffBrief(offShelfInfo.getApproveUid());
        variables.put("审核人花名", approveUser.map(StaffBrief::getNickname).orElse("**"));
        variables.put("提交时间", DateUtil.format(offShelfInfo.getSubmitTime()));
        variables.put(
                "商品名称",
                offShelfItemVOS.stream()
                        .map(OffShelfItemVO::getItemName)
                        .collect(Collectors.joining("、")));
        final ArrayList<StaffBrief> receivers = new ArrayList<>();
        receivers.add(creator);
        final List<StaffBrief> processUsers = staffService.getStaffBriefList(processUserIds);
        receivers.addAll(processUsers);
        variables.put(
                "接收者",
                receivers.stream().map(StaffBrief::getQwUserId).collect(Collectors.joining(",")));
        variables.put("ID", id);
        qyMsgSendService.send(
                RemindType.TO_DO_REMINDER, MsgTemplateCode.OFF_SHELF_TO_PROCESS, variables);
        return Response.buildSuccess();
    }

    private List<OffShelfItemVO> getOffShelfItemVOList(Long id) {
        final List<OffShelfItemVO> offShelfItemVos =
                iOffShelfItemService.getDaddyBaseMapper().selectOffShelfItemVOList(id);
        if (offShelfItemVos.isEmpty()) {
            return offShelfItemVos;
        }
        final List<Long> itemIds =
                offShelfItemVos.stream()
                        .map(OffShelfItemVO::getItemId)
                        .collect(Collectors.toList());
        final Map<Long, List<CorpBizTypeDTO>> corpBizTypeMap =
                bizLevelDivisionService.getCorpBizType(BizUnionTypeEnum.SPU, itemIds);
        offShelfItemVos.forEach(it -> it.setCorpBizType(corpBizTypeMap.get(it.getItemId())));
        return offShelfItemVos;
    }

    @Override
    public Response noticeRefused(Long id) {
        final OffShelfInfo offShelfInfo = getOffShelfInfo(id);
        final List<OffShelfItemVO> offShelfItemVos = getOffShelfItemVOList(id);
        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("紧急程度", offShelfInfo.getUrgentLevel().getDesc());
        final StaffBrief creator = StaffAssembler.INST.toStaffBrief(offShelfInfo.getCreatedUid());
        variables.put("发起人花名", creator.getNickname());
        variables.put("提交时间", DateUtil.format(offShelfInfo.getSubmitTime()));
        variables.put(
                "商品名称",
                offShelfItemVos.stream()
                        .map(OffShelfItemVO::getItemName)
                        .collect(Collectors.joining("、")));
        final List<StaffBrief> receivers = getReceivers(offShelfInfo, offShelfItemVos);
        variables.put(
                "接收者",
                receivers.stream().map(StaffBrief::getQwUserId).collect(Collectors.joining(",")));
        variables.put("ID", id);
        final Optional<StaffBrief> approveUser =
                staffService.getStaffBrief(offShelfInfo.getApproveUid());
        variables.put("审核人花名", approveUser.map(StaffBrief::getNickname).orElse("**"));
        qyMsgSendService.send(
                RemindType.TO_DO_REMINDER, MsgTemplateCode.OFF_SHELF_REFUSED, variables);
        return Response.buildSuccess();
    }

    @Override
    public Response noticeFinished(Long id) {
        final OffShelfInfo offShelfInfo = getOffShelfInfo(id);
        final List<OffShelfItemVO> offShelfItemVOS = getOffShelfItemVOList(id);
        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("紧急程度", offShelfInfo.getUrgentLevel().getDesc());
        final StaffBrief creator = StaffAssembler.INST.toStaffBrief(offShelfInfo.getCreatedUid());
        variables.put("发起人花名", creator.getNickname());
        variables.put("提交时间", DateUtil.format(offShelfInfo.getSubmitTime()));
        variables.put(
                "商品名称",
                offShelfItemVOS.stream()
                        .map(OffShelfItemVO::getItemName)
                        .collect(Collectors.joining("、")));
        final List<StaffBrief> receivers = getReceivers(offShelfInfo, offShelfItemVOS);
        variables.put(
                "接收者",
                receivers.stream().map(StaffBrief::getQwUserId).collect(Collectors.joining(",")));
        variables.put("ID", id);
        qyMsgSendService.send(
                RemindType.TO_DO_REMINDER, MsgTemplateCode.OFF_SHELF_FINISHED, variables);
        return Response.buildSuccess();
    }

    @Override
    public Response urge(OffShelfUrgeCmd cmd) {
        final OffShelfInfo offShelfInfo = getOffShelfInfo(cmd.getId());
        final String nickName = UserContext.getNickName();
        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("紧急程度", offShelfInfo.getUrgentLevel().getDesc());
        variables.put("催办花名", nickName);
        final StaffBrief processUser =
                staffService
                        .getStaffBrief(cmd.getProcessorUid())
                        .orElseThrow(
                                () ->
                                        ExceptionPlusFactory.bizException(
                                                ErrorCode.DATA_NOT_FOUND, "经办人信息异常"));
        variables.put("接收者", processUser.getQwUserId());
        variables.put("ID", cmd.getId());
        qyMsgSendService.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.OFF_SHELF_URGE, variables);
        return Response.buildSuccess();
    }

    @Override
    public Response executeOffShelf(Long id) {
        OffShelfInfo offShelfInfo = iOffShelfInfoService.getById(id);
        if (Objects.isNull(offShelfInfo)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未找到指定下架流程");
        }
        List<OffShelfItem> list =
                iOffShelfItemService.lambdaQuery().eq(OffShelfItem::getOffShelfInfoId, id).list();
        for (OffShelfItem offShelfItem : list) {
            Long itemId = offShelfItem.getItemId();
            try {
                String downFrameReason =
                        String.format(
                                "下架管理流程下架，流程编号 <a href=\"/erp/operation-management/off-shelf/detail?id=%s\">%s</a>",
                                offShelfInfo.getId(), offShelfInfo.getNo());
                itemBizService.updateStatus(
                        itemId, ItemStatus.OUT, DateUtil.currentTime(), downFrameReason, "下架管理");
            } catch (Exception e) {
                log.error("商品下架流程，执行下架异常，itemId={}", itemId, e);
            }
        }
        return Response.buildSuccess();
    }
}
