package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.File;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.FileMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IFileService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 文件信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
@Service
public class FileServiceImpl extends DaddyServiceImpl<FileMapper, File> implements IFileService {

}
