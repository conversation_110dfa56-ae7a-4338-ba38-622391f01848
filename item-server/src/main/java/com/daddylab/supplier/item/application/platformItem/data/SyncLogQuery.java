package com.daddylab.supplier.item.application.platformItem.data;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/3/21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SyncLogQuery extends PageQuery {
    private static final long serialVersionUID = 8371636812138788078L;
    @ApiModelProperty("同步时间-开始时间")
    Long syncStartTime;
    @ApiModelProperty("同步时间-结束时间")
    Long syncEndTime;
    @ApiModelProperty("平台商品ID")
    String outerItemId;
    @ApiModelProperty("商品SKU")
    String skuCode;
    @ApiModelProperty("平台")
    Platform platform;
}
