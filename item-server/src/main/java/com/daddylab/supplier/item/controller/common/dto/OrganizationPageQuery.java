package com.daddylab.supplier.item.controller.common.dto;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> up
 * @date 2022/3/31 11:47 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrganizationPageQuery extends PageQuery {

    private static final long serialVersionUID = 7912550990339070917L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("状态。0全部。1.正常，2.停用")
    private Integer state;

    @ApiModelProperty("类型。1:收料组织。2：采购组织")
    private Integer type;
}
