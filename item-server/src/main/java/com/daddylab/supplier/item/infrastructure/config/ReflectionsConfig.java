package com.daddylab.supplier.item.infrastructure.config;

import org.reflections.Reflections;
import org.reflections.scanners.Scanners;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class ReflectionsConfig {

    @Bean
    public Reflections reflections() {
        String basePackage = "com.daddylab.supplier";
        return new Reflections(basePackage, Scanners.SubTypes
                , Scanners.TypesAnnotated
                , Scanners.MethodsAnnotated
                , Scanners.ConstructorsAnnotated
                , Scanners.FieldsAnnotated
                , Scanners.Resources
                , Scanners.MethodsParameter
                , Scanners.ConstructorsParameter
                , Scanners.MethodsSignature
                , Scanners.ConstructorsSignature
                , Scanners.MethodsReturn);
    }
}
