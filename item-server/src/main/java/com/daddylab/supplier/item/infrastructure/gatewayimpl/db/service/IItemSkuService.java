package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.controller.item.dto.AttrDto;
import com.daddylab.supplier.item.controller.item.dto.SkuWithLaunchPlanDto;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <p>
 * 商品SKU 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface IItemSkuService extends IDaddyService<ItemSku> {
    /**
     * 获取 sku 信息
     * key：skuId
     * val：SkuWithLaunchPlanDto
     *
     * @param skuIds sku ID 集合
     * @return Map<Long, SkuWithLaunchPlanDto>
     */
    Map<Long, SkuWithLaunchPlanDto> getSkuDtoMap(List<Long> skuIds);

    /**
     * 获取映射
     * key: skuId
     * val: sku 对象
     *
     * @param skuIds 规格 IDs
     * @return Map<Long, ItemSku>
     */
    Map<Long, ItemSku> getSkuMap(List<Long> skuIds);

    /**
     * 查询商品 sku
     *
     * @param itemId 商品 ID
     * @return List<ItemSku>
     */
    List<ItemSku> selectListByItemId(Long itemId);

    /**
     * 获取 sku 属性
     *
     * @param skuId 规格 ID
     * @return List<SkuAttrDto>
     */
    List<AttrDto> selectSkuAttrDtosBySkuId(Long skuId);

    /**
     * 获取 SKU 的品牌 ID
     * @param skuCode
     * @return
     */
    Long getSkuBrandId(String skuCode);

    List<ItemSku> selectByItemIds(List<Long> itemIds);

    List<ItemSku> selectByMixCodes(Collection<String> relatedSkuCodes);

    Optional<ItemSku> getByMixCode(String mixCode);

    void updateNewProvider(Long itemId,Long newProviderId);
}
