package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.controller.handingsheet.dto.HandingSheetActivityTextPageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetActivityText;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 盘货表活动内容 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
public interface HandingSheetActivityTextMapper extends DaddyBaseMapper<HandingSheetActivityText> {

    IPage<HandingSheetActivityText> selectPageList(@Param("page") Page<HandingSheetActivityText> page,
                                                   @Param(Constants.WRAPPER) Wrapper<HandingSheetActivityText> queryWrapper);

    long selectPageListCount(@Param(Constants.WRAPPER) Wrapper<HandingSheetActivityText> queryWrapper);
}
