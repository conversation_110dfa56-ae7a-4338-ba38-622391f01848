package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/9/4
 */
@AllArgsConstructor
@Getter
public enum ItemBanniuRefType implements IIntegerEnum {
    ITEM(1, "后端商品"),
    COMBINATION_ITEM(2, "组合装"),
    ;

    private final Integer value;
    private final String desc;
}
