package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CombinationStepPriceExcel;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 组合多件供价（excel） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
public interface ICombinationStepPriceExcelService extends IDaddyService<CombinationStepPriceExcel> {

}
