package com.daddylab.supplier.item.application.drawer.converter;

import com.daddylab.supplier.item.application.drawer.compare.ItemDrawerCompare;
import com.daddylab.supplier.item.domain.drawer.form.ItemDrawerForm;
import com.daddylab.supplier.item.domain.drawer.form.ItemDrawerImageForm;
import com.daddylab.supplier.item.domain.drawer.vo.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.CheckDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Class  ItemDrawerConverter
 *
 * @Date 2022/6/1上午11:49
 * <AUTHOR>
 */
@Mapper
public interface ItemDrawerConverter {
    ItemDrawerConverter INSTANCE = Mappers.getMapper(ItemDrawerConverter.class);

    /**
     * ItemDrawer2ItemDrawerVO
     *
     * @param itemDrawer
     * @return
     */
    ItemDrawerVO itemDrawerToVO(ItemDrawer itemDrawer);


    /**
     * itemDrawerMarkImage2itemDrawerMarkImageVO
     *
     * @param itemDrawerMarkImage
     * @return
     */
    @Mapping(target = "itemDrawerMarkImageContents", ignore = true)
    ItemDrawerMarkImageVO itemDrawerMarkImageToVO(ItemDrawerMarkImage itemDrawerMarkImage);


    /**
     * itemDrawerMarkImageContent2itemDrawerMarkImageContentVO
     *
     * @param itemDrawerMarkImageContent PO
     * @return VO
     */
    @Mapping(target = "markUserNick", ignore = true)
    @Mapping(target = "markUserName", ignore = true)
    @Mapping(target = "markUid", source = "createdUid")
    @Mapping(target = "markTime", source = "createdAt")
    ItemDrawerMarkImageContentVO itemDrawerMarkImageToVO(ItemDrawerMarkImageContent itemDrawerMarkImageContent);

    /**
     * itemDrawerMarkImageContent2itemDrawerMarkImageContentVO
     *
     * @param itemDrawerMarkImageContent
     * @return
     */
    List<ItemDrawerMarkImageContentVO> itemDrawerMarkImageContentToListVO(List<ItemDrawerMarkImageContent> itemDrawerMarkImageContent);

    /**
     * converter
     *
     * @param itemDrawer
     * @return
     */
    @Mapping(target = "attrImages", ignore = true)
    @Mapping(target = "skuImages", ignore = true)
    @Mapping(target = "qcIds", ignore = true)
    @Mapping(target = "principalId", ignore = true)
    @Mapping(target = "planId", ignore = true)
    @Mapping(target = "mainImageVideo", ignore = true)
    @Mapping(target = "images", ignore = true)
    @Mapping(target = "detailImages", ignore = true)
    @Mapping(target = "buyerId", ignore = true)
    ItemDrawerCompare itemDrawerToCompare(ItemDrawer itemDrawer);

    /**
     *  form
     * @param itemDrawerForm
     * @return
     */
    @Mappings({
            @Mapping(source = "detailImages", target = "detailImages", ignore = true),
            @Mapping(source = "mainImageVideo", target = "mainImageVideo", ignore = true)}
    )
    ItemDrawerCompare itemDrawerFormToCompare(ItemDrawerForm itemDrawerForm);

    default List<String> mapImageForm2url(List<ItemDrawerImageForm> value) {
        return value.stream().map(ItemDrawerImageForm::getUrl).collect(Collectors.toList());
    }

    /**
     * 表单字段转model
     *
     * @param itemDrawerForm
     * @return
     */
    ItemDrawer itemDrawerFormToModel(ItemDrawerForm itemDrawerForm);

    /**
     * 对象
     * @param checkDetailDTO
     * @return
     */
    CheckDetailVO checkDetailDTOToVO(CheckDetailDTO checkDetailDTO);

    /**
     *
     * @param itemDrawerImage
     * @return
     */
    @Mappings(
            @Mapping(source = "firstImage", target = "firstImageUrl")
    )
    ItemDrawerImageVO itemDrawerImageToVO(ItemDrawerImage itemDrawerImage);

    /**
     *
     * @param itemDrawerImageForm
     * @return
     */
    @Mappings(
            @Mapping(source = "firstImageUrl", target = "firstImage")
    )
    ItemDrawerImage itemDrawerImageFormToItemDrawerImage(ItemDrawerImageForm itemDrawerImageForm);

    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "itemId", ignore = true),
            @Mapping(target = "createdAt", ignore = true),
            @Mapping(target = "createdUid", ignore = true),
    })
    ItemDrawer copyItemDrawer(ItemDrawer source);

    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "drawerId", ignore = true)
    })
    ItemDrawerImage copyItemDrawerImage(ItemDrawerImage source);

    ItemDrawerLiveVerbal copy(ItemDrawerLiveVerbal data);

    List<ItemDrawerLiveVerbal> copyItemDrawerLiveVerbalList(List<ItemDrawerLiveVerbal> datas);
}
