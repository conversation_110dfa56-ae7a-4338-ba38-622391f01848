package com.daddylab.supplier.item.infrastructure.accessControl;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 资源
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Data
@EqualsAndHashCode(callSuper = false)
public class Resource implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 父级id
     */
    private Long parentId;

    /**
     * 表示所在系统
     */
    private Long systemId;

    /**
     * 前端url
     */
    private String frontUrl;

    private String component;

    /**
     * 后端url
     */
    private String apiUrl;

    /**
     * 角色描述
     */
    private String name;

    /**
     * 标签
     */
    private String code;

    /**
     * 菜单类型
     * @see com.daddylab.supplier.item.domain.auth.enums.ResourceType
     */
    private String type;

    /**
     * 是否隐藏 0显示 1隐藏
     */
    private Integer hide;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 图标
     */
    private String icon;

    /**
     * 更新时间
     */
    private Long updatedAt;

    /**
     * 创建时间
     */
    private Long createdAt;

    /**
     * 是否已删除
     */
    private Long deletedAt;


}
