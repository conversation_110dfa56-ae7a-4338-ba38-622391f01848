package com.daddylab.supplier.item.application.message.wechat;

import static com.daddylab.supplier.item.application.message.wechat.MsgEventType.*;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;

import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatMsg;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWechatMsgService;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.google.common.eventbus.Subscribe;

import lombok.extern.slf4j.Slf4j;

import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> up
 * @date 2022/6/3 6:22 下午
 */
@Slf4j
@EventBusListener(value = "weChatMsgListener", mode = EventBusListener.MODE.ASYNC)
public class MsgEventListener {

    @Resource
    IWechatMsgService iWechatMsgService;

    @Resource
    RecipientUtil recipientUtil;

    @Resource
    MsgBodyUtil msgBodyUtil;

    @Resource
    RecipientConfig recipientConfig;

    @Resource
    MsgSender msgSender;

    @Subscribe
    public void listener(MsgEvent weChatMsgEvent) {
        log.info("MsgEventListener weChatMsgEvent:{}", JsonUtil.toJson(weChatMsgEvent));
        try {
            Assert.notNull(weChatMsgEvent.getMsgEventType(), "企微消息事件类型值。不得为空");
            checkEvent(weChatMsgEvent);
            eventHandler(weChatMsgEvent);
        } catch (Exception e) {
            log.error("MsgEventListener error,event:{}", JsonUtil.toJson(weChatMsgEvent), e);
            Alert.text(MessageRobotCode.GLOBAL, "企微消息提醒处理异常，" + e.getMessage());
        }
    }

    public void checkEvent(@NotNull MsgEvent msgEvent) {
        MsgEventType msgEventType = msgEvent.getMsgEventType();
        switch (msgEventType) {
            case IMPROVE_TO_DESIGN:
            case DESIGN_TO_AUDIT_LEGAL:
            case DESIGN_TO_AUDIT_QC:
            case AUDIT_TO_MODIFY:
            case LIVE_SPEECH:
            case LIVE_SPEECH_AUDIT_LEGAL:
            case LIVE_SPEECH_AUDIT_QC:
                if (CollUtil.isEmpty(msgEvent.getItemIdList())) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "商品id不得为空");
                }
                if (msgEvent.getItemIdList().size() != 1) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "只允许单件商品维度处理");
                }
                break;
            case PLAN_SUBMIT:
            case PLAN_INFO_CHANGE:
                MsgEvent.ItemLaunchPlaneOperator operator = msgEvent.getPlanOperator();
                if (Objects.isNull(operator)) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "上新流-上新计划消息提醒。上新计划参数不得为空");
                }
                if (Objects.isNull(operator.getOperateUserId()) || Objects.isNull(operator.getPlanId())) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "上新流-上新计划消息提醒。上新计划详细参数不得为空");
                }
                break;
            case ONE_FIELD_CHANGE:
                if (CollUtil.isEmpty(msgEvent.getItemIdList())) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "上新流-字段修改消息提醒。商品id不得为空");
                }
                break;
            case CHARGER_CHANGE:
                if (CollUtil.isEmpty(msgEvent.getItemIdList())) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "上新流-负责人修改消息提醒。商品id不得为空");
                }
                if (Objects.isNull(msgEvent.getChangeInfo())) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "上新流-负责人修改消息提醒。负责人参数不得为空");
                }
                if (Objects.isNull(msgEvent.getChangeInfo().getChangeOperateUserId()) ||
                        Objects.isNull(msgEvent.getChangeInfo().getNewPrincipalId())) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "上新流-负责人修改消息提醒。负责人详细参数不得为空");
                }
                break;
            case SHELF_ITEM_INFO_CHANG:
                break;
            default:
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "非法企微事件触发类型");
        }
    }

    public void eventHandler(MsgEvent event) throws ExecutionException, InterruptedException {
        // 获取企微通知的接收人，以及和接收信息相关的页面跳转绑定id,可能是商品id 也可能是上新计划id
        CompletableFuture<Set<RecipientBo>> setCompletableFuture = CompletableFuture
                .supplyAsync(() -> recipientUtil.getRecipient(event.getMsgEventType(), event.getItemIdList()
                        , event.getPlanOperator(), event.getChangeInfo()));
        // 构建信息内容
        CompletableFuture<MsgBodyBO> uCompletableFuture = CompletableFuture
                .supplyAsync(() -> msgBodyUtil.getMsgBody(event.getMsgEventType(), event.getItemIdList()
                        , event.getPlanOperator(), event.getChangeInfo(), event.getLiveVerbalTrickName()));
        CompletableFuture.allOf(setCompletableFuture, uCompletableFuture).join();

        // 根据每一个接受者来构建待发送的企微消息
        List<WechatMsg> saveList = new LinkedList<>();
        setCompletableFuture.get().forEach(recipient -> {
            try {
                WechatMsg wechatMsg = new WechatMsg();
                wechatMsg.setTitle(uCompletableFuture.get().getTitle());
                wechatMsg.setContent(uCompletableFuture.get().getContent());
                wechatMsg.setState(0);
                wechatMsg.setRecipient(recipient.getQwUserId());
                wechatMsg.setType(event.getMsgEventType().getRemindType().getValue());
                wechatMsg.setRequirement(CharSequenceUtil.join(",", recipient.getIdList()));

                if (event.getMsgEventType().equals(PLAN_SUBMIT)
                        || event.getMsgEventType().equals(PLAN_INFO_CHANGE)) {
                    wechatMsg.setLink(recipientConfig.getItemPlanLink() + "?id=" + wechatMsg.getRequirement());
                } else if (event.getMsgEventType().equals(SHELF_ITEM_INFO_CHANG)) {
                    wechatMsg.setLink(recipientConfig.getNewGoodLink() + "?itemId=" + wechatMsg.getRequirement() + "&productId=" + wechatMsg.getRequirement());
                } else {
                    // 如果是直播话术相关的信息，调到新品商品库直播话术tab,前端页面带上指定参数。
                    if (event.getMsgEventType().equals(LIVE_SPEECH_AUDIT_LEGAL) ||
                            event.getMsgEventType().equals(LIVE_SPEECH_AUDIT_QC) ||
                            event.getMsgEventType().equals(LIVE_SPEECH)) {
                        wechatMsg.setLink(recipientConfig.getNewGoodLink() + "?itemId=" + wechatMsg.getRequirement() + "&tab=live");
                    } else {
                        wechatMsg.setLink(recipientConfig.getNewGoodLink() + "?itemId=" + wechatMsg.getRequirement());
                    }
                }

                saveList.add(wechatMsg);
                log.info("MsgEventListener weChatMsgEvent msg:{}", JsonUtil.toJson(wechatMsg));
            } catch (InterruptedException e) {
                log.error("MsgEventListener weChatMsgEvent handler error", e);
                Thread.currentThread().interrupt();
            } catch (ExecutionException e) {
                log.error("MsgEventListener weChatMsgEvent handler error", e);
                throw new RuntimeException(e);
            }
        });
        iWechatMsgService.saveBatch(saveList);

        MsgEventType msgEventType = event.getMsgEventType();
        saveList.forEach(val -> {
            // 如果是即时提醒，立即发送。
            if (RemindType.IMMEDIATELY_REMINDER.equals(msgEventType.getRemindType())) {
                if (1 == msgEventType.getSendType()) {
                    msgSender.sendText(val);
                } else {
                    msgSender.send(val);
                }
            }
        });
    }
}
