package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopOperatorMap;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ShopOperatorMapMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopOperatorMapService;
import java.util.Collections;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 下架管理-店铺/运营映射关联 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-01
 */
@Service
public class ShopOperatorMapServiceImpl extends DaddyServiceImpl<ShopOperatorMapMapper, ShopOperatorMap> implements IShopOperatorMapService {

    @Override
    public List<ShopOperatorMap> listByShopIds(List<Long> selectShopIds) {
        if (selectShopIds == null || selectShopIds.isEmpty()) return Collections.emptyList();
        return lambdaQuery().in(ShopOperatorMap::getShopId, selectShopIds).list();
    }
}
