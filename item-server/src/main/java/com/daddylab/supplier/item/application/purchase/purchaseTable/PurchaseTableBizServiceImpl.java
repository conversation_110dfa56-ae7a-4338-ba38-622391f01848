package com.daddylab.supplier.item.application.purchase.purchaseTable;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.controller.purchaseTable.dto.PurchaseTableCmd;
import com.daddylab.supplier.item.domain.purchaseTable.service.PurchaseTableDomainService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseTable;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseTableService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static com.daddylab.supplier.item.application.purchase.PurchaseConst.MONTH_PATTERN;

/**
 * <AUTHOR>
 * @ClassName PurchaseTableBizServiceImpl.java
 * @description 采购确认
 * @createTime 2021年11月16日 21:14:00
 */
@Slf4j
@Service
public class PurchaseTableBizServiceImpl implements PurchaseTableBizService {
    @Autowired
    private PurchaseTableDomainService purchaseTableDomainService;
    @Autowired
    private IPurchaseTableService purchaseTableService;

    @Override
    public SingleResponse<Integer> selectStatusByMonth(String month) {
        month = month.trim();
        if (month.length() != 7) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "月份格式应为2022/05");
        }
        PurchaseTable purchaseTable = getOrCreate(month);
        return SingleResponse.of(purchaseTable.getIsEdit());
    }

    public PurchaseTable getOrCreate(String month) {
        PurchaseTable purchaseTable;
        QueryWrapper<PurchaseTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PurchaseTable::getMonth, month);
        purchaseTable = purchaseTableService.getOne(queryWrapper);
        if (Objects.isNull(purchaseTable)) {
            purchaseTable = new PurchaseTable();
            purchaseTable.setMonth(month);
            final String currentMonth = DateUtil.format(System.currentTimeMillis() / 1000, MONTH_PATTERN);
            purchaseTable.setIsEdit(Objects.equals(currentMonth, month) ? 0 : 1);
            purchaseTableService.save(purchaseTable);
        }
        return purchaseTable;
    }

    @Override
    public Map<String, Boolean> getEditableStatusBatch(Set<String> months) {
        final List<PurchaseTable> purchaseTables = purchaseTableService.lambdaQuery()
                .in(PurchaseTable::getMonth, months).list();
        final HashMap<String, Boolean> resultMap = new HashMap<>();
        for (String month : months) {
            final Optional<PurchaseTable> tableOptional = purchaseTables.stream()
                    .filter(purchaseTable -> purchaseTable.getMonth().equals(month)).findFirst();
            final PurchaseTable purchaseTable = tableOptional.orElseGet(() -> getOrCreate(month));
            resultMap.put(month, purchaseTable.getIsEdit() == 0);
        }
        return resultMap;
    }

    @Override
    public void insert(PurchaseTable purchaseTable) {
        QueryWrapper<PurchaseTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(StringUtils.isNotBlank(purchaseTable.getMonth()), PurchaseTable::getMonth, purchaseTable.getMonth());
        PurchaseTable one = purchaseTableService.getOne(queryWrapper);
        if (Objects.isNull(one)) {
            purchaseTableService.save(purchaseTable);
        }
    }

    @Override
    public MultiResponse<String> getMonth() {
        PurchaseTable one = purchaseTableService.lambdaQuery().orderByDesc(PurchaseTable::getMonth).select()
                .last("limit 1").one();
        LocalDate endTime = LocalDate.now();
        if (Objects.nonNull(one)) {
            if (isNotBlank(one.getMonth())) {
                LocalDate oneDate = LocalDate.parse(one.getMonth() + "/01", DateTimeFormatter.ofPattern("yyyy/MM/dd"));
                if (oneDate.isAfter(endTime)) {
                    endTime = oneDate;
                }
            }
        }
        List<String> month = new LinkedList<>();
        LocalDate startTime = LocalDate.parse("2021/11/01", DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        //isBefore函数左闭右开。最后需要加上endTime
        while (startTime.isBefore(endTime)) {
            month.add(startTime.format(DateTimeFormatter.ofPattern("yyyy/MM")));
            startTime = startTime.plusMonths(1);
        }
        month.add(endTime.format(DateTimeFormatter.ofPattern("yyyy/MM")));

        List<String> collect = month.stream().sorted(Comparator.reverseOrder()).distinct().collect(Collectors.toList());
        return MultiResponse.of(collect);
    }

    @Override
    public Response update(PurchaseTableCmd cmd) {
        Assert.notNull(cmd.getMonth(), "月份不得为空");
        QueryWrapper<PurchaseTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(PurchaseTable::getMonth, cmd.getMonth());
        PurchaseTable one = purchaseTableService.getOne(queryWrapper);
        if (Objects.nonNull(one)) {
            one.setIsEdit(cmd.getIsEdit());
            purchaseTableService.saveOrUpdate(one);
        }
        return Response.buildSuccess();
    }

    @Override
    public PurchaseTable selectDataByMonth(String month) {
        QueryWrapper<PurchaseTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(PurchaseTable::getMonth, month)
                .eq(PurchaseTable::getIsDeal, 0);
        return purchaseTableService.getOne(queryWrapper);
    }

    @Override
    public void updateIsDeal(Long id) {
        PurchaseTable purchaseTable = purchaseTableService.getById(id);
        if (Objects.isNull(purchaseTable)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未查询到ID为" + id + "的采购权限记录");
        }
        purchaseTable.setIsDeal(1);
        purchaseTableService.updateById(purchaseTable);
    }

    @Override
    public PurchaseTable selectOne(String month) {
        QueryWrapper<PurchaseTable> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PurchaseTable::getMonth, month);
        return purchaseTableService.getOne(queryWrapper);
    }
}
