package com.daddylab.supplier.item.infrastructure.wiki;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/7/18
 */
@FeignClient(value = "wikiClient", url = "https://wiki.dlab.cn/")
public interface WikiClient {
    @GetMapping("/rest/api/content/{id}")
    String getContentBody(@RequestHeader("Authorization") String token, @PathVariable("id") Long contentId, @SpringQueryMap Map<String, String> params);

    @GetMapping("{path}")
    byte[] downloadAttachment(@RequestHeader("Authorization") String token, @PathVariable("path") String path, @SpringQueryMap Map<String, String> params);

    @PutMapping("/rest/api/content/{id}")
    String putContentBody(@RequestHeader("Authorization") String token, @PathVariable("id") Long contentId, @RequestBody Map<String, Object> body);

}
