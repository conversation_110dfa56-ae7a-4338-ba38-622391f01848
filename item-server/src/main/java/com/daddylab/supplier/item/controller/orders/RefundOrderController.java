package com.daddylab.supplier.item.controller.orders;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.refundOrder.RefundOrderService;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderBaseInfo;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderDetail;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @ClassName OtherPayController.java
 * @description 其他应付单
 * @createTime 2022年03月24日 15:36:00
 */
@Slf4j
@Api(value = "退换单管理API", tags = "退换单管理API")
@RestController
@RequestMapping("/refundOrder")
public class RefundOrderController {

    @Autowired
    private RefundOrderService refundOrderService;


    @ResponseBody
    @ApiOperation(value = "退换单查询")
    @PostMapping("/refundOrderQuery")
    public PageResponse<RefundOrderBaseInfo> refundOrderQuery(@RequestBody RefundOrderQuery query) {
        return refundOrderService.refundOrderQuery(query);
    }

    @ResponseBody
    @ApiOperation(value = "退换单详情")
    @GetMapping("/refundOrderDetail")
    public SingleResponse<RefundOrderDetail> refundOrderDetail(
            @ApiParam(value = "退换单号", required = true) @RequestParam String refundOrderNo) {
        return refundOrderService.refundOrderDetail(refundOrderNo);
    }


}
