package com.daddylab.supplier.item.infrastructure.utils;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;

import javax.servlet.http.HttpServletResponse;

public class HttpHeaderUtil {
    public static void setXlsxAttachmentHeaders(HttpServletResponse response, String filename) {
        setAttachmentHeaders(response, FileUtil.ensureSuffix(filename, "xlsx"),
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", null);
    }


    public static void setAttachmentHeaders(HttpServletResponse response, String filename, String contentType, String charset) {
        response.setHeader("Content-Disposition", StrUtil.format("attachment;filename={}",
                URLUtil.encode(filename, charset != null ? CharsetUtil.charset(charset) : CharsetUtil.defaultCharset())));
        response.setContentType(contentType);
    }
}
