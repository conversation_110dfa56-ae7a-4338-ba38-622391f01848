package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockOutDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OtherStockOutDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOtherStockOutDetailService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 其他出库单详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@Service
public class OtherStockOutDetailServiceImpl extends DaddyServiceImpl<OtherStockOutDetailMapper, OtherStockOutDetail> implements IOtherStockOutDetailService {

}
