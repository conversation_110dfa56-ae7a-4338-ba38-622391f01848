package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemStockChangeLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemStockChangeLogMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemStockChangeLogService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品库存变动记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Service
public class ItemStockChangeLogServiceImpl extends DaddyServiceImpl<ItemStockChangeLogMapper, ItemStockChangeLog> implements IItemStockChangeLogService {

}
