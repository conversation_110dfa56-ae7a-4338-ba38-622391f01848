package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.UserRole;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.UserRoleMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IUserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户角色 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
@Service
@DS("authDb")
public class UserRoleServiceImpl extends DaddyServiceImpl<UserRoleMapper, UserRole> implements IUserRoleService {

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Override
    public void demo() {
        final UserRole userRole = userRoleMapper.selectById(100);
        System.out.println(JSONObject.toJSONString(userRole));
    }
}
