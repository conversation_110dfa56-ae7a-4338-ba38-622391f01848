package com.daddylab.supplier.item.application.handingsheet;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.HandingSheetMapper;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/31 15:04
 * @Description: 盘货表相关定时任务
 */
@Component
@Slf4j
public class HandingSheetTask {
    @Autowired
    private HandingSheetMapper handingSheetMapper;

    @Autowired
    private HandingSheetV2BizService handingSheetV2BizService;

    @XxlJob("HandingSheetTask.updateStatus")
    public void updateStatus() {
        Long nowSecond = DateUtil.getNowSecond();
        handingSheetMapper.updateToProcessing(nowSecond);
        handingSheetMapper.updateToExpired(nowSecond);
    }

    @XxlJob("HandingSheetTask.toBeConfirmNoticeSummary")
    public void toBeConfirmNoticeSummary() {
        handingSheetV2BizService.toBeConfirmNoticeSummary();
    }
}
