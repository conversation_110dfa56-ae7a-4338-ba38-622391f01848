package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrderLogistics;

/**
 * <p>
 * 旺店通销售出库单物流单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
public interface WdtSaleStockOutOrderLogisticsMapper extends DaddyBaseMapper<WdtSaleStockOutOrderLogistics> {

}
