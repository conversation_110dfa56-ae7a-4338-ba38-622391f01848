package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.springframework.stereotype.Repository;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.UserRole;

/**
 * <p>
 * 用户角色 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
@Repository
@DS("authDb")
public interface UserRoleMapper extends DaddyBaseMapper<UserRole> {

}
