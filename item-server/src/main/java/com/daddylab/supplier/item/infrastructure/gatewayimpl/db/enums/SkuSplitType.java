package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 */
@AllArgsConstructor
@Getter
public enum SkuSplitType implements IEnum<Integer> {

    /**
     * 消除黄线
     */
    NO_SINGLE(1, "非单发件（需和其他类型货品一起发）"),
    NORMAL(2, "普通件"),
    NORMAL_LARGE(3, "普通大件（可与非大件一起发）"),
    INDEPENDENCE_LARGE(4, "独立大件（必须单发"),
    SPLIT_BY_BOX(5, "按箱规拆分"),

    ;

    @EnumValue
    private final Integer value;
    private final String desc;
}
