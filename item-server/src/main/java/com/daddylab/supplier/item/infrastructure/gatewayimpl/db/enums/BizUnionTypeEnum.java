package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> up
 * @date 2025年02月06日 2:53 PM
 */
@AllArgsConstructor
@Getter
public enum BizUnionTypeEnum implements IEnum<Integer> {


    SPU(0, "后端商品"),
    COMBINATION(1, "组合装商品"),
    BRAND(2, "品牌"),
    PROVIDER(3, "供应商"),
    SHOP(4, "店铺"),
//    SETTLE_IN_ACTIVE_PRICE(5, "入驻活动价"),

    PURCHASE_ACTIVE_PRICE(6, "采购活动价"),
    PURCHASE_PARTITION_PRICE(7, "采购阶梯价"),

    // 下列5种单据，原本主表中的business_line字段不变。不动。修改其含义。
    PURCHASE_ORDER(8, "采购订单"),
    PAYMENT_ORDER(9, "付款单"),
    STOCK_IN_ORDER(10, "入库单"),
    STOCK_OUT_ORDER(11, "出库单"),
    STOCK_PAYMENT_ORDER(12, "应付单"),
    WAREHOUSE(13, "仓库"),
    SETTLEMENT_ORDER(14, "结算单"),
    INVENTORY_STATISTICS(15, "库存统计"),

    WAREHOUSE_INVENTORY(16, "仓库库存"),
    VIRTUAL_WAREHOUSE(17, "虚拟仓"),
    AFTER_SALES_REGISTRATION(18, "客户售后登记"),
    NEW_PRODUCT_LAUNCH_PLAN(19, "商品管理"),
    PRODUCT_OPTIMIZE_PLAN(20, "商品优化计划"),
    NEW_GOODS(21, "新品商品库"),
    PRODUCT_OPTIMIZE(22, "商品优化"),

    ;

    @EnumValue
    private final Integer value;

    private final String desc;
}
