package com.daddylab.supplier.item.controller.test.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2023年12月22日 12:00 PM
 */
@Data
public class ProviderExportInfoDto {

    @ExcelProperty("供应商")
    private String name;

    @ExcelProperty("统一社会信息代码")
    private String unifySocialCreditCodes;

    @ExcelIgnore
    private Long mainChargerUserId;

    @ExcelProperty("负责人")
    private String mainChargerUser;

    @ExcelIgnore
    private Long secondChargerUserId;

    @ExcelProperty("次要负责人")
    private String secondChargerUser;

    /*@ExcelProperty("银行账号")
    private String bankCard;

    @ExcelProperty("开户行")
    private String bankDeposit;

    @ExcelProperty("开户行行号")
    private String bankNo;*/

    @ExcelProperty(value = "类型", converter = ProviderTypeAutoConverter.class)
    private Integer type;

    @ExcelProperty(value = "状态", converter = ProviderStatusConvert.class)
    private Integer status;

    @ExcelProperty(value = "合作模式", converter = ProviderBusinessLineConvert.class)
    private String businessLine;

}
