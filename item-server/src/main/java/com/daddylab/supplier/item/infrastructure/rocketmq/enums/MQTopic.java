package com.daddylab.supplier.item.infrastructure.rocketmq.enums;

/**
 * <AUTHOR>
 * @since 2022/2/18
 */
public interface MQTopic {
  /** 权限系统策略变更 */
  String CASBIN_POLICY_UPDATED = "casbin-policy-updated";

  /** 金蝶同步 */
  String WECHAT_DELAY_MSG = "${spring.profiles.active}_${spring.application.name}_wechat_delay_msg";

  /** 重要消息推送 */
  String CRITICAL_PUSH = "${spring.profiles.active}_${spring.application.name}_critical-push";

  /** 快手回调消息 */
  String THIRD_CALLBACK_MESSAGE_KS_TOPIC =
      "${spring.profiles.active}_${spring.application.name}thirdCallbackMessageKuaiShouTopic";

  /** 小红书回调消息 */
  String THIRD_CALLBACK_MESSAGE_RED_BOOK_TOPIC =
      "${spring.profiles.active}_${spring.application.name}thirdCallbackMessageRedBookTopic";

  /** 自研电商商品变更事件 */
  String THIRD_MALL_ITEM_CHANGE_TOPIC = "${spring.profiles.active}_mall-item-update";

  /** 平台商品变更消息（库存、商品信息...） */
  String PLAT_ITEM_SKU_TOPIC =
      "${spring.profiles.active}_${spring.application.name}platformItemSkuTopic";

  /** 企微消息推送 */
  String QW_MESSAGE_PUSH = "${spring.profiles.active}_${spring.application.name}_qw_message_push";

  /** 快刀云物流轨迹回调 */
  String KUAIDAOYUN_CALLBACK_TOPIC =
      "${spring.profiles.active}_${spring.application.name}_kuaidaoyun_callback";

  /**
   * 旺店通订单更新事件
   *
   * @deprecated 预留，暂不使用
   */
  String WDT_ORDER_UPDATE_TOPIC =
      "${spring.profiles.active}_${spring.application.name}_wdt_order_update";

  /** 店铺库存设置 */
  String SHOP_STOCK_TOPIC = "${spring.profiles.active}_${spring.application.name}_shop_stock";
}
