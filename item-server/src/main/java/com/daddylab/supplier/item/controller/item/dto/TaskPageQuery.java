package com.daddylab.supplier.item.controller.item.dto;

import com.alibaba.cola.dto.PageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/25 6:07 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("导出任务列表请求参数")
public class TaskPageQuery extends PageQuery {
    private static final long serialVersionUID = -5527120209775112856L;

    @ApiModelProperty(
            "导出任务类型。BACK_ITEM：后端商品（默认）；BRAND：品牌。PURCHASE：采购。COMBINATION_ITEM：组合商品。"
                    + "PURCHASE_ORDER：采购入库订单明细。STOCK_IN_ORDER：采购入库。STOCK_OUT_ORDER：退料出库。SALE_ITEM_LIBRARY：销售商品库。NEW_GOODS：新品商品库"
                    + "。AFTER_SALES_ABNORMAL：异常件信息。REFUND_ORDER：退换单。AFTER_SALES_REGISTER：客服售后登记表。ORDER_DELIVERY_TRACE：订单发货跟踪。" +
                    "ALL_SETTLEMENT_INFO_ZIP:批量采购结算导出zip")
    private ExportTaskType type;
}
