package com.daddylab.supplier.item.application.purchasePayable.applyPayAudit.dto;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年02月16日 10:41 AM
 */
@RefreshScope
@ConfigurationProperties(prefix = "apply-pay-audit")
@Configuration
@Data
public class AuditRoleConfig {

    List<AuditRoleConfigBo> nodeRoles;

}
