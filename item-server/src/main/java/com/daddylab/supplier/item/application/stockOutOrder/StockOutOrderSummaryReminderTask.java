package com.daddylab.supplier.item.application.stockOutOrder;

import com.actionsoft.sdk.service.model.TaskInstance;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.aws.AwsOpenApiClient;
import com.daddylab.supplier.item.application.aws.service.AwsProcessClient;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockOutOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockOutOrderState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAwsBusinessLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockOutOrderService;
import com.daddylab.supplier.item.infrastructure.utils.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/1/4
 */
@Service
@Slf4j
@AllArgsConstructor
public class StockOutOrderSummaryReminderTask {
    private final IStockOutOrderService stockOutOrderService;
    private final IAwsBusinessLogService awsBusinessLogService;
    private final AwsOpenApiClient awsOpenApiClient;
    private final StockOutOrderBizService stockOutOrderBizService;
    private final StaffService staffService;

    @XxlJob("StockOutOrderSummaryReminderTask")
    public void doTask() {
        final List<StockOutOrder> list =
                stockOutOrderService
                        .lambdaQuery()
                        .eq(StockOutOrder::getState, StockOutOrderState.NO_AUDIT.getValue())
                        .list();
        final AwsProcessClient awsProcessClient = awsOpenApiClient.getAwsProcessClient();
        final ArrayList<TaskInstance> allTaskInstances = new ArrayList<>();
        final HashMap<String, StockOutOrder> stringStockOutOrderHashMap = new HashMap<>();
        for (StockOutOrder stockOutOrder : list) {
            final String processInstId =
                    awsBusinessLogService.getProcessIdByBusinessId(
                            PurchaseTypeEnum.OUT_STOCK_PAYABLE, stockOutOrder.getId());
            if (ObjectUtil.isEmpty(processInstId)) {
                continue;
            }
            final List<TaskInstance> taskInstances = awsProcessClient.taskQuery(processInstId);
            allTaskInstances.addAll(taskInstances);
            for (TaskInstance taskInstance : taskInstances) {
                stringStockOutOrderHashMap.put(taskInstance.getId(), stockOutOrder);
            }
        }
        log.info("【退料出库单待办汇总提醒】待提醒任务共计:{}", allTaskInstances.size());
        if (!allTaskInstances.isEmpty()) {
            final Map<String, List<TaskInstance>> taskGroup =
                    allTaskInstances.stream()
                            .collect(Collectors.groupingBy(TaskInstance::getTarget));
            log.info("【退料出库单待办汇总提醒】按照办理人汇总 {} {}", taskGroup.keySet().size(), taskGroup.keySet());
            for (Map.Entry<String, List<TaskInstance>> group : taskGroup.entrySet()) {
                final Optional<StaffBrief> staffOptional =
                        staffService.getStaffByLoginName(group.getKey());
                if (!staffOptional.isPresent()) {
                    log.warn("【退料出库单待办汇总提醒】查询员工数据异常:{}", group.getKey());
                    continue;
                }
                final StaffBrief staffBrief = staffOptional.get();
                final Set<StockOutOrder> stockOutOrders = new LinkedHashSet<>();
                for (TaskInstance taskInstance : group.getValue()) {
                    final StockOutOrder stockOutOrder =
                            stringStockOutOrderHashMap.get(taskInstance.getId());
                    stockOutOrders.add(stockOutOrder);
                }
                final List<Long> stockOutOrderIds =
                        stockOutOrders.stream()
                                .map(StockOutOrder::getId)
                                .distinct()
                                .collect(Collectors.toList());
                stockOutOrderBizService.toDoSummaryReminder(
                        staffBrief.getQwUserId(), stockOutOrderIds);
                log.info("【退料出库单待办汇总提醒】发送成功:{} 退料出库单:{}", group.getKey(), stockOutOrderIds);
            }
        }
        log.info("【退料出库单待办汇总提醒】END");
    }
}
