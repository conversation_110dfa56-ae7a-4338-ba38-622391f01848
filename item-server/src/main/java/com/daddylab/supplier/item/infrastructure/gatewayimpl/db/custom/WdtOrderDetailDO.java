package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * wdt 通用订单查询返回封装
 *
 * <AUTHOR> up
 * @date 2022/4/13 4:12 下午
 */
@Data
public class WdtOrderDetailDO {

    /**
     * skuCode
     */
    private String specNo;
    /**
     * 数量
     */
    private BigDecimal num;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 组合商品sku
     * 如果不是组合商品sku,则为空
     */
    private String suiteNo;

    private String tradeNo;

    /**
     * 是否是赠品
     * 0、非赠品 1、自动赠送 2、手工赠送 4、周期购赠送 8、平台赠送
     */
    private Integer giftType;

    private String warehouseNo;

    /**
     * 平台ID
     */
    private Integer platformId;


}
