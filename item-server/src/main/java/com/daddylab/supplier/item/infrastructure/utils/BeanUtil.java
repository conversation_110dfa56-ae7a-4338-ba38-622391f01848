package com.daddylab.supplier.item.infrastructure.utils;

import cn.hutool.core.util.ReflectUtil;
import io.swagger.annotations.ApiModelProperty;
import org.javers.core.metamodel.annotation.PropertyName;
import org.springframework.core.annotation.MergedAnnotations;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2023/10/27
 */
public class BeanUtil {
  /**
   * 获取模型的可读属性名
   *
   * @param beanClass beanClass
   * @return Map
   */
  public static Map<String, String> getReadabilityPropertyNames(Class<?> beanClass) {
    return Arrays.stream(ReflectUtil.getFields(beanClass))
        .collect(
            Collectors.toMap(
                Field::getName,
                field -> {
                  MergedAnnotations annotations = MergedAnnotations.from(field);
                  Optional<String> value1 =
                      annotations.get(ApiModelProperty.class).getValue("value", String.class);
                  Optional<String> value2 =
                      annotations.get(PropertyName.class).getValue("value", String.class);
                  return Stream.of(value1, value2)
                      .filter(Optional::isPresent)
                      .map(Optional::get)
                      .map(
                          v -> {
                            final int i = v.indexOf(" ");
                            if (i > -1) {
                              return v.substring(0, i);
                            }
                            return v;
                          })
                      .findFirst()
                      .orElse(field.getName());
                }));
  }
}
