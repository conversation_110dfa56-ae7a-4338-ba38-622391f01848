package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 外部任务状态 0:未完成 1:完成 2:异常
 */
@Getter
@AllArgsConstructor
public enum ThirdPlatformSyncExternalTaskState implements IEnum<Integer> {
    /**
     * 未完成
     */
    WAITING(0, "未完成"),

    /**
     * 完成
     */
    FINISH(1, "完成"),

    /**
     * 任务运行结束
     */
    ERROR(2, "异常"),


    ;

    private final Integer value;
    private final String desc;

}
