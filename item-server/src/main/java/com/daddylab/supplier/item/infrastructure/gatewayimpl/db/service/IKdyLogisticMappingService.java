package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.KdyLogisticMapping;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 快刀云物流公司名称映射 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
public interface IKdyLogisticMappingService extends IDaddyService<KdyLogisticMapping> {

    Optional<String> getStdNameByLogisticsName(String logisticsName);

    List<String> inferStdName(String logisticsName);
}
