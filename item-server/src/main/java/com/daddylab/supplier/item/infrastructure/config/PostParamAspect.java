package com.daddylab.supplier.item.infrastructure.config;

import cn.hutool.core.util.URLUtil;
import com.daddylab.supplier.item.infrastructure.validators.link.HttpURL;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Objects;

/**
 * <AUTHOR> up
 * @date 2023年04月19日 5:22 PM
 */
@Aspect
@Component
public class PostParamAspect {

    private final String p1 = "@annotation(org.springframework.web.bind.annotation.PostMapping)";

    @Pointcut(value = p1)
    public void p1() {
        // TODO document why this method is empty
    }

    @Around("p1()")
    public Object doBefore(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        Object[] wrappers = new Object[args.length];
        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            try {
                Object o = stringTrim(arg);
                wrappers[i] = o;
            } catch (Exception e) {
                wrappers[i] = arg;
            }
        }
        return joinPoint.proceed(wrappers);
    }

    private static Object stringTrim(Object arg) throws Exception {
        if (arg instanceof String) {
            String string = arg.toString();
            if (StringUtils.isNotBlank(string)) {
                arg = string.trim();
                return arg;
            }
        }

        Class<?> argClass = arg.getClass();
        if (argClass.isPrimitive()) {
            return arg;
        }
        if (isWrapClass(argClass)) {
            return arg;
        }
        for (Field field : argClass.getDeclaredFields()) {
            field.setAccessible(true);
            if (field.getType().equals(String.class)) {
                Object o = field.get(arg);
                String val = (String) o;
                if (StringUtils.isNotBlank(val)) {
                    field.set(arg, val.trim());
                }
                HttpURL urlFlag = field.getAnnotation(HttpURL.class);
                if (Objects.nonNull(urlFlag)) {
                    field.set(arg, URLUtil.decode((String) field.get(arg)));
                }
            }
        }
        return arg;
    }

    private static boolean isWrapClass(Class<?> clz) {
        try {
            return ((Class<?>) clz.getField("TYPE").get(null)).isPrimitive();
        } catch (Exception e) {
            return false;
        }
    }

    public static void main(String[] args) throws Exception {
        String url = "https://myseller.laobashop.com/h5preview?src=https%3A%2F%2Fcms.daddylab.com%2Factivity%2Fproduct_details_preview%3Fid%3D12156";
        System.out.println(URLUtil.decode(url));
    }
}
