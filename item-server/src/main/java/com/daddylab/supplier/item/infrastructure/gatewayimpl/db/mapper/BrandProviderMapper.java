package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BrandProvider;

/**
 * <p>
 * 品牌供应商 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
public interface BrandProviderMapper extends DaddyBaseMapper<BrandProvider> {

}
