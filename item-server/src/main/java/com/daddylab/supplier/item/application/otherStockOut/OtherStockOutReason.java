package com.daddylab.supplier.item.application.otherStockOut;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/4/24
 */
@AllArgsConstructor
@Getter
public enum OtherStockOutReason implements IEnum<Integer> {
    UNMAPPING(-1, "未映射"),
    ALL(0, "未映射"),
    NONE(1, "无"),
    REFUND_LACK_ITEM(2, "退货少件出库"),
    REFUND_BROKEN(3, "退货破损出库"),
    FACTORY_VIRTUAL(4, "工厂虚拟出库"),
    ORDER_INLINE(5, "线下下单"),
    CS_DEMAND(6, "客服需求"),
    TRANSFER(7, "调拨出库"),
    DEADLINE(8, "临期出库"),
    BROKEN(9, "破损出库"),
    ;
    private final Integer value;
    private final String desc;
}
