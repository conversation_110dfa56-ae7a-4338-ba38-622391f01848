package com.daddylab.supplier.item.application.stockInOrder;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.Response;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.purchase.PurchaseOrderAPI;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.PurchaseOrderQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.PurchaseOrderQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.PurchaseOrderQueryWithDetailResponse.Order.Detail;
import com.daddylab.supplier.item.application.common.event.StockInOrOutEvent;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizService;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockInState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockInOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockInOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrganizationService;
import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.kingdee.dto.SubmitAndAuditRes;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 旺店通采购入库单同步任务
 *
 * <AUTHOR>
 * @since 2022/4/16
 */
@Component
@Slf4j
public class WdtPurchaseStockInOrderSyncTask {

    private final WdtGateway wdtGateway;
    private final StockInOrderMapper stockInOrderMapper;
    private final StockInOrderDetailMapper stockInOrderDetailMapper;
    private final PurchaseOrderMapper purchaseOrderMapper;
    @Autowired
    PurchaseOrderBizService purchaseOrderBizService;
    @Autowired
    KingDeeTemplate kingDeeTemplate;
    @Resource
    ReqTemplate reqTemplate;
    @Autowired
    OperateLogDomainService operateLogDomainService;

    @Autowired
    IOrganizationService iOrganizationService;

    public WdtPurchaseStockInOrderSyncTask(
            WdtGateway wdtGateway,
            StockInOrderMapper stockInOrderMapper,
            StockInOrderDetailMapper stockInOrderDetailMapper,
            PurchaseOrderMapper purchaseOrderMapper) {
        this.wdtGateway = wdtGateway;
        this.stockInOrderMapper = stockInOrderMapper;
        this.stockInOrderDetailMapper = stockInOrderDetailMapper;
        this.purchaseOrderMapper = purchaseOrderMapper;
    }

    @XxlJob("WdtPurchaseStockInOrderSyncTask")
    public void sync() {
        final PurchaseOrderAPI api = wdtGateway.getAPI(PurchaseOrderAPI.class);
        final LambdaQueryWrapper<StockInOrder> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(StockInOrder::getState,
                        Arrays.asList(
                                StockInState.WAIT_IN.getValue(),
                                StockInState.PART_IN.getValue()))
                .isNotNull(StockInOrder::getWdtStorageNo)
                .ne(StockInOrder::getWdtStorageNo, "")
        ;
        final List<StockInOrder> stockInOrders = stockInOrderMapper.selectList(queryWrapper);
        int failCount = 0;
        for (StockInOrder stockInOrder : stockInOrders) {

            final PurchaseOrderQueryWithDetailParams params = new PurchaseOrderQueryWithDetailParams();
            params.setPurchaseNo(stockInOrder.getWdtStorageNo());
            final Pager pager = new Pager(10, 0, true);
            try {
                final PurchaseOrderQueryWithDetailResponse resp = api
                        .queryWithDetail(params, pager);

                log.info("WdtPurchaseStockInOrderSyncTask no:{},res:{}", stockInOrder.getNo(), JsonUtil.toJson(resp));

                if (CollectionUtil.isEmpty(resp.getOrder())) {
                    continue;
                }
                final PurchaseOrderQueryWithDetailResponse.Order order = resp.getOrder().get(0);

                //采购单状态 采购单状态：10 已取消,20 编辑中,30 待审核,40 已审核,50 部分到货,60 已到货,70 待结算,80 部分结算,90 已完成 多个状态间使用英文逗号隔开
                if (order.getStatus() == 10) {
                    stockInOrder.setState(StockInState.CANCEL.getValue());
                    stockInOrderMapper.updateById(stockInOrder);
                    operateLogDomainService.addOperatorLog(0L,
                            OperateLogTarget.STOCK_OUT_ORDER, stockInOrder.getId(), "旺店通侧取消采购入库单");
                }

                if (order.getStatus() >= 40) {
                    //采购入库单交货数量旺店通同步无需修改
                    //stockInOrder.setTotalReceiptQuantity(order.getGoodsStockinCount().intValue());

                    boolean stateChange = false;

                    Integer state = null;
                    //入库状态 0 未入库 1部分入库 2已入库 3停止入库 4超量入库
                    switch (order.getStockinStatus()) {
                        case 1:
                        case 2:
                        case 4:
                            //入库状态走我们自己的逻辑（只要入库数量大于等于我们设置的采购量就算做全部入库）
                            if (order.getGoodsStockinCount().compareTo(order.getGoodsCount()) >= 0) {
                                state = StockInState.HAVE_IN.getValue();
                            } else {
                                state = StockInState.PART_IN.getValue();
                            }
                            break;
                        case 3:
                            state = StockInState.CANCEL.getValue();
                            break;
                    }

                    if (state != null && !state.equals(stockInOrder.getState())) {
                        stockInOrder.setState(state);
                        stockInOrderMapper.updateById(stockInOrder);
                        stateChange = true;
                    }

                    final LambdaQueryWrapper<StockInOrderDetail> detailQuery = Wrappers
                            .lambdaQuery();
                    detailQuery.eq(StockInOrderDetail::getStockInOrderId, stockInOrder.getId());
                    final List<StockInOrderDetail> stockInOrderDetails = stockInOrderDetailMapper
                            .selectList(detailQuery);
                    for (Detail detail : order.getDetailList()) {
                        final List<StockInOrderDetail> specOrderDetails = stockInOrderDetails
                                .stream()
                                .filter(item -> item.getItemSkuCode().equals(detail.getSpecNo()))
                                .collect(Collectors.toList());
                        int remainStockInNum = detail.getStockinNum().intValue();
                        for (int i = 0; i < specOrderDetails.size(); i++) {
                            final StockInOrderDetail orderDetail = specOrderDetails.get(i);
                            if (remainStockInNum <= 0) {
                                break;
                            }
                            Integer realReceiptQuantity;
                            //针对超量入库的情况，多出来的库存全部分配到最后一个明细上
                            if (i == specOrderDetails.size() - 1) {
                                realReceiptQuantity = remainStockInNum;
                                remainStockInNum = 0;
                            } else if (remainStockInNum >= orderDetail.getReceiptQuantity()) {
                                realReceiptQuantity = orderDetail.getReceiptQuantity();
                                remainStockInNum -= orderDetail.getReceiptQuantity();
                            } else {
                                realReceiptQuantity = remainStockInNum;
                                remainStockInNum = 0;
                            }
                            if (!Objects.equals(orderDetail.getRealReceiptQuantity(),
                                    realReceiptQuantity)) {
                                orderDetail.setRealReceiptQuantity(realReceiptQuantity);
                                orderDetail.calcPriceAndTaxAmount();
                                stockInOrderDetailMapper.updateById(orderDetail);
                                stateChange = true;
                            }
                        }
                    }

                    //统计当前入库单关联的采购单，当所有入库单总计入库商品数量大于采购单的采购数量时，将采购单的状态更新为已完结
                    purchaseOrderMapper
                            .autoUpdatePurchaseOrderState(stockInOrder.getPurchaseOrderId());

                    //采购入库单入库完成
                    if (stockInOrder.getState().equals(StockInState.HAVE_IN.getValue())) {
                        operateLogDomainService.addOperatorLog(0L, OperateLogTarget.STOCK_ORDER,
                                stockInOrder.getId(), "系统回传入库数量（入库完成）");

                        // 非系统生成的单据。不管采购类型，都推送到金蝶
                        Long buyerUserId = stockInOrder.getBuyerUserId();
                        if (-1 != buyerUserId) {
                            stockInOrder.setReceiptTime(DateUtil.currentTime());
                            stockInOrderMapper.updateById(stockInOrder);

                            boolean needSyncKingDee = iOrganizationService.isSyncKingDeeOrg(stockInOrder.getPurchaseOrganizationId());
                            if (needSyncKingDee) {
                                Response kingDeeRes;
                                if (StringUtils.isBlank(stockInOrder.getKingDeeId())) {
                                    kingDeeRes = kingDeeTemplate.handler(ApiEnum.SAVE_STOCK_IN_ORDER, stockInOrder.getId(), "");
                                } else {
                                    kingDeeRes = kingDeeTemplate.handler(ApiEnum.UPDATE_STOCK_IN_ORDER, stockInOrder.getId(), stockInOrder.getKingDeeId());
                                    SpringUtil.getBean(MsgSender.class).sendMsgToSevenUp(
                                            StrUtil.format("stockInOrder wdt callback. syncKingDee fail.no:{}", stockInOrder.getNo())
                                    );
                                }
                                operateLogDomainService.addOperatorLog(0L, OperateLogTarget.STOCK_ORDER,
                                        stockInOrder.getId(), "【创建单据】系统同步金蝶响应：" + kingDeeRes.isSuccess());
                                if (kingDeeRes.isSuccess()) {
                                    SubmitAndAuditRes submitAndAuditRes = reqTemplate.submitAndAuditStockOrder(ApiEnum.SAVE_STOCK_IN_ORDER.getFormId(),
                                            ListUtil.of(stockInOrder.getNo()));
                                    operateLogDomainService.addOperatorLog(0L, OperateLogTarget.STOCK_ORDER,
                                            stockInOrder.getId(), "【提交&审核单据】系统同步金蝶响应：" + submitAndAuditRes.getIsSuccess());
                                }
                            }
                        }

                        //通知应付单
                        final StockInOrOutEvent stockInOrOutEvent = StockInOrOutEvent
                                .ofNotice(PurchaseTypeEnum.IN_STOCK_PAYABLE, stockInOrder.getId(), stockInOrder.getBuyerUserId(), stockInOrder.getCreatedUid());
                        EventBusUtil.post(stockInOrOutEvent, true);
                    } else {

                        if (stateChange) {
                            operateLogDomainService.addOperatorLog(0L, OperateLogTarget.STOCK_ORDER,
                                    stockInOrder.getId(), "系统回传入库数量");
                        }
                    }
                }
            } catch (WdtErpException e) {
                failCount++;
                Alert.text(MessageRobotCode.GLOBAL, "同步旺店通采购入库单失败:" + e.getMessage());

                if (failCount > 10) {
                    Alert.text(MessageRobotCode.GLOBAL, "连续多次同步旺店通采购入库单失败:" + e.getMessage());
                    break;
                }
            }
        }
    }

}
