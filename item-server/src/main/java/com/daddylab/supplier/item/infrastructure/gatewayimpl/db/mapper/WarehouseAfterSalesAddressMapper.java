package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WarehouseAfterSalesAddress;

/**
 * <p>
 * 仓库售后退回地址 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
public interface WarehouseAfterSalesAddressMapper extends DaddyBaseMapper<WarehouseAfterSalesAddress> {

}
