package com.daddylab.supplier.item.domain.enterprise.query;
import com.daddylab.supplier.item.domain.enterprise.query.LaunchCodeQuery.SingleChat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Class  LaunchCodeQuery
 *
 * @Date 2022/5/30下午9:46
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class LaunchCodeQuery {

    @JsonProperty("operator_userid")
    private String operatorUserid;
    @JsonProperty("single_chat")
    private SingleChat singleChat;

    @NoArgsConstructor
    @Data
    public static class SingleChat {
        @JsonProperty("userid")
        private String userid;
    }

    /**
     * 构造对象
     *
     * @param operateUserId
     * @param chatUserId
     * @return
     */
    public static LaunchCodeQuery of(String operateUserId, String chatUserId) {
        LaunchCodeQuery launchCodeQuery = new LaunchCodeQuery();
        launchCodeQuery.setOperatorUserid(operateUserId);
        SingleChat singleChat = new SingleChat();
        singleChat.setUserid(chatUserId);
        launchCodeQuery.setSingleChat(singleChat);
        return launchCodeQuery;
    }
}
