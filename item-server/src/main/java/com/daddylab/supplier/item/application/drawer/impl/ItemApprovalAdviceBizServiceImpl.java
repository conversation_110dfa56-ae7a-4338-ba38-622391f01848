package com.daddylab.supplier.item.application.drawer.impl;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.drawer.ItemApprovalAdviceBizService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.drawer.vo.ItemApprovalAdviceVO;
import com.daddylab.supplier.item.domain.drawer.vo.ItemApprovalAdvicesVO;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemApprovalAdvice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemApprovalAdviceType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemApprovalAdviceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerService;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2022/10/28
 */
@Service
@AllArgsConstructor
public class ItemApprovalAdviceBizServiceImpl implements ItemApprovalAdviceBizService {

    private final IItemApprovalAdviceService itemApprovalAdviceService;
    private final OperateLogGateway operateLogGateway;
    private final IItemDrawerService itemDrawerService;

    @NonNull
    private Long getItemDrawerId(Long itemId) {
        final Long itemDrawerId = itemDrawerService.getItemDrawerIdByItemId(itemId);
        if (Objects.isNull(itemDrawerId)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "商品抽屉数据异常");
        }
        return itemDrawerId;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public SingleResponse<Long> add(Long itemId, ItemApprovalAdviceType type, String content) {
        final Long itemDrawerId = getItemDrawerId(itemId);
        final ItemApprovalAdvice entity = new ItemApprovalAdvice();
        entity.setItemId(itemId);
        entity.setType(type.getValue());
        entity.setAdvice(content);
        itemApprovalAdviceService.save(entity);
        operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.ITEM_DRAWER,
                itemDrawerId, "新增" + type.getDesc() + ":" + content, null);
        return SingleResponse.of(entity.getId());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Response remove(Long adviceId) {
        final ItemApprovalAdvice advice = itemApprovalAdviceService.getById(adviceId);
        if (Objects.isNull(advice)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "审批意见已删除");
        }
        final Long itemDrawerId = getItemDrawerId(advice.getItemId());
        itemApprovalAdviceService.removeByIdWithTime(adviceId);
        operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.ITEM_DRAWER,
                itemDrawerId,
                "移除" + IEnum.getEnumByValue(ItemApprovalAdviceType.class, advice.getType())
                        .getDesc() + ":" + advice.getAdvice(), null);
        return Response.buildSuccess();
    }

    @Override
    public SingleResponse<ItemApprovalAdvicesVO> getAllByItemId(Long itemId) {
        final ItemApprovalAdvicesVO itemApprovalAdvicesVO = new ItemApprovalAdvicesVO();
        final List<ItemApprovalAdvice> advices = itemApprovalAdviceService.lambdaQuery()
                .eq(ItemApprovalAdvice::getItemId, itemId).list();
        itemApprovalAdvicesVO.setTitleAdvice(advices.stream()
                .filter(v -> ItemApprovalAdviceType.TITLE_ADVICE.getValue().equals(v.getType()))
                .map(this::mapAdvicePoToVo).collect(
                        Collectors.toList()));
        itemApprovalAdvicesVO.setCopyAdvice(advices.stream()
                .filter(v -> ItemApprovalAdviceType.COPY_ADVICE.getValue().equals(v.getType()))
                .map(this::mapAdvicePoToVo).collect(
                        Collectors.toList()));
        itemApprovalAdvicesVO.setMainImageAdvice(advices.stream()
                .filter(v -> ItemApprovalAdviceType.MAIN_IMAGE_ADVICE.getValue()
                        .equals(v.getType()))
                .map(this::mapAdvicePoToVo).collect(
                        Collectors.toList()));
        return SingleResponse.of(itemApprovalAdvicesVO);
    }

    @Override
    public Response deleteAllByItemId(Long itemId) {
        final LambdaQueryWrapper<ItemApprovalAdvice> removeCond = Wrappers.lambdaQuery();
        removeCond.eq(ItemApprovalAdvice::getItemId, itemId);
        itemApprovalAdviceService.removeWithTime(removeCond);
        return Response.buildSuccess();
    }

    private ItemApprovalAdviceVO mapAdvicePoToVo(ItemApprovalAdvice v) {
        final ItemApprovalAdviceVO itemApprovalAdviceVO = new ItemApprovalAdviceVO();
        itemApprovalAdviceVO.setId(v.getId());
        itemApprovalAdviceVO.setAdvice(v.getAdvice());
        return itemApprovalAdviceVO;
    }
}
