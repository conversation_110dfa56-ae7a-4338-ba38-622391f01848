package com.daddylab.supplier.item.domain.itemStock.task;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.api.wms.dto.StockSearchResponse;
import com.daddylab.supplier.item.domain.item.data.SkuCodeInfo;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.itemStock.service.ItemStockDomainService;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.wdt.exception.WdtBizException;
import com.daddylab.supplier.item.domain.wdt.service.StockSyncService;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

@Component
@Slf4j
public class StockSyncTask {
    StockSyncService stockSyncService;
    ItemStockDomainService itemStockDomainService;
    ItemSkuGateway itemSkuGateway;

    @Autowired
    public StockSyncTask(StockSyncService stockSyncService, ItemStockDomainService itemStockDomainService, ItemSkuGateway itemSkuGateway) {
        this.stockSyncService = stockSyncService;
        this.itemStockDomainService = itemStockDomainService;
        this.itemSkuGateway = itemSkuGateway;
    }

    @XxlJob("StockSyncTask")
    public void doTask() {
        log.info("start");

        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        AtomicInteger continuousFailure = new AtomicInteger();
        final int continuousFailureThreshold = 3;

        try {
            itemSkuGateway.traverseSkuCodeInfo(100, (skuCodeInfos) -> {
                try {
                    final Function<SkuCodeInfo, String> getSkuSyncCode = it -> StringUtil.isNotBlank(it.getProviderSpecifiedCode()) ?
                            it.getProviderSpecifiedCode() :
                            it.getSkuCode();
                    final List<String> skuCodes = skuCodeInfos.stream().map(getSkuSyncCode).collect(Collectors.toList());
                    final Map<String, StockSearchResponse.StockSearchDto> stringStockSearchDtoMap = stockSyncService.stockQuery(skuCodes);
                    for (SkuCodeInfo skuCodeInfo : skuCodeInfos) {
                        final String skuSyncCode = getSkuSyncCode.apply(skuCodeInfo);
                        final StockSearchResponse.StockSearchDto stockSearchDto = stringStockSearchDtoMap.get(skuSyncCode);
                        if (Objects.nonNull(stockSearchDto)) {
                            final BigDecimal stockNum = stockSearchDto.getStockNum();
                            if (Objects.isNull(stockNum)) {
                                Alert.text(MessageRobotCode.GLOBAL, "旺店通返回的库存数量为NULL:" + skuSyncCode);
                                continue;
                            }
                            itemStockDomainService.stockSet(skuCodeInfo.getItemId(), skuCodeInfo.getSkuId(), stockNum.intValue());
                        }
                    }
                } catch (WdtErpException e) {
                    if (continuousFailure.get() < continuousFailureThreshold) {
                        continuousFailure.getAndIncrement();
                    } else {
                        throw new WdtBizException("调用旺店通库存查询接口连续多次失败:" + e.getMessage());
                    }
                }
            });
        } catch (WdtBizException e) {
            log.error("旺店通异常:{}", e.getMessage());
            Alert.text(MessageRobotCode.GLOBAL, "旺店通异常:" + ExceptionUtil.getSimpleStackString(e));
        } catch (Exception e) {
            log.error("库存同步时遇到未知异常", e);
            Alert.text(MessageRobotCode.GLOBAL, "库存同步时遇到未知异常:" + ExceptionUtil.getSimpleStackString(e));
        }

        stopWatch.stop();
        log.info("end time:{}ms", stopWatch.getTotalTimeMillis());
    }
}
