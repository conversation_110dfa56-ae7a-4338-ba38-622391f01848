package com.daddylab.supplier.item.infrastructure.third.kuaishou.impl;

import com.daddylab.supplier.item.application.platformItemSkuInventory.PlatformServiceFactoryAbstract;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.KuaiShouService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/4/8
 */
@Service
public class KuaiShouServiceFactory extends PlatformServiceFactoryAbstract<KuaiShouService> {
    @Override
    protected KuaiShouService getService() {
        return new KuaiShouServiceImpl();
    }

    @Override
    public Platform platform() {
        return Platform.KUAISHOU;
    }
}
