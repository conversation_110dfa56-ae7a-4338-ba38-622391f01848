package com.daddylab.supplier.item.controller.item.dto;

import com.daddylab.ark.sailor.item.domain.vo.category.CategoryListVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/9/16 10:55
 * @Description: 商品匹配小程序展示VO
 */
@Data
@ApiModel("ItemMatchMiniProgramVo-商品匹配小程序展示VO")
public class ItemMatchMiniProgramVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("商品名称")
    private String name;

    @ApiModelProperty("商品编码")
    private String itemNo;

    @ApiModelProperty("SKU数量")
    private Integer skuNum;

    @ApiModelProperty("平台商品数量")
    private Integer itemNum;

    @ApiModelProperty("小程序商品列表")
    private List<MiniProgramItemVo> miniProgramItemList;

    @ApiModelProperty(value = "类目ID")
    private Long categoryId;

    @ApiModelProperty(value = "类目信息")
    private CategoryListVO category;

    @ApiModelProperty(value = "店铺列表")
    private List<Shop> shopList;

    @ApiModelProperty(value = "当前店铺ID")
    private Long mallShopId;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(of = "id")
    public static class Shop implements Serializable {
        private static final long serialVersionUID = 1L;
        private Long id;
        private String name;
    }

    @Data
    @ApiModel("MiniProgramItemVo-小程序商品")
    public static class MiniProgramItemVo implements Serializable {
        private static final long serialVersionUID = 1L;

        private Long id;

        @ApiModelProperty("商品名称")
        private String name;

        @ApiModelProperty("商品价格")
        private BigDecimal price;

        @ApiModelProperty(value = "创建时间")
        private Long createdAt;

        @ApiModelProperty(value = "上架状态（状态参照小程序端定义：0-未上架，1-已上架，2-定时上架，3-已下架，4-定时下架）")
        private Integer shelfStatus;

        @ApiModelProperty(value = "审核状态（状态参照小程序端定义：10-待提审核，20-审核中，30-审核通过，40-审核不通过）")
        private Integer auditStatus;

        @ApiModelProperty(value = "类目ID")
        private Long categoryId;

        @ApiModelProperty(value = "类目信息")
        private CategoryListVO category;
    }
}
