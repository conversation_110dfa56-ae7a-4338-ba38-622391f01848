package com.daddylab.supplier.item.domain.purchase.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName PurchaseStatus.java
 * @description
 * @createTime 2021年11月12日 11:49:00
 */
@Getter
@AllArgsConstructor
public enum PurchaseStatus implements IEnum<Integer>{
    NO_CONFIRM(0, "待确认"),
    AL_CONFIRM(1, "已确认"),
    DISSENT(2, "存在异议");
    final public Integer value;
    final public String desc;
}



