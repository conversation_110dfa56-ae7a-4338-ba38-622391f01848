package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 店铺授权 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
public interface IShopAuthorizationService extends IDaddyService<ShopAuthorization> {

    void saveOrUpdateShopAuthorization(ShopAuthorization shopAuthorization);

    Optional<ShopAuthorization> getByShopNo(String shopNo);

    List<ShopAuthorization> listNotExpiredAuthorizations(Platform platform);
    
    List<ShopAuthorization> listAuthorizations(Platform platform);

    List<ShopAuthorization> listAuthorizationsSoonToExpire(Platform platform, int remainTtl);
}
