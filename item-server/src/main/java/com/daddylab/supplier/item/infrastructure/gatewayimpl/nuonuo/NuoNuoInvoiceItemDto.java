package com.daddylab.supplier.item.infrastructure.gatewayimpl.nuonuo;

import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2024年07月31日 2:09 PM
 */
@Data
public class NuoNuoInvoiceItemDto {

    private Integer blueDetailIndex;
    private String goodsName;
    private String num;
    private String taxRate;

    /**
     * 单价
     * isIncludeTax true 含税单价
     * isIncludeTax false 不含税单价
     */
    private String itemPrice;
    private String goodsCode;

    /**
     * 含税标志
     */
    private Boolean isIncludeTax;

    private String itemUnit;

    private String itemSelfCode;

}
