package com.daddylab.supplier.item.controller.message;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.message.MessageBizService;
import com.daddylab.supplier.item.application.message.param.ConfigCmd;
import com.daddylab.supplier.item.application.message.param.ListPageQuery;
import com.daddylab.supplier.item.application.message.vo.ConfigVoList;
import com.daddylab.supplier.item.application.message.vo.MessageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 2:23 下午
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/msg")
@Api(value = "消息相关API", tags = "消息相关API")
public class MessageController {

    @Autowired
    MessageBizService messageBizService;

    @ApiOperation("消息一键清除")
    @GetMapping("/removeAll")
    @ResponseBody
    @ApiImplicitParam(name = "recipientId", value = "接收者id")
    public SingleResponse<Boolean> removeAll(Long recipientId) {
        log.info("/msg/removeAll param:{}", recipientId);
        return messageBizService.removeAll(recipientId);
    }

    @ApiOperation("消息一键全读")
    @GetMapping("/allRead")
    @ResponseBody
    @ApiImplicitParam(name = "recipientId", value = "接收者id")
    public SingleResponse<Boolean> allRead(Long recipientId) {
        log.info("/msg/allRead param:{}", recipientId);
        return messageBizService.allRead(recipientId);
    }

    @ApiOperation("读取一条消息")
    @GetMapping("/read")
    @ResponseBody
    @ApiImplicitParam(name = "messageId", value = "消息id", required = true)
    public SingleResponse<Boolean> read(Long messageId) {
        log.info("/msg/read param:{}", messageId);
        return messageBizService.read(messageId);
    }

    @ApiOperation("未读消息数量")
    @GetMapping("/countNoRead")
    @ResponseBody
    @ApiImplicitParam(name = "recipientId", value = "接收者id")
    public SingleResponse<Integer> countNoRead(Long recipientId) {
        log.info("/msg/countNoRead param:{}", recipientId);
        return messageBizService.countNoRead(recipientId);
    }

    @ApiOperation("消息配置列表")
    @GetMapping("/config")
    @ResponseBody
    public SingleResponse<ConfigVoList> listMessageConfig() {
        return messageBizService.listMessageConfig();
    }

    @ApiOperation("消息列表")
    @PostMapping("/page")
    @ResponseBody
    public PageResponse<MessageVO> pageMessages(@RequestBody ListPageQuery pageQuery) {
        log.info("/msg/page param:{}", pageQuery);
        return messageBizService.pageMessages(pageQuery);
    }

    @ApiOperation("保存消息配置")
    @PostMapping("/saveConfig")
    @ResponseBody
    public SingleResponse<Boolean> saveConfig(@RequestBody List<ConfigCmd> configCmd) {
        log.info("/msg/saveConfig param:{}", configCmd);
        for (ConfigCmd cmd : configCmd) {
            messageBizService.saveConfig(cmd);
        }
        return SingleResponse.of(true);
    }

}
