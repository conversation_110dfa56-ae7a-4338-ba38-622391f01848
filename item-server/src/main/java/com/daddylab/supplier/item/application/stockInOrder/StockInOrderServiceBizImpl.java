package com.daddylab.supplier.item.application.stockInOrder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.purchase.PurchaseOrderAPI;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.PurchaseOrderCreateOrderParams;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.PurchaseOrderCreateOrderParams.PurchaseDetails;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.PurchaseOrderCreateOrderResponse;
import com.daddylab.supplier.item.application.common.event.StockInOrOutEvent;
import com.daddylab.supplier.item.application.criticalPush.ManagedPush;
import com.daddylab.supplier.item.application.criticalPush.SourceType;
import com.daddylab.supplier.item.application.criticalPush.TargetType;
import com.daddylab.supplier.item.application.item.combinationItem.CombinationItemBizService;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuVO;
import com.daddylab.supplier.item.application.provider.ProviderBizService;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizService;
import com.daddylab.supplier.item.application.system.BaseInfoService;
import com.daddylab.supplier.item.application.wdt.WdtPurchaseBizService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.enums.DetailModelType;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.controller.common.dto.WarehousePageQuery;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.controller.provider.dto.ProviderVO;
import com.daddylab.supplier.item.domain.auth.enums.ResourceType;
import com.daddylab.supplier.item.domain.departments.DepartmentGateway;
import com.daddylab.supplier.item.domain.exportTask.ExportDomainService;
import com.daddylab.supplier.item.domain.exportTask.ExportType;
import com.daddylab.supplier.item.domain.item.gateway.ItemPriceGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.stockInOrder.dto.*;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderStockQuery;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.StockInOrderQueryDto;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PurchaseOrderState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockInState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.kingdee.util.HttpUtil;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqJsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 采购入库服务实现
 *
 * <AUTHOR>
 * @date 2022/3/24 16:52
 **/
@Slf4j
@Service
public class StockInOrderServiceBizImpl implements StockInOrderBizService {

    @Autowired
    CombinationItemBizService combinationItemBizService;
    @Autowired
    ItemPriceGateway itemPriceGateway;
    @Autowired
    IPurchaseOrderDetailService iPurchaseOrderDetailService;
    @Resource
    PurchaseOrderMapper purchaseOrderMapper;
    @Autowired
    KingDeeTemplate kingDeeTemplate;
    @Autowired
    private ExportDomainService exportDomainService;
    @Autowired
    private StockInOrderMapper stockInOrderMapper;
    @Autowired
    private StockInOrderDetailMapper stockInOrderDetailMapper;
    @Autowired
    private OperateLogDomainService operateLogDomainService;
    @Resource
    private WarehouseMapper warehouseMapper;
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private GroupMapper groupMapper;
    @Autowired
    private ProviderGateway providerGateway;
    @Resource
    private DepartmentGateway departmentGateway;
    @Autowired
    private PurchaseOrderBizService purchaseOrderBizService;
    @Resource
    private PurchaseOrderDetailMapper purchaseOrderDetailMapper;
    @Autowired
    private UserGateway userGateway;
    @Autowired
    private WdtGateway wdtGateway;
    @Autowired
    private ProviderBizService providerBizService;
    @Resource
    private BaseInfoService baseInfoService;
    @Autowired
    private IWarehouseService warehouseService;
    @Autowired
    private WdtPurchaseBizService wdtPurchaseBizService;
    @Autowired
    IStockInOrderService iStockInOrderService;
    @Autowired
    private IPurchaseOrderService iPurchaseOrderService;

    @Resource
    IStockInOrderDetailService iStockInOrderDetailService;

    @Resource
    ReqJsonUtil reqJsonUtil;

    @Resource
    HttpUtil httpUtil;

    @Resource
    IWarehouseService iWarehouseService;

    @Resource
    IBizLevelDivisionService iBizLevelDivisionService;

    /**
     * 新建采购入库单和更新采购入库单
     *
     * @param stockInOrderAndOrderDetail
     * @return com.alibaba.cola.dto.Response
     * <AUTHOR>
     * @date 2022/3/29 14:03
     **/
    @Override
    @Transactional(rollbackFor = Throwable.class)
//    @DistributedLock(searchKey = SearchKeyStrategy.PARAMETER_PROPERTY)
    public SingleResponse<String> createStockInOrder(StockInOrderAndOrderDetail stockInOrderAndOrderDetail, Boolean isSys, Boolean mockSync) {
        //验证采购入库必要参数
        if (stockInOrderAndOrderDetail != null) {

            if (!isSys) {
                // [20230919 七喜]验证合作模式是否匹配权限
                Long purchaseOrderId = stockInOrderAndOrderDetail.getPurchaseOrderId();
                Assert.notNull(purchaseOrderId, "必须存在关联的采购订单");
                PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(purchaseOrderId);
                Assert.notNull(purchaseOrder, "必须存在关联的采购订单");
                boolean contains = UserPermissionJudge.getUserBusinessLineValues().contains(purchaseOrder.getBusinessLine());
                Assert.isTrue(contains, "采购入库单不支持多种合作模式混合入库");
                stockInOrderAndOrderDetail.setBusinessLine(purchaseOrder.getBusinessLine());
            }

            //前端传入库日期
//            Long inStockDate = stockInOrderAndOrderDetail.getReceiptTime();
            //当日日期
//            Long presentDate = DateUtil.todayZeroTime();
            //判断入库单入库日期是否小于当日
//            if (stockInOrderAndOrderDetail.getOperationType() != 2) {
//                if (inStockDate.compareTo(presentDate) < 0) {
//                    throw ExceptionPlusFactory.bizException("入库日期不能小于当日!!!");
//                }
//            }

            //判断是否存在，存在即为更新
//            StockInOrder stockInOrderQuery = stockInOrderMapper.selectById(stockInOrderAndOrderDetail.getId());
            boolean isAdd = Objects.isNull(stockInOrderAndOrderDetail.getId());
            StockInOrder stockInOrder = getBean(stockInOrderAndOrderDetail);

//            List<BusinessLine> userBusinessLine = UserPermissionJudge.getUserBusinessLine();
//            stockInOrder.setBusinessLine();

            List<StockInOrderDetail> stockInOrderDetails = getBeans(stockInOrderAndOrderDetail);
            //同一个采购入库单，只允许入同一个仓库
            if (!itemStockOnlyCheck(stockInOrderDetails)) {
                return SingleResponse.buildFailure("false", "同一个入库单商品不允许入库两个及多个仓库");
            }

            //更新
            if (!isAdd) {
                StockInOrder oldOne = stockInOrderMapper.selectById(stockInOrderAndOrderDetail.getId());
//                if (stockInOrderAndOrderDetail.getOperationType() != 2) {
//                    if (!((checkStockInOrderNum(stockInOrder.getPurchaseOrderId())) - stockInOrder.getTotalReceiptQuantity() >= 0)) {
//                        return SingleResponse.buildFailure("false", "入库数量超过采购单入库数量或采购单不存在");
//                    }
//                }
                if (isPurchaseOrderClose(oldOne.getPurchaseOrderId())) {
                    return SingleResponse.buildFailure("false", "采购单已关闭");
                }
                return updateStockInOrder(stockInOrderAndOrderDetail, oldOne, stockInOrder, stockInOrderDetails);
            }

            // 新建入库单处理。
            else {
                // 非冲销创建采购单，是业务创建。需要进行业务数据验证，必须全ture才行
//                if (!isHedge) {
//                    boolean b1 = checkStockInOrderInNum(stockInOrder.getPurchaseOrderId(), stockInOrder.getTotalReceiptQuantity()) >= 0;
//                    boolean b2 = purchaseOrderStatus(stockInOrder.getPurchaseOrderId());
//                    if (!b1 || !b2) {
//                        return SingleResponse.buildFailure("false", "入库数量超过采购单入库数量或采购单不存在");
//                    }
//                }
                return addStockInOrder(stockInOrder, stockInOrderAndOrderDetail, stockInOrderDetails, mockSync);
            }
        }
        return SingleResponse.buildFailure("false", "请求参数不合法");
    }

    private boolean updatePurchaseOrderStatus(Long purchaseOrderId) {
        PurchaseOrder purchaseOrder = purchaseOrderMapper.selectById(purchaseOrderId);
        return purchaseOrder.getState().equals(PurchaseOrderState.CLOSED.getValue());
    }

    private boolean isPurchaseOrderClose(Long purchaseOrderId) {
        PurchaseOrder purchaseOrder = purchaseOrderMapper.selectById(purchaseOrderId);
        return PurchaseOrderState.CLOSED.getValue().equals(purchaseOrder.getState());
    }


    private boolean purchaseOrderStatus(Long purchaseOrderId) {
        PurchaseOrder purchaseOrder = purchaseOrderMapper.selectById(purchaseOrderId);
        if (purchaseOrder.getState().equals(PurchaseOrderState.FINISHED.getValue())) {
            //采购单状态为已完结时，并且类型为工厂时，则为自动入库单
            return purchaseOrder.getState().equals(PurchaseOrderState.FINISHED.getValue()) && purchaseOrder.getType() == 2;
        }
        return purchaseOrder.getState().equals(PurchaseOrderState.PASS_AUDIT.getValue()) || purchaseOrder.getState().equals(PurchaseOrderState.WAIT_FINISH.getValue());
    }

    private SingleResponse<String> addStockInOrder(StockInOrder stockInOrder, StockInOrderAndOrderDetail stockInOrderAndOrderDetail,
                                                   List<StockInOrderDetail> stockInOrderDetails, Boolean mockSync) {
        stockInOrder.setNo(stockInOrderNo());
//        stockInOrder.setIsDel(stockInOrder.getIsDel() == null ? 0 : stockInOrder.getIsDel());
        stockInOrder.setKingDeeId("");
        //获取采购类型 true 标准 false 工厂
        boolean purchaseOrderType = isPurchaseOrderType(stockInOrder.getPurchaseOrderId());
        //入库单状态设置
        stockInOrder.setState(checkStockInOrderInStatus(purchaseOrderType, stockInOrderAndOrderDetail.getOperationType(), stockInOrder.getIsSyncWdt()));
        //操作状态为提交时，且不同步旺店通的直接修改实际入库
        if (stockInOrderAndOrderDetail.getOperationType() == 1 && stockInOrder.getIsSyncWdt() == 0) {
            //入库单实际入库为商品交货总数
            stockInOrder.setTotalReceiptQuantity(getItemNum(stockInOrderDetails));
        }
        iStockInOrderService.save(stockInOrder);
        operateLogDomainService
                .addOperatorLog(UserContext.getUserId(),
                        OperateLogTarget.STOCK_ORDER, stockInOrder.getId(),
                        "新建了采购入库单"
                );
//        long stockInOrderId = stockInOrderMapper.selectByNo(stockInOrder.getNo());
        long stockInOrderId = stockInOrder.getId();
        stockInOrderAndOrderDetail.setId(stockInOrderId);

        Map<String, Integer> purchaseQuantityMap = purchaseQuantityMap(stockInOrder.getPurchaseOrderId());

        iStockInOrderDetailService.saveBatch(stockInOrderDetails.stream()
                .map(val -> setValues(val, stockInOrderId, stockInOrder, "add", null, null, purchaseQuantityMap))
                .collect(Collectors.toList()));
//        if (createNumbers > 0) {
//            for (StockInOrderDetail stockInOrderDetail : stockInOrderDetails) {
//                setValues(stockInOrderDetail, stockInOrderId, stockInOrder, "add", null, null);
////                log.info("插入采购入库单明细数据:{}", stockInOrderDetail);
//                stockInOrderDetailMapper.insert(stockInOrderDetail);
//            }
//        }
        //同步旺店通
        if (stockInOrder.getState().equals(StockInState.WAIT_IN.getValue()) && stockInOrder.getIsSyncWdt().equals(1)) {
            stockInOrderAndOrderDetail.setNo(stockInOrder.getNo());
            SingleResponse singleResponse = synWDT(stockInOrderAndOrderDetail);
            if (!singleResponse.isSuccess()) {
                //旺店通同步失败时，需要删除该入库单以及对应明细
//                deleteStockInOrderOrDetail(stockInOrderId);
                operateLogDomainService
                        .addOperatorLog(UserContext.getUserId(),
                                OperateLogTarget.STOCK_ORDER, stockInOrder.getId(),
                                "同步WDT失败" + singleResponse.getErrMessage());
                return SingleResponse.buildFailure(singleResponse.getErrCode(), "旺店通-" + singleResponse.getErrMessage());
            }
        }
        // 入库单状态为已入库，采购单对应入库单数量
        if (stockInOrder.getState() == 3) {
//            if (!purchaseOrderType) {
            if (!mockSync) {
                if(SpringUtil.getActiveProfile().equals("test")){
                    return SingleResponse.buildSuccess();
                }
                //虚拟入库同步金蝶
                Response kingDeeResponse = ((StockInOrderServiceBizImpl) AopContext.currentProxy()).pushKingDee(stockInOrder);
                // 同步金蝶失败
                if (!kingDeeResponse.isSuccess()) {
                    operateLogDomainService
                            .addOperatorLog(UserContext.getUserId(),
                                    OperateLogTarget.STOCK_ORDER, stockInOrder.getId(),
                                    "同步金蝶失败，请手动处理");
//                    deleteStockInOrderOrDetail(stockInOrderId);
                    return SingleResponse.buildFailure(ErrorCode.API_RESP_ERROR.getCode(), kingDeeResponse.getErrMessage());
                }
            }

            //验证当前采购单关联入库单是否存在已入库情况
            if (checkPurchaseOrderAsStockInOrder(stockInOrder.getPurchaseOrderId())) {
                //修改采购单状态
                PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(stockInOrder.getPurchaseOrderId());
                if (!PurchaseOrderState.FINISHED.getValue().equals(purchaseOrder.getState())) {
                    // 如果是从冲销流程进入，针对冲销的单据，不需要更新采购订单状态
                    if (!PurchaseOrderState.FINISH_FIXED.getValue().equals(purchaseOrder.getState())) {
                        purchaseOrderBizService.updateState(stockInOrder.getPurchaseOrderId(), checkStockInOrderNum(stockInOrder.getPurchaseOrderId()) > 0
                                ? PurchaseOrderState.WAIT_FINISH.getValue() : PurchaseOrderState.FINISHED.getValue());
                    }
                }
            }
            //通知应付单
            final StockInOrOutEvent stockInOrOutEvent = StockInOrOutEvent
                    .ofNotice(PurchaseTypeEnum.IN_STOCK_PAYABLE, stockInOrder.getId(), stockInOrder.getBuyerUserId(), stockInOrder.getCreatedUid());
            EventBusUtil.post(stockInOrOutEvent, true);
        }
        return SingleResponse.of(stockInOrder.getNo());
    }

    private Map<String, Integer> purchaseQuantityMap(Long purchaseOrderId) {
        return iPurchaseOrderDetailService.lambdaQuery()
                .eq(PurchaseOrderDetail::getPurchaseOrderId, purchaseOrderId)
                .list()
                .stream().collect(Collectors.toMap(
                        purchaseOrderDetail -> purchaseOrderDetail.getItemSkuCode() + "|" + purchaseOrderDetail.getTaxPrice(),
                        PurchaseOrderDetail::getPurchaseQuantity));

    }

    /**
     * 入库明细相关值计算
     *
     * @param stockInOrderDetail
     * @param stockInOrderId
     * @param stockInOrder
     * @param str
     * @param backupStockInOrder
     * @param backupStockInOrderDetail
     * @return com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrderDetail
     * <AUTHOR>
     * @date 2022/5/25 18:29
     **/
    private StockInOrderDetail setValues(StockInOrderDetail stockInOrderDetail, long stockInOrderId, StockInOrder stockInOrder,
                                         String str, StockInOrder backupStockInOrder, List<StockInOrderDetail> backupStockInOrderDetail,
                                         Map<String, Integer> purchaseQuantityMap) {
        stockInOrderDetail.setStockInOrderId(stockInOrderId);
//        Integer receiptQuantity = stockInOrderDetail.getReceiptQuantity() == null ? stockInOrder.getPurchaseQuantity() : stockInOrderDetail.getReceiptQuantity();

        // 2025-01-20 入库数量不得大于采购数量的1.1倍。四舍五入。
        Integer purchaseQuantity = purchaseQuantityMap.getOrDefault(stockInOrderDetail.getItemSkuCode() +
                "|" + stockInOrderDetail.getTaxPrice().setScale(6, RoundingMode.DOWN), 0);
        BigDecimal max = new BigDecimal(purchaseQuantity).multiply(new BigDecimal("1.1")).setScale(0, RoundingMode.HALF_UP);
        Integer receiptQuantity = stockInOrderDetail.getReceiptQuantity();
        if (new BigDecimal(receiptQuantity).compareTo(max) > 0) {
            throw ExceptionPlusFactory.bizException("sku[" + stockInOrderDetail.getItemSkuCode() + "]入库数【" + receiptQuantity + "】超出采购数量110%--入库单创建失败");
        }

        if ("add".equals(str)) {
            //sku校验数量,
            // fixme sevenUp 去掉这个校验，暂时没看懂这个逻辑校验。
//            if (checkPurchaseOrderSkuAsStockInOrderSku(stockInOrder.getPurchaseOrderId(), stockInOrderDetail.getItemSkuCode(), receiptQuantity, stockInOrder.getId())) {
//                deleteStockInOrderOrDetail(stockInOrderId);
//                throw ExceptionPlusFactory.bizException("sku[" + stockInOrderDetail.getItemSkuCode() + "]入库数【" + receiptQuantity + "】小于采购单可入库总数--入库单创建失败");
//            }
        }
        if ("update".equals(str)) {
//            //sku校验数量
//            if (checkPurchaseOrderSkuAsStockInOrderSku(stockInOrder.getPurchaseOrderId(), stockInOrderDetail.getItemSkuCode(), receiptQuantity, stockInOrder.getId())) {
//                backup(backupStockInOrder, backupStockInOrderDetail);
//                throw ExceptionPlusFactory.bizException("sku[" + stockInOrderDetail.getItemSkuCode() + "]入库数【" + receiptQuantity + "】小于采购单可入库总数--数据已回滚");
//            }
        }
        stockInOrderDetail.setIsDel(stockInOrder.getIsDel() == null ? 0 : stockInOrder.getIsDel());
        stockInOrderDetail.setStockState(1);
        //状态为已入库时计算实际入库
        if (stockInOrder.getState().equals(StockInState.HAVE_IN.getValue())) {
            stockInOrderDetail.setRealReceiptQuantity(stockInOrderDetail.getReceiptQuantity());
        } else {
            stockInOrderDetail.setRealReceiptQuantity(0);
        }
        stockInOrderDetail.calcPriceAndTaxAmount();
        return stockInOrderDetail;
    }

    /**
     * sku最后剩余入库数是否满足入库
     *
     * @param purchaseOrderId  - 采购单ID
     * @param itemSkuCode      - 商品sku
     * @param purchaseQuantity -本次入库数
     * @param id
     * @return boolean
     * <AUTHOR>
     * @date 2022/5/25 17:37
     **/
    private boolean checkPurchaseOrderSkuAsStockInOrderSku(Long purchaseOrderId, String itemSkuCode, Integer purchaseQuantity, Long id) {
        //查询采购单明细
        QueryWrapper<PurchaseOrderDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("purchase_order_id", purchaseOrderId);
        queryWrapper.eq("item_sku_code", itemSkuCode);
        queryWrapper.eq("is_del", 0);
        List<PurchaseOrderDetail> purchaseOrderDetailList = purchaseOrderDetailMapper.selectList(queryWrapper);
        //采购单明细sku总数
        int purchaseOrderDetailSkuNum = 0;
        if (purchaseOrderDetailList.size() > 0) {
            for (PurchaseOrderDetail purchaseOrderDetail : purchaseOrderDetailList) {
                purchaseOrderDetailSkuNum = purchaseOrderDetailSkuNum + purchaseOrderDetail.getPurchaseQuantity();
            }
        }
        //查询入库单明细
        QueryWrapper<StockInOrderDetail> stockInOrderDetailQueryWrapper = new QueryWrapper<>();
        stockInOrderDetailQueryWrapper.eq("stock_in_order_id", id);
        stockInOrderDetailQueryWrapper.eq("item_sku_code", itemSkuCode);
        stockInOrderDetailQueryWrapper.eq("is_del", 0);
        List<StockInOrderDetail> stockInOrderDetailList = stockInOrderDetailMapper.selectList(stockInOrderDetailQueryWrapper);
        //已入库单明细sku总数
        int stockInOrderDetailSkuNum = 0;
        if (stockInOrderDetailList.size() > 0) {
            for (StockInOrderDetail stockInOrderDetail : stockInOrderDetailList) {
                stockInOrderDetailSkuNum = stockInOrderDetailSkuNum + stockInOrderDetail.getReceiptQuantity();
            }
        }
        //sku最后剩余入库数 =未入库数（采购sku总数-已入库sku总数）-本次入库数
        return (purchaseOrderDetailSkuNum - stockInOrderDetailSkuNum) - purchaseQuantity < 0;
    }

    /**
     * 直接提交同步网店失败，删除入库单以及明细
     *
     * @param stockInOrderId
     * <AUTHOR>
     * @date 2022/5/25 17:39
     **/
    private void deleteStockInOrderOrDetail(long stockInOrderId) {
        int updateStockInOrder = stockInOrderMapper.updateByStockInOrderId(stockInOrderId);
        operateLogDomainService.addOperatorLog(UserContext.getUserId(), OperateLogTarget.STOCK_ORDER, stockInOrderId, (!UserContext.getUserInfo().isPresent() ? "系统" : UserContext.getUserInfo().get().getUserName()) + "删除了采购入库单");
        if (updateStockInOrder > 0) {
            List<StockInOrderDetail> stockInOrderDetails = stockInOrderDetailMapper.selectByStockInOrderDetailId(stockInOrderId);
            if (stockInOrderDetails.size() > 0) {
                for (StockInOrderDetail stockInOrderDetail : stockInOrderDetails) {
                    stockInOrderDetailMapper.delByStockInOrderDetailId(stockInOrderDetail.getId());
                    operateLogDomainService.addOperatorLog(UserContext.getUserId(), OperateLogTarget.STOCK_ORDER, stockInOrderId, (!UserContext.getUserInfo().isPresent() ? "系统" : UserContext.getUserInfo().get().getUserName()) + "删除了采购入库单明细");
                }
            }
        }
    }

    /**
     * 检查采购单关联入库单已入库数量是否大于0
     *
     * @param purchaseOrderId
     * @return boolean
     * <AUTHOR>
     * @date 2022/5/23 18:41
     **/
    private boolean checkPurchaseOrderAsStockInOrder(Long purchaseOrderId) {
        QueryWrapper<StockInOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", 0);
        queryWrapper.eq("state", StockInState.HAVE_IN.getValue());
        queryWrapper.eq("purchase_order_id", purchaseOrderId);
        Integer selectCount = stockInOrderMapper.selectCount(queryWrapper);
        return selectCount > 0;
    }

    @ManagedPush(sourceType = SourceType.PURCHASE_STOCK_IN_ORDER, targetType = TargetType.KINGDEE, idArgProp = "id")
    public Response pushKingDee(StockInOrder stockInOrder) {
        boolean syncAccess = stockInOrder.getPurchaseOrganizationId().equals(29L) || stockInOrder.getPurchaseOrganizationId().equals(4L);
        if (syncAccess) {
            if (StringUtils.isBlank(stockInOrder.getKingDeeId())) {
                return kingDeeTemplate.handler(ApiEnum.SAVE_STOCK_IN_ORDER, stockInOrder.getId(), "");
            }
            return kingDeeTemplate.handler(ApiEnum.UPDATE_STOCK_IN_ORDER, stockInOrder.getId(), stockInOrder.getKingDeeId());
        } else {
            return Response.buildSuccess();
        }
    }

    /**
     * 入库单状态选择器
     *
     * @param purchaseOrderType true 标准 false 工厂
     * @param operationType     0 保存 1 提交
     * @param isSyncWdt         0 不同步 1 同步
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2022/5/17 15:10
     **/
    private Integer checkStockInOrderInStatus(boolean purchaseOrderType, Integer operationType, @NotNull Integer isSyncWdt) {
        if (purchaseOrderType) {
            //标准发货
            if (isSyncWdt == 1) {
                //同步旺店通
                if (operationType == 0) {
                    //保存
                    // 采购单类型是标准发货，新增入库单，同步旺店通，保存，状态-待提交
                    return StockInState.WAIT_SUBMIT.getValue();
                } else {
                    //提交
                    // 采购单类型是标准发货，新增入库单，同步旺店通，提交，状态-待入库
                    return StockInState.WAIT_IN.getValue();
                }
            } else {
                //不同步旺店通
                if (operationType == 0) {
                    //保存
                    // 采购单类型是标准发货，新增入库单，不同步旺店通，保存，状态-待提交
                    return StockInState.WAIT_SUBMIT.getValue();
                }
                if (operationType == 1) {
                    //提交
                    //采购单类型是标准发货，新增入库单，不同步旺店通，提交，状态-待入库
                    return StockInState.WAIT_IN.getValue();
                }
                //手动入库-状态-已入库
                return StockInState.HAVE_IN.getValue();
            }
        } else {
            //工厂发货
            //不同步旺店通
            if (operationType == 0) {
                //保存
                // 采购单类型是工厂发货，新增入库单，保存，状态-待提交
                return StockInState.WAIT_SUBMIT.getValue();
            } else {
                // 采购单类型是工厂发货，新增入库单，提交，状态-已入库
                return StockInState.HAVE_IN.getValue();
            }
        }
    }

    private Integer getItemNum(List<StockInOrderDetail> stockInOrderDetails) {
        int itemNum = 0;
        for (StockInOrderDetail stockInOrderDetail : stockInOrderDetails) {
            itemNum = itemNum + stockInOrderDetail.getReceiptQuantity();
        }
        return itemNum;
    }

    private SingleResponse<String> updateStockInOrder(StockInOrderAndOrderDetail stockInOrderAndOrderDetail, StockInOrder stockInOrderQuery, StockInOrder stockInOrder, List<StockInOrderDetail> stockInOrderDetails) {
        // 角色权限校验
        Boolean financialExecutive = UserPermissionJudge.isFinancialExecutive();
        if (financialExecutive) {
            Assert.state(UserContext.getUserId().equals(stockInOrderQuery.getCreatedUid()), "当前用户角色只是财务主管。财务主管不允许编辑他人采购入库单");
        }
        if (stockInOrderQuery.getState().equals(StockInState.WAIT_SUBMIT.getValue()) || stockInOrderQuery.getState().equals(StockInState.CANCEL.getValue()) || stockInOrderQuery.getState().equals(StockInState.WAIT_IN.getValue())) {
            //增加备份数据
            StockInOrder backupStockInOrder = stockInOrderMapper.selectById(stockInOrder.getId());
            List<StockInOrderDetail> backupStockInOrderDetail = stockInOrderDetailMapper.selectByStockInOrderDetailId(stockInOrder.getId());

            //获取采购类型 true 标准 false 工厂
            boolean purchaseOrderType = isPurchaseOrderType(stockInOrder.getPurchaseOrderId());
            int state = checkStockInOrderInStatus(purchaseOrderType, stockInOrderAndOrderDetail.getOperationType(), stockInOrder.getIsSyncWdt());
            //入库单状态设置
            stockInOrder.setState(state);
            // 20240618 七喜 如果是手动入库，入库时间填充。
            if (stockInOrderAndOrderDetail.getOperationType() == 2) {
                stockInOrder.setReceiptTime(DateUtil.currentTime());
            }
            int updateNumbers = stockInOrderMapper.updateById(stockInOrder);
            operateLogDomainService
                    .addOperatorLog(UserContext.getUserId(),
                            OperateLogTarget.STOCK_ORDER,
                            stockInOrder.getId(),
                            (!UserContext.getUserInfo().isPresent() ? "系统" : (stockInOrderAndOrderDetail.getOperationType() == 2 ? (UserContext.getUserInfo().get().getUserName() + "提交了手动入库") : (UserContext.getUserInfo().get().getUserName() + "更新了采购入库单"))));
            if (updateNumbers > 0) {
                //更新明细先删除明细
                List<StockInOrderDetail> databaseStockInOrderDetails = stockInOrderDetailMapper.selectByStockInOrderDetailId(stockInOrder.getId());
                if (stockInOrderDetails.size() > 0) {
                    for (StockInOrderDetail databaseStockInOrderDetail : databaseStockInOrderDetails) {
                        stockInOrderDetailMapper.delByStockInOrderDetailId(databaseStockInOrderDetail.getId());
                    }
                }
                //后插入新明细
                Map<String, Integer> purchaseQuantityMap = purchaseQuantityMap(stockInOrder.getPurchaseOrderId());
                for (StockInOrderDetail stockInOrderDetail : stockInOrderDetails) {
                    setValues(stockInOrderDetail, stockInOrder.getId(), stockInOrder, "update", backupStockInOrder, backupStockInOrderDetail, purchaseQuantityMap);
                    stockInOrderDetailMapper.insert(stockInOrderDetail);
                }
            }
            //同步旺店通
            if (stockInOrder.getState().equals(StockInState.WAIT_IN.getValue()) && stockInOrder.getIsSyncWdt().equals(1)) {
                SingleResponse singleResponse = synWDT(stockInOrderAndOrderDetail);
                if (!singleResponse.isSuccess()) {
                    //同步失败回滚采购单入库单和明细以及采购单
                    //存入旺店通同步失败信息
                    backupStockInOrder.setSyncFailDetail("单据推送旺店通失败,请编辑后提交推送！【" + singleResponse.getErrMessage() + "】");
                    //同步失败回滚采购单入库单和明细以及采购单
                    backup(backupStockInOrder, backupStockInOrderDetail);
                    return SingleResponse.buildFailure(singleResponse.getErrCode(), "旺店通-" + singleResponse.getErrMessage());
                }
            }
            //入库单状态为已入库，采购单对应入库单数量
            if (state == 3) {
                if (!purchaseOrderType) {
                    //虚拟入库同步金蝶
                    Response response = ((StockInOrderServiceBizImpl) AopContext.currentProxy()).pushKingDee(stockInOrder);
                    if (!response.isSuccess()) {
                        backupStockInOrder.setSyncFailDetail("单据推送金蝶失败,请编辑后提交推送！【" + response.getErrMessage() + "】");
                        backup(backupStockInOrder, backupStockInOrderDetail);
                        return SingleResponse.buildFailure(response.getErrCode(), "金蝶-" + response.getErrMessage());
                    }
                }
                // 验证当前采购单关联入库单是否存在已入库情况
                if (checkPurchaseOrderAsStockInOrder(stockInOrder.getPurchaseOrderId())) {
                    //修改采购单状态，考虑采购单手动完结的情况，这时无需再更改采购单状态。
                    PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(stockInOrder.getPurchaseOrderId());
                    if (!PurchaseOrderState.FINISHED.getValue().equals(purchaseOrder.getState())) {
                        // 如果采购单数量大于已入库+部分入库的数量，则将采购单更新为待完结。采购单数量=入库单+部分入库；采购单完结。
                        purchaseOrderBizService.updateState(stockInOrder.getPurchaseOrderId(), checkStockInOrderNum(stockInOrder.getPurchaseOrderId()) > 0
                                ? PurchaseOrderState.WAIT_FINISH.getValue() : PurchaseOrderState.FINISHED.getValue());
                    }
                }
                //通知应付单
                final StockInOrOutEvent stockInOrOutEvent = StockInOrOutEvent
                        .ofNotice(PurchaseTypeEnum.IN_STOCK_PAYABLE, stockInOrder.getId(), stockInOrder.getBuyerUserId(), stockInOrder.getCreatedUid());
                EventBusUtil.post(stockInOrOutEvent, true);
            }
            return SingleResponse.of(stockInOrderQuery.getNo());
        }
        return SingleResponse.buildFailure("false", "入库单状态不符合编辑状态");
    }

    /**
     * 入库数据回滚
     *
     * @param backupStockInOrder
     * @param backupStockInOrderDetail
     * @return void
     * <AUTHOR>
     * @date 2022/5/25 18:05
     **/
    private void backup(StockInOrder backupStockInOrder, List<StockInOrderDetail> backupStockInOrderDetail) {
        stockInOrderMapper.updateById(backupStockInOrder);
        for (StockInOrderDetail stock : backupStockInOrderDetail) {
            stock.calcPriceAndTaxAmount();
            stockInOrderDetailMapper.updateById(stock);
        }
    }

    /**
     * 检查已创建入库单入库数与采购单采购数是否相等
     *
     * @param purchaseOrderId
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2022/5/20 11:40
     **/
    private int checkStockInOrderNum(Long purchaseOrderId) {
        PurchaseOrder purchaseOrder = purchaseOrderMapper.selectById(purchaseOrderId);
        if (purchaseOrder == null) {
            throw ExceptionPlusFactory.bizException("未查询到对应采购单【" + purchaseOrderId + "】");
        }
        Integer purchaseOrderNum = purchaseOrder.getTotalPurchaseQuantity();
        QueryWrapper<StockInOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("purchase_order_id", purchaseOrderId).eq("is_del", 0).ne("state", 4).ne("state", 1);
        List<StockInOrder> stockInOrders = stockInOrderMapper.selectList(queryWrapper);
        Integer inStockNum = 0;
        for (StockInOrder stockInOrder : stockInOrders) {
            inStockNum = inStockNum + stockInOrder.getTotalReceiptQuantity();
        }
        //未入库数（采购总数-已创建入库数
        return purchaseOrderNum - inStockNum;
    }

    /**
     * 检查采购单关联已创建入库单
     *
     * @param purchaseOrderId
     * @param totalReceiptQuantity
     * @return boolean
     * <AUTHOR>
     * @date 2022/5/6 11:36
     **/
    private Integer checkStockInOrderInNum(Long purchaseOrderId, @NotNull Integer totalReceiptQuantity) {
        //查询采购单
        PurchaseOrder purchaseOrder = purchaseOrderMapper.selectById(purchaseOrderId);
        if (purchaseOrder == null) {
            throw ExceptionPlusFactory.bizException("未查询到对应采购单【" + purchaseOrderId + "】");
        }
        //采购单-采购总数
        Integer purchaseOrderNum = purchaseOrder.getTotalPurchaseQuantity();
        QueryWrapper<StockInOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("purchase_order_id", purchaseOrderId).eq("is_del", 0).ne("state", 4);
        List<StockInOrder> stockInOrders = stockInOrderMapper.selectList(queryWrapper);
        //入库单-已创建总数
        Integer inStockNum = 0;
        for (StockInOrder stockInOrder : stockInOrders) {
            inStockNum = inStockNum + stockInOrder.getTotalReceiptQuantity();
        }
        //剩余未入库数（未入库数（采购总数-已创建入库数）-当前创建入库数）
        return (purchaseOrderNum - inStockNum) - totalReceiptQuantity;
    }

    /**
     * 查询采购单采购类型
     *
     * @return boolean
     * <AUTHOR>
     * @date 2022/4/29 13:49
     **/
    private boolean isPurchaseOrderType(Long purchaseOrderId) {
        PurchaseOrder purchaseOrder = purchaseOrderMapper.selectById(purchaseOrderId);
        if (purchaseOrder != null) {
            //采购类型 1 标准采购，2 工厂采购
            return purchaseOrder.getType() == 1;
        }
        return false;
    }

    /**
     * @deprecated 逻辑移动到 StockInOrderDetail 实体中计算
     */
    private BigDecimal multiplyTaxAmount(BigDecimal taxPrice, Integer receiptQuantity) {
        //含税总额 = 含税单价 * 数量
        return taxPrice.multiply(BigDecimal.valueOf(receiptQuantity)).setScale(6, RoundingMode.HALF_EVEN);
    }

    /**
     * @deprecated 逻辑移动到 StockInOrderDetail 实体中计算
     */
    private BigDecimal multiplyTaxQuota(BigDecimal taxPrice, BigDecimal afterTaxPrice, Integer receiptQuantity) {
        //税额=(税前单价-税后单价)*数量
        return (taxPrice.subtract(afterTaxPrice)).multiply(BigDecimal.valueOf(receiptQuantity)).setScale(6, RoundingMode.HALF_EVEN);
    }

    /**
     * @deprecated 逻辑移动到 StockInOrderDetail 实体中计算
     */
    private BigDecimal multiplyAfterTaxPrice(BigDecimal taxPrice, BigDecimal taxRate) {
        //税后单价 = 单价（税前）/ (1 + 税率)
        return taxPrice.divide(taxRate.add(BigDecimal.valueOf(1)), 6, RoundingMode.HALF_EVEN);
    }

    /**
     * @deprecated 逻辑移动到 StockInOrderDetail 实体中计算
     */
    private BigDecimal multiplyAfterTaxAmount(BigDecimal afterTaxPrice, Integer receiptQuantity) {
        //税后金额=税后单价 * 采购数量
        return afterTaxPrice.multiply(BigDecimal.valueOf(receiptQuantity)).setScale(6, RoundingMode.HALF_EVEN);
    }

    /**
     * @deprecated 逻辑移动到 StockInOrderDetail 实体中计算
     */
    private BigDecimal multiplyTotalPriceTax(Integer receiptQuantity, BigDecimal taxPrice) {
        //价税合计 单价（税前）* 采购数量
        return taxPrice.multiply(BigDecimal.valueOf(receiptQuantity)).setScale(6, RoundingMode.HALF_EVEN);
    }

    private boolean itemStockOnlyCheck(List<StockInOrderDetail> stockInOrderDetails) {
        String warehouseNo = null;
        for (int i = 0; i < stockInOrderDetails.size(); i++) {
            StockInOrderDetail stockInOrderDetail = stockInOrderDetails.get(i);
            if (i == 0) {
                warehouseNo = stockInOrderDetail.getWarehouseNo();
            }
            if (!warehouseNo.equals(stockInOrderDetail.getWarehouseNo())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 生成采购入库单号
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/4/12 14:18
     **/
    private String stockInOrderNo() {
        String no = "CGRK" + DateUtil.formatNow("yyMM");
        Long increment = RedisUtil.increment(no, 1);
        String format = String.format("%06d", increment);
        return no + format;
    }

    /**
     * 同步旺店通
     *
     * @param stockInOrderAndOrderDetail 明细
     * <AUTHOR>
     * @date 2022/3/28 13:34
     **/
    public SingleResponse synWDT(StockInOrderAndOrderDetail stockInOrderAndOrderDetail) {
        if (stockInOrderAndOrderDetail != null) {
            if (stockInOrderAndOrderDetail.getIsSyncWdt() == 1) {
                return (((StockInOrderServiceBizImpl) AopContext.currentProxy()))
                        .pushPurchaseOrderToWdt(stockInOrderAndOrderDetail);
            }
        }
        return SingleResponse.buildSuccess();
//        return SingleResponse.buildFailure("false", "同步数据不能为空");
    }

    @ManagedPush(sourceType = SourceType.PURCHASE_STOCK_IN_ORDER
            , targetType = TargetType.WDT
            , idArgProp = "id")
    public SingleResponse pushPurchaseOrderToWdt(StockInOrderAndOrderDetail stockInOrderAndOrderDetail) {
        final PurchaseOrderAPI api = wdtGateway.getAPI(PurchaseOrderAPI.class);
        final PurchaseOrderCreateOrderParams params = new PurchaseOrderCreateOrderParams();
        params.setPurchaseNo(stockInOrderAndOrderDetail.getNo());

        final SingleResponse<ProviderVO> providerResp = providerBizService
                .queryDetail(stockInOrderAndOrderDetail.getProviderId());
        if (!providerResp.isSuccess() || Objects.isNull(providerResp.getData())) {
            return SingleResponse.buildFailure(providerResp.getErrCode(), providerResp.getErrMessage());
        }
        params.setProviderNo(providerResp.getData().getProviderNo());

        Map<String, String> providerGoodsMap = new HashMap<>();
        for (StockInOrderDetail stockInOrderDetail : stockInOrderAndOrderDetail
                .getStockInOrderDetails()) {
            providerGoodsMap.put(params.getProviderNo(), stockInOrderDetail.getItemSkuCode());
        }
        wdtPurchaseBizService.confirmProviderGoodsValid(providerGoodsMap);

        final String warehouseNo = stockInOrderAndOrderDetail.getStockInOrderDetails().get(0)
                .getWarehouseNo();
        QueryWrapper<Warehouse> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("no", warehouseNo);
        final Warehouse warehouse = warehouseMapper.selectOne(queryWrapper);
        if (warehouse == null) {
            return SingleResponse.buildFailure(String.valueOf(ErrorCode.DATA_NOT_FOUND), "未找到指定仓库：" + warehouseNo);
        }
        params.setReceiveWarehouseNos(warehouse.getNo());
        params.setExpectWarehouseNo(warehouse.getNo());

        final Long buyerUserId = stockInOrderAndOrderDetail.getBuyerUserId();
        final StaffInfo staffInfo = userGateway.queryStaffInfoById(buyerUserId);
        if (staffInfo == null) {
            return SingleResponse.buildFailure(String.valueOf(ErrorCode.DATA_NOT_FOUND), "未能找到采购员信息：" + buyerUserId);
        }
        //要求在旺店通具备采购员职务（暂时用admin）
        params.setPurchaserName(ApplicationContextUtil.isActiveProfile("dev", "test") ? "admin"
                : staffInfo.getUserName());
        params.setIsCheck(true);
        //付款方式默认现付
        params.setPayType(1);

        final ArrayList<PurchaseDetails> purchaseDetails = Lists.newArrayList();
        final Map<String, PurchaseDetails> purchaseDetailsMap = new HashMap<>();
        for (StockInOrderDetail stockInOrderDetail : stockInOrderAndOrderDetail
                .getStockInOrderDetails()) {
            PurchaseDetails purchaseDetail = purchaseDetailsMap
                    .get(stockInOrderDetail.getItemSkuCode());
            if (purchaseDetail != null) {
                purchaseDetail.setNum(purchaseDetail.getNum() + stockInOrderDetail
                        .getReceiptQuantity());
            } else {
                purchaseDetail = new PurchaseDetails();
                purchaseDetail.setSpecNo(stockInOrderDetail.getItemSkuCode());
                purchaseDetail.setNum(stockInOrderDetail.getReceiptQuantity());
                purchaseDetail.setPurchaseUnitName("");
                purchaseDetail.setPrice(stockInOrderDetail.getTaxPrice());
                purchaseDetail.setTaxRate(stockInOrderDetail.getTaxRate());
                purchaseDetails.add(purchaseDetail);
                purchaseDetailsMap.put(stockInOrderDetail.getItemSkuCode(), purchaseDetail);
            }
        }
        params.setPurchaseDetails(purchaseDetails);
        try {
            final PurchaseOrderCreateOrderResponse createOrderResponse = api.createOrder(params);
            if (createOrderResponse.getStatus() == 0) {
                SingleResponse<String> singleResponse = new SingleResponse<>();
                final String wdtPurchaseOrderNo = createOrderResponse.getMessage();
                stockInOrderMapper.updateWdtPurchaseOrderNo(stockInOrderAndOrderDetail.getId(),
                        wdtPurchaseOrderNo);
                singleResponse.setSuccess(true);
                singleResponse.setData(createOrderResponse.getMessage());
                return singleResponse;
            }
            return SingleResponse.buildFailure(String.valueOf(createOrderResponse.getStatus()), createOrderResponse.getMessage());
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapBizException(e);
        }
    }

    /**
     * 转化明细
     *
     * @param stockInOrderAndOrderDetail
     * @return java.util.List<com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrderDetail>
     * <AUTHOR>
     * @date 2022/3/29 14:04
     **/
    private List<StockInOrderDetail> getBeans(StockInOrderAndOrderDetail stockInOrderAndOrderDetail) {
        JSONObject j = (JSONObject) JSONObject.toJSON(stockInOrderAndOrderDetail);
        return detailParamNotNull(JSONObject.parseArray(JSONObject.toJSON(j.get("stockInOrderDetails")).toString(), StockInOrderDetail.class));
    }

    private List<StockInOrderDetail> detailParamNotNull(List<StockInOrderDetail> stockInOrderDetails) {
        for (StockInOrderDetail stockInOrderDetail : stockInOrderDetails) {
            stockInOrderDetail.setCreatedAt(stockInOrderDetail.getCreatedAt() == null ? DateUtil.currentTime() : stockInOrderDetail.getCreatedAt());
            stockInOrderDetail.setCreatedUid(stockInOrderDetail.getCreatedUid() == null ? UserContext.getUserId() : stockInOrderDetail.getCreatedUid());
            stockInOrderDetail.setUpdatedUid(stockInOrderDetail.getUpdatedUid() == null ? UserContext.getUserId() : stockInOrderDetail.getUpdatedUid());
            stockInOrderDetail.setUpdatedAt(stockInOrderDetail.getUpdatedAt() == null ? DateUtil.currentTime() : stockInOrderDetail.getUpdatedAt());
        }
        return stockInOrderDetails;
    }

    /**
     * 转化采购入库
     *
     * @param stockInOrderAndOrderDetail
     * @return com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder
     * <AUTHOR>
     * @date 2022/3/29 14:04
     **/
    private StockInOrder getBean(StockInOrderAndOrderDetail stockInOrderAndOrderDetail) {
        JSONObject j = (JSONObject) JSONObject.toJSON(stockInOrderAndOrderDetail);
        j.remove("stockInOrderDetails");
        j.remove("operationType");
        return paramNotNull(JSONObject.toJavaObject(j, StockInOrder.class));
    }

    /**
     * 转化实体为空字段处理
     *
     * @param stockInOrder
     * @return com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder
     * <AUTHOR>
     * @date 2022/4/12 14:13
     **/
    private StockInOrder paramNotNull(StockInOrder stockInOrder) {
        stockInOrder.setUpdatedAt(stockInOrder.getUpdatedAt() == null ? DateUtil.currentTime() : stockInOrder.getUpdatedAt());
        stockInOrder.setCreatedUid(stockInOrder.getCreatedUid() == null ? UserContext.getUserId() : stockInOrder.getCreatedUid());
        stockInOrder.setUpdatedUid(stockInOrder.getUpdatedUid() == null ? UserContext.getUserId() : stockInOrder.getUpdatedUid());
        stockInOrder.setKingDeeId("");
        return stockInOrder;
    }

    private void setPermission(StockInOrderQuery query) {
        // 采购订单三波权限
        // 1.只能看到自己创建的
        // 2.能看到全部业务人员创建订单
        // 3.能看到系统创建订单
        boolean seeAllOrder = UserContext.hasPermission(GlobalConstant.STOCK_IN_ORDER_ALL_URI);
        boolean seeSysOrder = UserContext.hasPermission(GlobalConstant.VIEW_STOCK_IN_ORDER_WRITE_OFF, ResourceType.API);
        if (!seeAllOrder && !seeSysOrder) {
            query.setCreatedUidList(ListUtil.of(UserContext.getUserId()));
        }
        if (!seeAllOrder && seeSysOrder) {
            query.setCreatedUidList(ListUtil.of(UserContext.getUserId(), 0L));
        }
        if (seeAllOrder && !seeSysOrder) {
            query.setNeCreatedUidList(ListUtil.of(0L));
        }
    }

    private void setPermission(StockInOrderAndOrderDetailQuery stockInOrderAndOrderDetailQuery) {
        // 采购订单三波权限
        // 1.只能看到自己创建的
        // 2.能看到全部业务人员创建订单
        // 3.能看到系统创建订单
        boolean seeAllOrder = UserContext.hasPermission(GlobalConstant.STOCK_IN_ORDER_ALL_URI);
        boolean seeSysOrder = UserContext.hasPermission(GlobalConstant.VIEW_STOCK_IN_ORDER_WRITE_OFF, ResourceType.API);
        if (!seeAllOrder && !seeSysOrder) {
            stockInOrderAndOrderDetailQuery.setCreatedUidList(ListUtil.of(UserContext.getUserId()));
        }
        if (!seeAllOrder && seeSysOrder) {
            stockInOrderAndOrderDetailQuery.setCreatedUidList(ListUtil.of(UserContext.getUserId(), 0L));
        }
        if (seeAllOrder && !seeSysOrder) {
            stockInOrderAndOrderDetailQuery.setNeCreatedUidList(ListUtil.of(0L));
        }
    }

    /**
     * 分页查询采购入库单
     *
     * @param stockInOrderQuery, userID
     * @return com.alibaba.cola.dto.PageResponse<com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder>
     * <AUTHOR>
     * @date 2022/3/29 14:05
     **/
    @Override
    public PageResponse<StockInOrderShowDetail> queryStockInOrderList(StockInOrderQuery stockInOrderQuery) {
        stockInOrderQuery.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(stockInOrderQuery.getBusinessLine()));
        setPermission(stockInOrderQuery);

        final PageResponse<StockInOrderShowDetail> pageResponse = new PageResponse<>();

        //为空时表示首次进入列表，用户未点击页面开关，不需要更新状态，不为空时表示用户点击了开关，此时需要根据开关传入值更新当前明细开关状态状态。
        if (stockInOrderQuery.getIsDetailMode() != null) {
            baseInfoService.setOpenDetailModel(DetailModelType.STOCK_IN_ORDER, stockInOrderQuery.getIsDetailMode());
        }
        //获取用户查询明细开关
        Boolean singleResponse = baseInfoService.isOpenDetailModel(DetailModelType.STOCK_IN_ORDER.getVal());

        // ---- 2023-08-01 七喜 修改优化
        stockInOrderQuery.setOffsetVal(stockInOrderQuery.getOffset());
        List<StockInOrderQueryDto> stockInOrderQueryDtoList = stockInOrderMapper.queryStockInOrderList2(stockInOrderQuery);
        if (CollectionUtils.isEmpty(stockInOrderQueryDtoList)) {
            return PageResponse.of(new LinkedList<>(), 0, stockInOrderQuery.getPageSize(), stockInOrderQuery.getPageIndex());
        }
        List<StockInOrder> stockInOrders = iStockInOrderService.lambdaQuery()
                .in(StockInOrder::getId, stockInOrderQueryDtoList.stream().map(StockInOrderQueryDto::getStockInOrderId).collect(Collectors.toList()))
                .orderByDesc(StockInOrder::getId).list();
        Map<Long, String> orderIdAndDetailIdsMap = stockInOrderQueryDtoList.stream()
                .collect(Collectors.toMap(StockInOrderQueryDto::getStockInOrderId, StockInOrderQueryDto::getStockInOrderDetailIds));
        // ----

        Collection<StockInOrderShowDetail> collection = new ArrayList<>();
        List<StockInOrderShowDetail> stockInOrderShowDetails = new ArrayList<>();
        if (stockInOrders.size() > 0) {
            //用户查看明细开关
            if (Boolean.TRUE.equals(singleResponse)) {
                //查询明细
                for (StockInOrder stockInOrder : stockInOrders) {
                    if (stockInOrder != null) {
                        StockInOrderShowDetail stockInorderShowDetail = new StockInOrderShowDetail();
                        stockInorderShowDetail.setShowDetail(1);
                        StockInOrderAndOrderDetail stockInOrderAndOrderDetail = JSONObject.toJavaObject(resultBean(stockInOrder), StockInOrderAndOrderDetail.class);
                        //供应商ID转name
                        stockInOrderAndOrderDetail.setProvider(providerIdToProviderName(stockInOrderAndOrderDetail.getProviderId()));
                        final Optional<PartnerProviderResp> partnerProviderResp = providerGateway.partnerQueryByProviderId(stockInOrderAndOrderDetail.getProviderId());
                        stockInOrderAndOrderDetail.setIsBlacklist(partnerProviderResp.map(PartnerProviderResp::getIsBlacklist).orElse(0));

                        // ---- 2023-08-01 七喜 修改优化
                        String detailIds = orderIdAndDetailIdsMap.get(stockInOrder.getId());
                        if (StringUtils.isNotBlank(detailIds)) {
                            List<StockInOrderDetail> list = iStockInOrderDetailService.lambdaQuery()
                                    .in(StockInOrderDetail::getId, Arrays.asList(detailIds.split(",")))
                                    .list();
                            stockInOrderAndOrderDetail.setStockInOrderDetails(list);
                            stockInOrderAndOrderDetail.setWarehouseName(warehouseNoToName(list.get(0).getWarehouseNo()));
                        } else {
                            stockInOrderAndOrderDetail.setStockInOrderDetails(new LinkedList<>());
                            stockInOrderAndOrderDetail.setWarehouseName("");

                        }
                        stockInOrderAndOrderDetail.setBusinessLine(stockInOrder.getBusinessLine());
                        stockInorderShowDetail.setStockInOrderAndOrderDetail(stockInOrderAndOrderDetail);
                        stockInOrderShowDetails.add(stockInorderShowDetail);
                    }
                }
            } else {
                for (StockInOrder stockInOrder : stockInOrders) {
                    if (stockInOrder != null) {
                        StockInOrderShowDetail stockInorderShowDetail = new StockInOrderShowDetail();
                        stockInorderShowDetail.setShowDetail(0);
                        StockInOrderAndOrderDetail stockInOrderAndOrderDetail = JSONObject.toJavaObject(resultBean(stockInOrder), StockInOrderAndOrderDetail.class);
                        //供应商ID转name
                        stockInOrderAndOrderDetail.setProvider(providerIdToProviderName(stockInOrderAndOrderDetail.getProviderId()));
                        final Optional<PartnerProviderResp> partnerProviderResp = providerGateway.partnerQueryByProviderId(stockInOrderAndOrderDetail.getProviderId());
                        stockInOrderAndOrderDetail.setIsBlacklist(partnerProviderResp.map(PartnerProviderResp::getIsBlacklist).orElse(0));
                        stockInOrderAndOrderDetail.setWarehouseName(warehouseNoToName(stockInOrderAndOrderDetail.getWarehouseNo()));
                        stockInOrderAndOrderDetail.setBusinessLine(stockInOrder.getBusinessLine());
                        stockInorderShowDetail.setStockInOrderAndOrderDetail(stockInOrderAndOrderDetail);
                        stockInOrderShowDetails.add(stockInorderShowDetail);
                    }
                }
            }
            collection.addAll(stockInOrderShowDetails);
        }
        pageResponse.setPageIndex(stockInOrderQuery.getPageIndex());
        pageResponse.setPageSize(stockInOrderQuery.getPageSize());
        pageResponse.setTotalCount(stockInOrderMapper.countStockInOrderList2(stockInOrderQuery));
        pageResponse.setData(collection);
        pageResponse.setSuccess(true);
        return pageResponse;

    }

    private String warehouseNoToName(String warehouseNo) {
        WarehousePageQuery warehousePageQuery = new WarehousePageQuery();
        warehousePageQuery.setNo(warehouseNo);
        Page<Warehouse> pageWarehouse = warehouseService.pageQuery(warehousePageQuery);
        List<Warehouse> listWarehouse = pageWarehouse.getRecords();
        if (listWarehouse.size() == 0) {
//            throw ExceptionPlusFactory.bizException("仓库编号:[" + warehouseNo + "]目标仓库不存在!!!");
            return StrUtil.EMPTY;
        }
        return listWarehouse.get(0).getName();
    }

    /**
     * 基础类转化为返回类参数
     *
     * @param stockInOrder
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2022/3/29 18:41
     **/
    private JSONObject resultBean(StockInOrder stockInOrder) {
        JSONObject stockInOrderJson = (JSONObject) JSONObject.toJSON(stockInOrder);
        stockInOrderJson.put("stockInOrderDetails", null);
        return stockInOrderJson;
    }

    /**
     * 删除采购入库单
     *
     * @param stockInOrderId
     * @return com.alibaba.cola.dto.Response
     * <AUTHOR>
     * @date 2022/3/29 18:39
     **/
    @Override
    @Transactional
    public Response deleteStockInOrderService(Integer stockInOrderId) {
        Response response = new Response();
        StockInOrder stockInOrder = stockInOrderMapper.selectById(stockInOrderId);
        if (stockInOrder != null && stockInOrder.getState().equals(StockInState.WAIT_SUBMIT.getValue())) {
            response.setSuccess(true);
            response.setErrCode("10000");
            int updateStockInOrder = stockInOrderMapper.updateByStockInOrderId(stockInOrder.getId());
            operateLogDomainService.addOperatorLog(UserContext.getUserId(), OperateLogTarget.STOCK_ORDER, stockInOrder.getId(), (!UserContext.getUserInfo().isPresent() ? "系统" : UserContext.getUserInfo().get().getUserName()) + "删除了采购入库单");
            response.setErrMessage("入库单已删除");
            if (updateStockInOrder > 0) {
                List<StockInOrderDetail> stockInOrderDetails = stockInOrderDetailMapper.selectByStockInOrderDetailId(stockInOrder.getId());
                if (stockInOrderDetails.size() > 0) {
                    for (StockInOrderDetail stockInOrderDetail : stockInOrderDetails) {
                        stockInOrderDetailMapper.delByStockInOrderDetailId(stockInOrderDetail.getId());
                        operateLogDomainService.addOperatorLog(UserContext.getUserId(), OperateLogTarget.STOCK_ORDER, stockInOrder.getId(), (!UserContext.getUserInfo().isPresent() ? "系统" : UserContext.getUserInfo().get().getUserName()) + "删除了采购入库单明细");
                    }
                }
                response.setErrMessage("入库单&关联明细已删除");
            }
            return response;
        }
        return Response.buildFailure("false", "ID入库单ID无效");
    }

    /**
     * 采购入库单导出
     *
     * @param stockInOrderAndOrderDetailQuery
     * <AUTHOR>
     * @date 2022/4/2 11:12
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exportExcel(StockInOrderAndOrderDetailQuery stockInOrderAndOrderDetailQuery) {
        stockInOrderAndOrderDetailQuery.setBusinessLine(
                UserPermissionJudge.filterBusinessLinePerm(stockInOrderAndOrderDetailQuery.getBusinessLine())
        );
        Assert.isTrue(CollUtil.isNotEmpty(stockInOrderAndOrderDetailQuery.getBusinessLine()), "无数据权限，无法导出");
        setPermission(stockInOrderAndOrderDetailQuery);
        ExportTask exportTask = new ExportTask();
        exportTask.setStatus(ExportTaskStatus.RUNNING);
        exportTask.setName(ExportTaskType.STOCK_IN_ORDER.getDesc() + "-" + DateUtil.formatNow(DatePattern.PURE_DATETIME_PATTERN));
        exportTask.setType(ExportTaskType.STOCK_IN_ORDER);
        IExportTaskService exportTaskService = SpringUtil.getBean(IExportTaskService.class);
        exportTaskService.save(exportTask);

        exportDomainService.export(stockInOrderAndOrderDetailQuery, exportTask, ExportType.STOCK_IN_ORDER_DETAIL,
                StockInOrderAndOrderDetailSheet.class, null);
    }

    /**
     * 查询炎黄盈动字段
     *
     * @param no
     * @return com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetailQuery
     * <AUTHOR>
     * @date 2022/4/2 17:55
     **/
    @Override
    public List<StockInOrderAndDetailQuery> stockInOrderAndOrderDetailQuerylist(String no) {
        return stockInOrderMapper.selectStockInOrderAndDetailByNo(no);
    }

    /**
     * 查询炎黄盈动字段
     *
     * @param id
     * @return com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetailQuery
     * <AUTHOR>
     * @date 2022/4/2 17:55
     **/
    @Override
    public StockInOrderAndOrderDetail stockInOrderAndOrderDetailQuery(Long id) {
        StockInOrderAndOrderDetail stockInOrderAndOrderDetail = new StockInOrderAndOrderDetail();
        StockInOrder stockInOrder = stockInOrderMapper.selectById(id);
        BeanUtil.copyProperties(stockInOrder, stockInOrderAndOrderDetail);
        if (Objects.nonNull(stockInOrder)) {
            List<StockInOrderDetail> stockInOrderDetails = stockInOrderDetailMapper.selectByStockInOrderDetailId(stockInOrder.getId());
            stockInOrderAndOrderDetail.setStockInOrderDetails(stockInOrderDetails);
        }
        return stockInOrderAndOrderDetail;
    }

    /**
     * 根据入库单号查询订单以及明细
     *
     * @param no
     * @return com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndDetailQuery
     * <AUTHOR>
     * @date 2022/4/12 15:17
     **/
    @Override
    public SingleResponse<StockInOrderAndOrderDetailVo> stockInOrderAndDetailAllInfoQueryByNo(String no) {
        StockInOrder stockInOrder = stockInOrderMapper.selectStockInOrderByNo(no);
        StockInOrderAndOrderDetailVo stockInOrderAndOrderDetailVo = null;
        List<StockInOrderDetail> stockInOrderDetailList;
        if (stockInOrder != null) {
            stockInOrderAndOrderDetailVo = getStockInOrderBaseInfo(JSONObject.toJavaObject(resultBean(stockInOrder), StockInOrderAndOrderDetailVo.class));
            stockInOrderDetailList = stockInOrderDetailMapper.selectByStockInOrderDetailId(stockInOrder.getId());
            if (stockInOrderDetailList.size() > 0) {
                stockInOrderAndOrderDetailVo.setStockInOrderDetails(resultDetailBean(stockInOrderDetailList));
            }
        }
        return SingleResponse.of(stockInOrderAndOrderDetailVo);
    }

    /**
     * 前端基础数据转化
     *
     * @param stockInOrderAndOrderDetailVo
     * @return com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetailVo
     * <AUTHOR>
     * @date 2022/4/12 17:26
     **/
    private StockInOrderAndOrderDetailVo getStockInOrderBaseInfo(StockInOrderAndOrderDetailVo stockInOrderAndOrderDetailVo) {
        //用户
        stockInOrderAndOrderDetailVo.setCreateUser(userIdToUserName(stockInOrderAndOrderDetailVo.getCreatedUid()));
        //仓库
        stockInOrderAndOrderDetailVo.setStorage(warehouseNoToName(stockInOrderAndOrderDetailVo.getWarehouseNo()));
        //收料组织
        stockInOrderAndOrderDetailVo.setOrganization(organizationIdToOrganizationName(stockInOrderAndOrderDetailVo.getOrganizationId()));
        //采购组织
        stockInOrderAndOrderDetailVo.setPurchaseOrganization(organizationIdToOrganizationName(stockInOrderAndOrderDetailVo.getPurchaseOrganizationId()));
        //采购组
        stockInOrderAndOrderDetailVo.setPurchaseGroup(purchaseGroupIdToPurchaseGroupName(stockInOrderAndOrderDetailVo.getPurchaseGroupId()));
        //供应商
        setUpProviderData(stockInOrderAndOrderDetailVo);
        //采购员
        stockInOrderAndOrderDetailVo.setBuyerUser(userIdToUserName(stockInOrderAndOrderDetailVo.getBuyerUserId()));
        //采购部门
        stockInOrderAndOrderDetailVo.setPurchaseDepartment(purchaseDepartmentIdToPurchaseDepartmentName(stockInOrderAndOrderDetailVo.getPurchaseDepartmentId()));
        return stockInOrderAndOrderDetailVo;
    }

    private void setUpProviderData(StockInOrderAndOrderDetailVo stockInOrderAndOrderDetailVo) {
        final Long providerId = stockInOrderAndOrderDetailVo.getProviderId();
        Provider provider = providerGateway.getById(providerId);
        Objects.requireNonNull(provider, "供应商数据查询异常");
        stockInOrderAndOrderDetailVo.setProvider(provider.getName());
        final Long partnerProviderId = provider.getPartnerProviderId();
        stockInOrderAndOrderDetailVo.setPartnerProviderId(partnerProviderId);
        final Optional<PartnerProviderResp> partnerProviderResp =
                providerGateway.partnerQueryById(partnerProviderId);
        stockInOrderAndOrderDetailVo.setIsBlacklist(
                partnerProviderResp.map(PartnerProviderResp::getIsBlacklist).orElse(0));
    }

    private String purchaseDepartmentIdToPurchaseDepartmentName(Long purchaseDepartmentId) {
        if (purchaseDepartmentId != null) {
            if (purchaseDepartmentId == 0) {
                return "系统";
            }
            String department = departmentGateway.name(purchaseDepartmentId);
            if (department.isEmpty()) {
                throw ExceptionPlusFactory.bizException("采购部门[" + purchaseDepartmentId + "]不存在");
            }
            return department;
        }
        return null;
    }

    private String providerIdToProviderName(Long providerId) {
        if (providerId != null) {
            Provider provider = providerGateway.getById(providerId);
            if (provider == null) {
                throw ExceptionPlusFactory.bizException("供应商[" + providerId + "]不存在");
            }
            return provider.getName();
        }
        return null;
    }

    private String purchaseGroupIdToPurchaseGroupName(Long purchaseGroupId) {
        if (purchaseGroupId != null) {
            if (purchaseGroupId == 0) {
                return "系统";
            }
            // TODO 暂时不读取部门表数据
            // String erpGroup = departmentGateway.name(purchaseGroupId);
            //if (erpGroup == null) {
            //    throw ExceptionPlusFactory.bizException("采购组["+purchaseGroupId+"]不存在");
            //}
            //return erpGroup;
            //================temp--start===============
            ErpGroup e = groupMapper.selectById(purchaseGroupId);
            if (e == null) {
                throw ExceptionPlusFactory.bizException("采购组[" + purchaseGroupId + "]不存在");
            }
            return e.getName();
            //================temp--end================
        }
        return null;
    }

    private String organizationIdToOrganizationName(Long organizationId) {
        if (organizationId != null) {
            Organization organizationIn = organizationMapper.selectById(organizationId);
            if (organizationIn == null) {
                throw ExceptionPlusFactory.bizException("收料组织[" + organizationId + "]不存在");
            }
            return organizationIn.getName();
        }
        return null;
    }

    private String userIdToUserName(Long createdUid) {
        if (createdUid != null) {
            if (createdUid == 0) {
                return "系统";
            }
            StaffInfo staffInfo = userGateway.queryStaffInfoById(createdUid);
            if (staffInfo != null) {
                return staffInfo.getNickname();
            }
        }
        return null;
    }

    /**
     * 查看采购入库单明细转化
     *
     * @param stockInOrderDetailList
     * @return java.util.List<com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderDetailVo>
     * <AUTHOR>
     * @date 2022/4/12 16:35
     **/
    private List<StockInOrderDetailVo> resultDetailBean(List<StockInOrderDetail> stockInOrderDetailList) {
        List<StockInOrderDetailVo> list = JSONObject.parseArray(JSONObject.toJSON(stockInOrderDetailList).toString(), StockInOrderDetailVo.class);

        final Set<String> skuCodeList = list.stream().map(StockInOrderDetailVo::getItemSkuCode).collect(Collectors.toSet());
        final Map<String, List<CorpBizTypeDTO>> divisionMap = iBizLevelDivisionService.queryBySkuCode(skuCodeList);

        for (StockInOrderDetailVo stockInOrderDetailVo : list) {
            stockInOrderDetailVo.setStock(warehouseNoToName(stockInOrderDetailVo.getWarehouseNo()));
            stockInOrderDetailVo.setCorpBizType(divisionMap.getOrDefault(stockInOrderDetailVo.getItemSkuCode(),new LinkedList<>()));
        }
        return list;
    }

    @Override
    public List<StockInOrder> listFinishStockInOrder(Long startTime, Long endTime) {
        return stockInOrderMapper.listFinishStockInOrder(startTime, endTime);
    }

    @Override
    public Integer getStockInOrderSkuStock(StockOutOrderStockQuery stockQuery) {
        Integer stockInOrderSkuStock = stockInOrderDetailMapper.getStockInOrderSkuStock(stockQuery);
        if (stockInOrderSkuStock == null) {
            return 0;
        }
        return stockInOrderSkuStock;
    }

    @Override
    public List<StockInOrderDetail> getHasStockInOrderDetail(Long purchaseOrderId, String itemSkuCode) {
        return stockInOrderDetailMapper.getStockInOrderDetail(purchaseOrderId, itemSkuCode);
    }

    /**
     * 采购单ID查询采购入库单列表
     *
     * @param purchaseOrderId
     * @return com.alibaba.cola.dto.Response
     * <AUTHOR>
     * @date 2022/4/21 14:55
     **/
    @Override
    public SingleResponse<List<StockInOrder>> stockInOrderList(Long purchaseOrderId) {
        SingleResponse<List<StockInOrder>> singleResponse = new SingleResponse<>();
        singleResponse.setData(stockInOrderMapper.selectList(new QueryWrapper<StockInOrder>().eq("purchase_order_id", purchaseOrderId)));
        singleResponse.setSuccess(true);
        return singleResponse;
    }

    /**
     * 取消采购入库单
     *
     * @param stockInOrderId
     * @return com.alibaba.cola.dto.Response
     * <AUTHOR>
     * @date 2022/4/24 14:23
     **/
    @Override
    public Response cancelStockInOrderService(Integer stockInOrderId) {
        SingleResponse<Object> singleResponse = new SingleResponse<>();
        StockInOrder stockInOrder = stockInOrderMapper.selectById(stockInOrderId);
        if (stockInOrder.getState().equals(StockInState.WAIT_IN.getValue())) {
            if (StrUtil.isNotBlank(stockInOrder.getWdtStorageNo())) {
                try {
                    final PurchaseOrderAPI api = wdtGateway.getAPI(PurchaseOrderAPI.class);
                    api.cancelOrder(stockInOrder.getWdtStorageNo());
                } catch (WdtErpException e) {
                    throw WdtExceptions.wrapBizException(e);
                }
            }
            stockInOrder.setState(StockInState.CANCEL.getValue());
            Integer updateNum = stockInOrderMapper.updateById(stockInOrder);
            operateLogDomainService.addOperatorLog(UserContext.getUserId(), OperateLogTarget.STOCK_ORDER, stockInOrder.getId(), (!UserContext.getUserInfo().isPresent() ? "系统" : UserContext.getUserInfo().get().getUserName()) + "取消了采购入库单");
            singleResponse.setData(updateNum);
            singleResponse.setSuccess(true);
            return singleResponse;
        }
        return Response.buildFailure("false", "无效id未能查询到入库单！");
    }

    @Override
    public PageResponse<ComposeSkuVO> pagePurchaseOrderSku(ComposeSkuPageQuery query) {
        Assert.notNull(query.getPurchaseOrderId(), "关联的采购单不得为空");

        Long purchaseOrderId = query.getPurchaseOrderId();
        QueryWrapper<PurchaseOrderDetail> purchaseOrderDetailQueryWrapper = new QueryWrapper<>();
        purchaseOrderDetailQueryWrapper.eq("purchase_order_id", purchaseOrderId);
        purchaseOrderDetailQueryWrapper.eq("is_del", 0);
        List<PurchaseOrderDetail> purchaseOrderDetailList = purchaseOrderDetailMapper.selectList(purchaseOrderDetailQueryWrapper);

        List<ComposeSkuVO> list = new ArrayList<>();
        for (PurchaseOrderDetail purchaseOrderDetail : purchaseOrderDetailList) {
            query.setSkuCodeList(Collections.singletonList(purchaseOrderDetail.getItemSkuCode()));
            query.setWithPrice(true);
            PageResponse<ComposeSkuVO> composeSkuVOPageResponse = combinationItemBizService.pageSku(query);
            List<ComposeSkuVO> data = composeSkuVOPageResponse.getData();
            if (data.isEmpty()) {
                continue;
            }
            ComposeSkuVO composeSkuVO = data.get(0);
            composeSkuVO.setStockCount(Long.valueOf(purchaseOrderDetail.getPurchaseQuantity()));
            composeSkuVO.setWarehouseName(warehouseNoToName(purchaseOrderDetail.getWarehouseNo()));
            composeSkuVO.setWarehouseNo(purchaseOrderDetail.getWarehouseNo());
            list.add(composeSkuVO);
        }

        int count = purchaseOrderDetailList.size();
        return PageResponse.of(list, count, query.getPageSize(), query.getPageIndex());
    }


}
