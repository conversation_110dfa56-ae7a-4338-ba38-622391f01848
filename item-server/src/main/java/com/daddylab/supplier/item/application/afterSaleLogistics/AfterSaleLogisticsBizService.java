package com.daddylab.supplier.item.application.afterSaleLogistics;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSaleLogistics.domain.LogisticsTraceData;
import com.daddylab.supplier.item.application.afterSaleLogistics.dto.*;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.LogisticsTraceDataSource;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年05月23日 9:39 AM
 */
public interface AfterSaleLogisticsBizService {

  /**
   * 分销售后单
   *
   * @param request 请求参数
   * @return 售后单分销售结果
   */
  Response distributeCS(DistributeCSRequest request);

  /**
   * 跟进处理
   *
   * @param cmd 请求参数
   */
  Response followUp(AfterSaleLogisticsFollowUpCmd cmd);

  /**
   * 导出
   *
   * @param query 查询参数
   */
  Response export(AfterSaleLogisticsPageQuery query);

  SingleResponse<AfterSaleLogisticsPageVo> page(AfterSaleLogisticsPageQuery query);

  Response remark(AfterSaleLogisticsRemarkCmd cmd);

  SingleResponse<String> getRemark(AfterSaleLogisticsRemarkQuery query);

  Response open(AfterSaleLogisticsOpenCmd cmd);

  Response close(AfterSaleLogisticsCloseCmd cmd);

  PageResponse<AfterSaleLogisticsBatchWarnVO> batchWarnList(AfterSaleLogisticsBatchWarnQuery query);

  String getAbnormalStaticsMsg();

  MultiResponse<String> logisticsCompanyList(String company);

  SingleResponse<LogisticsTraceData> getTrackList(Long id);

  SingleResponse<LogisticsTraceData> getTrackList(
      LogisticsTraceDataSource traceSource, Long callbackId);

  boolean matchLogisticsTrace(OrderLogisticsTrace trace);

  /**
   * 过滤订单，判断是否满足跟踪条件
   * @param order 旺店通订单
   * @param detailList 旺店通订单详情
   * @return 满足跟踪条件返回 true
   */
  boolean filter(WdtOrder order, List<WdtOrderDetail> detailList);
  
  
}
