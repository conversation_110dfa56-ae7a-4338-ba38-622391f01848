package com.daddylab.supplier.item.infrastructure.utils;

import org.slf4j.Logger;
import org.slf4j.event.Level;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2022/2/18
 */
public class LogUtil {
    public static void log(Logger logger, Level level, String format, Object... vars) {
        switch (level) {
            case TRACE:
                logger.trace(format, vars);
                break;
            case DEBUG:
                logger.debug(format, vars);
                break;
            case INFO:
                logger.info(format, vars);
                break;
            case WARN:
                logger.warn(format, vars);
                break;
            case ERROR:
                logger.error(format, vars);
                break;
            default:
                throw new IllegalArgumentException("invalid level");
        }
    }

    public static String simpleLogStr(Level level, String format, Object... vars) {
        return String.format(
                "%s %s %s", LocalDateTime.now(), level.toString(), String.format(format, vars));
    }
}
