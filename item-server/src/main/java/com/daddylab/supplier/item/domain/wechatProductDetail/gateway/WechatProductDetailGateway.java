package com.daddylab.supplier.item.domain.wechatProductDetail.gateway;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatProductDetail;

import java.util.List;

/**
 * 微信商品详情Gateway
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface WechatProductDetailGateway {

    /**
     * 保存微信商品详情
     *
     * @param wechatProductDetail 微信商品详情
     * @return 是否成功
     */
    boolean save(WechatProductDetail wechatProductDetail);

    /**
     * 批量保存微信商品详情
     *
     * @param wechatProductDetails 微信商品详情列表
     * @return 是否成功
     */
    boolean saveBatch(List<WechatProductDetail> wechatProductDetails);

    /**
     * 根据类型和商品ID查询微信商品详情
     *
     * @param type 类型 daddylab/member
     * @param productId 商品ID
     * @return 微信商品详情
     */
    WechatProductDetail findByTypeAndProductId(String type, String productId);

    /**
     * 根据类型和商品ID列表查询微信商品详情
     *
     * @param type 类型 daddylab/member
     * @param productIds 商品ID列表
     * @return 微信商品详情列表
     */
    List<WechatProductDetail> findByTypeAndProductIds(String type, List<String> productIds);

    /**
     * 根据类型和商品ID更新微信商品详情
     *
     * @param wechatProductDetail 微信商品详情
     * @return 是否成功
     */
    boolean updateByTypeAndProductId(WechatProductDetail wechatProductDetail);

    /**
     * 根据类型和商品ID删除微信商品详情
     *
     * @param type 类型 daddylab/member
     * @param productId 商品ID
     * @return 是否成功
     */
    boolean deleteByTypeAndProductId(String type, String productId);

    /**
     * 根据商品ID查询微信商品详情（兼容旧接口）
     *
     * @param productId 商品ID
     * @return 微信商品详情
     */
    WechatProductDetail findByProductId(String productId);

    /**
     * 根据商品ID列表查询微信商品详情（兼容旧接口）
     *
     * @param productIds 商品ID列表
     * @return 微信商品详情列表
     */
    List<WechatProductDetail> findByProductIds(List<String> productIds);

    /**
     * 根据商品ID更新微信商品详情（兼容旧接口）
     *
     * @param wechatProductDetail 微信商品详情
     * @return 是否成功
     */
    boolean updateByProductId(WechatProductDetail wechatProductDetail);

    /**
     * 根据商品ID删除微信商品详情（兼容旧接口）
     *
     * @param productId 商品ID
     * @return 是否成功
     */
    boolean deleteByProductId(String productId);
} 