package com.daddylab.supplier.item.application.item.tasks;

import cn.hutool.core.collection.CollUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.item.BanniuShippingWarehouseConfig;
import com.daddylab.supplier.item.application.item.CombinationItemSyncBanniuBizService;
import com.daddylab.supplier.item.application.item.ItemSyncBanniuBizService;
import com.daddylab.supplier.item.domain.banniu.BanniuCommonService;
import com.daddylab.supplier.item.domain.banniu.BanniuConverter;
import com.daddylab.supplier.item.domain.banniu.BanniuMiniService;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemBanniuRef;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemBanniuRefType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemBanniuRefService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.timing.StopWatch;
import com.daddylab.supplier.item.types.banniu.MiniTaskQuery;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import wjb.open.api.response.mini.MiniColumnListResponse;
import wjb.open.api.response.mini.MiniProjectListResponse;
import wjb.open.api.response.mini.MiniQueryTaskListResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/9/7
 */
@Service
@Slf4j
@AllArgsConstructor
public class BanniuShippingWarehouseJob {

    private final BanniuMiniService banniuMiniService;
    private final BanniuCommonService banniuCommonService;
    private final RefreshConfig refreshConfig;
    private final BanniuShippingWarehouseConfig banniuShippingWarehouseConfig;
    private final IItemSkuService itemSkuService;
    private final IItemBanniuRefService itemBanniuRefService;
    private final ItemSyncBanniuBizService itemSyncBanniuBizService;
    private final CombinationItemSyncBanniuBizService combinationItemSyncBanniuBizService;

    @XxlJob("BanniuShippingWarehouseJob:init")
    public void init() {
        final Integer projectId = banniuShippingWarehouseConfig.getShippingWarehouseProjectId();
        final MiniProjectListResponse.Project project =
                banniuMiniService
                        .project(projectId)
                        .orElseThrow(() -> new IllegalArgumentException("班牛发货仓ID配置无效"));
        final List<MiniColumnListResponse.ColumnOption> columnOptions =
                banniuMiniService.columnOptions(projectId);
        int pageSize = 100;
        final MiniTaskQuery countQuery = new MiniTaskQuery();
        countQuery.setProjectId(project.getProject_id());
        countQuery.setPageNum(1);
        countQuery.setPageSize(pageSize);
        final MiniQueryTaskListResponse.ResultMap countResult = banniuMiniService.tasks(countQuery);
        int pageNum = countResult.getTotal_page_num();
        log.info(
                "【班牛发货仓同步｜数据初始化】开始，数据总计={}，共{}页",
                countResult.getTotal(),
                countResult.getTotal_page_num());
        while (pageNum > 0) {
            log.info("【班牛发货仓同步｜数据初始化】正在处理第{}页数据", pageNum);
            final MiniTaskQuery query = new MiniTaskQuery();
            query.setProjectId(project.getProject_id());
            query.setPageNum(pageNum--);
            query.setPageSize(pageSize);
            final MiniQueryTaskListResponse.ResultMap tasks = banniuMiniService.tasks(query);
            final List<Map<Integer, String>> result = tasks.getResult();
            if (CollUtil.isEmpty(result)) {
                break;
            }
            final List<JSONObject> taskJsonObjs =
                    BanniuConverter.SHARED.convertData(columnOptions, result);
            for (JSONObject taskJsonObj : taskJsonObjs) {
                final String skuCode = taskJsonObj.getString("商品编码");
                final List<ItemSku> itemSkus =
                        itemSkuService
                                .lambdaQuery()
                                .eq(ItemSku::getSkuCode, skuCode)
                                .or()
                                .eq(ItemSku::getProviderSpecifiedCode, skuCode)
                                .list();
                if (itemSkus.isEmpty()) {
                    continue;
                }
                for (ItemSku itemSku : itemSkus) {
                    final ItemBanniuRef itemBanniuRef = new ItemBanniuRef();
                    itemBanniuRef.setBanniuId(taskJsonObj.getString("id"));
                    itemBanniuRef.setType(ItemBanniuRefType.ITEM);
                    itemBanniuRef.setFirstId(String.valueOf(itemSku.getItemId()));
                    itemBanniuRef.setSecondId(String.valueOf(itemSku.getId()));
                    try {
                        itemBanniuRefService.save(itemBanniuRef);
                    } catch (DuplicateKeyException e) {
                        log.warn(
                                "【班牛发货仓同步｜数据初始化】正在处理第{}页数据，唯一键冲突 itemSku:{} banniuData:{}",
                                pageNum,
                                itemSku,
                                taskJsonObj);
                    } catch (Exception e) {
                        log.error(
                                "【班牛发货仓同步｜数据初始化】正在处理第{}页数据，未知异常:{} itemSku:{} banniuData:{}",
                                pageNum,
                                e.getMessage(),
                                itemSku,
                                taskJsonObj,
                                e);
                    }
                }
            }
        }
        log.info("【班牛发货仓同步｜数据初始化】完成");
    }

    @Data
    public static class SyncAllParams {
        boolean syncItem;
        boolean syncCombinationItem;
        int limit = Integer.MAX_VALUE;
    }

    @XxlJob("BanniuShippingWarehouseJob:syncAll")
    public void syncAll() {
        final SyncAllParams syncAllParams =
                JSON.parseObject(XxlJobHelper.getJobParam(), SyncAllParams.class);
        syncAll(syncAllParams);
    }

    public void syncAll(SyncAllParams syncAllParams) {
        log.info("【班牛发货仓同步｜全量同步】开始同步");
        final StopWatch stopWatch = new StopWatch();

        if (syncAllParams.isSyncItem()) {
            stopWatch.start("后端商品同步");
            itemSyncBanniuBizService.syncAll(syncAllParams.getLimit());
            stopWatch.stop();
            log.info("【班牛发货仓同步｜全量同步】后端商品同步完成 {}", stopWatch);
        }
        if (syncAllParams.isSyncCombinationItem()) {
            stopWatch.start("组合商品同步");
            combinationItemSyncBanniuBizService.syncAll(syncAllParams.getLimit());
            stopWatch.stop();
            log.info("【班牛发货仓同步｜全量同步】组合商品同步完成 {}", stopWatch);
        }
        log.info("【班牛发货仓同步｜全量同步】同步完成 {}", stopWatch);
    }
}
