package com.daddylab.supplier.item.application.process;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ProcessInstRef;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IProcessInstRefService;
import com.daddylab.supplier.item.types.process.ProcessBusinessType;
import com.daddylab.supplier.item.types.process.ProcessType;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/10/20
 */
@Service
@Slf4j
public class ProcessBizServiceImpl implements ProcessBizService {
  @Autowired @Lazy private ProcessEngine processEngine;

  @Autowired private IProcessInstRefService processInstRefService;

  @Override
  public Response claim(Long userId, String taskId) {
    try {
      processEngine.getTaskService().setAssignee(taskId, String.valueOf(userId));
      return Response.buildSuccess();
    } catch (Exception e) {
      return Response.buildFailure(ErrorCode.BUSINESS_OPERATE_ERROR.getCode(), e.getMessage());
    }
  }

  @NonNull
  private Task getTask(String taskId) {
    final Task task =
        processEngine.getTaskService().createTaskQuery().taskId(taskId).singleResult();
    if (task == null) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "任务ID无效，可能任务已办理，请刷新页面后重试");
    }
    return task;
  }

  @Override
  @Transactional(rollbackFor = Throwable.class)
  public Response complete(Long userId, String taskId, String comment) {
    final Task task = getTask(taskId);
    if (!Objects.equals(task.getAssignee(), userId.toString())) {
      throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "您未被分配办理该任务");
    }
    processEngine.getIdentityService().setAuthenticatedUserId(userId.toString());
    if (comment != null) {
      processEngine.getTaskService().addComment(taskId, task.getProcessInstanceId(), comment);
    }
    processEngine.getTaskService().complete(taskId);
    return Response.buildSuccess();
  }

  @Override
  public Response forceComplete(Long userId, String taskId, String comment) {
    return forceComplete(userId, taskId, comment, null);
  }

  @Override
  public Response forceComplete(
      Long userId, String taskId, String comment, Map<String, Object> variables) {
    final Task task = getTask(taskId);
    if (userId != null) {
      if (userId > 0) {
        processEngine.getTaskService().setAssignee(task.getId(), userId.toString());
        processEngine.getIdentityService().setAuthenticatedUserId(userId.toString());
      }
    }
    if (comment != null) {
      processEngine.getTaskService().addComment(taskId, task.getProcessInstanceId(), comment);
    }
    if (variables != null) {
      processEngine.getTaskService().complete(taskId, variables);
    } else {
      processEngine.getTaskService().complete(taskId);
    }
    return Response.buildSuccess();
  }

  @Override
  public Optional<Task> task(String taskId) {
    return Optional.ofNullable(
        processEngine.getTaskService().createTaskQuery().taskId(taskId).singleResult());
  }

  @Override
  public List<Task> tasksByProcInstId(String processInstId) {
    return processEngine.getTaskService().createTaskQuery().processInstanceId(processInstId).list();
  }

  @Override
  public List<HistoricTaskInstance> historicTasksByProcInstId(String processInstId) {
    return processEngine
        .getHistoryService()
        .createHistoricTaskInstanceQuery()
        .processInstanceId(processInstId)
        .taskWithoutDeleteReason()
        .orderByTaskCreateTime()
        .desc()
        .list();
  }

  @Override
  public Response moveTo(Long userId, String processInstId, String activityName) {
    List<Execution> executions =
        processEngine
            .getRuntimeService()
            .createExecutionQuery()
            .processInstanceId(processInstId)
            .onlyChildExecutions()
            .list();
    Execution mainExecution =
        executions.stream()
            .filter(it -> Objects.equals(it.getParentId(), processInstId))
            .findFirst()
            .orElseThrow(
                () -> ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前流程无法进行定位"));
    final String currentActivityId =
        executions.stream()
            .map(Execution::getActivityId)
            .filter(Objects::nonNull)
            .findFirst()
            .orElse(null);
    if (currentActivityId == null) {
      throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前流程无法进行定位");
    }
    final ProcessInstance processInstance =
        processEngine
            .getRuntimeService()
            .createProcessInstanceQuery()
            .processInstanceId(processInstId)
            .singleResult();
    final String processDefinitionId = processInstance.getProcessDefinitionId();

    final BpmnModel bpmnModel =
        processEngine.getRepositoryService().getBpmnModel(processDefinitionId);
    final Process process = bpmnModel.getMainProcess();
    FlowElement currentFlowElement = bpmnModel.getFlowElement(currentActivityId);
    FlowElement flowElementMoveTo = null;
    for (FlowElement flowElement : process.getFlowElements()) {
      if (flowElement instanceof UserTask && flowElement.getName().equals(activityName)) {
        flowElementMoveTo = flowElement;
      }
    }
    if (flowElementMoveTo == null) {
      throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "指定的活动节点不存在");
    }
    processEngine
        .getRuntimeService()
        .createChangeActivityStateBuilder()
        .moveExecutionToActivityId(mainExecution.getId(), flowElementMoveTo.getId())
        .changeState();
    log.info(
        "[FLOWABLE] user {} move state process {}[{}] from {}[{}] to {}[{}]",
        userId,
        processInstId,
        process.getName(),
        currentFlowElement.getId(),
        currentFlowElement.getName(),
        flowElementMoveTo.getId(),
        flowElementMoveTo.getName());
    return Response.buildSuccess();
  }

  @Override
  public Response terminal(Long userId, String processInstId, String reason) {
    processEngine.getRuntimeService().deleteProcessInstance(processInstId, reason);
    log.info("[FLOWABLE] user {} terminal process {} for {}", userId, processInstId, reason);
    return Response.buildSuccess();
  }

  @Override
  public SingleResponse<ProcessInstance> create(
      Long userId,
      String processDefKey,
      ProcessBusinessType businessType,
      Long businessId,
      String name,
      String owner,
      Map<String, Object> variables) {
    return createToActivity(
        userId, processDefKey, businessType, businessId, name, owner, variables, null);
  }

  @Override
  public SingleResponse<ProcessInstance> createToActivity(
      Long userId,
      String processDefKey,
      ProcessBusinessType businessType,
      Long businessId,
      String name,
      String owner,
      Map<String, Object> variables,
      String startActivityName) {
    final String businessKey = businessType.getBusinessKey(businessId);
    final RuntimeService runtimeService = processEngine.getRuntimeService();
    final ProcessInstance processInstance =
        runtimeService
            .createProcessInstanceBuilder()
            .processDefinitionKey(processDefKey)
            .businessKey(businessKey)
            .name(name)
            .owner(owner)
            .variables(variables)
            .start();

    final String processInstId = processInstance.getId();
    log.info(
        "[FLOWABLE] user {} create process {}, name: {}, businessKey: {}",
        userId,
        processInstId,
        name,
        businessKey);

    if (startActivityName != null) {
      moveTo(userId, processInstId, startActivityName);
    }

    final ProcessInstRef processInstRef = new ProcessInstRef();
    processInstRef.setProcessInstId(processInstId);
    processInstRef.setType(businessType.getValue());
    processInstRef.setProcessType(1);
    processInstRef.setBusinessId(businessId);

    processInstRefService.saveProcessInstRef(
        businessId, processInstId, businessType, ProcessType.SELF_DEVELOPED, null);
    return SingleResponse.of(processInstance);
  }

  @Override
  public <T> T getVariable(String executionId, String name, Class<T> clazz) {
    return processEngine.getRuntimeService().getVariable(executionId, name, clazz);
  }

  @Override
  public List<Task> tasksByProcDefId(String processDefId, String taskDefId) {
    return processEngine
        .getTaskService()
        .createTaskQuery()
        .processDefinitionId(processDefId)
        .taskDefinitionId(taskDefId)
        .list();
  }

  @Override
  public ProcessInstance processInstance(String processInstanceId) {
    return processEngine
        .getRuntimeService()
        .createProcessInstanceQuery()
        .processInstanceId(processInstanceId)
        .singleResult();
  }

  @Override
  public void saveTask(Task task) {
    processEngine.getTaskService().saveTask(task);
  }
}
