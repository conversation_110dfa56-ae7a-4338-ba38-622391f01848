package com.daddylab.supplier.item.controller.drawer;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/11/16
 */
@Data
public class RollbackCmd {
    @ApiModelProperty("商品ID")
    private Long id;

    @ApiModelProperty("上新状态 TO_BE_IMPROVED 待完善 TO_BE_DESIGNED 待设计 TO_BE_AUDITED 待审核 TO_BE_UPDATED 待修改")
    private ItemLaunchStatus rollbackTo;

    @ApiModelProperty("审核状态（仅上新状态为待审核有效） WAIT_LEGAL_AUDIT 待法务审核 WAIT_QC_AUDIT 待QC审核")
    private ItemAuditStatus auditRollbackTo;
}
