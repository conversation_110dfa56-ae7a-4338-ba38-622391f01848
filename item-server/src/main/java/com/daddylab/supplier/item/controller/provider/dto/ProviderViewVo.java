package com.daddylab.supplier.item.controller.provider.dto;

import com.alibaba.cola.dto.DTO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ProviderStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ProviderType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年04月26日 4:51 PM
 */
@Data
public class ProviderViewVo extends DTO {

    @ApiModelProperty("ID")
    private Long id;

    /**
     * 供应商名称
     */
    @ApiModelProperty("供应商名称")
    private String name;

    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    private String contact;

    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    private String contactMobile;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty("统一社会信用代码")
    private String unifySocialCreditCodes;

    /**
     * 联系地址 省
     */
    @ApiModelProperty("省")
    private String provinceCode;

    /**
     * 联系地址 市
     */
    @ApiModelProperty("市")
    private String cityCode;

    /**
     * 联系地址 区/县
     */
    @ApiModelProperty("区/县")
    private String areaCode;

    /**
     * 联系地址 区/县
     */
    @ApiModelProperty("省市区地址")
    private String codeStr;

    /**
     * 联系地址 详细地址
     */
    @ApiModelProperty("详细地址")
    private String address;

    /**
     * 状态 1:合作 2:停用
     */
    @ApiModelProperty("状态")
    private ProviderStatus status;

    /**
     * 供应商类型 1:自营 2:代发
     */
    @ApiModelProperty("供应商类型")
    private ProviderType type;

    /**
     * 关联合作伙伴系统中的供应商ID，没有则为0
     */
    @ApiModelProperty("关联P统供应商")
    private Long partnerProviderId;

    @ApiModelProperty("此供应商是否来自于合作伙伴系统")
    private Boolean isFromPartner;

    /**
     * 银行账户
     */
    @ApiModelProperty("银行账户")
    private List<BankAccountVO> bankAccountVOList;

    /**
     * 供应商编号
     */
    @ApiModelProperty("供应商编号")
    private String providerNo;

    @ApiModelProperty("负责人")
    private Long mainChargerUserId;

    /**
     * 主要负责人
     */
    @ApiModelProperty("主要负责人（花名）")
    private String mainChargerUserName;

    @ApiModelProperty("次要负责人")
    private Long secondChargerUserId;

    /**
     * 商家后台。shopId;
     */
    @ApiModelProperty("商家后台店铺ID")
    private String mallShopId;

    /**
     * 是否黑名单
     */
    @ApiModelProperty("是否黑名单")
    private Integer isBlacklist;

    /**
     * 合作方
     */
    @ApiModelProperty("合作方")
    private List<Integer> corpType;


}
