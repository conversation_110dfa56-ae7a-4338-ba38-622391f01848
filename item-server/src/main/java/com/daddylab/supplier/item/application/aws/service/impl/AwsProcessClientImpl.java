package com.daddylab.supplier.item.application.aws.service.impl;

import com.actionsoft.bpms.api.ApiException;
import com.actionsoft.bpms.api.OpenApiClient;
import com.actionsoft.bpms.api.common.ApiResponse;
import com.actionsoft.sdk.service.model.HistoryTaskInstance;
import com.actionsoft.sdk.service.model.TaskInstance;
import com.actionsoft.sdk.service.model.TaskQueryModel;
import com.actionsoft.sdk.service.response.BoolResponse;
import com.actionsoft.sdk.service.response.ListMapResponse;
import com.actionsoft.sdk.service.response.MapResponse;
import com.actionsoft.sdk.service.response.StringResponse;
import com.actionsoft.sdk.service.response.process.ProcessInstResponse;
import com.actionsoft.sdk.service.response.task.HisTaskInstsGetResponse;
import com.actionsoft.sdk.service.response.task.TaskInstGetResponse;
import com.actionsoft.sdk.service.response.task.TaskInstsGetResponse;
import com.daddylab.supplier.item.application.aws.AwsConstant;
import com.daddylab.supplier.item.application.aws.service.AwsProcessClient;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class AwsProcessClientImpl implements AwsProcessClient {

    private final OpenApiClient client;

    public AwsProcessClientImpl(OpenApiClient client) {
        this.client = client;
    }

    public AwsProcessClientImpl(String url, String accessKey, String secret) {
        this.client = new OpenApiClient(url, accessKey, secret);
    }

    @Override
    public String processCreate(String processDefId, String title, String uid) throws ApiException {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(3);
        params.put("processDefId", processDefId);
        params.put("uid", uid);
        params.put("title", title);
        ProcessInstResponse processInstResponse = client.exec(AwsConstant.PROCESS_CREATE, params,
                ProcessInstResponse.class);
        return processInstResponse.getData().getId();
    }

    @Override
    public void processStart(String processInstId) throws ApiException {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(1);
        args.put("processInstId", processInstId);
        client.exec(AwsConstant.PROCESS_START, args, ApiResponse.class);
    }

    @Override
    public void boCreate(String boName, String processInstId, Map<String, Object> data,
            String uid) {
        final HashMap<String, Object> boCreateMap = new HashMap<>();
        boCreateMap.put("boName", boName);
        boCreateMap.put("recordData", data);
        boCreateMap.put("bindId", processInstId);
        boCreateMap.put("uid", uid);
        client.exec(AwsConstant.BO_CREATE, boCreateMap, StringResponse.class);
    }

    @Override
    public void boFieldUpdate(String boName, String processInstId, String fieldName, Object value) {
        final HashMap<String, Object> boCreateMap = new HashMap<>();
        boCreateMap.put("boName", boName);
        boCreateMap.put("bindId", processInstId);
        boCreateMap.put("fieldName", fieldName);
        boCreateMap.put("value", value);
        client.exec(AwsConstant.BO_FIELD_UPDATE, boCreateMap, StringResponse.class);
    }

    @Override
    public List<Map<String, Object>> boQuery(String boName, Object querys, String selectClause,
            String orderBy,
            Integer firstRow, Integer rowCount) {
        final HashMap<String, Object> params = new HashMap<>();
        params.put("boName", boName);
        params.put("querys", JsonUtil.toJson(querys));
        params.put("selectClause", selectClause);
        params.put("orderBy", orderBy);
        params.put("firstRow", firstRow);
        params.put("rowCount", rowCount);
        final ListMapResponse response = client.exec(AwsConstant.BO_QUERY, params,
                ListMapResponse.class);
        return response.getData();
    }

    @Override
    public void boRemoveBindId(String boName, String processInstId) {
        final HashMap<String, Object> boCreateMap = new HashMap<>();
        boCreateMap.put("boName", boName);
        boCreateMap.put("bindId", processInstId);
        client.exec(AwsConstant.BO_REMOVE_BINDID, boCreateMap, StringResponse.class);
    }

    @Override
    public ProcessInstResponse processInstGet(String processInstId) throws ApiException {
        Map<String, Object> params = new HashMap<>();
        params.put("processInstId", processInstId);
        return client.exec(AwsConstant.PROCESS_INST_GET, params, ProcessInstResponse.class);
    }

    @Override
    public void processRestart(String processInstId) throws ApiException {
        Map<String, Object> params = new HashMap<>();
        params.put("processInstId", processInstId);
        client.exec(AwsConstant.PROCESS_RESTART, params, ApiResponse.class);
    }

    @Override
    public void processReactive(String processInstId, String targetActivityId,
            Boolean isClearHistory, String uid,
            String targetUID, String reactivateReason) throws ApiException {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(6);
        args.put("processInstId", processInstId);
        args.put("targetActivityId", targetActivityId);
        args.put("isClearHistory", true);
        args.put("uid", uid);
        args.put("targetUID", targetUID);
        args.put("reactivateReason", reactivateReason);
        TaskInstGetResponse r = client.exec(AwsConstant.PROCESS_REACTIVATE, args,
                TaskInstGetResponse.class);
    }

    @Override
    public Boolean processEndCheck(String processInstId) {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(1);
        params.put("processInstId", processInstId);
        BoolResponse r = client.exec(AwsConstant.PROCESS_END_CHECK, params, BoolResponse.class);
        return r.isData();
    }

    @Override
    public Boolean processTerminate(String processInstId, String uid) throws ApiException {
        Map<String, Object> params = Maps.newHashMapWithExpectedSize(2);
        params.put("processInstId", processInstId);
        params.put("uid", uid);
        ApiResponse r = client.exec(AwsConstant.PROCESS_TERMINATE, params, ApiResponse.class);
        return r.isSuccess();
    }

    @Override
    public List<TaskInstance> taskQuery(String processInstId) throws ApiException {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(1);
        TaskQueryModel taskQueryModel = new TaskQueryModel();
        taskQueryModel.setProcessInstId(processInstId);
        args.put("tqm", taskQueryModel);
        args.put("firstRow", 0);
        args.put("rowCount", 500);
        TaskInstsGetResponse taskResponse = client.exec(AwsConstant.TASK_QUERY_PAGE, args,
                TaskInstsGetResponse.class);
        return taskResponse.getData();
    }

    @Override
    public List<TaskInstance> taskQuery(TaskQueryModel model) throws ApiException {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(1);
        args.put("tqm", model);
        args.put("firstRow", 0);
        args.put("rowCount", 500);
        TaskInstsGetResponse taskResponse = client.exec(AwsConstant.TASK_QUERY_PAGE, args,
                TaskInstsGetResponse.class);
        return taskResponse.getData();
    }

    @Override
    public List<HistoryTaskInstance> historyTaskQuery(String processInstId) throws ApiException {
        String apiMethod = AwsConstant.TASK_HISTORY_QUERY;
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(1);
        TaskQueryModel taskQueryModel = new TaskQueryModel();
        taskQueryModel.setProcessInstId(processInstId);
        args.put("tqm", taskQueryModel);
        HisTaskInstsGetResponse taskResponse = client.exec(apiMethod, args,
                HisTaskInstsGetResponse.class);
        return taskResponse.getData();
    }

    @Override
    public void taskCommentCommit(String taskInstId, String user, String actionName, String commentMsg,
            Boolean isIgnoreDefaultSetting) {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(4);
        args.put("taskInstId", taskInstId);
        args.put("user", user);
        args.put("actionName", actionName);
        args.put("commentMsg", commentMsg);
        args.put("isIgnoreDefaultSetting", isIgnoreDefaultSetting);
        client.exec(AwsConstant.TASK_COMMENT_COMMIT, args, ApiResponse.class);
    }

    @Override
    public void taskComplete(String taskInstId, String uid) {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(2);
        args.put("taskInstId", taskInstId);
        args.put("uid", uid);
        MapResponse r = client.exec(AwsConstant.TASK_COMPLETE, args, MapResponse.class);
        log.info("taskComplete:{}", JsonUtil.toJson(r.getData()));
    }

    @Override
    public boolean taskDelegate(String taskInstId, String uid, String targetUid, String reason)
            throws ApiException {
        Map<String, Object> args = new HashMap<>();
        args.put("taskInstId", taskInstId);
        args.put("uid", uid);
        args.put("targetUID", targetUid);
        args.put("delegateReason", reason);
        ApiResponse res = client.exec(AwsConstant.TASK_DELEGATE, args, ApiResponse.class);
        return res.isSuccess();
    }

    @Override
    public String taskParticipantsGet(String uid, String processInstId, String taskInstId, String nextUserTaskDefId) {
        Map<String, Object> args = new HashMap<>();
        args.put("taskInstId", taskInstId);
        args.put("uid", uid);
        args.put("processInstId", processInstId);
        args.put("nextUserTaskDefId", nextUserTaskDefId);
        StringResponse res = client.exec(AwsConstant.TASK_PARTICIPANTS_GET, args, StringResponse.class);
        return res.getData();
    }

}