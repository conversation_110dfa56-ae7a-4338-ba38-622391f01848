package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;

import java.io.Serializable;

/**
 * <p>
 * 后台类目
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Category implements Serializable {

    private static final long serialVersionUID = 3215079334269123954L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    protected Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 所属一级类目（根类目）
     */
    private Long rootId;

    /**
     * 路径,逗号分隔。
     */
    private String path;

    /**
     * 上级类目
     */
    private Long parentId;

    /**
     * 层级 品类级别，1，2，3，4。1为顶级，最多5级。
     */
    private Integer level;

    /**
     * 删除时间
     */
    private Long deletedAt;

    /**
     * 第三方分配的id
     * 金蝶：FParentId 父分组内码。
     */
    private String kingDeeId;

    /**
     * 品类名称简称
     */
    private String shortName;

    /**
     * 创建时间createAt
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    protected Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(value = "created_uid", fill = FieldFill.INSERT)
    protected Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(value = "updated_at", fill = FieldFill.UPDATE)
    protected Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(value = "updated_uid", fill = FieldFill.UPDATE)
    protected Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    protected Integer isDel;

    private String kingDeeNum;

    public String getRootCategoryName() {
        return getRootCategoryName(getName());
    }

    @NonNull
    public static String getRootCategoryName(String categoryPath) {
        return StrUtil.split(categoryPath, ",").stream()
                .filter(v -> !"新类目".equals(v))
                .findFirst()
                .orElse("");
    }


}
