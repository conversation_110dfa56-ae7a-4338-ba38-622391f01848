package com.daddylab.supplier.item.domain.message.entity;

import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.domain.message.dto.MsgFillObj;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Message;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.MessageConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessageState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessageType;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/23 4:15 下午
 * @description
 */
public class MessageEntity {

    /**
     * 此消息对应的配置
     */
    private final MessageConfig config;


    public MessageEntity(MessageConfig config) {
        this.config = config;
    }


    /**
     * 构建后端商品的messageDB实体
     *
     * @param msgFillObjList
     * @param recipientId
     * @return
     */
    public Message getMessage(List<MsgFillObj> msgFillObjList, Long recipientId) {
        Message message = new Message();
        message.setConfigId(config.getId());
        message.setTemplate(config.getTemplate());
        message.setState(MessageState.NO_READ);
        message.setType(MessageType.SYSTEM);
        message.setRecipientId(recipientId);

        message.setPushDefault(config.getPushDefault());
        message.setPushRemind(config.getPushRemind());
        message.setPushText(config.getPushText());
        message.setPushMail(config.getPushMail());

        message.setFillContent(JsonUtil.toJson(msgFillObjList));
        return message;
    }

    /**
     * 构造 需要 推送到系统之外的 消息内容
     *
     * @param template
     * @param fillContentObjList
     * @param link
     * @return
     */
    public String getTextContent(String template, List<MsgFillObj> fillContentObjList, String link) {

        final Object[] fillObjArray = fillContentObjList.stream().sorted((o1, o2) -> {
            if (o1.getIndex() > o2.getIndex()) {
                return 1;
            } else if (o1.getIndex().equals(o2.getIndex())) {
                return 0;
            } else {
                return -1;
            }
        }).map(MsgFillObj::getVal).toArray(Object[]::new);
        String content = StrUtil.format(template, fillObjArray);

        return content + "。" + GlobalConstant.PUSH_MSG_SUFFIX + "：" + link;
    }

    public String getEmailContent(String template, List<MsgFillObj> fillContentObjList) {

        final Object[] fillObjArray = fillContentObjList.stream().sorted((o1, o2) -> {
            if (o1.getIndex() > o2.getIndex()) {
                return 1;
            } else if (o1.getIndex().equals(o2.getIndex())) {
                return 0;
            } else {
                return -1;
            }
        }).map(MsgFillObj::getVal).toArray(Object[]::new);
        String content = StrUtil.format(template, fillObjArray);

        return content + "。" + GlobalConstant.PUSH_MSG_SUFFIX;
    }

}
