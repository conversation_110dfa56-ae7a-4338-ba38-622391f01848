package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchModuleAdvice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemLaunchModuleAdviceMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemLaunchModuleAdviceService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品库模块审批修改建议 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
@Service
public class ItemLaunchModuleAdviceServiceImpl extends DaddyServiceImpl<ItemLaunchModuleAdviceMapper, ItemLaunchModuleAdvice> implements IItemLaunchModuleAdviceService {

}
