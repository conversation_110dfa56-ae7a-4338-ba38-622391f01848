package com.daddylab.supplier.item.application.message.event;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.domain.message.dto.MsgFillObj;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessageOperationType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022/4/18 5:27 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysPurchaseOrderEvent extends AbstractMessageEvent {

    private final Integer month;

    private final String link;

    private SysPurchaseOrderEvent(Integer month, String link) {
        this.month = month;
        this.link = link;
    }

    @Override
    public String getPushLink() {
        return "";
    }

    @Override
    public List<MsgFillObj> getFillObjList() {
        MsgFillObj msgFillObj1 = new MsgFillObj();
        msgFillObj1.setIndex(0);
        msgFillObj1.setVal(month.toString());
        msgFillObj1.setLink("");

        MsgFillObj msgFillObj2 = new MsgFillObj();
        msgFillObj2.setIndex(1);
        msgFillObj2.setVal("查看详情");
        msgFillObj2.setLink(link);
        msgFillObj2.setColor("#3370FF");

        return ListUtil.of(msgFillObj1, msgFillObj2);
    }

    @Override
    public String getEmailSubject() {
        return "";
    }

    public static SysPurchaseOrderEvent build(Integer month) {
        String activeProfile = SpringUtil.getActiveProfile();
        String link = "http://p.dlab.cn/erp/commodity-purchase/purchase-order/list";
        if ("prod".equals(activeProfile)) {
            link = "https://erp-ol.daddylab.com/erp/commodity-purchase/purchase-order/list";
        }
        if ("gray".equals(activeProfile)) {
            link = "https://p-gray.daddylab.com/erp/commodity-purchase/purchase-order/list";
        }

        SysPurchaseOrderEvent event = new SysPurchaseOrderEvent(month, link);
        event.setOperationType(MessageOperationType.SYS_PURCHASE_ORDER);
        return event;
    }


}
