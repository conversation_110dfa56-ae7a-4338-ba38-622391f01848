package com.daddylab.supplier.item.application.third;

import java.util.Map;

/**
 * <AUTHOR>
 * @class ThirdCallbackService.java
 * @description 描述类的作用
 * @date 2024-02-29 14:39
 */
public interface ThirdCallbackService {

    /**
     * 快手回调消息返回
     *
     * @param privateMessage String
     * @return java.lang.String
     * @date 2024/2/29 14:41
     * <AUTHOR>
     */
    Map<String, Object> ksMessageCallback(String privateMessage);

    /**
     * 小红书回调消息返回
     *
     * @param message String
     * @return java.lang.String
     * @date 2024/2/29 14:41
     * <AUTHOR>
     */
    Map<String, Object> redBookMessageCallback(String message);
}
