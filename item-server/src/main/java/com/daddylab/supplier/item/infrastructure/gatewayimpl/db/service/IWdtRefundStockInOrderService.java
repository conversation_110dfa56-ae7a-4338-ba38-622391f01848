package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundStockInOrder;

/**
 * <p>
 * 旺店通退货入库单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
public interface IWdtRefundStockInOrderService extends IDaddyService<WdtRefundStockInOrder> {

}
