package com.daddylab.supplier.item.infrastructure.gatewayimpl.user;

import cn.hutool.core.util.RandomUtil;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.domain.user.gateway.UserGatewayCacheOps;
import com.daddylab.supplier.item.infrastructure.cache.Cache;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.core.task.TaskRejectedException;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.inject.Inject;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component("UserGatewayCacheImpl")
@Slf4j
@Primary
public class UserGatewayCacheImpl implements UserGateway, UserGatewayCacheOps {
    private static final int CACHE_TIMEOUT = 600;
    @Inject
    private UserGateway userGateway;

    @Override
    public Map<Long, StaffInfo> batchQueryStaffInfoByIds(List<Long> userIds) {
        final List<String> userIdCks = userIds.stream().map(this::userStaffInfoCacheKey).collect(Collectors.toList());
        final Map<String, StaffInfo> cacheData = RedisUtil.multiGet(userIdCks, StaffInfo.class);
        final Map<Long, StaffInfo> returnData = new HashMap<>(userIds.size());
        final List<Long> cacheMissing = new ArrayList<>();
        for (Long userId : userIds) {
            final StaffInfo staffInfo = cacheData.get(userStaffInfoCacheKey(userId));
            if (staffInfo != null) {
                returnData.put(userId, staffInfo);
            } else {
                cacheMissing.add(userId);
            }
        }
        if (cacheMissing.isEmpty()) {
            return returnData;
        }
        log.debug("未命中缓存的用户ID:{}", cacheMissing);
        final Map<Long, StaffInfo> staffInfos = userGateway.batchQueryStaffInfoByIds(cacheMissing);
        for (Long userId : userIds) {
            final StaffInfo staffInfo = staffInfos.get(userId);
            if (staffInfo != null) {
                returnData.put(userId, staffInfo);
            }
        }
        cacheStaffInfoAsync(staffInfos);
        return returnData;
    }

    @Override
    @Cache(keyPrefix = UserCacheKeys.USER_STAFF_INFO, timeout = CACHE_TIMEOUT, cacheType = Cache.CacheType.CAFFEINE)
    public StaffInfo queryStaffInfoById(Long userId) {
        return batchQueryStaffInfoByIds(Collections.singletonList(userId)).get(userId);
    }

    @Override
    @Cache(keyPrefix = UserCacheKeys.QUERY_USER_STAFF_INFO, keySerialize = Cache.KeySerialize.TO_STRING_HASH, timeout = CACHE_TIMEOUT)
    public List<StaffInfo> queryStaffList(StaffListQuery query) {
        return userGateway.queryStaffList(query);
    }

    @Override
    @Cache(keyPrefix = UserCacheKeys.USER_STAFF_INFO, timeout = CACHE_TIMEOUT, cacheType = Cache.CacheType.CAFFEINE)
    public StaffInfo queryStaffInfoByNickname(String nickname) {
        return userGateway.queryStaffInfoByNickname(nickname);
    }

    @Override
    @Cache(keyPrefix = UserCacheKeys.USER_NICK_NAME, timeout = CACHE_TIMEOUT, cacheType = Cache.CacheType.CAFFEINE)
    public String queryNikcNameByLoginName(String loginName) {
        return userGateway.queryNikcNameByLoginName(loginName);
    }

    private void cacheStaffInfoAsync(Map<Long, StaffInfo> staffInfos) {
        try {
            ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
                cacheStaffInfo(staffInfos);
            });
        } catch (TaskRejectedException e) {
            cacheStaffInfo(staffInfos);
        }
    }

    private void cacheStaffInfo(Map<Long, StaffInfo> staffInfos) {
        final Map<String, Object> setMap = Maps.newHashMap();
        for (Map.Entry<Long, StaffInfo> staffInfoEntry : staffInfos.entrySet()) {
            final String key = userStaffInfoCacheKey(staffInfoEntry.getKey());
            setMap.put(key, staffInfoEntry.getValue());
        }
        RedisUtil.multiSet(setMap, false, CACHE_TIMEOUT * 1000L);
    }

    @NonNull
    private String userStaffInfoCacheKey(Long userId) {
        return UserCacheKeys.USER_STAFF_INFO + userId;
    }

    @Override
    public void clearAllCache() {
        clearStaffInfoCache();
        clearQueryStaffListCache();
    }

    @Override
    public void clearStaffInfoCache() {
        scanExpireKeys(UserCacheKeys.USER_STAFF_INFO + "*");
    }

    @Override
    public void clearStaffInfoCache(Long userId) {
        RedisUtil.del(userStaffInfoCacheKey(userId));
    }

    @Override
    public void clearQueryStaffListCache() {
        scanExpireKeys(UserCacheKeys.QUERY_USER_STAFF_INFO + "*");
    }

    private void scanExpireKeys(String pattern) {
        log.info("扫描清空缓存开始，pattern:{}", pattern);
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        final int bufferSize = 100;
        final long count = 1000L;
        final Set<String> keys = Sets.newHashSetWithExpectedSize(bufferSize);
        RedisUtil.scan(pattern, count, key -> {
            log.debug("scan:{}", key);
            keys.add(key);
            if (keys.size() >= bufferSize) {
                RedisUtil.multiExpire(keys, RandomUtil.randomLong(1000, 10000));
                keys.clear();
            }
        });
        if (!keys.isEmpty()) {
            RedisUtil.multiExpire(keys, RandomUtil.randomLong(1000, 10000));
        }
        stopWatch.stop();
        log.info("扫描清空缓存成功，耗时:{}ms", stopWatch.getTotalTimeMillis());
    }
}
