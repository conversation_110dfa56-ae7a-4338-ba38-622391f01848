/*
package com.daddylab.supplier.item.application.virtualWarehouse;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.virtualWarehouse.dto.*;

import java.util.List;

public interface StockConfigDetailBizService {

    */
/**
     * 库存明细设置。
     *
     * @param pageQuery
     * @return
     *//*

    PageResponse<StockConfigDetailPageVO> page(StockConfigDetailPageQuery pageQuery);

    */
/**
     * 更新 库存明细设置
     *
     * @param cmdList
     * @return
     *//*

    Response updateStockConfigDetail(List<StockConfigDetailDto> cmdList);

    */
/**
     * 检查SKU是否满足占比
     *
     * @param vwStockConfigDetailCmd
     * @return
     *//*

    MultiResponse<SkuRatioPrompt> checkSkuRatio(VwStockConfigDetailCmd vwStockConfigDetailCmd);

    MultiResponse<SkuRatioPrompt> checkVirtualWarehouseSkuRatio0(VwStockConfigDetailCmd vwStockConfigDetailCmd);

    MultiResponse<SkuRatioPrompt> checkShopSkuRatio(ShopStockConfigDetailCmd shopStockConfigDetailCmd);

}
     */
