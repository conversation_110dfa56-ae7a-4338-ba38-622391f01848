package com.daddylab.supplier.item.application.payment.wrietoff;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.DoProcessRes;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.ProcessRequest;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.SalesOrderDto;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.SkuUnitDto;
import com.daddylab.supplier.item.application.salesInStock.SalesInStockBizService;
import com.daddylab.supplier.item.application.salesOutStock.SalesOutStockBizService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderSettlementFormService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPaymentOrderWriteOffLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtRefundStockInOrderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtSaleStockOutOrderService;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2024年01月22日 10:20 AM
 */
@Component
@Slf4j
@Order(3)
public class SupplementSalesOrderProcess implements WriteOffProcess {

    final SalesOutStockBizService salesOutStockBizService;
    final SalesInStockBizService salesReturnBizService;
    final IOrderSettlementFormService iOrderSettlementFormService;
    final IPaymentOrderWriteOffLogService iPaymentOrderWriteOffLogService;
    final IWdtSaleStockOutOrderService iWdtSaleStockOutOrderService;
    final IWdtRefundStockInOrderService iWdtRefundStockInOrderService;

    @Autowired
    public SupplementSalesOrderProcess(SalesOutStockBizService salesOutStockBizService, SalesInStockBizService salesReturnBizService,
                                       IOrderSettlementFormService iOrderSettlementFormService, IPaymentOrderWriteOffLogService iPaymentOrderWriteOffLogService,
                                       IWdtSaleStockOutOrderService iWdtSaleStockOutOrderService, IWdtRefundStockInOrderService iWdtRefundStockInOrderService) {
        this.salesOutStockBizService = salesOutStockBizService;
        this.salesReturnBizService = salesReturnBizService;
        this.iOrderSettlementFormService = iOrderSettlementFormService;
        this.iPaymentOrderWriteOffLogService = iPaymentOrderWriteOffLogService;
        this.iWdtSaleStockOutOrderService = iWdtSaleStockOutOrderService;
        this.iWdtRefundStockInOrderService = iWdtRefundStockInOrderService;
    }

    private ProcessRequest processRequest;

    public void setProcessRequest(ProcessRequest processRequest) {
        this.processRequest = processRequest;
    }

    @Override
    public Boolean doProcess() {
        DoProcessRes<List<ResDto>> doProcessRes = doProcess0();
        List<String> logs = doProcessRes.getLogs();
        Boolean isSuccess = doProcessRes.getIsSuccess();
        List<ResDto> resDtoList = doProcessRes.getData();
        if (!isSuccess) {
            rollback(resDtoList, logs);
        }

        PaymentOrderWriteOffLog stepLog = processRequest.getStepLog();
        stepLog.setTraceMsg(StrUtil.join(StrUtil.CR, logs));
        iPaymentOrderWriteOffLogService.save(stepLog);
        return isSuccess;
    }


    @Data
    private static class ResDto {
        private Integer type;
        private String no;
    }


    public DoProcessRes<List<ResDto>> doProcess0() {
        DoProcessRes<List<ResDto>> doProcessRes = new DoProcessRes<>();
        List<ResDto> resDtoList = new ArrayList<>();
        boolean isSuccess = true;

        List<SkuUnitDto> diffSkuUnitDtoList = processRequest.getDiffSkuUnitDtoList();
        OrderSettlementForm orderSettlementForm = processRequest.getOrderSettlementForm();
        Long auditDate = processRequest.getAuditDate();
        Boolean mockSync = processRequest.getMockSync();

        List<String> traceLogList = new LinkedList<>();
        // 销售出库单
        List<SkuUnitDto> salesOutList = diffSkuUnitDtoList.stream().filter(val -> val.getNum() > 0).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(salesOutList)) {
            SingleResponse<SalesOrderDto> salesOutRes = salesOutStockBizService.generateOrderBySettlement(
                    orderSettlementForm.getWarehouseNo0(), salesOutList, auditDate, mockSync);
            log.info("冲销流程,销售出库单res:{}", JsonUtil.toJson(salesOutRes));
            if (salesOutRes.isSuccess()) {
                SalesOrderDto data = salesOutRes.getData();
                String traceLog = StrUtil.format("补偿生成销售出库单完成,res:{}", JsonUtil.toJson(data));
                traceLogList.add(traceLog);
                orderSettlementForm.setSalesOutOrderNo(data.getNo());
                iOrderSettlementFormService.updateById(orderSettlementForm);

                ResDto resDto = new ResDto();
                resDto.setType(1);
                resDto.setNo(data.getNo());
                resDtoList.add(resDto);
            } else {
                String traceLog = StrUtil.format("补偿生成销售出库单异常,res:{}", salesOutRes.getErrMessage());
                traceLogList.add(traceLog);
                isSuccess = false;
            }
            log.info("销售出库单处理完毕.res:{}", JsonUtil.toJson(salesOutRes));
        } else {
            String traceLog = StrUtil.format("无需补偿生成销售出库单");
            traceLogList.add(traceLog);
        }

        // 销售退货单
        List<SkuUnitDto> salesReturnList = diffSkuUnitDtoList.stream().filter(val -> val.getNum() < 0).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(salesReturnList)) {
            SingleResponse<SalesOrderDto> salesReturnRes = salesReturnBizService.generateOrderBySettlement(
                    orderSettlementForm.getWarehouseNo0(), salesReturnList, auditDate, mockSync);
            log.info("冲销流程,销售退货单(销退入库)res:{}", JsonUtil.toJson(salesReturnRes));
            if (salesReturnRes.isSuccess()) {
                SalesOrderDto data = salesReturnRes.getData();
                String traceLog = StrUtil.format("补偿生成销售退货单(销退入库)完成,res:{}", JsonUtil.toJson(data));
                traceLogList.add(traceLog);
                orderSettlementForm.setSalesReturnOrderNo(data.getNo());
                iOrderSettlementFormService.updateById(orderSettlementForm);

                ResDto resDto = new ResDto();
                resDto.setType(2);
                resDto.setNo(data.getNo());
                resDtoList.add(resDto);
            } else {
                String traceLog = StrUtil.format("补偿生成销售退货单(销退入库)异常,res:{}", salesReturnRes.getErrMessage());
                traceLogList.add(traceLog);
                isSuccess = false;
            }
            log.info("销售退货单处理完毕.res:{}", JsonUtil.toJson(salesReturnRes));
        } else {
            String traceLog = StrUtil.format("无需补偿生成销售退货单(销退入库)");
            traceLogList.add(traceLog);
        }

        doProcessRes.setLogs(traceLogList);
        doProcessRes.setIsSuccess(isSuccess);
        doProcessRes.setData(resDtoList);
        return doProcessRes;
    }

    public void rollback(List<ResDto> resDtoList, List<String> logs) {
        resDtoList.stream().filter(val -> val.getType() == 1).forEach(val -> {
            iWdtSaleStockOutOrderService.lambdaUpdate().eq(WdtSaleStockOutOrder::getOrderNo, val.getNo()).remove();
            logs.add(StrUtil.format("销售出库单回滚结束.已删除销售出库单号:{}", val.getNo()));
        });
        resDtoList.stream().filter(val -> val.getType() == 2).forEach(val -> {
            iWdtRefundStockInOrderService.lambdaUpdate().eq(WdtRefundStockInOrder::getOrderNo, val.getNo()).remove();
            logs.add(StrUtil.format("销售退货单回滚结束.已删除销售退货单号:{}", val.getNo()));
        });
    }
}
