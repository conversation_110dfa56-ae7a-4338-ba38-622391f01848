package com.daddylab.supplier.item.application.order.settlement.sys;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.daddylab.supplier.item.application.order.settlement.dto.SkuDeliverNumDo;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.WrapperType;
import com.daddylab.supplier.item.common.ExternalShopWdtNo;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailWrapperMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RCountDownLatch;
import org.redisson.api.RSemaphore;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2023年08月22日 1:48 PM
 */
@Service
@Slf4j
public class OrderSettlementSysService {

    @Resource
    IPurchaseOrderService iPurchaseOrderService;

    @Resource
    IPurchaseOrderDetailService iPurchaseOrderDetailService;

    @Resource
    WdtRefundOrderMapper wdtRefundOrderMapper;

    @Resource
    WdtOrderDetailWrapperMapper wdtOrderDetailWrapperMapper;

    @Resource
    OrderSettlementSysDao orderSettlementSysDao;

    @Resource
    ISkuPriceExplainService iSkuPriceExplainService;

    @Resource
    RedissonClient redisson;

    @Resource
    IOrderSettlementFormService iOrderSettlementFormService;

    @Resource
    IOrderSettlementDetailService iOrderSettlementDetailService;

    @Resource
    IItemSkuService iItemSkuService;

    @Resource
    IItemService iItemService;

    @Resource
    IWdtOrderDetailWrapperService iWdtOrderDetailWrapperService;

    @Resource
    ExternalShopWdtNo externalShopWdtNo;

    public void supplementData(TimeBO timeBo) {
        // 以采购订单的数据为准

        return;

//        List<PurchaseOrder> targetOrders = iPurchaseOrderService.lambdaQuery()
//                // 目标月份，自然月，最后一天的0点。10位时间戳
//                .eq(PurchaseOrder::getPurchaseDate, timeBo.getPurchaseDateTime())
//                .eq(PurchaseOrder::getType, 2)
//                .eq(PurchaseOrder::getBuyerUserId, -1)
//                .list();
//        if (CollUtil.isEmpty(targetOrders)) {
//            log.info("supplementData finished. targetPurchaseOrders is empty");
//            return;
//        }
//        // 补偿对象是系统结算单
//        List<String> purchaseOrderNos = targetOrders.stream().map(PurchaseOrder::getNo).collect(Collectors.toList());
//        // 存在差额，对差额进行补偿
//        List<String> supplementPnoList = new ArrayList<>();
//        purchaseOrderNos.forEach(pNo -> {
//            Integer count = iOrderSettlementFormService.lambdaQuery()
//                    .eq(OrderSettlementForm::getPurchaseOrderNo, pNo)
//                    .and(ww -> ww.isNull(OrderSettlementForm::getNo).or().eq(OrderSettlementForm::getNo, Strings.EMPTY))
//                    .count();
//            if (count == 0) {
//                supplementPnoList.add(pNo);
//            }
//        });
//        // 补偿
//        log.info("supplementData supplementPNoList size:{}", supplementPnoList.size());
//        supplementPnoList.forEach(pNo -> {
//            log.info("supplementData start.pNo:{}", pNo);
//            autoCreateOrderSettlementInfo(timeBo, pNo);
//        });
    }


    /**
     * 自动化流程 生成订单结算-工厂 分页列表
     *
     * @param timeBo          各种时间数据的封装
     * @param purchaseOrderNo 采购订单号
     */
    public void autoCreateOrderSettlementInfo(TimeBO timeBo, String purchaseOrderNo) {
        List<PurchaseOrder> targetOrders;
        if (SpringUtil.getActiveProfile().equals("test") || SpringUtil.getActiveProfile().equals("dev")) {
            targetOrders = iPurchaseOrderService.lambdaQuery()
                    .eq(StringUtils.hasText(purchaseOrderNo), PurchaseOrder::getNo, purchaseOrderNo)
                    .list();
        } else {
            // 目标月份上一个自然月，最后一天的0点。10位时间戳
            // 系统生成的工厂类型采购单，采购日期是目标月份。
            targetOrders = iPurchaseOrderService.lambdaQuery()
                    .eq(PurchaseOrder::getPurchaseDate, timeBo.getPurchaseDateTime())
                    .eq(PurchaseOrder::getType, 2)
                    .eq(PurchaseOrder::getBuyerUserId, -1)
                    .eq(StringUtils.hasText(purchaseOrderNo), PurchaseOrder::getNo, purchaseOrderNo)
                    .list();
        }
        if (CollUtil.isEmpty(targetOrders)) {
            log.error("autoCreateOrderSettlementInfo targetOrders is empty.");
            return;
        }

        RSemaphore semaphore = redisson.getSemaphore("settlementSemaphore");
        int maxParallelThreads = 2;
        semaphore.trySetPermits(maxParallelThreads);
        RCountDownLatch countDownLatch = redisson.getCountDownLatch("settlementCountDownLatch");
        countDownLatch.trySetCount(targetOrders.size());
        AtomicInteger correctCount = new AtomicInteger();
        AtomicInteger failCount = new AtomicInteger();
        try {
            targetOrders.forEach(purchaseOrder -> {
                ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
                    try {
                        semaphore.acquire();
                        generateSettlement(purchaseOrder, timeBo);
                        correctCount.getAndIncrement();
                        log.info("autoCreateOrderSettlementList save success. purchaseOrderNo:{}", purchaseOrder.getNo());
                    } catch (Exception e) {
                        log.error("autoCreateOrderSettlementList save error. purchaseOrderNo:{}", purchaseOrder.getNo(), e);
                        failCount.getAndIncrement();
                    } finally {
                        semaphore.release();
                        countDownLatch.countDown();
                    }
                });
            });
            countDownLatch.await();
            log.info("autoCreateOrderSettlementList. all execution is completed. correctCount:{}. failCount:{}",
                    correctCount.get(), failCount.get());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("autoCreateOrderSettlementList save error, InterruptedException", e);
        } finally {
            semaphore.release(maxParallelThreads - semaphore.availablePermits());
        }
    }


    /**
     * 检查是否可以根据此采购单编码生成采购结算数据。
     *
     * @param purchaseOrderNo
     * @return true，允许生成。false，不允许！
     */
    private Boolean checkDataByPurchaseOrderNo(String purchaseOrderNo) {
        /*// 如果关联到了人工创建的已结算的采购单，则不允许自动重新生成结算数据。
        Integer count = iOrderSettlementFormService.lambdaQuery()
                .eq(OrderSettlementForm::getStatus, OrderSettlementStatus.CONFIRMED)
                .ne(OrderSettlementForm::getCreatedUid, 0)
                .eq(OrderSettlementForm::getPurchaseOrderNo, purchaseOrderNo)
                .count();
        if (count > 0) {
            return false;
        }*/

        // 旧数据，删除。
        LambdaQueryChainWrapper<OrderSettlementForm> oldWrapper = iOrderSettlementFormService.lambdaQuery()
                .eq(OrderSettlementForm::getPurchaseOrderNo, purchaseOrderNo)
                .and(ww -> ww.isNull(OrderSettlementForm::getNo).or().eq(OrderSettlementForm::getNo, Strings.EMPTY));
        List<OrderSettlementForm> list = oldWrapper.list();
        if (CollUtil.isNotEmpty(list)) {
            List<Long> removeIds = list.stream().map(OrderSettlementForm::getId).collect(Collectors.toList());
            iOrderSettlementFormService.removeByIdsWithTime(removeIds);

            iOrderSettlementDetailService.lambdaUpdate().in(OrderSettlementDetail::getFormId, removeIds).remove();
        }
        return true;
    }

    private void generateSettlement(PurchaseOrder purchaseOrder, TimeBO timeBo) {
//        Boolean canGenerate = checkDataByPurchaseOrderNo(purchaseOrder.getNo());
//        if (!canGenerate) {
//            return;
//        }

        List<PurchaseOrderDetail> detailList = iPurchaseOrderDetailService.lambdaQuery()
                .eq(PurchaseOrderDetail::getPurchaseOrderId, purchaseOrder.getId())
                .list();
        if (CollUtil.isEmpty(detailList)) {
            return;
        }

        String warehouseNo = detailList.get(0).getWarehouseNo();
        Set<String> skuCodes = detailList.stream().map(PurchaseOrderDetail::getItemSkuCode).collect(Collectors.toSet());

//        List<Long> itemIdList = iItemService.lambdaQuery()
//                .eq(Item::getProviderId, purchaseOrder.getProviderId()).list().stream()
//                .map(Item::getId).collect(Collectors.toList());
//        List<String> thisProviderSkuCodeList = iItemSkuService.lambdaQuery()
//                .in(ItemSku::getItemId, itemIdList).list().stream().map(ItemSku::getSkuCode).collect(Collectors.toList());
        // 退换管理，当月退货数量。
        List<SkuRefundDo> skuRefundNumCurrentMonth = wdtRefundOrderMapper.getSkuRefundNumCurrentMonth2(
                warehouseNo, timeBo.getMonthStart(), timeBo.getMonthEnd());
        skuRefundNumCurrentMonth.forEach(val -> val.setPrice(getPriceFormStockOutPrice(val.getTradeNo(), val.getSkuCode())));
        // 用商品档案的价格来填充空价格
        fillNullPrice(skuRefundNumCurrentMonth);
        // sku必须是此供应商的。因为会出现，不同供应商但是同仓库的情况，会导致结算SKU变多，变脏。
        skuRefundNumCurrentMonth = skuRefundNumCurrentMonth.stream()
                .filter(val -> {
                    boolean b1 = skuCodes.contains(val.getSkuCode());
                    boolean b2 = !externalShopWdtNo.getShopNos().contains(val.getShopNo());
                    return b1 && b2;
                }).collect(Collectors.toList());

        // 退换管理，跨月退货数量
        List<SkuRefundDo> skuRefundNumCrossMonth = wdtRefundOrderMapper.getSkuRefundNumCrossMonth2(
                warehouseNo, timeBo.getMonthStart(), timeBo.getMonthEnd());
        skuRefundNumCrossMonth.forEach(val -> val.setPrice(getPriceFormStockOutPrice(val.getTradeNo(), val.getSkuCode())));
        fillNullPrice(skuRefundNumCrossMonth);
        skuRefundNumCrossMonth = skuRefundNumCrossMonth.stream()
                .filter(val -> {
                    boolean b1 = skuCodes.contains(val.getSkuCode());
                    boolean b2 = !externalShopWdtNo.getShopNos().contains(val.getShopNo());
                    return b1 && b2;
                }).collect(Collectors.toList());

        // 当月发货数量
        List<SkuDeliverNumDo> skuDeliverNumDos = wdtOrderDetailWrapperMapper.selectSkuDeliverNum(warehouseNo,
                timeBo.getOperateMonth(), purchaseOrder.getProviderId(), WrapperType.TOTAL.getValue());

        BigDecimal temporaryQuantity = getTemporaryQuantity(purchaseOrder.getId());

        OrderSettlementForm form = buildForm(purchaseOrder, temporaryQuantity.intValue(), warehouseNo, timeBo);
        Collection<OrderSettlementDetail> details = buildDetails(detailList, skuCodes, timeBo, skuDeliverNumDos,
                skuRefundNumCurrentMonth, skuRefundNumCrossMonth);
        // 计算出一个暂估的统计信息
        String temporaryStaticDo = getTemporaryStaticDo(details);
        form.setStaticInfo(temporaryStaticDo);

        orderSettlementSysDao.systemGenerate(form, details);
    }

    private BigDecimal getPriceFormStockOutPrice(String tradeNo, String skuCode) {
        BigDecimal price = null;
        if (StrUtil.isNotBlank(tradeNo) && StrUtil.isNotBlank(skuCode)) {
            List<WdtOrderDetailWrapper> list = iWdtOrderDetailWrapperService.lambdaQuery()
                    .eq(WdtOrderDetailWrapper::getSkuCode, skuCode)
                    .eq(WdtOrderDetailWrapper::getTradeNo, tradeNo)
                    .in(WdtOrderDetailWrapper::getType, ListUtil.of(WrapperType.STOCK_OUT_SINGLE, WrapperType.STOCK_OUT_COMBINATION))
                    .orderByDesc(WdtOrderDetailWrapper::getId)
                    .select(WdtOrderDetailWrapper::getPrice).last("limit 1").list();
            if (CollUtil.isNotEmpty(list)) {
                price = list.get(0).getPrice();
            }
        }
        return price;
    }


    public String getTemporaryStaticDo(Collection<OrderSettlementDetail> details) {
        DetailStaticDo detailStaticDo = new DetailStaticDo();
        int tsTemporaryQuantity = details.stream().mapToInt(OrderSettlementDetail::getTemporaryQuantity).sum();
        detailStaticDo.setTsTemporaryQuantity(tsTemporaryQuantity);
        int tsDeliverQuantity = details.stream().mapToInt(OrderSettlementDetail::getDeliverQuantity).sum();
        detailStaticDo.setTsDeliverQuantity(tsDeliverQuantity);
        int tsCurrentMonthRefundQuantity = details.stream().mapToInt(OrderSettlementDetail::getCurrentMonthRefundQuantity).sum();
        detailStaticDo.setTsCurrentMonthRefundQuantity(tsCurrentMonthRefundQuantity);
        int tsCrossMonthRefundQuantity = details.stream().mapToInt(OrderSettlementDetail::getCrossMonthRefundQuantity).sum();
        detailStaticDo.setTsCrossMonthRefundQuantity(tsCrossMonthRefundQuantity);
        AtomicReference<BigDecimal> tsSettlementAmount = new AtomicReference<>(BigDecimal.ZERO);
        AtomicInteger tsSettlementQuantity = new AtomicInteger(0);
        details.forEach(val -> {
            tsSettlementQuantity.set(tsSettlementQuantity.get() + val.getSettlementQuantity());
            BigDecimal oneSettlementAmount = val.getSettlementPrice().multiply(new BigDecimal(val.getSettlementQuantity()));
            tsSettlementAmount.set(tsSettlementAmount.get().add(oneSettlementAmount));
        });
        detailStaticDo.setTsSettlementQuantity(tsSettlementQuantity.get());
        detailStaticDo.setTsSettlementAmount(tsSettlementAmount.get());
        detailStaticDo.setTsFinalAmount(tsSettlementAmount.get());
        detailStaticDo.setTsRemark("");
        detailStaticDo.setTransportAmount(BigDecimal.ZERO);
        detailStaticDo.setTransportFinalAmount(BigDecimal.ZERO);
        detailStaticDo.setTransportRemark("");
        detailStaticDo.setAfterSalesAmount(BigDecimal.ZERO);
        detailStaticDo.setAfterSalesFinalAmount(BigDecimal.ZERO);
        detailStaticDo.setAfterSalesRemark("");
        detailStaticDo.setOtherAmount(BigDecimal.ZERO);
        detailStaticDo.setOtherFinalAmount(BigDecimal.ZERO);
        detailStaticDo.setOtherRemark("");
        detailStaticDo.setTotalSettlementQuantity(detailStaticDo.getTsSettlementQuantity());
        detailStaticDo.setTotalSettlementAmount(detailStaticDo.getTsSettlementAmount());
        detailStaticDo.setTotalFinalAmount(detailStaticDo.getTsFinalAmount());
        detailStaticDo.setTotalRemark("");
        detailStaticDo.setTotalTemporaryQuantity(tsTemporaryQuantity);

        return JsonUtil.toJson(detailStaticDo);
    }

    private String buildKey(String skuCode, BigDecimal price) {
        return skuCode + price.toString();
    }

    private void fillNullPrice(List<SkuRefundDo> paramList) {
        List<String> nullPriceSkuCodes = paramList.stream().filter(val -> Objects.isNull(val.getPrice()))
                .map(SkuRefundDo::getSkuCode).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(nullPriceSkuCodes)) {
            List<ItemSku> list = iItemSkuService.lambdaQuery()
                    .in(ItemSku::getSkuCode, nullPriceSkuCodes).or().in(ItemSku::getProviderSpecifiedCode, nullPriceSkuCodes)
                    .list();
            Map<String, ItemSku> map = list.stream().collect(Collectors.toMap(ItemSku::getPurchaseOrderSkuCode, Function.identity()));

            paramList.forEach(val -> {
                if (Objects.isNull(val.getPrice())) {
                    ItemSku itemSku = map.get(val.getSkuCode());
                    if (Objects.nonNull(itemSku)) {
                        val.setPrice(itemSku.getCostPrice());
                    } else {
                        val.setPrice(BigDecimal.ZERO);
                    }
                }
            });
        }
    }


    private Collection<OrderSettlementDetail> buildDetails(List<PurchaseOrderDetail> details,
                                                           Set<String> skuCodes,
                                                           TimeBO timeBo, List<SkuDeliverNumDo> skuDeliverNumDos,
                                                           List<SkuRefundDo> currentMonthRefundNum,
                                                           List<SkuRefundDo> crossMonthRefundNum) {

        // test
//        log.info("buildDetails detailsSize:{},skuDeliverNumDos:{},currentMonthRefundNum:{},crossMonthRefundNum:{}",
//                details.size(), JsonUtil.toJson(skuDeliverNumDos), JsonUtil.toJson(currentMonthRefundNum), JsonUtil.toJson(crossMonthRefundNum));

        Map<String, SkuRefundDo> currentMonthRefundMap = currentMonthRefundNum.stream()
                .collect(Collectors.toMap(
                        skuRefundDo -> buildKey(skuRefundDo.getSkuCode(), skuRefundDo.getPrice()),
                        a -> a, (v1, v2) -> {
                            v1.setRefundNum(v1.getRefundNum().add(v2.getRefundNum()));
                            return v1;
                        }));

        Map<String, SkuRefundDo> crossMonthRefundMap = crossMonthRefundNum.stream()
                .collect(Collectors.toMap(
                        skuRefundDo -> buildKey(skuRefundDo.getSkuCode(), skuRefundDo.getPrice()),
                        a -> a, (v1, v2) -> {
                            v1.setRefundNum(v1.getRefundNum().add(v2.getRefundNum()));
                            return v1;
                        }));

        Map<String, Set<SkuPriceExplain>> skuPriceExplainMap = iSkuPriceExplainService.lambdaQuery()
                .in(SkuPriceExplain::getSkuCode, skuCodes)
                .eq(SkuPriceExplain::getOperateTime, timeBo.getOperateMonth()).list()
                .stream().distinct().collect(Collectors.groupingBy(SkuPriceExplain::getSkuCode, Collectors.toSet()));

        List<OrderSettlementDetail> collect = details.stream().map(val -> {
            OrderSettlementDetail detail = new OrderSettlementDetail();
            detail.setSettlementStartDate(timeBo.getMonthStartTime());
            detail.setSettlementEndDate(timeBo.getMonthStartTime());
            detail.setSkuCode(val.getItemSkuCode());
            // 暂估单价
            detail.setTemporaryPrice(val.getTaxPrice());
            // 暂估数量（可正可负） = 采购单计算数量 = 销售出库数量（正数）- 销售退货入库数量（正数）
            detail.setTemporaryQuantity(val.getPurchaseQuantity());

            detail.setDeliverQuantity(0);
            // 发货数量，销售出库单的数量
            skuDeliverNumDos.stream().filter(skuDeliverNumDo -> (
                            skuDeliverNumDo.getSkuCode().equals(val.getItemSkuCode()))
                            && (skuDeliverNumDo.getPrice().compareTo(val.getTaxPrice()) == 0))
                    .findFirst().ifPresent(p -> {
                        Integer deliverNum = p.getDeliverNum();
                        detail.setDeliverQuantity(deliverNum);
                    });

            String thisSkuRefundKey = buildKey(detail.getSkuCode(), detail.getTemporaryPrice());
            // 当月退货数量
            SkuRefundDo currentMonthRefundDo = currentMonthRefundMap.get(thisSkuRefundKey);
            if (Objects.nonNull(currentMonthRefundDo)) {
                detail.setCurrentMonthRefundQuantity(currentMonthRefundDo.getRefundNum().intValue());
            } else {
                detail.setCurrentMonthRefundQuantity(0);
            }
            currentMonthRefundMap.remove(thisSkuRefundKey);
            // 跨月退货数量
            SkuRefundDo crossMonthRefundDo = crossMonthRefundMap.get(thisSkuRefundKey);
            if (Objects.nonNull(crossMonthRefundDo)) {
                detail.setCrossMonthRefundQuantity(crossMonthRefundDo.getRefundNum().intValue());
            } else {
                detail.setCrossMonthRefundQuantity(0);
            }
            crossMonthRefundMap.remove(thisSkuRefundKey);

            // 结算数量，暂时从暂估数量带过来
            detail.setSettlementQuantity(detail.getDeliverQuantity() - detail.getCurrentMonthRefundQuantity() - detail.getCrossMonthRefundQuantity());
            // 结算单价，暂时从暂估金额带过来
            detail.setSettlementPrice(detail.getTemporaryPrice());
            // 结算总金额，
            detail.setSettlementAmount(BigDecimal.ZERO);
            // 运费售后分摊金额
            detail.setAfterSalesCost(BigDecimal.ZERO);
            // 最终金额
            detail.setFinalAmount(BigDecimal.ZERO);
            // 价格计算来源
            Set<SkuPriceExplain> skuPriceExplains = skuPriceExplainMap.get(val.getItemSkuCode());
            if (CollUtil.isEmpty(skuPriceExplains)) {
                detail.setSource("");
            } else {
                detail.setSource(skuPriceExplains.stream()
                        .filter(v -> v.getPrice().compareTo(detail.getTemporaryPrice()) == 0)
                        .sorted(Comparator.comparing(SkuPriceExplain::getId))
                        .map(SkuPriceExplain::toString)
                        .collect(Collectors.joining(",")));
            }
            return detail;
        }).collect(Collectors.toList());

//        log.info("buildDetail base finish.detailSize:{}", details.size());

        // 检查当月/跨月退货中，是否有多余的sku，有就手动补充一条数据
        if (CollUtil.isNotEmpty(currentMonthRefundMap)) {
            List<OrderSettlementDetail> currentList = buildByRefund(timeBo, currentMonthRefundMap.values(), 1, skuDeliverNumDos);
            if (CollUtil.isNotEmpty(currentList)) {
//                log.info("currentList:{}", JsonUtil.toJson(currentList));
                collect.addAll(currentList);
            }
        }
        if (CollUtil.isNotEmpty(crossMonthRefundMap)) {
            List<OrderSettlementDetail> crossList = buildByRefund(timeBo, crossMonthRefundMap.values(), 2, skuDeliverNumDos);
            if (CollUtil.isNotEmpty(crossList)) {
//                log.info("crossList:{}", JsonUtil.toJson(crossList));
                collect.addAll(crossList);
            }
        }
//        log.info("buildDetail base2 finish.detailSize:{}", collect.size());

        // 金额6位转2位。
        collect.forEach(val -> {
            val.setTemporaryPrice(val.getTemporaryPrice().setScale(2, RoundingMode.HALF_UP));
            val.setSettlementPrice(val.getTemporaryPrice());
            val.setSettlementAmount(val.getSettlementPrice().multiply(new BigDecimal(val.getSettlementQuantity())).setScale(2, RoundingMode.HALF_UP));
            val.setFinalAmount(val.getSettlementAmount());
        });

        // 以sku+暂估单价=key，进行数据合并
        final Collection<OrderSettlementDetail> values = collect.stream()
                .collect(Collectors.toMap(
                        orderSettlementDetail -> orderSettlementDetail.getSkuCode() + "_" + orderSettlementDetail.getTemporaryPrice(),
                        a -> a,
                        (o1, o2) -> {
                            o1.setTemporaryQuantity(o1.getTemporaryQuantity() + o2.getTemporaryQuantity());
                            o1.setDeliverQuantity(o1.getDeliverQuantity() + o2.getDeliverQuantity());
                            o1.setCurrentMonthRefundQuantity(o1.getCurrentMonthRefundQuantity() + o2.getCurrentMonthRefundQuantity());
                            o1.setCrossMonthRefundQuantity(o1.getCrossMonthRefundQuantity() + o2.getCrossMonthRefundQuantity());
                            o1.setSettlementQuantity(o1.getSettlementQuantity() + o2.getSettlementQuantity());
                            o1.setSettlementAmount(o1.getSettlementAmount().add(o2.getSettlementAmount()).setScale(2, RoundingMode.HALF_UP));
                            o1.setFinalAmount(o1.getFinalAmount().add(o2.getSettlementAmount()).setScale(2, RoundingMode.HALF_UP));
                            o1.setRemark(o1.getRemark() + "。" + o2.getRemark());
                            o1.setSource(getSource(o1.getSource(), o2.getSource()));
                            return o1;
                        })).values();

//        log.info("buildDetail base3 finish.detailSize:{}", values.size());

        return values;
    }

    public static void main(String[] args) {
        List<OrderSettlementDetail> collect = new LinkedList<>();

        OrderSettlementDetail d1 = new OrderSettlementDetail();
        d1.setSkuCode("100184802");
        d1.setTemporaryPrice(new BigDecimal("1869.260000"));
        d1.setSettlementPrice(new BigDecimal("1869.260000"));
        d1.setSettlementQuantity(1);

        OrderSettlementDetail d2 = new OrderSettlementDetail();
        d2.setSkuCode("100184802");
        d2.setTemporaryPrice(new BigDecimal("2090"));
        d2.setSettlementPrice(new BigDecimal("2090"));
        d2.setSettlementQuantity(2);


        OrderSettlementDetail d3 = new OrderSettlementDetail();
        d3.setSkuCode("100184802");
        d3.setTemporaryPrice(new BigDecimal("2089.998125"));
        d3.setSettlementPrice(new BigDecimal("2089.998125"));
        d3.setSettlementQuantity(2);

        collect.add(d1);
        collect.add(d2);
        collect.add(d3);

        collect.forEach(val -> {
            val.setTemporaryPrice(val.getTemporaryPrice().setScale(2, RoundingMode.HALF_UP));
            val.setSettlementPrice(val.getTemporaryPrice());
            val.setSettlementAmount(val.getSettlementPrice().multiply(new BigDecimal(val.getSettlementQuantity())).setScale(2, RoundingMode.HALF_UP));
            val.setFinalAmount(val.getSettlementAmount());
        });

        Collection<OrderSettlementDetail> values = collect.stream()
                .collect(Collectors.toMap(
                        orderSettlementDetail -> orderSettlementDetail.getSkuCode() + "_" + orderSettlementDetail.getTemporaryPrice(),
                        a -> a,
                        (o1, o2) -> {
                            o1.setSettlementQuantity(o1.getSettlementQuantity() + o2.getSettlementQuantity());
                            o1.setSettlementAmount(o1.getSettlementAmount().add(o2.getSettlementAmount()).setScale(2, RoundingMode.HALF_UP));
                            o1.setFinalAmount(o1.getFinalAmount().add(o2.getSettlementAmount()).setScale(2, RoundingMode.HALF_UP));
                            return o1;
                        })).values();
        System.out.println(JsonUtil.toJson(values));

    }

    private String getSource(String s1, String s2) {
        if (StrUtil.isBlank(s1) && StrUtil.isBlank(s2)) {
            return StrUtil.EMPTY;
        } else {
            List<String> collect = ListUtil.of(s1, s2).stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
            return StrUtil.join(StrUtil.COLON, collect);
        }
    }

    /**
     * 根据退换管理多余的数量做数据手动补全
     *
     * @param timeBo
     * @param values
     * @param refundType 1.当月退货。2.跨月退货
     * @return
     */
    public List<OrderSettlementDetail> buildByRefund(TimeBO timeBo, Collection<SkuRefundDo> values, Integer refundType,
                                                     List<SkuDeliverNumDo> skuDeliverNumDos) {
        List<OrderSettlementDetail> details = new LinkedList<>();

        for (SkuRefundDo val : values) {
            String skuCode = val.getSkuCode();

            Long skuBrandId = iItemSkuService.getSkuBrandId(skuCode);
            if (skuBrandId.equals(195L) || skuBrandId.equals(1413L)) {
                continue;
            }

            OrderSettlementDetail detail = new OrderSettlementDetail();
            detail.setSettlementStartDate(timeBo.getMonthStartTime());
            detail.setSettlementEndDate(timeBo.getMonthStartTime());
            detail.setSkuCode(skuCode);
            detail.setTemporaryPrice(val.getPrice());
            detail.setTemporaryQuantity(0);
            if (1 == refundType) {
                detail.setCurrentMonthRefundQuantity(val.getRefundNum().intValue());
                detail.setCrossMonthRefundQuantity(0);
            }
            if (2 == refundType) {
                detail.setCurrentMonthRefundQuantity(0);
                detail.setCrossMonthRefundQuantity(val.getRefundNum().intValue());
            }
            detail.setDeliverQuantity(0);
            // 发货数量
            /*skuDeliverNumDos.stream().filter(skuDeliverNumDo -> (skuDeliverNumDo.getSkuCode().equals(detail.getSkuCode()))
                    && (skuDeliverNumDo.getPrice().compareTo(detail.getTemporaryPrice()) == 0))
                    .findFirst().ifPresent(p -> detail.setDeliverQuantity(p.getDeliverNum()));*/

            detail.setSettlementQuantity(detail.getDeliverQuantity() - detail.getCurrentMonthRefundQuantity() - detail.getCrossMonthRefundQuantity());
            detail.setSettlementPrice(val.getPrice());
            detail.setSettlementAmount(BigDecimal.ZERO);
            detail.setAfterSalesCost(BigDecimal.ZERO);
            detail.setFinalAmount(BigDecimal.ZERO);
            detail.setRemark("[系统]退换管理补全");
            detail.setSource("价格来源：出库计算价格或者商品档案价格");
            details.add(detail);
        }
        return details;
    }


    /**
     * 根据采购订单ID，统计每个采购订单的暂估数量
     *
     * @param purchaseOrderId 采购订单ID
     * @return 暂估数量累计
     */
    private BigDecimal getTemporaryQuantity(Long purchaseOrderId) {
        QueryWrapper<PurchaseOrderDetail> sumWrapper = new QueryWrapper<>();
        sumWrapper.select("IFNULL(SUM(purchase_quantity),0) as total")
                .eq("purchase_order_id", purchaseOrderId)
                .eq("is_del", 0);
        Map<String, Object> map = iPurchaseOrderDetailService.getMap(sumWrapper);
        return (BigDecimal) map.get("total");
    }

    private OrderSettlementForm buildForm(PurchaseOrder order, Integer temporaryQuantity,
                                          String warehouseNo, TimeBO timeBo) {
        OrderSettlementForm f = new OrderSettlementForm();
        f.setSettlementStartDate(timeBo.getMonthStartTime());
        f.setSettlementEndDate(timeBo.getMonthStartTime());
        f.setPProviderId(order.getProviderId());
        f.setSProviderId(order.getProviderId());
        f.setWarehouseNo(warehouseNo);
        f.setPurchaseOrderNo(order.getNo());
        f.setTemporaryQuantity(temporaryQuantity);
        f.setSettlementQuantity(temporaryQuantity);
        f.setBusinessLine(order.getBusinessLine());
        return f;
    }


}
