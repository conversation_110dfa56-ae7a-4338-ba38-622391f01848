package com.daddylab.supplier.item.application.system;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.daddylab.supplier.item.types.warehouse.WarehouseVO;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2022/11/3
 */
@Mapper
public interface WarehouseTypeMapper {

    WarehouseTypeMapper INST = Mappers.getMapper(WarehouseTypeMapper.class);

    @Mapping(target = "orderPersoneList", ignore = true)
//    @Mapping(target = "afterSalesAddressInfoList", ignore = true)
    @Mapping(target = "businessLine",ignore = true)
    WarehouseVO warehousePoToVo(Warehouse po);
}
