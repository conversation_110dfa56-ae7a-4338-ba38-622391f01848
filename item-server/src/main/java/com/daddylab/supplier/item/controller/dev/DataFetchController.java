package com.daddylab.supplier.item.controller.dev;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsQueryWithSpecParams;
import com.daddylab.supplier.item.application.item.ItemFetcher;
import com.daddylab.supplier.item.application.item.WdtGoodsFetcher;
import com.daddylab.supplier.item.application.order.WdtOrderDataSyncService;
import com.daddylab.supplier.item.common.domain.dto.GenericIdsBody;
import com.daddylab.supplier.item.domain.dataFetch.DataFetchManager;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import io.swagger.annotations.Api;
import java.util.HashSet;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2021/12/31
 */

@Api(hidden = true)
@Slf4j
@RestController
@RequestMapping("/dev/dataFetch")
public class DataFetchController {

    @Autowired
    private DataFetchManager dataFetchManager;
    @Autowired
    private WdtOrderDataSyncService wdtOrderDataSyncService;

    @PostMapping(value = "/fetch/pause")
    public void pause() {
        dataFetchManager.pause();
    }

    @PostMapping(value = "/fetch/resume")
    public void resume() {
        dataFetchManager.resume();
    }

    @PostMapping(value = "/fetch/reFetch")
    public void reFetch(Integer dataType, String startTime, String endTime) {
        final FetchDataType fetchDataType = IEnum.getEnumByValue(FetchDataType.class, dataType);
        dataFetchManager.reFetch(fetchDataType, DateUtil.parse(startTime), DateUtil.parse(endTime));
    }

    @PostMapping(value = "/fetchOrderBySrcTids")
    public SingleResponse<Boolean> fetchOrderBySrcTids(@RequestBody GenericIdsBody<String> params) {
        if (CollUtil.isEmpty(params.getIds())) {
            //noinspection unchecked
            return SingleResponse.buildFailure("1", "单号为空");
        }
        wdtOrderDataSyncService.saveOrUpdateBatchSpecifiedOrdersBySrcTids(params.getIds());
        return SingleResponse.of(true);
    }

    @PostMapping(value = "/cleanOrder")
    public SingleResponse<Integer> cleanOrder(@RequestBody GenericIdsBody<Long> params) {
        final HashSet<Long> tradeIds = Optional.ofNullable(params).map(GenericIdsBody::getIds)
                .map(HashSet::new).orElse(null);
        final int cleanOrderCount = wdtOrderDataSyncService.cleanOrders(tradeIds);
        return SingleResponse.of(cleanOrderCount);
    }

    @PostMapping(value = "/fetchWdtGoodsByQuery")
    public SingleResponse<Integer> fetchItemByGoodsNo(@RequestBody GoodsQueryWithSpecParams params) {
        final int count = ApplicationContextUtil.getBean(WdtGoodsFetcher.class)
                .fetchByQuery(params);
        return SingleResponse.of(count);
    }

    @PostMapping(value = "/fetchItemByGoodsNo")
    public SingleResponse<Integer> fetchItemByGoodsNo(@RequestBody GenericIdsBody<String> params) {
        if (CollUtil.isEmpty(params.getIds())) {
            //noinspection unchecked
            return SingleResponse.buildFailure("1", "单号为空");
        }
        final int count = ApplicationContextUtil.getBean(ItemFetcher.class)
                .fetchByGoodsNos(params.getIds());
        return SingleResponse.of(count);
    }
}
