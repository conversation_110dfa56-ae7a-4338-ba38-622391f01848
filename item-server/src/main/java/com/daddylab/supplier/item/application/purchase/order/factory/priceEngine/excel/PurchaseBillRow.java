package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2023年02月27日 3:49 PM
 */
@Data
public class PurchaseBillRow {

    @ExcelProperty("店铺")
    private String shop;

    @ExcelProperty("仓库")
    private String warehouse;

    @ExcelProperty("原始单号")
    private String originalNo;

    @ExcelProperty("订单编号")
    private String orderNo;

    @ExcelProperty("货品名称")
    private String goodName;

    @ExcelProperty("商家编码")
    private String erpCode;

    @ExcelProperty("货品编号")
    private String goodCode;

    @ExcelProperty("规格名称")
    private String specification;

    @ExcelProperty("货品数量")
    private String erpQuantity;

    @ExcelProperty("erp计算价格")
    private String erpPrice;

    @ExcelProperty("物流公司")
    private String logisticsCompany;

    @ExcelProperty("物流编号")
    private String logisticsNo;

    @ExcelProperty("货品成交价")
    private String goodSalePrice;

    @ExcelProperty("下单时间")
    private String payTime;

    @ExcelProperty("来源组合装编码")
    private String suitNo;

    @ExcelProperty("erp组合装日常价")
    private String erpSuitPrice;

    @ExcelProperty("erp组合装活动价")
    private String erpSuitActivityPrice;

    @ExcelProperty("erp组合装采购活动")
    private String erpSuitActivityDesc;

    @ExcelProperty("来源组合装名称")
    private String suitName;

    @ExcelProperty("出库状态")
    private String stockOutStatus;

    @ExcelProperty("出库单编号")
    private String stockOutOrderNo;

    @ExcelProperty("货品原总金额")
    private String goodTotalAmount;

    @ExcelProperty("货品成交总价")
    private String goodTotalSalePrice;

    @ExcelProperty("订单类别")
    private String orderType;

    @ExcelProperty("发货时间")
    private String deliveryTime;

    @ExcelProperty("赠品方式")
    private String giftType;

    @ExcelProperty("收货地区")
    @ColumnWidth(50)
    private String receiveAddress;

    @ExcelProperty("买家留言")
    @ColumnWidth(100)
    private String customerMsg;

    @ExcelProperty("客服备注")
    @ColumnWidth(100)
    private String customerServiceMsg;

    @ExcelProperty("打印备注")
    @ColumnWidth(100)
    private String printMsg;

}
