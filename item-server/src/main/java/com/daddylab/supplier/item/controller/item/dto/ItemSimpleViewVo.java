package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/26 9:00 下午
 * @description
 */
@Data
@ApiModel("商品详情简单返回封装")
public class ItemSimpleViewVo {

    @ApiModelProperty("商品名称")
    private String name;

    @ApiModelProperty("商品编码")
    private String code;

    @ApiModelProperty("品牌名称")
    private String brandName;

    @ApiModelProperty("品类名称")
    private String categoryName;

    @ApiModelProperty("图片url")
    private String imageUrl;

    @ApiModelProperty("商品id")
    private String id;
}
