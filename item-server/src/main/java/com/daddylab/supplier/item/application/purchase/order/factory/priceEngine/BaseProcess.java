package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.CommonCalculator;
import com.daddylab.supplier.item.application.purchase.order.factory.CommonUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.QuantityCombinedPriceBO;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.QuantityCombinedPriceVO;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.WrapperType;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.StatisticsQuantityByTradeNoDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailWrapperMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseRandomSkuCombinationPriceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseSingleSkuCombinationPriceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ISkuPriceExplainService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderDetailWrapperService;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hutool.core.text.CharSequenceUtil.hasBlank;
import static cn.hutool.core.text.CharSequenceUtil.isBlank;

/**
 * <AUTHOR> up
 * @date 2022年10月24日 2:55 PM
 */
@Component
@Slf4j
public class BaseProcess {

    @Resource
    IPurchaseSingleSkuCombinationPriceService purchaseSingleSkuCombinationPriceService;

    @Resource
    IWdtOrderDetailWrapperService iWdtOrderDetailWrapperService;

    @Resource
    IPurchaseRandomSkuCombinationPriceService purchaseRandomSkuCombinationPriceService;

    @Resource
    WdtOrderDetailMapper wdtOrderDetailMapper;

    @Resource
    ISkuPriceExplainService iSkuPriceExplainService;

    @Resource
    CommonUtil commonUtil;

    @Resource
    WdtOrderDetailWrapperMapper wdtOrderDetailWrapperMapper;

    public final static List<Integer> FREE_GIFT_TYPE = ListUtil.of(1, 2);

    List<QuantityCombinedPriceBO> getPriceBoList(String priceInfo) {
        if (hasBlank(priceInfo)) {
            return new LinkedList<>();
        }
        return JsonUtil.parse(priceInfo, new TypeReference<List<QuantityCombinedPriceBO>>() {
        });
    }

    List<String> getSubCodeList(String codeStr) {
        if (isBlank(codeStr)) {
            return new LinkedList<>();
        }
        return Arrays.asList(codeStr.split("\\|"));
    }

    private Long getActivityEarliestTime(TimeBO timeBO) {
        Long monthStartTime = timeBO.getMonthStartTime();
        Long earliestPayTime = wdtOrderDetailWrapperMapper.getEarliestPayTime(timeBO.getOperateMonth());

        if (earliestPayTime <= monthStartTime) {
            return earliestPayTime;
        }
        return monthStartTime;
    }

    private Long getActivityLatestTime(TimeBO timeBO) {
        Long monthEndTime = timeBO.getMonthEndTime();
        Long latestPayTime = wdtOrderDetailWrapperMapper.getLatestPayTime(timeBO.getOperateMonth());

        if (latestPayTime >= monthEndTime) {
            return latestPayTime;
        } else {
            return monthEndTime;
        }
    }

    // ----------------------------------------------------------------------------------------------------------------------

    /**
     * 查询单sku维度的组合价信息
     *
     * @param timeBO
     * @param priceType 1:日常价。2:活动价
     * @return
     */
    public List<PurchaseSingleSkuCombinationPrice> getSingleCombinationPriceByType(TimeBO timeBO, Integer priceType) {
        Long activityEarliestTime = getActivityEarliestTime(timeBO);
        Long activityLatestTime = getActivityLatestTime(timeBO);

        return purchaseSingleSkuCombinationPriceService.lambdaQuery()
                .eq(PurchaseSingleSkuCombinationPrice::getPriceType, priceType)
                .eq(PurchaseSingleSkuCombinationPrice::getSource, 1)
                .ge(priceType == 2, PurchaseSingleSkuCombinationPrice::getStartTime, activityEarliestTime)
                .le(priceType == 2, PurchaseSingleSkuCombinationPrice::getEndTime, activityLatestTime)
                .select().list();
    }


    /**
     * 单sku维度，任意数量组合价格处理。
     *
     * @param priceEntity 单sku维度的价格信息
     * @param operateTime 操作时间
     * @param priceType   1：日常价，2：活动价
     */
    public void singleCombinationPriceHandler(PurchaseSingleSkuCombinationPrice priceEntity, String operateTime, Integer priceType) {
        // 必须解析出价格信息，否则直接不处理。
        String priceInfo = priceEntity.getPriceInfo();
        if (isBlank(priceInfo)) {
            return;
        }
        List<QuantityCombinedPriceBO> priceBoList = getPriceBoList(priceInfo);

        Integer count = iWdtOrderDetailWrapperService.lambdaQuery()
                .eq(WdtOrderDetailWrapper::getOperateTime, operateTime)
                .eq(WdtOrderDetailWrapper::getSkuCode, priceEntity.getCode())
                .eq(WdtOrderDetailWrapper::getType, WrapperType.STOCK_OUT_SINGLE.getValue())
                .eq(priceEntity.getPlatform() != 0, WdtOrderDetailWrapper::getPlatformType, priceEntity.getPlatform())
                .ne(WdtOrderDetailWrapper::getTradeNo, "")
                .count();
        if (count <= 0) {
            return;
        }

        // 因为是多件组合价格，wdt_order_detail_wrapper 中的数据已经是单条拆分完毕的数据。
        // 根据交易订单号聚合统计wrapper的数量。数量，wrapperIds
        List<StatisticsQuantityByTradeNoDO> statisticsCountByTradeNoList;
        if (priceType == 1) {
            Set<BigDecimal> priceContainer = new HashSet<>();
            String skuCode = priceEntity.getCode();
            statisticsCountByTradeNoList = wdtOrderDetailMapper
                    .statisticsCountByTradeNo(skuCode, null, null, null, operateTime);

            singleUpdatePriceHandler(statisticsCountByTradeNoList, priceBoList, priceContainer);
            saveSkuPriceLog(skuCode, priceContainer, operateTime, "sku日常组合价计算");

        }
        if (priceType == 2) {
            Set<BigDecimal> priceContainer = new HashSet<>();
            statisticsCountByTradeNoList = wdtOrderDetailMapper
                    .statisticsCountByTradeNo(priceEntity.getCode(), null, priceEntity.getStartTime(), priceEntity.getEndTime(), operateTime);

            singleUpdatePriceHandler(statisticsCountByTradeNoList, priceBoList, priceContainer);
            saveSkuPriceLog(priceEntity.getCode(), priceContainer, operateTime, "sku活动组合价计算");
        }
    }


    private void singleUpdatePriceHandler(List<StatisticsQuantityByTradeNoDO> statisticsCountByTradeNoList,
                                          List<QuantityCombinedPriceBO> priceBoList, Set<BigDecimal> priceContainer) {
        // 数量-单价，首先根据组合价，初始化映射缓存
        Map<Integer, BigDecimal> priceCacheMap = new HashMap<>(8);
        priceBoList.forEach(val -> priceCacheMap.put(val.getQuantity(),
                val.getPrice().divide(new BigDecimal(val.getQuantity()), 6, RoundingMode.HALF_UP)));
        statisticsCountByTradeNoList.forEach(val -> {
            if (isBlank(val.getIds())) {
                return;
            }
            // 此笔订单交易下，sku累计数量
            int thisNum = val.getSumNum();
            // 是否存在此累计数量对应的组合价规格单价
            BigDecimal thisNumSpecPrice = priceCacheMap.get(thisNum);

            // 如果订单下的数量不是组合价的价格信息中对应的上的数量，拆分计算
            if (Objects.isNull(thisNumSpecPrice)) {
                // wrapperIdList
                /*Queue<Long> idQueue = new LinkedList<>();
                for (String s : val.getIds().split(",")) {
                    idQueue.add(Long.valueOf(s));
                }*/

                // 计算结果。
                QuantityCombinedPriceVO quantityCombinedPriceVO = CommonCalculator.stagedPrice0(priceBoList, thisNum);
                // 计算均价，最少合计价格/订单数量
                BigDecimal unitPrice = quantityCombinedPriceVO.getGreedMaxAmount()
                        .divide(new BigDecimal(quantityCombinedPriceVO.getReqQuantity()), 6, RoundingMode.HALF_UP);
                List<Long> wrapperIdList = Stream.of(val.getIds().split(StrUtil.COMMA)).map((Long::valueOf)).collect(Collectors.toList());
                iWdtOrderDetailWrapperService.lambdaUpdate()
                        .set(WdtOrderDetailWrapper::getPrice, unitPrice)
                        .in(WdtOrderDetailWrapper::getId, wrapperIdList)
                        .update();
                priceContainer.add(unitPrice);

                /*List<QuantityCombinedPriceVO.SpecVO> specList = quantityCombinedPriceVO.getSpecList();
                // 遍历计算结果，结果会返回所需要的各个组合价的数量，计算每一个组合价的单价，再根据总数量和组合数量，做更新。
                specList.forEach(priceResult -> {
                    BigDecimal unitPrice = priceResult.getPriceBo().getPrice()
                            .divide(new BigDecimal(priceResult.getPriceBo().getQuantity()), 6, RoundingMode.HALF_UP);
                    int updateCount = priceResult.getCaseQuantity() * priceResult.getPriceBo().getQuantity();
                    // 从ids中取出指定数量更新价格。
                    List<Long> updatePriceIdList = new LinkedList<>();
                    for (int i = 0; i < updateCount; i++) {
                        updatePriceIdList.add(idQueue.poll());
                    }
                    // 将目标id的价格进行更新
                    boolean update = iWdtOrderDetailWrapperService.lambdaUpdate()
                            .set(WdtOrderDetailWrapper::getPrice, unitPrice)
                            .in(WdtOrderDetailWrapper::getId, updatePriceIdList)
                            .update();
                    priceContainer.add(unitPrice);
                });*/
            } else {
                List<Long> idList = ListUtil.of(val.getIds().split(",")).stream().map(Long::valueOf).collect(Collectors.toList());
                boolean update = iWdtOrderDetailWrapperService.lambdaUpdate()
                        .set(WdtOrderDetailWrapper::getPrice, thisNumSpecPrice)
                        .in(WdtOrderDetailWrapper::getId, idList)
                        .update();
                priceContainer.add(thisNumSpecPrice);
            }
        });
    }


    // ----------------------------------------------------------------------------------------------------------------------


    /**
     * 单spu维度，下属任意sku数量组合，组合价格。
     *
     * @param timeBO
     * @param priceType 1日常价。2活动价
     * @return
     */
    public List<PurchaseRandomSkuCombinationPrice> getRandomCombinationPriceByType(TimeBO timeBO, Integer priceType) {
        Long activityEarliestTime = getActivityEarliestTime(timeBO);
        Long activityLatestTime = getActivityLatestTime(timeBO);

        return purchaseRandomSkuCombinationPriceService.lambdaQuery()
                .eq(PurchaseRandomSkuCombinationPrice::getPriceType, priceType)
                .ge(priceType == 2, PurchaseRandomSkuCombinationPrice::getStartTime, activityEarliestTime)
                .le(priceType == 2, PurchaseRandomSkuCombinationPrice::getEndTime, activityLatestTime)
                .select().list();
    }

    /**
     * 单spu维度，多sku维度，夸sku维度任意数量组合价格处理
     *
     * @param priceEntity 任意sku数量构成的组合价
     * @param operateTime 操作时间
     * @param priceType   1：日常价，2：活动价
     */
    public void randomCombinationPriceHandler(PurchaseRandomSkuCombinationPrice priceEntity, String operateTime, Integer priceType) {
        List<String> skuCodeList = getSubCodeList(priceEntity.getCode());
        List<QuantityCombinedPriceBO> priceBoList = getPriceBoList(priceEntity.getPriceInfo());
        if (CollUtil.isEmpty(skuCodeList) || CollUtil.isEmpty(priceBoList)) {
            return;
        }
        Integer count = iWdtOrderDetailWrapperService.lambdaQuery()
                .eq(WdtOrderDetailWrapper::getOperateTime, operateTime)
                .eq(WdtOrderDetailWrapper::getType, 1)
                .in(WdtOrderDetailWrapper::getSkuCode, skuCodeList)
                .eq(priceEntity.getPlatform() != 0, WdtOrderDetailWrapper::getPlatformType, priceEntity.getPlatform())
                .ne(WdtOrderDetailWrapper::getTradeNo, "")
                .count();
        if (count <= 0) {
            return;
        }

        List<StatisticsQuantityByTradeNoDO> statisticsCountByTradeNoList;
        if (priceType == 1) {
            Set<BigDecimal> priceContainer = new HashSet<>();
            statisticsCountByTradeNoList = wdtOrderDetailMapper
                    .statisticsCountByTradeNo(null, skuCodeList, null, null, operateTime);
            combinationUpdatePriceHandler(statisticsCountByTradeNoList, priceBoList, priceContainer);
            saveSkuPriceLog(skuCodeList, priceContainer, operateTime, "spu日常组合价计算");
        }
        if (priceType == 2) {
            statisticsCountByTradeNoList = wdtOrderDetailMapper
                    .statisticsCountByTradeNo(null, skuCodeList, priceEntity.getStartTime(), priceEntity.getEndTime(), operateTime);
            Set<BigDecimal> priceContainer = new HashSet<>();
            saveSkuPriceLog(skuCodeList, priceContainer, operateTime, "spu活动组合价计算");
            combinationUpdatePriceHandler(statisticsCountByTradeNoList, priceBoList, priceContainer);
        }

    }

    public void combinationUpdatePriceHandler(List<StatisticsQuantityByTradeNoDO> statisticsCountByTradeNoList,
                                              List<QuantityCombinedPriceBO> priceBoList, Set<BigDecimal> priceContainer) {
        for (StatisticsQuantityByTradeNoDO tradeNoDO : statisticsCountByTradeNoList) {
            String ids = tradeNoDO.getIds();
            if (StrUtil.isBlank(ids)) {
                continue;
            }

            List<Long> idList = Arrays.stream(tradeNoDO.getIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
            Map<Integer, BigDecimal> priceCacheMap = new HashMap<>(8);
            priceBoList.forEach(val -> priceCacheMap.put(val.getQuantity(),
                    val.getPrice().divide(new BigDecimal(val.getQuantity()), 6, RoundingMode.HALF_UP)));
            int thisNum = tradeNoDO.getSumNum();
            BigDecimal thisNumSpecPrice = priceCacheMap.get(thisNum);

            if (Objects.isNull(thisNumSpecPrice)) {
                QuantityCombinedPriceVO quantityCombinedPriceVO = CommonCalculator.stagedPrice0(priceBoList, thisNum);
                // 计算均价，最少合计价格/订单数量
                BigDecimal unitPrice = quantityCombinedPriceVO.getGreedMaxAmount()
                        .divide(new BigDecimal(quantityCombinedPriceVO.getReqQuantity()), 6, RoundingMode.HALF_UP);
                iWdtOrderDetailWrapperService.lambdaUpdate()
                        .set(WdtOrderDetailWrapper::getPrice, unitPrice)
                        .in(WdtOrderDetailWrapper::getId, idList)
                        .update();
                priceContainer.add(unitPrice);
            } else {
                iWdtOrderDetailWrapperService.lambdaUpdate()
                        .set(WdtOrderDetailWrapper::getPrice, thisNumSpecPrice)
                        .in(WdtOrderDetailWrapper::getId, idList)
                        .update();
                priceContainer.add(thisNumSpecPrice);
            }
        }
    }


    // ---------------------------------- 价格日志部分 --------------------------------------------------------

    public void saveSkuPriceLog(String skuCode, BigDecimal price, String operateTime, String remark) {
        SkuPriceExplain explain = new SkuPriceExplain();
        explain.setSkuCode(skuCode);
        explain.setPrice(price);
        explain.setOperateTime(operateTime);
        explain.setRemark(remark);
        Optional<SkuPriceExplain> optional = getExistOne(skuCode, price, operateTime, remark);
        if (!optional.isPresent()) {
            iSkuPriceExplainService.save(explain);
        }
    }

    private Optional<SkuPriceExplain> getExistOne(String skuCode, BigDecimal price, String operateTime, String remark) {
        return iSkuPriceExplainService.lambdaQuery()
                .eq(SkuPriceExplain::getSkuCode, skuCode)
                .eq(SkuPriceExplain::getPrice, price)
                .eq(SkuPriceExplain::getOperateTime, operateTime)
                .eq(SkuPriceExplain::getRemark, remark).oneOpt();
    }

    public void saveSkuPriceLog(String skuCode, Collection<BigDecimal> prices, String operateTime, String remark) {
        if (CollUtil.isNotEmpty(prices)) {
            List<SkuPriceExplain> collect = prices.stream().map(price -> {
                SkuPriceExplain explain = new SkuPriceExplain();
                explain.setSkuCode(skuCode);
                explain.setPrice(price);
                explain.setOperateTime(operateTime);
                explain.setRemark(remark);
                Optional<SkuPriceExplain> existOne = getExistOne(skuCode, price, operateTime, remark);
                if (!existOne.isPresent()) {
                    return explain;
                } else {
                    return null;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());

            iSkuPriceExplainService.saveBatch(collect);
        }
    }

    public void saveSkuPriceLog(Collection<String> skuCodes, Collection<BigDecimal> prices, String operateTime, String remark) {
        List<SkuPriceExplain> collect = skuCodes.stream().map(skuCode -> {
            if (CollUtil.isNotEmpty(prices)) {
                return prices.stream().map(price -> {
                    SkuPriceExplain explain = new SkuPriceExplain();
                    explain.setSkuCode(skuCode);
                    explain.setPrice(price);
                    explain.setOperateTime(operateTime);
                    explain.setRemark(remark);
                    Optional<SkuPriceExplain> existOne = getExistOne(skuCode, price, operateTime, remark);
                    if (!existOne.isPresent()) {
                        return explain;
                    } else {
                        return null;
                    }
                }).filter(Objects::nonNull).collect(Collectors.toList());
            } else {
                return new ArrayList<SkuPriceExplain>();
            }
        }).flatMap(List::stream).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(collect)) {
            iSkuPriceExplainService.saveBatch(collect);
        }
    }

    // ------------------------------------ 价格日志部分 end ------------------------------------------------------

    public static void main(String[] args) {

    }

}
