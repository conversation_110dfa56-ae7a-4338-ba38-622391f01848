package com.daddylab.supplier.item.application.refundOrder;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderBaseInfoForP;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderDetailForP;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderQueryForP;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2022/4/17
 */
public interface RefundOrderServiceForP {

    PageResponse<RefundOrderBaseInfoForP> refundOrderQuery(RefundOrderQueryForP query, Long partnerProviderId);

    SingleResponse<RefundOrderDetailForP> refundOrderDetail(String refundOrderNo, Long partnerProviderId);

    @Transactional(rollbackFor = Throwable.class)
    SingleResponse<Long> refundOrderExport(RefundOrderQueryForP query, Long partnerProviderId);
}
