package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtProviderGoods implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 供应商编号
     */
    private String providerNo;

    /**
     * 商家编码
     */
    private String specNo;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 最后采购时间（毫秒级时间戳格式）
     */
    private String lastPurchaseTime;

    /**
     * 产地
     */
    private String origin;

    /**
     * 货品简称
     */
    private String shortName;

    /**
     * 货品编号
     */
    private String goodsNo;

    /**
     * 规格码
     */
    private String specCode;

    /**
     * 分类名称
     */
    private String className;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * true：主供应商 false：不是主供应商
     */
    private Boolean isMaster;

    /**
     * 基本单位
     */
    private String baseUnitName;

    /**
     * 供应商货号
     */
    private String providerGoodsNo;

    /**
     * 总库存
     */
    private BigDecimal stockNum;

    /**
     * 待采购量
     */
    private BigDecimal toPurchaseNum;

    /**
     * 预订单量
     */
    private BigDecimal subscribeNum;

    /**
     * 待审核量
     */
    private BigDecimal orderNum;

    /**
     * 待发货量
     */
    private BigDecimal sendingNum;

    /**
     * 可发库存
     */
    private BigDecimal avaliableNum;

    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * 采购在途量
     */
    private BigDecimal purchaseNum;

    /**
     * 采购到货量
     */
    private BigDecimal purchaseArriveNum;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 采购价
     */
    private BigDecimal price;

    /**
     * 零售价
     */
    private BigDecimal retailPrice;

    /**
     * 最后采购价
     */
    private BigDecimal lastPrice;

    /**
     * 上一次采购价
     */
    private BigDecimal lastSecondPrice;

    /**
     * 最低采购价
     */
    private BigDecimal lowestPrice;

    /**
     * 今日销量
     */
    private BigDecimal todayNum;

    /**
     * 七天销量
     */
    @TableField("num_7days")
    private BigDecimal num7days;

    /**
     * 14天销量
     */
    @TableField("num_14days")
    private BigDecimal num14days;

    /**
     * 月销量
     */
    private BigDecimal numMonth;

    /**
     * 月销量
     */
    private BigDecimal numAll;

    /**
     * 采购单位
     */
    private String unitName;

    /**
     * 辅助单位和单位的换算系数
     */
    private BigDecimal unitRatio;

    /**
     * 最小采购量
     */
    private BigDecimal minPurchaseNum;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * true：停用 false：未停用
     */
    private Boolean isDisabled;

    /**
     * 单品自定义属性1
     */
    private String prop1;

    /**
     * 单品自定义属性2
     */
    private String prop2;

    /**
     * 单品自定义属性3
     */
    private String prop3;

    /**
     * 单品自定义属性4
     */
    private String prop4;

    /**
     * 单品自定义属性5
     */
    private String prop5;

    /**
     * 单品自定义属性6
     */
    private String prop6;

    /**
     * 供应商货品的备注
     */
    private String remark;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;


}
