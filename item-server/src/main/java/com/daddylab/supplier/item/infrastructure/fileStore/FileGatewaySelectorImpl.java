package com.daddylab.supplier.item.infrastructure.fileStore;

import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.ImageStub;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.fileStore.vo.VideoStub;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.File;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/10/12
 */
@Component
@Primary
public class FileGatewaySelectorImpl implements FileGateway {
    @Autowired
    @Qualifier(StorageService.OSS)
    FileGateway fileGatewayOssImpl;

    @Autowired
    @Qualifier(StorageService.UPYUN)
    FileGateway fileGatewayUnyunImpl;

    @Autowired UploadConfig uploadConfig;

    @Override
    public ImageStub uploadImage(UploadFileAction action) {
        return Objects.equals(uploadConfig.getImageStoreSvc(), StorageService.OSS)
                ? fileGatewayOssImpl.uploadImage(action)
                : fileGatewayUnyunImpl.uploadImage(action);
    }

    @Override
    public FileStub uploadFile(UploadFileAction action) {
        return Objects.equals(uploadConfig.getFileStoreSvc(), StorageService.OSS)
                ? fileGatewayOssImpl.uploadFile(action)
                : fileGatewayUnyunImpl.uploadFile(action);
    }

    @Override
    public VideoStub uploadVideo(UploadFileAction action) {
        return Objects.equals(uploadConfig.getVideoStoreSvc(), StorageService.OSS)
                ? fileGatewayOssImpl.uploadVideo(action)
                : fileGatewayUnyunImpl.uploadVideo(action);
    }

    @Override
    public Map<String, File> fileQueryBatchByUrls(Collection<String> fileUrls) {
        return fileGatewayOssImpl.fileQueryBatchByUrls(fileUrls);
    }

    @Override
    public Map<Long, File> fileQueryBatchByIds(Collection<Long> fileIds) {
        return fileGatewayOssImpl.fileQueryBatchByIds(fileIds);
    }

    @Override
    public String getAuthorizedUrl(String url) {
        return fileGatewayOssImpl.getAuthorizedUrl(url);
    }
}
