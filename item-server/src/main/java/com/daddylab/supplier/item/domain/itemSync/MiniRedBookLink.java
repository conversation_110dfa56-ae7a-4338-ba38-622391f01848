package com.daddylab.supplier.item.domain.itemSync;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.exception.BizException;
import com.daddylab.supplier.item.common.enums.ErrorCode;

/**
 * <AUTHOR> up
 * @date 2023年11月17日 3:22 PM
 */
public class MiniRedBookLink {

    /**
     * https://www.xiaohongshu.com/goods-detail/647f39d8896a160001f86b62?xhsshare=WeixinSession&appuid=598e914450c4b42eaebe1400&apptime=1700205021
     */

    public static String getGoodsId(String url) {
        if (StrUtil.isNotEmpty(url)) {
            try {
                url = url.replaceAll("https://www.xiaohongshu.com/goods-detail/", "");
                int i = url.indexOf("?");
                return url = url.substring(0, i);
            } catch (Exception e) {
                throw new BizException(ErrorCode.VERIFY_PARAM.getCode(),
                        "解析小红书url异常:" + e.getMessage());
            }
        }
        return StrUtil.EMPTY;
    }

    public static void main(String[] args) {
        System.out.println(MiniRedBookLink.getGoodsId("https://www.baidu.com/m2"));


    }


}
