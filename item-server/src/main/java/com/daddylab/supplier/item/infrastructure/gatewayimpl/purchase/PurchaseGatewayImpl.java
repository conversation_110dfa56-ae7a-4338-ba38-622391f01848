package com.daddylab.supplier.item.infrastructure.gatewayimpl.purchase;

import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.purchase.gateway.PurchaseGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Purchase;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseService;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @ClassName PurchaseGatewayImpl.java
 * @description
 * @createTime 2021年11月11日 14:35:00
 */
@Service
public class PurchaseGatewayImpl implements PurchaseGateway {

    @Autowired
    IPurchaseService iPurchaseService;

    @Autowired
    PurchaseMapper purchaseMapper;

    @Override
    public void saveDo(Purchase purchase) {
        String random = RandomStringUtils.random(8, true, true);
        purchase.setMd5(random);
        iPurchaseService.save(purchase);
    }

    @Override
    public void removeById(Long purchaseId) {
        Purchase purchase = iPurchaseService.getById(purchaseId);
        if (Objects.isNull(purchase)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查不到该记录,无法删除");
        }
        iPurchaseService.removeById(purchaseId);
    }

    @Override
    public void update(Purchase purchase) {
        iPurchaseService.updateById(purchase);
    }

}
