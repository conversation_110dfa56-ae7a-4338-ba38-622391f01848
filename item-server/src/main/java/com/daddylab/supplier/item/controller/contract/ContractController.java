package com.daddylab.supplier.item.controller.contract;

import com.alibaba.cola.dto.MultiResponse;
import com.daddylab.supplier.item.application.contract.ContractService;
import com.daddylab.supplier.item.types.contract.ContractDropdownItem;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/4/1
 */
@Api(value = "合同api", tags = "合同api")
@Slf4j
@RestController
@RequestMapping("/contract")
public class ContractController {
    @Autowired
    ContractService contractService;

    @GetMapping("/dropdownList")
    @ApiOperation("合同下拉选择")
    public MultiResponse<ContractDropdownItem> excelList(@ApiParam("合同编码") @RequestParam String contractNo,
                                                         @ApiParam("此供应商P系统ID") @RequestParam(required = false) Long partnerProviderId,
                                                         @ApiParam("3：合作中") @RequestParam(required = false) Integer status) {
        return contractService.contractDropdownList(contractNo, partnerProviderId, status);
    }
}
