package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.utils;

import org.apache.commons.lang3.RandomStringUtils;

import java.util.Objects;

public interface LogicDeleteUtil {
    /**
     * 添加了唯一索引的字段在逻辑删除后需要添加这个后缀以保证不会影响到新增
     * 为了避免后缀太长导致原字段长度不够存放，所以这边只添加下划线加两个随机字符
     * 这样大概有三千分之一的可能性会导致冲突，这个概率已经足够低
     */
    static String appendLogicDeleteSuffix(String value) {
        if (Objects.isNull(value)) return null;
        return value + "_" + RandomStringUtils.random(2, true, true);
    }
}
