package com.daddylab.supplier.item.controller.saleItem;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.application.saleItem.dto.NewGoodsCmd;
import com.daddylab.supplier.item.application.saleItem.dto.SkuCopyCmd;
import com.daddylab.supplier.item.application.saleItem.query.NewGoodsQueryPage;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsLiveVerbalTrickExportService;
import com.daddylab.supplier.item.application.saleItem.vo.LockResult;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsDownRequest;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsGroupVO;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsToBeReleasedRequest;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsVo;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.domain.dto.IdCmd;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerPersonCmd;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.user.dto.StaffDropDownItem;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerPersonResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffListQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName NewGoodsController.java
 * @description
 * @createTime 2022年04月18日 16:33:00
 */
@Slf4j
@Api(value = "新品商品API", tags = "新品商品API")
@RequestMapping("/newGoods")
@Controller
public class NewGoodsController {

    @Autowired
    private NewGoodsBizService newGoodsBizService;

    @Resource
    private NewGoodsLiveVerbalTrickExportService newGoodsLiveVerbalTrickExportService;

    @Autowired
    @Qualifier("UserGatewayCacheImpl")
    private UserGateway userGateway;

    @Autowired
    private ItemGateway itemGateway;

    @Autowired
    private OperateLogBizService operateLogBizService;

    @ResponseBody
    @ApiOperation(value = "新品商品分页（SPU维度分割）")
    @PostMapping("/viewListGroupBySpu")
    public PageResponse<NewGoodsGroupVO> viewListGroupBySpu(@RequestBody NewGoodsQueryPage newGoodsQueryPage) {
        setQueryPermParams(newGoodsQueryPage);
        return newGoodsBizService.queryPageGroupBySpu(newGoodsQueryPage);
    }

    @ResponseBody
    @ApiOperation(value = "新品商品sku复制")
    @PostMapping("/skuCopy")
    public SingleResponse<Boolean> skuCopy(@RequestBody SkuCopyCmd cmd) {
        log.info("新品商品sku复制 cmd:{}", cmd);
        return newGoodsBizService.copySku(cmd);
    }

//    @ResponseBody
//    @ApiOperation(value = "编辑保存某sku信息")
//    @PostMapping("/saveOneSku")
//    public SingleResponse<Boolean> saveOneSku(@RequestBody SaveOneSkuCmd cmd) {
//        log.info("编辑保存某sku信息 cmd:{}", cmd);
//        return newGoodsBizService.saveOneSku(cmd);
//    }

    @ResponseBody
    @ApiOperation(value = "锁定新品商品记录（单次锁定30秒，用户保持锁定状态时，10秒调用一次）")
    @PostMapping("/lock")
    public SingleResponse<LockResult> lock(@RequestBody IdCmd idCmd) {
        return newGoodsBizService.lock(idCmd.getId());
    }

    @ResponseBody
    @ApiOperation(value = "解锁新品商品记录（解锁，用户取消编辑时调用）")
    @PostMapping("/unlock")
    public Response unlock(@RequestBody IdCmd idCmd) {
        return newGoodsBizService.unlock(idCmd.getId());
    }

    @ResponseBody
    @ApiOperation(value = "修改新品商品")
    @PostMapping("/createOrUpdate")
    public SingleResponse<NewGoodsVo> update(@RequestBody @Validated NewGoodsCmd cmd) {
        log.info("新品商品库商品修改 cmd:{}", cmd);
        final Response response = newGoodsBizService.update(cmd);
        if (!response.isSuccess()) {
            //noinspection unchecked
            return SingleResponse.buildFailure(response.getErrCode(), response.getErrMessage());
        }
        return SingleResponse.of(newGoodsBizService.getNewGoodsVoById(cmd.getId()));
    }

    @ResponseBody
    @ApiOperation(value = "删除新品商品")
    @PostMapping("/delete")
    public Response delete(@RequestBody IdCmd idCmd) {
        log.info("新品商品库商品 删除 id:{}", idCmd.getId());
        return newGoodsBizService.delete(idCmd.getId());
    }

    @ResponseBody
    @PostMapping(value = "/export")
    @ApiOperation("导出")
    public void export(@RequestBody NewGoodsQueryPage newGoodsQueryPage) {
        setQueryPermParams(newGoodsQueryPage);
        newGoodsBizService.exportExcel(newGoodsQueryPage);
    }

    private void setQueryPermParams(
            @RequestBody NewGoodsQueryPage newGoodsQueryPage) {
        final Long userId = UserContext.getUserId();
        boolean showAll = UserContext.hasPermission(GlobalConstant.NEW_GOODS_SHOW_ALL);
        newGoodsQueryPage.setShowAll(showAll);
        newGoodsQueryPage.setUserId(userId);
        newGoodsQueryPage.setHasPricePerm(UserContext.hasPermission(GlobalConstant.VIEW_ITEM_PRICE_RIGHT_URI));

//        newGoodsQueryPage.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(newGoodsQueryPage.getBusinessLine()));
    }

    @ResponseBody
    @PostMapping(value = "/exportImages")
    @ApiOperation("导出图片")
    public void exportImages(@RequestBody NewGoodsQueryPage newGoodsQueryPage) {
        //TODO:导出图片可能需要限制下商品数量，因为需要下载图片可能会很慢
        setQueryPermParams(newGoodsQueryPage);
        newGoodsBizService.exportImages(newGoodsQueryPage);
    }

    @ResponseBody
    @PostMapping(value = "/exportLiveVerbalTrick")
    @ApiOperation("导出直播话术")
    public void exportLiveVerbalTrick(@RequestBody NewGoodsQueryPage newGoodsQueryPage) {
        setQueryPermParams(newGoodsQueryPage);
        newGoodsLiveVerbalTrickExportService.export(newGoodsQueryPage);
    }

    @ResponseBody
    @ApiOperation(value = "运营人员列表")
    @PostMapping("/itemOperator")
    public MultiResponse<StaffInfo> getItemOperator(@RequestBody StaffListQuery staffListQuery) {
        staffListQuery.setStatus(1);
        staffListQuery.setPageIndex(staffListQuery.getPageIndex());
        staffListQuery.setPageSize(staffListQuery.getPageSize());
        staffListQuery.setDept("消费者事业部");
        List<StaffInfo> staffInfos = userGateway.queryStaffList(staffListQuery);
        return MultiResponse.of(staffInfos);
    }

    @ResponseBody
    @ApiOperation(value = "QC负责人列表")
    @GetMapping("/QCList")
    public MultiResponse<StaffDropDownItem> detailBySkuCode(
            @ApiParam("type") @RequestParam Integer type,
            @ApiParam("name") @RequestParam(defaultValue = "") String name,
            @ApiParam("userId") @RequestParam(defaultValue = "") Long userId) {
        PartnerPersonCmd partnerPersonCmd = new PartnerPersonCmd();
        partnerPersonCmd.setRole_type(type);
        final List<PartnerPersonResp> data = itemGateway.partnerPersonQuery(partnerPersonCmd);
        final List<Long> userIds = data.stream().map(PartnerPersonResp::getId).map(Long::valueOf)
                .distinct()
                .collect(Collectors.toList());
        if(Objects.nonNull(userId)){
            userIds.add(userId);
        }
        final StaffListQuery staffListQuery = new StaffListQuery();
        staffListQuery.setUserIds(userIds);
        staffListQuery.setNickname(name);
        staffListQuery.setPageSize(49);
        final List<StaffInfo> staffInfos = userGateway.queryStaffList(staffListQuery);
        return MultiResponse.of(staffInfos.stream()
                .map(staffInfo -> new StaffDropDownItem(staffInfo.getUserId(),
                        staffInfo.getUserName(), staffInfo.getNickname()))
                .collect(Collectors.toList()));
    }

    @ResponseBody
    @ApiOperation(value = "操作记录")
    @GetMapping(value = "/getLog")
    public Response getOperateLogs(Long targetId) {
        return MultiResponse.of(newGoodsBizService.getOperateLogs(targetId));
    }

    @ResponseBody
    @ApiOperation(value = "价格记录")
    @GetMapping(value = "/getPriceLog")
    public Response getPriceLogs(Long targetId) {
        return MultiResponse.of(newGoodsBizService.getPriceLog(targetId));
    }


    @ResponseBody
    @ApiOperation(value = "下架商品")
    @PostMapping(value = "/down")
    public Response down(@RequestBody NewGoodsDownRequest request) {
        return newGoodsBizService.down(request);
    }


}
