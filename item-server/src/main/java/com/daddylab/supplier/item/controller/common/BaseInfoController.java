package com.daddylab.supplier.item.controller.common;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.bank.BankNumberBizService;
import com.daddylab.supplier.item.application.departments.DepartmentService;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.application.system.BaseInfoService;
import com.daddylab.supplier.item.application.warehouse.WarehouseBizService;
import com.daddylab.supplier.item.controller.common.dto.*;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.user.dto.StaffDropDownItem;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.types.DropdownItem;
import com.daddylab.supplier.item.types.bank.BankNumberQuery;
import com.daddylab.supplier.item.types.bank.BankSimpleInfo;
import com.daddylab.supplier.item.types.baseinfo.ResponsiblePersonQuery;
import com.daddylab.supplier.item.types.departments.DepartmentDropdownQuery;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizePlanDropDown;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizePlanQuery;
import com.daddylab.supplier.item.types.warehouse.EditWarehouseCmd;
import com.daddylab.supplier.item.types.warehouse.WarehouseVO;
import com.daddylab.supplier.item.types.warehouse.WarehouseViewVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> up
 * @date 2022/3/24 6:23 下午
 */
@Slf4j
@RestController
@RequestMapping("/baseInfo")
@Api(value = "基础信息相关API", tags = "基础信息相关API")
@RequiredArgsConstructor
public class BaseInfoController {

  private final BaseInfoService baseInfoService;

  private final WarehouseBizService warehouseBizService;

  private final DepartmentService departmentService;

  private final OperateLogBizService operateLogBizService;

  @ApiOperation(value = "基础单位分页列表")
  @ResponseBody
  @PostMapping("/unit")
  public MultiResponse<BaseUnit> unit(@RequestBody UnitPageQuery query) {
    return baseInfoService.pageUnit(query);
  }

  @ApiOperation(value = "仓库分页列表")
  @ResponseBody
  @PostMapping("/warehouse")
  public PageResponse<WarehouseVO> warehouse(@RequestBody WarehousePageQuery query) {
    return baseInfoService.pageWarehouseVo(query);
  }

  @ApiOperation(value = "仓库详情")
  @ResponseBody
  @GetMapping("/viewWarehouse")
  public SingleResponse<WarehouseViewVO> viewWarehouse(Long warehouseId) {
    return baseInfoService.viewWarehouse(warehouseId, "");
  }

  @ApiOperation(value = "导出仓库")
  @ResponseBody
  @PostMapping("/exportWarehouse")
  public SingleResponse<Boolean> exportWarehouse(@RequestBody WarehousePageQuery query) {
    return baseInfoService.exportWarehouse(query);
  }

  @ApiOperation(value = "编辑仓库")
  @ResponseBody
  @PostMapping("/warehouse/edit")
  public Response editWarehouse(@RequestBody @Valid EditWarehouseCmd cmd) {
    warehouseBizService.editWarehouse(cmd);
    return Response.buildSuccess();
  }

  @ApiOperation(value = "修改订单员")
  @ResponseBody
  @PostMapping("/warehouse/updateOrderPersonnel")
  public Response updateOrderPersonnel(@RequestBody @Valid OrderPersonnelCmd cmd) {
    return warehouseBizService.batchUpdateOrderPerson(cmd);
  }

  @GetMapping(value = "/operateLogs")
  @ApiOperation("仓库操作日志")
  public Response operateLogs(Long targetId) {
    return operateLogBizService.getOperateLogs(OperateLogTarget.WAREHOUSE, targetId);
  }

  @ApiOperation(value = "组织分页列表")
  @ResponseBody
  @PostMapping("/organization")
  public PageResponse<Organization> organization(@RequestBody OrganizationPageQuery query) {
    query.setType(2);
    return baseInfoService.pageOrganization(query);
  }

  @ResponseBody
  @ApiOperation(value = "部门列表")
  @GetMapping("/departments")
  PageResponse<DropdownItem<Long>> departmentDropdownList(
      @Validated DepartmentDropdownQuery query) {
    return departmentService.departmentDropdownList(query);
  }

  @ApiOperation(value = "采购组分页列表")
  @ResponseBody
  @PostMapping("/group")
  public PageResponse<ErpGroup> pageGroup(@RequestBody GroupPageQuery query) {
    return baseInfoService.pageGroup(query);
  }

  @ApiOperation(value = "采购税率")
  @ResponseBody
  @GetMapping("/rate")
  public MultiResponse<TaxRate> rate() {
    return baseInfoService.listTaxRate();
  }

  @ApiOperation(value = "原因列表")
  @ResponseBody
  @GetMapping("/reason")
  public MultiResponse<Reason> reason(
      @ApiParam(name = "原因类型 1:其他入库原因 2:其他出库原因", allowableValues = "1, 2")
          @RequestParam("reasonType")
          Integer reasonType) {
    return baseInfoService.reasonList(reasonType);
  }

  /**
   * 用户查看明细开关设置
   *
   * @param type
   * @return com.alibaba.cola.dto.Response
   * <AUTHOR>
   * @date 2022/3/29 11:48
   */
  @GetMapping(value = "/getUserShowDetail")
  @ApiOperation("用户查看明细开关设置")
  @ApiImplicitParam(name = "type", dataType = "int", value = "1:采购单。2:入库单。3:出库单")
  public SingleResponse<Boolean> getUserShowDetail(@RequestParam Integer type) {
    return SingleResponse.of(baseInfoService.isOpenDetailModel(type));
  }

  @ApiOperation(value = "附件列表查询")
  @ResponseBody
  @PostMapping("/pageQueryAdditional")
  public PageResponse<Additional> pageQueryAdditional(@RequestBody AdditionalPageQuery query) {
    return baseInfoService.pageQueryAdditional(query);
  }

  @ApiOperation(value = "附件列表上传")
  @ResponseBody
  @PostMapping("/uploadAdditional")
  public SingleResponse<Long> uploadAdditional(@RequestBody AdditionalCmd cmd) {
    return baseInfoService.uploadAdditional(cmd);
  }

  @ApiOperation(value = "附件列表信息获取")
  @ResponseBody
  @GetMapping("/uploadAdditional")
  public SingleResponse<Additional> uploadAdditional(@RequestParam("id") Long id) {
    return baseInfoService.downloadAdditional(id);
  }

  @ResponseBody
  @GetMapping("/deleteAdditional")
  @Auth(noAuth = true)
  public void deleteAdditional(@RequestParam("id") Long id) {
    baseInfoService.deleteAdditional(id);
  }

  @ResponseBody
  @GetMapping(value = {"/getBusinessLines", "/scopes"})
  @Auth(noAuth = true)
  public List<Integer> getBusinessLines() {
    return UserPermissionJudge.getDivisionLevelPermissions().stream()
        .map(DivisionLevelValueEnum::getValue)
        .collect(Collectors.toList());
  }

  @ResponseBody
  @PostMapping(value = "/itemOptimizePlan")
  @ApiOperation("商品优化计划基础信息查询")
  public PageResponse<ItemOptimizePlanDropDown> itemOptimizePlan(
      @RequestBody ItemOptimizePlanQuery query) {
    return baseInfoService.itemOptimizePlan(query);
  }

  @ResponseBody
  @ApiOperation(value = "负责人列表")
  @PostMapping("/responsiblePersonQuery")
  public MultiResponse<StaffDropDownItem> psysResponsiblePersonQuery(
      @Validated @RequestBody ResponsiblePersonQuery query) {
    return baseInfoService.responsiblePersonQuery(query);
  }

  @ResponseBody
  @ApiOperation(value = "银行信息查询")
  @PostMapping("/banks")
  public PageResponse<BankSimpleInfo> banks(@Validated @RequestBody BankNumberQuery query) {
    return ApplicationContextUtil.getBean(BankNumberBizService.class).querySimpleInfo(query);
  }

  @ResponseBody
  @ApiOperation(value = "查询公司发票抬头信息")
  @GetMapping("/invoiceTitle")
  public MultiResponse<String> invoiceTitle(String param) {
    return baseInfoService.invoiceTitle(param);
  }

  @ApiOperation(value = "外部账号分页查询")
  @ResponseBody
  @PostMapping("/externalUserPage")
  public PageResponse<ExternalUserVo> externalUserPage(@RequestBody ExternalUserPageQuery query) {
    return baseInfoService.externalUserPage(query);
  }

  @ApiOperation(value = "删除外部账号")
  @ResponseBody
  @GetMapping("/delExternalUser")
  public SingleResponse<Boolean> delExternalUser(Long id) {
    return baseInfoService.delExternalUser(id);
  }

  @ApiOperation(value = "编辑外部账号")
  @ResponseBody
  @Auth(noAuth = true)
  @PostMapping("/editExternalUser")
  public SingleResponse<Boolean> editExternalUser(@RequestBody ExternalUserCmd cmd) {
    return baseInfoService.updateExternalUser(cmd);
  }

  @ResponseBody
  @ApiOperation(value = "getRedisVal")
  @GetMapping("/getRedisVal")
  @Auth(noAuth = true)
  public SingleResponse<Object> getRedisVal(String key) {
    return baseInfoService.getValByKey(key);
  }

  @ResponseBody
  @ApiOperation(value = "获取仓库售后信息导入模板")
  @GetMapping("/warehouseImportTemplate")
  public SingleResponse<String> warehouseImportTemplate() {
    return warehouseBizService.warehouseInfoImportExcel();
  }

  @ResponseBody
  @ApiOperation(value = "导入仓库售后信息")
  @PostMapping("/importWarehouse")
  public SingleResponse<Boolean> importWarehouse(@RequestParam MultipartFile file) {
    return warehouseBizService.importWarehouse(file);
  }
}
