package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/9/19 10:50
 * @Description: 商品匹配小程序 feign 请求参数
 */
@Data
public class ItemSyncMiniProgramMatchParam implements Serializable {
    private static final long serialVersionUID = 1L;

    private String itemNo;
    private String name;
    private List<String> skuNos;
    private Long shopId;
    private List<Long> shopList;
    /**
     * ERP 系统的 categoryId（内部使用，实际上请求接口的时候不需要）
     */
    private Long erpCategoryId;
    /**
     * ERP 后端商品ID
     */
    private Long erpItemId;
}
