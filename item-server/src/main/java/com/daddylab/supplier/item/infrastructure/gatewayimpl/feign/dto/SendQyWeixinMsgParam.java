package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.nio.charset.StandardCharsets;

/**
 * @Author: <PERSON>
 * @Date: 2022/6/1 15:38
 * @Description: 发送企微消息参数
 */
@Data
public class SendQyWeixinMsgParam implements Serializable {
    private static final long serialVersionUID = 1L;

    private static final Integer TITLE_MAX = 128;

    /**
     * 多个接受者用“|“分隔
     */
    private String touser;

    public void setTouser(String touser) {
        if (touser.contains(",")) {
            touser = touser.replaceAll(",", "|");
        }
        this.touser = touser;
    }

    private String toparty;
    private String totag;
    private Integer agentid;

    /**
     * "text" or "textcard"
     */
    private String msgtype;

    private Text text;

    @JsonProperty("textcard")
    private TextCard textCard;

    @Data
    public static class Text {
        private String content;
    }

    @Data
    public static class TextCard {
        private String title;
        /**
         * html格式的描述。
         */
        private String description;
        private String url;

        private TextCard() {
        }

        public static TextCard of(String title, String description, String url) {
            if(StringUtils.hasText(url)) {
                Assert.state(url.getBytes(StandardCharsets.UTF_8).length <= 2048, "url长度不得超过2048字节");
            }

            TextCard textCard = new TextCard();

            byte[] bytes = title.getBytes();
            if (bytes.length >= TITLE_MAX) {
                title = new String(bytes, 0, 125) + "...";
            }

            textCard.setTitle(title);
            textCard.setDescription(description);
            textCard.setUrl(url);
            return textCard;
        }
    }



}
