package com.daddylab.supplier.item.application.saleItem.query;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName NewGoodsQueryPage.java
 * @description
 * @createTime 2022年04月18日 16:34:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("新品商品分页查询请求实体")
public class NewGoodsQueryPage extends PageQuery {
    private static final long serialVersionUID = -6886589598045629177L;

    @ApiModelProperty("新品商品ID批量查询")
    private List<Long> newGoodsIds;

    @ApiModelProperty("商品ID批量查询")
    private List<Long> itemIds;

    @ApiModelProperty("商品名称")
    private String name;

    @ApiModelProperty("SKU编码")
    private String skuCode;

    @ApiModelProperty("商品编码")
    private String itemCode;

    @ApiModelProperty("产品标准名")
    private String standardName;

    @ApiModelProperty(value = "品牌id")
    private Long brandId;

    @ApiModelProperty(value = "采购负责人id")
    private Long buyerId;

    @ApiModelProperty(value = "产品负责人id")
    private Long principalId;

    @ApiModelProperty(value = "QC负责人ids")
    private Long qcId;

    @ApiModelProperty(value = "品类id")
    private Long categoryId;

    @ApiModelProperty("预计上架开始时间")
    private Long startTime;

    @ApiModelProperty("预计上架结束时间")
    private Long endTime;

    @ApiModelProperty(value = "颜色/规格")
    private String specs;

    @ApiModelProperty(value = "发货类型 1:工厂发货 2:仓库发货")
    private Integer shipmentType;

    @ApiModelProperty(value = "状态 1待选择 2待完善 3待设计 4待审核 41待法务审核 42待QC审核 5待修改 6待上架 7已上架 8已下架")
    private Integer status;

    @ApiModelProperty(name = "直播话术状态", value = "0:未进入审核流程/未知 5:待法务审核 10:待QC审核 100:审核完成")
    private Integer liveVerbalTrickStatus;

    @ApiModelProperty(value = "物流")
    private String logistics;

    @ApiModelProperty(value = "是否参与满减 0:是 1:否")
    private Integer isReduce;

    @ApiModelProperty(value = "是否叠加券")
    private Integer isCoupon;

    @ApiModelProperty(value = "48小时发货 0:是 1:否")
    private Integer shipmentAging;

    @ApiModelProperty(value = "是否支持7天无理由退换 0:是 1:否")
    private Integer noReason;

    private Boolean showAll = false;

    private Long userId = 0L;

    private String platformSkuCode;

    @ApiModelProperty(value = "前端忽略，偏移量", hidden = true)
    private int offsetVal;

    @ApiModelProperty("商品类型 1 内部 2 外包")
    private Integer itemType;

    @ApiModelProperty("淘宝ID")
    private String tbId;

    @ApiModelProperty("抖音ID")
    private String douId;

    @ApiModelProperty("小程序ID")
    private String wechatId;

    @ApiModelProperty("小红书id")
    private String miniRedBookId;

    @ApiModelProperty("P系统技术类目 1 日化 2 食品 3 纺织鞋类 4 玩具 5 电器 6轻工百货")
    private Integer partnerSysType;

    @ApiModelProperty("合作模式（业务线）多选")
    private List<Integer> businessLine = new ArrayList<>();

    @ApiModelProperty(value = "是否拥有商品价格权限", hidden = true)
    private boolean hasPricePerm;

    @ApiModelProperty("培训资料状态 0 待提交 1 已提交 2 待法务审核 3 待QC审核 4 待修改 5 已完成")
    private Integer materialsStatus;

    @ApiModelProperty(hidden = true)
    private List<Long> itemIdsNoStatusFilter;

    @ApiModelProperty(value = "商品标签", notes = "DAD_SAMPLING_INSPECTION 老爸抽检, CUSTOMIZED_PRODUCTS 定制品")
    private String tag;

    @ApiModelProperty(name = "P系统款号")
    private String partnerProviderItemSn;

    @ApiModelProperty(value = "合并上新", notes = "0:全部 1:筛选合并上新 2:非合并上新")
    private Integer merge;

    @ApiModelProperty(value = "响应数据是否处理合并逻辑", hidden = true)
    private boolean mergeLogic = true;

    @ApiModelProperty("合作方")
    private List<Integer> corpType;

    @ApiModelProperty("业务类型")
    private List<Integer> bizType;

    @ApiModelProperty("下架开始时间")
    private Long downFrameTimeStart;

    @ApiModelProperty("下架结束时间")
    private Long downFrameTimeEnd;

}
