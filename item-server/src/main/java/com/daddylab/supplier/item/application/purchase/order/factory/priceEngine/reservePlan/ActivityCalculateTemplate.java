package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.reservePlan;

import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;

/**
 * 采购活动价格计算计算模板
 *
 * <AUTHOR> up
 * @date 2023年09月27日 4:47 PM
 */
public abstract class ActivityCalculateTemplate {

    /**
     * 考虑时间优惠
     */
    protected abstract void considerTime(TimeBO timeBO);

    /**
     * 考虑数量优惠
     */
    protected abstract void considerQuantity(TimeBO timeBO);



}
