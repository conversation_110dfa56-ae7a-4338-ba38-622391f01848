package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 采购订单关联的系统订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PurchaseOrderAssociateOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    @TableLogic
    private Integer isDel;

    /**
     * 订单编号（旺店通）
     */
    private String orderNo;

    /**
     * 店铺
     */
    private String shopId;

    /**
     * 订单状态
     */
    private Integer state;

    /**
     * 下单时间
     */
    private Long placeOrderTime;

    /**
     * 付款时间
     */
    private Long payTime;

    /**
     * 发货时间
     */
    private Long shippingTime;

    /**
     * 仓库id
     */
    private Long stockId;

    /**
     * 平台
     */
    private String platform;

    /**
     * 商品时间
     */
    private String itemName;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 商品skuCode
     */
    private String itemSkuCode;

    /**
     * 商品数量
     */
    private Integer itemQuantity;

    /**
     * 采购状态。1已下单，2当月退货，3跨越退货
     */
    private Integer purchaseState;


}
