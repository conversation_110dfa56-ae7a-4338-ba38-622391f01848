package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/3/22
 */
@AllArgsConstructor
@Getter
public enum StockSyncStatus implements IIntegerEnum {
    FAIL(0, "失败"),
    SUCCESS(1, "成功"),
    ;
    private final Integer value;
    private final String desc;
}
