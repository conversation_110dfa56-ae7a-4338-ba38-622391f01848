package com.daddylab.supplier.item.infrastructure.batchcall;

import java.lang.annotation.*;

/**
 * 通过增加缓冲区批量执行的方式来增加某个IO方法的吞吐量，
 * 请注意这会增加响应时间，请仔细考虑使用场景。
 * TODO:可能存在并发问题，仍需优化
 *
 * <AUTHOR>
 * @since 2021/12/7
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface BatchCall {
    /**
     * 批量方法所在类
     */
    Class<?> batchClass();

    /**
     * qualifier
     */
    String qualifier() default "";

    /**
     * 批量方法名称
     */
    String batchMethod();

    /**
     * buffer size
     */
    int bufferSize() default 10;

    /**
     * 超过这个时间即使 <b>buffer size</b> 未满也会直接触发批量调用
     */
    int bufferTime() default 10;

    /**
     * 期望等待时长小于多少（毫秒）<b>超过这个时间还未获取到结果就会直接调用被代理的方法</b>
     */
    int expectWaitLessThan() default 100;
}
