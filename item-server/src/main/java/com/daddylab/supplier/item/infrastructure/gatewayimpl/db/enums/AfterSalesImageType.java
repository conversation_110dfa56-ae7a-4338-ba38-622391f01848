package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> up
 * @date 2022年10月18日 11:17 AM
 */
@Getter
@AllArgsConstructor
public enum AfterSalesImageType implements IEnum<Integer> {

    /**
     * 不要下划线
     * 1:异常件登记。2:转寄信息 3:登记收货
     */
    ABNORMAL(1, "异常件登记"),
    SEND_ON(2, "转寄信息"),
    RECEIVE(3, "登记收货");

    @EnumValue
    private final Integer value;
    private final String desc;

}
