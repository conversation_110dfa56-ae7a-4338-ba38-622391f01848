package com.daddylab.supplier.item.application.order.settlement.sys;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.javers.core.metamodel.annotation.Id;
import org.javers.core.metamodel.annotation.PropertyName;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年08月11日 2:50 PM
 */
@Data
public class DetailStaticDo {

    @ApiModelProperty("小计-暂估结算数量")
    @PropertyName("小计-暂估结算数量")
    private Integer tsTemporaryQuantity;
    @PropertyName("小计-订单发货数量")
    @ApiModelProperty("小计-订单发货数量")
    private Integer tsDeliverQuantity;
    @PropertyName("小计-当月退货")
    @ApiModelProperty("小计-当月退货")
    private Integer tsCurrentMonthRefundQuantity;
    @PropertyName("小计-跨月退货")
    @ApiModelProperty("小计-跨月退货")
    private Integer tsCrossMonthRefundQuantity;
    @PropertyName("小计-结算数量")
    @ApiModelProperty("小计-结算数量")
    private Integer tsSettlementQuantity;
    @PropertyName("小计-结算金额")
    @ApiModelProperty("小计-结算金额")
    private BigDecimal tsSettlementAmount;
    @PropertyName("小计-运费售后以及分摊金额")
    @ApiModelProperty("小计-运费售后以及分摊金额")
    private BigDecimal tsTransportAndAfterSalesCost;
    @ApiModelProperty("小计-运费售后以及分摊金额")
    @PropertyName("小计-运费售后以及分摊金额")
    private BigDecimal tsFinalAmount;
    @ApiModelProperty("小计-备注")
    @PropertyName("小计-备注")
    private String tsRemark;


    @ApiModelProperty("运费-金额")
    @PropertyName("运费-金额")
    private BigDecimal transportAmount;
    @ApiModelProperty("运费-最终金额")
    @PropertyName("运费-最终金额")
    private BigDecimal transportFinalAmount;
    @ApiModelProperty("运费-备注")
    @PropertyName("运费-备注")
    private String transportRemark;

    @ApiModelProperty("售后-金额")
    @PropertyName("售后-金额")
    private BigDecimal afterSalesAmount;
    @ApiModelProperty("售后-最终金额")
    @PropertyName("售后-最终金额")
    private BigDecimal afterSalesFinalAmount;
    @ApiModelProperty("售后-备注")
    @PropertyName("售后-备注")
    private String afterSalesRemark;

    @ApiModelProperty("其他-金额")
    @PropertyName("其他-金额")
    private BigDecimal otherAmount;
    @ApiModelProperty("其他-最终金额")
    @PropertyName("其他-最终金额")
    private BigDecimal otherFinalAmount;
    @ApiModelProperty("其他-备注")
    @PropertyName("其他-备注")
    private String otherRemark;

    @ApiModelProperty("合计-结算数量")
    @PropertyName("合计-结算数量")
    private Integer totalSettlementQuantity;
    @ApiModelProperty("合计-金额")
    @PropertyName("合计-金额")
    private BigDecimal totalSettlementAmount;
    @ApiModelProperty("合计-最终金额")
    @PropertyName("合计-最终金额")
    private BigDecimal totalFinalAmount;
    @ApiModelProperty("合计-备注")
    @PropertyName("合计-备注")
    private String totalRemark;
    @ApiModelProperty("合计-暂估结算数量")
    @PropertyName("合计-暂估结算数量")
    private Integer totalTemporaryQuantity;

    @Id
    private Long diffId;

    public static DetailStaticDo emptyOne() {
        DetailStaticDo one = new DetailStaticDo();
        one.setTsTemporaryQuantity(0);
        one.setTsDeliverQuantity(0);
        one.setTsCurrentMonthRefundQuantity(0);
        one.setTsCrossMonthRefundQuantity(0);
        one.setTsSettlementQuantity(0);
        one.setTsSettlementAmount(BigDecimal.ZERO);
        one.setTsTransportAndAfterSalesCost(BigDecimal.ZERO);
        one.setTsFinalAmount(BigDecimal.ZERO);
        one.setTsRemark(StrUtil.EMPTY);
        one.setTransportAmount(BigDecimal.ZERO);
        one.setTransportFinalAmount(BigDecimal.ZERO);
        one.setTransportRemark(StrUtil.EMPTY);
        one.setAfterSalesAmount(BigDecimal.ZERO);
        one.setAfterSalesFinalAmount(BigDecimal.ZERO);
        one.setAfterSalesRemark(StrUtil.EMPTY);
        one.setOtherAmount(BigDecimal.ZERO);
        one.setOtherFinalAmount(BigDecimal.ZERO);
        one.setOtherRemark(StrUtil.EMPTY);
        one.setTotalSettlementQuantity(0);
        one.setTotalSettlementAmount(BigDecimal.ZERO);
        one.setTotalFinalAmount(BigDecimal.ZERO);
        one.setTotalRemark(StrUtil.EMPTY);
        one.setTotalTemporaryQuantity(0);
        return one;
    }
}
