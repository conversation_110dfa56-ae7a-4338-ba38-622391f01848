package com.daddylab.supplier.item.infrastructure.third.kuaishou.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @class KsItemCreate.java
 * @description 描述类的作用
 * @date 2024-03-01 17:53
 */
@NoArgsConstructor
@Data
public class KsSkuChangeDTO {

    /**
     * 店铺ID
     */
    private Long sellerId;
    /**
     * 商品ID
     */
    private Long itemId;
    /**
     * 快手skuId
     */
    private Long skuId;
    /**
     * 更新时间 （毫秒）
     */
    private Long updateTime;
}
