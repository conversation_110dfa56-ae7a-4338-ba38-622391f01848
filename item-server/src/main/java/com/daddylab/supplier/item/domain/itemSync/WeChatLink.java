package com.daddylab.supplier.item.domain.itemSync;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.cola.exception.BizException;
import com.daddylab.supplier.item.common.enums.ErrorCode;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> up
 * @date 2023年05月15日 10:57 AM
 */
public class WeChatLink {

    private final static Pattern pattern = Pattern.compile("id=(\\d+)");


    public static String getId(String subUrlString) {
        subUrlString = subUrlString.trim();
        subUrlString = URLUtil.decode(subUrlString);
        if (StrUtil.isBlank(subUrlString)) {
            return "";
        }
        try {
            Matcher matcher = pattern.matcher(subUrlString);
            if (matcher.find()) {
                return matcher.group(1);
            } else {
                throw new BizException(ErrorCode.VERIFY_PARAM.getCode(),
                        "小程序URL格式错误。" + subUrlString);
            }
        } catch (Exception e) {
            throw new BizException(ErrorCode.VERIFY_PARAM.getCode(),
                    "小程序URL格式错误。" + subUrlString);
        }
    }

    public static void main(String[] args) {
        System.out.println(getId(""));
    }
}
