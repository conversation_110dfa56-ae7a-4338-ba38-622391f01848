package com.daddylab.supplier.item.application.item.itemBatchProc.handlers;

import com.alibaba.cola.dto.PageResponse;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.item.ItemSyncWdtBizService;
import com.daddylab.supplier.item.application.item.itemBatchProc.ItemBatchProcHandler;
import com.daddylab.supplier.item.controller.item.dto.ItemPageQuery;
import com.daddylab.supplier.item.controller.item.dto.ItemPageVo;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemBatchProc;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IProviderService;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.itemBatchProc.ItemBatchProcType;
import com.daddylab.supplier.item.types.itemBatchProc.ModifyProviderCmd;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.slf4j.event.Level;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/12/25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ModifyProviderHandlerImpl implements ItemBatchProcHandler {
    private final ItemBizService itemBizService;
    private final IItemService itemService;
    private final IProviderService providerService;
    private final ItemSyncWdtBizService itemSyncWdtBizService;
    private final KingDeeTemplate kingDeeTemplate;
    private final OperateLogDomainService operateLogDomainService;
    final IItemSkuService iItemSkuService;

    @Override
    public void handle(ItemBatchProc batchProc, Synchronize synchronize) {
        final ModifyProviderCmd cmd = JsonUtil.parse(batchProc.getCmd(), ModifyProviderCmd.class);
        Objects.requireNonNull(cmd, "命令解析异常");

        final Long newProviderId = cmd.getProviderId();
        final Provider provider = providerService.getById(newProviderId);
        Objects.requireNonNull(provider, "供应商查询异常");

        int totalPage = -1;
        int pageIndex = 1;
        final ItemPageQuery query = cmd.getQuery();
        query.setPageSize(100);
        while (true) {
            query.setPageIndex(pageIndex);
            final PageResponse<ItemPageVo> itemPageVoPageResponse = itemBizService.queryPage(query);
            if (totalPage == -1) {
                totalPage = itemPageVoPageResponse.getTotalPages();
            }
            final List<ItemPageVo> data = itemPageVoPageResponse.getData();
            if (data.isEmpty()) {
                break;
            }
            for (ItemPageVo itemPageVo : data) {
                final Long itemId = itemPageVo.getId();
                final Long oldProviderId = itemPageVo.getProviderId();
                if (!Objects.equals(oldProviderId, newProviderId)) {
                    final Provider oldProvider = providerService.getById(oldProviderId);
                    itemService.updateProvider(itemId, newProviderId);
                    iItemSkuService.updateNewProvider(itemId, newProviderId);
                    operateLogDomainService.addOperatorLog(
                            UserContext.getUserId(),
                            OperateLogTarget.ITEM,
                            itemId,
                            String.format(
                                    "【商品批量处理】供应商从 %s 修改为 %s",
                                    oldProvider.getName(), provider.getName()),
                            new Object[]{
                                    new DiffUtil.ChangePropertyObj(
                                            "providerId", oldProviderId, newProviderId)
                            });
                    synchronize.log(Level.INFO, "商品 %s 供应商修改完成", itemId);

                    try {
                        kingDeeTemplate.syncItem(itemId);
                        synchronize.log(Level.INFO, "商品 %s 已同步至金蝶", itemId);
                    } catch (Exception e) {
                        synchronize.log(Level.WARN, "商品 %s 推送金蝶失败:{}", e.getMessage());
                        log.error("【商品批量处理】商品 {} 推送金蝶失败", itemId, e);
                    }

                    try {
                        itemSyncWdtBizService.syncItemToWdt(itemId);
                        synchronize.log(Level.INFO, "商品 %s 已同步至旺店通", itemId);

                    } catch (Exception e) {
                        synchronize.log(Level.WARN, "商品 %s 推送旺店通失败:{}", e.getMessage());
                        log.error("【商品批量处理】商品 {} 推送旺店通失败", itemId, e);
                    }
                } else {
                    synchronize.log(Level.WARN, "商品 %s 供应商无需变更", itemId);
                }
            }
            synchronize.setProcess(totalPage == 0 ? 100 : (pageIndex / totalPage) * 100);
            pageIndex++;
        }
    }

    @Override
    public boolean isSupport(ItemBatchProc batchProc) {
        return ItemBatchProcType.MODIFY_PROVIDER.getValue().equals(batchProc.getType());
    }
}
