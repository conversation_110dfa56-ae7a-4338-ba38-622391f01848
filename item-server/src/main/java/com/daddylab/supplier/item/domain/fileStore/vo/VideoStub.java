package com.daddylab.supplier.item.domain.fileStore.vo;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class VideoStub extends FileStub {

    /**
     * 时长
     */
    Integer duration;

    /**
     * 视频首帧
     */
    String videoFirstFrame;

    public static VideoStubBuilder<?, ?> builder(String url) {
        return new VideoStubBuilderImpl().url(url);
    }
}
