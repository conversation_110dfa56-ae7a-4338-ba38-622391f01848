package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemWarn;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 平台商品预警 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Repository
public interface PlatformItemWarnMapper extends DaddyBaseMapper<PlatformItemWarn> {

    /**
     * 查询待创建商品匹配预警的平台商品SKU列表
     *
     * @param limit    LIMIT
     * @param cursorId 游标ID
     */
    List<PlatformItemSku> selectToWarnUnmatchSkuList(@Param("limit") int limit, @Param("cursorId") long cursorId, @Param("skipWarned") boolean skipWarned);

    /**
     * 查询待创建库存预警的平台商品SKU列表
     *
     * @param limit    LIMIT
     * @param cursorId 游标ID
     */
    List<PlatformItemSku> selectToWarnStockSkuList(@Param("limit") int limit, @Param("cursorId") long cursorId, @Param("skipWarned") boolean skipWarned);

    void saveOrUpdateBatch(@Param("warns") Collection<PlatformItemWarn> warns);

    int autoHandleExpired(@Param("time") long time);
}
