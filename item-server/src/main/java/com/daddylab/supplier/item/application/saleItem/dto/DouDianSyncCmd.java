package com.daddylab.supplier.item.application.saleItem.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> up
 * @date 2022年09月19日 1:45 PM
 */
@Data
@Slf4j
@ApiModel("新品商品抖店同步请求封装")
public class DouDianSyncCmd {

    @ApiModelProperty("商品id")
    @NotNull
    private Long itemId;

    @ApiModelProperty("商品编码")
    @NotNull
    private String itemCode;

    @ApiModelProperty("抖音商品链接")
    @NotEmpty
    private String douDianUrl;

    @ApiModelProperty("抖店同步类型。31:抖音上新，32:抖音更新")
    @NotNull
    private Integer syncType;

    @ApiModelProperty("sku数量")
    private Integer skuCount;

    @ApiModelProperty("商品名称")
    private String name;

    private Long productId;

//    public static void checkCmd(List<DouDianSyncCmd> list) {
//        List<String> errorList = new LinkedList<>();
//        String regex = "(https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]";
//        for (DouDianSyncCmd douDianSyncCmd : list) {
//            boolean match = ReUtil.isMatch(regex, douDianSyncCmd.getDouDianUrl());
//            if (!match) {
//                errorList.add(douDianSyncCmd.getDouDianUrl());
//            }
//
//            try {
//                Long productId = getDouYinProductIdFromUrl(douDianSyncCmd.getDouDianUrl());
//                douDianSyncCmd.setProductId(productId);
//            } catch (Exception e) {
//                log.error("抖音链接异常，link:{}", douDianSyncCmd.getDouDianUrl(), e);
//                errorList.add(douDianSyncCmd.getDouDianUrl());
//            }
//        }
//
//        if (CollUtil.isNotEmpty(errorList)) {
//            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "抖音商品链接非法。" + StrUtil.join(",", errorList));
//        }
//    }



}
