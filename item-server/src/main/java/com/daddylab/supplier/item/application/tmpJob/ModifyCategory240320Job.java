package com.daddylab.supplier.item.application.tmpJob;

import com.alibaba.cola.exception.BizException;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.daddylab.supplier.item.application.item.ItemSyncWdtBizService;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Category;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @since 2024/3/20
 */
@Service
@Slf4j
public class ModifyCategory240320Job {

    @Data
    public static class ExcelRow {
        @ExcelProperty("商品编码")
        String itemCode;

        @ExcelProperty("商品名称")
        String itemName;

        @ExcelProperty("原一级类目")
        String category1;
        @ExcelProperty("原二级类目")
        String category2;
        @ExcelProperty("原三级类目")
        String category3;
        @ExcelProperty("更改一级类目")
        String newCategory1;
        @ExcelProperty("更改二级类目")
        String newCategory2;
        @ExcelProperty("更改三级类目")
        String newCategory3;

        @ExcelProperty("沫楠更改")
        String modifyFlag;

        @ExcelProperty("结果")
        String result;
    }

    @Autowired
    private FileGateway fileGateway;

    @Autowired
    private CategoryGateway categoryGateway;

    @Autowired
    private ItemSyncWdtBizService itemSyncWdtBizService;

    @Autowired
    private KingDeeTemplate kingDeeTemplate;

    @Autowired
    private IItemService itemService;

    public void handle(InputStream inputStream) {
        try {
            final List<ExcelRow> objects = EasyExcel.read(inputStream).head(
                    ExcelRow.class).sheet("商品分类").doReadSync();
            final String outputPath = String.format("/tmp/修改品类240320-%s.xlsx", DateUtil.formatNow());
            final ExcelWriter outputExcelWriter = EasyExcel.write(outputPath)
                                                           .head(ExcelRow.class)
                                                           .build();
            final WriteSheet outputWriteSheet = EasyExcel.writerSheet("商品分类").build();


            int continuousEmpty = 0;
            for (int i = 0; i < objects.size(); i++) {
                final ExcelRow object = objects.get(i);
                if (!"是".equals(object.getModifyFlag())) {
                    outputExcelWriter.write(Collections.singletonList(object), outputWriteSheet);
                    continue;
                }
                if (StringUtil.isBlank(object.getItemCode())) {
                    continuousEmpty++;
                    outputExcelWriter.write(Collections.singletonList(object), outputWriteSheet);
                    if (continuousEmpty >= 10) {
                        break;
                    }
                    continue;
                } else {
                    continuousEmpty = 0;
                }
                try {
                    final Item item = itemService.getByMixedCode(object.getItemCode());
                    if (item == null) {
                        object.setResult("商品编码无效");
                    } else {
                        final StringJoiner sj = new StringJoiner("/", "新类目/", "");
                        sj.add(object.getNewCategory1());
                        sj.add(object.getNewCategory2());
                        sj.add(object.getNewCategory3());
                        final String newCategoryPath = sj.toString();
                        Category newCategory = categoryGateway.getByPath(newCategoryPath);
                        if (newCategory == null) {
                            object.setResult("更改类目无效");
                        } else {
                            if (!categoryGateway.modifyItemCategory(item.getId(), newCategory.getId())) {
                                object.setResult("修改类目未成功");
                            } else {
                                try {
                                    itemSyncWdtBizService.syncItemToWdt(item.getId());
                                } catch (BizException e) {
                                    if (ErrorCode.GATEWAY_BUSY.is(e)) {
                                        ThreadUtil.sleep(1000);
                                        i--;
                                        continue;
                                    }
                                    throw new RuntimeException("同步旺店通异常:" + e.getMessage(), e);
                                } catch (Exception e) {
                                    throw new RuntimeException("同步旺店通异常:" + e.getMessage(), e);
                                }
                                try {
                                    kingDeeTemplate.syncItem(item.getId());
                                } catch (Exception e) {
                                    throw new RuntimeException("同步金蝶异常:" + e.getMessage(), e);
                                }
                                object.setResult("成功");
                            }
                        }
                    }
                    outputExcelWriter.write(Collections.singletonList(object), outputWriteSheet);
                    if (i % 10 == 0) {
                        log.info("【修改品类240320】第{}行处理完成", i);
                    }
                } catch (Exception e) {
                    log.error("【修改品类240320】第{}行处理异常，{}", i, e.getMessage(), e);
                    object.setResult(e.getMessage());
                    outputExcelWriter.write(Collections.singletonList(object), outputWriteSheet);
                }
            }
            outputExcelWriter.finish();
            final FileStub fileStub = fileGateway.uploadFile(UploadFileAction.ofFile(new File(outputPath)));
            log.info("【修改品类240320】处理完成，结果已写入：{}", fileGateway.getAuthorizedUrl(fileStub.getPath()));
            try {
                Paths.get(outputPath).toFile().delete();
                log.info("【修改品类240320】临时文件已删除");
            } catch (Exception e) {
                log.error("【修改品类240320】临时文件删除失败", e);
            }
        } catch (Exception e) {
            log.error("【修改品类240320】处理异常，{}", e.getMessage(), e);
        }
    }
}
