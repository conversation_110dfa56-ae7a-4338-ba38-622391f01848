package com.daddylab.supplier.item.application.afterSaleLogistics.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/12/31
 */
@AllArgsConstructor
@Getter
public enum AbnormalStatus implements IIntegerEnum {
  CLOSED(0, "已关闭"),
//  PROCESSING(1, "进行中"),
//  PROCESSED(2, "已处理"),
  WAIT_FOLLOW(1, "待跟进"),
  FOLLOWING(2, "跟进中"),
  ;

  private final Integer value;
  private final String desc;
}
