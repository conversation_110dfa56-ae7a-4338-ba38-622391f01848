package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesReturnLogisticsInfo;

/**
 * <p>
 * 售后单退货物流信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-25
 */
public interface AfterSalesReturnLogisticsInfoMapper extends DaddyBaseMapper<AfterSalesReturnLogisticsInfo> {

}
