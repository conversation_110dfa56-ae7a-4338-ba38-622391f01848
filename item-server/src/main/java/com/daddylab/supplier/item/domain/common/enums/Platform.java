package com.daddylab.supplier.item.domain.common.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 平台
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum Platform implements IEnum<Integer> {
    /**
     * 其他
     */
    OTHER(0, "其他", ""),
    /**
     * 淘宝
     */
    TAOBAO(1, "淘宝", "https://cdn-test.daddylab.com/Upload/supplier/item/image/235-1640766835154.png"),
    /**
     * 有赞
     */
    YOUZAN(2, "有赞", "https://cdn-test.daddylab.com/Upload/supplier/item/image/236-1640766847866.png"),
    /**
     * 抖店
     */
    DOUDIAN(3, "抖店", "https://cdn-test.daddylab.com/Upload/supplier/item/image/227-1640766748274.png"),
    /**
     * 快手小店
     */
    KUAISHOU(4, "快手小店", "https://cdn-test.daddylab.com/Upload/supplier/item/image/227-1640766868551.png"),
    /**
     * 小红书
     */
    XIAOHONGSHU(5, "小红书", "https://cdn-test.daddylab.com/Upload/supplier/item/image/246-1640766883631.png"),
    /**
     * 老爸商城（自研商城）
     */
    LAOBASHOP(7, "小程序", "https://cdn-test.daddylab.com/Upload/supplier/item/image/213-1640766898518.png"),
    /**
     * 拼多多
     */
    PDD(8, "拼多多", "https://cdn.pinduoduo.com/upload/home/<USER>/common/pdd_logo_v2.png"),
    /**
     * 京东
     */
    JD(9, "京东", ""),
    /**
     * 微信视频号
     */
    WECHAT_VIDEO(10, "微信视频号", "")
    ;
    final public Integer value;
    final public String desc;
    final public String icon;

    public static Platform of(Integer value) {
        for (Platform platform : Platform.values()) {
            if (Objects.equals(platform.getValue(), value)) {
                return platform;
            }
        }
        return null;
    }

    public static Platform ofName(String value) {
        for (Platform platform : Platform.values()) {
            if (Objects.equals(platform.getDesc(), value)) {
                return platform;
            }
        }
        return null;
    }
}
