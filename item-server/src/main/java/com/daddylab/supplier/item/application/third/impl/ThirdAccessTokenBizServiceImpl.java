package com.daddylab.supplier.item.application.third.impl;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.third.ThirdAccessTokenBizService;
import com.daddylab.supplier.item.domain.third.dto.AccessTokenForm;
import com.daddylab.supplier.item.infrastructure.third.base.AccessTokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @class ThirdAccessTokenBizServiceImpl.java
 * @description 描述类的作用
 * @date 2024-02-27 17:32
 */
@Service
public class ThirdAccessTokenBizServiceImpl implements ThirdAccessTokenBizService {

    @Autowired
    private AccessTokenService accessTokenService;

    @Override
    public SingleResponse<Boolean> saveAccessToken(AccessTokenForm accessTokenForm) {
        return accessTokenService.saveAccessToken(accessTokenForm.getType(), accessTokenForm);
    }
}
