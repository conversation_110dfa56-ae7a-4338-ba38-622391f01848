package com.daddylab.supplier.item.application.salesOutStock;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.SalesOrderDto;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.SkuUnitDto;
import com.daddylab.supplier.item.application.salesOutStock.dto.*;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemInfoForSalesStockBizDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.S01Order;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.S03Order;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.S13OrderNew;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IS01OrderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IS03OrderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IS13OrderNewService;
import com.daddylab.supplier.item.infrastructure.kingdee.dto.KingDeeResp;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqJsonUtil;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2023年10月20日 2:03 PM
 */
@Service
@Slf4j
public class SalesOutStockBizServiceImpl implements SalesOutStockBizService {

    @Resource
    S01OrderMapper taoBaoMapper;
    @Resource
    IS01OrderService taoBaoDbService;

    @Resource
    S03OrderMapper douYinMapper;
    @Resource
    IS03OrderService douYinDbService;

    @Resource
    S13OrderNewMapper redBookMapper;
    @Resource
    IS13OrderNewService redBookDbService;

    @Resource
    WdtSaleStockOutOrderMapper wdtSaleStockOutOrderMapper;

    @Resource
    WdtOrderMapper wdtOrderMapper;

    @Resource
    ItemMapper itemMapper;

    @Resource
    SalesOutStockTransDao salesOutStockTransDao;

    @Resource
    ReqJsonUtil kingDeeJsonUtil;

    @Resource
    ReqTemplate kingDeeReqTemplate;


    @Resource
    WdtSaleStockOutOrderDetailsMapper wdtSaleStockOutOrderDetailsMapper;

    private static final Integer SELECT_NAME_SIZE = 50;

    private static final Integer TAO_BAO_PLATFORM_ID = 1;
    private static final Integer DOU_YIN_PLATFORM_ID = 69;
    private static final Integer RED_BOOK_PLATFORM_ID = 56;


    @Override
    public MultiResponse<BuyerNameDO> getBuyerNickName(String name) {
        CopyOnWriteArrayList<BuyerNameDO> nameContainer = new CopyOnWriteArrayList<>();
        CompletableFuture<Void> taoBaoTask = CompletableFuture.runAsync(() -> {
            List<String> list = taoBaoMapper.selectBuyerName(name, SELECT_NAME_SIZE);
            nameContainer.addAll(convertResList(list, TAO_BAO_PLATFORM_ID));
        });
        CompletableFuture<Void> douYinTask = CompletableFuture.runAsync(() -> {
            List<String> list = douYinMapper.selectBuyerName(name, SELECT_NAME_SIZE);
            nameContainer.addAll(convertResList(list, DOU_YIN_PLATFORM_ID));
        });
        CompletableFuture<Void> redBookTask = CompletableFuture.runAsync(() -> {
            List<String> list = redBookMapper.selectBuyerName(name, SELECT_NAME_SIZE);
            nameContainer.addAll(convertResList(list, RED_BOOK_PLATFORM_ID));
        });
        CompletableFuture.allOf(taoBaoTask, douYinTask, redBookTask).join();

        return MultiResponse.of(nameContainer);
    }

    private List<BuyerNameDO> convertResList(List<String> pList, Integer source) {
        return pList.stream().map(val -> new BuyerNameDO(val, source)).collect(Collectors.toList());
    }

    @Override
    public PageResponse<String> getLogisticsName(LogisticsNamePageQuery pageQuery) {
        PageInfo<String> namePage = PageHelper.startPage(pageQuery.getPageIndex(), pageQuery.getPageSize())
                .doSelectPageInfo(() -> wdtSaleStockOutOrderMapper.selectLogisticsName(pageQuery));
        return PageResponse.of(namePage.getList(), (int) namePage.getTotal(), pageQuery.getPageSize(), pageQuery.getPageIndex());
    }

    @Override
    @DS("master")
    public PageResponse<SalesOutStockPageVO> queryPage(SalesOutStockPageQuery query) {
        if (Objects.nonNull(query.getBuyerNameDO())) {
            Collection<Long> wdtIds = queryPageWithBuyerNickName(query);
            if (CollUtil.isEmpty(wdtIds)) {
                return PageResponse.of(new ArrayList<>(), 0, query.getPageSize(), query.getPageIndex());
            } else {
                query.setWdtOrderIds(wdtIds);
                // wdt订单转为tradeNo
                List<String> tradeNos = wdtOrderMapper.tradeNos(wdtIds);
                tradeNos.add(query.getTradeNo());
                tradeNos = tradeNos.stream().distinct().collect(Collectors.toList());
                query.setTradeNos(tradeNos);
            }
        }

        if (StrUtil.isNotBlank(query.getSpecCode())
                || StrUtil.isNotBlank(query.getGoodsName())
                || StrUtil.isNotBlank(query.getGoodsNo())
        ) {
            List<Long> stockOutId = wdtSaleStockOutOrderDetailsMapper.getStockOutId(query.getSpecCode(), query.getGoodsName(), query.getGoodsNo());
            stockOutId = stockOutId.stream().distinct().collect(Collectors.toList());
            query.setStockOutIds(stockOutId);
        }

        PageInfo<SalesOutStockPageVO> objectPageInfo = PageHelper.startPage(query.getPageIndex(), query.getPageSize(), false)
                .doSelectPageInfo(() -> wdtSaleStockOutOrderMapper.selectSalesOutStockPage(query));
        Integer integer = wdtSaleStockOutOrderMapper.countSalesOutStockPage(query);
        objectPageInfo.setTotal(integer);
        return PageResponse.of(objectPageInfo.getList(), (int) objectPageInfo.getTotal(), query.getPageSize(), query.getPageIndex());
    }

    private Collection<Long> queryPageWithBuyerNickName(SalesOutStockPageQuery query) {
        BuyerNameDO buyerNameDO = query.getBuyerNameDO();
        Integer source = buyerNameDO.getSource();
        String name = buyerNameDO.getName();
        if (TAO_BAO_PLATFORM_ID.equals(source)) {
            Set<String> orderNos = taoBaoDbService.lambdaQuery()
                    .eq(S01Order::getReceiverName, name).select(S01Order::getOrderNo).list()
                    .stream().map(S01Order::getOrderNo).collect(Collectors.toSet());
            if (CollUtil.isEmpty(orderNos)) {
                return Collections.emptyList();
            }
            return wdtOrderMapper.selectIdBySrcTids(orderNos, TAO_BAO_PLATFORM_ID);
        }
        if (DOU_YIN_PLATFORM_ID.equals(source)) {
            Set<String> orderNos = douYinDbService.lambdaQuery()
                    .eq(S03Order::getReceiverName, name).select(S03Order::getOrderNo).list()
                    .stream().map(S03Order::getOrderNo).collect(Collectors.toSet());
            if (CollUtil.isEmpty(orderNos)) {
                return Collections.emptyList();
            }
            return wdtOrderMapper.selectIdBySrcTids(orderNos, DOU_YIN_PLATFORM_ID);
        }
        if (RED_BOOK_PLATFORM_ID.equals(source)) {
            Set<String> orderNos = redBookDbService.lambdaQuery()
                    .eq(S13OrderNew::getReceiverName, name).select(S13OrderNew::getOrderId).list()
                    .stream().map(S13OrderNew::getOrderId).collect(Collectors.toSet());
            if (CollUtil.isEmpty(orderNos)) {
                return Collections.emptyList();
            }
            return wdtOrderMapper.selectIdBySrcTids(orderNos, RED_BOOK_PLATFORM_ID);
        }
        return Collections.emptyList();
    }


    @Override
    public SingleResponse<SalesOutStockDetailVO> viewDetail(Long stockoutId) {
        SalesOutStockDetailVO res = new SalesOutStockDetailVO();

        CompletableFuture<Void> baseInfoTask = CompletableFuture.runAsync(() -> {
            SalesOutStockDetailBaseVO baseInfo = wdtSaleStockOutOrderMapper.selectSalesOutStockBaseInfo(stockoutId);
            res.setBaseInfo(baseInfo);
        });

        CompletableFuture<Void> detailListTask = CompletableFuture.runAsync(() -> {
            List<SalesOutStockDetailListVO> detailList = wdtSaleStockOutOrderMapper.selectSalesOutStockDetailList(stockoutId);
            Set<String> itemCode = detailList.stream().map(SalesOutStockDetailListVO::getGoodsNo).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(itemCode)) {
                Map<String, ItemInfoForSalesStockBizDO> map =
                        itemMapper.selectItemInfoForSalesStockBizDO(itemCode).stream()
                                .collect(Collectors.toMap(ItemInfoForSalesStockBizDO::getItemCode, v -> v));
                detailList.forEach(val -> {
                    ItemInfoForSalesStockBizDO vv = map.get(val.getGoodsNo());
                    if (Objects.nonNull(vv)) {
                        val.setCategory(vv.getCategory());
                        val.setBrand(vv.getBrand());
                        val.setItemCode(vv.getItemCode());
                        val.setItemName(vv.getItemName());
                        val.setImgUrl(vv.getImgUrl());
                    }
                });
            }
            res.setLists(detailList);
        });
        CompletableFuture.allOf(baseInfoTask, detailListTask).join();

        res.setListTotalAmount(res.getBaseInfo().getReceivable());
        res.setListTotalQuantity(res.getBaseInfo().getGoodsCount().intValue());

        return SingleResponse.of(res);
    }

    @Override
    public SingleResponse<SalesOrderDto> generateOrderBySettlement(String warehouseNo, List<SkuUnitDto> salesOutList
            , Long auditDate, Boolean mockSync) {

        log.info("generateOrderBySettlement param.warehouseNo:{},details:{},auditDate:{},mockSync:{}",
                warehouseNo, JsonUtil.toJson(salesOutList), auditDate, mockSync);

        SalesOrderDto resDto = new SalesOrderDto();
        Map<String, Integer> salesStockOutMap = new HashMap<>(16);
        WdtStockOutDto wdtDataNoDto;

        try {
            wdtDataNoDto = salesOutStockTransDao.buildWdtData(warehouseNo, salesOutList, salesStockOutMap);
            resDto.setNo(wdtDataNoDto.getOrderNo());

            if (mockSync) {
                return SingleResponse.of(resDto);
            }

            // 推送到销售出库单
            String jsonParam = kingDeeJsonUtil.saveSaleOutStock(salesStockOutMap, auditDate, wdtDataNoDto.getOrderNo());
            KingDeeResp resp = kingDeeReqTemplate.saveWithRes(jsonParam);
            resDto.setKingDeeId(resp.getId());
            resDto.setKingDeeNumber(resDto.getKingDeeNumber());
            return SingleResponse.of(resDto);
        } catch (Exception e) {
            log.error("销售出库单同步金蝶异常", e);
            return ResponseFactory.buildFail(ErrorCode.API_RESP_ERROR, "销售出库单同步到金蝶异常." + e.getMessage());
        }
    }


}

