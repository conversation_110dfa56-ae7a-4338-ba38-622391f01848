package com.daddylab.supplier.item.controller.stockout.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/4/11 11:41
 * @description StockOutOrderDetailCmd
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "出库明细详情")
public class StockOutOrderDetailCmd extends Command {

    private static final long serialVersionUID = 1L;

    /**
     * 出库单id
     */
    @ApiModelProperty(value = "出库单id")
    private Long id;

    /**
     * 商品id
     */
    @NotNull(message = "商品id不能为空")
    @ApiModelProperty(value = "商品id")
    private Long itemId;

    /**
     * 商品skuCode
     */
    @NotBlank(message = "商品SKU不能为空")
    @ApiModelProperty(value = "商品sku")
    private String itemSkuCode;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    /**
     * 规格
     */
    @NotBlank(message = "规格名称不能为空")
    @ApiModelProperty(value = "规格")
    private String specifications;

    /**
     * 库存单位
     */
    @NotBlank(message = "库存单位不能为空")
    @ApiModelProperty(value = "库存单位")
    private String stockUnit;

    /**
     * 计价单位
     */
    @NotBlank(message = "计价单位不能为空")
    @ApiModelProperty(value = "计价单位")
    private String valuationUnit;

    /**
     * 出库数量
     */
    @NotNull(message = "申请推料数量不能为空")
    @ApiModelProperty(value = "出库数量")
    private Integer returnQuantity;

    /**
     * 仓库编号
     */
    @NotBlank(message = "退料仓库不能为空")
    @ApiModelProperty(value = "仓库编号")
    private String warehouseNo;

    /**
     * 退料原因
     */
    @ApiModelProperty(value = "退料原因")
    private String returnReason;

    /**
     * 是否是赠品,0不是。1是
     */
    @NotNull(message = "是否赠品不能为空")
    @ApiModelProperty(value = "是否赠品")
    private Integer isGift;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 含税单价(6位)
     */
    @ApiModelProperty(value = "含税单价(6位)")
    private BigDecimal taxPrice;

    /**
     * 税率(实际计算值，非百分比)
     */
    @ApiModelProperty(value = "税率(实际计算值，非百分比)")
    private BigDecimal taxRate;

    private Integer realReturnQuantity;

//    /**
//     * 税额
//     */
//    @ApiModelProperty(value = "税额")
//    private BigDecimal taxQuota;
//
//    /**
//     * 价税合计
//     */
//    @ApiModelProperty(value = "价税合计")
//    private BigDecimal totalPriceTax;
//
//    /**
//     * 税后单价(6位)
//     */
//    @ApiModelProperty(value = "税后单价(6位)")
//    private BigDecimal afterTaxPrice;
//
//    /**
//     * 税后金额
//     */
//    @ApiModelProperty(value = "税后金额")
//    private BigDecimal afterTaxAmount;

}
