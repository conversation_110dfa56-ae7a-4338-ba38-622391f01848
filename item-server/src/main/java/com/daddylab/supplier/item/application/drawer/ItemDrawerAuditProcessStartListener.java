package com.daddylab.supplier.item.application.drawer;

import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
import com.daddylab.supplier.item.types.itemDrawer.ItemLaunchAuditProcessStartEvent;
import com.google.common.eventbus.Subscribe;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/12/6
 */
@EventBusListener(value = "ItemDrawerAuditProcessStartListener")
@RequiredArgsConstructor
public class ItemDrawerAuditProcessStartListener {

    private final ItemDrawerRecognitionBizService itemDrawerRecognitionBizService;

    @Subscribe
    public void listener(ItemLaunchAuditProcessStartEvent event) {
        Long itemId = event.getItemId();
        itemDrawerRecognitionBizService.createRecognitionTasks(event.getType(), itemId, event.getRound(), event.getModuleIds());
    }
}
