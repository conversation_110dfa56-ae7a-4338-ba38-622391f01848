package com.daddylab.supplier.item.application.itemOptimize;

import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemOptimize;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemOptimizePlan;
import com.daddylab.supplier.item.types.itemOptimize.*;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/20
 */
@Mapper(uses = {StaffAssembler.class})
public interface ItemOptimizeAssembler {
    ItemOptimizeAssembler INST = Mappers.getMapper(ItemOptimizeAssembler.class);

    @Mapping(target = "createUser", source = "createdUid")
    ItemOptimizePlanBaseInfo itemOptimizePlanToItemOptimizePlanBaseInfo(ItemOptimizePlan itemOptimizePlan);

    List<ItemOptimizePlanItem> itemOptimizeListToItemOptimizePlanItemList(List<ItemOptimize> itemOptimizes);

    @Mapping(target = "plan", ignore = true)
    ItemOptimizePlanItem itemOptimizeToItemOptimizePlanItem(ItemOptimize itemOptimize);

    ItemOptimizePlan itemOptimizePlanSaveCmdToItemOptimizePlan(
            ItemOptimizePlanSaveCmd saveCmd);

    List<ItemOptimize> itemOptimizePlanSaveCmdItemListToItemOptimizeList(List<ItemOptimizePlanSaveCmdItem> items);

    @Mapping(
            target = "data",
            expression = "java(itemOptimizePlanItemSaveCmdToItemOptimizeData(item))")
    ItemOptimize itemOptimizePlanSaveCmdItemToItemOptimize(ItemOptimizePlanSaveCmdItem item);

    @Mapping(target = "modifyUsers", source = "modifyUserIds")
    @Mapping(target = "qcUsers", source = "qcUserIds")
    @Mapping(target = "legalUsers", source = "legalUserIds")
    @Mapping(target = "buyerUsers", source = "buyerUserIds")
    ItemOptimizePersistData itemOptimizePlanItemSaveCmdToItemOptimizeData(
            ItemOptimizePlanSaveCmdItem item);

    ItemOptimizePersistData itemOptimizeSaveCmdDataToPersistData(ItemOptimizeSaveCmdData data);

    @Mapping(target = "modifyUserIds", source = "modifyUsers")
    @Mapping(target = "qcUserIds", source = "qcUsers")
    @Mapping(target = "legalUserIds", source = "legalUsers")
    @Mapping(target = "buyerUserIds", source = "buyerUsers")
    ItemOptimizeSaveCmdData persistDataToItemOptimizeSaveCmdData(ItemOptimizePersistData data);

    ItemOptimizePersistData copyItemOptimizePersistData(ItemOptimizePersistData data);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "qcUsers", source = "qcUserIds", qualifiedByName = "ItemOptimize")
    @Mapping(target = "modifyUsers", source = "modifyUserIds", qualifiedByName = "ItemOptimize")
    @Mapping(target = "legalUsers", source = "legalUserIds", qualifiedByName = "ItemOptimize")
    @Mapping(target = "buyerUsers", source = "buyerUserIds", qualifiedByName = "ItemOptimize")
    ItemOptimizePersistData updateItemOptimizePersistDataByCmdData(@MappingTarget ItemOptimizePersistData data, ItemOptimizeSaveCmdData cmdData);

    @Named("ItemOptimize")
    default List<StaffBrief> longListToStaffBriefList(List<Long> idList) {
        return StaffAssembler.INST.longListToStaffBriefList(idList, false);
    }

    ItemOptimize itemOptimizeSaveCmdDataToItemOptimize(ItemOptimizeSaveCmdData data);

    @Mapping(target = "modified", ignore = true)
    ItemOptimizeSaveCmdData itemOptimizePlanSaveCmdItemToItemOptimizeSaveCmdData(ItemOptimizePlanSaveCmdItem item);

    ItemOptimize copyItemOptimize(ItemOptimize itemOptimize);

    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "outerItemId", source = "outerItemId")
    @Mapping(target = "itemCode", source = "itemCode")
    @Mapping(
            target = "data",
            expression =
                    "java(updateItemOptimizePersistDataByCmdData(itemOptimize.getData(), saveCmd.getData()))")
    void updateItemOptimizeByCmd(
            @MappingTarget ItemOptimize itemOptimize, ItemOptimizeSaveCmd saveCmd);

    @Mapping(target = "modifyUserIds", source = "data.modifyUsers")
    @Mapping(target = "qcUserIds", source = "data.qcUsers")
    @Mapping(target = "legalUserIds", source = "data.legalUsers")
    @Mapping(target = "buyerUserIds", source = "data.buyerUsers")
    @Mapping(target = "modifyTime", source = "data.modifyTime")
    @Mapping(target = "link", source = "data.link")
    @Mapping(target = "canBeSupplementedHotSearchKeywords", source = "data.canBeSupplementedHotSearchKeywords")
    ItemOptimizePlanSaveCmdItem itemOptimizeToItemOptimizeSaveCmdItem(ItemOptimize item);
}
