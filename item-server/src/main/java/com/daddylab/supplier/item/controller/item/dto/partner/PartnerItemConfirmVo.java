package com.daddylab.supplier.item.controller.item.dto.partner;

import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/29 2:10 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("确定合作伙伴系统商品，返回封装")
public class PartnerItemConfirmVo extends PartnerItemVo {

    @ApiModelProperty("品牌id")
    private Long brandId;

    @ApiModelProperty("供应商id")
    private Long providerId;

    @ApiModelProperty("合作方-业务类型")
    private List<CorpBizTypeDTO> corpBizType;

}
