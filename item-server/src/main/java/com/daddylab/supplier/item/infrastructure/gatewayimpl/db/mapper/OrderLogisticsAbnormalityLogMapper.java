package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.afterSaleLogistics.dto.AfterSaleLogisticsPageQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.AbnormalityCountObj;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.AbnormalityListObj;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsAbnormality;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsAbnormalityLog;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 订单物流异常记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
public interface OrderLogisticsAbnormalityLogMapper
    extends DaddyBaseMapper<OrderLogisticsAbnormalityLog> {

  /**
   * 查询异常数量对象列表
   *
   * @param query 售后物流分页查询参数对象
   * @return 异常数量对象列表
   */
  List<AbnormalityCountObj> selectAbnormalityCountObjs(
      @Param("query") AfterSaleLogisticsPageQuery query);

  /**
   * 查询异常列表
   *
   * @param query 售后物流分页查询参数对象
   * @return 异常列表对象集合
   */
  List<AbnormalityListObj> selectAbnormalityList(@Param("query") AfterSaleLogisticsPageQuery query);

  /**
   * 查询异常列表数量
   *
   * @param query 售后物流分页查询参数对象
   * @return 异常列表数量
   */
  Integer selectAbnormalityListCount(@Param("query") AfterSaleLogisticsPageQuery query);

  /**
   * 查询异常PO列表
   *
   * @param query 售后物流分页查询参数对象
   * @return 异常PO列表对象集合
   */
  List<OrderLogisticsAbnormality> selectAbnormalityPOList(
      @Param("query") AfterSaleLogisticsPageQuery query);
}
