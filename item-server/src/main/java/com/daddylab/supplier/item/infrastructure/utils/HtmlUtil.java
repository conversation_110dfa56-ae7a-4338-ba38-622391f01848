package com.daddylab.supplier.item.infrastructure.utils;

import cn.hutool.core.util.ArrayUtil;

/**
 * <AUTHOR>
 * @since 2022/12/19
 */
public class HtmlUtil extends cn.hutool.http.HtmlUtil {

    private static final String[] HTML_TAGS = new String[]{
            "a",
            "abbr",
            "acronym",
            "address",
            "area",
            "b",
            "base",
            "basefont",
            "bdo",
            "bgsound",
            "big",
            "br",
            "button",
            "cite",
            "code",
            "col",
            "command",
            "datalist",
            "del",
            "device",
            "dfn",
            "em",
            "embed",
            "fieldset",
            "font",
            "frame",
            "h1",
            "h2",
            "h3",
            "h4",
            "h5",
            "h6",
            "hr",
            "i",
            "iframe",
            "img",
            "input",
            "ins",
            "kbd",
            "keygen",
            "label",
            "legend",
            "li",
            "link",
            "map",
            "mark",
            "menuitem",
            "meta",
            "meter",
            "object",
            "optgroup",
            "option",
            "output",
            "p",
            "param",
            "plaintext",
            "pre",
            "progress",
            "q",
            "rp",
            "rt",
            "ruby",
            "s",
            "samp",
            "script",
            "select",
            "small",
            "source",
            "span",
            "strong",
            "style",
            "sub",
            "summary",
            "sup",
            "td",
            "textarea",
            "th",
            "time",
            "title",
            "track",
            "tt",
            "u",
            "var",
            "wbr"
    };

    public static boolean containHtmlTag(String text) {
        return containHtmlTag(text, 0);
    }

    public static boolean containHtmlTag(String text, int startPos) {
        if (StringUtil.isEmpty(text)) {
            return false;
        }
        final int firstIndexOfTagStart = text.indexOf('<', startPos);
        if (firstIndexOfTagStart < 0) {
            return false;
        }
        final int nextIndexOfMatchTagEnd = text.indexOf('>', firstIndexOfTagStart + 1);
        if (nextIndexOfMatchTagEnd < 0) {
            return false;
        }
        final String tag = text.substring(firstIndexOfTagStart + 1, nextIndexOfMatchTagEnd);
        if (StringUtil.isEmpty(tag)) {
            return containHtmlTag(text, nextIndexOfMatchTagEnd + 1);
        }
        int tagNamePosEnd = -1;
        final int indexOfFirstSpace = tag.indexOf(" ");
        if (indexOfFirstSpace > -1) {
            tagNamePosEnd = indexOfFirstSpace;
        } else {
            final int lastIndexOfSlash = tag.lastIndexOf("/");
            if (lastIndexOfSlash > -1 && lastIndexOfSlash == tag.length() - 1) {
                tagNamePosEnd = lastIndexOfSlash;
            }
        }
        if (!ArrayUtil.contains(HTML_TAGS,
                tagNamePosEnd > -1 ? tag.substring(0, tagNamePosEnd) : tag)) {
            return containHtmlTag(text, nextIndexOfMatchTagEnd + 1);
        }
        return true;
    }
}
