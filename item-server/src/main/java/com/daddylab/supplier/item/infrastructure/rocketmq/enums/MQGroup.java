package com.daddylab.supplier.item.infrastructure.rocketmq.enums;

/**
 * <AUTHOR>
 * @class MQGroup.java
 * @description 消费者组
 * @date 2024-02-29 17:13
 */
public interface MQGroup {

    String THIRD_CALLBACK_MESSAGE_KS_GROUP = "${spring.profiles.active}_${spring.application.name}thirdCallbackMessageKuaiShouGroup";

    String THIRD_CALLBACK_MESSAGE_RED_BOOK_GROUP = "${spring.profiles.active}_${spring.application.name}thirdCallbackMessageRedBookGroup";

    String THIRD_MALL_ITEM_CHANGE_GROUP = "${spring.profiles.active}_mallItemChangeGroup";

    /**
     * 平台商品库存变更消息
     */
    String PLAT_ITEM_SKU_INVENTORY_GROUP = "${spring.profiles.active}_${spring.application.name}platformItemSkuInventoryGroup";
}
