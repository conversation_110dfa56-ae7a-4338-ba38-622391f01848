package com.daddylab.supplier.item.domain.platformItem.service.event;

import lombok.Data;

@Data
public class SyncSaleItemLibraryEvent {

    private String goodsName;

    private String itemSkuCode;


    public static SyncSaleItemLibraryEvent ofNotice(String goodsName, String itemSkuCode) {
        SyncSaleItemLibraryEvent syncSaleItemLibraryEvent = new SyncSaleItemLibraryEvent();
        syncSaleItemLibraryEvent.setGoodsName(goodsName);
        syncSaleItemLibraryEvent.setItemSkuCode(itemSkuCode);
        return syncSaleItemLibraryEvent;
    }
}
