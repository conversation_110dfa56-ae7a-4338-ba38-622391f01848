package com.daddylab.supplier.item.domain.fileStore.vo;

import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.crypto.digest.DigestUtil;

import com.daddylab.supplier.item.domain.fileStore.config.UploadConfig;
import com.daddylab.supplier.item.domain.fileStore.constant.FileType;
import com.daddylab.supplier.item.domain.fileStore.exception.FileStoreException;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.FileUtil;
import com.daddylab.supplier.item.infrastructure.utils.IOUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;

import lombok.Getter;
import lombok.SneakyThrows;

import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Optional;
import java.util.function.Supplier;

import javax.annotation.Nullable;

@Getter
public class UploadFileAction {

    /** 上传目标路径 */
    String destPath;

    /** 文件名称 */
    String fileName;

    public String getFileName() {
        return URLUtil.decode(fileName);
    }

    /** 文件类型 */
    FileType fileType;

    String md5;

    byte[] bytes;

    /** 是否是私有文件（又拍云未实现） */
    boolean privateFile;

    private UploadFileAction(UploadFileActionBuilder builder) {
        this.bytes = IOUtil.readBytes(builder.inputStreamSupplier.get());
        this.destPath = builder.destPath;
        this.fileName = FileNameUtil.getName(builder.fileName);
        this.fileType = builder.fileType;
        this.privateFile = builder().privateFile;
    }

    public static UploadFileAction ofFile(String destPath, File file) {
        return builder()
                .destPath(destPath)
                .fileName(file.getName().trim())
                .inputStreamSupplier(() -> FileUtil.getInputStream(file))
                .build();
    }

    public static UploadFileAction ofFile(File file) {
        return ofFile(null, file);
    }

    public static UploadFileAction ofMultipartFile(MultipartFile multipartFile) {
        return ofMultipartFile(multipartFile, null);
    }

    public static UploadFileAction ofMultipartFile(MultipartFile multipartFile, String fileName) {
        return builderOfMultipartFile(multipartFile, fileName).build();
    }

    public static UploadFileActionBuilder builderOfMultipartFile(
            MultipartFile multipartFile, String fileName) {
        return builder()
                .fileName(
                        StringUtil.isNotBlank(fileName)
                                ? fileName
                                : multipartFile.getOriginalFilename())
                .inputStreamSupplier(
                        () -> {
                            try {
                                return multipartFile.getInputStream();
                            } catch (IOException e) {
                                throw new FileStoreException("文件存储失败，打开文件输入流失败", e);
                            }
                        });
    }

    public String getMd5() {
        if (md5 == null) {
            md5 = DigestUtil.md5Hex(bytes);
        }
        return md5;
    }

    public static UploadFileAction ofInputStream(InputStream inputStream, String filename) {
        return builder().fileName(filename).inputStreamSupplier(() -> inputStream).build();
    }

    public static UploadFileActionBuilder builder() {
        return new UploadFileActionBuilder();
    }

    public InputStream getInputStream() {
        return new ByteArrayInputStream(bytes);
    }

    public static class UploadFileActionBuilder {

        private Supplier<InputStream> inputStreamSupplier;
        private String fileName;
        private String destPath;
        @Nullable private FileType fileType;
        boolean privateFile = true;

        UploadFileActionBuilder() {}

        private static String getDefaultDestPath(String fileName, FileType fileType) {
            final UploadConfig uploadConfig = ApplicationContextUtil.getBean(UploadConfig.class);
            // 如果传了文件类型则取传入的类型，如果没有尝试根据文件名后缀匹配
            String fileTypeDir =
                    Optional.ofNullable(fileType)
                            .map(v -> v.name().toLowerCase() + "/")
                            .orElseGet(
                                    () -> {
                                        final String mimeType = FileUtil.getMimeType(fileName);
                                        if (StringUtil.isNotBlank(mimeType)) {
                                            final String[] split = mimeType.split("/");
                                            if (split.length > 0) {
                                                return split[0] + "/";
                                            }
                                        }
                                        return "";
                                    });
            String suffix = FileUtil.getSuffix(fileName);
            suffix = StringUtil.isNotBlank(suffix) ? "." + suffix : "";
            final String nameWithoutSuffix = FileUtil.getNameWithoutSuffix(fileName);
            return uploadConfig.getPublicDir()
                    + fileTypeDir
                    + (uploadConfig.isPreserveName()
                            ? nameWithoutSuffix + "-" + DateUtil.currentTimeMillis()
                            : IdUtil.getSnowflake().nextIdStr())
                    + suffix;
        }

        public UploadFileActionBuilder inputStreamSupplier(
                Supplier<InputStream> inputStreamSupplier) {
            this.inputStreamSupplier = inputStreamSupplier;
            return this;
        }

        public UploadFileActionBuilder destPath(String destPath) {
            this.destPath = destPath;
            return this;
        }

        public UploadFileActionBuilder fileName(String fileName) {
            this.fileName = fileName;
            return this;
        }

        public UploadFileActionBuilder fileType(FileType fileType) {
            this.fileType = fileType;
            return this;
        }

        public UploadFileActionBuilder privateFile(boolean privateFile) {
            this.privateFile = privateFile;
            return this;
        }

        @SneakyThrows
        public UploadFileAction build() {
            Assert.notNull(inputStreamSupplier, "输入流不能为空");
            if (StringUtil.isBlank(fileName)) {
                fileName = RandomUtil.randomString(16);
            }
            Assert.hasText(fileName, "文件名不能为空");
            if (this.destPath == null) {
                this.destPath = getDefaultDestPath(fileName, fileType);
            }
            return new UploadFileAction(this);
        }

        @Override
        public String toString() {
            return "UploadFileAction.UploadFileActionBuilder(inputStreamSupplier="
                    + this.inputStreamSupplier
                    + ", destPath="
                    + this.destPath
                    + ", fileName="
                    + this.fileName
                    + ", fileType="
                    + this.fileType
                    + ")";
        }
    }
}
