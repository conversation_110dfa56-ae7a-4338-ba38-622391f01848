package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 销售商品库
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SaleItemLibrary implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品SKU编码
     */
    private String itemSkuCode;

    /**
     * 会员店商品ID
     */
    private String memberStoreItemId;

    /**
     * 小程序商品ID
     */
    private String appletsItemId;

    /**
     * 美妆店商品ID
     */
    private String beautyStoreItemId;

    /**
     * 母婴店商品ID
     */
    private String maternityBabyItemId;

    /**
     * 小红书商品ID
     */
    private String wotoBuyItemId;

    /**
     * 抖音商品ID
     */
    private String tikTokItemId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 行业类目
     */
    private String category;

    /**
     * 直播/短视频/社群价格
     */
    private String platformPrice;

    /**
     * 直播/短视频/社群活动内容
     */
    private String platformActivityContent;

    /**
     * 直播/短视频/社群成本
     */
    private String platformCost;

    /**
     * 直播/短视频/社群活动编码
     */
    private String platformActivityCode;

    /**
     * 特殊备注（仅限某渠道、库存限量等）
     */
    private String remark;

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除，0：未删除，1：已删除
     */
    @TableLogic
    private Integer isDel;

    private Long deletedAt;
}
