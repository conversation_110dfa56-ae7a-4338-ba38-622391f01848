package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOtherStockInOrderDetails;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOtherStockInOrderDetailsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOtherStockInOrderDetailsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 旺店通其他入库单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Service
public class WdtOtherStockInOrderDetailsServiceImpl extends DaddyServiceImpl<WdtOtherStockInOrderDetailsMapper, WdtOtherStockInOrderDetails> implements IWdtOtherStockInOrderDetailsService {

}
