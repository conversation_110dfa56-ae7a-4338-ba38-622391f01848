package com.daddylab.supplier.item.controller.item.dto;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @ClassName ItemSpecPageQuery.java
 * @description
 * @createTime 2022年06月08日 15:56:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("商品规格分页查询参数")
public class ItemSpecPageQuery  extends PageQuery {

    private static final long serialVersionUID = 6497922156943383051L;

    @ApiModelProperty("规格")
    private String spec;
}
