package com.daddylab.supplier.item.domain.purchase.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName PurchaseArea.java
 * @description
 * @createTime 2021年11月19日 21:50:00
 */
@Getter
@AllArgsConstructor
public enum PurchaseArea implements IEnum<Integer> {
    FACTORY(0, "工厂"),
    WAREHOUSE(1, "仓库");
    final public Integer value;
    final public String desc;



}
