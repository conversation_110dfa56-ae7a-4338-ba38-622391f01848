package com.daddylab.supplier.item.application.afterSaleLogistics.dto;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2024年05月23日 9:41 AM
 */
@Data
@ApiModel("订单物流异常列表请求参数")
public class AfterSaleLogisticsPageQuery extends PageQuery {

  private static final long serialVersionUID = -4039569656580418088L;

  @ApiModelProperty("物流单号")
  private String logisticsNo;

  @ApiModelProperty("物流公司")
  private String logisticsCompany;

  @ApiModelProperty("物流状态。0 待发货，1 已发货，2 已揽收，3 运输中，4 派送中，5 待取件，6 未签收，7 代签收，8 已签收")
  private Integer logisticsStatus;

  @ApiModelProperty("异常状态。0:已关闭 1:待处理 2:跟进中")
  private Integer abnormalStatus;

  @ApiModelProperty("出库仓库")
  private String stockoutWarehouseNo;

  @ApiModelProperty("出库单号")
  private String stockoutNo;

  @ApiModelProperty("平台。1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城 8:拼多多 9:京东")
  private Integer platform;

  @ApiModelProperty("原始单号")
  private String srcOrderNo;

  @ApiModelProperty("异常类型（一级）")
  private Integer logisticsExceptionType;

  @ApiModelProperty("异常类型（二级）")
  private List<Integer> logisticsExceptionType2;

  @ApiModelProperty(value = "订单支付起始时间")
  private Long payTimeStart;

  @ApiModelProperty(value = "订单支付结束时间")
  private Long payTimeEnd;

  @ApiModelProperty(value = "分配状态", notes = "0:未分配 1:已分配")
  private Integer distributeStatus;

  @ApiModelProperty(value = "负责客服")
  private Long csUserId;

  @ApiModelProperty(value = "店铺ID")
  private Long shopId;

  @ApiModelProperty(value = "订单金额范围最小值")
  private BigDecimal orderAmountMin;

  @ApiModelProperty(value = "订单金额范围右值")
  private BigDecimal orderAmountMax;

  @ApiModelProperty(value = "发货时间范围最小值")
  private Long deliveryTimeStart;

  @ApiModelProperty(value = "发货时间范围最大值")
  private Long deliveryTimeEnd;

  @ApiModelProperty(value = "下单时间范围最小值")
  private Long orderTimeStart;

  @ApiModelProperty(value = "下单时间范围最大值")
  private Long orderTimeEnd;
}
