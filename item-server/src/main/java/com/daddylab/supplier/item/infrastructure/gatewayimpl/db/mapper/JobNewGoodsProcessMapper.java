package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.JobNewGoodsProcess;

/**
 * <p>
 * 新品商品数据处理（临时任务） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
public interface JobNewGoodsProcessMapper extends DaddyBaseMapper<JobNewGoodsProcess> {

}
