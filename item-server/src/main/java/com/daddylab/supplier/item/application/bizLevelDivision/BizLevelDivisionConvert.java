package com.daddylab.supplier.item.application.bizLevelDivision;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.common.area.Division;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CascadedDivisionLevel;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BizLevelDivision;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.enums.PSysBusinessTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.enums.PSysCooperatorEnum;
import org.jetbrains.annotations.NotNull;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/14
 */
@Mapper
public interface BizLevelDivisionConvert {
  BizLevelDivisionConvert INSTANCE = Mappers.getMapper(BizLevelDivisionConvert.class);

  default ArrayList<CascadedDivisionLevel> corpBizTypeToCascadedDivisionLevels(
      List<CorpBizTypeDTO> corpBizType) {
    final ArrayList<CascadedDivisionLevel> divisionLevels = new ArrayList<>();
    for (CorpBizTypeDTO corpBizTypeDTO : corpBizType) {
      final CascadedDivisionLevel cascadedDivisionLevel =
          new CascadedDivisionLevel(corpBizTypeDTO.getCorpType());
      if (corpBizTypeDTO.getBizType() != null) {
        for (Integer bizType : corpBizTypeDTO.getBizType()) {
          cascadedDivisionLevel.addSubValue(bizType);
        }
      }
      divisionLevels.add(cascadedDivisionLevel);
    }
    return divisionLevels;
  }

  default List<CorpBizTypeDTO> cascadedDivisionLevelToCorpBizType(
      List<CascadedDivisionLevel> divisionLevels) {
    final ArrayList<CorpBizTypeDTO> corpBizType = new ArrayList<>();
    for (CascadedDivisionLevel cascadedLevel : divisionLevels) {
      if (cascadedLevel.getValue().getDivisionLevel() == DivisionLevelEnum.COOPERATION) {
        CorpBizTypeDTO corpBizTypeDto = new CorpBizTypeDTO();
        corpBizType.add(corpBizTypeDto);
        corpBizTypeDto.setCorpType(cascadedLevel.getValue().getValue());
        final ArrayList<Integer> bizType = new ArrayList<>();
        for (CascadedDivisionLevel subValue : cascadedLevel.getSubValues()) {
          if (subValue.getValue().getDivisionLevel() == DivisionLevelEnum.BUSINESS_TYPE) {
            bizType.add(subValue.getValue().getValue());
          }
        }
        corpBizTypeDto.setBizType(bizType);
      }
    }
    return corpBizType;
  }

  @NotNull
  default List<Integer> toBizLevelIntegerList(
      List<BizLevelDivision> bizLevelDivisions, DivisionLevelEnum divisionLevelEnum) {
    return bizLevelDivisions.stream()
        .filter(v -> divisionLevelEnum == v.getLevel())
        .map(BizLevelDivision::getLevelVal)
        .map(DivisionLevelValueEnum::getValue)
        .collect(Collectors.toList());
  }

  @NotNull
  default String toBizLevelDescStr(List<DivisionLevelValueEnum> divisionLevelValueEnums) {
    return divisionLevelValueEnums.stream()
        .map(DivisionLevelValueEnum::getDesc)
        .collect(Collectors.joining("、"));
  }

  @NotNull
  default String toBizLevelDescStr(
      List<BizLevelDivision> bizLevelDivisions, DivisionLevelEnum divisionLevelEnum) {
    return bizLevelDivisions.stream()
        .filter(v -> divisionLevelEnum != null && divisionLevelEnum == v.getLevel())
        .map(BizLevelDivision::getLevelVal)
        .map(DivisionLevelValueEnum::getDesc)
        .collect(Collectors.joining("、"));
  }

  default String cascadedDivisionLevelsToDescStr(
      List<CascadedDivisionLevel> cascadedDivisionLevels) {
    return cascadedDivisionLevels.stream()
        .collect(Collectors.groupingBy(v -> v.getValue().getDivisionLevel()))
        .entrySet()
        .stream()
        .sorted(Comparator.comparing(it -> it.getKey().getValue()))
        .map(
            g -> {
              DivisionLevelEnum key = g.getKey();
              return key.getDesc()
                  + ": "
                  + g.getValue().stream()
                      .map(
                          v -> {
                            if (CollUtil.isNotEmpty(v.getSubValues())) {
                              return v.getValue().getDesc() + "｜" + v.getSubValues().stream()
                                  .map(vv -> vv.getValue().getDesc())
                                  .collect(Collectors.joining("、"));
                            }
                            return v.getValue().getDesc();
                          })
                      .collect(Collectors.joining("，"));
            })
        .collect(Collectors.joining("; "));
  }

  default String corpBizTypeListToDescStr(List<CorpBizTypeDTO> corpBizTypeDTOS) {
    return corpBizTypeDTOS.stream()
        .sorted(Comparator.comparing(CorpBizTypeDTO::getCorpType))
        .map(
            v -> {
              final DivisionLevelValueEnum divisionLevelValueEnum =
                  DivisionLevelValueEnum.valueOf(v.getCorpType());
              return v.getBizType().stream()
                  .map(DivisionLevelValueEnum::valueOf)
                  .map(vv -> divisionLevelValueEnum.getDesc() + "-" + vv.getDesc())
                  .collect(Collectors.joining("、"));
            })
        .collect(Collectors.joining("、"));
  }

  default List<CorpBizTypeDTO> pSysCooperatorToCorpBizType(
      List<Integer> cooperator, List<Integer> businessType) {
    final ArrayList<CorpBizTypeDTO> bizTypeDTOS = new ArrayList<>();
    if (cooperator != null && !cooperator.isEmpty()) {
      for (Integer coop : cooperator) {
        final PSysCooperatorEnum pSysCooperatorEnum = PSysCooperatorEnum.valueOf(coop);
        if (pSysCooperatorEnum == null) {
          continue;
        }
        final DivisionLevelValueEnum divisionLevelValueEnum =
            DivisionLevelValueEnum.mapFromPSysCooperatorEnum(pSysCooperatorEnum);
        if (divisionLevelValueEnum == null) {
          continue;
        }
        final CorpBizTypeDTO corpBizTypeDTO = new CorpBizTypeDTO();
        corpBizTypeDTO.setCorpType(divisionLevelValueEnum.getValue());
        final ArrayList<Integer> bizType = new ArrayList<>();
        corpBizTypeDTO.setBizType(bizType);
        if (businessType != null && !businessType.isEmpty()) {
          for (Integer biz : businessType) {
            final PSysBusinessTypeEnum pSysBusinessTypeEnum = PSysBusinessTypeEnum.valueOf(biz);
            if (pSysBusinessTypeEnum != null
                && pSysBusinessTypeEnum.getCooperator() == pSysCooperatorEnum) {
              final DivisionLevelValueEnum bizTypeEnum =
                  DivisionLevelValueEnum.mapFromPSysBusinessTypeEnum(pSysBusinessTypeEnum);
              bizType.add(bizTypeEnum.getValue());
            }
          }
        }
        bizTypeDTOS.add(corpBizTypeDTO);
      }
    }
    return bizTypeDTOS;
  }
}
