package com.daddylab.supplier.item.application.category;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.controller.category.dto.CategoryCmd;
import com.daddylab.supplier.item.controller.category.dto.CategoryQueryCmd;
import com.daddylab.supplier.item.controller.category.dto.CategoryTree;
import com.daddylab.supplier.item.controller.category.dto.CategoryVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CategoryAttr;
import org.apache.commons.collections4.MultiSet;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/27 11:48 上午
 * @description
 */
public interface CategoryBizService {

    /**
     * 创建品类
     *
     * @param categoryCmd 创建品类，请求实体
     */
    SingleResponse<Long> add(CategoryCmd categoryCmd);

    /**
     * 修改品类，仅仅修改品类名称
     *
     * @param id
     * @param name
     */
    Response update(Long id, String name);

    /**
     * 查询品类列表
     *
     * @param categoryPageQuery
     * @return
     */
    MultiResponse<CategoryVo> queryList(CategoryQueryCmd categoryPageQuery);

    /**
     * 删除品类
     *
     * @param id
     */
    Response delete(Long id);

    /**
     * 展示品类销售属性
     * 如果parentId不为空，则展示父类和子类共有的属性。
     *
     * @param categoryId
     * @return
     */
    MultiResponse<CategoryAttr> getAttr(Long categoryId);

    /**
     * 修改属性
     *
     * @param attrId 属性id
     * @param name
     */
    Response updateAttr(Long attrId, String name);

    /**
     * 添加属性
     *
     * @param id
     * @param name
     */
    Response addAttr(Long id, String name);

    /**
     * 删除属性
     * @param id
     */
    Response deleteAttr(Long id);

    SingleResponse<CategoryTree> categoryTree();

    /**
     * 缓存驱逐
     */
    Response cacheEvictForCategoryTree();

    /**
     * 重置类目路径
     */
    Response resetPath();

    SingleResponse<MultiSet<String>> migrateItem(Long fromCategoryId, Long toCategoryId);

    Response modifyItemCategory(Long itemId, Long newCategoryId);
}
