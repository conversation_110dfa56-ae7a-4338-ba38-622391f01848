package com.daddylab.supplier.item.controller.purchase.dto;

import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName PurchaseVo.java
 * @description
 * @createTime 2021年11月11日 16:05:00
 */
@Data
@ApiModel("采购价格返回实体")
public class PurchaseVo implements Serializable {

    private static final long serialVersionUID = 2080418614734726139L;


    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 商品sku
     */
    @ApiModelProperty(value = "商品sku")
    private String itemSku;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    /**
     * 商品规格
     */
    @ApiModelProperty(value = "商品规格")
    private String itemSpecs;

    /**
     * 采购地点
     */
    @ApiModelProperty(value = "采购地点0:工厂 1:仓库")
    private Integer purchaseArea;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String provider;

    /**
     * 采购负责人
     */
    @ApiModelProperty(value = "采购负责人")
    private Long buyerId;

    /**
     * 日常供价(无优惠)
     */
    @ApiModelProperty(value = "日常供价(无优惠)")
    private BigDecimal usualPrice;

    /**
     * 规格数量
     */
    @ApiModelProperty(value = "规格数量")
    private Long specsCount;

    /**
     * 是否纯活动商品 0:否 1:是
     */
    @ApiModelProperty(value = "是否纯活动商品 0:否 1:是")
    private Integer isActive;

    /**
     * 优惠类型 0:货抵款 1:按时间供价 2:当月优惠件数
     */
    @ApiModelProperty(value = "优惠类型 0:货抵款 1:按时间供价 2:当月优惠件数")
    private Integer favourableType;

    /**
     * 平台名称 0:ALL 1:淘宝 2:抖店 3:小红书 4:自研电商 5:有赞 6:快手
     */
    @ApiModelProperty(value = "平台名称 0:ALL 1:淘宝 2:抖店 3:小红书 4:自研电商 5:有赞 6:快手")
    private Integer platformType;

    /**
     * 方式 0:大促 1:直播 2:无
     */
    @ApiModelProperty(value = "方式 0:大促 1:直播 2:无")
    private Integer activeType;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    private Long startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    private Long endTime;

    /**
     * 订单拍下份数
     */
    @ApiModelProperty(value = "订单拍下份数")
    private Long orderCount;

    /**
     * 实发单品数量
     */
    @ApiModelProperty(value = "实发单品数量")
    private Long finalCount;

    /**
     * 按价格优惠结算成本/元
     */
    @ApiModelProperty(value = "按价格优惠结算成本/元")
    private BigDecimal priceCost;

    /**
     * 按数量优惠结算成本/元
     */
    @ApiModelProperty(value = "按数量优惠结算成本/元")
    private BigDecimal numCost;

    /**
     * 供价优惠内容
     */
    @ApiModelProperty(value = "供价优惠内容")
    private String content;

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明")
    private String mark;

    /**
     * 0:未发起确认 1:已发起确认
     */
    @ApiModelProperty(value = "发起确认")
    private Integer isConfirm;

    /**
     * 0:待确认 1:已确认 2:存在异议
     */
    @ApiModelProperty(value = "0:待确认 1:已确认 2:存在异议")
    private Integer status;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;

    /**
     * 是否可以编辑
     */
    @ApiModelProperty(value = "是否可以编辑")
    private Integer isEdit;

    /**
     * 采购人姓名
     */
    @ApiModelProperty(value = "采购人姓名")
    private String buyerName;

    /**
     * 短链地址
     */
    @ApiModelProperty(value = "短链地址")
    private String shortLink;

    /**
     * md5
     */
    private String md5;

//    @ApiModelProperty(value = "合作方。0:电商。1:绿色家装。")
//    private Integer businessLine;

    /**
     * DB：0:sku日常价。1:sku活动价。2:spu日常价。3:spu活动价
     * VO：0日常阶梯供价。1活动阶梯供价。
     */
    @ApiModelProperty(value = "价格类型。1:sku日常阶梯价。2:sku活动阶梯价")
    private Integer priceType;


//    private String coryTypeStr;
//
//    private String bizTypeStr;

    @ApiModelProperty(value = "合作方/业务线 结构化数据")
    private List<CorpBizTypeDTO> corpBizType;

    @ApiModelProperty(value = "合作方 层级")
    private List<Integer> corpType;

    @ApiModelProperty(value = "业务类型 层级")
    private List<Integer> bizType;

}
