package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSkuInventorySetting;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <p>
 * 平台商品SKU库存设置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface PlatformItemSkuInventorySettingMapper extends DaddyBaseMapper<PlatformItemSkuInventorySetting> {

    int saveOrUpdateBatchByOuterId(@Param("models") Collection<PlatformItemSkuInventorySetting> models);
}
