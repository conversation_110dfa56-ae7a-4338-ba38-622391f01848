package com.daddylab.supplier.item.application.afterSales;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesReceiveResult;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.annotation.Nullable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/21
 */
@Data
public class AfterSalesReceiveCmd {


    /**
     * 关联的退换单号
     */
    @ApiModelProperty("退换单号")
    @NotBlank(message = "退换单号不能为空")
    private String returnOrderNo;

    /**
     * 退货仓库编号
     */
    @ApiModelProperty("退货仓库编号")
    @NotBlank(message = "退货仓库编号不能为空")
    private String returnWarehouseNo;

    /**
     * 收货结果 1:货品完好 2:微损可入库 3:破损不可入库 4:数量不一致 5:商品不一致
     */
    @ApiModelProperty("收货结果 1:货品完好 2:微损可入库 3:破损不可入库 4:数量不一致 5:商品不一致")
    @NotNull(message = "收货结果不能为空")
    private AfterSalesReceiveResult receiveResult;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @Nullable
    private String remark;

    @ApiModelProperty("图片列表")
    @Size(min = 1, max = 50, message = "请上传图片（数量限制{min}-{max}张）")
    private List<String> imageUrls;
}
