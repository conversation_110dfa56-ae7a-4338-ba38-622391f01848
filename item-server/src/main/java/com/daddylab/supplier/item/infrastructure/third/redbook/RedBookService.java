package com.daddylab.supplier.item.infrastructure.third.redbook;

import com.daddylab.supplier.item.infrastructure.third.base.IShopAuthorizationAware;
import com.daddylab.supplier.item.infrastructure.third.redbook.domain.dto.RedBookMessageDTO;
import com.daddylab.supplier.item.infrastructure.third.redbook.domain.query.RedBookItemPageQuery;
import com.daddylab.supplier.item.infrastructure.third.redbook.domain.query.SkuPageQuery;
import com.xiaohongshu.fls.opensdk.entity.inventory.response.SkuStockResponse;
import com.xiaohongshu.fls.opensdk.entity.product.response.v3.GetDetailSkuListResponse;
import com.xiaohongshu.fls.opensdk.entity.product.response.v3.GetItemInfoResponse;
import com.xiaohongshu.fls.opensdk.entity.product.response.v3.SearchItemListResponse;

/**
 * <AUTHOR>
 * @class RedBookService.java
 * @description 小红书服务类
 * @date 2024-02-28 10:56
 * @see <a href="https://open.xiaohongshu.com/document/api?apiNavigationId=221&id=63&gatewayId=103&gatewayVersionId=1661&apiId=27228">
 *     小红书字段说明文档</a>
 */
public interface RedBookService extends IShopAuthorizationAware {

    /**
     * 获取sku的分页列表
     *
     * @param itemPageQuery ItemPageQuery
     * @return void
     * @date 2024/2/28 16:28
     * <AUTHOR>
     */
    SearchItemListResponse getItemPageList(RedBookItemPageQuery itemPageQuery);

    /**
     * 获取sku的分页列表
     *
     * @param pageQuery SkuPageQuery
     * @return void
     * @date 2024/2/28 16:28
     * <AUTHOR>
     */
    GetDetailSkuListResponse getSkuPageList(SkuPageQuery pageQuery);


    /**
     * 获取商品详情
     *
     * @param itemId String
     * @return com.xiaohongshu.fls.opensdk.entity.product.response.v3.GetItemInfoResponse
     * @date 2024/2/29 09:43
     * <AUTHOR>
     */
    GetItemInfoResponse getItemInfo(String itemId);


    /**
     * 获取商品库存
     *
     * @param skuId String
     * @return com.xiaohongshu.fls.opensdk.entity.inventory.response.SkuStockResponse
     * @date 2024/2/29 09:47
     * <AUTHOR>
     */
    SkuStockResponse getSkuStock(String skuId);

    /**
     * 同步sku库存
     *
     * @param skuId String
     * @param num Integer 商品总库存数 = 可售库存 + 独立库存 + 保留库存， 接口逻辑会用这个数 - 独立库存 - 保留库存，去设置可售库存；
     * @return void
     * @date 2024/2/29 09:52
     * <AUTHOR>
     */
    SkuStockResponse syncSkuStock(String skuId, Integer num);

    /**
     * 增减sku库存
     *
     * @param skuId String
     * @param num Integer 增减库存数, 操作可售库存的增减(增为正,减为负)
     * @return void
     * @date 2024/2/29 09:52
     * <AUTHOR>
     */
    SkuStockResponse incSkuStock(String skuId, Integer num);

    /**
     * 小红书回调消息处理
     *
     * @param message RedBookMessageDTO
     * @date 2024/2/29 17:42
     * <AUTHOR>
     */
    void handle(RedBookMessageDTO message);

}
