package com.daddylab.supplier.item.application.drawer;

import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerImageTypeEnum;
import java.util.List;
import java.util.Optional;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/9/26
 */
public interface ItemDrawerImportMallGateway {

    boolean isMatchItem(String itemCode, Long itemId);

    List<Long> getItemIds(String itemCode);

    Optional<Long> getItemDrawerId(Long itemId);

    Optional<Long> mapAttrValueToId(String itemCode, String attrName, String attrValue);

    void saveBatchItemDrawerAttrImages(Long itemDrawerId, List<Image> images);

    void saveBatchItemDrawerMainImages(Long itemDrawerId, List<Image> images);

    void saveBatchItemDrawerMainVideo(Long itemDrawerId, String url, String fileName,
            String cover);

    void saveBatchItemDrawerDetailImages(Long itemDrawerId, List<Image> images);

    boolean hasItemDrawerImage(Long itemDrawerId, ItemDrawerImageTypeEnum typeEnum);

    void updateItemDrawerVideoCover(Long itemDrawerId, String url, String cover);

    /**
     * <AUTHOR>
     * @since 2022/9/30
     */
    @Data
    class Image {

        private final String url;
        private final String fileName;
        private final Long itemAttrId;


        public static Image of(String url) {
            return new Image(url, null, null);
        }

        public static Image of(String url, String fileName) {
            return new Image(url, fileName, null);
        }

        public static Image of(String url, String fileName, Long itemAttrId) {
            return new Image(url, fileName, itemAttrId);
        }
    }
}
