package com.daddylab.supplier.item.application.afterSaleLink.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年05月11日 4:49 PM
 */
@Data
public class AfterSaleShareLinkViewVO{

    private Long id;

    @ApiModelProperty("0正常，1禁用")
    private Integer status;

    @ApiModelProperty("链接名字")
    private String name;

    @ApiModelProperty("链接有效期。天")
    private Integer validityPeriod;

    @ApiModelProperty("0存在有效期，1长期有效")
    @NotNull(message = "是否长期有效标识不得为空")
    private Integer alwaysValidity;

    @ApiModelProperty("手机号码。英文逗号分割")
    private List<Long> telList;

    @ApiModelProperty("链接值")
    private String linkVal;

    @ApiModelProperty("创建人")
    private String createdName;

    @ApiModelProperty("创建时间")
    private String createdAt;

}
