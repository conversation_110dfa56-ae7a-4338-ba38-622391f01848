package com.daddylab.supplier.item.infrastructure.qimen.wdt;

import cn.hutool.core.util.ReflectUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.daddylab.supplier.item.infrastructure.qimen.QimenConfig;
import com.qimencloud.api.QimenCloudClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/4/15
 */
@Component
public class QimenApiFactory {

    private final QimenCloudClient qimenClient;
    private final QimenConfig qimenConfig;

    public QimenApiFactory(QimenCloudClient qimenClient,
            QimenConfig qimenConfig) {
        this.qimenClient = qimenClient;
        this.qimenConfig = qimenConfig;
    }

    @SuppressWarnings("unchecked")
    public <T> T getWdtAPIQimenImpl(
            Class<? extends WdtAPIQimenImplBase> apiImplClass,
            WdtConfig wdtConfig, int configIndex) {
        return (T) ReflectUtil
                .newInstance(apiImplClass, qimenClient, qimenConfig, wdtConfig, configIndex);
    }
}
