package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 单sku纬度的组合采购价格信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PurchaseSingleSkuCombinationPrice extends SkuCombinationPrice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 来源。1：单品价格;
     * 2：组合装价格(2022-11暂时没有组合装的情况)
     */
    private Integer source;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;


}
