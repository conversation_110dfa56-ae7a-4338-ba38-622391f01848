package com.daddylab.supplier.item.application.stockSpec;

import com.daddylab.supplier.item.types.stockSpec.ShopAvailableStockQuery;
import com.daddylab.supplier.item.types.stockSpec.ShopAvailableStockSpecVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/15
 * @deprecated
 */
public interface ShopStockSpecBizService {

    List<ShopAvailableStockSpecVO> shopAvailableStockQuery(ShopAvailableStockQuery query);
}
