package com.daddylab.supplier.item.application.ItemTrainingMaterials;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/16
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "item-training-materials-config")
@RefreshScope
public class ItemTrainingMaterialsConfig {
    /**
     * 流程定义 key
     */
    private String processDefKey = "item_training_materials_0.0.1";
    /**
     * 法务负责人候选
     */
    private List<Long> legalCandidates = Collections.emptyList();
    /**
     * QC负责人候选
     */
    private List<Long> qcCandidates = Collections.emptyList();
    /**
     * QC主管
     */
    private List<Long> qcSupervisors = Collections.emptyList();
    /**
     * 修改负责人候选
     */
    private List<Long> modifyCandidates = Collections.emptyList();
    /**
     * 指定客服
     */
    private List<Long> csCandidates = Collections.emptyList();
    /**
     * 打开流程状态修复开关
     */
    private boolean fixStatus;
}
