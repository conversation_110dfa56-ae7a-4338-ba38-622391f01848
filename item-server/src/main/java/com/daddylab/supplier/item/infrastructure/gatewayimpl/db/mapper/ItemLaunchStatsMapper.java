package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchStats;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <p>
 * 商品上新统计数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
public interface ItemLaunchStatsMapper extends DaddyBaseMapper<ItemLaunchStats> {

    ItemLaunchStats selectOneByItemId(@Param("itemId") Long itemId);

    int undelete(Collection<Long> ids);
}
