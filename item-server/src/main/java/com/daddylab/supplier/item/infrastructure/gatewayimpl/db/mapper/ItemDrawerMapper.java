package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;

/**
 * <p>
 * 商品抽屉表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
public interface ItemDrawerMapper extends DaddyBaseMapper<ItemDrawer> {

    Integer getItemLaunchStatus(Long itemDrawerId);

}
