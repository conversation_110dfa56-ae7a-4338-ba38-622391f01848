package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.TimeScheduleType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 时间调度表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TimeSchedule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 类型
     */
    private TimeScheduleType type;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 计划调度时间
     */
    private LocalDateTime scheduleTime;

    /**
     * 是否完成 0:未完成 1:完成
     */
    private Boolean isComplete;

    /**
     * 实际调度时间
     */
    private LocalDateTime actualTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;


}
