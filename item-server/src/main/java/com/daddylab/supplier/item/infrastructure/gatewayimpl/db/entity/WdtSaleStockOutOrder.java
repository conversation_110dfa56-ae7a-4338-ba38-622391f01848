package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 旺店通销售出库单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtSaleStockOutOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 出库单ID
     */
    private Long stockoutId;

    /**
     * 出库单号
     */
    private String orderNo;

    /**
     * ERP系统订单编号
     */
    private String srcOrderNo;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 出库时间，例如：2020-09-23 14:56:18
     */
    private LocalDateTime consignTime;

    /**
     * 1销售订单
     */
    private Integer orderType;

    /**
     * 货品数量
     */
    private BigDecimal goodsCount;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 收件人姓名 （ 仅自有平台 及线下平台返回，其他平台均不返回 ）
     */
    private String receiverName;

    /**
     * 国家
     */
    private Integer receiverCountry;

    /**
     * 省份ID，点击查看 城市代码表
     */
    private Integer receiverProvince;

    /**
     * 城市ID，点击查看 城市代码表
     */
    private Integer receiverCity;

    /**
     * 地区ID，点击查看 城市代码表
     */
    private Integer receiverDistrict;

    /**
     * 地址，不包含省市区， （ 仅自有平台 及线下平台返回，其他平台均不返回 ）
     */
    private String receiverAddress;

    /**
     * 收件人手机 （ 仅自有平台 及线下平台返回，其他平台均不返回 ）
     */
    private String receiverMobile;

    /**
     * 收件人固话 （ 仅自有平台 及线下平台返回，其他平台均不返回 ）
     */
    private String receiverTelno;

    /**
     * 收件人邮编
     */
    private String receiverZip;

    /**
     * 省市区空格分隔
     */
    private String receiverArea;

    /**
     * 出库单备注
     */
    private String remark;

    /**
     * 实际称得重量KG
     */
    private BigDecimal weight;

    /**
     * 截停原因: 0正常 1申请退款 2已退款 4地址被修改 8发票被修改 16物流被修改 32仓库变化 64备注修改 128更换货品 256取消退款
     */
    private Integer blockReason;

    /**
     * 物流方式，点击 物流代码表 查看
     */
    private Integer logisticsType;

    /**
     * ERP物流编号（查看路径：旺店通客户端设置--基本设置--物流）
     */
    private String logisticsCode;

    /**
     * 物流公司名称
     */
    private String logisticsName;

    /**
     * 店铺id
     */
    private Integer shopId;

    /**
     * 仓库id
     */
    private Integer warehouseId;

    /**
     * 物流id
     */
    private Integer logisticsId;

    /**
     * 异常原因: 0正常 1无库存记录 2地址发生变化 4发票变化 8仓库变化 16备注变化 32平台更换货品 64退款
     */
    private Integer badReason;

    /**
     * 大头笔
     */
    private String receiverDtb;

    /**
     * 退款状态: 0无退款 1申请退款 2部分退款 3全部退款
     */
    private Integer refundStatus;

    /**
     * 销售类型: 1网店销售 2线下零售 3售后换货 4批发业务 7现款销售 8分销订单 101 订单自定义属性1 102 订单自定义属性2 103 订单自定义属性3 104 订单自定义属性4 105 订单自定义属性5 106 订单自定义属性6
     */
    private Integer tradeType;

    /**
     * 业务员为"系统"时不返回
     */
    private String salesmanNo;

    /**
     * 业务员为"系统"时不返回
     */
    private String fullname;

    /**
     * 订单状态: 4线下退款 5已取消 6 待转预订单(待审核) 7 待转已完成 10未付款 12待尾款 15等未付 16延时审核 19预订单前处理 20 审核前处理 21自流转待发货 23 异常预订单 24 换货预订单 25 待处理预订单 27待分配预订单 30待客审 35待财审 55已审核 95已发货 96 成本确认（待录入计划成本，订单结算时有货品无计划成本） 101 已过账 110已完成
     */
    private Integer tradeStatus;

    /**
     * 订单编号
     */
    private String tradeNo;

    /**
     * 原始单号(如果有多个，以","分隔且以增序排列,不重复,过长将被裁剪)
     */
    private String srcTradeNo;

    /**
     * 客户网名 （ 仅自有平台 及线下平台返回，其他平台均不返回 ）
     */
    private String nickName;

    /**
     * 客户编码
     */
    private String customerNo;

    /**
     * 客户姓名 （ 仅自有平台 及线下平台返回，其他平台均不返回 ）
     */
    private String customerName;

    /**
     * 下单时间（毫秒级时间戳，例如：1631861379000）
     */
    private LocalDateTime tradeTime;

    /**
     * 支付时间（毫秒级时间戳，例如：1631861379000）
     */
    private LocalDateTime payTime;

    /**
     * 标记名称
     */
    private String flagName;

    /**
     * 邮费/订单邮费
     */
    private BigDecimal postAmount;

    /**
     * 证件类别
     */
    private Integer idCardType;

    /**
     * 证件号码 （ 仅自有平台 及线下平台返回，其他平台均不返回 ）
     */
    private String idCard;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺编号
     */
    private String shopNo;

    /**
     * 店铺备注
     */
    private String shopRemark;

    /**
     * 出库单状态: 5已取消 10待放回(拣货待放回), 小于该值的都是已取消的单子 51 缺货 53 WMS已接单 54 获取电子面单 60 待分配 61 排队中 63 待补货 65 待处理 70 待发货 73 爆款锁定 75 待拣货 77 拣货中,PDA拣货后 79 已拣货 90 延时发货 110已完成
     */
    private Integer status;

    /**
     * 发票类别: 0不需要 1普通发票 2增值税发票
     */
    private Integer invoiceType;

    /**
     * 发票id: 目前只设0-1， 1表示已开发票
     */
    private Integer invoiceId;

    /**
     * 货到付款金额
     */
    private BigDecimal codAmount;

    /**
     * 发货条件: 1款到发货 2货到付款(包含部分货到付款) 3分期付款
     */
    private Integer deliveryTerm;

    /**
     * 平台ID（请点击 平台代码表 查看对应关系 ）
     */
    private Integer platformId;

    /**
     * 订单ID
     */
    private Integer tradeId;

    /**
     * 审核员编号
     */
    private String employeeNo;

    /**
     * 优惠金额(订单总优惠)
     */
    private BigDecimal discount;

    /**
     * 税额
     */
    private BigDecimal tax;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 币种
     */
    private String currency;

    /**
     * 系统订单建单时间（毫秒级时间戳，例如：1631861379000）
     */
    private LocalDateTime created;

    /**
     * 出库单建单时间（毫秒级时间戳，例如：1631861379000）
     */
    private LocalDateTime stockCheckTime;

    /**
     * 打印备注
     */
    private String printRemark;

    /**
     * 买家留言
     */
    private String buyerMessage;

    /**
     * 客服备注
     */
    private String csRemark;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 发票内容
     */
    private String invoiceContent;

    /**
     * 称重预估邮资 (使用根据重量预估的邮费)
     */
    private BigDecimal postFee;

    /**
     * 包装成本(使用包装的计划成本)
     */
    private BigDecimal packageFee;

    /**
     * 已付金额(使用应收金额)
     */
    private BigDecimal receivable;

    /**
     * 总成本价
     */
    private BigDecimal goodsTotalCost;

    /**
     * 预估货品成本
     */
    private BigDecimal goodsTotalAmount;

    /**
     * 最后修改时间，例如：2021-09-23 15:56:18
     */
    private LocalDateTime modified;

    /**
     * 分销商昵称
     */
    private String fenxiaoNick;

    /**
     * 订单标签
     */
    private String tradeLabel;

    /**
     * 订单来源： 1、API抓单 2、手工建单 3、Excel导入 4、复制订单 5、接口推送 6、补发订单 7、PDA选货开单 8、分销补发订单
     */
    private Integer tradeFrom;

    /**
     * 分拣波次
     */
    private String picklistNo;

    /**
     * 分拣序号
     */
    private Integer picklistSeq;

    /**
     * 0：未打 印 1：打印中 2：已打印 3：无需打印
     */
    private Integer logisticsPrintStatus;

    @TableField(exist = false)
    List<WdtSaleStockOutOrderDetails> detailsList;

    @TableField(exist = false)
    List<WdtSaleStockOutOrderLogistics> logisticsList;


}
