package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataPushLog;

/**
 * <p>
 * 数据推送记录详细日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
public interface DataPushLogMapper extends DaddyBaseMapper<DataPushLog> {

}
