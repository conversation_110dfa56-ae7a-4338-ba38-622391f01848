package com.daddylab.supplier.item.domain.region.entity;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.cola.domain.Entity;
import com.daddylab.supplier.item.domain.region.enums.RegionLevel;
import com.daddylab.supplier.item.domain.region.vo.Region;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Entity
@Slf4j
public class RegionTree {
    /**
     * 东北
     */
    private static final String[] northEast = new String[]{"210000", "220000", "230000"};
    
    /**
     * 华东
     */
    private static final String[] eastChina = new String[]{"310000", "320000", "330000", "340000", "360000"};
    
    /**
     * 华中
     */
    private static final String[] centerChina = new String[]{"410000", "420000", "430000"};
    
    /**
     * 华南
     */
    private static final String[] southChina = new String[]{"350000", "440000", "450000", "460000"};
    
    /**
     * 华北
     */
    private static final String[] northChina = new String[]{"110000", "120000", "130000", "140000", "150000", "370000"};
    
    /**
     * 西北
     */
    private static final String[] northWest = new String[]{"610000", "620000", "630000", "640000", "650000"};
    
    /**
     * 西南
     */
    private static final String[] southWest = new String[]{"500000", "510000", "520000", "530000", "540000"};
    
    /**
     * 顶级区域列表
     */
    @JsonValue
    private final List<Region> rootRegions;
    /**
     * 所有区域的映射表,方便根据code获取区域对象
     */
    private final Map<String, Region> regionsMap;
    /**
     * 按照大区分组
     */
    private List<Region> bigRegions;
    
    public RegionTree(List<Region> regions) {
        rootRegions = new ArrayList<>(32);
        regionsMap = new HashMap<>(regions.size());
        final List<RegionLevel> regionLevels =
                Arrays.asList(RegionLevel.PROVINCE, RegionLevel.CITY, RegionLevel.COUNTY);
        final RegionLevel rootLevel = regionLevels.get(0);
        for (RegionLevel regionLevel : regionLevels) {
            for (Region region : regions) {
                if (regionLevel == rootLevel) {
                    regionsMap.put(region.getCode(), region);
                }
                if (regionLevel == region.getLevel()) {
                    if (regionLevel == rootLevel) {
                        rootRegions.add(region);
                    } else {
                        final Region parentRegion = regionsMap.get(region.getParentCode());
                        if (parentRegion == null) {
                            log.warn("region:{} parent not found", region);
                            continue;
                        }
                        region.setParent(parentRegion);
                        if (parentRegion.getChildren() == null)
                            parentRegion.setChildren(CollectionUtil.newArrayList(region));
                        else parentRegion.getChildren().add(region);
                    }
                }
            }
        }
    }
    
    /**
     * 获取顶级区域列表
     */
    public List<Region> getRootRegions() {
        return rootRegions;
    }
    
    /**
     * 根据地区编码批量获取地区
     */
    public List<Region> getRegionsByCode(String[] codes) {
        return getRegionsByCode(Arrays.asList(codes));
    }
    
    /**
     * 根据地区编码批量获取地区
     */
    public List<Region> getRegionsByCode(List<String> codes) {
        return codes.stream().map(regionsMap::get).collect(Collectors.toList());
    }
    
    /**
     * 根据地区编码获取地区
     */
    public Region getRegion(String code) {
        return regionsMap.get(code);
    }
    
    /**
     * 根据地区编码获取其子区域
     */
    public List<Region> getRegionChildren(String code) {
        return Optional.ofNullable(regionsMap.get(code)).map(Region::getChildren).orElse(Collections.emptyList());
    }
    
    /**
     * 根据名称查询省份
     */
    public Optional<Region> getProvinceByName(String name) {
        for (Region rootRegion : rootRegions) {
            if (StringUtil.endWithAny(name, "省", "市")) {
                name = name.substring(0, name.length() - 1);
            }
            if (StringUtil.contains(rootRegion.getName(), name)) {
                return Optional.of(rootRegion);
            }
        }
        return Optional.empty();
    }
    
    public Optional<Region> areaFuzzyQuery(String address) {
        //浙江省杭州市萧山区瓜沥镇长联村杭州和润实业内
        Optional<Region> region = fuzzyMatch(address, rootRegions);
        while (region.isPresent() && region.get().hasChildren()) {
            final Optional<Region> morePreciseRegion = fuzzyMatch(address, region.get().getChildren());
            if (morePreciseRegion.isPresent()) {
                region = morePreciseRegion;
            } else {
                break;
            }
        }
        if (!region.isPresent()) {
            Collection<Region> allRegions = regionsMap.values();
            region = fuzzyMatch(address, allRegions);
            while (region.isPresent() && region.get().hasChildren()) {
                final Optional<Region> morePreciseRegion = fuzzyMatch(address, region.get().getChildren());
                if (morePreciseRegion.isPresent()) {
                    region = morePreciseRegion;
                } else {
                    break;
                }
            }
        }
        return region;
    }
    
    @NonNull
    private Optional<Region> fuzzyMatch(String address, Collection<Region> regions) {
        for (Region region : regions) {
            final ArrayList<String> keywords = new ArrayList<>();
            String name = region.getName();
            keywords.add(name);
            for (String keyword : new String[]{"省", "市", "区", "县", "街道"}) {
                if (StringUtil.endWithAny(name, keyword)) {
                    String nameWithoutSuffix = name.substring(0, name.length() - keyword.length());
                    if (nameWithoutSuffix.length() > 1) {
                        keywords.add(nameWithoutSuffix);
                    }
                }
            }
            if (keywords.stream().anyMatch(keyword -> StringUtil.contains(address, keyword))) {
                return Optional.of(region);
            }
        }
        return Optional.empty();
    }
    
    /**
     * 根据省市区关键字模糊查找地区对象
     */
    public Optional<Region> areaFuzzyQuery(String province, String city, String area) {
        Optional<Region> provinceRegion = getProvinceByName(province);
        if (!provinceRegion.isPresent()) {
            return Optional.empty();
        }
        
        Optional<Region> cityRegion = provinceRegion.map(region -> region.getChildrenByName(city));
        
        if (cityRegion.isPresent()) {
            return cityRegion.map(region -> region.getChildrenByName(area));
        }
        
        //如果市级存在缺失，尝试查询区/县级，然后倒查市级
        String areaSearch = area;
        if (StringUtil.endWithAny(areaSearch, "区", "县")) {
            areaSearch = areaSearch.substring(0, areaSearch.length() - 1);
        }
        
        for (Region iterCity : provinceRegion.get().getChildren()) {
            
            if (CollUtil.isNotEmpty(iterCity.getChildren())) {
                for (Region iterArea : iterCity.getChildren()) {
                    
                    if (iterArea.getName().contains(areaSearch)) {
                        return Optional.of(iterArea);
                    }
                }
            }
        }
        return Optional.empty();
    }
    
    /**
     * 返回大区列表
     */
    public List<Region> getBigRegions() {
        if (bigRegions != null) {
            return bigRegions;
        }
        bigRegions = new ArrayList<>(8);
        // 东北
        bigRegions.add(new Region()
                .setLevel(RegionLevel.BIG_REGION)
                .setCode("1")
                .setName("东北")
                .setChildren(getRegionsByCode(northEast)));
        // 华东
        bigRegions.add(new Region()
                .setLevel(RegionLevel.BIG_REGION)
                .setCode("2")
                .setName("华东")
                .setChildren(getRegionsByCode(eastChina)));
        // 华中
        bigRegions.add(new Region()
                .setLevel(RegionLevel.BIG_REGION)
                .setCode("3")
                .setName("华中")
                .setChildren(getRegionsByCode(centerChina)));
        // 华南
        bigRegions.add(new Region()
                .setLevel(RegionLevel.BIG_REGION)
                .setCode("4")
                .setName("华南")
                .setChildren(getRegionsByCode(southChina)));
        // 华北
        bigRegions.add(new Region()
                .setLevel(RegionLevel.BIG_REGION)
                .setCode("5")
                .setName("华北")
                .setChildren(getRegionsByCode(northChina)));
        // 西北
        bigRegions.add(new Region()
                .setLevel(RegionLevel.BIG_REGION)
                .setCode("6")
                .setName("西北")
                .setChildren(getRegionsByCode(northWest)));
        // 西南
        bigRegions.add(new Region()
                .setLevel(RegionLevel.BIG_REGION)
                .setCode("7")
                .setName("西南")
                .setChildren(getRegionsByCode(southWest)));
        // 港澳台（目前不提供服务）
        bigRegions.add(new Region()
                .setLevel(RegionLevel.BIG_REGION)
                .setCode("8")
                .setName("港澳台")
                .setChildren(Collections.emptyList()));
        return bigRegions;
    }
}
