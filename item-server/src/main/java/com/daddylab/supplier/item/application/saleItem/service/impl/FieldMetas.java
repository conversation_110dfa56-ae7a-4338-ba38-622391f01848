package com.daddylab.supplier.item.application.saleItem.service.impl;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/6/7
 */
@Data
public class FieldMetas {

    private Map<String, FieldMeta> fieldMetas = new HashMap<>();

    public FieldMetas add(FieldMeta fieldMeta) {
        fieldMetas.put(fieldMeta.getProp(), fieldMeta);
        return this;
    }

    public Optional<FieldMeta> get(String propName) {
        return Optional.ofNullable(fieldMetas.get(propName));
    }

}
