package com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.assembler;

import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse.Order;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse.Order.SwapOrder;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse.Order.SwapOrder.SwapOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrderAmountDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSwapOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSwapOrderDetail;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2022/4/20
 */
@Mapper(uses = CommonAssembler.class)
public interface RefundOrderAssembler {

    RefundOrderAssembler INST = Mappers.getMapper(RefundOrderAssembler.class);

    @Mapping(target = "id", ignore = true)
    WdtRefundOrder toWdtRefundOrderPO(Order refundOrder);

    @Mapping(target = "id", ignore = true)
    WdtRefundOrderDetail toWdtRefundOrderDetailPO(String refundNo,
            Order.Detail refundOrderDetail);

    @Mapping(target = "id", ignore = true)
    WdtRefundOrderAmountDetail toWdtRefundOrderAmountDetailPO(String refundNo,
            Order.AmountDetail refundOrderAmountDetail);

    @Mapping(target = "id", ignore = true)
    WdtSwapOrder toWdtSwapOrderPO(String refundNo, SwapOrder swapOrder);

    @Mapping(target = "id", ignore = true)
    WdtSwapOrderDetail toWdtSwapOrderDetailPO(String refundNo, String tid,
            SwapOrderDetail swapOrderDetail);

}
