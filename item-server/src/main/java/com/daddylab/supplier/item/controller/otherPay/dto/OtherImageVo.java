package com.daddylab.supplier.item.controller.otherPay.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @ClassName OtherImageVo.java
 * @description
 * @createTime 2022年04月25日 17:18:00
 */
@Data
@ApiModel("其他凭证封装")
@AllArgsConstructor
@NoArgsConstructor
public class OtherImageVo {

    /**
     * 文件url
     */
    private String url;

}
