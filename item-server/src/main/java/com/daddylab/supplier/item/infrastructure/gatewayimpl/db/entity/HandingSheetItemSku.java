package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.daddylab.supplier.item.types.handingsheet.HandingSheetItemSkuPersistData;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 盘货表SKU纬度数据（V2.4.5新版盘货表）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class HandingSheetItemSku implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 盘货表ID
     */
    private Long handingSheetId;

    /**
     * 盘货表 item spu ID
     */
    private Long handingSheetItemSpuId;

    /**
     * 外部平台商品SKU编码
     */
    private String skuCode;

    /**
     * 后端商品SKU ID
     */
    private Long skuId;

    /**
     * 是否审核通过
     */
    private Integer isPassed;

    /**
     * 盘货表商品SKU纬度数据模型
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private HandingSheetItemSkuPersistData data;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人uid
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人uid
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除：0否1是
     */
    @TableLogic
    private Integer isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 采购员ID，生成列，仅用于查询
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Long buyerId;


}
