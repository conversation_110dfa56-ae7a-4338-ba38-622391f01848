package com.daddylab.supplier.item.application.fold.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName FoldVo.java
 * @description
 * @createTime 2022年06月07日 17:07:00
 */
@Data
@ApiModel("折叠返回实体")
public class FoldVo implements Serializable {

    private static final long serialVersionUID = -3652828560609799890L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "类型 1:新品商品")
    private Integer type;

    @ApiModelProperty(value = "列名")
    private String field;

    @ApiModelProperty(value = "是否折叠 0:否 1:是")
    private Integer status;
}
