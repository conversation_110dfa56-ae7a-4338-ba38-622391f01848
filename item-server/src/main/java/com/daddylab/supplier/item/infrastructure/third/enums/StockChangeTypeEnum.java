package com.daddylab.supplier.item.infrastructure.third.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @class StockChangeTypeEnum.java
 * @description 描述类的作用
 * @date 2024-02-29 11:50
 */
@AllArgsConstructor
@Getter
public enum StockChangeTypeEnum implements IEnum<Integer> {
    INCREASE(1, "增加"),
    DECREASE(2,"减少")
    ;

    private Integer value;
    private String name;
}
