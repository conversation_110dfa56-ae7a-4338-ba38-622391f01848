package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.afterSales.AbnormalExportSheet;
import com.daddylab.supplier.item.application.afterSales.AfterSalesPageQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.AfterSalesLogisticsDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.AfterSalesPageDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesAbnormalInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 售后异常件信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-17
 */
public interface AfterSalesAbnormalInfoMapper extends DaddyBaseMapper<AfterSalesAbnormalInfo> {

    List<AfterSalesLogisticsDO> pageQueryOrderByCreatedAt(AfterSalesPageQuery pageQuery);

    List<AfterSalesPageDO> pageQueryOrderByLogisticsNo(@Param("logisticsNoList") List<String> logisticsNoList);

    List<AbnormalExportSheet> exportList(AfterSalesPageQuery pageQuery);

    Integer exportCount(AfterSalesPageQuery pageQuery);

}
