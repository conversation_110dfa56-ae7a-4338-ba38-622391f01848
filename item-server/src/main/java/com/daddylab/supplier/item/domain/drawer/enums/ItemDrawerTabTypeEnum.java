package com.daddylab.supplier.item.domain.drawer.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @class ItemDrawerTabTypeEnum.java
 * @description 描述类的作用
 * @date 2024-04-11 14:27
 */
@Getter
@AllArgsConstructor
public enum ItemDrawerTabTypeEnum implements IEnum<Integer> {

    ITEM_INFO(1, ItemAuditType.ITEM_MATERIAL),
    LIVE_VERBAL(2, ItemAuditType.LIVE_VERBAL_TRICK),
    TRAINING_MATERIAL(3, ItemAuditType.TRAINING_MATERIAL),

    ;
    private final Integer value;

    private final ItemAuditType auditType;

    public static ItemDrawerTabTypeEnum of(Integer value) {
        for (ItemDrawerTabTypeEnum itemDrawerTabTypeEnum : ItemDrawerTabTypeEnum.values()) {
            if (itemDrawerTabTypeEnum.getValue().equals(value)) {
                return  itemDrawerTabTypeEnum;
            }
        }
        return null;
    }
}
