package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerRecognitionTask;

/**
 * <p>
 * 抽屉图片表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
public interface IItemDrawerRecognitionTaskService extends IDaddyService<ItemDrawerRecognitionTask> {

}
