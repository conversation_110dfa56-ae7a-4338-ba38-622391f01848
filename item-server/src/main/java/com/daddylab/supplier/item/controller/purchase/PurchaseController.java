package com.daddylab.supplier.item.controller.purchase;

import cn.hutool.http.HttpUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.purchase.PurchaseBizService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.dto.IdsCmd;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseCmd;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseQueryPage;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseVo;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseDetail;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseItem;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseOperate;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.utils.HttpHeaderUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

/**
 * <AUTHOR>
 * @ClassName purchaseController.java
 * @description 采购确认
 * @createTime 2021年11月11日 14:15:00
 */
@Slf4j
@Api(value = "采购相关API", tags = "采购相关API")
@RestController
@RequestMapping("/purchase")
public class PurchaseController {

    @Autowired
    private PurchaseBizService purchaseBizService;


    @ResponseBody
    @ApiOperation(value = "搜索查看采购列表")
    @PostMapping("/viewList")
    public PageResponse<PurchaseVo> viewList(@RequestBody PurchaseQueryPage purchaseQueryPage) {
        return purchaseBizService.queryPage(purchaseQueryPage);
    }

    @ResponseBody
    @ApiOperation(value = "修改采购")
    @PostMapping("/createOrUpdatePurchase")
    public Response update(@RequestBody @Validated PurchaseCmd cmd) {
        return purchaseBizService.createOrUpdatePurchase(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "删除采购")
    @PostMapping("/delete/{id}")
    @Auth(resource = "/purchase/delete")
    public Response delete(@ApiParam("采购id") @PathVariable("id") Long id) {
        return purchaseBizService.delete(id);
    }

    @ResponseBody
    @ApiOperation(value = "采购排序")
    @PostMapping("/doSort")
    public Response doSort(@RequestBody @Valid IdsCmd idsCmd) {
        return purchaseBizService.doSort(idsCmd.getIds());
    }

    @ResponseBody
    @ApiOperation(value = "查看凭证")
    @GetMapping("/getAccess")
    public SingleResponse<PurchaseDetail> getAccess(@ApiParam("采购id") Long id) {
        return purchaseBizService.getAccess(id);
    }

    @ResponseBody
    @ApiOperation(value = "重置修改")
    @PostMapping("/reset/{id}")
    @Auth(resource = "/purchase/reset")
    public Response reset(@ApiParam("采购id") @PathVariable("id") Long id) {
        return purchaseBizService.reset(id);
    }

    @ResponseBody
    @ApiOperation(value = "查看采购数据详情")
    @GetMapping("/getPurchaseDetail")
    public MultiResponse<PurchaseDetail> selectById(@ApiParam("采购id") Long id) {
        return purchaseBizService.getPurchaseDetail(id);
    }

    @ResponseBody
    @ApiOperation(value = "查看待确认商品")
    @GetMapping("/getStayConfirm")
    public MultiResponse<PurchaseDetail> getStayConfirm(String ids) {
        return purchaseBizService.getStayConfirm(ids);
    }

    @ResponseBody
    @ApiOperation(value = "发起供应商确认")
    @PostMapping("/initiate")
    public Response initiate(@RequestBody @Valid IdsCmd idsCmd) {
        return purchaseBizService.initiate(idsCmd.getIds());
    }

    @PostMapping(value = "/exportPurchaseList")
    @ApiOperation("采购导出")
    public SingleResponse<Long> exportPurchaseList(@RequestBody PurchaseQueryPage purchaseQueryPage) {
        return SingleResponse.of(purchaseBizService.exportExcel(purchaseQueryPage));
    }

    @GetMapping(value = "/excelTemplate")
    @ApiOperation("下载Excel模板")
    @Auth(noAuth = true)
    public void excelTemplate(HttpServletResponse response) {
        try {
            HttpHeaderUtil.setXlsxAttachmentHeaders(response, "采购导入模板");
            HttpUtil.download(purchaseBizService.getExcelTemplateUrl(), response.getOutputStream(), false);
        } catch (IOException e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.FILE_UPLOAD_ERROR, "下载文件失败，输出流写入异常");
        }
    }

    @PostMapping(value = "/importExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("导入Excel")
    public SingleResponse<PurchaseOperate> importExcel(@ApiParam(name = "file", value = "文件", required = true)
                                                       @RequestParam("file") MultipartFile file) {
        try {
            return SingleResponse.of(purchaseBizService.importExcel(file.getInputStream()));
        } catch (IOException e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.FILE_UPLOAD_ERROR, "导入Excel失败，读取文件异常");
        }
    }


    @ResponseBody
    @ApiOperation(value = "查看供应商列表")
    @GetMapping("/providerList")
    public MultiResponse<String> getProviderList(String month, String provider) {
        return purchaseBizService.getProviderList(month, provider);
    }

    @ResponseBody
    @ApiOperation(value = "根据商品sku查相关数据")
    @GetMapping("/getDataByItemSku")
    public SingleResponse<PurchaseItem> getDataByItemSku(String itemSku) {
        return purchaseBizService.getDataByItemSku(itemSku);
    }


    @ResponseBody
    @ApiOperation(value = "清洗历史采购价格记录的合作模式字段")
    @GetMapping("/clearHistoryDataBusinessLine")
    @Auth(noAuth = true)
    public SingleResponse<Boolean> clearHistoryDataBusinessLine() {
        return purchaseBizService.clearHistoryDataBusinessLine();
    }


}
