package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryAlloc;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.InventoryAllocMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IInventoryAllocService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 库存分配 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
public class InventoryAllocServiceImpl
        extends DaddyServiceImpl<InventoryAllocMapper, InventoryAlloc>
        implements IInventoryAllocService {
    
    @Override
    public List<InventoryAlloc> listByPlatformItemId(Long platformItemId) {
        if (platformItemId == null || platformItemId <= 0) {
            return Collections.emptyList();
        }
        return lambdaQuery().eq(InventoryAlloc::getPlatformItemId, platformItemId).list();
    }
    
    @Override
    public List<InventoryAlloc> listBySkuCode(Collection<String> skuCode) {
        if (CollUtil.isEmpty(skuCode)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(InventoryAlloc::getSkuCode, skuCode).list();
    }
    
    @Override
    public List<InventoryAlloc> listBySkuCode(String skuCode) {
        if (StringUtil.isBlank(skuCode)) {
            return Collections.emptyList();
        }
        return lambdaQuery().eq(InventoryAlloc::getSkuCode, skuCode).list();
    }
    
    @Override
    public List<InventoryAlloc> listByPlatformItemSkuId(Collection<Long> platformItemSkuIds) {
        if (CollUtil.isEmpty(platformItemSkuIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(InventoryAlloc::getPlatformItemSkuId, platformItemSkuIds).list();
    }
    
    @Override
    public InventoryAlloc getByPlatformItemSkuId(Long platformItemSkuId) {
        return lambdaQuery().eq(InventoryAlloc::getPlatformItemSkuId, platformItemSkuId).last("limit 1").one();
    }
    
}
