package com.daddylab.supplier.item.controller.framework;

import com.daddylab.supplier.item.application.visitStat.VisitStateManager;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.UserVisitDailyMapper;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import java.time.LocalDateTime;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Slf4j
@Component
@AllArgsConstructor
public class VisitStatInterceptor implements HandlerInterceptor {

    private final UserVisitDailyMapper userVisitDailyMapper;
    private final VisitStateManager visitStateManager = new VisitStateManager();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response,
            Object handler) throws Exception {
        final Long userId = UserContext.getUserId();
        if (NumberUtil.isPositive(userId)) {
            visitStateManager.visit(userId, DateUtil.now(), this::visitDaily);
        }
        return true;
    }

    private void visitDaily(Long userId, LocalDateTime visitTime) {
        userVisitDailyMapper.visitDaily(userId, visitTime);
    }

}
