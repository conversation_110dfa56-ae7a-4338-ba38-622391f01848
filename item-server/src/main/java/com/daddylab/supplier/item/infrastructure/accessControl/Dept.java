package com.daddylab.supplier.item.infrastructure.accessControl;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class Dept implements Serializable {
    private static final long serialVersionUID = 1;
    private Long id;

    private Long storeId;

    private String name;

    private String description;

    private Long pid;

    private Long createdUid;

    private Long createdAt;

    private Long updatedUid;

    private Long updatedAt;

    private Integer isDel;

    private Long deletedAt;
}