package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发票种类：
 * p,普通发票(电票)(默认);
 * c,普通发票(纸票);
 * s,专用发票;
 * e,收购发票(电票);
 * f,收购发票(纸质);
 * r,普通发票(卷式);
 * b,增值税电子专用发票;
 * j,机动车销售统一发票;
 * u,二手车销售统一发票;
 * bs:电子发票(增值税专用发票)-即数电专票(电子),
 * pc:电子发票(普通发票)-即数电普票(电子),
 * es:数电纸质发票(增值税专用发票)-即数电专票(纸质);
 * ec:数电纸质发票(普通发票)-即数电普票(纸质)
 *
 * <AUTHOR> up
 * @date 2024年07月26日 10:53 AM
 */
@AllArgsConstructor
@Getter
public enum InvoiceType implements IIntegerEnum {

    /**
     *
     */
    pc(0, "电子发票(普通发票)-即数电普票(电子)"),

    bs(1, "电子发票(增值税专用发票)-即数电专票(电子)");

    @EnumValue
    private final Integer value;

    private final String desc;

}
