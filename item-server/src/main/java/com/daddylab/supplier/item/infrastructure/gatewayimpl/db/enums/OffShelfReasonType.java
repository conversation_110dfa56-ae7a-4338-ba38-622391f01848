package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OffShelfReasonType implements IEnum<Integer> {
    SHOP_SALES(0,"店铺销量"),
    CUSTOMER_COMPLAINT(1,"客户投诉"),
    UNQUALIFIED_TEST(2,"检测问题"),
    PUBLIC_OPINION(3,"舆情问题"),
    BUSINESS(4,"商务合作问题"),
    OTHER(5,"其他问题")
    ;

    private final Integer value;
    private final String desc;

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
