package com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Class  CheckInfo
 *
 * @Date 2022/6/3上午11:19
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class CheckInfoDTO {

    @JsonProperty("item_id")
    private Integer itemId;
    @JsonProperty("first_check")
    private FirstCheck firstCheck;
    @JsonProperty("last_check")
    private LastCheck lastCheck;

    @NoArgsConstructor
    @Data
    public static class FirstCheck {
        @JsonProperty("id")
        private Integer id;
        @JsonProperty("report_at")
        private Integer reportAt;
        @JsonProperty("report_content")
        private String reportContent;
        @JsonProperty("check_project")
        private List<CheckProject> checkProject;
    }

    @NoArgsConstructor
    @Data
    public static class LastCheck {
        @JsonProperty("id")
        private Integer id;
        @JsonProperty("report_at")
        private Integer reportAt;
        @JsonProperty("report_content")
        private String reportContent;
        @JsonProperty("check_project")
        private List<CheckProject> checkProject;
    }


}
