package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.application.drawer.ItemDrawerService;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.itemTag.ItemTagBizService;
import com.daddylab.supplier.item.application.message.QyMsgSendUtils;
import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsPrincipalsInfo;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.controller.item.ItemLaunchPlanTextParam;
import com.daddylab.supplier.item.controller.item.dto.*;
import com.daddylab.supplier.item.domain.item.data.ItemLaunchPlanItemRefInfo;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemLaunchPlanPrincipalDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemLaunchPlanRefItemDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchPlan;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchPlanItemRef;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemLaunchPlanItemRefMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemLaunchPlanMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffListQuery;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil;
import com.daddylab.supplier.item.types.BatchResult;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.multimap.HashSetValuedHashMap;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isNotEmpty;

/**
 * <p>
 * 商品上新计划 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */
@Service
public class ItemLaunchPlanServiceImpl extends DaddyServiceImpl<ItemLaunchPlanMapper, ItemLaunchPlan> implements IItemLaunchPlanService {
    @Autowired
    private ItemLaunchPlanMapper itemLaunchPlanMapper;
    @Autowired
    private IItemLaunchPlanItemRefService itemLaunchPlanItemRefService;
    @Autowired
    private IItemService itemService;
    @Autowired
    private ItemLaunchPlanItemRefMapper itemLaunchPlanItemRefMapper;
    @Autowired
    private IItemSkuService itemSkuService;
    @Autowired
    private NewGoodsBizService newGoodsBizService;
    @Autowired
    private ItemBizService itemBizService;
    @Autowired
    private INewGoodsService newGoodsService;
    @Autowired
    private ItemDrawerService itemDrawerService;
    @Resource
    private IItemLaunchStatsService itemLaunchStatsService;


    @Autowired
    @Qualifier("UserGatewayCacheImpl")
    private UserGateway userGateway;

    @Autowired
    private ItemDrawerService itemDrawerAppService;

    @Autowired
    private ItemTagBizService itemTagBizService;

    @Autowired
    private IBizLevelDivisionService bizLevelDivisionService;



    @Override
    public PageResponse<ItemLaunchPlanPageVo> pageQuery(ItemLaunchPlanPageQuery pageQuery) {
        QueryWrapper<ItemLaunchPlan> queryWrapper = getQueryWrapper(pageQuery);

        // 不使用 mybatis-plus 自带的 count（理由是速度很慢）
        Page<ItemLaunchPlan> page = new Page<>();
        page.setSearchCount(false);
        page.setTotal(itemLaunchPlanMapper.selectPageListCount(queryWrapper));
        page.setCurrent(pageQuery.getPageIndex());
        page.setSize(pageQuery.getPageSize());

        IPage<ItemLaunchPlan> itemLaunchPlanPage = itemLaunchPlanMapper.selectPageList(page, queryWrapper);

        // to vo
        List<ItemLaunchPlan> records = itemLaunchPlanPage.getRecords();
        List<Long> userIds = records.stream().map(ItemLaunchPlan::getCreatedUid).collect(Collectors.toList());
        Map<Long, StaffInfo> longStaffInfoMap = userGateway.batchQueryStaffInfoByIds(userIds);

        List<ItemLaunchPlanPageVo> vos = new ArrayList<>();
        for (ItemLaunchPlan record : records) {
            StaffInfo staffInfo = longStaffInfoMap.get(record.getCreatedUid());
            ItemLaunchPlanPageVo vo = new ItemLaunchPlanPageVo();
            vo.setItemNum(record.getItemNum());
            vo.setCreator(Optional.ofNullable(staffInfo).map(StaffInfo::getNickname).orElse(""));
            vo.setCreatorRealName(Optional.ofNullable(staffInfo).map(StaffInfo::getUserName).orElse(""));
            vo.setCreatedAt(record.getCreatedAt());
            vo.setLaunchTime(record.getLaunchTime());
            Long linkItemOnNum = itemLaunchPlanItemRefService.getLinkItemOnNum(record.getId());
            vo.setItemOnNum(linkItemOnNum);
            vo.setName(record.getPlanName());
            vo.setNo(record.getPlanNo());
            vo.setId(record.getId());
            vo.setIsSubmit(record.getIsSubmit());
            vo.setBusinessLine(Integer.parseInt(record.getBusinessLine()));
            vos.add(vo);
        }
        return PageResponse.of(vos, (int) page.getTotal(), pageQuery.getPageSize(), pageQuery.getPageIndex());
    }

    @Override
    public PageResponse<ItemLaunchPlanLinkItemVo> pageQueryLinkItem(ItemLaunchPlanLinkItemPageQuery pageQuery) {
        ItemLaunchPlanLinkQueryParam param = new ItemLaunchPlanLinkQueryParam();
        QueryWrapper<ItemLaunchPlanItemRef> queryWrapper = getQueryWrapper2(pageQuery, param);

        // 不使用 mybatis-plus 自带的 count（理由是速度很慢）
        Page<ItemLaunchPlanItemRef> page = new Page<>();
        page.setSearchCount(false);
        page.setTotal(itemLaunchPlanItemRefMapper.selectPageListCount(queryWrapper, param));
        page.setCurrent(pageQuery.getPageIndex());
        page.setSize(pageQuery.getPageSize());

        IPage<ItemLaunchPlanItemRef> refPage = itemLaunchPlanItemRefMapper.selectPageList(page, queryWrapper, param);

        List<ItemLaunchPlanItemRef> refs = refPage.getRecords();
        List<Long> itemIds = refs.stream()
                .map(ItemLaunchPlanItemRef::getItemId)
                .distinct()
                .collect(Collectors.toList());

        // to vo
        // 商品采购人
        Map<Long, ItemBuyerDto> itemsBuyerMap = itemService.getItemsBuyerMap(itemIds);
        // 商品负责人
        Map<Long, ItemPrincipalDto> itemsPrincipalMap = itemService.getItemsPrincipalMap(itemIds);
        // item 相关信息
        Map<Long, ItemWithLaunchPlanDto> itemDtoMap = itemService.getItemDtoMap(itemIds);
        // sku 相关信息
//        Map<Long, SkuWithLaunchPlanDto> skuDtoMap = itemSkuService.getSkuDtoMap(skuIds);
//        Map<Long, PartnerItemResp> longPartnerItemRespMap = itemBizService.queryPartnerItemBatch(itemIds);
        final Map<Long, List<String>> tagMap = itemTagBizService.listByItemIds(itemIds);
        final Map<Long, List<CorpBizTypeDTO>> corpBizTypeMap = bizLevelDivisionService.getCorpBizType(BizUnionTypeEnum.SPU,
                                                                                                   itemIds);

        List<ItemLaunchPlanLinkItemVo> vos = new ArrayList<>();
        for (ItemLaunchPlanItemRef ref : refs) {
            Long itemId = ref.getItemId();
//            Long skuId = ref.getSkuId();

            ItemLaunchPlanLinkItemVo vo = new ItemLaunchPlanLinkItemVo();
            vo.setPlanItemRefId(ref.getId());
            vo.setPlanId(ref.getPlanId());
            vo.setItemId(itemId);
            vo.setItemCode(ref.getItemCode());
            vo.setSortNo(Objects.isNull(ref.getSortNo()) ? 0 : ref.getSortNo());
            vo.setHomeCopy(ref.getHomeCopy());
            vo.setItemType(ref.getItemType());
            vo.setLaunchPrice(ref.getLaunchPrice());
            ItemPrincipalDto itemPrincipalDto = itemsPrincipalMap.get(itemId);
            vo.setPrincipalUserId(Optional.ofNullable(itemPrincipalDto).map(ItemPrincipalDto::getPrincipalUserId).orElse(0L));
            vo.setPrincipalUserName(Optional.ofNullable(itemPrincipalDto).map(ItemPrincipalDto::getPrincipalUserName).orElse(""));
            vo.setPrincipalUserNickName(Optional.ofNullable(itemPrincipalDto).map(ItemPrincipalDto::getPrincipalUserNickName).orElse(""));
            vo.setRemark(ref.getRemark());
            vo.setShareDiskLink(ref.getShareDiskLink());
            vo.setActivePeriodStart(ref.getActivePeriodStart());
            vo.setActivePeriodEnd(ref.getActivePeriodEnd());

            ItemBuyerDto itemBuyerDto = itemsBuyerMap.get(itemId);
            Long buyerUserId = Optional.ofNullable(itemBuyerDto).map(ItemBuyerDto::getBuyerUserId).orElse(0L);
            String buyerUserName = Optional.ofNullable(itemBuyerDto).map(ItemBuyerDto::getBuyerUserName).orElse("");
            vo.setBuyerUserId(buyerUserId);
            vo.setBuyerUserName(buyerUserName);

            ItemWithLaunchPlanDto itemWithLaunchPlanDto = itemDtoMap.get(itemId);
            String itemName = Optional.ofNullable(itemWithLaunchPlanDto).map(ItemWithLaunchPlanDto::getItemName).orElse("");
            String categoryPath = Optional.ofNullable(itemWithLaunchPlanDto).map(ItemWithLaunchPlanDto::getCategoryPath).orElse("");
            vo.setItemName(itemName);
            vo.setCategoryPath(categoryPath);
            vo.setBusinessLine(Optional.ofNullable(itemWithLaunchPlanDto).map(ItemWithLaunchPlanDto::getBusinessLine).orElse(-1));
//            vo.setIsDadCheck(Optional.ofNullable(longPartnerItemRespMap.get(vo.getItemId()))
//                    .map(partnerItemResp -> BooleanUtil.toInteger(ItemBusinessLineEnum.DAD_CHECK.getValue().equals(partnerItemResp.getModuleType()))).orElse(0));
            vo.setTags(tagMap.getOrDefault(itemId, Collections.emptyList()));
            final List<CorpBizTypeDTO> corpBizTypeDTOS = corpBizTypeMap.get(vo.getItemId());
            vo.setCorpBizType(corpBizTypeDTOS);
            vos.add(vo);
        }
        List<ItemLaunchPlanLinkItemVo> collect = vos.stream().sorted(Comparator.comparing(ItemLaunchPlanLinkItemVo::getSortNo)).collect(Collectors.toList());
        return PageResponse.of(collect, (int) page.getTotal(), pageQuery.getPageSize(), pageQuery.getPageIndex());
    }

    @Override
    public SingleResponse<ItemLaunchPlanBaseVo> getBaseInfo(Long planId) {
        ItemLaunchPlan plan = itemLaunchPlanMapper.selectById(planId);
        if (plan == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "找不到商品上新计划");
        }
        ItemLaunchPlanBaseVo planBaseVo = new ItemLaunchPlanBaseVo();
        planBaseVo.setLaunchTime(plan.getLaunchTime());
        planBaseVo.setPlanName(plan.getPlanName());
        planBaseVo.setPlanId(plan.getId());
        planBaseVo.setBusinessLine(Integer.parseInt(plan.getBusinessLine()));

        return SingleResponse.of(planBaseVo);
    }

    @Override
    public ItemLaunchPlan getPlanByItemId(Long itemId) {
        return itemLaunchPlanMapper.getPlanByItemId(itemId);
    }

//    @Override
//    public Map<Long, ItemLaunchPlan> getPlanMapByItemIds(List<Long> itemIds) {
//        if (CollectionUtil.isEmpty(itemIds)) {
//            return new HashMap<>();
//        }
//        List<ItemLaunchPlan> plans = itemLaunchPlanMapper.selectBatchByItemIds(itemIds);
//        Map<Long, ItemLaunchPlan> map = new HashMap<>((int) (plans.size() / 0.75 + 1));
//        for (ItemLaunchPlan plan : plans) {
//            map.put();
//        }
//        return map;
//    }

    @Override
    public void deletePlanItemRef(Long planItemRefId) {
        itemLaunchPlanItemRefService.deleteById(planItemRefId);
    }

    @Override
    public void deletePlan(Long planId) {
        ItemLaunchPlan plan = itemLaunchPlanMapper.selectById(planId);
        if (plan == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "找不到商品上新计划");
        }
        List<ItemLaunchPlanItemRef> itemLaunchPlanItemRefs = itemLaunchPlanItemRefService.selectListByPlanId(planId);
        List<Long> itemIds = itemLaunchPlanItemRefs
                .stream()
                .map(ItemLaunchPlanItemRef::getItemId)
                .collect(Collectors.toList());
        List<Item> items = itemService.selectBatchByIds(itemIds);
        if (CollectionUtil.isNotEmpty(items)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "存关联商品，删除失败！");
        }

//        for (Item item : items) {
//            if (Objects.equals(item.getStatus(), ItemStatus.ON.getValue())) {
//                throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "存在已上架关联商品，删除失败");
//            }
//        }

        // delete
        itemLaunchPlanMapper.deleteById(planId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(ItemLaunchPlanParam param) {
        // check
        checkBeforeAdd(param);

        List<ItemLaunchPlanLinkItemParam> linkItems = param.getLinkItems();

        // insert item_launch_plan
        Long launchTime = param.getLaunchTime();
        ItemLaunchPlan insertItemLaunchPlan = new ItemLaunchPlan();
        insertItemLaunchPlan.setPlanName(param.getPlanName());
        insertItemLaunchPlan.setLaunchTime(launchTime);
        insertItemLaunchPlan.setItemNum(linkItems.size());
        insertItemLaunchPlan.setBusinessLine(param.getBusinessLine().toString());
        itemLaunchPlanMapper.insert(insertItemLaunchPlan);
        // 将 no 更新进去（no 的生成规则需要用的主键 ID，所以就先插入了）
        Long planId = insertItemLaunchPlan.getId();
        String planNo = getPlanNo(planId);
        ItemLaunchPlan updateItemLaunchPlan = new ItemLaunchPlan();
        updateItemLaunchPlan.setId(planId);
        updateItemLaunchPlan.setPlanNo(planNo);
        insertItemLaunchPlan.setPlanNo(planNo);
        itemLaunchPlanMapper.updateById(updateItemLaunchPlan);

        // insert item_launch_plan_item_ref
        List<ItemLaunchPlanItemRef> refs = new ArrayList<>();
        List<ItemLaunchPlanItemRefInfo> refInfos = new ArrayList<>();
        for (ItemLaunchPlanLinkItemParam linkItem : linkItems) {
            ItemLaunchPlanItemRef ref = new ItemLaunchPlanItemRef();
            ref.setPlanId(planId);
            ref.setItemId(linkItem.getItemId());
            ref.setItemCode(linkItem.getItemCode());
            ref.setRemark(linkItem.getRemark());
            ref.setHomeCopy(linkItem.getHomeCopy());
            ref.setLaunchPrice(linkItem.getLaunchPrice());
            ref.setItemType(linkItem.getItemType());
            ref.setSortNo(linkItem.getSortNo());
            ref.setShareDiskLink(linkItem.getShareDiskLink());
            refs.add(ref);

            ItemLaunchPlanItemRefInfo refInfo = new ItemLaunchPlanItemRefInfo();
            BeanUtils.copyProperties(ref, refInfo);
            refInfo.setPrincipalId(linkItem.getPrincipalUserId());
            refInfos.add(refInfo);
        }
        itemLaunchPlanItemRefService.saveBatch(refs);
        final BatchResult<NewGoods> itemSyncResult = newGoodsBizService.itemSync(refInfos, insertItemLaunchPlan);
        //初始化商品上新类型
        for (ItemLaunchPlanItemRefInfo refInfo : refInfos) {
            itemDrawerService.updateItemDrawerType(refInfo.getItemId(),
                    IEnum.getEnumByValue(LaunchItemType.class, refInfo.getItemType()));
        }
        // 商品的上新状态改为「待完善」
        List<Long> itemIds = refs.stream()
                .map(ItemLaunchPlanItemRef::getItemId)
                .distinct()
                .collect(Collectors.toList());

        //后端商品更新上新时间
        itemBizService.updateLaunchTime(itemIds, launchTime);

        final StaffBrief creatorUser = StaffAssembler.INST.toStaffBrief(UserContext.getUserId());
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            for (Long itemId : itemIds) {
                                itemBizService.updateLaunchStatus(itemId, ItemLaunchStatus.TO_BE_IMPROVED);
                            }

                            noticeItemLaunchToImprove(itemIds, creatorUser, launchTime);
                        }
                    }
            );
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(ItemLaunchPlanParam param) {
        // check
        checkBeforeEdit(param);

        Long planId = param.getPlanId();
        String planName = param.getPlanName();
        Long launchTime = param.getLaunchTime();
        List<ItemLaunchPlanLinkItemParam> linkItems = param.getLinkItems();
        List<ItemLaunchPlanPrincipalDO> itemLaunchPlanPrincipalDOS = itemLaunchPlanItemRefMapper.selectPrincipalByPlanId(planId);
        ItemLaunchPlan oldPlan = this.getById(planId);
        boolean submitted = 1 == oldPlan.getIsSubmit();

        // 操作 item_launch_plan_item_ref
        List<ItemLaunchPlanItemRef> oldRefs = itemLaunchPlanItemRefService.selectListByPlanId(planId);
        List<Long> existedRefIds = oldRefs.stream().map(ItemLaunchPlanItemRef::getId).collect(Collectors.toList());
        Set<Long> paramRefIds = linkItems.stream()
                .map(ItemLaunchPlanLinkItemParam::getPlanItemRefId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 需要删除
        List<Long> refIdsOfNeedToDel = new ArrayList<>();
        for (Long existedRefId : existedRefIds) {
            if (!paramRefIds.contains(existedRefId)) {
                refIdsOfNeedToDel.add(existedRefId);
            }
        }

        if (CollectionUtil.isNotEmpty(refIdsOfNeedToDel)) {
            List<ItemLaunchPlanItemRef> refsOfNeedToDel = itemLaunchPlanItemRefService.selectBatchByIds(refIdsOfNeedToDel);
            List<Long> itemIdsOfNeedToUpdate = refsOfNeedToDel.stream()
                    .map(ItemLaunchPlanItemRef::getItemId)
                    .distinct()
                    .collect(Collectors.toList());
//            List<NewGoods> newGoodsList = newGoodsService.selectBatchByItemIds(itemIdsOfNeedToUpdate);
//            List<Long> newGoodsIdsOfNeedToUpdate = newGoodsList.stream()
//                    .map(NewGoods::getId)
//                    .distinct()
//                    .collect(Collectors.toList());

            // 更新 item
            List<Item> updateItems = new ArrayList<>();
            for (Long itemId : itemIdsOfNeedToUpdate) {
                Item updateItem = new Item();
                updateItem.setId(itemId);
                updateItem.setLaunchStatus(ItemLaunchStatus.TO_BE_SELECTED.getValue());
                updateItems.add(updateItem);
            }
            itemService.updateBatchById(updateItems);

            //终止商品上新的审批流
            for (Long itemId : itemIdsOfNeedToUpdate) {
                itemDrawerService.processTerminateByItemId(itemId);
            }

            //删除商品上新统计数据
            itemLaunchStatsService.delStatsBatch(itemIdsOfNeedToUpdate);

            // 批量删除 ref
            itemLaunchPlanItemRefMapper.deleteBatchIds(refIdsOfNeedToDel);

        }
        // 需要新增或更新
        List<ItemLaunchPlanItemRef> refsOfSaveOrUpdate = new ArrayList<>();
        List<ItemLaunchPlanItemRefInfo> refInfos = new ArrayList<>();
        // 新关联的商品
        List<ItemLaunchPlanItemRef> newAddRefs = new ArrayList<>();
        for (ItemLaunchPlanLinkItemParam linkItem : linkItems) {
            ItemLaunchPlanItemRef ref = convertToRef(linkItem, planId);
            refsOfSaveOrUpdate.add(ref);

            if (linkItem.getPlanItemRefId() == null) {
                newAddRefs.add(ref);
            }

            ItemLaunchPlanItemRefInfo refInfo = new ItemLaunchPlanItemRefInfo();
            BeanUtils.copyProperties(ref, refInfo);
            refInfo.setPrincipalId(linkItem.getPrincipalUserId());
            refInfos.add(refInfo);
        }


        itemLaunchPlanItemRefService.saveOrUpdateBatch(refsOfSaveOrUpdate);

        // 上新计划字段变更，发送企微提醒消息。(仅仅首页文案和上新价)
        List<String> logs = new LinkedList<>();
        DiffUtil.ObjListDiff objListDiff = DiffUtil.getInstance().diffObjList(oldRefs, refsOfSaveOrUpdate, ItemLaunchPlanItemRef.class);
        Map<Object, Set<DiffUtil.ChangePropertyObj>> changeMap = objListDiff.getChangeMap();
        if (!MapUtil.isEmpty(changeMap)) {
            Set<Object> objects = changeMap.keySet();
            List<Long> refIdList = objects.stream().map(val -> Long.valueOf(val.toString())).collect(Collectors.toList());
            List<ItemLaunchPlanRefItemDO> list = itemLaunchPlanItemRefMapper.selectItemNameByRefIds(refIdList);
            Map<Long, String> refIdAndItemNameMap = list.stream().collect(Collectors.toMap(ItemLaunchPlanRefItemDO::getRefId,
                    ItemLaunchPlanRefItemDO::getItemName, (a, b) -> a));

            changeMap.forEach(((o, changePropertyObjs) -> {
                String itemName = refIdAndItemNameMap.get(Long.valueOf(o.toString()));
                changePropertyObjs.stream().filter(val -> {
                    String property = val.getProperty();
                    return "首页文案".equals(property) || "上新价".equals(property) || "共享盘链接".equals(property);
                }).forEach(val -> {
                    String format = String.format("%s的%s，由 %s 修改为 %s", itemName, val.getProperty(), val.getOldVal(),
                            val.getNewVal());
                    logs.add(format + "\n");
                });
            }));
        }

        //新增或更新的商品同步商品上新类型
        for (ItemLaunchPlanItemRef refInfo : refInfos) {
            itemDrawerService.updateItemDrawerType(refInfo.getItemId(),
                    IEnum.getEnumByValue(LaunchItemType.class, refInfo.getItemType()));
        }

        // 等修改完毕了，再统计下上新计划关联的商品数量，并更新
        List<ItemLaunchPlanItemRef> itemRefs = itemLaunchPlanItemRefService.selectListByPlanId(planId);
        // update item_launch_plan
        ItemLaunchPlan updateItemLaunchPlan = new ItemLaunchPlan();
        updateItemLaunchPlan.setId(planId);
        updateItemLaunchPlan.setPlanName(planName);
        updateItemLaunchPlan.setLaunchTime(launchTime);
        updateItemLaunchPlan.setItemNum(itemRefs.size());
        updateItemLaunchPlan.setBusinessLine(param.getBusinessLine().toString());
        itemLaunchPlanMapper.updateById(updateItemLaunchPlan);

        //将 update model 的属性复制过来保持 persist model 数据的一致性
        BeanUtil.copyProperties(updateItemLaunchPlan, oldPlan, CopyOptions.create().ignoreNullValue());

        final BatchResult<NewGoods> itemSyncResult = newGoodsBizService.itemSync(refInfos, oldPlan);

        // 商品的上新状态改为「待完善」
        List<Long> itemIds = newAddRefs.stream()
                .map(ItemLaunchPlanItemRef::getItemId)
                .distinct()
                .collect(Collectors.toList());
        for (Long itemId : itemIds) {
            itemBizService.updateLaunchStatus(itemId, ItemLaunchStatus.TO_BE_IMPROVED);
        }

        //后端商品更新上新时间
        itemBizService.updateLaunchTime(itemIds, launchTime);

        final Long createdUid = oldPlan.getCreatedUid();
        final StaffBrief creatorUser = StaffAssembler.INST.toStaffBrief(createdUid);

        final String operatorNick = UserContext.getNickName();

        // 上新计划下的商品是否存在
        boolean newItemNotice = CollectionUtils.isNotEmpty(newAddRefs);
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(
                    new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            // 此上新计划存在新增的spu商品，即发送消息
                            if (newItemNotice) {
                                noticeItemLaunchToImprove(itemIds, creatorUser, launchTime);
                            }

                            // 上新管理更改了产品负责人，发送事件通知
                            Map<Long, Long> collect = param.getLinkItems().stream().collect(Collectors
                                    .toMap(ItemLaunchPlanLinkItemParam::getItemId, ItemLaunchPlanLinkItemParam::getPrincipalUserId));
                            comparePrincipal(itemLaunchPlanPrincipalDOS, collect);

                            // 上新计划字段变更，发送企微信息通知
                            if (submitted && CollUtil.isNotEmpty(logs)) {
//                                MsgEvent of = MsgEvent.buildByPlanChange(planId, UserContext.getUserId(), StrUtil.join("。", logs));
//                                EventBusUtil.post(of, true);
                                noticePlanFieldEdit(operatorNick, oldPlan, StrUtil.join("", logs));
                            }

                        }
                    }
            );
        }

    }

    private void noticePlanSubmit(String operatorNick, ItemLaunchPlan plan) {
        final List<StaffBrief> users = getProductGroupAndDesignGroupUsers();
        if (!users.isEmpty()) {
            final HashMap<String, Object> variables = new HashMap<>();
            variables.put("提交人花名", operatorNick);
            variables.put("上新日期", DateUtil.formatDate(plan.getLaunchTime()));
            variables.put("上新活动名称", plan.getPlanName());
            variables.put("相关负责人", users.stream().map(StaffBrief::getQwUserId).collect(
                    Collectors.joining(",")));
            variables.put("id", plan.getId());
            QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.ITEM_LAUNCH_PLAN_SUBMIT, variables);
        }
    }

    private void noticePlanFieldEdit(String operatorNick, ItemLaunchPlan plan, String log) {
        final List<StaffBrief> users = getProductGroupAndDesignGroupUsers();
        if (!users.isEmpty()) {
            final HashMap<String, Object> variables = new HashMap<>();
            variables.put("修改人花名", operatorNick);
            variables.put("上新日期", DateUtil.formatDate(plan.getLaunchTime()));
            variables.put("上新活动名称", plan.getPlanName());
            variables.put("相关负责人", users.stream().map(StaffBrief::getQwUserId).collect(
                    Collectors.joining(",")));
            variables.put("变更项目", log);
            variables.put("id", plan.getId());
            QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.ITEM_LAUNCH_PLAN_FIELD_EDIT, variables);
        }
    }

    /**
     * 【待选择】➡️【待完善】
     * @param itemIds
     * @param creatorUser
     * @param launchTime
     */
    private void noticeItemLaunchToImprove(List<Long> itemIds, StaffBrief creatorUser, Long launchTime) {
        if (CollectionUtil.isEmpty(itemIds)) {
            return;
        }
        final MultiResponse<NewGoodsPrincipalsInfo> newGoodsPrincipalsInfoBatch = newGoodsBizService.getNewGoodsPrincipalsInfoBatch(
                itemIds);
        final HashSetValuedHashMap<StaffBrief, NewGoodsPrincipalsInfo> principalsInfoByBuyer = new HashSetValuedHashMap<>();
        for (NewGoodsPrincipalsInfo newGoodsPrincipalsInfo : newGoodsPrincipalsInfoBatch.getData()) {
            if (CollectionUtil.isNotEmpty(newGoodsPrincipalsInfo.getBuyerUsers())) {
                for (StaffBrief buyerUser : newGoodsPrincipalsInfo.getBuyerUsers()) {
                    principalsInfoByBuyer.put(buyerUser, newGoodsPrincipalsInfo);
                }
            }
        }
        for (Map.Entry<StaffBrief, Collection<NewGoodsPrincipalsInfo>> entry : principalsInfoByBuyer.asMap()
                                                                                                    .entrySet()) {
            final HashMap<String, Object> variables = new HashMap<>();
            variables.put("上新计划创建人", creatorUser.getNickname());
            variables.put("上新日期", DateUtil.formatDate(launchTime));
            variables.put("商品数量", entry.getValue().size());
            variables.put("SKU数量",
                    entry.getValue()
                         .stream().mapToInt(NewGoodsPrincipalsInfo::getNewGoodsNum).sum());
            variables.put("采购负责人", entry.getKey().getQwUserId());
            final String itemIdsStr = entry.getValue()
                                           .stream()
                                           .map(NewGoodsPrincipalsInfo::getItemId)
                                           .map(Object::toString)
                                           .collect(Collectors.joining(","));
            variables.put("itemIds", itemIdsStr);
            QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.ITEM_LAUNCH_TO_IMPROVE, variables);
        }
    }

    @Override
    public void editItemText(ItemLaunchPlanTextParam param) {
        ItemLaunchPlan plan = this.getById(param.getPlanId());
        Assert.notNull(plan, "计划id非法，计划查询为空");
        ItemLaunchPlanItemRef oleOne = itemLaunchPlanItemRefService.getItemLaunchPlanItemRef(param.getItemId());
        Assert.notNull(oleOne, "商品id非法，查询为空");
        String oldLaunchPrice = oleOne.getLaunchPrice();
        String oldHomeCopy = oleOne.getHomeCopy();
        String oldShareDiskLink = oleOne.getShareDiskLink();
        final String operatorNick = UserContext.getNickName();

        itemLaunchPlanItemRefService.lambdaUpdate()
                .set(isNotEmpty(param.getHomeCopy()), ItemLaunchPlanItemRef::getHomeCopy, param.getHomeCopy())
                .set(isNotEmpty(param.getLaunchPrice()), ItemLaunchPlanItemRef::getLaunchPrice, param.getLaunchPrice())
                .eq(ItemLaunchPlanItemRef::getPlanId, param.getPlanId())
                .eq(ItemLaunchPlanItemRef::getItemId, param.getItemId())
                .set(isNotEmpty(param.getShareDiskLink()), ItemLaunchPlanItemRef::getShareDiskLink,
                        param.getShareDiskLink())
                .update();

        if (plan.getIsSubmit() == 1) {
            Item item = itemService.getById(param.getItemId());
            Assert.notNull(oleOne, "商品id非法，商品查询为空");

            List<String> logs = new LinkedList<>();
            if (isNotEmpty(param.getLaunchPrice()) && !param.getLaunchPrice().equals(oldLaunchPrice)) {
                String format = String.format("%s的%s，由 %s 修改为 %s", item.getName(), "上新价格", oldLaunchPrice,
                        param.getLaunchPrice());
                logs.add(format + "\n");
            }
            if (isNotEmpty(param.getHomeCopy()) && !param.getHomeCopy().equals(oldHomeCopy)) {
                String format = String.format("%s的%s，由 %s 修改为 %s", item.getName(), "上新文案", oldHomeCopy,
                        param.getHomeCopy());
                logs.add(format + "\n");
            }
            if (isNotEmpty(param.getShareDiskLink()) && !param.getShareDiskLink().equals(oldShareDiskLink)) {
                String format = String.format("%s的%s，由 %s 修改为 %s", item.getName(), "共享盘链接", oldShareDiskLink,
                        param.getShareDiskLink());
                logs.add(format + "\n");
            }
            if (CollUtil.isNotEmpty(logs)) {
//                MsgEvent of = MsgEvent.buildByPlanChange(plan.getId(), UserContext.getUserId(),
//                        StrUtil.join("。", logs));
//                EventBusUtil.post(of, true);
                noticePlanFieldEdit(operatorNick, plan, String.join("", logs));

            }
        }

    }

    @Override
    public MultiResponse<PlanNameDropDownVo> nameDropDownList(String name, Integer pageIndex, Integer pageSie) {
        pageIndex = pageIndex == 0 ? 1 : pageIndex;
        List<PlanNameDropDownVo> vos = itemLaunchPlanMapper.nameDropDownList(name, (pageIndex - 1) * pageSie, pageSie);
        return MultiResponse.of(vos);
    }

    @Override
    public MultiResponse<LaunchTimeDropDownVo> launchTimeDownList(Integer pageIndex, Integer pageSize) {
        pageIndex = pageIndex == 0 ? 1 : pageIndex;
        List<LaunchTimeDropDownVo> pageVos = itemLaunchPlanMapper.launchPlanDownList((pageIndex - 1) * pageSize, pageSize);

//        List<LaunchTimeDropDownVo> allVos = itemLaunchPlanMapper.launchTimeDownList();
//        List<LaunchTimeDropDownVo> pageVos = ToolUtil.pagination(allVos, pageIndex, pageSize);
//        for (LaunchTimeDropDownVo vo : pageVos) {
//            if (vo == null || vo.getLaunchDate() == null) {
//                continue;
//            }
//            vo.setLaunchTime(DateUtil.parseTime(vo.getLaunchDate(), "yyyy-MM-dd"));
//        }
        return MultiResponse.of(pageVos);
    }

    @Override
    public void deleteRefByItemId(Long itemId) {
        final List<ItemLaunchPlanItemRef> itemLaunchPlanItemRefs = itemLaunchPlanItemRefService.lambdaQuery()
                .eq(ItemLaunchPlanItemRef::getItemId, itemId)
                .list();
        if (itemLaunchPlanItemRefs.isEmpty()) {
            return;
        }

        final List<Long> refIds = itemLaunchPlanItemRefs.stream().map(ItemLaunchPlanItemRef::getId)
                .collect(Collectors.toList());
        itemLaunchPlanItemRefService.removeByIds(refIds);

        final Long planId = itemLaunchPlanItemRefs.get(0).getPlanId();

        // 等修改完毕了，再统计下上新计划关联的商品数量，并更新
        List<ItemLaunchPlanItemRef> itemRefs = itemLaunchPlanItemRefService.selectListByPlanId(planId);
        ItemLaunchPlan updateItemLaunchPlan = new ItemLaunchPlan();
        updateItemLaunchPlan.setId(planId);
        updateItemLaunchPlan.setItemNum(itemRefs.size());
        itemLaunchPlanMapper.updateById(updateItemLaunchPlan);

        // 更新商品预计上新时间和上新状态
        itemBizService.resetLaunchState(Collections.singletonList(itemId));

    }

    @Override
    public Map<Long, Long> getEstimateSaleTimeBatch(List<Long> itemIds) {
        if (CollectionUtil.isEmpty(itemIds)) {
            return Collections.emptyMap();
        }
        final List<ItemLaunchPlanItemRef> refs = itemLaunchPlanItemRefService.lambdaQuery()
                .in(ItemLaunchPlanItemRef::getItemId, itemIds).list();
        final List<Long> planIds = refs.stream().map(ItemLaunchPlanItemRef::getPlanId).distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(planIds)) {
            return Collections.emptyMap();
        }

        final LambdaQueryWrapper<ItemLaunchPlan> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ItemLaunchPlan::getId, planIds);
        final Map<Long, ItemLaunchPlan> itemLaunchPlanMap = itemLaunchPlanMapper
                .selectList(queryWrapper)
                .stream().collect(Collectors.toMap(ItemLaunchPlan::getId, Function
                        .identity()));

        final Map<Long, Long> results = new HashMap<>();
        for (Long itemId : itemIds) {
            final Long launchTime = refs.stream()
                    .filter(ref -> Objects.equals(ref.getItemId(), itemId))
                    .findFirst()
                    .map(ref -> itemLaunchPlanMap.get(ref.getPlanId()))
                    .map(ItemLaunchPlan::getLaunchTime).orElse(0L);
            results.put(itemId, launchTime);
        }
        return results;
    }

    @Override
    public Map<Long, Long> saveBatchIfNotExistAndReturnPlanId(Collection<Long> launchDateEpochs) {
        final HashMap<Long, Long> returnMap = new HashMap<>();
        for (Long launchTime : launchDateEpochs) {
            ItemLaunchPlan itemLaunchPlan = lambdaQuery().eq(ItemLaunchPlan::getLaunchTime,
                    launchTime).oneOpt().orElseGet(() -> {
                ItemLaunchPlan insertItemLaunchPlan = new ItemLaunchPlan();
                insertItemLaunchPlan.setPlanName(
                        DateUtil.format(launchTime, DatePattern.CHINESE_DATE_PATTERN) + "上新计划");
                insertItemLaunchPlan.setLaunchTime(launchTime);
                insertItemLaunchPlan.setItemNum(0);
                itemLaunchPlanMapper.insert(insertItemLaunchPlan);

                Long planId = insertItemLaunchPlan.getId();
                String planNo = getPlanNo(planId);
                ItemLaunchPlan updateItemLaunchPlan = new ItemLaunchPlan();
                updateItemLaunchPlan.setId(planId);
                updateItemLaunchPlan.setPlanNo(planNo);
                itemLaunchPlanMapper.updateById(updateItemLaunchPlan);

                return insertItemLaunchPlan;
            });
            returnMap.put(launchTime, itemLaunchPlan.getId());
        }
        return returnMap;
    }

    @Override
    public int updateBatchPlanRefCount(Collection<Long> planIds) {
        return getBaseMapper().updateBatchPlanRefCount(planIds);
    }

    @Override
    public boolean ensurePlanItemRef(Long planId, Long itemId, String itemCode, Long time) {
        final Optional<ItemLaunchPlanItemRef> itemLaunchPlanItemRefOptional = itemLaunchPlanItemRefService.lambdaQuery()
                .eq(ItemLaunchPlanItemRef::getPlanId, planId)
                .eq(ItemLaunchPlanItemRef::getItemId, itemId).oneOpt();
        if (itemLaunchPlanItemRefOptional.isPresent()) {
            return false;
        }
        final ItemLaunchPlanItemRef itemLaunchPlanItemRef = new ItemLaunchPlanItemRef();
        itemLaunchPlanItemRef.setPlanId(planId);
        itemLaunchPlanItemRef.setItemId(itemId);
        itemLaunchPlanItemRef.setItemCode(itemCode);
        itemLaunchPlanItemRef.setRemark("");
        itemLaunchPlanItemRef.setCreatedAt(time);
        itemLaunchPlanItemRefService.save(itemLaunchPlanItemRef);
        return true;
    }

    /**
     * 当编辑上新管理时，比较编辑操作是否更改了产品负责人，
     * 如果更改产品负责人，发送事件通知
     *
     * @param itemLaunchPlanPrincipalDOS 上新计划id
     * @param editedItemIdPrincipalMap   编辑请求中的商品，负责人映射
     */
    public void comparePrincipal(List<ItemLaunchPlanPrincipalDO> itemLaunchPlanPrincipalDOS, Map<Long, Long> editedItemIdPrincipalMap) {
        Map<Long, Long> oldItemIdPrincipalMap = itemLaunchPlanPrincipalDOS.stream().distinct().collect(Collectors.toMap(ItemLaunchPlanPrincipalDO::getItemId,
                ItemLaunchPlanPrincipalDO::getPrincipalId));

//        List<Long> itemIdList = new LinkedList<>();

//        List<Long> newPrincipalList = new LinkedList<>();
        oldItemIdPrincipalMap.forEach((itemId, PrincipalId) -> {
            Long newPrincipal = editedItemIdPrincipalMap.getOrDefault(itemId, 0L);
            // 此商品新的产品负责人存在，并且非之前的旧的产品负责人。
            if (newPrincipal > 0 && !newPrincipal.equals(PrincipalId)) {
//                itemIdList.add(itemId);
//                newPrincipalList.add(newPrincipal);

//                MsgEvent event = MsgEvent.buildByPrincipalChange(newPrincipal, UserContext.getUserId(), ListUtil.of(itemId));
//                EventBusUtil.post(event, true);

                final Item item = itemService.getById(itemId);
                itemDrawerAppService.noticeItemLaunchModifyPrincipals(UserContext.getNickName(), newPrincipal, item.getName(), item.getId());


            }
        });

//        if (CollectionUtil.isNotEmpty(itemIdList)) {
//            MsgEvent.buildByPrincipalChange(newPrincipal,)
//            MsgEvent of = MsgEvent.ofOperatorChange(newPrincipalList, UserContext.getUserId(), itemIdList);
//            EventBusUtil.post(of, true);
//        }
    }

    private ItemLaunchPlanItemRef convertToRef(ItemLaunchPlanLinkItemParam linkItem, Long planId) {
        ItemLaunchPlanItemRef ref = new ItemLaunchPlanItemRef();
        ref.setId(linkItem.getPlanItemRefId());
        ref.setPlanId(planId);
        ref.setItemId(linkItem.getItemId());
        ref.setItemCode(linkItem.getItemCode());
        ref.setRemark(linkItem.getRemark());
        ref.setSortNo(linkItem.getSortNo());
        ref.setHomeCopy(linkItem.getHomeCopy());
        ref.setLaunchPrice(linkItem.getLaunchPrice());
        ref.setItemType(linkItem.getItemType());
        ref.setShareDiskLink(linkItem.getShareDiskLink());
        return ref;
    }

    private String getPlanNo(Long planId) {
        String prefix = "SX";
        String year = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyy");

        String planIdStr = planId.toString();
        if (planIdStr.length() < 3) {
            // 前面用 0 填补
            int diff = 3 - planIdStr.length();
            StringBuilder fillZero = new StringBuilder();
            for (int i = 0; i < diff; i++) {
                fillZero.append("0");
            }
            planIdStr = fillZero + planIdStr;
        }
        return prefix + year + planIdStr;
    }


    private void checkBeforeEdit(ItemLaunchPlanParam param) {
        String planName = param.getPlanName();
        Long launchTime = param.getLaunchTime();
        Long planId = param.getPlanId();

        if (planId == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "编辑时，计划ID不能为空");
        }
        baseCheck(param);
        List<ItemLaunchPlanLinkItemParam> linkItems = param.getLinkItems();
        if (CollectionUtil.isNotEmpty(linkItems)) {
            // 新增的关联关系，需要校验这个 item 是否已经关联其他上新计划
            for (ItemLaunchPlanLinkItemParam linkItem : linkItems) {
                Long refId = linkItem.getPlanItemRefId();
                if (refId == null) {
                    ItemLaunchPlan plan = getPlanByItemId(linkItem.getItemId());
                    if (Objects.nonNull(plan)) {
                        throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "存在商品已关联其他上新计划");
                    }
                }
            }
        }
    }

    private void checkBeforeAdd(ItemLaunchPlanParam param) {
        baseCheck(param);
        checkItems(param.getLinkItems(), null);
    }

    private void checkItems(List<ItemLaunchPlanLinkItemParam> linkItems, Long planId) {
        if (CollectionUtil.isNotEmpty(linkItems)) {
            for (ItemLaunchPlanLinkItemParam linkItem : linkItems) {
                ItemLaunchPlan plan = getPlanByItemId(linkItem.getItemId());
                if (Objects.nonNull(plan) && planId != null && !planId.equals(plan.getId())) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "存在商品已关联其他上新计划");
                }
            }
        }
    }

    private void baseCheck(ItemLaunchPlanParam param) {
        String planName = param.getPlanName();
        Long launchTime = param.getLaunchTime();
        List<ItemLaunchPlanLinkItemParam> linkItems = param.getLinkItems();

        if (!StringUtils.hasText(planName)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "计划名称不能为空");
        }
        int nameLengthLimit = 50;
        if (planName.length() > nameLengthLimit) {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "计划名称不能超过50字符");
        }

        if (launchTime == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "上新时间必填");
        }

        final Integer businessLine = param.getBusinessLine();
        if (businessLine == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "合作模式必填");
        }
        IEnum.getEnumOptByValue(DivisionLevelValueEnum.class, businessLine)
            .filter(it -> it.getDivisionLevel() == DivisionLevelEnum.COOPERATION)
            .orElseThrow(
                () -> ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "合作模式不在有效范围内"));
        checkSelectedItemsBusinessLineConsistency(param, businessLine);

        final Optional<ItemLaunchPlan> itemLaunchPlanOptional = Optional.ofNullable(param.getPlanId())
                .map(this::getById);

        Long nowSecond = DateUtil.getNowSecond();
        final Long currentLaunchTime = itemLaunchPlanOptional.map(ItemLaunchPlan::getLaunchTime)
                .orElse(null);
        if ((currentLaunchTime == null || !currentLaunchTime.equals(launchTime))
                && launchTime <= nowSecond) {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "上新时间必须要大于现在时间");
        }

//        if (CollectionUtil.isEmpty(linkItems)) {
//            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "必须关联商品");
//        }

        if (CollectionUtil.isNotEmpty(linkItems)) {
            // 关联的商品校验
            List<Long> itemIds = linkItems.stream()
                    .map(ItemLaunchPlanLinkItemParam::getItemId)
                    .distinct()
                    .collect(Collectors.toList());
            List<Item> items = itemService.selectBatchByIds(itemIds);
            for (Item item : items) {
                //仅新增商品时校验状态
                if (linkItems.stream().filter(v -> Objects.equals(v.getItemId(), item.getId()))
                        .anyMatch(v -> Objects.isNull(v.getPlanItemRefId()))) {
                    if (Objects.equals(item.getStatus(), ItemStatus.OUT.getValue())
                            || Objects.equals(item.getStatus(), ItemStatus.DISCARD.getValue())) {
                        throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "存在商品状态为已下架或已废弃");
                    }
                    if (org.apache.commons.lang3.StringUtils.isBlank(item.getPartnerProviderItemSn())) {
                        throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, item.getCode() + "没关联P系统的商品，请再次检查确认");
                    }
                }
            }
            for (ItemLaunchPlanLinkItemParam linkItemParam : linkItems) {
                Long principalUserId = linkItemParam.getPrincipalUserId();
//                String principalUserName = linkItemParam.getPrincipalUserName();
                String remark = linkItemParam.getRemark();

                if (principalUserId == null) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "产品负责人必填");
                }
                int remarkLengthLimit = 200;
                if (remark != null && remark.length() > remarkLengthLimit) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "备注不能超过200字符");
                }
            }
        }
    }

    private void checkSelectedItemsBusinessLineConsistency(ItemLaunchPlanParam param, Integer businessLine) {
        final DivisionLevelValueEnum selectedCorp = DivisionLevelValueEnum.valueOf(businessLine);
        if (selectedCorp == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "合作方无效");
        }
        final List<Long> itemIds = param.getLinkItems()
                                        .stream()
                                        .map(ItemLaunchPlanLinkItemParam::getItemId)
                                        .collect(
                                                Collectors.toList());
        final Map<Long, Set<DivisionLevelValueEnum>> corpTypeMap = bizLevelDivisionService.getCorpType(BizUnionTypeEnum.SPU, itemIds);
        for (Long itemId : itemIds) {
            final Set<DivisionLevelValueEnum> corpTypes = corpTypeMap.get(itemId);
            if (corpTypes == null) {
                throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "仅能添加设置了合作方的商品");
            }
            if (!corpTypes.contains(selectedCorp)) {
                throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "上新计划不支持多种合作模式！");
            }
        }
    }

    private QueryWrapper<ItemLaunchPlanItemRef> getQueryWrapper2(ItemLaunchPlanLinkItemPageQuery pageQuery, ItemLaunchPlanLinkQueryParam param) {
        Long planId = pageQuery.getPlanId();
        String itemCode = pageQuery.getItemCode();
        String itemName = pageQuery.getItemName();

        if (planId == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "计划ID不能为空");
        }

        QueryWrapper<ItemLaunchPlanItemRef> queryWrapper = new QueryWrapper();
        queryWrapper
                .eq("ref.is_del", 0)
                .eq("ref.plan_id", planId)
                .like(StringUtils.hasText(itemName), "item.name", itemName)
                .eq(StringUtils.hasText(itemCode), "ref.item_code", itemCode);

        if (StringUtils.hasText(itemName)) {
            param.setUseItemTable(true);
        } else {
            param.setUseItemTable(false);
        }

        return queryWrapper;
    }

    private QueryWrapper<ItemLaunchPlan> getQueryWrapper(ItemLaunchPlanPageQuery query) {
        String name = query.getName();
        String no = query.getNo();
        Long timeStart = query.getTimeStart();
        Long timeEnd = query.getTimeEnd();
        Integer itemNum = query.getItemNum();
        Long id = query.getId();

        boolean timeQuery = true;
        if (timeStart == null || timeEnd == null) {
            timeQuery = false;
        }

        query.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(query.getBusinessLine()));
        QueryWrapper<ItemLaunchPlan> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ItemLaunchPlan::getIsDel, 0)
                .like(StringUtils.hasText(name), ItemLaunchPlan::getPlanName, name)
                .eq(Objects.nonNull(id), ItemLaunchPlan::getId, id)
                .eq(StringUtils.hasText(no), ItemLaunchPlan::getPlanNo, no)
                .eq(Objects.nonNull(itemNum), ItemLaunchPlan::getItemNum, itemNum)
                .ge(timeQuery, ItemLaunchPlan::getLaunchTime, timeStart)
                .le(timeQuery, ItemLaunchPlan::getLaunchTime, timeEnd)
                .in(ItemLaunchPlan::getBusinessLine, query.getBusinessLine());
        return queryWrapper;
    }

    @Override
    public void syncItemLaunchPlan(Long itemId, Long planId) {
        ItemLaunchPlan plan = getById(planId);
        if (Objects.isNull(plan)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "上新计划不存在");
        }
        Item item = itemService.getById(itemId);
        if (Objects.isNull(item)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "商品不存在");
        }

        Long originPlanId = 0L;
        ItemLaunchPlanItemRef itemLaunchPlanItemRef = itemLaunchPlanItemRefService.getItemLaunchPlanItemRef(itemId);
        if (Objects.nonNull(itemLaunchPlanItemRef)) {
            originPlanId = itemLaunchPlanItemRef.getPlanId();
            itemLaunchPlanItemRefService.deleteById(itemLaunchPlanItemRef.getId());
        }
        ItemLaunchPlanItemRef itemLaunchPlanItemRefNew = new ItemLaunchPlanItemRef();
        itemLaunchPlanItemRefNew.setPlanId(planId);
        itemLaunchPlanItemRefNew.setItemId(itemId);
        itemLaunchPlanItemRefNew.setItemCode(item.getCode());
        itemLaunchPlanItemRefNew.setRemark("");
        boolean save = itemLaunchPlanItemRefService.save(itemLaunchPlanItemRefNew);
        if (!save) {
            throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "更新失败");
        }
        item.setEstimateSaleTime(plan.getLaunchTime());
        itemService.updateById(item);
        //计算原计划人数和现计划人数
        if (!Objects.equals(originPlanId, 0L)) {
            updatePlanItemNum(originPlanId);
        }
        updatePlanItemNum(planId);
    }

    /**
     * 更新上新计划人数
     *
     * @param planId
     * @return
     */
    Boolean updatePlanItemNum(Long planId) {
        Integer itemNum = itemLaunchPlanItemRefService.getItemNum(planId);
        ItemLaunchPlan itemLaunchPlan = new ItemLaunchPlan();
        itemLaunchPlan.setId(planId);
        itemLaunchPlan.setItemNum(itemNum);
        return itemLaunchPlanMapper.updateById(itemLaunchPlan) > 0;
    }

    @Override
    public SingleResponse<Boolean> submit(Long id) {
        ItemLaunchPlan itemLaunchPlan = this.getById(id);
        Assert.notNull(itemLaunchPlan, "上新计划id非法");
        Assert.isTrue(itemLaunchPlan.getIsSubmit() == 0, "只允许操作未提交状态的数据");

        itemLaunchPlan.setIsSubmit(1);
        this.updateById(itemLaunchPlan);

        //MsgEvent event = MsgEvent.buildBySubmitPlan(id, UserContext.getUserId());
        //EventBusUtil.post(event);

        noticePlanSubmit(UserContext.getNickName(), itemLaunchPlan);

        return SingleResponse.of(true);
    }


    private List<StaffBrief> getProductGroupAndDesignGroupUsers() {
        final StaffListQuery query = new StaffListQuery();
        query.setDepts(Lists.newArrayList("产品组", "设计组"));
        query.setPageSize(49);
        final List<StaffInfo> staffInfos = userGateway.queryStaffList(query);
        if (CollectionUtil.isNotEmpty(staffInfos)) {
            return StaffAssembler.INST.longListToStaffBriefList(staffInfos.stream()
                                                                          .map(StaffInfo::getUserId)
                                                                          .collect(Collectors.toList()));
        }
        return Collections.emptyList();
    }

    @Override
    public void addPlanItem(Long planId, List<ItemLaunchPlanLinkItemParam> items, boolean notice) {
        final ItemLaunchPlan plan = getById(planId);
        Assert.notNull(plan, "计划不存在");

        checkItems(items, planId);

        List<ItemLaunchPlanItemRef> newAddRefs = new ArrayList<>();
        final ArrayList<ItemLaunchPlanItemRefInfo> refInfos = new ArrayList<>();
        for (ItemLaunchPlanLinkItemParam linkItem : items) {
            ItemLaunchPlanItemRef ref = convertToRef(linkItem, planId);
            newAddRefs.add(ref);
            final ItemLaunchPlanItemRefInfo itemLaunchPlanItemRefInfo = new ItemLaunchPlanItemRefInfo();
            itemLaunchPlanItemRefInfo.setPrincipalId(linkItem.getPrincipalUserId());
            BeanUtil.copyProperties(ref, itemLaunchPlanItemRefInfo);
            refInfos.add(itemLaunchPlanItemRefInfo);
        }
        itemLaunchPlanItemRefService.saveBatch(newAddRefs);

        for (ItemLaunchPlanItemRef newAddRef : newAddRefs) {
            itemDrawerService.updateItemDrawerType(newAddRef.getItemId(),
                    IEnum.getEnumByValue(LaunchItemType.class, newAddRef.getItemType()));
        }

        // 重新统计下上新计划关联的商品数量
        updateBatchPlanRefCount(Collections.singletonList(planId));

        // 同步到新品商品库
        newGoodsBizService.itemSync(refInfos, plan);

        // 商品的上新状态改为「待完善」
        List<Long> itemIds = newAddRefs.stream()
                                       .map(ItemLaunchPlanItemRef::getItemId)
                                       .distinct()
                                       .collect(Collectors.toList());
        for (Long itemId : itemIds) {
            itemBizService.updateLaunchStatus(itemId, ItemLaunchStatus.TO_BE_IMPROVED);
        }

        final Long launchTime = plan.getLaunchTime();

        //后端商品更新上新时间
        itemBizService.updateLaunchTime(itemIds, launchTime);

        final Long userId = UserContext.getUserId();
        final StaffBrief createUser;
        if (userId > 0) {
            createUser = StaffAssembler.INST.toStaffBrief(userId);
        } else {
            createUser = StaffBrief.systemUser();
        }
        if (notice) {
            noticeItemLaunchToImprove(itemIds, createUser, launchTime);
        }
    }
}
