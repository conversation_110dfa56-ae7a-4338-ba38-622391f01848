package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheet;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 盘货表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
public interface HandingSheetMapper extends DaddyBaseMapper<HandingSheet> {

    long selectPageListCount(@Param(Constants.WRAPPER) Wrapper<HandingSheet> queryWrapper);
    long selectPageListCountWithStatus(@Param(Constants.WRAPPER) Wrapper<HandingSheet> queryWrapper,
                                       @Param("state") Integer state);

    IPage<HandingSheet> selectPageList(@Param("page") Page<HandingSheet> page,
                                       @Param(Constants.WRAPPER) Wrapper<HandingSheet> queryWrapper);

    int updateStatusByHandingSheetId(@Param("handingSheetId") Long handingSheetId,
                                     @Param("newStatus") Integer newStatus,
                                     @Param("oldStatus") Integer oldStatus);

    HandingSheet getByHandingSheetItemId(@Param("handingSheetItemId") Long handingSheetItemId);

    HandingSheet getByHandingSheetActivityTextId(@Param("handingSheetActivityTextId") Long handingSheetActivityTextId);

    void updateToProcessing(@Param("nowSec") Long nowSec);
    void updateToExpired(@Param("nowSec") Long nowSec);
}
