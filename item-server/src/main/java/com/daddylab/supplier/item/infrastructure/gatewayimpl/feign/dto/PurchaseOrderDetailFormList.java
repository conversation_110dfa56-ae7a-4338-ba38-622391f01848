package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2024年04月22日 11:56 AM
 */
@Data
public class PurchaseOrderDetailFormList {


    private Long businessId;
    private Integer isGift;
    private String itemName;
    private Integer num;
    private String remark;
    private String skuCode;
    private String specifications;
    private BigDecimal taxPrice;
    private BigDecimal taxRate;
    private BigDecimal totalPriceTax;
    private String unit;
    private String wareHouseName;
}
