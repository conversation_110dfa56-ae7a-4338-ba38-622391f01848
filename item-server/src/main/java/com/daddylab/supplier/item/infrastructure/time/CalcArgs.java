package com.daddylab.supplier.item.infrastructure.time;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nonnull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/6/7
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class CalcArgs {
    @Builder.Default
    LocalDateTime now = LocalDateTime.now();
    @Nonnull
    Integer syncFrequency;
    int windowOffset;
    int startTimeOffset;
}
