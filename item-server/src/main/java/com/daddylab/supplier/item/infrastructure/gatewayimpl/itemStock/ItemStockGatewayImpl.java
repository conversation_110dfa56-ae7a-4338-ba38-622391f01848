package com.daddylab.supplier.item.infrastructure.gatewayimpl.itemStock;

import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.itemStock.enums.StockChangeType;
import com.daddylab.supplier.item.domain.itemStock.gateway.ItemStockGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.domain.itemStock.vo.ItemStockChange;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemStock;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemStockChangeLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemStockMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemStockChangeLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemStockService;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
public class ItemStockGatewayImpl implements ItemStockGateway {
    private static final String SKU_STOCK_CACHE = "sku_stock:";
    @Inject
    IItemStockService itemStockService;
    @Inject
    ItemStockMapper itemStockMapper;
    @Inject
    IItemStockChangeLogService itemStockChangeLogService;
    @Autowired
    ItemSkuGateway itemSkuGateway;
    @Autowired
    RedissonClient redissonClient;
    LoadingCache<Long, RAtomicLong> stockCaches = Caffeine.newBuilder()
            .expireAfterAccess(1, TimeUnit.MINUTES)
            .build((skuId) -> redissonClient.getAtomicLong(SKU_STOCK_CACHE + skuId));

    private RAtomicLong getStockCache(long skuId) {
        return Objects.requireNonNull(stockCaches.get(skuId));
    }

    @Override
    public long getSkuStock(long itemId, long skuId) {
        if (getStockCache(skuId).isExists()) {
            return getStockCache(skuId).get();
        }
        return getStock(itemId, skuId);
    }

    @Override
    public ItemSku getSkuStock(long itemId, String skuCode, String warehouseNo) {
        ItemSku itemSku = itemSkuGateway.getItemSku(itemId, skuCode, warehouseNo);
        if (itemSku == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "sku为" + skuCode + "的商品所选的仓库没有库存");
        }
        return itemSku;
    }

    public long getStock(long itemId, long skuId) {
        final ItemStock itemStock = itemStockService.lambdaQuery()
                .eq(ItemStock::getItemId, itemId)
                .eq(ItemStock::getSkuId, skuId)
                .last(" LOCK IN SHARE MODE")
                .select(ItemStock::getStock).one();
        final int stockNum = itemStock != null ? itemStock.getStock() : 0;
        getStockCache(skuId).set(stockNum);
        return stockNum;
    }

    @Override
    public long getItemTotalStock(long itemId) {
        return itemStockMapper.getItemTotalStock(itemId);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public boolean stockIn(long itemId, long skuId, int num) {
        final long beforeNum = getStockCache(skuId).get();

        //如果库存记录不存在则插入库存记录，如果库存记录存在则直接增加库存数量
        itemStockMapper.insertOrAddStock(itemId, skuId, num, UserContext.getUserId(), DateUtil.currentTime());

        //数据库写入成功后再增加缓存数量，保证一致性
        final long afterNum = getStockCache(skuId).addAndGet(num);

        //增加库存操作日志
        addStockChangeLog(skuId, StockChangeType.STOCK_IN, num, (int)beforeNum, (int)afterNum);

        return true;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public boolean stockOut(long itemId, long skuId, int num) {
        //库存不足
        final long beforeNum = getStockCache(skuId).get();
        if (beforeNum < num) {
            return false;
        }

        final int updateCount = itemStockMapper.stockOut(itemId, skuId, num, UserContext.getUserId(), DateUtil.currentTime());

        //出库失败（库存记录不存在或者库存不足）
        if (updateCount == 0) {
            return false;
        }

        //在缓存中扣减库存
        final long afterNum = getStockCache(skuId).getAndAdd(-num);

        //出库成功增加操作日志
        addStockChangeLog(skuId, StockChangeType.STOCK_OUT, num, (int)beforeNum, (int)afterNum);

        return true;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public boolean setStock(long itemId, long skuId, int num) {
        //设定库存与原先相同，直接忽略
        final long beforeNum = getStockCache(skuId).get();
        if (beforeNum == num) {
            return false;
        }

        //直接设定库存数量或者库存记录不存在则插入库存记录
        itemStockMapper.insertOrSetStock(itemId, skuId, num, UserContext.getUserId(), DateUtil.currentTime());

        //新增库存变更记录
        addStockChangeLog(skuId, StockChangeType.STOCK_SET, num, (int) beforeNum, num);

        //修改缓存
        getStockCache(skuId).set(num);
        return true;
    }

    @Override
    public List<ItemStockChange> getStockChangeLogs(long itemId, long skuId) {
        return itemStockMapper.getSkuStockChangeLogs(itemId, skuId);
    }

    @Override
    public boolean addStockChangeLog(long skuId, StockChangeType type, int change, int before, int after) {
        final ItemStockChangeLog itemStockChangeLog = new ItemStockChangeLog();
        itemStockChangeLog.setSkuId(skuId);
        itemStockChangeLog.setType(type);
        itemStockChangeLog.setChange(change);
        itemStockChangeLog.setBefore(before);
        itemStockChangeLog.setAfter(after);
        return itemStockChangeLogService.save(itemStockChangeLog);
    }

}
