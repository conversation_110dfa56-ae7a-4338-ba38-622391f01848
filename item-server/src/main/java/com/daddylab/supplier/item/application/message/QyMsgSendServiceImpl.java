package com.daddylab.supplier.item.application.message;

import com.daddylab.supplier.item.application.message.errors.QyMsgSendError;
import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.MsgTemplate;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatMsg;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MsgTemplateCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IMsgTemplateService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWechatMsgService;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.env.*;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/5/14
 */
@Service
@Slf4j
public class QyMsgSendServiceImpl implements QyMsgSendService, ApplicationContextAware {
    @Autowired
    private IMsgTemplateService msgTemplateService;
    @Autowired
    private IWechatMsgService wechatMsgService;

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public long send(RemindType remindType, MsgTemplateCode code, Map<String, Object> variables) {
        final MsgTemplate msgTemplate = msgTemplateService.getByCode(code)
                .orElseThrow(() -> new QyMsgSendError("消息模板未配置:" + code));
        if (msgTemplate.getStatus() == 0) {
            log.warn("[企微消息发送]模板已禁用:{}", variables);
            return 0L;
        }
        try {
            final WechatMsg wechatMsg = new WechatMsg();
            final PropertySourcesPropertyResolver propertyResolver = getPropertyResolver(variables);
            wechatMsg.setTitle(propertyResolver.resolvePlaceholders(msgTemplate.getTitle()));
            wechatMsg.setContent(propertyResolver.resolvePlaceholders(msgTemplate.getContent()));
            wechatMsg.setLink(propertyResolver.resolvePlaceholders(msgTemplate.getLink()));
            wechatMsg.setState(0);
            wechatMsg.setRecipient(getRecipient(msgTemplate, propertyResolver));
            wechatMsg.setType(remindType.getValue());
            wechatMsg.setRequirement("");
            wechatMsg.setTemplateCode(code.name());

            wechatMsgService.save(wechatMsg);

            return wechatMsg.getId();
        } catch (Exception e) {
            throw new QyMsgSendError("企微消息入库失败:" + e.getMessage(), e);
        }
    }

    @NonNull
    private static String getRecipient(MsgTemplate msgTemplate, PropertySourcesPropertyResolver propertyResolver) {
        final String receivers = propertyResolver.resolvePlaceholders(msgTemplate.getReceiver());
        return Arrays.stream(receivers.split(","))
                                     .filter(StringUtil::isNotBlank)
                                     .collect(Collectors.joining(","));
    }

    private PropertySourcesPropertyResolver getPropertyResolver(Map<String, Object> variables) {
        final MutablePropertySources propertySources = new MutablePropertySources();
        for (PropertySource<?> propertySource : ((ConfigurableEnvironment) applicationContext.getEnvironment()).getPropertySources()) {
            propertySources.addLast(propertySource);
        }
        propertySources.addFirst(new MapPropertySource("sessionVariables", variables));
        return new PropertySourcesPropertyResolver(propertySources);
    }
}
