package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceConfirm;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.EntryActivityPriceConfirmMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IEntryActivityPriceConfirmService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 入驻活动价格确认 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-06
 */
@Service
public class EntryActivityPriceConfirmServiceImpl extends DaddyServiceImpl<EntryActivityPriceConfirmMapper, EntryActivityPriceConfirm> implements IEntryActivityPriceConfirmService {

}
