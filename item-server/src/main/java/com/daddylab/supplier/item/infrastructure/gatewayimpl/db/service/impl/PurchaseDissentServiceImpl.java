package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseDissent;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseDissentMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseDissentService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 采购异议 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
@Service
public class PurchaseDissentServiceImpl extends DaddyServiceImpl<PurchaseDissentMapper, PurchaseDissent> implements IPurchaseDissentService {

}
