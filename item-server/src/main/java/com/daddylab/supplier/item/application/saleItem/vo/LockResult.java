package com.daddylab.supplier.item.application.saleItem.vo;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/6/5
 */
@ApiModel("锁定结果")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LockResult {
    @ApiModelProperty("是否锁定成功")
    Boolean lockSuccess;

    @ApiModelProperty("当前锁定用户")
    StaffInfo currentLockHolder;

    public static LockResult ofSuccess() {
        return new LockResult(true, null);
    }

    public static LockResult ofFail(StaffInfo currentLockHolder) {
        return new LockResult(false, currentLockHolder);
    }
}
