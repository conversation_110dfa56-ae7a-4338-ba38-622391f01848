package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 1:待提交、2:待审核、3:审核通过、4:审核拒绝、5:撤回审核、6:待完结、7:已完结,8:已关闭
 *
 * <AUTHOR> up
 * @date 2022/3/28 3:20 下午
 */
@Getter
@AllArgsConstructor
public enum PurchaseOrderState implements IEnum {

    /**
     * 消除黄线
     */
    WAIT_SUBMIT(1, "待提交"),
    WAIT_CHECK(2, "待审核"),

    PASS_AUDIT(3, "审核通过"),
    REJECT_AUDIT(4, "审核拒绝"),
    WITHDRAW_AUDIT(5, "撤回审核"),
    WAIT_FINISH(6, "待完结"),
    FINISHED(7, "已完结"),
    CLOSED(8, "已关闭"),

    FINISH_FIXED(10, "冲销流程-此单据是某一笔采购订单的修正完结单据"),
    HAD_REVERSE(11, "冲销流程-已经成功生成了逆向单据");

    @EnumValue
    private final Integer value;
    private final String desc;


}
