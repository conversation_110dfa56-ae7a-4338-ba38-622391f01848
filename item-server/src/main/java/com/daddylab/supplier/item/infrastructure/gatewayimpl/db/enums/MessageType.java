package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 2:47 下午
 * @description
 */
@AllArgsConstructor
@Getter
public enum MessageType implements IEnum<Integer> {

    SYSTEM(0, "系统消息"),

    ;
    @EnumValue
    private final Integer value;
    private final String desc;

}
