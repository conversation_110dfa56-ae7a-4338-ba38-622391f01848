/*
package com.daddylab.supplier.item.application.virtualWarehouse.dto;

import lombok.Data;
import org.javers.core.metamodel.annotation.Id;
import org.javers.core.metamodel.annotation.PropertyName;

*/
/**
 * <AUTHOR> up
 * @date 2024年03月18日 5:07 PM
 *//*

@Data
public class VirtualWarehouseCompareBo {

    @Id
    private String no;

    @PropertyName("名称")
    private String name;

    @PropertyName("描述")
    private String desc;

    @PropertyName("合作模式")
    private String businessLine;

    @PropertyName("状态")
    private String status;

    @PropertyName("库存模式")
    private String mode;

}
*/
