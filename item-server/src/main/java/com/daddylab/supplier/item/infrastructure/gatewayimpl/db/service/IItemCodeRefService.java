package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemCodeRef;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemCodeRefMapper;

import java.util.List;

/**
 * <p>
 * 各种商品编码与后端商品的关联记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
public interface IItemCodeRefService extends IDaddylabServicePlus<ItemCodeRef, ItemCodeRefMapper> {

    int refresh();

    List<ItemCodeRef> selectByCodes(List<String> codes);
}
