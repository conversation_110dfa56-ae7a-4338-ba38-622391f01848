package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Fold;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.FoldMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IFoldService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 折叠业务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
@Service
public class FoldServiceImpl extends DaddyServiceImpl<FoldMapper, Fold> implements IFoldService {

}
