package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtPreStockInOrder;

/**
 * <p>
 * 旺店通预入库单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
public interface IWdtPreStockInOrderService extends IDaddyService<WdtPreStockInOrder> {

}
