package com.daddylab.supplier.item.domain.order;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/5/5
 */
public interface OrderSensitiveInfoRepository {

    /**
     * 获取订单隐私数据
     *
     * @param orderNo 订单号
     * @return 隐私数据对象封装
     */
    OrderSensitiveInfo getOrderSensitiveInfo(String orderNo);

    /**
     * 批量获取订单隐私数据
     *
     * @param orderNos 订单号
     * @return 隐私数据对象封装
     */
    Map<String, OrderSensitiveInfo> getOrderSensitiveInfoBatch(Collection<String> orderNos);

    /**
     * 条件查询
     * @param query 查询条件封装
     */
    List<OrderSensitiveInfo> query(OrderSensitiveInfoQuery query);
}
