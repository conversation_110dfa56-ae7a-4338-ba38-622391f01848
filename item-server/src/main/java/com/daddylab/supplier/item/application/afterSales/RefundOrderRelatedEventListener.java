package com.daddylab.supplier.item.application.afterSales;

import com.alibaba.cola.exception.BizException;
import com.daddylab.supplier.item.application.refundOrder.RefundOrderService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.google.common.eventbus.Subscribe;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/11/1
 */
@Slf4j
@EventBusListener
@Component
@AllArgsConstructor
public class RefundOrderRelatedEventListener {

    private final RefundOrderService refundOrderService;
    private final AfterSalesWarehouseBizService afterSalesWarehouseBizService;

    @Subscribe
    public void onEvent(RefundOrderRelatedEvent event) {
        final AbnormalStateChangeEvent abnormalStateChangeEvent = event.getAbnormalStateChangeEvents()
                .stream().filter(v -> StringUtil.isNotBlank(v.getWarehouseNo())).findAny()
                .orElseThrow(
                        () -> ExceptionPlusFactory.sysException(
                                "退换单异常件关联事件数据异常:" + JsonUtil.toJson(event)));
        final AfterSalesEditReturnLogisticsCmd cmd = new AfterSalesEditReturnLogisticsCmd();
        cmd.setReturnOrderNo(event.getReturnOrderNo());
        cmd.setReturnLogisticsName(abnormalStateChangeEvent.getLogisticsName());
        cmd.setReturnLogisticsNo(abnormalStateChangeEvent.getLogisticsNo());
        try {
            refundOrderService.editReturnLogistics(cmd);
        } catch (Exception e) {
            log.error("退换单异常件关联事件处理，编辑退换单退回物流信息异常，编辑命令：{}", JsonUtil.toJson(cmd), e);
            Alert.text(MessageRobotCode.GLOBAL,
                    StringUtil.format("退换单异常件关联事件处理，编辑退换单退回物流信息异常，编辑命令：{}, 异常信息：{}",
                            JsonUtil.toJson(cmd), e instanceof BizException ? e.getMessage()
                                    : ExceptionUtil.getSimpleStackString(e)));
        }

    }

}
