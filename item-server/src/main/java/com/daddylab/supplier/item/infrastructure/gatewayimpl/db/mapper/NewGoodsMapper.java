package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.saleItem.query.NewGoodsQueryPage;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsPrincipalsInfoPO;
import com.daddylab.supplier.item.controller.item.dto.SkuWithNewGoodsDto;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.NewGoodsQcDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods;
import com.daddylab.supplier.item.types.item.ItemQcProcessorsVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 新品商品 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Repository
public interface NewGoodsMapper extends DaddyBaseMapper<NewGoods> {

    int count(@Param("queryPage") NewGoodsQueryPage queryPage);

    List<NewGoods> queryPage(@Param("queryPage") NewGoodsQueryPage queryPage);

    List<NewGoodsQcDO> selectNoticeDOList(@Param("itemIdList") List<Long> itemIdList);

    int countGroupBySpu(@Param("queryPage") NewGoodsQueryPage queryPage);

    List<SkuWithNewGoodsDto> selectDtoBatchBySkuCodes(@Param("skuCodes") List<String> skuCodes);

    List<NewGoodsPrincipalsInfoPO> getNewGoodsPrincipalsInfo(@Param("itemIds") Collection<Long> itemIds);

    ItemQcProcessorsVO getQcProcessors(@Param("itemId") Long itemId);

    Map<Integer, Integer> staticsCountByStatus(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("statusList") List<Integer> statusList);
}
