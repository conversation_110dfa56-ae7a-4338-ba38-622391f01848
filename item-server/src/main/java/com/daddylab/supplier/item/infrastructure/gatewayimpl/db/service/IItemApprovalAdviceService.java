package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemApprovalAdvice;

/**
 * <p>
 * 商品审批建议 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
public interface IItemApprovalAdviceService extends IDaddyService<ItemApprovalAdvice> {




}
