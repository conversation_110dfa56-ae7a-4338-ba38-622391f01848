package com.daddylab.supplier.item.domain.winrobot;

import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.winrobot360.Winrobot360API;
import com.daddylab.supplier.item.infrastructure.winrobot360.Winrobot360Config;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.GenericRsp;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TokenCreateRspData;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import java.time.Duration;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/9/21
 */
@AllArgsConstructor
@Slf4j
@Service
public class WinrobotTokenManagerImpl implements WinrobotTokenManager {

    private final Winrobot360API winrobot360API;
    private final Winrobot360Config winrobot360Config;
    private final LoadingCache<Winrobot360Config, String> tokenHolder = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofSeconds(7000)).build(this::requestToken);

    @Override
    public String token() {
        return "Bearer " + tokenHolder.get(winrobot360Config);
    }

    @Override
    public void refresh() {
        tokenHolder.invalidateAll();
    }

    private String requestToken(Winrobot360Config config) {
        final GenericRsp<TokenCreateRspData> tokenCreate = winrobot360API.tokenCreate(
                config.getAccessKeyId(), config.getAccessKeySecret());
        if (!tokenCreate.getSuccess()) {
            log.error("请求影刀token失败 {}", tokenCreate);
            throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "请求影刀token失败");
        }
        return tokenCreate.getData().getAccessToken();
    }
}
