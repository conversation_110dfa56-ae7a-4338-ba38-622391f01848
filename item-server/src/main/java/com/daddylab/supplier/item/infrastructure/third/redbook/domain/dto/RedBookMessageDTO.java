package com.daddylab.supplier.item.infrastructure.third.redbook.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @class RedBookMessageDTO.java
 * @description 描述类的作用
 * @date 2024-02-29 17:36
 */
@Data
public class RedBookMessageDTO implements Serializable {
    /**
     * 消息内容
     */
    private String data;
    /**
     * 消息类型
     */
    private String msgTag;
    /**
     * 商家店铺Id
     */
    private String sellerId;
}
