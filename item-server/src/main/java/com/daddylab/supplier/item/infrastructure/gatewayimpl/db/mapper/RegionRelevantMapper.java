package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.RegionRelevantDo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.RegionRelevant;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 地区关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@Repository
public interface RegionRelevantMapper extends DaddyBaseMapper<RegionRelevant> {

    /**
     * 根据区域编码查询区域名称
     *
     * @param codes
     * @return
     */
    List<RegionRelevantDo> getNameByCode(@Param("codes") Collection<String> codes);


}
