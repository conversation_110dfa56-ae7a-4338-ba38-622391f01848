package com.daddylab.supplier.item.infrastructure.limit;

import org.redisson.api.RSemaphore;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> up
 * @date 2024年05月27日 6:22 PM
 */
@Component
public class RateLimiterAccess {


    @Autowired
    private RedissonClient redissonClient;

    public RateLimiterAccess(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    public boolean acquire(String key, int permits) {
        RSemaphore semaphore = redissonClient.getSemaphore(key);
        return semaphore.tryAcquire(permits);
    }

    public void release(String key, int permits) {
        RSemaphore semaphore = redissonClient.getSemaphore(key);
        semaphore.release(permits);
    }
}
