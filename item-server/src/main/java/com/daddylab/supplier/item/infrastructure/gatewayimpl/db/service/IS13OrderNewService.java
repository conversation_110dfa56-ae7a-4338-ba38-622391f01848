package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.S13OrderNew;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 小红书订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
public interface IS13OrderNewService extends IDaddyService<S13OrderNew> {

}
