package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.ComposeSkuCmd;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuShortVO;
import com.daddylab.supplier.item.common.trans.CombinationItemTransMapper;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuPriceDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ComposeSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CombinationItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CombinationSkuMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemSkuMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IComposeSkuService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@Service
public class ComposeSkuServiceImpl extends DaddyServiceImpl<CombinationSkuMapper, ComposeSku> implements IComposeSkuService {

    @Autowired
    CombinationItemMapper combinationItemMapper;

    @Resource
    ItemSkuMapper itemSkuMapper;

    @Override
    public List<ComposeSkuShortVO> getShortVo(Long combinationId) {
        QueryWrapper<ComposeSku> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ComposeSku::getCombinationId, combinationId);
        final List<ComposeSku> composeSkuList = this.list(queryWrapper);
        if (CollUtil.isEmpty(composeSkuList)) {
            return new LinkedList<>();
        }

        List<ComposeSkuShortVO> composeSkuShortVOList = CombinationItemTransMapper.INSTANCE.skuDbToShortVos(composeSkuList);
        List<String> skuCodeList = composeSkuShortVOList.stream()
                .map(ComposeSkuShortVO::getSkuCode)
                .collect(Collectors.toList());
        Map<String, BigDecimal> skuCodePriceMap = itemSkuMapper.selectPrice(skuCodeList).stream()
                .collect(Collectors.toMap(SkuPriceDO::getSkuCode, SkuPriceDO::getCostPrice, (a, b) -> b));
        return composeSkuShortVOList.stream()
                .peek(val -> val.setCostPrice(skuCodePriceMap.getOrDefault(val.getSkuCode(), BigDecimal.ZERO)))
                .collect(Collectors.toList());
    }

    @Override
    public String getShortVoStr(Long combinationId) {
        List<ComposeSkuShortVO> list = getShortVo(combinationId);
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        List<String> collect = list.stream().map(ComposeSkuShortVO::toStr).collect(Collectors.toList());
        return StrUtil.join("-", collect);
    }

    @Override
    public void addByComposeSkuCmd(List<ComposeSkuCmd> composeSkuCmdList, Long combinationId) {
        if (CollUtil.isEmpty(composeSkuCmdList)) {
            return;
        }

//        List<String> skuCodeList = composeSkuCmdList.stream().map(ComposeSkuCmd::getSkuCode).collect(Collectors.toList());
//        itemSkuMapper.selectBySkuCodes(skuCodeList).

        List<ComposeSku> collect = composeSkuCmdList.stream().map(val -> {
            ComposeSku composeSku = new ComposeSku();
            composeSku.setCombinationId(combinationId);
            composeSku.setSkuId(val.getSkuId());
            composeSku.setSkuCode(val.getSkuCode());
            composeSku.setItemId(val.getItemId());
            composeSku.setItemCode(val.getItemCode());
            composeSku.setCount(val.getCount());
            composeSku.setCostProportion(val.getCostProportion());
            composeSku.setSaleProportion(val.getSalesProportion());
            return composeSku;
        }).collect(Collectors.toList());
        this.saveBatch(collect);

    }

    @Override
    public List<ComposeSku> selectByCombinationId(Long combinationId) {
        if (combinationId == null || combinationId <= 0) {
            return Collections.emptyList();
        }
        return lambdaQuery().eq(ComposeSku::getCombinationId, combinationId).list();
    }

    @Override
    public List<ComposeSku> selectByCombinationIds(List<Long> combinationIds) {
        if (CollUtil.isEmpty(combinationIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(ComposeSku::getCombinationId, combinationIds).list();
    }
}
