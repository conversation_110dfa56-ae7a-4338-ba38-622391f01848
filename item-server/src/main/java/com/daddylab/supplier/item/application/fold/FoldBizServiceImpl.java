package com.daddylab.supplier.item.application.fold;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.fold.dto.FoldCmd;
import com.daddylab.supplier.item.application.fold.dto.FoldVo;
import com.daddylab.supplier.item.common.trans.FoldTransMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Fold;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.FoldMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IFoldService;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @ClassName FoldBizServiceImpl.java
 * @description
 * @createTime 2022年06月07日 15:22:00
 */
@Slf4j
@Service
public class FoldBizServiceImpl implements FoldBizService{

    @Autowired
    private IFoldService foldService;

    @Autowired
    private FoldMapper foldMapper;

    @Override
    public Response saveOrUpdate(List<FoldCmd> cmds) {
        List<Fold> folds = FoldTransMapper.INSTANCE.cmdToDos(cmds);
        foldService.saveOrUpdateBatch(folds);
        return Response.buildSuccess();
    }

    @Override
    public MultiResponse<FoldVo> getFold(Integer type) {
        Assert.notNull(type, "类型不得为空");
        List<Fold> folds = getFoldByType(type);
        if (CollUtil.isEmpty(folds)){
            return MultiResponse.of(Collections.emptyList());
        }
        List<FoldVo> foldVos = FoldTransMapper.INSTANCE.doToVos(folds);
        return MultiResponse.of(foldVos);
    }

    @Override
    public List<Fold> getFoldByType(Integer type) {
        final LambdaQueryWrapper<Fold> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Fold::getType, type);
        return foldMapper.selectList(queryWrapper);
    }
}
