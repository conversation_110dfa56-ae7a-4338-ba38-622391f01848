package com.daddylab.supplier.item.application.purchasePayable.dto;

import lombok.Data;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年02月03日 11:47 AM
 */
@Data
public class ArtificialPayOrderSaveDto {

    /**
     * 申请付库单编码
     */
    private String payableApplyOrderNo;

    /**
     * 原采购应付单编码
     */
    private String sourcePurchasePayNo;


    @Valid
    private List<ArtificialPayOrderDetailSaveDto> detailSaveDtoList;

    // ----- job 参数

    private Long providerId;
    private String warehouseNo;


}
