package com.daddylab.supplier.item.application.item;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.daddylab.supplier.item.application.provider.ProviderBizService;
import com.daddylab.supplier.item.application.category.CategoryBizService;
import com.daddylab.supplier.item.application.shortlink.ShortLinkService;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.controller.provider.dto.ProviderVO;
import com.daddylab.supplier.item.controller.category.dto.CategoryQueryCmd;
import com.daddylab.supplier.item.controller.category.dto.CategoryVo;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BizUnionTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.types.item.OpenItemPageQuery;
import com.daddylab.supplier.item.types.item.PartnerRelatedItemsQuery;
import com.daddylab.supplier.item.types.item.TbIdToMallIdMappingQuery;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MultiMapUtils;
import org.apache.commons.collections4.MultiValuedMap;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/6/20
 */
@Service
@RequiredArgsConstructor
public class OpenItemServiceImpl implements OpenItemService {
    
    private final IItemService itemService;
    private final IItemSkuService itemSkuService;
    private final IItemLaunchPlanService itemLaunchPlanService;
    private final IProviderService providerService;
    private final IBrandService brandService;
    private final IItemDrawerService itemDrawerService;
    private final ShortLinkService shortLinkService;
    private final RefreshConfig refreshConfig;
    private final IPlatformItemService platformItemService;
    private final ProviderBizService providerBizService;
    private final CategoryBizService categoryBizService;
    private final IBizLevelDivisionService bizLevelDivisionService;
    private final IWarehouseService warehouseService;
    
    
    @Override
    public MultiResponse<PartnerRelatedItems> partnerRelatedItemsQuery(
            PartnerRelatedItemsQuery query) {
        final List<Item> items =
                itemService
                        .lambdaQuery()
                        .in(Item::getPartnerProviderItemSn, query.getPartnerSnList())
                        .list();
        if (items.isEmpty()) {
            return MultiResponse.of(Collections.emptyList());
        }
        return MultiResponse.of(
                toOpenItems(items, false).stream()
                        .collect(Collectors.groupingBy(OpenItem::getPartnerSn))
                        .entrySet()
                        .stream()
                        .map(v -> new PartnerRelatedItems(v.getKey(), v.getValue()))
                        .collect(Collectors.toList()));
    }
    
    @Override
    public PageResponse<OpenItem> pageQuery(OpenItemPageQuery query) {
        List<Long> itemIdsFilterBySku = Collections.emptyList();
        if (StrUtil.isNotBlank(query.getSkuName())
                || StrUtil.isNotBlank(query.getSkuCode())
                || CollUtil.isNotEmpty(query.getSkuCodeList())) {
            final ArrayList<String> skuCodes = new ArrayList<>();
            if (StrUtil.isNotBlank(query.getSkuCode())) {
                skuCodes.add(query.getSkuCode());
            }
            if (CollUtil.isNotEmpty(query.getSkuCodeList())) {
                skuCodes.addAll(query.getSkuCodeList());
            }
            itemIdsFilterBySku =
                    itemSkuService
                            .lambdaQuery()
                            .like(
                                    StrUtil.isNotBlank(query.getSkuName()),
                                    ItemSku::getSpecifications,
                                    query.getSkuName())
                            .and(
                                    CollUtil.isNotEmpty(skuCodes),
                                    q ->
                                            q.in(ItemSku::getSkuCode, skuCodes)
                                                    .or().in(ItemSku::getProviderSpecifiedCode, skuCodes))
                            .select(ItemSku::getItemId)
                            .list()
                            .stream()
                            .map(ItemSku::getItemId)
                            .distinct()
                            .collect(Collectors.toList());
            if (itemIdsFilterBySku.isEmpty()) {
                return ResponseFactory.emptyPage();
            }
        }
        List<Long> providerIdsFilter = Collections.emptyList();
        final boolean hasPartnerProviderId =
                Objects.nonNull(query.getPartnerProviderId()) && query.getPartnerProviderId() > 0;
        final boolean hasProviderName = StrUtil.isNotBlank(query.getProviderName());
        if (hasProviderName || hasPartnerProviderId) {
            providerIdsFilter =
                    providerService
                            .lambdaQuery()
                            .like(hasProviderName, Provider::getName, query.getProviderName())
                            .eq(
                                    hasPartnerProviderId,
                                    Provider::getPartnerProviderId,
                                    query.getPartnerProviderId())
                            .select(Entity::getId)
                            .list()
                            .stream()
                            .map(Entity::getId)
                            .distinct()
                            .collect(Collectors.toList());
            if (providerIdsFilter.isEmpty()) {
                return ResponseFactory.emptyPage();
            }
        }
        List<Long> brandIdsFilter = Collections.emptyList();
        if (StrUtil.isNotBlank(query.getBrandName())) {
            brandIdsFilter =
                    brandService
                            .lambdaQuery()
                            .like(Brand::getName, query.getBrandName())
                            .select(Entity::getId)
                            .list()
                            .stream()
                            .map(Entity::getId)
                            .distinct()
                            .collect(Collectors.toList());
            if (brandIdsFilter.isEmpty()) {
                return ResponseFactory.emptyPage();
            }
        }
        final IPage<Item> page = query.getPage();
        itemService
                .lambdaQuery()
                .and(
                        CollUtil.isNotEmpty(query.getItemCodeList()),
                        sub ->
                                sub.in(Item::getCode, query.getItemCodeList())
                                        .or()
                                        .in(
                                                Item::getProviderSpecifiedCode,
                                                query.getItemCodeList()))
                .like(StrUtil.isNotEmpty(query.getItemName()), Item::getName, query.getItemName())
                .in(
                        CollUtil.isNotEmpty(query.getPartnerSnList()),
                        Item::getPartnerProviderItemSn,
                        query.getPartnerSnList())
                .in(CollUtil.isNotEmpty(providerIdsFilter), Item::getProviderId, providerIdsFilter)
                .in(CollUtil.isNotEmpty(brandIdsFilter), Item::getBrandId, brandIdsFilter)
                .in(CollUtil.isNotEmpty(itemIdsFilterBySku), Item::getId, itemIdsFilterBySku)
                .in(CollUtil.isNotEmpty(query.getWarehouseNoList()), Item::getWarehouseNo, query.getWarehouseNoList())
                .eq(
                        query.getItemStatus() != null && query.getItemStatus() >= 0,
                        Item::getStatus,
                        query.getItemStatus())
                .page(page);
        final List<Item> items = page.getRecords();
        if (items.isEmpty()) {
            return ResponseFactory.emptyPage();
        }
        List<OpenItem> openItems = toOpenItems(items, query.getQuerySkuList());
        return ResponseFactory.ofPage(openItems, (int) page.getTotal(), (int) page.getCurrent(), (int) page.getSize());
    }
    
    private List<OpenItem> toOpenItems(List<Item> items, Boolean querySku) {
        final List<Long> itemIds = items.stream().map(Entity::getId).collect(Collectors.toList());
        final Map<Long, Long> estimateSaleTimeBatch =
                itemLaunchPlanService.getEstimateSaleTimeBatch(itemIds);
        final List<Long> providerIds =
                items.stream().map(Item::getProviderId).collect(Collectors.toList());
        final List<Provider> providers = providerService.listByIds(providerIds);
        final Map<Long, Provider> providerMap =
                providers.stream().collect(Collectors.toMap(Entity::getId, Function.identity()));
        final List<Long> brandIds =
                items.stream().map(Item::getBrandId).collect(Collectors.toList());
        final List<Brand> brands = brandService.listByIds(brandIds);
        final Map<Long, Brand> brandMap =
                brands.stream().collect(Collectors.toMap(Entity::getId, Function.identity()));
        List<String> warehouseNos = items.stream().map(Item::getWarehouseNo).collect(Collectors.toList());
        List<Warehouse> warehouses = warehouseService.getBatchByNos(warehouseNos);
        Map<String, Warehouse> warehouseMap =
                warehouses.stream().collect(Collectors.toMap(Warehouse::getNo, Function.identity()));
        // 抽屉相关
        final List<ItemDrawer> itemDrawerList =
                itemDrawerService
                        .lambdaQuery()
                        .select(ItemDrawer::getItemId, ItemDrawer::getWechatId)
                        .in(ItemDrawer::getItemId, itemIds)
                        .list();
        // 微信商城商品ID
        final Map<Long, String> wechatIdMap =
                itemDrawerList.stream()
                        .collect(Collectors.toMap(ItemDrawer::getItemId, ItemDrawer::getWechatId));
        Map<Long, List<CorpBizTypeDTO>> corpBizTypeMap =
                bizLevelDivisionService.getCorpBizType(BizUnionTypeEnum.SPU, itemIds);
        List<ItemSku> itemSkus = querySku ? itemSkuService.selectByItemIds(itemIds) : Collections.emptyList();
        Map<Long, List<ItemSku>> itemSkuListMap = itemSkus.stream().collect(Collectors.groupingBy(ItemSku::getItemId));
        return items.stream().map(item -> {
            final OpenItem openItem = Assembler.MAPPER.itemPoToOpenItem(item);
            openItem.setEstimateSaleTime(
                    estimateSaleTimeBatch.getOrDefault(
                            openItem.getItemId(), openItem.getEstimateSaleTime()));
            Optional.ofNullable(wechatIdMap.get(openItem.getItemId()))
                    .filter(StrUtil::isNotBlank)
                    .map(this::getWxMiniappUrl)
                    .ifPresent(openItem::setWechatLink);
            Optional.ofNullable(providerMap.get(openItem.getProviderId()))
                    .ifPresent(
                            provider -> {
                                openItem.setProvider(provider.getName());
                                openItem.setUnifySocialCreditCodes(
                                        provider.getUnifySocialCreditCodes());
                            });
            Optional.ofNullable(brandMap.get(openItem.getBrandId()))
                    .ifPresent(brand -> openItem.setBrand(brand.getName()));
            Optional.ofNullable(corpBizTypeMap.get(openItem.getItemId())).ifPresent(openItem::setCorpBizType);
            if (querySku) {
                openItem.setSkuList(Optional.ofNullable(itemSkuListMap.get(openItem.getItemId()))
                        .map(Assembler.MAPPER::toOpenItemSkuDTOList).orElseGet(Collections::emptyList));
            }
            Optional.ofNullable(warehouseMap.get(openItem.getWarehouseNo()))
                    .ifPresent(warehouse -> {
                        openItem.setWarehouseName(warehouse.getName());
                        openItem.setWarehouseStatus(warehouse.getState());
                    });
            return openItem;
        }).collect(Collectors.toList());
    }
    
    @Override
    public SingleResponse<Map<String, Collection<Long>>> tbIdToMallIdMapping(TbIdToMallIdMappingQuery query) {
        final MultiValuedMap<String, Long> results = MultiMapUtils.newSetValuedHashMap();
        final List<String> tbIds = query.getTbIds();
        final List<ItemDrawer> itemDrawerList = itemDrawerService.lambdaQuery()
                .in(ItemDrawer::getTbId, tbIds)
                .list();
        itemDrawerList.stream().sorted(Comparator.comparing(Entity::getId).reversed()).forEachOrdered(itemDrawer -> {
            final String tbId = itemDrawer.getTbId();
            if (StrUtil.isNotBlank(itemDrawer.getWechatId())) {
                try {
                    results.put(tbId, Long.parseLong(itemDrawer.getWechatId()));
                } catch (NumberFormatException ignored) {
                }
            }
        });
        
        final List<String> missingTbIds = tbIds.stream()
                .filter(tbId -> !results.containsKey(tbId))
                .collect(Collectors.toList());
        if (!missingTbIds.isEmpty()) {
            final List<PlatformItem> tbPlatformItems = platformItemService
                    .lambdaQuery()
                    .in(PlatformItem::getPlatform, Platform.TAOBAO)
                    .in(PlatformItem::getOuterItemId, missingTbIds)
                    .orderByDesc(PlatformItem::getId)
                    .list();
            final List<String> outerItemCodes = tbPlatformItems.stream()
                    .map(PlatformItem::getOuterItemCode)
                    .collect(Collectors.toList());
            if (!outerItemCodes.isEmpty()) {
                final List<PlatformItem> mallPlatformItems = platformItemService
                        .lambdaQuery()
                        .in(PlatformItem::getPlatform, Platform.LAOBASHOP)
                        .in(PlatformItem::getOuterItemCode, outerItemCodes)
                        .list();
                for (PlatformItem tbItem : tbPlatformItems) {
                    for (PlatformItem mallItem : mallPlatformItems) {
                        if (Objects.equals(tbItem.getOuterItemCode(), mallItem.getOuterItemCode())) {
                            try {
                                results.put(tbItem.getOuterItemId(), Long.parseLong(mallItem.getOuterItemId()));
                            } catch (NumberFormatException ignored) {
                            }
                            break;
                        }
                    }
                }
            }
        }
        
        final Map<String, Collection<Long>> finalResults = new HashMap<>(tbIds.size());
        for (String tbId : tbIds) {
            finalResults.put(tbId, Optional.ofNullable(results.get(tbId)).orElseGet(Collections::emptyList));
        }
        return SingleResponse.of(finalResults);
    }
    
    @NonNull
    private String getWxMiniappUrl(String tWechatItemId) {
        Assert.hasText(tWechatItemId, "生成小程序商品详情链接参数异常，商品ID为空");
        return shortLinkService.generate(
                String.format(refreshConfig.getWxMiniItemDetailUrl(), tWechatItemId));
    }
    
    @Mapper
    public interface Assembler {
        
        Assembler MAPPER = Mappers.getMapper(Assembler.class);
        
        @Mapping(target = "partnerSn", source = "partnerProviderItemSn")
        @Mapping(target = "itemStatus", source = "status")
        @Mapping(target = "itemId", source = "id")
        @Mapping(target = "itemCode", source = "code")
        @Mapping(target = "wechatLink", ignore = true)
        @Mapping(target = "brand", ignore = true)
        @Mapping(target = "unifySocialCreditCodes", ignore = true)
        @Mapping(target = "provider", ignore = true)
        @Mapping(target = "shortName", source = "shortName")
        OpenItem itemPoToOpenItem(Item item);
        
        OpenItemSku toOpenItemSkuDTO(ItemSku itemSku);
        
        List<OpenItemSku> toOpenItemSkuDTOList(List<ItemSku> itemSkuList);
    }
    
    @Override
    public SingleResponse<ProviderVO> queryProviderByItemId(Long id) {
        return providerBizService.queryDetail(id);
    }
    
    
    @Override
    public MultiResponse<CategoryVo> queryCategoryList(CategoryQueryCmd categoryPageQuery) {
        return categoryBizService.queryList(categoryPageQuery);
    }
}
