package com.daddylab.supplier.item.application.item.itemBatchProc;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemBatchProc;

import org.slf4j.event.Level;

/**
 * <AUTHOR>
 * @since 2023/11/16
 */
public interface ItemBatchProcHandler {
    void handle(ItemBatchProc batchProc, Synchronize synchronize);

    boolean isSupport(ItemBatchProc batchProc);

    interface Synchronize {
        void log(Level level, String msg, Object... vars);

        void setProcess(int process);
    }
}
