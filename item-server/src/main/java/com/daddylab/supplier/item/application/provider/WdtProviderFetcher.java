package com.daddylab.supplier.item.application.provider;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.setting.PurchaseProviderAPI;
import com.daddylab.mall.wdtsdk.apiv2.setting.dto.PurchaseProviderQueryDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.setting.dto.PurchaseProviderQueryDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.setting.dto.PurchaseProviderQueryDetailResponse.Details;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.FinanceInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ProviderStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.FinanceInfoMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ProviderMapper;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.google.common.base.Joiner;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/5/15
 */
@Component
@Slf4j
public class WdtProviderFetcher implements PageFetcher {

    private final WdtGateway wdtGateway;
    private final ProviderMapper providerMapper;
    private final FinanceInfoMapper financeInfoMapper;
    private final OperateLogDomainService operateLogDomainService;

    public WdtProviderFetcher(WdtGateway wdtGateway,
            ProviderMapper providerMapper,
            FinanceInfoMapper financeInfoMapper,
            OperateLogDomainService operateLogDomainService) {
        this.wdtGateway = wdtGateway;
        this.providerMapper = providerMapper;
        this.financeInfoMapper = financeInfoMapper;
        this.operateLogDomainService = operateLogDomainService;
    }

    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.WDT_PROVIDER;
    }

    @Override
    public int getTotal() {
        return query(null, null, 1, 1, true).getTotalCount();
    }

    @Override
    public void fetch(long pageIndex, long pageSize, RunContext runContext) {
        final PurchaseProviderQueryDetailResponse response = query(null, null, (int) pageIndex,
                (int) pageSize,
                false);
        handleResponse(response);
    }

    private void handleResponse(PurchaseProviderQueryDetailResponse response) {
        if (CollUtil.isEmpty(response.getDetails())) {
            return;
        }
        for (Details detail : response.getDetails()) {
            final String providerNo = detail.getProviderNo();
            Provider provider = providerMapper.getProviderPOByNoIncludeLogicDeleted(providerNo);
            if (provider != null) {
                final ArrayList<String> changes = new ArrayList<>();
                if (!StringUtil.equals(provider.getName(), detail.getProviderName())) {
                    changes.add("名称");
                    provider.setName(detail.getProviderName());
                }
                if (!StringUtil.equals(provider.getContactMobile(), detail.getMobile())) {
                    changes.add("联系方式");
                    provider.setContactMobile(detail.getMobile());
                }
                if (!StringUtil.equals(provider.getContact(), detail.getContact())) {
                    changes.add("联系人");
                    provider.setContact(detail.getContact());
                }
                if (!StringUtil.equals(provider.getAddress(), detail.getAddress())) {
                    changes.add("联系地址");
                    provider.setAddress(detail.getAddress());
                }
                final ProviderStatus status = !detail.getIsDisabled() ? ProviderStatus.COOPERATION
                        : ProviderStatus.STOP_COOPERATION;
                if (status != provider.getStatus()) {
                    changes.add("状态:" + Optional.ofNullable(provider.getStatus())
                            .map(ProviderStatus::getDesc).orElse("") + " -> " + status.getDesc());
                    provider.setStatus(status);
                }
                if (!changes.isEmpty()) {
                    operateLogDomainService
                            .addOperatorLog(0L, OperateLogTarget.PROVIDER, provider.getId(),
                                    "系统自动同步旺店通供应商改动：" + Joiner.on('、').join(changes));
                }
                return;
            }
            //测试环境如果发现编号不同但是名称相同，已旺店通供应商为准，之前的改个名字
            if (ApplicationContextUtil.isActiveProfile("test", "dev")) {
                handleSameNameProvider(detail);
            }
            provider = ASSEMBLER.INST.apiDoToPo(detail);
            try {
                providerMapper.insert(provider);

                final FinanceInfo financeInfo = new FinanceInfo();
                financeInfo.setProviderId(provider.getId());
                //发票类型(1:增值税专用发票 2:普通发票)，默认增值税专用发货
                financeInfo.setInvoiceType(1);
                //结算币别(1:人民币 2:美金)
                financeInfo.setCurrency(1);
                //税分类(1:一般纳税人 2:小规模纳税人)
                financeInfo.setTaxType(1);
                financeInfo.setTaxRate(new BigDecimal("0.13"));
                financeInfoMapper.insert(financeInfo);

                operateLogDomainService
                        .addOperatorLog(0L, OperateLogTarget.PROVIDER, provider.getId(),
                                "系统自动同步旺店通供应商");
            } catch (Exception e) {
                log.error("从旺店通同步供应商失败:" + e.getMessage() + " :" + JsonUtil.toJson(detail), e);
            }
        }
    }

    private void handleSameNameProvider(Details detail) {
        final LambdaQueryWrapper<Provider> nameQuery = Wrappers.lambdaQuery();
        nameQuery.eq(Provider::getName, detail.getProviderName());
        Provider providerSameName = providerMapper.selectOne(nameQuery);
        if (providerSameName != null) {
            providerSameName.setName("供应商" + providerSameName.getProviderNo());
            providerMapper.updateById(providerSameName);
        }
    }

    @Override
    public int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
        return query(startTime, endTime, 1, 1, true).getTotalCount();
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, long pageIndex, long pageSize,
            RunContext runContext) {
        final PurchaseProviderQueryDetailResponse response = query(startTime, endTime,
                (int) pageIndex, (int) pageSize,
                false);
        handleResponse(response);
    }

    private PurchaseProviderQueryDetailResponse query(LocalDateTime st, LocalDateTime et,
            int pageNo, int pageSize, boolean calcTotal) {
        try {
            final PurchaseProviderAPI api = wdtGateway.getAPI(PurchaseProviderAPI.class);
            final Pager pager = new Pager(pageSize, pageNo - 1, calcTotal);
            final PurchaseProviderQueryDetailParams params = new PurchaseProviderQueryDetailParams();
            params.setDeleted(0);
            if (st != null) {
                params.setModifiedBegin(DateUtil.format(st));
            }
            if (et != null) {
                params.setModifiedEnd(DateUtil.format(et));
            }
            return api.queryDetail(params, pager);
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapFetchException(e, fetchDataType());
        }
    }

    @Mapper(imports = {ProviderStatus.class})
    interface ASSEMBLER {

        ASSEMBLER INST = Mappers.getMapper(ASSEMBLER.class);

        @Mapping(target = "id", ignore = true)
        @Mapping(target = "unifySocialCreditCodes", constant = "")
        @Mapping(target = "type", ignore = true)
        @Mapping(target = "status", expression = "java(!details.getIsDisabled() ? ProviderStatus.COOPERATION : ProviderStatus.STOP_COOPERATION)")
        @Mapping(target = "provinceCode", ignore = true)
        @Mapping(target = "codeStr", ignore = true)
        @Mapping(target = "cityCode", ignore = true)
        @Mapping(target = "areaCode", ignore = true)
        @Mapping(target = "partnerProviderId", constant = "0L")
        @Mapping(target = "name", source = "providerName")
        @Mapping(target = "kingDeeNo", ignore = true)
        @Mapping(target = "kingDeeId", ignore = true)
        @Mapping(target = "isFromPartner", constant = "false")
        @Mapping(target = "contactMobile", source = "mobile")
        @Mapping(target = "createdUid", constant = "0L")
        @Mapping(target = "createdAt", ignore = true)
        @Mapping(target = "updatedUid", ignore = true)
        @Mapping(target = "updatedAt", ignore = true)
        @Mapping(target = "isDel", ignore = true)
        @Mapping(target = "deletedAt", ignore = true)
        Provider apiDoToPo(PurchaseProviderQueryDetailResponse.Details details);


    }

}
