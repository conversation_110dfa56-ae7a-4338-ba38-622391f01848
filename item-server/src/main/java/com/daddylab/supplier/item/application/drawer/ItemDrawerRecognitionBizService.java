package com.daddylab.supplier.item.application.drawer;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerModuleId;

/**
 * <AUTHOR>
 * @since 2022/12/6
 */
public interface ItemDrawerRecognitionBizService {

    void createRecognitionTasks(ItemAuditType type, Long itemId, Integer round, ItemDrawerModuleId[] moduleIds);
}
