package com.daddylab.supplier.item.infrastructure.gatewayimpl.refundPreStockInOrder;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.refundPreStockInOrder.WdtRefundPreStockInOrderRepository;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtPreStockInOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtPreStockInOrderDetails;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtPreStockInOrderDetailsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtPreStockInOrderService;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2022/8/19
 */
@Repository
public class WdtRefundPreStockInOrderRepositoryImpl implements
        WdtRefundPreStockInOrderRepository {

    private final IWdtPreStockInOrderService wdtPreStockInOrderService;
    private final IWdtPreStockInOrderDetailsService wdtPreStockInOrderDetailsService;

    public WdtRefundPreStockInOrderRepositoryImpl(
            IWdtPreStockInOrderService wdtPreStockInOrderService,
            IWdtPreStockInOrderDetailsService wdtPreStockInOrderDetailsService) {
        this.wdtPreStockInOrderService = wdtPreStockInOrderService;
        this.wdtPreStockInOrderDetailsService = wdtPreStockInOrderDetailsService;
    }


    @Transactional
    @Override
    public void saveOrUpdateWdtPreStockInOrderWithDetails(WdtPreStockInOrder wdtPreStockInOrder) {
        if (wdtPreStockInOrder == null) {
            return;
        }

        saveOrUpdateWdtPreStockInOrder(wdtPreStockInOrder);
        saveOrUpdateWdtPreStockInOrderDetails(wdtPreStockInOrder);
    }

    private void saveOrUpdateWdtPreStockInOrderDetails(WdtPreStockInOrder wdtPreStockInOrder) {
        final LambdaQueryWrapper<WdtPreStockInOrderDetails> removeDetailsCond = Wrappers.lambdaQuery();
        removeDetailsCond.eq(WdtPreStockInOrderDetails::getStockinNo,
                wdtPreStockInOrder.getStockinNo());
        wdtPreStockInOrderDetailsService.remove(removeDetailsCond);

        final List<WdtPreStockInOrderDetails> detailList = wdtPreStockInOrder.getDetailList();
        if (CollectionUtil.isEmpty(detailList)) {
            return;
        }

        wdtPreStockInOrderDetailsService.saveBatch(detailList);
    }

    private void saveOrUpdateWdtPreStockInOrder(WdtPreStockInOrder wdtPreStockInOrder) {
        final Long id = getOrderLocalId(wdtPreStockInOrder);
        wdtPreStockInOrder.setId(id);
        if (Objects.isNull(id)) {
            wdtPreStockInOrderService.save(wdtPreStockInOrder);
        } else {
            wdtPreStockInOrderService.updateById(wdtPreStockInOrder);
        }
    }

    private Long getOrderLocalId(WdtPreStockInOrder wdtPreStockInOrder) {
        return wdtPreStockInOrderService.lambdaQuery()
                .eq(WdtPreStockInOrder::getStockinNo, wdtPreStockInOrder.getStockinNo())
                .select(WdtPreStockInOrder::getId).oneOpt().map(WdtPreStockInOrder::getId)
                .orElse(null);
    }
}
