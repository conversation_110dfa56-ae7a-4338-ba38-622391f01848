package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableApplyOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchasePayableApplyOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchasePayableApplyOrderDetailService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 采购付款申请单详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Service
public class PurchasePayableApplyOrderDetailServiceImpl extends DaddyServiceImpl<PurchasePayableApplyOrderDetailMapper, PurchasePayableApplyOrderDetail> implements IPurchasePayableApplyOrderDetailService {

}
