package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName PlatformItemIdType.java
 * @description
 * @createTime 2022年04月29日 17:02:00
 */
@Getter
@AllArgsConstructor
public enum PlatformItemIdType implements IEnum<Integer> {

    MINI(0, "小程序ID"),

    MEMBERSHIP(1, "会员店ID"),

    BEAUTY(2, "美妆店ID"),

    INFANT(3, "母婴店ID"),

    CUSTOMIZE(4, "自定义价格");


    @EnumValue
    private final Integer value;

    private final String desc;


    public static PlatformItemIdType convert(String val) {
        return IEnum.getEnumByDesc(PlatformItemIdType.class, val);
    }
}