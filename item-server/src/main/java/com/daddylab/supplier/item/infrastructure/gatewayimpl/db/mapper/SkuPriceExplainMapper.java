package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.SkuPriceExplain;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * sku价格说明表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
public interface SkuPriceExplainMapper extends DaddyBaseMapper<SkuPriceExplain> {


    @Delete("delete from sku_price_explain where operate_time = #{time}")
    void deleteExpireData(@Param("time") String operateTime);

}
