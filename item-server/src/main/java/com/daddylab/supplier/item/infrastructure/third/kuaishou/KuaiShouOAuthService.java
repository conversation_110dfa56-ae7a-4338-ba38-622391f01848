package com.daddylab.supplier.item.infrastructure.third.kuaishou;

import com.kuaishou.merchant.open.api.response.oauth.KsAccessTokenResponse;

/**
 * <AUTHOR>
 * @class KuaiShouService.java
 * @description 描述类的作用
 * @date 2024-02-28 15:39
 * @see <a href="https://open.kwaixiaodian.com/zone/new/docs/api?name=integration.item.stock.query&version=1">
 * 快手api文档</a>
 */
public interface KuaiShouOAuthService {

    KsAccessTokenResponse getAccessToken(String code);

    KsAccessTokenResponse refreshAccessToken(String refreshToken);

    String getAuthorizationUrl(String redirectUrl, String state);
}
