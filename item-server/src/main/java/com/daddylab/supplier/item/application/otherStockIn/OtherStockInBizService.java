package com.daddylab.supplier.item.application.otherStockIn;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.controller.otherStockIn.dto.OtherStockInQueryPage;
import com.daddylab.supplier.item.controller.otherStockIn.dto.OtherStockInVo;

/**
 * <AUTHOR>
 * @ClassName OtherStockInService.java
 * @description
 * @createTime 2022年03月30日 15:55:00
 */
public interface OtherStockInBizService {

    /**
     * 查询列表
     *
     * @param queryPage
     * @return
     */
    PageResponse<OtherStockInVo> queryPage(OtherStockInQueryPage queryPage);

    /**
     * 根据id查详情
     * @param id
     * @return
     */
    SingleResponse<OtherStockInVo> getById(Long id);
}
