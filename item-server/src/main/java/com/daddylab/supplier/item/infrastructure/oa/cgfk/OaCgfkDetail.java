package com.daddylab.supplier.item.infrastructure.oa.cgfk;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@Data
public class OaCgfkDetail {
    /**
     * 富文本
     */
    @JsonProperty("采购单号")
    @NotBlank
    private String purchaseOrderNo;

    @JsonProperty("应付金额")
    @Positive
    private BigDecimal payableAmount;

    @JsonProperty("申请付款金额")
    @Positive
    private BigDecimal requestPaymentAmount;

    @JsonProperty("SKU数")
    @Positive
    private Integer skuNum;

    @JsonProperty("商品数量")
    @Positive
    private Integer itemNum;

    /**
     * 富文本
     */
    @JsonProperty("付款明细")
    @NotBlank
    private String purchaseDetails;

    @JsonProperty("采购日期结算周期")
    private String cycleDesc;

}
