package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemRunning;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 运营信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-29
 */
public interface IItemRunningService extends IDaddyService<ItemRunning> {

}
