package com.daddylab.supplier.item.infrastructure.third.kuaishou.impl;

import com.daddylab.supplier.item.infrastructure.third.convert.KuaiShouConverter;
import com.daddylab.supplier.item.infrastructure.third.enums.StockChangeTypeEnum;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.domain.query.KsItemPageQuery;
import com.kuaishou.merchant.open.api.domain.item.GetItemListResponseParam;
import com.kuaishou.merchant.open.api.domain.item.ItemGetResponseParam;
import com.kuaishou.merchant.open.api.domain.item.SkuInfoResponseParam;
import com.kuaishou.merchant.open.api.request.item.OpenItemGetRequest;
import com.kuaishou.merchant.open.api.request.item.OpenItemListGetRequest;
import com.kuaishou.merchant.open.api.request.item.OpenItemSkuListGetRequest;
import com.kuaishou.merchant.open.api.request.item.OpenItemSkuStockUpdateRequest;
import com.kuaishou.merchant.open.api.response.item.OpenItemGetResponse;
import com.kuaishou.merchant.open.api.response.item.OpenItemListGetResponse;
import com.kuaishou.merchant.open.api.response.item.OpenItemSkuListGetResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @class KuaiShouServiceImpl.java
 * @description 快手服务类
 * @date 2024-02-28 15:39
 */
@Slf4j
public class KuaiShouServiceImpl extends KuaiShouAbstract {

    @Override
    public GetItemListResponseParam getItemList(KsItemPageQuery itemPageQuery) {
        OpenItemListGetRequest openItemListGetRequest = KuaiShouConverter.INSTANCE.itemPageQueryToOpenItemListGetReques(
                itemPageQuery);
        OpenItemListGetResponse openItemListGetResponse = executeApiResponse(openItemListGetRequest);
        return openItemListGetResponse.getData();
    }

    @Override
    public ItemGetResponseParam getItemDetail(Long itemId) {
        OpenItemGetRequest openItemGetRequest = new OpenItemGetRequest();
        openItemGetRequest.setKwaiItemId(itemId);
        OpenItemGetResponse openItemGetResponse = executeApiResponse(openItemGetRequest);
        return openItemGetResponse.getData();
    }

    @Override
    public SkuInfoResponseParam getSkuDetail(Long itemId, Long skuId) {
        OpenItemSkuListGetRequest request = new OpenItemSkuListGetRequest();
        request.setKwaiItemId(itemId);
        request.setRelSkuId(skuId);
        OpenItemSkuListGetResponse skuListGetResponse = executeApiResponse(request);
        if (skuListGetResponse.getData().getSkuList().length == 0) {
            return null;
        }
        return skuListGetResponse.getData().getSkuList()[0];
    }

    @Override
    public Boolean skuStockUpdate(Long itemId, Long skuId, StockChangeTypeEnum changeType, Integer num) {
        OpenItemSkuStockUpdateRequest openItemSkuStockUpdateRequest = new OpenItemSkuStockUpdateRequest();
        openItemSkuStockUpdateRequest.setKwaiItemId(itemId);
        openItemSkuStockUpdateRequest.setSkuId(skuId);
        openItemSkuStockUpdateRequest.setChangeType(changeType.getValue());
        openItemSkuStockUpdateRequest.setSkuChangeStock(num);
        executeApiResponse(openItemSkuStockUpdateRequest);
        return true;
    }

    @Override
    public String decode(String message) {
        return decodeMessage(message);
    }





}
