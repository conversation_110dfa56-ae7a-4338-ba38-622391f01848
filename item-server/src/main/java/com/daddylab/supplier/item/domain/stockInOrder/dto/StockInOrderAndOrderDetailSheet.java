package com.daddylab.supplier.item.domain.stockInOrder.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/4/1 16:16
 **/
@Data
@HeadRowHeight(20)
@ContentRowHeight(16)
@EqualsAndHashCode(callSuper = false)
@ApiModel("采购入库单以及明细导出sheet")
public class StockInOrderAndOrderDetailSheet extends ExportSheet {

    @ColumnWidth(50)
    @ExcelProperty("入库单号")
    @ApiModelProperty(value = "入库单号")
    private String no;

    /**
     * 采购组织
     */
    @ColumnWidth(50)
    @ExcelProperty("采购组织")
    @ApiModelProperty(value = "采购组织")
    private String purchaseOrganization;

    @ColumnWidth(50)
    @ExcelProperty("合作方")
    @ApiModelProperty(value = "合作方")
    private String businessLineStr;


    @ColumnWidth(50)
    @ExcelProperty("业务类型")
    @ApiModelProperty(value = "业务类型")
    private String bizTypeStr;


    /**
     * 供应商
     */
    @ColumnWidth(50)
    @ExcelProperty("供应商")
    @ApiModelProperty(value = "供应商")
    private String provider;
    /**
     * 采购部门
     */
    @ColumnWidth(50)
    @ExcelProperty("采购部门")
    @ApiModelProperty(value = "采购部门")
    private String purchaseDepartment;
    /**
     * 采购组
     */
    @ColumnWidth(50)
    @ExcelProperty("采购组")
    @ApiModelProperty(value = "采购组")
    private String purchaseGroup;
    /**
     * 采购员
     */
    @ColumnWidth(50)
    @ExcelProperty("采购员")
    @ApiModelProperty(value = "采购员")
    private String buyerUserStr;

    @ExcelIgnore
    private Long buyerUser;

    /**
     * 采购单号
     */
    @ColumnWidth(50)
    @ExcelProperty("采购单号")
    @ApiModelProperty(value = "采购单号")
    private String purchaseOrderNo;
    /**
     * 商品skuCode
     */
    @ColumnWidth(50)
    @ExcelProperty("商品SKU")
    @ApiModelProperty(value = "商品SKU")
    private String itemSkuCode;

    /**
     * 商品名称
     */
    @ColumnWidth(50)
    @ExcelProperty("商品名称")
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    /**
     * 规格名称
     */
    @ColumnWidth(50)
    @ExcelProperty("规格名称")
    @ApiModelProperty(value = "规格名称")
    private String specifications;

    /**
     * 收料单位
     */
    @ColumnWidth(50)
    @ExcelProperty("收料单位")
    @ApiModelProperty(value = "收料单位")
    private String unit;

    /**
     * 交货数量
     */
    @ColumnWidth(50)
    @ExcelProperty("交货数量")
    @ApiModelProperty(value = "交货数量")
    private Integer receiptQuantity;

    /**
     * 实际入库数量
     */
    @ColumnWidth(50)
    @ExcelProperty("实际入库数量")
    @ApiModelProperty(value = "实际入库数量")
    private Integer realReceiptQuantity;



    /**
     * 入库仓库
     */
    @ColumnWidth(50)
    @ExcelProperty("入库仓库")
    @ApiModelProperty(value = "入库仓库")
    private String warehouseNo;

    /**
     * 是否赠品,0不是。1是
     */
    @ColumnWidth(50)
    @ExcelProperty("是否赠品")
    @ApiModelProperty(value = "是否赠品,0不是。1是")
    private String isGift;

    /**
     * 含税单价(6位)
     */
    @ColumnWidth(50)
    @ExcelProperty("含税单价")
    @ApiModelProperty(value = "单价(税前)")
    private BigDecimal taxPrice;

    /**
     * 税率(实际计算值，非百分比)
     */
    @ColumnWidth(50)
    @ExcelProperty("税率%")
    @ApiModelProperty(value = "税率(实际计算值，非百分比)")
    private BigDecimal taxRate;


    /**
     * 税额
     */
    @ColumnWidth(50)
    @ExcelProperty("税额")
    @ApiModelProperty(value = "税额")
    private BigDecimal taxQuota;

    /**
     * 价税合计
     */
    @ColumnWidth(50)
    @ExcelProperty("税前金额")
    @ApiModelProperty(value = "税前金额")
    private BigDecimal totalPriceTax;

    /**
     * 税后单价(6位)
     */
    @ColumnWidth(50)
    @ExcelProperty("税后单价")
    @ApiModelProperty(value = "税后单价")
    private BigDecimal afterTaxPrice;

    /**
     * 税后金额
     */
    @ColumnWidth(50)
    @ExcelProperty("税后金额")
    @ApiModelProperty(value = "税后金额")
    private BigDecimal afterTaxAmount;

    /**
     * 采购订单数量
     */
    @ColumnWidth(50)
    @ExcelProperty("采购订单数量")
    @ApiModelProperty(value = "采购订单数量")
    private Integer purchaseQuantity;


    /**
     * 备注
     */
    @ColumnWidth(255)
    @ExcelProperty("备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    @ExcelIgnore
    private Integer businessLine;


    /**
     * 预计到货日期
     */
    @ColumnWidth(50)
    @ExcelProperty("实际入库时间")
    @ApiModelProperty(value = "实际入库时间")
    private String receiptTime;
}
