package com.daddylab.supplier.item.infrastructure.cache;

import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import com.diffplug.common.base.Errors;
import com.github.benmanes.caffeine.cache.Caffeine;

import lombok.extern.slf4j.Slf4j;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2021/12/7
 */
@Aspect
@Slf4j
@Component
public class CacheAspect {

    public static final ConcurrentHashMap<Method, com.github.benmanes.caffeine.cache.Cache<Object, Object>> caffeineLoadingCaches = new ConcurrentHashMap<>();

    @Autowired
    private RefreshConfig refreshConfig;

    @Pointcut("@annotation(cache) && args(Object,..)")
    public void cachePointCut(Cache cache) {
    }

    @Around(value = "cachePointCut(cache)", argNames = "pjp,cache")
    @SuppressWarnings({"rawtypes", "unchecked"})
    public Object around(ProceedingJoinPoint pjp, Cache cache) {
        Method method = ((MethodSignature) pjp.getSignature()).getMethod();
        final Type genericReturnType = method.getGenericReturnType();
        final Supplier proceedSupplier = Errors.Rethrowing.rethrow()
                .wrap(() -> pjp.proceed(pjp.getArgs()));
        if (refreshConfig.isDisableCache()) {
            return proceedSupplier.get();
        }
        final Object[] args = pjp.getArgs();
        final Object arg = args[0];
        final String keyPart = cache.keySerialize().apply(arg);
        final String cacheKey =
                Optional.ofNullable(cache.keyPrefix())
                                .filter(v -> !v.isEmpty())
                                .orElseGet(method::getName)
                        + keyPart;
        switch (cache.cacheType()) {
            case REDIS:
                return RedisUtil.loadingCache(cacheKey, genericReturnType, cache.timeout(),
                        cache.timeUnit(), proceedSupplier);
            case CAFFEINE:
                return caffeineLoadingCaches.computeIfAbsent(method, m -> Caffeine.newBuilder()
                                .expireAfterWrite(cache.timeout(), cache.timeUnit())
                                .recordStats()
                                .build())
                        .get(cacheKey, k -> proceedSupplier.get());
            default:
                throw new UnsupportedOperationException("Unsupported CacheType");
        }
    }
}
