package com.daddylab.supplier.item.application.afterSaleLogistics;

import cn.hutool.core.collection.CollectionUtil;
import com.daddylab.mall.wdtsdk.apiv2.statistic.dto.SearchLogisticsTraceResponse;
import com.daddylab.supplier.item.application.afterSaleLogistics.domain.LogisticsTraceConvert;
import com.daddylab.supplier.item.application.afterSaleLogistics.domain.LogisticsTraceData;
import com.daddylab.supplier.item.application.afterSaleLogistics.domain.LogisticsTraceItem;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.LogisticsTraceDataSource;
import com.daddylab.supplier.item.application.wdtLogisticsTrace.WdtLogisticsTraceEvent;
import com.daddylab.supplier.item.domain.wdt.consts.PlatformMap;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.CloseReasonType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OrderLogisticsTraceStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsAbnormalityLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsAbnormalityService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsTraceService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.kuaidaoyun.KdyCallbackEvent;
import com.daddylab.supplier.item.types.kuaidaoyun.KdyTrackItem;
import com.daddylab.supplier.item.types.kuaidaoyun.KdyTrackList;
import com.daddylab.supplier.item.types.order.WdtOrderBatchSaveEvent;
import com.google.common.collect.ImmutableList;
import com.google.common.eventbus.Subscribe;

import java.util.*;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/5/22
 */
@EventBusListener
@Service
@Slf4j
public class AfterSaleLogisticsListener {
  @Autowired private IOrderLogisticsTraceService orderLogisticsTraceService;

  @Autowired private IOrderLogisticsAbnormalityService iOrderLogisticsAbnormalityService;

  @Autowired private IOrderLogisticsAbnormalityLogService orderLogisticsAbnormalityLogService;

  @Autowired private AfterSaleLogisticsBizService afterSaleLogisticsBizService;

  @Autowired private AbnormalScanHandler abnormalScanHandler;

  @Autowired private AfterSaleLogisticsConfig afterSaleLogisticsConfig;

  @Autowired private AfterSaleLogisticsConfig config;

  @Subscribe
  public void onSaveOrder(WdtOrderBatchSaveEvent orderWdtOrderBatchSaveEvent) {
    final Collection<WdtOrder> orders = orderWdtOrderBatchSaveEvent.getModels();
    if (orders != null) {
      log.debug(
          "收到订单保存事件，订单数量：{}，订单号：{}",
          orders.size(),
          orders.stream().map(WdtOrder::getSrcTids).collect(Collectors.toList()));
    }
    if (CollectionUtil.isNotEmpty(orders)) {
      if (CollectionUtil.isNotEmpty(orders)) {
        final List<Long> tradeIds =
            orders.stream().map(WdtOrder::getTradeId).collect(Collectors.toList());
        final List<OrderLogisticsTrace> traceList =
            orderLogisticsTraceService
                .lambdaQuery()
                .in(OrderLogisticsTrace::getWdtTradeId, tradeIds)
                .list();
        if (CollectionUtil.isNotEmpty(traceList)) {
          final ArrayList<OrderLogisticsTrace> tracesToUpdate = new ArrayList<>();
          final List<OrderLogisticsAbnormality> abnormalities = new LinkedList<>();
          for (OrderLogisticsTrace orderLogisticsTrace : traceList) {
            for (WdtOrder order : orders) {
              if (orderLogisticsTrace.getWdtTradeNo().equals(order.getTradeNo())) {
                final Long traceId = orderLogisticsTrace.getId();
                final Long consignTime0 = orderLogisticsTrace.getConsignTime();
                final String logisticsNo0 = orderLogisticsTrace.getLogisticsNo();
                final long consignTime = DateUtil.toTime(order.getConsignTime());
                final String logisticsNo = order.getLogisticsNo();
                final Optional<OrderLogisticsAbnormality> abnormalityOptional =
                    iOrderLogisticsAbnormalityService
                        .lambdaQuery()
                        .eq(OrderLogisticsAbnormality::getTraceId, traceId)
                        .oneOpt();
                orderLogisticsTrace.setConsignTime(consignTime);
                orderLogisticsTrace.setLogisticsNo(logisticsNo);
                orderLogisticsTrace.setLogisticsName(order.getLogisticsTypeName());
                orderLogisticsTrace.setPlatform(
                    PlatformMap.mapPlatform(order.getPlatformId()).getValue());
                orderLogisticsTrace.setStockoutWarehouseNo(order.getWarehouseNo());
                orderLogisticsTrace.setStockoutNo(order.getStockoutNo());
                // 只要订单有变更，就标记跟踪，由扫描时决定是否关闭跟踪
                orderLogisticsTrace.setOpenTrace(1);

                abnormalityOptional.ifPresent(
                    abnormality -> {
                      abnormality.setOrderAmount(order.getReceivable());
                      abnormality.setStockoutNo(order.getStockoutNo());
                      abnormality.setTradeTime(DateUtil.toTime(order.getTradeTime()));
                      abnormality.setConsignTime(DateUtil.toTime(order.getConsignTime()));
                      abnormality.setPayTime(DateUtil.toTime(order.getPayTime()));

                      abnormalities.add(abnormality);
                    });

                // 订单被删除（各种原因，拆单，合并）
                if (order.getDeletedAt() != null && order.getDeletedAt() > 0) {
                  orderLogisticsTrace.setCloseReason(CloseReasonType.ORDER_DELETED);
                  abnormalityOptional.ifPresent(
                      abnormality -> {
                        abnormality.setAbnormalStatus(0);
                        orderLogisticsAbnormalityLogService.newNonExceptionMsgLog(
                            abnormality.getId(), 6, "订单被删除");
                      });
                } else
                // 有发货时间和物流单号，且跟踪状态为未发货，将跟踪状态改为待订阅
                if (consignTime > 0
                    && StringUtil.isNotBlank(logisticsNo)
                    && orderLogisticsTrace.getTraceStatus()
                        == OrderLogisticsTraceStatus.NOT_DELIVERY) {
                  orderLogisticsTrace.setTraceStatusForLogisticsTrace(afterSaleLogisticsConfig);
                }
                // 物流单号有变化，说明订单被修改，需要重新订阅
                else if (StringUtil.isNotBlank(logisticsNo0)
                    && StringUtil.isNotBlank(logisticsNo)
                    && !Objects.equals(logisticsNo0, logisticsNo)
                    && consignTime != 0) {
                  orderLogisticsTrace.setTraceStatusForLogisticsTrace(afterSaleLogisticsConfig);
                  abnormalityOptional.ifPresent(
                      orderLogisticsAbnormality ->
                          orderLogisticsAbnormalityLogService.newNonExceptionMsgLog(
                              orderLogisticsAbnormality.getId(), 6, "物流单号发生修改"));
                }
                // 若订单存在退款状态，则暂停跟踪并且关闭异常
                else if (order.getRefundStatus() > 0) {
                  orderLogisticsTrace.setCloseReason(CloseReasonType.ORDER_REFUND);
                  abnormalityOptional.ifPresent(
                      orderLogisticsAbnormality ->
                          orderLogisticsAbnormalityLogService.newNonExceptionMsgLog(
                              orderLogisticsAbnormality.getId(), 6, "订单存在退款，暂停跟踪"));
                  abnormalityOptional.ifPresent(
                      abnormality -> {
                        abnormality.setAbnormalStatus(0);
                      });
                }
                // 之前有发货时间，现在没有，说明订单被驳回，无需继续跟踪
                else if (consignTime0 != 0 && consignTime == 0) {
                  orderLogisticsTrace.setCloseReason(CloseReasonType.ORDER_REJECT);
                  abnormalityOptional.ifPresent(
                      orderLogisticsAbnormality ->
                          orderLogisticsAbnormalityLogService.newNonExceptionMsgLog(
                              orderLogisticsAbnormality.getId(), 6, "订单状态回退，物流单号已取消"));
                  abnormalityOptional.ifPresent(
                      abnormality -> {
                        abnormality.setAbnormalStatus(0);
                      });
                }
                // 订单被冻结
                else if (StringUtil.isNotBlank(order.getFreezeReason())
                    && !"无".equals(order.getFreezeReason())) {
                  orderLogisticsTrace.setCloseReason(CloseReasonType.ORDER_FREEZED);
                  abnormalityOptional.ifPresent(
                      abnormality -> {
                        abnormality.setAbnormalStatus(0);
                        orderLogisticsAbnormalityLogService.newNonExceptionMsgLog(
                            abnormality.getId(), 6, "订单被冻结");
                      });
                }
                // 订单已完成
                else if (order.getTradeStatus() == 110) {
                  if (config.isStopTraceForOrderFinished()) {
                    orderLogisticsTrace.setOpenTrace(0);
                    orderLogisticsTrace.setCloseReason(CloseReasonType.ORDER_FINISHED);
                    abnormalityOptional.ifPresent(
                        abnormality -> {
                          abnormality.setAbnormalStatus(0);
                          abnormalities.add(abnormality);
                          orderLogisticsAbnormalityLogService.newNonExceptionMsgLog(
                              abnormality.getId(), 6, CloseReasonType.ORDER_FINISHED.getDesc());
                        });
                  }
                }
                // 订单状态变更，仅【待处理预订单、待客审 、已审核、已发货、成本确认、已过账、已完成】状态需要跟踪
                else if (!Arrays.asList(25, 30, 55, 95, 96, 101, 110)
                    .contains(order.getTradeStatus())) {
                  orderLogisticsTrace.setCloseReason(CloseReasonType.ORDER_STATUS);
                  abnormalityOptional.ifPresent(
                      orderLogisticsAbnormality ->
                          orderLogisticsAbnormalityLogService.newNonExceptionMsgLog(
                              orderLogisticsAbnormality.getId(),
                              6,
                              CloseReasonType.ORDER_STATUS.getDesc()));
                }
                tracesToUpdate.add(orderLogisticsTrace);
                break;
              }
            }
          }
          if (CollectionUtil.isNotEmpty(tracesToUpdate)) {
            orderLogisticsTraceService.updateBatchById(tracesToUpdate);
          }
          if (CollectionUtil.isNotEmpty(abnormalities)) {
            iOrderLogisticsAbnormalityService.updateBatchById(abnormalities);
          }
        }
      }
    }
  }

  @Subscribe
  public void onKdyCallback(KdyCallbackEvent event) {
    final KdyCallback callbackData = event.getData();
    orderLogisticsTraceService
        .lambdaQuery()
        .eq(OrderLogisticsTrace::getLogisticsNo, callbackData.getLogisticsNo())
        .oneOpt()
        .ifPresent(
            orderLogisticsTrace -> {
              if (orderLogisticsTrace.getTraceSource() != LogisticsTraceDataSource.KUAIDAOYUN) {
                return;
              }
              orderLogisticsTrace.setTrackTime(callbackData.getTrackTimeSeconds());
              orderLogisticsTrace.setTraceStatus(OrderLogisticsTraceStatus.KDY_SUBSCRIBED);
              orderLogisticsTrace.setOpenTrace(1);
              final KdyTrackList trackList = callbackData.getTracklist();

              // v3. 揽收时间 = 第一条物流轨迹的时间
              trackList.getTrackList().stream()
                  .min(Comparator.comparing(KdyTrackItem::getTrackDate))
                  .ifPresent(
                      val ->
                          orderLogisticsTrace.setPickingUpTime(
                              DateUtil.toTime(val.getTrackDate())));
              // 发运时间时间判断
              trackList.getTrackList().stream()
                  .filter(
                      v ->
                          ImmutableList.of("转运", "中转", "分拨", "集散", "处理中心", "中心", "车间").stream()
                              .anyMatch(x -> v.getTrackStatus().contains(x)))
                  .findFirst()
                  .ifPresent(
                      v -> orderLogisticsTrace.setSendingTime(DateUtil.toTime(v.getTrackDate())));
              // v3 新增派件时间判断获取
              trackList.getTrackList().stream()
                  .filter(
                      v -> {
                        boolean b1 =
                            ImmutableList.of("派送").stream()
                                .anyMatch(x -> v.getShortStatus().contains(x));
                        boolean b2 =
                            ImmutableList.of("派件").stream()
                                .anyMatch(x -> v.getTrackStatus().contains(x));
                        return b1 || b2;
                      })
                  .findFirst()
                  .ifPresent(
                      v ->
                          orderLogisticsTrace.setDistributeTime(DateUtil.toTime(v.getTrackDate())));
              // v3 新增签收时间判断获取
              trackList.getTrackList().stream()
                  .filter(
                      v ->
                          config.getReceiveShortStatus().stream()
                              .anyMatch(x -> v.getTrackStatus().contains(x)))
                  .findFirst()
                  .ifPresent(
                      v -> orderLogisticsTrace.setSigningTime(DateUtil.toTime(v.getTrackDate())));

              orderLogisticsTrace.setCallbackId(callbackData.getId());
              orderLogisticsTrace.setTraceSource(LogisticsTraceDataSource.KUAIDAOYUN);
              orderLogisticsTraceService.updateById(orderLogisticsTrace);

              abnormalScanHandler.handle(
                  orderLogisticsTrace,
                  LogisticsTraceConvert.INSTANCE.kdyCallbackToLogisticsTraceData(callbackData));
            });
  }

  @Subscribe
  public void onWdtCallback(WdtLogisticsTraceEvent event) {
    if (!config.isWdtLogisticsTraceEnabled()) {
      return;
    }
    final WdtLogisticsTrace callbackData = event.getData();
    final List<SearchLogisticsTraceResponse.DetailListItem> trackList =
        callbackData.getDetailListObj();
    if (trackList == null || trackList.isEmpty()) {
      return;
    }
    orderLogisticsTraceService
        .lambdaQuery()
        .eq(OrderLogisticsTrace::getLogisticsNo, callbackData.getLogisticsNo())
        .oneOpt()
        .ifPresent(
            orderLogisticsTrace -> {
              orderLogisticsTrace.setOpenTrace(1);

              final LogisticsTraceData logisticsTraceData =
                  LogisticsTraceConvert.INSTANCE.wdtLogisticsTraceToLogisticsTraceData(
                      callbackData);
              orderLogisticsTrace.setTrackTime(logisticsTraceData.getTrackTime());
              orderLogisticsTrace.setPickingUpTime(DateUtil.toTime(callbackData.getPickTime()));
              // 发运时间时间判断
              logisticsTraceData.getTrackList().stream()
                  .filter(
                      v ->
                          ImmutableList.of("转运", "中转", "分拨", "集散", "处理中心", "中心", "车间").stream()
                              .anyMatch(v::containKeyword))
                  .findFirst()
                  .map(LogisticsTraceItem::getTrackDate)
                  .ifPresent(orderLogisticsTrace::setSendingTime);
              // 派件时间判断获取
              logisticsTraceData.getTrackList().stream()
                  .filter(v -> ImmutableList.of("配送").stream().anyMatch(v::containKeyword))
                  .findFirst()
                  .map(LogisticsTraceItem::getTrackDate)
                  .ifPresent(orderLogisticsTrace::setDistributeTime);

              // 签收时间判断获取
              logisticsTraceData.getTrackList().stream()
                  .filter(v -> config.getReceiveShortStatus().stream().anyMatch(v::containKeyword))
                  .findFirst()
                  .map(LogisticsTraceItem::getTrackDate)
                  .ifPresent(orderLogisticsTrace::setSigningTime);

              // 优先取旺店通的物流轨迹
              orderLogisticsTrace.setCallbackId(callbackData.getId());
              orderLogisticsTrace.setTraceSource(LogisticsTraceDataSource.WDT);
              orderLogisticsTrace.setTraceStatus(OrderLogisticsTraceStatus.WDT_CALLBACK);
              orderLogisticsTraceService.updateById(orderLogisticsTrace);

              abnormalScanHandler.handle(orderLogisticsTrace, logisticsTraceData);
            });
  }
}
