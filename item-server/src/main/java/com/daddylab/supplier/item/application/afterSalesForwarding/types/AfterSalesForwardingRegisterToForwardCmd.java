package com.daddylab.supplier.item.application.afterSalesForwarding.types;

import com.daddylab.supplier.item.common.domain.dto.Command;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/27
 */
@Data
public class AfterSalesForwardingRegisterToForwardCmd extends Command {
    private static final long serialVersionUID = -7729310261103884782L;

    @ApiModelProperty(value = "登记ID")
    private List<Long> ids;
}
