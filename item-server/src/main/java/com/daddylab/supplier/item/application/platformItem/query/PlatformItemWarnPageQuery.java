package com.daddylab.supplier.item.application.platformItem.query;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("平台商品警告查询")
public class PlatformItemWarnPageQuery extends PageQuery {
    private static final long serialVersionUID = -5075643989521421695L;
    @PositiveOrZero(message = "平台商品ID应该是一个正整数")
    @ApiModelProperty("平台商品ID")
    Long platformItemId;

    @ApiModelProperty("外部品牌商品ID")
    String outerItemId;

    @PositiveOrZero(message = "商品ID应该是一个正整数")
    @ApiModelProperty("商品ID")
    Long itemId;

    @PositiveOrZero(message = "店铺ID应该是一个正整数")
    @ApiModelProperty("店铺ID")
    Long shopId;

    @ApiModelProperty("平台")
    Platform platform;

    @ApiModelProperty("商品状态")
    PlatformItemStatus itemStatus;

    @NotNull(message = "预警状态是必填字段")
    @ApiModelProperty(value = "预警状态", required = true)
    PlatformItemWarnStatus warnStatus;

    @ApiModelProperty("合作模式（业务线）多选")
    private List<Integer> businessLine = new ArrayList<>();

    @ApiModelProperty("平台预警查看全部权限")
    private boolean showAll;
}
