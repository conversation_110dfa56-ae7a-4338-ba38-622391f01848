package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetItemSku;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 盘货表SKU纬度数据（V2.4.5新版盘货表） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
public interface HandingSheetItemSkuMapper extends DaddyBaseMapper<HandingSheetItemSku> {

    int countItemSpuByHandingSheetId(@Param("handingSheetId") Long handingSheetId);
}
