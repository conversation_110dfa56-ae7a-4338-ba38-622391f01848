package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.google.common.base.Objects;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.Id;
import org.javers.core.metamodel.annotation.PropertyName;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 采购订单明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PurchaseOrderDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @DiffIgnore
    private Integer sortId;

    @TableId(value = "id", type = IdType.AUTO)
    @DiffIgnore
    private Long id;

    @TableField(fill = FieldFill.INSERT)
    @DiffIgnore
    private Long createdAt;

    @TableField(fill = FieldFill.UPDATE)
    @DiffIgnore
    private Long updatedAt;

    @TableField(fill = FieldFill.INSERT)
    @DiffIgnore
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    @DiffIgnore
    private Long updatedUid;

    @TableLogic
    @DiffIgnore
    private Integer isDel;

    private Long deletedAt;

    /**
     * 关联采购订单
     */
    @ApiModelProperty(value = "关联的采购订单")
    @DiffIgnore
    private Long purchaseOrderId;

    /**
     * 商品skuCode
     */
    @Id
    @ApiModelProperty(value = "商品skuCode")
    @DiffIgnore
    private String itemSkuCode;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    @PropertyName("商品id")
    @DiffIgnore
    private Long itemId;

    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    @PropertyName("规格")
    @DiffIgnore
    private String specifications;

    /**
     * 条码名称
     */
    @ApiModelProperty(value = "条码名称")
    @PropertyName("条码名称")
    @DiffIgnore
    private String barCode;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @PropertyName("单位")
    private String unit;

    /**
     * 采购数量
     */
    @ApiModelProperty(value = "采购数量")
    @PropertyName("采购数量")
    private Integer purchaseQuantity;

    /**
     * 含税单价
     */
    @ApiModelProperty(value = "含税单价")
    @PropertyName("含税单价")
    private BigDecimal taxPrice;

    /**
     * 税率(实际计算值，非百分比)
     */
    @ApiModelProperty(value = "税率")
    @PropertyName("税率")
    private BigDecimal taxRate;

    /**
     * 税额
     */
    @ApiModelProperty(value = "税额")
    @DiffIgnore
    private BigDecimal taxQuota;

    /**
     * 价税合计
     */
    @ApiModelProperty(value = "价税合计")
    @DiffIgnore
    private BigDecimal totalPriceTax;

    /**
     * 税后单价
     */
    @ApiModelProperty(value = "税后单价")
    @DiffIgnore
    private BigDecimal afterTaxPrice;

    /**
     * 税后金额
     */
    @ApiModelProperty(value = "税后金额")
    @DiffIgnore
    private BigDecimal afterTaxAmount;

    /**
     * 是否是赠品,0不是。1是
     */
    @ApiModelProperty(value = "是否是赠品,0不是。1是")
    @PropertyName("是否是赠品")
    private Integer isGift;

    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库编号")
    @PropertyName("仓库编号")
    private String warehouseNo;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @DiffIgnore
    private String remark;

    /**
     * 采购订单明细 根据skuCode+isGift组成唯一key
     */
    @Data
    @AllArgsConstructor
    public static class KeyDto {
        private String itemSkuCode;
        private Integer isGift;
        private BigDecimal taxPrice;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            KeyDto keyDto = (KeyDto) o;
            return Objects.equal(itemSkuCode, keyDto.itemSkuCode) && Objects.equal(isGift, keyDto.isGift) && Objects.equal(taxPrice, keyDto.taxPrice);
        }

        @Override
        public int hashCode() {
            return Objects.hashCode(itemSkuCode, isGift, taxPrice);
        }
    }

    /**
     * 判断 订单明细是否改变
     * 采购数量、含税单价、税率、是否赠品
     *
     * @param purchaseOrderDetail
     */
    public Boolean isChange(PurchaseOrderDetail purchaseOrderDetail) {
        if (!this.getPurchaseQuantity().equals(purchaseOrderDetail.getPurchaseQuantity())) {
            return true;
        }
        if (this.getTaxRate().compareTo(purchaseOrderDetail.getTaxRate()) != 0) {
            return true;
        }
        if (!this.isGift.equals(purchaseOrderDetail.getIsGift())) {
            return true;
        }
        return this.getTaxPrice().compareTo(purchaseOrderDetail.getTaxPrice()) != 0;
    }


}
