package com.daddylab.supplier.item.application.otherStockOutDetail;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockOutDetail;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OtherStockOutDetailBizService.java
 * @description
 * @createTime 2022年04月01日 16:25:00
 */
public interface OtherStockOutDetailBizService {

    /**
     * 根据itemId查询订单
     * @param itemId
     * @return
     */
    List<Long> getByItemId(Long itemId);

    /**
     * 根据brandId查询订单
     * @param brandId
     * @return
     */
    List<Long> getByBrandId(Long brandId);

    /**
     * 根据id查详情列表
     * @param id
     * @return
     */
    List<OtherStockOutDetail> getByOrderId(Long id);

    /**
     * 查询交集主订单id
     * @param sku
     * @param code
     * @param itemId
     * @param brandId
     * @return
     */
    List<Long> getIdsByItem(Long sku, String code, Long itemId, Long brandId, Collection<Integer> businessLines);

}
