package com.daddylab.supplier.item.application.platformItem.data;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/3/21
 */
@Data
public class SyncLogVO {
    @ApiModelProperty("平台商品ID")
    String outerItemId;
    @ApiModelProperty("平台商品SKU ID")
    String outerSkuId;
    @ApiModelProperty("商品SKU")
    String skuCode;
    @ApiModelProperty("平台")
    Platform platform;
    @ApiModelProperty("同步状态")
    String syncStatus;
    @ApiModelProperty("同步时间")
    Long syncTime;
    @ApiModelProperty("同步人")
    StaffBrief staffBrief;
    @ApiModelProperty("响应消息")
    String msg;
    @ApiModelProperty("同步前库存")
    private Integer beforeStock;
    @ApiModelProperty("同步库存")
    private Integer stock;
}
