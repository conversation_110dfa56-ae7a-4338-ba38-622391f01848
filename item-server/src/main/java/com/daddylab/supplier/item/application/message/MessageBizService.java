package com.daddylab.supplier.item.application.message;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.message.param.ConfigCmd;
import com.daddylab.supplier.item.application.message.param.ListPageQuery;
import com.daddylab.supplier.item.application.message.vo.ConfigVoList;
import com.daddylab.supplier.item.application.message.vo.MessageVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 2:25 下午
 * @description
 */
public interface MessageBizService {

    /**
     * 消息分页
     *
     * @param pageQuery
     * @return
     */
    PageResponse<MessageVO> pageMessages(ListPageQuery pageQuery);

    /**
     * 清除全部消息
     *
     * @param recipientId
     * @return
     */
    SingleResponse<Boolean> removeAll(Long recipientId);

    /**
     * 消息一键全读
     *
     * @param recipientId
     * @return
     */
    SingleResponse<Boolean> allRead(Long recipientId);

    /**
     * 读取消息
     *
     * @param messageId
     * @return
     */
    SingleResponse<Boolean> read(Long messageId);

    /**
     * 消息配置列表
     *
     * @return
     */
    SingleResponse<ConfigVoList> listMessageConfig();

    /**
     * 编辑消息配置，保存消息配置的改动
     *
     * @param configCmd
     * @return
     */
    SingleResponse<Boolean> saveConfig(ConfigCmd configCmd);

    /**
     * 未读消息数量
     *
     * @param recipientId
     * @return
     */
    SingleResponse<Integer> countNoRead(Long recipientId);


}
