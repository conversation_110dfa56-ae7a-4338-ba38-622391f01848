package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatMsg;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-03
 */
public interface WechatMsgMapper extends DaddyBaseMapper<WechatMsg> {

    int updateState(@Param("ids") Collection<Long> ids, @Param("from") int from, @Param("to") int to);
}
