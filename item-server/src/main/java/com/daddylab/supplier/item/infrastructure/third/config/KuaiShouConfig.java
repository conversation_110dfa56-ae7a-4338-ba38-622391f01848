package com.daddylab.supplier.item.infrastructure.third.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @class KuaiShouConfig.java
 * @description 描述类的作用
 * @date 2024-02-28 15:31
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "third.kuaishou")
public class KuaiShouConfig {
    private String appKey;
    private String appSecret;
    private String signSecret;
    private String apiUrl;
    private String messageSign;
    /**
     * 影刀任务ID
     */
    private String scheduleUuid;
    private String authorizationCallbackUrl;
}
