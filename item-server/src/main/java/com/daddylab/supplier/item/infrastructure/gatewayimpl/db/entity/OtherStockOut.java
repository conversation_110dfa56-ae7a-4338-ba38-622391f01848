package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 其他出库单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OtherStockOut implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 出库单号
     */
    private String orderNo;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 状态 0:全部 1:待处理 2:已取消 3:未确认 4:待审核 5:拣货中 6:已完成
     */
    private Integer status;

    /**
     * 其他出库原因 0:全部 1:无 2:退货少件出库 3:退件破损出库 4:工厂虚拟出库 5:线下下单 6:客服需求 7:调拨出库 8:临期出库 9:破损出库
     */
    private Integer otherReason;

    /**
     * 入库原因原始文本
     */
    private String reasonText;

    /**
     * 标记名称
     */
    private String markName;

    /**
     * 物流公司id
     */
    private Long logisticsId;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 邮费
     */
    private BigDecimal freight;

    /**
     * 发货时间
     */
    private Long deliveryAt;

    /**
     * 备注
     */
    private String remark;

    /**
     * 制单人
     */
    private String operatorName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除，不能做业务。这是软删除标记
     */
    @TableLogic
    private Integer isDel;

    private String kingDeeId;

    private Long deletedAt;

}
