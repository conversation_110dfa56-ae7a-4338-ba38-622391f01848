package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 入驻活动价格(SKU纬度)
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EntryActivityPriceSku implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic(value = "0", delval = "id")
    private Long isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 入驻活动价格商品ID
     */
    private Long priceItemId;

    /**
     * 活动时间(归属时间)
     */
    private Long activeStart;
    /**
     * 活动时间(归属时间)
     */
    private Long activeEnd;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 商品编码
     */
    private String itemCode;

    /**
     * sku id
     */
    private Long skuId;

    /**
     * sku编码
     */
    private String skuCode;

    /**
     * 合同销售价
     */
    private BigDecimal contractSalePrice;

    /**
     * 平台佣金
     */
    private BigDecimal platformCommission;

    /**
     * 活动备注
     */
    private String activityRemark;


}
