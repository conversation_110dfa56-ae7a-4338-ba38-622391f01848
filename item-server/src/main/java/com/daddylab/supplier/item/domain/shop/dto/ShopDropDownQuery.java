package com.daddylab.supplier.item.domain.shop.dto;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("店铺下拉列表查询")
public class ShopDropDownQuery extends PageQuery {

    private static final long serialVersionUID = -339720010390593803L;

    @ApiModelProperty("店铺ID")
    private Long id;

    @ApiModelProperty("非必填。如果存在值，搜索")
    private String name;

    @ApiModelProperty("合作模式（业务线）多选")
    private List<Integer> businessLine = new ArrayList<>();
    
    @ApiModelProperty("店铺编号列表")
    private List<String> snList;
}
