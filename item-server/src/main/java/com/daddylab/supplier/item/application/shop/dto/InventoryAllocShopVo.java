package com.daddylab.supplier.item.application.shop.dto;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryAllocShopStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2025年06月23日 4:44 PM
 */
@Data
public class InventoryAllocShopVo {

  @ApiModelProperty("设置id")
  private Long id;

  @ApiModelProperty("店铺id")
  private Long shopId;

  @ApiModelProperty("店铺编号")
  private String shopSn;

  @ApiModelProperty("店铺名称")
  private String shopName;

  //    @ApiModelProperty(hidden = true)
  //    private String businessLine;

  //    @ApiModelProperty("业务线")
  //    private List<Integer> businessLineList;

  //    @ApiModelProperty("店铺负责人列表")
  //    private List<DadStaffVO> principalList;

  @ApiModelProperty("店铺平台")
  private Platform platform;

  @ApiModelProperty("库存分配权重")
  private Integer inventoryWeight;

  @ApiModelProperty("状态")
  private InventoryAllocShopStatus status;

  @ApiModelProperty("低库存预警")
  private Integer warnThreshold;

  // ---------------

  @ApiModelProperty("下次库存自动分配时间")
  private Long nextAllocTime;

  private Long lastAllocTime;

  @ApiModelProperty("库存分配周期")
  private Integer allocInterval;

  @ApiModelProperty("下次库存同步时间")
  private Long nextSyncTime;

  private Long lastSyncTime;

  @ApiModelProperty("'库存同步周期'")
  private Integer syncInterval;



}
