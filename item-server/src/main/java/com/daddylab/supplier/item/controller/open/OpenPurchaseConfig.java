package com.daddylab.supplier.item.controller.open;

import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.NonNull;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR> up
 * @date 2024年04月29日 4:03 PM
 */
@Configuration
public class OpenPurchaseConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new HandlerInterceptor() {
            @Override
            public boolean preHandle(@NonNull HttpServletRequest request,
                                     @NonNull HttpServletResponse response, @NonNull Object handler) throws IOException {
                final String openAuth = request.getHeader("OPEN-AUTH");
                boolean ok = StringUtil.isNotBlank(openAuth) && StringUtil.equals(openAuth, "bqPNnZUdMmNAUUXbw9fX76f_w4ai!BN6");
                if (!ok) {
                    Response errorResp = Response.buildFailure(String.valueOf(HttpServletResponse.SC_UNAUTHORIZED), "Unauthorized access");
                    response.setContentType("application/json");
                    response.getWriter().write(JsonUtil.toJson(errorResp));
                    return false;
                }
                return true;
            }
        }).order(Ordered.HIGHEST_PRECEDENCE).addPathPatterns("/open/purchase/**");
    }
}
