package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OpenApp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OpenAppMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOpenAppService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 开放平台应用（仅部分平台店铺授权到了多个应用需使用此表，其它平台配置还是在配置中心维护） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
@Service
public class OpenAppServiceImpl extends DaddyServiceImpl<OpenAppMapper, OpenApp> implements IOpenAppService {

}
