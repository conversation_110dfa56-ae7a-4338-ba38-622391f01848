/*
package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.application.virtualWarehouse.dto.LockSkuRatioDto;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WarehouseGoodsInventoryLockStatics;

import java.util.List;
import java.util.Map;

*/
/**
 * <p>
 * 库存锁定数量统计 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 *//*

public interface IWarehouseGoodsInventoryLockStaticsService extends IDaddyService<WarehouseGoodsInventoryLockStatics> {


//    Integer handler(String warehouse, String skuCode, Integer addLockNum, Integer addLockRatio);

    void handlerBatch(List<LockSkuRatioDto> lockSkuRatioDtoList);

    Map<String, Integer> occpuyRatio(String warehouse);

}
*/
