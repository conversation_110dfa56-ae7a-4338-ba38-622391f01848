package com.daddylab.supplier.item.infrastructure.oa;

import lombok.Data;

import java.util.Map;

@Data
public class OaAttachment {
    private String officeTransformEnable;
    private Boolean jsonMemberNew;
    private String extension;
    private Map<String, Object> extraMap;
    private String icon;
    private String createdate;
    private String description;
    private String mimeType;
    private Integer sort;
    private Integer type;
    private String genesisId;
    private String reference;
    private String filename;
    private String size;
    private String iconFont;
    private String subReference;
    private String v;
    private String obsObjectKey;
    private String fileUrl;
    private String id;
    private Integer category;
}
