package com.daddylab.supplier.item.application.enterprise;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.enterprise.query.LaunchCodeQuery.SingleChat;

import com.daddylab.supplier.item.domain.enterprise.WxCpFeignClient;
import com.daddylab.supplier.item.domain.enterprise.query.LaunchCodeQuery;
import com.daddylab.supplier.item.domain.enterprise.vo.LaunchCodeVO;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.WxCpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * Class  EnterpriseServiceImpl
 *
 * @Date 2022/5/30下午9:31
 * <AUTHOR>
 */
@Service
@Slf4j
public class EnterpriseServiceImpl implements EnterpriseService {

    @Autowired
    private WxCpService wxCpService;
    @Autowired
    private WxCpFeignClient wxCpFeignClient;
    @Autowired
    private StaffService staffService;

    @Override
    public SingleResponse<String> getLaunchCode(Long userId, String chatUserId) {
        DadStaffVO staff = staffService.getStaff(userId);
        if (Objects.isNull(staff)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "职工信息不存在");
        }
        String launchCode = "";
        try {
            LaunchCodeVO launchCodeVO = wxCpFeignClient.getLaunchCode(wxCpService.getAccessToken(), LaunchCodeQuery.of(staff.getQwUserId(), chatUserId));

            if (launchCodeVO.getErrcode() != 0) {
                throw ExceptionPlusFactory.bizException(ErrorCode.THIRD_PARAM_ERROR, launchCodeVO.getErrmsg());
            }
            launchCode = launchCodeVO.getLaunchCode();
        } catch (Exception e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.THIRD_PARAM_ERROR, e.getMessage());
        }
        return SingleResponse.of(launchCode);
    }
}
