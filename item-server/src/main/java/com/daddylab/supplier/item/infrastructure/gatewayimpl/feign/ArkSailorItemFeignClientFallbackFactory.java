package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Proxy;

/**
 * <AUTHOR>
 * @since 2024/4/28
 */
@Slf4j
@Component
public class ArkSailorItemFeignClientFallbackFactory implements FallbackFactory<ArkSailorItemFeignClient> {
    private static ArkSailorItemFeignClient arkSailorItemFeignClient;

    @Override
    public ArkSailorItemFeignClient create(Throwable cause) {
        if (arkSailorItemFeignClient != null) {
            return arkSailorItemFeignClient;
        }
        arkSailorItemFeignClient = (ArkSailorItemFeignClient) Proxy.newProxyInstance(this.getClass().getClassLoader(),
                new Class[]{ArkSailorItemFeignClient.class},
                (proxy, method, args) -> {
                    log.error("[小程序商城接口] {} 请求异常: {}", method.getName(), cause.getMessage(), cause);
                    throw cause;
                });
        return arkSailorItemFeignClient;
    }
}
