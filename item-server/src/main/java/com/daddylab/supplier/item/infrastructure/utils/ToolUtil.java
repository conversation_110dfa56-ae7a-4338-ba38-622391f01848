package com.daddylab.supplier.item.infrastructure.utils;

import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: <PERSON>
 * @Date: 2022/6/13 13:34
 * @Description: 实用工具
 */
public class ToolUtil {
    /**
     * <p>Description: 内存分页 </p>
     *
     * @param records  待分页的数据
     * @param pageNum  当前页码
     * @param pageSize 每页显示的条数
     * @return 分页之后的数据
     */
    public static <T> List<T> pagination(List<T> records, int pageNum, int pageSize) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        if (pageNum < 0 || pageSize < 0) {
            return Collections.emptyList();
        }
        int totalCount = records.size();
        int pageCount;
        int remainder = totalCount % pageSize;
        if (remainder > 0) {
            pageCount = totalCount / pageSize + 1;
        } else {
            pageCount = totalCount / pageSize;
        }
        if (remainder == 0) {
            records = records.stream()
                    .skip((pageNum - 1) * pageSize)
                    .limit(pageSize * pageNum)
                    .collect(Collectors.toList());
            return records;
        } else {
            if (pageNum == pageCount) {
                records = records.stream()
                        .skip((pageNum - 1) * pageSize)
                        .limit(totalCount)
                        .collect(Collectors.toList());
                return records;
            } else {
                records = records.stream()
                        .skip((pageNum - 1) * pageSize)
                        .limit(pageSize * pageNum)
                        .collect(Collectors.toList());
                return records;
            }
        }
    }
}
