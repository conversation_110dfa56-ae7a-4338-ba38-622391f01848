package com.daddylab.supplier.item.application.afterSaleLogistics.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/1/2
 */
@Data
public class AfterSaleLogisticsExportVO {
  // 订单编号（原始）
  @ExcelProperty(value = "订单编号（原始）", index = 0)
  private String srcOrderNo;

  // 所属平台
  @ExcelProperty(value = "所属平台", index = 1)
  private String platform;

  // 所属店铺
  @ExcelProperty(value = "所属店铺", index = 2)
  private String shop;

  // 下单时间
  @ExcelProperty(value = "下单时间", index = 3)
  private String orderTime;

  // 发货时间
  @ExcelProperty(value = "发货时间", index = 4)
  private String deliveryTime;

  // 出库单号
  @ExcelProperty(value = "出库单号", index = 5)
  private String stockOutNo;

  // 出库仓库
  @ExcelProperty(value = "出库仓库", index = 6)
  private String stockOutWarehouse;

  // 物流公司
  @ExcelProperty(value = "物流公司", index = 7)
  private String logisticsCompanyName;

  // 物流单号
  @ExcelProperty(value = "物流单号", index = 8)
  private String logisticsNo;

  // 物流状态
  @ExcelProperty(value = "物流状态", index = 9)
  private String logisticsStatus;

  // 物流轨迹
  @ExcelProperty(value = "物流轨迹", index = 10)
  private String logisticsTrace;

  // 异常状态
  @ExcelProperty(value = "异常状态", index = 11)
  private String abnormalStatus;

  // 异常类型
  @ExcelProperty(value = "异常类型", index = 12)
  private String abnormalType;

  // 分配状态
  @ExcelProperty(value = "分配状态", index = 13)
  private String distributeStatus;

  // 负责客服
  @ExcelProperty(value = "负责客服", index = 14)
  private String csUsers;

  // 跟进处理次数
  @ExcelProperty(value = "跟进处理次数", index = 15)
  private String handleCount;

  // 最近跟进处理时间
  @ExcelProperty(value = "最近跟进处理时间", index = 16)
  private String handleTime;

  // 是否关闭预警
  @ExcelProperty(value = "是否关闭预警", index = 17)
  private String isCloseWarning;

  // 关闭预警时间
  @ExcelProperty(value = "关闭预警时间", index = 18)
  private String closeTime;

  // 关闭预警原因
  @ExcelProperty(value = "关闭预警原因", index = 19)
  private String closeReason;

  // 关闭预警备注
  @ExcelProperty(value = "关闭预警备注", index = 20)
  private String closeRemark;

  // 商品SKU（多个）
  @ExcelProperty(value = "商品SKU", index = 21)
  private String skuNames;
}
