package com.daddylab.supplier.item.domain.banniu;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.banniu.BanniuColumn;
import com.daddylab.supplier.item.types.banniu.Condition;
import com.daddylab.supplier.item.types.banniu.MiniTaskQuery;
import com.daddylab.supplier.item.types.banniu.TaskStatus;
import com.daddylab.third.banniu.starter.BanniuProperties;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.convert.ApplicationConversionService;
import org.springframework.core.convert.ConversionService;
import org.springframework.core.convert.TypeDescriptor;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;
import wjb.open.api.ApiException;
import wjb.open.api.WjbClient;
import wjb.open.api.model.banniu.OptionVo;
import wjb.open.api.model.mini.Conditions;
import wjb.open.api.request.mini.*;
import wjb.open.api.response.WjbAbstractResponse;
import wjb.open.api.response.mini.*;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/21
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BanniuMiniServiceImpl implements BanniuMiniService {
    private final WjbClient wjbClient;
    private final BanniuProperties banniuProperties;
    private final LoadingCache<Integer, List<MiniColumnListResponse.ColumnOption>>
            columnOptionCache =
                    Caffeine.newBuilder()
                            .expireAfterWrite(60, TimeUnit.SECONDS)
                            .build(this::doGetColumnOptions);

    @Override
    @SneakyThrows
    public List<MiniProjectListResponse.Project> projects() {
        final MiniProjectListRequest miniProjectListRequest = new MiniProjectListRequest();
        final MiniProjectListResponse miniProjectListResponse =
                wjbClient.execute(miniProjectListRequest, banniuProperties.getAccessToken());
        return Optional.ofNullable(miniProjectListResponse.getResult())
                .orElseThrow(() -> new ApiException("班牛【PROJECTS】查询异常"));
    }

    LoadingCache<Integer, List<MiniProjectListResponse.Project>> projectsCache =
            Caffeine.newBuilder()
                    .expireAfterAccess(1, TimeUnit.MINUTES)
                    .build(ignored -> projects());

    @Override
    @SneakyThrows
    public Optional<MiniProjectListResponse.Project> project(int projectId) {
        return Objects.requireNonNull(projectsCache.get(0)).stream()
                .filter(project -> Objects.equals(project.getProject_id(), projectId))
                .findAny();
    }

    @Override
    @SneakyThrows
    public List<MiniColumnListResponse.ColumnOption> columnOptions(int projectId) {
        return columnOptions(projectId, true);
    }

    private List<MiniColumnListResponse.ColumnOption> doGetColumnOptions(int projectId)
            throws ApiException {
        final MiniColumnListRequest miniColumnListRequest = new MiniColumnListRequest();
        miniColumnListRequest.setProjectId(projectId);
        final MiniColumnListResponse columnListResponse =
                wjbClient.execute(miniColumnListRequest, banniuProperties.getAccessToken());
        return Optional.ofNullable(columnListResponse.getResult())
                .orElseThrow(() -> new ApiException("班牛【COLUMN OPTIONS】查询异常"));
    }

    @Override
    public List<MiniColumnListResponse.ColumnOption> columnOptions(int projectId, boolean cache) {
        if (!cache) {
            columnOptionCache.invalidate(projectId);
        }
        return columnOptionCache.get(projectId);
    }

    @Override
    @SneakyThrows
    public MiniQueryTaskListResponse.ResultMap tasks(MiniTaskQuery query) {
        final MiniQueryTaskListRequest miniQueryTaskListRequest = toMiniQueryTaskListRequest(query);
        final MiniQueryTaskListResponse taskListResponse =
                wjbClient.execute(miniQueryTaskListRequest, banniuProperties.getAccessToken());
        return Optional.ofNullable(taskListResponse.getMap())
                .filter(v -> v.getResult() != null)
                .orElseGet(this::emptyResultMap);
    }

    @Override
    @SneakyThrows
    public long taskCreate(int projectId, int userId, Object data) {
        final MiniTaskCreateRequest miniTaskCreateRequest = new MiniTaskCreateRequest();
        miniTaskCreateRequest.setHeader(null);
        if (data instanceof Map) {
            //noinspection unchecked
            miniTaskCreateRequest.setContents((Map<String, String>) data);
        } else {
            final HashMap<String, String> dataMap = model2map(projectId, data);
            miniTaskCreateRequest.setContents(dataMap);
        }
        miniTaskCreateRequest.setProjectId(projectId);
        final MiniProjectListResponse.Project project =
                project(projectId).orElseThrow(() -> new ApiException("非法的班牛工作表ID"));
        miniTaskCreateRequest.setAppId(project.getApp_id());
        miniTaskCreateRequest.setUserId(userId);
        final MiniTaskCreateResponse response;
        try {
            response = wjbClient.execute(miniTaskCreateRequest, banniuProperties.getAccessToken());
        } catch (Throwable e) {
            //noinspection ConstantValue
            if (e instanceof IOException) {
                throw new ApiException("网络请求异常:" + e.getMessage());
            } else {
                throw new ApiException("未知异常:" + e.getMessage());
            }
        }
        final int id = response != null ? response.getResult() : 0;
        if (id == 0) {
            log.error(
                    "【班牛小程序工单｜创建工单】创建失败, projectId={} userId={} data={} res={}",
                    projectId,
                    userId,
                    data,
                    JsonUtil.toJson(response));
            throw new ApiException(
                    Optional.ofNullable(response)
                            .map(WjbAbstractResponse::getErrorMsg)
                            .orElse("班牛创建工单失败，未知异常"));
        } else {
            log.info(
                    "【班牛小程序工单｜创建工单】创建成功, projectId={} userId={} data={} id={}",
                    projectId,
                    userId,
                    data,
                    id);
        }
        return id;
    }

    @Override
    @SneakyThrows
    public void taskUpdate(int projectId, int userId, Object data, int taskId) {
        final MiniTaskUpdateRequest miniTaskUpdateRequest = new MiniTaskUpdateRequest();
        miniTaskUpdateRequest.setHeader(null);
        if (data instanceof Map) {
            //noinspection unchecked
            miniTaskUpdateRequest.setContents((Map<String, String>) data);
        } else {
            final HashMap<String, String> dataMap = model2map(projectId, data);
            miniTaskUpdateRequest.setContents(dataMap);
        }
        miniTaskUpdateRequest.setTaskId(taskId);
        miniTaskUpdateRequest.setProjectId(projectId);
        final MiniProjectListResponse.Project project =
                project(projectId).orElseThrow(() -> new ApiException("非法的班牛工作表ID"));
        miniTaskUpdateRequest.setAppId(project.getApp_id());
        miniTaskUpdateRequest.setUserId(userId);
        final MiniTaskUpdateResponse response;
        try {
            response = wjbClient.execute(miniTaskUpdateRequest, banniuProperties.getAccessToken());
            if (StrUtil.isNotEmpty(response.getErrorMsg())) {
                log.error(
                        "【班牛小程序工单｜更新工单】更新失败，data={} id={} response={}",
                        data,
                        taskId,
                        JsonUtil.toJson(response));
                throw new ApiException(response.getErrorMsg());
            } else {
                log.info(
                        "【班牛小程序工单｜更新工单】更新成功，data={} id={} response={}",
                        data,
                        taskId,
                        JsonUtil.toJson(response));
            }
        } catch (Throwable e) {
            //noinspection ConstantValue
            if (e instanceof IOException) {
                throw new ApiException("网络请求异常:" + e.getMessage());
            } else {
                throw new ApiException("未知异常:" + e.getMessage());
            }
        }
    }

    @NonNull
    private HashMap<String, String> model2map(int projectId, Object data) {
        final List<MiniColumnListResponse.ColumnOption> columnOptions = columnOptions(projectId);
        final HashMap<String, String> dataMap = new HashMap<>();
        final ConversionService conversionService =
                ApplicationConversionService.getSharedInstance();
        ReflectionUtils.doWithFields(
                data.getClass(),
                field -> {
                    field.setAccessible(true);
                    final BanniuColumn annotation = field.getAnnotation(BanniuColumn.class);
                    if (annotation == null) {
                        return;
                    }
                    final Optional<MiniColumnListResponse.ColumnOption> columnOptionOptional =
                            columnOptions.stream()
                                    .filter(v -> v.getName().equals(annotation.value()))
                                    .findAny();
                    if (!columnOptionOptional.isPresent()) {
                        if (annotation.required()) {
                            throw new IllegalArgumentException(
                                    "班牛任务模型字段未使用 @BanniuColumn 注解属性映射错误，未找到匹配的班牛组件列:"
                                            + field.getName());
                        } else {
                            return;
                        }
                    }
                    final MiniColumnListResponse.ColumnOption columnOption = columnOptionOptional.get();
                    final Object value = field.get(data);
                    String convertedValue;
                    if (CollUtil.isNotEmpty(columnOption.getSon_column_bos())) {
                        throw new IllegalArgumentException(
                                "暂未支持班牛子表单类型字段推送:" + columnOption.getName());
                    }
                    if (CollUtil.isNotEmpty(columnOption.getOptions())) {
                        final List<String> optionTitleList =
                                columnOption.getOptions().stream()
                                        .map(OptionVo::getTitle)
                                        .collect(Collectors.toList());
                        convertedValue =
                                columnOption.getOptions().stream()
                                        .filter(v -> v.getTitle().equals(value))
                                        .findAny()
                                        .map(OptionVo::getId)
                                        .map(Object::toString)
                                        .orElseThrow(
                                                () ->
                                                        new IllegalArgumentException(
                                                                "班牛任务模型字段值无效，当前字段类型值有效区间为:"
                                                                        + JSON.toJSONString(
                                                                                optionTitleList)));
                    } else if (!(value instanceof String)) {
                        convertedValue =
                                (String)
                                        conversionService.convert(
                                                value,
                                                TypeDescriptor.nested(field, 0),
                                                TypeDescriptor.valueOf(String.class));
                    } else {
                        convertedValue = (String) value;
                    }
                    dataMap.put(String.valueOf(columnOption.getColumn_id()), convertedValue);
                });
        return dataMap;
    }

    @NonNull
    private MiniQueryTaskListResponse.ResultMap emptyResultMap() {
        final MiniQueryTaskListResponse.ResultMap resultMap =
                new MiniQueryTaskListResponse.ResultMap();
        resultMap.setResult(Collections.emptyList());
        resultMap.setTotal(0);
        resultMap.setTotal_page_num(0);
        resultMap.setPage_num(0);
        resultMap.setPage_size(0);
        return resultMap;
    }

    private MiniQueryTaskListRequest toMiniQueryTaskListRequest(MiniTaskQuery query) {
        final MiniQueryTaskListRequest miniQueryTaskListRequest = new MiniQueryTaskListRequest();
        miniQueryTaskListRequest.setProjectId(query.getProjectId());
        Optional.ofNullable(query.getPageNum()).ifPresent(miniQueryTaskListRequest::setPageNum);
        Optional.ofNullable(query.getPageSize()).ifPresent(miniQueryTaskListRequest::setPageSize);
        Optional.ofNullable(query.getTaskStatus())
                .map(TaskStatus::getValue)
                .ifPresent(miniQueryTaskListRequest::setTaskStatus);
        Optional.ofNullable(query.getModifiedStart())
                .map(DateUtil::date)
                .ifPresent(miniQueryTaskListRequest::setStarModified);
        Optional.ofNullable(query.getModifiedEnd())
                .map(DateUtil::date)
                .ifPresent(miniQueryTaskListRequest::setEndModified);
        final List<MiniColumnListResponse.ColumnOption> columnOptions =
                columnOptions(query.getProjectId());
        Optional.ofNullable(query.getConditionColumn())
                .filter(v -> !v.isEmpty())
                .ifPresent(
                        conditions -> {
                            final List<Conditions> conditionColumn =
                                    getConditions(columnOptions, conditions);
                            miniQueryTaskListRequest.setConditionColumn(conditionColumn);
                        });
        Optional.ofNullable(query.getFilterColumnIds())
                .filter(v -> !v.isEmpty())
                .ifPresent(v -> miniQueryTaskListRequest.setFilterColumnIds(v.toString()));
        return miniQueryTaskListRequest;
    }

    @NonNull
    private static List<Conditions> getConditions(
            List<MiniColumnListResponse.ColumnOption> columnOptions, List<Condition> conditions) {
        return conditions.stream()
                .map(condition -> getConditions(columnOptions, condition))
                .collect(Collectors.toList());
    }

    @NonNull
    private static Conditions getConditions(
            List<MiniColumnListResponse.ColumnOption> columnOptions, Condition condition) {
        final Conditions c = new Conditions();
        final Optional<MiniColumnListResponse.ColumnOption> columnOption =
                columnOptions.stream()
                        .filter(option -> option.getName().equals(condition.getColumn()))
                        .findAny();
        c.setId(
                columnOption
                        .map(MiniColumnListResponse.ColumnOption::getColumn_id)
                        .orElseThrow(
                                () ->
                                        new IllegalArgumentException(
                                                "班牛工作表查询条件指定的列名不存在:" + condition.getColumn())));
        c.setValue(condition.getValue());
        c.setBehaviorType((short) 1);
        c.setSearchType(String.valueOf(condition.getSearchType().getValue()));
        return c;
    }
}
