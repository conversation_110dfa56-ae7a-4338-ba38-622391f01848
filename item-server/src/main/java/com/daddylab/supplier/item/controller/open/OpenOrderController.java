package com.daddylab.supplier.item.controller.open;

import com.alibaba.cola.dto.PageResponse;
import com.daddylab.supplier.item.application.order.OpenOrderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrder;
import com.daddylab.supplier.item.types.order.OpenOrderQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/12/1
 */
@Slf4j
@RestController
@RequestMapping("/open/api/order")
@Api(value = "订单数据开放接口", tags = "订单数据开放接口")
public class OpenOrderController {

    @Resource
    OpenOrderService openOrderService;

    @ApiOperation("订单查询")
    @PostMapping("/orderQuery")
    public PageResponse<WdtOrder> query(
            @Validated
            @RequestBody OpenOrderQuery query) {
        return openOrderService.orderQuery(query);
    }

}
