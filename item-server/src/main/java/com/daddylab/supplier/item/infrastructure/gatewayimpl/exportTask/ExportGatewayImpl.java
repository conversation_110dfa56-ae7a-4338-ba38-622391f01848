package com.daddylab.supplier.item.infrastructure.gatewayimpl.exportTask;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.PageResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.application.afterSales.AbnormalExportSheet;
import com.daddylab.supplier.item.application.afterSales.AfterSalesPageQuery;
import com.daddylab.supplier.item.application.bizLevelDivision.BizLevelDivisionConvert;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.CombinationItemPageQuery;
import com.daddylab.supplier.item.application.saleItem.dto.NewGoodsSheet;
import com.daddylab.supplier.item.application.saleItem.dto.SaleItemLibrarySheet;
import com.daddylab.supplier.item.application.saleItem.query.NewGoodsQueryPage;
import com.daddylab.supplier.item.application.saleItem.query.SaleItemLibraryQueryPage;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsVo;
import com.daddylab.supplier.item.application.saleItem.vo.SaleItemLibraryVO;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.ItemTransMapper;
import com.daddylab.supplier.item.common.trans.NewGoodsTransMapper;
import com.daddylab.supplier.item.common.trans.SaleItemLibraryTransMapper;
import com.daddylab.supplier.item.controller.item.dto.ExportCmd;
import com.daddylab.supplier.item.controller.item.dto.ItemPageQuery;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseQueryPage;
import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderPageQuery;
import com.daddylab.supplier.item.domain.departments.DepartmentGateway;
import com.daddylab.supplier.item.domain.exportTask.ExportGateway;
import com.daddylab.supplier.item.domain.exportTask.dto.combinationItem.ComposerSkuAllSheet;
import com.daddylab.supplier.item.domain.exportTask.dto.item.ExportItemDto;
import com.daddylab.supplier.item.domain.exportTask.dto.item.ExportItemNoPriceDto;
import com.daddylab.supplier.item.domain.exportTask.dto.item.ExportSkuDto;
import com.daddylab.supplier.item.domain.exportTask.dto.item.ExportSkuNoPriceDto;
import com.daddylab.supplier.item.domain.exportTask.dto.purchase.PurchaseOrderDetailSheet;
import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetailQuery;
import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetailSheet;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderQuery;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderSheet;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BizLevelDivision;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Purchase;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBizLevelDivisionService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICombinationItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/2 10:07 上午
 * @description
 */
@Service
public class ExportGatewayImpl implements ExportGateway {

    @Autowired
    ItemMapper itemMapper;

    @Autowired
    IItemSkuService iItemSkuService;

    @Autowired
    PurchaseMapper purchaseMapper;

    @Autowired
    CombinationItemMapper combinationItemMapper;

    @Autowired
    PurchaseOrderDetailMapper purchaseOrderDetailMapper;

    @Autowired
    StockInOrderMapper stockInOrderMapper;

    @Autowired
    StockOutOrderMapper stockOutOrderMapper;

    @Autowired
    private SaleItemLibraryMapper saleItemLibraryMapper;

    @Autowired
    NewGoodsMapper newGoodsMapper;

    @Autowired
    NewGoodsBizService newGoodsBizService;

    @Autowired
    ICombinationItemService iCombinationItemService;

    @Autowired
    @Qualifier("UserGatewayCacheImpl")
    UserGateway userGateway;

    @Autowired
    DepartmentGateway departmentGateway;

    @Autowired
    AfterSalesAbnormalInfoMapper afterSalesAbnormalInfoMapper;
    @Autowired
    IBizLevelDivisionService iBizLevelDivisionService;

    @Override
    public List<ExportSkuDto> querySku(ExportCmd cmd) {
        return itemMapper.queryExportSku(cmd);
    }

    @Override
    public List<ExportSkuNoPriceDto> querySkuNoPrice(ExportCmd cmd) {
        return itemMapper.queryExportSkuNoPrice(cmd);
    }

    @Override
    public List<ExportItemDto> queryItem(ExportCmd cmd) {
        return itemMapper.queryExportItem(cmd);

    }

    @Override
    public List<ExportItemNoPriceDto> queryItemNoPrice(ExportCmd cmd) {
        return itemMapper.queryExportItemNoPrice(cmd);
    }


    @SuppressWarnings(value = "all")
    @Override
    public Integer exportItemAndSkuCount(ExportCmd cmd) {
        if ("item".equals(cmd.getLatitude())) {
            final ItemPageQuery pageQuery = ItemTransMapper.INSTANCE.exportItemCmdToQuery(cmd);
            return itemMapper.queryPageCount(pageQuery);
        }
        if ("sku".equals(cmd.getLatitude())) {
            return itemMapper.countExportSku(cmd);
        }
        throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "商品信息导出入参异常，导出类型非法");
    }

    private Long getItemIdBySkuId(String skuCode) {
        QueryWrapper<ItemSku> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ItemSku::getSkuCode, skuCode);
        final List<ItemSku> list = iItemSkuService.list(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return 0L;
        } else {
            return list.get(0).getItemId();
        }
    }

    @Override
    public List<Purchase> queryPurchase(PurchaseQueryPage page) {
        return purchaseMapper.queryExport(page);
    }

    @Override
    public Integer countCombinationItem(CombinationItemPageQuery query) {
        return iCombinationItemService.countListItem(query);
    }

    @Override
    public List<ComposerSkuAllSheet> queryCombinationItem(CombinationItemPageQuery query) {
        final IBizLevelDivisionService bean = SpringUtil.getBean(IBizLevelDivisionService.class);
        final List<ComposerSkuAllSheet> composerSkuAllSheets = combinationItemMapper.queryExport(query);
        final List<Long> combinationIds = composerSkuAllSheets.stream()
                .map(ComposerSkuAllSheet::getCombinationId)
                .collect(Collectors.toList());
        final List<BizLevelDivision> bizLevelDivisions = bean.selectByTypeAndBizIds(BizUnionTypeEnum.COMBINATION,
                combinationIds);
        for (ComposerSkuAllSheet sheet : composerSkuAllSheets) {
            final List<BizLevelDivision> theBizLevelDivisions = bizLevelDivisions.stream()
                    .filter(biz -> Objects.equals(sheet.getCombinationId(),
                            biz.getBizId()))
                    .collect(Collectors.toList());
            sheet.setCorpType(BizLevelDivisionConvert.INSTANCE.toBizLevelDescStr(theBizLevelDivisions,
                    DivisionLevelEnum.COOPERATION));
            sheet.setBizType(BizLevelDivisionConvert.INSTANCE.toBizLevelDescStr(theBizLevelDivisions,
                    DivisionLevelEnum.BUSINESS_TYPE));
        }
        return composerSkuAllSheets;
    }

    @Override
    public Integer countPurchaseDetailCount(PurchaseOrderPageQuery query) {
        return purchaseOrderDetailMapper.detailExportCount(query);
    }

    @Override
    public List<PurchaseOrderDetailSheet> queryPurchaseDetail(PurchaseOrderPageQuery query) {
        return purchaseOrderDetailMapper.detailExport(query, query.getOffset(), query.getPageSize());
    }

    @Override
    public List<SaleItemLibrarySheet> queryExportSaleItemLibrary(SaleItemLibraryQueryPage query) {
        List<SaleItemLibraryVO> saleItemLibraries = saleItemLibraryMapper.queryPage(query);
        return SaleItemLibraryTransMapper.INSTANCE.listVoToSheet(saleItemLibraries);
    }

    @Override
    public Integer countNewGoods(NewGoodsQueryPage query) {
        return newGoodsBizService.count(query);
    }

    @Override
    public Integer countExportSaleItemLibrary(SaleItemLibraryQueryPage query) {
        return saleItemLibraryMapper.count(query);
    }

    @Override
    public List<StockInOrderAndOrderDetailSheet> queryStockInOrder(StockInOrderAndOrderDetailQuery query) {
        query.setOffsetVal(query.getOffsetVal() == null ? 0 : query.getOffsetVal());
        List<StockInOrderAndOrderDetailSheet> sheetList = stockInOrderMapper.queryExport(query);

        final Set<String> skuCodeList = sheetList.stream().map(StockInOrderAndOrderDetailSheet::getItemSkuCode)
                .collect(Collectors.toSet());
        final Map<String, String> bizTypeBySkuCodeMap = iBizLevelDivisionService.queryBizTypeBySkuCode(skuCodeList);

        final List<Long> buyerUserIdList = sheetList.stream().map(StockInOrderAndOrderDetailSheet::getBuyerUser).distinct()
                .collect(Collectors.toList());
        final Map<Long, StaffInfo> staffInfoMap = userGateway.batchQueryStaffInfoByIds(buyerUserIdList);

        for (StockInOrderAndOrderDetailSheet sheet : sheetList) {

//            if (Objects.nonNull(sheet.getBuyerUser())) {
//                StaffInfo staffInfo = userGateway.queryStaffInfoById(Long.valueOf(sheet.getBuyerUser()));
//                if (Objects.nonNull(staffInfo)) {
//                    sheet.setBuyerUser(staffInfo.getNickname());
//                }
//            }
//            if (StringUtils.isEmpty(sheet.getBuyerUser())) {
//                sheet.setBuyerUser("系统");
//            }

            final StaffInfo staffInfo = staffInfoMap.get(sheet.getBuyerUser());
            String buyerName = Objects.isNull(staffInfo) ? "系统" : staffInfo.getNickname();
            sheet.setBuyerUserStr(buyerName);

            sheet.setPurchaseDepartment(departmentGateway.name(Long.valueOf(sheet.getPurchaseDepartment())));

            sheet.setBizTypeStr(bizTypeBySkuCodeMap.getOrDefault(sheet.getItemSkuCode(), ""));
            IEnum.getEnumOptByValue(BusinessLine.class, sheet.getBusinessLine()).ifPresent(v ->
                    sheet.setBusinessLineStr(v.getDesc()));
        }
        return sheetList;
    }

    @Override
    public Integer stockInOrderCount(StockInOrderAndOrderDetailQuery stockInOrderAndOrderDetailQuery) {
        return stockInOrderMapper.selectExportCount(stockInOrderAndOrderDetailQuery);
    }

    @Override
    public List<StockOutOrderSheet> queryStockOutOrder(StockOutOrderQuery query) {
        List<StockOutOrderSheet> stockOutOrderSheets = stockOutOrderMapper.queryExport(query);
        for (StockOutOrderSheet stockOutOrderSheet : stockOutOrderSheets) {
            String taxPrice = Optional.ofNullable(stockOutOrderSheet.getTaxPrice())
                    .map(BigDecimal::new).map(v -> v.setScale(6, RoundingMode.HALF_UP))
                    .map(BigDecimal::toString).orElse("");
            String taxQuota = Optional.ofNullable(stockOutOrderSheet.getTaxQuota())
                    .map(BigDecimal::new).map(v -> v.setScale(2, RoundingMode.HALF_UP))
                    .map(BigDecimal::toString).orElse("");
            String afterTaxPrice = Optional.ofNullable(stockOutOrderSheet.getAfterTaxPrice())
                    .map(BigDecimal::new).map(v -> v.setScale(6, RoundingMode.HALF_UP))
                    .map(BigDecimal::toString).orElse("");
            String totalPriceTax = Optional.ofNullable(stockOutOrderSheet.getTotalPriceTax())
                    .map(BigDecimal::new).map(v -> v.setScale(2, RoundingMode.HALF_UP))
                    .map(BigDecimal::toString).orElse("");
            String afterTaxAmount = Optional.ofNullable(stockOutOrderSheet.getAfterTaxAmount())
                    .map(BigDecimal::new).map(v -> v.setScale(2, RoundingMode.HALF_UP))
                    .map(BigDecimal::toString).orElse("");
            stockOutOrderSheet.setTaxPrice(taxPrice);
            stockOutOrderSheet.setAfterTaxPrice(afterTaxPrice);
            stockOutOrderSheet.setTaxQuota(taxQuota);
            stockOutOrderSheet.setTotalPriceTax(totalPriceTax);
            stockOutOrderSheet.setAfterTaxAmount(afterTaxAmount);
            stockOutOrderSheet.setReturnType(ReturnTypeEnum.getDesc(Integer.valueOf(stockOutOrderSheet.getReturnType())));
            stockOutOrderSheet.setReturnMode(ReturnModeEnum.getDesc(Integer.valueOf(stockOutOrderSheet.getReturnMode())));
        }
        return stockOutOrderSheets;
    }

    @Override
    public Integer stockOutOrderCount(StockOutOrderQuery query) {
        return stockOutOrderMapper.countExport(query);
    }

    @Override
    public List<AbnormalExportSheet> queryAfterSalesAbnormalList(AfterSalesPageQuery pageQuery) {
        Page<AbnormalExportSheet> objects = PageHelper.startPage(pageQuery.getPageIndex(), pageQuery.getPageSize())
                .doSelectPage(() -> afterSalesAbnormalInfoMapper.exportList(pageQuery));
        return objects.getResult();
    }

    @Override
    public Integer countAfterSalesAbnormalList(AfterSalesPageQuery pageQuery) {
        return afterSalesAbnormalInfoMapper.exportCount(pageQuery);

    }
}
