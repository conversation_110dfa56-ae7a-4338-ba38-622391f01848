package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 2:31 下午
 * @description
 */
@Getter
@AllArgsConstructor
public enum MessageState implements IEnum<Integer> {

    NO_READ(0, "未读"),
    HAD_READ(1, "已读"),

    ;
    @EnumValue
    private final Integer value;
    private final String desc;


}
