package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAuditTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerModuleAuditTaskMapper;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemLaunchProcessNodeId;

import java.util.List;

/**
 * 商品库抽屉模块审核 服务类
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
public interface IItemDrawerModuleAuditTaskService
    extends IDaddylabServicePlus<ItemDrawerModuleAuditTask, ItemDrawerModuleAuditTaskMapper> {

  List<ItemDrawerModuleAuditTask> getItemDrawerModulePendingAuditTasks(
      Long itemId, ItemLaunchProcessNodeId nodeId);

  List<ItemDrawerModuleAuditTask> getItemDrawerModulePendingAuditTasks(
      Long itemId, ItemAuditType type);
}
