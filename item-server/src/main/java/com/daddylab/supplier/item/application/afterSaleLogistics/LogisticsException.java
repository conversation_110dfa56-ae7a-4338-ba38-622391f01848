package com.daddylab.supplier.item.application.afterSaleLogistics;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @version 3.3.2
 */
@Getter
@AllArgsConstructor
public enum LogisticsException implements IIntegerEnum {

  /** 发货异常 */
  CONSIGN_24H(11, "发货超时24h", LogisticsRootException.CONSIGN_EXCEPTION),
  CONSIGN_48H(12, "发货超时48h", LogisticsRootException.CONSIGN_EXCEPTION),

  /** 揽收异常 */
  RECEIVE_24H(21, "揽收已超时24h", LogisticsRootException.RECEIVE_EXCEPTION),
  RECEIVE_72H(22, "揽收已超时72h", LogisticsRootException.RECEIVE_EXCEPTION),

  /** 发运异常 */
  SEND_24H(31, "发运超时24h", LogisticsRootException.SEND_EXCEPTION),
  SEND_48H(32, "发运超时48h", LogisticsRootException.SEND_EXCEPTION),
  SEND_72H(33, "发运超时72h", LogisticsRootException.SEND_EXCEPTION),
  SEND_96H(34, "发运超时96h", LogisticsRootException.SEND_EXCEPTION),
  SEND_7D(35, "疑似丢件", LogisticsRootException.SEND_EXCEPTION),

  /** 派送异常 */
  DISTRIBUTE_48H(41, "派送超48小时", LogisticsRootException.DISTRIBUTE_EXCEPTION),
  DISTRIBUTE_24H(42, "派送超24小时", LogisticsRootException.DISTRIBUTE_EXCEPTION),

  /** 异常件 */
  KNOTTY(51, "疑难件", LogisticsRootException.DIFFICULT_EXCEPTION),

/** 物流中转异常 */
//    TRANSFER_ABOUT_OVERTIME(40, "中转即将超时", LogisticsRootException.TRANSFER_EXCEPTION),
//    TRANSFER_ALREADY_OVERTIME(41, "中转超时", LogisticsRootException.TRANSFER_EXCEPTION),
//    TRANSFER_SUSPECT_LOST(42, "疑似丢件", LogisticsRootException.TRANSFER_EXCEPTION),
//    TRANSFER_DIFFICULT(43, "疑难件", LogisticsRootException.TRANSFER_EXCEPTION)
;

  // ------

  private final Integer value;
  private final String desc;
  private final LogisticsRootException root;

  public static LogisticsException valueOf(Integer value) {
    for (LogisticsException e : values()) {
      if (e.getValue().equals(value)) {
        return e;
      }
    }
    return null;
  }
}
