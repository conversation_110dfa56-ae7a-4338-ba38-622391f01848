package com.daddylab.supplier.item.application.payment.dto;

import lombok.Data;
import org.javers.core.metamodel.annotation.PropertyName;

import java.math.BigDecimal;

/**
 * 付款单比较对象。
 *
 * <AUTHOR> up
 * @date 2023年11月21日 2:18 PM
 */
@Data
public class PaymentCompareBo {

    /**
     * 合作模式
     */
    @PropertyName("合作模式")
    private String businessLine;

    /**
     * 采购类型。0：标准采购，1：工厂代发
     */
    @PropertyName("采购类型")
    private String purchaseType;

    /**
     * 币别。0：人命币。1：美元
     */
    @PropertyName("币别")
    private String currencyType;

    /**
     * 采购组织。默认：杭州老爸电商科技有限公司
     */
    @PropertyName("采购组织")
    private String purchaseOrg;

    /**
     * 付款组织。默认：杭州老爸电商科技有限公司
     */
    @PropertyName("付款组织")
    private String payOrg;

    /**
     * 收款单位银行账号
     */
    @PropertyName("收款单位银行账号")
    private String payeeBankCardNo;

    /**
     * 收款单位开户行行号
     */
    @PropertyName("开户行行号")
    private String payeeBankNo;

    /**
     * 收款单位开户行
     */
    @PropertyName("开户行")
    private String payeeBank;


    /**
     * 期望付款日期。10位时间戳
     */
    @PropertyName("期望付款日期")
    private String expectedPayTime;

    /**
     * 备注
     */
    @PropertyName("备注")
    private String remark;

    /**
     * 付款明细来源。0：采购单，1：结算单
     */
    @PropertyName("付款明细来源")
    private String detailSource;

    /**
     * 付款用途。0：采购付款（默认），1：预付款
     */
    @PropertyName("付款用途")
    private String payPurpose;

    /**
     * 付款比例。必须整数，直接为百分比值
     */
    @PropertyName("付款比例")
    private Integer payProportions;

    /**
     * 其他金额扣款
     */
    @PropertyName("其他金额扣款")
    private BigDecimal otherChargeback;

    /**
     * 总申请付款金额
     */
    @PropertyName("总申请付款金额")
    private BigDecimal totalApplyAmount;

    /**
     * 实际付款金额
     */
    @PropertyName("实际付款金额")
    private BigDecimal realPayAmount;


    // -------

    /**
     * 往来(交易)单位ID
     */
    @PropertyName("往来单位")
    private String tradeUnit;

    /**
     * 收款单位ID，同上
     */
    @PropertyName("收款单位")
    private String payeeUnit;

    /**
     * 采购员。系统登录人用户ID
     */
    @PropertyName("采购员")
    private String buyer;

    /**
     * 上传附件ID，英文逗号隔开。上传发票
     */
    @PropertyName("上传附件")
    private String additional;


}
