package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataFetch;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DataFetchMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDataFetchService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据同步记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Service
public class DataFetchServiceImpl extends DaddyServiceImpl<DataFetchMapper, DataFetch> implements IDataFetchService {

}
