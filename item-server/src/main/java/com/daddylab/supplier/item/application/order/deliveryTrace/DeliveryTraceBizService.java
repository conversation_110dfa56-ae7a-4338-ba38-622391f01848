package com.daddylab.supplier.item.application.order.deliveryTrace;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.types.order.DeliveryTracePageQuery;
import com.daddylab.supplier.item.types.order.OrderDeliveryTracePageVO;

/**
 * <AUTHOR>
 * @since 2023/8/22
 */
public interface DeliveryTraceBizService {
    PageResponse<OrderDeliveryTracePageVO> pageQuery(DeliveryTracePageQuery query);

    SingleResponse<Boolean> export(DeliveryTracePageQuery query);
}
