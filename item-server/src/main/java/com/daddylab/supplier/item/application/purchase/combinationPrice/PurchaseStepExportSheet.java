package com.daddylab.supplier.item.application.purchase.combinationPrice;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2023年10月12日 1:51 PM
 */
@Data
public class PurchaseStepExportSheet {

    @ExcelProperty("价格类型")
    private String priceType;

    @ExcelProperty("*商品sku")
    private String itemSku;

    
    @ExcelProperty("*商品名称")
    private String itemName;

    
    @ExcelProperty("商品规格")
    private String itemSpecs;

    
    @ExcelProperty("商品规格数量")
    private Long specsCount;

    
    @ExcelProperty("工厂/仓库")
    private String purchaseArea;

    
    @ExcelProperty("供应商")
    private String provider;

    @ExcelProperty("采购负责人")
    private String buyerName;

    @ExcelProperty(value = "日常供价无优惠")
    private String usualPrice;

    
    @ExcelProperty("是否纯活动商品")
    private String isActive;

    
    @ExcelProperty("优惠类型")
    private String favourableType;

    
    @ExcelProperty("平台名称")
    private String platformType;

    
    @ExcelProperty("方式")
    private String activeType;

    
    @ExcelProperty("活动开始时间")
    private String startTime;

    
    @ExcelProperty("活动结束时间")
    private String endTime;

    
    @ExcelProperty("订单拍下份数")
    private Long orderCount;

    
    @ExcelProperty("实发单品数量")
    private Long finalCount;

    
    @ExcelProperty(value = "按价格优惠结算成本")
    private String priceCost;

    
    @ExcelProperty(value = "按数量优惠结算成本")
    private String numCost;

    
    @ExcelProperty("供价优惠内容")
    private String content;

    @ExcelProperty("备注说明")
    private String remark;


}
