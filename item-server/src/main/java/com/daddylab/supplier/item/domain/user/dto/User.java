package com.daddylab.supplier.item.domain.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class User {

    /**
     * 用户ID
     */
    @JsonProperty("id")
    private Long id;

    /**
     * 用户昵称
     */
    @JsonProperty("nick_name")
    private String nickname;

    /**
     * 微信昵称
     */
    @JsonProperty("wechat_nick_name")
    private String wechatNickName;

    /**
     * 用户头像
     */
    @JsonProperty("avatar")
    private String avatar;

    /**
     * 手机号码
     */
    @JsonProperty("mobile")
    private String mobile;

    /**
     * 用户性别
     */
    @JsonProperty("sex")
    private Integer sex;

    /**
     * 生日
     */
    @JsonProperty("birthday")
    private Long birthday;
}
