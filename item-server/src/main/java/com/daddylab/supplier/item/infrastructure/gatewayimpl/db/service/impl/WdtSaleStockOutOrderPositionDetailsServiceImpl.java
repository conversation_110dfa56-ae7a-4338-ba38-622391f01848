package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrderPositionDetails;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtSaleStockOutOrderPositionDetailsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtSaleStockOutOrderPositionDetailsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 旺店通销售出库单出库货位明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Service
public class WdtSaleStockOutOrderPositionDetailsServiceImpl extends DaddyServiceImpl<WdtSaleStockOutOrderPositionDetailsMapper, WdtSaleStockOutOrderPositionDetails> implements IWdtSaleStockOutOrderPositionDetailsService {

}
