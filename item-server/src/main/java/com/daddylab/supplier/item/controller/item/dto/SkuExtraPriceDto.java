package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2025年02月17日 4:32 PM
 */
@Data
public class SkuExtraPriceDto {

    @NotBlank(message = "价格值必填")
    @ApiModelProperty("价格值，单位元，2位小数")
    private BigDecimal val;

    @ApiModelProperty("开始时间")
    private Long startTime;

    @ApiModelProperty("结束时间")
    private Long endTime;

    @ApiModelProperty("/**\n" +
            "     * 0 (SKU和组合装)成本价。默认值  SKU和组合装的区分 根据 skuCode 是否MU开头。\n" +
            "     * 1 (SKU和组合装)销售价。\n" +
            "     *\n" +
            "     * 2 SKU合同销售价\n" +
            "     * 3 SKU平台佣金\n" +
            "     *\n" +
            "     * 4 组合装合同销售价\n" +
            "     * 5 平台佣金\n" +
            "     */")
    private Integer type;
}
