package com.daddylab.supplier.item.application.criticalPush;

import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataPush;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataPushLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDataPushLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDataPushService;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import java.util.concurrent.atomic.AtomicBoolean;
import lombok.NonNull;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/4/15
 */
@Component("CriticalPushManagerImpl")
public class CriticalPushManagerImpl implements CriticalPushManager {

    private static final long serialVersionUID = 1L;
    private final IDataPushService dataPushService;
    private final IDataPushLogService dataPushLogService;

    public CriticalPushManagerImpl(
            IDataPushService dataPushService,
            IDataPushLogService dataPushLogService) {
        this.dataPushService = dataPushService;
        this.dataPushLogService = dataPushLogService;
    }


    private void persistRecords(DataPush record, DataPushLog dataPushLog) {
        dataPushLogService.save(dataPushLog);
        dataPushService.saveOrUpdate(record);
    }

    @Override
    public <P, R> void push(SourceType sourceType, long sourceId, TargetType targetType,
            VarargsFunctionCallback<P, R> callback, Long pushId)
            throws CriticalPushException {
        DataPush record = getDataPush(
                sourceType, sourceId, targetType, pushId);
        if (!PushState.INIT.getValue().equals(record.getState())) {
            return;
        }
        record.setAsync(pushId == null ? 0 : 1);
        try {
            AtomicBoolean called = new AtomicBoolean(false);
            callback.call((executor, args) -> {
                called.set(true);
                final DataPushLog dataPushLog = newDataPushLog(record, args);

                try {
                    final R resp = executor.call(args);
                    dataPushLog.setResp(serializeResp(resp));
                    record.setState(PushState.SUCCESS.getValue());
                } catch (Exception e) {
                    onError(record, dataPushLog, e);
                } finally {
                    persistRecords(record, dataPushLog);
                }
            });
            if (!called.get()) {
                throw new CriticalPushException(record.getId(), "代理方法未被正确调用");
            }
        } catch (Exception e) {
            if (e instanceof CriticalPushException) {
                throw ((CriticalPushException) e);
            }
            throw new CriticalPushException(record.getId(), e.getMessage(), e);
        }
    }

    private <R> String serializeResp(R resp) {
        if (resp == null) {
            return null;
        }
        return resp.getClass().isPrimitive() || resp instanceof CharSequence ? resp.toString()
                : JsonUtil.toJson(resp);
    }

    private void onError(DataPush record, DataPushLog dataPushLog, Throwable e) {
        dataPushLog.setErr(ExceptionUtil.stacktraceToString(e, 20000));
        record.setFailCount(record.getFailCount() + 1);
        checkFailCount(record, dataPushLog, e);
        throw new CriticalPushException(record.getId(), e.getMessage(), e);
    }

    private DataPush getDataPush(SourceType sourceType, long sourceId, TargetType targetType,
            Long pushId) {
        DataPush record;
        if (pushId != null) {
            record = dataPushService.getById(pushId);
            if (record == null) {
                throw new IllegalArgumentException("推送记录不存在");
            }
            return record;
        } else {
            record = new DataPush();
            record.setSourceType(sourceType.getValue());
            record.setSourceId(sourceId);
            record.setTargetType(targetType.getValue());
            record.setFailCount(0);
            record.setState(PushState.INIT.getValue());
            dataPushService.save(record);
        }
        return record;
    }

    @NonNull
    private DataPushLog newDataPushLog(DataPush record, Object[] args) {
        final DataPushLog dataPushLog = new DataPushLog();
        dataPushLog.setPushId(record.getId());
        dataPushLog.setReq(serializeResp(args));
        return dataPushLog;
    }

    private void checkFailCount(DataPush record, DataPushLog log, Throwable ex) {
        int failThreshold = record.getAsync() > 0 ? 3 : 1;
        if (record.getFailCount() >= failThreshold) {
            record.setState(PushState.ERROR.getValue());
            Alert.text(MessageRobotCode.GLOBAL,
                    StrUtil.format("重要消息推送失败 消息类型:{} 推送目标:{} 业务ID:{} 当前失败次数:{} 日志ID:{} 错误信息:{}",
                            IEnum.getEnumByValue(SourceType.class, record.getSourceType())
                                    .getDesc(),
                            IEnum.getEnumByValue(TargetType.class, record.getTargetType())
                                    .getDesc(),
                            record.getSourceId(), record.getFailCount(),
                            record.getId(), ExceptionUtil.stacktraceToOneLineString(ex)),
                    true);
        }
    }
}
