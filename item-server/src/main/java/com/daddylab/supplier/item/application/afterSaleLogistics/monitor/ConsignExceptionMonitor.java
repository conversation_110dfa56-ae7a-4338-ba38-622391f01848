package com.daddylab.supplier.item.application.afterSaleLogistics.monitor;

import com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsException;
import com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsRootException;
import com.daddylab.supplier.item.application.afterSaleLogistics.dto.LogisticExceptionRes;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsTrace;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> up
 * @date 2024年05月20日 6:14 PM
 * 现货/预售，至现在已超16h，且未发货（上传物流单号）订单。
 * 现货/预售，至现在已超24h，且未发货（上传物流单号）订单。
 * 现货/预售，至现在已超48h，且未发货（上传物流单号）订单。
 */
@Order(MonitorOrder.ConsingExceptionMonitor)
@Slf4j
@Component
public class ConsignExceptionMonitor implements OrderLogisticsExceptionMonitor {

    @Override
    public LogisticExceptionRes process(ProcessContext context) {
        final OrderLogisticsTrace trace = context.getTrace();
        final Long currentTurnTime = context.getTimestamp();
        LogisticExceptionRes res = new LogisticExceptionRes();
        boolean noConsign = StringUtils.isBlank(trace.getLogisticsNo());
        if (Objects.nonNull(trace.getPayTime()) && trace.getPayTime() != 0L) {
            res.setRootException(LogisticsRootException.CONSIGN_EXCEPTION);
            long aLong = DateUtil.calculateDifference(currentTurnTime, trace.getPayTime(), TimeUnit.HOURS);
            if (aLong >= 48 && noConsign) {
                res.setSubException(LogisticsException.CONSIGN_48H);
                return res;
            }
            if (aLong >= 24 && noConsign) {
                res.setSubException(LogisticsException.CONSIGN_24H);
                return res;
            }
        }
        res.setRootException(LogisticsRootException.NORMAL);
        return res;
    }
}
