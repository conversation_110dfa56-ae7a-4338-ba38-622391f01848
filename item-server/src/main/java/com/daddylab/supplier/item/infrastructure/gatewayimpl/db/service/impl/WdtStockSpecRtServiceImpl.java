package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtStockSpecRt;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtStockSpecRtMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtStockSpecRtService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-01
 */
@Service
public class WdtStockSpecRtServiceImpl extends DaddyServiceImpl<WdtStockSpecRtMapper, WdtStockSpecRt> implements IWdtStockSpecRtService {
    
}
