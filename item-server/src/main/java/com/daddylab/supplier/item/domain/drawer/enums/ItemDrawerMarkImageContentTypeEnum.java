package com.daddylab.supplier.item.domain.drawer.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.Getter;

/**
 * Class  ItemDrawerImageTypeEnum
 *
 * @Date 2022/6/1上午11:32
 * <AUTHOR>
 */
@Getter
public enum ItemDrawerMarkImageContentTypeEnum implements IEnum<Integer> {

    /**
     * 操作类型 1-改为 2-删除 3-确认
     */
    UPDATE(1,"修改"),
    DELETE(2, "删除"),
    CONFIRM(3, "确认"),
    ;

    ItemDrawerMarkImageContentTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
    private final Integer value;
    private final String desc;
}
