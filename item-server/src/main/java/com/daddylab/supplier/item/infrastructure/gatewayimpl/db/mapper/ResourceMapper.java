package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Resource;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 资源 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@DS("authDb")
@Repository
public interface ResourceMapper extends DaddyBaseMapper<Resource> {

}
