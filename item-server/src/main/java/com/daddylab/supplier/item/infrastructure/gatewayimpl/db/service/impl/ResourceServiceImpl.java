package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Resource;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ResourceMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IResourceService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 资源 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-29
 */
@Service
@DS("authDb")
public class ResourceServiceImpl extends DaddyServiceImpl<ResourceMapper, Resource> implements IResourceService {

}
