package com.daddylab.supplier.item.infrastructure.rocketmq.enums;

/**
 * <AUTHOR>
 * @since 2022/2/18
 */
public interface MQConsumerGroup {
    /**
     * 订阅权限系统策略变更，刷新用户权限
     */
    String REFRESH_PERMISSION = "${spring.profiles.active}_${spring.application.name}_refreshPermission";

    /**
     * 企微推送信息，延时消费处理
     */
    String WECHAT_DELAY_MSG = "${spring.profiles.active}_${spring.application.name}_wechat_delay_msg";

    /**
     * 重要消息异步推送消费
     */
    String CRITICAL_PUSH = "${spring.profiles.active}_${spring.application.name}_critical_push";


    /**
     * 企微消息推送
     */
    String QW_MESSAGE_PUSH = "${spring.profiles.active}_${spring.application.name}_qw_message_push";

    /**
     * 快刀云回调消费
     */
    String KUAIDAOYUN_CONSUMER = "${spring.profiles.active}_${spring.application.name}_kuaidaoyun_consumer";

    String SHOP_STOCK_CONSUMER = "${spring.profiles.active}_${spring.application.name}_shop_stock_consumer";

}
