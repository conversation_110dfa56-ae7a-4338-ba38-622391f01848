package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ProviderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IProviderService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 * 供应商 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class IProviderServiceImpl extends DaddyServiceImpl<ProviderMapper, Provider> implements IProviderService {


    @Override
    public Boolean isSyncKingDee(Long providerId) {
        Provider byId = this.getById(providerId);
        return Objects.nonNull(byId) && StringUtils.isNotBlank(byId.getKingDeeId());
    }

    @Override
    public void setKingDee(String kingDeeNo, String kingDeeId,Long id) {
        LambdaUpdateWrapper<Provider> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Provider::getProviderNo,kingDeeNo).set(Provider::getKingDeeId,kingDeeId)
                .eq(Provider::getId,id);
        this.update(updateWrapper);
    }
}
