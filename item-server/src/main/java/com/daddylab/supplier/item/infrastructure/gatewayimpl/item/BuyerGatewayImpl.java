package com.daddylab.supplier.item.infrastructure.gatewayimpl.item;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.domain.item.gateway.BuyerGateway;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Buyer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemProcurement;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBuyerService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemProcurementService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 12:22 下午
 * @description
 */
@Service
public class BuyerGatewayImpl implements BuyerGateway {

    @Autowired
    IBuyerService iBuyerService;

    @Resource
    IItemProcurementService iItemProcurementService;

    @Resource
    UserGateway userGateway;

    @Override
    public Optional<Buyer> buyerExist(Long userId) {
        QueryWrapper<Buyer> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Buyer::getUserId, userId);
        return Optional.ofNullable(iBuyerService.getOne(wrapper));
    }

    @Override
    public String getKingDeeId(Long userId) {
        QueryWrapper<Buyer> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(Buyer::getUserId, userId);
        Buyer buyer = iBuyerService.getOne(wrapper);
        if (Objects.nonNull(buyer)) {
            return buyer.getKingDeeId();
        } else {
            return "";
        }
    }

    @Override
    public String getKingDeeAccountByItemId(Long itemId) {
        return iBuyerService.getKingDeeIdByItemId(itemId);
    }

    @Override
    public List<String> getNameList() {
        return iBuyerService.list().stream().distinct().map(Buyer::getName).collect(Collectors.toList());
    }

    @Override
    public Optional<Buyer> buyer(Long id) {
        return Optional.ofNullable(iBuyerService.getById(id));
    }

    @Override
    public Long saveOrUpdateBuyer(Long userId, String userName) {
        Optional<Buyer> buyerOptional = iBuyerService.lambdaQuery()
                .eq(Buyer::getUserId, userId)
                .select().oneOpt();
        Buyer buyer = buyerOptional.orElseGet(() -> {
            Buyer newOne = new Buyer();
            newOne.setUserId(userId);
            newOne.setName(userName);
            newOne.setKingDeeId("0");
            iBuyerService.save(newOne);
            return newOne;
        });

        if (!buyer.getName().equals(userName)) {
            buyer.setName(userName);
            iBuyerService.updateById(buyer);
        }

        return buyer.getId();
    }

    @Override
    public Buyer getByItemId(Long itemId) {
        List<ItemProcurement> list = iItemProcurementService.lambdaQuery().eq(ItemProcurement::getItemId, itemId)
                .list();
        if (CollUtil.isNotEmpty(list)) {
            ItemProcurement itemProcurement = list.get(0);
            return iBuyerService.getById(itemProcurement.getBuyerId());
        }
        return null;
    }

    @Override
    public Optional<Buyer> addBuyerByUid(Long uid) {
        final Optional<Buyer> buyer = iBuyerService.lambdaQuery()
                .eq(Buyer::getUserId, uid)
                .last("limit 1")
                .oneOpt();
        if (buyer.isPresent()) {
            return buyer;
        }

        final StaffInfo staffInfo = userGateway.queryStaffInfoById(uid);
        if (Objects.isNull(staffInfo)) {
            return Optional.empty();
        }

        Buyer newOne = new Buyer();
        newOne.setUserId(uid);
        newOne.setName(staffInfo.getNickname());
        newOne.setKingDeeId("0");
        iBuyerService.save(newOne);
        return Optional.of(newOne);
    }
}
