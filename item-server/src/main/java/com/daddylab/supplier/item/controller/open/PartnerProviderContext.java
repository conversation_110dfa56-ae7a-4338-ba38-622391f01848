package com.daddylab.supplier.item.controller.open;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;

import java.util.Objects;

/**
 * <AUTHOR> up
 * @date 2022年10月20日 10:05 AM
 */
public class PartnerProviderContext {

    protected static final ThreadLocal<PartnerProviderResp> HOLDER = new ThreadLocal<>();

    public static void setHolder(PartnerProviderResp resp) {
        HOLDER.set(resp);
    }

    public static Long getId() {
        PartnerProviderResp resp = HOLDER.get();
        if (Objects.isNull(resp)) {
            return null;
        }
        return Long.valueOf(resp.getId());
    }

    public static String getName() {
        PartnerProviderResp resp = HOLDER.get();
        if (Objects.isNull(resp)) {
            return "";
        }
        return resp.getOrganizationName();
    }

    public static String getSocialCreditCode() {
        PartnerProviderResp resp = HOLDER.get();
        if (Objects.isNull(resp)) {
            return "";
        }
        return resp.getOrganizationNo();
    }

    public static void remove(){
        HOLDER.remove();
    }

}
