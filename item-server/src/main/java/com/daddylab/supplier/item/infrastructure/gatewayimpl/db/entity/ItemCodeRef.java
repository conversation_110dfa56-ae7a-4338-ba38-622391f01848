package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemCodeRefType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 各种商品编码与后端商品的关联记录
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemCodeRef implements Serializable {

    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 编码 */
    private String code;

    /** 编码类型 1:后端商品编码 2:后端商品供货指定编码 3:P系统款号 4:后端商品SKU编码 5:后端商品SKU供货指定编码 6:商品参照库商品编码 7:商品参照库商品SKU编码... */
    private ItemCodeRefType type;

    /** 后端商品ID */
    private Long itemId;

    /**
     * 后端商品SKU ID
     */
    private Long skuId;
}
