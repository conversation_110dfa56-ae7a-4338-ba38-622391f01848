package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockInState;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 采购入库实体
 *
 * <AUTHOR>
 * @date 2022/3/24 18:02
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel("采购入库单数据模型")
public class StockInOrder implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private Long id;
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Long createdAt;
    @TableField(fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Long updatedAt;
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建UID")
    private Long createdUid;
    @TableField(fill = FieldFill.UPDATE)
    @ApiModelProperty(value = "更新UID")
    private Long updatedUid;
    @ApiModelProperty(value = "删除时间")
    private Long deletedAt;
    /**
     * 是否已删除
     */
    @TableLogic
    @ApiModelProperty(value = "是否已删除")
    private Integer isDel;
    /**
     * 入库单号 按照CGRK+年+月+日+001
     */
    @NotNull
    @ApiModelProperty(value = "入库单号")
    private String no;
    /**
     * 关联采购单号
     */
    @NotNull
    @ApiModelProperty(value = "关联采购单号")
    private String purchaseOrderNo;
    /**
     * 关联采购单id
     */
    @NotNull
    @ApiModelProperty(value = "关联采购单id")
    private Long purchaseOrderId;
    /**
     * 供应商id 筛选供应商列表中的数据
     */
    @NotNull
    @ApiModelProperty(value = "供应商id 筛选供应商列表中的数据")
    private Long providerId;
    /**
     * 收料(入库)日期 只能选择当天日期，及之后的日期，到日期，不需要到时分秒
     */
    @NotNull
    @ApiModelProperty(value = "收料(入库)日期 只能选择当天日期，及之后的日期，到日期，不需要到时分秒")
    private Long receiptTime;
    /**
     * 交货(入库)总数量
     */
    @NotNull
    @ApiModelProperty(value = "交货(入库)总数量")
    private Integer totalReceiptQuantity;
    /**
     * 采购数量
     */
    @NotNull
    @ApiModelProperty(value = "采购数量")
    private Integer purchaseQuantity;

    /**
     * 入库状态，1.待提交、2.待入库、3.已入库、4.已取消.
     *
     * @StockInState
     */
    @ApiModelProperty(value = "入库状态，1.待提交、2.待入库、3.已入库、4.已取消")
    private Integer state;

    /**
     * 收料组织id
     */
    @NotNull
    @ApiModelProperty(value = "收料组织id")
    private Long organizationId;
    /**
     * 采购员id 默认当前登陆人，若是系统生成，则采购员为“系统”
     */
    @NotNull
    @ApiModelProperty(value = "采购员id 默认当前登陆人，若是系统生成，则采购员为“系统”")
    private Long buyerUserId;
    /**
     * 采购组织id 默认1：杭州老爸电商科技有限公司
     */
    @NotNull
    @ApiModelProperty(value = "采购组织id 默认1：杭州老爸电商科技有限公司")
    private Long purchaseOrganizationId;
    /**
     * 采购部门 默认当前登陆人所在部门
     */
    @NotNull
    @ApiModelProperty(value = "采购部门 默认当前登陆人所在部门")
    private Long purchaseDepartmentId;
    /**
     * 采购组id 取金蝶系统-业务组列表中的数据
     */
    @NotNull
    @ApiModelProperty(value = "采购组id 取金蝶系统-业务组列表中的数据")
    private Long purchaseGroupId;

    /**
     * 入库仓库id 默认同步采购单中
     */
//    @NotNull
//    @ApiModelProperty(value = "入库仓库id 默认同步采购单中")
//    private String warehouseNo;

    /**
     * 备注
     */
    @NotNull
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否同步网点通，0不推送。1推送
     */
    @NotNull
    @ApiModelProperty(value = "是否同步网点通，0不推送。1推送")
    private Integer isSyncWdt;
    /**
     * 同步网点通情况下，旺店通返回的入库单号
     */
    @ApiModelProperty(value = "同步网点通情况下，旺店通返回的入库单号")
    private String wdtStorageNo;
    /**
     * 金蝶同步ID
     **/
    @ApiModelProperty(value = "金蝶同步ID")
    private String kingDeeId;
    /**
     * 同步失败明细
     **/
    @ApiModelProperty(value = "同步失败明细")
    private String syncFailDetail;

    private Integer businessLine;

    /**
     * 逆向出库单编码
     * 当hedgeStatus = 9 表示原单据作废时，此字段存在对应的逆向出库单编码
     */
    private String reverseOutNo;
    /**
     * {@link StockInState}
     */
    private StockInState hedgeStatus;
}
