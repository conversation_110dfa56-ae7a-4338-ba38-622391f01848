package com.daddylab.supplier.item.infrastructure.third.redbook;

import com.daddylab.supplier.item.infrastructure.third.redbook.domain.dto.RedBookMessageDTO;

/**
 * <AUTHOR>
 * @class MessageHandlerService.java
 * @description 小红书消息处理
 * @date 2024-03-01 15:39
 */
public interface IRedBookMessageHandlerService {
    /**
     * 无指定事件方法时执行该方法
     *
     * @param redBookMessageDTO RedBookMessageDTO
     * @date 2024/3/1 16:24
     * <AUTHOR>
     */
    void handler(RedBookMessageDTO redBookMessageDTO);
}
