package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableOrder;

import java.util.List;

/**
 * <p>
 * 采购应付表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
public interface IPurchasePayableOrderService extends IDaddyService<PurchasePayableOrder> {

    /**
     * 采购单是否关联到 入库单或者出库单
     *
     * @param idList
     * @param type   1采购入库应付、2采退出库应付
     * @return
     */
    Boolean related(List<Long> idList, Integer type);


    /**
     * 根据上游的出入库单单号，给此应付单打上冲销标志
     *
     * @param stockOrderNo
     * @param type
     */
    void markWriteOffWithStockOrder(String stockOrderNo);

}
