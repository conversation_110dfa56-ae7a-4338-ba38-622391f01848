package com.daddylab.supplier.item.infrastructure.logback;

import ch.qos.logback.classic.pattern.ExtendedThrowableProxyConverter;
import ch.qos.logback.classic.spi.IThrowableProxy;

/**
 * <AUTHOR>
 * @since 2022/9/23
 */
public class OneLineExtendedThrowableProxyConverter extends ExtendedThrowableProxyConverter {

    @Override
    protected String throwableProxyToString(IThrowableProxy tp) {
        return super.throwableProxyToString(tp).replaceAll("\n", " ~~ ")
                .replaceAll("\t", "    ");
    }
}
