package com.daddylab.supplier.item.application.message.wechat;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.controller.item.dto.ItemBuyerDto;
import com.daddylab.supplier.item.domain.item.gateway.ItemProcurementGateway;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAuditTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerModuleAuditTaskService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INewGoodsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffListQuery;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerAuditTaskState;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;

/**
 * 企微消息接收者信息处理封装工具类
 *
 * <AUTHOR> up
 * @date 2022年11月11日 5:58 PM
 */
@Service
@Slf4j
public class RecipientUtil {

    @Resource
    ItemMapper itemMapper;

    @Resource
    INewGoodsService iNewGoodsService;

    @Resource
    private ItemProcurementGateway itemProcurementGateway;

    @Resource
    StaffService staffService;

    @Resource
    RecipientConfig recipientConfig;

    @Resource
    @Qualifier("UserGatewayCacheImpl")
    UserGateway userGateway;

    @Resource
    IItemDrawerService iItemDrawerService;

    @Resource
    IItemDrawerModuleAuditTaskService iItemDrawerModuleAuditTaskService;

    public Set<RecipientBo> getRecipient(MsgEventType msgEventType,
                                         List<Long> itemIdList,
                                         MsgEvent.ItemLaunchPlaneOperator operator,
                                         MsgEvent.PrincipalChangeInfo changeInfo) {
        switch (msgEventType) {
            //  「待完善」-「待设计」,产品负责人
            case IMPROVE_TO_DESIGN:
                return new HashSet<>(getProducerQwUserId(itemIdList));
            //  「待设计」-「法务审核」,法务
            case DESIGN_TO_AUDIT_LEGAL:
                return new HashSet<>(getAllLegalQwUserId(itemIdList));
            //  「法务审核」- 「QC审核」,QC负责人
            case DESIGN_TO_AUDIT_QC:
            case LIVE_SPEECH_AUDIT_QC:
                return new HashSet<>(getQcQwUserId(itemIdList));
            // 待审核 - 待修改。
            case AUDIT_TO_MODIFY:
                HashSet<RecipientBo> set1 = new HashSet<>(getProducerQwUserId(itemIdList));
//                set1.addAll(getAuditToModify(itemIdList));
                set1.addAll(getMiniGaoQwUserId(itemIdList));
                set1.addAll(getMaydayQwUserId(itemIdList));
                return set1;
            // 上新计划完成提交，上新计划字段变更（首页文案或者上新价）。产品组+设计组
            case PLAN_SUBMIT:
            case PLAN_INFO_CHANGE:
                HashSet<RecipientBo> set2 = new HashSet<>(getAllProduceQwUserId(operator.getPlanId()));
                set2.addAll(getAllDesignQwUserId(operator.getPlanId()));
                return set2;
            //  上架后某一字段发生变更,采购+QC+产品负责人+平台运营
            case ONE_FIELD_CHANGE:
                HashSet<RecipientBo> set3 = new HashSet<>(getBuyerQwUserId(itemIdList));
                set3.addAll(getQcQwUserId(itemIdList));
                set3.addAll(getProducerQwUserId(itemIdList));
                set3.addAll(getPlatformRunnerQwUserId(itemIdList));
                return set3;
            //  修改产品负责人
            case CHARGER_CHANGE:
                return new HashSet<>(getNewChargeQwUserId(itemIdList, changeInfo));
            // 直播话术审核人，审核完成后，提醒最后一个商品抽屉信息修改者。
            case LIVE_SPEECH:
                return new HashSet<>(getLiveSpeechUserId(itemIdList));
            // 直播话术审核提醒
            case LIVE_SPEECH_AUDIT_LEGAL:
                return new HashSet<>(getLiveSpeechSubmit(itemIdList));
            //【待上架】、【已上架】任何修改，点击【提交】
            case SHELF_ITEM_INFO_CHANG:
                return new HashSet<>(shelfItemInfoChange(itemIdList));
            default:
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "非法企微事件触发类型");
        }
    }

    private List<RecipientBo> getTest(List<Long> itemIdList) {
        RecipientBo bo = new RecipientBo();
        bo.setUserId(7405775L);
        bo.setQwUserId("yulin.du");
        bo.setIdList(itemIdList);
        return ListUtil.of(bo);
    }

    /**
     * 查询返回用户的企微账号
     *
     * @param userIds
     * @return
     */
    private Map<Long, String> getQwUserIdList(List<Long> userIds) {
        List<DadStaffVO> staffList = staffService.getStaffList(userIds);
        if (CollUtil.isEmpty(staffList)) {
            return new HashMap<>(8);
        }
        return staffList.stream().collect(Collectors.toMap(DadStaffVO::getUserId, DadStaffVO::getQwUserId, (a, b) -> a));
    }

    /**
     * 获取商品的产品负责人
     *
     * @param itemIdList
     * @return itemId-principalId
     */
    public List<RecipientBo> getProducerQwUserId(List<Long> itemIdList) {
        List<NewGoods> newGoodsList = iNewGoodsService.lambdaQuery().in(NewGoods::getItemId, itemIdList).select().list();
        Map<Long, List<Long>> userIdAndItemIdsMap = newGoodsList.stream().collect(Collectors.groupingBy(NewGoods::getPrincipalId
                , Collectors.mapping(NewGoods::getItemId, Collectors.toList())));

        List<Long> userIdList = new LinkedList<>(userIdAndItemIdsMap.keySet());
        Map<Long, String> userIdAndQwUserIdMap = getQwUserIdList(userIdList);

        return joinRecipientBos(userIdAndQwUserIdMap, userIdAndItemIdsMap);
    }

    /**
     * 获取采购负责人
     *
     * @param itemIdList
     * @return
     */
    public List<RecipientBo> getBuyerQwUserId(List<Long> itemIdList) {
        // 存在，一个采购负责人对应多个itemId的情况
        List<ItemBuyerDto> itemBuyerDtoList = itemMapper.selectBatchBuyersByItemIds(itemIdList);
        List<Long> buyerUserIdList = itemBuyerDtoList.stream().map(ItemBuyerDto::getBuyerUserId).distinct().collect(Collectors.toList());
        Map<Long, String> userIdAndQwUserIdMap = getQwUserIdList(buyerUserIdList);

        Map<Long, List<Long>> userIdAndItemIdsMap = itemBuyerDtoList.stream().collect(Collectors.groupingBy(ItemBuyerDto::getBuyerUserId
                , Collectors.mapping(ItemBuyerDto::getItemId, Collectors.toList())));

        return joinRecipientBos(userIdAndQwUserIdMap, userIdAndItemIdsMap);

    }

    @NotNull
    private List<RecipientBo> joinRecipientBos(Map<Long, String> userIdAndQwUserIdMap, Map<Long, List<Long>> userIdAndItemIdsMap) {
        List<RecipientBo> result = new LinkedList<>();
        userIdAndItemIdsMap.forEach((k, v) -> {
            RecipientBo bo = new RecipientBo();
            bo.setUserId(k);
            bo.setQwUserId(userIdAndQwUserIdMap.get(k));
            Set<Long> newSet = new HashSet<>(userIdAndItemIdsMap.get(k));
            bo.setIdList(new ArrayList<>(newSet));
            result.add(bo);
        });
        return result;
    }

    /**
     * 获取商品的qc负责人
     *
     * @param itemIdList
     * @return
     */
    public List<RecipientBo> getQcQwUserId(List<Long> itemIdList) {
        // itemId,qcIdString 一个商品可能存在多个qcId。
        Map<Long, String> itemIdAndQcIdsMap = itemProcurementGateway.getQcIdList(itemIdList);
        Set<Long> qcUserIdSet = new HashSet<>();
        itemIdAndQcIdsMap.values().forEach(qcIds -> {
            if (isNotBlank(qcIds)) {
                List<String> strings = Arrays.asList(qcIds.split(","));
                List<Long> collect = strings.stream().map(Long::valueOf).distinct().collect(Collectors.toList());
                qcUserIdSet.addAll(collect);
            }
        });
        if (CollUtil.isEmpty(qcUserIdSet)) {
            return new LinkedList<>();
        }
        Map<Long, String> userIdAndQwUserIdMap = getQwUserIdList(new ArrayList<>(qcUserIdSet));

        List<RecipientBo> result = new LinkedList<>();
        for (Long qcUserId : qcUserIdSet) {
            List<Long> qcItemIdList = new LinkedList<>();
            itemIdAndQcIdsMap.forEach((itemId, qcIds) -> {
                if (qcIds.contains(qcUserId.toString())) {
                    qcItemIdList.add(itemId);
                }
            });

            RecipientBo bo = new RecipientBo();
            bo.setUserId(qcUserId);
            bo.setQwUserId(userIdAndQwUserIdMap.get(qcUserId));
            bo.setIdList(qcItemIdList);
            result.add(bo);
        }

        return result;
    }

    /**
     * 获得法务部整体的企微账号信息
     *
     * @return
     */
    public List<RecipientBo> getAllLegalQwUserId(List<Long> itemIdList) {
        if (SpringUtil.getActiveProfile().equals("dev") || SpringUtil.getActiveProfile().equals("test")) {
            return getTest(itemIdList);
        }

        StaffListQuery staffListQuery = new StaffListQuery();
        staffListQuery.setDept("法务内控部");
        return getRecipientBosByDept(itemIdList, staffListQuery);
    }

    /**
     * 获取所有的法务用户ID
     *
     * @return java.util.List<java.lang.Long>
     * @date 2024/4/11 15:29
     * <AUTHOR>
     */
    public List<Long> getAllLegalUserId() {
        StaffListQuery staffListQuery = new StaffListQuery();
        staffListQuery.setDept("法务内控部");
        List<StaffInfo> staffInfos = userGateway.queryStaffList(staffListQuery);
        return staffInfos.stream().map(StaffInfo::getUserId).collect(Collectors.toList());
    }




    public List<RecipientBo> getAllProduceQwUserId(Long planId) {
        StaffListQuery staffListQuery = new StaffListQuery();
        if (SpringUtil.getActiveProfile().equals("dev") || SpringUtil.getActiveProfile().equals("test")) {
            staffListQuery.setUserId(7405775L);
        } else {
            staffListQuery.setDept("产品组");
        }
        return getRecipientBosByDept(Collections.singletonList(planId), staffListQuery);
    }


    public List<RecipientBo> getAllDesignQwUserId(Long planId) {
        StaffListQuery staffListQuery = new StaffListQuery();
        if (SpringUtil.getActiveProfile().equals("dev") || SpringUtil.getActiveProfile().equals("test")) {
            staffListQuery.setUserId(7405775L);
        } else {
            staffListQuery.setDept("设计组");
        }
        return getRecipientBosByDept(Collections.singletonList(planId), staffListQuery);
    }

//    @Deprecated
//    private List<RecipientBo> getAuditToModify(List<Long> itemIdList) {
//
//        if (SpringUtil.getActiveProfile().equals("dev") || SpringUtil.getActiveProfile().equals("test")) {
//            return getTest(itemIdList);
//        }
//
//        List<RecipientBo> list = new LinkedList<>(getProducerQwUserId(itemIdList));
//
//        List<String> auditToModify = recipientConfig.getAuditToModify();
//        if (CollUtil.isNotEmpty(auditToModify)) {
//            auditToModify.forEach(nickName -> {
//                StaffListQuery staffListQuery = new StaffListQuery();
//                staffListQuery.setNickname(nickName);
//                List<RecipientBo> recipientBosByDept = getRecipientBosByDept(itemIdList, staffListQuery);
//                list.addAll(recipientBosByDept);
//            });
//
//        }
//        return list;
//    }

    //    @Deprecated
    private List<RecipientBo> getMiniGaoQwUserId(List<Long> itemIdList) {
        if (SpringUtil.getActiveProfile().equals("dev") || SpringUtil.getActiveProfile().equals("test")) {
            return getTest(itemIdList);
        }
        StaffListQuery staffListQuery = new StaffListQuery();
        staffListQuery.setNickname("小高");
        return getRecipientBosByDept(itemIdList, staffListQuery);
    }

    @Deprecated
    private List<RecipientBo> getMaydayQwUserId(List<Long> itemIdList) {
//        if (SpringUtil.getActiveProfile().equals("dev") || SpringUtil.getActiveProfile().equals("test")) {
//            return getTest(itemIdList);
//        }

        List<String> mayday = recipientConfig.getMayday();
        List<DadStaffVO> staffListByLoginName = staffService.getStaffListByLoginName(mayday);
        return staffListByLoginName.stream().map(val -> {
            RecipientBo bo = new RecipientBo();
            bo.setUserId(val.getUserId());
            bo.setQwUserId(val.getQwUserId());
            bo.setIdList(itemIdList);
            return bo;
        }).collect(Collectors.toList());
    }

    @NotNull
    private List<RecipientBo> getRecipientBosByDept(List<Long> idList, StaffListQuery staffListQuery) {
        List<StaffInfo> staffInfos = userGateway.queryStaffList(staffListQuery);
        List<Long> allUserIdList = staffInfos.stream().map(StaffInfo::getUserId).collect(Collectors.toList());
        Map<Long, String> userIdAndQwUserIdMap = getQwUserIdList(allUserIdList);

        List<RecipientBo> result = new LinkedList<>();
        userIdAndQwUserIdMap.forEach((userId, userQwUserId) -> {
            RecipientBo bo = new RecipientBo();
            bo.setUserId(userId);
            bo.setQwUserId(userQwUserId);
            bo.setIdList(idList);
            result.add(bo);
        });

        return result;
    }

    /**
     * 平台运营
     *
     * @param itemIdList
     * @return
     */
    public List<RecipientBo> getPlatformRunnerQwUserId(List<Long> itemIdList) {
        return recipientConfig.getPlatformList().stream().map(val -> {
            RecipientBo bo = new RecipientBo();
            bo.setUserId(null);
            bo.setQwUserId(val);
            bo.setIdList(itemIdList);
            return bo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取新的负责人。
     *
     * @param itemIdList
     * @return
     */
    public List<RecipientBo> getNewChargeQwUserId(List<Long> itemIdList, MsgEvent.PrincipalChangeInfo changeInfo) {
        DadStaffVO staff = staffService.getStaff(changeInfo.getNewPrincipalId());

        RecipientBo bo = new RecipientBo();
        bo.setUserId(staff.getUserId());
        bo.setQwUserId(staff.getQwUserId());
        bo.setIdList(itemIdList);
        return Collections.singletonList(bo);
    }

    /**
     * 当直播话术过审的时候，通知直播话术的提交人
     * 从单个商品维度出发，每一个商品可能都是存在不同的话术提交人
     *
     * @param itemIdList
     * @return
     */
    public List<RecipientBo> getLiveSpeechUserId(List<Long> itemIdList) {
        if (CollUtil.isEmpty(itemIdList)) {
            return new LinkedList<>();
        }

        Long itemId = itemIdList.get(0);
        List<ItemDrawerModuleAuditTask> list = iItemDrawerModuleAuditTaskService.lambdaQuery()
                .eq(ItemDrawerModuleAuditTask::getItemId, itemId)
                .eq(ItemDrawerModuleAuditTask::getType, ItemAuditType.LIVE_VERBAL_TRICK)
                .eq(ItemDrawerModuleAuditTask::getAuditStatus, ItemDrawerAuditTaskState.PENDING)
                .orderByDesc(ItemDrawerModuleAuditTask::getId).list();
        Long createdUid = list.get(0).getCreatedUid();
        if (Objects.nonNull(createdUid)) {
            DadStaffVO staff = staffService.getStaff(createdUid);
            RecipientBo bo = new RecipientBo();
            bo.setUserId(createdUid);
            bo.setQwUserId(staff.getQwUserId());
            bo.setIdList(Collections.singletonList(itemId));
            return Collections.singletonList(bo);
        } else {
            log.error("直播话术审核完成，通知人信息查询为空，itemId:{}", JsonUtil.toJson(itemId));
            return new LinkedList<>();
        }
    }

    /**
     * 提交直播话术审核，接受者查询。
     * 法务负责人，如果是先提交的直播话术审核，还有进行抽屉审核执行法务，那么退给全部法务。
     *
     * @param itemIdList
     * @return
     */
    public List<RecipientBo> getLiveSpeechSubmit(List<Long> itemIdList) {
        if (CollUtil.isEmpty(itemIdList)) {
            return new LinkedList<>();
        }
        Long itemId = itemIdList.get(0);
        List<ItemDrawerModuleAuditTask> list = iItemDrawerModuleAuditTaskService.lambdaQuery()
                .eq(ItemDrawerModuleAuditTask::getItemId, itemId)
                .eq(ItemDrawerModuleAuditTask::getType, ItemAuditType.LIVE_VERBAL_TRICK)
                .eq(ItemDrawerModuleAuditTask::getAuditStatus, ItemDrawerAuditTaskState.PENDING)
                .orderByDesc(ItemDrawerModuleAuditTask::getId).list();
        if (CollUtil.isEmpty(list)) {
            log.error("直播话术待审核消息处理，待审核状态审核任务查询为空。itemIdList:{}", JsonUtil.toJson(itemIdList));
            return new LinkedList<>();
        }
        ItemDrawerModuleAuditTask itemDrawerModuleAuditTask = list.get(0);
        Long processorId = itemDrawerModuleAuditTask.getProcessorId();
        // 如果任务认领，给指定法务发消息，否则，全部法务
        if (Objects.nonNull(processorId) && processorId > 0) {
            StaffListQuery staffListQuery = new StaffListQuery();
            staffListQuery.setUserId(processorId);
            return getRecipientBosByDept(itemIdList, staffListQuery);
        } else {
            return getAllLegalQwUserId(itemIdList);
        }
    }

    public List<RecipientBo> shelfItemInfoChange(List<Long> itemIdList) {
        List<RecipientBo> resList = new LinkedList<>();

        if (CollUtil.isEmpty(itemIdList)) {
            return resList;
        }

        List<RecipientBo> producerQwUserId = getProducerQwUserId(itemIdList);
        List<String> shelfItemInfoChange = recipientConfig.getShelfItemInfoChange();

        if (CollUtil.isEmpty(shelfItemInfoChange)) {
            resList.addAll(producerQwUserId);
            return resList;
        }

        List<RecipientBo> collect = staffService.getStaffListByLoginName(shelfItemInfoChange)
                .stream().map(val -> {
                    RecipientBo bo = new RecipientBo();
                    bo.setUserId(val.getUserId());
                    bo.setQwUserId(val.getQwUserId());
                    bo.setIdList(itemIdList);
                    return bo;
                }).collect(Collectors.toList());
        collect.addAll(resList);
        return collect;
    }

}
