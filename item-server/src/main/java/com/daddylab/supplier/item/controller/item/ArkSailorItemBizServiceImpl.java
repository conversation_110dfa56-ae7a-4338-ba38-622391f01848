package com.daddylab.supplier.item.controller.item;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.ark.sailor.common.base.vo.BaseVO;
import com.daddylab.ark.sailor.item.common.base.vo.Result;
import com.daddylab.ark.sailor.item.domain.query.item.ItemPageQuery;
import com.daddylab.ark.sailor.item.domain.vo.item.ItemListVO;
import com.daddylab.ark.sailor.item.domain.vo.item.ItemSkuVO;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.ArkSailorItemFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ErpItemSkuUpdateStockParam;
import com.daddylab.supplier.item.infrastructure.utils.Logs;
import com.daddylab.supplier.item.types.item.ItemListWithSkuVO;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/1/22
 */
@Service
@Slf4j
public class ArkSailorItemBizServiceImpl implements ArkSailorItemBizService {

    @Autowired
    private ArkSailorItemFeignClient arkSailorItemFeignClient;

    @Autowired
    private ProviderGateway providerGateway;

    @Autowired
    private ItemGateway itemGateway;

    @Override
    public PageResponse<ItemListWithSkuVO> itemWithSkuPageQuery(ItemPageQuery query) {
        final Result<Page<ItemListVO>> pageResult = arkSailorItemFeignClient.itemPageQuery(query);
        final IPage<ItemListVO> data = pageResult.getData();
        final List<ItemListVO> records = data.getRecords();
        final List<Long> ids = records.stream().map(BaseVO::getId).collect(Collectors.toList());
        final Result<List<ItemSkuVO>> skuVOResults =
                arkSailorItemFeignClient.itemSkuQueryByItemIds(ids);
        final Map<Long, List<ItemSkuVO>> skuVOMap =
                skuVOResults.getData().stream()
                            .collect(Collectors.groupingBy(ItemSkuVO::getItemId));
        final ArrayList<ItemListWithSkuVO> itemListWithSkuVOS = new ArrayList<>();
        for (ItemListVO record : records) {
            final ItemListWithSkuVO vo = new ItemListWithSkuVO();
            vo.setItem(record);
            vo.setSkus(skuVOMap.get(record.getId()));

            itemListWithSkuVOS.add(vo);
        }
        return PageResponse.of(
                itemListWithSkuVOS,
                (int) data.getTotal(),
                (int) data.getPages(),
                (int) data.getCurrent());
    }

    @Override
    public MultiResponse<ItemListVO> listItemByItemNos(List<String> itemNos) {
        if (CollectionUtil.isEmpty(itemNos)) {
            return MultiResponse.of(Collections.emptyList());
        }
        final ItemPageQuery itemPageQuery = new ItemPageQuery();
        itemPageQuery.setItemNos(itemNos);
        itemPageQuery.setSize(100);
        final Result<Page<ItemListVO>> pageResult = arkSailorItemFeignClient.itemPageQuery(itemPageQuery);
        final IPage<ItemListVO> data = pageResult.getData();
        final List<ItemListVO> records = data.getRecords();
        return MultiResponse.of(records);
    }

    @Override
    public PageResponse<ItemListVO> itemPageQuery(ItemPageQuery query) {
        final Result<Page<ItemListVO>> pageResult = arkSailorItemFeignClient.itemPageQuery(query);
        final IPage<ItemListVO> data = pageResult.getData();
        final List<ItemListVO> records = data.getRecords();
        return PageResponse.of(records, (int) data.getTotal(), (int) data.getPages(), (int) data.getCurrent());
    }

    @Override
    public MultiResponse<ItemSkuVO> itemSkuQueryByItemIds(List<Long> itemIds) {
        if (CollectionUtil.isEmpty(itemIds)) {
            return MultiResponse.of(Collections.emptyList());
        }
        final Result<List<ItemSkuVO>> result = arkSailorItemFeignClient.itemSkuQueryByItemIds(itemIds);
        return MultiResponse.of(result.getData());

    }

    @Override
    public SingleResponse<Boolean> updateStock(ErpItemSkuUpdateStockParam erpItemSkuUpdateStockParam) {
        final Result<Boolean> result = arkSailorItemFeignClient.updateStock(erpItemSkuUpdateStockParam);
        if (result.getCode() != Result.SUCCESSFUL_CODE) {
            Logs.log(log,
                     Level.ERROR,
                     "[平台商品库存上传][小程序]上传异常",
                     Logs.var("request", erpItemSkuUpdateStockParam),
                     Logs.var("response", result).asJson());
            //noinspection unchecked
            return SingleResponse.buildFailure(ErrorCode.PLATFORM_ITEM_STOCK_SYNC_ERROR.getCode(), result.getMsg());
        }
        Logs.log(log,
                 Level.INFO,
                 "[平台商品库存上传][小程序]上传成功",
                 Logs.var("request", erpItemSkuUpdateStockParam),
                 Logs.var("response", result).asJson());
        return SingleResponse.of(result.getData());

    }


}
