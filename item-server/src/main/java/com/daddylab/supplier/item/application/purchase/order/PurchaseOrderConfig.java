package com.daddylab.supplier.item.application.purchase.order;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/1/16
 */
@Configuration
@ConfigurationProperties(prefix = "purchase-order")
@RefreshScope
@Data
public class PurchaseOrderConfig {
    /** 采购单审核超时时间 */
    private int auditTimeout = 86400;

    /** 审核超时后自动通过的节点配置 */
    private String[] nodeOfAutoAgreeAfterTimeout = new String[] {"财务主管审核", "老板审核"};

    /** 付款申请单申请金额允许超出实际可付款金额的百分比 */
    private int paymentExtraPercent = 10;
}
