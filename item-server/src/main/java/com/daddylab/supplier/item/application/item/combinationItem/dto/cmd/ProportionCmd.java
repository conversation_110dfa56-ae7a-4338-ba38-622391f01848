package com.daddylab.supplier.item.application.item.combinationItem.dto.cmd;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2022年11月04日 3:24 PM
 */
@ApiModel("组合装金额占比计算封装")
@Data
public class ProportionCmd extends Command {


    private static final long serialVersionUID = -3985154515881065732L;

    @ApiModelProperty("sku编码")
    @NotEmpty
    private String skuCode;

    @ApiModelProperty("数量")
    @NotNull
    private Integer count;

    @ApiModelProperty("成本价格")
    @NotNull
    private BigDecimal costPrice;

    @ApiModelProperty("销售价格")
    @NotNull
    private BigDecimal salePrice;


}
