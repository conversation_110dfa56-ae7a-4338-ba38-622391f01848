package com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/27 3:04 下午
 * @description
 */
@AllArgsConstructor
@NoArgsConstructor
@Setter
@Getter
@ToString
public class PartnerProviderResp {

    private String addr;

    private String area;

    private String city;

    private Integer id;

    private String mobile;

    /**
     * 供应商名称
     */
    private String organizationName;

    /**
     * 供应商社会信用代码
     */
    private String organizationNo;

    /**
     * 账号id
     */
    private Integer partnerId;

    private String province;

    /**
     * 供应商 联系人
     */
    private String uName;

    private List<String> addressCascadeList;

    /**
     * 商家后台 shopId
     */
    private Integer shopId;

    /** 是否黑名单 0否 1是 */
    @JsonProperty("is_blacklist")
    @JSONField(name = "is_blacklist")
    private Integer isBlacklist;
}

