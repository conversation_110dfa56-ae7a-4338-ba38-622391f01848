package com.daddylab.supplier.item.application.afterSales;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesAbnormalState;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022年10月17日 10:48 AM
 */
@ApiModel("异常件列表返回封装")
@Data
public class AfterSalesPageVO {

    @ApiModelProperty("物流编号")
    private String logisticsNo;

    @ApiModelProperty("创建时间")
    private String createdAt;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("供应商名称")
    private String providerName;

    @ApiModelProperty("异常件聚合状态")
    private AfterSalesAbnormalState groupState;

    List<AfterSalesLinePageVO> lineList;


}
