package com.daddylab.supplier.item.application.exportTask;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.controller.item.dto.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/9 3:01 下午
 * @description
 */
public interface ExportTaskBizService {

    PageResponse<ExportTaskVo> exportList(TaskPageQuery pageQuery);

    SingleResponse<String> getExcelDownloadUrl(Long expertTaskId);

    void clearExportList(String type);

}
