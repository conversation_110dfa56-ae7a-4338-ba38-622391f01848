package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataBackup;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DataBackupMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDataBackupService;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据备份表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Service
public class DataBackupServiceImpl extends DaddyServiceImpl<DataBackupMapper, DataBackup> implements IDataBackupService {

    @Override
    public long backup(Object backupData, String... keys) {
        final DataBackup dataBackup = new DataBackup();
        if (backupData instanceof CharSequence) {
            dataBackup.setBackupData(((CharSequence) backupData).toString());
        } else {
            dataBackup.setBackupData(JsonUtil.toJson(backupData));
        }
        for (int i = 0; i < keys.length; i++) {
            if (i == 0) {
                dataBackup.setKey1(keys[i]);
            } else if (i == 1) {
                dataBackup.setKey2(keys[i]);
            } else if (i == 2) {
                dataBackup.setKey3(keys[i]);
            }
        }
        save(dataBackup);
        return dataBackup.getId();
    }
}
