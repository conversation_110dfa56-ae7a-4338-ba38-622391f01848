package com.daddylab.supplier.item.application.message;

import cn.hutool.core.lang.Assert;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.application.message.param.ConfigCmd;
import com.daddylab.supplier.item.application.message.param.ListPageQuery;
import com.daddylab.supplier.item.application.message.vo.ConfigVoList;
import com.daddylab.supplier.item.application.message.vo.MessageConfigVO;
import com.daddylab.supplier.item.application.message.vo.MessageVO;
import com.daddylab.supplier.item.application.message.vo.MsgRecipientVO;
import com.daddylab.supplier.item.common.trans.MessageTransMapper;
import com.daddylab.supplier.item.domain.message.dto.MsgRecipientObj;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Message;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.MessageConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessageOperationType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessagePushType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessageState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IMessageConfigService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IMessageService;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 2:35 下午
 * @description
 */
@Service
@Slf4j
public class MessageBizServiceImpl implements MessageBizService {

    @Autowired
    IMessageService iMessageService;

    @Autowired
    IMessageConfigService messageConfigService;

    @Override
    public PageResponse<MessageVO> pageMessages(ListPageQuery pageQuery) {
        Long recipientId = 0 == UserContext.getUserId() ? pageQuery.getRecipientId() : UserContext.getUserId();
        Assert.notNull(recipientId, "消息接收者id不得为空");

        LambdaQueryWrapper<Message> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Message::getRecipientId, recipientId);
        queryWrapper.eq(MessagePushType.DEFAULT.equals(pageQuery.getPushType()), Message::getPushDefault, 1);
        if (MessagePushType.FORCE_REMIND.equals(pageQuery.getPushType())) {
            queryWrapper.and(messageLambdaQueryWrapper -> messageLambdaQueryWrapper.eq(Message::getPushRemind, 1).eq(Message::getState, MessageState.NO_READ));
        }
        queryWrapper.eq(MessagePushType.MAIL.equals(pageQuery.getPushType()), Message::getPushMail, 1);
        queryWrapper.eq(MessagePushType.TEXT.equals(pageQuery.getPushType()), Message::getPushText, 1);
        if (MessagePushType.ALL.equals(pageQuery.getPushType())) {
            queryWrapper.and(messageLambdaQueryWrapper -> messageLambdaQueryWrapper.eq(Message::getPushDefault, 1).or().eq(Message::getPushRemind, 1)
                    .or().eq(Message::getPushText, 1).or().eq(Message::getPushMail, 1));
        }
        queryWrapper.orderByDesc(Message::getCreatedAt);

        IPage<Message> iPage = new Page<>(pageQuery.getPageIndex(), pageQuery.getPageSize());
        final IPage<Message> page = iMessageService.page(iPage, queryWrapper);
        final List<MessageVO> list = MessageTransMapper.INSTANCE.toListVOs(page.getRecords());
        return PageResponse.of(list, (int) page.getTotal(), pageQuery.getPageSize(), pageQuery.getPageIndex());

    }

    @Override
    public SingleResponse<Boolean> removeAll(Long recipientId) {
        Long id = 0 == UserContext.getUserId() ? recipientId : UserContext.getUserId();
        Assert.notNull(id, "消息接收者id不得为空");

        QueryWrapper<Message> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Message::getRecipientId, id);
        return SingleResponse.of(iMessageService.removeWithTime(queryWrapper));

    }


    @Override
    public SingleResponse<Boolean> allRead(Long recipientId) {
        Long id = 0 == UserContext.getUserId() ? recipientId : UserContext.getUserId();
        Assert.notNull(id, "消息接收者id不得为空");

        final LambdaUpdateWrapper<Message> updateChainWrapper = new LambdaUpdateWrapper<>();
        updateChainWrapper.eq(Message::getRecipientId, id).eq(Message::getState, MessageState.NO_READ);
        updateChainWrapper.set(Message::getState, MessageState.HAD_READ);
        return SingleResponse.of(iMessageService.update(updateChainWrapper));
    }

    @Override
    public SingleResponse<Boolean> read(Long messageId) {
        final LambdaUpdateWrapper<Message> updateChainWrapper = new LambdaUpdateWrapper<>();
        updateChainWrapper.eq(Message::getId, messageId).eq(Message::getState, MessageState.NO_READ);
        updateChainWrapper.set(Message::getState, MessageState.HAD_READ);
        return SingleResponse.of(iMessageService.update(updateChainWrapper));
    }

    @Override
    public SingleResponse<ConfigVoList> listMessageConfig() {
        ConfigVoList vo = new ConfigVoList();

        List<MessageConfigVO> backItemList = new LinkedList<>();
        List<MessageConfigVO> platformList = new LinkedList<>();

        final List<MessageConfig> allList = messageConfigService.list();
        for (MessageConfig messageConfig : allList) {
            final MessageConfigVO messageConfigVO = MessageTransMapper.INSTANCE.toListConfigVo(messageConfig);

            List<MessagePushType> pushTypeList = new LinkedList<>();
            if (messageConfig.getPushDefault() > 0) {
                pushTypeList.add(MessagePushType.DEFAULT);
            }
            if (messageConfig.getPushRemind() > 0) {
                pushTypeList.add(MessagePushType.FORCE_REMIND);
            }
            if (messageConfig.getPushText() > 0) {
                pushTypeList.add(MessagePushType.TEXT);
            }
            if (messageConfig.getPushMail() > 0) {
                pushTypeList.add(MessagePushType.MAIL);
            }
            messageConfigVO.setPushType(pushTypeList);

            if (MessageOperationType.WARN_PLATFORM_ITEM.equals(messageConfigVO.getOperationType())) {
                platformList.add(messageConfigVO);
            } else {
                backItemList.add(messageConfigVO);
            }
        }
        vo.setBackItemList(backItemList);
        vo.setPlatformWarnList(platformList);
        return SingleResponse.of(vo);
    }

    @Override
    public SingleResponse<Boolean> saveConfig(ConfigCmd configCmd) {
        QueryWrapper<MessageConfig> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(MessageConfig::getId, configCmd.getId());
        final MessageConfig one = messageConfigService.getOne(wrapper);
        Assert.isTrue(Objects.nonNull(one), "消息配置id无效，id：" + configCmd.getId());

        one.setCanEffect(configCmd.getCanEffect() ? 1 : 0);
        one.setOperationType(configCmd.getOperationType());
        one.setPushDefault(configCmd.getPushDefault() ? 1 : 0);
        one.setPushText(configCmd.getPushText() ? 1 : 0);
        one.setPushMail(configCmd.getPushMail() ? 1 : 0);
        one.setPushRemind(configCmd.getPushRemind() ? 1 : 0);

        final List<MsgRecipientVO> recipients = configCmd.getRecipients();
        final List<MsgRecipientObj> msgRecipientObjList = MessageTransMapper.INSTANCE.msgRecipientToObjs(recipients);
        one.setRecipients(JsonUtil.objToStr(msgRecipientObjList));

        return SingleResponse.of(messageConfigService.updateById(one));

    }

    @Override
    public SingleResponse<Integer> countNoRead(Long recipientId) {
        Long id = 0 == UserContext.getUserId() ? recipientId : UserContext.getUserId();

        QueryWrapper<Message> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Message::getRecipientId, id).eq(Message::getState, MessageState.NO_READ);
        final int count = iMessageService.count(queryWrapper);

        return SingleResponse.of(count);
    }
}
