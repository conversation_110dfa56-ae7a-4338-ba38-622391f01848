package com.daddylab.supplier.item.application.order.settlement.dto;

import com.daddylab.supplier.item.application.order.settlement.sys.DetailStaticDo;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayApplyStatus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年08月09日 2:21 PM
 */
@ApiModel("查看采购结算单，单据基础信息")
@Data
public class FormDetailVo {

    @ApiModelProperty("采购供应商Id")
    private Long pProviderId;
    @ApiModelProperty("采购供应商名称")
    private String pProviderName;
    @ApiModelProperty("采购供应商是否黑名单")
    private Integer pProviderIsBlacklist;

    @ApiModelProperty("结算供应商Id")
    private Long sProviderId;
    @ApiModelProperty("结算供应商名称")
    private String sProviderName;
    @ApiModelProperty("结算供应商是否黑名单")
    private Integer sProviderIsBlacklist;

    @ApiModelProperty("采购组织Id")
    private Long pOrgId;
    @ApiModelProperty("采购组织名称")
    private String pOrgName;

    @ApiModelProperty("关联采购单号")
    private List<PurchaseOrderDo> purchaseOrders;

    @ApiModelProperty("订单员")
    private String orderPersonnel;

    @ApiModelProperty("仓库编号")
    private String warehouseNo;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

//    @ApiModelProperty("采购结算单状态")
//    private OrderSettlementStatus status;

    @ApiModelProperty("采购结算单明细的汇总信息")
    private DetailStaticDo staticDo;

    @ApiModelProperty("表单更新时间，已结算单据的更新时间")
    private Long updatedTime;

    @ApiModelProperty("结算单编码")
    private String no;

    @ApiModelProperty("已选中的账单ids")
    private List<Long> selectBillIds;

    private Integer businessLine;

    @ApiModelProperty("付款申请状态")
    private PayApplyStatus payApplyStatus;

    @ApiModelProperty("结算周期")
    private String cycleDate;

    private Integer version;

}
