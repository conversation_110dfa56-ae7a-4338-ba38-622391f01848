package com.daddylab.supplier.item.infrastructure.userInfoCrypto;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2022/5/26
 */
@ConfigurationProperties(prefix = "user-info-secret")
@Configuration("userInfoCryptoConfig")
@Data
public class Config {
    private String securityKey;
    private String ivKey;
    private String encryptMode;
}
