package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSyncLogDetail;

/**
 * <p>
 * 商品同步日志明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface IItemSyncLogDetailService extends IDaddyService<ItemSyncLogDetail> {

}
