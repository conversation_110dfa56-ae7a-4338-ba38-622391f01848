package com.daddylab.supplier.item.application.salesInStock.dto;

import com.daddylab.supplier.item.application.salesOutStock.dto.BuyerNameDO;
import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collection;

/**
 * <AUTHOR> up
 * @date 2023年10月23日 10:13 AM
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SalesInStockPageQuery extends PageQuery {
    private static final long serialVersionUID = -1919043781624258360L;

    @ApiModelProperty("入库单号")
    private String orderNo;

    @ApiModelProperty("实际入库仓")
    private String warehouseNo;

    @ApiModelProperty("类型。1:退款;2:退货;3:换货;4:退款不退货")
    private Integer type;

    @ApiModelProperty("店铺")
    private String shopName;

    @ApiModelProperty("入库状态。0:未入库;1:待入库;2:部分入库;3:全部入库;4:终止入库")
    private String stockinStatus;

    @ApiModelProperty("退换单号")
    private String refundNo;

    @ApiModelProperty("退货状态。10:已取消;20:待审核;30:已审核;40已推送:80:已结算;85:待过账;86:已过账;87:成本确认;90:已完成")
    private Integer status;

    @ApiModelProperty("订单编号（旺）")
    private String tradeNo;

    // ---

    @ApiModelProperty("商品SKU")
    private String specCode;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("商品编码")
    private String goodsNo;

    // ----

    @ApiModelProperty("客户昵称")
    private BuyerNameDO buyerNameDO;


    private Collection<String> tids;


}
