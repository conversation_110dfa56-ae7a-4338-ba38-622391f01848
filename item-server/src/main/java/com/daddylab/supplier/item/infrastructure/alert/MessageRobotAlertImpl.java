package com.daddylab.supplier.item.infrastructure.alert;

import com.daddylab.supplier.item.domain.alert.IAlerter;
import com.daddylab.supplier.item.domain.alert.IAlert;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

@Component("MessageRobotAlertAgentImpl")
@Slf4j
public class MessageRobotAlertImpl implements IAlert {
    private static final ConcurrentHashMap<Object, IAlerter> INSTANCES = new ConcurrentHashMap<>();

    @Override
    public boolean text(Object robotCode, String message) {
        try {
            return getTarget(robotCode).text(message);
        } catch (Exception e) {
            log.error("发送预警异常", e);
            return false;
        }
    }

    @Override
    public boolean markdown(Object robotCode, String message) {
        try {
            return getTarget(robotCode).markdown(message);
        } catch (Exception e) {
            log.error("发送预警异常", e);
            return false;
        }
    }

    private static IAlerter getInstance(MessageRobotCode robotCode) {
        return INSTANCES.computeIfAbsent(robotCode, key -> new MessageRobotAlerter(robotCode));
    }

    @Override
    public IAlerter getTarget(Object target) {
        return getInstance((MessageRobotCode) target);
    }
}
