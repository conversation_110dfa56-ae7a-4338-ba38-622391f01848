package com.daddylab.supplier.item.domain.drawer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Class  CheckReportVO
 *
 * @Date 2022/6/3上午11:57
 * <AUTHOR>
 */
@Data
@ApiModel(value = "CheckReportVO", description = "检测报告信息VO")
public class CheckReportVO implements Serializable {

    @ApiModelProperty("首检")
    private CheckDetailVO firstCheckInfo;

    @ApiModelProperty("最新抽检")
    private CheckDetailVO lastCheckInfo;

    @ApiModelProperty("最新抽检时间")
    private Long lastCheckTime;
}
