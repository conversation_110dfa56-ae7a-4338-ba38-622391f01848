package com.daddylab.supplier.item.domain.purchase.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName PurchaseIsActive.java
 * @description
 * @createTime 2021年11月19日 21:51:00
 */
@Getter
@AllArgsConstructor
public enum PurchaseIsActive implements IEnum<Integer> {
    NO(0, "否"),
    YES(1, "是");
    final public Integer value;
    final public String desc;
}
