package com.daddylab.supplier.item.controller.purchase.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2022/3/29 4:36 下午
 */
@Data
@ApiModel("采购订单查看详情返回封装")
public class PurchaseOrderViewVO implements Serializable {

    private static final long serialVersionUID = -1749629941481093305L;

    @ApiModelProperty(value = "采购订单id")
    private Long id;
    @ApiModelProperty(value = "采购订单编号")
    private String no;
    @ApiModelProperty(value = "采购订单状态。1:待提交、2:待审核、3:审核通过、4:审核拒绝、5:撤回审核、6:待完结、7:已完结、8:已关闭")
    private Integer state;
    @ApiModelProperty(value = "供应商id")
    private Long providerId;
    @ApiModelProperty(value = "采购组织")
    private Long organizationId;
    @ApiModelProperty(value = "采购部门")
    private Long departmentId;
    @ApiModelProperty(value = "采购组")
    private Long groupId;
    @ApiModelProperty(value = "采购类型。1：标准采购，2：工厂采购")
    private Integer type;
    @ApiModelProperty(value = "采购员id")
    private Long buyerUserId;
    @ApiModelProperty(value = "到货方式。1一次性到货。2分批到货")
    private Integer arrivalType;
    @ApiModelProperty(value = "采购日期")
    private Long purchaseDate;
    @ApiModelProperty(value = "付款方式。1:款到发货、2:预付10%、3:预付20%、4:预付30%、5:预付40%、6:预付50%、7:预付60%、8:货到付款、9:月结")
    private Integer payType;
    @ApiModelProperty(value = "创建人id")
    private Long createdUid;
    @ApiModelProperty(value = "创建时间")
    private Long createdAt;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "采购总数量")
    private Integer totalPurchaseQuantity;
    @ApiModelProperty(value = "采购总金额(税前)")
    private BigDecimal totalPurchaseTaxAmount;
    @ApiModelProperty(value = "采购总金额(税后)")
    private BigDecimal totalPurchaseAmount;
    @ApiModelProperty(value = "总税额")
    private BigDecimal totalTaxAmount;
    //    @ApiModelProperty(value = "采购订单明细")
//    List<PurchaseOrderDetailVO> detailList;
    @ApiModelProperty(value = "合同编号")
    private String contractNo;
    @ApiModelProperty(value = "合同id")
    private Long contractId;
    // --------- 非DB --------

    @ApiModelProperty(value = "供应商名称")
    private String providerName;
    @ApiModelProperty(value = "采购组织名称")
    private String organizationName;
    @ApiModelProperty(value = "采购部门名称")
    private String departmentName;
    @ApiModelProperty(value = "采购组名称")
    private String groupName;
    @ApiModelProperty(value = "采购员名称")
    private String buyerUserName;
    @ApiModelProperty(value = "创建人名称")
    private String createdName;

    @ApiModelProperty(value = "退料组织Id")
    private String stockOrganizationId;
    @ApiModelProperty(value = "退料组织名称")
    private String stockOrganizationName;

    @ApiModelProperty(value = "是否存在入库单")
    private Boolean relatedStockInOrder;
    @ApiModelProperty(value = "是否存在出库单")
    private Boolean relatedStockOutOrder;
    @ApiModelProperty(value = "是否存在关联入库应付单")
    private Boolean relatedInPayOrder;
    @ApiModelProperty(value = "是否存在关联出库应付单")
    private Boolean relatedOutPayOrder;
    @ApiModelProperty(value = "是否存在关联订单")
    private Boolean relatedOrder;

    private Integer businessLine;

    @ApiModelProperty("是否黑名单 0否 1是")
    private Integer isBlacklist;

    /**
     * 当这笔采购订单被冲销作废的时候
     * 新生成的对应的采购订单的 ID 和编码
     */
    private Long overrideOrderId;
    private String overrideOrderNo;

    private String workbenchProcessId;

    private String oaSealProcessId;


}
