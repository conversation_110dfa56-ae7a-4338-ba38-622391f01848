package com.daddylab.supplier.item.infrastructure.kingdee.req;

import cn.hutool.core.lang.Dict;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年11月17日 2:54 PM
 */
@Data
public class PaymentBillDetailDto {

    /**
     * 结算方式，默认电汇
     * "FSETTLETYPEID": {
     * "FNumber": "JSFS04_SYS"
     * },
     */
    @JsonProperty("FSETTLETYPEID")
    private Dict settlementType = Dict.create().set("FNumber", "JSFS04_SYS");

    /**
     * 付款用途，默认采购付款
     * "FPURPOSEID": {
     * "FNumber": "SFKYT08_SYS"
     * },
     */
    @JsonProperty("FPURPOSEID")
    private Dict PayUse = Dict.create().set("FNumber", "SFKYT08_SYS");

    /**
     * 应付款金额
     */
    @JsonProperty("FPAYTOTALAMOUNTFOR")
    private BigDecimal payAmount;

    /**
     * 联动 应付金额
     * （实际）实付金额
     */
    @JsonProperty("FPAYAMOUNTFOR_E")
    private BigDecimal realAmount;

    /**
     * 联动 应付金额
     * 折后金额
     */
    @JsonProperty("FSETTLEPAYAMOUNTFOR")
    private BigDecimal afterDiscountAmount;
}
