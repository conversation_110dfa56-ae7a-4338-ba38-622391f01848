package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtRefundOrderDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 旺店通退换单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Service
public class WdtRefundOrderDetailServiceImpl extends DaddyServiceImpl<WdtRefundOrderDetailMapper, WdtRefundOrderDetail> implements IWdtRefundOrderDetailService {

}
