package com.daddylab.supplier.item.infrastructure.domain;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Data;
import lombok.EqualsAndHashCode;

import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.Id;
import org.javers.core.metamodel.annotation.PropertyName;

import java.io.Serializable;

@com.alibaba.cola.domain.Entity
@org.javers.core.metamodel.annotation.Entity
@Data
@EqualsAndHashCode(
        exclude = {"createdAt", "createdUid", "updatedAt", "updatedUid", "isDel", "deletedAt"})
public class Entity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id */
    @PropertyName("ID")
    @Id
    @TableId(value = "id", type = IdType.AUTO)
    protected Long id;

    @PropertyName("ID")
    @Id
    public Long getId() {
        return id;
    }

    /** 创建时间createAt */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @DiffIgnore
    protected Long createdAt;

    /** 创建人updateUser */
    @TableField(value = "created_uid", fill = FieldFill.INSERT)
    @DiffIgnore
    protected Long createdUid;

    /** 更新时间updateAt */
    @TableField(value = "updated_at", fill = FieldFill.UPDATE)
    @DiffIgnore
    protected Long updatedAt;

    /** 更新人updateUser */
    @TableField(value = "updated_uid", fill = FieldFill.UPDATE)
    @DiffIgnore
    protected Long updatedUid;

    /** 删除时间 */
    @TableField(
            insertStrategy = FieldStrategy.NEVER,
            updateStrategy = FieldStrategy.NEVER,
            select = false)
    @DiffIgnore
    private Long deletedAt;

    /** 是否已删除 */
    @TableLogic @DiffIgnore protected Integer isDel;
}
