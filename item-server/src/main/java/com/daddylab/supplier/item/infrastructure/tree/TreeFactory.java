package com.daddylab.supplier.item.infrastructure.tree;

import cn.hutool.core.util.ObjectUtil;
import lombok.NonNull;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class TreeFactory {
    public static <TID, E extends Tree<TID, E>> List<E> buildTree(List<E> nodes, TID rootId) {
        final Map<TID, E> treeMap = toNodeMap(nodes);
        return buildTree(rootId, treeMap);
    }

    public static <TID, C, E extends Tree<TID, E>> List<E> buildTree(List<C> nodes, TID rootId, Function<C, E> mappingFunction) {
        final Map<TID, E> treeMap = toNodeMap(nodes, mappingFunction);
        return buildTree(rootId, treeMap);
    }

    @NonNull
    private static <TID, E extends Tree<TID, E>> List<E> buildTree(TID rootId, Map<TID, E> treeMap) {
        final E root = treeMap.get(rootId);
        Objects.requireNonNull(root, "Missing root node");
        final List<E> rootChildren = buildRootChildren(rootId, treeMap);
        root.setChildren(rootChildren);
        rootChildren.forEach(it -> it.setParent(root));
        return rootChildren;
    }

    public static <TID, E extends Tree<TID, E>> List<E> buildList(List<E> nodes, TID rootId) {
        final Map<TID, E> treeMap = toNodeMap(nodes);
        return buildRootChildren(rootId, treeMap);
    }

    public static <TID, C, E extends Tree<TID, E>> List<E> buildList(List<C> nodes, TID rootId, Function<C, E> mappingFunction) {
        final Map<TID, E> treeMap = toNodeMap(nodes, mappingFunction);
        return buildRootChildren(rootId, treeMap);
    }

    @NonNull
    private static <TID, E extends Tree<TID, E>> Map<TID, E> toNodeMap(List<E> nodes) {
        return nodes.stream().filter(Objects::nonNull).sorted(Tree::compareTo)
                .collect(Collectors.toMap(Tree::getId, Function.identity(), (a, b) -> b, LinkedHashMap::new));
    }

    @NonNull
    private static <TID, C, E extends Tree<TID, E>> Map<TID, E> toNodeMap(List<C> nodes, Function<C, E> mappingFunction) {
        return nodes.stream().map(mappingFunction).filter(Objects::nonNull).sorted(Tree::compareTo)
                .collect(Collectors.toMap(Tree::getId, Function.identity(), (a, b) -> b, LinkedHashMap::new));
    }

    @NonNull
    private static <TID, E extends Tree<TID, E>> List<E> buildRootChildren(TID rootId, Map<TID, E> treeMap) {
        List<E> rootTreeList = new ArrayList<>();
        for (E node : treeMap.values()) {
            if (null == node) {
                continue;
            }
            final TID parentId = node.getParentId();
            if (ObjectUtil.equals(rootId, parentId)) {
                rootTreeList.add(node);
                continue;
            }

            final E parentNode = treeMap.get(parentId);
            if (null != parentNode) {
                node.setParent(parentNode);
                if (parentNode.getChildren() == null)
                    parentNode.setChildren(new ArrayList<>());
                parentNode.addChild(node);
            }
        }
        return rootTreeList;
    }
}
