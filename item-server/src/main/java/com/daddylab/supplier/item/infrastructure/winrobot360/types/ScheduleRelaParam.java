package com.daddylab.supplier.item.infrastructure.winrobot360.types;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import java.util.List;
import lombok.Data;

@Data
@JsonInclude(Include.NON_NULL)
public class ScheduleRelaParam {

    /**
     * 对应参数应用uuid
     */
    private String robotUuid;

    /**
     *
     */
    private List<Param> params;
}