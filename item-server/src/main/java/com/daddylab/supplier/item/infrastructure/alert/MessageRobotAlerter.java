package com.daddylab.supplier.item.infrastructure.alert;

import com.alibaba.cola.exception.BizException;
import com.daddylab.supplier.item.domain.alert.ContentLengthFilter;
import com.daddylab.supplier.item.domain.alert.EnvFilter;
import com.daddylab.supplier.item.domain.alert.IAlerter;
import com.daddylab.supplier.item.domain.alert.RepeatFilter;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.messageRobot.exceptions.MessageRobotException;
import com.daddylab.supplier.item.domain.messageRobot.gateway.MessageRobotGateway;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class MessageRobotAlerter implements IAlerter {
    private final MessageRobotCode robotCode;
    private final List<Filter> filters = new ArrayList<>();
    private final MessageRobotGateway messageRobotGateway;


    public MessageRobotAlerter(MessageRobotCode robotCode) {
        this.robotCode = robotCode;
        this.messageRobotGateway = ApplicationContextUtil.getBean(MessageRobotGateway.class);
        addDefaultFilters();
    }

    public MessageRobotAlerter(MessageRobotCode robotCode, MessageRobotGateway messageRobotGateway, Filter... filters) {
        this.robotCode = robotCode;
        this.messageRobotGateway = messageRobotGateway;
        if (filters.length > 0)
            this.filters.addAll(Arrays.asList(filters));
    }

    private void addDefaultFilters() {
//        addFilter(ApplicationContextUtil.getBean(RepeatFilter.class));
        addFilter(ApplicationContextUtil.getBean(EnvFilter.class));
        addFilter(ApplicationContextUtil.getBean(ContentLengthFilter.class));
    }

    public void addFilter(Filter filter) {
        if (filter != null) filters.add(filter);
    }

    private List<Message> filter(MsgType msgType, String message) {
        List<Message> filtered = ImmutableList.of(
                new Message(msgType, message));
        for (Filter filter : filters) {
            if (CollectionUtils.isEmpty(filtered = filter.filter(this, filtered)))
                return filtered;
        }
        return filtered;
    }

    @Override
    public boolean text(String message) {
        return filterThenMessage(MsgType.TEXT, message);
    }

    @Override
    public boolean markdown(String message) {
        return filterThenMessage(MsgType.MARKDOWN, message);
    }

    private boolean filterThenMessage(MsgType msgType, String message) {
        final List<Message> filtered = filter(msgType, message);
        if (CollectionUtils.isEmpty(filtered)) return false;
        for (Message one : filtered)
            message0(one);
        return true;
    }


    private boolean message0(Message message) {
        try {
            switch (message.getMsgType()) {
                case TEXT:
                    return messageRobotGateway.sendText(robotCode, message.getContent());
                case MARKDOWN:
                    return messageRobotGateway.sendMarkdown(robotCode, message.getContent());
                default:
                    throw new BizException("消息类型未定义 " + message.getMsgType());
            }
        } catch (MessageRobotException e) {
            throw new BizException("消息发送异常 " + e.getMessage());
        }
    }

    @Override
    public String toString() {
        return "MessageRobotAlert:" + robotCode;
    }
}
