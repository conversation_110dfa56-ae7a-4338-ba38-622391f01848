package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ThirdPlatformSyncLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformSyncErrorLevel;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ThirdPlatformSyncLogMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IThirdPlatformSyncLogService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-13
 */
@Service
public class ThirdPlatformSyncLogServiceImpl extends DaddyServiceImpl<ThirdPlatformSyncLogMapper, ThirdPlatformSyncLog> implements
        IThirdPlatformSyncLogService {

//    @Override
//    public void asyncSave(ThirdPlatformSyncLog error) {
//        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
//            this.save(error);
//        });
//    }

    @Override
    public void asyncSaveDouDianLog(Long itemId, String error, PlatformSyncErrorLevel errorLevel) {
        ThirdPlatformSyncLog build = ThirdPlatformSyncLog.builder().itemId(itemId).errorLevel(errorLevel)
                .platformType(PlatformType.DOU_DIAN).error(error)
                .build();
        this.save(build);
    }

    @Override
    public void asyncSaveDouDianLog(Long syncId, Long itemId, String error, PlatformSyncErrorLevel errorLevel, String req, String res) {
        ThirdPlatformSyncLog build = ThirdPlatformSyncLog.builder()
                .syncId(syncId)
                .itemId(itemId).errorLevel(errorLevel)
                .platformType(PlatformType.DOU_DIAN).error(error)
                .req(req).resp(res)
                .build();
        this.save(build);
    }
}
