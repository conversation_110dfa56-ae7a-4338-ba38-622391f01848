package com.daddylab.supplier.item.domain.dataFetch;

import java.time.LocalDateTime;

/**
 * 自定义拉取器（由自己控制分页过程）
 *
 * <AUTHOR>
 * @since 2022/4/29
 */
public interface CustomFetcher extends Fetcher {

    /**
     * 拉取全量数据
     *
     * @param runContext 运行上下文
     */
    default void fetch(RunContext runContext) {
        throw new UnsupportedOperationException("当前数据拉取器（" + fetchDataType().name() + "）不支持全量初始化");
    }

    /**
     * 指定拉取某个时间段的数据
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param runContext 运行上下文
     */
    void fetch(LocalDateTime startTime, LocalDateTime endTime, RunContext runContext);


}
