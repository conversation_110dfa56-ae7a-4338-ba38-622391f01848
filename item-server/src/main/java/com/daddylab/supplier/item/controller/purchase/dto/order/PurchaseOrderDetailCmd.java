package com.daddylab.supplier.item.controller.purchase.dto.order;

import com.alibaba.cola.dto.Command;
import com.google.common.base.Objects;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR> up
 * @date 2022/3/28 10:37 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("保存采购订单详细请求参数")
public class PurchaseOrderDetailCmd extends Command {

    private static final long serialVersionUID = -2798860617433934262L;

    @ApiModelProperty(value = "序号")
    private Integer sortId;

    @ApiModelProperty(value = "采购订单明细id")
    private Long id;

    @ApiModelProperty(value = "商品skuCode")
    @NotNull(message = "商品skuCode不得为空")
    private String itemSkuCode;

    @ApiModelProperty(value = "商品id")
    @NotNull(message = "商品id不得为空")
    private Long itemId;

    @ApiModelProperty(value = "商品规格")
    @NotNull(message = "商品规格不得为空")
    private String specifications;

    @ApiModelProperty(value = "商品条码")
    private String barCode;

    @ApiModelProperty(value = "采购单位")
    @NotNull(message = "采购单位不得为空")
    private String unit;

    @ApiModelProperty(value = "采购数量")
    @NotNull(message = "采购数量不得为空")
    private Integer purchaseQuantity;

    @ApiModelProperty(value = "含税单价")
    @NotNull(message = "含税单价不得为空")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "税率(实际计算值，非百分比)")
    @NotNull(message = "税率不得为空")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxQuota;

    @ApiModelProperty(value = "价税合计")
    private BigDecimal totalPriceTax;

    @ApiModelProperty(value = "税后单价")
    private BigDecimal afterTaxPrice;

    @ApiModelProperty(value = "税后金额")
    private BigDecimal afterTaxAmount;

    @ApiModelProperty(value = "是否赠品。1是、0不是")
    private Integer isGift = 0;

    @ApiModelProperty(value = "预计入的仓库编号")
    @NotBlank(message = "仓库编号不得为空")
    private String warehouseNo;

    @ApiModelProperty(value = "备注")
    @Length(max = 100)
    private String remark;

    /**
     * 修正金额
     */
    private BigDecimal fixedTotalAmount;

    public int getHashCode() {
        return hashCode();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PurchaseOrderDetailCmd that = (PurchaseOrderDetailCmd) o;
        return Objects.equal(itemSkuCode, that.itemSkuCode) && Objects.equal(itemId, that.itemId) && Objects.equal(specifications, that.specifications) && Objects.equal(barCode, that.barCode) && Objects.equal(unit, that.unit) && Objects.equal(purchaseQuantity, that.purchaseQuantity) && Objects.equal(taxPrice, that.taxPrice) && Objects.equal(taxRate, that.taxRate) && Objects.equal(taxQuota, that.taxQuota) && Objects.equal(totalPriceTax, that.totalPriceTax) && Objects.equal(afterTaxPrice, that.afterTaxPrice) && Objects.equal(afterTaxAmount, that.afterTaxAmount) && Objects.equal(isGift, that.isGift) && Objects.equal(warehouseNo, that.warehouseNo) && Objects.equal(remark, that.remark);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(itemSkuCode, itemId, specifications, barCode, unit, purchaseQuantity, taxPrice, taxRate, taxQuota, totalPriceTax, afterTaxPrice, afterTaxAmount, isGift, warehouseNo, remark);
    }

    public void initializationForFixed() {
        this.taxPrice = this.fixedTotalAmount.divide(new BigDecimal(this.purchaseQuantity), 6, RoundingMode.HALF_UP);
        this.taxRate = this.taxRate.setScale(6, RoundingMode.DOWN);
        // 计算每条明细的价格
        calculatedAmountForFixed();
    }

    public void calculatedAmountForFixed() {
        // 价税合计 = 单价（税前）* 采购数量
        this.totalPriceTax = this.fixedTotalAmount;
        // 税后单价 = 单价（税前）/ (1 + 税率)
        this.afterTaxPrice = taxPrice.divide(new BigDecimal(1).add(taxRate), 6, RoundingMode.HALF_UP);
        // 税后金额 = 税后单价 * 采购数量
        this.afterTaxAmount = afterTaxPrice.multiply(new BigDecimal(purchaseQuantity)).setScale(6, RoundingMode.HALF_UP);
        // 税额 = 价税合计 - 税后金额
        this.taxQuota = totalPriceTax.subtract(afterTaxAmount).setScale(6, RoundingMode.HALF_UP);
    }

    public void initialization() {
        this.taxPrice = this.taxPrice.setScale(6, RoundingMode.DOWN);
        this.taxRate = this.taxRate.setScale(6, RoundingMode.DOWN);
        // 计算每条明细的价格
        calculatedAmount();
//        // 设置仓库编号
//        setWarehouseNoByType(purchaseType);
    }

    /**
     * 商品单价计算
     */
    private void calculatedAmount() {
        // 价税合计 = 单价（税前）* 采购数量
        this.totalPriceTax = taxPrice.multiply(new BigDecimal(purchaseQuantity)).setScale(6, RoundingMode.HALF_UP);
        // 税后单价 = 单价（税前）/ (1 + 税率)
        this.afterTaxPrice = taxPrice.divide(new BigDecimal(1).add(taxRate), 6, RoundingMode.HALF_UP);
        // 税后金额 = 税后单价 * 采购数量
        this.afterTaxAmount = afterTaxPrice.multiply(new BigDecimal(purchaseQuantity)).setScale(6, RoundingMode.HALF_UP);
        // 税额 = 价税合计 - 税后金额
        this.taxQuota = totalPriceTax.subtract(afterTaxAmount).setScale(6, RoundingMode.HALF_UP);
    }

    /**
     * 若业务手动创建的采购订单，优先级：1）后端商品上维护的仓库（旺店通回传后端商品，商品管理v1.5.0），2）默认预计入仓仓库为袋鼠仓（海销）和金诺仓，；
     * 若系统自动生成的采购单，按照优先级取数，1）最近商品采购单上设置的仓库；2）后端商品上维护的仓库（旺店通回传后端商品，商品管理v1.5.0）
     */
//    private void setWarehouseNoByType(Integer purchaseType) {
//        // 工厂采购流程。
//        if (2 == purchaseType) {
//            PurchaseOrderMapper purchaseOrderMapper = SpringUtil.getBean(PurchaseOrderMapper.class);
//            String warehouseNo = purchaseOrderMapper.getWarehouseNo(itemSkuCode);
//            if (StringUtils.isNotBlank(warehouseNo)) {
//                this.warehouseNo = warehouseNo;
//                return;
//            }
//            ItemSkuMapper skuMapper = SpringUtil.getBean(ItemSkuMapper.class);
//            this.warehouseNo = skuMapper.getWarehouseNo(this.getItemSkuCode());
//        }
//    }


}
