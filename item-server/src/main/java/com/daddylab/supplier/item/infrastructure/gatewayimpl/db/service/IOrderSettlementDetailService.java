package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderSettlementDetail;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 订单结算/订货明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
public interface IOrderSettlementDetailService extends IDaddyService<OrderSettlementDetail> {

}
