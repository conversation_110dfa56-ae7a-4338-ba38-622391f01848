package com.daddylab.supplier.item.application.item.tasks;

import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemCodeRef;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemCodeRefService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;

import lombok.AllArgsConstructor;

import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/12/21
 */
@Component
@AllArgsConstructor
public class ImportItemPropsGatewayImpl implements ImportItemPropsTask.ItemPropsGateway {
    private final IItemSkuService itemSkuService;
    private final IItemCodeRefService itemCodeRefService;
    private final OperateLogDomainService operateLogDomainService;

    @Override
    public Collection<Long> searchItem(Collection<String> generalizedCodes) {
        final List<ItemCodeRef> list =
                itemCodeRefService.lambdaQuery().in(ItemCodeRef::getCode, generalizedCodes).list();
        return list.stream().map(ItemCodeRef::getItemId).collect(Collectors.toSet());
    }

    @Override
    public boolean updateProps(Collection<Long> itemIds, Map<String, Object> props) {
        final List<ItemSku> itemSkus =
                itemSkuService.lambdaQuery().in(ItemSku::getItemId, itemIds).list();
        final Map<Long, List<ItemSku>> skusGroupByItemId =
                itemSkus.stream().collect(Collectors.groupingBy(ItemSku::getItemId));
        for (Map.Entry<Long, List<ItemSku>> itemSkuGroup : skusGroupByItemId.entrySet()) {
            final Long itemId = itemSkuGroup.getKey();
            final List<ItemSku> skus = itemSkuGroup.getValue();
            for (ItemSku itemSku : skus) {
                final LinkedHashMap<String, Object> currentProps = itemSku.getProps();
                currentProps.putAll(props);
                itemSku.setProps(currentProps);
                itemSkuService.updateById(itemSku);
            }
            operateLogDomainService.addOperatorLog(
                    0L, OperateLogTarget.ITEM, itemId, "【供应链商品参数数据初始化】导入商品属性", props);
        }
        return true;
    }
}
