package com.daddylab.supplier.item.application.saleItem.service;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.saleItem.dto.SaleItemLibraryCmd;
import com.daddylab.supplier.item.application.saleItem.query.SaleItemLibraryQueryPage;
import com.daddylab.supplier.item.application.saleItem.vo.SaleItemLibraryVO;
import com.daddylab.supplier.item.domain.platformItem.service.event.SyncSaleItemLibraryEvent;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseOperate;

import java.io.InputStream;

/**
 * 销售商品库
 */
public interface SaleItemLibraryBizService {

    /**
     * 分页查询销售商品库的商品
     * @param queryPage 查询参数
     * @return
     */
    PageResponse<SaleItemLibraryVO> queryPage(SaleItemLibraryQueryPage queryPage);

    /**
     * 根据skuCode 查询信息
     * @param skuCode sku编码
     * @return
     */
    SingleResponse<SaleItemLibraryVO> getBySkuCode(String skuCode);

    /**
     * 新增/修改新品商品库信息
     * @param cmd
     * @return
     */
    Response createOrUpdate(SaleItemLibraryCmd cmd);

    /**
     * 删除
     * @param id
     * @return
     */
    Response delete(Long id);


    /**
     * 获得模板
     * @return
     */
    String getExcelTemplateUrl();


    /**
     * 导入
     *
     * @param inputStream
     * @param confirm     是否确认覆盖已有数据
     */
    SingleResponse<PurchaseOperate> importExcel(InputStream inputStream, Boolean confirm);


    /**
     * 导出
     * @param queryPage
     */
    void exportExcel(SaleItemLibraryQueryPage queryPage);



    /**
     * 同步更新商品名称
     */
    void synchronizeItemName(SyncSaleItemLibraryEvent event);
}
