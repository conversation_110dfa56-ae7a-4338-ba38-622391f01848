package com.daddylab.supplier.item.application.itemSync;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.BizException;
import com.daddylab.supplier.item.application.itemSync.types.ItemSyncTbParam;
import com.daddylab.supplier.item.application.saleItem.vo.ThirdSyncVO;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.controller.item.dto.ThirdSyncPageQuery;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.itemSync.TaobaoGoodsDetailLink;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ThirdPlatformSync;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ThirdPlatformSyncState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ThirdPlatformSyncType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IThirdPlatformSyncService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/9/20
 */
@Service
@Slf4j
public class ThirdPlatformSyncBizServiceImpl implements ThirdPlatformSyncBizService {

    private final IThirdPlatformSyncService thirdPlatformSyncService;
    private final UserGateway userGateway;
    private final ItemGateway itemGateway;

    public ThirdPlatformSyncBizServiceImpl(IThirdPlatformSyncService thirdPlatformSyncService,
            @Qualifier("UserGatewayCacheImpl")
            UserGateway userGateway, ItemGateway itemGateway) {
        this.thirdPlatformSyncService = thirdPlatformSyncService;
        this.userGateway = userGateway;
        this.itemGateway = itemGateway;
    }

    @Override
    public Response syncTb(List<ItemSyncTbParam> params) {
        final ArrayList<ThirdPlatformSync> thirdPlatformSyncs = new ArrayList<>();
        for (ItemSyncTbParam param : params) {
            final ThirdPlatformSync thirdPlatformSync = new ThirdPlatformSync();
            final Long itemId = param.getItemId();
            final String link = param.getLink();
            thirdPlatformSync.setPlatformType(PlatformType.TAO_BAO);
            thirdPlatformSync.setItemId(itemId);
            thirdPlatformSync.setItemCode(param.getItemCode());
            thirdPlatformSync.setName(param.getTbTitle());
            thirdPlatformSync.setState(ThirdPlatformSyncState.WAITING);
            thirdPlatformSync.setType(ThirdPlatformSyncType.TAOBAO_WINROBOT);
            thirdPlatformSync.setThirdLink(link);
            thirdPlatformSync.setSkuCount(param.getSkuNum());
            thirdPlatformSync.setPlatformItemCount(0);
            thirdPlatformSync.setError("");
            thirdPlatformSync.setEnv(ApplicationContextUtil.getActiveProfile());
            thirdPlatformSyncs.add(thirdPlatformSync);
            try {
                if (!TaobaoGoodsDetailLink.checkUrl(link)) {
                    throw new BizException(ErrorCode.VERIFY_PARAM.getCode(), "URL格式错误");
                }

                final Item item = itemGateway.getItem(itemId);

                //一键同步到各渠道，因各端都需要最基础的商品物料才可以进行同步，要校验运营商品库的商品状态，
                //「待修改」「待上架」「已上架」才可以同步，其他状态的商品，同步任务创建后直接失败，失败原因：商品状态不符合同步条件
                if (!Arrays.asList(
                        ItemLaunchStatus.TO_BE_UPDATED.getValue(),
                        ItemLaunchStatus.TO_BE_RELEASED.getValue(),
                        ItemLaunchStatus.HAS_BE_RELEASED.getValue()
                ).contains(item.getLaunchStatus())) {
                    throw new BizException(ErrorCode.VERIFY_PARAM.getCode(), "商品状态不符合同步条件。状态必须为【待修改】或【待上架】或【已上架】");
                }
            } catch (BizException e) {
                thirdPlatformSync.setState(ThirdPlatformSyncState.ERROR);
                thirdPlatformSync.setError(e.getMessage());
            }
        }
        thirdPlatformSyncService.saveBatch(thirdPlatformSyncs);
        return Response.buildSuccess();
    }

    @Override
    public PageResponse<ThirdSyncVO> syncList(ThirdSyncPageQuery pageQuery) {
        PageInfo<ThirdPlatformSync> pageInfo = PageHelper.startPage(
                        pageQuery.getPageIndex(), pageQuery.getPageSize())
                .doSelectPageInfo(() ->
                        thirdPlatformSyncService.lambdaQuery()
                                .eq(Objects.nonNull(pageQuery.getPlatformType()),
                                        ThirdPlatformSync::getPlatformType,
                                        pageQuery.getPlatformType())
                                .orderByDesc(ThirdPlatformSync::getId).list()
                );
        List<ThirdSyncVO> collect = pageInfo.getList().stream().map(val -> {
            ThirdSyncVO one = new ThirdSyncVO();
            one.setBdId(val.getId());
            one.setPlatformType(val.getPlatformType().getDesc());
            one.setItemId(val.getItemId());
            one.setItemCode(val.getItemCode());
            one.setName(val.getName());
            one.setCreatedAt(val.getCreatedAt());
            StaffInfo staffInfo = userGateway.queryStaffInfoById(val.getCreatedUid());
            String name = Objects.isNull(staffInfo) ? "" : staffInfo.getNickname();
            one.setCreatedName(name);
            one.setState(val.getState().getValue());
            one.setError(val.getError());
            one.setSyncParam(val.getSyncParam());
            return one;
        }).collect(Collectors.toList());

        return PageResponse.of(collect, (int) pageInfo.getTotal(),
                pageInfo.getPageSize(), pageInfo.getPageNum());
    }
}
