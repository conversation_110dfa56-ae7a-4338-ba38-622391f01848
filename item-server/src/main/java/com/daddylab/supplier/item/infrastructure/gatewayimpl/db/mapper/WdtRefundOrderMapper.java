package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.order.settlement.dto.RefundManageExportSheet;
import com.daddylab.supplier.item.application.order.settlement.dto.RefundMangerExportCmd;
import com.daddylab.supplier.item.application.order.settlement.sys.SkuRefundDo;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.excel.PurchaseBillRow;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrderDetail;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderBaseInfo;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderDetail;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderItem;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderQuery;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 旺店通退换单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Repository
public interface WdtRefundOrderMapper extends DaddyBaseMapper<WdtRefundOrder> {

    int queryRefundOrderCountWithoutDetailsCond(RefundOrderQuery query);

    int queryRefundOrderCountWithDetailsCond(RefundOrderQuery query);

    List<Long> queryRefundOrderIdsWithDetailsCond(RefundOrderQuery query);

    List<Long> queryRefundOrderIdsWithoutDetailsCond(RefundOrderQuery query);

    List<RefundOrderBaseInfo> queryRefundOrderByIds(List<Long> ids);

    RefundOrderDetail queryRefundOrderDetail(@Param("refundOrderNo") String refundOrderNo);

    List<RefundOrderItem> querySwapItems(@Param("refundOrderNo") String refundOrderNo);

    /**
     * 批量插入或者已存在重复记录时更新数据
     *
     * @param orders 数据集合
     */
    void saveOrUpdateBatch(@Param("orders") Collection<WdtRefundOrder> orders);


    /**
     * 当月退换货处理。plus2
     * 退款审核时间在当前月，下单时间也在当前月
     *
     * @param warehouseNo
     * @param startTime
     * @param endTime
     * @return
     */
    List<SkuRefundDo> getSkuRefundNumCurrentMonth2(@Param("warehouseNo") String warehouseNo,
                                                   @Param("startTime") String startTime,
                                                   @Param("endTime") String endTime);


    List<SkuRefundDo> getSkuRefundNumCrossMonth2(@Param("warehouseNo") String warehouseNo,
                                                 @Param("startTime") String startTime,
                                                 @Param("endTime") String endTime);

    /**
     * 根据原始单号查询旺店通退换单明细
     *
     * @param srcOrderNos 原始单号
     * @param type        类型 1:退款;2:退货;3:换货;4:退款不退货
     * @return
     */
    List<WdtRefundOrderDetail> queryCheckedRefundOrderDetailsBatchBySrcOrderNo(
            @Param("srcOrderNos") Collection<String> srcOrderNos,
            @Param("type") Integer type);

    /**
     * 退货明细 导出数据查询
     *
     * @param cmd
     * @param warehouseNos
     * @return
     */
    @Deprecated
    List<PurchaseBillRow> refundManageList(@Param("cmd") RefundMangerExportCmd cmd,
                                           @Param("warehouseNos") Collection<String> warehouseNos);

    /**
     * 结算明细导出-退货明细sheet导出SQL
     *
     * @param start
     * @param end
     * @param skuCodes
     * @return
     */
    List<PurchaseBillRow> refundManageList2(@Param("start") String start, @Param("end") String end,
                                            @Param("skuCodes") Collection<String> skuCodes);

    List<RefundManageExportSheet> refundManageList3(@Param("start") String start, @Param("end") String end,
                                                    @Param("warehouseNos") Collection<String> warehouseNos,
                                                    @Param("skuCodes") Collection<String> skuCodes);


    List<WdtRefundOrder> listByTid(String tid);
}
