package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockOutOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockOutOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockOutOrderDetailService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 退料出库明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Service
public class StockOutOrderDetailServiceImpl extends DaddyServiceImpl<StockOutOrderDetailMapper, StockOutOrderDetail> implements IStockOutOrderDetailService {

    @Override
    public List<StockOutOrderDetail> getListByOrderId(Long stockOutOrderId) {
        LambdaQueryWrapper<StockOutOrderDetail> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StockOutOrderDetail::getStockOutOrderId, stockOutOrderId);
        return this.list(wrapper);
    }

    @Override
    public List<StockOutOrderDetail> getDetailByStockIdAndSku(Long stockOutOrderId, String itemSkuCode) {
        LambdaQueryWrapper<StockOutOrderDetail> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StockOutOrderDetail::getStockOutOrderId, stockOutOrderId);
        wrapper.eq(StockOutOrderDetail::getItemSkuCode, itemSkuCode);
        return this.list(wrapper);
    }


}
