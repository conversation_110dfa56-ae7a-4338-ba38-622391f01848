package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购价格表返回封装
 *
 * <AUTHOR> up
 * @date 2022/4/13 3:17 下午
 */
@Data
public class PurchasePriceDO {

    private Integer activeType;

    /**
     * 活动优惠价
     * 1.可以根据下单时间取活动优惠价
     * 2.可以根据活动优惠数量取优惠价。
     */
    private BigDecimal priceCost;

    private String skuCode;

    private Integer platformType;

    /**
     * 当优惠数量的取值大于0时。
     * 表示存在根据优惠数量取价的逻辑。
     * 这个取价逻辑优先级最高
     */
    private BigDecimal preferentialQuantity;

    private Long payStartTime;
    private Long payEndTime;

    private String month;
}
