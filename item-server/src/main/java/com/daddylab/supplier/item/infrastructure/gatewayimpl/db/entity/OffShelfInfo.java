package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OffShelfUrgentLevel;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 下架管理-下架流程信息
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OffShelfInfo implements Serializable {

  private static final long serialVersionUID = 1L;

  /** id */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /** 创建时间createAt */
  @TableField(fill = FieldFill.INSERT)
  private Long createdAt;

  /** 创建人updateUser */
  @TableField(fill = FieldFill.INSERT)
  private Long createdUid;

  /** 更新时间updateAt */
  @TableField(fill = FieldFill.INSERT_UPDATE)
  private Long updatedAt;

  /** 更新人updateUser */
  @TableField(fill = FieldFill.INSERT_UPDATE)
  private Long updatedUid;

  /** 是否已删除 */
  @TableLogic(value = "0", delval = "id")
  private Long isDel;

  /** 删除时间 */
  @TableField(fill = FieldFill.DEFAULT)
  private Long deletedAt;

  /** 紧急程度.1十分紧急，2紧急，3一般 */
  private OffShelfUrgentLevel urgentLevel;

  /** 下架理由（多选，英文逗号分割）。0店铺销量，1客诉问题，2检测不合格，3舆情问题，4商务合作问题，5其他 */
  private String reasonType;

  /** 下架理由 */
  private String reasonTxt;

  /** 下架理由（文件） */
  private String reasonFile;

  /** 下架流程编码 */
  private String no;

  /** 下架流程状态。0待提交，1待审核，11已撤回，2待处理，3已完成，31已拒绝 */
  private OffShelfStatus status;

  /** 下架店铺ID，英文逗号分割 */
  private String shopId;

  /**
   * 是否全选。true表示全选，false表示非全选
   */
  private Boolean selectAll;

  /** 运营人员ID，英文逗号分割 */
  private String operatorUid;

  /** 审核备注 */
  private String approveRemark;

  /** 提交时间 */
  private Long submitTime;

  /** 提交人ID */
  private Long submitUid;

  /** 流程实例ID */
  private String processInstId;

  /** 审核时间 */
  private Long approveTime;

  /** 审核人ID */
  private Long approveUid;
}
