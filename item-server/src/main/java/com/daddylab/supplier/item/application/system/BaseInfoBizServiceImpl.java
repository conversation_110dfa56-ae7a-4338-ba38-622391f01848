package com.daddylab.supplier.item.application.system;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.application.afterSales.AfterSalesWarehouseBizService;
import com.daddylab.supplier.item.application.afterSales.WarehouseAfterSalesAddressVO;
import com.daddylab.supplier.item.application.itemOptimize.ItemOptimizePlanBizService;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.excel.ReadExcelController;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.DetailModelType;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.common.dto.*;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerPersonCmd;
import com.daddylab.supplier.item.domain.exportTask.ExportManager;
import com.daddylab.supplier.item.domain.psys.PsysUserGateway;
import com.daddylab.supplier.item.domain.user.dto.StaffDropDownItem;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.config.InvoiceTitleConfig;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ExternalUserMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerPersonResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffListQuery;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.baseinfo.ResponsiblePersonQuery;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizePlanBaseInfo;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizePlanDropDown;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizePlanQuery;
import com.daddylab.supplier.item.types.reason.enums.ReasonType;
import com.daddylab.supplier.item.types.warehouse.WarehouseSheet;
import com.daddylab.supplier.item.types.warehouse.WarehouseVO;
import com.daddylab.supplier.item.types.warehouse.WarehouseViewVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> up
 * @date 2022/3/24 6:25 下午
 */
@Service
@Slf4j
public class BaseInfoBizServiceImpl implements BaseInfoService {

  @Autowired IBaseUnitService iBaseUnitService;

  @Autowired IWarehouseService iStockService;

  @Autowired IOrganizationService iOrganizationService;

  @Autowired IDepartmentService iDepartmentService;

  @Autowired IGroupService iGroupService;

  @Autowired ITaxRateService iTaxRateService;
  @Autowired private IReasonService reasonService;

  @Autowired private AfterSalesWarehouseBizService afterSalesWarehouseBizService;

  @Resource private IAdditionalService iAdditionalService;

  @Resource IWarehouseService iWarehouseService;

  @Resource
  @Qualifier("UserGatewayCacheImpl")
  UserGateway userGateway;

  @Resource ExportManager exportManager;

  @Resource IExternalUserService iExternalUserService;

  @Resource ExternalUserMapper externalUserMapper;

  @Resource StaffService staffService;

  @Resource ReadExcelController readExcelController;

  @Resource MsgSender msgSender;

  @Resource InvoiceTitleConfig invoiceTitleConfig;

  private static List<String> PRICE_EXCEL_KEY = new LinkedList<>();

  static {
    PRICE_EXCEL_KEY.add("活动spu多件供价");
    PRICE_EXCEL_KEY.add("日常spu多件供价");
    PRICE_EXCEL_KEY.add("活动sku多件供价");
    PRICE_EXCEL_KEY.add("日常sku多件供价");
  }

  @Override
  public MultiResponse<BaseUnit> pageUnit(UnitPageQuery query) {

    LambdaQueryWrapper<BaseUnit> queryWrapper = Wrappers.lambdaQuery();
    queryWrapper
        .like(StringUtils.hasText(query.getName()), BaseUnit::getName, query.getName())
        .eq(
            (Objects.nonNull(query.getState()) && query.getState() > 0),
            BaseUnit::getState,
            query.getState())
        .eq(Objects.nonNull(query.getId()), BaseUnit::getId, query.getId());
    return MultiResponse.of(iBaseUnitService.list(queryWrapper));
  }

  @Override
  public PageResponse<WarehouseVO> pageWarehouseVo(WarehousePageQuery query) {
    query.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(query.getBusinessLine()));
    if (CollUtil.isEmpty(query.getBusinessLine())) {
      return ResponseFactory.emptyPage();
    }

    Page<Warehouse> page = iWarehouseService.pageQuery(query);
    final List<Warehouse> records = page.getRecords();
    if (records.isEmpty()) {
      return ResponseFactory.emptyPage();
    }

    return PageResponse.of(
        records.stream()
            .map(
                val -> {
                  WarehouseVO warehouseVO = WarehouseTypeMapper.INST.warehousePoToVo(val);
                  warehouseVO.setBusinessLine(val.getBusinessLineList());
                  String orderPersonnel = val.getOrderPersonnel();
                  String afterSaleStaffStr = val.getAfterSaleStaffStr();
                  if (StringUtils.hasText(orderPersonnel)) {
                    List<Long> userIds =
                        Arrays.stream(orderPersonnel.split(","))
                            .map(Long::valueOf)
                            .collect(Collectors.toList());
                    Map<Long, StaffInfo> longStaffInfoMap =
                        userGateway.batchQueryStaffInfoByIds(userIds);
                    List<StaffInfo> orderPersonneList =
                        longStaffInfoMap.values().stream().distinct().collect(Collectors.toList());
                    warehouseVO.setOrderPersoneList(orderPersonneList);
                  }
                  if (StringUtils.hasText(afterSaleStaffStr)) {
                    List<Long> userIds =
                        Arrays.stream(afterSaleStaffStr.split(","))
                            .map(Long::valueOf)
                            .collect(Collectors.toList());
                    Map<Long, StaffInfo> longStaffInfoMap =
                        userGateway.batchQueryStaffInfoByIds(userIds);
                    List<StaffInfo> afterSaleStaffList =
                        longStaffInfoMap.values().stream().distinct().collect(Collectors.toList());
                    warehouseVO.setAfterSaleStaffList(afterSaleStaffList);
                  }
                  return warehouseVO;
                })
            .collect(Collectors.toList()),
        (int) page.getTotal(),
        (int) page.getSize(),
        (int) page.getCurrent());
  }

  @Override
  public SingleResponse<WarehouseViewVO> viewWarehouse(Long warehouseId, String warehouseName) {
    Warehouse warehouse = null;
    if (Objects.nonNull(warehouseId)) {
      warehouse = iWarehouseService.getById(warehouseId);
    }
    if (org.apache.commons.lang3.StringUtils.isNotBlank(warehouseName)) {
      List<Warehouse> list =
          iWarehouseService
              .lambdaQuery()
              .eq(Warehouse::getName, warehouseName.trim())
              .last("limit 1")
              .list();
      if (CollectionUtils.isNotEmpty(list)) {
        warehouse = list.get(0);
      }
    }
    Assert.state(Objects.nonNull(warehouse), "仓库查询不得为空");
    WarehouseVO warehouseVO = WarehouseTypeMapper.INST.warehousePoToVo(warehouse);
    WarehouseViewVO viewVO = new WarehouseViewVO();
    BeanUtils.copyProperties(warehouseVO, viewVO);
    List<WarehouseAfterSalesAddressVO> warehouseAfterSalesAddressVos =
        afterSalesWarehouseBizService.getWarehouseAfterSalesAddressVO(
            ListUtil.of(warehouse.getNo()));
    viewVO.setAfterSalesAddressInfoList(warehouseAfterSalesAddressVos);
    viewVO.setBusinessLine(warehouse.getBusinessLineList());
    String orderPersonnel = warehouse.getOrderPersonnel();
    if (StrUtil.isNotBlank(orderPersonnel)) {
      Map<Long, StaffInfo> staffList =
          userGateway.batchQueryStaffInfoByIds(
              Arrays.stream(orderPersonnel.split(","))
                  .map(Long::valueOf)
                  .collect(Collectors.toList()));
      List<StaffInfo> list = new LinkedList<>(staffList.values());
      viewVO.setOrderPersoneList(list);
    }
    String afterSaleStaffStr = warehouse.getAfterSaleStaffStr();
    if (StringUtils.hasText(afterSaleStaffStr)) {
      List<Long> userIds =
          Arrays.stream(afterSaleStaffStr.split(","))
              .map(Long::valueOf)
              .collect(Collectors.toList());
      Map<Long, StaffInfo> longStaffInfoMap = userGateway.batchQueryStaffInfoByIds(userIds);
      List<StaffInfo> afterSaleStaffList =
          longStaffInfoMap.values().stream().distinct().collect(Collectors.toList());
      viewVO.setAfterSaleStaffList(afterSaleStaffList);
    }
    return SingleResponse.of(viewVO);
  }

  @Override
  public SingleResponse<Boolean> exportWarehouse(WarehousePageQuery pageQuery) {

    exportManager.export(
        ExportTaskType.WAREHOUSE,
        WarehouseSheet.class,
        pageIndex -> {
          pageQuery.setPageIndex(pageIndex);
          return exportWarehouse0(pageQuery);
        });
    return SingleResponse.of(true);
  }

  public PageResponse<WarehouseSheet> exportWarehouse0(WarehousePageQuery pageQuery) {
    pageQuery.setPageSize(100);
    Page<Warehouse> page = iWarehouseService.pageQuery(pageQuery);
    List<Warehouse> records = page.getRecords();
    if (CollectionUtils.isEmpty(records)) {
      ResponseFactory.emptyPage();
    }

    Set<String> userIdSet = new HashSet<>();
    records.forEach(
        warehouse -> {
          String orderPersonnel = warehouse.getOrderPersonnel();
          String afterSaleStaffStr = warehouse.getAfterSaleStaffStr();
          if (StrUtil.isNotBlank(orderPersonnel)) {
            userIdSet.addAll(StringUtil.split(orderPersonnel, ","));
          }
          if (StrUtil.isNotBlank(afterSaleStaffStr)) {
            userIdSet.addAll(StringUtil.split(afterSaleStaffStr, ","));
          }
        });
    List<Long> thisPageUserIds = userIdSet.stream().map(Long::valueOf).collect(Collectors.toList());
    Map<Long, StaffInfo> userMap = userGateway.batchQueryStaffInfoByIds(thisPageUserIds);

    List<String> thisPageWarehouseNos =
        records.stream().map(Warehouse::getNo).collect(Collectors.toList());
    Map<String, List<String>> warehouseAfterSaleInfoMap =
        afterSalesWarehouseBizService.queryWarehouseAfterSaleInfo(thisPageWarehouseNos);

    List<WarehouseSheet> thisPageSheet = new LinkedList<>();
    for (Warehouse warehouse : records) {
      List<String> afterSaleInfoList = warehouseAfterSaleInfoMap.get(warehouse.getNo());
      if (CollUtil.isNotEmpty(afterSaleInfoList)) {
        thisPageSheet.addAll(
            afterSaleInfoList.stream()
                .map(
                    afterSaleInfo -> {
                      WarehouseSheet warehouseSheet = new WarehouseSheet();
                      warehouseSheet.setWarehouseNo(warehouse.getNo());
                      warehouseSheet.setWarehouseName(warehouse.getName());
                      warehouseSheet.setWmsType(vwmTypeStr(warehouse.getWmsType()));
                      warehouseSheet.setConcat(warehouse.getContacts());
                      warehouseSheet.setTel(warehouse.getTel());
                      warehouseSheet.setAddress(warehouse.getFullAddress());
                      warehouseSheet.setBusinessLine(warehouse.getBusinessLineStr());
                      warehouseSheet.setOrderPersonnel(
                          getNickName(warehouse.getOrderPersonnel(), userMap));
                      warehouseSheet.setAfterSaleStaff(
                          getNickName(warehouse.getAfterSaleStaffStr(), userMap));
                      warehouseSheet.setAfterSaleInfo(afterSaleInfo);
                      return warehouseSheet;
                    })
                .collect(Collectors.toList()));
      } else {
        WarehouseSheet warehouseSheet = new WarehouseSheet();
        warehouseSheet.setWarehouseNo(warehouse.getNo());
        warehouseSheet.setWarehouseName(warehouse.getName());
        warehouseSheet.setWmsType(vwmTypeStr(warehouse.getWmsType()));
        warehouseSheet.setConcat(warehouse.getContacts());
        warehouseSheet.setTel(warehouse.getTel());
        warehouseSheet.setAddress(warehouse.getFullAddress());
        warehouseSheet.setBusinessLine(warehouse.getBusinessLineStr());
        warehouseSheet.setOrderPersonnel(getNickName(warehouse.getOrderPersonnel(), userMap));
        warehouseSheet.setAfterSaleStaff(getNickName(warehouse.getAfterSaleStaffStr(), userMap));
        warehouseSheet.setAfterSaleInfo("");
        thisPageSheet.add(warehouseSheet);
      }
    }
    return PageResponse.of(
        thisPageSheet, (int) page.getTotal(), (int) page.getSize(), (int) page.getPages());
  }

  private String getNickName(String str, Map<Long, StaffInfo> userMap) {
    if (org.apache.commons.lang3.StringUtils.isBlank(str)) {
      return org.apache.commons.lang3.StringUtils.EMPTY;
    }
    return Arrays.stream(str.split(","))
        .map(
            id -> {
              Long userId = Long.valueOf(id);
              StaffInfo staffInfo = userMap.get(userId);
              if (Objects.nonNull(staffInfo)) {
                return staffInfo.getNickname();
              } else {
                return StringUtil.EMPTY;
              }
            })
        .collect(Collectors.joining(","));
  }

  /**
   * 1.内部、2.自流转、3.平台、4.京东沧海、5.抖音云仓、6分销委外
   *
   * @param wmsType
   * @return
   */
  private String vwmTypeStr(Integer wmsType) {
    if (Objects.isNull(wmsType)) {
      return "EMPTY";
    }
    switch (wmsType) {
      case 1:
        return "内部";
      case 2:
        return "自流转";
      case 3:
        return "平台";
      case 4:
        return "京东沧海";
      case 5:
        return "抖音云仓";
      case 6:
        return "分销委外";
      default:
        return "UNKNOWN";
    }
  }

  @Override
  public PageResponse<Organization> pageOrganization(OrganizationPageQuery query) {
    LambdaQueryWrapper<Organization> queryWrapper = Wrappers.lambdaQuery();
    queryWrapper
        .eq(Objects.nonNull(query.getId()), Organization::getId, query.getId())
        .like(StringUtils.hasText(query.getName()), Organization::getName, query.getName())
        .eq(
            (Objects.nonNull(query.getState()) && query.getState() > 0),
            Organization::getState,
            query.getState())
        .eq((Objects.nonNull(query.getType())), Organization::getType, query.getType());
    Page<Organization> page = new Page<>(query.getPageIndex(), query.getPageSize());
    page = iOrganizationService.page(page, queryWrapper);
    return PageResponse.of(
        page.getRecords(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
  }

  @Override
  public PageResponse<ErpGroup> pageGroup(GroupPageQuery query) {
    LambdaQueryWrapper<ErpGroup> queryWrapper = Wrappers.lambdaQuery();
    queryWrapper
        .like(StringUtils.hasText(query.getName()), ErpGroup::getName, query.getName())
        .eq(
            (Objects.nonNull(query.getState()) && query.getState() > 0),
            ErpGroup::getState,
            query.getState())
        .orderByDesc(ErpGroup::getId);
    Page<ErpGroup> page = new Page<>(query.getPageIndex(), query.getPageSize());
    page = iGroupService.page(page, queryWrapper);
    return PageResponse.of(
        page.getRecords(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
  }

  @Override
  public MultiResponse<TaxRate> listTaxRate() {
    LambdaQueryWrapper<TaxRate> objectLambdaQueryWrapper = Wrappers.lambdaQuery();
    objectLambdaQueryWrapper.eq(TaxRate::getState, 0);
    return MultiResponse.of(iTaxRateService.list(objectLambdaQueryWrapper));
  }

  @Override
  public MultiResponse<Reason> reasonList(Integer reasonType) {
    final ReasonType reasonTypeEnum = IEnum.getEnumByValue(ReasonType.class, reasonType);
    if (reasonTypeEnum == null) {
      return MultiResponse.of(Collections.emptyList());
    }

    LambdaQueryWrapper<Reason> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(Reason::getType, reasonTypeEnum.getValue());
    List<Reason> reasons = reasonService.list(queryWrapper);
    return MultiResponse.of(reasons);
  }

  @Override
  public Boolean isOpenDetailModel(Integer type) {
    String s = RedisUtil.get(UserContext.getUserId() + "_" + type + "_show_detail");
    if (Objects.isNull(s)) {
      return false;
    } else {
      return "1".equals(s);
    }
  }

  @Override
  public void setOpenDetailModel(DetailModelType type, Boolean state) {
    String key = UserContext.getUserId() + "_" + type.getVal() + "_show_detail";
    RedisUtil.set(key, state ? 1 : 0);
  }

  @Override
  public BaseUnit ById(Long id) {
    return iBaseUnitService.getById(id);
  }

  public static void main(String[] args) {
    String name = "24年5月活动spu多件供价.xlsx";
    long count = PRICE_EXCEL_KEY.stream().filter(name::contains).count();
    System.out.println(count);
  }

  @Override
  public SingleResponse<Long> uploadAdditional(AdditionalCmd cmd) {
    String name = cmd.getName();
    long count = PRICE_EXCEL_KEY.stream().filter(name::contains).count();
    boolean priceExcel = count > 0;
    AtomicReference<String> qwUserId = new AtomicReference<>("");
    staffService
        .getStaffBrief(UserContext.getUserId())
        .ifPresent(val -> qwUserId.set(val.getQwUserId()));
    if (priceExcel) {
      ThreadUtil.execute(
          PoolEnum.COMMON_POOL,
          () -> {
            com.alibaba.cola.dto.Response response = priceExcelHandler(cmd);
            if (StrUtil.isNotBlank(qwUserId.get())) {
              if (response.isSuccess()) {
                msgSender.sendMsgToSomeone(cmd.getName() + "处理完成", ListUtil.of(qwUserId.get()));
              } else {
                msgSender.sendMsgToSomeone(
                    cmd.getName() + "处理失败。" + response.getErrMessage(),
                    ListUtil.of(qwUserId.get()));
              }
            }
          });
      return SingleResponse.of(1L);
    }

    Additional additional = new Additional();
    additional.setName(cmd.getName());
    additional.setDownloadUrl(cmd.getUrl());
    iAdditionalService.save(additional);
    return SingleResponse.of(additional.getId());
  }

  private com.alibaba.cola.dto.Response priceExcelHandler(AdditionalCmd cmd) {
    File exceFile = null;
    try {
      String name = cmd.getName();
      exceFile = new File(System.currentTimeMillis() + ".xlsx");
      String modelDownloadUrl = cmd.getUrl();
      OkHttpClient client = new OkHttpClient();
      Request request = new Request.Builder().url(modelDownloadUrl).build();
      try (Response response = client.newCall(request).execute()) {
        ResponseBody body = response.body();
        if (body != null) {
          FileOutputStream fos = new FileOutputStream(exceFile);
          fos.write(body.bytes());
          fos.close();
        } else {
          throw new RuntimeException("Failed to download Excel file.ResponseBody is null");
        }
      } catch (IOException e) {
        log.error("failed to download Excel file", e);
        throw new RuntimeException("Failed to download Excel file." + e.getMessage());
      }

      if (name.contains("活动spu多件供价")) {
        readExcelController.randomHandler(Files.newInputStream(exceFile.toPath()), 2);
      }
      if (name.contains("日常spu多件供价")) {
        readExcelController.randomHandler(Files.newInputStream(exceFile.toPath()), 1);
      }
      if (name.contains("活动sku多件供价")) {
        readExcelController.singleHandler(Files.newInputStream(exceFile.toPath()), 2);
      }
      if (name.contains("日常sku多件供价")) {
        readExcelController.singleHandler(Files.newInputStream(exceFile.toPath()), 1);
      }

      Additional additional = new Additional();
      additional.setName(cmd.getName());
      additional.setDownloadUrl(cmd.getUrl());
      iAdditionalService.save(additional);
      return SingleResponse.of(additional.getId());
    } catch (Exception e) {
      log.error("priceExcelHandler fail", e);
      return com.alibaba.cola.dto.Response.buildFailure(
          ErrorCode.SYSTEM_BUSY.getCode(), e.getMessage());
    } finally {
      FileUtil.del(exceFile);
    }
  }

  @Override
  public SingleResponse<Additional> downloadAdditional(Long id) {
    return SingleResponse.of(iAdditionalService.getById(id));
  }

  @Override
  public PageResponse<Additional> pageQueryAdditional(AdditionalPageQuery pageQuery) {
    PageInfo<Additional> pageInfo =
        PageHelper.startPage(pageQuery.getPageIndex(), pageQuery.getPageSize())
            .doSelectPageInfo(
                () -> {
                  iAdditionalService
                      .lambdaQuery()
                      .like(
                          StrUtil.isNotEmpty(pageQuery.getName()),
                          Additional::getName,
                          pageQuery.getName())
                      .orderByDesc(Additional::getId)
                      .list();
                });
    return PageResponse.of(
        pageInfo.getList(),
        (int) pageInfo.getTotal(),
        pageInfo.getPageSize(),
        pageInfo.getPageNum());
  }

  @Override
  public void deleteAdditional(Long id) {
    Additional byId = iAdditionalService.getById(id);
    String name = byId.getName();
    File file = new File(name);
    if (file.exists()) {
      if (!file.delete()) {
        log.error("删除临时缓存文件失败，file:{}", file.getAbsolutePath());
      }
    }
    iAdditionalService.removeByIdWithTime(id);
  }

  @Autowired private ItemOptimizePlanBizService itemOptimizePlanBizService;

  @Override
  public PageResponse<ItemOptimizePlanDropDown> itemOptimizePlan(ItemOptimizePlanQuery query) {
    final PageResponse<ItemOptimizePlanBaseInfo> response = itemOptimizePlanBizService.query(query);
    return ResponseFactory.ofPage(
        response,
        baseInfo -> {
          final ItemOptimizePlanDropDown itemOptimizePlanDropDown = new ItemOptimizePlanDropDown();
          itemOptimizePlanDropDown.setId(baseInfo.getId());
          itemOptimizePlanDropDown.setPlanNo(baseInfo.getPlanNo());
          itemOptimizePlanDropDown.setPlanName(baseInfo.getPlanName());

          return itemOptimizePlanDropDown;
        });
  }

  @Autowired private PsysUserGateway psysUserGateway;

  @Override
  public MultiResponse<StaffDropDownItem> responsiblePersonQuery(ResponsiblePersonQuery query) {
    if (query.getType() == 4) {
      final StaffListQuery staffListQuery = new StaffListQuery();
      staffListQuery.setPageSize(query.getPageSize());
      staffListQuery.setPageIndex(query.getPageIndex());
      final ArrayList<String> departments = new ArrayList<>();
      departments.add("法务内控部");
      if (ApplicationContextUtil.isActiveProfile("test", "dev", "gray")) {
        departments.add("质控组");
      }
      staffListQuery.setDepts(departments);
      staffListQuery.setUserId(query.getUserId());
      staffListQuery.setNickname(query.getName());
      final List<StaffInfo> staffInfos = userGateway.queryStaffList(staffListQuery);
      return MultiResponse.of(toStaffDropDownItems(staffInfos));
    }
    PartnerPersonCmd partnerPersonCmd = new PartnerPersonCmd();
    partnerPersonCmd.setRole_type(query.getType());
    final List<PartnerPersonResp> data = psysUserGateway.responsiblePersonQuery(partnerPersonCmd);
    final List<Long> userIds =
        data.stream()
            .map(PartnerPersonResp::getId)
            .map(Long::valueOf)
            .distinct()
            .collect(Collectors.toList());
    final StaffListQuery staffListQuery = new StaffListQuery();
    staffListQuery.setUserIds(userIds);
    staffListQuery.setNickname(query.getName());
    staffListQuery.setPageSize(49);
    staffListQuery.setUserId(query.getUserId());
    final List<StaffInfo> staffInfos = userGateway.queryStaffList(staffListQuery);
    if (ApplicationContextUtil.isActiveProfile("test", "dev", "gray")) {
      final StaffListQuery staffListQueryForTest = new StaffListQuery();
      staffListQueryForTest.setPageSize(49);
      staffListQueryForTest.setPageIndex(1);
      staffListQuery.setDept("质控组");
      final List<StaffInfo> staffInfosForTest = userGateway.queryStaffList(staffListQuery);
      staffInfos.addAll(staffInfosForTest);
    }
    return MultiResponse.of(toStaffDropDownItems(staffInfos));
  }

  @NonNull
  private static List<StaffDropDownItem> toStaffDropDownItems(List<StaffInfo> staffInfos) {
    return staffInfos.stream()
        .map(
            staffInfo ->
                new StaffDropDownItem(
                    staffInfo.getUserId(), staffInfo.getUserName(), staffInfo.getNickname()))
        .distinct()
        .collect(Collectors.toList());
  }

  @Override
  public MultiResponse<String> invoiceTitle(String param) {
    List<String> titles = invoiceTitleConfig.getTitles();
    if (StrUtil.isNotBlank(param)) {
      return MultiResponse.of(
          titles.stream().filter(val -> val.contains(param)).collect(Collectors.toList()));
    }
    return MultiResponse.of(invoiceTitleConfig.getTitles());
  }

  @Override
  public SingleResponse<Object> getValByKey(String key) {
    return SingleResponse.of(RedisUtil.get(key));
  }

  @Override
  public PageResponse<ExternalUserVo> externalUserPage(ExternalUserPageQuery query) {
    PageInfo<ExternalUser> externalUserPageInfo =
        PageHelper.startPage(query.getPageIndex(), query.getPageSize())
            .doSelectPageInfo(
                () ->
                    iExternalUserService
                        .lambdaQuery()
                        .eq(
                            StringUtils.hasText(query.getTel().replaceAll("\"", "")),
                            ExternalUser::getTel,
                            query.getTel())
                        .eq(
                            Objects.nonNull(query.getStatus()),
                            ExternalUser::getStatus,
                            query.getStatus())
                        .list());
    return ResponseFactory.ofPage(
        externalUserPageInfo,
        externalUser -> {
          ExternalUserVo vo = new ExternalUserVo();
          vo.setId(externalUser.getId());
          vo.setTel(externalUser.getTel().toString());
          vo.setName(externalUser.getName());
          vo.setStatus(externalUser.getStatus());
          return vo;
        });
  }

  @Override
  public SingleResponse<Boolean> delExternalUser(Long id) {
    iExternalUserService.lambdaUpdate().eq(ExternalUser::getId, id).remove();
    return SingleResponse.of(true);
  }

  @Override
  public SingleResponse<Boolean> updateExternalUser(ExternalUserCmd cmd) {
    if (Objects.isNull(cmd.getId())) {
      return addExternalUser(cmd);
    }

    Long id = cmd.getId();
    ExternalUser oldOne = iExternalUserService.getById(id);
    if (!oldOne.getTel().toString().equals(cmd.getTel())) {
      Integer thisTelCount =
          iExternalUserService.lambdaQuery().eq(ExternalUser::getTel, cmd.getTel()).count();
      Assert.state(thisTelCount == 0, "手机号码不得重复");
    }
    iExternalUserService
        .lambdaUpdate()
        .set(StringUtils.hasText(cmd.getTel()), ExternalUser::getTel, cmd.getTel())
        .set(StringUtils.hasText(cmd.getName()), ExternalUser::getName, cmd.getName())
        .set(Objects.nonNull(cmd.getStatus()), ExternalUser::getStatus, cmd.getStatus())
        .eq(ExternalUser::getId, cmd.getId())
        .update();
    return SingleResponse.of(true);
  }

  @Override
  public SingleResponse<Boolean> addExternalUser(ExternalUserCmd cmd) {

    Integer thisTelCount =
        iExternalUserService.lambdaQuery().eq(ExternalUser::getTel, cmd.getTel()).count();
    Assert.state(thisTelCount == 0, "手机号码不得重复");

    ExternalUser externalUser = new ExternalUser();
    Long id = externalUserMapper.getIdByTel(Long.valueOf(cmd.getTel()));
    if (Objects.nonNull(id)) {
      externalUser.setId(id);
    }

    externalUser.setName(cmd.getName());
    externalUser.setTel(Long.valueOf(cmd.getTel()));
    externalUser.setStatus(cmd.getStatus());
    iExternalUserService.save(externalUser);
    return SingleResponse.of(true);
  }
}
