package com.daddylab.supplier.item.application.offShelf.dto;

import com.daddylab.supplier.item.types.process.ProcessAdvice;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class OffShelfBatchApproveCmd {
    @NotEmpty(message = "ids 不能为空")
    private List<Long> ids;
    private ProcessAdvice advice;
    private List<Long> shopIds;
    private Boolean selectAllShop;
    private List<Long> operatorUid;
    private String remark;
}
