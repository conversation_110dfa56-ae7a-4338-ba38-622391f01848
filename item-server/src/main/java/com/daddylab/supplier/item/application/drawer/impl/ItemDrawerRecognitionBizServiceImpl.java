package com.daddylab.supplier.item.application.drawer.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.http.HtmlUtil;
import com.daddylab.supplier.item.application.drawer.ItemDrawerRecognitionBizService;
import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerChangeEnum;
import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerImageTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerRecognitionTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerImageService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerRecognitionTaskService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerService;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerModuleId;
import com.daddylab.supplier.item.types.recognitionTask.ItemDrawerRecognitionTaskStatus;
import com.google.common.collect.Lists;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/12/6
 */
@Service
@RequiredArgsConstructor
public class ItemDrawerRecognitionBizServiceImpl implements ItemDrawerRecognitionBizService {

    private final IItemDrawerRecognitionTaskService itemDrawerRecognitionTaskService;
    private final IItemDrawerService itemDrawerService;
    private final IItemDrawerImageService itemDrawerImageService;

    @Override
    public void createRecognitionTasks(ItemAuditType type, Long itemId, Integer round, ItemDrawerModuleId[] moduleIds) {
        if (moduleIds.length == 0) {
            return;
        }
        final ItemDrawer itemDrawer = itemDrawerService.getByItemId(itemId)
                .orElseThrow(() -> new IllegalStateException("抽屉数据异常"));
        final List<ItemDrawerRecognitionTask> hisImageTasks = getHisImageTasks(itemId, type);

        List<ItemDrawerRecognitionTask> tasks = Lists.newArrayList();
        switch (type) {

            case ITEM_MATERIAL:
                final List<ItemDrawerImage> itemDrawerImages = itemDrawerImageService.lambdaQuery()
                        .eq(ItemDrawerImage::getDrawerId, itemDrawer.getId()).list();
                final Map<ItemDrawerImageTypeEnum, List<ItemDrawerImage>> itemDrawerImagesTypeGroup = itemDrawerImages.stream()
                        .collect(Collectors.groupingBy(ItemDrawerImage::getType));
                if (ArrayUtil.contains(moduleIds, ItemDrawerModuleId.IMG_INFO)) {
                    //【图片信息】商品主图
                    for (ItemDrawerImage itemDrawerImage : itemDrawerImagesTypeGroup.getOrDefault(
                            ItemDrawerImageTypeEnum.ITEM, Collections.emptyList())) {
                        createNewImageTaskOrCopyResultFromHisTask(type, itemId, round, hisImageTasks, tasks,
                                itemDrawerImage,
                                ItemDrawerModuleId.IMG_INFO, ItemDrawerChangeEnum.IMAGES);
                    }
                    //【图片信息】商品主图（其他尺寸）
                    for (ItemDrawerImage itemDrawerImage : itemDrawerImagesTypeGroup.getOrDefault(
                            ItemDrawerImageTypeEnum.OTHER_ITEM, Collections.emptyList())) {
                        createNewImageTaskOrCopyResultFromHisTask(type, itemId, round, hisImageTasks, tasks,
                                itemDrawerImage,
                                ItemDrawerModuleId.IMG_INFO, ItemDrawerChangeEnum.OTHER_IMAGES);
                    }
                    //【图片信息】规格图片
                    for (ItemDrawerImage itemDrawerImage : itemDrawerImagesTypeGroup.getOrDefault(
                            ItemDrawerImageTypeEnum.ATTR, Collections.emptyList())) {
                        createNewImageTaskOrCopyResultFromHisTask(type, itemId, round, hisImageTasks, tasks,
                                itemDrawerImage,
                                ItemDrawerModuleId.IMG_INFO, ItemDrawerChangeEnum.ATTR_IMAGES);
                    }
                }

                if (ArrayUtil.contains(moduleIds, ItemDrawerModuleId.TEXT_AND_ATTR)) {
                    //【文案&属性】首页文案
                    tasks.add(ofTextField(type, itemId, round, ItemDrawerModuleId.TEXT_AND_ATTR,
                            ItemDrawerChangeEnum.HOME_COPY, itemDrawer.getHomeCopy()));
                    //【文案&属性】淘宝标题
                    tasks.add(ofTextField(type, itemId, round, ItemDrawerModuleId.TEXT_AND_ATTR,
                            ItemDrawerChangeEnum.TB_TITLE, itemDrawer.getTbTitle()));
                }

                if (ArrayUtil.contains(moduleIds, ItemDrawerModuleId.DETAILS)) {
                    //【详情图片】
                    for (ItemDrawerImage itemDrawerImage : itemDrawerImagesTypeGroup.getOrDefault(
                            ItemDrawerImageTypeEnum.DETAIL, Collections.emptyList())) {
                        createNewImageTaskOrCopyResultFromHisTask(type, itemId, round, hisImageTasks, tasks,
                                itemDrawerImage,
                                ItemDrawerModuleId.DETAILS, ItemDrawerChangeEnum.DETAIL_IMAGES);
                    }
                }
                break;
            case LIVE_VERBAL_TRICK:
                //【直播话术】
                final String liveVerbalTrick = itemDrawer.getLiveVerbalTrick();
                //识别图片链接创建图片任务
                final List<String> imageUrlList = matchImageUrl(
                        liveVerbalTrick);
                //创建图片任务
                if (CollectionUtil.isNotEmpty(imageUrlList)) {
                    for (String url : imageUrlList) {
                        createNewImageTaskOrCopyResultFromHisTask(type, itemId, round, hisImageTasks, tasks,
                                url, ItemDrawerModuleId.LIVE_VERBAL_TRICK,
                                ItemDrawerChangeEnum.LIVE_VERBAL_TRICK);
                    }
                }
                //创建文本任务
                final String liveVerbalTrickFiltered = HtmlUtil.cleanHtmlTag(liveVerbalTrick);
                tasks.add(ofTextField(type, itemId, round, ItemDrawerModuleId.LIVE_VERBAL_TRICK,
                        ItemDrawerChangeEnum.LIVE_VERBAL_TRICK, liveVerbalTrickFiltered));
                break;
        }

        if (!tasks.isEmpty()) {
            itemDrawerRecognitionTaskService.saveBatch(tasks);
        }
    }

    @NonNull
    private List<String> matchImageUrl(String liveVerbalTrick) {
        if (StringUtil.isBlank(liveVerbalTrick)) {
            return Collections.emptyList();
        }
        final Pattern pattern = PatternPool.get(
                "(?:https://|http://)([\\w-]+\\.)+[\\w-]+(:\\d+)*(/[\\w- ./?%&=]*)?");
        final Matcher matcher = pattern.matcher(liveVerbalTrick);
        final StringBuffer sb = new StringBuffer();
        List<String> imageUrlList = Lists.newArrayList();
        while (matcher.find()) {
            matcher.appendReplacement(sb, "");
            final String url = matcher.group(0);
            final String suffix = FileNameUtil.getSuffix(url);
            if (!Arrays.asList("jpg", "jpeg", "png").contains(suffix)) {
                continue;
            }
            imageUrlList.add(url);
        }
        matcher.appendTail(sb);
        return imageUrlList;
    }

    private void createNewImageTaskOrCopyResultFromHisTask(ItemAuditType type, Long itemId, Integer round,
            List<ItemDrawerRecognitionTask> hisTasks,
            List<ItemDrawerRecognitionTask> tasks, ItemDrawerImage itemDrawerImage,
            ItemDrawerModuleId module, ItemDrawerChangeEnum field) {
        final Optional<ItemDrawerRecognitionTask> hisTaskMatched = hisTasks.stream()
                .filter(hisTask -> hisImageTaskIsMatch(itemDrawerImage, hisTask))
                .findFirst();
        if (hisTaskMatched.isPresent()) {
            tasks.add(ofHisTask(type, itemId, round, module, field, hisTaskMatched.get()));
        } else {
            tasks.add(ofImageField(type, itemId, round, module, field, itemDrawerImage));
        }
    }

    private void createNewImageTaskOrCopyResultFromHisTask(ItemAuditType type, Long itemId, Integer round,
            List<ItemDrawerRecognitionTask> hisTasks,
            List<ItemDrawerRecognitionTask> tasks, String imageUrl,
            ItemDrawerModuleId module, ItemDrawerChangeEnum field) {
        final Optional<ItemDrawerRecognitionTask> hisTaskMatched = hisTasks.stream()
                .filter(hisTask -> hisImageTaskIsMatch(imageUrl, hisTask))
                .findFirst();
        if (hisTaskMatched.isPresent()) {
            tasks.add(ofHisTask(type, itemId, round, module, field, hisTaskMatched.get()));
        } else {
            tasks.add(ofImageField(type, itemId, round, module, field, imageUrl));
        }
    }

    private boolean hisImageTaskIsMatch(ItemDrawerImage itemDrawerImage,
            ItemDrawerRecognitionTask hisTask) {
        return hisTask.getImageUrl().equals(itemDrawerImage.getUrl());
    }

    private boolean hisImageTaskIsMatch(String url, ItemDrawerRecognitionTask hisTask) {
        return hisTask.getImageUrl().equals(url);
    }

    private List<ItemDrawerRecognitionTask> getHisImageTasks(Long itemId, ItemAuditType type) {
        return itemDrawerRecognitionTaskService.lambdaQuery()
                .eq(ItemDrawerRecognitionTask::getItemId, itemId)
                .eq(ItemDrawerRecognitionTask::getStatus, ItemDrawerRecognitionTaskStatus.FINISHED)
                .ne(ItemDrawerRecognitionTask::getImageUrl, "")
                .eq(ItemDrawerRecognitionTask::getType, type)
                .orderByDesc(ItemDrawerRecognitionTask::getRound)
                .orderByAsc(ItemDrawerRecognitionTask::getId)
                .select(
                        ItemDrawerRecognitionTask::getId,
                        ItemDrawerRecognitionTask::getRound,
                        ItemDrawerRecognitionTask::getDrawerImageId,
                        ItemDrawerRecognitionTask::getImageUrl,
                        ItemDrawerRecognitionTask::getImageType,
                        ItemDrawerRecognitionTask::getHitResult).list();
    }

    private ItemDrawerRecognitionTask ofTextField(ItemAuditType type,Long itemId, Integer round,
            ItemDrawerModuleId moduleId,
            ItemDrawerChangeEnum field, String content) {
        final ItemDrawerRecognitionTask task = new ItemDrawerRecognitionTask();
        task.setModule(moduleId.getValue());
        task.setField(field.getValue());
        task.setItemId(itemId);
        task.setRound(round);
        task.setStatus(ItemDrawerRecognitionTaskStatus.WAIT_CHECK);
        task.setContent(content);
        task.setDrawerImageId(0L);
        task.setImageUrl(null);
        task.setImageType(null);
        task.setType(type);
        return task;
    }

    private ItemDrawerRecognitionTask ofImageField(ItemAuditType type, Long itemId, Integer round,
            ItemDrawerModuleId moduleId,
            ItemDrawerChangeEnum field, ItemDrawerImage image) {
        final ItemDrawerRecognitionTask task = new ItemDrawerRecognitionTask();
        task.setModule(moduleId.getValue());
        task.setField(field.getValue());
        task.setItemId(itemId);
        task.setRound(round);
        task.setStatus(ItemDrawerRecognitionTaskStatus.PREPARE);
        task.setContent(null);
        task.setDrawerImageId(image.getId());
        task.setImageUrl(image.getUrl());
        task.setImageType(image.getType());
        task.setType(type);
        return task;
    }

    private ItemDrawerRecognitionTask ofImageField(ItemAuditType type, Long itemId, Integer round,
            ItemDrawerModuleId moduleId,
            ItemDrawerChangeEnum field, String imageUrl) {
        final ItemDrawerRecognitionTask task = new ItemDrawerRecognitionTask();
        task.setModule(moduleId.getValue());
        task.setField(field.getValue());
        task.setItemId(itemId);
        task.setRound(round);
        task.setStatus(ItemDrawerRecognitionTaskStatus.PREPARE);
        task.setContent(null);
        task.setDrawerImageId(0L);
        task.setImageUrl(imageUrl);
        task.setImageType(null);
        task.setType(type);
        return task;
    }

    private ItemDrawerRecognitionTask ofHisTask(ItemAuditType type, Long itemId, Integer round,
            ItemDrawerModuleId moduleId,
            ItemDrawerChangeEnum field, ItemDrawerRecognitionTask hisTask) {
        final ItemDrawerRecognitionTask task = new ItemDrawerRecognitionTask();
        task.setModule(moduleId.getValue());
        task.setField(field.getValue());
        task.setItemId(itemId);
        task.setRound(round);
        task.setStatus(ItemDrawerRecognitionTaskStatus.FINISHED);
        task.setDrawerImageId(hisTask.getDrawerImageId());
        task.setImageUrl(hisTask.getImageUrl());
        task.setImageType(hisTask.getImageType());
        task.setHitResult(hisTask.getHitResult());
        task.setLog("copy from #" + hisTask.getId());
        task.setType(type);
        return task;
    }

}
