package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.combinationHandler;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.BaseProcess;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.PriceProcessor;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseRandomSkuCombinationPrice;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Semaphore;

/**
 * 日常价-同spu下，sku随机，任意数量组合价。
 * 只处理单品
 *
 * <AUTHOR> up
 * @date 2022年10月24日 10:49 AM
 */
@Service
@Slf4j
public class DailyRandomCombinationPrice extends BaseProcess implements PriceProcessor {

    private final Semaphore semaphore = new Semaphore(4);

    @Override
    public Boolean doPriceProcess(TimeBO timeBO) {
        log.info("DailyRandomCombinationPrice doPriceProcess start");

        List<PurchaseRandomSkuCombinationPrice> list = getRandomCombinationPriceByType(timeBO, 1);
        if (CollUtil.isEmpty(list)) {
            log.info("DailySingleCombinationPrice doPriceProcess finish.purchase price is empty");
            return true;
        }

        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        list.forEach(val -> ThreadUtil.execute(PoolEnum.PURCHASE_POOL, () -> {
            try {
                semaphore.acquire();
                log.info("DailyRandomCombinationPrice doPriceProcess start skuCode:{}", val.getCode());
                randomCombinationPriceHandler(val, timeBO.getOperateMonth(),1);
                log.info("DailyRandomCombinationPrice doPriceProcess finish skuCode:{}", val.getCode());
            } catch (Exception e) {
                log.error("DailyRandomCombinationPrice doPriceProcess fail price:{}", JsonUtil.toJson(val), e);
            } finally {
                countDownLatch.countDown();
                semaphore.release();
            }
        }));

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("[{}] DailyRandomCombinationPrice doPriceProcess await InterruptedException", timeBO.getOperateMonth(), e);
        }

        log.info("DailyRandomCombinationPrice doPriceProcess finish");
        return true;
    }


}
