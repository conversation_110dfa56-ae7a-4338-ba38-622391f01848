package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemOptimizeAdvice;

/**
 * <p>
 * 商品优化建议 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface IItemOptimizeAdviceService extends IDaddyService<ItemOptimizeAdvice> {

}
