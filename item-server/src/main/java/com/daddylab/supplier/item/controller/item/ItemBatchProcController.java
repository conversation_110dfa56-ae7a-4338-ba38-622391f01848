package com.daddylab.supplier.item.controller.item;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.item.itemBatchProc.ItemBatchProcBizService;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.types.itemBatchProc.ModifyBuyerCmd;
import com.daddylab.supplier.item.types.itemBatchProc.ModifyProviderCmd;
import com.daddylab.supplier.item.types.itemBatchProc.ModifyStatusCmd;
import com.daddylab.supplier.item.types.itemBatchProc.ModifyWarehouseCmd;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;

import lombok.RequiredArgsConstructor;

import org.springframework.web.bind.annotation.*;

/** 商品批量处理API */
@Api(value = "商品批量处理API", tags = "商品批量处理API")
@RestController()
@RequestMapping("/item/batchProc")
@RequiredArgsConstructor
public class ItemBatchProcController {
    private final ItemBatchProcBizService itemBatchProcBizService;

    @ResponseBody
    @ApiOperation("批量修改采购员")
    @PostMapping("/modifyBuyer")
    @ApiResponse(code = 200, message = "异步任务ID")
    public SingleResponse<Long> modifyBuyer(@RequestBody ModifyBuyerCmd cmd) {
        cmd.getQuery()
                .setBusinessLine(
                        UserPermissionJudge.filterBusinessLinePerm(
                                cmd.getQuery().getBusinessLine()));
        return itemBatchProcBizService.modifyBuyer(cmd);
    }

    @ResponseBody
    @ApiOperation("批量修改仓库")
    @PostMapping("/modifyWarehouse")
    @ApiResponse(code = 200, message = "异步任务ID")
    public SingleResponse<Long> modifyWarehouse(@RequestBody ModifyWarehouseCmd cmd) {
        cmd.getQuery()
                .setBusinessLine(
                        UserPermissionJudge.filterBusinessLinePerm(
                                cmd.getQuery().getBusinessLine()));
        return itemBatchProcBizService.modifyWarehouse(cmd);
    }

    @ResponseBody
    @ApiOperation("批量修改供应商")
    @PostMapping("/modifyProvider")
    @ApiResponse(code = 200, message = "异步任务ID")
    public SingleResponse<Long> modifyProvider(@RequestBody ModifyProviderCmd cmd) {
        cmd.getQuery()
                .setBusinessLine(
                        UserPermissionJudge.filterBusinessLinePerm(
                                cmd.getQuery().getBusinessLine()));
        return itemBatchProcBizService.modifyProvider(cmd);
    }

    @ResponseBody
    @ApiOperation("批量修改商品状态")
    @PostMapping("/modifyStatus")
    @ApiResponse(code = 200, message = "异步任务ID")
    public SingleResponse<Long> modifyStatus(@RequestBody ModifyStatusCmd cmd) {
        cmd.getQuery()
                .setBusinessLine(
                        UserPermissionJudge.filterBusinessLinePerm(
                                cmd.getQuery().getBusinessLine()));
        return itemBatchProcBizService.modifyStatus(cmd);
    }
}
