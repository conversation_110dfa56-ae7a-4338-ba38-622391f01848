package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemStockChangeLog;

/**
 * <p>
 * 商品库存变动记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
public interface ItemStockChangeLogMapper extends DaddyBaseMapper<ItemStockChangeLog> {

}
