package com.daddylab.supplier.item.domain.itemStock.service;

import com.daddylab.supplier.item.domain.itemStock.gateway.ItemStockGateway;
import com.daddylab.supplier.item.domain.itemStock.vo.ItemStockChange;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import java.util.List;

/**
 * @deprecated 使用 com.daddylab.supplier.item.domain.itemStock.gateway.WarehouseStockGateway 替代
 */
@Service
public class ItemStockDomainService {
    @Inject
    private ItemStockGateway gateway;

    public long getSkuStock(long itemId, long skuId) {
        return gateway.getSkuStock(itemId, skuId);
    }

    public long getSkuStock(long itemId, String skuCode, String warehouseNo) {
        ItemSku skuStock = gateway.getSkuStock(itemId, skuCode, warehouseNo);
        Long skuId = skuStock.getId();
        return gateway.getSkuStock(itemId, skuId);
    }

    public ItemSku getSku(long itemId, String skuCode, String warehouseNo) {
        return gateway.getSkuStock(itemId, skuCode, warehouseNo);
    }

    public boolean stockIn(long itemId, long skuId, int num) {
        return gateway.stockIn(itemId, skuId, num);
    }

    public boolean stockOut(long itemId, long skuId, int num) {
        return gateway.stockOut(itemId, skuId, num);
    }

    public boolean stockOut(long itemId, String skuCode, int num, String wdtStorageNo) {
        ItemSku skuStock = gateway.getSkuStock(itemId, skuCode, wdtStorageNo);
        Long skuId = skuStock.getId();
        return gateway.stockOut(itemId, skuId, num);
    }

    public boolean stockSet(long itemId, long skuId, int num) {
        return gateway.setStock(itemId, skuId, num);
    }

    public List<ItemStockChange> getStockChangeLogs(long itemId, long skuId) {
        return gateway.getStockChangeLogs(itemId, skuId);
    }

    public long getItemTotalStock(long itemId) {
        return gateway.getItemTotalStock(itemId);
    }
}
