package com.daddylab.supplier.item.infrastructure.gatewayimpl.purchase.dto;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName PartnerPurchaseResp.java
 * @description
 * @createTime 2021年11月18日 15:46:00
 */
@AllArgsConstructor
@NoArgsConstructor
public class PartnerPurchaseResp implements Serializable {

    private static final long serialVersionUID = -358687739114115762L;

    private Long partnerId;
    private Long organizationPartnerId;
    private String organizationName;
    private String itemName;
    private Long uId;
    private String mobile;
    private String mainMobile;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Long getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Long partnerId) {
        this.partnerId = partnerId;
    }

    public Long getOrganizationPartnerId() {
        return organizationPartnerId;
    }

    public void setOrganizationPartnerId(Long organizationPartnerId) {
        this.organizationPartnerId = organizationPartnerId;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public Long getuId() {
        return uId;
    }

    public void setuId(Long uId) {
        this.uId = uId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMainMobile() {
        return mainMobile;
    }

    public void setMainMobile(String mainMobile) {
        this.mainMobile = mainMobile;
    }
}
