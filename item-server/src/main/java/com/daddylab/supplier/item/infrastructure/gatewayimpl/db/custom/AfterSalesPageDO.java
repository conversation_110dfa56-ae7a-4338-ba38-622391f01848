package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesAbnormalState;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2022年10月19日 11:44 AM
 */
@Data
public class AfterSalesPageDO {

    private Long id;
    private String logisticsNo;
    private String logisticsName;
    private String itemName;
    private String remark;
    private Integer quantity;
    private AfterSalesAbnormalState state;
    private String createdAtStr;
    private String warehouseName;
    private String warehouseNo;
    private String providerName;
    private String images;
}
