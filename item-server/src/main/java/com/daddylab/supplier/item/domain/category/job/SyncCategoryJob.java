package com.daddylab.supplier.item.domain.category.job;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Category;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICategoryService;
import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022/5/23 2:16 下午
 */
@Slf4j
@Service
public class SyncCategoryJob {

    @Autowired
    ICategoryService iCategoryService;

    @Autowired
    KingDeeTemplate kingDeeTemplate;

    @XxlJob("syncCategoryJob")
    public void syncCategoryJob() {


        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            int size = 100;
            int index = 0;

            LambdaQueryWrapper<Category> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Category::getKingDeeId, "").orderByAsc(Category::getLevel);

            PageInfo<Category> pageInfo = PageHelper.startPage(index, size).doSelectPageInfo(() -> iCategoryService.list(wrapper));
            int pages = pageInfo.getPages();


            while (pages > 0) {
                List<Long> errorList = new LinkedList<>();
                for (Category record : pageInfo.getList()) {
                    try{
                        kingDeeTemplate.handler(ApiEnum.GROUP_SAVE_CATEGORY, record.getId(), "");
                    }catch (Exception e){
                        log.error("品类同步KingDee异常，id:{}",record.getId(),e);
                        errorList.add(record.getId());
                    }
                }
                if(CollectionUtils.isNotEmpty(errorList)){
                    Alert.text(MessageRobotCode.GLOBAL,"品类同步KingDee异常。"+ StrUtil.join(",",errorList));
                }

                pages = pages - 1;

                index = index + 1;
                pageInfo = PageHelper.startPage(index, size).doSelectPageInfo(() -> iCategoryService.list(wrapper));
            }
        });
    }

}
