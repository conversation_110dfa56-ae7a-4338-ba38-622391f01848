package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.daddylab.supplier.item.infrastructure.domain.Entity;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品上新计划
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemLaunchPlan extends Entity {
    /**
     * 编号
     */
    private String planNo;

    /**
     * 名称
     */
    private String planName;

    /**
     * 上新时间
     */
    private Long launchTime;

    /**
     * 商品数量
     */
    private Integer itemNum;

    /**
     * 0:未提交。1已提交
     */
    private Integer isSubmit;

    /**
     * 合作模式
     */
    private String businessLine;

    public String getBusinessLine() {
        return DivisionLevelValueEnum.filterCorpType(businessLine);
    }
}
