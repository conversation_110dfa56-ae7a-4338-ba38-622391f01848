package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.itemSyncMiniProgram;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/3
 */
@Data
@ApiModel(value = "ERP商品扩展信息", description = "ERP商品扩展信息")
public class ErpItemExtend {

    @ApiModelProperty(value = "产品备案名称")
    private String filingName = "";

    @ApiModelProperty(value = "品牌名称")
    private String brandName = "";

    @ApiModelProperty(value = "备案或注册编号")
    private String registerNo = "";

    @ApiModelProperty(value = "产品备案功效")
    private List<FilingEfficacy> filingEfficacyList = Collections.emptyList();

    @ApiModelProperty(value = "适用人群")
    private List<ApplicablePopulation> applicablePopulations = Collections.emptyList();

    @ApiModelProperty(value = "适用肤质")
    private String suitableSkin = "";

    @ApiModelProperty(value = "是否为特殊用途化妆品 0否1是")
    private Integer isSpecialPurpose = 0;

    @ApiModelProperty(value = "备案人/境内责任人（is_special_purpose为否）")
    private String filingPerson = "";

    @ApiModelProperty(value = "备案人/境内责任人地址（is_special_purpose为否）")
    private String filingAddress = "";

    @ApiModelProperty(value = "注册人/境内责任人（is_special_purpose为是）")
    private String registerPerson = "";

    @ApiModelProperty(value = "注册人地址/境内责任人地址（is_special_purpose为是）")
    private String registerAddress = "";

    @ApiModelProperty(value = "净含量")
    private String netContents = "";

    @ApiModelProperty(value = "保质期")
    private String shelfLifes = "";

    @ApiModelProperty(value = "自定义属性")
    private List<CustomAttribute> customAttributeList = Collections.emptyList();


}
