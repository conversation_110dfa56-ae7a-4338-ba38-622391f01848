package com.daddylab.supplier.item.application.afterSaleLogistics.dto;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/29
 */
@lombok.Data
public class AfterSaleLogisticsFollowUpCmd {
  @ApiModelProperty(value = "异常id")
  private Long id;

  @ApiModelProperty(value = "异常ids")
  private List<Long> ids;

  @ApiModelProperty(value = "处理备注")
  private String remark;
}
