package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapperError;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailWrapperErrorMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderDetailWrapperErrorService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * wdt订单信息清洗错误信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Service
public class WdtOrderDetailWrapperErrorServiceImpl extends DaddyServiceImpl<WdtOrderDetailWrapperErrorMapper, WdtOrderDetailWrapperError> implements IWdtOrderDetailWrapperErrorService {

    @Override
    public void asyncSave(WdtOrderDetailWrapperError error) {
//        ThreadUtil.execute(PoolEnum.PURCHASE_POOL,()->{
//            this.save(error);
//        });
        this.save(error);
    }
}

