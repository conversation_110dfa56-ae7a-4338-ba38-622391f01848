package com.daddylab.supplier.item.infrastructure.config.batch;

import org.redisson.api.RedissonClient;
import org.redisson.spring.transaction.RedissonTransactionManager;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.explore.support.MapJobExplorerFactoryBean;
import org.springframework.batch.core.launch.support.SimpleJobLauncher;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.repository.support.MapJobRepositoryFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

/**
 * <AUTHOR>
 * @class BatchConfig.java
 * @description 内存处理
 * @date 2024-03-14 09:33
 */
@Configuration
public class BatchConfig {

    @Bean
    public MapJobRepositoryFactoryBean jobRepository() {
        return new MapJobRepositoryFactoryBean();
    }

    @Bean
    public SimpleJobLauncher jobLauncher(JobRepository jobRepository) {
        SimpleJobLauncher simpleJobLauncher = new SimpleJobLauncher();
        simpleJobLauncher.setJobRepository(jobRepository);
        return simpleJobLauncher;
    }

    @Bean
    public JobBuilderFactory jobBuilders(JobRepository jobRepository) {
        return new JobBuilderFactory(jobRepository);
    }

    @Bean
    public StepBuilderFactory stepBuilders(JobRepository jobRepository, PlatformTransactionManager redissonTransactionManager) {
        return new StepBuilderFactory(jobRepository, redissonTransactionManager);
    }

    @Bean
    public MapJobExplorerFactoryBean jobExplorer() {
        MapJobExplorerFactoryBean mapJobExplorerFactoryBean = new MapJobExplorerFactoryBean();
        mapJobExplorerFactoryBean.setRepositoryFactory(jobRepository());
        return mapJobExplorerFactoryBean;
    }
}