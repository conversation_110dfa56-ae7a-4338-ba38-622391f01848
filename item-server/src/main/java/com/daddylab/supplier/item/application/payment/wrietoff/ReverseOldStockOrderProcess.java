package com.daddylab.supplier.item.application.payment.wrietoff;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.ProcessRequest;
import com.daddylab.supplier.item.application.stockInOrder.StockInOrderBizService;
import com.daddylab.supplier.item.application.stockOutOrder.StockOutOrderBizService;
import com.daddylab.supplier.item.controller.stockout.dto.StockOutOrderCmd;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PurchasePayableOrderStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockInState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockOutOrderState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 主流程，根据采购单获取对应的出入库单（非冲销作废状态，非冲销逆向单据），生成各自对应的逆向单据，并且同步给金蝶。
 * 回滚，根据采购单获取对应的单据（非冲销作废状态，非冲销逆向单据），
 * 冲销作废状态的出入库单/下游应付单，改为正常完结状态；冲销逆向单据（出入库单/下游应付单）删除
 *
 * <AUTHOR> up
 * @date 2024年01月19日 10:38 AM
 */
@Slf4j
@Component
@Order(1)
public class ReverseOldStockOrderProcess implements WriteOffProcess {

    final IStockInOrderService iStockInOrderService;
    final IStockOutOrderService iStockOutOrderService;
    final IStockInOrderDetailService iStockInOrderDetailService;
    final IStockOutOrderDetailService iStockOutOrderDetailService;
    final IItemSkuService iItemSkuService;
    final StockOutOrderBizService stockOutOrderBizService;
    final StockInOrderBizService stockInOrderBizService;
    final ProviderGateway providerGateway;
    final IPaymentOrderWriteOffLogService iPaymentOrderWriteOffLogService;
    final IPurchasePayableOrderService iPurchasePayableOrderService;
    final IPurchaseOrderService iPurchaseOrderService;

    final PlatformTransactionManager transactionManager;

    @Autowired
    public ReverseOldStockOrderProcess(IStockInOrderService iStockInOrderService, IStockOutOrderService iStockOutOrderService,
                                       IStockInOrderDetailService iStockInOrderDetailService, IStockOutOrderDetailService iStockOutOrderDetailService,
                                       IItemSkuService iItemSkuService, StockOutOrderBizService stockOutOrderBizService,
                                       StockInOrderBizService stockInOrderBizService, ProviderGateway providerGateway,
                                       IPaymentOrderWriteOffLogService iPaymentOrderWriteOffLogService, IPurchasePayableOrderService iPurchasePayableOrderService,
                                       IPurchaseOrderService iPurchaseOrderService, PlatformTransactionManager transactionManager) {
        this.iStockInOrderService = iStockInOrderService;
        this.iStockOutOrderService = iStockOutOrderService;
        this.iStockInOrderDetailService = iStockInOrderDetailService;
        this.iStockOutOrderDetailService = iStockOutOrderDetailService;
        this.iItemSkuService = iItemSkuService;
        this.stockOutOrderBizService = stockOutOrderBizService;
        this.stockInOrderBizService = stockInOrderBizService;
        this.providerGateway = providerGateway;
        this.iPaymentOrderWriteOffLogService = iPaymentOrderWriteOffLogService;
        this.iPurchasePayableOrderService = iPurchasePayableOrderService;
        this.iPurchaseOrderService = iPurchaseOrderService;
        this.transactionManager = transactionManager;
    }

    private ProcessRequest processRequest;

    public void setProcessRequest(ProcessRequest processRequest) {
        this.processRequest = processRequest;
    }

    @Override
    public Boolean doProcess() {
        List<HandlerResDto> processResList = new LinkedList<>();
        Boolean resSuccess = doProcess0(processRequest.getPurchaseOrderList(), processRequest.getMockSync(), processResList);
        List<String> traceLogs = processResList.stream().map(HandlerResDto::getTraceLog).collect(Collectors.toList());
        PaymentOrderWriteOffLog stepLog = processRequest.getStepLog();

        // 处理发生异常，事物回滚。
        if (!resSuccess) {
            stepLog.setTraceMsg(StrUtil.join(StrUtil.DOT, traceLogs));
            iPaymentOrderWriteOffLogService.save(stepLog);
            return false;
        }
        // 只有全部处理成功才允许进入下一个处理流程。
        List<HandlerResDto> failList = processResList.stream().filter(val -> Boolean.FALSE.equals(val.getResFlag())).collect(Collectors.toList());
        if (CollUtil.isEmpty(failList)) {
            stepLog.setTraceMsg(StrUtil.join(StrUtil.DOT, traceLogs));
            iPaymentOrderWriteOffLogService.save(stepLog);
            return true;
        }
        List<String> failPurchaseNos = failList.stream().map(HandlerResDto::getNo).collect(Collectors.toList());
        String failNos = StrUtil.join(StrUtil.COMMA, failPurchaseNos);
        try {
            rollback(failPurchaseNos);
            traceLogs.add(StringUtil.format("生成逆向单据失败,回滚处理成功.no:{}", failNos));
            stepLog.setTraceMsg(StrUtil.join(StrUtil.DOT, traceLogs));
            iPaymentOrderWriteOffLogService.save(stepLog);
        } catch (Exception e) {
            log.error("生成逆向单据失败,回滚处理异常.nos:{}", failNos, e);
            traceLogs.add(StringUtil.format("生成逆向单据失败,回滚处理异常.no:{},e:{}", failNos, e.getMessage()));
            stepLog.setTraceMsg(StrUtil.join(StrUtil.DOT, traceLogs));
            iPaymentOrderWriteOffLogService.save(stepLog);
        }
        return false;
    }

    @Data
    private static class HandlerResDto {
        private String no;
        private Boolean resFlag;
        private String traceLog;
    }

    public HandlerResDto buildHandlerResDto(String no, Boolean resFlag, String traceLog) {
        HandlerResDto dto = new HandlerResDto();
        dto.setNo(no);
        dto.setResFlag(resFlag);
        dto.setTraceLog(traceLog);
        return dto;
    }

    private Boolean doProcess0(List<PurchaseOrder> purchaseOrderList, Boolean mockSync, List<HandlerResDto> list) {
        TransactionStatus transactionStatus = transactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            for (PurchaseOrder purchaseOrder : purchaseOrderList) {
                // 判断采购订单是否已经完成了此逆向单据对冲处理流程
                if (purchaseOrder.getHadReverse().equals(1)) {
                    list.add(buildHandlerResDto(purchaseOrder.getNo(), true, "此采购订单逆向单据处理已经完成,跳过处理"));
                    continue;
                }
                String traceLog;
                // 逆向处理入库单,逆向处理出库单
                ReverseDto reverseStockInRes = reverseStockInOrderHandler(purchaseOrder, mockSync);
                ReverseDto reverseStockOutRes = reverseStockOutOrderHandler(purchaseOrder, mockSync);
                // 两种逆向单据都不存在
                if (!reverseStockInRes.getExist() && !reverseStockOutRes.getExist()) {
                    list.add(buildHandlerResDto(purchaseOrder.getNo(), false, "逆向单据处理失败,原单据即不存在已入库的销售入库单,也不存在已出库的退料出库单"));
                    continue;
                }
                // 逆向处理全部成功
                if (reverseStockInRes.getFlag() && reverseStockOutRes.getFlag()) {
                    traceLog = StrUtil.format(
                            "逆向单据处理完成,采购单号:{},逆向出库单处理:[{}],逆向入库单处理:[{}]",
                            purchaseOrder.getNo(), reverseStockOutRes.getTraceLog(), reverseStockInRes.getTraceLog());
                    list.add(buildHandlerResDto(purchaseOrder.getNo(), true, traceLog));
                    purchaseOrder.setHadReverse(1);
                    iPurchaseOrderService.updateById(purchaseOrder);
                    continue;
                }
                // 其余情况均算作失败
                traceLog = StrUtil.format(
                        "逆向单据处理失败,采购单号:{},逆向出库单处理:[{}],逆向入库单处理:[{}]",
                        purchaseOrder.getNo(), reverseStockOutRes.getTraceLog(), reverseStockInRes.getTraceLog());
                list.add(buildHandlerResDto(purchaseOrder.getNo(), false, traceLog));
            }
            transactionManager.commit(transactionStatus);
            return true;
        } catch (Exception e) {
            transactionManager.rollback(transactionStatus);
            String nos = purchaseOrderList.stream().map(PurchaseOrder::getNo).collect(Collectors.joining(StrUtil.COMMA));
            log.error("这批采购订单逆向单据处理异常,采购单号:{}", nos, e);
            HandlerResDto errorDto = buildHandlerResDto(nos, false, StrUtil.format("这批采购订单逆向单据处理异常,采购单号:{}.error:{}", nos, e.getMessage()));
            list.add(errorDto);
            return false;
        }
    }

    /**
     * 失败的情况大概率就是 生成的逆向出入库单同步到金蝶失败了。需要重新同步到金蝶。
     * 找到失败采购单对应的下游单据，出库单状态恢复，出库单下游的应付单状态恢复，逆向入库单删除，逆向入库单生成的应付单删除。
     * 入库单反之同理。
     *
     * @param failPurchaseNos
     */
    public void rollback(List<String> failPurchaseNos) {
        processRequest.getPurchaseOrderList().stream()
                .filter(val -> failPurchaseNos.contains(val.getNo()))
                .forEach(purchaseOrder -> {
                    // 下属作废的入库单恢复状态
                    List<StockInOrder> writeOffStockInList = iStockInOrderService.lambdaQuery()
                            .eq(StockInOrder::getPurchaseOrderId, purchaseOrder.getId())
                            .eq(StockInOrder::getHedgeStatus, StockInState.WRITE_OFF)
                            .list();
                    if (CollUtil.isNotEmpty(writeOffStockInList)) {
                        writeOffStockInList.forEach(val -> {
                            val.setState(StockInState.HAVE_IN.getValue());
                            val.setReverseOutNo(StrUtil.EMPTY);
                        });
                        iStockInOrderService.updateBatchById(writeOffStockInList);
                        // 作废入库单关联的应付单恢复状态
                        List<Long> writeOffStockInIds = writeOffStockInList.stream().map(StockInOrder::getId).collect(Collectors.toList());
                        iPurchasePayableOrderService.lambdaUpdate()
                                .set(PurchasePayableOrder::getStatus, PurchasePayableOrderStatus.DEFAULT)
                                .eq(PurchasePayableOrder::getType, 1)
                                .in(PurchasePayableOrder::getRelatedOrderId, writeOffStockInIds).update();
                        // 入库单逆向冲销出库单删除
                        List<String> reverseStockOutNos = writeOffStockInList.stream().map(StockInOrder::getReverseOutNo).collect(Collectors.toList());
                        iStockOutOrderService.lambdaUpdate().in(StockOutOrder::getNo, reverseStockOutNos).remove();
                        // 入库单逆向冲销出库单关联的应付单删除
                        iPurchasePayableOrderService.lambdaUpdate().in(PurchasePayableOrder::getRelatedOrderNo, reverseStockOutNos).remove();
                    }

                    List<StockOutOrder> writeOffStockOutList = iStockOutOrderService.lambdaQuery()
                            .eq(StockOutOrder::getPurchaseOrderId, purchaseOrder.getId())
                            .eq(StockOutOrder::getHedgeStatus, StockInState.WRITE_OFF)
                            .list();
                    if (CollUtil.isNotEmpty(writeOffStockOutList)) {
                        writeOffStockOutList.forEach(val -> {
                            val.setState(StockInState.HAVE_IN.getValue());
                            val.setReverseInNo(StrUtil.EMPTY);
                        });
                        iStockOutOrderService.updateBatchById(writeOffStockOutList);
                        // 作废入库单关联的应付单恢复状态
                        List<Long> writeOffStockOutIds = writeOffStockOutList.stream().map(StockOutOrder::getId).collect(Collectors.toList());
                        iPurchasePayableOrderService.lambdaUpdate()
                                .set(PurchasePayableOrder::getStatus, PurchasePayableOrderStatus.DEFAULT)
                                .eq(PurchasePayableOrder::getType, 2)
                                .in(PurchasePayableOrder::getRelatedOrderId, writeOffStockOutIds).update();
                        // 入库单逆向冲销出库单删除
                        List<String> reverseStockInNos = writeOffStockOutList.stream().map(StockOutOrder::getReverseInNo).collect(Collectors.toList());
                        iStockOutOrderService.lambdaUpdate().in(StockOutOrder::getNo, reverseStockInNos).remove();
                        // 入库单逆向冲销出库单关联的应付单删除
                        iPurchasePayableOrderService.lambdaUpdate().in(PurchasePayableOrder::getRelatedOrderNo, reverseStockInNos).remove();
                    }
                });

        iPurchaseOrderService.lambdaUpdate()
                .set(PurchaseOrder::getHadReverse, 0)
                .in(PurchaseOrder::getNo, failPurchaseNos)
                .update();
    }


    @Data
    private static class ReverseDto {
        /**
         *
         */
        private String traceLog;

        /**
         *
         */
        private Boolean flag;

        private Boolean exist;
    }

    private ReverseDto reverseStockInOrderHandler(PurchaseOrder purchaseOrder, Boolean mockSync) {
        ReverseDto res = new ReverseDto();
        res.setFlag(true);
        res.setExist(false);

        StockOutOrderCmd stockOutOrderCmd = reverseStockInOrder(purchaseOrder);
        if (Objects.nonNull(stockOutOrderCmd)) {
            res.setExist(true);
            SingleResponse<String> reverseStockOutRes = stockOutOrderBizService.syncStockOutOrder(stockOutOrderCmd, mockSync);
            if (reverseStockOutRes.isSuccess()) {
                String newStockOutOrderNo = reverseStockOutRes.getData();
                // 给需要冲销的旧入库单打上标记
                markOldStockInOrder(stockOutOrderCmd.getReverseOldId(), newStockOutOrderNo);
                // 逆向生成的冲销出库单编码,标记为冲销单据
                markNewStockOutOrder(newStockOutOrderNo);
                processRequest.getSyncStockOutOrderNos().add(newStockOutOrderNo);
                res.setTraceLog(StrUtil.format(
                        "原采购入库单:{},逆向退料出库单:{}", stockOutOrderCmd.getReverseOldNo(), newStockOutOrderNo));
            } else {
                res.setFlag(false);
                res.setTraceLog(StrUtil.format(
                        "原采购入库单:{},生成逆向退料出库单失败,error:{}.", stockOutOrderCmd.getReverseOldNo(), reverseStockOutRes.getErrMessage()));
            }
        }
        return res;
    }

    private ReverseDto reverseStockOutOrderHandler(PurchaseOrder purchaseOrder, Boolean mockSync) {
        ReverseDto res = new ReverseDto();
        res.setFlag(true);
        res.setExist(false);

        StockInOrderAndOrderDetail stockInOrderAndOrderDetail = reverseStockOutOrder(purchaseOrder);
        if (Objects.nonNull(stockInOrderAndOrderDetail)) {
            res.setExist(true);
            SingleResponse<String> reverseStockInRes = stockInOrderBizService.createStockInOrder(stockInOrderAndOrderDetail, true, mockSync);
            if (reverseStockInRes.isSuccess()) {
                String newStockInOrderNo = reverseStockInRes.getData();
                // 给需要冲销的旧出库单打上标记
                markOldStockOutOrder(stockInOrderAndOrderDetail.getReverseOldId(), newStockInOrderNo);
                // 逆向生成的冲销入库单编码,标记为冲销单据
                markNewStockInOrder(newStockInOrderNo);
                processRequest.getSyncStockInOrderNos().add(newStockInOrderNo);
                res.setTraceLog(StrUtil.format(
                        "原退料出库单:{},逆向采购入库单:{}", stockInOrderAndOrderDetail.getReverseOldNo(), newStockInOrderNo));
            } else {
                res.setFlag(false);
                res.setTraceLog(StrUtil.format(
                        "原退料出库单:{},生成逆向采购入库单失败,error:{}.", stockInOrderAndOrderDetail.getReverseOldNo(), reverseStockInRes.getErrMessage()));
            }
        }
        return res;
    }


    private void markOldStockInOrder(Long oldStockInOrderId, String newStockOutOrderNo) {
        iStockInOrderService.lambdaUpdate()
                .set(StockInOrder::getHedgeStatus, StockInState.WRITE_OFF)
                .set(StockInOrder::getReverseOutNo, newStockOutOrderNo)
                .in(StockInOrder::getId, oldStockInOrderId).update();
        iPurchasePayableOrderService.lambdaUpdate().set(PurchasePayableOrder::getStatus, PurchasePayableOrderStatus.WRITE_OFF)
                .eq(PurchasePayableOrder::getType, 1).in(PurchasePayableOrder::getRelatedOrderId, oldStockInOrderId).update();
    }

    private void markOldStockOutOrder(Long oldStockOutOrderId, String newStockInOrderNo) {
        iStockOutOrderService.lambdaUpdate()
                .set(StockOutOrder::getHedgeStatus, StockOutOrderState.WRITE_OFF)
                .set(StockOutOrder::getReverseInNo, newStockInOrderNo)
                .in(StockOutOrder::getId, oldStockOutOrderId).update();
        iPurchasePayableOrderService.lambdaUpdate().set(PurchasePayableOrder::getStatus, PurchasePayableOrderStatus.WRITE_OFF)
                .eq(PurchasePayableOrder::getType, 2).in(PurchasePayableOrder::getRelatedOrderId, oldStockOutOrderId).update();
    }

    private void markNewStockOutOrder(String newStockOutOrderNo) {
        iStockOutOrderService.lambdaUpdate()
                .set(StockOutOrder::getHedgeStatus, StockOutOrderState.REVERSE_FIXED)
                .set(StockOutOrder::getState, StockOutOrderState.STOCK_OUT.getValue())
                .eq(StockOutOrder::getNo, newStockOutOrderNo).update();
        iPurchasePayableOrderService.lambdaUpdate().set(PurchasePayableOrder::getStatus, PurchasePayableOrderStatus.REVERSE_FIXED)
                .eq(PurchasePayableOrder::getType, 2).in(PurchasePayableOrder::getRelatedOrderNo, newStockOutOrderNo).update();
    }

    private void markNewStockInOrder(String newStockInOrderNo) {
        iStockInOrderService.lambdaUpdate()
                .set(StockInOrder::getHedgeStatus, StockInState.REVERSE_FIXED)
                .set(StockInOrder::getState, StockInState.HAVE_IN.getValue())
                .eq(StockInOrder::getNo, newStockInOrderNo).update();
        iPurchasePayableOrderService.lambdaUpdate().set(PurchasePayableOrder::getStatus, PurchasePayableOrderStatus.REVERSE_FIXED)
                .eq(PurchasePayableOrder::getType, 1).in(PurchasePayableOrder::getRelatedOrderNo, newStockInOrderNo).update();
    }


    private StockOutOrderCmd reverseStockInOrder(PurchaseOrder purchaseOrder) {
        List<StockInOrder> stockInOrderList = iStockInOrderService.lambdaQuery()
                .eq(StockInOrder::getPurchaseOrderId, purchaseOrder.getId())
                // 已入库
                .eq(StockInOrder::getState, StockInState.HAVE_IN.getValue())
                // 之前没有冲销痕迹
                .eq(StockInOrder::getHedgeStatus, StockInState.DEFAULT)
                .last("limit 1").list();
        if (CollUtil.isNotEmpty(stockInOrderList)) {
            StockInOrder stockInOrder = stockInOrderList.get(0);
            List<StockInOrderDetail> sList = iStockInOrderDetailService.lambdaQuery().eq(StockInOrderDetail::getStockInOrderId, stockInOrder.getId()).list();
            List<String> itemSkuCodeList = sList.stream().map(StockInOrderDetail::getItemSkuCode).collect(Collectors.toList());
            Map<String, Long> itemCodeAndIdMap = iItemSkuService.lambdaQuery().in(ItemSku::getSkuCode, itemSkuCodeList)
                    .select(ItemSku::getSkuCode, ItemSku::getItemId).list()
                    .stream().collect(Collectors.toMap(ItemSku::getSkuCode, ItemSku::getItemId, (a, b) -> b));
            String remark = "冲销流程,逆向单据.原正向采购入库单:" + stockInOrder.getNo() + ".原正向采购入库单上游采购订单:" + purchaseOrder.getNo();
            // 构建逆向退料出库单参数
            StockOutOrderCmd stockOutOrderCmd = StockOutOrderCmd.ofByStockInOrder(purchaseOrder, itemCodeAndIdMap, sList, remark);
            stockOutOrderCmd.setReverseOldNo(stockInOrder.getNo());
            stockOutOrderCmd.setReverseOldId(stockInOrder.getId());
            return stockOutOrderCmd;
        }

        return null;
    }

    private StockInOrderAndOrderDetail reverseStockOutOrder(PurchaseOrder purchaseOrder) {
        // 此结算单下 正常状态 退料出库单
        List<StockOutOrder> stockOutOrderList = iStockOutOrderService.lambdaQuery()
                .eq(StockOutOrder::getState, StockOutOrderState.STOCK_OUT.getValue())
                .eq(StockOutOrder::getPurchaseOrderId, purchaseOrder.getId())
                .eq(StockOutOrder::getHedgeStatus, StockOutOrderState.DEFAULT)
                .last("limit 1").list();
        if (CollUtil.isNotEmpty(stockOutOrderList)) {
            StockOutOrder stockOutOrder = stockOutOrderList.get(0);
            List<StockOutOrderDetail> stockOutOrderDetailList = iStockOutOrderDetailService.lambdaQuery().eq(StockOutOrderDetail::getStockOutOrderId, stockOutOrder.getId()).list();

            String remark = "冲销流程,逆向单据.原正向退料出库单:" + stockOutOrder.getNo() + ".原正向退料出库单上游采购订单:" + purchaseOrder.getNo();
            StockInOrderAndOrderDetail stockInOrderAndOrderDetail = StockInOrderAndOrderDetail.ofStockOutOrder(
                    purchaseOrder,
                    stockOutOrder,
                    stockOutOrderDetailList,
                    providerGateway
                            .partnerQueryByProviderId(purchaseOrder.getProviderId())
                            .orElse(null), remark);
            stockInOrderAndOrderDetail.setReverseOldNo(stockOutOrder.getNo());
            stockInOrderAndOrderDetail.setReverseOldId(stockOutOrder.getId());
            return stockInOrderAndOrderDetail;
        }
        return null;
    }


}
