package com.daddylab.supplier.item.application.refundStockInOrder;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.StockinRefundAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.StockinRefundQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.StockinRefundQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.StockinRefundQueryWithDetailResponse.Order.Details.RefundOrderDetail;
import com.daddylab.supplier.item.common.trans.TimeCompatibilityTransMapper;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundStockInOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundStockInOrderDetails;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundStockInOrderDetailsRefundOrderDetails;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.google.common.collect.Lists;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/8/18
 */
@Component
@Slf4j
public class WdtRefundStockInOrderFetcher implements PageFetcher {

    private final WdtGateway wdtGateway;
    private final WdtRefundStockInOrderRepository wdtRefundStockInOrderRepository;
    private final WdtConfig wdtConfig;


    public WdtRefundStockInOrderFetcher(WdtGateway wdtGateway,
            WdtRefundStockInOrderRepository wdtRefundStockInOrderRepository, WdtConfig wdtConfig) {
        this.wdtGateway = wdtGateway;
        this.wdtRefundStockInOrderRepository = wdtRefundStockInOrderRepository;
        this.wdtConfig = wdtConfig;
    }

    @Override
    public int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
        return query(startTime, endTime, 1, 1, true).getTotalCount();
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, long pageIndex, long pageSize,
            RunContext runContext) {
        final Duration duration = LocalDateTimeUtil.between(startTime, endTime);
        if (duration.isNegative() || duration.isZero()
                || duration.compareTo(Duration.ofHours(1)) > 0) {
            throw new IllegalArgumentException("拉取时间最大范围为一个小时");
        }
        final StockinRefundQueryWithDetailResponse response = query(startTime, endTime,
                ((int) pageSize),
                ((int) pageIndex), false);
        handleResponse(response);
    }


    private StockinRefundQueryWithDetailResponse query(
            LocalDateTime startTime, LocalDateTime endTime, int pageSize, int pageNo,
            boolean calcTotal) {
        final StockinRefundQueryWithDetailResponse response = new StockinRefundQueryWithDetailResponse();
        AtomicInteger totalCount = new AtomicInteger();
        List<StockinRefundQueryWithDetailResponse.Order> orders = Collections.synchronizedList(
                Lists.newArrayList());
        CompletableFuture<?>[] futures = new CompletableFuture[wdtConfig.getConfigs().size()];
        for (int i = 0; i < wdtConfig.getConfigs().size(); i++) {
            int configIdx = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                try {
                    final Pager pager = new Pager(pageSize, pageNo - 1, calcTotal);
                    final StockinRefundAPI api = wdtGateway.getQimenAPI(StockinRefundAPI.class,
                            configIdx, true);
                    final StockinRefundQueryWithDetailParams params = new StockinRefundQueryWithDetailParams();
                    params.setStartTime(DateUtil.format(startTime));
                    params.setEndTime(DateUtil.format(endTime));
                    //过滤掉"编辑中"状态的入库单（因为目前生产账号数据中存在一条状态是"编辑中"的异常数据，查询到这条数据接口
                    //会报错，因为"编辑中"状态的单据不太需要，所以这边直接过滤掉以解决这个问题）
                    params.setStatus("10,30,37,40,60,70,80");
                    final StockinRefundQueryWithDetailResponse stockinRefundQueryWithDetailResponse = api.queryWithDetail(
                            params, pager);
                    if (CollectionUtil.isEmpty(stockinRefundQueryWithDetailResponse.getOrder())) {
                        return;
                    }
                    orders.addAll(stockinRefundQueryWithDetailResponse.getOrder());
                    totalCount.addAndGet(stockinRefundQueryWithDetailResponse.getTotalCount());
                } catch (Throwable e) {
                    throw WdtExceptions.wrapFetchException(e, fetchDataType());
                }
            });
        }
        CompletableFuture.allOf(futures).join();
        response.setOrder(orders);
        response.setTotalCount(totalCount.intValue());
        return response;
    }

    private void handleResponse(StockinRefundQueryWithDetailResponse response) {
        if (CollectionUtil.isEmpty(response.getOrder())) {
            return;
        }

        final List<WdtRefundStockInOrder> wdtRefundStockInOrders = response.getOrder().stream()
                .map(Assembler.INST::orderToPo).collect(Collectors.toList());

        for (WdtRefundStockInOrder wdtRefundStockInOrder : wdtRefundStockInOrders) {
            wdtRefundStockInOrderRepository.saveOrUpdateWdtPreStockInOrderWithDetails(
                    wdtRefundStockInOrder);
        }
    }


    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.WDT_REFUND_STOCK_IN_ORDER;
    }

    /**
     * <AUTHOR>
     * @since 2022/4/18
     */
    @Mapper(uses = {TimeCompatibilityTransMapper.class})
    public interface Assembler {

        Assembler INST = Mappers.getMapper(Assembler.class);

        @Mapping(target = "id", ignore = true)
        WdtRefundStockInOrder orderToPo(StockinRefundQueryWithDetailResponse.Order data);

        @Mapping(target = "snList", ignore = true)
        @Mapping(target = "id", ignore = true)
        @Mapping(target = "refundOrderDetailList", expression = "java("
                + "refundOrderDetailListToWdtRefundStockInOrderDetailsRefundOrderDetailsList("
                + "orderDetail, orderDetail.getRefundOrderDetailList()"
                + "))")
        WdtRefundStockInOrderDetails orderDetailsToPo(
                StockinRefundQueryWithDetailResponse.Order.Details orderDetail);

        default List<WdtRefundStockInOrderDetailsRefundOrderDetails> refundOrderDetailListToWdtRefundStockInOrderDetailsRefundOrderDetailsList(
                StockinRefundQueryWithDetailResponse.Order.Details orderDetails,
                List<RefundOrderDetail> list) {
            if (list == null) {
                return null;
            }

            List<WdtRefundStockInOrderDetailsRefundOrderDetails> list1 = new ArrayList<WdtRefundStockInOrderDetailsRefundOrderDetails>(
                    list.size());
            for (RefundOrderDetail refundOrderDetail : list) {
                list1.add(orderDetailsRefundOrderDetailToPo(orderDetails, refundOrderDetail));
            }

            return list1;
        }

        @Mapping(target = "stockinId", source = "orderDetail.stockinId")
        @Mapping(target = "specNo", source = "refundOrderDetail.specNo")
        @Mapping(target = "id", ignore = true)
        WdtRefundStockInOrderDetailsRefundOrderDetails orderDetailsRefundOrderDetailToPo(
                StockinRefundQueryWithDetailResponse.Order.Details orderDetail,
                StockinRefundQueryWithDetailResponse.Order.Details.RefundOrderDetail refundOrderDetail);


    }
}
