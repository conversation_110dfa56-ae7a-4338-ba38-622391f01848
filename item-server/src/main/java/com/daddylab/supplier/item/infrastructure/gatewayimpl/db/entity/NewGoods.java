package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffInclude;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 新品商品
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class NewGoods implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * sku编码
     */
    private Long itemId;

    /**
     * sku编码
     */
    private String skuCode;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    @DiffInclude
    private String name;

    /**
     * @deprecated 废弃，从后端商品表中取
     * 品牌id
     */
    private Long brandId;

    /**
     * 产品负责人id
     */
    @ApiModelProperty("产品负责人")
    @DiffInclude
    private Long principalId;

    /**
     * 法务负责人id
     */
    @ApiModelProperty("法务负责人")
    @DiffInclude
    private Long legalId;

    /**
     * @deprecated 废弃，取关联上新计划中的上新时间
     * 预计上架日期
     */
    private Long shelfTime;

    /**
     * 新品活动开始时间
     */
    @ApiModelProperty("新品活动开始时间")
    @DiffInclude
    private Long activePeriodStart;

    /**
     * 新品活动结束时间
     */
    @ApiModelProperty("新品活动结束时间")
    @DiffInclude
    private Long activePeriodEnd;

    /**
     * 是否长期有效
     */
    @ApiModelProperty("是否长期有效")
    @DiffInclude
    private Boolean isLongTerm;

    /**
     * 产品划线价
     */
    @ApiModelProperty("产品划线价")
    @DiffInclude
    private BigDecimal linePrice;

    /**
     * 产品日销价
     */
    @ApiModelProperty("产品日销价")
    @DiffInclude
    private BigDecimal dailyPrice;

    /**
     * 产品活动价
     */
    @ApiModelProperty("产品活动价")
    @DiffInclude
    private BigDecimal activePrice;

    /**
     * 新品活动内容
     */
    @ApiModelProperty("新品活动内容")
    @DiffInclude
    private String activeContent;

    /**
     * 直播活动机制
     */
    @ApiModelProperty("直播活动机制")
    @DiffInclude
    private String liveActive;


    /**
     * 是否参与满减 0:是 1:否
     */
    @ApiModelProperty("是否参与满减")
    @DiffInclude
    private Integer isReduce;

    /**
     * 是是否可叠券 0:是 1:否
     */
    @ApiModelProperty("是否可叠券")
    @DiffInclude
    private Integer isCoupon;

    /**
     * 渠道活动最低价
     */
    @ApiModelProperty("渠道活动最低价")
    @DiffInclude
    private BigDecimal channelLowest;

    /**
     * 发货类型 1:工厂发货 2:仓库发货
     */
    @ApiModelProperty("发货类型")
    @DiffInclude
    private Integer shipmentType;

    /**
     * 发货地
     */
    @ApiModelProperty("发货地")
    @DiffInclude
    private String shipmentArea;

    /**
     * 48小时发货 0:是 1:否
     */
    @ApiModelProperty("48小时发货")
    @DiffInclude
    private Integer shipmentAging;

    /**
     * 物流
     */
    @ApiModelProperty("物流")
    @DiffInclude
    private String logistics;

    /**
     * 快递模板
     */
    @ApiModelProperty("快递模板")
    @DiffInclude
    private String expressTemplate;


    /**
     * 是否支持7天无理由退换 0:是 1:否
     */
    @ApiModelProperty("是否支持7天无理由退换")
    @DiffInclude
    private Integer noReason;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @DiffInclude
    private String remark;

    /**
     * 日常活动
     */
    @ApiModelProperty("日常活动")
    @DiffInclude
    private String dailyActivities;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除，不能做业务。这是软删除标记
     */
    @TableLogic
    private Integer isDel;

    /**
     * 单买到手价
     */
    @ApiModelProperty("单买到手价")
    @DiffInclude
    private BigDecimal singleBuyPrice;

    /**
     * 运营意见反馈
     */
    @ApiModelProperty("运营意见反馈")
    @DiffInclude
    private String runFeedback;

    @ApiModelProperty("A级活动售价")
    @DiffInclude
    private String aLevelActivityPrice;

    @ApiModelProperty("A级活动赠品")
    @DiffInclude
    private String aLevelActivityGift;

    @ApiModelProperty("A级活动直播价")
    @DiffInclude
    private String aLevelActivityLivePrice;

    @ApiModelProperty("A级活动直播赠品")
    @DiffInclude
    private String aLevelActivityLiveGift;

    @ApiModelProperty("S级大促售价")
    @DiffInclude
    private String sLevelPromotePrice;

    @ApiModelProperty("S级大促机制")
    @DiffInclude
    private String sLevelPromoteRule;

    @ApiModelProperty("S级大促直播价")
    @DiffInclude
    private String sLevelPromoteLivePrice;

    @ApiModelProperty("S级大促直播机制")
    @DiffInclude
    private String sLevelPromoteLiveRule;


}
