package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtRefundOrderService;
import java.util.Collections;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 旺店通退换单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Service
public class WdtRefundOrderServiceImpl extends DaddyServiceImpl<WdtRefundOrderMapper, WdtRefundOrder> implements IWdtRefundOrderService {

    @Override
    public List<WdtRefundOrder> listByTid(String tid) {
        return getBaseMapper().listByTid(tid);
    }
}
