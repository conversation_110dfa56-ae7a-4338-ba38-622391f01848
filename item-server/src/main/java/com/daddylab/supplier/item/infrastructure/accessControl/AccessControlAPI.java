package com.daddylab.supplier.item.infrastructure.accessControl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.DlabConfiguration;
import com.daddylab.supplier.item.infrastructure.goclient.Rows;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "AccessControlAPI", url = "${ms-access-control.url}", configuration = DlabConfiguration.class,
        fallbackFactory = AccessControlFallbackFactory.class)
public interface AccessControlAPI {
    /**
     * 权限策略查询
     * @param systemId 系统ID
     */
    @GetMapping(path = "/internal/casbin/policy")
    Rsp<List<CasbinPolicy>> casbinPolicies(@RequestParam("domain") long systemId);

    /**
     * 系统资源查询
     * @param systemId 系统ID
     */
    @GetMapping(path = "/internal/resources")
    Rsp<List<Resource>> resourcesQuery(@RequestParam("system_id") long systemId);

    /**
     * 部门查询
     */
    @GetMapping(path = "/internal/dept/list")
    Rsp<List<Dept>> dept();

    /**
     * 用户在权限系统中登记的员工信息查询（角色、岗位）
     * @param query 查询参数封装
     */
    @GetMapping(path = "/internal/user/paged_list")
    Rsp<Rows<StaffAcInfo>> staffAcInfoPageQuery(@SpringQueryMap StaffAcInfoPageQuery query);

}
