package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Buyer;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-06
 */
public interface BuyerMapper extends DaddyBaseMapper<Buyer> {


    String getKingDeeIdByItemId(@Param("itemId") Long itemId);

    @Select("select buyer_id from item_procurement where item_id = \n" +
            "(select item_id from item_sku where sku_code = #{skuCode} and is_del = 0)\n" +
            "and is_del = 0;")
    Long getBuyerIdBySkuCode(@Param("skuCode") String skuCode);

    @Update("update item_procurement set buyer_id = #{targetBuyerDbId} where item_id = \n" +
            "(select item_id from item_sku where sku_code = #{skuCode} and is_del = 0);")
    int updateBuyerBySkuCode(@Param("targetBuyerDbId") Long targetBuyerDbId, @Param("skuCode") String skuCode);

}
