package com.daddylab.supplier.item.application.message.wechat;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> up
 * @date 2022/6/4 11:09 上午
 */
@Getter
@AllArgsConstructor
public enum RemindType implements IEnum<Integer> {
    /**
     * 消除黄线
     * log 2022-11-20。全部都改成了立即提醒。
     */
    TO_DO_REMINDER(1, "待办提醒"),
    CHANGE_NOTICE(2, "信息修改"),
    IMMEDIATELY_REMINDER(3, "立即提醒"),
    WEBHOOK(4, "群机器人"),
    ;
    
    private final Integer value;
    private final String desc;
}
