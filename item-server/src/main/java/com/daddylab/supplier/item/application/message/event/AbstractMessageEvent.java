package com.daddylab.supplier.item.application.message.event;

import com.daddylab.supplier.item.domain.message.dto.MsgFillObj;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessageOperationType;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/27 11:55 上午
 * @description
 */
public abstract class AbstractMessageEvent {

    /**
     * 根据事件，获取对外链接。
     *
     * @return
     */
    public abstract String getPushLink();

    /**
     * 根据事件，构建消息模板填充内容
     *
     * @return
     */
    public abstract List<MsgFillObj> getFillObjList();

    public MessageOperationType operationType;

    public MessageOperationType getOperationType() {
        return operationType;
    }

    public void setOperationType(MessageOperationType operationType) {
        this.operationType = operationType;
    }

    public abstract String getEmailSubject();

}
