package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSyncLogDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemSyncLogDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSyncLogDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品同步日志明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class ItemSyncLogDetailServiceImpl extends DaddyServiceImpl<ItemSyncLogDetailMapper, ItemSyncLogDetail> implements IItemSyncLogDetailService {

}
