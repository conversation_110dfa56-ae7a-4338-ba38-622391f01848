package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.application.afterSales.AfterSalesConfirmDetailInfoVO;
import com.daddylab.supplier.item.application.afterSales.AfterSalesConfirmInfoVO;
import com.daddylab.supplier.item.application.afterSales.AfterSalesConfirmInfoWithDetailsVO;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesConfirm;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesConfirmDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AfterSalesConfirmMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesConfirmDetailService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesConfirmService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 售后单销退确认单据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
@Service
public class AfterSalesConfirmServiceImpl extends
        DaddyServiceImpl<AfterSalesConfirmMapper, AfterSalesConfirm> implements
        IAfterSalesConfirmService {

    @Resource
    private IAfterSalesConfirmDetailService afterSalesConfirmDetailService;

    @Resource(name = "UserGatewayCacheImpl")
    private UserGateway userGateway;

    @Override
    public Optional<AfterSalesConfirmInfoVO> getAfterSalesConfirmInfoVO(String refundOrderNo) {
        final Optional<AfterSalesConfirm> afterSalesConfirmOptional = lambdaQuery().eq(
                AfterSalesConfirm::getReturnOrderNo, refundOrderNo).oneOpt();
        if (!afterSalesConfirmOptional.isPresent()) {
            return Optional.empty();
        }
        final AfterSalesConfirm afterSalesConfirm = afterSalesConfirmOptional.get();
        final AfterSalesConfirmInfoVO afterSalesConfirmInfoVO = new AfterSalesConfirmInfoVO();
        final Long createdUid = afterSalesConfirm.getCreatedUid();
        final Optional<StaffInfo> staffInfoOptional = Optional.ofNullable(
                userGateway.queryStaffInfoById(createdUid));
        afterSalesConfirmInfoVO.setConfirmUid(createdUid);
        afterSalesConfirmInfoVO.setConfirmUserNick(
                staffInfoOptional.map(StaffInfo::getNickname).orElse(""));
        afterSalesConfirmInfoVO.setConfirmUserName(
                staffInfoOptional.map(StaffInfo::getUserName).orElse(""));
        afterSalesConfirmInfoVO.setConfirmTime(afterSalesConfirm.getCreatedAt());
        afterSalesConfirmInfoVO.setRemark(afterSalesConfirm.getRemark());
        return Optional.of(afterSalesConfirmInfoVO);
    }

    @Override
    public Optional<AfterSalesConfirmInfoWithDetailsVO> getAfterSalesConfirmInfoWithDetailsVO(
            String refundOrderNo) {
        final Optional<AfterSalesConfirmInfoVO> afterSalesConfirmInfoVO = getAfterSalesConfirmInfoVO(
                refundOrderNo);
        if (!afterSalesConfirmInfoVO.isPresent()) {
            return Optional.empty();
        }
        final AfterSalesConfirmInfoWithDetailsVO afterSalesConfirmInfoWithDetailsVO = new AfterSalesConfirmInfoWithDetailsVO();
        afterSalesConfirmInfoWithDetailsVO.setConfirmInfo(afterSalesConfirmInfoVO.get());
        final List<AfterSalesConfirmDetailInfoVO> detailVOs = getAfterSalesConfirmDetailInfoVOs(
                refundOrderNo);
        afterSalesConfirmInfoWithDetailsVO.setDetails(detailVOs);
        return Optional.of(afterSalesConfirmInfoWithDetailsVO);
    }

    @Override
    public List<AfterSalesConfirmDetailInfoVO> getAfterSalesConfirmDetailInfoVOs(
            String refundOrderNo) {
        final ArrayList<AfterSalesConfirmDetailInfoVO> detailVOs = Lists.newArrayList();
        final List<AfterSalesConfirmDetail> detailPOs = afterSalesConfirmDetailService.lambdaQuery()
                .eq(AfterSalesConfirmDetail::getReturnOrderNo, refundOrderNo).list();
        for (AfterSalesConfirmDetail detailPO : detailPOs) {
            final AfterSalesConfirmDetailInfoVO afterSalesConfirmDetailInfoVO = new AfterSalesConfirmDetailInfoVO();
            afterSalesConfirmDetailInfoVO.setRecId(detailPO.getRecId());
            afterSalesConfirmDetailInfoVO.setGoodsNo(detailPO.getGoodsNo());
            afterSalesConfirmDetailInfoVO.setGoodsName(detailPO.getGoodsName());
            afterSalesConfirmDetailInfoVO.setSpecName(detailPO.getSpecName());
            afterSalesConfirmDetailInfoVO.setSpecNo(detailPO.getSpecNo());
            afterSalesConfirmDetailInfoVO.setQuantity(detailPO.getQuantity());
            afterSalesConfirmDetailInfoVO.setUndertakeType(detailPO.getUndertakeType());
            afterSalesConfirmDetailInfoVO.setUndertakeAmount(detailPO.getUndertakeAmount());
            detailVOs.add(afterSalesConfirmDetailInfoVO);
        }
        return detailVOs;
    }
}
