package com.daddylab.supplier.item.application.afterSales;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.refundOrder.RefundOrderService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderBaseInfo;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderQuery;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/10/21
 */
@Slf4j
@Service
@AllArgsConstructor
public class AfterSalesAuthBizServiceImpl implements AfterSalesAuthBizService {

    private final RefundOrderService refundOrderService;
    private final AfterSalesWarehouseBizService afterSalesWarehouseBizService;

    @Override
    public SingleResponse<Boolean> isAllowAccessWarehouse(Long partnerProviderId,
            String warehouseNo) {
        return SingleResponse.of(
                afterSalesWarehouseBizService.isPartnerProviderWarehouse(partnerProviderId,
                        warehouseNo));
    }

    @Override
    public SingleResponse<Boolean> isAllowAccessRefundOrder(Long partnerProviderId,
            String refundOrderNo) {
        final RefundOrderQuery query = new RefundOrderQuery();
        query.setRefundOrderNo(refundOrderNo);
        final PageResponse<RefundOrderBaseInfo> refundOrderBaseInfoPageResponse = refundOrderService.refundOrderQuery(
                query);
        if (!refundOrderBaseInfoPageResponse.isSuccess()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR);
        }
        final List<RefundOrderBaseInfo> data = refundOrderBaseInfoPageResponse.getData();
        final Optional<RefundOrderBaseInfo> refundOrderBaseInfoOptional;
        if (refundOrderBaseInfoPageResponse.isEmpty()
                || !(refundOrderBaseInfoOptional = data.stream().findAny()).isPresent()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "无效的退换单号");
        }
        final RefundOrderBaseInfo refundOrderBaseInfo = refundOrderBaseInfoOptional.get();
        return isAllowAccessWarehouse(partnerProviderId, refundOrderBaseInfo.getReturnWarehouseNo());
    }
}
