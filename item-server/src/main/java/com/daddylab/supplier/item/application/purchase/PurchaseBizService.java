package com.daddylab.supplier.item.application.purchase;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseCmd;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseQueryPage;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseVo;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseDetail;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseItem;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseOperate;

import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName PurchaseBizService.java
 * @description 采购确认
 * @createTime 2021年11月11日 14:53:00
 */
public interface PurchaseBizService {

    /**
     * 查询采购列表
     *
     * @param purchaseQueryPage
     * @return
     */
    PageResponse<PurchaseVo> queryPage(PurchaseQueryPage purchaseQueryPage);

    /**
     * 删除采购
     *
     * @param id
     */
    Response delete(Long id);

    /**
     * 修改采购
     *
     * @param cmd
     */
    Response createOrUpdatePurchase(PurchaseCmd cmd);

    /**
     * 采购排序
     *
     * @param ids
     */
    Response doSort(List<Long> ids);

    /**
     * 查看采购详情
     *
     * @param purchaseId
     * @return
     */
    MultiResponse<PurchaseDetail> getPurchaseDetail(Long purchaseId);

    /**
     * 查看待选采购详情
     *
     * @param ids
     * @return
     */
    MultiResponse<PurchaseDetail> getStayConfirm(String ids);

    /**
     * 发起供应商确认
     *
     * @param ids
     */
    Response initiate(List<Long> ids);

    /**
     * 查看凭证
     *
     * @param id
     * @return
     */
    SingleResponse<PurchaseDetail> getAccess(Long id);

    /**
     * 根据加密码显示供应商页面
     *
     * @param code
     * @return
     */
    MultiResponse<PurchaseDetail> getDetailByCode(String code, String token);

    /**
     * 重置修改
     *
     * @param id
     */
    Response reset(Long id);


    /**
     * 导出
     */
    long exportExcel(PurchaseQueryPage purchaseQueryPage);

    /**
     * @return 返回Excel模板链接
     */
    String getExcelTemplateUrl();

    /**
     * 导入
     */
    PurchaseOperate importExcel(InputStream inputStream);

    /**
     * 根据id查询采购数据
     *
     * @param id
     * @return
     */
    void updateStatus(Long id);

    /**
     * 价格达成协议
     *
     * @param id
     */
    Response agree(String token, Long id, String code);

    /**
     * 根据加密码获取供应商手机号
     *
     * @param code
     * @return
     */
    SingleResponse<String> getMobileByCode(String code);


    /**
     * 发验证码
     *
     * @param code
     * @return
     */
    Response getSmsCode(String code);

    /**
     * 校验验证码
     *
     * @param code
     * @param verifyCode
     * @return
     */
    SingleResponse<String> checkCode(String code, String verifyCode);

    /**
     * 处理上个月的纯活动商品
     */
    void dealActiveItem();

    /**
     * 校验token
     */
    Response checkToken(String token, String code);

    /**
     * 查看月度供应商列表
     *
     * @param month
     * @return
     */
    MultiResponse<String> getProviderList(String month, String provider);

    /**
     * 根据商品sku查相关数据
     *
     * @param itemSku
     * @return
     */
    SingleResponse<PurchaseItem> getDataByItemSku(String itemSku);

    /**
     * 清洗历史数据的合作模式字段值，根据商品进行关联
     *
     * @return
     */
    SingleResponse<Boolean> clearHistoryDataBusinessLine();

    /**
     * 当sku/组合装的业务模式发生变化的时候，联动采购活动价的数据更新。
     *
     * @param code            sku编码或者组合装编码
     * @param newBusinessLine 新的业务模式
     */
//    void checkBusinessLineUpdate(String code, Integer newBusinessLine);


}
