package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 大数据订单隐私数据
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AdsTidbEcpUsrPhoneInfoPd implements Serializable {

    private static final long serialVersionUID = 1L;

    //订单号
    @TableId
    private String orderId;
    //收货手机号
    private String phoneId;
    //店铺名称
    private String shopName;
    //平台名称
    private String platName;
    //收货人姓名
    private String receiverName;
    //收货详细地址
    private String receiverAddr;
    //更新日期
    private String ingestionDate;

}
