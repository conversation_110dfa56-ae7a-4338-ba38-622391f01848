package com.daddylab.supplier.item.application.offShelf.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class OffShelfBatchProcessFeedbackCmd {

    /** 下架反馈 1已下架 2未下架 3没有上架 */
    @NotNull(message = "请选择下架反馈")
    private Integer feedback;

    /** 备注说明 */
    private String remark;

    @ApiModelProperty(value = "商品列表")
    @NotEmpty
    List<OffShelfBatchProcessFeedbackItem> itemList;
}
