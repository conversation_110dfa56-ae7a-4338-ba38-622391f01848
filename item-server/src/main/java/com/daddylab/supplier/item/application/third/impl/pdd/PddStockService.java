package com.daddylab.supplier.item.application.third.impl.pdd;

/**
 * <AUTHOR>
 * @since 2024/4/18
 */
public interface PddStockService {
    /**
     *
     * @param shopNo 店铺
     * @param goodsId 商品ID
     * @param skuId SKU ID
     * @param quantity 库存修改值。当全量更新库存时，quantity必须为大于等于0的正整数；当增量更新库存时，quantity为整数，可小于等于0。若增量更新时传入的库存为负数，则负数与实际库存之和不能小于0。比如当前实际库存为1，传入增量更新quantity=-1，库存改为0
     * @param updateType 库存更新方式，可选。1为全量更新，2为增量更新。如果不填，默认为全量更新
     */
    void goodsQuantityUpdate(String shopNo, Long goodsId, Long skuId, Long quantity, Integer updateType);
}
