package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemOptimizeParticipant;
import com.daddylab.supplier.item.types.itemOptimize.ParticipantType;

import java.util.List;

/**
 * 商品优化参与者 服务类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface IItemOptimizeParticipantService extends IDaddyService<ItemOptimizeParticipant> {

    boolean addParticipants(
            Long itemOptimizeId, List<Long> userIds, ParticipantType participantType);

    boolean removeParticipants(
            Long itemOptimizeId, List<Long> userIds, ParticipantType participantType);
}
