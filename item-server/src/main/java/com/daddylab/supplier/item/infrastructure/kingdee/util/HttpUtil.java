package com.daddylab.supplier.item.infrastructure.kingdee.util;

import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum.*;

/**
 * <AUTHOR> up
 * @date 2022/4/9 4:03 下午
 */
@Slf4j
@Component
public class HttpUtil {

    @Autowired
    ReqTemplate reqTemplate;

    public static final Map<ApiEnum, String> METHOD_MAP = new HashMap<>(16);

    static {
        METHOD_MAP.put(GROUP_SAVE_CATEGORY, "groupSave");
        METHOD_MAP.put(GROUP_DELETE_CATEGORY, "groupDelete");

        METHOD_MAP.put(SAVE_SHOP, "save");
        METHOD_MAP.put(SAVE_PROVIDER, "save");
        METHOD_MAP.put(SAVE_SKU, "save");
        METHOD_MAP.put(SAVE_STOCK_IN_ORDER, "save");
        METHOD_MAP.put(SAVE_STOCK_OUT_ORDER, "save");

        METHOD_MAP.put(UPDATE_SHOP, "save");
        METHOD_MAP.put(UPDATE_PROVIDER, "save");
        METHOD_MAP.put(UPDATE_SKU, "save");
        METHOD_MAP.put(UPDATE_STOCK_IN_ORDER, "save");
        METHOD_MAP.put(UPDATE_STOCK_OUT_ORDER, "save");

        METHOD_MAP.put(DELETE_PROVIDER, "delete");
        METHOD_MAP.put(DELETE_STOCK_IN_ORDER, "delete");
        METHOD_MAP.put(DELETE_STOCK_OUT_ORDER, "delete");
    }

    public Object invoke(ApiEnum apiEnum, String json) throws Exception {
        if (apiEnum.name().startsWith("SAVE") || apiEnum.name().startsWith("UPDATE")) {
            return reqTemplate.save(json);
        }
        if (apiEnum.name().startsWith("GROUP_SAVE")) {
            return reqTemplate.groupSave(json);
        }
        if (apiEnum.name().startsWith("GROUP_DELETE")) {
            return reqTemplate.groupDelete(json);
        }
        if (apiEnum.name().startsWith("DELETE")) {
            return reqTemplate.delete(json);
        }
        return null;
    }


}
