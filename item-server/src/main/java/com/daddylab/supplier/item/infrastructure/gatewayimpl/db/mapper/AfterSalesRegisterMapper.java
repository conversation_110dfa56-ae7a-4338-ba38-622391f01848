package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesRegister;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;

/**
 * <p>
 * 客服售后登记表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
public interface AfterSalesRegisterMapper extends DaddyBaseMapper<AfterSalesRegister> {

}
