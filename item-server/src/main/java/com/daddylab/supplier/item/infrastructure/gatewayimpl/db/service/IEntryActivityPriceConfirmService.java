package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceConfirm;

/**
 * <p>
 * 入驻活动价格确认 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-06
 */
public interface IEntryActivityPriceConfirmService extends IDaddyService<EntryActivityPriceConfirm> {

}
