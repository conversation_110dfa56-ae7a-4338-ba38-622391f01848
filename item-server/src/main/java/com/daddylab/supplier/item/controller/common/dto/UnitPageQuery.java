package com.daddylab.supplier.item.controller.common.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> up
 * @date 2022/3/24 6:25 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("基础单位查询参数")
public class UnitPageQuery extends Command {

    private static final long serialVersionUID = -3930282615859250923L;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("状态。1.正常，2.停用")
    private Integer state;

    @ApiModelProperty("id")
    private Long id;

}
