package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.S03Order;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 抖店订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@DS("rpaProd")
public interface S03OrderMapper extends DaddyBaseMapper<S03Order> {


    List<String> selectBuyerName(@Param("name") String name, @Param("size") Integer size);

}
