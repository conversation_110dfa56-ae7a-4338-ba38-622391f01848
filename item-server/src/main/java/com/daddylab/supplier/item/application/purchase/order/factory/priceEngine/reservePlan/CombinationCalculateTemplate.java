package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.reservePlan;

import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;

/**
 * 组合价计算模板
 * 有sku纬度的组合，有spu纬度的组合。
 *
 * <AUTHOR> up
 * @date 2023年09月27日 5:13 PM
 */
public abstract class CombinationCalculateTemplate {

    /**
     * 考虑活动组合价
     *
     * @param timeBO
     */
    protected abstract void considerActivity(TimeBO timeBO);

    /**
     * 考虑日常组合价
     *
     * @param timeBO
     */
    protected abstract void considerDaily(TimeBO timeBO);

}
