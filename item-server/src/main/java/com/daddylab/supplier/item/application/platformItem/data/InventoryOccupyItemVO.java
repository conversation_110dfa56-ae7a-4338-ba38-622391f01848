package com.daddylab.supplier.item.application.platformItem.data;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class InventoryOccupyItemVO {
    private Long inventoryAllocId;
    private Long platformItemId;
    private Long platformItemSkuId;
    private String outerItemId;
    private String outerSkuId;
    private String platformItemName;
    private Platform platform;
    private String skuCode;
    private String outerSkuCode;
    private Boolean isCombinationItem;
    private List<InventoryOccupyItemComposeSkuVO> composeSkus;
    private Integer syncStock;
    private Integer totalStock;
    private Boolean lockEnabled;
    private List<String> calculations;
}
