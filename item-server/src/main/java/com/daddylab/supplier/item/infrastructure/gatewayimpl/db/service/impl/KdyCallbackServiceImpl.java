package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.KdyCallback;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.KdyCallbackMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IKdyCallbackService;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 快刀云订阅记录 服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Service
public class KdyCallbackServiceImpl extends DaddyServiceImpl<KdyCallbackMapper, KdyCallback>
    implements IKdyCallbackService {

  @Override
  public Optional<KdyCallback> selectByLogisticsNo(String logisticsNo) {
    return lambdaQuery()
        .eq(KdyCallback::getLogisticsNo, logisticsNo)
        .orderByDesc(KdyCallback::getId)
        .oneOpt();
  }

  @Override
  public Long selectIdByLogisticsNo(String logisticsNo) {
    return lambdaQuery()
        .select(KdyCallback::getId)
        .eq(KdyCallback::getLogisticsNo, logisticsNo)
        .orderByDesc(KdyCallback::getId)
        .oneOpt()
        .map(KdyCallback::getId)
        .orElse(null);
  }
}
