package com.daddylab.supplier.item.controller.stockout.dto;

import com.alibaba.cola.dto.Command;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 计算剩余可出库数量，请求参数
 *
 * <AUTHOR> up
 * @date 2022/6/24 10:59 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StockOutQuantityCmd extends Command {

    private static final long serialVersionUID = -1171979627227099828L;

    @NotNull
    private Integer isSyncWdt;

    @NotEmpty
    @Valid
    private List<StockCmd> detailVOList;

    @NotNull
    private Long purchaseOrderId;

    @Data
    public static class StockCmd {
        @NotBlank
        private String skuCode;
        @NotBlank
        private String warehouseNo;
        @NotNull
        private Integer isGift;


    }
}
