package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.saleItem.query.NewGoodsQueryPage;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.controller.item.dto.SkuWithNewGoodsDto;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.accessControl.AccessControlAPI;
import com.daddylab.supplier.item.infrastructure.accessControl.StaffAcInfo;
import com.daddylab.supplier.item.infrastructure.accessControl.StaffAcInfoPageQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.NewGoodsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INewGoodsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffListQuery;
import com.daddylab.supplier.item.infrastructure.goclient.Rows;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.types.item.ItemQcProcessorsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 新品商品 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@Service
public class NewGoodsServiceImpl extends DaddyServiceImpl<NewGoodsMapper, NewGoods> implements INewGoodsService {

    @Autowired
    private NewGoodsMapper newGoodsMapper;
    @Autowired
    private AccessControlAPI accessControlAPI;
    @Autowired
    @Qualifier("UserGatewayCacheImpl")
    UserGateway userGateway;

    @Override
    public int countNewGoods(NewGoodsQueryPage queryPage) {
        return newGoodsMapper.count(queryPage);
    }

    @Override
    public List<NewGoods> selectBatchByItemIds(List<Long> itemIds) {
        if (CollectionUtil.isEmpty(itemIds)) {
            return new ArrayList<>();
        }
        QueryWrapper<NewGoods> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(NewGoods::getItemId, itemIds);
        return newGoodsMapper.selectList(queryWrapper);
    }

    @Override
    public List<NewGoods> selectBatchBySkuCodes(Collection<String> skuCodes) {
        if (CollectionUtil.isEmpty(skuCodes)) {
            return new ArrayList<>();
        }
        QueryWrapper<NewGoods> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(NewGoods::getSkuCode, skuCodes);
        return newGoodsMapper.selectList(queryWrapper);
    }

    @Override
    @XxlJob("NewGoodsService::updateResignedPrincipalId")
    public void updateResignedPrincipalId() {
        List<Long> resignedUserIds = getResignedUserIds();
        if (CollectionUtil.isNotEmpty(resignedUserIds)) {
            QueryWrapper<NewGoods> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(NewGoods::getPrincipalId, resignedUserIds);
            List<NewGoods> newGoodsList = newGoodsMapper.selectList(queryWrapper);

            // 查询产品组的小高
            StaffListQuery staffListQuery = new StaffListQuery();
            staffListQuery.setPageIndex(1);
            staffListQuery.setPageSize(1);
            staffListQuery.setNickname("小高");
            staffListQuery.setDept("消费者事业部");
            List<StaffInfo> staffInfos = userGateway.queryStaffList(staffListQuery);
            if (CollectionUtil.isEmpty(staffInfos)) {
                log.warn("找不到消费者事业部的小高，无法更新产品负责人");
                return;
            }
            // 小高的 user_id
            Long userId = staffInfos.get(0).getUserId();

            // 更新
            List<NewGoods> updateNewGoodsList = new ArrayList<>();
            for (NewGoods newGoods : newGoodsList) {
                NewGoods updateNewGoods = new NewGoods();
                updateNewGoods.setId(newGoods.getId());
                updateNewGoods.setPrincipalId(userId);
                updateNewGoodsList.add(updateNewGoods);
            }
            updateBatchById(updateNewGoodsList);
        } else {
            log.warn("没有查询到离职人员");
        }
    }

    @Override
    public Map<String, SkuWithNewGoodsDto> skuCodeToDtoMap(List<String> skuCodes) {
        if (CollectionUtil.isEmpty(skuCodes)) {
            return new HashMap<>(1);
        }
        List<SkuWithNewGoodsDto> dtos = newGoodsMapper.selectDtoBatchBySkuCodes(skuCodes);
        Map<String, SkuWithNewGoodsDto> map = new HashMap<>((int) (dtos.size() / 0.75 + 1));
        for (SkuWithNewGoodsDto dto : dtos) {
            map.put(dto.getSkuCode(), dto);
        }
        return map;
    }

    @Override
    public List<StaffBrief> getQcProcessors(Long itemId) {
        final ItemQcProcessorsVO qcProcessors = newGoodsMapper.getQcProcessors(itemId);
        return StaffAssembler.INST.longListToStaffBriefList(qcProcessors.getQcIds());
    }

    private List<Long> getResignedUserIds() {
        final int pageSize = 49;
        int pageIndex = 0;
        List<StaffAcInfo> staffAcInfosAll = new ArrayList<>();
        while (true) {
            pageIndex++;
            final StaffAcInfoPageQuery query = new StaffAcInfoPageQuery(pageIndex, pageSize);
            query.setStatus(0);
            Rsp<Rows<StaffAcInfo>> rsp = accessControlAPI.staffAcInfoPageQuery(query);
            if (!rsp.isSuccess() || rsp.getData() == null || CollectionUtil.isEmpty(rsp.getData().getRows())) {
                break;
            }
            final List<StaffAcInfo> staffAcInfos = rsp.getData().getRows();
            staffAcInfosAll.addAll(staffAcInfos);
        }
        // 离职人员 user_id
        return staffAcInfosAll.stream()
                .map(StaffAcInfo::getUid)
                .distinct()
                .collect(Collectors.toList());
    }
}
