package com.daddylab.supplier.item.application.order.settlement.dto;

import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderSettlementDetail;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

import static com.daddylab.supplier.item.infrastructure.utils.NumberUtil.convertInt;

/**
 * <AUTHOR> up
 * @date 2023年08月14日 2:36 PM
 */
@Data
public class ImportExcelDo {

    /**
     * 序号
     */
    private String sort;

    /**
     * 结算月份
     * yyyy-MM
     */
    private String cycle;

    /**
     * 商家SKU
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 规格
     */
    private String specifications;

    private String unt;

    /**
     * 单价/结算价
     */
    private String settlementPrice;

    /**
     * 发货数量
     */
    private String deliverQuantity;

    /**
     * 当月退货
     */
    private String currentMonthRefundQuantity;

    /**
     * 跨月退货
     */
    private String crossMonthRefundQuantity;

    /**
     * 结算数量
     */
    private String settlementQuantity;

    /**
     * 结算金额
     */
    private String settlementAmount;

    /**
     * 运费以及售后分摊
     */
    private String transportAndAfterSalesCost;

    /**
     * 最终金额
     */
    private String finalAmount;

    /**
     * 备注
     */
    private String remark;

    private final static String F1 = "~";
    private final static String F2 = "～";


    public static OrderSettlementDetail convert(Long id, ImportExcelDo excelDo) {
        OrderSettlementDetail detail = new OrderSettlementDetail();

        long settlementTime;
        try {
            LocalDateTime localDateTime = DateUtil.parse(excelDo.getCycle(), "yyyy-MM");
            LocalDate localDate = localDateTime.toLocalDate();
            LocalDate firstDay = LocalDate.of(localDate.getYear(), localDate.getMonth(), 1);
            String siftStart = firstDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 00:00:00";
            LocalDateTime start = LocalDateTime.parse(siftStart, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            settlementTime = start.atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
        } catch (Exception e) {
            String error = StrUtil.format("数据日期格式错误。日期:{},序号:{},skuCode:{}",
                    excelDo.getCycle(), excelDo.getSort(), excelDo.getSkuCode());
            throw new RuntimeException(error);
        }

        detail.setFormId(id);
        detail.setSettlementStartDate(settlementTime);
        detail.setSettlementEndDate(settlementTime);
        detail.setSkuCode(excelDo.getSkuCode());
        detail.setDeliverQuantity(StrUtil.isBlank(excelDo.getDeliverQuantity()) ? 0 : convertInt(excelDo.getDeliverQuantity()));
        detail.setCurrentMonthRefundQuantity(StrUtil.isBlank(excelDo.getCurrentMonthRefundQuantity()) ? 0 : convertInt(excelDo.getCurrentMonthRefundQuantity()));
        detail.setCrossMonthRefundQuantity(StrUtil.isBlank(excelDo.getCrossMonthRefundQuantity()) ? 0 : convertInt(excelDo.getCrossMonthRefundQuantity()));
        detail.setSettlementQuantity(StrUtil.isBlank(excelDo.getSettlementQuantity()) ? 0 : convertInt(excelDo.getSettlementQuantity()));
        detail.setSettlementPrice(StrUtil.isBlank(excelDo.getSettlementPrice()) ? BigDecimal.ZERO
                : new BigDecimal(excelDo.getSettlementPrice()));
        detail.setTemporaryQuantity(detail.getSettlementQuantity());
        detail.setTemporaryPrice(detail.getSettlementPrice());
        detail.setSettlementAmount(StrUtil.isBlank(excelDo.getSettlementAmount()) ? BigDecimal.ZERO
                : new BigDecimal(excelDo.getSettlementAmount()));
        detail.setAfterSalesCost(StrUtil.isBlank(excelDo.getTransportAndAfterSalesCost()) ? BigDecimal.ZERO
                : new BigDecimal(excelDo.getTransportAndAfterSalesCost()));
        detail.setFinalAmount(StrUtil.isBlank(excelDo.getFinalAmount()) ? BigDecimal.ZERO
                : new BigDecimal(excelDo.getFinalAmount()));
        detail.setRemark(excelDo.getRemark());
        detail.setSource("excel导入");

        return detail;
    }


    public static void main(String[] args) {
        String cycle = "202308";
        LocalDateTime localDateTime = DateUtil.parse(cycle, "yyyyMM");
        LocalDate localDate = localDateTime.toLocalDate();
        LocalDate firstDay = LocalDate.of(localDate.getYear(), localDate.getMonth(), 1);
        String siftStart = firstDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 00:00:00";
        LocalDateTime start = LocalDateTime.parse(siftStart, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        long startSecond = start.atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
        System.out.println(startSecond);
    }


}
