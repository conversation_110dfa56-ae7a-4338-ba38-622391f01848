package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

import static cn.hutool.core.util.NumberUtil.add;
import static cn.hutool.core.util.NumberUtil.div;
import static com.daddylab.supplier.item.infrastructure.utils.NumberUtil.mul;
import static com.daddylab.supplier.item.infrastructure.utils.NumberUtil.sub;

/**
 * 采购入库明细实体
 * <AUTHOR>
 * @date 2022/3/24 17:28
 **/
@Data
@EqualsAndHashCode(callSuper = false)
public class StockInOrderDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField(fill = FieldFill.INSERT)
	private Long createdAt;
    @TableField(fill = FieldFill.UPDATE)
    private Long  updatedAt;
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    private Long deletedAt;
    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;
    /**
     * 关联采购入库单id
     **/
    private Long stockInOrderId;
    /**
     * 商品skuCode
     **/
    private String itemSkuCode;
    /**
     * 商品名称
     **/
    private String itemName;
    /**
     * 规格
     **/
    private String specifications;
    /**
     * 收料单位 （默认为“个”）可直接同步采购单中的【采购单位】
     **/
    private String unit;
    /**
     * 交货数量 同步采购单中采购数量
     **/
    private Integer receiptQuantity;

    /**
     * 实际入仓数量(旺店通回填)
     **/
    private Integer realReceiptQuantity;

    /**
     * 获取计算价税的入库数量（逻辑为在未有实际入库数量的情况下按照采购数量算，已实际入库的情况下按照实际入库数量算）
     */
    @JsonIgnore
    public Integer getCalcReceiptQuantity() {
        return realReceiptQuantity == 0 ? receiptQuantity : realReceiptQuantity;
    }

    /**
     * 库存状态
     **/
    private Integer stockState;

    /**
     * 仓库id 默认同步采购单中，预计入仓仓库，可根据实际入仓仓库修改
     **/
    private String warehouseNo;

    /**
     * 是否是赠品,0不是,1是。 同步采购单中，可修改
     **/
    private Integer isGift;

    /**
     * 含税单价
     **/
    private BigDecimal taxPrice;

    /**
     * 含税总额
     **/
    private BigDecimal taxAmount;

    /**
     * 税率(实际计算值，非百分比)
     **/
    private BigDecimal taxRate;

    /**
     * 税额=(含税单价-税后单价)*数量
     **/
    private BigDecimal taxQuota;

    /**
     * 价税合计 单价（税前）* 采购数量
     **/
    private BigDecimal totalPriceTax;

    /**
     * 税后单价 = 含税单价 / (1 + 税率)
     **/
    private BigDecimal afterTaxPrice;

    /**
     * 税后金额 = 税后单价 * 采购数量
     **/
    private BigDecimal afterTaxAmount;

    /**
     * 采购数量
     **/
    private Integer purchaseQuantity;

    /**
     * 备注
     **/
    private String remark;



    /**
     * 重新计算价税合计
     */
    public void calcPriceAndTaxAmount() {
        totalPriceTax = scale(mul(getCalcReceiptQuantity(), getTaxPrice()));
        taxAmount = totalPriceTax;
        afterTaxPrice = scale(div(getTaxPrice(), add(1, getTaxRate())));
        afterTaxAmount = scale(mul(getAfterTaxPrice(), getCalcReceiptQuantity()));
        taxQuota = scale(mul(getCalcReceiptQuantity(), sub(getTaxPrice(), getAfterTaxPrice())));
    }

    private BigDecimal scale(BigDecimal decimal) {
        if (decimal != null) {
            return decimal.setScale(6, RoundingMode.HALF_EVEN);
        }
        return null;
    }
}
