package com.daddylab.supplier.item.infrastructure.third.kuaishou.domain.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @class KsItemCreate.java
 * @description 描述类的作用
 * @date 2024-03-01 17:53
 */
@NoArgsConstructor
@Data
public class KsItemAuditStatusChangeDTO {

    /**
     * 快手商品id
     */
    private Long itemId;
    /**
     * 商家id
     */
    private Integer sellerId;
    /**
     * 审核状态 0待审核 1审核待修改 2审核通过 3审核拒绝
     */
    private Integer status;
    /**
     * 原商品审核状态
     */
    private Integer beforeStatus;
    /**
     * 	状态变更时间
     */
    private Integer updateTime;
    /**
     * 审核原因
     */
    private String reason;
}
