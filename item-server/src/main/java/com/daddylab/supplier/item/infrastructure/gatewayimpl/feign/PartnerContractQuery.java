package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import com.fasterxml.jackson.annotation.JsonProperty;
import feign.Param;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/3/28
 */
@Data
public class PartnerContractQuery {
    /**
     * 合同编码
     */
    @JsonProperty("contract_no")
    String contractNo;

    /**
     * 合同名称
     */
    String name;

    /**
     * 3: 合作中
     */
    @Param("contract_status")
    Integer status;

    /**
     * 此供应商P系统ID
     */
    @JsonProperty("organization_id")
    Long organizationId;

}
