package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrderLogistics;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtSaleStockOutOrderLogisticsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtSaleStockOutOrderLogisticsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 旺店通销售出库单物流单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Service
public class WdtSaleStockOutOrderLogisticsServiceImpl extends DaddyServiceImpl<WdtSaleStockOutOrderLogisticsMapper, WdtSaleStockOutOrderLogistics> implements IWdtSaleStockOutOrderLogisticsService {

}
