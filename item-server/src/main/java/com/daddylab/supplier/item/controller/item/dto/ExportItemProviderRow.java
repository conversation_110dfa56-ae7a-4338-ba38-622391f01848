package com.daddylab.supplier.item.controller.item.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2022年08月08日 3:11 PM
 */
@Data
@HeadRowHeight(20)
@ContentRowHeight(16)
@EqualsAndHashCode(of = {"skuCode"})
public class ExportItemProviderRow implements Serializable {


    private static final long serialVersionUID = 6005461852100076970L;


//    @ExcelProperty(value = "编码")
//    private String code;

//    @ExcelProperty(value = "名称")
//    private String name;
//
//    @ExcelProperty(value = "规格型号")
//    private String specifications;

//    @ExcelProperty("成本")
//    private String costPrice;

    @ExcelProperty("商品sku")
    private String skuCode;

    @ExcelProperty("采购税率")
    private BigDecimal purchaseTaxRate;

//    @ExcelProperty("日常价")
//    private String costPrice;
//
//    @ExcelProperty("调价时间")
//    private String startTime;

}
