package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.daddylab.supplier.item.types.handingsheet.HandingSheetItemSpuPersistData;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 盘货表关联的商品（V2.4.5新版盘货表）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class HandingSheetItemSpu implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 盘货表ID
     */
    private Long handingSheetId;

    /**
     * 主平台
     */
    private Integer platform;

    /**
     * 主平台商品ID
     */
    private String outerItemId;

    /**
     * 商品编码
     */
    private String itemCode;

    /**
     * 后端商品ID（未匹配为0）
     */
    private Long itemId;

    /**
     * 盘货表商品SPU纬度数据模型
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private HandingSheetItemSpuPersistData data;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人uid
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人uid
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除：0否1是
     */
    @TableLogic
    private Integer isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;


}
