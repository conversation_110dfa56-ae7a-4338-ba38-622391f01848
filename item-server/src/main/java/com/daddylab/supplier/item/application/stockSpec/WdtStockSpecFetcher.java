package com.daddylab.supplier.item.application.stockSpec;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.wms.StockSpecAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.dto.StockSpecSearch2Params;
import com.daddylab.mall.wdtsdk.apiv2.wms.dto.StockSpecSearch2Response;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtStockSpec;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtStockSpecRt;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtStockSpecRtService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtStockSpecService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2022/5/15
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class WdtStockSpecFetcher implements PageFetcher, WdtStockSpecFetchService {

    private final WdtGateway wdtGateway;
    private final IWdtStockSpecService wdtStockSpecService;
    private final IWdtStockSpecRtService wdtStockSpecRtService;
    private final OperateLogDomainService operateLogDomainService;

    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.WDT_STOCK_SPEC;
    }

    @Override
    public int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
        return query(startTime, endTime, 1, 1, true).getTotalCount();
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, long pageIndex, long pageSize,
                      RunContext runContext) {
        final StockSpecSearch2Response response = query(startTime, endTime, (int) pageIndex,
                (int) pageSize,
                false);
        if (CollUtil.isEmpty(response.getDetailList())) {
            return;
        }
        handleResponse(response);
    }

    private void handleResponse(StockSpecSearch2Response response) {
        for (StockSpecSearch2Response.Detail detail : response.getDetailList()) {
            final WdtStockSpec entity = WdtStockSpecAssembler.INST.apiModelToPersistModel(detail);
            entity.setAvailableStock(entity.calcAvailableStock());
            final WdtStockSpecRt wdtStockSpecRt = WdtStockSpecAssembler.INST.poToRtPo(entity);
            CompletableFuture.runAsync(() -> {
                final WdtStockSpecRt persistedEntity = wdtStockSpecRtService.lambdaQuery()
                        .eq(WdtStockSpecRt::getRecId, wdtStockSpecRt.getRecId()).one();
                if (persistedEntity == null) {
                    wdtStockSpecRtService.save(wdtStockSpecRt);
                } else {
                    wdtStockSpecRt.setId(persistedEntity.getId());
                    wdtStockSpecRtService.updateById(wdtStockSpecRt);
                }
            });
        }
    }

    private StockSpecSearch2Response query(LocalDateTime st, LocalDateTime et, int pageNo,
                                           int pageSize, boolean calcTotal) {
        try {
            final StockSpecAPI stockAPI = wdtGateway.getAPI(StockSpecAPI.class);
            final Pager pager = new Pager(pageSize, pageNo - 1, calcTotal);
            final StockSpecSearch2Params params = new StockSpecSearch2Params();
            params.setStartTime(st.format(DatePattern.NORM_DATETIME_FORMATTER));
            params.setEndTime(et.format(DatePattern.NORM_DATETIME_FORMATTER));

            return stockAPI.search2(params, pager);
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapFetchException(e, fetchDataType());
        }
    }

    @Override
    public void fetchBySpecNos(Collection<String> specNos) {
        try {
            int pageSize = 1000;
            int pageNo = 0;
            boolean calcTotal = true;
            final StockSpecAPI stockAPI = wdtGateway.getAPI(StockSpecAPI.class);
            final Pager pager = new Pager(pageSize, pageNo, calcTotal);
            final StockSpecSearch2Params params = new StockSpecSearch2Params();
            params.setSpecNos(new ArrayList<>(specNos));

            final StockSpecSearch2Response stockSpecSearch2Response = stockAPI.search2(params, pager);
            handleResponse(stockSpecSearch2Response);
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapFetchException(e, fetchDataType());
        }
    }
}
