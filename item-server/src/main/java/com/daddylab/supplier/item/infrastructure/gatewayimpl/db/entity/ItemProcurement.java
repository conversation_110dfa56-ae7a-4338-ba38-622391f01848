package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 商品采购设置
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemProcurement implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品ID
     */
    private Long itemId;

    /**
     * 供应商ID
     */
    private Long providerId;

    /**
     * 采购员 table buyer 主键ID
     */
    private Long buyerId;

//    /**
//     * 采购员名称
//     */
//    private String buyerName;

    /**
     * 是否赠品
     */
    private Integer isGift;

    /**
     * 关联主商品ID
     */
    private Long mainItemId;

    /**
     * 商品发货渠道。0.仓库发货。1.工厂发货。（多选逗号分隔）
     */
    private String delivery;

    /**
     * 采购仓库ID
     */
    private String warehouseNo;

    /**
     * 基本单位ID
     */
    private Long baseUnitId;

    /**
     * 销售税率
     */
    private BigDecimal rate;

    /**
     * 采购税率
     */
    private BigDecimal purchaseRate;

    /**
     * 删除时间
     */
    private Long deletedAt;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;

    private String qcIds;

    /**
     * 税率编码
     */
    private String taxRateCode;

    // ------------- DB 字段映射属性 end ------------------


}
