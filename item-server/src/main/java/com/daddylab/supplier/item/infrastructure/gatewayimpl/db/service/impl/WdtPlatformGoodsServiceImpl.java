package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtPlatformGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtPlatformGoodsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtPlatformGoodsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 旺店通系统数据-平台商品 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Service
public class WdtPlatformGoodsServiceImpl extends DaddyServiceImpl<WdtPlatformGoodsMapper, WdtPlatformGoods> implements IWdtPlatformGoodsService {

}
