/*
package com.daddylab.supplier.item.application.virtualWarehouse.dto;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

*/
/**
 * <AUTHOR> up
 * @date 2024年03月01日 3:39 PM
 *//*

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("库存明细列表请求参数封装")
public class StockConfigDetailPageQuery extends PageQuery {
    private static final long serialVersionUID = -2572274663723523394L;

    @ApiModelProperty("基础参数-仓库编码")
    private List<String> warehouseNos;

    @ApiModelProperty("基础参数-仓库ID")
    private List<Long> warehouseIds;

    @ApiModelProperty("基础参数-sku编码")
    private List<String> skuCodes;

    @ApiModelProperty("基础参数-spu编码")
    private List<String> spuCodes;

    @ApiModelProperty("基础参数-品牌 ID")
    private List<Long> brandIds;

    @ApiModelProperty("基础参数-商品名称")
    private List<String> itemNames;

    @ApiModelProperty("基础参数-品类 ID")
    private List<Long> categoryIds;

    @ApiModelProperty("基础参数-业务线")
    private List<Integer> businessLine;

    // -----------

    @ApiModelProperty("库存明细查询分类。1虚拟仓.2店铺.二选一就行")
    @NotNull(message = "查询分类不得为空")
    private Integer queryType;

    @ApiModelProperty("虚拟仓-库存明细")
    private VwStockConfigDetailCmd vwStockConfigDetailCmd;

    @ApiModelProperty("店铺-库存明细")
    private ShopStockConfigDetailCmd shopStockConfigDetailCmd;


}
*/
