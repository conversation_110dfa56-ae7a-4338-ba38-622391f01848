package com.daddylab.supplier.item.application.openwdt;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig.Config;

/**
 * <AUTHOR>
 * @since 2022/7/8
 */
public interface OpenWdtService {

    /**
     * 通用API实例获取方法
     *
     * @param apiClass  API接口类
     * @param config    旺店通配置
     * @param qimen     是否通过奇门接口调用
     * @param enableLog 是否启用日志代理
     * @param <T>       API接口类
     * @return API实例
     */
    <T> T getAPI(Class<T> apiClass, Config config, boolean qimen, boolean enableLog);
}
