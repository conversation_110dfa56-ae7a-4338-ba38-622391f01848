package com.daddylab.supplier.item.application.platformItem.data;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/7
 */
@Data
public class PlatformItemSyncResult {
    @ApiModelProperty("未拉取到数据的平台商品ID")
    final List<String> noUpdateGoodsIds;
    @ApiModelProperty("更新成功的平台商品ID")
    final List<String> fetchSuccessGoodsIds;

    public static PlatformItemSyncResult of(List<String> noUpdateGoodsIds, List<String> fetchSuccessGoodsIds) {
        return new PlatformItemSyncResult(noUpdateGoodsIds, fetchSuccessGoodsIds);
    }
}
