package com.daddylab.supplier.item.application.order.settlement.dto;


import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayApplyStatus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2023年08月14日 4:32 PM
 */
@ApiModel("结算单分页响应封装")
@Data
public class SettlementOrderPageVo {

    private Long id;
    private String no;

    @ApiModelProperty("周期")
    private String cycle;
    private Long providerId;
    private String providerName;
    @ApiModelProperty("是否黑名单 0否 1是")
    private Integer isBlacklist;
    private String warehouseNo;
    private String warehouseName;
    @ApiModelProperty("暂估数量")
    private Integer temporaryQuantity;
    @ApiModelProperty("结算数量")
    private Integer settlementQuantity;
    @ApiModelProperty("订单员")
    private String orderPersonnel;

    private Integer businessLine;

    private PayApplyStatus payApplyStatus;

    @ApiModelProperty("结算单版本号")
    private Integer version;
}
