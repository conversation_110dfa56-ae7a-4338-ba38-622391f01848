package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WechatMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String title;

    private String content;

    private String link;

    @TableLogic
    private Integer isDel;

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 0:入库。1:已发送。-1:发送异常 2:发送中
     */
    private Integer state;

    /**
     * 接收者id,英文逗号隔开。
     */
    private String recipient;

    /**
     * 1:待办提醒。2:信息修改 4:群机器人
     */
    private Integer type;

    private String requirement;

    public static WechatMsg init() {
        WechatMsg msg = new WechatMsg();
        msg.setState(0);
        return msg;
    }

    private String templateCode;

    public String getMsgSummary(){
        return StrUtil.format("id:{},code:{},title:{},receiver:{}", this.id, this.templateCode, title, this.recipient);
    }

}
