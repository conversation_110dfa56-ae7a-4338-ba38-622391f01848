package com.daddylab.supplier.item.controller.purchase.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2024年02月22日 10:58 AM
 */
@Data
@ApiModel("修改税率信息")
public class UpdateTaxDataCmd {

    @ApiModelProperty("明细 ID")
    Long detailId;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    @ApiModelProperty("税额")
    private BigDecimal taxQuota;

    @ApiModelProperty("税后金额")
    private BigDecimal afterTaxAmount;

}
