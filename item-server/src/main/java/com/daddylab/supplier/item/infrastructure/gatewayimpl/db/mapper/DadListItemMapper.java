package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DadListItem;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;

/**
 * <p>
 * 老爸清单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Repository
@DS("dpm")
public interface DadListItemMapper extends DaddyBaseMapper<DadListItem> {

    void deleteData(@Param("itemCodeList")Collection<String> itemCodeList);

    void deleteAllData();
}
