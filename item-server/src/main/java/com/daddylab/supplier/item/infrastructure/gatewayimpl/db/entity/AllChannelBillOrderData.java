package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 全渠道开票主订单数据
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AllChannelBillOrderData implements Serializable {

  private static final long serialVersionUID = 1L;

  /** id */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /** 订单编号（店铺维度） */
  private String orderId;

  /** 订单状态，0:未知,1:待支付,2:定金已支付,3:已支付,4:部分已发货,5:全部已发货,6:已完成,7:已取消 */
  private Integer orderStatus;

  /** 平台 */
  private String platName;

  /** 店铺名称 */
  private String shopName;

  /** 实付金额（支付金额含运费） */
  private BigDecimal realAmount;

  /** 运费 */
  private BigDecimal postFee;

  /** 售后金额（退款完成的） */
  private BigDecimal rfdAmount;

  /** 开票金额（实付-售后） */
  private BigDecimal invoiceAmt;

  /** 创建时间 */
  private LocalDateTime createTime;

  /** 更新时间 */
  private LocalDateTime updateTime;

  @TableLogic(value = "0", delval = "1")
  private Integer isDel;
}
