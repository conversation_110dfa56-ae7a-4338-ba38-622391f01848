package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorizationApp;

import java.util.Optional;

/**
 * <p>
 * 店铺授权应用 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
public interface IShopAuthorizationAppService extends IDaddyService<ShopAuthorizationApp> {

    Optional<ShopAuthorizationApp> getByShopNo(String shopNo);
}
