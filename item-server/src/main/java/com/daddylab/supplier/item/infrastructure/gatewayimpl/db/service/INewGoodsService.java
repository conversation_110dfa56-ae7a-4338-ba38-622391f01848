package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.application.saleItem.query.NewGoodsQueryPage;
import com.daddylab.supplier.item.controller.item.dto.SkuWithNewGoodsDto;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 新品商品 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
public interface INewGoodsService extends IDaddyService<NewGoods> {

    /**
     * 分页查询新品商品库的商品
     * @param queryPage
     * @return
     */
    int countNewGoods(NewGoodsQueryPage queryPage);

    /**
     * 批量查询（by itemIds）
     *
     * @param itemIds itemIds
     * @return List<NewGoods>
     */
    List<NewGoods> selectBatchByItemIds(List<Long> itemIds);

    /**
     * SKU编码批量查询
     * @param skuCodes SKU编码
     * @return
     */
    List<NewGoods> selectBatchBySkuCodes(Collection<String> skuCodes);

    /**
     * 更新产品负责人 ID
     * 如果产品负责人离职了，就讲产品负责人更改为「小高」
     */
    void updateResignedPrincipalId();

    /**
     * 生成映射
     * key: skuCode
     * val: dto 信息
     *
     * @param skuCodes 规格编码
     * @return Map<String, SkuWithNewGoodsDto>
     */
    Map<String, SkuWithNewGoodsDto> skuCodeToDtoMap(List<String> skuCodes);

    List<StaffBrief> getQcProcessors(Long itemId);
}
