package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AdsTidbEcpUsrPhoneInfoPd;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 大数据订单隐私数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
@DS("bigdata")
@Repository
public interface AdsTidbEcpUsrPhoneInfoPdMapper extends DaddyBaseMapper<AdsTidbEcpUsrPhoneInfoPd> {

}
