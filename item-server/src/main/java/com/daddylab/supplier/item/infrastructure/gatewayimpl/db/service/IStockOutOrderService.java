package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockOutOrderState;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockOutOrder;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 退料出库表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-24
 */
public interface IStockOutOrderService extends IDaddyService<StockOutOrder> {

    void setKingDeeId(Long id, String kingDeeId);

    void removeKingDeeId(Long id);

    Boolean related(Long purchaseOrderId);

    List<Long> relatedIdList(Long purchaseOrderId);

    void updateStatusByPurchaseOrderNos(Collection<String> purchaseOrderNos, StockOutOrderState state);

//    void updateStatus(String no, StockOutOrderState status);

}
