package com.daddylab.supplier.item.application.message.wechat;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 企微消息，事件触发类型
 *
 * <AUTHOR> up
 * @date 2022/6/2 6:05下午
 */
@AllArgsConstructor
@Getter
public enum MsgEventType implements IEnum<Integer> {

    /**
     * 消除黄线
     */
//    @Deprecated
//    CHOOSE_TO_IMPROVE(1, "「待选择」➡️「待完善」", RemindType.IMMEDIATELY_REMINDER,0),

    IMPROVE_TO_DESIGN(2, "「待完善」➡️「待设计」", RemindType.IMMEDIATELY_REMINDER, 0),

    /**
     * 待法务审核
     */
    DESIGN_TO_AUDIT_LEGAL(31, "「待设计」➡️「待审核」", RemindType.IMMEDIATELY_REMINDER, 0),

    /**
     * 待QC审核
     */
    DESIGN_TO_AUDIT_QC(32, "「待设计」➡️「待审核」", RemindType.IMMEDIATELY_REMINDER, 0),

    AUDIT_TO_MODIFY(4, "「待审核」➡️「待修改」", RemindType.IMMEDIATELY_REMINDER, 0),

//    @Deprecated
//    MODIFY_TO_AUDIT(5, "「待修改」➡️「待审核」", MsgType.TO_DO_REMINDER),

    //    @Deprecated
//    AUDIT_TO_ROOST(6, "「待审核」➡️「待上架」", MsgType.TO_DO_REMINDER),
    ONE_FIELD_CHANGE(7, "上架后某一字段发生变更", RemindType.IMMEDIATELY_REMINDER, 0),

    CHARGER_CHANGE(8, "修改负责人信息", RemindType.IMMEDIATELY_REMINDER, 0),

    PLAN_SUBMIT(9, "上新计划完成提交", RemindType.IMMEDIATELY_REMINDER, 0),

    PLAN_INFO_CHANGE(10, "上新计划字段变更", RemindType.IMMEDIATELY_REMINDER, 1),

    LIVE_SPEECH(11, "直播话术审核完成", RemindType.IMMEDIATELY_REMINDER, 0),

    LIVE_SPEECH_AUDIT_LEGAL(12,"直播话术审核提交(待法务审核)",RemindType.IMMEDIATELY_REMINDER,0),
    LIVE_SPEECH_AUDIT_QC(13,"直播话术待QC审核",RemindType.IMMEDIATELY_REMINDER,0),

    SHELF_ITEM_INFO_CHANG(14,"信息修改，待上架/已上架的商品信息发生修改",RemindType.IMMEDIATELY_REMINDER,0)
    ;

    /**
     * 映射值
     */
    private final Integer value;
    /**
     * 触发事件描述
     */
    private final String desc;
    /**
     * 提醒类型
     */
    private final RemindType remindType;

    /**
     * 0:文本卡片消息，默认
     * 1:文本消息
     */
    private final Integer sendType;


}
