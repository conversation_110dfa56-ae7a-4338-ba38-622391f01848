package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.checkerframework.common.value.qual.EnumVal;

/**
 * <AUTHOR> up
 * @date 2025年02月06日 11:07 AM
 */
@Getter
@AllArgsConstructor
public enum DivisionLevelEnum implements IEnum<Integer> {

    COOPERATION(0, "合作方"),
    BUSINESS_TYPE(1, "业务类型"),

    /**
     * 店铺资料 独有属性
     */
    RUNNING_MODEL(2, "运营模式"),
    ;

    @EnumVal
    private final Integer value;

    private final String desc;
}
