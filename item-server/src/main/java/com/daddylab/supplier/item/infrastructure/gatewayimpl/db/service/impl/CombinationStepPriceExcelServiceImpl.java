package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CombinationStepPriceExcel;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CombinationStepPriceExcelMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICombinationStepPriceExcelService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 组合多件供价（excel） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Service
public class CombinationStepPriceExcelServiceImpl extends DaddyServiceImpl<CombinationStepPriceExcelMapper, CombinationStepPriceExcel> implements ICombinationStepPriceExcelService {

}
