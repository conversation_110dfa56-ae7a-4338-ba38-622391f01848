package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSkuInventory;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemSkuInventoryMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemSkuInventoryService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 平台商品SKU库存表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-13
 */
@Service
public class PlatformItemSkuInventoryServiceImpl extends DaddyServiceImpl<PlatformItemSkuInventoryMapper, PlatformItemSkuInventory> implements IPlatformItemSkuInventoryService {


    @Override
    public PlatformItemSkuInventory getSkuInventory(Platform type, String outSkuId) {
        LambdaQueryWrapper<PlatformItemSkuInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlatformItemSkuInventory::getType, type.getValue())
                    .eq(PlatformItemSkuInventory::getOuterSkuId, outSkuId)
                .last("limit 1");
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<PlatformItemSkuInventory> getItemSkuInventory(Platform type, String outItemId) {
        LambdaQueryWrapper<PlatformItemSkuInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlatformItemSkuInventory::getType, type.getValue())
                .eq(PlatformItemSkuInventory::getOuterItemId, outItemId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public Long getSkuInventoryPlatformCount(Platform type) {
        LambdaQueryWrapper<PlatformItemSkuInventory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PlatformItemSkuInventory::getType, type.getValue());
        return baseMapper.selectCount(queryWrapper).longValue();
    }

    @Override
    public List<PlatformItemSkuInventory> getSkuInventories(Platform type, String outerItemId) {
        return lambdaQuery().eq(PlatformItemSkuInventory::getType, type.getValue())
                            .eq(PlatformItemSkuInventory::getOuterItemId, outerItemId)
                            .list();
    }
}
