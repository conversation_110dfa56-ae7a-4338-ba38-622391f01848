package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OperateLogMapper;

/**
 * <p>
 * 操作日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface IOperateLogService extends IDaddylabServicePlus<OperateLog, OperateLogMapper> {


}
