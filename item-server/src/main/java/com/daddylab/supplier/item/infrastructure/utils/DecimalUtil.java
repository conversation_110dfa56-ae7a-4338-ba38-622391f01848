package com.daddylab.supplier.item.infrastructure.utils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class DecimalUtil {
    private static final int DEFAULT_SCALE = 2;
    private static final int DEFAULT_ROUND_MODE = BigDecimal.ROUND_HALF_UP;

    public static String format(BigDecimal decimal, int scale, int round) {
        return decimal.setScale(scale, round).toPlainString();
    }

    public static String format(BigDecimal decimal, int scale) {
        return decimal.setScale(DEFAULT_SCALE, scale).toPlainString();
    }

    public static String format(BigDecimal decimal) {
        return decimal.setScale(DEFAULT_SCALE, DEFAULT_ROUND_MODE).toPlainString();
    }

    public static BigDecimal divide(BigDecimal n1, BigDecimal n2) {
        return divide(n1, n2, DEFAULT_SCALE, DEFAULT_ROUND_MODE);
    }

    public static BigDecimal divide(BigDecimal n1, BigDecimal n2, int scale) {
        return divide(n1, n2, scale, DEFAULT_ROUND_MODE);
    }

    public static BigDecimal divide(BigDecimal n1, BigDecimal n2, int scale, int mode) {
        return n2.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : n1.divide(n2, scale, mode);
    }
}
