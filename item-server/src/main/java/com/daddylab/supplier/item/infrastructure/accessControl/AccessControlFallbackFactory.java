package com.daddylab.supplier.item.infrastructure.accessControl;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import cn.hutool.core.io.FileUtil;
import com.daddylab.supplier.item.infrastructure.goclient.Rows;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import feign.hystrix.FallbackFactory;
import java.nio.charset.StandardCharsets;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.mockito.Mockito;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/3/31
 */
@Component
@Slf4j
public class AccessControlFallbackFactory implements FallbackFactory<AccessControlAPI> {
    public static final int DOMAIN = 6;
    private AccessControlAPI accessControlAPI;

    @Override
    public AccessControlAPI create(Throwable cause) {
        log.error("访问权限系统接口异常，异常消息：{}", cause.getMessage(), cause);
        if (this.accessControlAPI != null)
            return this.accessControlAPI;
        return getAccessControlAPI();
    }

    private synchronized AccessControlAPI getAccessControlAPI() {
        try {
            AccessControlAPI accessControlAPI = Mockito.mock(AccessControlAPI.class, invocation -> {
                if (invocation.getMethod().getReturnType().equals(Rsp.class)) {
                    final Rsp<Object> rsp = new Rsp<>();
                    rsp.setCode(0);
                    rsp.setFlag(false);
                    rsp.setMsg("权限系统响应异常，降级响应");
                    rsp.setData(null);
                    return rsp;
                }
                return null;
            });
            if (ApplicationContextUtil.isActiveProfile("local", "dev", null)) {
                final Rsp<List<CasbinPolicy>> policiesRsp = JsonUtil.parse(FileUtil.readString("policy.json", StandardCharsets.UTF_8),
                        new TypeReference<Rsp<List<CasbinPolicy>>>() {
                        });
                when(accessControlAPI.casbinPolicies(DOMAIN)).thenReturn(policiesRsp);

                final Rsp<List<Resource>> resourcesRsp = JsonUtil.parse(FileUtil.readString("resources.json", StandardCharsets.UTF_8),
                        new TypeReference<Rsp<List<Resource>>>() {
                        });
                when(accessControlAPI.resourcesQuery(DOMAIN)).thenReturn(resourcesRsp);

                final Rsp<List<Dept>> deptsRsp = JsonUtil.parse(FileUtil.readString("depts.json", StandardCharsets.UTF_8),
                        new TypeReference<Rsp<List<Dept>>>() {
                        });
                when(accessControlAPI.dept()).thenReturn(deptsRsp);

                final Rsp<Rows<StaffAcInfo>> acUserInfoRsp = JsonUtil.parse(FileUtil.readString("acUserInfo.json", StandardCharsets.UTF_8),
                        new TypeReference<Rsp<Rows<StaffAcInfo>>>() {
                        });
                when(accessControlAPI.staffAcInfoPageQuery(any())).thenReturn(acUserInfoRsp);
            }
            return this.accessControlAPI = accessControlAPI;
        } catch (Exception e) {
            log.error("创建降级对象失败", e);
            return null;
        }
    }
}
