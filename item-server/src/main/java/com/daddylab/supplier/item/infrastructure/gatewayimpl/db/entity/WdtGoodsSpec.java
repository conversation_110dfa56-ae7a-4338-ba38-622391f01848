package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtGoodsSpec implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货品唯一键
     */
    private Integer goodsId;

    /**
     * 单品唯一键
     */
    private Integer specId;

    /**
     * 商家编码
     */
    private String specNo;

    /**
     * 规格码
     */
    private String specCode;

    /**
     * 主条码
     */
    private String barcode;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 最低价
     */
    private BigDecimal lowestPrice;

    /**
     * 零售价
     */
    private BigDecimal retailPrice;

    /**
     * 批发价
     */
    private BigDecimal wholesalePrice;

    /**
     * 会员价
     */
    private BigDecimal memberPrice;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 有效期天数
     */
    private Integer validityDays;

    /**
     * 最佳销售天数
     */
    private Integer salesDays;

    /**
     * 最佳收获天数
     */
    private Integer receiveDays;

    /**
     * 重量(kg)
     */
    private BigDecimal weight;

    /**
     * 长(cm)
     */
    private BigDecimal length;

    /**
     * 宽(cm)
     */
    private BigDecimal width;

    /**
     * 高(cm)
     */
    private BigDecimal height;

    /**
     * 启用序列号(原is_sn_enable)
     */
    private Integer snType;

    /**
     * 允许低于成本价
     */
    private Boolean isLowerCost;

    /**
     * 航空禁运
     */
    private Integer isNotUseAir;

    /**
     * 默认0 （需要的仓库流程相加） 2、无需验货 8、需要质检 16、无需拣货 32、无需唯一码 64、无需自动打印吊牌
     */
    private Integer wmsProcessMask;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 大件类别 0、非大件 1、普通大 2、独立大件（不可和小件一起发） 3、按箱规拆分 -1、非单发件
     */
    private Integer largeType;

    /**
     * 货品标签，多个标签用英文逗号隔开
     */
    private String goodsLabel;

    /**
     * 0代表未删除 大于0代表已删除
     */
    private Integer deleted;

    /**
     * 备注
     */
    private String remark;

    /**
     * 最后修改时间
     */
    private LocalDateTime specModified;

    /**
     * 创建时间, 时间格式 YYYY-MM-DD HH:MM:SS
     */
    private LocalDateTime specCreated;

    /**
     * 自定义属性 1
     */
    private String prop1;

    /**
     * 自定义属性 2
     */
    private String prop2;

    /**
     * 自定义属性 3
     */
    private String prop3;

    /**
     * 自定义属性4
     */
    private String prop4;

    /**
     * 自定义属性5
     */
    private String prop5;

    /**
     * 自定义属性6
     */
    private String prop6;

    /**
     * 自定义价格1
     */
    private BigDecimal customPrice1;

    /**
     * 自定义价格2
     */
    private BigDecimal customPrice2;

    /**
     * 图片URL
     */
    private String imgUrl;

    /**
     * 基本单位
     */
    private String specUnitName;

    /**
     * 辅助单位
     */
    private String specAuxUnitName;

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;

    @TableLogic
    private Integer isDel;


}
