package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsTrace;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.CloseReasonType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OrderLogisticsTraceStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderLogisticsTraceMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsTraceService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.MPUtil;

import java.util.Collection;
import java.util.List;
import org.springframework.stereotype.Service;

import javax.websocket.CloseReason;

/**
 * 订单物流跟踪记录 服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Service
public class OrderLogisticsTraceServiceImpl
    extends DaddyServiceImpl<OrderLogisticsTraceMapper, OrderLogisticsTrace>
    implements IOrderLogisticsTraceService {

  @Override
  public boolean lock(OrderLogisticsTrace orderLogisticsTrace) {
    return lambdaUpdate()
        .set(OrderLogisticsTrace::getLockTime, DateUtil.currentTime())
        .eq(OrderLogisticsTrace::getId, orderLogisticsTrace.getId())
        .eq(OrderLogisticsTrace::getLockTime, 0)
        .update();
  }

  @Override
  public boolean updateStatusAndUnlock(OrderLogisticsTrace orderLogisticsTrace) {
    orderLogisticsTrace.setLockTime(0L);
    return updateById(orderLogisticsTrace);
  }

  @Override
  public long maxId() {
    return lambdaQuery()
        .select(OrderLogisticsTrace::getId)
        .orderByDesc(OrderLogisticsTrace::getId)
        .last("limit 1")
        .oneOpt()
        .map(OrderLogisticsTrace::getId)
        .orElse(0L);
  }

  @Override
  public List<OrderLogisticsTrace> listByTraceStatus(OrderLogisticsTraceStatus status, int num) {
    return lambdaQuery()
        .in(OrderLogisticsTrace::getTraceStatus, status)
        .last("limit " + num)
        .list();
  }

  @Override
  public boolean updateTraceStatusByIds(List<Long> ids, OrderLogisticsTraceStatus traceStatus) {
    return lambdaUpdate()
        .set(OrderLogisticsTrace::getTraceStatus, traceStatus)
        .set(OrderLogisticsTrace::getUpdatedAt, DateUtil.currentTime())
        .in(OrderLogisticsTrace::getId, ids)
        .update();
  }

  @Override
  public List<OrderLogisticsTrace> scanOpen(
      Long trackTime, Long cursor, int limit, Collection<Long> traceIds) {
    return lambdaQuery()
        .eq(OrderLogisticsTrace::getOpenTrace, 1)
        .ge(trackTime != null && trackTime > 0, OrderLogisticsTrace::getTrackTime, trackTime)
        .in(traceIds != null && !traceIds.isEmpty(), OrderLogisticsTrace::getId, traceIds)
        .gt(OrderLogisticsTrace::getId, cursor)
        .orderByAsc(OrderLogisticsTrace::getId)
        .last(MPUtil.limit(limit))
        .list();
  }

  @Override
  public boolean openTrace(Long traceId) {
    return lambdaUpdate()
        .set(OrderLogisticsTrace::getOpenTrace, 1)
        .set(OrderLogisticsTrace::getCloseReason, CloseReasonType.NONE)
        .set(OrderLogisticsTrace::getUpdatedAt, DateUtil.currentTime())
        .set(OrderLogisticsTrace::getUpdatedUid, 0L)
        .eq(OrderLogisticsTrace::getId, traceId)
        .update();
  }

  @Override
  public boolean closeTrace(Long traceId, CloseReasonType closeReason) {
    return lambdaUpdate()
        .set(OrderLogisticsTrace::getOpenTrace, 0)
        .set(OrderLogisticsTrace::getCloseReason, closeReason)
        .set(OrderLogisticsTrace::getUpdatedAt, DateUtil.currentTime())
        .set(OrderLogisticsTrace::getUpdatedUid, 0L)
        .eq(OrderLogisticsTrace::getId, traceId)
        .update();
  }
}
