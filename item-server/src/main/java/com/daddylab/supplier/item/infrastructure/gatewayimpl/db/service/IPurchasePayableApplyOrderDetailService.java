package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableApplyOrderDetail;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 采购付款申请单详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
public interface IPurchasePayableApplyOrderDetailService extends IDaddyService<PurchasePayableApplyOrderDetail> {

}
