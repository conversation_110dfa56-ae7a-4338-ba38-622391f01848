package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchPlan;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> up
 * @date 2022/6/3 4:49 下午
 */
@Data
public class ItemLaunchPlanWrapper{
    ItemLaunchPlan plan;

    private List<Long> itemIdList;

    /**
     * 产品负责人
     */
    private List<Long> principalIdList;
    private Map<Long,List<Long>> principalIdItemIdMap;
    /**
     * 采购负责人
     */
    private List<Long> buyerIdList;
    private Map<Long,List<Long>> buyerIdItemIdMap;
    /**
     * qc 负责人
     */
    private List<Long> qcIdList;
    private Map<Long,List<Long>> itemIdQcIdMap;
    private Long skuCount;
    private Long itemCount;

    /**
     * 新负责人
     */
    private List<Long> newOperatorList;
    /**
     * 新品商品 负责人信息修改者
     */
    private Long newGoodUpdaterUid;

    private Long beUrgedSomeone;
    private Long urgedInitiator;
    private Long processItemId;

    private ItemLaunchPlanWrapper() {
    }

    public static ItemLaunchPlanWrapper init(ItemLaunchPlan plan){
        ItemLaunchPlanWrapper wrapper = new ItemLaunchPlanWrapper();
        wrapper.setPlan(plan);
        return wrapper;
    }

}
