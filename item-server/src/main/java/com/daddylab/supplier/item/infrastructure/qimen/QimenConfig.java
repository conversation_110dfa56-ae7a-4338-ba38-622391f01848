package com.daddylab.supplier.item.infrastructure.qimen;

import com.qimencloud.api.DefaultQimenCloudClient;
import com.qimencloud.api.QimenCloudClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2022/1/17
 */
@ConfigurationProperties(prefix = "qimen")
@Configuration
@Data
public class QimenConfig {
    String serverUrl;
    String appKey;
    String appSecret;
    String wdtTargetKey;

    @Bean
    public QimenCloudClient qimenClient() {
        return new DefaultQimenCloudClient(serverUrl, appKey, appSecret);
    }
}
