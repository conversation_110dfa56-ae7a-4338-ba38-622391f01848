package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * wdt订单信息清洗错误信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtOrderDetailWrapperError implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * skuCode
     */
    private String skuCode;

    private String tradeNo;

    /**
     * 1.出库单品
     * 2.出库组合商品
     * -1.入库数量。
     * -9.既不是单品也不是组合装，这个商品编号哪里来的呢？
     */
    private Integer type;

    /**
     * 操作时间 yyyyMM
     */
    private String operateTime;

    /**
     * 错误信息
     */
    private String error;

    public static WdtOrderDetailWrapperError ofWrapper(WdtOrderDetailWrapper wrapper,String errorMsg){
        WdtOrderDetailWrapperError one = new WdtOrderDetailWrapperError();
        one.setSkuCode(wrapper.getSkuCode());
        one.setType(wrapper.getType());
        one.setOperateTime(wrapper.getOperateTime());
        one.setError(errorMsg);
        return one;
    }


}
