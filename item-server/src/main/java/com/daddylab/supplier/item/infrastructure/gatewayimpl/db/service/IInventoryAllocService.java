package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryAlloc;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

import java.util.Collection;
import java.util.List;

/**
 * 库存分配 服务类
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface IInventoryAllocService extends IDaddyService<InventoryAlloc> {

    List<InventoryAlloc> listByPlatformItemId(Long platformItemId);

    List<InventoryAlloc> listBySkuCode(Collection<String> skuCode);
    
    List<InventoryAlloc> listBySkuCode(String skuCode);
    
    List<InventoryAlloc> listByPlatformItemSkuId(Collection<Long> platformItemSkuIds);
    
    InventoryAlloc getByPlatformItemSkuId(Long platformItemSkuId);
    
}
