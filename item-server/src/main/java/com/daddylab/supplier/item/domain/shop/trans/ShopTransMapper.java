package com.daddylab.supplier.item.domain.shop.trans;

import com.daddylab.supplier.item.common.trans.CommaSerialTransMapper;
import com.daddylab.supplier.item.domain.shop.dto.CreateOrUpdateShopCmd;
import com.daddylab.supplier.item.domain.shop.dto.ShopDetail;
import com.daddylab.supplier.item.domain.shop.dto.ShopListItem;
import com.daddylab.supplier.item.domain.shop.entity.ShopEntity;
import com.daddylab.supplier.item.domain.shop.vo.ShopPrincipalVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Shop;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopPrincipal;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(
        imports = {Collections.class},
        uses = {CommaSerialTransMapper.class})
public interface ShopTransMapper {
    ShopTransMapper INSTANCE = Mappers.getMapper(ShopTransMapper.class);

    @Mapping(
            source = "principals",
            target = "principals",
            defaultExpression = "java(Collections.emptyList())")
    @Mapping(source = "corpType", target = "businessLine")
    ShopEntity toShopEntity(CreateOrUpdateShopCmd cmd);

    ShopEntity copy(ShopEntity shopEntity);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void copyFromCmd(@MappingTarget ShopEntity shopEntity, CreateOrUpdateShopCmd cmd);

    @Mapping(target = "kingDeeId", source = "kingDeeId")
    @Mapping(target = "businessLine", expression = "java(getBusinessLineList(shop.getBusinessLine()))")
    ShopEntity toShopEntity(Shop shop);

    Shop toShop(ShopEntity shopEntity);

    @Mapping(source = "shopPrincipalVos", target = "principals")
    @Mapping(target = "businessLine", expression = "java(getBusinessLineList(shop.getBusinessLine()))")
    ShopListItem toShopListItem(Shop shop, List<ShopPrincipalVo> shopPrincipalVos);

    default List<Integer> getBusinessLineList(String businessLineStr) {
        if (StringUtils.hasText(businessLineStr)) {
            String[] split = businessLineStr.split(",");
            return Arrays.stream(split).map(Integer::valueOf).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Mapping(target = "principals", source = "shopPrincipalVos")
    @Mapping(target = "businessLine", source = "shopEntity.businessLine")
    ShopDetail toShopDetail(ShopEntity shopEntity, List<ShopPrincipalVo> shopPrincipalVos);

    @Mapping(source = "shopId", target = "shopId")
    ShopPrincipal toShopPrincipal(Long shopId, ShopPrincipalVo dto);
}
