package com.daddylab.supplier.item.application.afterSales;

import com.alibaba.cola.dto.SingleResponse;

/**
 * 售后管理/退货仓库服务
 *
 * <AUTHOR>
 * @since 2022/10/20
 */
public interface AfterSalesAuthBizService {

    /**
     * 供应商是否允许访问指定仓库
     *
     * @param partnerProviderId P系统供应商ID
     * @param warehouseNo       仓库编号
     */
    SingleResponse<Boolean> isAllowAccessWarehouse(Long partnerProviderId, String warehouseNo);

    /**
     * 供应商是否允许访问指定退换单
     *
     * @param partnerProviderId P系统供应商ID
     * @param refundOrderNo     退换单号
     */
    SingleResponse<Boolean> isAllowAccessRefundOrder(Long partnerProviderId, String refundOrderNo);

}
