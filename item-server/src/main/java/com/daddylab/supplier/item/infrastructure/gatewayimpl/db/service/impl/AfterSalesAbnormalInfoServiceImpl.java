package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesAbnormalInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AfterSalesAbnormalInfoMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesAbnormalInfoService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 售后异常件信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-17
 */
@Service
public class AfterSalesAbnormalInfoServiceImpl extends DaddyServiceImpl<AfterSalesAbnormalInfoMapper, AfterSalesAbnormalInfo> implements IAfterSalesAbnormalInfoService {

}
