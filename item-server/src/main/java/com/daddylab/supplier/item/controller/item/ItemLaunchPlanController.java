package com.daddylab.supplier.item.controller.item;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.controller.item.dto.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemLaunchPlanService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @Author: <PERSON>
 * @Date: 2022/5/31 09:34
 * @Description: 请描述下这个类
 */
@Api(value = "商品上新计划相关API", tags = "商品上新计划相关API")
@RestController
@RequestMapping("/item-launch-plan")
public class ItemLaunchPlanController {
    @Autowired
    private IItemLaunchPlanService itemLaunchPlanService;

    @ResponseBody
    @ApiOperation("新增商品上新计划")
    @PostMapping("/add")
    public SingleResponse<String> add(@RequestBody @Valid ItemLaunchPlanParam param) {
        itemLaunchPlanService.add(param);
        return SingleResponse.of("OK");
    }

    @ResponseBody
    @ApiOperation("修改商品上新计划")
    @PostMapping("/edit")
    public SingleResponse<String> edit(@RequestBody @Valid ItemLaunchPlanParam param) {
        itemLaunchPlanService.edit(param);
        return SingleResponse.of("OK");
    }

    @ResponseBody
    @ApiOperation("修改商品上新文案和上新价格")
    @PostMapping("/editItemText")
    public SingleResponse<String> editItemText(@RequestBody @Valid ItemLaunchPlanTextParam param) {
        itemLaunchPlanService.editItemText(param);
        return SingleResponse.of("OK");
    }

    @ResponseBody
    @ApiOperation("商品上新计划分页查询")
    @PostMapping("/pageQuery")
    public PageResponse<ItemLaunchPlanPageVo> pageQuery(@RequestBody ItemLaunchPlanPageQuery query) {
        return itemLaunchPlanService.pageQuery(query);
    }

    @ResponseBody
    @ApiOperation("商品上新计划基础信息")
    @GetMapping("/viewBase")
    public SingleResponse<ItemLaunchPlanBaseVo> viewBase(@ApiParam("计划ID") Long planId) {
        return itemLaunchPlanService.getBaseInfo(planId);
    }

    @ResponseBody
    @ApiOperation("商品上新计划关联商品信息分页查询")
    @GetMapping("/pageQueryLinkItem")
    public PageResponse<ItemLaunchPlanLinkItemVo> pageQueryLinkItem(ItemLaunchPlanLinkItemPageQuery query) {
        return itemLaunchPlanService.pageQueryLinkItem(query);
    }

//    @ResponseBody
//    @ApiOperation("删除计划和商品关联关系")
//    @PostMapping("/deletePlanItemRef")
//    public SingleResponse<String> deletePlanItemRef(@RequestBody DelPlanRefParam param) {
//        itemLaunchPlanService.deletePlanItemRef(param.getPlanItemRefId());
//        return SingleResponse.of("OK");
//    }

    @ResponseBody
    @ApiOperation("删除上新计划")
    @PostMapping("/deletePlan")
    public SingleResponse<String> deletePlan(@RequestBody DelPlanParam param) {
        itemLaunchPlanService.deletePlan(param.getPlanId());
        return SingleResponse.of("OK");
    }

    @ResponseBody
    @ApiOperation("上新计划名称下拉框选择")
    @GetMapping("/nameDropDownList")
    public MultiResponse<PlanNameDropDownVo> nameDropDownList(PlanNameDropDownCmd cmd) {
        return itemLaunchPlanService.nameDropDownList(cmd.getName(), cmd.getPageIndex(), cmd.getPageSize());
    }

    @ResponseBody
    @ApiOperation("上新时间下拉框选择")
    @GetMapping("/launchTimeDownList")
    public MultiResponse<LaunchTimeDropDownVo> launchTimeDownList(LaunchTimeDropDownCmd cmd) {
        return itemLaunchPlanService.launchTimeDownList(cmd.getPageIndex(), cmd.getPageSize());
    }

    @ResponseBody
    @ApiOperation("提交上新计划")
    @GetMapping("/submit")
    public SingleResponse<Boolean> submit(Long planId) {
        return itemLaunchPlanService.submit(planId);
    }

}


