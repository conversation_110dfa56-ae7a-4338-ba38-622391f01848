package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemExpressTemplate;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemExpressTemplateMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemExpressTemplateService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 快递模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class ItemExpressTemplateServiceImpl extends DaddyServiceImpl<ItemExpressTemplateMapper, ItemExpressTemplate> implements IItemExpressTemplateService {

}
