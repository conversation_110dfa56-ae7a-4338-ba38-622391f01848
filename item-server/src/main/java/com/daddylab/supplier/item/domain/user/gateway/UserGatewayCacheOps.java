package com.daddylab.supplier.item.domain.user.gateway;


/**
 * <AUTHOR>
 */
public interface UserGatewayCacheOps {
    /**
     * 清空所有缓存
     */
    void clearAllCache();

    /**
     * 清空员工信息缓存
     */
    void clearStaffInfoCache();

    /**
     * 清空员工信息缓存
     * @param userId 指定用户ID
     */
    void clearStaffInfoCache(Long userId);

    /**
     * 清空查询员工列表缓存
     */
    void clearQueryStaffListCache();
}
