package com.daddylab.supplier.item.application.afterSales;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/25
 */
@Data
@ApiModel("售后销退确认信息VO")
public class AfterSalesConfirmInfoVO {

    /**
 	 * 确认用户ID
	 **/
	@ApiModelProperty("确认用户ID")
    private Long confirmUid;

    /**
 	 * 确认用户花名
	 **/
	@ApiModelProperty("确认用户花名")
    private String confirmUserNick;

    /**
 	 * 确认用户姓名
	 **/
	@ApiModelProperty("确认用户姓名")
    private String confirmUserName;

    /**
 	 * 确认时间
	 **/
	@ApiModelProperty("确认时间")
    private Long confirmTime;

    /**
     * 备注
     **/
    @ApiModelProperty("备注")
    private String remark;
    

   
}
