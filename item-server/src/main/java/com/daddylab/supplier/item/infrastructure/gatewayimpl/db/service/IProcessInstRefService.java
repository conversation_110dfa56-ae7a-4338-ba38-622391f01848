package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ProcessInstRef;
import com.daddylab.supplier.item.types.process.ProcessBusinessType;
import com.daddylab.supplier.item.types.process.ProcessType;

/**
 * 流程业务关联 服务类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface IProcessInstRefService extends IDaddyService<ProcessInstRef> {

    void saveProcessInstRef(
            Long businessId,
            String processInstId,
            ProcessBusinessType processBusinessType,
            ProcessType processType,
            Object data);

}
