package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTrainingMaterialsProcessRecord;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemTrainingMaterialsProcessRecordMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemTrainingMaterialsProcessRecordService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 新品商品培训资料流程处理记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Service
public class ItemTrainingMaterialsProcessRecordServiceImpl extends DaddyServiceImpl<ItemTrainingMaterialsProcessRecordMapper, ItemTrainingMaterialsProcessRecord> implements IItemTrainingMaterialsProcessRecordService {

}
