package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockOutDetail;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 其他出库单详情 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
public interface IOtherStockOutDetailService extends IDaddyService<OtherStockOutDetail> {

}
