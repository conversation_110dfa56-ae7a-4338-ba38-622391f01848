package com.daddylab.supplier.item.controller.item;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuVO;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.item.dto.*;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailPriceVo;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailRunningVo;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailVo;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemCmd;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemConfirmCmd;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemConfirmVo;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemVo;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.inject.Inject;
import javax.validation.Valid;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11 10:41 上午
 * @description
 */
@Api(value = "商品相关api", tags = "商品相关API")
@RestController()
@RequestMapping("/item")
public class ItemController {

    @Autowired
    ItemBizService itemBizService;

    @Inject
    private OperateLogBizService operateLogBizService;


    @ResponseBody
    @ApiOperation("商品名称下拉选")
    @PostMapping("/nameDropDown")
    public MultiResponse<NameDropDownVo> nameDropDown(@RequestBody NameDropDownCmd cmd) {
        return itemBizService.nameDropDownList(cmd.getName(), cmd.getPageIndex(), cmd.getPageSize(), cmd.getUseForItemLaunchPlan(), cmd.getProviderId());
    }


    @ResponseBody
    @ApiOperation("商品分页查询")
    @PostMapping("/pageQuery")
    public PageResponse<ItemPageVo> pageQuery(@RequestBody ItemPageQuery query) {
//        query.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(query.getBusinessLine()));
        return itemBizService.queryPage(query);
    }

    @ResponseBody
    @ApiOperation("商品简介信息查询")
    @PostMapping("/simpleQuery")
    public MultiResponse<ItemSimpleViewVo> simpleQuery(@RequestBody ItemSimpleViewCmd cmd) {
        return itemBizService.querySimple(cmd);
    }

    @ResponseBody
    @ApiOperation("查询商品快递模板")
    @GetMapping("/expressTemplate")
    public MultiResponse<ItemExpressVo> expressTemplate(Long itemId) {
        return itemBizService.getItemExpress(itemId);
    }

    @ResponseBody
    @ApiOperation("查询商品规格列表")
    @PostMapping("/itemSkuSpecList")
    public PageResponse<ItemSkuSpecVo> itemSkuSpecList(@RequestBody ItemSpecPageQuery query) {
        return itemBizService.getSpecList(query);
    }

    @ResponseBody
    @ApiOperation("查询商品规格详情")
    @GetMapping("/itemSkuDetail")
    public SingleResponse<ItemSkuDetailVo> itemSkuDetail(@RequestParam("itemId") Long itemId) {
        return itemBizService.getItemSkuDetail(itemId);
    }

    // ----------------------- detail start -----------------------------------

    @ResponseBody
    @ApiOperation("查看商品基础信息")
    @GetMapping("/viewBase")
    public SingleResponse<ItemDetailVo> viewBase(@ApiParam("商品id") Long itemId) {
        return itemBizService.queryDetail(itemId);
    }

    /**
     * 包含处理特殊权限
     * 1.是否可以新增编辑 价格
     * 2.是否可以新增编辑 制定编码（商品编码和sku编码）
     * <p>
     * base+special+price
     * base+price
     * base+special
     * base
     *
     * @param cmd
     * @return
     */
    @ResponseBody
    @ApiOperation("新增/编辑商品信息")
    @PostMapping("/saveBase")
    public Response saveBase(@RequestBody @Validated SaveItemCmd cmd) throws Exception {
        return itemBizService.saveItem(cmd);
    }

    @ResponseBody
    @ApiOperation("查看商品运营信息")
    @GetMapping("/viewRunning")
    public SingleResponse<ItemDetailRunningVo> viewRunning(@ApiParam("商品id") Long itemId) {
        return itemBizService.queryRunningDetail(itemId);
    }

    @ResponseBody
    @ApiOperation("编辑商品运营信息")
    @PostMapping("/updateRunning")
    public Response saveRunning(@RequestBody @Valid EditRunningCmd cmd) {
        return itemBizService.updateRunning(cmd);
    }

    @ResponseBody
    @ApiOperation("查看价格信息")
    @GetMapping("/viewPrice")
    public SingleResponse<ItemDetailPriceVo> viewPrice(@ApiParam("商品id") Long itemId) {
        return itemBizService.queryPrice(itemId);
    }

    @ResponseBody
    @ApiOperation("删除商品价格")
    @GetMapping("/deletePrice")
    public Response deletePrice(@ApiParam("价格id") Long priceId) {
        return itemBizService.removePrice(priceId);
    }

    // ------------------------------ detail end --------------------------------


    @ResponseBody
    @PostMapping("/export")
    @ApiOperation("商品查询导出")
    public Response exportItem(@RequestBody ExportCmd cmd) {
        itemBizService.exportItem(cmd);
        return Response.buildSuccess();
    }

    @ResponseBody
    @PostMapping("/queryPartnerItem")
    @ApiOperation("查询合作伙伴系统的商品")
    public MultiResponse<PartnerItemVo> queryPartnerItem(@Validated @RequestBody PartnerItemCmd cmd) {
        return itemBizService.queryPartnerItem(cmd);
    }

    @ResponseBody
    @PostMapping("/confirmQueryPartnerItem")
    @ApiOperation("确定合作伙伴系统的商品")
    public SingleResponse<PartnerItemConfirmVo> confirmQueryPartnerItem(@Validated @RequestBody PartnerItemConfirmCmd cmd) {
        return itemBizService.confirmPartnerItem(cmd);
    }

    @GetMapping(value = "/operateLogs")
    @ApiOperation("操作日志")
    public Response operateLogs(Long targetId) {
        return operateLogBizService.getOperateLogs(
                Arrays.asList(OperateLogTarget.ITEM, OperateLogTarget.ITEM_UP, OperateLogTarget.ITEM_DOWN,
                        OperateLogTarget.ITEM_DOWN_TEMPORARY, OperateLogTarget.ITEM_STATUS), targetId);
    }

    @GetMapping(value = "/priceLogs")
    @ApiOperation("动价记录")
    @Auth(value = GlobalConstant.PREVIEW_PURCHASE_PRICE)
    public Response priceLogs(Long targetId) {
        return operateLogBizService.getOperateLogs(OperateLogTarget.ITEM_PRICE, targetId);
    }

    @ResponseBody
    @ApiOperation("sku明细分页列表")
    @PostMapping("/skuPage")
    public PageResponse<ComposeSkuVO> pageSku(@RequestBody ComposeSkuPageQuery query) {
        query.setWithPrice(UserPermissionJudge.canViewComposerSkuPrice());
        return itemBizService.pageSku(query);
    }

    @ResponseBody
    @ApiOperation("sku成本价格列表")
    @GetMapping("/skuPriceList")
    public MultiResponse<ItemSkuPriceVO> skuItemPriceList(@RequestParam("skuCode") String skuCode,
                                                          @RequestParam(value = "type", required = false)
                                                          @ApiParam(value = "sku价格类型。0 成本价默认值，1 销售价。2 合同销售价。3 平台佣金") Integer type) {
        return itemBizService.skuPriceList(skuCode, type);
    }

    @ResponseBody
    @ApiOperation("item成本价格列表")
    @GetMapping("/itemPriceList")
    public MultiResponse<ItemPriceVO> itemPriceList(@RequestParam("itemId") Long itemId
            , @RequestParam("priceName") String priceName) {
        return itemBizService.itemCostPriceList(itemId, priceName);
    }

    @ResponseBody
    @ApiOperation("/删除商品关联的P系统商品")
    @GetMapping("/deletePartnerSn")
    public SingleResponse<Boolean> deletePartnerSn(@RequestParam("itemId") Long itemId) {
        return SingleResponse.of(itemBizService.deletePartnerSn(itemId));
    }

    @ResponseBody
    @ApiOperation("sku快速查询")
    @GetMapping("/querySku")
    public SingleResponse<ShortSkuVO> querySku(@RequestParam("skuCode") String skuCode) {
        return itemBizService.querySku(skuCode.trim());
    }


    @ResponseBody
    @ApiOperation("sku快速删除")
    @GetMapping("/deleteSku")
    @Auth(noAuth = true)
    public SingleResponse<Boolean> deleteSku(@RequestParam("skuCode") String skuCode) {
        return itemBizService.deleteSku(skuCode);
    }

    /**
     * 同步P系统的商品技术类目。
     */
    @GetMapping("/syncItemPartnerSysType")
    @Auth("/PERMISSION_OF_ADMINISTRATOR")
    public void syncItemPartnerSysType() {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            itemBizService.queryAllItemPartnerSysType();
        });
    }


    @PostMapping("/syncPsysItemInfo")
    @Auth("/PERMISSION_OF_ADMINISTRATOR")
    public void syncPsysItemInfo() {
        itemBizService.syncPsysItemInfo();
    }

    @ApiOperation("SPU弹窗分页查询")
    @PostMapping("/spuWindowPage")
    public PageResponse<SpuWindowPageVo> spuWindowPage(@RequestBody SpuWindowPageQuery pageQuery) {
        return itemBizService.spuWindowsPageQuery(pageQuery);
    }

}
