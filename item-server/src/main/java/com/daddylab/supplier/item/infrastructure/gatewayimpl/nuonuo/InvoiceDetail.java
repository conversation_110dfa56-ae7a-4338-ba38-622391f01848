package com.daddylab.supplier.item.infrastructure.gatewayimpl.nuonuo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2024年08月20日 10:31 AM
 */
@Data
public class InvoiceDetail {
    @JsonIgnore
    Integer i;

    private String goodsName;
    private String goodsCode;
    private String selfCode;
    private String unit;
    private String withTaxFlag;
    private String price;
    private String num;
    private String favouredPolicyFlag;
    private String taxRate;
    private String tax;
    private String taxExcludedAmount;
    private String taxIncludedAmount;
    private String invoiceLineProperty;
    private String dField1 = "dField1";
    private String dField2 = "dField2";
    private String dField3 = "dField3";
    private String dField4 = "dField4";
    private String dField5 = "dField5";
}
