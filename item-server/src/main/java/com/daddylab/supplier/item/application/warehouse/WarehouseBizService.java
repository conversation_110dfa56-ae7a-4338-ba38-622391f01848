package com.daddylab.supplier.item.application.warehouse;

import com.alibaba.cola.dto.SingleResponse;
//import com.daddylab.supplier.item.application.virtualWarehouse.dto.SkuStockChangeDto;
import com.daddylab.supplier.item.controller.common.dto.OrderPersonnelCmd;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.daddylab.supplier.item.types.warehouse.EditWarehouseCmd;
import java.util.Optional;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2022/4/22
 */
public interface WarehouseBizService {

    Optional<Long> warehouseId(String warehouseNo);

    Optional<Warehouse> getWarehouse(String warehouseNo);

    Optional<Warehouse> cacheGetWarehouse(String warehouseNo, boolean refresh);

    void editWarehouse(EditWarehouseCmd cmd);

//    /**
//     * 刷新共享模式的SKU二级库存占比
//     *
//     * @param ignoreVwIdList
//     * @param ignoreShopIdList
//     * @param warehouseNo
//     */
//    void refreshSharedGoodsInventoryRatio(Collection<Long> ignoreVwIdList, Collection<Long> ignoreShopIdList, String warehouseNo);

//    /**
//     * 库存同步，仓库下属SKU发生了变化
//     *
//     * @param warehouseNo
//     * @param skuCodes
//     */
//    void stockSkuChangeHandler(String warehouseNo, Collection<SkuStockChangeDto> skuCodes, Integer type);


    SingleResponse<String> warehouseInfoImportExcel();

    SingleResponse<Boolean> importWarehouse(MultipartFile multipartFile);


    SingleResponse<Boolean> batchUpdateOrderPerson(OrderPersonnelCmd cmd);

}
