package com.daddylab.supplier.item.controller.category.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/11/9
 */
@Data
public class CategoryTree {
    private List<CategoryTreeNode> treeNodes;
    private Map<Long, CategoryTreeNode> nodeMap;

    public Optional<CategoryTreeNode> getByCategoryId(long categoryId) {
        return Optional.ofNullable(nodeMap.get(categoryId));
    }
}
