package com.daddylab.supplier.item.application.offShelf.dto;

import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OffShelfStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OffShelfReasonType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OffShelfUrgentLevel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/20
 */
@Data
public class OffShelfPageVO {
    @ApiModelProperty("id")
    private Long id;

    /**
     * 下架流程编码
     */
    @ApiModelProperty("流程编号")
    private String no;

    @ApiModelProperty("下架状态")
    private OffShelfStatus status;

    /**
     * 下架理由
     */
    @ApiModelProperty("原因描述")
    private String reasonTxt;

    @ApiModelProperty("下架原因")
    private List<OffShelfReasonType> reasonType;

    @ApiModelProperty("紧急程度")
    private OffShelfUrgentLevel urgentLevel;

    @ApiModelProperty("申请人")
    private StaffBrief applicant;

    @ApiModelProperty("申请时间")
    private Long applyTime;

    @ApiModelProperty("下架商品编码")
    private List<OffShelfItemVO> itemList;

    @ApiModelProperty("下架运营")
    private List<StaffBrief> operatorUsers;
}
