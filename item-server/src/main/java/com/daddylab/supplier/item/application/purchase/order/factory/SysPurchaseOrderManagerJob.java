package com.daddylab.supplier.item.application.purchase.order.factory;

import cn.hutool.core.collection.ListUtil;
import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.order.settlement.sys.OrderSettlementSysService;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.PriceEngineManager;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.PurchaseBillService;
import com.daddylab.supplier.item.controller.test.DateScriptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static com.daddylab.supplier.item.common.GlobalConstant.SEVEN_UP;
import static com.daddylab.supplier.item.common.GlobalConstant.YUN_TUN;

/**
 * <AUTHOR> up
 * @date 2022年09月01日 5:23 PM
 */
@Service
@Slf4j
public class SysPurchaseOrderManagerJob {

    @Resource
    WdtStockOrderHandler cleaningConverter;

    @Resource
    ErpPurchaseOrderHandler sysPurchaseOrderGenerator;

    @Resource
    PriceEngineManager engineManager;

    @Resource
    DateScriptService dateScriptService;

    @Resource
    PurchaseBillService purchaseBillService;

    @Resource
    OrderSettlementSysService orderSettlementSysService;

    private static final String SPLIT_FLAG = ",";

    @Resource
    CommonUtil commonUtil;

    @XxlJob("sysPurchaseOrderManagerJob")
    public void run() {
        TimeBO timeBo;
        int type = 0;
        String dateParam;
        try {
            String jobParam = XxlJobHelper.getJobParam();
            if (jobParam.contains(SPLIT_FLAG)) {
                String[] split = jobParam.split(SPLIT_FLAG);
                type = StringUtils.hasText(split[1]) ? Integer.parseInt(split[1]) : 0;
                dateParam = split[0];
            } else {
                dateParam = jobParam;
            }
            timeBo = getTimeBo(dateParam);
        } catch (Exception e) {
            throw new RuntimeException("日期解析异常", e);
        }

        /**
         * 1.将所有sku的物料单位清洗一遍，以金蝶为准
         * --------------
         * 第一步从wdt原始订单中洗出业务需要的数据，中间态数据wrapper。db:wdt_order_detail_wrapper。生成目标月的wrapper数据之前，会将所有wrapper数据物理删除。
         * 出入库数据以单品基础价为准。
         * --------------
         * 价格清洗,主要处理了出库单的价格数据
         * --------------
         *  根据供应商纬度切分wrapper数据，然后聚合skuCode相同的数据，再根据price的不同，进行汇总统计。
         *  单一provider下，skuCode+price 组成唯一key
         *  每一次汇总，统计前，会将此provider在此月份已经生成的汇总信息物理删除
         *  --------------
         *  两个动作业务逻辑上捆绑，生成采购单时会删除，同月份上一次生成的采购单和入库单。
         *  --------------
         *  根据中间态的wdt_order_detail_wrapper表数据，按照供应链部-采购组的业务要求，生成账单数据。
         *  --------------
         *  生成结算采购单
         *  *
         *  ****** 请按顺序执行下面的方法
         */
        // 默认模式
        if (type == 0) {
            log.info("---- 默认模式开始 ----");
//            dateScriptService.clearSkuUnit();
            cleaningConverter.runClean(timeBo);
            commonUtil.remind(timeBo.getOperateMonth(), ListUtil.of(SEVEN_UP, YUN_TUN), "WDT出入库单数据清洗完毕。");

            engineManager.start(timeBo.getOperateMonth());
            commonUtil.remind(timeBo.getOperateMonth(), ListUtil.of(SEVEN_UP, YUN_TUN), "价格清洗链路执行完毕。");

            // 销退单据的价格更新，以正向单据为准。
//            engineManager.updateRefundWrapperPrice(timeBo.getOperateMonth());
//            commonUtil.remind(timeBo.getOperateMonth(), ListUtil.of(GlobalConstant.SEVEN_UP),"销退单据价格更新完毕。");

            sysPurchaseOrderGenerator.runStatistics(timeBo.getOperateMonth());
            commonUtil.remind(timeBo.getOperateMonth(), ListUtil.of(SEVEN_UP, YUN_TUN), "数据合并统计完毕。");

            sysPurchaseOrderGenerator.purchaseOrderGenerate(timeBo);
            commonUtil.remind(timeBo.getOperateMonth(), ListUtil.of(SEVEN_UP, YUN_TUN), "采购订单生成完毕。");


            // 点击此处链接，生成出入库单，并且单据推送金蝶。
//            commonUtil.remainWithUrl(timeBo.getOperateMonth(),
//                    "https://supplierapi-gray.daddylab.com/supplier/item/purchaseOrderCompensation/stockOrderGenerate?targetMoth=" + timeBo.getOperateMonth(),
//                    ListUtil.of(SEVEN_UP, YUN_TUN), "点击此处链接，自动生成出、入库单据并且推送金蝶。");
//
//            // 点击此处链接，生成采购结算总账单
//            commonUtil.remainWithUrl(timeBo.getOperateMonth(),
//                    "https://supplierapi-gray.daddylab.com/supplier/item/purchaseOrderCompensation/createBill?operateTime=" + timeBo.getOperateMonth(),
//                    ListUtil.of(SEVEN_UP, YUN_TUN), "点击此处链接，自动生成采购结算总对账单。");
//
//            // 点击此处链接，生成结算订单，并且通知【艾米】
//            commonUtil.remainWithUrl(timeBo.getOperateMonth(),
//                    "https://supplierapi-gray.daddylab.com/supplier/item/purchaseOrderCompensation/settlement?no=&targetMoth=" + timeBo.getOperateMonth(),
//                    ListUtil.of(SEVEN_UP, YUN_TUN), "点击此处链接，自动生成【待结算】工厂订单");


            // /purchaseOrderCompensation/stockOrderCompensate

            // 采购订单下游 出入库单，应付单
//            sysPurchaseOrderGenerator.erpStockInOrOutOrderGenerator(timeBo, null);
//            commonUtil.remindStockOrder(timeBo.getOperateMonth(), ListUtil.of(GlobalConstant.SEVEN_UP, GlobalConstant.YUN_TUN));

            // 总账单
//            StopWatch stopWatch = new StopWatch();
//            stopWatch.start();
//            String billUrl = purchaseBillService.createBill(timeBo.getOperateMonth());
//            stopWatch.stop();
//            long total = stopWatch.getTotal(TimeUnit.MINUTES);
//            commonUtil.remainBill(timeBo.getOperateMonth(), billUrl, ListUtil.of(GlobalConstant.SEVEN_UP, GlobalConstant.YUN_TUN), total);

            // 采购结算单
//            orderSettlementSysService.autoCreateOrderSettlementInfo(timeBo, null);
//            commonUtil.remainOrderSettlement(timeBo.getOperateMonth(), ListUtil.of(GlobalConstant.SEVEN_UP));
        }
        // test模式
        else if (type == 1) {
            log.info("---- 测试模式开始 ----");
//            cleaningConverter.runClean(timeBo);
            engineManager.start(timeBo.getOperateMonth());
            sysPurchaseOrderGenerator.runStatistics(timeBo.getOperateMonth());

            sysPurchaseOrderGenerator.purchaseOrderGenerate(timeBo);
            commonUtil.remindPurchaseOrder(timeBo.getOperateMonth(), ListUtil.of(SEVEN_UP));

//            String billUrl = purchaseBillService.createBill(timeBo.getOperateMonth());
//            commonUtil.remainBill(timeBo.getOperateMonth(), billUrl, ListUtil.of(GlobalConstant.SEVEN_UP));

            orderSettlementSysService.autoCreateOrderSettlementInfo(timeBo, null);
            commonUtil.remainOrderSettlement(timeBo.getOperateMonth(), ListUtil.of(SEVEN_UP));
        }

    }

    private TimeBO getTimeBo(String jobParam) {
        TimeBO thisTimeBo;
        if (StringUtils.hasText(jobParam)) {
            thisTimeBo = TimeBO.of(jobParam);
        } else {
            String runningMonth = LocalDateTime.now().plusMonths(-1).format(DateTimeFormatter.ofPattern("yyyyMM"));
            thisTimeBo = TimeBO.of(runningMonth);
        }
        return thisTimeBo;
    }


}
