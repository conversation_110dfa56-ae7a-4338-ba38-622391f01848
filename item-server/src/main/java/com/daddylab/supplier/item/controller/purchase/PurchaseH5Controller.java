package com.daddylab.supplier.item.controller.purchase;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.purchase.PurchaseBizService;
import com.daddylab.supplier.item.common.domain.dto.CodeCmd;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseDetail;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @ClassName PurchaseH5Controller.java
 * @description
 * @createTime 2021年11月19日 13:38:00
 */
@Slf4j
@Api(value = "采购h5相关API", tags = "采购h5相关API")
@RestController
@RequestMapping("/purchase/h5")
public class PurchaseH5Controller {

    @Autowired
    private PurchaseBizService purchaseBizService;


    @ResponseBody
    @ApiOperation(value = "根据加密码显示供应商页面")
    @GetMapping("/getDetailByCode")
    public MultiResponse<PurchaseDetail> getDetailByCode(@RequestHeader("Authorization") String token , @Valid CodeCmd cmd) {
        token = StringUtil.trimStart(token, "Bearer ");
        return purchaseBizService.getDetailByCode(cmd.getCode(), token);
    }

    @ResponseBody
    @ApiOperation(value = "根据加密码获取供应商手机号")
    @GetMapping("/getMobileByCode")
    public SingleResponse<String> getMobileByCode(@ApiParam("加密码") String code) {
//        return SingleResponse.of("155****4534");
        return purchaseBizService.getMobileByCode(code);
    }

    @ResponseBody
    @ApiOperation(value = "获取验证码")
    @PostMapping("/getSmsCode")
    public Response getSmsCode(@RequestBody @Valid CodeCmd cmd) {
        return purchaseBizService.getSmsCode(cmd.getCode());
    }

    @ResponseBody
    @ApiOperation(value = "校验验证码存token")
    @PostMapping("/checkCode")
    public SingleResponse<String> checkCode(@RequestBody @Valid CodeCmd cmd) {
        return purchaseBizService.checkCode(cmd.getCode(), cmd.getVerifyCode());
    }

    @ResponseBody
    @ApiOperation(value = "价格达成协议")
    @PostMapping("/agree")
    public Response agree(@RequestHeader("Authorization") String token ,@RequestBody @Valid CodeCmd cmd) {
        token = StringUtil.trimStart(token, "Bearer ");
        return purchaseBizService.agree(token,cmd.getId(),cmd.getCode());
    }


}
