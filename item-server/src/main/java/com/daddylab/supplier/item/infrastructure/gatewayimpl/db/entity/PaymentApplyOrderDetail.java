package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Data;
import lombok.EqualsAndHashCode;

import org.javers.core.metamodel.annotation.*;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.Id;
import org.javers.core.metamodel.annotation.PropertyName;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 采购管理-付款申请单-付款明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TypeName("付款申请单明细")
public class PaymentApplyOrderDetail implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @DiffIgnore
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @DiffIgnore
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 更新时间
     */
    @DiffIgnore
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 创建者
     */
    @DiffIgnore
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新者
     */
    @DiffIgnore
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 删除时间
     */
    @DiffIgnore
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 是否删除
     */
    @DiffIgnore
    @TableLogic
    private Integer isDel;

    /**
     * 采购管理-付款申请单id
     */
    @DiffIgnore
    private Long paymentApplyOrderId;

    /**
     * 采购管理-付款申请单no
     */
    @DiffIgnore
    private String paymentApplyOrderNo;

    /**
     * 付款明细来源。0：采购单，1：结算单
     */
    @PropertyName("付款明细来源")
    @DiffIgnore
    private Integer detailSource;

    @PropertyName("付款明细来源Str")
    @TableField(exist = false)
    private String detailSourceStr;

    /**
     * 付款申请关联单据编码。采购单号/结算单号
     */
    @PropertyName("关联单据编码")
    @Id
    private String applyRelatedNo;

    /**
     * 付款申请关联单据编码。采购单id/结算单id
     */
    @DiffIgnore
    private Long applyRelatedId;

    @PropertyName("应付金额")
    private BigDecimal rightAmount;

    /**
     * '申请付款金额'
     */
    @PropertyName("申请付款金额")
    private BigDecimal applyAmount;

    /**
     * '申请付款金额备注'
     */
    @PropertyName("申请付款金额备注")
    private String applyAmountRemark;

    /**
     * 关联单据的明细ID
     */
    @PropertyName("关联单据的明细ID")
    private String relateDetailId;


}
