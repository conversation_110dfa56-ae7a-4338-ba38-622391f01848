package com.daddylab.supplier.item.infrastructure.oa;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.FeignLog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import static com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.FeignLog.OA;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@FeignClient(
        name = "OaClient",
        url = "${oa-server.url}",
        configuration = OaFeignConfiguration.class)
public interface OaClient {

    @RequestMapping(
            method = RequestMethod.GET,
            path = "/seeyon/rest/token/gylfile/e8051b82-a5f3-4bc1-ab51-3cdabcd6ce41")
    String token(@RequestParam(value = "loginName") String loginName);

    @RequestMapping(method = RequestMethod.POST, path = "/seeyon/rest/bpm/process/start")
    @FeignLog(type = OA)
    OaResponse<OaProcessStartResponse> processStart(
            @RequestParam(value = "token") String token,
            @RequestBody OaProcessStartRequest request);

    @RequestMapping(
            method = RequestMethod.POST,
            path = "/seeyon/rest/attachment",
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    OaUploadAttachmentResponse uploadAttachment(
            @RequestParam(value = "token") String token, @RequestPart("file") MultipartFile file);

    @RequestMapping(method = RequestMethod.POST, path = "/seeyon/rest/affair/cancel")
    @FeignLog(type = OA)
    boolean processCancel(
            @RequestParam(value = "token") String token, OaProcessCancelRequest request);

    @RequestMapping(method = RequestMethod.POST, path = "/seeyon/rest/affair/stop")
    @FeignLog(type = OA)
    boolean processStop(@RequestParam(value = "token") String token, OaProcessStopRequest request);
}
