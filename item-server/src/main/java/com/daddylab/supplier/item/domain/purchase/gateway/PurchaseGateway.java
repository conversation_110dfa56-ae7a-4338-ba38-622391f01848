package com.daddylab.supplier.item.domain.purchase.gateway;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Purchase;

/**
 * <AUTHOR>
 * @ClassName PurchaseGateway.java
 * @description
 * @createTime 2021年11月11日 15:23:00
 */
public interface PurchaseGateway {

    /**
     * 保存数据
     *
     * @param purchase
     */
    void saveDo(Purchase purchase);


    /**
     * 根据Id删除
     *
     * @param purchaseId
     */
    void removeById(Long purchaseId);

    /**
     * 修改数据
     *
     * @param purchase
     */
    void update(Purchase purchase);

}
