package com.daddylab.supplier.item.infrastructure.doudian;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * {
// * "spec_detail_name1": "红色",
// * "spec_detail_name2": "S",
// * "spec_detail_name3": "",
 * "stock_num": 11,
 * "price": 100,
 * "code": "",
 * "step_stock_num": 0,
 * "supplier_id": "",
 * "outer_sku_id": "",
 * "delivery_infos": [
 * {
 * "info_type": "weight",
 * "info_value": "100",
 * "info_unit": "mg"
 * }
 * ]
 * }
 *
 * sku详情，数量应该等于规格1规格2规格3，sku数量和规格组合数必须一致 sku不可售时，库存可设置为0。price单位为分。
 * delivery_infos为SKU物流信息，info_value为字符串类型（示例："12"），info_type填weight，info_unit支持mg,g,kg，超市小时达场景主品用普通库存，
 * 子品用区域库存（"sku_type": 1 // 区域库存，"stock_num_map": {"123": 99999 // 门店ID:库存数量}）
 *
 * <AUTHOR> up
 * @date 2022年09月19日 9:59 AM
 */
@Data
public class SpecPrice {

    @JsonProperty("spec_detail_name1")
    private String specDetailName1;

    @JsonProperty("spec_detail_name2")
    private String specDetailName2;

    @JsonProperty("spec_detail_name3")
    private String specDetailName3;

    @JsonProperty("stock_num")
    private Integer stockNum;

    @JsonProperty("price")
    private BigDecimal price;

    @JsonProperty("code")
    private String code;

    @JsonProperty("step_stock_num")
    private Integer stepStockNum;

    @JsonProperty("supplier_id")
    private String supplierId;

    @JsonProperty("outer_sku_id")
    private String outerSkuId;

    @JsonProperty("delivery_infos")
    private List<DeliveryInfo> deliveryInfos;

    @Data
    public static class DeliveryInfo {
        @JsonProperty("info_type")
        private String infoType;

        @JsonProperty("info_value")
        private String infoValue;

        @JsonProperty("info_unit")
        private String infoUnit;
    }

}
