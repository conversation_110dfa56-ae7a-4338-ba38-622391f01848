package com.daddylab.supplier.item.infrastructure.bizLevelDivision;

import cn.hutool.core.collection.CollectionUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BizLevelDivision;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BizUnionTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2025年02月07日 6:01 PM
 */
@Data
@ApiModel("层级划分")
public class LevelDivisionDto {

    /**
     * ID
     */
    @ApiModelProperty(value = "业务ID")
    private Long id;
    /**
     * 编码
     */
    @ApiModelProperty(value = "业务编码")
    private String code;
    /**
     * 类型
     */
    @ApiModelProperty(value = "业务类型")
    private BizUnionTypeEnum idType;


    // ----------------------------------------------------


    /**
     * 合作方 层级
     */
    @ApiModelProperty(value = "层级：合作方")
    private List<Integer> corpType;

    /**
     * 业务类型 层级
     */
    @ApiModelProperty(value = "层级：业务类型")
    private List<Integer> bizType;

    /**
     * 运营类型 层级
     */
    @ApiModelProperty(value = "层级：运营类型（店铺独有）")
    private List<Integer> runType;


    public static void fillLevelVal(LevelDivisionDto levelDivisionDto, List<BizLevelDivision> bizLevelDivisionList) {
        if (CollectionUtil.isEmpty(bizLevelDivisionList)) return;

        final List<Integer> corpTypeVal = bizLevelDivisionList.stream()
                .filter(val -> val.getLevel().equals(DivisionLevelEnum.COOPERATION))
                .map(val -> val.getLevelVal().getValue()).collect(Collectors.toList());
        levelDivisionDto.setCorpType(corpTypeVal);

        final List<Integer> bizTypeVal = bizLevelDivisionList.stream()
                .filter(val -> val.getLevel().equals(DivisionLevelEnum.BUSINESS_TYPE))
                .map(val -> val.getLevelVal().getValue()).collect(Collectors.toList());
        levelDivisionDto.setBizType(bizTypeVal);

        final List<Integer> runTypeVal = bizLevelDivisionList.stream()
                .filter(val -> val.getLevel().equals(DivisionLevelEnum.RUNNING_MODEL))
                .map(val -> val.getLevelVal().getValue()).collect(Collectors.toList());
        levelDivisionDto.setRunType(runTypeVal);

    }


}
