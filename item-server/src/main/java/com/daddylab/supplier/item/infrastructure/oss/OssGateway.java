package com.daddylab.supplier.item.infrastructure.oss;

import com.aliyun.oss.model.ObjectMetadata;
import com.daddylab.supplier.item.infrastructure.oss.models.SaveAs;
import com.daddylab.supplier.item.infrastructure.oss.models.VideoSnapshots;

import java.io.InputStream;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/10/11
 */
public interface OssGateway {
    String put(boolean isPrivate, String path, InputStream input);

    ObjectMetadata metadata(boolean isPrivate, String path);

    Map<String, String> imageInfo(boolean isPrivate, String path);

    void delete(boolean isPrivate, String path);

    String generatePresignedUrl(boolean isPrivate, String path);

    String generatePresignedUrl(boolean isPrivate, String path, int expireSeconds);
    String generatePresignedUrl(boolean isPrivate, String path, int expireSeconds, String responseFileName);

    void videoSnapshots(
            boolean isPrivate, String path, String saveAs);

    void videoSnapshots(
            boolean isPrivate, String path, VideoSnapshots videoSnapshots, SaveAs saveAs);

    String sign(Map<String, String> params);

//    String getPrivateFileDownloadUrl(String url);
}
