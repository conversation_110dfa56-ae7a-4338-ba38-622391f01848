package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSkuAttrRef;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemSkuAttrRefMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuAttrRefService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品SKU关联属性 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class ItemSkuAttrRefServiceImpl extends DaddyServiceImpl<ItemSkuAttrRefMapper, ItemSkuAttrRef> implements IItemSkuAttrRefService {

}
