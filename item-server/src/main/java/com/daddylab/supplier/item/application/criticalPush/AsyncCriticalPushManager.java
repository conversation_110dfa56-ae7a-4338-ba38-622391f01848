package com.daddylab.supplier.item.application.criticalPush;

import java.io.Serializable;

/**
 * 关键推送管理器
 *
 * <AUTHOR>
 * @since 2022/4/15
 */
public interface AsyncCriticalPushManager extends Serializable {

    /**
     * 数据推送统一管理（适配目标方法签名为单个入参有返回值）
     *
     * @param sourceType 数据推送源
     * @param sourceId   来源ID
     * @param targetType 推送目标
     * @param mid   执行方法封装
     * @throws CriticalPushException 推送失败，上层必须要处理这个异常
     */
     default void push(SourceType sourceType, long sourceId, TargetType targetType,
             MethodInvocationDescriptor mid) throws CriticalPushException {
         push(sourceType, sourceId, targetType, mid, null);
     }

    /**
     * 数据推送统一管理（适配目标方法签名为单个入参有返回值）
     *
     * @param sourceType 数据推送源
     * @param sourceId   来源ID
     * @param targetType 推送目标
     * @param mid   执行方法封装
     * @param pushId     推送记录ID，携带此参数说明是重试
     * @throws CriticalPushException 推送失败，上层必须要处理这个异常
     */
     void push(SourceType sourceType, long sourceId, TargetType targetType,
            MethodInvocationDescriptor mid, Long pushId) throws CriticalPushException;
}
