package com.daddylab.supplier.item.application.shop.event;


import com.daddylab.supplier.item.application.common.event.EntityChangeEvent;
import com.daddylab.supplier.item.common.domain.EntityChange;
import org.javers.core.diff.Diff;

public class ShopChangeEvent <T> extends EntityChangeEvent<T> {

    public ShopChangeEvent(Long operatorId, Long targetId, EntityChange<T> entityChange) {
        super(operatorId, targetId, entityChange);
    }

    public static <T> ShopChangeEvent<T> ofNew(Long operatorId, Long targetId, T newEntity) {
        return new ShopChangeEvent<>(operatorId, targetId, EntityChange.ofAdd(newEntity));
    }

    public static <T> EntityChangeEvent<T> ofUpdate(Long operatorId, Long targetId, Diff diff, T oldEntity, T newEntity) {
        return new ShopChangeEvent<>(operatorId, targetId, EntityChange.ofUpdate(diff, oldEntity, newEntity));
    }
}
