package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtLogisticsTrace;

import java.util.Collection;
import java.util.Map;

/**
 * <p>
 * 旺店通物流轨迹 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
public interface IWdtLogisticsTraceService extends IDaddyService<WdtLogisticsTrace> {

    Map<String, Long> mapIdByLogisticsNos(Collection<String> logisticsNos);

    Long mapIdByLogisticsNo(String logisticsNo);
}
