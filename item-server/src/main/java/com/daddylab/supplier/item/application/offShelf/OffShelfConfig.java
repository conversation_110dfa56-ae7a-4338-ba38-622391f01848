package com.daddylab.supplier.item.application.offShelf;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/12/10
 */
@ConfigurationProperties(prefix = "off-shelf")
@Configuration
@RefreshScope
@Data
public class OffShelfConfig {
    private String processDefId = "offShelf";
}
