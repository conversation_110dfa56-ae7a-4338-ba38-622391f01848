package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AwsApprovalNode;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 炎黄盈动节点审批数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
public interface IAwsApprovalNodeService extends IDaddyService<AwsApprovalNode> {

}
