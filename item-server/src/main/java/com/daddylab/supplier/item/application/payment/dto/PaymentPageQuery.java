package com.daddylab.supplier.item.application.payment.dto;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PaymentOrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年11月20日 2:42 PM
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("付款单列表查选参数")
public class PaymentPageQuery extends PageQuery {

    private static final long serialVersionUID = 9099527235169102547L;
    @ApiModelProperty("付款单号")
    private String no;

    @ApiModelProperty("采购类型。0：标准采购，1：工厂代发")
    private Integer purchaseType;

    @ApiModelProperty("申请日期开始")
    private Long applyTimeStart;

    @ApiModelProperty("申请日期结束")
    private Long applyTimeEnd;

    @ApiModelProperty("收款单位id")
    private List<Long> payeeUnit;

    @ApiModelProperty("付款组织")
    private String payOrg;

    @ApiModelProperty("期望付款时间")
    private Long expectedPayTimeStart;

    @ApiModelProperty("期望付款时间")
    private Long expectedPayTimeEnd;

    @ApiModelProperty("采购员用户ID")
    private List<Long> buyerId;

    @ApiModelProperty("关联单据编码。采购单编码或者是结算单编码")
    private String relatedNo;

    private List<Long> ids;

    private PaymentOrderStatus status;

    private Integer viewAll;

    @ApiModelProperty("付款完成时间-起始")
    private Long finishPayStartTime;

    @ApiModelProperty("付款完成时间-结束")
    private Long finishPayEndTime;

    /**
     * 合作模式
     */
    @ApiModelProperty("合作模式（业务线）筛选（多选）")
    private List<Integer> businessLine;

}
