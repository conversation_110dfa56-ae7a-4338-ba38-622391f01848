package com.daddylab.supplier.item.application.saleItem.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.Data;

/**
 * 对比 NewGoodsVO add ：itemCode,homePageText
 *
 * <AUTHOR> up
 * @date 2022年07月27日 1:45 PM
 */
@Data
@ApiModel("新品商品返回spu封装")
public class NewGoodsSpuVO {

  private Long newGoodsDbId;

  @ApiModelProperty(value = "产品标准名")
  private String standardName;

  private Long itemId;

  @ApiModelProperty(value = "商品编码")
  private String itemCode;

  @ApiModelProperty(value = "采购品名/商品名称")
  private String name;

  @ApiModelProperty(value = "商品图片")
  private String image;

  @ApiModelProperty(value = "商品状态 1待选择 2待完善 3待设计 41待法务审核 42待QC审核 5待修改 6待上架 7已上架")
  private Integer status;

  @ApiModelProperty("商品审核状态 0:未进入审核流程/未知 5:待法务审核 10:待QC审核 100:审核完成")
  private Integer auditStatus;

  private Long categoryId;

  @ApiModelProperty(value = "品类")
  private String category;

  private Long brandId;

  @ApiModelProperty(value = "品牌")
  private String brand;

  /** 采购负责人id */
  private Long buyerId;

  /** 产品负责人id */
  private Long principalId;

  /** 法务负责人id */
  private Long legalId;

  /** QC负责人ids */
  private String qcIds;

  @ApiModelProperty(value = "采购负责人信息")
  private DadStaffVO buyer;

  @ApiModelProperty(value = "产品负责人信息")
  private DadStaffVO principal;

  @ApiModelProperty(value = "法务负责人信息")
  private DadStaffVO legal;

  @ApiModelProperty(value = "QC负责人信息")
  private List<DadStaffVO> qcs = Collections.emptyList();

  @ApiModelProperty(value = "首页文案")
  private String homePageText;

  @ApiModelProperty(value = "预计上架日期")
  private Long shelfTime;

  @ApiModelProperty(value = "发货类型 0:仓库发货 1:工厂发货")
  private Integer shipmentType;

  @ApiModelProperty(value = "发货地")
  private String shipmentArea;

  @ApiModelProperty(value = "48小时发货 0:是 1:否")
  private Integer shipmentAging;

  @ApiModelProperty(value = "物流")
  private String logistics;

  @ApiModelProperty(value = "快递模板")
  private String expressTemplate;

  @ApiModelProperty(value = "是否支持7天无理由退换 0:是 1:否")
  private Integer noReason;

  @ApiModelProperty(value = "备注")
  private String remark;

  @ApiModelProperty(value = "上新计划ID")
  private Long planId;

  @ApiModelProperty(value = "上新计划名称")
  private String planName;

  @ApiModelProperty(value = "上新时间")
  private String launchDate;

  @ApiModelProperty(value = "商品类型。1：内部，2：外包")
  private Integer itemType;

  @ApiModelProperty("P系统技术类目 1 日化 2 食品 3 纺织鞋类 4 玩具 5 电器 6轻工百货")
  private Integer partnerSysType;

  @ApiModelProperty("直播话术状态 0:未进入审核流程/未知 5:待法务审核 10:待QC审核 100:审核完成")
  private Integer liveVerbalTrickStatus;

  @ApiModelProperty("0供应商 1老爸抽检 2绿色家装 3商家入驻")
  private Integer businessLine;

  @ApiModelProperty("0供应商 1老爸抽检 2绿色家装 3商家入驻")
  private List<Integer> businessLines;

  @ApiModelProperty("商品拓展属性（键名）")
  private List<String> propKeys;

  @ApiModelProperty("是否黑名单 0否 1是")
  private Integer isBlacklist;

  @ApiModelProperty("是否老爸抽检 0否 1是")
  private Integer isDadCheck;

  @ApiModelProperty("直播话术（新版）")
  private List<LiveVerbalTrickVO> liveVerbalTrickVos;

  @ApiModelProperty("直播话术新版标识")
  private Integer newLiveVerbalTrickFlag;

  @ApiModelProperty("培训资料状态 0 待提交 1 已提交 2 待法务审核 3 待QC审核 4 待修改 5 已完成")
  private Integer materialsStatus;

  @ApiModelProperty("P系统款号")
  private String partnerProviderItemSn;

  @ApiModelProperty(value = "商品标签", notes = "DAD_SAMPLING_INSPECTION 老爸抽检, CUSTOMIZED_PRODUCTS 定制品")
  List<String> tags;

  @ApiModelProperty(value = "合同销售价")
  private BigDecimal contractSalePrice;

  @ApiModelProperty(value = "平台佣金")
  private BigDecimal platformCommission;

  @ApiModelProperty(value ="合作方-业务类型")
  private List<CorpBizTypeDTO> corpBizType;

  @ApiModelProperty("下架时间")
  private Long downFrameTime;

  @ApiModelProperty("下架理由")
  private String downFrameReason;
  
  @ApiModelProperty("会员店ID")
  private String vipShopId;
  
  @ApiModelProperty("小程序ID")
  private String wechatId;
  
  @ApiModelProperty("抖店ID")
  private String douDianId;
  
  @ApiModelProperty("小红书ID")
  private String miniRedBookId;

  public static NewGoodsSpuVO ofNewGoodsVo(NewGoodsVo newGoodsVo) {
    NewGoodsSpuVO one = new NewGoodsSpuVO();
    one.setNewGoodsDbId(newGoodsVo.getId());
    one.setStandardName(newGoodsVo.getStandardName());
    one.setItemId(newGoodsVo.getItemId());
    one.setItemCode(newGoodsVo.getItemCode());
    one.setName(newGoodsVo.getName());
    one.setImage(newGoodsVo.getImage());
    one.setStatus(newGoodsVo.getStatus());
    one.setAuditStatus(newGoodsVo.getAuditStatus());
    one.setCategoryId(newGoodsVo.getCategoryId());
    one.setCategory(newGoodsVo.getCategory());
    one.setBrandId(newGoodsVo.getBrandId());
    one.setBrand(newGoodsVo.getBrand());
    one.setBuyerId(newGoodsVo.getBuyerId());
    one.setPrincipalId(newGoodsVo.getPrincipalId());
    one.setLegalId(newGoodsVo.getLegalId());
    one.setQcIds(newGoodsVo.getQcIds());
    one.setBuyer(newGoodsVo.getBuyer());
    one.setPrincipal(newGoodsVo.getPrincipal());
    one.setLegal(newGoodsVo.getLegal());
    one.setQcs(newGoodsVo.getQcs());
    one.setHomePageText(newGoodsVo.getHomeCopy());
    one.setShelfTime(newGoodsVo.getShelfTime());
    one.setShipmentType(newGoodsVo.getShipmentType());
    one.setShipmentArea(newGoodsVo.getShipmentArea());
    one.setShipmentAging(newGoodsVo.getShipmentAging());
    one.setLogistics(newGoodsVo.getLogistics());
    one.setExpressTemplate(newGoodsVo.getExpressTemplate());
    one.setNoReason(newGoodsVo.getNoReason());
    one.setRemark(newGoodsVo.getRemark());
    one.setPlanId(newGoodsVo.getPlanId());
    one.setPlanName(newGoodsVo.getPlanName());
    one.setLaunchDate(newGoodsVo.getLaunchDate());
    if (Objects.isNull(newGoodsVo.getItemType())) {
      one.setItemType(1);
    } else {
      one.setItemType(newGoodsVo.getItemType());
    }
    one.setPartnerSysType(newGoodsVo.getPartnerSysType());
    one.setLiveVerbalTrickStatus(newGoodsVo.getLiveVerbalTrickStatus());
    one.setBusinessLine(newGoodsVo.getBusinessLine());
    one.setBusinessLines(
        Optional.ofNullable(newGoodsVo.getBusinessLines())
            .map(
                v ->
                    Arrays.stream(v.split(","))
                        .filter(StringUtil::isNotBlank)
                        .map(Integer::parseInt)
                        .collect(Collectors.toList()))
            .orElseGet(Collections::emptyList));
    one.setPropKeys(newGoodsVo.getPropKeys());
    one.setIsBlacklist(newGoodsVo.getIsBlacklist());
    one.setIsDadCheck(newGoodsVo.getIsDadCheck());

    one.setLiveVerbalTrickVos(newGoodsVo.getLiveVerbalTrickVos());
    one.setNewLiveVerbalTrickFlag(newGoodsVo.getNewLiveVerbalTrickFlag());
    one.setMaterialsStatus(newGoodsVo.getMaterialsStatus());
    one.setPartnerProviderItemSn(newGoodsVo.getPartnerProviderItemSn());
    one.setTags(newGoodsVo.getTags());

    one.setDownFrameReason(newGoodsVo.getDownFrameReason());
    one.setDownFrameTime(newGoodsVo.getDownFrameTime());
    
    one.setVipShopId(newGoodsVo.getVipShopId());
    one.setWechatId(newGoodsVo.getWechatId());
    one.setDouDianId(newGoodsVo.getDouDianId());
    one.setMiniRedBookId(newGoodsVo.getMiniRedBookId());

    return one;
  }
}
