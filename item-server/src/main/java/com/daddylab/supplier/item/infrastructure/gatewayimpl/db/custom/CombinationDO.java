package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.CombinationItemType;

import lombok.Data;

/**
 * id,code,name,type,created_at,created_uid
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/12 6:02 下午
 * @description
 */
@Data
public class CombinationDO {

    private Long id;

    private String code;

    private String name;

    private CombinationItemType type;

    private Long createdAt;

    private Long createdUid;

    private Integer businessLine;
}
