package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesReturnLogisticsInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AfterSalesReturnLogisticsInfoMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesReturnLogisticsInfoService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 售后单退货物流信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-25
 */
@Service
public class AfterSalesReturnLogisticsInfoServiceImpl extends DaddyServiceImpl<AfterSalesReturnLogisticsInfoMapper, AfterSalesReturnLogisticsInfo> implements IAfterSalesReturnLogisticsInfoService {

}
