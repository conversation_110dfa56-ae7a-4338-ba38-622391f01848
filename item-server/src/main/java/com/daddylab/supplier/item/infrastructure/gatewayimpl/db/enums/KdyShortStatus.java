package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IStringEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 收件  »  在途  »  疑难  »  派送  »  代签收  »  签收
 *
 * <AUTHOR>
 * @since 2024/12/18
 */
@RequiredArgsConstructor
@Getter
public enum KdyShortStatus implements IStringEnum {
    //收件
    RECEIVED("收件", "收件"),
    //在途
    IN_TRANSIT("在途", "在途"),
    //疑难
    DIFFICULT("疑难", "疑难"),
    //派送
    DELIVERING("派送", "派送"),
    //代签收
    COUNTER_SIGNED("代签收", "代签收"),
    //签收
    SIGNED("签收", "签收"),



    ;
    private final String value;
    private final String desc;
}
