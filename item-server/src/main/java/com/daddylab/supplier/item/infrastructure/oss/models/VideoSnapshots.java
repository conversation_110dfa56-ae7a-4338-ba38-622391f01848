package com.daddylab.supplier.item.infrastructure.oss.models;

import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/10/13
 */
@Builder
@Getter
public class VideoSnapshots implements OssProcessModel {
    public String command() {
        return "video/snapshots";
    }

    /**
     * 视频截帧的起始时间，单位为毫秒。取值：
     *
     * <p>0（默认值）：从起始位置开始。
     *
     * <p>大于0：从第ss毫秒开始。
     */
    @Builder.Default int ss = 0;

    /**
     * 图片输出格式，取值：
     *
     * <p>jpg
     *
     * <p>png
     */
    @Builder.Default String f = "png";

    /** 截帧数量，默认为不限制数量（截帧到视频结束）。 */
    @Builder.Default int num = 1;

    /**
     * 截帧间隔，单位为毫秒，默认截取所有视频帧。
     *
     * <p>说明 当该参数小于源视频帧间隔（帧率倒数）时，会按源视频帧间隔进行截帧。
     */
    int inter;

    /** 输出图片的宽度，单位为px，取值范围为[32,4096]，默认与源视频宽度相同。 */
    int w;

    /** 输出图片的高度，单位为px，取值范围为[32,4096]，默认与源视频高度相同。 */
    int h;

    /**
     * 输出图片宽度与原始视频宽度的百分比，取值范围为(0,200]，默认值为100。
     *
     * <p>说明 当w与pw同时设置时，pw无效。
     */
    int pw;

    /**
     * 输出图片高度与原始视频高度的百分比，取值范围为(0,200]，默认值为100。
     *
     * <p>说明 当h与ph同时设置时，ph无效。
     */
    int ph;

    /**
     * 缩放方式。取值：
     *
     * <p>crop：缩放并裁剪。
     *
     * <p>stretch（默认值）：拉伸以填满。
     *
     * <p>fill：缩放并保留黑边。
     *
     * <p>fit：缩放并不保留黑边，等比缩放。
     */
    String scaletype;
}
