package com.daddylab.supplier.item.controller.oa;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.payment.PaymentOrderBizService;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.types.oa.OaCgfkCallback;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@RestController
@RequestMapping(path = "/oa")
@Slf4j
public class OaController {
    @Autowired
    private PaymentOrderBizService paymentOrderBizService;

    @ApiOperation(value = "采购付款单OA审核状态回传")
    @PostMapping("/cgfk/callback")
    public SingleResponse<Boolean> cgfkCallback(@RequestBody OaCgfkCallback callback) {
        log.info("采购付款单OA审核状态回传:{}", callback);
        paymentOrderBizService.oaCallback(callback);
        return SingleResponse.of(true);
    }

    @ApiOperation(value = "采购付款单OA审核状态回传")
    @PostMapping("/cgfk/callback/batch")
    public SingleResponse<Boolean> cgfkCallback(@RequestBody List<OaCgfkCallback> callbacks) {
        for (OaCgfkCallback callback : callbacks) {
            ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
                log.info("采购付款单OA审核状态回传:{}", callback);
                try {
                    paymentOrderBizService.oaCallback(callback);
                    log.info("采购付款单OA回调处理完成:{}", callback.getProcessInstId());
                } catch (Exception e) {
                    log.error("采购付款单OA回调处理异常:{}", e.getMessage(), e);
                }
            });
        }
        return SingleResponse.of(true);
    }
}
