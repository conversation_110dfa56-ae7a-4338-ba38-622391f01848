//package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;
//
//import com.alibaba.cola.dto.Response;
//import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.EditSkuRatioDto;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WarehouseGoodsInventoryStatics;
//
//import java.util.List;
//
///**
// * <p>
// * 实仓中SKU的库存占比统计 服务类
// * </p>
// *
// * <AUTHOR>
// * @since 2024-04-02
// */
//public interface IWarehouseGoodsInventoryStaticsService extends IDaddyService<WarehouseGoodsInventoryStatics> {
//
//
//    /**
//     * 处理SKU得累计库存.
//     * 必须确保warehouse 一致。只批量处理统一个仓库（可实仓、虚拟仓）的SKU占比
//     *
//     * @param editSkuRatioDtoList
//     * @return
//     */
//    Response addBatchSkuRatio(List<EditSkuRatioDto> editSkuRatioDtoList);
//
//    /**
//     * 修改仓库纬度的占比
//     *
//     * @param warehouseNo 仓库编码，实仓或虚拟仓
//     * @param newVal      此仓库此场景下的新值
//     * @param oldVal      此仓库此场景下的旧值
//     * @return
//     */
//    Response addWarehouseRatio(String warehouseNo, Integer newVal, Integer oldVal);
//
//
////    /**
////     * CAS 更新实仓库存
////     *
////     * @param warehouseNo 实仓编码
////     * @param reqVersion  请求数据版本号
////     * @param addRatio    新增的库存，可正可负
////     */
////    void updateWarehouseRatioByCas(String warehouseNo, Integer reqVersion, Integer addRatio, Integer addUpTo);
//
//}
//
