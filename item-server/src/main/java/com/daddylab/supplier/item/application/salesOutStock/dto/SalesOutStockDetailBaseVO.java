package com.daddylab.supplier.item.application.salesOutStock.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年10月20日 4:09 PM
 */
@Data
@ApiModel("销售出库单基础信息")
public class SalesOutStockDetailBaseVO {

    @ApiModelProperty("出库单号")
    private String orderNo;

    @ApiModelProperty("仓库")
    private String warehouseName;

    @ApiModelProperty("店铺")
    private String shopName;

    @ApiModelProperty("物流公司")
    private String logisticsName;

    @ApiModelProperty("物流单号")
    private String logisticsNo;

    @ApiModelProperty("发货时间")
    private String consignTime;

    @ApiModelProperty("下单时间")
    private String tradeTime;

    @ApiModelProperty("支付时间")
    private String payTime;

    private Integer status;
    @ApiModelProperty("状态")
    private String statusStr;

    @ApiModelProperty("订单编号（旺）")
    private String tradeNo;

    @ApiModelProperty("称重重量")
    private BigDecimal weight;

    @ApiModelProperty("订单标签")
    private String tradeLabel;

    @ApiModelProperty("邮费")
    private BigDecimal postAmount;

    @ApiModelProperty("应收金额")
    private BigDecimal receivable;

    @ApiModelProperty("货品数量")
    private BigDecimal goodsCount;

    @ApiModelProperty("发货员")
    private String consignName;

    @ApiModelProperty("截停原因")
    private Integer blockReason;

    @ApiModelProperty("审核员")
    private String checkerName;

    @ApiModelProperty("审核时间")
    private String checkTime;

    // --------------------------

    @ApiModelProperty("客户昵称")
    private String buyerNick;

    @ApiModelProperty("收件人")
    private String receiverName;

    @ApiModelProperty("手机")
    private String receiverMobile;

    @ApiModelProperty("固话")
    private String receiverTelno;

    @ApiModelProperty("收件地址")
    private String receiverAddress;

    @ApiModelProperty("邮编")
    private String receiverZip;

    @ApiModelProperty("客户备注")
    private String buyerMessage;

    @ApiModelProperty("客户备注")
    private String csRemark;

    // -------------



}
