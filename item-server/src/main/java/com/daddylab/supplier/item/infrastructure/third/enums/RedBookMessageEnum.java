package com.daddylab.supplier.item.infrastructure.third.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @class RedBookMessageEnum.java
 * @description 描述类的作用
 * @date 2024-03-01 15:44
 */
@AllArgsConstructor
@Getter
public enum RedBookMessageEnum implements IEnum<String> {

    ITEM_DEFAULT("default", "公共消息"),

    ITEM_AUDIT_REJECT("msg_item_audit_reject", "商品状态展示已驳回"),
    SKU_CREATE("msg_sku_create", "sku新建"),
    SKU_BUY_ABLE("msg_sku_buyable", "sku上下架"),
    SKU_DELETE("msg_sku_delete", "sku删除"),
    ;

    private final String value;
    private final String desc;

    public static RedBookMessageEnum ofValue(String value) {
        for (RedBookMessageEnum redBookMessageEnum : RedBookMessageEnum.values()) {
            if (redBookMessageEnum.getValue().equals(value)) {
                return redBookMessageEnum;
            }
        }
        return null;
    }
}
