package com.daddylab.supplier.item.application.afterSaleLogistics.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2024年05月23日 10:37 AM
 */

@Data
public class ExceptionLogDto {
    @ApiModelProperty(value = "日志类型", notes = "1:异常 2:备注 3:开启预警 4:关闭预警 5:记录次数记录...")
    private Integer type;

    @ApiModelProperty(value = "日志内容")
    private String msg;

    @ApiModelProperty(value = "操作人")
    private String operatorNick;

    @ApiModelProperty(value = "操作时间")
    private Long time;
}
