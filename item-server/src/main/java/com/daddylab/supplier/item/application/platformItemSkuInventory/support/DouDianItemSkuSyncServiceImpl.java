package com.daddylab.supplier.item.application.platformItemSkuInventory.support;

import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params.PlatformSkuSyncInfo;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.infrastructure.doudian.DouDianTemplate;
import com.daddylab.supplier.item.infrastructure.doudian.DoudianProductWithSkuListResp;
import com.daddylab.supplier.item.infrastructure.doudian.DoudianProductWithSkuListVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopService;
import com.doudian.open.api.product_listV2.data.DataItem;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.FlowBuilder;
import org.springframework.batch.core.job.flow.Flow;
import org.springframework.batch.core.job.flow.support.SimpleFlow;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> up
 * @date 2024年03月18日 4:05 PM
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DouDianItemSkuSyncServiceImpl extends AbstractPlatformItemSkuSync<DouDianItemSkuSyncServiceImpl.DoudianProductContainer, List<PlatformSkuSyncInfo>> {

    final DouDianTemplate douDianTemplate;

    @Autowired
    private IShopService shopService;

    @Override
    public Platform defaultType() {
        return Platform.DOUDIAN;
    }

    @Override
    public void fullDoseSync() {
        try {
            final List<ShopAuthorization> shopAuthorizations = shopAuthorizationService.listNotExpiredAuthorizations(
                    Platform.DOUDIAN);
            log.info("[平台商品同步][抖店]开始同步，店铺={}",
                    shopAuthorizations.stream().map(ShopAuthorization::getSn).collect(
                            Collectors.toList()));
            final String platform = defaultType().getDesc();
            final SimpleAsyncTaskExecutor executor = new SimpleAsyncTaskExecutor(defaultType().name() + "-fullDoseSync");
            executor.setConcurrencyLimit(4);
            final ArrayList<Flow> stepFlows = new ArrayList<>();
            for (ShopAuthorization shopAuthorization : shopAuthorizations) {
                final String sn = shopAuthorization.getSn();
                final Step step = buildStep(getFullDoseItemReader(shopAuthorization),
                        platformItemSyncConfig.getChunkSize(),
                        sn);
                final SimpleFlow shopFlow = new FlowBuilder<SimpleFlow>(platform + sn).start(step).build();
                stepFlows.add(shopFlow);
            }
            final SimpleFlow flow = new FlowBuilder<SimpleFlow>(defaultType().name() + "-fullDoseSync")
                    .split(executor)
                    .add(stepFlows.toArray(new Flow[0]))
                    .next(stepBuilderFactory.get(String.format("[%s]-StatisticsStep", defaultType().getDesc()))
                                            .tasklet(getStatisticsTasklet())
                                            .build())
                    .build();
            final Job job = jobBuilderFactory.get(PLATFORM_ITEM_SKU_SYNC_JOB + "-" + defaultType().name())
                                             .start(flow)
                                             .build()
                                             .build();
            JobParametersBuilder parametersBuilder = new JobParametersBuilder();
            parametersBuilder.addDate("date", new Date());
            JobExecution run = jobLauncher.run(job, parametersBuilder.toJobParameters());
            log.info("[抖店平台商品同步]任务执行结果={}", run.getStatus());
        } catch (Exception e) {
            log.error("[抖店平台商品同步]任务运行异常", e);
        }
    }

    @Data
    public static class DoudianProductContainer {
        private DoudianProductWithSkuListVO productWithSkuListVO;
        private String shopNo;
    }

    public ItemReader<DoudianProductContainer> getFullDoseItemReader(ShopAuthorization shopAuthorization) {
        return getItemReader(iPage -> {
            final DoudianProductWithSkuListResp doudianProductWithSkuListResp = douDianTemplate.queryProductWithSkuList(
                    shopAuthorization.getAccessToken(),
                    iPage.getCurrent(),
                    iPage.getSize());
            if (doudianProductWithSkuListResp == null || doudianProductWithSkuListResp.getProducts() == null ||
                    doudianProductWithSkuListResp.getProducts().isEmpty()) {
                iPage.setRecords(Collections.emptyList());
                iPage.setTotal(0);
                return iPage;
            }
            iPage.setRecords(doudianProductWithSkuListResp.getProducts().stream().map(v -> {
                final DoudianProductContainer doudianProductContainer = new DoudianProductContainer();
                doudianProductContainer.setProductWithSkuListVO(v);
                doudianProductContainer.setShopNo(shopAuthorization.getSn());
                return doudianProductContainer;
            }).collect(Collectors.toList()));
            iPage.setTotal(doudianProductWithSkuListResp.getTotal());
            return iPage;
        }, 100);
    }

    @Override
    public ItemProcessor<DoudianProductContainer, List<PlatformSkuSyncInfo>> getItemProcess() {
        return this::productToPlatformItemSkuInventoryParams;
    }

    @Override
    public ItemWriter<List<PlatformSkuSyncInfo>> getItemWriter() {
        return platformParams -> {
            for (List<PlatformSkuSyncInfo> platformParamDatums : platformParams) {
                saveSkuSyncInfo(platformParamDatums);
            }
        };
    }


    List<PlatformSkuSyncInfo> productToPlatformItemSkuInventoryParams(DoudianProductContainer container) {
        final ArrayList<PlatformSkuSyncInfo> paramsList = new ArrayList<>();
        final DoudianProductWithSkuListVO productWithSkuListVO = container.getProductWithSkuListVO();
        if (productWithSkuListVO == null
                || productWithSkuListVO.getSkuList() == null
                || productWithSkuListVO.getSkuList().isEmpty()) {
            return paramsList;
        }
        final DataItem product = productWithSkuListVO.getProduct();
        final int skuNum = productWithSkuListVO.getSkuList().size();
        for (com.doudian.open.api.sku_list.data.DataItem dataItem : productWithSkuListVO.getSkuList()) {
            PlatformSkuSyncInfo platformSkuSyncInfo = new PlatformSkuSyncInfo();
            platformSkuSyncInfo.setPlatform(defaultType());
            platformSkuSyncInfo.setShopNo(container.getShopNo());
            platformSkuSyncInfo.setOuterSkuId(dataItem.getId().toString());
            platformSkuSyncInfo.setOuterItemId(dataItem.getProductId().toString());
            platformSkuSyncInfo.setStock(dataItem.getStockNum());
            platformSkuSyncInfo.setPrice(BigDecimal.valueOf(dataItem.getPrice())
                                                   .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
            //商品在店铺中状态: 0-在线；1-下线；2-删除；详见商品状态机：https://op.jinritemai.com/docs/question-docs/92/2070
            final int productStatus = product.getStatus() == 0 ? 1 : 0;
            platformSkuSyncInfo.setStatus(productStatus);
            platformSkuSyncInfo.setOuterSkuCode(dataItem.getCode());
            platformSkuSyncInfo.setOuterItemCode(null);
            platformSkuSyncInfo.setItemCreateTime(product.getCreateTime());
            platformSkuSyncInfo.setItemUpdateTime(product.getUpdateTime());
            platformSkuSyncInfo.setSkuCreateTime(dataItem.getCreateTime());
            platformSkuSyncInfo.setSkuUpdateTime(product.getUpdateTime());
            platformSkuSyncInfo.setGoodsName(product.getName());
            platformSkuSyncInfo.setSpecName(Stream.of(dataItem.getSpecDetailName1(),
                    dataItem.getSpecDetailName2(),
                    dataItem.getSpecDetailName3()).filter(StrUtil::isNotBlank).collect(Collectors.joining("|")));
            platformSkuSyncInfo.setSkuNum(skuNum);
            paramsList.add(platformSkuSyncInfo);
        }
        return paramsList;
    }

    @Autowired
    private ShopGateway shopGateway;

    @Override
    protected List<PlatformSkuSyncInfo> getSkuSyncInfos(String shopNo, String itemId) {
        final ShopAuthorization shopAuthorization = shopAuthorizationService.getByShopNo(shopNo)
                                                                            .orElseThrow(() -> ExceptionPlusFactory.bizException(
                                                                                    ErrorCode.SHOP_AUTHORIZATION_EXPIRED));
        final DoudianProductWithSkuListVO productWithSkuListVO = douDianTemplate.getProductWithSkuListVO(
                shopAuthorization.getAccessToken(),
                Long.parseLong(itemId));
        if (productWithSkuListVO == null) {
            return Collections.emptyList();
        }
        final DoudianProductContainer doudianProductContainer = new DoudianProductContainer();
        doudianProductContainer.setProductWithSkuListVO(productWithSkuListVO);
        doudianProductContainer.setShopNo(shopNo);
        return productToPlatformItemSkuInventoryParams(doudianProductContainer);
    }

    @Override
    public void incrementSync(LocalDateTime startTime, LocalDateTime endTime) throws UnsupportedOperationException {
        final List<ShopAuthorization> shopAuthorizations = shopAuthorizationService.listNotExpiredAuthorizations(
                Platform.DOUDIAN);
        for (ShopAuthorization shopAuthorization : shopAuthorizations) {
            run(buildStep(getItemReader(iPage -> {
                final DoudianProductWithSkuListResp doudianProductWithSkuListResp = douDianTemplate.queryProductWithSkuList(
                        shopAuthorization.getAccessToken(),
                        startTime,
                        endTime,
                        iPage.getCurrent(),
                        iPage.getSize());
                if (doudianProductWithSkuListResp == null || doudianProductWithSkuListResp.getProducts() == null ||
                        doudianProductWithSkuListResp.getProducts().isEmpty()) {
                    iPage.setRecords(Collections.emptyList());
                    iPage.setTotal(0);
                    return iPage;
                }
                iPage.setRecords(doudianProductWithSkuListResp.getProducts().stream().map(v -> {
                    final DoudianProductContainer doudianProductContainer = new DoudianProductContainer();
                    doudianProductContainer.setProductWithSkuListVO(v);
                    doudianProductContainer.setShopNo(shopAuthorization.getSn());
                    return doudianProductContainer;
                }).collect(Collectors.toList()));
                iPage.setTotal(doudianProductWithSkuListResp.getTotal());
                return iPage;
            }, 100)));
        }
    }


}
