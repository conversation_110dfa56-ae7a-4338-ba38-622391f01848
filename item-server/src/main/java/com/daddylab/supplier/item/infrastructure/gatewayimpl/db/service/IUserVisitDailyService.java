package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.UserVisitDaily;

/**
 * <p>
 * 用户每日访问记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
public interface IUserVisitDailyService extends IDaddyService<UserVisitDaily> {

}
