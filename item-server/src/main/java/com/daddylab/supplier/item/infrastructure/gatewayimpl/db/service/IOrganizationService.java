package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Organization;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 采购组织表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
public interface IOrganizationService extends IDaddyService<Organization> {

    Boolean isSyncKingDeeOrg(Long id);

}
