package com.daddylab.supplier.item.infrastructure.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.ForkJoinTask;
import java.util.concurrent.RecursiveTask;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @class PartitionUtils.java
 * @description 分批处理工具
 * @date 2024-03-06 16:05
 */
public class PartitionUtil {

    static int DEFAULT_BATCH_SIZE = 1000;
    static int DEFAULT_BINARY_BATCH_SIZE = 1 << 10;
    private static final ForkJoinPool forkJoinPool = new ForkJoinPool(16);

    /**
     *  分批多线程查询(二分法递归)
     *
     * @param keys List<T>
     * @param handler Function<List<List<R>>
     * @param batchSize int 最好设置为2的倍数
     * @return java.util.List<R>
     * @date 2024/3/7 10:03
     * <AUTHOR>
     */
    public static <T, R> List<R> partition(List<T> keys, Function<List<T>, List<R>> handler, int batchSize) {
        return forkJoinPool.invoke(PrimaryKeyPartitionTask.buildSimpleSortStrategy(keys, handler, batchSize));
    }

    /**
     * 默认批次(递归)
     *
     * @param keys List<T>
     * @param handler Function<List<List<R>>
     * @return java.util.List<R>
     * @date 2024/3/6 16:13
     * <AUTHOR>
     */
    public static <T, R> List<R> partition(List<T> keys, Function<List<T>, List<R>> handler) {
        return partition(keys, handler, DEFAULT_BINARY_BATCH_SIZE);
    }


    /**
     * 分批多线程查询(常规)
     *
     * @param keys List<T>
     * @param handler Function<List<List<R>>
     * @return java.util.List<R>
     * @date 2024/3/6 16:08
     * <AUTHOR>
     */
    public static <T, R> List<R> partitionGeneral(List<T> keys, Function<List<T>, List<R>> handler, int batchSize) {
        return forkJoinPool.invoke(PrimaryKeyPartitionTask.buildOrderSortStrategy(keys, handler, batchSize));
    }

    /**
     * 默认批次(常规)
     *
     * @param keys List<T>
     * @param handler Function<List<List<R>>
     * @return java.util.List<R>
     * @date 2024/3/6 16:13
     * <AUTHOR>
     */
    public static <T, R> List<R> partitionGeneral(List<T> keys, Function<List<T>, List<R>> handler) {
        return partitionGeneral(keys, handler, DEFAULT_BATCH_SIZE);
    }

    /**
     * @ClassName PrimaryKeyPartition.java
     * @Description 多线程分批处理工具
     * <AUTHOR>
     * @Date 2022-09-27 09:43
     */
    private static class PrimaryKeyPartitionTask<T, R> extends RecursiveTask<List<R>> {
        private final SortStrategy<T, R> sortStrategy;
        public PrimaryKeyPartitionTask(SortStrategy<T, R> sortStrategy) {
            this.sortStrategy = sortStrategy;
        }

        public static <T, R> PrimaryKeyPartitionTask<T, R> buildSimpleSortStrategy(List<T> data, Function<List<T>, List<R>> func) {
            return new PrimaryKeyPartitionTask<>(new SimpleSortStrategy<T, R>(data, func));
        }

        public static <T, R> PrimaryKeyPartitionTask<T, R> buildSimpleSortStrategy(List<T> data, Function<List<T>, List<R>> func, int batchSize) {
            return new PrimaryKeyPartitionTask<>(new SimpleSortStrategy<T, R>(data, func, batchSize));
        }

        public static <T, R> PrimaryKeyPartitionTask<T, R> buildSimpleSortStrategy(List<T> data, Function<List<T>, List<R>> func, int batchSize, int from, int to) {
            return new PrimaryKeyPartitionTask<>(new SimpleSortStrategy<T, R>(data, func, batchSize, from, to));
        }

        public static <T, R> PrimaryKeyPartitionTask<T, R> buildOrderSortStrategy(List<T> data, Function<List<T>, List<R>> func) {
            return new PrimaryKeyPartitionTask<>(new OrderStrategy<T, R>(data, func));
        }

        public static <T, R> PrimaryKeyPartitionTask<T, R> buildOrderSortStrategy(List<T> data, Function<List<T>, List<R>> func, int batchSize) {
            return new PrimaryKeyPartitionTask<>(new OrderStrategy<T, R>(data, func, batchSize));
        }

        @Override
        protected List<R> compute() {
            return sortStrategy.compute();
        }

        /**
         * sort strategy
         */
        static interface SortStrategy<T, R> {
            List<R> compute();
        }
        abstract static class AbstractSortStrategy<T, R> implements SortStrategy<T, R> {
            List<T> dataList;
            int from = 0;
            int to;
            int batchSize = 100;
            Function<List<T>, List<R>> func;
            /**
             * 具体实现算法
             *
             * @return java.util.List<R>
             * @date 2024/3/6 17:27
             * <AUTHOR>
             */
            @Override
            public abstract List<R> compute();
        }
        /**
         * unsort 递归计算数据
         */
        static class SimpleSortStrategy<T, R> extends AbstractSortStrategy<T, R> {
            public SimpleSortStrategy(List<T> data, Function<List<T>, List<R>> func) {
                this.func = func;
                this.dataList = data;
                this.to = data.size();
            }

            public SimpleSortStrategy(List<T> data, Function<List<T>, List<R>> func, int batchSize) {
                this.dataList = data;
                this.batchSize = batchSize;
                this.func = func;
                this.to = data.size();
            }

            public SimpleSortStrategy(List<T> data, Function<List<T>, List<R>> func, int batchSize, int from, int to) {
                this.dataList = data;
                this.batchSize = batchSize;
                this.func = func;
                this.from = from;
                this.to = to;
            }

            @Override
            public List<R> compute() {
                List<T> subList = new ArrayList<>();
                if (to - from <= batchSize) {
                    for (int i = from; i < to; i++) {
                        subList.add(dataList.get(i));
                    }
                    return func.apply(subList);
                }
                int middle = (from + to) / 2;
                PrimaryKeyPartitionTask<T, R> leftPrimaryKeyPartition = PrimaryKeyPartitionTask.buildSimpleSortStrategy(dataList, func, batchSize, from, middle);
                leftPrimaryKeyPartition.fork();
                PrimaryKeyPartitionTask<T, R> rightPrimaryKeyPartition = PrimaryKeyPartitionTask.buildSimpleSortStrategy(dataList, func, batchSize, middle, to);
                rightPrimaryKeyPartition.fork();
                return CollUtil.unionAll(leftPrimaryKeyPartition.join(), rightPrimaryKeyPartition.join());
            }
        }
        /**
         * 常规有序计算
         */
        static class OrderStrategy<T, R> extends AbstractSortStrategy<T, R> {
            public OrderStrategy(List<T> data, Function<List<T>, List<R>> func) {
                this.func = func;
                this.dataList = data;
                this.to = data.size();
            }
            public OrderStrategy(List<T> data, Function<List<T>, List<R>> func, int batchSize) {
                this.dataList = data;
                this.batchSize = batchSize;
                this.func = func;
                this.to = data.size();
            }
            @Override
            public List<R> compute() {
                if (dataList.size() <= batchSize) {
                    return func.apply(dataList);
                }
                List<ForkJoinTask<List<R>>> forkJoinTasks = new ArrayList<>();
                for (List<T> subDataList : ListUtil.partition(dataList, batchSize)) {
                    PrimaryKeyPartitionTask<T, R> subPrimaryKeyPartitionTask = PrimaryKeyPartitionTask.buildOrderSortStrategy(subDataList, func, batchSize);
                    ForkJoinTask<List<R>> fork = subPrimaryKeyPartitionTask.fork();
                    forkJoinTasks.add(fork);
                }
                return forkJoinTasks.stream().flatMap(forkJoinTask -> forkJoinTask.join().stream()).collect(Collectors.toList());
            }
        }
    }
}
