package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 2:58 下午
 * @description
 */
@AllArgsConstructor
@Getter
public enum MessagePushType implements IEnum<Integer> {


    DEFAULT(0, "系统通知"),
    TEXT(1, "短信"),
    MAIL(2, "邮件"),
    FORCE_REMIND(3, "系统强提醒"),
    ALL(4,"全部消息")
    ;
    @EnumValue
    private final Integer value;
    private final String desc;

}
