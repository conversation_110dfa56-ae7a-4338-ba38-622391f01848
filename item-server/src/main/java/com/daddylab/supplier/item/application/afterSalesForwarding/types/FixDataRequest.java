package com.daddylab.supplier.item.application.afterSalesForwarding.types;

import com.daddylab.supplier.item.common.domain.dto.Command;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/7
 */
@Data
public class FixDataRequest extends Command {
    private static final long serialVersionUID = 2798269551842317021L;
    @NotEmpty
    private List<String> deliveryNos;
}
