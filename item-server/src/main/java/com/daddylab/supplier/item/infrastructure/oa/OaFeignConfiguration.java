package com.daddylab.supplier.item.infrastructure.oa;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.CustomMappingJackson2HttpMessageConverter;

import feign.codec.Decoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.nio.charset.StandardCharsets;
import java.util.Collections;

public class OaFeignConfiguration {

    @Autowired private Jackson2ObjectMapperBuilder jackson2ObjectMapperBuilder;

    public ObjectFactory<HttpMessageConverters> feignHttpMessageConverter() {
        final HttpMessageConverters httpMessageConverters =
                new HttpMessageConverters(
                        new CustomMappingJackson2HttpMessageConverter(
                                jackson2ObjectMapperBuilder.build(),
                                Collections.singletonList(
                                        new MediaType("*", "*", StandardCharsets.UTF_8))));
        return () -> httpMessageConverters;
    }

    @Bean
    public Decoder feignDecoder() {
        return new ResponseEntityDecoder(new SpringDecoder(feignHttpMessageConverter()));
    }
}
