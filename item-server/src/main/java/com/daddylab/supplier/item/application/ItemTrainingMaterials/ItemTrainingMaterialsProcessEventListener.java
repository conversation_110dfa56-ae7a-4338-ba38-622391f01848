package com.daddylab.supplier.item.application.ItemTrainingMaterials;

import com.daddylab.supplier.item.application.process.ProcessBizService;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsPrincipalsInfo;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTrainingMaterials;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTrainingMaterialsProcess;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemTrainingMaterialsProcessService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemTrainingMaterialsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INewGoodsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IProcessInstRefService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.itemTrainingMaterials.ItemTrainingMaterialsProcessData;
import com.daddylab.supplier.item.types.itemTrainingMaterials.ItemTrainingMaterialsStatus;
import com.daddylab.supplier.item.types.itemTrainingMaterials.MaterialsProcessDataKey;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEntityEvent;
import org.flowable.engine.delegate.event.AbstractFlowableEngineEventListener;
import org.flowable.engine.delegate.event.FlowableCancelledEvent;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/10/23
 */
@Component
@Slf4j
public class ItemTrainingMaterialsProcessEventListener extends AbstractFlowableEngineEventListener {
    @Autowired
    private IItemTrainingMaterialsService itemTrainingMaterialsService;
    @Autowired
    private IItemTrainingMaterialsProcessService itemTrainingMaterialsProcessService;
    @Autowired
    private IProcessInstRefService processInstRefService;
    @Autowired
    private OperateLogDomainService operateLogDomainService;
    @Autowired
    private NewGoodsBizService newGoodsBizService;
    @Autowired
    private INewGoodsService newGoodsService;
    @Autowired
    private ProcessBizService processBizService;
    @Autowired
    private ItemTrainingMaterialsBizService itemTrainingMaterialsBizService;

    private void run(Runnable callable) {
        callable.run();
    }

    @Override
    protected void taskAssigned(FlowableEngineEntityEvent event) {
        run(() -> {
            final String processInstanceId = event.getProcessInstanceId();
            final Object entity = event.getEntity();
            if (entity instanceof TaskEntity) {
                final TaskEntity taskEntity = (TaskEntity) entity;
                for (ItemTrainingMaterialsProcess materialsProcess : getMaterialsProcessByProcessInstId(
                        processInstanceId)) {
                    final String assignee = taskEntity.getAssignee();
                    final Optional<StaffBrief> staffBrief = Optional.ofNullable(assignee)
                                                                    .filter(StringUtil::isNotBlank)
                                                                    .map(userId -> StaffAssembler.INST.toStaffBrief(
                                                                            assignee));
                    final long claimTime = taskEntity.getClaimTime() != null ? DateUtil.toTime(taskEntity.getClaimTime()) : DateUtil.currentTime();
                    log.info(
                            "ItemTrainingMaterialsProcessEventListener taskAssigned activity:{} taskId:{} claimTime:{} processInstId:{} assignee:{}",
                            taskEntity.getName(),
                            taskEntity.getId(),
                            claimTime,
                            processInstanceId,
                            assignee);
                    final ItemTrainingMaterialsProcessData processData = materialsProcess.getProcessData();
                    final ItemTrainingMaterials itemTrainingMaterials = itemTrainingMaterialsService.getById(
                            materialsProcess.getItemTrainingMaterialsId());
                    switch (taskEntity.getName()) {
                        case "待法务审核":
                            processData.setIfCurrentIsNull(MaterialsProcessDataKey.legalClaimTime, claimTime);
                            processData.setIfCurrentIsNull(MaterialsProcessDataKey.legalProcessor,
                                    staffBrief.orElse(null));
                            newGoodsService.lambdaUpdate().eq(NewGoods::getItemId, itemTrainingMaterials.getItemId())
                                           .set(NewGoods::getLegalId, assignee).update();
                            break;
                        case "待QC审核":
                            processData.setIfCurrentIsNull(MaterialsProcessDataKey.qcClaimTime, claimTime);
                            processData.setIfCurrentIsNull(MaterialsProcessDataKey.qcProcessor,
                                    staffBrief.orElse(null));
                            break;
                        case "待修改":
                            processData.setIfCurrentIsNull(MaterialsProcessDataKey.modifyClaimTime, claimTime);
                            processData.setIfCurrentIsNull(MaterialsProcessDataKey.modifyProcessor,
                                    staffBrief.orElse(null));
                            break;
                    }
                    itemTrainingMaterialsProcessService.updateById(materialsProcess);
                }
            }
        });
    }

    @Override
    protected void taskCreated(FlowableEngineEntityEvent event) {
        run(() -> {
            final String processInstanceId = event.getProcessInstanceId();
            final Object entity = event.getEntity();
            if (entity instanceof TaskEntity) {
                final TaskEntity task = (TaskEntity) entity;
                final String activityName = task.getName();
                for (ItemTrainingMaterialsProcess materialsProcess : getMaterialsProcessByProcessInstId(
                        processInstanceId)) {
                    log.info(
                            "ItemTrainingMaterialsProcessEventListener taskCreated processInstId:{} activity:{} {}",
                            processInstanceId,
                            activityName,
                            Arrays.toString(new Object[]{task.getId(), task.getAssignee(), task.getOwner()}));
                    final ItemTrainingMaterialsProcessData processData = materialsProcess.getProcessData();
                    final long createTime = DateUtil.toTime(task.getCreateTime());
                    final ItemTrainingMaterials itemTrainingMaterials = itemTrainingMaterialsService.getById(
                            materialsProcess.getItemTrainingMaterialsId());
                    switch (task.getName()) {
                        case "待法务审核":
                            setStatus(materialsProcess, ItemTrainingMaterialsStatus.TO_BE_LEGAL_REVIEW);
                            processData.setIfCurrentIsNull(MaterialsProcessDataKey.legalStartTime, createTime);
                            try {
                                final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo = newGoodsBizService.getNewGoodsPrincipalsInfo(
                                        itemTrainingMaterials.getItemId()).getData();
                                final List<StaffBrief> legalUsers = newGoodsPrincipalsInfo.getLegalUsers();
                                if (!legalUsers.isEmpty()) {
                                    final Long userId = legalUsers.get(0).getUserId();
                                    processBizService.claim(userId, task.getId());
                                    log.info("[培训资料][法务自动认领]自动分配成功 userId={} taskId={}",
                                            userId,
                                            task.getId());
                                    processData.setIfCurrentIsNull(MaterialsProcessDataKey.legalClaimTime, createTime);
                                    processData.setIfCurrentIsNull(MaterialsProcessDataKey.legalProcessor, userId);
                                }
                            } catch (Exception e) {
                                log.error("[培训资料][法务自动认领]处理异常 taskId={}", task.getId(), e);
                            }
                            break;

                        case "待QC审核":
                            setStatus(materialsProcess, ItemTrainingMaterialsStatus.TO_BE_QC_REVIEW);
                            processData.setIfCurrentIsNull(MaterialsProcessDataKey.qcStartTime, createTime);
                            break;

                        case "待修改":
                            setStatus(materialsProcess, ItemTrainingMaterialsStatus.TO_BE_MODIFY);
                            processData.setIfCurrentIsNull(MaterialsProcessDataKey.modifyStartTime, createTime);

                            break;

                        case "已完成":
                            setStatus(materialsProcess, ItemTrainingMaterialsStatus.FINISHED);
                            processData.setIfCurrentIsNull(MaterialsProcessDataKey.modifyCompleteTime, createTime);
                            processData.setIfCurrentIsNull(MaterialsProcessDataKey.processCompleteTime, createTime);
                            ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL)
                                      .execute(() -> itemTrainingMaterialsBizService.notifyModifyProcessed(
                                              materialsProcess));
                            break;
                    }
                    itemTrainingMaterialsProcessService.updateById(materialsProcess);
                }
            }
        });

    }

    @Override
    protected void taskCompleted(FlowableEngineEntityEvent event) {
        run(() -> {
            final String processInstanceId = event.getProcessInstanceId();
            final Object entity = event.getEntity();
            if (entity instanceof TaskEntity) {
                final TaskEntity task = (TaskEntity) entity;
                final String activityName = task.getName();
                final Long endTime = DateUtil.currentTime();
                for (ItemTrainingMaterialsProcess materialsProcess : getMaterialsProcessByProcessInstId(
                        processInstanceId)) {
                    log.info(
                            "ItemTrainingMaterialsProcessEventListener taskCompleted processInstId:{} activity:{} {}",
                            processInstanceId,
                            activityName,
                            Arrays.toString(new Object[]{task.getId(), task.getAssignee(), task.getOwner()}));
                    final ItemTrainingMaterialsProcessData processData = materialsProcess.getProcessData();
                    switch (task.getName()) {
                        case "待法务审核":
                            processData.setIfCurrentIsNull(MaterialsProcessDataKey.legalCompleteTime, endTime);

                            ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL)
                                      .execute(() -> itemTrainingMaterialsBizService.notifyQcProcess(materialsProcess));
                            break;

                        case "待QC审核":
                            processData.setIfCurrentIsNull(MaterialsProcessDataKey.qcCompleteTime, endTime);

                            ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL)
                                      .execute(() -> itemTrainingMaterialsBizService.notifyModifyProcess(
                                              materialsProcess));
                            break;

                        case "待修改":
                            processData.setIfCurrentIsNull(MaterialsProcessDataKey.modifyCompleteTime, endTime);

                            break;

                        case "已完成":
                            break;
                    }
                    itemTrainingMaterialsProcessService.updateById(materialsProcess);
                }
            }
        });
    }

    @Override
    protected void processCancelled(FlowableCancelledEvent event) {
        run(() -> {
            final String processInstanceId = event.getProcessInstanceId();
            for (ItemTrainingMaterialsProcess materialsProcess : getMaterialsProcessByProcessInstId(
                    processInstanceId)) {
                log.info(
                        "ItemTrainingMaterialsProcessEventListener processCancelled processInstId:{}",
                        processInstanceId);
                final ItemTrainingMaterials itemTrainingMaterials = new ItemTrainingMaterials();
                itemTrainingMaterials.setId(materialsProcess.getItemTrainingMaterialsId());
                itemTrainingMaterials.setStatus(ItemTrainingMaterialsStatus.TO_BE_COMMIT);
                itemTrainingMaterials.setProcessId(0L);
                itemTrainingMaterialsService.updateById(itemTrainingMaterials);
            }
        });
    }

    private void setStatus(ItemTrainingMaterialsProcess process, ItemTrainingMaterialsStatus currentStatus) {
        final ItemTrainingMaterialsStatus beforeStatus = process.getStatus();
        process.setStatus(currentStatus);

        final ItemTrainingMaterials itemTrainingMaterials = new ItemTrainingMaterials();
        itemTrainingMaterials.setId(process.getItemTrainingMaterialsId());
        itemTrainingMaterials.setStatus(currentStatus);
        itemTrainingMaterialsService.updateById(itemTrainingMaterials);

        final StringBuilder msg = new StringBuilder();
        if (beforeStatus != ItemTrainingMaterialsStatus.COMMITTED) {
            msg.append(String.format("[%s]提交", beforeStatus.getDesc())).append("，");
        }
        msg.append(String.format("流程状态提交到[%s]", currentStatus.getDesc()));
        operateLogDomainService.addOperatorLog(
                UserContext.getUserId(),
                OperateLogTarget.ITEM_TRAINING_MATERIALS,
                process.getItemTrainingMaterialsId(),
                msg.toString());

    }


    private List<ItemTrainingMaterialsProcess> getMaterialsProcessByProcessInstId(String processInstanceId) {
        return itemTrainingMaterialsProcessService.lambdaQuery()
                                                  .eq(ItemTrainingMaterialsProcess::getProcessInstId, processInstanceId)
                                                  .list();
    }
}
