package com.daddylab.supplier.item.application.afterSalesForwarding.types;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2024年07月25日 9:46 AM
 */
@Data
public class AfterSalesForwardingRegisterPageSheet {

    /**
     * 快递单号
     */
    @ExcelProperty(value = "快递单号")
    private String deliveryNo;

    /**
     * 登记时间
     */
    @ExcelProperty(value = "登记时间")
    private String registerTime;


    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    private String itemName;

    /**
     * 商品SKU
     */
    @ExcelProperty(value = "商品SKU")
    private String skuCode;

    /**
     * 规格名称
     */
    @ExcelProperty(value = "规格名称")
    private String skuName;


    /**
     * 商品数量
     */
    @ExcelProperty(value = "退回数量")
    private Integer itemNum;


    /**
     * 是否完好
     */
    @ExcelProperty(value = "是否完好")
    private String intact;

    /**
     * 异常说明
     */
    @ExcelProperty(value = "异常说明")
    private String abnormalDescription;

    /**
     * 影响销售凭证
     */
    @ExcelProperty(value = "影响销售凭证")
    private String affectsSalesVouchers;

    /**
     * 客服备注
     */
    @ApiModelProperty(value = "客服备注")
    private String csRemark;

    /**
     * 原始单号
     */
    @ExcelProperty(value = "原始单号")
    private String orderNo;

    /**
     * 出库单号
     */
    @ExcelProperty(value = "出库单号")
    private String stockoutNo;

    /**
     * 出库仓库
     */
    @ExcelProperty(value = "出库仓库")
    private String stockoutWarehouse;


    /**
     * 转寄地址
     */
    @ExcelProperty(value = "转寄地址")
    private String forwardingAddress;
    
    @ExcelProperty(value = "收件人")
    private String forwardingReceiver;
    
    @ExcelProperty(value = "联系电话")
    private String forwardingMobile;
    
    @ExcelProperty(value = "地址")
    private String forwardingAddressDetail;

    /**
     * 转寄单号
     */
    @ExcelProperty(value = "转寄单号")
    private String forwardingDeliveryNo;

    /**
     * 重量
     */
    @ExcelProperty(value = "重量")
    private String weight;

    /**
     * 纸箱型号
     */
    @ExcelProperty(value = "纸箱型号")
    private String cartonModel;

    /**
     * 无需转寄类型
     */
    @ExcelProperty(value = "无需转寄类型")
    private String needNotForwardType;


}
