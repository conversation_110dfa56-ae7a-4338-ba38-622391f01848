package com.daddylab.supplier.item.controller.item.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/25 2:07 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("商品导出请求参数封装")
public class ExportCmd extends Command {

    private static final long serialVersionUID = -4337529255161961170L;
    @ApiModelProperty("商品id")
    private Long itemId;

    @ApiModelProperty("商品名称")
    private String itemName;

    @ApiModelProperty("商品编码")
    private String code;

    @ApiModelProperty("关联款号（其他系统商品数据关联）")
    private String partnerCode;

    @ApiModelProperty("商品sku")
    private String sku;

    @ApiModelProperty("品类id")
    private Long categoryId;

    @ApiModelProperty("品牌id")
    private Long brandId;

    @ApiModelProperty("品牌名称")
    private String brandName;

    @ApiModelProperty("上新开始时间")
    private Long shelfStartTime;

    @ApiModelProperty("上新结束时间")
    private Long shelfEndTime;

    @ApiModelProperty("采购员id")
    private Long buyerUserId;

//    @ApiModelProperty("采购员名称")
//    private String buyerName;

    @ApiModelProperty("供应商id")
    private Long providerId;

    @ApiModelProperty("导出类型。item：商品纬度。sku：sku维度")
    private String latitude;

    @ApiModelProperty("前端同学请无视这个参数，导出翻页参数，偏移量")
    private Long offsetVal;
    @ApiModelProperty("前端同学请无视这个参数，导出翻页参数，页大小")
    private Integer size;

    @ApiModelProperty("合作模式：0供应商 1老爸抽检 2绿色家装 3商家入驻")
    private List<Integer> businessLine = new ArrayList<>();

    @ApiModelProperty("合作方")
    private List<Integer> corpType;

    @ApiModelProperty("业务类型")
    private List<Integer> bizType;

    @ApiModelProperty("下架开始时间")
    private Long offShelfStartTime;

    @ApiModelProperty("下架结束时间")
    private Long offShelfEndTime;




}
