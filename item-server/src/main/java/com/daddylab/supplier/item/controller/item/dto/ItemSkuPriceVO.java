package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2022年09月07日 2:49 PM
 */
@Data
@ApiModel("sku价格返回封装")
public class ItemSkuPriceVO {

    private Long skuId;
    private String skuCode;
    private BigDecimal price;
    private Long startTime;
    private Long endTime;

    @ApiModelProperty("sku价格类型。0 成本价默认值，1 销售价。2 合同销售价。3 平台佣金")
    private Integer type;

}
