package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrderAssociateOrder;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 采购订单关联的系统订单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
public interface IPurchaseOrderAssociateOrderService extends IDaddyService<PurchaseOrderAssociateOrder> {

}
