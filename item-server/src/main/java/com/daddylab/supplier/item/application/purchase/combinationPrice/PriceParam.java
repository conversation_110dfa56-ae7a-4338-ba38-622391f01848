package com.daddylab.supplier.item.application.purchase.combinationPrice;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年04月04日 11:30 AM
 */
@Data
@ApiModel("价格编辑参数")
public class PriceParam extends Command {

    private static final long serialVersionUID = 2213022120447591758L;
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("组合价格详情列表")
    @Valid
    @Size(min = 1, message = "价格详情列表至少得有一个值")
    private List<PriceDetailDto> priceInfo;

    @ApiModelProperty("开始时间")
    @NotNull(message = "开始时间不得为空")
    private Long startTime;

    @ApiModelProperty("结束时间")
    @NotNull(message = "结束时间不得为空")
    private Long endTime;

    @ApiModelProperty("价格类型。1：日常。2：活动")
    @NotNull(message = "价格类型不得为空")
    private Integer priceType;

    @ApiModelProperty("平台： 0:ALL 1:淘宝 2:抖店 3:小红书 4:自研电商 5:有赞 6:快手")
    @NotNull(message = "平台类型不得为空")
    private Integer platformType;

    @ApiModelProperty("价格类型选择。true：sku单纬度。false：spu纬度")
    @NotNull(message = "价格类型选择不得为空")
    private Boolean getIsSingle;

    @ApiModelProperty("新增时添加，编辑不处理。是否为单sku纬度")
    private Boolean isSingle;

    @ApiModelProperty("新增时添加，编辑不处理。sku编码，当跨sku,为spu纬度时存在多个输入，英文逗号隔开")
    private String code;
}
