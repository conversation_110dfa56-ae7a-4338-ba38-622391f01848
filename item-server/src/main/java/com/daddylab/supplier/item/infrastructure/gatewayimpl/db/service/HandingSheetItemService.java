package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetActivityEvent;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetItem;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.HandingSheetActivityTypeEnum;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 盘货表关联的商品 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
public interface HandingSheetItemService extends IDaddyService<HandingSheetItem> {
    /**
     * 获取盘货表下的商品数量（spu 维度）
     *
     * @param handingSheetId 盘货表 ID
     * @return long
     */
    Long getItemNumByHandingSheetId(Long handingSheetId);

    /**
     * 查询
     *
     * @param handingSheetId 盘货表 ID
     * @return List<HandingSheetItem>
     */
    List<HandingSheetItem> listBySheetId(Long handingSheetId);

    /**
     * 生成映射
     * key: HandingSheetItem#ID
     * val: HandingSheetItem
     *
     * @param handingSheetId 盘货表 ID
     * @return map
     */
    Map<Long, HandingSheetItem> getMap(Long handingSheetId);

    /**
     * 生成映射
     * key: HandingSheetItem#ID
     * val: HandingSheetItem
     *
     * @param handingSheetItems list
     * @return map
     */
    Map<Long, HandingSheetItem> getMap(List<HandingSheetItem> handingSheetItems);

    /**
     * 通过主键删除
     *
     * @param ids
     * @return int
     */
    int deleteBatchIds(List<Long> ids);

    /**
     * count 数量
     *
     * @param queryWrapper 条件参数
     * @return count
     */
    long selectPageListCount(QueryWrapper<HandingSheetItem> queryWrapper);

    /**
     * 分页查询
     *
     * @param page 页
     * @param queryWrapper 条件参数
     * @return IPage<HandingSheetItem>
     */
    IPage<HandingSheetItem> selectPageList(Page<HandingSheetItem> page, QueryWrapper<HandingSheetItem> queryWrapper);

    /**
     * count 数量
     * 审核时查询
     *
     * @param queryWrapper qw
     * @return long
     */
    long selectPageListCountForAudit(QueryWrapper<HandingSheetItem> queryWrapper);

    /**
     * 分页查询
     * 审核时查询
     *
     * @param page 页
     * @param queryWrapper qw
     * @return page
     */
    IPage<HandingSheetItem> selectPageListForAudit(Page<HandingSheetItem> page, QueryWrapper<HandingSheetItem> queryWrapper);

    /**
     * 计算到手价
     *
     * @param activePrice 活动价
     * @param activityEvents 活动力度
     * @return 到手价
     */
    BigDecimal calcArrivalPrice(BigDecimal activePrice, List<HandingSheetActivityEvent> activityEvents, Integer actType);

    /**
     * 查询（盘货表 ID + 采购人 ID）
     *
     * @param handingSheetId 盘货表 ID
     * @param buyerUid 采购人 ID
     * @return List<HandingSheetItem>
     */
    List<HandingSheetItem> listByHandingSheetIdAndBuyerUid(Long handingSheetId, Long buyerUid);

    /**
     * 查询 handing_sheet_item#is_passed 数据
     *
     * @param handingSheetId 盘货表 ID
     * @return List<Boolean>
     */
    List<Boolean> listPassedColumnByHandingSheetId(Long handingSheetId);
}
