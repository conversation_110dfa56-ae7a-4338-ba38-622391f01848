package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTrainingMaterialsProcessRecord;

/**
 * <p>
 * 新品商品培训资料流程处理记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
public interface IItemTrainingMaterialsProcessRecordService extends IDaddyService<ItemTrainingMaterialsProcessRecord> {

}
