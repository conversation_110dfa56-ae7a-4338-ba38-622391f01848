package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine;

import cn.hutool.core.collection.BoundedPriorityQueue;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.daddylab.supplier.item.application.purchase.order.factory.CommonUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.WrapperType;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.excel.PurchaseBillRow;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.purchase.enums.FavourableType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.RegionRelevantDo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.RegionRelevantMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailWrapperMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtSaleStockOutOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.oss.OssConfig;
import com.daddylab.supplier.item.infrastructure.oss.OssGateway;
import com.daddylab.supplier.item.infrastructure.timing.StopWatch;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.lmax.disruptor.*;
import com.lmax.disruptor.dsl.Disruptor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static com.lmax.disruptor.dsl.ProducerType.SINGLE;

/**
 * <AUTHOR> up
 * @date 2023年02月27日 3:43 PM
 */
@Service
@Slf4j
public class PurchaseBillService {

    @Resource
    IWdtOrderDetailWrapperService wrapperService;

    @Resource
    IWarehouseService iWarehouseService;

    @Resource
    IWdtOrderService iWdtOrderService;

    @Resource
    IWdtOrderDetailService iWdtOrderDetailService;

    @Resource
    WdtOrderDetailMapper wdtOrderDetailMapper;

    @Resource
    IWdtSaleStockOutOrderService iWdtSaleStockOutOrderService;

    @Resource
    ICombinationItemService iCombinationItemService;

    @Resource
    FileGateway fileGateway;

    @Resource
    CommonUtil commonUtil;

    @Resource
    IWdtRefundStockInOrderService iWdtRefundStockInOrderService;

    @Resource
    IWdtRefundStockInOrderDetailsService iWdtRefundStockInOrderDetailsService;

    @Resource
    IPurchaseService iPurchaseService;

    @Resource
    IExportTaskService iExportTaskService;

    @Resource
    WdtSaleStockOutOrderMapper wdtSaleStockOutOrderMapper;


    private static final Integer SHEET_SIZE = 1000000;


    @Resource
    private WdtOrderDetailWrapperMapper wdtOrderDetailWrapperMapper;

    @Resource
    RegionRelevantMapper regionRelevantMapper;

    @Resource
    OssGateway ossGateway;

    /**
     * 根据执行操作时间的wrapper表数据生成采购账单
     *
     * @param operateTime 系统采购单自动生成的操作时间，yyyymm
     */
    public String createBill(String operateTime) {
        String fileName = "purchaseBill_" + operateTime + ".xlsx";
        File excelFile = new File(fileName);
        ExcelWriter excelWriter = EasyExcel.write(excelFile, PurchaseBillRow.class).build();

        log.info("createBill operateTime:{}", operateTime);
        performSalesOutbound(ListUtil.of(operateTime), excelWriter);
        excelWriter.finish();

        String url = uploadFileAndGetUrl(excelFile);
        log.info("系统采购单账单数据处理结束。fileUrl:{}", url);

        boolean delete = excelFile.delete();
        if (!delete) {
            log.info("系统采购单账单数据临时文件删除失败。path:{}", excelFile.getAbsolutePath());
        }
        return url;

    }

    private void performSalesOutbound(Collection<String> operateTimes,
                                      ExcelWriter excelWriter) {
        salesOutbound(new ArrayList<>(), new ArrayList<>(), operateTimes, ListUtil.of(WrapperType.STOCK_OUT_SINGLE), "销售出库单品", excelWriter, null);
        salesOutbound(new ArrayList<>(), new ArrayList<>(), operateTimes, ListUtil.of(WrapperType.STOCK_OUT_COMBINATION), "销售出库组合装", excelWriter, null);
        salesOutbound(new ArrayList<>(), new ArrayList<>(), operateTimes, ListUtil.of(WrapperType.STOCK_IN_REFUND), "销售退货入库", excelWriter, null);
        salesOutbound(new ArrayList<>(), new ArrayList<>(), operateTimes, ListUtil.of(WrapperType.STOCK_IN_PRE), "预入库", excelWriter, null);
    }

    /**
     * 根据执行操作时间的wrapper表数据生成采购账单，同时创建导出任务
     *
     * @param operateTimes 系统采购单自动生成的操作时间，yyyymm
     * @param warehouseNos 仓库编号
     */
    public Boolean writeStockOutData(Collection<String> operateTimes, Collection<String> skuCodes, Collection<String> warehouseNos,
                                     ExcelWriter excelWriter) {
        try {
            salesOutbound(warehouseNos, skuCodes, operateTimes, ListUtil.of(WrapperType.STOCK_OUT_SINGLE, WrapperType.STOCK_OUT_COMBINATION),
                    "发货明细", excelWriter, null);
            return true;
        } catch (Exception e) {
            log.error("写入销售出库单（发货明细）异常", e);
            return false;
        }
    }


    private String uploadFileAndGetUrl(File file) {
        UploadFileAction action = UploadFileAction.ofFile(file);
        FileStub fileStub = fileGateway.uploadFile(action);

        OssConfig ossConfig = SpringUtil.getBean(OssConfig.class);
        String url = fileStub.getUrl();
        log.info("purchaseBill sourceUrl:{}", url);
        String path = url.replaceAll(ossConfig.getPrivateUrl(), "");
        return ossGateway.generatePresignedUrl(true, path);
    }


    /**
     * 销售处出库的账单明细数据
     * [20230925] 原始账单数据。1W条数据导出完毕大约需要1.5min.
     */
    private void salesOutbound(Collection<String> warehouseNos, Collection<String> skuCodes, Collection<String> operateTimes,
                               List<WrapperType> wrapperTypes, String sheetPrefix, ExcelWriter excelWriter,
                               Long taskId) {

        List<Integer> types = wrapperTypes.stream().map(WrapperType::getValue).collect(Collectors.toList());
        String typesJoin = CollUtil.join(wrapperTypes, ",");
        String operateTimesJoin = CollUtil.join(operateTimes, ",");
        boolean needExportProgress = Objects.nonNull(taskId);

        Integer count = wrapperService.lambdaQuery()
                .in(WdtOrderDetailWrapper::getType, types)
                .in(CollUtil.isNotEmpty(warehouseNos), WdtOrderDetailWrapper::getWarehouseNo, warehouseNos)
                .in(CollUtil.isNotEmpty(operateTimes), WdtOrderDetailWrapper::getOperateTime, operateTimes)
                .count();
        log.info("系统采购账单导出任务，导出数据量count:{}，operateTimes:{}，wrapperType:{}",
                count, JsonUtil.toJson(operateTimes), JsonUtil.toJson(wrapperTypes));
        if (count == 0) {
            WriteSheet writeSheet = EasyExcel.writerSheet(sheetPrefix).head(PurchaseBillRow.class).build();
            excelWriter.write(Collections.EMPTY_LIST, writeSheet);
            return;
        }

        AtomicLong finishCount = new AtomicLong(0);
        ReadProcessRunner readProcessRunner = null;
        BoundedPriorityQueue<BigDecimal> processQueue = null;
        if (needExportProgress) {
            processQueue = new BoundedPriorityQueue<>(99, (o1, o2) -> Integer.compare(0, o1.compareTo(o2)));
            processQueue.offer(getProcess(finishCount, count));
            boolean end = false;
            readProcessRunner = new ReadProcessRunner(processQueue, end, iExportTaskService, taskId);
            Thread readThread = new Thread(readProcessRunner);
            readThread.start();
        }

        try {
            int sheetSize = count % SHEET_SIZE == 0 ? count / SHEET_SIZE : (count / SHEET_SIZE) + 1;
            for (int i = 0; i < sheetSize; i++) {
                try {
                    String sheetName = i == 0 ? sheetPrefix : sheetPrefix + i;
                    WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).head(PurchaseBillRow.class).build();
                    Map<String, Long> idRange = getIdRange(warehouseNos, operateTimes, types, count, i);
                    Long minId = idRange.get("minId");
                    Long maxId = idRange.get("maxId");
                    boolean cc = Objects.nonNull(minId) && Objects.nonNull(maxId);
                    if (!cc) {
                        continue;
                    }

                    StopWatch stopWatch = new StopWatch();
                    stopWatch.start("系统采购账单清洗");
                    log.info("系统采购账单导出开始.operateTime:{},type:{},sheetSize:{},sheetIndex:{},minId:{},maxId:{}",
                            operateTimesJoin, typesJoin, sheetSize, i, minId, maxId);

                    int consumersCount = 5;
                    List<NumberParagraph> numberParagraphs = getNumberParagraphs(minId, maxId, consumersCount);
                    List<PurchaseBillRow> targetList = new CopyOnWriteArrayList<>();
                    CountDownLatch consumersLatch = new CountDownLatch(consumersCount);
                    int ringBufferSize = 1024 * 1024;
                    ExportIdEventFactory eventFactory = new ExportIdEventFactory();
                    ThreadFactory producerFactory = Executors.defaultThreadFactory();
                    WaitStrategy waitStrategy = new BlockingWaitStrategy();
                    Disruptor<ExportIdEvent<NumberParagraph>> disruptor =
                            new Disruptor<>(eventFactory, ringBufferSize, producerFactory, SINGLE, waitStrategy);
                    EventConsumer[] consumers = new EventConsumer[consumersCount];
                    for (int j = 0; j < consumers.length; j++) {
                        EventConsumer eventConsumer = new EventConsumer();
                        eventConsumer.setContainer(targetList);
                        eventConsumer.setWdtOrderDetailWrapperMapper(wdtOrderDetailWrapperMapper);
                        eventConsumer.setCountDownLatch(consumersLatch);
                        eventConsumer.setTypes(types);
                        eventConsumer.setWarehouseNos(new ArrayList<>(warehouseNos));
                        eventConsumer.setSkuCodes(new ArrayList<>(skuCodes));
                        eventConsumer.setOperateTimes(new ArrayList<>(operateTimes));
                        eventConsumer.setNeedExportProgress(needExportProgress);
                        eventConsumer.setFinishCount(finishCount);
                        eventConsumer.setQueue(processQueue);
                        eventConsumer.setTotal(count);
                        consumers[j] = eventConsumer;
                    }
                    disruptor.handleEventsWithWorkerPool(consumers);
                    disruptor.start();
                    EventTranslatorOneArg<ExportIdEvent<NumberParagraph>, NumberParagraph> eventTranslatorOneArg =
                            (event, l, arg) -> event.setNp(arg);
                    NumberParagraph[] npArray = numberParagraphs.toArray(new NumberParagraph[0]);
                    disruptor.publishEvents(eventTranslatorOneArg, npArray);
                    consumersLatch.await();
                    disruptor.shutdown();

                    stopWatch.stop();
                    log.info("系统采购账单导出,数据准备完毕.operateTime:{},type:{},sheetSize:{},sheetIndex:{},minId:{},maxId:{}",
                            operateTimesJoin, typesJoin, sheetSize, i, minId, maxId);

                    stopWatch.start("写入Excel");
                    excelWriter.write(targetList, writeSheet);
                    stopWatch.stop();

                    List<String> taskTimeLogs = new LinkedList<>();
                    for (cn.hutool.core.date.StopWatch.TaskInfo taskInfo : stopWatch.getTaskInfo()) {
                        String task = taskInfo.getTaskName() + "," + taskInfo.getTimeSeconds() + "秒";
                        taskTimeLogs.add(task);
                    }
                    String timeLog = CollUtil.join(taskTimeLogs, ",");
                    log.info("系统采购账单导出结束.operateTime:{},type:{},sheetSize:{},sheetIndex:{},minId:{},maxId:{},timeLog:{}",
                            operateTimesJoin, typesJoin, sheetSize, i, minId, maxId, timeLog);
                } catch (Exception e) {
                    log.error("系统采购账单sheet处理异常.operateTime:{},type:{},sheetSize:{},sheetIndex:{}",
                            operateTimesJoin, typesJoin, sheetSize, i, e);
                }
            }

        } finally {
            if (needExportProgress) {
                readProcessRunner.interrupt();
                iExportTaskService.lambdaUpdate().set(ExportTask::getProgress, 0.5)
                        .eq(ExportTask::getId, taskId).update();
            }
        }

    }

    private BigDecimal getProcess(AtomicLong finish, Integer count) {
        BigDecimal b1 = new BigDecimal(finish.get());
        BigDecimal b2 = new BigDecimal(count);
        return b1.divide(b2, 4, RoundingMode.DOWN).multiply(BigDecimal.valueOf(0.45)).setScale(4, RoundingMode.DOWN);
    }

    private static class ReadProcessRunner implements Runnable {
        private final BoundedPriorityQueue<BigDecimal> queue;
        private volatile Boolean end;

        private final IExportTaskService iExportTaskService;
        private final Long taskId;

        public ReadProcessRunner(BoundedPriorityQueue<BigDecimal> queue, Boolean end, IExportTaskService iExportTaskService, Long taskId) {
            this.queue = queue;
            this.end = end;
            this.iExportTaskService = iExportTaskService;
            this.taskId = taskId;
        }

        public void interrupt() {
            this.end = true;
        }

        @Override
        public void run() {
            while (!end) {
                try {
                    BigDecimal progress = queue.poll();
                    if (Objects.nonNull(progress)) {
                        iExportTaskService.lambdaUpdate().set(ExportTask::getProgress, progress)
                                .eq(ExportTask::getId, taskId).update();
                    }
                    if (!end) {
                        TimeUnit.SECONDS.sleep(2);
                    }
                } catch (Exception e) {
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    @Data
    private static class NumberParagraph {
        private Long sId;
        private Long eId;
    }

    private List<NumberParagraph> getNumberParagraphs(Long minId, Long maxId, Integer paragraphSize) {
        List<NumberParagraph> res = new ArrayList<>();
        long sub = maxId - minId + 1;
        long avg = sub / paragraphSize;

        for (int i = 0; i < paragraphSize; i++) {
            long offset = i * avg;
            NumberParagraph nn = new NumberParagraph();
            nn.setSId(minId + offset);
            long eId = nn.getSId() + avg - 1;
            if (i == paragraphSize - 1) {
                if (eId < maxId) {
                    eId = maxId;
                }
            }
            nn.setEId(eId);
            res.add(nn);
        }

        return res;
    }


    public void test() {
        BoundedPriorityQueue<BigDecimal> processQueue = new BoundedPriorityQueue<>(99, (o1, o2) -> {
            int i = o1.compareTo(o2);
            return Integer.compare(0, i);
        });
        boolean end = false;


        ReadProcessRunner readProcessRunner = new ReadProcessRunner(processQueue, end, null, null);
        Thread readThread = new Thread(readProcessRunner);
        readThread.start();

        System.out.println(Thread.currentThread().getName() + "-------- 继续执行主进程逻辑 -------");

        try {
            processQueue.add(BigDecimal.valueOf(0));
            TimeUnit.SECONDS.sleep(5);
            processQueue.add(BigDecimal.valueOf(0.1));
            TimeUnit.SECONDS.sleep(5);
            processQueue.add(BigDecimal.valueOf(0.5));
            TimeUnit.SECONDS.sleep(5);
            processQueue.add(BigDecimal.valueOf(0.9));
            TimeUnit.SECONDS.sleep(5);
            processQueue.add(BigDecimal.valueOf(1));
            System.out.println(Thread.currentThread().getName() + " 全部结束");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            readProcessRunner.interrupt();
        }
    }


    public static void main(String[] args) throws Exception {
        PurchaseBillService billService = new PurchaseBillService();
        billService.test();
        System.in.read();
    }


    @Data
    public static class ExportIdEvent<NumberParagraph> {

        private NumberParagraph np;

        public ExportIdEvent(NumberParagraph np) {
            this.np = np;
        }
    }

    public static class ExportIdEventFactory implements EventFactory<ExportIdEvent<NumberParagraph>> {

        private NumberParagraph np;

        @Override
        public ExportIdEvent<NumberParagraph> newInstance() {
            return new ExportIdEvent<>(this.np);
        }
    }

    @Data
    public class EventConsumer implements WorkHandler<ExportIdEvent<NumberParagraph>> {

        private List<PurchaseBillRow> container;
        private WdtOrderDetailWrapperMapper wdtOrderDetailWrapperMapper;
        private CountDownLatch countDownLatch;
        private List<Integer> types;
        private List<String> warehouseNos;
        private List<String> operateTimes;
        private List<String> skuCodes;

        private Boolean needExportProgress;
        private BoundedPriorityQueue<BigDecimal> queue;
        private AtomicLong finishCount;
        private Integer total;

        public EventConsumer() {
        }


        @Override
        public void onEvent(ExportIdEvent<NumberParagraph> event) {
            Long sId = event.getNp().getSId();
            Long eId = event.getNp().getEId();
            try {
                log.info("系统采购账单导出-消费者数据处理开始.thread:{},sId:{},eId:{}", Thread.currentThread().getName(), sId, eId);
                List<WdtOrderDetailWrapper> list = wdtOrderDetailWrapperMapper.selectListByParams(warehouseNos, skuCodes, operateTimes, sId, eId, types);
                list.forEach(val -> {
                    PurchaseBillRow convert = convert(val);
                    container.add(convert);
                });
                if (Objects.nonNull(queue)) {
                    finishCount.addAndGet(list.size());
                    queue.offer(getProcess(finishCount, total));
                }
                log.info("系统采购账单导出-消费者数据处理结束.thread:{},sId:{},eId:{}", Thread.currentThread().getName(), sId, eId);
            } catch (Exception e) {
                log.error("系统采购账单导出异常.sId:{},eId:{}", sId, eId, e);
                throw new RuntimeException("导出异常");
            } finally {
                countDownLatch.countDown();
            }
        }

    }

    public static class ExportIdExceptionHandler implements ExceptionHandler<ExportIdEvent<NumberParagraph>> {

        private Boolean exceptionFlag;

        public ExportIdExceptionHandler(Boolean exceptionFlag) {
            this.exceptionFlag = exceptionFlag;
        }

        @Override
        public void handleEventException(Throwable ex, long sequence, ExportIdEvent<NumberParagraph> event) {
            this.exceptionFlag = true;
        }

        @Override
        public void handleOnStartException(Throwable ex) {

        }

        @Override
        public void handleOnShutdownException(Throwable ex) {

        }
    }


//    public void loopWriteHandle(ExcelWriter excelWriter, WriteSheet writeSheet, List<Integer> wrapperTypes,
//                                Collection<String> warehouseNos, Collection<String> operateTimes,
//                                Long startId, Long endId) throws InterruptedException {
//
//        log.info("loopWriteHandle param. wrapperTypes:{},warehouseNos:{},operateTimes:{},startId:{},endId:{}",
//                JsonUtil.toJson(wrapperTypes), JsonUtil.toJson(warehouseNos), JsonUtil.toJson(operateTimes), startId, endId);
//        List<WdtOrderDetailWrapper> subList = wdtOrderDetailWrapperMapper.selectList(warehouseNos, operateTimes, startId, endId, wrapperTypes);
//        int size = subList.size();
//        log.info("系统采购单账单处理loopWriteHandle.size:{}", size);
//        if (CollUtil.isEmpty(subList)) {
//            return;
//        }
//
//        CountDownLatch countDownLatch = new CountDownLatch(size);
//        Semaphore semaphore = new Semaphore(6);
//        List<PurchaseBillRow> targetList = new CopyOnWriteArrayList<>();
//        for (int i = 0; i < subList.size(); i++) {
//            ThreadUtil.execute(PoolEnum.PURCHASE_POOL, () -> {
//                try {
//                    semaphore.acquire();
//                    targetList.add(convert(subList.get(0)));
//                } catch (Exception e) {
//                    log.error("系统采购单账单处理convert异常.sId:{} eId:{}", startId, endId, e);
//                } finally {
//                    semaphore.release();
//                    countDownLatch.countDown();
//                }
//            });
//        }
//
//        countDownLatch.await();
//
//        if (CollUtil.isNotEmpty(targetList)) {
//            excelWriter.write(targetList, writeSheet);
//        }
//
//    }

    private Map<String, Long> getIdRange(Collection<String> warehouseNos, Collection<String> operateTimes,
                                         List<Integer> wrapperTypes, Integer count, Integer i) {
        Map<String, Long> map = new HashMap<>(2);
        map.put("minId", null);
        map.put("maxId", null);

        // 左闭右闭。
        int cycleOffset = i * SHEET_SIZE;
        Optional<WdtOrderDetailWrapper> minIdOptional = wrapperService.lambdaQuery()
                .select(WdtOrderDetailWrapper::getId)
                .in(WdtOrderDetailWrapper::getType, wrapperTypes)
                .in(CollUtil.isNotEmpty(warehouseNos), WdtOrderDetailWrapper::getWarehouseNo, warehouseNos)
                .in(CollUtil.isNotEmpty(operateTimes), WdtOrderDetailWrapper::getOperateTime, operateTimes)
                .orderByAsc(WdtOrderDetailWrapper::getId)
                .select(WdtOrderDetailWrapper::getId)
                .last("limit " + cycleOffset + ",1").oneOpt();
        minIdOptional.ifPresent(wdtOrderDetailWrapper -> map.put("minId", Long.valueOf(wdtOrderDetailWrapper.getId())));

        int nextIdOffSet = Math.min((count - cycleOffset), SHEET_SIZE);
        Optional<WdtOrderDetailWrapper> maxIdOptional = wrapperService.lambdaQuery()
                .select(WdtOrderDetailWrapper::getId)
                .in(CollUtil.isNotEmpty(warehouseNos), WdtOrderDetailWrapper::getWarehouseNo, warehouseNos)
                .in(CollUtil.isNotEmpty(operateTimes), WdtOrderDetailWrapper::getOperateTime, operateTimes)
                .in(WdtOrderDetailWrapper::getType, wrapperTypes)
                .orderByAsc(WdtOrderDetailWrapper::getId)
                .select(WdtOrderDetailWrapper::getId)
                .last("limit " + ((cycleOffset + nextIdOffSet) - 1) + ",1").oneOpt();
        maxIdOptional.ifPresent(wdtOrderDetailWrapper -> map.put("maxId", Long.valueOf(wdtOrderDetailWrapper.getId())));

        return map;
    }


    private PurchaseBillRow convert(WdtOrderDetailWrapper wrapper) {
        PurchaseBillRow purchaseBillRow = new PurchaseBillRow();

        if (wrapper.getType().equals(WrapperType.STOCK_IN_REFUND.getValue())) {
            return convertForRefund(wrapper);
        }

        if (wrapper.getType().equals(WrapperType.STOCK_IN_PRE.getValue())) {
            return convertForPreRefund(wrapper);
        }

        WdtOrder wdtOrder = getWdtOrder(wrapper.getTradeNo());
        WdtOrderDetail wdtOrderDetail = Objects.isNull(wdtOrder) ? null : getWdtOrderDetail(wdtOrder.getTradeId(), wrapper.getSkuCode());

        purchaseBillRow.setShop(Objects.isNull(wdtOrder) ? "" : wdtOrder.getShopName());
        purchaseBillRow.setWarehouse(getWarehouse(wrapper.getWarehouseNo()));
        purchaseBillRow.setOriginalNo(Objects.isNull(wdtOrder) ? "" : wdtOrder.getSrcTids());
        purchaseBillRow.setOrderNo(wrapper.getTradeNo());
        purchaseBillRow.setGoodName(Objects.isNull(wdtOrderDetail) ? "" : wdtOrderDetail.getGoodsName());
        purchaseBillRow.setErpCode(wrapper.getSkuCode());
        purchaseBillRow.setGoodCode(Objects.isNull(wdtOrderDetail) ? "" : wdtOrderDetail.getGoodsNo());
        purchaseBillRow.setSpecification(Objects.isNull(wdtOrderDetail) ? "" : wdtOrderDetail.getSpecName());
        purchaseBillRow.setErpQuantity(wrapper.getQuantity().toString());
        purchaseBillRow.setErpPrice(wrapper.getPrice().toString());
        purchaseBillRow.setLogisticsCompany(Objects.isNull(wdtOrder) ? "" : wdtOrder.getLogisticsName());
        purchaseBillRow.setLogisticsNo(Objects.isNull(wdtOrder) ? "" : wdtOrder.getLogisticsNo());
        purchaseBillRow.setGoodSalePrice(Objects.isNull(wdtOrderDetail) ? "" : wdtOrderDetail.getOrderPrice().toString());
        purchaseBillRow.setPayTime(DateUtil.format(wrapper.getPayTime()));

        if (Objects.isNull(wdtOrder)) {
            purchaseBillRow.setReceiveAddress(StrUtil.EMPTY);
        } else {
            String s = completeAddress(wdtOrder.getReceiverProvince(), wdtOrder.getReceiverCity(), wdtOrder.getReceiverDistrict());
            purchaseBillRow.setReceiveAddress(s);
        }

        purchaseBillRow.setCustomerMsg(Objects.isNull(wdtOrder) ? "" : wdtOrder.getBuyerMessage());
        purchaseBillRow.setCustomerServiceMsg(Objects.isNull(wdtOrder) ? "" : wdtOrder.getCsRemark());
        purchaseBillRow.setPrintMsg(Objects.isNull(wdtOrder) ? "" : wdtOrder.getPrintRemark());

        purchaseBillRow.setSuitNo(wrapper.getSuiteNo());
        if (WrapperType.STOCK_OUT_COMBINATION.getValue().equals(wrapper.getType())) {
            SuitInfo erpSuitInfo = getErpSuitInfo(wrapper.getSuiteNo(), wrapper.getPayTime(), wrapper.getOperateTime());
//            purchaseBillRow.setSuitNo(wrapper.getSuiteNo());
            purchaseBillRow.setSuitName(erpSuitInfo.getName());
            purchaseBillRow.setErpSuitPrice(erpSuitInfo.getPrice());
            purchaseBillRow.setErpSuitActivityPrice(erpSuitInfo.getActivityPrice());
            purchaseBillRow.setErpSuitActivityDesc(
                    (StrUtil.isBlank(erpSuitInfo.getActivityType()) ? "" : erpSuitInfo.getActivityType()) +
                            "。" +
                            (StrUtil.isBlank(erpSuitInfo.getActivityDesc()) ? "" : erpSuitInfo.getActivityDesc())
            );
        }

        String wdtStockOutOrderNo = Objects.isNull(wdtOrder) ? "" : wdtOrder.getStockoutNo();
        purchaseBillRow.setStockOutOrderNo(wdtStockOutOrderNo);
        WdtSaleStockOutOrder wdtSaleStockOutOrder = getWdtSaleStockOutOrder(wdtStockOutOrderNo);
        purchaseBillRow.setStockOutStatus(Objects.isNull(wdtSaleStockOutOrder) ? "" :
                getStockOutStatus(wdtSaleStockOutOrder.getStatus()));

        purchaseBillRow.setGoodTotalAmount(Objects.isNull(wdtOrder) ? "" : wdtOrder.getGoodsAmount().toString());
        purchaseBillRow.setGoodTotalSalePrice(Objects.isNull(wdtOrder) ? "" : wdtOrder.getReceivable().toString());
        purchaseBillRow.setOrderType(Objects.isNull(wdtOrder) ? "" : getOrderType(wdtOrder.getTradeType()));

        String deliveryTime;
        if (Objects.isNull(wdtOrder)) {
            deliveryTime = "找不到订单" + wrapper.getTradeNo();
        } else {
            if (Objects.isNull(wdtOrder.getConsignTime())) {
                deliveryTime = "空时间";
            } else {
                deliveryTime = DateUtil.format(wdtOrder.getConsignTime());
            }
        }
        purchaseBillRow.setDeliveryTime(deliveryTime);

        purchaseBillRow.setGiftType(getGiftType(wrapper.getIsGift()));

        return purchaseBillRow;
    }


    private PurchaseBillRow convertForRefund(WdtOrderDetailWrapper wrapper) {
        Long tradeId = wrapper.getTradeId();
        WdtRefundStockInOrder wdtRefundOrder = getWdtRefundOrder(tradeId);
        WdtRefundStockInOrderDetails wdtRefundOrderDetail = getWdtRefundOrderDetail(tradeId, wrapper.getSkuCode());

        PurchaseBillRow row = new PurchaseBillRow();
        row.setShop(Objects.isNull(wdtRefundOrder) ? "" : wdtRefundOrder.getShopName());
        row.setWarehouse(getWarehouse(wrapper.getWarehouseNo()));
        row.setOriginalNo(Objects.isNull(wdtRefundOrder) ? "" : wdtRefundOrder.getTidList());
        row.setOrderNo(Objects.isNull(wdtRefundOrder) ? "" : wdtRefundOrder.getTradeNoList());
        row.setGoodName(Objects.isNull(wdtRefundOrderDetail) ? "" : wdtRefundOrderDetail.getGoodsName());
        row.setErpCode(wrapper.getSkuCode());
        row.setGoodCode(Objects.isNull(wdtRefundOrderDetail) ? "" : wdtRefundOrderDetail.getGoodsNo());
        row.setSpecification(Objects.isNull(wdtRefundOrderDetail) ? "" : wdtRefundOrderDetail.getSpecName());
        row.setErpQuantity(wrapper.getQuantity().toString());
        row.setErpPrice(wrapper.getPrice().toString());
        row.setLogisticsCompany(Objects.isNull(wdtRefundOrderDetail) ? "" : wdtRefundOrderDetail.getLogisticsName());
        row.setLogisticsNo(Objects.isNull(wdtRefundOrderDetail) ? "" : wdtRefundOrderDetail.getLogisticsNo());
        row.setGoodSalePrice("");
        // 退货单审核时间
        row.setPayTime(Objects.isNull(wdtRefundOrder) ? "" : DateUtil.format(wdtRefundOrder.getCheckTime()));
        row.setCustomerMsg("");
        row.setCustomerServiceMsg(Objects.isNull(wdtRefundOrderDetail) ? "" : wdtRefundOrderDetail.getRemark());
        row.setSuitNo("");
        row.setErpSuitPrice("");
        row.setSuitName("");
        row.setStockOutStatus("");
        row.setStockOutOrderNo("");
        row.setGoodTotalAmount("");
        // 总成本
        row.setGoodTotalSalePrice(Objects.isNull(wdtRefundOrderDetail) ? "" : wdtRefundOrderDetail.getTotalCost().toString());
        row.setOrderType("");
        row.setDeliveryTime("");
        row.setGiftType("");
        return row;
    }

    private PurchaseBillRow convertForPreRefund(WdtOrderDetailWrapper wrapper) {
        PurchaseBillRow row = new PurchaseBillRow();
        row.setErpPrice(wrapper.getPrice().toString());
        row.setErpCode(wrapper.getSkuCode());
        row.setGoodCode(wrapper.getSkuCode());
        row.setErpQuantity(wrapper.getQuantity().toString());
        return row;
    }

    private WdtRefundStockInOrder getWdtRefundOrder(Long stockInId) {
        if (Objects.isNull(stockInId)) {
            return null;
        }
        List<WdtRefundStockInOrder> list = iWdtRefundStockInOrderService.lambdaQuery()
                .eq(WdtRefundStockInOrder::getStockinId, stockInId).list();
        if (CollUtil.isEmpty(list)) {
            return null;
        } else {
            return list.get(0);
        }
    }

    /**
     * 获取销售退库入库单详情
     *
     * @param stockInId 入库单id
     * @param specNo    商家编码
     * @return
     */
    private WdtRefundStockInOrderDetails getWdtRefundOrderDetail(Long stockInId, String specNo) {
        if (Objects.isNull(stockInId)) {
            return null;
        }
        List<WdtRefundStockInOrderDetails> list = iWdtRefundStockInOrderDetailsService.lambdaQuery()
                .eq(WdtRefundStockInOrderDetails::getStockinId, stockInId)
                .eq(WdtRefundStockInOrderDetails::getSpecNo, specNo)
                .list();
        if (CollUtil.isEmpty(list)) {
            return null;
        } else {
            return list.get(0);
        }
    }

    /**
     * .put(1, Platform.TAOBAO)
     * .put(17, Platform.YOUZAN)
     * .put(69, Platform.DOUDIAN)
     * .put(75, Platform.KUAISHOU)
     * .put(56, Platform.XIAOHONGSHU)
     * .put(127, Platform.LAOBASHOP)
     *
     * @param erpPlatformType erp平台类型
     * @return 店铺名称
     */
    private String getShop(Integer erpPlatformType) {
        if (1 == erpPlatformType) {
            return "淘宝";
        } else if (5 == erpPlatformType) {
            return "有赞";
        } else if (2 == erpPlatformType) {
            return "抖店";
        } else if (6 == erpPlatformType) {
            return "快手";
        } else if (3 == erpPlatformType) {
            return "小红书";
        } else if (4 == erpPlatformType) {
            return "自研电商";
        } else {
            return "全平台";
        }
    }

    /**
     * 获取仓库名称
     *
     * @param warehouseNo 仓库编号
     * @return 仓库名称或者仓库编号
     */
    private String getWarehouse(String warehouseNo) {
        List<Warehouse> list = iWarehouseService.lambdaQuery().eq(Warehouse::getNo, warehouseNo)
                .list();
        if (CollUtil.isEmpty(list)) {
            return warehouseNo;
        } else {
            return list.get(0).getName();
        }
    }

    /**
     * 获取wdtOrder信息
     *
     * @param tradeNo JYxxxxxx 订单号
     * @return wdtOrderEntity or null
     */
    private WdtOrder getWdtOrder(String tradeNo) {
        if (StrUtil.isBlank(tradeNo)) {
            return null;
        } else {
            List<WdtOrder> list = iWdtOrderService.lambdaQuery().eq(WdtOrder::getTradeNo, tradeNo)
                    .last("limit 1")
                    .list();
            if (CollUtil.isEmpty(list)) {
                return null;
            } else {
                return list.get(0);
            }
        }
    }

    /**
     * 获取wdtOrderDetail
     *
     * @param tradeId 订单唯一键，wdtOrder和wdtOrderDetail的关联字段
     * @return wdtOrderDetailEntity or null
     */
    private WdtOrderDetail getWdtOrderDetail(Long tradeId, String specNo) {
        if (Objects.isNull(tradeId)) {
            return null;
        } else {
            List<WdtOrderDetail> list = wdtOrderDetailMapper.getOneByTradeIdAndSpecNo(tradeId, specNo);
            if (CollUtil.isEmpty(list)) {
                return null;
            } else {
                return list.get(0);
            }
        }
    }

    /**
     * 获取wdt销售出库单
     *
     * @param stockOutOrderNo 出库单号
     * @return null or 销售出库实体对象
     */
    private WdtSaleStockOutOrder getWdtSaleStockOutOrder(String stockOutOrderNo) {
        if (StrUtil.isBlank(stockOutOrderNo)) {
            return null;
        } else {
            List<WdtSaleStockOutOrder> list = wdtSaleStockOutOrderMapper.selectByStockOutOrderNo(stockOutOrderNo);
            if (CollUtil.isEmpty(list)) {
                return null;
            } else {
                return list.get(0);
            }
        }
    }

    /**
     * 5已取消
     * 10待放回(拣货待放回), 小于该值的都是已取消的单子
     * 51 缺货
     * 53 WMS已接单
     * 54 获取电子面单
     * 60 待分配
     * 61 排队中
     * 63 待补货
     * 65 待处理
     * 70 待发货
     * 73 爆款锁定
     * 75 待拣货
     * 77 拣货中,PDA拣货后
     * 79 已拣货
     * 90 延时发货
     * 110已完成',
     *
     * @return
     */
    private String getStockOutStatus(Integer status) {
        if (Objects.isNull(status)) {
            return "空";
        }
        if (10 == status) {
            return "待放回(拣货待放回)";
        } else if (status < 10) {
            return "已取消";
        } else if (51 == status) {
            return "缺货";
        } else if (53 == status) {
            return "WMS已接单";
        } else if (54 == status) {
            return "获取电子面单";
        } else if (60 == status) {
            return "待分配";
        } else if (61 == status) {
            return "排队中";
        } else if (63 == status) {
            return "待补货";
        } else if (65 == status) {
            return "待处理";
        } else if (70 == status) {
            return "待发货";
        } else if (73 == status) {
            return "爆款锁定";
        } else if (75 == status) {
            return "待捡货";
        } else if (77 == status) {
            return "拣货中,PDA拣货后";
        } else if (79 == status) {
            return "已捡货";
        } else if (90 == status) {
            return "延时发货";
        } else if (110 == status) {
            return "已完成";
        } else {
            return status.toString();
        }
    }

    /**
     * 获取赠品类型
     * 0、非赠品 1、自动赠送 2、手工赠送 4、周期购赠送 8、平台赠送
     *
     * @param giftType
     * @return
     */
    private String getGiftType(Integer giftType) {
        if (Objects.isNull(giftType)) {
            return "空";
        }
        if (0 == giftType) {
            return "非赠品";
        } else if (1 == giftType) {
            return "自动赠送";
        } else if (2 == giftType) {
            return "手工赠送";
        } else if (4 == giftType) {
            return "周期赠送";
        } else if (8 == giftType) {
            return "平台赠送";
        } else {
            return giftType.toString();
        }
    }


    /**
     * 订单类型
     * 订单类型: 1、网店销售 2、线下订单 3、售后换货 4、批发业务 7、现款销售 8、分销
     * 101、自定义类型一 102、自定义类型二 103、自定义类型三 104、自定义类型四 105、自定义类型五 106、自定义类型六
     *
     * @param tradeType
     * @return
     */
    public static String getOrderType(Integer tradeType) {
        if (Objects.isNull(tradeType)) {
            return "空";
        }
        if (1 == tradeType) {
            return "网店销售";
        } else if (2 == tradeType) {
            return "线下订单";
        } else if (3 == tradeType) {
            return "售后换货";
        } else if (4 == tradeType) {
            return "批发业务";
        } else if (7 == tradeType) {
            return "现款销售";
        } else if (8 == tradeType) {
            return "分销";
        } else if (101 == tradeType) {
            return "补发";
        } else if (102 == tradeType) {
            return "延迟发货";
        } else if (103 == tradeType) {
            return "活动中奖订单";
        } else if (104 == tradeType) {
            return "运营申请";
        } else if (105 == tradeType) {
            return "社群销售订单";
        } else if (106 == tradeType) {
            return "售后卡";
        } else {
            return tradeType.toString();
        }
    }

    @Data
    static class SuitInfo {
        String name;
        String price;

        String activityPrice;
        String activityType;
        String activityDesc;
    }

    /**
     * 获取组合装信息
     *
     * @param suitNo      组合装编码
     * @param payTime     下单时间
     * @param operateTime yyyyMM
     * @return
     */
    private SuitInfo getErpSuitInfo(String suitNo, Long payTime, String operateTime) {
        SuitInfo info = new SuitInfo();
        CombinationItem combinationItem = iCombinationItemService.getByItemCode(suitNo);
        if (Objects.isNull(combinationItem)) {
            info.setPrice(suitNo + " 编号查询为空");
            info.setName(payTime.toString());
            return info;
        }
        BigDecimal costPriceByPayTime = commonUtil.getCostPriceByPayTime(suitNo, payTime);
        BigDecimal suitPrice = Objects.isNull(costPriceByPayTime) ? combinationItem.getProcurementPrice() : costPriceByPayTime;
        info.setPrice(suitPrice.toString());
        info.setName(combinationItem.getName());

        String month = operateTime.substring(0, 4) + "/" + operateTime.substring(4);
        List<Purchase> list = iPurchaseService.lambdaQuery().eq(Purchase::getItemSku, suitNo)
                .eq(Purchase::getMonth, month).list();
        if (CollUtil.isNotEmpty(list)) {
            Purchase val = list.get(0);
            FavourableType favourableType = val.getFavourableType();
            if (FavourableType.ACCORDING_TIME.equals(favourableType)) {
                info.setActivityPrice(Objects.isNull(val.getPriceCost()) ? "" : val.getPriceCost().toString());
                info.setActivityType(FavourableType.ACCORDING_TIME.getDesc());
                info.setActivityDesc(DateUtil.format(val.getStartTime()) + "至" + DateUtil.format(val.getEndTime()));
            }
            if (FavourableType.FAVOURABLE_TIME.equals(favourableType)) {
                info.setActivityPrice(Objects.isNull(val.getPriceCost()) ? "" : val.getPriceCost().toString());
                info.setActivityType(FavourableType.FAVOURABLE_TIME.getDesc());
                info.setActivityDesc(val.getNumCost() + "。" + val.getContent());
            }
        }

        return info;
    }

    public String completeAddress(Integer province, Integer city, Integer district) {
        List<Integer> collect = ListUtil.of(province, city, district).stream().filter(val -> val > 0).collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)) {
            return StrUtil.EMPTY;
        }
        List<String> collect1 = collect.stream().map(String::valueOf).collect(Collectors.toList());
        List<RegionRelevantDo> nameByCodeList = regionRelevantMapper.getNameByCode(collect1);
        if (CollUtil.isEmpty(nameByCodeList)) {
            return StrUtil.EMPTY;
        }

        Map<String, String> nameByCode = nameByCodeList.stream().collect(Collectors.toMap(RegionRelevantDo::getCode, RegionRelevantDo::getName, (a, b) -> a));
        StringBuilder res = new StringBuilder();
        if (Objects.nonNull(province)) {
            res.append(nameByCode.getOrDefault(province.toString(), StrUtil.EMPTY));
        }
        if (Objects.nonNull(city)) {
            res.append(nameByCode.getOrDefault(city.toString(), StrUtil.EMPTY));
        }
        if (Objects.nonNull(district)) {
            res.append(nameByCode.getOrDefault(district.toString(), StrUtil.EMPTY));
        }
        return res.toString();
    }


    /**
     * 10:已取消;20:待审核;30:已审核;40已推送:80:已结算;85:待过账;86:已过账;87:成本确认;90:已完成
     *
     * @param status
     * @return
     */
    public static String getRefundStatusStr(Integer status) {
        if (Objects.isNull(status)) {
            return "空";
        }
        if (10 == status) {
            return "已取消";
        } else if (20 == status) {
            return "待审核";
        } else if (30 == status) {
            return "已审核";
        } else if (40 == status) {
            return "已推送";
        } else if (80 == status) {
            return "已结算";
        } else if (85 == status) {
            return "待过账";
        } else if (86 == status) {
            return "已过账";
        } else if (87 == status) {
            return "成本确认";
        } else if (90 == status) {
            return "已完成";
        } else {
            return status.toString();
        }
    }

}
