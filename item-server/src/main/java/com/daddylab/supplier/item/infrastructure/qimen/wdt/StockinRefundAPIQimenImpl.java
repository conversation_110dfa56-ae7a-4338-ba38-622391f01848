package com.daddylab.supplier.item.infrastructure.qimen.wdt;

import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.StockinRefundAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.StockinRefundQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.StockinRefundQueryWithDetailResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.daddylab.supplier.item.infrastructure.qimen.QimenConfig;
import com.daddylab.supplier.item.infrastructure.qimen.QimenWdtStockinRefundAPI;
import com.qimencloud.api.QimenCloudClient;
import com.qimencloud.api.scene3ldsmu02o9.request.WdtWmsStockinRefundQuerywithdetailRequest;
import com.qimencloud.api.scene3ldsmu02o9.response.WdtWmsStockinRefundQuerywithdetailResponse;
import com.taobao.api.ApiException;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2022/1/17
 */
public class StockinRefundAPIQimenImpl extends WdtAPIQimenImplBase implements StockinRefundAPI {

    QimenWdtStockinRefundAPI qimenAPI;

    public StockinRefundAPIQimenImpl(QimenCloudClient qimenClient,
            QimenConfig qimenConfig,
            WdtConfig wdtConfig,
            int configIndex
    ) {
        super(qimenClient, qimenConfig, wdtConfig, configIndex);
    }

    @Override
    protected void initApiInstance(QimenCloudClient qimenClient, QimenConfig qimenConfig,
            WdtConfig wdtConfig, int configIndex) {
        qimenAPI = new QimenWdtStockinRefundAPI(qimenClient, qimenConfig, wdtConfig, configIndex);
    }

    @Override
    public StockinRefundQueryWithDetailResponse queryWithDetail(
            StockinRefundQueryWithDetailParams params, Pager pager) throws WdtErpException {
        try {
            final WdtWmsStockinRefundQuerywithdetailResponse response = qimenAPI.queryWithDetail(
                    Assembler.INST.toQimenParams(params), pager.getPageNo() + 1, pager.getPageSize()
            );
            return checkAndReturnData(response, StockinRefundQueryWithDetailResponse.class);
        } catch (ApiException e) {
            throw transformException(e);
        }
    }

    @Mapper
    interface Assembler {

        Assembler INST = Mappers.getMapper(Assembler.class);

        WdtWmsStockinRefundQuerywithdetailRequest.Params toQimenParams(
                StockinRefundQueryWithDetailParams params);
    }
}
