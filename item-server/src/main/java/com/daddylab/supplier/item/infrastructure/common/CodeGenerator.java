package com.daddylab.supplier.item.infrastructure.common;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.po.TableFill;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Scanner;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/14 2:26 下午
 * @description
 */
public class CodeGenerator {
    /**
     * <p>
     * 读取控制台内容
     * </p>
     */
    public static String scanner(String tip) {
        Scanner scanner = new Scanner(System.in);
        StringBuilder help = new StringBuilder();
        help.append("请输入" + tip + "：");
        System.out.println(help.toString());
        if (scanner.hasNext()) {
            String ipt = scanner.next();
            if (StringUtils.isNotBlank(ipt)) {
                return ipt;
            }
        }
        throw new MybatisPlusException("请输入正确的" + tip + "！");
    }

    public static void main(String[] args) {
        // 代码生成器
        AutoGenerator mpg = new AutoGenerator();

        // 全局配置
        // /Users/<USER>/Downloads/zz_project/supplier-goods/itareaem-project/item-project-infrastructure/src/main/java/com/daddylab/supplier/item/common
        GlobalConfig gc = new GlobalConfig();
        String projectPath = System.getProperty("user.dir") + "/item-server";
        gc.setFileOverride(true);
        gc.setOutputDir(projectPath + "/src/main/java");
        gc.setAuthor("daddy");
        gc.setOpen(false);
        // gc.setSwagger2(true); 实体属性 Swagger2 注解
        mpg.setGlobalConfig(gc);

        // 数据源配置
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setDriverName("com.mysql.cj.jdbc.Driver");
//        dsc.setUrl("jdbc:mysql://************:3306/supplier?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&useSSL=false");
//        dsc.setUsername("root");
//        dsc.setPassword("P@ssw0rd");
        dsc.setUrl("*******************************************************************************************************************************************************************************");
        dsc.setUsername("root");
        dsc.setPassword("aa6655321");
//        dsc.setUrl("**************************************************************************************************************************************************************************************************************************************************");
//        dsc.setUsername("lk");
//        dsc.setPassword("FP5dvZpFekKs2sSK");

//
//        dsc.setUrl("***********************************************************************************************************************************************************************************************************************************************");
//        dsc.setUsername("bigdata");
//        dsc.setPassword("F0mvrcYhWqtiatnnyuHv");
        mpg.setDataSource(dsc);

        // 包配置
        PackageConfig pc = new PackageConfig();
        pc.setModuleName("infrastructure.gatewayimpl.db");
        pc.setParent("com.daddylab.supplier.item");
        mpg.setPackageInfo(pc);

        // 自定义配置
        InjectionConfig cfg = new InjectionConfig() {
            @Override
            public void initMap() {
                // to do nothing
            }
        };

        // 如果模板引擎是 freemarker
        String templatePath = "/templates/mapper.xml.ftl";
        // 如果模板引擎是 velocity
        // String templatePath = "/templates/mapper.xml.vm";

        // 自定义输出配置
        List<FileOutConfig> focList = new ArrayList<>();
        // 自定义配置会被优先输出
        focList.add(new FileOutConfig(templatePath) {
            @Override
            public String outputFile(TableInfo tableInfo) {
                // 自定义输出文件名 ， 如果你 Entity 设置了前后缀、此处注意 xml 的名称会跟着发生变化！！
                return projectPath + "/src/main/resources/mapper/"
                        + "/" + tableInfo.getEntityName() + "Mapper" + StringPool.DOT_XML;
            }
        });
        /*
        cfg.setFileCreate(new IFileCreate() {
            @Override
            public boolean isCreate(ConfigBuilder configBuilder, FileType fileType, String filePath) {
                // 判断自定义文件夹是否需要创建
                checkDir("调用默认方法创建的目录，自定义目录用");
                if (fileType == FileType.MAPPER) {
                    // 已经生成 mapper 文件判断存在，不想重新生成返回 false
                    return !new File(filePath).exists();
                }
                // 允许生成模板文件
                return true;
            }
        });
        */
        cfg.setFileOutConfigList(focList);
        mpg.setCfg(cfg);

        // 配置模板
        TemplateConfig templateConfig = new TemplateConfig();

        // 配置自定义输出模板
        //指定自定义模板路径，注意不要带上.ftl/.vm, 会根据使用的模板引擎自动识别
         templateConfig.setEntity("template/new-entity.java");
        // templateConfig.setService();
        templateConfig.setController("");
        templateConfig.setXml(null);
        mpg.setTemplate(templateConfig);

        // 策略配置
        StrategyConfig strategy = new StrategyConfig();
        strategy.setNaming(NamingStrategy.underline_to_camel);
        strategy.setColumnNaming(NamingStrategy.underline_to_camel);
        strategy.setEntityLombokModel(true);
        strategy.setLogicDeleteFieldName("is_del");

        List<TableFill> tableFillList = new ArrayList<>();
        tableFillList.add(new TableFill("created_at", FieldFill.INSERT));
        tableFillList.add(new TableFill("created_uid", FieldFill.INSERT));
        tableFillList.add(new TableFill("updated_at", FieldFill.INSERT_UPDATE));
        tableFillList.add(new TableFill("updated_uid", FieldFill.INSERT_UPDATE));
        tableFillList.add(new TableFill("deleted_at", FieldFill.DEFAULT));

        strategy.setTableFillList(tableFillList);
        strategy.setSuperMapperClass("com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper");
        strategy.setSuperServiceClass(IDaddyService.class);
        strategy.setSuperServiceImplClass(DaddyServiceImpl.class);
//        strategy.setSuperEntityClass("com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BaseEntity");
        // 写于父类中的公共字段
//        strategy.setSuperEntityColumns("id,created_at,updated_at,created_uid,updated_uid,is_del");
        strategy.setInclude(scanner("表名，多个英文逗号分割").split(","));
        strategy.setControllerMappingHyphenStyle(true);
        strategy.setTablePrefix(pc.getModuleName() + "_");

        mpg.setStrategy(strategy);
        mpg.setTemplateEngine(new FreemarkerTemplateEngine());
        mpg.execute();
    }
}

