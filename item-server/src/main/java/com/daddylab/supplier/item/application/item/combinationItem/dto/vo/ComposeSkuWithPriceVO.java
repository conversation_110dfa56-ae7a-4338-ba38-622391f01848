package com.daddylab.supplier.item.application.item.combinationItem.dto.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/7 10:03 上午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("sku构成明细列表（带价格）")
public class ComposeSkuWithPriceVO extends ComposeSkuVO {

    @ApiModelProperty("采购成本")
    private BigDecimal procurement;

    @ApiModelProperty("日常销售价")
    private BigDecimal sales;

}
