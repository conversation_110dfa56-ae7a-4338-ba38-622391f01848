package com.daddylab.supplier.item.application.platformItem.model;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2022/1/7
 */
@Data
public class PlatformItemSkuStat {
    private int skuNum;
    private BigDecimal avgPrice;
    private int totalStockNum;
    private LocalDateTime lastModified;
    private int onSaleCount;
    private int notOnSaleCount;
}
