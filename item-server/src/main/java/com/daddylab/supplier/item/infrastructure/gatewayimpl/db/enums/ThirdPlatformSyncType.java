package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 第三方平台商品同步类型
 * <p>
 * 同步类型。31抖音上新。32抖音编辑。
 */
@Getter
@AllArgsConstructor
public enum ThirdPlatformSyncType implements IEnum<Integer> {
    MINI_NEW(11, "小程序上新"),
    MINI_EDIT(12, "小程序编辑"),
    MINI_OVERRIDE(13, "小程序全量更新"),
    TAOBAO_WINROBOT(21, "淘宝影刀"),
    DOUDIAN_NEW(31, "抖店上新"),
    DOUDIAN_EDIT(32, "抖店编辑"),
    ;

    private final Integer value;
    private final String desc;

}
