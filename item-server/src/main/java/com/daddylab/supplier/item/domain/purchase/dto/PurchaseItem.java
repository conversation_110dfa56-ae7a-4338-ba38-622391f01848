package com.daddylab.supplier.item.domain.purchase.dto;

import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.javers.core.metamodel.annotation.Entity;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName PurchaseItem.java
 * @description 采购商品相关信息
 * @createTime 2022年03月23日 17:15:00
 */
@Entity
@Data
public class PurchaseItem {

    @ApiModelProperty(value = "商品id")
    private Long itemId;

    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    @ApiModelProperty(value = "供货指定编码")
    private String providerSpecifiedCode;

    @ApiModelProperty(value = "商品SPU")
    private String spuCode;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "商品规格")
    private String itemAttr;

    @ApiModelProperty(value = "工厂/仓库")
    private String delivery;

    @ApiModelProperty(value = "供应商id")
    private Long providerId;

    @ApiModelProperty(value = "供应商姓名")
    private String providerName;

    @ApiModelProperty(value = "采购负责人id")
    private Long buyerId;

    @ApiModelProperty(value = "采购负责人姓名")
    private String buyerName;

    @ApiModelProperty("合作模式")
    private Integer businessLine;

    private List<CorpBizTypeDTO> corpBizType;

//    @ApiModelProperty(value = "合作方 层级")
//    private List<Integer> corpType;
//
//    @ApiModelProperty(value = "业务类型 层级")
//    private List<Integer> bizType;
}
