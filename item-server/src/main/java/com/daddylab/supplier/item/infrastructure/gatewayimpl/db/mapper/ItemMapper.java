package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.message.wechat.v2.statics.QwMsgQcStaticsBo;
import com.daddylab.supplier.item.application.message.wechat.v2.statics.QwMsgStaticsBo;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsVo;
import com.daddylab.supplier.item.controller.item.dto.*;
import com.daddylab.supplier.item.domain.exportTask.dto.item.ExportItemDto;
import com.daddylab.supplier.item.domain.exportTask.dto.item.ExportItemNoPriceDto;
import com.daddylab.supplier.item.domain.exportTask.dto.item.ExportSkuDto;
import com.daddylab.supplier.item.domain.exportTask.dto.item.ExportSkuNoPriceDto;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseItem;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemBaseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemInfoForSalesStockBizDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.types.item.ItemBuyerAllocStat;
import com.daddylab.supplier.item.types.item.ItemLaunchStatusObj;
import com.daddylab.supplier.item.types.item.ItemLiveVerbalTrickStatusObj;
import com.daddylab.supplier.item.types.item.ItemTrainingMaterialsStatusObj;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 商品 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Repository
public interface ItemMapper extends DaddyBaseMapper<Item> {

    List<NameDropDownVo> dropDownList(@Param("name") String name, @Param("offset") Integer offset,
                                      @Param("size") Integer size, @Param("useForItemLaunchPlan") Boolean useForItemLaunchPlan,
                                      @Param("providerId") Long providerId);

    /**
     * 分页查询
     *
     * @param itemPageQuery
     * @return
     */
    List<ItemPageVo> queryPage(@Param("param") ItemPageQuery itemPageQuery);

    List<ExportItemDto> queryExportItem(@Param("param") ExportCmd cmd);

    List<ExportItemNoPriceDto> queryExportItemNoPrice(@Param("param") ExportCmd cmd);

    List<ExportSkuDto> queryExportSku(@Param("param") ExportCmd cmd);

    List<ExportSkuNoPriceDto> queryExportSkuNoPrice(@Param("param") ExportCmd cmd);


    /**
     * 分页查询 计数统计
     *
     * @param itemPageQuery
     * @return
     */
    Integer queryPageCount(@Param("param") ItemPageQuery itemPageQuery);

    /**
     * 查询 商品详情，基础数据部分
     *
     * @param itemId
     * @return
     */
    ItemBaseDO queryDetailBase(@Param("itemId") Long itemId);

    /**
     * 查询商品继续信息（带商品主图）
     */
    ItemBaseDO queryDetailBaseWithImage(@Param("itemId") Long itemId);

    /**
     * 批量查询商品基础数据
     *
     * @param itemIds 商品ids
     * @return 基础数据
     */
    List<ItemBaseDO> queryDetailBaseWithImageByIds(@Param("itemIds") List<Long> itemIds);

    /**
     * 查询商品的简单信息
     *
     * @param cmd
     * @return
     */
    List<ItemSimpleViewVo> queryItemSimple(@Param("param") ItemSimpleViewCmd cmd);

    /**
     * 查询出 sku 维度的商品导出，导出的数据量。
     *
     * @param cmd
     * @return
     */
    Integer countExportSku(@Param("param") ExportCmd cmd);

//    ExportItemOtherDto queryOtherItemExportDto(@Param("param") Long itemId);

    @Select("select code from item where source = 0 order by id desc limit 1")
    String getLatestItemCode();

    /**
     * 查相关信息
     *
     * @param skuCode
     * @return
     */
    PurchaseItem getPurchaseBySku(String skuCode);

    NewGoodsVo getNewGoodsById(Long id);

    List<ItemBuyerDto> selectBatchBuyersByItemIds(@Param("itemIds") List<Long> itemIds);

    List<ItemWithLaunchPlanDto> selectBatchDtosByItemIds(@Param("itemIds") List<Long> itemIds);

    List<ItemPrincipalDto> selectBatchPrincipalUsersByItemIds(@Param("itemIds") List<Long> itemIds);

    @Select("select provider_id from item where id = (select item_id from item_sku where sku_code = #{skuCode})")
    Long selectProviderId(@Param("skuCode") String skuCode);

    ItemSkuDetailVo selectItemBaseInfo(@Param("itemId") Long itemId);

    List<ItemSkuDetailVo.SkuDetail> selectSkuDetails(@Param("itemId") Long itemId);

    String getItemCategoryPath(@Param("itemNo") String itemNo);

    List<AttrDto> selectItemAttrDtosByItemId(@Param("id") Long id);

    List<Item> selectIdModelByCodeBatchIncludeDeleted(List<String> goodsNos);

    List<ItemSku> selectSkuIdModelByCodeBatchIncludeDeleted(List<String> goodsNos);

    List<ItemSku> selectSkuByCodeBatchIncludeDeleted(List<String> goodsNos);

    /**
     * 库存管理-采购入库，销退入库，两种单据详情列表展示，需要的商品信息
     *
     * @param itemCodes
     * @return
     */
    List<ItemInfoForSalesStockBizDO> selectItemInfoForSalesStockBizDO(@Param("itemCodes") Collection<String> itemCodes);

    List<ItemBuyerAllocStat> getItemBuyerAllocStatByBuyerIds(@Param("userIds") List<Long> userIds);

    int deleteInvalidItemAttrs(Long itemId);

    List<QwMsgStaticsBo> staticsItemToBeImprove() ;

    List<QwMsgStaticsBo> staticsItemToBeDesign();

    List<QwMsgStaticsBo> staticsItemToBeLegalAudit();

    List<QwMsgQcStaticsBo> staticsItemToBeQcAudit();

    List<QwMsgStaticsBo> staticsItemToBeEdit();

    List<QwMsgStaticsBo> staticsItemLegalByItemIds(@Param("itemIds") Collection<Long> itemIds);

    List<QwMsgQcStaticsBo> staticsItemQcByItemIds(@Param("itemIds") Collection<Long> itemIds);

    /**
     * 根据商品上新状态（考虑合并逻辑）筛选商品列表
     * @param launchStatus 上新状态
     * @param auditStatus 审核状态
     * @return 商品上新状态对象
     */
    List<ItemLaunchStatusObj> selectByLaunchStatus(@Param("launchStatus") Integer launchStatus, @Param("auditStatus") Integer auditStatus);

    List<ItemLiveVerbalTrickStatusObj> selectByLiveVerbalTrickStatus(@Param("liveVerbalTrickStatus") Integer liveVerbalTrickStatus);

    List<ItemTrainingMaterialsStatusObj> selectByMaterialsStatus(@Param("materialsStatus") Integer materialsStatus);


}
