package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemStock;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemStockMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemStockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品库存 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Service
public class ItemStockServiceImpl extends DaddyServiceImpl<ItemStockMapper, ItemStock> implements IItemStockService {

    @Autowired
    ItemStockMapper itemStockMapper;

    @Override
    public Long getSkuStockNum(String skuCode) {
        return itemStockMapper.selectSkuStockNum(skuCode);
    }
}
