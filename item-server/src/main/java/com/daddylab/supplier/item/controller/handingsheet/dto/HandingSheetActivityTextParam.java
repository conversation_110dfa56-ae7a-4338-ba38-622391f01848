package com.daddylab.supplier.item.controller.handingsheet.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/24 14:23
 * @Description: 增加/编辑店铺活动富文本内容
 */
@Data
@ApiModel("增加或编辑店铺活动富文本内容")
public class HandingSheetActivityTextParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "店铺活动富文本内容ID（新增时不用传，编辑时必传）")
    private Long handingSheetActivityTextId;

    @ApiModelProperty(value = "盘货表ID")
    @NotNull(message = "盘货表ID不能为空")
    private Long handingSheetId;

    @ApiModelProperty(value = "活动名称")
    @NotBlank(message = "活动名称必填")
    private String activityName;

    @ApiModelProperty(value = "活动内容")
    @NotBlank(message = "活动内容必填")
    private String activityContent;
}
