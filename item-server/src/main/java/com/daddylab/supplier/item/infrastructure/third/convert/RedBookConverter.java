package com.daddylab.supplier.item.infrastructure.third.convert;

import com.daddylab.supplier.item.infrastructure.third.redbook.domain.query.RedBookItemPageQuery;
import com.daddylab.supplier.item.infrastructure.third.redbook.domain.query.SkuPageQuery;
import com.xiaohongshu.fls.opensdk.entity.product.request.v3.GetDetailSkuRequest;
import com.xiaohongshu.fls.opensdk.entity.product.request.v3.SearchItemListRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @class RedBookConvert.java
 * @description 小红书转换类
 * @date 2024-02-28 17:31
 */
@Mapper
public interface RedBookConverter {

    RedBookConverter INSTANCE = Mappers.getMapper(RedBookConverter.class);


    SearchItemListRequest.ItemSearchParam itemPageQueryToItemSearchParam(RedBookItemPageQuery itemPageQuery);

    @Mappings(
            @Mapping(source = "buyAble", target = "buyable")
    )
    GetDetailSkuRequest skuPageQueryToGetDetailSkuRequest(SkuPageQuery skuPageQuery);
}
