package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.S03Order;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.S03OrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IS03OrderService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 抖店订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Service
public class S03OrderServiceImpl extends DaddyServiceImpl<S03OrderMapper, S03Order> implements IS03OrderService {

}
