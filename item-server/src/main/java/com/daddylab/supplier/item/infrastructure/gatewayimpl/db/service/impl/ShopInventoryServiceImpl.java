/*
package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopInventory;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ShopInventoryMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopInventoryService;
import org.springframework.stereotype.Service;

*/
/**
 * <p>
 * 店铺分配库存信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 *//*

@Service
public class ShopInventoryServiceImpl extends DaddyServiceImpl<ShopInventoryMapper, ShopInventory> implements IShopInventoryService {

}
*/
