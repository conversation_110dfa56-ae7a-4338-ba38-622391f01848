package com.daddylab.supplier.item.controller.purchase;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.purchase.combinationPrice.CombinationPriceBizServiceImpl;
import com.daddylab.supplier.item.application.purchase.combinationPrice.PricePageQuery;
import com.daddylab.supplier.item.application.purchase.combinationPrice.PriceParam;
import com.daddylab.supplier.item.application.purchase.combinationPrice.PurchasePriceVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR> up
 * @date 2023年04月10日 1:37 PM
 */
@Slf4j
@Api(value = "采购组合价格相关API", tags = "采购组合价格相关API")
@RestController
@RequestMapping("/purchase/combinationPrice")
@RequiredArgsConstructor
@Deprecated
public class PurchaseCombinationPriceController {

    @Resource
    CombinationPriceBizServiceImpl combinationPriceBizService;

    @ResponseBody
    @ApiOperation(value = "组合价格分页")
    @PostMapping("/page")
    public PageResponse<PurchasePriceVo> skuPricePage(@Validated @RequestBody PricePageQuery query) {
        return combinationPriceBizService.skuPricePage(query);
    }

    @ResponseBody
    @ApiOperation(value = "组合价格更新")
    @PostMapping("/update")
    public SingleResponse<Boolean> updateSkuPrice(@Validated @RequestBody PriceParam param) {
        combinationPriceBizService.updateSkuPrice(param);
        return SingleResponse.of(true);
    }

    @ResponseBody
    @ApiOperation(value = "删除组合价格")
    @GetMapping("/delete")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "long", value = "价格数据id"),
            @ApiImplicitParam(name = "isSingle", dataType = "boolean", value = "价格类型。true：单sku纬度。false：spu纬度")
    })
    public SingleResponse<Boolean> deleteSkuPrice(@RequestParam Long id, @RequestParam Boolean isSingle) {
        combinationPriceBizService.deleteSkuPrice(id, isSingle);
        return SingleResponse.of(true);
    }

    @ResponseBody
    @ApiOperation(value = "从Excel导入组合价格")
    @GetMapping("/import")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", dataType = "file", value = "价格数据id"),
            @ApiImplicitParam(name = "priceType", dataType = "Integer", value = "价格类型。1：日常价。2：活动价"),
            @ApiImplicitParam(name = "isSingle", dataType = "boolean", value = "价格类型。true：单sku纬度。false：spu纬度")
    })
    public SingleResponse<Boolean> importExcel(@RequestParam("file") MultipartFile file,
                                               @RequestParam("priceType") Integer priceType,
                                               @RequestParam("isSingle") Boolean isSingle) {
        combinationPriceBizService.importExcel(file, priceType, isSingle);
        return SingleResponse.of(Boolean.TRUE);
    }


    @ResponseBody
    @ApiOperation(value = "手动添加组合价格")
    @PostMapping("/save")
    public SingleResponse<Boolean> savePrice(@Validated @RequestBody PriceParam param) {
        combinationPriceBizService.savePrice(param);
        return SingleResponse.of(true);
    }

    // ------------------------ 我是分割线 -----------------------------







}
