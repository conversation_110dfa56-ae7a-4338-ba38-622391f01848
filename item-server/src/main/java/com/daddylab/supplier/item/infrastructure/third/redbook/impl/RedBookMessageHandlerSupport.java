package com.daddylab.supplier.item.infrastructure.third.redbook.impl;

import cn.hutool.core.util.ReflectUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.third.annotations.RedBookHandlerMapping;
import com.daddylab.supplier.item.infrastructure.third.enums.RedBookMessageEnum;
import com.daddylab.supplier.item.infrastructure.third.redbook.IRedBookMessageHandlerService;
import com.daddylab.supplier.item.infrastructure.third.redbook.domain.dto.RedBookMessageDTO;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.method.HandlerMethod;

import java.lang.reflect.Method;
import java.util.*;

/**
 * <AUTHOR>
 * @class RedBookMessageHandlerServiceImpl.java
 * @description 消息调度类
 * @date 2024-03-01 15:42
 */
@Slf4j
@Component
public class RedBookMessageHandlerSupport implements InitializingBean, ApplicationContextAware {

    private final EnumMap<RedBookMessageEnum, HandlerMethod> enumsMethods = new EnumMap<>(RedBookMessageEnum.class);

    private ApplicationContext applicationContext;

    /**
     * 消息调度
     *
     * @param redBookService
     * @param redBookMessageDTO RedBookMessageDTO
     * @date 2024/3/1 15:59
     * <AUTHOR>
     */
    public void dispatcher(RedBookAbstract redBookService, RedBookMessageDTO redBookMessageDTO) {
        RedBookMessageEnum redBookMessageEnum = RedBookMessageEnum.ofValue(redBookMessageDTO.getMsgTag());
        HandlerMethod handlerMethod = null;
        if (Objects.nonNull(redBookMessageEnum)) {
            handlerMethod = enumsMethods.get(redBookMessageEnum);
        }
        if (Objects.isNull(handlerMethod)) {
            handlerMethod = enumsMethods.get(RedBookMessageEnum.ITEM_DEFAULT);
        }
        if (Objects.isNull(handlerMethod)) {
            return;
        }
        String dataJson = StringEscapeUtils.unescapeJava(redBookMessageDTO.getData());
        Object bean = handlerMethod.getBean();
        Method method = handlerMethod.getMethod();
        Object[] args = new Object[method.getParameterCount()];
        Class<?>[] argTypes = method.getParameterTypes();
        try {
            for (int i = 0; i < args.length; i++) {
                if (argTypes[i].isAssignableFrom(RedBookMessageEnum.class)) {
                    args[i] = redBookMessageEnum;
                } else if (argTypes[i].isAssignableFrom(RedBookMessageDTO.class)) {
                    args[i] = redBookMessageDTO;
                } else if (argTypes[i].isAssignableFrom(Map.class)) {
                    args[i] = JsonUtil.parse(dataJson, Map.class);
                } else if (argTypes[i].isAssignableFrom(ShopAuthorization.class)) {
                    args[i] = redBookService.getAuthorization();
                } else {
                    args[i] = JsonUtil.parse(dataJson, argTypes[i]);
                }
            }
            ReflectionUtils.invokeMethod(method, bean, args);
        } catch (Exception e) {
            log.error("【调用方法失败】errMsg={}, beanName={}, method={}, args={}", e.getMessage(), bean.getClass().getName(), method.getName(), args, e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, IRedBookMessageHandlerService> beansOfType = applicationContext.getBeansOfType(IRedBookMessageHandlerService.class);
        beansOfType.forEach((name, bean) -> {
            Method[] methods = ReflectUtil.getMethods(bean.getClass(), method -> Objects.nonNull(AnnotationUtils.findAnnotation(method, RedBookHandlerMapping.class)));
            for (Method method : methods) {
                RedBookHandlerMapping annotation = AnnotationUtils.findAnnotation(method, RedBookHandlerMapping.class);
                if (Objects.isNull(annotation)) {
                    continue;
                }
                enumsMethods.put(annotation.value(), new HandlerMethod(bean, method));
            }
        });
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
