package com.daddylab.supplier.item.infrastructure.winrobot360.types;

import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/9/21
 */
@Data
public class EditTbGoodsParam {

    /**
     * 上新计划时间（eg. 九月第一周）
     */
    String planTime;

    /**
     * 淘宝商品ID
     */
    String spId;

    /**
     * 淘宝商品名称
     */
    String spName;

    /**
     * 主图 （图片-(1,2,3,4,5) -> 图片URL）
     */
    Map<String, String> mainPic;

    /**
     * 详情图（图片标题 -> 图片URL）
     */
    Map<String, String> detailPic;
}
