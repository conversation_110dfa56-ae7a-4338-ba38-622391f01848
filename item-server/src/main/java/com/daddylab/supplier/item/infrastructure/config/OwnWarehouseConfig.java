package com.daddylab.supplier.item.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 自有仓库配置类
 * 用于配置哪些仓库属于自有仓库，这些仓库的商品默认无需转寄
 * 
 * <AUTHOR>
 * @since 2025-08-14
 */
@ConfigurationProperties(prefix = "own-warehouse")
@Component
@RefreshScope
@Data
public class OwnWarehouseConfig {

    /**
     * 自有仓库编码列表
     * 默认包含：CK001430, CK001094, CK001569
     * 对应仓库：上海磐石云仓WMS仓、绿色家装杭州顺丰仓、绿色家装杭州顺丰冷库仓
     */
    private List<String> warehouseCodes = Arrays.asList(
            "CK001430",  // 上海磐石云仓WMS仓
            "CK001094",  // 绿色家装杭州顺丰仓
            "CK001569"   // 绿色家装杭州顺丰冷库仓
    );

    /**
     * 判断指定仓库编码是否为自有仓库
     * 
     * @param warehouseCode 仓库编码
     * @return 是否为自有仓库
     */
    public boolean isOwnWarehouse(String warehouseCode) {
        if (warehouseCode == null || warehouseCode.trim().isEmpty()) {
            return false;
        }
        return warehouseCodes != null && warehouseCodes.contains(warehouseCode.trim());
    }
}
