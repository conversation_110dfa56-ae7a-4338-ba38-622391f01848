//package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;
//
//import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.VirtualWarehouse;
//
///**
// * <p>
// * 虚拟仓 服务类
// * </p>
// *
// * <AUTHOR>
// * @since 2024-02-29
// */
//public interface IVirtualWarehouseService extends IDaddyService<VirtualWarehouse> {
//
//    String getNewNo();
//
//}
