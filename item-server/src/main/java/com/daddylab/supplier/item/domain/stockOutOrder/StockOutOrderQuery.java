package com.daddylab.supplier.item.domain.stockOutOrder;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/25 9:42
 * @description StockOutOrderQuery
 */
@Data
@ApiModel("退料出库列表查询对象")
@EqualsAndHashCode(callSuper = true)
public class StockOutOrderQuery extends PageQuery {

    /**
     * 出库单号
     */
    @ApiModelProperty("出库单号")
    private String no;

    @ApiModelProperty("出库单号数组")
    private List<String> noList;

    /**
     * 出库仓库
     */
    @ApiModelProperty("出库仓库")
    private String warehouseNo;

    /**
     * 供应商
     */
    @ApiModelProperty("供应商")
    private Long providerId;

    /**
     * 退料类型(1:库存退料)
     */
    @ApiModelProperty("退料类型(1:库存退料)")
    private Integer returnType;

    /**
     * 退料方式(1.退料补料。2.退料并扣款)
     */
    @ApiModelProperty("退料方式(1.退料补料。2.退料并扣款)")
    private Integer returnMode;

    /**
     * 出库状态(1待提交、2待审核、3待出库、4已出库、5已取消、6审核拒绝、7撤回审核)
     */
    @ApiModelProperty("出库状态(1待提交、2待审核、3待出库、4已出库、5已取消、6审核拒绝、7撤回审核)")
    private Integer state;

    /**
     * 商品SKU
     */
    @ApiModelProperty("商品SKU")
    private String itemSkuCode;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String itemName;

    /**
     * 退料组织
     */
    @ApiModelProperty("退料组织")
    private Long organizationId;



    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间(开始时间)")
    private Long createdAtStart;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间(结束时间)")
    private Long createdAtEnd;

    /**
     * 采购单号
     */
    @ApiModelProperty("采购单号")
    private String purchaseOrderNo;

    /**
     * 明细模式 false:关  true:开
     */
    @ApiModelProperty("明细模式 false:关  true:开")
    private Boolean showDetail = false;

    private List<Long> idList;

    private int offsetVal;

    private List<Integer> businessLine;

//    private Boolean seeHedge;

    private List<Long> createdUidList;
    private List<Long> neCreatedUidList;

    @ApiModelProperty("出库开始时间")
    private Long outboundTimeStart;

    @ApiModelProperty("出库结束时间")
    private Long outboundTimeEnd;
}
