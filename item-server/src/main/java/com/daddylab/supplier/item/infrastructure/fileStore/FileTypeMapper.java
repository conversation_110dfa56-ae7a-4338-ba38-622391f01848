package com.daddylab.supplier.item.infrastructure.fileStore;

import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.ImageStub;
import com.daddylab.supplier.item.domain.fileStore.vo.VideoStub;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.File;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2022/11/11
 */
@Mapper
public interface FileTypeMapper {

    FileTypeMapper INST = Mappers.getMapper(FileTypeMapper.class);

    default FileStub fileStubFromFile(File file) {
        return FileStub.builder(file.getUrl())
                .size(file.getSize())
                .mime(file.getMime())
                .name(file.getFullName())
                .fileId(file.getId())
                .build();
    }

    default ImageStub imageStubFromFile(File file) {
        return ImageStub.builder(file.getUrl())
                .size(file.getSize())
                .width(file.getWidth())
                .height(file.getHeight())
                .mime(file.getMime())
                .name(file.getFullName())
                .fileId(file.getId())
                .build();
    }

    default VideoStub videoStubFromFile(File file) {
        return VideoStub.builder(file.getUrl())
                .size(file.getSize())
                .mime(file.getMime())
                .videoFirstFrame(file.getCoverUrl())
                .duration(file.getDuration())
                .name(file.getFullName())
                .fileId(file.getId())
                .build();
    }
}
