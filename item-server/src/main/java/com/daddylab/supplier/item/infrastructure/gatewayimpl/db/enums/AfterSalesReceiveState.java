package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/10/20
 */
@Getter
@AllArgsConstructor
public enum AfterSalesReceiveState implements IEnum<Integer> {

    /**
     * 1:待收货
     */
    WAIT(1, "待收货"),
    /**
     * 2:已收货
     */
    RECEIVED(2, "已收货"),
    /**
     * 3:已确认
     */
    CONFIRMED(3, "已确认"),
    ;
    @EnumValue
    private final Integer value;
    private final String desc;
}
