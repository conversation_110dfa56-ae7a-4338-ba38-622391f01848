package com.daddylab.supplier.item.application.item.event;


import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import lombok.Data;

@Data
public class ItemLaunchStatusChangeEvent {

    protected final Long operatorId;
    protected final Long itemId;
    protected final ItemLaunchStatus fromStatus;
    protected final ItemLaunchStatus toStatus;


    public static ItemLaunchStatusChangeEvent of(Long operatorId, Long itemId, ItemLaunchStatus fromStatus,
            ItemLaunchStatus toStatus) {
        return new ItemLaunchStatusChangeEvent(operatorId, itemId, fromStatus, toStatus);
    }
}
