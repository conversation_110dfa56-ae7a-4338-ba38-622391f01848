package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchModuleAdvice;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 商品库模块审批修改建议 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
public interface ItemLaunchModuleAdviceMapper extends DaddyBaseMapper<ItemLaunchModuleAdvice> {

    /**
     * 查询商品最新的模块审批意见
     *
     * @param itemIds 商品ID
     * @return 审批意见
     */
    List<ItemLaunchModuleAdvice> queryLatestAuditAdvicesBatch(Collection<Long> itemIds);
}
