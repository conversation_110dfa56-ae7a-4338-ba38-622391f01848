package com.daddylab.supplier.item.controller.open;

import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.NonNull;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @since 2021/12/31
 */
@Configuration
public class OpenWdtConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new HandlerInterceptor() {
            @Override
            public boolean preHandle(@NonNull HttpServletRequest request,
                    @NonNull HttpServletResponse response, @NonNull Object handler) {
                final String openAuth = request.getHeader("OPEN-AUTH");
                return StringUtil.isNotBlank(openAuth) && StringUtil
                        .equals(openAuth, "bqPNnZUdMmNAUUXbw9fX76f_w4ai!BN6");
            }
        }).order(Ordered.HIGHEST_PRECEDENCE).addPathPatterns("/open/wdt/**");
    }

}