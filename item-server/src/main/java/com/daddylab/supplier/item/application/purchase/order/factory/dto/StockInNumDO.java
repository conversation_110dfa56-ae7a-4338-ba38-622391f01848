package com.daddylab.supplier.item.application.purchase.order.factory.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2022年08月18日 2:47 PM
 */
@Data
public class StockInNumDO {

    private String skuCode;
    private BigDecimal num;
    private String warehouseNo;
    private Long stockInId;

    private ItemSku itemSku;
    private BigDecimal price0;


}
