package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DadStaff;

/**
 * <p>
 * 员工表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
@DS("authDb")
public interface IDadStaffService extends IDaddyService<DadStaff> {

}
