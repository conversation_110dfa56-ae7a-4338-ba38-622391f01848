package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemImage;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 商品图片 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface IItemImageService extends IDaddyService<ItemImage> {

    Map<Long, String> getItemMainImageToMap(List<Long> itemIds);

    ItemImage getItemMainImage(Long itemId);
}
