package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 系统库存同步配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SysInventorySetting implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 同步频次（单位秒，实时设置为0即可）
     */
    private Integer syncFrequency;

    /**
     * 是否允许店铺自行配置同步频次
     */
    private Integer permitShopSyncFreqSetting;

    /**
     * 修改占比后是否需要实时同步库存
     * @deprecated
     */
    private Integer syncAfterModifyRadio;

    /**
     * 旺店通仓内库存的同步频次（单位秒，实时设置为0即可）
     */
    private Integer wdtSyncFrequency;


}
