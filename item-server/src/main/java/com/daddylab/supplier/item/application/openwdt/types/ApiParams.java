package com.daddylab.supplier.item.application.openwdt.types;

import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/7/8
 */
@ApiModel("API查询参数包装")
@Data
public class ApiParams<T> {


    @ApiModelProperty("是否通过奇门调用")
    boolean qimen = false;

    @NotNull(message = "旺店通配置不能为空")
    @ApiModelProperty("旺店通配置")
    WdtConfig.Config config;

    @NotNull(message = "业务参数不能为空")
    @ApiModelProperty("业务参数")
    T params;

    @ApiModelProperty("分页大小，不需要分页的接口不传")
    @PositiveOrZero(message = "分页大小应为大于0的整数")
    private Integer pageSize = 10;

    @ApiModelProperty("第几页，不需要分页的接口不传，从0开始")
    @PositiveOrZero(message = "分页应为大于等于0的整数")
    private Integer pageNo = 0;

    @ApiModelProperty("是否返回总页数，不需要分页的接口不传，默认为真")
    private boolean calcTotal = true;

    public Pager getPager() {
        return new Pager(pageSize, pageNo, calcTotal);
    }

}
