package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemAttr;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemAttrMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemAttrService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品属性 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class ItemAttrServiceImpl extends DaddyServiceImpl<ItemAttrMapper, ItemAttr> implements IItemAttrService {

}
