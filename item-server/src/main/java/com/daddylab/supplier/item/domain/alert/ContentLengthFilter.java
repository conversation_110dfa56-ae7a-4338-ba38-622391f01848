package com.daddylab.supplier.item.domain.alert;

import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class ContentLengthFilter implements IAlerter.Filter {

    public static final int TEXT_MAX_BYTES = 2048;
    public static final int MARKDOWN_MAX_BYTES = 4096;
    public static final String ELLIPSIS = "...";

    @Override
    public List<IAlerter.Message> filter(IAlerter alert, List<IAlerter.Message> message) {
        final ArrayList<IAlerter.Message> filtered = new ArrayList<>();
        for (IAlerter.Message oneMessage : message) {
            if (oneMessage.getMsgType().equals(IAlerter.MsgType.TEXT)) {
                final String content = oneMessage.getContent();
                final byte[] bytes = content.getBytes(StandardCharsets.UTF_8);
                if (bytes.length > TEXT_MAX_BYTES) {
                    oneMessage = new IAlerter.Message(oneMessage.getMsgType()
                            , StringUtil.truncateByMaxBytes(content, TEXT_MAX_BYTES - ELLIPSIS.length()) + ELLIPSIS);
                }
            } else if (oneMessage.getMsgType().equals(IAlerter.MsgType.MARKDOWN)) {
                final String content = oneMessage.getContent();
                final byte[] bytes = content.getBytes(StandardCharsets.UTF_8);
                if (bytes.length > MARKDOWN_MAX_BYTES) {
                    oneMessage = new IAlerter.Message(oneMessage.getMsgType()
                            , StringUtil.truncateByMaxBytes(content, MARKDOWN_MAX_BYTES - ELLIPSIS.length()) + ELLIPSIS);
                }
            }
            filtered.add(oneMessage);
        }
        return filtered;
    }
}
