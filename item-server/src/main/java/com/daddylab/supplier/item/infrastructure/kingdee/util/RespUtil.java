package com.daddylab.supplier.item.infrastructure.kingdee.util;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockInOrderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockOutOrderService;
import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum.*;

/**
 * <AUTHOR> up
 * @date 2022/4/9 3:40 下午
 */
@Component
@Slf4j
public class RespUtil {
    @Autowired
    ShopGateway shopGateway;

    @Autowired
    ProviderGateway providerGateway;

    @Autowired
    ItemSkuGateway itemSkuGateway;

    @Autowired
    IStockInOrderService iStockInOrderService;

    @Autowired
    IStockOutOrderService iStockOutOrderService;

    @Autowired
    CategoryGateway categoryGateway;

    public static final Map<ApiEnum, String> METHOD_MAP = new HashMap<>(16);

    static {
        METHOD_MAP.put(GROUP_SAVE_CATEGORY, "groupSaveCategory");
        METHOD_MAP.put(GROUP_DELETE_CATEGORY, "groupDeleteCategory");

        METHOD_MAP.put(SAVE_SHOP, "saveShop");
        METHOD_MAP.put(SAVE_PROVIDER, "saveProvider");
        METHOD_MAP.put(SAVE_SKU, "saveSku");
        METHOD_MAP.put(SAVE_STOCK_IN_ORDER, "saveStockInOrder");
        METHOD_MAP.put(SAVE_STOCK_OUT_ORDER, "saveStockOutOrder");

        METHOD_MAP.put(UPDATE_SHOP, "");
        METHOD_MAP.put(UPDATE_PROVIDER, "");
        METHOD_MAP.put(UPDATE_SKU, "");
        METHOD_MAP.put(UPDATE_STOCK_IN_ORDER, "");
        METHOD_MAP.put(UPDATE_STOCK_OUT_ORDER, "");

        METHOD_MAP.put(DELETE_PROVIDER, "deleteProvider");
        METHOD_MAP.put(DELETE_STOCK_IN_ORDER, "deleteStockInOrder");
        METHOD_MAP.put(DELETE_STOCK_OUT_ORDER, "deleteStockOutOrder");
    }

    public static void invoke(ApiEnum apiEnum, Object... arg) {
        String method = METHOD_MAP.get(apiEnum);
        if (StringUtil.isNotBlank(method)) {
            ReflectUtil.invoke(SpringUtil.getBean(RespUtil.class), METHOD_MAP.get(apiEnum), arg);
        }
    }

    public void groupSaveCategory(Long id, String kingDeeId) {
        categoryGateway.setKingDeeId(id, kingDeeId);
    }

    public void groupDeleteCategory(Long id) {
        categoryGateway.removeById(id);
    }

    public void saveShop(Long id, String kingDeeId) {
        shopGateway.setKingDeeId(id, kingDeeId);
    }

    public void saveProvider(Long id, String kingDeeId) {
        providerGateway.setKingDeeIdAndNo(id, kingDeeId);
    }

    public void saveSku(Long id, String kingDeeId) {
        itemSkuGateway.setSkuKingDeeId(id, kingDeeId);
    }

    public void saveStockInOrder(Long id, String kingDeeId) {
        iStockInOrderService.setKingDeeId(id, kingDeeId);
    }

    public void saveStockOutOrder(Long id, String kingDeeId) {
        iStockOutOrderService.setKingDeeId(id, kingDeeId);
    }

    public void deleteProvider(Long id) {
    }

    public void deleteStockInOrder(Long id) {
        iStockInOrderService.removeKingDeeId(id);
    }

    public void deleteStockOutOrder(Long id) {
        iStockOutOrderService.removeKingDeeId(id);
    }
}
