package com.daddylab.supplier.item.application.offShelf.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/20
 */
@Data
public class OffShelfPageExportVO {
    @ExcelProperty("id")
    private Long id;

    /**
     * 下架流程编码
     */
    @ExcelProperty("流程编号")
    private String no;

    @ExcelProperty("下架状态")
    private String status;

    /**
     * 下架理由
     */
    @ExcelProperty("原因描述")
    private String reasonTxt;

    @ExcelProperty("下架原因")
    private String reasonType;

    @ExcelProperty("下架商品编码")
    private String itemCode;

    @ExcelProperty("紧急程度")
    private String urgentLevel;

    @ExcelProperty("申请人")
    private String applicant;

    @ExcelProperty("申请时间")
    private String applyTime;

    @ExcelProperty("下架运营")
    private String operatorUsers;
}
