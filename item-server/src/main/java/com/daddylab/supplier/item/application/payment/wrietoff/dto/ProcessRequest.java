package com.daddylab.supplier.item.application.payment.wrietoff.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderSettlementForm;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentOrderWriteOffLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrder;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年01月23日 11:49 AM
 */
@Data
@Builder
public class ProcessRequest {

    PaymentOrderWriteOffLog stepLog;

    OrderSettlementForm orderSettlementForm;

    List<PurchaseOrder> purchaseOrderList;

//    Set<OrderSettlementDetail> diffSettlementDetailSet;

    private Long auditDate;

    private Boolean mockSync;

    private Boolean manual;

    private List<String> syncStockInOrderNos;

    private List<String> syncStockOutOrderNos;

    private List<SkuUnitDto> diffSkuUnitDtoList;
}
