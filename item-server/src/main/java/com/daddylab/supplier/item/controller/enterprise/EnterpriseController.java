package com.daddylab.supplier.item.controller.enterprise;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.contract.ContractService;
import com.daddylab.supplier.item.application.enterprise.EnterpriseService;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.types.contract.ContractDropdownItem;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @since 2022/4/1
 */
@Api(value = "企微", tags = "企微")
@Slf4j
@RestController
@RequestMapping("/enterprise")
@Validated
public class EnterpriseController {

    @Autowired
    private EnterpriseService enterpriseService;

    @GetMapping("/getLaunchCode")
    @ApiOperation("获取单聊code")
    public SingleResponse<String> getLaunchCode(
            @ApiParam("聊天对象(qwUserId,eg:jinbiao.shen)")
            @NotEmpty(message = "聊天对象不能为空")
            @RequestParam("chatUserId") String chatUserId) {
        return enterpriseService.getLaunchCode(UserContext.getUserId(), chatUserId);
    }
}
