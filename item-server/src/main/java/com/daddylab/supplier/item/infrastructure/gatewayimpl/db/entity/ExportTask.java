package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ExportTask implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * excel文件名称
     */
    private String name;

    /**
     * 1.进行中。2.成功。-1.失败。
     */
    private ExportTaskStatus status;

    /**
     * 异常
     */
    private String error;

    /**
     * excel可下载地址
     */
    private String downloadUrl;

    private ExportTaskType type;

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    @TableLogic
    private Integer isDel;

    private BigDecimal progress;

    private Long pId;

    private String params;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long externalUserId;

    public static ExportTask initTask(String filePrefix, ExportTaskType type) {
        ExportTask exportTask = new ExportTask();
        exportTask.setType(type);
        exportTask.setName(filePrefix + DateUtil.getNowSecond());
        exportTask.setStatus(ExportTaskStatus.RUNNING);
        return exportTask;
    }

}
