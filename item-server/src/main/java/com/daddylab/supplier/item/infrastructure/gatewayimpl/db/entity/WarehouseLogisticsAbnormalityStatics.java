package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 仓库异常物流信息统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WarehouseLogisticsAbnormalityStatics implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Long isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 记录时间
     */
    private String recordDate;

    /**
     * 预警数值
     */
    private Integer alertNum;


}
