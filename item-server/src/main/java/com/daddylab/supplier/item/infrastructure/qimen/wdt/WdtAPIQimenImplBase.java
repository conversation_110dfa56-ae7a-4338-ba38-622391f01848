package com.daddylab.supplier.item.infrastructure.qimen.wdt;

import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.JsonUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.daddylab.supplier.item.infrastructure.qimen.QimenConfig;
import com.daddylab.supplier.item.infrastructure.qimen.QimenConstants;
import com.daddylab.supplier.item.infrastructure.qimen.QimenResponseBody;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.qimencloud.api.QimenCloudClient;
import com.taobao.api.ApiException;
import com.taobao.api.TaobaoResponse;
import java.lang.reflect.Type;
import org.apache.commons.lang3.reflect.TypeUtils;

/**
 * <AUTHOR>
 * @since 2022/1/17
 */
abstract public class WdtAPIQimenImplBase {

    public WdtAPIQimenImplBase(QimenCloudClient qimenClient,
            QimenConfig qimenConfig,
            WdtConfig wdtConfig, int configIndex) {
        initApiInstance(qimenClient, qimenConfig, wdtConfig, configIndex);
    }

    protected abstract void initApiInstance(QimenCloudClient qimenClient,
            QimenConfig qimenConfig,
            WdtConfig wdtConfig, int configIndex);

    protected void checkError(TaobaoResponse response) throws WdtErpException {
        if (QimenConstants.FLAG_FAILURE.equals(response.getFlag())) {
            throw new WdtErpException(0,
                    StringUtil.format("通过奇门请求旺店通业务异常 code:{} errCode:{} wdtErrCode:{} wdtErrMsg:{}",
                            response.getCode(), response.getErrorCode(), response.getSubCode(),
                            response.getSubMessage()));
        }
    }

    protected WdtErpException transformException(ApiException exception) {
        return new WdtErpException(
                StringUtil.format("通过奇门请求旺店通异常 errCode:{} errMsg:{} wdtErrCode:{} wdtErrMsg:{}",
                        exception.getErrCode(), exception.getErrMsg(), exception.getSubErrCode(),
                        exception.getSubErrMsg()), exception);
    }

    protected <T> T checkAndReturnData(
            TaobaoResponse response, Type type)
            throws WdtErpException {
        checkError(response);
        final QimenResponseBody<T> qimenResponseBody = JsonUtil
                .fromJson(response.getBody(),
                        TypeUtils.parameterize(QimenResponseBody.class, type));
        return qimenResponseBody.getResponse().getData();
    }
}
