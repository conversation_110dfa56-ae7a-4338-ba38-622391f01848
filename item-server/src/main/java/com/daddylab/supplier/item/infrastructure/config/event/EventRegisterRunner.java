package com.daddylab.supplier.item.infrastructure.config.event;

import com.google.common.eventbus.AsyncEventBus;
import com.google.common.eventbus.EventBus;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * // * @version 1.0
 * @date 2021/9/30 2:06 下午
 * @description
 */
@Component
@Slf4j
public class EventRegisterRunner implements ApplicationListener<ContextRefreshedEvent> {

    @Resource(name = "syncEventBus")
    private EventBus syncEventBus;

    @Resource(name = "asyncEventBus")
    private AsyncEventBus asyncEventBus;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent context) {
        Map<String, Object> beans = context.getApplicationContext().getBeansWithAnnotation(EventBusListener.class);
        for (Object bean : beans.values()) {
            asyncEventBus.register(bean);
            syncEventBus.register(bean);
        }
        if (beans.size() > 0) {
            log.info("event registration completed ({})", beans.size());
        }
    }


}
