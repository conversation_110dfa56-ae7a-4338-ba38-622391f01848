package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetItemSpu;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.HandingSheetItemSpuMapper;

import java.util.List;

/**
 * 盘货表关联的商品（V2.4.5新版盘货表） 服务类
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
public interface IHandingSheetItemSpuService
        extends IDaddylabServicePlus<HandingSheetItemSpu, HandingSheetItemSpuMapper> {
    default List<HandingSheetItemSpu> selectByHandingSheetId(Long handingSheetId) {
        return lambdaQuery().eq(HandingSheetItemSpu::getHandingSheetId, handingSheetId).list();
    }

    List<Long> selectInvalidSpuIds(Long handingSheetId);
}
