package com.daddylab.supplier.item.infrastructure.third.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @class RedBookConfig.java
 * @description 描述类的作用
 * @date 2024-02-28 10:57
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "third.redbook")
@RefreshScope
public class RedBookConfig {

    private String appId;

    private String appSecret;

    private String version;

    private String apiUrl;
    /**
     * 影刀任务ID
     */
    private String scheduleUuid;
    private String authorizationCallbackUrl;

}
