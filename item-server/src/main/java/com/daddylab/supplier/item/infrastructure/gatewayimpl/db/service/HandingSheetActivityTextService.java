package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.controller.handingsheet.dto.HandingSheetActivityTextPageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetActivityText;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 盘货表活动内容 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
public interface HandingSheetActivityTextService extends IDaddyService<HandingSheetActivityText> {


    IPage<HandingSheetActivityText> selectPageList(Page<HandingSheetActivityText> page,
                                                   QueryWrapper<HandingSheetActivityText> queryWrapper);

    long selectPageListCount(Wrapper<HandingSheetActivityText> queryWrapper);
}
