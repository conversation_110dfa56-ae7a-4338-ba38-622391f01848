package com.daddylab.supplier.item.application.purchase.order.factory.dto;

import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * 按数量进行组合的阶梯价格封装。
 *
 * <AUTHOR> up
 * @date 2022年08月16日 10:37 AM
 */
@Data
public class QuantityCombinedPriceBO {

    /**
     * 价格编码
     */
    private String code;

    /**
     * 数量。
     */
    private Integer quantity;

    /**
     * 价格
     */
    private BigDecimal price;

    public static List<QuantityCombinedPriceBO> addOrConvert(List<QuantityCombinedPriceBO> existList, QuantityCombinedPriceBO addOne) {
        // 因为是阶梯价格，所以主要依据是数量对比
        Optional<QuantityCombinedPriceBO> opt = existList.stream().filter(vv -> vv.getQuantity().equals(addOne.getQuantity())).findFirst();
        // 不存在，执行添加操作
        if (!opt.isPresent()) {
            existList.sort(Comparator.comparing(QuantityCombinedPriceBO::getCode).reversed());
            QuantityCombinedPriceBO maxCodeOne = existList.get(0);
            String maxCode = maxCodeOne.getCode();
            int i = maxCode.lastIndexOf("P");
            String skuCode = maxCode.substring(0, i);
            int maxI = Integer.parseInt(maxCode.substring(i).replaceAll("P", ""));
            maxI = maxI + 1;
            String newMaxCode = skuCode + "P" + maxI;
            addOne.setCode(newMaxCode);
            List<QuantityCombinedPriceBO> newList = new ArrayList<>(existList);
            newList.add(addOne);
            return newList;
        }

        List<QuantityCombinedPriceBO> wrapperList = new ArrayList<>(existList);
        // 执行覆盖，
        wrapperList.remove(opt.get());
        List<QuantityCombinedPriceBO> newList = new ArrayList<>(wrapperList);
        addOne.setCode(opt.get().getCode());
        newList.add(addOne);
        return newList;
    }

    public static void main(String[] args) {
        List<String> old = new ArrayList<>();
        old.add("1");
        old.add("2");
        old.add("3");

        old.remove("1");
        List<String> newList = new ArrayList<>(old);
        System.out.println(JsonUtil.toJson(newList));
        newList.add("99");
        System.out.println(JsonUtil.toJson(newList));

    }
}
