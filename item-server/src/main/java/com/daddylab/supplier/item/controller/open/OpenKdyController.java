package com.daddylab.supplier.item.controller.open;

import com.daddylab.supplier.item.application.kuaidaoyun.KdyBizService;
import com.daddylab.supplier.item.types.kuaidaoyun.KdyCallbackRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/6/14
 */
@Slf4j
@RestController
@RequestMapping("/open/kdy")
@Api(value = "快刀云开放接口", tags = "快刀云开放接口")
public class OpenKdyController {
    @Autowired
    KdyBizService kdyBizService;

    @ApiOperation("物流轨迹回调")
    @PostMapping(value = "/callback", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public String callback(@RequestParam("companyname") String companyname,
                           @RequestParam("outid") String outid,
                           @RequestParam("status") String status,
                           @RequestParam("tracklist") String tracklist,
                           @RequestParam("sign") String sign,
                           @RequestParam("state") String state) {
        KdyCallbackRequest request = new KdyCallbackRequest();
        request.setCompanyname(companyname);
        request.setOutid(outid);
        request.setStatus(status);
        request.setTracklist(tracklist);
        request.setSign(sign);
        request.setState(state);
        return kdyBizService.callback(request, true);
    }
}
