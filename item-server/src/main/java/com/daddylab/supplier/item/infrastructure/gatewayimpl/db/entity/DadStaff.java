package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 员工表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DadStaff implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * uid
     */
    private Long uid;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 花名
     */
    private String nickname;

    /**
     * 登录名
     */
    private String loginName;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 联系电话
     */
    private String mobile;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 职位
     */
    private String position;

    /**
     * 岗位id
     */
    private Long postId;

    /**
     * 入职日期
     */
    private Long hireDate;

    /**
     * 状态 1在职
     */
    private Integer status;

    /**
     * 来源
     */
    private String ref;

    /**
     * 工号
     */
    private Long employeeId;

    /**
     * 工号
     */
    private String code;

    /**
     * 是否外部人员
     */
    private Integer isExternal;

    /**
     * oa ID
     */
    private Long oaId;

    /**
     * 企微用户id
     */
    private String qwUserId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;


}
