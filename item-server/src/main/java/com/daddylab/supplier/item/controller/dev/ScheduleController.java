package com.daddylab.supplier.item.controller.dev;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.infrastructure.schedule.RedisKeys;
import com.daddylab.supplier.item.infrastructure.schedule.TaskInfo;
import com.daddylab.supplier.item.infrastructure.schedule.TaskStatus;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.ReflectionsUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RSemaphore;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.config.ScheduledTask;
import org.springframework.scheduling.config.ScheduledTaskHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @since 2021/12/31
 */

@Api(hidden = true)
@Slf4j
@RestController
@RequestMapping("/dev/schedule")
public class ScheduleController {
    private final ScheduledTaskHolder scheduledTaskHolder;
    final private ConcurrentMap<String, CompletableFuture<?>> executingFutures = new ConcurrentHashMap<>();
    RedissonClient redissonClient;

    @Autowired
    public ScheduleController(ScheduledTaskHolder scheduledTaskHolder,
                              RedissonClient redissonClient) {
        this.scheduledTaskHolder = scheduledTaskHolder;
        this.redissonClient = redissonClient;
    }

    private Set<String> getScheduledTaskList() {
        final Set<String> taskList = Sets.newHashSet();
        final Set<ScheduledTask> scheduledTasks = scheduledTaskHolder.getScheduledTasks();
        for (ScheduledTask scheduledTask : scheduledTasks) {
            taskList.add(scheduledTask.getTask().toString());
        }
        final Set<Method> methods = ReflectionsUtil.getReflections().getMethodsAnnotatedWith(Scheduled.class);
        for (Method method : methods) {
            taskList.add(method.getDeclaringClass().getName() + "." + method.getName());
        }
        return taskList;
    }

    @GetMapping(value = "/task/list")
    public SingleResponse<List<Task>> taskList() {
        final ArrayList<String> taskList = new ArrayList<>(getScheduledTaskList());
        final ArrayList<Task> taskInfoList = Lists.newArrayList();
        for (String task : taskList) {
            final RBucket<TaskInfo> taskInfoBucket = redissonClient.getBucket(RedisKeys.TASK_INFO + task);
            final RSemaphore semaphore = redissonClient.getSemaphore(RedisKeys.TASK_SEMAPHORE + task);
            taskInfoList.add(new Task(
                    task,
                    Optional.ofNullable(taskInfoBucket.get()).orElseGet(TaskInfo::new),
                    semaphore.availablePermits()
            ));
        }
        return SingleResponse.of(taskInfoList);
    }

    @PostMapping(value = "/task/do")
    public Response doTask(String task) throws NoSuchMethodException, ClassNotFoundException {
        if (StringUtil.isBlank(task)) {
            return Response.buildFailure("0", "请输入任务名称");
        }
        final Future<?> existFuture = executingFutures.get(task);
        if (existFuture != null && !existFuture.isCancelled() && !existFuture.isDone()) {
            return Response.buildFailure("9", "任务已经在执行中");
        }
        final int lastIndexOfDot = task.lastIndexOf(".");
        if (lastIndexOfDot < 0) {
            return Response.buildFailure("1", "任务名称不合法");
        }
        final String taskService = task.substring(0, lastIndexOfDot);
        final String methodName = task.substring(lastIndexOfDot + 1);
        final Class<?> clazz = Class.forName(taskService);
        final Object bean = ApplicationContextUtil.getBean(clazz);
        if (bean == null) {
            return Response.buildFailure("2", "任务未查找到");
        }
        Method method = bean.getClass().getDeclaredMethod(methodName);
        final ThreadPoolTaskExecutor threadPool = ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL);
        final Runnable runnable = () -> {
            try {
                method.invoke(bean);
            } catch (Throwable e) {
                log.error("执行调度任务异常，任务:{}", task, e);
            }
        };
        final CompletableFuture<Void> future = CompletableFuture.runAsync(runnable, threadPool).thenRun(() -> {
            executingFutures.remove(task);
        });
        executingFutures.put(task, future);
        return Response.buildSuccess();
    }

    @PostMapping(value = "/task/stop")
    public Response stop(String task) {
        final Future<?> future = executingFutures.get(task);
        if (Objects.isNull(future)) {
            return Response.buildFailure("1", "任务未在执行中");
        }
        future.cancel(true);
        executingFutures.remove(task);
        return Response.buildSuccess();
    }

    @PostMapping(value = "/task/status")
    public Response status(String task, TaskStatus taskStatus) {
        final RBucket<TaskInfo> taskInfoBucket = redissonClient.getBucket(RedisKeys.TASK_INFO + task);
        TaskInfo taskInfo = taskInfoBucket.get();
        if (taskInfo == null) {
            taskInfo = new TaskInfo();
        }
        taskInfo.setTaskStatus(taskStatus);
        taskInfoBucket.set(taskInfo);
        return Response.buildSuccess();
    }

    @GetMapping(value = "/task/semaphore")
    public SingleResponse<Integer> semaphore(String task) {
        final RSemaphore semaphore = redissonClient.getSemaphore(RedisKeys.TASK_SEMAPHORE + task);
        return SingleResponse.of(semaphore.availablePermits());
    }

    @PostMapping(value = "/task/semaphore/release")
    public Response releaseSemaphore(String task) {
        final RSemaphore semaphore = redissonClient.getSemaphore(RedisKeys.TASK_SEMAPHORE + task);
        semaphore.release();
        return Response.buildSuccess();
    }

    @PostMapping(value = "/task/semaphore/acquire")
    public Response acquireSemaphore(String task) {
        final RSemaphore semaphore = redissonClient.getSemaphore(RedisKeys.TASK_SEMAPHORE + task);
        semaphore.tryAcquire();
        return Response.buildSuccess();
    }

    @PostMapping(value = "/task/semaphore/trySetPermits")
    public Response trySetPermits(String task, Integer permit) {
        final RSemaphore semaphore = redissonClient.getSemaphore(RedisKeys.TASK_SEMAPHORE + task);
        semaphore.trySetPermits(permit);
        return Response.buildSuccess();
    }

    @PostMapping(value = "/task/semaphore/drainPermits")
    public Response drainPermits(String task) {
        final RSemaphore semaphore = redissonClient.getSemaphore(RedisKeys.TASK_SEMAPHORE + task);
        semaphore.drainPermits();
        return Response.buildSuccess();
    }

    @Data
    static class Task {
        final String task;
        final TaskInfo taskInfo;
        final Integer semaphore;
    }
}
