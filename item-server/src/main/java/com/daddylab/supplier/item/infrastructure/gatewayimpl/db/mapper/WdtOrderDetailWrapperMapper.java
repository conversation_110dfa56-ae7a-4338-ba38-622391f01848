package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.order.settlement.dto.SkuDeliverNumDo;
import com.daddylab.supplier.item.application.order.settlement.dto.SkuRefundStaticDo;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
public interface WdtOrderDetailWrapperMapper extends DaddyBaseMapper<WdtOrderDetailWrapper> {

    @Select("select warehouse_no from wdt_order_detail_wrapper \n" +
            "where price = #{price} and sku_code = #{skuCode}\n" +
            "and provider_id = #{providerId}\n" +
            "and operate_time = #{operateTime}\n" +
            "and type in (1,2)\n" +
            "and warehouse_no is not null limit 1\n")
    String getWarehouseNo(@Param("price") BigDecimal price, @Param("skuCode") String skuCode,
                          @Param("operateTime") String operateTime, @Param("providerId") Long providerId);


    /**
     * 统计各个sku在指定条件下的 退款数量汇总
     *
     * @param providerId
     * @param warehouseNo
     * @param operateTime
     * @param skuCodes
     * @return
     */
    List<SkuRefundStaticDo> skuRefundQuantityStatics(@Param("providerId") Long providerId,
                                                     @Param("warehouseNo") String warehouseNo,
                                                     @Param("operateTime") String operateTime,
                                                     @Param("skuCodes") Collection<String> skuCodes);


    List<WdtOrderDetailWrapper> selectListByParams(@Param("warehouseNos") Collection<String> warehouseNos,
                                                   @Param("skuCodes") Collection<String> skuCodes,
                                                   @Param("operateTimes") Collection<String> operateTimes,
                                                   @Param("startId") long startId, @Param("endId") long endId,
                                                   @Param("types") Collection<Integer> types);


    List<SkuDeliverNumDo> selectSkuDeliverNum(@Param("warehouseNo") String warehouseNo,
                                              @Param("operateTime") String operateTime,
                                              @Param("providerId") long providerId,
                                              @Param("type") Integer type);

    List<String> selectStaticSkuCode(@Param("providerId") Long providerId,
                                     @Param("operateTime") String operateTime);

    List<WdtOrderDetailWrapper> selectStaticInfo(@Param("providerId") Long providerId,
                                                 @Param("operateTime") String operateTime,
                                                 @Param("skuCode") String skuCode);

    List<WdtOrderDetailWrapper> selectTotalStaticInfo(@Param("providerId") Long providerId,
                                                      @Param("operateTime") String operateTime);


    /**
     * 物理删除目标月份的统计数据
     *
     * @param targetMonth
     */
    void deleteOldStatisticsData(@Param("targetMonth") String targetMonth);

    /*List<String> getStockInTradeNoList(@Param("targetMonth") String targetMonth,
                                       @Param("giftTypes") Collection<Integer> giftTypes);*/

    /**
     * 获取最早的下单时间
     *
     * @param operateTime
     * @return
     */
    Long getEarliestPayTime(@Param("operateTime") String operateTime);

    /**
     * 获取最晚的下单时间
     *
     * @param operateTime
     * @return
     */
    Long getLatestPayTime(@Param("operateTime") String operateTime);
}
