package com.daddylab.supplier.item.application.item;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.goods.SuiteAPI;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.SuiteSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.SuiteSearchResponse;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.SuiteSearchResponse.Suite;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.SuiteSearchResponse.Suite.Detail;
import com.daddylab.supplier.item.application.item.combinationItem.CombinationItemBizService;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.ComposeSkuCmd;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.item.data.ItemAndSkuCodeInfo;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.diff.CollChange;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CombinationItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ComposeSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.CombinationItemType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CombinationItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICombinationItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IComposeSkuService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/12/31
 */
@Component
@Slf4j
public class SuiteFetcher implements PageFetcher {

    @Autowired
    private ICombinationItemService combinationItemService;
    @Resource
    private CombinationItemMapper combinationItemMapper;
    @Autowired
    private IComposeSkuService composeSkuService;
    @Autowired
    private ItemSkuGateway itemSkuGateway;
    @Autowired
    private WdtGateway wdtGateway;
    @Autowired
    private OperateLogDomainService operateLogDomainService;

    @Autowired
    private CombinationItemBizService combinationItemBizService;
    @Autowired
    private IComposeSkuService iComposeSkuService;
    @Autowired
    private IItemSkuService iItemSkuService;

    @Override
    public int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
        return query(startTime, endTime, 1, 1, true).getTotalCount();
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, long pageIndex, long pageSize,
                      RunContext runContext) {
        final SuiteSearchResponse response = query(startTime, endTime, ((int) pageIndex),
                ((int) pageSize), false);
        handleResponse(response);
    }

    private void handleResponse(SuiteSearchResponse response) {
        if (CollectionUtil.isEmpty(response.getSuiteList())) {
            return;
        }
        for (Suite suite : response.getSuiteList()) {
            CombinationItem combinationItem = combinationItemMapper.queryByCodeIncludeDeleted(
                    suite.getSuiteNo());
            if (null != combinationItem && NumberUtil.isPositive(combinationItem.getIsDel())) {
                continue;
            }
            boolean isFirstCreate = combinationItem == null;
            if (isFirstCreate) {
                CombinationItem addOne = handleFirstCreate(suite);
                fixComposeSkuProportion(addOne);
            } else {
                handleUpdate(suite, combinationItem);
                fixComposeSkuProportion(combinationItem);
            }
        }
    }

    /**
     * 针对WDT拉回来的数据，针对销售金额占比，做一些修正，保证占比相加 == 1
     *
     * @param combinationItem
     */
    public void fixComposeSkuProportion(CombinationItem combinationItem) {
        if (Objects.nonNull(combinationItem)) {
            // 下面情况无需处理
            List<ComposeSku> composeSkus = iComposeSkuService.lambdaQuery()
                    .eq(ComposeSku::getCombinationId, combinationItem.getId()).list();

            List<Long> skuIds = composeSkus.stream().map(ComposeSku::getSkuId).collect(Collectors.toList());
            Map<Long, ItemSku> skuIdAndValMap = iItemSkuService.lambdaQuery().in(ItemSku::getId, skuIds).list()
                    .stream().collect(Collectors.toMap(ItemSku::getId, v -> v, (a, b) -> a));
            if (CollUtil.isEmpty(composeSkus)) {
                return;
            }
            BigDecimal saleProportionTotal = composeSkus.stream()
                    .map(val -> Objects.isNull(val.getSaleProportion()) ? BigDecimal.ZERO : val.getSaleProportion())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal costProportionTotal = composeSkus.stream()
                    .map(val -> Objects.isNull(val.getCostProportion()) ? BigDecimal.ZERO : val.getCostProportion())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (saleProportionTotal.compareTo(BigDecimal.ONE) == 0 && costProportionTotal.compareTo(BigDecimal.ONE) == 0) {
                return;
            }

            // ------------- 开始进行销售/成本金额占比修正处理 -------------

            if (costProportionTotal.compareTo(BigDecimal.ONE) != 0) {
                BigDecimal procurementPrice = composeSkus.stream().map(val -> {
                    ItemSku itemSku = skuIdAndValMap.get(val.getSkuId());
                    if (Objects.nonNull(itemSku)) {
                        return Objects.isNull(itemSku.getCostPrice()) ? BigDecimal.ZERO : itemSku.getCostPrice();
                    }
                    return BigDecimal.ZERO;
                }).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (BigDecimal.ZERO.compareTo(procurementPrice) != 0) {
                    List<BigDecimal> updatedList = new LinkedList<>();
                    for (int i = 0; i < composeSkus.size() - 1; i++) {
                        ComposeSku composeSku = composeSkus.get(i);
                        ItemSku itemSku = skuIdAndValMap.get(composeSku.getSkuId());
                        if (Objects.nonNull(itemSku)) {
                            BigDecimal costPrice = itemSku.getCostPrice();
                            BigDecimal thisSkuCostProportion = costPrice.divide(procurementPrice, 6, RoundingMode.DOWN);
                            composeSku.setCostProportion(thisSkuCostProportion);
                            updatedList.add(thisSkuCostProportion);
                            iComposeSkuService.updateById(composeSku);
                        }
                    }
                    ComposeSku lastComposeSku = composeSkus.get(composeSkus.size() - 1);
                    BigDecimal reduce = updatedList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal subtract = BigDecimal.ONE.subtract(reduce);
                    lastComposeSku.setCostProportion(subtract);
                    iComposeSkuService.updateById(lastComposeSku);
                    operateLogDomainService
                            .addOperatorLog(0L, OperateLogTarget.COMBINATION_ITEM, combinationItem.getId(),
                                    "系统自动同步旺店通组合装订正：成本金额占比重新计算");
                } else {
                    composeSkus.forEach(val-> val.setCostProportion(BigDecimal.ZERO));
                    iComposeSkuService.updateBatchById(composeSkus);
                    operateLogDomainService
                            .addOperatorLog(0L, OperateLogTarget.COMBINATION_ITEM, combinationItem.getId(),
                                    "系统自动同步旺店通组合装订正：单品成本累计为0，清手动修正单品的成本占比");
                }
            }

            // 如果组合品的SKU销售金额都是0，直接copy成本金额占比。
            BigDecimal salePriceTotal = skuIdAndValMap.values().stream().map(ItemSku::getSalePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (salePriceTotal.compareTo(BigDecimal.ZERO) == 0) {
                composeSkus.forEach(val -> val.setSaleProportion(val.getCostProportion()));
                iComposeSkuService.updateBatchById(composeSkus);
                operateLogDomainService
                        .addOperatorLog(0L, OperateLogTarget.COMBINATION_ITEM, combinationItem.getId(),
                                "系统自动同步旺店通组合装订正：成本占比覆盖销售金额占比");
                return;
            }

            List<ComposeSku> correctList = new LinkedList<>();
            List<ComposeSku> zeroList = new LinkedList<>();
            composeSkus.forEach(val -> {
                ItemSku itemSku = skuIdAndValMap.get(val.getSkuId());
                if (Objects.nonNull(itemSku)) {
                    if (BigDecimal.ZERO.compareTo(itemSku.getSalePrice()) != 0) {
                        correctList.add(val);
                    } else {
                        zeroList.add(val);
                    }
                } else {
                    zeroList.add(val);
                }
            });

            boolean addLog = false;
            if (CollUtil.isNotEmpty(correctList)) {
                BigDecimal saleTotal = correctList.stream().map(composeSku -> {
                    ItemSku itemSku = skuIdAndValMap.get(composeSku.getSkuId());
                    return itemSku.getSalePrice().multiply(new BigDecimal(composeSku.getCount()));
                }).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal eachResTotal = BigDecimal.ZERO;

                for (int i = 0; i < correctList.size() - 1; i++) {
                    ComposeSku composeSku = correctList.get(i);
                    ItemSku itemSku = skuIdAndValMap.get(composeSku.getSkuId());
                    BigDecimal u = itemSku.getSalePrice().multiply(new BigDecimal(composeSku.getCount()));
                    BigDecimal res = u.divide(saleTotal, 6, RoundingMode.DOWN);
                    eachResTotal = eachResTotal.add(res);
                    composeSku.setSaleProportion(res);
                }
                ComposeSku lastComposeSku = correctList.get(correctList.size() - 1);
                lastComposeSku.setSaleProportion(BigDecimal.ONE.subtract(eachResTotal));

                iComposeSkuService.updateBatchById(correctList);
                addLog = true;
            }
            if (CollUtil.isNotEmpty(zeroList)) {
                zeroList.forEach(val -> val.setSaleProportion(BigDecimal.ZERO));
                iComposeSkuService.updateBatchById(zeroList);
                addLog = true;
            }

            if (addLog) {
                operateLogDomainService
                        .addOperatorLog(0L, OperateLogTarget.COMBINATION_ITEM, combinationItem.getId(),
                                "系统自动同步旺店通组合装订正：销售金额占比重新计算");
            }
        }
    }

    private void handleUpdate(Suite suite, CombinationItem combinationItem) {
        final ArrayList<String> changes = new ArrayList<>();
        boolean combinationItemChange = false;
        if (StringUtil.isNotBlank(suite.getBarcode()) && !StringUtil
                .equals(combinationItem.getBarCode(), suite.getBarcode())) {
            combinationItemChange = true;
            combinationItem.setBarCode(suite.getBarcode());
            changes.add("条码");
        }
        if (StringUtil.isNotBlank(suite.getSuiteName()) && !StringUtil
                .equals(combinationItem.getName(), suite.getSuiteName())) {
            combinationItemChange = true;
            combinationItem.setName(suite.getSuiteName());
            changes.add("名称");
        }
        if (combinationItemChange) {
            combinationItemService.updateById(combinationItem);
        }

        final List<Detail> detailList =
                suite.getDetailList() != null ? suite.getDetailList() : Collections
                        .emptyList();

        //查询本地组合装明细
        final List<ComposeSku> localComposeSkus = composeSkuService.lambdaQuery()
                .eq(ComposeSku::getCombinationId, combinationItem.getId()).list();

        //提取商品明细的skuCode
        final List<String> localSkuCodes = localComposeSkus.stream().map(ComposeSku::getSkuCode)
                .collect(Collectors.toList());

        //提取旺店通查询到的明细商家编码
        final Set<String> remoteSpecNos = detailList.stream().map(Detail::getSpecNo)
                .collect(Collectors.toSet());

        //查询 '供货指定编码' 和 'SKU编码' 匹配的后端商品编码信息
        final Map<String, ItemAndSkuCodeInfo> itemAndSkuCodeInfoBatch = itemSkuGateway
                .getItemAndSkuCodeInfoBatch(CollectionUtil.unionAll(localSkuCodes, remoteSpecNos));

        //itemAndSkuCodeInfoBatch是根据供货编码来索引的，转为根据skuCode来索引
        final Map<String, ItemAndSkuCodeInfo> itemAndSkuCodeInfoBatchIndexBySkuCode = itemAndSkuCodeInfoBatch.values()
                .stream().collect(
                        Collectors.toMap(ItemAndSkuCodeInfo::getSkuCode, Function.identity()));

        //旺店通那边的编码可能是供货指定编码也可能是skuCode，需要统一转为skuCode
        final List<String> remoteSkuCodes = remoteSpecNos.stream()
                .map(specNo -> Optional.ofNullable(itemAndSkuCodeInfoBatch.get(specNo))
                        .map(ItemAndSkuCodeInfo::getSkuCode).orElse(specNo)).collect(Collectors.toList());

        //比对明细是否变更
        Set<String> s1 = new HashSet<>(localSkuCodes);
        Set<String> s2 = new HashSet<>(remoteSkuCodes);
        final Optional<CollChange<String>> specsChangeOpt = DiffUtil.diffSet(s1, s2, String.class);

        specsChangeOpt.ifPresent(specsChange -> {
            final List<String> addedSkuCodes = specsChange.getAddedValues();
            List<ComposeSku> addedComposeSkus = new ArrayList<>();
            if (!addedSkuCodes.isEmpty()) {
                final List<String> addedSpecNos = addedSkuCodes.stream()
                        .map(skuCode -> Optional.ofNullable(itemAndSkuCodeInfoBatchIndexBySkuCode.get(skuCode))
                                .map(ItemAndSkuCodeInfo::getSkuSupplierCode).orElse(skuCode)).collect(
                                Collectors.toList());
                changes.add("新增单品：" + Joiner.on(',').join(addedSpecNos));
                for (String addedSkuCode : addedSkuCodes) {
                    final Optional<ItemAndSkuCodeInfo> itemAndSkuCodeInfo = Optional.ofNullable(
                            itemAndSkuCodeInfoBatchIndexBySkuCode.get(
                                    addedSkuCode));
                    final String addedSpecNo = itemAndSkuCodeInfo.map(ItemAndSkuCodeInfo::getSkuSupplierCode)
                            .orElse(addedSkuCode);
                    final Detail detail = detailList.stream()
                            .filter(it -> Objects.equals(it.getSpecNo(), addedSpecNo))
                            .findFirst().orElseThrow(NullPointerException::new);
                    final ComposeSku composeSku = new ComposeSku();
                    composeSku.setCombinationId(combinationItem.getId());
                    composeSku.setSkuId(
                            itemAndSkuCodeInfo.map(ItemAndSkuCodeInfo::getSkuId).orElse(0L));
                    composeSku.setSkuCode(addedSkuCode);
                    composeSku.setItemId(
                            itemAndSkuCodeInfo.map(ItemAndSkuCodeInfo::getItemId).orElse(0L));
                    composeSku
                            .setItemCode(itemAndSkuCodeInfo.map(ItemAndSkuCodeInfo::getItemCode)
                                    .orElse(detail.getGoodsNo()));
                    composeSku.setCount(detail.getNum().intValue());
                    composeSku.setSaleProportion(detail.getRatio());
                    addedComposeSkus.add(composeSku);
                }
                composeSkuService.saveBatch(addedComposeSkus);
            }
            List<ComposeSku> removedComposeSkus = new ArrayList<>();
            final List<String> removedSkuCodes = specsChange.getRemovedValues();
            if (!removedSkuCodes.isEmpty()) {
                final List<String> removedSpecNos = removedSkuCodes.stream()
                        .map(skuCode -> Optional.ofNullable(itemAndSkuCodeInfoBatchIndexBySkuCode.get(skuCode))
                                .map(ItemAndSkuCodeInfo::getSkuSupplierCode).orElse(skuCode)).collect(
                                Collectors.toList());
                changes.add("移除单品：" + Joiner.on(',').join(removedSpecNos));
                for (Object removedSkuCode : removedSkuCodes) {
                    removedComposeSkus.add(
                            localComposeSkus.stream().filter(
                                    it -> it.getSkuCode().equals(removedSkuCode)
                            ).findAny().orElseThrow(NullPointerException::new)
                    );
                }
                composeSkuService
                        .removeByIds(removedComposeSkus.stream().map(ComposeSku::getId).collect(
                                Collectors.toList()));
            }
            if (!addedSkuCodes.isEmpty() || !removedSkuCodes.isEmpty()) {
                final ArrayList<ComposeSku> composeSkus = new ArrayList<>(localComposeSkus);
                composeSkus.removeAll(removedComposeSkus);
                composeSkus.addAll(addedComposeSkus);
                updateUniqueCode(combinationItem, composeSkus);
            }
        });
        final Collection<String> intersectionSkuCodes = CollectionUtil
                .intersection(localSkuCodes, remoteSkuCodes);
        if (CollectionUtil.isNotEmpty(intersectionSkuCodes)) {
            List<ComposeSku> updateComposeSkus = new ArrayList<>();
            for (String intersectionSkuCode : intersectionSkuCodes) {
                final ComposeSku composeSku = localComposeSkus.stream().filter(
                        it -> it.getSkuCode().equals(intersectionSkuCode)
                ).findAny().orElseThrow(NullPointerException::new);
                final Optional<ItemAndSkuCodeInfo> itemAndSkuCodeInfo = Optional.ofNullable(
                        itemAndSkuCodeInfoBatchIndexBySkuCode.get(intersectionSkuCode));
                final String intersectionSpecNo = itemAndSkuCodeInfo.map(ItemAndSkuCodeInfo::getSkuSupplierCode)
                        .orElse(intersectionSkuCode);
                final Detail detail = detailList.stream()
                        .filter(it -> it.getSpecNo().equals(intersectionSpecNo)).findAny()
                        .orElseThrow(NullPointerException::new);
                if (NumberUtil.isZeroOrNull(composeSku.getSkuId())) {
                    composeSku.setSkuId(
                            itemAndSkuCodeInfo.map(ItemAndSkuCodeInfo::getSkuId).orElse(0L));
                    composeSku.setSkuCode(itemAndSkuCodeInfo.map(ItemAndSkuCodeInfo::getSkuCode)
                            .orElse(detail.getSpecNo()));
                    composeSku.setItemId(
                            itemAndSkuCodeInfo.map(ItemAndSkuCodeInfo::getItemId).orElse(0L));
                    composeSku
                            .setItemCode(itemAndSkuCodeInfo.map(ItemAndSkuCodeInfo::getItemCode)
                                    .orElse(detail.getGoodsNo()));
                }
                if (detail.getNum() != null && !Objects
                        .equals(composeSku.getCount(), detail.getNum().intValue())) {
                    changes.add(StringUtil.format("单品（{}）数量修改 {} -> {}", intersectionSpecNo,
                            composeSku.getCount(), detail.getNum()));
                    composeSku.setCount(detail.getNum().intValue());
                }
                if (detail.getRatio() != null && !NumberUtil
                        .equals(composeSku.getSaleProportion(), detail.getRatio())) {
                    changes.add(StringUtil.format("单品（{}）销售金额占比修改 {} -> {}", intersectionSpecNo,
                            composeSku.getSaleProportion(), detail.getRatio()));
                    composeSku.setSaleProportion(detail.getRatio());
                }

                updateComposeSkus.add(composeSku);
            }
            if (CollectionUtil.isNotEmpty(updateComposeSkus)) {
                composeSkuService.updateBatchById(updateComposeSkus);
            }
        }
        if (CollectionUtil.isNotEmpty(changes)) {
            operateLogDomainService
                    .addOperatorLog(0L, OperateLogTarget.COMBINATION_ITEM, combinationItem.getId(),
                            "系统自动同步旺店通组合装修改：" + Joiner.on('、').join(changes));
        }
        final boolean wdtIsDeleted = NumberUtil.isPositive(suite.getDeleted());
        if (wdtIsDeleted) {
            handleDeleted(combinationItem);
        }
    }

    private void handleDeleted(CombinationItem combinationItem) {
        final Long combinationItemId = combinationItem.getId();
        combinationItemService.removeById(combinationItemId);
        composeSkuService.lambdaUpdate()
                .eq(ComposeSku::getCombinationId, combinationItemId).remove();
        operateLogDomainService.addOperatorLog(0L, OperateLogTarget.COMBINATION_ITEM,
                combinationItemId, "系统自动同步旺店通删除组合装");
    }

    private CombinationItem handleFirstCreate(Suite suite) {
        CombinationItem combinationItem;
        combinationItem = new CombinationItem();
        combinationItem.setName(suite.getSuiteName());
        combinationItem.setCode(suite.getSuiteNo());
        combinationItem.setBarCode(suite.getBarcode());
        combinationItem.setProcurementPrice(BigDecimal.ZERO);
        combinationItem.setSalesPrice(BigDecimal.ZERO);
        combinationItem.setWeight(BigDecimal.ZERO);
        combinationItem.setType(CombinationItemType.COMMON);
        combinationItem.setUniqueCode("");
        combinationItem.setIsCustomProcurement(0);
        combinationItem.setIsCustomSales(0);

        final List<Detail> detailList = suite.getDetailList();
        if (CollectionUtil.isEmpty(detailList)) {
            combinationItemService.save(combinationItem);
            return null;
        }
        final Set<String> specNos = detailList.stream().map(Detail::getSpecNo)
                .collect(Collectors.toSet());
        final Map<String, ItemAndSkuCodeInfo> itemAndSkuCodeInfoBatch = itemSkuGateway
                .getItemAndSkuCodeInfoBatch(specNos);
        List<ComposeSku> composeSkus = new ArrayList<>();
        for (Detail detail : detailList) {
            final ComposeSku composeSku = new ComposeSku();
            final Optional<ItemAndSkuCodeInfo> itemAndSkuCodeInfo = Optional
                    .ofNullable(itemAndSkuCodeInfoBatch
                            .get(detail.getSpecNo()));
            composeSku.setSkuId(
                    itemAndSkuCodeInfo.map(ItemAndSkuCodeInfo::getSkuId).orElse(0L));
            composeSku.setSkuCode(itemAndSkuCodeInfo.map(ItemAndSkuCodeInfo::getSkuCode)
                    .orElse(detail.getGoodsNo()));
            composeSku.setItemId(
                    itemAndSkuCodeInfo.map(ItemAndSkuCodeInfo::getItemId).orElse(0L));
            composeSku.setItemCode(itemAndSkuCodeInfo.map(ItemAndSkuCodeInfo::getItemCode)
                    .orElse(detail.getSpecNo()));
            composeSku.setCount(detail.getNum().intValue());

            composeSkus.add(composeSku);
        }
        final String uniqueCode = getUniqueCode(composeSkus);
        combinationItem.setUniqueCode(uniqueCode);
        combinationItemService.save(combinationItem);
        composeSkus.forEach(composeSku -> composeSku.setCombinationId(combinationItem.getId()));
        composeSkuService.saveBatch(composeSkus);

        operateLogDomainService
                .addOperatorLog(0L, OperateLogTarget.COMBINATION_ITEM, combinationItem.getId(),
                        "系统自动同步旺店通组合装");

        return combinationItem;
    }

    private void updateUniqueCode(CombinationItem combinationItem, List<ComposeSku> composeSkus) {
        final String uniqueCode = getUniqueCode(composeSkus);
        if (StringUtil.isBlank(combinationItem.getUniqueCode()) || !StringUtil
                .equals(uniqueCode, combinationItem.getUniqueCode())) {
            combinationItem.setUniqueCode(uniqueCode);
            combinationItemService.lambdaUpdate()
                    .set(CombinationItem::getUniqueCode, combinationItem.getUniqueCode())
                    .eq(CombinationItem::getId, combinationItem.getId()).update();
        }
    }

    private String getUniqueCode(List<ComposeSku> composeSkus) {
        return DigestUtil.md5Hex(JsonUtil.toJson(
                composeSkus.stream().map(v -> {
                            final ComposeSkuCmd composeSkuCmd = new ComposeSkuCmd();
                            composeSkuCmd.setSkuId(v.getSkuId());
                            composeSkuCmd.setSkuCode(v.getSkuCode());
                            composeSkuCmd.setCount(v.getCount());
                            return composeSkuCmd;
                        }).sorted(Comparator.comparing(ComposeSkuCmd::getSkuId))
                        .collect(Collectors.toList())
        ));
    }

    public SuiteSearchResponse query(LocalDateTime startTime, LocalDateTime endTime,
                                     int pageIndex, int pageSize, boolean calcTotal) {
        try {
            final SuiteAPI api = wdtGateway.getAPI(SuiteAPI.class);
            Pager pager = new Pager(pageSize, pageIndex - 1, calcTotal);
            final SuiteSearchParams params = new SuiteSearchParams();
            if (startTime != null) {
                params.setStartTime(DateUtil.format(startTime));
            }
            if (endTime != null) {
                params.setEndTime(DateUtil.format(endTime));
            }
            return api.search(params, pager);
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapFetchException(e, fetchDataType());
        }
    }

    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.WDT_SUITE;
    }

}
