package com.daddylab.supplier.item.infrastructure.utils;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;

import java.util.Arrays;
import java.util.Collection;
import java.util.StringJoiner;

public class ApplicationContextUtil {

    public static <T> T getBean(String name) {
        return SpringUtil.getBean(name);
    }

    public static <T> T getBean(Class<T> clazz) {
        return SpringUtil.getBean(clazz);
    }

    public static <T> T getBean(String name, Class<T> clazz) {
        return SpringUtil.getBean(name, clazz);
    }

    public static <T> T getBean(TypeReference<T> reference) {
        return SpringUtil.getBean(reference);
    }

    public static void autowire(Object existingBean) {
        SpringUtil.getConfigurableBeanFactory().autowireBean(existingBean);
    }

    public static String[] getActiveProfiles() {
        return SpringUtil.getActiveProfiles();
    }

    public static String getActiveProfile() {
        return SpringUtil.getActiveProfile();
    }

    public static boolean isActiveProfile(String profile) {
        final String[] activeProfiles = SpringUtil.getActiveProfiles();
        if (activeProfiles == null) {
            if (profile == null) {
                return true;
            }
            return false;
        }
        return Arrays.asList(activeProfiles).contains(profile);
    }

    public static boolean isActiveProfile(Collection<String> profile) {
        return profile.stream().anyMatch(ApplicationContextUtil::isActiveProfile);
    }

    public static boolean isActiveProfile(String... profile) {
        return Arrays.stream(profile).anyMatch(ApplicationContextUtil::isActiveProfile);
    }

    public static boolean isLocalDebug() {
        return isActiveProfile("local")
                || (isActiveProfile("dev", "test") && StringUtil.equalsIgnoreCase(System.getenv("IS_LOCAL_DEBUG"), "true"));
    }

    public static String getProperty(String key) {
        return SpringUtil.getProperty(key);
    }

    public static String getProperty(String key, String defaultValue) {
        final String property = SpringUtil.getProperty(key);
        return property != null ? property : defaultValue;
    }

    public static String getAppName() {
        return getProperty("spring.application.name");
    }

    /**
     * 获取数据隔离前缀（某些环境可能基础组件是共用了，为了安全其间增加前缀进行隔离）
     */
    public static String getDataIsolationPrefix() {
        final StringJoiner stringJoiner = new StringJoiner(":");
        stringJoiner.add(getAppName());

        String profile = getActiveProfile();
        if (!StrUtil.equalsAnyIgnoreCase(profile, "prod", "gray")) {
            stringJoiner.add(profile);
        }
        return stringJoiner + ":";
    }

    public static void publishEvent(Object event) {
        SpringUtil.publishEvent(event);
    }
}
