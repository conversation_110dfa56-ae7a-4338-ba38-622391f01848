package com.daddylab.supplier.item.domain.exportTask;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;

import java.util.List;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @since 2024/3/4
 */
public interface ExportManager {

    ExportTask export(ExportTaskType type, Class<?> clazz, ExportHandler exportHandler);

    ExportTask export(ExportTaskType type, Class<?> clazz, ExportHandler exportHandler, BiConsumer<ExportTask, ExcelWriterBuilder> customizer);

    ExportTask export(ExportTaskType type, List<String> head, ExportHandler exportHandler);

    ExportTask export(ExportTaskType type, ExportHandler exportHandler, BiConsumer<ExportTask, ExcelWriterBuilder> customizer);

    ExportTask exportMultiRowHead(ExportTaskType type, List<List<String>> head, ExportHandler exportHandler);


    interface ExportHandler {
        PageResponse<?> handle(int pageIndex);
    }
}
