package com.daddylab.supplier.item.controller.purchase.dto.order;

//import com.daddylab.supplier.item.application.aws.enums.AwsActionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR> up
 * @date 2022/4/21 6:25 下午
 */
@Data
@ApiModel("采购单审核请求参数")
public class PurchaseOrderAuditCmd implements Serializable {

    private static final long serialVersionUID = 6974537488402296663L;

    @ApiModelProperty("采购订单id")
    @NotNull
    private Long id;

//    @ApiModelProperty("审核动作。SUBMIT:提交。WITHDRAW:作废。AGREE:同意。DISAGREE:不同意。ACTIVATION:激活")
//    @NotNull
//    private AwsActionEnum awsActionEnum;

    private String msg;

}
