package com.daddylab.supplier.item.infrastructure.fileStore;

import static cn.hutool.core.util.URLUtil.decode;
import static cn.hutool.core.util.URLUtil.getDecodedPath;

import static com.daddylab.supplier.item.infrastructure.utils.MPUtil.limit;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.URLUtil;

import com.daddylab.supplier.item.domain.fileStore.constant.FileType;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.ImageStub;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.fileStore.vo.VideoStub;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.File;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IFileService;
import com.daddylab.supplier.item.infrastructure.upyun.exception.FileUploadException;
import com.daddylab.supplier.item.infrastructure.upyun.gateway.UpyunGateway;
import com.daddylab.supplier.item.infrastructure.upyun.types.UploadFileResult;
import com.daddylab.supplier.item.infrastructure.upyun.types.UploadImageResult;
import com.daddylab.supplier.item.infrastructure.upyun.types.UploadVideoResult;
import com.github.rholder.retry.Attempt;
import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.RetryListener;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Deprecated
public class FileGatewayImpl  implements FileGateway{

    @Autowired
    private UpyunGateway upyunGateway;

    @Autowired
    private IFileService fileService;

    @Autowired
    private UploadConfig uploadConfig;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public ImageStub uploadImage(UploadFileAction action) {
        try {
            return (ImageStub) RetryerBuilder.<FileStub>newBuilder()
                    .retryIfExceptionOfType(FileUploadException.class)
                    .withWaitStrategy(WaitStrategies.fixedWait(2, TimeUnit.SECONDS))
                    .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                    .withRetryListener(new RetryListener() {
                        @Override
                        public <V> void onRetry(Attempt<V> attempt) {
                            if (attempt.hasException()) {
                                log.error("上传文件｜图片上传失败，文件:{}，第{}次重试，延迟{}", action.getFileName(), attempt.getAttemptNumber(),
                                        attempt.getDelaySinceFirstAttempt(), attempt.getExceptionCause());
                            }
                        }
                    })
                    .build().wrap(() -> {
                        final String md5 = action.getMd5();
                        InputStream inputStream = action.getInputStream();
                        File file;
                        if (!uploadConfig.isDisableCache()) {
                            file = fileService.lambdaQuery()
                                    .eq(File::getPath, action.getDestPath())
                                    .or().eq(File::getMd5, md5).orderByDesc(File::getId).last(limit(1)).one();
                            if (file != null
                                    && file.getMd5().equals(action.getMd5())
                                    && Objects.equals(file.getFullName(), action.getFileName())) {
                                log.debug("上传文件｜检查到相同图片已在库中 md5:{} dest:{} file:{}",
                                        action.getMd5(),
                                        action.getDestPath(),
                                        file);
                                return FileTypeMapper.INST.imageStubFromFile(file);
                            } else {
                                file = new File();
                            }
                        } else {
                            file = new File();
                        }
                        final UploadImageResult uploadImageResult = upyunGateway.uploadImage(action.getDestPath(),
                                inputStream, null);
                        saveImageFile(file, action, uploadImageResult);
                        return ImageStub.builder(uploadImageResult.getUrl())
                                .size(uploadImageResult.getSize())
                                .width(uploadImageResult.getWidth())
                                .height(uploadImageResult.getHeight())
                                .mime(uploadImageResult.getContentType())
                                .name(file.getFullName())
                                .build();
                    }).call();
        } catch (ExecutionException | RetryException e) {
            log.error("上传文件｜多次重试未成功:{}", e.getMessage(), e);
            throw new FileUploadException("文件上传失败，重试未成功");
        }
    }


    private void saveImageFile(File file, UploadFileAction action,
            UploadImageResult uploadImageResult) {
        try {
            file.setType(FileType.IMAGE);
            file.setPath(decode(uploadImageResult.getPath()));
            final String fileName = decode(action.getFileName());
            final String fileNamePrefix = FileNameUtil.getPrefix(fileName);
            final String fileNameSuffix = FileNameUtil.getSuffix(fileName);
            file.setName(fileNamePrefix);
            file.setExt(fileNameSuffix);
            file.setMime(uploadImageResult.getContentType());
            file.setUrl(uploadImageResult.getUrl());
            file.setMd5(action.getMd5());
            file.setSize(uploadImageResult.getSize());
            file.setWidth(uploadImageResult.getWidth());
            file.setHeight(uploadImageResult.getHeight());
            file.setUpyun(true);
            fileService.saveOrUpdate(file);
        } catch (DuplicateKeyException ignored) {
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public FileStub uploadFile(UploadFileAction action) {
        final String md5 = action.getMd5();
        File file;
        if (!uploadConfig.isDisableCache()) {
            file = fileService.lambdaQuery()
                    .eq(File::getPath, action.getDestPath())
                    .or().eq(File::getMd5, md5).orderByDesc(File::getId).last(limit(1)).one();
            if (file != null
                    && file.getMd5().equals(action.getMd5())
                    && Objects.equals(file.getFullName(), action.getFileName())) {
                log.debug("上传文件｜检查到相同文件已在库中 md5:{} dest:{} file:{}",
                        action.getMd5(),
                        action.getDestPath(),
                        file);
                return FileTypeMapper.INST.fileStubFromFile(file);
            } else {
                file = new File();
            }
        } else {
            file = new File();
        }
        final UploadFileResult uploadFileResult = upyunGateway.uploadFile(action.getInputStream(),
                action.getDestPath(), action.getMd5());
        saveFile(file, action, uploadFileResult);
        return FileStub.builder(uploadFileResult.getUrl())
                .size(uploadFileResult.getSize())
                .name(file.getFullName())
                .build();
    }

    private void saveFile(File file, UploadFileAction action,
            UploadFileResult uploadImageResult) {
        try {
            file.setType(FileType.UNKNOWN);
            file.setPath(decode(uploadImageResult.getPath()));
            final String fileName = decode(action.getFileName());
            final String fileNamePrefix = FileNameUtil.getPrefix(fileName);
            final String fileNameSuffix = FileNameUtil.getSuffix(fileName);
            file.setMime(uploadImageResult.getContentType());
            file.setName(fileNamePrefix);
            file.setExt(fileNameSuffix);
            file.setUrl(uploadImageResult.getUrl());
            file.setMd5(action.getMd5());
            file.setSize(uploadImageResult.getSize());
            file.setUpyun(true);
            fileService.saveOrUpdate(file);
        } catch (DuplicateKeyException ignored) {
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public VideoStub uploadVideo(UploadFileAction action) {
        final String md5 = action.getMd5();
        File file;
        if (!uploadConfig.isDisableCache()) {
            file = fileService.lambdaQuery()
                    .eq(File::getPath, action.getDestPath())
                    .or().eq(File::getMd5, md5).orderByDesc(File::getId).last(limit(1)).one();
            if (file != null
                    && file.getMd5().equals(action.getMd5())
                    && Objects.equals(file.getFullName(), action.getFileName())) {
                log.debug("上传文件｜检查到相同视频已在库中 md5:{} dest:{} file:{}",
                        action.getMd5(),
                        action.getDestPath(),
                        file);
                return FileTypeMapper.INST.videoStubFromFile(file);
            } else {
                file = new File();
            }
        } else {
            file = new File();
        }
        final UploadVideoResult uploadFileResult = upyunGateway.uploadVideo(action.getInputStream(),
                action.getDestPath(), action.getMd5());
        saveVideoFile(file, action, uploadFileResult);
        return VideoStub.builder(uploadFileResult.getUrl())
                .size(uploadFileResult.getSize())
                .mime("")
                .videoFirstFrame(uploadFileResult.getVideoFirstFrame())
                .name(file.getFullName())
                .build();
    }

    @Override
    public Map<String, File> fileQueryBatchByUrls(Collection<String> fileUrls) {
        if (CollectionUtil.isEmpty(fileUrls)) {
            return Collections.emptyMap();
        }
        final Map<String, String> pathMap = fileUrls.stream()
                .collect(Collectors.toMap(Function.identity(),
                        urlStr -> getDecodedPath(URLUtil.url(urlStr)), (a, b) -> a));
        final Map<String, File> fileMap = fileService.lambdaQuery()
                .in(File::getPath, pathMap.values())
                .list()
                .stream().collect(Collectors.toMap(File::getPath, Function.identity()));
        return fileUrls.stream()
                .filter(urlStr -> Objects.nonNull(pathMap.get(urlStr)) && fileMap.containsKey(
                        pathMap.get(urlStr)))
                .collect(Collectors.toMap(Function.identity(),
                        urlStr -> fileMap.get(pathMap.get(urlStr)), (a, b) -> a));
    }

    private void saveVideoFile(File file, UploadFileAction action,
            UploadVideoResult uploadVideoResult) {
        try {
            file.setType(FileType.VIDEO);
            file.setPath(decode(uploadVideoResult.getPath()));
            final String fileName = decode(action.getFileName());
            final String fileNamePrefix = FileNameUtil.getPrefix(fileName);
            final String fileNameSuffix = FileNameUtil.getSuffix(fileName);
            file.setName(fileNamePrefix);
            file.setExt(fileNameSuffix);
            file.setUrl(uploadVideoResult.getUrl());
            file.setMd5(action.getMd5());
            file.setSize(uploadVideoResult.getSize());
            file.setWidth(0);
            file.setHeight(0);
            file.setDuration(0);
            file.setCoverId(0L);
            file.setCoverUrl(uploadVideoResult.getVideoFirstFrame());
            file.setMeta("");
            file.setUpyun(true);
            fileService.saveOrUpdate(file);
        } catch (DuplicateKeyException ignored) {
        }

    }


}
