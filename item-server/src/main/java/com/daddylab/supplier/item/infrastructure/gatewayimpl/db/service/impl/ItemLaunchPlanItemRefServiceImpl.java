package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchPlanItemRef;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemLaunchPlanItemRefMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemLaunchPlanItemRefService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 商品上新计划和商品的关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */
@Service
public class ItemLaunchPlanItemRefServiceImpl extends DaddyServiceImpl<ItemLaunchPlanItemRefMapper, ItemLaunchPlanItemRef> implements IItemLaunchPlanItemRefService {
    @Autowired
    private ItemLaunchPlanItemRefMapper itemLaunchPlanItemRefMapper;

    @Override
    public List<ItemLaunchPlanItemRef> selectListByPlanId(Long planId) {
        QueryWrapper<ItemLaunchPlanItemRef> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ItemLaunchPlanItemRef::getPlanId, planId);
        return itemLaunchPlanItemRefMapper.selectList(queryWrapper);
    }

    @Override
    public List<ItemLaunchPlanItemRef> selectBatchByIds(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return itemLaunchPlanItemRefMapper.selectBatchIds(ids);
    }

    @Override
    public void deleteById(Long id) {
        itemLaunchPlanItemRefMapper.deleteById(id);
    }

    @Override
    public Long getLinkItemOnNum(Long planId) {
        return itemLaunchPlanItemRefMapper.getLinkItemOnNum(planId);
    }

    @Override
    public ItemLaunchPlanItemRef getItemLaunchPlanItemRef(Long itemId) {
        return itemLaunchPlanItemRefMapper.selectOne(new LambdaQueryWrapper<ItemLaunchPlanItemRef>() {{
            eq(ItemLaunchPlanItemRef::getItemId, itemId);
            last("limit 1");
        }});
    }

    @Override
    public Integer getItemNum(Long planId) {
        return itemLaunchPlanItemRefMapper.selectCount(new LambdaQueryWrapper<ItemLaunchPlanItemRef>() {{
            eq(ItemLaunchPlanItemRef::getPlanId, planId);
        }});
    }
}
