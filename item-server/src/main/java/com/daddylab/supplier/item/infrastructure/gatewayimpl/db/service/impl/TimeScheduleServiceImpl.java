package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.TimeSchedule;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.TimeScheduleType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.TimeScheduleMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ITimeScheduleService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 时间调度表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
@Service
public class TimeScheduleServiceImpl extends DaddyServiceImpl<TimeScheduleMapper, TimeSchedule> implements ITimeScheduleService {

    @Override
    public TimeSchedule getTimeSchedule(TimeScheduleType timeScheduleType, String businessId, LocalDateTime scheduleTime) {
        return lambdaQuery().eq(TimeSchedule::getType, timeScheduleType)
                            .eq(TimeSchedule::getBusinessId, businessId)
                            .eq(TimeSchedule::getScheduleTime, scheduleTime)
                            .one();
    }
}
