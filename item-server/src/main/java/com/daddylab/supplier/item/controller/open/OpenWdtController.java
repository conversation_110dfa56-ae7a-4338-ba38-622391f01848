package com.daddylab.supplier.item.controller.open;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.sales.TradeQueryAPI;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.StockoutSalesAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutSalesQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutSalesQueryWithDetailResponse;
import com.daddylab.supplier.item.application.openwdt.OpenWdtService;
import com.daddylab.supplier.item.application.openwdt.types.ApiParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/6/14
 */
@Slf4j
@RestController
@RequestMapping("/open/wdt")
@Api(value = "旺店通&奇门开放接口", tags = "旺店通&奇门开放接口")
public class OpenWdtController {

    @Autowired
    OpenWdtService openWdtService;

    @ApiOperation("旺店通销售出库单查询")
    @PostMapping("/stockoutSales/queryWithDetail")
    public SingleResponse<StockoutSalesQueryWithDetailResponse> stockoutSalesQueryWithDetail(
            @Validated
            @RequestBody ApiParams<StockoutSalesQueryWithDetailParams> params)
            throws WdtErpException {
        final StockoutSalesAPI api = openWdtService
                .getAPI(StockoutSalesAPI.class, params.getConfig(), params.isQimen(), true);
        return SingleResponse.of(api.queryWithDetail(params.getParams(), params.getPager()));
    }

    @ApiOperation("旺店通销售订单查询")
    @PostMapping("/tradeQuery/queryWithDetail")
    public SingleResponse<TradeQueryQueryWithDetailResponse> tradeQueryQueryWithDetail(
            @Validated
            @RequestBody ApiParams<TradeQueryQueryWithDetailParams> params)
            throws WdtErpException {
        final TradeQueryAPI api = openWdtService
                .getAPI(TradeQueryAPI.class, params.getConfig(), params.isQimen(), true);
        return SingleResponse.of(api.queryWithDetail(params.getParams(), params.getPager()));
    }
}
