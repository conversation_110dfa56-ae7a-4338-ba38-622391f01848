package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseSingleSkuCombinationPrice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 单sku纬度的组合采购价格信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-21
 */
public interface PurchaseSingleSkuCombinationPriceMapper extends DaddyBaseMapper<PurchaseSingleSkuCombinationPrice> {

    List<PurchaseSingleSkuCombinationPrice> getByTimeAndType(@Param("startTime") Long startTime,
                                                             @Param("endTime") Long endTime,
                                                             @Param("type") Integer type);


}
