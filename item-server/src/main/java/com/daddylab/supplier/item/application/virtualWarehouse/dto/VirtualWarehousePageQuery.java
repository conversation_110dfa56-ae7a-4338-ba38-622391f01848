/*
package com.daddylab.supplier.item.application.virtualWarehouse.dto;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryMode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

*/
/**
 * <AUTHOR> up
 * @date 2024年03月04日 3:50 PM
 *//*

@EqualsAndHashCode(callSuper = true)
@Data
public class VirtualWarehousePageQuery extends PageQuery {

    private static final long serialVersionUID = 5288042071136344244L;

    private String virtualWarehouseNo;

    private String virtualWarehouseName;

    private String warehouseName;

    */
/**
     * 0 正常。1 禁用
     *//*

    private Integer status;

    private List<Integer> businessLines;

    private Integer openDetailModel;

    private InventoryMode inventoryMode;

    private Integer modeVal;

}
*/
