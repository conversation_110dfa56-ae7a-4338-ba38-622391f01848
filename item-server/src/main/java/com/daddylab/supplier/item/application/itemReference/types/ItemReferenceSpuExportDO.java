package com.daddylab.supplier.item.application.itemReference.types;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

@Data
@ApiModel("商品参照表SPU导出模型")
public class ItemReferenceSpuExportDO {

    @ApiModelProperty("商品编码")
    @ExcelProperty("商品编码")
    private String itemCode;

    @ApiModelProperty("商品名称")
    @ExcelProperty("商品名称")
    private String itemName;

    @ApiModelProperty("品类")
    @ExcelProperty("品类")
    private String categoryPath;

    @ApiModelProperty("关联款号")
    @ExcelProperty("关联款号")
    private String partnerProviderItemSn;

    @ApiModelProperty("合作模式")
    @ExcelProperty("合作模式")
    @ExcelIgnore
    private String businessLine;

    @ApiModelProperty("SKU数量")
    @ExcelProperty("SKU数量")
    private Long skuNum;

    @ApiModelProperty("品牌")
    @ExcelProperty("品牌")
    private String brandName;

    @ApiModelProperty("供应商")
    @ExcelProperty("供应商")
    private String provider;

    @ApiModelProperty("采购员花名")
    @ExcelProperty("采购员")
    private String buyerNick;

    @ApiModelProperty("发货渠道 0.仓库发货。1.工厂发货。2.仓库工厂均发货")
    @ExcelProperty(value = "发货渠道")
    private String delivery;

    @ApiModelProperty("税率")
    @ExcelProperty("税率")
    private String taxRate;

    @ApiModelProperty("商品状态 0:待上架 1:已上架 2:下架")
    @ExcelProperty("商品状态")
    private String itemStatus;

    @ApiModelProperty("上架日期")
    @ExcelProperty("上架日期")
    private String launchDate;

    @ApiModelProperty("仓库库存")
    @ExcelProperty("仓库库存")
    private Long stock;

    @ApiModelProperty("合作方业务类型")
    @ExcelProperty("合作方/业务类型")
    private String corpBizType;
}