package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.daddylab.supplier.item.infrastructure.domain.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.PropertyName;

/**
 * <p>
 * 商品上新计划和商品的关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemLaunchPlanItemRef extends Entity {

    private static final long serialVersionUID = 1L;

    /**
     * 商品上新计划ID（item_launch_plan#id）
     */
    @DiffIgnore
    private Long planId;

    /**
     * 商品ID（item#id）
     */
    @DiffIgnore
    private Long itemId;

    /**
     * 商品规格ID
     */
//    private Long skuId;

    /**
     * 商品编码
     */
    @DiffIgnore
    private String itemCode;

    /**
     * 规格编码
     */
//    private String skuCode;

    /**
     * 商品名称
     */
//    private String itemName;

    /**
     * 规格名称
     */
//    private String skuName;

    /**
     * 类目
     */
//    private String categoryPath;

    /**
     * 采购价格
     */
//    private BigDecimal purchasePrice;

    /**
     * 销售价格
     */
//    private BigDecimal salePrice;

    /**
     * 库存
     */
//    private Integer stock;

    /**
     * 采购员user_id
     */
//    private Long buyerUserId;

    /**
     * 采购员姓名
     */
//    private String buyerUserName;

//    /**
//     * 产品负责人user_id
//     */
//    private Long principalUserId;
//
//    /**
//     * 产品负责人姓名
//     */
//    private String principalUserName;

    /**
     * 备注
     */
    @DiffIgnore
    private String remark;

    @DiffIgnore
    private Integer sortNo;


    @PropertyName("首页文案")
    private String homeCopy;

    @PropertyName("上新价")
    private String launchPrice;

    /**
     * 商品类型。1：内部，2：外包
     */
    @DiffIgnore
    private Integer itemType;

    @DiffIgnore
    @PropertyName("新品活动开始周期")
    public Long activePeriodStart;

    @DiffIgnore
    @PropertyName("新品活动结束周期")
    private Long activePeriodEnd;

    @PropertyName("共享盘链接")
    private String shareDiskLink;

}

