package com.daddylab.supplier.item.application.afterSaleLogistics.dto;

import com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsException;
import com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsRootException;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2024年05月23日 2:07 PM
 */
@Data
public class LogisticExceptionRes {

  LogisticsRootException rootException;

  LogisticsException subException;

  public boolean isException() {
    return LogisticsRootException.NORMAL != rootException;
  }
}
