package com.daddylab.supplier.item.controller.otherStockOutDetail.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName OtherStockOutDetailVo.java
 * @description
 * @createTime 2022年04月01日 16:30:00
 */
@Data
@ApiModel("其他出库单详情返回实体")
public class OtherStockOutDetailVo implements Serializable {

    private static final long serialVersionUID = 3672020452738864988L;

    @ApiModelProperty(value = "其他出库单详情id")
    private Long id;

    @ApiModelProperty(value = "其他入库单id")
    private Long orderId;

    @ApiModelProperty(value = "商品id")
    private Long itemId;

    @ApiModelProperty(value = "商品Sku")
    private Long skuId;

    @ApiModelProperty(value = "商品Sku名称")
    private String skuName;

    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "商品规格list")
    private String skuAttrRefDOS;

    @ApiModelProperty(value = "预期出库量")
    private Long predictCount;

    @ApiModelProperty(value = "数量")
    private Long count;

    @ApiModelProperty(value = "是否残次品 0:不是 1:是")
    private Integer isDefect;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "品牌id")
    private Long brandId;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "条形码")
    private String barCode;

    @ApiModelProperty(value = "预估重量")
    private BigDecimal predictWeight;

    @ApiModelProperty(value = "备注")
    private String remark;

    private Integer businessLine;
}
