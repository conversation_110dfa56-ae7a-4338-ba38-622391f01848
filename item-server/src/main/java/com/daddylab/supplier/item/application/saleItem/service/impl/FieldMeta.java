package com.daddylab.supplier.item.application.saleItem.service.impl;

import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.lang.func.LambdaUtil;
import java.util.HashSet;
import java.util.Set;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2022/6/7
 */
@Getter
@EqualsAndHashCode(of = "prop")
@ToString
public class FieldMeta {

    /**
     * 属性名称
     */
    String prop;

    /**
     * 访问权限
     */
    Set<String> permissions = new HashSet<>();

    /**
     * 是否允许任意用户访问
     */
    boolean accessAny = false;

    /**
     * 是否必填
     */
    boolean required = false;

    /**
     * 判空时使用类型宽松的判断（对于字符串，空格将会被视为空；对于整形，0将会被视为空）
     */
    boolean emptyCheckLenient = false;

    /**
     * 跳过检查
     */
    boolean notCheck = false;

    public static <T> FieldMeta of(Func1<T, ?> prop) {
        final FieldMeta fieldMeta = new FieldMeta();
        fieldMeta.prop = LambdaUtil.getFieldName(prop);
        return fieldMeta;
    }

    public FieldMeta accessAny() {
        accessAny = true;
        return this;
    }

    public FieldMeta access(String permission) {
        permissions.add(permission);
        return this;
    }

    public FieldMeta required() {
        required = true;
        return this;
    }

    public FieldMeta emptyCheckLenient() {
        this.emptyCheckLenient = true;
        return this;
    }

    public FieldMeta notCheck() {
        this.notCheck = true;
        return this;
    }
}
