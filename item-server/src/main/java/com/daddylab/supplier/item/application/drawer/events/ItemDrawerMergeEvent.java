package com.daddylab.supplier.item.application.drawer.events;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @class ItemDrawerMergeEvent.java
 * @description 描述类的作用
 * @date 2024-04-09 15:28
 */
@Getter
public class ItemDrawerMergeEvent extends ApplicationEvent {

    private final Long itemDrawerMergeId;

    public ItemDrawerMergeEvent(Object source, Long itemDrawerMergeId) {
        super(source);
        this.itemDrawerMergeId  = itemDrawerMergeId;
    }
}
