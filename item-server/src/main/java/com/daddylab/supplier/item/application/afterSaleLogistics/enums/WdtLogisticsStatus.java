package com.daddylab.supplier.item.application.afterSaleLogistics.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 物流单状态 0:待查询 1:待取件 2:已取件 3:在途中 4:待配送 5:已签收 6:拒收 7:已处理 8:未订阅(不支持物流) 9:退件中 10:已退签 11:问题件 12:拦截成功 13:拦截失败 99:推送失败
 *
 * <AUTHOR>
 * @since 2024/12/18
 */
@RequiredArgsConstructor
@Getter
public enum WdtLogisticsStatus implements IIntegerEnum {
    WAIT_QUERY(0, "待查询"),
    WAIT_PICKUP(1, "待取件"),
    ALREADY_PICKUP(2, "已取件"),
    IN_TRANSIT(3, "在途中"),
    WAIT_DISTRIBUTION(4, "待配送"),
    ALREADY_SIGNED(5, "已签收"),
    REJECTION(6, "拒收"),
    ALREADY_HANDLED(7, "已处理"),
    UNSUBSCRIBED(8, "未订阅(不支持物流)"),
    RETURNING(9, "退件中"),
    ALREADY_RETURNED(10, "已退签"),
    PROBLEM(11, "问题件"),
    INTERCEPT_SUCCESS(12, "拦截成功"),
    INTERCEPT_FAIL(13, "拦截失败"),
    PUSH_FAIL(99, "推送失败"),

    ;
    private final Integer value;
    private final String desc;
}
