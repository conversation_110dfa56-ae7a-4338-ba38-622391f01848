package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.message.wechat.v2.statics.QwMsgQcStaticsBo;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTrainingMaterials;

import java.util.List;

/**
 * <p>
 * 新品商品培训资料 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
public interface ItemTrainingMaterialsMapper extends DaddyBaseMapper<ItemTrainingMaterials> {


    List<QwMsgQcStaticsBo> staticsToBeQcAudit();

}
