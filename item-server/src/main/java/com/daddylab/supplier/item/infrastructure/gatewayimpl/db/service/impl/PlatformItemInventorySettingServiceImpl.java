package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemInventorySetting;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemInventorySettingMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemInventorySettingService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 平台商品库存设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Service
public class PlatformItemInventorySettingServiceImpl extends DaddyServiceImpl<PlatformItemInventorySettingMapper, PlatformItemInventorySetting> implements IPlatformItemInventorySettingService {

    @Override
    public PlatformItemInventorySetting getByOuterItemId(Platform platform, String shopNo, String outerItemId) {
        return lambdaQuery()
                .eq(PlatformItemInventorySetting::getPlatform, platform)
                .eq(PlatformItemInventorySetting::getShopNo, shopNo)
                .eq(PlatformItemInventorySetting::getOuterItemId,
                        outerItemId)
                .one();
    }
}
