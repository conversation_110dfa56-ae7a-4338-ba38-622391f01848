package com.daddylab.supplier.item.application.purchasePayable.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> up
 * @date 2023年02月06日 5:29 PM
 */
@Data
@ApiModel("应付单和关联单数据")
public class ApplyPayNoDto extends Command {

    private static final long serialVersionUID = -909440807972847413L;
    @ApiModelProperty("付款单原单号")
    @NotBlank(message = "付款单原单号不得为空")
    private String purchasePayOrderNo;

    @ApiModelProperty("原付款关联的单号，出入库单编号")
    @NotBlank(message = "原付款关联的单号不得为空")
    private String relationNo;
}
