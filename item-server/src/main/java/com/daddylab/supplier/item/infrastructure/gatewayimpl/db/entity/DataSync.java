package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DataSyncStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DataSyncType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 数据同步记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataSync implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据类型 1:旺店通系统内的平台商品 2:我们自己系统中的平台商品...
     */
    private DataSyncType dataType;

    /**
     * 同步时间范围-开始时间
     */
    private LocalDateTime syncRangeStart;

    /**
     * 同步时间范围-结束时间
     */
    private LocalDateTime syncRangeEnd;

    /**
     * 状态 0:未开始 1:同步中 2:同步完成 3:同步异常
     */
    private DataSyncStatus status;

    /**
     * 异常原因
     */
    private String err;

    /**
     * 拓展字段，额外数据
     */
    private String data;

    /**
     * 同步成功数量
     */
    private Integer successNum;

    /**
     * 同步失败数量
     */
    private Integer failNum;

    /**
     * 警告数量
     */
    private Integer warnNum;

    /**
     * 跳过数量
     */
    private Integer skipNum;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;


}
