package com.daddylab.supplier.item.infrastructure.third.kuaishou;

import com.daddylab.supplier.item.infrastructure.third.kuaishou.domain.dto.KsMessageDTO;

/**
 * <AUTHOR>
 * @class IKsMessageHandlerService.java
 * @description 描述类的作用
 * @date 2024-03-01 17:31
 */
public interface IKsMessageHandlerService {

    /**
     * 无指定事件方法时执行该方法
     *
     * @return void
     * @date 2024/3/1 17:31
     * <AUTHOR>
     */
    void handler(KsMessageDTO ksMessageDTO);
}
