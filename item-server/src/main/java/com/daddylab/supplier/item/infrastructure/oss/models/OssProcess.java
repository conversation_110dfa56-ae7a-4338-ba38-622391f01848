package com.daddylab.supplier.item.infrastructure.oss.models;

/**
 * <AUTHOR>
 * @since 2023/10/13
 */
public interface OssProcess {

    static OssProcess command(String command) {
        return new OssProcessImpl(command);
    }

    static OssProcess command(OssProcessModel model) {
        return new OssProcessImpl(model);
    }

    OssProcess param(String name, String value);

    OssProcess and(String command);

    OssProcess and(OssProcessModel model);
}
