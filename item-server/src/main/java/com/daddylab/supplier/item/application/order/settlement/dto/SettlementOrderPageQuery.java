package com.daddylab.supplier.item.application.order.settlement.dto;

import com.alibaba.cola.dto.PageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayApplyStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PaymentOrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年08月14日 4:26 PM
 */
@ApiModel("结算单分页查询参数")
@Data
public class SettlementOrderPageQuery extends PageQuery {

    private static final long serialVersionUID = -8230603437878640437L;
    @ApiModelProperty("结算单编号")
    private String no;

    private List<String> noList;

    @ApiModelProperty("供应商id")
    private Long providerId;
    @ApiModelProperty("仓库编码")
    private List<String> warehouseNos;
    @ApiModelProperty("订单员用户id")
    private List<Long> orderPersonnelIds;
    @ApiModelProperty("结算周期")
    private List<Long> times;
    @ApiModelProperty("合作模式")
    private List<Integer> businessLine;
    private List<Long> ids;

    @ApiModelProperty("付款申请状态")
    private PayApplyStatus payApplyStatus;

    @ApiModelProperty("付款单付款状态")
    private PaymentOrderStatus paymentOrderStatus;

    private List<PaymentOrderStatus> paymentOrderStatusList = new LinkedList<>();


}
