package com.daddylab.supplier.item.application.message.vo;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessageOperationType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessagePushType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 4:22 下午
 * @description
 */
@Data
@ApiModel("消息配置返回")
public class MessageConfigVO implements Serializable {


    private static final long serialVersionUID = -2685273232994442433L;

    @ApiModelProperty("消息配置id")
    private Long id;

    @ApiModelProperty(value = "消息关联的操作类型")
    private MessageOperationType operationType;

    @ApiModelProperty(value = "消息推送类型")
    private List<MessagePushType> pushType;

    @ApiModelProperty("消息填充demo数据")
    private String sample;

    @ApiModelProperty("消息填充模板")
    private String template;

    @ApiModelProperty("消息接收者")
    private List<MsgRecipientVO> recipients;

    @ApiModelProperty("消息是否生效")
    private Boolean canEffect;

}
