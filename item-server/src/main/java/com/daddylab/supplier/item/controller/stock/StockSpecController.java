package com.daddylab.supplier.item.controller.stock;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.stockSpec.StockSpecBizService;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;
import com.daddylab.supplier.item.types.BatchResultStat;
import com.daddylab.supplier.item.types.stockSpec.SetInventoryMonitorCmd;
import com.daddylab.supplier.item.types.stockSpec.StockSpecQuery;
import com.daddylab.supplier.item.types.stockSpec.StockSpecVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/1
 */
@RestController
@RequestMapping("/stock/spec/")
@Api(value = "库存管理/仓库库存", tags = "库存管理/仓库库存")
public class StockSpecController {
    @Resource
    StockSpecBizService stockSpecBizService;

    @ApiOperation(value = "列表查询", notes = "排序字段目前支持：stockNum（仓内库存）、availableSendStock（可发库存）、availableStock（可用库存）")
    @PostMapping(value = "/query")
    public PageResponse<StockSpecVO> queryPage(@RequestBody StockSpecQuery query) {
        query.setCorpType(UserPermissionJudge.filterCorpType(query.getCorpType()));
        return stockSpecBizService.query(query);
    }

    @ApiOperation(value = "导出")
    @PostMapping(value = "/export")
    public SingleResponse<ExportTask> export(@RequestBody StockSpecQuery query) {
        query.setCorpType(UserPermissionJudge.filterCorpType(query.getCorpType()));
        return stockSpecBizService.export(query);
    }

    @ApiOperation(value = "设置库存警戒")
    @PostMapping(value = "/setInventoryMonitor")
    public SingleResponse<BatchResultStat> setInventoryMonitor(@Validated @RequestBody SetInventoryMonitorCmd cmd) {
        return stockSpecBizService.setInventoryMonitor(cmd);
    }

    @ApiOperation(value = "指定规格编码拉取")
    @PostMapping(value = "/fetchBySpecNos")
    public Response fetchBySpecNos(@RequestBody List<String> specNos) {
        return stockSpecBizService.fetchBySpecNos(specNos);
    }
}
