package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/21 4:37 下午
 * @description
 */
@Data
@ApiModel("运营人员信息封装")
public class RunningDto {
//
//    @ApiModelProperty("id")
//    private Long id;

    @ApiModelProperty("是否主要负责人。1标示是主要负责人。0不是")
    private Long isHead;

    @ApiModelProperty("运营人员ids")
    private List<RunnerDto> operatorIdList;

    @ApiModelProperty("运营岗位名字")
    private String customName;

//    @Data
//    public static class Runner{
//        private Long userId;
//        private String name;
//    }
}
