package com.daddylab.supplier.item.domain.stockOutOrder;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/3/28 17:35
 * @description StockOutOrderSheet
 */
@Data
@HeadRowHeight(20)
@ContentRowHeight(16)
@EqualsAndHashCode(callSuper = false)
public class StockOutOrderSheet extends ExportSheet {

    @ColumnWidth(50)
    @ExcelProperty("出库单号")
    private String no;

    @ColumnWidth(50)
    @ExcelProperty("合作方")
    private String businessLineStr;

    /**
     * 供应商
     */
    @ColumnWidth(50)
    @ExcelProperty("供应商")
    private String provider;

    /**
     * 采购组织
     */
    @ColumnWidth(50)
    @ExcelProperty("采购组织")
    private String purchaseOrganization;

    /**
     * 退料类型(1:库存退料)
     */
    @ColumnWidth(50)
    @ExcelProperty("退料类型")
    private String returnType;

    /**
     * 退料方式(1.退料补料。2.退料并扣款)
     */
    @ColumnWidth(50)
    @ExcelProperty("退料方式")
    private String returnMode;

    /**
     * 商品skuCode
     */
    @ColumnWidth(50)
    @ExcelProperty("*商品SKU")
    private String itemSkuCode;

    /**
     * 商品名称
     */
    @ColumnWidth(50)
    @ExcelProperty("商品名称")
    private String itemName;

    /**
     * 规格名称
     */
    @ColumnWidth(50)
    @ExcelProperty("规格名称")
    private String specifications;

    /**
     * 库存单位
     */
    @ColumnWidth(50)
    @ExcelProperty("*库存单位")
    private String stockUnit;

    /**
     * 申请退料数量
     */
    @ColumnWidth(50)
    @ExcelProperty("*申请退料数量")
    private Integer returnQuantity;

    /**
     * 退料仓库
     */
    @ColumnWidth(50)
    @ExcelProperty("退料仓库")
    private String stock;


    /**
     * 计价单位
     */
    @ColumnWidth(50)
    @ExcelProperty("*计价单位")
    private String valuationUnit;

    /**
     * 计价数量
     */
    @ColumnWidth(50)
    @ExcelProperty("*计价数量")
    private Integer valuationQuantity;

    /**
     * 补料数量
     */
    @ColumnWidth(50)
    @ExcelProperty("补料数量")
    private Integer replenishQuantity;

    /**
     * 扣款数量
     */
    @ColumnWidth(50)
    @ExcelProperty("扣款数量")
    private Integer deductionQuantity;

    /**
     * 退料原因
     */
    @ColumnWidth(50)
    @ExcelProperty("退料原因")
    private String returnReason;

    /**
     * 是否赠品,0不是。1是
     */
    @ColumnWidth(50)
    @ExcelProperty("是否赠品")
    private String isGift;

    /**
     * 备注
     */
    @ColumnWidth(50)
    @ExcelProperty("备注")
    private String remark;

    /**
     * 含税单价(6位)
     */
    @ColumnWidth(50)
    @ExcelProperty("单价(税前)")
    private String taxPrice;

    /**
     * 税率(实际计算值，非百分比)
     */
    @ColumnWidth(50)
    @ExcelProperty("税率%")
    private String taxRate;

    /**
     * 税额
     */
    @ColumnWidth(50)
    @ExcelProperty("税额")
    private String taxQuota;

    /**
     * 价税合计
     */
    @ColumnWidth(50)
    @ExcelProperty("税前金额")
    private String totalPriceTax;

    /**
     * 税后单价(6位)
     */
    @ColumnWidth(50)
    @ExcelProperty("税后单价")
    private String afterTaxPrice;

    /**
     * 税后金额
     */
    @ColumnWidth(50)
    @ExcelProperty("税后金额")
    private String afterTaxAmount;

    @ExcelIgnore
    private Integer businessLine;
}
