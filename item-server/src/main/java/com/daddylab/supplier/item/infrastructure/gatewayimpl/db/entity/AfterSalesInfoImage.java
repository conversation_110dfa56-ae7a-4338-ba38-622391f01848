package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 售后异常件信息图片表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AfterSalesInfoImage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 售后异常件信息id
     */
    private Long relatedId;

    /**
     * 图片url
     */
    private String image;

    /**
     * 1：异常件信息图片
     * 2：转寄信息图片
     */
    private Integer type;


}
