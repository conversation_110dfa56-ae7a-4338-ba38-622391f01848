package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 第三方平台商品同步状态
 * <p>
 * 0:同步未开始 1:同步进行中 2:同步完成 -1:同步异常
 */
@Getter
@AllArgsConstructor
public enum ThirdPlatformSyncState implements IEnum<Integer> {
    /**
     * 等待调度
     */
    WAITING(0, "同步未开始"),

    /**
     * 任务运行中
     */
    RUNNING(1, "同步进行中"),

    /**
     * 任务运行结束
     */
    FINISH(2, "同步完成"),

    /**
     * ERROR
     */
    ERROR(-1, "同步异常"),
    ;

    private final Integer value;
    private final String desc;

}
