package com.daddylab.supplier.item.application.drawer;

import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAuditStats;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerModuleAuditStatsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemLaunchStatsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INewGoodsService;
import com.daddylab.supplier.item.types.itemDrawer.ItemLaunchAuditTaskClaimEvent;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemLaunchProcessNodeId;
import com.google.common.eventbus.Subscribe;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Slf4j
@EventBusListener(value = "itemDrawerAuditTaskClaimEventListener")
public class ItemDrawerAuditTaskClaimEventListener {

    private final IItemLaunchStatsService itemLaunchStatsService;
    private final IItemDrawerModuleAuditStatsService itemDrawerModuleAuditStatsService;
    private final INewGoodsService newGoodsService;


    @Subscribe
    public void listener(ItemLaunchAuditTaskClaimEvent event) {
        Long itemId = event.getItemId();

        if (event.getNodeId() == ItemLaunchProcessNodeId.LEGAL) {
            newGoodsService.lambdaUpdate().eq(NewGoods::getItemId, itemId)
                    .set(NewGoods::getLegalId, event.getProcessorId()).update();

            if (event.getType() == ItemAuditType.ITEM_MATERIAL) {
                itemLaunchStatsService.auditStat(itemId, event.getProcessorId(), 0L, 0L, null, null,
                        null);
            }
            final ItemDrawerModuleAuditStats moduleAuditStats = itemDrawerModuleAuditStatsService.getModuleAuditStatsCreateIfNotExists(
                    event.getType(), event.getItemId(), event.getRound());
            moduleAuditStats.setLegalAuditUid(event.getProcessorId());
            moduleAuditStats.setLegalAuditStartTime(0L);
            moduleAuditStats.setLegalAuditEndTime(0L);
            moduleAuditStats.setLegalAuditCostTime(0L);
            itemDrawerModuleAuditStatsService.updateById(moduleAuditStats);
        }
    }

}
