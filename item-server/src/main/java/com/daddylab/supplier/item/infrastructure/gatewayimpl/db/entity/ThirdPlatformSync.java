package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ThirdPlatformSyncState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ThirdPlatformSyncType;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 第三方平台同步信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ThirdPlatformSync implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 平台类型。1微信。2淘宝。3抖店
     */
    private PlatformType platformType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 商品编码
     */
    private String itemCode;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 同步状态。0.同步未开始 1.同步进行中。2.同步完成。-1同步异常
     */
    private ThirdPlatformSyncState state;

    /**
     * 同步类型。21:淘宝影刀 31:抖音上新 32:抖音编辑
     */
    private ThirdPlatformSyncType type;

    /**
     * 第三方同步链接
     */
    private String thirdLink;

    /**
     * sku数量
     */
    private Integer skuCount;

    /**
     * 平台商品数量
     */
    private Integer platformItemCount;

    /**
     * 异常原因
     */
    private String error;

    /**
     * 环境（特殊场景使用，目前仅淘宝同步使用，其他可为空）
     */
    private String env;

    /**
     * 发起同步参数
     */
    private String syncParam;


}
