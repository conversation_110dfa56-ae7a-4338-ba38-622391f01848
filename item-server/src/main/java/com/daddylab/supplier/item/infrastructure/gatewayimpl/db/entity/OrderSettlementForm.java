package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.application.order.settlement.sys.DetailStaticDo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OrderSettlementStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 订单结算表单
 * </p>
 * 表中的数据分两类。
 * 1.系统批量根据采购订单自动生成，为系统结算单，这批数据全部为待结算数据，没有结算编码。
 * 2.业务编辑系统结算单，生成具有结算编码的业务结算单，同时对应的系统结算单状态改为已结算。
 * relateId字段保存两者关联关系。
 * 业务结算单可以由多张系统结算单组成，对应关系为 1 对 N
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrderSettlementForm implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 结算单号
     */
    private String no;

    /**
     * 结算月份开始日期
     */
    private Long settlementStartDate;

    /**
     * 结算月份结束日期
     */
    private Long settlementEndDate;

    /**
     * 采购供应商
     */
    private Long pProviderId;

    /**
     * 结算供应商
     */
    private Long sProviderId;

    /**
     * 仓库编码
     */
    private String warehouseNo;

    public String getWarehouseNo0(){
        if(this.warehouseNo.contains(StrUtil.COMMA)){
            String[] split = warehouseNo.split(StrUtil.COMMA);
            return split[0];
        }
        return this.warehouseNo;
    }

    /**
     * 状态.详细
     * {@link OrderSettlementStatus}
     */
    private OrderSettlementStatus status;

    /**
     * 采购单编号
     */
    private String purchaseOrderNo;

    /**
     * 暂估数量
     */
    private Integer temporaryQuantity;

    /**
     * 结算数量
     */
    private Integer settlementQuantity;

    /**
     * 明细统计信息 json
     * {@link DetailStaticDo}
     */
    private String staticInfo;

    /**
     * 关联关系保存
     */
    private String relateId;

    /**
     * 业务线划分
     */
    private Integer businessLine;

    /**
     * 冲销流程，此结算单关联的销售出库单
     */
    private String salesOutOrderNo;

    /**
     * 冲销流程，结算单关联的销售退料单
     */
    private String salesReturnOrderNo;

    /**
     * 数据版本号
     */
    private Integer version;


}
