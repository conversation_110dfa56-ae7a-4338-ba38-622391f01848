package com.daddylab.supplier.item.application.refundOrder;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.RefundAPI;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse.Order;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse.Order.AmountDetail;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse.Order.Detail;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse.Order.SwapOrder.SwapOrderDetail;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrderAmountDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSwapOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSwapOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundOrderAmountDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtSwapOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtSwapOrderMapper;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/5/28
 */
@Service
public class WdtRefundOrderDataSyncServiceImpl implements WdtRefundOrderDataSyncService {

    private final WdtRefundOrderMapper wdtRefundOrderMapper;
    private final WdtRefundOrderDetailMapper wdtRefundOrderDetailMapper;
    private final WdtRefundOrderAmountDetailMapper wdtRefundOrderAmountDetailMapper;
    private final WdtSwapOrderMapper wdtSwapOrderMapper;
    private final WdtSwapOrderDetailMapper wdtSwapOrderDetailMapper;
    private final WdtGateway wdtGateway;

    public WdtRefundOrderDataSyncServiceImpl(
            WdtRefundOrderMapper wdtRefundOrderMapper,
            WdtRefundOrderDetailMapper wdtRefundOrderDetailMapper,
            WdtRefundOrderAmountDetailMapper wdtRefundOrderAmountDetailMapper,
            WdtSwapOrderMapper wdtSwapOrderMapper,
            WdtSwapOrderDetailMapper wdtSwapOrderDetailMapper,
            WdtGateway wdtGateway) {
        this.wdtRefundOrderMapper = wdtRefundOrderMapper;
        this.wdtRefundOrderDetailMapper = wdtRefundOrderDetailMapper;
        this.wdtRefundOrderAmountDetailMapper = wdtRefundOrderAmountDetailMapper;
        this.wdtSwapOrderMapper = wdtSwapOrderMapper;
        this.wdtSwapOrderDetailMapper = wdtSwapOrderDetailMapper;
        this.wdtGateway = wdtGateway;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveOrUpdateByApiQueryResponse(RefundSearchResponse response) {
        final List<WdtRefundOrder> wdtRefundOrders = new ArrayList<>();
        final List<WdtRefundOrderDetail> wdtRefundOrderDetails = new ArrayList<>();
        final ArrayList<WdtRefundOrderAmountDetail> wdtRefundOrderAmountDetails = new ArrayList<>();
        final ArrayList<WdtSwapOrder> swapOrders = new ArrayList<>();
        final ArrayList<WdtSwapOrderDetail> swapOrderDetails = new ArrayList<>();

        final RefundOrderAssembler assembler = RefundOrderAssembler.INST;
        for (Order order : response.getOrder()) {
            final WdtRefundOrder wdtRefundOrder = assembler.apiOrderDtoToPo(order);
            if (wdtRefundOrder.getCheckTime() == null && wdtRefundOrder.getStatus() >= 30) {
                wdtRefundOrder.setCheckTime(wdtRefundOrder.getModified());
            }
            wdtRefundOrders.add(wdtRefundOrder);

            final List<WdtRefundOrderDetail> wdtRefundOrderDetailsThisOrder = new ArrayList<>();
            for (Detail detail : order.getDetailList()) {
                final WdtRefundOrderDetail wdtRefundOrderDetail = assembler
                        .apiOrderDetailDtoToPo(detail, order.getRefundNo());
                wdtRefundOrderDetailsThisOrder.add(wdtRefundOrderDetail);
                wdtRefundOrderDetails.add(wdtRefundOrderDetail);
                wdtRefundOrder.setPlatformId(detail.getPlatformId());
            }
            wdtRefundOrder.setDetails(wdtRefundOrderDetailsThisOrder);

            final ArrayList<WdtRefundOrderAmountDetail> wdtRefundOrderAmountDetailsThisOrder = new ArrayList<>();
            for (AmountDetail amountDetail : order.getAmountDetailList()) {
                final WdtRefundOrderAmountDetail wdtRefundOrderAmountDetail = assembler
                        .apiOrderAmountDetailDtoToPo(amountDetail, order.getRefundNo());
                wdtRefundOrderAmountDetailsThisOrder.add(wdtRefundOrderAmountDetail);
                wdtRefundOrderAmountDetails.add(wdtRefundOrderAmountDetail);
            }
            wdtRefundOrder.setAmountDetails(wdtRefundOrderAmountDetailsThisOrder);

            if (order.getSwapOrder() != null) {
                final WdtSwapOrder wdtSwapOrder = assembler
                        .apiSwapOrderDtoToPo(order.getSwapOrder(), order.getRefundNo());
                wdtRefundOrder.setSwapOrder(wdtSwapOrder);
                swapOrders.add(wdtSwapOrder);

                final ArrayList<WdtSwapOrderDetail> swapOrderDetailsThisOrder = new ArrayList<>();
                for (SwapOrderDetail swapOrderDetail : order.getSwapOrder()
                        .getSwapOrderDetailList()) {
                    final WdtSwapOrderDetail wdtSwapOrderDetail = assembler
                            .apiSwapOrderDetailDtoToPo(swapOrderDetail, order.getRefundNo(),
                                    wdtSwapOrder.getTid());
                    swapOrderDetailsThisOrder.add(wdtSwapOrderDetail);
                    swapOrderDetails.add(wdtSwapOrderDetail);
                }
                wdtSwapOrder.setDetails(swapOrderDetailsThisOrder);
            }
        }

        deleteRemovedDetails(wdtRefundOrders);

        if (!wdtRefundOrders.isEmpty()) {
            wdtRefundOrderMapper.saveOrUpdateBatch(wdtRefundOrders);
        }

        if (!wdtRefundOrderDetails.isEmpty()) {
            wdtRefundOrderDetailMapper.saveOrUpdateBatch(wdtRefundOrderDetails);
        }
        if (!wdtRefundOrderAmountDetails.isEmpty()) {
            wdtRefundOrderAmountDetailMapper.saveOrUpdateBatch(wdtRefundOrderAmountDetails);
        }
        if (!swapOrders.isEmpty()) {
            wdtSwapOrderMapper.saveOrUpdateBatch(swapOrders);
        }
        if (!swapOrderDetails.isEmpty()) {
            wdtSwapOrderDetailMapper.saveOrUpdateBatch(swapOrderDetails);
        }
    }

    private void deleteRemovedDetails(List<WdtRefundOrder> wdtRefundOrders) {
        if (wdtRefundOrders.isEmpty()) {
            return;
        }
        final LambdaUpdateWrapper<WdtRefundOrderDetail> queryForRemoveDetails = Wrappers
                .lambdaUpdate();
        for (WdtRefundOrder wdtRefundOrder : wdtRefundOrders) {
            final List<Integer> recIds = wdtRefundOrder.getDetails().stream()
                    .map(WdtRefundOrderDetail::getRecId)
                    .collect(Collectors.toList());
            if (recIds.isEmpty()) {
                queryForRemoveDetails
                        .or(sub -> sub.eq(WdtRefundOrderDetail::getRefundNo,
                                wdtRefundOrder.getRefundNo()));
            } else {
                queryForRemoveDetails
                        .or(sub -> sub
                                .eq(WdtRefundOrderDetail::getRefundNo, wdtRefundOrder.getRefundNo())
                                .notIn(WdtRefundOrderDetail::getRecId, recIds));
            }
        }
        wdtRefundOrderDetailMapper
                .update(null, queryForRemoveDetails.set(WdtRefundOrderDetail::getDeletedAt,
                        DateUtil.currentTime()));

        final LambdaUpdateWrapper<WdtRefundOrderAmountDetail> queryForRemoveAmountDetails = Wrappers
                .lambdaUpdate();
        for (WdtRefundOrder wdtRefundOrder : wdtRefundOrders) {
            final List<Integer> recIds = wdtRefundOrder.getAmountDetails().stream()
                    .map(WdtRefundOrderAmountDetail::getRecId)
                    .collect(Collectors.toList());
            if (recIds.isEmpty()) {
                queryForRemoveAmountDetails
                        .or(sub -> sub
                                .eq(WdtRefundOrderAmountDetail::getRefundNo,
                                        wdtRefundOrder.getRefundNo()));
            } else {
                queryForRemoveAmountDetails
                        .or(sub -> sub
                                .eq(WdtRefundOrderAmountDetail::getRefundNo,
                                        wdtRefundOrder.getRefundNo())
                                .notIn(WdtRefundOrderAmountDetail::getRecId, recIds));
            }
        }
        wdtRefundOrderAmountDetailMapper.update(null, queryForRemoveAmountDetails
                .set(WdtRefundOrderAmountDetail::getDeletedAt, DateUtil.currentTime()));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveOrUpdateBatch(List<WdtRefundOrder> wdtRefundOrders) {
        if (wdtRefundOrders.isEmpty()) {
            return;
        }

        wdtRefundOrderMapper.saveOrUpdateBatch(wdtRefundOrders);

        final List<WdtRefundOrderDetail> wdtRefundOrderDetails = wdtRefundOrders.stream()
                .flatMap(order -> order.getDetails().stream()).collect(
                        Collectors.toList());
        if (!wdtRefundOrderDetails.isEmpty()) {
            wdtRefundOrderDetailMapper.saveOrUpdateBatch(wdtRefundOrderDetails);
        }

        final List<WdtRefundOrderAmountDetail> wdtRefundOrderAmountDetails = wdtRefundOrders
                .stream()
                .flatMap(order -> order.getAmountDetails().stream()).collect(
                        Collectors.toList());
        if (!wdtRefundOrderAmountDetails.isEmpty()) {
            wdtRefundOrderAmountDetailMapper.saveOrUpdateBatch(wdtRefundOrderAmountDetails);
        }

        final List<WdtSwapOrder> wdtSwapOrders = wdtRefundOrders.stream()
                .map(WdtRefundOrder::getSwapOrder).collect(
                        Collectors.toList());
        if (!wdtSwapOrders.isEmpty()) {
            wdtSwapOrderMapper.saveOrUpdateBatch(wdtSwapOrders);
        }

        final List<WdtSwapOrderDetail> wdtSwapOrderDetails = wdtRefundOrders.stream()
                .map(WdtRefundOrder::getSwapOrder).flatMap(so -> so.getDetails().stream()).collect(
                        Collectors.toList());
        if (!wdtSwapOrderDetails.isEmpty()) {
            wdtSwapOrderDetailMapper.saveOrUpdateBatch(wdtSwapOrderDetails);
        }
    }

    @Override
    public void saveOrUpdateBatchSpecifiedByRefundNo(String refundNo) {
        try {
            final RefundAPI api = wdtGateway.getQimenAPI(RefundAPI.class);
            final Pager pager = new Pager(1, 0, true);

            final RefundSearchParams params = new RefundSearchParams();
            params.setRefundNo(refundNo);
            saveOrUpdateByApiQueryResponse(api.search(params, pager));
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapFetchException(e, FetchDataType.WDT_REFUND_ORDER);
        }
    }
}
