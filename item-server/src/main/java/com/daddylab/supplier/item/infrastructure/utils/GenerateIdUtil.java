package com.daddylab.supplier.item.infrastructure.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.UUID;

/**
 * <AUTHOR> up
 * @date 2024年07月27日 3:01 PM
 */
public class GenerateIdUtil {

    /**
     * 定义字符集
     */
    private static final String CHAR_SET = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    public static String generateRandomString(String id, int length) {
        // 生成一个UUID并结合ID
        String combinedId = id + UUID.randomUUID();

        // 通过唯一ID生成哈希值
        byte[] hash = hashID(combinedId);

        // 使用哈希值生成随机字符串
        return generateStringFromHash(hash, length);
    }

    private static byte[] hashID(String id) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            return digest.digest(id.getBytes(StandardCharsets.UTF_8));
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256 algorithm not found.", e);
        }
    }

    private static String generateStringFromHash(byte[] hash, int length) {
        StringBuilder randomString = new StringBuilder(length);
        Base64.Encoder encoder = Base64.getUrlEncoder().withoutPadding();
        String encodedHash = encoder.encodeToString(hash);

        // 截取编码后的字符串以适应所需长度
        for (int i = 0; i < length; i++) {
            randomString.append(encodedHash.charAt(i % encodedHash.length()));
        }

        return randomString.toString();
    }

    public static void main(String[] args) {
        String id = "uniqueID12345";
        int length = 12;
        String randomString = generateRandomString(id, length);
        System.out.println("Generated String (12 chars): " + randomString);

        length = 8;
        randomString = generateRandomString(id, length);
        System.out.println("Generated String (8 chars): " + randomString);
    }

}
