package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockSyncRecord;

/**
 * <p>
 * 库存同步记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
public interface IStockSyncRecordService extends IDaddyService<StockSyncRecord> {

}
