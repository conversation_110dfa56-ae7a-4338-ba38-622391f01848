package com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.assembler;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeParseException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022/4/20
 */
@Mapper
public interface CommonAssembler {

    CommonAssembler INST = Mappers.getMapper(CommonAssembler.class);
    Logger log = LoggerFactory.getLogger(CommonAssembler.class);

    default LocalDateTime parseTime(String time) {
        try {
            return StrUtil.isBlank(time) ? null
                    : (time.matches("\\d{13}") ? DateUtil.millisToLocalDateTime(time)
                    : LocalDateTime.parse(time, DatePattern.NORM_DATETIME_FORMATTER));
        } catch (NumberFormatException | DateTimeParseException e) {
            log.error("日期格式解析出错 日期:{}", time, e);
            return null;
        }
    }

    default List<Long> longToLongList(Long value) {
        return value == null ? null : Collections.singletonList(value);
    }

    default Boolean integerToBoolean(Integer value) {
        return value == null ? null : !Objects.equals(value, 0);
    }

    default Integer booleanToInteger(Boolean value) {
        return value == null ? null : (value ? 1 : 0);
    }

    default Long integerToLong(Integer value) {
        return value == null ? null : value.longValue();
    }

    default Integer longToInteger(Long value) {
        return value == null ? null : value.intValue();
    }

    default String booleanToString(Boolean value) {
        return value == null ? null : (value ? "是" : "否");
    }

    default Boolean stringToBoolean(String value) {
        return "是".equals(value);
    }

}
