package com.daddylab.supplier.item.application.saleItem;

import lombok.Getter;

/**
 * 销售商品库错误码
 */
@Getter
public enum SaleItemErrorCode {

    IMPORT_SALE_ITEM_ERROR("1011", "当前导入数据存在错误/重复数据，请修正后再导入！"),
    IMPORT_SALE_ITEM_EXIST("1012", "当前导入的数据，系统已存在，是否覆盖？"),
    IMPORT_SALE_ITEM_HIATUS("1013", "必填项缺失,请检查后操作!"),

    ITEM_SKU_CODE_NULL("1014", "SKU编码为空"),
    ITEM_SKU_CODE_EXIST("1015", "商品编码已存在"),
    ITEM_NULL("1016", "商品不存在"),

    ;

    /**
     * 错误码
     */
    private String code;

    /**
     * 响应信息
     */
    private String msg;

    SaleItemErrorCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
