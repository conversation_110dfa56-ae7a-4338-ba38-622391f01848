package com.daddylab.supplier.item.application.payment.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年12月13日 5:02 PM
 */
@Data
public class RefreshRightAmountCmd {

    @NotNull
    private Integer detailSource;

    @NotNull
    private List<Long> chooseDetailIds;

    @NotNull
    private Long relatedOrderId;


    private Boolean advancePayment;



}
