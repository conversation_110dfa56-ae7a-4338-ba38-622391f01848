package com.daddylab.supplier.item.application.offShelf.dto;

import com.alibaba.cola.dto.Command;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OffShelfReasonType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OffShelfUrgentLevel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年11月04日 3:29 PM
 */
@Data
public class OffShelfFormCmd extends Command {

    private static final long serialVersionUID = -1509585581737535913L;

    private Long id;

    @ApiModelProperty(value = "紧急程度", notes = "1十分。2紧张。3一般")
    private OffShelfUrgentLevel urgentLevel;

    @ApiModelProperty(value = "下架理由", notes = "0店铺销量，1客诉问题，2检测不合格，3舆情问题，4商务合作问题，5其他")
    private List<OffShelfReasonType> reasonType;

    @ApiModelProperty("原因描述")
    private String reasonTxt;

    @ApiModelProperty("原因文件数组")
    private List<OffShelfFile> reasonFile;

    @ApiModelProperty("下架商品")
    @NotEmpty
    private List<OffShelfItemCmd> itemCmdList;

}
