package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.application.afterSales.AfterSalesSendOnInfoVO;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesInfoImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesSendOnInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesImageType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AfterSalesSendOnInfoMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesInfoImageService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesSendOnInfoService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWarehouseService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 售后异常转寄信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-17
 */
@Service
@AllArgsConstructor
public class AfterSalesSendOnInfoServiceImpl extends
        DaddyServiceImpl<AfterSalesSendOnInfoMapper, AfterSalesSendOnInfo> implements
        IAfterSalesSendOnInfoService {

    private final IAfterSalesInfoImageService afterSalesInfoImageService;
    private final IWarehouseService warehouseService;

    @Override
    public List<AfterSalesSendOnInfoVO> getAfterSalesSendOnInfoVOS(String refundOrderNo) {
        final List<AfterSalesSendOnInfo> afterSalesSendOnInfos = lambdaQuery().eq(
                AfterSalesSendOnInfo::getReturnOrderNo, refundOrderNo).list();
        if (afterSalesSendOnInfos.isEmpty()) {
            return Collections.emptyList();
        }

        final List<AfterSalesInfoImage> imagePOs = afterSalesInfoImageService.lambdaQuery()
                .in(AfterSalesInfoImage::getRelatedId,
                        afterSalesSendOnInfos.stream().map(AfterSalesSendOnInfo::getId).collect(
                                Collectors.toList()))
                .eq(AfterSalesInfoImage::getType,
                        AfterSalesImageType.SEND_ON).list();
        final Map<Long, List<String>> imageUrlsMap = imagePOs.stream().collect(
                Collectors.groupingBy(AfterSalesInfoImage::getRelatedId,
                        Collectors.mapping(AfterSalesInfoImage::getImage, Collectors.toList())));

        final List<String> fromWarehouseNos = afterSalesSendOnInfos.stream()
                .map(AfterSalesSendOnInfo::getFromWarehouseNo).collect(
                        Collectors.toList());
        final Map<String, String> warehouseNameMap = warehouseService.lambdaQuery()
                .in(Warehouse::getNo, fromWarehouseNos).list().stream().collect(
                        Collectors.toMap(Warehouse::getNo, Warehouse::getName));

        Set<String> duplicatesLogisticsSet = Sets.newHashSet();
        List<AfterSalesSendOnInfoVO> sendOnInfoVOs = Lists.newArrayList();
        for (AfterSalesSendOnInfo afterSalesSendOnInfo : afterSalesSendOnInfos) {
            final String logisticsNo = afterSalesSendOnInfo.getLogisticsNo();
            //当一个异常件被登记多次时，一键转寄会给每一条异常件记录生成转寄记录，体现在页面上是重复的数据，这边需要做一个过滤
            if (duplicatesLogisticsSet.contains(logisticsNo)) {
                continue;
            }
            duplicatesLogisticsSet.add(logisticsNo);
            final AfterSalesSendOnInfoVO afterSalesSendOnInfoVO = new AfterSalesSendOnInfoVO();
            afterSalesSendOnInfoVO.setSendOnTime(afterSalesSendOnInfo.getUpdatedAt());
            afterSalesSendOnInfoVO.setLogisticsName(afterSalesSendOnInfo.getLogisticsName());
            afterSalesSendOnInfoVO.setLogisticsNo(logisticsNo);
            afterSalesSendOnInfoVO.setWeight(afterSalesSendOnInfo.getWeight());
            afterSalesSendOnInfoVO.setRemark(afterSalesSendOnInfo.getRemark());
            afterSalesSendOnInfoVO.setSender(afterSalesSendOnInfo.getSender());
            afterSalesSendOnInfoVO.setSenderTel(afterSalesSendOnInfo.getSenderTel());
            afterSalesSendOnInfoVO.setSenderAddress(afterSalesSendOnInfo.getSenderAddress());
            afterSalesSendOnInfoVO.setSenderNo(afterSalesSendOnInfo.getSenderNo());
            afterSalesSendOnInfoVO.setFromWarehouseNo(afterSalesSendOnInfo.getFromWarehouseNo());
            afterSalesSendOnInfoVO.setFromWarehouse(
                    Optional.ofNullable(
                                    warehouseNameMap.get(afterSalesSendOnInfo.getFromWarehouseNo()))
                            .orElse("")
            );
            afterSalesSendOnInfoVO.setFromTel(afterSalesSendOnInfo.getFromTel());
            final List<String> images = Optional.ofNullable(
                            imageUrlsMap.get(afterSalesSendOnInfo.getId()))
                    .orElseGet(Collections::emptyList);
            afterSalesSendOnInfoVO.setImages(images);
            sendOnInfoVOs.add(afterSalesSendOnInfoVO);
        }
        return sendOnInfoVOs;
    }
}
