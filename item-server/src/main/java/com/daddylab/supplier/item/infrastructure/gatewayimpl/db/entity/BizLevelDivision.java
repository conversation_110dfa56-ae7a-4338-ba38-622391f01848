package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BizUnionTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 业务层级关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BizLevelDivision implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 是否删除
     */
    @TableLogic(value = "0", delval = "id")
    private Integer isDel;

    /**
     * 业务ID
     */
    private Long bizId;

    /**
     * 业务编码
     */
    private String bizCode;

    /**
     * 业务类型
     */
    private BizUnionTypeEnum type;

    /**
     * 层级
     */
    private DivisionLevelEnum level;

    /**
     * 层级值，具体看枚举值
     */
    private DivisionLevelValueEnum levelVal;

    /**
     * 父级ID，业务类型为SPU时才有值。
     */
    private Long parentId;


}
