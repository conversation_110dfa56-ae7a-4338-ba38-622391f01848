package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.MsgTemplate;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MsgTemplateCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.MsgTemplateMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IMsgTemplateService;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <p>
 * 消息模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Service
public class MsgTemplateServiceImpl extends DaddyServiceImpl<MsgTemplateMapper, MsgTemplate> implements IMsgTemplateService {

    @Override
    public Optional<MsgTemplate> getByCode(MsgTemplateCode code) {
        return lambdaQuery().eq(MsgTemplate::getCode, code).oneOpt();
    }
}
