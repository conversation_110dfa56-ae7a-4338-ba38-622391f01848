package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMarkImageContent;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 标注图片内容 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
public interface IItemDrawerMarkImageContentService extends IDaddyService<ItemDrawerMarkImageContent> {

}
