package com.daddylab.supplier.item.infrastructure.gatewayimpl.mall;

import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.controller.provider.dto.ProviderShopDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.ApiGatewayFeignClient;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;

/**
 * <AUTHOR>
 * @since 2024/8/5
 */
@Component
public class MallShopGatewayImpl implements MallShopGateway {
    @Autowired
    ApiGatewayFeignClient apiGatewayFeignClient;

    @Override
    public ProviderShopDetail queryShopDetail(Long mallShopId) {
        return queryShopDetail(Collections.singletonList(mallShopId)).stream().findFirst().orElse(null);
    }

    @Override
    public Collection<ProviderShopDetail> queryShopDetail(Collection<Long> mallShopIds) {
        final ArrayList<ProviderShopDetail> providerShopDetails = new ArrayList<>();
        for (Long mallShopId : mallShopIds) {
            final Rsp<ProviderShopDetail> providerShopDetailRsp = apiGatewayFeignClient.queryShopDetail(mallShopId);
            if (!providerShopDetailRsp.isSuccess()) {
                throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "查询小程序店铺信息异常");
            }
            providerShopDetails.add(providerShopDetailRsp.getData());
        }
        return Collections.unmodifiableCollection(providerShopDetails);
    }

}
