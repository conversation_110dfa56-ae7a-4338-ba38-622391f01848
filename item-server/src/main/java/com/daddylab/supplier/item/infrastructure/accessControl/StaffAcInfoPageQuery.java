package com.daddylab.supplier.item.infrastructure.accessControl;

import feign.Param;
import java.util.List;
import javax.annotation.Nonnull;
import lombok.Data;

@Data
public class StaffAcInfoPageQuery {
    @Nonnull
    @Param("page_index")
    private Integer pageIndex;

    @Nonnull
    @Param("page_size")
    private Integer pageSize;

    @Param("user_id")
    private Long userId;

    @Param("user_ids")
    private List<Long> userIds;

    @Param("post_id")
    private Long postId;

    @Param("role_id")
    private Long roleId;

    /**
     * 部门ID（请注意，不支持递归，如果需要查询所有下级部门，需要将下级部门ID一并传递）
     */
    @Param("dept_ids")
    private List<Long> deptIds;

    @Param("mobile")
    private String mobile;

    @Param("nickname")
    private String nickname;

    @Param("realname")
    private String realname;

    /**
     * 状态 0:离职；1:在职； -1:全部
     */
    @Param("status")
    private Integer status = -1;
}