package com.daddylab.supplier.item.domain.shop.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2024年03月18日 5:52 PM
 */
@Data
public class ShopWarehouseInventoryVo {

    @ApiModelProperty("仓库店铺配置Id")
    private Long id;

    @ApiModelProperty("仓库编码")
    private String warehouseNo;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("库存占比")
    private Integer inventoryRatio;

    @ApiModelProperty("仓内库存")
    private BigDecimal stockNum;

    @ApiModelProperty("可发库存")
    private BigDecimal sendStockNum;

    @ApiModelProperty("可用库存")
    private BigDecimal usableStockNum;
}
