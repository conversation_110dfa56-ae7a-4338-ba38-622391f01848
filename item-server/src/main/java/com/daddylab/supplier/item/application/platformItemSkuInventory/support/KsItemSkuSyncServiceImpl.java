package com.daddylab.supplier.item.application.platformItemSkuInventory.support;

import com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params.PlatformSkuSyncInfo;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.domain.query.KsItemPageQuery;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.impl.KuaiShouServiceFactory;
import com.kuaishou.merchant.open.api.domain.item.GetItemListResponseParam;
import com.kuaishou.merchant.open.api.domain.item.ShelfItemInfoResponseParam;
import com.kuaishou.merchant.open.api.domain.item.SkuInfoResponseParam;
import lombok.Data;
import lombok.NonNull;
import org.springframework.batch.core.Step;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @class KsItemSyncServiceImpl.java
 * @description 描述类的作用
 * @date 2024-03-14 09:30
 */
@Service
public class KsItemSkuSyncServiceImpl extends AbstractPlatformItemSkuSync<KsItemSkuSyncServiceImpl.ItemWrapper, List<PlatformSkuSyncInfo>> {

    @Autowired
    private KuaiShouServiceFactory kuaiShouServiceFactory;

    @Override
    public void fullDoseSync() {
        final ArrayList<Step> steps = new ArrayList<>();
        for (ShopAuthorization shopAuthorization : shopAuthorizationService.listNotExpiredAuthorizations(defaultType())) {
            final Step step = buildStep(getFullDoseItemReader(shopAuthorization));
            steps.add(step);
        }
        steps.add(getStatisticsStep());
        run(steps.toArray(new Step[0]));
    }


    public ItemReader<ItemWrapper> getFullDoseItemReader(ShopAuthorization shopAuthorization) {
        return getItemReader(iPage -> {
            KsItemPageQuery ksItemPageQuery = KsItemPageQuery.of((int) iPage.getCurrent(), (int) iPage.getSize());
            GetItemListResponseParam itemListResponses = kuaiShouServiceFactory.getService(shopAuthorization)
                                                                               .getItemList(ksItemPageQuery);
            if (itemListResponses == null || itemListResponses.getItems() == null || itemListResponses.getItems().length == 0) {
                return null;
            }
            iPage.setRecords(Arrays.stream(itemListResponses.getItems()).map(v -> {
                final ItemWrapper itemWrapper = new ItemWrapper();
                itemWrapper.setShopNo(shopAuthorization.getSn());
                itemWrapper.setItemInfo(v);

                return itemWrapper;
            }).collect(Collectors.toList()));
            iPage.setTotal(itemListResponses.getTotalItemCount());
            return iPage;
        }, 100);
    }

    @Override
    public ItemProcessor<ItemWrapper, List<PlatformSkuSyncInfo>> getItemProcess() {
        return this::convertPlatformSkuSyncInfos;
    }

    @NonNull
    private List<PlatformSkuSyncInfo> convertPlatformSkuSyncInfos(ItemWrapper itemWrapper) {
        final ShelfItemInfoResponseParam itemInfo = itemWrapper.getItemInfo();
        return Arrays.stream(itemInfo.getSkuList())
                     .map(skuInfo -> skuInfoResponseToPlatformItemSkuInventoryParams(itemWrapper.getShopNo(), itemInfo, skuInfo))
                     .collect(Collectors.toList());
    }

    @Override
    public ItemWriter<List<PlatformSkuSyncInfo>> getItemWriter() {
        return platformItemSkuInventoryParamLists -> {
            List<PlatformSkuSyncInfo> inventoryParams = platformItemSkuInventoryParamLists.stream()
                                                                                          .flatMap(Collection::stream)
                                                                                          .collect(Collectors.toList());
            saveSkuSyncInfo(inventoryParams);
        };
    }

    @Data
    public static class ItemWrapper {
        private String shopNo;
        private ShelfItemInfoResponseParam itemInfo;
    }

    PlatformSkuSyncInfo skuInfoResponseToPlatformItemSkuInventoryParams(String shopNo, ShelfItemInfoResponseParam itemInfo, SkuInfoResponseParam skuInfo) {
        PlatformSkuSyncInfo platformSkuSyncInfo = new PlatformSkuSyncInfo();
        platformSkuSyncInfo.setPlatform(defaultType());
        platformSkuSyncInfo.setShopNo(shopNo);
        platformSkuSyncInfo.setOuterSkuId(String.valueOf(skuInfo.getKwaiSkuId()));
        platformSkuSyncInfo.setOuterItemId(String.valueOf(skuInfo.getKwaiItemId()));
        platformSkuSyncInfo.setStock(skuInfo.getSkuStock());
        platformSkuSyncInfo.setPrice(BigDecimal.valueOf(skuInfo.getSkuSalePrice())
                                               .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        //上下架状态 0-下架 1-上架
        platformSkuSyncInfo.setStatus(itemInfo.getShelfStatus());
        platformSkuSyncInfo.setOuterSkuCode(skuInfo.getSkuNick());
        platformSkuSyncInfo.setOuterItemCode(null);
        if (itemInfo.getCreateTime() != null) {
            platformSkuSyncInfo.setItemCreateTime(itemInfo.getCreateTime() / 1000);
        }
        if (itemInfo.getUpdateTime() != null) {
            platformSkuSyncInfo.setItemUpdateTime(itemInfo.getUpdateTime() / 1000);
        }
        if (skuInfo.getCreateTime() != null) {
            platformSkuSyncInfo.setSkuCreateTime(skuInfo.getCreateTime() / 1000);
        }
        if (skuInfo.getUpdateTime() != null) {
            platformSkuSyncInfo.setSkuUpdateTime(skuInfo.getUpdateTime() / 1000);
        }
        platformSkuSyncInfo.setGoodsName(itemInfo.getTitle());
        platformSkuSyncInfo.setSpecName(skuInfo.getSpecification());
        platformSkuSyncInfo.setSkuNum(itemInfo.getSkuList().length);
        return platformSkuSyncInfo;
    }

    @Override
    public Platform defaultType() {
        return Platform.KUAISHOU;
    }

    @Override
    protected List<PlatformSkuSyncInfo> getSkuSyncInfos(String shopNo, String itemId) {
        KsItemPageQuery ksItemPageQuery = KsItemPageQuery.of(1, 10);
        ksItemPageQuery.setItemId(Long.valueOf(itemId));
        GetItemListResponseParam itemListResponses = kuaiShouServiceFactory.getService(shopNo).getItemList(ksItemPageQuery);
        final ItemProcessor<ItemWrapper, List<PlatformSkuSyncInfo>> itemProcess = getItemProcess();
        return Arrays.stream(itemListResponses.getItems())
                     .flatMap(v -> {
                         final ItemWrapper itemWrapper = new ItemWrapper();
                         itemWrapper.setShopNo(shopNo);
                         itemWrapper.setItemInfo(v);

                         return convertPlatformSkuSyncInfos(itemWrapper).stream();
                     }).collect(Collectors.toList());
    }

}
