package com.daddylab.supplier.item.application.itemOptimize;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.UtilException;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.net.url.UrlQuery;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.message.QyMsgSendUtils;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsPrincipalsInfo;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsVo;
import com.daddylab.supplier.item.common.ErrorChecker;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemCmd;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemVo;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.service.ItemDomainService;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.cache.Cache;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MsgTemplateCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemOptimizePlanService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemOptimizeService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemResp;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLock;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.itemOptimize.*;
import com.google.common.collect.ImmutableMap;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.ListValuedMap;
import org.apache.commons.collections4.MultiMapUtils;
import org.javers.core.diff.Diff;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.daddylab.supplier.item.common.GlobalConstant.ITEM_OPTIMIZE_PLAN_ALL;

/**
 * <AUTHOR>
 * @since 2023/10/20
 */
@Service
@RequiredArgsConstructor
public class ItemOptimizePlanBizServiceImpl implements ItemOptimizePlanBizService {
    private final IItemOptimizeService itemOptimizeService;
    private final IItemOptimizePlanService itemOptimizePlanService;
    private final IPlatformItemService platformItemService;
    private final OperateLogDomainService operateLogDomainService;
    private final ItemDomainService itemDomainService;
    private final NewGoodsBizService newGoodsBizService;
    private final ItemBizService itemBizService;
    private final ItemGateway itemGateway;

    @Autowired private ItemOptimizeBizService itemOptimizeBizService;
    @Autowired private RefreshConfig refreshConfig;
    @Autowired private MsgSender msgSender;

    @Override
    public PageResponse<ItemOptimizePlanBaseInfo> query(ItemOptimizePlanQuery query) {
        final List<Integer> userBusinessLines = query.getBusinessLine();
        if (userBusinessLines.isEmpty()) {
            return ResponseFactory.emptyPage();
        }
        final IPage<ItemOptimizePlan> page = query.getPage();
        final LambdaQueryChainWrapper<ItemOptimizePlan> queryChainWrapper =
                itemOptimizePlanService.lambdaQuery();
        queryChainWrapper
                .eq(query.getPlanId() != null, ItemOptimizePlan::getId, query.getPlanId())
                .eq(query.getPlatform() != null, ItemOptimizePlan::getPlatform, query.getPlatform())
                .eq(
                        StrUtil.isNotBlank(query.getPlanNo()),
                        ItemOptimizePlan::getPlanNo,
                        query.getPlanNo())
                .like(
                        StringUtil.isNotBlank(query.getPlanName()),
                        ItemOptimizePlan::getPlanName,
                        query.getPlanName())
                .eq(query.getStatus() != null, ItemOptimizePlan::getStatus, query.getStatus())
                .in(
                        !userBusinessLines.isEmpty(),
                        ItemOptimizePlan::getBusinessLine,
                        userBusinessLines);

        if (!UserPermissionJudge.hasApiPermission(ITEM_OPTIMIZE_PLAN_ALL)) {
            queryChainWrapper.eq(ItemOptimizePlan::getCreatedUid, UserContext.getUserId());
        }
        queryChainWrapper.orderByDesc(ItemOptimizePlan::getId);
        queryChainWrapper.page(page);

        return ResponseFactory.ofPage(
                page, ItemOptimizeAssembler.INST::itemOptimizePlanToItemOptimizePlanBaseInfo);
    }

    @Override
    public void recount(Long planId) {
        itemOptimizePlanService
                .lambdaUpdate()
                .eq(ItemOptimizePlan::getId, planId)
                .set(
                        ItemOptimizePlan::getItemNum,
                        itemOptimizeService
                                .lambdaQuery()
                                .eq(ItemOptimize::getPlanId, planId)
                                .count())
                .update();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Response delete(Long id) {
        // final ItemOptimizePlan plan = itemOptimizePlanService.getById(id);
        // if (plan.getStatus() == 1) {
        //    return Response.buildFailure(
        //            ErrorCode.OPERATION_REJECT.getCode(), "已提交的优化计划可编辑修改，不可删除");
        // }
        if (itemOptimizePlanService.removeByIdWithTime(id)) {
            final Set<Long> relatedOptimizeIds =
                    itemOptimizeService
                            .lambdaQuery()
                            .eq(ItemOptimize::getPlanId, id)
                            .select(ItemOptimize::getId)
                            .list()
                            .stream()
                            .map(ItemOptimize::getId)
                            .collect(Collectors.toSet());
            itemOptimizeService.removeByIdsWithTime(relatedOptimizeIds);
            operateLogDomainService.addOperatorLog(
                    UserContext.getUserId(), OperateLogTarget.ITEM_OPTIMIZE_PLAN, id, "删除商品优化计划");
        }
        return Response.buildSuccess();
    }

    @Override
    public SingleResponse<ItemOptimizePlanDetail> queryDetail(ItemOptimizePlanDetailQuery query) {
        final ItemOptimizePlan plan = itemOptimizePlanService.getById(query.getPlanId());
        Objects.requireNonNull(plan, "计划不存在");
        final ItemOptimizePlanBaseInfo itemOptimizePlanBaseInfo =
                ItemOptimizeAssembler.INST.itemOptimizePlanToItemOptimizePlanBaseInfo(plan);
        final ItemOptimizePlanDetail itemOptimizePlanDetail = new ItemOptimizePlanDetail();
        itemOptimizePlanDetail.setPlan(itemOptimizePlanBaseInfo);
        final List<ItemOptimize> itemOptimizes =
                itemOptimizeService.lambdaQuery().eq(ItemOptimize::getPlanId, plan.getId()).list();
        final List<ItemOptimizePlanItem> itemOptimizePlanItemList =
                ItemOptimizeAssembler.INST.itemOptimizeListToItemOptimizePlanItemList(
                        itemOptimizes);
        itemOptimizePlanDetail.setItems(itemOptimizePlanItemList);
        return SingleResponse.of(itemOptimizePlanDetail);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    @DistributedLock(
            searchKey = DistributedLock.SearchKeyStrategy.MULTI_PARAMETER_PROPERTY,
            msg = "请勿重复提交")
    public SingleResponse<Long> save(ItemOptimizePlanSaveCmd saveCmd) {
        ItemOptimizePlan plan;
        if (saveCmd.getId() == null) {
            plan = addPlan(saveCmd);
            saveCmd.getItems()
                    .forEach(
                            item -> {
                                item.setPlanId(plan.getId());
                                item.setPlatform(plan.getPlatform());
                            });
            final MultiResponse<ItemOptimize> saveItemsResponse =
                    itemOptimizeBizService.addBatch(saveCmd.getItems(), plan.getStatus());
            ErrorChecker.checkAndThrowIfError(saveItemsResponse);
            final List<ItemOptimize> saveItems = saveItemsResponse.getData();
            operateLogDomainService.addOperatorLog(
                    UserContext.getUserId(),
                    OperateLogTarget.ITEM_OPTIMIZE_PLAN,
                    plan.getId(),
                    formatLogForItemsModify(saveItems, Collections.emptyList()),
                    saveItems);
        } else {
            plan = update(saveCmd);
        }
        if (plan.getStatus() == 0 && saveCmd.isSubmit()) {
            submit(plan.getId());
        }
        return SingleResponse.of(plan.getId());
    }

    private void noticeAddItemsAfterSubmit(Long operatorId, ItemOptimizePlan plan, List<ItemOptimize> items) {
        noticeSubmit(operatorId, plan, items, true);
    }

    private void noticeSubmit(Long operatorId, ItemOptimizePlan plan) {
        List<ItemOptimize> items = itemOptimizeService.selectListByPlanId(plan.getId());
        noticeSubmit(operatorId, plan, items, false);
    }

    private void noticeSubmit(Long operatorId, ItemOptimizePlan plan, List<ItemOptimize> items, boolean isAdd) {
        final StaffBrief operator = StaffAssembler.INST.toStaffBrief(operatorId);
        final StaffBrief submitUser = StaffAssembler.INST.toStaffBrief(plan.getSubmitUid());
        final ListValuedMap<StaffBrief, ItemOptimize> msgGroup =
                MultiMapUtils.newListValuedHashMap();
        for (ItemOptimize item : items) {
            final List<StaffBrief> qcUsers = item.getData().getQcUsers();
            if (CollUtil.isNotEmpty(qcUsers)) {
                for (StaffBrief qcUser : qcUsers) {
                    msgGroup.put(qcUser, item);
                }
            }
        }
        for (Map.Entry<StaffBrief, Collection<ItemOptimize>> entry : msgGroup.asMap().entrySet()) {
            final WechatMsg wechatMsg = new WechatMsg();
            wechatMsg.setTitle(
                    String.format(
                            "%s 已提交 %s 优化计划，请查看后处理",
                            submitUser.getNickname(),
                            plan.getPlanName()));
            final Collection<ItemOptimize> itemOptimizes = entry.getValue();
            if (isAdd) {
                wechatMsg.setContent(String.format("共新增提交 %s 商品待审核", itemOptimizes.size()));
            } else {
                wechatMsg.setContent(String.format("共提交 %s 商品待审核", itemOptimizes.size()));
            }
            final StaffBrief qcUser = entry.getKey();
            final String url =
                    String.format(
                            refreshConfig.getDomain()
                                    + "/operation-management/optimize-item/list?qcId=%s&status=%s",
                            qcUser.getUserId(),
                            ItemOptimizeStatus.PENDING_QC_REVIEW.getValue());
            wechatMsg.setLink(url);
            wechatMsg.setRecipient(qcUser.getQwUserId());
            wechatMsg.setType(1);

//            msgSender.send(wechatMsg);
            final HashMap<String, Object> variables = new HashMap<>();
            variables.put("提交人花名", submitUser.getNickname());
            variables.put("商品优化计划名称", plan.getPlanName());
            variables.put("修改日期", "");
            variables.put("商品数量", itemOptimizes.size());
            variables.put("QC负责人", qcUser.getQwUserId());
            variables.put("status", ItemOptimizeStatus.PENDING_QC_REVIEW.getValue());
            QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.ITEM_OPTIMIZE_TO_QC, variables);
        }
    }

    private ItemOptimizePlan update(ItemOptimizePlanSaveCmd saveCmd) {
        final Long currentUid = saveCmd.getCurrentUid();
        ItemOptimizePlan plan;
        plan = itemOptimizePlanService.getById(saveCmd.getId());
        ItemOptimizePlan updateObj =
                ItemOptimizeAssembler.INST.itemOptimizePlanSaveCmdToItemOptimizePlan(saveCmd);
        final Diff diff = DiffUtil.diff(plan, updateObj);
        if (diff.hasChanges()) {
            operateLogDomainService.addOperatorLog(
                    UserContext.getUserId(),
                    OperateLogTarget.ITEM_OPTIMIZE_PLAN,
                    saveCmd.getId(),
                    DiffUtil.getSimpleDiffLog(diff, ItemOptimizePlan.class),
                    DiffUtil.toJson(diff));
            itemOptimizePlanService.updateById(updateObj);
        }
        if (CollUtil.isNotEmpty(saveCmd.getItems())) {
            final List<ItemOptimize> currentItems =
                    itemOptimizeService
                            .lambdaQuery()
                            .eq(ItemOptimize::getPlanId, plan.getId())
                            .list();
            final Map<Long, ItemOptimize> currentItemsMap =
                    currentItems.stream()
                            .collect(Collectors.toMap(ItemOptimize::getId, Function.identity()));
            final List<ItemOptimizePlanSaveCmdItem> cmdItems = saveCmd.getItems();
            final Set<Long> newItemIds =
                    cmdItems.stream()
                            .map(ItemOptimizePlanSaveCmdItem::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            final List<ItemOptimizePlanSaveCmdItem> addCmdItems =
                    cmdItems.stream()
                            .filter(item -> item.getId() == null)
                            .collect(Collectors.toList());

            final List<ItemOptimize> removeItems =
                    currentItems.stream()
                            .filter(v -> !newItemIds.contains(v.getId()))
                            .collect(Collectors.toList());
            List<ItemOptimize> addItems = Collections.emptyList();
            if (!addCmdItems.isEmpty()) {
                addCmdItems.forEach(
                        item -> {
                            item.setPlanId(plan.getId());
                            item.setPlatform(plan.getPlatform());
                        });
                final MultiResponse<ItemOptimize> addItemsResponse =
                        itemOptimizeBizService.addBatch(addCmdItems, plan.getStatus());
                ErrorChecker.checkAndThrowIfError(addItemsResponse);
                addItems = addItemsResponse.getData();
                if (plan.getStatus() == 1) {
                    noticeAddItemsAfterSubmit(saveCmd.getCurrentUid(), plan, addItems);
                }
            }
            if (!removeItems.isEmpty()) {
                for (ItemOptimize removeItem : removeItems) {
                    itemOptimizeBizService.delete(removeItem.getId());
                }
            }
            if (!addCmdItems.isEmpty() || !removeItems.isEmpty()) {
                operateLogDomainService.addOperatorLog(
                        UserContext.getUserId(),
                        OperateLogTarget.ITEM_OPTIMIZE_PLAN,
                        saveCmd.getId(),
                        formatLogForItemsModify(addItems, removeItems),
                        ImmutableMap.of("addItems", addCmdItems, "removeItems", removeItems));
            }
            final List<ItemOptimizePlanSaveCmdItem> updateItems =
                    cmdItems.stream()
                            .filter(
                                    cmdItem ->
                                            Optional.ofNullable(cmdItem.getId())
                                                    .map(currentItemsMap::get)
                                                    .filter(
                                                            itemOptimize -> {
                                                                ItemOptimizePlanSaveCmdItem
                                                                        cmdItemFromModel =
                                                                                ItemOptimizeAssembler
                                                                                        .INST
                                                                                        .itemOptimizeToItemOptimizeSaveCmdItem(
                                                                                                itemOptimize);
                                                                return !Objects.equals(
                                                                        cmdItem, cmdItemFromModel);
                                                            })
                                                    .isPresent())
                            .collect(Collectors.toList());
            if (!updateItems.isEmpty()) {
                if (!checkState(saveCmd.getId(), updateItems)) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.STATE_ERR);
                }

                for (ItemOptimizePlanSaveCmdItem updateItem : updateItems) {
                    final ItemOptimizeSaveCmd itemOptimizeSaveCmd = new ItemOptimizeSaveCmd();
                    itemOptimizeSaveCmd.setId(updateItem.getId());
                    itemOptimizeSaveCmd.setOuterItemId(updateItem.getOuterItemId());
                    itemOptimizeSaveCmd.setItemCode(updateItem.getItemCode());
                    itemOptimizeSaveCmd.setData(
                            ItemOptimizeAssembler.INST
                                    .itemOptimizePlanSaveCmdItemToItemOptimizeSaveCmdData(
                                            updateItem));
                    itemOptimizeSaveCmd.setV(updateItem.getV());
                    itemOptimizeBizService.save(itemOptimizeSaveCmd);
                }
                operateLogDomainService.addOperatorLog(
                        UserContext.getUserId(),
                        OperateLogTarget.ITEM_OPTIMIZE_PLAN,
                        plan.getId(),
                        String.format(
                                "修改了%s个（%s）商品",
                                updateItems.size(),
                                updateItems.stream()
                                        .map(ItemOptimizePlanSaveCmdItem::getOuterItemId)
                                        .collect(Collectors.joining(","))),
                        ImmutableMap.of("updateItems", updateItems));
            }
            recount(plan.getId());
        }
        return plan;
    }

    @NonNull
    private ItemOptimizePlan addPlan(ItemOptimizePlanSaveCmd saveCmd) {
        ItemOptimizePlan saveObj =
                ItemOptimizeAssembler.INST.itemOptimizePlanSaveCmdToItemOptimizePlan(saveCmd);
        final String planNo = generatePlanNo();
        saveObj.setPlanNo(planNo);
        saveObj.setItemNum(saveCmd.getItems().size());
        saveObj.setStatus(0);
        itemOptimizePlanService.save(saveObj);
        operateLogDomainService.addOperatorLog(
                UserContext.getUserId(),
                OperateLogTarget.ITEM_OPTIMIZE_PLAN,
                saveObj.getId(),
                "新增商品优化计划");
        return saveObj;
    }

    /**
     * 预检查更新项目数据状态是否发生变化
     *
     * @param id 计划ID
     * @param updateItems 更新项目
     * @return true 说明未发生变化
     */
    private boolean checkState(Long id, List<ItemOptimizePlanSaveCmdItem> updateItems) {
        final Map<Long, Integer> versions =
                itemOptimizeService
                        .lambdaQuery()
                        .eq(ItemOptimize::getPlanId, id)
                        .select(ItemOptimize::getId, ItemOptimize::getV)
                        .list()
                        .stream()
                        .collect(Collectors.toMap(ItemOptimize::getId, ItemOptimize::getV));
        return updateItems.stream()
                .allMatch(item -> versions.get(item.getId()).equals(item.getV()));
    }

    private void submit(Long id) {
        final ItemOptimizePlan plan = itemOptimizePlanService.getById(id);
        Assert.notNull(plan);
        final LambdaUpdateChainWrapper<ItemOptimizePlan> submitUpdateWrapper =
                itemOptimizePlanService.lambdaUpdate();
        plan.setSubmitUid(UserContext.getUserId());
        plan.setSubmitAt(DateUtil.currentSeconds());
        final boolean update =
                submitUpdateWrapper
                        .eq(ItemOptimizePlan::getId, id)
                        .eq(ItemOptimizePlan::getStatus, 0)
                        .set(ItemOptimizePlan::getStatus, 1)
                        .set(ItemOptimizePlan::getSubmitUid, plan.getSubmitUid())
                        .set(ItemOptimizePlan::getSubmitAt, plan.getSubmitAt())
                        .update();

        if (!update) {
            throw ExceptionPlusFactory.bizException(
                    ErrorCode.STATE_ERR, "商品优化计划提交失败，数据状态可能发生变更，请刷新后重试");
        }
        itemOptimizeBizService.planSubmit(id);
        operateLogDomainService.addOperatorLog(
                UserContext.getUserId(), OperateLogTarget.ITEM_OPTIMIZE_PLAN, id, "提交了商品优化计划");
        noticeSubmit(UserContext.getUserId(), plan);
    }

    private String formatLogForItemsModify(
            List<ItemOptimize> addItems, List<ItemOptimize> removeItems) {
        if (addItems.isEmpty() && removeItems.isEmpty()) {
            return "";
        }
        final String addItemFormatLog =
                addItems.stream()
                        .map(ItemOptimize::getOuterItemId)
                        .collect(Collectors.joining(","));
        final String removeItemFormatLog =
                removeItems.stream()
                        .map(ItemOptimize::getOuterItemId)
                        .collect(Collectors.joining(","));
        if (!addItems.isEmpty() && !removeItems.isEmpty()) {
            return String.format(
                    "添加了%s个（%s）商品，删除了%s个（%s）商品",
                    addItems.size(), addItemFormatLog, removeItems.size(), removeItemFormatLog);

        } else if (!addItems.isEmpty()) {
            return String.format("添加了%s个（%s）商品", addItems.size(), addItemFormatLog);
        } else {
            return String.format("删除了%s个（%s）商品", removeItems.size(), removeItemFormatLog);
        }
    }

    private static String generatePlanNo() {
        final String monthPart =
                DateUtil.format(LocalDateTime.now(), DatePattern.SIMPLE_MONTH_PATTERN);
        final String prefix = "SPYH" + monthPart;
        final Long increment = RedisUtil.increment(prefix, 1);
        return String.format("%s%02d", prefix, increment);
    }

    @Cache(keyPrefix = "itemOptimizeMatch", timeout = 600L)
    @Override
    public SingleResponse<ItemOptimizePlanItem> match(ItemOptimizePlanItemMatchQuery query) {
        String outerItemId = query.getId();
        if (StrUtil.isBlank(outerItemId)) {
            if (StrUtil.isNotBlank(query.getLink())) {
                outerItemId = recognizeLink(query.getLink());
            } else {
                throw new IllegalArgumentException("【平台商品ID】和【平台商品链接】不能同时为空");
            }
        }
        final List<PlatformItem> platformItemSkuList =
                platformItemService
                        .lambdaQuery()
                        .eq(PlatformItem::getOuterItemId, outerItemId)
                        .eq(PlatformItem::getPlatform, query.getPlatform())
                        .list();
        if (platformItemSkuList.isEmpty()) {
            return SingleResponse.of(null);
        }

        final PlatformItem platformItem =
                platformItemSkuList.stream()
                        .min(Comparator.comparing(PlatformItem::getId))
                        .orElseThrow(
                                () -> ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND));

        final ItemOptimizePlanItem itemOptimizePlanItem = new ItemOptimizePlanItem();
        itemOptimizePlanItem.setId(null);
        itemOptimizePlanItem.setPlanId(null);
        itemOptimizePlanItem.setOuterItemId(outerItemId);
        itemOptimizePlanItem.setPlatformItemId(platformItem.getId());
        itemOptimizePlanItem.setItemId(platformItem.getItemId());

        final ItemOptimizePersistData data = new ItemOptimizePersistData();
        // 平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城
        switch (query.getPlatform()) {
            case 1:
                data.setLink("https://item.taobao.com/item.htm?id=" + outerItemId);
                break;
            case 3:
                data.setLink(
                        String.format(
                                "https://haohuo.jinritemai.com/ecommerce/trade/detail/index.html?id=%s&origin_type=604",
                                outerItemId));
                break;
            case 4:
                data.setLink(
                        String.format(
                                "https://s.kwaixiaodian.com/zone/goods/config/release/detail?itemId=%s",
                                outerItemId));
                break;
            case 7:
                data.setLink(
                        String.format(
                                "https://myseller.laobashop.com/h5preview?src=https://cms.daddylab.com/activity/product_details_preview?id=%s",
                                outerItemId));
                break;
        }

        data.setLinkTitle(platformItem.getGoodsName());
        data.setCanBeSupplementedHotSearchKeywords("");

        final Optional<Item> itemUserSpecified =
                Optional.ofNullable(query.getItemCode())
                        .filter(StrUtil::isNotBlank)
                        .map(itemGateway::getItem);
        final Optional<Item> itemMatchByPlatform =
                Optional.ofNullable(platformItem.getItemId())
                        .filter(NumberUtil::isPositive)
                        .map(itemGateway::getItem);

        final Optional<Item> matchItemOptional =
                Stream.of(itemUserSpecified, itemMatchByPlatform)
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .findFirst();
        final Optional<PartnerItemResp> partnerItemRespOptional =
                matchItemOptional
                        .map(Item::getPartnerProviderItemSn)
                        .filter(StringUtil::isNotBlank)
                        .flatMap(itemGateway::partnerQueryByCode);

        data.setPsysType(partnerItemRespOptional.map(PartnerItemResp::getType).orElse(0));

        final Optional<NewGoodsPrincipalsInfo> newGoodsPrincipalsInfoOptional =
                matchItemOptional
                        .map(Entity::getId)
                        .map(newGoodsBizService::getNewGoodsPrincipalsInfo)
                        .filter(response -> response.getData() != null)
                        .map(SingleResponse::getData);
        if (newGoodsPrincipalsInfoOptional.isPresent()) {
            final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo =
                    newGoodsPrincipalsInfoOptional.get();
            data.setBuyerUsers(newGoodsPrincipalsInfo.getBuyerUsers());
            data.setQcUsers(newGoodsPrincipalsInfo.getQcUsers());
            data.setLegalUsers(newGoodsPrincipalsInfo.getLegalUsers());
        } else if (partnerItemRespOptional.isPresent()) {
            final PartnerItemResp partnerItemResp = partnerItemRespOptional.get();
            data.setBuyerUsers(
                    Optional.ofNullable(partnerItemResp.getPurchaseId())
                            .filter(NumberUtil::isPositive)
                            .map(Integer::longValue)
                            .map(StaffAssembler.INST::toStaffBrief)
                            .map(Collections::singletonList)
                            .orElseGet(Collections::emptyList));
            data.setQcUsers(
                    Optional.ofNullable(partnerItemResp.getQcIds())
                            .map(StaffAssembler.INST::longListToStaffBriefList)
                            .orElseGet(Collections::emptyList));
            data.setLegalUsers(Collections.emptyList());
        }

        data.setModifyUsers(Collections.emptyList());
        data.setModifyTime(0L);

        itemOptimizePlanItem.setItemCode(matchItemOptional.map(Item::getCode).orElse(""));
        itemOptimizePlanItem.setItemId(matchItemOptional.map(Item::getId).orElse(0L));
        itemOptimizePlanItem.setData(data);
        itemOptimizePlanItem.setStatus(null);
        itemOptimizePlanItem.setV(null);

        return SingleResponse.of(itemOptimizePlanItem);
    }

    private static String recognizeLink(String link) {
        String id = null;
        try {
            final String filteredLink = StrUtil.prependIfMissing(link, "https://", "http://");
            final URI uri = URLUtil.toURI(filteredLink);
            final UrlQuery urlQuery = UrlQuery.of(uri.getQuery(), StandardCharsets.UTF_8);
            for (String key : Collections.singletonList("id")) {
                final CharSequence value = urlQuery.get(key);
                if (value != null) {
                    id = value.toString();
                    break;
                }
            }
            if (StrUtil.isBlank(id)) {
                throw new IllegalArgumentException("无法识别的链接格式");
            }
        } catch (UtilException e) {
            throw new IllegalArgumentException("链接格式不正确");
        }
        return id;
    }

    @NonNull
    private static List<StaffBrief> getSingleUserFromNewGoodsVo(
            Optional<NewGoodsVo> newGoodsVoOptional, Function<NewGoodsVo, DadStaffVO> userGetter) {
        return newGoodsVoOptional
                .map(userGetter)
                .map(StaffAssembler.INST::dadStaffVoToStaffBrief)
                .map(Collections::singletonList)
                .orElseGet(Collections::emptyList);
    }

    @NonNull
    private static List<StaffBrief> getUsersFromNewGoodsVo(
            Optional<NewGoodsVo> newGoodsVoOptional,
            Function<NewGoodsVo, List<DadStaffVO>> userGetter) {
        return newGoodsVoOptional
                .map(userGetter)
                .map(StaffAssembler.INST::dadStaffVoListToStaffBriefList)
                .orElseGet(Collections::emptyList);
    }

    private Optional<PartnerItemVo> queryPsysItem(String psysItemNo) {
        final PartnerItemCmd cmd = new PartnerItemCmd();
        cmd.setSearchType(1);
        cmd.setContext(psysItemNo);
        cmd.setPageIndex(1);
        cmd.setPageSize(10);
        final List<PartnerItemVo> partnerItemVos = itemDomainService.queryPartnerItem(cmd);
        return Optional.ofNullable(partnerItemVos).flatMap(v -> v.stream().findFirst());
    }
}
