package com.daddylab.supplier.item.application.payment.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年12月05日 4:43 PM
 */
@Data
public class PaymentExportDto {

  @ExcelProperty("付款单号")
  private String no;

  @ExcelProperty("申请日期")
  private String applyTimeStr;

  @ExcelProperty("应付金额")
  private String rightAmount;

  @ExcelProperty("申请付款金额")
  private BigDecimal applyAmount;

  @ExcelProperty("其他金额")
  private BigDecimal otherChargeback;

  @ExcelProperty("实付金额")
  private BigDecimal realPayAmount;

  @ExcelProperty("采购组织")
  private String purchaseOrg;

  @ExcelProperty("付款组织")
  private String payOrgStr;

  @ExcelProperty("收款单位")
  private String payeeUnitStr;

  @ExcelProperty("采购员")
  private String buyerName;

  // --------

  @ExcelProperty("付款用途")
  private String payPurpose;

  @ExcelProperty("币别")
  private String currencyType;

  @ExcelProperty("采购类型")
  private String purchaseType;

  @ExcelProperty("合作方")
  private String businessLine;

  @ExcelProperty("付款状态")
  private String status;

  @ExcelProperty("采购单号/结算单号")
  private String relateNo;

  @ExcelProperty("申请人")
  private String applyUser;

  @ExcelProperty("申请时间")
  private String applyTimeStr2;
}
