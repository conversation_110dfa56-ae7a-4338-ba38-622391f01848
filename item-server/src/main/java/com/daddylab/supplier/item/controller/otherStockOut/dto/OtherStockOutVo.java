package com.daddylab.supplier.item.controller.otherStockOut.dto;

import com.daddylab.supplier.item.controller.otherStockOutDetail.dto.OtherStockOutDetailVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OtherStockOutVo.java
 * @description
 * @createTime 2022年04月01日 16:28:00
 */
@Data
@ApiModel("其他出库单返回实体")
public class OtherStockOutVo implements Serializable {

    private static final long serialVersionUID = 8888594643622960735L;

    @ApiModelProperty(value = "出库单id")
    private Long id;

    @ApiModelProperty(value = "出库单号")
    private String orderNo;

    @ApiModelProperty(value = "仓库 1:全部 2:袋鼠仓（海销） 3:金诺仓")
    private Integer warehouse;

    @ApiModelProperty(value = "仓库编号")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "状态 0:全部 1:待处理 2:已取消 3:未确认 4:待审核 5:拣货中 6:已完成")
    private Integer status;

    @ApiModelProperty(value = "其他出库原因 0:全部 1:无 2:退货少件出库 3:退件破损出库 4:工厂虚拟出库 5:线下下单 6:客服需求 7:调拨出库 8:临期出库 9:破损出库")
    private Integer otherReason;

    @ApiModelProperty(value = "其他入库原因名称")
    private String otherReasonName;

    @ApiModelProperty(value = "货品数量")
    private Integer itemCount;

    @ApiModelProperty(value = "货品种类数")
    private Integer kindCount;

    @ApiModelProperty(value = "物流公司")
    private Integer logisticsId;

    @ApiModelProperty(value = "物流单号")
    private String logisticsNo;

    @ApiModelProperty(value = "创建人")
    private String createdUid;

    @ApiModelProperty(value = "制单人")
    private String operatorName;

    @ApiModelProperty(value = "创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "邮费")
    private BigDecimal freight;

    @ApiModelProperty(value = "发货时间")
    private Long deliveryAt;

    @ApiModelProperty(value = "其他出库单详情")
    private List<OtherStockOutDetailVo> stockOutDetailVos;

    private Integer businessLine;

}
