package com.daddylab.supplier.item.application.afterSales;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesConfirmUndertakeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/21
 */
@Data
@ApiModel("销退确认命令模型")
public class AfterSalesConfirmCmd {

    /**
     * 退换单号
     */
    @ApiModelProperty("退换单号")
    @NotBlank(message = "退换单号不能为空")
    private String returnOrderNo;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 销退确认明细
     */
    @ApiModelProperty("销退确认明细")
    @NotEmpty(message = "销退确认明细不能为空")
    private List<Detail> details;

    @ApiModelProperty("强制销退确认")
    private Boolean forceConfirm = false;

    @Data
    @ApiModel("销退确认命令明细")
    public static class Detail {

        /**
         * 退换单商品明细ID
         */
        @ApiModelProperty("退换单商品明细ID（取退换单详情/退回商品明细/商品明细ID）")
        @NotNull(message = "退换单商品明细ID不能为空")
        private Long refundOrderItemId;

        /**
         * 承担类型 1:按百分比 2:按金额
         */
        @ApiModelProperty("承担类型 PERCENTAGE 按百分比 AMOUNT 按金额")
        @NotNull(message = "承担类型不能为空")
        private AfterSalesConfirmUndertakeType undertakeType;

        /**
         * 承担金额/占比（按金额时单位为元）
         */
        @ApiModelProperty("承担金额/占比（按金额时单位为元）")
        @NotNull(message = "承担金额/占比不能为空")
        @Positive(message = "承担金额/占比不能小于等于0")
        private BigDecimal undertakeAmount;
    }
}
