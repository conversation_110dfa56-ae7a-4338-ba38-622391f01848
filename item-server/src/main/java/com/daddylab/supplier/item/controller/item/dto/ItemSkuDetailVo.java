package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/9/1 09:28
 * @Description: 请描述下这个类
 */
@Data
@ApiModel("商品规格详情ItemSkuDetailVo")
public class ItemSkuDetailVo implements Serializable {
    private static final long serialVersionUID = -1030515343189102461L;

    @ApiModelProperty(value = "商品ID")
    private Long itemId;
    @ApiModelProperty(value = "商品编码")
    private String itemNo;
    @ApiModelProperty(value = "商品标准名")
    private String itemStandardName;
    @ApiModelProperty(value = "商品淘宝链接")
    private String taobaoLink;
    @ApiModelProperty(value = "商品抖音链接")
    private String douyinLink;
    @ApiModelProperty(value = "商品小程序链接")
    private String miniProgramLink;
    @ApiModelProperty(value = "规格详情")
    private List<SkuDetail> skuDetails;

    @ApiModel("ItemSkuDetailVoSkuDetail规格详情")
    @Data
    public static class SkuDetail implements Serializable {
        private static final long serialVersionUID = -1;

        @ApiModelProperty(value = "规格编码")
        private String itemSkuNo;

        @ApiModelProperty(value = "规格ID")
        private Long itemSkuId;

        @ApiModelProperty(value = "规格名称")
        private String itemSkuName;

        @ApiModelProperty(value = "日销价")
        private BigDecimal dailyPrice;

        @ApiModelProperty(value = "活动价")
        private BigDecimal activePrice;

        @ApiModelProperty(value = "采购成本")
        private BigDecimal costPrice;
    }
}
