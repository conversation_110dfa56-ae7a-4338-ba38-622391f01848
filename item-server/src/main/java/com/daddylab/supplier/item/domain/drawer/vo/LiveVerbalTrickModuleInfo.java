package com.daddylab.supplier.item.domain.drawer.vo;

import com.daddylab.supplier.item.types.itemDrawer.ItemDrawerModuleModifyAdviceVO;
import com.daddylab.supplier.item.types.recognitionTask.ItemDrawerRecognitionResults;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/12/30
 */
@Data
public class LiveVerbalTrickModuleInfo {

    @ApiModelProperty(name = "直播话术状态", value = "0:未进入审核流程/未知 5:待法务审核 10:待QC审核 100:审核完成")
    private Integer liveVerbalTrickStatus;

    @ApiModelProperty("直播话术识别结果（可能为 NULL）")
    private ItemDrawerRecognitionResults recognitionResults;

    @ApiModelProperty("审批意见")
    private List<ItemDrawerModuleModifyAdviceVO> advices;

    @ApiModelProperty("历史审批意见")
    private List<ItemDrawerModuleModifyAdviceVO> hisAdvices;

}
