package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.application.afterSaleLogistics.dto.LogisticExceptionRes;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.AbnormalStatus;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.AbnormalityLogType;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.ErpLogisticsStatus;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.CloseReasonType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderLogisticsAbnormalityMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsAbnormalityLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsAbnormalityService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.Optional;
import lombok.NonNull;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 订单物流异常记录 服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Service
public class OrderLogisticsAbnormalityServiceImpl
    extends DaddyServiceImpl<OrderLogisticsAbnormalityMapper, OrderLogisticsAbnormality>
    implements IOrderLogisticsAbnormalityService {

  @Autowired private IOrderLogisticsAbnormalityLogService logService;
  @Autowired private IWdtOrderService wdtOrderService;
  @Autowired private RedissonClient redissonClient;

  @Override
  public Optional<OrderLogisticsAbnormality> getByTraceId(Long traceId) {
    return lambdaQuery()
        .eq(OrderLogisticsAbnormality::getTraceId, traceId)
        .orderByDesc(OrderLogisticsAbnormality::getId)
        .last("limit 1")
        .oneOpt();
  }

  @Override
  public Optional<OrderLogisticsAbnormality> getActivateByTraceId(Long traceId) {
    return lambdaQuery()
        .eq(OrderLogisticsAbnormality::getActivateWarning, 1)
        .eq(OrderLogisticsAbnormality::getTraceId, traceId)
        .oneOpt();
  }

  @Override
  public OrderLogisticsAbnormality addAbnormality(
      OrderLogisticsTrace orderLogisticsTrace,
      AbnormalStatus abnormalityStatus,
      ErpLogisticsStatus logisticsStatus,
      LogisticExceptionRes logisticExceptionResult) {
    OrderLogisticsAbnormality orderLogisticsAbnormality = new OrderLogisticsAbnormality();
    orderLogisticsAbnormality.setAbnormalStatus(abnormalityStatus);
    copyProperties(orderLogisticsAbnormality, orderLogisticsTrace);
    orderLogisticsAbnormality.setLogisticsStatus(logisticsStatus);
    orderLogisticsAbnormality.setHandleCount(0);
    orderLogisticsAbnormality.setRemark(StringUtil.EMPTY);
    orderLogisticsAbnormality.setAbnormalType(
        logisticExceptionResult.getRootException().getValue());
    orderLogisticsAbnormality.setAbnormalType2(
        logisticExceptionResult.getSubException().getValue());
    copyOrderProperties(orderLogisticsTrace, orderLogisticsAbnormality);

    save(orderLogisticsAbnormality);
    return orderLogisticsAbnormality;
  }

  private void copyOrderProperties(
      OrderLogisticsTrace orderLogisticsTrace,
      OrderLogisticsAbnormality orderLogisticsAbnormality) {
    final Optional<WdtOrder> wdtOrder =
        wdtOrderService.getByTradeId(orderLogisticsTrace.getWdtTradeId());
    wdtOrder.ifPresent(
        wo -> {
          if (NumberUtil.isZeroOrNull(orderLogisticsAbnormality.getConsignTime())
              && wo.getConsignTime() != null) {
            orderLogisticsAbnormality.setConsignTime(DateUtil.toTime(wo.getConsignTime()));
          }
          orderLogisticsAbnormality.setTradeTime(DateUtil.toTime(wo.getTradeTime()));
          orderLogisticsAbnormality.setOrderAmount(wo.getReceivable());
        });
  }

  private void copyProperties(
      OrderLogisticsAbnormality orderLogisticsAbnormality,
      OrderLogisticsTrace orderLogisticsTrace) {
    orderLogisticsAbnormality.setTraceId(orderLogisticsTrace.getId());
    orderLogisticsAbnormality.setPlatform(orderLogisticsTrace.getPlatform());
    orderLogisticsAbnormality.setLogisticsCompanyName(orderLogisticsTrace.getLogisticsName());
    orderLogisticsAbnormality.setLogisticsNo(orderLogisticsTrace.getLogisticsNo());
    orderLogisticsAbnormality.setStockoutWarehouseNo(orderLogisticsTrace.getStockoutWarehouseNo());
    orderLogisticsAbnormality.setStockoutNo(orderLogisticsTrace.getStockoutNo());
    orderLogisticsAbnormality.setSrcOrderNo(orderLogisticsTrace.getSrcOrderNo());
    orderLogisticsAbnormality.setCallbackId(orderLogisticsTrace.getCallbackId());
    orderLogisticsAbnormality.setTraceSource(orderLogisticsTrace.getTraceSource());
    orderLogisticsAbnormality.setWdtTradeId(orderLogisticsTrace.getWdtTradeId());
    orderLogisticsAbnormality.setPayTime(orderLogisticsTrace.getPayTime());
    orderLogisticsAbnormality.setConsignTime(orderLogisticsTrace.getConsignTime());
    orderLogisticsAbnormality.setShopId(orderLogisticsTrace.getShopId());
  }

  @Override
  public OrderLogisticsAbnormality updateAbnormality(
      OrderLogisticsAbnormality orderLogisticsAbnormality,
      OrderLogisticsTrace orderLogisticsTrace,
      AbnormalStatus abnormalityStatus,
      ErpLogisticsStatus logisticsStatus,
      LogisticExceptionRes logisticExceptionResult) {
    orderLogisticsAbnormality.setPlatform(orderLogisticsTrace.getPlatform());
    orderLogisticsAbnormality.setLogisticsCompanyName(orderLogisticsTrace.getLogisticsName());
    orderLogisticsAbnormality.setLogisticsNo(orderLogisticsTrace.getLogisticsNo());
    orderLogisticsAbnormality.setAbnormalStatus(abnormalityStatus);
    orderLogisticsAbnormality.setStockoutWarehouseNo(orderLogisticsTrace.getStockoutWarehouseNo());
    orderLogisticsAbnormality.setStockoutNo(orderLogisticsTrace.getStockoutNo());
    orderLogisticsAbnormality.setSrcOrderNo(orderLogisticsTrace.getSrcOrderNo());
    if (Objects.nonNull(logisticsStatus)) {
      orderLogisticsAbnormality.setLogisticsStatus(logisticsStatus);
    }
    orderLogisticsAbnormality.setCallbackId(orderLogisticsTrace.getCallbackId());
    orderLogisticsAbnormality.setTraceSource(orderLogisticsTrace.getTraceSource());
    orderLogisticsAbnormality.setAbnormalType(
        logisticExceptionResult.getRootException().getValue());
    orderLogisticsAbnormality.setAbnormalType2(
        logisticExceptionResult.getSubException().getValue());
    updateById(orderLogisticsAbnormality);
    return orderLogisticsAbnormality;
  }

  @Override
  public Long addAbnormalityLog(Long abnormalityId, LogisticExceptionRes exceptionResult) {
    unsetLast(abnormalityId);
    final OrderLogisticsAbnormalityLog orderLogisticsAbnormalityLog =
        buildExceptionLog(abnormalityId, exceptionResult);
    logService.save(orderLogisticsAbnormalityLog);
    return orderLogisticsAbnormalityLog.getId();
  }

  private @NonNull OrderLogisticsAbnormalityLog buildExceptionLog(
      Long abnormalityId, LogisticExceptionRes exceptionResult) {
    OrderLogisticsAbnormalityLog orderLogisticsAbnormalityLog = new OrderLogisticsAbnormalityLog();
    orderLogisticsAbnormalityLog.setLast(1);
    orderLogisticsAbnormalityLog.setAbnormalityId(abnormalityId);
    orderLogisticsAbnormalityLog.setLogType(AbnormalityLogType.EXCEPTION);
    orderLogisticsAbnormalityLog.setAbnormalType(exceptionResult.getRootException().getValue());
    orderLogisticsAbnormalityLog.setAbnormalType2(exceptionResult.getSubException().getValue());
    orderLogisticsAbnormalityLog.setMsg(exceptionResult.getSubException().getDesc());
    return orderLogisticsAbnormalityLog;
  }

  @Override
  public void addAbnormalitySysLog(OrderLogisticsAbnormality abnormality, String msg) {
    OrderLogisticsAbnormalityLog abnormalityLog = new OrderLogisticsAbnormalityLog();
    abnormalityLog.setAbnormalityId(abnormality.getId());
    abnormalityLog.setLogType(AbnormalityLogType.SYSTEM.getValue());
    abnormalityLog.setMsg(msg);
    logService.save(abnormalityLog);
  }

  @Override
  public Optional<OrderLogisticsAbnormalityLog> getLog(Long logId) {
    return Optional.ofNullable(logService.getById(logId));
  }

  @Override
  public void closeAbnormal(
      OrderLogisticsAbnormality abnormality,
      OrderLogisticsTrace orderLogisticsTrace,
      CloseReasonType closeReasonType) {
    if (abnormality.getAbnormalStatus() == AbnormalStatus.CLOSED) {
      return;
    }
    copyProperties(abnormality, orderLogisticsTrace);
    abnormality.setAbnormalStatus(0);
    updateById(abnormality);

    addAbnormalitySysLog(abnormality, closeReasonType.getDesc());

    LocalDate today = LocalDate.now(ZoneId.systemDefault());
    String dayStr = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    String key = "logistic_" + abnormality.getStockoutWarehouseNo() + "_" + dayStr;
    RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
    if (Objects.nonNull(atomicLong)) {
      final long l = atomicLong.decrementAndGet();
      if (l <= 0) {
        redissonClient.getKeys().delete(key);
      }
    }
  }

  @Override
  public void closeAbnormal(
      OrderLogisticsTrace orderLogisticsTrace, CloseReasonType closeReasonType) {
    getByTraceId(orderLogisticsTrace.getId())
        .ifPresent(abnormality -> closeAbnormal(abnormality, orderLogisticsTrace, closeReasonType));
  }

  private void unsetLast(Long abnormalId) {
    logService
        .lambdaUpdate()
        .set(OrderLogisticsAbnormalityLog::getLast, 0)
        .eq(OrderLogisticsAbnormalityLog::getAbnormalityId, abnormalId)
        .update();
  }
}
