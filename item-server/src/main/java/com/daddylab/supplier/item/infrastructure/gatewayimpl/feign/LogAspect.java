package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Log;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ILogService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/24 4:44 下午 com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerFeignClient
 * @description
 */
@Slf4j
@Aspect
@Component
public class LogAspect {

    @Autowired
    private ILogService ilogService;

    private final String pointcutFeign = "@annotation(com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.FeignLog)";

    @Pointcut(value = pointcutFeign)
    public void logFeign() {

    }

    @Around(value = "logFeign()")
    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        final MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
        final Integer type = signature.getMethod().getAnnotation(FeignLog.class).type();
        String error = "";
        Object proceed = new Object();
        try {
            return proceed = proceedingJoinPoint.proceed();
        } catch (Throwable e) {
            error = e.getMessage();
            throw e;
        } finally {
            Log log = new Log();
            log.setReq(JsonUtil.toJson(proceedingJoinPoint.getArgs()));
            log.setResp(JsonUtil.toJson(proceed));
            log.setType(type);
            log.setError(error);
            ilogService.save(log);
        }
    }
}
