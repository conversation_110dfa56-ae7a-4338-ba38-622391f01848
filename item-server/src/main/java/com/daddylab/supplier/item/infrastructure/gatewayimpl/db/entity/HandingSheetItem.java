package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 盘货表关联的商品
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class HandingSheetItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 盘货表ID
     */
    private Long handingSheetId;

    /**
     * 商品编码
     */
    private String itemNo;

    /**
     * 商品ID
     */
    private Long itemId;

    /**
     * 商品规格编码
     */
    private String itemSkuNo;

    /**
     * 商品规格ID
     */
    private Long itemSkuId;

    /**
     * 赠品机制
     */
    private String giftDescription;

    /**
     * 赠品编码
     */
    private String giftCode;

    /**
     * 淘宝链接
     */
    private String taobaoLink;

    /**
     * 抖音链接
     */
    private String douyinLink;

    /**
     * 小程序链接
     */
    private String miniProgramLink;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否审核通过
     */
    @TableField("is_passed")
    private Boolean passed;

    /**
     * 活动价
     */
    private BigDecimal activePrice;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人uid
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人uid
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除：0否1是
     */
    @TableLogic
    private Integer isDel;


}
