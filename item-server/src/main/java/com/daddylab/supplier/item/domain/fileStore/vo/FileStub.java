package com.daddylab.supplier.item.domain.fileStore.vo;

import cn.hutool.core.util.URLUtil;
import com.daddylab.supplier.item.infrastructure.utils.FileUtil;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@ToString
@EqualsAndHashCode
@Getter
public class FileStub {
    /**
     * 文件url
     */
    protected final String url;
    /**
     * 文件格式
     */
    protected final String mime;
    /**
     * 大小(M)
     */
    protected final Integer size;
    /**
     * 名称
     */
    protected final String name;

    protected Long fileId;

    public static FileStubBuilder<?, ?> builder(String url) {
        return new FileStubBuilderImpl().url(url);
    }

    public String getSuffix() {
        return FileUtil.getSuffix(name);
    }

    public String getPath() {
        return URLUtil.getPath(url);
    }

    public void setFileId(Long id){
        this.fileId = id;
    }


}
