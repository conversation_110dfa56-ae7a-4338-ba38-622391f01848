package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 旺店通退货入库单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtRefundStockInOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 入库单号
     */
    private String orderNo;

    /**
     * 入库单状态 10:已取消；20:编辑中；30:待处理/待审核；80:已完成
     */
    private Integer status;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 入库单创建时间（毫秒级时间戳， 例如：1631861379000 ）
     */
    private String createdTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 分销商昵称
     */
    private String fenxiaoNick;

    /**
     * 退换建单人
     */
    private String operatorName;

    /**
     * 物流方式，点击查看 物流代码表
     */
    private Integer logisticsType;

    /**
     * 物流公司名称
     */
    private String logisticsName;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 物流公司唯一键
     */
    private Integer logisticsId;

    /**
     * 审核时间（毫秒级时间戳， 例如：1631861379000 ）
     */
    private LocalDateTime checkTime;

    /**
     * 退换单号
     */
    private String refundNo;

    /**
     * 货品数量
     */
    private BigDecimal goodsCount;

    /**
     * 退换单实际退款金额
     */
    private BigDecimal actualRefundAmount;

    /**
     * 客户编码
     */
    private String customerNo;

    /**
     * 退货人姓名 （ 仅自有平台 及线下平台返回，其他平台均不返回 ）
     */
    private String customerName;

    /**
     * 客户网名 （ 仅自有平台 及线下平台返回，其他平台均不返回 ）
     */
    private String nickName;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺编号
     */
    private String shopNo;

    /**
     * 店铺备注
     */
    private String shopRemark;

    /**
     * 标记名称
     */
    private String flagName;

    /**
     * 系统订单编号，若为多个以“，”分割
     */
    private String tradeNoList;

    /**
     * 原始单号
     */
    private String tidList;

    /**
     * 退换单主键
     */
    private Integer srcOrderId;

    /**
     * 入库单id
     */
    private Integer stockinId;

    /**
     * 店铺平台id，点击 文档 查看平台id对照表
     */
    private Integer shopPlatformId;

    /**
     * 店铺唯一键
     */
    private Integer shopId;

    /**
     * 仓库唯一键
     */
    private Integer warehouseId;

    /**
     * 入库单总成本 子单明细total_cost的总和
     */
    private BigDecimal totalPrice;

    /**
     * 退货入库单修改时间,样例:2020-04-23 14:55:08
     */
    private LocalDateTime modified;

    @TableField(exist = false)
    List<WdtRefundStockInOrderDetails> detailsList;


}
