package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderPageQuery;
import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderPageVO;
import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderViewVO;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PurchaseOrderState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockInState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 采购订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Service
@RequiredArgsConstructor
public class PurchaseOrderServiceImpl extends DaddyServiceImpl<PurchaseOrderMapper, PurchaseOrder> implements IPurchaseOrderService {

    final PurchaseOrderMapper purchaseOrderMapper;

    final IStockInOrderService iStockInOrderService;

    final IPurchasePayableOrderService iPurchasePayableOrderService;
    final IStockOutOrderService iStockOutOrderService;

    final IPurchaseOrderDetailService iPurchaseOrderDetailService;

    @Override
    public List<PurchaseOrderPageVO> page(PurchaseOrderPageQuery pageQuery) {
        return purchaseOrderMapper.page(pageQuery);
    }

    @Override
    public Integer getNewestStockInState(Long purchaseId) {
        LambdaQueryWrapper<StockInOrder> objectLambdaQueryWrapper = Wrappers.lambdaQuery();
        objectLambdaQueryWrapper.eq(StockInOrder::getPurchaseOrderId, purchaseId).orderByDesc(StockInOrder::getUpdatedAt);
        List<StockInOrder> list = iStockInOrderService.list(objectLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return StockInState.WAIT_SUBMIT.getValue();
        } else {
            return list.get(0).getState();
        }
    }

    @Override
    public PurchaseOrderViewVO getView(Long id) {
        Assert.notNull(id);
        return purchaseOrderMapper.getView(id);
    }

    @Override
    public List<String> getStockInOrderNoByState(Long purchaseOrderId, List<Integer> stockInOrderStateList) {
        LambdaQueryWrapper<StockInOrder> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StockInOrder::getPurchaseOrderId, purchaseOrderId).in(StockInOrder::getState, stockInOrderStateList);
        List<StockInOrder> list = iStockInOrderService.list(wrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().map(StockInOrder::getNo).collect(Collectors.toList());
        }
        return new LinkedList<>();
    }

    @Override
    public void deleteWithNo(String no) {
        LambdaQueryWrapper<PurchaseOrder> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PurchaseOrder::getNo, no);
        this.removeWithTime(wrapper);
    }

    @Override
    public boolean isOwnOrder(Long purchaseOrderId) {
        PurchaseOrder purchaseOrder = this.getById(purchaseOrderId);
        return purchaseOrder.getCreatedUid().equals(UserContext.getUserId());
    }

    @Override
    public Optional<PurchaseOrder> getLatestCode(String code) {
        return this.lambdaQuery()
                .select(PurchaseOrder::getNo)
                .like(PurchaseOrder::getNo, code)
//                .eq(PurchaseOrder::getIsDel,0)
                .orderByDesc(PurchaseOrder::getId).last("limit 1")
                .oneOpt();
    }

    @Override
    public PurchaseOrder getByNo(String no) {
        return lambdaQuery().eq(PurchaseOrder::getNo, no).one();
    }


    @Override
    public void updateStatus(Collection<String> nos, PurchaseOrderState state) {
        this.lambdaUpdate().set(PurchaseOrder::getState, state).in(PurchaseOrder::getNo, nos).update();
    }

//    @Override
//    public void writeOffOrders(Collection<String> nos) {
//        List<Long> stockInOrderIds = iStockInOrderService.lambdaQuery().in(StockInOrder::getPurchaseOrderNo, nos).select(StockInOrder::getId)
//                .list().stream().map(StockInOrder::getId).collect(Collectors.toList());
//        if (CollUtil.isNotEmpty(stockInOrderIds)) {
//            iStockInOrderService.lambdaUpdate().set(StockInOrder::getState, StockInState.WRITE_OFF.getValue()).in(StockInOrder::getId, stockInOrderIds).update();
//            iPurchasePayableOrderService.lambdaUpdate().set(PurchasePayableOrder::getIsHedge, 1)
//                    .eq(PurchasePayableOrder::getType, 1).in(PurchasePayableOrder::getRelatedOrderId, stockInOrderIds).update();
//        }
//        List<Long> stockOutOrderIds = iStockOutOrderService.lambdaQuery().in(StockOutOrder::getPurchaseOrderNo, nos).select(StockOutOrder::getId)
//                .list().stream().map(StockOutOrder::getId).collect(Collectors.toList());
//        if (CollUtil.isNotEmpty(stockOutOrderIds)) {
//            iStockOutOrderService.lambdaUpdate().set(StockOutOrder::getState, StockOutOrderState.WRITE_OFF.getValue()).in(StockOutOrder::getId, stockOutOrderIds).update();
//            iPurchasePayableOrderService.lambdaUpdate().set(PurchasePayableOrder::getIsHedge, 1)
//                    .eq(PurchasePayableOrder::getType, 2).in(PurchasePayableOrder::getRelatedOrderId, stockOutOrderIds).update();
//        }
//    }

    @Override
    public void deleteRelatedOrders(String purchaseOrderNo) {
        List<Long> stockInOrderIds = iStockInOrderService.lambdaQuery().eq(StockInOrder::getPurchaseOrderNo, purchaseOrderNo).select(StockInOrder::getId)
                .list().stream().map(StockInOrder::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(stockInOrderIds)) {
            iStockInOrderService.removeByIdsWithTime(stockInOrderIds);
            iPurchasePayableOrderService.lambdaUpdate()
                    .eq(PurchasePayableOrder::getType, 1)
                    .in(PurchasePayableOrder::getRelatedOrderId, stockInOrderIds).remove();
        }
        List<Long> stockOutOrderIds = iStockOutOrderService.lambdaQuery().eq(StockOutOrder::getPurchaseOrderNo, purchaseOrderNo).select(StockOutOrder::getId)
                .list().stream().map(StockOutOrder::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(stockOutOrderIds)) {
            iStockOutOrderService.removeByIdsWithTime(stockOutOrderIds);
            iPurchasePayableOrderService.lambdaUpdate()
                    .eq(PurchasePayableOrder::getType, 2)
                    .in(PurchasePayableOrder::getRelatedOrderId, stockOutOrderIds)
                    .remove();
        }
    }

    @Override
    public String getOverrideNo(String oldNo) {
        List<String> collect = this.lambdaQuery().likeRight(PurchaseOrder::getNo, oldNo)
                .select(PurchaseOrder::getNo).orderByDesc(PurchaseOrder::getId).list()
                .stream().map(PurchaseOrder::getNo).collect(Collectors.toList());
        String s = collect.get(0);
        String s1 = s.replaceAll(oldNo, "");
        if (StrUtil.isBlank(s1)) {
            return oldNo + "01";
        } else {
            long l = Long.parseLong(s1) + 1;
            return oldNo + l;
        }
    }

    @Override
    public String getWarehouseNo(Long purchaseOrderId) {
        List<PurchaseOrderDetail> list = iPurchaseOrderDetailService.lambdaQuery().eq(PurchaseOrderDetail::getPurchaseOrderId, purchaseOrderId)
                .last("limit 1").list();
        return CollUtil.isNotEmpty(list) ? list.get(0).getWarehouseNo() : StrUtil.EMPTY;
    }

    @Override
    public List<PurchaseOrder> listBySkuCode(String skuNo) {
        return getBaseMapper().listBySkuCode(skuNo);
    }
}
