package com.daddylab.supplier.item.infrastructure.utils;

import static java.util.Objects.requireNonNull;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import org.reflections.Reflections;

/**
 * <AUTHOR>
 */
public class ReflectionsUtil {

    @Nullable
    private static Reflections reflections = null;

    @Nonnull
    public static Reflections getReflections() {
        if (reflections == null) {
            reflections = ApplicationContextUtil.getBean(Reflections.class);
        }
        return reflections;
    }

    /**
     * Converts the primitive type to its corresponding wrapper.
     *
     * @param clazz Class that will be evaluated
     * @return if the parameter is a primitive type returns its wrapper; otherwise returns the same
     * class
     */
    public static Class<?> convertPrimitiveType(Class<?> clazz) {
        if (clazz.equals(char.class)) {
            return Character.class;
        } else if (clazz.equals(int.class)) {
            return Integer.class;
        } else if (clazz.equals(boolean.class)) {
            return Boolean.class;
        } else if (clazz.equals(double.class)) {
            return Double.class;
        } else if (clazz.equals(byte.class)) {
            return Byte.class;
        } else if (clazz.equals(short.class)) {
            return Short.class;
        } else if (clazz.equals(long.class)) {
            return Long.class;
        } else if (clazz.equals(float.class)) {
            return Float.class;
        } else {
            return clazz;
        }
    }

    /**
     * 兼容原始类的返回
     *
     * @param className 类名 class
     */
    public static Class<?> forName(String className) throws ClassNotFoundException {
        requireNonNull(className, "类名不能为空");
        switch (className) {
            case "char":
                return char.class;
            case "int":
                return int.class;
            case "boolean":
                return boolean.class;
            case "double":
                return double.class;
            case "byte":
                return byte.class;
            case "short":
                return short.class;
            case "long":
                return long.class;
            case "float":
                return float.class;
            default:
                return Class.forName(className);
        }
    }


}
