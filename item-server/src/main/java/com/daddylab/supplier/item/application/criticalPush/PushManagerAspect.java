package com.daddylab.supplier.item.application.criticalPush;

import cn.hutool.core.lang.func.Func;
import cn.hutool.core.util.ReflectUtil;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/4/25
 */
@Aspect
@Slf4j
@Component
public class PushManagerAspect implements Serializable, ApplicationContextAware {

    private static final long serialVersionUID = 1L;

    private static final ThreadLocal<Boolean> callRealMethodThreadLocal = ThreadLocal.withInitial(
            () -> false);
    private final AsyncCriticalPushManager asyncPushManager;
    private final CriticalPushManager pushManager;
    ApplicationContext applicationContext;

    public PushManagerAspect(AsyncCriticalPushManager asyncPushManager,
            CriticalPushManager pushManager) {
        this.asyncPushManager = asyncPushManager;
        this.pushManager = pushManager;
    }

    public static void nextCallRealMethod() {
        callRealMethodThreadLocal.set(true);
    }

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext)
            throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Pointcut("@annotation(annotation)")
    public void pushManagerPointCut(ManagedPush annotation) {
    }

    @Around(value = "pushManagerPointCut(annotation)", argNames = "pjp,annotation")
    public Object around(ProceedingJoinPoint pjp, ManagedPush annotation) throws Throwable {
        if (callRealMethodThreadLocal.get()) {
            log.info("调用被切的真实方法 方法:{} 参数:{}", pjp.getSignature().toShortString(), pjp.getArgs());
            try {
                return pjp.proceed(pjp.getArgs());
            } finally {
                callRealMethodThreadLocal.set(false);
            }
        } else {
            final MethodSignature signature = (MethodSignature) pjp.getSignature();
            final Object[] args = pjp.getArgs();
            Object idArg = args[annotation.idArgIndex()];
            final String idArgProp = annotation.idArgProp();
            if (!idArgProp.isEmpty()) {
                idArg = ReflectUtil.getFieldValue(idArg, idArgProp);
            }
            if (idArg == null) {
                throw new CriticalPushException("业务ID不能为空！");
            }
            if (!(idArg instanceof Long)) {
                if (idArg instanceof Integer) {
                    idArg = (long) ((Integer) idArg);
                } else {
                    throw new CriticalPushException("ID参数应该是Long或者Integer类型");
                }
            }
            if (annotation.async()) {
                final MethodInvocationDescriptor methodInvocationDescriptor = new MethodInvocationDescriptor(
                        applicationContext,
                        pjp.getTarget(), signature.getMethod(), args);
                asyncPushManager
                        .push(annotation.sourceType(), (long) idArg, annotation.targetType(),
                                methodInvocationDescriptor);
                return null;
            } else {
                try {
                    AtomicReference<Object> result = new AtomicReference<>();
                    pushManager.push(annotation.sourceType(), (long) idArg, annotation.targetType(),
                            (VarargsFunctionCallback<Object, Object>) delegate -> {
                                delegate.call((Func<Object, Object>) parameters -> {
                                    try {
                                        result.set(pjp.proceed(pjp.getArgs()));
                                        return result.get();
                                    } catch (ClassNotFoundException | NoSuchMethodException | InstantiationException | IllegalAccessException e) {
                                        throw new RuntimeException(
                                                "目标方法反射异常:" + signature.toShortString(), e);
                                    } catch (InvocationTargetException | RuntimeException e) {
                                        Throwable t = e;
                                        if (e instanceof InvocationTargetException) {
                                            t = ((InvocationTargetException) t).getTargetException();
                                        }
                                        throw ExceptionUtil.wrapRuntime(t);
                                    } catch (Throwable e) {
                                        throw new RuntimeException("目标方法执行异常:" + e.getMessage(), e);
                                    }
                                }, args);
                            }, null);
                    return result.get();
                } catch (CriticalPushException e) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR,
                            Optional.of(e)
                                    .map(Throwable::getMessage)
                                    .filter(StringUtil::isNotBlank)
                                    .orElse("无明确异常信息返回"));
                }
            }
        }
    }
}
