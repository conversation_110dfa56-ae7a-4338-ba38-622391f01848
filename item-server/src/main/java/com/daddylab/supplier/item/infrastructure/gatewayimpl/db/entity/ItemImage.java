package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemImageType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.Value;

import java.io.Serializable;

/**
 * <p>
 * 商品图片
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false, of = "imageUrl")
@Value
public class ItemImage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品ID
     */
    @DiffIgnore
    private Long itemId;

    /**
     * 商品图片
     */
    private String imageUrl;

    /**
     * 图片类型 1:普通商品图片 2:运营商品图片
     */
    private ItemImageType type;

    /**
     * 是否主图
     */
    private Integer isMain;

    /**
     * 图片排序
     */
    @DiffIgnore
    private Integer sort;

    /**
     * 删除时间
     */
    @DiffIgnore
    private Long deletedAt;

    /**
     * 创建时间createAt
     */
    @DiffIgnore
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @DiffIgnore
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @DiffIgnore
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @DiffIgnore
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @DiffIgnore
    @TableLogic
    private Integer isDel;






}
