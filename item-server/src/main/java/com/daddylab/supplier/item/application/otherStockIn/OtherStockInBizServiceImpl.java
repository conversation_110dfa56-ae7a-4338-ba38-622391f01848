package com.daddylab.supplier.item.application.otherStockIn;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.application.otherStockInDetail.OtherStockInDetailBizService;
import com.daddylab.supplier.item.application.reason.ReasonBizService;
import com.daddylab.supplier.item.application.warehouse.WarehouseGateway;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.OtherStockInDetailTransMapper;
import com.daddylab.supplier.item.common.trans.OtherStockInTransMapper;
import com.daddylab.supplier.item.controller.otherStockIn.dto.OtherStockInQueryPage;
import com.daddylab.supplier.item.controller.otherStockIn.dto.OtherStockInVo;
import com.daddylab.supplier.item.controller.otherStockInDetail.dto.OtherStockInDetailVo;
import com.daddylab.supplier.item.domain.brand.entity.BrandEntity;
import com.daddylab.supplier.item.domain.brand.gateway.BrandGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOtherStockInService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @ClassName OtherStockInServiceImpl.java
 * @description
 * @createTime 2022年03月30日 15:55:00
 */
@Slf4j
@Service
public class OtherStockInBizServiceImpl implements OtherStockInBizService {

    @Autowired
    private ItemSkuGateway itemSkuGateway;
    @Autowired
    private OtherStockInDetailBizService otherStockInDetailBizService;
    @Autowired
    private ItemGateway itemGateway;
    @Autowired
    private IOtherStockInService iOtherStockInService;
    @Autowired
    private BrandGateway brandGateway;
    @Autowired
    private WarehouseGateway warehouseGateway;
    @Autowired
    private ReasonBizService reasonBizService;


    @Override
    public PageResponse<OtherStockInVo> queryPage(OtherStockInQueryPage queryPage) {
        queryPage.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(queryPage.getBusinessLine()));
        if (CollUtil.isEmpty(queryPage.getBusinessLine())) {
            return PageResponse.of(new ArrayList<>(), 0, queryPage.getPageSize(), queryPage.getPageIndex());
        }

        List<Long> orderIds = otherStockInDetailBizService.getIdsByItem(queryPage.getItemSku(), queryPage.getItemCode(),
                queryPage.getItemId(), queryPage.getBrandId(), queryPage.getBusinessLine());
        if (CollUtil.isEmpty(orderIds)) {
            return PageResponse.of(queryPage.getPageSize(), queryPage.getPageIndex());
        }
        LambdaQueryWrapper<OtherStockIn> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(StringUtils.isNotBlank(queryPage.getOrderNo()), OtherStockIn::getOrderNo, queryPage.getOrderNo())
                .eq(StringUtils.isNotBlank(queryPage.getWarehouseNo()), OtherStockIn::getWarehouseNo, queryPage.getWarehouseNo())
                .in(CollectionUtils.isNotEmpty(orderIds), OtherStockIn::getId, orderIds)
                .ge(Objects.nonNull(queryPage.getStartTime()), OtherStockIn::getCreatedAt, queryPage.getStartTime())
                .le(Objects.nonNull(queryPage.getEndTime()), OtherStockIn::getCreatedAt, queryPage.getEndTime());
        if (Objects.nonNull(queryPage.getStatus()) && !Objects.equals(queryPage.getStatus(), OtherStockInState.ALL.getValue())) {
            queryWrapper.eq(OtherStockIn::getStatus, queryPage.getStatus());
        }

        queryWrapper.eq(Objects.nonNull(queryPage.getOtherReason()), OtherStockIn::getOtherReason, queryPage.getOtherReason());
        queryWrapper.orderByDesc(OtherStockIn::getCreatedAt);
        Page<OtherStockIn> pageReq = new Page<>(queryPage.getPageIndex(), queryPage.getPageSize());
        Page<OtherStockIn> page = iOtherStockInService.page(pageReq, queryWrapper);

        List<OtherStockInVo> otherStockInVos = OtherStockInTransMapper.INSTANCE.dbToVos(page.getRecords());
        for (OtherStockInVo otherStockInVo : otherStockInVos) {
            //货品数量和种类数
            List<OtherStockInDetail> byOrderId = otherStockInDetailBizService.getByOrderId(otherStockInVo.getId());
            Integer total = byOrderId.stream().mapToInt(OtherStockInDetail::getCount).sum();
            otherStockInVo.setItemCount(total);
            otherStockInVo.setKindCount(byOrderId.size());
            //仓库名称
            Optional<Warehouse> warehouse = warehouseGateway.getWarehouse(otherStockInVo.getWarehouseNo());
            warehouse.ifPresent(value -> otherStockInVo.setWarehouseName(value.getName()));
            Reason reason = reasonBizService.getById(Long.valueOf(otherStockInVo.getOtherReason()));
            if (Objects.nonNull(reason)) {
                otherStockInVo.setOtherReasonName(reason.getReason());
            }
        }
        return PageResponse.of(otherStockInVos, (int) page.getTotal(), queryPage.getPageSize(), queryPage.getPageIndex());
    }

    @Override
    public SingleResponse<OtherStockInVo> getById(Long id) {
        OtherStockIn otherStockIn = iOtherStockInService.getById(id);
        if (Objects.isNull(otherStockIn)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查不到该记录");
        }
        OtherStockInVo otherStockInVo = OtherStockInTransMapper.INSTANCE.dbToVo(otherStockIn);
        List<OtherStockInDetail> detailList = otherStockInDetailBizService.getByOrderId(id);
        Integer total = detailList.stream().mapToInt(OtherStockInDetail::getCount).sum();
        otherStockInVo.setItemCount(total);
        otherStockInVo.setKindCount(detailList.size());
        otherStockInVo.setAuditUid("系统");
        Reason reason = reasonBizService.getById(Long.valueOf(otherStockInVo.getOtherReason()));
        //仓库名称
        Optional<Warehouse> warehouse = warehouseGateway.getWarehouse(otherStockInVo.getWarehouseNo());
        warehouse.ifPresent(value -> otherStockInVo.setWarehouseName(value.getName()));
        if (Objects.nonNull(reason)) {
            otherStockInVo.setOtherReasonName(reason.getReason());
        }
        List<OtherStockInDetailVo> otherStockInDetailVos = OtherStockInDetailTransMapper.INSTANCE.otherDbToVos(detailList);
        for (OtherStockInDetailVo otherStockInDetail : otherStockInDetailVos) {
            Item item = itemGateway.getItem(otherStockInDetail.getItemId());
            if (Objects.isNull(item)) {
                continue;
            }
            otherStockInDetail.setItemName(item.getName());
            otherStockInDetail.setItemCode(item.getCode());
            Optional<ItemSku> byItemSkuId = itemSkuGateway.getByItemSkuId(otherStockInDetail.getSkuId());
            byItemSkuId.ifPresent(itemSku -> otherStockInDetail.setSkuName(itemSku.getSkuCode()));
            //获取规格名称
            if (byItemSkuId.isPresent()) {
                String skuAttrListStr = itemSkuGateway.getSkuAttrListStr(byItemSkuId.get().getSkuCode());
                if (Objects.nonNull(skuAttrListStr)) {
                    otherStockInDetail.setSkuAttrRefDOS(skuAttrListStr);
                }
            }
            BrandEntity brand = brandGateway.getBrand(otherStockInDetail.getBrandId());
            if (Objects.nonNull(brand)) {
                otherStockInDetail.setBrandName(brand.getName());
            }
        }

        otherStockInVo.setStockInDetailVos(otherStockInDetailVos);
        return SingleResponse.of(otherStockInVo);
    }


    private boolean check(Long sku, String code, Long itemId, Long brandId) {
        if (Objects.nonNull(sku) || StringUtils.isNotBlank(code) || Objects.nonNull(itemId) || Objects.nonNull(brandId)) {
            return true;
        } else {
            return false;
        }
    }


}
