package com.daddylab.supplier.item.infrastructure.gatewayimpl.provider;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.provider.event.ProviderChangeEvent;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.ProviderTransMapper;
import com.daddylab.supplier.item.controller.provider.dto.PartnerProviderPageQuery;
import com.daddylab.supplier.item.controller.provider.dto.ProviderShopDetail;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuCodeProviderDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ProviderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IProviderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseOrderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.utils.LogicDeleteUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.ApiGatewayFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerOpenFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.GetPartnerListReq;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.GetPartnersByItemNoReq;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.PartnerContacts;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderReq;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/30 3:06 下午
 * @description
 */
@Slf4j
@Service
public class ProviderGatewayImpl implements ProviderGateway {

    @Autowired
    IProviderService iProviderService;

    @Autowired
    PartnerFeignClient partnerFeignClient;

    @Autowired
    PartnerOpenFeignClient partnerOpenFeignClient;

    @Autowired
    IItemService iItemService;

    @Autowired
    ProviderMapper providerMapper;

    @Autowired
    IPurchaseOrderService iPurchaseOrderService;

    @Autowired
    ApiGatewayFeignClient apiGatewayFeignClient;

    @Override
    public Provider getById(Long providerId) {
        return iProviderService.getById(providerId);
    }

    @Override
    public boolean allExist(List<Long> providerIds) {
        return iProviderService.lambdaQuery().in(Provider::getId, providerIds).count() > 0;
    }

    @Override
    public List<PartnerProviderResp> partnerQuery(PartnerProviderPageQuery partnerPageQuery) {
        log.info("查询合作伙伴系统供应商信息，param:{}", JsonUtil.objToStr(partnerPageQuery));

        try {
            final PartnerProviderReq req = ProviderTransMapper.INSTANCE.partnerQueryToReq(partnerPageQuery);
            final String s = partnerFeignClient.providerQuery(req);
            JSONObject jsonObject = JSONObject.parseObject(s);
            final Boolean flag = jsonObject.getBoolean("flag");
            if (Boolean.FALSE.equals(flag)) {
                throw new RuntimeException(jsonObject.getString("msg"));
            }
            final Object data = jsonObject.get("data");
            if (Objects.nonNull(data)) {
                final List<PartnerProviderResp> partnerProviderList = JSONArray.parseArray(data.toString(), PartnerProviderResp.class);
                if (CollUtil.isNotEmpty(partnerProviderList)) {
                    return partnerProviderList;
                }
            }
            return new LinkedList<>();
        } catch (Exception e) {
            log.error("查询合作伙伴系统供应商信息异常", e);
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "查询合作伙伴系统供应商信息异常," + e.getMessage());
        }

    }

    @Override
    public Map<Long, PartnerProviderResp> partnerBatchQueryByIds(Collection<Long> partnerProviderIds) {
        final List<Long> ids =
                partnerProviderIds.stream()
                        .filter(id -> id != null && id > 0)
                        .distinct()
                        .collect(Collectors.toList());
        if (ids.isEmpty()) {
            return Collections.emptyMap();
        }
        final PartnerProviderPageQuery partnerPageQuery = new PartnerProviderPageQuery();
        partnerPageQuery.setPartnerProviderIds(ids);
        partnerPageQuery.setPageIndex(1);
        partnerPageQuery.setPageSize(ids.size() + 10);

        final List<PartnerProviderResp> partnerProviderResps = partnerQuery(partnerPageQuery);
        return partnerProviderResps.stream()
                .collect(Collectors.toMap(v -> (long) v.getId(), Function.identity()));
    }

    @Override
    public Optional<PartnerProviderResp> partnerQueryByProviderId(Long providerId) {
        return Optional.ofNullable(getById(providerId))
                .map(Provider::getPartnerProviderId)
                .flatMap(this::partnerQueryById);
    }

    @Override
    public List<Provider> queryBatchByIds(List<Long> providerIds) {
        if (CollUtil.isEmpty(providerIds)) {
            return Collections.emptyList();
        }
        return iProviderService.listByIds(providerIds);
    }

    @Override
    public PartnerProviderResp partnerQuery(String partnerToken) {
        log.info("根据P系统token查询合作伙伴系统供应商信息，token:{}", partnerToken);

        PartnerProviderResp resp;
        try {
            resp = partnerOpenFeignClient.providerQueryByToken(partnerToken).getData();
        } catch (Exception e) {
            log.error("根据P系统token查询合作伙伴系统供应商异常", e);
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "查询合作伙伴系统供应商信息异常," + e.getMessage());
        }
        if (Objects.isNull(resp)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "查询合作伙伴系统供应商信息为空,token:" + partnerToken);
        }
        return resp;
    }

    @Override
    public Provider getOne(String name, String code) {
        QueryWrapper<Provider> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Provider::getName, name).eq(Provider::getUnifySocialCreditCodes, code);
        return iProviderService.getOne(queryWrapper);
    }

    @Override
    public Provider getByNo(String no) {
        QueryWrapper<Provider> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Provider::getProviderNo, no);
        return iProviderService.getOne(queryWrapper);
    }

    @Override
    public Boolean isRepeat(String name) {
        QueryWrapper<Provider> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Provider::getName, name);
        int count = iProviderService.count(queryWrapper);
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public Long saveOrUpdate(Provider provider) {
        Assert.hasText(provider.getName(), "供应商名字不得为空");
        Assert.isTrue(provider.getName().length() <= 50, "供应商名字长度必须小于50");
        Assert.notNull(provider.getType(), "供应商类型不得为空");


        if (NumberUtil.isPositive(provider.getId())) {
            iProviderService.updateById(provider);
            return provider.getId();
        }

        QueryWrapper<Provider> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Provider::getName, provider.getName());
        Assert.isTrue(iProviderService.count(queryWrapper) <= 0, "供应商名字不得重复");
        iProviderService.saveOrUpdate(provider);

        return iProviderService.getOne(queryWrapper).getId();
    }

    @Override
    public Boolean canRemove(Long id) {
        QueryWrapper<Item> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Item::getProviderId, id).last("LIMIT 1");
        int count = iItemService.count(queryWrapper);

        QueryWrapper<PurchaseOrder> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(PurchaseOrder::getProviderId, id);
        int count1 = iPurchaseOrderService.count(wrapper);

        return count == 0 && count1 == 0;

    }

    @Override
    public void setKingDeeIdAndNo(Long id, String kingDeeId) {
        LambdaUpdateWrapper<Provider> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Provider::getKingDeeId, kingDeeId).eq(Provider::getId, id);
        iProviderService.update(updateWrapper);
    }

    @Override
    public void removeKingDeeId(Long id) {
        LambdaUpdateWrapper<Provider> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Provider::getKingDeeId, null).eq(Provider::getId, id);
        iProviderService.update(updateWrapper);
    }

    @Override
    public List<SkuCodeProviderDO> getIdBySkuCode(List<String> skuCodeList) {
        return providerMapper.getListBySkuCodeList(skuCodeList);
    }

    @Override
    public void remove(Long id) {
        final Provider provider = iProviderService.getById(id);
        if (Objects.isNull(provider)) return;

        iProviderService.removeByIdWithTime(id);

        EventBusUtil.post(ProviderChangeEvent.ofRemove(UserContext.getUserId(), id, provider));
    }

    @Override
    public ProviderShopDetail queryShopDetail(String mallShopId) {
        try {
            Rsp<ProviderShopDetail> providerShopDetailRsp = apiGatewayFeignClient.queryShopDetail(Long.valueOf(mallShopId));
            return providerShopDetailRsp.getData();
        } catch (Exception e) {
            log.error("查询店铺详情数据异常,mallShopId:{}", mallShopId, e);
            throw ExceptionPlusFactory.bizException(ErrorCode.API_REQ_ERROR, "查询店铺详情数据异常，mallShopId:" + mallShopId);
        }
    }

    @Override
    public Long getMallShopId(Long providerId) {
        final Provider provider = iProviderService.getById(providerId);
        Assert.notNull(provider, "供应商不存在");
        if (StringUtil.isEmpty(provider.getMallShopId())) {
            return 0L;
        }
        return Long.parseLong(provider.getMallShopId());
    }

    @Override
    public List<PartnerContacts> getPartnerContactList(Long partnerProviderId) {
        final Rsp<List<PartnerContacts>> partnerContactList = partnerFeignClient.getPartnerContactList(new GetPartnerListReq().setOrgId(
                partnerProviderId));
        if (!partnerContactList.isSuccess()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "从P系统获取供应商联系人失败");
        }
        return partnerContactList.getData();
    }

    @Override
    public PartnerContacts getPartnerContactByItemNo(String partnerItemNo) {
        final Rsp<PartnerContacts> partnerContactsRsp = partnerFeignClient.getPartnerContactListByItemNo(new GetPartnersByItemNoReq().setItemNo(
                partnerItemNo));
        if (!partnerContactsRsp.isSuccess()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "从P系统获取商品关联的供应商联系人失败");
        }
        return partnerContactsRsp.getData();
    }

}
