package com.daddylab.supplier.item.infrastructure.batchcall;

import com.diffplug.common.base.Errors;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.lang.invoke.WrongMethodTypeException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @since 2021/12/7
 */
@Aspect
@Slf4j
@Component
@SuppressWarnings({"unchecked", "rawtypes"})
public class BatchCallAspect {
    final RequestManager requestManager = new RequestManager();

    @Pointcut("@annotation(annotation) && args(arg)")
    public void batchCallPointcut(BatchCall annotation, Object arg) {
    }

    @Around(value = "batchCallPointcut(annotation,arg)", argNames = "pjp,annotation,arg")
    public Object around(ProceedingJoinPoint pjp, BatchCall annotation, Object arg) throws Throwable {
        final Context context = new Context(pjp, annotation, arg);
        final BatchCallFuture future = new BatchCallFuture(context, requestManager, () -> {
            execute(context);
        }, Errors.rethrow().wrap(() -> pjp.proceed(pjp.getArgs())));
        if (isReturnFuture(context)) {
            return future;
        }
        executeIfBufferFull(context);
        return get(future);
    }

    private boolean isReturnFuture(Context context) {
        return Future.class.isAssignableFrom(context.getMethod().getReturnType());
    }

    private Object get(BatchCallFuture future) throws Throwable {
        while (true) {
            try {
                return future.get();
            } catch (ExecutionException e) {
                throw e.getCause();
            } catch (InterruptedException ignored) {
            }
        }
    }

    private void executeIfBufferFull(Context context) {
        if (requestManager.requestSize(context.getMethod()) >= context.getAnnotation().bufferSize()) {
            execute(context);
        }
    }

    private void execute(Context context) {
        final Method method = context.getMethod();
        final List<RequestManager.RequestEntry> requestEntries = requestManager.dequeue(method, context.annotation.bufferSize());
        if (requestEntries.isEmpty()) {
            return;
        }
        final Object bean = context.getBean();
        final Method batchMethod = context.getBatchMethod();
        final Class<?> parameterType = batchMethod.getParameterTypes()[0];
        Collection invokeParam = constructInvokeParameter(requestEntries, parameterType);
        try {
            final Map<Object, Object> invokeResult = (Map<Object, Object>) batchMethod.invoke(bean, invokeParam);
            requestManager.complete(method, requestEntries, invokeResult);
        } catch (Throwable e) {
            requestManager.completeExceptionally(method, requestEntries, e);
        }
    }

    @NonNull
    private Collection constructInvokeParameter(List<RequestManager.RequestEntry> requestEntries, Class<?> parameterType) {
        Collection invokeParam = null;
        if (Modifier.isAbstract(parameterType.getModifiers())) {
            if (List.class.isAssignableFrom(parameterType)) {
                invokeParam = Lists.newArrayList();
            } else if (Set.class.isAssignableFrom(parameterType)) {
                invokeParam = Sets.newHashSet();
            }
        } else {
            try {
                invokeParam = ((Collection) parameterType.newInstance());
            } catch (InstantiationException | IllegalAccessException e) {
                throw new WrongMethodTypeException("批量方法参数无法实例化");
            }
        }
        if (Objects.isNull(invokeParam)) {
            throw new WrongMethodTypeException("批量方法参数构造失败");
        }
        requestEntries.stream().map(RequestManager.RequestEntry::getRequest).forEach(invokeParam::add);
        return invokeParam;
    }

}
