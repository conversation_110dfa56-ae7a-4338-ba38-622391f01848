package com.daddylab.supplier.item.application.otherStockOutDetail;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockOutDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OtherStockOutDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOtherStockOutDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName OtherStockOutDetailBizServiceImpl.java
 * @description
 * @createTime 2022年04月01日 16:25:00
 */
@Slf4j
@Service
public class OtherStockOutDetailBizServiceImpl implements OtherStockOutDetailBizService{

    @Autowired
    private IOtherStockOutDetailService iOtherStockOutDetailService;
    @Autowired
    private OtherStockOutDetailMapper otherStockOutDetailMapper;
    @Autowired
    private ItemSkuGateway itemSkuGateway;

    @Override
    public List<Long> getByItemId(Long itemId) {
        QueryWrapper<OtherStockOutDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(Objects.nonNull(itemId),OtherStockOutDetail::getItemId,itemId);
        return iOtherStockOutDetailService.list(queryWrapper).stream().map(OtherStockOutDetail::getOrderId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getByBrandId(Long brandId) {
        QueryWrapper<OtherStockOutDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(Objects.nonNull(brandId),OtherStockOutDetail::getBrandId,brandId);
        return iOtherStockOutDetailService.list(queryWrapper).stream().map(OtherStockOutDetail::getOrderId).collect(Collectors.toList());
    }

    @Override
    public List<OtherStockOutDetail> getByOrderId(Long id) {
        LambdaQueryWrapper<OtherStockOutDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OtherStockOutDetail::getOrderId,id);
        return iOtherStockOutDetailService.list(queryWrapper);
    }

    @Override
    public List<Long> getIdsByItem(Long sku, String code, Long itemId, Long brandId, Collection<Integer> businessLines) {
        String skuCode = "";
        Optional<ItemSku> itemSku = itemSkuGateway.getByItemSkuId(sku);
        if (itemSku.isPresent()){
            skuCode = itemSku.get().getSkuCode();
        }
        return otherStockOutDetailMapper.getIdsByItem(skuCode, code, itemId,brandId,businessLines);
    }


}
