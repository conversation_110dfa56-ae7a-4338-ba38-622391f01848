package com.daddylab.supplier.item.controller.open;

import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.types.partner.CooperateModeNotify;
import com.daddylab.supplier.item.types.partner.QcChangeNotify;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/6/14
 */
@Slf4j
@RestController
@RequestMapping("/partner/notify")
@Api(value = "P系统通知API", tags = "合作伙伴系统系统通知API")
public class PartnerNotifyController {
    @Autowired
    ItemBizService itemBizService;

    @ApiOperation("QC负责人修改通知")
    @PostMapping("/qcChange")
    public Response qcChange(@RequestBody QcChangeNotify notify) {
        log.info("收到P系统商品QC负责人修改通知:{}", notify);
        itemBizService.qcChange(notify);
        return Response.buildSuccess();
    }

    @ApiOperation("合作模式修改通知")
    @PostMapping("/cooperateMode")
    public Response cooperateMode(@RequestBody CooperateModeNotify notify) {
        log.info("收到P系统商品合作模式修改通知:{}", notify);
        itemBizService.cooperateMode(notify);
        return Response.buildSuccess();
    }




}
