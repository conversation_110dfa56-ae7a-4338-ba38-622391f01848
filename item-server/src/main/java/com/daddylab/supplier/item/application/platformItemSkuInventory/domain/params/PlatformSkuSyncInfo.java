package com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLockKey;
import lombok.Data;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @class PlatformItemSkuSyncInfo.java
 * @description 描述类的作用
 * @date 2024-03-13 17:46
 */
@Data
public class PlatformSkuSyncInfo implements Serializable {

    private static final long serialVersionUID = -4099667374000621216L;

    @DistributedLockKey
    private Platform platform;

    /**
     * 店铺编码
     */
    private String shopNo;

    /**
     * 平台SKU ID
     */
    @DistributedLockKey
    private String outerSkuId;

    /**
     * 平台商品ID
     */
    private String outerItemId;

    /**
     * 平台商品规格外部编码
     */
    private String outerSkuCode;

    /**
     * 平台商品商家外部编码
     */
    @Nullable
    private String outerItemCode;

    /**
     * 平台商品库存
     */
    private Long stock;

    /**
     * 商品总库存
     */
    private Long goodsStock;

    /**
     * SKU价格
     */
    private BigDecimal price;

    /**
     * 平台商品状态 0下架 1上架（在售中）2 待上架(未上架)
     */
    private Integer status;
    
    /**
     * 平台商品状态 0下架 1上架（在售中）2 待上架(未上架)
     */
    private Integer goodsStatus;

    /**
     * 平台商品创建时间
     */
    @Nullable
    private Long itemCreateTime;

    /**
     * 平台商品更新时间
     */
    @Nullable
    private Long itemUpdateTime;

    /**
     * SKU创建时间
     */
    @Nullable
    private Long skuCreateTime;

    /**
     * SKU更新时间
     */
    @Nullable
    private Long skuUpdateTime;

    /**
     * 平台商品名称
     */
    private String goodsName;

    /**
     * 平台商品规格名称
     */
    private String specName;

    /**
     * 平台商品SKU数量
     */
    private Integer skuNum;
    
    /**
     * 是否隐藏
     */
    private Boolean hidden = false;
}
