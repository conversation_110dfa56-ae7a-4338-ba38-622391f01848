package com.daddylab.supplier.item.infrastructure.doudian.sdk.product_listV2;

import com.doudian.open.api.product_listV2.ProductListV2Response;
import com.doudian.open.core.DoudianOpRequest;
import com.doudian.open.core.DoudianOpResponse;
import com.doudian.open.utils.JsonUtil;

/**
 * <AUTHOR>
 * @since 2024/4/2
 */
public class ProductListV2RequestPatch extends DoudianOpRequest<ProductListV2ParamPatch> {
    public ProductListV2RequestPatch() {
    }

    public String getUrlPath() {
        return "/product/listV2";
    }

    public Class<? extends DoudianOpResponse<?>> getResponseClass() {
        return ProductListV2Response.class;
    }

    public String toString() {
        return JsonUtil.toJson(this);
    }
}
