/*


package com.daddylab.supplier.item.application.virtualWarehouse;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.virtualWarehouse.dto.VirtualWarehousePageQuery;
import com.daddylab.supplier.item.application.virtualWarehouse.dto.VirtualWarehousePageVo;
import com.daddylab.supplier.item.application.virtualWarehouse.dto.VirtualWarehouseSaveCmd;
import com.daddylab.supplier.item.application.virtualWarehouse.dto.VirtualWarehouseViewVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.WarehouseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;

*/
/**
 * <AUTHOR> up
 * @date 2024年02月29日 4:50 PM
 *//*

public interface VirtualWarehouseBizService {

    void save(VirtualWarehouseSaveCmd saveCmd);

    PageResponse<VirtualWarehousePageVo> page(VirtualWarehousePageQuery pageQuery);

    Response export(VirtualWarehousePageQuery pageQuery);

    SingleResponse<VirtualWarehouseViewVo> view(Long id);

    SingleResponse<Boolean> updateInventoryRatio(Long id, Integer inventoryRatio);

    MultiResponse<OperateLog> operateLog(Long id);

    MultiResponse<WarehouseDO> warehouseNos(Long wvId);


    */
/**
     * 3.4.3迭代上线前，历史数据处理，只需要成功调用一次
     *
     * @return
     *//*

    Response preDataProcessing();


    void clearTestData(String vwNo);

    */
/**
     * 3.4.3 上线，数据操作
     * 0.店铺和虚拟仓的SKU全量二级占比数据已经补全
     * 1.移除所有测试的虚拟仓。
     * 2.将所有虚拟仓、店铺的库存模式置为共享
     * 3.删除 锁定库存统计 表的所有数据
     * 4.执行此方法，全局虚拟仓，店铺的一级占比和二级占比重新计算。
     *//*

    void recalculateGlobalRatioExclusionLock();


    void removeLockStatics();


}
*/
