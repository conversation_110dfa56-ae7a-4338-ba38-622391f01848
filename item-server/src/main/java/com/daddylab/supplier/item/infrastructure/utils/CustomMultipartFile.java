package com.daddylab.supplier.item.infrastructure.utils;

import java.io.*;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> up
 * @date 2025年06月18日 6:12 PM
 */
public class CustomMultipartFile implements MultipartFile {
  private final File file;

  public CustomMultipartFile(File file) {
    this.file = file;
  }

  @NotNull
  @Override
  public String getName() {
    return file.getName();
  }

  @Override
  public String getOriginalFilename() {
    return file.getName();
  }

  @Override
  public String getContentType() {
    return "application/octet-stream";
  }

  @Override
  public boolean isEmpty() {
    return file.length() == 0;
  }

  @Override
  public long getSize() {
    return file.length();
  }

  @NotNull
  @Override
  public byte[] getBytes() throws IOException {
    try (InputStream is = Files.newInputStream(file.toPath())) {
      return org.springframework.util.StreamUtils.copyToByteArray(is);
    }
  }

  @NotNull
  @Override
  public InputStream getInputStream() throws IOException {
    return new ByteArrayInputStream(getBytes());
  }

  @Override
  public void transferTo(@NotNull File dest) throws IOException, IllegalStateException {
    org.springframework.util.FileCopyUtils.copy(file, dest);
  }

  public void deleteFile() {
    if (file != null && file.exists()) {
      boolean deleted = file.delete();
      if (!deleted) {
      }
    }
  }
}
