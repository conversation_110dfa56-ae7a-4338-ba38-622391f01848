package com.daddylab.supplier.item.controller.item.dto;

import com.alibaba.cola.dto.Command;
import com.daddylab.supplier.item.application.item.itemRunning.cmd.ItemRunningCmd;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/21 6:10 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("编辑商品运营信息封装")
public class EditRunningCmd extends Command {


    private static final long serialVersionUID = 2920143394248451502L;

    @ApiModelProperty("商品id")
    @NotNull
    private Long itemId;

    @ApiModelProperty("预计上架时间")
    Long estimateSaleTime;

    @ApiModelProperty("运营图片url")
    List<ItemImageDto> runningImageList;

    @ApiModelProperty("运营人员信息")
    List<RunningDto> runnerList;

    @ApiModelProperty("产品标准名")
    @Size(message = "产品标准名长度应该在{min}-{max}个字符", max = 150)
    String standardName;

    @ApiModelProperty("卖点文案")
    @Size(message = "卖点文案长度应该在{min}-{max}个字符", max = 150)
    String sellingPoints;

    @ApiModelProperty("平台商品ID信息List")
    List<ItemRunningCmd> itemRunnings;

    @ApiModelProperty("是否同步 是:true 否:false")
    Boolean confirm;

}
