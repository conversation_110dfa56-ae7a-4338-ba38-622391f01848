package com.daddylab.supplier.item.controller.purchaseDissent.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName PurchaseDissentVo.java
 * @description
 * @createTime 2021年11月17日 13:55:00
 */
@Data
@ApiModel("采购异议返回实体")
public class PurchaseDissentVo implements Serializable {
    private static final long serialVersionUID = -3434506599180060119L;
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 采购id
     */
    @ApiModelProperty(value = "采购id")
    private Long purchaseId;

    /**
     * 异议内容
     */
    @ApiModelProperty(value = "异议内容")
    private String content;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "异议创建时间")
    private Long createdAt;

    /**
     * 异议时间
     */
    @ApiModelProperty(value = "异议时间")
    private String time;
}
