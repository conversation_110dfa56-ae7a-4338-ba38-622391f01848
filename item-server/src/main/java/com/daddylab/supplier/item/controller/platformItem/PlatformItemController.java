package com.daddylab.supplier.item.controller.platformItem;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.application.platformItem.*;
import com.daddylab.supplier.item.application.platformItem.data.*;
import com.daddylab.supplier.item.application.platformItem.query.OtherPlatformItemPageQuery;
import com.daddylab.supplier.item.application.platformItem.query.PlatformItemDropDownPageQuery;
import com.daddylab.supplier.item.application.platformItem.query.PlatformItemPageQuery;
import com.daddylab.supplier.item.common.domain.dto.GenericIdBody;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.shop.dto.ShopDropDownItem;
import com.daddylab.supplier.item.domain.shop.dto.ShopDropDownQuery;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.types.platformItem.SelectPlatformItemQuery;
import com.daddylab.supplier.item.types.platformItem.SelectPlatformItemVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 2:23 下午
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/platformItem")
@Api(tags = {"平台商品API"})
public class PlatformItemController {
    @Autowired
    PlatformItemBizService platformItemService;
    @Autowired
    PlatformItemStockSyncBizService platformItemStockSyncBizService;
    @Autowired
    InventoryAllocBizService inventoryAllocBizService;
    @Autowired
    OperateLogBizService operateLogBizService;
    @Autowired
    PlatformItemStockSyncBizService stockSyncBizService;

    @PostMapping("/page")
    @ResponseBody
    @ApiOperation(value = "平台商品分页查询", notes = "单品设置列表也使用该接口查询")
    public PageResponse<PlatformItemListItem> pageQuery(@Validated @RequestBody PlatformItemPageQuery pageQuery) {
        log.info("平台商品分页查询API:{}", pageQuery);
        return platformItemService.pageQuery(pageQuery);
    }

    @PostMapping("/selectPlatformItem")
    @ResponseBody
    @ApiOperation("平台商品选择（混合数据源，平台为小程序将会从小程序接口查询）")
    public PageResponse<SelectPlatformItemVO> selectPlatformItem(@Validated @RequestBody SelectPlatformItemQuery pageQuery) {
        log.info("平台商品选择（混合数据源）:{}", pageQuery);
        return platformItemService.hibridSelectPlatformItem(pageQuery);
    }

    @PostMapping("/dropdown")
    @ResponseBody
    @ApiOperation("平台商品下拉列表分页查询")
    public PageResponse<PlatformItemDropDownItem> dropdown(@Validated @RequestBody PlatformItemDropDownPageQuery pageQuery) {
        log.info("平台商品下拉列表分页查询API:{}", pageQuery);
        return platformItemService.dropDownList(pageQuery);
    }

    @PostMapping("/otherPlatform/page")
    @ResponseBody
    @ApiOperation("其他平台商品分页查询")
    public PageResponse<PlatformItemListItem> otherPlatformItemPageQuery(@Validated @RequestBody OtherPlatformItemPageQuery pageQuery) {
        log.info("其他平台商品分页查询API:{}", pageQuery);
        return platformItemService.otherPlatformItemPageQuery(pageQuery);
    }

    @PostMapping("/detail")
    @ResponseBody
    @ApiOperation("平台商品详情数据")
    public SingleResponse<PlatformItemDetail> detail(@Validated @RequestBody GenericIdBody<Long> body) {
        log.info("平台商品详情数据API:{}", body);
        return platformItemService.detail(body.getId());
    }

    @PostMapping(value = "/sync")
    @ResponseBody()
    @ApiOperation("同步平台商品")
    public SingleResponse<PlatformItemSyncResult> sync(@Validated @RequestBody PlatformItemSyncCmd body) {
        log.info("同步平台商品API:{}", body);
        return platformItemService.sync(body);
    }

    @ApiOperation(value = "操作记录")
    @GetMapping(value = "/operateLogs")
    public MultiResponse<OperateLog> getOperateLogs(@RequestParam @ApiParam("平台商品ID") String id) {
        return operateLogBizService.getOperateLogs(OperateLogTarget.PLATFORM_ITEM, id);
    }

    @ApiOperation(value = "库存同步开关")
    @PostMapping(value = "/inventorySync/switch")
    public Response inventorySyncSwitch(@RequestBody InventorySyncSwitchCmd cmd) {
        platformItemService.inventorySyncSwitch(cmd);
        return Response.buildSuccess();
    }

    @ApiOperation(value = "库存锁定开关")
    @PostMapping(value = "/inventoryLock/switch")
    public Response inventoryLockSwitch(@RequestBody InventoryLockSwitchCmd cmd) {
        platformItemService.inventoryLockSwitch(cmd);
        return Response.buildSuccess();
    }

    @ApiOperation(value = "库存占用明细")
    @PostMapping(value = "/inventoryOccupyDetail")
    public SingleResponse<InventoryOccupyDetailVO> inventoryOccupyDetail(@Validated @RequestBody InventoryOccupyDetailQuery query) {
        return SingleResponse.of(platformItemService.inventoryOccupyDetail(query));
    }
    
    @ApiOperation(value = "总库存明细")
    @PostMapping(value = "/warehouseInventoryDetail")
    public SingleResponse<WarehouseInventoryDetailVO> warehouseInventoryDetail(@Validated @RequestBody WarehouseInventoryDetailQuery query) {
        return SingleResponse.of(platformItemService.warehouseInventoryDetail(query));
    }

    @ApiOperation(value = "编辑同步库存")
    @PostMapping(value = "/editSyncInventory")
    public Response editSyncInventory(@Validated @RequestBody EditSyncInventoryCmd cmd) {
        platformItemService.editSyncInventory(cmd);
        return Response.buildSuccess();
    }
    
    @ApiOperation(value = "编辑低库存预警")
    @PostMapping(value = "/editWarnThreshold")
    public Response editWarnThreshold(@Validated @RequestBody EditWarnThresholdCmd cmd) {
        platformItemService.editWarnThreshold(cmd);
        return Response.buildSuccess();
    }

    @PostMapping(value = "/syncInventory")
    @ResponseBody()
    @ApiOperation("同步平台商品库存")
    public Response syncInventory(@Validated @RequestBody SyncInventoryCmd cmd) {
        platformItemService.syncInventory(cmd);
        return Response.buildSuccess();
    }

    @ApiOperation("库存分配")
    @PostMapping(value = "/allocInventory")
    public Response allocInventory(@RequestBody AllocInventoryCmd cmd) {
        inventoryAllocBizService.allocInventory(cmd.getSkuCodes());
        return Response.buildSuccess();
    }
    
    @ApiOperation(value = "库存同步记录")
    @PostMapping(value = "/syncLog")
    public PageResponse<SyncLogVO> syncLog(@Validated @RequestBody SyncLogQuery query) {
        return platformItemStockSyncBizService.syncLog(query);
    }
    
    @PostMapping(value = "/dropDownShopList")
    @ApiOperation("店铺下拉列表")
    public MultiResponse<ShopDropDownItem> dropDownShopList(
            @RequestBody @Valid ShopDropDownQuery query) {
        return platformItemService.dropDownShopList(query);
    }

}
