package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/9/19 10:50
 * @Description: 商品匹配小程序 feign 请求参数
 */
@Data
public class ItemSyncMiniProgramMatchResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品编码
     */
    private String itemNo;

    /**
     * SKU数量
     */
    private Integer skuNum;

    /**
     * 平台商品数量
     */
    private Integer itemNum;

    /**
     * 小程序商品列表
     */
    private List<ErpItemListVO> itemList;

    @Data
    public static class ErpItemListVO implements Serializable {
        private static final long serialVersionUID = 1L;

        private Long id;

        /**
         * 商品名称
         */
        private String name;

        /**
         * 商品价格
         */
        private BigDecimal price;

        /**
         * 创建时间
         */
        private Long createdAt;

        /**
         * 上架状态
         */
        private Integer shelfStatus;

        /**
         * 审核状态
         */
        private Integer auditStatus;

        /**
         * 类目ID
         */
        @ApiModelProperty(value = "类目ID")
        private Long categoryId;

    }
}
