package com.daddylab.supplier.item.infrastructure.gatewayimpl.nuonuo;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 诺诺财务系统配置参数
 *
 * <AUTHOR> up
 * @date 2024年07月25日 2:47 PM
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "nuo-nuo-invoice-config")
public class NuoNuoConfig {

    private String appKey;

    private String appSecret;

    private String url;

    private String defaultToken;

    /**
     * 销售方 名称,税号,电话，地址
     */
    private String saleName;
    private String saleTaxNum;
    private String salePhone;
    private String saleAddress;

    /**
     * 开票员（数电票时需要传入和开票登录账号对应的开票员姓名）
     */
    private String clerk;

    /**
     * 分机号：923 测 全电发票
     * 分机号：888 可开普票、专票等发票
     */
    private String extensionNumber;

    private String filterOrderNo;



}
