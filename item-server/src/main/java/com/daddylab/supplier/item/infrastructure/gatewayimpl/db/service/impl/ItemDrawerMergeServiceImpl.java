package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMerge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerMergeMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerMergeService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 新品商品合并审核包表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Service
public class ItemDrawerMergeServiceImpl extends DaddyServiceImpl<ItemDrawerMergeMapper, ItemDrawerMerge> implements IItemDrawerMergeService {

}
