package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine;

import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.combinationHandler.ActivityRandomCombinationPrice;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.combinationHandler.ActivitySingleCombinationPrice;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.combinationHandler.DailyRandomCombinationPrice;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.combinationHandler.DailySingleCombinationPrice;
import com.daddylab.supplier.item.infrastructure.timing.StopWatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> up
 * @date 2022年10月24日 10:48 AM
 */
@Slf4j
@Component
public class PriceEngineManager {

    @Resource
    ActivityQuantityPrice activityQuantityPrice;

    @Resource
    ActivityTimePrice activityTimePrice;

    @Resource
    ActivityRandomCombinationPrice activityRandomCombinationPrice;

    @Resource
    ActivitySingleCombinationPrice activitySingleCombinationPrice;

    @Resource
    DailyRandomCombinationPrice dailyRandomCombinationPrice;

    @Resource
    DailySingleCombinationPrice dailySingleCombinationPrice;

    @Resource
    RefundStockInPrice refundStockInPrice;

    @Resource
    GiftPrice giftPrice;

    @Resource
    SpecialTreatment specialTreatment;

    private static final List<PriceProcessor> PRICE_PROCESSOR_LIST = new ArrayList<>();

    private Integer currentIndex;


    /**
     * 初始化执行链路顺序
     * 按照价格计算逻辑的优先级，从低到高，依次添加到list
     */
    private void init() {
        // 单sku维度，日常多件供价计算
        PRICE_PROCESSOR_LIST.add(dailySingleCombinationPrice);
        // 跨sku，单spu维度，日常多件供价计算
        PRICE_PROCESSOR_LIST.add(dailyRandomCombinationPrice);
        // 单sku维度，活动多件供价计算
        PRICE_PROCESSOR_LIST.add(activitySingleCombinationPrice);
        // 跨sku，单spu维度，活动多件供价计算
        PRICE_PROCESSOR_LIST.add(activityRandomCombinationPrice);
        // 采购活动价，根据时间进行活动优惠
        PRICE_PROCESSOR_LIST.add(activityTimePrice);
        // 采购活动价，根据采购数量进行活动优惠
        PRICE_PROCESSOR_LIST.add(activityQuantityPrice);
        // 赠品价格处理。
        PRICE_PROCESSOR_LIST.add(giftPrice);
        // 销售退货入库单价格处理
        PRICE_PROCESSOR_LIST.add(refundStockInPrice);

        PRICE_PROCESSOR_LIST.add(specialTreatment);

  }

    /**
     * @param targetMonth yyyyMM
     */
    public void start(String targetMonth) {
        init();
        TimeBO timeBO = TimeBO.of(targetMonth);
        this.currentIndex = 0;
        Boolean aBoolean = handle(timeBO);
        if (Boolean.FALSE.equals(aBoolean)) {
            // ignore
            String name = PRICE_PROCESSOR_LIST.get(currentIndex).getClass().getName();
            log.error("------- price process fail. name:{} ------", name);
        } else {
            // 价格处理链全部执行完毕。
            log.info("------- all price process finish -------");
        }
    }

    private Boolean handle(TimeBO targetMonth) {
        PriceProcessor priceProcessor = PRICE_PROCESSOR_LIST.get(this.currentIndex);

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Boolean aBoolean = priceProcessor.doPriceProcess(targetMonth);
        stopWatch.stop();
        log.info("PriceEngineManager task name:{},res:{},operateTime:{},time-consuming:{} min.",
                priceProcessor.getClass().getSimpleName(), aBoolean, targetMonth.getOperateMonth(), stopWatch.getTotal(TimeUnit.MINUTES));

        if (Boolean.TRUE.equals(aBoolean) && this.currentIndex < PRICE_PROCESSOR_LIST.size() - 1) {
            this.currentIndex = this.currentIndex + 1;
            return handle(targetMonth);
        } else {
            // 只有当最后一个执行器执行正常执行完成。才会返回true。
            // 如果中间过程某个执行器返回失败，递归中断，并且返回false
            return aBoolean && this.currentIndex == PRICE_PROCESSOR_LIST.size() - 1;
        }
    }


}
