package com.daddylab.supplier.item.infrastructure.utils;

import java.util.Objects;
import java.util.StringJoiner;

public class ExceptionUtil extends cn.hutool.core.exceptions.ExceptionUtil {
    /**
     * 获取异常的栈信息（默认10个栈）
     */
    public static String getSimpleStackString(Throwable e) {
        return getSimpleStackString(e, 10);
    }

    /**
     * 获取异常的栈信息（前多少行）
     *
     * @param lineLimit //抓取多少个异常栈帧
     * @return 拼接后的异常堆栈
     */
    public static String getSimpleStackString(Throwable e, int lineLimit) {
        if (Objects.isNull(e)) return "";
        final String prefix = "com.daddylab.supplier.item";
        StringJoiner stringJoiner = new StringJoiner("\n");
        stringJoiner.add(e.toString());
        final int catchTop = 3;
        int line = 0;
        boolean addEllipsis = false;
        final StackTraceElement[] stackTrace = e.getStackTrace();
        for (int i = 0; i < stackTrace.length; i++) {
            final StackTraceElement stackTraceElement = stackTrace[i];
            if (i < catchTop || stackTraceElement.getClassName().startsWith(prefix)) {
                stringJoiner.add("\tat " + stackTraceElement.toString());

                if (++line > lineLimit + catchTop)
                    break;
            } else if (!addEllipsis) {
                stringJoiner.add("\t...");
                addEllipsis = true;
            }
        }
        return stringJoiner.toString();
    }
}
