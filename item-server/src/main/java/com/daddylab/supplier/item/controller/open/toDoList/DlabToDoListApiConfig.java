package com.daddylab.supplier.item.controller.open.toDoList;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> up
 * @date 2024年10月12日 9:49 AM
 */
@Configuration
public class DlabToDoListApiConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new HandlerInterceptor() {
            @Override
            public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
                final String header = request.getHeader("OPEN-AUTH");
                boolean verify = DigestUtil.md5Hex(header).equals("06d86297d6e28d4637d60c86c2a2f5b6");
                if (!verify) {
                    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                    final SingleResponse singleResponse =
                            SingleResponse.buildFailure(String.valueOf(HttpServletResponse.SC_UNAUTHORIZED), "验证失败");
                    response.getWriter().write(JsonUtil.toJson(singleResponse));
                    return false;
                }
                return true;
            }
        }).order(Ordered.HIGHEST_PRECEDENCE).addPathPatterns("/open/toDoList/**");
    }

    public static void main(String[] args) {
        // 06d86297d6e28d4637d60c86c2a2f5b6
        System.out.println(DigestUtil.md5Hex("12358"));

    }
}
