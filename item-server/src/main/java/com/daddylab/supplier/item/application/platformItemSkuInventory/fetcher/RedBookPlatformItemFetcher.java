package com.daddylab.supplier.item.application.platformItemSkuInventory.fetcher;

import com.daddylab.supplier.item.application.platformItemSkuInventory.PlatformItemSkuSyncService;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.dataFetch.CustomFetcher;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/15
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RedBookPlatformItemFetcher implements CustomFetcher {

    private final List<PlatformItemSkuSyncService> platformItemSkuSyncServiceList;

    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.REDBOOK_PLATFORM_ITEM;
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, RunContext runContext) {
        platformItemSkuSyncServiceList.stream().filter(service -> service.defaultType() == Platform.XIAOHONGSHU)
                                      .forEach(service -> service.incrementSync(startTime, endTime));
    }
}