package com.daddylab.supplier.item.infrastructure.submail.utils;


import com.daddylab.supplier.item.infrastructure.submail.config.AppConfig;
import com.daddylab.supplier.item.infrastructure.submail.config.MessageConfig;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;

/**
 * 这个类通过加载app_config.properties文件创建配置对象并获取值，包括创建
 * MailConfig，MessageConfig，VoiceConfig,InternationalsmsConfig,MobiledataConfig
 * @see AppConfig
 * @see MessageConfig
 *
 * @Auther: <EMAIL>
 * @version: 1.0.0
 * @Date: 2021/04/16/4:01 下午
 */
public class ConfigLoader {

    /**
     * enum define two kinds of configuration.
     * */
    public enum ConfigType {
        Message
    }

    /**
     * 外部类的静态方法，可以通过加载文件创建配置。
     *
     * @param type
     *            ConfigType
     * @return If the type is ConfigType#Mail,return the instance of
     *         {@link MessageConfig}. And,if the type is ConfigType#Message,return
     *         the instance of {@link MessageConfig}.
     * */
    public static AppConfig load(ConfigType type) {
        switch (type) {
            case Message:
                return createMessageConfig();
            default:
                return null;
        }
    }


    private static AppConfig createMessageConfig() {
        return ApplicationContextUtil.getBean(MessageConfig.class);
    }

}
