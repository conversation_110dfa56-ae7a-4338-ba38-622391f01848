package com.daddylab.supplier.item.application.afterSaleLogistics;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> up
 * @date 2024年05月23日 2:05 PM
 */
@Getter
@AllArgsConstructor
public enum LogisticsRootException implements IIntegerEnum {

    /**
     *
     */
    CONSIGN_EXCEPTION(1, "发货异常"),
    RECEIVE_EXCEPTION(2, "揽收异常"),
    SEND_EXCEPTION(3, "发运异常"),
    DISTRIBUTE_EXCEPTION(4, "派件异常"),
    DIFFICULT_EXCEPTION(5, "异常件"),

    NORMAL(0, "售后物流状态正常"),
    ERROR(9, "系统异常");

    private final Integer value;
    private final String desc;
}
