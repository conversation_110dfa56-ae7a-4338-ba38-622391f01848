package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderSettlementForm;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderSettlementFormMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderSettlementFormService;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 订单结算表单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Service
public class OrderSettlementFormServiceImpl extends DaddyServiceImpl<OrderSettlementFormMapper, OrderSettlementForm> implements IOrderSettlementFormService {

    @Override
    public OrderSettlementForm getByNo(String no) {
        return lambdaQuery().eq(OrderSettlementForm::getNo, no).one();
    }
}
