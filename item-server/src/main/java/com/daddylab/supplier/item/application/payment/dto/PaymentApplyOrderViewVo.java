package com.daddylab.supplier.item.application.payment.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PaymentOrderStatus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年11月20日 3:56 PM
 */
@Data
@ApiModel("付款申请单详情")
public class PaymentApplyOrderViewVo {

  private Long id;

  @ApiModelProperty("付款单号")
  private String no;

  @ApiModelProperty("合作模式")
  private Integer businessLine;

  @ApiModelProperty("采购类型。0：标准采购，1：工厂代发")
  private Integer purchaseType;

  @ApiModelProperty("币别。0：人命币，1：美元")
  private Integer currencyType;

  @ApiModelProperty("采购组织")
  private String purchaseOrgStr;

  private String purchaseOrg;

  @ApiModelProperty("付款组织")
  private String payOrgStr;

  private String payOrg;

  @ApiModelProperty("往来单位")
  private String tradeUnitStr;

  private Long tradeUnit;

  @ApiModelProperty("往来单位是否黑名单")
  private Integer tradeUnitIsBlacklist;

  @ApiModelProperty("收款单位")
  private String payeeUnitStr;

  private Long payeeUnit;

  @ApiModelProperty("收款单位是否黑名单")
  private Integer payeeUnitIsBlacklist;

  @ApiModelProperty("收款单位银行账号")
  private String payeeBankCardNo;

  @ApiModelProperty("收款单位开户行行号")
  private String payeeBankNo;

  @ApiModelProperty("收款单位开户行")
  private String payeeBank;

  private Long buyerId;

  @ApiModelProperty("采购员")
  private String buyerName;

  @ApiModelProperty("采购部门")
  private String buyerDept;

  private Long applyTime;

  @ApiModelProperty("申请日期")
  private String applyTimeStr;

  private Long createdUid;

  @ApiModelProperty("申请人")
  private String createdUser;

  private Long expectedPayTime;

  @ApiModelProperty("期望付款日期")
  private String expectedPayTimeStr;

  @ApiModelProperty("备注")
  private String remark;

  private Integer payPurpose;

  @ApiModelProperty("付款类型。0：采购付款（默认），1：预付款")
  private String payPurposeStr;

  private Integer payProportions;

  @ApiModelProperty("付款比例")
  private String payProportionsStr;

  @ApiModelProperty("总申请付款金额")
  private BigDecimal totalApplyAmount;

  @ApiModelProperty("其他金额")
  private BigDecimal otherChargeback;

  @ApiModelProperty("实付金额")
  private BigDecimal realPayAmount;

  @ApiModelProperty("发票附件列表")
  private List<AdditionalVo> additionalList;

  private String additionalIds;

  @ApiModelProperty("付款明细来源。0：采购单，1：结算单")
  private Integer detailSource;

  // -----------

  @ApiModelProperty("状态")
  private PaymentOrderStatus status;

  //    @ApiModelProperty("OA流程处理者")
  //    private String oaProcessHandleUser;
  //
  //    @ApiModelProperty("OA流程处理时间节点")
  //    private String oaProcessHandleTime;

  @ApiModelProperty("OA跳转URL")
  private String oaProcessUrl;

  @ApiModelProperty("OA审核流程")
  private List<OaAuditNode> oaAuditNodes;

  private Long submitUid;

  @ApiModelProperty("我方银行账号")
  private String ourAccountNumber;

  @ApiModelProperty("我方账户名称")
  private String ourAccountName;

  @ApiModelProperty("我方开户行")
  private String ourBankName;
}
