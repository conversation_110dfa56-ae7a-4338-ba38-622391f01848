package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseRandomSkuCombinationPrice;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;

/**
 * <p>
 * 任意sku组合采购价格信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-21
 */
public interface PurchaseRandomSkuCombinationPriceMapper extends DaddyBaseMapper<PurchaseRandomSkuCombinationPrice> {

}
