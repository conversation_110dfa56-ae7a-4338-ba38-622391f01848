package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerLiveVerbal;

import java.util.List;

/**
 * <p>
 * 新品商品-商品抽屉-直播话术审核表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
public interface IItemDrawerLiveVerbalService extends IDaddyService<ItemDrawerLiveVerbal> {

    List<ItemDrawerLiveVerbal> getByItemId(Long itemId);

    String getName(boolean hasOldData, ItemDrawerLiveVerbal liveVerbal);

    String getName(boolean hasOldData, ItemDrawerLiveVerbal liveVerbal, List<ItemDrawerLiveVerbal> liveVerbals);
}
