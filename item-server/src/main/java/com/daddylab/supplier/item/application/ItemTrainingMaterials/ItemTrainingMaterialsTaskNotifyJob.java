package com.daddylab.supplier.item.application.ItemTrainingMaterials;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsPrincipalsInfo;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchPlan;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTrainingMaterials;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatMsg;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemLaunchPlanService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemTrainingMaterialsProcessService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemTrainingMaterialsService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.types.itemTrainingMaterials.ItemTrainingMaterialsStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.HashSetValuedHashMap;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/4/21
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ItemTrainingMaterialsTaskNotifyJob {
    private final IItemTrainingMaterialsProcessService itemTrainingMaterialsProcessService;
    private final IItemTrainingMaterialsService itemTrainingMaterialsService;
    private final NewGoodsBizService newGoodsBizService;
    private final RefreshConfig refreshConfig;
    private final MsgSender msgSender;
    private final ItemTrainingMaterialsConfig materialsConfig;
    private final IItemLaunchPlanService itemLaunchPlanService;

    @XxlJob("ItemTrainingMaterialsTaskNotifyJob:doNotify")
    public void doNotify() {
        notifyLegal();
        notifyQc();
        notifyModify();
    }

    private void notifyLegal() {
        final List<ItemTrainingMaterials> list = itemTrainingMaterialsService
                .lambdaQuery()
                .eq(ItemTrainingMaterials::getStatus,
                        ItemTrainingMaterialsStatus.TO_BE_LEGAL_REVIEW)
                .list();
        final List<StaffBrief> legalCandidates = StaffAssembler.INST.longListToStaffBriefList(materialsConfig.getLegalCandidates());
        if (!list.isEmpty()) {
            final Map<StaffBrief, MultiValuedMap<ItemLaunchPlan, ItemTrainingMaterials>> sendGroup = new HashMap<>();
            for (ItemTrainingMaterials itemTrainingMaterials : list) {
                final Long itemId = itemTrainingMaterials.getItemId();
                try {
                    final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo = newGoodsBizService.getNewGoodsPrincipalsInfo(
                            itemId).getData();
                    final List<StaffBrief> legalUsers = newGoodsPrincipalsInfo.getLegalUsers();
                    final ItemLaunchPlan plan = itemLaunchPlanService.getPlanByItemId(itemId);
                    final List<StaffBrief> staffBriefs = !legalUsers.isEmpty() ? legalUsers : legalCandidates;
                    if (!staffBriefs.isEmpty()) {
                        for (StaffBrief staffBrief : staffBriefs) {
                            final MultiValuedMap<ItemLaunchPlan, ItemTrainingMaterials> planGroup = sendGroup.computeIfAbsent(
                                    staffBrief,
                                    k -> new HashSetValuedHashMap<>());
                            planGroup.put(plan, itemTrainingMaterials);
                        }
                    }
                } catch (Exception e) {
                    log.error("[培训资料待办提示汇总]处理异常 itemId={}", itemId, e);
                }
            }

            if (!sendGroup.isEmpty()) {
                send(sendGroup);
            }
        }
    }

    private void notifyQc() {
        final List<ItemTrainingMaterials> list = itemTrainingMaterialsService
                .lambdaQuery()
                .eq(ItemTrainingMaterials::getStatus,
                        ItemTrainingMaterialsStatus.TO_BE_QC_REVIEW)
                .list();
        final List<StaffBrief> qcSupervisors = StaffAssembler.INST.longListToStaffBriefList(materialsConfig.getQcSupervisors());
        if (!list.isEmpty()) {
            final Map<StaffBrief, MultiValuedMap<ItemLaunchPlan, ItemTrainingMaterials>> sendGroup = new HashMap<>();
            for (ItemTrainingMaterials itemTrainingMaterials : list) {
                final Long itemId = itemTrainingMaterials.getItemId();
                try {
                    final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo = newGoodsBizService.getNewGoodsPrincipalsInfo(
                            itemId).getData();
                    final List<StaffBrief> qcUsers = newGoodsPrincipalsInfo.getQcUsers();
                    final ItemLaunchPlan plan = itemLaunchPlanService.getPlanByItemId(itemId);
                    final List<StaffBrief> staffBriefs = !qcUsers.isEmpty() ? qcUsers : qcSupervisors;
                    if (!staffBriefs.isEmpty()) {
                        for (StaffBrief staffBrief : staffBriefs) {
                            final MultiValuedMap<ItemLaunchPlan, ItemTrainingMaterials> planGroup = sendGroup.computeIfAbsent(
                                    staffBrief,
                                    k -> new HashSetValuedHashMap<>());
                            planGroup.put(plan, itemTrainingMaterials);
                        }
                    }
                } catch (Exception e) {
                    log.error("[培训资料待办提示汇总]处理异常 itemId={}", itemId, e);
                }
            }

            if (!sendGroup.isEmpty()) {
                send(sendGroup);
            }
        }
    }

    private void notifyModify() {
        final List<ItemTrainingMaterials> list = itemTrainingMaterialsService
                .lambdaQuery()
                .eq(ItemTrainingMaterials::getStatus,
                        ItemTrainingMaterialsStatus.TO_BE_MODIFY)
                .list();
        final List<StaffBrief> csCandidates = StaffAssembler.INST.longListToStaffBriefList(materialsConfig.getCsCandidates());
        if (!list.isEmpty()) {
            final Map<StaffBrief, MultiValuedMap<ItemLaunchPlan, ItemTrainingMaterials>> sendGroup = new HashMap<>();
            for (ItemTrainingMaterials itemTrainingMaterials : list) {
                final Long itemId = itemTrainingMaterials.getItemId();
                try {
                    final ItemLaunchPlan plan = itemLaunchPlanService.getPlanByItemId(itemId);
                    if (!csCandidates.isEmpty()) {
                        for (StaffBrief staffBrief : csCandidates) {
                            final MultiValuedMap<ItemLaunchPlan, ItemTrainingMaterials> planGroup = sendGroup.computeIfAbsent(
                                    staffBrief,
                                    k -> new HashSetValuedHashMap<>());
                            planGroup.put(plan, itemTrainingMaterials);
                        }
                    }
                } catch (Exception e) {
                    log.error("[培训资料待办提示汇总]处理异常 itemId={}", itemId, e);
                }
            }

            if (!sendGroup.isEmpty()) {
                send(sendGroup);
            }
        }
    }

    private void send(Map<StaffBrief, MultiValuedMap<ItemLaunchPlan, ItemTrainingMaterials>> sendGroup) {
        for (Map.Entry<StaffBrief, MultiValuedMap<ItemLaunchPlan, ItemTrainingMaterials>> group : sendGroup.entrySet()) {
            final StaffBrief user = group.getKey();
            for (Map.Entry<ItemLaunchPlan, Collection<ItemTrainingMaterials>> planGroup : group.getValue()
                                                                                               .asMap()
                                                                                               .entrySet()) {
                final ItemLaunchPlan plan = planGroup.getKey();
                final Long launchTime = plan.getLaunchTime();
                final Collection<ItemTrainingMaterials> materials = planGroup.getValue();
                final String title = String.format("%s 共 %s 款商品【培训资料】待审核，请查看后处理！",
                        DateUtil.formatDate(launchTime),
                        materials.size());
                final String description = String.format("共 %s 款商品", materials.size());
                final String idsStr = materials.stream()
                                               .map(ItemTrainingMaterials::getItemId)
                                               .map(Object::toString)
                                               .collect(
                                                       Collectors.joining(
                                                               ","));
                final String url =
                        String.format(
                                refreshConfig.getDomain()
                                        + "/operation-management/new-products/list?itemIds=" + idsStr
                        );

                final WechatMsg wechatMsg = new WechatMsg();
                wechatMsg.setTitle(title);
                wechatMsg.setContent(description);
                wechatMsg.setLink(url);
                wechatMsg.setRecipient(user.getQwUserId());
                wechatMsg.setType(1);

                msgSender.send(wechatMsg);
            }
        }
    }

}
