package com.daddylab.supplier.item.infrastructure.gatewayimpl.daddylabWorkbench;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizService;
import com.daddylab.supplier.item.application.stockOutOrder.StockOutOrderBizService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderDetailPageQuery;
import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderDetailVO;
import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderViewVO;
import com.daddylab.supplier.item.controller.stockout.dto.StockOutOrderDetailVO;
import com.daddylab.supplier.item.controller.stockout.dto.StockOutOrderViewVO;
import com.daddylab.supplier.item.domain.daddylabWorkbench.DaddylabWorkbenchGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AwsBusinessLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockOutOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAwsBusinessLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IProviderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseOrderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockOutOrderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.DaddylabWorkbenchFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.*;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2024年04月22日 2:34 PM
 */
@Component
@Slf4j
public class DaddylabWorkbenchGatewayImpl implements DaddylabWorkbenchGateway {

    @Resource
    DaddylabWorkbenchFeignClient daddylabWorkbenchFeignClient;
    @Resource
    PurchaseOrderBizService purchaseOrderBizService;
    @Resource
    IPurchaseOrderService iPurchaseOrderService;
    @Resource
    StockOutOrderBizService stockOutOrderBizService;
    @Resource
    IStockOutOrderService iStockOutOrderService;
    @Resource
    IAwsBusinessLogService iAwsBusinessLogService;
    @Resource
    IProviderService iProviderService;

    private PurchaseOrderProcessForm buildPurchaseOrderForm(Long purchaseOrderId, Long currentUserId) {
        SingleResponse<PurchaseOrderViewVO> viewResponse = purchaseOrderBizService.view(purchaseOrderId);
        if (!viewResponse.isSuccess()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "采购订单单据信息查询失败");
        }
        PurchaseOrderViewVO purchaseOrder = viewResponse.getData();
        PurchaseOrderProcessForm form = new PurchaseOrderProcessForm();
        form.setBuyerId(purchaseOrder.getBuyerUserId());
        form.setContractNo(purchaseOrder.getContractNo());
        form.setDeliveryMethod(purchaseOrder.getArrivalType());
        form.setDept(purchaseOrder.getDepartmentName());
        form.setLoginUserId(currentUserId);
        form.setNo(purchaseOrder.getNo());
        form.setPayMethod(purchaseOrder.getPayType());
        form.setProviderName(purchaseOrder.getProviderName());
        form.setPurchaseDate(purchaseOrder.getPurchaseDate());
        form.setPurchaseOrg(purchaseOrder.getOrganizationName());
        form.setRemark(purchaseOrder.getRemark());
        form.setType(purchaseOrder.getType());
        form.setProcessInstId(purchaseOrder.getWorkbenchProcessId());

        PurchaseOrderDetailPageQuery pageQuery = new PurchaseOrderDetailPageQuery();
        pageQuery.setPurchaseOrderId(purchaseOrder.getId());
        pageQuery.setPageSize(9999);
        PageResponse<PurchaseOrderDetailVO> detailVoPageResponse = purchaseOrderBizService.pageView(pageQuery);
        if (!detailVoPageResponse.isSuccess()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "采购单据SKU明细列表查询失败");
        }
        List<PurchaseOrderDetailVO> detailVoList = detailVoPageResponse.getData();
        List<PurchaseOrderDetailFormList> formDetailList = detailVoList.stream().map(purchaseOrderDetailVO -> {
            PurchaseOrderDetailFormList purchaseOrderDetailFormList = new PurchaseOrderDetailFormList();
            purchaseOrderDetailFormList.setBusinessId(purchaseOrderDetailVO.getId());
            purchaseOrderDetailFormList.setIsGift(purchaseOrderDetailVO.getIsGift());
            purchaseOrderDetailFormList.setItemName(purchaseOrderDetailVO.getItemName());
            purchaseOrderDetailFormList.setNum(purchaseOrderDetailVO.getPurchaseQuantity());
            purchaseOrderDetailFormList.setRemark(purchaseOrderDetailVO.getRemark());
            purchaseOrderDetailFormList.setSkuCode(purchaseOrderDetailVO.getItemSkuCode());
            purchaseOrderDetailFormList.setSpecifications(purchaseOrderDetailVO.getSpecifications());
            purchaseOrderDetailFormList.setTaxPrice(purchaseOrderDetailVO.getTaxPrice());
            purchaseOrderDetailFormList.setTaxRate(purchaseOrderDetailVO.getTaxRate());
            purchaseOrderDetailFormList.setTotalPriceTax(purchaseOrderDetailVO.getTotalPriceTax());
            purchaseOrderDetailFormList.setUnit(purchaseOrderDetailVO.getUnit());
            purchaseOrderDetailFormList.setWareHouseName(purchaseOrderDetailVO.getWarehouseName());
            return purchaseOrderDetailFormList;
        }).collect(Collectors.toList());
        form.setPurchaseOrderDetailFormList(formDetailList);

        return form;
    }

    @Override
    public SingleResponse<String> syncPurchaseOrder(Long purchaseOrderId, Long currentUserId) {
        PurchaseOrderProcessForm form = buildPurchaseOrderForm(purchaseOrderId, currentUserId);
        DaddylabWorkbenchResponse daddylabWorkbenchResponse = daddylabWorkbenchFeignClient.purchaseOrderLaunch(form);
        log.info("[daddylab Workbench] syncPurchaseOrder res:{}", daddylabWorkbenchResponse);
        if (!daddylabWorkbenchResponse.isSuccess()) {
            return SingleResponse.buildFailure(ErrorCode.SYS_ERROR.getCode(), "工作台返回异常." + daddylabWorkbenchResponse.getMsg());
        }
        String processId = daddylabWorkbenchResponse.getData();
        if (StrUtil.isNotBlank(processId)) {
            iPurchaseOrderService.lambdaUpdate().set(PurchaseOrder::getWorkbenchProcessId, processId)
                    .eq(PurchaseOrder::getId, purchaseOrderId).update();

            List<AwsBusinessLog> list = iAwsBusinessLogService.lambdaQuery().eq(AwsBusinessLog::getBusinessId, purchaseOrderId)
                    .eq(AwsBusinessLog::getType, PurchaseTypeEnum.PURCHASE_ORDER.getValue()).orderByDesc(AwsBusinessLog::getId).list();
            if (CollUtil.isEmpty(list)) {
                AwsBusinessLog awsBusinessLog = new AwsBusinessLog();
                awsBusinessLog.setProcessId(processId);
                awsBusinessLog.setType(PurchaseTypeEnum.PURCHASE_ORDER.getValue());
                awsBusinessLog.setBusinessId(purchaseOrderId);
                iAwsBusinessLogService.save(awsBusinessLog);
            } else {
                AwsBusinessLog awsBusinessLog = list.get(0);
                awsBusinessLog.setProcessId(processId);
                iAwsBusinessLogService.updateById(awsBusinessLog);
            }
            return SingleResponse.of(processId);
        }
        return SingleResponse.buildFailure(ErrorCode.SYS_ERROR.getCode(), "工作台返回异常." + daddylabWorkbenchResponse);
    }

    private StockOutOrderProcessForm buildStockOutOrderForm(Long stockOutOrderId, Long currentUserId) {
        SingleResponse<StockOutOrderViewVO> stockOutOrderRes = stockOutOrderBizService.getStockOutOrder(stockOutOrderId);
        if (!stockOutOrderRes.isSuccess()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "退料出库单查询失败");
        }
        StockOutOrderViewVO stockOutOrder = stockOutOrderRes.getData();

        StockOutOrderProcessForm form = new StockOutOrderProcessForm();
        form.setIsPushWdt(stockOutOrder.getIsSyncWdt());
        form.setLoginUserId(currentUserId);
        form.setNo(stockOutOrder.getNo());
        form.setProcessInstId(stockOutOrder.getWorkbenchProcessId());
        form.setPurchaseOrderNo(stockOutOrder.getPurchaseOrderNo());
        form.setPurchaseOrg(stockOutOrder.getPurchaseOrganizationName());
        form.setRemark(stockOutOrder.getRemark());
        form.setReturnMode(stockOutOrder.getReturnMode());
        form.setReturnType(stockOutOrder.getReturnType());
        form.setStockOutOrg(stockOutOrder.getOrganizationName());
        Provider provider = iProviderService.getById(stockOutOrder.getProviderId());
        form.setProviderName(Objects.isNull(provider) ? StrUtil.EMPTY : provider.getName());

        final Integer businessLine = stockOutOrder.getBusinessLine();
        if (businessLine <= 1) {
            form.setBusinessLine(1);
        } else {
            form.setBusinessLine(stockOutOrder.getBusinessLine());
        }

        List<StockOutOrderDetailVO> stockOutOrderDetailList = stockOutOrder.getStockOutOrderDetailList();
        List<StockOutOrderProcessDetailFormList> detailList = stockOutOrderDetailList.stream().map(val -> {
            StockOutOrderProcessDetailFormList stock = new StockOutOrderProcessDetailFormList();
            stock.setBusinessId(val.getId());
            stock.setDeductionQuantity(val.getDeductionQuantity());
            stock.setIsGift(val.getIsGift());
            stock.setItemName(val.getItemName());
            stock.setNum(val.getReturnQuantity());
            stock.setRealNum(val.getRealReturnQuantity());
            stock.setRemark(val.getRemark());
            stock.setReplenishQuantity(val.getReplenishQuantity());
            stock.setReturnReason(val.getReturnReason());
            stock.setSkuCode(val.getItemSkuCode());
            stock.setSpecifications(val.getSpecifications());
            stock.setTaxPrice(val.getTaxPrice());
            stock.setTotalPriceTax(val.getTotalPriceTax());
            stock.setUnit(val.getStockUnit());
            stock.setValuationQuantity(val.getValuationQuantity());
            stock.setValuationUnit(val.getValuationUnit());
            stock.setWareHouseName(val.getWarehouseName());
            return stock;
        }).collect(Collectors.toList());
        form.setStockOutOrderProcessDetailFormList(detailList);

        return form;
    }

    /**
     * DIAN_SHANG(0, "电商"),
     * CHOU_JIAN(1, "老爸抽检"),
     * LV_SE_JIA_ZHUANG(2, "绿色家装"),
     * SHANG_JIA_RU_ZHU(3, "商家入驻");
     *
     * @param erpBusinessLine
     * @return 业务线 1-电商 2-绿色家装 3-商家入驻
     */
    private Integer workbenchBusinessLine(Integer erpBusinessLine) {
        switch (erpBusinessLine) {
            case 2:
                return 2;
            case 3:
                return 3;
            default:
                return 1;
        }
    }


    @Override
    public SingleResponse<String> syncStockOutOrder(Long stockOutOrderId, Long currentUserId) {
        StockOutOrderProcessForm form = buildStockOutOrderForm(stockOutOrderId, currentUserId);
        DaddylabWorkbenchResponse daddylabWorkbenchResponse = daddylabWorkbenchFeignClient.stockOutOrderLaunch(form);
        log.info("[daddylab Workbench] submitStockOutOrder res:{}", JsonUtil.toJson(daddylabWorkbenchResponse));
        if (!daddylabWorkbenchResponse.isSuccess()) {
            return SingleResponse.buildFailure(ErrorCode.SYS_ERROR.getCode(), "工作台返回异常." + daddylabWorkbenchResponse.getMsg());
        }

        String processId = daddylabWorkbenchResponse.getData();
        if (StrUtil.isNotBlank(processId)) {
            iStockOutOrderService.lambdaUpdate().set(StockOutOrder::getWorkbenchProcessId, processId)
                    .eq(StockOutOrder::getId, stockOutOrderId).update();

            List<AwsBusinessLog> list = iAwsBusinessLogService.lambdaQuery().eq(AwsBusinessLog::getBusinessId, stockOutOrderId)
                    .eq(AwsBusinessLog::getType, PurchaseTypeEnum.OUT_STOCK_PAYABLE.getValue()).orderByDesc(AwsBusinessLog::getId).list();
            if (CollUtil.isEmpty(list)) {
                AwsBusinessLog awsBusinessLog = new AwsBusinessLog();
                awsBusinessLog.setProcessId(processId);
                awsBusinessLog.setType(PurchaseTypeEnum.OUT_STOCK_PAYABLE.getValue());
                awsBusinessLog.setBusinessId(stockOutOrderId);
                iAwsBusinessLogService.save(awsBusinessLog);
            } else {
                AwsBusinessLog awsBusinessLog = list.get(0);
                awsBusinessLog.setProcessId(processId);
                iAwsBusinessLogService.updateById(awsBusinessLog);
            }
            return SingleResponse.of(processId);
        }
        return SingleResponse.buildFailure(ErrorCode.SYS_ERROR.getCode(), "工作台返回异常." + daddylabWorkbenchResponse);
    }


}
