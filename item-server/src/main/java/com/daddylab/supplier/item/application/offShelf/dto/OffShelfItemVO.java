package com.daddylab.supplier.item.application.offShelf.dto;

import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/12/5
 */
@Data
public class OffShelfItemVO {
    @ApiModelProperty("下架商品ID")
    private Long id;

    @ApiModelProperty("后端商品ID")
    private Long itemId;

    @ApiModelProperty("商品编码")
    private String itemCode;

    @ApiModelProperty("商品名称")
    private String itemName;

    @ApiModelProperty("后端商品状态")
    private Integer status;

    @ApiModelProperty(value = "预计上新时间")
    private Long shelfTime;

    @ApiModelProperty("关联款号")
    private String partnerProviderItemSn;

    /**
     * 业务类型（可能是空的，在 service 层组装）
     */
    @ApiModelProperty(value ="合作方-业务类型")
    private List<CorpBizTypeDTO> corpBizType;

    @ApiModelProperty("备注")
    private String remark;
}
