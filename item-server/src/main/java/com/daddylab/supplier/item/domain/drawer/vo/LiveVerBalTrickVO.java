package com.daddylab.supplier.item.domain.drawer.vo;

import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年12月20日 3:37 PM
 */
@Data
public class LiveVerBalTrickVO {

    @ApiModelProperty("直播话术")
    private String liveVerbalTrick;

    @ApiModelProperty("直播图片")
    private List<ItemDrawerImageVO> liveImages;

    @ApiModelProperty("直播话术ID")
    private Long id;

    @ApiModelProperty("直播话术名称")
    private String name;

    @ApiModelProperty("直播话术创建人")
    private Long createUid;

    @ApiModelProperty("直播话术负责人ID")
    private Long principalId;

    @ApiModelProperty("直播话术负责人")
    private StaffBrief principal;

    @ApiModelProperty("直播话术模块信息")
    private LiveVerbalTrickModuleInfo liveVerbalTrickModuleInfo;

    @ApiModelProperty("当前直播话术状态是否可撤回")
    private Boolean canRollbackOfLiveVerbalTrick = false;

    @ApiModelProperty(value = "直播话术进度节点信息")
    private List<ItemLaunchProgressNode> progressNodesForLiveVerbalTrick;

}
