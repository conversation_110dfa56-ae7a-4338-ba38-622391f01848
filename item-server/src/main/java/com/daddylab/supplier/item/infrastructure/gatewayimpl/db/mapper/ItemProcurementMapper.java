package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemProcurement;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 商品采购设置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Repository
public interface ItemProcurementMapper extends DaddyBaseMapper<ItemProcurement> {


    ItemProcurement selectOneByItemId(@Param("itemId") Long itemId);

}
