package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 平台商品预警
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PlatformItemWarn implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 平台商品ID
     */
    private Long platformItemId;

    /**
     * 平台商品SKU ID
     */
    private Long platformItemSkuId;

    /**
     * 预警类型 1:未匹配到后端商品SKU
     */
    private PlatformItemWarnType warnType;

    /**
     * 状态 0:未处理 1:已处理
     */
    private PlatformItemWarnStatus status;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;


}
