package com.daddylab.supplier.item.domain.dataFetch;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2022/5/7
 */
public interface PageFetcher extends Fetcher {

    /**
     * 获取全部数据的数量
     */
    default int getTotal() {
        throw new UnsupportedOperationException(
                "当前数据拉取器（" + fetchDataType().name() + "）不支持获取全部数据数量");
    }

    /**
     * 获取执行时间范围内数据的数据量
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    default int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
        throw new UnsupportedOperationException(
                "当前数据拉取器（" + fetchDataType().name() + "）不支持根据时间范围获取数据数量");
    }

    /**
     * 根据时间分页拉取
     *
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param pageIndex  当前页
     * @param pageSize   页面大小
     * @param runContext 运行环境
     */
    default void fetch(LocalDateTime startTime, LocalDateTime endTime, long pageIndex,
            long pageSize, RunContext runContext) {
        throw new UnsupportedOperationException(
                "当前数据拉取器（" + fetchDataType().name() + "）不支持根据时间范围拉取数据");
    }

    /**
     * 全量分页拉取
     *
     * @param pageIndex  当前页
     * @param pageSize   页面大小
     * @param runContext 运行环境
     */
    default void fetch(long pageIndex,
            long pageSize, RunContext runContext) {
        throw new UnsupportedOperationException("当前数据拉取器（" + fetchDataType().name() + "）不支持全量拉取");
    }
}
