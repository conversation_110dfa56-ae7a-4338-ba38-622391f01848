package com.daddylab.supplier.item.controller.handingsheet.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetActivityEvent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/9/5 11:44
 * @Description: 盘货表刷新价格参数
 */
@Data
@ApiModel("HandingSheetRefreshPriceParam-盘货表刷新价格参数")
public class HandingSheetRefreshPriceParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * {@link com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.HandingSheetActivityTypeEnum}
     */
    @ApiModelProperty(value = "活动类型（1-循环满减，2-阶梯满减）")
    private Integer activityType;

    @ApiModelProperty(value = "活动力度")
    private List<HandingSheetActivityEventVo> activityEvents;

    @ApiModelProperty(value = "活动价集合")
    private List<BigDecimal> activePrices;
}
