package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.itemSyncMiniProgram;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/11/3
 */
@Data
public class CustomAttribute {
    @ApiModelProperty(value = "属性名称")
    private String name;
    @ApiModelProperty(value = "属性值")
    private String value;
    @ApiModelProperty(value = "排序")
    private Integer sort;
}
