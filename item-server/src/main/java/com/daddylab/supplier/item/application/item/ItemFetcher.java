package com.daddylab.supplier.item.application.item;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.shaded.com.google.common.base.Supplier;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.domain.brand.gateway.BrandGateway;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.base.Joiner;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.javers.common.collections.Lists;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2021/12/31
 */
@Component
@Slf4j
public class ItemFetcher implements PageFetcher {

    @Autowired
    private WdtGoodsMapper wdtGoodsMapper;
    @Autowired
    private WdtGoodsSpecMapper wdtGoodsSpecMapper;
    @Autowired
    private IItemService itemService;
    @Autowired
    private ItemMapper itemMapper;
    @Autowired
    private IItemSkuService itemSkuService;
    @Autowired
    private IItemImageService itemImageService;
    @Autowired
    private ICategoryService categoryService;
    LoadingCache<String, Category> categoryCache = Caffeine.newBuilder()
            .maximumSize(100)
            .build(this::queryCategory);
    @Autowired
    private ICategoryAttrService categoryAttrService;
    LoadingCache<Long, List<CategoryAttr>> categoryAttrsCache = Caffeine.newBuilder()
            .maximumSize(100)
            .build(this::queryCategoryAttrs);
    @Autowired
    private IItemAttrService itemAttrService;
    @Autowired
    private IItemSkuAttrRefService itemSkuAttrRefService;
    @Autowired
    private BrandGateway brandGateway;
    @Autowired
    private IWdtProviderGoodsService wdtProviderGoodsService;
    @Autowired
    private IItemProcurementService itemProcurementService;
    @Autowired
    private IWarehouseService warehouseService;
    LoadingCache<String, Warehouse> warehouseCache = Caffeine.newBuilder()
            .maximumSize(100)
            .build(this::queryWarehouse);
    @Autowired
    private IBaseUnitService baseUnitService;
    LoadingCache<String, BaseUnit> baseUnitCache = Caffeine.newBuilder()
            .maximumSize(100)
            .build(this::queryBaseUnit);
    LoadingCache<Long, BaseUnit> baseUnitCacheById = Caffeine.newBuilder()
            .maximumSize(100)
            .build(this::queryBaseUnit);
    @Autowired
    private ProviderGateway providerGateway;
    LoadingCache<String, Provider> providerCache = Caffeine.newBuilder()
            .maximumSize(100)
            .build(this::queryProvider);
    @Autowired
    private OperateLogDomainService operateLogDomainService;
    @Autowired
    private KingDeeSkuPriceMapper kingDeeSkuPriceMapper;
    @Autowired
    private BuyerMapper buyerMapper;
    @Autowired
    private IItemPriceService itemPriceService;
    /**
     * 默认采购员ID
     */
    private Long defaultBuyerId;
    @Autowired
    RedissonClient redissonClient;

    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.ITEM;
    }

    @Override
    public int getTotal() {
        final LambdaQueryWrapper<WdtGoods> queryWrapper = Wrappers.lambdaQuery();
        return wdtGoodsMapper.selectCount(queryWrapper);
    }

    @Override
    public int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
        final LambdaQueryWrapper<WdtGoods> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ge(WdtGoods::getUpdatedAt, DateUtil.toEpochSecond(startTime));
        queryWrapper.le(WdtGoods::getUpdatedAt, DateUtil.toEpochSecond(endTime));
        return wdtGoodsMapper.selectCount(queryWrapper);
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, long pageIndex, long pageSize,
            RunContext runContext) {
        final LambdaQueryWrapper<WdtGoods> goodsQuery = Wrappers.lambdaQuery();
        goodsQuery.ge(WdtGoods::getUpdatedAt, DateUtil.toEpochSecond(startTime));
        goodsQuery.le(WdtGoods::getUpdatedAt, DateUtil.toEpochSecond(endTime));
        goodsQuery.orderByAsc(WdtGoods::getUpdatedAt);
        final Page<WdtGoods> wdtGoodsPage = query(pageIndex, pageSize, goodsQuery);
        handleResponse(wdtGoodsPage.getRecords());
    }

    @Override
    public void fetch(long pageIndex, long pageSize, RunContext runContext) {
        final LambdaQueryWrapper<WdtGoods> goodsQuery = Wrappers.lambdaQuery();
        final Page<WdtGoods> wdtGoodsPage = query(pageIndex, pageSize, goodsQuery);
        handleResponse(wdtGoodsPage.getRecords());
    }

    private Page<WdtGoods> query(long pageIndex, long pageSize,
            LambdaQueryWrapper<WdtGoods> goodsQuery) {
        final Page<WdtGoods> wdtGoodsPage = new Page<>(pageIndex, pageSize, false);
        wdtGoodsMapper.selectPage(wdtGoodsPage, goodsQuery);

        final List<WdtGoods> wdtGoodsList = wdtGoodsPage.getRecords();
        if (wdtGoodsList.isEmpty()) {
            return wdtGoodsPage;
        }
        final List<Integer> goodsIds = wdtGoodsList.stream().map(WdtGoods::getGoodsId)
                .collect(Collectors.toList());
        final LambdaQueryWrapper<WdtGoodsSpec> specQuery = Wrappers.lambdaQuery();
        specQuery.in(WdtGoodsSpec::getGoodsId, goodsIds);
        final List<WdtGoodsSpec> wdtGoodsSpecs = wdtGoodsSpecMapper.selectList(specQuery);
        final Map<Integer, List<WdtGoodsSpec>> wdtGoodsSpecListMap = wdtGoodsSpecs.stream()
                .collect(Collectors.groupingBy(WdtGoodsSpec::getGoodsId));
        wdtGoodsList.forEach(
                wdtGoods -> wdtGoods.setSpecs(wdtGoodsSpecListMap
                        .getOrDefault(wdtGoods.getGoodsId(), Collections.emptyList())));
        return wdtGoodsPage;
    }

    public int fetchByGoodsNos(List<String> goodsNos) {
        final LambdaQueryWrapper<WdtGoods> goodsQuery = Wrappers.lambdaQuery();
        goodsQuery.in(WdtGoods::getGoodsNo, goodsNos);
        final Page<WdtGoods> wdtGoodsPage = query(1, goodsNos.size(), goodsQuery);
        handleResponse(wdtGoodsPage.getRecords());
        return wdtGoodsPage.getRecords().size();
    }

    private void handleResponse(List<WdtGoods> wdtGoodsList) {
        if (CollectionUtil.isEmpty(wdtGoodsList)) {
            return;
        }
        final List<String> goodsNos = wdtGoodsList.stream().map(WdtGoods::getGoodsNo)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(goodsNos)) {
            return;
        }

        //根据供货指定编码和商品内码查询所有商品的ID、编码字段
        final List<Item> itemCodeList = itemMapper.selectIdModelByCodeBatchIncludeDeleted(goodsNos);

        //商品ID映射到商品
        final Map<Long, Item> itemMap = itemCodeList.stream()
                .collect(Collectors.toMap(Item::getId, Function.identity()));

        // 商品内码映射到商品ID
        final Map<String, Long> itemIdsMap =
                itemCodeList.stream()
                        .filter(item -> Objects.equals(item.getIsDel(), 0))
                        .collect(Collectors.toMap(Item::getCode, Item::getId, (a, b) -> a));

        //供货商指定编码映射到商品ID
        final Map<String, Long> providerSpecifiedCodeToItemIdsMap = itemCodeList.stream()
                .filter(v -> StringUtil.isNotEmpty(v.getProviderSpecifiedCode()))
                .collect(Collectors.toMap(Item::getProviderSpecifiedCode, Item::getId));

        //提取出所有规格编码
        final List<String> specNos = wdtGoodsList.stream().flatMap(wdtGoods -> wdtGoods.getSpecs()
                .stream()).map(WdtGoodsSpec::getSpecNo).distinct()
                .collect(Collectors.toList());

        //查询所有商品SKU（包括已经逻辑删除的）
        final List<ItemSku> itemSkusList = itemMapper.selectSkuIdModelByCodeBatchIncludeDeleted(
                specNos);

        // 根据规格编码（内码）做映射
        final Map<String, ItemSku> skusMap =
                itemSkusList.stream()
                        .filter(itemSku -> Objects.equals(itemSku.getIsDel(), 0))
                        .collect(
                                Collectors.toMap(
                                        ItemSku::getSkuCode, Function.identity(), (a, b) -> a));

        // 根据规格的供货指定编码做映射
        final Map<String, ItemSku> skusProviderCodeMap =
                itemSkusList.stream()
                        .filter(itemSku -> Objects.equals(itemSku.getIsDel(), 0))
                        .collect(
                                Collectors.toMap(
                                        ItemSku::getSupplierCode, Function.identity(), (a, b) -> a));

        for (WdtGoods wdtGoods : wdtGoodsList) {
            Item item = null;
            final ArrayList<String> changeLogs = new ArrayList<>();
            final RLock lock = redissonClient.getLock("ITEM_FETCH_LOCK:" + wdtGoods.getGoodsNo());
            if (!lock.tryLock()) {
                log.info("商品拉取已在执行中，跳过处理 {}({})", wdtGoods.getGoodsName(), wdtGoods.getGoodsNo());
                continue;
            }
            try {
                boolean isCreate = false;
                //优先根据供货商指定编码来判断
                if (providerSpecifiedCodeToItemIdsMap.containsKey(wdtGoods.getGoodsNo())) {
                    final Long id = providerSpecifiedCodeToItemIdsMap.get(wdtGoods.getGoodsNo());
                    if (NumberUtil.isPositive(itemMap.get(id).getIsDel())) {
                        log.info("后端商品已被删除，跳过同步，商品编码：{}，商品名称：{}", wdtGoods.getGoodsNo(),
                                wdtGoods.getGoodsName());
                        continue;
                    }
                    item = itemService.getById(id);
                    updateItem(wdtGoods, item, changeLogs);
                }
                //根据供货商指定编码未匹配到再根据商品编码匹配
                else if (itemIdsMap.containsKey(wdtGoods.getGoodsNo())) {
                    final Long id = itemIdsMap.get(wdtGoods.getGoodsNo());
                    if (NumberUtil.isPositive(itemMap.get(id).getIsDel())) {
                        log.info("后端商品已被删除，跳过同步，商品编码：{}，商品名称：{}", wdtGoods.getGoodsNo(),
                                wdtGoods.getGoodsName());
                        continue;
                    }
                    item = itemService.getById(id);
                    updateItem(wdtGoods, item, changeLogs);
                }
                else {
                    //SPU不存在，SKU已存在，可能是以单品形式同步到旺店通的商品（这种类型的商品在旺店通那边商品编码和规格编码都是我们的规格编码）
                    final Optional<ItemSku> anySkuOpt = wdtGoods.getSpecs().stream()
                            .map(WdtGoodsSpec::getSpecNo).map(skusMap::get).filter(Objects::nonNull)
                            .findFirst();
                    final Optional<ItemSku> anySkuOpt1 = wdtGoods.getSpecs().stream()
                            .map(WdtGoodsSpec::getSpecNo).map(skusProviderCodeMap::get).filter(Objects::nonNull)
                            .findFirst();
                    if (anySkuOpt.isPresent() || anySkuOpt1.isPresent()) {
                        final ItemSku anyItemSku = anySkuOpt.orElse(anySkuOpt1.get());
                        final Long itemId = anyItemSku.getItemId();
                        final ItemSku firstSku = itemSkuService.lambdaQuery()
                                .eq(ItemSku::getItemId, itemId).orderByAsc(ItemSku::getId)
                                .last("limit 1").one();
                        //以单品形式同步的，我们的商品和旺店通货品是一对多的，那么同步SPU信息的时候就需要指定特定的一个旺店通货品来同步，这边暂时
                        //选定为与当前SPU的ID最小的SKU对应的货品做同步
                        item = itemService.lambdaQuery().eq(Item::getId, itemId)
                                .oneOpt().orElse(null);
                        if (Objects.isNull(item)) {
                            log.info("后端商品已被删除，跳过同步，商品编码：{}，商品名称：{}", wdtGoods.getGoodsNo(),
                                    wdtGoods.getGoodsName());
                            continue;
                        }
                        if (Objects.equals(firstSku.getSkuCode(), wdtGoods.getGoodsNo())) {
                            updateItem(wdtGoods, item, changeLogs);
                        }
                    } else {
                        isCreate = true;
                        item = saveItem(wdtGoods);
                    }
                }
                if (item == null) {
                    continue;
                }
                if (isCreate) {
                    changeLogs.add("系统自动同步旺店通货品档案创建");
                }
                final ItemProcurement itemProcurement = saveItemProcurement(wdtGoods, item,
                        changeLogs);
                final List<ItemSku> itemSkus = saveItemSkus(wdtGoods, item, itemProcurement,
                        changeLogs);
                if (itemSkus.isEmpty()) {
                    log.warn("商品规格为空，商品编号:{}，商品名称:{}", wdtGoods.getGoodsNo(),
                            wdtGoods.getGoodsName());
                    continue;
                }
                if (isCreate) {
                    saveItemImages(wdtGoods, item);
                    saveItemAttrs(item, itemSkus);
                }
                //savePriceAndBuyer(item, itemSkus, wdtGoods, itemProcurement, changeLogs);
            } catch (Throwable throwable) {
                log.error("系统自动同步旺店通货品档案到后端商品时遇到异常，商品信息:{}，错误信息:{}", wdtGoods,
                        ExceptionUtil.getMessage(throwable), throwable);
                Alert.text(MessageRobotCode.GLOBAL,
                        StrUtil.format("系统自动同步旺店通货品档案到后端商品时遇到异常，商品编号:{}，商品名称:{}，错误信息:{}",
                                wdtGoods.getGoodsNo(), wdtGoods.getGoodsName(),
                                ExceptionUtil.stacktraceToOneLineString(throwable)));
                break;
            } finally {
                lock.unlock();
                if (!changeLogs.isEmpty()) {
                    operateLogDomainService.addOperatorLog(0L, OperateLogTarget.ITEM, item.getId(),
                            "系统自动同步旺店通货品档案改动，改动字段：" + Joiner
                                    .on('、').join(changeLogs));
                }
            }
        }
    }

    private void updateItem(WdtGoods wdtGoods, Item item, ArrayList<String> changeLogs) {
        boolean itemIsChange = false;
        if (!Objects.equals(item.getName(), wdtGoods.getGoodsName())) {
            item.setName(wdtGoods.getGoodsName());
            changeLogs.add("商品名称");
            itemIsChange = true;
        }
        if (!Objects.equals(item.getShortName(), wdtGoods.getShortName())) {
            item.setShortName(wdtGoods.getShortName());
            changeLogs.add("商品简称");
            itemIsChange = true;
        }
        //旺店通品牌名称不为空时才检查是否更改
        final String brandName = wdtGoods.getBrandName();
        if (StringUtil.isNotBlank(brandName)) {
            Long brandId = brandGateway.getBrandId(brandName);
            if (!NumberUtil.isPositive(brandId)) {
                final Long newBrandId = brandGateway.syncWdtBrand(brandName);
                if (NumberUtil.isPositive(newBrandId)) {
                    brandId = newBrandId;
                }
            }
            if (NumberUtil.isPositive(brandId) && !Objects.equals(item.getBrandId(), brandId)) {
                item.setBrandId(brandId);
                changeLogs.add("品牌");
                itemIsChange = true;
            }
        }
        //只有商品供应商为空时才尝试根据旺店通的供应商商品来匹配供应商
        if (NumberUtil.isZeroOrNull(item.getProviderId())) {
            final Long providerId = queryProviderId(wdtGoods.getGoodsNo());
            if (Objects.nonNull(providerId)) {
                item.setProviderId(providerId);
                changeLogs.add("供应商");
                itemIsChange = true;
            }
        }
        final long goodsCreatedEpochSecond = DateUtil.toEpochSecond(wdtGoods.getGoodsCreated());
        if (goodsCreatedEpochSecond > 0 && !ObjectUtil
                .equals(item.getCreatedAt(), goodsCreatedEpochSecond)) {
            item.setCreatedAt(goodsCreatedEpochSecond);
            itemIsChange = true;
        }
        final boolean isDeletedR = NumberUtil.isPositive(wdtGoods.getDeleted());
        if (!ObjectUtil.equal(isDeletedR, item.getIsDeletedInWdt())) {
            item.setIsDeletedInWdt(isDeletedR);
            if (isDeletedR) {
                changeLogs.add("系统自动同步旺店通货品档案删除");
            } else {
                changeLogs.add("系统自动同步旺店通货品档案从删除状态恢复");
            }
            itemIsChange = true;
        }
        if (itemIsChange) {
            itemService.updateById(item);
        }
    }

    /**
     * 默认采购员ID
     */
    private Long defaultBuyerId() {
        if (defaultBuyerId == null) {
            final LambdaQueryWrapper<Buyer> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(Buyer::getName, Arrays.asList("蔡慧萍", "蔡蔡")).last("limit 1");
            final Buyer buyer = buyerMapper.selectOne(queryWrapper);
            defaultBuyerId = (buyer != null ? buyer.getId() : 0L);
        }
        return defaultBuyerId;
    }

    private void savePriceAndBuyer(Item item, List<ItemSku> itemSkus, WdtGoods wdtGoods,
            ItemProcurement itemProcurement, List<String> changeLogs) {
        final KingDeeSkuPrice kingDeeSkuPrice = queryKingdeePrice(itemSkus);

        //如果当前商品没有采购员，尝试补全
        if (itemProcurement.getBuyerId() == null
                || itemProcurement.getBuyerId() == 0) {
            String buyerName = "默认采购员";
            itemProcurement.setBuyerId(defaultBuyerId());
            if (kingDeeSkuPrice != null && !NumberUtil
                    .isZeroOrNull(kingDeeSkuPrice.getKingDeeBuyerId())) {
                final LambdaQueryWrapper<Buyer> queryWrapper = Wrappers.lambdaQuery();
                queryWrapper.eq(Buyer::getKingDeeId, kingDeeSkuPrice.getKingDeeBuyerId())
                        .last("limit 1");
                final Buyer buyer = buyerMapper.selectOne(queryWrapper);
                if (buyer != null) {
                    buyerName = buyer.getName();
                    itemProcurement.setBuyerId(buyer.getId());
                }
            }
            itemProcurementService.updateById(itemProcurement);
            changeLogs.add(StringUtil.format("采购员（无 -> {}）", buyerName));
        }
        final LambdaQueryWrapper<ItemPrice> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ItemPrice::getItemId, item.getId());
        queryWrapper.in(ItemPrice::getType,
                Arrays.asList(ItemPriceType.PROCUREMENT.getValue(),
                        ItemPriceType.DAILY.getValue()));
        final List<ItemPrice> itemPrices = itemPriceService.list(queryWrapper);
        final ItemPrice itemProcurementPrice = itemPrices.stream()
                .filter(ip -> ip.getType() == ItemPriceType.PROCUREMENT).findFirst()
                .orElseGet(() -> {
                    final ItemPrice itemPrice = new ItemPrice();
                    itemPrice.setItemId(item.getId());
                    itemPrice.setType(ItemPriceType.PROCUREMENT);
                    itemPrice.setStartTime(1640966400L);
                    itemPrice.setEndTime(4070880000L);
                    itemPrice.setCustomName(ItemPriceType.PROCUREMENT.getDesc());
                    itemPrice.setRang(ItemPriceRang.PROCUREMENT);
                    return itemPrice;
                });
        if (StrUtil.isBlank(itemProcurementPrice.getCustomName())) {
            itemProcurementPrice.setCustomName(ItemPriceType.PROCUREMENT.getDesc());
        }
        if (kingDeeSkuPrice != null && (itemProcurementPrice.getPrice() == null
                || itemProcurementPrice.getPrice().compareTo(BigDecimal.ZERO) == 0)) {
            try {
                itemProcurementPrice
                        .setPrice(NumberUtil.toBigDecimal(kingDeeSkuPrice.getCostPrice()));
                changeLogs.add(StringUtil.format("采购成本（无 -> {}）", itemProcurementPrice.getPrice()));
            } catch (NumberFormatException e) {
                itemProcurementPrice.setPrice(BigDecimal.ZERO);
            }
        }
        final ItemPrice itemDailyPrice = itemPrices.stream()
                .filter(ip -> ip.getType() == ItemPriceType.DAILY).findFirst()
                .orElseGet(() -> {
                    final ItemPrice itemPrice = new ItemPrice();
                    itemPrice.setItemId(item.getId());
                    itemPrice.setType(ItemPriceType.DAILY);
                    itemPrice.setCustomName(ItemPriceType.DAILY.getDesc());
                    itemPrice.setRang(ItemPriceRang.SALES);
                    return itemPrice;
                });
        if (StrUtil.isBlank(itemDailyPrice.getCustomName())) {
            itemDailyPrice.setCustomName(ItemPriceType.DAILY.getDesc());
        }
        if (CollUtil.isNotEmpty(wdtGoods.getSpecs()) && (itemDailyPrice.getPrice() == null
                || itemDailyPrice.getPrice().compareTo(BigDecimal.ZERO) == 0)) {
            final BigDecimal retailPrice = wdtGoods.getSpecs().get(0).getRetailPrice();
            if (NumberUtil.isPositive(retailPrice)) {
                itemDailyPrice.setPrice(retailPrice);
                changeLogs.add(StringUtil.format("日销价（无 -> {}）", itemDailyPrice.getPrice()));
            }
        }
        try {
            itemPriceService.saveOrUpdateBatch(Arrays.asList(itemProcurementPrice, itemDailyPrice));
        } catch (DuplicateKeyException e) {
            log.warn("商品价格并发修改异常，商品编码：{}，采购成本：{}，日常销售价：{}", item.getCode(), itemProcurementPrice,
                    itemDailyPrice);
        }

        boolean skuChange = false;
        for (ItemSku skus : itemSkus) {
            if (NumberUtil.isPositive(itemDailyPrice.getPrice()) && NumberUtil
                    .isZeroOrNull(skus.getSalePrice())) {
                changeLogs.add(StringUtil
                        .format("规格（{}）日销价（{} -> {}）", skus.getSkuCode(), NumberUtil.format(skus.getSalePrice()),
                                NumberUtil.format(itemDailyPrice.getPrice())));
                skus.setSalePrice(itemDailyPrice.getPrice());
                skuChange = true;
            }
            if (NumberUtil.isPositive(itemProcurementPrice.getPrice()) && NumberUtil.isZeroOrNull(
                    skus.getCostPrice())) {
                changeLogs.add(StringUtil
                        .format("规格（{}）采购成本（{} -> {}）", skus.getSkuCode(), NumberUtil.format(skus.getCostPrice()),
                                NumberUtil.format(itemProcurementPrice.getPrice())));
                skus.setCostPrice(itemProcurementPrice.getPrice());
                skuChange = true;
            }
        }
        if (skuChange) {
            itemSkuService.updateBatchById(itemSkus);
        }
    }

    private KingDeeSkuPrice queryKingdeePrice(List<ItemSku> itemSkus) {
        if (CollUtil.isEmpty(itemSkus)) {
            return null;
        }
        final LambdaQueryWrapper<KingDeeSkuPrice> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(KingDeeSkuPrice::getSkuCode, itemSkus.get(0).getSkuCode()).last("limit 1");
        return kingDeeSkuPriceMapper.selectOne(queryWrapper);
    }

    private ItemProcurement saveItemProcurement(WdtGoods wdtGoods, Item item,
            List<String> changeLogs) {
        ItemProcurement itemProcurement = itemProcurementService.lambdaQuery()
                .eq(ItemProcurement::getItemId, item.getId()).one();
        final Optional<Warehouse> warehouseOptional = getWarehouse(
                wdtGoods);
        final Optional<BaseUnit> baseUnit = getBaseUnit(wdtGoods.getUnitName());
        final String warehouseNo = warehouseOptional.map(Warehouse::getNo).orElse("");
        final Long baseUnitId = baseUnit.map(BaseUnit::getId).orElse(0L);
        final String taxRateCode = wdtGoods.getProp2();
        if (itemProcurement == null) {
            itemProcurement = new ItemProcurement();
            itemProcurement.setItemId(item.getId());
            itemProcurement.setProviderId(item.getProviderId());
            itemProcurement.setBuyerId(defaultBuyerId());
            itemProcurement.setIsGift(0);
            itemProcurement.setMainItemId(0L);
            itemProcurement.setDelivery("");
            itemProcurement.setWarehouseNo(warehouseNo);
            itemProcurement.setTaxRateCode(taxRateCode);
            item.setWarehouseNo(warehouseNo);
            updateItemWarehouse(item);

            itemProcurement.setBaseUnitId(baseUnitId);
            if (StrUtil.isNotBlank(wdtGoods.getProp3()) && ReUtil
                    .isMatch("\\d+", wdtGoods.getProp3())) {
                itemProcurement.setRate(new BigDecimal(wdtGoods.getProp3())
                        .divide(new BigDecimal(100), 2, RoundingMode.UNNECESSARY));
            }
            itemProcurementService.saveOrUpdate(itemProcurement);
        } else {
            boolean change = false;
            if (!warehouseNo.isEmpty() && !warehouseNo.equals(itemProcurement.getWarehouseNo())) {
                changeLogs.add(StringUtil
                        .format("仓库（{} -> {}）", itemProcurement.getWarehouseNo(), warehouseNo));
                itemProcurement.setWarehouseNo(warehouseNo);
                item.setWarehouseNo(warehouseNo);
                updateItemWarehouse(item);
                change = true;
            }
            if (baseUnitId > 0 && !baseUnitId.equals(itemProcurement.getBaseUnitId())) {
                final Optional<BaseUnit> baseUnitL = Optional
                        .ofNullable(itemProcurement.getBaseUnitId())
                        .filter(NumberUtil::isPositive).flatMap(this::getBaseUnit);
                itemProcurement.setBaseUnitId(baseUnitId);
                changeLogs.add(StringUtil.format("基础单位（{} -> {}）",
                        baseUnitL.map(BaseUnit::getName).orElse(""),
                        baseUnit.map(BaseUnit::getName).orElse("")));
                change = true;
            }
            if (StrUtil.isNotBlank(wdtGoods.getProp3()) && ReUtil
                    .isMatch("\\d+", wdtGoods.getProp3())) {
                final BigDecimal rate = new BigDecimal(wdtGoods.getProp3())
                        .divide(new BigDecimal(100), 2, RoundingMode.UNNECESSARY);
                if (!ObjectUtil.equals(itemProcurement.getRate(), rate)) {
                    changeLogs.add(StringUtil
                            .format("税率（{} -> {}）", itemProcurement.getRate(), rate));
                    itemProcurement.setRate(rate);
                    change = true;
                }
            }
            if (StrUtil.isNotBlank(taxRateCode) && !StringUtil
                    .equals(taxRateCode, itemProcurement.getTaxRateCode())) {
                changeLogs.add(StringUtil
                        .format("税务编码（{} -> {}）", itemProcurement.getTaxRateCode(), taxRateCode));
                itemProcurement.setTaxRateCode(taxRateCode);
                change = true;
            }
            if (change) {
                itemProcurementService.saveOrUpdate(itemProcurement);
            }
        }
        return itemProcurement;
    }

    private void updateItemWarehouse(Item item) {
        itemService.lambdaUpdate().eq(Item::getId, item.getId())
                .set(Item::getWarehouseNo, item.getWarehouseNo()).update();
    }

    private Optional<Warehouse> getWarehouse(WdtGoods wdtGoods) {
        return Optional.ofNullable(warehouseCache.get(wdtGoods.getProp1()));
    }

    private Warehouse queryWarehouse(String warehouseName) {
        return warehouseService.lambdaQuery()
                .eq(Warehouse::getName, warehouseName).last("limit 1").one();
    }

    private void saveItemAttrs(Item item, List<ItemSku> itemSkus) {
        if (NumberUtil.isPositive(item.getCategoryId())) {
            List<CategoryAttr> categoryAttrs = getCategoryAttrs(item.getCategoryId());
            CategoryAttr colorAttr = categoryAttrs.stream().filter(ca -> ca.getName().equals("颜色"))
                    .findFirst().orElse(null);
            final ArrayList<CategoryAttr> newCategoryAttrs = new ArrayList<>();
            if (colorAttr == null) {
                colorAttr = new CategoryAttr();
                colorAttr.setCategoryId(item.getCategoryId());
                colorAttr.setName("颜色");
                colorAttr.setIsRequired(1);
                newCategoryAttrs.add(colorAttr);
            }
            CategoryAttr specAttr = categoryAttrs.stream().filter(ca -> ca.getName().equals("规格"))
                    .findFirst().orElse(null);
            if (specAttr == null) {
                specAttr = new CategoryAttr();
                specAttr.setCategoryId(item.getCategoryId());
                specAttr.setName("规格");
                specAttr.setIsRequired(1);
                newCategoryAttrs.add(specAttr);
            }
            if (!newCategoryAttrs.isEmpty()) {
                try {
                    categoryAttrService.saveBatch(newCategoryAttrs);
                } catch (DuplicateKeyException e) {
                    categoryAttrs = queryCategoryAttrs(
                            item.getCategoryId());
                    colorAttr = categoryAttrs.stream().filter(ca -> ca.getName().equals("颜色"))
                            .findFirst().orElseThrow(() -> new RuntimeException("规格查询异常！"));
                    specAttr = categoryAttrs.stream()
                            .filter(ca -> ca.getName().equals("规格"))
                            .findFirst().orElseThrow(() -> new RuntimeException("规格查询异常！"));
                }
            }

            List<ItemAttr> itemAttrs = itemAttrService.lambdaQuery()
                    .eq(ItemAttr::getItemId, item.getId())
                    .in(ItemAttr::getAttrId, Arrays.asList(colorAttr.getId(), specAttr.getId()))
                    .list();
            if (itemAttrs.isEmpty()) {
                itemAttrs = new ArrayList<>();
                final ItemAttr itemColorAttr = new ItemAttr();
                itemColorAttr.setItemId(item.getId());
                itemColorAttr.setAttrId(colorAttr.getId());
                itemColorAttr.setCategoryId(item.getCategoryId());
                itemColorAttr.setAttrValue("无");
                itemAttrs.add(itemColorAttr);

                for (ItemSku skus : itemSkus) {
                    final ItemAttr itemSpecAttr = new ItemAttr();
                    itemSpecAttr.setItemId(item.getId());
                    itemSpecAttr.setAttrId(specAttr.getId());
                    itemSpecAttr.setCategoryId(item.getCategoryId());
                    itemSpecAttr.setAttrValue(skus.getSpecifications());
                    itemAttrs.add(itemSpecAttr);
                }
                itemAttrService.saveBatch(itemAttrs);
            }

            CategoryAttr finalColorAttr = colorAttr;
            CategoryAttr finalSpecAttr = specAttr;
            List<ItemSkuAttrRef> itemSkuAttrRefs = itemSkuAttrRefService.lambdaQuery()
                    .eq(ItemSkuAttrRef::getItemId, item.getId())
                    .in(ItemSkuAttrRef::getItemSkuId, itemSkus.stream().map(ItemSku::getId).collect(
                            Collectors.toList()))
                    .in(ItemSkuAttrRef::getItemAttrId,
                            itemAttrs.stream().map(ItemAttr::getId).collect(
                                    Collectors.toList()))
                    .list();
            List<ItemSkuAttrRef> newItemSkuAttrRefs = new ArrayList<>();
            if (itemSkuAttrRefs.isEmpty()) {
                for (ItemSku skus : itemSkus) {
                    final ItemAttr itemColorAttr = itemAttrs.stream()
                            .filter(ia -> Objects.equals(ia.getAttrId(), finalColorAttr.getId()))
                            .findFirst()
                            .orElseThrow(() -> new RuntimeException("创建商品规格与商品属性的关联异常，未找到商品颜色属性"));
                    ItemSkuAttrRef itemSkuAttrRef = itemSkuAttrRefs.stream()
                            .filter(ref -> ref.getItemSkuId().equals(skus.getId()) && ref
                                    .getItemAttrId().equals(itemColorAttr.getId())).findFirst()
                            .orElse(null);
                    if (itemSkuAttrRef == null) {
                        itemSkuAttrRef = new ItemSkuAttrRef();
                        itemSkuAttrRef.setItemId(item.getId());
                        itemSkuAttrRef.setItemSkuId(skus.getId());
                        itemSkuAttrRef.setItemAttrId(itemColorAttr.getId());
                        newItemSkuAttrRefs.add(itemSkuAttrRef);
                    }
                    final ItemAttr itemSpecAttr = itemAttrs.stream()
                            .filter(ia -> Objects.equals(ia.getAttrId(), finalSpecAttr.getId())
                                    && ia.getAttrValue().equals(
                                    skus.getSpecifications()))
                            .findFirst()
                            .orElseThrow(() -> new RuntimeException("创建商品规格与商品属性的关联异常，未找到商品颜色属性"));
                    ItemSkuAttrRef itemSkuSpecAttrRef = itemSkuAttrRefs.stream()
                            .filter(ref -> ref.getItemSkuId().equals(skus.getId()) && ref
                                    .getItemAttrId().equals(itemSpecAttr.getId())).findFirst()
                            .orElse(null);
                    if (itemSkuSpecAttrRef == null) {
                        itemSkuSpecAttrRef = new ItemSkuAttrRef();
                        itemSkuSpecAttrRef.setItemId(item.getId());
                        itemSkuSpecAttrRef.setItemSkuId(skus.getId());
                        itemSkuSpecAttrRef.setItemAttrId(itemAttrs.stream()
                                .filter(i -> i.getAttrValue().equals(skus.getSpecifications()))
                                .findFirst()
                                .map(ItemAttr::getId).orElseThrow(() ->
                                        new RuntimeException(
                                                "处理商品规格属性异常，未找到属性值为" + skus.getSpecifications()
                                                        + "的商品属性")));
                        newItemSkuAttrRefs.add(itemSkuSpecAttrRef);
                    }
                }
                if (!newItemSkuAttrRefs.isEmpty()) {
                    itemSkuAttrRefService.saveBatch(newItemSkuAttrRefs);
                }
            }
        }
    }

    private List<ItemSku> saveItemSkus(WdtGoods wdtGoods, Item item,
            ItemProcurement itemProcurement, List<String> changeLogs) {
        if (CollUtil.isEmpty(wdtGoods.getSpecs())) {
            return Collections.emptyList();
        }
        final List<String> specNos = wdtGoods.getSpecs().stream().map(WdtGoodsSpec::getSpecNo)
                .collect(Collectors.toList());
        final List<ItemSku> itemSkuList = itemMapper.selectSkuIdModelByCodeBatchIncludeDeleted(
                specNos);
        final Map<String, ItemSku> itemSkuMap = itemSkuList.stream()
                .collect(Collectors.toMap(ItemSku::getSkuCode, Function
                        .identity()));

        final Optional<BaseUnit> baseUnit = getBaseUnit(wdtGoods.getUnitName());
        final List<ItemSku> itemSkus = new ArrayList<>();
        for (WdtGoodsSpec spec : wdtGoods.getSpecs()) {
            ItemSku itemSku = itemSkuMap.get(spec.getSpecNo());
            if (itemSku == null) {
                if (!itemSkuList.isEmpty()) {
                    //旺店通新增单品不做同步（因为在旺店通新增的没有关联属性，同步回来的规格数据是不完整的）
                    continue;
                }
                //首次新增商品时需创建SKU
                itemSku = new ItemSku();
                itemSku.setItemId(item.getId());
                itemSku.setSpecifications(spec.getSpecName());
                itemSku.setProviderSpecifiedCode("");
                itemSku.setSkuCode(spec.getSpecNo());
                itemSku.setKingDeeId("");
                itemSku.setBarCode(spec.getBarcode());
                itemSku.setUnit(baseUnit.map(BaseUnit::getName).orElse(""));
                itemSku.setProviderId(item.getProviderId());
                itemSku.setCategoryId(item.getCategoryId());
                itemSku.setWarehouseNo(item.getWarehouseNo());
                itemSku.setTaxRate(itemProcurement.getRate());
                itemSku.setCreatedAt(DateUtil.toEpochSecond(spec.getSpecCreated()));
                itemSku.setCreatedUid(0L);
                itemSku.setUpdatedAt(DateUtil.toEpochSecond(spec.getSpecModified()));
                itemSku.setUpdatedUid(0L);
                itemSku.setGoodsType(mapGoodsType(spec.getGoodsLabel()));
            } else if (NumberUtil.isPositive(itemSku.getIsDel())) {
                //SKU在本地已经被删除的不做同步
                log.info("后端商品规格已被删除，跳过同步，商品编码：{}，商品名称：{}，规格编码：{}，规格名称：{}", wdtGoods.getGoodsNo(),
                        wdtGoods.getGoodsName(), spec.getSpecNo(), spec.getSpecName());
                continue;
            } else {
                final String barCodeL = Optional.ofNullable(itemSku.getBarCode()).orElse("");
                final String barCodeR = Optional.ofNullable(spec.getBarcode()).orElse("");
                if (StringUtil.isNotBlank(barCodeR) &&
                        !StringUtil.equals(barCodeL, barCodeR)) {
                    itemSku.setBarCode(barCodeR);
                    changeLogs.add(StringUtil
                            .format("规格（{}）：条码（{} -> {}）", itemSku.getSkuCode(), barCodeL, barCodeR));
                }
                // 从wdt取数据不做sku单位的更新
                /*if (baseUnit.isPresent() && !Objects
                        .equals(itemSku.getUnit(), baseUnit.get().getName())) {
                    itemSku.setUnit(baseUnit.map(BaseUnit::getName).orElse(""));
                }*/
                if (NumberUtil.isPositive(item.getProviderId()) && !Objects
                        .equals(item.getProviderId(), itemSku.getProviderId())) {
                    itemSku.setProviderId(item.getProviderId());
                }
                if (StringUtil.isNotBlank(item.getWarehouseNo()) && !Objects
                        .equals(item.getWarehouseNo(), itemSku.getWarehouseNo())) {
                    itemSku.setWarehouseNo(item.getWarehouseNo());
                }
                if (NumberUtil.isPositive(itemSku.getTaxRate()) && !Objects
                        .equals(itemSku.getTaxRate(), itemProcurement.getRate())) {
                    itemSku.setTaxRate(itemProcurement.getRate());
                }
                final GoodsType goodsType = mapGoodsType(spec.getGoodsLabel());
                final GoodsType goodsTypeOld = itemSku.getGoodsType();
                if (goodsType != goodsTypeOld) {
                    itemSku.setGoodsType(goodsType);
                    changeLogs.add(StringUtil
                            .format("规格（{}）：商品标签（{} -> {}）", itemSku.getSkuCode(), goodsTypeOld, goodsType));
                }
                itemSku.setUpdatedAt(DateUtil.toEpochSecond(spec.getSpecModified()));
                itemSku.setUpdatedUid(0L);
            }
            itemSkus.add(itemSku);
        }
        itemSkuService.saveOrUpdateBatch(itemSkus);
        return itemSkus;

    }

    @NonNull
    private static GoodsType mapGoodsType(String goodsLabel) {
        return Optional.ofNullable(goodsLabel)
                       .filter(StringUtil::isNotBlank)
                       .map(v -> StringUtil.split(v, ","))
                       .filter(v -> !v.isEmpty())
                       .map(v -> v.get(0))
                       .map(v -> IEnum.getEnumByDesc(GoodsType.class, v))
                       .orElse(GoodsType.DEFAULT);
    }

    private void saveItemImages(WdtGoods wdtGoods, Item item) {
        if (CollUtil.isEmpty(wdtGoods.getSpecs())) {
            return;
        }
        final LambdaQueryWrapper<ItemImage> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ItemImage::getItemId, item.getId()).last("limit 1");
        final int count = itemImageService.count(wrapper);
        if (count > 0) {
            return;
        }
        List<ItemImage> itemImages = new ArrayList<>();
        for (WdtGoodsSpec spec : wdtGoods.getSpecs()) {
            if (StrUtil.isNotBlank(spec.getImgUrl())) {
                final ItemImage itemImage = new ItemImage();
                itemImage.setItemId(item.getId());
                itemImage.setImageUrl(spec.getImgUrl());
                itemImage.setType(ItemImageType.COMMON);
                itemImages.add(itemImage);
            }
        }
        if (!itemImages.isEmpty()) {
            itemImageService.saveBatch(itemImages);
        }
    }

    private List<CategoryAttr> getCategoryAttrs(Long categoryId) {
        return categoryAttrsCache.get(categoryId);
    }

    private List<CategoryAttr> queryCategoryAttrs(Long categoryId) {
        final LambdaQueryWrapper<CategoryAttr> categoryAttrQuery = Wrappers.lambdaQuery();
        categoryAttrQuery.eq(CategoryAttr::getCategoryId, categoryId);
        categoryAttrQuery.in(CategoryAttr::getName, Lists.asList("颜色", "规格"));
        return categoryAttrService.list(categoryAttrQuery);
    }

    private Optional<BaseUnit> getBaseUnit(String unitName) {
        return Optional.ofNullable(baseUnitCache.get(unitName));
    }

    private BaseUnit queryBaseUnit(String unitName) {
        return baseUnitService.lambdaQuery().eq(BaseUnit::getName, unitName).last("limit 1").one();
    }

    private Optional<BaseUnit> getBaseUnit(Long id) {
        return Optional.ofNullable(baseUnitCacheById.get(id));
    }

    private BaseUnit queryBaseUnit(Long id) {
        return baseUnitService.lambdaQuery().eq(BaseUnit::getId, id).last("limit 1").one();
    }

    private Item saveItem(WdtGoods wdtGoods) {
        try {
            final Category category = getCategory(wdtGoods.getClassName());
            final Long brandId = brandGateway.getBrandId(wdtGoods.getBrandName());
            final Long providerId = queryProviderId(wdtGoods.getGoodsNo());

            final Item item = new Item();
            item.setName(wdtGoods.getGoodsName());
            item.setShortName(wdtGoods.getShortName());
            item.setPartnerProviderItemSn("");
            item.setCategoryId(category != null ? category.getId() : 0L);
            item.setBrandId(brandId);
            item.setProviderId(providerId);
            item.setProviderSpecifiedCode("");
            item.setStatus(0);
            item.setStatusRemark(wdtGoods.getRemark());
            item.setEstimateSaleTime(0L);
            item.setCode(wdtGoods.getGoodsNo());
            item.setIsGift(0);
            item.setParentItemId(0L);
            item.setParentCode("");
            item.setCreatedAt(
                    DateUtil.toEpochSecond(wdtGoods.getGoodsCreated(), DateUtil.currentTime()));
            item.setDeletedAt(0L);

            item.setSource(DataSource.WDT);
            itemService.save(item);
            return item;
        } catch (DuplicateKeyException e) {
            log.warn("商品唯一键冲突，商品数据:{}，错误信息:{}", wdtGoods, ExceptionUtil.getMessage(e));
            return null;
        }
    }

    private Category getCategory(String className) {
        return categoryCache.get(className);
    }

    private Category queryCategory(String className) {
        final LambdaQueryWrapper<Category> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Category::getName, className);
        queryWrapper.last("LIMIT 1");
        return categoryService.getOne(queryWrapper);
    }

    private Long queryProviderId(String goodsNo) {
        final LambdaQueryWrapper<WdtProviderGoods> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(WdtProviderGoods::getGoodsNo, goodsNo);
        final List<WdtProviderGoods> list = wdtProviderGoodsService.list(queryWrapper);
        if (list.isEmpty()) {
            return 0L;
        }
        final Function<WdtProviderGoods, Long> updateAtGetter = (WdtProviderGoods wdtProviderGoods) -> {
            if (NumberUtil.isPositive(wdtProviderGoods.getUpdatedAt())) {
                return wdtProviderGoods.getUpdatedAt();
            } else {
                return wdtProviderGoods.getCreatedAt();
            }
        };
        final Comparator<WdtProviderGoods> comparator = Comparator.comparing(updateAtGetter).reversed();
        final Supplier<Optional<WdtProviderGoods>> masterProviderGoodsSupplier = () -> list.stream()
                                                                                           .filter(WdtProviderGoods::getIsMaster)
                                                                                           .min(comparator);
        final Supplier<Optional<WdtProviderGoods>> providerGoodsSupplier = () -> list.stream().min(comparator);
        return Stream.of(masterProviderGoodsSupplier, providerGoodsSupplier)
                     .map(Supplier::get)
                     .filter(Optional::isPresent)
                     .map(Optional::get)
                     .map(WdtProviderGoods::getProviderNo)
                     .map(this::getProvider)
                     .findFirst()
                     .map(Provider::getId)
                     .orElse(0L);
    }

    private Provider getProvider(String providerNo) {
        return providerCache.get(providerNo);
    }

    private Provider queryProvider(String providerNo) {
        return providerGateway.getByNo(providerNo);
    }
}
