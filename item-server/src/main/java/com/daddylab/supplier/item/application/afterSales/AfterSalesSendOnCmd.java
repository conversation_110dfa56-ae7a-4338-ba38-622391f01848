package com.daddylab.supplier.item.application.afterSales;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> up
 * @date 2022年10月18日 3:49 PM
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("异常件转寄信息保存封装")
public class AfterSalesSendOnCmd extends Command {

    private static final long serialVersionUID = -7797387761743633392L;

    @ApiModelProperty("异常件id")
    private Long abnormalInfoId;

    @ApiModelProperty("退换单编号")
    private String returnOrderNo;

    @ApiModelProperty("寄送方仓库编号")
    private String fromWarehouseNo;

    @ApiModelProperty("转寄单id")
    @NotNull(message = "转寄单id不得为空")
    private Long sendOnInfoId;

    @ApiModelProperty("物流编号")
    @NotBlank(message = "物流编号不得为空")
    private String logisticsNo;

    @ApiModelProperty("物流名称")
    @NotBlank(message = "物流名称不得为空")
    private String logisticsName;

    @ApiModelProperty("重量")
    @NotBlank(message = "重量不得为空")
    private String weight;

    @ApiModelProperty("图片")
    private List<String> imageUrls;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("寄送方联系电话")
    @NotBlank(message = "寄送方联系电话不能为空")
    private String fromTel;
}
