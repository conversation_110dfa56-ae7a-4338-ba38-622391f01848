package com.daddylab.supplier.item.application.bank;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/20
 */
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Data
public class LhhResponseData {
    Integer totalNum;
    List<LhhDataModel> data;
}
