package com.daddylab.supplier.item.infrastructure.oa.cgfk;

import com.daddylab.supplier.item.infrastructure.oa.OaFormAttachment;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@Data
public class OaCgfkData {

    @JsonProperty("formmain_0718")
    @NotNull
    private OaCgfkMainForm mainForm;

    @JsonProperty("formson_0719")
    @NotEmpty
    private List<OaCgfkDetail> details;

    @JsonProperty("formson_0720")
    @NotNull
    private List<OaCgfkInvoice> invoices;

    /** 表单中用到的附件需要在这边列出来 subReference、fileUrl 相同即可 */
    private List<OaFormAttachment> thirdAttachments;


}
