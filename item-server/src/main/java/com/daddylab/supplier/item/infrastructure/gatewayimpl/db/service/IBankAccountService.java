package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BankAccount;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 供应商账户 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-20
 */
public interface IBankAccountService extends IDaddyService<BankAccount> {

    List<BankAccount> getAccountByProviderId(Long providerId);
    Map<Long, List<BankAccount>> getAccountBatchByProviderIds(Collection<Long> providerId);

    void removeByProviderId(Long providerId);
}
