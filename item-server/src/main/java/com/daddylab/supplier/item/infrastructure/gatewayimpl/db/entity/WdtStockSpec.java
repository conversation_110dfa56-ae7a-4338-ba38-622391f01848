package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.utils.ObjectUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("wdt_stock_spec_rt")
public class WdtStockSpec implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 记录ID（旺店通主键ID）
     */
    private Long recId;

    /**
     * 缺陷 0,正品;1,残品
     */
    private Integer defect;

    /**
     * 库存数量
     */
    private BigDecimal stockNum;

    /**
     * WMS同步库存
     */
    private BigDecimal wmsSyncStock;

    /**
     * WMS库存差异
     */
    private BigDecimal wmsStockDiff;

    /**
     * 规格编号
     */
    private String specNo;

    /**
     * 规格ID
     */
    private Long specId;

    /**
     * 商品编号
     */
    private String goodsNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 规格代码
     */
    private String specCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 未付款量
     */
    private BigDecimal unpayNum;

    /**
     * 预订单数量
     */
    private BigDecimal subscribeNum;

    /**
     * 待审核量
     */
    private BigDecimal orderNum;

    /**
     * 待发货量
     */
    private BigDecimal sendingNum;

    /**
     * 采购在途量
     */
    private BigDecimal purchaseNum;

    /**
     * 调拨在途量
     */
    private BigDecimal transferNum;

    /**
     * 待采购数量
     */
    private BigDecimal toPurchaseNum;

    /**
     * 采购到货数量
     */
    private BigDecimal purchaseArriveNum;

    /**
     * 外部WMS同步时占用库存
     */
    private BigDecimal wmsPreemptyStock;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 图片URL
     */
    private String imgUrl;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库类型 仓库类型0为普通,非0为外部WMS
     */
    private Integer warehouseType;

    /**
     * 可发货库存（仓内库存-待审核量-待发货量）
     */
    private BigDecimal availableSendStock;

    /**
     * 可用库存（仓内库存-待审核量-待发货量-未付款量-预定单量库存）
     */
    private BigDecimal availableStock;

    public BigDecimal calcAvailableStock() {
        if (stockNum == null) {
            return BigDecimal.ZERO;
        }
        return stockNum.subtract(ObjectUtil.defaultIfNull(getOrderNum(), BigDecimal.ZERO))
                       .subtract(ObjectUtil.defaultIfNull(getSendingNum(), BigDecimal.ZERO))
                       .subtract(ObjectUtil.defaultIfNull(getUnpayNum(), BigDecimal.ZERO))
                       .subtract(ObjectUtil.defaultIfNull(getSubscribeNum(), BigDecimal.ZERO));
    }

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 修改时间
     */
    private LocalDateTime modified;

    /**
     * 部分支付数量
     */
    private BigDecimal partPaidNum;

    /**
     * 退换货在途数量（卖家发给买家）
     */
    private BigDecimal refundExchNum;

    /**
     * 销售退货数量
     */
    private BigDecimal refundNum;

    /**
     * 销售换货在途数量（从买家到卖家）
     */
    private BigDecimal refundOnwayNum;

    /**
     * 采购换货数量
     */
    private BigDecimal returnExchNum;

    /**
     * 采购退货数量
     */
    private BigDecimal returnNum;

    /**
     * 采购换货在途数量
     */
    private BigDecimal returnOnwayNum;

    /**
     * 待调拨数量
     */
    private BigDecimal toTransferNum;

    /**
     * 外部WMS同步时，占用库存差异
     */
    private BigDecimal wmsPreemptyDiff;

    /**
     * WMS同步时间
     */
    private LocalDateTime wmsSyncTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 库存锁定数量
     */
    private BigDecimal lockNum;

    /**
     * 标记ID
     */
    private Long flagId;

    /**
     * 标记名称
     */
    private String flagName;

    /**
     * 品牌编号
     */
    private String brandNo;

    /**
     * 其他待出库数量
     */
    private BigDecimal toOtherOutNum;

    /**
     * 生产待出库数量
     */
    private BigDecimal toProcessOutNum;

    /**
     * 生产待入库数量
     */
    private BigDecimal toProcessInNum;

    /**
     * 最后盘点时间
     */
    private LocalDateTime lastPdTime;

    /**
     * 最后出入库时间
     */
    private LocalDateTime lastInoutTime;

    /**
     * 状态 0：未启用 1：启用 2：停用
     */
    private Integer status;

    /**
     * 今日销量
     */
    private BigDecimal todayNum;

    /**
     * 7天销量
     */
    @TableField(value = "num_7days")
    private BigDecimal num7Days;

    /**
     * 14天销量
     */
    @TableField(value = "num_14days")
    private BigDecimal num14Days;

    /**
     * 月销量
     */
    private BigDecimal numMonth;

    /**
     * 总销量
     */
    private BigDecimal numAll;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    @Override
    public String toString() {
        return toSimpleString();
    }

    public String toSimpleString() {
        return String.format("{仓库:%s 商家编码:%s 可用库存:%s}", getWarehouseNo(), getSpecNo(), getAvailableStock());
    }

    private String toVerboseString() {
        return String.format(
                "{仓库:%s(%s) 商家编码:%s 库存量:%s 未付款量:%s 预订单量:%s 待审核量:%s 待发货量:%s 采购在途量:%s 调拨在途量:%s " +
                        "待采购量:%s 采购到货量:%s 外部WMS同步时占用库存:%s 可发库存:%s 可用库存:%s 部分付款库存:%s " +
                        "销售换货在途量(卖家发给买家):%s 销售退货量:%s 销售换货在途量(从买家回到卖家):%s " +
                        "采购换货量:%s 采购退货量:%s 采购换货在途量:%s 待调拨量:%s 库存锁定量:%s}",
                getSpecNo(),
                getWarehouseName(),
                getWarehouseNo(),
                getStockNum(),
                getUnpayNum(),
                getSubscribeNum(),
                getOrderNum(),
                getSendingNum(),
                getPurchaseNum(),
                getTransferNum(),
                getToPurchaseNum(),
                getPurchaseArriveNum(),
                getWmsPreemptyStock(),
                getAvailableSendStock(),
                getAvailableStock(),
                getPartPaidNum(),
                getRefundExchNum(),
                getRefundNum(),
                getRefundOnwayNum(),
                getReturnExchNum(),
                getReturnNum(),
                getReturnExchNum(),
                getToTransferNum(),
                getLockNum());
    }


}
