package com.daddylab.supplier.item.application.afterSaleLink.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年05月11日 10:06 AM
 */
@Data
@ApiModel("售后分享链接保存")
public class AfterSaleShareLinkCmd extends Command {

    private static final long serialVersionUID = -2340742180327975271L;
    private Long id;

    @ApiModelProperty("0正常，1禁用")
    private Integer status;

    @ApiModelProperty("1。链接禁用操作")
    private Integer forbidden;

    @ApiModelProperty("链接名字")
    private String name;

    @ApiModelProperty("链接有效期。天")
    private Integer validityPeriod;

    @ApiModelProperty("0存在有效期，1长期有效")
    @NotNull(message = "是否长期有效标识不得为空")
    private Integer alwaysValidity;

    @ApiModelProperty("手机号码。英文逗号分割")
    private List<Long> telList;

    @ApiModelProperty("分享链接参数")
    private String params;

}
