package com.daddylab.supplier.item.application.afterSalesForwarding.types;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.javers.core.metamodel.annotation.DiffIgnore;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2024/5/24
 */
@Data
public class AfterSalesForwardingRegisterFormItemVO {
    private Long id;

    /**
     * 快递单号
     */
    @NotBlank
    @ApiModelProperty(value = "快递单号")
    private String deliveryNo;

    /**
     * 登记时间
     */
    @ApiModelProperty(value = "登记时间")
    @DiffIgnore
    private Long registerTime;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    private Integer itemNum;

    /**
     * 商品SKU
     */
    @ApiModelProperty(value = "商品SKU")
    private String skuCode;

    /**
     * 规格名称
     */
    @ApiModelProperty(value = "规格名称")
    private String skuName;

    /**
     * 是否完好
     */
    @ApiModelProperty(value = "是否完好")
    private Boolean intact;

    /**
     * 异常说明
     */
    @ApiModelProperty(value = "异常说明")
    private String abnormalDescription;

    /**
     * 影响销售凭证
     */
    @ApiModelProperty(value = "影响销售凭证")
    private AffectsSalesVouchers affectsSalesVouchers;

    /**
     * 客服备注
     */
    @ApiModelProperty(value = "客服备注")
    private String csRemark;

    /**
     * 原始单号
     */
    @ApiModelProperty(value = "原始单号")
    private String orderNo;

    /**
     * 出库单号
     */
    @ApiModelProperty(value = "出库单号")
    private String stockoutNo;

    /**
     * 出库仓库
     */
    @ApiModelProperty(value = "出库仓库")
    private String stockoutWarehouse;

    /**
     * 出库仓库编号
     */
    @ApiModelProperty(value = "出库仓库编号")
    private String stockoutWarehouseNo;

    /**
     * 转寄地址
     */
    @ApiModelProperty(value = "转寄地址")
    private String forwardingAddress;

    /**
     * 转寄单号
     */
    @ApiModelProperty(value = "转寄单号")
    private String forwardingDeliveryNo;

    /**
     * 重量
     */
    @ApiModelProperty(value = "重量")
    private String weight;

    /**
     * 纸箱型号
     */
    @ApiModelProperty(value = "纸箱型号")
    private String cartonModel;

    @ApiModelProperty(value = "店铺编号")
    private String shopNo;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;


}
