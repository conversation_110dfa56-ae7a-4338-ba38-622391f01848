package com.daddylab.supplier.item.application.platformItemSkuInventory.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params.PlatformSkuSyncInfo;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatAccessTokenDto;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductDetailDto;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductListDto;
import com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct.Attribute;
import com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct.Sku;
import com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct.WeChatProduct;
import com.daddylab.supplier.item.application.shipinghao.service.WechatApiService;
import com.daddylab.supplier.item.application.shipinghao.service.WechatProductSyncService;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 视频号平台商品同步服务实现
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WechatVideoItemSyncServiceImpl
    extends AbstractPlatformItemSkuSync<
        WechatVideoItemSyncServiceImpl.ProductItemWrapper, List<PlatformSkuSyncInfo>> {

  private final WechatProductSyncService wechatProductSyncService;
  private final WechatApiService wechatApiService;

  @Override
  public Platform defaultType() {
    return Platform.WECHAT_VIDEO;
  }

  @Override
  public void fullDoseSync() {
    try {
      executeSimpleSingleThreadSync();
      log.info("[视频号平台商品同步]单线程同步任务执行完成");
    } catch (Exception e) {
      log.error("[视频号平台商品同步]任务运行异常", e);
    }
  }

  /** 简单的单线程全量同步方法（推荐使用，避免并发问题） */
  public void executeSimpleSingleThreadSync() {
    try {
      // 获取所有未过期的视频号店铺授权
      final List<ShopAuthorization> shopAuthorizations =
          shopAuthorizationService.listNotExpiredAuthorizations(Platform.WECHAT_VIDEO);

      log.info("[平台商品同步][视频号]开始单线程同步，店铺数量={}", shopAuthorizations.size());

      int totalProcessed = 0;
      int totalSuccessful = 0;
      int totalFailed = 0;

      for (ShopAuthorization shopAuthorization : shopAuthorizations) {
        final String shopNo = shopAuthorization.getSn();
        String[] types = {"daddylab", "member"};

        for (String type : types) {
          try {
            log.info("[平台商品同步][视频号]开始同步店铺: {}, 类型: {}", shopNo, type);

            int shopResult = syncShopProducts(shopAuthorization, type);
            totalProcessed += shopResult;
            totalSuccessful += shopResult;

            log.info("[平台商品同步][视频号]完成同步店铺: {}, 类型: {}, 商品数量: {}", shopNo, type, shopResult);

          } catch (Exception e) {
            totalFailed++;
            log.error("[平台商品同步][视频号]同步店铺失败: {}, 类型: {}", shopNo, type, e);
          }
        }
      }

      log.info(
          "[平台商品同步][视频号]全部完成 - 总处理: {}, 成功: {}, 失败: {}",
          totalProcessed,
          totalSuccessful,
          totalFailed);

    } catch (Exception e) {
      log.error("[平台商品同步][视频号]同步异常", e);
      throw new RuntimeException("视频号商品同步失败", e);
    }
  }

  /** 同步单个店铺的商品 */
  private int syncShopProducts(ShopAuthorization shopAuthorization, String type) {
    int syncedCount = 0;
    String nextKey = null;
    int pageSize = 30;

    try {
      // 获取访问令牌
      WechatAccessTokenDto accessToken = wechatApiService.getAccessToken(type);
      if (accessToken == null || !accessToken.isSuccess()) {
        log.error("[平台商品同步][视频号]获取访问令牌失败，店铺: {}, 类型: {}", shopAuthorization.getSn(), type);
        return 0;
      }

      int batchCount = 0;
      int totalProducts = 0; // 商品总数量（来自API）
      int totalFetched = 0; // 实际获取到的商品详情数量

      while (true) {
        batchCount++;

        // 获取商品列表
        WechatProductListDto productList =
            wechatApiService.getProductListWithNextKey(
                type, accessToken.getAccessToken(), pageSize, nextKey);

        if (productList == null || CollUtil.isEmpty(productList.getProductIds())) {
          break;
        }

        // 记录总数量（只在第一批次记录）
        if (batchCount == 1 && productList.getTotalNum() != null) {
          totalProducts = productList.getTotalNum();
          int estimatedBatches = (totalProducts + pageSize - 1) / pageSize; // 向上取整
          log.info(
              "[平台商品同步][视频号]开始同步 - 店铺: {}, 类型: {}, 预计商品总数: {}, 预计批次: {}",
              shopAuthorization.getSn(),
              type,
              totalProducts,
              estimatedBatches);
        }

        log.info(
            "[平台商品同步][视频号]批次{} - 并发查询{}个商品详情，店铺: {}",
            batchCount,
            productList.getProductIds().size(),
            shopAuthorization.getSn());

        // 并发批量获取商品详情（查询阶段）
        List<WechatProductDetailDto> productDetails =
            wechatProductSyncService.getProductDetails(type, productList.getProductIds());

        totalFetched += productDetails.size();

        log.info(
            "[平台商品同步][视频号]批次{} - 获取到{}个商品详情，开始顺序写入 (累计获取: {}/{})",
            batchCount,
            productDetails.size(),
            totalFetched,
            totalProducts > 0 ? totalProducts : "未知");

        // 顺序处理商品（写入阶段）
        int batchSyncedCount = 0;
        for (WechatProductDetailDto detail : productDetails) {
          if (detail != null && detail.getProduct() != null) {
            try {
              ProductItemWrapper wrapper = new ProductItemWrapper();
              wrapper.setProduct(detail.getProduct());
              wrapper.setShopNo(shopAuthorization.getSn());
              wrapper.setType(type);

              List<PlatformSkuSyncInfo> skuInfos = productToPlatformItemSkuInventoryParams(wrapper);
              if (!skuInfos.isEmpty()) {
                saveSkuSyncInfo(skuInfos);
                batchSyncedCount++;
                syncedCount++;
              }

            } catch (Exception e) {
              log.error(
                  "[平台商品同步][视频号]处理商品失败，ID: {}, 店铺: {}, 类型: {}",
                  detail.getProduct().getProductId(),
                  shopAuthorization.getSn(),
                  type,
                  e);
            }
          }
        }

        log.info(
            "[平台商品同步][视频号]批次{} - 完成写入{}个商品 (累计同步: {}/{})",
            batchCount,
            batchSyncedCount,
            syncedCount,
            totalProducts > 0 ? totalProducts : "未知");

        // 获取下一页
        nextKey = productList.getNextKey();
        if (nextKey == null || nextKey.isEmpty()) {
          break;
        }

        // 批次间短暂延迟，避免API限流
        if (batchCount % 5 == 0) { // 每5批次延迟一次
          try {
            Thread.sleep(200);
          } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            break;
          }
        }
      }

      log.info(
          "[平台商品同步][视频号]店铺同步完成 - 店铺: {}, 类型: {}, 总批次: {}, 预计商品: {}, 实际获取: {}, 成功同步: {}",
          shopAuthorization.getSn(),
          type,
          batchCount,
          totalProducts,
          totalFetched,
          syncedCount);

    } catch (Exception e) {
      log.error("[平台商品同步][视频号]同步店铺异常，店铺: {}, 类型: {}", shopAuthorization.getSn(), type, e);
      throw e;
    }

    return syncedCount;
  }

  /** 将微信商品转换为平台同步信息 */
  List<PlatformSkuSyncInfo> productToPlatformItemSkuInventoryParams(ProductItemWrapper wrapper) {
    final ArrayList<PlatformSkuSyncInfo> paramsList = new ArrayList<>();
    final WeChatProduct product = wrapper.getProduct();

    if (product == null) {
      return paramsList;
    }
    if (CollUtil.isEmpty(product.getSkus())) {
      return paramsList;
    }
    if (product.getStatus() != 5) {
      return paramsList;
    }

    final int skuNum = product.getSkus().size();

    for (Sku sku : product.getSkus()) {
      try {
        PlatformSkuSyncInfo platformSkuSyncInfo = new PlatformSkuSyncInfo();

        // 平台信息
        platformSkuSyncInfo.setPlatform(defaultType());
        platformSkuSyncInfo.setShopNo(wrapper.getShopNo());

        // SKU基本信息
        platformSkuSyncInfo.setOuterSkuId(sku.getSkuId());
        platformSkuSyncInfo.setOuterItemId(product.getProductId());
        platformSkuSyncInfo.setOuterSkuCode(sku.getSkuCode());
        platformSkuSyncInfo.setOuterItemCode(product.getSpuCode());

        // 库存和价格
        platformSkuSyncInfo.setStock(
            sku.getStockNum() != null ? sku.getStockNum().longValue() : 0L);
        if (sku.getSalePrice() != null) {
          // 微信价格单位是分，转换为元
          platformSkuSyncInfo.setPrice(
              BigDecimal.valueOf(sku.getSalePrice())
                  .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
        }

        // 状态转换：微信状态 5-上架 -> 平台状态 1-上架
        platformSkuSyncInfo.setStatus(1);

        // 时间信息
        platformSkuSyncInfo.setItemCreateTime(null);
        platformSkuSyncInfo.setItemUpdateTime(product.getEditTime());
        platformSkuSyncInfo.setSkuCreateTime(null);
        platformSkuSyncInfo.setSkuUpdateTime(product.getEditTime());

        // 商品信息
        platformSkuSyncInfo.setGoodsName(product.getTitle());

        // 规格名称：从SKU属性中提取
        if (CollUtil.isNotEmpty(sku.getSkuAttrs())) {
          String specName =
              sku.getSkuAttrs().stream()
                  .map(Attribute::getAttrValue)
                  .filter(StrUtil::isNotBlank)
                  .collect(Collectors.joining("|"));
          platformSkuSyncInfo.setSpecName(specName);
        }

        platformSkuSyncInfo.setSkuNum(skuNum);
        paramsList.add(platformSkuSyncInfo);

      } catch (Exception e) {
        log.error(
            "[视频号商品转换][SKU转换失败]转换SKU失败，商品ID: {}, SKU_ID: {}",
            product.getProductId(),
            sku.getSkuId(),
            e);
      }
    }

    return paramsList;
  }

  @Override
  protected List<PlatformSkuSyncInfo> getSkuSyncInfos(String shopNo, String itemId) {
    try {
      String type = determineTypeByShopNo(shopNo);

      WechatProductDetailDto productDetail =
          wechatProductSyncService.getProductDetail(type, itemId);

      if (productDetail == null || productDetail.getProduct() == null) {
        return Collections.emptyList();
      }

      ProductItemWrapper wrapper = new ProductItemWrapper();
      wrapper.setProduct(productDetail.getProduct());
      wrapper.setShopNo(shopNo);
      wrapper.setType(type);

      return productToPlatformItemSkuInventoryParams(wrapper);

    } catch (Exception e) {
      log.error("[视频号]获取SKU同步信息失败，店铺={}, 商品ID={}", shopNo, itemId, e);
      return Collections.emptyList();
    }
  }

  /** 根据店铺编号确定微信类型 */
  private String determineTypeByShopNo(String shopNo) {
    if (shopNo != null && shopNo.contains("member")) {
      return "member";
    }
    return "daddylab";
  }

  @Override
  public void incrementSync(LocalDateTime startTime, LocalDateTime endTime)
      throws UnsupportedOperationException {
    // 微信小店API不支持按时间范围增量同步，执行全量同步
    // log.info("[视频号]开始增量同步（实际为全量同步），时间范围: {} - {}", startTime, endTime);
    // executeSimpleSingleThreadSync();
    return;
  }

  @Override
  public org.springframework.batch.item.ItemProcessor<ProductItemWrapper, List<PlatformSkuSyncInfo>>
      getItemProcess() {
    return this::productToPlatformItemSkuInventoryParams;
  }

  @Override
  public org.springframework.batch.item.ItemWriter<List<PlatformSkuSyncInfo>> getItemWriter() {
    return platformParams -> {
      for (List<PlatformSkuSyncInfo> platformParamDatums : platformParams) {
        if (CollUtil.isNotEmpty(platformParamDatums)) {
          saveSkuSyncInfo(platformParamDatums);
        }
      }
    };
  }

  @Data
  public static class ProductItemWrapper {
    private WeChatProduct product;
    private String shopNo;
    private String type; // daddylab 或 member
  }

  void saveSkuSyncInfo(List<PlatformSkuSyncInfo> skuSyncInfos) {
    if (CollUtil.isEmpty(skuSyncInfos)) {
      return;
    }

    try {
      // 单线程执行，不应该再有并发冲突，直接保存
      savePlatformItemSkus(skuSyncInfos);
    } catch (Exception e) {
      log.error("[视频号商品同步][保存异常]保存SKU同步信息失败，数据: {}", JsonUtil.toJson(skuSyncInfos), e);
      throw e;
    }
  }
}
