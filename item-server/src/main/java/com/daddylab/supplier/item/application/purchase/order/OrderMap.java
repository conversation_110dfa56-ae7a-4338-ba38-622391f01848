package com.daddylab.supplier.item.application.purchase.order;

import com.google.common.collect.ImmutableMap;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR> up
 * @date 2022/4/26 3:01 下午
 */
public class OrderMap {

  /** 1:款到发货、2:预付10%、3:预付20%、4:预付30%、5:预付40%、6:预付50%、7:预付60%、8:货到付款、9:月结 */
  public static final Map<Integer, String> PAY_MAP =
      ImmutableMap.<Integer, String>builder()
          .put(1, "款到发货")
          .put(2, "预付10%")
          .put(3, "预付20%")
          .put(4, "预付30%")
          .put(5, "预付40%")
          .put(6, "预付50%")
          .put(7, "预付60%")
          .put(10, "预付70%")
          .put(11, "预付80%")
          .put(12, "预付90%")
          .put(13, "预付100%")
          .put(8, "货到付款")
          .put(9, "月结")
          .build();

  public static final Map<Integer, BigDecimal> PAY_PROPORTION =
      ImmutableMap.<Integer, BigDecimal>builder()
          .put(2, new BigDecimal("0.1"))
          .put(3, new BigDecimal("0.2"))
          .put(4, new BigDecimal("0.3"))
          .put(5, new BigDecimal("0.4"))
          .put(6, new BigDecimal("0.5"))
          .put(7, new BigDecimal("0.6"))
          .put(10, new BigDecimal("0.7"))
          .put(11, new BigDecimal("0.8"))
          .put(12, new BigDecimal("0.9"))
          .put(13, new BigDecimal("1"))
          .build();

  public static String mapPayTime(Integer payType) {
    return PAY_MAP.getOrDefault(payType, "");
  }
}
