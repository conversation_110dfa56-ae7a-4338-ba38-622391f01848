package com.daddylab.supplier.item.application.operateLog.eventListeners;

import com.daddylab.supplier.item.application.brand.event.BrandChangeEvent;
import com.daddylab.supplier.item.application.common.event.EntityChangeEvent;
import com.daddylab.supplier.item.application.provider.event.ProviderChangeEvent;
import com.daddylab.supplier.item.application.shop.event.ShopChangeEvent;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.brand.entity.BrandEntity;
import com.daddylab.supplier.item.domain.brand.gateway.BrandGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.shop.entity.ShopEntity;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.domain.shop.trans.ShopTransMapper;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Shop;
import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.javers.core.diff.Diff;
import org.javers.core.diff.changetype.PropertyChange;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import javax.inject.Inject;
import java.util.stream.Collectors;

@EventBusListener
@Slf4j
public class EntityChangeListener {
    @Inject
    OperateLogDomainService operateLogDomainService;

//    @Inject
//    KingDeeGateway kingDeeGateway;

    @Inject
    ShopGateway shopGateway;

    @Autowired
    BrandGateway brandGateway;

    @Resource
    KingDeeTemplate kingDeeTemplate;

    @Subscribe
    public void onShopChange(ShopChangeEvent<ShopEntity> event) {
        ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
            //记录操作日志
            try {
                logChange(event, OperateLogTarget.SHOP, ShopEntity.class);
            } catch (Exception e) {
                log.error("记录店铺操作日志异常", e);
            }

            //推送至金蝶
            try {
                final ShopEntity shopEntity = event.getEntityChange().getNewEntity();
                final Shop shop = ShopTransMapper.INSTANCE.toShop(shopEntity);


                if(StringUtil.isNotBlank(shopEntity.getKingDeeId())) {
                    //新增店铺的时候才关联金蝶ID
//                    KingDeeRemoteTemplate remoteTemplate = SpringUtil.getBean(KingDeeRemoteTemplate.class);
                    if (event.getEntityChange().isAdd()) {
                        kingDeeTemplate.handler(ApiEnum.SAVE_SHOP, shop.getId(), null);
//                        KingDeeResponse response = remoteTemplate.addShop(shop);
//                        if(response.isSuccess()){
//                            shopGateway.setKingDeeId(shopEntity.getId(), response.getId());
//                        }else{
//                            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR,response.getMsg());
//                        }
                    } else {
                        kingDeeTemplate.handler(ApiEnum.SAVE_SHOP, shop.getId(), shopEntity.getKingDeeId());

//                        KingDeeResponse response = remoteTemplate.updateShop(shop);
//                        if(!response.getSuccess()){
//                            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR,response.getMsg());
//                        }
                    }
                }
            } catch (Exception e) {
                log.error("店铺推送至金蝶失败", e);
            }
        });
    }

    @Subscribe
    public void onBrandChange(BrandChangeEvent<BrandEntity> event) {
        logChange(event, OperateLogTarget.BRAND, BrandEntity.class);
    }

//    @Subscribe
//    public void onItemChange(ItemChangeEvent<ItemEntity> event) {
//        logChange(event, OperateLogTarget.ITEM, ItemEntity.class);
//    }

    @Subscribe
    public void onProviderChange(ProviderChangeEvent<Provider> event) {
        logChange(event, OperateLogTarget.PROVIDER, Provider.class);

        //如果供应商被删除，需要移除掉其关联品牌与它的关联关系
        if (event.getEntityChange().isRemove()) {
            ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
                try {
                    brandGateway.removeRelateBrandForProvider(event.getTargetId());
                    log.info("删除供应商后，移除其关联品牌与它的关联关系成功");
                } catch (Exception e) {
                    log.error("删除供应商后，移除其关联品牌与它的关联关系时发生异常", e);
                }
            });
        }
    }

    private void logChange(EntityChangeEvent<?> event, OperateLogTarget operateLogTarget, Class<?> targetClass) {
        if (event.getEntityChange().isAdd()) {
            operateLogDomainService.addOperatorLog(event.getOperatorId(), operateLogTarget, event.getTargetId(), "新增了" + operateLogTarget.getDesc());
        } else if (event.getEntityChange().isRemove()) {
            operateLogDomainService.addOperatorLog(event.getOperatorId(), operateLogTarget, event.getTargetId(), "删除了" + operateLogTarget.getDesc());
        }
        if (event.getEntityChange().getDiff().hasChanges()) {
            logDiff(operateLogTarget, targetClass, event.getEntityChange().getDiff(), event.getOperatorId(), event.getTargetId());
        }
    }

    private void logDiff(OperateLogTarget operateLogTarget, Class<?> targetClass, Diff diff, Long operatorId, Long targetId) {
        final String msg = diff
                .getChangesByType(PropertyChange.class)
                .stream()
                .filter(propertyChange -> propertyChange.getAffectedObject()
                        .map(o -> o.getClass().equals(targetClass))
                        .orElse(false))
                .map(PropertyChange::getPropertyName)
                .distinct()
                .collect(Collectors.joining("、"));
        operateLogDomainService.addOperatorLog(operatorId, operateLogTarget, targetId, "修改了" + msg);
    }
}
