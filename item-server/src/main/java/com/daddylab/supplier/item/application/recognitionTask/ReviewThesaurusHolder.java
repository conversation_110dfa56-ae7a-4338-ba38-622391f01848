package com.daddylab.supplier.item.application.recognitionTask;

import cn.hutool.crypto.digest.DigestUtil;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ReviewThesaurus;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ReviewThesaurusHolder {

    private Map<String, Set<ReviewThesaurus>> keywordMap = Collections.emptyMap();
    private Map<Integer, ReviewThesaurus> thesaurusMap = Collections.emptyMap();
    private String dataMd5 = "";

    public ReviewThesaurus getThesaurusById(Integer id) {
        return thesaurusMap.get(id);
    }

    /**
     * @deprecated 改用 partnerReviewThesaurusAPI.reviewThesaurusAll(itemNo) 实时查询
     * @param keyword 关键词
     * @return 匹配词典
     */
    public Set<ReviewThesaurus> getThesaurusesByKeyword(String keyword) {
        return keywordMap.getOrDefault(keyword, Collections.emptySet());
    }

    public String getDataMd5() {
        return dataMd5;
    }

    public Optional<Pair<Set<String>, Set<String>>> refresh(
            List<ReviewThesaurus> reviewThesauruses) {
        final String reviewThesaurusesJSON = JsonUtil.toJson(reviewThesauruses);
        final String latestMd5 = DigestUtil.md5Hex(reviewThesaurusesJSON);
        if (latestMd5.equals(this.dataMd5)) {
            log.info("词库未发生变化 md5={}", this.dataMd5);
            return Optional.empty();
        }
        this.dataMd5 = latestMd5;
        final Map<String, Set<ReviewThesaurus>> keywordMap0 = this.keywordMap;
        Map<String, Set<ReviewThesaurus>> keywordMap = Maps.newHashMap();
        final Map<Integer, ReviewThesaurus> thesaurusMap = Maps.newHashMap();
        Set<String> keywordAdded = Sets.newHashSet();
        for (ReviewThesaurus reviewThesaurus : reviewThesauruses) {
            thesaurusMap.put(reviewThesaurus.getId(), reviewThesaurus);
            for (String keyword : reviewThesaurus.getContext()) {
                if (!keywordMap0.containsKey(keyword)) {
                    keywordAdded.add(keyword);
                }
                keywordMap
                        .computeIfAbsent(keyword, k -> new LinkedHashSet<>())
                        .add(reviewThesaurus);
            }
        }
        final Set<String> keywordRemoved =
                keywordMap0.keySet().stream()
                        .filter(k -> !keywordMap.containsKey(k))
                        .collect(Collectors.toSet());
        this.keywordMap = keywordMap;
        this.thesaurusMap = thesaurusMap;
        return Optional.of(Pair.of(keywordAdded, keywordRemoved));
    }
}
