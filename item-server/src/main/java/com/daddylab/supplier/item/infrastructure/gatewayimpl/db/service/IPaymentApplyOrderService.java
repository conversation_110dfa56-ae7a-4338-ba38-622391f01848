package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentApplyOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PaymentOrderStatus;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 采购管理-付款申请单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
public interface IPaymentApplyOrderService extends IDaddyService<PaymentApplyOrder> {

    /**
     * 根据关联单据编码获取已申请的付款金额合计。
     * 已申请：待审核+待付款+已付款
     *
     * @param relatedNo 关联单据编码
     * @return 已申请付款金额合计
     */
    BigDecimal getAppliedPaymentAmountByRelatedNo(Collection<String> relatedNo);


    /**
     * 通过付款单编号查询数据
     *
     * @param no 付款单单号
     */
    default PaymentApplyOrder getByNo(String no) {
        return lambdaQuery().eq(PaymentApplyOrder::getNo, no).one();
    }

    /**
     * 根据付款申请状态和类型，查询对应的关联单据编码
     *
     * @param statusList
     * @param detailSource 0：采购单，1：结算单'
     * @return
     */
    List<Long> getMatchedRelatedIdByStatus(Collection<PaymentOrderStatus> statusList, Integer detailSource);

}
