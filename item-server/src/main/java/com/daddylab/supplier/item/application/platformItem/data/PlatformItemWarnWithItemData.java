package com.daddylab.supplier.item.application.platformItem.data;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnType;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/1/6
 */
@Data
public class PlatformItemWarnWithItemData implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 平台商品ID
     */
    private Long platformItemId;

    /**
     * 平台商品SKU ID
     */
    private Long platformItemSkuId;

    /**
     * 预警类型 1:未匹配到后端商品SKU
     */
    private PlatformItemWarnType warnType;

    /**
     * 状态 0:未处理 1:已处理
     */
    private PlatformItemWarnStatus warnStatus;

    /**
     * 平台商品状态
     */
    private PlatformItemStatus platformItemStatus;

    /**
     * 关联商品ID
     */
    private Long itemId;

    /**
     * 商品编号
     */
    private String itemCode;

    /**
     * 匹配到的我们自己的SKU ID
     */
    private Long skuId;

    /**
     * sku系统编码
     */
    private String skuCode;

    /**
     * 平台货品ID
     */
    private String outerItemId;

    /**
     * 平台货品编码
     */
    private String outerItemCode;

    /**
     * 店铺ID
     */
    private Long shopId;


    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺编号
     */
    private String shopNo;

    /**
     * 平台
     */
    Platform platform;

    /**
     * 平台商品名称
     */
    String goodsName;

    /**
     * 平台修改时间
     */
    LocalDateTime modified;


    /**
     * 创建时间createAt
     */
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    private Long updatedUid;
}
