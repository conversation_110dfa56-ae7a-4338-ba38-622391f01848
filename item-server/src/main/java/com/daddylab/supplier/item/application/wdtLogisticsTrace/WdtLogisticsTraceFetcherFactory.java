package com.daddylab.supplier.item.application.wdtLogisticsTrace;

import com.daddylab.supplier.item.domain.dataFetch.*;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtLogisticsTraceService;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/2/20
 */
@Component
public class WdtLogisticsTraceFetcherFactory implements FetcherFactory {
  @Autowired private WdtGateway wdtGateway;
  @Autowired private IWdtLogisticsTraceService wdtLogisticsTraceService;

  @Override
  public FetcherFactoryType fetcherFactoryType() {
    return FetcherFactoryType.WDT_LOGISTICS_TRACE;
  }

  @Override
  public Fetcher getFetcher(FetcherConfig config, Map<String, Object> parameters) {
    return new WdtLogisticsTraceFetcher(wdtGateway, wdtLogisticsTraceService, parameters);
  }
}
