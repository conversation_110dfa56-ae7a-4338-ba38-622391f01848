package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.application.afterSaleLogistics.AfterSaleLogisticsConfig;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.LogisticsTraceDataSource;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.CloseReasonType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OrderLogisticsTraceStatus;

import java.io.Serializable;
import java.util.Objects;

import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 订单物流跟踪记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrderLogisticsTrace implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Long isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 跟踪状态
     */
    private OrderLogisticsTraceStatus traceStatus;

    /**
     * 锁定时间
     */
    private Long lockTime;

    /**
     * 异常记录
     */
    private String err;

    /**
     * 旺店通订单号
     */
    private String wdtTradeNo;

    /**
     * 旺店通订单ID
     */
    private Long wdtTradeId;

    /**
     * 原始单号
     */
    private String srcOrderNo;

    /**
     * 付款时间
     */
    private Long payTime;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 物流公司
     */
    private String logisticsName;

    /**
     * 收件人手机号
     */
    private String receiverMobile;

    /**
     * 发货时间
     */
    private Long consignTime;

    /**
     * 最后跟踪时间
     */
    private Long trackTime;

    /**
     * 揽收时间
     */
    private Long pickingUpTime;

    /**
     * 发运时间
     */
    private Long sendingTime;

    /**
     * 派件时间
     */
    private Long distributeTime;

    /**
     * 签收时间
     */
    private Long signingTime;

    /**
     * 订单平台
     */
    private Integer platform;

    /**
     * 出库仓库编号
     */
    private String stockoutWarehouseNo;

    /**
     * 出库单号
     */
    private String stockoutNo;

    /**
     * 是否打开跟踪
     */
    private Integer openTrace;

    /**
     * 设置是否打开跟踪（打开跟踪将自动清空关闭原因）
     * @param openTrace 1：打开跟踪，0：关闭跟踪
     */
    public void setOpenTrace(Integer openTrace) {
        this.openTrace = openTrace;
        if (openTrace == 1) {
            this.closeReason = CloseReasonType.NONE;
        }
    }

    /**
     * 物流回调ID
     */
    private Long callbackId;

    /**
     * 物流跟踪，数据源
     *
     * @see com.daddylab.supplier.item.application.afterSaleLogistics.enums.LogisticsTraceDataSource
     */
    private LogisticsTraceDataSource traceSource;

    /**
     * 关闭跟踪原因
     */
    private CloseReasonType closeReason;

    /**
     * 订阅时间
     */
    private Long subscribeTime;

    /**
     * 重复订阅次数
     */
    private Integer subscribeNum;

    /**
     * 店铺ID
     */
    private Long shopId;


    /**
     * 设置物流跟踪状态
     *
     * @param config 配置
     */
    public void setTraceStatusForLogisticsTrace(AfterSaleLogisticsConfig config) {
        final boolean isNotDelivery = StringUtil.isBlank(getLogisticsNo());
        if (isNotDelivery) {
            setTraceStatus(OrderLogisticsTraceStatus.NOT_DELIVERY);
        } else {
            if (config.isWdtLogisticsTraceEnabled() && config.isUseWdtLogisticsTrace(this)) {
                setTraceStatus(OrderLogisticsTraceStatus.WDT_WAIT_CALLBACK);
            } else {
                if (Objects.equals(getPlatform(), Platform.LAOBASHOP.getValue()) && !config.isSubscribeMallOrder()) {
                    setTraceStatus(OrderLogisticsTraceStatus.MALL_WAIT_CALLBACK);
                } else {
                    setTraceStatus(OrderLogisticsTraceStatus.KDY_WAIT_SUBSCRIBE);
                }
            }
        }
    }
}
