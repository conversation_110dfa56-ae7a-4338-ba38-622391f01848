package com.daddylab.supplier.item.domain.dataFetch;

import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.google.common.collect.ImmutableMap;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/4/24
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(of = {"fetchDataType", "round"})
@Slf4j(topic = "FetcherConfig")
public class FetcherConfig {

    /**
     * 拉取的数据类型
     */
    private FetchDataType fetchDataType;

    /**
     * 工厂类型
     */
    private FetcherFactoryType fetcherFactoryType;

    /**
     * 工厂生产Fetcher时使用的参数
     */
    private Map<String, Object> factoryParameters;

    /**
     * 是否禁用
     */
    private boolean disabled = false;

    /**
     * 当前数据全部分段完成时挂起分段行为多少秒
     */
    private int suspendSecondsForAllSegmented = 3;

    /**
     * 当前待执行的分段数量达到最大限制时挂起分段多少秒
     */
    private int suspendSecondsForMaxWaitingSegments = 3;

    /**
     * 分段异常时挂起多少秒
     */
    private int suspendSecondsForSegmentException = 10;

    /**
     * 当前数据全部拉取完成时挂起多少秒
     */
    private int suspendSecondsForAllFetched = 3;

    /**
     * 连续多次拉取数据异常时跳过这个区间（默认不启用这个特性）
     */
    private int skipOnContinuousError = Integer.MAX_VALUE;

    /**
     * 连续多次拉取数据异常时报警
     */
    private int alertOnContinuousError = 3;

    /**
     * 重试间隔（当网关接口出现临时错误时暂停当前数据类型的拉取直到多少秒之后再尝试）
     */
    private int tryLaterSeconds = 10;

    /**
     * 退避策略（当网关接口连续错误时将会采取指数值退避策略暂停拉取）
     */
    private String backoff = "1s 5s 10s 30s 1m";

    /**
     * 最大的拉取时间范围
     */
    private Duration maxSegment = Duration.ofDays(30);

    /**
     * 最小的拉取时间范围（当设定的过于小时可能会因为过于频繁的拉取导致问题）
     */
    private Duration minSegment = Duration.ofMinutes(1);

    /**
     * 拉取时间范围限制（开始时间）
     */
    private String startLimit;

    /**
     * 拉取时间范围限制（结束时间）
     */
    private String endLimit;

    /**
     * 分页顺序，默认从后向前分页
     */
    private boolean reverseFetch = true;

    /**
     * 默认分页大小
     */
    private int pageSize = 100;

    /**
     * 持久化分页进度（每拉取多少页持久化一次）
     */
    private int persistPageInterval = 1;

    /**
     * 数据拉取优先级策略
     */
    private PriorityPolicy priorityPolicy = PriorityPolicy.NEW_FIRST;

    /**
     * 锁定时长（单位秒）
     */
    private int lockSeconds = 60;

    /**
     * 每次调度拉取多少个段
     */
    private int fetchSegmentLimitPerDispatch = 10;

    /**
     * 最上方保留多少条拉取记录
     */
    private int preserveHead = 1440;

    /**
     * 最下方保留多少条拉取记录
     */
    private int preserveTail = 1440;

    /**
     * 最大待拉取段数量（超过后暂停分段）
     */
    private int maxWaitingSegmentCount = 50;

    /**
     * 并发控制
     */
    private int concurrent = 1;

    /**
     * 流量控制（单位为次）
     */
    private int rate = 30;

    /**
     * 流量控制窗口（单位为分钟）
     */
    private int rateInterval = 1;

    /**
     * 不支持根据时间分段查询
     */
    private boolean segmentFetch = true;

    /**
     * 启用全量拉取（需要实现对应方法）
     */
    private boolean fullFetch = false;

    /**
     * 初始化数据时的锁定时长（单位秒）
     */
    private int lockSecondsForFullFetch = 3600;

    /**
     * 周期性的全量拉取数据（每隔多少秒全量拉取一次，等于0说明只做一次初始化拉取）
     */
    private int fullFetchPeriodic = 0;

    /**
     * 拉取周期（修改这个周期会导致重新拉取全部时间范围的数据）
     */
    private int round = 0;

    /**
     * 拉取延迟时间（单位秒）
     */
    private int fetchDelay = 0;

    /**
     * 获取当前类型配置的拉取时间范围起始时间
     *
     * @return 拉取时间范围 - 起始时间
     */
    public LocalDateTime getStartLimitDateTime() {
        if (startLimit == null) {
            throw new IllegalArgumentException("未配置拉取时间范围开始时间");
        }
        return DateUtil.parse(startLimit);
    }

    /**
     * 获取当前类型配置的拉取时间范围结束时间
     *
     * @return 拉取时间范围 - 结束时间
     */
    public LocalDateTime getEndLimitDateTime() {
        if (endLimit == null) {
            return LocalDateTime.now();
        }
        return DateUtil.parse(endLimit);
    }

    /**
     * 获取当前连续失败次数对应的退避时间
     *
     * @param continuousErrorCounter 当前连续失败次数
     * @return 退避时间（单位秒）
     */
    public long backoffSeconds(int continuousErrorCounter) {
        final String[] levels = backoff.split(" ");
        if (continuousErrorCounter > levels.length) {
            return parseBackoffSeconds(levels[levels.length - 1]);
        }
        return parseBackoffSeconds(levels[continuousErrorCounter - 1]);
    }

    private long parseBackoffSeconds(String str) {
        final String unit = str.substring(str.length() - 1);
        final String value = str.substring(0, str.length() - 1);
        final ImmutableMap<String, TimeUnit> timeUnitMap = ImmutableMap
                .of("s", TimeUnit.SECONDS, "m", TimeUnit.MINUTES, "h", TimeUnit.HOURS);
        final TimeUnit timeUnit = timeUnitMap.getOrDefault(unit, TimeUnit.SECONDS);
        return timeUnit.toSeconds(Integer.parseInt(value));
    }
}
