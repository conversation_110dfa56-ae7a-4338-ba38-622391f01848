/*
package com.daddylab.supplier.item.application.virtualWarehouse.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

*/
/**
 * <AUTHOR> up
 * @date 2024年03月11日 4:57 PM
 *//*

@Data
public class VirtualWarehouseSheet {

    @ExcelProperty("仓库名称")
    private String warehouseName;

    @ExcelProperty("商品 SKU")
    private String skuNo;

    @ExcelProperty("商品 SPU")
    private String spuNo;

    @ExcelProperty("条码")
    private String barCode;

    @ExcelProperty("商品名称")
    private String itemName;

    @ExcelProperty("规格名称")
    private String specName;

    @ExcelProperty("品牌")
    private String brand;

    @ExcelProperty("品类")
    private String category;

    @ExcelProperty("库存占比/%")
    private String inventoryRatio;

    @ExcelProperty("可用库存(虚拟仓)")
    private String usableStock;

    @ExcelProperty("可发库存(虚拟仓)")
    private String availableSendStock;

    @ExcelProperty("仓内库存(虚拟仓)")
    private String availableStock;

    @ExcelProperty("合作方")
    private String coryType;





}
*/
