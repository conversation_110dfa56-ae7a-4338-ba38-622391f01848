package com.daddylab.supplier.item.infrastructure.spring.web;

import java.io.IOException;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.NonNull;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * <AUTHOR>
 * @since 2022/12/9
 */
public class XssFilter extends OncePerRequestFilter {

    private final XssConfig xssConfig;

    public XssFilter(XssConfig xssConfig) {
        this.xssConfig = xssConfig;
    }

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request,
            @NonNull HttpServletResponse response,
            @NonNull FilterChain filterChain) throws ServletException, IOException {
        if (xssConfig.getEnable()) {
            filterChain.doFilter(new XssFilterRequestWrapper(request, xssConfig.getMediaTypes()),
                    response);
        } else {
            filterChain.doFilter(request, response);
        }
    }
}
