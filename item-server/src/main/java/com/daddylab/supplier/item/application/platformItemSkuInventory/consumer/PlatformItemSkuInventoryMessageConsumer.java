package com.daddylab.supplier.item.application.platformItemSkuInventory.consumer;

import com.daddylab.supplier.item.application.platformItem.config.PlatformItemSyncConfig;
import com.daddylab.supplier.item.application.platformItemSkuInventory.domain.dto.PlatformItemChangeEvent;
import com.daddylab.supplier.item.application.platformItemSkuInventory.support.PlatformItemSkuInventorySupport;
import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQGroup;
import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQTopic;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @class KSConsumer.java
 * @description 小红书回调消息消费
 * @date 2024-02-29 17:10
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MQTopic.PLAT_ITEM_SKU_TOPIC, consumerGroup = MQGroup.PLAT_ITEM_SKU_INVENTORY_GROUP,
        messageModel = MessageModel.CLUSTERING)
public class PlatformItemSkuInventoryMessageConsumer implements RocketMQListener<PlatformItemChangeEvent> {

    @Autowired
    PlatformItemSkuInventorySupport platformItemSkuInventorySupport;

    @Autowired
    PlatformItemSyncConfig platformItemSyncConfig;

    @Override
    public void onMessage(PlatformItemChangeEvent platformItemChangeEvent) {
        log.debug("[平台商品更新消息] 接收mq到消息 message={}", platformItemChangeEvent);
        if (!platformItemSyncConfig.isDisablePlatformSkuMessageConsumer()) {
            platformItemSkuInventorySupport.handler(platformItemChangeEvent);
        }
    }
}
