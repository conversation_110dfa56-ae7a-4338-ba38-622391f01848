package com.daddylab.supplier.item.infrastructure.winrobot360.types;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;

@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@Data
public class TaskStartParam {

    private String scheduleUuid;
    private List<ScheduleRelaParam> scheduleRelaParams;

    public static TaskStartParam of(String scheduleUuid) {
        TaskStartParam taskStartParam = new TaskStartParam();
        taskStartParam.setScheduleUuid(scheduleUuid);
        return taskStartParam;
    }

}