package com.daddylab.supplier.item.domain.user.gateway;


import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffListQuery;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface UserGateway {
    /**
     * 查询员工信息
     * @param userId 员工用户ID
     * @return 员工信息
     */
    StaffInfo queryStaffInfoById(Long userId);

    /**
     * 批量查询员工信息
     * @param userIds 员工用户ID
     * @return 员工信息 Map<用户ID,员工信息>
     */
    Map<Long, StaffInfo> batchQueryStaffInfoByIds(List<Long> userIds);

    /**
     * 根据条件查询员工信息列表
     * @param query 查询条件
     * @return 员工信息列表
     */
    List<StaffInfo> queryStaffList(StaffListQuery query);

    StaffInfo queryStaffInfoByNickname(String nickname);

    String queryNikcNameByLoginName(String loginName);
}
