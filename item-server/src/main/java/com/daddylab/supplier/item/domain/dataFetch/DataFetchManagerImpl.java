package com.daddylab.supplier.item.domain.dataFetch;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.RandomUtil;

import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.config.RedissonClientInvocationHandler;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataFetch;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DataFetchMapper;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;

import lombok.NonNull;

import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RPermitExpirableSemaphore;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.task.TaskExecutorBuilder;
import org.springframework.boot.task.TaskSchedulerBuilder;
import org.springframework.context.event.EventListener;
import org.springframework.core.task.TaskRejectedException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.jdbc.CannotGetJdbcConnectionException;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

import javax.annotation.Nullable;

/**
 * <AUTHOR>
 * @since 2022/4/24
 */
@Component
public class DataFetchManagerImpl implements DataFetchManager {

  /** ID注册表前缀 */
  public static final String ID_REGISTRY = "FETCH_MANAGER_ID:";

  /** Leader身份缓存前缀 */
  public static final String LEADER = "FETCH_MANAGER_LEADER";

  /** 并发控制缓存前缀 */
  public static final String CONCURRENT_CONTROL_PREFIX = "DATA_FETCHER_CONCURRENT";

  /** 流量控制缓存前缀 */
  public static final String RATE_LIMITER_PREFIX = "DATA_FETCHER_RATE_LIMITER";

  /** 当前已分配的等待拉取的时间段数量缓存 */
  public static final String WAITING_COUNTER_PREFIX = "DATA_FETCHER_WAITING_COUNTER";

  /** 执行线程前缀 */
  public static final String EXECUTOR_THREAD_PREFIX = "FETCH_EXECUTOR";

  /** 调度器线程前缀 */
  public static final String SCHEDULER_THREAD_PREFIX = "FETCH_SCHEDULER";

  /** 主节点的任期（单位秒） */
  public static final int LEADER_TERM = 60;

  /** 注册有效任期（单位秒） */
  public static final int REGISTRY_TERM = 60;

  /** 协调多个实例状态任务的执行间隔（单位秒） */
  public static final int COORDINATE_INTERVAL = 30;

  /** 日志主题名称 */
  public static final String ROOT_LOGGER = "dataFetch";

  /** 管理器主日志收集器 */
  private static final Logger log = org.slf4j.LoggerFactory.getLogger(ROOT_LOGGER);

  /** 当前实例ID */
  public final String id = RandomUtil.randomStringUpper(16);

  /** 各个数据类型的日志收集器 */
  private final LoadingCache<FetchDataType, Logger> logs =
      Caffeine.newBuilder()
          .build(k -> org.slf4j.LoggerFactory.getLogger(ROOT_LOGGER + "." + k.name()));

  /** 任务执行器 */
  private final ThreadPoolTaskExecutor taskExecutor;

  /** 任务调度器 */
  private final ThreadPoolTaskScheduler taskScheduler;

  /** 任务调度器是否被初始化 */
  private final AtomicBoolean taskSchedulerInitialized = new AtomicBoolean(false);

  /** redisson客户端 */
  private final RedissonClient redissonClient;

  /** mybatis mapper */
  private final DataFetchMapper dataFetchMapper;

  /** 配置 */
  private final Configs configs;

  /** 数据拉取器实现对象 */
  private final Collection<Fetcher> fetchers;

  /** 数据拉取器工厂实现对象 */
  private final Collection<FetcherFactory> fetcherFactories;

  /** 当前数据管理器状态 */
  private final AtomicReference<State> state = new AtomicReference<>(State.INITIAL);

  /** 记录当前实例对应数据类型当前数据拉取的偏向（针对拉取优先级策略配置为ROUND的数据类型） */
  private final Map<FetchDataType, Boolean> preferNewDataBiasMap = new ConcurrentHashMap<>();

  /** 记录数据类型拉取是否被挂起 */
  private final Map<FetchDataType, LocalDateTime> suspendFetchMap = new ConcurrentHashMap<>();

  /** 记录数据类型的分段是否被挂起 */
  private final Map<FetchDataType, LocalDateTime> suspendSegmentMap = new ConcurrentHashMap<>();

  /** 挂起当前实例直到超过指定时间 */
  private LocalDateTime suspendTo;

  /** 记录数据类型拉取过程中发生的错误次数（连续错误时会导致对应类型被挂起，错误次数越多，挂起的时间越长） */
  private final Map<FetchDataType, AtomicInteger> errorCounterMap = new ConcurrentHashMap<>();

  /** 管理器主线程 */
  private final Thread managerThread;

  /** 切换前实例注册ID */
  public Integer registerId0;

  /** 当前实例注册ID */
  public Integer registryId;

  /** 存放Leader标识 */
  private RBucket<String> leaderBucket;

  /** Leader实例的ID */
  private boolean isLeader;

  /** 当前实例可能因为配置的最大实例数的限制导致初始化暂停，此时会启动一个调度任务定时尝试继续初始化 使得配置允许时，可以继续启动，成功启动后这个调度任务会被取消 */
  private ScheduledFuture<?> tryInitTask;

  public DataFetchManagerImpl(
      RedissonClient redissonClient,
      DataFetchMapper dataFetchMapper,
      Configs configs,
      Collection<Fetcher> fetchers,
      Collection<FetcherFactory> fetcherFactories) {
    this.redissonClient = RedissonClientInvocationHandler.proxy(redissonClient);
    this.dataFetchMapper = dataFetchMapper;
    this.configs = configs;
    this.fetchers = fetchers;
    this.fetcherFactories = fetcherFactories;
    this.taskExecutor = buildTaskExecutor();
    this.taskScheduler = buildTaskScheduler();
    this.managerThread = new Thread(this::dispatch);
    this.managerThread.setName("FETCH_MANAGER");
    this.managerThread.setDaemon(true);
  }

  private ThreadPoolTaskScheduler buildTaskScheduler() {
    return new TaskSchedulerBuilder().threadNamePrefix(SCHEDULER_THREAD_PREFIX).poolSize(1).build();
  }

  private ThreadPoolTaskExecutor buildTaskExecutor() {
    return new TaskExecutorBuilder()
        .threadNamePrefix(EXECUTOR_THREAD_PREFIX)
        .maxPoolSize(16)
        .corePoolSize(8)
        .allowCoreThreadTimeOut(true)
        .keepAlive(Duration.ofMinutes(1))
        .queueCapacity(100)
        .awaitTermination(true)
        .awaitTerminationPeriod(Duration.ofSeconds(60))
        .build();
  }

  @EventListener(ApplicationReadyEvent.class)
  public void startAfterApplicationReady() {
    start();
  }

  @Override
  public void start() {
    init();
  }

  private synchronized void init() {
    // 初始化调度器
    if (this.taskSchedulerInitialized.compareAndSet(false, true)) {
      log.info("初始化任务调度器");
      this.taskScheduler.initialize();
    }

    final State state = getState();
    switch (state) {
      case SUSPEND_FOR_SCALE:
      case SUSPEND_FOR_DISABLED:
      case PAUSE:
      case RUNNING:
      case STOPPED:
        log.warn("当前实例({})已完成初始化，忽略初始化重复触发，当前状态为{}", id, state);
        return;

      case INITIALIZE:
        log.warn("当前实例({})已经在初始化中，忽略初始化重复触发，当前状态为{}", id, state);
        return;

      case SUSPEND_INITIALIZE:
      case INITIAL:
      default:
        if (Objects.equals(state, State.INITIAL)
            && !this.state.compareAndSet(State.INITIAL, State.INITIALIZE)) {
          log.warn("将当前实例({})的状态从'初始状态'切换成'初始化中'失败，当前状态为'{}'", id, state);
          return;
        } else if (Objects.equals(state, State.SUSPEND_INITIALIZE)
            && !this.state.compareAndSet(State.SUSPEND_INITIALIZE, State.INITIALIZE)) {
          log.warn("将当前实例({})的状态从'初始化挂起'切换成'初始化中'失败，当前状态为'{}'", id, state);
          return;
        }
        log.debug("实例({})开始初始化", fullId());

        // 尝试注册实例
        if (configs.isDisabled() || !tryRegister()) {
          setState(State.SUSPEND_INITIALIZE);
          if (configs.isDisabled()) {
            log.debug("当前实例({})配置被禁用，暂时休眠等待唤醒", fullId());
          } else {
            log.debug("当前实例({})未注册成功，暂时休眠等待唤醒", fullId());
          }

          if (tryInitTask == null) {

            // 如果未获取到实例ID，固定间隔重试（这是为了在配置实例数修改时可以自动扩容）
            tryInitTask =
                this.taskScheduler.scheduleWithFixedDelay(
                    this::init, Instant.now().plusSeconds(60), Duration.ofSeconds(60));

            log.debug("实例({})将以固定间隔调度重试初始化任务", fullId());
          }
          return;
        }

        // 实例ID分配成功则取消定期尝试初始化的任务
        cancelTryInitTask();

        // 初始化心跳维持任务
        initCoordinateTask();

        // 初始化任务执行器
        this.taskExecutor.initialize();

        // 启动管理线程
        this.managerThread.start();

        // 初始化等待中任务计数器
        initWaitingCounter();

        // 初始化清理任务
        initCleanTask();

        this.state.compareAndSet(State.INITIALIZE, State.RUNNING);
        log.info("数据拉取管理器({}:{})运行中...", id, registryId);
    }
  }

  private synchronized State getState() {
    return state.get();
  }

  private synchronized void setState(State state) {
    this.state.set(state);
  }

  private boolean setState(State expect, State update) {
    return this.state.compareAndSet(expect, update);
  }

  /** 取消尝试初始化任务 */
  private void cancelTryInitTask() {
    if (tryInitTask != null) {
      tryInitTask.cancel(false);
      tryInitTask = null;

      log.debug("实例({}):固定间隔调度重试初始化任务已取消", fullId());
    }
  }

  /**
   * 尝试注册实例
   *
   * @return 是否分配成功（超出配置实例数会导致分配失败）
   */
  private boolean tryRegister() {
    if (this.registryId == null) {
      for (int tryRegisterId = 0; tryRegisterId < configs.getMaxInstanceNum(); tryRegisterId++) {

        final RBucket<String> idBucket = getRegistry(tryRegisterId);

        // 检查如果当前注册位为空，则将当前实例ID注册到这个未知
        if (idBucket.compareAndSet(null, id)) {
          // 注册成功记录当前注册ID
          this.registryId = tryRegisterId;

          // 设置注册时间
          expireTerm(idBucket);

          log.info("实例({}:{})注册成功", id, registryId);
          break;
        }
      }
    }

    // 注册ID为空说明未注册成功
    return this.registryId != null;
  }

  /**
   * 获取实例注册位
   *
   * @param tryRegistryId 注册ID
   */
  private RBucket<String> getRegistry(int tryRegistryId) {
    return redissonClient.getBucket(ID_REGISTRY + tryRegistryId);
  }

  /** 初始化清理任务调度 */
  private void initCleanTask() {
    this.taskScheduler.scheduleWithFixedDelay(this::cleanTask, Duration.ofMinutes(10));
  }

  /** 清理历史记录 */
  private void cleanTask() {
    if (!isLeader()) {
      return;
    }
    log.info("实例({}):执行清理任务", fullId());
    for (FetcherConfig fetcherConfig : configs.getFetcherConfigs()) {
      if (getState() != State.RUNNING) {
        break;
      }

      final DataFetch endRecord =
          dataFetchMapper.getNthRecord(
              fetcherConfig.getFetchDataType(),
              fetcherConfig.getRound(),
              fetcherConfig.getPreserveHead(),
              true);
      final DataFetch startRecord =
          dataFetchMapper.getNthRecord(
              fetcherConfig.getFetchDataType(),
              fetcherConfig.getRound(),
              fetcherConfig.getPreserveTail(),
              false);
      if (endRecord == null || startRecord == null) {
        continue;
      }

      LocalDateTime cleanEndLimit = endRecord.getFetchPoint();
      LocalDateTime offsetFetchPoint = startRecord.getFetchPoint();
      if (offsetFetchPoint.compareTo(cleanEndLimit) >= 0) {
        getLogger(fetcherConfig).info("没有需要清理的记录");
        continue;
      }
      getLogger(fetcherConfig).info("清理时间范围在{}到{}之间的分段", offsetFetchPoint, cleanEndLimit);
      int limit = 100;
      boolean stop = false;

      while (true) {
        final List<DataFetch> list =
            dataFetchMapper.list(
                fetcherConfig.getFetchDataType(),
                fetcherConfig.getRound(),
                offsetFetchPoint,
                limit);
        int continuousSuccessCount = 0;
        for (int i = 0; i < list.size(); i++) {
          final DataFetch dataFetch = list.get(i);

          // 跳过初始化数据节点
          if (dataFetch.getFetchPoint() == null) {
            continue;
          }

          if (dataFetch.getFetchPoint().isAfter(cleanEndLimit)) {
            stop = true;
            break;
          }
          if (!dataFetch.getStatus().equals(FetchStatus.SUCCESS.getValue())) {
            if (continuousSuccessCount > 1) {
              final ArrayList<Long> needDeletedRecordIds = new ArrayList<>();
              do {
                needDeletedRecordIds.add(list.get(i - continuousSuccessCount).getId());
              } while (--continuousSuccessCount > 1);
              continuousSuccessCount = 0;
              dataFetchMapper.deleteRecordsByIds(needDeletedRecordIds);
              getLogger(fetcherConfig).info("已清理:{}", needDeletedRecordIds);
            }
            continue;
          }
          continuousSuccessCount++;
        }
        if (continuousSuccessCount > 1) {
          final ArrayList<Long> needDeletedRecordIds = new ArrayList<>();
          do {
            needDeletedRecordIds.add(list.get(list.size() - continuousSuccessCount).getId());
          } while (--continuousSuccessCount > 1);
          dataFetchMapper.deleteRecordsByIds(needDeletedRecordIds);
          getLogger(fetcherConfig).info("已清理:{}", needDeletedRecordIds);
        }
        if (stop) {
          break;
        }
        offsetFetchPoint = list.get(list.size() - 1).getFetchPoint();
      }
    }
  }

  /** 初始化实例状态协调任务 */
  private void initCoordinateTask() {
    this.taskScheduler.scheduleWithFixedDelay(
        this::coordinate, Duration.ofSeconds(COORDINATE_INTERVAL));
  }

  /** 协调多个实例之间的状态（刷新注册时间、协商主实例） */
  private void coordinate() {
    // 如果配置为禁用，暂停当前实例
    if (configs.isDisabled()) {

      if (getState() == State.RUNNING) {
        setState(State.SUSPEND_FOR_DISABLED);
        log.info("实例({})由于配置禁用的原因挂起", fullId());
      }
      return;
    } else if (getState() == State.SUSPEND_FOR_DISABLED) {
      if (setState(State.SUSPEND_FOR_DISABLED, State.RUNNING)) {
        log.info("实例({})从配置禁用中恢复", fullId());
      }
    }

    // 如果心跳已经丢失需要先尝试重新注册
    if (registryId == null) {

      // 重新获取实例ID成功
      if (tryRegister()) {
        log.info("实例重新注册成功，注册ID发生切换({}:{} -> {})", id, registerId0, registryId);

        // 因注册状态丢失而挂起的恢复状态
        if (getState() == State.SUSPEND_FOR_MISSING_REGISTER) {
          setState(State.SUSPEND_FOR_MISSING_REGISTER, State.RUNNING);
          log.info("实例({})重新注册成功，从挂起中恢复", fullId());
        }

        if (getState() == State.SUSPEND_FOR_SCALE) {
          if (setState(State.SUSPEND_FOR_SCALE, State.RUNNING)) {
            log.info("实例({})最大实例数配置修改，从挂起中恢复", fullId());
          }
        }
      }
      return;
    }

    if (registryId >= configs.getMaxInstanceNum()) {
      if (unregister(State.SUSPEND_FOR_SCALE)) {
        log.info("实例({})因为减少了最大实例数配置导致当前运行中的实例挂起", fullId());
        return;
      }
    }

    final RBucket<String> registry = getRegistry(registryId);

    // 检查实例注册位是否被其他实例抢占
    if (registry.compareAndSet(id, id)) {

      // 维持抢占心跳时间
      expireTerm(registry);

      // 竞争主节点
      negotiateLeader();

      return;
    }

    // 放弃当前实例ID
    if (unregister(State.SUSPEND_FOR_MISSING_REGISTER)) {
      log.warn("实例({}:{})丢失注册状态", id, registryId);
    }
  }

  /**
   * 注册延期
   *
   * @param registry 实例注册
   */
  private void expireTerm(RBucket<String> registry) {
    registry.expire(REGISTRY_TERM, TimeUnit.SECONDS);
  }

  /** 当前实例是否为Leader */
  private boolean isLeader() {
    return isLeader;
  }

  /** 协商主节点 */
  private void negotiateLeader() {
    if (registryId != null) {
      final RBucket<String> leaderBucket = getLeaderBucket();

      // 如果强制指定 LEADER，则强制将当前实例擢升
      if (configs.getForceLeaderId() == registryId && !isLeader()) {
        isLeader = true;
        final String beforeLeaderId =
            leaderBucket.getAndSet(fullId(), LEADER_TERM, TimeUnit.SECONDS);

        // 挂起当前实例等待其他实例退出
        timedSuspend(COORDINATE_INTERVAL);

        if (beforeLeaderId != null) {
          if (!beforeLeaderId.equals(fullId())) {
            log.info("实例({}:{})强制成为主实例，上任主实例({})", id, registryId, beforeLeaderId);
          }
        } else {
          log.info("实例({}:{})强制成为主实例", id, registryId);
        }
        return;
      }

      // 当前节点不是主节点，则试图竞争主节点
      final String fullId = fullId();
      if (!isLeader()) {

        // 如果主节点失联则顶替他
        if (leaderBucket.compareAndSet(null, fullId)) {
          log.info("普通实例({}:{})晋升为主实例", id, registryId);
          isLeader = true;
          leaderBucket.expire(LEADER_TERM, TimeUnit.SECONDS);
        }
      } else {
        // 如果是主节点，且当前任期未过
        if (leaderBucket.compareAndSet(fullId, fullId)) {

          // 延长任期
          leaderBucket.expire(LEADER_TERM, TimeUnit.SECONDS);
          log.debug(
              "当前主实例({}:{})延长任期到:{}", id, registryId, LocalDateTime.now().plusSeconds(LEADER_TERM));
        }

        // 说明已经失去了主节点的身份，获取当前主节点的ID
        else {
          // 部分缓存只有保证是主节点的情况下才是准确的，失去主节点的身份需要失败缓存
          clearCache();

          // 如果当前主实例ID为空，尝试重新成为主实例
          if (leaderBucket.get() == null && leaderBucket.compareAndSet(null, fullId)) {
            return;
          }
          isLeader = false;
          log.warn("主实例({}:{})退化为普通实例，当前主实例ID:{}", id, registryId, leaderBucket.get());
        }
      }
    }
  }

  /**
   * 挂起当前实例
   *
   * @param suspendSeconds 挂起多少秒
   */
  private void timedSuspend(int suspendSeconds) {
    suspendTo = LocalDateTime.now().plusSeconds(suspendSeconds);
  }

  /** 获取完整的ID（实例ID加注册ID拼接） */
  private String fullId() {
    return id + ":" + (registryId != null ? registryId : "");
  }

  /** 缓存失效 */
  private void clearCache() {
    initWaitingCounter();
  }

  private RBucket<String> getLeaderBucket() {
    if (leaderBucket != null) {
      return leaderBucket;
    }
    leaderBucket = redissonClient.getBucket(LEADER);
    return leaderBucket;
  }

  /**
   * 心跳丢失需要重置当前实例注册ID
   *
   * @param update 更新状态
   */
  private boolean unregister(State update) {
    if (setState(State.RUNNING, update)) {
      registerId0 = registryId;
      registryId = null;
      return true;
    }
    return false;
  }

  @Override
  public void pause() {
    if (!state.compareAndSet(State.RUNNING, State.PAUSE)) {
      throw new StateSwitchException(fullId(), getState(), State.PAUSE, "当前状态不是" + State.RUNNING);
    }
    log.info("数据拉取管理器({})已暂停", fullId());
  }

  @Override
  public void resume() {
    if (state.get().equals(State.RUNNING)) {
      return;
    }
    if (!state.compareAndSet(State.PAUSE, State.RUNNING)) {
      throw new StateSwitchException(fullId(), getState(), State.RUNNING, "当前状态不是" + State.PAUSE);
    }
    log.info("数据拉取管理器({})恢复运行", fullId());
  }

  @Override
  public void stop() {
    if (state.get() == State.STOPPED) {
      return;
    }
    if (!state.compareAndSet(State.RUNNING, State.STOPPED)) {
      throw new StateSwitchException(fullId(), getState(), State.STOPPED, "当前状态不是" + State.RUNNING);
    }
    this.taskExecutor.shutdown();
    this.taskScheduler.shutdown();
    this.managerThread.interrupt();
    log.info("数据拉取管理器({})已终止", fullId());
  }

  /** 管理器主线程状态控制逻辑 */
  @SuppressWarnings("BusyWait")
  public void dispatch() {
    final Thread thread = Thread.currentThread();
    L1:
    while (!thread.isInterrupted()) {
      try {
        if (isSuspend()) {
          Thread.sleep(1000);
          continue;
        }

        switch (getState()) {
          case SUSPEND_INITIALIZE:
          case INITIAL:
          case INITIALIZE:
          case PAUSE:
          case SUSPEND_FOR_SCALE:
          case SUSPEND_FOR_DISABLED:
          case SUSPEND_FOR_MISSING_REGISTER:
            Thread.sleep(1000);

          case RUNNING:
            doDispatch();
            sleepIfConfigured();
            break;

          case STOPPED:
            break L1;
        }
      } catch (InterruptedException e) {
        thread.interrupt();
      } catch (TaskRejectedException e) {
        log.info("实例({}):任务执行队列满，主线程进入休眠", fullId());
        sleep(thread, 1000);
      } catch (Throwable throwable) {
        if (throwable instanceof CannotGetJdbcConnectionException
            && Optional.ofNullable(throwable.getCause())
                .map(Throwable::getMessage)
                .filter(v -> StringUtil.contain(v, "closed"))
                .isPresent()) {
          return;
        }
        log.error("数据拉取管理器(" + fullId() + ")调度过程遇到异常:" + throwable.getMessage(), throwable);

        // 为了避免遇到无法恢复的异常导致疯狂刷屏，每次异常时休眠一秒
        sleep(thread, 1000);
      }
    }
    stop();
  }

  /** 当前实例是否被挂起 */
  private boolean isSuspend() {
    return suspendTo != null && LocalDateTime.now().isBefore(suspendTo);
  }

  /** 取消所有挂起状态 */
  public void cancelSuspend() {
    suspendTo = null;
    suspendSegmentMap.clear();
    suspendFetchMap.clear();
  }

  /** 使线程休眠，如果线程休眠过程中被打断，则退出休眠然后设置中断标识 */
  private void sleep(Thread thread, int millis) {
    try {
      Thread.sleep(millis);
    } catch (InterruptedException exception) {
      thread.interrupt();
    }
  }

  /** 是否在每次调度完成后休眠一段时间（默认10毫秒） */
  private void sleepIfConfigured() throws InterruptedException {
    if (configs.getSleepMillisAfterPerDispatch() > 0) {
      Thread.sleep(configs.getSleepMillisAfterPerDispatch());
    }
  }

  /** 管理器主线程调度逻辑 */
  public void doDispatch() {
    for (FetcherConfig fetcherConfig : configs.getFetcherConfigs()) {
      // 调度过程中遇到注册ID丢失则直接退出
      if (registryId == null) {
        break;
      }

      // 如果修改了配置的最大实例数，且当前注册ID大于最大实例，暂时挂起当前实例
      if (registryId >= configs.getMaxInstanceNum()) {
        break;
      }

      // 当前实例配置不为运行中
      if (getState() != State.RUNNING) {
        break;
      }
      final FetchDataType fetchDataType = fetcherConfig.getFetchDataType();

      // 如果当前数据类型不支持根据时间分段查询不需要做分段操作
      if (fetcherConfig.isSegmentFetch()) {
        // 主节点才有权进行分段操作
        if (isLeader()) {

          // 当前分段操作是否挂起，已挂起则跳过
          if (!isSuspendSegment(fetchDataType)) {

            final int segmentSelect = segmentSelect(fetcherConfig);
            if (segmentSelect == 0) {
              suspendSegment(fetcherConfig, fetcherConfig.getSuspendSecondsForAllSegmented());
            } else if (segmentSelect == 1) {
              dispatchSegmentUp(fetchDataType);
            } else {
              dispatchSegmentDown(fetchDataType);
            }
          }
        }
      }

      if (!isSuspendFetch(fetchDataType)) {
        dispatchFetch(fetcherConfig);
      }
    }
  }

  /** 获取当前配置最新记录 */
  private LocalDateTime getLatestFetchPoint(FetcherConfig fetcherConfig) {
    return queryLatestRecord(fetcherConfig).map(DataFetch::getFetchPoint).orElse(null);
  }

  /** 查询当前配置最新记录 */
  private Optional<DataFetch> queryLatestRecord(FetcherConfig fetcherConfig) {
    return Optional.ofNullable(
        dataFetchMapper.getLatestRecord(
            fetcherConfig.getFetchDataType(), fetcherConfig.getRound()));
  }

  /**
   * 初始化原点
   *
   * @param fetcherConfig 配置
   * @param time 时间
   */
  private DataFetch initialSegment(FetcherConfig fetcherConfig, LocalDateTime time) {
    final AtomicReference<DataFetch> initRecord = new AtomicReference<>();
    doSegmentWithLock(
        SegmentLock.SEGMENT_INIT,
        fetcherConfig,
        () -> {
          final DataFetch newRecord = newSegment(fetcherConfig, time);

          if (newRecord != null) {
            getLogger(fetcherConfig)
                .debug(
                    "实例({}):{}:初始化原点:{}", fullId(), fetcherConfig.getFetchDataType().name(), time);
            initRecord.set(newRecord);
          }
        });
    return initRecord.get();
  }

  /**
   * 指定数据类型对新数据进行分段
   *
   * @param fetchDataType 数据类型
   */
  public void dispatchSegmentUp(FetchDataType fetchDataType) {
    dispatchSegmentUp(getFetcherConfig(fetchDataType));
  }

  /**
   * 指定数据类型对新数据进行分段
   *
   * @param fetcherConfig 配置
   */
  private void dispatchSegmentUp(FetcherConfig fetcherConfig) {
    doSegmentWithLock(
        SegmentLock.SEGMENT_UP,
        fetcherConfig,
        () -> {
          LocalDateTime latestFetchPoint = getLatestFetchPoint(fetcherConfig);
          final LocalDateTime now = LocalDateTime.now();
          if (latestFetchPoint == null) {
            final DataFetch initialRecord = initialSegment(fetcherConfig, now);
            if (initialRecord == null) {
              return;
            }
            latestFetchPoint = initialRecord.getFetchPoint();
          }
          doSegmentUp(fetcherConfig, latestFetchPoint);
        });
  }

  /**
   * 指定数据类型对新数据进行分段
   *
   * @param fetcherConfig 配置
   * @param segmentFrom 从哪个时间点开始分段
   */
  private void doSegmentUp(FetcherConfig fetcherConfig, LocalDateTime segmentFrom) {
    final FetchDataType fetchDataType = fetcherConfig.getFetchDataType();
    final LocalDateTime endLimit = fetcherConfig.getEndLimitDateTime();
    LocalDateTime cursor0;
    LocalDateTime cursor = segmentFrom;
    while (true) {
      cursor0 = cursor;
      LocalDateTime cursor1 = cursor.plus(fetcherConfig.getMaxSegment());
      if (cursor1.isAfter(endLimit)) {
        final Duration duration = Duration.between(cursor, endLimit);
        if (duration.compareTo(fetcherConfig.getMinSegment()) < 0) {
          break;
        } else {
          cursor = endLimit;
        }
      } else {
        cursor = cursor1;
      }
      if (!incrementWaitingCount(fetcherConfig)) {
        getLogger(fetcherConfig)
            .info(
                "实例({}):{}:等待执行的分段数量达到配置的最大数量{}，暂停分段",
                fullId(),
                fetcherConfig.getFetchDataType().name(),
                fetcherConfig.getMaxWaitingSegmentCount());
        suspendSegment(fetcherConfig, fetcherConfig.getSuspendSecondsForMaxWaitingSegments());
        break;
      }
      newSegment(fetcherConfig, cursor, FetchStatus.WAIT);
      getLogger(fetcherConfig)
          .info("实例({}):{}[{},{}]:已分段", fullId(), fetchDataType.name(), cursor0, cursor);
    }
  }

  /** 获取指定周期最早的记录 */
  private LocalDateTime getEarliestFetchPoint(FetcherConfig fetcherConfig) {
    return queryEarliestRecord(fetcherConfig).map(DataFetch::getFetchPoint).orElse(null);
  }

  /** 查询指定周期最早的记录 */
  private Optional<DataFetch> queryEarliestRecord(FetcherConfig fetcherConfig) {
    return Optional.ofNullable(
        dataFetchMapper.getEarliestRecord(
            fetcherConfig.getFetchDataType(), fetcherConfig.getRound()));
  }

  /**
   * 指定数据类型对老数据进行分段
   *
   * @param dataType 数据类型
   */
  public void dispatchSegmentDown(FetchDataType dataType) {
    final FetcherConfig fetcherConfig = getFetcherConfig(dataType);
    dispatchSegmentDown(fetcherConfig);
  }

  /**
   * 指定数据类型对老数据进行分段
   *
   * @param fetcherConfig 配置
   */
  private void dispatchSegmentDown(FetcherConfig fetcherConfig) {
    doSegmentWithLock(
        SegmentLock.SEGMENT_DOWN,
        getFetcherConfig(fetcherConfig.getFetchDataType()),
        () -> {
          LocalDateTime earliestFetchPoint = getEarliestFetchPoint(fetcherConfig);
          final LocalDateTime now = LocalDateTime.now();
          if (earliestFetchPoint == null) {
            final DataFetch initialRecord = initialSegment(fetcherConfig, now);
            if (initialRecord == null) {
              return;
            }
            earliestFetchPoint = initialRecord.getFetchPoint();
          }
          doSegmentDown(fetcherConfig, earliestFetchPoint);
        });
  }

  /**
   * 指定数据类型对老数据进行分段
   *
   * @param fetcherConfig 配置
   * @param segmentFrom 从哪个时间点开始分段
   */
  private void doSegmentDown(FetcherConfig fetcherConfig, LocalDateTime segmentFrom) {
    LocalDateTime startLimit = fetcherConfig.getStartLimitDateTime();
    if (segmentFrom.compareTo(startLimit) <= 0) {
      getLogger(fetcherConfig)
          .debug(
              "实例({}):{}:达到配置的开始时间:{}，不再继续向下分段",
              fullId(),
              fetcherConfig.getFetchDataType(),
              startLimit);
      return;
    }

    LocalDateTime cursor = segmentFrom;
    LocalDateTime cursor1;
    boolean reachStartLimit = false;
    while (true) {
      cursor1 = cursor;
      LocalDateTime cursor0 = cursor.minus(fetcherConfig.getMaxSegment());
      if (cursor0.compareTo(startLimit) <= 0) {
        cursor = startLimit;
        reachStartLimit = true;
      } else {
        cursor = cursor0;
      }
      if (!incrementWaitingCount(fetcherConfig)) {
        getLogger(fetcherConfig)
            .debug(
                "实例({}):{}:等待执行的分段数量达到配置的最大数量{}，暂停分段",
                fullId(),
                fetcherConfig.getFetchDataType().name(),
                fetcherConfig.getMaxWaitingSegmentCount());
        suspendSegment(fetcherConfig, fetcherConfig.getSuspendSecondsForMaxWaitingSegments());
        break;
      }
      newSegment(fetcherConfig, cursor);
      getLogger(fetcherConfig)
          .info(
              "实例({}):{}[{},{}]:已分段",
              fullId(),
              fetcherConfig.getFetchDataType().name(),
              cursor,
              cursor1);
      if (reachStartLimit) {
        getLogger(fetcherConfig).debug("实例({}):达到配置的开始时间:{}，不再继续向下分段", fullId(), startLimit);
        break;
      }
    }
  }

  /**
   * 初始化数据
   *
   * @param fullFetchRecord 初始化记录
   * @param fetcherConfig 配置
   */
  private void fullFetch(DataFetch fullFetchRecord, FetcherConfig fetcherConfig) {
    // 计算初始化锁定时长
    fullFetchRecord.setLockTime(
        LocalDateTime.now().plusSeconds(fetcherConfig.getLockSecondsForFullFetch()));

    // 尝试锁定记录
    if (dataFetchMapper.lock(fullFetchRecord.getId(), fullFetchRecord.getLockTime()) > 0) {
      getLogger(fetcherConfig)
          .debug("实例({}):{}:初始化数据锁定成功", fullId(), fetcherConfig.getFetchDataType().name());

      // 构造执行上下文
      final RunData runData = fullFetchRecord.parseRunData();
      setPersistCallback(fullFetchRecord.getId(), runData);
      final RunContext runContext =
          new RunContext(runData, getLockTimeRenewCallback(fullFetchRecord.getId()));

      // 投递任务到执行队列
      taskExecutor.execute(
          () -> {
            Throwable e = null;
            try {
              final Fetcher fetcher =
                  getFetcher(getFetcherConfig(fetcherConfig.getFetchDataType()));
              if (fetcher instanceof PageFetcher) {
                initPageFetch(fetcherConfig, runContext);
                while (fetcherConfig.isReverseFetch() && runData.getPageIndex() > 0
                    || !fetcherConfig.isReverseFetch()
                        && runData.getPageIndex() <= runData.getMaxPage()) {
                  if (isDisabled(fetcherConfig)) {
                    throw new FetchInterruptedException(
                        fetcherConfig.getFetchDataType(), "配置禁用导致全量拉取中断");
                  }
                  final LocalDateTime now = LocalDateTime.now();
                  if (now.isAfter(fullFetchRecord.getLockTime().minusSeconds(3))) {
                    fullFetchRecord.setLockTime(now.plusSeconds(fetcherConfig.getLockSeconds()));
                    runContext.getLockTimeRenewCallback().accept(fullFetchRecord.getLockTime());
                  }
                  ((PageFetcher) fetcher)
                      .fetch(runData.getPageIndex(), fetcherConfig.getPageSize(), runContext);
                  if (fetcherConfig.isReverseFetch()) {
                    runData.subtractPageIndex();
                  } else {
                    runData.addPageIndex();
                  }
                  if (runData.getPageIndex() > fetcherConfig.getPersistPageInterval()
                      && runData.getPageIndex() % fetcherConfig.getPersistPageInterval() == 0) {
                    runData.persist();
                  }
                }
                runData.setFetched(true);
              } else if (fetcher instanceof CustomFetcher) {
                ((CustomFetcher) fetcher).fetch(runContext);
              }

            } catch (Throwable exception) {
              e = exception;
            } finally {
              final AtomicInteger errorCounter = getErrorCounter(fetcherConfig);

              fullFetchRecord.setData(JsonUtil.toJson(runData));
              // 完成执行，解锁并标记状态
              if (e == null) {
                fullFetchRecord.setStatus(FetchStatus.SUCCESS.getValue());
                errorCounter.set(0);
                getLogger(fetcherConfig)
                    .info("实例({}):{}:初始化数据完成", fullId(), fetcherConfig.getFetchDataType().name());
              } else if (e instanceof FetchInterruptedException) {
                runData.persist();
                getLogger(fetcherConfig).warn("实例({}):{}", fullId(), e.getMessage());
              } else {
                getLogger(fetcherConfig)
                    .error(
                        "实例({}):{} 初始化数据失败，异常信息:{}",
                        fullId(),
                        fetcherConfig.getFetchDataType(),
                        e.getMessage() == null ? e.getClass().getSimpleName() : e.getMessage(),
                        e);
                errorCounter.incrementAndGet();

                runData.addFailCount();
                fullFetchRecord.setErr(ExceptionUtil.stacktraceToString(e));

                if (runData.getFailCount() >= fetcherConfig.getSkipOnContinuousError()) {
                  fullFetchRecord.setStatus(FetchStatus.ERROR.getValue());
                }

                if (runData.getFailCount() >= fetcherConfig.getAlertOnContinuousError()) {
                  Alert.text(
                      MessageRobotCode.GLOBAL,
                      "数据类型:"
                          + fetcherConfig.getFetchDataType().getDesc()
                          + " 初始化数据失败，异常信息:\n"
                          + fullFetchRecord.getErr(),
                      true);
                }

                if (e instanceof TryLaterException) {
                  suspendByTryLaterException(fetcherConfig, (TryLaterException) e);
                } else {
                  suspendFetchByErrorCounter(fetcherConfig);
                }
              }
              dataFetchMapper.updateStateAndUnLockByRecord(fullFetchRecord);
            }
          });
    }
  }

  /**
   * 根据当前的错误计数来挂起
   *
   * @param fetcherConfig 配置
   */
  private void suspendFetchByErrorCounter(FetcherConfig fetcherConfig) {
    suspendFetch(
        fetcherConfig,
        LocalDateTime.now()
            .plusSeconds(fetcherConfig.backoffSeconds(getErrorCounter(fetcherConfig).get())));
  }

  /**
   * 初始化分页数据
   *
   * @param fetcherConfig 配置
   * @param runContext 运行环境
   */
  private void initPageFetch(FetcherConfig fetcherConfig, RunContext runContext) {
    final RunData runData = runContext.getRunData();
    if (runData.getPageIndex() == null || runData.getFetched()) {
      runData.setTotal(((PageFetcher) getFetcher(fetcherConfig)).getTotal());
      runData.setPageSize(fetcherConfig.getPageSize());
      runData.setMaxPage((int) Math.ceil(runData.getTotal() / (float) runData.getPageSize()));
      runData.setPageIndex(fetcherConfig.isReverseFetch() ? runData.getMaxPage() : 1);
      runData.setReverseFetch(fetcherConfig.isReverseFetch());
      runData.setFetched(false);
    }
  }

  /**
   * 初始化分页数据
   *
   * @param fetcherConfig 配置
   * @param runContext 运行环境
   */
  private void initPageFetch(
      FetcherConfig fetcherConfig, RunContext runContext, FetchSegment fetchSegment) {
    final RunData runData = runContext.getRunData();
    if (runData.getPageIndex() == null) {
      runData.setTotal(
          ((PageFetcher) getFetcher(fetcherConfig))
              .getTotal(fetchSegment.getStartTime(), fetchSegment.getEndTime()));
      runData.setPageSize(fetcherConfig.getPageSize());
      runData.setMaxPage((int) Math.ceil(runData.getTotal() / (float) runData.getPageSize()));
      runData.setPageIndex(fetcherConfig.isReverseFetch() ? runData.getMaxPage() : 1);
      runData.setReverseFetch(fetcherConfig.isReverseFetch());
      runData.setFetched(false);
    }
  }

  /**
   * 创建新的段
   *
   * @param fetcherConfig 配置
   * @param fetchPoint 时间节点
   */
  private DataFetch newSegment(FetcherConfig fetcherConfig, LocalDateTime fetchPoint) {
    return newSegment(fetcherConfig, fetchPoint, FetchStatus.WAIT);
  }

  /**
   * 创建新的段
   *
   * @param fetcherConfig 配置
   * @param fetchPoint 时间节点
   * @param status 状态
   */
  private DataFetch newSegment(
      FetcherConfig fetcherConfig, LocalDateTime fetchPoint, FetchStatus status) {
    try {
      final DataFetch dataFetch = new DataFetch();
      dataFetch.setDataType(fetcherConfig.getFetchDataType().getValue());
      dataFetch.setStatus(status.getValue());
      dataFetch.setFetchPoint(fetchPoint);
      dataFetch.setRound(fetcherConfig.getRound());
      dataFetchMapper.insert(dataFetch);
      return dataFetch;
    } catch (DuplicateKeyException e) {
      return null;
    }
  }

  @Override
  public void reFetch(FetchDataType fetchDataType, LocalDateTime startTime, LocalDateTime endTime) {
    final FetcherConfig fetcherConfig = getFetcherConfig(fetchDataType);
    doSegmentWithLock(
        SegmentLock.RE_SEGMENT,
        fetcherConfig,
        () -> {
          final DataFetch latestRecord =
              dataFetchMapper.getLatestRecord(fetchDataType, fetcherConfig.getRound());
          final DataFetch earliestRecord =
              dataFetchMapper.getEarliestRecord(fetchDataType, fetcherConfig.getRound());

          if (latestRecord == null
              || earliestRecord == null
              || startTime.isBefore(earliestRecord.getFetchPoint())
              || endTime.isAfter(latestRecord.getFetchPoint())) {
            throw new IllegalArgumentException(
                "当前时间段[" + startTime + "," + endTime + "]还未拉取，请等待调度");
          }

          final List<Long> recordIds =
              dataFetchMapper.getRecordIdsBetween(
                  fetchDataType, startTime, endTime, true, fetcherConfig.getRound());

          if (!recordIds.isEmpty()) {
            getLogger(fetcherConfig)
                .info(
                    "实例({}):{}[{},{}]:ID为{}的记录被删除",
                    fullId(),
                    fetchDataType,
                    startTime,
                    endTime,
                    recordIds);
            dataFetchMapper.deleteRecordsByIds(recordIds);
          }

          newSegment(fetcherConfig, endTime);

          LocalDateTime cursor = endTime;
          LocalDateTime cursor1;
          boolean reachStartLimit = false;
          do {
            cursor1 = cursor;
            cursor = cursor.minus(fetcherConfig.getMaxSegment());

            if (cursor.compareTo(startTime) <= 0) {
              reachStartLimit = true;
              cursor = startTime;
            }
            newSegment(
                fetcherConfig, cursor, reachStartLimit ? FetchStatus.SUCCESS : FetchStatus.WAIT);
            getLogger(fetcherConfig)
                .info(
                    "实例({}):{}[{},{}]:已分段",
                    fullId(),
                    fetcherConfig.getFetchDataType().name(),
                    cursor,
                    cursor1);

          } while (!reachStartLimit);
        });
  }

  /**
   * 获取分段锁，成功则执行分段操作，失败则返回false
   *
   * @param segmentLock 分段锁类型
   * @param fetcherConfig 配置
   * @param runnable 实际分段操作
   */
  private void doSegmentWithLock(
      SegmentLock segmentLock, FetcherConfig fetcherConfig, Runnable runnable) {
    if (isDisabled(fetcherConfig)) {
      return;
    }
    final RLock lock =
        redissonClient.getLock(segmentLock.name() + ":" + fetcherConfig.getFetchDataType());
    if (lock.tryLock()) {
      try {
        runnable.run();
      } catch (Throwable e) {
        getLogger(fetcherConfig.getFetchDataType())
            .error("实例({}):{}:分段异常", fullId(), fetcherConfig.getFetchDataType().name(), e);
        suspendSegment(fetcherConfig, fetcherConfig.getSuspendSecondsForSegmentException());
      } finally {
        lock.unlock();
      }
    } else {
      getLogger(fetcherConfig)
          .debug(
              "实例({}):{}:未能成功获取分段锁{}，跳过处理",
              fullId(),
              fetcherConfig.getFetchDataType().name(),
              segmentLock.name());
    }
  }

  /**
   * 获取数据类型对应的配置
   *
   * @param dataType 数据类型
   * @return 配置类
   */
  private FetcherConfig getFetcherConfig(FetchDataType dataType) {
    for (FetcherConfig fetcherConfig : configs.getFetcherConfigs()) {
      if (fetcherConfig.getFetchDataType() == dataType) {
        return fetcherConfig;
      }
    }
    throw new RuntimeException("数据类型未配置:" + dataType);
  }

  /**
   * 获取信号量
   *
   * @param fetcherConfig 配置
   * @return 成功返回准入ID，失败返回null
   */
  private String acquireSemaphore(FetcherConfig fetcherConfig) {
    final RPermitExpirableSemaphore semaphore = getConcurrentControlSemaphore(fetcherConfig);
    try {
      return semaphore.tryAcquire(0, fetcherConfig.getLockSeconds(), TimeUnit.SECONDS);
    } catch (InterruptedException ignored) {
    }
    return null;
  }

  /**
   * 释放信号量
   *
   * @param fetcherConfig 配置
   */
  private void releaseSemaphore(FetcherConfig fetcherConfig, String permitId) {
    final RPermitExpirableSemaphore semaphore = getConcurrentControlSemaphore(fetcherConfig);
    semaphore.release(permitId);
  }

  /**
   * 获取并发控制信号量对象
   *
   * @param fetcherConfig 配置
   */
  private RRateLimiter getRateLimiter(FetcherConfig fetcherConfig) {
    final FetchDataType fetchDataType = fetcherConfig.getFetchDataType();
    final RRateLimiter rateLimiter =
        redissonClient.getRateLimiter(RATE_LIMITER_PREFIX + ":" + fetchDataType);
    rateLimiter.setRate(
        RateType.OVERALL,
        fetcherConfig.getRate(),
        fetcherConfig.getRateInterval(),
        RateIntervalUnit.MINUTES);
    return rateLimiter;
  }

  /**
   * 获取并发控制信号量对象
   *
   * @param fetcherConfig 配置
   */
  private RPermitExpirableSemaphore getConcurrentControlSemaphore(FetcherConfig fetcherConfig) {
    final FetchDataType fetchDataType = fetcherConfig.getFetchDataType();
    final RPermitExpirableSemaphore semaphore =
        redissonClient.getPermitExpirableSemaphore(CONCURRENT_CONTROL_PREFIX + ":" + fetchDataType);
    semaphore.trySetPermits(fetcherConfig.getConcurrent());
    return semaphore;
  }

  /** 初始化待执行计数器 */
  private void initWaitingCounter() {
    for (FetcherConfig fetcherConfig : configs.getFetcherConfigs()) {
      final int countWaitingSegments =
          dataFetchMapper.countWaitingSegments(
              fetcherConfig.getFetchDataType(), fetcherConfig.getRound());
      getWaitingCounter(fetcherConfig).setAsync(countWaitingSegments);
    }
  }

  /**
   * 获取待执行计数器
   *
   * @param fetcherConfig 配置
   * @return 计数器对象
   */
  private RAtomicLong getWaitingCounter(FetcherConfig fetcherConfig) {
    return redissonClient.getAtomicLong(
        WAITING_COUNTER_PREFIX + ":" + fetcherConfig.getFetchDataType() + fetcherConfig.getRound());
  }

  /**
   * 增加待执行计数
   *
   * @param fetcherConfig 配置
   * @return 是否增加成功
   */
  private boolean incrementWaitingCount(FetcherConfig fetcherConfig) {
    final RAtomicLong waitingCounter = getWaitingCounter(fetcherConfig);
    if (waitingCounter.incrementAndGet() > fetcherConfig.getMaxWaitingSegmentCount()) {
      waitingCounter.decrementAndGetAsync();
      return false;
    }
    return true;
  }

  /**
   * 减少待执行计数
   *
   * @param fetcherConfig 配置
   */
  private void decrementWaitingCount(FetcherConfig fetcherConfig) {
    final RAtomicLong waitingCounter = getWaitingCounter(fetcherConfig);
    if (waitingCounter.decrementAndGet() < 0) {
      waitingCounter.incrementAndGetAsync();
    }
  }

  /**
   * 获取数据类型对应的拉取器
   *
   * @param fetcherConfig 配置
   */
  private Fetcher getFetcher(FetcherConfig fetcherConfig) {
    final FetcherFactoryType fetcherFactoryType = fetcherConfig.getFetcherFactoryType();
    if (fetcherFactoryType != null) {
      return fetcherFactories.stream()
          .filter(f -> f.fetcherFactoryType() == fetcherFactoryType)
          .findFirst()
          .map(f -> f.getFetcher(fetcherConfig, fetcherConfig.getFactoryParameters()))
          .orElseThrow(() -> new FetchException("拉取数据工厂未找到实现类", fetcherConfig.getFetchDataType()));
    }
    return fetchers.stream()
        .filter(f -> f.fetchDataType() == fetcherConfig.getFetchDataType())
        .findFirst()
        .orElseThrow(() -> new FetchException("拉取数据类型未找到实现类", fetcherConfig.getFetchDataType()));
  }

  /**
   * 调度待执行的任务，投递到执行队列
   *
   * @param fetcherConfig 配置
   */
  private void dispatchFetch(FetcherConfig fetcherConfig) {
    if (isDisabled(fetcherConfig)) {
      return;
    }
    // 如果支持全量拉取的话，先做初始化
    if (fetcherConfig.isFullFetch()) {
      DataFetch fullFetchRecord = getFullFetchRecordOrCreate(fetcherConfig);
      if (fullFetchRecord == null) {
        return;
      }

      if (FetchStatus.WAIT.getValue().equals(fullFetchRecord.getStatus())) {
        fullFetch(fullFetchRecord, fetcherConfig);

        // 全量拉取完成再执行后面的分段拉取
        return;
      }

      // 如果数据是初始化成功的状态，但是开启了周期性初始化，检查是否时间已经到达下一周期
      if (FetchStatus.SUCCESS.getValue().equals(fullFetchRecord.getStatus())
          && fetcherConfig.getFullFetchPeriodic() > 0
          && DateUtil.currentTime() - fullFetchRecord.getUpdatedAt()
              >= fetcherConfig.getFullFetchPeriodic()) {

        // 如果记录已经锁定，说明已经在执行中，不需要再次触发
        if (fullFetchRecord.getLockTime() == null
            || LocalDateTime.now().isAfter(fullFetchRecord.getLockTime())) {
          fullFetch(fullFetchRecord, fetcherConfig);
        }

        // 全量拉取完成再执行后面的分段拉取
        return;
      }
    }

    // 如果当前数据类型不支持根据时间分段查询就直接退出
    if (!fetcherConfig.isSegmentFetch()) {
      return;
    }

    List<FetchSegment> fetchSegments = getFetchSegments(fetcherConfig);
    if (fetchSegments.isEmpty()) {
      getLogger(fetcherConfig)
          .info("实例({}):{}:当前所有区间已拉取完毕", fullId(), fetcherConfig.getFetchDataType().name());

      // 所有段拉取完毕后挂起一小段时间
      suspendFetch(fetcherConfig);
      return;
    }

    for (FetchSegment fetchSegment : fetchSegments) {
      if (getState() != State.RUNNING) {
        break;
      }

      final String permitId = acquireSemaphore(fetcherConfig);
      // 未返回 permitId 说明超过最大并发，需要暂时跳过其他待拉取的片段
      if (permitId == null) {
        return;
      }

      // 当前时间窗口超出最大流量限制
      if (!getRateLimiter(fetcherConfig).tryAcquire()) {
        return;
      }

      // 尝试对当前片段加锁
      final LocalDateTime lockTo = LocalDateTime.now().plusSeconds(fetcherConfig.getLockSeconds());
      if (dataFetchMapper.lock(fetchSegment.getId(), lockTo) <= 0) {
        getLogger(fetchSegment)
            .debug(
                "实例({}):{}[{},{}]:锁定失败，跳过",
                fullId(),
                fetchSegment.getDataType(),
                fetchSegment.getStartTime(),
                fetchSegment.getEndTime());
        continue;
      }
      fetchSegment.setLockTime(lockTo);

      // 投递任务到执行队列
      CompletableFuture.runAsync(() -> doFetchSegment(fetchSegment), taskExecutor)
          .thenRun(
              () -> {
                // 不管成功还是失败都释放并发信号量
                releaseSemaphore(fetcherConfig, permitId);

                // 拉取成功则减少等待拉取的数量
                if (fetchSegment.getStatus() == FetchStatus.SUCCESS) {
                  decrementWaitingCount(fetcherConfig);
                }
              });
      getLogger(fetcherConfig)
          .info(
              "实例({}):{}[{},{}]:已入队",
              fullId(),
              fetchSegment.getDataType().name(),
              fetchSegment.getStartTime(),
              fetchSegment.getEndTime());
    }
  }

  @Nullable
  private DataFetch getFullFetchRecordOrCreate(FetcherConfig fetcherConfig) {
    DataFetch fullFetchRecord =
        dataFetchMapper.getFullFetchRecord(
            fetcherConfig.getFetchDataType(), fetcherConfig.getRound());
    if (fullFetchRecord == null) {
      fullFetchRecord = newSegment(fetcherConfig, null);
    }
    return fullFetchRecord;
  }

  /** 获取数据类型对应的日志收集器 */
  private Logger getLogger(FetchSegment fetchSegment) {
    return logs.get(fetchSegment.getDataType());
  }

  /** 获取数据类型对应的日志收集器 */
  private Logger getLogger(FetchDataType fetchDataType) {
    return logs.get(fetchDataType);
  }

  /** 获取数据类型对应的日志收集器 */
  private Logger getLogger(FetcherConfig fetcherConfig) {
    return logs.get(fetcherConfig.getFetchDataType());
  }

  /**
   * 根据配置查询当前等待拉取的片段
   *
   * @param fetcherConfig 配置
   */
  private List<FetchSegment> getFetchSegments(FetcherConfig fetcherConfig) {
    LocalDateTime fetchPointMax = LocalDateTime.now();
    if (fetcherConfig.getFetchDelay() > 0) {
      fetchPointMax = fetchPointMax.minusSeconds(fetcherConfig.getFetchDelay());
    }
    return dataFetchMapper.getWaitFetchSegments(
        fetcherConfig.getFetchDataType(),
        true,
        fetcherConfig.getFetchSegmentLimitPerDispatch(),
        fetcherConfig.getRound(),
        fetchPointMax);
  }

  /**
   * 当前数据类型是否被挂起分段
   *
   * @param fetchDataType 数据类型
   */
  private boolean isSuspendSegment(FetchDataType fetchDataType) {
    final LocalDateTime dispatchDisabledTo = suspendSegmentMap.get(fetchDataType);
    return dispatchDisabledTo != null && LocalDateTime.now().isBefore(dispatchDisabledTo);
  }

  /**
   * 挂起当前数据类型的分段操作一段时间
   *
   * @param fetcherConfig 配置
   */
  private void suspendSegment(FetcherConfig fetcherConfig, int suspendSeconds) {
    suspendSegmentMap.put(
        fetcherConfig.getFetchDataType(), LocalDateTime.now().plusSeconds(suspendSeconds));
  }

  /**
   * 当前数据类型是否被挂起
   *
   * @param fetchDataType 数据类型
   */
  private boolean isSuspendFetch(FetchDataType fetchDataType) {
    final LocalDateTime dispatchDisabledTo = suspendFetchMap.get(fetchDataType);
    return dispatchDisabledTo != null && LocalDateTime.now().isBefore(dispatchDisabledTo);
  }

  /**
   * 挂起当前数据类型的调度一段时间
   *
   * @param fetcherConfig 配置
   */
  private void suspendFetch(FetcherConfig fetcherConfig) {
    suspendFetch(
        fetcherConfig,
        LocalDateTime.now().plusSeconds(fetcherConfig.getSuspendSecondsForAllFetched()));
  }

  /**
   * 挂起当前数据类型的调度一段时间
   *
   * @param fetcherConfig 配置
   * @param suspendTo 挂起到什么时间
   */
  private void suspendFetch(FetcherConfig fetcherConfig, LocalDateTime suspendTo) {
    suspendFetchMap.put(fetcherConfig.getFetchDataType(), suspendTo);
  }

  /** 根据TryLater异常挂起 */
  private void suspendByTryLaterException(FetcherConfig fetcherConfig, TryLaterException e) {
    Integer tryLaterSeconds = e.getTryLaterSeconds();
    if (tryLaterSeconds == null) {
      tryLaterSeconds = fetcherConfig.getTryLaterSeconds();
    }
    suspendFetch(fetcherConfig, LocalDateTime.now().plusSeconds(tryLaterSeconds));
  }

  /**
   * 本次调度是对新数据分段还是对老数据分段
   *
   * @param fetcherConfig 配置
   * @return 1:拉取新数据 0:当前无数据可拉取 -1:拉取老数据
   */
  private int segmentSelect(FetcherConfig fetcherConfig) {
    final FetchDataType fetchDataType = fetcherConfig.getFetchDataType();
    PriorityPolicy priorityPolicy = fetcherConfig.getPriorityPolicy();
    if (priorityPolicy == PriorityPolicy.ROUND) {
      final Boolean preferNewData =
          this.preferNewDataBiasMap.computeIfAbsent(fetcherConfig.getFetchDataType(), k -> true);
      this.preferNewDataBiasMap.put(fetchDataType, !preferNewData);
      priorityPolicy = preferNewData ? PriorityPolicy.NEW_FIRST : PriorityPolicy.OLD_FIRST;
    }
    switch (priorityPolicy) {
      case OLD_FIRST:
        return canSegmentDown(fetcherConfig) ? -1 : (canSegmentUp(fetcherConfig) ? 1 : 0);
      case NEW_FIRST:
        return canSegmentUp(fetcherConfig) ? 1 : (canSegmentDown(fetcherConfig) ? -1 : 0);
      default:
        throw new IllegalArgumentException("拉取优先级策略未实现:" + priorityPolicy);
    }
  }

  private boolean canSegmentUp(FetcherConfig fetcherConfig) {
    final LocalDateTime latestFetchPoint = getLatestFetchPoint(fetcherConfig);
    return latestFetchPoint == null
        || LocalDateTime.now().compareTo(latestFetchPoint.plus(fetcherConfig.getMinSegment())) >= 0;
  }

  private boolean canSegmentDown(FetcherConfig fetcherConfig) {
    final LocalDateTime earliestFetchPoint = getEarliestFetchPoint(fetcherConfig);
    return earliestFetchPoint == null
        || earliestFetchPoint.compareTo(fetcherConfig.getStartLimitDateTime()) > 0;
  }

  /**
   * 执行数据分段拉取
   *
   * @param fetchSegment 分段数据
   */
  private void doFetchSegment(FetchSegment fetchSegment) {
    if (getState() != State.RUNNING) {
      return;
    }
    final FetcherConfig fetcherConfig = getFetcherConfig(fetchSegment.getDataType());
    final RunContext runContext = getRunContext(fetchSegment);
    final RunData runData = runContext.getRunData();
    Throwable e = null;
    try {
      final Fetcher fetcher = getFetcher(getFetcherConfig(fetcherConfig.getFetchDataType()));
      if (fetcher instanceof PageFetcher) {
        initPageFetch(fetcherConfig, runContext, fetchSegment);

        int i = 0;
        while (fetcherConfig.isReverseFetch() && runData.getPageIndex() > 0
            || !fetcherConfig.isReverseFetch() && runData.getPageIndex() <= runData.getMaxPage()) {
          i++;
          if (isDisabled(fetcherConfig)) {
            throw new FetchInterruptedException(fetchSegment, "配置禁用导致中断");
          }
          if (i > 1) {
            // 当前时间窗口超出最大流量限制
            if (!getRateLimiter(fetcherConfig).tryAcquire()) {
              throw new TryLaterException(fetcherConfig.getFetchDataType());
            }
          }
          final LocalDateTime now = LocalDateTime.now();
          if (now.isAfter(fetchSegment.getLockTime().minusSeconds(3))) {
            fetchSegment.setLockTime(now.plusSeconds(fetcherConfig.getLockSeconds()));
            runContext.getLockTimeRenewCallback().accept(fetchSegment.getLockTime());
          }
          ((PageFetcher) fetcher)
              .fetch(
                  fetchSegment.getStartTime(),
                  fetchSegment.getEndTime(),
                  runData.getPageIndex(),
                  fetcherConfig.getPageSize(),
                  runContext);
          if (fetcherConfig.isReverseFetch()) {
            runData.subtractPageIndex();
          } else {
            runData.addPageIndex();
          }
          if (runData.getPageIndex() % fetcherConfig.getPersistPageInterval() == 0) {
            runData.persist();
          }
        }
      } else if (fetcher instanceof CustomFetcher) {
        ((CustomFetcher) fetcher)
            .fetch(fetchSegment.getStartTime(), fetchSegment.getEndTime(), runContext);
      }
      runData.setFetched(true);
    } catch (Throwable exception) {
      e = exception;
    } finally {
      final AtomicInteger errorCounter = getErrorCounter(fetcherConfig);
      if (e != null) {
        if (e instanceof FetchInterruptedException) {
          getLogger(fetcherConfig).warn("实例({})数据拉取中断:{}", fullId(), e.getMessage());
        } else {
          errorCounter.incrementAndGet();

          runData.addFailCount();
          fetchSegment.setErr(ExceptionUtil.stacktraceToString(e));

          if (runData.getFailCount() >= fetcherConfig.getSkipOnContinuousError()) {
            fetchSegment.setStatus(FetchStatus.ERROR);
          }

          if (runData.getFailCount() >= fetcherConfig.getAlertOnContinuousError()) {
            Alert.text(
                MessageRobotCode.GLOBAL,
                "数据类型:"
                    + fetcherConfig.getFetchDataType().getDesc()
                    + "拉取数据失败，异常信息:\n"
                    + fetchSegment.getErr(),
                true);
          }

          if (e instanceof TryLaterException) {
            suspendByTryLaterException(fetcherConfig, (TryLaterException) e);
          } else {
            suspendFetchByErrorCounter(fetcherConfig);
          }
        }
      } else {
        errorCounter.set(0);
        fetchSegment.setStatus(FetchStatus.SUCCESS);
      }
      persistFetchSegmentAndUnLock(fetchSegment);
    }
  }

  private boolean isDisabled(FetcherConfig fetcherConfig) {
    return fetcherConfig.isDisabled() || configs.isDisabled();
  }

  @NonNull
  private AtomicInteger getErrorCounter(FetcherConfig fetcherConfig) {
    return errorCounterMap.computeIfAbsent(
        fetcherConfig.getFetchDataType(), k -> new AtomicInteger());
  }

  /**
   * 获取当前片段的运行环境
   *
   * @param fetchSegment 段数据
   */
  private RunContext getRunContext(FetchSegment fetchSegment) {
    return new RunContext(getRunData(fetchSegment), getLockTimeRenewCallback(fetchSegment.getId()));
  }

  /**
   * 获取当前段的运行数据
   *
   * @param fetchSegment 数据段
   */
  private RunData getRunData(FetchSegment fetchSegment) {
    final RunData data = fetchSegment.getData();
    setPersistCallback(fetchSegment.getId(), data);
    return data;
  }

  /**
   * 设置持久化回调
   *
   * @param id 段ID
   * @param data 运行数据
   */
  private void setPersistCallback(Long id, RunData data) {
    data.setPersistCallback(rd -> dataFetchMapper.updateData(id, JsonUtil.toJson(rd)));
  }

  /**
   * 执行方法中可能需要根据实际情况延长锁定时间，设置回调委托当前类数据
   *
   * @param id 段ID
   */
  @NonNull
  private Consumer<LocalDateTime> getLockTimeRenewCallback(Long id) {
    return lockTime -> dataFetchMapper.updateLockTime(id, lockTime);
  }

  /** 持久化片段数据并解锁 */
  private void persistFetchSegmentAndUnLock(FetchSegment fetchSegment) {
    fetchSegment.getData().persist();
    dataFetchMapper.updateStateAndUnLock(fetchSegment);
  }
}
