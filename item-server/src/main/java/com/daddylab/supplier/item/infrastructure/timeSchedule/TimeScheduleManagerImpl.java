package com.daddylab.supplier.item.infrastructure.timeSchedule;

import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.TimeSchedule;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ITimeScheduleService;
import com.daddylab.supplier.item.infrastructure.time.CalcArgs;
import com.daddylab.supplier.item.infrastructure.time.TimeWindows;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2024/7/1
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TimeScheduleManagerImpl implements TimeScheduleManager {
    private final ITimeScheduleService timeScheduleService;

    @Override
    public void action(TimeScheduleConfig config, Consumer<TimeSchedule> action) {
        final String businessId = config.getBusinessId();
        final LocalDateTime currentWindowTime = getNextTime(config, 0);
        TimeSchedule timeSchedule = timeScheduleService.getTimeSchedule(config.getType(),
                businessId,
                currentWindowTime);

        if (timeSchedule != null) {
            if (timeSchedule.getIsComplete()) {
                return;
            }
        } else {
            timeSchedule = newRecord(config, currentWindowTime);
            try {
                timeScheduleService.save(timeSchedule);
            } catch (DuplicateKeyException ignored) {
                return;
            }
        }
        final LocalDateTime now = LocalDateTime.now();
        if (timeSchedule.getScheduleTime().isAfter(now)) {
            return;
        }
        final Duration tolerateOfOverdue = config.getTolerateOfOverdue();
        if (now.isAfter(timeSchedule.getScheduleTime().plus(tolerateOfOverdue))) {
            return;
        }
        try {
            timeSchedule.setActualTime(now);
            action.accept(timeSchedule);
        } catch (Exception e) {
            log.error("[TimeScheduleManager][{}]action error", config.getLogPrefix(), e);
            Alert.text(MessageRobotCode.NOTICE, String.format("[TimeScheduleManager][%s]err:%s", config.getLogPrefix(),
                    ExceptionUtil.getSimpleMessage(e)));
        } finally {
            final LocalDateTime completeTime = LocalDateTime.now();
            timeSchedule.setIsComplete(true);
            timeSchedule.setCompleteTime(completeTime);
            timeScheduleService.updateById(timeSchedule);
        }
    }

    @NonNull
    private static TimeSchedule newRecord(TimeScheduleConfig config, LocalDateTime currentWindowTime) {
        TimeSchedule timeSchedule;
        timeSchedule = new TimeSchedule();
        timeSchedule.setType(config.getType());
        timeSchedule.setBusinessId(config.getBusinessId());
        timeSchedule.setScheduleTime(currentWindowTime);
        return timeSchedule;
    }

    private LocalDateTime getNextTime(TimeScheduleConfig config, int windowOffset) {
        return TimeWindows.calculateWindowTime(CalcArgs.builder()
                                                       .syncFrequency(config.getSyncFrequency())
                                                       .windowOffset(windowOffset)
                                                       .startTimeOffset(config.getStartTimeOffset())
                                                       .build());
    }
}
