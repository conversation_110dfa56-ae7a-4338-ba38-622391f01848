package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerImageMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerImageService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 抽屉图片表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Service
public class ItemDrawerImageServiceImpl extends DaddyServiceImpl<ItemDrawerImageMapper, ItemDrawerImage> implements IItemDrawerImageService {
    @Autowired
    private ItemDrawerImageMapper itemDrawerImageMapper;

    @Override
    public List<ItemDrawerImage> selectSortedListByDrawerId(Long drawerId) {
        if (drawerId == null || drawerId <= 0) {
            return new ArrayList<>();
        }
//        QueryWrapper<ItemDrawerImage> queryWrapper = new QueryWrapper<>();
//        queryWrapper.lambda().eq(ItemDrawerImage::getDrawerId, drawerId).orderByAsc(ItemDrawerImage::getSort);
//        return itemDrawerImageMapper.selectList(queryWrapper);

        return itemDrawerImageMapper.selectSortedListByDrawerId(drawerId);
    }

    @Override
    public List<ItemDrawerImage> queryDrawerImages(Long itemDrawerId, Boolean isLiveVerbal) {
        LambdaQueryWrapper<ItemDrawerImage> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ItemDrawerImage::getDrawerId, itemDrawerId);
        if (isLiveVerbal) {
            queryWrapper.gt(ItemDrawerImage::getLiveVerbalTrickId, 0L);
        } else {
            queryWrapper.isNull(ItemDrawerImage::getLiveVerbalTrickId);
        }
        return list(queryWrapper);
    }
}
