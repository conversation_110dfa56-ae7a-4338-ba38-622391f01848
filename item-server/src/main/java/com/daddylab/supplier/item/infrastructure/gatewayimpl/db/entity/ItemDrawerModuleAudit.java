package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 商品库抽屉模块审核
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemDrawerModuleAudit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 商品ID
     */
    private Long itemId;

    /**
     * 当前是第几轮审核
     */
    private Integer round;

    /**
     * 审核状态 0:无需审核 5:待法务审核 10:待QC审核 100:审核完成
     */
    private ItemAuditStatus auditStatus;

    /**
     * 敏感词识别状态
     */
    private Boolean recognitionStatus;

    /**
     * 【基础信息】是否需要审核
     */
    private Boolean basicInfo;

    /**
     * 【图片信息】是否需要审核
     */
    private Boolean imgInfo;

    /**
     * 【文案&属性】是否需要审核
     */
    private Boolean textAndAttr;

    /**
     * 【直播话术】是否需要审核
     */
    private Boolean liveVerbalTrick;

    /**
     *【详情图片】是否需要审核
     */
    private Boolean details;

    /**
     * 审批类型 1 商品资料 2 直播话术
     */
    private ItemAuditType type;

    /**
     * 直播话术ID
     */
    private Long liveVerbalTrickId;

    /**
     * 提交人
     */
    private Long submitUid;

    /**
     * 提交时间
     */
    private Long submitAt;

    public Boolean hasModuleModify() {
        return Boolean.TRUE.equals(basicInfo) || Boolean.TRUE.equals(imgInfo)
                || Boolean.TRUE.equals(textAndAttr) || Boolean.TRUE.equals(liveVerbalTrick);
    }


}
