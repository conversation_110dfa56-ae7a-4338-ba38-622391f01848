package com.daddylab.supplier.item.infrastructure.cache;

import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import java.lang.reflect.Method;
import java.util.Optional;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/12/7
 */
@Aspect
@Slf4j
@Component
public class CacheEvictAspect {

    @Pointcut("@annotation(cacheEvict) && args(Object,..)")
    public void cachePointCut(CacheEvict cacheEvict) {
    }

    @Around(value = "cachePointCut(cacheEvict)", argNames = "pjp,cacheEvict")
    @SneakyThrows
    public Object around(ProceedingJoinPoint pjp, CacheEvict cacheEvict) {
        final Object proceed = pjp.proceed(pjp.getArgs());
        Method method = ((MethodSignature) pjp.getSignature()).getMethod();
        final Object[] args = pjp.getArgs();
        final Object arg = args[0];
        final String keyPart = cacheEvict.keySerialize().apply(arg);
        final String cacheKey = cacheEvict.keyPrefix() + keyPart;
        switch (cacheEvict.cacheType()) {
            case REDIS:
                RedisUtil.del(cacheKey);
            case CAFFEINE:
                Optional.ofNullable(CacheAspect.caffeineLoadingCaches.get(method))
                        .ifPresent(cache -> cache.invalidate(cacheKey));
            default:
        }
        return proceed;
    }
}
