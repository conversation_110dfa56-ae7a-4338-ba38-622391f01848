package com.daddylab.supplier.item.application.purchase;

import com.daddylab.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

/**
 * <AUTHOR>
 * @ClassName PurchaseRotateTask.java
 * @description
 * @createTime 2021年11月19日 15:48:00
 */
@Slf4j
@Component
public class PurchaseRotateTask {
    @Autowired
    private PurchaseBizService purchaseBizService;


//    @Scheduled(cron = "59 59 23 1/31 1/1 ?")
    @XxlJob("PurchaseRotateTask")
    public void doTask() {
        log.info("================采购处理上个月的纯活动商品定时任务开始=============");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        purchaseBizService.dealActiveItem();
        stopWatch.stop();
        log.info("================采购处理上个月的纯活动商品定时任务结束=============耗时 {} s", stopWatch.getTotalTimeSeconds());
    }
}
