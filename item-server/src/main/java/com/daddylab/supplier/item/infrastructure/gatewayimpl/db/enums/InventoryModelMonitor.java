package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> up
 * @date 2024年07月02日 3:37 PM
 */
@AllArgsConstructor
@Getter
public enum InventoryModelMonitor implements IIntegerEnum {

    /**
     * 虚拟仓状态改变
     */
    NO_CHANGE(0, "模式无变化"),
    LOCK_TO_SHARE(1, "独占模式转为共享模式"),
    SHARE_TO_LOCK(2, "共享模式转为独占模式");

    private final Integer value;
    private final String desc;

}
