package com.daddylab.supplier.item.application.contract;

import com.alibaba.cola.dto.MultiResponse;
import com.daddylab.supplier.item.domain.contract.ContractGateway;
import com.daddylab.supplier.item.types.contract.ContractDropdownItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/4/1
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ContractServiceImpl implements ContractService {
    private final ContractGateway contractGateway;

    @Override
    public MultiResponse<ContractDropdownItem> contractDropdownList(String contractNo, Long partnerProviderId, Integer status) {
        final List<ContractDropdownItem> contractDropdownItems = contractGateway.contractDropdownList(contractNo, partnerProviderId, status);
        return MultiResponse.of(contractDropdownItems);
    }
}
