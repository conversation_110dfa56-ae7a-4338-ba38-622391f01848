package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtPreStockInOrderDetails;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtPreStockInOrderDetailsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtPreStockInOrderDetailsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 旺店通预入库单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Service
public class WdtPreStockInOrderDetailsServiceImpl extends DaddyServiceImpl<WdtPreStockInOrderDetailsMapper, WdtPreStockInOrderDetails> implements
        IWdtPreStockInOrderDetailsService {

}
