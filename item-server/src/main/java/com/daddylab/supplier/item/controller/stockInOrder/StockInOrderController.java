package com.daddylab.supplier.item.controller.stockInOrder;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuVO;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.application.stockInOrder.StockInOrderBizService;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.stockInOrder.dto.*;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采购入库控制类
 *
 * <AUTHOR>
 * @date 2022/3/24 16:44
 **/
@Slf4j
@RestController
@RequestMapping("/stockInOrder")
@Api(value = "采购入库相关api", tags = "采购入库相关API")
public class StockInOrderController {
    @Autowired
    private StockInOrderBizService stockInOrderService;
    @Autowired
    private OperateLogBizService operateLogBizService;

    @ResponseBody
    @PostMapping(value = "/createStockInOrder")
    @ApiOperation("采购入库创建/更新")
    public Response createStockInOrder(@RequestBody StockInOrderAndOrderDetail stockInOrderAndOrderDetail) {
        return stockInOrderService.createStockInOrder(stockInOrderAndOrderDetail, false, false);
    }

    @ResponseBody
    @PostMapping(value = "/queryStockInOrderList")
    @ApiOperation("采购入库单列表")
    public PageResponse<StockInOrderShowDetail> queryStockInOrderList(@RequestBody StockInOrderQuery stockInOrderQuery) {
        return stockInOrderService.queryStockInOrderList(stockInOrderQuery);
    }

    @ResponseBody
    @GetMapping("/queryStockInOrderOrDetailByNo")
    @ApiOperation("采购入库单查看")
    public SingleResponse<StockInOrderAndOrderDetailVo> queryStockInOrderOrDetailByNo(@RequestParam String no) {
        return stockInOrderService.stockInOrderAndDetailAllInfoQueryByNo(no);
    }

    @ResponseBody
    @GetMapping("/queryStockInOrderListByPurchaseOrderId")
    @ApiOperation("采购单ID查询采购入库单列表")
    public SingleResponse<List<StockInOrder>> queryStockInOrderListByPurchaseOrderId(@RequestParam Long purchaseOrderId) {
        return stockInOrderService.stockInOrderList(purchaseOrderId);
    }

    @GetMapping(value = "/deleteStockInOrder")
    @ApiOperation("删除采购入库单")
    public Response deleteStockInOrder(@RequestParam Integer stockInOrderId) {
        return stockInOrderService.deleteStockInOrderService(stockInOrderId);
    }

    @GetMapping(value = "/cancelStockInOrder")
    @ApiOperation("取消采购入库单")
    public Response cancelStockInOrder(@RequestParam Integer stockInOrderId) {
        return stockInOrderService.cancelStockInOrderService(stockInOrderId);
    }

    @GetMapping(value = "/operateLogs")
    @ApiOperation("采购入库单操作日志")
    public MultiResponse<OperateLog> operateLogs(@RequestParam Long targetId) {
        return operateLogBizService.getOperateLogs(OperateLogTarget.STOCK_ORDER, targetId);
    }

    @PostMapping(value = "/exportStockInOrder")
    @ApiOperation("导出采购入库单")
    public SingleResponse<Boolean> exportStockInOrderTask(@RequestBody StockInOrderAndOrderDetailQuery stockInOrderAndOrderDetailQuery) {
        stockInOrderAndOrderDetailQuery.setBusinessLine(
                UserPermissionJudge.filterBusinessLinePerm(stockInOrderAndOrderDetailQuery.getBusinessLine()));
        stockInOrderService.exportExcel(stockInOrderAndOrderDetailQuery);
        return SingleResponse.of(true);
    }

    @ResponseBody
    @ApiOperation(value = "搜索查看sku明细列表")
    @PostMapping("/skuPage")
    public PageResponse<ComposeSkuVO> skuPage(@RequestBody ComposeSkuPageQuery query) {
        return stockInOrderService.pagePurchaseOrderSku(query);
    }

//    /**
//     * 当将入库单同步到kingDee时，经过多次测试，发现有时候因为网络问题，会响应超时（这种情况出现概率小，并且非常不稳定）
//     * feign内置hystrix会触发熔断报错（但是实际入库单已经推送到KingDee了），调整了响应超时时间参数，但是依旧偶尔会出现。
//     * 针对这种情况，添加了补偿接口。
//     * 此接口补偿逻辑，将库中此no对应的数据恢复。因为当入库单没有同步KingDee响应失败，会逻辑删除此入库单。
//     *
//     * @param query
//     * @return
//     */
//    @ResponseBody
//    @ApiOperation(value = "补偿入库单接口")
//    @GetMapping("/makeUp")
//    @Auth(noAuth = true)
//    public SingleResponse<Boolean> makeUp(@RequestParam(value = "no") String no) {
//        return stockInOrderService.makeUp(no);
//    }


}
