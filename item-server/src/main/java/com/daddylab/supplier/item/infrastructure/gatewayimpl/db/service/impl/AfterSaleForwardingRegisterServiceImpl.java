package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSaleForwardingRegister;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AfterSaleForwardingRegisterMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSaleForwardingRegisterService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 售后转寄登记 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@Service
public class AfterSaleForwardingRegisterServiceImpl extends DaddyServiceImpl<AfterSaleForwardingRegisterMapper, AfterSaleForwardingRegister> implements IAfterSaleForwardingRegisterService {

}
