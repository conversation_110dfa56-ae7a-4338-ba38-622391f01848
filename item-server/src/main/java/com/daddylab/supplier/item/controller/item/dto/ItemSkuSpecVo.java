package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName ItemSkuVo.java
 * @description
 * @createTime 2022年06月08日 10:34:00
 */
@Data
@ApiModel("sku规格封装")
public class ItemSkuSpecVo implements Serializable {
    private static final long serialVersionUID = -1030515343189102461L;

    public ItemSkuSpecVo(){
    }

    @ApiModelProperty("规格")
    private String specifications;

}
