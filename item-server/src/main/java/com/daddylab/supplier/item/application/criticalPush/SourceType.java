package com.daddylab.supplier.item.application.criticalPush;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/4/15
 */
@AllArgsConstructor
@Getter
public enum SourceType implements IEnum<Integer> {

    PURCHASE_STOCK_IN_ORDER(1, "采购入库单"),
    PURCHASE_RETURN_STOCK_OUT_ORDER(2, "退料出库单"),
    CATEOGRY(3,"品类（物料分组）"),
    PROVIDER(4,"供应商"),
    ITEM(5,"后端商品"),
    ;

    private final Integer value;
    private final String desc;

}
