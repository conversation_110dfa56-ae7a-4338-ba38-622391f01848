package com.daddylab.supplier.item.domain.drawer.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * Class  ItemDrawerForm
 *
 * @Date 2022/5/31下午4:18
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ItemDrawerImageForm", description = "商品抽屉详情图片表单")
public class ItemDrawerImageForm implements Serializable {

    @ApiModelProperty(value = "抽屉id")
    private Long id;

    @Length(max = 500, message = "图片地址不能大于500个字符")
    @NotEmpty(message = "图片地址不能为空")
    @ApiModelProperty(value = "图片或者视频地址")
    private String url;

    @NotNull(message = "文件类型不能为空")
    @ApiModelProperty(value = "文件类型 1-图片 2-视频")
    private Integer fileType;

    @ApiModelProperty(value = "视频首图")
    private String firstImageUrl;
}
