package com.daddylab.supplier.item.application.tmpJob;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.item.gateway.BuyerGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Buyer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemProcurement;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemProcurementService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IProviderService;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.util.concurrent.RateLimiter;
import java.io.InputStream;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.Semaphore;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/3/11
 */
@Service
@Slf4j
public class ProviderHandoverTask {

  @Autowired private IProviderService providerService;

  @Autowired private IItemService itemService;

  @Autowired private ItemBizService itemBizService;

  @Autowired private OperateLogDomainService operateLogDomainService;

  @Autowired private StaffService staffService;

  @Autowired private IItemProcurementService itemProcurementService;

  @Autowired private KingDeeTemplate kingDeeTemplate;

  @Autowired private BuyerGateway buyerGateway;

  @Data
  public static class ExcelVO {

    @ExcelProperty(value = "供应商")
    private String providerName;

    @ExcelProperty(value = "新采购员")
    private String newBuyer;

    @ExcelProperty(value = "是否交接商品")
    private String handoverItem;

    @ExcelProperty(value = "是否同步P系统")
    private String syncPsys;

    @ExcelProperty(value = "是否同步金蝶")
    private String syncKingdee;
  }

  public void doTask(InputStream inputStream) {
    log.info("[供应商交接]开始执行");
    final List<ExcelVO> inputs = EasyExcel.read(inputStream).head(ExcelVO.class).doReadAllSync();
    if (CollUtil.isEmpty(inputs)) {
      return;
    }
    final LoadingCache<Long, StaffBrief> staffCache =
        Caffeine.newBuilder().build(id -> staffService.getStaffBrief(id).orElse(null));
    int index = 0;
    for (ExcelVO input : inputs) {
      try {
        index++;
        final List<Provider> providers =
            providerService.lambdaQuery().eq(Provider::getName, input.getProviderName()).list();
        if (providers.isEmpty()) {
          throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未找到供应商");
        }
        final String newBuyer = input.getNewBuyer();
        final StaffBrief newBuyerUser =
            staffService
                .getStaffByNickName(newBuyer)
                .orElseThrow(
                    () -> ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未找到采购员"));
        final Long newBuyerId =
            buyerGateway.saveOrUpdateBuyer(newBuyerUser.getUserId(), newBuyerUser.getNickname());
        for (Provider provider : providers) {
          final Long mainChargerUserId = provider.getMainChargerUserId();
          final Long secondChargerUserId = provider.getSecondChargerUserId();
          final Optional<StaffBrief> mainCharger =
              Optional.ofNullable(staffCache.get(mainChargerUserId));
          final Optional<StaffBrief> secondCharger =
              Optional.ofNullable(staffCache.get(secondChargerUserId));
          provider.setMainChargerUserId(newBuyerUser.getUserId());
          provider.setSecondChargerUserId(newBuyerUser.getUserId());
          providerService.updateById(provider);
          final String msg =
              String.format(
                  "[供应商交接]负责人从'%s'更换为'%s'，次要负责人从'%s'更换为'%s'",
                  mainCharger.map(StaffBrief::getNickname).orElse("?"),
                  newBuyerUser.getNickname(),
                  secondCharger.map(StaffBrief::getNickname).orElse("?"),
                  newBuyerUser.getNickname());
          log.info("[供应商交接]供应商'{}'{}", provider.getId(), msg);
          operateLogDomainService.addOperatorLog(
              0L,
              OperateLogTarget.PROVIDER,
              provider.getId(),
              msg,
              new Object[] {
                new DiffUtil.ChangePropertyObj("mainChargerUserId", mainChargerUserId, newBuyerId),
                new DiffUtil.ChangePropertyObj("secondChargerUserId", mainChargerUserId, newBuyerId)
              });

          if (!"是".equals(input.getHandoverItem())) {
            continue;
          }

          final List<Item> itemList =
              itemService.lambdaQuery().eq(Item::getProviderId, provider.getId()).list();
          if (itemList.isEmpty()) {
            log.info("[供应商交接]供应商'{}'没有商品需要交接", provider.getId());
            continue;
          }
          final RateLimiter rateLimiter = RateLimiter.create(3);
          for (Item item : itemList) {
            final Long itemId = item.getId();
            final ItemProcurement itemProcurement = itemProcurementService.getByItemId(itemId);
            if (itemProcurement == null) {
              continue;
            }
            final Long buyerId0 = itemProcurement.getBuyerId();
            final Optional<StaffBrief> itemBuyerUser0 =
                buyerGateway.buyer(buyerId0).map(Buyer::getUserId).map(staffCache::get);
            itemProcurement.setBuyerId(newBuyerId);
            itemProcurementService.updateById(itemProcurement);
            operateLogDomainService.addOperatorLog(
                0L,
                OperateLogTarget.ITEM,
                itemId,
                String.format(
                    "[供应商交接]采购员从 %s 修改为 %s",
                    itemBuyerUser0.map(StaffBrief::getNickname).orElse("?"),
                    newBuyerUser.getNickname()),
                new Object[] {new DiffUtil.ChangePropertyObj("buyerId", buyerId0, newBuyerId)});

            if ("是".equals(input.getSyncPsys())) {
              try {
                itemBizService.notifyBuyerOrQcChange(itemId);
                log.info("[供应商交接]商品 {} 采购员&QC修改同步到P系统成功", itemId);
              } catch (Exception e) {
                log.error("[供应商交接]商品 {} 采购员&QC修改同步到P系统失败", itemId, e);
              }
            }

            if ("是".equals(input.getSyncKingdee())) {
              rateLimiter.acquire();
              try {
                kingDeeTemplate.syncItem(itemId);
                log.info("[供应商交接]商品 {} 采购员&QC修改同步到金蝶成功", itemId);
              } catch (Exception e) {
                log.error("[供应商交接]商品 {} 推送金蝶失败", itemId, e);
              }
            }
            log.info("[供应商交接]商品 {} 交接完成", itemId);
          }
          log.info("[供应商交接]供应商'{}'交接完成", provider.getId());
        }
      } catch (BizException e) {
        log.warn("[供应商交接]第{}行处理:{}:{}", index, input, e.getMessage());
      }
    }
  }

  public void taskOffset(InputStream inputStream) {
    final List<ExcelVO> inputs = EasyExcel.read(inputStream).head(ExcelVO.class).doReadAllSync();
    if (CollUtil.isEmpty(inputs)) {
      return;
    }
    int index = 0;
    for (ExcelVO input : inputs) {
      try {
        index++;
        final List<Provider> providers =
            providerService.lambdaQuery().eq(Provider::getName, input.getProviderName()).list();
        if (providers.isEmpty()) {
          throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未找到供应商");
        }
        for (Provider provider : providers) {
          final List<Item> itemList =
              itemService.lambdaQuery().eq(Item::getProviderId, provider.getId()).list();

          if ("是".equals(input.getSyncPsys())) {
            for (Item item : itemList) {
              final Long itemId = item.getId();
              try {
                itemBizService.notifyBuyerOrQcChange(itemId);
                log.info("[供应商交接补偿]商品 {} 采购员&QC修改同步到P系统成功", itemId);
              } catch (Exception e) {
                log.error("[供应商交接补偿]商品 {} 采购员&QC修改同步到P系统失败", itemId, e);
              }
            }
          }
          if ("是".equals(input.getSyncKingdee())) {
            final RateLimiter rateLimiter = RateLimiter.create(10);
            for (int i = 0; i < itemList.size(); i++) {
              final Item item = itemList.get(i);
              final Long itemId = item.getId();
              rateLimiter.acquire();
              try {
                kingDeeTemplate.syncItem(itemId);
                log.info("[供应商交接补偿]商品 {} 采购员&QC修改同步到金蝶成功", itemId);
              } catch (RejectedExecutionException e) {
                ThreadUtil.sleep(15000);
                log.warn("[供应商交接补偿]金蝶推送队列已满，等待");
                i--;
              } catch (Exception e) {
                log.error("[供应商交接补偿]商品 {} 推送金蝶失败", itemId, e);
              }
            }
          }
        }
      } catch (BizException e) {
        log.warn("[供应商交接补偿]第{}行处理:{}:{}", index, input, e.getMessage());
      }
    }
  }
}
