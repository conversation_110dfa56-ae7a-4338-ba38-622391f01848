package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 抖店订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class S03Order implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 买家识别号，目前取收货人手机号
     */
    private String buyerId;

    /**
     * 有赞:订单号/淘宝:订单编号/抖店:主订单编号/快手:订单号
     */
    private String orderNo;

    /**
     * 有赞:订单类型/抖店:订单类型/快手:分销类型/享测小程序:订单类型
     */
    private String orderType;

    /**
     * 有赞:订单状态/淘宝:订单状态/抖店:订单状态/快手:订单状态/享测小程序:订单状态
     */
    private String orderStatus;

    /**
     * 有赞:订单来源/抖店:渠道/享测小程序:订单来源
     */
    private String sourceType;

    /**
     * 有赞:订单创建时间/淘宝:订单创建时间/抖店:订单创建时间/快手:订单创建时间/享测小程序:下单时间
     */
    private String createTime;

    /**
     * 有赞:买家付款时间/淘宝:订单付款时间/抖店:订单支付时间/快手:付款时间
     */
    private String payTime;

    /**
     * 有赞:交易成功时间/淘宝:确认收货时间/抖店:收货时间/快手:确认收货时间
     */
    private String receiverTime;

    /**
     * 淘宝:支付宝/有赞:付款方式/抖店:支付类型/快手:支付方式
     */
    private String payType;

    /**
     * 有赞:运费/淘宝:买家应付邮费/抖店:邮费/快手:运费
     */
    private BigDecimal fee;

    /**
     * 有赞:订单实付金额/淘宝:买家实际支付金额/抖店:实付款(订单实付金额+运费)/快手:实付款（货款不含运费）
     */
    private BigDecimal payAmount;

    /**
     * 有赞:商品种类数/抖店:子订单数量/快手:sku商品种类数
     */
    private String itemTypesNum;

    /**
     * 有赞:收货人/淘宝:收货人姓名/抖店:收件人/快手:收件人/享测小程序:姓名
     */
    private String receiverName;

    /**
     * 有赞:收货人手机号/淘宝:联系手机/抖店:收件人手机号/快手:收件人电话/享测小程序:联系电话
     */
    private String receiverPhone;

    /**
     * 有赞:收货人省份/抖店:省/快手:省
     */
    private String receiverProvince;

    /**
     * 有赞:收货人城市/抖店:市/快手:市
     */
    private String receiverCity;

    /**
     * 有赞:收货人地区/抖店:区/快手:区
     */
    private String receiverRegion;

    /**
     * 有赞:详细收货地址/淘宝:收货地址/抖店:收件地址/快手:收件地址/享测小程序:详细地址
     */
    private String receiverAddr;

    /**
     * 有赞:买家备注/抖店:买家备注/快手:买家留言
     */
    private String buyerRemark;

    /**
     * 有赞:商家订单备注/淘宝:订单备注/抖店:卖家备注/快手:卖家备注
     */
    private String sellerRemark;

    /**
     * 淘宝:物流单号/抖店:快递单号/快手:快递单号
     */
    private String logisticsNo;

    /**
     * 淘宝:物流公司//抖店:快递公司/快手:快递公司
     */
    private String logisticsCompany;

    /**
     * 淘宝:店铺Id/抖店:所属门店id
     */
    private String shopId;

    /**
     * 淘宝:订单关闭原因/抖店:订单取消原因
     */
    private String closeReason;

    private LocalDateTime importTime;

    /**
     * 有赞:用户ID
     */
    private String yzOpenId;

    /**
     * 抖店:平台优惠券金额
     */
    private BigDecimal couponAmount;

    /**
     * 抖店:商家优惠券金额
     */
    private BigDecimal shopCouponAmount;

    /**
     * 淘宝:发货时间/快手:发货时间/抖店:发货时间
     */
    private LocalDateTime deliveryTime;

    private String couponName;

    /**
     * 加密收货人姓名
     */
    private String encryptName;

    /**
     * 加密收货电话
     */
    private String encryptMobile;

    /**
     * 加密收货地址
     */
    private String encryptAddr;

    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;

    /**
     * 是否需要发货   0：不需要  1：需要
     */
    private Integer isDelivery;


}
