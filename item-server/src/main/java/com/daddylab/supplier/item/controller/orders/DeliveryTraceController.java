package com.daddylab.supplier.item.controller.orders;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.order.deliveryTrace.DeliveryTraceBizService;
import com.daddylab.supplier.item.types.order.DeliveryTracePageQuery;
import com.daddylab.supplier.item.types.order.OrderDeliveryTracePageVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/8/21
 */
@Slf4j
@RestController
@RequestMapping("/deliveryTrace")
@Api(value = "订单发货跟踪API", tags = "订单发货跟踪API")
public class DeliveryTraceController {

    @Resource
    DeliveryTraceBizService deliveryTraceBizService;

    @ResponseBody
    @ApiOperation(value = "订单发货跟踪分页查询")
    @PostMapping("/pageQuery")
    public PageResponse<OrderDeliveryTracePageVO> pageQuery(
            @RequestBody @Validated DeliveryTracePageQuery query) {
        return deliveryTraceBizService.pageQuery(query);
    }

    @ResponseBody
    @ApiOperation(value = "订单发货跟踪导出")
    @PostMapping("/export")
    public SingleResponse<Boolean> export(
            @RequestBody @Validated DeliveryTracePageQuery query) {
        return deliveryTraceBizService.export(query);
    }


}
