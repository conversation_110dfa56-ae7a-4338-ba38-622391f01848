package com.daddylab.supplier.item.infrastructure.limit;

import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * <AUTHOR> up
 * @date 2022/5/12 10:32 上午
 */
@Component
@Aspect
@Slf4j
public class RateLimitAspect {
    @Autowired
    private RedissonClient redissonClient;


    @Pointcut("@annotation(com.daddylab.supplier.item.infrastructure.limit.RateLimit)")
    public void serviceLimit() {

    }

    @Around("serviceLimit()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes sra = (ServletRequestAttributes) ra;
        if (sra == null) {
            throw new IllegalStateException("ServletRequestAttributes is null");
        }

        HttpServletRequest request = sra.getRequest();
        String ipAddress = getIpAddress(request);
        RateLimit rateLimit = getRateLimitAspect(joinPoint);

        long permitsPerSecond = rateLimit.count();
        long rateInterval = rateLimit.time();
        RRateLimiter rRateLimiter = redissonClient.getRateLimiter(rateLimit.key() + "_" + ipAddress);
        rRateLimiter.trySetRate(RateType.OVERALL, permitsPerSecond, rateInterval, RateIntervalUnit.SECONDS);

        boolean flag = rRateLimiter.tryAcquire();
        Object obj;

        log.info("Rate limit exceeded for method: {}, IP: {}", rateLimit.methodDesc(), ipAddress);

        try {
            if (flag) {
                obj = joinPoint.proceed();
            } else {
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, rateLimit.methodDesc() + "请求超频");
            }
        } catch (Exception e) {
            log.error("{},请求异常", rateLimit.methodDesc(), e);
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, rateLimit.methodDesc() + "，操作失败。" + e.getMessage());
        }
        return obj;
    }

    /**
     * @param rateLimit
     * @return
     */
//    private RRateLimiter getRateLimiter(RateLimit rateLimit, String ipAddress) {
//        long count = rateLimit.count();
//        long time = rateLimit.time();
//
//        RRateLimiter rRateLimiter = redissonClient.getRateLimiter(rateLimit.key() + "_" + ipAddress);
//        rRateLimiter.trySetRate(RateType.OVERALL, count, time, RateIntervalUnit.SECONDS);
//
//
//
//        RRateLimiter rRateLimiter = redissonClient.getRateLimiter(rateLimit.key() + "_" + ipAddress);
//        if (rRateLimiter.isExists()) {
//            RateLimiterConfig rateLimiterConfig = rRateLimiter.getConfig();
//            long rateInterval = rateLimiterConfig.getRateInterval();
//            long rate = rateLimiterConfig.getRate();
//            if (time != rateInterval || rate != count) {
//                rRateLimiter.delete();
//                rRateLimiter.trySetRate(RateType.OVERALL, count, time, RateIntervalUnit.SECONDS);
//            }
//        } else {
//            rRateLimiter.trySetRate(RateType.OVERALL, count, time, RateIntervalUnit.SECONDS);
//        }
//        return rRateLimiter;
//    }

    /**
     * 获取脚注配置信息
     *
     * @param joinPoint
     * @return
     */
    private RateLimit getRateLimitAspect(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        return method.getAnnotation(RateLimit.class);
    }


    public static String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
            ip = ip.split(",")[0].trim(); // 取第一个IP
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

}
