package com.daddylab.supplier.item.application.purchasePayable.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年03月01日 2:14 PM
 */
@Data
@ApiModel("付款申请单编辑请求参数")
public class EditApplyPayOrderCmd extends Command {

    @ApiModelProperty("订单id")
    private Long applyOrderId;

    @ApiModelProperty("订单整体修正总金额")
    private BigDecimal orderFixedTotalAmount;

    @ApiModelProperty("sku列表修改明细")
    private List<EditApplyPayOrderDetailCmd> detailCmdList;

    @ApiModelProperty("附件id")
    private Long additionalId;
}
