package com.daddylab.supplier.item.application.saleItem.dto;

import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022年08月05日 2:20 PM
 */
@Data
@ApiModel("更新spu请求参数封装")
public class NewGoodsSpuSaveCmd {

    private Long newGoodsDbId;

    @ApiModelProperty(value = "产品标准名")
    private String standardName;

    private Long itemId;

    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    @ApiModelProperty(value = "采购品名/商品名称")
    private String name;

    @ApiModelProperty(value = "商品状态 :1待选择 2待完善 3待设计 4待审核 5待修改 6待上架 7已上架")
    private Integer status;

    private Long categoryId;

    @ApiModelProperty(value = "品类")
    private String category;

    private Long brandId;

    @ApiModelProperty(value = "品牌")
    private String brand;

    /**
     * 采购负责人id
     */
    private Long buyerId;

    /**
     * 产品负责人id
     */
    private Long principalId;

    /**
     * QC负责人ids
     */
    private String qcIds;

    @ApiModelProperty(value = "采购负责人信息")
    private DadStaffVO buyer;

    @ApiModelProperty(value = "产品负责人信息")
    private DadStaffVO principal;

    @ApiModelProperty(value = "产品负责人信息")
    private List<DadStaffVO> qcs = Collections.emptyList();

    @ApiModelProperty(value = "首页文案")
    private String homePageText;

    @ApiModelProperty(value = "预计上架日期")
    private Long shelfTime;

    @ApiModelProperty(value = "发货类型 0:仓库发货 1:工厂发货")
    private Integer shipmentType;

    @ApiModelProperty(value = "发货地")
    private String shipmentArea;

    @ApiModelProperty(value = "48小时发货 0:是 1:否")
    private Integer shipmentAging;

    @ApiModelProperty(value = "物流")
    private String logistics;

    @ApiModelProperty(value = "快递模板")
    private String expressTemplate;

    @ApiModelProperty(value = "是否支持7天无理由退换 0:是 1:否")
    private Integer noReason;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "上新计划ID")
    private Long planId;

    @ApiModelProperty(value = "上新计划名称")
    private String planName;

    @ApiModelProperty(value = "上新时间")
    private String launchDate;



}
