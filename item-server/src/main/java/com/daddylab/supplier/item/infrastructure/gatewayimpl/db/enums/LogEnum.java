package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *     int KINGDEE = 1;
 *     int PARTNER_SERVER = 2;
 *     int USER_SERVER = 3;
 *     int ACCESS_CONTROL = 4;
 *     int KINGDEE_MAKEUP = 5;
 * <AUTHOR> up
 * @date 2022/6/22 11:42 上午
 */
@Getter
@AllArgsConstructor
public enum LogEnum implements IEnum {

    /**
     * 消除黄线
     */
    KING_DEE_COMMON(1, "金蝶系统"),
    PARTNER_SERVER(2, "P系统"),
    USER_SERVER(3, "用户服务"),
    ACCESS_CONTROL(4, "权限服务"),
    AWS_SERVER(5,"炎黄盈动回调通知"),
    PARTNER_SERVER_REVIEW_THESAURUS_ADD_HIT_NUM(6,"审核词库增加命中次数"),

    ;

    @EnumValue
    private Integer value;
    private String desc;

}
