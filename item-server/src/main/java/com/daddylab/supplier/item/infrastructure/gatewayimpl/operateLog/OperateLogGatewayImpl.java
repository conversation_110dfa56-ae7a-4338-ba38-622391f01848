package com.daddylab.supplier.item.infrastructure.gatewayimpl.operateLog;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.infrastructure.common.LoginType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOperateLogService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.operateLog.TypedTargetId;
import org.springframework.stereotype.Component;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Component
public class OperateLogGatewayImpl implements OperateLogGateway {
    @Inject
    IOperateLogService operateLogService;

    @Override
    public boolean batchAddOperateLog(List<OperateLog> operateLogs) {
        return operateLogService.saveBatch(operateLogs);
    }

    @Override
    public void addOperatorLog(LoginType loginType, Long operatorId, OperateLogTarget target, Object targetId, String msg, Object data) {
        final OperateLog entity = new OperateLog();
        entity.setTargetType(target);
        entity.setTargetId(targetId.toString());
        entity.setMsg(msg);
        entity.setData(data instanceof String ? (String) data : JsonUtil.objToStr(data));
        entity.setOperatorId(operatorId);
        entity.setCreatedAt(DateUtil.currentTime());
        entity.setOperatorType(loginType.ordinal());
        operateLogService.save(entity);
    }

    @Override
    public void addOperatorLog(Long operatorId, OperateLogTarget target, Object targetId, String msg, Object data) {
        final OperateLog entity = new OperateLog();
        entity.setTargetType(target);
        entity.setTargetId(targetId.toString());
        entity.setMsg(msg);
        entity.setData(data instanceof String ? (String) data : JsonUtil.objToStr(data));
        entity.setOperatorId(operatorId);
        entity.setCreatedAt(DateUtil.currentTime());
        operateLogService.save(entity);
    }

    @Override
    public void addOperatorLog(Long operatorId,
                               OperateLogTarget target,
                               Object targetId,
                               List<String> msgs,
                               Object data) {
        final Long currentTime = DateUtil.currentTime();
        final ArrayList<OperateLog> operateLogs = new ArrayList<>();
        for (int i = 0; i < msgs.size(); i++) {
            final String msg = msgs.get(i);
            final OperateLog entity = new OperateLog();
            entity.setTargetType(target);
            entity.setTargetId(targetId.toString());
            entity.setMsg(msg);
            if (data != null) {
                if (data instanceof List) {
                    final List<?> listData = (List<?>) data;
                    if (i <= listData.size() - 1) {
                        entity.setData(listData.get(i) instanceof String ? (String) listData.get(i)
                                               : JsonUtil.toJson(listData.get(i)));
                    }
                } else {
                    entity.setData(data instanceof String ? (String) data : JsonUtil.toJson(data));
                }
            }
            entity.setOperatorId(operatorId);
            entity.setCreatedAt(currentTime);
            operateLogs.add(entity);
        }
        operateLogService.saveBatch(operateLogs);
    }

    @Override
    public void addOperatorLog(Long operatorId,
                               OperateLogTarget target,
                               List<Object> targetIds,
                               String msg,
                               Object data) {
        final String dataStr = data instanceof String ? (String) data : JsonUtil.objToStr(data);
        final Long currentTime = DateUtil.currentTime();
        final ArrayList<OperateLog> operateLogs = new ArrayList<>();
        for (Object targetId : targetIds) {

            final OperateLog entity = new OperateLog();
            entity.setTargetType(target);
            entity.setTargetId(targetId.toString());
            entity.setMsg(msg);
            entity.setData(dataStr);
            entity.setOperatorId(operatorId);
            entity.setCreatedAt(currentTime);
            operateLogs.add(entity);
        }
        operateLogService.saveBatch(operateLogs);
    }

    @Override
    public List<OperateLog> getOperateLogs(OperateLogTarget target, Object targetId) {
        if (targetId instanceof Collection) {
            if (((Collection<?>) targetId).isEmpty()) {
                return Collections.emptyList();
            }
            return operateLogService.lambdaQuery().in(OperateLog::getTargetId, (Collection<?>) targetId)
                                    .eq(OperateLog::getTargetType, target)
                                    .orderByDesc(OperateLog::getId)
                                    .list();
        }
        return operateLogService.lambdaQuery().eq(OperateLog::getTargetId, targetId.toString())
                                .eq(OperateLog::getTargetType, target)
                                .orderByDesc(OperateLog::getId)
                                .list();
    }

    @Override
    public List<OperateLog> getOperateLogs(List<OperateLogTarget> target, Object targetId) {
        return operateLogService.lambdaQuery().eq(OperateLog::getTargetId, targetId.toString())
                                .in(OperateLog::getTargetType, target)
                                .orderByDesc(OperateLog::getId)
                                .list();
    }

    @Override
    public List<OperateLog> getOperateLogs(OperateLogTarget target, Collection<Object> targetIds) {
        return operateLogService.lambdaQuery().in(OperateLog::getTargetId, targetIds)
                                .eq(OperateLog::getTargetType, target)
                                .orderByDesc(OperateLog::getId)
                                .list();
    }

    @Override
    public List<OperateLog> getMultiTargetOperateLogs(List<TypedTargetId> targetIds) {
        final LambdaQueryChainWrapper<OperateLog> operateLogLambdaQueryChainWrapper = operateLogService.lambdaQuery();
        for (TypedTargetId targetId : targetIds) {
            operateLogLambdaQueryChainWrapper.or(
                    q -> q.eq(OperateLog::getTargetType, targetId.getTarget())
                          .eq(OperateLog::getTargetId, targetId.getTargetId().toString()));
        }
        return operateLogLambdaQueryChainWrapper.orderByDesc(OperateLog::getId)
                                                .list();
    }


}
