package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsAbnormalityAlert;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderLogisticsAbnormalityAlertMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsAbnormalityAlertService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 订单物流异常记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Service
public class OrderLogisticsAbnormalityAlertServiceImpl extends DaddyServiceImpl<OrderLogisticsAbnormalityAlertMapper, OrderLogisticsAbnormalityAlert> implements IOrderLogisticsAbnormalityAlertService {

}
