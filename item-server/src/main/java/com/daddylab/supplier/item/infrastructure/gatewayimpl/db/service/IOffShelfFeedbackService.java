package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OffShelfFeedback;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 下架管理-下架反馈 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface IOffShelfFeedbackService extends IDaddyService<OffShelfFeedback> {

}
