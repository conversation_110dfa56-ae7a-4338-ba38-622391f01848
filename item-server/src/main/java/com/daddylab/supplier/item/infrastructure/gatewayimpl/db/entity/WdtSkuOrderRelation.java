//package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;
//
//import com.baomidou.mybatisplus.annotation.*;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.io.Serializable;
//
///**
// * <p>
// *
// * </p>
// *
// * <AUTHOR>
// * @since 2022-04-15
// */
//@Data
//@EqualsAndHashCode(callSuper = false)
//public class WdtSkuOrderRelation implements Serializable {
//
//    private static final long serialVersionUID = 1L;
//
//    @TableId(value = "id", type = IdType.AUTO)
//    private Long id;
//
//    @TableLogic
//    private Integer isDel;
//
//    @TableField(fill = FieldFill.INSERT)
//    private Long createdAt;
//
//    @TableField(fill = FieldFill.INSERT)
//    private Long createdUid;
//
//    @TableField(fill = FieldFill.UPDATE)
//    private Long updatedAt;
//
//    @TableField(fill = FieldFill.UPDATE)
//    private Long updatedUid;
//
//    private String skuCode;
//
//    /**
//     * wdt_order_detail  trade_id
//     */
//    private Long tradeId;
//
//    /**
//     * 采购单id
//     */
//    private Long purchaseId;
//
//}
