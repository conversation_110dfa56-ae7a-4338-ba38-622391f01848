package com.daddylab.supplier.item.domain.drawer.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;

import lombok.Getter;

/**
 * Class  ItemDrawerImageTypeEnum
 *
 * @Date 2022/6/1上午11:32
 * <AUTHOR>
 */
@Getter
public enum ItemDrawerImageTypeEnum implements IEnum<Integer> {

    /**
     * 图片类型 1-抽屉商品 2-抽屉详情页 3-规格图片 4-主图视频 5-属性图片 6-商品图片（其他尺寸）7-直播图片
     */
    ITEM(1, "抽屉商品"),
    DETAIL(2, "抽屉详情页"),
    SKU(3, "规格图片"),
    MAIN_IMAGE_VIDEO(4, "主图视频"),
    ATTR(5, "属性图片"),
    OTHER_ITEM(6, "商品图片（其他尺寸）"),
    LIVE_IMAGE(7, "直播图片"),
    ;

    ItemDrawerImageTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    private final Integer value;
    private final String desc;
}
