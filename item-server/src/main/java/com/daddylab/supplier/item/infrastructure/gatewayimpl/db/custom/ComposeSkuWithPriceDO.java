package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2022年11月09日 2:18 PM
 */
@Data
public class ComposeSkuWithPriceDO  {

    private static final long serialVersionUID = 4121557210162485611L;
    private Long combinationId;
    private String skuCode;
    private Integer count;
    private BigDecimal costPrice;
    private BigDecimal salePrice;

    private BigDecimal costProportion;
    private BigDecimal saleProportion;
}
