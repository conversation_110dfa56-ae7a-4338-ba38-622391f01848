package com.daddylab.supplier.item.infrastructure.fileStore;

import cn.hutool.core.io.file.FileNameUtil;
import com.daddylab.supplier.item.domain.fileStore.constant.FileType;
import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.ImageStub;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.fileStore.vo.VideoStub;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.File;
import com.daddylab.supplier.item.infrastructure.upyun.gateway.UpyunGateway;
import com.daddylab.supplier.item.infrastructure.upyun.types.UploadFileResult;
import com.daddylab.supplier.item.infrastructure.upyun.types.UploadImageResult;
import com.daddylab.supplier.item.infrastructure.upyun.types.UploadVideoResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import static cn.hutool.core.util.URLUtil.decode;

/**
 * <AUTHOR>
 * @since 2023/10/12
 */
@Service
@Qualifier(StorageService.UPYUN)
public class FileGatewayUpyunImpl extends FileGatewayBaseImpl {
    @Autowired private UpyunGateway upyunGateway;

    @Override
    protected ImageStub uploadImage0(UploadFileAction action, File file) {
        final UploadImageResult uploadImageResult =
                upyunGateway.uploadImage(action.getDestPath(), action.getInputStream(), null);
        file.setType(FileType.IMAGE);
        file.setPath(decode(uploadImageResult.getPath()));
        final String fileName = decode(action.getFileName());
        final String fileNamePrefix = FileNameUtil.getPrefix(fileName);
        final String fileNameSuffix = FileNameUtil.getSuffix(fileName);
        file.setName(fileNamePrefix);
        file.setExt(fileNameSuffix);
        file.setMime(uploadImageResult.getContentType());
        file.setUrl(uploadImageResult.getUrl());
        file.setMd5(action.getMd5());
        file.setSize(uploadImageResult.getSize());
        file.setWidth(uploadImageResult.getWidth());
        file.setHeight(uploadImageResult.getHeight());
        file.setUpyun(true);
        return ImageStub.builder(uploadImageResult.getUrl())
                .size(uploadImageResult.getSize())
                .width(uploadImageResult.getWidth())
                .height(uploadImageResult.getHeight())
                .mime(uploadImageResult.getContentType())
                .name(file.getFullName())
                .build();
    }

    @Override
    protected FileStub uploadFile0(UploadFileAction action, File file) {
        final UploadFileResult uploadFileResult =
                upyunGateway.uploadFile(
                        action.getInputStream(), action.getDestPath(), action.getMd5());
        file.setType(FileType.UNKNOWN);
        file.setPath(decode(uploadFileResult.getPath()));
        final String fileName = decode(action.getFileName());
        final String fileNamePrefix = FileNameUtil.getPrefix(fileName);
        final String fileNameSuffix = FileNameUtil.getSuffix(fileName);
        file.setMime(uploadFileResult.getContentType());
        file.setName(fileNamePrefix);
        file.setExt(fileNameSuffix);
        file.setUrl(uploadFileResult.getUrl());
        file.setMd5(action.getMd5());
        file.setSize(uploadFileResult.getSize());
        file.setUpyun(true);
        return FileStub.builder(uploadFileResult.getUrl())
                .size(uploadFileResult.getSize())
                .name(file.getFullName())
                .build();
    }

    @Override
    protected VideoStub uploadVideo0(UploadFileAction action, File file) {
        final UploadVideoResult uploadVideoResult =
                upyunGateway.uploadVideo(
                        action.getInputStream(), action.getDestPath(), action.getMd5());
        file.setType(FileType.VIDEO);
        file.setPath(decode(uploadVideoResult.getPath()));
        final String fileName = decode(action.getFileName());
        final String fileNamePrefix = FileNameUtil.getPrefix(fileName);
        final String fileNameSuffix = FileNameUtil.getSuffix(fileName);
        file.setName(fileNamePrefix);
        file.setExt(fileNameSuffix);
        file.setUrl(uploadVideoResult.getUrl());
        file.setMd5(action.getMd5());
        file.setSize(uploadVideoResult.getSize());
        file.setWidth(0);
        file.setHeight(0);
        file.setDuration(0);
        file.setCoverId(0L);
        file.setCoverUrl(uploadVideoResult.getVideoFirstFrame());
        file.setMeta("");
        file.setUpyun(true);
        return VideoStub.builder(uploadVideoResult.getUrl())
                .size(uploadVideoResult.getSize())
                .mime("")
                .videoFirstFrame(uploadVideoResult.getVideoFirstFrame())
                .name(file.getFullName())
                .build();
    }
}
