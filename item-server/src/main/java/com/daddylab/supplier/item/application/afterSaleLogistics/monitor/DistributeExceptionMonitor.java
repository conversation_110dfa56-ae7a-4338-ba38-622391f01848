package com.daddylab.supplier.item.application.afterSaleLogistics.monitor;

import com.daddylab.supplier.item.application.afterSaleLogistics.AfterSaleLogisticsConfig;
import com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsException;
import com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsRootException;
import com.daddylab.supplier.item.application.afterSaleLogistics.domain.LogisticsTraceData;
import com.daddylab.supplier.item.application.afterSaleLogistics.domain.LogisticsTraceItem;
import com.daddylab.supplier.item.application.afterSaleLogistics.dto.LogisticExceptionRes;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsTrace;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 派件异常 24/48小时，派件后一直没有显示：已到达快递点、代签收，签收、本人已签收、已放置前台/门口等记录
 *
 * <AUTHOR> up
 * @date 2024年08月01日 3:51 PM
 */
@Order(MonitorOrder.DistributeExceptionMonitor)
@Component
public class DistributeExceptionMonitor implements OrderLogisticsExceptionMonitor {
  @Autowired private AfterSaleLogisticsConfig config;

  @Override
  public LogisticExceptionRes process(ProcessContext context) {
    final OrderLogisticsTrace trace = context.getTrace();
    final Long currentTurnTime = context.getTimestamp();
    LogisticExceptionRes res = new LogisticExceptionRes();

    final LogisticsTraceData logisticsTraceData = context.getLogisticsTraceData();
    // [打个补丁]如果没有派件时间，则从物流轨迹中寻找派件时间
    if (NumberUtil.isZeroOrNull(trace.getSigningTime()) && logisticsTraceData != null) {
      logisticsTraceData.getTrackList().stream()
          .filter(v -> config.getReceiveShortStatus().stream().anyMatch(v::containKeyword))
          .findFirst()
          .map(LogisticsTraceItem::getTrackDate)
          .ifPresent(trace::setSigningTime);
    }
    // 派件时间后有24/48小时无签收记录，则认为是异常
    if (NumberUtil.isPositive(trace.getDistributeTime())
        && !NumberUtil.isPositive(trace.getSigningTime())) {
      final long gapHour =
          DateUtil.calculateDifference(currentTurnTime, trace.getDistributeTime(), TimeUnit.HOURS);
      if (gapHour > 48) {
        res.setRootException(LogisticsRootException.DISTRIBUTE_EXCEPTION);
        res.setSubException(LogisticsException.DISTRIBUTE_48H);
        return res;
      }
      if (gapHour > 24) {
        res.setRootException(LogisticsRootException.DISTRIBUTE_EXCEPTION);
        res.setSubException(LogisticsException.DISTRIBUTE_24H);
        return res;
      }
    }

    res.setRootException(LogisticsRootException.NORMAL);
    return res;
  }
}
