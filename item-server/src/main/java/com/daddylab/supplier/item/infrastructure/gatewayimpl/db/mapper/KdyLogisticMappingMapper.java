package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.KdyLogisticMapping;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 快刀云物流公司名称映射 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
public interface KdyLogisticMappingMapper extends DaddyBaseMapper<KdyLogisticMapping> {

    List<String> inferStdName(@Param("logisticsName") String logisticsName);
}
