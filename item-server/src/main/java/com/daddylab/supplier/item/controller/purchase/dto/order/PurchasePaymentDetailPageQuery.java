package com.daddylab.supplier.item.controller.purchase.dto.order;

import com.alibaba.cola.dto.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class PurchasePaymentDetailPageQuery extends PageQuery {

    private static final long serialVersionUID = 5996273118025856303L;

    private Long purchaseOrderId;

    private List<Long> detailIds = new ArrayList<>();

}
