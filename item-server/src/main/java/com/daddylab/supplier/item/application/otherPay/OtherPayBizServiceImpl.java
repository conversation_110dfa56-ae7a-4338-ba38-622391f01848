package com.daddylab.supplier.item.application.otherPay;

import cn.hutool.core.collection.ListUtil;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.application.aws.service.AwsApprovalFlowService;
import com.daddylab.supplier.item.application.common.event.StockInOrOutEvent;
import com.daddylab.supplier.item.application.organazation.OrganizationService;
import com.daddylab.supplier.item.application.otherPayDetail.OtherPayDetailBizService;
import com.daddylab.supplier.item.application.purchasePayable.dto.PurchasePayableCmd;
import com.daddylab.supplier.item.application.purchasePayable.service.PurchasePayableBizService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.common.trans.OtherPayTransMapper;
import com.daddylab.supplier.item.controller.otherPay.dto.OtherImageVo;
import com.daddylab.supplier.item.controller.otherPay.dto.OtherPayCmd;
import com.daddylab.supplier.item.controller.otherPay.dto.OtherPayQueryPage;
import com.daddylab.supplier.item.controller.otherPay.dto.OtherPayVo;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.domain.otherPay.dto.OtherAuditDto;
import com.daddylab.supplier.item.domain.otherPay.enums.OtherAuditStatus;
import com.daddylab.supplier.item.domain.otherPay.enums.OtherPayStatus;
import com.daddylab.supplier.item.domain.otherPayDetail.enums.PayType;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Organization;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherPay;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherPayDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAwsBusinessLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOtherPayService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @ClassName OtherPayBizServiceImpl.java
 * @description
 * @createTime 2022年03月24日 15:42:00
 */
@Slf4j
@Service
public class OtherPayBizServiceImpl implements OtherPayBizService {

    private final static String OrderPrefix = "YF-QT00";

    @Autowired
    private IOtherPayService iOtherPayService;
    @Autowired
    private ProviderGateway providerGateway;
    @Autowired
    @Qualifier("UserGatewayCacheImpl")
    private UserGateway userGateway;
    @Autowired
    private OtherPayDetailBizService otherPayDetailBizService;
    @Autowired
    private OperateLogGateway operateLogGateway;
    @Autowired
    @Qualifier("awsApprovalFlowServiceImpl")
    private AwsApprovalFlowService awsApprovalFlowService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private IAwsBusinessLogService iAwsBusinessLogService;
    @Autowired
    RefreshConfig refreshConfig;
    @Autowired
    private PurchasePayableBizService purchasePayableBizService;

    @Override
    public PageResponse<OtherPayVo> queryPage(OtherPayQueryPage otherPayQueryPage) {
        LambdaQueryWrapper<OtherPay> queryWrapper = setParam(otherPayQueryPage);
        Page<OtherPay> pageReq = new Page<>(otherPayQueryPage.getPageIndex(), otherPayQueryPage.getPageSize());
        Page<OtherPay> page = iOtherPayService.page(pageReq, queryWrapper);
        List<OtherPayVo> otherPayVos = OtherPayTransMapper.INSTANCE.doToVos(page.getRecords());

        otherPayVos.forEach(
                otherPayVo -> {
                    ArrayList<Integer> list = Lists.newArrayList();
                    // 查询应付总金额和其他应付类型
                    List<OtherPayDetail> detailList =
                            otherPayDetailBizService.getById(otherPayVo.getId());
                    detailList.forEach(
                            detail -> {
                                list.add(detail.getPayType());
                                if (Objects.equals(detail.getPayType(), PayType.TOTAL.getValue())) {
                                    otherPayVo.setPayTotal(detail.getPayAmount());
                                }
                            });
                    otherPayVo.setPayType(list);
                    Provider provider = providerGateway.getById(otherPayVo.getProviderId());
                    if (Objects.nonNull(provider)) {
                        otherPayVo.setProviderName(provider.getName());
                        final Long partnerProviderId = provider.getPartnerProviderId();
                        otherPayVo.setIsBlacklist(
                                providerGateway
                                        .partnerQueryById(partnerProviderId)
                                        .map(PartnerProviderResp::getIsBlacklist)
                                        .orElse(0));
                    }
                    // 查询采购人员
                    StaffInfo staffInfo = userGateway.queryStaffInfoById(otherPayVo.getBuyerId());
                    if (Objects.nonNull(staffInfo)) {
                        otherPayVo.setBuyerName(staffInfo.getNickname());
                    } else {
                        otherPayVo.setBuyerName("");
                    }
                });
        return PageResponse.of(otherPayVos, (int) page.getTotal(), otherPayQueryPage.getPageSize(), otherPayQueryPage.getPageIndex());
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response createOrUpdateOtherPay(OtherPayCmd cmd) {
        cmd.setVoucher(JSON.toJSONString(cmd.getVouchers()));
        OtherPay otherPay = OtherPayTransMapper.INSTANCE.cmdToDo(cmd);
        if (Objects.isNull(cmd.getId())) {
            String orderNo = createOrderNo();
            otherPay.setPayNo(orderNo);
            iOtherPayService.saveOrUpdate(otherPay);
            otherPayDetailBizService.insert(cmd.getOtherPayDetails(), otherPay.getId());
            saveOperateLog(otherPay);
            if (Objects.equals(OtherPayStatus.WAIT_AUDIT.getValue(), cmd.getStatus())) {
                log.info("该创建的----------------------------------其他应付ID：{},状态:{}", cmd.getId(), cmd.getStatus());
                //创建审核实例
                createAwsApproval(otherPay);
            }
        } else {
            OtherPay other = iOtherPayService.getById(cmd.getId());
            if (Objects.isNull(other)) {
                throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查不到该记录");
            }
            if (OtherPayStatus.isEdit(other.getStatus())) {
                throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前数据状态不可该操作");
            }
            otherPayDetailBizService.update(cmd.getOtherPayDetails(), other.getId());
            operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.OTHER_PAY,
                    other.getId(), "修改其他应付单", null);
            iOtherPayService.saveOrUpdate(otherPay);

            if (Objects.equals(OtherPayStatus.WAIT_AUDIT.getValue(), cmd.getStatus())) {
                log.info("该创建的----------------------------------其他应付ID：{},状态:{}", cmd.getId(), cmd.getStatus());
                otherPay.setStatus(OtherPayStatus.WAIT_AUDIT.getValue());

                if (Objects.equals(OtherPayStatus.NO_COMMIT.getValue(), other.getStatus()) &&
                        StringUtils.isNotBlank(other.getApprovalId())) {
                    awsApprovalFlowService.submit(otherPay.getApprovalId());
                }

                if (StringUtils.isNotBlank(other.getApprovalId())) {
                    otherPay.setApprovalId(other.getApprovalId());
                }

                //创建审核实例
                createAwsApproval(otherPay);
                if (Objects.equals(OtherPayStatus.REFUSE.getValue(), other.getStatus()) ||
                        Objects.equals(OtherPayStatus.REVOCATION.getValue(), other.getStatus())) {
                    awsApprovalFlowService.submit(otherPay.getApprovalId());
                }
            }
        }
        return Response.buildSuccess();
    }

    private void saveOperateLog(OtherPay otherPay){
        OtherPayStatus status = OtherPayStatus.of(otherPay.getStatus());
        if (Objects.nonNull(status)){
            switch (status){
                case NO_COMMIT:
                    operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.OTHER_PAY,
                            otherPay.getId(), "保存其他应付单", null);
                    break;
                case WAIT_AUDIT:
                    operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.OTHER_PAY,
                            otherPay.getId(), "提交其他应付单", null);
                    break;
                default:
            }
        }
    }


    private void createAwsApproval(OtherPay otherPay) {
        String approvalId;
        if (StringUtils.isBlank(otherPay.getApprovalId())) {
            // 创建审核实例
            List<String> list = ListUtil.of("BO_DLAB_ERP_QTYFD_NEW");
            approvalId = awsApprovalFlowService.createAndStart(otherPay.getId(),
                    PurchaseTypeEnum.OTHER_PAYABLE, "其他应付-" + otherPay.getId(), list);
            log.info("其他应付单----------------------------------流程ID：{}", approvalId);
        } else {
            approvalId = otherPay.getApprovalId();
        }
        otherPay.setApprovalId(approvalId);
        otherPay.setAuditedAt(DateUtil.currentTime());
        iOtherPayService.updateById(otherPay);
    }


    @Override
    public SingleResponse<OtherPayVo> getById(Long id) {
        Long userId = UserContext.getUserId();
        OtherPay otherPay = iOtherPayService.getById(id);
        if (Objects.isNull(otherPay)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查不到该记录");
        }
        OtherPayVo otherPayVo = OtherPayTransMapper.INSTANCE.doToVo(otherPay);
        List<OtherPayDetail> otherPayDetails = otherPayDetailBizService.getById(id);
        otherPayVo.setOtherPayDetails(otherPayDetails);
        otherPayDetails.forEach(otherPayDetail -> {
            if (Objects.equals(otherPayDetail.getPayType(),PayType.TOTAL.getValue())){
                otherPayVo.setPayTotal(otherPayDetail.getPayAmount());
            }
        });

        List<OtherImageVo> itemDetailImageVos = JSON.parseArray(otherPay.getVoucher(), OtherImageVo.class);
        otherPayVo.setImageList(itemDetailImageVos);
        //查询采购组织名称
        if (Objects.nonNull(otherPayVo.getOrganizationId())){
            SingleResponse<Organization> organization = organizationService.getById(otherPayVo.getOrganizationId());
            if (Objects.nonNull(organization)){
                otherPayVo.setOrganizationName(organization.getData().getName());
            }
        }
        //查询姓名
        StaffInfo staffInfo = userGateway.queryStaffInfoById(otherPayVo.getCreatedUid());
        if (Objects.nonNull(staffInfo)) {
            otherPayVo.setCreatedName(staffInfo.getNickname());
        } else {
            otherPayVo.setCreatedName("");
        }
        //查供应商姓名
        Provider provider = providerGateway.getById(otherPayVo.getProviderId());
        if (Objects.nonNull(provider)){
            otherPayVo.setProviderName(provider.getName());
            final Long partnerProviderId = provider.getPartnerProviderId();
            otherPayVo.setIsBlacklist(
                    providerGateway
                            .partnerQueryById(partnerProviderId)
                            .map(PartnerProviderResp::getIsBlacklist)
                            .orElse(0));
        }
        //查询采购名称
        StaffInfo buyer = userGateway.queryStaffInfoById(otherPayVo.getBuyerId());
        if (Objects.nonNull(buyer)) {
            otherPayVo.setBuyerName(buyer.getNickname());
        } else {
            otherPayVo.setBuyerName("");
        }
        if (Objects.equals(userId,otherPayVo.getCreatedUid())){
            otherPayVo.setIsEdit(0);
        }
        return SingleResponse.of(otherPayVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response delete(Long id) {
        OtherPay otherPay = iOtherPayService.getById(id);
        if (Objects.isNull(otherPay)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查不到该记录");
        }
        Integer status = otherPay.getStatus();
        if (status.equals(OtherPayStatus.NO_COMMIT.getValue())||status.equals(OtherPayStatus.REFUSE.getValue())
                ||status.equals(OtherPayStatus.REVOCATION.getValue())){
            iOtherPayService.removeById(id);
            operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.OTHER_PAY,
                    otherPay.getId(), "删除其他应付单", null);
        } else{
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "该应付单状态不允许删除");
        }
        return Response.buildSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response revocation(Long id) {
        OtherPay otherPay = iOtherPayService.getById(id);
        if (Objects.isNull(otherPay)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查不到该记录");
        }
        Integer status = otherPay.getStatus();
        if (status.equals(OtherPayStatus.WAIT_AUDIT.getValue())) {
            //调用炎黄撤回
            if (Objects.nonNull(otherPay.getApprovalId())){
                awsApprovalFlowService.processRestart(otherPay.getApprovalId());
            }
            otherPay.setStatus(OtherPayStatus.REVOCATION.getValue());
            otherPay.setAuditedStatus(OtherAuditStatus.NO_COMMIT.getValue());
            iOtherPayService.updateById(otherPay);
            operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.OTHER_PAY,
                    otherPay.getId(), "撤回审核其他应付单", null);
        } else {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "该应付单状态不允许撤回");
        }
        return Response.buildSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response audit(OtherAuditDto otherAuditDto) {
        Long userId = UserContext.getUserId();
        OtherPay otherPay = iOtherPayService.getById(otherAuditDto.getId());
        if (Objects.isNull(otherPay)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查不到该记录");
        }
        Integer status = otherPay.getStatus();
        if (status.equals(OtherPayStatus.WAIT_AUDIT.getValue())) {
            OtherAuditStatus typeEnum = OtherAuditStatus.of(otherAuditDto.getAuditStatus());
            if (Objects.isNull(typeEnum)) {
                throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "该应付单状态不存在");
            }
            otherPay.setAuditedStatus(typeEnum.getValue());
            otherPay.setAuditedUid(userId.intValue());
            switch (typeEnum) {
                case AUDIT_PASS:
                    operateLogGateway.addOperatorLog(userId, OperateLogTarget.OTHER_PAY,
                            otherPay.getId(), "审核其他应付单", null);
//                    awsOperate(userId, otherPay.getApprovalId(), AwsActionEnum.AGREE, otherAuditDto.getMsg());
//                    AwsControlStateEnum awsControlStateEnum = awsApprovalFlowService.processControlState(otherPay.getApprovalId());
//                    if (Objects.equals(AwsControlStateEnum.END, awsControlStateEnum)){
//                        otherPay.setStatus(OtherPayStatus.FINISH.getValue());
//                        otherPay.setAuditedStatus(OtherAuditStatus.FINISH.getValue());
//                    }
                    otherPay.setAuditedAt(DateUtil.currentTime());
                    break;
                case REFUSE:
                    operateLogGateway.addOperatorLog(userId, OperateLogTarget.OTHER_PAY,
                            otherPay.getId(), "拒绝其他应付单", null);
//                    awsOperate(userId, otherPay.getApprovalId(), AwsActionEnum.DISAGREE, otherAuditDto.getMsg());
//                    AwsControlStateEnum awsControlState = awsApprovalFlowService.processControlState(otherPay.getApprovalId());
//                    if (Objects.equals(AwsControlStateEnum.TERMINATE, awsControlState)){
//                        otherPay.setStatus(OtherPayStatus.REFUSE.getValue());
//                        otherPay.setAuditedStatus(OtherAuditStatus.REFUSE.getValue());
//                    }
                    break;
                default:
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "该应付单状态不存在");
            }
            iOtherPayService.updateById(otherPay);
        } else {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "该应付单状态不允许审核");
        }
        return Response.buildSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response unAudit(Long id) {
        Long userId = UserContext.getUserId();
        OtherPay otherPay = iOtherPayService.getById(id);
        if (Objects.isNull(otherPay)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查不到该记录");
        }
        if (!Objects.equals(userId,otherPay.getCreatedUid())){
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "非创建人员,无权限反审核");
        }

        Integer status = otherPay.getStatus();
        if (status.equals(OtherPayStatus.FINISH.getValue())) {
            //反审核
            awsApprovalFlowService.antiAudit(PurchaseTypeEnum.OTHER_PAYABLE,otherPay.getApprovalId());
            operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.OTHER_PAY,
                    otherPay.getId(), "反审核其他应付单", null);
            otherPay.setStatus(OtherPayStatus.WAIT_AUDIT.getValue());
            otherPay.setAuditedStatus(OtherAuditStatus.WAIT_AUDIT.getValue());
            otherPay.setAuditedUid(UserContext.getUserId().intValue());
            iOtherPayService.updateById(otherPay);

            PurchasePayableCmd cmd = new PurchasePayableCmd();
            cmd.setType(PurchaseTypeEnum.OTHER_PAYABLE.getValue());
            cmd.setRelatedOrderId(otherPay.getId());
            purchasePayableBizService.delete(cmd);
        } else {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "该应付单状态不允许反审核");
        }
        return Response.buildSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateState(Long otherPayId, Integer state) {
        OtherPay otherPay = iOtherPayService.getById(otherPayId);
        Assert.notNull(otherPay, "id非法，其他应付单查询为空");
        otherPay.setStatus(state);
        iOtherPayService.updateById(otherPay);
        //推给采购应付
        if (Objects.equals(OtherPayStatus.FINISH.getValue(),state)){
            final StockInOrOutEvent stockInOrOutEvent = StockInOrOutEvent.ofNotice(PurchaseTypeEnum.OTHER_PAYABLE, otherPay.getId(),
                    otherPay.getBuyerId(), otherPay.getCreatedUid());
            EventBusUtil.post(stockInOrOutEvent, true);
        }
    }


    private LambdaQueryWrapper<OtherPay> setParam(OtherPayQueryPage otherPayQueryPage){
        Long userId = UserContext.getUserId();
        otherPayQueryPage.setUserId(userId);
        boolean showAll = UserContext.hasPermission(GlobalConstant.OTHER_PAY_ALL);
        final Boolean isBuyer = UserPermissionJudge.isBuyer();
        log.info("登录---------------------userId:{} isBuyer:{}", userId, isBuyer);
        otherPayQueryPage.setShowAll(showAll);

        LambdaQueryWrapper<OtherPay> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(otherPayQueryPage.getBuyerId()), OtherPay::getBuyerId,
                otherPayQueryPage.getBuyerId());
        queryWrapper.eq(StringUtils.isNotBlank(otherPayQueryPage.getPayNo()), OtherPay::getPayNo, otherPayQueryPage.getPayNo())
                .eq(Objects.nonNull(otherPayQueryPage.getProviderId()), OtherPay::getProviderId, otherPayQueryPage.getProviderId())
                .eq(Objects.nonNull(otherPayQueryPage.getPayYear()), OtherPay::getPayYear, otherPayQueryPage.getPayYear())
                .eq(Objects.nonNull(otherPayQueryPage.getPayMonth()), OtherPay::getPayMonth, otherPayQueryPage.getPayMonth())
                .eq(Objects.nonNull(otherPayQueryPage.getStatus()), OtherPay::getStatus, otherPayQueryPage.getStatus());
        if (!otherPayQueryPage.getShowAll() && Objects.nonNull(otherPayQueryPage.getUserId())) {
            if (isBuyer){
                queryWrapper
                        .and(r -> r.eq(Objects.nonNull(userId), OtherPay::getBuyerId, userId).or()
                                .eq(Objects.nonNull(userId), OtherPay::getCreatedUid, userId));
            } else {
                queryWrapper.eq(OtherPay::getBuyerId, -1);
            }
        }
//        else {
//            //查看全部的权限
//            queryWrapper.and(r -> r.eq(OtherPay::getBuyerId,userId)
//                    .ne(OtherPay::getStatus, OtherPayStatus.NO_COMMIT.getValue())
//                    .or()
//                    .eq(OtherPay::getBuyerId,userId)
//                    .eq(OtherPay::getStatus, OtherPayStatus.NO_COMMIT.getValue())
//                    .or()
//                    .ne(OtherPay::getBuyerId,userId)
//                    .ne(OtherPay::getStatus, OtherPayStatus.NO_COMMIT.getValue())
//            );
//        }
        queryWrapper.orderByDesc(OtherPay::getCreatedAt);
        return queryWrapper;
    }

    private String createOrderNo() {
        String no = OrderPrefix + DateUtil.formatNow("yyyyMMdd");
        Long increment = RedisUtil.increment(no, 1);
        String format = String.format("%02d", increment);
        return no + format;
    }

}
