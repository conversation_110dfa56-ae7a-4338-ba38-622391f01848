package com.daddylab.supplier.item.application.platformItem.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/12/27
 */
@Data
@AllArgsConstructor
@ApiModel("平台商品下拉列表项")
public class PlatformItemDropDownItem {

    /**
     * id
     */
	@ApiModelProperty("id")
    private Long id;

    /**
     * 平台商品名称
     */
    @ApiModelProperty("平台商品名称")
    private String name;
}
