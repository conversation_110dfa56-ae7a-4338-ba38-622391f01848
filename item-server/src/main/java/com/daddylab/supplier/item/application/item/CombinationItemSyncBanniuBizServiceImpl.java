package com.daddylab.supplier.item.application.item;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

import com.daddylab.supplier.item.application.item.syncBanniu.ItemSyncBanniuException;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.application.warehouse.WarehouseGateway;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.thread.ThreadExecutors;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.banniu.BanniuCommonService;
import com.daddylab.supplier.item.domain.banniu.BanniuMiniService;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.item.gateway.BuyerGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemProcurementGateway;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemBanniuRefType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.types.banniu.ShippingWarehouseTask;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.internal.functions.Functions;
import io.reactivex.rxjava3.internal.schedulers.ExecutorScheduler;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.multiset.HashMultiSet;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.backoff.BackOffExecution;
import org.springframework.util.backoff.ExponentialBackOff;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Nullable;

/**
 * <AUTHOR>
 * @since 2023/9/5
 */
@Service
@Slf4j
@Validated
@RequestMapping
public class CombinationItemSyncBanniuBizServiceImpl
        implements CombinationItemSyncBanniuBizService {

    @javax.annotation.Resource private BanniuMiniService banniuMiniService;
    @javax.annotation.Resource private BanniuCommonService banniuCommonService;
    @javax.annotation.Resource private RefreshConfig refreshConfig;
    @javax.annotation.Resource private BanniuShippingWarehouseConfig banniuShippingWarehouseConfig;
    @javax.annotation.Resource private IItemBanniuRefService itemBanniuRefService;
    @javax.annotation.Resource private WarehouseGateway warehouseGateway;
    @javax.annotation.Resource private BuyerGateway buyerGateway;
    @javax.annotation.Resource private ItemProcurementGateway itemProcurementGateway;
    @javax.annotation.Resource private CategoryGateway categoryGateway;
    @javax.annotation.Resource private StaffService staffService;
    @javax.annotation.Resource private ICombinationItemService combinationItemService;
    @javax.annotation.Resource private IComposeSkuService composeSkuService;
    @javax.annotation.Resource private IItemService itemService;
    @javax.annotation.Resource private IItemSkuService itemSkuService;

    @Override
    @RequestMapping(
            path = "/dev/itemSyncBanniu/syncBatchByCombinationItemIds",
            method = RequestMethod.POST)
    @ResponseBody
    public void syncBatchByCombinationItemIds(@RequestBody List<Long> combinationItemIds) {
        if (CollUtil.isEmpty(combinationItemIds)) {
            return;
        }
        for (Long combinationItemId : combinationItemIds) {
            try {
                syncOneByCombinationItemId(combinationItemId);
            } catch (Exception e) {
                log.error("【班牛发货仓同步|组合装|批量ID同步】同步异常 组合装ID={}", combinationItemId, e);
            }
        }
    }

    @Override
    public void syncOneByCombinationItemId(Long combinationItemId) {
        final CombinationItem combinationItem =
                Objects.requireNonNull(
                        combinationItemService.getById(combinationItemId),
                        "班牛发货仓同步异常，组合装ID无效:" + combinationItemId);
        final List<ComposeSku> composeSkus =
                composeSkuService
                        .lambdaQuery()
                        .eq(ComposeSku::getCombinationId, combinationItemId)
                        .list();
        Assert.notEmpty(composeSkus, "班牛发货仓同步异常，组合装单品列表为空:" + combinationItemId);
        syncOneByCombinationItemModel(combinationItem, composeSkus);
    }

    @Override
    public void syncOneByCombinationItemModel(
            CombinationItem combinationItem, List<ComposeSku> composeSkus) {
        if (CollUtil.isEmpty(composeSkus)) {
            return;
        }
        final List<ItemBanniuRef> refs =
                itemBanniuRefService
                        .lambdaQuery()
                        .eq(ItemBanniuRef::getType, ItemBanniuRefType.COMBINATION_ITEM)
                        .eq(ItemBanniuRef::getFirstId, combinationItem.getId())
                        .list();
        final Set<Long> itemIds =
                composeSkus.stream().map(ComposeSku::getItemId).collect(Collectors.toSet());
        final Map<Long, Item> itemMap =
                itemService.listByIds(itemIds).stream()
                        .collect(Collectors.toMap(Entity::getId, Function.identity()));
        final Set<Long> skuIds =
                composeSkus.stream().map(ComposeSku::getSkuId).collect(Collectors.toSet());
        final Map<Long, ItemSku> itemSkuMap =
                itemSkuService.listByIds(skuIds).stream()
                        .collect(Collectors.toMap(ItemSku::getId, Function.identity()));
        final ThreadPoolTaskExecutor threadPool = ThreadUtil.getThreadPool(PoolEnum.EVENT_POOL);
        final CountDownLatch countDownLatch = new CountDownLatch(composeSkus.size());
        final ArrayList<ItemSyncBanniuException.ErrorItem> errorItems = new ArrayList<>();
        for (ComposeSku composeSku : composeSkus) {
            final Integer banniuId = getBanniuId(refs, composeSku);
            final Long itemId = composeSku.getItemId();
            final Long skuId = composeSku.getSkuId();
            final Item item = itemMap.get(itemId);
            final ItemSku itemSku = itemSkuMap.get(skuId);
            if (item == null || itemSku == null) {
                countDownLatch.countDown();
                continue;
            }

            final Warehouse warehouse =
                    Optional.ofNullable(item.getWarehouseNo())
                            .flatMap(warehouseGateway::getWarehouse)
                            .orElseThrow(
                                    () ->
                                            ExceptionPlusFactory.bizException(
                                                    ErrorCode.DATA_NOT_FOUND,
                                                    "商品关联的仓库不存在，itemId:" + item.getId()));

            final Optional<ItemProcurement> itemProcurementOpt =
                    Optional.ofNullable(itemProcurementGateway.getProcurementByItemId(itemId));

            final Optional<Category> categoryOpt =
                    Optional.ofNullable(item.getCategoryId()).map(categoryGateway::getById);
            final String category =
                    categoryOpt
                            .map(Category::getPath)
                            .map(
                                    path ->
                                            StrUtil.split(path, '/', true, true).stream()
                                                    .limit(2)
                                                    .collect(Collectors.joining("/")))
                            .orElse("无");
            Optional<DadStaffVO> buyerBanniuUserOptional =
                    itemProcurementOpt
                            .map(ItemProcurement::getBuyerId)
                            .flatMap(buyerGateway::buyer)
                            .map(Buyer::getUserId)
                            .map(staffService::getStaff);

            final List<Long> orderPersonnelIds =
                    StrUtil.splitTrim(warehouse.getOrderPersonnel(), ',').stream()
                            .map(Long::parseLong)
                            .collect(Collectors.toList());
            final Map<Long, DadStaffVO> staffMap = staffService.getStaffMap(orderPersonnelIds);
            final String orderPersonnelNicks =
                    staffMap.values().stream()
                            .map(DadStaffVO::getNickname)
                            .collect(Collectors.joining(","));
            final ShippingWarehouseTask shippingWarehouseTask = new ShippingWarehouseTask();
            final LocalDateTime now = LocalDateTime.now();
            if (banniuId != null) {
                shippingWarehouseTask.setUpdateTime(now);
            } else {
                shippingWarehouseTask.setCreatorUserId(
                        banniuShippingWarehouseConfig.getSysUserId());
                shippingWarehouseTask.setCreateTime(now);
            }
            if (!banniuShippingWarehouseConfig.getNotUpdateCompleteUserId()) {
                shippingWarehouseTask.setCompleteUserId(shippingWarehouseTask.getCreatorUserId());
            }
            shippingWarehouseTask.setCompleteTime(now);
            shippingWarehouseTask.setShelfTime(null);
            shippingWarehouseTask.setBuyerUser(
                    buyerBanniuUserOptional.map(DadStaffVO::getNickname).orElse(null));
            shippingWarehouseTask.setCategory(category);

            shippingWarehouseTask.setItemName(combinationItem.getName());
            shippingWarehouseTask.setItemSpec(
                    Optional.ofNullable(itemSku.getSpecifications())
                            .filter(StrUtil::isNotBlank)
                            .orElse("无"));
            shippingWarehouseTask.setItemCode(combinationItem.getCode());
            shippingWarehouseTask.setWarehouse(warehouse.getName());
            shippingWarehouseTask.setSpuCode(item.getSupplierCode());
            shippingWarehouseTask.setSpuName(item.getName());
            shippingWarehouseTask.setOrderUser(orderPersonnelNicks);

            if (banniuId == null) {
                log.debug("【班牛发货仓同步｜组合商品】当前无班牛ID关联，走新增逻辑:{} {}", composeSku, shippingWarehouseTask);
                CompletableFuture.supplyAsync(
                                () ->
                                        banniuMiniService.taskCreate(
                                                banniuShippingWarehouseConfig
                                                        .getShippingWarehouseProjectId(),
                                                banniuShippingWarehouseConfig.getSysUserId(),
                                                shippingWarehouseTask),
                                threadPool)
                        .whenComplete(
                                (r, e) -> {
                                    countDownLatch.countDown();
                                    try {
                                        final ItemBanniuRef itemBanniuRef = new ItemBanniuRef();
                                        itemBanniuRef.setBanniuId(String.valueOf(r));
                                        itemBanniuRef.setType(ItemBanniuRefType.COMBINATION_ITEM);
                                        itemBanniuRef.setFirstId(
                                                String.valueOf(combinationItem.getId()));
                                        itemBanniuRef.setSecondId(
                                                String.valueOf(composeSku.getId()));
                                        if (e != null) {
                                            log.error(
                                                    "【班牛发货仓同步｜组合商品】同步失败 combinationId={} composeSkuId={} data={}",
                                                    combinationItem.getId(),
                                                    composeSku.getId(),
                                                    shippingWarehouseTask,
                                                    e);
                                            errorItems.add(
                                                    new ItemSyncBanniuException.ErrorItem(
                                                            combinationItem.getId(),
                                                            composeSku.getId(),
                                                            e));
                                        } else {
                                            itemBanniuRefService.save(itemBanniuRef);
                                        }
                                    } catch (Exception ex) {
                                        log.error(
                                                "【班牛发货仓同步｜组合商品】工单创建后置处理异常 combinationId={} composeSkuId={} data={}",
                                                combinationItem.getId(),
                                                composeSku.getId(),
                                                shippingWarehouseTask,
                                                e);
                                        errorItems.add(
                                                new ItemSyncBanniuException.ErrorItem(
                                                        combinationItem.getId(),
                                                        composeSku.getId(),
                                                        "工单创建后置处理异常",
                                                        true));
                                    }
                                });
            } else {
                log.debug(
                        "【班牛发货仓同步｜组合商品】当前已存在班牛ID关联，走更新逻辑:{} {}", composeSku, shippingWarehouseTask);
                CompletableFuture.runAsync(
                                () ->
                                        banniuMiniService.taskUpdate(
                                                banniuShippingWarehouseConfig
                                                        .getShippingWarehouseProjectId(),
                                                banniuShippingWarehouseConfig.getSysUserId(),
                                                shippingWarehouseTask,
                                                banniuId),
                                threadPool)
                        .whenComplete(
                                (r, e) -> {
                                    if (e != null) {
                                        log.error(
                                                "【班牛发货仓同步｜组合商品】更新失败 itemSkuId:{} {} {}",
                                                itemSku.getId(),
                                                banniuId,
                                                shippingWarehouseTask,
                                                e);
                                        errorItems.add(
                                                new ItemSyncBanniuException.ErrorItem(
                                                        combinationItem.getId(),
                                                        composeSku.getId(),
                                                        e));
                                    }
                                    countDownLatch.countDown();
                                });
            }
        }
        try {
            countDownLatch.await();
        } catch (Exception ignored) {
        }
        if (errorItems.size() > 0) {
            throw new ItemSyncBanniuException(ItemBanniuRefType.COMBINATION_ITEM, errorItems);
        }
    }

    @Nullable
    private static Integer getBanniuId(List<ItemBanniuRef> refs, ComposeSku composeSku) {
        return refs.stream()
                .filter(ref -> ref.getSecondId().equals(composeSku.getId().toString()))
                .findAny()
                .map(ItemBanniuRef::getBanniuId)
                .map(Integer::parseInt)
                .orElse(null);
    }

    @SneakyThrows
    @Override
    public void syncAll(int limit) {
        final HashMultiSet<String> stats = new HashMultiSet<>();
        final List<CombinationItem> combinationItems = combinationItemService.list();
        final Map<Long, List<ComposeSku>> composeSkusGroup =
                composeSkuService.list().stream()
                        .collect(Collectors.groupingBy(ComposeSku::getCombinationId));
        final ExecutorService executorService =
                ThreadExecutors.newBlockOfferExecutor("combination-sync-banniu", 8, 8);
        final ExecutorScheduler scheduler = new ExecutorScheduler(executorService, true, false);
        final ExponentialBackOff backOff = new ExponentialBackOff(2000, 2);
        backOff.setMaxElapsedTime(15_000);
        final LoadingCache<Object, BackOffExecution> backOffs =
                Caffeine.newBuilder().build(k -> backOff.start());
        int count = 0;
        for (CombinationItem combinationItem : combinationItems) {
            if (++count > limit) {
                break;
            }
            final Long id = combinationItem.getId();
            log.debug("【班牛发货仓同步｜全量同步组合商品】开始处理 combinationItemId={}", id);
            Observable.just(combinationItem)
                    .doOnNext(
                            none -> {
                                syncOneByCombinationItemModel(
                                        combinationItem, composeSkusGroup.get(id));
                                stats.add("同步成功");
                                log.debug("【班牛发货仓同步｜全量同步组合商品】同步完成 combinationItemId={}", id);
                            })
                    .doOnError(
                            e -> {
                                stats.add("同步失败");
                                log.error("【班牛发货仓同步｜全量同步组合商品】同步异常，combinationItemId:{}", id, e);
                            })
                    .retryWhen(
                            throwableObservable ->
                                    throwableObservable.flatMap(
                                            e -> {
                                                if (!(e instanceof IOException
                                                        | e instanceof ItemSyncBanniuException)) {
                                                    return Observable.error(e);
                                                }
                                                final BackOffExecution backOffExecution =
                                                        Objects.requireNonNull(backOffs.get(id));
                                                final long nextBackOff =
                                                        backOffExecution.nextBackOff();
                                                if (nextBackOff > 0) {
                                                    stats.add("失败重试");
                                                    log.error(
                                                            "【班牛发货仓同步｜全量同步组合商品】同步异常，{}ms 后重试， combinationItemId:{}",
                                                            nextBackOff,
                                                            id,
                                                            e);
                                                    return Observable.timer(
                                                            nextBackOff, TimeUnit.MILLISECONDS);
                                                }
                                                return Observable.error(
                                                        new RuntimeException("重试未成功:" + id));
                                            }))
                    .subscribeOn(scheduler)
                    .subscribe(Functions.emptyConsumer(), Functions.emptyConsumer());
        }
        executorService.shutdown();
        executorService.awaitTermination(30, TimeUnit.SECONDS);
        log.info("【班牛发货仓同步｜全量同步组合商品】同步完成 {}", stats);
    }
}
