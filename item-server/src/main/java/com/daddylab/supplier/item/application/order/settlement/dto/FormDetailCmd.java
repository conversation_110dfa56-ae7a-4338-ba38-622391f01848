package com.daddylab.supplier.item.application.order.settlement.dto;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年08月21日 2:01 PM
 */
@Data
@ApiOperation("查看单据基本信息请求")
public class FormDetailCmd {

    @ApiModelProperty("ids")
    private List<Long> ids;

    @ApiModelProperty("是否新增结算")
    @NotNull(message = "是否新增结算判断不得为空")
    private Boolean isAdd;
}
