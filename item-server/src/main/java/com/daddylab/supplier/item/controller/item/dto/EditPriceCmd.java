package com.daddylab.supplier.item.controller.item.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/21 6:10 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("编辑商品价格信息入参")
public class EditPriceCmd extends Command {

    private static final long serialVersionUID = 5521379561151713720L;

    @ApiModelProperty("商品id")
    @NotNull
    private Long itemId;

    @Valid
    @ApiModelProperty("采购成本list")
    List<ProcurementPriceDto> procurementPrices;
    @ApiModelProperty("日常售价list")
    @Valid
    List<SalesPriceDto> salesPrices;

}
