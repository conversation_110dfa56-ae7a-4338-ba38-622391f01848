package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 其他出库单详情
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OtherStockOutDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 明细ID
     */
    private Integer recId;

    /**
     * 其他入库单id
     */
    private Long orderId;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 商品skuid
     */
    private Long skuId;

    /**
     * skucode
     */
    private String skuCode;

    /**
     * 预期出库量
     */
    private Integer predictCount;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 是否残次品 0:不是 1:是
     */
    private Integer isDefect;

    /**
     * 单位
     */
    private String unit;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 品牌编号
     */
    private String brandSn;

    /**
     * 条形码
     */
    private String barCode;

    /**
     * 预估重量
     */
    private BigDecimal predictWeight;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除，不能做业务。这是软删除标记
     */
    @TableLogic
    private Integer isDel;

    private Long deletedAt;

    private Integer businessLine;

}
