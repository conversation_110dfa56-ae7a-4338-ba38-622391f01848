package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.item.combinationItem.dto.query.CombinationItemPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.domain.exportTask.dto.combinationItem.ComposerSkuAllSheet;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CombinationItem;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
public interface CombinationItemMapper extends DaddyBaseMapper<CombinationItem> {

    List<ComposeSkuDO> listSkuDetail(@Param("query") ComposeSkuPageQuery query, @Param("skuIdList") List<Long> skuIdList
            , @Param("skuCodeList") List<String> skuCodeList);

    Integer countSkuDetail(@Param("query") ComposeSkuPageQuery query, @Param("skuIdList") List<Long> skuIdList
            , @Param("skuCodeList") List<String> skuCodeList);

    List<CombinationDO> listItem(@Param("query") CombinationItemPageQuery query);

    Integer countListItem(@Param("query") CombinationItemPageQuery query);

    List<ComposerSkuAllSheet> queryExport(@Param("query") CombinationItemPageQuery query);

    List<CombinationProviderDO> queryProviderList(@Param("codeList") List<String> codeList);

    List<CombinationNameDO> queryNameList(@Param("codeList") List<String> codeList);

    @Update("update combination_item set procurement_price = #{price} where code = #{code} ")
    void updateCombinationCostPrice(@Param("code") String code, @Param("price") String price);

    List<ComposeSkuWithPriceDO> selectSameGroupSkuBySkuCode(@Param("skuCodes") List<String> skuCodes);

    @Update("update compose_sku set cost_proportion = #{costProportion},sale_proportion = #{saleProportion}\n" +
            "where sku_code = #{skuCode} and combination_id = #{id}")
    void updateSkuProportion(@Param("costProportion") BigDecimal p1, @Param("saleProportion") BigDecimal p2
            , @Param("skuCode") String skuCode, @Param("id") Long id);

    CombinationItem queryByCodeIncludeDeleted(String suiteNo);

    /**
     * 根据组合装编码查询 下属单品的供应商ID，去重返回
     * @param combinationItemCode
     * @return
     */
    List<Long> getComposeSkuProviderIds(@Param("combinationItemCode") String combinationItemCode);

    Integer skuBelongToCombination(@Param("skuId") Long skuId,
                                   @Param("combinationItemId") Long combinationItemId);
}
