package com.daddylab.supplier.item.application.purchase.combinationPrice.sheet;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2025年05月06日 4:40 PM
 */
@Data
public class CombinationStepPriceExcelSheet {

  @ExcelProperty("商品spu")
  private String spu;

  @ExcelProperty("商品sku")
  private String skuCode;

  @ExcelProperty("商品名称")
  private String productName;

  @ExcelProperty("商品规格")
  private String productSpec;

  @ExcelProperty("商品规格数量")
  private String productSpecQuantity;

  @ExcelProperty("是否纯活动商品")
  private String isActivityOnly;

  @ExcelProperty("日常供价无优惠")
  private String regularPrice;

  @ExcelProperty("工厂/仓库")
  private String factoryOrWarehouse;

  @ExcelProperty("供应商")
  private String supplier;

  @ExcelProperty("采购负责人")
  private String purchaser;

  @ExcelProperty("优惠类型")
  private String discountType;

  @ExcelProperty("平台名称")
  private String platformName;

  @ExcelProperty("方式")
  private String priceType;

  @ExcelProperty("活动开始时间")
  private String activityStartTime;

  @ExcelProperty("活动结束时间")
  private String activityEndTime;

  @ExcelProperty("订单拍下份数")
  private String orderQuantity;

  @ExcelProperty("实发单品数量")
  private String deliveredQuantity;

  @ExcelProperty("按价格优惠结算成本")
  private String discountPriceCost;

  @ExcelProperty("按数量优惠件数")
  private String discountQuantity;

  @ExcelProperty("供价优惠内容")
  private String discountContent;

  @ExcelProperty("备注说明")
  private String remark;
}
