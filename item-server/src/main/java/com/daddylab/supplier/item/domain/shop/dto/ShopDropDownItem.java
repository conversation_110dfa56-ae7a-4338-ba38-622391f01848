package com.daddylab.supplier.item.domain.shop.dto;

import com.alibaba.cola.dto.DTO;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("店铺类下拉列表值对象")
public class ShopDropDownItem extends DTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("店铺id")
    private Long id;

    @ApiModelProperty("店铺编号")
    private String sn;

    @ApiModelProperty("店铺名称")
    private String name;

    @ApiModelProperty(hidden = true)
    private String businessLine;

    @ApiModelProperty("业务线")
    private List<Integer> businessLineList;

    @ApiModelProperty("店铺负责人列表")
    private List<DadStaffVO> principalList;

    @ApiModelProperty("店铺平台")
    private Platform platform;


}
