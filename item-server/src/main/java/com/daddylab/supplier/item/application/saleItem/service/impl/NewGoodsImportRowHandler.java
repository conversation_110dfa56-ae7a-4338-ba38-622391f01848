package com.daddylab.supplier.item.application.saleItem.service.impl;

import com.daddylab.supplier.item.application.saleItem.dto.NewGoodsSheetRow;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.item.gateway.BuyerGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemProcurement;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemLaunchPlanService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemProcurementService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INewGoodsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil.ChangePropertyObj;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2022/8/4
 */
@Service
@Slf4j(topic = "com.daddylab.supplier.item.application.saleItem.service.impl.NewGoodsBizServiceImpl")
class NewGoodsImportRowHandler {

    @Autowired
    FileGateway fileGateway;
    @Autowired
    private ItemSkuGateway itemSkuGateway;
    @Autowired
    private INewGoodsService newGoodsService;
    @Autowired
    private OperateLogDomainService operateLogDomainService;
    @Autowired
    @Qualifier("UserGatewayCacheImpl")
    private UserGateway userGateway;
    @Autowired
    private BuyerGateway buyerGateway;
    @Autowired
    private ItemGateway itemGateway;
    @Autowired
    private IItemLaunchPlanService itemLaunchPlanService;
    @Autowired
    private IItemProcurementService itemProcurementService;

    @Transactional(rollbackFor = Throwable.class)
    public void handleImportRow(HashMap<String, Long> launchDateEpochsMap,
            Map<Long, Long> planIdMap,
            Long currentTime, NewGoodsSheetRow sheetRow) {
        final String skuCode = sheetRow.getSkuCode();
        if (StringUtil.isBlank(skuCode)) {
            return;
        }
        final Long planId = planIdMap.get(launchDateEpochsMap.get(sheetRow.getLaunchDate()));
        if (Objects.isNull(planId)) {
            log.error("商品未能匹配到上新计划，SKU编码:{}，名称:{}", skuCode,
                    sheetRow.getName());
            return;
        }
        final List<ChangePropertyObj> changes = new ArrayList<>();
        final NewGoods newGoods = newGoodsService.lambdaQuery()
                .eq(NewGoods::getSkuCode, skuCode).oneOpt().orElseGet(
                        NewGoods::new);
        final Optional<ItemSku> itemSkuOptional = Optional.ofNullable(
                itemSkuGateway.getBySkuCode(skuCode));
        if (NumberUtil.isZeroOrNull(newGoods.getCreatedAt())) {
            newGoods.setCreatedAt(currentTime);
        }
        newGoods.setUpdatedAt(currentTime);
        newGoods.setSkuCode(skuCode);
        if (!Objects.equals(newGoods.getName(), sheetRow.getName())) {
            changes.add(new ChangePropertyObj("name", newGoods.getName(),
                    sheetRow.getName()));
            newGoods.setName(sheetRow.getName());
        }
        final Long itemId = itemSkuOptional.map(ItemSku::getItemId).orElse(0L);
        if (itemId == 0) {
            log.error("SKU编码未匹配，SKU编码:{}，名称:{}", skuCode,
                    sheetRow.getName());
            return;
        }
        newGoods.setItemId(itemId);
        final StaffInfo principalInfo = userGateway.queryStaffInfoByNickname(
                sheetRow.getPrincipal());
        if (principalInfo == null) {
            log.error("产品负责人信息异常，未查询到员工信息:{}，SKU编码:{}，名称:{}",
                    sheetRow.getPrincipal(),
                    skuCode, sheetRow.getName());
            return;
        } else {
            boolean principalChange = true;
            final Long principalId = newGoods.getPrincipalId();
            if (NumberUtil.isZeroOrNull(principalId)) {
                newGoods.setPrincipalId(principalInfo.getUserId());
            } else if (!principalId.equals(principalInfo.getUserId())) {
                log.info("新品商品产品负责人变更为{}，前负责人ID:{}，SKU编码:{}，名称:{}",
                        sheetRow.getPrincipal(),
                        principalId, skuCode, sheetRow.getName());
                newGoods.setPrincipalId(principalInfo.getUserId());
            } else {
                principalChange = false;
            }
            if (principalChange) {
                changes.add(new ChangePropertyObj("principalId", principalId,
                        principalInfo.getUserId()));
            }
        }
        if (Objects.isNull(newGoods.getId())) {
            newGoodsService.save(newGoods);
            log.info("新品商品新增，SKU编码:{}，名称:{}", skuCode, sheetRow.getName());
            operateLogDomainService.addOperatorLog(0L,
                    OperateLogTarget.NEW_GOODS,
                    newGoods.getId(), "导入新品商品");
        } else if (!changes.isEmpty()) {
            newGoodsService.updateById(newGoods);
            log.info("新品商品更新，SKU编码:{}，名称:{}", skuCode, sheetRow.getName());
            operateLogDomainService.addOperatorLog(0L,
                    OperateLogTarget.NEW_GOODS,
                    newGoods.getId(), "导入新品商品，更新记录", changes);
        } else {
            log.info("新品商品无变更，SKU编码:{}，名称:{}", skuCode, sheetRow.getName());
        }
        final StaffInfo buyerInfo = userGateway.queryStaffInfoByNickname(
                sheetRow.getBuyer());
        final Item item = itemGateway.getItem(newGoods.getItemId());
        if (buyerInfo == null) {
            log.error("采购员信息异常，未查询到员工信息:{} SKU编码:{}，名称:{}",
                    sheetRow.getBuyer(),
                    skuCode, sheetRow.getName());
        } else {
            final Long buyerId = buyerGateway.saveOrUpdateBuyer(
                    buyerInfo.getUserId(),
                    buyerInfo.getNickname());
            final Optional<ItemProcurement> itemProcurementOptional = itemProcurementService.lambdaQuery()
                    .eq(ItemProcurement::getItemId, itemId).oneOpt();
            itemProcurementOptional.ifPresent(itemProcurement -> {
                final Long buyerIdOld = itemProcurement.getBuyerId();
                if (!Objects.equals(buyerIdOld, buyerId) && itemProcurementService.lambdaUpdate()
                        .set(ItemProcurement::getBuyerId, buyerId)
                        .eq(ItemProcurement::getItemId, newGoods.getItemId()).update()) {
                    itemProcurement.setBuyerId(buyerId);
                    log.info("商品产品采购员变更为{}，之前的采购员ID为:{}，变更后的采购员ID为:{}，商品编码:{}，SKU编码:{}，名称:{}",
                            sheetRow.getBuyer(), buyerIdOld,
                            buyerId, item.getCode(),
                            skuCode, sheetRow.getName());
                    operateLogDomainService.addOperatorLog(0L, OperateLogTarget.ITEM, itemId,
                            "新品商品导入，变更采购员",
                            Collections.singletonList(
                                    new ChangePropertyObj("buyerId", buyerIdOld,
                                            buyerId)));
                }
            });
        }

        if (itemLaunchPlanService.ensurePlanItemRef(planId, item.getId(),
                item.getCode(),
                currentTime)) {
            log.info("商品产品关联到上新计划，上新计划ID:{}，SKU编码:{}，名称:{}，商品编码:{}", planId,
                    skuCode, sheetRow.getName(), item.getCode());
        }
    }
}
