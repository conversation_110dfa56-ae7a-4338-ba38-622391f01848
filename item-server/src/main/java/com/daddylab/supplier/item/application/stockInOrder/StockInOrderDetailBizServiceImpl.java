package com.daddylab.supplier.item.application.stockInOrder;

import com.daddylab.supplier.item.domain.stockInOrder.gateway.StockInOrderDetailGateway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 采购入库明细服务实现
 * <AUTHOR>
 * @date 2022/3/24 16:52
 **/
@Service
public class StockInOrderDetailBizServiceImpl implements StockInOrderDetailBizService {
    @Autowired
    private StockInOrderDetailGateway stockInOrderDetailGateway;
}
