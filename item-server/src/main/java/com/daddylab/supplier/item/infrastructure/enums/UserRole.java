package com.daddylab.supplier.item.infrastructure.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/21 4:04 下午
 * @description
 */
@Getter
public enum UserRole {

    GOD(0, "上帝。不解释"),

    ORDER(1, "订单者。新建商品基本信息;编辑商品基本信息;查看商品基本信息，商品运营信息"),

    PURCHASE(2, "采购者，新建商品基本信息，商品价格信息;编辑商品基本信息，商品价格信息;查看商品基本信息，商品价格信息，商品运营信息"),

    RUNNER(3, "运营者,新建商品基本信息;编辑商品运营信息;查看商品基本信息，商品运营信息"),

    PRICE(4, "价格支配者，查看商品价格信息");

    private Integer type;
    private String des;

    UserRole(Integer type, String des) {
        this.type = type;
        this.des = des;
    }
}
