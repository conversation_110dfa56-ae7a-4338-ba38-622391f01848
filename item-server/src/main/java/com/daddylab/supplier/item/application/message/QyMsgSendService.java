package com.daddylab.supplier.item.application.message;

import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MsgTemplateCode;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/5/14
 */
public interface QyMsgSendService {
    long send(RemindType remindType, MsgTemplateCode code, Map<String, Object> variables);
}
