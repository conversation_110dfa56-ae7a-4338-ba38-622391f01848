package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderDeliveryTrace;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderDeliveryTraceMapper;

/**
 * <p>
 * 订单发货跟踪 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
public interface IOrderDeliveryTraceService extends IDaddylabServicePlus<OrderDeliveryTrace, OrderDeliveryTraceMapper> {

}
