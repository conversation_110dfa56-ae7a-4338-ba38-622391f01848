package com.daddylab.supplier.item.domain.brand.entity;

import com.daddylab.supplier.item.common.enums.EnableStatusEnum;
import com.daddylab.supplier.item.infrastructure.domain.Entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.PropertyName;
import org.javers.core.metamodel.annotation.TypeName;

import java.util.List;

/**
 * <p>
 * 品牌
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TypeName("品牌")
public class BrandEntity extends Entity {
    private static final long serialVersionUID = -4127392334151927339L;
    /**
     * 品牌编号
     */
    @PropertyName("品牌编号")
    @DiffIgnore
    private String sn;

    /**
     * 品牌名称
     */
    @PropertyName("品牌名称")
    private String name;

    /**
     * 品牌LOGO
     */
    @PropertyName("LOGO")
    private String logo;

    /**
     * 所属供应商
     */
    @PropertyName("所属供应商")
    private List<Long> providerIds;

    /**
     * 状态 0:停用 1:正常
     */
    @PropertyName("状态")
    private EnableStatusEnum status;

    /**
     * 业务线
     */
    @PropertyName("合作模式")
    private List<Integer> businessLine;


}
