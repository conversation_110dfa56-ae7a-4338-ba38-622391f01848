/*
package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.VirtualWarehouseInventoryGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.VirtualWarehouseInventoryGoodsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IVirtualWarehouseInventoryGoodsService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

*/
/**
 * <p>
 * 虚拟仓库存信息（商品纬度） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 *//*

@Service
public class VirtualWarehouseInventoryGoodsServiceImpl extends DaddyServiceImpl<VirtualWarehouseInventoryGoodsMapper, VirtualWarehouseInventoryGoods> implements IVirtualWarehouseInventoryGoodsService {

    @Override
    public List<VirtualWarehouseInventoryGoods> selectByVirtualWarehouseIdsAndSkuNos(List<Long> virtualWarehouseIds, List<String> skuCodes) {
        if (virtualWarehouseIds == null || virtualWarehouseIds.isEmpty() || skuCodes == null || skuCodes.isEmpty()) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(VirtualWarehouseInventoryGoods::getVirtualWarehouseId,
                        virtualWarehouseIds)
                .in(VirtualWarehouseInventoryGoods::getSkuNo,
                        skuCodes)
                .list();
    }
}
*/
