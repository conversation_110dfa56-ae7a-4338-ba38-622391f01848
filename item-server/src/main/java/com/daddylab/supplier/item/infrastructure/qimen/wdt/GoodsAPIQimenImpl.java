package com.daddylab.supplier.item.infrastructure.qimen.wdt;

import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.api.goods.GoodsAPI;
import com.daddylab.mall.wdtsdk.api.goods.dto.GoodsClassSearchRequest;
import com.daddylab.mall.wdtsdk.api.goods.dto.GoodsClassSearchResponse;
import com.daddylab.mall.wdtsdk.api.goods.dto.GoodsDto;
import com.daddylab.mall.wdtsdk.api.goods.dto.GoodsSearchRequest;
import com.daddylab.mall.wdtsdk.api.goods.dto.GoodsSearchResponse;
import com.daddylab.mall.wdtsdk.api.goods.dto.GoodsSpecDto;
import com.daddylab.mall.wdtsdk.api.goods.dto.PlatformGoodsPushParams;
import com.daddylab.mall.wdtsdk.api.goods.dto.PlatformGoodsSearchRequest;
import com.daddylab.mall.wdtsdk.api.goods.dto.PlatformGoodsSearchResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.JsonUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.daddylab.supplier.item.infrastructure.qimen.QimenConfig;
import com.daddylab.supplier.item.infrastructure.qimen.QimenResponseBody;
import com.daddylab.supplier.item.infrastructure.qimen.QimenWdtGoodsAPI;
import com.google.common.reflect.TypeToken;
import com.qimencloud.api.QimenCloudClient;
import com.qimencloud.api.scene3ldsmu02o9.request.WdtGoodsApigoodsSearchRequest;
import com.qimencloud.api.scene3ldsmu02o9.response.WdtGoodsApigoodsSearchResponse;
import com.taobao.api.ApiException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/17
 */
public class GoodsAPIQimenImpl extends WdtAPIQimenImplBase implements GoodsAPI {

    QimenWdtGoodsAPI qimenWdtGoodsAPI;

    public GoodsAPIQimenImpl(QimenCloudClient qimenClient,
            QimenConfig qimenConfig,
            WdtConfig wdtConfig,
            int configIndex
    ) {
        super(qimenClient, qimenConfig, wdtConfig, configIndex);
    }

    @Override
    protected void initApiInstance(QimenCloudClient qimenClient, QimenConfig qimenConfig,
            WdtConfig wdtConfig, int configIndex) {
        qimenWdtGoodsAPI = new QimenWdtGoodsAPI(qimenClient, qimenConfig, wdtConfig, configIndex);
    }

    @Override
    public void push(GoodsDto goodsDto, List<GoodsSpecDto> list) throws WdtErpException {
        throw new UnsupportedOperationException("暂未实现");
    }

    @Override
    public Integer platformGoodsPush(PlatformGoodsPushParams platformGoodsPushParams)
            throws WdtErpException {
        throw new UnsupportedOperationException("暂未实现");
    }

    @Override
    public GoodsSearchResponse search(GoodsSearchRequest goodsSearchRequest, Pager pager)
            throws WdtErpException {
        throw new UnsupportedOperationException("暂未实现");
    }

    @Override
    public PlatformGoodsSearchResponse platformGoodsSearch(
            PlatformGoodsSearchRequest platformGoodsSearchRequest,
            Pager pager) throws WdtErpException {
        try {
            final WdtGoodsApigoodsSearchRequest.Params params = new WdtGoodsApigoodsSearchRequest.Params();
            params.setStartTime(platformGoodsSearchRequest.getStartTime());
            params.setEndTime(platformGoodsSearchRequest.getEndTime());
            params.setGoodsId(platformGoodsSearchRequest.getGoodsId());
            params.setShopNo(platformGoodsSearchRequest.getShopNo());
            params.setSpecId(platformGoodsSearchRequest.getSpecId());

            final WdtGoodsApigoodsSearchResponse wdtGoodsApigoodsSearchResponse = qimenWdtGoodsAPI
                    .platformGoodsSearch(params, pager.getPageNo() + 1, pager.getPageSize());
            checkError(wdtGoodsApigoodsSearchResponse);
            //noinspection UnstableApiUsage
            final QimenResponseBody<PlatformGoodsSearchResponse> qimenResponseBody = JsonUtil
                    .fromJson(wdtGoodsApigoodsSearchResponse.getBody(),
                            new TypeToken<QimenResponseBody<PlatformGoodsSearchResponse>>() {
                            }.getType());
            return qimenResponseBody.getResponse().getData();
        } catch (ApiException exception) {
            throw transformException(exception);
        }
    }

    @Override
    public GoodsClassSearchResponse goodsClassSearch(
            GoodsClassSearchRequest goodsClassSearchRequest,
            Pager pager) throws WdtErpException {
        throw new UnsupportedOperationException("暂未实现");
    }
}
