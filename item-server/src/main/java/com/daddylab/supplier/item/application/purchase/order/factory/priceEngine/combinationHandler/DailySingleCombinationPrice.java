package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.combinationHandler;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.BaseProcess;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.PriceProcessor;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseSingleSkuCombinationPrice;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Semaphore;

/**
 * 日常价-单sku纬度，任意数量组合价
 * 只处理单品
 *
 * <AUTHOR> up
 * @date 2022年10月24日 10:49 AM
 */
@Service
@Slf4j
public class DailySingleCombinationPrice extends BaseProcess implements PriceProcessor {


    private final Semaphore semaphore = new Semaphore(4);


    /**
     * @param timeBO 目标月份 202208
     * @return
     */
    @Override
    public Boolean doPriceProcess(TimeBO timeBO) {
        log.info("DailySingleCombinationPrice doPriceProcess start");

        List<PurchaseSingleSkuCombinationPrice> list = getSingleCombinationPriceByType(timeBO, 1);
        if (CollUtil.isEmpty(list)) {
            log.info("DailySingleCombinationPrice doPriceProcess finish.purchase price is empty");
            return true;
        }

        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        list.forEach(entity -> ThreadUtil.execute(PoolEnum.PURCHASE_POOL, () -> {
            try {
                semaphore.acquire();
                log.info("DailySingleCombinationPrice doPriceProcess start skuCode:{} ", entity.getCode());
                singleCombinationPriceHandler(entity, timeBO.getOperateMonth(),1);
                log.info("DailySingleCombinationPrice doPriceProcess finish skuCode:{} ", entity.getCode());
            } catch (Exception e) {
                log.error("DailySingleCombinationPrice doPriceProcess fail price:{}", JsonUtil.toJson(entity), e);
            } finally {
                semaphore.release();
                countDownLatch.countDown();
            }
        }));

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("[{}] DailySingleCombinationPrice doPriceProcess await InterruptedException", timeBO.getOperateMonth(), e);
        }

        log.info("DailySingleCombinationPrice doPriceProcess finish");
        return true;
    }


}
