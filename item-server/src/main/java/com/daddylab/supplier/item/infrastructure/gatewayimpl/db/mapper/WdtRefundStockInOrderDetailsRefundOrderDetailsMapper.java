package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundStockInOrderDetailsRefundOrderDetails;

/**
 * <p>
 * 旺店通退货入库单明细与退换单明细的关联关系 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
public interface WdtRefundStockInOrderDetailsRefundOrderDetailsMapper extends DaddyBaseMapper<WdtRefundStockInOrderDetailsRefundOrderDetails> {

}
