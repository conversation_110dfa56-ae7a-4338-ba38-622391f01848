package com.daddylab.supplier.item.controller.drawer;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemApprovalAdviceType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel("商品审核意见新增命令模型")
public class AddCmd {

    @ApiModelProperty("商品ID")
    @NotNull(message = "商品ID不能为空")
    private Long itemId;

    @ApiModelProperty("意见类型")
    @NotNull(message = "意见类型不能为空")
    private ItemApprovalAdviceType type;

    @ApiModelProperty("审批意见")
    @NotBlank(message = "审批意见不能为空")
    private String content;
}
