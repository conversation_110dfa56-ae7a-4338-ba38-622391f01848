package com.daddylab.supplier.item.domain.alert;

import lombok.Data;

import java.util.List;

/**
 * 警报器接口
 */
public interface IAlerter {

    boolean text(String message);

    boolean markdown(String message);

    enum MsgType {
        TEXT,
        MARKDOWN
    }

    interface Filter {
        List<Message> filter(IAlerter alert, List<Message> message);
    }

    @Data
    class Message {
        final MsgType msgType;

        final String content;
    }
}
