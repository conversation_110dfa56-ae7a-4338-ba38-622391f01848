package com.daddylab.supplier.item.domain.drawer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.Positive;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.javers.core.metamodel.annotation.Value;

/**
 * Class  ItemDrawerForm
 *
 * @Date 2022/5/31下午4:18
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ItemDrawerSkuImage", description = "商品抽屉规格图片")
@Value
@EqualsAndHashCode(of = {"skuId", "url"})
public class ItemDrawerSkuImage implements Serializable {

    private static final long serialVersionUID = -9140120460642242057L;

    @ApiModelProperty(value = "SKU ID")
    @Positive
    private Long skuId;

    @ApiModelProperty(value = "SKU编码")
    private String skuCode;

    @ApiModelProperty(value = "规格")
    private String specifications;

    @Length(max = 500, message = "图片地址不能大于500个字符")
    @ApiModelProperty(value = "图片地址")
    private String url;

}
