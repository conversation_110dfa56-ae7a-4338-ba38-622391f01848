package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.daddylab.supplier.item.common.trans.CommaSerialTransMapper;
import com.daddylab.supplier.item.controller.provider.dto.BankAccountVO;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ProviderStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ProviderType;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.Id;
import org.javers.core.metamodel.annotation.PropertyName;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Provider extends Entity {

  @Id private Long id;

  /** 供应商名称 */
  @PropertyName("供应商名称")
  private String name;

  /** 联系人 */
  @PropertyName("联系人")
  private String contact;

  /** 联系方式 */
  @PropertyName("联系方式")
  private String contactMobile;

  /** 统一社会信用代码 */
  @PropertyName("统一社会信用代码")
  private String unifySocialCreditCodes;

  /** 联系地址 省 */
  @PropertyName("省")
  @DiffIgnore
  private String provinceCode;

  /** 联系地址 市 */
  @PropertyName("市")
  @DiffIgnore
  private String cityCode;

  /** 联系地址 区/县 */
  @PropertyName("区/县")
  @DiffIgnore
  private String areaCode;

  /** 联系地址 区/县 */
  @TableField(exist = false)
  @PropertyName("省市区地址")
  private String codeStr;

  /** 联系地址 详细地址 */
  @PropertyName("详细地址")
  private String address;

  /** 状态 1:合作 2:停用 */
  @PropertyName("状态")
  private ProviderStatus status;

  /** 供应商类型 1:自营 2:代发 */
  @PropertyName("供应商类型")
  private ProviderType type;

  /** 关联合作伙伴系统中的供应商ID，没有则为0 */
  @PropertyName("关联P统供应商")
  @DiffIgnore
  private Long partnerProviderId;

  /** 删除时间 */
  @DiffIgnore private Long deletedAt;

  /** 金蝶系统供应商ID */
  @DiffIgnore private String kingDeeId;

  /** 金蝶系统供应商编号 */
  @DiffIgnore private String kingDeeNo;

  @PropertyName("此供应商是否来自于合作伙伴系统")
  @TableField(exist = false)
  @DiffIgnore
  private Boolean isFromPartner;

  /** 银行账户 */
  @PropertyName("银行账户")
  @TableField(exist = false)
  @DiffIgnore
  private List<BankAccountVO> bankAccountVOList;

  /** 供应商编号 */
  @DiffIgnore
  @PropertyName("供应商编号")
  private String providerNo;

  @PropertyName("负责人")
  private Long mainChargerUserId;

  @PropertyName("次要负责人")
  private Long secondChargerUserId;

  /** 商家后台。shopId; */
  @DiffIgnore private String mallShopId;

  /** 合作模式（业务线） */
  @PropertyName("业务线")
  private String businessLine;

  public String getBusinessLine() {
    return DivisionLevelValueEnum.filterCorpType(businessLine);
  }

  @JsonProperty("businessLineList")
  public List<Integer> getBusinessLineList() {
    return businessLine != null
        ? CommaSerialTransMapper.INSTANCE.strToIntegerList(businessLine).stream()
            .filter(DivisionLevelValueEnum::isCorpType)
            .collect(Collectors.toList())
        : null;
  }

  @DiffIgnore
  @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
  private Boolean isBusinessLine0;

  @DiffIgnore
  @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
  private Boolean isBusinessLine1;

  @DiffIgnore
  @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
  private Boolean isBusinessLine2;

  @DiffIgnore
  @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
  private Boolean isBusinessLine3;

  /** 是否黑名单 */
  @TableField(exist = false)
  @DiffIgnore
  private Integer isBlacklist;

  /** 是否已删除 */
  @TableLogic(delval = "id")
  @DiffIgnore protected Integer isDel;
}
