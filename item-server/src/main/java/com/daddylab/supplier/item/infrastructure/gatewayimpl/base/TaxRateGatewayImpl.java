package com.daddylab.supplier.item.infrastructure.gatewayimpl.base;

import com.daddylab.supplier.item.domain.base.TaxRateGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.TaxRate;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ITaxRateService;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> up
 * @date 2022/5/25 9:45 下午
 */
@Service
public class TaxRateGatewayImpl implements TaxRateGateway {

    public static final int CACHE_SECONDS = 60 * 30;
    public static final int CACHE_MAXIMUM_SIZE = 100;

    @Autowired
    ITaxRateService iTaxRateService;

    LoadingCache<BigDecimal, Optional<TaxRate>> cache = Caffeine.newBuilder()
            .expireAfterAccess(CACHE_SECONDS, TimeUnit.SECONDS)
            .maximumSize(CACHE_MAXIMUM_SIZE)
            .build(this::getTaxRate0);

    public Optional<TaxRate> getTaxRate0(BigDecimal val) {
        cache.invalidate(val);
        return iTaxRateService.lambdaQuery().eq(TaxRate::getValue, val).select().oneOpt();
    }

    @Override
    public Optional<TaxRate> kingDeeNo(BigDecimal val) {
        return cache.get(val);


    }

}
