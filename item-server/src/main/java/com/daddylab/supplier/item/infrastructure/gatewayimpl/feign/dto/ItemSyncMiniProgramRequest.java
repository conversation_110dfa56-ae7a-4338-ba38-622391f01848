package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import com.daddylab.supplier.item.controller.item.dto.ItemSyncMiniProgramParam;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.itemSyncMiniProgram.ErpItemExtend;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/9/19 13:47
 * @Description: 商品同步到小程序 --- 参数
 */
@Data
public class ItemSyncMiniProgramRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 小程序商品ids
     */
    private List<Long> miniItemIds;

    /**
     * 卖家类目ID
     */
    private Long categoryId;

    private List<String> categoryList;

    /**
     * 属性 组成规格的所有属性列表
     */
    private List<ErpItemAttr> attrList;

    /**
     * 小程序标题
     */
    private String name;

    /**
     * 商品编码
     */
    private String itemNo;

    /**
     * 首页文案
     */
    private String saleKey;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 商品主图
     */
    private String images;

    /**
     * 主图视频
     */
    private String video;

    /**
     * 规格
     */
    private List<ErpItemSku> skuList;

    /**
     * 发货类型
     */
    private Integer shipmentType;

    /**
     * 详情页
     */
    private String detailHtml;

    /**
     * P系统商品ID
     */
    private Long qcItemId;

    /**
     * 是否覆盖式更新
     */
    private boolean override;

    /**
     * 商家后台店铺ID
     */
    private Long mallShopId;

    /**
     * 小程序是否展示扩展信息 0否 1是
     */
    private Integer isItemExtendShow;

    /**
     * 商品扩展信息
     */
    private ErpItemExtend erpItemExtend;

    @Data
    public static class ErpItemAttr implements Serializable {
        private static final long serialVersionUID = 1L;

        public ErpItemAttr() {
        }

        public ErpItemAttr(String key, List<AttrValue> values) {
            this.key = key;
            this.values = values;
        }

        /**
         * 属性名
         */
        private String key;

        /**
         * 属性值
         */
        private List<AttrValue> values;

        @Data
        public static class AttrValue implements Serializable {
            private static final long serialVersionUID = 1L;

            public AttrValue() {
            }

            public AttrValue(String name, String image) {
                this.name = name;
                this.image = image;
            }

            private String name;
            private String image;
        }
    }

    @Data
    public static class ErpItemSku implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * SKU编码
         */
        private String skuNo;

        /**
         * 产品日销价
         */
        private BigDecimal salePrice;

        /**
         * 产品划线价
         */
        private BigDecimal advicePrice;

        /**
         * SKU属性
         */
        private List<ErpItemSkuAttr> attrList;
    }

    @Data
    public static class ErpItemSkuAttr implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 属性名
         */
        private String key;

        /**
         * 属性值
         */
        private String value;
        /**
         * @deprecated 不需要了
         * 属性图片
         */
        private String image;
    }

    @JsonIgnore
    Integer skuNum;
    @JsonIgnore
    Integer itemNum;
    @JsonIgnore
    Integer syncType;
    @JsonIgnore
    ItemSyncMiniProgramParam param;
    @JsonIgnore private List<String> errors = new ArrayList<>();
}
