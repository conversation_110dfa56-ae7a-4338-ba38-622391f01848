package com.daddylab.supplier.item.application.platformItemSkuInventory;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;

/**
 * <AUTHOR>
 * @since 2024/4/8
 */
public interface PlatformServiceFactory<T> {

    T getService(String shopNo);

    T getService(ShopAuthorization shopAuthorization);

    Platform platform();

}
