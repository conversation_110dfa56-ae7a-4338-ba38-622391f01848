package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherPay;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OtherPayMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOtherPayService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 其他应付 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-24
 */
@Service
public class OtherPayServiceImpl extends DaddyServiceImpl<OtherPayMapper, OtherPay> implements IOtherPayService {

}
