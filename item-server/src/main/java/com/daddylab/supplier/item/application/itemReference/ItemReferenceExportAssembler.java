package com.daddylab.supplier.item.application.itemReference;

import com.daddylab.supplier.item.application.bizLevelDivision.BizLevelDivisionConvert;
import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceSku;
import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceSkuExportDO;
import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceSpu;
import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceSpuExportDO;
import com.daddylab.supplier.item.common.trans.CommaSerialTransMapper;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BusinessLine;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemDelivery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemStatus;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/9/14
 */
@Mapper(imports = {ItemDelivery.class, ItemStatus.class, IEnum.class, CommaSerialTransMapper.class, BusinessLine.class, BizLevelDivisionConvert.class})
public interface ItemReferenceExportAssembler {

    ItemReferenceExportAssembler INST = Mappers.getMapper(ItemReferenceExportAssembler.class);

    @Mapping(target = "delivery", expression = "java(ItemDelivery.valueStrToDesc(sku.getDelivery()))")
    @Mapping(target = "itemStatus", expression = "java(IEnum.getEnumOptByValue(ItemStatus.class, sku.getItemStatus()).map(ItemStatus::getDesc).orElse(\"\"))")
//    @Mapping(target = "businessLine", expression = "java(IEnum.getEnumOptByValue(BusinessLine.class, sku.getBusinessLine()).map(IEnum::getDesc).orElse(\"\"))")
    @Mapping(target = "platformCommission", expression = "java(java.util.Objects.isNull(sku.getPlatformCommission()) ? \"\" : sku.getPlatformCommission().toString())")
    @Mapping(target = "corpBizType", expression = "java(BizLevelDivisionConvert.INSTANCE.corpBizTypeListToDescStr(sku.getCorpBizType()))")
    ItemReferenceSkuExportDO skuToExportDO(ItemReferenceSku sku);


    List<ItemReferenceSkuExportDO> skuListToExportDOList(List<ItemReferenceSku> sku);

    @Mapping(target = "delivery", expression = "java(ItemDelivery.valueStrToDesc(sku.getDelivery()))")
    @Mapping(target = "itemStatus", expression = "java(IEnum.getEnumOptByValue(ItemStatus.class, sku.getItemStatus()).map(ItemStatus::getDesc).orElse(\"\"))")
    @Mapping(target = "businessLine", expression = "java(IEnum.getEnumOptByValue(BusinessLine.class, sku.getBusinessLine()).map(IEnum::getDesc).orElse(\"\"))")
    @Mapping(target = "corpBizType", expression = "java(BizLevelDivisionConvert.INSTANCE.corpBizTypeListToDescStr(sku.getCorpBizType()))")
    ItemReferenceSpuExportDO spuToExportDO(ItemReferenceSpu sku);

    List<ItemReferenceSpuExportDO> spuListToExportDOList(List<ItemReferenceSpu> sku);

}
