package com.daddylab.supplier.item.controller.item.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/26 15:25
 * @Description: 新品库关联的 sku 信息
 */
@Data
public class SkuWithNewGoodsDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private String skuCode;
    private BigDecimal dailyPrice;
    private BigDecimal activePrice;
    private BigDecimal linePrice;
}
