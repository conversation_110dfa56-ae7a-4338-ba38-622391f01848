package com.daddylab.supplier.item.infrastructure.submail.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SubMailConfig {
    @Bean
    @ConfigurationProperties(prefix = "submail.sms")
    public MessageConfig messageConfig() {
        return new MessageConfig();
    }
}
