package com.daddylab.supplier.item.application.afterSaleLogistics.monitor;

import com.daddylab.supplier.item.application.afterSaleLogistics.domain.LogisticsTraceData;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.ErpLogisticsStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsTrace;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/30
 */
@Data
public class ProcessContext {
  private Long timestamp;
  private OrderLogisticsTrace trace;
  private LogisticsTraceData logisticsTraceData;
  private ErpLogisticsStatus revisedLogisticsStatus;

  public ProcessContext(Long timestamp, OrderLogisticsTrace trace) {
    this.timestamp = timestamp;
    this.trace = trace;
  }

  public ProcessContext(
      Long timestamp,
      OrderLogisticsTrace trace,
      LogisticsTraceData logisticsTraceData,
      ErpLogisticsStatus revisedLogisticsStatus) {
    this.timestamp = timestamp;
    this.trace = trace;
    this.logisticsTraceData = logisticsTraceData;
    this.revisedLogisticsStatus = revisedLogisticsStatus;
  }
}
