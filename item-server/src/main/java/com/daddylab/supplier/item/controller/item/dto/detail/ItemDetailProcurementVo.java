package com.daddylab.supplier.item.controller.item.dto.detail;

import com.daddylab.supplier.item.controller.item.dto.ItemExpressVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/20 6:43 下午
 * @description
 */
@Data
@ApiModel("采购，物流信息返回封装")
public class ItemDetailProcurementVo {

    @ApiModelProperty("物流采购id")
    private Long id;

    @ApiModelProperty("供应商id")
    private Long providerId;

    @ApiModelProperty("供应商名称")
    private String providerName;

    @ApiModelProperty("采购员id")
    private Long buyerId;

//    @ApiModelProperty("是否为赠品")
//    boolean isGift;

    @ApiModelProperty("发货渠道")
    private String delivery;

    @ApiModelProperty("模板信息")
    List<ItemExpressVo> expressDtoList;

    @ApiModelProperty("基本单位ID")
    private Long baseUnitId;

    @ApiModelProperty("基本单位")
    private String baseUnit;

    @ApiModelProperty("税率")
    private BigDecimal rate;

    @ApiModelProperty("税率")
    private BigDecimal purchaseTaxRate;

    @ApiModelProperty("税率编码")
    private String taxRateCode;

    @ApiModelProperty("仓库编号")
    private String warehouseNo;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

}
