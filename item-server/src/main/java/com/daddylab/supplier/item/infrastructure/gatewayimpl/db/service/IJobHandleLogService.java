package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.JobHandle;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.JobHandleLog;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
public interface IJobHandleLogService extends IDaddyService<JobHandleLog> {

    void log(JobHandle jobHandle, String msg);
}
