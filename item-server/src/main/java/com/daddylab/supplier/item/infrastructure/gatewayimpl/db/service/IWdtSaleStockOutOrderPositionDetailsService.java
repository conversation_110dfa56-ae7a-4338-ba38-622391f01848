package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrderPositionDetails;

/**
 * <p>
 * 旺店通销售出库单出库货位明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
public interface IWdtSaleStockOutOrderPositionDetailsService extends IDaddyService<WdtSaleStockOutOrderPositionDetails> {

}
