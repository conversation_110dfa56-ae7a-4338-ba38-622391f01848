package com.daddylab.supplier.item.domain.shop.entity;


import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.shop.enums.ShopStatus;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DataSource;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.PropertyName;

/**
 * <p>
 * 店铺
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ShopEntity extends Entity {

    /**
     * 店铺编号
     */
    @PropertyName("店铺编号")
    private String sn;

    /**
     * 店铺名称
     */
    @PropertyName("店铺名称")
    private String name;

    /**
     * 店铺账号
     */
    @PropertyName("店铺账号")
    private String account;

    /**
     * 店铺链接
     */
    @PropertyName("店铺链接")
    private String link;

    /**
     * 店铺LOGO
     */
    @PropertyName("店铺LOGO")
    private String logo;

    /**
     * 平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城
     */
    @PropertyName("平台")
    private Platform platform;

    /**
     * 状态 0:关闭 1:准备中 2:营业
     */
    @PropertyName("状态")
    private ShopStatus status;

    /**
     * 店铺负责人
     */
    @PropertyName("店铺负责人")
    private List<Long> principals;

    /**
     * 金蝶ID
     */
    @DiffIgnore
    private String kingDeeId;

    @PropertyName("数据来源")
    private DataSource source;

    /**
     * 合作方，冗余字段，方便查询
     */
    @PropertyName("合作模式")
    private List<Integer> businessLine;

    @ApiModelProperty("运营模式")
    private List<Integer> runningMode;

    /**
     * 是否开启自动分配库存 0否，1是
     */
//    @PropertyName("是否开启自动分配库存")
//    private Integer autoAllocateInventory;
//
//    @PropertyName("设置自动同步库存频次(单位秒)")
//    private Integer syncFrequency;
//
//    @PropertyName("安全库存设置")
//    private Integer safetyThreshold;
//
//    @PropertyName("实体仓库库存明细列表展示")
//    private List<ShopWarehouseInventoryVo> shopWarehouseInventoryVos;
//
//    @PropertyName("库存模式")
//    private InventoryMode inventoryMode;

    @PropertyName("店铺主体公司")
    private String mainCompany;

}
