package com.daddylab.supplier.item.controller.category.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/27 4:21 下午
 * @description
 */
@Data
@ApiModel("品类返回实体")
public class CategoryVo implements Serializable {


    private static final long serialVersionUID = -709460237505415468L;
    @ApiModelProperty(value = "品类id", example = "1")
    private Long id;

    @ApiModelProperty("品类名称")
    private String name;

    @ApiModelProperty(value = "此品类根品类id", example = "1")
    private Long rootId;

    @ApiModelProperty(value = "此品类父品类id", example = "1")
    private Long parentId;

    @ApiModelProperty(value = "此品类层级", example = "1")
    private Integer level;

    @ApiModelProperty(value = "是否是最后一级")
    private Boolean isLastLevel;

    @ApiModelProperty(value = "是否存在属性")
    private Boolean isHaveAttr;

    @ApiModelProperty(value = "品类路径")
    private String path;
}
