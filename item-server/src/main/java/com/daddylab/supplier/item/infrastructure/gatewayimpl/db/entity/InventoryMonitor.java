package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 库存警戒
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class InventoryMonitor implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 实体仓仓库编号
     */
    private String warehouseNo;

    /**
     * SKU编码（为空表明在仓库级别预警）
     */
    private String skuNo;

    /**
     * 阈值类型 1:按照库存绝对值 2:按照库存百分比
     */
    private Integer thresholdType;

    /**
     * 警戒库存
     */
    private Integer alertThreshold;

    /**
     * 状态 0:禁用 1:启用
     */
    private Integer status;

    /**
     * 是否是仓库维度的库存警戒
     */
    public boolean isWarehouseDimension() {
        return skuNo == null || skuNo.isEmpty();
    }


}
