package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.S13OrderNew;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.S13OrderNewMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IS13OrderNewService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 小红书订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Service
public class S13OrderNewServiceImpl extends DaddyServiceImpl<S13OrderNewMapper, S13OrderNew> implements IS13OrderNewService {

}
