package com.daddylab.supplier.item.domain.exportTask.dto.combinationItem;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/13 1:59 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ColumnWidth(40)
public class ComposerSkuWithSkuPriceSheet extends ExportSheet {

    @ExcelProperty(value = "组合商品名称",index = 0)
    protected String combinationName;

    @ExcelProperty(value = "组合编码",index = 1)
    protected String combinationCode;

    @ExcelProperty(value = "条码",index = 2)
    protected String combinationBarCode;

    @ExcelProperty(value = "重量",index = 3)
    protected BigDecimal combinationWeight;

    @ExcelProperty(value = "合作模式", index = 4)
    @ExcelIgnore
    protected String businessLineLabel;

    @ExcelIgnore
    protected String businessLine;

    @ExcelProperty(value = "单品商品编码",index = 5)
    protected String itemCode;

    @ExcelProperty(value = "单品商品SKU",index = 6)
    protected String skuCode;

    @ExcelProperty(value = "单品数量",index = 7)
    protected String count;

    @ExcelProperty(value = "单品采购成本",index = 8)
    protected BigDecimal skuCostPrice;

    @ExcelProperty(value = "单品日常销售价",index = 9)
    protected BigDecimal skuSalesPrice;

    @ExcelProperty(value = "采购成本占比",index = 10)
    protected BigDecimal costProportion;

    @ExcelProperty(value = "销售占比",index = 11)
    protected BigDecimal saleProportion;

    @ExcelProperty(value = "单品库存",index = 12)
    protected String stockCount;

    /**
     * 平台佣金
     */
    @ExcelProperty(value = "平台佣金", index = 15)
    private BigDecimal platformCommission;

    /**
     * 合同销售价
     */
    @ExcelProperty(value = "合同销售价", index = 16)
    private BigDecimal contractSalePrice;

    /**
     * 平台佣金
     */
    @ExcelProperty(value = "单品平台佣金", index = 17)
    private BigDecimal skuPlatformCommission;

    /**
     * 合同销售价
     */
    @ExcelProperty(value = "单品合同销售价", index = 18)
    private BigDecimal skuContractSalePrice;

    @ExcelProperty(value = "合作方", index = 19)
    private String corpType;

    @ExcelProperty(value = "业务类型", index = 20)
    private String bizType;

}
