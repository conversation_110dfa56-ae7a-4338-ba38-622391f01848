package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundStockInOrderDetails;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundStockInOrderDetailsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtRefundStockInOrderDetailsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 旺店通退货入库单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Service
public class WdtRefundStockInOrderDetailsServiceImpl extends DaddyServiceImpl<WdtRefundStockInOrderDetailsMapper, WdtRefundStockInOrderDetails> implements IWdtRefundStockInOrderDetailsService {

}
