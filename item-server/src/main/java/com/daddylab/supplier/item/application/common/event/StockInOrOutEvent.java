package com.daddylab.supplier.item.application.common.event;

import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import lombok.Data;

@Data
public class StockInOrOutEvent{

    /**
     *  应付类型。 {@link PurchaseTypeEnum}
     */
    private Integer type;

    /**
     * 关联单据(指采购入库单ID/采退出库单ID)
     */
    private Long relatedOrderId;

    /**
     * 关联的入库单/出库单/其他应付的采购员
     */
    private Long buyerId;

    /**
     * 创建人
     */
    private Long createdUid;

    public static StockInOrOutEvent ofNotice(PurchaseTypeEnum purchaseTypeEnum, Long relatedOrderId, Long buyerId, Long createdUid) {
        StockInOrOutEvent stockInOrOutEvent = new StockInOrOutEvent();
        stockInOrOutEvent.setType(purchaseTypeEnum.getValue());
        stockInOrOutEvent.setRelatedOrderId(relatedOrderId);
        stockInOrOutEvent.setBuyerId(buyerId);
        stockInOrOutEvent.setCreatedUid(createdUid);
        return stockInOrOutEvent;
    }
}
