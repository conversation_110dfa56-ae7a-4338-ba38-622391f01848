package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerMarkImageContentTypeEnum;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 标注图片内容
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemDrawerMarkImageContent extends Entity {

    private static final long serialVersionUID = 1L;

    /**
     * 标注图片id
     */
    private Long markImageId;

    /**
     * 抽屉id
     */
    private Long drawerId;

    /**
     * 标注原内容
     */
    @TableField("`key`")
    private String key;

    /**
     * 操作类型 1-改为 2-删除 3-确认
     */
    private ItemDrawerMarkImageContentTypeEnum type;

    /**
     * 替换内容
     */
    @TableField("`value`")
    private String value;

    /**
     * 标注内容位置信息
     */
    private String position;

    /**
     * 前端要求增加的字段
     */
    private String pidentifier;
}
