package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存分配
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@EqualsAndHashCode()
public class InventoryAlloc implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;
    
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;
    
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;
    
    /**
     * 是否已删除
     */
    @TableLogic(value = "0", delval = "id")
    private Long isDel;
    
    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;
    
    /**
     * 平台商品ID
     */
    private Long platformItemId;
    
    /**
     * 平台商品SkuID
     */
    private Long platformItemSkuId;
    
    /**
     * 店铺编号
     */
    private String shopNo;
    
    /**
     * SKU编码
     */
    private String skuCode;
    
    /**
     * 系统计算出的分配数量
     */
    private Integer calcNum;
    
    /**
     * 最终生效的分配数量（允许人工调整）
     */
    private Integer effectiveNum;
    
    /**
     * 低库存预警
     */
    private Integer warnThreshold;
    
    /**
     * 是否开启库存锁定
     */
    private Boolean lockEnabled;
    
    /**
     * 最后分配时间
     */
    private Long lastAllocTime;
    
    /**
     * 组合装编码
     * 该字段不为空，说明该记录是组合装的库存分配记录，有两种情况：
     * 1、skuCode与suiteNo相同，表示该记录是组合装本身的库存分配记录；
     * 2、skuCode与suiteNo不同，表示该记录是组合装内单品的库存分配记录明细；
     */
    private String suiteNo;
    
    /**
     * 是否是主库存记录（组合装内单品的库存分配记录明细，不算主库存记录）
     */
    public boolean isMainRecord() {
        return suiteNo == null || suiteNo.isEmpty() || skuCode.equals(suiteNo);
    }
    
    /**
     * 计算过程
     */
    private String calculation;
}
