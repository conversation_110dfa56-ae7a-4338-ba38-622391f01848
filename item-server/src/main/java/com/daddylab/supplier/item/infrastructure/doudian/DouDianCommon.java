package com.daddylab.supplier.item.infrastructure.doudian;

import cn.hutool.core.util.ReUtil;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import com.doudian.open.core.AccessToken;
import com.doudian.open.core.AccessTokenBuilder;
import com.doudian.open.core.GlobalConfig;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> up
 * @date 2022年09月13日 11:23 AM
 */
@Service
public class DouDianCommon {

    @Resource
    DoudianConfig doudianConfig;

    private static final String TOKEN_KEY = "douDian_token";

    public synchronized AccessToken getAccessToken() {
        return getAccessToken(doudianConfig.getShopId());
    }

    public synchronized AccessToken getAccessToken(String shopId) {
        GlobalConfig.initAppKey(doudianConfig.getAppKey());
        GlobalConfig.initAppSecret(doudianConfig.getAppSecret());
        GlobalConfig.initHttpClientReadTimeout(Integer.valueOf(doudianConfig.getHttpClientReadTimeout()));

        if (!ReUtil.isMatch("\\d+", shopId)) {
            return AccessToken.wrap(shopId, "");
        }
        final String key = TOKEN_KEY + ":" + shopId;
        AccessToken accessToken = RedisUtil.get(key, AccessToken.class);
        if (Objects.nonNull(accessToken) && accessToken.isSuccess() && accessToken.getAccessToken() != null) {
            return accessToken;
        }

        AccessToken newToken = AccessTokenBuilder.build(Long.valueOf(shopId));
        if (!newToken.isSuccess()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR,
                    shopId + "获取抖音 AccessToken 失败:" + newToken.getSubCode() + " " +  newToken.getSubMsg());
        }
        RedisUtil.set(key, JsonUtil.toJson(newToken), 1, TimeUnit.HOURS);
        return newToken;
    }

}
