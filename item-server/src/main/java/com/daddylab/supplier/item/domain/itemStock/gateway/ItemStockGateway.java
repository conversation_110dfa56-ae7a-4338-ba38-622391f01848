package com.daddylab.supplier.item.domain.itemStock.gateway;

import com.daddylab.supplier.item.domain.itemStock.enums.StockChangeType;
import com.daddylab.supplier.item.domain.itemStock.vo.ItemStockChange;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ItemStockGateway {
    /**
     * 获取SKU库存
     * @param itemId 商品ID
     * @param skuId SKU ID
     * @return 库存数量
     */
    long getSkuStock(long itemId, long skuId);

    /**
     * 获取SKU库存
     * @param itemId 商品ID
     * @param skuCode skuCode
     * @return 库存数量
     */
    ItemSku getSkuStock(long itemId, String skuCode, String warehouseNo);

    /**
     * 获取商品库存（所有SKU库存之和）
     * @param itemId 商品ID
     * @return 库存数量
     */
    long getItemTotalStock(long itemId);

    /**
     * 入库
     * @param itemId 商品ID
     * @param skuId SKU ID
     * @param num 入库数量
     * @return 是否成功
     */
    boolean stockIn(long itemId, long skuId, int num);

    /**
     * 出库
     * @param itemId 商品ID
     * @param skuId SKU ID
     * @param num 出库数量
     * @return 是否成功
     */
    boolean stockOut(long itemId, long skuId, int num);

    /**
     * 重置库存
     * @param itemId 商品ID
     * @param skuId SKU ID
     * @param num 设置库存数量
     * @return 是否成功
     */
    boolean setStock(long itemId, long skuId, int num);

    /**
     * 获取库存变更日志
     * @param itemId 商品ID
     * @param skuId SKU ID
     * @return 日志列表
     */
    List<ItemStockChange> getStockChangeLogs(long itemId, long skuId);

    /**
     * 增加库存变更日志
     * @param skuId skuId
     * @param type 变更类型（入库、出库、重置）
     * @param chang 变更数量
     * @param before 变更前库存
     * @param after 变更后库存
     * @return 是否记录成功
     */
    boolean addStockChangeLog(long skuId, StockChangeType type, int chang, int before, int after);
}
