package com.daddylab.supplier.item.infrastructure.spring.web;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;

/**
 * <AUTHOR>
 * @since 2022/12/9
 */
@Configuration
@ConfigurationProperties(prefix = "xss")
@Data
public class XssConfig {

    private Boolean enable = true;
    private MediaType[] mediaTypes = new MediaType[]{
            MediaType.APPLICATION_JSON,
            MediaType.APPLICATION_XML};

}
