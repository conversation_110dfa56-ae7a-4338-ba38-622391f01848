/*
package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryMode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

*/
/**
 * <p>
 * 虚拟仓
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 *//*

@Data
@EqualsAndHashCode(callSuper = false)
public class VirtualWarehouse implements Serializable {

    private static final long serialVersionUID = 1L;

    */
/**
     * id
     *//*

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    */
/**
     * 创建时间createAt
     *//*

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    */
/**
     * 创建人updateUser
     *//*

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    */
/**
     * 更新时间updateAt
     *//*

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    */
/**
     * 更新人updateUser
     *//*

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    */
/**
     * 是否已删除
     *//*

    @TableLogic
    private Integer isDel;

    */
/**
     * 删除时间
     *//*

    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    */
/**
     * 虚拟仓名称
     *//*

    private String name;

    */
/**
     * 虚拟仓编号
     *//*

    private String no;

    */
/**
     * 状态.0正常,1停用
     *//*

    private Integer status;

    */
/**
     * 合作模式 0电商 1老爸抽检 2绿色家装 3商家入驻
     *//*

    private Integer businessLine;

    */
/**
     * 虚拟仓描述
     *//*

    private String description;

    */
/**
     * 库存模式 0 共享, 1 锁定
     *//*

    private InventoryMode inventoryMode;


}
*/
