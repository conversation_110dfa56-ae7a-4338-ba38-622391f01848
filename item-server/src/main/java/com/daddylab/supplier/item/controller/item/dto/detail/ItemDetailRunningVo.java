package com.daddylab.supplier.item.controller.item.dto.detail;

import com.daddylab.supplier.item.application.item.itemRunning.vo.ItemRunningVo;
import com.daddylab.supplier.item.controller.item.dto.ItemImageDto;
import com.daddylab.supplier.item.controller.item.dto.RunningDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13 7:51 下午
 * @description
 */
@Data
@ApiModel("商品详情-运营信息返回封装")
public class ItemDetailRunningVo {

    @ApiModelProperty("预计上架时间")
    Long estimateSaleTime;

    @ApiModelProperty("运营图片url")
    List<ItemImageDto> runningImageList;

    @ApiModelProperty("运营人员信息")
    List<RunningDto> runnerList;

    @ApiModelProperty("平台商品Ids")
    List<ItemRunningVo> itemRunnings;

    @ApiModelProperty(value = "产品标准名")
    String standardName;

    @ApiModelProperty(value = "卖点文案8-10个字")
    String sellingPoints;
}
