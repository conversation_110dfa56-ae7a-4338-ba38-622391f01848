package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ThirdPlatformSyncExternalTaskState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ThirdPlatformSyncExternalTaskType;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 第三方平台同步外部任务
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ThirdPlatformSyncExternalTask implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 同步id
     */
    private Long syncId;

    /**
     * 外部任务类型 1:影刀
     */
    private ThirdPlatformSyncExternalTaskType type;

    /**
     * 外部任务ID（依赖外部服务做同步的可以用来存外部任务ID，比如影刀）
     */
    private String taskId;

    /**
     * 外部任务状态 0:未完成 1:完成 2:异常
     */
    private ThirdPlatformSyncExternalTaskState state;

    /**
     * 任务数据
     */
    private String data;

    /**
     * 异常
     */
    private String error;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Integer isDel;


}
