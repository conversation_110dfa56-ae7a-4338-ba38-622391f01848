package com.daddylab.supplier.item.application.payment.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年04月23日 11:09 AM
 */
@Data
public class PrintOrderVo {

    @ApiModelProperty("单据类型")
    private String orderType;

    @ApiModelProperty("单据编号")
    private String orderNo;

    @ApiModelProperty("申请日期")
    private String applyDate;

    @ApiModelProperty("往来单位类型")
    private String tradeObjectType;

    @ApiModelProperty("往来单位")
    private String tradeObject;

    @ApiModelProperty("采购组织")
    private String purchaseOrg;

    @ApiModelProperty("采购部门")
    private String purchaseDept;

    @ApiModelProperty("采购员")
    private String purchaseBuyer;

    @ApiModelProperty("发起人")
    private String initiator;

    @ApiModelProperty("应付金额")
    private String paymentAmount;

    @ApiModelProperty("申请付款金额")
    private String applyAmount;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("明细列表")
    List<PrintOrderDetailVo> orderDetailVos;

    @ApiModelProperty("审批信息列表")
    List<PrintOrderAuditVo> auditVoList;

}
