package com.daddylab.supplier.item.infrastructure.config.event;

import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.google.common.eventbus.AsyncEventBus;
import com.google.common.eventbus.EventBus;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/30 11:29 上午
 * @description
 */
@Configuration
public class EventBusConfig {

    @Bean("syncEventBus")
    public EventBus getSyncEventBus() {
        return new EventBus();
    }

    @Bean("asyncEventBus")
    public AsyncEventBus getAsyncEventBus() {
        return new AsyncEventBus(ThreadUtil.getThreadPool(PoolEnum.EVENT_POOL));
    }

}
