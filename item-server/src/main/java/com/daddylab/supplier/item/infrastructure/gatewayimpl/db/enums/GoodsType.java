package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * SKU 货品类型
 *
 * <AUTHOR> up
 * @date 2024年03月12日 11:07 AM
 */
@Getter
@AllArgsConstructor
public enum GoodsType implements IEnum<Integer> {

    /**
     * 默认
     */
    DEFAULT(0, "默认(无)"),

    /**
     * 生鲜
     */
    FRESH(1, "生鲜"),

    APPLIANCE(2,"大家电"),

    FURNITURE(3,"大家具"),

    INSTALLATION_REQUIRED(4,"需要安装"),

    SHORT_GUARANTEE(5,"短保"),

    NO_INTERCEPT(6,"不支持拦截"),
    
    /**
     * 不支持7天无理由退货
     */
    NOT_SUPPORT_7_DAYS_NO_REASON(7,"不支持7天无理由退货"),
    ;


    private final Integer value;
    private final String desc;

}
