package com.daddylab.supplier.item.infrastructure.kingdee.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.exception.ExceptionFactory;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.item.gateway.BuyerGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.infrastructure.config.KingDeeConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum.*;

/**
 * <AUTHOR> up
 * @date 2022/4/2 10:10 上午
 */
@Service
@Slf4j
public class ReqJsonUtil {

  @Autowired CategoryGateway categoryGateway;

  @Autowired IShopService iShopService;

  @Autowired ProviderGateway providerGateway;

  @Autowired ItemSkuGateway itemSkuGateway;

  @Autowired IItemSkuService iItemSkuService;

  @Autowired ItemGateway itemGateway;

  @Autowired IStockInOrderService iStockInOrderService;

  @Autowired IOrganizationService iOrganizationService;

  @Autowired BuyerGateway buyerGateway;

  @Autowired IStockInOrderDetailService iStockInOrderDetailService;

  @Autowired IWarehouseService iWarehouseService;

  @Autowired IStockOutOrderService iStockOutOrderService;

  @Autowired IStockOutOrderDetailService iStockOutOrderDetailService;

  @Autowired IBaseUnitService iBaseUnitService;

  @Autowired IFinanceInfoService iFinanceInfoService;

  @Autowired IBankAccountService iBankAccountService;

  @Autowired ITaxRateService iTaxRateService;

  @Resource IPurchaseOrderService iPurchaseOrderService;

  @Resource IItemProcurementService iItemProcurementService;

  @Autowired IPurchasePayableApplyOrderService iPurchasePayableApplyOrderService;

  @Resource IPaymentApplyOrderService iPaymentApplyOrderService;

  /**
   * 新建根物料分组目录
   *
   * @param number
   * @param name
   * @return
   */
  public static String saveRootCategory(String number, String name) {
    Assert.notBlank(number, "根物料分组编码不得为空");
    Assert.notBlank(name, "根物料分组编码不得为空");

    Map<String, Object> reqMap = new HashMap<>(8);
    reqMap.put("formid", ApiEnum.GROUP_SAVE_CATEGORY.getFormId());
    Map<String, Object> dataMap = new HashMap<>(8);
    dataMap.put("FNumber", number);
    dataMap.put("FName", name);
    // 创建组织
    dataMap.put("FCreateOrgId", getCreateOrgId(null));
    // 使用组织
    dataMap.put("FUseOrgId", getUseOrgId(null));
    // 物料分组固定填写 FMaterialGroup
    dataMap.put("GroupFieldKey", "FMaterialGroup");
    //        // 分组描述，字符串类型(非必录)
    //        dataMap.put("fDescription", "");
    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  public String invoke(ApiEnum apiEnum, Long id) throws Exception {
    if (SAVE_STOCK_IN_ORDER.equals(apiEnum) || UPDATE_STOCK_IN_ORDER.equals(apiEnum)) {
      return saveStockInOrder(id);
    }
    if (SAVE_SKU.equals(apiEnum) || UPDATE_SKU.equals(apiEnum)) {
      return saveSku(id);
    }
    if (SAVE_STOCK_OUT_ORDER.equals(apiEnum) || UPDATE_STOCK_OUT_ORDER.equals(apiEnum)) {
      return saveStockOutOrder(id);
    }
    if (SAVE_PRICE_CATEGORY.equals(apiEnum)) {
      return saveSkuPriceCategory(id);
    }
    if (SAVE_PAY_BILL.equals(apiEnum)) {
      return savePayBill(id);
    }

    return ReflectUtil.invoke(this, METHOD_MAP.get(apiEnum), id);
  }

  // -------------------------------------------- 我是分割线
  // --------------------------------------------------

  public static final Map<ApiEnum, String> METHOD_MAP = new HashMap<>(16);

  static {
    METHOD_MAP.put(GROUP_SAVE_CATEGORY, "groupSaveCategory");
    METHOD_MAP.put(GROUP_DELETE_CATEGORY, "groupDeleteCategory");

    METHOD_MAP.put(SAVE_SHOP, "saveShop");
    METHOD_MAP.put(SAVE_PROVIDER, "saveProvider");
    METHOD_MAP.put(SAVE_SKU, "saveSku");
    METHOD_MAP.put(SAVE_STOCK_IN_ORDER, "saveStockInOrder");
    METHOD_MAP.put(SAVE_STOCK_OUT_ORDER, "saveStockOutOrder");

    METHOD_MAP.put(UPDATE_SHOP, "saveShop");
    METHOD_MAP.put(UPDATE_PROVIDER, "saveProvider");
    METHOD_MAP.put(UPDATE_SKU, "saveSku");
    METHOD_MAP.put(UPDATE_STOCK_IN_ORDER, "saveStockInOrder");
    METHOD_MAP.put(UPDATE_STOCK_OUT_ORDER, "saveStockOutOrder");

    METHOD_MAP.put(DELETE_PROVIDER, "deleteProvider");
    METHOD_MAP.put(DELETE_STOCK_IN_ORDER, "deleteStockInOrder");
    METHOD_MAP.put(DELETE_STOCK_OUT_ORDER, "deleteStockOutOrder");
  }

  public String deleteProvider(Long id) {
    Provider byId = providerGateway.getById(id);
    Assert.notNull(byId, "供应商查询不得为空。id:" + id);
    Assert.notBlank(byId.getKingDeeId(), "供应商金蝶id不得为空。id:" + id);
    return commonReqJson(byId.getKingDeeId(), ApiEnum.DELETE_PROVIDER.getFormId());
  }

  public String deleteStockInOrder(Long id) {
    StockInOrder byId = iStockInOrderService.getById(id);
    Assert.notNull(byId, "入库单查询不得为空。id:" + id);
    Assert.notBlank(byId.getKingDeeId(), "入库单金蝶id不得为空。id:" + id);
    return commonReqJson(byId.getKingDeeId(), ApiEnum.DELETE_STOCK_IN_ORDER.getFormId());
  }

  public String deleteStockOutOrder(Long id) {
    StockOutOrder byId = iStockOutOrderService.getById(id);
    Assert.notNull(byId, "出库单查询不得为空。id:" + id);
    Assert.notBlank(byId.getKingDeeId(), "出库单金蝶id不得为空。id:" + id);
    return commonReqJson(byId.getKingDeeId(), ApiEnum.DELETE_STOCK_OUT_ORDER.getFormId());
  }

  /**
   * 提交，审核，反审核。删除
   *
   * @param kingDeeId
   * @param formId
   * @return
   */
  public String commonReqJson(String kingDeeId, String formId) {
    Assert.notBlank(kingDeeId, "金蝶同步id不得为空");
    Assert.notBlank(formId, "金蝶表单id不得为空");

    Map<String, Object> reqMap = new HashMap<>(4);
    reqMap.put("formid", formId);
    Map<String, Object> dataMap = new HashMap<>(4);
    dataMap.put("Ids", kingDeeId);
    dataMap.put("CreateOrgId", getKingDeeConfig().getCreateOrgId());
    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  private static KingDeeConfig getKingDeeConfig() {
    return SpringUtil.getBean("kingDeeConfig", KingDeeConfig.class);
  }

  private static Map<String, Object> getCreateOrgId(String skuCode) {
    Map<String, Object> createOrgMap = new HashMap<>(2);
    // 针对次skuCode做特殊处理，创建组织和修改必须一致，这sku在20年4月28日创建。
    if ("SG00010301".equals(skuCode)) {
      createOrgMap.put("FNumber", "101");
    } else {
      createOrgMap.put("FNumber", getKingDeeConfig().getCreateOrgId());
    }
    return createOrgMap;
  }

  private static Map<String, Object> getUseOrgId(String skuCode) {
    Map<String, Object> useOrgMap = new HashMap<>(2);
    if ("SG00010301".equals(skuCode)) {
      useOrgMap.put("FNumber", "101");
    } else {
      useOrgMap.put("FNumber", getKingDeeConfig().getUseOrgId());
    }
    return useOrgMap;
  }

  private static String getActiveShortName() {
    String activeProfile = SpringUtil.getActiveProfile();
    if (StringUtil.isNotBlank(activeProfile)) {
      return activeProfile.substring(0, 2).toUpperCase();
    }
    return "MA";
  }

  private static String getCategoryNum(Category category) {
    return PinyinUtil.getFirstLetter(category.getName(), "").toUpperCase()
        + category.getId()
        + getActiveShortName();
  }

  private static String getStockInOrderNum(String stockInOrderNo) {
    if (stockInOrderNo.startsWith("CGRK")) {
      return stockInOrderNo + getActiveShortName();
    } else {
      // 可能存在此入库单数据是根据kingDee的数据手动添加到erp库，编号必须保持一致。
      return stockInOrderNo;
    }
  }

  private static String getStockOutOrderNum(String stockInOrderNo) {
    if (stockInOrderNo.startsWith("CGCK")) {
      return stockInOrderNo + getActiveShortName();
    } else {
      // 可能存在此入库单数据是根据kingDee的数据手动添加到erp库，编号必须保持一致。
      return stockInOrderNo;
    }
  }

  //    private String getUnitCode(Long itemId, String unit, String skuCode) {
  //
  //
  ////        String unitCode;
  ////        // 如果商品id小于分割id，说明此sku已经将单位同步到kingDee.
  ////        if (itemId <= Long.parseLong(getKingDeeConfig().getLineItemId())) {
  ////            // 判断此sku是否已经生成入库出库单，如果生成了就无法再重写单位了，如果没有，就将单位纠正为Pcs
  ////            if (itemSkuGateway.beRelatedStockInOrder(skuCode)
  ////                    && itemSkuGateway.beRelatedStockOutOrder(skuCode)) {
  ////                unitCode = iBaseUnitService.getKingDeeNum(unit);
  ////            } else {
  ////                unitCode = getKingDeeConfig().getUnitCode();
  ////            }
  ////        } else {
  ////            unitCode = getKingDeeConfig().getUnitCode();
  ////        }
  ////        return unitCode;
  //    }

  public String groupSaveCategory(Long categoryId) {
    Category category = categoryGateway.getById(categoryId);
    Assert.notNull(category, "品类查询不得为空，id:" + categoryId);

    Category partnerCategory = categoryGateway.getById(category.getParentId());
    String parentKingDeeId =
        Objects.isNull(partnerCategory)
            ? getKingDeeConfig().getRootCategory()
            : partnerCategory.getKingDeeId();
    boolean add = StringUtil.isBlank(category.getKingDeeId());

    Map<String, Object> reqMap = new HashMap<>(8);
    reqMap.put("formid", ApiEnum.GROUP_SAVE_CATEGORY.getFormId());
    Map<String, Object> dataMap = new HashMap<>(16);
    // 分组编码，字符串类型(必录)，注（必须唯一）
    dataMap.put("FNumber", category.getKingDeeNum());
    // 分组名，字符串类型(必录)
    dataMap.put("FName", category.getName());
    if (add) {
      dataMap.put("FCreateOrgId", getCreateOrgId(null));
      dataMap.put("FUseOrgId", getUseOrgId(null));
      // 物料分组固定填写 FMaterialGroup
      dataMap.put("GroupFieldKey", "FMaterialGroup");
      // 父分组内码，整型(非必录)，品类在金蝶系统中生成的id
      dataMap.put("FParentId", parentKingDeeId);
    } else {
      // 料分组在金蝶系统中对应的id,无值，表示新增品类，有值表示更新品类。
      dataMap.put("GroupPkId", Long.valueOf(category.getKingDeeId()));
    }
    reqMap.put("data", dataMap);

    return JsonUtil.toJson(reqMap);
  }

  public String groupDeleteCategory(Long categoryId) {
    Category category = categoryGateway.getById(categoryId);
    Assert.notNull(category, "品类查询不得为空，id:" + categoryId);
    Assert.notBlank(category.getKingDeeId(), "删除品类，对应的金蝶id不得为空。id:" + categoryId);

    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("FormId", GlobalConstant.KING_DEE_CATEGORY_API_ID);
    Map<String, Object> dataMap = new HashMap<>(4);
    dataMap.put("FormId", ApiEnum.GROUP_SAVE_CATEGORY.getFormId());
    dataMap.put("groupFieldKey", "FMaterialGroup");
    dataMap.put("groupPkIds", category.getKingDeeId());
    reqMap.put("data", dataMap);

    return JsonUtil.objToStr(reqMap);
  }

  public String saveShop(Long shopId) {
    Shop shop = iShopService.getById(shopId);
    Assert.notNull(shop, "店铺查询不得为空，id:" + shopId);

    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("formid", ApiEnum.SAVE_SHOP.getFormId());

    Map<String, Object> dataMap = new HashMap<>(4);
    dataMap.put("IsAutoSubmitAndAudit", "true");
    dataMap.put("FName", shop.getName());
    dataMap.put("FNumber", shop.getSn());

    Map<String, Object> modelMap = new HashMap<>(8);
    modelMap.put("FCreateOrgId", getCreateOrgId(null));
    modelMap.put("FUseOrgId", getUseOrgId(null));
    modelMap.put("FNumber", shop.getSn());
    modelMap.put("FName", shop.getName());
    modelMap.put("FShortName", shop.getName());

    if (StringUtil.isNotBlank(shop.getKingDeeId())) {
      modelMap.put("FCUSTID", shop.getKingDeeId());
    }

    dataMap.put("Model", modelMap);
    reqMap.put("data", dataMap);

    return JsonUtil.toJson(reqMap);
  }

  public String saveProvider(Long providerId) {
    Provider provider = providerGateway.getById(providerId);
    Assert.notNull(provider, "供应商查询不得为空，id:" + providerId);
    FinanceInfo financeInfo = iFinanceInfoService.getInfoByProviderId(providerId);
    List<BankAccount> bankAccountList = iBankAccountService.getAccountByProviderId(providerId);
    boolean isUpdate = StringUtil.isNotBlank(provider.getKingDeeId());
    Optional<Buyer> buyerOptional = buyerGateway.buyerExist(provider.getMainChargerUserId());

    Map<String, Object> baseInfoMap = new HashMap<>(8);
    Map<String, Object> countryMap = new HashMap<>(2);
    countryMap.put("FNumber", "China");
    baseInfoMap.put("fCountry", countryMap);
    baseInfoMap.put("FSOCIALCRECODE", provider.getUnifySocialCreditCodes());
    baseInfoMap.put("FSupplyClassify", "CG");
    baseInfoMap.put("FAddress", provider.getAddress());

    // 添加负责人
    buyerOptional.ifPresent(
        val -> {
          Map<String, Object> staffMap = new HashMap<>();
          staffMap.put("FNumber", val.getKingDeeId());
          baseInfoMap.put("FStaffId", staffMap);
        });

    // 财务信息
    Map<String, Object> financeInfoMap = new HashMap<>(2);
    // 结算币类
    Map<String, Object> payCurrencyMap = new HashMap<>(2);
    payCurrencyMap.put("FNumber", "PRE001");

    if (Objects.nonNull(financeInfo)) {
      if (financeInfo.getCurrency() == 2) {
        payCurrencyMap.put("FNumber", "PRE007");
      }
      // 税分类。1:一般纳税人 2:小规模纳税人
      Map<String, Object> taxTypeMap = new HashMap<>(2);
      if (financeInfo.getTaxType() == 1) {
        taxTypeMap.put("FNumber", "SFL02_SYS");
      } else {
        taxTypeMap.put("FNumber", "SFL01_SYS");
      }
      financeInfoMap.put("FTaxType", taxTypeMap);
      // 发票类型(1:增值税专用发票 2:普通发票)
      if (financeInfo.getInvoiceType() == 1) {
        financeInfoMap.put("FInvoiceType", 1);
      } else {
        financeInfoMap.put("FInvoiceType", 2);
      }
      // 默认税率
      String kingDeeNo = iTaxRateService.kingDeeNo(financeInfo.getTaxRate());
      Map<String, Object> taxRateMap = new HashMap<>(2);
      taxRateMap.put("FNUMBER", kingDeeNo);

      financeInfoMap.put("FTaxRateId", taxRateMap);
    }
    financeInfoMap.put("FPayCurrencyId", payCurrencyMap);

    // 联系人，地址信息
    List<Map<String, Object>> bankInfoList = new LinkedList<>();
    if (CollectionUtils.isNotEmpty(bankAccountList)) {
      for (BankAccount bankAccount : bankAccountList) {
        Map<String, Object> bankMap = new HashMap<>(4);
        Map<String, Object> bankCountryMap = new HashMap<>(2);
        bankCountryMap.put("FNumber", "China");
        bankMap.put("FBankCountry", bankCountryMap);
        bankMap.put("FBankCode", bankAccount.getBankCard());
        bankMap.put("FOpenBankName", bankAccount.getBankDeposit());
        bankMap.put("FBankIsDefault", false);
        bankMap.put("FBankDesc", bankAccount.getDescription());

        bankInfoList.add(bankMap);
      }
    } else {
      Map<String, Object> bankMap = new HashMap<>(3);
      bankMap.put("FBankIsDefault", false);
      bankMap.put("FOpenBankName", "/");
      bankMap.put("FBankCode", "/");
      bankInfoList.add(bankMap);
    }

    Map<String, Object> modelMap = new HashMap<>(8);
    String number =
        StringUtil.isNotBlank(provider.getProviderNo())
            ? provider.getProviderNo()
            : "G00S" + String.format("%05d", provider.getId());
    modelMap.put("FNumber", number);
    modelMap.put("FName", provider.getName());

    if (!isUpdate) {
      modelMap.put("FCreateOrgId", getCreateOrgId(null));
      modelMap.put("FUseOrgId", getUseOrgId(null));
    }
    modelMap.put("FBaseInfo", baseInfoMap);
    modelMap.put("FFinanceInfo", financeInfoMap);
    modelMap.put("FBankInfo", bankInfoList);

    if (isUpdate) {
      modelMap.put("FSupplierId", Long.valueOf(provider.getKingDeeId()));
    }

    Map<String, Object> dataMap = new HashMap<>(8);
    dataMap.put("Model", modelMap);
    dataMap.put("isAutoSubmitAndAudit", "true");

    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("formid", ApiEnum.SAVE_PROVIDER.getFormId());
    reqMap.put("data", dataMap);

    return JsonUtil.toJson(reqMap);
  }

  public String saveSku(Long skuId) {
    ItemSku itemSku = iItemSkuService.getById(skuId);
    boolean updateSku = StringUtil.isNotBlank(itemSku.getKingDeeId());

    Assert.notNull(itemSku, "sku查询不得为空，id:" + skuId);
    Item item = itemGateway.getItem(itemSku.getItemId());
    Assert.notNull(item, "商品查询不得为空，skuId:" + skuId);
    Category category = categoryGateway.getById(item.getCategoryId());
    Provider provider = providerGateway.getById(item.getProviderId());
    if (!updateSku) {
      Assert.notNull(provider, "供应商查询不得为空，skuId:" + skuId);
      Assert.notNull(provider.getProviderNo(), "供应商查询对应的金蝶编号不得为空，providerId:" + provider.getId());
      Assert.notNull(category, "品类查询不得为空，skuId:" + skuId);
      Assert.notBlank(category.getKingDeeId(), "品类查询对应的金蝶id不得为空，categoryId:" + category.getId());
    }
    String attrJson = itemSkuGateway.getSkuAttrListStr(itemSku.getSkuCode());
    String buyerKingDeeAccount = buyerGateway.getKingDeeAccountByItemId(item.getId());
    Buyer buyer = buyerGateway.getByItemId(itemSku.getItemId());
    String unitCode = iBaseUnitService.getKingDeeNum(itemSku.getUnit());
    String warehouseNo = itemSku.getWarehouseNo();
    String taxRateNo = iTaxRateService.kingDeeNo(itemSku.getTaxRate());

    Map<String, Object> modelMap = new HashMap<>();
    if (!updateSku) {
      modelMap.put("FCreateOrgId", getCreateOrgId(itemSku.getSkuCode()));
      modelMap.put("FUseOrgId", getUseOrgId(itemSku.getSkuCode()));
    }
    modelMap.put("FNumber", itemSku.getSupplierCode());
    modelMap.put("FName", item.getName());
    // 规格
    modelMap.put("FSpecification", attrJson);
    // 同步新增还是同步更新
    if (updateSku) {
      modelMap.put("FMATERIALID", itemSku.getKingDeeId());
    }

    // 存货类别（必存），暂时写死。CHLB01_SYS(原材料)。001(商品)
    Map<String, Object> categoryIdMap = new HashMap<>(2);
    String activeProfile = SpringUtil.getActiveProfile();

    if (activeProfile.equals("gray")
        || activeProfile.equals("pre")
        || activeProfile.equals("prod")) {
      categoryIdMap.put("FNumber", "001");
    } else {
      categoryIdMap.put("FNumber", "CHLB01_SYS");
    }
    // 基本单位
    Map<String, Object> fBaseUnitIdMap = new HashMap<>(2);
    fBaseUnitIdMap.put("FNumber", unitCode);
    Map<String, Object> subHeadMap = new HashMap<>(2);
    subHeadMap.put("FCategoryID", categoryIdMap);
    if (!updateSku) {
      subHeadMap.put("FBaseUnitId", fBaseUnitIdMap);
    }
    // 税率
    Map<String, Object> taxRateMap = new HashMap<>(2);
    taxRateMap.put("FNUMBER", taxRateNo);
    subHeadMap.put("FTaxRateId", taxRateMap);
    // 条形码
    subHeadMap.put("FBARCODE", itemSku.getBarCode());
    modelMap.put("SubHeadEntity", subHeadMap);
    // 库存
    Map<String, Object> fStoreUnitIdMap = new HashMap<>(2);
    // 库存单位
    fStoreUnitIdMap.put("FNumber", unitCode);
    Map<String, Object> subHeadMap1 = new HashMap<>(2);
    if (!updateSku) {
      subHeadMap1.put("FStoreUnitID", fStoreUnitIdMap);
    }
    if (StringUtil.isNotBlank(warehouseNo) && !"0".equals(warehouseNo)) {
      Map<String, Object> fStockIdMap = new HashMap<>(2);
      fStockIdMap.put("FNumber", warehouseNo);
      subHeadMap1.put("FStockId", fStockIdMap);
    }
    // 库存成本
    subHeadMap1.put("FRefCost", itemSku.getCostPrice());
    modelMap.put("SubHeadEntity1", subHeadMap1);

    // 物料分组类别
    Map<String, Object> materialGroupMap = new HashMap<>(2);
    materialGroupMap.put("FNumber", Objects.isNull(category) ? "" : category.getKingDeeNum());
    modelMap.put("FMaterialGroup", materialGroupMap);
    // 采购组织
    Map<String, Object> purchaseOrgIdMap = new HashMap<>(2);
    purchaseOrgIdMap.put("FNumber", getKingDeeConfig().getUseOrgId());
    // 供应商
    Map<String, Object> defaultVendorMap = new HashMap<>(2);
    defaultVendorMap.put("FNumber", Objects.isNull(provider) ? "" : provider.getProviderNo());
    // 采购单位
    Map<String, Object> fPurchaseUnitIdMap = new HashMap<>(2);
    fPurchaseUnitIdMap.put("FNumber", unitCode);
    // 采购计价单位
    Map<String, Object> fPurchasePriceUnitIdMap = new HashMap<>(2);
    fPurchasePriceUnitIdMap.put("FNumber", unitCode);
    Map<String, Object> subHeadEntity3Map = new HashMap<>(4);
    subHeadEntity3Map.put("FPurchaseOrgId", purchaseOrgIdMap);
    // 采购员
    if (Objects.nonNull(buyer)) {
      Map<String, Object> purchaserIdMap = new HashMap<>();
      purchaserIdMap.put("FNumber", buyer.getKingDeeId());
      subHeadEntity3Map.put("FPurchaserId", purchaserIdMap);
    }
    if (!updateSku) {
      subHeadEntity3Map.put("FDefaultVendor", defaultVendorMap);
      subHeadEntity3Map.put("FPurchaseUnitId", fPurchaseUnitIdMap);
      subHeadEntity3Map.put("FPurchasePriceUnitId", fPurchasePriceUnitIdMap);
    } else {
      if (StringUtil.isNotBlank(defaultVendorMap.get("FNumber").toString())) {
        subHeadEntity3Map.put("FDefaultVendor", defaultVendorMap);
      }
    }

    modelMap.put("SubHeadEntity3", subHeadEntity3Map);

    modelMap.put("F_PAEZ_Text", itemSku.getCostPrice());

    // 采购员
    if (StrUtil.isNotBlank(buyerKingDeeAccount)) {
      Map<String, Object> oraBaseMap = new HashMap<>(2);
      oraBaseMap.put("FUSERACCOUNT", buyerKingDeeAccount);
      modelMap.put("F_ora_Base", oraBaseMap);
    }

    Map<String, Object> dataMap = new HashMap<>(4);
    if (!updateSku) {
      dataMap.put("IsAutoSubmitAndAudit", "true");
    }
    dataMap.put("Model", modelMap);

    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("formid", ApiEnum.SAVE_SKU.getFormId());
    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  /**
   * 保存 采购入库单。
   *
   * @param id
   * @return
   */
  public String saveStockInOrder(Long id) {
    StockInOrder stockInOrder = iStockInOrderService.getById(id);
    Organization organization = iOrganizationService.getById(stockInOrder.getOrganizationId());
    Provider provider = providerGateway.getById(stockInOrder.getProviderId());
    String buyerKingDeeId = buyerGateway.getKingDeeId(stockInOrder.getBuyerUserId());
    List<String> errorSkuCode = new LinkedList<>();

    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("formId", "STK_InStock");
    Map<String, Object> modelMap = new HashMap<>(16);
    // 如果存在kingDeeId，则表示更新，否则为新增。
    if (StringUtil.isNotBlank(stockInOrder.getKingDeeId())) {
      modelMap.put("FID", stockInOrder.getKingDeeId());
    }
    // 单据编号
    modelMap.put("FBillNo", stockInOrder.getNo());
    // 入库日期
    //        if (isHedge) {
    //            LocalDateTime localDateTime =
    // DateUtil.parseTimeStamp(stockInOrder.getReceiptTime());
    //            LocalDateTime localDateTime1 = localDateTime.plusDays(1);
    //            modelMap.put("FDate", DateUtil.format(localDateTime1, "yyyy-MM-dd"));
    //        } else {
    modelMap.put("FDate", DateUtil.format(stockInOrder.getReceiptTime(), "yyyy-MM-dd"));
    //        }
    // 收料组织id
    Map<String, Object> stockMap = new HashMap<>(2);
    stockMap.put("FNumber", organization.getKingDeeId());
    modelMap.put("FStockOrgId", stockMap);
    // 采购组织
    Map<String, Object> purchaseMap = new HashMap<>(2);
    purchaseMap.put("FNumber", organization.getKingDeeId());
    modelMap.put("FPurchaseOrgId", purchaseMap);
    // 采购员
    Map<String, Object> buyerMap = new HashMap<>(2);
    buyerMap.put("FNumber", buyerKingDeeId);
    modelMap.put("FPurchaserId", buyerMap);
    // 供应商
    Map<String, Object> providerMap = new HashMap<>(2);
    providerMap.put("FNumber", provider.getProviderNo());
    modelMap.put("FSupplierId", providerMap);
    // 需求组织
    Map<String, Object> demandOrgMap = new HashMap<>(2);
    demandOrgMap.put("FNumber", organization.getKingDeeId());
    modelMap.put("FDemandOrgId", demandOrgMap);

    // 物料信息列
    List<StockInOrderDetail> stockInOrderDetailList =
        iStockInOrderDetailService.getListByOrderId(stockInOrder.getId());
    List<Map<String, Object>> stockEntryList = new LinkedList<>();
    for (StockInOrderDetail detail : stockInOrderDetailList) {

      ItemSku itemSku = itemSkuGateway.getBySkuCode(detail.getItemSkuCode());
      String unit = iBaseUnitService.getKingDeeNum(itemSku.getUnit());
      if (StrUtil.isBlank(unit)) {
        break;
      }

      Map<String, Object> entryMap = new HashMap<>(16);
      // 物料编号
      Map<String, Object> fMaterialId = new HashMap<>(2);
      fMaterialId.put("FNumber", detail.getItemSkuCode());
      entryMap.put("FMaterialId", fMaterialId);
      // 规格
      //            entryMap.put("FUOM", attrStr);
      // 库存单位
      Map<String, Object> unitMap = new HashMap<>(2);
      unitMap.put("FNumber", unit);
      entryMap.put("FUnitID", unitMap);
      // 定价单位
      Map<String, Object> setPriceUnitMap = new HashMap<>(2);
      setPriceUnitMap.put("FNumber", unit);
      entryMap.put("FSetPriceUnitID", setPriceUnitMap);
      // 采购单位
      Map<String, Object> remainInStockUnitMap = new HashMap<>(2);
      remainInStockUnitMap.put("FNumber", unit);
      entryMap.put("FRemainInStockUnitId", remainInStockUnitMap);
      // 计价单位
      Map<String, Object> priceUnitMap = new HashMap<>(2);
      priceUnitMap.put("FNumber", unit);
      entryMap.put("FPriceUnitID", priceUnitMap);
      // 应收数量
      entryMap.put("FMustQty ", detail.getReceiptQuantity());
      // 实收数量
      entryMap.put("FRealQty ", detail.getReceiptQuantity());
      // 是否赠品
      String giveAway = detail.getTaxPrice().compareTo(BigDecimal.ZERO) == 0 ? "true" : "false";
      entryMap.put("FGiveAway", giveAway);
      // 仓库
      Map<String, Object> stockIdMap = new HashMap<>(2);
      stockIdMap.put("FNumber", detail.getWarehouseNo());
      entryMap.put("FStockId", stockIdMap);
      // 备注
      entryMap.put("FNote", detail.getRemark());
      // 含税单价
      entryMap.put("FTaxPrice", detail.getTaxPrice());
      // 税率，金蝶单位是%，所以要再乘以100
      entryMap.put("FEntryTaxRate", detail.getTaxRate().multiply(BigDecimal.valueOf(100L)));
      // 计价数量
      entryMap.put("FPriceUnitQty", detail.getReceiptQuantity());
      stockEntryList.add(entryMap);
    }
    Assert.state(
        CollectionUtils.isEmpty(errorSkuCode),
        "入库单中，以下sku不存在金蝶id。skuCode:" + StrUtil.join(",", errorSkuCode));
    modelMap.put("FInStockEntry", stockEntryList);

    // 单据类型
    Map<String, Object> billTypeIdMap = new HashMap<>(2);
    billTypeIdMap.put("FNumber", "RKD01_SYS");
    modelMap.put("FBillTypeID", billTypeIdMap);
    // 货主类型
    modelMap.put("FOwnerTypeIdHead", "BD_OwnerOrg");
    // 货主
    Map<String, Object> ownerTypeIdHeadMap = new HashMap<>(2);
    ownerTypeIdHeadMap.put("FNumber", organization.getKingDeeId());
    modelMap.put("FOwnerIdHead", ownerTypeIdHeadMap);

    Map<String, Object> dataMap = new HashMap<>(2);
    dataMap.put("Model", modelMap);
    if (getKingDeeConfig().getAutoSubmit()) {
      dataMap.put("IsAutoSubmitAndAudit", "true");
    }
    dataMap.put("InterationFlags", "STK_InvCheckResult");
    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  public String saveStockOutOrder(Long id) {
    StockOutOrder stockOutOrder = iStockOutOrderService.getById(id);
    Organization organization =
        iOrganizationService.getById(stockOutOrder.getPurchaseOrganizationId());
    Provider provider = providerGateway.getById(stockOutOrder.getProviderId());

    List<String> errorSkuCode = new LinkedList<>();

    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("formId", ApiEnum.SAVE_STOCK_OUT_ORDER.getFormId());
    Map<String, Object> dataMap = new HashMap<>(2);
    Map<String, Object> modelMap = new HashMap<>(16);

    // 单据编号
    modelMap.put("FBillNo", stockOutOrder.getNo());
    // 退料组织
    Map<String, Object> stockMap = new HashMap<>(2);
    stockMap.put("FNumber", organization.getKingDeeId());
    modelMap.put("FStockOrgId", stockMap);
    // 采购组织
    Map<String, Object> purchaseMap = new HashMap<>(2);
    purchaseMap.put("FNumber", organization.getKingDeeId());
    modelMap.put("FPurchaseOrgId", purchaseMap);
    // 需求组织 = 采购组织
    Map<String, Object> requireOrgMap = new HashMap<>(2);
    requireOrgMap.put("FNumber", organization.getKingDeeId());
    modelMap.put("FRequireOrgId", requireOrgMap);

    // 退料日期
    if (StrUtil.isNotBlank(stockOutOrder.getRemark())
        && stockOutOrder.getRemark().contains("冲销流程")) {
      modelMap.put("FDate", DateUtil.format(DateUtil.now(), "yyyy-MM-dd"));
    } else {
      if (Objects.nonNull(stockOutOrder.getOutboundTime())
          && stockOutOrder.getOutboundTime() != 0L) {
        modelMap.put("FDate", DateUtil.format(stockOutOrder.getOutboundTime(), "yyyy-MM-dd"));
      } else {
        PurchaseOrder purchaseOrder =
            iPurchaseOrderService.getById(stockOutOrder.getPurchaseOrderId());
        if (Objects.nonNull(purchaseOrder) && purchaseOrder.getType() == 2) {
          modelMap.put("FDate", DateUtil.format(purchaseOrder.getPurchaseDate(), "yyyy-MM-dd"));
        } else {
          modelMap.put("FDate", DateUtil.format(stockOutOrder.getCreatedAt(), "yyyy-MM-dd"));
        }
      }
    }

    // 采购组? todo
    // 采购员？todo
    // 退料方式.退料补料 1:A 。2.退料并扣款 2:B
    modelMap.put("FMRMODE", stockOutOrder.getReturnMode() == 1 ? "A" : "B");
    // 供应商
    Map<String, Object> providerMap = new HashMap<>(2);
    providerMap.put("FNumber", provider.getProviderNo());
    modelMap.put("FSupplierId", providerMap);
    // 退料类型 库存退料1:B
    modelMap.put("FMRTYPE", "B");

    List<Map<String, Object>> entryMapList = new LinkedList<>();
    List<StockOutOrderDetail> stockOutOrderDetailList =
        iStockOutOrderDetailService.getListByOrderId(stockOutOrder.getId());
    for (StockOutOrderDetail detail : stockOutOrderDetailList) {

      Boolean syncKingDee = itemSkuGateway.isSyncKingDee(detail.getItemSkuCode());
      if (!syncKingDee) {
        errorSkuCode.add(detail.getItemSkuCode());
        break;
      }

      ItemSku itemSku = itemSkuGateway.getBySkuCode(detail.getItemSkuCode());
      String unit = iBaseUnitService.getKingDeeNum(itemSku.getUnit());
      if (StrUtil.isBlank(unit)) {
        break;
      }

      Map<String, Object> entryMap = new HashMap<>(8);
      // 物料编号
      Map<String, Object> fMaterialId = new HashMap<>(2);
      fMaterialId.put("FNumber", detail.getItemSkuCode());
      entryMap.put("FMaterialId", fMaterialId);
      // 名称，规则，计价单位、采购单位。这几个参数直接物料的数据，自动填充，无需填写
      // 库存单位，
      Map<String, Object> unitMap = new HashMap<>(2);
      unitMap.put("FNumber", unit);
      entryMap.put("FUnitID", unitMap);
      // 计价单位
      Map<String, Object> priceUnitMap = new HashMap<>(2);
      priceUnitMap.put("FNumber", unit);
      entryMap.put("FPriceUnitID", priceUnitMap);
      // 实退数量（主要，可编辑)，扣款数量 = 计价数量 = 采购数量 自动填充跟随实退数量。对应erp实际退料数量
      entryMap.put("FRMREALQTY", detail.getReturnQuantity());
      // 补料数量（可编辑）。对应erp实际退料数量
      entryMap.put("FBaseReplayQty", detail.getReturnQuantity());
      // 库存状态，默认可用
      Map<String, Object> stockStatusMap = new HashMap<>(2);
      stockStatusMap.put("FNumber", "KCZT01_SYS");
      entryMap.put("FStockStatusId", stockStatusMap);
      // 是否是赠品
      boolean giveAway = detail.getTaxPrice().compareTo(BigDecimal.ZERO) == 0;
      entryMap.put("FGiveAway", giveAway);
      // 仓库
      Map<String, Object> stockIdMap = new HashMap<>(2);
      stockIdMap.put("FNumber", detail.getWarehouseNo());
      entryMap.put("FSTOCKID", stockIdMap);
      // 备注
      entryMap.put("FNote", detail.getRemark());
      // 含税单价
      entryMap.put("FTAXPRICE", detail.getTaxPrice());
      // 税率（单位%带小数点）
      entryMap.put("FENTRYTAXRATE", detail.getTaxRate().multiply(BigDecimal.valueOf(100L)));
      entryMapList.add(entryMap);
    }

    Assert.state(
        CollectionUtils.isEmpty(errorSkuCode),
        "出库单中，以下sku不存在金蝶id。skuCode:" + StrUtil.join(",", errorSkuCode));

    modelMap.put("FPURMRBENTRY", entryMapList);
    dataMap.put("Model", modelMap);
    if (getKingDeeConfig().getAutoSubmit()) {
      dataMap.put("IsAutoSubmitAndAudit", "true");
    }
    // 补丁参数解决。更新库存时出现可以忽略的异常数据是否继续？
    dataMap.put("InterationFlags", "STK_InvCheckResult");
    reqMap.put("data", dataMap);

    return JsonUtil.toJson(reqMap);
  }

  /**
   * 保存，提交，销售出库单
   *
   * @param paramMap
   * @return
   */
  public String saveSaleOutStock(Map<String, Integer> paramMap, Long auditDate, String orderNo) {

    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("formId", SAL_OUTSTOCK.getFormId());
    Map<String, Object> dataMap = new LinkedHashMap<>(8);

    Map<String, Object> modelMap = new LinkedHashMap<>(16);

    String customerId = orderNo.startsWith("LS") ? "K0002" : "K0005";

    modelMap.put("FID", 0);
    modelMap.put("FBillTypeID", Dict.create().set("FNUMBER", "XSCKD01_SYS"));
    modelMap.put("FDate", DateUtil.format(auditDate, "yyyy-MM-dd") + " 00:00:00");
    modelMap.put("FSaleOrgId", Dict.create().set("FNumber", getKingDeeConfig().getCreateOrgId()));
    modelMap.put("FCustomerID", Dict.create().set("FNumber", customerId));
    modelMap.put("FReceiverID", Dict.create().set("FNumber", customerId));
    modelMap.put("FStockOrgId", Dict.create().set("FNumber", getKingDeeConfig().getCreateOrgId()));
    modelMap.put("FSettleID", Dict.create().set("FNumber", customerId));
    modelMap.put("FPayerID", Dict.create().set("FNumber", customerId));
    modelMap.put("FOwnerTypeIdHead", "BD_OwnerOrg");
    modelMap.put("FCDateOffsetValue", 0);
    modelMap.put("FIsTotalServiceOrCost", false);

    // ----
    //        modelMap.put("FBillTypeID", Dict.create().set("FNUMBER", "XSCKD01_SYS"));
    //        modelMap.put("FDate", DateUtil.format(auditDate, "yyyy-MM-dd") + " 00:00:00");
    //        modelMap.put("FBillNo", orderNo);
    Dict orgDict = Dict.create().set("FNumber", getKingDeeConfig().getCreateOrgId());
    //        modelMap.put("FSaleOrgId", orgDict);
    //        // 商家（店铺）
    //        Dict customerDict;
    //        if (SpringUtil.getActiveProfile().equals("dev") ||
    // SpringUtil.getActiveProfile().equals("test")) {
    //            customerDict = Dict.create().set("FNumber", "TEST0720001Shop");
    //        } else {
    //            if (orderNo.startsWith("LS")) {
    //                customerDict = Dict.create().set("FNumber", "k0002");
    //            } else {
    //                customerDict = Dict.create().set("FNumber", "k0005");
    //            }
    //        }
    //        modelMap.put("FCustomerID", customerDict);
    //        modelMap.put("FReceiverID", customerDict);
    //        modelMap.put("FStockOrgId", orgDict);
    //        modelMap.put("FSettleID", customerDict);
    //        modelMap.put("FPayerID", customerDict);
    //        modelMap.put("FOwnerTypeIdHead", "BD_OwnerOrg");
    //        modelMap.put("FIsTotalServiceOrCost", false);

    //        Map<String, Object> subHeadEntityMap = new HashMap<>(8);
    //        Dict currID = Dict.create().set("FNumber", "PRE001");
    //        subHeadEntityMap.put("FSettleCurrID", currID);
    //        subHeadEntityMap.put("FSettleOrgID", orgDict);
    //        subHeadEntityMap.put("FIsIncludedTax", true);
    //        subHeadEntityMap.put("FLocalCurrID", currID);
    //        subHeadEntityMap.put("FExchangeTypeID", Dict.create().set("FNumber", "HLTX01_SYS"));
    //        subHeadEntityMap.put("FExchangeRate", 1.0);
    //        subHeadEntityMap.put("FIsPriceExcludeTax", true);
    //        modelMap.put("SubHeadEntity", subHeadEntityMap);

    List<Map<String, Object>> entityList = new LinkedList<>();
    paramMap.forEach(
        (skuCode, count) -> {
          Map<String, Object> entityMap = new HashMap<>(16);
          entityMap.put("FRowType", "Standard");
          ItemSku itemSku = itemSkuGateway.getBySkuCode(skuCode);
          if (Objects.isNull(itemSku)) {
            log.error("saveSaleOutStock itemSku is empty. skuCode:{}", skuCode);
            return;
          }
          entityMap.put("FMaterialID", Dict.create().set("FNumber", itemSku.getSkuCode()));
          String unit = iBaseUnitService.getKingDeeNum(itemSku.getUnit());
          if (StrUtil.isBlank(unit)) {
            log.error("saveSaleOutStock unit is empty. skuCode:{}", skuCode);
            return;
          }
          entityMap.put("FUnitID", Dict.create().set("FNumber", unit));
          // 实发数量
          entityMap.put("FRealQty", count.floatValue());
          // 是否赠品
          Long itemId = itemSku.getItemId();
          List<ItemProcurement> list =
              iItemProcurementService.lambdaQuery().eq(ItemProcurement::getItemId, itemId).list();
          if (CollUtil.isEmpty(list)) {
            log.error("saveSaleOutStock ItemProcurement is empty. skuCode:{}", skuCode);
            return;
          }
          ItemProcurement itemProcurement = list.get(0);
          boolean isGift =
              Objects.nonNull(itemProcurement.getIsGift()) && itemProcurement.getIsGift() == 1;
          entityMap.put("FIsFree", isGift);
          // 货主类型
          entityMap.put("FOwnerTypeID", "BD_OwnerOrg");
          // 货主id
          entityMap.put("FOwnerID", orgDict);
          // 仓库
          entityMap.put("FStockID", Dict.create().set("FNumber", itemSku.getWarehouseNo()));

          entityList.add(entityMap);
        });
    modelMap.put("FEntity", entityList);
    if (CollUtil.isEmpty(entityList)) {
      throw ExceptionFactory.bizException(
          ErrorCode.BUSINESS_OPERATE_ERROR.getCode(), "销售出库单请求参数实体列表为空");
    }
    dataMap.put("InterationFlags", "STK_InvCheckResult");
    dataMap.put("Model", modelMap);
    dataMap.put("IsAutoSubmitAndAudit", "true");
    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  /**
   * 上推销售退货单请求参数拼接
   *
   * @param paramMap
   * @return
   */
  public String saveSaleReturnStock(Map<String, Integer> paramMap, Long auditDate, String order) {

    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("formId", SAL_RETURNSTOCK.getFormId());
    Map<String, Object> dataMap = new LinkedHashMap<>(2);

    Map<String, Object> modelMap = new LinkedHashMap<>(16);
    String dateStr = DateUtil.format(auditDate, "yyyy-MM-dd") + " 00:00:00";
    Dict orgDict = Dict.create().set("FNumber", getKingDeeConfig().getCreateOrgId());

    Dict customerDict;
    if (SpringUtil.getActiveProfile().equals("dev")
        || SpringUtil.getActiveProfile().equals("test")) {
      customerDict = Dict.create().set("FNumber", "TEST0720001Shop");
    } else {
      if (order.startsWith("LS")) {
        customerDict = Dict.create().set("FNumber", "K0002");
      } else {
        customerDict = Dict.create().set("FNumber", "K0005");
      }
    }
    modelMap.put("FSaleOrgId", orgDict);
    modelMap.put("FReceiveCustId", customerDict);
    modelMap.put("FSettleCustId", customerDict);
    modelMap.put("FStockOrgId", orgDict);
    modelMap.put("FPayCustId", customerDict);
    modelMap.put("FRetcustId", customerDict);
    modelMap.put("FTransferBizType", Dict.create().set("FNumber", "OverOrgSal"));
    modelMap.put("FBillTypeID", Dict.create().set("FNUMBER", "XSTHD01_SYS"));
    modelMap.put("FDate", dateStr);
    modelMap.put("FID", 0);
    modelMap.put("FOwnerTypeIdHead", "BD_OwnerOrg");
    modelMap.put("FIsTotalServiceOrCost", false);
    //        modelMap.put("FBillNo", order);

    Map<String, Object> subHeadEntityMap = new LinkedHashMap<>(8);
    Dict currID = Dict.create().set("FNumber", "PRE001");
    subHeadEntityMap.put("FSettleCurrID", currID);
    subHeadEntityMap.put("FSettleOrgID", orgDict);
    subHeadEntityMap.put("FLocalCurrID", currID);
    subHeadEntityMap.put("FExchangeTypeID", Dict.create().set("FNumber", "HLTX01_SYS"));
    subHeadEntityMap.put("FExchangeRate", 1.0f);
    modelMap.put("SubHeadEntity", subHeadEntityMap);

    List<Map<String, Object>> entityList = new LinkedList<>();
    paramMap.forEach(
        (skuCode, count) -> {
          Map<String, Object> entityMap = new HashMap<>(16);
          entityMap.put("FRowType", "Standard");
          entityMap.put("FMaterialId", Dict.create().set("FNumber", skuCode));
          ItemSku itemSku = itemSkuGateway.getBySkuCode(skuCode);
          if (Objects.isNull(itemSku)) {
            log.error("saveSaleReturnStock itemSku is empty. skuCode:{}", skuCode);
            return;
          }
          String unit = iBaseUnitService.getKingDeeNum(itemSku.getUnit());
          if (StrUtil.isBlank(unit)) {
            log.error("saveSaleReturnStock unit is empty. skuCode:{}", skuCode);
            return;
          }
          entityMap.put("FUnitID", Dict.create().set("FNumber", unit));
          if (count < 0) {
            entityMap.put("FRealQty", new BigDecimal(count).abs().floatValue());
          } else {
            entityMap.put("FRealQty", count.floatValue());
          }
          Long itemId = itemSku.getItemId();
          List<ItemProcurement> list =
              iItemProcurementService.lambdaQuery().eq(ItemProcurement::getItemId, itemId).list();
          if (CollUtil.isEmpty(list)) {
            log.error("saveSaleReturnStock ItemProcurement is empty. skuCode:{}", skuCode);
            return;
          }
          ItemProcurement itemProcurement = list.get(0);
          boolean isGift =
              Objects.nonNull(itemProcurement.getIsGift()) && itemProcurement.getIsGift() == 1;
          entityMap.put("FIsFree", isGift);
          entityMap.put("FOwnerTypeId", "BD_OwnerOrg");
          entityMap.put("FOwnerId", orgDict);
          entityMap.put("FReturnType", Dict.create().set("FNumber", "THLX01_SYS"));
          entityMap.put("FDeliveryDate", dateStr);
          entityMap.put("FStockID", Dict.create().set("FNumber", itemSku.getWarehouseNo()));

          entityList.add(entityMap);
        });

    modelMap.put("FEntity", entityList);
    if (CollUtil.isEmpty(entityList)) {
      throw ExceptionFactory.bizException(
          ErrorCode.BUSINESS_OPERATE_ERROR.getCode(), "销售退货单请求参数实体列表为空");
    }
    dataMap.put("IsAutoSubmitAndAudit", "true");
    dataMap.put("InterationFlags", "STK_InvCheckResult");
    dataMap.put("Model", modelMap);
    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  public String saveSkuPriceCategory(Long itemSkuId) {
    Optional<ItemSku> itemSkuOptional = itemSkuGateway.getByItemSkuId(itemSkuId);
    if (!itemSkuOptional.isPresent()) {
      return "";
    }
    ItemSku itemSku = itemSkuOptional.get();
    Dict model = new Dict();
    String createId = getKingDeeConfig().getCreateOrgId();
    model
        .set("FCreateOrgId", Dict.create().set("FNumber", createId))
        .set("FName", "价目表")
        .set("FPriceType", "2")
        .set("FPriceObject", "A")
        .set("FCurrencyID", Dict.create().set("FNumber", "PRE001"));

    BigDecimal purchaseTaxRate =
        Objects.isNull(itemSku.getPurchaseTaxRate())
            ? BigDecimal.ZERO
            : itemSku.getPurchaseTaxRate();
    String unit = iBaseUnitService.getKingDeeNum(itemSku.getUnit());
    BigDecimal taxRate =
        purchaseTaxRate.multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
    BigDecimal price =
        itemSku.getCostPrice().divide(BigDecimal.ONE.add(purchaseTaxRate), 6, RoundingMode.HALF_UP);
    Dict entity =
        Dict.create()
            .set("FMaterialId", Dict.create().set("FNumber", itemSku.getSupplierCode()))
            .set("FMaterialTypeId", Dict.create().set("FNumber", "001"))
            .set("FUnitID", Dict.create().set("FNumber", unit))
            .set("FTaxPrice", itemSku.getCostPrice())
            .set("FTaxRate", taxRate)
            .set("FDownPrice", 0)
            .set("FPriceCoefficient", 1.0)
            .set("FUpPrice", 100000)
            .set("FPrice", price)
            .set("FEntryEffectiveDate", "2020-5-10 00:00:00")
            .set("FEntryExpiryDate", "2100-01-01 00:00:00");
    model.set("FPriceListEntry", ListUtil.of(entity));

    Dict date =
        Dict.create()
            .set("IsAutoSubmitAndAudit", "true")
            .set("InterationFlags", "STK_InvCheckResult")
            .set("Model", model);
    Dict req = Dict.create().set("formId", SAVE_PRICE_CATEGORY.getFormId()).set("data", date);
    return JsonUtil.toJson(req);
  }

  /**
   * 付款申请单
   *
   * @return
   */
  public String savePayBill(Long id) {
    PaymentApplyOrder order = iPaymentApplyOrderService.getById(id);
    Assert.notNull(order, "付款申请单ID查询为空。ID:" + id);
    //        Long tradeUnit = order.getTradeUnit();
    Long payeeUnit = order.getPayeeUnit();
    //        Provider tradeProvider = providerGateway.getById(tradeUnit);
    Provider payeeProvider = providerGateway.getById(payeeUnit);
    //        Assert.notNull(tradeProvider, "往来（交易）供应商ID查询为空。id:" + tradeUnit);
    Assert.notNull(payeeProvider, "收款供应商编号查询为空。id:" + payeeUnit);
    //        String date = DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd") + " 00:00:00";
    // 付款状态回传时间
    String date = DateUtil.format(order.getPaymentCallbackTime(), "yyyy-MM-dd") + " 00:00:00";
    // 人民币/美元
    String currencyType = order.getCurrencyType() == 0 ? "PRE001" : "PRE007";

    Dict model =
        Dict.create()
            // 采购业务付款单
            .set("FBillTypeID", Dict.create().set("FNUMBER", "FKDLX01_SYS"))
            // 业务日期
            .set("FDATE", date)
            // 往来单位
            .set("FCONTACTUNITTYPE", "BD_Supplier")
            .set("FCONTACTUNIT", Dict.create().set("FNumber", payeeProvider.getProviderNo()))
            // 收款单位
            .set("FRECTUNITTYPE", "BD_Supplier")
            .set("FRECTUNIT", Dict.create().set("FNumber", payeeProvider.getProviderNo()))
            .set("FISINIT", false)
            // 币别
            .set("FCURRENCYID", Dict.create().set("FNumber", currencyType))
            .set("FEXCHANGERATE", 1.0)
            .set("FSETTLERATE", 1.0)
            // 结算组织
            .set("FSETTLEORGID", Dict.create().set("FNumber", "103"))
            // 采购组织
            .set("FPURCHASEORGID", Dict.create().set("FNumber", "103"))
            .set("FDOCUMENTSTATUS", "Z")
            .set("FBUSINESSTYPE", "2")
            .set("FCancelStatus", "A")
            .set("FPAYORGID", Dict.create().set("FNumber", "103"))
            .set("FISSAMEORG", true)
            .set("FIsCredit", false)
            .set("FSETTLECUR", Dict.create().set("FNUMBER", currencyType))
            .set("FSETTLEMAINBOOKID", Dict.create().set("FNUMBER", currencyType))
            .set("FIsWriteOff", false)
            .set("FREALPAY", false)
            .set("FISCARRYRATE", false)
            .set("FSETTLEMAINBOOKID", Dict.create().set("FNUMBER", currencyType))
            .set("FMoreReceive", false)
            .set("FVirIsSameAcctOrg", false);
    Dict entry =
        Dict.create()
            // 结算方式 默认电汇
            .set("FSETTLETYPEID", Dict.create().set("FNumber", "JSFS04_SYS"))
            // 付款用途 默认采购付款
            .set("FPURPOSEID", Dict.create().set("FNumber", "SFKYT08_SYS"))
            .set("FPAYTOTALAMOUNTFOR", order.getRealPayAmount())
            .set("FPAYAMOUNTFOR_E", order.getRealPayAmount())
            .set("FSETTLEPAYAMOUNTFOR", order.getRealPayAmount())
            .set("FREALPAYAMOUNTFOR_D", order.getRealPayAmount())
            .set("FPAYAMOUNT_E", order.getRealPayAmount())
            .set("FPOSTDATE", date)
            .set("FRuZhangType", "1")
            .set("FPayType", "A")
            .set("FNOTVERIFICATEAMOUNT", order.getRealPayAmount())
            .set("FACCOUNTID", Dict.create().set("FNumber", order.getOurAccountNumber().trim()));
    model.set("FPAYBILLENTRY", ListUtil.of(entry));

    Dict data =
        Dict.create()
            .set("IsAutoSubmitAndAudit", "true")
            .set("InterationFlags", "STK_InvCheckResult")
            .set("Model", model);
    Dict req = Dict.create().set("formId", SAVE_PAY_BILL.getFormId()).set("data", data);

    return JsonUtil.toJson(req);
  }

  public static String submitStockOrder(List<String> nos, String formId) {
    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("formId", formId);
    Map<String, Object> dataMap = new HashMap<>(2);
    dataMap.put("Numbers", nos.toArray());
    dataMap.put("InterationFlags", "STK_InvCheckResult");
    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  public static String auditStockOrder(List<String> nos, String formId) {
    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("formId", formId);
    Map<String, Object> dataMap = new HashMap<>(2);
    dataMap.put("Numbers", nos.toArray());
    dataMap.put("InterationFlags", "STK_InvCheckResult");
    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  // ---------------------- 下面全部是数据脚本 ----------------------

  /**
   * 查询全量的sku reqJson
   *
   * @param startRow
   * @param limit
   * @return
   */
  public static String getQuerySkuJson(Long startRow, Long limit) {
    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("FormId", "BD_MATERIAL");

    Map<String, Object> dataMap = new HashMap<>(4);
    dataMap.put("FormId", "BD_MATERIAL");
    dataMap.put("FieldKeys", "FNumber,FName,FPurchaserId");

    Dict dict =
        Dict.create()
            .set("Left", "(")
            .set("FieldName", "FCreateOrgId")
            .set("Compare", "=")
            .set("Value", getKingDeeConfig().getFCreateOrgId())
            .set("Right", ")")
            .set("Logic", "and")
            .set("Left", "(")
            .set("FieldName", "FUseOrgId")
            .set("Compare", "=")
            .set("Value", getKingDeeConfig().getFUseOrgId())
            .set("Right", ")");
    dataMap.put("FilterString", ListUtil.of(dict));

    dataMap.put("OrderString", "");
    dataMap.put("StartRow", startRow);
    dataMap.put("Limit", limit);

    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  public static String getQuerySkuProviderJson(Long startRow, Long limit) {
    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("FormId", "BD_MATERIAL");

    Map<String, Object> dataMap = new HashMap<>(4);
    dataMap.put("FormId", "BD_MATERIAL");
    dataMap.put("FieldKeys", "FNumber,FName,FDefaultVendor");

    Dict dict =
        Dict.create()
            .set("Left", "(")
            .set("FieldName", "FCreateOrgId")
            .set("Compare", "=")
            .set("Value", getKingDeeConfig().getFCreateOrgId())
            .set("Right", ")")
            .set("Logic", "and")
            .set("Left", "(")
            .set("FieldName", "FUseOrgId")
            .set("Compare", "=")
            .set("Value", getKingDeeConfig().getFUseOrgId())
            .set("Right", ")");
    dataMap.put("FilterString", ListUtil.of(dict));

    dataMap.put("OrderString", "");
    dataMap.put("StartRow", startRow);
    dataMap.put("Limit", limit);

    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  public static String getZeroStockCostPriceSku(Long startRow, Long limit) {
    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("FormId", "BD_MATERIAL");

    Map<String, Object> dataMap = new HashMap<>(4);
    dataMap.put("FormId", "BD_MATERIAL");
    dataMap.put("FieldKeys", "FNumber,FRefCost");

    Dict dict1 =
        Dict.create()
            .set("Left", "(")
            .set("FieldName", "FRefCost")
            .set("Compare", "=")
            .set("Value", 0)
            .set("Right", ")")
            .set("Logic", "and");
    //        Dict dict2 = Dict.create()
    //                .set("Left", "(").set("FieldName", "FCreateOrgId").set("Compare",
    // "=").set("Value", getKingDeeConfig().getFCreateOrgId()).set("Right", ")")
    //                .set("Logic", "and");
    Dict dict3 =
        Dict.create()
            .set("Left", "(")
            .set("FieldName", "FUseOrgId")
            .set("Compare", "=")
            .set("Value", getKingDeeConfig().getFUseOrgId())
            .set("Right", ")")
            .set("Logic", "");
    dataMap.put("FilterString", ListUtil.of(dict1, dict3));

    dataMap.put("OrderString", "");
    dataMap.put("StartRow", startRow);
    dataMap.put("Limit", limit);

    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  /**
   * 根据skuCode查询物料列表中是否存在 100037 就是线上环境的orgId. test环境为 1
   *
   * @param skuCode
   * @return
   */
  public static String getQuerySkuByNoJson(String skuCode) {
    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("FormId", "BD_MATERIAL");

    Map<String, Object> dataMap = new HashMap<>(4);
    dataMap.put("FormId", "BD_MATERIAL");
    dataMap.put("FieldKeys", "FNumber,FName,FMATERIALID,FDefaultVendor");

    Dict dict1 =
        Dict.create()
            .set("Left", "(")
            .set("FieldName", "FNumber")
            .set("Compare", "=")
            .set("Value", skuCode.trim())
            .set("Right", ")")
            .set("Logic", "and");
    //        Dict dict2 = Dict.create()
    //                .set("Left", "(").set("FieldName", "FCreateOrgId").set("Compare",
    // "=").set("Value", getKingDeeConfig().getFCreateOrgId()).set("Right", ")")
    //                .set("Logic", "and");
    Dict dict3 =
        Dict.create()
            .set("Left", "(")
            .set("FieldName", "FUseOrgId")
            .set("Compare", "=")
            .set("Value", getKingDeeConfig().getFUseOrgId())
            .set("Right", ")")
            .set("Logic", "");
    dataMap.put("FilterString", ListUtil.of(dict1, dict3));

    dataMap.put("OrderString", "");
    dataMap.put("Limit", 1);

    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  /**
   * 根据名字查询供应商
   *
   * @param name
   * @return
   */
  public static String getQueryProvider(String name) {
    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("FormId", "BD_Supplier");

    Map<String, Object> dataMap = new HashMap<>(4);
    dataMap.put("FormId", "BD_Supplier");
    dataMap.put("FieldKeys", "FNumber,FSupplierId");

    Dict dict =
        Dict.create()
            .set("Left", "(")
            .set("FieldName", "FName")
            .set("Compare", "=")
            .set("Value", name.trim())
            .set("Right", ")")
            .set("Logic", "");
    dataMap.put("FilterString", ListUtil.of(dict));
    dataMap.put("Limit", 1);

    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  public static String getQueryProviderBankInfo(String providerNo) {
    String reqJson =
        "{\n"
            + "    \"data\": {\n"
            + "        \"FieldKeys\": \"FBankCode,FOpenBankName\",\n"
            + "        \"FormId\": \"BD_Supplier\",\n"
            + "        \"Limit\": 1,\n"
            + "        \"FilterString\": [\n"
            + "            {\n"
            + "                \"Left\": \"(\",\n"
            + "                \"FieldName\": \"FNumber\",\n"
            + "                \"Compare\": \"=\",\n"
            + "                \"Value\": \""
            + providerNo
            + "\",\n"
            + "                \"Right\": \")\",\n"
            + "                \"Logic\": \"\"\n"
            + "            }\n"
            + "        ]\n"
            + "    },\n"
            + "    \"FormId\": \"BD_Supplier\"\n"
            + "}";
    return reqJson;
  }

  /**
   * 根据仓库编验证仓库是否存在金蝶 BD_STOCK
   *
   * @param warehouseNo
   * @return
   */
  public static String getQueryWarehouse(String warehouseNo) {
    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("FormId", "BD_STOCK");

    Map<String, Object> dataMap = new HashMap<>(4);
    dataMap.put("FormId", "BD_STOCK");
    dataMap.put("FieldKeys", "FNumber");

    Dict dict =
        Dict.create()
            .set("Left", "(")
            .set("FieldName", "FNumber")
            .set("Compare", "=")
            .set("Value", warehouseNo.trim())
            .set("Right", ")")
            .set("Logic", "");
    dataMap.put("FilterString", ListUtil.of(dict));
    dataMap.put("Limit", 1);

    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  public static String getSaveWarehouse(String name, String no) {
    Map<String, Object> reqMap = new HashMap<>(4);
    reqMap.put("formid", "BD_STOCK");
    Map<String, Object> dataMap = new HashMap<>(4);
    Map<String, Object> modelMap = new HashMap<>(4);

    Dict orgDict = Dict.create().set("FNumber", getKingDeeConfig().getCreateOrgId());
    modelMap.put("FCreateOrgId", orgDict);
    modelMap.put("FUseOrgId", orgDict);

    modelMap.put("FName", name);
    modelMap.put("FNumber", no);

    dataMap.put("Model", modelMap);
    dataMap.put("IsAutoSubmitAndAudit", "true");

    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  public static String onlyUpdateSkuProvider(String skuKingDeeId, String providerNo) {
    Map<String, Object> modelMap = new HashMap<>(4);
    Map<String, Object> subHeadEntity3Map = new HashMap<>(4);
    Map<String, Object> defaultVendorMap = new HashMap<>(2);
    defaultVendorMap.put("FNumber", providerNo);
    subHeadEntity3Map.put("FDefaultVendor", defaultVendorMap);
    modelMap.put("FMATERIALID", skuKingDeeId);
    modelMap.put("SubHeadEntity3", subHeadEntity3Map);
    Map<String, Object> dataMap = new HashMap<>(4);
    dataMap.put("Model", modelMap);

    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("formid", ApiEnum.SAVE_SKU.getFormId());
    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  /**
   * @param itemSku
   * @param type 0：全部同步。1：同步供应商。2：同步buyer
   * @return
   */
  //    public String getUpdateSkuProviderAndBuyerJson(ItemSku itemSku, Integer type) {
  //        Assert.notNull(itemSku, "sku查询不得为空");
  //        Item item = itemGateway.getItem(itemSku.getItemId());
  //        Assert.notNull(item, "商品查询不得为空");
  //        Provider provider = providerGateway.getById(item.getProviderId());
  //        Assert.notBlank(provider.getProviderNo(), "供应商编码不得为空");
  //        String buyerKingDeeId = buyerGateway.getKingDeeIdByItemId(item.getId());
  //
  //        Map<String, Object> modelMap = new HashMap<>();
  //        modelMap.put("FMATERIALID", itemSku.getKingDeeId());
  //
  //        Map<String, Object> subHeadEntity3Map = new HashMap<>(4);
  //
  //        if (type == 0 || type == 1) {
  //            // 供应商
  //            Map<String, Object> defaultVendorMap = new HashMap<>(2);
  //            defaultVendorMap.put("FNumber", provider.getProviderNo());
  //            subHeadEntity3Map.put("FDefaultVendor", defaultVendorMap);
  //        }
  //
  //        if (type == 0 || type == 2) {
  //            // 采购员
  //            if (StrUtil.isNotBlank(buyerKingDeeId) && !"0".equals(buyerKingDeeId)) {
  //                Map<String, Object> purchaseMap = new HashMap<>(2);
  //                purchaseMap.put("FNumber", buyerKingDeeId);
  //                subHeadEntity3Map.put("FPurchaserId", purchaseMap);
  //            }
  //        }
  //
  //        modelMap.put("SubHeadEntity3", subHeadEntity3Map);
  //
  //        Map<String, Object> dataMap = new HashMap<>(2);
  //        dataMap.put("IsAutoSubmitAndAudit", "true");
  //        dataMap.put("Model", modelMap);
  //
  //        Map<String, Object> reqMap = new HashMap<>(2);
  //        reqMap.put("formid", "BD_MATERIAL");
  //        reqMap.put("data", dataMap);
  //        return JsonUtil.toJson(reqMap);
  //
  //    }

  /**
   * A:创建，C:审核通过 根据时间查询入库单 审核时间在targetDt的入库单。
   *
   * @param dateStr yyyy-MM-dd
   * @return
   */
  public static String getStockInOrderByTime(String dateStr) {
    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("FormId", "STK_InStock");

    Map<String, Object> dataMap = new HashMap<>(4);
    dataMap.put("FormId", "STK_InStock");
    dataMap.put("FieldKeys", "FBillNo,FDocumentStatus,FID");

    Dict dict1 =
        Dict.create()
            .set("Left", "(")
            .set("FieldName", "FAPPROVEDATE")
            .set("Compare", ">=")
            .set("Value", dateStr)
            .set("Right", ")")
            .set("Logic", "and");
    Dict dict2 =
        Dict.create()
            .set("Left", "(")
            .set("FieldName", "FAPPROVEDATE")
            .set("Compare", "<=")
            .set("Value", dateStr)
            .set("Right", ")")
            .set("Logic", "");

    dataMap.put("FilterString", ListUtil.of(dict1, dict2));
    dataMap.put("Limit", 200);

    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  public static String getStockInOrderByBillNo(String billNo) {
    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("FormId", "STK_InStock");

    Map<String, Object> dataMap = new HashMap<>(4);
    dataMap.put("FormId", "STK_InStock");
    // 入库单单据编号，审核状态，供应商id.
    dataMap.put("FieldKeys", "FBillNo,FDocumentStatus,FID");

    Dict dict1 =
        Dict.create()
            .set("Left", "(")
            .set("FieldName", "FBillNo")
            .set("Compare", "=")
            .set("Value", billNo)
            .set("Right", ")")
            .set("Logic", "");

    dataMap.put("FilterString", ListUtil.of(dict1));
    dataMap.put("Limit", 1);

    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  public static String getStockOutOrderByBillNo(String billNo) {
    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("FormId", "PUR_MRB");

    Map<String, Object> dataMap = new HashMap<>(4);
    dataMap.put("FormId", "PUR_MRB");
    // 入库单单据编号，审核状态，供应商id.
    dataMap.put("FieldKeys", "FBillNo,FDocumentStatus,FID");

    Dict dict1 =
        Dict.create()
            .set("Left", "(")
            .set("FieldName", "FBillNo")
            .set("Compare", "=")
            .set("Value", billNo)
            .set("Right", ")")
            .set("Logic", "");

    dataMap.put("FilterString", ListUtil.of(dict1));
    dataMap.put("Limit", 1);

    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  public static String updateProviderOnly(String skuKingDeeId, String providerNo) {
    return "{\n"
        + "    \"formid\": \"BD_MATERIAL\",\n"
        + "    \"data\": {\n"
        + "        \"Model\": {\n"
        + "            \"FMATERIALID\": \""
        + skuKingDeeId
        + "\",\n"
        + "            \"SubHeadEntity3\": {\n"
        + "                \"FPurchaseOrgId\": {\n"
        + "                    \"FNumber\": \"103\"\n"
        + "                },\n"
        + "                \"FDefaultVendor\": {\n"
        + "                    \"FNumber\": \""
        + providerNo
        + "\"\n"
        + "                }\n"
        + "            }\n"
        + "        }\n"
        + "    }\n"
        + "} \n";
  }

  public static String updateSkuCostPrice(String skuKingDeeId, String costPrice) {
    return "{\n"
        + "    \"formid\": \"BD_MATERIAL\",\n"
        + "    \"data\": {\n"
        + "        \"Model\": {\n"
        + "            \"FMATERIALID\": \""
        + skuKingDeeId
        + "\",\n"
        + "            \"SubHeadEntity1\":{\n"
        + "            \t\"FRefCost\":\""
        + costPrice
        + "\"\n"
        + "            },\n"
        + "            \"F_PAEZ_Text\":\""
        + costPrice
        + "\"\n"
        + "        }\n"
        + "    }\n"
        + "} \n";
  }

  // FMATERIALID,FBaseUnitId,FNumber
  public static String querySkuKingDeeInfo(String skuCode) {
    return "{\n"
        + "    \"data\": {\n"
        + "        \"FieldKeys\": \"FBaseUnitId\",\n"
        + "        \"FormId\": \"BD_MATERIAL\",\n"
        + "        \"Limit\": 1,\n"
        + "        \"FilterString\": [\n"
        + "            {\n"
        + "                \"Left\": \"(\",\n"
        + "                \"FieldName\": \"FNumber\",\n"
        + "                \"Compare\": \"=\",\n"
        + "                \"Value\": \""
        + skuCode
        + "\",\n"
        + "                \"Right\": \")\",\n"
        + "                \"Logic\": \"and\"\n"
        + "            },\n"
        + "        ],\n"
        + "        \"OrderString\": \"\"\n"
        + "    },\n"
        + "    \"FormId\": \"BD_MATERIAL\"\n"
        + "}";
  }

  public static String updateSkuCategory(String skuKingDeeId, String categoryKingDeeNum) {
    return "{\n"
        + "    \"formid\": \"BD_MATERIAL\",\n"
        + "    \"data\": {\n"
        + "        \"Model\": {\n"
        + "            \"FMATERIALID\": "
        + skuKingDeeId
        + ",\n"
        + "\t        \"FMaterialGroup\": {\n"
        + "\t            \"FNumber\": \""
        + categoryKingDeeNum
        + "\"\n"
        + "\t        }\n"
        + "        }\n"
        + "    }\n"
        + "}";
  }

  /**
   * resp,str,[[896201]] || []
   *
   * @param skuKingDeeId
   * @return
   */
  public static String querySkuPriceCategoryReqJson(String skuKingDeeId) {
    return "{\n"
        + "    \"data\": {\n"
        + "        \"FieldKeys\": \"FNumber,FMATERIALID,FTaxPrice,FTaxRate\",\n"
        + "        \"FormId\": \"PUR_PriceCategory\",\n"
        + "        \"FilterString\": [\n"
        + "            {\n"
        + "                \"Left\": \"(\",\n"
        + "                \"FieldName\": \"FMATERIALID\",\n"
        + "                \"Compare\": \"=\",\n"
        + "                \"Value\": \""
        + skuKingDeeId
        + "\",\n"
        + "                \"Right\": \")\",\n"
        + "                \"Logic\": \"and\"\n"
        + "            }\n"
        + "        ],\n"
        + "        \"Limit\": 1\n"
        + "    },\n"
        + "    \"FormId\": \"PUR_PriceCategory\"\n"
        + "}";
  }

  public static String deleteSkuPriceCategory(String billNo) {
    return "{\n"
        + "    \"data\":{\n"
        + "        \"CreateOrgId\": 0,\n"
        + "        \"Numbers\": [\n"
        + "            \""
        + billNo
        + "\"\n"
        + "        ],\n"
        + "        \"Ids\": \"\",\n"
        + "        \"NetworkCtrl\": \"\"\n"
        + "    },\n"
        + "    \"FormId\":\"PUR_PriceCategory\"\n"
        + "}\n";
  }

  public static String unAuditSkuPriceCategory(String billNo) {
    return "{\n"
        + "    \"data\": {\n"
        + "        \"CreateOrgId\": 0,\n"
        + "        \"Numbers\": [\n"
        + "            \""
        + billNo
        + "\"\n"
        + "        ],\n"
        + "        \"Ids\": \"\",\n"
        + "        \"InterationFlags\": \"\",\n"
        + "        \"IgnoreInterationFlag\": \"\",\n"
        + "        \"NetworkCtrl\": \"\",\n"
        + "        \"IsVerifyProcInst\": \"\"\n"
        + "    },\n"
        + "    \"FormId\": \"PUR_PriceCategory\"\n"
        + "}";
  }

  public static void main(String[] args) {
    System.out.println(getQueryProviderBankInfo("G00S05665"));
  }
}
