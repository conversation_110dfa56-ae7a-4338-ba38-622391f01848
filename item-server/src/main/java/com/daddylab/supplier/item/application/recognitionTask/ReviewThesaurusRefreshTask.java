package com.daddylab.supplier.item.application.recognitionTask;

import cn.hutool.core.thread.ThreadUtil;

import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerReviewThesaurusAPI;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ReviewThesaurus;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apdplat.word.dictionary.Dictionary;
import org.apdplat.word.dictionary.DictionaryFactory;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * P系统审查词库刷新任务
 *
 * <AUTHOR>
 * @since 2022/12/5
 * @deprecated 2023-09-22 由于P系统词库增加了商品适用规则，所以全局词典已经不适用了，考虑到实时请求词典增加的负载并不高，所以这边决定移除全局词典缓存
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReviewThesaurusRefreshTask implements InitializingBean {

    public static final String REVIEW_THESAURUS_REFRESH_TASK_LOCK =
            "ReviewThesaurusRefreshTask::Lock";
    private final RedissonClient redissonClient;
    private final PartnerReviewThesaurusAPI partnerReviewThesaurusAPI;
    private final ReviewThesaurusHolder reviewThesaurusHolder;

    @Override
    public void afterPropertiesSet() throws Exception {
        //ThreadUtil.execute(this::doTask);
    }

    @XxlJob("ReviewThesaurusRefreshTask")
    public void doTask() {
        final RLock lock = redissonClient.getLock(REVIEW_THESAURUS_REFRESH_TASK_LOCK);
        if (!lock.tryLock()) {
            log.warn("P系统审查词库刷新任务:加锁失败");
            return;
        }
        try {
            final Rsp<List<ReviewThesaurus>> reviewThesaurusRsp;
            try {
                reviewThesaurusRsp = partnerReviewThesaurusAPI.reviewThesaurusAll();
            } catch (Exception e) {
                XxlJobHelper.handleFail("请求P系统词库接口异常:" + e.getMessage());
                return;
            }
            final List<ReviewThesaurus> reviewThesauruses = reviewThesaurusRsp.getData();
            log.info("P系统审查词库刷新任务:词库请求成功 total={}", reviewThesauruses.size());
            reviewThesaurusHolder
                    .refresh(reviewThesauruses)
                    .ifPresent(
                            modify -> {
                                final Dictionary dictionary = DictionaryFactory.getDictionary();
                                modify.getLeft().forEach(dictionary::add);
                                log.info("P系统审查词库刷新任务:分词字典新增:{}", modify.getLeft());
                                modify.getRight().forEach(dictionary::remove);
                                log.info("P系统审查词库刷新任务:分词字典移除:{}", modify.getRight());
                            });
            log.info("P系统审查词库刷新任务:刷新完成");
        } finally {
            lock.unlock();
        }
    }
}
