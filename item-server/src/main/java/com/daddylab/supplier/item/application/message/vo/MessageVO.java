package com.daddylab.supplier.item.application.message.vo;

import com.daddylab.supplier.item.domain.message.dto.MsgFillObj;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessageState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessageType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 2:44 下午
 * @description
 */
@Data
public class MessageVO implements Serializable {


    private static final long serialVersionUID = -2307550120001797439L;

    @ApiModelProperty("消息id")
    private Long id;

    @ApiModelProperty(value = "消息类型")
    private MessageType type;

    @ApiModelProperty("消息创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "消息状态")
    private MessageState state;

    @ApiModelProperty("消息模板填充内容")
    private List<MsgFillObj> fillObjList;

    @ApiModelProperty("消息模板")
    private String template;
}
