package com.daddylab.supplier.item.controller.auth;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.auth.AuthAppService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.auth.entity.SysMenu;
import com.daddylab.supplier.item.domain.auth.types.LoginState;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

@Api(tags = "登录认证相关API")
@RestController
@RequestMapping("/auth")
@Slf4j
public class AuthController {
    @Autowired
    private AuthAppService authAppService;

    @ApiOperation("登录认证")
    @PostMapping(value = "/casAuthLogin")
    @SneakyThrows
    @Auth(noAuth = true)
    public SingleResponse<LoginState> casAuthLogin(HttpServletRequest request, @RequestParam(name = "ticket") String ticket) {
        try {
            final Optional<LoginState> loginState = authAppService.casAuthLogin(request.getRequestURL().toString(), ticket);
            if (loginState.isPresent()) {
                return SingleResponse.of(loginState.get());
            }
            throw ExceptionPlusFactory.bizException(ErrorCode.CAS_AUTH_FAIL);
        } catch (Exception e) {
            log.error("CAS_AUTH_FAIL", e);
            throw ExceptionPlusFactory.bizException(ErrorCode.CAS_AUTH_FAIL);
        }
    }

    @ApiOperation("退出登陆")
    @PostMapping(value = "/logout")
    public Response logout() {
        authAppService.logout();
        return Response.buildSuccess();

    }

    @ApiOperation("用户信息")
    @GetMapping(value = "/userInfo")
    @Auth(noAuth = true)
    public SingleResponse<UserContext.UserInfo> getUserInfo() {
        return SingleResponse.of(authAppService.getCurrentUserInfo());
    }

    @ApiOperation("用户信息")
    @GetMapping(value = "/menus")
    @Auth(noAuth = true)
    public MultiResponse<SysMenu> getMenus(@RequestParam(required = false)
                                           @ApiParam(name = "是否过滤掉没有权限的菜单", defaultValue = "false") Boolean filterNoAuth) {
        filterNoAuth = filterNoAuth != null && filterNoAuth;
        return MultiResponse.of(authAppService.getCurrentUserMenus(filterNoAuth));
    }

}
