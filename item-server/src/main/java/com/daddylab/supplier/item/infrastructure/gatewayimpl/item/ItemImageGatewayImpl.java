package com.daddylab.supplier.item.infrastructure.gatewayimpl.item;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.domain.item.gateway.ItemImageGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemImageType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/19 2:06 下午
 * @description
 */
@Service
public class ItemImageGatewayImpl implements ItemImageGateway {

    @Autowired
    IItemImageService iItemImageService;

    @Override
    public List<ItemImage> getImageListByType(Long itemId, ItemImageType itemImageType) {
        QueryWrapper<ItemImage> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ItemImage::getItemId, itemId);
        if (!ItemImageType.ALL.equals(itemImageType)) {
            wrapper.lambda().eq(ItemImage::getType, itemImageType);
        }
        return iItemImageService.list(wrapper);
    }

    @Override
    public Map<Long, List<ItemImage>> batchGetItemImagesByType(List<Long> itemIds,
            ItemImageType itemImageType) {
        QueryWrapper<ItemImage> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(ItemImage::getItemId, itemIds);
        if (!ItemImageType.ALL.equals(itemImageType)) {
            wrapper.lambda().eq(ItemImage::getType, itemImageType);
        }
        return iItemImageService.list(wrapper).stream()
                .collect(Collectors.groupingBy(ItemImage::getItemId));
    }

    @Override
    public Map<Long, String> batchGetItemMainImgUrls(Collection<Long> itemIds) {
        if (itemIds.isEmpty()) {
            return Collections.emptyMap();
        }
        QueryWrapper<ItemImage> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(ItemImage::getItemId, itemIds).eq(ItemImage::getIsMain, 1);
        final List<ItemImage> itemImages = iItemImageService.list(wrapper);
        Map<Long, String> itemMainImgUrls = new HashMap<>();
        itemImages.forEach(itemImage -> itemMainImgUrls
                .putIfAbsent(itemImage.getItemId(), itemImage.getImageUrl()));
        return itemMainImgUrls;
    }

    @Override
    public String getItemMainImgUrl(Long itemId) {
        QueryWrapper<ItemImage> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ItemImage::getItemId, itemId).eq(ItemImage::getIsMain, 1)
                .eq(ItemImage::getType, ItemImageType.COMMON).last("LIMIT 1");
        final ItemImage itemImage = iItemImageService.getOne(wrapper);
        if (itemImage != null) {
            return itemImage.getImageUrl();
        }
        return null;
    }

    @Override
    public List<ItemImage> getRunningImages(Long itemId) {
        return getImageListByType(itemId, ItemImageType.RUNNING);
    }

    @Override
    public List<ItemImage> getItemImages(Long itemId) {
        return getImageListByType(itemId, ItemImageType.COMMON);
    }

    @Override
    public void saveOrUpdateBatchImage(List<ItemImage> list) {
        iItemImageService.saveOrUpdateBatch(list);
    }

    @Override
    public void removeImages(List<Long> itemImageIds) {
        iItemImageService.removeByIds(itemImageIds);
    }
}
