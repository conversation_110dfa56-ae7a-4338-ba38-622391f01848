package com.daddylab.supplier.item.application.platformItem.converter;

import com.daddylab.supplier.item.domain.platformItem.vo.PlatformItemOpenVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Class  PlatformItemConverter
 *
 * @Date 2022/8/8上午11:51
 * <AUTHOR>
 */
@Mapper
public interface PlatformItemConverter {
    PlatformItemConverter INSTANCE = Mappers.getMapper(PlatformItemConverter.class);

    /**
     * 对象转换
     *
     * @param platformItem
     * @return
     */
    @Mappings({
            @Mapping(target = "partnerProviderItemSn", ignore = true),
            @Mapping(target = "platformItemSkus", expression = "java(com.google.common.collect.Lists.newArrayList())"),
    })
    PlatformItemOpenVO platformItemToPlatformItemVO(PlatformItem platformItem);


    /**
     * list对象转换
     *
     * @param platformItemList
     * @return
     */
    List<PlatformItemOpenVO> platformItemListToPlatformItemVOList(List<PlatformItem> platformItemList);
}
