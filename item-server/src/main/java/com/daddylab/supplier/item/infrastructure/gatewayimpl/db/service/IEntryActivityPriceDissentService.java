package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceDissent;

/**
 * <p>
 * 入驻活动价格异议 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-06
 */
public interface IEntryActivityPriceDissentService extends IDaddyService<EntryActivityPriceDissent> {

}
