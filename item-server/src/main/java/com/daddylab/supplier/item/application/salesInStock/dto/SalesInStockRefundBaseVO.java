package com.daddylab.supplier.item.application.salesInStock.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年10月23日 11:42 AM
 */
@Data
public class SalesInStockRefundBaseVO {

    @ApiModelProperty("退货单号")
    private String refundNo;

    @ApiModelProperty("退换单状态")
    private Integer status;

    @ApiModelProperty("退货仓库")
    private String returnWarehouseNo;

    @ApiModelProperty("订单编号（旺）")
    private String tradeNoList;

    @ApiModelProperty("退款金额")
    private BigDecimal actualRefundAmount;

    @ApiModelProperty("商品数量")
    private Integer returnGoodsCount;

    @ApiModelProperty("物流公司")
    private String returnLogisticsName;

    @ApiModelProperty("物流单号")
    private String returnLogisticsNo;

    @ApiModelProperty("退换说明")
    private String reasonName;

    @ApiModelProperty("退换说明")
    private String remark;

    @ApiModelProperty("退换建单人")
    private String operatorName;

}
