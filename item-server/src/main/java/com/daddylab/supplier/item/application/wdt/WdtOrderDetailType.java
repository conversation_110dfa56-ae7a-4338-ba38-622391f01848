//package com.daddylab.supplier.item.application.wdt;
//
//import lombok.AllArgsConstructor;
//import lombok.Getter;
//
///**
// * <AUTHOR> up
// * @date 2022/4/14 11:30 下午
// */
//@Getter
//@AllArgsConstructor
//public enum WdtOrderDetailType {
//
//    /**
//     * 不要黄线
//     */
//    SINGLE(1,"单个商品订单明细"),
//    SUITE(2,"组合商品名单明细"),
//    ;
//
//    private final Integer type;
//    private final String desc;
//}
