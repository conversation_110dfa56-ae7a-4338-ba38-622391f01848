/*
package com.daddylab.supplier.item.application.virtualWarehouse.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryMode;
import lombok.Data;

import java.util.List;

*/
/**
 * <AUTHOR> up
 * @date 2024年03月04日 3:53 PM
 *//*

@Data
public class VirtualWarehousePageVo {

    private Long virtualWarehouseId;

    private String virtualWarehouseNo;

    private String virtualWarehouseName;

    private Integer status;

    private Integer businessLine;

    private Integer warehouseQuantity;

    private Integer spuQuantity;

    private List<VirtualWarehousePageDetailVo> detailVoList;

    private InventoryMode inventoryMode;

}
*/
