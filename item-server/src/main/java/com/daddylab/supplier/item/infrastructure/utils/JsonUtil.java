package com.daddylab.supplier.item.infrastructure.utils;

import cn.hutool.core.date.DatePattern;
import com.daddylab.supplier.item.common.domain.EntityChange;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import java.io.IOException;
import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Iterator;
import java.util.List;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.javers.core.diff.Diff;

/**
 * json隔离 工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/29 11:56 上午
 * @description
 */
@Slf4j
public class JsonUtil {
    /**
     * 定义jackson对象
     */
    public static final ObjectMapper MAPPER = new ObjectMapper();

    static {
        //注册JSR310（LocalDateTime）
        MAPPER.registerModule(new JavaTimeModule());
        final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
        MAPPER.registerModule(new SimpleModule() {
            {
                addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter));
                addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(dateTimeFormatter));
            }
        });

        //定义不要将日期对象写为时间戳
        MAPPER.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

        //忽略未定义字段
        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        //如果是空对象的时候,不抛异常
        MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

        //json转bean时忽略大小写
        MAPPER.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);

        //循环引用时不抛异常，写入NULL
        MAPPER.configure(SerializationFeature.FAIL_ON_SELF_REFERENCES, false);
        MAPPER.configure(SerializationFeature.WRITE_SELF_REFERENCES_AS_NULL, true);
    }

    public static String objToStr(Object obj) {
        try {
            if (Objects.isNull(obj)) {
                return "";
            }
            if (obj instanceof Diff || obj instanceof EntityChange) {
                return DiffUtil.toJson(obj);
            }
            return MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new IllegalStateException(e);
        }
    }

    /**
     * 对象转另一个对象
     *
     * @param obj Object
     * @param classType Class<T>
     * @return T
     * @date 2024/3/1 17:41
     * <AUTHOR>
     */
    public static <T> T objToObj(Object obj, Class<T> classType) {
        T result = null;
        try {
            result = MAPPER.convertValue(obj, classType);
        } catch (Exception e) {
            throw new IllegalStateException(e);
        }
        return result;
    }

    public static <T> T parseJsonStr(String jsonStr, Class<T> clazz) {
        try {
            if (clazz.isAssignableFrom(Diff.class) || clazz.isAssignableFrom(EntityChange.class)) {
                return DiffUtil.fromJson(jsonStr, clazz);
            }
            return MAPPER.readValue(jsonStr, clazz);
        } catch (JsonProcessingException e) {
            throw new IllegalStateException(e);
        }
    }

    public static String toJson(Object obj) {
        if (Objects.isNull(obj)) {
            return "";
        }
        return objToStr(obj);
    }

    public static String toJson(Object obj, boolean pretty) {
        if (Objects.isNull(obj)) {
            return "";
        }
        try {
            if (pretty) {
                return MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
            }
        } catch (JsonProcessingException e) {
            throw new IllegalStateException(e);
        }
        return objToStr(obj);
    }

    public static <T> T parse(String jsonStr, Type type) {
        try {
            return StringUtil.isBlank(jsonStr) ? null : MAPPER.readValue(jsonStr, MAPPER.getTypeFactory().constructType(type));
        } catch (JsonProcessingException e) {
            throw new IllegalStateException(e);
        }
    }

    public static <T> List<T> parseList(String jsonStr, Class<T> beanType) {
        if (jsonStr == null || jsonStr.isEmpty()) return null;
        try {
            JavaType javaType = MAPPER.getTypeFactory().constructParametricType(List.class, beanType);
            return MAPPER.readValue(jsonStr, javaType);
        } catch (Exception e) {
            throw new IllegalStateException(e);
        }
    }


    public static <T> T parse(byte[] data, Type type) {
        try {
            return data == null || data.length == 0 ? null : MAPPER.readValue(data, MAPPER.getTypeFactory().constructType(type));
        } catch (IOException e) {
            throw new IllegalStateException(e);
        }
    }

    public static <T> T parse(String jsonStr, Class<T> clazz) {
        try {
            if (clazz.isAssignableFrom(Diff.class) || clazz.isAssignableFrom(EntityChange.class)) {
                return DiffUtil.fromJson(jsonStr, clazz);
            }
            return StringUtil.isBlank(jsonStr) ? null : MAPPER.readValue(jsonStr, clazz);
        } catch (JsonProcessingException e) {
            throw new IllegalStateException(e);
        }
    }

    public static <T> T parse(String jsonStr, TypeReference<T> typeReference) {
        try {
            if (StringUtil.isBlank(jsonStr)) return null;
            return MAPPER.readValue(jsonStr, typeReference);
        } catch (JsonProcessingException e) {
            throw new IllegalStateException(e);
        }
    }

    public static JsonNode parse(String jsonStr) {
        try {
            if (StringUtil.isBlank(jsonStr)) return null;
            return MAPPER.readTree(jsonStr);
        } catch (JsonProcessingException e) {
            throw new IllegalStateException(e);
        }
    }

    public static JsonNode parse(byte[] data) {
        try {
            if (data.length == 0) return null;
            return MAPPER.readTree(data);
        } catch (IOException e) {
            throw new IllegalStateException(e);
        }
    }

    public static JsonNode mapRecursive(JsonNode node,
            Function<JsonNode, JsonNode> mappingFunction) {
        final JsonNodeFactory instance = JsonNodeFactory.instance;
        if (node.isObject()) {
            final ObjectNode jsonNodes = instance.objectNode();
            final Iterator<Entry<String, JsonNode>> fields = node.fields();
            while (fields.hasNext()) {
                final Entry<String, JsonNode> subNode = fields.next();
                jsonNodes.set(subNode.getKey(), mapRecursive(subNode.getValue(), mappingFunction));
            }
            return jsonNodes;
        } else if (node.isArray()) {
            final ArrayNode jsonNodes = instance.arrayNode();
            for (JsonNode subNode : node) {
                jsonNodes.add(mapRecursive(subNode, mappingFunction));
            }
            return jsonNodes;
        } else {
            return mappingFunction.apply(node);
        }

    }



}
