package com.daddylab.supplier.item.domain.operateLog.entity;

import cn.hutool.core.bean.BeanDesc;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.PropDesc;
import cn.hutool.core.map.MapUtil;
import com.daddylab.supplier.item.common.domain.EntityChange;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.domain.LogDetails;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.javers.core.diff.Diff;
import org.javers.core.diff.changetype.PropertyChange;
import org.javers.core.diff.changetype.ValueChange;
import org.javers.core.diff.changetype.container.CollectionChange;
import org.javers.core.metamodel.annotation.PropertyName;

@Slf4j
public class ItemChangeBuffers {

    public static final String DEFAULT_PREFIX = "修改了";
    private static final ConcurrentHashMap<Long, List<EntityChange<?>>> changeBuffers = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<Long, ConcurrentHashMap<OperateLogTarget, List<String>>> messageBuffers = new ConcurrentHashMap<>();
    public static final Map<OperateLogTarget, String> LOG_PREFIX_MAP = MapUtil.<OperateLogTarget, String>builder()
            .put(OperateLogTarget.ITEM, "修改了")
            .put(OperateLogTarget.ITEM_STATUS, "")
            .put(OperateLogTarget.ITEM_UP, "")
            .put(OperateLogTarget.ITEM_DOWN, "")
            .put(OperateLogTarget.ITEM_DOWN_TEMPORARY, "")
            .build();

    public static void addChange(Long itemId, EntityChange<?> entityChange) {
        getChanges(itemId).add(entityChange);
    }

    public static void addChange(Long itemId, String message) {
        addChange(itemId, message, OperateLogTarget.ITEM);
    }

    public static void addChange(Long itemId, String message, OperateLogTarget operateLogTarget) {
        if (StringUtil.isNotBlank(message)) {
            getMessages(itemId, operateLogTarget).add(message);
        }
    }

    private static ConcurrentHashMap<OperateLogTarget, List<String>> getMessages(Long itemId) {
        return messageBuffers.computeIfAbsent(itemId, v -> new ConcurrentHashMap<>());
    }

    private static List<String> getMessages(Long itemId, OperateLogTarget operateLogTarget) {
        return getMessages(itemId).computeIfAbsent(operateLogTarget, v -> new ArrayList<>());
    }

    private static List<EntityChange<?>> getChanges(Long itemId) {
        changeBuffers.putIfAbsent(itemId, new ArrayList<>());
        return changeBuffers.get(itemId);
    }

    public static void clear(Long itemId) {
        changeBuffers.remove(itemId);
        messageBuffers.remove(itemId);
    }

    /**
     * 记录操作变更后必须提交，否则会出现内存泄漏
     *
     * @param itemId 商品ID
     */
    public static void submitChange(Long itemId) {
        if (changeBuffers.isEmpty() && messageBuffers.isEmpty()) {
            return;
        }
        try {
            final List<EntityChange<?>> entityChanges = getChanges(itemId);
            for (EntityChange<?> change : entityChanges) {
                final String entityName = change.getEntityName();
                String msg;
                if (StringUtil.isNotBlank(entityName)) {
                    msg = entityName;
                } else {
                    final Diff diff = change.getDiff();
                    if (!diff.hasChanges()) {
                        continue;
                    }
                    final BeanDesc beanDesc = BeanUtil.getBeanDesc(Item.class);
                    final HashMap<String, PropDesc> props = new HashMap<>();
                    for (PropDesc prop : beanDesc.getProps()) {
                        props.put(prop.getRawFieldName(), prop);
                        final PropertyName propertyName = prop.getField()
                                .getAnnotation(PropertyName.class);
                        if (propertyName != null) {
                            props.put(propertyName.value(), prop);
                        }
                    }
                    msg = diff
                            .getChangesByType(PropertyChange.class)
                            .stream()
                            .map(v -> {
                                final PropDesc propDesc = props.get(v.getPropertyName());
                                if (propDesc != null) {
                                    final LogDetails logDetails = propDesc.getField()
                                            .getAnnotation(LogDetails.class);
                                    if (logDetails != null && logDetails.valueChange()) {
                                        if (v instanceof ValueChange) {
                                            return StringUtil
                                                    .format("{}({} -> {})", v.getPropertyName(),
                                                            ((ValueChange) v).getLeft(),
                                                            ((ValueChange) v).getRight());
                                        } else if (v instanceof CollectionChange) {
                                            return StringUtil
                                                    .format("{}(++: {}, --: {})",
                                                            v.getPropertyName(),
                                                            ((CollectionChange<?>) v).getAddedValues(),
                                                            ((CollectionChange<?>) v).getRemovedValues());
                                        }
                                    }
                                }
                                return v.getPropertyName();
                            })
                            .distinct()
                            .collect(Collectors.joining("、"));
                }
                getMessages(itemId, OperateLogTarget.ITEM).add(msg);
            }
            final OperateLogDomainService operateLogDomainService = ApplicationContextUtil
                    .getBean(OperateLogDomainService.class);
            getMessages(itemId).forEach((operateLogTarget, messages) -> {
                StringJoiner sj = new StringJoiner("、", LOG_PREFIX_MAP.getOrDefault(operateLogTarget, DEFAULT_PREFIX),
                        "");
                for (String message : messages) {
                    if (StringUtil.isNotBlank(message)) {
                        sj.add(message);
                    }
                }
                final String msg = sj.toString();
                if (StringUtil.equals(msg, DEFAULT_PREFIX)) {
                    return;
                }
                operateLogDomainService
                        .addOperatorLog(UserContext.getUserId(), operateLogTarget, itemId, msg);
            });
        } finally {
            clear(itemId);
        }

    }
}
