package com.daddylab.supplier.item.domain.stockInOrder.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockInState;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

@Data
@ApiModel("采购入库单查看返回数据模型")
public class StockInOrderAndOrderDetailVo {
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Long createdAt;
    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long createdUid;
    /**
     * 创建人 (前端)
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 是否已删除
     */
    @ApiModelProperty(value = "是否已删除")
    private Integer isDel;
    /**
     * 入库单号
     */
    @ApiModelProperty(value = "入库单号")
    private String no;
    /**
     * 关联采购单号
     */
    @ApiModelProperty(value = "关联采购单号")
    private String purchaseOrderNo;
    /**
     * 关联采购单id
     */
    @ApiModelProperty(value = "关联采购单id")
    private Long purchaseOrderId;
    /**
     * 供应商id
     */
    @ApiModelProperty(value = "供应商id")
    private Long providerId;
    /**
     * P系统供应商id
     */
    @ApiModelProperty(value = "P系统供应商id")
    private Long partnerProviderId;
    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String provider;
    /**
     * 收料(入库)日期
     */
    @ApiModelProperty(value = "收料(入库)日期")
    private String receiptTime;
    /**
     * 交货(入库)总数量
     */
    @ApiModelProperty(value = "交货(入库)总数量")
    private Integer totalReceiptQuantity;
    /**
     * 采购数量
     */
    @ApiModelProperty(value = "采购数量")
    private Integer purchaseQuantity;
    /**
     * 入库状态，1.待提交、2.待入库、3.已入库、4.已取消
     */
    @ApiModelProperty(value = "入库状态，1.待提交、2.待入库、3.已入库、4.已取消")
    private Integer state;
    /**
     * 收料组织id
     */
    @ApiModelProperty(value = "收料组织id")
    private Long organizationId;
    /**
     * 收料组织 (前端)
     */
    @ApiModelProperty(value = "收料组织")
    private String organization;
    /**
     * 采购员id
     */
    @ApiModelProperty(value = "采购员id")
    private Long buyerUserId;
    /**
     * 采购员 (前端)
     */
    @ApiModelProperty(value = "采购员")
    private String buyerUser;
    /**
     * 采购组织id
     */
    @ApiModelProperty(value = "采购组织id")
    private Long purchaseOrganizationId;
    /**
     * 采购组织 (前端)
     */
    @ApiModelProperty(value = "采购组织")
    private String purchaseOrganization;
    /**
     * 采购部门id
     */
    @ApiModelProperty(value = "采购部门id")
    private Long purchaseDepartmentId;
    /**
     * 采购部门 (前端)
     */
    @ApiModelProperty(value = "采购部门")
    private String purchaseDepartment;
    /**
     * 采购组id
     */
    @ApiModelProperty(value = "采购组id")
    private Long purchaseGroupId;
    /**
     * 采购组 (前端)
     */
    @ApiModelProperty(value = "采购组")
    private String purchaseGroup;
    /**
     * 入库仓库id
     */
    @ApiModelProperty(value = "入库仓库id")
    private String warehouseNo;
    /**
     * 入库仓库 (前端)
     */
    @ApiModelProperty(value = "入库仓库")
    private String storage;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否同步旺店通，0不推送。1推送
     */
    @ApiModelProperty(value = "是否同步旺店通，0不推送。1推送")
    private Integer isSyncWdt;
    /**
     * 同步旺店通情况下，旺店通返回的入库单号
     */
    @ApiModelProperty(value = "同步旺店通情况下，旺店通返回的入库单号")
    private String wdtStorageNo;
    /**
     * 同步失败明细
     **/
    @ApiModelProperty(value = "同步失败明细")
    private String syncFailDetail;
    /**
     * 收料明细
     */
    @ApiModelProperty(value = "收料明细列表")
    private List<StockInOrderDetailVo> stockInOrderDetails;

    private Integer businessLine;

    @ApiModelProperty("是否黑名单 0否 1是")
    private Integer isBlacklist;

    /**
     * 冲销状态
     */
    @ApiModelProperty(value = "操作类型 0默认，REVERSE_FIXED：逆向对冲单据，WRITE_OFF：原单据作废")
    private StockInState hedgeStatus;



}
