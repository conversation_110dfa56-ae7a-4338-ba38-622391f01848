package com.daddylab.supplier.item.application.warehouse;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.ExceptionFactory;
import com.alibaba.excel.EasyExcel;
import com.daddylab.supplier.item.application.afterSales.AfterSalesWarehouseBizService;
// import com.daddylab.supplier.item.application.virtualWarehouse.dto.LockSkuRatioDto;
// import com.daddylab.supplier.item.application.virtualWarehouse.dto.SkuStockChangeDto;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.controller.common.dto.OrderPersonnelCmd;
import com.daddylab.supplier.item.controller.common.dto.WarehouseImportDo;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.region.entity.RegionTree;
import com.daddylab.supplier.item.domain.region.enums.RegionLevel;
import com.daddylab.supplier.item.domain.region.service.RegionDomainService;
import com.daddylab.supplier.item.domain.region.vo.Region;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
// import
// com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.VirtualWarehouseInventoryMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.warehouse.EditWarehouseCmd;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2022/4/22
 */
@Service
@Slf4j
public class WarehouseBizServiceImpl implements WarehouseBizService {

  public static final int CACHE_SECONDS = 60;
  public static final int CACHE_MAXIMUM_SIZE = 100;

  @Autowired IWarehouseService warehouseService;

  @Autowired private AfterSalesWarehouseBizService afterSalesWarehouseBizService;

  //  @Resource IWarehouseGoodsInventoryLockStaticsService goodsInventoryLockStaticsService;

  //  @Resource VirtualWarehouseInventoryMapper virtualWarehouseInventoryMapper;

  //  @Resource IVirtualWarehouseInventoryGoodsService iVirtualWarehouseInventoryGoodsService;
  //  @Resource IVirtualWarehouseInventoryService iVirtualWarehouseInventoryService;

  //  @Resource IShopInventoryGoodsService iShopInventoryGoodsService;
  //  @Resource IShopInventoryService iShopInventoryService;
  //
  //  @Resource IVirtualWarehouseService iVirtualWarehouseService;
  //
  //  @Resource IWarehouseGoodsInventoryStaticsService iWarehouseGoodsInventoryStaticsService;

  @Autowired IAdditionalService iAdditionalService;

  @Autowired UserGateway userGateway;

  @Autowired IWarehouseAfterSalesAddressService iWarehouseAfterSalesAddressService;

  @Autowired RegionDomainService regionDomainService;
  @Autowired OperateLogDomainService operateLogDomainService;

  LoadingCache<String, Optional<Warehouse>> warehouseLoadingCache =
      Caffeine.newBuilder()
          .expireAfterAccess(CACHE_SECONDS, TimeUnit.SECONDS)
          .maximumSize(CACHE_MAXIMUM_SIZE)
          .build(this::getWarehouse0);

  @Override
  public Optional<Long> warehouseId(String warehouseNo) {
    return cacheGetWarehouse(warehouseNo, false).map(Warehouse::getId);
  }

  @Override
  public Optional<Warehouse> getWarehouse(String warehouseNo) {
    warehouseLoadingCache.invalidate(warehouseNo);
    return warehouseLoadingCache.get(warehouseNo);
  }

  private Optional<Warehouse> getWarehouse0(String warehouseNo) {
    return warehouseService.lambdaQuery().eq(Warehouse::getNo, warehouseNo).oneOpt();
  }

  @Override
  public Optional<Warehouse> cacheGetWarehouse(String warehouseNo, boolean refresh) {
    return warehouseLoadingCache.get(warehouseNo);
  }

  @Override
  public void editWarehouse(EditWarehouseCmd cmd) {
    String warehouseNo = cmd.getWarehouseNo();
    Optional<Warehouse> warehouse0 = getWarehouse0(warehouseNo);
    if (warehouse0.isPresent()) {
      Warehouse warehouse = warehouse0.get();
      if (warehouse.getIsVirtualWarehouse() == 1) {
        throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "请去虚拟仓业态编辑虚拟仓数据");
      }
    } else {
      throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "仓库编码异常");
    }

    List<String> logs = new LinkedList<>();
    final String orderPersonnel = warehouse0.get().getOrderPersonnel();
    List<Long> oldOrderPersons = new LinkedList<>();
    if (StringUtil.isNotBlank(orderPersonnel)) {
      oldOrderPersons =
          Stream.of(orderPersonnel.split(",")).map(Long::parseLong).collect(Collectors.toList());
    }
    final List<Long> newOrderPersons =
        Objects.isNull(cmd.getOrderPersonnelIds())
            ? new LinkedList<>()
            : cmd.getOrderPersonnelIds();
    final DiffUtil.LangDiffRes<Long> longLangDiffRes =
        DiffUtil.diffLangObjList(oldOrderPersons, newOrderPersons, Long.class);
    if (CollectionUtils.isNotEmpty(longLangDiffRes.getAddList())) {
      final Map<Long, StaffInfo> longStaffInfoMap =
          userGateway.batchQueryStaffInfoByIds(longLangDiffRes.getAddList());
      if (CollUtil.isNotEmpty(longStaffInfoMap)) {
        logs.add(
            "添加售后客服。"
                + StringUtil.join(
                    ",",
                    longStaffInfoMap.values().stream()
                        .map(StaffInfo::getNickname)
                        .collect(Collectors.joining("，"))));
      }
    }
    if (CollectionUtils.isNotEmpty(longLangDiffRes.getRemoveList())) {
      final Map<Long, StaffInfo> longStaffInfoMap =
          userGateway.batchQueryStaffInfoByIds(longLangDiffRes.getAddList());
      if (CollUtil.isNotEmpty(longStaffInfoMap)) {
        logs.add(
            "移除售后客服。"
                + StringUtil.join(
                    ",",
                    longStaffInfoMap.values().stream()
                        .map(StaffInfo::getNickname)
                        .collect(Collectors.joining("，"))));
      }
    }
    if (CollectionUtils.isNotEmpty(logs)) {
      operateLogDomainService.addOperatorLog(
          UserContext.getUserId(),
          OperateLogTarget.WAREHOUSE,
          warehouse0.get().getId(),
          StringUtil.join("；", logs),
          null);
    }

    afterSalesWarehouseBizService.editWarehouseAfterSalesInfoList(
        warehouse0.get(), cmd.getDeleteAddressIds(), cmd.getAfterSalesAddressInfoList());

    final String orderPersonnelIds = StringUtil.join(",", cmd.getOrderPersonnelIds());
    final String businessLinesStr = StringUtil.join(",", cmd.getBusinessLine());
    String afterSaleStaffIdStr = StringUtil.join(",", cmd.getAfterSaleStaffIds());
    if (StringUtil.isNotBlank(orderPersonnelIds)
        || StringUtil.isNotBlank(businessLinesStr)
        || StringUtil.isNotBlank(afterSaleStaffIdStr)) {
      warehouseService
          .lambdaUpdate()
          .set(
              StringUtil.isNotBlank(orderPersonnelIds),
              Warehouse::getOrderPersonnel,
              orderPersonnelIds)
          .set(
              StringUtil.isNotBlank(businessLinesStr), Warehouse::getBusinessLine, businessLinesStr)
          .set(
              StringUtil.isNotBlank(afterSaleStaffIdStr),
              Warehouse::getAfterSaleStaffStr,
              afterSaleStaffIdStr)
          .eq(Warehouse::getNo, cmd.getWarehouseNo())
          .update();
    }
  }

  //  @Override
  //  public void refreshSharedGoodsInventoryRatio(
  //      Collection<Long> ignoreVwIdList, Collection<Long> ignoreShopIdList, String warehouseNo) {
  //    // 此仓库下全部SKU的已锁定占比值
  //    final Map<String, Integer> skuLockRatioMap =
  //        goodsInventoryLockStaticsService.occpuyRatio(warehouseNo);
  //
  //    List<VirtualWarehouseInventoryGoods> vwGoodsList = new LinkedList<>();
  //    List<ShopInventoryGoods> shopGoodsList = new LinkedList<>();
  //
  //    if (CollUtil.isNotEmpty(ignoreVwIdList)) {
  //      // 和此实仓关联的所有共享模式的正常状态的虚拟仓数据
  //      final List<VirtualWarehouseInventory> virtualWarehouseInventoryList =
  //          virtualWarehouseInventoryMapper.selectNoramlSharedList(warehouseNo, ignoreVwIdList);
  //      if (CollUtil.isNotEmpty(virtualWarehouseInventoryList)) {
  //        iVirtualWarehouseInventoryGoodsService
  //            .lambdaQuery()
  //            .in(
  //                VirtualWarehouseInventoryGoods::getVirtualWarehouseInventoryId,
  //                virtualWarehouseInventoryList.stream()
  //                    .map(VirtualWarehouseInventory::getId)
  //                    .collect(Collectors.toList()))
  //            .list()
  //            .forEach(
  //                goodsOne -> {
  //                  final Integer lockRatio = skuLockRatioMap.get(goodsOne.getSkuNo());
  //                  if (Objects.nonNull(lockRatio)) {
  //                    goodsOne.setInventoryRatio2(
  //                        calculateRatio2(lockRatio, goodsOne.getInventoryRatio()));
  //                    vwGoodsList.add(goodsOne);
  //                  }
  //                });
  //      }
  //    }
  //
  //    if (CollUtil.isNotEmpty(ignoreShopIdList)) {
  //      final List<ShopInventory> shopInventoryList =
  //          iShopInventoryService
  //              .lambdaQuery()
  //              .eq(ShopInventory::getWarehouseNo, warehouseNo)
  //              .eq(ShopInventory::getInventoryMode, InventoryMode.SHARED)
  //              .notIn(ShopInventory::getShopId, ignoreShopIdList)
  //              .list();
  //      if (CollUtil.isNotEmpty(shopInventoryList)) {
  //        iShopInventoryGoodsService
  //            .lambdaQuery()
  //            .in(
  //                ShopInventoryGoods::getShopInventoryId,
  //
  // shopInventoryList.stream().map(ShopInventory::getId).collect(Collectors.toList()))
  //            .list()
  //            .forEach(
  //                goodsOne -> {
  //                  final Integer lockRatio = skuLockRatioMap.get(goodsOne.getSkuNo());
  //                  if (Objects.nonNull(lockRatio)) {
  //                    goodsOne.setInventoryRatio2(
  //                        calculateRatio2(lockRatio, goodsOne.getInventoryRatio()));
  //                    shopGoodsList.add(goodsOne);
  //                  }
  //                });
  //      }
  //    }
  //
  //    if (CollUtil.isNotEmpty(vwGoodsList)) {
  //      iVirtualWarehouseInventoryGoodsService.updateBatchById(vwGoodsList);
  //    }
  //    if (CollUtil.isNotEmpty(shopGoodsList)) {
  //      iShopInventoryGoodsService.updateBatchById(shopGoodsList);
  //    }
  //  }

  /**
   * 重新计算二级占比` 转换占比的核心目的是保证其他非独占的SKU库存占比在用重新生成的占比，计算实际库存的时候，实际库存数据不变。 当然因为小数点的存在，会有一些误差。
   *
   * @param lockedRatio 已经锁定占比
   * @param originalRatio 原二级占比值
   * @return
   */
  private static BigDecimal calculateRatio2(Integer lockedRatio, Integer originalRatio) {
    if (lockedRatio >= 100) {
      return BigDecimal.ZERO;
    }
    BigDecimal remindRatio = new BigDecimal(100 - lockedRatio);
    return new BigDecimal(originalRatio)
        .divide(remindRatio, 6, RoundingMode.HALF_UP)
        .multiply(new BigDecimal(100))
        .setScale(6, RoundingMode.HALF_UP);
  }

  public static void main(String[] args) {
    System.out.println(calculateRatio2(80, 10));
  }

  //  @Override
  //  public void stockSkuChangeHandler(
  //      String warehouseNo, Collection<SkuStockChangeDto> skuCodes, Integer type) {
  //
  //    final List<VirtualWarehouseInventory> virtualWarehouseInventoryList =
  //        iVirtualWarehouseInventoryService
  //            .lambdaQuery()
  //            .eq(VirtualWarehouseInventory::getWarehouseNo, warehouseNo)
  //            .list();
  //    // 此仓库存在关联的虚拟仓
  //    if (CollUtil.isNotEmpty(virtualWarehouseInventoryList)) {
  //      final List<Long> vwIdList =
  //          virtualWarehouseInventoryList.stream()
  //              .map(VirtualWarehouseInventory::getVirtualWarehouseId)
  //              .collect(Collectors.toList());
  //      final Map<Long, VirtualWarehouse> vwMap =
  //          iVirtualWarehouseService
  //              .lambdaQuery()
  //              .in(VirtualWarehouse::getId, vwIdList)
  //              .list()
  //              .stream()
  //              .collect(Collectors.toMap(VirtualWarehouse::getId, Function.identity()));
  //      final Map<Long, VirtualWarehouseInventory> vwInventoyMap =
  //          virtualWarehouseInventoryList.stream()
  //              .collect(
  //                  Collectors.toMap(
  //                      VirtualWarehouseInventory::getVirtualWarehouseId, Function.identity()));
  //      final List<VirtualWarehouseInventoryGoods> allGoodsList =
  //          iVirtualWarehouseInventoryGoodsService
  //              .lambdaQuery()
  //              .in(VirtualWarehouseInventoryGoods::getVirtualWarehouseId, vwIdList)
  //              .eq(VirtualWarehouseInventoryGoods::getWarehouseNo, warehouseNo)
  //              .list();
  //      // 查询全部关联到的虚拟仓的二级库存占比数据
  //      if (CollUtil.isNotEmpty(allGoodsList)) {
  //        allGoodsList.stream()
  //
  // .collect(Collectors.groupingBy(VirtualWarehouseInventoryGoods::getVirtualWarehouseId))
  //            .forEach(
  //                (Long vwId, List<VirtualWarehouseInventoryGoods> vwGoodsList) -> {
  //                  // 某个虚拟仓下存在二级库存占比
  //                  if (CollUtil.isNotEmpty(vwGoodsList)) {
  //                    final VirtualWarehouse virtualWarehouse = vwMap.get(vwId);
  //                    final VirtualWarehouseInventory virtualWarehouseInventory =
  //                        vwInventoyMap.get(vwId);
  //                    if (Objects.nonNull(virtualWarehouse)
  //                        && Objects.nonNull(virtualWarehouseInventory)) {
  //
  //                      // 添加这批新增/删除这批SKU的处理逻辑
  //                      oneVirtualWarehouseHandlerChangeHandler(
  //                          virtualWarehouse.getId(),
  //                          virtualWarehouse.getNo(),
  //                          virtualWarehouseInventory,
  //                          vwGoodsList,
  //                          skuCodes,
  //                          type);
  //                    }
  //                  }
  //                });
  //      }
  //    }
  //
  //    // 此实仓关联到的所有店铺占比数据
  //    final List<ShopInventory> shopInventoryList =
  //        iShopInventoryService.lambdaQuery().eq(ShopInventory::getWarehouseNo,
  // warehouseNo).list();
  //    if (CollUtil.isNotEmpty(shopInventoryList)) {
  //      final Map<Long, ShopInventory> shopInventoryMap =
  //          shopInventoryList.stream()
  //              .collect(Collectors.toMap(ShopInventory::getShopId, Function.identity()));
  //      final List<Long> shopIdList =
  //          shopInventoryList.stream().map(ShopInventory::getShopId).collect(Collectors.toList());
  //      final Map<Long, List<ShopInventoryGoods>> shopInventoryGoodsMap =
  //          iShopInventoryGoodsService
  //              .lambdaQuery()
  //              .in(ShopInventoryGoods::getShopId, shopIdList)
  //              .eq(ShopInventoryGoods::getWarehouseNo, warehouseNo)
  //              .list()
  //              .stream()
  //              .collect(Collectors.groupingBy(ShopInventoryGoods::getShopId));
  //      shopInventoryGoodsMap.forEach(
  //          (shopId, goodsList) -> {
  //            final ShopInventory shopInventory = shopInventoryMap.get(shopId);
  //            if (Objects.nonNull(shopInventory)) {
  //
  //              oneShopHandlerChangeHandler(shopId, shopInventory, goodsList, skuCodes, type);
  //            }
  //          });
  //    }
  //  }

  //  private void oneVirtualWarehouseHandlerChangeHandler(
  //      Long vwId,
  //      String vwNo,
  //      VirtualWarehouseInventory virtualWarehouseInventory,
  //      List<VirtualWarehouseInventoryGoods> oldGoodsList,
  //      Collection<SkuStockChangeDto> skuCodes,
  //      int type) {
  //    boolean lock = InventoryMode.LOCK.equals(virtualWarehouseInventory.getInventoryMode());
  //
  //    List<VirtualWarehouseInventoryGoods> addGoodsList = new LinkedList<>();
  //    List<EditSkuRatioDto> editSkuRatioDtoList = new LinkedList<>();
  //    List<LockSkuRatioDto> lockSkuRatioDtoList = new LinkedList<>();
  //    final Set<String> oldSkuCodeSet =
  //        oldGoodsList.stream()
  //            .map(VirtualWarehouseInventoryGoods::getSkuNo)
  //            .collect(Collectors.toSet());
  //
  //    if (CollUtil.isNotEmpty(oldSkuCodeSet)) {
  //      skuCodes =
  //          skuCodes.stream()
  //              .filter(val -> !oldSkuCodeSet.contains(val.getSkuCode()))
  //              .collect(Collectors.toSet());
  //    }
  //
  //    log.info(
  //        "虚拟仓仓库新增SKU数据触发，vwId:{},warehouseNo:{},skuCode:{}",
  //        vwId,
  //        virtualWarehouseInventory.getWarehouseNo(),
  //        JsonUtil.toJson(skuCodes));
  //
  //    for (SkuStockChangeDto skuDto : skuCodes) {
  //      VirtualWarehouseInventoryGoods goods = new VirtualWarehouseInventoryGoods();
  //      goods.setWarehouseNo(virtualWarehouseInventory.getWarehouseNo());
  //      goods.setSpuNo(skuDto.getSpuCode());
  //      goods.setSkuNo(skuDto.getSkuCode());
  //      goods.setInventoryRatio(virtualWarehouseInventory.getInventoryRatio());
  //
  // goods.setInventoryRatio2(BigDecimal.valueOf(virtualWarehouseInventory.getInventoryRatio()));
  //      goods.setVirtualWarehouseId(vwId);
  //      goods.setVirtualWarehouseNo(vwNo);
  //      goods.setVirtualWarehouseInventoryId(virtualWarehouseInventory.getId());
  //
  //      final EditSkuRatioDto editSkuRatioDto =
  //          EditSkuRatioDto.of(
  //              virtualWarehouseInventory.getWarehouseNo(),
  //              skuDto.getSkuCode(),
  //              0,
  //              virtualWarehouseInventory.getInventoryRatio());
  //      editSkuRatioDtoList.add(editSkuRatioDto);
  //
  //      if (lock) {
  //        int lockNum =
  //            new BigDecimal(skuDto.getStockNum())
  //                .multiply(
  //                    new BigDecimal(virtualWarehouseInventory.getInventoryRatio())
  //                        .divide(new BigDecimal(100), 4, RoundingMode.HALF_UP))
  //                .setScale(0, RoundingMode.HALF_UP)
  //                .intValue();
  //        final LockSkuRatioDto lockSkuRatioDto =
  //            LockSkuRatioDto.of(
  //                virtualWarehouseInventory.getWarehouseNo(),
  //                skuDto.getSkuCode(),
  //                0,
  //                virtualWarehouseInventory.getInventoryRatio(),
  //                0,
  //                lockNum);
  //        lockSkuRatioDtoList.add(lockSkuRatioDto);
  //        goods.setInventoryLockNum(lockNum);
  //
  // goods.setInventoryRatio2(BigDecimal.valueOf(virtualWarehouseInventory.getInventoryRatio()));
  //      }
  //      addGoodsList.add(goods);
  //    }
  //
  //    iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(editSkuRatioDtoList);
  //
  //    if (lock) {
  //      goodsInventoryLockStaticsService.handlerBatch(lockSkuRatioDtoList);
  //      refreshSharedGoodsInventoryRatio(
  //          ListUtil.of(vwId), null, virtualWarehouseInventory.getWarehouseNo());
  //    }
  //
  //    if (CollUtil.isNotEmpty(addGoodsList)) {
  //      iVirtualWarehouseInventoryGoodsService.saveBatch(addGoodsList);
  //    }
  //  }

  //  private void oneShopHandlerChangeHandler(
  //      Long shopId,
  //      ShopInventory shopInventory,
  //      List<ShopInventoryGoods> oldGoodsList,
  //      Collection<SkuStockChangeDto> skuCodes,
  //      int type) {
  //
  //    boolean lock = InventoryMode.LOCK.equals(shopInventory.getInventoryMode());
  //
  //    List<ShopInventoryGoods> addGoodsList = new LinkedList<>();
  //    List<EditSkuRatioDto> editSkuRatioDtoList = new LinkedList<>();
  //    List<LockSkuRatioDto> lockSkuRatioDtoList = new LinkedList<>();
  //    final Set<String> oldSkuCodeSet =
  //        oldGoodsList.stream().map(ShopInventoryGoods::getSkuNo).collect(Collectors.toSet());
  //
  //    // 新增SKU
  //
  //    if (CollUtil.isNotEmpty(oldGoodsList)) {
  //      skuCodes =
  //          skuCodes.stream()
  //              .filter(val -> !oldSkuCodeSet.contains(val.getSkuCode()))
  //              .collect(Collectors.toSet());
  //    }
  //
  //    log.info(
  //        "店铺仓库新增SKU数据触发，shopId:{},warehouseNo:{},skuCode:{}",
  //        shopId,
  //        shopInventory.getWarehouseNo(),
  //        JsonUtil.toJson(skuCodes));
  //
  //    for (SkuStockChangeDto skuDto : skuCodes) {
  //      ShopInventoryGoods goods = new ShopInventoryGoods();
  //      goods.setWarehouseNo(shopInventory.getWarehouseNo());
  //      goods.setSpuNo(skuDto.getSpuCode());
  //      goods.setSkuNo(skuDto.getSkuCode());
  //      goods.setInventoryRatio(shopInventory.getInventoryRatio());
  //      goods.setInventoryRatio2(new BigDecimal(shopInventory.getInventoryRatio()));
  //      goods.setShopId(shopId);
  //      goods.setShopInventoryId(shopInventory.getId());
  //
  //      final EditSkuRatioDto editSkuRatioDto =
  //          EditSkuRatioDto.of(
  //              shopInventory.getWarehouseNo(),
  //              skuDto.getSkuCode(),
  //              0,
  //              shopInventory.getInventoryRatio());
  //      editSkuRatioDtoList.add(editSkuRatioDto);
  //
  //      if (lock) {
  //        int lockNum =
  //            new BigDecimal(skuDto.getStockNum())
  //                .multiply(
  //                    new BigDecimal(shopInventory.getInventoryRatio())
  //                        .divide(new BigDecimal(100), 4, RoundingMode.HALF_UP))
  //                .setScale(0, RoundingMode.HALF_UP)
  //                .intValue();
  //        final LockSkuRatioDto lockSkuRatioDto =
  //            LockSkuRatioDto.of(
  //                shopInventory.getWarehouseNo(),
  //                skuDto.getSkuCode(),
  //                0,
  //                shopInventory.getInventoryRatio(),
  //                0,
  //                lockNum);
  //        lockSkuRatioDtoList.add(lockSkuRatioDto);
  //        goods.setInventoryLockNum(lockNum);
  //        goods.setInventoryRatio2(BigDecimal.valueOf(shopInventory.getInventoryRatio()));
  //      }
  //      addGoodsList.add(goods);
  //    }
  //
  //    log.info(
  //        "店铺仓库新增SKU数据完成，shopId:{},warehouseNo:{},skuCode:{}",
  //        shopId,
  //        shopInventory.getWarehouseNo(),
  //        JsonUtil.toJson(skuCodes));
  //
  //    iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(editSkuRatioDtoList);
  //    if (lock) {
  //      goodsInventoryLockStaticsService.handlerBatch(lockSkuRatioDtoList);
  //      refreshSharedGoodsInventoryRatio(null, ListUtil.of(shopId),
  // shopInventory.getWarehouseNo());
  //    }
  //
  //    if (CollUtil.isNotEmpty(addGoodsList)) {
  //      iShopInventoryGoodsService.saveBatch(addGoodsList);
  //    }
  //  }

  @Override
  public SingleResponse<String> warehouseInfoImportExcel() {
    final List<Additional> list =
        iAdditionalService
            .lambdaQuery()
            .like(Additional::getName, "仓库售后地址导入模板")
            .orderByDesc(Additional::getId)
            .list();
    if (CollUtil.isNotEmpty(list)) {
      return SingleResponse.of(list.get(0).getDownloadUrl());
    } else {
      return SingleResponse.buildFailure(ErrorCode.API_RESP_ERROR.getCode(), "模板不存在");
    }
  }

  @Override
  public SingleResponse<Boolean> importWarehouse(MultipartFile multipartFile) {

    try {
      List<WarehouseImportDo> warehouseImportDoList =
          EasyExcel.read(multipartFile.getInputStream())
              .sheet(0)
              .headRowNumber(2)
              .head(WarehouseImportDo.class)
              .doReadSync();
      warehouseImportDoList.forEach(WarehouseImportDo::verify);

      importDataHandler(warehouseImportDoList);
    } catch (Exception e) {
      log.error("importWarehouse error", e);
      return SingleResponse.buildFailure(ErrorCode.SYS_ERROR.getCode(), e.getMessage());
    }

    return SingleResponse.of(true);
  }

  private void importDataHandler(List<WarehouseImportDo> doList) {
    if (CollUtil.isEmpty(doList)) {
      throw ExceptionFactory.bizException(ErrorCode.IMPORT_DATA_ERROR.getCode(), "导入数据为空");
    }
    final Set<String> noSet =
        doList.stream().map(WarehouseImportDo::getNo).collect(Collectors.toSet());
    final Map<String, Warehouse> warehouseMap =
        warehouseService.lambdaQuery().in(Warehouse::getNo, noSet).list().stream()
            .collect(Collectors.toMap(Warehouse::getNo, Function.identity()));
    for (int i = 0; i < doList.size(); i++) {
      final WarehouseImportDo doItem = doList.get(i);
      if (!warehouseMap.containsKey(doItem.getNo())) {
        throw ExceptionFactory.bizException(
            ErrorCode.IMPORT_DATA_ERROR.getCode(), String.format("第%s行仓库编号不存在", 3 + i));
      }
    }

    final Set<String> customerServices =
        doList.stream()
            .map(WarehouseImportDo::getCustomerService)
            .filter(StrUtil::isNotBlank)
            .collect(Collectors.toSet());
    final Map<String, StaffBrief> staffinfomap =
        StaffAssembler.INST.nicknameList2staffList(customerServices).stream()
            .collect(Collectors.toMap(StaffBrief::getNickname, Function.identity()));

    List<WarehouseAfterSalesAddress> allWarehouseAfterSalesAddressList = new LinkedList<>();
    List<Warehouse> updateWarehouseList = new LinkedList<>();

    final RegionTree regionTree = regionDomainService.getRegionTree();

    doList.stream()
        .collect(Collectors.groupingBy(WarehouseImportDo::getNo))
        .forEach(
            (no, list) -> {
              final Warehouse thisNoWarehouse = warehouseMap.get(no);
              if (Objects.nonNull(thisNoWarehouse)) {

                final String afterSaleStaffStr =
                    list.stream()
                        .map(WarehouseImportDo::getCustomerService)
                        .filter(StrUtil::isNotBlank)
                        .collect(Collectors.toSet())
                        .stream()
                        .map(
                            val -> {
                              final StaffBrief staffInfo = staffinfomap.get(val);
                              if (Objects.nonNull(staffInfo)
                                  && Objects.nonNull(staffInfo.getUserId())) {
                                return String.valueOf(staffInfo.getUserId());
                              }
                              return StrUtil.EMPTY;
                            })
                        .filter(StrUtil::isNotBlank)
                        .collect(Collectors.joining(StrUtil.COMMA));

                final List<WarehouseAfterSalesAddress> warehouseAfterSalesAddressList =
                    list.stream()
                        .map(
                            warehouseImportDo -> {
                              WarehouseAfterSalesAddress warehouseAfterSalesAddress =
                                  new WarehouseAfterSalesAddress();
                              warehouseAfterSalesAddress.setWarehouseNo(warehouseImportDo.getNo());
                              warehouseAfterSalesAddress.setContacts(warehouseImportDo.getConcat());
                              warehouseAfterSalesAddress.setTel(warehouseImportDo.getConcatTel());
                              warehouseAfterSalesAddress.setWarehouseName(
                                  thisNoWarehouse.getName());
                              warehouseAfterSalesAddress.setRemark(warehouseImportDo.getRemark());
                              final String address = warehouseImportDo.getAddress();
                              final Region region =
                                  regionTree
                                      .areaFuzzyQuery(address)
                                      .orElseThrow(
                                          () ->
                                              ExceptionPlusFactory.bizException(
                                                  ErrorCode.VERIFY_PARAM, "地址解析失败"));
                              warehouseAfterSalesAddress.setFullAddress(address);
                              final ArrayList<String> regionKeywords = new ArrayList<>();
                              regionKeywords.add(region.getPath());
                              region
                                  .getAncestor(RegionLevel.PROVINCE)
                                  .ifPresent(
                                      r -> {
                                        warehouseAfterSalesAddress.setProvinceCode(r.getCode());
                                        regionKeywords.add(r.getName());
                                      });
                              region
                                  .getAncestor(RegionLevel.CITY)
                                  .ifPresent(
                                      r -> {
                                        warehouseAfterSalesAddress.setCityCode(r.getCode());
                                        regionKeywords.add(r.getName());
                                      });
                              region
                                  .getAncestor(RegionLevel.COUNTY)
                                  .ifPresent(
                                      r -> {
                                        warehouseAfterSalesAddress.setAreaCode(r.getCode());
                                        regionKeywords.add(r.getName());
                                      });
                              String removeRegionAddress = address;
                              for (String regionKeyword : regionKeywords) {
                                removeRegionAddress =
                                    removeRegionAddress.replaceAll(regionKeyword, "");
                              }
                              warehouseAfterSalesAddress.setFullAddress(removeRegionAddress);
                              return warehouseAfterSalesAddress;
                            })
                        .collect(Collectors.toList());

                allWarehouseAfterSalesAddressList.addAll(warehouseAfterSalesAddressList);

                thisNoWarehouse.setAfterSaleStaffStr(afterSaleStaffStr);
                updateWarehouseList.add(thisNoWarehouse);
              }
            });

    if (CollUtil.isNotEmpty(allWarehouseAfterSalesAddressList)) {
      iWarehouseAfterSalesAddressService.saveBatch(allWarehouseAfterSalesAddressList);
    }
    if (CollUtil.isNotEmpty(updateWarehouseList)) {
      warehouseService.updateBatchById(updateWarehouseList);
    }
  }

  @Override
  public SingleResponse<Boolean> batchUpdateOrderPerson(OrderPersonnelCmd cmd) {
    warehouseService
        .lambdaUpdate()
        .set(
            Warehouse::getOrderPersonnel,
            cmd.getUidList().stream()
                .map(String::valueOf)
                .collect(Collectors.joining(StrUtil.COMMA)))
        .in(Warehouse::getId, cmd.getWarehouseIdList())
        .update();
    return SingleResponse.of(true);
  }
}
