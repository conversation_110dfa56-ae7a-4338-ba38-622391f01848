package com.daddylab.supplier.item.domain.dataFetch;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/2/20
 */
@Getter
@AllArgsConstructor
public enum FetcherFactoryType implements IEnum<Integer> {
    WDT_LOGISTICS_TRACE(1, "旺店通物流轨迹"),
    ;
    private final Integer value;
    private final String desc;
}
