package com.daddylab.supplier.item.application.criticalPush;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/4/15
 */
@AllArgsConstructor
@Getter
public enum PushState implements IEnum<Integer> {
    INIT(0, "等待推送"),
    SUCCESS(1, "推送成功"),
    ERROR(2, "推送失败"),
    ;
    private final Integer value;
    private final String desc;
}
