package com.daddylab.supplier.item.application.order.settlement.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OrderSettlementStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2023年08月08日 5:34 PM
 */
@ApiModel("订单结算，新增页面返回")
@Data
public class SysBillPageVo {

    @ApiModelProperty("采购账单id")
    private Long billId;

    @ApiModelProperty("周期")
    private String cycleDesc;

    @ApiModelProperty("仓库")
    private String warehouse;

    @ApiModelProperty("暂估数量")
    private Integer temporaryQuantity;

    @ApiModelProperty("状态。WAIT_CONFIRM 待结算。CONFIRMED 已结算")
    private OrderSettlementStatus status;

    @ApiModelProperty("采购单号")
    private String purchaseOrderNo;

    @ApiModelProperty("订单员")
    private String orderPersonnelName;

    @ApiModelProperty("结算数量")
    private Integer settlementQuantity;

    @ApiModelProperty("合作模式")
    private Integer businessLine;



}
