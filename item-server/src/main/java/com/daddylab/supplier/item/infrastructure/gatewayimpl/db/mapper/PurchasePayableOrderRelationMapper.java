package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableOrderRelation;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;

/**
 * <p>
 * 采购应付关联信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-03
 */
public interface PurchasePayableOrderRelationMapper extends DaddyBaseMapper<PurchasePayableOrderRelation> {

}
