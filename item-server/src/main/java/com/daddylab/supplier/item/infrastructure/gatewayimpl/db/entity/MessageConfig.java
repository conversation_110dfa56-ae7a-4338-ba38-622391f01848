package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessageOperationType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MessageConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String template;

    private String sample;

    /**
     * 消息配置类型
     */
    private MessageOperationType operationType;

    /**
     * 消息推送类型
     */
    private Integer pushDefault;
    private Integer pushRemind;
    private Integer pushText;
    private Integer pushMail;

    /**
     * 消息接收者们
     */
    private String recipients;

    private Long deletedAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    @TableLogic
    private Integer isDel;

    private Integer canEffect;


}
