package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.ark.sailor.common.base.vo.Result;
import com.daddylab.ark.sailor.trade.domain.query.order.OrderSaleMessageQuery;
import com.daddylab.ark.sailor.trade.domain.query.refund.RefundOrderPageQuery;
import com.daddylab.ark.sailor.trade.domain.vo.order.OrderSaleMessageVO;
import com.daddylab.ark.sailor.trade.domain.vo.refund.RefundInfoVO;
import com.daddylab.ark.sailor.trade.domain.vo.refund.RefundOrderDetailVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年07月25日 4:22 PM
 */
@FeignClient(name = "mallMerchantApiFeignClient", url = "${mall-merchant-server.url:http://ms-mall-merchant:9903/merchant}")
public interface MallMerchantApiFeignClient {

    /**
     * 维权列表(分页)
     *
     * @param pageQuery 查询参数
     */
    @RequestMapping(value = "/innerapi/mall/refund/page", method = RequestMethod.GET)
    Result<Page<RefundInfoVO>> queryRefundPage(@SpringQueryMap RefundOrderPageQuery pageQuery);

    /**
     * 维权详情
     *
     * @param refundOrderId 退换单ID
     */
    @RequestMapping(value = "/innerapi/mall/refund/detail/{refundOrderId}", method = RequestMethod.GET)
    Result<RefundOrderDetailVO> queryRefundDetail(@PathVariable("refundOrderId") Long refundOrderId);

    /**
     * 卖家订单备注列表查询
     *
     * @param query 查询参数
     */
    @RequestMapping(value = "/innerapi/mall/order/seller/remark/list", method = RequestMethod.POST)
    Result<List<OrderSaleMessageVO>> querySellerRemarkList(@RequestBody OrderSaleMessageQuery query);




}
