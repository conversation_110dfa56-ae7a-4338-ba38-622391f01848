package com.daddylab.supplier.item.application.afterSalesRegister;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.thread.GlobalThreadPool;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesRegister;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesRegisterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.multiset.HashMultiSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.daddylab.supplier.item.application.afterSalesRegister.AfterSalesRegisterFetchServiceImpl.SETTLE_DURATION_FORMATTER;
import static com.daddylab.supplier.item.infrastructure.utils.MPUtil.limit;

/**
 * <AUTHOR>
 * @since 2023/8/28
 */
@Service
@Slf4j
public class AfterSalesRegisterFilterServiceImpl implements AfterSalesRegisterFilterService {
    public static final int BATCH_LIMIT = 100;
    @Autowired IAfterSalesRegisterService afterSalesRegisterService;
    @Autowired WdtRefundOrderMapper wdtRefundOrderMapper;

    @Override
    public CompletableFuture<Void> filter(YearMonth month) {
        return CompletableFuture.runAsync(() -> filterImpl(month), GlobalThreadPool.getExecutor());
    }

    private void filterImpl(YearMonth month) {
        final StopWatch stopWatch = new StopWatch();
        final String settleDurationStr = month.format(SETTLE_DURATION_FORMATTER);

        stopWatch.start("count");
        final Integer total =
                afterSalesRegisterService
                        .lambdaQuery()
                        .eq(AfterSalesRegister::getSettleDuration, settleDurationStr)
                        .count();
        stopWatch.stop();

        log.info(
                "【客服售后登记表】FILTER START month={} total={} time={}",
                settleDurationStr,
                total,
                stopWatch);
        stopWatch.start("process");

        long cursor = 0;
        final HashMultiSet<String> stats = new HashMultiSet<>();
        while (true) {
            final StopWatch stopWatch1 = new StopWatch();
            stopWatch1.start("query");
            final List<AfterSalesRegister> list =
                    afterSalesRegisterService
                            .lambdaQuery()
                            .eq(AfterSalesRegister::getSettleDuration, settleDurationStr)
                            .gt(AfterSalesRegister::getId, cursor)
                            .orderByAsc(AfterSalesRegister::getId)
                            .last(limit(BATCH_LIMIT))
                            .list();
            stopWatch1.stop();
            log.info(
                    "【客服售后登记表】FILTER PROCESSING [{} + {}] month={} total={} time={}",
                    cursor,
                    list.size(),
                    settleDurationStr,
                    total,
                    stopWatch1);
            if (list.isEmpty()) {
                break;
            }
            cursor =
                    list.stream()
                            .max(Comparator.comparing(AfterSalesRegister::getId))
                            .map(AfterSalesRegister::getId)
                            .orElseThrow(() -> new IllegalArgumentException("逻辑异常"));
            log.info(
                    "【客服售后登记表】FILTER PROCESSING month={} total={} stats={}",
                    settleDurationStr,
                    total,
                    stats);
            final List<String> orderNos =
                    list.stream()
                            .map(AfterSalesRegister::getOrderNo)
                            .distinct()
                            .collect(Collectors.toList());
            stopWatch1.start("queryRefundOrder");
            final List<WdtRefundOrderDetail> wdtRefundOrderDetails =
                    wdtRefundOrderMapper.queryCheckedRefundOrderDetailsBatchBySrcOrderNo(
                            orderNos, 2);
            final Map<String, List<WdtRefundOrderDetail>> wdtRefundOrderDetailsGroupByTid =
                    wdtRefundOrderDetails.stream()
                            .collect(Collectors.groupingBy(WdtRefundOrderDetail::getTid));
            stopWatch1.stop();
            log.debug(
                    "【客服售后登记表】FILTER PROCESSING month={} total={} stats={}",
                    settleDurationStr,
                    total,
                    stats);
            stopWatch1.start("check");
            final ArrayList<AfterSalesRegister> updatePOList = new ArrayList<>();
            for (AfterSalesRegister afterSalesRegister : list) {
                final AfterSalesRegister updatePO = new AfterSalesRegister();
                updatePO.setId(afterSalesRegister.getId());
                //- 工厂/仓库承担运费、工厂/仓库承担货款、工厂/仓库承担其他补偿、体验基金（优惠券）、体验基金（现金），这几个字段任何一个有值都应该保留（特殊：值为“无货款费用”也视为无有效值）
                //- 如果“工厂/仓库承担货款”该字段值为“工厂承担货款”，且其他费用字段皆无有效值，那么当这个订单在退换管理中存在类型为“退货”的退换单时，这个记录需要过滤掉。
                final boolean hasNotValidFactoryUndertakeAmount =
                        hasNotValidFactoryUndertakeAmount(afterSalesRegister);
                final boolean hasExperienceCash = hasExperienceCash(afterSalesRegister);
                if (hasNotValidFactoryUndertakeAmount && !hasExperienceCash) {
                    updatePO.setFilterReason(1);
                    stats.add("工厂/仓库承担费用、体验基金等字段无有效值");
                    updatePOList.add(updatePO);
                } else if ("工厂承担货款".equals(afterSalesRegister.getFactoryUndertakeGoodsAmount())
                        && !isValidAmountValue(afterSalesRegister.getFactoryUndertakeFreight())
                        && !isValidAmountValue(afterSalesRegister.getFactoryUndertakeOtherAmount())
                        && !hasExperienceCash
                        && wdtRefundOrderDetailsGroupByTid.containsKey(afterSalesRegister.getOrderNo())) {
                    updatePO.setFilterReason(2);
                    stats.add("工厂承担货款，其他费用字段皆无有效值，且已存在退货单");
                    updatePOList.add(updatePO);
                }
            }
            stopWatch1.stop();
            log.debug(
                    "【客服售后登记表】FILTER PROCESSING month={} total={} stats={}",
                    settleDurationStr,
                    total,
                    stats);
            stopWatch1.start("update");
            if (!updatePOList.isEmpty()) {
                afterSalesRegisterService.updateBatchById(updatePOList);
            }
            stopWatch1.stop();
            log.info(
                    "【客服售后登记表】FILTER PROCESSING DONE month={} total={} stats={}",
                    month,
                    total,
                    stats);
        }
        stopWatch.stop();

        stopWatch.start("clean");
        afterSalesRegisterService
                .lambdaUpdate()
                .eq(AfterSalesRegister::getSettleDuration, settleDurationStr)
                .gt(AfterSalesRegister::getFilterReason, 0)
                .remove();
        stopWatch.stop();

        log.info("【客服售后登记表】FILTER DONE month={} total={} time={}", month, total, stopWatch);
    }

    private static boolean isValidAmountValue(String amount) {
        return !StrUtil.isBlank(amount) && !"无货款费用".equals(amount) && !"无需承担".equals(amount);
    }

    private static boolean hasNotValidFactoryUndertakeAmount(
            AfterSalesRegister afterSalesRegister) {
        return !isValidAmountValue(afterSalesRegister.getFactoryUndertakeFreight())
                && !isValidAmountValue(afterSalesRegister.getFactoryUndertakeGoodsAmount())
                && !isValidAmountValue(afterSalesRegister.getFactoryUndertakeOtherAmount());
    }

    private static boolean hasExperienceCash(AfterSalesRegister po) {
        return !StrUtil.isBlank(po.getExperienceFundCash())
                || !StrUtil.isBlank(po.getExperienceFundCoupon());
    }
}
