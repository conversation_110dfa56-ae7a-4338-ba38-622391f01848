package com.daddylab.supplier.item.application.otherStockIn;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.StockinOtherAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.StockinOtherQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.StockinOtherQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.StockinOtherQueryWithDetailResponse.Order.Detail;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockother.StockotherInQueryAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockother.dto.StockotherInQueryQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockother.dto.StockotherInQueryQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockother.dto.StockotherInQueryQueryWithDetailResponse.Order;
import com.daddylab.supplier.item.domain.brand.gateway.BrandGateway;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockIn;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockInDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OtherStockInDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OtherStockInMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ReasonMapper;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.reason.enums.ReasonType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 旺店通其他入库同步任务
 *
 * <AUTHOR>
 * @since 2021/04/22
 */
@Component
@Slf4j
public class OtherStockInOrderFetcher implements PageFetcher {

    final private WdtGateway wdtGateway;
    final private OtherStockInMapper otherStockInMapper;
    final private OtherStockInDetailMapper otherStockInDetailMapper;
    final private ItemSkuGateway itemSkuGateway;
    final private BrandGateway brandGateway;
    final private ReasonMapper reasonMapper;

    public OtherStockInOrderFetcher(
            WdtGateway wdtGateway,
            OtherStockInMapper otherStockInMapper,
            OtherStockInDetailMapper otherStockInDetailMapper,
            ItemSkuGateway itemSkuGateway,
            BrandGateway brandGateway,
            ReasonMapper reasonMapper) {
        this.wdtGateway = wdtGateway;
        this.otherStockInMapper = otherStockInMapper;
        this.otherStockInDetailMapper = otherStockInDetailMapper;
        this.itemSkuGateway = itemSkuGateway;
        this.brandGateway = brandGateway;
        this.reasonMapper = reasonMapper;
    }

    @Override
    public int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
        return query(startTime, endTime, 1, 1, true).getTotalCount();
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, long pageIndex, long pageSize,
            RunContext runContext) {
        final Duration duration = LocalDateTimeUtil.between(startTime, endTime);
        if (duration.isNegative() || duration.isZero()
                || duration.compareTo(Duration.ofDays(30)) > 0) {
            throw new IllegalArgumentException("拉取时间最大范围为三十天");
        }
        final StockinOtherQueryWithDetailResponse response = query(startTime, endTime,
                (int) pageIndex,
                (int) pageSize, false);
        handleResponse(response);
    }

    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.WDT_OTHER_STOCK_IN_ORDER;
    }

    private StockinOtherQueryWithDetailResponse query(
            LocalDateTime startTime, LocalDateTime endTime, int pageNo,
            int pageSize, boolean calcTotal) {
        try {
            final StockinOtherAPI api = wdtGateway.getAPI(StockinOtherAPI.class, 0, false);
            final Pager pager = new Pager(pageSize, pageNo - 1, calcTotal);
            final StockinOtherQueryWithDetailParams params = new StockinOtherQueryWithDetailParams();
            params.setStartTime(DateUtil.format(startTime));
            params.setEndTime(DateUtil.format(endTime));

            return api.queryWithDetail(params, pager);
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapFetchException(e, fetchDataType());
        }
    }

    private void handleResponse(StockinOtherQueryWithDetailResponse response) {
        for (StockinOtherQueryWithDetailResponse.Order order : response.getOrder()) {
            handleOrder(order);
        }
    }

    private Order queryStockOtherBizOrder(String otherStockInBizOrderNo) {
        try {
            final StockotherInQueryAPI api = wdtGateway.getAPI(StockotherInQueryAPI.class);
            final Pager pager = new Pager(1, 0, false);
            final StockotherInQueryQueryWithDetailParams params = new StockotherInQueryQueryWithDetailParams();
            params.setOtherInNo(otherStockInBizOrderNo);

            final StockotherInQueryQueryWithDetailResponse response = api
                    .queryWithDetail(params, pager);
            final List<Order> order = response.getOrder();
            if (order.isEmpty()) {
                return null;
            }
            return order.get(0);
        } catch (WdtErpException e) {
            log.error("查询其他入库业务单失败", e);
            return null;
        }
    }

    private void handleOrder(StockinOtherQueryWithDetailResponse.Order order) {
        final LambdaQueryWrapper<OtherStockIn> query = Wrappers
                .lambdaQuery();
        final String orderNo = order.getOrderNo();
        query.eq(OtherStockIn::getOrderNo, orderNo);
        OtherStockIn otherStockIn = otherStockInMapper.selectOne(query);
        if (otherStockIn == null) {
            otherStockIn = new OtherStockIn();
        }
        otherStockIn.setOrderNo(orderNo);
        otherStockIn.setWarehouseNo(order.getWarehouseNo());
        otherStockIn.setStatus(
                OtherStockInState.MAP.getOrDefault(order.getStatus(), OtherStockInState.UNMAPPING)
                        .getValue());

        //TODO:后面删除
        OtherStockInReason reasonEnum = IEnum
                .getEnumByDesc(OtherStockInReason.class, order.getReason());
        if (reasonEnum == null) {
            reasonEnum = OtherStockInReason.UNMAPPING;
            log.warn("其他入库单同步异常，原因未映射:{}", JsonUtil.toJson(order));
        }
        otherStockIn.setOtherReason(reasonEnum.getValue());

        //改为存表
        Long reasonId = reasonMapper.id(ReasonType.OTHER_STOCK_IN, order.getReason());
        if (reasonId == null) {
            reasonId = reasonMapper.addReason(ReasonType.OTHER_STOCK_IN, order.getReason());
        }
        otherStockIn.setOtherReason(reasonId.intValue());

        otherStockIn.setReasonText(order.getReason());
        otherStockIn.setMarkName("");
        otherStockIn.setLogisticsId(
                Optional.ofNullable(order.getLogisticsType()).map(Integer::longValue).orElse(null));
        otherStockIn.setAuditUid(0L);
        otherStockIn.setRemark(order.getRemark());
        otherStockIn.setOperatorName(order.getOperatorName());
        final String srcOrderNo = order.getSrcOrderNo();
        final Order bizOrder = queryStockOtherBizOrder(srcOrderNo);
        if (bizOrder != null) {
            otherStockIn.setLogisticsNo(bizOrder.getLogisticsNo());
        }

        if (otherStockIn.getId() != null) {
            otherStockInMapper.updateById(otherStockIn);
        } else {
            otherStockInMapper.insert(otherStockIn);
        }

        if (CollUtil.isNotEmpty(order.getDetailList())) {
            deleteRemovedDetails(otherStockIn, order);

            for (Detail detail : order.getDetailList()) {
                final LambdaQueryWrapper<OtherStockInDetail> queryDetail = Wrappers
                        .lambdaQuery();
                queryDetail.eq(OtherStockInDetail::getRecId, detail.getRecId());
                OtherStockInDetail otherStockInDetail = otherStockInDetailMapper
                        .selectOne(queryDetail);
                if (otherStockInDetail == null) {
                    otherStockInDetail = new OtherStockInDetail();
                    otherStockInDetail.setRecId(detail.getRecId().longValue());
                }
                otherStockInDetail.setOrderId(otherStockIn.getId());
                final String specNo = detail.getSpecNo();
                final ItemSku itemSku = itemSkuGateway.getBySkuCode(specNo);
                otherStockInDetail.setItemId(itemSku != null ? itemSku.getItemId() : 0);
                otherStockInDetail.setSkuId(itemSku != null ? itemSku.getId() : 0);
                otherStockInDetail.setSkuCode(specNo);
                otherStockInDetail.setBusinessLine(itemSkuGateway.getSkuBusinessLine(specNo));
                Integer predictCount = null;
                if (bizOrder != null && CollUtil.isNotEmpty(bizOrder.getDetailList())) {
                    for (Order.Detail bizOrderDetail : bizOrder.getDetailList()) {
                        if (Objects.equals(bizOrderDetail.getSpecNo(), specNo)) {
                            predictCount = bizOrderDetail.getNum().intValue();
                            break;
                        }
                    }
                }
                otherStockInDetail.setPredictCount(predictCount);
                otherStockInDetail.setCount(
                        detail.getRightNum() != null ? detail.getRightNum().intValue()
                                : 0);
                otherStockInDetail.setIsDefect(detail.getDefect() ? 1 : 0);
                otherStockInDetail.setUnit(detail.getGoodsUnit());
                otherStockInDetail.setAssistUnit(detail.getGoodsUnit());
                otherStockInDetail.setAssistCount(null);
                otherStockInDetail.setBrandId(brandGateway.id(detail.getBrandNo()));
                otherStockInDetail.setBrandSn(detail.getBrandNo());
                otherStockInDetail
                        .setBarCode(itemSku != null ? itemSku.getBarCode() : null);
                otherStockInDetail.setTotalWeight(BigDecimal.ZERO);
                otherStockInDetail.setPredictWeight(BigDecimal.ZERO);
                otherStockInDetail.setRemark(detail.getRemark());
                if (otherStockInDetail.getId() != null) {
                    otherStockInDetailMapper.updateById(otherStockInDetail);
                } else {
                    otherStockInDetailMapper.insert(otherStockInDetail);
                }

            }
        }

    }

    private void deleteRemovedDetails(OtherStockIn OtherStockIn,
            StockinOtherQueryWithDetailResponse.Order order) {
        final List<Integer> recIds = order.getDetailList().stream().map(Detail::getRecId)
                .collect(Collectors.toList());
        final LambdaQueryWrapper<OtherStockInDetail> deleteRemovedDetailQuery = Wrappers
                .lambdaQuery();
        deleteRemovedDetailQuery.eq(OtherStockInDetail::getOrderId, OtherStockIn.getId())
                .notIn(OtherStockInDetail::getRecId, recIds);
        otherStockInDetailMapper.deleteWithTime(deleteRemovedDetailQuery);
    }
}
