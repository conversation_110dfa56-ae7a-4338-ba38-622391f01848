package com.daddylab.supplier.item.application.afterSalesRegister;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/3/11
 */
@ConfigurationProperties(prefix = "after-sales-register")
@Configuration
@Data
@RefreshScope
public class AfterSalesRegisterConfig {

    /**
     * 客服售后登记数据拉取是否详细记录数据来源
     */
    private boolean traceDetails = true;

    /**
     * 需要同步数据的自研商城店铺ID
     */
    private Long mallShopId = 10004L;
}
