package com.daddylab.supplier.item.infrastructure.gatewayimpl.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.domain.order.OrderSensitiveInfo;
import com.daddylab.supplier.item.domain.order.OrderSensitiveInfoQuery;
import com.daddylab.supplier.item.domain.order.OrderSensitiveInfoRepository;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AdsTidbEcpUsrPhoneInfoPd;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AdsTidbEcpUsrPhoneInfoPdMapper;
import com.daddylab.supplier.item.infrastructure.userInfoCrypto.UserInfoCrypto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/5/5
 */
@Repository
public class OrderSensitiveInfoRepositoryImpl implements OrderSensitiveInfoRepository {

    private final AdsTidbEcpUsrPhoneInfoPdMapper adsTidbEcpUsrPhoneInfoPdMapper;
    private final UserInfoCrypto userInfoCrypto;

    public OrderSensitiveInfoRepositoryImpl(
            AdsTidbEcpUsrPhoneInfoPdMapper adsTidbEcpUsrPhoneInfoPdMapper,
            UserInfoCrypto userInfoCrypto) {
        this.adsTidbEcpUsrPhoneInfoPdMapper = adsTidbEcpUsrPhoneInfoPdMapper;
        this.userInfoCrypto = userInfoCrypto;
    }

    @Override
    public OrderSensitiveInfo getOrderSensitiveInfo(String orderNo) {
        final LambdaQueryWrapper<AdsTidbEcpUsrPhoneInfoPd> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AdsTidbEcpUsrPhoneInfoPd::getOrderId, orderNo);
        final AdsTidbEcpUsrPhoneInfoPd adsTidbEcpUsrPhoneInfoPd = adsTidbEcpUsrPhoneInfoPdMapper
                .selectOne(wrapper);
        if (adsTidbEcpUsrPhoneInfoPd == null) {
            return null;
        }
        return Assembler.INST.poToOrderSensitiveInfo(adsTidbEcpUsrPhoneInfoPd, userInfoCrypto);
    }

    @Override
    public Map<String, OrderSensitiveInfo> getOrderSensitiveInfoBatch(Collection<String> orderNos) {
        final LambdaQueryWrapper<AdsTidbEcpUsrPhoneInfoPd> wrapper = Wrappers.lambdaQuery();
        wrapper.in(AdsTidbEcpUsrPhoneInfoPd::getOrderId, orderNos);
        final List<AdsTidbEcpUsrPhoneInfoPd> adsTidbEcpUsrPhoneInfoPds = adsTidbEcpUsrPhoneInfoPdMapper
                .selectList(wrapper);
        if (CollUtil.isEmpty(adsTidbEcpUsrPhoneInfoPds)) {
            return Collections.emptyMap();
        }
        final HashMap<String, OrderSensitiveInfo> result = new HashMap<>();
        for (AdsTidbEcpUsrPhoneInfoPd adsTidbEcpUsrPhoneInfoPd : adsTidbEcpUsrPhoneInfoPds) {
            result.put(adsTidbEcpUsrPhoneInfoPd.getOrderId(),
                    Assembler.INST
                            .poToOrderSensitiveInfo(adsTidbEcpUsrPhoneInfoPd, userInfoCrypto));
        }
        return result;
    }

    @Override
    public List<OrderSensitiveInfo> query(OrderSensitiveInfoQuery query) {
        if (StrUtil.isBlank(query.getReceiverName()) && StrUtil.isBlank(query.getReceiverPhone())) {
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<AdsTidbEcpUsrPhoneInfoPd> wrapper = Wrappers.lambdaQuery();
        wrapper
                .eq(Objects.nonNull(query.getReceiverPhone()), AdsTidbEcpUsrPhoneInfoPd::getPhoneId,
                        userInfoCrypto.encrypt(query.getReceiverPhone()))
                .eq(Objects.nonNull(query.getReceiverName()),
                        AdsTidbEcpUsrPhoneInfoPd::getReceiverName, query.getReceiverName())
        ;
        final List<AdsTidbEcpUsrPhoneInfoPd> adsTidbEcpUsrPhoneInfoPds = adsTidbEcpUsrPhoneInfoPdMapper
                .selectList(wrapper);
        if (CollUtil.isEmpty(adsTidbEcpUsrPhoneInfoPds)) {
            return Collections.emptyList();
        }
        return adsTidbEcpUsrPhoneInfoPds.stream().map(
                po -> Assembler.INST.poToOrderSensitiveInfo(po, userInfoCrypto))
                .collect(Collectors.toList());
    }

    @Mapper
    public interface Assembler {

        Assembler INST = Mappers.getMapper(Assembler.class);

        @Mapping(target = "receiverPhone", source = "po.phoneId")
        @Mapping(target = "orderNo", source = "po.orderId")
        OrderSensitiveInfo poToOrderSensitiveInfo(AdsTidbEcpUsrPhoneInfoPd po,
                 UserInfoCrypto userInfoCrypto);

    }
}
