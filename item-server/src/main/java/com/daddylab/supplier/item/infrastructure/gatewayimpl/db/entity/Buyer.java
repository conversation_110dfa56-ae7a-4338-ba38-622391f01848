package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Buyer implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long userId;

    private String name;

    private String kingDeeId;

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    @TableLogic
    private Integer isDel;

    private String kingDeeMapId;

    private String kingDeeAccount;

    private String thirdId;

    /**
     * 0 金蝶 默认
     * 1 班牛
     */
    private Integer type;

}
