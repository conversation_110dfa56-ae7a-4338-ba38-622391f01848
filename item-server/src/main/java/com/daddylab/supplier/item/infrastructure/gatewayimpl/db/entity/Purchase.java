package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.domain.purchase.enums.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 采购确认
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Purchase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 月份
     */
    private String month;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 商品skuid
     */
    private Long itemSkuId;

    /**
     * 商品sku
     */
    private String itemSku;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品规格
     */
    private String itemSpecs;

    /**
     * 采购地点0:工厂 1:仓库
     */
    private PurchaseArea purchaseArea;

    /**
     * 供应商
     */
    private String provider;

    /**
     * 采购负责人
     */
    private Long buyerId;

    /**
     * 日常供价(无优惠)
     */
    private BigDecimal usualPrice;

    /**
     * 规格数量
     */
    private Long specsCount;

    /**
     * 是否纯活动商品 0:否 1:是
     */
    private PurchaseIsActive isActive;

    /**
     * 优惠类型 0:货抵款 1:按时间供价 2:当月优惠件数
     */
    private FavourableType favourableType;

    /**
     * 平台名称 0:ALL 1:淘宝 2:抖店 3:小红书 4:自研电商 5:有赞 6:快手
     */
    private PurchasePlatform platformType;

    /**
     * 方式 0:大促 1:直播 2:无
     */
    private PurchaseType activeType;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 订单拍下份数
     */
    private Long orderCount;

    /**
     * 实发单品数量
     */
    private Long finalCount;

    /**
     * 按价格优惠结算成本/元
     */
    private BigDecimal priceCost;

    /**
     * 按数量优惠结算成本/元
     */
    private BigDecimal numCost;

    /**
     * 供价优惠内容
     */
    private String content;

    /**
     * 备注说明
     */
    private String mark;

    /**
     * 0:待确认 1:已确认 2:存在异议
     */
    private PurchaseStatus status;

    /**
     * 0:未发起确认 1:已发起确认
     */
    private Integer isConfirm;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 加密码
     */
    private String md5;

    /**
     * token
     */
    private String token;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除，不能做业务。这是软删除标记
     */
    @TableLogic
    private Integer isDel;

    /**
     * 合作模式。0:电商。1:老爸抽检。2:绿色家装。3:商家入驻
     */
    private Integer businessLine;

    /**
     * 0:采购活动价。1:sku日常阶梯价。2:sku活动阶梯价。3:spu日常阶梯价。4:spu活动阶梯价
     */
    private Integer priceType;


    /**
     * 合作方 层级
     */
    @TableField(exist = false)
    private String coryTypeStr;
    
    /**
     * 业务类型 层级
     */
//    @TableField(exist = false)
//    private String bizTypeStr;




}
