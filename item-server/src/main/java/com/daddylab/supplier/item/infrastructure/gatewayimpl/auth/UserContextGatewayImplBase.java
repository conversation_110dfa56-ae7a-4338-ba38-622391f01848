package com.daddylab.supplier.item.infrastructure.gatewayimpl.auth;

import com.daddylab.supplier.item.domain.auth.PermRefreshManager;
import com.daddylab.supplier.item.domain.auth.entity.SysResource;
import com.daddylab.supplier.item.domain.auth.enums.ResourceType;
import com.daddylab.supplier.item.domain.auth.gateway.UserContextGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;

import lombok.extern.slf4j.Slf4j;

import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/3/30
 */
@Slf4j
public abstract class UserContextGatewayImplBase implements UserContextGateway {
    static final String USER_INFO_CACHE_KEY = "supplier.item.userinfo.";
    static final int CACHE_TIME = 3600;

    protected final PermRefreshManager permRefreshManager;
    protected final RedissonClient redissonClient;

    public UserContextGatewayImplBase(PermRefreshManager permRefreshManager,
                                      RedissonClient redissonClient) {
        this.permRefreshManager = permRefreshManager;
        this.redissonClient = redissonClient;
    }

    @Override
    public UserContext.UserInfo getUserContext(long userId, boolean refreshCache) {
        final String key = USER_INFO_CACHE_KEY + userId;
        final RBucket<UserContext.UserInfo> bucket = redissonClient.getBucket(key);
        UserContext.UserInfo userinfo;

        if (!refreshCache) {
            userinfo = bucket.get();
            if (userinfo != null
                    && !permRefreshManager.isNeedRefreshPerm(userinfo.getQueryTime())) {
//                log.debug("获取用户上下文信息[缓存]:{}", userinfo);
                return userinfo;
            }
        }
        userinfo = getUserContext0(userId);
        bucket.set(userinfo, CACHE_TIME, TimeUnit.SECONDS);
        return userinfo;
    }


    /**
     * 实际查询方法
     */
    abstract UserContext.UserInfo getUserContext0(long userId);

    /**
     * 资源权限分组返回标识
     *
     * @param resources 资源列表
     */
    Map<ResourceType, List<String>> extractCodesGroupingByType(List<SysResource> resources) {
        return resources.stream()
                .collect(
                        Collectors.groupingBy(
                                // 根据资源类型分组
                                it ->
                                        IEnum.getEnumOptByValue(ResourceType.class, it.getType())
                                                .orElse(ResourceType.UNKNOWN),
                                Collector.of(
                                        (Supplier<List<String>>) ArrayList::new,
                                        (List<String> c, SysResource r) -> {
                                            String code;
                                            switch (r.getType()) {
                                                case "p":
                                                case "l":
                                                    code = r.getFrontUrl();
                                                    break;
                                                case "a":
                                                    code = r.getApiUrl();
                                                    break;
                                                default:
                                                    code = r.getCode();
                                                    break;
                                            }
                                            if (!code.isEmpty() && !c.contains(code)) {
                                                c.add(code);
                                            }
                                        },
                                        (left, right) -> {
                                            left.addAll(right);
                                            return left;
                                        })));
    }
}
