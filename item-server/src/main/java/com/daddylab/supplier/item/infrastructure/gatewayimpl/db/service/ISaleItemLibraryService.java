package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.SaleItemLibrary;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;


/**
 * <p>
 * 销售商品库 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
public interface ISaleItemLibraryService extends IDaddyService<SaleItemLibrary> {
}
