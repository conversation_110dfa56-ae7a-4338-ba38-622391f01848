package com.daddylab.supplier.item.infrastructure.third.kuaishou.impl;

import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.winrobot.WinrobotTokenManager;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.third.base.AccessTokenService;
import com.daddylab.supplier.item.infrastructure.third.config.KuaiShouConfig;
import com.daddylab.supplier.item.infrastructure.third.consts.ThirdRedisConstants;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.KuaiShouService;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.domain.exception.KsRateLimiterException;
import com.daddylab.supplier.item.infrastructure.utils.Logs;
import com.daddylab.supplier.item.infrastructure.winrobot360.Winrobot360API;
import com.kuaishou.merchant.open.api.AccessTokenKsMerchantRequestSupport;
import com.kuaishou.merchant.open.api.KsMerchantApiException;
import com.kuaishou.merchant.open.api.KsMerchantResponse;
import com.kuaishou.merchant.open.api.client.AccessTokenKsMerchantClient;
import com.kuaishou.merchant.open.api.client.AutoRetryTokenKsMerchantClient;
import com.kuaishou.merchant.open.api.client.oauth.OauthAccessTokenKsClient;
import com.kuaishou.merchant.open.api.common.utils.PlatformEventSecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.slf4j.event.Level;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @class KuaiShouAbstract.java
 * @description 描述类的作用
 * @date 2024-02-29 10:24
 */
@Slf4j
public abstract class KuaiShouAbstract implements InitializingBean, KuaiShouService {

    @Autowired
    protected KsMessageHandlerSupport ksMessageHandlerSupport;

    AccessTokenKsMerchantClient merchantClient;
    RRateLimiter rateLimiter;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private KuaiShouConfig kuaiShouConfig;
    @Autowired
    private AccessTokenService accessTokenService;
    @Autowired
    private Winrobot360API winrobot360Api;
    @Autowired
    private WinrobotTokenManager winrobotTokenManager;

    static final String TOKEN_SIGN_ERROR = "28";

    static final String RATE_LIMITER_ERROR = "802000";

    private ShopAuthorization shopAuthorization;
    protected OauthAccessTokenKsClient oauthAccessTokenClient;

    @Override
    public void setAuthorization(ShopAuthorization shopAuthorization) {
        this.shopAuthorization = shopAuthorization;
    }

    @Override
    public ShopAuthorization getAuthorization() {
        return shopAuthorization;
    }

    /**
     * 快手api调用 (限频重试)
     *
     * @param request AccessTokenKsMerchantRequestSupport<T>
     * @return T
     * @date 2024/2/29 10:59
     * <AUTHOR>
     */
    <T extends KsMerchantResponse> T executeApiResponse(AccessTokenKsMerchantRequestSupport<T> request) {
        T response = null;
        int times = 3;
        do {
            try {
                request.setAccessToken(getAccessToken());
                response  = merchantClient.execute(request);
                Logs.log(log,
                        Level.INFO,
                        "[快手接口调用] 请求参数",
                        Logs.var("request", request).asJson(),
                        Logs.var("response", response).asJson());
                if (response.isSuccess()) {
                    break;
                }
                if (RATE_LIMITER_ERROR.equals(response.getCode())) {
                    throw new KsRateLimiterException();
                }
                if (TOKEN_SIGN_ERROR.equals(response.getCode())) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.SHOP_AUTHORIZATION_EXPIRED);
                }
                throw ExceptionPlusFactory.bizException(ErrorCode.KUAI_SHOU_ERROR, response.getErrorMsg());
            } catch (KsRateLimiterException ksRateLimiterException) {
                log.debug("[快手接口调用] 触发限频，正在第{}次重试.", times);
                try {
                    TimeUnit.MILLISECONDS.sleep(500);
                } catch (Exception ignored) {}
            }catch (KsMerchantApiException e) {
                throw ExceptionPlusFactory.bizException(ErrorCode.KUAI_SHOU_ERROR, e.getErrorMsg());
            } catch (Exception e) {
                throw ExceptionPlusFactory.bizException(ErrorCode.KUAI_SHOU_ERROR, e.getMessage());
            }
        } while (times --> 0);
        return response;
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        rateLimiter = redissonClient.getRateLimiter(ThirdRedisConstants.KS_YD_TOKEN_FRESH_LIMIT_KEY);
        rateLimiter.trySetRate(RateType.OVERALL, 1, 1, RateIntervalUnit.MINUTES);
        merchantClient = new AutoRetryTokenKsMerchantClient(kuaiShouConfig.getApiUrl(), kuaiShouConfig.getAppKey(), kuaiShouConfig.getSignSecret());
        oauthAccessTokenClient = new OauthAccessTokenKsClient(kuaiShouConfig.getAppKey(), kuaiShouConfig.getSignSecret(), kuaiShouConfig.getApiUrl());
    }

    /**
     * 回调消息解密
     *
     * @param message String
     * @return java.lang.String
     * @date 2024/2/29 16:25
     * <AUTHOR>
     */
    String decodeMessage(String message) {
        try {
            return PlatformEventSecurityUtil.decode(message, kuaiShouConfig.getMessageSign());
        } catch (KsMerchantApiException e) {
            log.error("[快手消息解密] 失败.msg={}", e.getErrorMsg());
        }
        return StrUtil.EMPTY;
    }
}
