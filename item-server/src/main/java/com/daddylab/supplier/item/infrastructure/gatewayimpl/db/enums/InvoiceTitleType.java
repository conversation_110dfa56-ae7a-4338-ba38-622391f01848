package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> up
 * @date 2024年07月26日 10:57 AM
 */
@AllArgsConstructor
@Getter
public enum InvoiceTitleType implements IIntegerEnum {

    /**
     *
     */
    PERSON_OR_UNIT(0,"个人或者事业单位"),

    COMPANY(1,"企业");

    @EnumValue
    private final Integer value;

    private final String desc;
}
