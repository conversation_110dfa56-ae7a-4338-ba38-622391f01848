package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 第三方平台商品同步外部任务类型
 * <p>
 */
@Getter
@AllArgsConstructor
public enum ThirdPlatformSyncExternalTaskType implements IEnum<Integer> {
    /**
     * 影刀
     */
    WINROBOT(1, "影刀"),

    DOUDIAN_MAIN(21, "抖店-主图上传任务"),
    DOUDIAN_SPEC(22, "抖店-规格图上传任务"),
    DOUDIAN_DESC(23, "抖店-详情图上传任务");

    private final Integer value;
    private final String desc;

}
