package com.daddylab.supplier.item.application.drawer;


import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAuditTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import com.daddylab.supplier.item.types.itemDrawer.CreateProcessCmd;
import com.daddylab.supplier.item.types.itemDrawer.ItemDrawerModuleAdviceVO;
import com.daddylab.supplier.item.types.itemDrawer.ItemDrawerModuleAuditTaskVO;
import com.daddylab.supplier.item.types.itemDrawer.ItemDrawerModuleModifyAdviceForm;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerModuleId;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemLaunchProcessNodeId;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/11
 */
public interface ItemDrawerAuditBizService {

    List<ItemDrawerModuleAuditTask> getHistoryTasks(ItemAuditType type, Long liveVerbalTrickId, Long uid, Long itemId, int round);

    ItemDrawerModuleAuditTaskVO getActiveTask(ItemAuditType type, Long liveVerbalTrickId, Long uid, Long itemId);

    ItemDrawerModuleAuditTaskVO getActiveTask(ItemAuditType type, Long uid, Long itemId);

    ItemDrawerModuleAuditTaskVO getActiveTask(ItemAuditType type, Long liveVerbalTrickId, Long uid, Long itemId, ItemLaunchProcessNodeId nodeId);

    ItemDrawerModuleAuditTaskVO getActiveTask(ItemAuditType type, Long uid, Long itemId, ItemLaunchProcessNodeId nodeId);

    Long createProcess(ItemAuditType type, Long uid, Long itemId, ItemDrawerModuleId... moduleIds);

    Long createProcess(ItemAuditType type, Long liveVerbalTrickId, Long uid, Long itemId, ItemDrawerModuleId... moduleIds);
    void createProcess(CreateProcessCmd createProcessCmd);

    void claim(Long uid, Long taskId);

    @Transactional(rollbackFor = Throwable.class)
    void complete(Long uid, Long taskId, List<ItemDrawerModuleModifyAdviceForm> advices, boolean pass);

    void complete(Long uid, Long taskId, List<ItemDrawerModuleModifyAdviceForm> advices, Long auditAt, boolean pass);

    void complete(Long uid, Long taskId, Long liveVerbalTrickId, List<ItemDrawerModuleModifyAdviceForm> advices, Long auditAt, boolean pass);

    void temporarilySaveQcAdvice(Long taskId, List<ItemDrawerModuleModifyAdviceForm> advices);

    List<ItemDrawerModuleAdviceVO> getQcTemporarilyAdvice(Long itemId);

    void terminal(ItemAuditType type, Long liveVerbalTrickId, Long uid, Long itemId);

    void terminal(ItemAuditType type, Long uid, Long itemId);

    void rollback(ItemAuditType type, Long itemId, ItemAuditStatus auditRollbackTo);

    void rollback(ItemAuditType type, Long liveVerbalTrickId, Long itemId, ItemAuditStatus auditRollbackTo);

    void qcChange(Long itemId, Long qcId);

    void qcChange(Long itemId, String qcId);

    void closeTask(ItemAuditType type, Long itemId);
}
