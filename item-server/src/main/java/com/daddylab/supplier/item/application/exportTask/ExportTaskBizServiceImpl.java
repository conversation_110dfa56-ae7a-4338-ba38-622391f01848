package com.daddylab.supplier.item.application.exportTask;

import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.common.trans.ItemTransMapper;
import com.daddylab.supplier.item.controller.item.dto.ExportTaskVo;
import com.daddylab.supplier.item.controller.item.dto.TaskPageQuery;
import com.daddylab.supplier.item.controller.open.PartnerProviderContext;
import com.daddylab.supplier.item.infrastructure.common.ExternalUserContext;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExternalUser;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IExportTaskService;
import com.daddylab.supplier.item.infrastructure.oss.OssGateway;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/9 3:01 下午
 * @description
 */
@Service
public class ExportTaskBizServiceImpl implements ExportTaskBizService {
  @Inject IExportTaskService exportTaskService;

  @Autowired private OssGateway ossGateway;

  @Override
  public PageResponse<ExportTaskVo> exportList(TaskPageQuery taskPageQuery) {

    final LambdaQueryWrapper<ExportTask> queryWrapper = Wrappers.lambdaQuery();
    queryWrapper.orderByDesc(ExportTask::getCreatedAt);
    // 兼容开发平台接口，如果P系统供应商id存在上下文中，则已pId为准。
    Long id = PartnerProviderContext.getId();
    final ExternalUser externalUser = ExternalUserContext.get();
    queryWrapper.and(
        q -> {
          if (externalUser != null) {
            q.or().eq(ExportTask::getExternalUserId, externalUser.getId());
          }
          if (NumberUtil.isPositive(id)) {
            q.or().eq(ExportTask::getPId, id);
          }
          final Long userId = UserContext.getUserId();
          if (userId != null && userId > 0) {
            q.or().eq(ExportTask::getCreatedUid, userId);
          }
        });
    if (ExportTaskType.BACK_ITEM.equals(taskPageQuery.getType())) {
      List<ExportTaskType> exportTaskTypes =
          Arrays.asList(ExportTaskType.BACK_ITEM, ExportTaskType.BACK_ITEM_SKU);
      queryWrapper.in(ExportTask::getType, exportTaskTypes);
    } else if (ExportTaskType.PURCHASE_ORDER.equals(taskPageQuery.getType())) {
      List<ExportTaskType> exportTaskTypes =
          Arrays.asList(ExportTaskType.PURCHASE_ORDER, ExportTaskType.PURCHASE_ORDER_DETAIL,ExportTaskType.PURCHASE_CONTACT);
      queryWrapper.in(ExportTask::getType, exportTaskTypes);
    } else {
      queryWrapper.eq(ExportTask::getType, taskPageQuery.getType());
    }
    Page<ExportTask> pageReq =
        new Page<>(taskPageQuery.getPageIndex(), taskPageQuery.getPageSize());
    final Page<ExportTask> page = exportTaskService.page(pageReq, queryWrapper);
    page.getRecords().forEach(task -> task.setDownloadUrl(getSignedDownloadUrl(task)));
    final List<ExportTaskVo> exportTaskVos =
        ItemTransMapper.INSTANCE.exportTaskDbToVos(page.getRecords());
    return PageResponse.of(
        exportTaskVos,
        (int) page.getTotal(),
        taskPageQuery.getPageSize(),
        taskPageQuery.getPageIndex());
  }

  @Override
  public SingleResponse<String> getExcelDownloadUrl(Long expertTaskId) {
    return SingleResponse.of(
        Optional.ofNullable(exportTaskService.getById(expertTaskId))
            .map(this::getSignedDownloadUrl)
            .orElse(""));
  }

  private String getSignedDownloadUrl(ExportTask task) {
    return getSignedDownloadUrl(
        task.getDownloadUrl(), task.getName() + "." + FileNameUtil.extName(task.getDownloadUrl()));
  }

  private String getSignedDownloadUrl(String url, String downloadName) {
    if (url.contains("daddyoss-private")) {
      url = url.replaceAll(" ", "");
      return ossGateway.generatePresignedUrl(true, URLUtil.getPath(url), 600, downloadName);
    }
    return url;
  }

  @Override
  public void clearExportList(String type) {
    ExportTaskType enumByName = IEnum.getEnumByName(ExportTaskType.class, type);
    final LambdaQueryWrapper<ExportTask> queryWrapper = Wrappers.lambdaQuery();
    queryWrapper.orderByDesc(ExportTask::getCreatedAt);
    // 兼容开发平台接口，如果P系统供应商id存在上下文中，则已pId为准。
    Long id = PartnerProviderContext.getId();
    final ExternalUser externalUser = ExternalUserContext.get();
    queryWrapper.and(
        q -> {
          if (externalUser != null) {
            q.or().eq(ExportTask::getExternalUserId, externalUser.getId());
          }
          if (NumberUtil.isPositive(id)) {
            q.or().eq(ExportTask::getPId, id);
          }
          final Long userId = UserContext.getUserId();
          if (userId != null && userId > 0) {
            q.or().eq(ExportTask::getCreatedUid, userId);
          }
        });
    queryWrapper.eq(ExportTask::getType, enumByName);
    exportTaskService.remove(queryWrapper);
  }

  public static void main(String[] args) {
    String url =
        "https://daddyoss-private.oss-cn-hangzhou.aliyuncs.com/Upload/supplier/item/GCJS202311001385_老爸评测【金华市初赞玩具有限公司】订货明细单_tdfu.xlsx";
    System.out.println(URLUtil.getPath(url));
  }
}
