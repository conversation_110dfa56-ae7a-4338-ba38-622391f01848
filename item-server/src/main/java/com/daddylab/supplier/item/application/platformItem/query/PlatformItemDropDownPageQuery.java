package com.daddylab.supplier.item.application.platformItem.query;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @since 2021/12/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("平台商品下拉列表分页查询")
public class PlatformItemDropDownPageQuery extends PageQuery {

    @Size(max = 50, message = "平台商品名称长度应该在{min}-{max}个字符之间")
    @ApiModelProperty("平台商品名称")
    String name;


}
