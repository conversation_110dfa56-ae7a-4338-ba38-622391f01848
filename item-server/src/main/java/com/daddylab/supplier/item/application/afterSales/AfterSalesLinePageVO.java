package com.daddylab.supplier.item.application.afterSales;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesAbnormalState;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022年10月19日 10:08 AM
 */
@Data
@ApiModel("异常件分页查询返回详情封装")
public class AfterSalesLinePageVO {

    @ApiModelProperty("异常件id")
    private Long abnormalInfoId;

    @ApiModelProperty("商品名称")
    private String itemName;

    @ApiModelProperty("数量")
    private Integer quantity;

    @ApiModelProperty("异常件状态。1:待处理。2:待转寄。3:已转寄。4:已处理(不显示)")
    private AfterSalesAbnormalState abnormalState;

    @ApiModelProperty("图片")
    private List<String> imageUrls;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("物流名称")
    private String logisticsName;

    @ApiModelProperty("仓库编号")
    private String warehouseNo;

    @ApiModelProperty("仓库名称")
    private String warehouseName;


}
