package com.daddylab.supplier.item.application.itemOptimize;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemOptimize;
import com.daddylab.supplier.item.types.itemOptimize.*;
import com.daddylab.supplier.item.types.process.TaskUrgeCmd;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/19
 */
public interface ItemOptimizeBizService {
    PageResponse<ItemOptimizePlanItem> query(ItemOptimizeQuery query);

    SingleResponse<ItemOptimizeDrawer> drawer(Long query);

    Response save(ItemOptimizeSaveCmd saveCmd);

    Response modifyResponsiblePersons(ModifyResponsiblePersonCmd modifyCmd);

    MultiResponse<ItemOptimize> addBatch(List<ItemOptimizePlanSaveCmdItem> items, int planStatus);

    Response delete(Long id);

    Response urge(Long reminderUid, TaskUrgeCmd cmd);

    Response complete(TaskCompleteCmd cmd);

    void planSubmit(Long planId);

    Response rollback(RollbackCmd cmd);
}
