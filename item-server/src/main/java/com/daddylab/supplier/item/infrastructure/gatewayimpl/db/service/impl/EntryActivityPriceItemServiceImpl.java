package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.EntryActivityPriceItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IEntryActivityPriceItemService;
import java.util.Collection;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 入驻活动价格(商品纬度) 服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-04
 */
@Service
public class EntryActivityPriceItemServiceImpl
    extends DaddyServiceImpl<EntryActivityPriceItemMapper, EntryActivityPriceItem>
    implements IEntryActivityPriceItemService {

  @Override
  public List<EntryActivityPriceSku> listConfirmedSkuBySkuIds(Collection<Long> skuIds) {
    return getBaseMapper().listConfirmedSkuBySkuIds(skuIds);
  }
}
