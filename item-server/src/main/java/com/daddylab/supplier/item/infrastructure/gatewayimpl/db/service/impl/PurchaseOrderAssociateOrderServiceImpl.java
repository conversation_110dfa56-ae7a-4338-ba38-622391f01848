package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrderAssociateOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseOrderAssociateOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseOrderAssociateOrderService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 采购订单关联的系统订单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Service
public class PurchaseOrderAssociateOrderServiceImpl extends DaddyServiceImpl<PurchaseOrderAssociateOrderMapper, PurchaseOrderAssociateOrder> implements IPurchaseOrderAssociateOrderService {

}
