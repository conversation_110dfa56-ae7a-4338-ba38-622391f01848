package com.daddylab.supplier.item.domain.itemStock.gateway;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.itemStock.enums.StockType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtStockSpec;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtStockSpecService;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/2/25
 */
@Primary
@Component(value = "warehouseStockGatewayDbImpl")
@RequiredArgsConstructor
@Slf4j
public class WarehouseStockGatewayDbImpl implements WarehouseStockGateway {
  private final IWdtStockSpecService wdtStockSpecService;
  private final ItemSkuGateway itemSkuGateway;

  @Override
  public Map<String, BigDecimal> getStock(String skuNo) {
    if (StrUtil.isBlank(skuNo)) {
      return Collections.emptyMap();
    }
    final List<WdtStockSpec> list =
        wdtStockSpecService.lambdaQuery().eq(WdtStockSpec::getSpecNo, skuNo).list();
    if (list.isEmpty()) {
      return Collections.emptyMap();
    }
    return list.stream()
        .collect(Collectors.toMap(WdtStockSpec::getWarehouseNo, WdtStockSpec::getStockNum));
  }

  @Override
  public Map<String, Map<String, BigDecimal>> getStock(Collection<String> skuNos) {
    if (CollectionUtil.isEmpty(skuNos)) {
      return Collections.emptyMap();
    }
    final List<WdtStockSpec> list =
        wdtStockSpecService.lambdaQuery().in(WdtStockSpec::getSpecNo, skuNos).list();
    if (list.isEmpty()) {
      return Collections.emptyMap();
    }
    return list.stream()
        .collect(
            Collectors.groupingBy(
                WdtStockSpec::getSpecNo,
                Collectors.toMap(WdtStockSpec::getWarehouseNo, WdtStockSpec::getStockNum)));
  }

  @Override
  public BigDecimal getStock(String skuNo, String warehouseNo) {
    if (StrUtil.isBlank(skuNo)) {
      return BigDecimal.ZERO;
    }
    final List<WdtStockSpec> list =
        wdtStockSpecService
            .lambdaQuery()
            .eq(WdtStockSpec::getSpecNo, skuNo)
            .eq(WdtStockSpec::getWarehouseNo, warehouseNo)
            .list();
    if (list.isEmpty()) {
      return BigDecimal.ZERO;
    }
    return list.get(0).getStockNum();
  }

  @Override
  public Map<String, BigDecimal> getStock(Collection<String> skuNos, String warehouseNo) {
    if (CollectionUtil.isEmpty(skuNos)) {
      return Collections.emptyMap();
    }
    final List<WdtStockSpec> list =
        wdtStockSpecService
            .lambdaQuery()
            .in(WdtStockSpec::getSpecNo, skuNos)
            .eq(WdtStockSpec::getWarehouseNo, warehouseNo)
            .eq(WdtStockSpec::getDefect, 0)
            .list();
    if (list.isEmpty()) {
      return Collections.emptyMap();
    }
    return list.stream()
        .collect(Collectors.toMap(WdtStockSpec::getSpecNo, WdtStockSpec::getStockNum, (a, b) -> a));
  }

  @Override
  public Map<String, Map<String, BigDecimal>> getStock(
      Collection<String> skuNos, Collection<String> warehouseNos) {
    if (CollectionUtil.isEmpty(skuNos)) {
      return Collections.emptyMap();
    }
    final List<WdtStockSpec> list =
        wdtStockSpecService
            .lambdaQuery()
            .in(WdtStockSpec::getSpecNo, skuNos)
            .in(WdtStockSpec::getWarehouseNo, warehouseNos)
            .list();
    if (list.isEmpty()) {
      return Collections.emptyMap();
    }
    return list.stream()
        .collect(
            Collectors.groupingBy(
                WdtStockSpec::getSpecNo,
                Collectors.toMap(WdtStockSpec::getWarehouseNo, WdtStockSpec::getStockNum)));
  }

  @Override
  public BigDecimal getStockSum(String skuNo) {
    final List<WdtStockSpec> list =
        wdtStockSpecService.lambdaQuery().eq(WdtStockSpec::getSpecNo, skuNo).list();
    if (list.isEmpty()) {
      return BigDecimal.ZERO;
    }
    return list.stream().map(WdtStockSpec::getStockNum).reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  @Override
  public Map<String, BigDecimal> getStockSum(Collection<String> skuNos) {
    if (skuNos.isEmpty()) {
      return Collections.emptyMap();
    }
    final List<WdtStockSpec> list =
        wdtStockSpecService.lambdaQuery().in(WdtStockSpec::getSpecNo, skuNos).list();
    if (list.isEmpty()) {
      return Collections.emptyMap();
    }
    return list.stream()
        .collect(
            Collectors.groupingBy(
                WdtStockSpec::getSpecNo,
                Collectors.reducing(BigDecimal.ZERO, WdtStockSpec::getStockNum, BigDecimal::add)));
  }

  @Override
  public Map<String, BigDecimal> getStockSum(
      Collection<String> skuNos, Collection<String> warehouseNos) {
    if (CollectionUtil.isEmpty(skuNos) || CollectionUtil.isEmpty(warehouseNos)) {
      return Collections.emptyMap();
    }
    final List<WdtStockSpec> list =
        wdtStockSpecService
            .lambdaQuery()
            .in(WdtStockSpec::getSpecNo, skuNos)
            .in(WdtStockSpec::getWarehouseNo, warehouseNos)
            .list();
    if (list.isEmpty()) {
      return Collections.emptyMap();
    }
    return list.stream()
        .collect(
            Collectors.groupingBy(
                WdtStockSpec::getSpecNo,
                Collectors.reducing(BigDecimal.ZERO, WdtStockSpec::getStockNum, BigDecimal::add)));
  }

  @Override
  public Map<String, Map<StockType, BigDecimal>> getStockDetail(String skuNo) {
    return getStockDetail(Collections.singletonList(skuNo)).get(skuNo);
  }

  @Override
  public Map<String, Map<String, Map<StockType, BigDecimal>>> getStockDetail(
      Collection<String> skuNos) {
    if (CollectionUtil.isEmpty(skuNos)) {
      return Collections.emptyMap();
    }
    final Map<String, Map<String, Map<StockType, BigDecimal>>> result =
        skuNos.stream()
            .collect(Collectors.toMap(Function.identity(), it -> Collections.emptyMap()));
    final List<WdtStockSpec> list =
        wdtStockSpecService.lambdaQuery().in(WdtStockSpec::getSpecNo, skuNos).list();
    if (CollectionUtil.isEmpty(list)) {
      return result;
    }
    result.putAll(
        list.stream()
            .collect(
                Collectors.groupingBy(
                    WdtStockSpec::getSpecNo,
                    Collectors.toMap(WdtStockSpec::getWarehouseNo, this::wdtStockSpecToMap))));
    return result;
  }

  @NonNull
  private Map<StockType, BigDecimal> wdtStockSpecToMap(WdtStockSpec datum) {
    final Map<StockType, BigDecimal> stockMap = new HashMap<>();
    stockMap.put(StockType.STOCK, datum.getStockNum());
    stockMap.put(StockType.WMS_SYNC_STOCK, datum.getWmsSyncStock());
    stockMap.put(StockType.WMS_STOCK_DIFF, datum.getWmsStockDiff());
    stockMap.put(StockType.UNPAY_NUM, datum.getUnpayNum());
    stockMap.put(StockType.SUBSCRIBE_NUM, datum.getSubscribeNum());
    stockMap.put(StockType.ORDER_NUM, datum.getOrderNum());
    stockMap.put(StockType.SENDING_NUM, datum.getSendingNum());
    stockMap.put(StockType.PURCHASE_NUM, datum.getPurchaseNum());
    stockMap.put(StockType.TRANSFER_NUM, datum.getTransferNum());
    stockMap.put(StockType.TO_PURCHASE_NUM, datum.getToPurchaseNum());
    stockMap.put(StockType.PURCHASE_ARRIVE_NUM, datum.getPurchaseArriveNum());
    stockMap.put(StockType.WMS_PREEMPTY_STOCK, datum.getWmsPreemptyStock());
    stockMap.put(StockType.AVAILABLE_SEND_STOCK, datum.getAvailableSendStock());
    stockMap.put(StockType.PART_PAID_NUM, datum.getPartPaidNum());
    stockMap.put(StockType.REFUND_EXCH_NUM, datum.getRefundExchNum());
    stockMap.put(StockType.REFUND_NUM, datum.getRefundNum());
    stockMap.put(StockType.REFUND_ONWAY_NUM, datum.getRefundOnwayNum());
    stockMap.put(StockType.RETURN_EXCH_NUM, datum.getReturnExchNum());
    stockMap.put(StockType.RETURN_NUM, datum.getReturnNum());
    stockMap.put(StockType.RETURN_ONWAY_NUM, datum.getReturnOnwayNum());
    stockMap.put(StockType.TO_TRANSFER_NUM, datum.getToTransferNum());
    stockMap.put(StockType.WMS_PREEMPTY_DIFF, datum.getWmsPreemptyDiff());
    stockMap.put(StockType.LOCK_NUM, datum.getLockNum());

    return stockMap;
  }

  @Override
  public BigDecimal getItemStockSum(String itemNo, String warehouseNo) {
    return getItemStockSum(
            Collections.singletonList(itemNo), Collections.singletonList(warehouseNo))
        .getOrDefault(itemNo, BigDecimal.ZERO);
  }

  @Override
  public BigDecimal getItemStockSum(String itemNo) {
    return getItemStockSum(Collections.singletonList(itemNo), Collections.emptyList())
        .getOrDefault(itemNo, BigDecimal.ZERO);
  }

  @Override
  public Map<String, BigDecimal> getItemStockSum(Collection<String> itemNos) {
    return getItemStockSum(itemNos, Collections.emptyList());
  }

  @Override
  public Map<String, BigDecimal> getItemStockSum(
      Collection<String> itemNos, Collection<String> warehouseNos) {
    if (CollectionUtil.isEmpty(itemNos)) {
      return Collections.emptyMap();
    }
    final Map<String, BigDecimal> result =
        itemNos.stream().collect(Collectors.toMap(Function.identity(), k -> BigDecimal.ZERO));
    final Map<String, List<String>> skuCodesMap = itemSkuGateway.getSkuCodesBatch(itemNos);
    if (CollectionUtil.isEmpty(skuCodesMap)) {
      return result;
    }
    final List<String> skuCodes =
        skuCodesMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
    final List<WdtStockSpec> data =
        wdtStockSpecService.lambdaQuery().in(WdtStockSpec::getSpecNo, skuCodes).list();
    if (CollectionUtil.isEmpty(data)) {
      return result;
    }
    Stream<WdtStockSpec> stream = data.stream();
    if (!CollectionUtil.isEmpty(warehouseNos)) {
      stream = stream.filter(v -> warehouseNos.contains(v.getWarehouseNo()));
    }
    result.putAll(
        stream.collect(
            Collectors.groupingBy(
                WdtStockSpec::getSpecNo,
                Collectors.mapping(
                    WdtStockSpec::getStockNum,
                    Collectors.reducing(BigDecimal.ZERO, NumberUtil::add)))));
    return result;
  }
}
