package com.daddylab.supplier.item.domain.platformItem.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/1/10
 */
@Data
public class SyncResult {
    final String goodsId;
    final SyncStatus syncStatus;
    final PlatformItem platformItem;
    final PlatformItemSku platformItemSku;
}
