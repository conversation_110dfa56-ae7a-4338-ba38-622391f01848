package com.daddylab.supplier.item.application.salesInStock.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2023年10月23日 9:48 AM
 */
@Data
@ApiModel("销售入库单列表返回")
public class SalesInStockPageVO {

    @ApiModelProperty("入库单id")
    private Long stockinId;

    @ApiModelProperty("入库单号")
    private String orderNo;

    @ApiModelProperty("类型。1:退款;2:退货;3:换货;4:退款不退货")
    private Integer type;

    @ApiModelProperty("实际入库仓")
    private String warehouseName;

    @ApiModelProperty("入库人")
    private String operatorName;

    @ApiModelProperty("店铺")
    private String shopName;

    @ApiModelProperty("退换单号")
    private String refundNo;

    @ApiModelProperty("入库状态。0:未入库;1:待入库;2:部分入库;3:全部入库;4:终止入库")
    private String stockinStatus;


}
