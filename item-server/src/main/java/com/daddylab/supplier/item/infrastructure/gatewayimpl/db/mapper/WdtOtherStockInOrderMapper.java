package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOtherStockInOrder;

/**
 * <p>
 * 旺店通其他入库单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
public interface WdtOtherStockInOrderMapper extends DaddyBaseMapper<WdtOtherStockInOrder> {

}
