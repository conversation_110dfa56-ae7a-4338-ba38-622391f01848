package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.application.afterSales.AfterSalesReceiveInfoVO;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesReceive;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesReceiveState;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;

/**
 * <p>
 * 售后单登记收货 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
public interface IAfterSalesReceiveService extends IDaddyService<AfterSalesReceive> {

    /**
     * 批量查询退换单收货状态
     *
     * @param refundOrderNos 退换单号集合
     */
    Map<String, AfterSalesReceiveState> queryReceiveStateBatch(Collection<String> refundOrderNos);

    /**
     * 获取退换单收货记录视图模型
     * 请注意，强制确认产生的收货记录不会返回
     *
     * @param refundOrderNo 退换单号
     */
    Optional<AfterSalesReceiveInfoVO> getAfterSalesReceiveInfoVO(String refundOrderNo);

    /**
     * 更新退换单收货状态到已确认
     *
     * @param refundOrderNo 退换单号
     * @return 是否更新成功
     */
    boolean confirmReceive(String refundOrderNo);

    /**
     * 强制销退确认
     *
     * @param refundOrderNo 退换单号
     * @param update 更新收货记录或新增收货记录
     * @return 是否成功
     */
    boolean forceConfirmReceive(String refundOrderNo, Boolean update);
}
