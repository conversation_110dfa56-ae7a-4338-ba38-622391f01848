package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataBackup;

/**
 * <p>
 * 数据备份表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
public interface IDataBackupService extends IDaddyService<DataBackup> {

    long backup(Object backupData, String... keys);

}
