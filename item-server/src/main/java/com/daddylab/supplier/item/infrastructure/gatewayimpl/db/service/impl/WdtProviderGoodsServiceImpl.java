package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtProviderGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtProviderGoodsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtProviderGoodsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-15
 */
@Service
public class WdtProviderGoodsServiceImpl extends DaddyServiceImpl<WdtProviderGoodsMapper, WdtProviderGoods> implements IWdtProviderGoodsService {

}
