package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11 10:32 上午
 * @description
 */
@Data
@ApiModel("商品名称下拉选返回")
public class NameDropDownVo implements Serializable {


    private static final long serialVersionUID = 6039959557284642363L;

    @ApiModelProperty("商品名称")
    private String name;
    @ApiModelProperty("商品id")
    private Long id;
}
