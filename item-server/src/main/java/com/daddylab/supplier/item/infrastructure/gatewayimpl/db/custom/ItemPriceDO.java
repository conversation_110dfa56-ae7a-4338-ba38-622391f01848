package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemPriceRang;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemPriceType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/20 4:30 下午
 * @description
 */
@Builder
@Data
@AllArgsConstructor
public class ItemPriceDO implements Serializable {

    private static final long serialVersionUID = -3273486642398925686L;
    /**
     * 价格名称
     */
    private String name;

    /**
     * 价格值
     */
    private BigDecimal value;
    /**
     * 价格有效期开始时间
     */
    private Long starTime;
    /**
     * 价格有效期结束时间
     */
    private Long endTime;

    private String remark;
    /**
     * 价格种类, 价格类型 1:采购成本 2:日常销售价 3:划线价格 4:产品活动价 5:渠道最低价 6:自定义价格
     */
    private ItemPriceType itemPriceEnum;

    /**
     * 价格范围分类。1:采购成本价格。2:日常销售价格
     */
    private ItemPriceRang rang;
}
