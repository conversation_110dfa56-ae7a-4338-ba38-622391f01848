package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessageState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessageType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Message implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long configId;

    private String template;

    private String fillContent;

    private Long deletedAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    @TableLogic
    private Integer isDel;

    private MessageState state;

    private MessageType type;

    private Long recipientId;

    private Integer pushDefault;
    private Integer pushRemind;
    private Integer pushMail;
    private Integer pushText;

}
