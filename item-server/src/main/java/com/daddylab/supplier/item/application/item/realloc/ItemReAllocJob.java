package com.daddylab.supplier.item.application.item.realloc;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatMsg;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffListQuery;
import com.daddylab.supplier.item.types.item.ItemBuyerAllocStat;

import lombok.AllArgsConstructor;

import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 离职人员商品重新分配
 *
 * <AUTHOR>
 * @since 2023/11/13
 */
@Service
@AllArgsConstructor
public class ItemReAllocJob {
    private final UserGateway userGateway;
    private final ItemMapper itemMapper;
    private final ItemReAllocJobConfig itemReAllocJobConfig;
    private final RefreshConfig refreshConfig;
    private final MsgSender msgSender;

    @XxlJob("ItemReAllocJob")
    public void run() {
        int pageIndex = 1;
        while (true) {
            final StaffListQuery query = new StaffListQuery();
            query.setPageSize(49);
            query.setPageIndex(pageIndex);
            query.setStatus(0);

            final List<StaffInfo> resignedUsers = userGateway.queryStaffList(query);
            if (resignedUsers.isEmpty()) {
                break;
            }
            final List<Long> resignedUserIds =
                    resignedUsers.stream().map(StaffInfo::getUserId).collect(Collectors.toList());
            List<ItemBuyerAllocStat> itemBuyerAllocStats =
                    itemMapper.getItemBuyerAllocStatByBuyerIds(resignedUserIds);
            final Map<Long, List<ItemBuyerAllocStat>> itemBuyerAllocStatGroup =
                    itemBuyerAllocStats.stream()
                            .collect(
                                    Collectors.groupingBy(
                                            ItemBuyerAllocStat::getUserId, Collectors.toList()));
            for (StaffInfo resignedUser : resignedUsers) {
                final Set<String> notifyUsers = new HashSet<>();
                final List<ItemBuyerAllocStat> itemBuyerAllocStatList =
                        itemBuyerAllocStatGroup.get(resignedUser.getUserId());
                if (itemBuyerAllocStatList == null) {
                    continue;
                }
                for (ItemBuyerAllocStat itemBuyerAllocStat : itemBuyerAllocStatList) {
                    final String notifyUser =
                            itemReAllocJobConfig
                                    .getNotifyUsers()
                                    .get(itemBuyerAllocStat.getBusinessLine());
                    if (notifyUser == null) {
                        continue;
                    }
                    notifyUsers.add(notifyUser);
                }

                final WechatMsg wechatMsg = new WechatMsg();
                wechatMsg.setTitle(
                        String.format(
                                "商品采购员【%s】已离职，负责的商品请及时分配新的采购负责人！", resignedUser.getNickname()));
                wechatMsg.setContent("您有一个新的待办任务，请及时处理");
                wechatMsg.setLink(
                        refreshConfig.getDomain()
                                + String.format(
                                        "/commodity-management/back-end-good/list?buyerUserId=%s&buyer=%s",
                                        resignedUser.getUserId(), resignedUser.getNickname()));
                wechatMsg.setRecipient(String.join(",", notifyUsers));
                wechatMsg.setType(1);
                msgSender.send(wechatMsg);
            }

            pageIndex++;
        }
    }
}
