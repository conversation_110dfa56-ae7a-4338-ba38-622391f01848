package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Organization;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 采购组织表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
public interface OrganizationMapper extends DaddyBaseMapper<Organization> {

    @Select("select id from organization where name like '%老爸评测科技有限公司%' and is_del = 0 and type = 2 order by id desc limit 1")
    Long getRootOrganizationId();

}
