package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BankNumber;
import com.daddylab.supplier.item.types.bank.BankNumberQuery;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 银行联行号 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
public interface BankNumberMapper extends DaddyBaseMapper<BankNumber> {

    List<BankNumber> bankNumberQuery(@Param("query") BankNumberQuery query);
}
