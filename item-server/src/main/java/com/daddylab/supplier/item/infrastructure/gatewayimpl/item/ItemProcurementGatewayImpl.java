package com.daddylab.supplier.item.infrastructure.gatewayimpl.item;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.daddylab.supplier.item.domain.item.gateway.ItemProcurementGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Buyer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemProcurement;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBuyerService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemProcurementService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 11:59 上午
 * @description
 */
@Service
public class ItemProcurementGatewayImpl implements ItemProcurementGateway {

    @Autowired
    IItemProcurementService iItemProcurementService;

    @Autowired
    IBuyerService iBuyerService;

    @Override
    public void saveOrUpdateProcurement(ItemProcurement itemProcurement) {
        iItemProcurementService.saveOrUpdate(itemProcurement);
    }

    @Override
    public ItemProcurement getProcurementByItemId(Long itemId) {
        QueryWrapper<ItemProcurement> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ItemProcurement::getItemId, itemId);
        return iItemProcurementService.getOne(queryWrapper);
    }

    @Override
    public Boolean syncBuyer(Long itemId, Long buyerId) {
        LambdaUpdateWrapper<ItemProcurement> objectLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        objectLambdaUpdateWrapper.eq(ItemProcurement::getItemId, itemId)
                .set(ItemProcurement::getBuyerId, buyerId);
        return iItemProcurementService.update(objectLambdaUpdateWrapper);
    }

    @Override
    public ItemProcurement getItemProcurement(Long itemId) {
        LambdaQueryWrapper<ItemProcurement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ItemProcurement::getItemId, itemId);
        return iItemProcurementService.getOne(queryWrapper);
    }

    @Override
    public Map<Long, String> getQcIdList(List<Long> itemIdList) {
        LambdaQueryWrapper<ItemProcurement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ItemProcurement::getItemId, itemIdList);
        List<ItemProcurement> list = iItemProcurementService.list(queryWrapper);
        return list.stream().collect(Collectors.toMap(ItemProcurement::getItemId, ItemProcurement::getQcIds, (a, b) -> a));
    }

    @Override
    public Map<Long, Long> getBuyerUidList(List<Long> itemIdList) {
        if (CollectionUtil.isEmpty(itemIdList)) {
            return new HashMap<>();
        }
        Map<Long, Long> itemBuyerUidMap = new HashMap<>(itemIdList.size());

        LambdaQueryWrapper<ItemProcurement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ItemProcurement::getItemId, itemIdList);
        List<ItemProcurement> list = iItemProcurementService.list(queryWrapper);
        final Map<Long, ItemProcurement> itemProcurementMap = list.stream()
                .collect(Collectors.toMap(ItemProcurement::getItemId, Function.identity()));

        final List<Long> buyerIdList = list.stream().map(ItemProcurement::getBuyerId).collect(Collectors.toList());
        final Map<Long, Buyer> buyerMap = iBuyerService.lambdaQuery()
                .in(Buyer::getId, buyerIdList)
                .list().stream().collect(Collectors.toMap(Buyer::getId, Function.identity()));

        itemIdList.forEach(itemId -> {
            final ItemProcurement itemProcurement = itemProcurementMap.get(itemId);
            if (Objects.nonNull(itemProcurement) && Objects.nonNull(itemProcurement.getBuyerId())) {
                final Long buyerId = itemProcurement.getBuyerId();
                final Buyer buyer = buyerMap.get(buyerId);
                if (Objects.nonNull(buyer) && Objects.nonNull(buyer.getUserId())) {
                    itemBuyerUidMap.put(itemId, buyer.getUserId());
                }
            }
        });

        return itemBuyerUidMap;
    }
}
