package com.daddylab.supplier.item.application.platformItem;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSkuInventorySetting;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/7/29
 */
@Mapper
public interface PlatformItemSkuInventorySettingAssembler {
    PlatformItemSkuInventorySettingAssembler INSTANCE = Mappers.getMapper(
            PlatformItemSkuInventorySettingAssembler.class);

    PlatformItemSkuInventorySetting copy(PlatformItemSkuInventorySetting source);
}
