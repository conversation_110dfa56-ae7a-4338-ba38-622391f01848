package com.daddylab.supplier.item.domain.dataFetch;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/4/27
 */
@AllArgsConstructor
@Getter
public enum FetchStatus implements IIntegerEnum {
    WAIT(0, "待同步"),
    SUCCESS(1, "同步完成"),
    ERROR(2, "同步异常"),
    ;
    private final Integer value;
    private final String desc;
}
