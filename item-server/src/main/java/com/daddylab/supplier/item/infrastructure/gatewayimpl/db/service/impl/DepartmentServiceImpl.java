package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Department;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DepartmentMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDepartmentService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 采购部门表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@Service
public class DepartmentServiceImpl extends DaddyServiceImpl<DepartmentMapper, Department> implements IDepartmentService {

}
