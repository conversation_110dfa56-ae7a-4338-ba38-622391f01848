package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTrainingMaterialsProcess;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemTrainingMaterialsProcessMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemTrainingMaterialsProcessService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 新品商品培训资料处理流程 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Service
public class ItemTrainingMaterialsProcessServiceImpl extends DaddyServiceImpl<ItemTrainingMaterialsProcessMapper, ItemTrainingMaterialsProcess> implements IItemTrainingMaterialsProcessService {

}
