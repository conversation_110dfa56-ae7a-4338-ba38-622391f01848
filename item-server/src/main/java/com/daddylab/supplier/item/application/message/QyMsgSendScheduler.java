package com.daddylab.supplier.item.application.message;

import com.alibaba.fastjson.JSON;
import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.domain.messageRobot.exceptions.MessageRobotException;
import com.daddylab.supplier.item.domain.messageRobot.gateway.MessageRobotGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatMsg;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWechatMsgService;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/5/14
 */
@Component
@Slf4j
public class QyMsgSendScheduler {
    @Autowired
    private MsgSender msgSender;
    
    @Autowired
    private IWechatMsgService wechatMsgService;
    
    @Autowired
    private MessageRobotGateway messageRobotGateway;
    
    @Data
    public static class ScheduleParams {
        int limit = 100;
    }
    
    @XxlJob("QyMsgSendScheduler:schedule")
    public void schedule() {
        final ScheduleParams scheduleParams = getScheduleParams();
        final List<WechatMsg> list = wechatMsgService.lambdaQuery()
                .eq(WechatMsg::getState, 0)
                .orderByAsc(WechatMsg::getId)
                .last("limit " + scheduleParams.getLimit())
                .list();
        
        final List<Long> ids = list.stream().map(WechatMsg::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        if (wechatMsgService.updateState(ids, 0, 2)) {
            for (WechatMsg wechatMsg : list) {
                log.debug("[企微消息发送]id={}", wechatMsg.getId());
                try {
                    if (Objects.equals(wechatMsg.getType(), RemindType.WEBHOOK.getValue())) {
                        messageRobotGateway.sendMarkdownWebhook(wechatMsg.getRecipient(),
                                "# " + wechatMsg.getTitle() + "\n" + wechatMsg.getContent());
                    } else {
                        msgSender.send(wechatMsg);
                    }
                } catch (Exception e) {
                    log.error("[企微消息发送]发送失败，id={}", wechatMsg.getId(), e);
                }
            }
        }
    }
    
    @NonNull
    private static ScheduleParams getScheduleParams() {
        return Optional.ofNullable(XxlJobHelper.getJobParam())
                .filter(StringUtil::isNotBlank)
                .map(v -> JSON.parseObject(v, ScheduleParams.class))
                .orElseGet(ScheduleParams::new);
    }
    
}
