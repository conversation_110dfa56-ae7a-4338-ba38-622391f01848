package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtPlatformGoods;
import java.util.Collection;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 旺店通系统数据-平台商品 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Repository
public interface WdtPlatformGoodsMapper extends DaddyBaseMapper<WdtPlatformGoods> {
    /**
     * 批量插入或者已存在重复记录时更新数据
     *
     * @param goods 数据集合
     */
    void saveOrUpdateBatch(@Param("goods") Collection<WdtPlatformGoods> goods);
}
