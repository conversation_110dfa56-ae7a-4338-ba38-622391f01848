package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemRunning;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemRunningMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemRunningService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 运营信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-29
 */
@Service
public class ItemRunningServiceImpl extends DaddyServiceImpl<ItemRunningMapper, ItemRunning> implements IItemRunningService {

}
