package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsVo;
import com.daddylab.supplier.item.controller.item.dto.*;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseItem;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemBaseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 商品 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface IItemService extends IDaddyService<Item> {


    List<NameDropDownVo> dropDownList(String name, Integer offset, Integer size,Boolean useForItemLaunchPlan,Long providerId);

    /**
     * 分页查询
     *
     * @param itemPageQuery
     * @return
     */
    List<ItemPageVo> queryPage(ItemPageQuery itemPageQuery);

    /**
     * 分页查询 计数统计
     *
     * @param itemPageQuery
     * @return
     */
    Integer queryPageCount(ItemPageQuery itemPageQuery);

    /**
     * 查询 商品详情，基础数据部分
     *
     * @param itemId
     * @return
     */
    ItemBaseDO queryDetailBase(Long itemId);

    /**
     * 查询商品基础信息（带主图）
     */
    ItemBaseDO queryDetailBaseWithImage(Long itemId);

    /**
     * 查询商品的简单信息
     *
     * @param cmd
     * @return
     */
    List<ItemSimpleViewVo> queryItemSimple(ItemSimpleViewCmd cmd);

    /**
     * 查询出 sku 维度的商品导出，导出的数据量。
     *
     * @param cmd
     * @return
     */
    Integer countExportSku(ExportCmd cmd);

//    ExportItemOtherDto queryOtherItemExportDto(Long itemId);


    PurchaseItem getPurchaseBySku(String itemSku);

    NewGoodsVo getNewGoodsById(Long id);

    /**
     * 批量查询（主键查询）
     *
     * @param ids IDs
     * @return List<Item>
     */
    List<Item> selectBatchByIds(List<Long> ids);

    /**
     * 获取商品的采购人
     * key：itemId
     * val：采购人
     *
     * @param itemIds 商品 ID 集合
     * @return map
     */
    Map<Long, ItemBuyerDto> getItemsBuyerMap(List<Long> itemIds);

    /**
     * 批量查询商品的采购人信息
     *
     * @param itemIds 商品 IDs
     * @return List<ItemBuyerDto>
     */
    List<ItemBuyerDto> selectBatchBuyersByItemIds(List<Long> itemIds);

    /**
     * 获取商品负责人信息
     * key：itemId
     * val：商品负责人
     *
     * @param itemIds 商品 ID 集合
     * @return map
     */
    Map<Long, ItemPrincipalDto> getItemsPrincipalMap(List<Long> itemIds);

    /**
     * 获取商品相关信息
     * key：itemId
     * val: 商品相关信息
     *
     * @param itemIds 商品 ID 集合
     * @return Map<Long, ItemWithLaunchPlanDto>
     */
    Map<Long, ItemWithLaunchPlanDto> getItemDtoMap(List<Long> itemIds);

    /**
     * 生成映射
     * key: itemNo
     * val: item
     *
     * @param itemNos 商品编号
     * @return Map<String, Item>
     */
    Map<String, Item> getNoToItemMap(List<String> itemNos);

    boolean reAudit(Long itemId);

    /**
     * 更新商品的上新状态（当商品上新状态变更为已上架时，会将商品状态同时修改为已上架）
     *
     * @param itemId 商品ID
     * @param launchStatus 需要更新成的状态
     */
    boolean updateLaunchStatusById(Long itemId, ItemLaunchStatus launchStatus);

    /**
     * 获取商品当前的上新状态
     *
     * @param itemId 商品ID
     */
    ItemLaunchStatus getItemLaunchStatus(Long itemId);

    String getPartnerProviderItemSn(Long itemId);

    /**
     * 获取商品详情信息
     *
     * @param itemId 商品 ID
     * @return ItemSkuDetailVo
     */
    ItemSkuDetailVo getItemSkuDetail(Long itemId);

    /**
     * 获取商品的外链
     *
     * @param itemId 商品 ID
     * @return ItemOutlinkDto
     */
    ItemOutlinkDto getItemOutlink(Long itemId);

    /**
     * get by code
     *
     * @param code 商品编码
     * @return item
     */
    Item getByCode(String code);

    Item getByMixedCode(String code);
    List<Item> getBatchByMixedCode(List<String> codes);

    /**
     * 获取商品的类目 path
     *
     * @param itemNo 商品编码
     * @return String
     */
    String getItemCategoryPath(String itemNo);

    /**
     * 查询商品属性
     *
     * @param id item#id
     * @return List<AttrDto>
     */
    List<AttrDto> selectItemAttrDtosByItemId(Long id);

    /**
     * 更新供应商
     *
     * @param itemId 商品ID
     * @param providerId 供应商ID
     * @return
     */
    boolean updateProvider(long itemId, long providerId);
}
