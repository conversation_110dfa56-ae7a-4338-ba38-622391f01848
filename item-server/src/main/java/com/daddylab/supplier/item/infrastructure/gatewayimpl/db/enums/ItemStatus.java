package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品状态 0:待上架 1:在售中 2:已下架 3:废弃
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/20 10:17 上午
 * @description
 */
@Getter
@AllArgsConstructor
public enum ItemStatus implements IEnum {
    /**
     *
     */
    WAIT(0, "待上架"),
    ON(1, "在售中"),
    OUT(2, "已下架"),
    DISCARD(3, "已废弃"),
    ;

    @EnumValue
    private Integer value;
    private String desc;

}
