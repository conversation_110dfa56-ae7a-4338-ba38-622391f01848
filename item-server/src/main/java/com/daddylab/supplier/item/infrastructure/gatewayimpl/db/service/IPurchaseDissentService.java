package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseDissent;

/**
 * <p>
 * 采购异议 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
public interface IPurchaseDissentService extends IDaddyService<PurchaseDissent> {

}
