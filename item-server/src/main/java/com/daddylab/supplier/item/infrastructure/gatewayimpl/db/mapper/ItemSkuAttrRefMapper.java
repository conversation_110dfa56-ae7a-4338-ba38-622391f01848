package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuAttrRefDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSkuAttrRef;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 商品SKU关联属性 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Repository
public interface ItemSkuAttrRefMapper extends DaddyBaseMapper<ItemSkuAttrRef> {

    /**
     * 查询和sku关联的属性内容
     *
     * @param skuIdList
     * @return skuId-attrId
     */
    List<SkuAttrRefDO> getSkuAttrList(List<Long> skuIdList);

    List<SkuAttrRefDO> getSkuAttrListByCode(@Param("skuCode") String skuCode);

    List<SkuAttrRefDO> getSkuAttrListByItemId(@Param("itemId") Long itemId);
}
