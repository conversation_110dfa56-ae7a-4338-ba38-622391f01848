package com.daddylab.supplier.item.infrastructure.oa.constants;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 3407886014828595961 全额付款，发票现交 -3773311331841129162 全额付款，发票待开 8497093189833343065 全额付款，发票已交
 * -8818491797558505916 分次付款，发票现交 8678677603647572021 分次付款，发票待开 507817062767611144 分次付款，发票已交
 *
 * <AUTHOR>
 * @since 2023/11/23
 */
@RequiredArgsConstructor
@Getter
public enum PaymentType implements IEnum<Long> {
    FULL_PAYMENT_INVOICE_SUBMIT(3407886014828595961L, "全额付款，发票现交"),
    FULL_PAYMENT_TO_BE_INVOICED(-3773311331841129162L, "全额付款，发票待开"),
    FULL_PAYMENT_INVOICE_SUBMITTED(8497093189833343065L, "全额付款，发票已交"),
    PAYMENT_IN_INSTALLMENTS_INVOICE_SUBMIT(-8818491797558505916L, "分次付款，发票现交"),
    PARTIAL_PAYMENT_TO_BE_INVOICED(8678677603647572021L, "分次付款，发票待开"),
    PARTIAL_PAYMENT_INVOICE_SUBMITTED(507817062767611144L, "分次付款，发票已交"),
    ;
    @JsonValue private final Long value;
    private final String desc;
}
