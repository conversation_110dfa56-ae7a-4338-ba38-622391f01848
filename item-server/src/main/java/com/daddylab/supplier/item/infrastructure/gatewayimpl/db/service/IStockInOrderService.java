package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.StockInOrderDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockInState;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 采购入库表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
public interface IStockInOrderService extends IDaddyService<StockInOrder> {

    void setKingDeeId(Long id, String kingDeeId);

    void removeKingDeeId(Long id);

    Integer stateCount(StockInState stockInState, Long purchaseOrderId);

    /**
     * 计算采购单的已入库数量
     *
     * @param purchaseOrderIdList
     * @return
     */
    List<StockInOrderDO> receiptQuantity(List<Long> purchaseOrderIdList);

    /**
     * 已申请的入库单的数量。
     * <p>
     * 待提交状态+待入库状态+已入库状态
     *
     * @param purchaseOrderIdList
     * @return
     */
    List<StockInOrderDO> sumHadAndWillReceiptQuantity(List<Long> purchaseOrderIdList);

    /**
     * 采购单是否可以关联到出库单
     *
     * @param purchaseOrderId
     * @return
     */
    Boolean related(Long purchaseOrderId);

    List<Long> relatedIdList(Long purchaseOrderId);

    /**
     * 和此采购单关联的，非 已入库状态的入库单数量（即，待提交和待入库状态的入库单数量）
     *
     * @param purchaseOrderId
     * @return
     */
    Integer noeWarehousingCount(Long purchaseOrderId);

    /**
     * 根据采购单号更新入库单状态
     * @param purchaseOrderNos
     * @param state
     */
    void updateStatusByPurchaseOrderNos(Collection<String> purchaseOrderNos, StockInState state);

//    void updateStatus(String no,StockInState status);


}
