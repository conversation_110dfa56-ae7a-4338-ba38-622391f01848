package com.daddylab.supplier.item.controller.item.dto;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: <PERSON>
 * @Date: 2022/5/31 19:26
 * @Description: 请描述下这个类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("商品上新计划关联商品查询参数")
public class ItemLaunchPlanLinkItemPageQuery extends PageQuery {
    @ApiModelProperty(value = "商品上新计划")
    private Long planId;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "商品编码")
    private String itemCode;
}
