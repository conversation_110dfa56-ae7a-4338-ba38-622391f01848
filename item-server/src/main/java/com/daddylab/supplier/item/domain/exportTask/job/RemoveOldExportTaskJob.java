package com.daddylab.supplier.item.domain.exportTask.job;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.domain.exportTask.ExportTaskGateway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * <AUTHOR> up
 * @date 2022/5/25 11:36 上午
 */
@Service
public class RemoveOldExportTaskJob {

    @Autowired
    ExportTaskGateway exportTaskGateway;

    @XxlJob("removeOldExportTaskJob")
    public void removeOldExportTaskJob(){
        long l = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000;
        exportTaskGateway.removeExpireTask(l);
    }



}
