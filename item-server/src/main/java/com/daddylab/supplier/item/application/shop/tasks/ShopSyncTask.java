package com.daddylab.supplier.item.application.shop.tasks;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.setting.ShopAPI;
import com.daddylab.mall.wdtsdk.apiv2.setting.dto.ShopQueryShopParams;
import com.daddylab.mall.wdtsdk.apiv2.setting.dto.ShopQueryShopResponse;
import com.daddylab.mall.wdtsdk.apiv2.setting.dto.ShopQueryShopResponse.Details;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.shop.enums.ShopStatus;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.domain.wdt.consts.PlatformMap;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Shop;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DataSource;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/12/31
 */
@Component
@Slf4j
public class ShopSyncTask {

    final private WdtGateway wdtGateway;
    final private ShopGateway shopGateway;

    @Autowired
    public ShopSyncTask(WdtGateway wdtGateway, ShopGateway shopGateway) {
        this.wdtGateway = wdtGateway;
        this.shopGateway = shopGateway;
    }

    /**
     * 同步店铺数据（一个小时跑一次）
     */
    @XxlJob("shopSyncTask")
    public void doTask() {
        final ShopAPI shopAPI = wdtGateway.getAPI(ShopAPI.class);
        int pageIndex = 0;
        int pageSize = 1000;
        while (true) {
            final Pager pager = new Pager(pageSize, pageIndex, true);
            try {
                final ShopQueryShopResponse shopQueryShopResponse = shopAPI
                        .queryShop(new ShopQueryShopParams(), pager);
                final Set<String> shopNoSet = shopQueryShopResponse.getDetails().stream()
                        .map(Details::getShopNo).collect(
                                Collectors.toSet());

                final Map<String, Shop> existShops = shopGateway.batchQueryShopBySn(shopNoSet);
                for (Details detail : shopQueryShopResponse.getDetails()) {
                    final String shopNo = detail.getShopNo();
                    Shop shop = existShops.get(shopNo);
                    if (shop == null) {
                        shop = new Shop();
                        shop.setStatus(ShopStatus.PREPARING);
                        shop.setSn(shopNo);
                    }
                    final Platform platform = mapPlatform(detail.getPlatformId());
                    if (Objects.isNull(platform)) {
                        log.info("未维护平台映射关系，忽略，data:{}", detail);
                        continue;
                    }
                    shop.setPlatform(platform);
                    shop.setName(detail.getShopName());
                    if (StringUtil.isEmpty(shop.getAccount())) {
                        shop.setAccount(detail.getAccountId());
                    }
                    shop.setSource(DataSource.WDT);
                    shop.setWdtIsDisabled(detail.getIsDisabled());
                    shopGateway.createOrUpdateShop(shop);
                }
                if ((pageIndex + 1) * pageSize
                        >= shopQueryShopResponse.getTotalCount()) {
                    break;
                }
                pageIndex++;
            } catch (WdtErpException e) {
                log.error("旺店通店铺同步任务异常", e);
            }
        }
    }

    private Platform mapPlatform(int wdtPlatformId) {
        return PlatformMap.mapPlatform(wdtPlatformId);
    }

}
