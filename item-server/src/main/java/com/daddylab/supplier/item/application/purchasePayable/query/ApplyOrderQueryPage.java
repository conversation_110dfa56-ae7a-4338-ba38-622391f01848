package com.daddylab.supplier.item.application.purchasePayable.query;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayableApplyOrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2023年02月14日 6:11 PM
 */
@Data
@ApiModel("申请付款单列表查询参数")
public class ApplyOrderQueryPage extends PageQuery {

    @ApiModelProperty("供应商id")
    private Long providerId;

    @ApiModelProperty("申请付款单状态枚举")
    private PayableApplyOrderStatus status;

    @ApiModelProperty("申请付款单编号")
    private String no;
}
