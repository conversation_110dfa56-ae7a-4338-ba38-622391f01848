package com.daddylab.supplier.item.controller.test.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 商品编码、采购品名、商品状态、商品资料法务审核时间（最新审核节点）、商品资料QC审核时间（最新审核节点）、直播话术状态、直播话术法务审核时间（最新审核节点）、
 * 直播话术QC审核时间（最新审核节点），是否上架（参照小程序）、上架时间（小程序上架时间）
 *
 * <AUTHOR> up
 * @date 2024年06月20日 10:13 AM
 */
@Data
public class NewGoodsPullSheet {

    @ExcelProperty("商品编码")
    private String itemCode;

    @ExcelProperty("采购品名")
    private String itemName;

    @ExcelProperty("商品状态")
    private String itemStatus;

    @ExcelProperty("商品资料法务审核时间(最新)")
    private String legalAuditTime;

    @ExcelProperty("商品资料QC审核时间(最新)")
    private String qcAuditTime;

    @ExcelProperty("直播话术状态")
    private String liveVerbalStatus;

    @ExcelProperty("直播话术法务审核时间(最新)")
    private String liveVerbalLegalAuditTime;

    @ExcelProperty("直播话术QC审核时间(最新)")
    private String liveVerbalQcAuditTime;

    @ExcelProperty("是否上架（参照小程序）")
    private String inShelf;

    @ExcelProperty("上架时间（小程序上架时间）")
    private String shelfTime;

}
