package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.TmpAfterSalesSupplement;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.TmpAfterSalesSupplementMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ITmpAfterSalesSupplementService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 2月份客服登记差异表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
@Service
public class TmpAfterSalesSupplementServiceImpl extends DaddyServiceImpl<TmpAfterSalesSupplementMapper, TmpAfterSalesSupplement> implements ITmpAfterSalesSupplementService {

}
