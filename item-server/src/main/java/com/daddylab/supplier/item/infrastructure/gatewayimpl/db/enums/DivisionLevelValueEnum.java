package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.enums.PSysBusinessTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.enums.PSysCooperatorEnum;
import io.vavr.Tuple2;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 业务权限隔离，常量定义
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum DivisionLevelValueEnum implements IEnum<Integer> {

  /** 合作方-电商（与之前合作模式兼容） */
  C_E_COMMERCE(0, "电商", DivisionLevelEnum.COOPERATION),

  /** 合作方-绿色家装（与之前合作模式兼容） */
  C_DECORATION(2, "绿色家装", DivisionLevelEnum.COOPERATION),

  // ------------------------------------------------
  /** 商品合作 */
  B_PRODUCT_COOPERATION(10, "商品合作", DivisionLevelEnum.BUSINESS_TYPE),

  /** 商家入驻。 */
  B_MERCHANT_ENTER(11, "商家入驻", DivisionLevelEnum.BUSINESS_TYPE),

  /** 老爸清单 */
  B_DAD_LIST(12, "老爸清单", DivisionLevelEnum.BUSINESS_TYPE),

  /** 定制品 */
  B_CUSTOMIZED_PRODUCTS(13, "定制品", DivisionLevelEnum.BUSINESS_TYPE),

  /** 老爸抽检 */
  B_DAD_SAMPLING_INSPECTION(14, "老爸抽检", DivisionLevelEnum.BUSINESS_TYPE),

  // -----------------------------------

  // 运营模式（店铺独有）
  RUN_SELF(20, "自营", DivisionLevelEnum.RUNNING_MODEL),
  RUN_PROXY(21, "代运营", DivisionLevelEnum.RUNNING_MODEL),
  ;

  @EnumValue private final Integer value;

  private final String desc;
  private final DivisionLevelEnum divisionLevel;

  public static DivisionLevelValueEnum fromBusinessLine(BusinessLine businessLine) {
    switch (businessLine) {
      case DIAN_SHANG:
      case SHANG_JIA_RU_ZHU:
        return C_E_COMMERCE;
      case LV_SE_JIA_ZHUANG:
        return C_DECORATION;
      default:
        throw new IllegalArgumentException("不支持的业务线：" + businessLine);
    }
  }

  public static DivisionLevelValueEnum valueOf(Integer value) {
    return IEnum.getEnumByValue(DivisionLevelValueEnum.class, value);
  }

  public static List<DivisionLevelValueEnum> valueOf(Collection<Integer> values) {
    if (values == null) {
      return null;
    }
    return values.stream()
        .map(DivisionLevelValueEnum::valueOf)
        .filter(Objects::nonNull)
        .distinct()
        .collect(Collectors.toList());
  }

  public static List<DivisionLevelValueEnum> valueOfCorpType(Collection<Integer> values) {
    if (values == null) {
      return null;
    }
    return values.stream()
        .map(DivisionLevelValueEnum::valueOf)
        .filter(it -> it != null && it.getDivisionLevel() == DivisionLevelEnum.COOPERATION)
        .distinct()
        .collect(Collectors.toList());
  }

  public static List<DivisionLevelValueEnum> valueOfCorpTypeNameCommaStr(String value) {
    if (value == null) {
      return null;
    }
    return StrUtil.splitTrim(value, ",").stream()
        .map(DivisionLevelValueEnum::valueOfCorpTypeName)
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  public static DivisionLevelValueEnum valueOfCorpTypeName(String value) {
    if (value == null) {
      return null;
    }
    for (DivisionLevelValueEnum divisionLevelValueEnum : values()) {
      if (divisionLevelValueEnum.getDivisionLevel() == DivisionLevelEnum.COOPERATION
          && divisionLevelValueEnum.getDesc().equals(value)) {
        return divisionLevelValueEnum;
      }
    }
    return null;
  }

  public static List<DivisionLevelValueEnum> valueOf(
      Collection<Integer> values1, Collection<Integer> values2) {
    return Stream.of(values1, values2)
        .filter(Objects::nonNull)
        .flatMap(Collection::stream)
        .map(DivisionLevelValueEnum::valueOf)
        .filter(Objects::nonNull)
        .distinct()
        .collect(Collectors.toList());
  }

  public static Set<DivisionLevelValueEnum> valueOfSet(
      Collection<Integer> values1, Collection<Integer> values2) {
    return Stream.of(values1, values2)
        .filter(Objects::nonNull)
        .flatMap(Collection::stream)
        .map(DivisionLevelValueEnum::valueOf)
        .filter(Objects::nonNull)
        .collect(Collectors.toSet());
  }

  static final Map<PSysCooperatorEnum, DivisionLevelValueEnum> MAP_FROM_PSYS_COOPERATOR_ENUM;
  static final Map<DivisionLevelValueEnum, PSysCooperatorEnum> MAP_TO_PSYS_COOPERATOR_ENUM;

  static {
    MAP_FROM_PSYS_COOPERATOR_ENUM = new HashMap<>();
    MAP_FROM_PSYS_COOPERATOR_ENUM.put(
        PSysCooperatorEnum.ItemCooperatorSupplier, DivisionLevelValueEnum.C_E_COMMERCE);
    MAP_FROM_PSYS_COOPERATOR_ENUM.put(
        PSysCooperatorEnum.ItemCooperatorGreenHome, DivisionLevelValueEnum.C_DECORATION);
    MAP_TO_PSYS_COOPERATOR_ENUM = MapUtil.inverse(MAP_FROM_PSYS_COOPERATOR_ENUM);
  }

  public static DivisionLevelValueEnum mapFromPSysCooperatorEnum(PSysCooperatorEnum cooperator) {
    return MAP_FROM_PSYS_COOPERATOR_ENUM.get(cooperator);
  }

  public static PSysCooperatorEnum mapToPSysCooperatorEnum(
      DivisionLevelValueEnum divisionLevelValueEnum) {
    return MAP_TO_PSYS_COOPERATOR_ENUM.get(divisionLevelValueEnum);
  }

  static final Map<PSysBusinessTypeEnum, DivisionLevelValueEnum> MAP_FROM_PSYS_BUSINESS_TYPE_ENUM;
  static final Map<Tuple2<DivisionLevelValueEnum, DivisionLevelValueEnum>, PSysBusinessTypeEnum>
          MAP_TO_P_SYS_BUSINESS_TYPE_ENUM;

  static {
    MAP_FROM_PSYS_BUSINESS_TYPE_ENUM = new HashMap<>();
    MAP_FROM_PSYS_BUSINESS_TYPE_ENUM.put(
        PSysBusinessTypeEnum.ItemBusinessTypeSupplierCooperate,
        DivisionLevelValueEnum.B_PRODUCT_COOPERATION);
    MAP_FROM_PSYS_BUSINESS_TYPE_ENUM.put(
        PSysBusinessTypeEnum.ItemBusinessTypeSupplierMerchantSettlement,
        DivisionLevelValueEnum.B_MERCHANT_ENTER);
    MAP_FROM_PSYS_BUSINESS_TYPE_ENUM.put(
        PSysBusinessTypeEnum.ItemBusinessTypeSupplierDadList, DivisionLevelValueEnum.B_DAD_LIST);
    MAP_FROM_PSYS_BUSINESS_TYPE_ENUM.put(
        PSysBusinessTypeEnum.ItemBusinessTypeSupplierDadCheck,
        DivisionLevelValueEnum.B_DAD_SAMPLING_INSPECTION);
    MAP_FROM_PSYS_BUSINESS_TYPE_ENUM.put(
        PSysBusinessTypeEnum.ItemBusinessTypeGreenHomeCooperate,
        DivisionLevelValueEnum.B_PRODUCT_COOPERATION);
    MAP_FROM_PSYS_BUSINESS_TYPE_ENUM.put(
        PSysBusinessTypeEnum.ItemBusinessTypeGreenHomeMerchantSettlement,
        DivisionLevelValueEnum.B_MERCHANT_ENTER);
    MAP_FROM_PSYS_BUSINESS_TYPE_ENUM.put(
        PSysBusinessTypeEnum.ItemBusinessTypeGreenHomeDadList, DivisionLevelValueEnum.B_DAD_LIST);
    MAP_FROM_PSYS_BUSINESS_TYPE_ENUM.put(
        PSysBusinessTypeEnum.ItemBusinessTypeGreenHomeDadCheck,
        DivisionLevelValueEnum.B_DAD_SAMPLING_INSPECTION);
    MAP_TO_P_SYS_BUSINESS_TYPE_ENUM = new HashMap<>();
    MAP_TO_P_SYS_BUSINESS_TYPE_ENUM.put(
        new Tuple2<>(
            DivisionLevelValueEnum.C_E_COMMERCE, DivisionLevelValueEnum.B_PRODUCT_COOPERATION),
        PSysBusinessTypeEnum.ItemBusinessTypeSupplierCooperate);
    MAP_TO_P_SYS_BUSINESS_TYPE_ENUM.put(
        new Tuple2<>(DivisionLevelValueEnum.C_E_COMMERCE, DivisionLevelValueEnum.B_MERCHANT_ENTER),
        PSysBusinessTypeEnum.ItemBusinessTypeSupplierMerchantSettlement);
    MAP_TO_P_SYS_BUSINESS_TYPE_ENUM.put(
        new Tuple2<>(DivisionLevelValueEnum.C_E_COMMERCE, DivisionLevelValueEnum.B_DAD_LIST),
        PSysBusinessTypeEnum.ItemBusinessTypeSupplierDadList);
    MAP_TO_P_SYS_BUSINESS_TYPE_ENUM.put(
        new Tuple2<>(
            DivisionLevelValueEnum.C_E_COMMERCE, DivisionLevelValueEnum.B_DAD_SAMPLING_INSPECTION),
        PSysBusinessTypeEnum.ItemBusinessTypeSupplierDadCheck);
    MAP_TO_P_SYS_BUSINESS_TYPE_ENUM.put(
        new Tuple2<>(
            DivisionLevelValueEnum.C_DECORATION, DivisionLevelValueEnum.B_PRODUCT_COOPERATION),
        PSysBusinessTypeEnum.ItemBusinessTypeGreenHomeCooperate);
    MAP_TO_P_SYS_BUSINESS_TYPE_ENUM.put(
        new Tuple2<>(DivisionLevelValueEnum.C_DECORATION, DivisionLevelValueEnum.B_MERCHANT_ENTER),
        PSysBusinessTypeEnum.ItemBusinessTypeGreenHomeMerchantSettlement);
    MAP_TO_P_SYS_BUSINESS_TYPE_ENUM.put(
        new Tuple2<>(DivisionLevelValueEnum.C_DECORATION, DivisionLevelValueEnum.B_DAD_LIST),
        PSysBusinessTypeEnum.ItemBusinessTypeGreenHomeDadList);
    MAP_TO_P_SYS_BUSINESS_TYPE_ENUM.put(
        new Tuple2<>(
            DivisionLevelValueEnum.C_DECORATION, DivisionLevelValueEnum.B_DAD_SAMPLING_INSPECTION),
        PSysBusinessTypeEnum.ItemBusinessTypeGreenHomeDadCheck);
  }

  public static DivisionLevelValueEnum mapFromPSysBusinessTypeEnum(
      PSysBusinessTypeEnum businessType) {
    return MAP_FROM_PSYS_BUSINESS_TYPE_ENUM.get(businessType);
  }

  public static PSysBusinessTypeEnum mapToPSysBusinessTypeEnum(
      DivisionLevelValueEnum value1, DivisionLevelValueEnum value2) {
    return MAP_TO_P_SYS_BUSINESS_TYPE_ENUM.get(new Tuple2<>(value1, value2));
  }

  public static boolean isCorpType(Integer value) {
    final DivisionLevelValueEnum divisionLevelValueEnum = DivisionLevelValueEnum.valueOf(value);
    return divisionLevelValueEnum != null
        && divisionLevelValueEnum.getDivisionLevel() == DivisionLevelEnum.COOPERATION;
  }

  public static String filterCorpType(String corpType) {
    if (corpType == null) {
      return null;
    }
    if (StrUtil.isBlank(corpType)) {
      return "";
    }
    return StrUtil.split(corpType, ",").stream()
        .filter(StrUtil::isNotBlank)
        .map(
            it -> {
              try {
                final int intValue = Integer.parseInt(it);
                if (isCorpType(intValue)) {
                  return it;
                } else if (BusinessLine.SHANG_JIA_RU_ZHU.getValue() == intValue) {
                  return String.valueOf(DivisionLevelValueEnum.C_E_COMMERCE.getValue());
                } else {
                  return null;
                }
              } catch (NumberFormatException e) {
                return null;
              }
            })
        .filter(Objects::nonNull)
        .distinct()
        .sorted()
        .collect(Collectors.joining(","));
  }
}
