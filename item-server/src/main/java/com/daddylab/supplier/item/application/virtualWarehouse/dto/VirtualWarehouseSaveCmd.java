/*
package com.daddylab.supplier.item.application.virtualWarehouse.dto;

import com.alibaba.cola.dto.Command;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryMode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

*/
/**
 * <AUTHOR> up
 * @date 2024年02月29日 4:53 PM
 *//*

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("保存虚拟仓请求参数封装")
public class VirtualWarehouseSaveCmd extends Command {

    private static final long serialVersionUID = -4890661452964009311L;

    @ApiModelProperty("虚拟仓 ID")
    private Long id;

    @ApiModelProperty("虚拟仓名称")
    @NotEmpty(message = "虚拟仓名称不得为空")
    private String name;

    @ApiModelProperty("虚拟仓描述")
    private String description;

    @ApiModelProperty("业务线")
    private Integer businessLine;

    @ApiModelProperty("虚拟仓下属明细列表")
    @Valid
    @Size(min = 1)
    private List<VirtualWarehouseDetailSaveCmd> detailSaveCmdList;

    @ApiModelProperty("虚拟仓下属明细的库存设置列表")
    @Valid
    private List<InventoryGoodsSaveCmd> inventoryGoodsSaveCmdList;

    @ApiModelProperty("虚拟仓状态。0正常。1禁用")
    @NotNull(message = "虚拟仓状态不得为空")
    private Integer status;

    @ApiModelProperty("库存模式 SHARED:(0,共享),LOCK:(1,锁定)")
    @NotNull(message = "库存模式参数不得为空")
    private InventoryMode inventoryMode;

//    @ApiModelProperty("状态监控。NO_CHANGE(0,状态无变化/数据新建)；RUN_TO_FORBID(1,状态运行变为禁止)；FORBID_TO_RUN(2,状态禁止变为运行)")
//    @NotNull(message = "状态监控参数不得为空")
//    private VWarehouseStatusMonitor statusMonitor;

//    private Integer difference;


}
*/
