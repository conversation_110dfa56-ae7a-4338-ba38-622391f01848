package com.daddylab.supplier.item.controller.item.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Date: 2022/6/3 11:59
 * @Description: 商品采购员信息
 */
@Data
public class ItemBuyerDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long buyerUserId;
    private String buyerUserName;
    private String qcUserIds;
//    private Long qcUserId;
//    private String qcUserName;
    private Long itemId;
}
