package com.daddylab.supplier.item.domain.dataFetch;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/2/20
 */
public interface FetcherFactory {

    /**
     * 获取当前数据拉取器工厂负责的数据类型
     */
    FetcherFactoryType fetcherFactoryType();

    /**
     * 生产数据Fetcher实例
     *
     * @param config 配置
     * @param parameters 实例化参数
     * @return Fetcher实例
     */
    Fetcher getFetcher(FetcherConfig config, Map<String, Object> parameters);
}
