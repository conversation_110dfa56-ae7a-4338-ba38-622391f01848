package com.daddylab.supplier.item.infrastructure.gatewayimpl.user;


import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class UserFeignClientFallbackFactory implements FallbackFactory<UserFeignClient> {

    @Override
    public UserFeignClient create(Throwable throwable) {
        return new UserFeignClient() {
            @Override
            public Response<Users> getStaffInfos(StaffInfoQuery query) {
                log.error("UserFeignClient.getStaffInfos error, params:{}", query, throwable);
                return null;
            }

            @Override
            public Response<RowsData<StaffListItem>> staffList(StaffListQuery query) {
                log.error("UserFeignClient.staffList error", throwable);
                return null;
            }
        };
    }
}
