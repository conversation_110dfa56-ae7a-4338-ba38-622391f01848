package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.List;

/**
 * 客服售后登记表
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AfterSalesRegister implements Serializable {

    private static final long serialVersionUID = 1L;

    /** ID */
    private Integer id;

    /** 结算周期 202308 */
    private String settleDuration;

    @JsonIgnore
    public YearMonth getSettleDurationYearMonth() {
        return YearMonth.parse(settleDuration, DatePattern.SIMPLE_MONTH_FORMATTER);
    }

    /** 是否删除 */
    @TableLogic private Integer isDel;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /** 修改时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /** 订单号 */
    private String orderNo;

    /** 付款时间 */
    private LocalDateTime payTime;

    /** 店铺 */
    private String shopName;

    /** 商品编码（SKU） */
    private String itemCode;

    /** 商品名称 */
    private String itemName;

    /** 商品规格 */
    private String specifications;

    /** 售后数量 */
    private String num;

    /** 发货仓 */
    private String warehouse;

    /** 问题描述(选项1级标题) */
    private String desc1;

    /** 问题描述(选项2级标题) */
    private String desc2;

    /** 问题描述(选项3级标题) */
    private String desc3;

    /** 售后处理意见 */
    private String handleAdvice;

    /** 工厂/仓库承担运费 */
    private String factoryUndertakeFreight;

    /** 工厂/仓库承担货款 */
    private String factoryUndertakeGoodsAmount;

    /** 工厂/仓库承担其他补偿 */
    private String factoryUndertakeOtherAmount;

    /** 体验基金（优惠券） */
    private String experienceFundCoupon;

    /** 体验基金（现金） */
    private String experienceFundCash;

    /** 体验金承担原因(选项1级标题) */
    private String experienceFundReason1;

    /** 体验金承担原因(选项2级标题) */
    private String experienceFundReason2;

    /** 体验金承担原因(选项3级标题) */
    private String experienceFundReason3;

    /** 数据来源跟踪 */
    @TableField(
            typeHandler = JacksonTypeHandler.class,
            insertStrategy = FieldStrategy.NOT_NULL,
            updateStrategy = FieldStrategy.NOT_NULL, select = false)
    private AfterSalesRegisterTrace trace;

    /** 数据来源跟踪 */
    @TableField(
            value = "trace",
            typeHandler = JacksonTypeHandler.class,
            update = "json_merge_preserve(%s, #{et.traceAppend})",
            insertStrategy = FieldStrategy.NOT_NULL,
            updateStrategy = FieldStrategy.NOT_NULL,
            select = false)
    private AfterSalesRegisterTrace traceAppend;

    /** 数据被过滤的原因 1:【工厂/仓库承担运费】、【工厂/仓库承担货款】、【工厂/仓库承担其他补偿】都为空 2：旺店通售后表中匹配到状态为【已审核】的【退款】单 */
    private Integer filterReason;

    /**
     * 班牛ID（调试用途）
     */
    @TableField(exist = false)
    private Long banniuId;

    /** 相关图片 */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> images;
}
