package com.daddylab.supplier.item.application.purchase.order;

import com.actionsoft.sdk.service.model.TaskInstance;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.aws.AwsOpenApiClient;
import com.daddylab.supplier.item.application.aws.service.AwsProcessClient;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PurchaseOrderState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAwsBusinessLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseOrderService;
import com.daddylab.supplier.item.infrastructure.utils.ObjectUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2024/1/4
 */
@Service
@Slf4j
@AllArgsConstructor
public class PurchaseOrderTimeoutTask {
    private final IPurchaseOrderService purchaseOrderService;
    private final IAwsBusinessLogService awsBusinessLogService;
    private final AwsOpenApiClient awsOpenApiClient;
    private final PurchaseOrderBizService purchaseOrderBizService;
    private final StaffService staffService;
    private final PurchaseOrderConfig purchaseOrderConfig;

    @XxlJob("PurchaseOrderTimeoutTask")
    public void doTask() {
        final List<PurchaseOrder> list =
                purchaseOrderService
                        .lambdaQuery()
                        .eq(PurchaseOrder::getState, PurchaseOrderState.WAIT_CHECK.getValue())
                        .list();
        final AwsProcessClient awsProcessClient = awsOpenApiClient.getAwsProcessClient();
        for (PurchaseOrder purchaseOrder : list) {
            final String processInstId =
                    awsBusinessLogService.getProcessIdByBusinessId(
                            PurchaseTypeEnum.PURCHASE_ORDER, purchaseOrder.getId());
            if (ObjectUtil.isEmpty(processInstId)) {
                continue;
            }
            final List<TaskInstance> taskInstances = awsProcessClient.taskQuery(processInstId);
            for (TaskInstance taskInstance : taskInstances) {
                final LocalDateTime now = LocalDateTime.now();
                final String taskId = taskInstance.getId();
                final String title = taskInstance.getTitle();
                final String processor = taskInstance.getTarget();
                if (StringUtil.isEmpty(processor)) {
                    log.warn("【采购单流程任务超时处理】任务当前无办理人，跳过处理:{} {}", taskId, title);
                    continue;
                }
                final boolean isNodeOfAutoAgree =
                        Arrays.stream(purchaseOrderConfig.getNodeOfAutoAgreeAfterTimeout())
                                .anyMatch(title::contains);
                final LocalDateTime beginTime = taskInstance.getBeginTime().toLocalDateTime();
                final LocalDateTime timeoutTime =
                        beginTime.plusSeconds(purchaseOrderConfig.getAuditTimeout());
                final boolean isTimeout = now.isAfter(timeoutTime);
                if (isNodeOfAutoAgree) {
                    if (isTimeout) {
                        awsProcessClient.taskCommentCommit(
                                taskId,
                                processor,
                                "提交",
                                String.format(
                                        "超过%s小时未处理，系统自动审核通过",
                                        TimeUnit.HOURS.convert(
                                                purchaseOrderConfig.getAuditTimeout(),
                                                TimeUnit.SECONDS)),
                                false);
                        awsProcessClient.taskComplete(taskId, processor);
                        purchaseOrderBizService.taskTimeoutAutoAgreeReminder(
                                purchaseOrder, taskInstance);
                        log.info(
                                "【采购单流程任务超时处理】超时自动办理，知会消息发送成功:{} 采购单:{}",
                                processor,
                                purchaseOrder.getId());
                    } else {
                        log.debug(
                                "【采购单流程任务超时处理】未到超时时间 开始时间:{} 超时时间:{} 采购单:{} {} {} {}",
                                beginTime,
                                timeoutTime,
                                purchaseOrder.getId(),
                                title,
                                processor,
                                processInstId);
                    }
                }
            }
        }
    }
}
