package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataPushLog;

/**
 * <p>
 * 数据推送记录详细日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
public interface IDataPushLogService extends IDaddyService<DataPushLog> {

}
