package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.daddylab.supplier.item.application.bizLevelDivision.BizLevelDivisionConvert;
import com.daddylab.supplier.item.application.itemTag.ItemTagEnum;
import com.daddylab.supplier.item.controller.item.dto.CorpBizListDto;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CascadedDivisionLevel;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BizUnionTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.BizLevelDivisionMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 业务层级关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-06
 */
@Service
public class BizLevelDivisionServiceImpl
    extends DaddyServiceImpl<BizLevelDivisionMapper, BizLevelDivision>
    implements IBizLevelDivisionService {

  @Autowired IItemService iItemService;
  @Autowired ICombinationItemService itemCombinationService;
  @Autowired IProviderService iProviderService;
  @Autowired IShopService iShopService;
  @Autowired IBrandService iBrandService;
  @Autowired IEntryActivityPriceItemService iEntryActivityPriceItemService;
  @Autowired IItemTagService iItemTagService;
  @Autowired IPurchaseOrderService iPurchaseOrderService;
  @Autowired IStockInOrderService iStockInOrderService;
  @Autowired IStockOutOrderService iStockOutOrderService;
  @Autowired IPaymentApplyOrderService iPaymentApplyOrderService;
  @Autowired IPurchasePayableOrderService iPurchasePayableOrderService;
  @Autowired IWarehouseService iWarehouseService;
  @Autowired IOrderSettlementFormService iOrderSettlementFormService;
  @Autowired IAfterSalesRegisterService iAfterSalesRegisterService;
  @Autowired IItemSkuService iItemSkuService;
//  @Autowired IVirtualWarehouseService iVirtualWarehouseService;

  @Override
  public boolean containValue(BizUnionTypeEnum type, Long bizId, DivisionLevelValueEnum value) {
    return lambdaQuery()
            .eq(BizLevelDivision::getType, type)
            .eq(BizLevelDivision::getBizId, type)
            .eq(BizLevelDivision::getLevelVal, value)
            .count()
        > 0;
  }

  @Override
  public boolean saveCascadedLevels(
      BizUnionTypeEnum type,
      Long bizId,
      List<CascadedDivisionLevel> values,
      List<DivisionLevelEnum> divisionLevels) {
    return saveCascadedLevels(type, bizId, null, values, divisionLevels, 0L);
  }

  @Override
  public boolean saveCascadedLevels(
      BizUnionTypeEnum type,
      Long bizId,
      String bizCode,
      List<CascadedDivisionLevel> values,
      List<DivisionLevelEnum> divisionLevels) {
    return saveCascadedLevels(type, bizId, bizCode, values, divisionLevels, 0L);
  }

  @Override
  public boolean savePlainLevels(
      BizUnionTypeEnum type,
      Long bizId,
      List<DivisionLevelValueEnum> values,
      List<DivisionLevelEnum> divisionLevels) {
    return saveCascadedLevels(
        type,
        bizId,
        null,
        values.stream().map(CascadedDivisionLevel::new).collect(Collectors.toList()),
        divisionLevels,
        0L);
  }

  @Override
  public boolean savePlainLevels(
      BizUnionTypeEnum type,
      Long bizId,
      String bizCode,
      List<DivisionLevelValueEnum> values,
      List<DivisionLevelEnum> divisionLevels) {
    return saveCascadedLevels(
        type,
        bizId,
        bizCode,
        values.stream().map(CascadedDivisionLevel::new).collect(Collectors.toList()),
        divisionLevels,
        0L);
  }

  private boolean saveCascadedLevels(
      BizUnionTypeEnum type,
      Long bizId,
      String bizCode,
      List<CascadedDivisionLevel> values,
      List<DivisionLevelEnum> divisionLevels,
      Long parentId) {
    final List<CascadedDivisionLevel> cascadedLevels = getCascadedLevels(type, bizId);
    final boolean result =
        saveCascadedLevels0(type, bizId, bizCode, values, divisionLevels, parentId);
    if (result) {
      final String oldStr =
          BizLevelDivisionConvert.INSTANCE.cascadedDivisionLevelsToDescStr(cascadedLevels);
      final List<CascadedDivisionLevel> cascadedLevels1 = getCascadedLevels(type, bizId);
      final String newStr =
          BizLevelDivisionConvert.INSTANCE.cascadedDivisionLevelsToDescStr(cascadedLevels1);
      final OperateLogTarget logTarget = convertToOperateLogTarget(type);
      if (logTarget != null) {
        SpringUtil.getBean(OperateLogDomainService.class)
            .addOperatorLog(
                UserContext.getUserId(),
                logTarget,
                bizId,
                String.format("修改合作方权限，从 '%s' 修改为 '%s'", oldStr, newStr));
      }
    }
    return result;
  }

  /**
   * 将BizUnionTypeEnum转换为对应的OperateLogTarget
   *
   * @param type BizUnionTypeEnum类型
   * @return 对应的OperateLogTarget类型
   */
  private static OperateLogTarget convertToOperateLogTarget(BizUnionTypeEnum type) {
    switch (type) {
      case SPU:
        return OperateLogTarget.ITEM;
      case COMBINATION:
        return OperateLogTarget.COMBINATION_ITEM;
      case BRAND:
        return OperateLogTarget.BRAND;
      case PROVIDER:
        return OperateLogTarget.PROVIDER;
      case SHOP:
        return OperateLogTarget.SHOP;
      case PURCHASE_ORDER:
        return OperateLogTarget.PURCHASE_ORDER;
      case PAYMENT_ORDER:
        return OperateLogTarget.PAYMENT_APPLY_ORDER;
      case STOCK_IN_ORDER:
        return OperateLogTarget.STOCK_ORDER;
      case STOCK_OUT_ORDER:
        return OperateLogTarget.STOCK_OUT_ORDER;
      case STOCK_PAYMENT_ORDER:
        return OperateLogTarget.OTHER_PAY;
      case WAREHOUSE:
        return OperateLogTarget.WAREHOUSE;
      case SETTLEMENT_ORDER:
        return OperateLogTarget.ORDER_SETTLEMENT;
      case VIRTUAL_WAREHOUSE:
        return OperateLogTarget.VIRTUAL_WAREHOUSE;
      case PRODUCT_OPTIMIZE_PLAN:
        return OperateLogTarget.ITEM_OPTIMIZE_PLAN;
      case NEW_GOODS:
        return OperateLogTarget.NEW_GOODS_SPU;
      case PRODUCT_OPTIMIZE:
        return OperateLogTarget.ITEM_OPTIMIZE;
      default:
        return null;
    }
  }

  private boolean saveCascadedLevels0(
      BizUnionTypeEnum type,
      Long bizId,
      String bizCode,
      List<CascadedDivisionLevel> values,
      List<DivisionLevelEnum> divisionLevels,
      Long parentId) {
    if (values == null) {
      return false;
    }
    final List<BizLevelDivision> bizLevelDivisions =
        selectByTypeAndBizId(type, bizId, parentId, divisionLevels);
    final Map<DivisionLevelValueEnum, BizLevelDivision> bizLevelDivisionMap =
        bizLevelDivisions.stream()
            .collect(Collectors.toMap(BizLevelDivision::getLevelVal, Function.identity()));
    boolean hasChanged = false;
    for (CascadedDivisionLevel value : values) {
      if (bizLevelDivisionMap.containsKey(value.getValue())) {
        final BizLevelDivision bizLevelDivision = bizLevelDivisionMap.get(value.getValue());
        if (value.getSubValues() != null) {
          hasChanged =
              saveCascadedLevels(
                      type,
                      bizId,
                      bizCode,
                      value.getSubValues(),
                      divisionLevels,
                      bizLevelDivision.getId())
                  || hasChanged;
        }
      } else {
        final BizLevelDivision bizLevelDivision = new BizLevelDivision();
        bizLevelDivision.setBizId(bizId);
        bizLevelDivision.setBizCode(bizCode);
        bizLevelDivision.setType(type);
        bizLevelDivision.setLevelVal(value.getValue());
        bizLevelDivision.setLevel(value.getValue().getDivisionLevel());
        bizLevelDivision.setParentId(parentId);
        save(bizLevelDivision);
        if (CollUtil.isNotEmpty(value.getSubValues())) {
          final List<BizLevelDivision> subBizLevelDivisions =
              value.getSubValues().stream()
                  .map(
                      v -> {
                        final BizLevelDivision subBizLevelDivision = new BizLevelDivision();
                        subBizLevelDivision.setBizId(bizId);
                        subBizLevelDivision.setType(type);
                        subBizLevelDivision.setLevelVal(v.getValue());
                        subBizLevelDivision.setLevel(v.getValue().getDivisionLevel());
                        subBizLevelDivision.setParentId(bizLevelDivision.getId());
                        return subBizLevelDivision;
                      })
                  .collect(Collectors.toList());
          saveBatch(subBizLevelDivisions);
        }
        hasChanged = true;
      }
    }
    if (!bizLevelDivisions.isEmpty()) {
      final Set<DivisionLevelValueEnum> enumValues =
          values.stream().map(CascadedDivisionLevel::getValue).collect(Collectors.toSet());
      final List<BizLevelDivision> toRemoveValues =
          bizLevelDivisions.stream()
              .filter(v -> !enumValues.contains(v.getLevelVal()))
              .collect(Collectors.toList());
      if (!toRemoveValues.isEmpty()) {
        final List<Long> toRemoveIds =
            toRemoveValues.stream().map(BizLevelDivision::getId).collect(Collectors.toList());
        removeByIds(toRemoveIds);
        lambdaUpdate().in(BizLevelDivision::getParentId, toRemoveIds).remove();
        hasChanged = true;
      }
    }
    if (bizCode != null) {
      updateBizCode(type, bizId, bizCode);
    }
    return hasChanged;
  }

  @Override
  public boolean updateBizCode(BizUnionTypeEnum type, Long bizId, String bizCode) {
    return lambdaUpdate()
        .eq(BizLevelDivision::getType, type)
        .eq(BizLevelDivision::getBizId, bizId)
        .set(BizLevelDivision::getBizCode, bizCode)
        .update();
  }

  @Override
  public List<CascadedDivisionLevel> getCascadedLevels(BizUnionTypeEnum type, Long bizId) {
    final List<BizLevelDivision> bizLevelDivisions = selectByTypeAndBizId(type, bizId);
    return getCascadedLevels0(bizLevelDivisions, 0L);
  }

  @Override
  public Map<Long, List<CascadedDivisionLevel>> getCascadedLevels(
      BizUnionTypeEnum type, List<Long> bizIds) {
    final List<BizLevelDivision> bizLevelDivisions = selectByTypeAndBizIds(type, bizIds);
    final HashMap<Long, List<CascadedDivisionLevel>> result = new HashMap<>();
    bizLevelDivisions.stream()
        .collect(Collectors.groupingBy(BizLevelDivision::getBizId))
        .forEach(
            (bizId, bizLevelDivisionList) -> {
              result.put(bizId, getCascadedLevels0(bizLevelDivisionList, 0L));
            });
    return result;
  }

  @Override
  public List<CorpBizTypeDTO> getCorpBizType(BizUnionTypeEnum type, Long bizId) {
    final List<CascadedDivisionLevel> cascadedLevels = getCascadedLevels(type, bizId);
    return BizLevelDivisionConvert.INSTANCE.cascadedDivisionLevelToCorpBizType(cascadedLevels);
  }

  @Override
  public Map<Long, List<CorpBizTypeDTO>> getCorpBizType(BizUnionTypeEnum type, List<Long> bizIds) {
    final Map<Long, List<CascadedDivisionLevel>> cascadedLevels = getCascadedLevels(type, bizIds);
    HashMap<Long, List<CorpBizTypeDTO>> result = new HashMap<>();
    cascadedLevels.forEach(
        (bizId, cascadedLevels1) -> {
          result.put(
              bizId,
              BizLevelDivisionConvert.INSTANCE.cascadedDivisionLevelToCorpBizType(cascadedLevels1));
        });
    return result;
  }

  @Override
  public Map<Long, Set<DivisionLevelValueEnum>> getCorpType(
      BizUnionTypeEnum type, List<Long> bizIds) {
    final List<BizLevelDivision> bizLevelDivisions =
        lambdaQuery()
            .eq(BizLevelDivision::getType, type)
            .in(BizLevelDivision::getBizId, bizIds)
            .eq(BizLevelDivision::getParentId, 0L)
            .eq(BizLevelDivision::getLevel, DivisionLevelEnum.COOPERATION)
            .list();
    if (bizLevelDivisions.isEmpty()) {
      return Collections.emptyMap();
    }
    return bizLevelDivisions.stream()
        .collect(
            Collectors.groupingBy(
                BizLevelDivision::getBizId,
                Collectors.mapping(BizLevelDivision::getLevelVal, Collectors.toSet())));
  }

  @NotNull
  private List<CascadedDivisionLevel> getCascadedLevels0(
      List<BizLevelDivision> bizLevelDivisions, Long parentId) {
    return bizLevelDivisions.stream()
        .filter(bizLevelDivision -> Objects.equals(bizLevelDivision.getParentId(), parentId))
        .map(
            bizLevelDivision ->
                new CascadedDivisionLevel(
                    bizLevelDivision.getLevelVal(),
                    getCascadedLevels0(bizLevelDivisions, bizLevelDivision.getId())))
        .collect(Collectors.toList());
  }

  @Override
  public List<BizLevelDivision> selectByTypeAndBizId(BizUnionTypeEnum type, Long bizId) {
    return selectByTypeAndBizId(type, bizId, null);
  }

  @Override
  public List<BizLevelDivision> selectByTypeAndBizId(
      BizUnionTypeEnum type, Long bizId, Long parentId) {
    return selectByTypeAndBizId(type, bizId, parentId, null);
  }

  @Override
  public List<BizLevelDivision> selectByTypeAndBizId(
      BizUnionTypeEnum type, Long bizId, Long parentId, List<DivisionLevelEnum> divisionLevels) {
    return lambdaQuery()
        .eq(BizLevelDivision::getType, type)
        .eq(BizLevelDivision::getBizId, bizId)
        .eq(parentId != null, BizLevelDivision::getParentId, parentId)
        .in(divisionLevels != null, BizLevelDivision::getLevel, divisionLevels)
        .list();
  }

  @Override
  public List<BizLevelDivision> selectByTypeAndBizIds(BizUnionTypeEnum type, List<Long> bizIds) {
    if (CollUtil.isEmpty(bizIds)) {
      return Collections.emptyList();
    }
    return lambdaQuery()
        .eq(BizLevelDivision::getType, type)
        .in(BizLevelDivision::getBizId, bizIds)
        .list();
  }

  @Override
  public Map<Long, List<BizLevelDivision>> mapByBizIds(
      BizUnionTypeEnum bizUnionTypeEnum, Collection<Long> bizIds) {
    return this.lambdaQuery()
        .in(BizLevelDivision::getBizId, bizIds)
        .eq(BizLevelDivision::getType, bizUnionTypeEnum)
        .list()
        .stream()
        .collect(
            Collectors.groupingBy(
                BizLevelDivision::getBizId,
                Collectors.collectingAndThen(
                    Collectors.toList(),
                    list -> list.stream().distinct().collect(Collectors.toList()))));
  }

  @Override
  public Map<Long, List<CorpBizTypeDTO>> queryByItemId(Collection<Long> itemIds) {
    if (CollUtil.isEmpty(itemIds)) {
      return Collections.emptyMap();
    }
    itemIds = itemIds.stream().distinct().collect(Collectors.toList());
    final List<BizLevelDivision> list =
        this.lambdaQuery()
            .eq(BizLevelDivision::getType, BizUnionTypeEnum.SPU)
            .in(BizLevelDivision::getBizId, itemIds)
            .list();
    if (CollUtil.isEmpty(list)) {
      return Collections.emptyMap();
    }

    Map<Long, List<CorpBizTypeDTO>> resMap = new HashMap<>(16);
    final Map<Long, List<BizLevelDivision>> itemIdGroupByMap =
        list.stream().collect(Collectors.groupingBy(BizLevelDivision::getBizId));
    itemIdGroupByMap.forEach(
        (itemId, subList) -> {
          // 此SPU合作模式
          final List<BizLevelDivision> cooperationList =
              subList.stream()
                  .filter(val -> val.getLevel().equals(DivisionLevelEnum.COOPERATION))
                  .collect(Collectors.toList());
          // 此SPU业务类型
          final List<BizLevelDivision> businessList =
              subList.stream()
                  .filter(val -> val.getLevel().equals(DivisionLevelEnum.BUSINESS_TYPE))
                  .collect(Collectors.toList());

          final List<CorpBizTypeDTO> oneList =
              cooperationList.stream()
                  .map(
                      cd -> {
                        final List<BizLevelDivision> thisCdSubBusiness =
                            businessList.stream()
                                .filter(val -> val.getParentId().equals(cd.getId()))
                                .collect(Collectors.toList());
                        CorpBizTypeDTO vo = new CorpBizTypeDTO();
                        vo.setCorpType(cd.getLevelVal().getValue());
                        vo.setBizType(
                            thisCdSubBusiness.stream()
                                .map(val -> val.getLevelVal().getValue())
                                .distinct()
                                .collect(Collectors.toList()));
                        return vo;
                      })
                  .collect(Collectors.toList());

          resMap.put(itemId, oneList);
        });

    return resMap;
  }

  @Override
  public Map<String, List<CorpBizTypeDTO>> queryBySkuCode(Collection<String> skuCodes) {
    if (CollUtil.isEmpty(skuCodes)) {
      return Collections.emptyMap();
    }
    skuCodes = skuCodes.stream().distinct().collect(Collectors.toList());
    final List<ItemSku> itemSkuList = iItemSkuService.selectByMixCodes(skuCodes);
    if (CollUtil.isEmpty(itemSkuList)) {
      return Collections.emptyMap();
    }
    Map<String, List<CorpBizTypeDTO>> resMap = new HashMap<>(16);

    final List<Long> itemIdList =
        itemSkuList.stream().map(ItemSku::getItemId).distinct().collect(Collectors.toList());
    final Map<Long, List<CorpBizTypeDTO>> itemMap = queryByItemId(itemIdList);
    Map<String, Long> skuCodeAndItemIdMap = new HashMap<>(itemSkuList.size() * 2);
    for (ItemSku itemSku : itemSkuList) {
      skuCodeAndItemIdMap.put(itemSku.getSkuCode(), itemSku.getItemId());
      if (StringUtils.hasText(itemSku.getProviderSpecifiedCode())) {
        skuCodeAndItemIdMap.put(itemSku.getProviderSpecifiedCode(), itemSku.getItemId());
      }
    }

    skuCodes.forEach(
        skuCode -> {
          final Long itemId = skuCodeAndItemIdMap.get(skuCode);
          if (Objects.isNull(itemId)) {
            resMap.put(skuCode, new LinkedList<>());
          } else {
            resMap.put(skuCode, itemMap.get(itemId));
          }
        });

    return resMap;
  }

  @Override
  public Map<String, CorpBizListDto> queryByCombinationCode(Collection<String> codes) {

    // 组合装处理
    final Set<String> cCodeSet =
        codes.stream().filter(v -> v.startsWith("MU")).collect(Collectors.toSet());
    if (CollUtil.isEmpty(cCodeSet)) {
      return Collections.emptyMap();
    }
    final Map<String, Long> cCodeAndIdMap =
        itemCombinationService.lambdaQuery().in(CombinationItem::getCode, cCodeSet).list().stream()
            .collect(Collectors.toMap(CombinationItem::getCode, CombinationItem::getId));
    if (CollUtil.isEmpty(cCodeAndIdMap)) {
      return Collections.emptyMap();
    }

    Map<Long, CorpBizListDto> map = new HashMap<>(codes.size());
    this.lambdaQuery()
        .in(BizLevelDivision::getBizId, cCodeAndIdMap.values())
        .eq(BizLevelDivision::getType, BizUnionTypeEnum.COMBINATION)
        .list()
        .stream()
        .collect(Collectors.groupingBy(BizLevelDivision::getBizId))
        .forEach(
            (id, list) -> {
              final List<Integer> cList =
                  list.stream()
                      .filter(val -> val.getLevel().equals(DivisionLevelEnum.COOPERATION))
                      .map(val -> val.getLevelVal().getValue())
                      .distinct()
                      .collect(Collectors.toList());
              final List<Integer> bList =
                  list.stream()
                      .filter(val -> val.getLevel().equals(DivisionLevelEnum.BUSINESS_TYPE))
                      .map(val -> val.getLevelVal().getValue())
                      .distinct()
                      .collect(Collectors.toList());
              CorpBizListDto dto = new CorpBizListDto();
              dto.setCorpType(cList);
              dto.setBizType(bList);
              map.put(id, dto);
            });

    Map<String, CorpBizListDto> resMap = new HashMap<>(codes.size());
    cCodeAndIdMap.forEach((code, id) -> resMap.put(code, map.get(id)));
    return resMap;
  }

  @Override
  public Integer getCoryType(Long bizId, String bizCode, BizUnionTypeEnum bizUnionTypeEnum) {
    final LambdaQueryChainWrapper<BizLevelDivision> wrapper = this.lambdaQuery();
    if (Objects.nonNull(bizId) && bizId > 0L) {
      wrapper.eq(BizLevelDivision::getBizId, bizId);
    }
    if (StringUtils.hasText(bizCode)) {
      wrapper.eq(BizLevelDivision::getBizCode, bizCode);
    }
    if (Objects.nonNull(bizUnionTypeEnum)) {
      wrapper.eq(BizLevelDivision::getType, bizUnionTypeEnum);
    }
    wrapper.eq(BizLevelDivision::getLevel, DivisionLevelEnum.COOPERATION);
    final List<BizLevelDivision> list = wrapper.list();

    // 默认合作方 = 电商
    if (CollUtil.isEmpty(list)) {
      return 0;
    }
    return list.get(0).getLevelVal().getValue();
  }

  @Override
  public Map<String, String> queryBizTypeBySkuCode(Collection<String> skuCodes) {
    if (CollUtil.isEmpty(skuCodes)) {
      return Collections.emptyMap();
    }
    skuCodes = skuCodes.stream().distinct().collect(Collectors.toList());
    return convertToStr(skuCodes.size(), queryBySkuCode(skuCodes));
  }

  @Override
  public Map<Long, String> queryBizTypeByItemId(Collection<Long> itemIds) {
    if (CollUtil.isEmpty(itemIds)) {
      return Collections.emptyMap();
    }
    itemIds = itemIds.stream().distinct().collect(Collectors.toList());
    return convertToStr(itemIds.size(), queryByItemId(itemIds));
  }

  public <K> Map<K, String> convertToStr(Integer size, Map<K, List<CorpBizTypeDTO>> stringListMap) {
    Map<K, String> resMap = new HashMap<>(size);
    stringListMap.forEach(
        (skuCode, list) -> {
          final String bizVal =
              list.stream()
                  .map(
                      val -> {
                        String cory =
                            IEnum.getEnumByValue(DivisionLevelValueEnum.class, val.getCorpType())
                                .getDesc();
                        return val.getBizType().stream()
                            .map(
                                bizType -> {
                                  String subBiz =
                                      IEnum.getEnumByValue(DivisionLevelValueEnum.class, bizType)
                                          .getDesc();
                                  return cory + "-" + subBiz;
                                })
                            .collect(Collectors.toList());
                      })
                  .collect(Collectors.toList())
                  .stream()
                  .flatMap(List::stream)
                  .collect(Collectors.joining("，"));

          resMap.put(skuCode, bizVal);
        });
    return resMap;
  }

  // ------------------------------------------------------------------------------------

  @Override
  public void historyDataHandler() {
    // 商品逻辑处理
    //    if (BizUnionTypeEnum.SPU.equals(bizUnionTypeEnum)) {
    ItemHandler2();
    //    }
    // 组合装处理
    //    if (BizUnionTypeEnum.COMBINATION.equals(bizUnionTypeEnum)) {
    //      combinationHandler();
    ////    }
    ////    if (BizUnionTypeEnum.SHOP.equals(bizUnionTypeEnum)) {
    //      shopHandler();
    ////    }
    ////    if (BizUnionTypeEnum.PROVIDER.equals(bizUnionTypeEnum)) {
    //      providerHandler();
    //    }
    //    if (BizUnionTypeEnum.BRAND.equals(bizUnionTypeEnum)) {
    //      //            brandHandler();
    //    }

    //    // 采购订单
    //    if (BizUnionTypeEnum.PURCHASE_ORDER.equals(bizUnionTypeEnum)) {
    ////      purchaseOrderHandler();
    //    }
    //    // 申请付款单
    //    if (BizUnionTypeEnum.PAYMENT_ORDER.equals(bizUnionTypeEnum)) {
    ////      paymentOrderHandler();
    //    }
    //    // 入库单
    //    if (BizUnionTypeEnum.STOCK_IN_ORDER.equals(bizUnionTypeEnum)) {
    ////      stockInOrderHandler();
    //    }
    //    // 出库单
    //    if (BizUnionTypeEnum.STOCK_OUT_ORDER.equals(bizUnionTypeEnum)) {
    ////      stockOutOrderHandler();
    //    }
    //    // 采购应付单
    //    if (BizUnionTypeEnum.STOCK_PAYMENT_ORDER.equals(bizUnionTypeEnum)) {
    ////      purchasePaymentHandler();
    //    }

    // 仓库列表
    //    if (BizUnionTypeEnum.WAREHOUSE.equals(bizUnionTypeEnum)) {
    //      warehouseHandler();
    //    }

    //    if (BizUnionTypeEnum.SETTLEMENT_ORDER.equals(bizUnionTypeEnum)) {
    ////      settlementHandler();
    //    }
    //
    //    if (BizUnionTypeEnum.AFTER_SALES_REGISTRATION.equals(bizUnionTypeEnum)) {
    ////      afterSalesHandler();
    //    }
    //
    //    if (BizUnionTypeEnum.VIRTUAL_WAREHOUSE.equals(bizUnionTypeEnum)) {}

    // 入驻活动价
    //        if (BizUnionTypeEnum.SETTLE_IN_ACTIVE_PRICE.equals(bizUnionTypeEnum)) {
    //
    //        }

  }

  private void ItemHandler2() {

    final List<Item> items = iItemService.lambdaQuery().list();
    for (Item item : items) {
      final List<ItemTag> itemTags = iItemTagService.listByItemId(item.getId());

      // 业务类型,老爸抽检 或者 订制品
      List<BizLevelDivision> bizTypeList =
          itemTags.stream()
              .map(
                  val -> {
                    BizLevelDivision bizLevelDivision = new BizLevelDivision();
                    bizLevelDivision.setBizId(item.getId());
                    bizLevelDivision.setBizCode(item.getCode());
                    bizLevelDivision.setType(BizUnionTypeEnum.SPU);
                    bizLevelDivision.setLevel(DivisionLevelEnum.BUSINESS_TYPE);
                    if (ItemTagEnum.CUSTOMIZED_PRODUCTS.name().equals(val.getTagId())) {
                      bizLevelDivision.setLevelVal(DivisionLevelValueEnum.B_CUSTOMIZED_PRODUCTS);
                    } else if (ItemTagEnum.DAD_SAMPLING_INSPECTION.name().equals(val.getTagId())) {
                      bizLevelDivision.setLevelVal(
                          DivisionLevelValueEnum.B_DAD_SAMPLING_INSPECTION);
                    } else {
                      return null;
                    }
                    return bizLevelDivision;
                  })
              .filter(Objects::nonNull)
              .distinct()
              .collect(Collectors.toList());

      // 构建新的合作方。历史映射值          0:电商。1:老爸抽检。2:绿色家装。3:商家入驻
      final String businessLines = item.oldBusinessLines();
      System.out.println(businessLines);
      if (StringUtils.hasText(businessLines)) {
        final List<Integer> blValList =
            Arrays.stream(businessLines.split(","))
                .map(Integer::valueOf)
                .collect(Collectors.toList());

        List<BizLevelDivision> cooperationIdList = new LinkedList<>();
        List<BizLevelDivision> bizList = new LinkedList<>();

        //                 两种合作方
        if (blValList.contains(0)) {
          BizLevelDivision bizLevelDivision = new BizLevelDivision();
          bizLevelDivision.setBizId(item.getId());
          bizLevelDivision.setBizCode(item.getCode());
          bizLevelDivision.setType(BizUnionTypeEnum.SPU);
          bizLevelDivision.setLevel(DivisionLevelEnum.COOPERATION);
          bizLevelDivision.setLevelVal(DivisionLevelValueEnum.C_E_COMMERCE);
          this.save(bizLevelDivision);
          cooperationIdList.add(bizLevelDivision);
        }
        if (blValList.contains(2)) {
          BizLevelDivision bizLevelDivision = new BizLevelDivision();
          bizLevelDivision.setBizId(item.getId());
          bizLevelDivision.setBizCode(item.getCode());
          bizLevelDivision.setType(BizUnionTypeEnum.SPU);
          bizLevelDivision.setLevel(DivisionLevelEnum.COOPERATION);
          bizLevelDivision.setLevelVal(DivisionLevelValueEnum.C_DECORATION);
          this.save(bizLevelDivision);
          cooperationIdList.add(bizLevelDivision);
        }

        // 如果合作方+历史商品标签存在。转为合作方下的业务类型
        if (CollUtil.isNotEmpty(cooperationIdList) && CollUtil.isNotEmpty(bizTypeList)) {
          for (BizLevelDivision cOne : cooperationIdList) {
            bizTypeList.forEach(val -> val.setParentId(cOne.getId()));
            bizList.addAll(bizTypeList);
          }
        }

        // 如果包含商家入驻
        if (blValList.contains(3)) {
          // 如果没有合作方。直接构造一个 电商+商家入驻
          if (CollUtil.isEmpty(cooperationIdList)) {
            BizLevelDivision bizLevelDivision = new BizLevelDivision();
            bizLevelDivision.setBizId(item.getId());
            bizLevelDivision.setBizCode(item.getCode());
            bizLevelDivision.setType(BizUnionTypeEnum.SPU);
            bizLevelDivision.setLevel(DivisionLevelEnum.COOPERATION);
            bizLevelDivision.setLevelVal(DivisionLevelValueEnum.C_E_COMMERCE);
            this.save(bizLevelDivision);

            BizLevelDivision bizLevelDivision2 = new BizLevelDivision();
            bizLevelDivision2.setBizId(item.getId());
            bizLevelDivision2.setBizCode(item.getCode());
            bizLevelDivision2.setType(BizUnionTypeEnum.SPU);
            bizLevelDivision2.setLevel(DivisionLevelEnum.BUSINESS_TYPE);
            bizLevelDivision2.setLevelVal(DivisionLevelValueEnum.B_MERCHANT_ENTER);
            bizLevelDivision2.setParentId(bizLevelDivision.getId());
            bizList.add(bizLevelDivision2);
          }
          // 如果存在合作方。每一个合作方写一个商家入驻的业务类型。
          else {
            bizList.addAll(
                cooperationIdList.stream()
                    .map(
                        cOne -> {
                          BizLevelDivision bizLevelDivision2 = new BizLevelDivision();
                          bizLevelDivision2.setBizId(item.getId());
                          bizLevelDivision2.setBizCode(item.getCode());
                          bizLevelDivision2.setType(BizUnionTypeEnum.SPU);
                          bizLevelDivision2.setLevel(DivisionLevelEnum.BUSINESS_TYPE);
                          bizLevelDivision2.setLevelVal(DivisionLevelValueEnum.B_MERCHANT_ENTER);
                          bizLevelDivision2.setParentId(cOne.getId());
                          return bizLevelDivision2;
                        })
                    .collect(Collectors.toList()));
          }
        }

        // 既没有原本的商品标签，也不满足商家入驻业务线的构建条件，手动给每一个合作方构建一个保底的合作商品业务类型
        if (CollUtil.isEmpty(bizList)) {
          for (BizLevelDivision cOne : cooperationIdList) {
            BizLevelDivision bizLevelDivision = new BizLevelDivision();
            bizLevelDivision.setBizId(item.getId());
            bizLevelDivision.setBizCode(item.getCode());
            bizLevelDivision.setType(BizUnionTypeEnum.SPU);
            bizLevelDivision.setLevel(DivisionLevelEnum.BUSINESS_TYPE);
            bizLevelDivision.setLevelVal(DivisionLevelValueEnum.B_PRODUCT_COOPERATION);
            bizLevelDivision.setParentId(cOne.getId());
            bizList.add(bizLevelDivision);
          }
        }

        bizList = bizList.stream().distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(bizList)) {
          this.saveBatch(bizList);
        }
      }
    }
  }

  //    private void itemSubHandler1(Item item, List<BizLevelDivision> bizTypeList, List<Integer>
  // blValList) {
  //        BizLevelDivision bizLevelDivision = new BizLevelDivision();
  //        bizLevelDivision.setBizId(item.getId());
  //        bizLevelDivision.setBizCode(item.getCode());
  //        bizLevelDivision.setType(BizUnionTypeEnum.SPU);
  //        bizLevelDivision.setLevel(DivisionLevelEnum.COOPERATION);
  //        bizLevelDivision.setLevelVal(DivisionLevelValueEnum.C_E_COMMERCE);
  //        this.save(bizLevelDivision);

  //        if (blValList.contains("3")) {
  //            BizLevelDivision bizLevelDivision2 = new BizLevelDivision();
  //            bizLevelDivision2.setBizId(item.getId());
  //            bizLevelDivision2.setBizCode(item.getCode());
  //            bizLevelDivision2.setType(BizUnionTypeEnum.SPU);
  //            bizLevelDivision2.setLevel(DivisionLevelEnum.BUSINESS_TYPE);
  //            bizLevelDivision2.setLevelVal(DivisionLevelValueEnum.B_MERCHANT_ENTER);
  //            bizTypeList.add(bizLevelDivision2);
  //        }

  //        if (CollUtil.isNotEmpty(bizTypeList)) {
  //            bizTypeList.forEach(val -> val.setParentId(bizLevelDivision.getId()));
  //            this.saveBatch(bizTypeList);
  //        }
  //    }

  //    private void itemSubHandler2(Item item, List<BizLevelDivision> bizTypeList, List<Integer>
  // blValList) {
  //        BizLevelDivision bizLevelDivision = new BizLevelDivision();
  //        bizLevelDivision.setBizId(item.getId());
  //        bizLevelDivision.setBizCode(item.getCode());
  //        bizLevelDivision.setType(BizUnionTypeEnum.SPU);
  //        bizLevelDivision.setLevel(DivisionLevelEnum.COOPERATION);
  //        bizLevelDivision.setLevelVal(DivisionLevelValueEnum.C_DECORATION);
  //        this.save(bizLevelDivision);
  //
  //        if (blValList.contains("3")) {
  //            BizLevelDivision bizLevelDivision2 = new BizLevelDivision();
  //            bizLevelDivision2.setBizId(item.getId());
  //            bizLevelDivision2.setBizCode(item.getCode());
  //            bizLevelDivision2.setType(BizUnionTypeEnum.SPU);
  //            bizLevelDivision2.setLevel(DivisionLevelEnum.BUSINESS_TYPE);
  //            bizLevelDivision2.setLevelVal(DivisionLevelValueEnum.B_MERCHANT_ENTER);
  //            bizTypeList.add(bizLevelDivision2);
  //        }
  //
  //        if (CollUtil.isNotEmpty(bizTypeList)) {
  //            bizTypeList.forEach(val -> val.setParentId(bizLevelDivision.getId()));
  //            this.saveBatch(bizTypeList);
  //        }
  //
  //        if (CollUtil.isNotEmpty(bizTypeList)) {
  //            bizTypeList.forEach(val -> val.setParentId(bizLevelDivision.getId()));
  //            this.saveBatch(bizTypeList);
  //        }
  //    }

  //    private void itemHandler() {
  //        List<BizLevelDivision> allSaveList = new LinkedList<>();
  //        final List<Item> items = iItemService.lambdaQuery().list();
  //        for (Item item : items) {
  //
  //            List<BizLevelDivision> bizLevelDivisions = new LinkedList<>();
  //            final List<ItemTag> itemTags = iItemTagService.listByItemId(item.getId());
  //
  //            // 原本的商品标签，订制品和老爸抽检 映射为 订制品业务类型和老爸抽检业务类型
  //            for (ItemTag itemTag : itemTags) {
  //                BizLevelDivision bizLevelDivision = new BizLevelDivision();
  //                bizLevelDivision.setBizId(item.getId());
  //                bizLevelDivision.setBizCode(item.getCode());
  //                bizLevelDivision.setType(BizUnionTypeEnum.SPU);
  //                bizLevelDivision.setLevel(DivisionLevelEnum.BUSINESS_TYPE);
  //                if (ItemTagEnum.CUSTOMIZED_PRODUCTS.name().equals(itemTag.getTagId())) {
  //                    bizLevelDivision.setLevelVal(DivisionLevelValueEnum.B_CUSTOMIZED_PRODUCTS);
  //                    bizLevelDivisions.add(bizLevelDivision);
  //                }
  //                if (ItemTagEnum.DAD_SAMPLING_INSPECTION.name().equals(itemTag.getTagId())) {
  //
  // bizLevelDivision.setLevelVal(DivisionLevelValueEnum.B_DAD_SAMPLING_INSPECTION);
  //                    bizLevelDivisions.add(bizLevelDivision);
  //                }
  //            }
  //            // 原本的商家入驻业务线，映射为 电商合作方&商家入驻业务类型
  //            if
  // (item.getBusinessLines().contains(String.valueOf(BusinessLine.SHANG_JIA_RU_ZHU.getValue()))) {
  //                BizLevelDivision bizLevelDivision = new BizLevelDivision();
  //                bizLevelDivision.setBizId(item.getId());
  //                bizLevelDivision.setBizCode(item.getCode());
  //                bizLevelDivision.setType(BizUnionTypeEnum.SPU);
  //                bizLevelDivision.setLevel(DivisionLevelEnum.BUSINESS_TYPE);
  //                bizLevelDivision.setLevelVal(DivisionLevelValueEnum.B_MERCHANT_ENTER);
  //                bizLevelDivisions.add(bizLevelDivision);
  //
  //                BizLevelDivision bizLevelDivision2 = new BizLevelDivision();
  //                bizLevelDivision2.setBizId(item.getId());
  //                bizLevelDivision2.setBizCode(item.getCode());
  //                bizLevelDivision2.setType(BizUnionTypeEnum.SPU);
  //                bizLevelDivision2.setLevel(DivisionLevelEnum.COOPERATION);
  //                bizLevelDivision2.setLevelVal(DivisionLevelValueEnum.C_E_COMMERCE);
  //                bizLevelDivisions.add(bizLevelDivision2);
  //            }
  //            // 原本的绿色家装业务线，映射为 绿色家装合作方
  //            if
  // (item.getBusinessLines().contains(String.valueOf(BusinessLine.LV_SE_JIA_ZHUANG.getValue()))) {
  //                BizLevelDivision bizLevelDivision = new BizLevelDivision();
  //                bizLevelDivision.setBizId(item.getId());
  //                bizLevelDivision.setBizCode(item.getCode());
  //                bizLevelDivision.setType(BizUnionTypeEnum.SPU);
  //                bizLevelDivision.setLevel(DivisionLevelEnum.COOPERATION);
  //                bizLevelDivision.setLevelVal(DivisionLevelValueEnum.C_DECORATION);
  //                bizLevelDivisions.add(bizLevelDivision);
  //            }
  //            // 原本的电商业务线，映射为 电商合作方
  //            if
  // (item.getBusinessLines().contains(String.valueOf(BusinessLine.DIAN_SHANG.getValue()))) {
  //                BizLevelDivision bizLevelDivision = new BizLevelDivision();
  //                bizLevelDivision.setBizId(item.getId());
  //                bizLevelDivision.setBizCode(item.getCode());
  //                bizLevelDivision.setType(BizUnionTypeEnum.SPU);
  //                bizLevelDivision.setLevel(DivisionLevelEnum.COOPERATION);
  //                bizLevelDivision.setLevelVal(DivisionLevelValueEnum.C_E_COMMERCE);
  //                bizLevelDivisions.add(bizLevelDivision);
  //            }
  //            // 原本的老爸抽检业务线，映射为 老爸抽检业务类型
  //            if
  // (item.getBusinessLines().contains(String.valueOf(BusinessLine.CHOU_JIAN.getValue()))) {
  //                BizLevelDivision bizLevelDivision = new BizLevelDivision();
  //                bizLevelDivision.setBizId(item.getId());
  //                bizLevelDivision.setBizCode(item.getCode());
  //                bizLevelDivision.setType(BizUnionTypeEnum.SPU);
  //                bizLevelDivision.setLevel(DivisionLevelEnum.BUSINESS_TYPE);
  //                bizLevelDivision.setLevelVal(DivisionLevelValueEnum.B_DAD_SAMPLING_INSPECTION);
  //                bizLevelDivisions.add(bizLevelDivision);
  //            }
  //
  //            // 如果跑下来，此商品没有 业务方数据，给其默认新增一个 商品合作业务方。
  //            final long count = bizLevelDivisions.stream()
  //                    .filter(bizLevelDivision ->
  //                            bizLevelDivision.getLevel().equals(DivisionLevelEnum.BUSINESS_TYPE))
  //                    .count();
  //            if (count == 0) {
  //                BizLevelDivision bizLevelDivision = new BizLevelDivision();
  //                bizLevelDivision.setBizId(item.getId());
  //                bizLevelDivision.setBizCode(item.getCode());
  //                bizLevelDivision.setType(BizUnionTypeEnum.SPU);
  //                bizLevelDivision.setLevel(DivisionLevelEnum.BUSINESS_TYPE);
  //                bizLevelDivision.setLevelVal(DivisionLevelValueEnum.B_PRODUCT_COOPERATION);
  //                bizLevelDivisions.add(bizLevelDivision);
  //            }
  //
  //
  // allSaveList.addAll(bizLevelDivisions.stream().distinct().collect(Collectors.toList()));
  //        }
  //
  //        if (CollUtil.isNotEmpty(allSaveList)) {
  //            this.saveBatch(allSaveList);
  //        }
  //    }

  private void combinationHandler() {
    final List<CombinationItem> combinationItems = itemCombinationService.lambdaQuery().list();

    List<BizLevelDivision> divisions = new LinkedList<>();
    for (CombinationItem item : combinationItems) {
      final Integer businessLine = item.getBusinessLine();

      // 业务线商家入驻 改成 合作方电商+业务类型商家入驻
      if (3 == businessLine) {
        BizLevelDivision bizLevelDivision = new BizLevelDivision();
        bizLevelDivision.setBizId(item.getId());
        bizLevelDivision.setBizCode(item.getCode());
        bizLevelDivision.setType(BizUnionTypeEnum.COMBINATION);
        bizLevelDivision.setLevel(DivisionLevelEnum.COOPERATION);
        bizLevelDivision.setLevelVal(DivisionLevelValueEnum.C_E_COMMERCE);
        divisions.add(bizLevelDivision);

        BizLevelDivision bizLevelDivision2 = new BizLevelDivision();
        bizLevelDivision2.setBizId(item.getId());
        bizLevelDivision2.setBizCode(item.getCode());
        bizLevelDivision2.setType(BizUnionTypeEnum.COMBINATION);
        bizLevelDivision2.setLevel(DivisionLevelEnum.BUSINESS_TYPE);
        bizLevelDivision2.setLevelVal(DivisionLevelValueEnum.B_MERCHANT_ENTER);
        divisions.add(bizLevelDivision2);
      }
      // 电商
      if (0 == businessLine) {
        BizLevelDivision bizLevelDivision = new BizLevelDivision();
        bizLevelDivision.setBizId(item.getId());
        bizLevelDivision.setBizCode(item.getCode());
        bizLevelDivision.setType(BizUnionTypeEnum.COMBINATION);
        bizLevelDivision.setLevel(DivisionLevelEnum.COOPERATION);
        bizLevelDivision.setLevelVal(DivisionLevelValueEnum.C_E_COMMERCE);
        divisions.add(bizLevelDivision);

        BizLevelDivision bizLevelDivision2 = new BizLevelDivision();
        bizLevelDivision2.setBizId(item.getId());
        bizLevelDivision2.setBizCode(item.getCode());
        bizLevelDivision2.setType(BizUnionTypeEnum.COMBINATION);
        bizLevelDivision2.setLevel(DivisionLevelEnum.BUSINESS_TYPE);
        bizLevelDivision2.setLevelVal(DivisionLevelValueEnum.B_PRODUCT_COOPERATION);
        divisions.add(bizLevelDivision2);
      }
      // 绿色家装
      if (2 == businessLine) {
        BizLevelDivision bizLevelDivision = new BizLevelDivision();
        bizLevelDivision.setBizId(item.getId());
        bizLevelDivision.setBizCode(item.getCode());
        bizLevelDivision.setType(BizUnionTypeEnum.COMBINATION);
        bizLevelDivision.setLevel(DivisionLevelEnum.COOPERATION);
        bizLevelDivision.setLevelVal(DivisionLevelValueEnum.C_DECORATION);
        divisions.add(bizLevelDivision);

        BizLevelDivision bizLevelDivision2 = new BizLevelDivision();
        bizLevelDivision2.setBizId(item.getId());
        bizLevelDivision2.setBizCode(item.getCode());
        bizLevelDivision2.setType(BizUnionTypeEnum.COMBINATION);
        bizLevelDivision2.setLevel(DivisionLevelEnum.BUSINESS_TYPE);
        bizLevelDivision2.setLevelVal(DivisionLevelValueEnum.B_PRODUCT_COOPERATION);
        divisions.add(bizLevelDivision2);
      }
    }

    this.saveBatch(divisions);
  }

  /**
   * fixme 等待产品完善历史供应商数据清洗逻辑
   *
   * <p>合作模式。0:电商。1:老爸抽检。2:绿色家装。3:商家入驻
   */
  private void providerHandler() {
    final List<Provider> providers = iProviderService.lambdaQuery().list();
    List<BizLevelDivision> divisions = new LinkedList<>();
    for (Provider provider : providers) {
      // 合作模式。0:电商。1:老爸抽检。2:绿色家装。3:商家入驻
      final String businessLine = provider.getBusinessLine();
      Set<BizLevelDivision> thisSet = new HashSet<>();
      final Long providerId = provider.getId();

      // 供应商原本的合作方
      if (StringUtils.hasText(businessLine)) {
        for (String s : businessLine.split(",")) {
          final DivisionLevelValueEnum valueEnum =
              DivisionLevelValueEnum.valueOf(Integer.valueOf(s));
          if (Objects.nonNull(valueEnum)) {
            BizLevelDivision division = new BizLevelDivision();
            division.setBizId(provider.getId());
            division.setBizCode(provider.getUnifySocialCreditCodes());
            division.setType(BizUnionTypeEnum.PROVIDER);
            division.setLevel(DivisionLevelEnum.COOPERATION);
            division.setLevelVal(valueEnum);
            thisSet.add(division);
          }
        }
      }

      final List<Item> itemList =
          iItemService.lambdaQuery().eq(Item::getProviderId, providerId).list();
      Set<Long> thisProviderItemIdList =
          itemList.stream().map(Item::getId).collect(Collectors.toSet());

      if (CollUtil.isNotEmpty(thisProviderItemIdList)) {
        // 供应商关联到的item所拥有的合作方
        List<BizLevelDivision> thisProviderItemDivisionList =
            this.lambdaQuery()
                .eq(BizLevelDivision::getType, BizUnionTypeEnum.SPU)
                .in(BizLevelDivision::getBizId, thisProviderItemIdList)
                .eq(BizLevelDivision::getLevel, DivisionLevelEnum.COOPERATION)
                .list()
                .stream()
                .distinct()
                .collect(Collectors.toList());
        for (BizLevelDivision levelDivision : thisProviderItemDivisionList) {
          BizLevelDivision division = new BizLevelDivision();
          division.setBizId(provider.getId());
          division.setBizCode(provider.getUnifySocialCreditCodes());
          division.setType(BizUnionTypeEnum.PROVIDER);
          division.setLevel(DivisionLevelEnum.COOPERATION);
          division.setLevelVal(levelDivision.getLevelVal());
          thisSet.add(division);
        }

        // 供应商关联到的item所拥有的业务类型
        List<BizLevelDivision> thisProviderItemDivisionList2 =
            this.lambdaQuery()
                .eq(BizLevelDivision::getType, BizUnionTypeEnum.SPU)
                .in(BizLevelDivision::getBizId, thisProviderItemIdList)
                .eq(BizLevelDivision::getLevel, DivisionLevelEnum.BUSINESS_TYPE)
                .list()
                .stream()
                .distinct()
                .collect(Collectors.toList());
        for (BizLevelDivision levelDivision : thisProviderItemDivisionList2) {
          BizLevelDivision division = new BizLevelDivision();
          division.setBizId(provider.getId());
          division.setBizCode(provider.getUnifySocialCreditCodes());
          division.setType(BizUnionTypeEnum.PROVIDER);
          division.setLevel(DivisionLevelEnum.BUSINESS_TYPE);
          division.setLevelVal(levelDivision.getLevelVal());
          thisSet.add(division);
        }
      }

      divisions.addAll(thisSet);
    }

    if (CollUtil.isNotEmpty(divisions)) {
      this.saveBatch(divisions);
    }
  }

  private void shopHandler() {
    final List<Shop> shopList = iShopService.lambdaQuery().list();
    List<BizLevelDivision> divisions = new LinkedList<>();

    for (Shop shop : shopList) {
      Set<BizLevelDivision> oneList = new HashSet<>();
      final String businessLine = shop.getBusinessLine();
      if (businessLine.contains("0")) {
        BizLevelDivision bizLevelDivision = new BizLevelDivision();
        bizLevelDivision.setBizId(shop.getId());
        bizLevelDivision.setBizCode(shop.getSn());
        bizLevelDivision.setType(BizUnionTypeEnum.SHOP);
        bizLevelDivision.setLevel(DivisionLevelEnum.COOPERATION);
        bizLevelDivision.setLevelVal(DivisionLevelValueEnum.C_E_COMMERCE);
        oneList.add(bizLevelDivision);

        BizLevelDivision bizLevelDivision2 = new BizLevelDivision();
        bizLevelDivision2.setBizId(shop.getId());
        bizLevelDivision2.setBizCode(shop.getSn());
        bizLevelDivision2.setType(BizUnionTypeEnum.SHOP);
        bizLevelDivision2.setLevel(DivisionLevelEnum.RUNNING_MODEL);
        bizLevelDivision2.setLevelVal(DivisionLevelValueEnum.RUN_SELF);
        oneList.add(bizLevelDivision2);
      }
      if (businessLine.contains("2")) {
        BizLevelDivision bizLevelDivision = new BizLevelDivision();
        bizLevelDivision.setBizId(shop.getId());
        bizLevelDivision.setBizCode(shop.getSn());
        bizLevelDivision.setType(BizUnionTypeEnum.SHOP);
        bizLevelDivision.setLevel(DivisionLevelEnum.COOPERATION);
        bizLevelDivision.setLevelVal(DivisionLevelValueEnum.C_DECORATION);
        oneList.add(bizLevelDivision);

        BizLevelDivision bizLevelDivision2 = new BizLevelDivision();
        bizLevelDivision2.setBizId(shop.getId());
        bizLevelDivision2.setBizCode(shop.getSn());
        bizLevelDivision2.setType(BizUnionTypeEnum.SHOP);
        bizLevelDivision2.setLevel(DivisionLevelEnum.RUNNING_MODEL);
        bizLevelDivision2.setLevelVal(DivisionLevelValueEnum.RUN_SELF);
        oneList.add(bizLevelDivision2);
      }
      if (businessLine.contains("3")) {
        BizLevelDivision bizLevelDivision = new BizLevelDivision();
        bizLevelDivision.setBizId(shop.getId());
        bizLevelDivision.setBizCode(shop.getSn());
        bizLevelDivision.setType(BizUnionTypeEnum.SHOP);
        bizLevelDivision.setLevel(DivisionLevelEnum.COOPERATION);
        bizLevelDivision.setLevelVal(DivisionLevelValueEnum.C_E_COMMERCE);
        oneList.add(bizLevelDivision);

        BizLevelDivision bizLevelDivision2 = new BizLevelDivision();
        bizLevelDivision2.setBizId(shop.getId());
        bizLevelDivision2.setBizCode(shop.getSn());
        bizLevelDivision2.setType(BizUnionTypeEnum.SHOP);
        bizLevelDivision2.setLevel(DivisionLevelEnum.RUNNING_MODEL);
        bizLevelDivision2.setLevelVal(DivisionLevelValueEnum.RUN_PROXY);
        oneList.add(bizLevelDivision2);
      }

      divisions.addAll(oneList);
    }

    this.saveBatch(divisions);
  }

  //    /**
  //     * fixme 等待产品完善历史品牌数据清洗逻辑
  //     */
  //    private void brandHandler() {
  //        List<BizLevelDivision> divisions = new LinkedList<>();
  //        final List<Brand> list = iBrandService.lambdaQuery().list();
  //        for (Brand brand : list) {
  //
  //        }
  //    }

  //  private void purchaseOrderHandler() {
  //    final List<PurchaseOrder> orderList = iPurchaseOrderService.lambdaQuery().list();
  //    final List<Long> idList =
  //        orderList.stream()
  //            .filter(val -> val.getBusinessLine().equals(2))
  //            .map(PurchaseOrder::getId)
  //            .collect(Collectors.toList());
  //    iPurchaseOrderService
  //        .lambdaUpdate()
  //        .set(PurchaseOrder::getBusinessLine, 1)
  //        .in(PurchaseOrder::getId, idList)
  //        .update();
  //
  //    final List<Long> idList2 =
  //        orderList.stream()
  //            .filter(val -> val.getBusinessLine().equals(1) || val.getBusinessLine().equals(3))
  //            .map(PurchaseOrder::getId)
  //            .collect(Collectors.toList());
  //    iPurchaseOrderService
  //        .lambdaUpdate()
  //        .set(PurchaseOrder::getBusinessLine, 0)
  //        .in(PurchaseOrder::getId, idList2)
  //        .update();
  //  }

  private void stockInOrderHandler() {
    final List<StockInOrder> orderList = iStockInOrderService.lambdaQuery().list();
    final List<Long> idList =
        orderList.stream()
            .filter(val -> val.getBusinessLine().equals(2))
            .map(StockInOrder::getId)
            .collect(Collectors.toList());
    iStockInOrderService
        .lambdaUpdate()
        .set(StockInOrder::getBusinessLine, 1)
        .in(StockInOrder::getId, idList)
        .update();

    final List<Long> idList2 =
        orderList.stream()
            .filter(val -> val.getBusinessLine().equals(1) || val.getBusinessLine().equals(3))
            .map(StockInOrder::getId)
            .collect(Collectors.toList());
    iStockInOrderService
        .lambdaUpdate()
        .set(StockInOrder::getBusinessLine, 0)
        .in(StockInOrder::getId, idList2)
        .update();
  }

  private void stockOutOrderHandler() {
    final List<StockOutOrder> orderList = iStockOutOrderService.lambdaQuery().list();
    final List<Long> idList =
        orderList.stream()
            .filter(val -> val.getBusinessLine().equals(2))
            .map(StockOutOrder::getId)
            .collect(Collectors.toList());
    iStockOutOrderService
        .lambdaUpdate()
        .set(StockOutOrder::getBusinessLine, 1)
        .in(StockOutOrder::getId, idList)
        .update();

    final List<Long> idList2 =
        orderList.stream()
            .filter(val -> val.getBusinessLine().equals(1) || val.getBusinessLine().equals(3))
            .map(StockOutOrder::getId)
            .collect(Collectors.toList());
    iStockOutOrderService
        .lambdaUpdate()
        .set(StockOutOrder::getBusinessLine, 0)
        .in(StockOutOrder::getId, idList2)
        .update();
  }

  //  private void paymentOrderHandler() {
  //    final List<PaymentApplyOrder> orderList = iPaymentApplyOrderService.lambdaQuery().list();
  //    final List<Long> idList =
  //        orderList.stream()
  //            .filter(val -> val.getBusinessLine().equals(2))
  //            .map(PaymentApplyOrder::getId)
  //            .collect(Collectors.toList());
  //    iPaymentApplyOrderService
  //        .lambdaUpdate()
  //        .set(PaymentApplyOrder::getBusinessLine, 1)
  //        .in(PaymentApplyOrder::getId, idList)
  //        .update();
  //
  //    final List<Long> idList2 =
  //        orderList.stream()
  //            .filter(val -> val.getBusinessLine().equals(1) || val.getBusinessLine().equals(3))
  //            .map(PaymentApplyOrder::getId)
  //            .collect(Collectors.toList());
  //    iPaymentApplyOrderService
  //        .lambdaUpdate()
  //        .set(PaymentApplyOrder::getBusinessLine, 0)
  //        .in(PaymentApplyOrder::getId, idList2)
  //        .update();
  //  }

  private void purchasePaymentHandler() {
    final List<PurchasePayableOrder> orderList = iPurchasePayableOrderService.lambdaQuery().list();
    final List<Long> idList =
        orderList.stream()
            .filter(val -> val.getBusinessLine().equals(2))
            .map(PurchasePayableOrder::getId)
            .collect(Collectors.toList());
    iPurchasePayableOrderService
        .lambdaUpdate()
        .set(PurchasePayableOrder::getBusinessLine, 1)
        .in(PurchasePayableOrder::getId, idList)
        .update();

    final List<Long> idList2 =
        orderList.stream()
            .filter(val -> val.getBusinessLine().equals(1) || val.getBusinessLine().equals(3))
            .map(PurchasePayableOrder::getId)
            .collect(Collectors.toList());
    iPurchasePayableOrderService
        .lambdaUpdate()
        .set(PurchasePayableOrder::getBusinessLine, 0)
        .in(PurchasePayableOrder::getId, idList2)
        .update();
  }

  private void warehouseHandler() {
    final List<Warehouse> warehouseList = iWarehouseService.lambdaQuery().list();
    for (Warehouse warehouse : warehouseList) {

      final String businessLine = warehouse.getBusinessLine();
      // 商家入驻的全部洗成电商
      final boolean contains = businessLine.contains("3");
      if (contains) {
        final String s = businessLine.replaceAll("3", "0");
        final String collect =
            Arrays.stream(s.split(","))
                .map(String::trim)
                .distinct()
                .collect(Collectors.joining(","));
        warehouse.setBusinessLine(collect);
        iWarehouseService.updateById(warehouse);
      }
    }
  }

  //  private void settlementHandler() {
  //    // 更改原本绿色家装的映射值。2->1
  //    iOrderSettlementFormService
  //        .lambdaUpdate()
  //        .set(OrderSettlementForm::getBusinessLine, 2)
  //        .eq(OrderSettlementForm::getBusinessLine, 1)
  //        .update();
  //    //        // 原本商家入驻合作模式划到电商。
  //    //        iOrderSettlementFormService.lambdaUpdate()
  //    //                .set(OrderSettlementForm::getBusinessLine, 0)
  //    //                .eq(OrderSettlementForm::getBusinessLine, 3)
  //    //                .update();
  //  }

  //  private void afterSalesHandler() {
  //    iAfterSalesRegisterService
  //        .lambdaUpdate()
  //        .eq(AfterSalesRegister::getBusinessLine, 2)
  //        .set(AfterSalesRegister::getBusinessLine, 1)
  //        .update();
  //  }

//  private void virtualWarehouseHandler() {
//    final List<VirtualWarehouse> list = iVirtualWarehouseService.lambdaQuery().list();

    //        for (VirtualWarehouse virtualWarehouse : list) {
    //            final String businessLine = virtualWarehouse.getBusinessLine();
    //            if (StringUtils.hasText(businessLine)) {
    //                Set<String> set = new HashSet<>();
    //                Stream.of(businessLine.split(",")).forEach(val -> {
    //                    if (val.equals("0") || val.equals("1") || val.equals("3") ) {
    //                        set.add("0");
    //                    }
    //                    if (val.equals("2")) {
    //                        set.add("1");
    //                    }
    //                });
    //                warehouse.setBusinessLine(String.join(",", set));
    //                iWarehouseService.updateById(warehouse);
    //            }
    //        }

//  }
}
