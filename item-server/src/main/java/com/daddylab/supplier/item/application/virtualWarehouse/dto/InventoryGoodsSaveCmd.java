/*
package com.daddylab.supplier.item.application.virtualWarehouse.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

*/
/**
 * <AUTHOR> up
 * @date 2024年03月01日 3:20 PM
 *//*

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("商品纬度库存设置明细")
public class InventoryGoodsSaveCmd extends Command {

    private static final long serialVersionUID = -6181271365831289711L;

    @ApiModelProperty("上一层级仓库编码，可实仓可以虚拟仓")
    private String upperWarehouseNo;

    @ApiModelProperty("库存设置明细sku编码")
    private String skuNo;

    @ApiModelProperty("库存设置明细spu编码")
    private String spuNo;

    @ApiModelProperty("库存设置明细归属仓库")
    private String warehouseNo;

    @ApiModelProperty("库存设置明细sku库存占比")
    @NotNull
    private Integer inventoryRatio;

}
*/
