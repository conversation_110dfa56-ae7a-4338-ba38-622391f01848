package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 旺店通销售出库单物流单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtSaleStockOutOrderLogistics implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 销售出库单详情id
     */
    private Integer recId;

    /**
     * 出库单ID
     */
    private Long stockoutId;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 估算重量
     */
    private BigDecimal calcWeight;

    /**
     * 称重重量
     */
    private BigDecimal weight;

    /**
     * 包装
     */
    private String packageName;

    /**
     * 物流名称
     */
    private String logisticsName;

    /**
     * 物流ID
     */
    private Integer logisticsId;

    /**
     * 估算邮资
     */
    private BigDecimal postage;

    /**
     * 备注
     */
    private String remark;

    /**
     * 长
     */
    private BigDecimal length;

    /**
     * 宽
     */
    private BigDecimal width;

    /**
     * 高
     */
    private BigDecimal height;

    /**
     * 长*宽*高
     */
    private BigDecimal volume;


}
