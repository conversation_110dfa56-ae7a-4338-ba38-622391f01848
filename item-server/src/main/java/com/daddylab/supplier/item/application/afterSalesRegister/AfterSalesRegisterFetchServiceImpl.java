package com.daddylab.supplier.item.application.afterSalesRegister;

import static com.daddylab.ark.sailor.common.base.vo.Result.SUCCESSFUL_CODE;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.BlockPolicy;
import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.GlobalThreadPool;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.ark.sailor.common.base.vo.Result;
import com.daddylab.ark.sailor.trade.domain.query.order.OrderSaleMessageQuery;
import com.daddylab.ark.sailor.trade.domain.query.refund.RefundOrderPageQuery;
import com.daddylab.ark.sailor.trade.domain.vo.order.OrderSaleMessageVO;
import com.daddylab.ark.sailor.trade.domain.vo.refund.RefundInfoVO;
import com.daddylab.ark.sailor.trade.domain.vo.refund.RefundOrderDetailVO;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.banniu.BanniuCommonService;
import com.daddylab.supplier.item.domain.banniu.BanniuConverter;
import com.daddylab.supplier.item.domain.banniu.BanniuMiniService;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.OrderWarehouseVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.MallMerchantApiFeignClient;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.MPUtil;
import com.daddylab.supplier.item.types.banniu.MiniTaskQuery;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.ImmutableList;
import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.util.backoff.BackOffExecution;
import org.springframework.util.backoff.ExponentialBackOff;
import wjb.open.api.ApiException;
import wjb.open.api.response.mini.MiniColumnListResponse;
import wjb.open.api.response.mini.MiniProjectListResponse;
import wjb.open.api.response.mini.MiniQueryTaskListResponse;
import wjb.open.api.response.v2.banniu.SellerListResponse;

/**
 * <AUTHOR>
 * @since 2023/8/21
 */
@Service
@Slf4j
public class AfterSalesRegisterFetchServiceImpl implements AfterSalesRegisterFetchService {

  public static final String[][] banniuSheetNames = {
    {"舆情团队", "普通舆情登记表（舆情组）"},
    {"舆情团队", "高危舆情登记表（舆情组）"},
    {"客服团队", "售前登记表"},
    {"售后团队", "售后工单对接表"},
    {"售后团队", "VIP售后登记表"},
    {"售后团队", "（新）售后登记表"},
    {"售后团队", "呼入电话登记表"},
    {"舆情团队", "舆情投诉登记表"},
    {"旺店通旗舰版", "【退换补】旺店通旗舰版"},
    {"旺店通旗舰版", "【原始单补发】旺店通旗舰版"},
  };
  public static final DateTimeFormatter SETTLE_DURATION_FORMATTER =
      DatePattern.SIMPLE_MONTH_FORMATTER;
  private final ExponentialBackOff backoff;

  public AfterSalesRegisterFetchServiceImpl() {
    this.backoff = new ExponentialBackOff();
    this.backoff.setMaxElapsedTime(180_000);
  }

  private AfterSalesRegisterConfig afterSalesRegisterConfig;
  private IItemService iItemService;
  private ItemSkuGateway itemSkuGateway;

  @Autowired
  public void setAfterSalesRegisterConfig(AfterSalesRegisterConfig afterSalesRegisterConfig,IItemService iItemService, ItemSkuGateway itemSkuGateway) {
    this.afterSalesRegisterConfig = afterSalesRegisterConfig;
    this.iItemService = iItemService;
    this.itemSkuGateway = itemSkuGateway;
  }

  @SuppressWarnings("rawtypes")
  private ImmutableList<FieldMapping> fieldMapping() {
    return ImmutableList.<FieldMapping>builder()
        .add(
            FieldMapping.<String, Long>builderWithConvert("*", "$['id']")
                .map(Long::parseLong)
                .fieldSetter(AfterSalesRegister::setBanniuId)
                .build())
        .add(
            FieldMapping.builderForStr("*", "$['订单号']")
                .fieldSetter(AfterSalesRegister::setOrderNo)
                .build())
        .add(
            FieldMapping.<String, LocalDateTime>builderWithConvert("*", "$['付款时间']")
                .map(DateUtil::parseCompatibility)
                .fieldSetter(AfterSalesRegister::setPayTime)
                .build())
        .add(
            FieldMapping.<String, LocalDateTime>builderWithConvert("*", "$['下单时间']")
                .map(DateUtil::parseCompatibility)
                .fieldSetter(
                    (afterSalesRegister, time) -> {
                      if (afterSalesRegister.getPayTime() == null) {
                        afterSalesRegister.setPayTime(time);
                      }
                    })
                .build())
        .add(
            FieldMapping.builderForStr("*", "$['店铺']", "$['店铺名称']")
                .map(this::mapShopName)
                .fieldSetter(AfterSalesRegister::setShopName)
                .build())
        .add(
            FieldMapping.builderForStr("*", "$['商品信息'][0]['商品编码']")
                .fieldSetter(AfterSalesRegister::setItemCode)
                .build())
        .add(
            FieldMapping.builderForStr("*", "$['商品信息'][0]['商品名称']")
                .fieldSetter(AfterSalesRegister::setItemName)
                .build())
        .add(
            FieldMapping.builderForStr("*", "$['商品信息'][0]['商品规格']")
                .fieldSetter(AfterSalesRegister::setSpecifications)
                .build())
        .add(
            FieldMapping.builderForStr("*", "$['商品信息'][0]['售后数量']")
                .fieldSetter(AfterSalesRegister::setNum)
                .build())
        .add(
            FieldMapping.builderForStr("*", "$['商品信息'][0]['商品数量']")
                .fieldSetter(AfterSalesRegister::setNum)
                .build())
        .add(
            FieldMapping.builderForStr("*", "$['商品信息'][0]['发货仓']")
                .fieldSetter(AfterSalesRegister::setWarehouse)
                .build())
        .add(
            FieldMapping.<String, List<String>>builderWithConvert("*", "$.问题描述")
                .map(v -> StrUtil.split(v, ","))
                .fieldSetter(
                    (o, v) -> Optional.ofNullable(CollUtil.get(v, 0)).ifPresent(o::setDesc1))
                .fieldSetter(
                    (o, v) -> Optional.ofNullable(CollUtil.get(v, 1)).ifPresent(o::setDesc2))
                .fieldSetter(
                    (o, v) -> Optional.ofNullable(CollUtil.get(v, 2)).ifPresent(o::setDesc3))
                .build())
        .add(
            FieldMapping.builderForStr("*", "$.售后处理意见")
                .fieldSetter(AfterSalesRegister::setHandleAdvice)
                .build())
        .add(
            FieldMapping.builderForStr("*", "$['工厂\\/仓库承担运费']", "$['工厂\\/云仓承担运费']")
                .fieldSetter(AfterSalesRegister::setFactoryUndertakeFreight)
                .build())
        .add(
            FieldMapping.builderForStr(
                    "*", "$['工厂承担商品货款']", "$['云仓承担商品货款']", "$['工厂\\/仓库承担货款']", "$['工厂\\/仓库承担商品货款']")
                .fieldSetter(AfterSalesRegister::setFactoryUndertakeGoodsAmount)
                .build())
        .add(
            FieldMapping.builderForStr("*", "$['工厂\\/仓库承担其他补偿']", "$['工厂\\/云仓承担其他补偿']")
                .fieldSetter(AfterSalesRegister::setFactoryUndertakeOtherAmount)
                .build())
        .add(
            FieldMapping.builderForStr("*", "$['体验基金（优惠券）']")
                .fieldSetter(AfterSalesRegister::setExperienceFundCoupon)
                .build())
        .add(
            FieldMapping.builderForStr("*", "$['体验基金（现金）']")
                .fieldSetter(AfterSalesRegister::setExperienceFundCash)
                .build())
        .add(
            FieldMapping.<String, List<String>>builderWithConvert("*", "$.体验金承担原因")
                .map(v -> StrUtil.split(v, ","))
                .fieldSetter(
                    (o, v) ->
                        Optional.ofNullable(CollUtil.get(v, 0))
                            .ifPresent(o::setExperienceFundReason1))
                .fieldSetter(
                    (o, v) ->
                        Optional.ofNullable(CollUtil.get(v, 1))
                            .ifPresent(o::setExperienceFundReason2))
                .fieldSetter(
                    (o, v) ->
                        Optional.ofNullable(CollUtil.get(v, 2))
                            .ifPresent(o::setExperienceFundReason3))
                .build())
        .add(
            FieldMapping.<String, List<String>>builderWithConvert("*", "$.相关图片")
                .map(v -> StrUtil.splitTrim(v, ","))
                .fieldSetter(AfterSalesRegister::setImages)
                .build())
        // 【退换补】旺店通旗舰版、 【原始单补发】旺店通旗舰版
        .add(
            FieldMapping.builderForStr(
                    "*",
                    "$['补发商品'][0]['ERP商家编码']",
                    "$['原单商品'][0]['原单商品-商家编码']",
                    "$['非原单商品'][0]['ERP商家编码']")
                .fieldSetter(AfterSalesRegister::setItemCode)
                .build())
        .add(
            FieldMapping.builderForStr(
                    "*",
                    "$['补发商品'][0]['ERP货品名称']",
                    "$['原单商品'][0]['原单商品-货品名称']",
                    "$['非原单商品'][0]['ERP货品名称']")
                .fieldSetter(AfterSalesRegister::setItemName)
                .build())
        .add(
            FieldMapping.builderForStr(
                    "*",
                    "$['补发商品'][0]['补发商品-补发数量']",
                    "$['原单商品'][0]['原单商品-数量']",
                    "$['非原单商品'][0]['补发数量']")
                .fieldSetter(AfterSalesRegister::setNum)
                .build())
        .add(
            FieldMapping.builderForStr(
                    "*",
                    "$['补发商品'][0]['ERP规格名称']",
                    "$['补发商品'][0]['ERP规格名称']",
                    "$['原单商品'][0]['\\[WDTQJB\\]-\\[子\\]规格码']")
                .fieldSetter(AfterSalesRegister::setSpecifications)
                .build())
        .add(
            FieldMapping.builderForStr("*", "$['仓库信息查询（非原单）'][0]['发货仓']", "$['仓库信息查询'][0]['发货仓']")
                .fieldSetter(AfterSalesRegister::setWarehouse)
                .build())
        .add(
            FieldMapping.builderForStr("*", "$['工厂\\/仓库承担商品货款']")
                .fieldSetter(AfterSalesRegister::setFactoryUndertakeGoodsAmount)
                .build())
        .add(
            FieldMapping.builderForStr("*", "$['\\[WDTQJB\\]-\\[主\\]店铺编号']")
                .filter(po -> StrUtil.isBlank(po.getShopName()))
                .map(this::mapShopNo2ShopName)
                .fieldSetter(AfterSalesRegister::setShopName)
                .build())
        .build();
  }

  @Setter @Autowired private BanniuMiniService banniuMiniService;
  @Setter @Autowired private BanniuCommonService banniuCommonService;
  @Setter @Autowired private IAfterSalesRegisterService afterSalesRegisterService;
  @Setter @Autowired private MallMerchantApiFeignClient mallMerchantApiFeignClient;
  @Setter @Autowired private IWdtOrderService wdtOrderService;
  @Setter @Autowired private IWdtOrderDetailService wdtOrderDetailService;
  @Setter @Autowired private IWarehouseService warehouseService;
  @Autowired @Setter private IShopService shopService;

  private final @NonNull LoadingCache<Integer, List<MiniColumnListResponse.ColumnOption>>
      columnOptionsCache =
          Caffeine.newBuilder()
              .expireAfterAccess(Duration.ofSeconds(600))
              .build(this::columnOptions);

  List<MiniColumnListResponse.ColumnOption> columnOptions(int projectId) throws ApiException {
    return banniuMiniService.columnOptions(projectId);
  }

  public static void main(String[] args) {
    String s =
        "{\n"
            + "          \"补发商品\": [\n"
            + "            {\n"
            + "              \"ERP商家编码\": 100357401,\n"
            + "              \"补发商品-原价\": \"\",\n"
            + "              \"ERP货品名称\": \"【家装工厂发货】维科60支素色长绒棉\",\n"
            + "              \"补发商品-补发数量\": 1,\n"
            + "              \"ERP规格名称\": \"颜色:冰川灰|规格:200*230cm\",\n"
            + "              \"补发商品-备注\": \"\",\n"
            + "              \"货品类型\": 34498,\n"
            + "              \"补发商品-折扣\": 1.0\n"
            + "            }\n"
            + "          ],\n"
            + "          \"同步时间\": \"2025-03-25 22:34:41\",\n"
            + "          \"退回物流公司、单号为空时是否取原单信息\": \"否\",\n"
            + "          \"原始单号\": \"20250318467397616907007237-1\",\n"
            + "          \"仓库信息查询（非原单）\": [\n"
            + "            {\n"
            + "              \"发货仓\": \"饮尚宝\",\n"
            + "              \"补发/换货单-发货形式\": \"\",\n"
            + "              \"商品编码\": \"\"\n"
            + "            }\n"
            + "          ],\n"
            + "          \"修改时间\": \"2025-03-31 16:39:08\",\n"
            + "          \"id\": 9921,\n"
            + "          \"创建人\": 191927886,\n"
            + "          \"物流单号\": \"邮政快递包裹: 9878483527681\",\n"
            + "          \"工厂/仓库承担商品货款\": \"无货款费用\",\n"
            + "          \"退回物流公司、单号为空时是否取原单信息(退换单)\": \"否\",\n"
            + "          \"重复工单量\": 1,\n"
            + "          \"同步详情\": \"ok\",\n"
            + "          \"物流单号（定时获取）\": \"邮政快递包裹: 9878483527681\",\n"
            + "          \"[WDTQJB]-[主]是否已归档\": 2,\n"
            + "          \"仓库信息查询\": [\n"
            + "            {\n"
            + "              \"\": \"$.仓库信息查询（非原单）[0]\"\n"
            + "            }\n"
            + "          ],\n"
            + "          \"创建类型\": \"补发单\",\n"
            + "          \"是否忽略地址解析警告\": \"是\",\n"
            + "          \"新生成的订单编号(trade_no)(补发)\": \"JY2025032516955\",\n"
            + "          \"ERP包裹号\": 34425002,\n"
            + "          \"同步状态\": \"成功\",\n"
            + "          \"订单号\": 20250318467397616907007237,\n"
            + "          \"[WDTQJB]-[主]店铺编号\": \"K0004\",\n"
            + "          \"ERP订单编号\": \"JY2025031814271\",\n"
            + "          \"补发类型\": \"非原单原地址\",\n"
            + "          \"创建时间\": \"2025-03-25 22:34:41\",\n"
            + "          \"新生成的订单编号\": \"JY2025032516955\",\n"
            + "          \"任务状态\": \"待处理\",\n"
            + "          \"重复工作表名称\": \"售后单自动拉取\",\n"
            + "          \"标题\": \"成功\",\n"
            + "          \"补发商品（非原单时填写）\": [\n"
            + "            {\n"
            + "              \"\": \"$.补发商品[0]\"\n"
            + "            }\n"
            + "          ]\n"
            + "        }";
    JSONObject jsonObject = JSONObject.parseObject(s);
    JSONPath jsonPath = JSONPath.compile("$['\\[WDTQJB\\]-\\[主\\]店铺编号']");
    Object eval = jsonPath.eval(jsonObject);
    System.out.println(eval);
  }

  private final @NonNull LoadingCache<String, List<SellerListResponse.Seller>> banniuSellerCache =
      Caffeine.newBuilder().expireAfterAccess(Duration.ofSeconds(600)).build(k -> shops());

  @SneakyThrows
  private List<SellerListResponse.Seller> shops() {
    return banniuCommonService.shops();
  }

  private String mapShopName(String shopId) {
    return Objects.requireNonNull(banniuSellerCache.get(""), "班牛店铺列表加载失败").stream()
        .filter(v -> v.getId().equals(shopId))
        .findFirst()
        .map(SellerListResponse.Seller::getTitle)
        .orElse(shopId);
  }

  private String mapShopNo2ShopName(String shopNo) {
    return shopService.getBySn(shopNo).map(Shop::getName).orElse(shopNo);
  }

  @Override
  public CompletableFuture<Void> fetchBanniu(YearMonth month) {
    return CompletableFuture.runAsync(() -> pullImpl(month), GlobalThreadPool.getExecutor());
  }

  @Override
  public CompletableFuture<Void> fetchMall(YearMonth month) {
    return CompletableFuture.runAsync(
        () -> fetchMallRefundOrderImpl(month), GlobalThreadPool.getExecutor());
  }

  @SneakyThrows
  private void pullImpl(YearMonth month) {
    final List<MiniProjectListResponse.Project> projects;
    projects = banniuMiniService.projects();
    final Map<String, MiniProjectListResponse.Project> projectsMap =
        projects.stream()
            .collect(
                Collectors.toMap(
                    MiniProjectListResponse.Project::getName, Function.identity(), (a, b) -> b));
    final ExecutorService executorService =
        ExecutorBuilder.create()
            .setThreadFactory(ThreadFactoryBuilder.create().setNamePrefix("ASR-FETCH").build())
            .useSynchronousQueue()
            .setHandler(new BlockPolicy())
            .build();
    final CountDownLatch countDownLatch = new CountDownLatch(banniuSheetNames.length);
    for (String[] banniuSheetItem : banniuSheetNames) {
      log.info("【客服售后登记表】{} START", Arrays.toString(banniuSheetItem));
      final String banniuSheetName = banniuSheetItem[1];
      final MiniProjectListResponse.Project project = projectsMap.get(banniuSheetName);
      if (project == null) {
        log.info("【客服售后登记表】{} 未找到指定工作表", Arrays.toString(banniuSheetItem));
        countDownLatch.countDown();
        continue;
      }
      while (true) {
        try {
          CompletableFuture.runAsync(
                  () -> {
                    fetchSheetImpl(month, banniuSheetItem, project);
                  },
                  executorService)
              .thenRun(countDownLatch::countDown);
          break;
        } catch (RejectedExecutionException e) {
          ThreadUtil.sleep(1_000);
        }
      }
    }
    countDownLatch.await();
    executorService.shutdownNow();
  }

  private void fetchSheetImpl(
      YearMonth month, String[] banniuSheetItem, MiniProjectListResponse.Project project) {
    final List<MiniColumnListResponse.ColumnOption> columnOptions =
        Objects.requireNonNull(columnOptionsCache.get(project.getProject_id()), "查询班牛工作表组件配置异常");

    BackOffExecution backOffExecution = backoff.start();
    int continuousErr = 0;
    for (LocalDate cursorForDayOfMonth = month.atDay(1), monthEnd = month.atEndOfMonth();
        !cursorForDayOfMonth.isAfter(monthEnd);
        cursorForDayOfMonth = cursorForDayOfMonth.plusDays(1)) {
      final LocalDateTime timeStart = cursorForDayOfMonth.atStartOfDay();
      final LocalDateTime timeEnd = cursorForDayOfMonth.plusDays(1).atStartOfDay();
      final int pageSize = 100;
      int pageNum = 0;
      while (true) {
        final MiniTaskQuery query = new MiniTaskQuery();
        query.setProjectId(project.getProject_id());
        query.setPageNum(++pageNum);
        query.setPageSize(pageSize);
        query.setModifiedStart(timeStart);
        query.setModifiedEnd(timeEnd);

        final StopWatch stopWatch = new StopWatch();
        log.info("【客服售后登记表】{} FETCH {}", JSON.toJSONString(banniuSheetItem), query);

        final MiniQueryTaskListResponse.ResultMap tasksResult;
        try {
          stopWatch.start("fetch");
          tasksResult = banniuMiniService.tasks(query);
          stopWatch.stop();

          if (tasksResult.getResult() == null) {
            continue;
          }

          stopWatch.start("convert");
          final List<JSONObject> data =
              BanniuConverter.SHARED.convertData(columnOptions, tasksResult.getResult());
          stopWatch.stop();

          stopWatch.start("save");
          saveData(project, data, month);
          stopWatch.stop();

          log.debug(
              "【客服售后登记表】{} FETCH DONE size:{} query:{} time:{}",
              Arrays.toString(banniuSheetItem),
              tasksResult.getResult().size(),
              query,
              stopWatch);

          // 拉取成功一次即重置错误指示器
          if (continuousErr > 0) {
            log.info("【客服售后登记表】班牛数据拉取第{}次重试成功，重置错误指示器:{}", continuousErr, backOffExecution);
            continuousErr = 0;
            backOffExecution = backoff.start();
          }
          if (tasksResult.getPage_num() >= tasksResult.getTotal_page_num()) {
            break;
          }
        } catch (Throwable ex) {
          continuousErr++;
          if (ex instanceof ApiException) {
            final ApiException ex1 = (ApiException) ex;
            log.error("【客服售后登记表】班牛数据拉取异常:{} {}", ex1.getErrCode(), ex1.getErrMsg());
          } else {
            log.error("【客服售后登记表】班牛数据拉取异常:{}", ex.getMessage(), ex);
          }
          final long backoffMillis = backOffExecution.nextBackOff();
          if (backoffMillis == BackOffExecution.STOP) {
            log.error("【客服售后登记表】班牛数据拉取异常，连续失败，任务终止 backoff:{}", backOffExecution);
            throw new RuntimeException("班牛数据拉取异常，连续失败，任务终止");
          } else {
            log.debug("【客服售后登记表】班牛数据拉取异常，等待{}ms后进行第{}次重试", backoffMillis, continuousErr);
            ThreadUtil.sleep((int) backoffMillis);
          }
        }
      }
    }
  }

  private void saveData(
      MiniProjectListResponse.Project project, List<JSONObject> data, YearMonth month) {
    if (CollUtil.isEmpty(data)) {
      return;
    }
    final HashMap<String, JSONObject> rawDataMap = new HashMap<>();
    final List<AfterSalesRegister> poList =
        data.stream()
            .map(
                datum -> {
                  final AfterSalesRegister afterSalesRegister =
                      convert2po(datum, project.getName(), month);
                  ReflectionUtils.doWithFields(
                      AfterSalesRegister.class,
                      field -> {
                        if (CharSequence.class.isAssignableFrom(field.getType())) {
                          ReflectionUtils.makeAccessible(field);
                          final CharSequence v = (CharSequence) field.get(afterSalesRegister);
                          if (StrUtil.isBlank(v)) {
                            field.set(afterSalesRegister, null);
                          }
                        }
                      });
                  if (!StrUtil.isBlank(afterSalesRegister.getOrderNo())) {
                    rawDataMap.put(afterSalesRegister.getOrderNo(), datum);
                  }
                  return afterSalesRegister;
                })
            .filter(v -> StrUtil.isNotBlank(v.getOrderNo()))
            .collect(Collectors.toList());
    final List<String> orderNos =
        poList.stream().map(AfterSalesRegister::getOrderNo).collect(Collectors.toList());
    if (CollUtil.isEmpty(orderNos)) {
      return;
    }
    final String settlementDuration = month.format(SETTLE_DURATION_FORMATTER);
    final List<AfterSalesRegister> existsData =
        afterSalesRegisterService
            .lambdaQuery()
            .in(AfterSalesRegister::getOrderNo, orderNos)
            .eq(AfterSalesRegister::getSettleDuration, settlementDuration)
            .list();
    final Map<AfterSalesRegisterKey, AfterSalesRegister> existsDataMap =
        existsData.stream()
            .collect(
                Collectors.toMap(
                    this::makeAfterSalesRegisterKey, Function.identity(), (a, b) -> a));
    final ArrayList<AfterSalesRegister> savePOList = new ArrayList<>();
    final ArrayList<AfterSalesRegister> updatePOList = new ArrayList<>();
    for (AfterSalesRegister po : poList) {
      final AfterSalesRegister existsPo = existsDataMap.get(makeAfterSalesRegisterKey(po));
      log.debug(
          "【客服售后登记表】{} SAVE DATA {} exists:{}", new String[] {project.getName()}, po, existsPo);
      if (StrUtil.isBlank(po.getOrderNo())) {
        continue;
      }

      String itemCode = "";
      if (StrUtil.isNotBlank(po.getItemCode())) {
        final ItemSku itemSku = itemSkuGateway.getBySkuCode(po.getItemCode());
        if (Objects.nonNull(itemSku)) {
          final Item item = iItemService.getById(itemSku.getItemId());
          if (Objects.nonNull(item)) {
            itemCode = item.getCode();
          }
        }
      }
      final String itemCodeFinal = itemCode;
      List<WdtOrderDetail> wdtOrderDetails = wdtOrderDetailService.listBySrcTid(po.getOrderNo());
      Optional<WdtOrderDetail> wdtOrderDetailThisSpec =
          wdtOrderDetails.stream()
              .filter(
                  it -> {
                    boolean b1 =
                        it.getSpecNo().equals(po.getItemCode())
                            || it.getGoodsNo().equals(po.getItemCode())
                            || it.getSuiteNo().equals(po.getItemCode());
                    if (!b1 && StringUtils.hasText(itemCodeFinal)) {
                      b1 =
                          it.getSpecNo().equals(itemCodeFinal)
                              || it.getGoodsNo().equals(itemCodeFinal)
                              || it.getSuiteNo().equals(itemCodeFinal);
                    }
                    return b1;
                  })
              .findFirst();
      wdtOrderDetailThisSpec.ifPresent(
          wdtOrderDetail -> {
            if (po.getPayTime() == null) {
              po.setPayTime(wdtOrderDetail.getPayTime());
            }
            if (StrUtil.isNotBlank(po.getWarehouse())) {
              String warehouseNo = wdtOrderDetail.getWarehouseNo();
              warehouseService
                  .queryWarehouseByNo(warehouseNo)
                  .ifPresent(warehouse -> po.setWarehouse(warehouse.getName()));
            }
          });
      final JSONObject datum = rawDataMap.get(po.getOrderNo());
      final String id = MapUtil.getStr(datum, "id");
      final AfterSalesRegisterTrace.TableTrace source =
          new AfterSalesRegisterTrace.TableTrace(
              project.getProject_id(),
              project.getName(),
              id,
              afterSalesRegisterConfig.isTraceDetails() ? datum.toJSONString() : null);
      if (existsPo != null) {
        po.setId(existsPo.getId());
        po.setTraceAppend(new AfterSalesRegisterTrace(source));
        final List<String> images = existsPo.getImages();
        if (images != null && po.getImages() != null) {
          images.addAll(po.getImages());
          final List<String> distinctImages =
              images.stream().distinct().collect(Collectors.toList());
          po.setImages(distinctImages);
        }
        updatePOList.add(po);
      } else {
        po.setTrace(new AfterSalesRegisterTrace(source));
        savePOList.add(po);
      }
    }
    if (!savePOList.isEmpty()) {
      afterSalesRegisterService.saveBatch(savePOList);
    }
    if (!updatePOList.isEmpty()) {
      afterSalesRegisterService.updateBatchById(updatePOList);
    }
  }

  private AfterSalesRegisterKey makeAfterSalesRegisterKey(AfterSalesRegister v) {
    return AfterSalesRegisterKey.of(v.getOrderNo(), v.getItemCode());
  }

  @lombok.NonNull
  public AfterSalesRegister convert2po(JSONObject datum, String name, YearMonth month) {
    preHandleData(datum);
    final AfterSalesRegister afterSalesRegister = new AfterSalesRegister();
    //noinspection rawtypes
    for (FieldMapping mapping : fieldMapping()) {
      if (mapping.testSheet(name)) {
        final String[] fieldNames = mapping.getBanniuField();
        for (String fieldName : fieldNames) {
          try {
            JSONPath jsonPath = JSONPath.compile(fieldName);
            Object value = jsonPath.eval(datum);
            if (value == null) {
              continue;
            }
            if (value instanceof CharSequence) {
              value = StrUtil.trim((CharSequence) value);
            }
            mapping.set(afterSalesRegister, value);
            break;
          } catch (Exception e) {
            log.error(
                "[客服售后登记-FETCH] convert2po, mapping exception:{} for:{}",
                e.getMessage(),
                new String[] {mapping.getBanniuSheet(), fieldName},
                e);
          }
        }
      }
    }
    afterSalesRegister.setSettleDuration(month.format(SETTLE_DURATION_FORMATTER));
    return afterSalesRegister;
  }

  private static void preHandleData(JSONObject datum) {
    final JSONObject addValues = new JSONObject();
    for (String key : datum.keySet()) {
      for (Pair<String, String> pair : Arrays.asList(new Pair<>("(", ")"), new Pair<>("（", "）"))) {
        final int indexOfLeft = key.indexOf(pair.getKey());
        if (indexOfLeft > 0) {
          int indexOfRight = key.lastIndexOf(pair.getValue());
          String keyCleaned;
          if (indexOfRight == key.length() - 1) {
            keyCleaned = key.substring(0, indexOfLeft);
            addValues.put(keyCleaned, datum.get(key));
          } else if (indexOfRight > 0) {
            keyCleaned = key.substring(0, indexOfLeft) + key.substring(indexOfRight + 1);
            addValues.put(keyCleaned, datum.get(key));
          }
        }
      }
    }
    if (!addValues.isEmpty()) {
      addValues.forEach(datum::putIfAbsent);
    }
  }

  private void fetchMallRefundOrderImpl(YearMonth month) {
    log.info("【客服售后登记表】开始获取老爸商城维权信息:{}", month);

    final ZoneId zone = ZoneId.systemDefault();
    final LocalDateTime startTime = month.atDay(1).atStartOfDay();
    final long startTimeEpochSecond = startTime.atZone(zone).toEpochSecond();
    final LocalDateTime endTime = month.atEndOfMonth().plusDays(1).atStartOfDay();
    final long endTimeEpochSecond = endTime.atZone(zone).toEpochSecond();
    long currentPage = 1L;
    final String settlementDuration = month.format(SETTLE_DURATION_FORMATTER);
    while (true) {
      final RefundOrderPageQuery pageQuery = new RefundOrderPageQuery();
      pageQuery.setReturnsModes(Collections.singletonList(4));
      pageQuery.setCurrent(currentPage);
      pageQuery.setSize(100L);
      pageQuery.setShopId(afterSalesRegisterConfig.getMallShopId());
      pageQuery.setStartTime(startTimeEpochSecond);
      pageQuery.setEndTime(endTimeEpochSecond);

      final Result<Page<RefundInfoVO>> refundPage =
          mallMerchantApiFeignClient.queryRefundPage(pageQuery);
      final IPage<RefundInfoVO> refundPageData = refundPage.getData();
      if (refundPageData == null) {
        log.error("【客服售后登记表】获取老爸商城维权列表异常:{}", refundPage);
        break;
      }
      log.info("【客服售后登记表】获取老爸商城维权信息:{}/{}", refundPageData.getCurrent(), refundPageData.getPages());
      if (CollectionUtil.isEmpty(refundPageData.getRecords())) {
        break;
      }

      final List<String> orderNos =
          refundPageData.getRecords().stream()
              .map(RefundInfoVO::getOrderNo)
              .distinct()
              .collect(Collectors.toList());
      final WdtOrderMapper wdtOrderMapper = wdtOrderService.getDaddyBaseMapper();
      final List<OrderWarehouseVO> orderWarehouseVOS =
          wdtOrderMapper.selectOrderWarehouseByOrderNos(orderNos);
      final Map<AfterSalesRegisterKey, OrderWarehouseVO> warehouseVOMap =
          orderWarehouseVOS.stream()
              .collect(
                  Collectors.toMap(
                      v -> AfterSalesRegisterKey.of(v.getOrderNo(), v.getSkuCode()),
                      Function.identity(),
                      (a, b) -> a));

      for (RefundInfoVO refundInfoVO : refundPageData.getRecords()) {
        final String orderNo = refundInfoVO.getOrderNo();
        final Long orderId = refundInfoVO.getOrderId();
        final Long refundOrderId = refundInfoVO.getRefundOrderId();
        final Result<RefundOrderDetailVO> refundOrderDetailVOResult =
            mallMerchantApiFeignClient.queryRefundDetail(refundOrderId);
        if (refundOrderDetailVOResult.getCode() != SUCCESSFUL_CODE
            || Objects.isNull(refundOrderDetailVOResult.getData())) {
          log.error("【客服售后登记表】获取老爸商城维权详情异常:{}", refundOrderDetailVOResult);
          continue;
        }
        final RefundOrderDetailVO refundOrderDetailVO = refundOrderDetailVOResult.getData();
        final Optional<RefundOrderDetailVO.ReturnFreightDetail> returnFreightDetail =
            Optional.ofNullable(refundOrderDetailVO.getReturnFreightDetail());
        final Optional<BigDecimal> merchantReduceAmount =
            returnFreightDetail.map(
                RefundOrderDetailVO.ReturnFreightDetail::getMerchantReduceAmount);
        final AfterSalesRegister afterSalesRegister =
            afterSalesRegisterService
                .lambdaQuery()
                .eq(AfterSalesRegister::getOrderNo, orderNo)
                .eq(AfterSalesRegister::getSettleDuration, settlementDuration)
                .eq(AfterSalesRegister::getItemCode, refundOrderDetailVO.getSkuNo())
                .last(MPUtil.limit(1))
                .one();
        final OrderSaleMessageQuery sellerRemarkQuery = new OrderSaleMessageQuery();
        sellerRemarkQuery.setOrderId(orderId);
        final Result<List<OrderSaleMessageVO>> sellerRemarkListQueryResult =
            mallMerchantApiFeignClient.querySellerRemarkList(sellerRemarkQuery);
        if (sellerRemarkListQueryResult.getCode() != SUCCESSFUL_CODE) {
          log.error("【客服售后登记表】获取老爸商城卖家备注异常:{}", sellerRemarkListQueryResult);
          continue;
        }
        final List<OrderSaleMessageVO> selelrRemarkList = sellerRemarkListQueryResult.getData();
        final String sellerRemarkMergedStr =
            selelrRemarkList.stream()
                .map(
                    v ->
                        String.format(
                            "%s %s %s",
                            v.getDisplayName(), DateUtil.format(v.getRemarkTime()), v.getRemark()))
                .collect(Collectors.joining("\n"));
        final String merchantReduceAmountStr =
            merchantReduceAmount.map(BigDecimal::toPlainString).orElse("");
        if (afterSalesRegister != null) {
          final String factoryUndertakeFreight = afterSalesRegister.getFactoryUndertakeFreight();
          if (merchantReduceAmount.isPresent()) {
            if (StrUtil.isNotBlank(factoryUndertakeFreight)) {
              final String mergedFactoryUndertakeFreight =
                  String.format("%s、%s", factoryUndertakeFreight, merchantReduceAmountStr);
              afterSalesRegister.setFactoryUndertakeFreight(mergedFactoryUndertakeFreight);

            } else {
              afterSalesRegister.setFactoryUndertakeFreight(merchantReduceAmountStr);
            }
          }
          if (!selelrRemarkList.isEmpty()) {

            afterSalesRegister.setHandleAdvice(
                Optional.ofNullable(afterSalesRegister.getHandleAdvice())
                    .filter(StrUtil::isNotBlank)
                    .map(ha -> String.format("%s\n%s", ha, sellerRemarkMergedStr))
                    .orElse(sellerRemarkMergedStr));
          }
          if (afterSalesRegister.getPayTime() == null) {
            final LocalDateTime payTime =
                Optional.ofNullable(refundOrderDetailVO.getPayTime())
                    .map(DateUtil::toLocalDateTime)
                    .orElse(null);
            afterSalesRegister.setPayTime(payTime);
          }
          if (StrUtil.isBlank(afterSalesRegister.getShopName())) {
            afterSalesRegister.setShopName("自研商城");
          }
          if (StrUtil.isBlank(afterSalesRegister.getItemCode())) {
            afterSalesRegister.setItemCode(refundOrderDetailVO.getSkuNo());
          }
          if (StrUtil.isBlank(afterSalesRegister.getNum())) {
            afterSalesRegister.setNum(String.valueOf(refundOrderDetailVO.getRefundNum()));
          }
          if (StrUtil.isBlank(afterSalesRegister.getItemName())) {
            afterSalesRegister.setItemName(refundOrderDetailVO.getOrderItemName());
            afterSalesRegister.setSpecifications(refundOrderDetailVO.getOrderItemSpec());
          }
          final AfterSalesRegisterKey afterSalesRegisterKey =
              makeAfterSalesRegisterKey(afterSalesRegister);
          final OrderWarehouseVO orderWarehouseVO = warehouseVOMap.get(afterSalesRegisterKey);
          if (orderWarehouseVO != null && StrUtil.isBlank(afterSalesRegister.getWarehouse())) {
            afterSalesRegister.setWarehouse(orderWarehouseVO.getWarehouseName());
          }
          afterSalesRegisterService.updateById(afterSalesRegister);
        } else {
          final AfterSalesRegister afterSalesRegisterNew = new AfterSalesRegister();
          afterSalesRegisterNew.setSettleDuration(settlementDuration);
          afterSalesRegisterNew.setOrderNo(orderNo);
          final LocalDateTime payTime =
              Optional.ofNullable(refundOrderDetailVO.getPayTime())
                  .map(DateUtil::toLocalDateTime)
                  .orElse(null);
          afterSalesRegisterNew.setPayTime(payTime);
          afterSalesRegisterNew.setShopName("自研商城");
          afterSalesRegisterNew.setItemCode(refundOrderDetailVO.getSkuNo());
          afterSalesRegisterNew.setNum(String.valueOf(refundOrderDetailVO.getRefundNum()));
          afterSalesRegisterNew.setItemName(refundOrderDetailVO.getOrderItemName());
          afterSalesRegisterNew.setSpecifications(refundOrderDetailVO.getOrderItemSpec());
          final AfterSalesRegisterKey afterSalesRegisterKey =
              makeAfterSalesRegisterKey(afterSalesRegisterNew);
          final OrderWarehouseVO orderWarehouseVO = warehouseVOMap.get(afterSalesRegisterKey);
          if (orderWarehouseVO != null) {
            afterSalesRegisterNew.setWarehouse(orderWarehouseVO.getWarehouseName());
          }

          afterSalesRegisterNew.setHandleAdvice(sellerRemarkMergedStr);
          afterSalesRegisterNew.setFactoryUndertakeFreight(merchantReduceAmountStr);

          final AfterSalesRegisterTrace.TableTrace trace =
              new AfterSalesRegisterTrace.TableTrace(
                  0, "老爸商城商家后台", "refundOrderId:" + refundOrderId, "");
          afterSalesRegisterNew.setTrace(new AfterSalesRegisterTrace(trace));
          afterSalesRegisterNew.setImages(Collections.emptyList());
          afterSalesRegisterService.save(afterSalesRegisterNew);
        }
      }

      currentPage++;
      if (currentPage >= refundPageData.getPages()) {
        break;
      }
    }

  }
}
