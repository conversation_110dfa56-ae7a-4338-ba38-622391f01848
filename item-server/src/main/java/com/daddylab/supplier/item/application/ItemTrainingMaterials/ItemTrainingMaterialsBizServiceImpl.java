package com.daddylab.supplier.item.application.ItemTrainingMaterials;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.drawer.ItemDrawerMergeBizService;
import com.daddylab.supplier.item.application.message.QyMsgSendUtils;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.application.process.ProcessBizService;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsPrincipalsInfo;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.application.wiki.WikiProperties;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.ResponseAssert;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MsgTemplateCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.daddylab.supplier.item.infrastructure.wiki.WikiClient;
import com.daddylab.supplier.item.types.itemTrainingMaterials.*;
import com.daddylab.supplier.item.types.process.*;
import com.daddylab.supplier.item.types.process.assembler.TaskAssembler;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.javers.core.diff.Diff;
import org.javers.core.diff.changetype.container.CollectionChange;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/4/11
 */
@Service
@Slf4j
public class ItemTrainingMaterialsBizServiceImpl implements ItemTrainingMaterialsBizService {
    @Autowired
    private IItemTrainingMaterialsService itemTrainingMaterialsService;
    @Autowired
    private IItemTrainingMaterialsProcessService itemTrainingMaterialsProcessService;
    @Autowired
    private IItemTrainingMaterialsProcessRecordService itemTrainingMaterialsProcessRecordService;
    @Autowired
    private INewGoodsService newGoodsService;
    @Autowired
    private NewGoodsBizService newGoodsBizService;

    @Autowired
    private ItemTrainingMaterialsConfig config;
    @Autowired
    private OperateLogDomainService operateLogDomainService;
    @Autowired()
    @Lazy
    private ProcessEngine processEngine;

    @Autowired
    private MsgSender msgSender;

    @Autowired
    private StaffService staffService;

    @Autowired
    private RefreshConfig refreshConfig;

    @Autowired
    private ProcessBizService processBizService;

    @Autowired
    private IItemService itemService;

    @Autowired
    private IItemLaunchPlanService itemLaunchPlanService;

    @Autowired
    private ItemDrawerMergeBizService itemDrawerMergeBizService;

    @Autowired
    private IItemDrawerService itemDrawerService;

    @Autowired
    private WikiClient wikiClient;

    @Autowired
    private WikiProperties wikiProperties;

    @Override
    public SingleResponse<ItemTrainingMaterialsDrawer> drawer(Long itemId) {
        final Optional<ItemDrawerMerge> itemDrawerMerge = getItemDrawerMerge(itemId);
        final Long rawItemId = itemId;
        itemId = itemDrawerMerge.map(ItemDrawerMerge::getItemId).orElse(rawItemId);

        Long finalItemId = itemId;
        ItemTrainingMaterials itemTrainingMaterials = Optional.ofNullable(getItemTrainingMaterials(itemId))
                                                              .orElseGet(() -> getMaterialsCreateIfNotExits(finalItemId));
        final ItemTrainingMaterialsData materialsData = itemTrainingMaterials.getData();
        final ItemTrainingMaterialsDrawer itemTrainingMaterialsDrawer = new ItemTrainingMaterialsDrawer();
        itemTrainingMaterialsDrawer.setItemTrainingMaterialsId(itemTrainingMaterials.getId());
        itemTrainingMaterialsDrawer.setItemId(itemTrainingMaterials.getItemId());
        itemTrainingMaterialsDrawer.setStatus(itemTrainingMaterials.getStatus().getValue());
        itemTrainingMaterialsDrawer.setCanRollback(checkCanRollback(itemTrainingMaterials, false));
        itemTrainingMaterialsDrawer.setCanRollback2(checkCanRollback2(itemTrainingMaterials, false));

        itemTrainingMaterialsDrawer.setMaterialsData(materialsData);
        setProcessData(itemTrainingMaterialsDrawer, itemTrainingMaterials);
        return SingleResponse.of(itemTrainingMaterialsDrawer);
    }

    private Optional<ItemDrawerMerge> getItemDrawerMerge(Long itemId) {
        return Optional.ofNullable(itemDrawerMergeBizService.getItemDrawerMerge(itemId));
    }

    private boolean checkCanRollback(ItemTrainingMaterials itemTrainingMaterials, boolean throwException) {
        final ItemTrainingMaterialsProcess materialsProcess = itemTrainingMaterialsProcessService.getById(
                itemTrainingMaterials.getProcessId());
        final ItemTrainingMaterialsStatus status = itemTrainingMaterials.getStatus();
        switch (status) {
            case TO_BE_LEGAL_REVIEW:
                if (materialsProcess != null && materialsProcess.getProcessData()
                                                                .get(MaterialsProcessDataKey.legalProcessor.name()) != null) {
                    if (throwException) {
                        throw ExceptionPlusFactory.bizException(
                                ErrorCode.OPERATION_REJECT,
                                "法务已认领，无法撤回");
                    }
                }
                return true;
            case TO_BE_QC_REVIEW:
                if (materialsProcess != null && materialsProcess.getProcessData()
                                                                .get(MaterialsProcessDataKey.qcProcessor.name()) != null) {
                    if (throwException) {
                        throw ExceptionPlusFactory.bizException(
                                ErrorCode.OPERATION_REJECT,
                                "QC已认领，无法撤回");
                    }
                }
                return true;
            case TO_BE_MODIFY:
                if (materialsProcess != null && itemTrainingMaterials.getData().getDisableRollback() != null) {
                    if (throwException) {
                        throw ExceptionPlusFactory.bizException(
                                ErrorCode.OPERATION_REJECT,
                                "客服已修改，无法撤回");
                    }
                }
                return true;
            default:
                if (throwException) {
                    throw ExceptionPlusFactory.bizException(
                            ErrorCode.OPERATION_REJECT, "当前状态不允许撤回");
                }
                return false;
        }
    }
    private boolean checkCanRollback2(ItemTrainingMaterials itemTrainingMaterials, boolean throwException) {
        final ItemTrainingMaterialsProcess materialsProcess = itemTrainingMaterialsProcessService.getById(
                itemTrainingMaterials.getProcessId());
        final ItemTrainingMaterialsStatus status = itemTrainingMaterials.getStatus();
        switch (status) {
            case TO_BE_QC_REVIEW:
                return true;
            default:
                if (throwException) {
                    throw ExceptionPlusFactory.bizException(
                            ErrorCode.OPERATION_REJECT, "当前状态不允许退回采购");
                }
                return false;
        }
    }

    @NonNull
    private static List<ProcessTask> getProcessTasks(
            List<HistoricTaskInstance> historicTaskInstances,
            String name,
            List<StaffBrief> candidates,
            List<ItemTrainingMaterialsProcessRecord> processRecords) {
        return historicTaskInstances.stream()
                                    .filter(task -> task.getName().equals(name))
                                    .map(TaskAssembler.INST::historicTaskToProcessTask)
                                    .peek(
                                            task -> {
                                                task.setCandidates(candidates);
                                                if (task.isCompleted()) {
                                                    task.setComments(getComments(processRecords, task));
                                                }
                                            })
                                    .collect(Collectors.toList());
    }

    @NonNull
    private static List<Comment> getComments(List<ItemTrainingMaterialsProcessRecord> processRecords, ProcessTask task) {
        return processRecords.stream()
                             .filter(record -> record.getTaskInstId().equals(task.getId()))
                             .map(
                                     record -> {
                                         final Comment comment = new Comment();
                                         comment.setContent(record.getMsg());
                                         comment.setTime(record.getCreatedAt());
                                         comment.setAttachments(ObjectUtil.defaultIfNull(record.getAttachments(),
                                                 Collections.emptyList()));

                                         return comment;
                                     })
                             .collect(Collectors.toList());
    }


    private void setProcessData(ItemTrainingMaterialsDrawer itemTrainingMaterialsDrawer, ItemTrainingMaterials itemTrainingMaterials) {
        StaffBrief createUser = StaffAssembler.INST.toStaffBrief(itemTrainingMaterials.getCreatedUid());
        List<StaffBrief> legalCandidates = StaffAssembler.INST.longListToStaffBriefList(config.getLegalCandidates());
        List<StaffBrief> qcCandidates = StaffAssembler.INST.longListToStaffBriefList(config.getQcCandidates());
        List<StaffBrief> modifyCandidates = StaffAssembler.INST.longListToStaffBriefList(config.getModifyCandidates());
        List<StaffBrief> csCandidates = StaffAssembler.INST.longListToStaffBriefList(config.getCsCandidates());

        final Optional<ItemTrainingMaterialsProcess> itemTrainingMaterialsProcess = Optional
                .ofNullable(itemTrainingMaterials.getProcessId())
                .filter(NumberUtil::isPositive)
                .map(processId -> itemTrainingMaterialsProcessService.getById(processId));
        final Optional<ItemTrainingMaterialsProcessData> itemTrainingMaterialsProcessData = itemTrainingMaterialsProcess.map(
                ItemTrainingMaterialsProcess::getProcessData);

        final List<ItemTrainingMaterialsProcessRecord> processRecords = itemTrainingMaterialsProcess
                .map(ItemTrainingMaterialsProcess::getId)
                .map(processId -> itemTrainingMaterialsProcessRecordService.lambdaQuery()
                                                                           .eq(ItemTrainingMaterialsProcessRecord::getProcessId,
                                                                                   processId)
                                                                           .list())
                .orElseGet(Collections::emptyList);
        final List<HistoricTaskInstance> historicTaskInstances = itemTrainingMaterialsProcess
                .map(ItemTrainingMaterialsProcess::getProcessInstId)
                .map(this::getProcessHisTasks)
                .orElseGet(Collections::emptyList);

        final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo = newGoodsBizService.getNewGoodsPrincipalsInfo(
                itemTrainingMaterialsDrawer.getItemId()).getData();
        if (!CollUtil.isEmpty(newGoodsPrincipalsInfo.getQcUsers())) {
            final ArrayList<StaffBrief> qcUsers = new ArrayList<>(newGoodsPrincipalsInfo.getQcUsers());
            qcUsers.addAll(qcCandidates);
            qcCandidates = qcUsers.stream().distinct().collect(Collectors.toList());
        }
        final List<StaffBrief> buyerUsers = newGoodsPrincipalsInfo.getBuyerUsers();

        fixStatus(itemTrainingMaterials, itemTrainingMaterialsProcess, historicTaskInstances);
        final ItemTrainingMaterialsStatus status = itemTrainingMaterials.getStatus();

        final ArrayList<ProcessNode> processNodes = Lists.newArrayList();

        final ProcessNode processNode = new ProcessNode();
        processNode.setTitle("待完善");
        processNode.setProcessors(Optional.ofNullable(buyerUsers).orElseGet(Collections::emptyList));
        processNode.setOk(status.getValue() > ItemTrainingMaterialsStatus.TO_BE_COMMIT.getValue());
        processNode.setCompleteTime(itemTrainingMaterialsProcess.map(ItemTrainingMaterialsProcess::getSubmitAt)
                                                                .orElse(0L));
        processNode.setTasks(Lists.newArrayList());
        processNode.setHisTasks(Lists.newArrayList());
        processNode.setIsCurrentNode(status == ItemTrainingMaterialsStatus.TO_BE_COMMIT);
        processNodes.add(processNode);

//        final ProcessNode processNode1 = new ProcessNode();
//        processNode1.setTitle("待法务审核");
//        processNode1.setTasks(getProcessTasks(historicTaskInstances, "待法务审核", legalCandidates, processRecords));
//        processNode1.setOk(status.getValue() > ItemTrainingMaterialsStatus.TO_BE_LEGAL_REVIEW.getValue());
//        processNode1.setIsCurrentNode(status == ItemTrainingMaterialsStatus.TO_BE_LEGAL_REVIEW);
//        itemTrainingMaterialsProcess.map(ItemTrainingMaterialsProcess::getProcessData)
//                                    .map(data -> data.getLong(MaterialsProcessDataKey.legalCompleteTime.name()))
//                                            .ifPresent(processNode::setCompleteTime);
//        processNode1.setNeedClaim(false);
//        processNodes.add(processNode1);

        final ProcessNode processNode2 = new ProcessNode();
        processNode2.setTitle("待QC审核");
        processNode2.setNeedClaim(false);
        processNode2.setTasks(getProcessTasks(historicTaskInstances, "待QC审核", qcCandidates, processRecords));
        if (CollUtil.isEmpty(processNode2.getTasks())) {
            processNode2.setProcessors(qcCandidates);
        }
        processNode2.setOk(status.getValue() > ItemTrainingMaterialsStatus.TO_BE_QC_REVIEW.getValue());
        processNode2.setIsCurrentNode(status == ItemTrainingMaterialsStatus.TO_BE_QC_REVIEW);
        itemTrainingMaterialsProcess.map(ItemTrainingMaterialsProcess::getProcessData)
                                    .map(data -> data.getLong(MaterialsProcessDataKey.qcCompleteTime.name()))
                                    .ifPresent(processNode::setCompleteTime);
        processNodes.add(processNode2);

        final ProcessNode processNode3 = new ProcessNode();
        processNode3.setTitle("待修改");
        processNode3.setNeedClaim(false);
        final Set<StaffBrief> qcProcessors = new HashSet<>();
        qcProcessors.addAll(csCandidates);
        qcProcessors.addAll(modifyCandidates);
        processNode3.setProcessors(new ArrayList<>(qcProcessors));
        processNode3.setOk(status.getValue() > ItemTrainingMaterialsStatus.TO_BE_MODIFY.getValue());
        processNode3.setIsCurrentNode(status == ItemTrainingMaterialsStatus.TO_BE_MODIFY);
        itemTrainingMaterialsProcess.map(ItemTrainingMaterialsProcess::getProcessData)
                                    .map(data -> data.getLong(MaterialsProcessDataKey.modifyCompleteTime.name()))
                                    .ifPresent(processNode::setCompleteTime);
        processNodes.add(processNode3);

        final ProcessNode processNode4 = new ProcessNode();
        processNode4.setTitle("已完成");
        processNode4.setProcessors(Collections.emptyList());
        processNode4.setOk(status.getValue().equals(ItemTrainingMaterialsStatus.FINISHED.getValue()));
        processNode4.setCompleteTime(itemTrainingMaterialsProcessData.map(v -> v.getLong(MaterialsProcessDataKey.processCompleteTime.name()))
                                                                     .orElse(0L));
        processNode4.setTasks(Lists.newArrayList());
        processNode4.setHisTasks(Lists.newArrayList());
        processNode4.setIsCurrentNode(status == ItemTrainingMaterialsStatus.FINISHED);
        processNodes.add(processNode4);

        itemTrainingMaterialsDrawer.setProcessNodes(processNodes);
    }

    private void fixStatus(ItemTrainingMaterials itemTrainingMaterials, Optional<ItemTrainingMaterialsProcess> itemTrainingMaterialsProcessOptional, List<HistoricTaskInstance> historicTaskInstances) {
        if (config.isFixStatus() && !historicTaskInstances.isEmpty() && itemTrainingMaterialsProcessOptional.isPresent()) {
            final ItemTrainingMaterialsStatus status = itemTrainingMaterials.getStatus();
            final ItemTrainingMaterialsProcess itemTrainingMaterialsProcess = itemTrainingMaterialsProcessOptional.get();
            final ItemTrainingMaterialsProcessData processData = itemTrainingMaterialsProcess.getProcessData();
            for (HistoricTaskInstance historicTaskInstance : historicTaskInstances) {
                if ("待法务审核".equals(historicTaskInstance.getName())) {
                    processData.setIfCurrentIsNull(MaterialsProcessDataKey.legalStartTime, historicTaskInstance.getCreateTime());
                    processData.setIfCurrentIsNull(MaterialsProcessDataKey.legalCompleteTime, historicTaskInstance.getEndTime());
                    processData.setIfCurrentIsNull(MaterialsProcessDataKey.legalClaimTime, historicTaskInstance.getClaimTime());
                    processData.setIfCurrentIsNull(MaterialsProcessDataKey.legalProcessor,
                                                   Optional.ofNullable(historicTaskInstance.getAssignee())
                                                           .map(Long::parseLong)
                                                           .orElse(null));

                }
                if ("待QC审核".equals(historicTaskInstance.getName())) {
                    processData.setIfCurrentIsNull(MaterialsProcessDataKey.qcStartTime, historicTaskInstance.getCreateTime());
                    processData.setIfCurrentIsNull(MaterialsProcessDataKey.qcCompleteTime, historicTaskInstance.getEndTime());
                    processData.setIfCurrentIsNull(MaterialsProcessDataKey.qcClaimTime, historicTaskInstance.getClaimTime());
                    processData.setIfCurrentIsNull(MaterialsProcessDataKey.qcProcessor,
                                                   Optional.ofNullable(historicTaskInstance.getAssignee())
                                                           .map(Long::parseLong)
                                                           .orElse(null));

                }
                if ("待修改".equals(historicTaskInstance.getName())) {
                    processData.setIfCurrentIsNull(MaterialsProcessDataKey.modifyStartTime, historicTaskInstance.getCreateTime());
                    processData.setIfCurrentIsNull(MaterialsProcessDataKey.modifyCompleteTime, historicTaskInstance.getEndTime());
                    processData.setIfCurrentIsNull(MaterialsProcessDataKey.modifyClaimTime, historicTaskInstance.getClaimTime());
                    processData.setIfCurrentIsNull(MaterialsProcessDataKey.modifyProcessor,
                                                   Optional.ofNullable(historicTaskInstance.getAssignee())
                                                           .map(Long::parseLong)
                                                           .orElse(null));
                    processData.setIfCurrentIsNull(MaterialsProcessDataKey.processCompleteTime, historicTaskInstance.getEndTime());
                }
            }
            ItemTrainingMaterialsStatus rightStatus = null;
            if (processData.get(MaterialsProcessDataKey.processCompleteTime.name()) != null) {
                rightStatus = ItemTrainingMaterialsStatus.FINISHED;
            } else if (processData.get(MaterialsProcessDataKey.modifyCompleteTime.name()) != null) {
                rightStatus = ItemTrainingMaterialsStatus.FINISHED;
            } else if (processData.get(MaterialsProcessDataKey.qcCompleteTime.name()) != null) {
                rightStatus = ItemTrainingMaterialsStatus.TO_BE_MODIFY;
            } else if (processData.get(MaterialsProcessDataKey.legalCompleteTime.name()) != null) {
                rightStatus = ItemTrainingMaterialsStatus.TO_BE_QC_REVIEW;
            }
            if (rightStatus != null && rightStatus != status) {
                itemTrainingMaterials.setStatus(rightStatus);
                itemTrainingMaterialsService.updateById(itemTrainingMaterials);

                itemTrainingMaterialsProcess.setStatus(rightStatus);
                itemTrainingMaterialsProcessService.updateById(itemTrainingMaterialsProcess);

                log.info("[培训资料]商品ID:{} 培训资料ID:{} 流程ID:{} 流程状态修正为:{} -> {} 流程数据:{}",
                         itemTrainingMaterials.getItemId(),
                         itemTrainingMaterialsProcess.getId(),
                         itemTrainingMaterials.getId(),
                         status.getDesc(),
                         rightStatus.getDesc(),
                         processData);
            }
        }
    }

    private ItemTrainingMaterials getItemTrainingMaterials(Long itemId) {
        return itemTrainingMaterialsService.lambdaQuery()
                                           .eq(ItemTrainingMaterials::getItemId, itemId)
                                           .one();
    }

    @Override
    public ItemTrainingMaterials getMaterialsCreateIfNotExits(Long itemId) {
        ItemTrainingMaterials itemTrainingMaterials = getItemTrainingMaterials(itemId);
        if (itemTrainingMaterials == null) {
            itemTrainingMaterials = new ItemTrainingMaterials();
            itemTrainingMaterials.setItemId(itemId);

            final ItemTrainingMaterialsData data = new ItemTrainingMaterialsData();
            data.setDetails("");
            data.setAttachments(Lists.newArrayList());

            itemTrainingMaterials.setData(data);
            itemTrainingMaterials.setProcessId(0L);
            itemTrainingMaterials.setStatus(ItemTrainingMaterialsStatus.TO_BE_COMMIT);

            try {
                itemTrainingMaterialsService.save(itemTrainingMaterials);
            } catch (DuplicateKeyException e) {
                return getItemTrainingMaterials(itemId);
            }
        }
        return itemTrainingMaterials;
    }

    private List<HistoricTaskInstance> getProcessHisTasks(String processInstId) {
        return Optional.ofNullable(processInstId)
                       .filter(StringUtil::isNotEmpty)
                       .map(
                               id ->
                                       processEngine
                                               .getHistoryService()
                                               .createHistoricTaskInstanceQuery()
                                               .processInstanceId(processInstId)
                                               .taskWithoutDeleteReason()
                                               .list())
                       .orElseGet(Collections::emptyList);
    }

    @Override
    public ProcessInstance createProcessInstance(ItemTrainingMaterialsProcess itemTrainingMaterialsProcess) {
        final SingleResponse<ProcessInstance> response = processBizService.create(UserContext.getUserId(),
                config.getProcessDefKey(),
                ProcessBusinessType.ITEM_TRAINING_MATERIALS,
                itemTrainingMaterialsProcess.getId(),
                "商品培训资料流程:" + itemTrainingMaterialsProcess.getId(),
                itemTrainingMaterialsProcess.getCreatedUid().toString(),
                null);
        ResponseAssert.assertJust(response, ErrorCode.PROCESS_ERROR, "流程创建异常");
        return response.getData();
    }

    @Override
    public Response save(ItemTrainingMaterialsSaveCmd cmd) {
        final ItemTrainingMaterialsDrawer drawer = cmd.getDrawer();
        final ItemTrainingMaterials itemTrainingMaterials = getItemTrainingMaterials(drawer.getItemId());
        Objects.requireNonNull(itemTrainingMaterials, "未找到请求的培训资料记录");
        if (drawer.getMaterialsData() != null) {
            final ItemTrainingMaterialsData oldData = itemTrainingMaterials.getData();
            final ItemTrainingMaterialsData newData = ItemTrainingMaterialsAssembler.instance.copy(oldData);
            ItemTrainingMaterialsAssembler.instance.override(newData, drawer.getMaterialsData());
            itemTrainingMaterials.setData(newData);
            itemTrainingMaterials.setWikiContentId(newData.getWikiContentId());
            itemTrainingMaterialsService.updateById(itemTrainingMaterials);
            final Diff diff = DiffUtil.diff(oldData, newData);
            if (diff.hasChanges()) {
                logChange(itemTrainingMaterials, oldData, newData, diff);
            }
        }
        if (cmd.isSubmit()) {
            switch (itemTrainingMaterials.getStatus()) {

                case TO_BE_COMMIT: {
                    operateLogDomainService.addOperatorLog(UserContext.getUserId(),
                            OperateLogTarget.ITEM_TRAINING_MATERIALS,
                            itemTrainingMaterials.getId(),
                            "提交",
                            null);

                    //发起流程后状态自动切换
                    final ItemTrainingMaterialsProcess itemTrainingMaterialsProcess = startProcess(itemTrainingMaterials);

                    //发送[QC提醒]
                    ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> notifyQcProcess(itemTrainingMaterialsProcess));
                }
                break;
                case TO_BE_MODIFY: {
                    Assert.isTrue(NumberUtil.isPositive(itemTrainingMaterials.getProcessId()), "流程数据异常");
                    operateLogDomainService.addOperatorLog(UserContext.getUserId(),
                            OperateLogTarget.ITEM_TRAINING_MATERIALS,
                            itemTrainingMaterials.getId(),
                            "提交修改",
                            null);
                    final ItemTrainingMaterialsProcess materialsProcess = itemTrainingMaterialsProcessService.getById(
                            itemTrainingMaterials.getProcessId());
                    final List<Task> tasks = processBizService.tasksByProcInstId(materialsProcess.getProcessInstId());
                    for (Task task : tasks) {
                        processBizService.forceComplete(UserContext.getUserId(), task.getId(), "提交");
                    }
                }
                break;
                case FINISHED: {
                    //do nothing
                }
                break;

                default:
                    throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT,
                            "培训材料当前状态无法提交");
            }
        }
        return Response.buildSuccess();
    }

    private void logChange(ItemTrainingMaterials itemTrainingMaterials, ItemTrainingMaterialsData oldData, ItemTrainingMaterialsData newData, Diff diff) {
        final String simpleDiffLog = DiffUtil.getSimpleDiffLog(diff, ItemTrainingMaterialsData.class);
        final ArrayList<String> attachmentChangeLog = new ArrayList<>();
        diff.getChangesByType(CollectionChange.class)
            .stream()
            .filter(c -> "attachments".equals(c.getPropertyName()))
            .findAny().ifPresent(c -> {
                final Set<ItemTrainingMaterialsAttachment> oldAttachments = new HashSet<>(oldData.getAttachments());
                final Set<ItemTrainingMaterialsAttachment> newAttachments = new HashSet<>(newData.getAttachments());
                final List<ItemTrainingMaterialsAttachment> removedAttachments = oldAttachments
                        .stream()
                        .filter(v -> !newAttachments.contains(v))
                        .collect(Collectors.toList());
                final List<ItemTrainingMaterialsAttachment> addedAttachments = newAttachments
                        .stream()
                        .filter(v -> !oldAttachments.contains(v))
                        .collect(Collectors.toList());
                if (!removedAttachments.isEmpty()) {
                    for (ItemTrainingMaterialsAttachment removedAttachment : removedAttachments) {
                        attachmentChangeLog.add(String.format("删除“%s”", removedAttachment));
                    }
                }
                if (!addedAttachments.isEmpty()) {
                    for (ItemTrainingMaterialsAttachment addedAttachment : addedAttachments) {
                        attachmentChangeLog.add(String.format("上传“%s”", addedAttachment));
                    }
                }
            });
        final StringBuilder logBuilder = new StringBuilder(simpleDiffLog);
        if (!attachmentChangeLog.isEmpty()) {
            logBuilder.append("，").append(String.join("，", attachmentChangeLog));
        }
        operateLogDomainService.addOperatorLog(UserContext.getUserId(),
                OperateLogTarget.ITEM_TRAINING_MATERIALS,
                itemTrainingMaterials.getId(),
                logBuilder.toString(),
                diff);
    }

    private void updateStatus(ItemTrainingMaterials itemTrainingMaterials) {
        final ItemTrainingMaterials updateStatusObj = new ItemTrainingMaterials();
        updateStatusObj.setId(itemTrainingMaterials.getId());
        updateStatusObj.setStatus(itemTrainingMaterials.getStatus());
        itemTrainingMaterialsService.updateById(updateStatusObj);
    }

    private ItemTrainingMaterialsProcess startProcess(ItemTrainingMaterials itemTrainingMaterials) {
        final Long currentProcessId = itemTrainingMaterials.getProcessId();
        if (NumberUtil.isPositive(currentProcessId)) {
            final ItemTrainingMaterialsProcess currentProcess = new ItemTrainingMaterialsProcess();
            currentProcess.setId(currentProcessId);
            currentProcess.setStatus(ItemTrainingMaterialsStatus.CLOSED);
            itemTrainingMaterialsProcessService.updateById(currentProcess);
        }
        final ItemTrainingMaterialsProcess materialsProcess = new ItemTrainingMaterialsProcess();

        materialsProcess.setStatus(ItemTrainingMaterialsStatus.COMMITTED);
        materialsProcess.setItemTrainingMaterialsId(itemTrainingMaterials.getId());
        materialsProcess.setSubmitUid(UserContext.getUserId());
        materialsProcess.setSubmitAt(DateUtil.currentTime());

        final ItemTrainingMaterialsProcessData processData = new ItemTrainingMaterialsProcessData();
        materialsProcess.setProcessData(processData);
        itemTrainingMaterialsProcessService.save(materialsProcess);

        itemTrainingMaterials.setProcessId(materialsProcess.getId());
        itemTrainingMaterials.getData().setLegalMsg("");
        itemTrainingMaterials.getData().setQcMsg("");
        itemTrainingMaterialsService.updateById(itemTrainingMaterials);

        final ProcessInstance processInstance = createProcessInstance(materialsProcess);

        final String processInstanceId = processInstance.getId();
        materialsProcess.setProcessInstId(processInstanceId);
        materialsProcess.setStatus(ItemTrainingMaterialsStatus.COMMITTED);
        itemTrainingMaterialsProcessService.updateById(materialsProcess);

        processBizService.moveTo(UserContext.getUserId(), processInstanceId, "待QC审核");
        return materialsProcess;
    }

    @Override
    public Response urge(Long reminderUid, TaskUrgeCmd cmd) {
        final String reminderNickname =
                staffService.getStaffBrief(reminderUid).map(StaffBrief::getNickname).orElse("");
        final ItemTrainingMaterials itemTrainingMaterials = getItemTrainingMaterials(cmd.getBusinessId());

        final StaffBrief staffBrief =
                staffService
                        .getStaffBrief(cmd.getProcessorUid())
                        .filter(v -> StringUtil.isNotBlank(v.getQwUserId()))
                        .orElseThrow(
                                () ->
                                        ExceptionPlusFactory.bizException(
                                                ErrorCode.DATA_NOT_FOUND, "未查询到该用户的企业微信用户ID，无法催办"));
        final String title = String.format("%s 提醒您审核培训资料信息，请查看后处理！", reminderNickname);
        final String description = "您有一个新的待办任务，请尽快处理";
        final Long itemId = itemTrainingMaterials.getItemId();
        final String url =
                String.format(
                        refreshConfig.getDomain()
                                + "/operation-management/new-products/list?productId=%s&tabType=3",
                        itemId);

        final Optional<ItemDrawer> itemDrawer = itemDrawerService.getByItemId(itemId);
        final Item item = itemService.getById(itemId);

        // ${催办人花名}提醒您处理商品：${产品标准名} 的培训资料审核，请查看后处理！
        // 商品编码：${商品编码} 商品状态：${状态}
        // ${被通知人}
        // ${sys.domain}/operation-management/new-products/list?productId=${商品ID}&tabType=3
        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("催办人花名", reminderNickname);
        variables.put("被通知人", staffBrief.getQwUserId());
        variables.put("产品标准名", itemDrawer.map(ItemDrawer::getStandardName).orElse("?"));
        variables.put("商品编码", item.getCode());
        variables.put("状态", itemTrainingMaterials.getStatus().getDesc());
        variables.put("商品ID", itemId);

        QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.TRAINING_MATERIALS_URGING, variables);
        return Response.buildSuccess();
    }


    @Override
    public Response complete(TaskCompleteCmd cmd) {
        Task task =
                processBizService
                        .task(cmd.getTaskId())
                        .orElseThrow(
                                () -> ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND));

        final String processInstanceId = task.getProcessInstanceId();
        final List<ItemTrainingMaterialsProcess> itemTrainingMaterialsProcesses = itemTrainingMaterialsProcessService
                .lambdaQuery()
                .eq(ItemTrainingMaterialsProcess::getProcessInstId, processInstanceId)
                .list();
        if (!itemTrainingMaterialsProcesses.isEmpty()) {
            processBizService.forceComplete(UserContext.getUserId(), task.getId(), cmd.getComment());

            for (ItemTrainingMaterialsProcess itemTrainingMaterialsProcess : itemTrainingMaterialsProcesses) {
                final ItemTrainingMaterials itemTrainingMaterials = itemTrainingMaterialsService.getById(
                        itemTrainingMaterialsProcess.getItemTrainingMaterialsId());
                final ItemTrainingMaterialsProcessRecord processRecord = new ItemTrainingMaterialsProcessRecord();

                processRecord.setItemTrainingMaterialsId(itemTrainingMaterials.getId());
                processRecord.setNodeStatus(itemTrainingMaterials.getStatus());
                processRecord.setProcessId(itemTrainingMaterials.getProcessId());
                processRecord.setAssigneeUid(UserContext.getUserId());
                processRecord.setCompleteTime(DateUtil.currentTime());
                processRecord.setMsg(cmd.getComment());
                processRecord.setTaskInstId(cmd.getTaskId());
                processRecord.setAttachments(Optional.ofNullable(cmd.getAttachments()).orElseGet(Collections::emptyList));

                itemTrainingMaterialsProcessRecordService.save(processRecord);
            }
        }
        return Response.buildSuccess();
    }

    @Override
    public Response rollback(RollbackCmd cmd) {
        final Long itemId = cmd.getItemId();
        final ItemTrainingMaterials itemTrainingMaterials = getItemTrainingMaterials(itemId);
        Objects.requireNonNull(itemTrainingMaterials, "商品培训资料查询异常");

        final ItemTrainingMaterialsProcess itemTrainingMaterialsProcess = itemTrainingMaterialsProcessService.getById(
                itemTrainingMaterials.getProcessId());
        Objects.requireNonNull(itemTrainingMaterialsProcess, "商品培训资料流程查询异常");

        final Long itemTrainingMaterialsId = itemTrainingMaterialsProcess.getItemTrainingMaterialsId();

        final ItemTrainingMaterialsData data = itemTrainingMaterials.getData();
        if (ObjectUtil.defaultIfNull(data.getDisableRollback(), false)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "下个流程人编辑后不可以撤回");
        }

        final String processInstId = itemTrainingMaterialsProcess.getProcessInstId();
        final List<Task> tasks = processBizService.tasksByProcInstId(processInstId);
        if (tasks.isEmpty()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前流程状态异常，无法撤回");
        }
        final Task task = tasks.get(0);
        if (task.getClaimTime() != null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前事项已被经办人认领，无法撤回");
        }

        operateLogDomainService.addOperatorLog(
                UserContext.getUserId(),
                OperateLogTarget.ITEM_TRAINING_MATERIALS,
                itemTrainingMaterialsId,
                "流程撤回", null);

        final ItemTrainingMaterialsStatus status = itemTrainingMaterials.getStatus();
        final Response rollback;
        final String reason = Optional.ofNullable(cmd.getReason()).filter(StringUtil::isNotBlank).orElse("流程撤回");
        switch (status) {
            case TO_BE_LEGAL_REVIEW:
            case TO_BE_QC_REVIEW:
                rollback = processBizService.terminal(UserContext.getUserId(), processInstId, reason);
                break;
            case TO_BE_MODIFY:
                rollback = processBizService.moveTo(UserContext.getUserId(), processInstId, "待QC审核");
                break;
            default:
                rollback = Response.buildFailure(ErrorCode.OPERATION_REJECT.getCode(), "当前状态不允许撤回");
        }
        if (rollback.isSuccess()) {
            final ItemTrainingMaterialsProcessRecord processRecord = new ItemTrainingMaterialsProcessRecord();
            processRecord.setItemTrainingMaterialsId(itemTrainingMaterials.getId());
            processRecord.setNodeStatus(itemTrainingMaterials.getStatus());
            processRecord.setProcessId(itemTrainingMaterials.getProcessId());
            processRecord.setAssigneeUid(UserContext.getUserId());
            processRecord.setCompleteTime(DateUtil.currentTime());
            processRecord.setMsg(reason);
            processRecord.setTaskInstId(task.getId());
            processRecord.setAttachments(Collections.emptyList());
            itemTrainingMaterialsProcessRecordService.save(processRecord);
        }
        return rollback;
    }

    @Override
    public Response rollback2(RollbackCmd cmd) {
        final Long itemId = cmd.getItemId();
        final ItemTrainingMaterials itemTrainingMaterials = getItemTrainingMaterials(itemId);
        Objects.requireNonNull(itemTrainingMaterials, "商品培训资料查询异常");

        final ItemTrainingMaterialsProcess itemTrainingMaterialsProcess = itemTrainingMaterialsProcessService.getById(
                itemTrainingMaterials.getProcessId());
        Objects.requireNonNull(itemTrainingMaterialsProcess, "商品培训资料流程查询异常");

        final Long itemTrainingMaterialsId = itemTrainingMaterialsProcess.getItemTrainingMaterialsId();

        final String processInstId = itemTrainingMaterialsProcess.getProcessInstId();
        final List<Task> tasks = processBizService.tasksByProcInstId(processInstId);
        if (tasks.isEmpty()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前流程状态异常，无法撤回");
        }
        final Task task = tasks.get(0);

        operateLogDomainService.addOperatorLog(
                UserContext.getUserId(),
                OperateLogTarget.ITEM_TRAINING_MATERIALS,
                itemTrainingMaterialsId,
                "退回采购", null);

        final Response rollback = processBizService.terminal(UserContext.getUserId(), processInstId, cmd.getReason());
        if (rollback.isSuccess()) {
            final ItemTrainingMaterialsProcessRecord processRecord = new ItemTrainingMaterialsProcessRecord();
            processRecord.setItemTrainingMaterialsId(itemTrainingMaterials.getId());
            processRecord.setNodeStatus(itemTrainingMaterials.getStatus());
            processRecord.setProcessId(itemTrainingMaterials.getProcessId());
            processRecord.setAssigneeUid(UserContext.getUserId());
            processRecord.setCompleteTime(DateUtil.currentTime());
            processRecord.setMsg(cmd.getReason());
            processRecord.setTaskInstId(task.getId());
            processRecord.setAttachments(Collections.emptyList());
            itemTrainingMaterialsProcessRecordService.save(processRecord);

            noticeRollbackSubmitUser(itemId, itemTrainingMaterialsProcess, cmd.getReason());
        }
        return rollback;
    }

    private void noticeRollbackSubmitUser(Long itemId, ItemTrainingMaterialsProcess itemTrainingMaterialsProcess, String reason) {
        final Optional<Item> item = Optional.ofNullable(itemService.getById(itemId));
        final StaffBrief submitUser = StaffAssembler.INST.toStaffBrief(itemTrainingMaterialsProcess.getSubmitUid());
        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("商品编码", item.map(Item::getCode).orElse("?"));
        variables.put("商品ID", itemId);
        variables.put("负责人", UserContext.getNickName());
        variables.put("退回原因", reason);
        variables.put("采购负责人", getQwUserIds(Collections.singletonList(submitUser)));
        QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.TRAINING_MATERIALS_TERMINAL, variables);
    }

    @Override
    public Response terminal(TerminalCmd cmd) {
        final ItemTrainingMaterials itemTrainingMaterials = getItemTrainingMaterials(cmd.getItemId());
        Objects.requireNonNull(itemTrainingMaterials, "商品培训资料查询异常");

        Assert.isTrue(Arrays.asList(ItemTrainingMaterialsStatus.TO_BE_LEGAL_REVIEW,
                        ItemTrainingMaterialsStatus.TO_BE_QC_REVIEW,
                        ItemTrainingMaterialsStatus.TO_BE_MODIFY).contains(itemTrainingMaterials.getStatus()),
                "当前状态不允许终止");

        final ItemTrainingMaterialsProcess itemTrainingMaterialsProcess = itemTrainingMaterialsProcessService.getById(
                itemTrainingMaterials.getProcessId());
        Objects.requireNonNull(itemTrainingMaterialsProcess, "商品培训资料流程查询异常");

        final Long itemTrainingMaterialsId = itemTrainingMaterials.getId();
        final String processInstId = itemTrainingMaterialsProcess.getProcessInstId();
        final StringBuilder msg = new StringBuilder("流程终止");
        Optional.ofNullable(cmd.getRemark()).ifPresent(str -> msg.append("，").append(str));
        processBizService.terminal(UserContext.getUserId(), processInstId, msg.toString());

        operateLogDomainService.addOperatorLog(UserContext.getUserId(),
                OperateLogTarget.ITEM_TRAINING_MATERIALS,
                itemTrainingMaterialsId,
                msg.toString(),
                null);
        return Response.buildSuccess();
    }

    @Override
    public Response terminalBatch(TerminalBatchCmd cmd) {
        for (Long itemId : cmd.getItemIds()) {
            try {
                final Response response = terminal(new TerminalCmd().setItemId(itemId).setRemark(cmd.getRemark()));
                ResponseAssert.assertJust(response);
                log.error("批量终止培训资料流程，处理成功，itemId:{}", itemId);
            } catch (Exception e) {
                log.error("批量终止培训资料流程，处理异常，itemId:{}, err:{}", itemId, e.getMessage(), e);
            }
        }
        return Response.buildSuccess();
    }

    @Override
    public Response submitReview(Long userId, MaterialsSubmitReviewCmd cmd) {
        final Long itemId = cmd.getItemId();
        final ItemTrainingMaterials itemTrainingMaterials = getItemTrainingMaterials(itemId);
        Objects.requireNonNull(itemTrainingMaterials, "请求的培训资料记录不存在");

        if (itemTrainingMaterials.getStatus() != ItemTrainingMaterialsStatus.FINISHED
                && itemTrainingMaterials.getStatus() != ItemTrainingMaterialsStatus.TO_BE_MODIFY) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前状态无法提交复审");
        }
        operateLogDomainService.addOperatorLog(UserContext.getUserId(),
                OperateLogTarget.ITEM_TRAINING_MATERIALS,
                itemTrainingMaterials.getId(),
                "发起复审",
                null);
        final ItemTrainingMaterialsProcess itemTrainingMaterialsProcess = startProcess(itemTrainingMaterials);

        //发送[QC复审提醒]
        ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> notifySubmitReview(itemTrainingMaterialsProcess));
        return Response.buildSuccess();
    }

    @Override
    public MultiResponse<ItemTrainingMaterialsRecordVO> historyRecordsQuery(ItemTrainingMaterialsRecordQuery query) {
        final ItemTrainingMaterials itemTrainingMaterials = itemTrainingMaterialsService.getByItemId(query.getItemId());
        Assert.notNull(itemTrainingMaterials, "未找到请求的培训材料");

        final List<ItemTrainingMaterialsProcessRecord> list = itemTrainingMaterialsProcessRecordService
                .lambdaQuery()
                .eq(ItemTrainingMaterialsProcessRecord::getItemTrainingMaterialsId, itemTrainingMaterials.getId())
                .ne(ItemTrainingMaterialsProcessRecord::getCompleteTime, 0L)
                .orderByDesc(ItemTrainingMaterialsProcessRecord::getCreatedAt)
                .list();
        if (list.isEmpty()) {
            return MultiResponse.of(Collections.emptyList());
        }

        final ArrayList<ItemTrainingMaterialsRecordVO> results = new ArrayList<>();
        for (ItemTrainingMaterialsProcessRecord itemTrainingMaterialsProcessRecord : list) {
            final ItemTrainingMaterialsRecordVO itemTrainingMaterialsRecordVO = new ItemTrainingMaterialsRecordVO();
            itemTrainingMaterialsRecordVO.setContent(itemTrainingMaterialsProcessRecord.getMsg());
            itemTrainingMaterialsRecordVO.setTime(itemTrainingMaterialsProcessRecord.getCompleteTime());
            itemTrainingMaterialsRecordVO.setProcessUser(StaffAssembler.INST.toStaffBrief(
                    itemTrainingMaterialsProcessRecord.getAssigneeUid()));
            itemTrainingMaterialsRecordVO.setAttachments(ObjectUtil.defaultIfNull(itemTrainingMaterialsProcessRecord.getAttachments(),
                    Collections.emptyList()));

            results.add(itemTrainingMaterialsRecordVO);
        }

        return MultiResponse.of(results);
    }

    @Override
    public void notifyLegalProcess(ItemTrainingMaterialsProcess materialsProcess) {
        notifyLegalProcess(materialsProcess, false);
    }

    @Override
    public void notifyLegalProcess(ItemTrainingMaterialsProcess materialsProcess, boolean isReview) {
        final Long materialsId = materialsProcess.getItemTrainingMaterialsId();
        final ItemTrainingMaterials itemTrainingMaterials = itemTrainingMaterialsService.getById(materialsId);
        final Long itemId = itemTrainingMaterials.getItemId();
        final Optional<Item> item = Optional.ofNullable(itemService.getById(itemId));
        final Long submitUid = materialsProcess.getSubmitUid();
        final Optional<StaffBrief> submitUser = Optional.ofNullable(StaffAssembler.INST.toStaffBrief(submitUid));
        final Long submitAt = materialsProcess.getSubmitAt();
        final String submitAtFStr = DateUtil.format(submitAt);
        final Optional<ItemLaunchPlan> plan = Optional.ofNullable(itemLaunchPlanService.getPlanByItemId(itemId));
        final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo = newGoodsBizService.getNewGoodsPrincipalsInfo(itemId)
                                                                                .getData();

        final String title;
        if (isReview) {
            title = String.format("培训资料审核：%s %s %s %s 提交复审",
                    item.map(Item::getCode).orElse("?"),
                    item.map(Item::getName).orElse("?"),
                    submitUser.map(StaffBrief::getNickname).orElse("?"),
                    submitAtFStr
            );
        } else {
            title = String.format("培训资料审核：%s %s %s %s 已提交审核，请法务认领！",
                    item.map(Item::getCode).orElse("?"),
                    item.map(Item::getName).orElse("?"),
                    submitUser.map(StaffBrief::getNickname).orElse("?"),
                    submitAtFStr
            );
        }
        final String description = "上新日期" + plan.map(ItemLaunchPlan::getLaunchTime)
                                                    .map(DateUtil::formatDate)
                                                    .orElse("?");
        final String url =
                String.format(
                        refreshConfig.getDomain()
                                + "/operation-management/new-products/list?productId=%s&tabType=3",
                        itemTrainingMaterials.getItemId());
        final List<StaffBrief> legalCandidates = StaffAssembler.INST.longListToStaffBriefList(config.getLegalCandidates());

        final List<StaffBrief> receivers = !newGoodsPrincipalsInfo.getLegalUsers()
                                                                  .isEmpty() ? newGoodsPrincipalsInfo.getLegalUsers() : legalCandidates;
        final WechatMsg wechatMsg = new WechatMsg();
        wechatMsg.setTitle(title);
        wechatMsg.setContent(description);
        wechatMsg.setLink(url);
        wechatMsg.setRecipient(receivers.stream()
                                        .map(StaffBrief::getQwUserId)
                                        .filter(StringUtil::isNotBlank)
                                        .collect(
                                                Collectors.joining(",")));
        wechatMsg.setType(1);

        msgSender.send(wechatMsg);
    }

    @Override
    public void notifyQcProcess(ItemTrainingMaterialsProcess materialsProcess) {
        final Long materialsId = materialsProcess.getItemTrainingMaterialsId();
        final ItemTrainingMaterials itemTrainingMaterials = itemTrainingMaterialsService.getById(materialsId);
        final Long itemId = itemTrainingMaterials.getItemId();
        final Optional<Item> item = Optional.ofNullable(itemService.getById(itemId));
        final Long submitUid = materialsProcess.getSubmitUid();
        final Optional<StaffBrief> submitUser = Optional.ofNullable(StaffAssembler.INST.toStaffBrief(submitUid));
        final Long submitAt = materialsProcess.getSubmitAt();
        final String submitAtFStr = DateUtil.format(submitAt);
        final Optional<ItemLaunchPlan> plan = Optional.ofNullable(itemLaunchPlanService.getPlanByItemId(itemId));
        final String launchDate = plan.map(ItemLaunchPlan::getLaunchTime).map(DateUtil::formatDate).orElse("?");
        final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo = newGoodsBizService.getNewGoodsPrincipalsInfo(itemId)
                                                                                .getData();
        final List<StaffBrief> qcUsers = newGoodsPrincipalsInfo.getQcUsers();
        final List<StaffBrief> qcSupervisors = StaffAssembler.INST.longListToStaffBriefList(config.getQcSupervisors());
        final List<StaffBrief> qcSenders = !qcUsers.isEmpty() ? qcUsers : qcSupervisors;

        final String title = String.format("培训资料审核：%s %s 法务已审核完成！",
                item.map(Item::getCode).orElse("?"),
                item.map(Item::getName).orElse("?")
        );
        final String description = "上新日期" + launchDate;
        final String url =
                String.format(
                        refreshConfig.getDomain()
                                + "/operation-management/new-products/list?productId=%s&tabType=3",
                        itemTrainingMaterials.getItemId());

//        final WechatMsg wechatMsg = new WechatMsg();
//        wechatMsg.setTitle(title);
//        wechatMsg.setContent(description);
//        wechatMsg.setLink(url);
//        wechatMsg.setRecipient(qcSenders.stream().map(StaffBrief::getQwUserId).collect(Collectors.joining(",")));
//        wechatMsg.setType(1);
//
//        msgSender.send(wechatMsg);

        //培训资料审核：${商品编码} ${商品名称} ${提交人} ${提交时间} 已提交审核！
        //上新日期${上新计划日期}
        //${QC负责人}
        //${sys.domain}/operation-management/new-products/list?productId=${商品ID}&tabType=3
        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("商品编码", item.map(Item::getCode).orElse("?"));
        variables.put("商品名称", item.map(Item::getName).orElse("?"));
        variables.put("提交人", submitUser.map(StaffBrief::getNickname).orElse("?"));
        variables.put("提交时间", submitAtFStr);
        variables.put("上新计划日期", launchDate);
        variables.put("QC负责人", getQwUserIds(qcSenders));
        variables.put("商品ID", itemId);
        QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.TRAINING_MATERIALS_TO_QC, variables);
    }

    @Override
    public void notifySubmitReview(ItemTrainingMaterialsProcess materialsProcess) {
        final Long materialsId = materialsProcess.getItemTrainingMaterialsId();
        final ItemTrainingMaterials itemTrainingMaterials = itemTrainingMaterialsService.getById(materialsId);
        final Long itemId = itemTrainingMaterials.getItemId();
        final Optional<Item> item = Optional.ofNullable(itemService.getById(itemId));
        final Long submitUid = materialsProcess.getSubmitUid();
        final Optional<StaffBrief> submitUser = Optional.ofNullable(StaffAssembler.INST.toStaffBrief(submitUid));
        final Long submitAt = materialsProcess.getSubmitAt();
        final String submitAtFStr = DateUtil.format(submitAt);
        final Optional<ItemLaunchPlan> plan = Optional.ofNullable(itemLaunchPlanService.getPlanByItemId(itemId));
        final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo = newGoodsBizService.getNewGoodsPrincipalsInfo(itemId)
                                                                                .getData();
        final List<StaffBrief> qcUsers = newGoodsPrincipalsInfo.getQcUsers();
        final List<StaffBrief> qcSupervisors = StaffAssembler.INST.longListToStaffBriefList(config.getQcSupervisors());
        final List<StaffBrief> qcSenders = !qcUsers.isEmpty() ? qcUsers : qcSupervisors;

        final String title = String.format("培训资料审核：%s %s 法务已审核完成！",
                item.map(Item::getCode).orElse("?"),
                item.map(Item::getName).orElse("?")
        );
        final String launchDateStr = plan.map(ItemLaunchPlan::getLaunchTime)
                             .map(DateUtil::formatDate)
                             .orElse("?");
        final String description = "上新日期" + launchDateStr;
        final String url =
                String.format(
                        refreshConfig.getDomain()
                                + "/operation-management/new-products/list?productId=%s&tabType=3",
                        itemTrainingMaterials.getItemId());

//        final WechatMsg wechatMsg = new WechatMsg();
//        wechatMsg.setTitle(title);
//        wechatMsg.setContent(description);
//        wechatMsg.setLink(url);
//        wechatMsg.setRecipient(qcSenders.stream().map(StaffBrief::getQwUserId).collect(Collectors.joining(",")));
//        wechatMsg.setType(1);
//
//        msgSender.send(wechatMsg);

        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("商品编码", item.map(Item::getCode).orElse("?"));
        variables.put("商品名称", item.map(Item::getName).orElse("?"));
        variables.put("发起复审人", submitUser.map(StaffBrief::getNickname).orElse("?"));
        variables.put("发起复审时间", submitAtFStr);
        variables.put("QC负责人", getQwUserIds(qcSenders));
        variables.put("商品ID", itemId);
        variables.put("上新计划日期", launchDateStr);
        QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.TRAINING_MATERIALS_REVIEW, variables);
    }

    @Override
    public void notifyModifyProcess(ItemTrainingMaterialsProcess materialsProcess) {
        final Long materialsId = materialsProcess.getItemTrainingMaterialsId();
        final ItemTrainingMaterials itemTrainingMaterials = itemTrainingMaterialsService.getById(materialsId);
        final Long itemId = itemTrainingMaterials.getItemId();
        final Optional<Item> item = Optional.ofNullable(itemService.getById(itemId));
        final Long submitUid = materialsProcess.getSubmitUid();
        final Optional<StaffBrief> submitUser = Optional.ofNullable(StaffAssembler.INST.toStaffBrief(submitUid));
        final Long submitAt = materialsProcess.getSubmitAt();
        final String submitAtFStr = DateUtil.format(submitAt);
        final Optional<ItemLaunchPlan> plan = Optional.ofNullable(itemLaunchPlanService.getPlanByItemId(itemId));
        final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo = newGoodsBizService.getNewGoodsPrincipalsInfo(itemId)
                                                                                .getData();
        final List<StaffBrief> csCandidates = StaffAssembler.INST.longListToStaffBriefList(config.getCsCandidates());
        final List<StaffBrief> buyerUsers = newGoodsPrincipalsInfo.getBuyerUsers();

        final String title = String.format("培训资料审核：%s %s QC已审核完成！",
                item.map(Item::getCode).orElse("?"),
                item.map(Item::getName).orElse("?")
        );
        final String launchDateStr = plan.map(ItemLaunchPlan::getLaunchTime)
                             .map(DateUtil::formatDate)
                             .orElse("?");
        final String description = "上新日期" + launchDateStr;
        final String url =
                String.format(
                        refreshConfig.getDomain()
                                + "/operation-management/new-products/list?productId=%s&tabType=3",
                        itemTrainingMaterials.getItemId());



        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("商品编码", item.map(Item::getCode).orElse("?"));
        variables.put("商品名称", item.map(Item::getName).orElse("?"));
        variables.put("指定客服", getQwUserIds(csCandidates));
        variables.put("采购负责人", getQwUserIds(buyerUsers));
        variables.put("商品ID", itemId);
        variables.put("上新计划日期", launchDateStr);
        QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.TRAINING_MATERIALS_TO_UPDATE, variables);
    }

    private static String getQwUserIds(List<StaffBrief> csCandidates) {
        if (CollectionUtils.isEmpty(csCandidates)) {
            return "";
        }
        return csCandidates.stream().map(StaffBrief::getQwUserId).collect(Collectors.joining(","));
    }

    @Override
    public void notifyModifyProcessed(ItemTrainingMaterialsProcess materialsProcess) {
        final Long materialsId = materialsProcess.getItemTrainingMaterialsId();
        final ItemTrainingMaterials itemTrainingMaterials = itemTrainingMaterialsService.getById(materialsId);
        final Long itemId = itemTrainingMaterials.getItemId();
        final Optional<Item> item = Optional.ofNullable(itemService.getById(itemId));
        final Long submitUid = materialsProcess.getSubmitUid();
        final Optional<StaffBrief> submitUser = Optional.ofNullable(StaffAssembler.INST.toStaffBrief(submitUid));
        final Long submitAt = materialsProcess.getSubmitAt();
        final String submitAtFStr = DateUtil.format(submitAt);
        final Optional<ItemLaunchPlan> plan = Optional.ofNullable(itemLaunchPlanService.getPlanByItemId(itemId));
        final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo = newGoodsBizService.getNewGoodsPrincipalsInfo(itemId)
                                                                                .getData();
        final List<StaffBrief> csCandidates = StaffAssembler.INST.longListToStaffBriefList(config.getCsCandidates());
        final ArrayList<StaffBrief> receivers = new ArrayList<>();
        receivers.addAll(csCandidates);
        receivers.addAll(newGoodsPrincipalsInfo.getBuyerUsers());

        final String title = String.format("培训资料审核：%s %s 已修改完成！",
                item.map(Item::getCode).orElse("?"),
                item.map(Item::getName).orElse("?")
        );
        final String launchDateStr = plan.map(ItemLaunchPlan::getLaunchTime)
                             .map(DateUtil::formatDate)
                             .orElse("?");
        final String description = "上新日期" + launchDateStr;
        final String url =
                String.format(
                        refreshConfig.getDomain()
                                + "/operation-management/new-products/list?productId=%s&tabType=3",
                        itemTrainingMaterials.getItemId());

//        final WechatMsg wechatMsg = new WechatMsg();
//        wechatMsg.setTitle(title);
//        wechatMsg.setContent(description);
//        wechatMsg.setLink(url);
//        wechatMsg.setRecipient(receivers.stream().map(StaffBrief::getQwUserId).collect(Collectors.joining(",")));
//        wechatMsg.setType(1);
//
//        msgSender.send(wechatMsg);

        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("商品编码", item.map(Item::getCode).orElse("?"));
        variables.put("商品名称", item.map(Item::getName).orElse("?"));
        variables.put("上新计划日期", launchDateStr);
        variables.put("采购负责人", getQwUserIds(newGoodsPrincipalsInfo.getBuyerUsers()));
        variables.put("商品ID", itemId);
        QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.TRAINING_MATERIALS_COMPLETED, variables);
    }

    @Override
    public Response getOperateLogs(Long itemId) {
        final ItemTrainingMaterials itemTrainingMaterials = getMaterialsCreateIfNotExits(itemId);
        return MultiResponse.of(operateLogDomainService.getOperateLogs(OperateLogTarget.ITEM_TRAINING_MATERIALS,
                itemTrainingMaterials.getId()));
    }

    @Override
    public void predicateComplete(Long userId, Long itemId, Predicate<Task> predicate, String comment) {
        final ItemTrainingMaterials itemTrainingMaterials = getItemTrainingMaterials(itemId);
        Assert.notNull(itemTrainingMaterials, "商品没有培训资料");
        final ItemTrainingMaterialsProcess itemTrainingMaterialsProcess = Optional
                .ofNullable(itemTrainingMaterials.getProcessId())
                .filter(NumberUtil::isPositive)
                .map(processId -> itemTrainingMaterialsProcessService.getById(processId))
                .orElseThrow(() -> ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未找到培训资料流程记录"));
        final String processInstId = itemTrainingMaterialsProcess.getProcessInstId();
        final HashSet<String> proceed = new HashSet<>();
        while (true) {
            final List<Task> tasks = processBizService.tasksByProcInstId(processInstId)
                                                      .stream()
                                                      .filter(v -> !proceed.contains(v.getId()))
                                                      .collect(Collectors.toList());
            if (tasks.isEmpty()) {
                return;
            }
            for (Task task : tasks) {
                proceed.add(task.getId());
                if (predicate.test(task)) {
                    processBizService.forceComplete(userId, task.getId(), comment);
                }
            }
        }
    }

    @Override
    public void syncContentToWiki(Long itemId) {
        final ItemTrainingMaterials itemTrainingMaterials = getItemTrainingMaterials(itemId);
        Assert.notNull(itemTrainingMaterials, "商品没有培训资料");
        final Long wikiContentId = itemTrainingMaterials.getWikiContentId();
        Assert.notNull(wikiContentId, "未登记 WIKI ID");
        final HashMap<String, String> params = new HashMap<>();
        params.put("expand", "version");
        final String contentBody = wikiClient.getContentBody(wikiProperties.getToken(), wikiContentId, params);
        Assert.notNull(contentBody, "查询WIKI页面详情异常");
        final ObjectNode contentBodyJsonNode = JsonUtil.parse(contentBody, ObjectNode.class);
        final String title = Optional.ofNullable(contentBodyJsonNode)
                                 .map(v -> v.get("title"))
                                 .map(JsonNode::asText)
                                 .orElse("");
        final Integer versionNum = Optional.ofNullable(contentBodyJsonNode)
                                        .map(v -> v.get("version"))
                                        .map(v -> v.get("number"))
                                        .map(JsonNode::asInt)
                                        .orElse(1);
        log.info("同步培训资料到WIKI itemId={} -> {}, 获取到 标题:{}，版本号:{}", itemId, wikiContentId, title, versionNum);
        final HashMap<String, Object> request = new HashMap<>();
        final HashMap<String, Object> body = new HashMap<>();
        final HashMap<String, String> storage = new HashMap<>();
        final HashMap<String, Object> version = new HashMap<>();
        request.put("body", body);
        request.put("title", title);
        request.put("type", "page");
        request.put("version", version);

        body.put("storage", storage);
        final ItemTrainingMaterialsData data = itemTrainingMaterials.getData();
        final String details = data.getDetails();
        final Document doc = Jsoup.parse(details);
        doc.outputSettings().syntax(Document.OutputSettings.Syntax.xml);
        storage.put("value", doc.body().html());
        storage.put("representation", "storage");
        version.put("number", versionNum + 1);

        try {
            wikiClient.putContentBody(wikiProperties.getToken(), wikiContentId, request);
            log.info("同步培训资料到WIKI itemId={} -> {}，同步成功", itemId, wikiContentId);
        } catch (Exception e) {
            Throwable e1 = e;
            if (e instanceof HystrixRuntimeException && e.getCause() != null) {
                e1 = e.getCause();
            }
            log.error("同步培训资料到WIKI itemId={} -> {} 同步异常，请求参数:{}", itemId, wikiContentId, JsonUtil.toJson(request), e1);
            throw ExceptionPlusFactory.bizException(ErrorCode.WIKI_ERROR, "同步培训资料到WIKI异常");
        }
    }

}
