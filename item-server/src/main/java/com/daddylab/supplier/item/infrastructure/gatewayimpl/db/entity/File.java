package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.URLUtil;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.daddylab.supplier.item.domain.fileStore.constant.FileType;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * <p>
 * 文件信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class File implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 文件类型 0 未知 1 图片 2 视频 ...
     */
    private FileType type;

    /**
     * 文件访问路径
     */
    private String path;

    /**
     * 文件名（不含后缀）
     */
    private String name;

    /**
     * 文件后缀
     */
    private String ext;

    /**
     * 文件完整名称（包含后缀）
     */
    public String getFullName() {
        return name != null ? name + Optional.ofNullable(ext).filter(StringUtil::isNotEmpty)
                .map(v -> "." + v).orElse("") : null;
    }

    public void setFullName(String fileName) {
        final String fileNamePrefix = FileNameUtil.getPrefix(fileName);
        final String fileNameSuffix = FileNameUtil.getSuffix(fileName);
        setName(fileNamePrefix);
        setExt(fileNameSuffix);
    }

    /**
     * MIME
     */
    private String mime;

    /**
     * 文件访问链接
     */
    private String url;

    public void setUrl(String url) {
        this.url = URLUtil.decode(url);
        this.path = URLUtil.getDecodedPath(URLUtil.url(url));
    }

    /**
     * 文件md5
     */
    private String md5;

    /**
     * 文件大小（byte）
     */
    private Integer size;

    /**
     * 图片/视频宽度（像素）
     */
    private Integer width;

    /**
     * 图片/视频高度（像素）
     */
    private Integer height;

    /**
     * 图片比例（宽度/高度）保留两位小数
     */
    public BigDecimal getProportion() {
        if (NumberUtil.isPositive(width) && NumberUtil.isPositive(height)) {
            return NumberUtil.div(width, height, 2);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 视频时长（秒）
     */
    private Integer duration;

    /**
     * 视频封面（文件ID）
     */
    private Long coverId;

    /**
     * 视频封面图片URL
     */
    private String coverUrl;

    /**
     * 文件元数据JSON
     */
    private String meta;

    /**
     * 文件是否上传至又拍云
     */
    private Boolean upyun;

    /**
     * 文件是否上传至阿里云OSS
     */
    private Boolean aliOss;

    /**
     * 是否私有文件
     */
    private boolean isPrivate;


}
