package com.daddylab.supplier.item.infrastructure.third.kuaishou.domain.query;

import lombok.Data;

/**
 * <AUTHOR>
 * @class ItemPageQuery.java
 * @description 描述类的作用
 * @date 2024-02-29 10:43
 */
@Data
public class KsItemPageQuery {

    /**
     * 快手商品id(同kwaiItemId)
     */
    private Long itemId;
    /**
     * 外部商品Id.如kwaiItemId未传，但relItemId传了，将会按照appkey+relItemId查询
     */
    private Long relItemId;
    /**
     * 商品状态。不传kwaiItemId和relItemId时才会生效 1-正常 目前接口只支持1-正常（商品状态1是指未删除的商品）
     */
    private Integer itemStatus;
    /**
     * 商品类型。不传kwaiItemId和relItemId时才会生效 1-自建商品 2闪电购商品
     */
    private Integer itemType;
    /**
     * 0 < 数值 < totalPage
     */
    private Integer pageNumber;
    /**
     * 推荐值20，范围为10～100，超过100或不传默认取100
     */
    private Integer pageSize;
    /**
     * 上下架条件，目前仅支持查询1（上架）商品
     */
    private Integer onOfflineStatus;

    public static KsItemPageQuery of(Integer pageNo, Integer pageSize) {
        KsItemPageQuery itemPageQuery = new KsItemPageQuery();
        itemPageQuery.setPageNumber(pageNo);
        itemPageQuery.setPageSize(pageSize);
        return itemPageQuery;
    }
}
