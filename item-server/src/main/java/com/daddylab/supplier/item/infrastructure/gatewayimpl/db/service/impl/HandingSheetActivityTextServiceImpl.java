package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.controller.handingsheet.dto.HandingSheetActivityTextPageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetActivityText;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.HandingSheetActivityTextMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.HandingSheetActivityTextService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 盘货表活动内容 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@Service
public class HandingSheetActivityTextServiceImpl extends DaddyServiceImpl<HandingSheetActivityTextMapper, HandingSheetActivityText> implements HandingSheetActivityTextService {
    @Autowired
    private HandingSheetActivityTextMapper handingSheetActivityTextMapper;

    @Override
    public IPage<HandingSheetActivityText> selectPageList(Page<HandingSheetActivityText> page, QueryWrapper<HandingSheetActivityText> queryWrapper) {
        return handingSheetActivityTextMapper.selectPageList(page, queryWrapper);
    }

    @Override
    public long selectPageListCount(Wrapper<HandingSheetActivityText> queryWrapper) {
        return handingSheetActivityTextMapper.selectPageListCount(queryWrapper);
    }
}
