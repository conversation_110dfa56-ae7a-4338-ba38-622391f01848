package com.daddylab.supplier.item.infrastructure.submail.lib.base;

import java.util.Map;

/**
 * 接口定义了一个方法，所有类要发送请求数据必须继承他，这些类通 常被设计为通信模式或工具，无论我们选择哪种模式或工具，我们都可以发送请求。
 *
 * @Auther: <EMAIL>
 * @version: 1.0.0
 * @Date: 2021/04/16/4:01 下午
 */
public interface ISender {

    /**
     * 发送请求数据
     *
     * @param data{@link HashMap}
     * @return 如果发送成功, 返回true，发生错误,返回false。
     */
    String send(Map<String, Object> data);

    String xsend(Map<String, Object> data);
}
