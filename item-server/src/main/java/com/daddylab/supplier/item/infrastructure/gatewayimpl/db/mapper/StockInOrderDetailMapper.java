package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderDetailVo;
import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderQuery;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderStockQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrderDetail;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 采购入库明细mapper
 * <AUTHOR>
 * @date 2022/3/24 17:37
 **/
@Repository
public interface StockInOrderDetailMapper extends DaddyBaseMapper<StockInOrderDetail> {
    /**
     * 根据入库单ID查询入库单明细列表
     * <AUTHOR>
     * @date 2022/3/30 18:48
     * @param id
     * @return java.util.List<com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrderDetail>
     **/
    List<StockInOrderDetail> selectByStockInOrderDetailId(Long id);

    /**
     * 根据页面筛选条件查询入库单明细列表
     * <AUTHOR>
     * @date 2022/3/30 18:48
     * @param stockInOrderQuery
     * @return java.util.List<com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrderDetail>
     **/
    List<StockInOrderDetail> selectByItemSkuCodeAndItemName(StockInOrderQuery stockInOrderQuery);

    /**
     * 根据id更新明细删除状态
     * <AUTHOR>
     * @date 2022/4/1 10:35
     * @param id
     **/
    void delByStockInOrderDetailId(Long id);

    int batchUpdateStockInNum(@Param("map") Map<String, Integer> stockInNum);

    /**
     * 查询入库商品的库存数量
     *
     * @param stockQuery   查询对象
     * @return  入库数量
     */
    Integer getStockInOrderSkuStock(StockOutOrderStockQuery stockQuery);
    /**
     * 根据旺店通回传入库单号和sku更新入库单明细
     * <AUTHOR>
     * @date 2022/5/19 19:33
     * @param id
     * @param specNo
     * @param intValue
     * @throws
     * @return int
     **/
    void updateDetailInfoByInOrderIdAndSku(@Param("id") Long id, @Param("specNo") String specNo, @Param("intValue") int intValue);


    /**
     * 查询采购单下入库的商品
     *
      * @param purchaseOrderId
     * @param itemSkuCode
     * @return
     */
    List<StockInOrderDetail> getStockInOrderDetail(@Param("purchaseOrderId") Long purchaseOrderId, @Param("itemSkuCode") String itemSkuCode);

    /**
     * 查询采购单下入库的商品(分组)
     *
     * @param query
     * @return
     */
    List<StockInOrderDetailVo> getDetailByPurchaseOrderId(@Param("param") ComposeSkuPageQuery query);

    /**
     * 查询采购单下入库的商品总数(分组)
     *
     * @param purchaseOrderId
     * @return
     */
    Integer getDetailCountByPurchaseOrderId(Long purchaseOrderId);
}
