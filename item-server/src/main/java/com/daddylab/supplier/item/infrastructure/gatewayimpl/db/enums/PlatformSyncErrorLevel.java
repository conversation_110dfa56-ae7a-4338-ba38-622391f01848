package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> up
 * @date 2022年09月13日 3:22 PM
 */
@AllArgsConstructor
@Getter
public enum PlatformSyncErrorLevel implements IEnum<Integer> {

    /**
     *
     */
    WARN(2, "警告，不影响整体流程"),

    ERROR(1, "报警，影响业务流程了"),
    NONE(0, "无错误");

    private Integer value;
    private String desc;
}
