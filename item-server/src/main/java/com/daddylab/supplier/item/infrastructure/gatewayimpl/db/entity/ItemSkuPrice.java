package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 商品sku价格
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemSkuPrice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * skuID,|| combinationItemId
     * 兼容下组合装的情况
     */
    private Long skuId;

    /**
     * skuCode || combinationCode
     * 兼容下组合装的情况
     */
    private String skuCode;


    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 时间范围 开始时间
     */
    private Long startTime;

    /**
     * 时间范围 结束时间
     */
    private Long endTime;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 0 (SKU和组合装)成本价。默认值  SKU和组合装的区分 根据 skuCode 是否MU开头。
     * 1 (SKU和组合装)销售价。
     *
     * 2 SKU合同销售价
     * 3 SKU平台佣金
     *
     * 4 组合装合同销售价
     * 5 平台佣金
     */
    private Integer type;


}
