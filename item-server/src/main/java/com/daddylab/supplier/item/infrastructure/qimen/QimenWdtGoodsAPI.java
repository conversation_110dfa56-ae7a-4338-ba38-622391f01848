package com.daddylab.supplier.item.infrastructure.qimen;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.qimencloud.api.QimenCloudClient;
import com.qimencloud.api.scene3ldsmu02o9.request.WdtGoodsApigoodsSearchRequest;
import com.qimencloud.api.scene3ldsmu02o9.response.WdtGoodsApigoodsSearchResponse;
import com.taobao.api.ApiException;

/**
 * <AUTHOR>
 * @since 2022/1/17
 */
public class QimenWdtGoodsAPI extends QimenWdtAPIBase {

    public QimenWdtGoodsAPI(QimenCloudClient qimenClient,
            QimenConfig qimenConfig,
            WdtConfig wdtConfig, int configIndex) {
        super(qimenClient, qimenConfig, wdtConfig, configIndex);
    }

    /**
     * 通过奇门接口查询旺店通平台商品数据
     *
     * @param params    旺店通请求参数封装
     * @param pageIndex pageIndex
     * @param pageSize  pageSize
     * @return 奇门返回的数据结构封装
     */
    public WdtGoodsApigoodsSearchResponse platformGoodsSearch(
            WdtGoodsApigoodsSearchRequest.Params params,
            long pageIndex,
            long pageSize) throws ApiException {
        return execute(WdtGoodsApigoodsSearchRequest.class, params, pageIndex, pageSize);
    }
}
