package com.daddylab.supplier.item.application.drawer.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.daddylab.supplier.item.application.drawer.ItemDrawerAuditBizService;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.item.gateway.ItemProcurementGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAudit;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAuditTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchModuleAdvice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemProcurement;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerModuleAuditService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerModuleAuditTaskService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemLaunchModuleAdviceService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.itemDrawer.*;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerAuditTaskState;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerModuleId;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemLaunchProcessNodeId;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/11/14
 */
@Service
@RequiredArgsConstructor
public class ItemDrawerAuditBizServiceImpl implements ItemDrawerAuditBizService {

    public static final ImmutableMap<ItemAuditStatus, ItemLaunchProcessNodeId> STATUS_TO_NODE_MAP =
            ImmutableMap.<ItemAuditStatus, ItemLaunchProcessNodeId>builder()
                    .put(ItemAuditStatus.NONE, ItemLaunchProcessNodeId.START)
                    .put(ItemAuditStatus.WAIT_LEGAL_AUDIT, ItemLaunchProcessNodeId.LEGAL)
                    .put(ItemAuditStatus.WAIT_QC_AUDIT, ItemLaunchProcessNodeId.QC)
                    .put(ItemAuditStatus.FINISHED, ItemLaunchProcessNodeId.END)
                    .build();
    private final IItemDrawerModuleAuditService itemDrawerModuleAuditService;
    private final IItemDrawerModuleAuditTaskService itemDrawerModuleAuditTaskService;
    private final IItemLaunchModuleAdviceService itemLaunchModuleAdviceService;
    private final ItemProcurementGateway itemProcurementGateway;

    private final ItemLaunchProcessDefConfig processDefConfig;

    private final OperateLogGateway operateLogGateway;

    @Autowired
    private NewGoodsBizService newGoodsBizService;

    @Override
    public List<ItemDrawerModuleAuditTask> getHistoryTasks(
            ItemAuditType type, Long liveVerbalTrickId, Long uid, Long itemId, int round) {
        LambdaQueryChainWrapper<ItemDrawerModuleAuditTask> wrapper = itemDrawerModuleAuditTaskService
                .lambdaQuery()
                .eq(ItemDrawerModuleAuditTask::getItemId, itemId)
                .eq(ItemDrawerModuleAuditTask::getType, type)
                .eq(ItemDrawerModuleAuditTask::getAuditStatus, ItemDrawerAuditTaskState.DONE)
                .eq(ItemDrawerModuleAuditTask::getRound, round);
        if (Objects.nonNull(liveVerbalTrickId) && liveVerbalTrickId != 0) {
            return wrapper.eq(ItemDrawerModuleAuditTask::getLiveVerbalTrickId, liveVerbalTrickId).list();
        } else {
            return wrapper.isNull(ItemDrawerModuleAuditTask::getLiveVerbalTrickId).list();
        }
    }

    @NonNull
    private static ItemDrawerModuleAuditTaskVO getItemDrawerModuleAuditTaskVO(
            Long uid, ItemDrawerModuleAuditTask v) {
        final ItemDrawerModuleAuditTaskVO itemDrawerModuleAuditTaskVO =
                new ItemDrawerModuleAuditTaskVO();
        itemDrawerModuleAuditTaskVO.setId(v.getId().toString());
        itemDrawerModuleAuditTaskVO.setClaimable(0 == v.getProcessorId());
        itemDrawerModuleAuditTaskVO.setOwner(Objects.equals(uid, v.getProcessorId()));
        itemDrawerModuleAuditTaskVO.setPass(v.isPass());
        itemDrawerModuleAuditTaskVO.setNode(v.getNode());
        return itemDrawerModuleAuditTaskVO;
    }


    @Override
    public ItemDrawerModuleAuditTaskVO getActiveTask(ItemAuditType type, Long liveVerbalTrickId, Long uid, Long itemId) {
        return getActiveTask0(type, liveVerbalTrickId, uid, itemId, null);
    }

    @Override
    public ItemDrawerModuleAuditTaskVO getActiveTask(ItemAuditType type, Long uid, Long itemId) {
        return getActiveTask0(type, null, uid, itemId, null);
    }

    @Override
    public ItemDrawerModuleAuditTaskVO getActiveTask(ItemAuditType type, Long liveVerbalTrickId, Long uid, Long itemId, ItemLaunchProcessNodeId nodeId) {
        return getActiveTask0(type, liveVerbalTrickId, uid, itemId, nodeId);
    }

    @Override
    public ItemDrawerModuleAuditTaskVO getActiveTask(ItemAuditType type, Long uid, Long itemId, ItemLaunchProcessNodeId nodeId) {
        return getActiveTask0(type, null, uid, itemId, nodeId);
    }

    private ItemDrawerModuleAuditTaskVO getActiveTask0(ItemAuditType type, Long liveVerbalTrickId, Long uid, Long itemId,
                                                       ItemLaunchProcessNodeId nodeId) {
        final List<ItemDrawerModuleAuditTask> list =
                itemDrawerModuleAuditTaskService
                        .lambdaQuery()
                        .eq(ItemDrawerModuleAuditTask::getItemId, itemId)
                        .eq(ItemDrawerModuleAuditTask::getType, type)
                        .and(q -> {
                            if (nodeId != null) {
                                q.eq(ItemDrawerModuleAuditTask::getNode, nodeId.getValue());
                            } else {
                                q.in(ItemDrawerModuleAuditTask::getNode,
                                        Lists.newArrayList(ItemLaunchProcessNodeId.LEGAL, ItemLaunchProcessNodeId.QC));
                            }
                        })
                        .eq(
                                ItemDrawerModuleAuditTask::getAuditStatus,
                                ItemDrawerAuditTaskState.PENDING)
                        .and(
                                sub ->
                                        sub.eq(ItemDrawerModuleAuditTask::getProcessorId, 0)
                                           .or()
                                           .eq(ItemDrawerModuleAuditTask::getProcessorId, uid))
                        .eq(Objects.nonNull(liveVerbalTrickId),
                                ItemDrawerModuleAuditTask::getLiveVerbalTrickId,
                                liveVerbalTrickId)
                        .list();
        return list.stream()
                   .max(Comparator.comparing(ItemDrawerModuleAuditTask::getId))
                   .map(v -> getItemDrawerModuleAuditTaskVO(uid, v))
                   .orElse(null);
    }

    @Override
    public Long createProcess(ItemAuditType type, Long uid, Long itemId, ItemDrawerModuleId... moduleIds) {
        return createProcess0(type, null, uid, itemId, moduleIds);
    }

    @Override
    public Long createProcess(ItemAuditType type, Long liveVerbalTrickId, Long uid, Long itemId, ItemDrawerModuleId... moduleIds) {
        return createProcess0(type, liveVerbalTrickId, uid, itemId, moduleIds);
    }

    @Override
    public void createProcess(CreateProcessCmd createProcessCmd) {
        final ItemDrawerModuleId[] moduleIds = createProcessCmd.getModuleIds();
        final ItemAuditType type = createProcessCmd.getType();
        final Long liveVerbalTrickId = createProcessCmd.getLiveVerbalTrickId();
        final Long itemId = createProcessCmd.getItemId();
        final Long uid = createProcessCmd.getUid();
        if (moduleIds.length == 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "模块无变更，无需发起审核流程");
        }
        final ItemDrawerModuleAudit itemDrawerModuleAudit =
                itemDrawerModuleAuditService.getItemDrawerModuleAuditCreateIfNotExits(type, liveVerbalTrickId, itemId);
        itemDrawerModuleAudit.setImgInfo(
                ArrayUtil.contains(moduleIds, ItemDrawerModuleId.IMG_INFO));
        itemDrawerModuleAudit.setTextAndAttr(
                ArrayUtil.contains(moduleIds, ItemDrawerModuleId.TEXT_AND_ATTR));
        itemDrawerModuleAudit.setLiveVerbalTrick(
                ArrayUtil.contains(moduleIds, ItemDrawerModuleId.LIVE_VERBAL_TRICK));
        itemDrawerModuleAudit.setDetails(ArrayUtil.contains(moduleIds, ItemDrawerModuleId.DETAILS));
        itemDrawerModuleAudit.setRound(itemDrawerModuleAudit.getRound() + 1);
        ItemAuditStatus auditStatus;
        switch (createProcessCmd.getStartNodeId()) {
            case LEGAL:
                auditStatus = ItemAuditStatus.WAIT_LEGAL_AUDIT;
                break;
            case QC:
                auditStatus = ItemAuditStatus.WAIT_QC_AUDIT;
                break;
            case MODIFY:
                auditStatus = ItemAuditStatus.WAIT_MODIFY;
                break;
            default:
                throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "不支持的审核节点");
        }
        itemDrawerModuleAudit.setAuditStatus(auditStatus);
        itemDrawerModuleAudit.setRecognitionStatus(false);
        itemDrawerModuleAudit.setType(type);
        itemDrawerModuleAudit.setSubmitAt(DateUtil.currentTime());
        itemDrawerModuleAudit.setSubmitUid(uid);
        itemDrawerModuleAudit.setLiveVerbalTrickId(liveVerbalTrickId);
        itemDrawerModuleAuditService.updateById(itemDrawerModuleAudit);

        final ItemDrawerModuleAuditTask itemDrawerModuleAuditTask = new ItemDrawerModuleAuditTask();
        itemDrawerModuleAuditTask.setItemId(itemId);
        itemDrawerModuleAuditTask.setNode(createProcessCmd.getStartNodeId().getValue());
        itemDrawerModuleAuditTask.setRound(itemDrawerModuleAudit.getRound());
        itemDrawerModuleAuditTask.setAuditStatus(ItemDrawerAuditTaskState.PENDING);
        itemDrawerModuleAuditTask.setProcessorId(0L);
        itemDrawerModuleAuditTask.setType(type);
        itemDrawerModuleAuditTask.setLiveVerbalTrickId(liveVerbalTrickId);
//        if (itemDrawerModuleAuditTask.getNode().equals(ItemLaunchProcessNodeId.LEGAL.getValue())) {
//            try {
//                final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo = newGoodsBizService.getNewGoodsPrincipalsInfo(itemId)
//                                                                                        .getData();
//                if (newGoodsPrincipalsInfo != null && !newGoodsPrincipalsInfo.getLegalUsers().isEmpty()) {
//                    itemDrawerModuleAuditTask.setProcessorId(newGoodsPrincipalsInfo.getLegalUsers().get(0).getUserId());
//                }
//            } catch (Exception ignored) {
//            }
//        }
        itemDrawerModuleAuditTaskService.save(itemDrawerModuleAuditTask);

        if (createProcessCmd.isTriggerEvent()) {
            EventBusUtil.post(
                    ItemLaunchAuditProcessStartEvent.of(
                            type,
                            itemId,
                            itemDrawerModuleAudit.getRound(),
                            UserContext.getUserId(),
                            moduleIds));
        }
    }

    private Long createProcess0(
            ItemAuditType type, Long liveVerbalTrickId, Long uid, Long itemId, ItemDrawerModuleId... moduleIds) {
        if (moduleIds.length == 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "模块无变更，无需发起审核流程");
        }
        final ItemDrawerModuleAudit itemDrawerModuleAudit =
                itemDrawerModuleAuditService.getItemDrawerModuleAuditCreateIfNotExits(type, liveVerbalTrickId, itemId);
        itemDrawerModuleAudit.setImgInfo(
                ArrayUtil.contains(moduleIds, ItemDrawerModuleId.IMG_INFO));
        itemDrawerModuleAudit.setTextAndAttr(
                ArrayUtil.contains(moduleIds, ItemDrawerModuleId.TEXT_AND_ATTR));
        itemDrawerModuleAudit.setLiveVerbalTrick(
                ArrayUtil.contains(moduleIds, ItemDrawerModuleId.LIVE_VERBAL_TRICK));
        itemDrawerModuleAudit.setDetails(ArrayUtil.contains(moduleIds, ItemDrawerModuleId.DETAILS));
        itemDrawerModuleAudit.setRound(itemDrawerModuleAudit.getRound() + 1);
        itemDrawerModuleAudit.setAuditStatus(ItemAuditStatus.WAIT_LEGAL_AUDIT);
        itemDrawerModuleAudit.setRecognitionStatus(false);
        itemDrawerModuleAudit.setType(type);
        itemDrawerModuleAudit.setSubmitAt(DateUtil.currentTime());
        itemDrawerModuleAudit.setSubmitUid(uid);
        itemDrawerModuleAudit.setLiveVerbalTrickId(liveVerbalTrickId);
        itemDrawerModuleAuditService.updateById(itemDrawerModuleAudit);

        final ItemDrawerModuleAuditTask itemDrawerModuleAuditTask = new ItemDrawerModuleAuditTask();
        itemDrawerModuleAuditTask.setItemId(itemId);
        itemDrawerModuleAuditTask.setNode(ItemLaunchProcessNodeId.LEGAL.getValue());
        itemDrawerModuleAuditTask.setRound(itemDrawerModuleAudit.getRound());
        itemDrawerModuleAuditTask.setAuditStatus(ItemDrawerAuditTaskState.PENDING);
        itemDrawerModuleAuditTask.setProcessorId(0L);
        itemDrawerModuleAuditTask.setType(type);
        itemDrawerModuleAuditTask.setLiveVerbalTrickId(liveVerbalTrickId);

//        final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo = newGoodsBizService.getNewGoodsPrincipalsInfo(itemId)
//                                                                                .getData();
//        if (newGoodsPrincipalsInfo != null && !newGoodsPrincipalsInfo.getLegalUsers().isEmpty()) {
//            itemDrawerModuleAuditTask.setProcessorId(newGoodsPrincipalsInfo.getLegalUsers().get(0).getUserId());
//        }
        itemDrawerModuleAuditTaskService.save(itemDrawerModuleAuditTask);

        EventBusUtil.post(
                ItemLaunchAuditProcessStartEvent.of(
                        type,
                        itemId,
                        itemDrawerModuleAudit.getRound(),
                        UserContext.getUserId(),
                        moduleIds));
        return itemDrawerModuleAudit.getId();
    }

    @Override
    public void claim(Long uid, Long taskId) {
        final ItemDrawerModuleAuditTask task = itemDrawerModuleAuditTaskService.getById(taskId);
        if (task.getProcessorId() != 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "任务已被认领");
        }
        if (!processDefConfig.getLegalIds().contains(uid)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "仅法务组可认领");
        }
        task.setProcessorId(uid);
        itemDrawerModuleAuditTaskService.updateById(task);
        EventBusUtil.post(
                ItemLaunchAuditTaskClaimEvent.of(
                        task.getType(),
                        task.getItemId(),
                        task.getId(),
                        task.getRound(),
                        IEnum.getEnumByValue(ItemLaunchProcessNodeId.class, task.getNode()),
                        uid));
    }

    @Override
    public void temporarilySaveQcAdvice(Long taskId, List<ItemDrawerModuleModifyAdviceForm> advices) {
        final ItemDrawerModuleAuditTask task = itemDrawerModuleAuditTaskService.getById(taskId);
        Assert.notNull(task, "taskId非法");
        Long itemId = task.getItemId();
        List<ItemDrawerModuleAudit> list = itemDrawerModuleAuditService.lambdaQuery()
                .eq(ItemDrawerModuleAudit::getItemId, itemId)
                .eq(ItemDrawerModuleAudit::getType, ItemAuditType.ITEM_MATERIAL)
                .orderByDesc(ItemDrawerModuleAudit::getId)
                .last("limit 1").list();
        ItemDrawerModuleAudit itemDrawerModuleAudit = list.get(0);
        Integer round = itemDrawerModuleAudit.getRound();
        final ItemLaunchProcessNodeId nodeId = IEnum.getEnumByValue(ItemLaunchProcessNodeId.class, task.getNode());

        itemLaunchModuleAdviceService.lambdaUpdate()
                .eq(ItemLaunchModuleAdvice::getRound, round)
                .eq(ItemLaunchModuleAdvice::getItemId, itemId)
                .eq(ItemLaunchModuleAdvice::getTemporary, 1)
                .remove();

        List<ItemLaunchModuleAdvice> addList = new ArrayList<>();
        for (ItemDrawerModuleModifyAdviceForm advice : advices) {
            ItemLaunchModuleAdvice itemLaunchModuleAdvice = new ItemLaunchModuleAdvice();
            itemLaunchModuleAdvice.setItemId(itemId);
            itemLaunchModuleAdvice.setRound(round);
            itemLaunchModuleAdvice.setNode(nodeId);
            itemLaunchModuleAdvice.setModule(advice.getModule());
            itemLaunchModuleAdvice.setAdvice(Optional.ofNullable(advice.getAdvice()).orElse(""));
            itemLaunchModuleAdvice.setType(advice.getType());
            itemLaunchModuleAdvice.setTemporary(1);
            addList.add(itemLaunchModuleAdvice);
        }
        itemLaunchModuleAdviceService.saveBatch(addList);
    }

    @Override
    public List<ItemDrawerModuleAdviceVO> getQcTemporarilyAdvice(Long itemId) {
        List<ItemDrawerModuleAudit> list = itemDrawerModuleAuditService.lambdaQuery()
                .eq(ItemDrawerModuleAudit::getItemId, itemId)
                .eq(ItemDrawerModuleAudit::getType, ItemAuditType.ITEM_MATERIAL)
                .orderByDesc(ItemDrawerModuleAudit::getId)
                .last("limit 1").list();
        if (list.isEmpty()) {
            return Collections.emptyList();
        }
        ItemDrawerModuleAudit itemDrawerModuleAudit = list.get(0);
        Integer round = itemDrawerModuleAudit.getRound();

        return itemLaunchModuleAdviceService.lambdaQuery()
                .eq(ItemLaunchModuleAdvice::getRound, round)
                .eq(ItemLaunchModuleAdvice::getItemId, itemId)
                .eq(ItemLaunchModuleAdvice::getTemporary, 1)
                .list()
                .stream().map(val -> {
                    ItemDrawerModuleAdviceVO form = new ItemDrawerModuleAdviceVO();
                    form.setModuleId(val.getModule());
                    form.setAdvice(val.getAdvice());
                    form.setType(val.getType());
                    return form;
                }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void complete(
            Long uid, Long taskId, List<ItemDrawerModuleModifyAdviceForm> advices, boolean pass) {
        complete0(uid, taskId, null, advices, DateUtil.currentTime(), pass);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void complete(Long uid, Long taskId, List<ItemDrawerModuleModifyAdviceForm> advices, Long auditAt, boolean pass) {
        complete0(uid, taskId, null, advices, auditAt, pass);

    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void complete(Long uid, Long taskId, Long liveVerbalTrickId, List<ItemDrawerModuleModifyAdviceForm> advices, Long auditAt, boolean pass) {
        complete0(uid, taskId, liveVerbalTrickId, advices, auditAt, pass);
    }

    public void complete0(
            Long uid,
            Long taskId,
            Long liveVerbalTrickId,
            List<ItemDrawerModuleModifyAdviceForm> advices,
            Long auditAt,
            boolean pass) {
        final ItemDrawerModuleAuditTask task = itemDrawerModuleAuditTaskService.getById(taskId);
        if (null == task || task.getAuditStatus() != ItemDrawerAuditTaskState.PENDING) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "任务状态已发生变更，请刷新后重试");
        }
        if (!Objects.equals(task.getProcessorId(), uid)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "您不是当前任务的处理人");
        }
        final Long itemId = task.getItemId();
        final ItemAuditType type = task.getType();
        final ItemDrawerModuleAudit itemDrawerModuleAudit =
                itemDrawerModuleAuditService
                        .getItemDrawerModuleAudit(type, liveVerbalTrickId, itemId)
                        .orElseThrow(
                                () ->
                                        ExceptionPlusFactory.bizException(
                                                ErrorCode.OPERATION_REJECT, "流程获取异常"));
        boolean isNewVersion = Objects.nonNull(liveVerbalTrickId) && liveVerbalTrickId != 0;
        final ItemLaunchProcessNodeId nodeId =
                IEnum.getEnumByValue(ItemLaunchProcessNodeId.class, task.getNode());
        int round = itemDrawerModuleAudit.getRound();
        int nextRound = itemDrawerModuleAudit.getRound();
        task.setAuditStatus(ItemDrawerAuditTaskState.DONE);
        task.setAuditAt(auditAt);
        task.setPass(pass);
        itemDrawerModuleAuditTaskService.updateById(task);

        ItemAuditStatus currentStatus;
        ItemAuditStatus updateStatus;
        ItemLaunchProcessNodeId nextNodeId = null;
        Optional<ItemDrawerModuleAuditTask> nextTaskOptional = Optional.empty();

        // 如果是法务审核则进入下一个审核节点
        if (Objects.equals(task.getNode(), ItemLaunchProcessNodeId.LEGAL.getValue())) {
            nextNodeId = ItemLaunchProcessNodeId.QC;
            currentStatus = ItemAuditStatus.WAIT_LEGAL_AUDIT;
            updateStatus = ItemAuditStatus.WAIT_QC_AUDIT;
            updateAuditStatus(type, liveVerbalTrickId, itemId, task.getRound(), currentStatus, updateStatus);
            ItemDrawerModuleAuditTask nextTask = new ItemDrawerModuleAuditTask();
            nextTask.setItemId(task.getItemId());
            nextTask.setNode(ItemLaunchProcessNodeId.QC.getValue());
            nextTask.setRound(task.getRound());
            nextTask.setAuditStatus(ItemDrawerAuditTaskState.PENDING);
            nextTask.setType(task.getType());
            nextTask.setReAudit(task.isReAudit());
            if (isNewVersion) {
                nextTask.setLiveVerbalTrickId(liveVerbalTrickId);
            }
            final ItemProcurement itemProcurement =
                    itemProcurementGateway.getProcurementByItemId(task.getItemId());
            Objects.requireNonNull(itemProcurement, "商品数据异常，无法找到QC负责人信息");
            // QC负责人
            final Long qcId =
                    Optional.of(itemProcurement)
                            .map(ItemProcurement::getQcIds)
                            .filter(StringUtil::isNotBlank)
                            .map(
                                    v ->
                                            StringUtil.splitTrim(v, ",").stream()
                                                    .map(Long::parseLong)
                                                    .collect(Collectors.toList()))
                            .flatMap(qcIds -> qcIds.stream().findFirst())
                            .orElseThrow(
                                    () ->
                                            ExceptionPlusFactory.bizException(
                                                    ErrorCode.DATA_NOT_FOUND,
                                                    "商品数据异常，无法找到QC负责人信息"));
            nextTask.setProcessorId(qcId);
            itemDrawerModuleAuditTaskService.save(nextTask);
            nextTaskOptional = Optional.of(nextTask);
        }
        // 如果是QC审核完成则流程结束
        else if (Objects.equals(task.getNode(), ItemLaunchProcessNodeId.QC.getValue())) {
            currentStatus = ItemAuditStatus.WAIT_QC_AUDIT;
            final List<ItemDrawerModuleAuditTask> historyTasks =
                    getHistoryTasks(type, liveVerbalTrickId, uid, itemId, round);
            //历史任务全部【通过】则流程结束
            if (historyTasks.stream().allMatch(ItemDrawerModuleAuditTask::isPass)) {
                updateStatus = ItemAuditStatus.FINISHED;
            } else {
                //否则进入复审流程【待修改】
                updateStatus = ItemAuditStatus.WAIT_MODIFY;
                //创建【待修改】任务
                ItemDrawerModuleAuditTask nextTask = new ItemDrawerModuleAuditTask();
                nextTask.setItemId(task.getItemId());
                nextTask.setNode(ItemLaunchProcessNodeId.MODIFY.getValue());
                nextTask.setRound(round);
                nextTask.setAuditStatus(ItemDrawerAuditTaskState.PENDING);
                nextTask.setType(task.getType());
                nextTask.setReAudit(task.isReAudit());
                if (isNewVersion) {
                    nextTask.setLiveVerbalTrickId(task.getLiveVerbalTrickId());
                }
                itemDrawerModuleAuditTaskService.save(nextTask);
                nextTaskOptional = Optional.of(nextTask);
            }
            updateAuditStatus(type, liveVerbalTrickId, itemId, round, currentStatus, updateStatus);
        } else if (Objects.equals(task.getNode(), ItemLaunchProcessNodeId.MODIFY.getValue())) {
            currentStatus = ItemAuditStatus.WAIT_MODIFY;
            updateStatus = ItemAuditStatus.WAIT_LEGAL_AUDIT;

            //审核周期+1
            nextRound++;

            // 复审，创建法务审核任务
            final ItemDrawerModuleAuditTask itemDrawerModuleAuditTask =
                    new ItemDrawerModuleAuditTask();
            itemDrawerModuleAuditTask.setItemId(itemId);
            itemDrawerModuleAuditTask.setNode(ItemLaunchProcessNodeId.LEGAL.getValue());
            itemDrawerModuleAuditTask.setRound(nextRound);
            itemDrawerModuleAuditTask.setAuditStatus(ItemDrawerAuditTaskState.PENDING);
            itemDrawerModuleAuditTask.setProcessorId(0L);
            itemDrawerModuleAuditTask.setType(type);
            itemDrawerModuleAuditTask.setReAudit(true);
            if (isNewVersion) {
                itemDrawerModuleAuditTask.setLiveVerbalTrickId(liveVerbalTrickId);
            }
            itemDrawerModuleAuditTaskService.save(itemDrawerModuleAuditTask);

            // 更新审核状态到【待法务审核】
            updateAuditStatus(type, liveVerbalTrickId, itemId, round, currentStatus, updateStatus, nextRound);
        } else {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "流程异常");
        }
        final Long nextTaskId = nextTaskOptional.map(ItemDrawerModuleAuditTask::getId).orElse(0L);
        final Long nextProcessorId =
                nextTaskOptional.map(ItemDrawerModuleAuditTask::getProcessorId).orElse(0L);

        List<ItemLaunchModuleAdvice> advicePoList = Lists.newArrayList();
        for (ItemDrawerModuleModifyAdviceForm advice : advices) {
            final ItemLaunchModuleAdvice itemLaunchModuleAdvice = new ItemLaunchModuleAdvice();
            itemLaunchModuleAdvice.setItemId(itemId);
            itemLaunchModuleAdvice.setRound(task.getRound());
            itemLaunchModuleAdvice.setNode(nodeId);
            itemLaunchModuleAdvice.setModule(advice.getModule());
            itemLaunchModuleAdvice.setAdvice(Optional.ofNullable(advice.getAdvice()).orElse(""));
            itemLaunchModuleAdvice.setPass(pass);
            if (advice.getModule() == ItemDrawerModuleId.TEXT_AND_ATTR
                    && StringUtil.isEmpty(advice.getType())) {
                advice.setType("文案建议");
            }
            if (isNewVersion) {
                itemLaunchModuleAdvice.setLiveVerbalTrickId(liveVerbalTrickId);
            }
            itemLaunchModuleAdvice.setType(advice.getType());
            advicePoList.add(itemLaunchModuleAdvice);
        }
        itemLaunchModuleAdviceService.saveBatch(advicePoList);

        final List<ItemDrawerModuleId> auditModuleIds = Lists.newArrayList();
        if (itemDrawerModuleAudit.getImgInfo()) {
            auditModuleIds.add(ItemDrawerModuleId.IMG_INFO);
        }
        if (itemDrawerModuleAudit.getTextAndAttr()) {
            auditModuleIds.add(ItemDrawerModuleId.TEXT_AND_ATTR);
        }
        if (itemDrawerModuleAudit.getLiveVerbalTrick()) {
            auditModuleIds.add(ItemDrawerModuleId.LIVE_VERBAL_TRICK);
        }
        if (itemDrawerModuleAudit.getDetails()) {
            auditModuleIds.add(ItemDrawerModuleId.DETAILS);
        }
        EventBusUtil.post(
                new ItemLaunchAuditStatusChangeEvent(
                        task.getType(),
                        itemId,
                        task.getId(),
                        nextTaskId,
                        round,
                        nextRound,
                        nodeId,
                        nextNodeId,
                        currentStatus,
                        updateStatus,
                        task.getCreatedAt(),
                        auditAt,
                        task.getProcessorId(),
                        nextProcessorId,
                        auditModuleIds,
                        pass,
                        liveVerbalTrickId), true);
    }

    private void cleanHistoryTasks(Long itemId, ItemAuditType type, Integer round) {
        final List<ItemDrawerModuleAuditTask> historyTasks =
                itemDrawerModuleAuditTaskService
                        .lambdaQuery()
                        .eq(ItemDrawerModuleAuditTask::getItemId, itemId)
                        .eq(ItemDrawerModuleAuditTask::getType, type)
                        .eq(ItemDrawerModuleAuditTask::getRound, round)
                        .list();
        itemDrawerModuleAuditTaskService.removeByIdsWithTime(
                historyTasks.stream()
                        .map(ItemDrawerModuleAuditTask::getId)
                        .collect(Collectors.toSet()));
    }

    @Override
    public void terminal(ItemAuditType type, Long liveVerbalTrickId, Long uid, Long itemId) {
        terminal0(type, liveVerbalTrickId, uid, itemId);
    }

    @Override
    public void terminal(ItemAuditType type, Long uid, Long itemId) {
        terminal0(type, null, uid, itemId);
    }

    public void terminal0(ItemAuditType type, Long liveVerbalTrickId, Long uid, Long itemId) {
        final ItemDrawerModuleAudit itemDrawerModuleAudit =
                itemDrawerModuleAuditService.getItemDrawerModuleAuditCreateIfNotExits(type, liveVerbalTrickId, itemId);
        // 流程未开始或者已审批完成不允许终止
        if (itemDrawerModuleAudit.getAuditStatus() == ItemAuditStatus.NONE
                || itemDrawerModuleAudit.getAuditStatus() == ItemAuditStatus.FINISHED) {
            return;
        }

        // 重置审核状态
        final Integer round = itemDrawerModuleAudit.getRound();
        itemDrawerModuleAudit.setAuditStatus(ItemAuditStatus.NONE);
        itemDrawerModuleAudit.setImgInfo(false);
        itemDrawerModuleAudit.setTextAndAttr(false);
        itemDrawerModuleAudit.setLiveVerbalTrick(false);
        itemDrawerModuleAudit.setDetails(false);
        itemDrawerModuleAudit.setRound(round);
        itemDrawerModuleAuditService.updateById(itemDrawerModuleAudit);

        // 删除审批任务
        itemDrawerModuleAuditTaskService
                .lambdaUpdate()
                .eq(ItemDrawerModuleAuditTask::getRound, round)
                .eq(ItemDrawerModuleAuditTask::getItemId, itemDrawerModuleAudit.getItemId())
                .eq(ItemDrawerModuleAuditTask::getType, itemDrawerModuleAudit.getType())
                .eq(Objects.nonNull(liveVerbalTrickId) && liveVerbalTrickId != 0,
                        ItemDrawerModuleAuditTask::getLiveVerbalTrickId, liveVerbalTrickId)
                .remove();

        EventBusUtil.post(ItemLaunchAuditTerminalEvent.of(type, itemId, round));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void rollback(ItemAuditType type, Long itemId, ItemAuditStatus auditRollbackTo) {
        rollback0(type, null, itemId, auditRollbackTo);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void rollback(ItemAuditType type, Long liveVerbalTrickId, Long itemId, ItemAuditStatus auditRollbackTo) {
        rollback0(type, liveVerbalTrickId, itemId, auditRollbackTo);
    }

    public void rollback0(ItemAuditType type, Long liveVerbalTrickId, Long itemId, ItemAuditStatus auditRollbackTo) {
        if (!Arrays.asList(ItemAuditStatus.WAIT_LEGAL_AUDIT, ItemAuditStatus.WAIT_QC_AUDIT)
                .contains(auditRollbackTo)) {
            throw ExceptionPlusFactory.bizException(
                    ErrorCode.OPERATION_REJECT, "无法退回到【" + auditRollbackTo.getDesc() + "】");
        }
        final ItemDrawerModuleAudit itemDrawerModuleAudit =
                itemDrawerModuleAuditService.getItemDrawerModuleAuditCreateIfNotExits(type, liveVerbalTrickId, itemId);
        final ItemAuditStatus auditStatus = itemDrawerModuleAudit.getAuditStatus();
        if (auditStatus.ordinal() <= auditRollbackTo.ordinal()) {
            throw ExceptionPlusFactory.bizException(
                    ErrorCode.OPERATION_REJECT,
                    "当前审核状态为【"
                            + auditStatus.getDesc()
                            + "】"
                            + "无法回退到【"
                            + auditRollbackTo.getDesc()
                            + "】");
        }
        final Integer round = itemDrawerModuleAudit.getRound();
        updateAuditStatus(type, liveVerbalTrickId, itemId, round, auditStatus, auditRollbackTo);
        final Supplier<LambdaUpdateChainWrapper<ItemDrawerModuleAuditTask>>
                updateAuditTaskWrapperSupplier =
                () ->
                        itemDrawerModuleAuditTaskService
                                .lambdaUpdate()
                                .eq(ItemDrawerModuleAuditTask::getItemId, itemId)
                                .eq(ItemDrawerModuleAuditTask::getType, type)
                                .eq(ItemDrawerModuleAuditTask::getRound, round)
                                .eq(Objects.nonNull(liveVerbalTrickId) && liveVerbalTrickId != 0,
                                        ItemDrawerModuleAuditTask::getLiveVerbalTrickId, liveVerbalTrickId);
        if (auditRollbackTo == ItemAuditStatus.WAIT_QC_AUDIT) {
            updateAuditTaskWrapperSupplier
                    .get()
                    .eq(ItemDrawerModuleAuditTask::getNode, ItemLaunchProcessNodeId.QC)
                    .set(
                            ItemDrawerModuleAuditTask::getAuditStatus,
                            ItemDrawerAuditTaskState.PENDING)
                    .update();
        } else {
            updateAuditTaskWrapperSupplier
                    .get()
                    .eq(ItemDrawerModuleAuditTask::getNode, ItemLaunchProcessNodeId.QC)
                    .remove();
            updateAuditTaskWrapperSupplier
                    .get()
                    .eq(ItemDrawerModuleAuditTask::getNode, ItemLaunchProcessNodeId.LEGAL)
                    .set(ItemDrawerModuleAuditTask::getAuditStatus, ItemDrawerAuditTaskState.PENDING)
                    .set(ItemDrawerModuleAuditTask::getProcessorId, 0L)
                    .update();
        }
        EventBusUtil.post(
                ItemLaunchAuditRollbackEvent.of(
                        type,
                        itemId,
                        round,
                        STATUS_TO_NODE_MAP.get(auditStatus),
                        STATUS_TO_NODE_MAP.get(auditRollbackTo)));
    }

    @Override
    public void qcChange(Long itemId, Long qcId) {
        final List<ItemDrawerModuleAuditTask> itemDrawerModulePendingAuditTasks =
                itemDrawerModuleAuditTaskService.getItemDrawerModulePendingAuditTasks(
                        itemId, ItemLaunchProcessNodeId.QC);
        if (itemDrawerModulePendingAuditTasks.isEmpty()) {
            return;
        }
        final Long qcBefore =
                itemDrawerModulePendingAuditTasks.stream()
                        .map(ItemDrawerModuleAuditTask::getProcessorId)
                        .findAny()
                        .orElse(0L);
        itemDrawerModuleAuditTaskService
                .lambdaUpdate()
                .set(ItemDrawerModuleAuditTask::getProcessorId, qcId)
                .in(
                        ItemDrawerModuleAuditTask::getId,
                        itemDrawerModulePendingAuditTasks.stream()
                                .map(ItemDrawerModuleAuditTask::getId)
                                .collect(Collectors.toList()))
                .update();
        operateLogGateway.addOperatorLog(
                0L,
                OperateLogTarget.NEW_GOODS_SPU,
                itemId,
                String.format("同步后端商品QC变动，商品上新QC审核经办人自动变更，从【%s】变更为【%s】", qcBefore, qcId),
                null);
    }

    @Override
    public void qcChange(Long itemId, String qcId) {
        Optional.ofNullable(qcId)
                .filter(StringUtil::isNotBlank)
                .flatMap(
                        v ->
                                CollectionUtil.reverse(StringUtil.splitTrim(v, ",")).stream()
                                        .filter(StringUtil::isNotBlank)
                                        .map(Long::parseLong)
                                        .findFirst())
                .ifPresent(
                        v -> {
                            qcChange(itemId, v);
                        });
    }

    @Override
    public void closeTask(ItemAuditType type, Long itemId) {
        final List<ItemDrawerModuleAuditTask> itemDrawerModulePendingAuditTasks =
            itemDrawerModuleAuditTaskService.getItemDrawerModulePendingAuditTasks(itemId, type);
        if (itemDrawerModulePendingAuditTasks.isEmpty()) {
            return;
        }
        itemDrawerModuleAuditTaskService.removeByIdsWithTime(
                itemDrawerModulePendingAuditTasks.stream()
                        .map(ItemDrawerModuleAuditTask::getId)
                        .collect(Collectors.toSet()));
    }

    private void updateAuditStatus(
            ItemAuditType type,
            Long liveVerbalTrickId,
            Long itemId,
            Integer round,
            ItemAuditStatus expectStatus,
            ItemAuditStatus updateStatus) {
        updateAuditStatus(type, liveVerbalTrickId, itemId, round, expectStatus, updateStatus, null);
    }

    private void updateAuditStatus(
            ItemAuditType type,
            Long liveVerbalTrickId,
            Long itemId,
            Integer round,
            ItemAuditStatus expectStatus,
            ItemAuditStatus updateStatus, Integer nextRound) {
        if (!itemDrawerModuleAuditService
                .lambdaUpdate()
                .set(ItemDrawerModuleAudit::getAuditStatus, updateStatus.getValue())
                .set(nextRound != null, ItemDrawerModuleAudit::getRound, nextRound)
                .eq(ItemDrawerModuleAudit::getAuditStatus, expectStatus.getValue())
                .eq(ItemDrawerModuleAudit::getItemId, itemId)
                .eq(ItemDrawerModuleAudit::getType, type)
                .eq(ItemDrawerModuleAudit::getRound, round)
                .eq(Objects.nonNull(liveVerbalTrickId) && liveVerbalTrickId != 0, ItemDrawerModuleAudit::getLiveVerbalTrickId, liveVerbalTrickId)
                .update()) {
            throw ExceptionPlusFactory.bizException(
                    ErrorCode.OPERATION_REJECT, "流程状态已发生改变，请刷新页面后重试");
        }
    }
}
