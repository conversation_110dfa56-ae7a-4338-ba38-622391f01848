package com.daddylab.supplier.item.domain.shop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2024年03月11日 11:06 AM
 */
@Data
@ApiModel("店铺-商品维度库存设置")
public class ShopGoodsInventoryDto {

//    @ApiModelProperty("商品纬度库存设置明细Id。新建没有值，编辑有值")
//    private Long id;

    @ApiModelProperty("库存设置明细sku编码")
    private String skuNo;

    @ApiModelProperty("库存设置明细spu编码")
    private String spuNo;

    @ApiModelProperty("库存设置明细归属仓库")
    private String warehouseNo;

    @ApiModelProperty("库存设置明细sku库存占比")
    private Integer inventoryRatio;

}
