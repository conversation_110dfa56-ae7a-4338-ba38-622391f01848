package com.daddylab.supplier.item.controller.orders;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.order.OrderService;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizService;
import com.daddylab.supplier.item.types.order.OrderBaseInfo;
import com.daddylab.supplier.item.types.order.OrderDetail;
import com.daddylab.supplier.item.types.order.OrderQuery;
import com.daddylab.supplier.item.types.order.PurchaseOrderTabVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @ClassName OtherPayController.java
 * @description 其他应付单
 * @createTime 2022年03月24日 15:36:00
 */
@Slf4j
@Api(value = "订单管理API", tags = "订单管理API")
@RestController
@RequestMapping("/order")
public class OrderController {

    @Autowired
    PurchaseOrderBizService purchaseOrderBizService;
    @Autowired
    private OrderService orderService;

    @ApiOperation(value = "订单查询")
    @PostMapping("/orderQuery")
    public PageResponse<OrderBaseInfo> orderQuery(@RequestBody OrderQuery orderQuery) {
        return orderService.orderQuery(orderQuery);
    }

    @ApiOperation(value = "订单详情")
    @GetMapping("/orderDetail")
    public SingleResponse<OrderDetail> orderDetail(
            @ApiParam(value = "旺店通订单号", required = true) @RequestParam String wdtOrderNo) {
        return orderService.orderDetail(wdtOrderNo);
    }

    @ApiOperation(value = "采购订单明细关联的订单详情")
    @GetMapping("/purchaseOrderTab")
    @ApiImplicitParam(name = "id", dataType = "long", value = "采购单id")
    public PageResponse<PurchaseOrderTabVO> purchaseOrderTab(Long id) {
        return orderService.purchaseOrderTab(id);
    }


    //2.2.0 订单计算相关API


}
