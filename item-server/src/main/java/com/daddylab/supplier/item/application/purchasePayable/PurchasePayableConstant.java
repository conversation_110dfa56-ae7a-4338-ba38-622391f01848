package com.daddylab.supplier.item.application.purchasePayable;

import java.math.BigDecimal;

/**
 * 采购应付常量
 */
public class PurchasePayableConstant {

    // 采购应付单号公共前缀
    public static final String YF = "YF-";

    // 采购应付单号公共数字
    public static final String NUMBER = "00";

    // 采购应付单号 入库
    public static final String RK = "RK";

    // 采购应付单号 出库
    public static final String CK = "CK";

    // 采购应付单号 其他应付
    public static final String QT = "QT";

    public static final BigDecimal DEFAULT_BIG_DECIMAL = new BigDecimal( "0.000000");
}
