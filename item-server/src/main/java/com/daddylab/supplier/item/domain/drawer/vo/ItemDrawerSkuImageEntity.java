package com.daddylab.supplier.item.domain.drawer.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.Id;

@Data
@EqualsAndHashCode(of = {"skuId"})
public class ItemDrawerSkuImageEntity {

    @Id
    private Long skuId;

    @DiffIgnore
    private String skuCode;

    @DiffIgnore
    private String specifications;

    private String url;

}
