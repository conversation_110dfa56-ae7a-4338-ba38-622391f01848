package com.daddylab.supplier.item.infrastructure.oa.constants;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 枚举，标准采购-915271252877883011，工厂代发2144444166363759331
 *
 * <AUTHOR>
 * @since 2023/11/23
 */
@RequiredArgsConstructor
@Getter
public enum ProcurementType implements IEnum<Long> {
    STANDARD_PROCUREMENT(-915271252877883011L, "标准采购"),
    FACTORY_REPLACEMENT_SHIPMENT(2144444166363759331L, "工厂代发"),
    ;
    @JsonValue
    private final Long value;
    private final String desc;
}
