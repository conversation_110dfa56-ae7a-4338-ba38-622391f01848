package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 其他入库单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OtherStockIn implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 入库单号
     */
    private String orderNo;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 状态 0:全部 1:待处理 2:已取消 3:编辑中 4:待审核 5:待质检 6:质检待确认 7:已完成
     */
    private Integer status;

    /**
     * 其他入库原因 0:全部 1:无 2:工厂WMS仓入库 3:调拨入库 4:退货入库 5:搬仓入库 6:退货异常入库（无主件）
     */
    private Integer otherReason;

    /**
     * 其他入库原因（原始文本）
     */
    private String reasonText;

    /**
     * 标记名称
     */
    private String markName;

    /**
     * 物流公司id
     */
    private Long logisticsId;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 审核人
     */
    private Long auditUid;

    /**
     * 备注
     */
    private String remark;

    /**
     * 制单人
     */
    private String operatorName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除，不能做业务。这是软删除标记
     */
    @TableLogic
    private Integer isDel;

    private Long deletedAt;


}
