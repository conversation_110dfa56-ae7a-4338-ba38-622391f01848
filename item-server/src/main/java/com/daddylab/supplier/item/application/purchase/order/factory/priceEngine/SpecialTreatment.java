package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.WrapperType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderDetailWrapperService;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> up
 * @date 2025年03月27日 10:08 AM
 */
@Slf4j
@Component
public class SpecialTreatment implements PriceProcessor {

  @Autowired IWdtOrderDetailWrapperService iWdtOrderDetailWrapperService;
  @Autowired IItemService iItemService;
  @Autowired IItemSkuService iItemSkuService;

  private static final Map<String, BigDecimal> priceMap = new HashMap<>(128);

  static {
    priceMap.put("100396801", new BigDecimal("43.13"));
    priceMap.put("100396802", new BigDecimal("43.13"));
    priceMap.put("100396803", new BigDecimal("43.13"));
    priceMap.put("100396804", new BigDecimal("50.33"));
    priceMap.put("100396805", new BigDecimal("50.33"));
    priceMap.put("100396806", new BigDecimal("50.33"));
    priceMap.put("100396807", new BigDecimal("50.33"));
    priceMap.put("100396808", new BigDecimal("50.33"));
    priceMap.put("100396809", new BigDecimal("64.73"));
    priceMap.put("100396810", new BigDecimal("64.73"));
    priceMap.put("100396811", new BigDecimal("64.73"));
    priceMap.put("100396812", new BigDecimal("43.13"));
    priceMap.put("100396813", new BigDecimal("43.13"));
    priceMap.put("100396814", new BigDecimal("43.13"));
    priceMap.put("100396815", new BigDecimal("50.33"));
    priceMap.put("100396816", new BigDecimal("50.33"));
    priceMap.put("100396817", new BigDecimal("50.33"));
    priceMap.put("100396818", new BigDecimal("50.33"));
    priceMap.put("100396819", new BigDecimal("50.33"));
    priceMap.put("100396820", new BigDecimal("64.73"));
    priceMap.put("100396821", new BigDecimal("64.73"));
    priceMap.put("100396822", new BigDecimal("64.73"));
    priceMap.put("100396901", new BigDecimal("43.13"));
    priceMap.put("100396902", new BigDecimal("43.13"));
    priceMap.put("100396903", new BigDecimal("43.13"));
    priceMap.put("100396904", new BigDecimal("50.33"));
    priceMap.put("100396905", new BigDecimal("50.33"));
    priceMap.put("100396906", new BigDecimal("50.33"));
    priceMap.put("100396907", new BigDecimal("50.33"));
    priceMap.put("100396908", new BigDecimal("50.33"));
    priceMap.put("100396909", new BigDecimal("43.13"));
    priceMap.put("100396910", new BigDecimal("43.13"));
    priceMap.put("100396911", new BigDecimal("43.13"));
    priceMap.put("100396912", new BigDecimal("50.33"));
    priceMap.put("100396913", new BigDecimal("50.33"));
    priceMap.put("100396914", new BigDecimal("50.33"));
    priceMap.put("100396915", new BigDecimal("50.33"));
    priceMap.put("100396916", new BigDecimal("50.33"));
    priceMap.put("100396917", new BigDecimal("43.13"));
    priceMap.put("100396918", new BigDecimal("43.13"));
    priceMap.put("100396919", new BigDecimal("43.13"));
    priceMap.put("100396920", new BigDecimal("50.33"));
    priceMap.put("100396921", new BigDecimal("50.33"));
    priceMap.put("100396922", new BigDecimal("50.33"));
    priceMap.put("100396923", new BigDecimal("50.33"));
    priceMap.put("100396924", new BigDecimal("50.33"));
    priceMap.put("100396925", new BigDecimal("43.13"));
    priceMap.put("100396926", new BigDecimal("43.13"));
    priceMap.put("100396927", new BigDecimal("43.13"));
    priceMap.put("100396928", new BigDecimal("50.33"));
    priceMap.put("100396929", new BigDecimal("50.33"));
    priceMap.put("100396930", new BigDecimal("50.33"));
    priceMap.put("100396931", new BigDecimal("50.33"));
    priceMap.put("100396932", new BigDecimal("50.33"));
  }

  @Override
  public Boolean doPriceProcess(TimeBO timeBO) {
    try {
      doPriceProcess0(
          timeBO,
          ListUtil.of(
              WrapperType.STOCK_OUT_SINGLE.getValue(),
              WrapperType.STOCK_OUT_COMBINATION.getValue()));
      doPriceProcess0(
          timeBO,
          ListUtil.of(WrapperType.STOCK_IN_REFUND.getValue(), WrapperType.STOCK_IN_PRE.getValue()));
      return true;
    } catch (Exception e) {
      log.error("处理特殊价格异常", e);
      return true;
    }
  }

  /**
   * 1003968+1003969这2个spu，基础单价均有43.13元和50.33元2种，多件价格形式是任意2件-10元
   *
   * @param timeBO 目标月份
   * @return
   */
  public void doPriceProcess0(TimeBO timeBO, List<Integer> types) {

    final List<WdtOrderDetailWrapper> list =
        iWdtOrderDetailWrapperService
            .lambdaQuery()
            .eq(WdtOrderDetailWrapper::getOperateTime, timeBO.getOperateMonth())
            .in(WdtOrderDetailWrapper::getType, types)
            .in(WdtOrderDetailWrapper::getSkuCode, priceMap.keySet())
            .list();
    if (CollUtil.isEmpty(list)) return;

    list.stream()
        .collect(Collectors.groupingBy(WdtOrderDetailWrapper::getTradeNo))
        .forEach(
            (tradeNo, subList) -> {
              final Map<Long, BigDecimal> resMap = handler(subList);
              final List<WdtOrderDetailWrapper> updaList =
                  subList.stream()
                      .peek(ww -> ww.setPrice(resMap.get(ww.getId())))
                      .collect(Collectors.toList());
              iWdtOrderDetailWrapperService.updateBatchById(updaList);
            });
  }

  /**
   * 1003968+1003969这2个spu，基础单价均有43.13元和50.33元2种，多件价格形式是任意2件-10元
   *
   * @param tmpDtoList
   * @return
   */
  private static Map<Long, BigDecimal> handler(List<WdtOrderDetailWrapper> tmpDtoList) {
    Map<Long, BigDecimal> resMap = new HashMap<>(16);
    final int sum = tmpDtoList.stream().mapToInt(WdtOrderDetailWrapper::getQuantity).sum();
    BigDecimal favourable = new BigDecimal("5");
    // int favourable = 10 * (sum/2) * 1/sum;
    if (sum % 2 == 0) {
      for (WdtOrderDetailWrapper tmpDto : tmpDtoList) {
        resMap.put(tmpDto.getId(), tmpDto.getPrice().subtract(favourable));
      }
    } else {
      // 价格排序，从高到底，单价最高的减去优惠金额
      final List<WdtOrderDetailWrapper> newList =
          tmpDtoList.stream()
              .sorted(Comparator.comparing(WdtOrderDetailWrapper::getPrice).reversed())
              .collect(Collectors.toList());
      for (int i = 0; i < newList.size() - 1; i++) {
        resMap.put(newList.get(i).getId(), newList.get(i).getPrice().subtract(favourable));
      }
      resMap.put(
          newList.get(newList.size() - 1).getId(), newList.get(newList.size() - 1).getPrice());
    }
    return resMap;
  }

  public static void main(String[] args) {}
}
