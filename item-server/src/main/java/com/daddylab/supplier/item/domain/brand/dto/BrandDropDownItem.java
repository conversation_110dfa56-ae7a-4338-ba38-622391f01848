package com.daddylab.supplier.item.domain.brand.dto;

import com.daddylab.supplier.item.common.enums.EnableStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/9 4:38 下午
 * @description
 */
@Data
@ApiModel("品牌类下拉列表值对象")
public class BrandDropDownItem implements Serializable {
    private static final long serialVersionUID = -1489460411458737203L;

    @ApiModelProperty("品牌id")
    private Long id;

    @ApiModelProperty("品牌名称")
    private String name;

    /**
     * 状态 0:停用 1:正常
     */
    @ApiModelProperty("品牌状态")
    private EnableStatusEnum status;

    private String standard;
}
