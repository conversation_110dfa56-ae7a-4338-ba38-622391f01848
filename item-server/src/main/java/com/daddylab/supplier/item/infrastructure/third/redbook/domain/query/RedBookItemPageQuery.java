package com.daddylab.supplier.item.infrastructure.third.redbook.domain.query;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @class ItemPageQuery.java
 * @description 描述类的作用
 * @date 2024-03-01 13:33
 */
@NoArgsConstructor
@Data
public class RedBookItemPageQuery implements Serializable {
    /**
     * 页码
     */
    private Integer pageNo;
    /**
     * 页大小
     */
    private Integer pageSize;
    /**
     * 商品名称关键词
     */
    private String keyword;
    /**
     * 一级品类
     */
    private List<String> topCategoryIds;
    /**
     * 二级品类
     */
    private List<String> lvl2CategoryIds;
    /**
     * 三级品类
     */
    private List<String> lvl3CategoryIds;
    /**
     * 四级品类
     */
    private List<String> lvl4CategoryIds;
    /**
     * 在架状态
     */
    private String buyable;
    /**
     * 小红书编码/条形码/商品ID/SPUID/货号
     */
    private List<String> keywords;
    /**
     * 商品物流方案ID
     */
    private List<String> logisticsPlanIds;
    /**
     * 商品创建时间大于
     */
    private Long createTimeFrom;
    /**
     * 商品创建时间小于
     */
    private Long createTimeTo;
    /**
     * 查询起始itemId，全店item按照时间倒序
     */
    private String lastId;

    public static RedBookItemPageQuery of(Integer pageNo, Integer pageSize) {
        RedBookItemPageQuery itemPageQuery = new RedBookItemPageQuery();
        itemPageQuery.setPageNo(pageNo);
        itemPageQuery.setPageSize(pageSize);
        return itemPageQuery;
    }

    public static RedBookItemPageQuery of(List<String> outItemIds) {
        RedBookItemPageQuery itemPageQuery = new RedBookItemPageQuery();
        itemPageQuery.setKeywords(outItemIds);
        return itemPageQuery;
    }
}
