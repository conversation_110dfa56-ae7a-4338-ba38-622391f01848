package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.Getter;

/**
 * 图片类型 1:普通商品图片 2:运营商品图片
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13 7:27 下午
 * @description
 */
@Getter
public enum ItemImageType implements IEnum<Integer> {

    ALL(0, "所有类型"),

    /**
     * 1:普通商品图片
     */
    COMMON(1, "普通图片"),
    /**
     * 2:运营商品图片
     */
    RUNNING(2, "运营图片");

    @EnumValue
    private final Integer value;

    private final String desc;

    ItemImageType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

}
