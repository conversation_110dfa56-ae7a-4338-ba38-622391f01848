package com.daddylab.supplier.item.application.salesOutStock.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年10月20日 4:50 PM
 */
@ApiModel("销售出库单明细列表")
@Data
public class SalesOutStockDetailListVO {

    @ApiModelProperty("商品SKU")
    private String specNo;

    @ApiModelProperty("规格")
    private String specName;

    @ApiModelProperty("商家编码")
    private String goodsNo;

    @ApiModelProperty("单价")
    private BigDecimal sellPrice;

    @ApiModelProperty("数量")
    private BigDecimal goodsCount;

    @ApiModelProperty("单位")
    private String unitName;

    @ApiModelProperty("总货款")
    private BigDecimal totalAmount;

    // ----- What the hell are these fields?

    @ApiModelProperty("辅助数量")
    private Integer auxiliaryQuantity;

    @ApiModelProperty("辅助单位")
    private String auxiliaryUnit;

    @ApiModelProperty("是否残次品")
    private Integer isDefective;

    // ------

    @ApiModelProperty("平台货品名称")
    private String goodsName;

    // ----

    @ApiModelProperty("品类")
    private String category;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("商品名称")
    private String itemName;

    @ApiModelProperty("商品编码")
    private String itemCode;

    @ApiModelProperty("商品图片")
    private String imgUrl;

}
