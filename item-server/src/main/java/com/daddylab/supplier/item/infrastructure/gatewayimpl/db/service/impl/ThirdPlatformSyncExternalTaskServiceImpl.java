package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ThirdPlatformSyncExternalTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ThirdPlatformSyncExternalTaskMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IThirdPlatformSyncExternalTaskService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 第三方平台同步外部任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-20
 */
@Service
public class ThirdPlatformSyncExternalTaskServiceImpl extends DaddyServiceImpl<ThirdPlatformSyncExternalTaskMapper, ThirdPlatformSyncExternalTask> implements IThirdPlatformSyncExternalTaskService {

}
