package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderDetailService;
import java.util.Collections;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 旺店通订单明细 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Service
public class WdtOrderDetailServiceImpl
    extends DaddyServiceImpl<WdtOrderDetailMapper, WdtOrderDetail>
    implements IWdtOrderDetailService {
  @Override
  public List<WdtOrderDetail> listByTradeIds(List<Long> wdtTradeIds) {
    if (wdtTradeIds == null || wdtTradeIds.isEmpty()) {
      return Collections.emptyList();
    }
    return lambdaQuery().in(WdtOrderDetail::getTradeId, wdtTradeIds).list();
  }

  @Override
  public List<WdtOrderDetail> listBySrcTid(String srcTid) {
    return lambdaQuery().eq(WdtOrderDetail::getSrcTid, srcTid).list();
  }
}
