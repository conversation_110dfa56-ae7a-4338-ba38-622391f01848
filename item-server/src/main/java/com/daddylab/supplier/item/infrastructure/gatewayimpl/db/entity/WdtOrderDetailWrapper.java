package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.WrapperType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtOrderDetailWrapper implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableLogic
    private Integer isDel;

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    private Long deletedAt;


    private String skuCode;

    private BigDecimal price;

    /**
     * @link{com.daddylab.supplier.item.application.purchase.order.factory.dto.WrapperType}
     */
    private Integer type;

    private String suiteNo;

    private Integer num;

    private Long tradeId;

    private String tradeNo;

    private Long recId;

    /**
     * 出入库数量
     */
    private Integer quantity;

    private String operateTime;

    /**
     * 0、非赠品 1、自动赠送 2、手工赠送 4、周期购赠送 8、平台赠送
     */
    private Integer isGift;

    private Long payTime;

    private Integer platformType;

    private String warehouseNo;

    private Long providerId;

    private String suiteDbId;

    /**
     * 退款数量和发货数量，业务线
     * 只有再type = 99，统计时出现这个值。
     */
    private Integer refundNum;
    private Integer deliverNum;

    private Integer businessLine;

    @Data
    @AllArgsConstructor
    public static class SingleKey {
        private String skuCode;
        private Integer isGift;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            SingleKey singleKey = (SingleKey) o;
            return com.google.common.base.Objects.equal(skuCode, singleKey.skuCode) && com.google.common.base.Objects.equal(isGift, singleKey.isGift);
        }

        @Override
        public int hashCode() {
            return com.google.common.base.Objects.hashCode(skuCode, isGift);
        }
    }

    @Data
    @AllArgsConstructor
    public static class SuiteKey {
        private String suiteNo;
        private Integer isGift;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            SuiteKey suiteKey = (SuiteKey) o;
            return com.google.common.base.Objects.equal(suiteNo, suiteKey.suiteNo) && com.google.common.base.Objects.equal(isGift, suiteKey.isGift);
        }

        @Override
        public int hashCode() {
            return com.google.common.base.Objects.hashCode(suiteNo, isGift);
        }
    }


    public static class Builder {
        private String operateTime;
        private String skuCode;
        private BigDecimal price;
        private Integer quantity;
        /**
         * 1.出库单品
         * 2.出库组合商品
         * -1.入库数量。
         * -9.异常情况。
         * 9.表示是sku进行出入库处理后的汇总记录。
         */
        private Integer type;
        private String warehouseNo;
        private Long providerId;

        private String tradeNo;

        private Long payTime;

        private Integer platformType;

        private String suiteNo;

        private Integer isGift;

        private Integer businessLine;

        private Builder() {
        }

        ;

        public static Builder builder() {
            return new Builder();
        }

        public Builder operateTime(String operateTime) {
            this.operateTime = operateTime;
            return this;
        }

        public Builder skuCode(String skuCode) {
            this.skuCode = skuCode;
            return this;
        }

        public Builder price(BigDecimal price) {
            this.price = price;
            return this;
        }

        public Builder quantity(Integer quantity) {
            this.quantity = quantity;
            return this;
        }

        public Builder type(WrapperType type) {
            this.type = type.getValue();
            return this;
        }

        public Builder warehouseNo(String warehouseNo) {
            this.warehouseNo = warehouseNo;
            return this;
        }

        public Builder providerId(Long providerId) {
            this.providerId = providerId;
            return this;
        }

        public Builder tradeNo(String tradeNo) {
            this.tradeNo = tradeNo;
            return this;
        }

        public Builder payTime(Long payTime) {
            this.payTime = payTime;
            return this;
        }

        public Builder platformType(Integer platformType) {
            this.platformType = platformType;
            return this;
        }

        public Builder suiteNo(String suiteNo) {
            this.suiteNo = suiteNo;
            return this;
        }

        public Builder gift(Integer giftType) {
            this.isGift = giftType;
            return this;
        }

        public Builder businessLine(Integer businessLine) {
            this.businessLine = businessLine;
            return this;
        }

        public WdtOrderDetailWrapper build() {
            WdtOrderDetailWrapper wrapper = new WdtOrderDetailWrapper();
            wrapper.setOperateTime(operateTime);
            wrapper.setSkuCode(skuCode);
            wrapper.setPrice(price);
            wrapper.setQuantity(quantity);
            wrapper.setType(type);
            wrapper.setWarehouseNo(warehouseNo);
            wrapper.setProviderId(providerId);
            wrapper.setTradeNo(tradeNo);
            wrapper.setPayTime(payTime);
            wrapper.setPlatformType(platformType);
            wrapper.setSuiteNo(suiteNo);
            wrapper.setIsGift(isGift);
            wrapper.setBusinessLine(businessLine);
            return wrapper;
        }
    }


}
