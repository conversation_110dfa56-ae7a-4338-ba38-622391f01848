package com.daddylab.supplier.item.application.order;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.refundOrder.RefundOrderService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.item.gateway.ItemImageGateway;
import com.daddylab.supplier.item.domain.order.OrderSensitiveInfo;
import com.daddylab.supplier.item.domain.order.OrderSensitiveInfoQuery;
import com.daddylab.supplier.item.domain.order.OrderSensitiveInfoRepository;
import com.daddylab.supplier.item.domain.wdt.consts.PlatformMap;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseOrderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderDetailWrapperService;
import com.daddylab.supplier.item.infrastructure.userInfoCrypto.UserInfoCrypto;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.order.OrderBaseInfo;
import com.daddylab.supplier.item.types.order.OrderDetail;
import com.daddylab.supplier.item.types.order.OrderItem;
import com.daddylab.supplier.item.types.order.OrderQuery;
import com.daddylab.supplier.item.types.order.PurchaseOrderTabQuery;
import com.daddylab.supplier.item.types.order.PurchaseOrderTabVO;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderBaseInfo;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @since 2022/4/17
 */
@Service
public class OrderServiceImpl implements OrderService {

    @Autowired
    IWdtOrderDetailWrapperService iWdtOrderDetailWrapperService;
    @Autowired
    IPurchaseOrderService iPurchaseOrderService;
    @Autowired
    ItemImageGateway itemImageGateway;
    @Autowired
    private RefundOrderService refundOrderService;
    @Autowired
    private WdtOrderMapper wdtOrderMapper;
    @Autowired
    private OrderSensitiveInfoRepository orderSensitiveInfoRepository;
    @Autowired
    private UserInfoCrypto userInfoCrypto;

    @Override
    public Map<String, OrderSensitiveInfo> queryOrderSensitiveInfoBatchBySrcOrderNo(
            List<String> srcOrderNos) {
        final Map<String, OrderSensitiveInfo> sensitiveInfoMap = wdtOrderMapper
                .queryOrderSensitiveInfoBatchBySrcOrderNo(srcOrderNos).stream().collect(
                        Collectors.toMap(OrderSensitiveInfo::getOrderNo, Function.identity(),
                                (a, b) -> a));
        final Map<String, OrderSensitiveInfo> orderSensitiveInfoMapFromBigdata = orderSensitiveInfoRepository
                .getOrderSensitiveInfoBatch(srcOrderNos);
        orderSensitiveInfoMapFromBigdata.forEach((orderNo, sensitiveInfo) -> {
            sensitiveInfo.setReceiverPhone(sensitiveInfo.getReceiverPhone());
            sensitiveInfoMap.put(orderNo, sensitiveInfo);
        });
        return sensitiveInfoMap;
    }

    @Override
    public PageResponse<OrderBaseInfo> orderQuery(OrderQuery orderQuery) {
        if (orderQuery.withSensitiveInfoQuery()) {
            return orderSensitiveInfoPageQuery(orderQuery);
        }
        final Long total = RedisUtil.loadingCache(getOrderQueryKey(orderQuery), Long.class,
                60, TimeUnit.SECONDS, () -> count(orderQuery));
        if (total == 0) {
            return ResponseFactory.emptyPage();
        }
        List<Long> ids;
        if (orderQuery.getWithOrderDetailsCond()) {
            ids = wdtOrderMapper.queryOrderIdsWithDetailsCond(orderQuery);
        } else {
            ids = wdtOrderMapper.queryOrderIdsWithoutDetailsCond(orderQuery);
        }
        return buildOrderQueryPageResponse(orderQuery, total, ids);
    }

    @NonNull
    private PageResponse<OrderBaseInfo> orderSensitiveInfoPageQuery(OrderQuery orderQuery) {
        final OrderQueryIdsResult orderQueryIdsResult = RedisUtil
                .loadingCache(getOrderQueryKey(orderQuery), OrderQueryIdsResult.class, 60,
                        TimeUnit.SECONDS, () -> orderSensitiveInfoFullQuery(orderQuery));
        if (orderQueryIdsResult.getTotal() == 0) {
            return ResponseFactory.emptyPage();
        }
        final List<Long> idsThisPage = orderQueryIdsResult.getIds().stream()
                .skip(orderQuery.getOffset())
                .limit(orderQuery.getPageSize()).collect(
                        Collectors.toList());
        return buildOrderQueryPageResponse(orderQuery, orderQueryIdsResult.getTotal(), idsThisPage);
    }

    private List<Long> queryOrderIdsAdapterCond(OrderQuery orderQuery) {
        if (orderQuery.getWithOrderDetailsCond()) {
            return wdtOrderMapper.queryOrderIdsWithDetailsCond(orderQuery);
        } else {
            return wdtOrderMapper.queryOrderIdsWithoutDetailsCond(orderQuery);
        }
    }

    @NonNull
    private OrderQueryIdsResult orderSensitiveInfoFullQuery(OrderQuery orderQuery) {
        Long count = count(orderQuery);
        List<Long> ids = queryOrderIdsAdapterCond(orderQuery);
        final OrderSensitiveInfoQuery orderSensitiveInfoQuery = new OrderSensitiveInfoQuery();
        orderSensitiveInfoQuery.setReceiverPhone(orderQuery.getReceiverMobile());
        orderSensitiveInfoQuery.setReceiverName(orderQuery.getReceiverName());

        final List<OrderSensitiveInfo> orderSensitiveInfos = orderSensitiveInfoRepository
                .query(orderSensitiveInfoQuery);
        count = count + orderSensitiveInfos.size();
        if (count == 0) {
            return OrderQueryIdsResult.of(count, Collections.emptyList());
        }
        final OrderQuery orderQueryWithSrcOrderNos = OrderAssembler.INST.copy(orderQuery);
        final List<String> srcOrderNos = orderSensitiveInfos.stream()
                .map(OrderSensitiveInfo::getOrderNo).collect(
                        Collectors.toList());
        if (CollectionUtil.isNotEmpty(srcOrderNos)) {
            orderQueryWithSrcOrderNos.setSrcOrderNos(srcOrderNos);
            orderQueryWithSrcOrderNos.setReceiverMobile(null);
            orderQueryWithSrcOrderNos.setReceiverName(null);
            orderQueryWithSrcOrderNos.setPageIndex(1);
            orderQueryWithSrcOrderNos.setPageSize(2333);
            ids.addAll(queryOrderIdsAdapterCond(orderQueryWithSrcOrderNos));
        }
        final List<Long> resultIds = ids.stream().distinct().collect(Collectors.toList());
        return OrderQueryIdsResult.of((long) resultIds.size(), resultIds);
    }

    @NonNull
    private PageResponse<OrderBaseInfo> buildOrderQueryPageResponse(OrderQuery orderQuery,
            Long total, List<Long> ids) {
        if (ids.isEmpty()) {
            return ResponseFactory.emptyPage();
        }
        final List<OrderBaseInfo> orderBaseInfos = wdtOrderMapper.queryOrderByIds(ids);
        final Set<Long> itemIds = orderBaseInfos.stream().map(OrderBaseInfo::getOrderItems)
                .flatMap(v -> v.stream().map(OrderItem::getItemId)).filter(Objects::nonNull)
                .collect(
                        Collectors.toSet());
        final Map<Long, String> mainImgUrls = itemImageGateway.batchGetItemMainImgUrls(itemIds);
        if (!mainImgUrls.isEmpty()) {
            orderBaseInfos.forEach(orderBaseInfo -> orderBaseInfo.getOrderItems()
                    .forEach(orderItem -> {
                        orderItem.setItemMainImg(Optional.ofNullable(orderItem.getItemMainImg())
                                .orElseGet(
                                        () -> mainImgUrls.getOrDefault(orderItem.getItemId(), "")));
                    }));
        }
        return ResponseFactory.ofPage(orderBaseInfos, total, orderQuery.getPageIndex(),
                orderQuery.getPageSize());
    }

    @NonNull
    private String getOrderQueryKey(OrderQuery orderQuery) {
        return "ORDER_COUNT:" + DigestUtil.md5Hex(orderQuery.toString());
    }

    private Long count(OrderQuery orderQuery) {
        if (orderQuery.getWithOrderDetailsCond()) {
            return wdtOrderMapper.queryOrderCountWithDetailsCond(orderQuery);
        } else {
            return wdtOrderMapper.queryOrderCountWithoutDetailsCond(orderQuery);
        }
    }

    @Override
    public SingleResponse<OrderDetail> orderDetail(String wdtOrderNo) {
        OrderDetail orderDetail = wdtOrderMapper.orderDetailQuery(wdtOrderNo);
        if (orderDetail == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "订单不存在");
        }
        final RefundOrderQuery queryRefundOrder = new RefundOrderQuery();
        queryRefundOrder.setWdtOrderNo(wdtOrderNo);
        final PageResponse<RefundOrderBaseInfo> refundOrderResp = refundOrderService
                .refundOrderQuery(queryRefundOrder);
        if (!refundOrderResp.isSuccess()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.API_REQ_ERROR, "退换单查询失败");
        }
        final List<RefundOrderBaseInfo> orderRefundDetails = refundOrderResp.getData();
        orderDetail.setRefundDetails(orderRefundDetails);

        final HashSet<Long> itemIds = new HashSet<>();
        orderDetail.getItemDetails().stream().map(OrderItem::getItemId).filter(Objects::nonNull)
                .forEach(itemIds::add);

        final Map<Long, String> itemMainImgUrls = itemImageGateway.batchGetItemMainImgUrls(itemIds);
        if (!itemMainImgUrls.isEmpty()) {
            orderDetail.getItemDetails().forEach(item -> item.setItemMainImg(
                    Optional.ofNullable(item.getItemMainImg())
                            .orElseGet(() -> itemMainImgUrls.getOrDefault(item.getItemId(), ""))));
        }

        fillOrderSensitiveInfo(orderDetail);
        encryptSensitiveInfo(orderDetail);

        return SingleResponse.of(orderDetail);
    }

    private void encryptSensitiveInfo(OrderDetail orderDetail) {
        String receiverMobile = orderDetail.getReceiverMobile();
        orderDetail.setReceiverMobile(StringUtil.maskMobile(receiverMobile));
        orderDetail.setReceiverMobileCipher(userInfoCrypto.encrypt(receiverMobile));
        String receiverTel = orderDetail.getReceiverTel();
        orderDetail.setReceiverTel(StringUtil.maskMobile(receiverTel));
        orderDetail.setReceiverTelCipher(userInfoCrypto.encrypt(receiverTel));
    }

    private void fillOrderSensitiveInfo(OrderDetail orderDetail) {
        if (StrUtil.isNotBlank(orderDetail.getSrcOrderNos())) {
            final List<String> srcOrderNos = StrUtil.splitTrim(orderDetail.getSrcOrderNos(), ",");
            if (!srcOrderNos.isEmpty()) {
                final OrderSensitiveInfo orderSensitiveInfo = orderSensitiveInfoRepository
                        .getOrderSensitiveInfo(srcOrderNos.get(0));
                if (orderSensitiveInfo != null) {
                    final String receiverPhone = orderSensitiveInfo.getReceiverPhone();
                    orderDetail.setReceiverMobile(StringUtil.maskMobile(receiverPhone));
                    orderDetail.setReceiverName(orderSensitiveInfo.getReceiverName());
                    orderDetail.setReceiverAddress(orderSensitiveInfo.getReceiverAddr());
                }
            }
        }
    }


    @Override
    public PageResponse<PurchaseOrderTabVO> purchaseOrderTab(Long purchaseOrderId) {
        PurchaseOrderTabQuery query = initTabQuery(purchaseOrderId);

        PageInfo<PurchaseOrderTabVO> pageInfo = PageHelper
                .startPage(query.getPageIndex(), query.getPageSize())
                .doSelectPage(() -> wdtOrderMapper.purchaseTabQuery(query))
                .toPageInfo(tabVO -> {
                    if (tabVO instanceof PurchaseOrderTabVO) {
                        PurchaseOrderTabVO vo = (PurchaseOrderTabVO) tabVO;
                        vo.setPlatform(PlatformMap.mapPlatform(vo.getWdtPlatformId()).desc);
                        vo.setRelationType(relationType(vo.getWdtTradeId()));
                        return vo;
                    }
                    return null;
                });
        return ResponseFactory.ofPage(pageInfo);
    }

    private PurchaseOrderTabQuery initTabQuery(Long purchaseOrderId) {
        PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(purchaseOrderId);
        Assert.notNull(purchaseOrder, "采购订单id非法");
        Assert.state(purchaseOrder.getType().equals(2), "必须为工厂采购流程订单");

        LocalDateTime createdTime = DateUtil.parseTimeStamp(purchaseOrder.getCreatedAt());
        String startDt = DateUtil.getFirstDayOfMonth(createdTime.plusMonths(-1))
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:dd"));
        String endDt = DateUtil.getLastDayOfMonth(createdTime.plusMonths(-1))
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:dd"));

        PurchaseOrderTabQuery query = new PurchaseOrderTabQuery();
        query.setProviderId(purchaseOrder.getProviderId());
        query.setStartDt(startDt);
        query.setEndDt(endDt);

        return query;
    }

    /**
     * wdt订单和采购单明细的关联类型
     *
     * @param tradeId
     * @return
     */
    private Integer relationType(String tradeId) {
        LambdaQueryWrapper<WdtOrderDetailWrapper> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(WdtOrderDetailWrapper::getTradeId, tradeId);
        if (iWdtOrderDetailWrapperService.count(wrapper) > 0) {
            return 1;
        }
        return null;
    }

    @Override
    public Boolean related(Long purchaseOrderId) {
        PurchaseOrderTabQuery query = initTabQuery(purchaseOrderId);
        return wdtOrderMapper.purchaseTabCount(query) > 0;
    }
}
