package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.controller.item.dto.AttrDto;
import com.daddylab.supplier.item.controller.item.dto.SkuWithLaunchPlanDto;
import com.daddylab.supplier.item.domain.item.data.ItemAndSkuCodeInfo;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuIsGiftDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuNameDo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuPriceDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuWarehouseNoDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 商品SKU Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Repository
public interface ItemSkuMapper extends DaddyBaseMapper<ItemSku> {

    @Select("select sku_code from item_sku where item_id = #{param} and is_del = 0")
    List<String> selectSkuCode(@Param("param") Long itemId);

    List<String> getSkuCodes(@Param("itemCode") String itemCode);

    List<ItemAndSkuCodeInfo> getItemAndSkuCodeInfoBatchByItemNos(@Param("itemCodes") Collection<String> itemCodes);

    @Update("update item_sku set king_dee_id = #{kingDeeId} where id = #{id}")
    void saveKingDeeId(@Param("kingDeeId") String kingDeeId, @Param("id") Long id);

    @Select("select sku_code from item_sku where item_id = #{itemId} order by id desc limit 1")
    String getLatestSkuCode(@Param("itemId") Long itemId);

    @Select("select IFNULL(warehouse_no,'') from item_sku where sku_code = #{skuCode} and is_del = 0")
    String getWarehouseNo(@Param("skuCode") String skuCode);

    List<SkuWarehouseNoDO> getWarehouseNoList(@Param("list") List<String> skuCodeList);

    List<SkuPriceDO> selectPrice(@Param("skuCodeList") List<String> skuCodeList);

    List<SkuIsGiftDO> getIsGiftList(@Param("list") List<String> skuCodeList);

    List<SkuWithLaunchPlanDto> selectBatchDtosBySkuIds(@Param("skuIds") List<Long> skuIds);

    List<ItemAndSkuCodeInfo> getItemAndSkuCodeInfoBatch(Collection<String> skuCodes);

    @Select("select name from provider where id = (\n" +
            "    select provider_id from item_sku where sku_code = #{skuCode}\n" +
            ") and is_del = 0 ")
    String selectProviderNameBySKuCode(String skuCode);

    List<AttrDto> selectSkuAttrDtosBySkuId(@Param("skuId") Long skuId);

    List<SkuNameDo> selectSkuName(@Param("skuCodes") Collection<String> skuCodes);

    /**
     * 获取SKU的合作方
     *
     * @param skuCode SKU编码
     * @return 合作模式，BusinessLine
     */
    List<Integer> getSkuBusinessLine(@Param("skuCode") String skuCode);

    /**
     * 获取 sku 的品牌 ID
     *
     * @param skuCode
     * @return
     */
    Long getSkuBrandId(@Param("skuCode") String skuCode);
}
