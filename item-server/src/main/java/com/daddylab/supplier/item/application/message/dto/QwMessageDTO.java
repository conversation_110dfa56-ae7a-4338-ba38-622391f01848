package com.daddylab.supplier.item.application.message.dto;
import com.daddylab.supplier.item.application.message.dto.QwMessageDTO.MessageType;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @class QwMessgeDTO.java
 * @description 描述类的作用
 * @date 2024-04-11 13:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
public class QwMessageDTO implements Serializable  {
    private List<Long> userIds;
    private String title;
    private String content;
    private String link;
    private MessageType messageType = MessageType.CARD;

    public static enum MessageType {
        CARD,
        TEXT;
    }

    public static QwMessageDTO of(List<Long> userIds, String title, String content, String link) {
        QwMessageDTO qwMessageDTO = new QwMessageDTO();
        qwMessageDTO.setUserIds(userIds);
        qwMessageDTO.setTitle(title);
        qwMessageDTO.setContent(content);
        qwMessageDTO.setLink(link);
        qwMessageDTO.setMessageType(MessageType.CARD);
        return qwMessageDTO;
    }
}
