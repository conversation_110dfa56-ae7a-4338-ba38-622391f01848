package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.MessageConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessageOperationType;

import java.util.Optional;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-21
 */
public interface IMessageConfigService extends IDaddyService<MessageConfig> {

    Optional<MessageConfig> getByOperationType(MessageOperationType operationType, Boolean canEffect);
}
