package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableApplyOrderDetail;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 采购付款申请单详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
public interface PurchasePayableApplyOrderDetailMapper extends DaddyBaseMapper<PurchasePayableApplyOrderDetail> {

    /**
     * 根据 原应付单单号，查询 非处理异常状态的 申请付款单列表中的sku数量。
     * @param purchasePayableOrderNo 原应付单单号
     * @return
     */
    Integer countSkuCountByPurchasePayableOrderNo(@Param("no") String purchasePayableOrderNo);

}
