package com.daddylab.supplier.item.application.item.combinationItem.dto.query;

import com.alibaba.cola.dto.PageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.CombinationItemType;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/11 5:29 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("组合商品分页查询入参")
public class CombinationItemPageQuery extends PageQuery {


    private static final long serialVersionUID = -4956610937463986515L;

    @ApiModelProperty("组合商品id")
    private Long id;

    @ApiModelProperty("组合商品编码")
    private String code;

    @ApiModelProperty("组合商品名称")
    private String name;

    @ApiModelProperty("单品商品id")
    private Long itemId;

    @ApiModelProperty("单品商品编码")
    private String itemCode;

    @ApiModelProperty("单品商品skuCode")
    private String skuCode;

    private List<String> skuCodes;

    @ApiModelProperty("属性")
    private CombinationItemType type;

    @ApiModelProperty("创建开始时间")
    private Long startTime;

    @ApiModelProperty("创建结束时间")
    private Long endTime;

    @ApiModelProperty("前端忽略，偏移量")
    private Long offsetVal;

    @ApiModelProperty("合作模式（业务线）筛选（多选）")
    private List<Integer> businessLine = new ArrayList<>();

    @ApiModelProperty("合作方")
    private List<Integer> corpType;

    @ApiModelProperty("业务类型")
    private List<Integer> bizType;
}
