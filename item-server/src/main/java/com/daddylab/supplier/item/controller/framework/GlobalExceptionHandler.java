package com.daddylab.supplier.item.controller.framework;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.exception.SysException;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.exceptions.BizExceptionWithData;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.infrastructure.validators.link.ValidateHelper;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.HttpMediaTypeException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/18 1:58 下午
 * @description
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(value = {HttpMediaTypeException.class})
    public Response httpMediaTypeException(Exception e) {
        log.warn("HttpMediaTypeException msg:{} e:{}", e.getMessage(), e);
        final ErrorCode errorCode = ErrorCode.HTTP_API_RESOURCE_NOT_VALID;
        return Response.buildFailure(errorCode.getCode(), errorCode.getMsg() + ":" + e.getMessage());
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public Response httpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        log.error("HttpRequestMethodNotSupportedException: " + e.getMessage());
        return Response.buildFailure(ErrorCode.HTTP_METHOD_NOT_SUPPORT.getCode(),
                ErrorCode.HTTP_METHOD_NOT_SUPPORT.getMsg() + ":" + e.getMessage());
    }

    @ExceptionHandler(value = {MissingServletRequestParameterException.class})
    public Response missingServletRequestParameterException(MissingServletRequestParameterException e) {
        log.warn("MissingServletRequestParameterException msg:{} e{}", e.getMessage(), e);
        final ErrorCode errorCode = ErrorCode.VERIFY_PARAM;
        return Response.buildFailure(errorCode.getCode(), errorCode.getMsg() + ":" + e.getMessage());
    }

    @ExceptionHandler(value = {MethodArgumentNotValidException.class})
    public Response methodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.warn("MethodArgumentNotValidException msg:{}", e.getMessage());
        String msg = null;
        try {
            msg = ValidateHelper.constraintsToOneLine(
                    e.getBindingResult().getFieldErrors().stream()
                            .map(v -> v.unwrap(ConstraintViolation.class)).collect(
                                    Collectors.toSet())
            );
        } catch (Exception exception) {
            //避免当出现问题时产生太多警告日志
            if (LocalDateTime.now().getSecond() == 0) {
                log.warn("方法参数验证异常，其包装的字段错误非预期类型，可能项目架构发生变化，请检查:" + exception.getMessage());
            }
        }
        if (msg == null) {
            msg = Optional.of(e.getBindingResult())
                    .map(Errors::getFieldErrors)
                    .map(fieldErrors -> ":" + fieldErrors.stream()
                            .map(DefaultMessageSourceResolvable::getDefaultMessage)
                            .collect(Collectors.joining("\n")))
                    .orElse("");
        }
        final ErrorCode errorCode = ErrorCode.VERIFY_PARAM;
        return Response.buildFailure(errorCode.getCode(), errorCode.getMsg() + ":" + msg);
    }

    @ExceptionHandler(value = {ConstraintViolationException.class})
    public Response constraintViolationException(ConstraintViolationException e) {
        log.warn("ConstraintViolationException msg:{} e:{}", e.getMessage(), e);
        Set<ConstraintViolation<?>> constraintViolations = e.getConstraintViolations();
        final String constraintViolationsStr = ValidateHelper.constraintsToOneLine(constraintViolations);
        return Response.buildFailure(ErrorCode.VERIFY_PARAM.getCode(),
                ErrorCode.VERIFY_PARAM.getMsg() + ":" + constraintViolationsStr);
    }

    @ExceptionHandler(value = {BindException.class})
    public Response bindException(BindException e) {
        log.warn("bindException msg:{}", e.getMessage());
        final String msg = Optional.of(e.getFieldErrors())
                .map(fieldErrors -> ":" + fieldErrors.stream().map(DefaultMessageSourceResolvable::getDefaultMessage)
                        .collect(Collectors.joining("\n")))
                .orElse("");
        final ErrorCode errorCode = ErrorCode.VERIFY_PARAM;
        return Response.buildFailure(errorCode.getCode(), errorCode.getMsg() + ":" + msg);
    }

    @ExceptionHandler(value = {MethodArgumentTypeMismatchException.class})
    public Response methodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e) {
        log.warn("MethodArgumentTypeMismatchException msg:{} e:{}", e.getMessage(), e);
        return Response.buildFailure(ErrorCode.VERIFY_PARAM.getCode(),
                ErrorCode.VERIFY_PARAM.getMsg() + ":" + e.getMessage());
    }

    /**
     * @param e 未知异常捕获
     */
    @ExceptionHandler(Exception.class)
    @SuppressWarnings({"rawtypes", "unchecked"})
    public Response exception(Exception e) {
        if (e instanceof BizException) {
            log.error("业务异常:{} {}", ((BizException) e).getErrCode(), e.getMessage());
            if (e instanceof BizExceptionWithData) {
                final SingleResponse response = SingleResponse.buildFailure(((BizException) e).getErrCode(),
                        e.getMessage());
                response.setData(((BizExceptionWithData) e).getData());
                return response;
            } else {
                return Response.buildFailure(((BizException) e).getErrCode(), e.getMessage());
            }
        }
        if (e instanceof SysException) {
            log.error("系统异常", e);
            Alert.text(MessageRobotCode.GLOBAL, "系统异常:" + ExceptionUtil.getSimpleStackString(e));
            return Response.buildFailure(((SysException) e).getErrCode(), e.getMessage());
        }
        if (e instanceof CompletionException) {
            log.error("业务异常", e);
            return Response.buildFailure(ErrorCode.SYS_ERROR.getCode(), e.getMessage());
        }

        log.error("未知异常", e);
        Alert.text(MessageRobotCode.GLOBAL, "未知异常:" + ExceptionUtil.getSimpleStackString(e));
        return Response.buildFailure(ErrorCode.UN_KNOW_ERROR.getCode(), ErrorCode.UN_KNOW_ERROR.getMsg());
    }

    @ExceptionHandler(WdtErpException.class)
    public Response wdtErpException(WdtErpException e) {
        return Response.buildFailure(String.valueOf(e.getCode()), e.getMessage());
    }

    @ExceptionHandler(SQLException.class)
    public Response sqlException(SQLException e) {
        log.error("sqlException:{}", e.getSQLState(), e);
        return Response.buildFailure(ErrorCode.SYS_ERROR.getCode(), ErrorCode.SYS_ERROR.getMsg());
    }

    @ExceptionHandler(value = {IllegalArgumentException.class})
    public Response illegalArgumentException(IllegalArgumentException e) {
        final ErrorCode errorCode = ErrorCode.VERIFY_PARAM;
        log.error("IllegalArgumentException msg:{}", e.getMessage(), e);
        return Response.buildFailure(errorCode.getCode(), e.getMessage());
    }

    @ExceptionHandler(value = {IllegalStateException.class})
    public Response illegalStateException(IllegalStateException e) {
        final ErrorCode errorCode = ErrorCode.BUSINESS_OPERATE_ERROR;
        log.error("IllegalStateException msg:{}", e.getMessage(), e);
        return Response.buildFailure(errorCode.getCode(), e.getMessage());
    }

    @ExceptionHandler(value = {HttpMessageNotReadableException.class})
    public Response httpMessageNotReadableException(HttpMessageNotReadableException e) {
        final ErrorCode errorCode = ErrorCode.VERIFY_PARAM;
        log.error("HttpMessageNotReadableException msg:{}", e.getMessage(), e);
        return Response.buildFailure(errorCode.getCode(), e.getMessage());
    }

    @ExceptionHandler(value = {NullPointerException.class})
    public Response nullPointerException(NullPointerException e) {
        log.error("[数据异常]NullPointerException msg:{}", e.getMessage(), e);
        final ErrorCode errorCode = ErrorCode.DATA_NOT_FOUND;
        return Response.buildFailure(errorCode.getCode(),
                Optional.ofNullable(e.getMessage()).filter(StringUtil::isNotEmpty).orElse("数据异常"));
    }

    @ExceptionHandler({UnsupportedOperationException.class})
    public Response unsupportedOperationException(UnsupportedOperationException e) {
        throw ExceptionPlusFactory.bizException(ErrorCode.UNSUPPORTED_OPERATION, e.getMessage());
    }

    @ExceptionHandler(value = {JsonProcessingException.class})
    public Response jsonParseException(Exception e) {
        log.error("请求参数解析异常", e);
        final ErrorCode errorCode = ErrorCode.VERIFY_PARAM;
        return Response.buildFailure(errorCode.getCode(), errorCode.getMsg());
    }

}
