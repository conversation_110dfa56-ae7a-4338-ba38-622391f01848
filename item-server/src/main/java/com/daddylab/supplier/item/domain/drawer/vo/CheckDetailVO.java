package com.daddylab.supplier.item.domain.drawer.vo;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.CheckDetailDTO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.CheckDetailDTO.ItemType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.CheckProject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Class  CheckDetailVO
 *
 * @Date 2022/6/3下午12:53
 * <AUTHOR>
 */
@ApiModel(value = "CheckDetailVO", description = "检测报告详情")
@Data
public class CheckDetailVO {

    @ApiModelProperty("检测id")
    private Long id;
    @ApiModelProperty("商品ID")
    private Long itemId;
    @ApiModelProperty("商品名称")
    private String itemName;
    @ApiModelProperty("商品类型")
    private ItemType itemType;
    @ApiModelProperty("检测类型")
    private Integer checkType;
    @ApiModelProperty("商品品牌")
    private String organizationName;
    @ApiModelProperty("委托单位")
    private String agentCompany;
    @ApiModelProperty("报告开始时间")
    private Integer reportStartTime;
    @ApiModelProperty("报告结束时间")
    private Long reportEndTime;
    @ApiModelProperty("背景")
    private String background;
    @ApiModelProperty("报告信息")
    private List<CheckDetailDTO.Reports> reports;
    @ApiModelProperty(value = "检测报告内容")
    private String reportContent;
    @ApiModelProperty("结果")
    private Integer result;
    @ApiModelProperty("opinion")
    private String opinion;
    @ApiModelProperty("是否隐藏")
    private Integer isHide;
    @ApiModelProperty("更新时间")
    private Long updatedAt;
    @ApiModelProperty("更新人id")
    private Long updatedUid;
    @ApiModelProperty("更新人")
    private String updatedUname;
    @ApiModelProperty("是否qc负责人")
    private Boolean isQcChargePerson;
    @ApiModelProperty("检测项目")
    private List<CheckProject> checkProject;

    @NoArgsConstructor
    @Data
    public static class Reports {
        @ApiModelProperty("报告id")
        private Long id;
        @ApiModelProperty("检测id")
        private Long checkId;
        @ApiModelProperty("报告编号")
        private String reportNo;
        @ApiModelProperty("检测单位")
        private String checkCompany;
        @ApiModelProperty("检测费用")
        private BigDecimal fee;
        @ApiModelProperty("fee_voucher")
        private List<?> feeVoucher;
        @ApiModelProperty("报告文件")
        private List<CheckDetailDTO.Reports.Files> files;
        @ApiModelProperty("status")
        private Integer status;
        @ApiModelProperty("is_repeat")
        private Integer isRepeat;

        @NoArgsConstructor
        @Data
        public static class Files {
            @ApiModelProperty("id")
            private Long id;
            @ApiModelProperty("报告ID")
            private Long reportId;
            @ApiModelProperty("文件名称")
            private String name;
            @ApiModelProperty("报告PDF")
            private String pdfUrl;
            @ApiModelProperty("图片地址")
            private List<String> imgUrls;
            @ApiModelProperty("状态")
            private Integer status;
        }
    }

}
