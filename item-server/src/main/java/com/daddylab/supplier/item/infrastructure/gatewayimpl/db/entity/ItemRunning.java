package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 运营信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemRunning implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 平台商品ID 0:小程序ID 1:会员店ID 2:美妆店ID 3:母婴店ID 4:自定义ID
     */
    private Integer type;

    /**
     * 平台商品ID名称
     */
    private String name;

    /**
     * 商品ID
     */
    private Long itemId;

    /**
     * 平台商品ID
     */
    private String platformItemId;

    /**
     * 产品标准名
     */
    private String standardName;

    /**
     * 卖点文案
     */
    private String sellingPoints;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除，不能做业务。这是软删除标记
     */
    @TableLogic
    private Integer isDel;


}
