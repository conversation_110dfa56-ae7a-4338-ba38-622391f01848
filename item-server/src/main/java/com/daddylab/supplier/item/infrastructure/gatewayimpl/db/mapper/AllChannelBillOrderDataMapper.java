package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AllChannelBillOrderData;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;

/**
 * <p>
 * 全渠道开票主订单数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
public interface AllChannelBillOrderDataMapper extends DaddyBaseMapper<AllChannelBillOrderData> {

}
