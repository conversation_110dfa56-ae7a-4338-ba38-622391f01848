package com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector;

import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/25 2:20 下午
 * @description
 */
public class DeleteByIdWithTime extends AbstractMethod {

    public DeleteByIdWithTime() {
    }

    private static final String METHOD = "deleteByIdWithTime";

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        SqlMethod sqlMethod = SqlMethod.LOGIC_DELETE_BY_ID;
        String sql;
        SqlSource sqlSource;
        if (tableInfo.isWithLogicDelete()) {
            sql = String.format(sqlMethod.getSql(), tableInfo.getTableName(), this.sqlLogicSet(tableInfo) + ",deleted_at = unix_timestamp()", tableInfo.getKeyColumn(), tableInfo.getKeyProperty(), tableInfo.getLogicDeleteSql(true, true));
            sqlSource = this.languageDriver.createSqlSource(this.configuration, sql, Object.class);
            return this.addUpdateMappedStatement(mapperClass, modelClass, METHOD, sqlSource);
        }
//        final String method = this.getMethod(SqlMethod.DELETE_BY_ID);
//        else {
//            sqlMethod = SqlMethod.DELETE_BY_ID;
//            sql = String.format(sqlMethod.getSql(), tableInfo.getTableName(), tableInfo.getKeyColumn(), tableInfo.getKeyProperty());
//            sqlSource = this.languageDriver.createSqlSource(this.configuration, sql, Object.class);
//            return this.addDeleteMappedStatement(mapperClass, this.getMethod(sqlMethod), sqlSource);
//        }
        return null;
    }
}
