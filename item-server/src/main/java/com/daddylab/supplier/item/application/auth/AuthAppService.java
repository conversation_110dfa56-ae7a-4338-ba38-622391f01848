package com.daddylab.supplier.item.application.auth;

import com.daddylab.supplier.item.domain.auth.entity.SysMenu;
import com.daddylab.supplier.item.domain.auth.types.LoginState;
import com.daddylab.supplier.item.infrastructure.common.UserContext;

import java.util.List;
import java.util.Optional;

public interface AuthAppService {
    /**
     * sso单点登录验证
     * @param service 当前服务
     * @param ticket 令牌
     * @return 登录状态
     */
    Optional<LoginState> casAuthLogin(String service, String ticket);

    /**
     * 加载用户登录上下文信息
     * @param userId 用户ID
     * @param refreshUserInfo 是否强制刷新缓存
     */
    void loadUserContext(Long userId, boolean refreshUserInfo);

    /**
     * 退出登录
     */
    void logout();

    /**
     * 验证用户访问权限
     *
     * @param userId 用户ID
     * @param uri 接口URI
     */
    void verify(Long userId, String uri);

    /**
     * 获取当前用户基本信息（不带菜单）
     */
    UserContext.UserInfo getCurrentUserInfo();

    /**
     * 获取当前用户菜单
     * @param filterNoAuth 是否过滤掉没有权限的菜单
     */
    List<SysMenu> getCurrentUserMenus(Boolean filterNoAuth);

}
