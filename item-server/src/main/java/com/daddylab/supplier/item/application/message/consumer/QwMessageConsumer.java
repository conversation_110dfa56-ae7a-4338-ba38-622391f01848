package com.daddylab.supplier.item.application.message.consumer;

import com.daddylab.supplier.item.application.message.dto.QwMessageDTO;
import com.daddylab.supplier.item.application.message.support.QwMessageSupport;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatMsg;
import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQConsumerGroup;
import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQTopic;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @class QwMessageConsumer.java
 * @description 企微信息消费
 * @date 2024-04-11 13:36
 */
@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = MQConsumerGroup.QW_MESSAGE_PUSH, topic = MQTopic.QW_MESSAGE_PUSH)
public class QwMessageConsumer implements RocketMQListener<QwMessageDTO> {

    @Autowired
    QwMessageSupport qwMessageSupport;

    @Override
    public void onMessage(QwMessageDTO message) {
        qwMessageSupport.send(message);
    }
}
