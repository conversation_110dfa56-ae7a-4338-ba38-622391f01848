package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataPushLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DataPushLogMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDataPushLogService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据推送记录详细日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Service
public class DataPushLogServiceImpl extends DaddyServiceImpl<DataPushLogMapper, DataPushLog> implements IDataPushLogService {

}
