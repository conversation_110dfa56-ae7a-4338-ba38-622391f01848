package com.daddylab.supplier.item.infrastructure.config;

import cn.hutool.core.util.ReflectUtil;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import org.redisson.Redisson;
import org.redisson.api.RPermitExpirableSemaphore;
import org.redisson.api.RedissonClient;

/**
 * <AUTHOR>
 * @since 2022/5/19
 */
public class RedissonClientInvocationHandler implements InvocationHandler {

    private final Redisson redissonClient;

    public RedissonClientInvocationHandler(Redisson redissonClient) {
        this.redissonClient = redissonClient;
    }

    public static RedissonClient proxy(RedissonClient redissonClient) {
        return (RedissonClient) Proxy
                .newProxyInstance(redissonClient.getClass().getClassLoader(),
                        new Class[]{RedissonClient.class}, new RedissonClientInvocationHandler(
                                ((Redisson) redissonClient)));
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        final Method methodProxy = ReflectUtil
                .getMethod(getClass(), method.getName(), method.getParameterTypes());
        if (methodProxy != null) {
            return methodProxy.invoke(this, args);
        }

        return method.invoke(redissonClient, args);
    }

    public RPermitExpirableSemaphore getPermitExpirableSemaphore(String name) {
        return new PermitExpirableSemaphore(redissonClient.getCommandExecutor(), name);
    }
}
