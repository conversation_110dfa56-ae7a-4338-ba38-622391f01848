package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Data;
import lombok.EqualsAndHashCode;

import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.Id;
import org.javers.core.metamodel.annotation.PropertyName;

import java.io.Serializable;

/**
 * <p>
 * 供应商账户
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BankAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @DiffIgnore
    private Long id;

    @TableField(fill = FieldFill.INSERT)
    @DiffIgnore
    private Long createdAt;

    @TableField(fill = FieldFill.INSERT)
    @DiffIgnore
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    @DiffIgnore
    private Long updatedAt;

    @TableField(fill = FieldFill.UPDATE)
    @DiffIgnore
    private Long updatedUid;

    @DiffIgnore
    private Long deletedAt;

    @TableLogic
    @DiffIgnore
    private Integer isDel;

    /**
     * 供应商ID
     */
    @DiffIgnore
    private Long providerId;

    /**
     * 银行账号
     */
    @PropertyName("银行账号")
    @Id
    private String bankCard;

    /**
     * 开户行
     */
    @PropertyName("开户行名称")
    private String bankDeposit;
    /**
     * 开户行行号
     */
    @PropertyName("开户行行号")
    private String bankNo;

    /**
     * 描述
     */
    @PropertyName("账户信息描述")
    private String description;


}
