package com.daddylab.supplier.item.application.category;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.goods.GoodsClassAPI;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsClassSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsClassSearchResponse;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsClassSearchResponse.Order;
import com.daddylab.supplier.item.domain.category.entity.CategoryEntity;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Category;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CategoryAttr;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CategoryAttrMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CategoryMapper;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/5/15
 */
@Component
@Slf4j
public class WdtGoodsClassFetcher implements PageFetcher {

    private final WdtGateway wdtGateway;
    private final CategoryMapper categoryMapper;
    private final CategoryAttrMapper categoryAttrMapper;
    private final CategoryGateway categoryGateway;

    public WdtGoodsClassFetcher(WdtGateway wdtGateway,
            CategoryMapper categoryMapper,
            CategoryAttrMapper categoryAttrMapper,
            CategoryGateway categoryGateway) {
        this.wdtGateway = wdtGateway;
        this.categoryMapper = categoryMapper;
        this.categoryAttrMapper = categoryAttrMapper;
        this.categoryGateway = categoryGateway;
    }

    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.WDT_GOODS_CLASS;
    }

    @Override
    public int getTotal() {
        return query(1, 1, true).getTotalCount();
    }

    @Override
    public void fetch(long pageIndex, long pageSize, RunContext runContext) {
        final GoodsClassSearchResponse response = query((int) pageIndex, (int) pageSize,
                false);
        if (CollUtil.isEmpty(response.getOrderList())) {
            return;
        }
        List<Order> goodsClasses;
        if (ApplicationContextUtil.isActiveProfile("test", "dev")) {
            goodsClasses = response.getOrderList().stream()
                    .filter(order -> order.getPath().startsWith("-1,1509,"))
                    .peek(order -> {
                        order.setPath(StrUtil.replace(order.getPath(), "-1,1509,", "-1,"));
                        final List<String> pathIds = StrUtil.splitTrim(order.getPath(), ',');
                        final String parentId = pathIds.size() < 2 ? "-1" : pathIds.get(pathIds.size() - 1 - 1);
                        order.setParentId(Integer.parseInt(parentId));
                        final List<String> nameChips = StrUtil.splitTrim(order.getClassName(), "/");
                        order.setClassName(nameChips.get(nameChips.size() - 1));
                    })
                    .collect(Collectors.toList());
        } else {
            goodsClasses = response.getOrderList();
        }
        final Map<Integer, Order> orderMap = goodsClasses.stream()
                .collect(Collectors.toMap(Order::getClassId, Function.identity()));
        final List<Order> leafClasses = goodsClasses.stream()
                .filter(Order::getIsLeaf).collect(Collectors.toList());

        for (Order leafClass : leafClasses) {
            getCategoryOrCreate(leafClass, orderMap);
        }
    }

    private Category getCategoryOrCreate(Order classObj, Map<Integer, Order> orderMap) {
        Category category = getCategory(classObj.getClassName());
        if (category != null) {
            return category;
        }

        Category parentCategory = null;
        if (classObj.getParentId() != -1) {
            final Order parentClassObj = orderMap.get(classObj.getParentId());
            parentCategory = getCategory(parentClassObj.getClassName());
            if (parentCategory == null) {
                parentCategory = getCategoryOrCreate(parentClassObj, orderMap);
            }
        }
        category = new Category();
        category.setName(classObj.getClassName());
        if (parentCategory == null) {
            category.setRootId(0L);
            category.setPath(category.getName());
            category.setParentId(0L);
            category.setLevel(1);
        } else {
            category.setRootId(parentCategory.getRootId() == 0 ? parentCategory.getId() : parentCategory
                    .getRootId());
            category.setPath(parentCategory.getPath() + "/" + category.getName());
            category.setParentId(parentCategory.getId());
            category.setLevel(parentCategory.getLevel() + 1);
        }
        category.setKingDeeId("");
        final String shortName = new CategoryEntity()
                .getShortName(classObj.getClassName(), categoryGateway);
        category.setKingDeeNum(shortName + RandomUtil.randomNumbers(6));
        category.setShortName(shortName);
        category.setIsDel(0);

        categoryMapper.insert(category);

        // 每个品类都设置以下两个默认属性
        createCategoryAttr(category.getId(), "颜色");
        createCategoryAttr(category.getId(), "规格");

        return category;
    }

    private void createCategoryAttr(Long categoryId, String name) {
        final CategoryAttr categoryAttr = new CategoryAttr();
        categoryAttr.setCategoryId(categoryId);
        categoryAttr.setName(name);
        categoryAttr.setIsRequired(1);
        categoryAttrMapper.insert(categoryAttr);
    }

    private Category getCategory(String className) {
        final LambdaQueryWrapper<Category> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Category::getName, className);
        return categoryMapper.selectOne(queryWrapper);
    }

    private GoodsClassSearchResponse query(int pageNo, int pageSize, boolean calcTotal) {
        try {
            final GoodsClassAPI api = wdtGateway.getAPI(GoodsClassAPI.class);
            final Pager pager = new Pager(pageSize, pageNo - 1, calcTotal);
            final GoodsClassSearchParams params = new GoodsClassSearchParams();
            return api.search(params, pager);
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapFetchException(e, fetchDataType());
        }
    }


}
