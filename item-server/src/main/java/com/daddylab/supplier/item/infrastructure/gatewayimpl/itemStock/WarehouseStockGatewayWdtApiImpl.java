package com.daddylab.supplier.item.infrastructure.gatewayimpl.itemStock;

import cn.hutool.core.collection.CollectionUtil;

import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.wms.StockSpecAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.dto.StockSpecSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.dto.StockSpecSearchResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.dto.StockSpecSearchResponse.Data;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.itemStock.enums.StockType;
import com.daddylab.supplier.item.domain.itemStock.gateway.WarehouseStockGateway;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.concurrent.CircuitBreaker;
import org.apache.commons.lang3.concurrent.EventCountCircuitBreaker;
import org.dataloader.DataLoader;
import org.dataloader.DataLoaderFactory;
import org.dataloader.DataLoaderOptions;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class WarehouseStockGatewayWdtApiImpl implements WarehouseStockGateway {

    public static final int MAX_BATCH_SIZE = 1000;
    public static final int BATCH_DELAY_MILLIS = 100;
    public static final int TIMEOUT_SECONDS = 30;
    public static final BigDecimal NEGATIVE_ONE = BigDecimal.valueOf(-1);
    private final ItemSkuGateway itemSkuGateway;
    private final StockSpecAPI stockSpecAPI;
    private DataLoader<String, List<Data>> dataLoader;
    private final CircuitBreaker<Integer> circuitBreaker =
            new EventCountCircuitBreaker(10, 60, TimeUnit.SECONDS, 3);

    public WarehouseStockGatewayWdtApiImpl(
            WdtGateway wdtGateway,
            ItemSkuGateway itemSkuGateway,
            @Qualifier("taskScheduler") ThreadPoolTaskScheduler taskScheduler,
            @Qualifier("commonExecutor") TaskExecutor taskExecutor) {
        stockSpecAPI = wdtGateway.getAPI(StockSpecAPI.class);
        this.itemSkuGateway = itemSkuGateway;
        initDataLoader(taskScheduler, taskExecutor);
    }

    private void initDataLoader(ThreadPoolTaskScheduler taskScheduler, TaskExecutor taskExecutor) {
        if (this.dataLoader == null) {
            this.dataLoader =
                    DataLoaderFactory.newMappedDataLoader(
                            keys -> CompletableFuture.completedFuture(doQueryStock(keys)),
                            DataLoaderOptions.newOptions()
                                    .setMaxBatchSize(BATCH_DELAY_MILLIS)
                                    .setCachingEnabled(false));
            taskScheduler.scheduleWithFixedDelay(this.dataLoader::dispatch, BATCH_DELAY_MILLIS);
        }
    }

    private List<Data> queryStock(List<String> skuNos) {
        if (circuitBreaker.incrementAndCheckState(1)) {
            return queryStockInternal(skuNos);
        } else {
            return Collections.emptyList();
        }
    }

    private List<Data> queryStockInternal(List<String> skuNos) {
        final CompletableFuture<List<List<Data>>> future = dataLoader.loadMany(skuNos);
        if (this.dataLoader.dispatchDepth() >= MAX_BATCH_SIZE) {
            this.dataLoader.dispatch();
        }
        try {
            return future.get(TIMEOUT_SECONDS, TimeUnit.SECONDS).stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
        } catch (InterruptedException | TimeoutException e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "旺店通库存接口查询超时");
        } catch (ExecutionException e) {
            final Throwable cause = e.getCause();
            if (cause instanceof RuntimeException) {
                throw ((RuntimeException) cause);
            }
            throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "旺店通库存接口查询异常");
        }
    }

    private Map<String, List<Data>> doQueryStock(Set<String> skuNos) {
        try {
            int pageSize = 2000;
            int pageNo = 0;
            final Pager pager = new Pager(pageSize, pageNo, true);
            final StockSpecSearchParams params = new StockSpecSearchParams();
            params.setSpecNos(new ArrayList<>(skuNos));
            final StockSpecSearchResponse response = stockSpecAPI.search(params, pager);
            final Map<String, List<Data>> result =
                    skuNos.stream()
                            .collect(Collectors.toMap(Function.identity(), k -> new ArrayList<>()));
            final int total = response.getTotal() == null ? 0 : response.getTotal();
            final List<Data> data = response.getData();
            if (data == null) {
                return result;
            }
            data.forEach(datum -> result.get(datum.getSpecNo()).add(datum));
            final double maxPage = Math.ceil(total / (float) pageSize);
            pageNo++;
            while (pageNo < maxPage) {
                final StockSpecSearchResponse nextPageResponse = stockSpecAPI.search(params, pager);
                nextPageResponse
                        .getData()
                        .forEach(datum -> result.get(datum.getSpecNo()).add(datum));
                pageNo++;
            }
            return result;
        } catch (WdtErpException exception) {
            throw ExceptionPlusFactory.bizException(
                    ErrorCode.GATEWAY_ERROR, "旺店通库存接口查询异常，错误原因：" + exception.getMessage());
        }
    }

    @Override
    public Map<String, BigDecimal> getStock(String skuNo) {
        return getStock(Collections.singletonList(skuNo), Collections.emptyList())
                .getOrDefault(skuNo, Collections.emptyMap());
    }

    @Override
    public Map<String, Map<String, BigDecimal>> getStock(Collection<String> skuNos) {
        return getStock(skuNos, Collections.emptyList());
    }

    @Override
    public BigDecimal getStock(String skuNo, String warehouseNo) {
        return Optional.ofNullable(
                        getStock(
                                Collections.singletonList(skuNo),
                                Collections.singletonList(warehouseNo)))
                .map(map -> map.get(skuNo))
                .map(map -> map.get(warehouseNo))
                .orElse(NEGATIVE_ONE);
    }

    @Override
    public Map<String, BigDecimal> getStock(Collection<String> skuNos, String warehouseNo) {
        return getStock(skuNos, Collections.singletonList(warehouseNo)).entrySet().stream()
                .collect(
                        Collectors.toMap(
                                Entry::getKey,
                                it -> it.getValue().getOrDefault(warehouseNo, NEGATIVE_ONE)));
    }

    @Override
    public Map<String, Map<String, BigDecimal>> getStock(
            Collection<String> skuNos, Collection<String> warehouseNos) {
        if (CollectionUtil.isEmpty(skuNos)) {
            return Collections.emptyMap();
        }
        final List<Data> data = queryStock(new ArrayList<>(skuNos));
        if (CollectionUtil.isEmpty(data)) {
            return Collections.emptyMap();
        }
        final Map<String, Map<String, BigDecimal>> result =
                skuNos.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(), it -> Collections.emptyMap()));
        Stream<Data> stream = data.stream();
        if (CollectionUtil.isNotEmpty(warehouseNos)) {
            stream = stream.filter(it -> warehouseNos.contains(it.getWarehouseNo()));
        }
        result.putAll(
                stream.collect(
                        Collectors.groupingBy(
                                Data::getSpecNo,
                                Collectors.toMap(Data::getWarehouseNo, Data::getStockNum))));
        return result;
    }

    @Override
    public BigDecimal getStockSum(String skuNo) {
        return getStockSum(Collections.singletonList(skuNo), Collections.emptyList()).get(skuNo);
    }

    @Override
    public Map<String, BigDecimal> getStockSum(Collection<String> skuNos) {
        return getStockSum(skuNos, Collections.emptyList());
    }

    @Override
    public Map<String, BigDecimal> getStockSum(
            Collection<String> skuNos, Collection<String> warehouseNos) {
        if (CollectionUtil.isEmpty(skuNos)) {
            return Collections.emptyMap();
        }
        final List<Data> data = queryStock(new ArrayList<>(skuNos));
        final Map<String, BigDecimal> result =
                skuNos.stream()
                        .collect(Collectors.toMap(Function.identity(), it -> NEGATIVE_ONE));
        if (CollectionUtil.isEmpty(data)) {
            return result;
        }
        Stream<Data> stream = data.stream();
        if (CollectionUtil.isNotEmpty(warehouseNos)) {
            stream = stream.filter(it -> warehouseNos.contains(it.getWarehouseNo()));
        }
        result.putAll(
                stream.collect(
                        Collectors.groupingBy(
                                Data::getSpecNo,
                                Collectors.mapping(
                                        Data::getStockNum,
                                        Collectors.reducing(BigDecimal.ZERO, NumberUtil::add)))));
        return result;
    }

    @Override
    public Map<String, Map<StockType, BigDecimal>> getStockDetail(String skuNo) {
        return getStockDetail(Collections.singletonList(skuNo)).get(skuNo);
    }

    @Override
    public Map<String, Map<String, Map<StockType, BigDecimal>>> getStockDetail(
            Collection<String> skuNos) {
        if (CollectionUtil.isEmpty(skuNos)) {
            return Collections.emptyMap();
        }
        final Map<String, Map<String, Map<StockType, BigDecimal>>> result =
                skuNos.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(), it -> Collections.emptyMap()));
        final List<Data> data = queryStock(new ArrayList<>(skuNos));
        if (CollectionUtil.isEmpty(data)) {
            return result;
        }
        result.putAll(
                data.stream()
                        .collect(
                                Collectors.groupingBy(
                                        Data::getSpecNo,
                                        Collectors.toMap(Data::getWarehouseNo, this::dataToMap))));
        return result;
    }

    @NonNull
    private Map<StockType, BigDecimal> dataToMap(Data datum) {
        final Map<StockType, BigDecimal> stockMap = new HashMap<>();
        stockMap.put(StockType.STOCK, datum.getStockNum());
        stockMap.put(StockType.WMS_SYNC_STOCK, datum.getWmsSyncStock());
        stockMap.put(StockType.WMS_STOCK_DIFF, datum.getWmsStockDiff());
        stockMap.put(StockType.UNPAY_NUM, datum.getUnpayNum());
        stockMap.put(StockType.SUBSCRIBE_NUM, datum.getSubscribeNum());
        stockMap.put(StockType.ORDER_NUM, datum.getOrderNum());
        stockMap.put(StockType.SENDING_NUM, datum.getSendingNum());
        stockMap.put(StockType.PURCHASE_NUM, datum.getPurchaseNum());
        stockMap.put(StockType.TRANSFER_NUM, datum.getTransferNum());
        stockMap.put(StockType.TO_PURCHASE_NUM, datum.getToPurchaseNum());
        stockMap.put(StockType.PURCHASE_ARRIVE_NUM, datum.getPurchaseArriveNum());
        stockMap.put(StockType.WMS_PREEMPTY_STOCK, datum.getWmsPreemptyStock());
        stockMap.put(StockType.AVAILABLE_SEND_STOCK, datum.getAvailableSendStock());
        stockMap.put(StockType.PART_PAID_NUM, datum.getPartPaidNum());
        stockMap.put(StockType.REFUND_EXCH_NUM, datum.getRefundExchNum());
        stockMap.put(StockType.REFUND_NUM, datum.getRefundNum());
        stockMap.put(StockType.REFUND_ONWAY_NUM, datum.getRefundOnwayNum());
        stockMap.put(StockType.RETURN_EXCH_NUM, datum.getReturnExchNum());
        stockMap.put(StockType.RETURN_NUM, datum.getReturnNum());
        stockMap.put(StockType.RETURN_ONWAY_NUM, datum.getReturnOnwayNum());
        stockMap.put(StockType.TO_TRANSFER_NUM, datum.getToTransferNum());
        stockMap.put(StockType.WMS_PREEMPTY_DIFF, datum.getWmsPreemptyDiff());
        stockMap.put(StockType.LOCK_NUM, datum.getLockNum());

        return stockMap;
    }

    @Override
    public BigDecimal getItemStockSum(String itemNo, String warehouseNo) {
        return getItemStockSum(
                        Collections.singletonList(itemNo), Collections.singletonList(warehouseNo))
                .getOrDefault(itemNo, BigDecimal.ZERO);
    }

    @Override
    public BigDecimal getItemStockSum(String itemNo) {
        return getItemStockSum(Collections.singletonList(itemNo), Collections.emptyList())
                .getOrDefault(itemNo, BigDecimal.ZERO);
    }

    @Override
    public Map<String, BigDecimal> getItemStockSum(Collection<String> itemNos) {
        return getItemStockSum(itemNos, Collections.emptyList());
    }

    /**
     * 批量获取指定商品编码商品的总库存，当给定仓库编号集合不为空时，只统计指定仓库内的库存
     *
     * @param itemNos 商品编码集合
     * @param warehouseNos 仓库编码集合
     * @return Map{商品编码:总库存}
     */
    public Map<String, BigDecimal> getItemStockSum(
            Collection<String> itemNos, Collection<String> warehouseNos) {
        if (CollectionUtil.isEmpty(itemNos)) {
            return Collections.emptyMap();
        }
        final Map<String, BigDecimal> result =
                itemNos.stream()
                        .collect(Collectors.toMap(Function.identity(), k -> BigDecimal.ZERO));
        final Map<String, List<String>> skuCodesMap = itemSkuGateway.getSkuCodesBatch(itemNos);
        if (CollectionUtil.isEmpty(skuCodesMap)) {
            return result;
        }
        final List<String> skuCodes =
                skuCodesMap.values().stream()
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());
        final List<Data> data = queryStock(new ArrayList<>(skuCodes));
        if (CollectionUtil.isEmpty(data)) {
            return result;
        }
        Stream<Data> stream = data.stream();
        if (!CollectionUtil.isEmpty(warehouseNos)) {
            stream = stream.filter(v -> warehouseNos.contains(v.getWarehouseNo()));
        }
        result.putAll(
                stream.collect(
                        Collectors.groupingBy(
                                Data::getGoodsNo,
                                Collectors.mapping(
                                        Data::getStockNum,
                                        Collectors.reducing(BigDecimal.ZERO, NumberUtil::add)))));
        return result;
    }
}
