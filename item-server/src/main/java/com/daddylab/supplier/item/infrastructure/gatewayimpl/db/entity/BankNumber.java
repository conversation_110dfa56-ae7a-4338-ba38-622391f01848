package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 银行联行号
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BankNumber implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer updatedUid;

    /**
     * 是否删除：0否1是
     */
    @TableLogic
    private Integer isDel;

    /**
     * 上级地区ID
     */
    private Integer areaPid;

    /**
     * 地区ID
     */
    private Integer areaId;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 行号
     */
    private String bankNo;

    /**
     * 银行ID 1:中国工商银行 2:中国农业银行 3:中国银行 4:中国建设银行...
     */
    private Integer bankId;

    /**
     * 联系电话
     */
    private String tel;

    /**
     * 地址
     */
    private String address;


}
