package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 售后单退货物流信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AfterSalesReturnLogisticsInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 退货物流单号
     */
    private String returnLogisticsNo;

    /**
     * 物流公司名称
     */
    private String returnLogisticsName;

    /**
     * 关联的退换单号
     */
    private String returnOrderNo;

    /**
     * 退货仓库编号
     */
    private String returnWarehouseNo;

    /**
     * 退回地址
     */
    private String fullAddress;

    /**
     * 退货联系人
     */
    private String contacts;

    /**
     * 退货联系电话
     */
    private String tel;


}
