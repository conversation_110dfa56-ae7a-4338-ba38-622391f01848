package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtGoodsSn;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * ERP序列号管理页面的数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
public interface IWdtGoodsSnService extends IDaddyService<WdtGoodsSn> {

}
