package com.daddylab.supplier.item.infrastructure.events;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.ApplicationEvent;
import org.springframework.core.ResolvableType;
import org.springframework.core.ResolvableTypeProvider;

/**
 * <AUTHOR>
 * @class SaveEvent.java
 * @description 保存事件监听
 * @date 2024-04-09 15:17
 */
@Getter
@Accessors(chain = true)
public class SaveEvent<T> extends ApplicationEvent implements ResolvableTypeProvider {
    private static final long serialVersionUID = -3539860860961013605L;
    private final T model;
    private final boolean isNewRecord;
    @Setter()
    private long operatorId = 0L;

    public SaveEvent(Object source, T model, boolean isNewRecord) {
        super(source);
        this.model = model;
        this.isNewRecord = isNewRecord;
    }

    @Override
    public ResolvableType getResolvableType() {
        return ResolvableType.forClassWithGenerics(getClass(), ResolvableType.forInstance(model));
    }
}