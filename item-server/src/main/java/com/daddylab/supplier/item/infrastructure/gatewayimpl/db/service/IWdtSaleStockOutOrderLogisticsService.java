package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrderLogistics;

/**
 * <p>
 * 旺店通销售出库单物流单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
public interface IWdtSaleStockOutOrderLogisticsService extends IDaddyService<WdtSaleStockOutOrderLogistics> {

}
