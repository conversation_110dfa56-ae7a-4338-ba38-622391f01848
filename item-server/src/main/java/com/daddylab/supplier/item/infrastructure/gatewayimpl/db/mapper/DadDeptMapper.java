package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DadDept;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 部门表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
@DS("authDb")
@Repository
public interface DadDeptMapper extends DaddyBaseMapper<DadDept> {

}
