/*
package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryMode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

*/
/**
 * <p>
 * 店铺库存设置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 *//*

@Data
@EqualsAndHashCode(callSuper = false)
public class ShopInventorySetting implements Serializable {

    private static final long serialVersionUID = 1L;

    */
/**
     * id
     *//*

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    */
/**
     * 创建时间createAt
     *//*

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    */
/**
     * 创建人updateUser
     *//*

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    */
/**
     * 更新时间updateAt
     *//*

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    */
/**
     * 更新人updateUser
     *//*

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    */
/**
     * 是否已删除
     *//*

    @TableLogic
    private Integer isDel;

    */
/**
     * 删除时间
     *//*

    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    */
/**
     * 同步频次（单位秒，实时设置为0即可）
     *//*

    private Integer syncFrequency;

    */
/**
     * 安全库存
     *//*

    private Integer safetyThreshold;

    */
/**
     * 店铺ID
     *//*

    private Long shopId;

    */
/**
     * 店铺编号
     *//*

    private String shopNo;

    */
/**
     * 库存模式 0 共享, 1 锁定
     *//*

    private InventoryMode inventoryMode;


}
*/
