package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.shop.enums.ShopStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DataSource;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryMode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffIgnore;

import java.io.Serializable;

/**
 * 店铺
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Shop implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺编号
     */
    private String sn;

    /**
     * 店铺名称
     */
    private String name;

    /**
     * 店铺账号
     */
    private String account;

    /**
     * 店铺链接
     */
    private String link;

    /**
     * 店铺LOGO
     */
    private String logo;

    /**
     * 平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城
     */
    private Platform platform;

    /**
     * 状态 0:关闭 1:准备中 2:营业
     */
    private ShopStatus status;

    /**
     * 删除时间
     */
    private Long deletedAt;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 金蝶ID
     */
    @DiffIgnore
    private String kingDeeId;

    /**
     * 数据来源
     */
    private DataSource source;

    /**
     * 是否在旺店通被禁用
     */
    private Boolean wdtIsDisabled;

    /**
     * 合作方，冗余字段，方便查询
     */
    private String businessLine;

    public String getBusinessLine() {
        return DivisionLevelValueEnum.filterCorpType(businessLine);
    }

    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Boolean isBusinessLine0;

    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Boolean isBusinessLine1;

    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Boolean isBusinessLine2;

    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Boolean isBusinessLine3;

    /**
     * 主体公司
     */
    private String mainCompany;
    
    /**
     * 商家后台，店铺ID
     */
    private Long mallShopId;


}
