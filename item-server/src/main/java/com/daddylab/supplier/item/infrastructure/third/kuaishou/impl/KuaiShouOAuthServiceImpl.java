package com.daddylab.supplier.item.infrastructure.third.kuaishou.impl;

import com.daddylab.supplier.item.infrastructure.third.config.KuaiShouConfig;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.KuaiShouOAuthService;
import com.kuaishou.merchant.open.api.KsMerchantApiException;
import com.kuaishou.merchant.open.api.client.oauth.OauthAccessTokenKsClient;
import com.kuaishou.merchant.open.api.response.oauth.KsAccessTokenResponse;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/4/9
 */
@Service
public class KuaiShouOAuthServiceImpl implements KuaiShouOAuthService {
    private KuaiShouConfig kuaiShouConfig;
    protected OauthAccessTokenKsClient oauthAccessTokenClient;

    public KuaiShouOAuthServiceImpl(KuaiShouConfig config) {
        this.kuaiShouConfig = config;
        oauthAccessTokenClient = new OauthAccessTokenKsClient(kuaiShouConfig.getAppKey(),
                kuaiShouConfig.getSignSecret(),
                kuaiShouConfig.getApiUrl());
    }

    @Override
    public KsAccessTokenResponse getAccessToken(String code) {
        try {
            return oauthAccessTokenClient.getAccessToken(code);
        } catch (KsMerchantApiException e) {
            throw new RuntimeException("获取快手授权令牌异常:" + e.getErrorMsg(), e);
        }
    }

    @Override
    public KsAccessTokenResponse refreshAccessToken(String refreshToken) {
        try {
            return oauthAccessTokenClient.refreshAccessToken(refreshToken);
        } catch (KsMerchantApiException e) {
            throw new RuntimeException("刷新快手授权令牌异常:" + e.getErrorMsg(), e);
        }
    }

    @Override
    public String getAuthorizationUrl(String redirectUrl, String state) {
        return String.format(
                "https://open.kwaixiaodian.com/oauth/authorize?app_id=%s&redirect_uri=%s&scope=%s&response_type=code&state=%s",
                kuaiShouConfig.getAppKey(),
                redirectUrl,
                "merchant_comment,merchant_distribution,merchant_funds,merchant_item,merchant_logistics,merchant_order,merchant_promotion,merchant_refund,merchant_scm,merchant_user,merchant_video,user_info",
                state);
    }
}
