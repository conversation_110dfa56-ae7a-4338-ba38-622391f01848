package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import cn.hutool.core.util.StrUtil;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.common.trans.CommaSerialTransMapper;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BusinessLine;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 仓库列表
 *
 * <AUTHOR>
 * @since 2022-03-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class Warehouse implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  @TableField(fill = FieldFill.INSERT)
  private Long createdAt;

  @TableField(fill = FieldFill.UPDATE)
  private Long updatedAt;

  @TableField(fill = FieldFill.INSERT)
  private Long createdUid;

  @TableField(fill = FieldFill.UPDATE)
  private Long updatedUid;

  @TableLogic private Integer isDel;

  /** 仓库编号 */
  private String no;

  /** 仓库名称 */
  private String name;

  /** 1.内部、2.自流转、3.平台、4.京东沧海、5.抖音云仓、6分销委外 */
  private Integer wmsType;

  /** 联系人 */
  private String contacts;

  /** 联系方式，固话 */
  private String tel;

  /** 联系方式，手机 */
  private String phone;

  /** 省 */
  @JsonIgnore private String province;

  /** 城市 */
  @JsonIgnore private String city;

  /** 区 */
  @JsonIgnore private String district;

  /** 仓库地址 */
  @JsonIgnore private String address;

  /** 状态。1.正常，2.停用 */
  private Integer state;

  /** 邮编 */
  private String zip;

  /** 备注 */
  private String remark;

  /** 仓库权限关联到哪个旺店通APP */
  private String appKey;

  /** 是否是虚拟仓 */
  private Integer isVirtualWarehouse;

  /**
   * @return 完整地址
   */
  @JsonGetter("address")
  public String getFullAddress() {
    return StrUtil.concat(true, province, city, district, address);
  }

  /** 订单员 */
  private String orderPersonnel;

  public List<Long> selectOrderPersonnelIds() {
    return CommaSerialTransMapper.INSTANCE.strToLongList(this.orderPersonnel);
  }

  /** 合作模式（业务线）：0电商 1老爸抽检 2绿色家装 3商家入驻 */
  private String businessLine;

  public List<Integer> getBusinessLineList() {
    return businessLine != null
        ? CommaSerialTransMapper.INSTANCE.strToIntegerList(businessLine).stream()
            .filter(DivisionLevelValueEnum::isCorpType)
            .collect(Collectors.toList())
        : null;
  }

  public String getBusinessLineStr() {
    if (StrUtil.isBlank(this.businessLine)) {
      return StrUtil.EMPTY;
    }

    List<String> list = new ArrayList<>();
    String[] split = this.businessLine.split(",");
    for (String s : split) {
      BusinessLine enumByValue = IEnum.getEnumByValue(BusinessLine.class, Integer.valueOf(s));
      if (DivisionLevelValueEnum.isCorpType(enumByValue.getValue())) {
        list.add(enumByValue.getDesc());
      }
    }
    return String.join(StrUtil.COMMA, list);
  }

  @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
  private Boolean isBusinessLine0;

  @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
  private Boolean isBusinessLine1;

  @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
  private Boolean isBusinessLine2;

  @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
  private Boolean isBusinessLine3;

  public void setBusinessLineWithVal(Integer businessLine) {
    if (businessLine == 0) {
      this.isBusinessLine0 = true;
    }
    if (businessLine == 1) {
      this.isBusinessLine1 = true;
    }
    if (businessLine == 2) {
      this.isBusinessLine2 = true;
    }
    if (businessLine == 3) {
      this.isBusinessLine3 = true;
    }
  }

  private Integer inventoryRatio;

  public Integer getInventoryRatio() {
    return Objects.isNull(inventoryRatio) ? 0 : inventoryRatio;
  }

  private Integer version;

  public Integer getVersion() {
    return Objects.isNull(version) ? 1 : version;
  }

  /** 多个售后客服人员ID */
  private String afterSaleStaffStr;
}
