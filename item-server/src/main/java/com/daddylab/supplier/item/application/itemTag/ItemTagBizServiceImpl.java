package com.daddylab.supplier.item.application.itemTag;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTag;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemTagService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/6/18
 */
@Service
public class ItemTagBizServiceImpl implements ItemTagBizService {
    @Autowired
    private IItemTagService itemTagService;

    @Override
    public void addTag(Long itemId, String tagId) {
        final List<ItemTag> itemTags = itemTagService.listByItemId(itemId);
        for (ItemTag itemTag : itemTags) {
            if (Objects.equals(itemTag.getTagId(), tagId)) {
                return;
            }
        }
        final ItemTag itemTag = new ItemTag();
        itemTag.setItemId(itemId);
        itemTag.setTagId(tagId);
        itemTagService.save(itemTag);
    }

    @Override
    public void removeTag(Long itemId, String tagId) {
        final List<ItemTag> itemTags = itemTagService.listByItemId(itemId);
        for (ItemTag itemTag : itemTags) {
            if (Objects.equals(itemTag.getTagId(), tagId)) {
                itemTagService.removeById(itemTag);
            }
        }
    }

    @Override
    public void setItemTag(Long itemId, Collection<String> tagIds) {
        final List<ItemTag> itemTags = itemTagService.listByItemId(itemId);
        final Set<String> tagIdsToAdd = new HashSet<>(tagIds);
        itemTags.stream().map(ItemTag::getTagId).forEach(tagIdsToAdd::remove);
        if (!tagIdsToAdd.isEmpty()) {
            itemTagService.saveBatch(tagIdsToAdd.stream().map(v -> {
                final ItemTag itemTag = new ItemTag();
                itemTag.setItemId(itemId);
                itemTag.setTagId(v);
                return itemTag;
            }).collect(Collectors.toList()));
        }
        final Set<ItemTag> tagsToRemove = itemTags.stream()
                                                  .filter(v -> !tagIds.contains(v.getTagId()))
                                                  .collect(Collectors.toSet());
        if (!tagsToRemove.isEmpty()) {
            itemTagService.removeByIdsWithTime(tagsToRemove.stream().map(ItemTag::getId).collect(Collectors.toList()));
        }
    }

    @Override
    public List<String> listByItemId(Long itemId) {
        return itemTagService.listByItemId(itemId).stream().map(ItemTag::getTagId).collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<String>> listByItemIds(Collection<Long> itemIds) {
        return itemTagService.listByItemIds(itemIds)
                             .stream()
                             .collect(Collectors.groupingBy(ItemTag::getItemId,
                                                            Collectors.mapping(ItemTag::getTagId,
                                                                               Collectors.toList())));
    }
}
