package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.reservePlan;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.CommonCalculator;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.ActivityQuantitySuiteDO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.ActivityTimeSuitePriceDO;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderDetailWrapperService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Semaphore;

/**
 * <AUTHOR> up
 * @date 2023年09月27日 5:04 PM
 */
@Slf4j
@Component
public class ActivitySuiteTemp extends ActivityCalculateTemplate {

    @Resource
    WdtOrderDetailMapper wdtOrderDetailMapper;
    @Resource
    CommonCalculator commonCalculator;
    @Resource
    IWdtOrderDetailWrapperService iWdtOrderDetailWrapperService;

    @Override
    protected void considerTime(TimeBO timeBO) {
        // 主处理逻辑和单品处理类似。
        List<ActivityTimeSuitePriceDO> list = wdtOrderDetailMapper.selectStockOutSuiteTimePurchase(timeBO.getOperateMonth());
        if (CollUtil.isEmpty(list)) {
            return;
        }

        list.forEach(val -> {
            try {
                String suiteNo = val.getSuiteNo();
                BigDecimal activityCostPrice = val.getPriceCost();
                Long payStartTime = val.getStartTime();
                Long payEndTime = val.getEndTime();
                // 计算此组合装下构成单品的活动成本
                commonCalculator.getSkuCostPriceUnderSuiteNo(suiteNo, activityCostPrice).forEach(skuVo -> {
                    String skuCode = skuVo.getSkuCode();
                    BigDecimal needUpdatePrice = skuVo.getUnitPrice();
                    iWdtOrderDetailWrapperService.lambdaUpdate()
                            .set(WdtOrderDetailWrapper::getPrice, needUpdatePrice)
                            .eq(WdtOrderDetailWrapper::getSuiteNo, suiteNo)
                            .eq(WdtOrderDetailWrapper::getSkuCode, skuCode)
                            .eq(WdtOrderDetailWrapper::getType, 2)
                            .eq(val.getPlatformType() != 0, WdtOrderDetailWrapper::getPlatformType, val.getPlatformType())
                            .between(WdtOrderDetailWrapper::getPayTime, payStartTime, payEndTime)
                            .update();
                });
            } catch (Exception e) {
                log.error("ActivityTimePrice doPriceProcess tockOutSuite update sku price fail val:{}", val, e);
            }
        });
    }

    @Override
    protected void considerQuantity(TimeBO timeBO) {
        List<ActivityQuantitySuiteDO> list = wdtOrderDetailMapper.selectSuiteQuantityPurchase(timeBO.getOperateMonth());
        if (CollUtil.isEmpty(list)) {
            return;
        }

        Semaphore semaphore = new Semaphore(2);
        CountDownLatch countDownLatch = new CountDownLatch(list.size());

        list.forEach(activityQuantitySuiteDo -> ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            try {
                semaphore.acquire();
                List<CommonCalculator.SkuPriceUnderSuiteNo> skuCostPriceUnderSuiteNo = commonCalculator.getSkuCostPriceUnderSuiteNo(activityQuantitySuiteDo.getSuiteNo()
                        , activityQuantitySuiteDo.getPriceCost());
                skuCostPriceUnderSuiteNo.forEach(skuPrice -> {
                    int count = activityQuantitySuiteDo.getNumCost().intValue() * skuPrice.getCount();
                    iWdtOrderDetailWrapperService.lambdaUpdate().set(WdtOrderDetailWrapper::getPrice, skuPrice.getUnitPrice())
                            .eq(WdtOrderDetailWrapper::getSuiteNo, activityQuantitySuiteDo.getSuiteNo())
                            .eq(WdtOrderDetailWrapper::getSkuCode, skuPrice.getSkuCode())
                            .eq(activityQuantitySuiteDo.getPlatformType() != 0,
                                    WdtOrderDetailWrapper::getPlatformType, activityQuantitySuiteDo.getPlatformType())
                            .eq(WdtOrderDetailWrapper::getType, 2)
                            .between(WdtOrderDetailWrapper::getPayTime, activityQuantitySuiteDo.getStartTime()
                                    , activityQuantitySuiteDo.getEndTime())
                            .orderByAsc(WdtOrderDetailWrapper::getId)
                            .last("limit " + count)
                            .update();

                });
            } catch (Exception e) {
                log.error("ActivityQuantityPrice doPriceProcess suiteHandler update sku price fail.val:{}", activityQuantitySuiteDo, e);
            } finally {
                semaphore.release();
                countDownLatch.countDown();
            }
        }));

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("ActivityQuantityPrice doPriceProcess suiteHandler countDownLatch await", e);
        }
    }
}
