package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsAbnormalityLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderLogisticsAbnormalityLogMapper;

/**
 * <p>
 * 订单物流异常记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
public interface IOrderLogisticsAbnormalityLogService extends IDaddylabServicePlus<OrderLogisticsAbnormalityLog, OrderLogisticsAbnormalityLogMapper> {


    void newNonExceptionMsgLog(Long abnormalityId, int logType, String msg);
}
