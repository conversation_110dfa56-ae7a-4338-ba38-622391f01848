package com.daddylab.supplier.item.infrastructure.gatewayimpl.item;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.domain.item.gateway.ItemPriceGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemPrice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemPriceMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemPriceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/19 11:53 上午
 * @description
 */
@Slf4j
@Service
public class ItemPriceGatewayImpl implements ItemPriceGateway {

    @Autowired
    IItemPriceService iItemPriceService;

    @Autowired
    ItemPriceMapper itemPriceMapper;

    @Override
    public List<ItemPrice> getPriceList(Long itemId) {
        QueryWrapper<ItemPrice> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ItemPrice::getItemId, itemId);
        return iItemPriceService.list(wrapper);
    }

    @Override
    public void saveOrUpdateBatchPrice(List<ItemPrice> list) {
        iItemPriceService.saveOrUpdateBatch(list);
    }

    @Override
    public void removePriceWithIds(List<Long> itemPriceIds) {
        if (CollUtil.isNotEmpty(itemPriceIds)) {
            iItemPriceService.removeByIdsWithTime(itemPriceIds);
        }
    }

//    @Override
//    public BigDecimal getCostPrice(String skuCode, Long targetTime) {
//        BigDecimal price = itemPriceMapper.getPrice(skuCode, ItemPriceType.PROCUREMENT.getValue());
//        return Optional.ofNullable(price).orElse(BigDecimal.ZERO);
//    }

}
