package com.daddylab.supplier.item.application.warehouse;

import com.alibaba.cola.dto.MultiResponse;
import com.daddylab.supplier.item.application.afterSalesForwarding.types.AfterSalesForwardingShareWarehouseCmd;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/4/22
 */
public interface WarehouseGateway {

    Optional<Long> warehouseId(String warehouseNo);

    Optional<Warehouse> getWarehouse(String warehouseNo);

    Optional<Warehouse> cacheGetWarehouse(String warehouseNo, boolean refresh);

    List<Warehouse> getWarehouseListByOrderPersonnelIds(List<Long> orderPersonnelIds);

    List<Warehouse> getWarehouseListByNos(List<String> warehouseNos);

    Map<String,String> getOrderPersonnelByNames(Collection<String> names);

    MultiResponse<Warehouse> shareQueryWarehouseByName(AfterSalesForwardingShareWarehouseCmd warehouseCmd);

    MultiResponse<Warehouse> queryWarehouseByName(String warehouseCmd);

}
