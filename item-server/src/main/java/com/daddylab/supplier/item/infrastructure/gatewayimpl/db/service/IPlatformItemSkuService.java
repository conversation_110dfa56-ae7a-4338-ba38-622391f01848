package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.domain.platformItem.vo.ListOuterSkuCodeQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.PlatformSkuRefQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemSkuMapper;
import com.daddylab.supplier.item.types.platformItem.ComposeSkuPlatformItem;
import com.daddylab.supplier.item.types.platformItem.PlatformSkuOfSameSkuCombinationItem;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 平台商品（投放到其他平台的商品）SKU维度 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
public interface IPlatformItemSkuService extends IDaddylabServicePlus<PlatformItemSku, PlatformItemSkuMapper> {

    /**
     * 后端商品SKU关联的平台商品SKU
     *
     * @param query 查询参数模型
     */
    List<PlatformItemSku> platformSkuQuery(PlatformSkuRefQuery query);

    /**
     * 组合装套内单品关联的平台商品SKU
     *
     * @param query 查询参数模型
     */
    List<ComposeSkuPlatformItem> platformSkuQueryOfComposeSku(PlatformSkuRefQuery query);

    /**
     * 查询与指定组合装包含了相同单品的其他组合装关联的平台商品（某些场景返回的平台商品可能重复）
     *
     * @param query 查询参数模型
     */
    List<PlatformSkuOfSameSkuCombinationItem> platformSkuQueryOfSameSkuCombinationItem(PlatformSkuRefQuery query);


    /**
     * 根据平台商品ID查询
     * @param platformItemIds 平台商品ID
     * @return 平台商品SKU
     */
    List<PlatformItemSku> listByPlatformItemId(Collection<Long> platformItemIds);

    /**
     * 查询平台商品SKU
     * @param platformItemId 平台商品ID
     * @return
     */
    List<PlatformItemSku> listByPlatformItemId(Long platformItemId);
    
    /**
     * 根据外部平台商品SKU ID查询
     * @param outerSkuIds 外部平台商品SKU ID
     * @return 平台商品SKU
     */
    List<PlatformItemSku> listByOuterSkuIds(Collection<String> outerSkuIds);

    /**
     * 查询平台商品的外部编码
     * @param query 查询条件
     * @return 外部编码列表
     */
    List<String> listOuterSkuCode(ListOuterSkuCodeQuery query);
}
