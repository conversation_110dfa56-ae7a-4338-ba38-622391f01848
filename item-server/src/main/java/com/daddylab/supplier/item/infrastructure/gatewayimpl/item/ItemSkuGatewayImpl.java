package com.daddylab.supplier.item.infrastructure.gatewayimpl.item;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.PageResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.common.trans.ItemTransMapper;
import com.daddylab.supplier.item.controller.item.dto.ItemAttrDto;
import com.daddylab.supplier.item.controller.item.dto.ItemSkuSpecVo;
import com.daddylab.supplier.item.controller.item.dto.ItemSpecPageQuery;
import com.daddylab.supplier.item.domain.item.data.ItemAndSkuCodeInfo;
import com.daddylab.supplier.item.domain.item.data.SkuCodeInfo;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuAttrRefDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuIsGiftDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuWarehouseNoDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemSkuAttrRefMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemSkuMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.github.yulichang.toolkit.MPJWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/19 2:02 下午
 * @description
 */
@Service
@Slf4j
public class ItemSkuGatewayImpl implements ItemSkuGateway {

    @Autowired
    IItemSkuService iItemSkuService;

    @Autowired
    ItemSkuAttrRefMapper itemSkuAttrRefMapper;

    @Autowired
    IItemAttrService iItemAttrService;

    @Autowired
    IItemSkuAttrRefService iItemSkuAttrRefService;

    @Autowired
    ItemSkuMapper itemSkuMapper;

    @Autowired
    IStockInOrderDetailService iStockInOrderDetailService;

    @Autowired
    IStockOutOrderDetailService iStockOutOrderDetailService;

    @Override
    public List<ItemSku> getSkuList(Long itemId) {
        QueryWrapper<ItemSku> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ItemSku::getItemId, itemId);
        return iItemSkuService.list(wrapper);
    }

    @Override
    public List<String> getSkuCodesByItemCode(String itemCode) {
        return itemSkuMapper.getSkuCodes(itemCode);
    }

    @Override
    public String getSkuCodeBySkuId(Long skuId) {
        return iItemSkuService.lambdaQuery().eq(ItemSku::getId, skuId).select(ItemSku::getSkuCode)
                .oneOpt().map(ItemSku::getSkuCode).orElse(null);
    }

    @Override
    public Map<String, List<String>> getSkuCodesBatch(Collection<String> itemCodes) {
        if (CollectionUtil.isEmpty(itemCodes)) {
            return Collections.emptyMap();
        }
        return itemSkuMapper.getItemAndSkuCodeInfoBatchByItemNos(itemCodes).stream()
                .collect(Collectors
                        .groupingBy(ItemAndSkuCodeInfo::getItemCode,
                                Collectors.mapping(ItemAndSkuCodeInfo::getSkuCode,
                                        Collectors.toList())));
    }

    @Override
    public List<Long> getSkuIdList(Long itemId) {
        QueryWrapper<ItemSku> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ItemSku::getItemId, itemId).select(ItemSku::getId);
        return iItemSkuService.list(wrapper).stream().map(ItemSku::getId).collect(Collectors.toList());
    }

    @Override
    public List<SkuAttrRefDO> getSkuAttrList(List<Long> skuIdList) {
        return CollectionUtil.isEmpty(skuIdList) ? new LinkedList<>() : itemSkuAttrRefMapper.getSkuAttrList(skuIdList);
    }

    @Override
    public List<SkuAttrRefDO> getSkuAttrList(String skuCode) {
        return itemSkuAttrRefMapper.getSkuAttrListByCode(skuCode);
    }

    @Override
    public String getSkuAttrListStr(String skuCode) {
        List<SkuAttrRefDO> skuAttrList = getSkuAttrList(skuCode);
        if (CollectionUtil.isEmpty(skuAttrList)) {
            return "";
        }
        List<String> list = new LinkedList<>();
        for (SkuAttrRefDO skuAttrRefDo : skuAttrList) {
            list.add(skuAttrRefDo.getAttrName() + ":" + skuAttrRefDo.getAttrValue());
        }
        return StrUtil.join("|", list);
    }

    @Override
    public void traverseSkuCodeInfo(int batchSize, Consumer<List<SkuCodeInfo>> consumer) {
        long cursor = 0L;
        while (true) {
            final List<ItemSku> list = iItemSkuService.lambdaQuery().select(ItemSku::getId, ItemSku::getItemId,
                            ItemSku::getSkuCode, ItemSku::getProviderSpecifiedCode)
                    .gt(ItemSku::getId, cursor).orderByAsc(ItemSku::getId).last("LIMIT " + batchSize).list();
            if (list.isEmpty()) {
                break;
            }
            final List<SkuCodeInfo> skuCodeInfos = list.stream().map(ItemTransMapper.INSTANCE::toSkuCodeInfo).collect(Collectors.toList());
            consumer.accept(skuCodeInfos);
            cursor = skuCodeInfos.get(skuCodeInfos.size() - 1).getSkuId();
        }
    }

    @Override
    public Long saveItemSkuReturnId(ItemSku itemSku) {
        Assert.notNull(itemSku.getItemId(), "表item_sku,item_id不得为空");
        Assert.hasText(itemSku.getSkuCode(), "表item_sku,sku_code不得为空");
        iItemSkuService.saveOrUpdate(itemSku);
        return itemSku.getId();
    }

    @Override
    public void saveOrUpdateBatchItemSku(List<ItemSku> list) {
        iItemSkuService.saveOrUpdateBatch(list);
    }


    @Override
    public void updateItemAttr(ItemAttrDto itemAttrDto) {
        LambdaUpdateWrapper<ItemAttr> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ItemAttr::getAttrValue, itemAttrDto.getValue());
        updateWrapper.set(ItemAttr::getUpdatedAt, DateUtil.currentSeconds());
        updateWrapper.eq(ItemAttr::getId, itemAttrDto.getItemAttrDbId());
        iItemAttrService.update(updateWrapper);
    }


    @Override
    public Long saveItemAttrReturnId(ItemAttr itemAttr) {
        Assert.notNull(itemAttr.getItemId(), "表item_attr,item_id不得为空");
        Assert.notNull(itemAttr.getAttrId(), "表item_attr,attr_id不得为空");
        Assert.notNull(itemAttr.getCategoryId(), "表item_attr,category_id不得为空");

        QueryWrapper<ItemAttr> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ItemAttr::getItemId, itemAttr.getItemId()).eq(ItemAttr::getAttrId, itemAttr.getAttrId())
                .eq(ItemAttr::getAttrValue, itemAttr.getAttrValue());
        final ItemAttr one = iItemAttrService.getOne(queryWrapper);
        if (Objects.nonNull(one)) {
            return one.getId();
        } else {
            iItemAttrService.saveOrUpdate(itemAttr);
            return itemAttr.getId();
        }

//        iItemAttrService.save(itemAttr);
//        return itemAttr.getId();
    }

    @Override
    public void saveBatchItemSkuAttrRef(List<ItemSkuAttrRef> list) {
        iItemSkuAttrRefService.saveOrUpdateBatch(list);
    }

    @Override
    public void setSkuKingDeeId(Long skuId, String kingDeeId) {
        itemSkuMapper.saveKingDeeId(kingDeeId, skuId);
    }

    @Override
    public Integer getSkuCount(Long itemId) {
        QueryWrapper<ItemSku> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ItemSku::getItemId, itemId);
        return iItemSkuService.count(queryWrapper);
    }

    @Override
    public Boolean isSpecialSkuCodeRepeat(String specialCode) {
        QueryWrapper<ItemSku> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ItemSku::getProviderSpecifiedCode, specialCode);
        return iItemSkuService.count(queryWrapper) > 0;
    }

    @Override
    public Boolean isSkuBarCodeRepeat(String barCode) {
        QueryWrapper<ItemSku> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ItemSku::getBarCode, barCode);
        return iItemSkuService.count(queryWrapper) > 0;
    }

    @Override
    public Optional<ItemSku> getByItemSkuId(Long id) {
        return Optional.ofNullable(iItemSkuService.getById(id));
    }

    @Override
    public Optional<String> getLatestSkuCode(Long itemId) {
        return Optional.ofNullable(itemSkuMapper.getLatestSkuCode(itemId));
    }

    @Override
    public Boolean isSyncKingDee(String skuCode) {
        LambdaQueryWrapper<ItemSku> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ItemSku::getSkuCode, skuCode);
        List<ItemSku> list = iItemSkuService.list(wrapper);
        if (CollectionUtil.isEmpty(list)) {
            return false;
        }
        String kingDeeId = list.get(0).getKingDeeId();
        return StringUtil.isNotBlank(kingDeeId);
    }

    @Override
    public ItemSku getBySkuCode(String skuCode) {
        LambdaQueryWrapper<ItemSku> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ItemSku::getSkuCode, skuCode).or().eq(ItemSku::getProviderSpecifiedCode, skuCode);
        return iItemSkuService.getOne(wrapper);
    }

    @Override
    public ItemSku getItemSku(Long itemId, String skuCode, String warehouseNo) {
        LambdaQueryWrapper<ItemSku> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ItemSku::getItemId, itemId);
        wrapper.eq(ItemSku::getSkuCode, skuCode);
        wrapper.eq(ItemSku::getWarehouseNo, warehouseNo);
        return iItemSkuService.getOne(wrapper);
    }

    @Override
    public List<SkuWarehouseNoDO> getWarehouseNoList(List<String> skuCodeList) {
        Assert.state(CollectionUtils.isNotEmpty(skuCodeList), "skuCodeList不得为空");
        return itemSkuMapper.getWarehouseNoList(skuCodeList);
    }

    @Override
    public List<SkuIsGiftDO> getIsGiftList(List<String> skuCodeList) {
        Assert.state(CollectionUtils.isNotEmpty(skuCodeList), "skuCodeList不得为空");
        return itemSkuMapper.getIsGiftList(skuCodeList);
    }

    @Override
    public Boolean verifySkuCode(String skuCode) {
        LambdaQueryWrapper<ItemSku> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ItemSku::getSkuCode, skuCode);
        return iItemSkuService.count(wrapper) > 0;
    }

    @Override
    public PageResponse<ItemSkuSpecVo> getListBySpec(ItemSpecPageQuery query) {
        MPJLambdaWrapper<ItemSku> queryWrapper = MPJWrappers.lambdaJoin();
        queryWrapper
                .select(ItemSku::getSpecifications)
                .like(StringUtil.isNotBlank(query.getSpec()), ItemSku::getSpecifications, query.getSpec())
                .groupBy(ItemSku::getSpecifications);
        Page<ItemSku> pageReq = new Page<>(query.getPageIndex(), query.getPageSize());
        IPage<ItemSkuSpecVo> page = itemSkuMapper.selectJoinPage(pageReq, ItemSkuSpecVo.class, queryWrapper);
        List<ItemSkuSpecVo> records = page.getRecords();
        return PageResponse.of(records, (int) page.getTotal(), query.getPageSize(), query.getPageIndex());
    }

    @Override
    public Boolean beRelatedStockInOrder(String skuCode) {
        Integer count = iStockInOrderDetailService.lambdaQuery()
                .eq(StockInOrderDetail::getItemSkuCode, skuCode).select().count();
        return count > 0;
    }

    @Override
    public Boolean beRelatedStockOutOrder(String skuCode) {
        Integer count = iStockOutOrderDetailService.lambdaQuery().eq(StockOutOrderDetail::getItemSkuCode, skuCode)
                .select().count();
        return count > 0;
    }

    @Override
    public Map<String, ItemAndSkuCodeInfo> getItemAndSkuCodeInfoBatch(Collection<String> skuCodes) {
        final List<ItemAndSkuCodeInfo> itemAndSkuCodeInfoBatch = itemSkuMapper
                .getItemAndSkuCodeInfoBatch(skuCodes);
        if (CollectionUtil.isEmpty(itemAndSkuCodeInfoBatch)) {
            return Collections.emptyMap();
        }
        return itemAndSkuCodeInfoBatch.stream().collect(Collectors.toMap(ItemAndSkuCodeInfo::getSkuSupplierCode, Function
                .identity()));
    }

    /**
     * C_E_COMMERCE(0, "合作方-电商"),
     * C_DECORATION(1, "合作方-绿色家装"),
     *
     * @param skuCode
     * @return
     */
    @Override
    public Integer getSkuBusinessLine(String skuCode) {
        // 如果SKU选择多个合作方，统一算电商。
        final List<Integer> res = itemSkuMapper.getSkuBusinessLine(skuCode);
        if (CollectionUtil.isNotEmpty(res)) {
            if (res.size() > 1) {
                return 0;
            }
            return res.get(0);
        }
        return 0;
    }
}
