package com.daddylab.supplier.item.domain.dataFetch;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/4/24
 */
@Getter
@AllArgsConstructor
public enum FetchDataType implements IEnum<Integer> {
  WDT_PLATFORM_GOODS(1, "旺店通平台商品"),
  PLATFORM_ITEM(2, "系统平台商品"),
  WDT_ORDER(3, "旺店通订单"),
  WDT_REFUND_ORDER(4, "旺店通退换单"),
  WDT_OTHER_STOCK_IN_ORDER(5, "旺店通其他入库单"),
  WDT_OTHER_STOCK_OUT_ORDER(6, "旺店通其他出库单"),
  WDT_WAREHOUSE(7, "旺店通仓库"),
  WDT_PROVIDER_GOODS(8, "旺店通供应商商品"),
  WDT_GOODS_CLASS(9, "旺店通货品分类"),
  WDT_PROVIDER(10, "旺店通采购供应商"),
  WDT_GOODS(11, "旺店通货品档案"),
  ITEM(12, "系统后端商品"),
  BRAND(13, "品牌同步"),
  WDT_SUITE(14, "旺店通组合装"),
  WDT_SALE_STOCK_OUT_ORDER(15, "旺店通销售出库单"),
  WDT_REFUND_STOCK_IN_ORDER(16, "旺店通退货入库单"),
  WDT_PRE_STOCK_IN_ORDER(17, "旺店通退货预入库单"),
  WDT_GOODS_SN(18, "旺店通退货预入库单"),
  WDT_STOCK_SPEC(19, "旺店通库存管理"),
  REDBOOK_PLATFORM_ITEM(20, "小红书平台商品"),
  DOUDIAN_PLATFORM_ITEM(21, "抖店平台商品"),
  WDT_LOGISTICS_TRACE(22, "旺店通物流轨迹"),
  WDT_LOGISTICS_TRACE1(2201, "旺店通物流轨迹-待取件"),
  WDT_LOGISTICS_TRACE2(2202, "旺店通物流轨迹-已取件"),
  WDT_LOGISTICS_TRACE3(2203, "旺店通物流轨迹-在途中"),
  WDT_LOGISTICS_TRACE4(2204, "旺店通物流轨迹-待配送"),
  WDT_LOGISTICS_TRACE5(2205, "旺店通物流轨迹-已签收"),
  WDT_LOGISTICS_TRACE6(2206, "旺店通物流轨迹-拒收"),
  WDT_LOGISTICS_TRACE7(2207, "旺店通物流轨迹-已处理"),
  WDT_LOGISTICS_TRACE9(2209, "旺店通物流轨迹-退件中"),
  WDT_LOGISTICS_TRACE10(2210, "旺店通物流轨迹-已退签"),
  WDT_LOGISTICS_TRACE11(2211, "旺店通物流轨迹-问题件"),
  WDT_LOGISTICS_TRACE1_C1(220101, "旺店通物流轨迹-待取件（补偿1）"),
  WDT_LOGISTICS_TRACE2_C1(220201, "旺店通物流轨迹-已取件（补偿1）"),
  WDT_LOGISTICS_TRACE3_C1(220301, "旺店通物流轨迹-在途中（补偿1）"),
  WDT_LOGISTICS_TRACE4_C1(220401, "旺店通物流轨迹-待配送（补偿1）"),
  WDT_LOGISTICS_TRACE5_C1(220501, "旺店通物流轨迹-已签收（补偿1）"),
  WDT_LOGISTICS_TRACE6_C1(220601, "旺店通物流轨迹-拒收（补偿1）"),
  WDT_LOGISTICS_TRACE7_C1(220701, "旺店通物流轨迹-已处理（补偿1）"),
  WDT_LOGISTICS_TRACE9_C1(220901, "旺店通物流轨迹-退件中（补偿1）"),
  WDT_LOGISTICS_TRACE10_C1(221001, "旺店通物流轨迹-已退签（补偿1）"),
  WDT_LOGISTICS_TRACE11_C1(221101, "旺店通物流轨迹-问题件（补偿1）"),
  ;
  private final Integer value;
  private final String desc;
}
