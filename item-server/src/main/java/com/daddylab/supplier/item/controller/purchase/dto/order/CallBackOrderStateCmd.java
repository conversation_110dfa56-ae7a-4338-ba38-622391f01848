package com.daddylab.supplier.item.controller.purchase.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> up
 * @date 2024年04月22日 11:04 AM
 */
@Data
@ApiModel("采购订单审核状态回调，请求参数")
public class CallBackOrderStateCmd {

    /**
     * 流程ID
     */
    @ApiModelProperty("流程ID")
    @NotNull(message = "流程ID不得为空")
    private String processInstId;

    /**
     * 采购单状态
     */
    @ApiModelProperty("回调的审核状态。O:废弃/删除。1:审核拒绝。2:审核完成。3:审核撤销.4:审核同意")
    @NotNull(message = "回调的审核状态不得为空")
    private Integer callBackState;


}
