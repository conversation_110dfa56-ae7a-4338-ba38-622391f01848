package com.daddylab.supplier.item.controller.item.dto.partner;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/27 4:02 下午
 * @description
 */
@Data
@ApiModel("合作伙伴系统查询返回封装")
public class PartnerItemVo {

    @ApiModelProperty("品牌名称")
    protected String brand;

    @ApiModelProperty("合作伙伴系统中的商品id")
    protected Integer partnerItemId;

    @ApiModelProperty("商品图片")
    protected String itemImg;

    @ApiModelProperty("商品款号")
    protected String partnerItemNo;

    @ApiModelProperty("商品名称")
    protected String name;

    @ApiModelProperty("采购负责人用户Id")
    protected Integer buyerUserId;

    @ApiModelProperty("采购负责人名字")
    protected String buyerUserName;

    @ApiModelProperty("采购负责人花名")
    protected String buyerNickName;

    @ApiModelProperty("供应商地址")
    protected String addr;

    @ApiModelProperty("供应商区县")
    protected String area;

    @ApiModelProperty("供应商城市")
    protected String city;

    @ApiModelProperty("合作伙伴系统供应商id")
    protected Integer partnerProviderId;

    @ApiModelProperty("供应商联系方式")
    protected String providerMobile;

    @ApiModelProperty("合作伙伴系统供应商名称")
    protected String partnerProviderName;

    @ApiModelProperty("社会信用代码")
    protected String organizationNo;

    @ApiModelProperty("供应商省份")
    protected String province;

    @ApiModelProperty("供应商联系人")
    protected String providerName;

    @ApiModelProperty("商品类型，1:日化;2:食品;3:纺织鞋类;4:玩具;5:电器;6:轻工百货")
    protected Integer type;

    @ApiModelProperty("QC负责人")
    protected List<String> qcIds;

    /**
     * moduleType 模块类型 0供应商(供应商) 1老爸抽检 2绿色家装 3商家入驻
     */
    @ApiModelProperty("模块类型 0电商 1老爸抽检 2绿色家装 3商家入驻")
    private Integer moduleType;

    @ApiModelProperty("是否黑名单 0否 1是")
    private Integer isBlacklist;

    @ApiModelProperty("是否老爸抽检 0否 1是")
    private Integer isDadCheck;

//    @ApiModelProperty("供应商系统商品款号")
//    protected String itemNo;

//         * ItemTypeCosmetic                       = 1  // 日化
//            * ItemTypeFood                           = 2  // 食品
//            * ItemTypeTextileFootwear                = 3  // 纺织鞋类
//            * ItemTypeToys                           = 4  // 玩具
//            * ItemTypeElectricalEquipment            = 5  // 电器
//            * ItemTypeLightIndustrialDepartmentStore = 6  // 轻工百货

    /**
     * 	ItemCooperatorAll             = 0 // 电商
     * 	ItemCooperatorSupplier        = 1 // 电商
     * 	ItemCooperatorGreenHome       = 2 // 绿色家装
     * 	ItemCooperatorDadCheck        = 4 // 老爸抽检
     * 	ItemCooperatorEvaluateScience = 8 // 评测科普
     */
    @ApiModelProperty(value = "合作方", notes = "1:电商,2:绿色家装,4:老爸抽检,8:评测科普")
    private List<Integer> cooperator;

    /**
     * ItemBusinessTypeSupplierCooperate           = 1   // 电商-商品合作
     * 	ItemBusinessTypeSupplierMerchantSettlement  = 2   // 电商-商家入驻
     * 	ItemBusinessTypeSupplierDadList             = 4   // 电商-老爸清单
     * 	ItemBusinessTypeSupplierDadCheck            = 8   // 电商-老爸抽检
     * 	ItemBusinessTypeGreenHomeCooperate          = 16  // 绿色家装-商品合作
     * 	ItemBusinessTypeGreenHomeMerchantSettlement = 32  // 绿色家装-商家入驻
     * 	ItemBusinessTypeGreenHomeDadList            = 64  // 绿色家装-老爸清单
     * 	ItemBusinessTypeGreenHomeDadCheck           = 128 // 绿色家装-老爸抽检
     * 	ItemBusinessTypeCps                         = 256 // CPS合作品
     */
    @ApiModelProperty(value = "业务类型",notes = "1:电商-商品合作,2:电商-商家入驻,4:电商-老爸清单,8:电商-老爸抽检,16:绿色家装-商品合作,32:绿色加装-商家入驻,64:绿色家装-老爸清单,128:绿色家装-老爸抽检,256:CPS合作品")
    @JsonProperty(value = "business_type")
    private List<Integer> businessType;


}
