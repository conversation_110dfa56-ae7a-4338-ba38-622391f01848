package com.daddylab.supplier.item.application.afterSalesRegister;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;

/**
 * <AUTHOR>
 * @since 2023/8/21
 */
public interface AfterSalesRegisterBizService {
    PageResponse<AfterSalesRegisterPageVO> pageQuery(AfterSalesRegisterPageQuery query);

    SingleResponse<Boolean> export(AfterSalesRegisterPageQuery query);

    MultiResponse<String> shopDropdown(String name);
}
