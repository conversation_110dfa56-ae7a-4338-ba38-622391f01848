package com.daddylab.supplier.item.controller.otherPay.dto;

import com.alibaba.cola.dto.Command;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherPayDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OtherPayVoCmd.java
 * @description
 * @createTime 2022年03月24日 17:14:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("其他应付单新增实体")
public class OtherPayCmd extends Command {

    private static final long serialVersionUID = -3190752686778153038L;

    @ApiModelProperty(value = "其他应付单id")
    private Long id;

    @ApiModelProperty(value = "应付年")
    private Long payYear;

    @ApiModelProperty(value = "应付月")
    private Long payMonth;

    @ApiModelProperty(value = "采购组织id")
    private Long organizationId;

    @ApiModelProperty(value = "供应商id")
    private Long providerId;

    @ApiModelProperty(value = "采购员id")
    private Long buyerId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "应付详情")
    private List<OtherPayDetail> otherPayDetails;

    @ApiModelProperty(value = "凭证")
    private List<OtherImageVo> vouchers;

    private String voucher;

    @ApiModelProperty(value = "状态 0:已完成 1:待提交 2:待审核 3:已拒绝 4:已撤回")
    private Integer status;
}
