package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerAuditTaskState;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 商品库抽屉模块审核
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemDrawerModuleAuditTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 商品ID
     */
    private Long itemId;

    /**
     * 流程节点
     */
    private String node;

    /**
     * 当前是第几轮审核
     */
    private Integer round;

    /**
     * 审核状态 0:待审核 1:已审核 ...
     */
    private ItemDrawerAuditTaskState auditStatus;

    /**
     * 当前任务处理人，为0则为公共任务，可自行认领
     */
    private Long processorId;

    /**
     * 审批时间
     */
    private Long auditAt;

    /**
     * 审批类型 1 商品资料 2 直播话术
     */
    private ItemAuditType type;

    /**
     * 审核通过还是拒绝
     */
    private boolean pass;

    /**
     * 是否复审
     */
    private boolean reAudit;

    /**
     * 直播话术ID。
     */
    private Long liveVerbalTrickId;

}
