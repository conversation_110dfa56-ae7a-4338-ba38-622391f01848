package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockInDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OtherStockInDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOtherStockInDetailService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 其他入库单详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-30
 */
@Service
public class OtherStockInDetailServiceImpl extends DaddyServiceImpl<OtherStockInDetailMapper, OtherStockInDetail> implements IOtherStockInDetailService {

}
