package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/1/4
 */
@AllArgsConstructor
@Getter
public enum DataSyncStatus implements IEnum<Integer> {
    /**
     * 未开始
     */
    NOT_START(0, "未开始"),
    /**
     * 同步中
     */
    SYNCING(1, "同步中"),
    /**
     * 同步完成
     */
    SYNC_SUCCESS(2, "同步完成"),
    /**
     * 同步异常
     */
    SYNC_ERROR(3, "同步异常"),
    /**
     * 同步被中断
     */
    SYNC_INTERRUPT(4, "同步被中断"),
    ;
    private final Integer value;
    private final String desc;
}
