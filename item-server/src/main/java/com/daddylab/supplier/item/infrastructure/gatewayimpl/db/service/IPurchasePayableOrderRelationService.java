package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableOrderRelation;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 采购应付关联信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-03
 */
public interface IPurchasePayableOrderRelationService extends IDaddyService<PurchasePayableOrderRelation> {

}
