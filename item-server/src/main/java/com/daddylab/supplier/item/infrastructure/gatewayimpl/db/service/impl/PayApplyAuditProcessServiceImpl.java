package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PayApplyAuditProcess;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PayApplyAuditProcessMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPayApplyAuditProcessService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 申请付款单审核流程表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Service
public class PayApplyAuditProcessServiceImpl extends DaddyServiceImpl<PayApplyAuditProcessMapper, PayApplyAuditProcess> implements IPayApplyAuditProcessService {

}
