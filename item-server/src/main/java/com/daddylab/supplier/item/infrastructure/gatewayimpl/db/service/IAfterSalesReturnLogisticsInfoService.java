package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesReturnLogisticsInfo;

/**
 * <p>
 * 售后单退货物流信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-25
 */
public interface IAfterSalesReturnLogisticsInfoService extends IDaddyService<AfterSalesReturnLogisticsInfo> {

}
