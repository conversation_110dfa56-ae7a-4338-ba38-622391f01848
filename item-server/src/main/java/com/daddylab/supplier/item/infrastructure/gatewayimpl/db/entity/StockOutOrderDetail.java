package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <p>
 * 退料出库明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StockOutOrderDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    private Long deletedAt;

    @TableLogic
    private Integer isDel;

    /**
     * 关联出库单id
     */
    private Long stockOutOrderId;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 商品skuCode
     */
    private String itemSkuCode;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 规格
     */
    private String specifications;

    /**
     * 库存单位
     */
    private String stockUnit;

    /**
     * 计价单位
     */
    private String valuationUnit;

    /**
     * 计价数量
     */
    private Integer valuationQuantity;

    /**
     * 补料数量
     */
    private Integer replenishQuantity;

    /**
     * 扣款数量
     */
    private Integer deductionQuantity;

    /**
     * 出库数量
     */
    private Integer returnQuantity;

    /**
     * 实际出库数量(旺店通回填)
     */
    private Integer realReturnQuantity;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 退料原因
     */
    private String returnReason;

    /**
     * 是否是赠品,0不是。1是
     */
    private Integer isGift;

    /**
     * 备注
     */
    private String remark;

    /**
     * 含税单价(6位)
     */
    private BigDecimal taxPrice;

    /**
     * 税率(实际计算值，非百分比)
     */
    private BigDecimal taxRate;

    /**
     * 税额
     */
    private BigDecimal taxQuota;

    /**
     * 价税合计
     */
    private BigDecimal totalPriceTax;

    /**
     * 税后单价(6位)
     */
    private BigDecimal afterTaxPrice;

    /**
     * 税后金额
     */
    private BigDecimal afterTaxAmount;


    public void setTaxRate(BigDecimal taxRate) {
        if (Objects.isNull(taxRate)) {
            this.taxRate = BigDecimal.ZERO;
        } else {
            this.taxRate = taxRate.setScale(4, RoundingMode.HALF_DOWN);
        }
    }

    public void setTaxQuota(BigDecimal taxQuota) {
        this.taxQuota = taxQuota.setScale(4, RoundingMode.HALF_DOWN);
    }

    public void setTotalPriceTax(BigDecimal totalPriceTax) {
        this.totalPriceTax = totalPriceTax.setScale(4, RoundingMode.HALF_DOWN);
    }

    public void setAfterTaxAmount(BigDecimal afterTaxAmount) {
        this.afterTaxAmount = afterTaxAmount.setScale(4, RoundingMode.HALF_DOWN);
    }

    /**
     * 商品单价计算
     */
    public void calculatedAmount() {
        int num = NumberUtil.isPositive(realReturnQuantity) ? realReturnQuantity : returnQuantity;
        // 价税合计 = 单价（税前）* 采购数量
        this.totalPriceTax = taxPrice.multiply(new BigDecimal(num)).setScale(4, RoundingMode.HALF_UP);
        // 税后单价 = 单价（税前）/ (1 + 税率)
        this.afterTaxPrice = taxPrice.divide(new BigDecimal(1).add(taxRate), 6, RoundingMode.HALF_UP);
        // 税后金额 = 税后单价 * 采购数量
        this.afterTaxAmount = afterTaxPrice.multiply(new BigDecimal(num)).setScale(4, RoundingMode.HALF_UP);
        // 税额 = 价税合计 - 税后金额
        this.taxQuota = totalPriceTax.subtract(afterTaxAmount).setScale(4, RoundingMode.HALF_UP);
    }

}
