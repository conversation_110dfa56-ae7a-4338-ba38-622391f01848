package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.controller.purchase.dto.PurchaseQueryPage;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Purchase;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 采购确认 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@Service
public class PurchaseServiceImpl extends DaddyServiceImpl<PurchaseMapper, Purchase> implements IPurchaseService {

    @Autowired
    PurchaseMapper purchaseMapper;

    @Override
    public Integer countExport(PurchaseQueryPage param) {
        return purchaseMapper.countExport(param);
    }

//    @Override
//    public BigDecimal getCostPriceForWdtDetail(String skuCode, Long payTime, Integer platformType) {
//        List<PurchasePriceDO> purchasePrice = purchaseMapper.getPurchasePrice(skuCode, payTime, platformType);
//        if (CollectionUtils.isEmpty(purchasePrice)) {
//            return BigDecimal.ZERO;
//        }
//
//        Map<Integer, PurchasePriceDO> activeTypeMap = purchasePrice.stream().collect(Collectors.toMap(PurchasePriceDO::getActiveType, v -> v));
//        // 直播价格
//        PurchasePriceDO liveValue = activeTypeMap.get(PurchaseType.LIVE.value);
//        if (Objects.nonNull(liveValue)) {
//            return liveValue.getPriceCost();
//        }
//        // 大促价格
//        PurchasePriceDO saleValue = activeTypeMap.get(PurchaseType.SALE.value);
//        if (Objects.nonNull(saleValue)) {
//            return saleValue.getPriceCost();
//        }
//        // 无，常规价格
//        PurchasePriceDO noValue = activeTypeMap.get(PurchaseType.NOT_HAVE.value);
//        if (Objects.nonNull(noValue)) {
//            return noValue.getPriceCost();
//        }
//        return BigDecimal.ZERO;
//    }
}
