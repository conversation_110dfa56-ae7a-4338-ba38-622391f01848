package com.daddylab.supplier.item.infrastructure.qimen.wdt;

import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.RawRefundAPI;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RawRefundSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RawRefundSearchResponse;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RawRefundUploadOrder;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RawRefundUploadResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.daddylab.supplier.item.infrastructure.qimen.QimenConfig;
import com.daddylab.supplier.item.infrastructure.qimen.QimenWdtRawRefundAPI;
import com.qimencloud.api.QimenCloudClient;
import com.qimencloud.api.scene3ldsmu02o9.request.WdtAftersalesRefundRawrefundSearchRequest;
import com.qimencloud.api.scene3ldsmu02o9.response.WdtAftersalesRefundRawrefundSearchResponse;
import com.taobao.api.ApiException;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2022/10/26
 */
public class RawRefundAPIQimenImpl extends WdtAPIQimenImplBase implements RawRefundAPI {

    QimenWdtRawRefundAPI qimenAPI;

    public RawRefundAPIQimenImpl(QimenCloudClient qimenClient,
            QimenConfig qimenConfig,
            WdtConfig wdtConfig,
            int configIndex
    ) {
        super(qimenClient, qimenConfig, wdtConfig, configIndex);
    }

    @Override
    public RawRefundUploadResponse upload(String shopNo, List<RawRefundUploadOrder> orderList)
            throws WdtErpException {
        throw new UnsupportedOperationException("暂未实现");

    }

    @Override
    public RawRefundSearchResponse search(RawRefundSearchParams params, Pager pager)
            throws WdtErpException {
        try {
            final WdtAftersalesRefundRawrefundSearchRequest.Params qimenParams =
                    Assembler.INST.toQimenParams(params);
            final WdtAftersalesRefundRawrefundSearchResponse response = qimenAPI
                    .afterSalesRawRefundSearch(qimenParams, pager.getPageNo() + 1,
                            pager.getPageSize());
            return checkAndReturnData(response, RawRefundSearchResponse.class);
        } catch (ApiException exception) {
            throw transformException(exception);
        }
    }

    @Override
    protected void initApiInstance(QimenCloudClient qimenClient, QimenConfig qimenConfig,
            WdtConfig wdtConfig, int configIndex) {
        qimenAPI = new QimenWdtRawRefundAPI(qimenClient, qimenConfig, wdtConfig, configIndex);
    }

    @Mapper
    interface Assembler {

        Assembler INST = Mappers.getMapper(Assembler.class);

        WdtAftersalesRefundRawrefundSearchRequest.Params toQimenParams(
                RawRefundSearchParams params);
    }
}
