package com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector;

import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import java.util.Map;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/25 3:16 下午
 * @description
 */
public class DeleteByMapWithTime extends AbstractMethod {

    private static final String METHOD = "deleteByMapWithTime";

    public DeleteByMapWithTime() {
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        SqlMethod sqlMethod = SqlMethod.LOGIC_DELETE_BY_MAP;
        String sql;
        SqlSource sqlSource;
        if (tableInfo.isWithLogicDelete()) {
            sql = String.format(sqlMethod.getSql(), tableInfo.getTableName(), this.sqlLogicSet(tableInfo) + ",deleted_at = unix_timestamp()", this.sqlWhereByMap(tableInfo));
            sqlSource = this.languageDriver.createSqlSource(this.configuration, sql, Map.class);
            return this.addUpdateMappedStatement(mapperClass, Map.class, METHOD, sqlSource);
        }
        return null;
//        else {
//            sqlMethod = SqlMethod.DELETE_BY_MAP;
//            sql = String.format(sqlMethod.getSql(), tableInfo.getTableName(), this.sqlWhereByMap(tableInfo));
//            sqlSource = this.languageDriver.createSqlSource(this.configuration, sql, Map.class);
//            return this.addDeleteMappedStatement(mapperClass, this.getMethod(sqlMethod), sqlSource);
//        }
    }

}
