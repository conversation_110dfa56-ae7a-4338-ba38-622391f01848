package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.FetchSegment;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataFetch;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 数据同步记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Repository
public interface DataFetchMapper extends DaddyBaseMapper<DataFetch> {

  /**
   * 锁定记录
   *
   * @param id 记录ID
   * @param time 锁定当前记录直到指定时间
   */
  int lock(@Param("id") Long id, @Param("time") LocalDateTime time);

  /**
   * 更新记录然后解锁
   *
   * @param fetchSegment 拉取段数据包装
   */
  int updateStateAndUnLock(@Param("fetchSegment") FetchSegment fetchSegment);

  /**
   * 更新记录然后解锁
   *
   * @param record PO
   */
  int updateStateAndUnLockByRecord(@Param("record") DataFetch record);

  /**
   * 更新数据
   *
   * @param id ID
   * @param data Data
   */
  int updateData(@Param("id") Long id, @Param("data") String data);

  /**
   * 获取指定时间范围内所有的记录ID
   *
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @param includeBoundary 是否包含边界
   */
  List<Long> getRecordIdsBetween(
      @Param("dataType") FetchDataType dataType,
      @Param("startTime") LocalDateTime startTime,
      @Param("endTime") LocalDateTime endTime,
      @Param("includeBoundary") boolean includeBoundary,
      @Param("round") Integer round);

  /**
   * 根据ID集合删除指定记录
   *
   * @param recordIds ids
   */
  boolean deleteRecordsByIds(@Param("recordIds") List<Long> recordIds);

  /**
   * 获取时间最近的记录
   *
   * @param dataType 数据类型
   */
  DataFetch getLatestRecord(
      @Param("dataType") FetchDataType dataType, @Param("round") Integer round);

  /**
   * 获取最近第N条记录
   *
   * @param dataType 数据类型
   * @param round 拉取周期
   * @param nth 第N条
   * @param desc 是否倒序
   */
  DataFetch getNthRecord(
      @Param("dataType") FetchDataType dataType,
      @Param("round") Integer round,
      @Param("nth") int nth,
      @Param("desc") boolean desc);

  /**
   * 获取时间最早的记录
   *
   * @param dataType 数据类型
   */
  DataFetch getEarliestRecord(
      @Param("dataType") FetchDataType dataType, @Param("round") Integer round);

  /**
   * 获取初始化数据记录
   *
   * @param dataType 数据类型
   */
  DataFetch getFullFetchRecord(
      @Param("dataType") FetchDataType dataType, @Param("round") Integer round);

  /**
   * 更新锁定时间（主要用于初始化记录时做锁续租）
   *
   * @param id 初始化记录ID
   * @param lockTime 锁定时间
   * @return 更新记录数
   */
  int updateLockTime(@Param("id") Long id, @Param("lockTime") LocalDateTime lockTime);

  /**
   * 获取当前状态未拉取的段
   *
   * @param dataType 数据类型
   * @param orderDesc 是否正序拉取（默认倒序 - 从最近到最早）
   * @param limit 拉取多少个段
   * @param fetchPointMax 拉取时间最大值
   */
  List<FetchSegment> getWaitFetchSegments(
      @Param("dataType") FetchDataType dataType,
      @Param("orderDesc") boolean orderDesc,
      @Param("limit") int limit,
      @Param("round") Integer round,
      @Param("fetchPointMax") LocalDateTime fetchPointMax);

  /**
   * 统计指定数据类型待执行的分段数量
   *
   * @param dataType 数据类型
   * @param round 拉取周期
   */
  int countWaitingSegments(
      @Param("dataType") FetchDataType dataType, @Param("round") Integer round);

  /**
   * 从下向上遍历记录
   *
   * @param dataType 数据类型
   * @param round 拉取周期
   * @param offsetFetchPoint 偏移时间（不包括当前ID）
   * @param limit 拉取多少条
   */
  default List<DataFetch> list(
      FetchDataType dataType, Integer round, LocalDateTime offsetFetchPoint, int limit) {
    final LambdaQueryWrapper<DataFetch> query = Wrappers.lambdaQuery();
    query
        .gt(Objects.nonNull(offsetFetchPoint), DataFetch::getFetchPoint, offsetFetchPoint)
        .eq(DataFetch::getDataType, dataType.getValue())
        .eq(DataFetch::getRound, round)
        .orderByAsc(DataFetch::getFetchPoint)
        .last("limit " + limit);
    return selectList(query);
  }
}
