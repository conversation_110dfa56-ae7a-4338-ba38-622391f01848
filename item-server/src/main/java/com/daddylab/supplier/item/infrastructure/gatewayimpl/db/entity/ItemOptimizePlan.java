package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import org.javers.core.metamodel.annotation.DiffIgnore;

import java.io.Serializable;

/**
 * 商品优化计划
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemOptimizePlan implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城 */
    @ApiModelProperty("平台")
    private Integer platform;

    /** 编号 */
    @DiffIgnore private String planNo;

    /** 名称 */
    @ApiModelProperty("计划名称")
    private String planName;

    /** 商品数量 */
    @DiffIgnore
    @ApiModelProperty("商品数量")
    private Integer itemNum;

    /** 0:未提交，1已提交 */
    @DiffIgnore
    @ApiModelProperty("状态")
    private Integer status;

    /** 合作模式（业务线）：0电商 1老爸抽检 2绿色家装 3商家入驻（目前为单选） */
    @ApiModelProperty("合作模式")
    private String businessLine;

    public String getBusinessLine() {
        return DivisionLevelValueEnum.filterCorpType(businessLine);
    }

    @ApiModelProperty("提交人")
    @DiffIgnore
    private Long submitUid;

    @ApiModelProperty("提交时间")
    @DiffIgnore
    private Long submitAt;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    @DiffIgnore
    private Long createdAt;

    /** 创建人 */
    @TableField(fill = FieldFill.INSERT)
    @DiffIgnore
    private Long createdUid;

    /** 更新时间 */
    @TableField(fill = FieldFill.UPDATE)
    @DiffIgnore
    private Long updatedAt;

    /** 更新人 */
    @TableField(fill = FieldFill.UPDATE)
    @DiffIgnore
    private Long updatedUid;

    /** 是否删除：0否1是 */
    @TableLogic @DiffIgnore private Integer isDel;

    /** 删除时间 */
    @TableField(fill = FieldFill.DEFAULT)
    @DiffIgnore
    private Long deletedAt;
}
