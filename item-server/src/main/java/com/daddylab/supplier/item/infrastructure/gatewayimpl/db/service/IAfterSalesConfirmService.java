package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.application.afterSales.AfterSalesConfirmDetailInfoVO;
import com.daddylab.supplier.item.application.afterSales.AfterSalesConfirmInfoVO;
import com.daddylab.supplier.item.application.afterSales.AfterSalesConfirmInfoWithDetailsVO;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesConfirm;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 售后单销退确认单据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
public interface IAfterSalesConfirmService extends IDaddyService<AfterSalesConfirm> {

    /**
     * 查询销退确认信息视图对象
     *
     * @param refundOrderNo 退换单号
     */
    Optional<AfterSalesConfirmInfoVO> getAfterSalesConfirmInfoVO(String refundOrderNo);

    Optional<AfterSalesConfirmInfoWithDetailsVO> getAfterSalesConfirmInfoWithDetailsVO(
            String refundOrderNo);

    List<AfterSalesConfirmDetailInfoVO> getAfterSalesConfirmDetailInfoVOs(
            String refundOrderNo);
}
