package com.daddylab.supplier.item.infrastructure.gatewayimpl.db;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.domain.wechatProductDetail.gateway.WechatProductDetailGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatProductDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WechatProductDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 微信商品详情Gateway实现
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
public class WechatProductDetailGatewayImpl implements WechatProductDetailGateway {

    @Autowired
    private WechatProductDetailMapper wechatProductDetailMapper;

    @Override
    public boolean save(WechatProductDetail wechatProductDetail) {
        try {
            return wechatProductDetailMapper.insert(wechatProductDetail) > 0;
        } catch (Exception e) {
            log.error("保存微信商品详情失败", e);
            return false;
        }
    }

    @Override
    public boolean saveBatch(List<WechatProductDetail> wechatProductDetails) {
        try {
            for (WechatProductDetail wechatProductDetail : wechatProductDetails) {
                wechatProductDetailMapper.insert(wechatProductDetail);
            }
            return true;
        } catch (Exception e) {
            log.error("批量保存微信商品详情失败", e);
            return false;
        }
    }

    @Override
    public WechatProductDetail findByTypeAndProductId(String type, String productId) {
        try {
            QueryWrapper<WechatProductDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("type", type);
            queryWrapper.eq("product_id", productId);
            return wechatProductDetailMapper.selectOne(queryWrapper);
        } catch (Exception e) {
            log.error("根据类型和商品ID查询微信商品详情失败，类型: {}, 商品ID: {}", type, productId, e);
            return null;
        }
    }

    @Override
    public List<WechatProductDetail> findByTypeAndProductIds(String type, List<String> productIds) {
        try {
            QueryWrapper<WechatProductDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("type", type);
            queryWrapper.in("product_id", productIds);
            return wechatProductDetailMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("根据类型和商品ID列表查询微信商品详情失败，类型: {}, 商品ID数量: {}", type, productIds != null ? productIds.size() : 0, e);
            return null;
        }
    }

    @Override
    public boolean updateByTypeAndProductId(WechatProductDetail wechatProductDetail) {
        try {
            QueryWrapper<WechatProductDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("type", wechatProductDetail.getType());
            queryWrapper.eq("product_id", wechatProductDetail.getProductId());
            return wechatProductDetailMapper.update(wechatProductDetail, queryWrapper) > 0;
        } catch (Exception e) {
            log.error("根据类型和商品ID更新微信商品详情失败，类型: {}, 商品ID: {}", wechatProductDetail.getType(), wechatProductDetail.getProductId(), e);
            return false;
        }
    }

    @Override
    public boolean deleteByTypeAndProductId(String type, String productId) {
        try {
            QueryWrapper<WechatProductDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("type", type);
            queryWrapper.eq("product_id", productId);
            return wechatProductDetailMapper.delete(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("根据类型和商品ID删除微信商品详情失败，类型: {}, 商品ID: {}", type, productId, e);
            return false;
        }
    }

    @Override
    public WechatProductDetail findByProductId(String productId) {
        try {
            QueryWrapper<WechatProductDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("product_id", productId);
            return wechatProductDetailMapper.selectOne(queryWrapper);
        } catch (Exception e) {
            log.error("根据商品ID查询微信商品详情失败，商品ID: {}", productId, e);
            return null;
        }
    }

    @Override
    public List<WechatProductDetail> findByProductIds(List<String> productIds) {
        try {
            QueryWrapper<WechatProductDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("product_id", productIds);
            return wechatProductDetailMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("根据商品ID列表查询微信商品详情失败，商品ID数量: {}", productIds != null ? productIds.size() : 0, e);
            return null;
        }
    }

    @Override
    public boolean updateByProductId(WechatProductDetail wechatProductDetail) {
        try {
            QueryWrapper<WechatProductDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("product_id", wechatProductDetail.getProductId());
            return wechatProductDetailMapper.update(wechatProductDetail, queryWrapper) > 0;
        } catch (Exception e) {
            log.error("根据商品ID更新微信商品详情失败，商品ID: {}", wechatProductDetail.getProductId(), e);
            return false;
        }
    }

    @Override
    public boolean deleteByProductId(String productId) {
        try {
            QueryWrapper<WechatProductDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("product_id", productId);
            return wechatProductDetailMapper.delete(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("根据商品ID删除微信商品详情失败，商品ID: {}", productId, e);
            return false;
        }
    }
} 