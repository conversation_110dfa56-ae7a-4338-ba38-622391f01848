package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AllChannelBillOrderData;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 全渠道开票主订单数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
public interface IAllChannelBillOrderDataService extends IDaddyService<AllChannelBillOrderData> {

}
