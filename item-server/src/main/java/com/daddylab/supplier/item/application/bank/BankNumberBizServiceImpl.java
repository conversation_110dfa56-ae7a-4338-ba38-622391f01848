package com.daddylab.supplier.item.application.bank;

import ch.qos.logback.classic.Level;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BankNumber;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.BankNumberMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBankNumberService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.bank.BankNumberQuery;
import com.daddylab.supplier.item.types.bank.BankSimpleInfo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/20
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class BankNumberBizServiceImpl implements BankNumberBizService {
    private final IBankNumberService bankNumberService;

    @Override
    public Response sync(BankNumberSyncCmd cmd) {
        final Logger logger =
                LoggerFactory.getLogger("com.daddylab.supplier.item.infrastructure.gatewayimpl.db");
        ((ch.qos.logback.classic.Logger) logger).setLevel(Level.INFO);
        final LhhFetcher lhhFetcher = new LhhFetcher();
        final Set<Integer> savedIds =
                bankNumberService.lambdaQuery().select(BankNumber::getId).list().stream()
                        .map(BankNumber::getId)
                        .collect(Collectors.toSet());
        lhhFetcher.fetch(
                2000,
                page -> {
                    log.info(
                            "【银联号同步】第{}页 当前页数量={} 总页数={} 总数={}",
                            page.getNumber(),
                            page.getNumberOfElements(),
                            page.getTotalPages(),
                            page.getTotalElements());
                    final ArrayList<BankNumber> bankNumbersToInsert = new ArrayList<>();
                    final ArrayList<BankNumber> bankNumbersToUpdate = new ArrayList<>();
                    final int currentTime = DateUtil.currentTime().intValue();
                    page.forEach(
                            lhhDataModel -> {
                                final BankNumber bankNumber = new BankNumber();
                                bankNumber.setId(lhhDataModel.getId());
                                bankNumber.setAreaPid(lhhDataModel.getAreaPid());
                                bankNumber.setAreaId(lhhDataModel.getAreaId());
                                bankNumber.setBankName(lhhDataModel.getBankname());
                                bankNumber.setBankNo(lhhDataModel.getHanghao());
                                bankNumber.setBankId(lhhDataModel.getBankId());
                                bankNumber.setTel(lhhDataModel.getTel());
                                bankNumber.setAddress(lhhDataModel.getAddress());
                                if (!savedIds.contains(bankNumber.getId())) {
                                    bankNumber.setCreatedAt(currentTime);
                                    bankNumber.setCreatedUid(0);
                                    bankNumber.setUpdatedAt(currentTime);
                                    bankNumber.setUpdatedUid(0);
                                    bankNumbersToInsert.add(bankNumber);
                                } else if (!cmd.isSkipUpdate()) {
                                    bankNumber.setUpdatedAt(currentTime);
                                    bankNumber.setUpdatedUid(0);
                                    bankNumbersToUpdate.add(bankNumber);
                                }
                            });
                    if (!bankNumbersToInsert.isEmpty())
                        bankNumberService.saveBatch(bankNumbersToInsert);
                    if (!bankNumbersToUpdate.isEmpty())
                        bankNumberService.updateBatchById(bankNumbersToUpdate);
                });
        return Response.buildSuccess();
    }

    @Override
    public PageResponse<BankSimpleInfo> querySimpleInfo(BankNumberQuery query) {
        final BankNumberMapper mapper = bankNumberService.getDaddyBaseMapper();
        final Page<BankNumber> page =
                PageHelper.startPage(query.getPageIndex(), query.getPageSize(), false)
                        .doSelectPage(() -> mapper.bankNumberQuery(query));
        return ResponseFactory.ofPage(
                page,
                datum -> {
                    final BankSimpleInfo bankSimpleInfo = new BankSimpleInfo();
                    bankSimpleInfo.setId(datum.getId());
                    bankSimpleInfo.setBankName(datum.getBankName());
                    bankSimpleInfo.setBankNo(datum.getBankNo());

                    return bankSimpleInfo;
                });
    }

    @XxlJob("BankNumberBizService:syncJob")
    public void syncJob() {
        final String jobParam = XxlJobHelper.getJobParam();
        final BankNumberSyncCmd cmd = JsonUtil.parse(jobParam, BankNumberSyncCmd.class);
        sync(cmd);
    }
}
