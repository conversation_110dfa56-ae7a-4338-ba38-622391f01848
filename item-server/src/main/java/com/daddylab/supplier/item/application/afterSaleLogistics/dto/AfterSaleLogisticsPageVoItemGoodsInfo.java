package com.daddylab.supplier.item.application.afterSaleLogistics.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/5/23
 */
@Data
public class AfterSaleLogisticsPageVoItemGoodsInfo {
    @ApiModelProperty("商品SKU")
    private String skuCode;
    @ApiModelProperty("商品名称")
    private String itemName;
    @ApiModelProperty("商品规格")
    private String specifications;
    @ApiModelProperty("商品价格")
    private String price;
    @ApiModelProperty("商品数量")
    private Integer itemQuantity;
    @ApiModelProperty(value = "商品图片")
    private String imgUrl;
}
