package com.daddylab.supplier.item.infrastructure.third.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @class AccessTokenEnum.java
 * @description 描述类的作用
 * @date 2024-02-27 17:35
 */
@Getter
@AllArgsConstructor
public enum AccessTokenTypeEnum implements IEnum<String> {
    RED_BOOK("redbook", "小红书"),
    KUAI_SHOU("kuaishou", "快手");

    public final String value;
    public final String desc;
}
