package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.purchase.combinationPrice.PurchaseStepPricePageQuery;
import com.daddylab.supplier.item.application.purchase.combinationPrice.sheet.CombinationStepPriceExcelVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CombinationStepPriceExcel;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 组合多件供价（excel） Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
public interface CombinationStepPriceExcelMapper
    extends DaddyBaseMapper<CombinationStepPriceExcel> {

  List<CombinationStepPriceExcelVo> queryPage(@Param("pageQuery") PurchaseStepPricePageQuery query);
}
