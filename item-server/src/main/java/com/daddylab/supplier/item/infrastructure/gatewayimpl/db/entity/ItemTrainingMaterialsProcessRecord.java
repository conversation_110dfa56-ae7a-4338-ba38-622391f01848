package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.daddylab.supplier.item.types.itemTrainingMaterials.ItemTrainingMaterialsAttachment;
import com.daddylab.supplier.item.types.itemTrainingMaterials.ItemTrainingMaterialsStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 新品商品培训资料流程处理记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemTrainingMaterialsProcessRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除：0否1是
     */
    @TableLogic
    private Integer isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 商品培训材料ID
     */
    private Long itemTrainingMaterialsId;

    /**
     * 对应审批节点状态
     */
    private ItemTrainingMaterialsStatus nodeStatus;

    /**
     * 商品培训材料处理流程ID
     */
    private Long processId;

    /**
     * 分配的办理人
     */
    private Long assigneeUid;

    /**
     * 完成时间（未完成为0）
     */
    private Long completeTime;

    /**
     * 记录消息
     */
    private String msg;

    /**
     * 流程任务ID
     */
    private String taskInstId;

    /**
     * 附件
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<ItemTrainingMaterialsAttachment> attachments;


}
