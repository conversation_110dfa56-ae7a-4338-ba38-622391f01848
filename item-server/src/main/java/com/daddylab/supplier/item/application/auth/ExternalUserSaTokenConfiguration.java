package com.daddylab.supplier.item.application.auth;

import cn.dev33.satoken.config.SaTokenConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/5/29
 */
@Configuration
public class ExternalUserSaTokenConfiguration {
    @Bean(autowireCandidate = false)
    @ConfigurationProperties(prefix = "external-user-sa-token")
    @RefreshScope
    public SaTokenConfig externalUserSaTokenConfig() {
        return new SaTokenConfig();
    }

    @Bean
    public ExternalUserLoginLogic externalUserLoginLogic() {
        return new ExternalUserLoginLogic(externalUserSaTokenConfig());
    }
}
