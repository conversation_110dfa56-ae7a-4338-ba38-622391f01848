package com.daddylab.supplier.item.application.item;

import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/6/20
 */
@Data
public class OpenItem {

    @ApiModelProperty(value = "商品ID")
    Long itemId;

    @ApiModelProperty(value = "商品编码")
    String itemCode;

    @ApiModelProperty(value = "P系统款号")
    String partnerSn;

    @ApiModelProperty(value = "商品名称")
    String name;

    @ApiModelProperty(value = "商品简称")
    String shortName;

    @ApiModelProperty(value = "创建时间")
    Long createdAt;

    @ApiModelProperty(value = "商品状态", notes = "商品状态 0:待上架 1:在售 2:下架。3:废弃")
    Integer itemStatus;

    @ApiModelProperty(value = "商品预计上架时间")
    Long estimateSaleTime;
    
    @ApiModelProperty(value = "微信小程序商品链接")
    String wechatLink;

    @ApiModelProperty(value = "品牌ID")
    Long brandId;

    @ApiModelProperty(value = "品牌")
    String brand;

    @ApiModelProperty(value = "供应商")
    String provider;

    @ApiModelProperty(value = "供应商ID")
    Long providerId;

    @ApiModelProperty(value = "供应商社会信用代码")
    String unifySocialCreditCodes;
    
    @ApiModelProperty(value = "合作方/业务类型", notes = "合作方:[0:电商,2:绿色家装];业务类型:[10:商品合作,11:商家入驻,12:老爸清单,13:定制品,14:老爸抽检];")
    List<CorpBizTypeDTO> corpBizType;
    
    @ApiModelProperty(value = "商品SKU列表")
    List<OpenItemSku> skuList;
    
    @ApiModelProperty(value = "仓库编码")
    String warehouseNo;
    
    @ApiModelProperty(value = "仓库名称")
    String warehouseName;
    
    @ApiModelProperty(value = "仓库状态", notes = "1.正常，2.停用")
    Integer warehouseStatus;
}
