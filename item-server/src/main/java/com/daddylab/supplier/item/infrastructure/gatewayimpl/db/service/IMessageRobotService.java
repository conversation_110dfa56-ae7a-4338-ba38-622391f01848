package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.MessageRobot;

/**
 * <p>
 * 消息机器人维护表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
public interface IMessageRobotService extends IDaddyService<MessageRobot> {

}
