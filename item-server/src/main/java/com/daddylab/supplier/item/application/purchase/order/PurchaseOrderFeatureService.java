package com.daddylab.supplier.item.application.purchase.order;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.application.stockInOrder.StockInOrderBizService;
import com.daddylab.supplier.item.application.stockOutOrder.StockOutOrderBizService;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.controller.stockout.dto.StockOutOrderCmd;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2023年02月06日 2:19 PM
 */
@Service
@Slf4j
public class PurchaseOrderFeatureService {

    @Resource
    IPurchaseOrderDetailService iPurchaseOrderDetailService;

    @Resource
    ProviderGateway providerGateway;

    @Resource
    StockInOrderBizService stockInOrderBizService;

    @Resource
    StockOutOrderBizService stockOutOrderBizService;

    @Resource
    IStockOutOrderService iStockOutOrderService;

    @Resource
    IStockOutOrderDetailService iStockOutOrderDetailService;

    @Resource
    IStockInOrderService iStockInOrderService;

    @Resource
    IStockInOrderDetailService iStockInOrderDetailService;

    public MultiResponse<String> stockOrderGenerationByPurchaseOrder(PurchaseOrder purchaseOrder, List<PurchaseOrderDetail> list) {
        List<String> newRelateNoList = new LinkedList<>();

        // 辅助采购单生成完成，继续生成出入库单
        List<PurchaseOrderDetail> newDetailList = iPurchaseOrderDetailService.getDetailList(purchaseOrder.getId());
        List<PurchaseOrderDetail> stockInList = newDetailList.stream().filter(val -> val.getPurchaseQuantity() > 0).collect(Collectors.toList());
        List<PurchaseOrderDetail> stockOutList = newDetailList.stream().filter(val -> val.getPurchaseQuantity() < 0).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(stockInList)) {
            StockInOrderAndOrderDetail detail =
                    StockInOrderAndOrderDetail.ofPurchaseOrder(
                            purchaseOrder,
                            list,
                            true,
                            providerGateway
                                    .partnerQueryByProviderId(purchaseOrder.getProviderId())
                                    .orElse(null));
            SingleResponse<String> stockInOrderRes = stockInOrderBizService.createStockInOrder(detail, true,false);
            if (stockInOrderRes.isSuccess()) {
                log.info("修正应付单-根据修复采购单生成新的入库单成功。stockInOrderNo:{}", stockInOrderRes.getData());
                newRelateNoList.add(stockInOrderRes.getData());
            } else {
                String no = stockInOrderRes.getData();
                removeStockInInfo(no);
                return MultiResponse.buildFailure(ErrorCode.API_RESP_ERROR.getCode(),
                        "修正应付单-根据修复采购单生成新的入库单成功。assistPurchaseOrderId:" + purchaseOrder.getId()
                                + "。error:" + stockInOrderRes.getErrMessage());
            }
        }

        if (CollUtil.isNotEmpty(stockOutList)) {
            StockOutOrderCmd stockOutOrderCmd = StockOutOrderCmd.of(purchaseOrder, stockOutList);
            SingleResponse<String> stockOutOrderRes = stockOutOrderBizService.syncStockOutOrder(stockOutOrderCmd, false);
            if (stockOutOrderRes.isSuccess()) {
                log.info("修正应付单-根据修复采购单生成新的出库单成功。stockOutOrderNo:{}", stockOutOrderRes.getData());
                newRelateNoList.add(stockOutOrderRes.getData());
            } else {
                // 删除异常的出库单
                String no = stockOutOrderRes.getData();
                removeStockOutInfo(no);
                return MultiResponse.buildFailure(ErrorCode.API_RESP_ERROR.getCode(),
                        "修正应付单-根据修复采购单生成新的出库单异常。assistPurchaseOrderId:" + purchaseOrder.getId()
                                + "。error:" + stockOutOrderRes.getErrMessage());
            }
        }

        return MultiResponse.of(newRelateNoList);
    }

    private void removeStockOutInfo(String no) {
        List<StockOutOrder> list = iStockOutOrderService.lambdaQuery().eq(StockOutOrder::getNo, no).list();
        if (CollUtil.isNotEmpty(list)) {
            StockOutOrder stockOutOrder = list.get(0);
            iStockOutOrderService.removeById(stockOutOrder.getId());

            QueryWrapper<StockOutOrderDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("stock_out_order_id", stockOutOrder.getId());
            iStockOutOrderDetailService.removeWithTime(queryWrapper);
        }
    }

    private void removeStockInInfo(String no) {
        List<StockInOrder> list = iStockInOrderService.lambdaQuery().eq(StockInOrder::getNo, no).list();
        if (CollUtil.isNotEmpty(list)) {
            StockInOrder stockInOrder = list.get(0);
            iStockInOrderService.removeById(stockInOrder.getId());

            QueryWrapper<StockInOrderDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("stock_in_order_id", stockInOrder.getId());
            iStockInOrderDetailService.removeWithTime(queryWrapper);
        }
    }

}
