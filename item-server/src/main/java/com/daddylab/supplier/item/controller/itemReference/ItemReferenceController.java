package com.daddylab.supplier.item.controller.itemReference;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.itemReference.ItemReferenceBizService;
import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceQuery;
import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceSku;
import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceSpu;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.bizLevelDivision.BizLevelFilter;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BizUnionTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@Slf4j
@RestController
@RequestMapping("/itemReference")
@Api(value = "商品参照表相关API", tags = "商品参照表相关API")
public class ItemReferenceController {

    @Autowired
    private ItemReferenceBizService itemReferenceBizService;

    @ApiOperation("商品参照表查询（SPU维度）")
    @PostMapping("/spuPageQuery")
    @Auth("/itemReference/query")
    public PageResponse<ItemReferenceSpu> spuPageQuery(@Validated @RequestBody ItemReferenceQuery query) {
        return itemReferenceBizService.spuPageQuery(query);
    }

    @ApiOperation("商品参照表查询（SKU维度）")
    @PostMapping("/skuPageQuery")
    @Auth("/itemReference/query")
    public PageResponse<ItemReferenceSku> skuPageQuery(@Validated @RequestBody ItemReferenceQuery query) {
        return itemReferenceBizService.skuPageQuery(query);
    }

    @ApiOperation("商品参照表查询（SPU维度）")
    @PostMapping("/spuExport")
    @Auth("/itemReference/export")
    public SingleResponse<Long> spuExport(@Validated @RequestBody ItemReferenceQuery query) {
        return itemReferenceBizService.spuExport(query);
    }

    @ApiOperation("商品参照表导出（SKU维度）")
    @PostMapping("/skuExport")
    @Auth("/itemReference/export")
    public SingleResponse<Long> skuExport(@Validated @RequestBody ItemReferenceQuery query) {
        return itemReferenceBizService.skuExport(query);
    }

    @ApiOperation("商品参照表导入关联关系")
    @PostMapping("/importRef")
    public Response importRef(
            @ApiParam(name = "file", value = "文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(name = "sheet", value = "第几张工作表（从0开始）", required = true) @RequestParam("sheet") Integer sheet) {
        try {
            return itemReferenceBizService.importRef(file.getInputStream(), sheet);
        } catch (IOException e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM,
                    "文件上传异常，读取失败：" + e.getMessage());
        }
    }

}
