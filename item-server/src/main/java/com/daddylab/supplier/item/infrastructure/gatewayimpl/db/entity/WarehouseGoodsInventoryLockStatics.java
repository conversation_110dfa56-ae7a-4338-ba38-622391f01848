/*
package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

*/
/**
 * <p>
 * 库存锁定数量统计
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 *//*

@Data
@EqualsAndHashCode(callSuper = false)
public class WarehouseGoodsInventoryLockStatics implements Serializable {

    private static final long serialVersionUID = 1L;

    */
/**
     * id
     *//*

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    */
/**
     * 创建时间createAt
     *//*

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    */
/**
     * 创建人updateUser
     *//*

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    */
/**
     * 更新时间updateAt
     *//*

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    */
/**
     * 更新人updateUser
     *//*

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    */
/**
     * 是否已删除
     *//*

    @TableLogic(value = "0", delval = "id")
    private Long isDel;

    */
/**
     * 删除时间
     *//*

    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    */
/**
     * 仓库编码
     *//*

    private String warehouseNo;

    */
/**
     * SKU编码
     *//*

    private String skuNo;

    */
/**
     * 库存锁定数量
     *//*

    private Integer lockNum;

    private Integer lockRatio;

    */
/**
     * 数据版本号
     *//*

    private Integer version;


}
*/
