package com.daddylab.supplier.item.infrastructure.timing;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;

import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.DecimalUtil;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2021/12/12
 */
@Slf4j
public class StopWatch extends cn.hutool.core.date.StopWatch {
    Options options = new Options();

    public StopWatch() {}

    public StopWatch(String id) {
        super(id);
    }

    public StopWatch(String id, Options options) {
        super(id);
        this.options = options;
    }

    public static void watch(String id, Consumer<StopWatch> consumer) {
        watch(id, id, consumer);
    }

    public static void watch(String id, String firstTask, Consumer<StopWatch> consumer) {
        StopWatch stopWatch = new StopWatch(id);
        stopWatch.start(firstTask);
        try {
            consumer.accept(stopWatch);
        } finally {
            stopWatch.stopThenOutput();
        }
    }

    public static <T> T watch(String id, Function<StopWatch, T> callback) {
        return watch(id, id, callback);
    }

    public static <T> T watch(String id, String firstTask, Function<StopWatch, T> callback) {
        StopWatch stopWatch = new StopWatch(id);
        stopWatch.start(firstTask);
        try {
            return callback.apply(stopWatch);
        } finally {
            stopWatch.stopThenOutput();
        }
    }

    public Options getOptions() {
        return options;
    }

    public void setOptions(Options options) {
        this.options = options;
    }

    public void next(String taskName) {
        if (this.isRunning()) {
            stop();
        }
        start(taskName);
    }

    @Override
    public String shortSummary() {
        return StrUtil.format(
                "StopWatch '{}': running time = {} {}",
                this.getId(),
                this.getTotalTime(),
                DateUtil.abbrev(options.getUnit()));
    }

    @Override
    public String prettyPrint() {
        final String unitAbbrev = DateUtil.abbrev(options.getUnit());
        StringBuilder sb = new StringBuilder(shortSummary());
        if (null == this.getTaskInfo()) {
            sb.append(FileUtil.getLineSeparator());
            sb.append("No task info kept");
        } else if (this.getTaskInfo().length > 1) {
            sb.append(FileUtil.getLineSeparator());
            sb.append("---------------------------------------------")
                    .append(FileUtil.getLineSeparator());
            sb.append(unitAbbrev)
                    .append("         %     Task name")
                    .append(FileUtil.getLineSeparator());
            sb.append("---------------------------------------------")
                    .append(FileUtil.getLineSeparator());

            final NumberFormat nf = NumberFormat.getNumberInstance();
            nf.setMinimumIntegerDigits(9);
            nf.setGroupingUsed(false);

            final NumberFormat pf = NumberFormat.getPercentInstance();
            pf.setMinimumIntegerDigits(3);
            pf.setGroupingUsed(false);
            for (TaskInfo task : getTaskInfo()) {
                sb.append(nf.format(getTaskTime(task))).append("  ");
                sb.append(pf.format(DecimalUtil.divide(getTaskTime(task), getTotalTime())))
                        .append("  ");
                sb.append(task.getTaskName()).append(FileUtil.getLineSeparator());
            }
        }
        return sb.toString();
    }

    private BigDecimal getTaskTime(TaskInfo taskInfo) {
        switch (options.getUnit()) {
            case SECONDS:
                return BigDecimal.valueOf(taskInfo.getTimeSeconds());
            case MILLISECONDS:
                return BigDecimal.valueOf(taskInfo.getTimeMillis());
            case MICROSECONDS:
                return BigDecimal.valueOf(TimeUnit.NANOSECONDS.toMicros(taskInfo.getTimeNanos()));
            case NANOSECONDS:
            default:
                return BigDecimal.valueOf(taskInfo.getTimeNanos());
        }
    }

    private BigDecimal getTotalTime() {
        switch (options.getUnit()) {
            case SECONDS:
                return BigDecimal.valueOf(getTotalTimeSeconds());
            case MILLISECONDS:
                return BigDecimal.valueOf(getTotalTimeMillis());
            case MICROSECONDS:
                return BigDecimal.valueOf(getTotalTimeMicros());
            case NANOSECONDS:
            default:
                return BigDecimal.valueOf(getTotalTimeNanos());
        }
    }

    public long getTotalTimeMicros() {
        return TimeUnit.NANOSECONDS.toMicros(getTotalTimeNanos());
    }

    public void stopThenOutput() {
        stop();
        output();
    }

    public void output() {
        switch (options.getOutput()) {
            case LOG:
                outputLog();
                break;
            case STDOUT:
                stdout();
            default:
                break;
        }
    }

    private void stdout() {
        System.out.println(prettyPrint());
    }

    private void outputLog() {
        switch (options.getLevel()) {
            case INFO:
                log.info("{}", prettyPrint());
                break;
            case DEBUG:
            default:
                log.debug("{}", prettyPrint());
                break;
        }
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder(shortSummary());
        final TaskInfo[] taskInfos = this.getTaskInfo();
        if (null != taskInfos) {
            for (TaskInfo task : taskInfos) {
                final String unitAbbrev = DateUtil.abbrev(options.getUnit());
                sb.append("; [")
                        .append(task.getTaskName())
                        .append("] took ")
                        .append(getTaskTime(task).toPlainString())
                        .append(" ")
                        .append(unitAbbrev);
                long percent = Math.round(100.0 * task.getTimeNanos() / getTotalTimeNanos());
                sb.append(" = ").append(percent).append("%");
            }
        } else {
            sb.append("; no task info kept");
        }
        return sb.toString();
    }
}
