package com.daddylab.supplier.item.infrastructure.gatewayimpl.department;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.PageResponse;
import com.daddylab.supplier.item.domain.departments.DepartmentGateway;
import com.daddylab.supplier.item.infrastructure.accessControl.AccessControlAPI;
import com.daddylab.supplier.item.infrastructure.accessControl.Dept;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.DropdownItem;
import com.daddylab.supplier.item.types.departments.DepartmentDropdownQuery;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2022/4/1
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DepartmentGatewayImpl implements DepartmentGateway {

    final private AccessControlAPI accessControlAPI;
    private List<Dept> departments;
    private LocalDateTime lastDeptQueryTime;

    private List<Dept> getDepartments() throws RuntimeException {
        refreshDepartmentsIfNecessary();
        return departments;
    }

    @Override
    public PageResponse<DropdownItem<Long>> departmentDropdownList(DepartmentDropdownQuery query)
            throws RuntimeException {
        final long count = attachDepartmentQueryFilter(getDepartments().stream(), query).count();
        final List<DropdownItem<Long>> data = attachDepartmentQueryFilter(getDepartments().stream(),
                query)
                .map(it -> new DropdownItem<Long>(it.getId(), it.getName()))
                .skip(query.getOffset())
                .limit(query.getPageSize())
                .collect(Collectors.toList());
        return PageResponse.of(data, (int) count, query.getPageSize(), query.getPageIndex());
    }

    private Stream<Dept> attachDepartmentQueryFilter(
        Stream<Dept> stream, DepartmentDropdownQuery query) {
      return stream
          .filter(dept -> dept.getIsDel() == 0)
          .filter(
              dept -> Objects.isNull(query.getId()) || Objects.equals(query.getId(), dept.getId()))
          .filter(
              dept -> Objects.isNull(query.getPid()) || Objects.equals(query.getPid(), dept.getPid()))
          .filter(
              dept ->
                  StrUtil.isBlank(query.getName())
                      || StrUtil.isNotBlank(dept.getName())
                          && dept.getName().contains(query.getName()));
    }

    private void refreshDepartmentsIfNecessary() throws RuntimeException {
        if (departments == null || lastDeptQueryTime.plusMinutes(10)
                .isBefore(LocalDateTime.now())) {
            final Rsp<List<Dept>> rsp = accessControlAPI.dept();
            if (!rsp.isSuccess()) {
                throw new RuntimeException("从权限系统查询权限策略失败");
            }
            departments = rsp.getData();
            lastDeptQueryTime = LocalDateTime.now();
        }
    }

    @Override
    public Long id(String name) throws RuntimeException {
        if (StringUtil.contains(name, "/")) {
            final List<String> hierarchies = StringUtil.splitTrim(name, "/");
            final List<String> ancestors = hierarchies.subList(0, hierarchies.size() - 1);
            Long parentId = null;
            for (String ancestor : ancestors) {
                if (parentId == null) {
                    parentId = id(ancestor, 0L);
                } else {
                    parentId = id(ancestor, parentId);
                }
            }
            final String selfName = hierarchies.get(hierarchies.size() - 1);
            return id(selfName, parentId);
        } else {
            return id(name, null);
        }
    }

    /**
     * 根据名称获取部门ID（如果有相同名称的部门获取到匹配到的第一个）
     *
     * @param name 部门名称
     * @param parentId 上级部门ID（未指定的情况只根据名称匹配，指定的情况下只查询直接子部门）
     * @return 部门ID
     */
    @NonNull
    public Long id(String name, Long parentId) {
        Stream<Dept> stream = getDepartments().stream()
                .filter(dept -> Objects.equals(name, dept.getName()));
        if (parentId != null) {
            stream = stream.filter(dept -> dept.getPid().equals(parentId));
        }
        return stream.map(Dept::getId).findFirst().orElse(0L);
    }

    @Override
    public String name(Long id) throws RuntimeException {
        return getDepartment(id).map(Dept::getName).orElse("");
    }

    @Override
    public Optional<Dept> getDepartment(Long id) {
        if (!NumberUtil.isPositive(id)) {
            return Optional.empty();
        }
        return getDepartments().stream().filter(dept -> Objects.equals(id, dept.getId()))
                               .findAny();
    }

    @Override
    public Optional<Dept> getTopDepartment(Long id) {
        if (!NumberUtil.isPositive(id)) {
            return Optional.empty();
        }
        final Optional<Dept> departmentOptional = getDepartment(id);
        if (!departmentOptional.isPresent()) {
            return departmentOptional;
        }
        Dept dept = departmentOptional.get();
        while (NumberUtil.isPositive(dept.getPid())) {
            final Optional<Dept> parentDept = getDepartment(dept.getPid());
            if (!parentDept.isPresent()) {
                break;
            }
            dept = parentDept.get();
        }
        return Optional.of(dept);
    }

    @Override
    public Collection<Long> subIds(Long id, boolean recursive) throws RuntimeException {
        return getSubDepartments(id, recursive).stream().map(Dept::getId).collect(Collectors.toSet());
    }

    @Override
    public List<Dept> getSubDepartments(Long id, boolean recursive) {
        final List<Dept> subDepartments = getDepartments().stream()
                .filter(dept -> Objects.equals(id, dept.getPid()))
                .collect(Collectors.toList());
        if (recursive) {
            List<Dept> recursiveSubDepartments = new ArrayList<>();
            for (Dept subDept : subDepartments) {
                recursiveSubDepartments.addAll(getSubDepartments(subDept.getId(), true));
            }
            subDepartments.addAll(recursiveSubDepartments);
        }
        return subDepartments;
    }

    @Override
    public Collection<Long> idAndDescendantIds(String name) throws RuntimeException {
        final Long id = id(name);
        if (id == null) {
            return Collections.emptySet();
        }
        Set<Long> allIds = new HashSet<>();
        allIds.add(id);
        allIds.addAll(subIds(id, true));
        return allIds;
    }
}
