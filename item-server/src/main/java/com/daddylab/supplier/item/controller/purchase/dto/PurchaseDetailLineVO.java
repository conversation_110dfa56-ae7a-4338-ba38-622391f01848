package com.daddylab.supplier.item.controller.purchase.dto;

import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 采购订单列表 明细模式打开，采购明细展示
 *
 * <AUTHOR> up
 * @date 2022/3/25 11:31 上午
 */
@Data
@ApiModel("采购订单列表明细模式展示行")
public class PurchaseDetailLineVO {

    @ApiModelProperty(value = "商品sku")
    private String skuCode;
    @ApiModelProperty(value = "商品名称")
    private String itemName;
    @ApiModelProperty(value = "采购数量")
    private Integer purchaseQuantity;
    @ApiModelProperty(value = "申请入库数量")
    private Integer applyStockQuantity;
    @ApiModelProperty(value = "剩余入库数量")
    private Integer remainStockQuantity;

    /**
     * 超量入库情况可能出现负数，这种情况转为0
     */
    public void setRemainStockQuantity(Integer remainStockQuantity) {
        this.remainStockQuantity = NumberUtil.isNegativeOrNull(remainStockQuantity) ? 0 : remainStockQuantity;
    }

    @ApiModelProperty(value = "累计入库数量")
    private Integer addUpStockQuantity;
    @ApiModelProperty(value = "是否是赠品")
    private Boolean isGift;

    @ApiModelProperty(value = "仓库编号")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;
}
