package com.daddylab.supplier.item.application.payment.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayPurposeEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年12月15日 3:09 PM
 */
@Data
public class PaymentDetailAmountVO {

    /**
     * 已付金额
     */
    private BigDecimal paidAmount;

    /**
     * 剩余应付金额（减去已付）
     */
    private BigDecimal rightAmount;

    /**
     *  单据目前的应付金额
     */
    private BigDecimal orderAmount;

    /**
     * 支持的支付用途枚举
     */
    private PayPurposeEnum supportPayPurpose;


    private Integer maxAdvanceRatio;
}
