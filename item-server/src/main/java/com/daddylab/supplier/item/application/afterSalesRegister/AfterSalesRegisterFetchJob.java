package com.daddylab.supplier.item.application.afterSalesRegister;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.YearMonth;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2023/8/25
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class AfterSalesRegisterFetchJob {
    private final AfterSalesRegisterFetchService afterSalesRegisterFetchService;
    private final AfterSalesRegisterFilterService afterSalesRegisterFilterService;

    @XxlJob("AfterSalesRegisterFetchJob:fetch")
    public void fetch() {
        fetch(getConfig());
    }

    @NonNull
    private static Config getConfig() {
        return Optional.ofNullable(XxlJobHelper.getJobParam())
                       .filter(StrUtil::isNotBlank)
                       .map(v -> JsonUtil.parse(v, Config.class))
                       .orElseGet(() -> new Config(YearMonth.now().minusMonths(1)));
    }


    @Data
    @NoArgsConstructor
    public static class Config {
        private YearMonth month;
        private boolean skipFetchBanniu;
        private boolean skipFetchMall;
        private boolean skipFilter;

        public Config(YearMonth month) {
            this.month = month;
        }
    }

    public void fetch(Config config) {
        final YearMonth month = config.getMonth();
        log.info("【客服售后登记表】数据拉取开始 {}", month);
        final StopWatch stopWatch = new StopWatch();
        final CompletableFuture<Void> future =
                CompletableFuture.completedFuture(month)
                                 .thenCompose(
                                         none -> fetchBanniu(config, stopWatch))
                                 .thenCompose(
                                         none -> fetchMall(config, stopWatch))
                                 .thenCompose(
                                         none -> filter(config, stopWatch));
        future.join();
        XxlJobHelper.handleResult(200, "【客服售后登记表】数据同步完成");
    }

    private CompletableFuture<Void> fetchBanniu(Config config, StopWatch stopWatch) {
        final YearMonth month = config.getMonth();
        if (config.isSkipFetchBanniu()) {
            return CompletableFuture.completedFuture(null);
        }
        stopWatch.start("fetch");
        return afterSalesRegisterFetchService
                .fetchBanniu(month)
                .whenComplete(
                        (r, e) -> {
                            stopWatch.stop();
                            if (e != null) {
                                log.error(
                                        "【客服售后登记表】数据拉取异常 {} err:{} time:{}",
                                        month,
                                        e.getMessage(),
                                        stopWatch,
                                        e);
                            } else {
                                log.info(
                                        "【客服售后登记表】数据拉取完成 {} time:{}",
                                        month,
                                        stopWatch);
                            }
                        });
    }

    private CompletableFuture<Void> fetchMall(Config config, StopWatch stopWatch) {
        final YearMonth month = config.getMonth();
        if (config.isSkipFetchMall()) {
            return CompletableFuture.completedFuture(null);
        }
        stopWatch.start("fetchMallRefundOrder");
        return afterSalesRegisterFetchService
                .fetchMall(month)
                .whenComplete(
                        (r, e) -> {
                            stopWatch.stop();
                            if (e != null) {
                                log.error(
                                        "【客服售后登记表】老爸商城维权管理数据拉取异常 {} err:{} time:{}",
                                        month,
                                        e.getMessage(),
                                        stopWatch,
                                        e);
                            } else {
                                log.info(
                                        "【客服售后登记表】老爸商城维权管理数据拉取完成 {} time:{}",
                                        month,
                                        stopWatch);
                            }
                        });
    }


    private CompletableFuture<Void> filter(Config config, StopWatch stopWatch) {
        if (config.isSkipFilter()) {
            return CompletableFuture.completedFuture(null);
        }
        final YearMonth month = config.getMonth();
        stopWatch.start("filter");
        return afterSalesRegisterFilterService
                .filter(month)
                .whenComplete(
                        (r, e) -> {
                            stopWatch.stop();
                            if (e != null) {
                                log.error(
                                        "【客服售后登记表】数据过滤异常 {} err:{} time:{}",
                                        month,
                                        e.getMessage(),
                                        stopWatch,
                                        e);
                            } else {
                                log.info(
                                        "【客服售后登记表】数据过滤完成 {} time:{}",
                                        month,
                                        stopWatch);
                            }
                        });
    }
}
