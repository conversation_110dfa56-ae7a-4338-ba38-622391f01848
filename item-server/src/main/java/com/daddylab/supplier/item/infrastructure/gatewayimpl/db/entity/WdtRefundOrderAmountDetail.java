package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 旺店通退换金额明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtRefundOrderAmountDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 金额明细记录id
     */
    private Integer recId;

    /**
     * 退换单id
     */
    private Integer refundId;

    /**
     * 退换单号
     */
    private String refundNo;

    /**
     * 1: 货款 2:邮费 99:返现
     */
    private Integer refundType;

    /**
     * 0:商家->买家 1:买家->商家
     */
    private Integer isReturn;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 收款金额
     */
    private BigDecimal receiveAmount;

    /**
     * 1:担保支付 0:非担保支付
     */
    private Boolean isGuarantee;

    /**
     * 支付账号
     */
    private Integer accountId;

    /**
     * 买家账号
     */
    private String payAccount;

    /**
     * 买家开户人姓名
     */
    private String accountName;

    /**
     * 开户银行
     */
    private String accountBank;

    /**
     * 是否系统自动生成
     */
    private Boolean isAuto;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除时间
     */
    private Long deletedAt;

}
