package com.daddylab.supplier.item.application.purchasePayable.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年03月01日 2:17 PM
 */
@Data
@ApiModel("付款申请单明细列表编辑请求参数")
public class EditApplyPayOrderDetailCmd extends Command {

    @ApiModelProperty("detail Id")
    private Long detailId;

    @ApiModelProperty("修正数量")
    private Integer fixedQuantity;

    @ApiModelProperty("")
    private BigDecimal fixedTotalAmount;
}
