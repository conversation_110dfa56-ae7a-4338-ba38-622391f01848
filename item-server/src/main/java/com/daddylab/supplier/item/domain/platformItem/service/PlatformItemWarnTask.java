package com.daddylab.supplier.item.domain.platformItem.service;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemWarn;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemWarnMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemSkuService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemWarnService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/4/3
 */
@Service
@Slf4j
public class PlatformItemWarnTask {

  @Autowired private IPlatformItemSkuService platformItemSkuService;

  @Autowired private IPlatformItemWarnService platformItemWarnService;
  @Autowired
  private MsgSender msgSender;
  @Autowired
  private RefreshConfig refreshConfig;

  @XxlJob("PlatformItemWarnTask:doTask")
  public void doTask() {
    final Long currentTime = DateUtil.currentTime();

    // 商品编码未匹配
    skuNoMatch();

    // 库存警告
    stockAlert();

    // 删除过期警告
    autoHandleExpired(currentTime);
  }


  private void autoHandleExpired(long expireTime) {
    final PlatformItemWarnMapper platformItemWarnMapper =
        platformItemWarnService.getDaddyBaseMapper();
    final int num = platformItemWarnMapper.autoHandleExpired(expireTime);
    log.info("[平台商品警告][自动处理]已处理{}条记录", num);
  }

  private void skuNoMatch() {
    final PlatformItemWarnMapper platformItemWarnMapper =
        platformItemWarnService.getDaddyBaseMapper();
    long cursorId = 0;
    int limit = 1000;
    for (; ; ) {
      List<PlatformItemSku> unmatchToWarnList =
          platformItemWarnMapper.selectToWarnUnmatchSkuList(limit, cursorId, true);
      if (!unmatchToWarnList.isEmpty()) {
        createOrUpdateWarnBatch(unmatchToWarnList, PlatformItemWarnType.SKU_NOT_MATCH);
        cursorId = unmatchToWarnList.get(unmatchToWarnList.size() - 1).getId();
      } else {
        break;
      }
    }
  }

  private void stockAlert() {
    final PlatformItemWarnMapper platformItemWarnMapper =
        platformItemWarnService.getDaddyBaseMapper();
    long cursorId = 0;
    int limit = 1000;
    for (; ; ) {
      List<PlatformItemSku> toWarnStockSkuList =
          platformItemWarnMapper.selectToWarnStockSkuList(limit, cursorId, true);
      if (!toWarnStockSkuList.isEmpty()) {
        createOrUpdateWarnBatch(toWarnStockSkuList, PlatformItemWarnType.STOCK_ALERT);
        cursorId = toWarnStockSkuList.get(toWarnStockSkuList.size() - 1).getId();
      } else {
        break;
      }
    }
  }

  private void createOrUpdateWarnBatch(
      List<PlatformItemSku> unmatchToWarnList, PlatformItemWarnType platformItemWarnType) {
    final Long currentTime = DateUtil.currentTime();
    final ArrayList<PlatformItemWarn> warns = new ArrayList<>();
    for (PlatformItemSku platformItemSku : unmatchToWarnList) {
      final PlatformItemWarn platformItemWarn = new PlatformItemWarn();
      platformItemWarn.setPlatformItemId(platformItemSku.getPlatformItemId());
      platformItemWarn.setPlatformItemSkuId(platformItemSku.getId());
      platformItemWarn.setWarnType(platformItemWarnType);
      platformItemWarn.setStatus(PlatformItemWarnStatus.UNHANDLED);
      platformItemWarn.setCreatedAt(currentTime);
      platformItemWarn.setUpdatedAt(currentTime);
      warns.add(platformItemWarn);
    }
    platformItemWarnService.getDaddyBaseMapper().saveOrUpdateBatch(warns);
  }
}
