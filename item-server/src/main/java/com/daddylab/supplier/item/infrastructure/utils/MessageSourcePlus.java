package com.daddylab.supplier.item.infrastructure.utils;

import cn.hutool.core.collection.CollUtil;
import com.google.common.base.Joiner;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/6/3
 */
@Component
public class MessageSourcePlus {

    private final MessageSource messageSource;

    public MessageSourcePlus(MessageSource messageSource) {
        this.messageSource = messageSource;
    }

    public Map<String, String> getMessages(List<String> source, String... contexts) {
        return getMessages(Locale.CHINESE, source, contexts);
    }

    public Map<String, String> getMessages(Locale locale, List<String> source, String... contexts) {
        if (CollUtil.isEmpty(source)) {
            return Collections.emptyMap();
        }
        final HashMap<String, String> messages = new HashMap<>();
        for (String s : source) {
            messages.put(s, getMessage(locale, s, contexts));
        }
        return messages;
    }

    public String getMessage(String source, String... contexts) {
        return getMessage(Locale.CHINESE, source, contexts);
    }

    public String getMessage(Locale locale, String source, String... contexts) {
        for (int i = 0; i < contexts.length; i++) {
            final String contextId = Joiner.on('.')
                    .join(Arrays.copyOfRange(contexts, 0, contexts.length - i));
            final String message = messageSource
                    .getMessage(contextId + "." + source, null, null, Locale.getDefault());
            if (message != null) {
                return message;
            }
        }
        return messageSource.getMessage(source, null, source, Locale.getDefault());
    }
}
