package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.AbnormalityLogType;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单物流异常记录
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrderLogisticsAbnormalityLog implements Serializable {

  private static final long serialVersionUID = 1L;

  /** id */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /** 创建时间createAt */
  @TableField(fill = FieldFill.INSERT)
  private Long createdAt;

  /** 创建人updateUser */
  @TableField(fill = FieldFill.INSERT)
  private Long createdUid;

  /** 更新时间updateAt */
  @TableField(fill = FieldFill.UPDATE)
  private Long updatedAt;

  /** 更新人updateUser */
  @TableField(fill = FieldFill.UPDATE)
  private Long updatedUid;

  /** 是否已删除 */
  @TableLogic private Long isDel;

  /** 删除时间 */
  @TableField(fill = FieldFill.DEFAULT)
  private Long deletedAt;

  /** 订单物流异常记录ID */
  private Long abnormalityId;

  /**
   * 日志类型 1:异常 2:备注 3:开启预警 4:关闭预警 5:记录次数记录 6:系统日志...
   *
   * @see AbnormalityLogType
   */
  private AbnormalityLogType logType;

  public void setLogType(AbnormalityLogType logType) {
    this.logType = logType;
  }

  public void setLogType(Integer logType) {
    this.logType = IEnum.getEnumByValue(AbnormalityLogType.class, logType);
  }

  /**
   * 异常类型 1 发货即将超时16h，2 发货超时24h，3 发货超时48h...
   *
   * @see com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsRootException
   */
  private Integer abnormalType;

  /**
   * 异常类型2
   *
   * @see com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsException
   */
  private Integer abnormalType2;

  /** 消息 */
  private String msg;

  /**
   * 是否为最新的异常日志
   */
  private Integer last;
}
