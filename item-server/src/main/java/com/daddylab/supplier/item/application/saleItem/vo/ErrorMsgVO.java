package com.daddylab.supplier.item.application.saleItem.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@Data
public class ErrorMsgVO implements Serializable {

    private static final long serialVersionUID = -7249646301406047739L;

    /**
     * 重复的sku_code
     */
    private Set<String> repeatSkuCode = new HashSet<>();

    /**
     * 错误的sku_code
     */
    private Set<String> errorSkuCode = new HashSet<>();

    /**
     * 存在的sku_code
     */
    private Set<String> existSkuCode = new HashSet<>();
}
