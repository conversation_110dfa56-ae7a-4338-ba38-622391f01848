package com.daddylab.supplier.item.application.afterSalesForwarding.types;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/5/24
 */
@Data
public class AfterSalesForwardingRegisterPageVO {

    /**
     * 登记记录ID
     */
    @ApiModelProperty(value = "登记记录ID", notes = "新增时为空")
    private Long id;

    /**
     * 快递单号
     */
    @ApiModelProperty(value = "快递单号")
    private String deliveryNo;

    /**
     * 登记时间
     */
    @ApiModelProperty(value = "登记时间")
    private Long registerTime;

    /**
     * 处理状态 0:待处理 1:待转寄 2:已转寄 3:无需转寄
     */
    @ApiModelProperty(value = "处理状态", notes = "0:待处理 1:待转寄 2:已转寄 3:无需转寄")
    private Integer status;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    private Integer itemNum;

    /**
     * 商品SKU
     */
    @ApiModelProperty(value = "商品SKU")
    private String skuCode;

    /**
     * 规格名称
     */
    @ApiModelProperty(value = "规格名称")
    private String skuName;

    /**
     * 是否完好
     */
    @ApiModelProperty(value = "是否完好")
    private Boolean intact;

    /**
     * 异常说明
     */
    @ApiModelProperty(value = "异常说明")
    private String abnormalDescription;

    /**
     * 影响销售凭证
     */
    @ApiModelProperty(value = "影响销售凭证")
    private AffectsSalesVouchers affectsSalesVouchers;

    /**
     * 客服备注
     */
    @ApiModelProperty(value = "客服备注")
    private String csRemark;

    /**
     * 原始单号
     */
    @ApiModelProperty(value = "原始单号")
    private String orderNo;

    /**
     * 出库单号
     */
    @ApiModelProperty(value = "出库单号")
    private String stockoutNo;

    /**
     * 出库仓库
     */
    @ApiModelProperty(value = "出库仓库")
    private String stockoutWarehouse;

    /**
     * 出库仓库编号
     */
    @ApiModelProperty(value = "出库仓库编号")
    private String stockoutWarehouseNo;

    /**
     * 出库仓库ID
     */
    @ApiModelProperty(value = "出库仓库ID")
    private Long stockoutWarehouseId;

    /**
     * 转寄地址
     */
    @ApiModelProperty(value = "转寄地址")
    private String forwardingAddress;
    
    @ApiModelProperty(value = "收件人")
    private String forwardingReceiver;
    
    @ApiModelProperty(value = "联系电话")
    private String forwardingMobile;
    
    @ApiModelProperty(value = "地址")
    private String forwardingAddressDetail;

    /**
     * 转寄单号
     */
    @ApiModelProperty(value = "转寄单号")
    private String forwardingDeliveryNo;

    /**
     * 重量
     */
    @ApiModelProperty(value = "重量")
    private String weight;

    /**
     * 纸箱型号
     */
    @ApiModelProperty(value = "纸箱型号")
    private String cartonModel;

    @ApiModelProperty(value = "操作人ID", notes = "操作人ID")
    private Long operatorId;

    @ApiModelProperty(value = "操作人昵称", notes = "操作人昵称")
    private String operatorNick;

    @ApiModelProperty(value = "操作人类型", notes = "操作人类型 0:内部员工 1:外部用户")
    private Integer operatorType;

    @ApiModelProperty(value = "是否为系统自动处理", notes = "是否为系统自动处理")
    private Boolean autoHandle;

    @ApiModelProperty(value = "店铺编号", notes = "店铺编号")
    private String shopNo;

    @ApiModelProperty(value = "店铺名称", notes = "店铺名称")
    private String shopName;

    @ApiModelProperty(value = "无需转寄类型", notes = "无需转寄类型 0:全部 1:自有仓库 2:影响销售 3:其他")
    private Integer needNotForwardType;
    
    @ApiModelProperty(value = "退换单号")
    private String refundOrderNo;
    
    @ApiModelProperty(value = "退换金额")
    private BigDecimal refundOrderAmount;

}
