package com.daddylab.supplier.item.application.order.settlement.dto;

import com.alibaba.cola.dto.Command;
import com.daddylab.supplier.item.application.order.settlement.sys.DetailStaticDo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年08月11日 10:08 AM
 */
@ApiModel("结算单编辑页面保存参数")
@Data
public class SettlementSaveCmd extends Command {
    private static final long serialVersionUID = 4220720131415078230L;

    @ApiModelProperty("ids")
    @NotEmpty(message = "结算单编辑页面保存参数,ids不得为空")
    private List<Long> ids;

    @ApiModelProperty("明细列表")
    @Valid
    private List<DetailExcelEditCmd> detailList;

    @ApiModelProperty("需要删除的明细id集合")
    private List<Long> removeIds;

    @ApiModelProperty("汇总信息")
    @NotNull(message = "汇总信息不得为空")
    private DetailStaticDo detailStaticDo;

    @ApiModelProperty("采购供应商Id")
    @NotNull(message = "采购供应商Id不得为空")
    private Long purProviderId;

    @NotNull(message = "结算供应商Id不得为空")
    @ApiModelProperty("结算供应商Id")
    private Long settProviderId;

    /**
     * 来自于新增的单据进行选择，新建结算单据。
     */
    @ApiModelProperty("是否新增结算")
    @NotNull(message = "是否新增结算判断不得为空")
    private Boolean isAdd;

    /**
     * isAdd = false 的情况下，updatedTime必须是存在的。
     */
    @ApiModelProperty("结算单据的更新时间")
    private Long updatedTime;

    @ApiModelProperty("是否结算修改")
    private Boolean settlementChange;

    @ApiModelProperty("新结算的账单Ids")
    private List<Long> newBillIds;


}
