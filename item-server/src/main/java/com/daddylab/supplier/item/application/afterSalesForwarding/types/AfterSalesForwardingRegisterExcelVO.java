package com.daddylab.supplier.item.application.afterSalesForwarding.types;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/5/24
 */
@Data
public class AfterSalesForwardingRegisterExcelVO {
    @ApiModelProperty(value = "ID")
    @ExcelProperty("ID")
    private Long id;

    /**
     * 快递单号
     */
    @ApiModelProperty(value = "快递单号")
    @ExcelProperty("快递单号")
    private String deliveryNo;

    /**
     * 登记时间
     */
    @ApiModelProperty(value = "登记时间")
    @ExcelProperty("登记时间")
    private String registerTime;

    /**
     * 处理状态 0:待处理 1:待转寄 2:已转寄 3:无需转寄
     */
    @ApiModelProperty(value = "处理状态", notes = "0:待处理 1:待转寄 2:已转寄 3:无需转寄")
    @ExcelProperty("处理状态")
    private String status;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    @ExcelProperty("商品名称")
    private String itemName;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "商品数量")
    @ExcelProperty("退回数量")
    private Integer itemNum;

    /**
     * 商品SKU
     */
    @ApiModelProperty(value = "商品SKU")
    @ExcelProperty("商品SKU")
    private String skuCode;

    /**
     * 规格名称
     */
    @ApiModelProperty(value = "规格名称")
    @ExcelProperty("规格名称")
    private String skuName;

    /**
     * 是否完好
     */
    @ApiModelProperty(value = "是否完好")
    @ExcelProperty("是否完好")
    private String intact;

    /**
     * 异常说明
     */
    @ApiModelProperty(value = "异常说明")
    @ExcelProperty("异常说明")
    private String abnormalDescription;

    /**
     * 影响销售凭证
     */
    @ApiModelProperty(value = "影响销售凭证")
    @ExcelProperty("影响销售凭证")
    private String affectsSalesVouchers;

    /**
     * 客服备注
     */
    @ApiModelProperty(value = "客服备注")
    private String csRemark;

    /**
     * 原始单号
     */
    @ApiModelProperty(value = "原始单号")
    @ExcelProperty("原始单号")
    private String orderNo;

    @ApiModelProperty(value = "店铺编号")
    @ExcelProperty("店铺编号")
    private String shopNo;

    @ApiModelProperty(value = "店铺名称")
    @ExcelProperty("店铺名称")
    private String shopName;

    /**
     * 出库单号
     */
    @ApiModelProperty(value = "出库单号")
    @ExcelProperty("出库单号")
    private String stockoutNo;

    /**
     * 出库仓库
     */
    @ApiModelProperty(value = "出库仓库")
    @ExcelProperty("出库仓库")
    private String stockoutWarehouse;

    /**
     * 出库仓库编号
     */
    @ApiModelProperty(value = "出库仓库编号")
    @ExcelProperty("出库仓库编号")
    private String stockoutWarehouseNo;

    /**
     * 转寄地址
     */
    @ApiModelProperty(value = "转寄地址")
    @ExcelProperty("转寄地址")
    private String forwardingAddress;
    
    @ExcelProperty(value = "收件人")
    private String forwardingReceiver;
    
    @ExcelProperty(value = "联系电话")
    private String forwardingMobile;
    
    @ExcelProperty(value = "地址")
    private String forwardingAddressDetail;

    /**
     * 转寄单号
     */
    @ApiModelProperty(value = "转寄单号")
    @ExcelProperty("转寄单号")
    private String forwardingDeliveryNo;

    /**
     * 重量
     */
    @ApiModelProperty(value = "重量")
    @ExcelProperty("重量")
    private String weight;

    /**
     * 纸箱型号
     */
    @ApiModelProperty(value = "纸箱型号")
    @ExcelProperty("纸箱型号")
    private String cartonModel;

}
