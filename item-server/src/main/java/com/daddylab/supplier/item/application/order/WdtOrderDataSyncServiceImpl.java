package com.daddylab.supplier.item.application.order;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ConcurrentHashSet;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.apiv2.sales.TradeQueryAPI;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailResponse.Order;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailResponse.Order.Detail;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderMapper;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.types.order.OrderDetailPrimaryIdObj;
import com.daddylab.supplier.item.types.order.WdtOrderBatchSaveEvent;
import com.google.common.base.Joiner;
import lombok.NonNull;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/5/27
 */
@Service
public class WdtOrderDataSyncServiceImpl implements WdtOrderDataSyncService {

    private final WdtOrderMapper wdtOrderMapper;
    private final WdtOrderDetailMapper wdtOrderDetailMapper;
    private final RefreshConfig refreshConfig;
    private final WdtGateway wdtGateway;
    private final Set<String> modifiedSrcTids = new ConcurrentHashSet<>();

    public WdtOrderDataSyncServiceImpl(
            WdtOrderMapper wdtOrderMapper,
            WdtOrderDetailMapper wdtOrderDetailMapper,
            RefreshConfig refreshConfig,
            WdtGateway wdtGateway, TaskScheduler taskScheduler) {
        this.wdtOrderMapper = wdtOrderMapper;
        this.wdtOrderDetailMapper = wdtOrderDetailMapper;
        this.refreshConfig = refreshConfig;
        this.wdtGateway = wdtGateway;
        taskScheduler.scheduleAtFixedRate(this::executeInvalidCheck, Duration.ofSeconds(60));
    }

    private void executeInvalidCheck() {
        final ArrayList<String> srcTids = new ArrayList<>();
        if (!modifiedSrcTids.isEmpty()) {
            modifiedSrcTids.removeAll(srcTids);
            CollectionUtil.split(srcTids, 100).forEach(v -> {
                executeInvalidCheck(srcTids);
            });
        }
    }

    private void executeInvalidCheck(ArrayList<String> srcTids) {
        final TradeQueryQueryWithDetailResponse response = fetchBySrcTids(srcTids);
        if (CollectionUtil.isNotEmpty(response.getOrder())) {
            List<WdtOrder> wdtOrderPOs = assembleWdtOrderPersistObjs(response);
            if (!wdtOrderPOs.isEmpty()) {
                wdtOrderMapper.saveOrUpdateBatch(wdtOrderPOs);

                final List<WdtOrderDetail> orderDetails = wdtOrderPOs.stream()
                                                                     .flatMap(v1 -> v1.getOrderDetails()
                                                                                      .stream())
                                                                     .collect(Collectors.toList());
                if (!orderDetails.isEmpty()) {
                    wdtOrderDetailMapper.saveOrUpdateBatch(orderDetails);
                    final Set<String> fetchSrcTids = orderDetails.stream()
                                                                 .map(WdtOrderDetail::getSrcTid)
                                                                 .collect(Collectors.toSet());
                    final List<Long> tradeIds = orderDetails.stream()
                                                            .map(WdtOrderDetail::getTradeId)
                                                            .distinct()
                                                            .collect(Collectors.toList());
                    final List<Long> tradeIds0 = wdtOrderDetailMapper.selectTradeIdsBySrcTids(fetchSrcTids);
                    final Collection<Long> removedTradeIds = CollectionUtil.subtract(tradeIds0, tradeIds);
                    if (CollectionUtil.isNotEmpty(removedTradeIds)) {
                        wdtOrderMapper.deleteByTradeIds(removedTradeIds);
                        wdtOrderDetailMapper.deleteByTradeIds(removedTradeIds);
                    }
                }
            }
        }
    }

    public TradeQueryQueryWithDetailResponse fetchBySrcTids(List<String> srcTids) {
        try {
            final Pager pager = new Pager(srcTids.size() * 25, 0, true);
            final TradeQueryAPI api;
            if (refreshConfig.getFetchWdtOrderByQimen()) {
                api = wdtGateway.getQimenAPI(TradeQueryAPI.class);
            } else {
                api = wdtGateway.getAPI(TradeQueryAPI.class);
            }
            final TradeQueryQueryWithDetailParams params = new TradeQueryQueryWithDetailParams();
            params.setSrcTid(Joiner.on(',').join(srcTids));
            return api.queryWithDetail(params, pager);
        } catch (Throwable e) {
            throw WdtExceptions.wrapFetchException(e, FetchDataType.WDT_ORDER);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveOrUpdateBatchSpecifiedOrdersBySrcTids(List<String> srcTids) {
        final TradeQueryQueryWithDetailResponse response = fetchBySrcTids(srcTids);
        saveOrUpdateBatchByApiQueryResponse(response);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveOrUpdateBatchByApiQueryResponse(TradeQueryQueryWithDetailResponse response) {
        List<WdtOrder> wdtOrderPOs = assembleWdtOrderPersistObjs(response);
        saveOrUpdateBatchByApiQueryResponse(wdtOrderPOs);
    }

    @NonNull
    private static List<WdtOrder> assembleWdtOrderPersistObjs(TradeQueryQueryWithDetailResponse response) {
        List<WdtOrder> wdtOrderPOs = new ArrayList<>();
        for (Order order : response.getOrder()) {
            final WdtOrder wdtOrder = OrderAssembler.INST.apiOrderDtoToPo(order);
            wdtOrderPOs.add(wdtOrder);

            final ArrayList<WdtOrderDetail> wdtOrderDetailsThisOrder = new ArrayList<>();
            for (Detail detail : order.getDetailList()) {
                final WdtOrderDetail wdtOrderDetail = OrderAssembler.INST
                        .apiOrderDetailDtoToPo(detail);
                wdtOrderDetail.setTradeTime(wdtOrder.getTradeTime());
                wdtOrderDetail.setPayTime(wdtOrder.getPayTime());
                wdtOrderDetail.setWarehouseNo(wdtOrder.getWarehouseNo());
                wdtOrderDetail.setProviderId(0L);
                wdtOrderDetailsThisOrder.add(wdtOrderDetail);
            }
            wdtOrder.setOrderDetails(wdtOrderDetailsThisOrder);
        }
        return wdtOrderPOs;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveOrUpdateBatchByApiQueryResponse(List<WdtOrder> wdtOrderPOs) {
        if (!wdtOrderPOs.isEmpty()) {
            wdtOrderMapper.saveOrUpdateBatch(wdtOrderPOs);

            final List<WdtOrderDetail> wdtOrderDetails = wdtOrderPOs.stream()
                                                                    .flatMap(v -> v.getOrderDetails().stream())
                                                                    .collect(Collectors.toList());

            //明细ID集合
            final Set<String> srcTids = wdtOrderDetails.stream().map(WdtOrderDetail::getSrcTid)
                                                       .collect(Collectors.toSet());

            //这批订单明细当前关联的订单ID
            final Set<Long> currentTradeIds = wdtOrderDetails.stream().map(WdtOrderDetail::getTradeId)
                                                             .collect(Collectors.toSet());

            //这批订单明细ID当前在数据库中关联的订单ID
            final List<Long> beforeTradeIds = wdtOrderDetailMapper.getRefTradeIdsBySrcTids(srcTids);

            //可能存在订单明细变更的情况，发生变更时明细ID会发生改变，所以每次更新的时候都需要移除掉已经失效的明细ID
            deleteRemovedDetails(wdtOrderPOs);

            //更新明细数据以及其与订单的关联关系
            if (!wdtOrderDetails.isEmpty()) {
                wdtOrderDetailMapper.saveOrUpdateBatch(wdtOrderDetails);
            }

            //汇总全部订单ID，检查这些订单在关联关系变更后是否已经失效，若失效则将失效订单标记为已删除
            final Set<Long> allTradeIds = CollectionUtil.unionDistinct(currentTradeIds, beforeTradeIds);
            wdtOrderMapper.cleanOrders(allTradeIds, DateUtil.currentTime());

            //保存后置处理，回填ID，订单、明细删除时间等...
            afterSave(wdtOrderPOs);

            //加入待处理队列
            if (!wdtOrderDetails.isEmpty()) {
                wdtOrderDetails.forEach(wdtOrderDetail -> modifiedSrcTids.add(wdtOrderDetail.getSrcTid()));
            }

            //异步触发订单批量保存事件
            EventBusUtil.post(WdtOrderBatchSaveEvent.of(wdtOrderPOs), true);
        }
    }

    /**
     * 回填数据库主键ID
     *
     * @param wdtOrderPOs PO
     */
    private void afterSave(List<WdtOrder> wdtOrderPOs) {
        if (CollectionUtil.isEmpty(wdtOrderPOs)) {
            return;
        }
        final Set<Long> tradeIds = wdtOrderPOs.stream().map(WdtOrder::getTradeId).collect(Collectors.toSet());
        final List<OrderDetailPrimaryIdObj> primaryIdModels = wdtOrderMapper.selectOrderDetailPrimaryIdObjs(tradeIds);
        final Map<Long, Long> primaryIdMap = primaryIdModels.stream()
                                                            .collect(Collectors.toMap(OrderDetailPrimaryIdObj::getTradeId,
                                                                    OrderDetailPrimaryIdObj::getOrderId, (a, b) -> a));
        final Map<Long, Long> detailIdMap = primaryIdModels.stream()
                                                           .collect(Collectors.toMap(OrderDetailPrimaryIdObj::getOrderDetailRecId,
                                                                   OrderDetailPrimaryIdObj::getOrderDetailId,
                                                                   (a, b) -> a));
        final Map<Long, Long> deletedAtMap = primaryIdModels.stream()
                                                            .collect(Collectors.toMap(OrderDetailPrimaryIdObj::getTradeId,
                                                                    OrderDetailPrimaryIdObj::getDeletedAt,
                                                                    (a, b) -> a));
        final Map<Long, Long> detailDeletedAtMap = primaryIdModels.stream()
                                                                  .collect(Collectors.toMap(OrderDetailPrimaryIdObj::getOrderDetailRecId,
                                                                          OrderDetailPrimaryIdObj::getDetailDeletedAt,
                                                                          (a, b) -> a));
        for (WdtOrder wdtOrderPO : wdtOrderPOs) {
            wdtOrderPO.setId(primaryIdMap.get(wdtOrderPO.getTradeId()));
            wdtOrderPO.setDeletedAt(deletedAtMap.getOrDefault(wdtOrderPO.getTradeId(), 0L));
            if (wdtOrderPO.getOrderDetails() != null) {
                for (WdtOrderDetail orderDetail : wdtOrderPO.getOrderDetails()) {
                    orderDetail.setId(detailIdMap.get(orderDetail.getRecId()));
                    orderDetail.setDeletedAt(detailDeletedAtMap.getOrDefault(orderDetail.getRecId(), 0L));
                }
            }
        }
    }

    @Override
    public int cleanOrders(@Nullable Set<Long> tradeIds, Long currentTime) {
        return wdtOrderMapper.cleanOrders(tradeIds, currentTime);
    }

    private void deleteRemovedDetails(List<WdtOrder> wdtOrders) {
        final LambdaUpdateWrapper<WdtOrderDetail> queryForRemoveDetails = Wrappers.lambdaUpdate();
        for (WdtOrder wdtOrder : wdtOrders) {
            final List<Long> recIds = wdtOrder.getOrderDetails().stream()
                                              .map(WdtOrderDetail::getRecId)
                                              .collect(Collectors.toList());
            if (recIds.isEmpty()) {
                queryForRemoveDetails
                        .or(sub -> sub.eq(WdtOrderDetail::getTradeId, wdtOrder.getTradeId()));
            } else {
                queryForRemoveDetails
                        .or(sub -> sub.eq(WdtOrderDetail::getTradeId, wdtOrder.getTradeId())
                                      .notIn(WdtOrderDetail::getRecId, recIds));
            }
        }
        wdtOrderDetailMapper.update(null, queryForRemoveDetails.set(WdtOrderDetail::getDeletedAt,
                DateUtil.currentTime()));
    }
}
