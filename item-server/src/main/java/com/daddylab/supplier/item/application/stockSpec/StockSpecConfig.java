package com.daddylab.supplier.item.application.stockSpec;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2025/1/3
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "stock-spec")
@Configuration
public class StockSpecConfig {

  /** 库管员用户ID列表 */
  private List<Long> adminUids;
}
