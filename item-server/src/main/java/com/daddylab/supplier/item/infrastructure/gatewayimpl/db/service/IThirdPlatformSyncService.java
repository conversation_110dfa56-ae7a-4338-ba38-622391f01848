package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ThirdPlatformSync;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 第三方平台同步信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
public interface IThirdPlatformSyncService extends IDaddyService<ThirdPlatformSync> {

}
