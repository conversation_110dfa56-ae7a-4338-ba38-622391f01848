package com.daddylab.supplier.item.application.criticalPush;

import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.ReflectionsUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.beans.Introspector;
import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Nullable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.ToString;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.ClassUtils;

/**
 * <AUTHOR>
 * @since 2022/4/25
 */
@Setter
@Getter
@NoArgsConstructor
@ToString
public final class MethodInvocationDescriptor implements Serializable {

    private static final long serialVersionUID = 758430168535002376L;
    private String typeName;
    @Nullable
    private String beanName;
    private String methodName;
    private String[] parameterTypes;
    private String[] invocationArgs;

    public MethodInvocationDescriptor(ApplicationContext context, Object target, Method method, Object[] invocationArgs) {
        final Class<?> declaringClass = method.getDeclaringClass();
        String beanName = null;
        if (context != null) {
            final String[] beanNamesForType = context.getBeanNamesForType(declaringClass);
            if (beanNamesForType.length > 0) {
                if (beanNamesForType.length == 1) {
                    beanName = beanNamesForType[0];
                } else {
                    Service serviceAnnotation = declaringClass.getAnnotation(Service.class);
                    if (serviceAnnotation != null) {
                        beanName = serviceAnnotation.value();
                    } else {
                        Component componentAnnotation = declaringClass.getAnnotation(Component.class);
                        if (componentAnnotation != null) {
                            beanName = componentAnnotation.value();
                        }
                    }
                    if (StrUtil.isBlank(beanName)) {
                        beanName = getDefaultBeanName(declaringClass);
                    }
                }
            }
        }

        final String methodName = method.getName();
        final String[] parameterTypes = new String[method.getParameterCount()];
        for (int i = 0; i < method.getParameterCount(); i++) {
            parameterTypes[i] = method.getParameterTypes()[i].getName();
        }
        this.typeName = declaringClass.getName();
        this.beanName = beanName;
        this.methodName = methodName;
        this.parameterTypes = parameterTypes;
        this.invocationArgs = Arrays.stream(invocationArgs).map(JsonUtil::toJson)
                .toArray(String[]::new);
    }

    @NonNull
    private String getDefaultBeanName(Class<?> declaringClass) {
        return Introspector.decapitalize(ClassUtils.getShortName(declaringClass));
    }

    @JsonIgnore
    public Class<?> getType() {
        try {

            return ReflectionsUtil.forName(typeName);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException(e);
        }
    }

    private Optional<Object> getBean(ApplicationContext context)
            throws BeansException {
        if (beanName != null) {
            return Optional.of(Objects.requireNonNull(context, "应用上下文不能为空").getBean(beanName));
        }
        return Optional.empty();
    }

    @JsonIgnore
    public Object getTarget(ApplicationContext context) {
        final Optional<Object> bean = getBean(context);
        return bean.orElseGet(this::newTarget);
    }

    @NonNull
    private Object newTarget() {
        try {
            return getType().newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    @JsonIgnore
    public Method getMethod() {
        try {
            return getType().getDeclaredMethod(methodName,
                    getParameterClasses());
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
    }

    @NonNull
    @JsonIgnore
    public Class<?>[] getParameterClasses() {
        return Arrays.stream(parameterTypes)
                .map(it -> {
                    try {
                        return ReflectionsUtil.forName(it);
                    } catch (ClassNotFoundException e) {
                        throw new RuntimeException("解析方法参数类型失败", e);
                    }
                })
                .toArray(Class[]::new);
    }

    @JsonIgnore
    public Object[] getParsedInvocationArgs() {
        final Object[] objects = new Object[invocationArgs.length];
        final Type[] genericParameterTypes = getMethod().getGenericParameterTypes();
        for (int i = 0; i < invocationArgs.length; i++) {
            final Type parameterType = genericParameterTypes[i];
            objects[i] = JsonUtil.parse(invocationArgs[i], parameterType);
        }
        return objects;
    }

    public Object invoke(ApplicationContext context)
            throws ClassNotFoundException, NoSuchMethodException, InstantiationException, IllegalAccessException, InvocationTargetException {
        final Method method = getMethod();
        if (!method.isAccessible()) {
            method.setAccessible(true);
        }
        return method.invoke(getTarget(context), getParsedInvocationArgs());
    }
}
