package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.JobHandle;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.JobHandleLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.JobHandleLogMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IJobHandleLogService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Service
public class JobHandleLogServiceImpl extends DaddyServiceImpl<JobHandleLogMapper, JobHandleLog> implements IJobHandleLogService {

    @Override
    public void log(JobHandle jobHandle, String msg) {
        final JobHandleLog entity = new JobHandleLog();
        entity.setJob(jobHandle.getJob());
        entity.setScope(jobHandle.getScope());
        entity.setRecId(jobHandle.getRecId());
        entity.setLog(msg);
        save(entity);
    }
}
