package com.daddylab.supplier.item.application.drawer.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.exceptions.UtilException;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.daddylab.supplier.item.application.aws.service.AwsApprovalFlowService;
import com.daddylab.supplier.item.application.category.CategoryBizService;
import com.daddylab.supplier.item.application.drawer.*;
import com.daddylab.supplier.item.application.drawer.compare.ItemDrawerCompare;
import com.daddylab.supplier.item.application.drawer.converter.ItemDrawerConverter;
import com.daddylab.supplier.item.application.drawer.events.ItemDrawerMergeEvent;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.message.QyMsgSendUtils;
import com.daddylab.supplier.item.application.message.wechat.MsgEvent;
import com.daddylab.supplier.item.application.message.wechat.RecipientConfig;
import com.daddylab.supplier.item.application.message.wechat.RecipientUtil;
import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.application.psys.PsysCategoryConfig;
import com.daddylab.supplier.item.application.recognitionTask.ReviewThesaurusHolder;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsVo;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.domain.auth.enums.ResourceType;
import com.daddylab.supplier.item.domain.drawer.enums.*;
import com.daddylab.supplier.item.domain.drawer.form.ItemDrawerForm;
import com.daddylab.supplier.item.domain.drawer.form.ItemDrawerImageForm;
import com.daddylab.supplier.item.domain.drawer.form.LaunchReviewForm;
import com.daddylab.supplier.item.domain.drawer.vo.*;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.item.gateway.BuyerGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemProcurementGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.itemSync.DouLink;
import com.daddylab.supplier.item.domain.itemSync.MiniRedBookLink;
import com.daddylab.supplier.item.domain.itemSync.TaobaoGoodsDetailLink;
import com.daddylab.supplier.item.domain.itemSync.WeChatLink;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.config.QyWeixinDaddyErpProperties;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.QyWeixinFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.PartnerSyncChargePersonReq;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.PartnerSyncChargePersonReqWrapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.SendQyWeixinMsgParam.TextCard;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.CheckDetailDTO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.CheckInfoDTO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemReq;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffListQuery;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQProducer;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil.ChangePropertyObj;
import com.daddylab.supplier.item.types.common.ImgInfo;
import com.daddylab.supplier.item.types.itemDrawer.*;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerAuditTaskState;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerModuleAuditState;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerModuleId;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemLaunchProcessNodeId;
import com.daddylab.supplier.item.types.recognitionTask.ItemDrawerRecognitionHit;
import com.daddylab.supplier.item.types.recognitionTask.ItemDrawerRecognitionResults;
import com.daddylab.supplier.item.types.recognitionTask.ItemDrawerRecognitionStatus;
import com.daddylab.supplier.item.types.recognitionTask.ItemDrawerRecognitionTaskStatus;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.*;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.javers.core.diff.Change;
import org.javers.core.diff.Diff;
import org.javers.core.diff.changetype.PropertyChange;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.daddylab.supplier.item.common.GlobalConstant.EDIT_ALL_LIVE_VERBAL_TRICK;
import static com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus.*;

/**
 * Class  ItemDrawerServiceImpl
 *
 * @Date 2022/5/31下午4:16
 * <AUTHOR>
 */
@Service
@Slf4j
public class ItemDrawerAppServiceImpl implements ItemDrawerService {

    public static final int URGE_CD = 30 * 60;

    /**
     * 商品上新可回退的状态
     */
    public static final List<Integer> CAN_ROLLBACK_STATUS =
            Arrays.asList(
                    TO_BE_DESIGNED.getValue(),
                    TO_BE_AUDITED.getValue(),
                    TO_BE_UPDATED.getValue(),
                    TO_BE_RELEASED.getValue());

    /**
     * 直播话术可回退的状态
     */
    public static final List<Integer> CAN_ROLLBACK_STATUS_LIVE_VERBAL_TRICK =
            Arrays.asList(
                    ItemAuditStatus.WAIT_LEGAL_AUDIT.getValue(),
                    ItemAuditStatus.WAIT_QC_AUDIT.getValue());

    @Autowired
    private IItemDrawerService itemDrawerService;
    @Autowired
    private IItemTrainingMaterialsService itemTrainingMaterialsService;
    @Autowired
    private NewGoodsBizService newGoodsBizService;
    @Autowired
    private IItemDrawerImageService IItemDrawerImageService;
    @Autowired
    private IItemService itemService;
    @Autowired
    private PartnerFeignClient partnerFeignClient;
    @Autowired
    private AwsApprovalFlowService awsApprovalFlowService;
    @Autowired
    private IAwsBusinessLogService awsBusinessLogService;
    @Autowired
    private OperateLogGateway operateLogGateway;
    @Autowired
    private ItemBizService itemBizService;
    @Autowired
    private ItemProcurementGateway itemProcurementGateway;
    @Autowired
    private IItemLaunchPlanService itemLaunchPlanService;
    @Autowired
    private IItemLaunchPlanItemRefService itemLaunchPlanItemRefService;
    @Autowired
    private ItemDrawerMarkImageService itemDrawerMarkImageService;
    @Autowired
    private ItemApprovalAdviceBizService itemApprovalAdviceBizService;
    @Resource
    private IItemDrawerModuleAuditService itemDrawerModuleAuditService;
    @Resource
    private IItemDrawerRecognitionTaskService itemDrawerRecognitionTaskService;

    @Resource
    private ReviewThesaurusHolder reviewThesaurusHolder;

    @Resource
    private ItemDrawerAuditBizService itemDrawerAuditBizService;
    @Autowired
    private BuyerGateway buyerGateway;
    @Autowired
    private UserGateway userGateway;

    @Autowired
    private RecipientConfig recipientConfig;

    @Autowired
    private StaffService staffService;

    @Autowired
    private IItemLaunchStatsService itemLaunchStatsService;
    @Resource
    private IItemDrawerModuleAuditStatsService itemDrawerModuleAuditStatsService;

    @Autowired
    private IItemDrawerModuleAuditTaskService itemDrawerModuleAuditTaskService;

    @Resource
    private ItemLaunchModuleAdviceBizService itemLaunchModuleAdviceBizService;

    @Autowired
    private FileGateway fileGateway;

    @Resource
    private RefreshConfig refreshConfig;

    @Resource
    IItemDrawerLiveVerbalService iItemDrawerLiveVerbalService;
    @Autowired
    private ItemDrawerMergeBizService itemDrawerMergeBizService;
    @Autowired
    private List<ItemDrawerCopyService> itemDrawerCopyServices;
    @Autowired
    private IItemImageService itemImageService;
    @Autowired
    private RocketMQProducer rocketMQProducer;
    @Autowired
    private RecipientUtil recipientUtil;
    @Resource
    IItemDrawerModuleAuditTaskService iItemDrawerModuleAuditTaskService;

    @Resource
    IItemDrawerMergeService iItemDrawerMergeService;
    @Resource
    IItemDrawerMergeItemService iItemDrawerMergeItemService;

    @Autowired
    IBizLevelDivisionService bizLevelDivisionService;

    /**
     * 字段编辑权限
     */
    private final Map<ItemDrawerChangeEnum, ItemDrawerChangeAuthEnum> ITEM_DRAWER_EDIT_AUTH_MAP = new HashMap<>();

    /**
     * 在【待上架】和【已上架】状态下，模块下哪些字段发生修改需要重新触发审核流程
     */
    private final Map<ItemDrawerModuleId, List<ItemDrawerChangeEnum>> needReAuditFields = ImmutableMap.<ItemDrawerModuleId, List<ItemDrawerChangeEnum>>builder()
            .put(ItemDrawerModuleId.IMG_INFO, Arrays.asList(
                    ItemDrawerChangeEnum.IMAGES
            ))
            .put(ItemDrawerModuleId.TEXT_AND_ATTR, Arrays.asList(
                    ItemDrawerChangeEnum.TB_TITLE,
                    ItemDrawerChangeEnum.DOU_TITLE,
                    ItemDrawerChangeEnum.MINI_TITLE,
                    ItemDrawerChangeEnum.HOME_COPY
            ))
            .put(ItemDrawerModuleId.LIVE_VERBAL_TRICK, Arrays.asList(
                    ItemDrawerChangeEnum.LIVE_VERBAL_TRICK
            ))
            .put(ItemDrawerModuleId.DETAILS, Arrays.asList(
                    ItemDrawerChangeEnum.DETAIL_IMAGES
            ))
            .build();

    @PostConstruct
    public void init() {
        ITEM_DRAWER_EDIT_AUTH_MAP.put(ItemDrawerChangeEnum.STANDARD_NAME,
                ItemDrawerChangeAuthEnum.PRINCIPAL);
        ITEM_DRAWER_EDIT_AUTH_MAP.put(ItemDrawerChangeEnum.TB_TITLE,
                ItemDrawerChangeAuthEnum.TAOBAO);
        ITEM_DRAWER_EDIT_AUTH_MAP.put(ItemDrawerChangeEnum.DOU_TITLE,
                ItemDrawerChangeAuthEnum.DOUYIN);
        ITEM_DRAWER_EDIT_AUTH_MAP.put(ItemDrawerChangeEnum.MINI_TITLE,
                ItemDrawerChangeAuthEnum.WEIXIN);
        ITEM_DRAWER_EDIT_AUTH_MAP.put(ItemDrawerChangeEnum.HOME_COPY,
                ItemDrawerChangeAuthEnum.PRINCIPAL);
        ITEM_DRAWER_EDIT_AUTH_MAP.put(ItemDrawerChangeEnum.IMAGES,
                ItemDrawerChangeAuthEnum.PRINCIPAL);
        ITEM_DRAWER_EDIT_AUTH_MAP.put(ItemDrawerChangeEnum.PLAN_ID,
                ItemDrawerChangeAuthEnum.PRINCIPAL);
        ITEM_DRAWER_EDIT_AUTH_MAP.put(ItemDrawerChangeEnum.PRINCIPAL,
                ItemDrawerChangeAuthEnum.PRINCIPAL);
        ITEM_DRAWER_EDIT_AUTH_MAP.put(ItemDrawerChangeEnum.BUYER, ItemDrawerChangeAuthEnum.BUYER);
        ITEM_DRAWER_EDIT_AUTH_MAP.put(ItemDrawerChangeEnum.QC, ItemDrawerChangeAuthEnum.QC);
        ITEM_DRAWER_EDIT_AUTH_MAP.put(ItemDrawerChangeEnum.DETAIL_IMAGES,
                ItemDrawerChangeAuthEnum.DETAIL_IMAGES);
        ITEM_DRAWER_EDIT_AUTH_MAP.put(ItemDrawerChangeEnum.TB_LINK,
                ItemDrawerChangeAuthEnum.TAOBAO);
        ITEM_DRAWER_EDIT_AUTH_MAP.put(ItemDrawerChangeEnum.DOU_LINK,
                ItemDrawerChangeAuthEnum.DOUYIN);
        ITEM_DRAWER_EDIT_AUTH_MAP.put(ItemDrawerChangeEnum.LIVE_VERBAL_TRICK,
                ItemDrawerChangeAuthEnum.LIVE_VERBAL_TRICK);
        ITEM_DRAWER_EDIT_AUTH_MAP.put(ItemDrawerChangeEnum.DOU_YIN_LINK_CONTENT,
                ItemDrawerChangeAuthEnum.DOUYIN);
        ITEM_DRAWER_EDIT_AUTH_MAP.put(ItemDrawerChangeEnum.KUAI_SHOU_LINK_CONTENT,
                ItemDrawerChangeAuthEnum.DOUYIN);
    }

    @Autowired
    private QyWeixinFeignClient qyWeixinFeignClient;
    @Autowired
    private QyWeixinService qyWeixinService;
    @Autowired
    private QyWeixinDaddyErpProperties qyWeixinDaddyErpProperties;

    @Override
    public Response urge(List<Long> recipients, Long itemId, String urger) {
        if (CollectionUtil.isEmpty(recipients)) {
            return Response.buildFailure(ErrorCode.VERIFY_PARAM.getCode(), "请指定催办用户");
        }
        final Supplier<BizException> bizExceptionSupplier = () -> ExceptionPlusFactory.bizException(
                ErrorCode.DATA_NOT_FOUND,
                "商品数据异常");
        final ItemDrawer itemDrawer = itemDrawerService.lambdaQuery()
                .eq(ItemDrawer::getItemId, itemId)
                .oneOpt()
                .orElseThrow(bizExceptionSupplier);
        final Item item = itemService.getById(itemId);
        if (item == null) {
            throw bizExceptionSupplier.get();
        }
        final Long recipient = recipients.get(0);
        final String key =
                "ITEM_LAUNCH_URGE_" + itemId + "_" + item.getLaunchStatus() + "_" + recipient;
        if (RedisUtil.get(key) != null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.URGE_TOO_FREQUENT);
        }
        final NewGoods newGoods = newGoodsBizService.getNewGoods(itemId);
        if (newGoods == null) {
            throw bizExceptionSupplier.get();
        }
        final List<DadStaffVO> staffList = staffService.getStaffList(
                Collections.singletonList(recipient));
        if (CollectionUtil.isEmpty(staffList)) {
            return Response.buildFailure(ErrorCode.DATA_NOT_FOUND.getCode(), "未能查询到催办用户的身份信息");
        }
        final Map<Long, DadStaffVO> staffVOMap = staffList.stream()
                .collect(Collectors.toMap(DadStaffVO::getUserId, Function.identity()));
        final String token = qyWeixinService.getTokenWithCache();
        //【待完善】状态停留在列表即可，其他状态需要自动打开商品库抽屉
        final String urgeItemUrl =
                !Objects.equals(item.getLaunchStatus(), TO_BE_IMPROVED.getValue())
                        ? StringUtil.format("{}?productId={}", recipientConfig.getNewGoodLink(),
                        itemId.toString())
                        : StringUtil.format("{}?itemId={}", recipientConfig.getNewGoodLink(),
                        itemId.toString());
        final TextCard textCard = TextCard.of(
                StringUtil.format("「{}」提醒您处理商品：「{}」的资料信息，请查看后处理", urger,
                        Optional.ofNullable(itemDrawer.getStandardName())
                                .filter(StringUtil::isNotBlank).orElse(item.getName())),
                StringUtil.format("商品编码：「{}」 商品状态：「{}」",
                        item.getCode(),
                        Objects.requireNonNull(ItemLaunchStatus.of(item.getLaunchStatus()),
                                "商品上新状态无效值异常").getDesc()),
                urgeItemUrl
        );
        final String qwUserId = Optional.ofNullable(staffVOMap.get(recipient))
                .map(DadStaffVO::getQwUserId)
                .orElseThrow(() -> ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND,
                        "未查询到该用户的企业微信用户ID，无法催办"));
//        final SendQyWeixinMsgParam param = new SendQyWeixinMsgParam();

//        param.setTouser(qwUserId);
//        param.setAgentid(qyWeixinDaddyErpProperties.getAgentid());
//        param.setMsgtype("textcard");
//        param.setTextCard(textCard);
//        qyWeixinFeignClient.sendMessage(token, param);

        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("催办人花名", urger);
        variables.put("产品标准名", Optional.ofNullable(itemDrawer.getStandardName())
                .filter(StringUtil::isNotBlank).orElse(item.getName()));
        variables.put("商品编码", item.getCode());
        variables.put("商品状态", Objects.requireNonNull(ItemLaunchStatus.of(item.getLaunchStatus()),
                "商品上新状态无效值异常").getDesc());
        variables.put("被通知人", qwUserId);
        variables.put("itemId", itemId);
        QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.ITEM_LAUNCH_URGING, variables);

        RedisUtil.set(key, DateUtil.currentTime(), URGE_CD, TimeUnit.SECONDS);
        return Response.buildSuccess();
    }

    @Transactional
    @Override
    public SingleResponse<Long> add(ItemDrawerForm itemDrawerForm, Long userId, String loginName) {
        return SingleResponse.of(save(itemDrawerForm, userId, true).getRight());
    }

    /**
     * 参数校验
     *
     * @param itemDrawerForm
     * @param userId
     * @return
     */
    Pair<ItemDrawer, Item> check(ItemDrawerForm itemDrawerForm, Long userId) {
        return check(itemDrawerForm, userId, null, new AtomicReference<>());
    }

    /**
     * 参数校验
     *
     * @param itemDrawerForm
     * @param userId
     * @return
     */
    Pair<ItemDrawer, Item> check(ItemDrawerForm itemDrawerForm, Long userId,
                                 AtomicBoolean isChange, AtomicReference<String> changePropertiesStr) {
        Item item = itemService.getById(itemDrawerForm.getItemId());
        if (Objects.isNull(item)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "商品不存在");
        }
        ItemDrawer updateItemDrawer = new ItemDrawer();
        updateItemDrawer.setId(itemDrawerForm.getId());
        ItemDrawer itemDrawer = itemDrawerService.getById(itemDrawerForm.getId());
        ItemDrawerCompare itemDrawerCompareForm = ItemDrawerConverter.INSTANCE.itemDrawerFormToCompare(
                itemDrawerForm);

        if (Objects.nonNull(itemDrawerForm.getDetailImages())) {
            itemDrawerCompareForm.setDetailImages(
                    itemDrawerForm.getDetailImages().stream().map(ItemDrawerImageForm::getUrl)
                            .collect(Collectors.toList()));
        }
        if (Objects.nonNull(itemDrawerForm.getMainImageVideo())) {
            itemDrawerCompareForm.setMainImageVideo(
                    itemDrawerForm.getMainImageVideo().stream().map(ItemDrawerImageForm::getUrl)
                            .collect(Collectors.toList()));
        }
        if (Objects.nonNull(itemDrawerForm.getSkuImages())) {
            itemDrawerCompareForm.setSkuImages(
                    itemDrawerForm.getSkuImages().stream().filter(v -> Objects.nonNull(v.getUrl()))
                            .collect(Collectors.toList()));
        }
        //Ver1.7.0(2022-08-29):新增淘宝链接、抖音链接，全员可见，渠道运营可编辑
        if (Objects.nonNull(itemDrawerForm.getTbLink())) {
            itemDrawerCompareForm.setTbLink(itemDrawerForm.getTbLink());
        }
        if (Objects.nonNull(itemDrawerForm.getDouLink())) {
            itemDrawerCompareForm.setDouLink(itemDrawerForm.getDouLink());
        }
        if (Objects.nonNull(itemDrawerForm.getWechatLink())) {
            itemDrawerCompareForm.setWechatLink(itemDrawerForm.getWechatLink());
        }
        if (StringUtil.isNotBlank(itemDrawerForm.getMiniRedBookLink())) {
            itemDrawerCompareForm.setMiniRedBookLink(itemDrawerForm.getMiniRedBookLink());
        }
        refreshDrawerImageCache(itemDrawerForm.getId());
        final Long itemId = item.getId();
        ItemDrawerCompare itemDrawerCompareModel = ItemDrawerConverter.INSTANCE.itemDrawerToCompare(
                itemDrawer);
        final Triple<List<ItemDrawerImageVO>, List<ItemDrawerImageVO>, List<ItemDrawerImageVO>> drawerImages = getItemDrawerImages(
                itemDrawerForm.getId());
        itemDrawerCompareModel.setImages(drawerImages.getLeft().stream().map(ItemDrawerImageVO::getUrl)
                .collect(Collectors.toList()));
        itemDrawerCompareModel.setOtherImages(drawerImages.getMiddle().stream().map(ItemDrawerImageVO::getUrl)
                .collect(Collectors.toList()));
        itemDrawerCompareModel.setDetailImages(
                drawerImages.getRight().stream().map(ItemDrawerImageVO::getUrl)
                        .collect(Collectors.toList()));
        itemDrawerCompareModel.setSkuImages(
                getItemDrawerSkuImages(itemDrawer.getId(), itemDrawer.getItemId()).stream()
                        .filter(v -> Objects.nonNull(v.getUrl())).collect(
                                Collectors.toList()));
        final List<ItemDrawerAttrImages> itemDrawerAttrImages = getItemDrawerAttrImages(
                itemDrawer.getId(), itemDrawer.getItemId());
        itemDrawerCompareModel.setAttrImages(itemDrawerAttrImages);
        final List<ItemDrawerImage> mainImageVideo = getDrawerImagesCache(itemDrawerForm.getId(),
                ItemDrawerImageTypeEnum.MAIN_IMAGE_VIDEO);
        itemDrawerCompareModel.setMainImageVideo(
                mainImageVideo.stream().map(ItemDrawerImage::getUrl).collect(Collectors.toList()));
        NewGoods newGoods = newGoodsBizService.getNewGoods(itemDrawerForm.getItemId());
        if (Objects.nonNull(newGoods)) {
            itemDrawerCompareModel.setPrincipalId(newGoods.getPrincipalId());
        }
        ItemProcurement procurement = itemProcurementGateway.getProcurementByItemId(
                itemDrawerForm.getItemId());
        if (Objects.nonNull(procurement)) {
            buyerGateway.buyer(procurement.getBuyerId())
                    .ifPresent(value -> itemDrawerCompareModel.setBuyerId(value.getUserId()));
            itemDrawerCompareModel.setQcIds(procurement.getQcIds());
        }
        ItemLaunchPlanItemRef itemLaunchPlanItemRef = itemLaunchPlanItemRefService.getItemLaunchPlanItemRef(
                itemDrawerForm.getItemId());
        if (Objects.nonNull(itemLaunchPlanItemRef)) {
            itemDrawerCompareModel.setPlanId(itemLaunchPlanItemRef.getPlanId());
        }
        //过滤null的字段
        syncFormNull(itemDrawerCompareModel, itemDrawerCompareForm);

        Diff diff = DiffUtil.diff(itemDrawerCompareModel, itemDrawerCompareForm);
        List<DiffUtil.ChangePropertyObj> changeProperties = new ArrayList<>();
        List<ItemDrawerChangeEnum> changeEnums = new ArrayList<>();
        for (Change change : diff.getChanges()) {
            String propertyName = "";
            Object oldValue = null;
            Object newValue = null;
            if (change instanceof PropertyChange) {
                final PropertyChange<?> propertyChange = (PropertyChange<?>) change;
                propertyName = propertyChange.getPropertyName();
                oldValue = propertyChange.getLeft();
                newValue = propertyChange.getRight();
            }

            ItemDrawerChangeEnum itemDrawerChangeEnum = ItemDrawerChangeEnum.of(propertyName);
            if (Objects.isNull(itemDrawerChangeEnum)) {
                continue;
            }
            if (!hasPermission(itemDrawerChangeEnum)) {
                throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT,
                        "您没有编辑'" + itemDrawerChangeEnum.getDesc() + "'字段的权限");
            }
            changeEnums.add(itemDrawerChangeEnum);
            changeProperties.add(
                    new DiffUtil.ChangePropertyObj(itemDrawerChangeEnum.getDesc(), oldValue,
                            newValue));
        }
        if (!Objects.equals(itemDrawerCompareModel.getAttrImages(), itemDrawerCompareForm.getAttrImages())) {
          changeProperties.add(
              new DiffUtil.ChangePropertyObj(
                  ItemDrawerChangeEnum.ATTR_IMAGES.getDesc(),
                  itemDrawerCompareModel.getAttrImages(),
                  itemDrawerCompareForm.getAttrImages()));
        }
        //商品状态为【待上架】和【已上架】，特定字段发生修改需重新审核
        boolean reAudit = false;
        Map<ItemDrawerModuleId, Collection<ItemDrawerChangeEnum>> modifiedModuleIds = Collections.emptyMap();
        if (refreshConfig.getStatusOfTriggersReAudit().stream()
                .anyMatch(v -> Objects.equals(v, item.getLaunchStatus()))) {
            modifiedModuleIds = Maps.newHashMap();
            for (Map.Entry<ItemDrawerModuleId, List<ItemDrawerChangeEnum>> entry : needReAuditFields.entrySet()) {
                final Collection<ItemDrawerChangeEnum> intersection = CollectionUtil.intersection(
                        entry.getValue(), changeEnums);
                if (!intersection.isEmpty()) {
                    modifiedModuleIds.put(entry.getKey(), intersection);
                }
            }
            if (!modifiedModuleIds.isEmpty()) {
                if (!Boolean.TRUE.equals(itemDrawerForm.getConfirmReAudit())) {
                    final String modules = modifiedModuleIds.keySet().stream()
                            .map(v -> "「" + v.getDesc() + "」").collect(
                                    Collectors.joining(""));
                    throw ExceptionPlusFactory.bizException(ErrorCode.NEED_CONFIRM,
                            modules + "信息发生变化，将触发审核流程，是否提交审核？");
                } else {
                    reAudit = true;
                }
            }
        }
        if (!changeProperties.isEmpty()) {
            for (ChangePropertyObj changeProperty : changeProperties) {
                final ItemDrawerChangeEnum itemDrawerChangeEnum = ItemDrawerChangeEnum.ofDesc(
                        changeProperty.getProperty());
                Objects.requireNonNull(itemDrawerChangeEnum);
                switch (itemDrawerChangeEnum) {
                    case TYPE:
                        Optional.ofNullable(itemDrawerForm.getType()).ifPresent(
                                type -> {
                                    updateItemDrawer.setType(type);
                                    itemLaunchPlanItemRefService.lambdaUpdate()
                                            .eq(ItemLaunchPlanItemRef::getItemId, itemId)
                                            .set(ItemLaunchPlanItemRef::getItemType,
                                                    itemDrawerForm.getType().getValue())
                                            .update();
                                });
                        break;
                    case STANDARD_NAME:
                        if (StringUtils.isEmpty(itemDrawerForm.getStandardName())) {
                            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM,
                                    "产品标准名不能为空");
                        }
                        updateItemDrawer.setStandardName(itemDrawerForm.getStandardName());
                        break;
                    case TB_TITLE:
                        final boolean isMerchantEntry =
                            bizLevelDivisionService.containValue(
                                BizUnionTypeEnum.SPU, itemId, DivisionLevelValueEnum.B_MERCHANT_ENTER);
                        if (StringUtils.isEmpty(itemDrawerForm.getTbTitle()) && !isMerchantEntry) {
                          throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "淘宝标题不能为空");
                        }
                        updateItemDrawer.setTbTitle(itemDrawerForm.getTbTitle());
                        break;
                    case MINI_TITLE:
                        updateItemDrawer.setMiniTitle(itemDrawerForm.getMiniTitle());
                        break;
                    case DOU_TITLE:
                        updateItemDrawer.setDouTitle(itemDrawerForm.getDouTitle());
                        break;
                    case HOME_COPY:
                        if (StringUtils.isEmpty(itemDrawerForm.getHomeCopy())) {
                            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "首页文案不能为空");
                        }
                        updateItemDrawer.setHomeCopy(itemDrawerForm.getHomeCopy());
                        break;
                    case TB_LINK:
                        updateItemDrawer.setTbLink(itemDrawerForm.getTbLink());
                        try {
                            updateItemDrawer.setTbId(
                                    new TaobaoGoodsDetailLink(itemDrawerForm.getTbLink()).getGoodsId());
                        } catch (Exception ignored) {
                        }
                        break;
                    case DOU_LINK:
                        updateItemDrawer.setDouLink(itemDrawerForm.getDouLink());
                        try {
                            updateItemDrawer.setDouId(
                                    new DouLink(itemDrawerForm.getDouLink()).getGoodsId());
                        } catch (Exception ignored) {
                        }
                        break;
                    case WECHAT_LINK:
                        updateItemDrawer.setWechatLink(itemDrawerForm.getWechatLink());
                        updateItemDrawer.setWechatId(WeChatLink.getId(itemDrawerForm.getWechatLink()));
                        break;
                    case MINI_RED_BOOK_LINK:
                        updateItemDrawer.setMiniRedBookLink(itemDrawerForm.getMiniRedBookLink());
                        try {
                            updateItemDrawer.setMiniRedBookId(MiniRedBookLink.getGoodsId(itemDrawerForm.getMiniRedBookLink()));
                        } catch (Exception ignored) {

                        }
                        break;
                    case PLAN_ID:
                        if (Objects.isNull(itemDrawerForm.getPlanId())) {
                            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM,
                                    "planId不能为空");
                        }
                        // [同步修改上新计划]
                        itemLaunchPlanService.syncItemLaunchPlan(itemDrawerForm.getItemId(),
                                itemDrawerForm.getPlanId());
                        break;
                    case IMAGES:
                        if (itemDrawerForm.getImages().isEmpty()) {
                            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "商品图片不能为空");
                        }
                        //保存商品图
                        saveImage(itemDrawerForm.getId(), itemDrawerForm.getImages().stream().map(ItemDrawerImageForm::getUrl).collect(Collectors.toList()),
                                ItemDrawerImageTypeEnum.ITEM, null);
                        break;
                    case OTHER_IMAGES:
                        if (itemDrawerForm.getOtherImages().isEmpty()) {
                            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "商品图片不能为空");
                        }
                        //保存商品图
                        saveImage(itemDrawerForm.getId(), itemDrawerForm.getOtherImages().stream().map(ItemDrawerImageForm::getUrl).collect(Collectors.toList()),
                                ItemDrawerImageTypeEnum.OTHER_ITEM, null);
                        break;
                    case DETAIL_IMAGES:
                        if (itemDrawerForm.getDetailImages().isEmpty()) {
                            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM,
                                    "商品详情图片不能为空");
                        }
                        //保存详情图
                        saveDetailImage(itemDrawerForm.getId(), itemDrawerForm.getDetailImages());
                        break;
                    case MAIN_IMAGE_VIDEO:
                        if (itemDrawerForm.getMainImageVideo() != null) {
                            //保存主图视频
                            saveMainImageVideo(itemDrawerForm.getId(),
                                    itemDrawerForm.getMainImageVideo());
                        }
                        break;
                    case SKU_IMAGES:
                        if (!CollectionUtil.isEmpty(itemDrawerForm.getSkuImages())) {
                            //保存详情图
                            saveSkuImage(itemDrawerForm.getId(), itemDrawerForm.getSkuImages());
                        }
                        break;
                    case ATTR_IMAGES:
                        if (!CollectionUtil.isEmpty(itemDrawerForm.getAttrImages())) {
                            //保存属性图片
                            saveAttrImages(itemDrawerForm.getId(), itemDrawerForm.getAttrImages());
                        }
                        break;
                    case BUYER:
                        //同步采购负责人  ---> //后端商品
                        //itemProcurementGateway.syncBuyer(itemDrawer.getItemId(), itemDrawerForm.getBuyerId());
                        break;
                    case PRINCIPAL:
                        //同步产品负责人  ---> //新品库
                        newGoodsBizService.syncPrincipal(itemDrawer.getItemId(), itemDrawerForm.getPrincipalId());
                        //发送产品负责人变更事件
//                        MsgEvent event = MsgEvent.buildByPrincipalChange(itemDrawerForm.getPrincipalId(), userId,
//                                ListUtil.of(itemDrawerForm.getItemId()));
//                        EventBusUtil.post(event, true);

                        noticeItemLaunchModifyPrincipals(UserContext.getNickName(),
                                itemDrawerForm.getPrincipalId(), item.getName(), item.getId());
                        break;
                    case QC:
                        //同步qc负责人  ---> //新品库
                        newGoodsBizService.syncQc(itemDrawer.getItemId(), itemDrawerForm.getQcIds());

                        // ERP QC改动通知P系统
                        List<Long> targetItemIdList = CollUtil.isNotEmpty(itemDrawerForm.getMergeItemIds()) ?
                                itemDrawerForm.getMergeItemIds() : ListUtil.of(itemDrawerForm.getItemId());
                        final Map<Long, Long> itemBuyerUidMap = itemProcurementGateway.getBuyerUidList(targetItemIdList);

                        itemService.lambdaQuery()
                                .in(Item::getId, targetItemIdList)
                                .list().stream()
                                .filter(val -> StrUtil.isNotBlank(val.getPartnerProviderItemSn()))
                                .forEach(val -> {

                                    PartnerSyncChargePersonReq req = new PartnerSyncChargePersonReq();
                                    req.setItemNo(val.getPartnerProviderItemSn());
                                    final List<Long> qcIdList = Arrays.stream(itemDrawerForm.getQcIds().split(StrUtil.COMMA))
                                            .map(Long::valueOf).collect(Collectors.toList());
                                    req.setQcIds(qcIdList);
                                    req.setPurchaseId(itemBuyerUidMap.get(val.getId()));
                                    final PartnerSyncChargePersonReqWrapper reqWrapper = PartnerSyncChargePersonReqWrapper.of(ListUtil.of(req));
                                    final Rsp<Object> objectRsp = partnerFeignClient.notifyBuryOrQcChange(reqWrapper);
                                    if (!objectRsp.isSuccess()) {
                                        log.error("商品抽屉QC负责人改变，同步P系统失败.res:{}", JsonUtil.toJson(objectRsp));
                                    }
                                });

                        break;
                    case DOU_YIN_LINK_CONTENT:
                        updateItemDrawer.setDouYinLinkContent(itemDrawerForm.getDouYinLinkContent());
                    case KUAI_SHOU_LINK_CONTENT:
                        updateItemDrawer.setKuaiShouLinkContent(itemDrawerForm.getKuaiShouLinkContent());

                }
            }
            if (isChange != null) {
                isChange.set(true);
            }
            itemDrawerService.updateById(updateItemDrawer);
            //添加变更日志
            operateLogGateway.addOperatorLog(userId, OperateLogTarget.ITEM_DRAWER,
                    itemDrawerForm.getId(),
                    "商品抽屉信息变更", changeProperties);
            changePropertiesStr.set(changeProperties.stream().map(ChangePropertyObj::getProperty).collect(Collectors.joining(",")));
        }
        //触发重新审核流程
        if (reAudit) {
//            itemService.reAudit(item.getId());
//            itemDrawerAuditBizService.createProcess(ItemAuditType.ITEM_MATERIAL,
//                    UserContext.getUserId(), item.getId(),
//                    modifiedModuleIds.keySet().toArray(new ItemDrawerModuleId[0]));
//            //添加变更日志
//            operateLogGateway.addOperatorLog(userId, OperateLogTarget.ITEM_DRAWER,
//                    itemDrawerForm.getId(),
//                    "商品抽屉模块信息变更，触发审核流程", null);
//
//            MsgEvent of = MsgEvent.of(ListUtil.of(item.getId()), MsgEventType.DESIGN_TO_AUDIT_LEGAL);
//            EventBusUtil.post(of, true);

        }
        invalidateItemDrawerImageCache(itemDrawer.getId());
        // 处理合并审核商品
        doMergeItem(itemDrawerForm);

        return Pair.of(updateItemDrawer, item);
    }

    @Override
    public void noticeItemLaunchModifyPrincipals(String operatorNick, Long newPrincipalId, String itemName, Long itemId) {
        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("修改者", operatorNick);
        variables.put("商品名称", itemName);
        variables.put("商品数量", 1);
        variables.put("新负责人", StaffAssembler.INST.toStaffBrief(newPrincipalId).getQwUserId());
        variables.put("itemids", itemId);
        QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.ITEM_LAUNCH_MODIFY_PRINCIPALS, variables);
    }


    /**
     * 处理合并审核
     *
     * @param itemDrawerForm ItemDrawerForm
     * @date 2024/4/9 11:17
     * <AUTHOR>
     */
    private void doMergeItem(ItemDrawerForm itemDrawerForm) {
        // 绑定关联关系
        ItemDrawerMergeItem itemDrawerMergeItem = itemDrawerMergeBizService.getItemDrawerMergeItem(itemDrawerForm.getItemId());
        if (Objects.isNull(itemDrawerMergeItem)) {
            // 若当前商品不存在关联关系
            if (CollUtil.isEmpty(itemDrawerForm.getMergeItemIds())) {
                return;
            }
            // 商品状态校验
            List<Item> checkItems = itemService.selectBatchByIds(itemDrawerForm.getMergeItemIds());
            for (Item checkItem : checkItems) {
                if (Objects.equals(checkItem.getLaunchStatus(), TO_BE_IMPROVED.getValue())) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "存在待完善的商品");
                }
            }

            //查询需要添加的商品是否已经存在其他包中
            List<ItemDrawerMergeItem> itemDrawerMergeItemList = itemDrawerMergeBizService.getItemDrawerMergeItemList(itemDrawerForm.getMergeItemIds());
            if (!itemDrawerMergeItemList.isEmpty()) {
                List<Item> items = itemService.selectBatchByIds(itemDrawerMergeItemList.stream().map(ItemDrawerMergeItem::getItemId).collect(Collectors.toList()));
                String remark = items.stream().map(Item::getCode).collect(Collectors.joining(StrUtil.COMMA));
                throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, String.format("选择的商品%s已存在其他包中, 请重新选择!", remark));
            }
            // 新增的关联关系
            itemDrawerMergeBizService.doSave(itemDrawerForm.getId(), itemDrawerForm.getItemId(), itemDrawerForm.getMergeItemIds());
            return;
        }
        if (CollUtil.isEmpty(itemDrawerForm.getMergeItemIds())) {
            // 触发同步
            final List<ItemDrawerMergeItem> itemDrawerMergeItemList = itemDrawerMergeBizService.getItemDrawerMergeItemListByMergeId(
                    itemDrawerMergeItem.getMergeId());
            if (!itemDrawerMergeItemList.isEmpty()) {
                final List<Long> itemIdsInPack = itemDrawerMergeItemList.stream()
                        .map(ItemDrawerMergeItem::getItemId)
                        .collect(Collectors.toList());
                itemDrawerMergeBizService.doCopy(itemDrawerMergeItem.getMergeId(), itemIdsInPack);
            }
            // 解散包
            itemDrawerMergeBizService.disbandPackage(itemDrawerMergeItem);
            return;
        }
        // 商品状态校验
        List<Item> checkItems = itemService.selectBatchByIds(itemDrawerForm.getMergeItemIds());
        for (Item checkItem : checkItems) {
            if (Objects.equals(checkItem.getLaunchStatus(), TO_BE_IMPROVED.getValue())) {
                throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "存在待完善的商品:" + checkItem.getCode());
            }
        }
        ItemDrawerMerge itemDrawerMerge = itemDrawerMergeBizService.getItemDrawerMergeById(itemDrawerMergeItem.getMergeId());
        if (Objects.isNull(itemDrawerMerge)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "包数据异常");
        }

        // 校验商品是否在不同的包中
        List<ItemDrawerMergeItem> formItemDrawerMergeItemList = itemDrawerMergeBizService.getItemDrawerMergeItemList(itemDrawerForm.getMergeItemIds());
        Set<Long> formMergeIds = formItemDrawerMergeItemList.stream().map(ItemDrawerMergeItem::getMergeId).collect(Collectors.toSet());
        if (formMergeIds.size() > 1) {
            List<Long> existsItemIds = formItemDrawerMergeItemList.stream().filter(formItemDrawerMergeItem -> !itemDrawerMergeItem.getMergeId().equals(formItemDrawerMergeItem.getMergeId())).map(ItemDrawerMergeItem::getItemId).collect(Collectors.toList());
            List<Item> items = itemService.selectBatchByIds(existsItemIds);
            String remark = items.stream().map(Item::getCode).collect(Collectors.joining(StrUtil.COMMA));
            throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, String.format("选择的商品%s已存在其他包中, 请重新选择!", remark));
        }
        List<ItemDrawerMergeItem> itemDrawerMergeItemList = itemDrawerMergeBizService.getItemDrawerMergeItemListByMergeId(itemDrawerMergeItem.getMergeId());
        // 包中当前的商品
        List<Long> originItemIds = itemDrawerMergeItemList.stream().map(ItemDrawerMergeItem::getItemId).collect(Collectors.toList());

        // 需要移除的商品
        List<Long> deleteItemIds = CollUtil.subtractToList(originItemIds, itemDrawerForm.getMergeItemIds());
        if (!deleteItemIds.isEmpty()) {
            itemDrawerMergeBizService.removeItemByIds(itemDrawerMergeItem.getMergeId(), deleteItemIds);
            // 触发同步
            itemDrawerMergeBizService.doCopy(itemDrawerMergeItem.getMergeId(), deleteItemIds);

            // 如果主商品被删除，则当前商品成为主商品
            if (deleteItemIds.contains(itemDrawerMerge.getItemId())) {
                itemDrawerMerge.setItemId(itemDrawerForm.getRawItemId());
                final ItemDrawer rawItemDrawer = getItemDrawer(itemDrawerForm.getRawItemId());
                itemDrawerMerge.setItemDrawerId(rawItemDrawer.getId());
                itemDrawerMergeBizService.update(itemDrawerMerge);
            }
        }
        // 需要新增的包
        List<Long> needAddItemIds = CollUtil.subtractToList(itemDrawerForm.getMergeItemIds(), originItemIds);
        // 若当前商品存在关联关系
        if (!needAddItemIds.isEmpty()) {
            // 新增的关联关系
            itemDrawerMergeBizService.doAddMergeItem(itemDrawerMergeItem.getMergeId(), needAddItemIds);
        }
        SpringUtil.publishEvent(new ItemDrawerMergeEvent(this, itemDrawerMergeItem.getMergeId()));
    }

    @NonNull
    private String filterRichText(String liveVerbalTrick) {
        return liveVerbalTrick.trim();
    }

    /**
     * 若字段为null则同步另一个字段为null
     *
     * @param itemDrawerCompareModel
     * @param itemDrawerCompareForm
     */
    private void syncFormNull(ItemDrawerCompare itemDrawerCompareModel,
                              ItemDrawerCompare itemDrawerCompareForm) {
        Class<? extends ItemDrawerCompare> modelClass = itemDrawerCompareModel.getClass();
        for (Field declaredField : itemDrawerCompareForm.getClass().getDeclaredFields()) {
            try {
                declaredField.setAccessible(true);
                Object o = declaredField.get(itemDrawerCompareForm);
                if (o == null) {
                    Field modelField = modelClass.getDeclaredField(declaredField.getName());
                    modelField.setAccessible(true);
                    modelField.set(itemDrawerCompareModel, null);
                }
            } catch (Exception ignored) {
            }
        }
    }


    /**
     * 保存操作
     *
     * @param itemDrawerForm
     * @param userId
     * @return
     */
    Pair<Boolean, Long> save(ItemDrawerForm itemDrawerForm, Long userId, Boolean isNewRecord) {
        /**
         * 参数校验
         */
        final AtomicBoolean isChange = new AtomicBoolean();
        final AtomicReference<String> changePropertiesStr = new AtomicReference<>();
        Pair<ItemDrawer, Item> checkPair = check(itemDrawerForm, userId, isChange, changePropertiesStr);
        ItemDrawer updateItemDraw = checkPair.getLeft();
        Item item = checkPair.getRight();
        if (isChange.get()) {
            //如果商品上新状态为已上架，那么数据有更新时需要通知
            final ItemLaunchStatus itemLaunchStatus = ItemLaunchStatus.of(item.getLaunchStatus());
            if (itemLaunchStatus == ItemLaunchStatus.HAS_BE_RELEASED) {
                EventBusUtil.post(MsgEvent.buildByOneFieldChange(Collections.singletonList(item.getId())), true);
            }
            // 2023-07-13 七喜。【待上架】、【已上架】任何修改，点击【提交】。产品负责人、指定运营
            boolean b = itemLaunchStatus == ItemLaunchStatus.HAS_BE_RELEASED || itemLaunchStatus == TO_BE_RELEASED;
            if (b) {
                EventBusUtil.post(MsgEvent.buildShelfItemChange(Collections.singletonList(item.getId()),
                        UserContext.getUserId(), changePropertiesStr.get()), true);
            }
        }
        return Pair.of(true, updateItemDraw.getId());
    }

    /**
     * 存储图片
     *
     * @param drawerId
     * @param imageList
     * @param itemDrawerImageTypeEnum
     * @return
     */
    private ArrayList<ItemDrawerImage> saveImage(Long drawerId, List<String> imageList,
                                                 ItemDrawerImageTypeEnum itemDrawerImageTypeEnum,
                                                 Long liveVerbalTrickId) {
        final Map<String, File> fileMap = fileGateway.fileQueryBatchByUrls(imageList);
        //商品主图需要检查尺寸比例
        if (itemDrawerImageTypeEnum == ItemDrawerImageTypeEnum.ITEM) {
            for (String imageUrlStr : imageList) {
                final File file = fileMap.get(imageUrlStr);
                if (file == null || !NumberUtil.isPositive(file.getWidth()) || !NumberUtil.isPositive(
                        file.getHeight())) {
                    if (refreshConfig.getSkipCheckWhenDrawerImageMetaMissing()) {
                        log.info("图片元信息缺失，跳过检查:{}", imageUrlStr);
                        continue;
                    }
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM,
                            "未获取到图片元信息，无法检查图片比例");
                }
                if (file.getWidth() - file.getHeight() > 0) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM,
                            "商品主图比例需为1:1，请检查后重新上传");
                }
            }
        }
        //删除旧的图片
        IItemDrawerImageService.getBaseMapper().delete(new LambdaQueryWrapper<ItemDrawerImage>() {{
            eq(ItemDrawerImage::getDrawerId, drawerId);
            eq(ItemDrawerImage::getType, itemDrawerImageTypeEnum);
            eq(Objects.nonNull(liveVerbalTrickId) && liveVerbalTrickId != 0,
                    ItemDrawerImage::getLiveVerbalTrickId, liveVerbalTrickId);
        }});
        long sort = 1;
        final ArrayList<ItemDrawerImage> itemDrawerImages = new ArrayList<ItemDrawerImage>();
        for (String image : imageList) {
            final File file = fileMap.get(image);
            ItemDrawerImage itemDrawerImage = new ItemDrawerImage();
            itemDrawerImage.setDrawerId(drawerId);
            itemDrawerImage.setUrl(image);
            itemDrawerImage.setType(itemDrawerImageTypeEnum);
            itemDrawerImage.setFileType(ItemDrawerImageFileTypeEnum.IMAGE.getValue());
            itemDrawerImage.setSort(sort);
            if (file != null) {
                itemDrawerImage.setFilename(file.getFullName());
                itemDrawerImage.setExt(file.getExt());
                itemDrawerImage.setWidth(file.getWidth());
                itemDrawerImage.setHeight(file.getHeight());
                itemDrawerImage.setProportion(file.getProportion());
            } else {
                itemDrawerImage.setFilename(StringUtils.getFilename(image));
                itemDrawerImage.setExt(StringUtils.getFilenameExtension(image));
            }
            itemDrawerImage.setLiveVerbalTrickId(liveVerbalTrickId);
            IItemDrawerImageService.getBaseMapper().insert(itemDrawerImage);
            itemDrawerImages.add(itemDrawerImage);
            sort++;
        }
        return itemDrawerImages;
    }

    /**
     * 存储图片
     *
     * @param drawerId
     * @param imageList
     */
    private void saveDetailImage(Long drawerId, List<ItemDrawerImageForm> imageList) {
        //删除旧的图片
        IItemDrawerImageService.getBaseMapper().delete(new LambdaQueryWrapper<ItemDrawerImage>() {{
            eq(ItemDrawerImage::getDrawerId, drawerId);
            eq(ItemDrawerImage::getType, ItemDrawerImageTypeEnum.DETAIL);
        }});
        final List<String> imgUrls = imageList.stream().map(ItemDrawerImageForm::getUrl)
                .collect(Collectors.toList());
        final Map<String, File> fileMap = fileGateway.fileQueryBatchByUrls(imgUrls);
        long sort = 1;
        for (ItemDrawerImageForm image : imageList) {
            final File file = fileMap.get(image.getUrl());
            ItemDrawerImage itemDrawerImage = ItemDrawerConverter.INSTANCE.itemDrawerImageFormToItemDrawerImage(
                    image);
            itemDrawerImage.setType(ItemDrawerImageTypeEnum.DETAIL);
            itemDrawerImage.setDrawerId(drawerId);
            itemDrawerImage.setSort(sort);
            if (file != null) {
                itemDrawerImage.setFilename(file.getFullName());
                itemDrawerImage.setExt(file.getExt());
                itemDrawerImage.setWidth(file.getWidth());
                itemDrawerImage.setHeight(file.getHeight());
                itemDrawerImage.setProportion(file.getProportion());
            } else {
                itemDrawerImage.setFilename(StringUtils.getFilename(image.getUrl()));
                itemDrawerImage.setExt(StringUtils.getFilenameExtension(image.getUrl()));
            }
            IItemDrawerImageService.getBaseMapper().insert(itemDrawerImage);
            sort++;
        }
    }

    /**
     * 存储图片
     *
     * @param drawerId
     * @param imageList
     */
    private void saveMainImageVideo(Long drawerId, List<ItemDrawerImageForm> imageList) {
        //删除旧的图片
        IItemDrawerImageService.getBaseMapper().delete(new LambdaQueryWrapper<ItemDrawerImage>() {{
            eq(ItemDrawerImage::getDrawerId, drawerId);
            eq(ItemDrawerImage::getType, ItemDrawerImageTypeEnum.MAIN_IMAGE_VIDEO);
        }});
        final List<String> imageUrls = imageList.stream().map(ItemDrawerImageForm::getUrl).collect(Collectors.toList());
        final Map<String, File> fileMap = fileGateway.fileQueryBatchByUrls(imageUrls);
        long sort = 1;
        for (ItemDrawerImageForm image : imageList) {
            final File file = fileMap.get(image.getUrl());
            ItemDrawerImage itemDrawerImage = ItemDrawerConverter.INSTANCE.itemDrawerImageFormToItemDrawerImage(
                    image);
            itemDrawerImage.setType(ItemDrawerImageTypeEnum.MAIN_IMAGE_VIDEO);
            itemDrawerImage.setFileType(ItemDrawerImageFileTypeEnum.VIDEO.getValue());
            itemDrawerImage.setDrawerId(drawerId);
            itemDrawerImage.setFirstImage(image.getFirstImageUrl());
            if (file != null) {
                itemDrawerImage.setFilename(file.getFullName());
                itemDrawerImage.setExt(file.getExt());
                itemDrawerImage.setWidth(file.getWidth());
                itemDrawerImage.setHeight(file.getHeight());
                itemDrawerImage.setProportion(file.getProportion());
            } else {
                itemDrawerImage.setFilename(StringUtils.getFilename(image.getUrl()));
                itemDrawerImage.setExt(StringUtils.getFilenameExtension(image.getUrl()));
            }
            itemDrawerImage.setSort(sort);
            IItemDrawerImageService.getBaseMapper().insert(itemDrawerImage);
            sort++;
        }
    }

    /**
     * 保存商品规格图片
     *
     * @param drawerId
     * @param imageList
     */
    private void saveSkuImage(Long drawerId, List<ItemDrawerSkuImage> imageList) {
        final List<String> imageUrls = imageList.stream().map(ItemDrawerSkuImage::getUrl).collect(
                Collectors.toList());
        final Map<String, File> fileMap = fileGateway.fileQueryBatchByUrls(imageUrls);
        final List<ItemDrawerImage> itemDrawerImages = getDrawerImagesCache(drawerId,
                ItemDrawerImageTypeEnum.SKU);
        if (CollectionUtil.isNotEmpty(itemDrawerImages)) {
            final Map<Long, ItemDrawerImage> skuImageMap = itemDrawerImages.stream().collect(
                    Collectors.toMap(ItemDrawerImage::getSkuId, Function.identity(),
                            (a, b) -> a));
            final List<ItemDrawerImage> needAdded = new ArrayList<>();
            final List<ItemDrawerImage> needUpdated = new ArrayList<>();
            for (ItemDrawerSkuImage itemDrawerSkuImage : imageList) {
                final File file = fileMap.get(itemDrawerSkuImage.getUrl());
                if (!skuImageMap.containsKey(itemDrawerSkuImage.getSkuId())) {
                    needAdded.add(buildItemDrawerImagePO(drawerId, itemDrawerSkuImage, file));
                } else {
                    final ItemDrawerImage itemDrawerImage = skuImageMap.get(
                            itemDrawerSkuImage.getSkuId());
                    if (!Objects.equals(itemDrawerSkuImage.getUrl(), itemDrawerImage.getUrl())) {
                        itemDrawerImage.setUrl(itemDrawerSkuImage.getUrl());
                        if (file != null) {
                            itemDrawerImage.setFilename(file.getFullName());
                            itemDrawerImage.setExt(file.getExt());
                            itemDrawerImage.setWidth(file.getWidth());
                            itemDrawerImage.setHeight(file.getHeight());
                            itemDrawerImage.setProportion(file.getProportion());
                        } else {
                            itemDrawerImage.setFilename(StringUtils.getFilename(itemDrawerSkuImage.getUrl()));
                            itemDrawerImage.setExt(StringUtils.getFilenameExtension(itemDrawerSkuImage.getUrl()));
                        }
                        needUpdated.add(itemDrawerImage);
                    }
                }
            }
            if (!needAdded.isEmpty()) {
                IItemDrawerImageService.saveBatch(needAdded);
            }
            if (!needUpdated.isEmpty()) {
                IItemDrawerImageService.updateBatchById(needUpdated);
            }
            final List<Long> needRemoved = imageList.stream()
                    .filter(v -> Objects.isNull(v.getUrl())).map(ItemDrawerSkuImage::getSkuId)
                    .map(skuImageMap::get).map(Entity::getId).collect(Collectors.toList());
            if (!needRemoved.isEmpty()) {
                IItemDrawerImageService.removeByIds(needRemoved);
            }
        } else {

            final ArrayList<ItemDrawerImage> newItemDrawerImages = new ArrayList<>();
            for (ItemDrawerSkuImage image : imageList) {
                if (StringUtil.isBlank(image.getUrl())) {
                    continue;
                }
                final File file = fileMap.get(image.getUrl());
                ItemDrawerImage itemDrawerImage = buildItemDrawerImagePO(
                        drawerId, image, file);
                newItemDrawerImages.add(itemDrawerImage);
            }
            IItemDrawerImageService.saveBatch(newItemDrawerImages);
        }
    }

    /**
     * 保存商品属性图片
     *
     * @param drawerId
     * @param imagesList
     */
    private void saveAttrImages(Long drawerId, List<ItemDrawerAttrImages> imagesList) {
        final List<String> imgUrls = imagesList.stream()
                .flatMap(v -> v.getItemAttrs().stream().map(ItemDrawerAttrImage::getUrl).filter(Objects::nonNull))
                .collect(Collectors.toList());
        final Map<String, File> fileMap = fileGateway.fileQueryBatchByUrls(imgUrls);
        final List<ItemDrawerImage> itemDrawerImages = getDrawerImagesCache(drawerId,
                ItemDrawerImageTypeEnum.ATTR);
        if (CollectionUtil.isNotEmpty(itemDrawerImages)) {
            final Map<Long, ItemDrawerImage> attrImageMap = itemDrawerImages.stream().collect(
                    Collectors.toMap(ItemDrawerImage::getItemAttrId, Function.identity(),
                            (a, b) -> a));
            final List<ItemDrawerImage> needAdded = new ArrayList<>();
            final List<ItemDrawerImage> needUpdated = new ArrayList<>();
            for (ItemDrawerAttrImages itemDrawerAttrImages : imagesList) {
                for (ItemDrawerAttrImage itemAttrImage : itemDrawerAttrImages.getItemAttrs()) {
                    if (StringUtil.isBlank(itemAttrImage.getUrl())) {
                        continue;
                    }
                    final File file = fileMap.get(itemAttrImage.getUrl());
                    if (!attrImageMap.containsKey(itemAttrImage.getId())) {
                        needAdded.add(buildItemDrawerImagePO(drawerId, itemAttrImage, file));
                    } else {
                        final ItemDrawerImage itemDrawerImage = attrImageMap.get(
                                itemAttrImage.getId());
                        if (!Objects.equals(itemAttrImage.getUrl(), itemDrawerImage.getUrl())) {
                            itemDrawerImage.setUrl(itemAttrImage.getUrl());
                            if (file != null) {
                                itemDrawerImage.setFilename(file.getFullName());
                                itemDrawerImage.setExt(file.getExt());
                                itemDrawerImage.setWidth(file.getWidth());
                                itemDrawerImage.setHeight(file.getHeight());
                                itemDrawerImage.setProportion(file.getProportion());
                            } else {
                                itemDrawerImage.setFilename(StringUtils.getFilename(itemAttrImage.getUrl()));
                                itemDrawerImage.setExt(StringUtils.getFilenameExtension(itemAttrImage.getUrl()));
                            }
                            needUpdated.add(itemDrawerImage);
                        }
                    }
                }
            }
            if (!needAdded.isEmpty()) {
                IItemDrawerImageService.saveBatch(needAdded);
            }
            if (!needUpdated.isEmpty()) {
                IItemDrawerImageService.updateBatchById(needUpdated);
            }
            final List<Long> needRemoved = imagesList.stream()
                    .flatMap(v -> v.getItemAttrs().stream())
                    .filter(v -> StringUtil.isBlank(v.getUrl()))
                    .map(ItemDrawerAttrImage::getId)
                    .map(attrImageMap::get).filter(Objects::nonNull).map(Entity::getId)
                    .collect(Collectors.toList());
            if (!needRemoved.isEmpty()) {
                IItemDrawerImageService.removeByIds(needRemoved);
            }
        } else {

            final ArrayList<ItemDrawerImage> newItemDrawerImages = new ArrayList<>();
            for (ItemDrawerAttrImages itemDrawerAttrImages : imagesList) {
                for (ItemDrawerAttrImage image : itemDrawerAttrImages.getItemAttrs()) {
                    if (StringUtil.isBlank(image.getUrl())) {
                        continue;
                    }
                    final File file = fileMap.get(image.getUrl());
                    ItemDrawerImage itemDrawerImage = buildItemDrawerImagePO(
                            drawerId, image, file);
                    newItemDrawerImages.add(itemDrawerImage);
                }
            }
            IItemDrawerImageService.saveBatch(newItemDrawerImages);
        }
    }

    private void invalidateItemDrawerImageCache(Long drawerId) {
        itemDrawerImageLoadingCache.invalidate(drawerId);
    }

    @NonNull
    private ItemDrawerImage buildItemDrawerImagePO(Long drawerId, ItemDrawerSkuImage image,
                                                   File file) {
        ItemDrawerImage itemDrawerImage = new ItemDrawerImage();
        itemDrawerImage.setType(ItemDrawerImageTypeEnum.SKU);
        itemDrawerImage.setDrawerId(drawerId);
        itemDrawerImage.setSkuId(image.getSkuId());
        itemDrawerImage.setUrl(image.getUrl());
        itemDrawerImage.setSort(0L);
        if (file != null) {
            itemDrawerImage.setFilename(file.getFullName());
            itemDrawerImage.setExt(file.getExt());
            itemDrawerImage.setWidth(file.getWidth());
            itemDrawerImage.setHeight(file.getHeight());
            itemDrawerImage.setProportion(file.getProportion());
        } else {
            itemDrawerImage.setFilename(StringUtils.getFilename(image.getUrl()));
            itemDrawerImage.setExt(StringUtils.getFilenameExtension(image.getUrl()));
        }
        return itemDrawerImage;
    }

    @NonNull
    private ItemDrawerImage buildItemDrawerImagePO(Long drawerId, ItemDrawerAttrImage image, File file) {
        ItemDrawerImage itemDrawerImage = new ItemDrawerImage();
        itemDrawerImage.setType(ItemDrawerImageTypeEnum.ATTR);
        itemDrawerImage.setDrawerId(drawerId);
        itemDrawerImage.setItemAttrId(image.getId());
        itemDrawerImage.setUrl(image.getUrl());
        itemDrawerImage.setSort(0L);
        if (file != null) {
            itemDrawerImage.setFilename(file.getFullName());
            itemDrawerImage.setExt(file.getExt());
            itemDrawerImage.setWidth(file.getWidth());
            itemDrawerImage.setHeight(file.getHeight());
            itemDrawerImage.setProportion(file.getProportion());
        } else {
            itemDrawerImage.setFilename(StringUtils.getFilename(image.getUrl()));
            itemDrawerImage.setExt(StringUtils.getFilenameExtension(image.getUrl()));
        }
        return itemDrawerImage;
    }


    @Transactional
    @Override
    public SingleResponse<Boolean> edit(ItemDrawerForm itemDrawerForm, Long userId,
                                        String loginName) {
        return SingleResponse.of(save(itemDrawerForm, userId, false).getLeft());
    }

    @Override
    public SingleResponse<ItemDrawerVO> detail(Long itemId, Long userId, String loginName) {
        ItemDrawerMerge itemDrawerMerge = itemDrawerMergeBizService.getItemDrawerMerge(itemId);
        Long newItemId = itemId;
        if (Objects.nonNull(itemDrawerMerge)) {
            newItemId = itemDrawerMerge.getItemId();
        }
        ItemDrawer itemDrawer = getItemDrawer(newItemId);
        ItemDrawerVO itemDrawerVO = getItemDrawerVO(itemDrawer);
        itemDrawerVO.setRawItemId(itemId);
        final Item rawItem = itemService.getById(itemId);
        if (rawItem != null) {
            itemDrawerVO.setItemName(rawItem.getName());
            final ItemLaunchPlan itemLaunchPlan = itemLaunchPlanService.getPlanByItemId(itemId);
            if (itemLaunchPlan != null) {
                itemDrawerVO.setPlanId(itemLaunchPlan.getId());
                itemDrawerVO.setPlanName(itemLaunchPlan.getPlanName());
                itemDrawerVO.setLaunchDate(
                        DateUtil.formatDate(new Date(itemLaunchPlan.getLaunchTime() * 1000)));
                itemDrawerVO.setLaunchBusinessLine(Integer.valueOf(itemLaunchPlan.getBusinessLine()));
            }
        }
        final List<CorpBizTypeDTO> corpBizType = bizLevelDivisionService.getCorpBizType(BizUnionTypeEnum.SPU, itemId);
        itemDrawerVO.setCorpBizType(corpBizType);
        return SingleResponse.of(itemDrawerVO);
    }

    /**
     * 获取ItemDrawerVO
     *
     * @param itemDrawer
     * @return
     */
    @Override
    public ItemDrawerVO getItemDrawerVO(ItemDrawer itemDrawer) {
        Item item = itemService.getById(itemDrawer.getItemId());
        if (Objects.isNull(item)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "商品不存在");
        }

        List<ItemDrawerLiveVerbal> itemDrawerLiveVerbals = iItemDrawerLiveVerbalService.getByItemId(itemDrawer.getItemId());
        boolean isNewLiveVerbalTrick = CollUtil.isNotEmpty(itemDrawerLiveVerbals);

        //刷新抽屉图片本地缓存
        refreshDrawerImageCache(itemDrawer.getId());
        ItemDrawerVO itemDrawerVO = ItemDrawerConverter.INSTANCE.itemDrawerToVO(itemDrawer);
        itemDrawerVO.setItemName(item.getName());
        Triple<List<ItemDrawerImageVO>, List<ItemDrawerImageVO>, List<ItemDrawerImageVO>> drawerImages = getItemDrawerImages(
                itemDrawer.getId());
        final List<ItemDrawerImageVO> mainImages = drawerImages.getLeft();
        itemDrawerVO.setImages(mainImages);
        itemDrawerVO.setOtherImages(drawerImages.getMiddle());
        itemDrawerVO.setDetailImages(drawerImages.getRight());
        itemDrawerVO.setMainImageVideo(getDrawerImagesCache(itemDrawer.getId(),
                ItemDrawerImageTypeEnum.MAIN_IMAGE_VIDEO).stream()
                .map(ItemDrawerConverter.INSTANCE::itemDrawerImageToVO).collect(
                        Collectors.toList()));
        itemDrawerVO.setSkuImages(
                getItemDrawerSkuImages(itemDrawer.getId(), itemDrawer.getItemId()));
        itemDrawerVO.setAttrImages(
                getItemDrawerAttrImages(itemDrawer.getId(), itemDrawer.getItemId()));
        itemDrawerVO.setPartnerProviderItemSn(item.getPartnerProviderItemSn());
        itemDrawerVO.setItemLaunchStatus(item.getLaunchStatus());
        itemDrawerVO.setProcessIframeUrl(awsApprovalFlowService.getUrl(itemDrawer.getId(),
                PurchaseTypeEnum.ITEM_LIBRARY_AUDIT));
        itemDrawerVO.setDownFrameReason(item.getDownFrameReason());
        itemDrawerVO.setDownFrameTime(item.getDownFrameTime());
        itemDrawerVO.setCanRecoverBeforeStatus(isCanRecoverBeforeStatus(item));
        List<NewGoodsVo> newGoodsVO = newGoodsBizService.getNewGoodsVO(itemDrawer.getItemId());
        if (!newGoodsVO.isEmpty()) {
            NewGoodsVo newGoodsVo = newGoodsVO.remove(0);
            itemDrawerVO.setBuyerId(newGoodsVo.getBuyerId());
            itemDrawerVO.setBuyer(newGoodsVo.getBuyer());
            itemDrawerVO.setPrincipalId(newGoodsVo.getPrincipalId());
            itemDrawerVO.setPrincipal(newGoodsVo.getPrincipal());
            itemDrawerVO.setQcIds(newGoodsVo.getQcIds());
            itemDrawerVO.setQcs(newGoodsVo.getQcs());
        }
        //查询预计上架信息
        ItemLaunchPlan itemLaunchPlan = itemLaunchPlanService.getPlanByItemId(
                itemDrawer.getItemId());
        if (Objects.nonNull(itemLaunchPlan)) {
            itemDrawerVO.setPlanId(itemLaunchPlan.getId());
            itemDrawerVO.setPlanName(itemLaunchPlan.getPlanName());
            itemDrawerVO.setLaunchDate(
                    DateUtil.formatDate(new Date(itemLaunchPlan.getLaunchTime() * 1000)));
            itemDrawerVO.setLaunchBusinessLine(Integer.valueOf(itemLaunchPlan.getBusinessLine()));
        }

        //淘宝链接、抖音链接 小程序链接
        itemDrawerVO.setTbLink(itemDrawer.getTbLink());
        itemDrawerVO.setDouLink(itemDrawer.getDouLink());
        itemDrawerVO.setWechatLink(itemDrawer.getWechatLink());
        //查询上新进度
        List<ItemLaunchProgressNode> processNodes = getLaunchProgressNodes(item, itemDrawerVO, itemDrawer);
        itemDrawerVO.setLaunchProcessNodes(processNodes);
        //待审核状态是否可撤回
        itemDrawerVO.setCanRollback(canRollback(itemDrawerVO));
        //商品类型
        itemDrawerVO.setType(itemDrawer.getType());
        //模块审批意见
        setItemModuleInfo(item.getId(), itemDrawerVO);

        //  ------------------------ 直播话术处理 ------------------------
        List<LiveVerBalTrickVO> LiveVerBalTrickVoList = new LinkedList<>();
        if (StrUtil.isNotEmpty(itemDrawer.getLiveVerbalTrick()) && Objects.nonNull(itemDrawer.getLiveVerbalTrickStatus())) {
            // 直播话术 审批模块
            LiveVerbalTrickModuleInfo liveVerbalTrickModule = getLiveVerbalTrickModule(itemDrawer, null);
            //直播话术审核进度
            List<ItemLaunchProgressNode> progressNodesForLiveVerbalTrick = getProgressNodesForLiveVerbalTrick(item, null, itemDrawerVO);
            //当前直播话术审核状态是否可撤回
            Boolean aBoolean = canRollbackOfLiveVerbalTrick(liveVerbalTrickModule, progressNodesForLiveVerbalTrick);
            // 直播话术
            String liveVerbalTrick = itemDrawer.getLiveVerbalTrick();
            // 直播图片
            List<ItemDrawerImageVO> drawerImageVOS = getDrawerImagesCache(itemDrawer.getId(), ItemDrawerImageTypeEnum.LIVE_IMAGE)
                    .stream()
                    .map(ItemDrawerConverter.INSTANCE::itemDrawerImageToVO)
                    .collect(Collectors.toList());
            LiveVerBalTrickVO vo = new LiveVerBalTrickVO();
            vo.setLiveVerbalTrick(liveVerbalTrick);
            vo.setLiveImages(drawerImageVOS);
            vo.setId(0L);
            vo.setName("话术1");
            vo.setCreateUid(0L);
            vo.setPrincipalId(0L);
            vo.setLiveVerbalTrickModuleInfo(liveVerbalTrickModule);
            vo.setCanRollbackOfLiveVerbalTrick(aBoolean);
            vo.setProgressNodesForLiveVerbalTrick(progressNodesForLiveVerbalTrick);
            LiveVerBalTrickVoList.add(vo);
        }
        if (isNewLiveVerbalTrick) {
            for (ItemDrawerLiveVerbal itemDrawerLiveVerbal : itemDrawerLiveVerbals) {
                Long id = itemDrawerLiveVerbal.getId();
                LiveVerbalTrickModuleInfo liveVerbalTrickModule = getLiveVerbalTrickModule(itemDrawer, id);
                List<ItemLaunchProgressNode> progressNodesForLiveVerbalTrick = getProgressNodesForLiveVerbalTrick(item, id, itemDrawerVO);
                Boolean aBoolean = canRollbackOfLiveVerbalTrick(liveVerbalTrickModule, progressNodesForLiveVerbalTrick);
                String liveVerbalTrick = itemDrawerLiveVerbal.getLiveVerbalTrick();
                List<ItemDrawerImageVO> drawerImageVOS = getDrawerLiveImagesCache(itemDrawer.getId(), id).stream()
                        .map(ItemDrawerConverter.INSTANCE::itemDrawerImageToVO)
                        .collect(Collectors.toList());

                LiveVerBalTrickVO vo = new LiveVerBalTrickVO();
                vo.setLiveVerbalTrick(liveVerbalTrick);
                vo.setLiveImages(drawerImageVOS);
                vo.setId(id);
                vo.setName(
                        iItemDrawerLiveVerbalService.getName(
                                StringUtil.isNotEmpty(itemDrawer.getLiveVerbalTrick()),
                                itemDrawerLiveVerbal,
                                itemDrawerLiveVerbals));
                vo.setCreateUid(itemDrawerLiveVerbal.getCreatedUid());
                vo.setPrincipalId(itemDrawerLiveVerbal.getPrincipalId());
                vo.setPrincipal(
                        Optional.ofNullable(itemDrawerLiveVerbal.getPrincipalId()).filter(NumberUtil::isPositive)
                                .map(StaffAssembler.INST::toStaffBrief)
                                .orElse(null)
                );
                vo.setLiveVerbalTrickModuleInfo(liveVerbalTrickModule);
                vo.setCanRollbackOfLiveVerbalTrick(aBoolean);
                vo.setProgressNodesForLiveVerbalTrick(progressNodesForLiveVerbalTrick);
                LiveVerBalTrickVoList.add(vo);
            }
        }
        itemDrawerVO.setLiveVerBalTrickVos(LiveVerBalTrickVoList);
        //  ------------------------ 直播话术处理结束 ------------------------

        itemDrawerVO.setLaunchSubmitStatus(
                Objects.equals(TO_BE_UPDATED.getValue(), item.getLaunchStatus()) ? itemDrawer.getLaunchSubmitStatus()
                        : 0);
        // 协同用户信息获取
        itemDrawerVO.setCoopUser(
                Optional.ofNullable(itemDrawer.getCoopUid())
                        .filter(v -> v > 0)
                        .map(staffService::getStaff)
                        .orElseGet(
                                () ->
                                        io.vavr.collection.List.ofAll(
                                                        itemDrawerVO.getLaunchProcessNodes())
                                                .filter(v -> Objects.equals(v.getTitle(), TO_BE_UPDATED.getDesc()))
                                                .map(ItemLaunchProgressNode::getProcessors)
                                                .headOption()
                                                .map(CollUtil::getLast)
                                                .get()));

        itemDrawerVO.setShowExtendInfo(isShowExtendInfo(item.getId()));

        // 合并上新商品
        ItemDrawerMerge itemDrawerMerge = itemDrawerMergeBizService.getItemDrawerMerge(itemDrawer.getItemId());
        if (Objects.nonNull(itemDrawerMerge)) {
            List<ItemDrawerMergeItem> itemDrawerMergeItems = itemDrawerMergeBizService.getItemDrawerMergeItemListByMergeId(itemDrawerMerge.getId());
            if (!itemDrawerMergeItems.isEmpty()) {
                List<Long> mergeItemIds = itemDrawerMergeItems.stream().map(ItemDrawerMergeItem::getItemId).collect(Collectors.toList());
                Map<Long, String> itemMainImageMap = itemImageService.getItemMainImageToMap(mergeItemIds);
                List<SimpleItemVo> simpleItemVos = itemService.selectBatchByIds(mergeItemIds).stream().map(itemModel -> {
                    SimpleItemVo simpleItemVo = new SimpleItemVo();
                    simpleItemVo.setItemId(itemModel.getId());
                    simpleItemVo.setName(itemModel.getName());
                    simpleItemVo.setImage(itemMainImageMap.getOrDefault(itemModel.getId(), ""));
                    simpleItemVo.setCode(itemModel.getCode());
                    simpleItemVo.setStatus(itemModel.getStatus());
                    simpleItemVo.setDownFrameTime(itemModel.getDownFrameTime());
                    simpleItemVo.setDownFrameReason(itemModel.getDownFrameReason());
                    return simpleItemVo;
                }).collect(Collectors.toList());
                itemDrawerVO.setMergeItemIds(mergeItemIds);
                itemDrawerVO.setMergeItemList(simpleItemVos);
            }

        }
        return itemDrawerVO;
    }

    private static boolean isCanRecoverBeforeStatus(Item item) {
      return Objects.equals(item.getLaunchStatus(), DOWN.getValue())
          && item.getBeforeLaunchStatus() != null
          && !Arrays.asList(
              DOWN.getValue(),
              NON_SELECTIVITY.getValue()
          ).contains(item.getBeforeLaunchStatus());
    }

    @Autowired
    private PsysCategoryConfig psysCategoryConfig;

    private boolean isShowExtendInfo(Long itemId) {
        try {
            final SingleResponse<PartnerItemResp> partnerItemRespSingleResponse = psysItemInfo(itemId);
            if (partnerItemRespSingleResponse.isSuccess()
                    && partnerItemRespSingleResponse.getData() != null) {
                return partnerItemRespSingleResponse.getData().isBeautyAndSkincareCategory(psysCategoryConfig);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * @param itemId       商品ID
     * @param itemDrawerVO 视图模型
     */
    private void setItemModuleInfo(Long itemId, ItemDrawerVO itemDrawerVO) {
        final Comparator<ItemDrawerModuleModifyAdviceVO> adviceTimeComparator = Comparator.comparing(ItemDrawerModuleModifyAdviceVO::getTime).reversed();
        final List<ItemDrawerModuleAudit> itemDrawerModuleAudits = itemDrawerModuleAuditService.lambdaQuery()
                .eq(ItemDrawerModuleAudit::getItemId, itemId).list();
        final Optional<ItemDrawerModuleAudit> itemDrawerModuleAuditOptional = itemDrawerModuleAudits.stream()
                .filter(v -> v.getType() == ItemAuditType.ITEM_MATERIAL).findAny();
        final Map<ItemDrawerModuleId, Map<Integer, List<ItemLaunchModuleAdvice>>> moduleAdvices = itemLaunchModuleAdviceBizService.getModuleAdvicesAllRound(
                itemId);
        final List<Long> userIds = moduleAdvices.values().stream()
                .flatMap(round -> round.values().stream().flatMap(
                        advices -> advices.stream().map(ItemLaunchModuleAdvice::getCreatedUid)))
                .distinct()
                .collect(Collectors.toList());
        final Map<Long, DadStaffVO> dadStaffVOMap =
                CollectionUtil.isEmpty(userIds) ? Collections.emptyMap()
                        : staffService.getStaffList(userIds).stream()
                        .collect(Collectors.toMap(DadStaffVO::getUserId,
                                Function.identity()));
        if (!itemDrawerModuleAuditOptional.isPresent()) {
            itemDrawerVO.setModules(
                    Arrays.stream(ItemDrawerModuleId.values()).map(moduleId -> {
                        final ItemDrawerModuleInfoVO itemDrawerModuleInfoVO = new ItemDrawerModuleInfoVO();
                        itemDrawerModuleInfoVO.setModuleId(moduleId);
                        itemDrawerModuleInfoVO.setAuditState(ItemDrawerModuleAuditState.NONE);
                        itemDrawerModuleInfoVO.setAdvices(Collections.emptyList());
                        return itemDrawerModuleInfoVO;
                    }).collect(Collectors.toList())
            );
            itemDrawerVO.setRecognitionResults(null);
        } else {
            final ItemDrawerModuleAudit itemDrawerModuleAudit = itemDrawerModuleAuditOptional.get();

            final ItemAuditStatus auditStatus = itemDrawerModuleAudit.getAuditStatus();
            final ItemDrawerModuleAuditState itemDrawerModuleAuditState =
                    auditStatus == ItemAuditStatus.NONE ? ItemDrawerModuleAuditState.NONE
                            : (auditStatus == ItemAuditStatus.FINISHED
                            ? ItemDrawerModuleAuditState.DONE
                            : ItemDrawerModuleAuditState.PENDING);
            final ArrayList<ItemDrawerModuleInfoVO> modules = Lists.newArrayList();
            final ImmutableList<ItemDrawerModuleId> excludeModuleIds = ImmutableList.of(
                    ItemDrawerModuleId.LIVE_VERBAL_TRICK);
            for (ItemDrawerModuleId moduleId : ItemDrawerModuleId.values()) {
                if (excludeModuleIds.contains(moduleId)) {
                    continue;
                }
                final ItemDrawerModuleInfoVO itemDrawerModuleInfoVO = new ItemDrawerModuleInfoVO();
                itemDrawerModuleInfoVO.setModuleId(moduleId);
                itemDrawerModuleInfoVO.setHisAdvices(Collections.emptyList());
                Boolean needAudit = false;
                try {
                    needAudit = (Boolean) ReflectUtil.getFieldValue(itemDrawerModuleAudit,
                            StringUtil.toCamelCase(moduleId.getValue().toLowerCase()));
                } catch (UtilException ignored) {
                }
                final ItemDrawerModuleAuditState auditState = needAudit ? itemDrawerModuleAuditState
                        : (itemDrawerModuleAudit.getRound() > 1 ? ItemDrawerModuleAuditState.DONE
                        : ItemDrawerModuleAuditState.NONE);
                itemDrawerModuleInfoVO.setAuditState(auditState);
                final Map<Integer, List<ItemLaunchModuleAdvice>> moduleAdvicesByRound = moduleAdvices.getOrDefault(
                        moduleId, Collections.emptyMap());
                List<ItemDrawerModuleModifyAdviceVO> returnAdviceVos = Collections.emptyList();
                List<ItemDrawerModuleModifyAdviceVO> hisAdviceVos = new ArrayList<>();
                final Integer round = itemDrawerModuleAudit.getRound();
                final int minRound = 0;
                for (int i = round; i >= minRound; i--) {
                    final List<ItemLaunchModuleAdvice> advicesCurrentRound = moduleAdvicesByRound.get(
                            i);
                    if (CollectionUtil.isEmpty(advicesCurrentRound)) {
                        continue;
                    }
                    final List<ItemDrawerModuleModifyAdviceVO> adviceVOS = advicesCurrentRound.stream()
                            .map(v -> makeItemDrawerModuleModifyAdviceVO(v, dadStaffVOMap)).collect(
                                    Collectors.toList());
                    if (i == round) {
                        returnAdviceVos = adviceVOS;
                    } else {
                        hisAdviceVos.addAll(adviceVOS);
                    }
                }

                returnAdviceVos.sort(adviceTimeComparator);
                itemDrawerModuleInfoVO.setAdvices(returnAdviceVos);

                hisAdviceVos.sort(adviceTimeComparator);
                itemDrawerModuleInfoVO.setHisAdvices(hisAdviceVos);
                modules.add(itemDrawerModuleInfoVO);
            }
            itemDrawerVO.setModules(modules);
            //识别结果
            itemDrawerVO.setRecognitionResults(getItemDrawerRecognitionResults(itemDrawerModuleAudit));
        }

//        final Optional<ItemDrawerModuleAudit> itemDrawerModuleAuditLvtOptional = itemDrawerModuleAudits.stream()
//                .filter(v -> v.getType() == ItemAuditType.LIVE_VERBAL_TRICK).findAny();
//        final LiveVerbalTrickModuleInfo liveVerbalTrickModuleInfo = new LiveVerbalTrickModuleInfo();
//        liveVerbalTrickModuleInfo.setAdvices(Collections.emptyList());
//        liveVerbalTrickModuleInfo.setHisAdvices(Collections.emptyList());
//        itemDrawerVO.setLiveVerbalTrickModuleInfo(liveVerbalTrickModuleInfo);
//        final ItemAuditStatus liveVerbalTrickStatus = itemDrawer.getLiveVerbalTrickStatus();
//        if (Objects.nonNull(liveVerbalTrickStatus)) {
//            liveVerbalTrickModuleInfo.setLiveVerbalTrickStatus(liveVerbalTrickStatus.getValue());
//        }
//        if (itemDrawerModuleAuditLvtOptional.isPresent()) {
//            final ItemDrawerModuleAudit itemDrawerModuleAudit = itemDrawerModuleAuditLvtOptional.get();
//            List<ItemDrawerModuleModifyAdviceVO> returnAdviceVos = Collections.emptyList();
//            List<ItemDrawerModuleModifyAdviceVO> hisAdviceVos = new ArrayList<>();
//            final Map<Integer, List<ItemLaunchModuleAdvice>> moduleAdvicesByRound = moduleAdvices.getOrDefault(
//                    ItemDrawerModuleId.LIVE_VERBAL_TRICK, Collections.emptyMap());
//            final Integer round = itemDrawerModuleAudit.getRound();
//            final int minRound = 0;
//            for (int i = round; i >= minRound; i--) {
//                final List<ItemLaunchModuleAdvice> advicesCurrentRound = moduleAdvicesByRound.get(
//                        i);
//                if (CollectionUtil.isEmpty(advicesCurrentRound)) {
//                    continue;
//                }
//                final List<ItemDrawerModuleModifyAdviceVO> adviceVOS = advicesCurrentRound.stream()
//                        .map(v -> makeItemDrawerModuleModifyAdviceVO(v, dadStaffVOMap)).collect(
//                                Collectors.toList());
//                if (returnAdviceVos.isEmpty()) {
//                    returnAdviceVos = adviceVOS;
//                } else {
//                    hisAdviceVos.addAll(adviceVOS);
//                }
//            }
//
//            returnAdviceVos.sort(adviceTimeComparator);
//            liveVerbalTrickModuleInfo.setAdvices(returnAdviceVos);
//
//            hisAdviceVos.sort(adviceTimeComparator);
//            liveVerbalTrickModuleInfo.setHisAdvices(hisAdviceVos);
//
//            liveVerbalTrickModuleInfo.setRecognitionResults(getItemDrawerRecognitionResults(itemDrawerModuleAudit));
//        }

    }

    private LiveVerbalTrickModuleInfo getLiveVerbalTrickModule(ItemDrawer itemDrawer, Long liveVerbalTrickId) {
        boolean isNewVersion = Objects.nonNull(liveVerbalTrickId) && liveVerbalTrickId != 0;
        ItemDrawerLiveVerbal itemDrawerLiveVerbal = null;
        if (isNewVersion) {
            itemDrawerLiveVerbal = iItemDrawerLiveVerbalService.getById(liveVerbalTrickId);
            Assert.notNull(itemDrawerLiveVerbal, "直播话术id非法");
        }

        final Comparator<ItemDrawerModuleModifyAdviceVO> adviceTimeComparator = Comparator.comparing(ItemDrawerModuleModifyAdviceVO::getTime).reversed();

        final List<ItemDrawerModuleAudit> itemDrawerModuleAudits = itemDrawerModuleAuditService.lambdaQuery()
                .eq(ItemDrawerModuleAudit::getItemId, itemDrawer.getItemId())
                .eq(Objects.nonNull(liveVerbalTrickId) && liveVerbalTrickId != 0, ItemDrawerModuleAudit::getLiveVerbalTrickId, liveVerbalTrickId)
                .list();
        final Optional<ItemDrawerModuleAudit> itemDrawerModuleAuditLvtOptional = itemDrawerModuleAudits.stream()
                .filter(v -> {
                    boolean b1 = v.getType() == ItemAuditType.LIVE_VERBAL_TRICK;
                    if (isNewVersion) {
                        boolean b2 = v.getLiveVerbalTrickId().equals(liveVerbalTrickId);
                        return b1 && b2;
                    } else {
                        return b1;
                    }
                }).findAny();

        final LiveVerbalTrickModuleInfo liveVerbalTrickModuleInfo = new LiveVerbalTrickModuleInfo();
        liveVerbalTrickModuleInfo.setAdvices(Collections.emptyList());
        liveVerbalTrickModuleInfo.setHisAdvices(Collections.emptyList());

        ItemAuditStatus liveVerbalTrickStatus;
        if (isNewVersion) {
            liveVerbalTrickStatus = itemDrawerLiveVerbal.getLiveVerbalTrickStatus();
        } else {
            liveVerbalTrickStatus = itemDrawer.getLiveVerbalTrickStatus();
        }
        if (Objects.nonNull(liveVerbalTrickStatus)) {
            liveVerbalTrickModuleInfo.setLiveVerbalTrickStatus(liveVerbalTrickStatus.getValue());
        }

        if (itemDrawerModuleAuditLvtOptional.isPresent()) {
            final ItemDrawerModuleAudit itemDrawerModuleAudit = itemDrawerModuleAuditLvtOptional.get();
            List<ItemDrawerModuleModifyAdviceVO> returnAdviceVos = Collections.emptyList();
            List<ItemDrawerModuleModifyAdviceVO> hisAdviceVos = new ArrayList<>();

            Map<Integer, List<ItemLaunchModuleAdvice>> moduleAdvicesByRound;
            if (isNewVersion) {
                moduleAdvicesByRound =
                        itemLaunchModuleAdviceBizService.getModuleAdvicesAllRoundByLiveVerbalTrickId(itemDrawer.getItemId(), liveVerbalTrickId);
            } else {
                Map<ItemDrawerModuleId, Map<Integer, List<ItemLaunchModuleAdvice>>> moduleAdvices = itemLaunchModuleAdviceBizService.getModuleAdvicesAllRound(
                        itemDrawer.getItemId());
                moduleAdvicesByRound = moduleAdvices.getOrDefault(
                        ItemDrawerModuleId.LIVE_VERBAL_TRICK, Collections.emptyMap());
            }
            List<Long> userIds = moduleAdvicesByRound.values().stream().flatMap(List::stream)
                    .map(ItemLaunchModuleAdvice::getCreatedUid).distinct().collect(Collectors.toList());
            Map<Long, DadStaffVO> dadStaffVOMap = CollUtil.isEmpty(moduleAdvicesByRound)
                    ? new HashMap<>()
                    : staffService.getStaffList(userIds).stream().collect(Collectors.toMap(DadStaffVO::getUserId, Function.identity()));

            final Integer round = itemDrawerModuleAudit.getRound();
            final int minRound = 0;
            for (int i = round; i >= minRound; i--) {
                final List<ItemLaunchModuleAdvice> advicesCurrentRound = moduleAdvicesByRound.get(
                        i);
                if (CollectionUtil.isEmpty(advicesCurrentRound)) {
                    continue;
                }
                final List<ItemDrawerModuleModifyAdviceVO> adviceVOS = advicesCurrentRound.stream()
                        .map(v -> makeItemDrawerModuleModifyAdviceVO(v, dadStaffVOMap)).collect(
                                Collectors.toList());
                if (returnAdviceVos.isEmpty()) {
                    returnAdviceVos = adviceVOS;
                } else {
                    hisAdviceVos.addAll(adviceVOS);
                }
            }

            returnAdviceVos.sort(adviceTimeComparator);
            liveVerbalTrickModuleInfo.setAdvices(returnAdviceVos);

            hisAdviceVos.sort(adviceTimeComparator);
            liveVerbalTrickModuleInfo.setHisAdvices(hisAdviceVos);

            liveVerbalTrickModuleInfo.setRecognitionResults(getItemDrawerRecognitionResults(itemDrawerModuleAudit));
        }

        return liveVerbalTrickModuleInfo;
    }


    @NonNull
    private static Boolean canRollback(ItemDrawerVO itemDrawerVO) {
        final boolean isCanRollbackStatus =
                CAN_ROLLBACK_STATUS.contains(itemDrawerVO.getItemLaunchStatus());
        if (isCanRollbackStatus
                && UserContext.hasPermission(GlobalConstant.NEW_GOODS_ROLLBACK_PERM)) {
            final Long currentUserId = UserContext.getUserId();
            return isProcessorOfPrevNode(itemDrawerVO.getLaunchProcessNodes(), currentUserId);
        }
        return false;
    }

    @NonNull
    private static Boolean canRollbackOfLiveVerbalTrick(LiveVerbalTrickModuleInfo moduleInfo, List<ItemLaunchProgressNode> progressNodesForLiveVerbalTrick) {
        if (Objects.isNull(moduleInfo)) {
            return false;
        }
        final boolean isCanRollbackStatus =
                CAN_ROLLBACK_STATUS_LIVE_VERBAL_TRICK
                        .contains(
                                moduleInfo.getLiveVerbalTrickStatus());
        if (isCanRollbackStatus
                && UserContext.hasPermission(GlobalConstant.ROLLBACK_LIVE_VERBAL_TRICK_PERM)) {
            final Long currentUserId = UserContext.getUserId();
            return isProcessorOfPrevNode(progressNodesForLiveVerbalTrick, currentUserId);
        }
        return false;
    }

    @NonNull
    private static Boolean isProcessorOfPrevNode(List<ItemLaunchProgressNode> progressNodes, Long currentUserId) {
        for (int i = 0; i < progressNodes.size(); i++) {
            final ItemLaunchProgressNode node = progressNodes.get(i);
            if (!node.getOk()) {
                if (i > 0) {
                    final ItemLaunchProgressNode prev = progressNodes.get(i - 1);
                    return prev.isProcessor(currentUserId);
                }
            }
        }
        return false;
    }

    private ItemDrawerRecognitionResults getItemDrawerRecognitionResults(ItemDrawerModuleAudit itemDrawerModuleAudit) {
        final Long itemId = itemDrawerModuleAudit.getItemId();
        final ItemDrawerRecognitionResults itemDrawerRecognitionResults = new ItemDrawerRecognitionResults();
        itemDrawerRecognitionResults.setStatus(
                itemDrawerModuleAudit.getRecognitionStatus() ? ItemDrawerRecognitionStatus.FINISHED
                        : ItemDrawerRecognitionStatus.PREPARE);
        final List<ItemDrawerRecognitionTask> itemDrawerRecognitionTasks = itemDrawerRecognitionTaskService.lambdaQuery()
                .eq(ItemDrawerRecognitionTask::getItemId, itemId)
                .eq(ItemDrawerRecognitionTask::getType, itemDrawerModuleAudit.getType())
                .orderByDesc(ItemDrawerRecognitionTask::getRound)
                .orderByAsc(ItemDrawerRecognitionTask::getId)
                .select(
                        ItemDrawerRecognitionTask::getId,
                        ItemDrawerRecognitionTask::getStatus,
                        ItemDrawerRecognitionTask::getRound,
                        ItemDrawerRecognitionTask::getModule,
                        ItemDrawerRecognitionTask::getField,
                        ItemDrawerRecognitionTask::getHitResult).list();
        Map<Integer, Map<ItemDrawerModuleId, Set<ItemDrawerRecognitionHit>>> hitResults = Maps.newHashMap();
        Map<Integer, Map<ItemDrawerModuleId, Set<String>>> errors = Maps.newHashMap();
        for (ItemDrawerRecognitionTask task : itemDrawerRecognitionTasks) {
            final ItemDrawerModuleId itemDrawerModuleId = IEnum.getEnumByValue(ItemDrawerModuleId.class, task.getModule());
            if (itemDrawerModuleId == null) {
                continue;
            }
            final Map<ItemDrawerModuleId, Set<ItemDrawerRecognitionHit>> roundHitResult = hitResults.computeIfAbsent(
                    task.getRound(), k -> Maps.newHashMap());
            if (task.getStatus() == ItemDrawerRecognitionTaskStatus.FAIL) {
                final Map<ItemDrawerModuleId, Set<String>> roundErrors = errors.computeIfAbsent(
                        task.getRound(), k -> Maps.newHashMap());
                final Set<String> moduleErrors = roundErrors.computeIfAbsent(itemDrawerModuleId,
                        k -> Sets.newHashSet());
                final ItemDrawerChangeEnum field = ItemDrawerChangeEnum.of(task.getField());
                Optional.ofNullable(field).map(ItemDrawerChangeEnum::getDesc)
                        .ifPresent(moduleErrors::add);
                continue;
            }
            if (task.getStatus() != ItemDrawerRecognitionTaskStatus.FINISHED
                    || StringUtil.isEmpty(task.getHitResult())) {
                continue;
            }
            final List<ItemDrawerRecognitionHit> hitResult;
            try {
                hitResult = JsonUtil.parse(task.getHitResult(),
                        new TypeReference<List<ItemDrawerRecognitionHit>>() {
                        });
            } catch (Exception e) {
                log.error("敏感词识别结果格式异常 taskId={} itemId={}", task.getId(), task.getItemId());
                continue;
            }
            if (hitResult == null) {
                log.error("敏感词识别结果格式为空 taskId={} itemId={}", task.getId(), task.getItemId());
                continue;
            }
            final Set<ItemDrawerRecognitionHit> itemDrawerRecognitionHits = roundHitResult.computeIfAbsent(itemDrawerModuleId, k -> Sets.newLinkedHashSet());
            itemDrawerRecognitionHits.addAll(hitResult);
        }
        final Map<ItemDrawerModuleId, Set<ItemDrawerRecognitionHit>> composedHitResult = Maps.newHashMap();
        final Map<ItemDrawerModuleId, Set<String>> composedErrors = Maps.newHashMap();
        final Integer round = itemDrawerModuleAudit.getRound();
        FOR_MODULE:
        for (ItemDrawerModuleId moduleId : ItemDrawerModuleId.values()) {
            final String moduleModifyField = StringUtil.toCamelCase(moduleId.getValue().toLowerCase());
            final Boolean isModifyCurrentRound = (Boolean) ReflectUtil.getFieldValue(
                    itemDrawerModuleAudit, moduleModifyField);
            final int moduleRound = isModifyCurrentRound ? round : round - 1;
            if (!composedHitResult.containsKey(moduleId)) {
                for (int thisRound = moduleRound; thisRound > 0; thisRound--) {
                    final Optional<Set<ItemDrawerRecognitionHit>> thatRoundModuleHitResultOptional = Optional.ofNullable(
                                    hitResults.get(thisRound))
                            .map(roundHitResult -> roundHitResult.get(moduleId));
                    if (thatRoundModuleHitResultOptional.isPresent()) {
                        composedHitResult.put(moduleId, thatRoundModuleHitResultOptional.get());
                        continue FOR_MODULE;
                    }
                }
            }
            if (!composedErrors.containsKey(moduleId)) {
                for (int thisRound = moduleRound; thisRound > 0; thisRound--) {
                    final Optional<Set<String>> thatRoundModuleErrorsOptional = Optional.ofNullable(
                                    errors.get(thisRound))
                            .map(roundHitResult -> roundHitResult.get(moduleId));
                    if (thatRoundModuleErrorsOptional.isPresent()) {
                        composedErrors.put(moduleId, thatRoundModuleErrorsOptional.get());
                        continue FOR_MODULE;
                    }
                }
            }
        }
        itemDrawerRecognitionResults.setHitResults(composedHitResult);
        itemDrawerRecognitionResults.setErrors(composedErrors);
        itemDrawerRecognitionResults.setHasError(composedErrors.entrySet().stream().anyMatch(v -> !v.getValue()
                .isEmpty()));
        return itemDrawerRecognitionResults;
    }

    private ItemDrawerModuleModifyAdviceVO makeItemDrawerModuleModifyAdviceVO(ItemLaunchModuleAdvice v,
                                                                              Map<Long, DadStaffVO> dadStaffVOMap) {
        final ItemDrawerModuleModifyAdviceVO itemDrawerModuleModifyAdviceVO = new ItemDrawerModuleModifyAdviceVO();
        itemDrawerModuleModifyAdviceVO.setModule(v.getModule());
        itemDrawerModuleModifyAdviceVO.setNode(v.getNode());
        itemDrawerModuleModifyAdviceVO.setType(v.getType());
        itemDrawerModuleModifyAdviceVO.setAdvice(v.getAdvice());
        itemDrawerModuleModifyAdviceVO.setTime(v.getCreatedAt());
        itemDrawerModuleModifyAdviceVO.setStaff(dadStaffVOMap.get(v.getCreatedUid()));
        itemDrawerModuleModifyAdviceVO.setRound(v.getRound());
        itemDrawerModuleModifyAdviceVO.setPass(v.isPass());
        return itemDrawerModuleModifyAdviceVO;
    }

    /**
     * 直播话术审核流程进度
     */
    private List<ItemLaunchProgressNode> getProgressNodesForLiveVerbalTrick(Item item, Long liveVerbalTrickId, ItemDrawerVO itemDrawerVO) {
        final Long itemId = item.getId();
        final ArrayList<ItemLaunchProgressNode> itemLaunchProgressNodes = new ArrayList<>();

        final Optional<ItemDrawerLiveVerbal> itemDrawerLiveVerbal = Optional.ofNullable(liveVerbalTrickId)
                                                                            .filter(v -> v > 0)
                                                                            .map(iItemDrawerLiveVerbalService::getById);

        //从配置中取到各个节点的默认通知人
        List<String> userLoginNames = Stream.of(recipientConfig.getLegalList(),
                        recipientConfig.getTaobaoList(), recipientConfig.getPlatformList(), recipientConfig.getMayday())
                .flatMap(Collection::stream).collect(Collectors.toList());

        //查询通知人的员工信息
        final Map<String, DadStaffVO> staffVOMap = staffService.getStaffListByLoginName(
                        userLoginNames)
                .stream()
                .collect(Collectors.toMap(DadStaffVO::getLoginName, Function.identity(),
                        (a, b) -> a));

        //法务负责人
        final List<DadStaffVO> legalPrincipals = recipientConfig.getLegalList().stream()
                .map(staffVOMap::get).filter(Objects::nonNull).collect(
                        Collectors.toList());
        final List<DadStaffVO> defaultProcessorsForLegalNode = Collections.emptyList();

        ItemDrawerModuleAudit moduleAudit;
        if (Objects.nonNull(liveVerbalTrickId) && liveVerbalTrickId != 0) {
            moduleAudit = itemDrawerModuleAuditService.getItemDrawerModuleAudit(
                    ItemAuditType.LIVE_VERBAL_TRICK, liveVerbalTrickId, itemId).orElse(null);
        } else {
            moduleAudit = itemDrawerModuleAuditService.getItemDrawerModuleAudit(
                    ItemAuditType.LIVE_VERBAL_TRICK, itemId).orElse(null);
        }
        if (moduleAudit == null) {
            itemLaunchProgressNodes.add(new ItemLaunchProgressNode("待提交", Collections.emptyList(), false, 0L));
            itemLaunchProgressNodes.add(new ItemLaunchProgressNode("待法务审核", defaultProcessorsForLegalNode, false, 0L));
            itemLaunchProgressNodes.add(new ItemLaunchProgressNode("待QC审核", itemDrawerVO.getQcs(), false, 0L));
            itemLaunchProgressNodes.add(new ItemLaunchProgressNode("已完成", Collections.emptyList(), false, 0L));
            return itemLaunchProgressNodes;
        }

        final List<ItemDrawerModuleAuditTask> list = itemDrawerModuleAuditTaskService.lambdaQuery()
                .eq(ItemDrawerModuleAuditTask::getItemId, itemId)
                .eq(ItemDrawerModuleAuditTask::getType, ItemAuditType.LIVE_VERBAL_TRICK)
                .eq(ItemDrawerModuleAuditTask::getRound, moduleAudit.getRound())
                .eq(Objects.nonNull(liveVerbalTrickId) && liveVerbalTrickId != 0, ItemDrawerModuleAuditTask::getLiveVerbalTrickId, liveVerbalTrickId)
                .orderByAsc(ItemDrawerModuleAuditTask::getId)
                .list();

        final ArrayList<ItemDrawerModuleAuditTask> itemDrawerModuleAuditTasks = new ArrayList<>();
        boolean flagModifyTask = false;
        for (ItemDrawerModuleAuditTask itemDrawerModuleAuditTask : list) {
            if (flagModifyTask) {
                itemDrawerModuleAuditTasks.add(itemDrawerModuleAuditTask);
            } else if (Objects.equals(
                    itemDrawerModuleAuditTask.getNode(),
                    ItemLaunchProcessNodeId.MODIFY.getValue())
                    && itemDrawerModuleAuditTask.getAuditStatus()
                    == ItemDrawerAuditTaskState.DONE) {
                flagModifyTask = true;
            }
        }
        if (itemDrawerModuleAuditTasks.isEmpty()) {
            itemDrawerModuleAuditTasks.addAll(list);
        }
        final Optional<ItemDrawerModuleAuditTask> legalTask =
                findTaskForNode(itemDrawerModuleAuditTasks, ItemLaunchProcessNodeId.LEGAL);

        //优先取直播话术配置的负责人
        final Long processorId = itemDrawerLiveVerbal.map(ItemDrawerLiveVerbal::getPrincipalId)
                                                     .filter(NumberUtil::isPositive)
                                                     .orElse(moduleAudit.getSubmitUid());
        // 【待提交】节点
        itemLaunchProgressNodes.add(
                new ItemLaunchProgressNode(
                        "待提交",
                        Optional.ofNullable(processorId)
                                .filter(uid -> uid != 0)
                                .map(uid -> Collections.singletonList(staffService.getStaff(uid)))
                                .orElseGet(Collections::emptyList),
                        moduleAudit.getAuditStatus() != ItemAuditStatus.NONE,
                        moduleAudit.getSubmitAt()));

        // 【待法务审核】节点
        legalTask.ifPresent(
                task -> itemLaunchProgressNodes.add(
                        new ItemLaunchProgressNode(
                                "待法务审核",
                                task.getProcessorId() == 0
                                        ? defaultProcessorsForLegalNode
                                        : Collections.singletonList(staffService.getStaff(task.getProcessorId())),
                                task.getAuditStatus() == ItemDrawerAuditTaskState.DONE,
                                task.getAuditAt())));
        if (!legalTask.isPresent()) {
            itemLaunchProgressNodes.add(
                    new ItemLaunchProgressNode("待法务审核", defaultProcessorsForLegalNode, false, 0L));
        }

        //【待QC审核】节点
        final Optional<ItemDrawerModuleAuditTask> qcTask =
                findTaskForNode(itemDrawerModuleAuditTasks, ItemLaunchProcessNodeId.QC);
        qcTask.ifPresent(task -> {
            itemLaunchProgressNodes.add(
                    new ItemLaunchProgressNode(
                            "待QC审核",
                            Collections.singletonList(staffService.getStaff(task.getProcessorId())),
                            task.getAuditStatus() == ItemDrawerAuditTaskState.DONE,
                            task.getAuditAt()));
        });
        if (!qcTask.isPresent()) {
            itemLaunchProgressNodes.add(new ItemLaunchProgressNode("待QC审核", itemDrawerVO.getQcs(), false, 0L));
        }
        //【待修改】节点
        final Optional<ItemDrawerModuleAuditTask> modifyTask =
                findTaskForNode(itemDrawerModuleAuditTasks, ItemLaunchProcessNodeId.MODIFY);
        modifyTask.ifPresent(task -> {
            itemLaunchProgressNodes.add(
                    new ItemLaunchProgressNode(
                            "待修改",
                            Collections.singletonList(staffService.getStaff(task.getProcessorId())),
                            task.getAuditStatus() == ItemDrawerAuditTaskState.DONE,
                            task.getAuditAt()));
        });

        // 【已完成】节点（取决于最后一个任务节点的完成状态）
        final ItemLaunchProgressNode lastNode =
                itemLaunchProgressNodes.get(itemLaunchProgressNodes.size() - 1);
        itemLaunchProgressNodes.add(
                new ItemLaunchProgressNode(
                        "审核完成",
                        Collections.emptyList(),
                        lastNode.getOk(),
                        lastNode.getCompleteTime()));

        return itemLaunchProgressNodes;
    }

    @NonNull
    private static Optional<ItemDrawerModuleAuditTask> findTaskForNode(
            ArrayList<ItemDrawerModuleAuditTask> itemDrawerModuleAuditTasks,
            ItemLaunchProcessNodeId itemLaunchProcessNodeId) {
        return itemDrawerModuleAuditTasks.stream()
                .filter(v -> Objects.equals(v.getNode(), itemLaunchProcessNodeId.getValue()))
                .findFirst();
    }

    /**
     * 获取上新流程进度
     *
     * @param item         后端商品PO
     * @param itemDrawerVO 商品抽屉VO
     * @param itemDrawer   商品抽屉PO
     * @return 流程进度
     */
    private List<ItemLaunchProgressNode> getLaunchProgressNodes(Item item,
                                                                ItemDrawerVO itemDrawerVO, ItemDrawer itemDrawer) {
        final ItemLaunchStatus launchStatus = ItemLaunchStatus.of(item.getLaunchStatus());
        if (launchStatus == null) {
            throw new IllegalStateException(
                    "商品上新状态异常，非有效枚举值:" + item.getLaunchStatus() + "，商品ID:" + item.getId());
        }

        //获取商品上新流程统计记录
        final Optional<ItemLaunchStats> itemLaunchStatsOptional = itemLaunchStatsService.lambdaQuery()
                .eq(ItemLaunchStats::getItemId, item.getId()).oneOpt();
        Long actualLegalId = itemLaunchStatsOptional.map(
                        ItemLaunchStats::getToBeLegalAuditProcessorUid)
                .orElse(0L);
        Long actualQcId = itemLaunchStatsOptional.map(ItemLaunchStats::getToBeQcAuditProcessorUid)
                .orElse(0L);

        final Optional<ItemDrawerModuleAudit> itemDrawerModuleAudit = itemDrawerModuleAuditService.getItemDrawerModuleAudit(
                ItemAuditType.ITEM_MATERIAL, item.getId());
        final Integer auditRound = itemDrawerModuleAudit.map(ItemDrawerModuleAudit::getRound)
                .orElse(0);
        //模块审核统计记录
        final Optional<ItemDrawerModuleAuditStats> itemDrawerModuleAuditStatsOptional =
                !itemDrawerModuleAudit.isPresent() ? Optional.empty()
                        : itemDrawerModuleAuditStatsService.lambdaQuery()
                        .eq(ItemDrawerModuleAuditStats::getItemId, item.getId())
                        .eq(ItemDrawerModuleAuditStats::getRound, auditRound)
                        .orderByDesc(ItemDrawerModuleAuditStats::getId).last(
                                MPUtil.limit(1)).oneOpt();
        if (itemDrawerModuleAuditStatsOptional.isPresent()) {
            actualLegalId = itemDrawerModuleAuditStatsOptional.map(
                            ItemDrawerModuleAuditStats::getLegalAuditUid)
                    .orElse(0L);
            actualQcId = itemDrawerModuleAuditStatsOptional.map(ItemDrawerModuleAuditStats::getQcAuditUid)
                    .orElse(0L);
        }

        // 从配置中取到各个节点的默认通知人
        List<String> userLoginNames =
                Stream.of(
                                recipientConfig.getLegalList(),
                                recipientConfig.getTaobaoList(item.getBusinessLine()),
                                recipientConfig.getPlatformList(),
                                recipientConfig.getMayday(item.getBusinessLine()))
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());

        //查询通知人的员工信息
        final Map<String, DadStaffVO> staffVOMap = staffService.getStaffListByLoginName(
                        userLoginNames)
                .stream()
                .collect(Collectors.toMap(DadStaffVO::getLoginName, Function.identity(),
                        (a, b) -> a));

        // 淘系负责人
        final List<DadStaffVO> taobaoPrincipals =
                recipientConfig.getTaobaoList(item.getBusinessLine()).stream()
                        .map(staffVOMap::get)
                        .filter(Objects::nonNull)
                        .limit(1)
                        .collect(Collectors.toList());

        // 待编辑协同用户
        final List<DadStaffVO> maydayPrincipals =
                recipientConfig.getMayday(item.getBusinessLine()).stream()
                        .map(staffVOMap::get)
                        .filter(Objects::nonNull)
                        .filter(
                                v -> itemDrawer.getCoopUid() == null
                                        || itemDrawer.getCoopUid() == 0
                                        || Objects.equals(
                                        v.getUserId(), itemDrawer.getCoopUid()))
                        .limit(1)
                        .collect(Collectors.toList());

        // 待编辑协同用户
        final List<DadStaffVO> xiaogao =
                recipientConfig.getXiaogao(item.getBusinessLine()).stream()
                        .map(staffVOMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        //法务负责人
        final List<DadStaffVO> legalPrincipals = recipientConfig.getLegalList().stream()
                .map(staffVOMap::get).filter(Objects::nonNull).limit(1).collect(
                        Collectors.toList());

        List<ItemLaunchProgressNode> processNodes = new ArrayList<>();

        //待完善阶段通知人为【采购负责人、QC负责人】
        final ItemLaunchProgressNode toBeImprovedNode = new ItemLaunchProgressNode(
                TO_BE_IMPROVED.getDesc(),
                Stream.of(Collections.singletonList(itemDrawerVO.getBuyer()), itemDrawerVO.getQcs())
                        .flatMap(Collection::stream).filter(Objects::nonNull)
                        .limit(1)
                        .collect(Collectors.toList()),
                launchStatus.getValue() > TO_BE_IMPROVED.getValue(),
                itemLaunchStatsOptional.map(ItemLaunchStats::getToBeImproveEndTime).orElse(null));
        processNodes.add(toBeImprovedNode);

        //待审核阶段通知人为【产品负责人、淘系负责人】
        final ItemLaunchProgressNode toBeDesignedNode = new ItemLaunchProgressNode(
                TO_BE_DESIGNED.getDesc(),
                Stream.of(Collections.singletonList(itemDrawerVO.getPrincipal()), taobaoPrincipals)
                        .flatMap(Collection::stream).filter(Objects::nonNull)
                        .limit(2)
                        .collect(Collectors.toList()),
                launchStatus.getValue() > TO_BE_DESIGNED.getValue(),
                itemLaunchStatsOptional.map(ItemLaunchStats::getToBeDesignEndTime).orElse(null));
        processNodes.add(toBeDesignedNode);

        //法务审核是否完成
        boolean legalAuditIsOk = item.getAuditStatus() > ItemAuditStatus.WAIT_LEGAL_AUDIT.getValue();

        //法务审核完成时间
        Long legalAuditCompleteTime = itemLaunchStatsOptional.map(
                ItemLaunchStats::getToBeLegalAuditEndTime).orElse(null);

        if (itemDrawerModuleAuditStatsOptional.isPresent()) {
            legalAuditCompleteTime = itemDrawerModuleAuditStatsOptional.map(
                    ItemDrawerModuleAuditStats::getLegalAuditEndTime).orElse(0L);
        }

        //法务审核处理人
        List<DadStaffVO> legalAuditProcessors =
                NumberUtil.isPositive(actualLegalId) ? Collections.singletonList(
                        staffService.getStaff(actualLegalId)) : legalPrincipals;

        //QC审核是否完成
        boolean qcAuditIsOk = item.getAuditStatus() > ItemAuditStatus.WAIT_QC_AUDIT.getValue();

        //QC审核完成时间
        Long qcAuditCompleteTime = itemLaunchStatsOptional.map(
                ItemLaunchStats::getToBeQcAuditEndTime).orElse(null);

        if (itemDrawerModuleAuditStatsOptional.isPresent()) {
            qcAuditCompleteTime = itemDrawerModuleAuditStatsOptional.map(
                    ItemDrawerModuleAuditStats::getQcAuditEndTime).orElse(0L);
        }

        //QC审核处理人
        List<DadStaffVO> qcAuditProcessors =
                actualQcId > 0 ? Collections.singletonList(staffService.getStaff(actualQcId))
                        : itemDrawerVO.getQcs();

        //待法务审核
        final ItemLaunchProgressNode toBeAuditedLegalNode = new ItemLaunchProgressNode(
                "待法务审核", legalAuditProcessors.stream().limit(1).collect(Collectors.toList()),
                legalAuditIsOk, legalAuditCompleteTime);
        processNodes.add(toBeAuditedLegalNode);

        //待QC审核
        final ItemLaunchProgressNode toBeAuditedQcNode = new ItemLaunchProgressNode(
                "待QC审核", qcAuditProcessors.stream().limit(1).collect(Collectors.toList()),
                qcAuditIsOk, qcAuditCompleteTime);
        processNodes.add(toBeAuditedQcNode);

        //待修改阶段的处理人是 【产品负责人、淘系负责人、小高】
        final ItemLaunchProgressNode toBeUpdateNode = new ItemLaunchProgressNode(
                TO_BE_UPDATED.getDesc(),
                Stream.of(Collections.singletonList(itemDrawerVO.getPrincipal()), maydayPrincipals, xiaogao)
                        .flatMap(Collection::stream).filter(Objects::nonNull)
                        .collect(Collectors.toList()),
                Objects.equals(launchStatus.getValue(), TO_BE_RELEASED.getValue())
                        || Objects.equals(launchStatus.getValue(),
                        ItemLaunchStatus.HAS_BE_RELEASED.getValue()),
                itemLaunchStatsOptional.map(ItemLaunchStats::getToBeUpdateEndTime).orElse(null));
        processNodes.add(toBeUpdateNode);

        //待上架阶段未指定需要通知的人员
        final ItemLaunchProgressNode toBeReleaseQcNode = new ItemLaunchProgressNode(
                TO_BE_RELEASED.getDesc(), Collections.emptyList(),
                Objects.equals(launchStatus.getValue(),
                        ItemLaunchStatus.HAS_BE_RELEASED.getValue()),
                itemLaunchStatsOptional.map(ItemLaunchStats::getToBeReleaseEndTime).orElse(null));
        processNodes.add(toBeReleaseQcNode);

        //已上架
        final ItemLaunchProgressNode hasReleasedQcNode = new ItemLaunchProgressNode(
                ItemLaunchStatus.HAS_BE_RELEASED.getDesc(), Collections.emptyList(),
                Objects.equals(launchStatus.getValue(),
                        ItemLaunchStatus.HAS_BE_RELEASED.getValue()),
                itemLaunchStatsOptional.map(ItemLaunchStats::getLaunchTime).orElse(null));
        processNodes.add(hasReleasedQcNode);

        // 已下架
        boolean isDown = Objects.equals(launchStatus.getValue(), DOWN.getValue());
        final ItemLaunchProgressNode hasOffShelfNode =
            new ItemLaunchProgressNode(
                ItemLaunchStatus.DOWN.getDesc(),
                Collections.emptyList(),
                isDown,
                isDown ? item.getDownFrameTime() : 0L);
        processNodes.add(hasOffShelfNode);

        // 处理流程状态跳变
        io.vavr.collection.List.ofAll(processNodes)
            .findLast(ItemLaunchProgressNode::getOk)
            .toJavaOptional()
            .ifPresent(
                lastOkNode -> {
                  for (ItemLaunchProgressNode processNode : processNodes) {
                    if (processNode != lastOkNode) {
                      processNode.setOk(true);
                    } else {
                      break;
                    }
                  }
                });
        return processNodes;
    }

    @Override
    public ItemDrawerVO getItemDrawerVO(Long drawerId) {
        return getItemDrawerVO(itemDrawerService.getById(drawerId));
    }

    @Override
    public ItemDrawer getItemDrawer(Long itemId) {
        ItemDrawer itemDrawer = itemDrawerService.getOne(new LambdaQueryWrapper<ItemDrawer>() {{
            eq(ItemDrawer::getItemId, itemId).last("LIMIT 1");
        }});
        if (Objects.isNull(itemDrawer)) {
            itemDrawer = createDrawer(itemId, LaunchItemType.INNER);
        }
        return itemDrawer;
    }

    @Override
    public ItemDrawer getMergeMainItemDrawer(Long itemId) {
        ItemDrawerMerge itemDrawerMerge = itemDrawerMergeBizService.getItemDrawerMerge(itemId);
        Long newItemId = itemId;
        if (Objects.nonNull(itemDrawerMerge)) {
            newItemId = itemDrawerMerge.getItemId();
        }
        ItemDrawer itemDrawer = itemDrawerService.lambdaQuery()
                .eq(ItemDrawer::getItemId, newItemId)
                .last("LIMIT 1")
                .one();
        if (Objects.isNull(itemDrawer)) {
            itemDrawer = createDrawer(itemId, LaunchItemType.INNER);
        }
        return itemDrawer;
    }

    @NonNull
    private ItemDrawer createDrawer(Long itemId, LaunchItemType type) {
        ItemDrawer itemDrawer;
        itemDrawer = new ItemDrawer();
        itemDrawer.setStandardName("");
        itemDrawer.setTbTitle("");
        itemDrawer.setMiniTitle("");
        itemDrawer.setHomeCopy("");
        itemDrawer.setShelfTime(0L);
        itemDrawer.setItemId(itemId);
        itemDrawer.setType(type);
        itemDrawerService.save(itemDrawer);
        return itemDrawer;
    }

    @Override
    public Map<Long, String> getStandardNamesByItemIds(List<Long> itemIds) {
        if (CollectionUtil.isEmpty(itemIds)) {
            return new HashMap<>(1);
        }
        return itemDrawerService.lambdaQuery().in(ItemDrawer::getItemId, itemIds)
                .select(ItemDrawer::getStandardName, ItemDrawer::getItemId)
                .list().stream()
                .collect(Collectors.toMap(ItemDrawer::getItemId, ItemDrawer::getStandardName));
    }

    @Override
    public Map<Long, String> getHomeCopyByItemIds(List<Long> itemIds) {
        return itemDrawerService.lambdaQuery().in(ItemDrawer::getItemId, itemIds)
                .select(ItemDrawer::getHomeCopy, ItemDrawer::getItemId)
                .list().stream()
                .collect(Collectors.toMap(ItemDrawer::getItemId, ItemDrawer::getHomeCopy));
    }

    @Override
    public SingleResponse<CheckReportVO> getCheckInfo(Long itemId, Long userId, String loginName) {
        CheckReportVO checkReportVO = new CheckReportVO();
        try {
            final String partnerProviderItemSn = itemService.getPartnerProviderItemSn(itemId);
            if (StringUtil.isBlank(partnerProviderItemSn)) {
                return SingleResponse.of(checkReportVO);
            }

            final PartnerItemReq req = new PartnerItemReq();
            req.setSearchType(1);
            req.setContext(partnerProviderItemSn);
            req.setPageIndex(1);
            req.setPageSize(10);
            final Rsp<List<PartnerItemResp>> itemQueryRsp = partnerFeignClient.itemQuery(req);
            if (Objects.isNull(itemQueryRsp) || CollUtil.isEmpty(itemQueryRsp.getData())) {
                throw ExceptionPlusFactory
                        .bizException(ErrorCode.DATA_NOT_FOUND,
                                "商品可能关联了无效的P系统商品款号：" + partnerProviderItemSn + "，未能在P系统查询到对应商品信息");
            }

            final Integer partnerItemId = itemQueryRsp.getData().get(0).getId();
            Rsp<CheckInfoDTO> checkInfoRes = partnerFeignClient.getCheckInfo((long) partnerItemId);
            if (!checkInfoRes.isSuccess() || Objects.isNull(checkInfoRes.getData())) {
                throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR,
                        "获取检测报告失败");
            }
            CheckInfoDTO checkInfoVO = checkInfoRes.getData();
            //获取首检
            if (checkInfoVO.getFirstCheck() != null && checkInfoVO.getFirstCheck().getId() != 0L) {
                CheckDetailVO firstCheckDetail = getCheckDetailVO(
                        checkInfoVO.getFirstCheck().getId().longValue());
                firstCheckDetail.setCheckProject(checkInfoVO.getFirstCheck().getCheckProject());
                checkReportVO.setFirstCheckInfo(firstCheckDetail);
            }
            //最新抽检
            if (checkInfoVO.getLastCheck() != null && checkInfoVO.getLastCheck().getId() != 0L) {
                CheckDetailVO lastCheckDetail = getCheckDetailVO(
                        checkInfoVO.getLastCheck().getId().longValue());
                lastCheckDetail.setCheckProject(checkInfoVO.getLastCheck().getCheckProject());
                checkReportVO.setLastCheckInfo(lastCheckDetail);
                checkReportVO.setLastCheckTime(
                        checkInfoVO.getLastCheck().getReportAt().longValue());
            }
        } catch (Exception e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR,
                    e.getMessage());
        }
        return SingleResponse.of(checkReportVO);
    }

    /**
     * 获取检测报告信息
     *
     * @param checkId
     * @return
     */
    CheckDetailVO getCheckDetailVO(Long checkId) {
        try {
            Rsp<CheckDetailDTO> lastCheckDetail = partnerFeignClient.getCheckDetail(checkId);
            if (!lastCheckDetail.isSuccess() || Objects.isNull(lastCheckDetail.getData())) {
                throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR,
                        "获取最新抽检失败");
            }
            return ItemDrawerConverter.INSTANCE.checkDetailDTOToVO(lastCheckDetail.getData());
        } catch (Exception e) {
            log.error("[检测报告]获取首检失败", e);
        }
        return null;
    }


    /**
     * 判断流程已全部填写信息
     *
     * @return
     */
    Boolean isFieldsFillOver(ItemDrawer itemDrawer) {
        Triple<List<ItemDrawerImageVO>, List<ItemDrawerImageVO>, List<ItemDrawerImageVO>> itemDrawerImages = getItemDrawerImages(
                itemDrawer.getId());
        return !StringUtils.isEmpty(itemDrawer.getStandardName())
                && !StringUtils.isEmpty(itemDrawer.getTbTitle())
//                && !StringUtils.isEmpty(itemDrawer.getMiniTitle())
                && !StringUtils.isEmpty(itemDrawer.getHomeCopy())
                && !itemDrawerImages.getLeft().isEmpty()
                && !itemDrawerImages.getMiddle().isEmpty()
                && !itemDrawerImages.getRight().isEmpty()
                ;
    }

    /**
     * 商品抽屉图片缓存，缓存键为抽屉ID
     */
    LoadingCache<Long, List<ItemDrawerImage>> itemDrawerImageLoadingCache = Caffeine.newBuilder()
            .maximumSize(100)
            .expireAfterAccess(3, TimeUnit.SECONDS).build(this::queryDrawerImages);

    /**
     * 获取抽屉图片
     *
     * @param itemDrawerId
     * @return
     */
    @Override
    public Triple<List<ItemDrawerImageVO>, List<ItemDrawerImageVO>, List<ItemDrawerImageVO>> getItemDrawerImages(
            Long itemDrawerId) {
        List<ItemDrawerImage> itemDrawerImages = getDrawerImagesCache(itemDrawerId)
                .stream().sorted(Comparator.comparing(ItemDrawerImage::getSort)).collect(Collectors.toList());
        final List<ItemDrawerImage> mainImages = itemDrawerImages.stream()
                .filter(itemDrawerImage -> Objects.equals(itemDrawerImage.getType(),
                        ItemDrawerImageTypeEnum.ITEM)).collect(Collectors.toList());
        List<ItemDrawerImageVO> images = mainImages.stream()
                .map(ItemDrawerConverter.INSTANCE::itemDrawerImageToVO)
                .collect(Collectors.toList());
        final List<ItemDrawerImage> mainImagesOtherSize = itemDrawerImages.stream()
                .filter(itemDrawerImage -> Objects.equals(itemDrawerImage.getType(),
                        ItemDrawerImageTypeEnum.OTHER_ITEM)).collect(Collectors.toList());
        List<ItemDrawerImageVO> otherImages = mainImagesOtherSize.stream()
                .map(ItemDrawerConverter.INSTANCE::itemDrawerImageToVO)
                .collect(Collectors.toList());
        List<ItemDrawerImageVO> detailImages = itemDrawerImages.stream()
                .filter(itemDrawerImage -> Objects.equals(itemDrawerImage.getType(),
                        ItemDrawerImageTypeEnum.DETAIL))
                .map(ItemDrawerConverter.INSTANCE::itemDrawerImageToVO)
                .collect(Collectors.toList());
        return Triple.of(images, otherImages, detailImages);
    }

    @Autowired
    ItemSkuGateway itemSkuGateway;

    public List<ItemDrawerSkuImage> getItemDrawerSkuImages(Long itemDrawerId, Long itemId) {
        final List<ItemSku> skuList = itemSkuGateway.getSkuList(itemId);
        final List<ItemDrawerImage> itemDrawerImages = getDrawerImagesCache(itemDrawerId,
                ItemDrawerImageTypeEnum.SKU);
        final Map<Long, String> drawerImageMap = itemDrawerImages.stream().collect(
                Collectors.toMap(ItemDrawerImage::getSkuId, ItemDrawerImage::getUrl, (a, b) -> a));
        return skuList.stream().map(sku -> {
            final ItemDrawerSkuImage itemDrawerSkuImage = new ItemDrawerSkuImage();
            final Long skuId = sku.getId();
            itemDrawerSkuImage.setSkuId(skuId);
            itemDrawerSkuImage.setSkuCode(sku.getSkuCode());
            itemDrawerSkuImage.setUrl(drawerImageMap.get(skuId));
            itemDrawerSkuImage.setSpecifications(sku.getSpecifications());
            return itemDrawerSkuImage;
        }).collect(Collectors.toList());
    }

    @Autowired
    IItemAttrService itemAttrService;
    @Autowired
    ICategoryAttrService categoryAttrService;
    @Autowired
    CategoryBizService categoryBizService;

    public List<ItemDrawerAttrImages> getItemDrawerAttrImages(Long itemDrawerId, Long itemId) {
        final List<ItemAttr> itemAttrs = itemAttrService.lambdaQuery()
                .eq(ItemAttr::getItemId, itemId)
                .list();
        final List<CategoryAttr> categoryAttrs = categoryAttrService.lambdaQuery()
                .in(CategoryAttr::getId,
                        itemAttrs.stream().map(ItemAttr::getAttrId).collect(Collectors.toList()))
                .list();
        final Map<Long, String> categoryAttrNameMap = categoryAttrs.stream()
                .collect(Collectors.toMap(CategoryAttr::getId, CategoryAttr::getName));
        final Map<Long, List<ItemAttr>> itemAttrsGroup = itemAttrs.stream()
                .collect(Collectors.groupingBy(ItemAttr::getAttrId));

        final List<ItemDrawerImage> itemDrawerImages = getDrawerImagesCache(itemDrawerId,
                ItemDrawerImageTypeEnum.ATTR);
        final Map<Long, String> drawerImageMap = itemDrawerImages.stream().collect(
                Collectors.toMap(ItemDrawerImage::getItemAttrId, ItemDrawerImage::getUrl,
                        (a, b) -> a));

        return itemAttrsGroup.entrySet().stream().map(group -> {
            final Long attrId = group.getKey();
            final ItemDrawerAttrImages itemDrawerAttrImages = new ItemDrawerAttrImages();
            itemDrawerAttrImages.setAttrId(attrId);
            itemDrawerAttrImages.setAttrName(categoryAttrNameMap.get(attrId));
            final ArrayList<ItemDrawerAttrImage> itemAttrImages = Lists.newArrayList();
            itemDrawerAttrImages.setItemAttrs(itemAttrImages);
            for (ItemAttr itemAttr : group.getValue()) {
                final ItemDrawerAttrImage itemDrawerAttrImage = new ItemDrawerAttrImage();
                itemDrawerAttrImage.setId(itemAttr.getId());
                itemDrawerAttrImage.setAttrValue(itemAttr.getAttrValue());
                itemDrawerAttrImage.setUrl(drawerImageMap.get(itemAttr.getId()));
                itemAttrImages.add(itemDrawerAttrImage);
            }
            itemDrawerAttrImages.calcImagesAdded();
            return itemDrawerAttrImages;
        }).collect(Collectors.toList());
    }

    private List<ItemDrawerImage> getDrawerImagesCache(Long itemDrawerId) {
        return getDrawerImagesCache(itemDrawerId, null);
    }

    private List<ItemDrawerImage> getDrawerImagesCache(Long itemDrawerId,
                                                       ItemDrawerImageTypeEnum itemDrawerImageType) {
        final List<ItemDrawerImage> itemDrawerImages = itemDrawerImageLoadingCache.get(
                itemDrawerId);
        if (CollectionUtil.isEmpty(itemDrawerImages)) {
            return Collections.emptyList();
        }
        return itemDrawerImageType != null ? itemDrawerImages.stream()
                .filter(v -> itemDrawerImageType.equals(v.getType())).collect(
                        Collectors.toList()) : itemDrawerImages;
    }

    private List<ItemDrawerImage> getDrawerLiveImagesCache(Long itemDrawerId, Long liveVerbalTrickId) {
        final List<ItemDrawerImage> itemDrawerImages = itemDrawerImageLoadingCache.get(
                itemDrawerId);
        if (CollectionUtil.isEmpty(itemDrawerImages)) {
            return Collections.emptyList();
        }
        return itemDrawerImages.stream()
                .filter(v -> {
                            boolean b1 = ItemDrawerImageTypeEnum.LIVE_IMAGE.equals(v.getType());
                            if (Objects.nonNull(liveVerbalTrickId) && liveVerbalTrickId != 0) {
                                boolean b2 = liveVerbalTrickId.equals(v.getLiveVerbalTrickId());
                                return b1 && b2;
                            } else {
                                return b1;
                            }
                        }
                ).collect(
                        Collectors.toList());
    }


    private void refreshDrawerImageCache(Long itemDrawerId) {
        itemDrawerImageLoadingCache.refresh(itemDrawerId);
    }

    private List<ItemDrawerImage> queryDrawerImages(Long itemDrawerId) {
        final LambdaQueryWrapper<ItemDrawerImage> query = Wrappers.lambdaQuery();
        query.eq(ItemDrawerImage::getDrawerId, itemDrawerId);
        return IItemDrawerImageService.getBaseMapper().selectList(query);
    }

    /**
     * 是否已经创建流程
     *
     * @param itemDrawer
     * @return
     */
    String hasProcessInst(ItemDrawer itemDrawer) {
        return awsBusinessLogService.getProcessIdByBusinessId(PurchaseTypeEnum.ITEM_LIBRARY_AUDIT,
                itemDrawer.getId());
    }

    @Transactional
    @Override
    public SingleResponse<Boolean> submit(Long drawerId, Long userId, String loginName) {
        if (!UserPermissionJudge.hasApiPermission(ItemDrawerChangeAuthEnum.PRINCIPAL.getValue())) {
            throw ExceptionPlusFactory.bizException(ErrorCode.NO_ACCESS);
        }
        ItemDrawer itemDrawer = itemDrawerService.getById(drawerId);
        if (Objects.isNull(itemDrawer)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "抽屉不存在");
        }
        Item item = itemService.getById(itemDrawer.getItemId());
        if (Objects.isNull(item)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "商品不存在");
        }
        if (!TO_BE_DESIGNED.getValue().equals(item.getLaunchStatus())
                && !TO_BE_UPDATED.getValue().equals(item.getLaunchStatus())) {
            throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR,
                    "商品状态仅待修改和待设计才可提交");
        }
        /**
         *  [字段填写完成]
         * 1.创建流程
         * 2.修改商品流程状态
         */
        if (!isFieldsFillOver(itemDrawer)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR,
                    "数据还未补充完成不能提交");
        }
        //待设计提交到待审核，发起审批流程
        if (Objects.equals(item.getLaunchStatus(), TO_BE_DESIGNED.getValue())) {

            //图片信息文案和商品详情都是必填的，首次提交必定触发审核
            final List<ItemDrawerModuleId> itemDrawerModuleIds = Lists.newArrayList(
                    ItemDrawerModuleId.IMG_INFO,
                    ItemDrawerModuleId.TEXT_AND_ATTR,
                    ItemDrawerModuleId.DETAILS
            );
            itemDrawerAuditBizService.createProcess(ItemAuditType.ITEM_MATERIAL, UserContext.getUserId(), item.getId(),
                    itemDrawerModuleIds.toArray(new ItemDrawerModuleId[0]));
        }
        //待修改 -> 待上架
        if (item.getLaunchStatus().equals(TO_BE_UPDATED.getValue())) {
            //需求：待修改➡️待上架流转，除了产品组提交商品，还需要「五月天」同时点击提交，她也要修改信息。【都提交才可到待上架】
            //上新子状态定义 1:产品组已提交，等待五月天提交 2:五月天已提交，等待产品组提交
            if (recipientConfig.getMayday(item.getBusinessLine()).contains(UserContext.getLoginName())) {
                if (itemDrawer.getLaunchSubmitStatus() == 0) {
                    itemDrawer.setLaunchSubmitStatus(2);
                    itemDrawer.setCoopUid(UserContext.getUserId());
                    itemDrawerService.lambdaUpdate().eq(Entity::getId, itemDrawer.getId())
                            .set(ItemDrawer::getLaunchSubmitStatus, itemDrawer.getLaunchSubmitStatus())
                            .set(ItemDrawer::getCoopUid, itemDrawer.getCoopUid())
                            .update();
                    operateLogGateway.addOperatorLog(userId, OperateLogTarget.ITEM_DRAWER, itemDrawer.getId(),
                            "协同者已提交，等待产品组提交", null);
                } else if (itemDrawer.getLaunchSubmitStatus() == 2) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR,
                            "您已提交，请等待产品组提交");
                } else {
                    itemBizService.updateLaunchStatus(itemDrawer.getItemId(), TO_BE_RELEASED);
                }
            } else {
                if (itemDrawer.getLaunchSubmitStatus() == 0) {
                    itemDrawer.setLaunchSubmitStatus(1);
                    itemDrawerService.lambdaUpdate().eq(Entity::getId, itemDrawer.getId())
                            .set(ItemDrawer::getLaunchSubmitStatus, itemDrawer.getLaunchSubmitStatus()).update();
                    operateLogGateway.addOperatorLog(userId, OperateLogTarget.ITEM_DRAWER, itemDrawer.getId(),
                            "产品组已提交，等待协同者提交", null);
                } else if (itemDrawer.getLaunchSubmitStatus() == 1) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR,
                            "您已提交，请等待协同者提交");
                } else {
                    itemBizService.updateLaunchStatus(itemDrawer.getItemId(), TO_BE_RELEASED);
                }
            }
        } else {
            //[待设计] --> [待审核]
            itemBizService.updateLaunchStatus(itemDrawer.getItemId(), TO_BE_AUDITED);
        }
        // 复制状态
        copyStatusInMerge(itemDrawer.getItemId());
        return SingleResponse.of(Boolean.TRUE);
    }

    @Override
    public void processTerminateByItemId(Long itemId) {
        final ItemLaunchStatus itemLaunchStatus = itemService.getItemLaunchStatus(itemId);
        if (itemLaunchStatus == null || !ImmutableList
                .of(TO_BE_AUDITED)
                .contains(itemLaunchStatus)) {
            return;
        }

        doProcessTerminateByItemId(itemId);
    }

    private void doProcessTerminateByItemId(Long itemId) {
        itemDrawerAuditBizService.terminal(ItemAuditType.ITEM_MATERIAL, UserContext.getUserId(), itemId);
    }

    @Override
    public Boolean deleteItemDrawer(Long itemId) {
        ItemDrawer itemDrawer = getItemDrawer(itemId);
        if (Objects.isNull(itemDrawer)) {
            return true;
        }
        IItemDrawerImageService.getBaseMapper().delete(new LambdaQueryWrapper<ItemDrawerImage>() {{
            eq(ItemDrawerImage::getDrawerId, itemDrawer.getId());
        }});
        //删除标记信息
        itemDrawerMarkImageService.deleteByDrawerId(itemDrawer.getId());
        return itemDrawerService.removeById(itemDrawer.getId());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void rollback(Long itemId, ItemLaunchStatus rollbackTo, ItemAuditStatus auditRollbackTo) {
        if (rollbackTo != TO_BE_AUDITED) {
            auditRollbackTo = null;
        }
        final List<ItemLaunchStatus> permitRollbackToStatus = Arrays.asList(
                TO_BE_IMPROVED,
                TO_BE_DESIGNED,
                TO_BE_AUDITED,
                TO_BE_UPDATED
        );
        if (!permitRollbackToStatus.contains(rollbackTo)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT,
                    "不允许退回到【" + rollbackTo.getDesc() + "】状态");
        }
        final Item item = itemService.getById(itemId);
        final Integer currentLaunchStatusVal = item.getLaunchStatus();
        final ItemLaunchStatus currentLaunchStatus = ItemLaunchStatus.of(currentLaunchStatusVal);
        final Integer currentAuditStatusVal = item.getAuditStatus();
        final ItemAuditStatus currentAuditStatus = IEnum.getEnumByValue(ItemAuditStatus.class,
                currentAuditStatusVal);
        if (currentLaunchStatus == null || currentAuditStatus == null) {
            throw new IllegalStateException("商品当前上新状态&审核状态值异常");
        }

        if (currentLaunchStatus.ordinal() < rollbackTo.ordinal()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT,
                    "当前状态为【" + currentLaunchStatus.getDesc() + "】无法回退到【" + rollbackTo.getDesc()
                            + "】");
        }

        //如果当前为待审核，回退到待审核之前的状态，需要将当前的审核流程终止
        if (TO_BE_AUDITED == currentLaunchStatus
                && rollbackTo.ordinal() < TO_BE_AUDITED.ordinal()) {
            itemDrawerAuditBizService.terminal(ItemAuditType.ITEM_MATERIAL, UserContext.getUserId(), itemId);
        }

        //状态回退到【待审核】之前，或者回退到【待审核|法务审核】，需要移除掉之前的详情审核标注
        if (rollbackTo.ordinal() < TO_BE_AUDITED.ordinal()
                || auditRollbackTo == ItemAuditStatus.WAIT_LEGAL_AUDIT) {
            itemDrawerMarkImageService.clean(itemId, true, UserContext.getUserId());
        }

        //回退到待审核，需将审核流程逆操作
        if (rollbackTo == TO_BE_AUDITED) {
            if (!Arrays.asList(ItemAuditStatus.WAIT_LEGAL_AUDIT, ItemAuditStatus.WAIT_QC_AUDIT)
                    .contains(auditRollbackTo)) {
                throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "请指定退回到哪个审核节点");
            }
            itemDrawerAuditBizService.rollback(ItemAuditType.ITEM_MATERIAL, itemId, auditRollbackTo);
        }

        //退回商品主记录状态
        itemService.lambdaUpdate()
                .set(Item::getLaunchStatus, rollbackTo.getValue())
                .set(auditRollbackTo != null, Item::getAuditStatus,
                        Optional.ofNullable(auditRollbackTo).map(ItemAuditStatus::getValue)
                                .orElse(null))
                .eq(Entity::getId, itemId).update();

        //操作日志
        operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.NEW_GOODS_SPU,
                itemId, "回退商品上新状态到【" + rollbackTo.getDesc() + Optional.ofNullable(auditRollbackTo)
                        .map(v -> " | " + v.getDesc()).orElse("") + "】", null);
    }

    @Override
    public void setLaunchStatus(SetLaunchStatusCmd cmd) {
        final ItemAuditType type = cmd.getAuditType();
        final Long itemId = cmd.getItemId();
        final ItemLaunchStatus setLaunchStatus = cmd.getLaunchStatus();
        final ItemAuditStatus setAuditStatus = cmd.getAuditStatus();
        final Item item = itemService.getById(itemId);
        final Integer currentLaunchStatusVal = item.getLaunchStatus();
        final ItemLaunchStatus currentLaunchStatus = ItemLaunchStatus.of(currentLaunchStatusVal);
        final Integer currentAuditStatusVal = item.getAuditStatus();
        final ItemAuditStatus currentAuditStatus = IEnum.getEnumByValue(ItemAuditStatus.class,
                currentAuditStatusVal);
        if (currentLaunchStatus == null || currentAuditStatus == null) {
            throw new IllegalStateException("商品当前上新状态&审核状态值异常");
        }

        if (currentLaunchStatus == setLaunchStatus && currentAuditStatus == setAuditStatus) {
            return;
        }

        //如果当前为待审核，回退到待审核之前的状态，需要将当前的审核流程终止
        if (TO_BE_AUDITED == currentLaunchStatus
                && setLaunchStatus.ordinal() < TO_BE_AUDITED.ordinal()) {
            itemDrawerAuditBizService.terminal(type, UserContext.getUserId(), itemId);
        }

        //状态回退到【待审核】之前，或者回退到【待审核|法务审核】，需要移除掉之前的详情审核标注
        if (type == ItemAuditType.ITEM_MATERIAL && cmd.isCleanMark() && (setLaunchStatus.ordinal() < TO_BE_AUDITED.ordinal()
                || setAuditStatus.getValue() < currentAuditStatusVal)) {
            itemDrawerMarkImageService.clean(itemId, true, UserContext.getUserId());
        }

        //状态变更至【待审核】，重新创建流程
        if (setLaunchStatus == TO_BE_AUDITED) {
            final ItemDrawerModuleId[] itemDrawerModuleIds = {
                    ItemDrawerModuleId.BASIC_INFO,
                    ItemDrawerModuleId.DETAILS,
                    ItemDrawerModuleId.IMG_INFO,
                    ItemDrawerModuleId.TEXT_AND_ATTR,
                    ItemDrawerModuleId.LIVE_VERBAL_TRICK};
            final CreateProcessCmd createProcessCmd = new CreateProcessCmd();
            createProcessCmd.setType(type);
            createProcessCmd.setLiveVerbalTrickId(cmd.getLiveVerbalTrickId());
            createProcessCmd.setUid(cmd.getCurrentUserId());
            createProcessCmd.setItemId(itemId);
            createProcessCmd.setModuleIds(itemDrawerModuleIds);
            switch (setAuditStatus) {
                case WAIT_LEGAL_AUDIT:
                    createProcessCmd.setStartNodeId(ItemLaunchProcessNodeId.LEGAL);
                    break;
                case WAIT_QC_AUDIT:
                    createProcessCmd.setStartNodeId(ItemLaunchProcessNodeId.QC);
                    break;
                default:
                    throw new IllegalStateException("不支持的审核状态");
            }
            createProcessCmd.setTriggerEvent(cmd.isTriggerEvent());
            itemDrawerAuditBizService.createProcess(createProcessCmd);
        }

        //退回商品主记录状态
        itemService.lambdaUpdate()
                .set(Item::getLaunchStatus, setLaunchStatus.getValue())
                .set(setAuditStatus != null, Item::getAuditStatus,
                        Optional.ofNullable(setAuditStatus).map(ItemAuditStatus::getValue)
                                .orElse(null))
                .set(Entity::getUpdatedAt, DateUtil.currentTime())
                .set(Entity::getUpdatedUid, UserContext.getUserId())
                .eq(Entity::getId, itemId).update();

        // 操作日志
        operateLogGateway.addOperatorLog(
                UserContext.getUserId(),
                OperateLogTarget.NEW_GOODS_SPU,
                itemId,
                "商品上新状态跳转到【"
                        + setLaunchStatus.getDesc()
                        + Optional.ofNullable(setAuditStatus)
                                .filter(v -> v != ItemAuditStatus.NONE)
                                .map(v -> " | " + v.getDesc())
                                .orElse("")
                        + "】",
                null);
    }

    /**
     * 判断是否有权限
     *
     * @param itemDrawerChangeEnum
     * @return
     */
    boolean hasPermission(ItemDrawerChangeEnum itemDrawerChangeEnum) {
        if (!ITEM_DRAWER_EDIT_AUTH_MAP.containsKey(itemDrawerChangeEnum)) {
            return true;
        }
        //获取字段需要权限
        ItemDrawerChangeAuthEnum itemDrawerChangeAuthEnums = ITEM_DRAWER_EDIT_AUTH_MAP.get(
                itemDrawerChangeEnum);
        return UserPermissionJudge.hasApiPermission(itemDrawerChangeAuthEnums.getValue());
    }


    @Override
    public void updateHomeCopy(Long itemId) {
        ItemLaunchPlanItemRef itemLaunchPlanItemRef = itemLaunchPlanItemRefService.lambdaQuery()
                .eq(ItemLaunchPlanItemRef::getItemId, itemId)
                .orderByDesc(ItemLaunchPlanItemRef::getId).last("limit 1").one();
        if (Objects.nonNull(itemLaunchPlanItemRef)) {
            String homeCopy = itemLaunchPlanItemRef.getHomeCopy();
            if (StrUtil.isNotBlank(homeCopy)) {
                ItemDrawer one = itemDrawerService.lambdaQuery().eq(ItemDrawer::getItemId, itemId)
                        .select(ItemDrawer::getId, ItemDrawer::getHomeCopy)
                        .orderByDesc(ItemDrawer::getId).last("limit 1").one();
                if (Objects.nonNull(one)) {
                    String oldHomeCopy = one.getHomeCopy();
                    one.setHomeCopy(homeCopy);
                    itemDrawerService.updateById(one);

                    ChangePropertyObj changePropertyObj = new ChangePropertyObj();
                    changePropertyObj.setProperty("首页文案");
                    changePropertyObj.setOldVal(oldHomeCopy);
                    changePropertyObj.setNewVal(homeCopy);
                    operateLogGateway.addOperatorLog(0L, OperateLogTarget.ITEM_DRAWER, one.getId()
                            , "上新计划首页文案系统同步", new Object[]{changePropertyObj});
                }

            }
        }
    }

    @Override
    public void updateItemDrawerType(Long itemId, LaunchItemType type) {
        ItemDrawer itemDrawer = itemDrawerService.lambdaQuery()
                .eq(ItemDrawer::getItemId, itemId).last("LIMIT 1").one();
        if (itemDrawer == null) {
            createDrawer(itemId, type);
            operateLogGateway.addOperatorLog(0L, OperateLogTarget.ITEM_DRAWER, itemId,
                    "上新计划修改，初始化抽屉数据",
                    JsonUtil.toJson(new ChangePropertyObj[]{
                            new ChangePropertyObj(ItemDrawerChangeEnum.TYPE.getDesc(), null, type)
                    }));
        } else if (itemDrawer.getType() != type) {
            final ItemDrawer itemDrawerToUpdate = new ItemDrawer();
            itemDrawerToUpdate.setId(itemDrawer.getId());
            itemDrawerToUpdate.setType(type);
            itemDrawerService.updateById(itemDrawerToUpdate);
            operateLogGateway.addOperatorLog(0L, OperateLogTarget.ITEM_DRAWER, itemId,
                    "上新计划修改商品类型",
                    JsonUtil.toJson(new ChangePropertyObj[]{
                            new ChangePropertyObj(ItemDrawerChangeEnum.TYPE.getDesc(),
                                    itemDrawer.getType(), type)
                    }));
        }
    }

    /**
     * 新版本的话术编辑处理逻辑
     *
     * @param cmd
     * @return
     */
    public Response editLiveVerbalTrickNewVersion(EditLiveVerbalTrickCmd cmd) {
        Boolean submit = cmd.getSubmit();
        boolean isAdd = Objects.isNull(cmd.getLiveVerbalTrickId());
        ItemDrawer itemDrawer = getItemDrawer(cmd.getItemId());
        Item item = itemService.getById(cmd.getItemId());

        if (isAdd) {
            ItemDrawerLiveVerbal liveVerbal = new ItemDrawerLiveVerbal();
            liveVerbal.setName("");
            liveVerbal.setItemId(itemDrawer.getItemId());
            liveVerbal.setItemDrawerId(itemDrawer.getId());
            liveVerbal.setLiveVerbalTrickStatus(ItemAuditStatus.NONE);
            liveVerbal.setLiveVerbalTrick(cmd.getLiveVerbalTrick());
            liveVerbal.setPrincipalId(cmd.getPrincipalId());
            iItemDrawerLiveVerbalService.save(liveVerbal);
            Long liveVerbalTrickId = liveVerbal.getId();

            List<ItemDrawerImageForm> images = cmd.getImages();
            List<String> imageList = images.stream().map(ItemDrawerImageForm::getUrl).collect(Collectors.toList());
            saveImage(itemDrawer.getId(), imageList, ItemDrawerImageTypeEnum.LIVE_IMAGE, liveVerbalTrickId);

            if (submit) {
                itemDrawerAuditBizService.createProcess(
                        ItemAuditType.LIVE_VERBAL_TRICK,
                        liveVerbalTrickId,
                        cmd.getUid(),
                        cmd.getItemId(),
                        ItemDrawerModuleId.LIVE_VERBAL_TRICK);

//                EventBusUtil.post(
//                        MsgEvent.ofLiveVerbalTrickAuditCompleted(
//                                cmd.getItemId(),
//                                MsgEventType.LIVE_SPEECH_AUDIT_LEGAL,
//                                liveVerbalTrickId,
//                                iItemDrawerLiveVerbalService.getName(
//                                        StringUtil.isNotEmpty(itemDrawer.getLiveVerbalTrick()),
//                                        liveVerbal)));
                qwMsgLiveVerbalToLegal(cmd.getItemId(), item.getCode(), itemDrawer.getStandardName());
            }

//            SpringUtil.publishEvent(new SaveEvent<>(this, liveVerbal, Boolean.TRUE));
            return Response.buildSuccess();
        }

        // id=0表示为老数据。老数据处理依旧。
        if (cmd.getLiveVerbalTrickId() == 0) {
            return editLiveVerbalTrickOldVersion(cmd);
        }
        Long liveVerbalTrickId = cmd.getLiveVerbalTrickId();
        ItemDrawerLiveVerbal oldOne = iItemDrawerLiveVerbalService.getById(liveVerbalTrickId);
        Assert.notNull(oldOne, "话术直播ID非法");
        // 权限检查
        boolean editAll = UserContext.hasPermission(EDIT_ALL_LIVE_VERBAL_TRICK, ResourceType.API);
        if (!editAll) {
            Assert.state(Objects.equals(UserContext.getUserId(), oldOne.getPrincipalId()),
                         "您不是当前话术的负责人，无权修改");
        }
        final boolean isReAudit = oldOne.getLiveVerbalTrickStatus() == ItemAuditStatus.WAIT_MODIFY;
        String name =
                iItemDrawerLiveVerbalService.getName(
                        StringUtil.isNotEmpty(itemDrawer.getLiveVerbalTrick()), oldOne);

        final String liveVerbalTrickL = oldOne.getLiveVerbalTrick();
        final String liveVerbalTrickR = cmd.getLiveVerbalTrick();
        final List<ItemDrawerImage> imagesL = getDrawerLiveImagesCache(itemDrawer.getId(), liveVerbalTrickId);
        final List<String> imageUrlsL = imagesL.stream().map(ItemDrawerImage::getUrl).collect(Collectors.toList());
        final List<ItemDrawerImageForm> imagesR = cmd.getImages();
        final List<String> imageUrlsR = imagesR.stream().map(ItemDrawerImageForm::getUrl).collect(Collectors.toList());

        if (!ObjectUtil.isEmpty(liveVerbalTrickR, true) && (!ObjectUtil.equal(liveVerbalTrickR, liveVerbalTrickL)
                || !ObjectUtil.equal(imageUrlsL, imageUrlsR))) {
            oldOne.setLiveVerbalTrick(liveVerbalTrickR);
            if (submit) {
                oldOne.setLiveVerbalTrickStatus(ItemAuditStatus.WAIT_LEGAL_AUDIT);
            }
            iItemDrawerLiveVerbalService.updateById(oldOne);
            ArrayList<ItemDrawerImage> itemDrawerImages = saveImage(itemDrawer.getId(), imageUrlsR, ItemDrawerImageTypeEnum.LIVE_IMAGE, liveVerbalTrickId);

            if (submit) {
                if (isReAudit) {
                    final ItemDrawerModuleAuditTaskVO activeTask =
                            itemDrawerAuditBizService.getActiveTask(
                                    ItemAuditType.LIVE_VERBAL_TRICK,
                                    cmd.getLiveVerbalTrickId(),
                                    UserContext.getUserId(),
                                    itemDrawer.getItemId(),
                                    ItemLaunchProcessNodeId.MODIFY);
                    if (activeTask == null) {
                        throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "您并非当前节点的经办人");
                    }
                    itemDrawerAuditBizService.complete(
                            UserContext.getUserId(),
                            Long.valueOf(activeTask.getId()),
                            cmd.getLiveVerbalTrickId(),
                            Collections.emptyList(),
                            DateUtil.currentTime(),
                            true);
                } else {
                    itemDrawerAuditBizService.createProcess(
                            ItemAuditType.LIVE_VERBAL_TRICK,
                            cmd.getLiveVerbalTrickId(),
                            cmd.getUid(),
                            cmd.getItemId(),
                            ItemDrawerModuleId.LIVE_VERBAL_TRICK);
                }
                // 已提交状态的直播话术发生改变，交个法务重新审核，发送消息
//                EventBusUtil.post(
//                        MsgEvent.ofLiveVerbalTrickAuditCompleted(
//                                cmd.getItemId(),
//                                MsgEventType.LIVE_SPEECH_AUDIT_LEGAL,
//                                liveVerbalTrickId,
//                                name));
                qwMsgLiveVerbalToLegal(cmd.getItemId(), item.getCode(), itemDrawer.getStandardName());
            }
            final String msg = cmd.getSubmit() ? "直播话术（" + name + "）变更，提交审核" : "直播话术（" + name + "）变更";
            operateLogGateway.addOperatorLog(
                    cmd.getUid(),
                    OperateLogTarget.ITEM_DRAWER,
                    itemDrawer.getId(),
                    msg,
                    new ChangePropertyObj[]{
                            new ChangePropertyObj(
                                    ItemDrawerChangeEnum.LIVE_VERBAL_TRICK.getDesc(),
                                    liveVerbalTrickL,
                                    liveVerbalTrickR),
                            new ChangePropertyObj(
                                    ItemDrawerChangeEnum.LIVE_IMAGE.getDesc(),
                                    convertImgInfos(imagesL),
                                    convertImgInfos(itemDrawerImages))
                    });
        } else if (submit) {
            iItemDrawerLiveVerbalService
                    .lambdaUpdate()
                    .eq(ItemDrawerLiveVerbal::getId, cmd.getLiveVerbalTrickId())
                    .set(ItemDrawerLiveVerbal::getLiveVerbalTrickStatus, ItemAuditStatus.WAIT_LEGAL_AUDIT)
                    .update();
            if (isReAudit) {
                final ItemDrawerModuleAuditTaskVO activeTask =
                        itemDrawerAuditBizService.getActiveTask(
                                ItemAuditType.LIVE_VERBAL_TRICK,
                                cmd.getLiveVerbalTrickId(),
                                UserContext.getUserId(),
                                itemDrawer.getItemId(),
                                ItemLaunchProcessNodeId.MODIFY);
                if (activeTask == null) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "您并非当前节点的经办人");
                }
                itemDrawerAuditBizService.complete(
                        UserContext.getUserId(),
                        Long.valueOf(activeTask.getId()),
                        cmd.getLiveVerbalTrickId(),
                        Collections.emptyList(),
                        DateUtil.currentTime(),
                        true);
                operateLogGateway.addOperatorLog(
                        cmd.getUid(),
                        OperateLogTarget.ITEM_DRAWER,
                        itemDrawer.getId(),
                        "直播话术（" + name + "）提交复审",
                        null);
            } else {
                itemDrawerAuditBizService.createProcess(
                        ItemAuditType.LIVE_VERBAL_TRICK,
                        cmd.getLiveVerbalTrickId(),
                        cmd.getUid(),
                        cmd.getItemId(),
                        ItemDrawerModuleId.LIVE_VERBAL_TRICK);
                operateLogGateway.addOperatorLog(
                        cmd.getUid(),
                        OperateLogTarget.ITEM_DRAWER,
                        itemDrawer.getId(),
                        "直播话术（" + name + "）提交审核",
                        null);
            }
            // 话术提交法务审核，发送消息
//            EventBusUtil.post(
//                    MsgEvent.ofLiveVerbalTrickAuditCompleted(
//                            cmd.getItemId(),
//                            MsgEventType.LIVE_SPEECH_AUDIT_LEGAL,
//                            liveVerbalTrickId,
//                            name));
            qwMsgLiveVerbalToLegal(cmd.getItemId(), item.getCode(), itemDrawer.getStandardName());
        }

//        SpringUtil.publishEvent(new SaveEvent<>(this, oldOne, Boolean.TRUE));
        return Response.buildSuccess();
    }

    private void qwMsgLiveVerbalToLegal(Long itemId, String itemCode, String itemName) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("提交人花名", UserContext.getNickName());
        paramMap.put("商品名称", itemName);
        paramMap.put("商品编码", itemCode);

        String receiver = "";
        List<ItemDrawerModuleAuditTask> list = iItemDrawerModuleAuditTaskService.lambdaQuery()
                .eq(ItemDrawerModuleAuditTask::getItemId, itemId)
                .eq(ItemDrawerModuleAuditTask::getType, ItemAuditType.LIVE_VERBAL_TRICK)
                .eq(ItemDrawerModuleAuditTask::getAuditStatus, ItemDrawerAuditTaskState.PENDING)
                .orderByDesc(ItemDrawerModuleAuditTask::getId).list();
        if (CollUtil.isEmpty(list)) {
            log.error("直播话术待审核消息处理，待审核状态审核任务查询为空。itemId:{}", itemId);
            return;
        }
        ItemDrawerModuleAuditTask itemDrawerModuleAuditTask = list.get(0);
        Long processorId = itemDrawerModuleAuditTask.getProcessorId();
        // 如果任务认领，给指定法务发消息，否则，全部法务
        if (Objects.nonNull(processorId) && processorId > 0) {
            List<DadStaffVO> staffList = staffService.getStaffList(Collections.singletonList(processorId));
            if (CollectionUtils.isNotEmpty(staffList)) {
                receiver = staffList.get(0).getQwUserId();
            }
        } else {
            StaffListQuery staffListQuery = new StaffListQuery();
            staffListQuery.setDept("法务内控部");
            List<StaffInfo> staffInfos = userGateway.queryStaffList(staffListQuery);
            List<Long> allUserIdList = staffInfos.stream().map(StaffInfo::getUserId).collect(Collectors.toList());
            List<DadStaffVO> staffList = staffService.getStaffList(allUserIdList);
            if (CollectionUtils.isNotEmpty(staffList)) {
                receiver = staffList.stream().map(DadStaffVO::getQwUserId).distinct().collect(Collectors.joining(StrUtil.COMMA));
            }
        }
        paramMap.put("法务负责人", receiver);
        paramMap.put("商品ID", itemId);

        final ItemLaunchPlan plan = itemLaunchPlanService.getPlanByItemId(itemId);
        final String launchDateStr = Optional.ofNullable(plan)
                .map(ItemLaunchPlan::getLaunchTime)
                .map(DateUtil::formatDate)
                .orElse("?");
        paramMap.put("上新计划日期", launchDateStr);

        QyMsgSendUtils.send(RemindType.IMMEDIATELY_REMINDER, MsgTemplateCode.LIVE_VERBAL_TO_LEGAL, paramMap);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response editLiveVerbalTrick(EditLiveVerbalTrickCmd cmd) {
        return editLiveVerbalTrickNewVersion(cmd);
    }

    @Override
    public Response editLiveVerbalTrickPrincipal(EditLiveVerbalTrickPrincipalCmd cmd) {
        final ItemDrawerLiveVerbal liveVerbal = iItemDrawerLiveVerbalService.getById(cmd.getLiveVerbalTrickId());
        Assert.notNull(liveVerbal, "直播话术不存在");
        final Long oldPrincipalId = liveVerbal.getPrincipalId();
        final boolean editAll = UserPermissionJudge.hasApiPermission(EDIT_ALL_LIVE_VERBAL_TRICK);
        if (!Objects.equals(oldPrincipalId, cmd.getCurrentUserId()) && !editAll) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "您不是当前话术负责人，不能修改");
        }
        final Optional<StaffBrief> optionalOldPrincipal = Optional.ofNullable(oldPrincipalId)
                                                          .filter(NumberUtil::isPositive)
                                                          .map(StaffAssembler.INST::toStaffBrief);
        final Long newPrincipalId = cmd.getPrincipalId();
        final StaffBrief newPrincipal = Optional.ofNullable(newPrincipalId)
                                                .filter(NumberUtil::isPositive)
                                                .map(StaffAssembler.INST::toStaffBrief)
                                                .orElseThrow(() -> ExceptionPlusFactory.bizException(
                                                        ErrorCode.VERIFY_PARAM, "无效的负责人"));
        if (!Objects.equals(newPrincipalId, oldPrincipalId)) {
            final ItemDrawerLiveVerbal updateObj = new ItemDrawerLiveVerbal();
            updateObj.setId(liveVerbal.getId());
            updateObj.setPrincipalId(newPrincipalId);
            iItemDrawerLiveVerbalService.updateById(updateObj);
            final Optional<ItemDrawer> optionalItemDrawer = itemDrawerService.getByItemId(liveVerbal.getItemId());
            final boolean hasOldData = optionalItemDrawer
                    .filter(v -> StringUtil.isNotBlank(v.getLiveVerbalTrick())).isPresent();
            operateLogGateway.addOperatorLog(cmd.getCurrentUserId(),
                                             OperateLogTarget.ITEM_DRAWER,
                                             liveVerbal.getItemDrawerId(),
                                             String.format("将直播话术（%s）的负责人从 %s 修改为 %s",
                                                           iItemDrawerLiveVerbalService.getName(hasOldData, liveVerbal),
                                                           optionalOldPrincipal.map(StaffBrief::getNickname)
                                                                               .orElse("/"),
                                                           newPrincipal.getNickname()),
                                             new DiffUtil.ChangePropertyObj[]{
                                                     new DiffUtil.ChangePropertyObj("principalId",
                                                                                    oldPrincipalId,
                                                                                    newPrincipalId)
                                             });
        }
        return Response.buildSuccess();
    }

    public Response editLiveVerbalTrickOldVersion(EditLiveVerbalTrickCmd cmd) {
        Objects.requireNonNull(cmd.getUid(), "用户ID不能为空");
        final ItemDrawer itemDrawer = getItemDrawer(cmd.getItemId());
        Item item = itemService.getById(cmd.getItemId());
        final ItemAuditStatus liveVerbalTrickStatus = itemDrawer.getLiveVerbalTrickStatus();
        final boolean isReAudit = liveVerbalTrickStatus == ItemAuditStatus.WAIT_MODIFY;
        if (liveVerbalTrickStatus != ItemAuditStatus.NONE
                && liveVerbalTrickStatus != ItemAuditStatus.FINISHED
                && !isReAudit) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "审核中不能编辑直播话术");
        }
        final String liveVerbalTrickR = cmd.getLiveVerbalTrick();
        final String liveVerbalTrickL = itemDrawer.getLiveVerbalTrick();
        final List<ItemDrawerImage> imagesL =
                getDrawerImagesCache(itemDrawer.getId(), ItemDrawerImageTypeEnum.LIVE_IMAGE);
        final List<String> imageUrlsL =
                imagesL.stream().map(ItemDrawerImage::getUrl).collect(Collectors.toList());
        final List<String> imageNamesL =
                imagesL.stream().map(ItemDrawerImage::getFilename).collect(Collectors.toList());
        final List<ItemDrawerImageForm> imagesR = cmd.getImages();
        final List<String> imageUrlsR =
                imagesR.stream().map(ItemDrawerImageForm::getUrl).collect(Collectors.toList());
        if (!ObjectUtil.isEmpty(liveVerbalTrickR, true)
                && (!ObjectUtil.equal(liveVerbalTrickR, liveVerbalTrickL)
                || !ObjectUtil.equal(imageUrlsL, imageUrlsR))) {
            final LambdaUpdateChainWrapper<ItemDrawer> itemDrawerUpdateStatement =
                    itemDrawerService.lambdaUpdate().eq(Entity::getId, itemDrawer.getId());
            if (cmd.getSubmit()) {
                itemDrawerUpdateStatement.set(
                        ItemDrawer::getLiveVerbalTrickStatus, ItemAuditStatus.WAIT_LEGAL_AUDIT);
            }
            itemDrawerUpdateStatement
                    .set(ItemDrawer::getLiveVerbalTrick, liveVerbalTrickR)
                    .update();
            final ArrayList<ItemDrawerImage> itemDrawerImages =
                    saveImage(itemDrawer.getId(), imageUrlsR, ItemDrawerImageTypeEnum.LIVE_IMAGE, null);
            if (cmd.getSubmit()) {
                if (isReAudit) {
                    final ItemDrawerModuleAuditTaskVO activeTask =
                            itemDrawerAuditBizService.getActiveTask(
                                    ItemAuditType.LIVE_VERBAL_TRICK,
                                    UserContext.getUserId(),
                                    itemDrawer.getItemId(),
                                    ItemLaunchProcessNodeId.MODIFY);
                    if (activeTask == null) {
                        throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "您并非当前节点的经办人");
                    }
                    itemDrawerAuditBizService.complete(
                            UserContext.getUserId(),
                            Long.valueOf(activeTask.getId()),
                            Collections.emptyList(),
                            true);
                } else {
                    itemDrawerAuditBizService.createProcess(
                            ItemAuditType.LIVE_VERBAL_TRICK,
                            cmd.getUid(),
                            cmd.getItemId(),
                            ItemDrawerModuleId.LIVE_VERBAL_TRICK);
                }

                // 已提交状态的直播话术发生改变，交个法务重新审核，发送消息
//                EventBusUtil.post(
//                        MsgEvent.ofLiveVerbalTrickAuditCompleted(
//                                cmd.getItemId(), MsgEventType.LIVE_SPEECH_AUDIT_LEGAL, 0L, "话术1"));
                qwMsgLiveVerbalToLegal(cmd.getItemId(), item.getCode(), itemDrawer.getStandardName());
            }
            final String msg = cmd.getSubmit() ? "直播话术变更，提交审核" : "直播话术变更";
            operateLogGateway.addOperatorLog(
                    cmd.getUid(),
                    OperateLogTarget.ITEM_DRAWER,
                    itemDrawer.getId(),
                    msg,
                    new ChangePropertyObj[]{
                            new ChangePropertyObj(
                                    ItemDrawerChangeEnum.LIVE_VERBAL_TRICK.getDesc(),
                                    liveVerbalTrickL,
                                    liveVerbalTrickR),
                            new ChangePropertyObj(
                                    ItemDrawerChangeEnum.LIVE_IMAGE.getDesc(),
                                    convertImgInfos(imagesL),
                                    convertImgInfos(itemDrawerImages))
                    });
        } else if (cmd.getSubmit()) {
            itemDrawerService
                    .lambdaUpdate()
                    .eq(Entity::getId, itemDrawer.getId())
                    .set(ItemDrawer::getLiveVerbalTrickStatus, ItemAuditStatus.WAIT_LEGAL_AUDIT)
                    .update();
            if (isReAudit) {
                final ItemDrawerModuleAuditTaskVO activeTask =
                        itemDrawerAuditBizService.getActiveTask(
                                ItemAuditType.LIVE_VERBAL_TRICK,
                                UserContext.getUserId(),
                                itemDrawer.getItemId(),
                                ItemLaunchProcessNodeId.MODIFY);
                if (activeTask == null) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "您并非当前节点的经办人");
                }
                itemDrawerAuditBizService.complete(
                        UserContext.getUserId(),
                        Long.valueOf(activeTask.getId()),
                        Collections.emptyList(),
                        true);
                operateLogGateway.addOperatorLog(
                        cmd.getUid(),
                        OperateLogTarget.ITEM_DRAWER,
                        itemDrawer.getId(),
                        "直播话术提交复审",
                        null);
            } else {
                itemDrawerAuditBizService.createProcess(
                        ItemAuditType.LIVE_VERBAL_TRICK,
                        cmd.getUid(),
                        cmd.getItemId(),
                        ItemDrawerModuleId.LIVE_VERBAL_TRICK);
                operateLogGateway.addOperatorLog(
                        cmd.getUid(),
                        OperateLogTarget.ITEM_DRAWER,
                        itemDrawer.getId(),
                        "直播话术提交审核",
                        null);
            }
            // 话术提交法务审核，发送消息
//            EventBusUtil.post(
//                    MsgEvent.ofLiveVerbalTrickAuditCompleted(
//                            cmd.getItemId(), MsgEventType.LIVE_SPEECH_AUDIT_LEGAL, 0L, "话术1"));
            qwMsgLiveVerbalToLegal(cmd.getItemId(), item.getCode(), itemDrawer.getStandardName());
        }
        return Response.buildSuccess();
    }

    @NonNull
    private static List<ImgInfo> convertImgInfos(List<ItemDrawerImage> images) {
        return images.stream()
                .map(v -> ImgInfo.of(v.getFilename(), v.getUrl()))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Response rollbackLiveVerbalTrick(Long userId, Long itemId, Long verbalTrickId) {
        boolean isNewVersion = Objects.nonNull(verbalTrickId) && verbalTrickId != 0L;

        ItemDrawerLiveVerbal itemDrawerLiveVerbal = null;
        ItemAuditStatus liveVerbalTrickStatus;
        final ItemDrawer itemDrawer = getItemDrawer(itemId);
        if (isNewVersion) {
            itemDrawerLiveVerbal = iItemDrawerLiveVerbalService.getById(verbalTrickId);
            Assert.notNull(itemDrawerLiveVerbal, "话术直播ID非法");
            liveVerbalTrickStatus = itemDrawerLiveVerbal.getLiveVerbalTrickStatus();
        } else {
            liveVerbalTrickStatus = itemDrawer.getLiveVerbalTrickStatus();
        }
        if (liveVerbalTrickStatus == ItemAuditStatus.NONE
                || liveVerbalTrickStatus == ItemAuditStatus.FINISHED) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前状态不允许撤回");
        }

//        final Optional<ItemDrawerModuleAudit> itemDrawerModuleAudit = itemDrawerModuleAuditService.getItemDrawerModuleAudit(
//                ItemAuditType.LIVE_VERBAL_TRICK, verbalTrickId, itemId);
        ItemAuditStatus rollbackTo;
        if (liveVerbalTrickStatus == ItemAuditStatus.WAIT_LEGAL_AUDIT) {
            rollbackTo = ItemAuditStatus.NONE;
        } else if (liveVerbalTrickStatus == ItemAuditStatus.WAIT_QC_AUDIT) {
            rollbackTo = ItemAuditStatus.WAIT_LEGAL_AUDIT;
        } else {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前状态异常");
        }
        if (isNewVersion) {
            itemDrawerLiveVerbal.setLiveVerbalTrickStatus(rollbackTo);
            iItemDrawerLiveVerbalService.updateById(itemDrawerLiveVerbal);
        } else {
            itemDrawerService.lambdaUpdate().eq(Entity::getId, itemDrawer.getId())
                    .set(ItemDrawer::getLiveVerbalTrickStatus, rollbackTo)
                    .update();
        }

        if (rollbackTo == ItemAuditStatus.NONE) {
            itemDrawerAuditBizService.terminal(ItemAuditType.LIVE_VERBAL_TRICK, verbalTrickId, userId, itemId);
        } else {
            itemDrawerAuditBizService.rollback(ItemAuditType.LIVE_VERBAL_TRICK, verbalTrickId, itemId, rollbackTo);
        }
        final boolean hasOldData = StringUtil.isNotBlank(itemDrawer.getLiveVerbalTrick());
        operateLogGateway.addOperatorLog(userId,
                                         OperateLogTarget.ITEM_DRAWER,
                                         itemDrawer.getId(),
                                         String.format("直播话术（%s）状态从【%s】回退到【%s】",
                                                       iItemDrawerLiveVerbalService.getName(hasOldData, itemDrawerLiveVerbal),
                                                       liveVerbalTrickStatus.getDesc(),
                                                       rollbackTo.getDesc()),
                                         MapBuilder.create().put("liveVerbalTrickId", verbalTrickId).build());

        // 复制状态到合并商品
        copyStatusInMerge(itemId);

        return Response.buildSuccess();
    }

    @Override
    public SingleResponse<PartnerItemResp> psysItemInfo(Long itemId) {
        final String partnerProviderItemSn = itemService.getPartnerProviderItemSn(itemId);
        if (StringUtil.isBlank(partnerProviderItemSn)) {
            return SingleResponse.of(null);
        }

        final PartnerItemReq req = new PartnerItemReq();
        req.setSearchType(1);
        req.setContext(partnerProviderItemSn);
        req.setPageIndex(1);
        req.setPageSize(10);
        final Rsp<List<PartnerItemResp>> itemQueryRsp = partnerFeignClient.itemQuery(req);
        if (Objects.isNull(itemQueryRsp) || CollUtil.isEmpty(itemQueryRsp.getData())) {
            throw ExceptionPlusFactory
                    .bizException(ErrorCode.DATA_NOT_FOUND,
                            "商品可能关联了无效的P系统商品款号：" + partnerProviderItemSn + "，未能在P系统查询到对应商品信息");
        }
        return SingleResponse.of(itemQueryRsp.getData().get(0));
    }

    @Override
    public void delLiveVerbalTrick(Long itemId, Long liveVerbalTrickId) {
        ItemDrawer itemDrawer = getItemDrawer(itemId);
        if (Objects.nonNull(liveVerbalTrickId) && liveVerbalTrickId != 0) {
            ItemDrawerLiveVerbal itemDrawerLiveVerbal = iItemDrawerLiveVerbalService.getById(liveVerbalTrickId);
            iItemDrawerLiveVerbalService.removeById(liveVerbalTrickId);
            operateLogGateway.addOperatorLog(
                    UserContext.getUserId(),
                    OperateLogTarget.ITEM_DRAWER,
                    itemDrawer.getId(),
                    StrUtil.format(
                            "删除直播话术（{}）",
                            iItemDrawerLiveVerbalService.getName(
                                    StringUtil.isNotEmpty(itemDrawer.getLiveVerbalTrick()),
                                    itemDrawerLiveVerbal)),
                    null);
        } else {
            itemDrawerService.lambdaUpdate()
                    .set(ItemDrawer::getLiveVerbalTrickStatus, null)
                    .set(ItemDrawer::getLiveVerbalTrick, StrUtil.EMPTY)
                    .eq(ItemDrawer::getItemId, itemId)
                    .update();
            operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.ITEM_DRAWER,
                    itemDrawer.getId(), "删除直播话术（话术1）", null);
        }
    }

    @Override
    public Boolean launchReview(Long userId, LaunchReviewForm launchReviewForm) {
        Long itemId = launchReviewForm.getItemId();
        Integer tabType = launchReviewForm.getTabType();
        Item item = itemService.getById(itemId);
        if (Objects.isNull(item)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "商品不存在");
        }
        final ItemDrawer itemDrawer = itemDrawerService.getByItemId(itemId)
                                                       .orElseThrow(() -> ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND,
                                                                                                            "商品抽屉不存在"));
        if (!TO_BE_UPDATED.getValue().equals(item.getLaunchStatus()) && !TO_BE_RELEASED.getValue().equals(item.getLaunchStatus())
                && !HAS_BE_RELEASED.getValue().equals(item.getLaunchStatus()) && !DOWN.getValue().equals(item.getLaunchStatus())) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "商品状态不允许发起复审");
        }
        ItemDrawerTabTypeEnum itemDrawerTabTypeEnum = ItemDrawerTabTypeEnum.of(tabType);
        if (Objects.isNull(itemDrawerTabTypeEnum)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "tab类型不正确");
        }

        itemService.reAudit(itemId);

        itemDrawerAuditBizService.createProcess(itemDrawerTabTypeEnum.getAuditType(), userId, item.getId(), ItemDrawerModuleId.values());
        //添加变更日志
        operateLogGateway.addOperatorLog(userId, OperateLogTarget.ITEM_DRAWER, itemDrawer.getId(), "手动触发审核流程", null);

        // 发送企微消息
        noticeSubmitReview(item, userId, itemDrawerTabTypeEnum);

        // 复制状态到合并商品
        copyStatusInMerge(itemId);

        return Boolean.TRUE;
    }

    /**
     * 发送企微消息
     *
     * @param item    Item
     * @param userId  Long
     * @param tabType Integer
     * @date 2024/4/11 15:20
     * <AUTHOR>
     */
    private void noticeSubmitReview(Item item, Long userId, ItemDrawerTabTypeEnum tabType) {
        NewGoods newGoods = newGoodsBizService.getNewGoods(item.getId());
        if (Objects.isNull(newGoods)) {
            return;
        }
        // 上新计划
        ItemLaunchPlan itemLaunchPlan = itemLaunchPlanService.getPlanByItemId(item.getId());

        List<Long> receiverIds = new ArrayList<>();
        // 获取发送人
        if (Objects.isNull(newGoods.getLegalId()) || Objects.equals(newGoods.getLegalId(), 0L)) {
            // 获取全部法务
            receiverIds = recipientUtil.getAllLegalUserId();
        } else {
            receiverIds.add(newGoods.getLegalId());
        }

        DadStaffVO staff = staffService.getStaff(userId);
        String title = null;
        String content = String.format("上新日期%s", Optional.ofNullable(itemLaunchPlan)
                .map(launchPlan -> DateUtil.format(launchPlan.getLaunchTime())).orElse(""));
        switch (tabType) {
            case ITEM_INFO:
                title = String.format("%s已提交%s信息重新审核，请尽快处理！", Optional.ofNullable(staff).map(DadStaffVO::getNickname).orElse(""), item.getName());
                break;
            default:
                break;
        }
        if (title == null || receiverIds.isEmpty()) {
            return;
        }
//        rocketMQProducer.syncSend(QwMessageDTO.of(receiverIds, title, content, String.format(recipientConfig.getNewGoodLink(), item.getId(), tabType)), MQTopic.QW_MESSAGE_PUSH);
        final String operatorNick = Optional.ofNullable(staff).map(DadStaffVO::getNickname).orElse("");
        final Optional<ItemLaunchStatus> launchStatus = IEnum.getEnumOptByValue(ItemLaunchStatus.class,
                item.getLaunchStatus());
        final List<StaffBrief> receiverUsers = StaffAssembler.INST.longListToStaffBriefList(receiverIds);
        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("产品负责人花名", operatorNick);
        variables.put("商品名称", item.getName());
        variables.put("商品编码", item.getCode());
        variables.put("状态", launchStatus.map(ItemLaunchStatus::getDesc).orElse("?"));
        variables.put("法务负责人", receiverUsers.stream().map(StaffBrief::getQwUserId).collect(Collectors.joining(",")));
        variables.put("itemId", item.getId());

        QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.ITEM_LAUNCH_REVIEW, variables);
    }


    @Override
    public void copyDrawer(Long sourceDrawerId, Long targetDrawerId) {
        ItemDrawer sourceDrawer = itemDrawerService.getById(sourceDrawerId);
        if (Objects.isNull(sourceDrawer)) {
            return;
        }
        ItemDrawer targetDrawer = itemDrawerService.getById(targetDrawerId);
        if (Objects.isNull(targetDrawer)) {
            return;
        }
        copyDrawerToDrawer(sourceDrawer, targetDrawer);
    }


    @Override
    public void copyDrawerToDrawer(ItemDrawer sourceDrawer, ItemDrawer targetDrawer) {
        // 基础数据
        BeanUtil.copyProperties(sourceDrawer, targetDrawer,
                CopyOptions.create().setIgnoreProperties(ItemDrawer::getId, ItemDrawer::getItemId, ItemDrawer::getCreatedAt));
        itemDrawerService.updateById(targetDrawer);

        // 其他数据的复制
        itemDrawerCopyServices.forEach(itemDrawerCopyService -> itemDrawerCopyService.handler(sourceDrawer, targetDrawer));
    }

    @Override
    public void copyDrawerToItemIds(Long sourceDrawerId, List<Long> itemIds) {
        ItemDrawer sourceDrawer = itemDrawerService.getById(sourceDrawerId);
        if (Objects.isNull(sourceDrawer)) {
            return;
        }
        itemIds.stream().filter(itemId -> !sourceDrawer.getItemId().equals(itemId))
                .map(this::getItemDrawer)
                .forEach(targetDrawer -> copyDrawerToDrawer(sourceDrawer, targetDrawer));
    }

    @Override
    public ItemDrawer getById(Long id) {
        return itemDrawerService.getById(id);
    }

    @Override
    public void noticeToBeDesigned(Long itemId, Long operatorId) {
        final ItemDrawer itemDrawer = itemDrawerService.getByItemId(itemId)
                .orElseThrow(() -> ExceptionPlusFactory.bizException(
                        ErrorCode.DATA_NOT_FOUND,
                        "抽屉信息查询异常"));
        final ItemDrawerVO itemDrawerVO = getItemDrawerVO(itemDrawer);
        final List<ItemLaunchProgressNode> launchProgressNodes = getLaunchProgressNodes(itemService.getById(itemId),
                itemDrawerVO,
                itemDrawer);
        launchProgressNodes.stream()
                .filter(v -> TO_BE_DESIGNED.getDesc().equals(v.getTitle()))
                .findFirst()
                .ifPresent(v -> {
                    final HashMap<String, Object> variables = new HashMap<>();
                    final String buyerNick = itemDrawerVO.getBuyer().getNickname();
                    variables.put("采购负责人花名", buyerNick);
                    variables.put("商品名称", itemDrawerVO.getItemName());
                    variables.put("上新计划日期", itemDrawerVO.getLaunchDate());
                    variables.put("产品负责人",
                            v.getProcessors().stream().map(DadStaffVO::getQwUserId).collect(
                                    Collectors.joining(",")));
                    variables.put("itemId", itemId);
                    QyMsgSendUtils.send(RemindType.TO_DO_REMINDER,
                            MsgTemplateCode.ITEM_LAUNCH_TO_DESIGN,
                            variables);
                });
    }

    @Override
    public void noticeToLegal(Long itemId, Long operatorId) {
        final ItemDrawer itemDrawer = itemDrawerService.getByItemId(itemId)
                .orElseThrow(() -> ExceptionPlusFactory.bizException(
                        ErrorCode.DATA_NOT_FOUND,
                        "抽屉信息查询异常"));
        final ItemDrawerVO itemDrawerVO = getItemDrawerVO(itemDrawer);
        final List<ItemLaunchProgressNode> launchProgressNodes = getLaunchProgressNodes(itemService.getById(itemId),
                itemDrawerVO,
                itemDrawer);
        final StaffBrief staffBrief = StaffAssembler.INST.toStaffBrief(operatorId);
        launchProgressNodes.stream()
                .filter(v -> "待法务审核".equals(v.getTitle()))
                .findFirst()
                .ifPresent(v -> {
                    final HashMap<String, Object> variables = new HashMap<>();
                    variables.put("产品负责人花名", staffBrief.getNickname());
                    variables.put("商品名称", itemDrawerVO.getItemName());
                    variables.put("上新计划日期", itemDrawerVO.getLaunchDate());
                    variables.put("法务负责人",
                            v.getProcessors().stream().map(DadStaffVO::getQwUserId).collect(
                                    Collectors.joining(",")));
                    variables.put("itemId", itemId);
                    QyMsgSendUtils.send(RemindType.TO_DO_REMINDER,
                            MsgTemplateCode.ITEM_LAUNCH_TO_LEGAL,
                            variables);
                });
    }

    @Override
    public void noticeToQc(Long itemId, Long operatorId) {
        final ItemDrawer itemDrawer = itemDrawerService.getByItemId(itemId)
                .orElseThrow(() -> ExceptionPlusFactory.bizException(
                        ErrorCode.DATA_NOT_FOUND,
                        "抽屉信息查询异常"));
        final ItemDrawerVO itemDrawerVO = getItemDrawerVO(itemDrawer);
        final List<ItemLaunchProgressNode> launchProgressNodes = getLaunchProgressNodes(itemService.getById(itemId),
                itemDrawerVO,
                itemDrawer);
        final StaffBrief staffBrief = StaffAssembler.INST.toStaffBrief(operatorId);
        launchProgressNodes.stream()
                .filter(v -> "待QC审核".equals(v.getTitle()))
                .findFirst()
                .ifPresent(v -> {
                    final HashMap<String, Object> variables = new HashMap<>();
                    variables.put("法务负责人花名", staffBrief.getNickname());
                    variables.put("商品名称", itemDrawerVO.getItemName());
                    variables.put("上新计划日期", itemDrawerVO.getLaunchDate());
                    variables.put("QC负责人",
                            v.getProcessors().stream().map(DadStaffVO::getQwUserId).collect(
                                    Collectors.joining(",")));
                    variables.put("itemId", itemId);

                    QyMsgSendUtils.send(RemindType.TO_DO_REMINDER,
                            MsgTemplateCode.ITEM_LAUNCH_TO_QC,
                            variables);
                });
    }


    @Override
    public void noticeToUpdate(Long itemId, Long operatorId) {
        final ItemDrawer itemDrawer = itemDrawerService.getByItemId(itemId)
                .orElseThrow(() -> ExceptionPlusFactory.bizException(
                        ErrorCode.DATA_NOT_FOUND,
                        "抽屉信息查询异常"));
        final ItemDrawerVO itemDrawerVO = getItemDrawerVO(itemDrawer);
        final List<ItemLaunchProgressNode> launchProgressNodes = getLaunchProgressNodes(itemService.getById(itemId),
                itemDrawerVO,
                itemDrawer);
        launchProgressNodes.stream()
                .filter(v -> "待修改".equals(v.getTitle()))
                .findFirst()
                .ifPresent(v -> {
                    final HashMap<String, Object> variables = new HashMap<>();
                    variables.put("商品名称", itemDrawerVO.getItemName());
                    variables.put("上新计划日期", itemDrawerVO.getLaunchDate());
                    variables.put("相关负责人",
                            v.getProcessors().stream().map(DadStaffVO::getQwUserId).collect(
                                    Collectors.joining(",")));
                    variables.put("itemId", itemId);

                    QyMsgSendUtils.send(RemindType.TO_DO_REMINDER,
                            MsgTemplateCode.ITEM_LAUNCH_TO_UPDATE,
                            variables);
                });
    }

    @Override
    public void recoverToBeforeStatus(Long itemId) {
        Item item = itemService.getById(itemId);
        if (item == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "商品不存在");
        }
        ItemDrawerMergeItem itemDrawerMergeItem =
                itemDrawerMergeBizService.getItemDrawerMergeItem(item.getId());
        if (itemDrawerMergeItem != null) {
            List<ItemDrawerMergeItem> itemDrawerMergeItems =
                    itemDrawerMergeBizService.getItemDrawerMergeItemListByMergeId(
                            itemDrawerMergeItem.getMergeId());
            List<Long> itemIds =
                    itemDrawerMergeItems.stream()
                            .map(ItemDrawerMergeItem::getItemId)
                            .collect(Collectors.toList());
            List<Item> items = itemService.listByIds(itemIds);
            for (Item item1 : items) {
                if (!Objects.equals(item1.getLaunchStatus(), DOWN.getValue())) {
                    continue;
                }
                ItemLaunchStatus beforeLaunchStatusEnum = IEnum.getEnumByValue(ItemLaunchStatus.class, item1.getBeforeLaunchStatus());
                if (beforeLaunchStatusEnum == null || beforeLaunchStatusEnum == ItemLaunchStatus.NON_SELECTIVITY) {
                    continue;
                }
                Integer auditStatus = item1.getAuditStatus();
                ItemAuditStatus auditStatusEnum = IEnum.getEnumByValue(ItemAuditStatus.class, auditStatus);

                SetLaunchStatusCmd cmd = new SetLaunchStatusCmd();
                cmd.setItemId(item1.getId());
                cmd.setLaunchStatus(beforeLaunchStatusEnum);
                cmd.setAuditStatus(auditStatusEnum);
                cmd.setAuditType(ItemAuditType.ITEM_MATERIAL);
                setLaunchStatus(cmd);

                operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.NEW_GOODS_SPU,
                        itemId, String.format("商品上新状态还原到下架前状态[%s]", beforeLaunchStatusEnum.getDesc()), null);
            }
        } else {
            if (!Objects.equals(item.getLaunchStatus(), DOWN.getValue())) {
                throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "商品状态当前状态无法还原");
            }
            ItemLaunchStatus beforeLaunchStatusEnum = IEnum.getEnumByValue(ItemLaunchStatus.class, item.getBeforeLaunchStatus());
            if (beforeLaunchStatusEnum == null || beforeLaunchStatusEnum == ItemLaunchStatus.NON_SELECTIVITY) {
                throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "商品未记录下架前状态，无法还原");
            }
            Integer auditStatus = item.getAuditStatus();
            ItemAuditStatus auditStatusEnum = IEnum.getEnumByValue(ItemAuditStatus.class, auditStatus);

            SetLaunchStatusCmd cmd = new SetLaunchStatusCmd();
            cmd.setItemId(item.getId());
            cmd.setLaunchStatus(beforeLaunchStatusEnum);
            cmd.setAuditStatus(auditStatusEnum);
            cmd.setAuditType(ItemAuditType.ITEM_MATERIAL);
            setLaunchStatus(cmd);

            operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.NEW_GOODS_SPU,
                    itemId, String.format("商品上新状态还原到下架前状态【%s】", beforeLaunchStatusEnum.getDesc()), null);
        }
    }

    @Override
    public void copyStatusInMerge(Long itemId) {
        ItemDrawerMerge itemDrawerMerge = itemDrawerMergeBizService.getItemDrawerMerge(itemId);
        if (itemDrawerMerge != null) {
            List<ItemDrawerMergeItem> itemDrawerMergeItems = itemDrawerMergeBizService.getItemDrawerMergeItemListByMergeId(itemDrawerMerge.getId());
            copyStatus(itemDrawerMerge.getItemId(), itemDrawerMergeItems.stream().map(ItemDrawerMergeItem::getItemId).collect(Collectors.toList()));
        }
    }

    @Override
    public void copyStatus(Long mainItemId, List<Long> itemIds) {
        if (!itemIds.contains(mainItemId)) {
            itemIds.add(mainItemId);
        }
        List<Item> items = itemService.listByIds(itemIds);
        Item main = items.stream()
                .filter(item -> item.getId().equals(mainItemId))
                .findFirst()
                .orElseThrow(
                        () ->
                                ExceptionPlusFactory.bizException(
                                        ErrorCode.DATA_NOT_FOUND, "商品信息查询异常"));
        ItemDrawer mainItemDrawer = itemDrawerService.getByItemId(mainItemId).orElse(null);
        ItemTrainingMaterials mainTrainingMaterial = itemTrainingMaterialsService.getByItemId(mainItemId);

        for (Item item : items) {
            if (main == item) {
                continue;
            }
            operateLogGateway.addOperatorLog(
                    UserContext.getUserId(),
                    OperateLogTarget.ITEM_STATUS,
                    item.getId(),
                    String.format(
                            "状态变动，同步到所有合并上新的商品，主商品 %s",
                            main.getSupplierCode()),
                    MapBuilder.create().put("sourceItem", main).put("targetItem", item).build());
            item.setLaunchStatus(main.getLaunchStatus());
            item.setAuditStatus(main.getAuditStatus());
            item.setBeforeLaunchStatus(main.getBeforeLaunchStatus());
            item.setStatus(main.getStatus());
            item.setDownFrameTime(main.getDownFrameTime());
            item.setDownFrameReason(main.getDownFrameReason());
            itemService.updateById(item);

            if (mainItemDrawer != null) {
                ItemDrawer itemDrawer = itemDrawerService.getByItemId(item.getId()).orElse(null);
                if (itemDrawer != null) {
                    itemDrawer.setLiveVerbalTrickStatus(
                            mainItemDrawer.getLiveVerbalTrickStatus());
                    itemDrawer.setLaunchSubmitStatus(
                            mainItemDrawer.getLaunchSubmitStatus());
                    itemDrawer.setCoopUid(mainItemDrawer.getCoopUid());
                    itemDrawerService.updateById(itemDrawer);
                } else {
                    ItemDrawer newItemDrawer = new ItemDrawer();
                    newItemDrawer.setStandardName(mainItemDrawer.getStandardName());
                    newItemDrawer.setTbTitle(mainItemDrawer.getTbTitle());
                    newItemDrawer.setMiniTitle(mainItemDrawer.getMiniTitle());
                    newItemDrawer.setHomeCopy(mainItemDrawer.getHomeCopy());
                    newItemDrawer.setShelfTime(mainItemDrawer.getShelfTime());
                    newItemDrawer.setItemId(item.getId());
                    newItemDrawer.setDouLink(mainItemDrawer.getDouLink());
                    newItemDrawer.setDouId(mainItemDrawer.getDouId());
                    newItemDrawer.setWechatId(mainItemDrawer.getWechatId());
                    newItemDrawer.setTbLink(mainItemDrawer.getTbLink());
                    newItemDrawer.setWechatLink(mainItemDrawer.getWechatLink());
                    newItemDrawer.setTbId(mainItemDrawer.getTbId());
                    newItemDrawer.setType(mainItemDrawer.getType());
                    newItemDrawer.setLiveVerbalTrick(mainItemDrawer.getLiveVerbalTrick());
                    newItemDrawer.setLiveVerbalTrickStatus(mainItemDrawer.getLiveVerbalTrickStatus());
                    newItemDrawer.setLaunchSubmitStatus(mainItemDrawer.getLaunchSubmitStatus());
                    newItemDrawer.setCoopUid(mainItemDrawer.getCoopUid());
                    newItemDrawer.setDouTitle(mainItemDrawer.getDouTitle());
                    newItemDrawer.setMiniRedBookId(mainItemDrawer.getMiniRedBookId());
                    newItemDrawer.setMiniRedBookLink(mainItemDrawer.getMiniRedBookLink());
                    newItemDrawer.setDouYinLinkContent(mainItemDrawer.getDouYinLinkContent());
                    newItemDrawer.setKuaiShouLinkContent(mainItemDrawer.getKuaiShouLinkContent());
                    newItemDrawer.setCreatedAt(DateUtil.currentTime());
                    newItemDrawer.setCreatedUid(UserContext.getUserId());
                    newItemDrawer.setUpdatedAt(0L);
                    newItemDrawer.setUpdatedUid(0L);
                    itemDrawerService.save(newItemDrawer);
                }
            }

            if (mainTrainingMaterial != null) {
                ItemTrainingMaterials trainingMaterials = itemTrainingMaterialsService.getByItemId(item.getId());
                if (trainingMaterials != null) {
                    trainingMaterials.setStatus(mainTrainingMaterial.getStatus());
                    itemTrainingMaterialsService.updateById(trainingMaterials);
                } else {
                    ItemTrainingMaterials newTrainingMaterials = new ItemTrainingMaterials();
                    newTrainingMaterials.setV(mainTrainingMaterial.getV());
                    newTrainingMaterials.setCreatedAt(DateUtil.currentTime());
                    newTrainingMaterials.setCreatedUid(UserContext.getUserId());
                    newTrainingMaterials.setUpdatedAt(0L);
                    newTrainingMaterials.setUpdatedUid(0L);
                    newTrainingMaterials.setItemId(item.getId());
                    newTrainingMaterials.setData(mainTrainingMaterial.getData());
                    newTrainingMaterials.setProcessId(mainTrainingMaterial.getProcessId());
                    newTrainingMaterials.setStatus(mainTrainingMaterial.getStatus());
                    newTrainingMaterials.setWikiContentId(mainTrainingMaterial.getWikiContentId());
                    itemTrainingMaterialsService.save(newTrainingMaterials);
                }
            }
        }
    }
}
