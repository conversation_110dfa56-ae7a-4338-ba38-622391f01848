package com.daddylab.supplier.item.application.purchase.combinationPrice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年04月04日 11:33 AM
 */
@Data
@ApiModel("价格详情数据结构")
public class PriceDetailDto {

    @ApiModelProperty("价格编码")
    private String code;

    @ApiModelProperty("数量")
    @NotNull(message = "价格数量不得为空")
    private Integer quantity;

    @ApiModelProperty("价格值")
    @NotNull(message = "价格值不为空")
    private BigDecimal price;

}
