package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品发货渠道。0.仓库发货。1.工厂发货。warehouse factory
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/18 11:06 上午
 * @description
 */
@Getter
@AllArgsConstructor
public enum ItemDelivery implements IEnum<Integer> {

    /**
     * 仓库发货
     */
    WAREHOUSE(0, "仓库发货"),

    /**
     * 2:工厂发货
     */
    FACTORY(1, "工厂发货"),

    ALL(2, "仓库发货,工厂发货"),
    ;

    @EnumValue
    private final Integer value;

    private final String desc;


    public static ItemDelivery convert(Integer type) {
        return IEnum.getEnumByValue(ItemDelivery.class, type);
    }
    public static String valueStrToDesc(String type) {
        if (StringUtil.isBlank(type)) {
            return "";
        }
        return IEnum.getEnumOptByValue(ItemDelivery.class, Integer.parseInt(type))
                .map(ItemDelivery::getDesc).orElse("");
    }

}
