package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailWrapperMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderDetailWrapperService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Service
public class WdtOrderDetailWrapperServiceImpl extends DaddyServiceImpl<WdtOrderDetailWrapperMapper, WdtOrderDetailWrapper> implements IWdtOrderDetailWrapperService {

    @Override
    public List<WdtOrderDetailWrapper> getListBySkuCode(List<String> skuCodeList, String operateTime) {
        LambdaQueryWrapper<WdtOrderDetailWrapper> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(WdtOrderDetailWrapper::getOperateTime, operateTime).in(WdtOrderDetailWrapper::getSkuCode, skuCodeList);
        return this.list(wrapper);
    }

    @Override
    public void append(WdtOrderDetailWrapper wrapper) {
        this.save(wrapper);
//        Integer count = this.lambdaQuery()
//                .eq(WdtOrderDetailWrapper::getSkuCode, wrapper.getSkuCode())
//                .eq(WdtOrderDetailWrapper::getPrice, wrapper.getPrice())
//                .eq(WdtOrderDetailWrapper::getType, wrapper.getType())
//                .select().count();
//        if (count > 0) {
//            Optional<WdtOrderDetailWrapper> wdtOrderDetailWrapper = this.lambdaQuery()
//                    .eq(WdtOrderDetailWrapper::getSkuCode, wrapper.getSkuCode())
//                    .eq(WdtOrderDetailWrapper::getPrice, wrapper.getPrice())
//                    .eq(WdtOrderDetailWrapper::getType, wrapper.getType())
//                    .select().oneOpt();
//            WdtOrderDetailWrapper wrapper1 = wdtOrderDetailWrapper.get();
//            wrapper1.setQuantity(wrapper1.getQuantity() + wrapper.getQuantity());
//            this.updateById(wrapper1);
//        } else {
//            this.save(wrapper);
//        }
    }
}
