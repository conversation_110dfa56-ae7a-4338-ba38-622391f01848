package com.daddylab.supplier.item.application.offShelf.dto;

import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OffShelfStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OffShelfReasonType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OffShelfUrgentLevel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class OffShelfBatchProcessFeedbackItem {
    @ApiModelProperty("处理反馈ID")
    private Long feedbackId;

    @ApiModelProperty("下架流程ID")
    private Long offShelfId;

    /**
     * 下架流程编码
     */
    @ApiModelProperty("流程编号")
    private String no;

    @ApiModelProperty("下架状态")
    private OffShelfStatus status;

    /**
     * 下架理由
     */
    @ApiModelProperty("原因描述")
    private String reasonTxt;

    @ApiModelProperty("下架原因")
    private List<OffShelfReasonType> reasonType;

    @ApiModelProperty("紧急程度")
    private OffShelfUrgentLevel urgentLevel;

    @ApiModelProperty("下架商品编码")
    private List<OffShelfItemVO> itemList;

    @ApiModelProperty(value = "下架链接", notes = "用户填写")
    private String link;


}
