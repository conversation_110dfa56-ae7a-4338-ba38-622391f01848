package com.daddylab.supplier.item.application.afterSalesRegister;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.daddylab.supplier.item.common.easyexcel.CollectionToCommaSeperaterStringConverter;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/21
 */
@Data
@ApiModel("售后登记分页查询结果对象")
public class AfterSalesRegisterPageVO {
    @ApiModelProperty("结算周期")
    @ExcelIgnore
    String settleTimeStart;

    @ApiModelProperty("结算周期")
    @ExcelIgnore
    String settleTimeEnd;

    @ApiModelProperty("结算周期")
    @ExcelProperty("结算周期")
    String settleMonth;

//    @ApiModelProperty("合作模式")
//    @ExcelProperty("合作模式")
//    String businessLineStr;

    @ApiModelProperty("订单号")
    @ExcelProperty("订单号")
    String orderNo;

    @ApiModelProperty("付款时间")
    @ExcelProperty("付款时间")
    String payTime;

    @ApiModelProperty("店铺")
    @ExcelProperty("店铺")
    String shopName;

    @ApiModelProperty("商品编码")
    @ExcelProperty("商品编码")
    String itemCode;

    @ApiModelProperty("商品名称")
    @ExcelProperty("商品名称")
    String itemName;

    @ApiModelProperty("商品规格")
    @ExcelProperty("商品规格")
    String specifications;

    @ApiModelProperty("售后数量")
    @ExcelProperty("售后数量")
    String num;

    @ApiModelProperty("发货仓")
    @ExcelProperty("发货仓")
    String warehouse;

    @ApiModelProperty("工厂/仓库承担运费")
    @ExcelProperty("工厂/仓库承担运费")
    String factoryUndertakeFreight;

    @ApiModelProperty("工厂/仓库承担货款")
    @ExcelProperty("工厂/仓库承担货款")
    String factoryUndertakeGoodsAmount;

    @ApiModelProperty("工厂/仓库承担其他补偿")
    @ExcelProperty("工厂/仓库承担其他补偿")
    String factoryUndertakeOtherAmount;

    @ApiModelProperty("体验基金（优惠券）")
    @ExcelProperty("体验基金（优惠券）")
    String experienceFundCoupon;

    @ApiModelProperty("体验基金（现金）")
    @ExcelProperty("体验基金（现金）")
    String experienceFundCash;

    @ApiModelProperty("售后处理意见")
    @ExcelProperty("售后处理意见")
    @ColumnWidth(40)
    String handleAdvice;

    @ApiModelProperty("体验金承担原因(选项1级标题)")
    @ExcelProperty("体验金承担原因(选项1级标题)")
    @ColumnWidth(40)
    String experienceFundReason1;

    @ApiModelProperty("体验金承担原因(选项2级标题)")
    @ExcelProperty("体验金承担原因(选项2级标题)")
    @ColumnWidth(40)
    String experienceFundReason2;

    @ApiModelProperty("体验金承担原因(选项3级标题)")
    @ExcelProperty("体验金承担原因(选项3级标题)")
    @ColumnWidth(40)
    String experienceFundReason3;

    @ApiModelProperty("问题描述(选项1级标题)")
    @ExcelProperty("问题描述(选项1级标题)")
    @ColumnWidth(40)
    String desc1;

    @ApiModelProperty("问题描述(选项2级标题)")
    @ExcelProperty("问题描述(选项2级标题)")
    @ColumnWidth(40)
    String desc2;

    @ApiModelProperty("问题描述(选项3级标题)")
    @ExcelProperty("问题描述(选项3级标题)")
    @ColumnWidth(40)
    String desc3;

    @ApiModelProperty("仓库订单员，存在多个值")
    @ExcelIgnore
    String orderPersonnel;

//    @ApiModelProperty("合作方类型")
//    @ExcelIgnore
//    private List<Integer> corpType;
//
//    @ApiModelProperty("业务类型")
//    @ExcelIgnore
//    private List<Integer> bizType;


    @ExcelProperty("合作方和业务类型")
    private String corpBizTypeStr;


    @ApiModelProperty("业务类型(结构化数据，单SKU特有)")
    @ExcelIgnore
    private List<CorpBizTypeDTO> corpBizType;


    @ApiModelProperty("合作方(非结构化数据，组合装特有)")
    @ExcelIgnore
    private List<Integer> corpType;
    @ApiModelProperty("业务类型(非结构化数据，组合装特有)")
    @ExcelIgnore
    private List<Integer> bizType;


//    @ExcelIgnore
//    @ApiModelProperty("合作方/合作模式")
//    private Integer businessLine;

    @ApiModelProperty("相关图片")
    @ExcelProperty(value = "相关图片", converter = CollectionToCommaSeperaterStringConverter.class)
    private List<String> images;
}
