/*
package com.daddylab.supplier.item.application.platformItem.tasks;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.application.platformItem.PlatformItemStockSyncBizService;
import com.daddylab.supplier.item.application.platformItem.config.PlatformItemSyncConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.TimeScheduleType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.time.CalcArgs;
import com.daddylab.supplier.item.infrastructure.time.TimeWindows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.task.TaskExecutorBuilder;
import org.springframework.context.SmartLifecycle;
import org.springframework.core.task.TaskRejectedException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

*/
/**
 * <AUTHOR>
 * @since 2024/3/29
 *//*

@Service
@Slf4j
public class PlatformInventoryUploadTask implements SmartLifecycle {
//    @Autowired
//    private IShopInventorySettingService shopInventorySettingService;
    @Autowired
    private ISysInventorySettingService sysInventorySettingService;
    @Autowired
    private IShopService shopService;
    @Autowired
    private IPlatformItemService platformItemService;
    @Autowired
    private PlatformItemStockSyncBizService platformItemStockSyncBizService;
    @Autowired
    private ITimeScheduleService timeScheduleService;
    @Autowired
    private PlatformItemSyncConfig platformItemSyncConfig;

    @Autowired
    private RedissonClient redissonClient;

    private ThreadPoolTaskExecutor taskExecutor;

    public synchronized ThreadPoolTaskExecutor getTaskExecutor() {
        if (taskExecutor == null) {
            taskExecutor = new TaskExecutorBuilder().corePoolSize(8)
                                                    .maxPoolSize(16)
                                                    .threadNamePrefix("inventory-up-worker")
                                                    .queueCapacity(256)
                                                    .build();
            taskExecutor.initialize();
        }
        return taskExecutor;
    }

    private ThreadPoolTaskExecutor shopExecutor;

    public synchronized ThreadPoolTaskExecutor getShopExecutor() {
        if (shopExecutor == null) {
            shopExecutor = new TaskExecutorBuilder().corePoolSize(2)
                                                    .maxPoolSize(4)
                                                    .threadNamePrefix("shop-up-worker")
                                                    .queueCapacity(16)
                                                    .build();
            shopExecutor.initialize();
        }
        return shopExecutor;
    }

    private void loop() {
        while (true) {
            if (!platformItemSyncConfig.isOpenUpload()) {
                ThreadUtil.sleep(30 * 1000);
                continue;
            }
            aLoop();
        }
    }

    private void aLoop() {
        final List<Shop> shops = shopService.lambdaQuery().eq(Shop::getAutoAllocateInventory, 1).list();
        final SysInventorySetting sysInventorySetting = sysInventorySettingService.getSysInventorySetting();
        final Integer permitShopSyncFreqSetting = sysInventorySetting.getPermitShopSyncFreqSetting();
//        final List<ShopInventorySetting> shopInventorySettings = shopInventorySettingService.list();
        final List<String> shopNos = shops.stream().map(Shop::getSn).collect(Collectors.toList());
        int minSyncFrequency = sysInventorySetting.getSyncFrequency();
        log.debug("[平台库存上传]开始执行 店铺列表={} 允许店铺自定义同步频率={} 系统同步频率={}",
                shopNos,
                permitShopSyncFreqSetting,
                sysInventorySetting.getSyncFrequency());
        final ThreadPoolTaskExecutor shopExecutor = getShopExecutor();
        final Semaphore semaphore = new Semaphore(platformItemSyncConfig.getShopConcurrent());
        final CountDownLatch countDownLatch = new CountDownLatch(shops.size());
        for (Shop shop : shops) {
//            Integer syncFrequency;
            if (permitShopSyncFreqSetting > 0) {
//                final ShopInventorySetting shopInventorySetting = shopInventorySettings
//                        .stream()
//                        .filter(v -> v.getShopId().equals(shop.getId())).findFirst().orElse(null);
//                if (!Objects.isNull(shopInventorySetting)) {
//                    syncFrequency = shopInventorySetting.getSyncFrequency();
//                } else {
//                    syncFrequency = sysInventorySetting.getSyncFrequency();
//                }
//            } else {
//                syncFrequency = sysInventorySetting.getSyncFrequency();
//            }
//            log.debug("[平台库存上传][店铺{}]进入执行队列 同步频率={}", shop.getSn(), syncFrequency);
//            try {
//                while (true) {
//                    if (semaphore.tryAcquire(1, TimeUnit.SECONDS)) {
//                        try {
//                            CompletableFuture.runAsync(() -> shopInventoryUpload(syncFrequency, shop), shopExecutor)
//                                             .thenRun(countDownLatch::countDown)
//                                             .thenRun(semaphore::release);
//                            break;
//                        } catch (TaskRejectedException ignored) {
//                            semaphore.release();
//                        }
//                    }
//                }
//            } catch (InterruptedException e) {
//                log.warn("[平台库存上传]执行中断", e);
//                return;
//            }
//            if (syncFrequency < minSyncFrequency) {
//                minSyncFrequency = syncFrequency;
//            }
        }
        try {
            if (!countDownLatch.await(10, TimeUnit.MINUTES)) {
                log.warn("[平台库存上传]等待超时");
            }
        } catch (InterruptedException e) {
            log.warn("[平台库存上传]等待同步完成中断", e);
        }
        log.debug("[平台库存上传]计算下一轮执行周期 minSyncFrequency={}", minSyncFrequency);
        long sleepMillis = platformItemSyncConfig.getMinSleep();
        if (minSyncFrequency > 0) {
            LocalDateTime nextTime = getNextTime(minSyncFrequency, 1);
            final long millis = ChronoUnit.MILLIS.between(LocalDateTime.now(), nextTime);
            if (millis > sleepMillis) {
                sleepMillis = Math.min(millis, platformItemSyncConfig.getMaxSleep());
            }
        }
        log.debug("[平台库存上传]休眠{}毫秒", sleepMillis);
        ThreadUtil.sleep(sleepMillis);
    }

    private LocalDateTime getNextTime(int minSyncFrequency, int windowOffset) {
        return TimeWindows.calculateWindowTime(CalcArgs.builder()
                                                       .syncFrequency(minSyncFrequency)
                                                       .windowOffset(windowOffset)
                                                       .startTimeOffset(platformItemSyncConfig.getSyncTimeStartOffset())
                                                       .build());
    }

    private void shopInventoryUpload(int syncFrequency, Shop shop) {
        if (!platformItemSyncConfig.isOpenUpload()) {
            return;
        }
        final RLock lock = redissonClient.getLock("AUTO_ALLOCATE_INVENTORY:" + shop.getSn());
        if (!lock.tryLock()) {
            return;
        }
        log.debug("[平台库存上传][店铺{}]执行 同步频率={}", shop.getSn(), syncFrequency);
        try {
            final LocalDateTime currentWindowTime = getNextTime(syncFrequency, 0);
            final String businessId = shop.getId().toString();
            TimeSchedule timeSchedule = timeScheduleService.getTimeSchedule(TimeScheduleType.PLATFORM_INVENTORY_UPLOAD,
                    businessId,
                    currentWindowTime);

            if (timeSchedule != null) {
                log.debug("[平台库存上传][店铺{}]{} 调度记录已存在", shop.getSn(), currentWindowTime);
                if (timeSchedule.getIsComplete()) {
                    log.debug("[平台库存上传][店铺{}]{} 已完成跳过", shop.getSn(), currentWindowTime);
                    return;
                }
            } else {
                timeSchedule = new TimeSchedule();
                timeSchedule.setType(TimeScheduleType.PLATFORM_INVENTORY_UPLOAD);
                timeSchedule.setBusinessId(businessId);
                timeSchedule.setScheduleTime(currentWindowTime);
                try {
                    timeScheduleService.save(timeSchedule);
                } catch (DuplicateKeyException ignored) {
                    log.debug("[平台库存上传][店铺{}]{} 调度记录重复跳过", shop.getSn(), currentWindowTime);
                    return;
                }
            }
            final LocalDateTime now = LocalDateTime.now();
            if (timeSchedule.getScheduleTime().isAfter(now)) {
                log.debug("[平台库存上传][店铺{}]未到调度时间 {}", shop.getSn(), timeSchedule.getScheduleTime());
                return;
            }
            final Duration tolerateOfOverdue = Duration.ofMinutes(10);
            if (now.isAfter(timeSchedule.getScheduleTime().plus(tolerateOfOverdue))) {
                return;
            }
            int pageIndex = 1;
            int pageSize = 100;
            final LambdaQueryChainWrapper<PlatformItem> itemQueryWrapper = platformItemService
                    .lambdaQuery()
                    .eq(PlatformItem::getShopNo, shop.getSn())
                    .eq(platformItemSyncConfig.isSkipOffSaleStatus(),
                            PlatformItem::getStatus,
                            PlatformItemStatus.ON_SALE)
                    .eq(platformItemSyncConfig.isSafeMode(), PlatformItem::getExplicitlySync, true);
            final Integer count = itemQueryWrapper.count();
            log.info("[平台库存上传][店铺{}]开始同步，当前窗口时间={}，同步频率={}，商品总数={}",
                    shop.getSn(),
                    currentWindowTime,
                    syncFrequency, count);
            while (true) {
                final Page<PlatformItem> page = new Page<>(pageIndex, pageSize, false);
                itemQueryWrapper.page(page);
                pageIndex++;
                if (page.getRecords().isEmpty()) {
                    break;
                }
                final CountDownLatch countDownLatch = new CountDownLatch(page.getRecords().size());
                for (PlatformItem platformItem : page.getRecords()) {
                    CompletableFuture.runAsync(() -> {
                        if (!platformItemSyncConfig.isOpenUpload()) {
                            return;
                        }
                        try {
                            if (platformItemSyncConfig.isMockUpload()) {
                                log.info("[平台库存上传][店铺{}][MOCK推送]{}", shop.getSn(), platformItem.getId());
                            } else {
                                platformItemStockSyncBizService.syncStock(platformItem.getId());
                                log.debug("[平台库存上传][店铺{}][库存推送完成]{}", shop.getSn(), platformItem.getId());
                            }
                        } catch (Exception e) {
                            log.error("[平台库存上传][店铺{}]同步异常，当前窗口时间={}",
                                    shop.getSn(),
                                    currentWindowTime, e
                            );
                        }
                    }, getTaskExecutor()).thenRun(countDownLatch::countDown);
                }
                try {
                    countDownLatch.await();
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
            timeSchedule.setIsComplete(true);
            timeSchedule.setActualTime(now);
            timeSchedule.setCompleteTime(now);
            timeScheduleService.updateById(timeSchedule);
            log.info("[平台库存上传][店铺{}]同步完成，当前窗口时间={}，同步频率={}",
                    shop.getSn(),
                    currentWindowTime,
                    syncFrequency);
        } catch (Exception e) {
            log.error("[平台库存上传][店铺{}]同步异常中断", shop.getSn(), e);
        } finally {
            lock.unlock();
        }
    }


    public static void main(String[] args) {
        final int syncFrequency = 24 * 3600;
        final int startTimeOffset = (int) (3600 * 14);

        final DateTimeFormatter timeFormatter = DatePattern.NORM_DATETIME_FORMATTER;
        for (int i = 0; i < 36; i++) {
            final LocalDateTime now = LocalDateTime.parse("2024-06-07 00:00:00", timeFormatter).plusHours(i);
            final LocalDateTime nextTime = TimeWindows.calculateWindowTime(CalcArgs.builder()
                                                                                   .now(now)
                                                                                   .syncFrequency(syncFrequency)
                                                                                   .startTimeOffset(startTimeOffset)
                                                                                   .build());
            System.out.printf("时间:%s --- %s\n", now.format(timeFormatter), nextTime.format(timeFormatter));

        }
    }

    private Thread thread;

    @Override
    public void start() {
        if (thread != null) {
            return;
        }
        thread = new Thread(this::loop);
        thread.setName("inventory-upload");
        thread.start();
        log.info("[平台库存上传]线程启动");

    }

    @Override
    public void stop() {
        thread.interrupt();
    }

    @Override
    public boolean isRunning() {
        return thread != null && thread.isAlive();
    }
}
*/
