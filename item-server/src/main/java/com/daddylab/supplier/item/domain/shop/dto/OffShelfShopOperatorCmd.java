package com.daddylab.supplier.item.domain.shop.dto;

import com.alibaba.cola.dto.Command;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> up
 * @date 2024年11月04日 10:58 AM
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OffShelfShopOperatorCmd extends Command {

    private static final long serialVersionUID = 1787220206040528400L;

    private Long id;

    @NotNull
    private Long shopId;

    @NotNull
    private Long operatorUid;
}
