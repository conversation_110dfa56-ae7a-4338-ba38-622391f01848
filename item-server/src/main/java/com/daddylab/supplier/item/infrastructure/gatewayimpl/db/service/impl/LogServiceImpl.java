package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Log;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.LogMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ILogService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
@Service
public class LogServiceImpl extends DaddyServiceImpl<LogMapper, Log> implements ILogService {

}
