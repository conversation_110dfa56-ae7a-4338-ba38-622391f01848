package com.daddylab.supplier.item.controller.test.dto;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.metadata.data.WriteCellData;

/**
 * <AUTHOR> up
 * @date 2024年01月17日 9:55 AM
 */
public class ProviderTypeAutoConverter implements Converter<Integer> {

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) throws Exception {
        Integer value = context.getValue();
        if (value == 1) {
            return new WriteCellData<>("自营");
        } else {
            return new WriteCellData<>("代发");
        }
    }
}
