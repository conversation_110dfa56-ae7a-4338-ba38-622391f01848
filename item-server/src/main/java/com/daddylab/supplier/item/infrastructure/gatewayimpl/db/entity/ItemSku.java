package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.GoodsType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.SkuSplitType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedHashMap;

/**
 * <p>
 * 商品SKU
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemSku implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * sku id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品ID
     */
    private Long itemId;

    /**
     * 供货指定编码
     */
    private String providerSpecifiedCode;

    /**
     * 供货编码，不存在供货指定编码时，取规格编码
     */
    public String getSupplierCode() {
        return providerSpecifiedCode != null && !providerSpecifiedCode.isEmpty() ? providerSpecifiedCode : skuCode;
    }

    /**
     * 成本价格，如果为0，说明未手动设置，默认使用商品维度的采购成本价格
     */
    private BigDecimal costPrice;

//    private Long costPriceStart;
//    private Long costPriceEnd;

    /**
     * 销售价格，如果为0，说明未手动设置，使用商品维度的日常销售价格
     */
    private BigDecimal salePrice;

    /**
     * 系统sku编码
     */
    private String skuCode;

    public String getPurchaseOrderSkuCode() {
        return providerSpecifiedCode != null && !providerSpecifiedCode.isEmpty() ? providerSpecifiedCode : skuCode;
    }

    /**
     * 编码中的6位数字
     */
    private String skuNum;

    /**
     * 删除时间
     */
    private Long deletedAt;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;

    private String kingDeeId;

    private String barCode;

    private String unit;

    private Long providerId;

    private Long categoryId;

    private String specifications;

    private String warehouseNo;

    /**
     * 商品税率
     */
    private BigDecimal taxRate;

    /**
     * 采购税率
     */
    private BigDecimal purchaseTaxRate;

    private SkuSplitType splitType;

    /**
     * 商品SKU额外拓展属性
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private LinkedHashMap<String, Object> props;

    /**
     * 平台佣金
     */
    private BigDecimal platformCommission;

    /**
     * 货品类型
     */
    private GoodsType goodsType;

    /**
     * 合同销售价
     */
    private BigDecimal contractSalePrice;
}
