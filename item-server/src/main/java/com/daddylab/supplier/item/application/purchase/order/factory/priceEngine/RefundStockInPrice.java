package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine;

import cn.hutool.core.collection.ListUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.WrapperType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> up
 * @date 2023年11月16日 9:55 AM
 */
@Slf4j
@Service
public class RefundStockInPrice extends BaseProcess implements PriceProcessor {

    /**
     * 在历史数据中，根据订单号，找到此月销售退货入库单的正向单据，将价格带回来
     *
     * @param timeBO 目标月份
     * @return
     */
    @Override
    public Boolean doPriceProcess(TimeBO timeBO) {

        List<WdtOrderDetailWrapper> list = iWdtOrderDetailWrapperService.lambdaQuery()
                .eq(WdtOrderDetailWrapper::getOperateTime, timeBO.getOperateMonth())
                .eq(WdtOrderDetailWrapper::getType, WrapperType.STOCK_IN_REFUND.getValue())
                .list();
        for (WdtOrderDetailWrapper refundStockInWrapper : list) {
            if (Objects.isNull(refundStockInWrapper.getTradeNo())) {
                continue;
            }
            iWdtOrderDetailWrapperService.lambdaQuery()
                    .eq(WdtOrderDetailWrapper::getTradeNo, refundStockInWrapper.getTradeNo())
                    .eq(WdtOrderDetailWrapper::getSkuCode, refundStockInWrapper.getSkuCode())
                    .in(
                            WdtOrderDetailWrapper::getType,
                            ListUtil.of(WrapperType.STOCK_OUT_SINGLE.getValue(), WrapperType.STOCK_OUT_COMBINATION.getValue())
                    )
                    .orderByDesc(WdtOrderDetailWrapper::getId).last("limit 1").list()
                    .stream().findFirst().ifPresent(val -> {
                        if (refundStockInWrapper.getPrice().compareTo(val.getPrice()) != 0) {
                            refundStockInWrapper.setPrice(val.getPrice());
                            iWdtOrderDetailWrapperService.updateById(refundStockInWrapper);
                        }
                    });
        }

        return true;
    }
}
