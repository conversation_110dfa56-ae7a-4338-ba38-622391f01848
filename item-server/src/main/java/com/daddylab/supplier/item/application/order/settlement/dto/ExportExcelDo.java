package com.daddylab.supplier.item.application.order.settlement.dto;

import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2023年08月15日 11:01 AM
 */
@Data
public class ExportExcelDo {
    private String sort;
    private String cycle;
    private String skuCode;
    private String itemName;
    private String specifications;
    private String unit;
    private String temporaryPrice;
    private String temporaryQuantity;
    private String deliverQuantity;
    private String currentMonthRefundQuantity;
    private String crossMonthRefundQuantity;
    private String settlementPrice;
    private String settlementQuantity;
    private String settlementAmount;
    private String afterSalesCost;
    private String finalAmount;
    private String remark;
    private String source;

    public static ExportExcelDo ofPageVo(FormDetailPageVo val) {
        ExportExcelDo excelDo = new ExportExcelDo();
        excelDo.setCycle(val.getCycle());
        excelDo.setSkuCode(val.getSkuCode());
        excelDo.setItemName(val.getItemName());
        excelDo.setSpecifications(val.getSpecifications());
        excelDo.setUnit(val.getUnit());
        excelDo.setTemporaryPrice(val.getTemporaryPrice().toString());
        excelDo.setTemporaryQuantity(val.getTemporaryQuantity().toString());
        excelDo.setDeliverQuantity(val.getDeliverQuantity().toString());
        excelDo.setCurrentMonthRefundQuantity(val.getCurrentMonthRefundQuantity().toString());
        excelDo.setCrossMonthRefundQuantity(val.getCrossMonthRefundQuantity().toString());
        excelDo.setSettlementPrice(val.getSettlementPrice().toString());
        excelDo.setSettlementQuantity(val.getSettlementQuantity().toString());
        excelDo.setSettlementAmount(val.getSettlementAmount().toString());
        excelDo.setAfterSalesCost(val.getAfterSalesCost().toString());
        excelDo.setFinalAmount(val.getFinalAmount().toString());
        excelDo.setRemark(val.getRemark());
        excelDo.setSource(val.getSource());
        return excelDo;
    }


}
