package com.daddylab.supplier.item.controller.handingsheet.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/29 15:46
 * @Description: 请描述下这个类
 */
@Data
@HeadRowHeight(20)
@ContentRowHeight(16)
@EqualsAndHashCode(callSuper = true)
public class HandingSheetExportVo extends ExportSheet implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * 盘货表名称
     */
    @ExcelProperty(value = "盘货表名称")
    @ColumnWidth(50)
    private String name;

    /**
     * 所属平台
     * 多个用逗号隔开：抖音,小红书
     */
    @ExcelProperty(value = "所属平台")
    @ColumnWidth(50)
    private String platforms;

    /**
     * 活动时间
     * 格式为：2022-08-15 00:00:00 ~ 2022-09-15 23:59:59
     */
    @ExcelProperty(value = "活动时间")
    @ColumnWidth(50)
    private String time;

    /**
     * 活动力度
     */
    @ExcelProperty(value = "活动力度")
    @ColumnWidth(50)
    private String activityEvents;

    /**
     * 活动标签
     */
    @ExcelProperty(value = "活动标签")
    @ColumnWidth(50)
    private String label;

    /**
     * 商品数量
     */
    @ExcelProperty(value = "商品数量")
    @ColumnWidth(50)
    private Long itemNum;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    @ColumnWidth(50)
    private String creator;

    /**
     * 创建时间
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    @ExcelProperty(value = "创建时间")
    @ColumnWidth(50)
    private String createdTime;
}
