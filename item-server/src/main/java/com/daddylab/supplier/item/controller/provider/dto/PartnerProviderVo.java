package com.daddylab.supplier.item.controller.provider.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/8 2:12 下午
 * @description
 */
@Data
@ApiModel("老爸会员系统名字模糊查询返回")
public class PartnerProviderVo implements Serializable {


    private static final long serialVersionUID = -695309864066491933L;

    @ApiModelProperty("地址")
    private String addr;

    @ApiModelProperty("区")
    private String area;

    private String areaCode;

    @ApiModelProperty("市")
    private String city;

    private String cityCode;

    @ApiModelProperty("省")
    private String province;

    private String provinceCode;

    @ApiModelProperty("供应商id")
    private Integer id;

    @ApiModelProperty("联系方式")
    private String mobile;

    @ApiModelProperty("供应商名称")
    private String name;

    @ApiModelProperty("社会信用代码")
    private String organizationNo;

    @ApiModelProperty("联系人")
    private String uname;

    @ApiModelProperty("商家后台，店铺ID")
    private String mallShopId;

}
