package com.daddylab.supplier.item.infrastructure.gatewayimpl.nuonuo;

import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import org.springframework.util.Assert;

import java.util.Objects;

/**
 * {
 * "code": "E0000",
 * "describe": "开票提交成功",
 * "result": {
 * "invoiceSerialNum": "20160108165823395151"
 * }
 * }
 *
 * <AUTHOR> up
 * @date 2024年07月26日 5:34 PM
 */
@Data
public class NuoNuoResponse {

    private static final String SUCCESS_CODE = "E0000";

    private String code;

    private String describe;

    private Object result;

    public static String getBlueInvoiceNum(String responseJson) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(responseJson);
            final JsonNode jsonNode = rootNode.get("code");
            Assert.state(Objects.equals(jsonNode.asText(), SUCCESS_CODE), "请求失败");
            final JsonNode resultNode = rootNode.get("result");
            final String val = resultNode.get("invoiceSerialNum").asText();
            Assert.hasText(val, "返回开票流水号为空");
            return val;
        }catch (Exception e){
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "解析诺诺返回参数异常");
        }
    }


    public static Boolean isSuccess(String responseJson) {
        try {
            responseJson = responseJson.replace("\\\"", "\"");
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(responseJson);
            final JsonNode jsonNode = rootNode.get("code");
            return Objects.equals(jsonNode.asText(), SUCCESS_CODE);
        } catch (Exception e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "解析诺诺返回参数异常");
        }
    }


    public static void main(String[] args) {
        String json = "{\\\"code\\\":\\\"E0000\\\",\\\"describe\\\":\\\"获取成功\\\",\\\"result\\\":[{\\\"additionalElementList\\\":[],\\\"additionalElementName\\\":\\\"\\\",\\\"address\\\":\\\"\\\",\\\"allElectronicInvoiceNumber\\\":\\\"20882407311600040844\\\",\\\"bankAccount\\\":\\\"\\\",\\\"buyerManagerName\\\":\\\"\\\",\\\"checker\\\":\\\"李四**********\\\",\\\"clerk\\\":\\\"旺仔\\\",\\\"clerkId\\\":\\\"\\\",\\\"createTime\\\":*************,\\\"deptId\\\":\\\"\\\",\\\"digitAccount\\\":\\\"***********\\\",\\\"emailNotifyStatus\\\":\\\"4\\\",\\\"extensionNumber\\\":\\\"923\\\",\\\"imgUrls\\\":\\\"https://inv.jss.com.cn/fp2/VQJLfBlqlJuNyiJuMaQIQlXMCuPmQcuL_0oxsbxz4cmZcpk5VjV9yVqqEmkiN6HPktULqis-3wgEOXP3IE2SKg.jpg\\\",\\\"invoiceDate\\\":*************,\\\"invoiceType\\\":\\\"1\\\",\\\"listFlag\\\":\\\"0\\\",\\\"listName\\\":\\\"\\\",\\\"managerCardNo\\\":\\\"\\\",\\\"managerCardType\\\":\\\"\\\",\\\"naturalPersonFlag\\\":0,\\\"notifyEmail\\\":\\\"<EMAIL>\\\",\\\"ofdUrl\\\":\\\"https://inv.jss.com.cn/fp2/VQJLfBlqlJuNyiJuMaQIQiGhFbXc5HAS4Q7Wn3ArENCQipP6v80RHIdkh4t8Bv_yzBok8HpUxBcBfxSvqTyOvA.ofd\\\",\\\"oldInvoiceCode\\\":\\\"\\\",\\\"oldInvoiceNo\\\":\\\"\\\",\\\"orderAmount\\\":\\\"100.00\\\",\\\"payee\\\":\\\"张三\\\",\\\"phone\\\":\\\"\\\",\\\"phoneNotifyStatus\\\":\\\"4\\\",\\\"productOilFlag\\\":0,\\\"proxyInvoiceFlag\\\":\\\"0\\\",\\\"redReason\\\":\\\"\\\",\\\"remark\\\":\\\"开具红字增值税专用发票信息表编号Cx9kZR7x_ihj4M3m\\\",\\\"requestSrc\\\":\\\"0\\\",\\\"saleName\\\":\\\"航信培训企业199\\\",\\\"salerAccount\\\":\\\"浙江桐庐农村商业银行股份有限公司城南支行***************\\\",\\\"salerAddress\\\":\\\"杭州市上城区\\\",\\\"salerTaxNum\\\":\\\"***************\\\",\\\"salerTel\\\":\\\"0571-********\\\",\\\"specificFactor\\\":0,\\\"stateUpdateTime\\\":*************,\\\"telephone\\\":\\\"\\\",\\\"terminalNumber\\\":\\\"\\\",\\\"updateTime\\\":*************,\\\"serialNo\\\":\\\"24073116000402807171\\\",\\\"orderNo\\\":\\\"ORD005\\\",\\\"status\\\":\\\"2\\\",\\\"statusMsg\\\":\\\"开票完成（最终状态）\\\",\\\"failCause\\\":\\\"\\\",\\\"pdfUrl\\\":\\\"https://inv.jss.com.cn/fp2/VQJLfBlqlJuNyiJuMaQIQm7qQT-ggKTbIF3xFx5pYzyP3ck8BWvQSBbTYGQnpuSXUVBYO6sS97xLra-1r0k92A.pdf\\\",\\\"pictureUrl\\\":\\\"nnfp.jss.com.cn/6vGS=DfeC2-IPoA\\\",\\\"invoiceTime\\\":*************,\\\"invoiceCode\\\":\\\"\\\",\\\"invoiceNo\\\":\\\"20882407311600040844\\\",\\\"exTaxAmount\\\":\\\"88.50\\\",\\\"taxAmount\\\":\\\"11.50\\\",\\\"payerName\\\":\\\"杭州老爸电商科技有限公司\\\",\\\"payerTaxNo\\\":\\\"339901999999198\\\",\\\"invoiceKind\\\":\\\"电子发票(普通发票)\\\",\\\"checkCode\\\":\\\"\\\",\\\"qrCode\\\":\\\"01,32,,20882407311600040844,100.00,20240731,,2D86\\\",\\\"machineCode\\\":\\\"\\\",\\\"cipherText\\\":\\\"\\\",\\\"invoiceItems\\\":[{\\\"dField1\\\":\\\"dField1\\\",\\\"dField2\\\":\\\"dField2\\\",\\\"dField3\\\":\\\"dField3\\\",\\\"dField4\\\":\\\"dField4\\\",\\\"dField5\\\":\\\"dField5\\\",\\\"deduction\\\":\\\"0.00\\\",\\\"immediateTaxReturnType\\\":\\\"\\\",\\\"itemCodeAbb\\\":\\\"计算机外部设备\\\",\\\"itemIndex\\\":1,\\\"itemSelfCode\\\":\\\"G005\\\",\\\"itemName\\\":\\\"Goods5\\\",\\\"itemUnit\\\":\\\"PCS\\\",\\\"itemPrice\\\":\\\"100.000000000000000000\\\",\\\"itemTaxRate\\\":\\\"0.13\\\",\\\"itemNum\\\":\\\"1.000000000000000000\\\",\\\"itemAmount\\\":\\\"100.00\\\",\\\"itemTaxAmount\\\":\\\"11.50\\\",\\\"itemSpec\\\":\\\"\\\",\\\"itemCode\\\":\\\"1090511030000000000\\\",\\\"isIncludeTax\\\":\\\"true\\\",\\\"invoiceLineProperty\\\":\\\"0\\\",\\\"zeroRateFlag\\\":\\\"\\\",\\\"favouredPolicyName\\\":\\\"\\\"}]}]}\n";
        System.out.println(isSuccess(json));
        System.out.println(11);
    }
}
