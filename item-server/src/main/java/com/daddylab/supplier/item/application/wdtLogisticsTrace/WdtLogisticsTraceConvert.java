package com.daddylab.supplier.item.application.wdtLogisticsTrace;

import com.daddylab.mall.wdtsdk.apiv2.statistic.dto.SearchLogisticsTraceResponse;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.WdtLogisticsStatus;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtLogisticsTrace;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import java.time.LocalDateTime;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @since 2024/11/29
 */
@Mapper(imports = {JsonUtil.class})
public interface WdtLogisticsTraceConvert {
    WdtLogisticsTraceConvert INSTANCE = org.mapstruct.factory.Mappers.getMapper(WdtLogisticsTraceConvert.class);

    WdtLogisticsTrace toEntity(SearchLogisticsTraceResponse.OrderListItem orderListItem);

    default String mapWdtDetailListItemToString(List<SearchLogisticsTraceResponse.DetailListItem> value) {
        return JsonUtil.toJson(value);
    }

    default WdtLogisticsStatus toWdtLogisticsStatus(Integer value) {
        return IEnum.getEnumByValue(WdtLogisticsStatus.class, value);
    }

    default LocalDateTime parseTime(String dateTime) {
        return DateUtil.parseCompatibility(dateTime);
    }

}
