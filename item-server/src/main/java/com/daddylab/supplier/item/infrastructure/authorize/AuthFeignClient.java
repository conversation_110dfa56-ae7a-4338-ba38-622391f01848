package com.daddylab.supplier.item.infrastructure.authorize;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.DlabConfiguration;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "AuthFeignClient", url = "${auth-server.url}", configuration = DlabConfiguration.class)
public interface AuthFeignClient {
    @GetMapping(path = "/cas/serviceValidate")
    Rsp<ServiceValidateRst> serviceValidate(@RequestParam("service") String service, @RequestParam("ticket") String ticket);
}
