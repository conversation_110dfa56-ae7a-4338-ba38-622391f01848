package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformSyncErrorLevel;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformType;
import lombok.*;
import org.javers.core.metamodel.annotation.DiffIgnore;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-13
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class ThirdPlatformSyncLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 同步ID（third_platform_sync.id）
     */
    private Long syncId;

    /**
     * 请求参数
     */
    private String req;

    /**
     * 响应参数
     */
    private String resp;

    /**
     * 平台类型
     */
    private PlatformType platformType;

    /**
     * 错误等级
     */
    private PlatformSyncErrorLevel errorLevel;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;



    /**
     * 逻辑删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 异常
     */
    private String error;

    /**
     * 商品code
     */
    private String itemCode;

    /**
     * 商品id
     */
    private Long itemId;


}
