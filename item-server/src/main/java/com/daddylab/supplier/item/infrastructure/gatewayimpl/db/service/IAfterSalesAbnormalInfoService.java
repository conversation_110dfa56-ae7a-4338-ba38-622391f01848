package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesAbnormalInfo;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 售后异常件信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-17
 */
public interface IAfterSalesAbnormalInfoService extends IDaddyService<AfterSalesAbnormalInfo> {

}
