package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.purchase.order.factory.dto.OrderTradeTimeDO;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.StockInDetailDO;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.StockInNumDO;
import com.daddylab.supplier.item.application.salesInStock.dto.*;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundStockInOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 旺店通退货入库单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
public interface WdtRefundStockInOrderMapper extends DaddyBaseMapper<WdtRefundStockInOrder> {

//    Integer selectRefundStockInCount(@Param("start") String start, @Param("end") String end);

//    List<StockInNumDO> selectRefundStockInList(@Param("stockInIdList") List<Long> stockInIdList);

//    List<OrderTradeTimeDO> selectOrderTradeTimeByStockInId(@Param("stockInId") Long stockInId);

    List<OrderTradeTimeDO> selectOrderTradeTimeByStockInId2(@Param("stockInId") Long stockInId);

    List<StockInNumDO> selectPreStockInList(@Param("start") String start, @Param("end") String end,
                                            @Param("eliminateWarehouseNos") List<String> eliminateWarehouseNos);


    /*List<Long> selectRefundStockInIdList(@Param("start") String start, @Param("end") String end
            , @Param("eliminateWarehouseNos") List<String> eliminateWarehouseNos);*/

//    Integer selectRefundStockInIdCount(@Param("start") String start, @Param("end") String end);

    List<StockInNumDO> selectRefundStockInList2(@Param("startTime") String startTime, @Param("endTime") String endTime,
                                                @Param("eliminateWarehouseNos") List<String> eliminateWarehouseNos,
                                                @Param("externalShopNos") List<String> externalShopNos
                                                );

    List<StockInDetailDO> newRefundStockInList(@Param("startTime") String startTime, @Param("endTime") String endTime,
                                                   @Param("eliminateWarehouseNos") List<String> eliminateWarehouseNos,
                                                   @Param("externalShopNos") List<String> externalShopNos,
                                               @Param("skuCode") String skuCode);


    // ------------------------ 库存管理-销售入库单 ---------------

    /**
     * 库存管理-销售入库单列表查询
     *
     * @param pageQuery
     * @return
     */
    List<SalesInStockPageVO> selectSalesInStockPage(@Param("param") SalesInStockPageQuery pageQuery);

    /**
     * 库存管理-销售入库单详情-入库单基础信息查询
     *
     * @param orderNo 入库单号
     * @return
     */
    SalesInStockBaseVO selectSalesInStockBase(@Param("orderNo") String orderNo);

    /**
     * 库存管理-销售入库单详情-退换单基础信息查询
     *
     * @param refundNo 退货单号
     * @return
     */
    SalesInStockRefundBaseVO selectSalesInStockRefundBase(@Param("refundNo") String refundNo);

    /**
     * 库存管理-销售入库单详情-入库单明细list
     *
     * @param stockInId 入库单id
     * @return
     */
    List<SalesInStockDetailListVO> selectSalesInStockDetailList(@Param("stockInId") Long stockInId);


}
