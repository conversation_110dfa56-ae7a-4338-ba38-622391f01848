/*
package com.daddylab.supplier.item.application.virtualWarehouse.dto;

import lombok.Data;

import java.math.BigDecimal;

*/
/**
 * <AUTHOR> up
 * @date 2024年03月04日 3:58 PM
 *//*

@Data
public class VirtualWarehousePageDetailVo {

    private Long id;

    private String warehouseName;

    private String warehouseNo;

    private Integer inventoryRatio;

    */
/**
     * 仓内库存
     *//*

    private BigDecimal availableStock;

    */
/**
     * 可发库存
     *//*

    private BigDecimal availableSendStock;

    */
/**
     * 可用库存
     *//*

    private BigDecimal usableStock;

    private Integer warehouseVersion;

}
*/
