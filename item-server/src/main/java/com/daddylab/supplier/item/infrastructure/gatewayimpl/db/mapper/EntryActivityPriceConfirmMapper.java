package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceConfirm;

/**
 * <p>
 * 入驻活动价格确认 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-06
 */
public interface EntryActivityPriceConfirmMapper extends DaddyBaseMapper<EntryActivityPriceConfirm> {

}
