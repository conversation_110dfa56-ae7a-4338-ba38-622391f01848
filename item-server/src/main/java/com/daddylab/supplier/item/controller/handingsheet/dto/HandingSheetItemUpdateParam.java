package com.daddylab.supplier.item.controller.handingsheet.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/24 15:29
 * @Description: 修改盘货表商品价格参数封装
 */
@Data
public class HandingSheetItemUpdateParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "盘货表和商品信息的关联关系ID")
    private Long handingSheetItemId;

    @ApiModelProperty(value = "活动价")
    private BigDecimal activePrice;

    @ApiModelProperty(value = "赠品机制")
    private String giftDescription;

    @ApiModelProperty(value = "赠品编码")
    private String giftCode;

    @ApiModelProperty(value = "备注")
    private String remark;
}
