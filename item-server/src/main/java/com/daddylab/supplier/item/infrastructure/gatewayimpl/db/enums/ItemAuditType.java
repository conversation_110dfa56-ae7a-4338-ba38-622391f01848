package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ItemAuditType implements IEnum<Integer> {

    ITEM_MATERIAL(1, "商品资料"),

    LIVE_VERBAL_TRICK(2, "直播话术"),

    TRAINING_MATERIAL(3, "培训资料"),
    ;

    private final Integer value;
    private final String desc;


}
