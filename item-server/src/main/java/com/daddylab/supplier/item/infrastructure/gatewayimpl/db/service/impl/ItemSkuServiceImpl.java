package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.controller.item.dto.AttrDto;
import com.daddylab.supplier.item.controller.item.dto.SkuWithLaunchPlanDto;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemSkuMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品SKU 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class ItemSkuServiceImpl extends DaddyServiceImpl<ItemSkuMapper, ItemSku> implements IItemSkuService {
    @Autowired
    private ItemSkuMapper itemSkuMapper;

    @Override
    public Map<Long, SkuWithLaunchPlanDto> getSkuDtoMap(List<Long> skuIds) {
        if (CollectionUtil.isEmpty(skuIds)) {
            return new HashMap<>();
        }

        List<SkuWithLaunchPlanDto> dtos = itemSkuMapper.selectBatchDtosBySkuIds(skuIds);
        Map<Long, SkuWithLaunchPlanDto> map = new HashMap<>((int) (dtos.size() / 0.75 + 1));
        for (SkuWithLaunchPlanDto dto : dtos) {
            map.put(dto.getSkuId(), dto);
        }
        return map;
    }

    @Override
    public Map<Long, ItemSku> getSkuMap(List<Long> skuIds) {
        if (CollectionUtil.isEmpty(skuIds)) {
            return new HashMap<>(1);
        }
        QueryWrapper<ItemSku> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ItemSku::getId, skuIds);
        List<ItemSku> itemSkus = itemSkuMapper.selectList(queryWrapper);

        Map<Long, ItemSku> map = new HashMap<>((int) (itemSkus.size() / 0.75 + 1));
        for (ItemSku sku : itemSkus) {
            map.put(sku.getId(), sku);
        }
        return map;
    }

    @Override
    public List<ItemSku> selectListByItemId(Long itemId) {
        if (itemId == null || itemId <= 0) {
            return new ArrayList<>();
        }
        QueryWrapper<ItemSku> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ItemSku::getItemId, itemId);
        return itemSkuMapper.selectList(queryWrapper);
    }

    @Override
    public List<AttrDto> selectSkuAttrDtosBySkuId(Long skuId) {
        if (skuId == null || skuId <= 0) {
            return new ArrayList<>();
        }
        return itemSkuMapper.selectSkuAttrDtosBySkuId(skuId);
    }

    @Override
    public Long getSkuBrandId(String skuCode) {
        Long skuBrandId = itemSkuMapper.getSkuBrandId(skuCode);
        return Objects.isNull(skuBrandId) ? 0L : skuBrandId;
    }

    @Override
    public List<ItemSku> selectByItemIds(List<Long> itemIds) {
        if (CollectionUtil.isEmpty(itemIds)) return Collections.emptyList();
        return lambdaQuery().in(ItemSku::getItemId, itemIds).list();
    }

    @Override
    public List<ItemSku> selectByMixCodes(Collection<String> relatedSkuCodes) {
        relatedSkuCodes = relatedSkuCodes.stream()
                                         .filter(StringUtil::isNotBlank)
                                         .distinct()
                                         .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(relatedSkuCodes)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(ItemSku::getSkuCode, relatedSkuCodes)
                .or()
                .in(ItemSku::getProviderSpecifiedCode, relatedSkuCodes)
                .list();
    }

    @Override
    public Optional<ItemSku> getByMixCode(String mixCode) {
        if (StringUtil.isBlank(mixCode)) {
            return Optional.empty();
        }
        return selectByMixCodes(Collections.singleton(mixCode)).stream().findFirst();
    }

    @Override
    public void updateNewProvider(Long itemId, Long newProviderId) {
        this.lambdaUpdate().set(ItemSku::getProviderId,newProviderId)
                .eq(ItemSku::getItemId,itemId).update();
    }
}
