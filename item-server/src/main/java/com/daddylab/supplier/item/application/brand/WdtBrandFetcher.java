package com.daddylab.supplier.item.application.brand;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.goods.GoodsBrandAPI;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsBrandSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsBrandSearchResponse;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsBrandSearchResponse.Detail;
import com.daddylab.supplier.item.common.enums.EnableStatusEnum;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Brand;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBrandService;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.google.common.base.Joiner;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022/5/15
 */
@Component
@Slf4j
public class WdtBrandFetcher implements PageFetcher {

    private final WdtGateway wdtGateway;
    private final IBrandService brandService;
    private final OperateLogDomainService operateLogDomainService;

    public WdtBrandFetcher(WdtGateway wdtGateway,
                           IBrandService brandService,
                           OperateLogDomainService operateLogDomainService) {
        this.wdtGateway = wdtGateway;
        this.brandService = brandService;
        this.operateLogDomainService = operateLogDomainService;
    }

    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.BRAND;
    }

    @Override
    public int getTotal() {
        return query(null, null, 1, 1, true).getTotalCount();
    }

    @Override
    public int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
        return query(startTime, endTime, 1, 1, true).getTotalCount();
    }

    @Override
    public void fetch(long pageIndex, long pageSize, RunContext runContext) {
        final GoodsBrandSearchResponse response = query(null, null, (int) pageIndex, (int) pageSize,
                false);
        if (CollUtil.isEmpty(response.getDetailList())) {
            return;
        }
        handleResponse(response);

    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, long pageIndex, long pageSize,
                      RunContext runContext) {
        final GoodsBrandSearchResponse response = query(startTime, endTime, (int) pageIndex,
                (int) pageSize,
                false);
        if (CollUtil.isEmpty(response.getDetailList())) {
            return;
        }
        handleResponse(response);
    }

    private void handleResponse(GoodsBrandSearchResponse response) {
        for (Detail detail : response.getDetailList()) {
            List<Brand> brands = brandService.lambdaQuery().eq(Brand::getSn, detail.getBrandNo()).or()
                    .eq(Brand::getName, detail.getBrandName())
                    .list();

            if (brands.isEmpty()) {
                Brand brand = new Brand();
                brand.setSn(detail.getBrandNo());
                brand.setName(detail.getBrandName());
                brand.setLogo("");
                brandService.save(brand);
            } else {
                for (Brand brand : brands) {
                    final ArrayList<String> operateLogs = new ArrayList<>();
                    if (!Objects.equals(brand.getSn(), detail.getBrandNo())) {
                        operateLogs.add(StringUtil
                                .format("编号:'{}' -> '{}'", brand.getSn(), detail.getBrandNo()));
                        brand.setSn(detail.getBrandNo());
                    }
                    if (!Objects.equals(brand.getName(), detail.getBrandName())) {
                        operateLogs.add(StringUtil.format("名称:'{}' -> '{}'", brand.getName(),
                                detail.getBrandName()));
                        brand.setName(detail.getBrandName());
                    }
                    final EnableStatusEnum status =
                            detail.getIsDisabled() != null && detail.getIsDisabled() == 0
                                    ? EnableStatusEnum.ON : EnableStatusEnum.OFF;
                    if (!Objects.equals(brand.getStatus(), status)) {
                        operateLogs.add(StringUtil
                                .format("状态:'{}' -> '{}'", statusDesc(brand.getStatus()),
                                        statusDesc(status)));
                        brand.setStatus(status);
                    }
                    if (!operateLogs.isEmpty()) {
                        try {
                            brandService.updateById(brand);
                            operateLogDomainService
                                    .addOperatorLog(0L, OperateLogTarget.BRAND, brand.getId(),
                                            StringUtil.format("旺店通回传数据，{}", Joiner.on('、').join(operateLogs)));
                        } catch (Exception e) {
                            log.error("【旺店通品牌同步】同步品牌'{}'时数据库更新异常:{}", detail.getBrandName(), e.getMessage(), e);
                            Alert.text(MessageRobotCode.GLOBAL, String.format("旺店通品牌同步异常，品牌:%s", JSON.toJSONString(detail)));
                        }
                    }
                }
            }
        }
    }

    @NonNull
    private String statusDesc(EnableStatusEnum status) {
        return status == EnableStatusEnum.ON ? "正常" : "停用";
    }

    private GoodsBrandSearchResponse query(LocalDateTime st, LocalDateTime et, int pageNo,
                                           int pageSize, boolean calcTotal) {
        try {
            final GoodsBrandAPI api = wdtGateway.getAPI(GoodsBrandAPI.class);
            final Pager pager = new Pager(pageSize, pageNo - 1, calcTotal);
            final GoodsBrandSearchParams params = new GoodsBrandSearchParams();
            if (st != null) {
                params.setStartTime(DateUtil.format(st));
            }
            if (et != null) {
                params.setEndTime(DateUtil.format(et));
            }
            return api.search(params, pager);
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapFetchException(e, fetchDataType());
        }
    }


}
