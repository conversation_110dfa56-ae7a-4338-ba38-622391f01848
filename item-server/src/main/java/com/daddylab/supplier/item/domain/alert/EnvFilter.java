package com.daddylab.supplier.item.domain.alert;

import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class EnvFilter implements IAlerter.Filter {

    @Override
    public List<IAlerter.Message> filter(IAlerter alert, List<IAlerter.Message> message) {
        final ArrayList<IAlerter.Message> filtered = new ArrayList<>();
        final String activeProfile = ApplicationContextUtil.getActiveProfile();
        for (IAlerter.Message oneMessage : message) {
            String content = oneMessage.getContent();
            content = "profile:" + activeProfile + "\n\n" + content;
            filtered.add(new IAlerter.Message(oneMessage.getMsgType()
                    , content));
        }
        return filtered;
    }
}
