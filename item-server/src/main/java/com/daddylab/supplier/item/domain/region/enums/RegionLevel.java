package com.daddylab.supplier.item.domain.region.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum RegionLevel implements IEnum<Integer> {
    BIG_REGION(0, "大区"),
    PROVINCE(1, "省"),
    CITY(2, "市"),
    COUNTY(3, "区/县"),
    ;
    private final Integer value;
    private final String desc;

}
