//package com.daddylab.supplier.item.application.item.itemBatchProc.batch;
//
//import org.springframework.batch.core.configuration.annotation.DefaultBatchConfigurer;
//import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
//import org.springframework.context.annotation.Configuration;
//
//import javax.sql.DataSource;
//
///**
// * <AUTHOR> up
// * @date 2023年12月27日 10:56 AM
//  # 批处理
//          spring.batch.job.enabled:false
//
// */
//@Configuration
//@EnableBatchProcessing
//public class DefaultConfig extends DefaultBatchConfigurer {
//    @Override
//    public void setDataSource(DataSource dataSource) {
////        super.setDataSource(dataSource);
//    }
//
//
//}
