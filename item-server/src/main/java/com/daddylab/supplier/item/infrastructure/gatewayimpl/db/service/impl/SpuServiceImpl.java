package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Spu;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.SpuMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ISpuService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 标准销售单元（由于金蝶缺少SPU，导致我们存在大量历史商品被拆分为"散装"的SKU，这张表的目的就是将这些散装的SKU重新聚合） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
@Service
public class SpuServiceImpl extends DaddyServiceImpl<SpuMapper, Spu> implements ISpuService {

}
