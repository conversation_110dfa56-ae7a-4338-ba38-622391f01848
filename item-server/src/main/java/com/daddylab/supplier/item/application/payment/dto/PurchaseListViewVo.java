package com.daddylab.supplier.item.application.payment.dto;

import com.daddylab.supplier.item.controller.item.dto.CorpBizListDto;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年11月20日 5:42 PM
 */
@Data
@ApiModel("采购单明细list")
public class PurchaseListViewVo {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("sku编码")
    private String skuCode;

    @ApiModelProperty("商品名称")
    private String itemName;

    @ApiModelProperty("规格")
    private String specifications;

    @ApiModelProperty("单价")
    private BigDecimal price;

    @ApiModelProperty("数量")
    private Integer quantity;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    private List<CorpBizTypeDTO> corpBizType;

//    @ApiModelProperty("是否已经申请")
//    private Integer isApplied;

}
