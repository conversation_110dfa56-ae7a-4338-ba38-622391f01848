package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.offShelf.dto.OffShelfItemVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OffShelfItem;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 下架管理-下架流程商品信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface OffShelfItemMapper extends DaddyBaseMapper<OffShelfItem> {

    List<OffShelfItemVO> selectOffShelfItemVOList(@Param("id") Long id);
}
