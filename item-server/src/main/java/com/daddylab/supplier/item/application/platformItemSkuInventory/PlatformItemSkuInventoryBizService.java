package com.daddylab.supplier.item.application.platformItemSkuInventory;

import com.daddylab.supplier.item.domain.common.enums.Platform;

/**
 * <AUTHOR>
 * @class PlatformItemSkuInventoryBizService.java
 * @description 平台商品库存服务类
 * @date 2024-03-13 16:00
 */
public interface PlatformItemSkuInventoryBizService {

    /**
     * 全量同步库存
     *
     * @date 2024/3/14 15:41
     * <AUTHOR>
     * @param platform 平台
     */
    void fullDoseSync(Platform platform);

    /**
     * 某些平台没有库存变更事件，这边定时主动触发库存变更事件
     *
     * @param time Long 时间戳
     * @date 2024/3/13 16:06
     * <AUTHOR>
     * @deprecated
     */
    void syncStock(Long time);


}
