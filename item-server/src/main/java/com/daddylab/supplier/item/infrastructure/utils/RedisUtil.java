package com.daddylab.supplier.item.infrastructure.utils;

import cn.hutool.core.date.StopWatch;
import com.alibaba.cola.exception.SysException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import java.lang.reflect.Type;
import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Consumer;
import java.util.function.Supplier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.DecoratedRedisConnection;
import org.springframework.data.redis.connection.RedisClusterConnection;
import org.springframework.data.redis.connection.RedisClusterNode;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;

/**
 * <AUTHOR>
 */
@Slf4j
public class RedisUtil {

    public static final int LOCK_TIMEOUT = 30;
    /**
     * 原始类型数据用这个
     */
    private static StringRedisTemplate stringRedisTemplate = null;

    /**
     * 缓存加载排它锁
     */
    private static final LoadingCache<Object, ReentrantLock> exLockLoadingCache = Caffeine.newBuilder()
            .maximumSize(1000).build(k -> new ReentrantLock());

    private static StringRedisTemplate getStringRedisTemplate() {
        if (stringRedisTemplate == null) {
            stringRedisTemplate = ApplicationContextUtil.getBean(StringRedisTemplate.class);
        }
        return stringRedisTemplate;
    }

    /**
     * 删除指定key
     *
     * @param key key
     */
    public static void del(String key) {
        getStringRedisTemplate().delete(key);
    }

    /**
     * 设置值（字符串和原始类型不序列化，其他类型使用JsonUtil序列化）
     * 原始类型包括：Boolean.TYPE, Character.TYPE, Byte.TYPE, Short.TYPE, Integer.TYPE, Long.TYPE, Float.TYPE, Double.TYPE, Void.TYPE
     *
     * @param key   key
     * @param value value
     */
    public static void set(String key, Object value) {
        if (value instanceof String) {
            getStringRedisTemplate().opsForValue().set(key, (String) value);
        } else if (!value.getClass().isPrimitive()) {
            getStringRedisTemplate().opsForValue().set(key, JsonUtil.toJson(value));
        } else {
            getStringRedisTemplate().opsForValue().set(key, value.toString());
        }
    }

    /**
     * 设置值和超时时间（字符串和原始类型不序列化，其他类型使用JsonUtil序列化）
     * 原始类型包括：Boolean.TYPE, Character.TYPE, Byte.TYPE, Short.TYPE, Integer.TYPE, Long.TYPE, Float.TYPE, Double.TYPE, Void.TYPE
     *
     * @param key     key
     * @param value   value
     * @param timeout 超时时间
     * @param unit    时间单位
     */
    public static void set(String key, Object value, long timeout, TimeUnit unit) {
        if (value instanceof String) {
            getStringRedisTemplate().opsForValue().set(key, (String) value, timeout, unit);
        } else if (!value.getClass().isPrimitive()) {
            getStringRedisTemplate().opsForValue().set(key, JsonUtil.toJson(value), timeout, unit);
        } else {
            getStringRedisTemplate().opsForValue().set(key, value.toString(), timeout, unit);
        }
    }

    /**
     * 批量设置键值
     *
     * @param map           KV
     * @param timeoutMillis 超时时间，毫秒
     */
    public static void multiSetIfAbsent(Map<String, Object> map, Long timeoutMillis) {
        multiSet(map, true, timeoutMillis);
    }

    /**
     * 批量设置键值
     *
     * @param map           KV
     * @param ifAbsent      键值不存在时再设置
     * @param timeoutMillis 超时时间，毫秒
     */
    public static void multiSet(Map<String, Object> map, Boolean ifAbsent, Long timeoutMillis) {
        Map<String, String> setMap = new HashMap<>(map.size());
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            final String key = entry.getKey();
            final Object value = entry.getValue();

            if (value instanceof String) {
                setMap.put(key, (String) value);
            } else if (!value.getClass().isPrimitive()) {
                setMap.put(key, JsonUtil.toJson(value));
            } else {
                setMap.put(key, value.toString());
            }
        }

        if (Boolean.TRUE.equals(ifAbsent)) {
            getStringRedisTemplate().opsForValue().multiSetIfAbsent(setMap);
        } else {
            getStringRedisTemplate().opsForValue().multiSet(setMap);
        }

        if (NumberUtil.isPositive(timeoutMillis)) {
            multiExpire(setMap.keySet(), timeoutMillis);
        }
    }

    /**
     * 批量设置key的超时时间
     *
     * @param keys          keys
     * @param timeoutMillis 超时时间，毫秒
     */
    public static void multiExpire(Set<String> keys, long timeoutMillis) {
        getStringRedisTemplate().executePipelined((RedisCallback<Object>) connection -> {
            for (String key : keys) {
                connection.pExpire(serializeKey(getStringRedisTemplate(), key), timeoutMillis);
            }
            return null;
        });
    }

    /**
     * 设置过期时间
     *
     * @param key     key
     * @param timeout 超时时长
     * @param unit    时间单位
     */
    public static void expire(String key, long timeout, TimeUnit unit) {
        getStringRedisTemplate().expire(key, timeout, unit);
    }

    /**
     * 设置过期时间
     *
     * @param key     key
     * @param instant 指定时间点
     */
    public static void expireAt(String key, Instant instant) {
        getStringRedisTemplate().expireAt(key, instant);
    }

    /**
     * 获取过期时间
     *
     * @param key key
     * @return 过期剩余秒数
     * <p>
     * The command returns -2 if the key does not exist.
     * The command returns -1 if the key exists but has no associated expire.
     */
    public static Long getExpire(String key) {
        return getStringRedisTemplate().getExpire(key);
    }

    /**
     * 获取过期时间
     *
     * @param key      key
     * @param timeUnit 时间单位
     * @return 过期剩余时间
     * <p>
     * The command returns -2 if the key does not exist.
     * The command returns -1 if the key exists but has no associated expire.
     */
    public static Long getExpire(String key, TimeUnit timeUnit) {
        return getStringRedisTemplate().getExpire(key, timeUnit);
    }

    /**
     * 获取单个key
     *
     * @param key key
     * @return 值
     */
    public static String get(String key) {
        return getStringRedisTemplate().opsForValue().get(key);
    }

    /**
     * 获取单个值
     *
     * @param key   key
     * @param clazz 类型
     * @return 值
     */
    @SuppressWarnings({"unchecked"})
    public static <T> T get(String key, Class<T> clazz) {
        final String string = getStringRedisTemplate().opsForValue().get(key);
        if (clazz.isAssignableFrom(String.class)) {
            return (T) string;
        }
        return string == null ? null : JsonUtil.parse(string, clazz);
    }

    /**
     * 获取单个值
     *
     * @param key           key
     * @param typeReference 类型引用（支持范型）
     * @return 值
     */
    public static <T> T get(String key, TypeReference<T> typeReference) {
        final String string = getStringRedisTemplate().opsForValue().get(key);
        return string == null ? null : JsonUtil.parse(string, typeReference);
    }

    /**
     * 获取单个值
     *
     * @param key  key
     * @param type java.lang.reflect.Type
     * @return 值
     */
    public static <T> T get(String key, Type type) {
        final String string = getStringRedisTemplate().opsForValue().get(key);
        return string == null ? null : JsonUtil.parse(string, type);
    }

    /**
     * 批量获取（默认返回String）
     *
     * @param keys keys
     * @return Map<Key, Value>
     */
    public static Map<String, String> multiGet(List<String> keys) {
        return multiGet(keys, String.class);
    }

    /**
     * 批量获取
     *
     * @param keys  keys
     * @param clazz 值类型
     * @return Map<Key, Value>
     */
    @SuppressWarnings("unchecked")
    public static <T> Map<String, T> multiGet(List<String> keys, Class<T> clazz) {
        final List<String> strings = getStringRedisTemplate().opsForValue().multiGet(keys);
        if (strings == null) {
            return Collections.emptyMap();
        }
        final HashMap<String, T> result = new HashMap<>(keys.size());
        for (int i = 0; i < keys.size(); i++) {
            final String value = strings.get(i);
            if (String.class.equals(clazz)) {
                result.put(keys.get(i), (T) value);
            } else {
                result.put(keys.get(i), JsonUtil.parse(value, clazz));
            }
        }
        return result;
    }

    /**
     * 原子递增
     *
     * @param key    缓存键
     * @param addVal 递增值
     * @return 递增后的值
     */
    public static Long increment(String key, Integer addVal) {
        return getStringRedisTemplate().opsForValue().increment(key, addVal);
    }

    /**
     * 原子递减
     *
     * @param key     缓存键
     * @param decrVal 递减值
     * @return 递减后的值
     */
    public static Long decrement(String key, Integer decrVal) {
        return getStringRedisTemplate().opsForValue().decrement(key, decrVal);
    }

    /**
     * 获取缓存或者当缓存不存在的直接载入缓存
     *
     * @param key      缓存键
     * @param clazz    缓存数据类型
     * @param timeout  缓存超时时间
     * @param timeUnit 时间单位
     * @param supplier 载入数据提供者
     * @return 缓存数据
     */
    public static <T> T loadingCache(String key, Class<T> clazz, long timeout, TimeUnit timeUnit, Supplier<T> supplier) {
        return loadingCache0(key, timeout, timeUnit, supplier, () -> get(key, clazz));
    }

    /**
     * 获取缓存或者当缓存不存在的直接载入缓存
     *
     * @param key      缓存键
     * @param type     缓存数据类型
     * @param timeout  缓存超时时间
     * @param timeUnit 时间单位
     * @param supplier 载入数据提供者
     * @return 缓存数据
     */
    public static <T> T loadingCache(String key, Type type, long timeout, TimeUnit timeUnit, Supplier<T> supplier) {
        return loadingCache0(key, timeout, timeUnit, supplier, () -> get(key, type));
    }

    /**
     * 获取缓存或者当缓存不存在的直接载入缓存
     *
     * @param key           缓存键
     * @param typeReference 缓存数据类型引用，针对范性类型
     * @param timeout       缓存超时时间
     * @param timeUnit      时间单位
     * @param supplier      载入数据提供者
     * @return 缓存数据
     */
    public static <T> T loadingCache(String key, TypeReference<T> typeReference, long timeout, TimeUnit timeUnit, Supplier<T> supplier) {
        return loadingCache0(key, timeout, timeUnit, supplier, () -> get(key, typeReference));
    }

    /**
     * 直接返回缓存或者缓存数据为null时载入缓存数据
     */
    private static <T> T loadingCache0(String key, long timeout, TimeUnit timeUnit, Supplier<T> supplier, Supplier<T> cacheDataSupplier) {
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start("get");

        T returnCacheData = cacheDataSupplier.get();

        stopWatch.stop();
        log.debug("loadingCache:get(key = {}, time = {}ms)", key,
                stopWatch.getLastTaskTimeMillis());

        if (Objects.nonNull(returnCacheData)) {
            log.debug("loadingCache:hit(key={})", key);
            return returnCacheData;
        }

        stopWatch.start("lock");
        final ReentrantLock reentrantLock = exLockLoadingCache.get(key);
        Objects.requireNonNull(reentrantLock);
        try {
            if (!reentrantLock.tryLock(LOCK_TIMEOUT, TimeUnit.SECONDS)) {
                stopWatch.stop();
                log.debug("loadingCache:lock_fail(key={}, time={}ms)", key, stopWatch.getLastTaskTimeMillis());

                throw new SysException("缓存并发加载超时，请略微等待后重试");
            }
        } catch (InterruptedException e) {
            throw new SysException("缓存并发加载等待被打断");
        }

        try {
            stopWatch.stop();
            log.debug("loadingCache:lock(key={}, time={}ms)", key, stopWatch.getLastTaskTimeMillis());

            //锁等待时间超过10毫秒，认为其他线程可能已经完成了数据的加载，再获取一次数据做二次确认
            if (stopWatch.getLastTaskTimeMillis() > 10) {
                stopWatch.start("get2");

                returnCacheData = cacheDataSupplier.get();
                stopWatch.stop();

                log.debug("loadingCache:get2(key = {}, time = {}ms)", key,
                        stopWatch.getLastTaskTimeMillis());

                if (Objects.nonNull(returnCacheData)) {
                    log.debug("loadingCache:hit(key={})", key);
                    return returnCacheData;
                }
            }

            stopWatch.start("load");
            returnCacheData = supplier.get();

            stopWatch.stop();
            log.debug("loadingCache:load(key={}, time={}ms)", key, stopWatch.getLastTaskTimeMillis());

            stopWatch.start("set");
            set(key, returnCacheData, timeout, timeUnit);

            stopWatch.stop();
            log.debug("loadingCache:set(key={}, timeout={}, unit={}, time={}ms)",
                    key, timeout, timeUnit, stopWatch.getLastTaskTimeMillis());
            return returnCacheData;
        } finally {
            reentrantLock.unlock();
            log.debug("loadingCache:unlock(key={}, total_time={}ms)", key,
                    stopWatch.getTotalTimeMillis());
        }
    }

    /**
     * 扫描匹配key
     *
     * @param pattern  匹配模式
     * @param count    一次扫描多少key（不代表返回数量）
     * @param consumer 消费扫描结果
     */
    public static void scan(String pattern, Long count, Consumer<String> consumer) {
        final StringRedisTemplate stringRedisTemplate = getStringRedisTemplate();
        stringRedisTemplate.execute((RedisCallback<Object>) connection -> {

            RedisClusterConnection clusterConnection = null;
            if (connection instanceof RedisClusterConnection) {
                clusterConnection = (RedisClusterConnection) connection;
            }
            if (connection instanceof DecoratedRedisConnection
                    && ((DecoratedRedisConnection) connection).getDelegate() instanceof RedisClusterConnection) {
                clusterConnection = ((RedisClusterConnection) ((DecoratedRedisConnection) connection).getDelegate());
            }

            final ScanOptions scanOptions = new ScanOptions.ScanOptionsBuilder()
                    .match(new String(serializeKey(stringRedisTemplate, pattern)))
                    .count(count).build();

            if (clusterConnection != null) {
                for (RedisClusterNode clusterNode : clusterConnection.clusterGetNodes()) {
                    log.debug("SCAN NODE(host={},port={})", clusterNode.getHost(), clusterNode.getPort());

                    final Cursor<byte[]> cursor = clusterConnection.scan(clusterNode, scanOptions);
                    while (cursor.hasNext()) {
                        consumer.accept(new String(cursor.next()));
                    }
                }
            } else {
                final Cursor<byte[]> cursor = connection.scan(scanOptions);
                while (cursor.hasNext()) {
                    consumer.accept(new String(cursor.next()));
                }
            }
            return null;
        });
    }

    /**
     * 从左边塞入队列
     *
     * @param listKey
     * @param obj
     */
    public static void leftPushPush(String listKey, String obj) {
        StringRedisTemplate stringRedisTemplate = getStringRedisTemplate();
        stringRedisTemplate.opsForList().leftPush(listKey, obj);
    }

    /**
     * 使用指定redisTemplate配置的keySerializer对key进行序列化
     *
     * @param redisTemplate redisTemplate
     * @param key           key
     * @return 序列化后的字节数组
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    private static byte[] serializeKey(StringRedisTemplate redisTemplate, Object key) {
        final RedisSerializer keySerializer = redisTemplate.getKeySerializer();
        return keySerializer.serialize(key);
    }


    /**
     * 如果不存在则插入返回true, 如果已存在则返回false
     * @param key  缓存键
     * @param value  缓存值
     * @param timeout 缓存超时时间
     * @param unit   时间单位
     * @return
     */
    public static Boolean setIfAbsent(String key, String value, long timeout, TimeUnit unit){
        return getStringRedisTemplate().opsForValue().setIfAbsent(key, value, timeout,unit);
    }
}
