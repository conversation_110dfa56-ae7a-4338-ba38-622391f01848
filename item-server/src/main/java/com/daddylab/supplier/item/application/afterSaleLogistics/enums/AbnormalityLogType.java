package com.daddylab.supplier.item.application.afterSaleLogistics.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 异常日志类型 1:异常 2:备注 3:开启预警 4:关闭预警 5:记录次数记录 6:系统日志...
 *
 * <AUTHOR>
 * @since 2024/12/23
 */
@RequiredArgsConstructor
@Getter
public enum AbnormalityLogType implements IIntegerEnum {
    EXCEPTION(1, "异常"),
    REMARK(2, "备注"),
    OPEN_TRACE(3, "开启预警"),
    CLOSE_TRACE(4, "关闭预警"),
    COUNT(5, "记录次数"),
    SYSTEM(6, "系统日志"),
    ;

    private final Integer value;
    private final String desc;
}
