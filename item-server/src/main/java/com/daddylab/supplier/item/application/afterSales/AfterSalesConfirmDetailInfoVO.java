package com.daddylab.supplier.item.application.afterSales;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesConfirmUndertakeType;
import io.swagger.annotations.ApiModel;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/25
 */
@Data
@ApiModel("售后销退确认信息明细VO")
public class AfterSalesConfirmDetailInfoVO {

	/**
	 * 退换单明细ID
	 */
	private Integer recId;

	/**
	 * 货品编号
	 */
	private String goodsNo;

	/**
	 * 货品名称
	 */
	private String goodsName;

	/**
	 * 规格名称
	 */
	private String specName;

	/**
	 * 商家编码
	 */
	private String specNo;

	/**
	 * 数量
	 */
	private Integer quantity;

	/**
	 * 承担类型 1:按百分比 2:按金额
	 */
	private AfterSalesConfirmUndertakeType undertakeType;

	/**
	 * 承担金额/占比（按金额时单位为元）
	 */
	private BigDecimal undertakeAmount;
    

   
}
