package com.daddylab.supplier.item.controller.handingsheet.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/24 15:09
 * @Description: 删除盘货表店铺活动富文本内容参数
 */
@Data
@ApiModel("删除盘货表店铺活动富文本内容参数")
public class HandingSheetActivityTextDelParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "店铺活动富文本内容ID")
    private Long handingSheetActivityTextId;

    @ApiModelProperty(value = "当前用户ID", hidden = true)
    private Long currentUserId;
}
