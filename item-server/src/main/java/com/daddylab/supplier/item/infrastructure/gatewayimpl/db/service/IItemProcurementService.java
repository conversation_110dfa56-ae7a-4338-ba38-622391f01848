package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemProcurement;

/**
 * <p>
 * 商品采购设置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface IItemProcurementService extends IDaddyService<ItemProcurement> {
    /**
     * get by itemId
     *
     * @param itemId 商品 ID
     * @return ItemProcurement
     */
    ItemProcurement getByItemId(Long itemId);

    /**
     * 设置采购员用户ID
     * @param itemId 商品ID
     * @param buyerId 采购员 userId
     */
    boolean setBuyerId(Long itemId, Long buyerId);
}
