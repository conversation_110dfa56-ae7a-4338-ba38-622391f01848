package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> up
 * @date 2022年09月13日 3:19 PM
 */
@Getter
@AllArgsConstructor
public enum PlatformType implements IEnum<Integer> {
    /**
     *
     */
    WE_CHAT(1, "小程序"),
    TAO_BAO(2, "淘宝"),
    DOU_DIAN(3, "抖店");

    private final Integer value;
    private final String desc;
}
