package com.daddylab.supplier.item.infrastructure.utils;

import com.google.common.collect.Maps;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.reflect.Method;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class AspectUtil {
    final static Map<Object, Method> PJP_METHOD_CACHE = Maps.newHashMap();

    public static Method getMethod(ProceedingJoinPoint pjp) {
        return PJP_METHOD_CACHE.computeIfAbsent(pjp.getTarget(), k -> ((MethodSignature) pjp.getSignature()).getMethod());
    }

}
