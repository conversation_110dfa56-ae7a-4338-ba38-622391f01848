package com.daddylab.supplier.item.domain.purchase.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName PurchaseConfirm.java
 * @description 采购确认枚举
 * @createTime 2021年11月15日 11:33:00
 */
@Getter
@AllArgsConstructor
public enum PurchaseConfirm {
    ALLOW_Edit(0, "未发起确认"),
    NO_Edit(1, "已发起确认");
    final public Integer value;
    final public String desc;
}
