package com.daddylab.supplier.item;

import com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration;
import com.spring4all.swagger.EnableSwagger2Doc;
import lombok.extern.slf4j.Slf4j;
import org.flowable.spring.boot.RestApiAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.batch.BatchAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.Locale;

/**
 * 商品服务启动类
 *
 * <AUTHOR>
 */
@EnableScheduling
@EnableSwagger2
@EnableSwagger2Doc
@EnableFeignClients
@SpringBootApplication(exclude = {RestApiAutoConfiguration.class, PageHelperAutoConfiguration.class, BatchAutoConfiguration.class})
@EnableAspectJAutoProxy(exposeProxy = true)
@Slf4j
@EnableRetry
//@DubboComponentScan(basePackages = "com.daddylab.supplier.item.service")
public class ItemApplication {
    public static void main(String[] args) {
        try {
            Locale.setDefault(Locale.CHINA);
            new SpringApplicationBuilder(ItemApplication.class)
                    .logStartupInfo(true)
                    .run(args);
            log.info("---------- ItemApplication Start Successfully ----------");
        }catch (Exception e){
            e.printStackTrace();
        }
    }

}