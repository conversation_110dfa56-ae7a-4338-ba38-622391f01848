package com.daddylab.supplier.item.application.departments;

import com.alibaba.cola.dto.PageResponse;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.departments.DepartmentGateway;
import com.daddylab.supplier.item.types.DropdownItem;
import com.daddylab.supplier.item.types.departments.DepartmentDropdownQuery;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/3/31
 */
@Service("departmentServiceImplTwo")
@Slf4j
@AllArgsConstructor
public class DepartmentServiceImpl implements DepartmentService {
    DepartmentGateway departmentGateway;

    @Override
    public PageResponse<DropdownItem<Long>> departmentDropdownList(DepartmentDropdownQuery query) {
        try {
            return departmentGateway.departmentDropdownList(query);
        } catch (Exception e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "从权限系统查询部门列表失败");
        }
    }
}
