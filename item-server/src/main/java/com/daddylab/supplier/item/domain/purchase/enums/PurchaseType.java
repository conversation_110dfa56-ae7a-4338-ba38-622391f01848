package com.daddylab.supplier.item.domain.purchase.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName PurchaseType.java
 * @description
 * @createTime 2021年11月19日 22:05:00
 */
@Getter
@AllArgsConstructor
public enum PurchaseType  implements IEnum<Integer> {
    SALE(0, "大促"),
    LIVE(1, "直播"),
    NOT_HAVE(2, "无");
    final public Integer value;
    final public String desc;
}
