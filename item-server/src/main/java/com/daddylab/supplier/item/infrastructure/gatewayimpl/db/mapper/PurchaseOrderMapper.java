package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderPageQuery;
import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderPageVO;
import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderViewVO;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrder;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 采购订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Repository
public interface PurchaseOrderMapper extends DaddyBaseMapper<PurchaseOrder> {

    /**
     * 采购订单分页查询
     *
     * @param param
     * @return
     */
    List<PurchaseOrderPageVO> page(@Param("param") PurchaseOrderPageQuery param);

//    /**
//     * 采购订单分页查询计数
//     *
//     * @param param
//     * @return
//     */
//    Integer pageCount(@Param("param") PurchaseOrderPageQuery param);

    /**
     * 查看采购单详情
     *
     * @param purchaseId
     * @return
     */
    PurchaseOrderViewVO getView(@Param("id") Long purchaseId);

    /**
     * 自动根据采购入库情况更新采购单状态
     *
     * @param purchaseOrderId 采购单ID
     * @return 更新记录数量
     */
    int autoUpdatePurchaseOrderState(@Param("id") long purchaseOrderId);

    /**
     * 查询skuCode仓库编号
     *
     * @param skuCode
     * @return
     */
    String getWarehouseNo(@Param("skuCode") String skuCode);

    void deleteSysOrder(@Param("start") Long start, @Param("end") Long end);


    PurchaseOrder getById(@Param("id") Long id);

    List<PurchaseOrder> listBySkuCode(@Param("skuNo") String skuNo);
}
