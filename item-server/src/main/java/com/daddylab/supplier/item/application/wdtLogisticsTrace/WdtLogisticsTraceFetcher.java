package com.daddylab.supplier.item.application.wdtLogisticsTrace;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.apiv2.statistic.GoodsSendStatisticAPI;
import com.daddylab.mall.wdtsdk.apiv2.statistic.dto.SearchLogisticsTraceParams;
import com.daddylab.mall.wdtsdk.apiv2.statistic.dto.SearchLogisticsTraceResponse;
import com.daddylab.supplier.item.domain.dataFetch.*;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtLogisticsTrace;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtLogisticsTraceService;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2024/11/29
 */
@Slf4j
public class WdtLogisticsTraceFetcher implements PageFetcher {

  private final WdtGateway wdtGateway;
  private final IWdtLogisticsTraceService wdtLogisticsTraceService;

  /**
   * 物流状态 0:待查询 * 1:待取件 * 2:已取件 * 3:在途中 * 4:待配送 * 5:已签收 * 6:拒收 * 7:已处理 8:未订阅(不支持物流) 9:退件中 * 10:已退签
   * 11:问题件 不传默认为已签收(不支持一次传入多个状态)
   */
  @SuppressWarnings("unused")
  private @Setter @Getter Integer logisticsStatus;

  private final int configId;

  public WdtLogisticsTraceFetcher(
      WdtGateway wdtGateway,
      IWdtLogisticsTraceService wdtLogisticsTraceService,
      Map<String, Object> parameters) {
    this.wdtGateway = wdtGateway;
    this.wdtLogisticsTraceService = wdtLogisticsTraceService;
    this.logisticsStatus = (Integer) parameters.get("logisticsStatus");
    this.configId =
        Optional.ofNullable(parameters.get("configId")).map(it -> (Integer) it).orElse(0);
    fetchDataType();
  }

  @Override
  public FetchDataType fetchDataType() {
    switch (logisticsStatus) {
      case 1:
        return FetchDataType.WDT_LOGISTICS_TRACE1;
      case 2:
        return FetchDataType.WDT_LOGISTICS_TRACE2;
      case 3:
        return FetchDataType.WDT_LOGISTICS_TRACE3;
      case 4:
        return FetchDataType.WDT_LOGISTICS_TRACE4;
      case 5:
        return FetchDataType.WDT_LOGISTICS_TRACE5;
      case 6:
        return FetchDataType.WDT_LOGISTICS_TRACE6;
      case 7:
        return FetchDataType.WDT_LOGISTICS_TRACE7;
      case 9:
        return FetchDataType.WDT_LOGISTICS_TRACE9;
      case 10:
        return FetchDataType.WDT_LOGISTICS_TRACE10;
      case 11:
        return FetchDataType.WDT_LOGISTICS_TRACE11;
    }
    throw new IllegalArgumentException("[旺店通物流轨迹同步]物流轨迹状态入参异常:" + logisticsStatus);
  }

  @Override
  public int getTotal() {
    return PageFetcher.super.getTotal();
  }

  @Override
  public int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
    final SearchLogisticsTraceResponse logisticsTraceResponse =
        query(startTime, endTime, 1, 1, true, logisticsStatus, false);
    return logisticsTraceResponse.getTotalCount();
  }

  @Override
  public void fetch(
      LocalDateTime startTime,
      LocalDateTime endTime,
      long pageIndex,
      long pageSize,
      RunContext runContext) {
    final SearchLogisticsTraceResponse response0 =
        query(startTime, endTime, (int) pageIndex, (int) pageSize, false, logisticsStatus, true);
    handleResponse(response0);
  }

  private void handleResponse(SearchLogisticsTraceResponse response) {
    if (CollUtil.isEmpty(response.getOrderList())) {
      return;
    }
    final List<WdtLogisticsTrace> entities =
        response.getOrderList().stream()
            .map(WdtLogisticsTraceConvert.INSTANCE::toEntity)
            .collect(Collectors.toList());
    final List<String> logisticsNos =
        entities.stream().map(WdtLogisticsTrace::getLogisticsNo).collect(Collectors.toList());
    final Map<String, Long> idMap = wdtLogisticsTraceService.mapIdByLogisticsNos(logisticsNos);
    entities.forEach(
        it -> {
          if (idMap.containsKey(it.getLogisticsNo())) {
            it.setId(idMap.get(it.getLogisticsNo()));
          }
        });
    final List<WdtLogisticsTrace> newEntities =
        entities.stream().filter(it -> Objects.isNull(it.getId())).collect(Collectors.toList());
    final List<WdtLogisticsTrace> updateEntities =
        entities.stream().filter(it -> Objects.nonNull(it.getId())).collect(Collectors.toList());
    if (!newEntities.isEmpty()) {
      wdtLogisticsTraceService.saveBatch(newEntities);
      dispatchEvents(newEntities);
    }
    if (!updateEntities.isEmpty()) {
      wdtLogisticsTraceService.updateBatchById(updateEntities);
      dispatchEvents(updateEntities);
    }
  }

  private static void dispatchEvents(List<WdtLogisticsTrace> newEntities) {
    for (WdtLogisticsTrace newEntity : newEntities) {
      EventBusUtil.post(new WdtLogisticsTraceEvent(newEntity));
    }
  }

  private SearchLogisticsTraceResponse query(
      LocalDateTime st,
      LocalDateTime et,
      int pageNo,
      int pageSize,
      boolean calcTotal,
      int logisticsStatus,
      boolean needDetail) {
    if (pageSize > 100) {
      throw new IllegalArgumentException("pageSize不能大于100");
    }
    try {
      final GoodsSendStatisticAPI api =
          wdtGateway.getAPI(GoodsSendStatisticAPI.class, configId, true);
      final Pager pager = new Pager(pageSize, pageNo - 1, calcTotal);
      final SearchLogisticsTraceParams params = new SearchLogisticsTraceParams();
      params.setLogisticsStatus(logisticsStatus);
      params.setNeedDetail(needDetail);
      params.setStartTime(st.format(DatePattern.NORM_DATETIME_FORMATTER));
      params.setEndTime(et.format(DatePattern.NORM_DATETIME_FORMATTER));

      return api.searchLogisticsTrace(params, pager);
    } catch (Exception e) {
      throw WdtExceptions.wrapFetchException(e, fetchDataType());
    }
  }
}
