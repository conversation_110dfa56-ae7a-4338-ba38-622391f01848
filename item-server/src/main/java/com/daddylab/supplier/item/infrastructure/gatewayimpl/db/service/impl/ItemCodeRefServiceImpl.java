package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemCodeRef;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemCodeRefMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemCodeRefService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 各种商品编码与后端商品的关联记录 服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-21
 */
@Service
public class ItemCodeRefServiceImpl extends DaddyServiceImpl<ItemCodeRefMapper, ItemCodeRef>
        implements IItemCodeRefService {

    @Override
    public int refresh() {
        int count = 0;
        final ItemCodeRefMapper mapper = getDaddyBaseMapper();
        mapper.clearAll();
        count += mapper.scanItemCode();
        count += mapper.scanProviderSpecifiedCode();
        count += mapper.scanPsysCode();
        count += mapper.scanSkuCode();
        count += mapper.scanSkuProviderSpecifiedCode();
        count += mapper.scanRefItemCode();
        count += mapper.scanRefSkuCode1();
        count += mapper.scanRefSkuCode2();
        return count;
    }

    @Override
    public List<ItemCodeRef> selectByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(ItemCodeRef::getCode, codes).list();
    }
}
