package com.daddylab.supplier.item.controller.open;

import cn.hutool.crypto.digest.DigestUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.NonNull;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @since 2021/12/31
 */
@Configuration
public class OpenApiConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new HandlerInterceptor() {
            @Override
            public boolean preHandle(@NonNull HttpServletRequest request,
                    @NonNull HttpServletResponse response, @NonNull Object handler) {
                final String openAuth = request.getHeader("OPEN-AUTH");
                return StringUtil.isNotBlank(openAuth) && StringUtil
                        .equals(DigestUtil.md5Hex(openAuth), "3c819871f5b53a89814e0a007ffc5b5a");
            }
        }).order(Ordered.HIGHEST_PRECEDENCE).addPathPatterns("/open/api/**");
    }

    public static void main(String[] args) {
        String s = "bqPNnZUdMmNAUUXbw9fX76f_w4ai!BN6";
        System.out.println(DigestUtil.md5Hex(s));

    }

}