package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemPriceRang;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemPriceType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 商品价格
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemPrice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品ID
     */
    private Long itemId;

    /**
     * 价格类型 1:采购成本 2:日常销售价 3:划线价格 4:产品活动价 5:渠道最低价 6:自定义价格
     */
    private ItemPriceType type;

    /**
     * 是否为主价格
     */
    private Integer isMain;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 自定义价格名称
     */
    private String customName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否长期有效
     */
    private Integer isLongTerm;

    /**
     * 当前成本价所属时间范围 开始时间
     */
    private Long startTime;

    /**
     * 当前成本价所属时间范围 结束时间
     */
    private Long endTime;

    /**
     * 删除时间
     */
    private Long deletedAt;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 是否是采购价格
     */
    private ItemPriceRang rang;


}
