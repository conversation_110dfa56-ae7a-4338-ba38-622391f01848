package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CategoryAttr;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 类目属性 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Repository
public interface CategoryAttrMapper extends DaddyBaseMapper<CategoryAttr> {

}
