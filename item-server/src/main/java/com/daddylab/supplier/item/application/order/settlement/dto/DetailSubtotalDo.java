package com.daddylab.supplier.item.application.order.settlement.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2023年08月09日 5:08 PM
 */
@Data
@ApiModel("采购单结算明细，小计部分统计")
public class DetailSubtotalDo {

    @ApiModelProperty("小计-暂估结算数量")
    private Integer temporaryQuantity;

    @ApiModelProperty("小计-订单发货数量")
    private Integer deliverQuantity;

    @ApiModelProperty("小计-当月退货。返回正数")
    public Integer currentMonthRefundQuantity;

    @ApiModelProperty("小计-跨月退货。返回正数")
    public Integer crossMonthRefundQuantity;


}
