package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTag;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemTagMapper;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 商品标签 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
public interface IItemTagService extends IDaddylabServicePlus<ItemTag, ItemTagMapper> {

    List<ItemTag> listByItemId(Long itemId);
    List<ItemTag> listByItemIds(Collection<Long> itemIds);
}
