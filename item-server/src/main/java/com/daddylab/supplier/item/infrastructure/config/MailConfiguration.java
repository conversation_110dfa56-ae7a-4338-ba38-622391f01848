//package com.daddylab.supplier.item.infrastructure.config;
//
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.mail.javamail.JavaMailSenderImpl;
//
///**
// * <AUTHOR> up
// * @date 2022/3/21 4:31 下午
// */
//@Configuration
//@ConfigurationProperties(prefix = "spring.mail")
//@Data
//public class MailConfiguration {
//
//    private String protocol;
//    private String host;
//    private String port;
//    private String username;
//    private String password;
//
//
//    @Bean
//    public JavaMailSenderImpl JavaMailSender() {
//        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
//        mailSender.setProtocol(protocol);
//        mailSender.setHost(host);
//        mailSender.setUsername(username);
//        mailSender.setPassword(password);
//        mailSender.setPort(Integer.parseInt(port));
//        return mailSender;
//    }
//
//}
