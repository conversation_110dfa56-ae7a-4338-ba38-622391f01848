package com.daddylab.supplier.item.infrastructure.spring.web;

import com.alibaba.cola.dto.Response;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.util.Objects;

/**
 * 针对返回类型是void的controller做自动封装处理
 *
 * <AUTHOR> up
 * @date 2024年01月04日 11:35 AM
 */
@ControllerAdvice
public class VoidResponseAdvice implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, @NotNull Class converterType) {
        boolean b1 = Objects.requireNonNull(returnType.getMethod()).getReturnType().equals(Void.TYPE);
        boolean b2 = MappingJackson2HttpMessageConverter.class.isAssignableFrom(converterType);
        return b1 && b2;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        return Response.buildSuccess();
    }
}
