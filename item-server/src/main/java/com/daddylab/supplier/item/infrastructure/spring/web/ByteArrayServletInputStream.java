package com.daddylab.supplier.item.infrastructure.spring.web;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;

/**
 * <AUTHOR>
 * @since 2022/10/17
 */
class ByteArrayServletInputStream extends ServletInputStream {

    private final InputStream delegate;

    public ByteArrayServletInputStream(byte[] bytes) {
        this.delegate = new ByteArrayInputStream(bytes);
    }

    @Override
    public boolean isFinished() {
        try {
            return delegate.available() == 0;
        } catch (IOException e) {
            return true;
        }
    }

    @Override
    public boolean isReady() {
        return true;
    }

    @Override
    public void setReadListener(ReadListener listener) {
        throw new UnsupportedOperationException();
    }

    @Override
    public int read() throws IOException {
        return delegate.read();
    }
}
