package com.daddylab.supplier.item.application.afterSalesForwarding.types;

import com.daddylab.supplier.item.common.domain.dto.Command;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.PositiveOrZero;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/27
 */
@Data
public class AfterSalesForwardingRegisterCreateShareLinkCmd extends Command {
    private static final long serialVersionUID = -3350179536958637269L;

    @ApiModelProperty(value = "分享链接名称", example = "分享链接")
    @NotBlank
    private String linkName;

    @ApiModelProperty(value = "过期时间，单位秒，长期有效传0", example = "3600")
    @PositiveOrZero
    private Integer expireSeconds;

    @NotEmpty
    @ApiModelProperty(value = "邀请的手机号列表", example = "[\"13800000000\"]")
    private List<String> inviteMobiles;
}
