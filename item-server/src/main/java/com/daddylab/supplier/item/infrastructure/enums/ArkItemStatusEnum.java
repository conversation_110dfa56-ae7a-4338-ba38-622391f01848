package com.daddylab.supplier.item.infrastructure.enums;

import com.daddylab.ark.sailor.item.domain.vo.item.ItemListVO;
import com.daddylab.ark.sailor.item.enums.ItemAuditStatusEnum;
import com.daddylab.ark.sailor.item.enums.ItemShelfStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * Ark商品状态枚举
 * 用于映射商品的审核状态和上架状态到统一的Ark状态
 * 
 * <AUTHOR>
 * @since 2025-06-25
 */
@Getter
@AllArgsConstructor
public enum ArkItemStatusEnum implements IEnum<String> {

    /**
     * 出售中
     */
    ON_SALE("ON_SALE", "出售中"),

    /**
     * 仓库中/待上架
     */
    WAIT_SALE("WAIT_SALE", "仓库中"),

    /**
     * 审核中
     */
    AUDITING("AUDITING", "审核中"),

    /**
     * 平台强制下架
     */
    FORCE_OFF_SHELF("FORCE_OFF_SHELF", "平台强制下架");

    private final String value;
    private final String desc;

    /**
     * 根据商品信息获取Ark状态
     * 
     * 状态映射规则：
     * 1. 如果审核状态为AUDITING，返回AUDITING
     * 2. 如果上架状态为ON_SHELF，返回ON_SALE
     * 3. 如果上架状态为OFF_SHELF，返回FORCE_OFF_SHELF
     * 4. 其他情况返回WAIT_SALE
     * 
     * @param item 商品信息
     * @return Ark状态字符串
     */
    public static ArkItemStatusEnum getStatusByItem(ItemListVO item) {
        if (item == null) {
            return WAIT_SALE;
        }
        // 优先检查审核状态
        if (Objects.equals(item.getAuditStatus(), ItemAuditStatusEnum.AUDITING.getValue())) {
            return AUDITING;
        }
        // 检查上架状态
        if (Objects.equals(ItemShelfStatusEnum.ON_SHELF.getValue(), item.getShelfStatus())) {
            return ON_SALE;
        } else if (Objects.equals(ItemShelfStatusEnum.OFF_SHELF.getValue(), item.getShelfStatus())) {
            return FORCE_OFF_SHELF;
        }

        // 默认状态
        return WAIT_SALE;
    }

    /**
     * 根据状态值获取枚举实例
     * 
     * @param value 状态值
     * @return 对应的枚举实例，如果未找到返回null
     */
    public static ArkItemStatusEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        
        for (ArkItemStatusEnum status : values()) {
            if (Objects.equals(status.getValue(), value)) {
                return status;
            }
        }
        return null;
    }
}
