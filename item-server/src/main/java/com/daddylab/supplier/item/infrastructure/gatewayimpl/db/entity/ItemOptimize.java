package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizePersistData;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.DiffInclude;

import java.io.Serializable;

/**
 * 商品优化
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@EqualsAndHashCode(
        callSuper = true,
        of = {
            "outerItemId",
            "platformItemId",
            "itemId",
            "data",
            "submitUid",
            "submitAt",
            "itemCode"
        })
public class ItemOptimize extends Entity implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城 */
    @ApiModelProperty("平台")
    private Integer platform;

    /** 商品优化计划ID */
    private Long planId;

    /** 对应销售平台的货品ID */
    @DiffInclude private String outerItemId;

    /** 平台货品ID（系统内部的平台货品），未匹配为0 */
    @DiffInclude private Long platformItemId;

    /** 商品ID，未匹配为0 */
    @DiffInclude private Long itemId;

    /** 商品SPU编码 */
    @ApiModelProperty("商品SPU")
    private String itemCode;

    /** 状态 0 待提交 1 已提交 2 待QC审核 3 待法务审核 4 待修改 5 已完成 */
    private Integer status;

    /** 优化计划状态 0 未提交 1 已提交 */
    private Integer planStatus;

    /** 当前流程实例ID */
    private String processInstId;

    /** 商品优化模型数据 */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ItemOptimizePersistData data;

    /** 版本号（乐观锁） */
    private Integer v;

    @ApiModelProperty("提交人")
    @DiffIgnore
    private Long submitUid;

    @ApiModelProperty("提交时间")
    @DiffIgnore
    private Long submitAt;

    @ApiModelProperty("最后提交人")
    @DiffIgnore
    private Long lastSubmitUid;

    @ApiModelProperty("最后提交时间")
    @DiffIgnore
    private Long lastSubmitAt;

    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private String linkTitle;

    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Integer psysType;

    @ApiModelProperty("第几次复审")
    @DiffIgnore
    private Integer reviewNum;

    /**
     * 增加复审次数
     */
    @TableField(value = "review_num", update = "%s + #{et.addReviewNum}", select = false)
    @DiffIgnore
    private Integer addReviewNum;

}
