package com.daddylab.supplier.item.controller.shop;

import com.daddylab.supplier.item.application.shop.ShopAuthorizationBizService;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024/4/9
 */
@RequestMapping("/shop/authorization")
@Api(value = "店铺授权相关api", tags = "店铺授权相关api")
@RestController
public class ShopAuthorizationController {
    @Autowired
    private ShopAuthorizationBizService shopAuthorizationBizService;

    @RequestMapping(value = "/authorize", produces = {MediaType.TEXT_HTML_VALUE})
    @Auth(noAuth = true)
    public String authorize(String shopNo) {
        return shopAuthorizationBizService.authorize(shopNo);
    }

    @RequestMapping("/callback/redBook")
    @Auth(noAuth = true)
    public String redBookCallback(String code, String state) {
        return shopAuthorizationBizService.authorizationCallback(Platform.XIAOHONGSHU, code, state);
    }

    @RequestMapping("/callback/kuaishou")
    @Auth(noAuth = true)
    public String kuaishouCallback(String code, String state) {
        return shopAuthorizationBizService.authorizationCallback(Platform.KUAISHOU, code, state);
    }

}
