package com.daddylab.supplier.item.domain.region.service;

import com.daddylab.supplier.item.domain.region.entity.RegionTree;
import com.daddylab.supplier.item.domain.region.gateway.RegionGateway;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.inject.Inject;

@Service
@Slf4j
public class RegionDomainServiceImpl implements RegionDomainService, InitializingBean {
    @Inject
    RegionGateway regionGateway;

    @Value("${loadRegionTreeEarly:false}")
    private Boolean loadRegionTreeEarly;

    private RegionTree regionTree;


    @Override
    public void afterPropertiesSet() throws Exception {
        if (Boolean.TRUE.equals(loadRegionTreeEarly)) {
            loadRegionTree();
        }
    }

    private void loadRegionTree() {
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        regionTree = new RegionTree(regionGateway.getRegions());
        stopWatch.stop();
        log.info("地区树形结构完成初始化,消耗时间:{}ms", stopWatch.getTotalTimeMillis());
    }

    @Override
    public void clearRegionTreeCache() {
        regionTree = null;
    }

    @Override
    public RegionTree getRegionTree() {
        if (regionTree == null) {
            synchronized (this) {
                if (regionTree == null) {
                    loadRegionTree();
                }
            }
        }
        return regionTree;
    }
}
