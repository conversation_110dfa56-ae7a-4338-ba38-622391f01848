//package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;
//
//import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopInventory;
//
///**
// * <p>
// * 店铺分配库存信息 服务类
// * </p>
// *
// * <AUTHOR>
// * @since 2024-02-29
// */
//public interface IShopInventoryService extends IDaddyService<ShopInventory> {
//
//}
