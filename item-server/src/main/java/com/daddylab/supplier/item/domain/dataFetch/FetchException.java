package com.daddylab.supplier.item.domain.dataFetch;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2022/4/29
 */
public class FetchException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private FetchDataType fetchDataType;
    private LocalDateTime startTime;
    private LocalDateTime endTime;

    public FetchException(FetchSegment fetchSegment) {
        initByFetchSegment(fetchSegment);
    }

    public FetchException(FetchDataType fetchDataType) {
        this.fetchDataType = fetchDataType;
    }

    public FetchException(String message,
            FetchDataType fetchDataType) {
        super(message);
        this.fetchDataType = fetchDataType;
    }

    public FetchException(Throwable cause,
            FetchDataType fetchDataType) {
        super(cause);
        this.fetchDataType = fetchDataType;
    }

    public FetchException(FetchSegment fetchSegment, String message) {
        super(message);
        initByFetchSegment(fetchSegment);
    }

    public FetchException(FetchDataType fetchDataType, String message) {
        super(message);
        this.fetchDataType = fetchDataType;
    }

    private void initByFetchSegment(FetchSegment fetchSegment) {
        this.fetchDataType = fetchSegment.getDataType();
        this.startTime = fetchSegment.getStartTime();
        this.endTime = fetchSegment.getEndTime();
    }

    @Override
    public String getMessage() {
        if (startTime == null || endTime == null) {
            return "拉取" + fetchDataType.getDesc() + "数据失败"
                    + (super.getMessage() != null ? ":" + super.getMessage() : "");
        }
        return "拉取区间为" + "[" + startTime + "," + endTime + "]的" + fetchDataType.getDesc() + "数据失败"
                + (super.getMessage() != null ? ":" + super.getMessage() : "");
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }
}
