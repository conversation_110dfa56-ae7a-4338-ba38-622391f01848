package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ReUtil;
import com.alibaba.cola.dto.PageResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.application.platformItem.data.PlatformItemListItem;
import com.daddylab.supplier.item.application.platformItem.query.PlatformItemPageQuery;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryAllocShopStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemSkuService;

import java.util.*;
import java.util.stream.Collectors;

import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.github.yulichang.toolkit.MPJWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * 平台商品（投放到其他平台的商品）商品维度 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Service
public class PlatformItemServiceImpl extends DaddyServiceImpl<PlatformItemMapper, PlatformItem>
        implements IPlatformItemService {
    
    @Autowired
    IPlatformItemSkuService platformItemSkuService;
    
    @Autowired
    private PlatformItemMapper platformItemMapper;
    
    @Override
    public IPage<PlatformItem> pageQuery(PlatformItemPageQuery pageQuery) {
        final Page<PlatformItem> emptyPage = new Page<>();
        final IPage<PlatformItem> page = pageQuery.getPage();
        final MPJLambdaWrapper<PlatformItem> queryWrapper = MPJWrappers.lambdaJoin();
        queryWrapper.selectAll(PlatformItem.class);
        if (StringUtil.isNotBlank(pageQuery.getKeyword())) {
            if (ReUtil.isMatch("[a-ZA-Z0-9]+", pageQuery.getKeyword())) {
                pageQuery.setOuterItemId(pageQuery.getKeyword());
            } else {
                pageQuery.setItemName(pageQuery.getKeyword());
            }
        }
        if (pageQuery.getLockEnabled() != null) {
            queryWrapper.eq(PlatformItem::getLockEnabled, pageQuery.getLockEnabled());
        }
        if (pageQuery.getSyncEnabled() != null) {
            queryWrapper.innerJoin(InventoryAllocShop.class, InventoryAllocShop::getShopNo, PlatformItem::getShopNo)
                    .ne(InventoryAllocShop::getStatus, InventoryAllocShopStatus.FORBIDDEN);
            if (pageQuery.getSyncEnabled()) {
                queryWrapper.and(
                        q -> q.eq(PlatformItem::getSyncEnabled, true).or().isNull(PlatformItem::getSyncEnabled));
            } else {
                queryWrapper.eq(PlatformItem::getSyncEnabled, false);
            }
        }
        queryWrapper.leftJoin(Shop.class, Shop::getSn, PlatformItem::getShopNo);
        queryWrapper.eq(
                StringUtil.isNotBlank(pageQuery.getOuterItemId()),
                PlatformItem::getOuterItemId,
                pageQuery.getOuterItemId());
        queryWrapper.eq(
                CollectionUtil.isNotEmpty(pageQuery.getOuterItemIds()),
                PlatformItem::getOuterItemId,
                pageQuery.getOuterItemIds());
        queryWrapper.eq(
                NumberUtil.isPositive(pageQuery.getPlatformItemId()),
                PlatformItem::getId,
                pageQuery.getPlatformItemId());
        queryWrapper.in(
                CollectionUtil.isNotEmpty(pageQuery.getPlatformItemIds()),
                PlatformItem::getId,
                pageQuery.getPlatformItemIds());
        queryWrapper.eq(
                NumberUtil.isPositive(pageQuery.getItemId()),
                PlatformItem::getItemId,
                pageQuery.getItemId());
        queryWrapper.eq(
                NumberUtil.isPositive(pageQuery.getShopId()),
                PlatformItem::getShopId,
                pageQuery.getShopId());
        queryWrapper.eq(
                StringUtil.isNotBlank(pageQuery.getShopNo()),
                PlatformItem::getShopNo,
                pageQuery.getShopNo());
        queryWrapper.eq(
                Objects.nonNull(pageQuery.getPlatform()),
                PlatformItem::getPlatform,
                pageQuery.getPlatform());
        queryWrapper.in(
                CollectionUtil.isNotEmpty(pageQuery.getPlatforms()),
                PlatformItem::getPlatform,
                pageQuery.getPlatforms());
        queryWrapper.eq(
                StringUtil.isNotBlank(pageQuery.getItemCode()),
                PlatformItem::getOuterItemCode,
                pageQuery.getItemCode());
        if (StringUtil.isNotBlank(pageQuery.getSkuCode())) {
            final List<Long> platformItemIds =
                    platformItemSkuService
                            .lambdaQuery()
                            .eq(PlatformItemSku::getOuterSkuCode, pageQuery.getSkuCode())
                            .select(PlatformItemSku::getPlatformItemId)
                            .list()
                            .stream()
                            .map(PlatformItemSku::getPlatformItemId)
                            .collect(Collectors.toList());
            if (platformItemIds.isEmpty()) {
                return emptyPage;
            }
            queryWrapper.in(PlatformItem::getId, platformItemIds);
        }
        queryWrapper.eq(
                Objects.nonNull(pageQuery.getStatus()),
                PlatformItem::getStatus,
                pageQuery.getStatus());
        queryWrapper.in(
                CollectionUtil.isNotEmpty(pageQuery.getStatusIn()),
                PlatformItem::getStatus,
                pageQuery.getStatusIn());
        queryWrapper.eq(
                Objects.nonNull(pageQuery.getCategoryId()),
                PlatformItem::getCategoryId,
                pageQuery.getCategoryId());
        queryWrapper.like(
                StringUtil.isNotBlank(pageQuery.getItemName()),
                PlatformItem::getGoodsName,
                pageQuery.getItemName());
        
        queryWrapper.orderByDesc(PlatformItem::getId);
        page(page, queryWrapper);
        return page;
    }
    
    private static void setBusinessLineFilter(
            MPJLambdaWrapper<PlatformItem> queryWrapper, List<Integer> businessLine) {
        if (businessLine != null) {
            queryWrapper.and(
                    q -> {
                        for (Integer line : businessLine) {
                            switch (line) {
                                case 0:
                                    q.or().eq(Shop::getIsBusinessLine0, 1);
                                    break;
                                case 1:
                                    q.or().eq(Shop::getIsBusinessLine1, 1);
                                    break;
                                case 2:
                                    q.or().eq(Shop::getIsBusinessLine2, 1);
                                    break;
                                case 3:
                                    q.or().eq(Shop::getIsBusinessLine3, 1);
                                    break;
                            }
                        }
                    });
        }
    }
    
    @Override
    public List<PlatformItem> listByItemId(Long itemId) {
        QueryWrapper<PlatformItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PlatformItem::getItemId, itemId);
        return platformItemMapper.selectList(queryWrapper);
    }
    
    @Override
    public void updateLastAllocTime(Collection<Long> platformItemIds, Long lastAllocTime) {
        if (CollectionUtil.isEmpty(platformItemIds)) {
            return;
        }
        lambdaUpdate().in(PlatformItem::getId, platformItemIds)
                .set(PlatformItem::getLastStockAllocTime, lastAllocTime)
                .update();
    }
    
    @Override
    public void updateLastStockSyncTime(Collection<Long> platformItemIds, Long syncTime) {
        lambdaUpdate().in(PlatformItem::getId, platformItemIds)
                .set(PlatformItem::getLastStockSyncTime, syncTime)
                .update();
    }
    
    @Override
    public void updateLastStockSyncTime(PlatformItem platformItem) {
        lambdaUpdate().eq(PlatformItem::getId, platformItem.getId())
                .set(PlatformItem::getLastStockSyncTime, platformItem.getLastStockSyncTime())
                .update();
    }
    
    @Override
    public List<PlatformItem> listByShopNo(String shopNo) {
        return lambdaQuery().eq(PlatformItem::getShopNo, shopNo).list();
    }
    
    @Override
    public List<PlatformItem> listByOuterItemId(List<String> outerItemIds) {
        if (CollectionUtil.isEmpty(outerItemIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(PlatformItem::getOuterItemId, outerItemIds).list();
    }
}
