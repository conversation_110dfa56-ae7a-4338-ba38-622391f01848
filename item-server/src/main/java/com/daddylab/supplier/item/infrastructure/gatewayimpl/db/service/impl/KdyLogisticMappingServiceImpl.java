package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.KdyLogisticMapping;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.KdyLogisticMappingMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IKdyLogisticMappingService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 快刀云物流公司名称映射 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Service
public class KdyLogisticMappingServiceImpl extends DaddyServiceImpl<KdyLogisticMappingMapper, KdyLogisticMapping> implements IKdyLogisticMappingService {

    @Override
    public Optional<String> getStdNameByLogisticsName(String logisticsName) {
        return lambdaQuery().eq(KdyLogisticMapping::getAliaName, logisticsName)
                            .or()
                            .eq(KdyLogisticMapping::getStdName, logisticsName)
                            .list()
                            .stream()
                            .map(KdyLogisticMapping::getStdName)
                            .findFirst();
    }

    @Override
    public List<String> inferStdName(String logisticsName) {
        return getDaddyBaseMapper().inferStdName(logisticsName);
    }
}
