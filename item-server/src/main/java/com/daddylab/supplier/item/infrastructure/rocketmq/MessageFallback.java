package com.daddylab.supplier.item.infrastructure.rocketmq;

import org.apache.rocketmq.client.producer.SendResult;

/**
 * <AUTHOR>
 * @since 2022/2/18
 */
@FunctionalInterface
public interface MessageFallback {
    /**
     * 消息发送异常降级策略
     * @param payload 消息负载
     * @param topic topic
     * @param tag tag
     * @param keys 业务唯一标识
     * @param sendResult 发送结果
     * @param throwable 异常
     * @return 是否降级成功
     */
    boolean fallback(Object payload, String topic, String tag, String keys, SendResult sendResult, Throwable throwable);
}
