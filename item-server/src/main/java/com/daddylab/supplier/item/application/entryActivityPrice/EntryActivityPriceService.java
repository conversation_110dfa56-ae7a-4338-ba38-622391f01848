package com.daddylab.supplier.item.application.entryActivityPrice;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.entryActivityPrice.models.*;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @since 2024/10/4
 */
public interface EntryActivityPriceService {
    PageResponse<EntryActivityPricePageVO> page(EntryActivityPricePageQry qry);

    SingleResponse<EntryActivityPriceItemInfoVO> itemInfo(EntryActivityPriceItemInfoQry qry);

    SingleResponse<EntryActivityPriceSkuInfoVO> skuInfo(EntryActivityPriceSkuInfoQry qry);

    Response importExcel(InputStream inputStream);

    Response exportExcel(EntryActivityPricePageQry query);

    Response save(EntryActivityPriceSaveReq cmd);

    Response delete(EntryActivityPriceDeleteReq cmd);

    SingleResponse<PrepareStartConfirmationResult> prepareStartConfirmation(PrepareStartConfirmationReq req);

    Response startConfirmation(StartConfirmationReq req);

    SingleResponse<ConfirmationViewVO> confirmationView(ConfirmationViewReq req);

    Response confirm(ConfirmReq req);

    Response dissent(DissentReq req);
    Response updateDissent(DissentReq req);

    SingleResponse<ViewVoucherVO> viewVoucher(ViewVoucherReq req);

    Response sendCode(SendCodeReq req);

    SingleResponse<String> token(TokenReq req);

    Response checkToken(String token);
}
