package com.daddylab.supplier.item.application.ItemTrainingMaterials;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTrainingMaterials;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTrainingMaterialsProcess;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTrainingMaterialsProcessRecord;
import com.daddylab.supplier.item.types.itemTrainingMaterials.ItemTrainingMaterialsData;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/4/22
 */
@Mapper
public interface ItemTrainingMaterialsAssembler {
    ItemTrainingMaterialsAssembler instance = Mappers.getMapper(ItemTrainingMaterialsAssembler.class);

    ItemTrainingMaterialsData copy(ItemTrainingMaterialsData data);

    ItemTrainingMaterials copy(ItemTrainingMaterials data);

    ItemTrainingMaterialsProcess copy(ItemTrainingMaterialsProcess data);

    ItemTrainingMaterialsProcessRecord copy(ItemTrainingMaterialsProcessRecord data);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void override(@MappingTarget ItemTrainingMaterialsData newData, ItemTrainingMaterialsData params);
}
