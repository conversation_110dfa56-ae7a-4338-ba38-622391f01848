package com.daddylab.supplier.item.controller.afterSales;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSales.WarehouseAfterSalesAddressVO;
import com.daddylab.supplier.item.application.afterSalesForwarding.AfterSalesForwardingRegisterBizService;
import com.daddylab.supplier.item.application.afterSalesForwarding.types.*;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.application.system.BaseInfoService;
import com.daddylab.supplier.item.application.warehouse.WarehouseGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.daddylab.supplier.item.types.warehouse.WarehouseViewVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2024/5/13
 */
@RestController
@Api(value = "售后转寄登记API", tags = "售后转寄登记")
@RequestMapping("/afterSales/forwardingRegister")
@RequiredArgsConstructor
@Validated
public class AfterSalesForwardingRegisterController {

    private final AfterSalesForwardingRegisterBizService afterSalesForwardingRegisterBizService;
    private final WarehouseGateway warehouseGateway;
    private final BaseInfoService baseInfoService;

    @ResponseBody
    @ApiOperation(value = "查询补全表单")
    @PostMapping("/completion")
    public MultiResponse<AfterSalesForwardingRegisterFormItemVO> completion(@RequestBody @Validated AfterSalesForwardingRegisterCompletionCmd cmd) {
        return afterSalesForwardingRegisterBizService.completion(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "保存登记")
    @PostMapping("/save")
    public SingleResponse<Boolean> save(@RequestBody @Validated AfterSalesForwardingRegisterSaveCmd cmd) {
        return afterSalesForwardingRegisterBizService.save(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "查询转寄登记记录")
    @PostMapping("/query")
    public PageResponse<AfterSalesForwardingRegisterPageVO> query(@RequestBody AfterSalesForwardingRegisterQuery query) {
        return afterSalesForwardingRegisterBizService.query(query);
    }

    @ResponseBody
    @ApiOperation(value = "导入")
    @PostMapping("/importExcel")
    public Response importExcel(@ApiParam(name = "file", value = "文件", required = true) @RequestParam("file")
                                MultipartFile file) throws IOException {
        return afterSalesForwardingRegisterBizService.importExcel(file.getInputStream());
    }

    @ResponseBody
    @ApiOperation(value = "导出")
    @PostMapping("/exportExcel")
    public Response exportExcel(@RequestBody AfterSalesForwardingRegisterQuery query) {
        return afterSalesForwardingRegisterBizService.exportExcel(query);
    }

    @ResponseBody
    @ApiOperation(value = "标记记录状态为无需转寄")
    @PostMapping("/noNeedToForward")
    public Response noNeedToForward(@RequestBody AfterSalesForwardingRegisterNoNeedToForwardCmd cmd) {
        return afterSalesForwardingRegisterBizService.noNeedToForward(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "标记记录状态为需转寄")
    @PostMapping("/toForward")
    public Response toForward(@RequestBody AfterSalesForwardingRegisterToForwardCmd cmd) {
        return afterSalesForwardingRegisterBizService.toForward(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "报告异常")
    @PostMapping("/reportAnomalies")
    public Response reportAnomalies(@RequestBody AfterSalesForwardingRegisterReportAnomaliesCmd cmd) {
        return afterSalesForwardingRegisterBizService.reportAnomalies(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "仓库下拉")
    @GetMapping("/warehouseDropdown")
    public MultiResponse<Warehouse> warehouseDropdown(String warehouseName) {
        return warehouseGateway.queryWarehouseByName(warehouseName);
    }

    @ResponseBody
    @ApiOperation(value = "转寄地址")
    @GetMapping("/warehouseAddress")
    public MultiResponse<WarehouseAfterSalesAddressVO> warehouseAddress(Long warehouseId, String warehouseName) {
        SingleResponse<WarehouseViewVO> response = baseInfoService.viewWarehouse(warehouseId, warehouseName);
        if (response.isSuccess()) {
            return MultiResponse.of(response.getData().getAfterSalesAddressInfoList());
        }
        return MultiResponse.buildFailure(response.getErrCode(), response.getErrMessage());
    }

    private final OperateLogBizService operateLogBizService;

    @ResponseBody
    @ApiOperation(value = "操作记录")
    @GetMapping("/log")
    public MultiResponse<OperateLog> log(@ApiParam(name = "id", value = "快递单号", required = true) String id) {
        return operateLogBizService.getOperateLogs(OperateLogTarget.AFTER_SALE_FORWARDING, id);
    }

    @ResponseBody
    @ApiOperation(value = "删除")
    @GetMapping("/delete")
    public Response delete(@ApiParam(name = "id", value = "登记记录ID", required = true) Long id) {
        return afterSalesForwardingRegisterBizService.delete(id);
    }



}
