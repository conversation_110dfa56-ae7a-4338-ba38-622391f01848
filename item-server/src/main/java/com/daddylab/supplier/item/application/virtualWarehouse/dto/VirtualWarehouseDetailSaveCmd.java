/*
package com.daddylab.supplier.item.application.virtualWarehouse.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

*/
/**
 * <AUTHOR> up
 * @date 2024年03月01日 9:54 AM
 *//*

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("虚拟仓明细保存参数封装")
public class VirtualWarehouseDetailSaveCmd extends Command {

    private static final long serialVersionUID = 3344632447987024194L;

    @ApiModelProperty("明细仓库 ID")
    private Long id;

    @ApiModelProperty("明细仓库编码")
    @NotEmpty
    private String warehouseNo;

    @ApiModelProperty("明细仓库名称")
    @NotEmpty
    private String warehouseName;

    @ApiModelProperty("明细仓库占比")
    @NotNull
    private Integer inventoryRatio;

    @ApiModelProperty("实仓数据版本号")
    @NotNull(message = "实仓数据版本号不得为空")
    private Integer warehouseVersion;

}
*/
