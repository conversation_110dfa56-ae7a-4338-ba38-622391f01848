package com.daddylab.supplier.item.domain.purchase.service;

import com.daddylab.supplier.item.application.purchase.purchaseTable.PurchaseTableBizService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.PurchaseTransMapper;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseCmd;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.purchase.enums.PurchaseConfirm;
import com.daddylab.supplier.item.domain.purchase.enums.PurchaseIsActive;
import com.daddylab.supplier.item.domain.purchase.enums.PurchaseStatus;
import com.daddylab.supplier.item.domain.purchase.gateway.PurchaseGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Purchase;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseTable;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.Objects;

import static com.daddylab.supplier.item.application.purchase.PurchaseConst.MONTH_PATTERN;

/**
 * <AUTHOR>
 * @ClassName PurchaseDomainService.java
 * @description
 * @createTime 2021年11月11日 15:21:00
 */
@Service
@Slf4j
public class PurchaseDomainService {

    @Autowired
    PurchaseGateway purchaseGateway;
    @Autowired
    IPurchaseService iPurchaseService;
    @Autowired
    private PurchaseTableBizService purchaseTableBizService;
    @Autowired
    private ItemGateway itemGateway;


    public void deletePurchase(Long id) {
        Assert.notNull(id, "采购记录id入参不得为空");
        purchaseGateway.removeById(id);
    }

    public Purchase updatePurchase(PurchaseCmd purchaseCmd) {
        if (Objects.isNull(purchaseCmd.getId())) {
            PurchaseTable purchaseTable = new PurchaseTable();
            purchaseTable.setMonth(purchaseCmd.getMonth().trim());
            purchaseTableBizService.insert(purchaseTable);

            Purchase purchase = PurchaseTransMapper.INSTANCE.cmdToDo(purchaseCmd);
            //新增采购员
//            itemGateway.saveBuyer(purchaseCmd.getBuyerId(), purchaseCmd.getBuyerName(), "");
            // 保存采购价格
            purchaseGateway.saveDo(purchase);
            purchase.setSort(purchase.getId());
            iPurchaseService.updateById(purchase);
            return purchase;
        } else {
            Assert.notNull(purchaseCmd.getId(), "采购记录id入参不得为空");
            Purchase purchase = iPurchaseService.getById(purchaseCmd.getId());
            if (Objects.isNull(purchase)) {
                throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查不到该记录,无法删除");
            }
            if (Objects.equals(purchaseCmd.getProvider(), purchase.getProvider())) {
                purchaseCmd.setToken("");
            }

            String nowMonth = DateUtil.format(System.currentTimeMillis() / 1000, MONTH_PATTERN);

            //校验该记录状态,纯活动商品则判断月份
            if (judgeTime(purchase.getCreatedAt())) {
                final Purchase updatePurchase = PurchaseTransMapper.INSTANCE.cmdToDo(purchaseCmd);
                purchaseGateway.update(updatePurchase);
            }

            //纯活动且可编辑
            Integer isEdit = purchaseTableBizService.selectStatusByMonth(purchase.getMonth()).getData();
            if (purchase.getIsActive().equals(PurchaseIsActive.YES) && isEdit == 0) {
                final Purchase updatePurchase = PurchaseTransMapper.INSTANCE.cmdToDo(purchaseCmd);
                purchaseGateway.update(updatePurchase);
            }
            return purchase;
        }
    }

    public static Boolean judgeTime(Long time) {
        final LocalDateTime now = DateUtil.toLocalDateTime(time);
        final LocalDateTime firstDayOfMonth = DateUtil.getFirstDayOfMonth();
        final LocalDateTime lastSecondOfFirstDayOfNextMonth = DateUtil.toLastSecondOfDay(firstDayOfMonth.plusMonths(1));
        return now.isAfter(firstDayOfMonth) && now.isBefore(lastSecondOfFirstDayOfNextMonth);
    }

    public void resetPurchase(Long id) {
        Purchase purchase = iPurchaseService.getById(id);
        if (Objects.isNull(purchase)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查不到该记录,无法删除");
        }
        //校验该记录状态
        if (judgeTime(purchase.getCreatedAt())) {
            String random = RandomStringUtils.random(8, true, true);
            purchase.setMd5(random);
            purchase.setToken("");
            purchase.setStatus(PurchaseStatus.NO_CONFIRM);
            purchase.setIsConfirm(PurchaseConfirm.ALLOW_Edit.getValue());
            iPurchaseService.updateById(purchase);
        }
    }

}
