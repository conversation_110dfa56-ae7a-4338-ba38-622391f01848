package com.daddylab.supplier.item.application.afterSales.impl;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.StockinSmartRefundAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.StockinSmartRefundCreateExtDetailMap;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.StockinSmartRefundCreateExtRefundInfo;
import com.daddylab.supplier.item.application.afterSales.AfterSalesConfirmBizService;
import com.daddylab.supplier.item.application.afterSales.AfterSalesConfirmCmd;
import com.daddylab.supplier.item.application.afterSales.AfterSalesConfirmCmd.Detail;
import com.daddylab.supplier.item.application.afterSales.AfterSalesReceiveInfoVO;
import com.daddylab.supplier.item.application.refundOrder.RefundOrderService;
import com.daddylab.supplier.item.application.warehouse.WarehouseGateway;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesConfirm;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesConfirmDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesReceiveState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesConfirmDetailService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesConfirmService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesReceiveService;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderDetail;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderItem;
import com.daddylab.supplier.item.types.warehouse.WmsType;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2022/10/21
 */
@Service
@AllArgsConstructor
@Slf4j
public class AfterSalesConfirmBizServiceImpl implements AfterSalesConfirmBizService {

    private final IAfterSalesConfirmService afterSalesConfirmService;
    private final IAfterSalesConfirmDetailService afterSalesConfirmDetailService;
    private final IAfterSalesReceiveService afterSalesReceiveService;
    private final RefundOrderService refundOrderService;
    private final WdtGateway wdtGateway;
    private final WarehouseGateway warehouseGateway;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Response confirm(AfterSalesConfirmCmd cmd) {
        final String returnOrderNo = cmd.getReturnOrderNo();
        final Optional<AfterSalesConfirm> afterSalesConfirmOptional = afterSalesConfirmService.lambdaQuery()
                .eq(AfterSalesConfirm::getReturnOrderNo, returnOrderNo).oneOpt();
        if (afterSalesConfirmOptional.isPresent()) {
            return Response.buildFailure(ErrorCode.BUSINESS_OPERATE_ERROR.getCode(), "该单据已完成销退确认");
        }
        final SingleResponse<RefundOrderDetail> refundOrderDetailResp = refundOrderService.refundOrderDetail(
                returnOrderNo);
        if (!refundOrderDetailResp.isSuccess()) {
            return refundOrderDetailResp;
        }
        final RefundOrderDetail refundOrderDetail = refundOrderDetailResp.getData();

        final Optional<AfterSalesReceiveInfoVO> afterSalesReceiveInfoVO = afterSalesReceiveService.getAfterSalesReceiveInfoVO(
                returnOrderNo);
        final AfterSalesReceiveState afterSalesReceiveState = afterSalesReceiveInfoVO.map(
                AfterSalesReceiveInfoVO::getState).orElse(AfterSalesReceiveState.WAIT);
        //更新确认收货状态到收货记录
        if (afterSalesReceiveState == AfterSalesReceiveState.RECEIVED) {
            if (!afterSalesReceiveService.confirmReceive(returnOrderNo)) {
                return Response.buildFailure(ErrorCode.BUSINESS_OPERATE_ERROR.getCode(),
                        "该单据已完成销退确认");
            }
        }
        //如果是待收货，走强制确认收货逻辑
        else if (afterSalesReceiveState == AfterSalesReceiveState.WAIT) {
            if (cmd.getForceConfirm()) {
                afterSalesReceiveService.forceConfirmReceive(returnOrderNo, afterSalesReceiveInfoVO.isPresent());
            } else {
                return Response.buildFailure(ErrorCode.BUSINESS_OPERATE_ERROR.getCode(),
                        "仓库未确认收货，确认要进行销退确认?");
            }
        }

        final AfterSalesConfirm afterSalesConfirm = new AfterSalesConfirm();
        afterSalesConfirm.setReturnOrderNo(returnOrderNo);
        afterSalesConfirm.setRemark(Optional.ofNullable(cmd.getRemark()).orElse(""));
        afterSalesConfirmService.save(afterSalesConfirm);
        final Map<Long, Detail> cmdDetailsMap = cmd.getDetails().stream()
                .collect(Collectors.toMap(Detail::getRefundOrderItemId, Function.identity()));
        List<AfterSalesConfirmDetail> afterSalesConfirmDetails = Lists.newArrayList();
        for (RefundOrderItem returnItemDetail : refundOrderDetail.getReturnItemDetails()) {
            final Detail detailCmd = cmdDetailsMap.get(returnItemDetail.getId());
            if (Objects.isNull(detailCmd)) {
                return Response.buildFailure(ErrorCode.VERIFY_PARAM.getCode(), "销退确认明细未完整填写");
            }
            final AfterSalesConfirmDetail afterSalesConfirmDetail = new AfterSalesConfirmDetail();
            afterSalesConfirmDetail.setReturnOrderNo(returnOrderNo);
            afterSalesConfirmDetail.setRecId(returnItemDetail.getRecId());
            afterSalesConfirmDetail.setGoodsNo(returnItemDetail.getItemCode());
            afterSalesConfirmDetail.setGoodsName(returnItemDetail.getItemName());
            afterSalesConfirmDetail.setSpecName(returnItemDetail.getSpecName());
            afterSalesConfirmDetail.setSpecNo(returnItemDetail.getSpecNo());
            afterSalesConfirmDetail.setQuantity(returnItemDetail.getRefundQuantity());
            afterSalesConfirmDetail.setUndertakeType(detailCmd.getUndertakeType());
            afterSalesConfirmDetail.setUndertakeAmount(detailCmd.getUndertakeAmount());
            afterSalesConfirmDetails.add(afterSalesConfirmDetail);
        }
        afterSalesConfirmDetailService.saveBatch(afterSalesConfirmDetails);

        //推送销退入库单到旺店通
        try {
            pushRefundStockInOrderToWdt(refundOrderDetail);
        } catch (BizException e) {
            final String format = "推送销退确认到旺店通异常，退换单号:{}，异常信息：{}";
            log.error(format, cmd.getReturnOrderNo(), e.getMessage());
            Alert.text(MessageRobotCode.GLOBAL,
                    StringUtil.format(format, cmd.getReturnOrderNo(), e.getMessage()));
        }
        return Response.buildSuccess();
    }

    /**
     * 推送销退入库单到旺店通
     *
     * @param refundOrderDetail 退换单详情视图对象
     */
    private void pushRefundStockInOrderToWdt(RefundOrderDetail refundOrderDetail)
            throws BizException {
        final String returnWarehouseNo = refundOrderDetail.getReturnWarehouseNo();
        final Warehouse warehouse = warehouseGateway.getWarehouse(returnWarehouseNo).orElseThrow(
                () -> ExceptionPlusFactory.sysException("仓库记录还未创建：" + returnWarehouseNo));
        if (!Objects.equals(warehouse.getWmsType(), WmsType.INNER.getValue())) {
            final String wmsTypeDesc = IEnum.getEnumOptByValue(WmsType.class, warehouse.getWmsType())
                    .map(WmsType::getDesc)
                    .orElse(warehouse.getWmsType().toString());
            final String message = StringUtil.format("销退确认，因为退货仓库类型为{}，非内部仓库需跳过推送退货入库单至旺店通，仓库编号为{}",
                    wmsTypeDesc, returnWarehouseNo);
            log.info(message);
            Alert.text(MessageRobotCode.GLOBAL, message);
            return;
        }
        final String appKey = warehouse.getAppKey();
        final StockinSmartRefundAPI api = wdtGateway.getAPI(StockinSmartRefundAPI.class, appKey, true);
        final StockinSmartRefundCreateExtRefundInfo refundInfo = new StockinSmartRefundCreateExtRefundInfo();
        refundInfo.setTid(refundOrderDetail.getSrcOrderNos());
        refundInfo.setReturnWarehouseNo(returnWarehouseNo);
        refundInfo.setRemark("ERP售后管理销退确认");

        final ArrayList<StockinSmartRefundCreateExtDetailMap> detailMapList = new ArrayList<>();
        for (RefundOrderItem returnItemDetail : refundOrderDetail.getReturnItemDetails()) {
            final StockinSmartRefundCreateExtDetailMap stockinSmartRefundCreateExtDetailMap = new StockinSmartRefundCreateExtDetailMap();
            stockinSmartRefundCreateExtDetailMap.setSpecNo(returnItemDetail.getSpecNo());
            stockinSmartRefundCreateExtDetailMap.setStockinNum(
                    BigDecimal.valueOf(returnItemDetail.getRefundQuantity()));
            stockinSmartRefundCreateExtDetailMap.setRemark("");
            detailMapList.add(stockinSmartRefundCreateExtDetailMap);
        }
        try {
            api.createExt(refundInfo, detailMapList, false);
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapBizException(e);
        }
    }
}
