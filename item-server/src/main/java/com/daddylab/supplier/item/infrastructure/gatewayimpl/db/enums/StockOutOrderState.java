package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/3/29 11:45
 * @description StockOutOrderState
 */
@Getter
@AllArgsConstructor
public enum StockOutOrderState {
    /**
     * 待提交
     */
    NO_SUBMIT(1, "待提交"),

    /**
     * 待审核
     */
    NO_AUDIT(2, "待审核"),

    /**
     * 待出库
     */
    NO_STOCK_OUT(3, "待出库"),

    /**
     * 部分出库（2022-06-21 新增状态）
     */
    PART_OUT(31, "部分出库"),

    /**
     * 已出库
     */
    STOCK_OUT(4, "已出库"),

    /**
     * 已取消
     */
    CANCEL(5, "已取消"),

    /**
     * 审核拒绝
     */
    REJECT(6, "审核拒绝"),

    /**
     * 撤回审核
     */
    REVOCATION(7, "撤回审核"),


    DEFAULT(0, "默认"),
    REVERSE_FIXED(8, "冲销流程,此单据为逆向单据"),
    WRITE_OFF(9, "冲销流程,原单据冲销作废"),
    FIX_ORDER(10, "冲销流程,原单据修正单据");

    @EnumValue
    private final Integer value;
    private final String desc;


    /**
     * 是否可编辑
     *
     * @param state 状态
     * @return 是/否
     */
    public static boolean isEdit(Integer state) {
        return state.equals(StockOutOrderState.NO_SUBMIT.getValue())
                || state.equals(StockOutOrderState.REJECT.getValue())
                || state.equals(StockOutOrderState.REVOCATION.getValue());
    }

}
