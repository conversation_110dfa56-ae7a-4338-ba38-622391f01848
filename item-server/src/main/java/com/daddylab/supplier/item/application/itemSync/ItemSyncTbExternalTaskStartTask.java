package com.daddylab.supplier.item.application.itemSync;

import com.alibaba.cola.exception.BizException;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.itemSync.TaobaoGoodsDetailLink;
import com.daddylab.supplier.item.domain.winrobot.WinrobotTokenManager;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ThirdPlatformSync;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ThirdPlatformSyncExternalTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ThirdPlatformSyncLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformSyncErrorLevel;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ThirdPlatformSyncExternalTaskState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ThirdPlatformSyncExternalTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ThirdPlatformSyncState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ThirdPlatformSyncType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IThirdPlatformSyncExternalTaskService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IThirdPlatformSyncLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IThirdPlatformSyncService;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.winrobot360.Winrobot360API;
import com.daddylab.supplier.item.infrastructure.winrobot360.Winrobot360Config;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.EditTbGoodsParam;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.GenericRsp;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TaskStartParam;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TaskStartRspData;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TypeFactory;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/9/21
 */
@Component
@AllArgsConstructor
@Slf4j
public class ItemSyncTbExternalTaskStartTask {

    private final IThirdPlatformSyncService thirdPlatformSyncService;
    private final IThirdPlatformSyncLogService thirdPlatformSyncLogService;
    private final IThirdPlatformSyncExternalTaskService thirdPlatformSyncExternalTaskService;
    private final WinrobotTokenManager winrobotTokenManager;
    private final Winrobot360API winrobot360API;
    private final Winrobot360Config winrobot360Config;
    private final ItemSyncTbQueryService itemSyncTbQueryService;

    @XxlJob("ItemSyncTbExternalTaskStartTask")
    @Trace(operationName = "ItemSyncTbExternalTaskStartTask")
    public void run() {
        final String env = ApplicationContextUtil.getActiveProfile();
        final List<ThirdPlatformSync> thirdPlatformSyncs = thirdPlatformSyncService.lambdaQuery()
                .eq(ThirdPlatformSync::getState, ThirdPlatformSyncState.WAITING)
                .eq(ThirdPlatformSync::getPlatformType, PlatformType.TAO_BAO)
                .eq(ThirdPlatformSync::getType, ThirdPlatformSyncType.TAOBAO_WINROBOT)
                .eq(ThirdPlatformSync::getEnv, env)
                .list();
        Integer runningCount = thirdPlatformSyncService.lambdaQuery()
                .eq(ThirdPlatformSync::getState, ThirdPlatformSyncState.RUNNING)
                .eq(ThirdPlatformSync::getPlatformType, PlatformType.TAO_BAO)
                .eq(ThirdPlatformSync::getType, ThirdPlatformSyncType.TAOBAO_WINROBOT)
                .count();
        log.info("商品同步淘宝/启动影刀任务: 任务开始 待处理数量={} 运行中数量={}", thirdPlatformSyncs.size(), runningCount);

        if (runningCount >= winrobot360Config.getConcurrentTaskNum()) {
            return;
        }

        final String token = winrobotTokenManager.token();
        log.info("商品同步淘宝/启动影刀任务: 获取到token={}", token);

        for (int i = 0; i < thirdPlatformSyncs.size(); i++) {
            final Integer concurrentTaskNum = winrobot360Config.getConcurrentTaskNum();
            if (runningCount >= concurrentTaskNum) {
                log.info("商品同步淘宝/启动影刀任务: 任务并行数超过配置，中止任务。 运行中数量={} 并行数配置={}", runningCount,
                        concurrentTaskNum);
                break;
            }
            log.info("商品同步淘宝/启动影刀任务: 正在处理 {}/{}", i + 1, thirdPlatformSyncs.size());
            final ThirdPlatformSync thirdPlatformSync = thirdPlatformSyncs.get(i);

            try {
                final TaobaoGoodsDetailLink taobaoGoodsDetailLink = new TaobaoGoodsDetailLink(
                        thirdPlatformSync.getThirdLink());

                final EditTbGoodsParam editTbGoodsParam = itemSyncTbQueryService.editTbGoodsParamQuery(
                        thirdPlatformSync.getItemId(), taobaoGoodsDetailLink.getGoodsId());

                if (editTbGoodsParam.getMainPic().isEmpty() || editTbGoodsParam.getDetailPic()
                        .isEmpty()) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "商品图片为空");
                }

                //由于淘宝影刀同步没有测试环境，非生产环境写死一个固定的测试商品，避免污染生产数据
                if (!ApplicationContextUtil.isActiveProfile("prod")) {
                    editTbGoodsParam.setSpId("************");
                }

                final TaskStartParam param = TypeFactory.buildTaskStartParam(
                        winrobot360Config.getScheduleUuid(),
                        winrobot360Config.getEditTbGoodsRobotUuid(), editTbGoodsParam);

                final ThirdPlatformSyncLog thirdPlatformSyncLog = new ThirdPlatformSyncLog();
                thirdPlatformSyncLog.setSyncId(thirdPlatformSync.getId());
                thirdPlatformSyncLog.setReq(JsonUtil.toJson(param));
                thirdPlatformSyncLog.setPlatformType(thirdPlatformSync.getPlatformType());
                thirdPlatformSyncLog.setItemCode(thirdPlatformSync.getItemCode());
                thirdPlatformSyncLog.setItemId(thirdPlatformSync.getItemId());
                thirdPlatformSyncLogService.save(thirdPlatformSyncLog);

                final GenericRsp<TaskStartRspData> taskStart = winrobot360API.taskStart(
                        token, param);

                thirdPlatformSyncLog.setResp(JsonUtil.toJson(taskStart));
                if (!taskStart.getSuccess()) {
                    log.error("商品同步淘宝/启动影刀任务: 启动影刀任务失败，失败原因：{}", taskStart.getMsg());

                    thirdPlatformSyncLog.setError(taskStart.getMsg());
                    thirdPlatformSyncLog.setErrorLevel(PlatformSyncErrorLevel.ERROR);
                    thirdPlatformSyncLogService.updateById(thirdPlatformSyncLog);

                    throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "影刀任务启动失败");
                }
                thirdPlatformSyncLogService.updateById(thirdPlatformSyncLog);

                final ThirdPlatformSyncExternalTask thirdPlatformSyncExternalTask = new ThirdPlatformSyncExternalTask();
                thirdPlatformSyncExternalTask.setSyncId(thirdPlatformSync.getId());
                thirdPlatformSyncExternalTask.setType(ThirdPlatformSyncExternalTaskType.WINROBOT);
                thirdPlatformSyncExternalTask.setTaskId(taskStart.getData().getTaskUuid());
                thirdPlatformSyncExternalTask.setState(ThirdPlatformSyncExternalTaskState.WAITING);
                thirdPlatformSyncExternalTaskService.save(thirdPlatformSyncExternalTask);

                thirdPlatformSyncService.lambdaUpdate()
                        .set(ThirdPlatformSync::getState, ThirdPlatformSyncState.RUNNING)
                        .eq(ThirdPlatformSync::getId, thirdPlatformSync.getId()).update();
                runningCount++;
            } catch (Throwable e) {
                log.error("商品同步淘宝/启动影刀任务: 异常", e);

                thirdPlatformSyncService.lambdaUpdate()
                        .set(ThirdPlatformSync::getState, ThirdPlatformSyncState.ERROR)
                        .set(ThirdPlatformSync::getError,
                                e instanceof BizException ? e.getMessage() : "任务异常")
                        .eq(ThirdPlatformSync::getId, thirdPlatformSync.getId()).update();
            }
        }
        log.info("商品同步淘宝/启动影刀任务: 处理完成");

    }
}
