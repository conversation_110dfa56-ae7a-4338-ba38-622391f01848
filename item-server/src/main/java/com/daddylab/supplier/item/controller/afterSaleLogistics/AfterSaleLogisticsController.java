package com.daddylab.supplier.item.controller.afterSaleLogistics;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSaleLogistics.AfterSaleLogisticsBizService;
import com.daddylab.supplier.item.application.afterSaleLogistics.domain.LogisticsTraceData;
import com.daddylab.supplier.item.application.afterSaleLogistics.dto.*;
import com.daddylab.supplier.item.application.kuaidaoyun.KdyBizService;
import com.daddylab.supplier.item.common.domain.dto.GenericIdBody;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsTrace;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsTraceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> up
 * @date 2024年05月23日 10:43 AM
 */
@Slf4j
@RestController
@RequestMapping("/afterSalesLogistics")
@Api(value = "售后物流相关API", tags = "售后物流相关API")
@AllArgsConstructor
class AfterSaleLogisticsController {

  private final AfterSaleLogisticsBizService afterSaleLogisticsBizService;
  private final KdyBizService kdyBizService;
  private final IOrderLogisticsTraceService iOrderLogisticsTraceService;

  @ApiOperation(value = "分配客服")
  @PostMapping("/distributeCS")
  public Response distributeCS(@RequestBody @Validated DistributeCSRequest cmd) {
    return afterSaleLogisticsBizService.distributeCS(cmd);
  }

  @ApiOperation(value = "跟进处理")
  @PostMapping("/followUp")
  public Response followUp(@RequestBody @Validated AfterSaleLogisticsFollowUpCmd cmd) {
    return afterSaleLogisticsBizService.followUp(cmd);
  }

  @ApiOperation(value = "导出")
  @PostMapping("/export")
  public Response export(@RequestBody @Validated AfterSaleLogisticsPageQuery cmd) {
    return afterSaleLogisticsBizService.export(cmd);
  }

  @ApiOperation(value = "列表查询")
  @PostMapping("/page")
  public SingleResponse<AfterSaleLogisticsPageVo> page(
      @RequestBody @Validated AfterSaleLogisticsPageQuery pageQuery) {
    return afterSaleLogisticsBizService.page(pageQuery);
  }

  @ApiOperation(value = "获取物流跟踪列表")
  @PostMapping("/getTrackList")
  public SingleResponse<LogisticsTraceData> getTrackList(@RequestBody @Validated GenericIdBody<Long> cmd) {
    return afterSaleLogisticsBizService.getTrackList(cmd.getId());
  }

  @ApiOperation(value = "备注")
  @PostMapping("/remark")
  public Response remark(@RequestBody @Validated AfterSaleLogisticsRemarkCmd cmd) {
    return afterSaleLogisticsBizService.remark(cmd);
  }

  @ApiOperation(value = "获取备注")
  @PostMapping("/getRemark")
  public SingleResponse<String> getRemark(@RequestBody @Validated AfterSaleLogisticsRemarkQuery query) {
    return afterSaleLogisticsBizService.getRemark(query);
  }

  @ApiOperation(value = "处理", notes = "已废弃")
  @PostMapping("/handle")
  public Response handle(@RequestBody @Validated AfterSaleLogisticsHandleCmd cmd) {
    return Response.buildSuccess();
  }

  @ApiOperation(value = "关闭")
  @PostMapping("/close")
  public Response close(@RequestBody @Validated AfterSaleLogisticsCloseCmd cmd) {
    return afterSaleLogisticsBizService.close(cmd);
  }

  @ApiOperation(value = "开启")
  @PostMapping("/open")
  public Response open(@RequestBody @Validated AfterSaleLogisticsOpenCmd cmd) {
    return afterSaleLogisticsBizService.open(cmd);
  }

  @ApiOperation(value = "批量预警列表")
  @PostMapping("/batchWarnList")
  public PageResponse<AfterSaleLogisticsBatchWarnVO> batchWarnList(
      @RequestBody @Validated AfterSaleLogisticsBatchWarnQuery query) {
    return afterSaleLogisticsBizService.batchWarnList(query);
  }

  @ApiOperation(value = "物流公司列表")
  @GetMapping("/logisticsCompanyList")
  public MultiResponse<String> logisticsCompanyList(String company) {
    return afterSaleLogisticsBizService.logisticsCompanyList(company);
  }

}
