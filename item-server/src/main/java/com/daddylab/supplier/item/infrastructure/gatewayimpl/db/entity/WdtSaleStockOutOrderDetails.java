package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 旺店通销售出库单详情
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtSaleStockOutOrderDetails implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 销售出库单详情的id
     */
    private Integer recId;

    /**
     * 出库单ID
     */
    private Long stockoutId;

    /**
     * 订单明细id
     */
    private Integer srcOrderDetailId;

    /**
     * 规格id
     */
    private Integer specId;

    /**
     * 商家编码
     */
    private String specNo;

    /**
     * 货品数量: 如果按照货位分组就是总货品数量
     */
    private BigDecimal goodsCount;

    /**
     * 总成本: 成本价*货品数量
     */
    private BigDecimal totalAmount;

    /**
     * 成交价（当订单货品发生变更导致价格变化，该值不变，若需获取ERP销售出库明细中的货品成交价取share_price字段）
     */
    private BigDecimal sellPrice;

    /**
     * 出库单明细备注
     */
    private String remark;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 货品编号
     */
    private String goodsNo;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 规格码
     */
    private String specCode;

    /**
     * 货品成本
     */
    private BigDecimal costPrice;

    /**
     * 总重量
     */
    private BigDecimal weight;

    /**
     * 货品id
     */
    private Integer goodsId;

    /**
     * 规格自定义属性1
     */
    private String prop1;

    /**
     * 规格自定义属性2
     */
    private String prop2;

    /**
     * 规格自定义属性3
     */
    private String prop3;

    /**
     * 规格自定义属性4
     */
    private String prop4;

    /**
     * 规格自定义属性5
     */
    private String prop5;

    /**
     * 规格自定义属性6
     */
    private String prop6;

    /**
     * 平台id，点击查看 平台代码表
     */
    private Integer platformId;

    /**
     * 退款状态: 0无退款 1取消退款, 2已申请退款 3等待退货 4等待收货 5退款成功---(原始子订单关闭，这里也是退款状态)
     */
    private Integer refundStatus;

    /**
     * 单价/货品原单价
     */
    private BigDecimal marketPrice;

    /**
     * 货品总优惠
     */
    private BigDecimal discount;

    /**
     * 系统销售出库明细对应的货品成交价
     */
    private BigDecimal sharePrice;

    /**
     * 总货款/货品成交总价
     */
    private BigDecimal shareAmount;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 主条码
     */
    private String barcode;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 订单货品(子订单)id
     */
    private Integer saleOrderId;

    /**
     * 是否是赠品: 0非赠品 1自动赠送 2手工赠送 4周期购赠送 8平台赠送
     */
    private Integer giftType;

    /**
     * 原始子订单号
     */
    private String srcOid;

    /**
     * 原始订单号
     */
    private String srcTid;

    /**
     * 订单内部来源: 1手机 2聚划算
     */
    private Integer fromMask;

    /**
     * 货品类型: 0：其它 1：销售货品 2：原材料 3：包装物 4：周转材料 5：虚拟商品 6：固定资产 8：分装箱
     */
    private Integer goodsType;

    /**
     * 货品自定义属性1
     */
    private String goodProp1;

    /**
     * 货品自定义属性2
     */
    private String goodProp2;

    /**
     * 货品自定义属性3
     */
    private String goodProp3;

    /**
     * 货品自定义属性4
     */
    private String goodProp4;

    /**
     * 货品自定义属性5
     */
    private String goodProp5;

    /**
     * 货品自定义属性6
     */
    private String goodProp6;

    /**
     * 当need_sn=true时返回英文逗号分隔的sn
     */
    private String snList;

    /**
     * 系统订单生成时组合装对应的组合装编码
     */
    private String suiteNo;

    @TableField(exist = false)
    List<WdtSaleStockOutOrderPositionDetails> positionDetailsList;

}
