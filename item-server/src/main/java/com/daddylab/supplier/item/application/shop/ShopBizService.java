package com.daddylab.supplier.item.application.shop;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.shop.domain.ShopOperatorMapVO;
import com.daddylab.supplier.item.domain.shop.dto.*;
import com.daddylab.supplier.item.domain.shop.enums.ShopStatus;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/9 4:37 下午
 * @description
 */
public interface ShopBizService {

    /**
     * 店铺下拉列表搜索
     */
    MultiResponse<ShopDropDownItem> dropDownList(ShopDropDownQuery query);

    /**
     * 创建或更新店铺
     */
    SingleResponse<ShopDetail> createOrUpdateShop(CreateOrUpdateShopCmd cmd);

    /**
     * 查询店铺
     */
    MultiResponse<ShopListItem> queryShopList(ShopQuery shopQuery);

    /**
     * 分页查询店铺
     */
    PageResponse<ShopListItem> pageQueryShopList(ShopQuery shopQuery);

    /**
     * 获取店铺模型
     */
    SingleResponse<ShopDetail> getShopDetail(Long id);

    /**
     * 更新店铺状态
     */
    Response updateShopStatus(Long id, ShopStatus status);

    /**
     * 删除店铺
     */
    @Deprecated
    Response deleteShop(Long id);


    PageResponse<ShopOperatorMapVO> offShelfShopOperatorMappingPage(OffShelfShopOperatorPageQuery queryPage);

    SingleResponse<Boolean> offShelfShopOperatorMappingSave(OffShelfShopOperatorCmd cmd);

    SingleResponse<Boolean> offShelfShopOperatorMappingRemove(Long id);
}
