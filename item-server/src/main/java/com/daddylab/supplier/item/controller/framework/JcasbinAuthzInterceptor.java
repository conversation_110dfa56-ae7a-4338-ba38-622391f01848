package com.daddylab.supplier.item.controller.framework;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.exception.ExceptionFactory;
import com.daddylab.supplier.item.application.auth.AuthAppService;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.auth.gateway.LoginGateway;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.common.CompositeUserContext;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/17 4:01 下午
 */
@Slf4j
@Component
public class JcasbinAuthzInterceptor implements HandlerInterceptor {

    private List<String> whiteUriList = null;

    @Autowired
    private AuthAppService authAppService;

    @Autowired
    private LoginGateway loginGateway;

    @Autowired
    RefreshConfig refreshConfig;

    public List<String> getWhiteUriList() {
        return StrUtil.splitTrim(refreshConfig.getWhiteUri(), ",");
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        // 如果不是映射到方法直接通过
        if (!(handler instanceof HandlerMethod) || "/supplier/item/error".equals(
                request.getRequestURI())) {
            return true;
        }

        String activeProfile = SpringUtil.getActiveProfile();
        if ("local".equals(activeProfile)) {
            if (loginGateway.isLogin())
                authAppService.loadUserContext(loginGateway.getLoginId(), false);
            return true;
        }

        //确保之前的用户上下文已经移除
        CompositeUserContext.remove();

        // 当前接口是否需要进行权限校验
        boolean noAuth = false;

        // 当前接口校验的资源标记
        String resourceCode = "";

        final HandlerMethod handlerMethod = (HandlerMethod) handler;
        Auth auth = handlerMethod.getMethodAnnotation(Auth.class);
        if (auth == null) {
            auth = handlerMethod.getBeanType().getAnnotation(Auth.class);
        }

        if (auth != null) {
            noAuth = auth.noAuth();
            resourceCode = auth.resource();
        }

        // 当前请求uri
        final String uri = request.getRequestURI();

        // 如果没有指定资源标记，默认取当前接口URI
        if (resourceCode.isEmpty()) {
            resourceCode = uri;
        }

        // 是否登录
        final boolean isLogin = loginGateway.isLogin();
        Long userId = 0L;

        // 已登陆载入用户上下文
        if (isLogin) {
            userId = loginGateway.getLoginId();
            authAppService.loadUserContext(userId, false);
        }

//        log.info("uri:{},是否登陆:{} 用户ID:{}", uri, isLogin, userId);

        // 白名单接口不校验权限
        final boolean inWhiteList = getWhiteUriList().stream().anyMatch(it -> StringUtils.startsWith(uri, it));
        // 白名单接口无需检查是否登陆，是否有权限访问
        if (noAuth || inWhiteList || refreshConfig.isAuthorizationCheckDisabled()) {
//            log.debug("白名单资源访问，直接放行，uri:{}", uri);
            return true;
        }

        // 未登录且访问的是非白名单接口，直接异常
        if (!isLogin) {
//            log.info("--2-- 是否登陆:{} 用户ID:{}", isLogin, userId);
            throw ExceptionFactory.bizException(ErrorCode.NO_LOGIN.getCode(), ErrorCode.NO_LOGIN.getMsg());
        }

        // 验证用户是否有访问当前接口的权限
        authAppService.verify(userId, resourceCode);

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        UserContext.remove();
    }
}
