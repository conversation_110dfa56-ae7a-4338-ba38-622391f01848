package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsTrace;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OrderLogisticsTraceStatus;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 订单物流跟踪记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
public interface OrderLogisticsTraceMapper extends DaddyBaseMapper<OrderLogisticsTrace> {

  List<WdtOrder> scanToTraceOrderList(
      @Param("timeCursor") LocalDateTime timeCursor, @Param("limit") int limit);

  int updateStatusToBatch(
      @Param("from") OrderLogisticsTraceStatus from,
      @Param("to") OrderLogisticsTraceStatus to,
      @Param("limit") int limit);
}
