package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtGoodsSpec;
import java.util.Collection;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-24
 */
public interface WdtGoodsSpecMapper extends DaddyBaseMapper<WdtGoodsSpec> {
    /**
     * 批量插入或者已存在重复记录时更新数据
     *
     * @param specs 数据集合
     */
    void saveOrUpdateBatch(@Param("specs") Collection<WdtGoodsSpec> specs);

}
