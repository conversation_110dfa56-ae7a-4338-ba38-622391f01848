package com.daddylab.supplier.item.infrastructure.gatewayimpl.region;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.domain.region.gateway.RegionGateway;
import com.daddylab.supplier.item.domain.region.trans.RegionTransMapper;
import com.daddylab.supplier.item.domain.region.vo.Region;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.RegionRelevant;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.RegionRelevantMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class RegionGatewayImpl implements RegionGateway {
    @Autowired
    RegionRelevantMapper regionRelevantMapper;

    @Override
    public List<Region> getRegions() {
        final QueryWrapper<RegionRelevant> queryWrapper = new QueryWrapper<>();
        return regionRelevantMapper.selectList(queryWrapper)
                .stream()
                .map(RegionTransMapper.INSTANCE::toRegion)
                .collect(Collectors.toList());
    }

    @Override
    public String getNameByCode(String code) {
        QueryWrapper<RegionRelevant> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(RegionRelevant::getCode, code);
        final RegionRelevant regionRelevant = regionRelevantMapper.selectOne(queryWrapper);
        return Objects.isNull(regionRelevant) ? "" : regionRelevant.getName();
    }

    @Override
    public String getCodeByName(String name) {
        QueryWrapper<RegionRelevant> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(RegionRelevant::getName, name);
        final List<RegionRelevant> regionRelevantList = regionRelevantMapper.selectList(queryWrapper);
        return CollUtil.isEmpty(regionRelevantList) ? "" : regionRelevantList.get(0).getCode();
    }

    @Override
    public String getCodeStr(String pCode, String cCode, String aCode) {
        return getAddressCode(pCode, cCode, aCode);
    }

    private String getAddressCode(String provinceCode, String cityCode, String areCode) {
        StringBuilder stringBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(provinceCode)) {
            stringBuilder.append(getNameByCode(provinceCode));
        }
        if (StringUtils.isNotBlank(cityCode)) {
            stringBuilder.append("-");
            stringBuilder.append(getNameByCode(cityCode));
        }
        if (StringUtils.isNotBlank(areCode)) {
            stringBuilder.append("-");
            stringBuilder.append(getNameByCode(areCode));
        }
        return stringBuilder.toString();
    }
}
