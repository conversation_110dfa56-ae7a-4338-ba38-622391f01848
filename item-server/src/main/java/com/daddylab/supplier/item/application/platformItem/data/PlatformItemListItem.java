package com.daddylab.supplier.item.application.platformItem.data;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2021/12/27
 */
@Data
@ApiModel("平台商品列表项")
public class PlatformItemListItem {

    /**
     * id
     */
	@ApiModelProperty("平台商品ID")
    private Long id;

    /**
     * 平台商品名称
     */
    @ApiModelProperty("平台商品名称")
    private String goodsName;

    /**
     * 商品图片
     */
	@ApiModelProperty("商品图片")
    private String itemImage;

    /**
     * 商品编码
     */
	@ApiModelProperty("商品编码")
    private String itemCode;

    /**
     * 匹配到的我们自己的商品ID
     */
	@ApiModelProperty("匹配到的我们自己的商品ID")
    private Long itemId;

    /**
     * 店铺名称
     */
	@ApiModelProperty("店铺名称")
    private String shopName;

    /**
     * 匹配到的我们自己的店铺ID
     */
	@ApiModelProperty("匹配到的我们自己的店铺ID")
    private Long shopId;

    /**
     * 平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城
     */
	@ApiModelProperty("平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城")
    private Platform platform;

    /**
     * 平台ICON
     */
    @ApiModelProperty("平台ICON")
    private String platformIcon;

    /**
     * 1:在售 0:已下架
     */
	@ApiModelProperty("1:在售 0:已下架")
    private PlatformItemStatus status;

    /**
     * 外部平台商品ID
     */
	@ApiModelProperty("外部平台商品ID")
    private String outerItemId;


    /**
     * 外部平台商品编码
     */
	@ApiModelProperty("外部平台商品编码")
    private String outerItemCode;


    /**
     * 平台售价
     */
	@ApiModelProperty("平台售价")
    private BigDecimal price;

    /**
     * 平台库存
     */
	@ApiModelProperty("平台库存")
    private Integer stock;

    /**
     * 同步库存
     */
    @ApiModelProperty("同步库存")
    private Integer syncStock;

    /**
     * 可用库存
     */
    @ApiModelProperty("可用库存")
    private Integer availableStock;

    /**
     * sku数量
     */
	@ApiModelProperty("sku数量")
    private Integer skuNum;

    /**
     * 品类ID
     */
    @ApiModelProperty("品类ID")
    private Long categoryId;

    /**
     * 品类名称
     */
    @ApiModelProperty("品类名称")
    private String categoryName;

    /**
     * 是否打开同步开关
     */
    @ApiModelProperty("是否打开同步开关")
    private Boolean syncEnabled;

    /**
     * 是否打开库存锁定开关
     */
    @ApiModelProperty("是否打开库存锁定开关")
    private Boolean lockEnabled;
    
    /**
     * 链接类型（日常|活动|积分）
     */
    private String linkType;

}
