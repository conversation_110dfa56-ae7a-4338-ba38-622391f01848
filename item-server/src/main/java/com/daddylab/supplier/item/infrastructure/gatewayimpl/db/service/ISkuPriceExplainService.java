package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.SkuPriceExplain;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * sku价格说明表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
public interface ISkuPriceExplainService extends IDaddyService<SkuPriceExplain> {

}
