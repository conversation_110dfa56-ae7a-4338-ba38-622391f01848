package com.daddylab.supplier.item.infrastructure.gatewayimpl.shop;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.application.stockSpec.StockSpecBizService;
//import com.daddylab.supplier.item.application.stockStatistic.StockStatisticBizService;
import com.daddylab.supplier.item.application.warehouse.WarehouseBizService;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.domain.shop.dto.*;
import com.daddylab.supplier.item.domain.shop.entity.ShopEntity;
import com.daddylab.supplier.item.domain.shop.enums.ShopStatus;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.domain.shop.trans.ShopTransMapper;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.inject.Inject;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ShopGatewayImpl implements ShopGateway {

    @Autowired
    IShopService shopService;

    @Inject
    ShopMapper shopMapper;

    @Inject
    ShopPrincipalMapper shopPrincipalMapper;

    @Inject
    IShopPrincipalService shopPrincipalService;
    
    LoadingCache<String, Optional<Shop>> byShopNoCache = Caffeine.newBuilder()
            .expireAfterAccess(60, TimeUnit.SECONDS)
            .build(this::getByShopNo);
    
    LoadingCache<Long, Optional<Shop>> byMallShopIdCache = Caffeine.newBuilder()
            .expireAfterAccess(60, TimeUnit.SECONDS)
            .build(mallShopId -> shopService.lambdaQuery().eq(Shop::getMallShopId, mallShopId).oneOpt());

//    @Inject
//    IShopInventorySettingService iShopInventorySettingService;

//    @Inject
//    IShopInventoryService iShopInventoryService;
//
//    @Inject
//    IShopInventoryGoodsService iShopInventoryGoodsService;
//
//    @Inject
//    ShopInventoryMapper shopInventoryMapper;
//
//    @Inject
//    ShopInventoryGoodsMapper shopInventoryGoodsMapper;

//    @Inject
//    StockStatisticBizService stockStatisticBizService;

//    @Inject
//    IVirtualWarehouseService iVirtualWarehouseService;
//
//    @Inject
//    IVirtualWarehouseInventoryService iVirtualWarehouseInventoryService;

    @Inject
    IWarehouseService iWarehouseService;

//    @Inject
//    IWarehouseGoodsInventoryStaticsService iWarehouseGoodsInventoryStaticsService;

    @Inject
    IWdtStockSpecService iWdtStockSpecService;

    @Inject
    OperateLogGateway operateLogGateway;

//    @Inject
//    IWarehouseGoodsInventoryLockStaticsService goodsInventoryLockStaticsService;
//
//    @Inject
//    IVirtualWarehouseInventoryGoodsService iVirtualWarehouseInventoryGoodsService;
//
//    @Inject
//    VirtualWarehouseInventoryMapper virtualWarehouseInventoryMapper;

    @Resource
    WarehouseBizService warehouseBizService;

    @Resource
    StockSpecBizService stockSpecBizService;

    @Resource
    RedissonClient redissonClient;
    @Resource
    StaffService staffService;



    @Override
    public int countShop() {
        return shopService.count();
    }

    @Override
    public ShopEntity createOrUpdateShop(ShopEntity shopEntity) {
        final Shop shop = ShopTransMapper.INSTANCE.toShop(shopEntity);
        shopService.saveOrUpdate(shop);
        if (shopEntity.getId() == null) {
            shopEntity.setId(shop.getId());
        }
        return shopEntity;
    }

    @Override
    public void createOrUpdateShop(Shop shop) {
        shopService.saveOrUpdate(shop);
    }

    @Override
    public boolean addShopPrincipals(Long shopId, List<Long> shopPrincipals) {
        return shopPrincipalService.saveBatch(shopPrincipals.stream()
                .map(shopPrincipalId -> {
                    final ShopPrincipal shopPrincipal = new ShopPrincipal();
                    shopPrincipal.setShopId(shopId);
                    shopPrincipal.setStaffId(shopPrincipalId);
                    return shopPrincipal;
                })
                .collect(Collectors.toList()));
    }

    @Override
    public boolean removeShopPrincipals(Long shopId, List<Long> shopPrincipals) {
        final LambdaQueryWrapper<ShopPrincipal> query = Wrappers.lambdaQuery();
        query.eq(ShopPrincipal::getShopId, shopId)
                .in(ShopPrincipal::getStaffId, shopPrincipals);
        return shopPrincipalService.remove(query);
    }

    @Override
    public List<Shop> queryShopList(ShopQuery shopQuery) {
        return shopMapper.queryShopList(shopQuery);
    }

    @Override
    public Page<Shop> pageQueryShopList(ShopQuery shopQuery) {
        final int totalCount = shopMapper.countShopList(shopQuery);
        final List<Shop> brandListItems = shopMapper.queryShopList(shopQuery);
        final Page<Shop> page = new Page<>(shopQuery.getPageIndex(), shopQuery.getPageSize(),
                totalCount);
        page.setRecords(brandListItems);
        return page;
    }

    @Override
    public List<ShopPrincipal> queryShopPrincipals(Long shopId) {
        return shopPrincipalService.lambdaQuery().eq(ShopPrincipal::getShopId, shopId).list();
    }

    @Override
    public List<ShopPrincipal> batchQueryShopPrincipals(List<Long> shopIds) {
        if (CollUtil.isEmpty(shopIds)) {
            return Collections.emptyList();
        }
        return shopPrincipalService.lambdaQuery().in(ShopPrincipal::getShopId, shopIds).list();
    }

    @Override
    public ShopEntity getShopEntity(Long id) {
        final Shop shop = shopService.getById(id);
        if (shop == null) {
            return null;
        }
        final ShopEntity shopEntity = ShopTransMapper.INSTANCE.toShopEntity(shop);
        final LambdaQueryWrapper<ShopPrincipal> queryPrincipals = new LambdaQueryWrapper<>();
        queryPrincipals.eq(ShopPrincipal::getShopId, id);
        shopEntity.setPrincipals(shopPrincipalMapper.selectList(queryPrincipals).stream()
                .map(ShopPrincipal::getStaffId)
                .collect(Collectors.toList()));

//        shopEntity.setAutoAllocateInventory(shop.getAutoAllocateInventory());
//        List<ShopInventorySetting> shopInventorySettings = iShopInventorySettingService.lambdaQuery().eq(ShopInventorySetting::getShopId, id).list();
//        if (CollUtil.isNotEmpty(shopInventorySettings)) {
//            ShopInventorySetting shopInventorySetting = shopInventorySettings.get(0);
//            shopEntity.setSyncFrequency(shopInventorySetting.getSyncFrequency());
//            shopEntity.setSafetyThreshold(shopInventorySetting.getSafetyThreshold());
//            shopEntity.setInventoryMode(shopInventorySetting.getInventoryMode());
//        } else {
//            shopEntity.setSyncFrequency(null);
//            shopEntity.setSafetyThreshold(null);
//        }

//        List<ShopInventory> shopInventoryList = iShopInventoryService.lambdaQuery().eq(ShopInventory::getShopId, id).list();
//        List<String> warehouseNos = shopInventoryList.stream().map(ShopInventory::getWarehouseNo).distinct().collect(Collectors.toList());
//        Map<String, StockStatisticPageVO> stockSpecVoMap = new HashMap<>(warehouseNos.size());
//        if (CollUtil.isNotEmpty(warehouseNos)) {
//            StockStatisticPageQuery stockSpecQuery = new StockStatisticPageQuery();
//            stockSpecQuery.setNos(warehouseNos);
//            stockSpecQuery.setBusinessLine(ListUtil.of(0, 1, 2, 3));
//            stockSpecQuery.setType(0);
//            PageResponse<StockStatisticPageVO> pageVOPageResponse = stockStatisticBizService.query(stockSpecQuery);
//            List<StockStatisticPageVO> data = pageVOPageResponse.getData();
//            if (CollUtil.isNotEmpty(data)) {
//                stockSpecVoMap = data.stream().collect(Collectors.toMap(StockStatisticPageVO::getWarehouseNo, Function.identity()));
//            }
//        }

//        List<ShopWarehouseInventoryVo> tList = new LinkedList<>();
//        for (ShopInventory shopInventory : shopInventoryList) {
//            ShopWarehouseInventoryVo shopWarehouseInventoryVo = new ShopWarehouseInventoryVo();
//            shopWarehouseInventoryVo.setId(shopInventory.getId());
//            shopWarehouseInventoryVo.setWarehouseNo(shopInventory.getWarehouseNo());
//            shopWarehouseInventoryVo.setWarehouseName(shopInventory.getWarehouseName());
//            shopWarehouseInventoryVo.setInventoryRatio(shopInventory.getInventoryRatio());
//            StockStatisticPageVO stockStatisticPageVO = stockSpecVoMap.get(shopInventory.getWarehouseNo());
//
//            boolean isVw = shopWarehouseInventoryVo.getWarehouseNo().startsWith("VWH");
//            if (!isVw) {
//                if (Objects.nonNull(stockStatisticPageVO)) {
//                    BigDecimal ratioVal = new BigDecimal(shopWarehouseInventoryVo.getInventoryRatio()).divide(new BigDecimal(100), 2, RoundingMode.DOWN);
//                    shopWarehouseInventoryVo.setStockNum(ratioVal.multiply(new BigDecimal(stockStatisticPageVO.getStockNum())).setScale(0, RoundingMode.HALF_UP));
//                    shopWarehouseInventoryVo.setSendStockNum(ratioVal.multiply(new BigDecimal(stockStatisticPageVO.getAvailableSendNum())).setScale(0, RoundingMode.HALF_UP));
//                    shopWarehouseInventoryVo.setUsableStockNum(ratioVal.multiply(new BigDecimal(stockStatisticPageVO.getAvailableNum())).setScale(0, RoundingMode.HALF_UP));
//                }
//            } else {
//                List<String> realWarehouseNos = iVirtualWarehouseInventoryService.lambdaQuery().eq(VirtualWarehouseInventory::getVirtualWarehouseNo, shopWarehouseInventoryVo.getWarehouseNo()).list()
//                        .stream().map(VirtualWarehouseInventory::getWarehouseNo).collect(Collectors.toList());
//                final WdtStockSpecStatisticQuery thisVwQuery = new WdtStockSpecStatisticQuery();
//                thisVwQuery.setWarehouseNos(realWarehouseNos);
//                Map<String, WarehouseStockSpecStatistic> vwWarehouseStatics = iWdtStockSpecService.statistics(thisVwQuery);
//                if (CollUtil.isNotEmpty(vwWarehouseStatics)) {
//                    BigDecimal ratioVal = new BigDecimal(shopWarehouseInventoryVo.getInventoryRatio()).divide(new BigDecimal(100), 2, RoundingMode.DOWN);
//                    Collection<WarehouseStockSpecStatistic> values = vwWarehouseStatics.values();
//
//                    BigDecimal getTotalStock = values.stream().map(WarehouseStockSpecStatistic::getTotalStock).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    shopWarehouseInventoryVo.setStockNum(ratioVal.multiply(getTotalStock).setScale(0, RoundingMode.HALF_UP));
//                    BigDecimal getTotalAvailableSendStock = values.stream().map(WarehouseStockSpecStatistic::getTotalAvailableSendStock).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    shopWarehouseInventoryVo.setSendStockNum(ratioVal.multiply(getTotalAvailableSendStock).setScale(0, RoundingMode.HALF_UP));
//                    BigDecimal getTotalAvailableStock = values.stream().map(WarehouseStockSpecStatistic::getTotalAvailableStock).reduce(BigDecimal.ZERO, BigDecimal::add);
//                    shopWarehouseInventoryVo.setUsableStockNum(ratioVal.multiply(getTotalAvailableStock).setScale(0, RoundingMode.HALF_UP));
//                }
//            }
//            tList.add(shopWarehouseInventoryVo);
//        }
//        shopEntity.setShopWarehouseInventoryVos(tList);
        return shopEntity;
    }

    @Override
    public void updateShopStatus(Long id, ShopStatus status) {
        shopService.lambdaUpdate().set(Shop::getStatus, status).eq(Shop::getId, id).update();
    }

    @Override
    public void deleteShop(Long id) {
        shopService.removeById(id);
    }

    @Override
    public List<ShopDropDownItem> dropDownList(ShopDropDownQuery query) {
        final List<ShopDropDownItem> shopDropDownItems = shopMapper.dropDownList(query);

        final Set<Long> shopIdSet = shopDropDownItems.stream().map(ShopDropDownItem::getId).collect(Collectors.toSet());
        if(CollUtil.isNotEmpty(shopIdSet)){
            final Map<Long, Set<Long>> shopIdPrincipalIdMap = shopPrincipalService.lambdaQuery()
                    .in(ShopPrincipal::getShopId, shopIdSet)
                    .list().stream().collect(Collectors.groupingBy(ShopPrincipal::getShopId,
                            Collectors.mapping(ShopPrincipal::getStaffId, Collectors.toSet())));
            final List<Long> principalIdList = shopIdPrincipalIdMap.values().stream().flatMap(Collection::stream)
                    .distinct().collect(Collectors.toList());
            final Map<Long, DadStaffVO> dadStaffVoMap = staffService.getStaffList(principalIdList).stream()
                    .collect(Collectors.toMap(DadStaffVO::getUserId, Function.identity()));

            shopDropDownItems.forEach(val->{
                val.setBusinessLineList(StrUtil.isNotBlank(val.getBusinessLine()) ?
                        Arrays.stream(val.getBusinessLine().split(StrUtil.COMMA)).map(Integer::valueOf).collect(Collectors.toList()) :
                        new LinkedList<>());

                List<DadStaffVO> shopDadStaffVoList = new LinkedList<>();
                final Long shopId = val.getId();
                final Set<Long> shopPrincipalIds = shopIdPrincipalIdMap.get(shopId);
                if(CollUtil.isNotEmpty(shopPrincipalIds)){
                    shopDadStaffVoList = shopPrincipalIds.stream()
                            .map(v -> dadStaffVoMap.getOrDefault(v, null))
                            .filter(Objects::nonNull).distinct().collect(Collectors.toList());
                }
                val.setPrincipalList(shopDadStaffVoList);
            });
        }

        return shopDropDownItems;
    }

    @Override
    public boolean setKingDeeId(Long shopId, String kingDeeId) {
        return shopService.lambdaUpdate().set(Shop::getKingDeeId, kingDeeId).eq(Shop::getId, shopId)
                .update();
    }

    @Override
    public Map<String, Shop> batchQueryShopBySn(Collection<String> shopSns) {
        return shopService.lambdaQuery().in(Shop::getSn, shopSns).list().stream()
                .collect(Collectors.toMap(Shop::getSn, Function.identity()));
    }

    @Override
    public Optional<Shop> queryShopBySn(String shopNo) {
        return shopService.lambdaQuery().eq(Shop::getSn, shopNo).oneOpt();
    }

    @Override
    public Shop queryShopById(Long shopId) {
        if (shopId == null) return null;
        return shopService.getById(shopId);
    }

    @Override
    public Optional<Shop> queryShopByIdOpt(Long shopId) {
        return Optional.ofNullable(shopId).map(shopService::getById);
    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public Boolean saveShopInventorySetting(Long shopId, String shopNo, CreateOrUpdateShopCmd cmd) {
//
//        cmd.getShopStockDtoList().stream().map(ShopWarehouseInventoryDto::getWarehouseNo)
//                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
//                .forEach((warehouseNo, count) -> {
//                    if (count > 1) {
//                        throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "仓库：" + warehouseNo + "不得重复");
//                    }
//                });
//        ShopInventorySetting shopInventorySetting = new ShopInventorySetting();
//        List<ShopInventorySetting> shopInventorySettings = iShopInventorySettingService.lambdaQuery()
//                .eq(ShopInventorySetting::getShopId, shopId)
//                .last("limit 1").list();
//        Long id = null;
//        InventoryMode oldMode = null;
//        if (CollUtil.isNotEmpty(shopInventorySettings)) {
//            id = shopInventorySettings.get(0).getId();
//            oldMode = shopInventorySettings.get(0).getInventoryMode();
//        }
//        shopInventorySetting.setId(id);
//        shopInventorySetting.setSyncFrequency(cmd.getSyncFrequency());
//        shopInventorySetting.setSafetyThreshold(cmd.getSafetyThreshold());
//        shopInventorySetting.setShopId(shopId);
//        shopInventorySetting.setShopNo(shopNo);
//        shopInventorySetting.setInventoryMode(cmd.getInventoryMode());
//        iShopInventorySettingService.saveOrUpdate(shopInventorySetting);
//
//        InventoryModelMonitor modelMonitor = InventoryModelMonitor.NO_CHANGE;
//        if (Objects.nonNull(oldMode)) {
//            if (InventoryMode.LOCK.equals(oldMode) && InventoryMode.SHARED.equals(cmd.getInventoryMode())) {
//                modelMonitor = InventoryModelMonitor.LOCK_TO_SHARE;
//            }
//            if (InventoryMode.SHARED.equals(oldMode) && InventoryMode.LOCK.equals(cmd.getInventoryMode())) {
//                modelMonitor = InventoryModelMonitor.SHARE_TO_LOCK;
//            }
//        }
//
//        // --------------------------------- 店铺库存设置处理完毕 --------------------------------------------------------------------------
//
//        // prepare data
//        List<ShopWarehouseInventoryDto> reqShopStockDtoList = cmd.getShopStockDtoList();
//        List<ShopInventory> oldShopInventories = iShopInventoryService.lambdaQuery().eq(ShopInventory::getShopId, shopId).list();
//        List<Long> oldShopInventoryIds = oldShopInventories.stream().map(ShopInventory::getId).collect(Collectors.toList());
//
//        List<ShopWarehouseInventoryDto> shopStockDtoList = cmd.getShopStockDtoList();
//        List<InventoryGoodsSaveCmd> inventoryGoodsSaveCmdList = cmd.getInventoryGoodsSaveCmdList();
//        Map<String, List<InventoryGoodsSaveCmd>> inventoryGoodsSaveCmdMap = new HashMap<>(32);
//        if (CollUtil.isNotEmpty(inventoryGoodsSaveCmdList)) {
//            inventoryGoodsSaveCmdMap = inventoryGoodsSaveCmdList.stream()
//                    .collect(Collectors.groupingBy(InventoryGoodsSaveCmd::getWarehouseNo));
//        }
//
//        // 锁住虚拟仓的编辑
//        for (ShopWarehouseInventoryDto inventoryDto : shopStockDtoList) {
//            RLock lock = redissonClient.getLock("update_vw_" + inventoryDto.getWarehouseNo());
//            if (!lock.tryLock()) {
//                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "此仓库" + inventoryDto.getWarehouseNo() + "正在编辑中，请稍后再试");
//            }
//        }
//        try {
//            // 移除虚拟仓编辑
//            List<Long> reqShopInventoryIds = reqShopStockDtoList.stream().map(ShopWarehouseInventoryDto::getId).collect(Collectors.toList());
//            List<Long> removeIds = oldShopInventoryIds.stream().filter(oldId -> !reqShopInventoryIds.contains(oldId)).collect(Collectors.toList());
//            removeShopInventory(removeIds, shopId, cmd.getInventoryMode(), modelMonitor);
//
//            for (ShopWarehouseInventoryDto shopWarehouseInventoryDto : shopStockDtoList) {
//                String warehouseNo = shopWarehouseInventoryDto.getWarehouseNo();
//                boolean isVw = iVirtualWarehouseService.lambdaQuery().eq(VirtualWarehouse::getNo, warehouseNo).count() > 0L;
//                VirtualWarehouse virtualWarehouse = null;
//                if (isVw) {
//                    virtualWarehouse = iVirtualWarehouseService.lambdaQuery()
//                            .eq(VirtualWarehouse::getNo, warehouseNo)
//                            .orderByDesc(VirtualWarehouse::getId)
//                            .last("limit 1").list().get(0);
//                }
//                List<String> logList = new LinkedList<>();
//                boolean isAdd = Objects.isNull(shopWarehouseInventoryDto.getId());
//
//                List<InventoryGoodsSaveCmd> inventoryGoodsSaveList = inventoryGoodsSaveCmdMap
//                        .getOrDefault(shopWarehouseInventoryDto.getWarehouseNo(), new ArrayList<>());
//                inventoryGoodsSaveList = inventoryGoodsSaveList.stream().distinct().collect(Collectors.toList());
//
//                ShopInventoryRes shopInventoryRes = saveShopWarehouseInventory(shopId, isVw, shopWarehouseInventoryDto,
//                        logList, cmd.getInventoryMode());
//                warehouseGoodsInventoryHandler(shopId, shopInventoryRes, inventoryGoodsSaveList, logList, modelMonitor,
//                        isAdd, cmd.getInventoryMode(), isVw, virtualWarehouse);
//
//                if (CollUtil.isNotEmpty(logList)) {
//                    operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.SHOP,
//                            shopId, "占比修改：" + StrUtil.join("。", logList), null);
//                }
//            }
//        } catch (Exception e) {
//            log.error("更新店铺库存占比数据异常", e);
//            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, e.getMessage());
//        } finally {
//            for (ShopWarehouseInventoryDto inventoryDto : shopStockDtoList) {
//                RLock lock = redissonClient.getLock("update_vw_" + inventoryDto.getWarehouseNo());
//                if (lock.isHeldByCurrentThread()) {
//                    lock.unlock();
//                }
//            }
//        }
//        return true;
//    }


    @Override
    public List<Long> selectPrincipalShopIds(Long userId) {
        return shopPrincipalService.lambdaQuery().eq(ShopPrincipal::getStaffId, userId).list().stream().map(ShopPrincipal::getShopId).collect(
                Collectors.toList());
    }

//    private void removeShopInventory(List<Long> removeIds, Long shopId, InventoryMode oldMode, InventoryModelMonitor modelMonitor) {
//        if (CollUtil.isEmpty(removeIds)) {
//            return;
//        }
//
//        List<ShopInventory> shopInventoryList = iShopInventoryService.lambdaQuery().in(ShopInventory::getId, removeIds).list();
//        List<String> warehouseNos = shopInventoryList.stream().map(ShopInventory::getWarehouseNo).collect(Collectors.toList());
//        Map<String, Warehouse> warehouseMap = iWarehouseService.getBatchByNos(warehouseNos).stream().collect(Collectors.toMap(Warehouse::getNo, Function.identity()));
//        boolean lockToShare = InventoryModelMonitor.LOCK_TO_SHARE.equals(modelMonitor);
//        // 当库存模式为共享改为锁定，因为先执行的移除操作，这次这批仓库还没有锁定数据，这里就不处理。
//        // boolean shareToLock = InventoryModelMonitor.SHARE_TO_LOCK.equals(modelMonitor);
//        boolean removeLockDate = InventoryModelMonitor.NO_CHANGE.equals(modelMonitor)
//                && InventoryMode.LOCK.equals(oldMode);
//        boolean dealLockSkuRatio = lockToShare || removeLockDate;
//
//        for (ShopInventory shopInventory : shopInventoryList) {
//            String warehouseNo = shopInventory.getWarehouseNo();
//            Warehouse warehouse = warehouseMap.get(warehouseNo);
//            Assert.notNull(warehouse, "仓库编码非法" + warehouseNo);
//            Long inventoryId = shopInventory.getId();
//            boolean isVw = warehouse.getIsVirtualWarehouse() == 1;
//
//            // 移除一级占比
//            iWarehouseGoodsInventoryStaticsService.addWarehouseRatio(warehouseNo, 0, shopInventory.getInventoryRatio());
//
//            List<ShopInventoryGoods> shopInventoryGoodsList = iShopInventoryGoodsService.lambdaQuery()
//                    .eq(ShopInventoryGoods::getShopInventoryId, inventoryId)
//                    .list();
//            List<EditSkuRatioDto> editSkuRatioDtoList = new LinkedList<>();
//            List<LockSkuRatioDto> lockSkuRatioDtoList = new LinkedList<>();
//            for (ShopInventoryGoods inventoryGoods : shopInventoryGoodsList) {
//                // 移除二级占比
//                final EditSkuRatioDto editSkuRatioDto = EditSkuRatioDto.of(warehouseNo, inventoryGoods.getSkuNo(),
//                        inventoryGoods.getInventoryRatio(), 0);
//                editSkuRatioDtoList.add(editSkuRatioDto);
//
//                // 移除锁定占比
//                if (dealLockSkuRatio && isVw) {
//                    final LockSkuRatioDto lockSkuRatioDto = LockSkuRatioDto.of(warehouseNo, inventoryGoods.getSkuNo(),
//                            inventoryGoods.getInventoryRatio(), 0, inventoryGoods.getInventoryLockNum(), 0);
//                    lockSkuRatioDtoList.add(lockSkuRatioDto);
//                }
//            }
//
//            iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(editSkuRatioDtoList);
//            goodsInventoryLockStaticsService.handlerBatch(lockSkuRatioDtoList);
//            if (dealLockSkuRatio && !isVw) {
//                warehouseBizService.refreshSharedGoodsInventoryRatio(null, ListUtil.of(shopInventory.getShopId()), warehouseNo);
//            }
//        }
//
//        iShopInventoryService.removeByIdsWithTime(removeIds);
//        iShopInventoryGoodsService.lambdaUpdate().in(ShopInventoryGoods::getShopInventoryId, removeIds).remove();
//
//        operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.SHOP,
//                shopId, "移除库存仓库，" + StrUtil.join(StrUtil.COMMA, warehouseNos), null);
//    }


    /*@Deprecated
    private void removeHandler(List<String> allWarehouseSkuCodes, Long inventoryId, String warehouseNo, Integer oldInventory) {
        List<ShopInventoryGoods> shopInventoryGoodsList = iShopInventoryGoodsService.lambdaQuery().eq(ShopInventoryGoods::getShopInventoryId, inventoryId).list();
        if (CollUtil.isEmpty(shopInventoryGoodsList)) {
            List<EditSkuRatioDto> editSkuRatioDtoList = new LinkedList<>();
            for (String skuCode : allWarehouseSkuCodes) {
                EditSkuRatioDto editSkuRatioDto = new EditSkuRatioDto();
                editSkuRatioDto.setWarehouseNo(warehouseNo);
                editSkuRatioDto.setSkuNo(skuCode);
                editSkuRatioDto.setNewVal(0);
                editSkuRatioDto.setOldVal(oldInventory);
                editSkuRatioDtoList.add(editSkuRatioDto);
            }
            iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(editSkuRatioDtoList);
        } else {
            List<EditSkuRatioDto> editSkuRatioDtoList1 = new LinkedList<>();
            for (ShopInventoryGoods goods : shopInventoryGoodsList) {
                EditSkuRatioDto editSkuRatioDto = new EditSkuRatioDto();
                editSkuRatioDto.setWarehouseNo(warehouseNo);
                editSkuRatioDto.setSkuNo(goods.getSkuNo());
                editSkuRatioDto.setNewVal(0);
                editSkuRatioDto.setOldVal(goods.getInventoryRatio());
                editSkuRatioDtoList1.add(editSkuRatioDto);
            }
            iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(editSkuRatioDtoList1);

            List<String> skuCodes = shopInventoryGoodsList.stream().map(ShopInventoryGoods::getSkuNo).collect(Collectors.toList());
            allWarehouseSkuCodes.removeAll(skuCodes);
            List<EditSkuRatioDto> editSkuRatioDtoList2 = new LinkedList<>();
            for (String skuCode : allWarehouseSkuCodes) {
                EditSkuRatioDto editSkuRatioDto = new EditSkuRatioDto();
                editSkuRatioDto.setWarehouseNo(warehouseNo);
                editSkuRatioDto.setSkuNo(skuCode);
                editSkuRatioDto.setNewVal(0);
                editSkuRatioDto.setOldVal(oldInventory);
                editSkuRatioDtoList2.add(editSkuRatioDto);
            }
            iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(editSkuRatioDtoList2);
        }
    }
    @Data
    @Deprecated
    private static class WarehouseDto {
        private String warehouse;
        private Integer ratio;
        private Integer version;
    }*/


//    @Data
//    private static class ShopInventoryRes {
//        ShopInventory shopInventory;
//        Boolean isVw;
//        Integer oldWarehouseInventory;
//        Integer newWarehouseInventory;
//        Boolean inventoryChange;
//    }

//    private ShopInventoryRes saveShopWarehouseInventory(Long shopId, Boolean isVw,
//                                                        ShopWarehouseInventoryDto shopWarehouseInventoryDto,
//                                                        List<String> logList, InventoryMode inventoryMode) {
//        ShopInventoryRes res = new ShopInventoryRes();
//        res.setIsVw(isVw);
//
//        boolean isAdd = Objects.isNull(shopWarehouseInventoryDto.getId());
//        boolean inventoryChange;
//
//        // 实仓处理
//        if (!isVw) {
//            String warehouseNo = shopWarehouseInventoryDto.getWarehouseNo();
//            Integer inventoryRatio = shopWarehouseInventoryDto.getInventoryRatio();
//            if (isAdd) {
//                res.setOldWarehouseInventory(0);
//                res.setNewWarehouseInventory(inventoryRatio);
//                inventoryChange = true;
//                logList.add("新增仓库：" + warehouseNo);
//            } else {
//                ShopInventory oldShopInventory = iShopInventoryService.getById(shopWarehouseInventoryDto.getId());
//                Assert.notNull(oldShopInventory, "店铺库存设置ID非法，" + shopWarehouseInventoryDto.getId());
//                res.setOldWarehouseInventory(oldShopInventory.getInventoryRatio());
//                res.setNewWarehouseInventory(inventoryRatio);
//                inventoryChange = !oldShopInventory.getInventoryRatio().equals(shopWarehouseInventoryDto.getInventoryRatio());
//                if (inventoryChange) {
//                    logList.add(StrUtil.format("仓库：{}修改占比：{}%改为{}%", warehouseNo, oldShopInventory.getInventoryRatio(), shopWarehouseInventoryDto.getInventoryRatio()));
//                }
//            }
//            iWarehouseGoodsInventoryStaticsService.addWarehouseRatio(warehouseNo, res.getNewWarehouseInventory(), res.getOldWarehouseInventory());
//            res.setInventoryChange(inventoryChange);
//
//            ShopInventory shopInventory = new ShopInventory();
//            shopInventory.setId(shopWarehouseInventoryDto.getId());
//            shopInventory.setShopId(shopId);
//            shopInventory.setWarehouseNo(warehouseNo);
//            shopInventory.setWarehouseName(shopWarehouseInventoryDto.getWarehouseName());
//            shopInventory.setInventoryRatio(inventoryRatio);
//            shopInventory.setInventoryMode(inventoryMode);
//            iShopInventoryService.saveOrUpdate(shopInventory);
//            res.setShopInventory(shopInventory);
//
//            return res;
//        }
//
//        // ---------------------------------------------------------------------
//
//        // 虚拟仓处理
//        if (isAdd) {
//            ShopInventory shopInventory = new ShopInventory();
//            shopInventory.setShopId(shopId);
//            shopInventory.setWarehouseNo(shopWarehouseInventoryDto.getWarehouseNo());
//            shopInventory.setWarehouseName(shopWarehouseInventoryDto.getWarehouseName());
//            shopInventory.setInventoryRatio(shopWarehouseInventoryDto.getInventoryRatio());
//            shopInventory.setInventoryMode(inventoryMode);
//            iShopInventoryService.save(shopInventory);
//
//            res.setShopInventory(shopInventory);
//            res.setInventoryChange(true);
//            res.setNewWarehouseInventory(shopWarehouseInventoryDto.getInventoryRatio());
//            res.setOldWarehouseInventory(0);
//            logList.add("新增仓库：" + shopWarehouseInventoryDto.getWarehouseNo());
//            iWarehouseGoodsInventoryStaticsService.addWarehouseRatio(shopWarehouseInventoryDto.getWarehouseNo(), shopWarehouseInventoryDto.getInventoryRatio(), 0);
//            return res;
//        } else {
//            ShopInventory oldShopInventory = iShopInventoryService.getById(shopWarehouseInventoryDto.getId());
//            Integer oldRatio = oldShopInventory.getInventoryRatio();
//            inventoryChange = !oldRatio.equals(shopWarehouseInventoryDto.getInventoryRatio());
//            if (inventoryChange) {
//                logList.add(StrUtil.format("仓库：{}修改占比：{}%改为{}%", shopWarehouseInventoryDto.getWarehouseNo(), oldShopInventory.getInventoryRatio(), shopWarehouseInventoryDto.getInventoryRatio()));
//                iWarehouseGoodsInventoryStaticsService.addWarehouseRatio(shopWarehouseInventoryDto.getWarehouseNo(), shopWarehouseInventoryDto.getInventoryRatio(), oldShopInventory.getInventoryRatio());
//            }
//            res.setNewWarehouseInventory(shopWarehouseInventoryDto.getInventoryRatio());
//            res.setOldWarehouseInventory(oldRatio);
//            oldShopInventory.setInventoryRatio(shopWarehouseInventoryDto.getInventoryRatio());
//            oldShopInventory.setInventoryMode(inventoryMode);
//            iShopInventoryService.updateById(oldShopInventory);
//
//            res.setShopInventory(oldShopInventory);
//            res.setInventoryChange(inventoryChange);
//            return res;
//        }
//    }

//    private void warehouseGoodsInventoryHandler(Long shopId, ShopInventoryRes shopInventoryRes,
//                                                List<InventoryGoodsSaveCmd> thisWarehouseGoodsList, List<String> logList,
//                                                InventoryModelMonitor modelMonitor, Boolean isAdd, InventoryMode inventoryMode,
//                                                Boolean isVw, VirtualWarehouse virtualWarehouse) {
//
//        ShopInventory shopInventory = shopInventoryRes.getShopInventory();
//        String upperWarehouseNo = shopInventory.getWarehouseNo();
//        Integer newWarehouseInventory = shopInventoryRes.getNewWarehouseInventory();
//        Boolean inventoryChange = shopInventoryRes.getInventoryChange();
//
//        // 一级占比没有变化，没有指定二级占比，库存模式没有发生改变
//        boolean b1 = !inventoryChange && CollUtil.isEmpty(thisWarehouseGoodsList);
//        boolean b2 = InventoryModelMonitor.NO_CHANGE.equals(modelMonitor);
//        if (b1 && b2) {
//            return;
//        }
//        Map<String, Integer> skuInventoryRatioMap = CollUtil.isNotEmpty(thisWarehouseGoodsList)
//                ? thisWarehouseGoodsList.stream()
//                .collect(Collectors.toMap(InventoryGoodsSaveCmd::getSkuNo, InventoryGoodsSaveCmd::getInventoryRatio))
//                : new HashMap<>(256);
//
//        // 新增数据
//        if (isAdd) {
//            addWarehouseGoodsInventory(shopId, shopInventory, upperWarehouseNo, newWarehouseInventory,
//                    skuInventoryRatioMap, logList, inventoryMode, isVw);
//        }
//
//        // 编辑
//        else {
//            updateWarehouseGoodsInventory(shopId, shopInventory, upperWarehouseNo, newWarehouseInventory,
//                    skuInventoryRatioMap, logList, inventoryMode, isVw, modelMonitor);
//        }
//    }

//    private void updateWarehouseGoodsInventory(Long shopId, ShopInventory shopInventory,
//                                               String upperWarehouseNo, Integer newWarehouseInventory,
//                                               Map<String, Integer> skuInventoryRatioMap,
//                                               List<String> logList,
//                                               InventoryMode inventoryMode,
//                                               Boolean isVw,
//                                               InventoryModelMonitor modelMonitor) {
//        final List<ShopInventoryGoods> goodsList = iShopInventoryGoodsService.lambdaQuery()
//                .eq(ShopInventoryGoods::getShopId, shopId)
//                .eq(ShopInventoryGoods::getShopInventoryId, shopInventory.getId())
//                .list();
//        if (CollUtil.isEmpty(goodsList)) {
//            return;
//        }
//
//        boolean lockToShare = InventoryModelMonitor.LOCK_TO_SHARE.equals(modelMonitor);
//        boolean shareToLock = InventoryModelMonitor.SHARE_TO_LOCK.equals(modelMonitor);
//        boolean editLockDate = InventoryModelMonitor.NO_CHANGE.equals(modelMonitor) && InventoryMode.LOCK.equals(inventoryMode);
//        Map<String, BigDecimal> skuStockMap = (shareToLock || editLockDate) ?
//                allSkuAvailableStockMap(upperWarehouseNo, null)
//                : new HashMap<>(256);
//        List<ShopInventoryGoods> updateGoodsOneList = new LinkedList<>();
//        List<EditSkuRatioDto> updateSkuRatioList = new LinkedList<>();
//        List<LockSkuRatioDto> updateSkuLockRatioList = new LinkedList<>();
//        List<String> subLogStrList = new LinkedList<>();
//
//        for (ShopInventoryGoods goodsOne : goodsList) {
//            int setInventoryRatio = newWarehouseInventory;
//            if (Objects.nonNull(skuInventoryRatioMap.get(goodsOne.getSkuNo()))) {
//                setInventoryRatio = skuInventoryRatioMap.get(goodsOne.getSkuNo());
//
//                String subLogStr = StrUtil.format("sku:{},仓库编码:{},库存占比从{}%改为{}%",
//                        goodsOne.getSkuNo(), goodsOne.getWarehouseNo(), goodsOne.getInventoryRatio(), setInventoryRatio);
//                subLogStrList.add(subLogStr);
//            }
//
//            final EditSkuRatioDto addGoodsOneRatio = EditSkuRatioDto.of(upperWarehouseNo, goodsOne.getSkuNo(),
//                    goodsOne.getInventoryRatio(), setInventoryRatio);
//            updateSkuRatioList.add(addGoodsOneRatio);
//
//            if (!isVw) {
//                if (lockToShare) {
//                    final LockSkuRatioDto lockSkuRatioDto = LockSkuRatioDto.of(goodsOne.getWarehouseNo(), goodsOne.getSkuNo(),
//                            goodsOne.getInventoryRatio(), 0, goodsOne.getInventoryLockNum(), 0);
//                    goodsOne.setInventoryLockNum(0);
//                    goodsOne.setInventoryRatio2(BigDecimal.valueOf(setInventoryRatio));
//                    updateSkuLockRatioList.add(lockSkuRatioDto);
//                } else if (shareToLock) {
//                    int skuLockNum = skuLockNum(setInventoryRatio, skuStockMap.getOrDefault(goodsOne.getSkuNo(), BigDecimal.ZERO));
//                    final LockSkuRatioDto lockSkuRatioDto = LockSkuRatioDto.of(goodsOne.getWarehouseNo(), goodsOne.getSkuNo(),
//                            0, setInventoryRatio, 0, skuLockNum);
//                    goodsOne.setInventoryLockNum(skuLockNum);
//                    goodsOne.setInventoryRatio2(BigDecimal.valueOf(setInventoryRatio));
//                    updateSkuLockRatioList.add(lockSkuRatioDto);
//                } else if (editLockDate) {
//                    int skuLockNum = skuLockNum(setInventoryRatio, skuStockMap.getOrDefault(goodsOne.getSkuNo(), BigDecimal.ZERO));
//                    final LockSkuRatioDto lockSkuRatioDto = LockSkuRatioDto.of(goodsOne.getWarehouseNo(), goodsOne.getSkuNo(),
//                            goodsOne.getInventoryRatio(), setInventoryRatio, goodsOne.getInventoryLockNum(), skuLockNum);
//                    goodsOne.setInventoryLockNum(skuLockNum);
//                    goodsOne.setInventoryRatio2(BigDecimal.valueOf(setInventoryRatio));
//                    updateSkuLockRatioList.add(lockSkuRatioDto);
//                }
//            }
//
//            goodsOne.setInventoryRatio(setInventoryRatio);
//            updateGoodsOneList.add(goodsOne);
//        }
//
//        iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(updateSkuRatioList);
//        goodsInventoryLockStaticsService.handlerBatch(updateSkuLockRatioList);
//        if (CollUtil.isNotEmpty(updateGoodsOneList)) {
//            iShopInventoryGoodsService.updateBatchById(updateGoodsOneList);
//        }
//        if (lockToShare || shareToLock || editLockDate) {
//            warehouseBizService.refreshSharedGoodsInventoryRatio(null, ListUtil.of(shopId), upperWarehouseNo);
//        }
//
//        if (CollUtil.isNotEmpty(subLogStrList)) {
//            final String subLog = String.join(StrUtil.COMMA, subLogStrList);
//            logList.add("指定情况下，" + subLog);
//        }
//
//        logList.add(StrUtil.format("一般情况下，SKU库存占比统一改为{}%", newWarehouseInventory));
//    }

//    private void addWarehouseGoodsInventory(Long shopId, ShopInventory shopInventory,
//                                            String upperWarehouseNo, Integer newWarehouseInventory,
//                                            Map<String, Integer> skuInventoryRatioMap,
//                                            List<String> logList,
//                                            InventoryMode inventoryMode,
//                                            Boolean isVw) {
//
//        boolean addLockData = InventoryMode.LOCK.equals(inventoryMode);
//        List<WdtStockSpec> thisWarehouseAllSkuList = new LinkedList<>();
//        if (isVw) {
//            final List<String> realWarehouseNoList = iVirtualWarehouseInventoryService.lambdaQuery()
//                    .eq(VirtualWarehouseInventory::getVirtualWarehouseNo, upperWarehouseNo)
//                    .select(VirtualWarehouseInventory::getWarehouseNo)
//                    .list().stream().map(VirtualWarehouseInventory::getWarehouseNo).collect(Collectors.toList());
//            if (CollUtil.isNotEmpty(realWarehouseNoList)) {
//                thisWarehouseAllSkuList = iWdtStockSpecService.lambdaQuery()
//                        .in(WdtStockSpec::getWarehouseNo, realWarehouseNoList)
//                        .eq(WdtStockSpec::getDefect, 0)
//                        .list().stream().distinct().collect(Collectors.toList());
//            }
//        } else {
//            thisWarehouseAllSkuList = iWdtStockSpecService.lambdaQuery()
//                    .eq(WdtStockSpec::getWarehouseNo, upperWarehouseNo)
//                    .eq(WdtStockSpec::getDefect, 0)
//                    .list().stream().distinct().collect(Collectors.toList());
//        }
//        List<ShopInventoryGoods> addGoodsOneList = new LinkedList<>();
//        List<EditSkuRatioDto> addSkuRatioList = new LinkedList<>();
//        List<LockSkuRatioDto> addSkuLockRatioList = new LinkedList<>();
//        Map<String, BigDecimal> skuStockMap = addLockData ?
//                thisWarehouseAllSkuList.stream()
//                        .collect(Collectors.toMap(WdtStockSpec::getSpecNo, WdtStockSpec::getAvailableStock, (a, b) -> a))
//                : new HashMap<>(256);
//
//        for (WdtStockSpec wdtStockSpec : thisWarehouseAllSkuList) {
//            int setInventoryRatio = Objects.isNull(skuInventoryRatioMap.get(wdtStockSpec.getSpecNo())) ?
//                    newWarehouseInventory : skuInventoryRatioMap.get(wdtStockSpec.getSpecNo());
//            final ShopInventoryGoods goodsOne = buildAddOne(shopId, upperWarehouseNo, setInventoryRatio,
//                    shopInventory.getId(), wdtStockSpec);
//            // 二级占比
//            final EditSkuRatioDto addGoodsOneRatio = EditSkuRatioDto.of(upperWarehouseNo, goodsOne.getSkuNo(), 0, setInventoryRatio);
//            addSkuRatioList.add(addGoodsOneRatio);
//
//            if (addLockData) {
//                // 实仓，锁定数据
//                if (!isVw) {
//                    int skuLockNumVal = skuLockNum(setInventoryRatio, skuStockMap.getOrDefault(goodsOne.getSkuNo(), BigDecimal.ZERO));
//                    goodsOne.setInventoryLockNum(skuLockNumVal);
//                    goodsOne.setInventoryRatio2(BigDecimal.valueOf(setInventoryRatio));
//                    final LockSkuRatioDto lockSkuRatioDto = LockSkuRatioDto.of(goodsOne.getWarehouseNo(), goodsOne.getSkuNo(),
//                            0, setInventoryRatio, 0, skuLockNumVal);
//                    addSkuLockRatioList.add(lockSkuRatioDto);
//                }
//                // 虚拟仓，锁定数据
//                else {
//                    // 不处理
//                }
//            }
//
//            addGoodsOneList.add(goodsOne);
//        }
//
//        iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(addSkuRatioList);
//        goodsInventoryLockStaticsService.handlerBatch(addSkuLockRatioList);
//        iShopInventoryGoodsService.saveBatch(addGoodsOneList);
//
//        if (addLockData) {
//            if (!isVw) {
//                warehouseBizService.refreshSharedGoodsInventoryRatio(null, ListUtil.of(shopId), upperWarehouseNo);
//            }
//        }
//
//        String logStr = StrUtil.format("新增SKU库存占比，仓库编码:{}", upperWarehouseNo);
//        logList.add(logStr);
//    }


//    private Map<String, BigDecimal> allSkuAvailableStockMap(String warehouseNo, List<String> allSkuCodes) {
//        StockSpecQuery stockSpecQuery = new StockSpecQuery();
//        stockSpecQuery.setWarehouseNos(ListUtil.of(warehouseNo));
//        if (CollUtil.isNotEmpty(allSkuCodes)) {
//            stockSpecQuery.setSkuCodes(allSkuCodes);
//        }
//        stockSpecQuery.setPageIndex(1);
//        stockSpecQuery.setPageSize(9999);
//        stockSpecQuery.setBusinessLine(ListUtil.of(0, 1, 2, 3));
//        PageResponse<AvailableStockSpecVO> stockSpecVoPageResponse = stockSpecBizService.availableStockQuery(stockSpecQuery);
//        org.springframework.util.Assert.isTrue(stockSpecVoPageResponse.isSuccess(), "查询下属SKU可用库存统计的数据失败，仓库：" + warehouseNo);
//        return stockSpecVoPageResponse.getData().stream()
//                .collect(Collectors.toMap(AvailableStockSpecVO::getSkuCode,
//                        AvailableStockSpecVO::getAvailableStock
//                        , (a, b) -> a));
//    }


//    private Integer skuLockNum(Integer ratio, BigDecimal stockNum) {
//        if (ratio == 0 || BigDecimal.ZERO.compareTo(stockNum) == 0) {
//            return 0;
//        }
//        return new BigDecimal(ratio).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
//                .multiply(stockNum)
//                .setScale(0, RoundingMode.HALF_UP).intValue();
//    }

//    private ShopInventoryGoods buildAddOne(Long shopId, String warehouseNo, Integer ratio, Long inventoryId, WdtStockSpec wdtStockSpec) {
//        ShopInventoryGoods shopInventoryGoods = new ShopInventoryGoods();
//        shopInventoryGoods.setShopId(shopId);
//        shopInventoryGoods.setWarehouseNo(warehouseNo);
//        shopInventoryGoods.setSpuNo(wdtStockSpec.getGoodsNo());
//        shopInventoryGoods.setSkuNo(wdtStockSpec.getSpecNo());
//        shopInventoryGoods.setInventoryRatio(ratio);
//        shopInventoryGoods.setInventoryRatio2(BigDecimal.valueOf(ratio));
//        shopInventoryGoods.setShopInventoryId(inventoryId);
//        return shopInventoryGoods;
//    }


    @Override
    public Optional<Shop> getByShopNo(String shopNo) {
        return shopService.lambdaQuery().eq(Shop::getSn, shopNo).oneOpt();
    }
    
    @Override
    public Optional<Shop> cacheGetByShopNo(String shopNo) {
        return byShopNoCache.get(shopNo);
    }
    
    @Override
    public Optional<Shop> cacheGetByMallShopId(Long mallShopId) {
        return byMallShopIdCache.get(mallShopId);
    }
    
    //    @Override
//    public com.alibaba.cola.dto.Response preDataProcessing(Long id) {
//        final List<ShopInventory> shopInventoryList = iShopInventoryService.lambdaQuery()
//                .eq(ShopInventory::getShopId, id)
//                .list();
//        Assert.state(CollUtil.isNotEmpty(shopInventoryList), "店铺下仓库占比设置必须存在");
//
//        ThreadUtil.execute(PoolEnum.COMMON_POOL,
//                () -> shopInventoryList.forEach(shopInventory -> {
//            final String warehouseNo = shopInventory.getWarehouseNo();
//            try {
//                List<WdtStockSpec> allWarehouseAllSkuList = iWdtStockSpecService.lambdaQuery()
//                        .eq(WdtStockSpec::getWarehouseNo, warehouseNo)
//                        .eq(WdtStockSpec::getDefect, 0)
//                        .list();
//                final List<ShopInventoryGoods> goodsList = iShopInventoryGoodsService.lambdaQuery()
//                        .eq(ShopInventoryGoods::getShopInventoryId, shopInventory.getId())
//                        .list();
//                List<ShopInventoryGoods> addGoodsList = new LinkedList<>();
//
//                if (CollUtil.isEmpty(goodsList)) {
//                    for (WdtStockSpec wdtStockSpec : allWarehouseAllSkuList) {
//                        ShopInventoryGoods addShopGoodsOne = new ShopInventoryGoods();
//                        addShopGoodsOne.setShopId(shopInventory.getShopId());
//                        addShopGoodsOne.setWarehouseNo(warehouseNo);
//                        addShopGoodsOne.setSpuNo(wdtStockSpec.getGoodsNo());
//                        addShopGoodsOne.setSkuNo(wdtStockSpec.getSpecNo());
//                        addShopGoodsOne.setInventoryRatio(shopInventory.getInventoryRatio());
//                        addShopGoodsOne.setInventoryRatio2(new BigDecimal(shopInventory.getInventoryRatio()));
//                        addShopGoodsOne.setShopInventoryId(shopInventory.getId());
//                        addGoodsList.add(addShopGoodsOne);
//                    }
//                } else {
//                    final Set<String> goodsSkuSet = goodsList.stream().map(ShopInventoryGoods::getSkuNo).collect(Collectors.toSet());
//                    for (WdtStockSpec wdtStockSpec : allWarehouseAllSkuList) {
//                        if (!goodsSkuSet.contains(wdtStockSpec.getSpecNo())) {
//                            ShopInventoryGoods addShopGoodsOne = new ShopInventoryGoods();
//                            addShopGoodsOne.setShopId(shopInventory.getShopId());
//                            addShopGoodsOne.setWarehouseNo(warehouseNo);
//                            addShopGoodsOne.setSpuNo(wdtStockSpec.getGoodsNo());
//                            addShopGoodsOne.setSkuNo(wdtStockSpec.getSpecNo());
//                            addShopGoodsOne.setInventoryRatio(shopInventory.getInventoryRatio());
//                            addShopGoodsOne.setInventoryRatio2(new BigDecimal(shopInventory.getInventoryRatio()));
//                            addShopGoodsOne.setShopInventoryId(shopInventory.getId());
//                            addGoodsList.add(addShopGoodsOne);
//                        }
//                    }
//                }
//
//                if (CollUtil.isNotEmpty(addGoodsList)) {
//                    final List<EditSkuRatioDto> editSkuRatioDtoList = addGoodsList.stream()
//                            .map(val -> EditSkuRatioDto.of(warehouseNo, val.getSkuNo(), 0, val.getInventoryRatio()))
//                            .collect(Collectors.toList());
//                    iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(editSkuRatioDtoList);
//
//                    iShopInventoryGoodsService.saveBatch(addGoodsList);
//                }
//
//            } catch (Exception e) {
//                log.error("preDataProcessing error,warehouseNo:{}", warehouseNo, e);
//            }
//
//        }));
//
//        return Response.buildSuccess();
//    }
}
