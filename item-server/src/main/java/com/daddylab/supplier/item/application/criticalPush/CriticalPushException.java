package com.daddylab.supplier.item.application.criticalPush;

/**
 * <AUTHOR>
 * @since 2022/4/16
 */
public class CriticalPushException extends RuntimeException {

    private static final long serialVersionUID = 1L;
    private Long pushId;

    public CriticalPushException(String message) {
        super(message);
    }

    public CriticalPushException(String message, Throwable cause) {
        super(message, cause);
    }

    public CriticalPushException(Long pushId, String message) {
        super(message);
        this.pushId = pushId;
    }

    public CriticalPushException(Long pushId, String message, Throwable cause) {
        super(message, cause);
        this.pushId = pushId;
    }

    public Long getPushId() {
        return pushId;
    }
}
