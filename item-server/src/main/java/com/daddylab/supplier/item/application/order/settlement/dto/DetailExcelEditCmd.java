package com.daddylab.supplier.item.application.order.settlement.dto;

import cn.hutool.core.lang.Assert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.javers.core.metamodel.annotation.DiffIgnore;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR> up
 * @date 2023年08月10日 5:18 PM
 */
@ApiModel("编辑结算法请求参数封装")
@Data
public class DetailExcelEditCmd {

    @DiffIgnore
    @ApiModelProperty("id")
    private Long id;

    @DiffIgnore
    @ApiModelProperty("商品SKU")
    private String skuCode;

    @ApiModelProperty("结算周期。自然月1号0点，10位时间戳")
    @NotNull(message = "结算周期")
    private Long time;

    @ApiModelProperty("订单发货数量")
    @NotNull(message = "订单发货数量不得为空")
    public Integer deliverQuantity;

    public BigDecimal temporaryPrice;

    public Integer temporaryQuantity;

    @ApiModelProperty("当月退货")
    @NotNull(message = "当月退货不得为空")
    public Integer currentMonthRefundQuantity;

    @ApiModelProperty("跨月退货")
    @NotNull(message = "跨月退货不得为空")
    public Integer crossMonthRefundQuantity;

    @ApiModelProperty("结算单价")
    public BigDecimal settlementPrice;

    public BigDecimal getSettlementPrice() {
        return Objects.isNull(settlementPrice) ? BigDecimal.ZERO : settlementPrice;
    }

    @ApiModelProperty("结算单价")
    @NotNull(message = "结算单价不得为空")
    public Integer settlementQuantity;

    @ApiModelProperty("结算金额")
    public BigDecimal settlementAmount;

    public BigDecimal getSettlementAmount() {
        return Objects.isNull(settlementAmount) ? BigDecimal.ZERO : settlementAmount;
    }

    @ApiModelProperty("运费及售后分摊金额")
    @NotNull(message = "运费及售后分摊金额不得为空")
    private BigDecimal afterSalesCost;

    public BigDecimal getAfterSalesCost() {
        return Objects.isNull(afterSalesCost) ? BigDecimal.ZERO : afterSalesCost;
    }

    @ApiModelProperty("最终金额")
    @NotNull(message = "最终金额不得为空")
    private BigDecimal finalAmount;

    public BigDecimal getFinalAmount() {
        return Objects.isNull(finalAmount) ? BigDecimal.ZERO : finalAmount;
    }

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("来源")
    private String source;

    public void dataCheck() {
        boolean equals = (this.settlementAmount.add(this.afterSalesCost)).compareTo(finalAmount) == 0;
        Assert.state(equals, "sku:" + this.skuCode + "结算明细校验失败");
    }


}
