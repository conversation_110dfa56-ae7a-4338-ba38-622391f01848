package com.daddylab.supplier.item.application.category;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.CategoryTransMapper;
import com.daddylab.supplier.item.controller.category.dto.*;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.category.service.CategoryDomainService;
import com.daddylab.supplier.item.infrastructure.cache.Cache;
import com.daddylab.supplier.item.infrastructure.cache.Cache.KeySerialize;
import com.daddylab.supplier.item.infrastructure.cache.CacheEvict;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Category;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CategoryAttr;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CategoryMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICategoryAttrService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICategoryService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.tree.TreeFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MultiSet;
import org.apache.commons.collections4.multiset.HashMultiSet;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/27 11:57 上午
 * @description
 */
@Slf4j
@Service
public class CategoryBizServiceImpl implements CategoryBizService {

    @Autowired
    CategoryDomainService categoryDomainService;

    @Autowired
    private ICategoryService iCategoryService;

    @Autowired
    private ICategoryAttrService iCategoryAttrService;

    @Autowired
    private CategoryGateway categoryGateway;

    @Autowired
    private CategoryExtendConfig categoryExtendConfig;

    @Autowired
    private IItemService itemService;


    @Override
    @CacheEvict(keyPrefix = "categoryTree", keySerialize = KeySerialize.EMPTY)
    public SingleResponse<Long> add(CategoryCmd categoryCmd) {
        Long categoryId = categoryDomainService.addCategory(categoryCmd);
        // 每个品类都设置以下两个默认属性
        addAttr(categoryId, "颜色");
        addAttr(categoryId, "规格");

        return SingleResponse.of(categoryId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(keyPrefix = "categoryTree", keySerialize = KeySerialize.EMPTY)
    public Response update(Long id, String name) {
        categoryDomainService.updateCategory(id, name);
        return Response.buildSuccess();
    }

    @Override
    public MultiResponse<CategoryVo> queryList(CategoryQueryCmd categoryPageQuery) {
        final List<Category> list;

        if (Objects.nonNull(categoryPageQuery.getId())) {
            QueryWrapper<Category> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(Category::getId, categoryPageQuery.getId());
            list = iCategoryService.list(queryWrapper);
        } else {
            Assert.notNull(categoryPageQuery.getLevel(), "品类等级不得为空");

            QueryWrapper<Category> queryWrapper = new QueryWrapper<>();

            queryWrapper.lambda().eq(Category::getLevel, categoryPageQuery.getLevel());
            if (Objects.nonNull(categoryPageQuery.getParentId())) {
                queryWrapper.lambda().eq(Category::getParentId, categoryPageQuery.getParentId());
            }
            queryWrapper.lambda()
                    .like(StringUtils.isNotBlank(categoryPageQuery.getName()),
                            Category::getName, categoryPageQuery.getName());
            queryWrapper.lambda().orderByDesc(Category::getCreatedAt);
            list = iCategoryService.list(queryWrapper);
        }


        final List<CategoryVo> categoryVos = CategoryTransMapper.INSTANCE.doToVos(list);
        for (CategoryVo categoryVo : categoryVos) {
            categoryVo.setIsLastLevel(categoryGateway.isLastLevel(categoryVo.getId()));
            categoryVo.setIsHaveAttr(categoryGateway.isHaveAttr(categoryVo.getPath()));
        }
        return MultiResponse.of(categoryVos);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(keyPrefix = "categoryTree", keySerialize = KeySerialize.EMPTY)
    public Response delete(Long id) {
        categoryDomainService.deleteCategory(id);
        return Response.buildSuccess();
    }

    @Override
    public MultiResponse<CategoryAttr> getAttr(Long categoryId) {
        return MultiResponse.of(categoryGateway.getAttrListByCategoryId(categoryId));
    }

    @Override
    public Response updateAttr(Long attrId, String name) {
        Assert.isTrue(Objects.nonNull(attrId), "属性id不得为空");
        Assert.isTrue(StringUtils.isNotBlank(name), "属性名称不得为空");
        final CategoryAttr categoryAttr = iCategoryAttrService.getById(attrId);
        categoryAttr.setName(name);
        iCategoryAttrService.updateById(categoryAttr);
        return Response.buildSuccess();
    }

    @Override
    public Response addAttr(Long id, String name) {
        Assert.isTrue(Objects.nonNull(id), "品类id不得为空");
        Assert.isTrue(StringUtils.isNotBlank(name), "属性名称不得为空");
        CategoryAttr categoryAttr = new CategoryAttr();
        categoryAttr.setCategoryId(id);
        categoryAttr.setName(name);
        iCategoryAttrService.save(categoryAttr);
        return Response.buildSuccess();
    }

    @Override
    public Response deleteAttr(Long id) {
        Assert.notNull(id, "属性id不得为空");

        if (categoryGateway.canDeleteAttr(id)) {
            categoryGateway.removeAttrById(id);
            return Response.buildSuccess();

        }
        throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "此属性存在关联商品，无法删除");
    }

    @Override
    @Cache(keyPrefix = "categoryTree", timeout = 3600)
    public SingleResponse<CategoryTree> categoryTree() {
        final List<Category> list = iCategoryService.lambdaQuery().list();
        final HashMap<Long, CategoryTreeNode> nodeMap = new HashMap<>();
        final List<CategoryTreeNode> treeTopNodes =
                TreeFactory.buildList(
                        list,
                        0L,
                        category -> {
                            final CategoryTreeNode categoryTreeNode =
                                    CategoryTransMapper.INSTANCE.poToTreeNode(category);
                            categoryTreeNode.setProps(
                                    categoryExtendConfig
                                            .getProps()
                                            .getOrDefault(category.getPath(), Collections.emptyList()));
                            nodeMap.put(category.getId(), categoryTreeNode);
                            return categoryTreeNode;
                        });
        final CategoryTree categoryTree = new CategoryTree();
        categoryTree.setTreeNodes(treeTopNodes);
        categoryTree.setNodeMap(nodeMap);
        return SingleResponse.of(categoryTree);
    }

    @Override
    @CacheEvict(keyPrefix = "categoryTree", keySerialize = KeySerialize.EMPTY)
    public Response cacheEvictForCategoryTree() {
        return Response.buildSuccess();
    }

    @Override
    @CacheEvict(keyPrefix = "categoryTree", keySerialize = KeySerialize.EMPTY)
    public Response resetPath() {
        final CategoryMapper categoryMapper = iCategoryService.getDaddyBaseMapper();
        categoryMapper.resetPath(1);
        categoryMapper.resetPath(2);
        categoryMapper.resetPath(3);
        categoryMapper.resetPath(4);
        categoryMapper.resetPath(5);
        return Response.buildSuccess();
    }

    @Override
    public SingleResponse<MultiSet<String>> migrateItem(Long fromCategoryId, Long toCategoryId) {
        final HashMultiSet<String> results = new HashMultiSet<>();
        for (Item item : itemService.lambdaQuery()
                .eq(Item::getCategoryId, fromCategoryId)
                .select(Entity::getId)
                .list()) {
            try {
                categoryGateway.modifyItemCategory(item.getId(), toCategoryId);
                results.add("成功");

            } catch (Exception e) {
                log.error("[迁移类目商品]商品 {} 迁移异常:{} from={} to={}",
                        item.getId(),
                        e.getMessage(),
                        fromCategoryId,
                        toCategoryId, e);
                if (e instanceof BizException) {
                    results.add(e.getMessage());
                } else {
                    results.add("未知异常");
                }
            }
        }
        return SingleResponse.of(results);
    }

    @Override
    public Response modifyItemCategory(Long itemId, Long newCategoryId) {
        categoryGateway.modifyItemCategory(itemId, newCategoryId);
        return Response.buildSuccess();
    }
}
