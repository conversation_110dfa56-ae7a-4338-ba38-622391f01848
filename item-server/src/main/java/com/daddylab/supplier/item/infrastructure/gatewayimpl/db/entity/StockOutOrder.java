package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockOutOrderState;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/3/24 16:38
 * @description 退料出库表
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StockOutOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    private Long deletedAt;

    @TableLogic
    private Integer isDel;

    /**
     * 出库单号
     */
    private String no;

    /**
     * 关联采购单号
     */
    private String purchaseOrderNo;

    /**
     * 关联采购单id
     */
    private Long purchaseOrderId;

    /**
     * 供应商id
     */
    private Long providerId;

    /**
     * 交货(出库)总数量
     */
    private Integer totalReturnQuantity;

    /**
     * 退料类型(1:库存退料)
     */
    private Integer returnType;

    /**
     * 退料方式(1.退料补料。2.退料并扣款)
     */
    private Integer returnMode;

    /**
     * 出库状态(1待提交、2待审核、3待出库、4已出库、5已取消、6审核拒绝、7撤回审核)
     *
     * @StockOutOrderState
     */
    private Integer state;

    /**
     * 采购组织id
     */
    private Long purchaseOrganizationId;

    /**
     * 退料组织id
     */
    private Long organizationId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否同步网点通.0不同步。1同步
     */
    private Integer isSyncWdt;

    /**
     * 同步网点通情况下，旺店通返回的入库单号
     */
    private String wdtStorageNo;

    /**
     * 审核流程id
     */
    private String approvalId;

    /**
     * 审核操作时间
     */
    private Long approvalAt;

    /**
     * 金蝶id
     */
    private String kingDeeId;

    /**
     * 同步状态 1:成功  2:旺店通失败  3:金蝶失败
     */
    private Integer syncState;

    /**
     * 同步消息
     */
    private String syncMsg;

    private Integer businessLine;

    /**
     * 冲销流程生成的逆向入库单单据编码
     * 当hedgeStatus = 9标识此单据冲销作废时，此字段存下对应的逆向单据编码
     */
    private String reverseInNo;

    /**
     * {@link StockOutOrderState}
     */
    private StockOutOrderState hedgeStatus;

    private String workbenchProcessId;

    private Long outboundTime;



}
