package com.daddylab.supplier.item.domain.departments;

import com.alibaba.cola.dto.PageResponse;
import com.daddylab.supplier.item.infrastructure.accessControl.Dept;
import com.daddylab.supplier.item.types.DropdownItem;
import com.daddylab.supplier.item.types.departments.DepartmentDropdownQuery;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/4/1
 */
public interface DepartmentGateway {
    /**
     * 部门下拉列表查询
     */
    PageResponse<DropdownItem<Long>> departmentDropdownList(DepartmentDropdownQuery query) throws RuntimeException;

    /**
     * 根据部门名称查询部门ID
     * @param name 部门名称
     * @return 部门ID
     */
    Long id(String name) throws RuntimeException;

    /**
     * 根据部门id查询部门名称
     * @param id
     * @return
     * @throws RuntimeException
     */
    String name(Long id) throws RuntimeException;


    Optional<Dept> getDepartment(Long id);

    Optional<Dept> getTopDepartment(Long id);

    /**
     * 查询所有下级部门ID
     * @param id 当前部门ID
     * @param recursive 是否递归查询，若否则只查直属下级
     */
    Collection<Long> subIds(Long id, boolean recursive) throws RuntimeException;


    /**
     * 查询所有下级部门
     * @param id 当前部门ID
     * @param recursive 是否递归查询，若否则只查直属下级
     */
    List<Dept> getSubDepartments(Long id, boolean recursive);

    /**
     * 根据部门名称查询其部门ID以及其所有下级部门ID（包括非直属）
     * @param name 部门名称
     * @return 部门IDS
     */
    Collection<Long> idAndDescendantIds(String name) throws RuntimeException;
}
