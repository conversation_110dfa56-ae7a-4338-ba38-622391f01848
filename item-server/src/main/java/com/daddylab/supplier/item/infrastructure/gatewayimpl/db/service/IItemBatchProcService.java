package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemBatchProc;
import com.daddylab.supplier.item.types.itemBatchProc.ItemBatchProcType;

import org.slf4j.event.Level;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;

/**
 * 商品批处理 服务类
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
public interface IItemBatchProcService extends IDaddyService<ItemBatchProc> {

    @Transactional
    default long createTask(Object cmd, ItemBatchProcType itemBatchProcType) {
        return createTask(cmd, itemBatchProcType, null);
    }

    @Transactional
    long createTask(
            Object cmd,
            ItemBatchProcType itemBatchProcType,
            @Nullable Runnable synchronizedCallback);

    boolean lock(long id, long lockUtil);

    boolean setProcessingStatus(long id, long lockUtil);

    boolean setProcess(long id, int progress);

    boolean setCompleteStatus(long id, boolean success, String msg);

    boolean log(long id, Level level, String msg, Object... vars);

    boolean renewLock(long id, long lockUtil);

    boolean retry(long id);
}
