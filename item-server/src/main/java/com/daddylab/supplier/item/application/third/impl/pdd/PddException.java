package com.daddylab.supplier.item.application.third.impl.pdd;

import com.pdd.pop.sdk.http.PopBaseHttpResponse;

/**
 * <AUTHOR>
 * @since 2024/4/18
 */
public class PddException extends RuntimeException {
    private final PopBaseHttpResponse.ErrorResponse errorResponse;

    public PddException(String message, PopBaseHttpResponse.ErrorResponse response) {
        super(message);
        this.errorResponse = response;
    }

    public PopBaseHttpResponse.ErrorResponse getErrorResponse() {
        return errorResponse;
    }
}
