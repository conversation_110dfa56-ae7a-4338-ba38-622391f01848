/*
package com.daddylab.supplier.item.application.virtualWarehouse.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

*/
/**
 * <AUTHOR> up
 * @date 2024年03月11日 2:14 PM
 *//*

@Data
@ApiModel("虚拟仓-库存明细查询")
public class VwStockConfigDetailCmd {

    @ApiModelProperty("入口。1.虚拟仓分页列表。2.虚拟仓新建/编辑/详情页面")
    @NotNull
    private Integer entryType;

    @ApiModelProperty("虚拟仓库 ID。必填")
    private Long virtualWarehouseId;

    @ApiModelProperty("实仓编码。入口2必填")
    private String warehouseNo;

    @ApiModelProperty("实仓库存占比，可以为空")
    private Integer inventoryRatio;

    @ApiModelProperty("业务线")
    private Integer businessLine;

    */
/**
     * 虚拟仓状态行为监控
     * 1.正常->停用
     * 2.停用->正常
     *//*

    private Integer type;


}
*/
