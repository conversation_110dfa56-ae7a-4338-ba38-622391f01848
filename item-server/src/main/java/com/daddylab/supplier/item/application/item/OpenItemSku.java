package com.daddylab.supplier.item.application.item;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.GoodsType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.SkuSplitType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.LinkedHashMap;

@Data
@ApiModel("商品SKU信息")
public class OpenItemSku {
    
    /**
     * sku id
     */
    @ApiModelProperty("sku id")
    private Long id;
    
    /**
     * 商品ID
     */
    @ApiModelProperty("商品ID")
    private Long itemId;
    
    /**
     * 供货指定编码
     */
    @ApiModelProperty("供货指定编码")
    private String providerSpecifiedCode;
    
    /**
     * 成本价格，如果为0，说明未手动设置，默认使用商品维度的采购成本价格
     */
    @ApiModelProperty("成本价格，如果为0，说明未手动设置，默认使用商品维度的采购成本价格")
    private BigDecimal costPrice;

    /**
     * 销售价格，如果为0，说明未手动设置，使用商品维度的日常销售价格
     */
    @ApiModelProperty("销售价格，如果为0，说明未手动设置，使用商品维度的日常销售价格")
    private BigDecimal salePrice;
    
    /**
     * 系统sku编码
     */
    @ApiModelProperty("系统sku编码")
    private String skuCode;
    
    /**
     * 编码中的6位数字
     */
    @ApiModelProperty("编码中的6位数字")
    private String skuNum;
    
    /**
     * 删除时间
     */
    @ApiModelProperty("删除时间")
    private Long deletedAt;
    
    @ApiModelProperty("金蝶ID")
    private String kingDeeId;
    
    @ApiModelProperty("条形码")
    private String barCode;
    
    @ApiModelProperty("单位")
    private String unit;
    
    @ApiModelProperty("供应商ID")
    private Long providerId;
    
    @ApiModelProperty("分类ID")
    private Long categoryId;
    
    @ApiModelProperty("规格")
    private String specifications;
    
    @ApiModelProperty("仓库编号")
    private String warehouseNo;
    
    /**
     * 商品税率
     */
    @ApiModelProperty("商品税率")
    private BigDecimal taxRate;
    
    /**
     * 采购税率
     */
    @ApiModelProperty("采购税率")
    private BigDecimal purchaseTaxRate;
    
    @ApiModelProperty("拆分类型")
    private SkuSplitType splitType;
    
    /**
     * 商品SKU额外拓展属性
     */
    @ApiModelProperty("商品SKU额外拓展属性")
    private LinkedHashMap<String, Object> props;
    
    /**
     * 平台佣金
     */
    @ApiModelProperty("平台佣金")
    private BigDecimal platformCommission;
    
    /**
     * 货品类型
     */
    @ApiModelProperty("货品类型")
    private GoodsType goodsType;
    
    /**
     * 合同销售价
     */
    @ApiModelProperty("合同销售价")
    private BigDecimal contractSalePrice;
}
