package com.daddylab.supplier.item.application.refundOrder;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.StopWatch;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.daddylab.supplier.item.application.afterSales.AfterSalesAuthBizService;
import com.daddylab.supplier.item.application.afterSales.AfterSalesSendOnInfoVO;
import com.daddylab.supplier.item.application.afterSales.AfterSalesWarehouseBizService;
import com.daddylab.supplier.item.application.afterSales.WarehouseAfterSalesAddressVO;
import com.daddylab.supplier.item.common.ErrorChecker;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.exportTask.ExportTaskGateway;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesConfirmUndertakeType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesReceiveState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesSendOnInfoService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderBaseInfoExportDoForP;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderBaseInfoForP;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderDetail;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderDetailForP;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderItem;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderQuery;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderQueryForP;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderTypeMapper;
import com.daddylab.supplier.item.types.refundOrder.WdtRefundOrderTypeEnum;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2022/10/25
 */
@Service
@Slf4j
public class RefundOrderServiceForPImpl implements RefundOrderServiceForP {

    @Resource
    RefundOrderService refundOrderService;

    @Resource
    AfterSalesWarehouseBizService afterSalesWarehouseBizService;

    @Resource
    AfterSalesAuthBizService afterSalesAuthBizService;
    @Resource
    private ExportTaskGateway exportTaskGateway;

    @Resource
    private FileGateway fileGateway;

    @Resource
    private IAfterSalesSendOnInfoService afterSalesSendOnInfoService;

    @Resource
    private RefreshConfig refreshConfig;

    private LocalDateTime getRefundOrderQueryTimeStartLimitForPSys() {
        return DateUtil.parse(
                Optional.ofNullable(refreshConfig.getRefundOrderQueryTimeStartLimitForPSys())
                        .filter(StringUtil::isNotBlank)
                        .orElse("2022-11-07 00:00:00"));
    }

    @Override
    public PageResponse<RefundOrderBaseInfoForP> refundOrderQuery(RefundOrderQueryForP query,
            Long partnerProviderId) {
        final RefundOrderQuery refundOrderQuery = RefundOrderTypeMapper.INST.convRefundOrderQueryForP(
                query);
        final MultiResponse<WarehouseAfterSalesAddressVO> partnerProviderReturnWarehouseAddressVOs = afterSalesWarehouseBizService.getPartnerProviderReturnWarehouseAddressVOs(
                partnerProviderId);
        ErrorChecker.checkAndThrowIfError(partnerProviderReturnWarehouseAddressVOs);
        final List<WarehouseAfterSalesAddressVO> warehouseAddressVOsData = partnerProviderReturnWarehouseAddressVOs.getData();
        final List<String> warehouseNos = warehouseAddressVOsData
                .stream()
                .map(WarehouseAfterSalesAddressVO::getWarehouseNo).collect(
                        Collectors.toList());
        final boolean isPreciseQuery =
                StringUtil.isNotBlank(query.getRefundOrderNo()) || StringUtil.isNotBlank(
                        query.getReturnLogisticsNo());
        //只要类型为退货和换货的退换单
        refundOrderQuery.setTypes(Arrays.asList(2, 3));
        //时间范围筛选，只要售后管理上线以后产生的数据
        refundOrderQuery.setOrderTimeStart(
                DateUtil.toTime(getRefundOrderQueryTimeStartLimitForPSys()));
        //如果是精确搜索，则不指定仓库编号范围，使之可以查询到非当前供应商的数据
        if (!isPreciseQuery) {
            if (warehouseNos.isEmpty()) {
                return ResponseFactory.emptyPage();
            }
            refundOrderQuery.setReturnWarehouseNos(warehouseNos);
            if (NumberUtil.isPositive(query.getReceiveState())) {
                refundOrderQuery.setReceiveStates(
                        Collections.singletonList(query.getReceiveState()));
            }
        }
        return ResponseFactory.ofPage(refundOrderService.refundOrderQuery(refundOrderQuery),
                v -> {
                    if (!isPreciseQuery) {
                        return true;
                    }
                    final boolean isSelfOrder = warehouseNos.contains(v.getReturnWarehouseNo());
                    final boolean isMatchQueryState = NumberUtil.isZeroOrNull(query.getReceiveState())
                            || Objects.equals(query.getReceiveState(), v.getReceiveState().getValue());
                    if (isSelfOrder) {
                        return isMatchQueryState;
                    }
                    final boolean isAccessibleState = Objects.equals(v.getReceiveState(),
                            AfterSalesReceiveState.WAIT);
                    return isMatchQueryState && isAccessibleState;
                },
                v -> {
                    final RefundOrderBaseInfoForP refundOrderBaseInfoForP = RefundOrderTypeMapper.INST.convRefundOrderBaseInfoForP(
                            v);
                    final boolean isSelfOrder = warehouseNos.contains(v.getReturnWarehouseNo());
                    refundOrderBaseInfoForP.setSelfOrder(isSelfOrder);
                    final List<AfterSalesSendOnInfoVO> afterSalesSendOnInfoVOS = afterSalesSendOnInfoService.getAfterSalesSendOnInfoVOS(
                            v.getRefundOrderNo());
                    final boolean hasSendInfoInfo =
                            !afterSalesSendOnInfoVOS.isEmpty() && afterSalesSendOnInfoVOS.stream()
                                    .map(AfterSalesSendOnInfoVO::getLogisticsNo)
                                    .anyMatch(StringUtil::isNotBlank);
                    refundOrderBaseInfoForP.setCanSendOn(!isSelfOrder && !hasSendInfoInfo);
                    return refundOrderBaseInfoForP;
                });
    }

    @Override
    public SingleResponse<RefundOrderDetailForP> refundOrderDetail(String refundOrderNo,
            Long partnerProviderId) {
        final SingleResponse<Boolean> allowAccessRefundOrder = afterSalesAuthBizService.isAllowAccessRefundOrder(
                partnerProviderId, refundOrderNo);
        ErrorChecker.checkAndThrowIfError(allowAccessRefundOrder, v -> v,
                ErrorCode.BUSINESS_OPERATE_ERROR, "您无权限访问该退换单");
        final SingleResponse<RefundOrderDetail> response = refundOrderService.refundOrderDetail(
                refundOrderNo);
        if (!response.isSuccess()) {
            //noinspection unchecked
            return SingleResponse.buildFailure(response.getErrCode(), response.getErrMessage());
        }
        return SingleResponse.of(
                RefundOrderTypeMapper.INST.convRefundOrderDetailForP(response.getData()));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public SingleResponse<Long> refundOrderExport(RefundOrderQueryForP query,
            Long partnerProviderId) {
        ExportTask exportTask = new ExportTask();
        exportTask.setStatus(ExportTaskStatus.RUNNING);
        exportTask.setName("供应商退换单导出-" + DateUtil.formatNow(DatePattern.PURE_DATETIME_PATTERN));
        exportTask.setType(ExportTaskType.REFUND_ORDER);
        exportTask.setPId(partnerProviderId);
        final Long taskId = exportTaskGateway.saveOrUpdateExportTask(exportTask);
        exportTask.setId(taskId);
        ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
            final StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            log.info("供应商退换单导出开始执行 任务ID:{} pSysProviderId:{}", exportTask.getId(),
                    partnerProviderId);
            try (final ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
                query.setPageIndex(1);
                query.setPageSize(999999);
                final PageResponse<RefundOrderBaseInfoForP> pageResponse = refundOrderQuery(
                        query, partnerProviderId);
                EasyExcel.write(byteArrayOutputStream)
                        .useDefaultStyle(false)
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .sheet("工作表1")
                        .head(RefundOrderBaseInfoExportDoForP.class)
                        .doWrite(mapRefundOrderBaseInfoListToExportDoList(
                                pageResponse.getData()));
                UploadFileAction action = UploadFileAction.ofInputStream(
                        new ByteArrayInputStream(byteArrayOutputStream.toByteArray()),
                        exportTask.getName() + ".xlsx");
                final String downloadUrl = fileGateway.uploadFile(action).getUrl();
                exportTask.setStatus(ExportTaskStatus.SUCCESS);
                exportTask.setDownloadUrl(downloadUrl);
            } catch (Exception e) {
                log.error("供应商退换单导出异常 任务ID:{} pSysProviderId:{}", exportTask.getId(),
                        partnerProviderId, e);
                exportTask.setError(ExceptionUtil.getSimpleStackString(e));
                exportTask.setStatus(ExportTaskStatus.FAIL);
            } finally {
                stopWatch.stop();
                exportTaskGateway.saveOrUpdateExportTask(exportTask);
                log.info("供应商退换单导出执行结束 任务ID:{} pSysProviderId:{} 状态:{} 总耗时:{}ms",
                        exportTask.getId(), partnerProviderId,
                        exportTask.getStatus(), stopWatch.getTotalTimeMillis());
            }
        });
        return SingleResponse.of(taskId);
    }

    private List<RefundOrderBaseInfoExportDoForP> mapRefundOrderBaseInfoListToExportDoList(
            List<RefundOrderBaseInfoForP> refundOrderBaseInfoForPList) {
        if (refundOrderBaseInfoForPList == null) {
            return null;
        }

        List<RefundOrderBaseInfoExportDoForP> list = new ArrayList<RefundOrderBaseInfoExportDoForP>(
                refundOrderBaseInfoForPList.size());
        for (RefundOrderBaseInfoForP refundOrderBaseInfoForP : refundOrderBaseInfoForPList) {

            for (RefundOrderItem itemDetail : refundOrderBaseInfoForP.getItemDetails()) {
                RefundOrderBaseInfoExportDoForP refundOrderBaseInfoExportDoForP = new RefundOrderBaseInfoExportDoForP();

                refundOrderBaseInfoExportDoForP.setRefundOrderNo(
                        refundOrderBaseInfoForP.getRefundOrderNo());
                refundOrderBaseInfoExportDoForP.setSrcOrderNos(
                        refundOrderBaseInfoForP.getSrcOrderNos());
                refundOrderBaseInfoExportDoForP.setReturnWarehouseName(
                        refundOrderBaseInfoForP.getReturnWarehouseName());
                if (refundOrderBaseInfoForP.getType() != null) {
                    refundOrderBaseInfoExportDoForP.setType(IEnum.getEnumOptByValue(
                                    WdtRefundOrderTypeEnum.class, refundOrderBaseInfoForP.getType())
                            .map(WdtRefundOrderTypeEnum::getDesc).orElse(""));
                }
                if (refundOrderBaseInfoForP.getReceiveState() != null) {
                    refundOrderBaseInfoExportDoForP.setReceiveState(
                            Optional.ofNullable(refundOrderBaseInfoForP.getReceiveState())
                                    .map(AfterSalesReceiveState::getDesc).orElse(""));
                }
                refundOrderBaseInfoExportDoForP.setReturnLogisticsName(
                        refundOrderBaseInfoForP.getReturnLogisticsName());
                refundOrderBaseInfoExportDoForP.setReturnLogisticsNo(
                        refundOrderBaseInfoForP.getReturnLogisticsNo());
                if (refundOrderBaseInfoForP.getCreateTime() != null) {
                    refundOrderBaseInfoExportDoForP.setCreateTime(
                            DateUtil.format(refundOrderBaseInfoForP.getCreateTime()));
                }
                refundOrderBaseInfoExportDoForP.setRefundQuantity(itemDetail.getRefundQuantity());
                refundOrderBaseInfoExportDoForP.setItemName(itemDetail.getItemName());
                refundOrderBaseInfoExportDoForP.setSpecName(itemDetail.getSpecName());
                final AfterSalesConfirmUndertakeType undertakeType = itemDetail.getUndertakeType();
                refundOrderBaseInfoExportDoForP.setUndertakeType(
                        Optional.ofNullable(undertakeType).map(
                                AfterSalesConfirmUndertakeType::getDesc).orElse(""));
                refundOrderBaseInfoExportDoForP.setUndertakeAmount(
                        AfterSalesConfirmUndertakeType.PERCENTAGE == undertakeType ?
                                Optional.ofNullable(itemDetail.getUndertakeAmount())
                                        .map(v -> v.multiply(BigDecimal.valueOf(100))
                                                .setScale(2, RoundingMode.DOWN)
                                                .toPlainString() + "%").orElse("")
                                : Optional.ofNullable(itemDetail.getUndertakeAmount())
                                        .map(BigDecimal::toPlainString).orElse(""));
                list.add(refundOrderBaseInfoExportDoForP);
            }

        }

        return list;
    }




}
