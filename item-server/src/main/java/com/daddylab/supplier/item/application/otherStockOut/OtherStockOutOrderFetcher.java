package com.daddylab.supplier.item.application.otherStockOut;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockother.StockotherOutQueryAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockother.dto.StockotherOutQueryQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockother.dto.StockotherOutQueryQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.StockoutOtherQueryAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutOtherQueryQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutOtherQueryQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutOtherQueryQueryWithDetailResponse.Order;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutOtherQueryQueryWithDetailResponse.Order.Detail;
import com.daddylab.supplier.item.domain.brand.gateway.BrandGateway;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockOut;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockOutDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OtherStockOutDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OtherStockOutMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ReasonMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.assembler.CommonAssembler;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.types.reason.enums.ReasonType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 旺店通其他出库同步任务
 *
 * <AUTHOR>
 * @since 2021/04/22
 */
@Component
@Slf4j
public class OtherStockOutOrderFetcher implements PageFetcher {

    final private WdtGateway wdtGateway;
    final private OtherStockOutMapper otherStockOutMapper;
    final private OtherStockOutDetailMapper otherStockOutDetailMapper;
    final private ItemSkuGateway itemSkuGateway;
    final private BrandGateway brandGateway;
    final private ReasonMapper reasonMapper;

    public OtherStockOutOrderFetcher(
            WdtGateway wdtGateway,
            OtherStockOutMapper OtherStockOutMapper,
            OtherStockOutDetailMapper OtherStockOutDetailMapper,
            ItemSkuGateway itemSkuGateway,
            BrandGateway brandGateway,
            ReasonMapper reasonMapper) {
        this.wdtGateway = wdtGateway;
        this.otherStockOutMapper = OtherStockOutMapper;
        this.otherStockOutDetailMapper = OtherStockOutDetailMapper;
        this.itemSkuGateway = itemSkuGateway;
        this.brandGateway = brandGateway;
        this.reasonMapper = reasonMapper;
    }

    @Override
    public int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
        return query(startTime, endTime, 1, 1, true).getTotalCount();
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, long pageIndex, long pageSize,
            RunContext runContext) {
        final StockoutOtherQueryQueryWithDetailResponse response = query(startTime, endTime,
                (int) pageIndex,
                (int) pageSize, false);
        handleResponse(response);
    }

    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.WDT_OTHER_STOCK_OUT_ORDER;
    }

    private StockoutOtherQueryQueryWithDetailResponse query(
            LocalDateTime startTime, LocalDateTime endTime, int pageNo, int pageSize,
            boolean calcTotal) {
        try {
            final StockoutOtherQueryAPI api = wdtGateway
                    .getAPI(StockoutOtherQueryAPI.class, 0, false);
            final Pager pager = new Pager(pageSize, pageNo - 1, calcTotal);

            final StockoutOtherQueryQueryWithDetailParams params = new StockoutOtherQueryQueryWithDetailParams();
            params.setStartTime(DateUtil.format(startTime));
            params.setEndTime(DateUtil.format(endTime));

            return api.queryWithDetail(params, pager);
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapFetchException(e, fetchDataType());
        }
    }

    private StockotherOutQueryQueryWithDetailResponse.Order queryStockOtherBizOrder(
            String bizOrderNo) {
        try {
            final StockotherOutQueryAPI api = wdtGateway.getAPI(StockotherOutQueryAPI.class);
            final Pager pager = new Pager(1, 0, false);
            final StockotherOutQueryQueryWithDetailParams params = new StockotherOutQueryQueryWithDetailParams();
            params.setOtherOutNo(bizOrderNo);

            final StockotherOutQueryQueryWithDetailResponse response = api
                    .queryWithDetail(params, pager);

            final List<StockotherOutQueryQueryWithDetailResponse.Order> order = response.getOrder();
            if (order.isEmpty()) {
                return null;
            }
            return order.get(0);
        } catch (WdtErpException e) {
            log.error("查询其他入库业务单失败", e);
            return null;
        }
    }

    private void handleResponse(StockoutOtherQueryQueryWithDetailResponse response) {
        for (Order order : response.getOrder()) {
            final String orderNo = order.getOrderNo();
            final LambdaQueryWrapper<OtherStockOut> query = Wrappers
                    .lambdaQuery();
            query.eq(OtherStockOut::getOrderNo, orderNo);
            OtherStockOut otherStockOut = otherStockOutMapper.selectOne(query);
            if (otherStockOut == null) {
                otherStockOut = new OtherStockOut();
            }
            otherStockOut.setOrderNo(orderNo);
            otherStockOut.setWarehouseNo(order.getWarehouseNo());
            otherStockOut.setStatus(
                    OtherStockOutState.MAP.getOrDefault(order.getStatus().intValue(),
                            OtherStockOutState.UNMAPPING)
                            .getValue());

            final String reason = order.getReason();

            //改为存表
            Long reasonId = reasonMapper.id(ReasonType.OTHER_STOCK_OUT, order.getReason());
            if (reasonId == null) {
                reasonId = reasonMapper.addReason(ReasonType.OTHER_STOCK_OUT, order.getReason());
            }
            otherStockOut.setOtherReason(reasonId.intValue());

            otherStockOut.setReasonText(reason);
            otherStockOut.setMarkName("");
            otherStockOut.setLogisticsNo(order.getLogisticsNo());
            otherStockOut.setFreight(order.getPostFee());
            final LocalDateTime consignTime = CommonAssembler.INST
                    .parseTime(order.getConsignTime());
            otherStockOut.setDeliveryAt(LocalDateTimeUtil.toEpochMilli(consignTime));
            otherStockOut.setRemark(order.getRemark());
            otherStockOut.setOperatorName(order.getOperatorName());
            if (otherStockOut.getId() == null) {
                otherStockOutMapper.insert(otherStockOut);
            } else {
                otherStockOutMapper.updateById(otherStockOut);
            }

            final String srcOrderNo = order.getSrcOrderNo();
            final StockotherOutQueryQueryWithDetailResponse.Order bizOrder = queryStockOtherBizOrder(
                    srcOrderNo);

            if (CollUtil.isNotEmpty(order.getDetailList())) {
                deleteRemovedDetails(otherStockOut, order);
                for (Detail detail : order.getDetailList()) {
                    final LambdaQueryWrapper<OtherStockOutDetail> queryDetail = Wrappers
                            .lambdaQuery();
                    queryDetail.eq(OtherStockOutDetail::getRecId, detail.getRecId());
                    OtherStockOutDetail otherStockOutDetail = otherStockOutDetailMapper
                            .selectOne(queryDetail);
                    if (otherStockOutDetail == null) {
                        otherStockOutDetail = new OtherStockOutDetail();
                        otherStockOutDetail.setRecId(detail.getRecId());
                    }
                    otherStockOutDetail.setOrderId(otherStockOut.getId());
                    final String specNo = detail.getSpecNo();
                    final ItemSku itemSku = itemSkuGateway.getBySkuCode(specNo);
                    otherStockOutDetail
                            .setItemId(itemSku != null ? itemSku.getItemId() : 0);
                    otherStockOutDetail.setSkuId(itemSku != null ? itemSku.getId() : 0);
                    otherStockOutDetail.setSkuCode(specNo);
                    otherStockOutDetail.setBusinessLine(itemSkuGateway.getSkuBusinessLine(specNo));

                    Integer predictCount = null;
                    if (bizOrder != null && CollUtil.isNotEmpty(bizOrder.getDetailList())) {
                        for (StockotherOutQueryQueryWithDetailResponse.Order.Detail bizOrderDetail : bizOrder
                                .getDetailList()) {
                            if (Objects.equals(bizOrderDetail.getSpecNo(), specNo)) {
                                predictCount = bizOrderDetail.getNum().intValue();
                                break;
                            }
                        }
                    }

                    otherStockOutDetail.setPredictCount(predictCount);
                    otherStockOutDetail.setCount(detail.getGoodsCount() != null ? detail
                            .getGoodsCount().intValue() : 0);
                    otherStockOutDetail.setIsDefect(detail.getDefect() ? 1 : 0);
                    otherStockOutDetail.setUnit(detail.getUnitName());
                    final String brandNo = detail.getBrandNo();
                    otherStockOutDetail.setBrandId(brandGateway.id(brandNo));
                    otherStockOutDetail.setBrandSn(brandNo);
                    otherStockOutDetail
                            .setBarCode(itemSku != null ? itemSku.getBarCode() : null);
                    otherStockOutDetail.setPredictWeight(BigDecimal.ZERO);
                    otherStockOutDetail.setRemark(detail.getRemark());
                    if (otherStockOutDetail.getId() == null) {
                        otherStockOutDetailMapper.insert(otherStockOutDetail);
                    } else {
                        otherStockOutDetailMapper.updateById(otherStockOutDetail);
                    }
                }
            }

        }
    }

    private void deleteRemovedDetails(OtherStockOut otherStockOut, Order order) {
        final List<Integer> recIds = order.getDetailList().stream().map(Detail::getRecId)
                .collect(Collectors.toList());
        final LambdaQueryWrapper<OtherStockOutDetail> deleteRemovedDetailQuery = Wrappers
                .lambdaQuery();
        deleteRemovedDetailQuery.eq(OtherStockOutDetail::getOrderId, otherStockOut.getId())
                .notIn(OtherStockOutDetail::getRecId, recIds);
        otherStockOutDetailMapper.deleteWithTime(deleteRemovedDetailQuery);
    }
}
