package com.daddylab.supplier.item.application.shipinghao.service;

import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductDetailDto;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductListDto;

import java.util.List;

/**
 * 微信商品同步服务接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface WechatProductSyncService {
    
    /**
     * 全量同步商品
     *
     * @param type 类型 daddylab/member
     * @return 是否成功
     */
//    boolean syncAllProducts(String type);
    
//    /**
//     * 增量同步商品
//     *
//     * @param type 类型 daddylab/member
//     * @param lastUpdateTime 上次更新时间
//     * @return 是否成功
//     */
//    boolean syncIncrementalProducts(String type, Long lastUpdateTime);
    
    /**
     * 获取商品列表
     *
     * @param type 类型 daddylab/member
     * @param offset 偏移量
     * @param limit 每页数量
     * @return 商品列表信息
     */
    WechatProductListDto getProductList(String type, Integer offset, Integer limit);
    
    /**
     * 获取商品详情
     *
     * @param type 类型 daddylab/member
     * @param productId 商品ID
     * @return 商品详情信息
     */
    WechatProductDetailDto getProductDetail(String type, String productId);
    
    /**
     * 批量获取商品详情
     *
     * @param type 类型 daddylab/member
     * @param productIds 商品ID列表
     * @return 商品详情列表
     */
    List<WechatProductDetailDto> getProductDetails(String type, List<String> productIds);
} 