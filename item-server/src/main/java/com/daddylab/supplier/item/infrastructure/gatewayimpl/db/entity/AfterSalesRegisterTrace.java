package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.google.common.collect.Lists;

import lombok.*;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 客服售后登记表数据来源跟踪
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AfterSalesRegisterTrace implements Serializable {

    private static final long serialVersionUID = 1L;

    public static AfterSalesRegisterTrace EMPTY =
            new AfterSalesRegisterTrace(Collections.emptyList());

    @Getter private List<TableTrace> sources;

    public AfterSalesRegisterTrace(TableTrace source) {
        this.sources = Lists.newArrayList(source);
    }

    /**
     * <AUTHOR>
     * @since 2023/8/28
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TableTrace {
        Integer projectId;
        String projectName;
        String id;
        String data;
    }

    public boolean contain(Integer projectId, String id) {
        return sources != null
                && sources.stream()
                        .anyMatch(
                                source ->
                                        Objects.equals(source.getProjectId(), projectId)
                                                && Objects.equals(source.getId(), id));
    }
}
