package com.daddylab.supplier.item.application.order;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.domain.order.OrderSensitiveInfo;
import com.daddylab.supplier.item.types.order.OrderBaseInfo;
import com.daddylab.supplier.item.types.order.OrderDetail;
import com.daddylab.supplier.item.types.order.OrderQuery;
import com.daddylab.supplier.item.types.order.PurchaseOrderTabVO;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/4/17
 */
public interface OrderService {

    Map<String, OrderSensitiveInfo> queryOrderSensitiveInfoBatchBySrcOrderNo(
            List<String> srcOrderNos);

    /**
     * 订单查询
     */
    PageResponse<OrderBaseInfo> orderQuery(OrderQuery orderQuery);

    /**
     * 订单详情
     */
    SingleResponse<OrderDetail> orderDetail(String wdtOrderNo);

    /**
     * 查询 采购单订单明细tab
     *
     * @param purchaseOrderId
     * @return
     */
    PageResponse<PurchaseOrderTabVO> purchaseOrderTab(Long purchaseOrderId);

    Boolean related(Long purchaseOrderId);

}
