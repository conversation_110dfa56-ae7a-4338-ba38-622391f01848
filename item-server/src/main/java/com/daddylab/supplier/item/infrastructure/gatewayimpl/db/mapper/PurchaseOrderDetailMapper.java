package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderPageQuery;
import com.daddylab.supplier.item.domain.exportTask.dto.purchase.PurchaseOrderDetailSheet;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrderDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 采购订单明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
public interface PurchaseOrderDetailMapper extends DaddyBaseMapper<PurchaseOrderDetail> {

    /**
     * 采购订单商品明细 导出计数
     * @param query
     * @return
     */
    Integer detailExportCount(@Param("param")PurchaseOrderPageQuery query);

    /**
     * 采购订单商品明细 导出数据
     * @param query
     * @return
     */
    List<PurchaseOrderDetailSheet> detailExport(@Param("param")PurchaseOrderPageQuery query,@Param("offset") Integer offset, @Param("size") Integer size);


}
