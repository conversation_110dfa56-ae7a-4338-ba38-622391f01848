package com.daddylab.supplier.item.application.itemReference.types;

import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemDelivery;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Data
@ApiModel("商品参照表SKU")
public class ItemReferenceSku {

    @ApiModelProperty("商品ID")
    private Long itemId;

    @ApiModelProperty("商品SKU")
    private String skuCode;

    @ApiModelProperty("商品名称")
    private String itemName;

    @ApiModelProperty("商品编码")
    private String itemCode;

    @ApiModelProperty("关联款号")
    private String partnerProviderItemSn;

    @ApiModelProperty("品类ID")
    private Long categoryId;

    @ApiModelProperty("品类")
    private String categoryPath;

    @ApiModelProperty("品牌ID")
    private Long brandId;

    @ApiModelProperty("品牌")
    private String brandName;

    @ApiModelProperty("供应商ID")
    private Long providerId;

    @ApiModelProperty("P系统供应商ID")
    private Long partnerProviderId;

    @ApiModelProperty("供应商")
    private String provider;

    @ApiModelProperty("采购员用户ID")
    private Long buyerUid;

    @ApiModelProperty("采购员花名")
    private String buyerNick;

    @ApiModelProperty("采购员真名")
    private String buyerName;

    @ApiModelProperty("发货渠道 0.仓库发货。1.工厂发货。2.仓库工厂均发货")
    private String delivery;

    @JsonProperty
    public String getDelivery() {
        if (Objects.nonNull(delivery) && delivery.contains(",")) {
            return ItemDelivery.ALL.getValue().toString();
        }
        return delivery;
    }

    @ApiModelProperty("税率")
    private String taxRate;

    @ApiModelProperty("日常销售价")
    private String salePrice;

    @ApiModelProperty("产品活动价")
    private String activePrice;

    @ApiModelProperty("采购成本")
    private String costPrice;

    @ApiModelProperty("划线价格")
    private String linePrice;

    @ApiModelProperty("商品状态 0:待上架 1:已上架 2:下架")
    private Long itemStatus;

    @ApiModelProperty("上架日期（时间戳）")
    private Long launchTime;

    @ApiModelProperty("上架日期")
    @JsonProperty
    public String getLaunchDate() {
        if (NumberUtil.isPositive(launchTime)) {
            return DateUtil.formatDate(launchTime);
        }
        return "";
    }

    @ApiModelProperty("仓库库存")
    private Long stock;

    @ApiModelProperty("合作模式（业务线）")
    private Integer businessLine;

    @ApiModelProperty("是否黑名单 0否 1是")
    private Integer isBlacklist;

    @ApiModelProperty("平台佣金")
    private BigDecimal platformCommission;

    @ApiModelProperty("合同销售价")
    private BigDecimal contractSalePrice;

    @ApiModelProperty("合作方业务类型")
    private List<CorpBizTypeDTO> corpBizType;
}