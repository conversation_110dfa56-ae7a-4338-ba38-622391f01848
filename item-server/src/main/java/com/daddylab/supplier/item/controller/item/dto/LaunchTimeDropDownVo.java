package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Date: 2022/6/8 13:41
 * @Description: 上新时间下拉框
 */
@Data
@ApiModel("上新时间下拉框")
public class LaunchTimeDropDownVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "上新时间（时间戳，单位秒）")
    private Long launchTime;

    @ApiModelProperty(value = "上新时间（格式化好的，YYYY-dd-mm）")
    private String launchDate;

    @ApiModelProperty(value = "上新计划名称")
    private String planName;

    @ApiModelProperty(value = "上新计划ID")
    private Long planId;

    @ApiModelProperty("合作模式")
    private Integer businessLine;
}
