package com.daddylab.supplier.item.application.purchasePayable.vo;

import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("采购应付凭证商品明细返回结果")
public class PurchasePayableVoucherItemDetailsVO implements Serializable {
    private static final long serialVersionUID = -7388637783744442458L;

    @ApiModelProperty(value = "sku")
    private String itemSkuCode;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "规格")
    private String specifications;

    @ApiModelProperty(value = "申请数量")
    private Integer appleQuantity;

    @ApiModelProperty(value = "应付数量")
    private Integer payableQuantity;

    /**
     *  应付类型 {@link PurchaseTypeEnum}
     */
    private Integer type;
}
