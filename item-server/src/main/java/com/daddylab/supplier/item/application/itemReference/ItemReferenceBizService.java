package com.daddylab.supplier.item.application.itemReference;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceQuery;
import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceSku;
import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceSpu;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @since 2022/9/1
 */
public interface ItemReferenceBizService {

    /**
     * 商品参照表查询（SPU维度）
     */
    PageResponse<ItemReferenceSpu> spuPageQuery(ItemReferenceQuery query);

    /**
     * 商品参照表查询（SKU维度）
     */
    PageResponse<ItemReferenceSku> skuPageQuery(ItemReferenceQuery query);

    /**
     * 商品参照表导出（SPU维度）
     *
     * @return 导出任务ID
     */
    SingleResponse<Long> spuExport(ItemReferenceQuery query);

    /**
     * 商品参照表导出（SKU维度）
     *
     * @return 导出任务ID
     */
    SingleResponse<Long> skuExport(ItemReferenceQuery query);

    /**
     * 导入SPU和SKU的关联关系
     *
     * @param inputStream excel 文件输入流
     * @param sheetIndex  导入第几张工作表
     * @return 结果
     */
    Response importRef(InputStream inputStream, Integer sheetIndex);

    void syncItemSku(String itemCode, Long userId);
}
