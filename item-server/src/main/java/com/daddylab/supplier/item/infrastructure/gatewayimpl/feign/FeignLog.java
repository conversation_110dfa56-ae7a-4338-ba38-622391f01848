package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/24 5:05 下午
 * @description
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface FeignLog {

    /**
     * com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.LogEnum
     */
    int KINGDEE = 1;
    int PARTNER_SERVER = 2;
    int USER_SERVER = 3;
    int ACCESS_CONTROL = 4;
    /**
     * P系统，审查词库，增加词条命中次数
     */
    int PARTNER_SERVER_REVIEW_THESAURUS_ADD_HIT_NUM = 6;
    /**
     * OA
     */
    int OA = 7;

    /**
     * 。1:金蝶 2:合作伙伴系统.3:用户系统
     *
     * @return
     */
    int type() default 0;

}
