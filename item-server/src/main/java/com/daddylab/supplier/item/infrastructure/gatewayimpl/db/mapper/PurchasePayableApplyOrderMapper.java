package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableApplyOrder;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;

/**
 * <p>
 * 采购付款申请单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
public interface PurchasePayableApplyOrderMapper extends DaddyBaseMapper<PurchasePayableApplyOrder> {

}
