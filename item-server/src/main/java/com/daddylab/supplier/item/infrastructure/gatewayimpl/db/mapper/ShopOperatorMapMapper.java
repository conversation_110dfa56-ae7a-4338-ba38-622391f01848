package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.shop.domain.ShopOperatorMapVO;
import com.daddylab.supplier.item.domain.shop.dto.OffShelfShopOperatorPageQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopOperatorMap;

import java.util.List;

/**
 * <p>
 * 下架管理-店铺/运营映射关联 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-01
 */
public interface ShopOperatorMapMapper extends DaddyBaseMapper<ShopOperatorMap> {

    List<ShopOperatorMapVO> shopOperatorMapPageQuery(OffShelfShopOperatorPageQuery queryPage);
}
