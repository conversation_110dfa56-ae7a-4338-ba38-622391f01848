package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PayApplyAuditProcessNode;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;

/**
 * <p>
 * 申请付款单审核流节点审核详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
public interface PayApplyAuditProcessNodeMapper extends DaddyBaseMapper<PayApplyAuditProcessNode> {

}
