package com.daddylab.supplier.item.application.shipinghao.converter;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson2.JSON;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductDetailDto;
import com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct.DescInfo;
import com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct.WeChatProduct;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatProductDetail;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * 微信商品详情转换工具类
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
public class WechatProductDetailConverter {

  /**
   * DTO转数据库实体
   *
   * @param dto 微信商品详情DTO
   * @param type 类型 daddylab/member
   * @return 数据库实体
   */
  public static WechatProductDetail dtoToEntity(WechatProductDetailDto dto, String type) {
    if (dto == null || dto.getProduct() == null) {
      return null;
    }

    WechatProductDetail entity = new WechatProductDetail();
    entity.setType(type);

    // 从Product对象中获取数据
    WeChatProduct product = dto.getProduct();
    entity.setProductId(product.getProductId());
    entity.setTitle(product.getTitle());
    entity.setSubTitle(product.getSubTitle());

    // 处理主图（取第一张作为主图）
    if (product.getHeadImgs() != null && !product.getHeadImgs().isEmpty()) {
      entity.setHeadImg(product.getHeadImgs().get(0));
    }

    // 处理类目ID（取第一个类目的ID）
    if (product.getCats() != null && !product.getCats().isEmpty()) {
      entity.setCategoryId(product.getCats().get(0).getCatId());
    }

    entity.setBrandId(product.getBrandId());
    entity.setStatus(product.getStatus());
    entity.setProductType(product.getProductType());

    // 处理商品详情信息
    if (product.getDescInfo() != null) {
      entity.setDescText(product.getDescInfo().getDesc());
      if (product.getDescInfo().getImgs() != null) {
        entity.setDescImgs(JSON.toJSONString(product.getDescInfo().getImgs()));
      }
    }

    // 处理商品属性
    if (product.getAttrs() != null) {
      entity.setAttrs(JSON.toJSONString(product.getAttrs()));
    }

    // 处理商品规格
    if (product.getSkus() != null) {
      entity.setSkus(JSON.toJSONString(product.getSkus()));
    }

    // 处理尺码表
    if (product.getSizeChart() != null) {
      entity.setSizeChart(JSON.toJSONString(product.getSizeChart()));
    }

    // 设置同步时间
    entity.setSyncTime(System.currentTimeMillis() / 1000);
    entity.setSyncStatus(1); // 成功
    entity.setSyncError(null);

    return entity;
  }

  /**
   * 数据库实体转DTO
   *
   * @param entity 数据库实体
   * @return 微信商品详情DTO
   */
  public static WechatProductDetailDto entityToDto(WechatProductDetail entity) {
    if (entity == null) {
      return null;
    }

    WechatProductDetailDto dto = new WechatProductDetailDto();
    dto.setErrcode(0);
    dto.setErrmsg("ok");

    // 创建Product对象
    WeChatProduct product =
        new WeChatProduct();
    product.setProductId(entity.getProductId());
    product.setTitle(entity.getTitle());
    product.setSubTitle(entity.getSubTitle());
    product.setStatus(entity.getStatus());
    product.setProductType(entity.getProductType());
    product.setBrandId(entity.getBrandId());

    // 处理主图
    if (entity.getHeadImg() != null) {
      product.setHeadImgs(ListUtil.of(entity.getHeadImg()));
    }

    // 处理商品详情信息
    if (entity.getDescText() != null || entity.getDescImgs() != null) {
      DescInfo descInfo =
          new com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct.DescInfo();
      descInfo.setDesc(entity.getDescText());
      if (entity.getDescImgs() != null) {
        try {
          List<String> imgs = JSON.parseArray(entity.getDescImgs(), String.class);
          descInfo.setImgs(imgs);
        } catch (Exception e) {
          log.error("解析商品详情图片失败", e);
        }
      }
      product.setDescInfo(descInfo);
    }

    // 处理商品属性
    if (entity.getAttrs() != null) {
      try {
        List<com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct.Attribute> attrs =
            JSON.parseArray(
                entity.getAttrs(),
                com.daddylab
                    .supplier
                    .item
                    .application
                    .shipinghao
                    .dto
                    .wechatProduct
                    .Attribute
                    .class);
        product.setAttrs(attrs);
      } catch (Exception e) {
        log.error("解析商品属性失败", e);
      }
    }

    // 处理商品规格
    if (entity.getSkus() != null) {
      try {
        List<com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct.Sku> skus =
            JSON.parseArray(
                entity.getSkus(),
                com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct.Sku.class);
        product.setSkus(skus);
      } catch (Exception e) {
        log.error("解析商品规格失败", e);
      }
    }

    // 处理尺码表
    if (entity.getSizeChart() != null) {
      try {
        com.daddylab.supplier.item.application.shipinghao.dto.wechatProduct.SizeChart sizeChart =
            JSON.parseObject(
                entity.getSizeChart(),
                com.daddylab
                    .supplier
                    .item
                    .application
                    .shipinghao
                    .dto
                    .wechatProduct
                    .SizeChart
                    .class);
        product.setSizeChart(sizeChart);
      } catch (Exception e) {
        log.error("解析尺码表失败", e);
      }
    }

    dto.setProduct(product);
    return dto;
  }

  /**
   * 批量转换DTO列表
   *
   * @param dtos DTO列表
   * @param type 类型 daddylab/member
   * @return 数据库实体列表
   */
  public static List<WechatProductDetail> dtoListToEntityList(
      List<WechatProductDetailDto> dtos, String type) {
    if (dtos == null || dtos.isEmpty()) {
      return null;
    }

    return dtos.stream()
        .map(dto -> dtoToEntity(dto, type))
        .filter(entity -> entity != null)
        .collect(Collectors.toList());
  }

  /**
   * 批量转换数据库实体列表
   *
   * @param entities 数据库实体列表
   * @return DTO列表
   */
  public static List<WechatProductDetailDto> entityListToDtoList(
      List<WechatProductDetail> entities) {
    if (entities == null || entities.isEmpty()) {
      return null;
    }

    return entities.stream()
        .map(WechatProductDetailConverter::entityToDto)
        .filter(dto -> dto != null)
        .collect(Collectors.toList());
  }
}
