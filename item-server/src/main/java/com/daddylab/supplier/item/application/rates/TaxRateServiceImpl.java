package com.daddylab.supplier.item.application.rates;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.PageResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.TaxRate;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ITaxRateService;
import com.daddylab.supplier.item.infrastructure.utils.DecimalUtil;
import com.daddylab.supplier.item.types.DropdownItem;
import com.daddylab.supplier.item.types.DropdownQuery;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/4/1
 */
@Service("taxRateServiceImplTwo")
@RequiredArgsConstructor
public class TaxRateServiceImpl implements TaxRateService {
    final private ITaxRateService taxRateService;

    @Override
    public PageResponse<DropdownItem<String>> rateDropdownQuery(DropdownQuery query) {
        final IPage<TaxRate> page = query.getPage();
        taxRateService.page(
                page,
                taxRateService.lambdaQuery()
                        .like(StrUtil.isNotBlank(query.getName()), TaxRate::getName, query.getName())
                        .select(TaxRate::getName, TaxRate::getValue)
        );
        return ResponseFactory.ofPage(page, this::toDropdownItem);
    }

    @NonNull
    private DropdownItem<String> toDropdownItem(TaxRate it) {
        return new DropdownItem<>(DecimalUtil.format(it.getValue()), it.getName());
    }
}
