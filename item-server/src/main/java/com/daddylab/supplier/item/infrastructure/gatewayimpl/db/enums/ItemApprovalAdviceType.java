package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.Getter;

/**
 * 建议类型 1:标题建议 2:文案建议 3:主图建议
 */
@Getter
public enum ItemApprovalAdviceType implements IEnum {

    TITLE_ADVICE(1,"标题建议"),

    COPY_ADVICE(2,"文案建议"),

    MAIN_IMAGE_ADVICE(3,"主图建议");

    @EnumValue
    private final Integer value;
    private final String desc;

    ItemApprovalAdviceType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
