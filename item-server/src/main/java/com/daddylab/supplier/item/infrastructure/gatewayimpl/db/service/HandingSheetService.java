package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.controller.handingsheet.dto.*;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheet;

/**
 * <p>
 * 盘货表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
public interface HandingSheetService extends IDaddyService<HandingSheet> {

    /**
     * 盘货表分页查询
     *
     * @param query 分页查询条件
     * @return PageResponse<HandingSheetPageVo>
     */
    PageResponse<HandingSheetPageVo> pageQuery(HandingSheetPageQuery query);

    /**
     * 新增盘货表
     *
     * @param param 参数
     * @return 盘货表主键 ID
     */
    @Deprecated
    Long add(HandingSheetParam param);

    /**
     * 编辑盘货表
     *
     * @param param 参数
     * @return 盘货表 ID
     */
    Long saveOrEdit(HandingSheetParam param);

    Long doAudit(HandingSheetParam param);

    /**
     * 盘货表主键 ID
     *
     * @param id 盘货表主键 ID
     * @return SingleResponse<HandingSheetBaseVo>
     */
    SingleResponse<HandingSheetBaseVo> getBaseInfo(Long id);

    /**
     * 盘货表关联的商品信息分页查询
     *
     * @param query 分页查询参数
     * @return PageResponse<HandingSheetItemVo>
     */
    PageResponse<HandingSheetItemVo> sheetItemInfoPage(HandingSheetItemPageQuery query);

    /**
     * 删除
     *
     * @param param 删除参数
     */
    void delete(HandingSheetDelParam param);

    /**
     * 撤销审核
     *
     * @param param 撤销审核参数
     */
    void cancelAudit(HandingSheetCancelAuditParam param);

    /**
     * 提交审核
     *
     * @param param 参数
     */
    void submitAudit(HandingSheetSubmitAuditParam param);

    /**
     * 盘货表审核
     *
     * @param param 参数
     */
//    void audit(HandingSheetAuditParam param);

    /**
     * 新增店铺活动富文本内容
     *
     * @param param 参数
     */
    @Deprecated
    void addActivityText(HandingSheetActivityTextParam param);

    /**
     * 新增或编辑店铺活动富文本内容
     *
     * @param param 参数
     */
    void saveOrUpdateActivityText(HandingSheetActivityTextParam param);

    /**
     * 删除店铺活动富文本内容
     *
     * @param param 删除参数
     */
    void deleteActivityText(HandingSheetActivityTextDelParam param);

    /**
     * 店铺活动富文本内容分页展示
     *
     * @param pageQuery 分页查询参数
     * @return page
     */
    PageResponse<HandingSheetActivityTextPageVo> pageQueryActivityText(HandingSheetActivityTextPageQuery pageQuery);

    /**
     * 导出
     *
     * @param pageQuery 参数封装
     * @deprecated
     * @see com.daddylab.supplier.item.application.handingsheet.HandingSheetV2BizServiceImpl#exportItemInfoExcel(com.daddylab.supplier.item.controller.handingsheet.dto.HandingSheetItemPageQuery)
     */
    void exportExcel(HandingSheetPageQuery pageQuery);

    /**
     * 导出盘货表下的商品数据
     *
     * @param query 查询参数
     */
    void exportItemInfoExcel(HandingSheetItemPageQuery query);

    /**
     * 单独编辑盘货物表商品
     *
     * @param param 参数
     */
    void updateHandingSheetItem(HandingSheetItemUpdateParam param);

    /**
     * 通过 handing_sheet_item#ID 查询盘货表
     *
     * @param handingSheetItemId handing_sheet_item#ID
     * @return HandingSheet
     */
    HandingSheet getByHandingSheetItemId(Long handingSheetItemId);

    /**
     * 通过 handing_sheet_activity_text#ID 查询盘货表
     *
     * @param handingSheetActivityTextId handing_sheet_activity_text#ID
     * @return HandingSheet
     */
    HandingSheet getByHandingSheetActivityTextId(Long handingSheetActivityTextId);

    /**
     * 刷新价格
     *
     * @param param 参数
     * @return SingleResponse<ArrivalPriceVo>
     */
    SingleResponse<ArrivalPriceVo> refreshPrice(HandingSheetRefreshPriceParam param);

    /**
     * 各个状态的盘货表数量
     *
     * @param query query
     * @return SingleResponse<HandingSheetStatusCountVo>
     */
    SingleResponse<HandingSheetStatusCountVo> statusCount(HandingSheetPageQuery query);

}
