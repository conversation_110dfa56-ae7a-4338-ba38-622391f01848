package com.daddylab.supplier.item.controller.item.dto;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/12 2:41 下午
 * @description
 */
@Data
@ApiModel("商品分页查询返回封装")
public class ItemPageVo implements Serializable {

  private static final long serialVersionUID = 229857538598694003L;

  @ApiModelProperty("商品id")
  private Long id;

  @ApiModelProperty("商品编码（存在供货指定编码时返回供货指定编码）")
  private String code;

  @ApiModelProperty("商品状态 0:待上架 1:已上架 2:下架 3:已废弃")
  private Long status;

  @ApiModelProperty("商品品牌id")
  private Long brandId;

  @ApiModelProperty("商品品类id")
  private Long categoryId;

  @ApiModelProperty("商品供应商id")
  private Long providerId;

  @ApiModelProperty("商品第三方关联编号")
  private String partnerCode;

  @ApiModelProperty("商品采购人 user_id")
  protected Long itemBuyerUserId;

  @ApiModelProperty("商品采购人 user_name")
  protected String itemBuyerUserName;

  @ApiModelProperty("商品采购人 花名")
  protected String itemBuyerNickName;

  @ApiModelProperty("QC")
  protected List<QcUserInfo> qcUserInfoList;

  //    -------

  @ApiModelProperty("商品名称")
  private String name;

  @ApiModelProperty("品类path")
  private String categoryPath;

  @ApiModelProperty("品牌")
  private String brandName;

  @ApiModelProperty("sku数")
  private Integer skuCount;

  @ApiModelProperty("仓库库存")
  private Integer stock;

  //    @ApiModelProperty("价格集合")
  //    private List<PriceDo> priceDoList;

  @ApiModelProperty("采购成本(单位元)")
  private String purchasePrice = "0";

  @ApiModelProperty("日常售价(单位元)")
  private String salePrice = "0";

  @ApiModelProperty("预期上新时间（时间戳）")
  private Long shelfTime;

  @ApiModelProperty("商品图片")
  private String imageUrl;

  @ApiModelProperty("0供应商 1老爸抽检 2绿色家装 3商家入驻")
  private Integer businessLine;

  @ApiModelProperty("0供应商 1老爸抽检 2绿色家装 3商家入驻")
  private List<Integer> businessLines;

  public void setBusinessLines(String businessLines) {
    this.businessLines =
        StrUtil.split(businessLines, ",").stream()
            .filter(StrUtil::isNotBlank)
            .map(Integer::valueOf)
            .collect(Collectors.toList());
  }

  @ApiModelProperty("是否老爸抽检0否 1是")
  private Integer isDadCheck;

  @ApiModelProperty(value = "商品标签", notes = "DAD_SAMPLING_INSPECTION 老爸抽检, CUSTOMIZED_PRODUCTS 定制品")
  List<String> tags;

  @ApiModelProperty("下架时间")
  private Long downFrameTime;

  @ApiModelProperty("下架理由")
  private String downFrameReason;

  //    @Data
  //    @ApiModel("商品价格")
  //    class PriceDo {
  //        @ApiModelProperty("商品价格id")
  //        private Long priceId;
  //        @ApiModelProperty("价格类型 1:采购成本 2:日常销售价 3:划线价格 4:产品活动价 5:渠道最低价 6:自定义价格")
  //        private Integer type;
  //        @ApiModelProperty("价格")
  //        private BigDecimal price;
  //    }

  @Data
  public static class QcUserInfo {
    @ApiModelProperty("QC user_id")
    private Long qcUserId;

    @ApiModelProperty("QC user_name")
    private String qcUserName;

    @ApiModelProperty("QC user_nick_name")
    private String qcUserNickName;
  }

  @ApiModelProperty("供应商名称")
  private String providerName;

  @ApiModelProperty("小程序商品上架状态 0 未上架/商家下架 1 已上架 2 定时上架  3 平台强制下架")
  private Integer mallShelfStatus;

  @ApiModelProperty("合作方业务类型")
  private List<CorpBizTypeDTO> corpBizType;
}
