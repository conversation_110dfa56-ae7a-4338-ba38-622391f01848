package com.daddylab.supplier.item.application.afterSalesForwarding;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSalesForwarding.types.*;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @since 2024/5/24
 */
public interface AfterSalesForwardingRegisterBizService {
    MultiResponse<AfterSalesForwardingRegisterFormItemVO> completion(AfterSalesForwardingRegisterCompletionCmd cmd);

    SingleResponse<Boolean> save(AfterSalesForwardingRegisterSaveCmd cmd);

    PageResponse<AfterSalesForwardingRegisterPageVO> query(AfterSalesForwardingRegisterQuery query);

    Response importExcel(InputStream inputStream);

    Response exportExcel(AfterSalesForwardingRegisterQuery query);


    Response noNeedToForward(AfterSalesForwardingRegisterNoNeedToForwardCmd cmd);

    Response toForward(AfterSalesForwardingRegisterToForwardCmd cmd);

    Response reportAnomalies(AfterSalesForwardingRegisterReportAnomaliesCmd cmd);

    PageResponse<AfterSalesForwardingRegisterPageVO> shareLinkQuery(AfterSalesForwardingRegisterShareQuery query);

    SingleResponse<String> shareLinkQueryExport(AfterSalesForwardingRegisterShareQuery query);

    Response delete(Long id);

    Response autoHandle();

    Response fixData(FixDataRequest request);
}
