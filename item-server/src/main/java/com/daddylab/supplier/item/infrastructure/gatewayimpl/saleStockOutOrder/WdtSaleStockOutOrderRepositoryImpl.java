package com.daddylab.supplier.item.infrastructure.gatewayimpl.saleStockOutOrder;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.daddylab.supplier.item.application.saleStockOutOrder.WdtSaleStockOutOrderRepository;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrderDetails;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrderLogistics;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrderPositionDetails;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtSaleStockOutOrderDetailsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtSaleStockOutOrderLogisticsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtSaleStockOutOrderPositionDetailsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtSaleStockOutOrderService;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2022/8/19
 */
@Repository
public class WdtSaleStockOutOrderRepositoryImpl implements WdtSaleStockOutOrderRepository {

    private final IWdtSaleStockOutOrderService wdtSaleStockOutOrderService;
    private final IWdtSaleStockOutOrderDetailsService wdtSaleStockOutOrderDetailsService;
    private final IWdtSaleStockOutOrderPositionDetailsService wdtSaleStockOutOrderPositionDetailsService;

    public WdtSaleStockOutOrderRepositoryImpl(
            IWdtSaleStockOutOrderService wdtSaleStockOutOrderService,
            IWdtSaleStockOutOrderDetailsService wdtSaleStockOutOrderDetailsService,
            IWdtSaleStockOutOrderPositionDetailsService wdtSaleStockOutOrderPositionDetailsService,
            IWdtSaleStockOutOrderLogisticsService wdtSaleStockOutOrderLogisticsService) {
        this.wdtSaleStockOutOrderService = wdtSaleStockOutOrderService;
        this.wdtSaleStockOutOrderDetailsService = wdtSaleStockOutOrderDetailsService;
        this.wdtSaleStockOutOrderPositionDetailsService = wdtSaleStockOutOrderPositionDetailsService;
        this.wdtSaleStockOutOrderLogisticsService = wdtSaleStockOutOrderLogisticsService;
    }

    private final IWdtSaleStockOutOrderLogisticsService wdtSaleStockOutOrderLogisticsService;

    @Transactional
    public void saveOrUpdateWdtSaleStockOutOrderWithDetails(
            WdtSaleStockOutOrder wdtSaleStockOutOrder) {
        saveOrUpdateWdtSaleStockOutOrder(wdtSaleStockOutOrder);
        saveOrUpdateWdtSaleStockOutOrderDetails(wdtSaleStockOutOrder);
        saveOrUpdateWdtSaleStockOutOrderLogistics(wdtSaleStockOutOrder);
        saveOrUpdateWdtSaleStockOutOrderPositionDetails(wdtSaleStockOutOrder);
    }

    private void saveOrUpdateWdtSaleStockOutOrderPositionDetails(
            WdtSaleStockOutOrder wdtSaleStockOutOrder) {
        final List<WdtSaleStockOutOrderPositionDetails> wdtSaleStockOutOrderPositionDetails =
                wdtSaleStockOutOrder.getDetailsList().stream()
                        .flatMap(v -> v.getPositionDetailsList()
                                .stream()).collect(Collectors.toList());
        final LambdaQueryWrapper<WdtSaleStockOutOrderPositionDetails> removePositionDetailsCond = Wrappers.lambdaQuery();
        removePositionDetailsCond.eq(WdtSaleStockOutOrderPositionDetails::getStockoutId,
                wdtSaleStockOutOrder.getStockoutId());
        wdtSaleStockOutOrderPositionDetailsService.remove(removePositionDetailsCond);
        wdtSaleStockOutOrderPositionDetailsService.saveBatch(wdtSaleStockOutOrderPositionDetails);
    }

    private void saveOrUpdateWdtSaleStockOutOrderLogistics(
            WdtSaleStockOutOrder wdtSaleStockOutOrder) {
        final Long stockoutId = wdtSaleStockOutOrder.getStockoutId();
        final List<WdtSaleStockOutOrderLogistics> wdtSaleStockOutOrderLogistics = wdtSaleStockOutOrder.getLogisticsList();

        final List<Integer> orderLogisticsRecIdsRemote = wdtSaleStockOutOrderLogistics.stream()
                .map(WdtSaleStockOutOrderLogistics::getRecId).collect(
                        Collectors.toList());

        final List<WdtSaleStockOutOrderLogistics> wdtSaleStockOutOrderLogisticsLocal = wdtSaleStockOutOrderLogisticsService.lambdaQuery()
                .eq(WdtSaleStockOutOrderLogistics::getStockoutId, stockoutId)
                .select(WdtSaleStockOutOrderLogistics::getId,
                        WdtSaleStockOutOrderLogistics::getRecId).list();

        final Map<Integer, Long> orderLogisticsRecId2IdMap = wdtSaleStockOutOrderLogisticsLocal.stream()
                .collect(
                        Collectors.toMap(WdtSaleStockOutOrderLogistics::getRecId,
                                WdtSaleStockOutOrderLogistics::getId));

        final List<Integer> orderLogisticsRecIdsLocal = wdtSaleStockOutOrderLogisticsLocal.stream()
                .map(WdtSaleStockOutOrderLogistics::getRecId).collect(
                        Collectors.toList());

        final Collection<Integer> needAddedLogisticsRecIds = CollectionUtil.subtract(
                orderLogisticsRecIdsRemote,
                orderLogisticsRecIdsLocal
        );

        final Collection<Integer> needRemovedLogisticsRecIds = CollectionUtil.subtract(
                orderLogisticsRecIdsLocal,
                orderLogisticsRecIdsRemote);

        final Collection<Integer> needUpdatedLogisticsRecIds = CollectionUtil.intersection(
                orderLogisticsRecIdsLocal, orderLogisticsRecIdsRemote);

        if (CollectionUtil.isNotEmpty(needAddedLogisticsRecIds)) {
            final List<WdtSaleStockOutOrderLogistics> needAddedDetails = wdtSaleStockOutOrderLogistics
                    .stream().filter(v -> needAddedLogisticsRecIds.contains(v.getRecId()))
                    .collect(Collectors.toList());
            wdtSaleStockOutOrderLogisticsService.saveBatch(needAddedDetails);
        }

        if (CollectionUtil.isNotEmpty(needRemovedLogisticsRecIds)) {
            final List<Long> needRemovedLogisticsIds = needRemovedLogisticsRecIds.stream()
                    .map(orderLogisticsRecId2IdMap::get).filter(Objects::nonNull).collect(
                            Collectors.toList());
            wdtSaleStockOutOrderLogisticsService.removeByIds(needRemovedLogisticsIds);
        }

        if (CollectionUtil.isNotEmpty(needUpdatedLogisticsRecIds)) {
            final List<WdtSaleStockOutOrderLogistics> needUpdatedLogistics = wdtSaleStockOutOrderLogistics
                    .stream().filter(v -> needUpdatedLogisticsRecIds.contains(v.getRecId()))
                    .peek(v -> v.setId(orderLogisticsRecId2IdMap.get(v.getRecId())))
                    .collect(Collectors.toList());
            wdtSaleStockOutOrderLogisticsService.updateBatchById(needUpdatedLogistics);
        }
    }

    @SafeVarargs
    private final List<WdtSaleStockOutOrderDetails> getWdtSaleStockOutOrderDetailsLocal(
            Long stockoutId,
            SFunction<WdtSaleStockOutOrderDetails, ?>... columns) {
        return wdtSaleStockOutOrderDetailsService.lambdaQuery()
                .eq(WdtSaleStockOutOrderDetails::getStockoutId,
                        stockoutId)
                .select(columns).list();
    }

    private void saveOrUpdateWdtSaleStockOutOrder(WdtSaleStockOutOrder wdtSaleStockOutOrder) {
        final Long id = getSaleStockoutOrderLocalId(wdtSaleStockOutOrder)
                .orElse(null);
        wdtSaleStockOutOrder.setId(id);
        if (Objects.isNull(wdtSaleStockOutOrder.getId())) {
            wdtSaleStockOutOrderService.save(wdtSaleStockOutOrder);
        } else {
            wdtSaleStockOutOrderService.updateById(wdtSaleStockOutOrder);
        }
    }

    private void saveOrUpdateWdtSaleStockOutOrderDetails(
            WdtSaleStockOutOrder wdtSaleStockOutOrder) {
        final Long stockoutId = wdtSaleStockOutOrder.getStockoutId();
        final List<WdtSaleStockOutOrderDetails> wdtSaleStockOutOrderDetailsLocal = getWdtSaleStockOutOrderDetailsLocal(
                stockoutId,
                WdtSaleStockOutOrderDetails::getId, WdtSaleStockOutOrderDetails::getRecId);

        final Map<Integer, Long> orderDetailsRecId2IdMap = wdtSaleStockOutOrderDetailsLocal.stream()
                .collect(Collectors.toMap(WdtSaleStockOutOrderDetails::getRecId,
                        WdtSaleStockOutOrderDetails::getId));
        final List<Integer> orderDetailsRecIdsLocal = wdtSaleStockOutOrderDetailsLocal.stream()
                .map(WdtSaleStockOutOrderDetails::getRecId).collect(
                        Collectors.toList());
        final List<WdtSaleStockOutOrderDetails> wdtSaleStockOutOrderDetailsList = wdtSaleStockOutOrder.getDetailsList();
        final List<Integer> orderDetailsRecIdsRemote = wdtSaleStockOutOrderDetailsList
                .stream()
                .map(WdtSaleStockOutOrderDetails::getRecId).collect(
                        Collectors.toList());
        final Collection<Integer> needAddedDetailsRecIds = CollectionUtil.subtract(
                orderDetailsRecIdsRemote,
                orderDetailsRecIdsLocal
        );
        final Collection<Integer> needRemovedDetailsRecIds = CollectionUtil.subtract(
                orderDetailsRecIdsLocal,
                orderDetailsRecIdsRemote);
        final Collection<Integer> needUpdatedDetailsRecIds = CollectionUtil.intersection(
                orderDetailsRecIdsLocal, orderDetailsRecIdsRemote);
        if (CollectionUtil.isNotEmpty(needAddedDetailsRecIds)) {
            final List<WdtSaleStockOutOrderDetails> needAddedDetails = wdtSaleStockOutOrderDetailsList
                    .stream().filter(v -> needAddedDetailsRecIds.contains(v.getRecId()))
                    .collect(Collectors.toList());
            wdtSaleStockOutOrderDetailsService.saveBatch(needAddedDetails);
        }
        if (CollectionUtil.isNotEmpty(needRemovedDetailsRecIds)) {
            final List<Long> needRemovedDetailsIds = needRemovedDetailsRecIds.stream()
                    .map(orderDetailsRecId2IdMap::get).filter(Objects::nonNull).collect(
                            Collectors.toList());
            wdtSaleStockOutOrderDetailsService.removeByIds(needRemovedDetailsIds);
        }
        if (CollectionUtil.isNotEmpty(needUpdatedDetailsRecIds)) {
            final List<WdtSaleStockOutOrderDetails> needUpdatedDetails = wdtSaleStockOutOrderDetailsList
                    .stream().filter(v -> needUpdatedDetailsRecIds.contains(v.getRecId()))
                    .peek(v -> v.setId(orderDetailsRecId2IdMap.get(v.getRecId())))
                    .collect(Collectors.toList());
            wdtSaleStockOutOrderDetailsService.updateBatchById(needUpdatedDetails);
        }
    }

    @NonNull
    private Optional<Long> getSaleStockoutOrderLocalId(WdtSaleStockOutOrder wdtSaleStockOutOrder) {
        return wdtSaleStockOutOrderService.lambdaQuery()
                .eq(WdtSaleStockOutOrder::getStockoutId, wdtSaleStockOutOrder.getStockoutId())
                .select(WdtSaleStockOutOrder::getId).oneOpt().map(WdtSaleStockOutOrder::getId);
    }

}
