package com.daddylab.supplier.item.application.afterSales;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022年10月24日 1:54 PM
 */
@ApiModel("异常件关联操作请求参数")
@Data
public class RelateCmd extends Command {

    private static final long serialVersionUID = -4351639196537034169L;
    @ApiModelProperty("退换单号")
    private String returnOrderNo;

    @ApiModelProperty("异常件id列表")
    private List<Long> abnormalInfoIds;
}
