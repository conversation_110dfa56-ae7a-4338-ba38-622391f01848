package com.daddylab.supplier.item.application.item.itemRunning;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.daddylab.supplier.item.application.item.itemRunning.cmd.ItemRunningCmd;
import com.daddylab.supplier.item.application.item.itemRunning.vo.ItemRunningVo;
import com.daddylab.supplier.item.common.trans.ItemRunningTransMapper;
import com.daddylab.supplier.item.controller.item.dto.EditRunningCmd;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemRunning;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemRunningService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName ItemRunningBizServiceImpl.java
 * @description
 * @createTime 2022年04月29日 16:52:00
 */
@Service
@Slf4j
public class ItemRunningBizServiceImpl implements ItemRunningBizService{

    @Autowired
    private IItemRunningService iItemRunningService;

    @Autowired
    private ItemGateway itemGateway;


    @Override
    public void insert(EditRunningCmd cmd) {
        Item item = itemGateway.getItem(cmd.getItemId());
        Assert.notNull(item, "获取商品运营信息失败，无效的商品ID:{}" + cmd.getItemId());

        List<ItemRunningCmd> itemRunnings = cmd.getItemRunnings();
        LambdaQueryWrapper<ItemRunning> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ItemRunning::getItemId, item.getId());
        List<ItemRunning> runningList = iItemRunningService.list(queryWrapper);
        if (CollUtil.isNotEmpty(runningList)) {
            List<Long> ids = runningList.stream().map(ItemRunning::getId).collect(Collectors.toList());
            iItemRunningService.removeByIds(ids);
        }

        List<ItemRunning> list = ItemRunningTransMapper.INSTANCE.cmdToDos(itemRunnings);
        iItemRunningService.saveBatch(list);
    }

    @Override
    public List<ItemRunningVo> getList(Long itemId) {
        LambdaQueryWrapper<ItemRunning> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ItemRunning::getItemId, itemId);
        List<ItemRunning> runningList = iItemRunningService.list(queryWrapper);
        return ItemRunningTransMapper.INSTANCE.doToVos(runningList);
    }
}
