package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
* <p>
    * 入驻活动价格确认
    * </p>
*
* <AUTHOR>
* @since 2024-10-06
*/
    @Data
        @EqualsAndHashCode(callSuper = false)
    public class EntryActivityPriceConfirm implements Serializable {

    private static final long serialVersionUID = 1L;

            /**
            * id
            */
            @TableId(value = "id", type = IdType.AUTO)
    private Long id;

            /**
            * 创建时间
            */
            @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

            /**
            * 创建人
            */
            @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

            /**
            * 更新时间
            */
            @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

            /**
            * 更新人
            */
            @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;

            /**
            * 是否已删除
            */
        @TableLogic(value = "0", delval = "id")
    private Long isDel;

            /**
            * 删除时间
            */
            @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

            /**
            * 供应商ID
            */
    private Long providerId;

            /**
            * 短链接
            */
    private String shortLink;


}
