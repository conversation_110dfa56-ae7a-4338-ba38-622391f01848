package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.application.item.combinationItem.dto.query.CombinationItemPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CombinationDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CombinationNameDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CombinationProviderDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ComposeSkuDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CombinationItem;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
public interface ICombinationItemService extends IDaddyService<CombinationItem> {

    List<ComposeSkuDO> listSkuDetail(ComposeSkuPageQuery query);

    Integer countSkuDetail(ComposeSkuPageQuery query);

    List<CombinationDO> listItem(CombinationItemPageQuery query);

    Integer countListItem(CombinationItemPageQuery query);

    CombinationItem getByItemCode(String itemCode);

    List<CombinationProviderDO> queryProviderList(List<String> combinationCodeList);

    List<CombinationNameDO> queryNameList(List<String> codeList);

    Boolean verifyCode(String itemCode);

    /**
     * 是否组合装编码，若是，则返回对应组合装ID
     * @param code 组合装编码
     * @return 组合装ID
     */
    Long isCombinationItem(String code);

    Map<String, Long> isCombinationItem(List<String> codes);

    List<CombinationItem> listByCombinationCode(Collection<String> combinationCodes);

    List<CombinationItem> listBySkuCode(String skuCode);
    
    List<CombinationItem> listBySkuCode(List<String> skuCodes);
    
    Map<String, List<CombinationItem>> mapListBySkuCode(List<String> skuCodes);
    


}
