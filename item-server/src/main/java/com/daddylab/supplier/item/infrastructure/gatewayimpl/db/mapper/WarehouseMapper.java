package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.WarehouseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 仓库列表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-24
 */
public interface WarehouseMapper extends DaddyBaseMapper<Warehouse> {

    List<WarehouseDO> queryWarehouseMapByNo(@Param("list") Collection<String> noList);

    List<Warehouse> queryBySkuCodeAndIsGift(@Param("skuCode") String skuCode, @Param("isGift") Integer isGift, @Param("taxPrice") BigDecimal taxPrice);

    void updateWarehouseRatio(@Param("warehouseNo") String no, @Param("version") Integer version, @Param("newRatio") Integer newRatio);
}
