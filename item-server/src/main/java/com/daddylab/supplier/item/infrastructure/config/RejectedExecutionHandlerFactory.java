package com.daddylab.supplier.item.infrastructure.config;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/9 10:28 上午
 * @description
 */
@Slf4j
public class RejectedExecutionHandlerFactory {

    private static final AtomicLong COUNTER = new AtomicLong();

    /**
     * 拒绝任务抛出异常
     *
     * @return A handler for tasks that cannot be executed by ThreadPool
     */
    public static RejectedExecutionHandler throwExecution() {
        return new ThreadPoolExecutor.AbortPolicy();
    }

    /**
     * 直接丢弃该任务,不抛出异常
     *
     * @param source log name
     * @return A handler for tasks that cannot be executed by ThreadPool
     */
    public static RejectedExecutionHandler newReject(String source) {
        return (r, p) -> {
            log.error("[{}] ThreadPool[{}] overload, the task[{}] will be dropped!", source, p, r);
            log.warn("[{}] Maybe you need to adjust the ThreadPool config!", source);
        };
    }

    /**
     * 调用线程运行
     *
     * @param source log name
     * @return A handler for tasks that cannot be executed by ThreadPool
     */
    public static RejectedExecutionHandler newCallerRun(String source) {
        return (r, p) -> {
            log.warn("[{}] ThreadPool[{}] overload, the task[{}] will run by caller thread!", source, p, r);
            log.warn("[{}] Maybe you need to adjust the ThreadPool config!", source);
            if (!p.isShutdown()) {
                r.run();
            }
        };
    }

    /**
     * 新线程运行
     *
     * @param source log name
     * @return A handler for tasks that cannot be executed by ThreadPool
     */
    public static RejectedExecutionHandler newThreadRun(String source) {
        return (r, p) -> {
            log.warn("[{}] ThreadPool[{}] overload, the task[{}] will run by a new thread!", source, p, r);
            log.warn("[{}] Maybe you need to adjust the ThreadPool config!", source);
            if (!p.isShutdown()) {
                String threadName = source + "-T-" + COUNTER.getAndIncrement();
                log.info("[{}] create new thread[{}] to run job", source, threadName);
                new Thread(r, threadName).start();
            }
        };
    }
}
