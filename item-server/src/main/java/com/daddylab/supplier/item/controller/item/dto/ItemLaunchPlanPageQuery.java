package com.daddylab.supplier.item.controller.item.dto;

import com.alibaba.cola.dto.PageQuery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/5/31 09:39
 * @Description: 请描述下这个类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("商品上新计划分页查询参数")
public class ItemLaunchPlanPageQuery extends PageQuery {
    private static final long serialVersionUID = -3064232663280040700L;
    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "编号")
    private String no;

    @ApiModelProperty(value = "时间范围查询-开始（时间戳，单位秒）")
    private Long timeStart;

    @ApiModelProperty(value = "时间范围查询-结束（时间戳，单位秒）")
    private Long timeEnd;

    @ApiModelProperty(value = "商品数量")
    private Integer itemNum;

    @ApiModelProperty("合作模式（业务线）多选")
    private List<Integer> businessLine = new ArrayList<>();
}
