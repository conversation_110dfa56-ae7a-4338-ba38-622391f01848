package com.daddylab.supplier.item.controller.open.externalUser;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.auth.ExternalUserAuthBizService;
import com.daddylab.supplier.item.application.auth.LoginCmd;
import com.daddylab.supplier.item.application.auth.SendCodeCmd;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @since 2024/5/13
 */
@RestController
@Api(value = "[外部系统]登录API", tags = "[外部系统]登录API")
@RequestMapping("/external/auth")
@RequiredArgsConstructor
@Validated
public class ExternalAuthController {

    private final ExternalUserAuthBizService externalUserAuthBizService;

    @ResponseBody
    @ApiOperation(value = "获取验证码")
    @PostMapping("/getSmsCode")
    public Response getSmsCode(@RequestBody @Valid SendCodeCmd cmd) {
        return externalUserAuthBizService.sendCode(cmd.getPhone());
    }

    @ResponseBody
    @ApiOperation(value = "登录")
    @PostMapping("/login")
    public SingleResponse<String> login(@RequestBody @Valid LoginCmd cmd) {
        return externalUserAuthBizService.login(cmd.getPhone(), cmd.getCode());
    }


}
