package com.daddylab.supplier.item.controller.drawer;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.drawer.CheckInfoService;
import com.daddylab.supplier.item.application.drawer.ItemDrawerAuditBizService;
import com.daddylab.supplier.item.application.drawer.ItemDrawerService;
import com.daddylab.supplier.item.common.domain.dto.IdCmd;
import com.daddylab.supplier.item.domain.drawer.form.ItemDrawerForm;
import com.daddylab.supplier.item.domain.drawer.form.ItemLaunchUrgeParams;
import com.daddylab.supplier.item.domain.drawer.form.LaunchReviewForm;
import com.daddylab.supplier.item.domain.drawer.vo.CheckReportVO;
import com.daddylab.supplier.item.domain.drawer.vo.ItemDrawerVO;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.CommonCheckInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemResp;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.types.itemDrawer.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/4/1
 */
@Api(value = "商品抽屉管理", tags = "商品抽屉管理")
@Slf4j
@RestController
@RequestMapping("/item-drawer")
public class ItemDrawerController {

    @Autowired
    private ItemDrawerService itemDrawerService;

    @Resource
    private ItemDrawerAuditBizService itemDrawerAuditBizService;

    @Resource
    private CheckInfoService checkInfoService;

    //    @ApiOperation("保存")
//    @PostMapping("/add")
//    public SingleResponse<Long> add(@RequestBody ItemDrawerForm itemDrawerForm) {
//        return itemDrawerService.add(itemDrawerForm,  UserContext.getUserId(), UserContext.getLoginName());
//    }
    @Auth(resource = "/item-drawer/edit")
    @ApiOperation("编辑")
    @PostMapping("/edit")
    public SingleResponse<Boolean> edit(@Valid @RequestBody ItemDrawerForm itemDrawerForm) {
        return itemDrawerService.edit(itemDrawerForm, UserContext.getUserId(),
                UserContext.getLoginName());
    }

    @Auth(resource = "/item-drawer/urge")
    @ApiOperation("催办")
    @PostMapping("/urge")
    public Response urge(@Valid @RequestBody ItemLaunchUrgeParams params) {
        return itemDrawerService.urge(params.getRecipients(), params.getItemId(),
                UserContext.getNickName());
    }

    @ApiOperation("获取抽屉详情")
    @GetMapping("/detail")
    public SingleResponse<ItemDrawerVO> detail(
            @ApiParam(value = "商品ID")
            @RequestParam("itemId") Long itemId) {
        return itemDrawerService.detail(itemId, UserContext.getUserId(),
                UserContext.getLoginName());
    }

    @ApiOperation("获取检测报告信息")
    @GetMapping("/checkInfo")
    public SingleResponse<CheckReportVO> getCheckInfo(
            @ApiParam(value = "商品ID")
            @RequestParam("itemId") Long itemId) {
        return itemDrawerService.getCheckInfo(itemId, UserContext.getUserId(),
                UserContext.getLoginName());
    }

    @ApiOperation("获取检测报告信息（V2）")
    @Auth("/item-drawer/checkInfo")
    @GetMapping("/checkInfo/v2")
    public SingleResponse<CommonCheckInfo> getCheckInfoV2(
            @ApiParam(value = "商品ID")
            @RequestParam("itemId") Long itemId) {
        return checkInfoService.getCommonCheckInfo(itemId);
    }


    @Auth("/item-drawer/submit")
    @ApiOperation("提交")
    @PostMapping("/submit")
    public SingleResponse<Boolean> submit(
            @ApiParam(value = "抽屉ID")
            @RequestParam("drawerId") Long drawerId) {
        return itemDrawerService.submit(drawerId, UserContext.getUserId(),
                UserContext.getLoginName());
    }

    @Auth("/item-drawer/rollback")
    @ApiOperation("商品上新状态回退")
    @PostMapping("/rollback")
    public SingleResponse<Boolean> rollback(@Valid @RequestBody RollbackCmd cmd) {
        //兼容之前的入参，默认回退到【待设计】
        itemDrawerService.rollback(cmd.getId(),
                Optional.ofNullable(cmd.getRollbackTo()).orElse(ItemLaunchStatus.TO_BE_DESIGNED),
                cmd.getAuditRollbackTo());
        return SingleResponse.of(true);
    }

    @Auth("/item-drawer/rollback")
    @ApiOperation("商品上新状态跳转")
    @PostMapping("/setLaunchStatus")
    public SingleResponse<Boolean> rollback(@Valid @RequestBody SetLaunchStatusCmd cmd) {
        itemDrawerService.setLaunchStatus(cmd);
        return SingleResponse.of(true);
    }

    @ApiOperation("商品上新审核任务获取")
    @PostMapping("/audit/getActiveTask")
    public SingleResponse<ItemDrawerModuleAuditTaskVO> getActiveTask(
            @Validated @RequestBody GetActiveTaskQuery query) {
        return SingleResponse.of(
                itemDrawerAuditBizService.getActiveTask(query.getType(), query.getLiveVerbalTrickId(), UserContext.getUserId(),
                        query.getItemId()));
    }

    @ApiOperation("商品上新审核任务认领")
    @PostMapping("/audit/claim")
    public Response claim(@Validated @RequestBody TaskClaimCmd cmd) {
        itemDrawerAuditBizService.claim(UserContext.getUserId(), cmd.getTaskId());
        return Response.buildSuccess();
    }

    @ApiOperation("获取 QC 暂存建议")
    @GetMapping("/detail/qcTemporarilyAdvice")
    public MultiResponse<ItemDrawerModuleAdviceVO> qcTemporarilyAdvice(@ApiParam(value = "新品商品ID") @RequestParam("itemId") Long itemId) {
        List<ItemDrawerModuleAdviceVO> qcTemporarilyAdvice = itemDrawerAuditBizService.getQcTemporarilyAdvice(itemId);
        return MultiResponse.of(qcTemporarilyAdvice);
    }

    @ApiOperation("商品上新审核任务提交")
    @PostMapping("/audit/complete")
    public Response complete(@Validated @RequestBody TaskCompleteCmd cmd) {
        // QC 暂存数据
        if (Objects.nonNull(cmd.getSubmit()) && !cmd.getSubmit()) {
            itemDrawerAuditBizService.temporarilySaveQcAdvice(cmd.getTaskId(), cmd.getAdvices());
            return Response.buildSuccess();
        }
        itemDrawerAuditBizService.complete(UserContext.getUserId(), cmd.getTaskId(), cmd.getLiveVerbalTrickId(), cmd.getAdvices(),
                DateUtil.currentTime(), cmd.getPass());
        return Response.buildSuccess();
    }

    @Auth("NP_LIVE_VERBAL_TRICK")
    @ApiOperation("编辑直播话术")
    @PostMapping("/editLiveVerbalTrick")
    public Response editLiveVerbalTrick(@Valid @RequestBody EditLiveVerbalTrickCmd cmd) {
        cmd.setUid(UserContext.getUserId());
        return itemDrawerService.editLiveVerbalTrick(cmd);
    }

    @Auth("NP_LIVE_VERBAL_TRICK")
    @ApiOperation("编辑直播话术负责人")
    @PostMapping("/editLiveVerbalTrickPrincipal")
    public Response editLiveVerbalTrickPrincipal(@RequestBody EditLiveVerbalTrickPrincipalCmd cmd) {
        return itemDrawerService.editLiveVerbalTrickPrincipal(cmd);
    }

    @Auth("/item-drawer/rollbackLiveVerbalTrick")
    @ApiOperation(value = "直播话术撤回", notes = "撤回到上一状态，若当前状态不允许撤回，则返回错误信息")
    @PostMapping("/rollbackLiveVerbalTrick")
    public Response rollbackLiveVerbalTrick(@Valid @RequestBody IdCmd cmd) {
        return itemDrawerService.rollbackLiveVerbalTrick(UserContext.getUserId(), cmd.getId(), cmd.getLiveVerbalTrickId());
    }

    @ApiOperation("获取P系统商品信息")
    @GetMapping("/psysItemInfo")
    public SingleResponse<PartnerItemResp> psysItemInfo(
            @ApiParam(value = "商品ID")
            @RequestParam("itemId") Long itemId) {
        return itemDrawerService.psysItemInfo(itemId);
    }

    @ApiOperation("删除直播话术")
    @GetMapping("/delLiveVerbalTrick")
    public Response delLiveVerbalTrick(
            @ApiParam(value = "商品ID") @RequestParam("itemId") Long itemId,
            @ApiParam(value = "直播话术ID") @RequestParam("liveVerbalTrickId") Long liveVerbalTrickId) {
        itemDrawerService.delLiveVerbalTrick(itemId, liveVerbalTrickId);
        return Response.buildSuccess();
    }

    @ApiOperation("发起复审")
    @PostMapping("/launchReview")
    public SingleResponse<Boolean> launchReview(@Valid @RequestBody LaunchReviewForm launchReview) {
        return SingleResponse.of(itemDrawerService.launchReview(UserContext.getUserId(), launchReview));
    }

    @ApiOperation("商品上新状态还原到下架前状态")
    @PostMapping("/recoverToBeforeStatus")
    public Response recoverToBeforeStatus(@Valid @RequestBody IdCmd cmd) {
        itemDrawerService.recoverToBeforeStatus(cmd.getId());
        return Response.buildSuccess();
    }

}
