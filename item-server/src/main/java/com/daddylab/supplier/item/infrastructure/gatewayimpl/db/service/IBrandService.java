package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Brand;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 品牌 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface IBrandService extends IDaddyService<Brand> {

    Collection<String> brandIdsToNos(List<Long> brandIds);
}
