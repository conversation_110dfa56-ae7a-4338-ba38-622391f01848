package com.daddylab.supplier.item.application.region;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.region.enums.RegionLevel;
import com.daddylab.supplier.item.domain.region.service.RegionDomainService;
import com.daddylab.supplier.item.domain.region.vo.Region;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import java.util.Optional;

/**
 * <p>
 * 地区关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-13
 */
@Service
public class RegionBizServiceImpl implements RegionBizService {
    @Inject
    RegionDomainService regionDomainService;
    
    @Override
    public MultiResponse<Region> getRegionList() {
        return MultiResponse.of(regionDomainService.getRegionTree().getRootRegions());
    }
    
    @Override
    public MultiResponse<Region> getRegionList(String parentCode) {
        if (StringUtil.isNotBlank(parentCode)) {
            return Optional.ofNullable(regionDomainService.getRegionTree().getRegion(parentCode))
                    .map(Region::getChildren)
                    .map(MultiResponse::of)
                    .orElseThrow(() -> ExceptionPlusFactory.bizException(ErrorCode.REGION_CODE_ERROR));
        }
        return getRegionList();
    }
    
    @Override
    public MultiResponse<Region> getBigRegionList() {
        return MultiResponse.of(regionDomainService.getRegionTree().getBigRegions());
    }
    
    @Override
    public SingleResponse<AddressParseResult> parseAddress(String address) {
        return regionDomainService.getRegionTree().areaFuzzyQuery(address)
                .map(region -> {
                    AddressParseResult addressParseResult = new AddressParseResult();
                    addressParseResult.setAddress(address);
                    for (Region r : region.getRegionPath()) {
                        if (r.isLevel(RegionLevel.COUNTY)) {
                            addressParseResult.setArea(r.getName());
                            addressParseResult.setAreaCode(r.getCode());
                        }
                        if (r.isLevel(RegionLevel.CITY)) {
                            addressParseResult.setCity(r.getName());
                            addressParseResult.setCityCode(r.getCode());
                        }
                        if (r.isLevel(RegionLevel.PROVINCE)) {
                            addressParseResult.setProvince(r.getName());
                            addressParseResult.setProvinceCode(r.getCode());
                        }
                    }
                    return SingleResponse.of(addressParseResult);
                })
                .orElseThrow(() -> ExceptionPlusFactory.bizException(ErrorCode.REGION_CODE_ERROR));
    }
    
    @Override
    public Response clearRegionTreeCache() {
        regionDomainService.clearRegionTreeCache();
        return Response.buildSuccess();
    }
}
