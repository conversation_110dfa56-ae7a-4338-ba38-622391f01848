package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 全渠道开票数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AllChannelBillData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 子订单号
     */
    private String subOrderId;

    /**
     * 平台商品ID
     */
    private String goodsId;

    /**
     * 平台
     */
    private String platName;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 下单时间
     */
    private Long ctime;

    /**
     * 支付时间
     */
    private Long payTime;

    /**
     * 订单状态，0:未知,1:待支付,2:定金已支付,3:已支付,4:部分已发货,5:全部已发货,6:已完成,7:已取消
     */
    private Integer orderStatus;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * sku编码
     */
    private String skuCode;

    /**
     * 规格型号
     */
    private String spec;

    /**
     * 单位
     */
    private String unit;

    /**
     * 商品数量
     */
    private Integer goodsNum;

    /**
     * 商品单价
     */
    private BigDecimal unitPrice;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 优惠折扣金额
     */
    private BigDecimal discountAmt;

    /**
     * 运费
     */
    private BigDecimal postFee;

    /**
     * 售后金额
     */
    private BigDecimal rfdAmount;

    /**
     * 商品和服务税收分类编码
     */
    private String taxClassCode;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 实付金额
     */
    private BigDecimal realAmount;

    /**
     * 开票金额（实付-售后）
     */
    private BigDecimal invoiceAmt;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 不含税开票金额
     */
    private BigDecimal invoiceNorate;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 1 退款中。0没有
     */
    private Integer isRefund;

    /**
     * 金额刷新，0：否。1是。默认0
     */
    private Integer amountRefresh;

    /**
     * 逻辑删除标示
     */
    @TableLogic(value = "0", delval = "1")
    private Integer isDel;

    /**
     * 退款状态是否刷新。否,1:是，默认0
     */
    private Boolean refundRefresh;

    private String errorMsg;

    /**
     * 退款完成时间
     */
    private Long refundFinishTime;


}
