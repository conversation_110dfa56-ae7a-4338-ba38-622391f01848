package com.daddylab.supplier.item.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年11月29日 2:24 PM
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "invoice-title")
@EnableConfigurationProperties(InvoiceTitleConfig.class)
@Component
public class InvoiceTitleConfig {

    private List<String> titles;
}
