package com.daddylab.supplier.item.application.drawer;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.domain.drawer.form.ItemDrawerForm;
import com.daddylab.supplier.item.domain.drawer.form.LaunchReviewForm;
import com.daddylab.supplier.item.domain.drawer.vo.CheckReportVO;
import com.daddylab.supplier.item.domain.drawer.vo.ItemDrawerImageVO;
import com.daddylab.supplier.item.domain.drawer.vo.ItemDrawerVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.LaunchItemType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemResp;
import com.daddylab.supplier.item.types.itemDrawer.EditLiveVerbalTrickCmd;
import com.daddylab.supplier.item.types.itemDrawer.EditLiveVerbalTrickPrincipalCmd;
import com.daddylab.supplier.item.types.itemDrawer.SetLaunchStatusCmd;
import org.apache.commons.lang3.tuple.Triple;

import java.util.List;
import java.util.Map;

/**
 * Class  ItemDrawerService
 *
 * @Date 2022/5/31下午4:16
 * <AUTHOR>
 */
public interface ItemDrawerService {
    Response urge(List<Long> recipients, Long itemId, String urger);

    /**
     * 新增商品抽屉
     *
     * @param itemDrawerForm
     * @param userId
     * @param loginName
     * @return
     */
    SingleResponse<Long> add(ItemDrawerForm itemDrawerForm, Long userId, String loginName);

    /**
     * 获取抽屉详情
     *
     * @param itemId
     * @param userId
     * @param loginName
     * @return
     */
    SingleResponse<ItemDrawerVO> detail(Long itemId, Long userId, String loginName);


    /**
     * 获取ItemDrawerVO
     *
     * @param itemDrawer
     * @return
     */
    ItemDrawerVO getItemDrawerVO(ItemDrawer itemDrawer);

    /**
     * 获取ItemDrawerVO
     *
     * @param drawerId
     * @return
     */
    ItemDrawerVO getItemDrawerVO(Long drawerId);

    /**
     * 获取抽屉
     *
     * @param itemId
     * @return
     */
    ItemDrawer getItemDrawer(Long itemId);

    ItemDrawer getMergeMainItemDrawer(Long itemId);

    Map<Long, String> getStandardNamesByItemIds(List<Long> itemIds);

    Map<Long, String> getHomeCopyByItemIds(List<Long> itemIds);

    void noticeItemLaunchModifyPrincipals(String operatorNick, Long newPrincipalId, String itemName, Long itemId);

    /**
     * 编辑
     *
     * @param itemDrawerForm
     * @param userId
     * @param loginName
     * @return
     */
    SingleResponse<Boolean> edit(ItemDrawerForm itemDrawerForm, Long userId, String loginName);

    /**
     * 获取检测报告信息
     *
     * @param itemId
     * @param userId
     * @param loginName
     * @return
     */
    SingleResponse<CheckReportVO> getCheckInfo(Long itemId, Long userId, String loginName);

    /**
     * 获取抽屉图片
     *
     * @param itemDrawerId
     * @return
     */
    Triple<List<ItemDrawerImageVO>, List<ItemDrawerImageVO>, List<ItemDrawerImageVO>> getItemDrawerImages(Long itemDrawerId);

    /**
     * 提交
     *
     * @param drawerId
     * @return
     */
    SingleResponse<Boolean> submit(Long drawerId, Long userId, String loginName);

    void processTerminateByItemId(Long itemId);

    /**
     * 删除抽屉
     *
     * @param itemId
     */
    Boolean deleteItemDrawer(Long itemId);

    /**
     * 商品上新状态回退
     *
     * @param itemId          商品ID
     * @param rollbackTo      回退到
     * @param auditRollbackTo 审核状态退回
     */
    void rollback(Long itemId, ItemLaunchStatus rollbackTo, ItemAuditStatus auditRollbackTo);

    void setLaunchStatus(SetLaunchStatusCmd cmd);

    /**
     * 当上新状态从待审核变为待修改时，如果上新计划中存在首页文案，则同步更新。
     *
     * @param itemId
     */
    void updateHomeCopy(Long itemId);

    void updateItemDrawerType(Long itemId, LaunchItemType type);

    /**
     * 编辑直播话术
     *
     * @param cmd 参数封装
     * @return 响应封装
     */
    Response editLiveVerbalTrick(EditLiveVerbalTrickCmd cmd);

    /**
     * 编辑直播话术负责人
     * @param cmd 参数封装
     * @return
     */
    Response editLiveVerbalTrickPrincipal(EditLiveVerbalTrickPrincipalCmd cmd);

    /**
     * 直播话术撤回
     *
     * @param userId 用户ID
     * @param itemId 商品ID
     * @return
     */
    Response rollbackLiveVerbalTrick(Long userId, Long itemId, Long verbalTrickId);

    SingleResponse<PartnerItemResp> psysItemInfo(Long itemId);

    /**
     * 删除直播话术
     *
     * @param itemId
     * @param liveVerbalTrickId
     */
    void delLiveVerbalTrick(Long itemId, Long liveVerbalTrickId);

    /**
     * 发起复审
     *
     * @param userId Long 当前用户
     * @param launchReviewForm LaunchReviewForm 表单数据
     * @return java.lang.Boolean
     * @date 2024/4/8 18:12
     * <AUTHOR>
     */
    Boolean launchReview(Long userId, LaunchReviewForm launchReviewForm);

    /**
     * 将源抽屉数据拷贝到目标抽屉
     *
     * @param sourceDrawerId Long 源抽屉
     * @param targetDrawerId Long 目标抽屉
     * @date 2024/4/9 09:20
     * <AUTHOR>
     */
    void copyDrawer(Long sourceDrawerId, Long targetDrawerId);

    /**
     *
     *
     * @param sourceDrawer ItemDrawer
     * @param targetDrawer ItemDrawer
     * @return void
     * @date 2024/4/9 15:07
     * <AUTHOR>
     */
    void copyDrawerToDrawer(ItemDrawer sourceDrawer, ItemDrawer targetDrawer);

    /**
     * 将抽屉信息拷贝给其他商品
     *
     * @param sourceDrawerId Long
     * @param itemIds List<Long>
     * @date 2024/4/9 15:07
     * <AUTHOR>
     */
    void copyDrawerToItemIds(Long sourceDrawerId, List<Long> itemIds);

    ItemDrawer getById(Long id);

    void noticeToBeDesigned(Long itemId, Long operatorId);

    void noticeToLegal(Long itemId, Long operatorId);

    void noticeToQc(Long itemId, Long operatorId);

    void noticeToUpdate(Long itemId, Long operatorId);

    /**
     * 还原到上一个上新状态
     *
     * @param itemId 商品ID
     */
    void recoverToBeforeStatus(Long itemId);

    void copyStatusInMerge(Long itemId);

    void copyStatus(Long mainItemId, List<Long> itemIds);
}
