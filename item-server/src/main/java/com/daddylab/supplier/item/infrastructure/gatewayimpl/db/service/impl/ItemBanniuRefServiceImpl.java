package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemBanniuRef;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemBanniuRefMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemBanniuRefService;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 班牛数据关联记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Service
public class ItemBanniuRefServiceImpl extends DaddyServiceImpl<ItemBanniuRefMapper, ItemBanniuRef> implements IItemBanniuRefService {

}
