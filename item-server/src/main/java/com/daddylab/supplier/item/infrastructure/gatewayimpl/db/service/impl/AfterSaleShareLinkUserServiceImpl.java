package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSaleShareLinkUser;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AfterSaleShareLinkUserMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSaleShareLinkUserService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 售后分享链接邀请用户信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
@Service
public class AfterSaleShareLinkUserServiceImpl extends DaddyServiceImpl<AfterSaleShareLinkUserMapper, AfterSaleShareLinkUser> implements IAfterSaleShareLinkUserService {

}
