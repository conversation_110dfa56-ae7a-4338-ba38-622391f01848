package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 店铺授权
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ShopAuthorization implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺编号
     */
    private String sn;

    /**
     * 平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城
     */
    private Platform platform;

    /**
     * 店铺授权访问令牌
     */
    private String accessToken;

    /**
     * 令牌失效时间
     */
    private Long expiredAt;

    /**
     * 令牌刷新凭证
     */
    private String refreshToken;

    /**
     * 令牌失效时间
     */
    private Long refreshTokenExpiredAt;

    /**
     * 授权域
     */
    private String scopes;

    /**
     * 平台响应数据
     */
    private String tokenData;
    
    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    public boolean isExpired() {
        return DateUtil.currentSeconds() >= expiredAt;
    }

    public boolean isNotExpired() {
        return !isExpired();
    }

}
