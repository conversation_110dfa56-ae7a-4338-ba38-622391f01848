package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Author: <PERSON>
 * @Date: 2022/6/1 15:37
 * @Description: 发送企微消息结果
 */
@Data
public class SendMsgQyWeixinResult extends BaseQyWeixinResult {
    private String invaliduser;
    private String invalidparty;
    private String invalidtag;
    private String unlicenseduser;
    private String msgid;
    @JsonProperty("response_code")
    private String responseCode;


    public Boolean isSendSuccess(){
        return this.getErrcode() == 0;
    }
}
