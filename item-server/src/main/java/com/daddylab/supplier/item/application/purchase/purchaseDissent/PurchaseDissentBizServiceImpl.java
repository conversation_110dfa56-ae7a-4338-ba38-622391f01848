package com.daddylab.supplier.item.application.purchase.purchaseDissent;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.application.purchase.PurchaseBizService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.PurchaseDissentTransMapper;
import com.daddylab.supplier.item.controller.purchaseDissent.dto.PurchaseDissentVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseDissent;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseDissentService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @ClassName PurchaseDissentServiceImpl.java
 * @description
 * @createTime 2021年11月17日 14:01:00
 */
@Slf4j
@Service
public class PurchaseDissentBizServiceImpl implements PurchaseDissentBizService {

    @Autowired
    private IPurchaseDissentService iPurchaseDissentService;
    @Autowired
    private PurchaseBizService purchaseBizService;

    @Override
    public MultiResponse<PurchaseDissentVo> getDissent(Long id) {
        Assert.notNull(id, "采购异议id入参不得为空");
        QueryWrapper<PurchaseDissent> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(PurchaseDissent::getPurchaseId, id)
                .orderByDesc(PurchaseDissent::getId);
        List<PurchaseDissent> list = iPurchaseDissentService.list(queryWrapper);
        List<PurchaseDissentVo> purchaseDissentVos = PurchaseDissentTransMapper.INSTANCE.doToVos(list);
        for (PurchaseDissentVo purchaseDissentVo : purchaseDissentVos) {
            purchaseDissentVo.setTime(DateUtil.format(purchaseDissentVo.getCreatedAt()));
        }
        return MultiResponse.of(purchaseDissentVos);
    }

    @Override
    @Transactional
    public Response add(String token, com.daddylab.supplier.item.controller.purchaseDissent.dto.PurchaseDissentCmd cmd) {
        purchaseBizService.checkToken(token,cmd.getCode());
        PurchaseDissent purchaseDissent = PurchaseDissentTransMapper.INSTANCE.cmdToDo(cmd);
        iPurchaseDissentService.save(purchaseDissent);
        purchaseBizService.updateStatus(cmd.getPurchaseId());
        return Response.buildSuccess();
    }

    @Override
    public Response update(String token, com.daddylab.supplier.item.controller.purchaseDissent.dto.PurchaseDissentCmd cmd) {
        purchaseBizService.checkToken(token,cmd.getCode());
        Assert.notNull(cmd.getId(), "修改采购异议id入参不得为空");
        PurchaseDissent purchaseDissent = iPurchaseDissentService.getById(cmd.getId());
        if (Objects.isNull(purchaseDissent)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查不到该记录,无法删除");
        }
        PurchaseDissent purchaseDissentData = PurchaseDissentTransMapper.INSTANCE.cmdToDo(cmd);
        iPurchaseDissentService.updateById(purchaseDissentData);
        return Response.buildSuccess();
    }


}
