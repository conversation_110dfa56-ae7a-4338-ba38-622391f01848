package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTrainingMaterials;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemTrainingMaterialsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemTrainingMaterialsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 新品商品培训资料 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Service
public class ItemTrainingMaterialsServiceImpl extends DaddyServiceImpl<ItemTrainingMaterialsMapper, ItemTrainingMaterials> implements IItemTrainingMaterialsService {

    @Override
    public ItemTrainingMaterials getByItemId(Long itemId) {
        return lambdaQuery().eq(ItemTrainingMaterials::getItemId, itemId).one();
    }
}
