package com.daddylab.supplier.item.infrastructure.cv;

import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.microsoft.azure.cognitiveservices.vision.computervision.ComputerVisionClient;
import com.microsoft.azure.cognitiveservices.vision.computervision.implementation.ComputerVisionImpl;
import com.microsoft.azure.cognitiveservices.vision.computervision.models.OperationStatusCodes;
import com.microsoft.azure.cognitiveservices.vision.computervision.models.ReadHeaders;
import com.microsoft.azure.cognitiveservices.vision.computervision.models.ReadInStreamHeaders;
import com.microsoft.azure.cognitiveservices.vision.computervision.models.ReadOperationResult;

import lombok.extern.slf4j.Slf4j;

import rx.Observable;
import rx.Single;
import rx.schedulers.Schedulers;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Class  CVUtil 微软云图像识别文字
 *
 * @Date 2022/6/30下午12:07
 * <AUTHOR>
 */
@Slf4j
public class CVUtil {

    /**
     * 轮训间隔（单位毫秒）
     */
    public static final int POLLING_INTERVAL = 500;
    public static final int TIMEOUT = 60;

    /**
     * 图片识别文字
     *
     * @param url cdn地址
     * @return 识别结果
     */
    public static Single<ReadOperationResult> imageVision(String url) {
        return imageVision(ApplicationContextUtil.getBean(ComputerVisionClient.class), url);
    }

    /**
     * 图片识别文字
     *
     * @param url    cdn地址
     * @param client 微软云CV客户端
     * @return 识别结果
     */
    public static Single<ReadOperationResult> imageVision(ComputerVisionClient client, String url) {
        log.info("【CV】开始提交任务，url={}", url);
        ComputerVisionImpl vision = (ComputerVisionImpl) client.computerVision();
        final Observable<ReadOperationResult> objectObservable = vision.readWithServiceResponseAsync(
                        url, null)
                .timeout(TIMEOUT, TimeUnit.SECONDS)
                .flatMap(response -> {
                    final ReadHeaders responseHeader = response.headers();
                    String operationLocation = responseHeader.operationLocation();
                    final String operationId = extractOperationIdFromOpLocation(operationLocation);
                    log.info("【CV】任务提交成功，开始轮询获取操作结果，url={}，operationId={}", url, operationId);
                    return Observable.interval(500, TimeUnit.MILLISECONDS).subscribeOn(Schedulers.io())
                            .doOnNext(v -> log.debug("【CV】轮询获取操作结果（第{}次），url={}，operationId={}", v, url, operationId))
                            .map(v -> vision.getReadResult(UUID.fromString(operationId))).takeFirst(
                                    readResults ->
                                            readResults.status().equals(OperationStatusCodes.FAILED)
                                                    || readResults.status()
                                                    .equals(OperationStatusCodes.SUCCEEDED));
                });
        return objectObservable.toSingle();
    }


    /**
     * 根据图片内容识别
     *
     * @param data 二进制数据
     * @return 识别结果
     */
    public static Single<ReadOperationResult> imageVision(byte[] data) {
        return imageVision(ApplicationContextUtil.getBean(ComputerVisionClient.class), data);
    }

    /**
     * 图片识别文字
     *
     * @param client 微软云CV客户端
     * @param data   图片二进制数据
     * @return 识别结果
     */
    public static Single<ReadOperationResult> imageVision(ComputerVisionClient client,
            byte[] data) {
        ComputerVisionImpl vision = (ComputerVisionImpl) client.computerVision();
        final Observable<ReadOperationResult> objectObservable = vision.readInStreamWithServiceResponseAsync(
                        data, null)
                .timeout(TIMEOUT, TimeUnit.SECONDS)
                .flatMap(response -> {
                    final ReadInStreamHeaders responseHeader = response.headers();
                    String operationLocation = responseHeader.operationLocation();
                    final String operationId = extractOperationIdFromOpLocation(operationLocation);
                    return Observable.interval(POLLING_INTERVAL, TimeUnit.MILLISECONDS).subscribeOn(Schedulers.io())
                            .map(v -> vision.getReadResult(UUID.fromString(operationId))).takeFirst(
                                    readResults ->
                                            readResults.status().equals(OperationStatusCodes.FAILED)
                                                    || readResults.status()
                                                    .equals(OperationStatusCodes.SUCCEEDED));
                });
        return objectObservable.toSingle();
    }

    /**
     * 提取操作id
     *
     * @param operationLocation 操作地址
     * @return 提取操作id
     */
    private static String extractOperationIdFromOpLocation(String operationLocation) {
        if (operationLocation != null && !operationLocation.isEmpty()) {
            String[] splits = operationLocation.split("/");

            if (splits.length > 0) {
                return splits[splits.length - 1];
            }
        }
        throw new IllegalStateException(
                "Something went wrong: Couldn't extract the operation id from the operation location");
    }
}
