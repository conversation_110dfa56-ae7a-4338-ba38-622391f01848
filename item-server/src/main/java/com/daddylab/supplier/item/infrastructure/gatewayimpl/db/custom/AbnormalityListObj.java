package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/5/23
 */
@Data
public class AbnormalityListObj {
    private Long abnormalityId;
    private String logisticsNo;
    private String logisticsCompanyName;
    private String consignTime;
    private String wdtTradeNo;
    private Long wdtTradeId;
    private String stockoutNo;
    private String srcOrderNo;
    private Integer tradeStatus;
    private String receiverName;
    private String receiverAddress;
    private String receiverArea;
    private String receiverMobile;
    private Long callbackId;
    private Integer traceSource;
    private Integer logisticsStatus;
    private Long trackTime;
    private String warehouseNo;
    private Integer abnormalStatus;
    private Integer activateWarning;
    private Integer platform;
    private Long shopId;

}
