package com.daddylab.supplier.item.controller.open;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSales.*;
import com.daddylab.supplier.item.application.exportTask.ExportTaskBizService;
import com.daddylab.supplier.item.application.refundOrder.RefundOrderServiceForP;
import com.daddylab.supplier.item.controller.item.dto.ExportTaskVo;
import com.daddylab.supplier.item.controller.item.dto.TaskPageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesSendOnInfo;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderBaseInfoForP;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderDetailForP;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderQueryForP;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> up
 * @date 2022年10月18日 9:53 AM
 */
@Slf4j
@Api(value = "售后管理开放接口", tags = "售后管理开放接口")
@RestController
@RequestMapping("/open/partner/afterSales")
public class OpenAfterSalesController {

    @Resource
    AfterSalesBizService afterSalesBizService;

    @Resource
    AfterSalesReceiveBizService afterSalesReceiveBizService;

    @Resource
    RefundOrderServiceForP refundOrderServiceForP;

    @Resource
    ExportTaskBizService exportTaskBizService;

    @Resource
    AfterSalesWarehouseBizService afterSalesWarehouseBizService;

    @ResponseBody
    @ApiOperation(value = "登记/编辑异常件信息")
    @PostMapping("/saveAbnormalInfo")
    public SingleResponse<Boolean> saveAbnormalInfo(@RequestBody AfterSalesAbnormalCmd cmd) {
        log.info("售后管理-保存异常件信息参数:{}", cmd);
        return afterSalesBizService.saveAbnormalInfo(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "删除异常件信息")
    @GetMapping("/removeAbnormal")
    public SingleResponse<Boolean> removeAbnormal(@RequestParam(value = "abnormalInfoId") Long abnormalInfoId) {
        log.info("售后管理-删除异常件信息参数，id:{}", abnormalInfoId);
        return afterSalesBizService.removeAbnormalInfo(abnormalInfoId);
    }


    @ResponseBody
    @ApiOperation(value = "异常件列表")
    @PostMapping("/pageQuery")
    public PageResponse<AfterSalesPageVO> pageQuery(@RequestBody @Validated AfterSalesPageQuery afterSalesPageQuery) {
        log.info("售后管理-异常件列表参数，query:{}", afterSalesPageQuery);
        afterSalesPageQuery.setPartnerProviderId(PartnerProviderContext.getId());
        return afterSalesBizService.pageQueryAbnormalInfo(afterSalesPageQuery);
    }

    @ResponseBody
    @ApiOperation(value = "异常件信息导出")
    @PostMapping("/export")
    public SingleResponse<Boolean> export(@RequestBody @Validated AfterSalesPageQuery afterSalesPageQuery) {
        log.info("售后管理-异常件信息导出参数，query:{}", afterSalesPageQuery);
        afterSalesPageQuery.setPartnerProviderId(PartnerProviderContext.getId());
        return afterSalesBizService.exportAbnormalInfo(afterSalesPageQuery);
    }

    @ResponseBody
    @ApiOperation(value = "查询转寄单原始信息")
    @GetMapping("/getSendOnInfo")
    public SingleResponse<AfterSalesSendOnInfo> getSendOnInfo(
            @RequestParam(value = "abnormalInfoId", required = false) Long abnormalInfoId,
            @RequestParam(value = "returnOrderNo", required = false) String returnOrderNo) {
        log.info("售后管理-查询转寄单原始信息参数，id:{},no:{}", abnormalInfoId, returnOrderNo);
        return afterSalesBizService.getSendOnInfo(abnormalInfoId, returnOrderNo);
    }

    @ResponseBody
    @ApiOperation(value = "保存完善转寄单信息")
    @PostMapping("/saveSendOnInfo")
    public SingleResponse<Boolean> saveSendOnInfo(@RequestBody @Validated AfterSalesSendOnCmd cmd) {
        return afterSalesBizService.saveSendOnInfo(cmd);
    }

    @ApiOperation(value = "登记收货")
    @PostMapping("/receive")
    public Response receive(@RequestBody @Validated AfterSalesReceiveCmd cmd) {
        return afterSalesReceiveBizService.receive(cmd, PartnerProviderContext.getId());
    }

    @ResponseBody
    @ApiOperation(value = "退换单查询")
    @PostMapping("/refundOrderQuery")
    public PageResponse<RefundOrderBaseInfoForP> refundOrderQuery(
            @RequestBody @Validated RefundOrderQueryForP query) {
        return refundOrderServiceForP.refundOrderQuery(query, PartnerProviderContext.getId());
    }

    @ResponseBody
    @ApiOperation(value = "退换单导出")
    @PostMapping("/refundOrderExport")
    public SingleResponse<Long> refundOrderExport(
            @RequestBody @Validated RefundOrderQueryForP query) {
        return refundOrderServiceForP.refundOrderExport(query, PartnerProviderContext.getId());
    }

    @ResponseBody
    @ApiOperation(value = "退换单详情")
    @GetMapping("/refundOrderDetail")
    public SingleResponse<RefundOrderDetailForP> refundOrderDetail(
            @ApiParam(value = "退换单号", required = true) @RequestParam String refundOrderNo) {
        return refundOrderServiceForP.refundOrderDetail(refundOrderNo, PartnerProviderContext.getId());
    }

    @ResponseBody
    @PostMapping("/excelList")
    @ApiOperation("excel导出列表")
    public PageResponse<ExportTaskVo> excelList(@RequestBody TaskPageQuery pageQuery) {
        return exportTaskBizService.exportList(pageQuery);
    }

    @ResponseBody
    @ApiOperation(value = "供应商仓库列表")
    @GetMapping("/warehouseList")
    public MultiResponse<WarehouseAfterSalesAddressVO> warehouseList() {
        return afterSalesWarehouseBizService.getPartnerProviderReturnWarehouseAddressVOs(PartnerProviderContext.getId());
    }

}
