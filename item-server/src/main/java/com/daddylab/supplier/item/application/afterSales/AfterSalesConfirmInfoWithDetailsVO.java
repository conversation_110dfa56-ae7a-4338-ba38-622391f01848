package com.daddylab.supplier.item.application.afterSales;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/25
 */
@Data
@ApiModel("售后销退确认信息VO（带明细）")
public class AfterSalesConfirmInfoWithDetailsVO {

	/**
	 * 销退确认信息
	 */
	@ApiModelProperty("销退确认信息")
	private AfterSalesConfirmInfoVO confirmInfo;

	/**
	 * 销退明细
	 */
	@ApiModelProperty("销退明细")
	private List<AfterSalesConfirmDetailInfoVO> details;
    

   
}
