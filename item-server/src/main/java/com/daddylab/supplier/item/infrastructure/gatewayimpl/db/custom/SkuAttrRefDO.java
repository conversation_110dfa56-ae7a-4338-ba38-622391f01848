package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13 5:36 下午
 * @description
 */
@Data
public class SkuAttrRefDO implements Serializable {

    private static final long serialVersionUID = 1330814088199936386L;
    private Long skuId;
    private Long attrId;
    private String attrName;
    private String attrValue;

    private Long itemAttrDbId;
}
