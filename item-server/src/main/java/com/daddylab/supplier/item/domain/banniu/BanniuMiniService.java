package com.daddylab.supplier.item.domain.banniu;

import com.daddylab.supplier.item.types.banniu.MiniTaskQuery;

import wjb.open.api.response.mini.MiniColumnListResponse;
import wjb.open.api.response.mini.MiniProjectListResponse;
import wjb.open.api.response.mini.MiniQueryTaskListResponse;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/8/21
 */
public interface BanniuMiniService {
    List<MiniProjectListResponse.Project> projects();

    Optional<MiniProjectListResponse.Project> project(int projectId);

    List<MiniColumnListResponse.ColumnOption> columnOptions(int projectId);

    List<MiniColumnListResponse.ColumnOption> columnOptions(int projectId, boolean cache);

    MiniQueryTaskListResponse.ResultMap tasks(MiniTaskQuery query);

    /**
     * 创建班牛工单（就是工作表记录）
     *
     * @param projectId 工作表ID
     * @param userId 班牛用户ID
     * @param data Map&lt;ColumnId, Value&gt; OR model
     * @return 工单ID
     */
    long taskCreate(int projectId, int userId, Object data);

    void taskUpdate(int projectId, int userId, Object data, int taskId);
}
