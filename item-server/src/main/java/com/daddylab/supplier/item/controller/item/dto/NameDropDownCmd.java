package com.daddylab.supplier.item.controller.item.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11 10:29 上午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("商品名称下拉选入参")
public class NameDropDownCmd extends Command {

    private static final long serialVersionUID = -7996006526509279183L;
    @ApiModelProperty("非必填。商品名称。模糊搜索")
    private String name;

    private Integer pageIndex = 1;

    private Integer pageSize = 10;

    private Boolean useForItemLaunchPlan;

    @ApiModelProperty("非必填。供应商 ID。限制条件")
    private Long providerId;

}
