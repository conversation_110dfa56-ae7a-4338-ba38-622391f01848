package com.daddylab.supplier.item.application.tmpJob;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HtmlUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.SyncReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.TypeReference;
import com.daddylab.supplier.item.application.ItemTrainingMaterials.ItemTrainingMaterialsBizService;
import com.daddylab.supplier.item.application.drawer.ItemDrawerAuditBizService;
import com.daddylab.supplier.item.application.drawer.ItemDrawerMergeBizService;
import com.daddylab.supplier.item.application.drawer.impl.ItemDrawerAppServiceImpl;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.controller.item.dto.ItemLaunchPlanLinkItemParam;
import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerImageTypeEnum;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.timing.StopWatch;
import com.daddylab.supplier.item.infrastructure.upyun.config.UpyunConfig;
import com.daddylab.supplier.item.infrastructure.upyun.gateway.UpyunGateway;
import com.daddylab.supplier.item.infrastructure.upyun.types.UploadImageResult;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.infrastructure.wiki.WikiClient;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerModuleId;
import com.daddylab.supplier.item.types.itemTrainingMaterials.ItemTrainingMaterialsData;
import com.daddylab.supplier.item.types.itemTrainingMaterials.ItemTrainingMaterialsDrawer;
import com.daddylab.supplier.item.types.itemTrainingMaterials.ItemTrainingMaterialsSaveCmd;
import com.google.common.collect.Lists;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import feign.Feign;
import feign.Logger;
import feign.Target;
import feign.form.spring.SpringFormEncoder;
import feign.slf4j.Slf4jLogger;
import io.reactivex.rxjava3.core.Single;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.collections4.multimap.HashSetValuedHashMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.cloud.openfeign.support.SpringMvcContract;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.xml.sax.SAXException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import javax.annotation.Resource;
import javax.sql.DataSource;
import javax.xml.parsers.ParserConfigurationException;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;
import java.util.logging.Level;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/3/13
 */
@Service
@Slf4j
@RefreshScope
@ConfigurationProperties(prefix = "new-goods-process-job")
public class NewGoodsDataProcessJob {
    public static final String wikiToken = "Bearer OTQ4MDkwMTc1OTc4Oi85h7YayUzOnBcF0M8Nz3Cv46W+";
    public static final Pattern PATTERN = Pattern.compile("<img +[^>]*?[^>]+>");
    public static final Pattern PATTERN1 = Pattern.compile(" src=(\"?)([^\">]+)(\")?");
    public static final Pattern PATTERN2 = Pattern.compile(" data-image-src=(\"?)([^\">]+)(\")?");
    /**
     * 计划ID
     */
    private long planId = 497L;

    @Data
    public static class ScopeExcelRow {
        @ExcelProperty(value = "商品ID")
        private String mallItemId;
        @ExcelProperty(value = "商品编码")
        private String itemCode;
        @ExcelProperty(value = "商品名称")
        private String itemName;
        @ExcelProperty(value = "店铺ID")
        private String shopId;
        @ExcelProperty(value = "店铺名称")
        private String shopName;
        @ExcelProperty(value = "品牌")
        private String brand;
        @ExcelProperty(value = "平台类目")
        private String category;
        @ExcelProperty(value = "价格")
        private String price;
        @ExcelProperty(value = "积分")
        private String point;
        @ExcelProperty(value = "销量")
        private String sales;
        @ExcelProperty(value = "创建时间")
        private String createTime;
        @ExcelProperty(value = "上架状态")
        private String status;
        @ExcelProperty(value = "上架时间")
        private String launchTime;
        @ExcelProperty(value = "24年上半年销量")
        private String sales24;
        @ExcelProperty(value = "商品中心商品名称")
        private String itemName2;
        @ExcelProperty(value = "商品中心类目")
        private String category2;
        @ExcelProperty(value = "采购负责人")
        private String buyer;
        @ExcelProperty(value = "产品负责人")
        private String principal;
        @ExcelProperty(value = "QC负责人")
        private String qc;
        @ExcelProperty(value = "P系统商品款号")
        private String partnerItemCode;
        @ExcelProperty(value = "P系统商品名称")
        private String partnerItemName;
        @ExcelProperty(value = "P系统类目（日化/食品/轻工）")
        private String partnerCategory;
    }

    @Data
    public static class RedBookExcelRow {
        @ExcelProperty(value = "itemId")
        private String itemId;
        @ExcelProperty(value = "标题")
        private String title;
        @ExcelProperty(value = "编码")
        private String code;
        @ExcelProperty(value = "结果")
        private String result;
    }

    /**
     * 商品ID	商品名称	编码	链接
     */
    @Data
    public static class DouYinExcelRow {
        @ExcelProperty(value = "商品ID")
        private String itemId;
        @ExcelProperty(value = "商品名称")
        private String title;
        @ExcelProperty(value = "编码")
        private String code;
        @ExcelProperty(value = "链接")
        private String link;
        @ExcelProperty(value = "结果")
        private String result;
    }

    /**
     * 商品ID	商品标题	编码
     */
    @Data
    public static class KuaiShouExcelRow {

        @ExcelProperty(value = "商品ID")
        private String itemId;
        @ExcelProperty(value = "商品标题")
        private String title;
        @ExcelProperty(value = "编码")
        private String code;
        @ExcelProperty(value = "结果")
        private String result;

    }

    /**
     * id	链接	编码	商品名称
     */
    @Data
    public static class TaobaoExcelRow {
        @ExcelProperty(value = "id")
        private String itemId;
        @ExcelProperty(value = "链接")
        private String link;
        @ExcelProperty(value = "商品名称")
        private String title;
        @ExcelProperty(value = "编码")
        private String code;
        @ExcelProperty(value = "结果")
        private String result;
    }

    @Autowired
    private INewGoodsService newGoodsService;
    @Autowired
    private IItemService itemService;
    @Autowired
    private IItemSkuService itemSkuService;
    @Autowired
    private FileGateway fileGateway;
    @Autowired
    private IItemLaunchPlanItemRefService itemLaunchPlanItemRefService;
    @Autowired
    private IItemDrawerService itemDrawerService;
    @Autowired
    private IItemCodeRefService itemCodeRefService;
    @Autowired
    private IJobNewGoodsProcessService jobNewGoodsProcessService;
    @Autowired
    private IDataBackupService dataBackupService;
    @Autowired
    private IItemLaunchPlanService itemLaunchPlanService;
    @Autowired
    private IItemDrawerImageService itemDrawerImageService;
    @Autowired
    private OperateLogDomainService operateLogDomainService;
    @Autowired
    private ISkuService skuService;


    public void importList(InputStream inputStream) {
        String task = "新品商品数据处理-导入";
        try {
            final List<ScopeExcelRow> objects = EasyExcel.read(inputStream)
                                                         .head(ScopeExcelRow.class)
                                                         .doReadAllSync();
            int rowIndex = 1;
            final ArrayList<JobNewGoodsProcess> jobNewGoodsProcesses = new ArrayList<>();
            int handleCount = 0;
            for (ScopeExcelRow object : objects) {
                rowIndex++;
                log.info("[{}]处理中，第{}行", task, rowIndex);
                final JobNewGoodsProcess jobNewGoodsProcess = new JobNewGoodsProcess();
                jobNewGoodsProcess.setRowId(rowIndex);
                jobNewGoodsProcess.setImportData(JSON.toJSONString(object));
                try {
                    jobNewGoodsProcess.setMallItemId(Long.parseLong(object.getMallItemId()));
                } catch (NumberFormatException ignored) {

                }
                jobNewGoodsProcess.setItemNo(object.getItemCode());
                jobNewGoodsProcess.setPartnerItemNo(object.getPartnerItemCode());
                jobNewGoodsProcesses.add(jobNewGoodsProcess);
                if (jobNewGoodsProcesses.size() >= 100) {
                    jobNewGoodsProcessService.saveBatch(jobNewGoodsProcesses);
                    handleCount += jobNewGoodsProcesses.size();
                    jobNewGoodsProcesses.clear();
                }
            }
            if (jobNewGoodsProcesses.size() > 0) {
                jobNewGoodsProcessService.saveBatch(jobNewGoodsProcesses);
                handleCount += jobNewGoodsProcesses.size();
                jobNewGoodsProcesses.clear();
            }
            log.info("[{}]处理完成，共导入{}行", task, handleCount);
        } catch (Exception e) {
            log.error("[{}]处理异常，{}", task, e.getMessage(), e);
        }
    }

    private void handleException(String task, Runnable runnable) {
        try {
            runnable.run();
        } catch (Exception e) {
            log.error("[{}]处理异常，{}", task, e.getMessage(), e);
        }
    }

    public void preHandle() {
        final String task = "新品商品数据处理-数据预处理";
        final List<JobNewGoodsProcess> list = jobNewGoodsProcessService.list();
        for (JobNewGoodsProcess jobNewGoodsProcess : list) {
            log.info("[{}]处理中，第{}行", task, jobNewGoodsProcess.getRowId());
            try {
                Long itemId = jobNewGoodsProcess.getItemId();
                if (itemId == 0) {
                    List<ItemCodeRef> codeRefs = getItemCodeRefs(
                            jobNewGoodsProcess);
                    itemId = codeRefs.stream()
                                     .sorted(Comparator.comparingInt(v -> v.getType().getValue()))
                                     .map(ItemCodeRef::getItemId)
                                     .findFirst()
                                     .orElseThrow(() -> new RuntimeException("找不到编码"));
                    jobNewGoodsProcess.setItemId(itemId);
                    final Item item = itemService.getById(itemId);
                    Assert.notNull(item, "匹配到的商品ID数据异常");
                    jobNewGoodsProcess.setItemCode(item.getCode());
                    final ItemDrawer itemDrawer = itemDrawerService.getOrCreate(itemId);
                    jobNewGoodsProcess.setDrawerId(itemDrawer.getId());
                    jobNewGoodsProcessService.updateById(jobNewGoodsProcess);
                }
            } catch (RuntimeException e) {
                log.error("[{}]处理异常，第{}行:{}", task, jobNewGoodsProcess.getRowId(), e.getMessage(), e);
                jobNewGoodsProcess.setFilterReason(e.getMessage());
                jobNewGoodsProcessService.updateById(jobNewGoodsProcess);
                jobNewGoodsProcess.setIsDel(jobNewGoodsProcess.getId());
                jobNewGoodsProcessService.removeById(jobNewGoodsProcess.getId());
            }
        }
        final Map<Long, List<JobNewGoodsProcess>> itemGroup = list
                .stream()
                .filter(v -> v.getItemId() != 0 && StringUtils.isBlank(v.getFilterReason()))
                .collect(Collectors.groupingBy(JobNewGoodsProcess::getItemId));
        final ArrayList<JobNewGoodsProcess> duplicateItems = new ArrayList<>();
        itemGroup.forEach((itemId, jobNewGoodsProcesses) -> {
            if (jobNewGoodsProcesses.size() > 1) {
                final JobNewGoodsProcess jobNewGoodsProcess = jobNewGoodsProcesses.get(0);
                for (int i = 1; i < jobNewGoodsProcesses.size(); i++) {
                    final JobNewGoodsProcess jobNewGoodsProcess1 = jobNewGoodsProcesses.get(i);
                    jobNewGoodsProcess1.setFilterReason(String.format("与第%s行数据重复",
                            jobNewGoodsProcess.getRowId()));
                    jobNewGoodsProcess.setIsDel(jobNewGoodsProcess.getId());
                    duplicateItems.add(jobNewGoodsProcess1);
                }
            }
        });
        if (!duplicateItems.isEmpty()) {
            jobNewGoodsProcessService.updateBatchById(duplicateItems);
            jobNewGoodsProcessService.removeByIdsWithTime(duplicateItems.stream()
                                                                        .map(JobNewGoodsProcess::getId)
                                                                        .collect(
                                                                                Collectors.toList()));
            log.warn("[{}]过滤重复数据完成，共{}条", task, duplicateItems.size());
        }
    }

    private List<ItemCodeRef> getItemCodeRefs(JobNewGoodsProcess jobNewGoodsProcess) {
        final ArrayList<String> codes = new ArrayList<>();
        codes.add(jobNewGoodsProcess.getItemNo());
        codes.add(jobNewGoodsProcess.getPartnerItemNo());
        return itemCodeRefService.selectByCodes(codes.stream()
                                                     .filter(StringUtils::isNotBlank)
                                                     .collect(Collectors.toList()));
    }

    public void planHandle() {
        final String job = "新品商品数据处理-上新计划处理";
        final List<JobNewGoodsProcess> list = jobNewGoodsProcessService.list();
        for (JobNewGoodsProcess jobNewGoodsProcess : list) {
            log.info("[{}]处理中，第{}行", job, jobNewGoodsProcess.getRowId());
            try {
                final Long itemId = jobNewGoodsProcess.getItemId();
                if (itemId == 0) {
                    throw new RuntimeException("商品ID未匹配");
                }
                if (jobNewGoodsProcess.getPlanHandled() > 0) {
                    log.info("[{}]已处理，第{}行", job, jobNewGoodsProcess.getRowId());
                    continue;
                }
                final ItemLaunchPlanItemRef itemLaunchPlanItemRef = itemLaunchPlanItemRefService.getItemLaunchPlanItemRef(
                        itemId);
                if (itemLaunchPlanItemRef != null) {
                    log.info("[{}]已存在上新计划，商品ID:{}, 计划ID:{}", job, itemId, itemLaunchPlanItemRef.getPlanId());
                    jobNewGoodsProcess.setPlanId(itemLaunchPlanItemRef.getPlanId());
                    jobNewGoodsProcess.setPlanHandled(2);
                    jobNewGoodsProcessService.updateById(jobNewGoodsProcess);
                    continue;
                }

                final String importData = jobNewGoodsProcess.getImportData();
                final ScopeExcelRow scopeExcelRow = JSON.parseObject(importData, ScopeExcelRow.class);

                final ItemLaunchPlanLinkItemParam itemParam = new ItemLaunchPlanLinkItemParam();
                itemParam.setItemId(itemId);
                itemParam.setItemCode(jobNewGoodsProcess.getItemCode());
                itemParam.setPrincipalUserId(0L);
                final String principal = scopeExcelRow.getPrincipal();
                if (StringUtils.isNotBlank(principal) && !principal.contains("N/A")) {
                    final StaffBrief staffBrief = StaffAssembler.INST.toStaffBrief(principal);
                    if (staffBrief != null) {
                        itemParam.setPrincipalUserId(staffBrief.getUserId());
                    }
                }
                itemParam.setRemark("新品商品数据处理-批量导入");
                itemParam.setSortNo(0);
                itemParam.setHomeCopy("");
                itemParam.setLaunchPrice("");
                itemParam.setItemType(1);
                itemParam.setShareDiskLink("");

                itemLaunchPlanService.addPlanItem(planId, Collections.singletonList(itemParam), false);
                log.info("[{}]添加商品计划完成，计划ID:{}, 商品ID:{}，产品负责人:{}",
                        job,
                        planId,
                        itemId,
                        itemParam.getPrincipalUserId());

                jobNewGoodsProcess.setPlanId(planId);
                jobNewGoodsProcess.setPlanHandled(1);
                jobNewGoodsProcessService.updateById(jobNewGoodsProcess);

            } catch (RuntimeException e) {
                log.error("[{}]处理异常，第{}行:{}", job, jobNewGoodsProcess.getRowId(), e.getMessage(), e);
                jobNewGoodsProcess.setPlanHandleLog(e.getMessage());
                jobNewGoodsProcessService.updateById(jobNewGoodsProcess);
            }
        }
    }

    public void importTaobaoExcel(InputStream inputStream) {
        final String job = "新品商品数据处理-淘宝";
        handleException(job, () -> {
            final List<TaobaoExcelRow> objects = EasyExcel.read(inputStream)
                                                          .head(TaobaoExcelRow.class)
                                                          .doReadAllSync();
            boolean error = false;
            int i = 0;
            for (TaobaoExcelRow object : objects) {
                i++;
                if (StringUtils.isBlank(object.getCode()) || StringUtils.isBlank(object.getItemId()) || StringUtils.isBlank(
                        object.getTitle()) || StringUtils.isBlank(object.getLink())) {
                    log.error("[{}]第{}行，数据不完整:{}", job, i, object);
                    error = true;
                }
            }
            if (error) {
                throw new RuntimeException("数据校验未通过");
            }
            final Map<String, List<TaobaoExcelRow>> importGroup = objects.stream()
                                                                         .collect(Collectors.groupingBy(
                                                                                 TaobaoExcelRow::getCode));
            final String outputPath = String.format("/tmp/" + job + "-%s.xlsx", DateUtil.formatNow());
            final ExcelWriter outputExcelWriter = EasyExcel.write(outputPath)
                                                           .head(TaobaoExcelRow.class)
                                                           .build();
            final WriteSheet outputWriteSheet = EasyExcel.writerSheet("sheet1").build();
            final List<JobNewGoodsProcess> list = jobNewGoodsProcessService.list();
            for (JobNewGoodsProcess jobNewGoodsProcess : list) {
                log.info("[{}]处理中，第{}行", job, jobNewGoodsProcess.getRowId());
                if (jobNewGoodsProcess.getTaobaoHandled() > 0) {
                    log.info("[{}]已处理，第{}行", job, jobNewGoodsProcess.getRowId());
                    continue;
                }
                try {
                    if (jobNewGoodsProcess.getItemId() == 0) {
                        throw new RuntimeException("商品ID未匹配");
                    }
                    if (jobNewGoodsProcess.getDrawerId() == 0) {
                        throw new RuntimeException("商品抽屉ID未匹配");
                    }

                    final List<ItemCodeRef> codeRefs = itemCodeRefService
                            .lambdaQuery().eq(ItemCodeRef::getItemId, jobNewGoodsProcess.getItemId()).list();
                    final List<String> codes = codeRefs
                            .stream()
                            .map(ItemCodeRef::getCode).collect(Collectors.toList());
                    if (StringUtils.isNotBlank(jobNewGoodsProcess.getRefSpuCode())) {
                        codes.add(jobNewGoodsProcess.getRefSpuCode());
                    }
                    codes.add(jobNewGoodsProcess.getItemNo());
                    final Optional<TaobaoExcelRow> matchRow = codes
                            .stream()
                            .map(code -> importGroup.getOrDefault(code, Collections.emptyList()))
                            .filter(v -> !v.isEmpty())
                            .map(v -> v.get(0))
                            .findFirst();
                    if (!matchRow.isPresent()) {
                        jobNewGoodsProcess.setTaobaoHandleLog("未在导入表格中找到匹配的数据");
                        jobNewGoodsProcess.setTaobaoHandled(1);
                        jobNewGoodsProcessService.updateById(jobNewGoodsProcess);
                        continue;
                    }
                    final TaobaoExcelRow taobaoExcelRow = matchRow.get();
                    final ItemDrawer itemDrawer = itemDrawerService.getByItemId(jobNewGoodsProcess.getItemId())
                                                                   .orElseThrow(() -> new RuntimeException("找不到商品抽屉"));
                    final long backupId = dataBackupService.backup(
                            toJsonString(itemDrawer), "itemDrawer", "taobao", itemDrawer.getId().toString());

                    final ItemDrawer updateItemDrawer = new ItemDrawer();
                    updateItemDrawer.setId(itemDrawer.getId());
                    updateItemDrawer.setTbTitle(taobaoExcelRow.getTitle());
                    updateItemDrawer.setTbId(taobaoExcelRow.getItemId());
                    updateItemDrawer.setTbLink(taobaoExcelRow.getLink());
                    itemDrawerService.updateById(updateItemDrawer);

                    jobNewGoodsProcess.setTaobaoHandled(1);
                    jobNewGoodsProcess.setTaobaoBackupId(backupId);
                    jobNewGoodsProcess.setTaobaoHandleLog(String.format("修改成功:%s",
                            JSON.toJSONString(updateItemDrawer)));
                    jobNewGoodsProcessService.updateById(jobNewGoodsProcess);

                    operateLogDomainService.addOperatorLog(0L,
                            OperateLogTarget.ITEM_DRAWER,
                            itemDrawer.getId(),
                            String.format("新品商品数据处理，导入淘宝链接信息，淘宝标题修改为:%s，淘宝ID修改为:%s，淘宝链接修改为:%s",
                                    taobaoExcelRow.getTitle(),
                                    taobaoExcelRow.getItemId(),
                                    taobaoExcelRow.getLink()),
                            null);

                    taobaoExcelRow.setResult(String.format("写入到抽屉:%s", itemDrawer.getId()));

                } catch (Exception e) {
                    log.error("[{}]处理异常，第{}行:{}", job, jobNewGoodsProcess.getRowId(), e.getMessage(), e);

                    jobNewGoodsProcess.setTaobaoHandleLog(e.getMessage());
                    jobNewGoodsProcessService.updateById(jobNewGoodsProcess);
                }

            }

            outputExcelWriter.write(objects, outputWriteSheet);
            outputExcelWriter.finish();
            final FileStub fileStub = fileGateway.uploadFile(UploadFileAction.ofFile(new File(outputPath)));
            log.info("[{}]处理完成，结果已写入：{}", job, fileGateway.getAuthorizedUrl(fileStub.getPath()));
        });
    }

    private static String toJsonString(Object o) {
        return JSON.toJSONString(o);
    }

    public void importRedBookExcel(InputStream inputStream) {
        final String job = "新品商品数据处理-小红书";
        handleException(job, () -> {
            final List<RedBookExcelRow> objects = EasyExcel.read(inputStream)
                                                           .head(RedBookExcelRow.class)
                                                           .sheet(1).doReadSync();
            final Map<String, List<RedBookExcelRow>> importGroup = objects.stream()
                                                                          .collect(Collectors.groupingBy(
                                                                                  RedBookExcelRow::getCode));
            final String outputPath = String.format("/tmp/" + job + "-%s.xlsx", DateUtil.formatNow());
            final ExcelWriter outputExcelWriter = EasyExcel.write(outputPath)
                                                           .head(RedBookExcelRow.class)
                                                           .build();
            final WriteSheet outputWriteSheet = EasyExcel.writerSheet("sheet1").build();
            final List<JobNewGoodsProcess> list = jobNewGoodsProcessService.list();
            for (JobNewGoodsProcess jobNewGoodsProcess : list) {
                log.info("[{}]处理中，第{}行", job, jobNewGoodsProcess.getRowId());
                if (jobNewGoodsProcess.getXiaohongshuHandled() > 0) {
                    log.info("[{}]已处理，第{}行", job, jobNewGoodsProcess.getRowId());
                    continue;
                }

                try {
                    if (jobNewGoodsProcess.getItemId() == 0) {
                        throw new RuntimeException("商品ID未匹配");
                    }
                    if (jobNewGoodsProcess.getDrawerId() == 0) {
                        throw new RuntimeException("商品抽屉ID未匹配");
                    }

                    final List<ItemCodeRef> codeRefs = itemCodeRefService
                            .lambdaQuery().eq(ItemCodeRef::getItemId, jobNewGoodsProcess.getItemId()).list();
                    if (codeRefs.isEmpty()) {
                        throw new RuntimeException("商品编码异常");
                    }
                    final Optional<RedBookExcelRow> matchRow = codeRefs
                            .stream()
                            .map(ItemCodeRef::getCode)
                            .map(code -> importGroup.getOrDefault(code, Collections.emptyList()))
                            .filter(v -> !v.isEmpty())
                            .map(v -> v.get(0))
                            .findFirst();
                    if (!matchRow.isPresent()) {
                        jobNewGoodsProcess.setXiaohongshuHandleLog("未在导入表格中找到匹配的数据");
                        jobNewGoodsProcess.setXiaohongshuHandled(1);
                        jobNewGoodsProcessService.updateById(jobNewGoodsProcess);
                        continue;
                    }
                    final RedBookExcelRow redBookExcelRow = matchRow.get();
                    final ItemDrawer itemDrawer = itemDrawerService.getByItemId(jobNewGoodsProcess.getItemId())
                                                                   .orElseThrow(() -> new RuntimeException("找不到商品抽屉"));
                    final long backupId = dataBackupService.backup(
                            toJsonString(itemDrawer), "itemDrawer", "redbook", itemDrawer.getId().toString());

                    final ItemDrawer updateItemDrawer = new ItemDrawer();
                    updateItemDrawer.setId(itemDrawer.getId());
                    updateItemDrawer.setMiniRedBookLink(String.format("https://www.xiaohongshu.com/goods-detail/%s",
                            redBookExcelRow.getItemId()));
                    updateItemDrawer.setMiniRedBookId(redBookExcelRow.getItemId());
                    itemDrawerService.updateById(updateItemDrawer);

                    jobNewGoodsProcess.setXiaohongshuHandled(1);
                    jobNewGoodsProcess.setXiaohongshuBackupId(backupId);
                    jobNewGoodsProcess.setXiaohongshuHandleLog(String.format("修改成功:%s",
                            JSON.toJSONString(updateItemDrawer)));
                    jobNewGoodsProcessService.updateById(jobNewGoodsProcess);

                    operateLogDomainService.addOperatorLog(0L,
                            OperateLogTarget.ITEM_DRAWER,
                            itemDrawer.getId(),
                            String.format("新品商品数据处理，导入小红书链接信息，小红书链接:%s，小红书ID:%s",
                                    updateItemDrawer.getMiniRedBookLink(),
                                    updateItemDrawer.getMiniRedBookId()
                            ), null);

                    redBookExcelRow.setResult(String.format("写入到抽屉:%s", itemDrawer.getId()));

                } catch (Exception e) {
                    log.error("[{}]处理异常，第{}行:{}", job, jobNewGoodsProcess.getRowId(), e.getMessage());


                    jobNewGoodsProcess.setXiaohongshuHandleLog(e.getMessage());
                    jobNewGoodsProcessService.updateById(jobNewGoodsProcess);
                }

            }

            outputExcelWriter.write(objects, outputWriteSheet);
            outputExcelWriter.finish();
            final FileStub fileStub = fileGateway.uploadFile(UploadFileAction.ofFile(new File(outputPath)));
            log.info("[{}]处理完成，结果已写入：{}", job, fileGateway.getAuthorizedUrl(fileStub.getPath()));
        });
    }

    public void importDouyinExcel(InputStream inputStream) {
        final String job = "新品商品数据处理-抖音";
        handleException(job, () -> {
            final List<DouYinExcelRow> objects = EasyExcel.read(inputStream)
                                                          .head(DouYinExcelRow.class)
                                                          .sheet(1).doReadSync();
            log.info("[{}]处理中，共{}行", job, objects.size());
            int i = 0;
            for (DouYinExcelRow object : objects) {
                i++;
                if (StringUtils.isBlank(object.getCode()) || StringUtils.isBlank(object.getItemId()) || StringUtils.isBlank(
                        object.getTitle()) || StringUtils.isBlank(object.getLink())) {
                    log.error("[{}]第{}行，数据不完整:{}", job, i, object);
                }
            }
            final Map<String, List<DouYinExcelRow>> importGroup = objects.stream()
                                                                         .filter(v -> !StringUtils.isBlank(v.getCode()))
                                                                         .collect(Collectors.groupingBy(
                                                                                 DouYinExcelRow::getCode));
            final String outputPath = String.format("/tmp/" + job + "-%s.xlsx", DateUtil.formatNow());
            final ExcelWriter outputExcelWriter = EasyExcel.write(outputPath)
                                                           .head(DouYinExcelRow.class)
                                                           .build();
            final WriteSheet outputWriteSheet = EasyExcel.writerSheet("sheet1").build();
            final List<JobNewGoodsProcess> list = jobNewGoodsProcessService.list();
            for (JobNewGoodsProcess jobNewGoodsProcess : list) {
                log.info("[{}]处理中，第{}行", job, jobNewGoodsProcess.getRowId());
                if (jobNewGoodsProcess.getDouyinHandled() > 0) {
                    log.info("[{}]已处理，第{}行", job, jobNewGoodsProcess.getRowId());
                    continue;
                }

                try {
                    if (jobNewGoodsProcess.getItemId() == 0) {
                        throw new RuntimeException("商品ID未匹配");
                    }
                    if (jobNewGoodsProcess.getDrawerId() == 0) {
                        throw new RuntimeException("商品抽屉ID未匹配");
                    }

                    final List<ItemCodeRef> codeRefs = itemCodeRefService
                            .lambdaQuery().eq(ItemCodeRef::getItemId, jobNewGoodsProcess.getItemId()).list();
                    if (codeRefs.isEmpty()) {
                        throw new RuntimeException("商品编码异常");
                    }
                    final Optional<DouYinExcelRow> matchRow = codeRefs
                            .stream()
                            .map(ItemCodeRef::getCode)
                            .map(code -> importGroup.getOrDefault(code, Collections.emptyList()))
                            .filter(v -> !v.isEmpty())
                            .map(v -> v.get(0))
                            .findFirst();
                    if (!matchRow.isPresent()) {
                        jobNewGoodsProcess.setDouyinHandleLog("未在导入表格中找到匹配的数据");
                        jobNewGoodsProcess.setDouyinHandled(1);
                        jobNewGoodsProcessService.updateById(jobNewGoodsProcess);
                        continue;
                    }
                    final DouYinExcelRow douyinExcelRow = matchRow.get();
                    final ItemDrawer itemDrawer = itemDrawerService.getByItemId(jobNewGoodsProcess.getItemId())
                                                                   .orElseThrow(() -> new RuntimeException("找不到商品抽屉"));
                    final long backupId = dataBackupService.backup(
                            toJsonString(itemDrawer), "itemDrawer", "douyin", itemDrawer.getId().toString());

                    final ItemDrawer updateItemDrawer = new ItemDrawer();
                    updateItemDrawer.setId(itemDrawer.getId());
                    updateItemDrawer.setDouLink(douyinExcelRow.getLink());
                    updateItemDrawer.setDouTitle(douyinExcelRow.getTitle());
                    updateItemDrawer.setDouId(douyinExcelRow.getItemId());
                    itemDrawerService.updateById(updateItemDrawer);

                    jobNewGoodsProcess.setDouyinHandled(1);
                    jobNewGoodsProcess.setDouyinBackupId(backupId);
                    jobNewGoodsProcess.setDouyinHandleLog(String.format("修改成功:%s",
                            JSON.toJSONString(updateItemDrawer)));
                    jobNewGoodsProcessService.updateById(jobNewGoodsProcess);

                    operateLogDomainService.addOperatorLog(0L,
                            OperateLogTarget.ITEM_DRAWER,
                            itemDrawer.getId(),
                            String.format("新品商品数据处理，导入抖音链接信息，抖音链接:%s，抖音ID:%s，抖音标题:%s",
                                    updateItemDrawer.getDouLink(),
                                    updateItemDrawer.getDouId(), updateItemDrawer.getDouTitle()
                            ), null);

                    douyinExcelRow.setResult(String.format("写入到抽屉:%s", itemDrawer.getId()));

                } catch (Exception e) {
                    log.error("[{}]处理异常，第{}行:{}", job, jobNewGoodsProcess.getRowId(), e.getMessage());

                    jobNewGoodsProcess.setDouyinHandleLog(e.getMessage());
                    jobNewGoodsProcessService.updateById(jobNewGoodsProcess);
                }

            }

            outputExcelWriter.write(objects, outputWriteSheet);
            outputExcelWriter.finish();
            final FileStub fileStub = fileGateway.uploadFile(UploadFileAction.ofFile(new File(outputPath)));
            log.info("[{}]处理完成，结果已写入：{}", job, fileGateway.getAuthorizedUrl(fileStub.getPath()));
        });
    }

    public void importKuaishouExcel(InputStream inputStream) {
        final String job = "新品商品数据处理-快手";
        handleException(job, () -> {
            final List<KuaiShouExcelRow> objects = EasyExcel.read(inputStream)
                                                            .head(KuaiShouExcelRow.class)
                                                            .sheet(1).doReadSync();
            log.info("[{}]处理中，共{}行", job, objects.size());
            int i = 0;
            for (KuaiShouExcelRow object : objects) {
                i++;
                if (StringUtils.isBlank(object.getCode()) || StringUtils.isBlank(object.getItemId()) || StringUtils.isBlank(
                        object.getTitle())) {
                    log.error("[{}]第{}行，数据不完整:{}", job, i, object);
                }
            }

            final Map<String, List<KuaiShouExcelRow>> importGroup = objects.stream()
                                                                           .filter(v -> !StringUtils.isBlank(v.getCode()))
                                                                           .collect(Collectors.groupingBy(
                                                                                   KuaiShouExcelRow::getCode));
            final String outputPath = String.format("/tmp/" + job + "-%s.xlsx", DateUtil.formatNow());
            final ExcelWriter outputExcelWriter = EasyExcel.write(outputPath)
                                                           .head(KuaiShouExcelRow.class)
                                                           .build();
            final WriteSheet outputWriteSheet = EasyExcel.writerSheet("sheet1").build();
            final List<JobNewGoodsProcess> list = jobNewGoodsProcessService.list();
            for (JobNewGoodsProcess jobNewGoodsProcess : list) {
                log.info("[{}]处理中，第{}行", job, jobNewGoodsProcess.getRowId());

                if (jobNewGoodsProcess.getKuaishouHandled() > 0) {
                    log.info("[{}]已处理，第{}行", job, jobNewGoodsProcess.getRowId());
                    continue;
                }

                try {
                    if (jobNewGoodsProcess.getItemId() == 0) {
                        throw new RuntimeException("商品ID未匹配");
                    }
                    if (jobNewGoodsProcess.getDrawerId() == 0) {
                        throw new RuntimeException("商品抽屉ID未匹配");
                    }

                    final List<ItemCodeRef> codeRefs = itemCodeRefService
                            .lambdaQuery().eq(ItemCodeRef::getItemId, jobNewGoodsProcess.getItemId()).list();
                    if (codeRefs.isEmpty()) {
                        throw new RuntimeException("商品编码异常");
                    }
                    final Optional<KuaiShouExcelRow> matchRow = codeRefs
                            .stream()
                            .map(ItemCodeRef::getCode)
                            .map(code -> importGroup.getOrDefault(code, Collections.emptyList()))
                            .filter(v -> !v.isEmpty())
                            .map(v -> v.get(0))
                            .findFirst();
                    if (!matchRow.isPresent()) {
                        jobNewGoodsProcess.setKuaishouHandleLog("未在导入表格中找到匹配的数据");
                        jobNewGoodsProcess.setKuaishouHandled(1);
                        jobNewGoodsProcessService.updateById(jobNewGoodsProcess);
                        continue;
                    }
                    final KuaiShouExcelRow kuaiShouExcelRow = matchRow.get();
                    final ItemDrawer itemDrawer = itemDrawerService.getByItemId(jobNewGoodsProcess.getItemId())
                                                                   .orElseThrow(() -> new RuntimeException("找不到商品抽屉"));
                    final long backupId = dataBackupService.backup(
                            toJsonString(itemDrawer), "itemDrawer", "kuaishou", itemDrawer.getId().toString());

                    final ItemDrawer updateItemDrawer = new ItemDrawer();
                    updateItemDrawer.setId(itemDrawer.getId());
                    updateItemDrawer.setKuaiShouLinkContent(
                            String.format("<p>标题：%s</p>" +
                                            "<p>链接：https://s.kwaixiaodian.com/zone/goods/config/release/detail?itemId=%s</p>",
                                    kuaiShouExcelRow.getTitle(),
                                    kuaiShouExcelRow.getItemId()));
                    itemDrawerService.updateById(updateItemDrawer);

                    jobNewGoodsProcess.setKuaishouHandled(1);
                    jobNewGoodsProcess.setKuaishouBackupId(backupId);
                    jobNewGoodsProcess.setKuaishouHandleLog(String.format("修改成功:%s",
                            JSON.toJSONString(updateItemDrawer)));
                    jobNewGoodsProcessService.updateById(jobNewGoodsProcess);

                    operateLogDomainService.addOperatorLog(0L,
                            OperateLogTarget.ITEM_DRAWER,
                            itemDrawer.getId(),
                            String.format("新品商品数据处理，导入快手链接信息，快手标题:%s，快手ID:%s",
                                    kuaiShouExcelRow.getTitle(),
                                    kuaiShouExcelRow.getItemId())
                            , null);

                    kuaiShouExcelRow.setResult(String.format("写入到抽屉:%s", itemDrawer.getId()));

                } catch (Exception e) {
                    log.error("[{}]处理异常，第{}行:{}", job, jobNewGoodsProcess.getRowId(), e.getMessage());

                    jobNewGoodsProcess.setKuaishouHandleLog(e.getMessage());
                    jobNewGoodsProcessService.updateById(jobNewGoodsProcess);
                }

            }

            outputExcelWriter.write(objects, outputWriteSheet);
            outputExcelWriter.finish();
            final FileStub fileStub = fileGateway.uploadFile(UploadFileAction.ofFile(new File(outputPath)));
            log.info("[{}]处理完成，结果已写入：{}", job, fileGateway.getAuthorizedUrl(fileStub.getPath()));
        });
    }

    @Data
    public static class ImageExcelRow {
        @ExcelProperty("id")
        private String id;
        @ExcelProperty("image")
        private String image;
        @ExcelProperty("description_images")
        private String descriptionImages;
        @ExcelProperty("base_detail")
        private String baseDetail;

    }

    @Data
    public static class Image {
        String fileName;
        String thumbnail;
        String url;
        String width;
    }

    @Data
    public static class WatermarkImageExcelRow {
        @ExcelProperty("id")
        private String id;
        @ExcelProperty("item_id")
        private String itemId;
        @ExcelProperty("image")
        private String image;
        @ExcelProperty("watermark_image")
        private String watermarkImage;
        @ExcelProperty("status")
        private String status;
        @ExcelProperty("startTime")
        private String startTime;
        @ExcelProperty("endTime")
        private String endTime;

    }

    public void watermarkHandle(InputStream inputStream) {
        final String job = "新品商品数据处理-水印图片";
        handleException(job, () -> {
            final List<WatermarkImageExcelRow> objects = EasyExcel.read(inputStream)
                                                                  .head(WatermarkImageExcelRow.class)
                                                                  .doReadAllSync();
            final HashSetValuedHashMap<Long, WatermarkImageExcelRow> objectGroup = new HashSetValuedHashMap<>();
            for (WatermarkImageExcelRow object : objects) {
                try {
                    objectGroup.put(Long.parseLong(object.getItemId()), object);
                } catch (NumberFormatException ignored) {
                }
            }

            final List<JobNewGoodsProcess> list = jobNewGoodsProcessService.list();
            for (JobNewGoodsProcess jobNewGoodsProcess : list) {
                log.info("[{}]处理中，第{}行", job, jobNewGoodsProcess.getRowId());

                if (jobNewGoodsProcess.getWatermarkImgHandled() > 0) {
                    log.info("[{}]已处理，第{}行", job, jobNewGoodsProcess.getRowId());
                    continue;
                }
                try {
                    final Long itemId = jobNewGoodsProcess.getItemId();
                    if (itemId == 0) {
                        throw new RuntimeException("商品ID未匹配");
                    }
                    final Long drawerId = jobNewGoodsProcess.getDrawerId();
                    if (drawerId == 0) {
                        throw new RuntimeException("商品抽屉ID未匹配");
                    }

                    final Long mallItemId = jobNewGoodsProcess.getMallItemId();
                    if (mallItemId == null || mallItemId == 0) {
                        throw new RuntimeException("未提供小程序ID");
                    }

                    final WatermarkImageExcelRow watermarkImageExcelRow =
                            Optional.ofNullable(objectGroup.get(mallItemId))
                                    .orElseGet(Collections::emptySet)
                                    .stream()
                                    .filter(v -> StringUtils.isNotBlank(v.getImage()))
                                    .max(Comparator.comparing(WatermarkImageExcelRow::getId))
                                    .orElseThrow(() -> new RuntimeException("未找到对应的水印原图"));

                    final Image originalImage = JSON.parseObject(watermarkImageExcelRow.getImage(), Image.class);
                    if (originalImage == null) {
                        throw new RuntimeException("水印原图反序列化异常");
                    }

                    final List<ItemDrawerImage> itemDrawerImages = itemDrawerImageService
                            .lambdaQuery()
                            .eq(ItemDrawerImage::getDrawerId, drawerId).list();
                    final List<ItemDrawerImage> images = itemDrawerImages.stream()
                                                                         .filter(v -> v.getType() == ItemDrawerImageTypeEnum.ITEM)
                                                                         .collect(Collectors.toList());
                    if (images.isEmpty()) {
                        throw new RuntimeException("商品主图为空");
                    }
                    final ItemDrawerImage mainImage = images.stream()
                                                            .min(Comparator.comparing(ItemDrawerImage::getSort))
                                                            .get();
                    final long backupId = dataBackupService.backup(
                            toJsonString(images), "itemDrawer", "images", drawerId.toString());

                    itemDrawerImageService.removeById(mainImage.getId());

                    final ItemDrawerImage newMainImage = new ItemDrawerImage();
                    newMainImage.setFilename(originalImage.getFileName());
                    newMainImage.setDrawerId(drawerId);
                    newMainImage.setUrl(originalImage.getUrl());
                    newMainImage.setType(ItemDrawerImageTypeEnum.ITEM);
                    newMainImage.setFileType(1);
                    newMainImage.setExt(FileNameUtil.getSuffix(originalImage.getUrl()));
                    newMainImage.setSort(1L);
                    itemDrawerImageService.save(newMainImage);

                    jobNewGoodsProcess.setWatermarkImgHandled(1);
                    jobNewGoodsProcess.setWatermarkImgBackupId(backupId);
                    final String handleLog = String.format("水印图片已处理，%s 替换为 %s",
                            mainImage.getUrl(),
                            newMainImage.getUrl());
                    jobNewGoodsProcess.setWatermarkImgHandleLog(handleLog);
                    jobNewGoodsProcessService.updateById(jobNewGoodsProcess);

                    operateLogDomainService.addOperatorLog(0L,
                            OperateLogTarget.ITEM_DRAWER,
                            drawerId,
                            "新品商品数据处理，" + handleLog, null);

                } catch (RuntimeException e) {
                    log.error("[{}]处理异常，第{}行:{}", job, jobNewGoodsProcess.getRowId(), e.getMessage());

                    jobNewGoodsProcess.setWatermarkImgHandled(2);
                    jobNewGoodsProcess.setWatermarkImgHandleLog(e.getMessage());
                    jobNewGoodsProcessService.updateById(jobNewGoodsProcess);
                }
            }
        });
    }

    public void importImageExcel(InputStream inputStream) {
        final String job = "新品商品数据处理-图片";
        handleException(job, () -> {
            final List<ImageExcelRow> objects = EasyExcel.read(inputStream)
                                                         .head(ImageExcelRow.class)
                                                         .doReadAllSync();
            final Map<Long, ImageExcelRow> objectMap = new HashMap<>();
            for (ImageExcelRow object : objects) {
                try {
                    objectMap.put(Long.parseLong(object.getId()), object);
                } catch (NumberFormatException ignored) {
                }
            }

            final List<JobNewGoodsProcess> list = jobNewGoodsProcessService.list();
            for (JobNewGoodsProcess jobNewGoodsProcess : list) {
                log.info("[{}]处理中，第{}行", job, jobNewGoodsProcess.getRowId());

                if (jobNewGoodsProcess.getImgHandled() > 0) {
                    log.info("[{}]已处理，第{}行", job, jobNewGoodsProcess.getRowId());
                    continue;
                }
                try {
                    final Long itemId = jobNewGoodsProcess.getItemId();
                    if (itemId == 0) {
                        throw new RuntimeException("商品ID未匹配");
                    }
                    final Long drawerId = jobNewGoodsProcess.getDrawerId();
                    if (drawerId == 0) {
                        throw new RuntimeException("商品抽屉ID未匹配");
                    }

                    final Long mallItemId = jobNewGoodsProcess.getMallItemId();
                    if (mallItemId == null || mallItemId == 0) {
                        throw new RuntimeException("未提供小程序ID");
                    }

                    final ImageExcelRow imageExcelRow = objectMap.get(mallItemId);
                    if (imageExcelRow == null) {
                        throw new RuntimeException("未找到对应的小程序图片");
                    }

                    final ArrayList<Image> mainImages = new ArrayList<>();
                    final ArrayList<Image> detailImages = new ArrayList<>();
//                    final String image = imageExcelRow.getImage();
//                    if (StringUtils.isNotBlank(image)) {
//                        final Image imageObj = JSON.parseObject(image, Image.class);
//                        if (imageObj != null) {
//                            mainImages.add(imageObj);
//                        }
//                    }
                    final String descriptionImages = imageExcelRow.getDescriptionImages();
                    if (StringUtils.isNotBlank(descriptionImages)) {
                        final List<Image> images = JSON.parseArray(descriptionImages, Image.class);
                        if (images != null && images.size() > 0) {
                            mainImages.addAll(images);
                        }
                    }
                    final String baseDetail = imageExcelRow.getBaseDetail();
                    if (StringUtils.isNotBlank(baseDetail)) {
                        final List<String> imageUrls = matchImageUrl(baseDetail);
                        if (imageUrls.size() > 0) {
                            final ArrayList<Image> images = new ArrayList<>();
                            for (int i = 0; i < imageUrls.size(); i++) {
                                final String url = imageUrls.get(i);
                                final Image imageObj = new Image();
                                imageObj.setUrl(url);
                                imageObj.setFileName("详情" + (i + 1));
                                images.add(imageObj);
                            }
                            detailImages.addAll(images);
                        }
                    }

                    final List<ItemDrawerImage> itemDrawerImages = itemDrawerImageService
                            .lambdaQuery()
                            .eq(ItemDrawerImage::getDrawerId, drawerId).list();
                    final List<ItemDrawerImage> images = itemDrawerImages.stream()
                                                                         .filter(v -> v.getType() == ItemDrawerImageTypeEnum.ITEM || v.getType() == ItemDrawerImageTypeEnum.DETAIL)
                                                                         .collect(
                                                                                 Collectors.toList());

                    final long backupId = dataBackupService.backup(
                            toJsonString(images), "itemDrawer", "images", drawerId.toString());

                    final List<Long> imageIds = images.stream().map(Entity::getId).collect(Collectors.toList());
                    itemDrawerImageService.removeByIds(imageIds);

                    final ArrayList<ItemDrawerImage> itemDrawerImagesNew = new ArrayList<>();
                    if (!mainImages.isEmpty()) {
                        long i = 0;
                        for (Image mainImage : mainImages) {
                            i++;
                            final ItemDrawerImage itemDrawerImage = new ItemDrawerImage();
                            itemDrawerImage.setFilename(mainImage.getFileName());
                            itemDrawerImage.setDrawerId(drawerId);
                            itemDrawerImage.setUrl(mainImage.getUrl());
                            itemDrawerImage.setType(ItemDrawerImageTypeEnum.ITEM);
                            itemDrawerImage.setFileType(1);
                            itemDrawerImage.setExt(FileNameUtil.getSuffix(mainImage.getUrl()));
                            itemDrawerImage.setSort(i);
                            itemDrawerImagesNew.add(itemDrawerImage);
                        }
                    }
                    if (!detailImages.isEmpty()) {
                        long i = 0;
                        for (Image detailImage : detailImages) {
                            i++;
                            final ItemDrawerImage itemDrawerImage = new ItemDrawerImage();
                            itemDrawerImage.setFilename(detailImage.getFileName());
                            itemDrawerImage.setDrawerId(drawerId);
                            itemDrawerImage.setUrl(detailImage.getUrl());
                            itemDrawerImage.setType(ItemDrawerImageTypeEnum.DETAIL);
                            itemDrawerImage.setFileType(1);
                            itemDrawerImage.setExt(FileNameUtil.getSuffix(detailImage.getUrl()));
                            itemDrawerImage.setSort(i);
                            itemDrawerImagesNew.add(itemDrawerImage);
                        }
                    }
                    itemDrawerImageService.saveBatch(itemDrawerImagesNew);

                    jobNewGoodsProcess.setImgHandled(1);
                    jobNewGoodsProcess.setImgBackupId(backupId);
                    final String handleLog = String.format("图片已处理，删除已存在图片：%s，插入新的图片，主图：%s，详情图：%s",
                            imageIds.size(),
                            mainImages.size(),
                            detailImages.size());
                    jobNewGoodsProcess.setImgHandleLog(handleLog);
                    jobNewGoodsProcessService.updateById(jobNewGoodsProcess);

                    operateLogDomainService.addOperatorLog(0L,
                            OperateLogTarget.ITEM_DRAWER,
                            drawerId,
                            "新品商品数据处理，导入小程序商品图片，" + handleLog, null);

                } catch (RuntimeException e) {
                    log.error("[{}]处理异常，第{}行:{}", job, jobNewGoodsProcess.getRowId(), e.getMessage());

                    jobNewGoodsProcess.setImgHandleLog(e.getMessage());
                    jobNewGoodsProcessService.updateById(jobNewGoodsProcess);
                }
            }
        });
    }

    public void importImageExcel2(InputStream inputStream) {
        final String job = "新品商品数据处理-图片";
        handleException(job, () -> {
            final List<ImageExcelRow> objects = EasyExcel.read(inputStream)
                                                         .head(ImageExcelRow.class)
                                                         .doReadAllSync();
            final Map<Long, ImageExcelRow> objectMap = new HashMap<>();
            for (ImageExcelRow object : objects) {
                try {
                    objectMap.put(Long.parseLong(object.getId()), object);
                } catch (NumberFormatException ignored) {
                }
            }

            final List<JobNewGoodsProcess> list = jobNewGoodsProcessService.list();
            for (JobNewGoodsProcess jobNewGoodsProcess : list) {
                log.info("[{}]处理中，第{}行", job, jobNewGoodsProcess.getRowId());

                if (jobNewGoodsProcess.getImgHandled2() > 0) {
                    log.info("[{}]已处理，第{}行", job, jobNewGoodsProcess.getRowId());
                    continue;
                }
                try {
                    final Long itemId = jobNewGoodsProcess.getItemId();
                    if (itemId == 0) {
                        throw new RuntimeException("商品ID未匹配");
                    }
                    final Long drawerId = jobNewGoodsProcess.getDrawerId();
                    if (drawerId == 0) {
                        throw new RuntimeException("商品抽屉ID未匹配");
                    }

                    final Long mallItemId = jobNewGoodsProcess.getMallItemId();
                    if (mallItemId == null || mallItemId == 0) {
                        throw new RuntimeException("未提供小程序ID");
                    }

                    final ImageExcelRow imageExcelRow = objectMap.get(mallItemId);
                    if (imageExcelRow == null) {
                        throw new RuntimeException("未找到对应的小程序图片");
                    }

                    final String baseDetail = imageExcelRow.getBaseDetail();
                    if (StringUtils.isBlank(baseDetail)) {
                        throw new RuntimeException("小程序详情为空");
                    }

                    final ArrayList<Image> detailImages = new ArrayList<>();
                    final List<String> imageUrls = getImgUrls2(baseDetail);
                    if (imageUrls.size() > 0) {
                        final ArrayList<Image> images = new ArrayList<>();
                        for (int i = 0; i < imageUrls.size(); i++) {
                            final String url = imageUrls.get(i);
                            final Image imageObj = new Image();
                            imageObj.setUrl(url);
                            imageObj.setFileName("详情" + (i + 1));
                            images.add(imageObj);
                        }
                        detailImages.addAll(images);
                    }

                    if (detailImages.isEmpty()) {
                        throw new RuntimeException("提取小程序图片数量为0");
                    }

                    final ArrayList<ItemDrawerImage> itemDrawerImagesNew = new ArrayList<>();
                    long i = 0;
                    for (Image detailImage : detailImages) {
                        i++;
                        final ItemDrawerImage itemDrawerImage = new ItemDrawerImage();
                        itemDrawerImage.setFilename(detailImage.getFileName());
                        itemDrawerImage.setDrawerId(drawerId);
                        itemDrawerImage.setUrl(detailImage.getUrl());
                        itemDrawerImage.setType(ItemDrawerImageTypeEnum.DETAIL);
                        itemDrawerImage.setFileType(1);
                        itemDrawerImage.setExt(FileNameUtil.getSuffix(detailImage.getUrl()));
                        itemDrawerImage.setSort(i);
                        itemDrawerImagesNew.add(itemDrawerImage);
                    }

                    final List<ItemDrawerImage> images = itemDrawerImageService
                            .lambdaQuery()
                            .eq(ItemDrawerImage::getType, ItemDrawerImageTypeEnum.DETAIL)
                            .eq(ItemDrawerImage::getDrawerId, drawerId).list();

                    itemDrawerImageService.saveBatch(itemDrawerImagesNew);

                    final List<Long> imageIds = images.stream().map(Entity::getId).collect(Collectors.toList());
                    long backupId = 0;
                    if (!images.isEmpty()) {
                        backupId = dataBackupService.backup(
                                toJsonString(images), "itemDrawer", "detailImages", drawerId.toString());

                        itemDrawerImageService.removeByIds(imageIds);
                    }

                    jobNewGoodsProcess.setImgHandled2(1);
                    jobNewGoodsProcess.setImgBackupId2(backupId);
                    final String handleLog = String.format("图片已处理，删除已存在图片：%s，插入新的图片，详情图：%s",
                                                           imageIds.size(),
                                                           detailImages.size());
                    jobNewGoodsProcess.setImgHandleLog2(handleLog);
                    jobNewGoodsProcessService.updateById(jobNewGoodsProcess);

                    operateLogDomainService.addOperatorLog(0L,
                                                           OperateLogTarget.ITEM_DRAWER,
                                                           drawerId,
                                                           "新品商品数据处理，导入小程序商品图片，" + handleLog, null);

                } catch (RuntimeException e) {
                    log.error("[{}]处理异常，第{}行:{}", job, jobNewGoodsProcess.getRowId(), e.getMessage());

                    jobNewGoodsProcess.setImgHandleLog2(e.getMessage());
                    jobNewGoodsProcessService.updateById(jobNewGoodsProcess);
                }
            }
        });
    }

    @Data
    public static class RecoverCmd {
        String itemCode;
        String type;
    }

    @Data
    public static class RecoverBatchCmd {
        List<String> itemCodes;
        String type;
    }

    public void recoverBatch(RecoverBatchCmd cmd) {
        for (String itemCode : cmd.getItemCodes()) {
            try {
                final RecoverCmd cmd1 = new RecoverCmd();
                cmd1.setItemCode(itemCode);
                cmd1.setType(cmd.getType());

                final String recoverResult = recover(cmd1);
                log.info("新品商品数据处理-恢复数据，{}:{}", itemCode, recoverResult);
            } catch (Exception e) {
                log.error("新品商品数据处理-恢复数据，{}:{}", itemCode, e.getMessage(), e);
            }
        }
    }

    public String recover(RecoverCmd cmd) {
        final String type = cmd.getType();
        final String itemCode = cmd.getItemCode();
        final Item item = itemService.getByMixedCode(itemCode);
        if (item == null) {
            return "商品不存在";
        }
        final List<JobNewGoodsProcess> list = jobNewGoodsProcessService
                .lambdaQuery()
                .eq(JobNewGoodsProcess::getItemCode, itemCode).list();
        for (JobNewGoodsProcess jobNewGoodsProcess : list) {
            final Long drawerId = jobNewGoodsProcess.getDrawerId();
            final ItemDrawer drawer = itemDrawerService.getById(drawerId);
            if (drawer == null) {
                log.error("新品商品数据处理，恢复数据失败，抽屉不存在，itemCode:{}, drawerId:{}", itemCode, drawerId);
                continue;
            }
            boolean update = false;
            if (StringUtils.isBlank(type) || type.equals("小红书")) {
                final Long xiaohongshuBackupId = jobNewGoodsProcess.getXiaohongshuBackupId();
                if (xiaohongshuBackupId != 0) {
                    update = true;
                    final DataBackup backup = dataBackupService.getById(xiaohongshuBackupId);
                    final ItemDrawer backupDrawer = JSON.parseObject(backup.getBackupData(), ItemDrawer.class);
                    drawer.setMiniRedBookId(backupDrawer.getMiniRedBookId());
                    drawer.setMiniRedBookLink(backupDrawer.getMiniRedBookLink());
                    operateLogDomainService.addOperatorLog(0L,
                            OperateLogTarget.ITEM_DRAWER,
                            drawerId,
                            "新品商品数据处理，还原小红书链接信息备份", null);
                    log.info("新品商品数据处理，还原小红书链接信息备份，itemCode:{}，抽屉ID:{}，备份ID:{}",
                            itemCode, drawerId,
                            xiaohongshuBackupId);
                }
            }
            if (StringUtils.isBlank(type) || type.equals("淘宝")) {
                final Long taobaoBackupId = jobNewGoodsProcess.getTaobaoBackupId();
                if (taobaoBackupId != 0) {
                    update = true;
                    final DataBackup backup = dataBackupService.getById(taobaoBackupId);
                    final ItemDrawer backupDrawer = JSON.parseObject(backup.getBackupData(), ItemDrawer.class);
                    drawer.setTbId(backupDrawer.getTbId());
                    drawer.setTbLink(backupDrawer.getTbLink());
                    drawer.setTbTitle(backupDrawer.getTbTitle());
                    operateLogDomainService.addOperatorLog(0L,
                            OperateLogTarget.ITEM_DRAWER,
                            drawerId,
                            "新品商品数据处理，还原淘宝链接信息备份", null);
                    log.info("新品商品数据处理，还原淘宝链接信息备份，itemCode:{}，抽屉ID:{}，备份ID:{}",
                            itemCode,
                            drawerId,
                            taobaoBackupId);

                }
            }
            if (StringUtils.isBlank(type) || type.equals("抖音")) {
                final Long douyinBackupId = jobNewGoodsProcess.getDouyinBackupId();
                if (douyinBackupId != 0) {
                    update = true;
                    final DataBackup backup = dataBackupService.getById(douyinBackupId);
                    final ItemDrawer backupDrawer = JSON.parseObject(backup.getBackupData(), ItemDrawer.class);
                    drawer.setDouId(backupDrawer.getDouId());
                    drawer.setDouLink(backupDrawer.getDouLink());
                    drawer.setDouTitle(backupDrawer.getDouTitle());
                    operateLogDomainService.addOperatorLog(0L,
                            OperateLogTarget.ITEM_DRAWER,
                            drawerId,
                            "新品商品数据处理，还原抖音链接信息备份", null);
                    log.info("新品商品数据处理，还原抖音链接信息备份，itemCode:{}，抽屉ID:{}，备份ID:{}",
                            itemCode,
                            drawerId,
                            douyinBackupId);
                }
            }
            if (StringUtils.isBlank(type) || type.equals("快手")) {
                final Long kuaishouBackupId = jobNewGoodsProcess.getKuaishouBackupId();
                if (kuaishouBackupId != 0) {
                    update = true;
                    final DataBackup backup = dataBackupService.getById(kuaishouBackupId);
                    final ItemDrawer backupDrawer = JSON.parseObject(backup.getBackupData(), ItemDrawer.class);
                    drawer.setKuaiShouLinkContent(backupDrawer.getKuaiShouLinkContent());
                    operateLogDomainService.addOperatorLog(0L,
                            OperateLogTarget.ITEM_DRAWER,
                            drawerId,
                            "新品商品数据处理，还原快手链接信息备份", null);
                    log.info("新品商品数据处理，还原快手链接信息备份，itemCode:{}，抽屉ID:{}，备份ID:{}",
                            itemCode,
                            drawerId,
                            kuaishouBackupId);
                }
            }
            if (update) {
                itemDrawerService.updateById(drawer);
                log.info("新品商品数据处理，还原抽屉，商品编码:{}，抽屉ID:{}，还原数据: {}", itemCode, drawerId, drawer);
            }
            if (StringUtils.isBlank(type) || type.equals("图片")) {
                final Long imgBackupId = jobNewGoodsProcess.getImgBackupId();
                if (imgBackupId != 0) {
                    final DataBackup backup = dataBackupService.getById(imgBackupId);
                    final List<ItemDrawerImage> itemDrawerImages = itemDrawerImageService
                            .lambdaQuery()
                            .eq(ItemDrawerImage::getDrawerId, drawerId)
                            .in(ItemDrawerImage::getType,
                                    Arrays.asList(
                                            ItemDrawerImageTypeEnum.DETAIL,
                                            ItemDrawerImageTypeEnum.ITEM))
                            .list();
                    final List<Long> imageIds = itemDrawerImages.stream()
                                                                .map(Entity::getId)
                                                                .collect(Collectors.toList());
                    itemDrawerImageService.removeByIds(imageIds);
                    log.info("新品商品数据处理，商品编码:{}，抽屉ID:{}，删除图片: {}", itemCode, drawerId, imageIds);

                    final List<ItemDrawerImage> backupImages = JSON.parseArray(backup.getBackupData(),
                            ItemDrawerImage.class);
                    for (ItemDrawerImage backupImage : backupImages) {
                        backupImage.setId(null);
                    }
                    itemDrawerImageService.saveBatch(backupImages);
                    log.info("新品商品数据处理，商品编码:{}，抽屉ID:{}，还原图片: {}",
                            itemCode,
                            drawerId,
                            backupImages.size());

                    operateLogDomainService.addOperatorLog(0L,
                            OperateLogTarget.ITEM_DRAWER,
                            drawerId,
                            String.format("新品商品数据处理，还原图片，删除:%s，还原:%s",
                                    imageIds.size(),
                                    backupImages.size()), null);
                }
            }
            if (StringUtils.isBlank(type) || type.equals("图片2")) {
                final Long imgBackupId = jobNewGoodsProcess.getImgBackupId();
                if (imgBackupId != 0) {
                    final DataBackup backup = dataBackupService.getById(imgBackupId);
                    final List<ItemDrawerImage> itemDrawerImages = itemDrawerImageService
                            .lambdaQuery()
                            .eq(ItemDrawerImage::getDrawerId, drawerId)
                            .in(ItemDrawerImage::getType,
                                Arrays.asList(
                                        ItemDrawerImageTypeEnum.DETAIL))
                            .list();
                    final List<Long> imageIds = itemDrawerImages.stream()
                                                                .map(Entity::getId)
                                                                .collect(Collectors.toList());
                    if (!imageIds.isEmpty()) {
                        itemDrawerImageService.removeByIds(imageIds);
                        log.info("新品商品数据处理，商品编码:{}，抽屉ID:{}，删除图片: {}", itemCode, drawerId, imageIds);
                    }

                    final List<ItemDrawerImage> backupImages = JSON.parseArray(backup.getBackupData(),
                                                                               ItemDrawerImage.class);
                    if (!backupImages.isEmpty()) {
                        for (ItemDrawerImage backupImage : backupImages) {
                            backupImage.setId(null);
                        }
                        itemDrawerImageService.saveBatch(backupImages);
                    }
                    log.info("新品商品数据处理，商品编码:{}，抽屉ID:{}，还原图片: {}",
                             itemCode,
                             drawerId,
                             backupImages.size());
                    operateLogDomainService.addOperatorLog(0L,
                                                           OperateLogTarget.ITEM_DRAWER,
                                                           drawerId,
                                                           String.format("新品商品数据处理，还原图片，删除:%s，还原:%s",
                                                                         imageIds.size(),
                                                                         backupImages.size()), null);
                }
            }
        }
        return "success";
    }

    private List<String> matchImageUrl(String liveVerbalTrick) {
        final Pattern pattern = PatternPool.get(
                "(?:https://|http://)([\\w-]+\\.)+[\\w-]+(:\\d+)*(/[\\w- ./?%&=]*)?");
        final Matcher matcher = pattern.matcher(liveVerbalTrick);
        final StringBuffer sb = new StringBuffer();
        List<String> imageUrlList = Lists.newArrayList();
        while (matcher.find()) {
            matcher.appendReplacement(sb, "");
            final String url = matcher.group(0);
            final String suffix = FileNameUtil.getSuffix(url);
            if (!Arrays.asList("jpg", "jpeg", "png").contains(suffix)) {
                continue;
            }
            imageUrlList.add(url);
        }
        matcher.appendTail(sb);
        return imageUrlList;
    }

    @Autowired
    private ItemDrawerMergeBizService itemDrawerMergeBizService;

    public void mergeHandle() {
        final List<JobNewGoodsProcess> list = jobNewGoodsProcessService.lambdaQuery()
                                                                       .ne(JobNewGoodsProcess::getItemId, 0)
                                                                       .eq(JobNewGoodsProcess::getMerged, 0)
                                                                       .list();
        final HashSetValuedHashMap<String, JobNewGoodsProcess> mergeGroup = new HashSetValuedHashMap<>();
        for (JobNewGoodsProcess jobNewGoodsProcess : list) {
            if (StringUtils.isBlank(jobNewGoodsProcess.getItemCode())) {
                jobNewGoodsProcess.setMerged(3);
                jobNewGoodsProcess.setMergeLog("商品编码空");
                jobNewGoodsProcessService.updateById(jobNewGoodsProcess);
                continue;
            }
            final Sku sku = skuService.lambdaQuery().eq(Sku::getSkuCode, jobNewGoodsProcess.getItemCode()).one();
            if (sku == null) {
                jobNewGoodsProcess.setMerged(3);
                jobNewGoodsProcess.setMergeLog("商品参照表未查询到");
                jobNewGoodsProcessService.updateById(jobNewGoodsProcess);
                continue;
            }
            mergeGroup.put(sku.getItemCode(), jobNewGoodsProcess);
        }
        mergeGroup.asMap().forEach((itemCode, jobNewGoodsProcessList) -> {
            if (jobNewGoodsProcessList.size() > 1) {
                final List<JobNewGoodsProcess> sortList = jobNewGoodsProcessList
                        .stream()
                        .sorted(Comparator.comparing(JobNewGoodsProcess::getItemCode))
                        .collect(Collectors.toList());
                final JobNewGoodsProcess mergeTo = sortList.get(0);
                List<Long> itemIds = new ArrayList<>();
                for (JobNewGoodsProcess newGoodsProcess : sortList) {
                    itemIds.add(newGoodsProcess.getItemId());
                }
                final Long itemId = mergeTo.getItemId();
                final Long drawerId = mergeTo.getDrawerId();
                try {
                    itemDrawerMergeBizService.doMerge(drawerId, itemId, itemIds);
                    jobNewGoodsProcessList.forEach(jobNewGoodsProcess -> {
                        jobNewGoodsProcess.setMerged(1);
                        jobNewGoodsProcess.setMergeTo(mergeTo.getItemCode());
                        jobNewGoodsProcess.setMergeLog("合并完成");
                    });
                    jobNewGoodsProcessService.updateBatchById(jobNewGoodsProcessList);
                } catch (Exception e) {
                    log.error("合并异常，itemId:{}，itemIds:{}", itemId, itemIds, e);
                    jobNewGoodsProcessList.forEach(jobNewGoodsProcess -> {
                        jobNewGoodsProcess.setMerged(3);
                        jobNewGoodsProcess.setMergeLog("合并异常:" + e.getMessage());
                    });
                    jobNewGoodsProcessService.updateBatchById(jobNewGoodsProcessList);
                }
            } else {
                jobNewGoodsProcessList.forEach(jobNewGoodsProcess -> {
                    jobNewGoodsProcess.setMerged(2);
                    jobNewGoodsProcess.setMergeLog("无需合并");
                });
                jobNewGoodsProcessService.updateBatchById(jobNewGoodsProcessList);
            }
        });
    }

    @Autowired
    ItemDrawerAppServiceImpl itemDrawerAppService;


    @Resource
    private ItemDrawerAuditBizService itemDrawerAuditBizService;

    @Autowired
    private ItemBizService itemBizService;

    public void statusHandle() {
        final List<JobNewGoodsProcess> list = jobNewGoodsProcessService.list();
        for (JobNewGoodsProcess jobNewGoodsProcess : list) {
            final Long itemId = jobNewGoodsProcess.getItemId();
            if (itemId == 0) {
                continue;
            }
            final Long drawerId = jobNewGoodsProcess.getDrawerId();
            if (drawerId == 0) {
                continue;
            }
            try {
                final List<ItemDrawerModuleId> itemDrawerModuleIds = Lists.newArrayList(
                        ItemDrawerModuleId.IMG_INFO,
                        ItemDrawerModuleId.TEXT_AND_ATTR,
                        ItemDrawerModuleId.DETAILS
                );
                itemDrawerAuditBizService.createProcess(ItemAuditType.ITEM_MATERIAL, 0L, itemId,
                        itemDrawerModuleIds.toArray(new ItemDrawerModuleId[0]));
                itemService.lambdaUpdate()
                           .eq(Entity::getId, itemId)
                           .set(Item::getLaunchStatus, ItemLaunchStatus.TO_BE_AUDITED.getValue())
                           .set(Item::getAuditStatus, ItemAuditStatus.WAIT_LEGAL_AUDIT.getValue())
                           .update();
                log.info("新品商品数据处理，发起审核成功，itemId:{}，drawerId:{}", itemId, drawerId);
            } catch (Exception e) {
                log.error("新品商品数据处理，发起审核异常，itemId:{},drawerId:{}", itemId, drawerId, e);
            }
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Content {
        private Long contentid;
        private String title;
        private List<String> codes;
        private String body;
        private List<String> imageUrls;
    }

    public static List<Content> queryPageList(NamedParameterJdbcTemplate namedParameterJdbcTemplate, List<Long> pids) {
        final HashMap<String, Object> params = new HashMap<>();
        params.put("pids", pids);
        final String contentListSql = "SELECT `CONTENTID`,`TITLE` FROM `CONTENT` WHERE `PARENTID` IN (:pids) AND `CONTENT_STATUS` = 'current' AND `CONTENTTYPE` = 'PAGE';";
        return namedParameterJdbcTemplate.query(contentListSql, params, new BeanPropertyRowMapper<>(Content.class));
    }

    @Autowired
    WikiClient wikiClient;

    public static String getBodyContent(WikiClient client, Long id) {
        final HashMap<String, String> params = new HashMap<>();
        params.put("expand", "body.editor");
        String contentBody = client.getContentBody(wikiToken, id, params);
        if (StringUtils.isEmpty(contentBody)) {
            throw new RuntimeException("获取WIKI内容异常，返回空");
        }
        final JSONObject jsonObject = JSON.parseObject(contentBody);
        return Optional.ofNullable(jsonObject)
                       .map(v -> v.getJSONObject("body"))
                       .map(v -> v.getJSONObject("editor"))
                       .map(v -> v.getString("value"))
                       .orElseThrow(() -> new RuntimeException("获取WIKI内容异常"));
    }


    @Autowired
    IJobHandleService jobHandleService;
    @Autowired
    IJobHandleLogService jobHandleLogService;

    @Data
    public static class TrainingMaterialsHandle {
        @ExcelProperty("采购品名")
        String itemName;

        @ExcelProperty("商品编码")
        String itemCode;

        @ExcelProperty("商品参照库/SPU编码")
        String refSpuCode;

        @ExcelProperty("VIKI编码是否已加*")
        String wikiCodeAdded;

        @ExcelProperty("VIKI培训资料是否缺失*")
        String wikiMissing;

        @ExcelProperty("备注")
        String remark;

    }

    public void trainingMaterialsHandleImport(InputStream inputStream) {
        final SyncReadListener syncReadListener = new SyncReadListener() {

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {

                final ArrayList<JobHandle> jobHandles = new ArrayList<>(100);
                final Integer sheetNo = context.readSheetHolder().getSheetNo();
                int i = 0;
                for (Object item : getList()) {
                    assert item instanceof TrainingMaterialsHandle;
                    final TrainingMaterialsHandle object = (TrainingMaterialsHandle) item;
                    i++;
                    final JobHandle jobHandle = new JobHandle();
                    jobHandle.setJob("新品商品数据处理-培训资料处理");
                    jobHandle.setScope(String.valueOf(sheetNo));
                    jobHandle.setRecId(String.valueOf(i));
                    jobHandle.setV1(object.getItemName());
                    jobHandle.setV2(object.getItemCode());
                    jobHandle.setV3(object.getRefSpuCode());
                    jobHandle.setV4(object.getWikiCodeAdded());
                    jobHandle.setV5(object.getWikiMissing());
                    jobHandle.setV6(object.getRemark());
                    jobHandles.add(jobHandle);
                    if (jobHandles.size() >= 100) {
                        saveBatchJobHandles(jobHandles);
                        jobHandles.clear();
                    }
                }
                if (!jobHandles.isEmpty()) {
                    saveBatchJobHandles(jobHandles);
                    jobHandles.clear();
                }
                getList().clear();
            }
        };
        EasyExcel.read(inputStream)
                 .head(TrainingMaterialsHandle.class)
                 .registerReadListener(syncReadListener).doReadAll();
    }

    private void saveBatchJobHandles(ArrayList<JobHandle> jobHandles) {
        try {
            jobHandleService.saveBatch(jobHandles);
        } catch (DuplicateKeyException e) {
            log.warn("新品商品数据处理，导入任务列表，批量插入遇到唯一键重复，尝试逐条插入，当前批次共{}条",
                    jobHandles.size());
            int duplicateCount = 0;
            for (JobHandle handle : jobHandles) {
                if (duplicateCount > 3) {
                    log.warn("新品商品数据处理，导入任务列表，连续重复数据，放弃尝试插入，当前批次共{}条",
                            jobHandles.size());
                    break;
                }
                try {
                    jobHandleService.save(handle);
                } catch (DuplicateKeyException ignored) {
                    log.warn("新品商品数据处理，导入任务列表，重复数据:{}", handle.getUniqueKey());
                    duplicateCount++;
                }
            }
        }
    }

    @Autowired
    private ItemTrainingMaterialsBizService itemTrainingMaterialsBizService;
    @Autowired
    private IItemTrainingMaterialsService itemTrainingMaterialsService;

    @Data
    public static class TrainingMaterialsSyncParams {
        boolean onlyMatch;
    }

    public void trainingMaterialsSync(InputStream inputStream) throws IOException {
        trainingMaterialsSyncLogic(inputStream);
    }


    private void trainingMaterialsSyncLogic(InputStream inputStream) throws IOException {
        final String job = "新品商品数据处理-培训资料处理";

        final List<Content> contentsAll = JSON.parseObject(new String(IoUtil.readBytes(inputStream)),
                new TypeReference<List<Content>>() {
                });
        Assert.notNull(contentsAll, "读取培训资料数据异常");
        log.info("{}，读取培训资料数据，共{}条", job, contentsAll.size());
        final HashSetValuedHashMap<String, Content> code2contentMap = new HashSetValuedHashMap<>();
        for (Content content : contentsAll) {
            if (CollectionUtil.isNotEmpty(content.getCodes())) {
                for (String code : content.getCodes()) {
                    code2contentMap.put(code, content);
                }
            }
        }

        final List<JobHandle> list = jobHandleService.lambdaQuery().eq(JobHandle::getJob, job).list();
        for (JobHandle jobHandle : list) {
            if (StringUtils.equals(jobHandle.getV7(), "处理成功")) {
                continue;
            }
            log.info("{}，开始处理任务:{}", job, jobHandle.getUniqueKey());
            try {
                final String itemCode = jobHandle.getV2();
                final String refSpuCode = jobHandle.getV3();

                final Set<Content> contents = code2contentMap.get(itemCode);
                final Set<Content> contents1 = code2contentMap.get(refSpuCode);
                final Optional<Content> matchContent = Stream.of(contents, contents1)
                                                             .flatMap(Collection::stream)
                                                             .findFirst();
                if (!matchContent.isPresent()) {
                    throw new RuntimeException("未找到匹配的WIKI页面");
                }
                final Content content = matchContent.get();
                final Long contentid = content.getContentid();
                jobHandle.setV9(String.valueOf(contentid));
                jobHandle.setV10(JSON.toJSONString(content));

                final Long itemId;
                if (StringUtils.isBlank(jobHandle.getV8())) {
                    final ArrayList<String> codes = new ArrayList<>();
                    codes.add(itemCode);
                    codes.add(refSpuCode);
                    final List<ItemCodeRef> itemCodeRefs = itemCodeRefService.selectByCodes(codes.stream()
                                                                                                 .filter(StringUtils::isNotBlank)
                                                                                                 .collect(Collectors.toList()));

                    final Optional<Long> matchItemId = itemCodeRefs.stream().map(ItemCodeRef::getItemId).findFirst();
                    if (!matchItemId.isPresent()) {
                        throw new RuntimeException("未找到匹配的后端商品");
                    }
                    itemId = matchItemId.get();
                    jobHandle.setV8(String.valueOf(itemId));
                } else {
                    itemId = Long.parseLong(jobHandle.getV8());
                }

                final String bodyContent = content.getBody();
                final SingleResponse<ItemTrainingMaterialsDrawer> drawerResp = itemTrainingMaterialsBizService.drawer(
                        itemId);
                if (!drawerResp.isSuccess()) {
                    throw new RuntimeException("获取培训资料抽屉数据异常",
                            new BizException(drawerResp.getErrCode(), drawerResp.getErrMessage()));
                }
                final ItemTrainingMaterialsDrawer drawer = drawerResp.getData();
                final ItemTrainingMaterialsSaveCmd cmd = new ItemTrainingMaterialsSaveCmd();

                drawer.getMaterialsData().setDetails(bodyContent);
                drawer.getMaterialsData().setWikiContentId(contentid);
                cmd.setDrawer(drawer);
                cmd.setSubmit(true);
                try {
                    itemTrainingMaterialsBizService.save(cmd);
                } catch (Exception e) {
                    log.error("{}，{} 提交培训资料抽屉异常，{}", job, jobHandle.getUniqueKey(), e.getMessage(), e);
                    throw new RuntimeException("提交培训资料抽屉异常", e);
                }
                jobHandle.setV7("处理成功");
            } catch (RuntimeException e) {
                log.error("{}，{} 处理异常，{}", job, jobHandle.getUniqueKey(), e.getMessage());
                jobHandle.setV7(e.getMessage());
                jobHandleLogService.log(jobHandle, e.getMessage());
            } catch (Throwable e) {
                log.error("{}，{} 未知异常，{}", job, jobHandle.getUniqueKey(), e.getMessage(), e);
                jobHandle.setV7("未知异常");
                jobHandleLogService.log(jobHandle, ExceptionUtil.stacktraceToOneLineString(e, 3000));
            } finally {
                jobHandleService.updateById(jobHandle);
            }
        }
    }

    public void trainingMaterialsImageLogic(InputStream inputStream) throws IOException {
        final String job = "新品商品数据处理-培训资料处理";
        final Map<String, String> imageMap = JSON.parseObject(new String(IoUtil.readBytes(inputStream)),
                new TypeReference<Map<String, String>>() {
                });
        Assert.notNull(imageMap, "读取培训资料图片映射异常");
        log.info("{}，读取培训资料数据，共{}条", job, imageMap.size());

        final HashSet<String> proceed = new HashSet<>();
        final List<JobHandle> list = jobHandleService.lambdaQuery().eq(JobHandle::getJob, job).list();
        for (JobHandle jobHandle : list) {
            log.info("{}，开始处理任务:{}", job, jobHandle.getUniqueKey());
            if (Arrays.asList("处理成功", "数据重复").contains(jobHandle.getV11())) {
                continue;
            }
            if (proceed.contains(jobHandle.getUniqueKey())) {
                jobHandle.setV11("数据重复");
                continue;
            }
            proceed.add(jobHandle.getUniqueKey());
            try {
                final Long itemId = Optional.ofNullable(jobHandle.getV8()).map(Long::parseLong).orElse(0L);
                if (itemId == 0L) {
                    jobHandle.setV11("无需处理");
                    continue;
                }
                final ItemTrainingMaterials trainingMaterials = itemTrainingMaterialsService.getByItemId(itemId);
                Assert.notNull(trainingMaterials, "未找到培训资料");

                final ItemTrainingMaterialsData data = trainingMaterials.getData();
                Assert.notNull(trainingMaterials, "培训资料数据为空");

                if (StringUtils.isBlank(data.getDetails())) {
                    jobHandle.setV11("无需处理");
                    continue;
                }
                final String details = data.getDetails();
                final List<String> imgUrls = getImgUrls(details);
                int i = 0;
                String replacedDetails = details;
                for (String imgUrl : imgUrls) {
                    if (imageMap.containsKey(imgUrl)) {
                        final String newImgUrl = imageMap.get(imgUrl);
                        replacedDetails = replacedDetails.replaceAll(Pattern.quote(imgUrl), newImgUrl);
                        i++;
                    }
                }
                data.setDetails(replacedDetails);
                itemTrainingMaterialsService.updateById(trainingMaterials);
                jobHandle.setV11("处理成功");
                jobHandle.setV12(String.format("替换成功 %s 个图片链接", i));
                log.info("{}，{} 处理成功，替换成功 {} 个图片链接", job, jobHandle.getUniqueKey(), i);
            } catch (Throwable e) {
                log.error("{}，{} 处理异常，{}", job, jobHandle.getUniqueKey(), e.getMessage(), e);
                jobHandle.setV11("处理异常");
                jobHandleLogService.log(jobHandle, ExceptionUtil.stacktraceToOneLineString(e, 3000));
            } finally {
                jobHandleService.updateById(jobHandle);
            }
        }
    }

    @NonNull
    private static List<String> getImgUrls(String details) {
        final List<String> imgUrls = new ArrayList<>();
        final List<String> tags = ReUtil.findAll(PATTERN, details, 0);
        for (String imgTag : tags) {
            final List<String> url1 = ReUtil.findAll(PATTERN1, imgTag, 2);
            imgUrls.addAll(url1);
            final List<String> url2 = ReUtil.findAll(PATTERN2, imgTag, 2);
            imgUrls.addAll(url2);
        }
        return imgUrls.stream().filter(v -> v.startsWith("/")).collect(Collectors.toList());
    }

    @NonNull
    private static List<String> getImgUrls2(String details) {
        final List<String> imgUrls = new ArrayList<>();
        final List<String> tags = ReUtil.findAll(PATTERN, details, 0);
        for (String imgTag : tags) {
            final List<String> url1 = ReUtil.findAll(PATTERN1, imgTag, 2);
            imgUrls.addAll(url1);
        }
        return imgUrls;
    }


    public static void main(String[] args) throws IOException, InterruptedException, ParserConfigurationException, SAXException {
        WebClient webClient = WebClient.builder()
                                       .codecs(clientCodecConfigurer -> clientCodecConfigurer.defaultCodecs()
                                                                                             .maxInMemorySize(1024 * 1024 * 64))
                                       .build();
        final Path path = Paths.get("/Users/<USER>/Desktop/ERP/新品商品数据处理/wiki5.json");
        final byte[] bytes = Files.readAllBytes(path);
        final List<Content> pageList = JSON.parseArray(bytes, Content.class);

        final Path mapPath = Paths.get("/Users/<USER>/Desktop/ERP/新品商品数据处理/map.json");
        final ConcurrentHashMap<String, String> map = new ConcurrentHashMap<>();
        if (Files.exists(mapPath)) {
            final byte[] bytes1 = Files.readAllBytes(mapPath);
            map.putAll(JSON.parseObject(new String(bytes1), new TypeReference<Map<String, String>>() {
            }));
        }

        final UpyunConfig upyunConfig = new UpyunConfig();
        upyunConfig.setBucketName("daddylab");
        upyunConfig.setOperatorName("dprod");
        upyunConfig.setOperatorPwd("m2Pd8uacXz5alUCwaKSBGzf0bAdpi7D0");
        upyunConfig.setUpyunBaseUrl("https://cdn.daddylab.com");
        upyunConfig.setHmacSha1Algorithm("HmacSHA1");
        upyunConfig.setEdAuto("http://p1.api.upyun.com");
        upyunConfig.setDefaultDir("/Upload/supplier/item/default/");
        upyunConfig.setPublicDir("/Upload/supplier/item/");
        upyunConfig.setApiDomain("https://v1.api.upyun.com");

        final UpyunGateway upyunGateway = new UpyunGateway(upyunConfig);

        java.util.logging.Logger.getLogger(OkHttpClient.class.getName()).setLevel(Level.FINE);
        final AtomicInteger count = new AtomicInteger();
        final Semaphore semaphore = new Semaphore(128);
        final ReentrantLock lock = new ReentrantLock();
        try {
            Flux.range(0, pageList.size())
                .zipWith(Flux.fromIterable(pageList))
                .doOnNext(v -> {
                    final Integer i = v.getT1();
                    final Content content = v.getT2();
                    log.info("处理第 {} 文章:{}", i, content.getTitle());
                })
                .filter(v -> !StringUtils.isBlank(v.getT2().getBody()))
                .flatMap(v -> {
                    final Content content = v.getT2();
                    final String body = content.getBody();
                    final List<String> imgUrls = getImgUrls(body);
                    content.setImageUrls(imgUrls);
                    return Flux.fromIterable(imgUrls);
                })
                .filter(url -> !map.containsKey(url))
                .doOnNext(url -> {
                    try {
                        semaphore.acquire();
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                })
                .publishOn(Schedulers.boundedElastic())
                .flatMap(url -> saveImg(webClient, upyunGateway, url)
                        .doOnNext(upUrl -> map.put(url, upUrl))
                        .doOnNext(upUrl -> {
                            log.info("转存图片成功 {} -> {}", url, upUrl);
                        })
                        .doOnError(err -> {
                            log.error("转存图片失败 {}", url, err);
                        })
                        .onErrorResume((err) -> Mono.just(""))
                )
                .doOnError(err -> {
                    semaphore.release();
                    log.error("处理图片异常", err);
                })
                .onErrorResume((err) -> Mono.just(""))
                .doOnNext(v -> {
                    if (count.incrementAndGet() % 100 == 0) {
                        log.info("已处理 {} 张图片", count);
                        if (lock.tryLock()) {
                            try {
                                Files.write(mapPath, JSON.toJSONBytes(map, JSONWriter.Feature.PrettyFormat));
                            } catch (IOException e) {
                                log.error("保存图片映射文件失败", e);
                            } finally {
                                lock.unlock();
                            }
                        }
                    }
                })
                .doOnEach(s -> {
                    semaphore.release();
                })
                .blockLast();
            log.info("处理完成");
        } catch (Exception e) {
            log.error("处理替换图片异常", e);
        } finally {
            Files.write(path, JSON.toJSONBytes(pageList, JSONWriter.Feature.PrettyFormat));
            Files.write(mapPath, JSON.toJSONBytes(map, JSONWriter.Feature.PrettyFormat));
            log.info("写入文件完成");
        }
    }

    private static Mono<String> saveImg(WebClient webClient, UpyunGateway upyunGateway, String path) {
        path = HtmlUtil.unescape(URLUtil.decode(path));
        String pathNoParams = URLUtil.getPath(URLUtil.encode(path));
        final int indexOf = pathNoParams.indexOf("?");
        if (indexOf > -1) {
            pathNoParams = pathNoParams.substring(0, indexOf);
        }
        String finalPathNoParams = pathNoParams;
        return webClient.get()
                        .uri("https://wiki.dlab.cn" + path)
                        .header("Authorization", wikiToken)
                        .retrieve()
                        .bodyToMono(byte[].class)
                        .publishOn(Schedulers.boundedElastic())
                        .map(bytes -> {
                            final String destPath = "/Upload/supplier/item/wiki" + finalPathNoParams;
                            final Map<String, List<String>> fileInfo = upyunGateway.getFileInfo(destPath);
                            if (fileInfo != null) {
                                log.info("图片已存在:{} fileInfo:{}", destPath, fileInfo);
                                return "https://cdn.daddylab.com" + destPath;
                            }
                            final UploadImageResult uploadImageResult = upyunGateway.uploadImage(
                                    destPath,
                                    new ByteArrayInputStream(bytes),
                                    null);
                            return uploadImageResult.getUrl();
                        }).retry(3);
    }

    private static void fetchPages() throws InterruptedException, IOException {
        final WikiClient client = getWikiClient();

        final HikariDataSource dataSource = getDataSource();
        final NamedParameterJdbcTemplate jdbcTemplate = getJdbcTemplate(dataSource);
        final List<Content> pageList = getPageList(jdbcTemplate);

        log.info("获取WIKI页面列表成功，共计:{}", pageList.size());

        final StopWatch stopWatch = new StopWatch();
        stopWatch.start("根据WIKI页面标题构建索引");
        final Pattern pattern = Pattern.compile("[a-zA-Z0-9]+");
        for (Content content : pageList) {
            final StringTokenizer stringTokenizer = new StringTokenizer(content.getTitle(), "/。，,、【】'\"\\s[]\n", false);
            final ArrayList<String> codes = new ArrayList<>();
            while (stringTokenizer.hasMoreTokens()) {
                final String token = stringTokenizer.nextToken();
                if (pattern.matcher(token).matches()) {
                    codes.add(token);
                }
            }
            content.setCodes(codes);
        }
        stopWatch.stopThenOutput();

        final int permits = 32;
        final ExecutorService executorService = Executors.newFixedThreadPool(permits);
        final Semaphore semaphore = new Semaphore(permits);
        final CountDownLatch countDownLatch = new CountDownLatch(pageList.size());
        for (Content content : pageList) {
            semaphore.acquire();
            final CompletableFuture<String> stage = CompletableFuture.supplyAsync(() -> getBodyContent(client,
                    content.getContentid()), executorService);
            Single.fromCompletionStage(stage)
                  .doOnSubscribe(subscription -> log.info("开始获取WIKI页面内容:{}", content.getContentid()))
                  .doOnSuccess(content::setBody)
                  .doOnTerminate(semaphore::release)
                  .doOnTerminate(countDownLatch::countDown)
                  .retry(3)
                  .doOnError(throwable -> {
                      log.error("获取WIKI页面内容异常:{}", content.getContentid(), throwable);
                      content.setBody("err");
                  })
                  .subscribe();
        }
        countDownLatch.await();
        executorService.shutdown();
        Files.write(Paths.get("/Users/<USER>/Desktop/ERP/新品商品数据处理/wiki3.json"), JSON.toJSONBytes(pageList));
        log.info("结果已写入");
    }

    private static WikiClient getWikiClient() {
        final SpringFormEncoder springFormEncoder = new SpringFormEncoder();
        final SpringEncoder springEncoder = new SpringEncoder(springFormEncoder, HttpMessageConverters::new);
        final WikiClient client = Feign.builder()
                                       .logger(new Slf4jLogger(WikiClient.class))
                                       .logLevel(Logger.Level.FULL)
                                       .encoder(springEncoder)
                                       .contract(new SpringMvcContract())
                                       .target(new Target.HardCodedTarget<>(WikiClient.class, "https://wiki.dlab.cn/"));
        return client;
    }

    public static HikariDataSource getDataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("**************************************************************************");
        config.setUsername("erp");
        config.setPassword("pJTsWUKmHjDJTb5D");
        config.setDriverClassName("com.mysql.cj.jdbc.Driver");

        // Additional configuration for HikariCP
        config.setMaximumPoolSize(10);
        config.setIdleTimeout(30000);
        config.setConnectionTestQuery("SELECT 1");

        return new HikariDataSource(config);
    }

    public static NamedParameterJdbcTemplate getJdbcTemplate(DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        return new NamedParameterJdbcTemplate(jdbcTemplate);
    }

    private static List<Content> getPageList(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        final ArrayList<Long> pids = new ArrayList<>();
        pids.add(38340484L);
        pids.add(38340520L);

        return getPageList(namedParameterJdbcTemplate, pids);
    }

    private static List<Content> getPageList(NamedParameterJdbcTemplate namedParameterJdbcTemplate, ArrayList<Long> pids) {
        final List<Content> contents = queryPageList(namedParameterJdbcTemplate, pids);
        final ArrayList<Content> allContents = new ArrayList<>();
        allContents.addAll(contents);
        allContents.addAll(getSubPageList(namedParameterJdbcTemplate, contents));
        return allContents;
    }

    private static List<Content> getSubPageList(NamedParameterJdbcTemplate namedParameterJdbcTemplate, List<Content> contents) {
        final List<Long> contentIds = contents.stream().map(Content::getContentid).collect(Collectors.toList());
        final List<Content> subContents = queryPageList(namedParameterJdbcTemplate, contentIds);
        if (subContents.isEmpty()) {
            return subContents;
        }
        subContents.addAll(getSubPageList(namedParameterJdbcTemplate, subContents));
        return subContents;
    }

    public void materialsToBeUpdateAndSyncWiki() {
        String job = "培训资料提交到待修改";
        final List<JobHandle> list = jobHandleService.lambdaQuery().eq(JobHandle::getJob, job).list();
        for (JobHandle jobHandle : list) {
            log.info("开始处理:{}", jobHandle.getUniqueKey());
            if ("处理完成".equals(jobHandle.getV4())) {
                continue;
            }
            try {
                final String v2 = jobHandle.getV2();
                final List<String> codes = Arrays.stream(StringUtils.split(v2, ",/"))
                                                 .filter(StringUtils::isNotBlank)
                                                 .collect(Collectors.toList());
                if (codes.isEmpty()) {
                    jobHandle.setV4("无效编码");
                    continue;
                }
                final List<ItemCodeRef> itemCodeRefs = itemCodeRefService.selectByCodes(codes);
                final Long itemId = itemCodeRefs.stream()
                                               .map(ItemCodeRef::getItemId)
                                               .findFirst()
                                               .orElseThrow(() -> new RuntimeException("编码未匹配到商品"));

                itemTrainingMaterialsBizService.predicateComplete(0L,
                        itemId,
                        task -> "待QC审核".equals(task.getName()),
                        "线下审核日化类商品");
                jobHandleLogService.log(jobHandle, String.format("商品 %s 已提交", itemId));

                itemTrainingMaterialsBizService.syncContentToWiki(itemId);
                jobHandleLogService.log(jobHandle, String.format("商品 %s 已同步到WIKI", itemId));

                jobHandle.setV4("处理完成");
            } catch (Exception e) {
                jobHandle.setV4("处理异常");
                jobHandleLogService.log(jobHandle, ExceptionUtil.getSimpleStackString(e));
            } finally {
                jobHandleService.updateById(jobHandle);
            }
        }

    }

    @Autowired
    IItemDrawerLiveVerbalService itemDrawerLiveVerbalService;

    @Autowired
    IItemDrawerModuleAuditTaskService itemDrawerModuleAuditTaskService;
    @Autowired
    IItemDrawerModuleAuditService itemDrawerModuleAuditService;


    @Data
    public static class MigrateLVTParams {
        private Long testItemId;
    }
    public void migrateLVT(MigrateLVTParams params) {
        final Long testItemId = params.getTestItemId();
        final List<ItemDrawer> itemDrawerList = itemDrawerService.lambdaQuery()
                                                                 .eq(testItemId != null,
                                                                     ItemDrawer::getItemId,
                                                                     testItemId)
                                                                 .ne(ItemDrawer::getLiveVerbalTrick, "")
                                                                 .isNotNull(ItemDrawer::getLiveVerbalTrick).list();
        for (ItemDrawer itemDrawer : itemDrawerList) {
            final ItemAuditStatus liveVerbalTrickStatus = itemDrawer.getLiveVerbalTrickStatus();
            final ItemDrawerLiveVerbal itemDrawerLiveVerbal = new ItemDrawerLiveVerbal();
            itemDrawerLiveVerbal.setCreatedAt(itemDrawer.getCreatedAt());
            itemDrawerLiveVerbal.setUpdatedAt(itemDrawer.getCreatedAt());
            itemDrawerLiveVerbal.setCreatedUid(itemDrawer.getCreatedUid());
            itemDrawerLiveVerbal.setUpdatedUid(0L);
            itemDrawerLiveVerbal.setDeletedAt(0L);
            itemDrawerLiveVerbal.setIsDel(0);
            itemDrawerLiveVerbal.setName("话术1");
            itemDrawerLiveVerbal.setItemId(itemDrawer.getItemId());
            itemDrawerLiveVerbal.setItemDrawerId(itemDrawer.getId());
            itemDrawerLiveVerbal.setLiveVerbalTrickStatus(liveVerbalTrickStatus);
            itemDrawerLiveVerbal.setLiveVerbalTrick(itemDrawer.getLiveVerbalTrick());
            itemDrawerLiveVerbal.setPrincipalId(0L);
            itemDrawerLiveVerbalService.save(itemDrawerLiveVerbal);

            itemDrawerModuleAuditService.lambdaQuery()
                                        .eq(ItemDrawerModuleAudit::getType, ItemAuditType.LIVE_VERBAL_TRICK)
                                        .eq(ItemDrawerModuleAudit::getItemId, itemDrawer.getItemId())
                                        .and(q -> q.eq(ItemDrawerModuleAudit::getLiveVerbalTrickId, 0)
                                                   .or()
                                                   .isNull(ItemDrawerModuleAudit::getLiveVerbalTrickId))
                                        .oneOpt()
                                        .ifPresent(v -> {
                                            v.setLiveVerbalTrickId(itemDrawerLiveVerbal.getId());
                                            itemDrawerModuleAuditService.updateById(v);
                                        });

            itemDrawerService.lambdaUpdate()
                             .eq(ItemDrawer::getId, itemDrawer.getId())
                             .set(ItemDrawer::getLiveVerbalTrick, "")
                             .update();
            log.info("直播话术旧数据完成迁移，itemId={}，新建直播话术记录ID={}",
                     itemDrawer.getItemId(),
                     itemDrawerLiveVerbal.getId());
        }
    }

}
