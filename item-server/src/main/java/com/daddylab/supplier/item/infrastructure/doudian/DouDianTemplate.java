package com.daddylab.supplier.item.infrastructure.doudian;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ThirdPlatformSyncType;
import com.doudian.open.api.material_queryMaterialDetail.MaterialQueryMaterialDetailResponse;
import com.doudian.open.api.product_listV2.ProductListV2Response;
import com.doudian.open.api.product_listV2.data.DataItem;
import com.doudian.open.api.sku_list.SkuListResponse;
import com.doudian.open.api.sku_stockNum.SkuStockNumResponse;
import com.doudian.open.api.sku_syncStockBatch.SkuSyncStockBatchResponse;
import com.doudian.open.core.AccessToken;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年03月07日 10:17 AM
 */
public interface DouDianTemplate {

    /**
     * 抖店商品编辑
     *
     * @param itemId    erp商品 ID
     * @param productId 抖店产品 ID
     * @param syncType  同步类型 {@link ThirdPlatformSyncType}
     * @return
     */
    Resp<DouDianSyncBO> productEdit(Long itemId, Long productId, Integer syncType);

    /**
     * 查询素材中心素材信息
     *
     * @param materialId  素材中心id
     * @param accessToken 抖店token
     * @return
     */
    MaterialQueryMaterialDetailResponse getMaterial0(String materialId, AccessToken accessToken);


    DoudianProductWithSkuListVO getProductWithSkuListVO(String shopId, Long productId);

    /**
     * 批量查询抖店商品
     *
     * @param pageIndex 页码
     * @param pageSize  页大小
     * @return
     */
    ProductListV2Response batchQueryProduct(Long pageIndex, Long pageSize);

    DataItem queryProductById(String shopId, Long productId);

    /**
     * 查询 SKU 列表
     *
     * @param productId 抖店商品 ID
     * @return
     */
    SkuListResponse querySkuList(Long productId);

    SkuListResponse querySkuList(String shopId, Long productId);

    /**
     * 查询 sku库存
     *
     * @param douDianSkuId 抖店SKU ID，{@link SkuListResponse}
     * @return
     */
    SkuStockNumResponse querySkuStock(Long douDianSkuId);

    DouDianSkuStockResp queryBatchSkuStock(Long pageIndex, Long pageSize);

    /**
     * 库存批量同步接口
     * 1、只支持批量同步归属于同一个商品的sku库存；
     * 2、接口规则全部成功或全部失败，例：批量更新10个skuid库存，其中一个skuid信息不正确，这样整个请求都会失败，10个skuid都未更新成功。
     *
     * @param productId 抖店商品 ID
     * @param paramList 同步参数 {@link DouDianSkuStockParam}
     * @return
     */
    SkuSyncStockBatchResponse syncStockBatch(String token, Long productId, List<DouDianSkuStockParam> paramList);

    ProductListV2Response batchQueryProduct(String shopId, Long pageIndex, Long pageSize);

    DoudianProductWithSkuListResp queryProductWithSkuList(String shopId, long current, long size);
    DoudianProductWithSkuListResp queryProductWithSkuList(String shopId, LocalDateTime updateTimeStart, LocalDateTime updateTimeEnd, long current, long size);
}
