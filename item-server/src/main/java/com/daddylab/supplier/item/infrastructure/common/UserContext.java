package com.daddylab.supplier.item.infrastructure.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;

import com.daddylab.supplier.item.domain.auth.entity.SysMenu;
import com.daddylab.supplier.item.domain.auth.enums.ResourceType;
import com.daddylab.supplier.item.domain.auth.types.SimpleRole;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import org.flowable.engine.ProcessEngine;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/21 2:10 下午
 * @description
 */
public class UserContext {

    protected static final ThreadLocal<UserInfo> USER_HOLDER = new ThreadLocal<>();

    public static void setUserHolder(UserInfo userInfo) {
        USER_HOLDER.set(userInfo);
        SpringUtil.getBeanFactory()
                .getBeanProvider(ProcessEngine.class)
                .ifAvailable(
                        processEngine ->
                                processEngine
                                        .getIdentityService()
                                        .setAuthenticatedUserId(userInfo.getUserId().toString()));
    }

    public static Long getUserId() {
        return Objects.isNull(USER_HOLDER.get()) ? 0L : USER_HOLDER.get().getUserId();
    }

    public static String getNickName() {
        return Objects.isNull(USER_HOLDER.get()) ? "在下无名" : USER_HOLDER.get().getNickname();
    }

    public static String getLoginName() {
        final UserInfo userInfo = USER_HOLDER.get();
        if (userInfo == null) {
            return "";
        }
        String loginName = userInfo.loginName;
        if (StrUtil.isNotBlank(loginName)) {
            return loginName;
        }
        // 如果登录名为空，但邮件地址不为空，尝试从邮件解析登录名
        if (StrUtil.isNotBlank(userInfo.mail)) {
            final int indexOfAt = userInfo.mail.indexOf("@");
            loginName = userInfo.mail.substring(0, indexOfAt);
        }
        return loginName;
    }

    public static String getUserName() {
        return Objects.isNull(USER_HOLDER.get()) ? "在下无名" : USER_HOLDER.get().userName;
    }

    public static List<Long> getRoleIds() {
        return Objects.isNull(USER_HOLDER.get())
                ? new LinkedList<>()
                : USER_HOLDER.get().getRoleIds();
    }

    public static Optional<UserInfo> getUserInfo() {
        return Optional.ofNullable(USER_HOLDER.get());
    }

    public static boolean hasPermission(String resource) {
        return getUserInfo().map(userInfo -> userInfo.hasPermission(resource)).orElse(false);
    }

    public static boolean hasPermission(String resource, ResourceType resourceType) {
        return getUserInfo()
                .map(userInfo -> userInfo.hasPermission(resource, resourceType))
                .orElse(false);
    }

    public static void remove() {
        USER_HOLDER.remove();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class UserInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /** 用户id */
        private Long userId;

        /** 用户名称 */
        private String userName;

        /** 花名 */
        private String nickname;

        /** 手机号 */
        private String mobileNum;

        /** 邮件地址 */
        private String mail;

        /** 登录名 */
        private String loginName;

        /** 用户角色id */
        private List<Long> roleIds;

        /** 用户菜单 */
        private List<SysMenu> menus;

        /** 部门ID */
        private Long deptId;

        /** 部门名称 */
        private String dept;

        /**
         * 一级部门ID
         */
        private Long topDeptId;

        /**
         * 一级部门
         */
        private String topDept;

        /** 用户角色名称 */
        private List<SimpleRole> roles;

        /** 用户有权限访问的资源 */
        private Map<ResourceType, List<String>> resourceCodes;

        /** 刷新时间 */
        private LocalDateTime queryTime;

        /**
         * 用户是否具有访问某一资源的权限
         *
         * @param resource 资源标识
         */
        public boolean hasPermission(String resource) {
            for (Map.Entry<ResourceType, List<String>> resourceTypeEntry :
                    resourceCodes.entrySet()) {
                final List<String> resources = resourceTypeEntry.getValue();
                if (resources.contains(resource)) {
                    return true;
                }
            }
            return false;
        }

        /**
         * 用户是否具有访问某一资源的权限
         *
         * @param resource 资源标识
         * @param resourceType 资源类型
         */
        public boolean hasPermission(String resource, ResourceType resourceType) {
            final List<String> resources = resourceCodes.get(resourceType);
            if (CollUtil.isEmpty(resources)) return false;
            return resources.contains(resource);
        }

        /**
         * 获取用户菜单
         *
         * @param filterNoAuth 是否过滤掉没有权限访问的菜单
         */
        public List<SysMenu> getMenus(boolean filterNoAuth) {
            return filterNoAuth ? filterNoAuthMenu(menus) : menus;
        }

        /**
         * 过滤掉没有权限的菜单
         *
         * @param menus 所有菜单
         * @return 有访问权限的菜单
         */
        private List<SysMenu> filterNoAuthMenu(List<SysMenu> menus) {
            final ArrayList<SysMenu> filteredMenus = new ArrayList<>(menus.size() / 2);
            for (SysMenu menu : menus) {
                if (!menu.isHasPermission()) {
                    continue;
                }
                final List<SysMenu> children = menu.getChildren();
                if (children != null && !children.isEmpty()) {
                    final List<SysMenu> filterChildren = filterNoAuthMenu(children);
                    menu.setChildren(filterChildren);
                }
                filteredMenus.add(menu);
            }
            return filteredMenus;
        }
    }
}
