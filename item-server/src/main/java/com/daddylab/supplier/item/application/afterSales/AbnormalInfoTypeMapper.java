package com.daddylab.supplier.item.application.afterSales;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesAbnormalInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2022/11/1
 */
@Mapper
public interface AbnormalInfoTypeMapper {

    AbnormalInfoTypeMapper INST = Mappers.getMapper(AbnormalInfoTypeMapper.class);

    @Mapping(source = "previousState", target = "prevState")
    AbnormalStateChangeEvent abnormalInfoToEvent(AfterSalesAbnormalInfo po, Integer previousState);

}
