package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.excel;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.Response;
import com.alibaba.excel.EasyExcel;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.QuantityCombinedPriceBO;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseRandomSkuCombinationPrice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseSingleSkuCombinationPrice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseRandomSkuCombinationPriceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseSingleSkuCombinationPriceService;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR> up
 * @date 2022年11月15日 2:54 PM
 */
@Slf4j
@RestController
@RequestMapping("/purchasePrice")
public class ReadExcelController {

    @Resource
    IPurchaseSingleSkuCombinationPriceService iPurchaseSingleSkuCombinationPriceService;

    @Resource
    IPurchaseRandomSkuCombinationPriceService iPurchaseRandomSkuCombinationPriceService;

    /**
     * @param file
     * @param priceType 1.日常价格。2 活动价格（需要考虑时间）
     * @return
     */
    @PostMapping(value = "/singleSku", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("单sku纬度数据导入")
    @Auth(noAuth = true)
    public Response singleSku(@RequestParam("file") MultipartFile file, Integer priceType) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            try {
                singleHandler(file.getInputStream(), priceType);
            } catch (IOException e) {
                Alert.text(MessageRobotCode.GLOBAL, "导入SKU阶梯价异常:" + ExceptionUtil.getSimpleStackString(e));
                log.error("导入单品组合价异常", e);
            }
        });
        return Response.buildSuccess();
    }

    public void singleHandler(InputStream inputStream, Integer priceType) throws IOException {
        List<ExportSingleSkuRow> sheetRowsRaw = EasyExcel.read(inputStream)
                .headRowNumber(1)
                .head(ExportSingleSkuRow.class).sheet(0).doReadSync();
        List<ExportSingleSkuRow> sheetRows = sheetRowsRaw.stream().distinct()
                .filter(val -> StrUtil.isNotBlank(val.getSkuCode())).collect(Collectors.toList());
        log.info("批量导入价格预处理，总计{}行，去重后{}行", sheetRowsRaw.size(), sheetRows.size());

        List<PurchaseSingleSkuCombinationPrice> saveList = new LinkedList<>();
        sheetRows.stream().collect(Collectors.groupingBy(ExportSingleSkuRow::getSkuCode))
                .forEach((skuCode, list) ->
                        saveList.add(singleHandler0(list, skuCode, priceType)));
        iPurchaseSingleSkuCombinationPriceService.saveBatch(saveList);
        log.info("批量导入价格预处理，总计{}行，去重后{}行。全部处理完成", sheetRowsRaw.size(), sheetRows.size());
    }

    public PurchaseSingleSkuCombinationPrice singleHandler0(List<ExportSingleSkuRow> list
            , String skuCode, Integer priceType) {
        AtomicInteger i = new AtomicInteger();
        List<QuantityCombinedPriceBO> prices = new LinkedList<>();
        list.forEach(val -> {
            QuantityCombinedPriceBO bo = new QuantityCombinedPriceBO();
            bo.setCode(val.getSkuCode() + "P" + i);
            bo.setQuantity(val.getQuantity());
            bo.setPrice(new BigDecimal(val.getPrice().replaceAll(" ", "")));
            prices.add(bo);
            i.getAndIncrement();
        });

        long startTime = 1640966400L;
        long endTime = 1956499200L;
        if (priceType == 2) {
            String activityStartTime = list.get(0).getActivityStartTime();
            startTime = DateUtil.parseTime(activityStartTime, "yyyy-MM-dd HH:mm:ss");
            String activityEndTime = list.get(0).getActivityEndTime();
            endTime = DateUtil.parseTime(activityEndTime, "yyyy-MM-dd HH:mm:ss");
        }

        PurchaseSingleSkuCombinationPrice price = new PurchaseSingleSkuCombinationPrice();
        price.setCode(skuCode);
        price.setPriceInfo(JsonUtil.toJson(prices));
        price.setSource(1);
        price.setPlatform(0);
        price.setStartTime(startTime);
        price.setEndTime(endTime);
        price.setPriceType(priceType);
        return price;
    }

    @PostMapping(value = "/randomSku", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("任意sku多纬度数据导入")
    @Auth(noAuth = true)
    public Response randomSku(@RequestParam("file") MultipartFile file, Integer priceType) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            try {
                randomHandler(file.getInputStream(), priceType);
            } catch (IOException e) {
                Alert.text(MessageRobotCode.GLOBAL, "导入SPU阶梯价异常:" + ExceptionUtil.getSimpleStackString(e));
                log.error("导入单品日常组合价异常", e);
            }
        });
        return Response.buildSuccess();
    }

    public void randomHandler(InputStream inputStream, Integer priceType) throws IOException {
        List<ExportSingleSkuRow> sheetRowsRaw = EasyExcel.read(inputStream)
                .headRowNumber(1)
                .head(ExportSingleSkuRow.class).sheet(0).doReadSync();
        List<ExportSingleSkuRow> sheetRows = sheetRowsRaw.stream()
                .filter(val -> val.getSpuCode() != null && !"".equals(val.getSpuCode()))
                .distinct().collect(toList());
        log.info("批量导入价格预处理，总计{}行，去重后{}行", sheetRowsRaw.size(), sheetRows.size());

        List<PurchaseRandomSkuCombinationPrice> saveList = new LinkedList<>();
        sheetRows.stream().collect(Collectors.groupingBy(ExportSingleSkuRow::getSpuCode))
                .forEach((spuCode, list) -> {
                    saveList.add(randomHandler0(list, RandomUtil.randomString(6), priceType));
                });
        iPurchaseRandomSkuCombinationPriceService.saveBatch(saveList);
    }

    public PurchaseRandomSkuCombinationPrice randomHandler0(List<ExportSingleSkuRow> list, String spuCode,
                                                            Integer priceType) {
        List<QuantityCombinedPriceBO> prices = new LinkedList<>();
        Map<String, List<ExportSingleSkuRow>> collect = list.stream()
                .collect(Collectors.groupingBy(ExportSingleSkuRow::getSkuCode));
        long startTime = 1640966400L;
        long endTime = 1956499200L;
        for (Map.Entry<String, List<ExportSingleSkuRow>> listEntry : collect.entrySet()) {
            AtomicInteger i = new AtomicInteger();
            String randomCode = RandomUtil.randomString(6);
            listEntry.getValue().forEach(val -> {
                QuantityCombinedPriceBO bo = new QuantityCombinedPriceBO();
                bo.setCode(randomCode + "P" + i);
                bo.setQuantity(val.getQuantity());
                bo.setPrice(new BigDecimal(val.getPrice().replaceAll(" ", "")));
                prices.add(bo);
                i.getAndIncrement();
            });

            if (priceType == 2) {
                String activityStartTime = listEntry.getValue().get(0).getActivityStartTime();
                startTime = DateUtil.parseTime(activityStartTime, "yyyy-MM-dd HH:mm:ss");
                String activityEndTime = listEntry.getValue().get(0).getActivityEndTime();
                endTime = DateUtil.parseTime(activityEndTime, "yyyy-MM-dd HH:mm:ss");
            }
            break;
        }

        String codes = StrUtil.join("|", new LinkedList<>(collect.keySet()));
        PurchaseRandomSkuCombinationPrice vo = new PurchaseRandomSkuCombinationPrice();
        vo.setGroupCode(spuCode);
        vo.setCode(codes);
        vo.setPriceInfo(JsonUtil.toJson(prices));
        vo.setPlatform(0);
        vo.setPriceType(priceType);
        // 2022-01-01 00:00:00
        vo.setStartTime(startTime);
        // 2032-01-01 00:00:00
        vo.setEndTime(endTime);
        return vo;
    }



}
