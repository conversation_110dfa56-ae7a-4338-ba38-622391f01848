package com.daddylab.supplier.item.application.platformItemSkuInventory.impl;

import com.daddylab.supplier.item.application.platformItem.data.PlatformItemSkuInventoryVO;
import com.daddylab.supplier.item.application.platformItemSkuInventory.PlatformItemSkuInventoryBizService;
import com.daddylab.supplier.item.application.platformItemSkuInventory.PlatformItemSkuSyncService;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSkuInventory;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemSkuInventoryService;
import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @class PlatformItemSkuInventoryBizServiceImpl.java
 * @description 平台商品库存服务实现类
 * @date 2024-03-13 16:01
 */
@Service
@Slf4j
public class PlatformItemSkuInventoryBizServiceImpl implements PlatformItemSkuInventoryBizService {
    @Autowired
    private IPlatformItemSkuInventoryService platformItemSkuInventoryService;
    @Autowired
    private ObjectProvider<List<PlatformItemSkuSyncService>> platformItemSkuSyncServiceProvider;

    @Autowired
    private RocketMQProducer rocketMQProducer;

    @Override
    public void fullDoseSync(Platform platform) {
        platformItemSkuSyncServiceProvider.ifAvailable(services -> {
            for (PlatformItemSkuSyncService syncService : services) {
                if (platform == null || syncService.defaultType() == platform) {
                    log.info("[平台商品同步][全量同步][平台{}]触发", syncService.defaultType().getDesc());
                    syncService.fullDoseSync();
                }
            }
        });
    }

    @Override
    public void syncStock(Long time) {

    }

    @Mapper
    public interface Assembler {
        Assembler INST = Mappers.getMapper(Assembler.class);

        PlatformItemSkuInventoryVO poToVo(PlatformItemSkuInventory platformItemSkuInventory);
    }

}
