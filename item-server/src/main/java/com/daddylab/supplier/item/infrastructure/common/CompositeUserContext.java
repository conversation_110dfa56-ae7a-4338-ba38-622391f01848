package com.daddylab.supplier.item.infrastructure.common;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExternalUser;

/**
 * <AUTHOR>
 * @since 2024/6/5
 */
public class CompositeUserContext {

    public static Long getUserId() {
        return UserContext.getUserId() != 0L ?
                UserContext.getUserId() :
                ExternalUserContext.getOptional().map(ExternalUser::getId).orElse(0L);
    }

    public static LoginType getLoginType() {
        return UserContext.getUserId() != 0L ? LoginType.DEFAULT : LoginType.EXTERNAL_USER;
    }

    public static void remove() {
        UserContext.remove();
        ExternalUserContext.remove();

    }
}
