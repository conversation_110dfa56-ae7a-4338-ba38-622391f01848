package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopPrincipal;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ShopPrincipalMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopPrincipalService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 店铺负责人 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class ShopPrincipalServiceImpl extends DaddyServiceImpl<ShopPrincipalMapper, ShopPrincipal> implements IShopPrincipalService {

}
