package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentApplyOrder;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 采购管理-付款申请单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
public interface PaymentApplyOrderMapper extends DaddyBaseMapper<PaymentApplyOrder> {

    String getMaxNo(@Param("prefix") String prefix);


}
