package com.daddylab.supplier.item.application.drawer.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/5/9
 */
@Service
@Slf4j
public class ItemLaunchPlanRecoverService {
    @Autowired
    private IItemDrawerModuleAuditService itemDrawerModuleAuditService;
    @Autowired
    private IItemDrawerModuleAuditTaskService itemDrawerModuleAuditTaskService;
    @Autowired
    private IItemLaunchPlanItemRefService itemLaunchPlanItemRefService;
    @Autowired
    private IItemService itemService;
    @Autowired
    private IItemLaunchStatsService itemLaunchStatsService;

    public void recoverRef(Collection<Long> refIds) {
        final int undelete = itemLaunchPlanItemRefService.getDaddyBaseMapper().undelete(refIds);
        log.info("[商品上新计划数据恢复]恢复{}条上新计划商品关联记录", undelete);

        final List<ItemLaunchPlanItemRef> itemLaunchPlanItemRefs = itemLaunchPlanItemRefService.listByIds(refIds);
        for (ItemLaunchPlanItemRef itemLaunchPlanItemRef : itemLaunchPlanItemRefs) {
            recoverItemLaunch(itemLaunchPlanItemRef.getItemId());
        }
    }

    private void recoverItemLaunch(Long itemId) {
        log.info("[商品上新计划数据恢复]正在恢复商品上新数据 {}", itemId);
        final ItemAuditType type = ItemAuditType.ITEM_MATERIAL;

        ItemLaunchStats stats = itemLaunchStatsService.getDaddyBaseMapper().selectOneByItemId(itemId);
        final int undelete = itemLaunchStatsService.getDaddyBaseMapper()
                                                   .undelete(Collections.singletonList(stats.getId()));
        log.info("[商品上新计划数据恢复]恢复{}条上新统计数据 {}", undelete, itemId);

        ItemLaunchStatus launchStatus;
        ItemAuditStatus auditStatus = ItemAuditStatus.NONE;
        if (stats.getLaunchTime() > 0) {
            launchStatus = ItemLaunchStatus.HAS_BE_RELEASED;
            auditStatus = ItemAuditStatus.FINISHED;
        } else if (stats.getToBeReleaseStartTime() > 0) {
            launchStatus = ItemLaunchStatus.TO_BE_RELEASED;
            auditStatus = ItemAuditStatus.FINISHED;
        } else if (stats.getToBeUpdateStartTime() > 0) {
            launchStatus = ItemLaunchStatus.TO_BE_UPDATED;
            auditStatus = ItemAuditStatus.FINISHED;
        } else if (stats.getToBeQcAuditStartTime() > 0) {
            launchStatus = ItemLaunchStatus.TO_BE_UPDATED;
            auditStatus = ItemAuditStatus.WAIT_QC_AUDIT;
        } else if (stats.getToBeLegalAuditStartTime() > 0) {
            launchStatus = ItemLaunchStatus.TO_BE_UPDATED;
            auditStatus = ItemAuditStatus.WAIT_LEGAL_AUDIT;
        } else if (stats.getToBeDesignStartTime() > 0) {
            launchStatus = ItemLaunchStatus.TO_BE_DESIGNED;
        } else if (stats.getToBeImproveStartTime() > 0) {
            launchStatus = ItemLaunchStatus.TO_BE_IMPROVED;
        } else {
            log.error("[商品上新计划数据恢复]确定上新状态异常 {} {}", itemId, stats);
            return;
        }

        Item updateItem = new Item();
        updateItem.setId(itemId);
        updateItem.setLaunchStatus(launchStatus.getValue());
        updateItem.setAuditStatus(auditStatus.getValue());
        itemService.updateById(updateItem);

        final Optional<ItemDrawerModuleAudit> itemDrawerModuleAuditOptional =
                itemDrawerModuleAuditService.getItemDrawerModuleAudit(type, 0L, itemId);
        if (!itemDrawerModuleAuditOptional.isPresent()) {
            log.warn("[商品上新计划数据恢复]未找到模块审核记录 {}", itemId);
            return;
        }

        final ItemDrawerModuleAudit itemDrawerModuleAudit = itemDrawerModuleAuditOptional.get();
        itemDrawerModuleAudit.setAuditStatus(auditStatus);
        itemDrawerModuleAudit.setAuditStatus(auditStatus);
        itemDrawerModuleAuditService.updateById(itemDrawerModuleAudit);

        final Integer round = itemDrawerModuleAudit.getRound();
        final List<ItemDrawerModuleAuditTask> itemDrawerModuleAuditTasks = itemDrawerModuleAuditTaskService
                .getDaddyBaseMapper()
                .selectByItemIdAndRound(type, round, itemId);
        if (itemDrawerModuleAuditTasks.isEmpty()) {
            log.warn("[商品上新计划数据恢复]未找到可恢复的模块审核任务 {}", itemId);
            return;
        }
        final List<ItemDrawerModuleAuditTask> deletedTasks = itemDrawerModuleAuditTasks.stream()
                                                                                       .filter(v -> v.getIsDel() > 0)
                                                                                       .collect(Collectors.toList());
        if (!deletedTasks.isEmpty()) {
            final int undeleteTaskNum = itemDrawerModuleAuditTaskService.getDaddyBaseMapper()
                                                                        .undelete(deletedTasks.stream()
                                                                                              .map(ItemDrawerModuleAuditTask::getId)
                                                                                              .collect(
                                                                                                      Collectors.toList()));
            log.info("[商品上新计划数据恢复]恢复{}条模块审核任务 {}", undeleteTaskNum, itemId);
        }

    }
}
