package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetActivityEvent;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.HandingSheetActivityEventMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.HandingSheetActivityEventService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 盘货表对应的活动力度 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
@Service
public class HandingSheetActivityEventServiceImpl extends DaddyServiceImpl<HandingSheetActivityEventMapper, HandingSheetActivityEvent> implements HandingSheetActivityEventService {
    @Autowired
    private HandingSheetActivityEventMapper handingSheetActivityEventMapper;

    @Override
    public List<HandingSheetActivityEvent> listByHandingSheetId(Long handingSheetId) {
        if (handingSheetId == null || handingSheetId <= 0) {
            return new ArrayList<>();
        }
        QueryWrapper<HandingSheetActivityEvent> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(HandingSheetActivityEvent::getHandingSheetId, handingSheetId);
        return handingSheetActivityEventMapper.selectList(queryWrapper);
    }

    @Override
    public void deleteBySheetId(Long handingSheetId) {
        handingSheetActivityEventMapper.deleteBySheetId(handingSheetId);
    }
}
