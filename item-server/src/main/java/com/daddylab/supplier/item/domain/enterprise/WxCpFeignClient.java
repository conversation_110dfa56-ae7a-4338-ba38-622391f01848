package com.daddylab.supplier.item.domain.enterprise;

import com.daddylab.supplier.item.domain.enterprise.query.LaunchCodeQuery;
import com.daddylab.supplier.item.domain.enterprise.vo.LaunchCodeVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.DlabConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Class  WxCpFeignClient
 *
 * @Date 2022/5/30下午9:44
 * <AUTHOR>
 */
@FeignClient(name = "WxCpFeignClient", url = "https://qyapi.weixin.qq.com", path = "/cgi-bin", configuration = DlabConfiguration.class)
public interface WxCpFeignClient {


    @PostMapping("/get_launch_code")
    LaunchCodeVO getLaunchCode(@RequestParam("access_token") String accessToken,
                               @RequestBody  LaunchCodeQuery launchCodeQuery);
}
