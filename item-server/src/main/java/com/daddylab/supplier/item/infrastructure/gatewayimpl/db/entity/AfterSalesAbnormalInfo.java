package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 售后异常件信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AfterSalesAbnormalInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 物流名称
     */
    private String logisticsName;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 备注
     */
    private String remark;

    /**
     * 关联的退换单号
     */
    private String returnOrderNo;

    /**
     * P系统供应商id
     */
    private Long partnerProviderId;

    private String partnerProviderName;

    /**
     * 统一社会信用代码
     */
    private String unifySocialCreditCode;

    /**
     * 仓库编号
     */
    private String warehouseNo;


}
