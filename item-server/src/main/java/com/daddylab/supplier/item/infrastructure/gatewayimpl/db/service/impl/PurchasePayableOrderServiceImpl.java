package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PurchasePayableOrderStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchasePayableOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchasePayableOrderService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 采购应付表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Service
public class PurchasePayableOrderServiceImpl extends DaddyServiceImpl<PurchasePayableOrderMapper, PurchasePayableOrder> implements IPurchasePayableOrderService {

    @Override
    public Boolean related(List<Long> idList, Integer type) {
        if (CollectionUtils.isEmpty(idList)) {
            return false;
        }
        return this.lambdaQuery().in(PurchasePayableOrder::getRelatedOrderId, idList)
                .eq(PurchasePayableOrder::getType, type).count() > 0;
    }

    @Override
    public void markWriteOffWithStockOrder(String stockOrderNo) {
        this.lambdaUpdate()
                .set(PurchasePayableOrder::getStatus, PurchasePayableOrderStatus.FIXED_ORDER)
                .eq(PurchasePayableOrder::getNo,stockOrderNo)
                .update();
    }
}
