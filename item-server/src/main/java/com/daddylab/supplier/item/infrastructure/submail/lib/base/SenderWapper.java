package com.daddylab.supplier.item.infrastructure.submail.lib.base;


import com.daddylab.supplier.item.infrastructure.submail.entity.DataStore;
import com.daddylab.supplier.item.infrastructure.submail.lib.Message;


/**
 * 包装类 ADDRESSBOOKMail、ADDRESSBOOKMessage、MAILSend、MAILXSend、MESSAGEXsend等父类
 *
 * @Auther: <EMAIL>
 * @version: 1.0.0
 * @Date: 2021/04/16/4:01 下午
 */
public abstract class SenderWapper {

	protected DataStore requestData = new DataStore();

	public abstract Message getSender();

	public DataStore getRequestData() {
		return requestData;
	}
}
