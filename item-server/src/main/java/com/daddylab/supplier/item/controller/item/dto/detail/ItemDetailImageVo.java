package com.daddylab.supplier.item.controller.item.dto.detail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13 7:25 下午
 * @description
 */
@AllArgsConstructor
@Builder
@Data
@ApiModel("商品详情-图片信息返回封装")
public class ItemDetailImageVo {

    @ApiModelProperty("imageId")
    private Long id;
    @ApiModelProperty("图片url")
    private String imageUrl;
    @ApiModelProperty("图片排序")
    private Integer sort;
    @ApiModelProperty("图片类型。1:普通商品图片 2:运营商品图片")
    private Integer type;
    @ApiModelProperty("是否是主图。1表示主图。0非主图")
    private Integer isMain;

}
