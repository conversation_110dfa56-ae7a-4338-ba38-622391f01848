package com.daddylab.supplier.item.infrastructure.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;

/**
 * 金蝶SDK 相关配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/23 1:54 下午
 * @description
 */
@Data
@Configuration
@RefreshScope
public class KingDeeConfig {

    @Value("${kingdee.X-KDApi-AcctID}")
    private String acctId;

    @Value("${kingdee.X-KDApi-AppID}")
    private String appId;

    @Value("${kingdee.X-KDApi-AppSec}")
    private String appSec;

    @Value("${kingdee.X-KDApi-UserName}")
    private String userName;

    @Value("${kingdee.X-KDApi-Password}")
    private String password;

    @Value("${kingdee.X-KDApi-LCID}")
    private String lcId;

    @Value("${kingdee.createOrgId}")
    private String createOrgId;

    @Value("${kingdee.useOrgId}")
    private String useOrgId;

    @Value("${kingdee.rootCategory}")
    private String rootCategory;

    @Value("${kingdee.connect-timeout:}")
    private String connectTimeout;

    @Value("${kingdee.request-timeout:}")
    private String requestTimeout;

    @Value("${kingdee.stock-timeout:}")
    private String stockTimeout;

    /**
     * 大于此id的item的sku为固定单位，不再维护单位。
     */
    @Value("${kingdee.lineItemId}")
    private String lineItemId;

    @Value("${kingdee.unitCode}")
    private String unitCode;

    /**
     * 根据skuCode查询物料编码的时候，发现物料在kingDee系统中暗自配置了以下两个id。
     */
    @Value("${kingdee.FUseOrgId}")
    private String fUseOrgId;
    @Value("${kingdee.FCreateOrgId}")
    private String fCreateOrgId;

    @Value("${kingdee.url}")
    private String url;

    @Value("${kingdee.autoSubmit:0}")
    private String autoSubmit;

    public Boolean getAutoSubmit() {
        return Objects.equals("1", this.autoSubmit);
    }

    private final static String SERVER_URL = "https://daddylab.ik3cloud.com/k3cloud/";

//    @Bean
//    public IdentifyInfo webApiClient() {
//        Assert.notNull(rootCategory, "物料分组根目录不得为空");
//        Assert.notNull(lineItemId, "物料单位同步分割id不得为空");
//        Assert.notNull(unitCode, "物料单位基础值不得为空");
//
//        final IdentifyInfo identifyInfo = new IdentifyInfo();
//        identifyInfo.setdCID(acctId);
//        identifyInfo.setAppId(appId);
//        identifyInfo.setAppSecret(appSec);
//        identifyInfo.setLcid(2052);
//        identifyInfo.setUserName(userName);
//        identifyInfo.setServerUrl(SERVER_URL);
//        identifyInfo.setRequestTimeout(StringUtil.isBlank(connectTimeout) ? 120 : Integer.parseInt(connectTimeout));
//        identifyInfo.setConnectTimeout(StringUtil.isBlank(requestTimeout) ? 120 : Integer.parseInt(requestTimeout));
//        identifyInfo.setStockTimeout(StringUtil.isBlank(stockTimeout) ? 180 : Integer.parseInt(stockTimeout));
//        return identifyInfo;
//
//    }

}
