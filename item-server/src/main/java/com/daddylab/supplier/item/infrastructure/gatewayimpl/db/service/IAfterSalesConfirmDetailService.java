package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesConfirmDetail;

/**
 * <p>
 * 售后单销退确认单据明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
public interface IAfterSalesConfirmDetailService extends IDaddyService<AfterSalesConfirmDetail> {

}
