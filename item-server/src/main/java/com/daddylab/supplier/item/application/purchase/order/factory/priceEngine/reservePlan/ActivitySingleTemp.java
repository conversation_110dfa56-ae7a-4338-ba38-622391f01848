package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.reservePlan;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.CommonUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.WrapperType;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.ActivityQuantitySingleDO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.ActivityTimeSinglePriceDO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.HistoryQuantitySingleDO;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderDetailWrapperService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR> up
 * @date 2023年09月27日 4:50 PM
 */
@Slf4j
@Component
public class ActivitySingleTemp extends ActivityCalculateTemplate {
    @Resource
    WdtOrderDetailMapper wdtOrderDetailMapper;
    @Resource
    IWdtOrderDetailWrapperService iWdtOrderDetailWrapperService;
    @Resource
    CommonUtil commonUtil;


    @Override
    protected void considerTime(TimeBO timeBO) {
        // 拿两个线程出来进行处理
        // 以wrapper表的数据inner join采购价格表，捞出所有sku以及其在指定活动时间范围内的优惠采购成本价。
        List<ActivityTimeSinglePriceDO> list = wdtOrderDetailMapper.selectStockOutSingleTimePurchase(timeBO.getOperateMonth());
        if (CollUtil.isEmpty(list)) {
            return;
        }

        Semaphore semaphore = new Semaphore(4);
        CountDownLatch countDownLatch = new CountDownLatch(list.size());

        list.forEach(val -> ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            try {
                semaphore.acquire();
                iWdtOrderDetailWrapperService.lambdaUpdate()
                        .set(WdtOrderDetailWrapper::getPrice, val.getPriceCost())
                        .eq(WdtOrderDetailWrapper::getSkuCode, val.getSkuCode())
                        .eq(WdtOrderDetailWrapper::getType, WrapperType.STOCK_OUT_SINGLE.getValue())
                        .eq(val.getPlatformType() != 0, WdtOrderDetailWrapper::getPlatformType, val.getPlatformType())
                        .between(WdtOrderDetailWrapper::getPayTime, val.getStartTime(), val.getEndTime())
                        .update();
            } catch (Exception e) {
                log.error("ActivityTimePrice doPriceProcess stockSingle update sku price fail.val:{},wrapperType:{}",
                        JsonUtil.toJson(val), WrapperType.STOCK_OUT_SINGLE.getDesc(), e);
            } finally {
                semaphore.release();
                countDownLatch.countDown();
            }
        }));

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("ActivityTimePrice doPriceProcess stockOutSingle countDownLatch await", e);
        }


    }

    @Override
    protected void considerQuantity(TimeBO timeBO) {
        // 查询出和单品关联的采购活动价（按数量进行优惠）。
        List<ActivityQuantitySingleDO> list = wdtOrderDetailMapper.selectSingleQuantityPurchase(timeBO.getOperateMonth());
        if (CollUtil.isEmpty(list)) {
            return;
        }

        Semaphore semaphore = new Semaphore(2);
        CountDownLatch countDownLatch = new CountDownLatch(list.size());

        list.forEach(activityQuantitySingleDo -> ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            try {
                semaphore.acquire();

                // 拿发货时间在当月的sku去碰撞活动价数据（按数量优惠），发现存在N条如下数据
                // 比如skuA，发货时间在11月，但是支付时间在10月，并且这个品有个数量50的优惠的采购活动价，活动时间范围是整个10月。
                // 那算skuA价格的时候，就必须考虑10月份的时候，skuA出库了多少。看是否达到50。如果到了50，那这N条数据就不需要走采购活动优惠计算
                // 如果没到50，那差值就必须得走采购活动按优惠计算了，所以，这种情况下，要查询历史订单数据来判断。
                AtomicInteger historyCount = new AtomicInteger(0);
                // 进入if分支条件：此sku的支付时间能和某个数量优惠采购活动匹配，并且这个活动的结束时间已经小于当前月了。是历史了。
                if (activityQuantitySingleDo.getEndTime() <= timeBO.getMonthStartTime()) {
                    // 历史订单数据筛选条件：1.支付时间在此活动时间范围之内。
                    //                   2.发货时间小于当前时间，已完成历史发货。
                    List<HistoryQuantitySingleDO> historyQuantitySingleDdList = wdtOrderDetailMapper.selectSingleHistoryQuantityDO(
                            activityQuantitySingleDo.getSkuCode(),
                            DateUtil.parseTimeStamp(activityQuantitySingleDo.getStartTime(), "yyyy-MM-dd HH:mm:ss"),
                            DateUtil.parseTimeStamp(activityQuantitySingleDo.getEndTime(), "yyyy-MM-dd HH:mm:ss"),
                            timeBO.getMonthStart());

                    // 全平台通用
                    if (activityQuantitySingleDo.getPlatformType() == 0) {
                        // 累计所有平台的数据为历史数据
                        BigDecimal historyReduce = historyQuantitySingleDdList.stream().map(HistoryQuantitySingleDO::getSumNum)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        historyCount.set(historyReduce.intValue());
                    }
                    // 做平台对应
                    else {
                        historyQuantitySingleDdList.stream().filter(val -> {
                            Integer erpPurchasePricePlatformType = commonUtil.getErpPurchasePricePlatformType(val.getPlatformId());
                            return erpPurchasePricePlatformType.equals(activityQuantitySingleDo.getPlatformType());
                        }).findFirst().ifPresent(val -> historyCount.set(val.getSumNum().intValue()));
                    }
                    log.info("ActivityQuantityPrice singleHandler historyCount:{},skuCode:{}",
                            historyCount.get(), activityQuantitySingleDo.getSkuCode());
                }

                // 根据历史已处理的处理，来处理当月的情况
                // 活动数量 - 历史数量，仍然大于0，才继续更新。否则不处理。
                int stillNeedUpdateCount = (activityQuantitySingleDo.getNumCost().intValue() - historyCount.get());
                if (stillNeedUpdateCount > 0) {
                    List<Long> updateIds = new LinkedList<>();
                    int count = 0, offset = 0, size = 50;
                    l1:
                    while (count < stillNeedUpdateCount) {
                        List<WdtOrderDetailWrapper> list1 = iWdtOrderDetailWrapperService.lambdaQuery()
                                .eq(WdtOrderDetailWrapper::getSkuCode, activityQuantitySingleDo.getSkuCode())
                                .eq(activityQuantitySingleDo.getPlatformType() != 0, WdtOrderDetailWrapper::getPlatformType,
                                        activityQuantitySingleDo.getPlatformType())
                                .eq(WdtOrderDetailWrapper::getType, 1)
                                .between(WdtOrderDetailWrapper::getPayTime, activityQuantitySingleDo.getStartTime(),
                                        activityQuantitySingleDo.getEndTime())
                                .orderByAsc(WdtOrderDetailWrapper::getId).last("limit " + offset + "," + size).list();
                        if (CollUtil.isNotEmpty(list1)) {
                            break l1;
                        }
                        l2:
                        for (WdtOrderDetailWrapper wdtOrderDetailWrapper : list1) {
                            count = count + wdtOrderDetailWrapper.getQuantity();
                            if (count < stillNeedUpdateCount) {
                                updateIds.add(wdtOrderDetailWrapper.getId());
                            } else if (count == stillNeedUpdateCount) {
                                updateIds.add(wdtOrderDetailWrapper.getId());
                                break l2;
                            } else {
                                // 根据补全数量和剩余的数量。更新这条数据的数量为剩余数量，补全数量和活动价格，生成一条新的数据。
                                int complementQuantity = count - stillNeedUpdateCount;
                                int remainQuantity = wdtOrderDetailWrapper.getQuantity() - complementQuantity;
                                commonUtil.splitWrapper(wdtOrderDetailWrapper, remainQuantity,
                                        complementQuantity, activityQuantitySingleDo.getPriceCost());
                                break l2;
                            }
                        }
                        if (count < stillNeedUpdateCount) {
                            offset = offset + 50;
                        }
                    }
                    if (CollUtil.isNotEmpty(updateIds)) {
                        iWdtOrderDetailWrapperService.lambdaUpdate()
                                .set(WdtOrderDetailWrapper::getPrice, activityQuantitySingleDo.getPriceCost())
                                .in(WdtOrderDetailWrapper::getId, updateIds).update();
                    }
                }
            } catch (Exception e) {
                log.error("ActivityQuantityPrice doPriceProcess singleHandler update sku price fail.val:{}", activityQuantitySingleDo, e);
            } finally {
                semaphore.release();
                countDownLatch.countDown();
            }

        }));

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("ActivityQuantityPrice singleHandler countDownLatch await", e);
        }
    }
}
