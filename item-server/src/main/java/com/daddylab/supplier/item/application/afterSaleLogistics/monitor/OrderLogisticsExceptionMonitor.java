package com.daddylab.supplier.item.application.afterSaleLogistics.monitor;

import com.daddylab.supplier.item.application.afterSaleLogistics.dto.LogisticExceptionRes;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsTrace;

public interface OrderLogisticsExceptionMonitor {

  default LogisticExceptionRes process(Long currentTurnTime, OrderLogisticsTrace trace) {
    return process(new ProcessContext(currentTurnTime, trace));
  }

  LogisticExceptionRes process(ProcessContext context);
}
