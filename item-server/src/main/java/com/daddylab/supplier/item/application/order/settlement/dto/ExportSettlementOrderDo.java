package com.daddylab.supplier.item.application.order.settlement.dto;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.FormulaData;
import com.alibaba.excel.metadata.data.WriteCellData;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR> up
 * @date 2023年09月12日 10:27 AM
 */
@Data
public class ExportSettlementOrderDo {

    private String sort;
    private String cycle;
    private String skuCode;
    private String itemName;
    private String specifications;
    private String unit;
    private String settlementPrice;
    private WriteCellData<Integer> deliverQuantity;
    private WriteCellData<Integer> currentMonthRefundQuantity;
    private WriteCellData<Integer> crossMonthRefundQuantity;
//    private String settlementQuantityVal;
    private WriteCellData<String> settlementQuantity;
    private WriteCellData<String> settlementAmount;
    private String afterSalesCost;
//    private String finalAmountVal;
    private WriteCellData<String> finalAmount;
    private String remark;

    private String settlementAmountVal;


//    private WriteCellData<String> remark;

//    public static ExportSettlementOrderDo ofPageVo(FormDetailPageVo val) {
//        ExportSettlementOrderDo excelDo = new ExportSettlementOrderDo();
//        excelDo.setCycle(val.getCycle());
//        excelDo.setSkuCode(val.getSkuCode());
//        excelDo.setItemName(val.getItemName());
//        excelDo.setSpecifications(val.getSpecifications());
//        excelDo.setUnit(val.getUnit());
//        excelDo.setDeliverQuantity(val.getDeliverQuantity().toString());
//        excelDo.setCurrentMonthRefundQuantity(val.getCurrentMonthRefundQuantity().toString());
//        excelDo.setCrossMonthRefundQuantity(val.getCrossMonthRefundQuantity().toString());
//        excelDo.setSettlementPrice(val.getSettlementPrice().toString());
//        excelDo.setSettlementQuantity(val.getSettlementQuantity().toString());
//        excelDo.setSettlementAmount(val.getSettlementAmount().toString());
//        excelDo.setAfterSalesCost(val.getAfterSalesCost().toString());
//        excelDo.setFinalAmount(val.getFinalAmount().toString());
//        excelDo.setRemark(val.getRemark());
//        return excelDo;
//    }

    public static ExportSettlementOrderDo ofPageVo(int index, FormDetailPageVo val, int scale, RoundingMode roundingMode) {
        ExportSettlementOrderDo excelDo = new ExportSettlementOrderDo();
        excelDo.setCycle(val.getCycle());
        excelDo.setSkuCode(val.getSkuCode());
        excelDo.setItemName(val.getItemName());
        excelDo.setSpecifications(val.getSpecifications());
        excelDo.setUnit(val.getUnit());

        WriteCellData<Integer> deliverQuantityCell = new WriteCellData<>();
        deliverQuantityCell.setNumberValue(new BigDecimal(val.getDeliverQuantity()));
        deliverQuantityCell.setType(CellDataTypeEnum.NUMBER);
        excelDo.setDeliverQuantity(deliverQuantityCell);

        WriteCellData<Integer> currentMonthRefundQuantityCell = new WriteCellData<>();
        currentMonthRefundQuantityCell.setType(CellDataTypeEnum.NUMBER);
        currentMonthRefundQuantityCell.setNumberValue(new BigDecimal(val.getCurrentMonthRefundQuantity()));
        excelDo.setCurrentMonthRefundQuantity(currentMonthRefundQuantityCell);

        WriteCellData<Integer> crossMonthRefundQuantityCell = new WriteCellData<>();
        crossMonthRefundQuantityCell.setType(CellDataTypeEnum.NUMBER);
        crossMonthRefundQuantityCell.setNumberValue(new BigDecimal(val.getCrossMonthRefundQuantity()));
        excelDo.setCrossMonthRefundQuantity(crossMonthRefundQuantityCell);

        /*excelDo.setDeliverQuantity(val.getDeliverQuantity());
        excelDo.setCurrentMonthRefundQuantity(val.getCurrentMonthRefundQuantity());
        excelDo.setCrossMonthRefundQuantity(val.getCrossMonthRefundQuantity());*/

        BigDecimal settlementPrice = val.getSettlementPrice().setScale(scale, roundingMode);
        excelDo.setSettlementPrice(settlementPrice.toString());

        FormulaData quantityFormulaData = new FormulaData();
        String quantityFormula = StrUtil.format("H{}-I{}-J{}", index + 3, index + 3, index + 3);
        quantityFormulaData.setFormulaValue(quantityFormula);
        WriteCellData<String> quantityWriteCellData = new WriteCellData<>();
        quantityWriteCellData.setFormulaData(quantityFormulaData);
        excelDo.setSettlementQuantity(quantityWriteCellData);
//        excelDo.setSettlementQuantityVal(val.getSettlementQuantity().toString());

        BigDecimal settlementAmount = settlementPrice.multiply(new BigDecimal(val.getSettlementQuantity()))
                .setScale(scale, RoundingMode.HALF_UP);
        FormulaData formulaData = new FormulaData();
        String formula = StrUtil.format("G{}*K{}", index + 3, index + 3);
        formulaData.setFormulaValue(formula);
        WriteCellData<String> writeCellData = new WriteCellData<>();
        writeCellData.setFormulaData(formulaData);
        excelDo.setSettlementAmount(writeCellData);
        excelDo.setSettlementAmountVal(settlementAmount.toString());

        BigDecimal afterSalesCost = val.getAfterSalesCost().setScale(scale, roundingMode);
        excelDo.setAfterSalesCost(afterSalesCost.toString());


        FormulaData finalAmount = new FormulaData();
        finalAmount.setFormulaValue(StrUtil.format("L{}+M{}", index + 3, index + 3));
        WriteCellData<String> setFormulaValueCellData = new WriteCellData<>();
        setFormulaValueCellData.setFormulaData(finalAmount);
        excelDo.setFinalAmount(setFormulaValueCellData);

//        BigDecimal finalAmountVal = settlementAmount.add(afterSalesCost).setScale(2, roundingMode);
//        excelDo.setFinalAmountVal(finalAmount.toString());

//        String remark = StrUtil.isBlank(val.getRemark()) ? "" : val.getRemark();
//        String source = StrUtil.isBlank(val.getSource()) ? "" : val.getSource();
//        String rStr = StrUtil.format("备注:{}。来源:{}", remark, source);
//        excelDo.setRemark(rStr);
        excelDo.setRemark(val.getRemark());
        return excelDo;
    }

    public static void main(String[] args) {
        String s1 = "备注";
        String s2 = "来源";

        String remark = StrUtil.isBlank(s1) ? "" : s1;
        String source = StrUtil.isBlank(s2) ? "" : s2;
        String rStr = StrUtil.format("备注:{}。来源:{}", remark, source);
        System.out.println(rStr);

    }
}
