package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import com.daddylab.ark.sailor.item.common.base.vo.BaseVO;
import com.daddylab.ark.sailor.item.domain.po.ItemSku;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 商品规格列表VO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021/4/29 4:01 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@ApiModel(value = "SkuPageListItemVO", description = "商品规格列表VO")
public class SkuPageListItemVO extends BaseVO<ItemSku> {

    public SkuPageListItemVO(ItemSku itemSku) {
        super(itemSku);
    }

    @ApiModelProperty(value = "店铺ID")
    private Long shopId;

    @ApiModelProperty(value = "上架状态 0未上架 1已上架 3已下架 ")
    private Integer shelfStatus;

    @ApiModelProperty(value = "商品ID")
    private Long itemId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "商品编码")
    private String itemNo;

    @ApiModelProperty(value = "商品主图")
    private String itemImage;

    @ApiModelProperty(value = "商品价格")
    private BigDecimal minUnitPrice;

    @ApiModelProperty(value = "商家编码")
    private String skuNo;

    @ApiModelProperty(value = "sku主图")
    private String image;

    @ApiModelProperty(value = "销售价格")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "划线价格")
    private BigDecimal advisePrice;

    @ApiModelProperty(value = "积分价格")
    private Integer pointPrice;

    @ApiModelProperty(value = "重量")
    private BigDecimal weight;

    @ApiModelProperty(value = "库存")
    private Integer stock;

    @ApiModelProperty(value = "商品条形码")
    private String barCode;

    @ApiModelProperty(value = "是否显示")
    private Integer isShow;

    @ApiModelProperty(value = "甲醛仪商品-关联卡券ID")
    private Long driftCouponId;

    @ApiModelProperty(value = "限购数量")
    private Integer limitNum;

    @ApiModelProperty(value = "限购生效时间")
    private Long limitTime;

    @ApiModelProperty(value = "限购周期 1 终生 2 每年 3 每月 4 每周 5 每天")
    private Integer limitCycle;

    @ApiModelProperty(value = "是否仅会员可买")
    private Integer isVipBuy;



}
