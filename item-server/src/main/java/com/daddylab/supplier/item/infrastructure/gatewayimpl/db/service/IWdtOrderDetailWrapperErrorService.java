package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapperError;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * wdt订单信息清洗错误信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
public interface IWdtOrderDetailWrapperErrorService extends IDaddyService<WdtOrderDetailWrapperError> {

    void asyncSave(WdtOrderDetailWrapperError error);

}
