package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 新品商品-商品抽屉-直播话术审核表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Data
@EqualsAndHashCode(callSuper = false, of = "id")
public class ItemDrawerLiveVerbal implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    private String name;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 商品抽屉id
     */
    private Long itemDrawerId;

    /**
     * 直播话术状态 0:未进入审核流程/未知 5:待法务审核 10:待QC审核 100:审核完成
     */
    private ItemAuditStatus liveVerbalTrickStatus;

    /**
     * 直播话术
     */
    private String liveVerbalTrick;

    /**
     * 直播话术负责人
     */
    private Long principalId;


}
