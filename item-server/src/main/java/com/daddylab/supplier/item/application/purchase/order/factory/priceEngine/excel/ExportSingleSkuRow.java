package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2022年11月15日 2:57 PM
 */
@Data
public class ExportSingleSkuRow {

    @ExcelProperty(value = "SPU")
    private String spuCode;

    @ExcelProperty(value = "商家编码")
    private String skuCode;

    @ExcelProperty("实发单品数量")
    private Integer quantity;

    @ExcelProperty("按价格优惠结算成本")
    private String price;

    @ExcelProperty("活动开始时间")
    private String activityStartTime;

    @ExcelProperty("活动结束时间")
    private String activityEndTime;

}
