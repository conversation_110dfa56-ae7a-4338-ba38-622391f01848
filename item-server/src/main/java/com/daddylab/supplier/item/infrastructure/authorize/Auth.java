package com.daddylab.supplier.item.infrastructure.authorize;

import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Auth {

    @AliasFor("resource")
    String value() default "";
    /**
     * 权限校验资源标识
     */
    @AliasFor("value")
    String resource() default "";

    /**
     * 是否不需要进行权限校验
     */
    boolean noAuth() default false;
}
