package com.daddylab.supplier.item.application.item.itemBatchProc;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/11/16
 */
@Configuration
@Data
@ConfigurationProperties(prefix = "item-batch-proc")
@RefreshScope
public class ItemBatchProcConfig {
    private int maxNumLimit = 500;
    private int timeout = 600;
}
