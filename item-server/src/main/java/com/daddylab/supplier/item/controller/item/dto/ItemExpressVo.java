package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/14 4:49 下午
 * @description
 */
@Data
@ApiModel("快递模板返回封装")
public class ItemExpressVo implements Serializable {


    private static final long serialVersionUID = 3904817852265278130L;

    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("始发地")
    private String fromArea;
    @ApiModelProperty("快递公司")
    private String expressCompany;
    @ApiModelProperty("包邮区")
    private String freeArea;
    @ApiModelProperty("不包邮区域")
    private String chargeArea;
    @ApiModelProperty("快递模板")
    private String area;
    @ApiModelProperty("备注")
    private String remark;

}
