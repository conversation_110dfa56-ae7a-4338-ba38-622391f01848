package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NuoNuoInvoiceRequest;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.NuoNuoInvoiceRequestMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INuoNuoInvoiceRequestService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户诺诺开票请求参数 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Service
public class NuoNuoInvoiceRequestServiceImpl extends DaddyServiceImpl<NuoNuoInvoiceRequestMapper, NuoNuoInvoiceRequest> implements INuoNuoInvoiceRequestService {


    @Override
    public List<NuoNuoInvoiceRequest> getByOrderNo(String orderNo) {
        return this.lambdaQuery()
                .eq(NuoNuoInvoiceRequest::getOrderNo, orderNo)
                .orderByDesc(NuoNuoInvoiceRequest::getId)
                .list();
    }


}
