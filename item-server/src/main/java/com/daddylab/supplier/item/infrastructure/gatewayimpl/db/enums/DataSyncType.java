package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/1/4
 */
@AllArgsConstructor
@Getter
public enum DataSyncType implements IEnum<Integer> {
    /**
     * 旺店通平台商品
     */
    WDT_PLATFORM_GOODS(1, "旺店通平台商品"),
    /**
     * 我们自己的平台商品
     */
    PLATFORM_ITEM(2, "我们自己的平台商品"),
    ;
    private final Integer value;
    private final String desc;
}
