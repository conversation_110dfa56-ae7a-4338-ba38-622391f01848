package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 入驻活动价格(商品纬度)
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EntryActivityPriceItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic(value = "0", delval = "id")
    private Long isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 所属年月(2024-10)
     */
    private String month;

    /**
     * 供应商id
     */
    private Long providerId;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 商品编码
     */
    private String itemCode;

    /**
     * 状态 -1 待发起 0 待确认 1 已确认 2 存在异议
     */
    private Integer status;

    /**
     * 确认ID
     */
    private Long confirmId;


}
