package com.daddylab.supplier.item.domain.staff.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.Value;

/** 员工简单信息 */
@ApiModel("员工信息")
@Data
@Value
@EqualsAndHashCode(of = "userId")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StaffBrief {

    /** 用户id */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /** 姓名 */
    @ApiModelProperty(value = "姓名")
    private String realName;

    /** 花名 */
    @ApiModelProperty(value = "花名")
    private String nickname;

    /**
     * 企微用户ID
     */
    private String qwUserId;

    @ApiModelProperty(value = "岗位")
    private String post;

    @Override
    public String toString() {
        return nickname;
    }

    @JsonIgnore
    public boolean isEmptyObject() {
        return userId == null || userId == 0L;
    }

    public static StaffBrief empty() {
        return new StaffBrief();
    }

    public static StaffBrief systemUser() {
        final StaffBrief staffBrief = new StaffBrief();
        staffBrief.setUserId(0L);
        staffBrief.setRealName("系统");
        staffBrief.setNickname("系统");
        staffBrief.setQwUserId("");
        staffBrief.setPost("");

        return staffBrief;
    }
}
