package com.daddylab.supplier.item.infrastructure.config.mybatisPlus;

/**
 * <AUTHOR> up
 * @date 2022年07月29日 11:46 AM
 */
public enum BaseFuncEnum implements com.github.yulichang.wrapper.enums.BaseFuncEnum {

    DISTINCT("DISTINCT(%s)");

    private final String sql;

    BaseFuncEnum(String sql) {
        this.sql = sql;
    }

    @Override
    public String getSql() {
        return this.sql;
    }
}
