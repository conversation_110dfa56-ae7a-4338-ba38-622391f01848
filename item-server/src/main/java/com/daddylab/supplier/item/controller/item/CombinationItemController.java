package com.daddylab.supplier.item.controller.item;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.item.combinationItem.CombinationItemBizService;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.CombinationItemSaveCmd;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.ComposeSkuCmd;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.ProportionCmd;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.CombinationItemNamePageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.CombinationItemPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.*;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.infrastructure.bizLevelDivision.BizLevelFilter;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BizUnionTypeEnum;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 组合商品控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/7 9:57 上午
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/combination")
@Api(value = "组合商品相关API", tags = "组合商品相关API")
@RequiredArgsConstructor
public class CombinationItemController {

    final CombinationItemBizService combinationItemBizService;

    final OperateLogBizService operateLogBizService;

    @ResponseBody
    @ApiOperation("sku明细分页列表")
    @PostMapping("/pageSku")
    public PageResponse<ComposeSkuVO> pageSku(@RequestBody ComposeSkuPageQuery query) {
        query.setWithPrice(UserPermissionJudge.canViewComposerSkuPrice());
        query.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(query.getBusinessLine()));
        return combinationItemBizService.pageSku(query);
    }

    @ResponseBody
    @ApiOperation("保存组合商品")
    @PostMapping("/save")
    public SingleResponse<Boolean> save(@RequestBody @Validated CombinationItemSaveCmd cmd) {
        return combinationItemBizService.save(cmd);
    }

    /**
     * 编辑页面和查看详情页面 都会查询这个接口进行数据回显。
     * <p>
     * 编辑页面能看到所有的数据，包括组合商品价格
     * 查看详情页面就不展示价格了，需要前端另外查询价格接口
     *
     * @param id
     * @return
     */
    @ResponseBody
    @ApiOperation("组合商品详情")
    @GetMapping("/view")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", dataType = "long", value = "组合商品id"),
            @ApiImplicitParam(name = "isEdit", dataType = "boolean", value = "编辑页面 为true")
    })
    public SingleResponse<CombinationItemWithPriceVO> view(Long id, Boolean isEdit) {
        return combinationItemBizService.get(id, isEdit);
    }

    @ResponseBody
    @ApiOperation("组合商品价格详情")
    @GetMapping("/viewPrice")
    @ApiImplicitParam(name = "id", value = "组合商品id")
    public SingleResponse<CombinationItemPriceVO> viewPrice(Long id) {
        return combinationItemBizService.getPrice(id);
    }

    @ResponseBody
    @ApiOperation("删除组合商品")
    @GetMapping("/delete")
    @ApiImplicitParam(name = "id", value = "组合商品id")
    public SingleResponse<Boolean> delete(Long id) {
        return combinationItemBizService.delete(id);
    }

    @ResponseBody
    @ApiOperation("组合商品分页")
    @PostMapping("/pageItem")
    public PageResponse<CombinationPageVO> pageItem(@RequestBody CombinationItemPageQuery query) {
        return combinationItemBizService.pageItem(query);
    }

    @ResponseBody
    @ApiOperation("组合商品名字下拉")
    @PostMapping("/dropDownName")
    public PageResponse<CombinationItemNameVO> pageItem(@RequestBody CombinationItemNamePageQuery query) {
        return combinationItemBizService.pageItemName(query);
    }

    @ResponseBody
    @ApiOperation("组合商品导出")
    @PostMapping("/export")
    public SingleResponse<Boolean> export(@RequestBody CombinationItemPageQuery query) {
        combinationItemBizService.export(query);
        return SingleResponse.of(true);
    }


    @GetMapping(value = "/operateLogs")
    @ApiOperation("操作日志")
    @ApiImplicitParam(name = "targetId", value = "组合商品id")
    public Response operateLogs(Long targetId) {
        return operateLogBizService.getOperateLogs(OperateLogTarget.COMBINATION_ITEM, targetId);
    }

    @GetMapping(value = "/priceLogs")
    @ApiOperation("动价记录")
    @ApiImplicitParam(name = "targetId", value = "组合商品id")
    public Response priceLogs(Long targetId) {
        return operateLogBizService.getOperateLogs(OperateLogTarget.COMBINATION_ITEM_PRICE, targetId);
    }

    @ResponseBody
    @ApiOperation("计算价格")
    @PostMapping("/calculatePrice")
    public SingleResponse<Map<String, BigDecimal>> calculatePrice(@RequestBody List<ComposeSkuCmd> query) {
        return combinationItemBizService.calculatePrice(query);
    }


    @ResponseBody
    @ApiOperation("计算金额占比")
    @PostMapping("/calculateProportion")
    public MultiResponse<ProportionVO> calculateProportion(@RequestBody @Validated List<ProportionCmd> cmdList) {
        log.info("组合装-金额占比计算，入参:{}", JsonUtil.toJson(cmdList));
        return MultiResponse.of(combinationItemBizService.calculateProportion(cmdList));
    }

}
