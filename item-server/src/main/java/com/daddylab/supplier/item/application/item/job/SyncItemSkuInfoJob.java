//package com.daddylab.supplier.item.application.item.job;
//
//import cn.hutool.core.util.StrUtil;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.daddylab.job.core.context.XxlJobHelper;
//import com.daddylab.job.core.handler.annotation.XxlJob;
//import com.daddylab.supplier.item.common.enums.PoolEnum;
//import com.daddylab.supplier.item.common.util.ThreadUtil;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
//import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
//import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
//import com.daddylab.supplier.item.infrastructure.kingdee.dto.KingDeeSkuResp;
//import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
//import com.daddylab.supplier.item.infrastructure.timing.StopWatch;
//import com.github.pagehelper.PageHelper;
//import com.github.pagehelper.PageInfo;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.LinkedList;
//import java.util.List;
//import java.util.Optional;
//
///**
// * 将 库中 所有存在金蝶Id的sku 全部往金蝶同步一遍。
// * 将erp的数据洗到 金蝶系统。
// *
// * <AUTHOR> up
// * @date 2022年08月01日 5:41 PM
// */
//@Slf4j
//@Service
//public class SyncItemSkuInfoJob {
//
//    @Autowired
//    IItemSkuService iItemSkuService;
//
//    @Autowired
//    ReqTemplate reqTemplate;
//
//    @Autowired
//    KingDeeTemplate kingDeeTemplate;
//
//    @XxlJob("syncItemSkuInfoJob")
//    public void syncItemSkuInfoJob() {
//
//        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
//            StopWatch stopWatch = new StopWatch();
//            stopWatch.start();
//            long count = 0;
//
//            int size = 1000;
//            int index = 0;
//
//            LambdaQueryWrapper<ItemSku> wrapper = Wrappers.lambdaQuery();
//            wrapper.ne(ItemSku::getKingDeeId, "").isNotNull(ItemSku::getKingDeeId);
//            PageInfo<ItemSku> pageInfo = PageHelper.startPage(index, size).doSelectPageInfo(() -> iItemSkuService.list(wrapper));
//            int pageNum = pageInfo.getPages();
//            int startPageNum = 0;
//
//            while (startPageNum <= pageNum) {
//                XxlJobHelper.log("正在批量同步数据，页码:{}", startPageNum);
//                List<Long> errorId = new LinkedList<>();
//                for (ItemSku record : pageInfo.getList()) {
//                    try {
//                        Optional<KingDeeSkuResp> kingDeeSkuResp = reqTemplate.querySkuByNo(record.getSkuCode());
//                        if (kingDeeSkuResp.isPresent()) {
//                            log.info("--- sync count:{} --- ", count++);
//                            kingDeeTemplate.handler(ApiEnum.UPDATE_SKU, record.getId(), record.getKingDeeId());
//                        }
//                    } catch (Exception e) {
//                        log.error("将sku同步到kingDee异常,id:{},error:{}", record.getId(), e.getMessage(), e);
//                        errorId.add(record.getId());
//                    }
//                }
//                if (CollectionUtils.isNotEmpty(errorId)) {
//                    String s = StrUtil.join(",", errorId);
//                    XxlJobHelper.log("以下skuId同步到KingDee异常,{}", s);
//                }
//
//                startPageNum = startPageNum + 1;
//                index = index + 1;
//                pageInfo = PageHelper.startPage(index, size).doSelectPageInfo(() -> iItemSkuService.list(wrapper));
//            }
//
//            stopWatch.stop();
//            XxlJobHelper.log("同步sku到kingDee流程结束。耗时:{}", stopWatch.getTotalTimeSeconds());
//
//        });
//    }
//
//}
