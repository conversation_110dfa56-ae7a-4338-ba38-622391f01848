package com.daddylab.supplier.item.domain.drawer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(value = "ItemApprovalAdvicesVO", description = "商品审批意见VO集合包装")
public class ItemApprovalAdvicesVO implements Serializable {

    private static final long serialVersionUID = 3927794904969310945L;
    @ApiModelProperty(value = "标题建议")
    private List<ItemApprovalAdviceVO> titleAdvice;

    @ApiModelProperty(value = "文案建议")
    private List<ItemApprovalAdviceVO> copyAdvice;

    @ApiModelProperty(value = "主图建议")
    private List<ItemApprovalAdviceVO> mainImageAdvice;

}
