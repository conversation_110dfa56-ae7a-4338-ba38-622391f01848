package com.daddylab.supplier.item.application.handingsheet;


import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.controller.handingsheet.dto.HandingSheetItemPageQuery;
import com.daddylab.supplier.item.controller.handingsheet.dto.HandingSheetParam;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.types.handingsheet.*;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/22
 */
public interface HandingSheetV2BizService {


    void excelTemplate(List<Integer> platforms, HttpServletResponse response);

    List<HandingSheetItemVO> importExcel(@Nullable Long id, List<Integer> platforms, InputStream inputStream);

    PageResponse<HandingSheetItemVO> handingSheetItemQuery(HandingSheetItemPageQuery query);

    SingleResponse<Boolean> syncBackPriceToNewGoods(Long id);

    Long saveOrEdit(HandingSheetParam param);

    @Transactional
    void saveItems(HandingSheetSaveItemsCmd cmd);

    void confirm(HandingSheetConfirmCmd cmd);

    MultiResponse<HandingSheetItemVO> bringOut(HandingSheetItemBringOutCmd cmd);

    SingleResponse<HandingSheetItemSkuBringOutVO> bringOutForSkuCode(String skuCode);

    SingleResponse<HandingSheetItemSkuBringOutVO> bringOutForSkuCode(String skuCode, List<Integer> platforms);

    void copyInfo(Long handingSheetId, Long sourceId, List<Long> targetIds);

    MultiResponse<HandingSheetItemVO> refreshItemView(List<HandingSheetItemVO> views);

    MultiResponse<OperateLog> operateLogs(Long targetId, Integer type);

    void exportItemInfoExcel(HandingSheetItemPageQuery pageQuery);

    void toBeConfirmNoticeSummary();
}
