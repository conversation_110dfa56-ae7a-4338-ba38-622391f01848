package com.daddylab.supplier.item.application.item;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/9/7
 */
@ConfigurationProperties(prefix = "banniu.shipping-warehouse")
@EnableConfigurationProperties(BanniuShippingWarehouseConfig.class)
@Data
@Configuration
public class BanniuShippingWarehouseConfig {

    /**
     * 班牛系统用户ID（默认 ERP商品中心）
     */
    private Integer sysUserId = 192010020;

    /**
     * 班牛发货仓 ProjectId（默认 14250 为 发货仓【IT测试】）
     */
    private Integer shippingWarehouseProjectId = 14250;

    /**
     * 班牛发货仓同步不更新完结用户ID
     */
    private Boolean notUpdateCompleteUserId = false;
}
