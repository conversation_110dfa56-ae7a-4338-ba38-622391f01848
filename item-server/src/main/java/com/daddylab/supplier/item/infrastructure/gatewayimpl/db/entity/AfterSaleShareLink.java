package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 售后分享链接信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AfterSaleShareLink implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic(delval = "id")
    private Integer isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 0正常，1禁用
     */
    private Integer status;

    /**
     * 有效期，单位天
     */
    private Integer validityPeriod;

    /**
     * 0存在有效期，1长期有效
     */
    private Integer alwaysValidity;

    /**
     * 链接编码
     */
    private String no;

    /**
     * 链接名称
     */
    private String name;

    /**
     * 链接参数
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private JSONObject linkParams;

    private String linkVal;

    private Long expiredTimePoint;


}
