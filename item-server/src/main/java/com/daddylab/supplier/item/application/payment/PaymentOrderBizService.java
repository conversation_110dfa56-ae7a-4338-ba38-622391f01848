package com.daddylab.supplier.item.application.payment;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.payment.dto.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentOrderWriteOffLog;
import com.daddylab.supplier.item.types.oa.OaCgfkCallback;
import java.util.List;

/** 采购管理-付款单列表 业务接口 */
public interface PaymentOrderBizService {

  /**
   * 列表分页查选你
   *
   * @param pageQuery
   * @return
   */
  PageResponse<PaymentPageVo> page(PaymentPageQuery pageQuery);

  /**
   * 查看付款申请单详情
   *
   * @param id
   * @return
   */
  SingleResponse<PaymentApplyOrderViewVo> view(Long id);

  /**
   * 查看付款申请单详情 - 付款明细
   *
   * @param id
   * @return
   */
  MultiResponse<PaymentApplyDetailVo> viewDetail(Long id);

  /**
   * 付款申请单，新增/编辑 保存
   *
   * @param cmd
   * @return
   */
  SingleResponse<Long> save(PaymentApplyOrderSaveCmd cmd);

  /**
   * 删除付款单
   *
   * @param id
   * @return
   */
  SingleResponse<Boolean> remove(Long id);

  /**
   * 付款单提交审核
   *
   * @param id
   * @return
   */
  Response submit(Long id);

  /**
   * OA流程回调
   *
   * @param callback 回调
   */
  Response oaCallback(OaCgfkCallback callback);

  /**
   * 同步单据到金蝶
   *
   * @param id
   * @return
   */
  Response sync(Long id);

  /**
   * 付款申请导出
   *
   * @param cmd
   * @return
   */
  Response export(PaymentPageQuery pageQuery);

  /**
   * 当删除某个付款明细下的某个SKU时，刷新计算这笔付款明细的应付金额
   *
   * @param cmd
   * @return
   */
  SingleResponse<PaymentDetailAmountVO> refreshRightAmount(RefreshRightAmountCmd cmd);

  /**
   * 查询 单据的 付款单相关信息
   *
   * @param relatedNo 结算单好或者采购单号
   * @return
   */
  SingleResponse<String> getPaymentInfoByRelatedNo(String relatedNo);

  /**
   * 根据付款单id删除数据
   *
   * @param ids
   * @return
   */
  Response deleteByFormIds(List<Long> ids);

  /**
   * 获取付款单的冲销流程日志
   *
   * @param id 付款单 ID
   * @return
   */
  MultiResponse<PaymentOrderWriteOffLog> getWriteOffLog(Long id);

  Response withdraw(Long id, String comment);

  /**
   * 手动触发冲销流程
   *
   * @param id
   * @return
   */
  MultiResponse<String> manualWriteOff(Long id);

  /**
   * 回滚结算单明细中，SKU 的冲销状态
   *
   * @param formId 结算单 ID
   * @return
   */
  Response rollbackSku(Long formId);

  /**
   * 修改付款单的采购人员信息
   *
   * @param id
   * @param userId
   * @return
   */
  SingleResponse<Boolean> updateBuyer(Long id, Long userId);

  MultiResponse<PrintOrderVo> print(PrintCmd cmd);

  Response rollbackAudit(Long id);


}
