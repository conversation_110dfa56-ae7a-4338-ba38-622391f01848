package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.log;

import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
import com.google.common.eventbus.Subscribe;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> up
 * @date 2023年10月25日 6:04 PM
 */
@Service
@EventBusListener(value = "purchasePriceLogListener")
public class PriceLogListener {




    @Subscribe
    public void listener(PriceLogEvent priceLogEvent){

    }


}
