package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.controller.item.dto.ItemLaunchPlanLinkQueryParam;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemLaunchPlanPrincipalDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemLaunchPlanRefItemDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchPlanItemRef;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 商品上新计划和商品的关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-30
 */
public interface ItemLaunchPlanItemRefMapper extends DaddyBaseMapper<ItemLaunchPlanItemRef> {

    Long selectPageListCount(@Param(Constants.WRAPPER) Wrapper<ItemLaunchPlanItemRef> wrapper,
                             @Param("param") ItemLaunchPlanLinkQueryParam param);

    IPage<ItemLaunchPlanItemRef> selectPageList(@Param("page") Page<ItemLaunchPlanItemRef> page,
                                                @Param(Constants.WRAPPER) Wrapper<ItemLaunchPlanItemRef> wrapper,
                                                @Param("param") ItemLaunchPlanLinkQueryParam param);

    Long getLinkItemOnNum(@Param("planId") Long planId);

    List<ItemLaunchPlanPrincipalDO> selectPrincipalByPlanId(@Param("planId") Long planId);

    List<ItemLaunchPlanRefItemDO> selectItemNameByRefIds(@Param("refIdList") List<Long> refIds);

    int undelete(Collection<Long> refIds);
}
