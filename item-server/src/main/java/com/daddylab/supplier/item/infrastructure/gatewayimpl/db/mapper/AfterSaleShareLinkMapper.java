package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSaleShareLink;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;

/**
 * <p>
 * 售后分享链接信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
public interface AfterSaleShareLinkMapper extends DaddyBaseMapper<AfterSaleShareLink> {

    Long getNewOne();
}
