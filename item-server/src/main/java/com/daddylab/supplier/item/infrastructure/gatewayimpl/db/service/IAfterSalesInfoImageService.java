package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesInfoImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesImageType;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 售后异常件信息图片表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-17
 */
public interface IAfterSalesInfoImageService extends IDaddyService<AfterSalesInfoImage> {

    void saveImageByType(Long relatedId, List<String> imageUrls, AfterSalesImageType type);

    void removeThenInsertNewImageByType(Long relatedId, List<String> imageUrls,
            AfterSalesImageType type);

    Map<Long, List<String>> getImageUrlsRelateIdsAndType(Collection<Long> relateIds,
            AfterSalesImageType afterSalesImageType);

    List<String> getImageUrlsRelateIdAndType(Long relateId,
            AfterSalesImageType afterSalesImageType);
}
