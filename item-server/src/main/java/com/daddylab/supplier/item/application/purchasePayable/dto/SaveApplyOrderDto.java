package com.daddylab.supplier.item.application.purchasePayable.dto;

import com.daddylab.supplier.item.application.purchasePayable.vo.ApplyPayOrderDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年02月14日 4:36 PM
 */
@Data
@ApiModel("保存采购付款申请单数据请求参数")
public class SaveApplyOrderDto extends ApplyPayOrderDetailVO {

    private String remark;

    @ApiModelProperty("修正后应付总额")
    private BigDecimal fixedTotalAmount;

    @ApiModelProperty("sku维度的修正细节")
    private List<SaveApplyOrderDetailDto> detailCmdList;

    @ApiModelProperty("附件id")
    private Long additionalId;
}
