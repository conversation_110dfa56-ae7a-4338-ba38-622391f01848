package com.daddylab.supplier.item.infrastructure.userInfoCrypto;

import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import java.nio.charset.StandardCharsets;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/5/26
 */
@Component
@Slf4j
public class UserInfoCryptoImpl implements UserInfoCrypto {

    private final Config config;

    public UserInfoCryptoImpl(
            Config config) {
        this.config = config;
    }

    public static byte[] hexToBytes(String hex) {
        byte[] result = new byte[hex.length() / 2];
        int j = 0;
        for (int i = 0; i < hex.length(); i += 2) {
            result[j++] = (byte) Integer.parseInt(hex.substring(i, i + 2), 16);
        }
        return result;
    }

    public static String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder("");
        if (src == null || src.length <= 0) {
            return null;
        }
        for (byte b : src) {
            int v = b & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }

    @Override
    public String encrypt(String clearText) {
        try {
            if (StrUtil.isBlank(clearText)) {
                return "";
            }
            SecretKeySpec keySpec = new SecretKeySpec(config.getSecurityKey().getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance(config.getEncryptMode());
            IvParameterSpec iv = new IvParameterSpec(config.getIvKey().getBytes(StandardCharsets.UTF_8));
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, iv);
            return bytesToHexString(cipher.doFinal(clearText.getBytes()));
        } catch (Exception e) {
            log.error("用户信息加密失败 明文：{} 异常信息：{}", clearText, ExceptionUtil.getMessage(e));
        }
        return clearText;
    }

    @Override
    public String decrypt(String cipherText) {
        try {
            if (StrUtil.isBlank(cipherText)) {
                return "";
            }
            SecretKeySpec keySpec = new SecretKeySpec(
                    config.getSecurityKey().getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance(config.getEncryptMode());
            IvParameterSpec iv = new IvParameterSpec(
                    config.getIvKey().getBytes(StandardCharsets.UTF_8));
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, iv);
            return new String(cipher.doFinal(hexToBytes(cipherText)), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("用户信息解密失败 密文：{} 异常信息：{}", cipherText, ExceptionUtil.getMessage(e));
        }
        return cipherText;
    }
}
