package com.daddylab.supplier.item.application.kuaidaoyun;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.KdyCallback;
import com.daddylab.supplier.item.infrastructure.kuaidaoyun.types.KdySubscribeData;
import com.daddylab.supplier.item.types.kuaidaoyun.KdyCallbackRequest;

/**
 * <AUTHOR>
 * @since 2024/5/22
 */
public interface KdyBizService {

    long subscribe(KdySubscribeData subscribeData) throws BizException;

    String callback(KdyCallbackRequest request, boolean checkSign);

    void doCallback(KdyCallbackRequest request);

    SingleResponse<KdyCallback> getCallback(Long id);
}
