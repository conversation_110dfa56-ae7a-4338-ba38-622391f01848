package com.daddylab.supplier.item.application.order.settlement.dto;

import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2023年08月09日 3:31 PM
 */
@ApiModel("采购结算单详情表格数据")
@Data
public class FormDetailPageVo {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("周期")
    public String cycle;

    @ApiModelProperty("周期")
    public Long settlement;

    @ApiModelProperty("结算周期开始时间")
    private Long settlementStartDate;

    @ApiModelProperty("结算周期结束时间")
    private Long settlementEndDate;

    @ApiModelProperty("商品SKU")
    public String skuCode;

    @ApiModelProperty("商品名称")
    public String itemName;

    @ApiModelProperty("规格名称")
    public String specifications;

    @ApiModelProperty("单位")
    public String unit;

    @ApiModelProperty("订单发货数量")
    public Integer deliverQuantity;

    @ApiModelProperty("暂估单价")
    public BigDecimal temporaryPrice;

    @ApiModelProperty("暂估结算数量")
    public Integer temporaryQuantity;

    @ApiModelProperty("当月退货")
    public Integer currentMonthRefundQuantity;

    @ApiModelProperty("跨月退货")
    public Integer crossMonthRefundQuantity;

    @ApiModelProperty("结算单价")
    private BigDecimal settlementPrice;

    @ApiModelProperty("结算数量")
    private Integer settlementQuantity;

    @ApiModelProperty("结算金额")
    private BigDecimal settlementAmount;

    @ApiModelProperty("运费及售后分摊金额")
    private BigDecimal afterSalesCost;

    @ApiModelProperty("最终金额")
    private BigDecimal finalAmount;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("来源")
    private String source;

//    @ApiModelProperty("是否申请付款")
//    private Integer isPayApplied;

    @ApiModelProperty("明细所属的单据ID")
    private Long formId;

    @ApiModelProperty("明细所属的单据编码")
    private String formNo;

    @ApiModelProperty("业务类型")
    private List<CorpBizTypeDTO> corpBizType;
}
