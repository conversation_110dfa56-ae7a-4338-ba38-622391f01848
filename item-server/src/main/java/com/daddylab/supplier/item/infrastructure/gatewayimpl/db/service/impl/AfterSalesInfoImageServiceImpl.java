package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesInfoImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesImageType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AfterSalesInfoImageMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesInfoImageService;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 售后异常件信息图片表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-17
 */
@Service
public class AfterSalesInfoImageServiceImpl extends
        DaddyServiceImpl<AfterSalesInfoImageMapper, AfterSalesInfoImage> implements
        IAfterSalesInfoImageService {

    @Override
    public void saveImageByType(Long relatedId, List<String> imageUrls, AfterSalesImageType type) {
        if (CollUtil.isNotEmpty(imageUrls) && Objects.nonNull(relatedId) && Objects.nonNull(type)) {
            this.saveBatch(imageUrls.stream().map(imageUrl -> {
                AfterSalesInfoImage image = new AfterSalesInfoImage();
                image.setRelatedId(relatedId);
                image.setImage(imageUrl);
                image.setType(type.getValue());
                return image;
            }).collect(Collectors.toList()));
        }
    }

    @Override
    public void removeThenInsertNewImageByType(Long relatedId, List<String> imageUrls,
            AfterSalesImageType type) {
        QueryWrapper<AfterSalesInfoImage> wrapper = new QueryWrapper<>();
        wrapper.eq("related_id", relatedId);
        this.remove(wrapper);

        saveImageByType(relatedId, imageUrls, type);
    }

    @Override
    public Map<Long, List<String>> getImageUrlsRelateIdsAndType(Collection<Long> relateIds,
            AfterSalesImageType afterSalesImageType) {
        final List<AfterSalesInfoImage> imagePOs = lambdaQuery()
                .in(AfterSalesInfoImage::getRelatedId,
                        relateIds)
                .eq(AfterSalesInfoImage::getType,
                        afterSalesImageType).list();
        return imagePOs.stream().collect(
                Collectors.groupingBy(AfterSalesInfoImage::getRelatedId,
                        Collectors.mapping(AfterSalesInfoImage::getImage, Collectors.toList())));
    }

    @Override
    public List<String> getImageUrlsRelateIdAndType(Long relateId,
            AfterSalesImageType afterSalesImageType) {
        return lambdaQuery().eq(AfterSalesInfoImage::getRelatedId, relateId)
                .eq(AfterSalesInfoImage::getType, afterSalesImageType).list().stream()
                .map(AfterSalesInfoImage::getImage).collect(
                        Collectors.toList());
    }
}
