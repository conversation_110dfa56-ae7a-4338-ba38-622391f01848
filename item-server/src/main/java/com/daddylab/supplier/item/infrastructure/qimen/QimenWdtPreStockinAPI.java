package com.daddylab.supplier.item.infrastructure.qimen;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.qimencloud.api.QimenCloudClient;
import com.qimencloud.api.scene3ldsmu02o9.request.WdtWmsStockinPrestockinSearchRequest;
import com.qimencloud.api.scene3ldsmu02o9.response.WdtWmsStockinPrestockinSearchResponse;
import com.taobao.api.ApiException;

/**
 * <AUTHOR>
 * @since 2022/8/22
 */
public class QimenWdtPreStockinAPI extends QimenWdtAPIBase {

    public QimenWdtPreStockinAPI(QimenCloudClient qimenClient,
            QimenConfig qimenConfig,
            WdtConfig wdtConfig, int configIndex) {
        super(qimenClient, qimenConfig, wdtConfig, configIndex);
    }

    /**
     * 通过奇门接口查询旺店通退货预入库单
     *
     * @param params    旺店通请求参数封装
     * @param pageIndex pageIndex
     * @param pageSize  pageSize
     * @return 奇门返回的数据结构封装
     */
    public WdtWmsStockinPrestockinSearchResponse wmsStockinPrestockinSearch(
            WdtWmsStockinPrestockinSearchRequest.Params params,
            long pageIndex,
            long pageSize) throws ApiException {
        return execute(WdtWmsStockinPrestockinSearchRequest.class, params, pageIndex, pageSize);
    }
}
