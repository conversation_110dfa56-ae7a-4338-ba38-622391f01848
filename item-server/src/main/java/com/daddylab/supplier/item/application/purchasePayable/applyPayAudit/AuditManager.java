package com.daddylab.supplier.item.application.purchasePayable.applyPayAudit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.exception.ExceptionFactory;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.application.purchasePayable.applyPayAudit.dto.*;
import com.daddylab.supplier.item.application.purchasePayable.service.PurchasePayableBizService;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PayApplyAuditProcess;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PayApplyAuditProcessNode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableApplyOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayApplyAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayableApplyOrderStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPayApplyAuditProcessNodeService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPayApplyAuditProcessService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchasePayableApplyOrderService;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.daddylab.supplier.item.application.purchasePayable.applyPayAudit.dto.NodeRoleType.*;

/**
 * <AUTHOR> up
 * @date 2023年02月16日 10:10 AM
 */
@Service
@Slf4j
public class AuditManager {

    private final static BigDecimal ONE_HUNDRED_THOUSAND = new BigDecimal(100000);
    private final static BigDecimal FIVE_HUNDRED_THOUSAND = new BigDecimal(500000);
    private final static BigDecimal ONE_MILLION = new BigDecimal(1000000);

    @Resource
    IPayApplyAuditProcessService iPayApplyAuditProcessService;

    @Resource
    IPayApplyAuditProcessNodeService iPayApplyAuditProcessNodeService;

    @Resource
    IPurchasePayableApplyOrderService iPurchasePayableApplyOrderService;

    @Resource
    AuditRoleConfig auditRoleConfig;

    @Resource
    PurchasePayableBizService purchasePayableBizService;


    private Map<String, List<AuditRoleConfigBo>> roleInfoMap = new HashMap<>(16);

    @PostConstruct
    public void initRolaInfoMap() {
        roleInfoMap = auditRoleConfig.getNodeRoles().stream().collect(Collectors.groupingBy(AuditRoleConfigBo::getRoleName));
    }

    private List<Long> getSuperman() {
        List<AuditRoleConfigBo> superman = roleInfoMap.get("SUPERMAN");
        return superman.stream().map(AuditRoleConfigBo::getUserId).collect(Collectors.toList());
    }

    private List<Long> getAllAuditRoleUserId() {
        return auditRoleConfig.getNodeRoles().stream().map(AuditRoleConfigBo::getUserId).collect(Collectors.toList());
    }

    /**
     * 根据申请付款的金额，构建审核流节点list
     * 0-10W。部门领导手动审批->vp自动审批->财务会计手动审批->财务经理自动审批->出纳审批
     * 10W-50W。部门领导手动审批->vp手动审批->财务会计手动审批->财务经理自动审批->出纳审批
     * 50W-100W。部门领导手动审批->vp手动审批->财务会计手动审批->财务经理手动审批->出纳审批
     * 100W以上。部门领导手动审批->vp手动审批->财务会计手动审批->财务经理手动审批->boos手动审批->出纳审批
     *
     * @param totalAmount
     * @return
     */
    private List<AuditNodeBo> createdProcessNodes(BigDecimal totalAmount) {
        List<AuditNodeBo> nodeList = new LinkedList<>();

        // 本部门上级审核
        AuditNodeBo node1 = AuditNodeBo.builder().nodeRoleType(DEPARTMENT_MANAGER)
                .auditStatus(PayApplyAuditStatus.WAITING)
                .build();
        nodeList.add(node1);

        // 大于10W，vp手动审批，小于10W，vp自动审核通过
        AuditNodeBo node2 = AuditNodeBo.builder().nodeRoleType(BUSINESS_GROUP_VP)
                .auditStatus(PayApplyAuditStatus.WAITING)
                .autoAudit(totalAmount.compareTo(ONE_HUNDRED_THOUSAND) < 0 ? 1 : 0)
                .build();
        nodeList.add(node2);

        // 财务会计审核
        AuditNodeBo node3 = AuditNodeBo.builder().nodeRoleType(FINANCIAL_ACCOUNTING)
                .auditStatus(PayApplyAuditStatus.WAITING)
                .build();
        nodeList.add(node3);

        if (totalAmount.compareTo(FIVE_HUNDRED_THOUSAND) >= 0) {
            // 大于50W，财务经理手动审核
            AuditNodeBo node4 = AuditNodeBo.builder().nodeRoleType(FINANCIAL_MANAGER)
                    .auditStatus(PayApplyAuditStatus.WAITING)
                    .build();
            nodeList.add(node4);
            // 大于100W，BOSS签字
            if (totalAmount.compareTo(ONE_MILLION) > 0) {
                AuditNodeBo node5 = AuditNodeBo.builder().nodeRoleType(FINAL_BOSS)
                        .auditStatus(PayApplyAuditStatus.WAITING)
                        .build();
                nodeList.add(node5);
            }
        } else {
            // 小于50W，财务经理自动审核通过
            AuditNodeBo node6 = AuditNodeBo.builder().nodeRoleType(FINANCIAL_MANAGER)
                    .auditStatus(PayApplyAuditStatus.WAITING).autoAudit(1)
                    .build();
            nodeList.add(node6);
        }

        // 最后一步，出纳人员处理
        AuditNodeBo node7 = AuditNodeBo.builder().nodeRoleType(CASHIER)
                .auditStatus(PayApplyAuditStatus.WAITING)
                .build();
        nodeList.add(node7);

        return nodeList;
    }

    /**
     * 构建审核任务，
     * 分支判断逻辑主要是申请付款的审核金额
     *
     * @param payApplyOrderId
     * @param totalAmount
     * @param creator
     */
    @Transactional(rollbackFor = Exception.class)
    public void createTask(Long payApplyOrderId, BigDecimal totalAmount, Long creator) {
        // 检测此付款申请单是否已经创建审核流程，不允许重复创建
        Integer count = iPayApplyAuditProcessService.lambdaQuery().eq(PayApplyAuditProcess::getApplyOrderId, payApplyOrderId)
                .count();
        if (count > 0) {
            throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "此付款申请单已经创建审核流程，不允许重复创建");
        }
        PurchasePayableApplyOrder payableApplyOrder = iPurchasePayableApplyOrderService.getById(payApplyOrderId);
        if (Objects.isNull(payableApplyOrder)) {
            throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "付款申请单查询为空，id非法");
        }
        // 审核流程发起人必须是付款申请单的创建人或者是众多审核人员之一
        boolean canCreate = payableApplyOrder.getCreatedUid().equals(creator) || getAllAuditRoleUserId().contains(creator);
        if (!canCreate) {
            throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "当前用户既不是申请付款单创建人员也不是审核人员，无操作权限");
        }

        List<AuditNodeBo> auditNodeBos = createdProcessNodes(totalAmount);

        List<PayApplyAuditProcess> saveProcessList = new LinkedList<>();
        List<PayApplyAuditProcessNode> saveNodeList = new LinkedList<>();

        // 根据创建任务的节点list,依次保存节点审核相关信息
        for (int i = 0; i < auditNodeBos.size(); i++) {
            // 此审核节点，初始化信息
            AuditNodeBo thisNodeBo = auditNodeBos.get(i);
            NodeRoleType nodeRoleType = thisNodeBo.getNodeRoleType();
            String randomIdSuffix = RandomUtil.randomNumbers(6);
            String nodeIdStr = payApplyOrderId + "0" + i + "0" + randomIdSuffix;
            Long nodeId = Long.valueOf(nodeIdStr);

            // 保存节点审核信息
            PayApplyAuditProcess payApplyAuditProcess = new PayApplyAuditProcess();
            payApplyAuditProcess.setApplyOrderId(payApplyOrderId);
            payApplyAuditProcess.setSeq(i);
            payApplyAuditProcess.setNodeId(nodeId);
            payApplyAuditProcess.setNodeStatus(thisNodeBo.getAuditStatus());
            payApplyAuditProcess.setAutoAudit(thisNodeBo.getAutoAudit());
            saveProcessList.add(payApplyAuditProcess);

            // 针对此节点的每一个审核人员，初始化一条审核信息
            // 一条节点的审核记录关联N条(由此审核节点配置的审核角色数量决定)此节点配置的各个角色成员的审核信息
            String roleName = nodeRoleType.name();
            List<AuditRoleConfigBo> roleInfoList = roleInfoMap.getOrDefault(roleName, new LinkedList<>());
            List<PayApplyAuditProcessNode> nodeList = roleInfoList.stream().map(roleInfo -> {
                PayApplyAuditProcessNode node = new PayApplyAuditProcessNode();
                node.setNodeId(nodeId);
                node.setAuditStatus(thisNodeBo.getAuditStatus());
                node.setRoleName(nodeRoleType.getDesc());
                node.setUserId(roleInfo.getUserId());
                node.setUserName(roleInfo.getUserName());
                node.setOpinion(thisNodeBo.getOpinion());
                node.setAutoAudit(thisNodeBo.getAutoAudit());
                return node;
            }).collect(Collectors.toList());
            saveNodeList.addAll(nodeList);
        }

        if (CollUtil.isNotEmpty(saveProcessList)) {
            iPayApplyAuditProcessService.saveBatch(saveProcessList);
        }
        if (CollUtil.isNotEmpty(saveNodeList)) {
            iPayApplyAuditProcessNodeService.saveBatch(saveNodeList);
        }

        // 将付款申请单，状态改为审核中
        payableApplyOrder.setStatus(PayableApplyOrderStatus.IN_AUDIT);
        iPurchasePayableApplyOrderService.updateById(payableApplyOrder);
    }

    /**
     * 将节点状态重置
     *
     * @param nodeIds
     */
    private void resetWaitStatus(List<Long> nodeIds) {
        iPayApplyAuditProcessService.lambdaUpdate()
                .set(PayApplyAuditProcess::getNodeStatus, PayApplyAuditStatus.WAITING)
                .in(PayApplyAuditProcess::getNodeId, nodeIds)
                .update();
        iPayApplyAuditProcessNodeService.lambdaUpdate()
                .set(PayApplyAuditProcessNode::getAuditStatus, PayApplyAuditStatus.WAITING)
                .set(PayApplyAuditProcessNode::getOpinion, "")
                .in(PayApplyAuditProcessNode::getNodeId, nodeIds)
                .update();
    }

    private void checkUser(Long userId) {
        boolean canDo = false;
        if (getSuperman().contains(userId)) {
            canDo = true;
        }
        if (getAllAuditRoleUserId().contains(userId)) {
            canDo = true;
        }
        if (!canDo) {
            throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "当前用户不是审核人员，无此功能操作权限");
        }
    }

    /**
     * 回退到指定节点
     *
     * @param payApplyOrderId 付款申请单id
     * @param dedicatedNodeId 指定要回退的节点id
     */
    @Transactional(rollbackFor = Exception.class)
    public void rollbackDedicatedNodes(Long payApplyOrderId, Long dedicatedNodeId, Long userId) throws BizException {
        checkUser(userId);

        PurchasePayableApplyOrder purchasePayableApplyOrder = iPurchasePayableApplyOrderService.getById(payApplyOrderId);
        if (Objects.isNull(purchasePayableApplyOrder)) {
            throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "付款申请单查询为空，id非法");
        }
        PayableApplyOrderStatus status = purchasePayableApplyOrder.getStatus();
        if (PayableApplyOrderStatus.HAD_AUDIT.equals(status) ||
                PayableApplyOrderStatus.FINISH.equals(status)) {
            throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "单据审核流执行完毕。不允许执行此操作");
        }

        Optional<PayApplyAuditProcess> optional = iPayApplyAuditProcessService.lambdaQuery()
                .eq(PayApplyAuditProcess::getApplyOrderId, payApplyOrderId)
                .eq(PayApplyAuditProcess::getNodeId, dedicatedNodeId).oneOpt();
        if (!optional.isPresent()) {
            throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "申请付款单id或者节点id非法。查不到节点信息");
        }
        // 查询出此节点的审核角色的审核信息
        List<PayApplyAuditProcessNode> nodeList = iPayApplyAuditProcessNodeService.lambdaQuery()
                .eq(PayApplyAuditProcessNode::getNodeId, dedicatedNodeId).list();
        if (CollUtil.isEmpty(nodeList)) {
            throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "节点id非法，查不到节点审核人员信息。" + dedicatedNodeId);
        }

        // 指定要回退的审核节点。
        PayApplyAuditProcess dedicatedNode = optional.get();
        // 如果指定要回退的节点是自动审核节点。
        if (dedicatedNode.getAutoAudit() == 1) {
            throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "此审核流中，指定要退回的节点为自动审核节点。回退操作无意义");
        }
        // 如果指定要回退的节点是人工审核节点
        else {
            Integer dedicatedNodeSeq = dedicatedNode.getSeq();
            // 将序号大于等于指定退回节点需要的全部节点，重置为等待状态
            List<Long> resetNodeIds = iPayApplyAuditProcessService.lambdaQuery()
                    .eq(PayApplyAuditProcess::getApplyOrderId, payApplyOrderId)
                    .ge(PayApplyAuditProcess::getSeq, dedicatedNodeSeq)
                    .list().stream().map(PayApplyAuditProcess::getNodeId)
                    .collect(Collectors.toList());
            resetWaitStatus(resetNodeIds);
        }

    }

    /**
     * 回退到发起人
     * 将整个付款申请单置为 待审核状态
     *
     * @param payApplyOrderId 付款申请单id
     * @param userId          操作人用户id
     */
    @Transactional(rollbackFor = Exception.class)
    public void rollbackInitiator(Long payApplyOrderId, Long userId) {
        checkUser(userId);

        // 付款申请单回到待提交状态
        PurchasePayableApplyOrder payableApplyOrder = iPurchasePayableApplyOrderService.getById(payApplyOrderId);
        if (Objects.isNull(payableApplyOrder)) {
            throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "付款申请单id非法，查询为空");
        }

//        // 如果付款申请单已经审核完成在处理中或者已经处理成功，则不再允许退回到发起人。
        if (payableApplyOrder.getStatus().equals(PayableApplyOrderStatus.HAD_AUDIT)
                || payableApplyOrder.getStatus().equals(PayableApplyOrderStatus.FINISH)) {
            throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "审核流执行完毕，不允许执行此操作");
        }

        payableApplyOrder.setStatus(PayableApplyOrderStatus.WAIT_AUDIT);
        iPurchasePayableApplyOrderService.updateById(payableApplyOrder);

        // 将此付款申请单关联到的审核节点全部删除
        QueryWrapper<PayApplyAuditProcess> wrapper = new QueryWrapper<>();
        wrapper.eq("apply_order_id", payableApplyOrder.getId());
        iPayApplyAuditProcessService.removeWithTime(wrapper);

        List<PayApplyAuditProcess> list = iPayApplyAuditProcessService.lambdaQuery()
                .eq(PayApplyAuditProcess::getApplyOrderId, payableApplyOrder).list();
        if (CollUtil.isNotEmpty(list)) {
            List<Long> nodeIdList = list.stream().map(PayApplyAuditProcess::getNodeId).collect(Collectors.toList());

            QueryWrapper<PayApplyAuditProcessNode> wrapper1 = new QueryWrapper<>();
            wrapper1.in("node_id", nodeIdList);
            iPayApplyAuditProcessNodeService.removeWithTime(wrapper1);
        }
    }

    /**
     * 查询申请付款的审核流程信息
     *
     * @param payApplyOrderId 付款申请单id
     * @return 审核流程信息
     */
    public List<AuditProcessNodVo> getAuditStream(Long payApplyOrderId) {

        List<PayApplyAuditProcess> list = iPayApplyAuditProcessService.lambdaQuery()
                .eq(PayApplyAuditProcess::getApplyOrderId, payApplyOrderId)
                .orderByAsc(PayApplyAuditProcess::getSeq)
                .list();
        if (CollUtil.isEmpty(list)) {
            throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "付款申请单id非法，查不到审核流信息");
        }

        boolean isAllFinished = list.stream().noneMatch(val -> val.getNodeStatus().equals(PayApplyAuditStatus.WAITING));
        boolean isLastNodePassed = list.get(list.size() - 1).getNodeStatus().equals(PayApplyAuditStatus.PASS);

        return list.stream().map(node -> {
            AuditProcessNodVo vo = new AuditProcessNodVo();
            vo.setSeq(node.getSeq());
            vo.setNodeId(node.getNodeId());
            vo.setNodeStatus(node.getNodeStatus());
            vo.setAutoAudit(node.getAutoAudit());

            List<PayApplyAuditProcessNode> nodeIndoList = iPayApplyAuditProcessNodeService.lambdaQuery()
                    .eq(PayApplyAuditProcessNode::getNodeId, node.getNodeId())
                    .orderByAsc(PayApplyAuditProcessNode::getId).list();
            if (CollUtil.isNotEmpty(nodeIndoList)) {
                vo.setNodeInfoVoList(nodeIndoList.stream().map(nodeInfo -> {
                    AuditProcessNodeInfoVo nodeInfoVo = new AuditProcessNodeInfoVo();
                    nodeInfoVo.setNodeId(nodeInfo.getNodeId());
                    nodeInfoVo.setRoleName(nodeInfo.getRoleName());
                    nodeInfoVo.setUserId(nodeInfo.getUserId());
                    nodeInfoVo.setUserName(nodeInfo.getUserName());
                    nodeInfoVo.setAutoAudit(nodeInfo.getAutoAudit());
                    nodeInfoVo.setAuditStatus(nodeInfo.getAuditStatus());
                    nodeInfoVo.setOpinion(nodeInfo.getOpinion());
                    return nodeInfoVo;
                }).collect(Collectors.toList()));

                String roleNames = nodeIndoList.stream().map(PayApplyAuditProcessNode::getRoleName).collect(Collectors.joining("|"));
                vo.setRoleName(roleNames);
            } else {
                vo.setNodeInfoVoList(new LinkedList<>());
            }

            // 是否允许存在【退回到此节点】按钮，判断逻辑
            // 自动审核的节点不存在此按钮。
            // 手动审核节点存在此按钮，但是当整个审核流程结束并且最后一个节点是PASS状态时，此按钮也不存在
            if (node.getAutoAudit() == 1) {
                vo.setRollbackDedicatedNodes(0);
            } else {
                if (isAllFinished && isLastNodePassed) {
                    vo.setRollbackDedicatedNodes(0);
                } else {
                    vo.setRollbackDedicatedNodes(1);
                }
            }

            return vo;
        }).collect(Collectors.toList());
    }


    /**
     * 节点审核。如果是最后一个节点，则触发后续处理流程。
     *
     * @param cmd
     * @param userId
     */
    @Transactional(rollbackFor = Exception.class)
    public void auditNode(AuditNodeCmd cmd, Long userId) {
        Long finishId = auditNode0(cmd, userId);
        if (finishId != 0) {
            // 触发后续流程
            log.info("付款申请单审核流程走完，开始进行冲销操作。id:{}", finishId);
            ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
                // 1.之前的出入库单冲销完成。2.根据修正后的金额重新生成了出入库单。3.根据修正数量和之前数量的差额，生成对应的销售退货单或者是销售出库单
                purchasePayableBizService.hedgeHandler(finishId);
            });
        }
    }

    /**
     * 提交审核
     *
     * @param cmd 审核请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    public Long auditNode0(AuditNodeCmd cmd, Long userId) {
        log.info("付款申请单审核参数。cmd:{},userId:{}", JsonUtil.toJson(cmd), userId);

        List<PayApplyAuditProcessNode> nodeList = iPayApplyAuditProcessNodeService.lambdaQuery()
                .eq(PayApplyAuditProcessNode::getNodeId, cmd.getNodeId()).list();
        if (CollUtil.isEmpty(nodeList)) {
            throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "节点id非法，查询节点审核人员数据为空");
        }
        List<PayApplyAuditProcess> processList = iPayApplyAuditProcessService.lambdaQuery()
                .eq(PayApplyAuditProcess::getNodeId, cmd.getNodeId()).list();
        if (CollUtil.isEmpty(processList)) {
            throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "节点id非法，查询节点本身数据为空");
        }
        PayApplyAuditProcess payApplyAuditProcess = processList.get(0);
        if (!payApplyAuditProcess.getNodeStatus().equals(PayApplyAuditStatus.WAITING)) {
            throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "当前节点状态非等待审核状态。此审核操作非法");
        }

        List<PayApplyAuditProcessNode> matchNodeInfo = nodeList.stream()
                .filter(val -> val.getUserId().equals(userId)).collect(Collectors.toList());
        boolean isSuperman = getSuperman().contains(userId);
        if (!isSuperman && CollUtil.isEmpty(matchNodeInfo)) {
            throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "当前用户即不是此节点的审核角色也不是管理员，不允许此操作");
        }

        if (isSuperman) {
            List<PayApplyAuditProcessNode> collect = nodeList.stream().peek(val -> {
                val.setAuditStatus(cmd.getStatus());
                val.setOpinion("超级管理员(" + userId + ")审核处理。" + cmd.getOpinion());
            }).collect(Collectors.toList());
            iPayApplyAuditProcessNodeService.updateBatchById(collect);
        } else {
            // 更新节点的审核人员操作信息
            PayApplyAuditProcessNode node = matchNodeInfo.get(0);
            node.setAuditStatus(cmd.getStatus());
            node.setOpinion(cmd.getOpinion());
            iPayApplyAuditProcessNodeService.updateById(node);
        }

        // 更新节点的审核状态
        payApplyAuditProcess.setNodeStatus(cmd.getStatus());
        iPayApplyAuditProcessService.updateById(payApplyAuditProcess);

        // 操作动作为审核通过
        boolean isPass = cmd.getStatus().equals(PayApplyAuditStatus.PASS);
        if (isPass) {
            List<PayApplyAuditProcess> allProcessList = iPayApplyAuditProcessService.lambdaQuery()
                    .eq(PayApplyAuditProcess::getApplyOrderId, payApplyAuditProcess.getApplyOrderId()).list();

            int seq = payApplyAuditProcess.getSeq();
            int finalSeq = seq;
            Optional<PayApplyAuditProcess> nextProcessOptional = allProcessList.stream()
                    .filter(val -> val.getSeq().equals(finalSeq + 1) && val.getAutoAudit() == 1).findFirst();
            // 循环检查下一个节点是否是自动审核。如果是自动审核，全部设置为审核通过
            while (nextProcessOptional.isPresent()) {
                PayApplyAuditProcess nextProcess = nextProcessOptional.get();
                nextProcess.setNodeStatus(PayApplyAuditStatus.PASS);
                iPayApplyAuditProcessService.updateById(nextProcess);

                iPayApplyAuditProcessNodeService.lambdaUpdate()
                        .set(PayApplyAuditProcessNode::getAuditStatus, PayApplyAuditStatus.PASS)
                        .set(PayApplyAuditProcessNode::getOpinion, "自动审核")
                        .in(PayApplyAuditProcessNode::getNodeId, nextProcess.getNodeId())
                        .update();

                seq = nextProcess.getSeq();
                int finalSeq1 = seq;
                nextProcessOptional = allProcessList.stream()
                        .filter(val -> val.getSeq().equals(finalSeq1 + 1) && val.getAutoAudit() == 1).findFirst();
            }

            // 如果是审核成功，并且是最后一个审核流节点，将此付款申请单的审核状态变为已审核
            boolean isEnd = iPayApplyAuditProcessService.lambdaQuery()
                    .eq(PayApplyAuditProcess::getApplyOrderId, payApplyAuditProcess.getApplyOrderId())
                    .gt(PayApplyAuditProcess::getSeq, seq).count() == 0;
            if (isEnd) {
                iPurchasePayableApplyOrderService.lambdaUpdate()
                        .eq(PurchasePayableApplyOrder::getId, payApplyAuditProcess.getApplyOrderId())
                        .set(PurchasePayableApplyOrder::getStatus, PayableApplyOrderStatus.HAD_AUDIT)
                        .update();
                return payApplyAuditProcess.getApplyOrderId();
            }

        }
        // 审核拒绝。只要是审核流程中出现了拒绝状态，整个付款申请单的状态改为审核拒绝。
        else {
            iPurchasePayableApplyOrderService.lambdaUpdate()
                    .eq(PurchasePayableApplyOrder::getId, payApplyAuditProcess.getApplyOrderId())
                    .set(PurchasePayableApplyOrder::getStatus, PayableApplyOrderStatus.HAD_REJECT)
                    .update();
        }

        return 0L;
    }


}
