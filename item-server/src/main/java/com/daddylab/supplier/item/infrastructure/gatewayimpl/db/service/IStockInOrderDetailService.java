package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrderDetail;
import com.daddylab.supplier.item.types.stockInOrder.StockInOrderDetailApplyQuantity;

import java.util.List;

/**
 * <p>
 * 采购入库明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
public interface IStockInOrderDetailService extends IDaddyService<StockInOrderDetail> {

    /**
     * 根据采购单查询入库单明细
     *
     * @param purchaseOrderId
     * @return
     */
    List<StockInOrderDetail> getListByPurchaseOrderId(Long purchaseOrderId);

    /**
     * 根据采购单id 查询此sku的申请入库数量。
     * 入库单状态为 不是待提交。不是已取消
     *
     * @param purchaseOrderId
     * @param skuCode
     * @return
     */
    Integer getApplyQuantity(Long purchaseOrderId, String skuCode, Boolean isGift);
    List<StockInOrderDetailApplyQuantity> getApplyQuantities(Long purchaseOrderId);

    /**
     * 根据入库单id查询明细表
     *
     * @param stockInOrderId
     * @return
     */
    List<StockInOrderDetail> getListByOrderId(Long stockInOrderId);

}
