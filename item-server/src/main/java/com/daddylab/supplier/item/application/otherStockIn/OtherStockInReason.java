package com.daddylab.supplier.item.application.otherStockIn;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/4/24
 */
@AllArgsConstructor
@Getter
public enum OtherStockInReason implements IEnum<Integer> {
    UNMAPPING(-1, "未映射"),
    ALL(0, "全部"),
    NONE(1, "无"),
    FACTORY_WMS(2, "工厂WMS仓入库"),
    TRANSFER(3, "调拨入库"),
    REFUND(4, "退货入库"),
    Move(5, "搬仓入库"),
    REFUND_ABNORMAL_STOCK_IN(6, "退货异常入库（无主件）"),
    ;
    private final Integer value;
    private final String desc;
}
