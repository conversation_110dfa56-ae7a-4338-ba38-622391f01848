package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> up
 */
@Getter
@AllArgsConstructor
public enum PaymentOrderStatus implements IEnum<Integer> {
    /**
     * 消除黄线
     */
    WAIT_SUBMIT(0, "待提交"),

    WAIT_AUDIT(1, "待审核"),
    WAIT_PAY(2, "待付款"),
    FINISH(4, "已完成"),

    AUDIT_REJECT(-1, "审核拒绝"),
    PAY_ERROR(-2, "付款异常"),
    SYNC_KING_DEE_ERROR(-3, "同步金蝶失败"),

    ROLLBACK_AUDIT(-4,"审核撤回"),

    ;

    @EnumValue
    private final Integer value;
    private final String desc;
}
