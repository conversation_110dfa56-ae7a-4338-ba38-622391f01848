package com.daddylab.supplier.item.controller.handingsheet.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Date: 2022/9/5 15:47
 * @Description: 请描述下这个类
 */
@Data
@ApiModel("HandingSheetStatusCountVo(盘货表不同状态的数量)")
public class HandingSheetStatusCountVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "全部数量")
    private Long totalCount;
    @ApiModelProperty(value = "待提交数量")
    private Long toBeSubmitCount;
    @ApiModelProperty(value = "待审核数量")
    private Long toBeAuditedCount;
    @ApiModelProperty(value = "已过审数量")
    private Long auditedCount;
    @ApiModelProperty(value = "进行中数量")
    private Long processingCount;
    @ApiModelProperty(value = "已过期数量")
    private Long expiredCount;
}
