package com.daddylab.supplier.item.infrastructure.kuaidaoyun.types;

import cn.hutool.crypto.digest.DigestUtil;
import com.daddylab.supplier.item.infrastructure.kuaidaoyun.configs.KdyConfig;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2024/5/22
 */

@Getter
@ToString
public class KdyBaseRequest<T> {
    private String account;
    private String data;
    private String sign;

    public KdyBaseRequest(KdyConfig config, T dataModel) {
        this.account = config.getAccount();
        this.data = dataModel.toString();
        final String secret = config.getSecret();
        this.sign = DigestUtil.md5Hex(secret + this.data + secret).toUpperCase();
    }
}
