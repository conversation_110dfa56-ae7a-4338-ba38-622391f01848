package com.daddylab.supplier.item.domain.dataFetch;

/**
 * <AUTHOR>
 * @since 2022/4/29
 */
public class StateSwitchException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private final String fullId;
    private final State currentState;
    private final State expectUpdateTo;

    public StateSwitchException(String fullId, State currentState, State expectUpdateTo,
            String message) {
        super(message);
        this.fullId = fullId;
        this.currentState = currentState;
        this.expectUpdateTo = expectUpdateTo;
    }

    @Override
    public String getMessage() {
        return "实例(" + fullId + ")状态从" + currentState + "切换为" + expectUpdateTo + "异常:" + super
                .getMessage();
    }

    public String getFullId() {
        return fullId;
    }

    public State getCurrentState() {
        return currentState;
    }

    public State getExpectUpdateTo() {
        return expectUpdateTo;
    }
}
