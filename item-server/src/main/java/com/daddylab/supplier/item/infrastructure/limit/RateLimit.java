package com.daddylab.supplier.item.infrastructure.limit;

import java.lang.annotation.*;

/**
 * <AUTHOR> up
 * @date 2022/5/12 10:31 上午
 */
@Inherited
@Documented
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {
    /**
     * 限流唯一标示
     *
     * @return
     */
    String key();

    /**
     * 方法描述
     *
     * @return
     */
    String methodDesc();

    /**
     * 限流时间
     *
     * @return
     */
    long time() default 1;

    /**
     * 限流次数
     *
     * @return
     */
    long count() default 1;
}
