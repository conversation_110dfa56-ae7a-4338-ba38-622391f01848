package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesReceive;

/**
 * <p>
 * 售后单登记收货 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
public interface AfterSalesReceiveMapper extends DaddyBaseMapper<AfterSalesReceive> {

}
