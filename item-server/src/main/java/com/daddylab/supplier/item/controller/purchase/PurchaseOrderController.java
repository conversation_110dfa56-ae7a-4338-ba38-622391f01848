package com.daddylab.supplier.item.controller.purchase;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuVO;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizService;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.PurchasePaymentDetailVo;
import com.daddylab.supplier.item.common.domain.dto.GenericIdBody;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseDetailExportCmd;
import com.daddylab.supplier.item.controller.purchase.dto.order.*;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> up
 * @date 2022/3/28 1:59 下午
 */
@Slf4j
@Api(value = "采购订单相关API", tags = "采购订单相关API")
@RestController
@RequestMapping("/purchase/order")
@RequiredArgsConstructor
public class PurchaseOrderController {

  final PurchaseOrderBizService purchaseOrderBizService;
  final OperateLogGateway operateLogGateway;

  @ResponseBody
  @ApiOperation(value = "搜索查看采购订单列表")
  @PostMapping("/page")
  public PageResponse<PurchaseOrderPageVO> page(
      @RequestBody @Validated PurchaseOrderPageQuery query) {
    return purchaseOrderBizService.page(query);
  }

  @ResponseBody
  @ApiOperation(value = "采购单付款条件")
  @GetMapping("/payCondition")
  public SingleResponse<Map<Integer, String>> payCondition() {
    return purchaseOrderBizService.payCondition();
  }

  @ResponseBody
  @ApiOperation(value = "搜索查看sku明细列表")
  @PostMapping("/skuPage")
  public PageResponse<ComposeSkuVO> skuPage(@RequestBody ComposeSkuPageQuery query) {
    query.setWithPrice(true);
    return purchaseOrderBizService.pageSku(query);
  }

  @ResponseBody
  @ApiOperation(value = "保存采购订单")
  @PostMapping("/save")
  //    @RateLimit(key = "purchase_order_save", methodDesc = "保存采购订单")
  public SingleResponse<Long> save(@RequestBody @Validated PurchaseOrderCmd cmd) {
    return purchaseOrderBizService.save(cmd);
  }

  @ResponseBody
  @ApiOperation(value = "查看采购订单详情")
  @GetMapping("/view")
  @ApiImplicitParam(name = "id", dataType = "long", value = "采购订单id")
  public SingleResponse<PurchaseOrderViewVO> view(Long id) {
    return purchaseOrderBizService.view(id);
  }

  @ResponseBody
  @ApiOperation(value = "查看采购订单详情列表")
  @PostMapping("/viewPage")
  public PageResponse<PurchaseOrderDetailVO> viewPage(
      @RequestBody PurchaseOrderDetailPageQuery query) {
    return purchaseOrderBizService.pageView(query);
  }

  @ResponseBody
  @ApiOperation(value = "查看付款单明细")
  @PostMapping("/paymentDetailPage")
  public PageResponse<PurchasePaymentDetailVo> paymentDetailPage(
      @RequestBody PurchasePaymentDetailPageQuery query) {
    return purchaseOrderBizService.paymentDetailPage(query);
  }

  @ResponseBody
  @ApiOperation(value = "删除采购订单详情")
  @GetMapping("/delete")
  @ApiImplicitParam(name = "id", dataType = "long", value = "采购订单id")
  public SingleResponse<Boolean> delete(Long id) {
    return purchaseOrderBizService.delete(id);
  }

  @ResponseBody
  @ApiOperation(value = "查看采购订单操作日志")
  @GetMapping("/log")
  @ApiImplicitParam(name = "id", dataType = "long", value = "采购订单id")
  public MultiResponse<OperateLog> log(Long id) {
    return purchaseOrderBizService.logList(id);
  }

  @ResponseBody
  @ApiOperation(value = "导出采购订单列表")
  @PostMapping("/export")
  public SingleResponse<Boolean> export(@RequestBody @Validated PurchaseOrderPageQuery query) {
    return purchaseOrderBizService.export(query);
  }

  @ResponseBody
  @ApiOperation(value = "提交采购订单")
  @GetMapping("/submit")
  @ApiImplicitParam(name = "id", dataType = "long", value = "采购订单id")
  public SingleResponse<Boolean> submit(Long id) {
    return purchaseOrderBizService.submit(id);
  }

  @ResponseBody
  @ApiOperation(value = "撤回审核")
  @GetMapping("/withdrawAudit")
  @ApiImplicitParam(name = "id", dataType = "long", value = "采购订单id")
  public SingleResponse<Boolean> withdrawAudit(Long id) {
    return purchaseOrderBizService.withdrawAudit(id);
  }

  @ResponseBody
  @ApiOperation(value = "更新采购单状态")
  @GetMapping("/updateState")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "id", dataType = "long", value = "采购订单id"),
    @ApiImplicitParam(
        name = "state",
        dataType = "int",
        value = "1:待提交、2:待审核、3:审核通过、4:审核拒绝、5:撤回审核、6:待完结、7:已完结,8:已关闭"),
  })
  public SingleResponse<Boolean> updateState(Long id, Integer state) {
    purchaseOrderBizService.updateState(id, state);
    return SingleResponse.of(true);
  }

  @ResponseBody
  @ApiOperation(value = "获取此采购订单可申请付款的金额")
  @GetMapping("/getPayAmount")
  @ApiImplicitParam(name = "id", dataType = "long", value = "采购订单id")
  public Response getPayAmount(
      @RequestParam Long id, @RequestParam(required = false) Boolean advancePayment) {
    if (Objects.isNull(advancePayment)) {
      advancePayment = false;
    }
    return purchaseOrderBizService.getRightPayAmount(id, null, advancePayment);
  }

  @ResponseBody
  @ApiOperation(value = "采购订单明细导出")
  @PostMapping("/exportDetail")
  public Response exportDetail(@RequestBody PurchaseDetailExportCmd cmd) {
    return purchaseOrderBizService.exportDetail(cmd.getIds());
  }

  @ResponseBody
  @ApiOperation(value = "采购订单流程催办")
  @PostMapping("/urge")
  public Response urge(@RequestBody GenericIdBody<Long> cmd) {
    return purchaseOrderBizService.urgent(cmd.getId());
  }

  @ResponseBody
  @ApiOperation(value = "更新明细的税率信息")
  @PostMapping("/updateTaxData")
  public SingleResponse<Boolean> updateTaxDate(@RequestBody UpdateTaxDataCmd cmd) {
    return purchaseOrderBizService.updateTaxData(cmd);
  }

  @ResponseBody
  @ApiOperation(value = "待办列表")
  @PostMapping("/getTodoList")
  public MultiResponse<Long> getTodoList() {
    return purchaseOrderBizService.getTodoList();
  }

  @ResponseBody
  @ApiOperation(value = "此采购单是否存在出/入库单据")
  @PostMapping("/isExistStockOrder")
  public SingleResponse<Boolean> isExistStockOrder(@RequestBody PurchaseOrderIdCmd cmd) {
    //    return purchaseOrderBizService.isExistStockOrder(cmd);
    return SingleResponse.of(true);
  }

  @ResponseBody
  @ApiOperation(value = "发起OA盖章流程")
  @GetMapping("/startSealProcess")
  public SingleResponse<Boolean> startSealProcess(Long id) {
    final Long userId = UserContext.getUserId();
    operateLogGateway.addOperatorLog(
        userId, OperateLogTarget.PURCHASE_ORDER, id, StrUtil.format("手动发起OA盖章流程"), null);
    ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL)
        .execute(() -> purchaseOrderBizService.startOASealProcess(id));
    return SingleResponse.of(true);
  }

  @ResponseBody
  @ApiOperation(value = "获取关联的OA盖章流程URL")
  @GetMapping("/oaSealUrl")
  //  @Auth(noAuth = true)
  public SingleResponse<String> oaSealUrl(Long id) {
    return purchaseOrderBizService.oaSealUrl(id);
  }

  @ResponseBody
  @ApiOperation(value = "采购合同导出")
  @GetMapping("/exportContact")
  //  @Auth(noAuth = true)
  public SingleResponse<Boolean> exportContact(Long id) {
    return purchaseOrderBizService.exportContact(id);
  }

  @ResponseBody
  @ApiOperation(value = "是否包含定制商品")
  @GetMapping("/containsCustom")
  public SingleResponse<Boolean> containsCustomizedProducts(Long id) {
    return purchaseOrderBizService.containsCustomizedProducts(id);
  }
}
