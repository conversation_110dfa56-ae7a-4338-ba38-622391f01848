package com.daddylab.supplier.item.controller.handingsheet.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/24 13:52
 * @Description: 盘货表分页查询返回 VO
 */
@Data
@ApiModel("盘货表分页查询返回对象")
public class HandingSheetPageVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "盘货表主键ID")
    private Long id;

    @ApiModelProperty(value = "盘货表名称")
    private String name;

    /**
     * {@link com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.HandingSheetStateEnum}
     */
    @ApiModelProperty(value = "状态（1-待提交，2-待审核，3-已过审，4-进行中，5-已过期）")
    private Integer state;

    /**
     * {@link com.daddylab.supplier.item.domain.common.enums.Platform}
     */
    @ApiModelProperty(value = "所属平台（0-其他，1-淘宝，2-有赞，3-抖店，4-快手小店，5-小红书，7-小程序）")
    private List<Integer> platformVals;

    @ApiModelProperty(value = "活动开始时间（时间戳，单位秒）")
    private Long startTime;

    @ApiModelProperty(value = "活动结束时间（时间戳，单位秒）")
    private Long endTime;

    @ApiModelProperty(value = "活动力度")
    private List<String> activityEvents;

    @ApiModelProperty(value = "活动标签")
    private String label;

    @ApiModelProperty(value = "商品数量")
    private Long itemNum;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建时间（时间戳，单位秒）")
    private Long createAt;
}
