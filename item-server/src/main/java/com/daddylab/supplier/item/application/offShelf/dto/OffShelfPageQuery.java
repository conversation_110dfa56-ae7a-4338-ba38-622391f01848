package com.daddylab.supplier.item.application.offShelf.dto;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OffShelfStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OffShelfReasonType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OffShelfUrgentLevel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/20
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OffShelfPageQuery extends PageQuery {
    private static final long serialVersionUID = 2007700966235421455L;

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("商品ID")
    private Long itemId;

    @ApiModelProperty("商品名称")
    private String itemName;

    @ApiModelProperty("商品编码")
    private String itemCode;

    @ApiModelProperty("关联款号")
    private String partnerProviderItemSn;

    @ApiModelProperty("流程编号")
    private String no;

    @ApiModelProperty("品牌ID")
    private Long brandId;

    @ApiModelProperty("品类ID")
    private Long categoryId;

    @ApiModelProperty("紧急程度")
    private OffShelfUrgentLevel urgentLevel;

    @ApiModelProperty("下架原因")
    private OffShelfReasonType reasonType;

    @ApiModelProperty("下架状态")
    private OffShelfStatus status;

    @ApiModelProperty("申请人ID")
    private Long applicantUid;

    @ApiModelProperty("申请时间-开始范围")
    private Long applyTimeStart;

    @ApiModelProperty("申请时间-结束范围")
    private Long applyTimeEnd;

    @ApiModelProperty("下架运营ID")
    private Long operatorUid;

    @ApiModelProperty("合作方")
    private List<Integer> corpType;

    @ApiModelProperty("业务类型")
    private List<Integer> bizType;


}
