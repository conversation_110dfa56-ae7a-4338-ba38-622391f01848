package com.daddylab.supplier.item.application.salesInStock;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.SalesOrderDto;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.SkuUnitDto;
import com.daddylab.supplier.item.application.salesInStock.dto.*;
import com.daddylab.supplier.item.application.salesOutStock.dto.BuyerNameDO;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemMainImgDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.S01Order;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.S03Order;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.S13OrderNew;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemImageMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundStockInOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.kingdee.dto.KingDeeResp;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqJsonUtil;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2023年10月23日 10:24 AM
 */
@Slf4j
@Service
public class SalesInStockBizServiceImpl implements SalesInStockBizService {

    @Resource
    IS01OrderService taoBaoDbService;

    @Resource
    IS03OrderService douYinDbService;

    @Resource
    IS13OrderNewService redBookDbService;

    @Resource
    WdtRefundStockInOrderMapper wdtRefundStockInOrderMapper;

    @Resource
    ItemImageMapper itemImageMapper;

    @Resource
    ReqJsonUtil kingDeeJsonUtil;

    @Resource
    ReqTemplate kingDeeReqTemplate;
    @Resource
    SalesInStockTransDao salesInStockTransDao;


    private static final Integer TAO_BAO_PLATFORM_ID = 1;
    private static final Integer DOU_YIN_PLATFORM_ID = 69;
    private static final Integer RED_BOOK_PLATFORM_ID = 56;

    @Override
    public PageResponse<SalesInStockPageVO> queryPage(SalesInStockPageQuery pageQuery) {
        if (Objects.nonNull(pageQuery.getBuyerNameDO())) {
            Collection<String> tidList = getTidList(pageQuery.getBuyerNameDO());
            if (CollUtil.isEmpty(tidList)) {
                return PageResponse.of(new ArrayList<>(), 0, pageQuery.getPageSize(), pageQuery.getPageIndex());
            } else {
                pageQuery.setTids(tidList);
            }
        }

        PageInfo<SalesInStockPageVO> objectPageInfo = PageHelper.startPage(pageQuery.getPageIndex(), pageQuery.getPageSize())
                .doSelectPageInfo(() -> wdtRefundStockInOrderMapper.selectSalesInStockPage(pageQuery));
        return PageResponse.of(objectPageInfo.getList(), (int) objectPageInfo.getTotal(), pageQuery.getPageSize(),
                pageQuery.getPageIndex());
    }

    private Collection<String> getTidList(BuyerNameDO buyerNameDO) {
        String name = buyerNameDO.getName();
        Integer source = buyerNameDO.getSource();
        if (TAO_BAO_PLATFORM_ID.equals(source)) {
            return taoBaoDbService.lambdaQuery()
                    .eq(S01Order::getReceiverName, name).select(S01Order::getOrderNo).list()
                    .stream().map(S01Order::getOrderNo).collect(Collectors.toSet());
        }
        if (DOU_YIN_PLATFORM_ID.equals(source)) {
            return douYinDbService.lambdaQuery()
                    .eq(S03Order::getReceiverName, name).select(S03Order::getOrderNo).list()
                    .stream().map(S03Order::getOrderNo).collect(Collectors.toSet());

        }
        if (RED_BOOK_PLATFORM_ID.equals(source)) {
            return redBookDbService.lambdaQuery()
                    .eq(S13OrderNew::getReceiverName, name).select(S13OrderNew::getOrderId).list()
                    .stream().map(S13OrderNew::getOrderId).collect(Collectors.toSet());
        }
        return Collections.emptyList();
    }

    @Override
    public SingleResponse<SalesInStockDetailVO> viewDetail(SalesInStockDetailCmd cmd) {
        SalesInStockDetailVO res = new SalesInStockDetailVO();

        CompletableFuture<Void> baseInfoTask = CompletableFuture.runAsync(() -> {
            SalesInStockBaseVO baseVo = wdtRefundStockInOrderMapper.selectSalesInStockBase(cmd.getOrderNo());
            res.setBaseVO(baseVo);
        });

        CompletableFuture<Void> refundBaseInfoTask = CompletableFuture.runAsync(() -> {
            SalesInStockRefundBaseVO refundBaseVo = wdtRefundStockInOrderMapper.selectSalesInStockRefundBase(cmd.getRefundNo());
            res.setRefundBaseVO(refundBaseVo);
        });

        CompletableFuture<Void> listTask = CompletableFuture.runAsync(() -> {
            List<SalesInStockDetailListVO> listVo = wdtRefundStockInOrderMapper.selectSalesInStockDetailList(cmd.getStockinId());
            Set<String> itemCodes = listVo.stream().map(SalesInStockDetailListVO::getGoodsNo).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(itemCodes)) {
                Map<String, ItemMainImgDO> imgMap = itemImageMapper.selectMainImg(itemCodes).stream()
                        .collect(Collectors.toMap(ItemMainImgDO::getItemCode, v -> v));
                listVo.forEach(val -> {
                    ItemMainImgDO itemMainImgDO = imgMap.get(val.getGoodsNo());
                    if (Objects.nonNull(itemMainImgDO)) {
                        val.setImgUrl(itemMainImgDO.getMainImgUrl());
                    }
                });
            }
            res.setBaseListVO(listVo);
        });

        CompletableFuture.allOf(baseInfoTask, refundBaseInfoTask, listTask).join();

        return SingleResponse.of(res);
    }


    @Override
    public SingleResponse<SalesOrderDto> generateOrderBySettlement(String warehouseNo, List<SkuUnitDto> salesReturnList,
                                                                   Long auditDate, Boolean mockSync) {
        log.info("generateOrderBySettlement param.warehouseNo:{},details:{},auditDate:{},mock:{}",
                warehouseNo, JsonUtil.toJson(salesReturnList), auditDate, mockSync);
        SalesOrderDto salesOrderDto = new SalesOrderDto();
        WdtStockInDto wdtStockInDto;

        Map<String, Integer> salesReturnGoodMap = new HashMap<>(16);
        try {
            wdtStockInDto = salesInStockTransDao.buildData(warehouseNo, salesReturnList, salesReturnGoodMap);
            salesOrderDto.setNo(wdtStockInDto.getOrderNo());

            if (mockSync) {
                return SingleResponse.of(salesOrderDto);
            }

            String jsonParam = kingDeeJsonUtil.saveSaleReturnStock(salesReturnGoodMap, auditDate, wdtStockInDto.getOrderNo());
            KingDeeResp kingDeeResp = kingDeeReqTemplate.saveWithRes(jsonParam);
            salesOrderDto.setKingDeeId(kingDeeResp.getId());
            salesOrderDto.setKingDeeNumber(kingDeeResp.getNumber());
            return SingleResponse.of(salesOrderDto);
        } catch (Exception e) {
            log.error("销售退货单同步金蝶异常", e);
            return ResponseFactory.buildFail(ErrorCode.API_RESP_ERROR, "销售退货单同步到金蝶异常." + e.getMessage());
        }
    }
}
