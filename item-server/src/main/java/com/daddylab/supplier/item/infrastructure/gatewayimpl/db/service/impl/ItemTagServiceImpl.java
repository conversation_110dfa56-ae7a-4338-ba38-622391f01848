package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTag;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemTagMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemTagService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 商品标签 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Service
public class ItemTagServiceImpl extends DaddyServiceImpl<ItemTagMapper, ItemTag> implements IItemTagService {

    @Override
    public List<ItemTag> listByItemId(Long itemId) {
        return lambdaQuery().eq(ItemTag::getItemId, itemId).list();
    }

    @Override
    public List<ItemTag> listByItemIds(Collection<Long> itemIds) {
        if (CollUtil.isEmpty(itemIds)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(ItemTag::getItemId, itemIds).list();
    }
}
