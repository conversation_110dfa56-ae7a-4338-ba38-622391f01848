package com.daddylab.supplier.item.application.platformItem;

import com.daddylab.supplier.item.application.platformItem.model.PlatformItemSkuStat;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemSkuMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/1/7
 */
@Service
public class PlatformItemDaoImpl implements PlatformItemDao {

    final PlatformItemSkuMapper platformItemSkuMapper;
    final PlatformItemMapper platformItemMapper;
    @Autowired
    public PlatformItemDaoImpl(
            PlatformItemSkuMapper platformItemSkuMapper, PlatformItemMapper platformItemMapper) {
        this.platformItemSkuMapper = platformItemSkuMapper;
        this.platformItemMapper = platformItemMapper;
    }

    @Override
    public PlatformItemSkuStat statSku(long platformItemId) {
        return platformItemSkuMapper.statPlatformItemSku(platformItemId);
    }

    @Override
    public long countOnSalePlatformItem(long itemId) {
        return platformItemMapper.countOnSalePlatformItem(itemId, null);
    }
}
