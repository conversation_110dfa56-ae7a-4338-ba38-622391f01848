package com.daddylab.supplier.item.infrastructure.separator;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apdplat.word.WordSegmenter;
import org.apdplat.word.segmentation.SegmentationAlgorithm;
import org.apdplat.word.segmentation.Word;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> up
 * @date 2022年11月28日 10:08 AM
 */
@Component
@Slf4j
public class CommonSeparatorTemplate {

    /**
     * word分词器
     *
     * @param txt
     * @return
     */
    public Set<String> seg(String txt) {
        List<Word> words = WordSegmenter.seg(txt);
        return words.stream().map(Word::getText).collect(Collectors.toSet());
    }

    /**
     * word分词器
     *
     * @param txt
     * @param segmentationAlgorithm @link{org.apdplat.word.segmentation.SegmentationAlgorithm}
     * @return
     */
    public Set<String> seg(String txt, SegmentationAlgorithm segmentationAlgorithm) {
        List<Word> words = WordSegmenter.seg(txt, segmentationAlgorithm);
        return words.stream().map(Word::getText).collect(Collectors.toSet());
    }

    /**
     * 对文本进行分词，保留停用词 使用双向最大匹配算法
     *
     * @param txt 文本
     * @return 分词集合
     */
    public Set<String> segWithStopWords(String txt) {
        List<Word> words = WordSegmenter.segWithStopWords(txt);
        return words.stream().map(Word::getText).collect(Collectors.toSet());
    }

    public static void main(String[] args){
        final Set<String> seg = new CommonSeparatorTemplate().segWithStopWords(
            "服用指南L20.. 2122. 2627-2829GNCWOMEN'Sak * Prog每天一包30搭配无烦恼-小颗粒,好吞服GNCWOMEN'S50餐后一包GINCWOMEN'S每日一次40GNE30WOMEN'S");
    System.out.println(seg);
    }


}
