package com.daddylab.supplier.item.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Author: <PERSON>
 * @Date: 2022/6/1 15:49
 * @Description: 请描述下这个类
 */
@Component
@Data
@ConfigurationProperties(prefix = "qyweixin.daddyerp")
public class QyWeixinDaddyErpProperties {
    private Integer agentid;
    private String corpid;
    private String corpsecret;
}
