package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemOptimizeParticipant;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemOptimizeParticipantMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemOptimizeParticipantService;
import com.daddylab.supplier.item.types.itemOptimize.ParticipantType;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品优化参与者 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Service
public class ItemOptimizeParticipantServiceImpl
        extends DaddyServiceImpl<ItemOptimizeParticipantMapper, ItemOptimizeParticipant>
        implements IItemOptimizeParticipantService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addParticipants(
            Long itemOptimizeId, List<Long> userIds, ParticipantType participantType) {
        final ArrayList<ItemOptimizeParticipant> itemOptimizeParticipants = new ArrayList<>();
        for (Long userId : userIds) {
            final ItemOptimizeParticipant itemOptimizeParticipant = new ItemOptimizeParticipant();
            itemOptimizeParticipant.setItemOptimizeId(itemOptimizeId);
            itemOptimizeParticipant.setType(participantType.getValue());
            itemOptimizeParticipant.setUid(userId);
            itemOptimizeParticipants.add(itemOptimizeParticipant);
        }
        return saveBatch(itemOptimizeParticipants);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeParticipants(
            Long itemOptimizeId, List<Long> userIds, ParticipantType participantType) {
        final List<Long> ids =
                lambdaQuery()
                        .in(ItemOptimizeParticipant::getItemOptimizeId, itemOptimizeId)
                        .eq(ItemOptimizeParticipant::getType, participantType.getValue())
                        .in(ItemOptimizeParticipant::getUid, userIds)
                        .select(ItemOptimizeParticipant::getId)
                        .list()
                        .stream()
                        .map(ItemOptimizeParticipant::getId)
                        .collect(Collectors.toList());
        return removeByIdsWithTime(ids);
    }
}
