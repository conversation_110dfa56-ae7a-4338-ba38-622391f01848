package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import cn.hutool.core.lang.func.Func1;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemOptimize;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizePersistData;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizeStatus;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 商品优化 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface IItemOptimizeService extends IDaddyService<ItemOptimize> {


    boolean setStatusById(Long id, ItemOptimizeStatus currentStatus, ItemOptimizeStatus updateTo);

    <T> boolean setDataField(Long id, Func1<ItemOptimizePersistData, T> field, T value);
    
    <K, V> boolean setDataFieldMapValue(Long id, Func1<ItemOptimizePersistData, Map<K, V>> field, K key, V value);

    <T> T getDataField(Long id, Func1<ItemOptimizePersistData, T> field);

    boolean updateSubmitMeta(Long id, Long userId, Long time, Boolean review);

    List<ItemOptimize> selectListByPlanId(Long planId);
}
