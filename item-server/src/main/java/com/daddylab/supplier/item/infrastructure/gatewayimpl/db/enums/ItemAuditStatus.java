package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ItemAuditStatus implements IEnum<Integer> {

    NONE(0, "未进入审核流程/未知"),

    WAIT_LEGAL_AUDIT(5, "待法务审核"),

    WAIT_QC_AUDIT(10, "待QC审核"),

    WAIT_MODIFY(15, "待修改"),

    FINISHED(100, "审核完成"),
    ;

    @EnumValue
    private Integer value;
    private String desc;

}
