package com.daddylab.supplier.item.application.afterSaleLogistics;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.exception.BizException;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.job.extend.autoRegister.XxlJobAutoRegister;
import com.daddylab.supplier.item.application.afterSaleLogistics.dto.AbnormalScanCmd;
import com.daddylab.supplier.item.application.kuaidaoyun.KdyBizService;
import com.daddylab.supplier.item.application.warehouse.WarehouseGateway;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.JobUtils;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.order.OrderSensitiveInfoRepository;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.domain.wdt.consts.PlatformMap;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.CloseReasonType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OrderLogisticsTraceStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderLogisticsTraceMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.kuaidaoyun.types.KdySubscribeData;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.SysVariableKey;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.multiset.HashMultiSet;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RSemaphore;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/5/22
 */
@Service
@Slf4j
public class AfterSaleLogisticsJob {

  public static final DateTimeFormatter TIME_FORMATTER = DatePattern.NORM_DATETIME_FORMATTER;
  @Autowired private IWdtOrderService wdtOrderService;
  @Autowired private IWdtOrderDetailService wdtOrderDetailService;

  @Autowired private IOrderLogisticsTraceService orderLogisticsTraceService;

  @Autowired private IOrderLogisticsAbnormalityService orderLogisticsAbnormalityService;

  @Autowired private ISysVariableService sysVariableService;

  @Autowired private AfterSaleLogisticsConfig config;

  @Autowired private IKdyLogisticMappingService kdyLogisticMappingService;

  @Autowired private OrderSensitiveInfoRepository orderSensitiveInfoRepository;

  @Autowired AfterSaleLogisticsBizService afterSaleLogisticsBizService;

  @Autowired AbnormalScanService abnormalScanService;

  @Autowired RedissonClient redissonClient;

  @Autowired IOrderLogisticsAbnormalityAlertService abnormalityAlertService;

  @Autowired AfterSaleLogisticsConfig afterSaleLogisticsConfig;

  @Autowired WarehouseGateway warehouseGateway;

  @Autowired IKdyCallbackService iKdyCallbackService;

  @Autowired private ShopGateway shopGateway;

  @XxlJob("AfterSaleLogistics:scanOrder")
  @XxlJobAutoRegister(
      cron = "0 * * * * ? *",
      author = "徵乌",
      jobDesc = "物流异常-扫描订单",
      autoUpdate = false)
  public void scanOrder() {
    if (config.getTraceLimit() > -1) {
      final long maxTraceId = orderLogisticsTraceService.maxId();
      if (maxTraceId >= config.getTraceLimit()) {
        return;
      }
    }
    final OrderLogisticsTraceMapper orderLogisticsTraceMapper =
        orderLogisticsTraceService.getDaddyBaseMapper();
    final LocalDateTime timeCursor = getScanOrderTimeCursor();
    int limit = config.getScanLimit();
    final List<WdtOrder> wdtOrders =
        orderLogisticsTraceMapper.scanToTraceOrderList(timeCursor, limit);
    LocalDateTime nextTimeCursor =
        wdtOrders.stream()
            .max(Comparator.comparing(WdtOrder::getModified))
            .map(WdtOrder::getModified)
            .orElse(LocalDateTime.now());
    setScanOrderTimeCursor(nextTimeCursor);
    if (CollectionUtil.isEmpty(wdtOrders)) {
      return;
    }
    final List<Long> tradeIds =
        wdtOrders.stream().map(WdtOrder::getTradeId).collect(Collectors.toList());
    final List<WdtOrderDetail> wdtOrderDetails =
        wdtOrderDetailService
            .lambdaQuery()
            .in(WdtOrderDetail::getTradeId, tradeIds)
            .and(
                q ->
                    q.eq(WdtOrderDetail::getDeletedAt, 0).or().isNull(WdtOrderDetail::getDeletedAt))
            .list();
    Map<Long, List<WdtOrderDetail>> orderDetailsGroup =
        wdtOrderDetails.stream().collect(Collectors.groupingBy(WdtOrderDetail::getTradeId));
    final Long currentTime = DateUtil.currentTime();
    final ArrayList<OrderLogisticsTrace> orderLogisticsTraces = new ArrayList<>();
    for (WdtOrder wdtOrder : wdtOrders) {
      if (!afterSaleLogisticsBizService.filter(
          wdtOrder,
          orderDetailsGroup.getOrDefault(wdtOrder.getTradeId(), Collections.emptyList()))) {
        continue;
      }
      final Platform platform = PlatformMap.mapPlatform(wdtOrder.getPlatformId());
      final OrderLogisticsTrace orderLogisticsTrace = new OrderLogisticsTrace();
      orderLogisticsTrace.setWdtTradeId(wdtOrder.getTradeId());
      orderLogisticsTrace.setWdtTradeNo(wdtOrder.getTradeNo());
      orderLogisticsTrace.setSrcOrderNo(wdtOrder.getSrcTids());
      orderLogisticsTrace.setPayTime(DateUtil.toTime(wdtOrder.getPayTime()));
      orderLogisticsTrace.setLogisticsNo(wdtOrder.getLogisticsNo());
      orderLogisticsTrace.setLogisticsName(wdtOrder.getLogisticsTypeName());
      orderLogisticsTrace.setReceiverMobile(wdtOrder.getReceiverMobile());
      orderLogisticsTrace.setConsignTime(DateUtil.toTime(wdtOrder.getConsignTime()));
      orderLogisticsTrace.setTrackTime(currentTime);
      orderLogisticsTrace.setPickingUpTime(0L);
      orderLogisticsTrace.setSendingTime(0L);
      orderLogisticsTrace.setDistributeTime(0L);
      orderLogisticsTrace.setSigningTime(0L);
      orderLogisticsTrace.setPlatform(platform.getValue());
      orderLogisticsTrace.setStockoutNo(wdtOrder.getStockoutNo());
      orderLogisticsTrace.setStockoutWarehouseNo(wdtOrder.getWarehouseNo());
      final Optional<Shop> shop = shopGateway.queryShopBySn(wdtOrder.getShopNo());
      orderLogisticsTrace.setShopId(shop.map(Shop::getId).orElse(0L));
      orderLogisticsTrace.setTraceStatusForLogisticsTrace(config);
      orderLogisticsTraces.add(orderLogisticsTrace);
    }
    try {
      orderLogisticsTraceService.saveBatch(orderLogisticsTraces);
    } catch (DuplicateKeyException ignored) {
    }
  }

  private void setScanOrderTimeCursor(LocalDateTime nextTimeCursor) {
    sysVariableService.setValue(
        SysVariableKey.AFTER_SALE_LOGISTICS_SCAN_ORDER_CURSOR,
        nextTimeCursor.format(TIME_FORMATTER));
  }

  private LocalDateTime getScanOrderTimeCursor() {
    return sysVariableService
        .getValue(SysVariableKey.AFTER_SALE_LOGISTICS_SCAN_ORDER_CURSOR)
        .map(v -> LocalDateTimeUtil.parse(v, TIME_FORMATTER))
        .orElseGet(() -> LocalDateTimeUtil.parse(config.getScanStartTime(), TIME_FORMATTER));
  }

  @Autowired private KdyBizService kdyBizService;

  @XxlJob("AfterSaleLogistics:scanSubscribe")
  @XxlJobAutoRegister(
      cron = "0 * * * * ? *",
      author = "徵乌",
      jobDesc = "物流异常-扫描订阅",
      autoUpdate = false)
  public void scanSubscribe() {
    final List<OrderLogisticsTrace> traceList =
        orderLogisticsTraceService.listByTraceStatus(
            OrderLogisticsTraceStatus.KDY_WAIT_SUBSCRIBE, config.getSubscribeLimit());
    log.info("[客服物流异常][扫描订阅]查询到{}条记录", traceList.size());
    final HashMultiSet<String> results = new HashMultiSet<>();
    if (CollectionUtil.isNotEmpty(traceList)) {
      for (OrderLogisticsTrace orderLogisticsTrace : traceList) {
        // 没有物流单号，将状态置为未发货
        if (StringUtil.isBlank(orderLogisticsTrace.getLogisticsNo())) {
          orderLogisticsTrace.setTraceStatus(OrderLogisticsTraceStatus.NOT_DELIVERY);
          orderLogisticsTraceService.updateStatusAndUnlock(orderLogisticsTrace);
          log.info("[客服物流异常][扫描订阅]没有物流单号，将状态重置为'未发货' id={}", orderLogisticsTrace.getId());
          results.add("未发货");
          continue;
        }
        final boolean lock = orderLogisticsTraceService.lock(orderLogisticsTrace);
        if (!lock) {
          log.warn("[客服物流异常][扫描订阅]锁定失败 id={}", orderLogisticsTrace.getId());
          results.add("锁定失败");
          continue;
        }
        // 防止重复订阅
        final Optional<KdyCallback> callbackOptional =
            iKdyCallbackService.selectByLogisticsNo(orderLogisticsTrace.getLogisticsNo());
        if (callbackOptional.isPresent()) {
          orderLogisticsTrace.setTraceStatus(OrderLogisticsTraceStatus.KDY_SUBSCRIBED);
          orderLogisticsTrace.setCallbackId(callbackOptional.get().getId());
          orderLogisticsTraceService.updateStatusAndUnlock(orderLogisticsTrace);
          continue;
        }
        try {
          final String logisticsName = orderLogisticsTrace.getLogisticsName();
          final Optional<String> kdyLogisticMapping =
              kdyLogisticMappingService.getStdNameByLogisticsName(logisticsName);
          String stdName;
          if (!kdyLogisticMapping.isPresent()) {
            if (orderLogisticsTrace.getLogisticsNo().startsWith("YT")) {
              stdName = "圆通速递";
            } else {
              final List<String> inferStdNames =
                  kdyLogisticMappingService.inferStdName(logisticsName);
              Alert.text(
                  MessageRobotCode.GLOBAL,
                  "物流名称:["
                      + logisticsName
                      + "]没有映射，可能结果：\n"
                      + inferStdNames.stream()
                          .map(
                              v ->
                                  String.format(
                                      "{\"stdName\":\"%s\",\"aliaName\":\"%s\"}", v, logisticsName))
                          .collect(Collectors.joining("\n")));
              throw ExceptionPlusFactory.bizException(ErrorCode.KDY_SUBSCRIBE_NO_MAPPING);
            }
          } else {
            stdName = kdyLogisticMapping.get();
          }
          if (stdName.contains("顺丰") || stdName.contains("丰网")) {
            results.add(CloseReasonType.SF.getDesc());
            orderLogisticsTrace.setCloseReason(CloseReasonType.SF);
            orderLogisticsTrace.setOpenTrace(0);
            orderLogisticsAbnormalityService.closeAbnormal(orderLogisticsTrace, CloseReasonType.SF);
          } else {
            final KdySubscribeData data =
                KdySubscribeData.of(
                    stdName,
                    orderLogisticsTrace.getLogisticsNo(),
                    null,
                    "erp:trace:" + orderLogisticsTrace.getId());
            kdyBizService.subscribe(data);
          }
          orderLogisticsTrace.setOpenTrace(1);
          orderLogisticsTrace.setSubscribeTime(DateUtil.currentTime());
          final Integer subscribeNum =
              Optional.ofNullable(orderLogisticsTrace.getSubscribeNum()).orElse(0);
          orderLogisticsTrace.setSubscribeNum(subscribeNum + 1);
          orderLogisticsTrace.setTraceStatus(OrderLogisticsTraceStatus.KDY_SUBSCRIBED);
          orderLogisticsTraceService.updateStatusAndUnlock(orderLogisticsTrace);
          log.debug("[客服物流异常][扫描订阅]订阅成功 id={}", orderLogisticsTrace.getId());
          results.add("订阅成功");
        } catch (Exception e) {
          results.add(e.getMessage());
          orderLogisticsTrace.setErr(e.getMessage());
          OrderLogisticsTraceStatus traceStatus = OrderLogisticsTraceStatus.KDY_SUBSCRIBE_FAIL;
          if (e instanceof BizException) {
            final BizException e1 = (BizException) e;
            if (Objects.equals(e1.getErrCode(), ErrorCode.KDY_SUBSCRIBE_NO_MAPPING.getCode())) {
              traceStatus = OrderLogisticsTraceStatus.KDY_SUBSCRIBE_DELAY;
            }
          }
          orderLogisticsTrace.setTraceStatus(traceStatus);
          orderLogisticsTraceService.updateStatusAndUnlock(orderLogisticsTrace);
          if (traceStatus == OrderLogisticsTraceStatus.KDY_SUBSCRIBE_FAIL) {
            log.error("[客服物流异常][扫描订阅]订阅失败:{} id={}", e.getMessage(), orderLogisticsTrace.getId());
          } else {
            log.warn(
                "[客服物流异常][扫描订阅]订阅失败，可重试异常:{} id={}", e.getMessage(), orderLogisticsTrace.getId());
          }
        }
      }
    }
    log.info("[客服物流异常][扫描订阅]{}条记录处理完成，处理结果：{}", traceList.size(), results);
  }

  @XxlJob("AfterSaleLogistics:scanLockTimeout")
  @XxlJobAutoRegister(
      cron = "0 * * * * ? *",
      author = "徵乌",
      jobDesc = "物流异常-扫描锁定超时",
      autoUpdate = false)
  public void scanLockTimeout() {
    final boolean update =
        orderLogisticsTraceService
            .lambdaUpdate()
            .le(OrderLogisticsTrace::getLockTime, DateUtil.currentTime() - 10 * 60)
            .gt(OrderLogisticsTrace::getLockTime, 0L)
            .set(OrderLogisticsTrace::getLockTime, 0L)
            .update();
    log.info("[客服物流异常]扫描锁定超时，解锁{}条记录", update);
  }

  @XxlJob("AfterSaleLogistics:scanSubscribeDelay")
  @XxlJobAutoRegister(
      cron = "0 0 * * * ? *",
      author = "徵乌",
      jobDesc = "物流异常-扫描延迟订阅",
      autoUpdate = false)
  public void scanSubscribeDelay() {
    int num = 1000;
    for (; ; ) {
      final int updateCount =
          orderLogisticsTraceService
              .getDaddyBaseMapper()
              .updateStatusToBatch(
                  OrderLogisticsTraceStatus.KDY_SUBSCRIBE_DELAY,
                  OrderLogisticsTraceStatus.KDY_WAIT_SUBSCRIBE,
                  num);
      log.info("[客服物流异常]超时延迟订阅，处理{}单", updateCount);
      if (updateCount == 0) {
        break;
      }
    }
  }

//  @XxlJob("warehouseLogisticStaticsJob")
//  @XxlJobAutoRegister(cron = "0 */1 * * * ?", author = "七喜", jobDesc = "物流异常-仓库统计")
  public void hh() {
    List<String> warehouseNoList =
        orderLogisticsTraceService
            .lambdaQuery()
            .select(OrderLogisticsTrace::getStockoutWarehouseNo)
            .groupBy(OrderLogisticsTrace::getStockoutWarehouseNo)
            .list()
            .stream()
            .map(OrderLogisticsTrace::getStockoutWarehouseNo)
            .collect(Collectors.toList());
    String dateStr = DateUtil.format(LocalDateTime.now(), DateUtil.DEFAULT_DATE);

    RSemaphore semaphore = redissonClient.getSemaphore("warehouseLogisticStaticsJob");
    semaphore.trySetPermits(10);
    for (String warehouseNo : warehouseNoList) {
      ThreadUtil.getThreadPool(PoolEnum.JOB_POOL)
          .execute(
              () -> {
                try {
                  semaphore.acquire();
                  warehouseStatics(warehouseNo, dateStr);
                } catch (Exception e) {
                  log.error("warehouseLogisticStaticsJob error.warehouseNo:{}", warehouseNo, e);
                } finally {
                  semaphore.release();
                }
              });
    }
  }

  public void warehouseStatics(String warehouseNo, String dateStr) {
    String key = warehouseNo + "_" + dateStr;
    RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
    Long start = DateUtil.currentDayStart();
    Long end = DateUtil.currentDayEnd();
    if (atomicLong.get() >= Long.parseLong(afterSaleLogisticsConfig.getOneDayLimit())) {
      Integer count =
          abnormalityAlertService
              .lambdaQuery()
              .eq(OrderLogisticsAbnormalityAlert::getWarehouseNo, warehouseNo)
              .eq(OrderLogisticsAbnormalityAlert::getAlertType, 1)
              .between(OrderLogisticsAbnormalityAlert::getCreatedAt, start, end)
              .eq(OrderLogisticsAbnormalityAlert::getIsActive, 1)
              .count();
      if (count == 0) {
        alertCustomerService24h(warehouseNo, (int) atomicLong.get());
        OrderLogisticsAbnormalityAlert alert = new OrderLogisticsAbnormalityAlert();
        alert.setAlertType(1);
        alert.setWarehouseNo(warehouseNo);
        alert.setAlertNum((int) atomicLong.get());
        abnormalityAlertService.save(alert);
      }
    } else {
      String yDateStr = DateUtil.format(LocalDateTime.now().plusDays(-1), DateUtil.DEFAULT_DATE);
      String yKey = warehouseNo + "_" + yDateStr;
      RAtomicLong yVal = redissonClient.getAtomicLong(yKey);
      if ((yVal.get() + atomicLong.get())
          > Long.parseLong(afterSaleLogisticsConfig.getTwoDaysLimit())) {
        Integer count =
            abnormalityAlertService
                .lambdaQuery()
                .eq(OrderLogisticsAbnormalityAlert::getWarehouseNo, warehouseNo)
                .eq(OrderLogisticsAbnormalityAlert::getAlertType, 2)
                .between(OrderLogisticsAbnormalityAlert::getCreatedAt, start, end)
                .eq(OrderLogisticsAbnormalityAlert::getIsActive, 1)
                .count();
        if (count == 0) {
          alertCustomerService48h(warehouseNo, (int) (yVal.get() + atomicLong.get()));
          OrderLogisticsAbnormalityAlert alert = new OrderLogisticsAbnormalityAlert();
          alert.setAlertType(2);
          alert.setWarehouseNo(warehouseNo);
          alert.setAlertNum((int) (yVal.get() + atomicLong.get()));
          alert.setIsActive(1);
          abnormalityAlertService.save(alert);
        }
      }
    }
  }

  private void alertCustomerService24h(String warehouseNo, Integer num) {
    Optional<Warehouse> warehouseOptional = warehouseGateway.getWarehouse(warehouseNo);
    String warehouseName =
        warehouseOptional.isPresent() ? warehouseOptional.get().getName() : warehouseNo;
    String msg =
        StrUtil.format(
            "批量物流异常预警：{}{}当天异常订单新增【{}】条，请查看原因并处理！",
            warehouseName,
            DateUtil.format(DateUtil.currentTime()),
            num);
    Alert.text(MessageRobotCode.CUSTOMER_SERVICE, msg);
  }

  private void alertCustomerService48h(String warehouseNo, Integer num) {
    Optional<Warehouse> warehouseOptional = warehouseGateway.getWarehouse(warehouseNo);
    String warehouseName =
        warehouseOptional.isPresent() ? warehouseOptional.get().getName() : warehouseNo;
    String msg = StrUtil.format("批量物流异常预警：{}两天内异常订单新增【{}】条，请查看原因并处理！", warehouseName, num);
    Alert.text(MessageRobotCode.CUSTOMER_SERVICE, msg);
  }

  @XxlJob("abnormalStaticsRemind9Am")
  @XxlJobAutoRegister(
      cron = "0 0 9 * * ?",
      author = "七喜",
      jobDesc = "物流异常-统计每天9点",
      autoUpdate = false)
  public void staticsMsg9Am() {
    String abnormalStaticsMsg = afterSaleLogisticsBizService.getAbnormalStaticsMsg();
    Alert.text(MessageRobotCode.CUSTOMER_SERVICE, abnormalStaticsMsg);
  }

  @XxlJob("abnormalStaticsRemind12Am")
  @XxlJobAutoRegister(
      cron = "0 0 12 * * ?",
      author = "七喜",
      jobDesc = "物流异常-统计每天12点",
      autoStartUp = true)
  public void staticsMsg12Am() {
    String abnormalStaticsMsg = afterSaleLogisticsBizService.getAbnormalStaticsMsg();
    Alert.text(MessageRobotCode.CUSTOMER_SERVICE, abnormalStaticsMsg);
  }

  @XxlJob("abnormalStaticsRemind3Pm")
  @XxlJobAutoRegister(
      cron = "0 0 15 * * ?",
      author = "七喜",
      jobDesc = "物流异常-统计每天15点",
      autoUpdate = false)
  public void staticsMsg3Pm() {
    String abnormalStaticsMsg = afterSaleLogisticsBizService.getAbnormalStaticsMsg();
    Alert.text(MessageRobotCode.CUSTOMER_SERVICE, abnormalStaticsMsg);
  }

  @XxlJob("AfterSaleLogistics:abnormalScan")
  @XxlJobAutoRegister(
      cron = "0 6 * * * ?",
      author = "徴乌",
      jobDesc = "物流异常-全局扫描（新）",
      autoUpdate = false)
  public void abnormalScan() {
    try {
      final AbnormalScanCmd abnormalScanCmd = JobUtils.getJobParam(AbnormalScanCmd.class);
      abnormalScanService.abnormalScan(abnormalScanCmd);
    } catch (Exception e) {
      log.error("[物流异常]全局扫描异常", e);
    }
  }
}
