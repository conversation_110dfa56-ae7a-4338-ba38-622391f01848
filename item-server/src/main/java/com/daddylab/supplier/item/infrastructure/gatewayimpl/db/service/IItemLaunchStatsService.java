package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchStats;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemLaunchStatsMapper;

import java.util.Collection;

/**
 * <p>
 * 商品上新统计数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
public interface IItemLaunchStatsService extends IDaddylabServicePlus<ItemLaunchStats, ItemLaunchStatsMapper> {

    /**
     * 审核信息统计
     *
     * @param itemId              商品ID
     * @param legalProcessorId    法务经办人ID
     * @param legalAuditStartTime 法务办理开始时间
     * @param legalAuditEndTime   法务班级结束时间
     * @param qcProcessorId       QC经办人ID
     * @param qcAuditStartTime    QC办理开始时间
     * @param qcAuditEndTime      QC办理结束时间
     */
    ItemLaunchStats auditStat(Long itemId, Long legalProcessorId, Long legalAuditStartTime,
            Long legalAuditEndTime, Long qcProcessorId, Long qcAuditStartTime, Long qcAuditEndTime);

    ItemLaunchStats getItemLaunchStats(Long itemId);

    /**
     * 修正商品的上新时间（上新时间可能并不是触发状态修改的时间）
     *
     * @param itemId     商品
     * @param launchTime 上新时间
     */
    void fixLaunchTime(Long itemId, Long launchTime);

    /**
     * 删除商品上新统计记录
     * @param itemId 商品ID
     * @return 是否删除成功
     */
    boolean delStats(Long itemId);

    /**
     * 批量删除商品上新统计记录
     * @param itemIds 商品ID
     * @return 是否删除成功
     */
    boolean delStatsBatch(Collection<Long> itemIds);

}
