package com.daddylab.supplier.item.application.item.combinationItem.dto.cmd;

import com.alibaba.cola.dto.Command;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.CombinationItemType;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/11 10:37 上午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("组合商品保存参数")
public class CombinationItemSaveCmd extends Command {

    private static final long serialVersionUID = -2140672696310392642L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("组合装名称")
    @Length(max = 100)
    private String name;

    @ApiModelProperty("条码")
    @Length(max = 15)
    private String barCode;

    @ApiModelProperty("属性")
    private CombinationItemType type = CombinationItemType.COMMON;

    @ApiModelProperty("重量。4位小数")
    private BigDecimal weight;

    @ApiModelProperty("采购成本价")
    private BigDecimal procurementPrice;

    @ApiModelProperty("采购成本价开始时间")
    private Long procurementPriceStart;

    public Long getProcurementPriceStart() {
        return Objects.isNull(procurementPriceStart) ? DateUtil.todayZeroTime() : DateUtil.targetDayZeroTime(procurementPriceStart);
    }

    @ApiModelProperty("销售价")
    private BigDecimal salesPrice;

    @ApiModelProperty("销售价开始时间")
    private Long salesPriceStart;

    public Long getSalesPriceStart() {
        return Objects.isNull(salesPriceStart) ? DateUtil.todayZeroTime() : DateUtil.targetDayZeroTime(salesPriceStart);
    }

    @ApiModelProperty("组合对象")
    @Valid
    private List<ComposeSkuCmd> composeSkuCmdList;

    @ApiModelProperty("前端忽视")
    private String uniqueCode;

    @ApiModelProperty("计算成本")
    private BigDecimal calculateProcurementPrice;

    @ApiModelProperty("计算售价")
    private BigDecimal calculateSalesPrice;

    /**
     * 平台佣金
     */
    @ApiModelProperty("平台佣金")
    private BigDecimal platformCommission;

    @ApiModelProperty("平台佣金开始时间")
    private Long platformCommissionStart;

    /**
     * 合同销售价
     */
    @ApiModelProperty("合同销售价")
    private BigDecimal contractSalePrice;

    @ApiModelProperty("合同销售价开始时间")
    private Long contractSalePriceStart;

    @ApiModelProperty("合作方类型")
    private List<Integer> corpType;

    @ApiModelProperty("业务类型")
    private List<Integer> bizType;

    @ApiModelProperty("供应商ID")
    private Long providerId;


}
