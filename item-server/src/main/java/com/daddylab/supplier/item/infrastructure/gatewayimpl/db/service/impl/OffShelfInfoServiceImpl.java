package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.application.offShelf.dto.OffShelfPageQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OffShelfInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OffShelfInfoMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOffShelfInfoService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 下架管理-下架流程信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Service
public class OffShelfInfoServiceImpl extends DaddyServiceImpl<OffShelfInfoMapper, OffShelfInfo> implements IOffShelfInfoService {

    @Override
    public Long getMaxId() {
        final List<OffShelfInfo> list = this.lambdaQuery()
                                            .select(OffShelfInfo::getId)
                                            .orderByDesc(OffShelfInfo::getId)
                                            .last("limit 1")
                                            .list();
        return CollUtil.isNotEmpty(list) ? list.get(0).getId() : null;
    }

    @Override
    public List<OffShelfInfo> listByProcessInstId(String processInstanceId) {
        return lambdaQuery()
                .eq(OffShelfInfo::getProcessInstId, processInstanceId)
                .list();
    }

    @Override
    public PageInfo<OffShelfInfo> page(OffShelfPageQuery query) {
        return PageHelper.startPage(query.getPageIndex(),
                                    query.getPageSize(),
                                    query.isNeedTotalCount())
                         .doSelectPageInfo(() -> getDaddyBaseMapper().page(query));
    }
}
