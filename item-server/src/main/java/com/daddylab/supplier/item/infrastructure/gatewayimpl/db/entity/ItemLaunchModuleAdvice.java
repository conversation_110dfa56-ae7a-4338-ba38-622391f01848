package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerModuleId;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemLaunchProcessNodeId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 商品库模块审批修改建议
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemLaunchModuleAdvice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 后端商品ID
     */
    private Long itemId;

    /**
     * 第几轮审核
     */
    private Integer round;

    /**
     * 审批节点
     */
    private ItemLaunchProcessNodeId node;

    /**
     * 模块
     */
    private ItemDrawerModuleId module;

    /**
     * 建议内容
     */
    private String advice;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 类型
     */
    private String type;

    /**
     * 审核通过/拒绝
     */
    private boolean pass;

    private Long liveVerbalTrickId;

    /**
     * 是否是临时建议
     */
    private Integer temporary;

}
