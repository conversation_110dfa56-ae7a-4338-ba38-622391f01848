package com.daddylab.supplier.item.controller.brand;

import cn.hutool.http.HttpUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.daddylab.supplier.item.application.brand.BrandBizService;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.dto.IdCmd;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.brand.dto.*;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.HttpHeaderUtil;
import com.daddylab.supplier.item.infrastructure.utils.IOUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.inject.Inject;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11 9:56 上午
 * @description
 */
@Slf4j
@RestController
@RequestMapping("/brand")
@Api(value = "品牌相关api", tags = "品牌相关API")
public class BrandController {

    @Autowired
    private BrandBizService brandBizService;
    @Inject
    private OperateLogBizService operateLogBizService;

    @PostMapping(value = "/dropDownBrandList")
    @ApiOperation("品牌下拉列表")
    public MultiResponse<BrandDropDownItem> dropDownBrandList(@RequestBody @Valid BrandDropDownQuery query) {
        return brandBizService.dropDownList(query);
    }

    @PutMapping(value = "/createOrUpdateBrand")
    @ApiOperation("品牌创建/更新")
    public SingleResponse<BrandDetail> createOrUpdateBrand(@RequestBody @Validated CreateOrUpdateBrandCmd cmd) {
        return brandBizService.createOrUpdateBrand(cmd);
    }

    @PostMapping(value = "/queryBrandList")
    @ApiOperation("品牌列表")
    public PageResponse<BrandListItem> queryBrandList(@RequestBody @Valid BrandQuery brandQuery) {
        return brandBizService.pageQueryBrandList(brandQuery);
    }

    @PostMapping(value = "/getBrandDetail")
    @ApiOperation("品牌详情")
    public SingleResponse<BrandDetail> getBrand(@RequestBody @Valid IdCmd idCmd) {
        return brandBizService.getBrandDetail(idCmd.getId());
    }

    @PostMapping(value = "/deleteBrand")
    @ApiOperation("删除品牌")
    public Response deleteBrand(@RequestBody @Valid IdCmd idCmd) {
        try {
            return brandBizService.deleteBrand(idCmd.getId());
        } catch (BizException e) {
            //返回一个特殊的成功响应，以便于前端处理
            if (e.getErrCode().equals(ErrorCode.OPERATION_REJECT.getCode())) {
                final Response response = new Response();
                response.setSuccess(true);
                response.setErrCode(e.getErrCode());
                response.setErrMessage(e.getMessage());
                return response;
            }
            throw e;
        }
    }

    @GetMapping(value = "/operateLogs")
    @ApiOperation("操作日志")
    public Response operateLogs(Long targetId) {
        return operateLogBizService.getOperateLogs(OperateLogTarget.BRAND, targetId);
    }

    @GetMapping(value = "/excelTemplate")
    @ApiOperation("下载Excel模板")
    @Auth(noAuth = true)
    public void excelTemplate(HttpServletResponse response) {
        try {
            HttpHeaderUtil.setXlsxAttachmentHeaders(response, "品牌导入模板" + DateUtil.currentTime());
            HttpUtil.download(brandBizService.getExcelTemplateUrl(), response.getOutputStream(), false);
        } catch (IOException e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.FILE_UPLOAD_ERROR, "下载文件失败，输出流写入异常");
        }
    }

    @PostMapping(value = "/importExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("导入Excel")
    public Response importExcel(@ApiParam(name = "file", value = "文件", required = true)
                                @RequestParam("file") MultipartFile file) {
        try {
            brandBizService.importExcel(file.getInputStream());
            return Response.buildSuccess();
        } catch (IOException e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.FILE_UPLOAD_ERROR, "导入Excel失败，读取文件异常");
        }
    }

    @PostMapping(value = "/importWdtCsv", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("导入旺店通导出的csv文件")
    public Response importCsv(@ApiParam(name = "file", value = "文件", required = true)
                              @RequestParam("file") MultipartFile file) {
        try {
            brandBizService.importWdtCsv(IOUtil.toBOMInputStream(file.getInputStream()));
            return Response.buildSuccess();
        } catch (IOException e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.FILE_UPLOAD_ERROR, "导入Excel失败，读取文件异常");
        }
    }

    @GetMapping(value = "/exportBrandList")
    @ApiOperation("品牌导出")
    public void exportBrandList(HttpServletResponse response, @Valid BrandQuery brandQuery) {
        try {
            brandQuery.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(brandQuery.getBusinessLine()));
            brandBizService.exportExcel(brandQuery, response.getOutputStream());
            HttpHeaderUtil.setXlsxAttachmentHeaders(response, "品牌导出");
        } catch (IOException e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.FILE_UPLOAD_ERROR, "导出文件失败，输出流写入异常");
        }
    }

    @PostMapping(value = "/createExportBrandTask")
    @ApiOperation("创建品牌导出任务")
    public SingleResponse<Long> createExportBrandTask(@Valid @RequestBody BrandQuery brandQuery) {
        return SingleResponse.of(brandBizService.exportExcel(brandQuery));
    }

}
