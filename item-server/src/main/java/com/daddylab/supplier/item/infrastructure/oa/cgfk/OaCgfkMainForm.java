package com.daddylab.supplier.item.infrastructure.oa.cgfk;

import com.daddylab.supplier.item.infrastructure.oa.constants.Partner;
import com.daddylab.supplier.item.infrastructure.oa.constants.PaymentPurpose;
import com.daddylab.supplier.item.infrastructure.oa.constants.PaymentType;
import com.daddylab.supplier.item.infrastructure.oa.constants.ProcurementType;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.annotation.Nullable;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@Data
public class OaCgfkMainForm {
  @JsonProperty("归属公司")
  @NotBlank
  private String belongCompany;

  @JsonProperty("付款方")
  @NotBlank
  private String payer;

  /** 传付款申请单编号 */
  @JsonProperty("流程编号")
  @NotBlank
  private String processNo;

  @JsonProperty("收款方")
  @NotBlank
  private String payee;

  @JsonProperty("收款人开户行")
  @NotBlank
  private String payeeBank;

  @JsonProperty("收款人账号")
  @NotBlank
  private String payeeBankAccount;

  @JsonProperty("收款人银联号")
  @NotBlank
  private String payeeBankNo;

  @JsonProperty("采购类型")
  @NotNull
  private ProcurementType procurementType;

  @JsonProperty("采购员")
  @NotBlank
  private String buyer;

  @JsonProperty("采购")
  private String buyerOaId;

  /** 款项性质 默认货款 */
  @JsonProperty("款项性质")
  @NotBlank
  private String natureOfPayment;

  /** 可以不传，OA会自动判断 */
  @JsonProperty("付款类型")
  @Nullable
  private PaymentType paymentType;

  @JsonProperty("备注")
  @Nullable
  private String remark;

  @JsonProperty("付款用途")
  @NotNull
  private PaymentPurpose paymentPurpose;

  @JsonProperty("付款比例")
  @DecimalMax("1")
  @NotNull
  private BigDecimal paymentRatio;

  @JsonProperty("总申请付款金额")
  @NotNull
  @PositiveOrZero
  private BigDecimal totalRequestedPaymentAmount;

  @JsonProperty("其他金额扣减项")
  @NotNull
  @PositiveOrZero
  private BigDecimal deductionsFromOtherAmounts;

  /** 环境 开发dev，测试test，灰度gray，正式prod */
  @JsonProperty("环境")
  @NotBlank
  private String env;

  /** 当前审批节点 0发起者，1跳过主管，2跳过经理，3跳过分管，4跳过业务财务，5跳过财务经理，6跳过总裁，7跳过出纳（这步不能跳过，要制单） */
  @JsonProperty("当前审批节点")
  @NotNull
  private Integer currentNode;

  /**
   *
   * <li>电商：9005488124879391011
   * <li>绿色家装：937470142167662875
   */
  @JsonProperty("合作方")
  private Partner partner;
}
