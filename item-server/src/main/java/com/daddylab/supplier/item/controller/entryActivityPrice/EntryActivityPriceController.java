package com.daddylab.supplier.item.controller.entryActivityPrice;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.entryActivityPrice.EntryActivityPriceService;
import com.daddylab.supplier.item.application.entryActivityPrice.models.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2024/10/4
 */
@RestController
@RequestMapping("/entryActivityPrice")
@Api(value = "入驻活动价格", tags = {"入驻活动价格", "采购管理"})
public class EntryActivityPriceController {
    @Resource
    private EntryActivityPriceService entryActivityPriceService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询")
    public PageResponse<EntryActivityPricePageVO> page(@Validated @RequestBody EntryActivityPricePageQry qry) {
        return entryActivityPriceService.page(qry);
    }

    @ApiOperation("保存")
    @PostMapping("/save")
    public Response save(@Validated @RequestBody EntryActivityPriceSaveReq cmd) {
        return entryActivityPriceService.save(cmd);
    }

    @ApiOperation("删除")
    @PostMapping("/delete")
    public Response delete(@Validated @RequestBody EntryActivityPriceDeleteReq cmd) {
        return entryActivityPriceService.delete(cmd);
    }

    @PostMapping("/itemInfo")
    @ApiOperation("商品信息查询")
    public SingleResponse<EntryActivityPriceItemInfoVO> itemInfo(@Validated @RequestBody EntryActivityPriceItemInfoQry qry) {
        return entryActivityPriceService.itemInfo(qry);
    }

    @PostMapping("/skuInfo")
    @ApiOperation("商品SKU信息查询")
    public SingleResponse<EntryActivityPriceSkuInfoVO> itemInfo(@Validated @RequestBody EntryActivityPriceSkuInfoQry qry) {
        return entryActivityPriceService.skuInfo(qry);
    }


    @ApiOperation(value = "导入")
    @PostMapping("/importExcel")
    public Response importExcel(@ApiParam(name = "file", value = "文件", required = true) @RequestParam("file")
                                MultipartFile file) throws IOException {
        return entryActivityPriceService.importExcel(file.getInputStream());
    }

    @ApiOperation(value = "导出")
    @PostMapping("/exportExcel")
    public Response exportExcel(@Validated @RequestBody EntryActivityPricePageQry query) {
        return entryActivityPriceService.exportExcel(query);
    }

    @ApiOperation(value = "准备发起确认【查看待发起商品列表】")
    @PostMapping("/startConfirmation/prepare")
    public SingleResponse<PrepareStartConfirmationResult> prepareStartConfirmation(@Validated @RequestBody PrepareStartConfirmationReq req) {
        return entryActivityPriceService.prepareStartConfirmation(req);
    }

    @ApiOperation(value = "发起确认")
    @PostMapping("/startConfirmation")
    public Response startConfirmation(@Validated @RequestBody StartConfirmationReq req) {
        return entryActivityPriceService.startConfirmation(req);
    }

    @ApiOperation(value = "查看凭证")
    @PostMapping("/viewVoucher")
    public SingleResponse<ViewVoucherVO> viewVoucher(@Validated @RequestBody ViewVoucherReq req) {
        return entryActivityPriceService.viewVoucher(req);
    }
}
