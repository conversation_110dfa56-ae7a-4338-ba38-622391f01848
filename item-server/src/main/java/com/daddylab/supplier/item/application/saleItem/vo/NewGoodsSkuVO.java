package com.daddylab.supplier.item.application.saleItem.vo;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2022年07月27日 2:13 PM
 */
@Data
@ApiModel("新品商品返回skuList封装")
public class NewGoodsSkuVO {

    private Long newGoodsDbId;

    private Long itemId;

    @ApiModelProperty(value = "sku编码")
    private String skuCode;

    @ApiModelProperty(value = "sku指定编码")
    private String specialSkuCode;

    @ApiModelProperty(value = "颜色/规格")
    private String specs;

    @ApiModelProperty(value = "采购成本")
    private BigDecimal costPrice;

    public BigDecimal getCostPrice() {
        if (UserPermissionJudge.hasApiPermission(GlobalConstant.VIEW_ITEM_PRICE_RIGHT_URI)) {
            return costPrice;
        }
        return null;
    }

    @ApiModelProperty(value = "产品划线价")
    private BigDecimal linePrice;

    @ApiModelProperty(value = "产品日销价")
    private BigDecimal dailyPrice;

    @ApiModelProperty(value = "日常活动机制")
    private String dailyActivities;

    @ApiModelProperty(value = "S级/新品活动价")
    private BigDecimal activePrice;

    @ApiModelProperty(value = "新品活动开始周期")
    private Long activePeriodStart;

    @ApiModelProperty(value = "新品活动结束周期")
    private Long activePeriodEnd;

    @ApiModelProperty(value = "S级/新品活动机制")
    private String activeContent;

    @ApiModelProperty(value = "新品直播机制")
    private String liveActive;

//    @ApiModelProperty(value = "是否参与满减 0:是 1:否")
//    private Integer isReduce;

//    @ApiModelProperty(value = "是否叠加券")
//    private Integer isCoupon;

//    @ApiModelProperty(value = "渠道活动最低价")
//    private BigDecimal channelLowest;

    @ApiModelProperty(value = "新品活动周期是否长期有效")
    private Boolean isLongTerm;

    @ApiModelProperty(value = "单买到手价")
    private BigDecimal singleBuyPrice;

//    @ApiModelProperty("运营意见反馈")
//    private String runFeedback;

    @ApiModelProperty("A级活动售价")
    private String aLevelActivityPrice;

    @ApiModelProperty("A级活动赠品")
    private String aLevelActivityGift;

    @ApiModelProperty("A级活动直播价")
    private String aLevelActivityLivePrice;

    @ApiModelProperty("A级活动直播赠品")
    private String aLevelActivityLiveGift;

    @ApiModelProperty("S级大促售价")
    private String sLevelPromotePrice;

    @ApiModelProperty("S级大促机制")
    private String sLevelPromoteRule;

    @ApiModelProperty("S级一口价/直播价")
    private String sLevelPromoteLivePrice;

    @ApiModelProperty("S级一口价活动机制/直播机制")
    private String sLevelPromoteLiveRule;

    @ApiModelProperty(value = "合同销售价")
    private BigDecimal contractSalePrice;

    @ApiModelProperty(value = "平台佣金")
    private BigDecimal platformCommission;

    /**
     * 供货价=合同销售价*（1-平台佣金）
     */
    @ApiModelProperty(value = "供货价")
    private BigDecimal supplyPrice;


    public static NewGoodsSkuVO ofNewGoodsVo(NewGoodsVo newGoodsVo) {
        NewGoodsSkuVO one = new NewGoodsSkuVO();
        one.setNewGoodsDbId(newGoodsVo.getId());
        one.setItemId(newGoodsVo.getItemId());
        one.setSkuCode(newGoodsVo.getSkuCode());
        one.setSpecialSkuCode(newGoodsVo.getSpecialSkuCode());
        one.setSpecs(newGoodsVo.getSpecs());
        if (UserPermissionJudge.hasApiPermission(GlobalConstant.VIEW_ITEM_PRICE_RIGHT_URI)) {
            one.setCostPrice(newGoodsVo.getCostPrice());
        }
        one.setLinePrice(newGoodsVo.getLinePrice());
        one.setDailyPrice(newGoodsVo.getDailyPrice());
        one.setActivePrice(newGoodsVo.getActivePrice());
        one.setActivePeriodStart(newGoodsVo.getActivePeriodStart());
        one.setActivePeriodEnd(newGoodsVo.getActivePeriodEnd());
        one.setActiveContent(newGoodsVo.getActiveContent());
        one.setLiveActive(newGoodsVo.getLiveActive());
//        one.setIsReduce(newGoodsVo.getIsReduce());
//        one.setIsCoupon(newGoodsVo.getIsCoupon());
//        one.setChannelLowest(newGoodsVo.getChannelLowest());
        one.setIsLongTerm(newGoodsVo.getIsLongTerm());
        one.setDailyActivities(newGoodsVo.getDailyActivities());
        one.setSingleBuyPrice(newGoodsVo.getSingleBuyPrice());
        one.setALevelActivityGift(newGoodsVo.getALevelActivityGift());
        one.setALevelActivityLivePrice(newGoodsVo.getALevelActivityLivePrice());
        one.setALevelActivityLiveGift(newGoodsVo.getALevelActivityLiveGift());
        one.setALevelActivityPrice(newGoodsVo.getALevelActivityPrice());
        one.setSLevelPromoteRule(newGoodsVo.getSLevelPromoteRule());
        one.setSLevelPromotePrice(newGoodsVo.getSLevelPromotePrice());
        one.setSLevelPromoteLiveRule(newGoodsVo.getSLevelPromoteLiveRule());
        one.setSLevelPromoteLivePrice(newGoodsVo.getSLevelPromoteLivePrice());
//        one.setRunFeedback(newGoodsVo.getRunFeedback());
        one.setProps(newGoodsVo.getProps());

        if (newGoodsVo.getBusinessLines().contains("3")) {
            one.setContractSalePrice(newGoodsVo.getContractSalePrice());
            one.setPlatformCommission(newGoodsVo.getPlatformCommission());
            if (Objects.nonNull(newGoodsVo.getContractSalePrice())
                    && Objects.nonNull(newGoodsVo.getPlatformCommission())
                    && BigDecimal.ZERO.compareTo(newGoodsVo.getPlatformCommission()) != 0
            ) {
                final BigDecimal rate = BigDecimal.valueOf(100).subtract(newGoodsVo.getPlatformCommission())
                        .divide(BigDecimal.valueOf(100), 6, RoundingMode.HALF_UP);
                one.setSupplyPrice(newGoodsVo.getContractSalePrice().multiply(rate).setScale(2, RoundingMode.HALF_UP));
            } else {
                one.setSupplyPrice(newGoodsVo.getContractSalePrice());
            }
        }

        return one;
    }


    public static List<NewGoodsSkuVO> ofNewGoodsVos(List<NewGoodsVo> newGoodsVoList) {
        if (CollUtil.isEmpty(newGoodsVoList)) {
            return new LinkedList<>();
        }

        return newGoodsVoList.stream()
                .sorted(Comparator.comparing(NewGoodsVo::getLaunchDate).reversed())
                .map(NewGoodsSkuVO::ofNewGoodsVo).collect(Collectors.toList());
    }

    /**
     * 商品SKU额外拓展属性
     */
    @ApiModelProperty("商品SKU额外拓展属性")
    private LinkedHashMap<String, Object> props;
}
