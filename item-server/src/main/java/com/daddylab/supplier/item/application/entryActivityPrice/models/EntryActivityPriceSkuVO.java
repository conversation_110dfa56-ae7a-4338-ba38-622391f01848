package com.daddylab.supplier.item.application.entryActivityPrice.models;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/10/4
 */
@Data
public class EntryActivityPriceSkuVO {
    private Long id;
    @NotBlank
    private String skuCode;
    @Positive
    private Long skuId;
    @NotBlank
    private String specification;
    @Positive
    private Long activeStart;
    @Positive
    private Long activeEnd;
    @NotNull
    @Positive
    private BigDecimal contractSalePrice;
    @NotNull
    @Positive
    private BigDecimal platformCommission;
    private String activityRemark;
}
