package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemOperator;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemOperatorMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemOperatorService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品运营人员 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class ItemOperatorServiceImpl extends DaddyServiceImpl<ItemOperatorMapper, ItemOperator> implements IItemOperatorService {

}
