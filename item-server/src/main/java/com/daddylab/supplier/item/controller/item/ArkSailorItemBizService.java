package com.daddylab.supplier.item.controller.item;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.ark.sailor.item.domain.query.item.ItemPageQuery;
import com.daddylab.ark.sailor.item.domain.vo.item.ItemListVO;
import com.daddylab.ark.sailor.item.domain.vo.item.ItemSkuVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ErpItemSkuUpdateStockParam;
import com.daddylab.supplier.item.types.item.ItemListWithSkuVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/22
 */
public interface ArkSailorItemBizService {


    PageResponse<ItemListWithSkuVO> itemWithSkuPageQuery(ItemPageQuery query);

    MultiResponse<ItemListVO> listItemByItemNos(List<String> itemNos);

    PageResponse<ItemListVO> itemPageQuery(ItemPageQuery query);

    MultiResponse<ItemSkuVO> itemSkuQueryByItemIds(List<Long> itemIds);

    SingleResponse<Boolean> updateStock(ErpItemSkuUpdateStockParam erpItemSkuUpdateStockParam);

}
