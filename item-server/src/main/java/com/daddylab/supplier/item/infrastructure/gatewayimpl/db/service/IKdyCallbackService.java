package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.KdyCallback;

import java.util.Optional;

/**
 * 快刀云订阅记录 服务类
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
public interface IKdyCallbackService extends IDaddyService<KdyCallback> {

  Optional<KdyCallback> selectByLogisticsNo(String logisticsNo);

  Long selectIdByLogisticsNo(String logisticsNo);
}
