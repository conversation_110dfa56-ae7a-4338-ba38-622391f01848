package com.daddylab.supplier.item.application.afterSaleLogistics.dto;

import io.swagger.annotations.ApiModelProperty;
import java.util.Collection;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/25
 */
@Data
public class AbnormalScanCmd {
    @ApiModelProperty(value = "物流跟踪时间过滤", notes = "过滤最近跟踪时间小于这个时间的，即长时间未更新物流轨迹的")
    private Long traceTime;

    @ApiModelProperty("物流跟踪ID")
    private Collection<Long> traceIds;

    @ApiModelProperty("是否强制解锁")
    private boolean forceUnlock;
}
