//package com.daddylab.supplier.item.controller.open;
//
//import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
//import org.springframework.util.Assert;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR> up
// * @date 2022年10月18日 9:54 AM
// */
//@RestController
//public class PartnerBaseController {
//
//    @Resource
//    ProviderGateway providerGateway;
//
//    public PartnerProviderResp getPartnerProviderInfo(String partnerToken) {
//        Assert.hasText(partnerToken, "P系统token不得为空");
//        return providerGateway.partnerQuery(partnerToken);
//    }
//
//
//}
