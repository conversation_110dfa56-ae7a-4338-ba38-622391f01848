//package com.daddylab.supplier.item.application.message.wechat;
//
//import cn.hutool.http.HttpUtil;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatMsg;
//import com.daddylab.supplier.item.infrastructure.utils.AspectUtil;
//import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.aspectj.lang.JoinPoint;
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.After;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Pointcut;
//import org.springframework.stereotype.Component;
//import org.springframework.util.StringUtils;
//
//import javax.annotation.Resource;
//import java.lang.reflect.Method;
//import java.util.Objects;
//
///**
// * <AUTHOR> up
// * @date 2023年08月23日 11:54 AM
// */
//@Slf4j
//@Aspect
//@Component
//public class RemindByCardTextAspect {
//
//    @Resource
//    private MsgSender msgSender;
//
//    private final String pointcutFeign = "@annotation(com.daddylab.supplier.item.application.message.wechat.RemindByCardText)";
//
//    @Pointcut(value = pointcutFeign)
//    public void oneFeign() {
//
//    }
//
//    @After(value = "oneFeign()")
//    public void after(JoinPoint joinPoint) throws Throwable {
//        Method method = AspectUtil.getMethod((ProceedingJoinPoint) joinPoint);
//        Object result = ((ProceedingJoinPoint) joinPoint).proceed();
//
//        RemindByCardText annotation = method.getAnnotation(RemindByCardText.class);
//        if (Objects.isNull(annotation)) {
//            return;
//        }
//        String[] recipient = annotation.recipient();
//        if (null == recipient) {
//            return;
//        }
//        for (String rr : recipient) {
//            WechatMsg wechatMsg = new WechatMsg();
//            try {
//                wechatMsg.setTitle(annotation.title());
//                wechatMsg.setContent(annotation.content());
//                wechatMsg.setRecipient(rr);
//                if (StringUtils.hasText(annotation.linke()) && HttpUtil.isHttp(annotation.linke())) {
//                    wechatMsg.setLink(annotation.linke());
//                }
//                msgSender.sendMsg(wechatMsg);
//            } catch (Exception e) {
//                // ignore this
//                log.error("remindByCardText fail.msg:{}", JsonUtil.toJson(wechatMsg), e);
//            }
//        }
//    }
//}
