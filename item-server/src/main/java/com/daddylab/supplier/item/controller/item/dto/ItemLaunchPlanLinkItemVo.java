package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.PropertyName;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/5/31 14:36
 * @Description: 请描述下这个类
 */
@Data
@ApiModel("商品上新计划关联的商品信息")
public class ItemLaunchPlanLinkItemVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品上新计划和商品的关联关系ID")
    private Long planItemRefId;

    /**
     * 商品上新计划ID（item_launch_plan#id）
     */
    @ApiModelProperty(value = "商品上新计划ID")
    private Long planId;

    /**
     * 商品ID（item#id）
     */
    @ApiModelProperty(value = "商品ID")
    private Long itemId;

    /**
     * 商品规格ID
     */
//    @ApiModelProperty(value = "商品规格ID")
//    private Long skuId;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    /**
     * 规格编码
     */
//    @ApiModelProperty(value = "规格编码")
//    private String skuCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    /**
     * 规格名称
     */
//    @ApiModelProperty(value = "规格名称")
//    private String skuName;

    /**
     * 类目
     */
    @ApiModelProperty(value = "类目")
    private String categoryPath;

    /**
     * 采购价格
     */
//    @ApiModelProperty(value = "采购价格")
//    private BigDecimal purchasePrice;

    /**
     * 销售价格
     */
//    @ApiModelProperty(value = "销售价格")
//    private BigDecimal salePrice;

    /**
     * 库存
     */
//    @ApiModelProperty(value = "库存")
//    private Integer stock;

    /**
     * 采购员user_id
     */
    @ApiModelProperty(value = "采购员user_id")
    private Long buyerUserId;

    /**
     * 采购员姓名
     */
    @ApiModelProperty(value = "采购员姓名")
    private String buyerUserName;

    /**
     * 产品负责人user_id
     */
    @ApiModelProperty(value = "产品负责人user_id")
    private Long principalUserId;

    /**
     * 产品负责人姓名
     */
    @ApiModelProperty(value = "产品负责人姓名")
    private String principalUserName;

    /**
     * 产品负责人花名
     */
    @ApiModelProperty(value = "产品负责人花名")
    private String principalUserNickName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "序号")
    private Integer sortNo;

    @ApiModelProperty(value = "首页文案")
    private String homeCopy;

    @ApiModelProperty(value = "上新价")
    @Size(max = 100)
    private String launchPrice;

    @ApiModelProperty(value = "商品类型。1：内部，2：外包")
    private Integer itemType;

    @PropertyName("新品活动开始周期")
    public Long activePeriodStart;

    @DiffIgnore
    @PropertyName("新品活动结束周期")
    private Long activePeriodEnd;

    @PropertyName("共享盘链接")
    private String shareDiskLink;

    private Integer businessLine;

    @PropertyName("是否抽检商品 0:否，1:是")
    private Integer isDadCheck;

    @ApiModelProperty(value = "商品标签", notes = "DAD_SAMPLING_INSPECTION 老爸抽检, CUSTOMIZED_PRODUCTS 定制品")
    List<String> tags;

    @ApiModelProperty("合作方-业务类型")
    List<CorpBizTypeDTO> corpBizType;
}
