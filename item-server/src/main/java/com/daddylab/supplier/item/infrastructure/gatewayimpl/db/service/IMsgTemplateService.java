package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.MsgTemplate;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MsgTemplateCode;

import java.util.Optional;

/**
 * <p>
 * 消息模板 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
public interface IMsgTemplateService extends IDaddyService<MsgTemplate> {

    Optional<MsgTemplate> getByCode(MsgTemplateCode code);
}
