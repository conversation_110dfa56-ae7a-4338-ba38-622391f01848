package com.daddylab.supplier.item.domain.purchase.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @ClassName PurchaseSheetItem.java
 * @description
 * @createTime 2021年11月15日 15:39:00
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ContentRowHeight(16)
@HeadRowHeight(20)
public class PurchaseSheet extends ExportSheet {

    @ColumnWidth(50)
    @ExcelProperty("*月份 如:(2022/05)")
    private String month;

//    @ColumnWidth(50)
//    @ExcelProperty("合作模式")
//    private String businessLine;

    @ColumnWidth(50)
    @ExcelProperty("合作方")
    private String coryType;

    @ColumnWidth(50)
    @ExcelProperty("*商品sku")
    private String itemSku;

    @ColumnWidth(50)
    @ExcelProperty("*商品名称")
    private String itemName;

    @ColumnWidth(50)
    @ExcelProperty("商品规格")
    private String itemSpecs;

    @ColumnWidth(50)
    @ExcelProperty("商品规格数量")
    private Long specsCount;

    @ColumnWidth(50)
    @ExcelProperty("工厂/仓库")
    private String purchaseArea;

    @ColumnWidth(50)
    @ExcelProperty("供应商")
    private String provider;

    @ExcelIgnore
    private Long buyerId;

    @ColumnWidth(50)
    @ExcelProperty("采购负责人")
    private String buyerName;

    @ColumnWidth(50)
    @ExcelProperty(value = "日常供价无优惠")
    private String usualPrice;

    @ColumnWidth(50)
    @ExcelProperty("是否纯活动商品")
    private String isActive;

    @ColumnWidth(50)
    @ExcelProperty("优惠类型")
    private String favourableType;

    @ColumnWidth(50)
    @ExcelProperty("平台名称")
    private String platformType;

    @ColumnWidth(50)
    @ExcelProperty("方式")
    private String activeType;

    @ColumnWidth(50)
    @ExcelProperty("活动开始时间")
    private String startTime;

    @ColumnWidth(50)
    @ExcelProperty("活动结束时间")
    private String endTime;

    @ColumnWidth(50)
    @ExcelProperty("订单拍下份数")
    private Long orderCount;

    @ColumnWidth(50)
    @ExcelProperty("实发单品数量")
    private Long finalCount;

    @ColumnWidth(50)
    @ExcelProperty(value = "按价格优惠结算成本")
    private String priceCost;

    @ColumnWidth(50)
    @ExcelProperty(value = "按数量优惠结算成本")
    private String numCost;

    @ColumnWidth(50)
    @ExcelProperty("供价优惠内容")
    private String content;

    @ColumnWidth(50)
    @ExcelProperty("备注说明")
    private String mark;

    @ColumnWidth(50)
    @ExcelProperty("状态")
    private String status;

}
