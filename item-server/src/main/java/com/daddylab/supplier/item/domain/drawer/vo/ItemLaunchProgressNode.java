package com.daddylab.supplier.item.domain.drawer.vo;

import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.function.Predicate;

/**
 * Class  CheckDetailVO
 *
 * @Date 2022/6/3下午12:53
 * <AUTHOR>
 */
@ApiModel(value = "ItemLaunchProgressNode", description = "商品上新进度节点")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItemLaunchProgressNode {

    @ApiModelProperty("节点名称")
    private String title;

    @ApiModelProperty("经办人")
    private List<DadStaffVO> processors;

    @ApiModelProperty("是否完成")
    private Boolean ok;

    @ApiModelProperty("完成时间")
    private Long completeTime;
    
    public boolean isProcessor(Long userId) {
        final Predicate<List<DadStaffVO>> isProcessor =
                processors ->
                        processors.stream()
                                .anyMatch(
                                        processor ->
                                                processor.getUserId().equals(userId));
        return processors != null && isProcessor.test(processors);
    }


}
