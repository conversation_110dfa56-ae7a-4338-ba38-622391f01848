package com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt;

import cn.hutool.core.util.StrUtil;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.lang.reflect.Proxy;
import java.util.Map.Entry;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
public class APILogProxy implements InvocationHandler {

    protected Object delegate;
    protected WdtConfig.Config config;
    protected static LoadingCache<Method, String> methodIdCache = Caffeine.newBuilder()
            .maximumSize(1000)
            .build(APILogProxy::generateMethodId);
    protected static LoadingCache<Method, Logger> loggers = Caffeine.newBuilder()
            .maximumSize(1000)
            .build(APILogProxy::buildLogger);
    /**
     * 委托类类名，如果是旺店通SDK，固定为 wdtsdk
     */
    private String delegateClassName;

    public APILogProxy(Object delegate, WdtConfig.Config config) {
        this.delegate = delegate;
        this.config = config;
    }

    private String getDelegateClassName() {
        if (this.delegateClassName != null) {
            return this.delegateClassName;
        }
        Class<?> delegateClass = delegate.getClass();
        while (Proxy.class.isAssignableFrom(delegateClass)) {
            delegateClass = delegateClass.getSuperclass();
        }
        if (Object.class.equals(delegateClass)) {
            delegateClass = null;
        }
        this.delegateClassName = Objects.isNull(delegateClass) ? "wdtsdk"
                : simplyReferenceName(delegateClass.getName());
        return delegateClassName;
    }

    @SuppressWarnings("unchecked")
    public static <T> T proxy(Object delegate, Class<T> face, WdtConfig.Config config) {
        return (T) Proxy.newProxyInstance(face.getClassLoader(), new Class<?>[]{face},
                new APILogProxy(delegate, config));
    }

    private String toLogPart(Object obj, int maxLen) {
        final String json = JsonUtil.toJson(obj);
        if (maxLen != 0 && json.length() > maxLen) {
            return json.substring(0, maxLen) + "...";
        }
        return json;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        if (!Modifier.isPublic(method.getModifiers())) {
            return method.invoke(delegate, args);
        }
        final Logger log = getLogger(method);
        String methodId = getMethodId(method);
        boolean hasException = false;
        Object result = null;
        long startTimeMillis = DateUtil.currentTimeMillis();

        try {
            return result = method.invoke(delegate, args);
        } catch (Throwable exception) {
            hasException = true;
            final long timeConsumeMillis = DateUtil.currentTimeMillis() - startTimeMillis;

            Throwable e = exception;
            if (e instanceof InvocationTargetException) {
                e = ((InvocationTargetException) e).getTargetException();
            }

            if (e instanceof WdtErpException) {
                if ("sid 'hzlb3' is not found".equals(e.getMessage())) {
                    e = new WdtErpException(((WdtErpException) e).getCode(), "触发旺店通接口限频，请稍后再试");
                }
                if ("应用服务器异常,请稍后重试".equals(e.getMessage())) {
                    e = new WdtErpException(((WdtErpException) e).getCode(), "旺店通当前服务繁忙，请稍后再试");
                }
                final String baseErr = ((WdtErpException) e).getCode() + " " + e.getMessage();
                if (null == e.getCause()) {
                    log.error("旺店通CALL({}) > ({}|{}) {}ms 业务异常:{} {}({})", getDelegateClassName(),
                            config.getSid(), config.getKey(), timeConsumeMillis, baseErr, methodId,
                            toLogPart(args, 0));
                } else {
                    log.error("旺店通CALL({}) > ({}|{}) {}ms 异常:{} {}({})", getDelegateClassName(),
                            config.getSid(), config.getKey(), timeConsumeMillis, baseErr + " cause by " + ExceptionUtil
                                    .getMessage(e.getCause()), methodId,
                            toLogPart(args, 0));
                }
                throw e;
            }

            log.error("旺店通CALL({}) > ({}|{}) {}ms 未知异常 {}({})", getDelegateClassName(), config.getSid(),
                    config.getKey(), timeConsumeMillis, methodId,
                    toLogPart(args, 0),
                    e);
            throw new WdtErpException("旺店通接口调用出现未知异常:" + ExceptionUtil.getMessage(e),
                    e != null ? e : exception);
        } finally {

            final long timeConsumeMillis = DateUtil.currentTimeMillis() - startTimeMillis;
            if (!hasException) {
                log.info("旺店通CALL({}) > ({}|{}) {}ms {}({}) << {}", getDelegateClassName(),
                        config.getSid(), config.getKey(), timeConsumeMillis, methodId,
                        toLogPart(args, log.isDebugEnabled() ? 0 : 1024),
                        toLogPart(result, log.isDebugEnabled() ? 0 : 4096));
            }
        }
    }

    private Logger getLogger(Method method) {
        return loggers.get(method);
    }

    private static Logger buildLogger(Method method) {
        final String className = method.getDeclaringClass().getName();
        return LoggerFactory.getLogger("WdtAPI." + simplyReferenceName(className));
    }

    private static String simplyReferenceName(String name) {
        for (Entry<String, String> entry : ImmutableMap.<String, String>builder()
                .put("com.daddylab.mall.wdtsdk.", "wdtsdk.")
                .put("com.daddylab.supplier.item.infrastructure.", "")
                .build().entrySet()) {
            name = StrUtil.replace(name, entry.getKey(), entry.getValue());
        }
        return name;
    }

    private static String getMethodId(Method method) {
        return methodIdCache.get(method);
    }

    private static String generateMethodId(Method method) {
        final String className = method.getDeclaringClass().getName();
        String methodId = className + ":" + method.getName();
        methodId = simplyReferenceName(methodId);
        return methodId;
    }
}
