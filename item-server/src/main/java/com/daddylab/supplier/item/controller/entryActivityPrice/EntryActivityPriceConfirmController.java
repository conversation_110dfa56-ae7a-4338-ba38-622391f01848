package com.daddylab.supplier.item.controller.entryActivityPrice;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.entryActivityPrice.EntryActivityPriceService;
import com.daddylab.supplier.item.application.entryActivityPrice.models.*;
import com.daddylab.supplier.item.common.ResponseAssert;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/10/4
 */
@RestController
@RequestMapping("/open/entryActivityPrice/confirmation")
@Api(value = "入驻活动价格确认", tags = {"入驻活动价格", "采购管理", "开放接口", "价格确认"})
public class EntryActivityPriceConfirmController {
    @Resource
    private EntryActivityPriceService entryActivityPriceService;

    @ApiOperation(value = "发送验证码")
    @PostMapping("/sendCode")
    public Response sendCode(@RequestBody SendCodeReq req) {
        return entryActivityPriceService.sendCode(req);
    }

    @PostMapping("/token")
    @ApiOperation(value = "获取令牌")
    public SingleResponse<String> token(@RequestBody @Validated TokenReq req) {
        return entryActivityPriceService.token(req);
    }

    @ApiOperation(value = "确认页信息")
    @PostMapping("/view")
    public SingleResponse<ConfirmationViewVO> confirmationView(@Validated @RequestBody ConfirmationViewReq req) {
        ResponseAssert.assertJust(entryActivityPriceService.checkToken(req.getToken()));
        return entryActivityPriceService.confirmationView(req);
    }

    @ApiOperation(value = "价格确认")
    @PostMapping("/confirm")
    public Response confirm(@Validated @RequestBody ConfirmReq req) {
        ResponseAssert.assertJust(entryActivityPriceService.checkToken(req.getToken()));
        return entryActivityPriceService.confirm(req);
    }

    @ApiOperation(value = "存在异议")
    @PostMapping("/dissent")
    public Response dissent(@Validated @RequestBody DissentReq req) {
        ResponseAssert.assertJust(entryActivityPriceService.checkToken(req.getToken()));
        return entryActivityPriceService.dissent(req);
    }
}
