package com.daddylab.supplier.item.infrastructure.third.redbook.impl;

import com.daddylab.supplier.item.application.platformItemSkuInventory.PlatformServiceFactoryAbstract;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.third.redbook.RedBookService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/4/8
 */
@Service
public class RedBookServiceFactory extends PlatformServiceFactoryAbstract<RedBookService> {
    @Override
    public Platform platform() {
        return Platform.XIAOHONGSHU;
    }

    @Override
    protected RedBookService getService() {
        return new RedBookServiceImpl();
    }
}
