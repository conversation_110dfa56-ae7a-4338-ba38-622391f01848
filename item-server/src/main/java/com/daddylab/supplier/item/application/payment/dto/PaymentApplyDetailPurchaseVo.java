package com.daddylab.supplier.item.application.payment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年12月04日 11:05 AM
 */
@Data
@ApiModel("付款明细封装（采购单来源）")
public class PaymentApplyDetailPurchaseVo extends PaymentApplyDetailVo{

    @ApiModelProperty("付款明细-采购单明细")
    private List<PurchaseListViewVo> purchaseListViewVoList;

}
