package com.daddylab.supplier.item.controller.stock;

import com.alibaba.cola.dto.PageResponse;
import com.daddylab.supplier.item.application.stockStatistic.StockStatisticBizService;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.types.stockStatistic.StockStatisticPageQuery;
import com.daddylab.supplier.item.types.stockStatistic.StockStatisticPageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/3/1
 */
@RestController
@RequestMapping("/stock/statistic")
@Api(value = "库存管理/库存统计", tags = "库存管理/库存统计")
public class StockStatisticController {
    @Resource
    StockStatisticBizService stockStatisticBizService;

    @ApiOperation(value = "列表查询(库存统计)")
    @PostMapping(value = "/query")
    public PageResponse<StockStatisticPageVO> queryPage(@Validated @RequestBody StockStatisticPageQuery query) {
        query.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(query.getBusinessLine()));
        return stockStatisticBizService.query(query);
    }

//    @ApiOperation(value = "列表查询(虚拟仓)")
//    @PostMapping(value = "/query2")
//    public PageResponse<StockStatisticPageVO> queryPage2(@Validated @RequestBody StockStatisticPageQuery query) {
//        query.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(query.getBusinessLine()));
//        return stockStatisticBizService.query2(query);
//    }


}
