package com.daddylab.supplier.item.application.platformItem;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.platformItem.data.*;
import com.daddylab.supplier.item.application.platformItem.query.OtherPlatformItemPageQuery;
import com.daddylab.supplier.item.application.platformItem.query.PlatformItemDropDownPageQuery;
import com.daddylab.supplier.item.application.platformItem.query.PlatformItemPageQuery;
import com.daddylab.supplier.item.controller.platformItem.PlatformItemSyncCmd;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.shop.dto.ShopDropDownItem;
import com.daddylab.supplier.item.domain.shop.dto.ShopDropDownQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryAlloc;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryAllocShop;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import com.daddylab.supplier.item.types.platformItem.SelectPlatformItemQuery;
import com.daddylab.supplier.item.types.platformItem.SelectPlatformItemVO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/27
 */
public interface PlatformItemBizService {
    PageResponse<PlatformItemListItem> pageQuery(PlatformItemPageQuery pageQuery);

    PageResponse<PlatformItemListItem> otherPlatformItemPageQuery(OtherPlatformItemPageQuery pageQuery);

    SingleResponse<PlatformItemDetail> detail(Long id);
    
    List<PlatformItemDetailSku> detailSkuList(Long platformItemId);

    SingleResponse<PlatformItemDetail> detail(Platform platform, String outerItemId);

    SingleResponse<PlatformItemSyncResult> sync(PlatformItemSyncCmd cmd);

    PageResponse<PlatformItemDropDownItem> dropDownList(PlatformItemDropDownPageQuery pageQuery);

    /**
     * 选择平台商品（混合查询，平台为【小程序】将会调用小程序商城提供的接口查询
     *
     * @param query 查询参数
     * @return SelectPlatformItemVO
     */
    PageResponse<SelectPlatformItemVO> hibridSelectPlatformItem(SelectPlatformItemQuery query);

    void inventorySyncSwitch(InventorySyncSwitchCmd cmd);

    void inventoryLockSwitch(InventoryLockSwitchCmd cmd);

    InventoryOccupyDetailVO inventoryOccupyDetail(InventoryOccupyDetailQuery query);
    
    WarehouseInventoryDetailVO warehouseInventoryDetail(WarehouseInventoryDetailQuery query);
    
    void syncInventory(SyncInventoryCmd cmd);
    
    void editSyncInventory(EditSyncInventoryCmd cmd);
    
    void editWarnThreshold(EditWarnThresholdCmd cmd);
    
    MultiResponse<ShopDropDownItem> dropDownShopList(ShopDropDownQuery query);
    
    void sendPlatformItemLowStockAlert(PlatformItem platformItem, PlatformItemSku platformItemSku);
    
    void sendPlatformItemLowStockAlert(List<InventoryAlloc> inventoryAllocs);
    
    void sendPlatformItemLowStockAlert(InventoryAlloc inventoryAlloc);
}
