package com.daddylab.supplier.item.application.stockStatistic;

import com.alibaba.cola.dto.PageResponse;
import com.daddylab.supplier.item.types.stockStatistic.StockStatisticPageQuery;
import com.daddylab.supplier.item.types.stockStatistic.StockStatisticPageVO;

/**
 * <AUTHOR>
 * @since 2024/3/1
 */
public interface StockStatisticBizService {
    PageResponse<StockStatisticPageVO> query(StockStatisticPageQuery query);

//    PageResponse<StockStatisticPageVO> query2(StockStatisticPageQuery query);
}
