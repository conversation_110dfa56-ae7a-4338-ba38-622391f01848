package com.daddylab.supplier.item.domain.platformItem.vo;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemType;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Class  PlatformVO
 *
 * @Date 2022/8/8上午11:50
 * <AUTHOR>
 */
@Data
public class PlatformItemOpenVO implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 平台货品ID
     */
    private String outerItemId;

    /**
     * 平台货品编码
     */
    private String outerItemCode;

    /**
     * 匹配到的我们自己的商品ID
     */
    private Long itemId;

    /**
     * 商品编码
     */
    private String itemCode;

    /**
     * 商品品类ID
     */
    private Long categoryId;

    /**
     * 匹配到的我们自己的店铺ID
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺编号
     */
    private String shopNo;

    /**
     * 平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城
     */
    private Platform platform;

    /**
     * 平台商品类型 1:自营 2:代销
     */
    private PlatformItemType type;

    /**
     * 1:在售 0:已下架
     */
    private PlatformItemStatus status;

    /**
     * sku数量
     */
    private Integer skuNum;

    /**
     * 平台售价
     */
    private BigDecimal price;

    /**
     * 平台库存
     */
    private Integer stock;

    /**
     * 平台货品名称
     */
    private String goodsName;

    /**
     * 平台商品最后修改时间（旺店通返回）
     */
    private LocalDateTime modified;

    /**
     * 合作伙伴系统商品编号
     */
    private String partnerProviderItemSn;

    /**
     * 平台商品规格
     */
    private List<PlatformItemSku> platformItemSkus;
}
