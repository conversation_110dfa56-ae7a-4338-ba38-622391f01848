package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 地区关联表
 * </p>
 *
 */
@Data
@Accessors(chain = true)
@TableName("region_relevant")
public class RegionRelevant {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 地区代码
     */
    @TableField("code")
    private String code;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 上级地区id
     */
    @TableField("p_id")
    private Long pId;

    /**
     * 等级
     */
    @TableField("level")
    private Integer level;

    /**
     * 上级地区代码
     */
    @TableField("p_code")
    private String pCode;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;


}
