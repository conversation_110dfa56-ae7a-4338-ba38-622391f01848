package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/20 4:39 下午
 * @description
 */
@Data
@ApiModel("日常售价list")
public class SalesPriceDto {

    @ApiModelProperty("priceId")
    Long id;

    @NotNull(message = "价格类型必填")
    @ApiModelProperty("价格类型。必填。 价格类型 0:采购成本 1:日常销售价 2:划线价格 3:产品活动价 4:渠道最低价 5:自定义价格")
    Integer type;

    @ApiModelProperty("价格名称")
    @NotBlank(message = "价格名称必填")
    String name;

    @NotBlank(message = "价格值必填")
    @ApiModelProperty("价格值，单位元，2位小数")
    String val;
}
