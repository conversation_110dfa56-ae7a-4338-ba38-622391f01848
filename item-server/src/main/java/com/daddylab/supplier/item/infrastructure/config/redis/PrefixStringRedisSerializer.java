package com.daddylab.supplier.item.infrastructure.config.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * Class  PrefixStringRedisSerializer
 *
 * @Date 2021/8/6下午12:59
 * <AUTHOR>
 */
@Slf4j
public class PrefixStringRedisSerializer implements RedisSerializer<String> {

    private final Charset charset;

    private String keyPrefix = "";

    public PrefixStringRedisSerializer() {
        this (StandardCharsets.UTF_8);
    }

    public PrefixStringRedisSerializer(String keyPrefix) {
        this (StandardCharsets.UTF_8);
        this.keyPrefix = keyPrefix;
    }

    public void setKeyPrefix(String keyPrefix) {
        this.keyPrefix = keyPrefix;
    }

    public PrefixStringRedisSerializer(Charset charset) {
        Assert.notNull ( charset, "Charset must not be null!" );
        this.charset = charset;
    }

    @Override
    public String deserialize(byte[] bytes) {
        return (bytes == null ? null : new String(bytes, charset).replaceFirst(keyPrefix, ""));
    }

    @Override
    public byte[] serialize(@Nullable String string) {
        return (keyPrefix + string).getBytes(charset);
    }
}
