package com.daddylab.supplier.item.application.platformItem.tasks;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.application.message.event.PlatformWarnMessageEvent;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.platformItem.service.PlatformItemSyncService;
import com.daddylab.supplier.item.domain.platformItem.service.SyncResult;
import com.daddylab.supplier.item.domain.platformItem.service.SyncStatus;
import com.daddylab.supplier.item.domain.wdt.dataSync.DataSyncContext;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtPlatformGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtPlatformGoodsService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/05/06
 */
@Component
@Slf4j
public class PlatformItemFetcher implements PageFetcher {

    final private IWdtPlatformGoodsService wdtPlatformGoodsService;
    final private PlatformItemSyncService platformItemSyncService;

    RefreshConfig refreshConfig;

    @Autowired
    public PlatformItemFetcher(
            IWdtPlatformGoodsService wdtPlatformGoodsService,
            PlatformItemSyncService platformItemSyncService) {
        this.wdtPlatformGoodsService = wdtPlatformGoodsService;
        this.platformItemSyncService = platformItemSyncService;
    }

    @Override
    public int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
        final LambdaQueryChainWrapper<WdtPlatformGoods> wdtPlatformGoodsQuery = wdtPlatformGoodsService
                .lambdaQuery();
        wdtPlatformGoodsQuery.ge(WdtPlatformGoods::getUpdatedAt, DateUtil.toTime(startTime));
        wdtPlatformGoodsQuery.le(WdtPlatformGoods::getUpdatedAt, DateUtil.toTime(endTime));
        return wdtPlatformGoodsQuery.count();
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, long pageIndex, long pageSize,
            RunContext runContext) {
        final List<WdtPlatformGoods> wdtPlatformGoods = getWdtPlatformGoods(
                startTime, endTime, (int)pageIndex, (int)pageSize);
        final DataSyncContext dataSyncContext = new DataSyncContext();
        for (WdtPlatformGoods wdtPlatformGood : wdtPlatformGoods) {
            savePlatformItem(dataSyncContext, wdtPlatformGood, isDisablePlatformItemSyncMessage());
        }
        runContext.getRunData().getExtras().set("stat", dataSyncContext);
    }

    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.PLATFORM_ITEM;
    }

    @Autowired
    public void setRefreshConfig(RefreshConfig refreshConfig) {
        this.refreshConfig = refreshConfig;
    }

    private boolean isDisablePlatformItemSyncMessage() {
        final Boolean disablePlatformItemSyncMessage = refreshConfig
                .getDisablePlatformItemSyncMessage();
        return disablePlatformItemSyncMessage != null && disablePlatformItemSyncMessage;
    }

    private List<WdtPlatformGoods> getWdtPlatformGoods(LocalDateTime startTime,
            LocalDateTime endTime, int pageIndex, int pageSize) {
        final LambdaQueryChainWrapper<WdtPlatformGoods> wdtPlatformGoodsQuery = wdtPlatformGoodsService
                .lambdaQuery();
        if (Objects.nonNull(startTime)) {
            wdtPlatformGoodsQuery.ge(WdtPlatformGoods::getUpdatedAt, DateUtil.toTime(startTime));
        }
        if (Objects.nonNull(endTime)) {
            wdtPlatformGoodsQuery.le(WdtPlatformGoods::getUpdatedAt, DateUtil.toTime(endTime));
        }
        final Page<WdtPlatformGoods> page = new Page<>(pageIndex, pageSize);
        wdtPlatformGoodsQuery.page(page);
        return page.getRecords();
    }

    private void savePlatformItem(DataSyncContext context, WdtPlatformGoods wdtPlatformGood,
            Boolean triggerEvent) {
        try {
            final SyncResult syncResult = platformItemSyncService
                    .syncWdtPlatformGoods(wdtPlatformGood);
            final SyncStatus syncStatus = syncResult.getSyncStatus();
            final Optional<PlatformItem> platformItemOpt = Optional
                    .ofNullable(syncResult.getPlatformItem());
            switch (syncStatus) {
                case SUCCESS:
                case IGNORE:
                    context.addSuccessNum();
                    break;
                case ERROR_ITEM_NO_MATCH:
                    log.warn("后端商品未匹配到，data = {}", wdtPlatformGood);
                    context.addWarnNum();
                    if (triggerEvent) {
                        triggerMessage(syncResult.getGoodsId(),
                                platformItemOpt.map(PlatformItem::getId).orElse(0L));
                    }
                    break;
                case ERROR_NO_GOODS_ID:
                    log.warn("没有外部平台商品ID，忽略，data = {}", wdtPlatformGood);
                    context.addSkipNum();
                    break;
                case ERROR_NO_SPEC_ID:
                    log.warn("没有外部平台商品SKU ID，忽略，data = {}", wdtPlatformGood);
                    context.getSkipNum();
                    break;
                default:
                    log.warn("未定义的结果，data = {}", wdtPlatformGood);
                    context.addWarnNum();
            }
        } catch (Exception ex) {
            log.error("同步平台商品异常，data = {}", wdtPlatformGood, ex);
            context.addErrorNum();
        }
    }

    private void triggerMessage(String goodsId, Long platformItemId) {
        try {
            PlatformWarnMessageEvent event = PlatformWarnMessageEvent.build(
                    String.valueOf(platformItemId), goodsId,
                    PlatformItemWarnType.SKU_NOT_MATCH.getDesc());
            EventBusUtil.post(event);
        } catch (Exception e) {
            log.error("触发平台商品警告消息事件时遇到异常 goodsId={}", platformItemId, e);
        }
    }


}
