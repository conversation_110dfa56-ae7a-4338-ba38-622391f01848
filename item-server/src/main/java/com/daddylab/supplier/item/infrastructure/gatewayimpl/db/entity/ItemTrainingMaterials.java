package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.daddylab.supplier.item.types.itemTrainingMaterials.ItemTrainingMaterialsData;
import com.daddylab.supplier.item.types.itemTrainingMaterials.ItemTrainingMaterialsStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 新品商品培训资料
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemTrainingMaterials implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 版本号（做乐观锁用）
     */
    private Integer v;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除：0否1是
     */
    @TableLogic
    private Integer isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 商品ID
     */
    private Long itemId;

    /**
     * 培训资料模型数据
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ItemTrainingMaterialsData data;

    /**
     * 处理流程ID（item_training_materials_process.id）
     */
    private Long processId;

    /**
     * 状态 0 待提交 1 已提交 2 待法务审核 3 待QC审核 4 待修改 5 已完成
     */
    private ItemTrainingMaterialsStatus status;

    /**
     * 老爸WIKI内容ID
     */
    private Long wikiContentId;


}
