package com.daddylab.supplier.item.controller.item.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: <PERSON>
 * @Date: 2022/9/20 14:53
 * @Description: sku 价格 dto
 */
@Data
public class SkuPriceDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long skuId;
    private String skuCode;
    private BigDecimal linePrice;
    private BigDecimal dailyPrice;
}
