//package com.daddylab.supplier.item.application.item.itemBatchProc.batch;
//
//import com.daddylab.supplier.item.controller.item.dto.ItemPageQuery;
//import com.daddylab.supplier.item.controller.item.dto.ItemPageVo;
//import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
//import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
//import com.daddylab.supplier.item.infrastructure.common.UserContext;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemMapper;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
//import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
//import com.daddylab.supplier.item.types.itemBatchProc.ModifyProviderCmd;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.batch.core.Job;
//import org.springframework.batch.core.StepExecution;
//import org.springframework.batch.core.annotation.BeforeStep;
//import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
//import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
//import org.springframework.batch.core.step.tasklet.TaskletStep;
//import org.springframework.batch.item.ItemReader;
//import org.springframework.batch.item.ItemWriter;
//import org.springframework.batch.item.support.ListItemReader;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.context.annotation.Bean;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR> up
// * @date 2023年12月27日 9:54 AM
// */
//@Slf4j
//@Component
//public class ProviderBatchBizService {
//
//
//    @Autowired
//    IItemService iItemService;
//
//    @Autowired
//    OperateLogGateway operateLogGateway;
//
//    @Resource
//    private JobBuilderFactory jobBuilderFactory;
//    @Resource
//    private StepBuilderFactory stepBuilderFactory;
//
//
//    @Component("providerReader")
//    public class CustomItemReader implements ItemReader<Long> {
//        private ItemReader<Long> delegate;
//
//        @Autowired
//        ItemMapper itemMapper;
//
//        @BeforeStep
//        public void beforeStep(StepExecution stepExecution) {
//            String param = stepExecution.getJobParameters().getString("param");
//            ModifyProviderCmd cmd = JsonUtil.parse(param, ModifyProviderCmd.class);
//            assert cmd != null;
//            ItemPageQuery query = cmd.getQuery();
//            List<ItemPageVo> itemPageVos = itemMapper.queryPage(query);
//            delegate = new ListItemReader<>(itemPageVos.stream().map(ItemPageVo::getId).collect(Collectors.toList()));
//        }
//
//        @Override
//        public Long read() throws Exception {
//            return delegate.read();
//        }
//    }
//
//    @Component("providerWriter")
//    public class CustomItemWriter implements ItemWriter<Long> {
//        private Long providerId;
//
//        @BeforeStep
//        public void beforeStep(StepExecution stepExecution) {
//            String param = stepExecution.getJobParameters().getString("param");
//            ModifyProviderCmd cmd = JsonUtil.parse(param, ModifyProviderCmd.class);
//            assert cmd != null;
//            providerId = cmd.getProviderId();
//        }
//
//        @Override
//        public void write(List<? extends Long> items) throws Exception {
//            Long userId = UserContext.getUserId();
//            for (Long itemId : items) {
//                boolean update = iItemService.lambdaUpdate()
//                        .set(Item::getProviderId, providerId).eq(Item::getId, itemId)
//                        .update();
//                if (update) {
//                    operateLogGateway.addOperatorLog(userId, OperateLogTarget.ITEM, itemId, "批量修改供应商为", null);
//                }
//            }
//        }
//    }
//
//    @Bean("providerJob0")
//    public Job sampleJob(@Qualifier("providerReader") ItemReader<Long> itemReader,
//                         @Qualifier("providerWriter") ItemWriter<Long> itemWriter) {
//        TaskletStep providerStep0 = stepBuilderFactory.get("providerStep0")
//                .<Long, Long>chunk(1)
//                .reader(itemReader)
//                .writer(itemWriter)
//                .build();
//        return jobBuilderFactory.get("providerJob0")
//                .start(providerStep0)
//                .build();
//    }
//
//
//}
