package com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.github.yulichang.injector.MPJSqlInjector;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/25 2:16 下午
 * @description
 */
public class DaddylabSqlInjector extends MPJSqlInjector {

    public DaddylabSqlInjector() {
    }

    @Override
    public List<AbstractMethod> getMethodList(Class<?> mapperClass) {
        List<AbstractMethod> methodList = super.getMethodList(mapperClass);
        methodList.add(new DeleteWithTime());
        methodList.add(new DeleteByIdWithTime());
        methodList.add(new DeleteByMapWithTime());
        methodList.add(new DeleteBatchIdsWithTime());
        return methodList;
    }
}
