package com.daddylab.supplier.item.application.otherStockIn;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.google.common.collect.ImmutableMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/4/24
 */
@AllArgsConstructor
@Getter
public enum OtherStockInState implements IEnum<Integer> {
    UNMAPPING(-1, "未映射"),
    ALL(0, "全部"),
    WAIT_PROCESS(1, "待处理"),
    CANCELED(2, "已取消"),
    EDITING(3, "编辑中"),
    WAIT_AUDIT(4, "待审核"),
    WAIT_QC(5, "待质检"),
    QC_WAIT_VERIFY(6, "质检待确认"),
    FINISHED(7, "已完成"),
    ;
    /**
     * 旺店通状态 10=已取消，20=编辑中，30=待审核/待处理，37=待质检 ，40=质检待确认，80=已完成
     * 系统状态 0:全部 1:待处理 2:已取消 3:编辑中 4:待审核 5:待质检 6:质检待确认 7:已完成
     */
    public static final Map<Integer, OtherStockInState> MAP = ImmutableMap
            .<Integer, OtherStockInState>builder()
            .put(10, OtherStockInState.CANCELED)
            .put(20, OtherStockInState.EDITING)
            .put(30, OtherStockInState.WAIT_AUDIT)
            .put(37, OtherStockInState.WAIT_QC)
            .put(40, OtherStockInState.QC_WAIT_VERIFY)
            .put(80, OtherStockInState.FINISHED)
            .build();
    private final Integer value;
    private final String desc;
}
