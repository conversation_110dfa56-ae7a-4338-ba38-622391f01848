package com.daddylab.supplier.item.controller.drawer;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.drawer.ItemDrawerMarkImageService;
import com.daddylab.supplier.item.domain.drawer.form.ItemDrawerMarkImageForm;
import com.daddylab.supplier.item.domain.drawer.vo.ItemDrawerMarkImageVO;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.common.UserContext;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/4/1
 */
@Api(value = "商品抽屉标记管理", tags = "商品抽屉标记管理")
@Slf4j
@RestController
@RequestMapping("/item-drawer-mark")
@Validated
public class ItemDrawerMarkController {

    @Autowired
    private ItemDrawerMarkImageService itemDrawerMarkImageService;

    @ApiOperation("获取标记详情")
    @GetMapping("/detail")
    public SingleResponse<ItemDrawerMarkImageVO> detail(
            @ApiParam(value = "抽屉ID")
            @RequestParam("drawerId") Long drawerId) {
        final SingleResponse<ItemDrawerMarkImageVO> detail = itemDrawerMarkImageService.detail(
                drawerId);
        log.info("获取标记详情 抽屉ID:{} 响应:{}", drawerId, detail);
        return detail;
    }

    @Auth(resource = "/item-drawer-mark/add")
    @ApiOperation("添加图片标记")
    @PostMapping("/add")
    public SingleResponse<Long> add(@RequestBody ItemDrawerMarkImageForm itemDrawerMarkImageForm) {
        final SingleResponse<Long> response = itemDrawerMarkImageService.add(itemDrawerMarkImageForm,
                UserContext.getUserId());
        log.info("添加图片标记 参数:{} 响应:{}", itemDrawerMarkImageForm, response);
        return response;
    }

//
//    @ApiOperation("编辑图片标记")
//    @PostMapping("/edit")
//    public SingleResponse<Boolean> edit(@RequestBody ItemDrawerMarkImageForm itemDrawerMarkImageForm) {
//
//        final SingleResponse<Boolean> response = itemDrawerMarkImageService.edit(
//                itemDrawerMarkImageForm, UserContext.getUserId());
//        log.info("编辑图片标记 参数:{} 响应:{}", itemDrawerMarkImageForm, response);
//        return response;
//    }
//
//    @ApiOperation("清空图片标记")
//    @PostMapping("/clean")
//    public SingleResponse<Boolean> clean(@RequestBody ItemDrawerMarkImageCleanForm form) {
//        return SingleResponse.of(
//                itemDrawerMarkImageService.clean(form.getItemId(), form.getCleanAll(),
//                        UserContext.getUserId()));
//    }


}
