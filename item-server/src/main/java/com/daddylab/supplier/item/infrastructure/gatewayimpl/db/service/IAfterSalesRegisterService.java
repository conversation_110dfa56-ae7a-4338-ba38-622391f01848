package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesRegister;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 客服售后登记表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-21
 */
public interface IAfterSalesRegisterService extends IDaddyService<AfterSalesRegister> {

}
