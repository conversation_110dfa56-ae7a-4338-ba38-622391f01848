package com.daddylab.supplier.item.controller.third;

import cn.hutool.core.map.MapUtil;
import com.daddylab.supplier.item.application.third.ThirdAccessTokenBizService;
import com.daddylab.supplier.item.application.third.ThirdCallbackService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.javers.common.collections.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @class ThirdCallbackController.java
 * @description 第三方回调类
 * @date 2024-02-29 14:38
 */
@RestController
@RequestMapping(path = "/third/message/callback")
@Slf4j
public class ThirdCallbackController {

    @Autowired
    private ThirdCallbackService thirdCallbackService;

    @ApiOperation(value = "快手消息回调")
    @PostMapping("/ks")
    public Map<String, Object> ks(@RequestBody String privateMessage) {
        return thirdCallbackService.ksMessageCallback(privateMessage);
    }

    @ApiOperation(value = "小红书消息回调")
    @PostMapping("/redbook")
    public Map<String, Object> redBook(@RequestBody String message) {
        return thirdCallbackService.redBookMessageCallback(message);
    }
}