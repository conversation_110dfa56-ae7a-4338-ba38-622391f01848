package com.daddylab.supplier.item.infrastructure.config;

import com.daddylab.supplier.item.common.OtherStockInOutConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年11月06日 11:06 AM
 */
@EnableConfigurationProperties(OtherStockInOutConfig.class)
@ConfigurationProperties(prefix = "lvsejiazhuang-buyer")
@Component
@RefreshScope
@Data
public class LvSeJiaZhuangBuyerConfig {

    /**
     * 订单员昵称
     */
    private List<String> buyerUserId;

}




