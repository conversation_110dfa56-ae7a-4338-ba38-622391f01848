package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum PayableApplyOrderStatus implements IEnum<Integer> {

    /**
     *
     */
    WAIT_AUDIT(1, "待审核"),

    IN_AUDIT(2, "审核中"),

    HAD_AUDIT(3, "审核完成，处理中"),

    HAD_REJECT(4, "审核拒绝"),

    FINISH(5, "处理完成"),

    ERROR(-1, "处理异常");


    @EnumValue
    private final Integer value;
    private final String desc;
}
