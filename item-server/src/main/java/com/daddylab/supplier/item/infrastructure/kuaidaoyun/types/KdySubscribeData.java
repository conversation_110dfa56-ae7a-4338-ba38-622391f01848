package com.daddylab.supplier.item.infrastructure.kuaidaoyun.types;

import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/5/21
 */
@Data
@AllArgsConstructor(staticName = "of")
@NoArgsConstructor
public class KdySubscribeData {
    @NotBlank
    private String company;
    @NotBlank
    private String deliveryNo;
    private String checkMobile;
    private String state;

    @Override
    @JsonValue
    public String toString() {
        final String finalDeliveryNo = Stream.of(deliveryNo, checkMobile)
                                             .filter(StringUtil::isNotBlank)
                                             .collect(Collectors.joining("-"));
        return Stream.of(company, finalDeliveryNo, state).filter(Objects::nonNull).collect(Collectors.joining(","));
    }
}
