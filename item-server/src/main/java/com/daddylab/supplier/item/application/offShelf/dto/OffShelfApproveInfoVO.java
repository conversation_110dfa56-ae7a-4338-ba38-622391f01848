package com.daddylab.supplier.item.application.offShelf.dto;

import com.daddylab.ark.sailor.seller.domain.vo.ShopInfoVO;
import com.daddylab.supplier.item.types.process.ProcessAdvice;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/9
 */
@Data
public class OffShelfApproveInfoVO {
  private ProcessAdvice advice;
  private String approveRemark;
  private Boolean selectAllShop;
  private List<ShopInfoVO> shops;
  private List<OperatorInfoVO> operators;

  @Data
  public static class OperatorInfoVO {
    private Long operatorUid;
    private String operatorNick;
  }

  @Data
  public static class ShopInfoVO {
    private Long shopId;
    private String shopName;
    private String operatorNick;
    private Long operatorUid;
  }
}
