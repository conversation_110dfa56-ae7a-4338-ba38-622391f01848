package com.daddylab.supplier.item.infrastructure.flowable;

import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.delegate.event.FlowableEventListener;
import org.flowable.common.engine.impl.util.DefaultClockImpl;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.flowable.spring.boot.EngineConfigurationConfigurer;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.time.ZoneId;
import java.util.List;
import java.util.TimeZone;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/10/8
 */
@Configuration
@Slf4j
public class ProcessEngineConfigurationCustomer
        implements EngineConfigurationConfigurer<SpringProcessEngineConfiguration> {

    @Autowired
    private ObjectProvider<FlowableEventListener> eventListenersProvider;

    @Override
    public void configure(SpringProcessEngineConfiguration engineConfiguration) {
        engineConfiguration.setEnableDatabaseEventLogging(true);
        engineConfiguration.setEnableHistoricTaskLogging(true);
        final List<FlowableEventListener> eventListeners =
                eventListenersProvider.stream().collect(Collectors.toList());
        log.info("ProcessEngineConfigurationCustomer, event listeners: {}",
                 eventListeners.stream().map(v -> v.getClass().getName()).collect(
                         Collectors.joining(",")));
        engineConfiguration.setEventListeners(eventListeners);
        engineConfiguration.setClock(new DefaultClockImpl(TimeZone.getTimeZone(ZoneId.systemDefault())));
    }
}
