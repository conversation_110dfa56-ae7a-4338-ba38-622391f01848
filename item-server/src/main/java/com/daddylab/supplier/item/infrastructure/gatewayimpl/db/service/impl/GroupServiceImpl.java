package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ErpGroup;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.GroupMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IGroupService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 采购组表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@Service
public class GroupServiceImpl extends DaddyServiceImpl<GroupMapper, ErpGroup> implements IGroupService {

}
