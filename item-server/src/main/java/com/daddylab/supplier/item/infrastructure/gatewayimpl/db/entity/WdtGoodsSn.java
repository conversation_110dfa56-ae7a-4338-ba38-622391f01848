package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * ERP序列号管理页面的数据
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtGoodsSn implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 明细id(主键)
     */
    private Integer recId;

    /**
     * SN编号
     */
    private String sn;

    /**
     * 商家编码
     */
    private String specNo;

    /**
     * 出库单号 order_type=0时返回
     */
    private String stockoutNo;

    /**
     * 入库单号 order_type=1时返回
     */
    private String stockinNo;

    /**
     * 源单号
     */
    private String srcOrderNo;

    /**
     * 源单据类型 1：销售出库 2：调拨出库/调拨入库 5：生产出库/生产原料入库 6：生产入库 10：采购入库 12：退货入库 14：采购退货出库 20：其他入库 21：其他出库
     */
    private Integer srcOrderType;

    /**
     * 创建时间 格式: yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime created;


}
