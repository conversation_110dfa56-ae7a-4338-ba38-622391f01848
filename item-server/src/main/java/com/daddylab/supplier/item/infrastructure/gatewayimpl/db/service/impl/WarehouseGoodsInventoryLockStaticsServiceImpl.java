/*
package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.virtualWarehouse.dto.LockSkuRatioDto;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WarehouseGoodsInventoryLockStatics;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WarehouseGoodsInventoryLockStaticsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWarehouseGoodsInventoryLockStaticsService;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

*/
/**
 * <p>
 * 库存锁定数量统计 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 *//*

@Service
public class WarehouseGoodsInventoryLockStaticsServiceImpl extends DaddyServiceImpl<WarehouseGoodsInventoryLockStaticsMapper, WarehouseGoodsInventoryLockStatics> implements IWarehouseGoodsInventoryLockStaticsService {

    */
/*@Override
    @DistributedLock
    public Integer handler(String warehouse, String skuCode, Integer addLockNum, Integer addLockRatio) {
        final Optional<WarehouseGoodsInventoryLockStatics> staticsOptional = this.lambdaQuery()
                .eq(WarehouseGoodsInventoryLockStatics::getWarehouseNo, warehouse)
                .eq(WarehouseGoodsInventoryLockStatics::getSkuNo, skuCode)
                .oneOpt();
        if (!staticsOptional.isPresent()) {
            addLockNum = addLockNum < 0 ? 0 : addLockNum;

            addLockRatio = addLockRatio < 0 ? 0 : addLockRatio;

            Assert.state(addLockRatio <= 100, "此仓库下此SKU独占占比不能超过100%。no:" + warehouse + ",skuNo:" + skuCode);

            WarehouseGoodsInventoryLockStatics warehouseGoodsInventoryLockStatics = new WarehouseGoodsInventoryLockStatics();
            warehouseGoodsInventoryLockStatics.setWarehouseNo(warehouse);
            warehouseGoodsInventoryLockStatics.setSkuNo(skuCode);
            warehouseGoodsInventoryLockStatics.setLockNum(addLockNum);
            warehouseGoodsInventoryLockStatics.setLockRatio(addLockRatio);
            warehouseGoodsInventoryLockStatics.setVersion(0);
            this.save(warehouseGoodsInventoryLockStatics);

            return warehouseGoodsInventoryLockStatics.getLockRatio();
        }

        final WarehouseGoodsInventoryLockStatics statics = staticsOptional.get();
        int newLockNum = statics.getLockNum() + addLockNum;
        newLockNum = Math.max(newLockNum, 0);
        statics.setLockNum(newLockNum);
        int newLockRatio = statics.getLockRatio() + addLockRatio;
        if (newLockRatio > 100) {
            String error = StrUtil.format("此仓库{}下此SKU{}已经独占占比{}%,剩余可用独占占比为:{}%",
                    warehouse, skuCode, statics.getLockRatio(), 100 - statics.getLockRatio());
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, error);
        }
        newLockRatio = Math.max(newLockRatio, 0);
        statics.setLockRatio(newLockRatio);
        this.updateById(statics);

        return statics.getLockRatio();
    }*//*



    @Override
    @DistributedLock
    @Transactional(rollbackFor = Exception.class)
    public void handlerBatch(List<LockSkuRatioDto> lockSkuRatioDtoList) {
        if (CollUtil.isEmpty(lockSkuRatioDtoList)) {
            return;
        }

        final Map<String, List<LockSkuRatioDto>> collect = lockSkuRatioDtoList.stream().collect(Collectors.groupingBy(LockSkuRatioDto::getWarehouseNo));
        collect.forEach((warehouseNo, reqList) -> {
            final List<String> skuCodes = reqList.stream().map(LockSkuRatioDto::getSkuCode).collect(Collectors.toList());
            final Map<String, WarehouseGoodsInventoryLockStatics> lockedSkuMap = this.lambdaQuery()
                    .eq(WarehouseGoodsInventoryLockStatics::getWarehouseNo, warehouseNo)
                    .in(WarehouseGoodsInventoryLockStatics::getSkuNo, skuCodes)
                    .list().stream()
                    .collect(Collectors.toMap(WarehouseGoodsInventoryLockStatics::getSkuNo, Function.identity()));

            List<WarehouseGoodsInventoryLockStatics> updateList = new LinkedList<>();
            List<WarehouseGoodsInventoryLockStatics> addList = new LinkedList<>();
            reqList.forEach(reqDto -> {
                final WarehouseGoodsInventoryLockStatics lockStatics = lockedSkuMap.get(reqDto.getSkuCode());

                int addRatio = reqDto.getNewRatio() - reqDto.getOldRatio();
                int addNum = reqDto.getNewNum() - reqDto.getOldNum();

                if (Objects.nonNull(lockStatics)) {
                    if (addRatio != 0) {
                        if ((lockStatics.getLockRatio() + addRatio) > 100) {
                            String error = StrUtil.format("此仓库{}下此SKU{}已经独占占比{}%,剩余可用独占占比为:{}%",
                                    warehouseNo, reqDto.getSkuCode(), lockStatics.getLockRatio(), 100 - lockStatics.getLockRatio());
                            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, error);
                        }
                        lockStatics.setLockRatio(Math.max(lockStatics.getLockRatio() + addRatio, 0));
                        lockStatics.setLockNum(Math.max(lockStatics.getLockNum() + addNum, 0));
                        updateList.add(lockStatics);
                    }
                } else {
                    if (addRatio > 100) {
                        String error = StrUtil.format("此仓库{}下此SKU{}已经独占不能超过100%，请求占比:{}%",
                                warehouseNo, reqDto.getSkuCode(), addRatio);
                        throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, error);
                    }
                    WarehouseGoodsInventoryLockStatics addOne = new WarehouseGoodsInventoryLockStatics();
                    addOne.setWarehouseNo(warehouseNo);
                    addOne.setSkuNo(reqDto.getSkuCode());
                    addOne.setLockRatio(addRatio);
                    addOne.setLockNum(addNum);
                    addOne.setVersion(0);
                    addList.add(addOne);
                }
            });
            if (CollUtil.isNotEmpty(updateList)) {
                this.updateBatchById(updateList);
            }
            if (CollUtil.isNotEmpty(addList)) {
                this.saveBatch(addList);
            }
        });
    }

    @Override
    public Map<String, Integer> occpuyRatio(String warehouse) {
        final List<WarehouseGoodsInventoryLockStatics> list = this.lambdaQuery()
                .eq(WarehouseGoodsInventoryLockStatics::getWarehouseNo, warehouse)
//                .in(WarehouseGoodsInventoryLockStatics::getSkuNo, skuCodeList)
                .list();
        return list.stream()
                .collect(Collectors.toMap(WarehouseGoodsInventoryLockStatics::getSkuNo, WarehouseGoodsInventoryLockStatics::getLockRatio, (a, b) -> a));
    }
}
*/
