package com.daddylab.supplier.item.application.brand;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.common.enums.EnableStatusEnum;
import com.daddylab.supplier.item.domain.brand.dto.*;
import com.daddylab.supplier.item.domain.brand.entity.BrandEntity;
import feign.form.multipart.Output;

import java.io.InputStream;
import java.io.OutputStream;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/9 4:37 下午
 * @description
 */
public interface BrandBizService {
    /**
     * @return 返回Excel模板链接
     */
    String getExcelTemplateUrl();

    /**
     * 品牌下拉列表搜索
     */
    MultiResponse<BrandDropDownItem> dropDownList(BrandDropDownQuery query);

    /**
     * 创建或更新品牌
     */
    SingleResponse<BrandDetail> createOrUpdateBrand(CreateOrUpdateBrandCmd cmd);

    /**
     * 查询平台列表
     */
    MultiResponse<BrandListItem> queryBrandList(BrandQuery brandQuery);

    /**
     * 分页查询平台列表
     */
    PageResponse<BrandListItem> pageQueryBrandList(BrandQuery brandQuery);

    /**
     * 获取品牌模型
     */
    SingleResponse<BrandEntity> getBrand(Long id);

    /**
     * 获取品牌详情
     */
    SingleResponse<BrandDetail> getBrandDetail(Long id);

    /**
     * 更新品牌状态
     */
    Response updateBrandStatus(Long id, EnableStatusEnum status);

    /**
     * 删除品牌
     */
    Response deleteBrand(Long id);

    /**
     * 生成品牌编号
     */
    String generateBrandSn(String name);

    /**
     * 品牌是否可以被删除
     */
    boolean canDelete(Long id);

    /**
     * 导入
     */
    boolean importExcel(InputStream inputStream);

    /**
     * 导入旺店通导出的csv文件
     */
    boolean importWdtCsv(InputStream inputStream);

    /**
     * 导出
     */
    void exportExcel(BrandQuery brandQuery, OutputStream outputStream);

    long exportExcel(BrandQuery brandQuery);


}
