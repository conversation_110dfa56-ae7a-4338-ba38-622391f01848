package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.FinanceInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.FinanceInfoMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IFinanceInfoService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 财务信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-20
 */
@Service
public class FinanceInfoServiceImpl extends DaddyServiceImpl<FinanceInfoMapper, FinanceInfo> implements IFinanceInfoService {

    @Override
    public FinanceInfo getInfoByProviderId(Long providerId) {
        LambdaQueryWrapper<FinanceInfo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(FinanceInfo::getProviderId, providerId);
        return this.getOne(queryWrapper);
    }

    @Override
    public void removeByProviderId(Long providerId) {
        LambdaQueryWrapper<FinanceInfo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(FinanceInfo::getProviderId, providerId);
        this.remove(queryWrapper);
    }
}
