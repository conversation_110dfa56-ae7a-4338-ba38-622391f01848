package com.daddylab.supplier.item.domain.shop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> up
 * @date 2024年03月08日 1:50 PM
 */
@Data
@ApiModel("店铺库存配置-实体仓库库存信息")
public class ShopWarehouseInventoryDto {

    @ApiModelProperty("店铺纬度库存设置明细Id")
    private Long id;

    @ApiModelProperty("仓库编码")
    @NotEmpty
    private String warehouseNo;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("库存占比")
    @NotNull(message = "库存占比不得为空")
    private Integer inventoryRatio;

//    @ApiModelProperty("是否是虚拟仓。0不是。1是")
//    @NotNull(message = "仓库类别标识不得为空")
//    private Integer isVirtualWarehouse;

//    @ApiModelProperty("仓库数据版本号")
//    @NotEmpty(message = "仓库数据版本号不得为空")
//    private Integer warehouseVersion;
//
//    public Integer getWarehouseVersion() {
//        return Objects.isNull(warehouseVersion) ? 1 : warehouseVersion;
//    }



}
