package com.daddylab.supplier.item.application.aws.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.jwt.JWTPayload;
import cn.hutool.jwt.JWTUtil;
import com.actionsoft.bpms.api.ApiException;
import com.actionsoft.bpms.api.common.ApiResponse;
import com.actionsoft.sdk.service.model.*;
import com.actionsoft.sdk.service.response.BoolResponse;
import com.actionsoft.sdk.service.response.MapResponse;
import com.actionsoft.sdk.service.response.StringResponse;
import com.actionsoft.sdk.service.response.process.ProcessInstResponse;
import com.actionsoft.sdk.service.response.process.TaskCommentModelsGetResponse;
import com.actionsoft.sdk.service.response.task.HisTaskInstsGetResponse;
import com.actionsoft.sdk.service.response.task.TaskInstGetResponse;
import com.actionsoft.sdk.service.response.task.TaskInstsGetResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.application.aws.AwsConstant;
import com.daddylab.supplier.item.application.aws.AwsOpenApiClient;
import com.daddylab.supplier.item.application.aws.enums.AwsActionEnum;
import com.daddylab.supplier.item.application.aws.enums.AwsControlStateEnum;
import com.daddylab.supplier.item.application.aws.event.AwsChangeEvent;
import com.daddylab.supplier.item.application.aws.service.AwsApprovalFlowService;
import com.daddylab.supplier.item.application.aws.service.AwsBusinessFinishPredicate;
import com.daddylab.supplier.item.application.aws.service.AwsConfig;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerChangeAuthEnum;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AwsApprovalNode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AwsBusinessLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockOutOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAwsApprovalNodeService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAwsBusinessLogService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseOrderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockOutOrderService;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AwsApprovalFlowServiceImpl implements AwsApprovalFlowService {

    private static final String PROCESS_ID_KEY = "processInstId:";
    private static final String PROCESS_ID_REFRESH_KEY = "processInstId:refresh:";
    @Value("${aws.url}")
    private String url;
    @Value("${aws.environment}")
    private String environment;
    @Autowired
    private AwsOpenApiClient client;
    @Autowired
    private IAwsBusinessLogService awsBusinessLogService;
    /**
     * 流程实例ID缓存
     */
    private final LoadingCache<ProcessInstIdCacheKey, String> processInstIdsCache = Caffeine
            .newBuilder()
            .maximumSize(1000)
            .expireAfterAccess(Duration.ofSeconds(60))
            .build(key -> awsBusinessLogService
                    .getProcessIdByBusinessId(key.getTypeEnum(), key.getBusinessId()));
    @Autowired
    private IAwsApprovalNodeService awsApprovalNodeService;
    @Autowired
    private AwsConfig awsConfig;

    @Resource
    IPurchaseOrderService iPurchaseOrderService;

    @Resource
    IStockOutOrderService iStockOutOrderService;


    @Override
    public String createAndStart(Long businessId, PurchaseTypeEnum type, String title,
                                 List<String> list) throws ApiException {
        Assert.notNull(businessId, "businessId入参不得为空");
        Assert.notNull(type, "type入参不得为空");
        Assert.notNull(title, "title入参不得为空");
        Assert.notEmpty(list, "list入参不得为空");
        log.info("businessId, type,  title， list 入参不得为空: {} {}  {} {}", businessId, type, title,
                list);

        String processDefId = getProcessDefId(type);

        //创建
        String processInstId = processCreate(processDefId, title);

        //绑定数据
        batchBoCreate(processInstId, list, environment);

        //启动
        processStart(processInstId);

        //插入aws日志表
        createAwsBusinessLog(businessId, processInstId, type.getValue());

        submit(processInstId);
        return processInstId;
    }

    @Override
    public String createAndStartWithData(Long businessId, PurchaseTypeEnum type, String title,
                                         Map<String, Map<String, Object>> data) throws ApiException {
        String processDefId = getProcessDefId(type);

        //创建
        String processInstId = processCreate(processDefId, title);

        //绑定数据
        data.forEach((table, datum) -> {
            final HashMap<String, Object> boCreateMap = new HashMap<>();
            boCreateMap.put("boName", table);

            datum.put("ENV", environment);
            boCreateMap.put("recordData", datum);

            boCreateMap.put("bindId", processInstId);
            boCreateMap.put("uid", getLoginName());

            StringResponse response = client.getOpenApiClient()
                    .exec(AwsConstant.BO_CREATE, boCreateMap, StringResponse.class);
            if (!response.isSuccess()) {
                throw new ApiException("AWS BO 数据绑定异常");
            }
        });

        //启动
        processStart(processInstId);

        //插入aws日志表
        createAwsBusinessLog(businessId, processInstId, type.getValue());

        submit(processInstId);
        return processInstId;
    }

    @Autowired
    AwsBusinessFinishPredicate businessFinishPredicate;

    @Override
    public String getUrl(Long businessId, PurchaseTypeEnum type) {
        if (businessFinishPredicate.isHidden(businessId, type)) {
            return "";
        }
        final Boolean finished = businessFinishPredicate.isFinished(businessId, type);
        return getUrl(businessId, type, null, finished);
    }

    /**
     * 商品上新流程（任务选择器）
     *
     * @param taskInstance 任务实例
     * @return 是否匹配
     */
    private boolean itemAuditTaskCheck(TaskInstance taskInstance) {
        //如果当前节点是QC审核，且当前用户拥有QC负责人权限
        if (StringUtil.containsIgnoreCase(taskInstance.getTitle(), "QC") && UserContext
                .hasPermission(
                        ItemDrawerChangeAuthEnum.QC.getValue())) {
            return true;
        }
        //如果当前节点是QC审核，且当前用户拥有法务权限（有标注编辑权限即认为是法务）
        if (StringUtil.containsIgnoreCase(taskInstance.getTitle(), "法务") && UserContext
                .hasPermission(
                        ItemDrawerChangeAuthEnum.IMAGE_MAR.getValue())) {
            return true;
        }
        return false;
    }

    @Override
    public String getUrl(Long businessId, PurchaseTypeEnum type,
                         Predicate<TaskInstance> taskChecker, Boolean isFinished) {
        Assert.notNull(businessId, "businessId入参不得为空");
        String loginName = getLoginName();
//        if(PurchaseTypeEnum.PURCHASE_ORDER.equals(type) || PurchaseTypeEnum.OUT_STOCK_PAYABLE.equals(type)){
//            loginName = "ban.dai";
//        }
        final String jwtLoginName = jwtLoginName(loginName);
        Assert.hasText(jwtLoginName, "登录名信息不得为空");

        String processInstId = getProcessInstId(businessId, type);
        if (StringUtils.isBlank(processInstId)) {
            return "";
        }

        String taskId = null;
        String taskState;

        //流程是否已结束（告知前端是否需要轮询状态查询接口）
        Integer isFinishedInt = null;
        //如果上层告知流程是否已结束，以上层结果为准
        if (isFinished != null) {
            isFinishedInt = isFinished ? 1 : 0;
        }

        //获取当前激活的任务列表（并签节点可能有多个）
        final List<TaskInstance> taskInstances = taskQuery(processInstId);
        if (!CollectionUtil.isEmpty(taskInstances)) {
            if (isFinishedInt == null) {
                isFinishedInt = 0;
            }

            //给定任务选择器的情况下交给任务选择器来判断
            if (taskChecker != null) {

                for (TaskInstance taskInstance : taskInstances) {
                    if (taskChecker.evaluate(taskInstance)) {
                        taskId = taskInstance.getId();
                        break;
                    }
                }
            }

            //如果任务选择器没有返回确定的结果
            if (taskId == null) {

                //优先找当前登录用户匹配的任务，如果都不匹配则默认返回第一个
                String finalLoginName = loginName;
                taskId = taskInstances.stream().filter(v -> v.getTarget().equals(finalLoginName)).map(
                        TaskInstance::getId).findAny().orElse(taskInstances.get(0).getId());
            }
            taskState = taskState(taskInstances);
        } else {
            //当前流程任务列表为空，说明流程已经结束
            if (isFinishedInt == null) {
                isFinishedInt = 1;
            }
            //如果业务单据状态还未结束，但是流程实际已经结束了，线程休眠1秒等待通知处理完毕（这是为了页面状态展示的一致性）
            if (isFinished != null && !isFinished) {
                ThreadUtil.sleep(1000);
            }

            final HistoryTaskInstance historyTaskInstance = lastTaskHistoryQuery(processInstId);
            if (historyTaskInstance != null) {
                taskId = historyTaskInstance.getId();
                taskState = taskId;
            } else {
                return "";
            }
        }

        return StringUtil.format(url, taskId, jwtLoginName, processInstId) + "&xState=" + taskState
                + "&xIsFinished=" + isFinishedInt;
    }

    @Override
    public void notice(String processInstId) {
        log.info("接收到通知 processInstId: {}", processInstId);
        final TaskScheduler taskScheduler = ThreadUtil.getTaskScheduler();

        taskScheduler.schedule(() -> {
            try {
                AwsBusinessLog awsBusinessLog = awsBusinessLogService
                        .getByProcessInstId(processInstId);
                if (Objects.isNull(awsBusinessLog)) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未查询到流程数据");
                }

                final List<TaskInstance> taskInstances = taskQuery(processInstId);
                final List<HistoryTaskInstance> historyTaskInstances = historyTaskQuery(
                        processInstId);

                final AwsControlStateEnum awsControlStateEnum = processControlState(processInstId);
                final AwsActionEnum awsActionEnum = getAwsActionEnum(processInstId);

                //通知
                if (AwsControlStateEnum.END == awsControlStateEnum
                        || AwsActionEnum.AGREE == awsActionEnum
                        || AwsActionEnum.DISAGREE == awsActionEnum
                        || AwsActionEnum.RE_SUBMIT == awsActionEnum) {
                    final AwsChangeEvent awsChangeEvent = AwsChangeEvent
                            .ofNotice(awsBusinessLog.getBusinessId(),
                                    PurchaseTypeEnum.getByCode(awsBusinessLog.getType()),
                                    awsControlStateEnum, awsActionEnum, taskInstances,
                                    historyTaskInstances);
                    EventBusUtil.post(awsChangeEvent, true);
                    log.info("通知完成");
                }

                if (!CollectionUtil.isEmpty(taskInstances)) {
                    final String taskState = taskState(taskInstances);
                    RedisUtil.set(PROCESS_ID_KEY + processInstId, taskState, 30, TimeUnit.SECONDS);

                    log.info("流程实例({}):当前任务实例状态发生切换:{}", processInstId, taskState);
                } else if (StringUtils.isNotBlank(RedisUtil.get(PROCESS_ID_KEY + processInstId))) {
                    //魔术值，表示流程已结束
                    RedisUtil.set(PROCESS_ID_KEY + processInstId, "-1", 30, TimeUnit.SECONDS);
                }
            } catch (Throwable e) {
                log.error("流程实例状态变更通知处理异常 流程实例ID:{}", processInstId, e);

                final String message = StringUtil
                        .format("流程实例状态变更通知处理异常 流程实例ID:{} error:{}", processInstId, ExceptionUtil
                                .stacktraceToOneLineString(e));
                Alert.text(MessageRobotCode.GLOBAL, message);
            }
        }, LocalDateTime.now().plusSeconds(2).atZone(ZoneId.systemDefault()).toInstant());
    }

    @Override
    public void processRestart(String processInstId) throws ApiException {
        Assert.notNull(processInstId, "processInstId入参不得为空");
        log.info("撤回审核（尚未结束的流程跳转到到第一个节点 processInstId :{} ", processInstId);

        //验证权限，只有提交者可以撤回
        if (checkPermission(processInstId)) {
            //尚未结束的流程到第一个节点
            restart(processInstId);
            // 通知
            notice(processInstId);
        } else {
            throw new ApiException("没有权限！");
        }
    }

    @Override
    public Boolean checkTask(Long businessId, PurchaseTypeEnum typeEnum, String taskInsId) {
        Assert.notNull(businessId, "businessId入参不得为空");
        Assert.notNull(taskInsId, "taskInsId入参不得为空");

        String processInstId = getProcessInstId(businessId, typeEnum);
        if (StringUtil.isBlank(processInstId)) {
            return false;
        }
        final String key = PROCESS_ID_KEY + processInstId;
        final String refreshedKey = PROCESS_ID_REFRESH_KEY + processInstId + ":" + taskInsId;
        final String currentTaskState = getCurrentTaskState(processInstId);
        boolean isChange = !Objects.equals(taskInsId, currentTaskState);

        //对同一状态的连续刷新进行计数
        Long refreshed = Optional.ofNullable(RedisUtil.get(refreshedKey)).map(Long::parseLong)
                .orElse(0L);
        if (isChange) {
            RedisUtil.set(refreshedKey, ++refreshed, 10, TimeUnit.MINUTES);
            RedisUtil.del(key);
        }

        //连续刷新大于等于3次强制停止刷新
        if (refreshed >= 3) {
            isChange = false;
        }
        return isChange;
    }

    /**
     * 流程实例当前的任务状态（将当前活跃的任务ID排序拼接）
     *
     * @param processInstId 流程实例ID
     * @return 任务状态
     */
    private String getCurrentTaskState(String processInstId) {
        final List<TaskInstance> taskInstances = taskQuery(processInstId);
        String currentTaskState;
        if (taskInstances.isEmpty()) {
            currentTaskState = "-1";
        } else {
            currentTaskState = taskState(taskInstances);
        }
        return currentTaskState;
    }

    @NonNull
    private String taskState(List<TaskInstance> taskInstances) {
        return taskInstances.stream().map(AbstTaskInstanceModel::getId)
                .sorted().collect(Collectors.joining(","));
    }

    private String getProcessInstId(Long businessId, PurchaseTypeEnum typeEnum) {
        return awsBusinessLogService.getProcessIdByBusinessId(typeEnum, businessId);
    }

    @Override
    public void antiAudit(PurchaseTypeEnum type, String processInstId) throws ApiException {
        Assert.notNull(processInstId, "processInstId入参不得为空");
        log.info("反审核 processInstId :{} ", processInstId);

        if (type != PurchaseTypeEnum.OTHER_PAYABLE) {
            throw ExceptionPlusFactory.bizException("该功能无法使用");
        }
        //验证权限，只有提交者可以撤回
        if (checkPermission(processInstId)) {
            //获得第一个节点的 activityDefId
            ProcessInstance processInstance = processInstGet(processInstId);
            //重启流程到第一个节点
            processInstIdAntiAudit(processInstId, processInstance.getStartActivityId(),
                    processInstance.getCreateUser());
            // 通知
            notice(processInstId);
            submit(processInstId);
        } else {
            throw new ApiException("没有权限！");
        }
    }

    @Override
    public void submit(String processInstId) throws ApiException {
        Assert.notNull(processInstId, "processInstId入参不得为空");

        if (checkPermission(processInstId)) {
            final List<TaskInstance> taskInstances = taskQuery(processInstId);
            if (CollectionUtil.isNotEmpty(taskInstances)) {
                final TaskInstance taskInstance = taskInstances.get(0);
                String taskId = taskInstance.getId();
                taskCommit(taskId);
                taskComplete(taskId);
            }
        } else {
            throw new ApiException("没有权限！");
        }
    }

    @Override
    public Boolean hideButton(Long businessId, PurchaseTypeEnum typeEnum) {
        Assert.notNull(businessId, "businessId入参不得为空");
        Assert.notNull(typeEnum, "typeEnum入参不得为空");

        if (PurchaseTypeEnum.PURCHASE_ORDER.equals(typeEnum) || PurchaseTypeEnum.OUT_STOCK_PAYABLE.equals(typeEnum)) {
            return false;
        }

        String processInstId = getProcessInstId(businessId, typeEnum);

        //获取第一节节点的定义ID
        ProcessInstance processInstance = processInstGet(processInstId);
        if (Objects.isNull(processInstance)) {
            return false;
        }
        final String startActivityId = processInstance.getStartActivityId();

        //当前任务
        final List<TaskInstance> taskInstances = taskQuery(processInstId);
        if (CollectionUtil.isEmpty(taskInstances)) {
            return false;
        }
        final TaskInstance taskInstance = taskInstances.get(0);
        final String activityDefId = taskInstance.getActivityDefId();

        return Objects.equals(startActivityId, activityDefId);
    }

    @Override
    public Boolean processTerminate(String processInstId) throws ApiException {
        Assert.notNull(processInstId, "processInstId入参不得为空");

        Map<String, Object> args = Maps.newHashMapWithExpectedSize(2);
        args.put("processInstId", processInstId);
        args.put("uid", getLoginName());
        ApiResponse r = client.getOpenApiClient()
                .exec(AwsConstant.PROCESS_TERMINATE, args, ApiResponse.class);
        log.info("流程终止 :{}", JSON.toJSONString(r));
        return r.isSuccess();
    }

    @Override
    public boolean taskDelegate(String taskInstId, String uid, String targetUid, String reason)
            throws ApiException {
        Map<String, Object> args = new HashMap<>();
        args.put("taskInstId", taskInstId);
        args.put("uid", uid);
        args.put("targetUID", targetUid);
        args.put("delegateReason", reason);
        ApiResponse res = client.getOpenApiClient()
                .exec(AwsConstant.TASK_DELEGATE, args, ApiResponse.class);
        log.info("流程移交 taskInstId:{} uid:{} targetUid:{} reason:{} res:{}", taskInstId, uid,
                targetUid,
                reason, res);
        if (!res.isSuccess()) {
            throw new ApiException(res.getErrorCode(), res.getMsg());
        }
        return res.isSuccess();
    }

    /**
     * 根据类型获得流程定义ID
     *
     * @param type 业务类型 {@link PurchaseTypeEnum}
     */
    private String getProcessDefId(PurchaseTypeEnum type) {
        List<AwsApprovalNode> awsApprovalNodes = listAwsApprovalNode(type);
        Assert.state(CollectionUtils.isNotEmpty(awsApprovalNodes), "审核流节点不得为空");
        return awsApprovalNodes.get(0).getProcessDefId();

    }

    /**
     * 查询审批流节点数据信息
     *
     * @param type 业务类型 {@link PurchaseTypeEnum}
     */
    private List<AwsApprovalNode> listAwsApprovalNode(PurchaseTypeEnum type) {
        QueryWrapper<AwsApprovalNode> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AwsApprovalNode::getType, type.getValue())
                .le(AwsApprovalNode::getVersion, awsConfig.getVersion(type.name()))
                .orderByDesc(AwsApprovalNode::getVersion);
        return awsApprovalNodeService.list(queryWrapper);
    }

    /**
     * 创建流程实例
     *
     * @param processDefId 流程定义Id
     * @param title        流程标题
     */
    private String processCreate(String processDefId, String title) throws ApiException {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(3);
        map.put("processDefId", processDefId);
        map.put("uid", getLoginName());
        map.put("title", title);
        ProcessInstResponse processInstResponse = client.getOpenApiClient()
                .exec(AwsConstant.PROCESS_CREATE, map, ProcessInstResponse.class);
        log.info("创建流程实例: {}", JSON.toJSONString(processInstResponse));
        return processInstResponse.getData().getId();
    }

    /**
     * 绑定数据
     *
     * @param processInstId 流程实例Id
     * @param list          表名称和数据
     */
    private void batchBoCreate(String processInstId, List<String> list, String environment)
            throws ApiException {
        Map<String, Object> boCreateMap = Maps.newHashMapWithExpectedSize(4);
        list.forEach(e -> {
            boCreateMap.put("boName", e);

            HashMap<String, String> recordData = Maps.newHashMapWithExpectedSize(1);
            recordData.put("ENV", environment);
            boCreateMap.put("recordData", recordData);

            boCreateMap.put("bindId", processInstId);
            boCreateMap.put("uid", getLoginName());
            StringResponse response = client.getOpenApiClient()
                    .exec(AwsConstant.BO_CREATE, boCreateMap, StringResponse.class);
            log.info("绑定数据: {}", JSON.toJSONString(response));
        });
    }

    /**
     * 启动流程
     *
     * @param processInstId 流程实例Id
     */
    private void processStart(String processInstId) throws ApiException {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(1);
        args.put("processInstId", processInstId);
        ApiResponse apiResponse = client.getOpenApiClient()
                .exec(AwsConstant.PROCESS_START, args, ApiResponse.class);
        log.info("启动实例: {}", JSON.toJSONString(apiResponse));
    }

    /**
     * 炎黄盈动插入数据
     *
     * @param businessId    业务ID
     * @param processInstId 流程实例ID
     * @param type          业务类型 {@link PurchaseTypeEnum}
     */
    private void createAwsBusinessLog(Long businessId, String processInstId, Integer type) {
        AwsBusinessLog awsBusinessLog = new AwsBusinessLog();
        awsBusinessLog.setBusinessId(businessId);
        awsBusinessLog.setProcessId(processInstId);
        awsBusinessLog.setType(type);
        awsBusinessLogService.save(awsBusinessLog);
        log.info("炎黄盈动插入数据成功");
    }

    /**
     * 为任务提交审批留言
     *
     * @param taskInstId 节点ID
     */
    private void taskCommit(String taskInstId) {
        taskCommit(taskInstId, "");
    }

    /**
     * 为任务提交审批留言
     *
     * @param taskInstId 节点ID
     */
    private void taskCommit(String taskInstId, String commentMsg) {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(4);
        args.put("taskInstId", taskInstId);
        args.put("user", getLoginName());
        args.put("actionName", AwsActionEnum.SUBMIT.getDesc());
        args.put("isIgnoreDefaultSetting", true);
        args.put("commentMsg", commentMsg);
        BoolResponse r = client.getOpenApiClient()
                .exec(AwsConstant.TASK_COMMENT_COMMIT, args, BoolResponse.class);
        log.info("为任务提交审批留言：: {}", JSON.toJSONString(Arrays.asList(args, r)));
    }

    /**
     * 提交任务
     *
     * @param taskInstId 节点ID
     */
    private void taskComplete(String taskInstId) {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(2);
        args.put("taskInstId", taskInstId);
        args.put("uid", getLoginName());
        MapResponse r = client.getOpenApiClient()
                .exec(AwsConstant.TASK_COMPLETE, args, MapResponse.class);
        log.info("提交任务:{}", JSON.toJSONString(Arrays.asList(args, r)));
    }

    /**
     * 流程是否结束
     *
     * @param processInstId 流程实例ID
     * @return true:流程结束 false:流程未结束
     */
    private Boolean processEndCheck(String processInstId) {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(1);
        args.put("processInstId", processInstId);
        BoolResponse r = client.getOpenApiClient()
                .exec(AwsConstant.PROCESS_END_CHECK, args, BoolResponse.class);
        return r.isData();
    }

    /**
     * 查询当前任务
     *
     * @param processInstId 流程实例ID
     */
    @Override
    public List<TaskInstance> taskQuery(String processInstId) throws ApiException {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(1);
        TaskQueryModel taskQueryModel = new TaskQueryModel();
        taskQueryModel.setProcessInstId(processInstId);
        args.put("tqm", taskQueryModel);
        args.put("firstRow", 0);
        args.put("rowCount", 500);
        TaskInstsGetResponse taskResponse = client.getOpenApiClient()
                .exec(AwsConstant.TASK_QUERY_PAGE, args, TaskInstsGetResponse.class);
        return taskResponse.getData();
    }

    @Override
    public List<TaskInstance> taskQuery(TaskQueryModel queryModel) throws ApiException {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(1);
        args.put("tqm", queryModel);
        args.put("firstRow", 0);
        args.put("rowCount", 500);
        TaskInstsGetResponse taskResponse = client.getOpenApiClient()
                .exec(AwsConstant.TASK_QUERY_PAGE, args, TaskInstsGetResponse.class);
        return taskResponse.getData();
    }

    @Override
    public List<HistoryTaskInstance> historyTaskQuery(String processInstId) throws ApiException {
        String apiMethod = AwsConstant.TASK_HISTORY_QUERY;
        Map<String, Object> args = new HashMap<>();
        JSONObject json = new JSONObject();
        json.put("processInstId", processInstId);
        args.put("tqm", json.toJSONString());
        HisTaskInstsGetResponse taskResponse = client.getOpenApiClient()
                .exec(apiMethod, args, HisTaskInstsGetResponse.class);
        return taskResponse.getData();
    }

    @Override
    public void boFieldUpdate(String boName, String processInstId, String fieldName, Object value) {
        final AwsProcessClientImpl awsProcessClient = new AwsProcessClientImpl(client.getOpenApiClient());
        awsProcessClient.boFieldUpdate(boName, processInstId, fieldName, value);
    }

    @Override
    public boolean processSuspendCheck(String processInstId) throws ApiException {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(1);
        args.put("processInstId", processInstId);
        final BoolResponse response = client.getOpenApiClient().exec(AwsConstant.PROCESS_SUSPEND_CHECK, args, BoolResponse.class);
        return response.isData();
    }

    @Override
    public void processSuspend(String processInstId) throws ApiException {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(1);
        args.put("processInstId", processInstId);
        client.getOpenApiClient().exec(AwsConstant.PROCESS_SUSPEND, args, BoolResponse.class);
    }

    @Override
    public void processResume(String processInstId) throws ApiException {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(1);
        args.put("processInstId", processInstId);
        client.getOpenApiClient().exec(AwsConstant.PROCESS_RESUME, args, BoolResponse.class);
    }


    /**
     * 获得流程实例
     *
     * @param processInstId 流程定义ID
     */
    private ProcessInstResponse getProcess(String processInstId) throws ApiException {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(1);
        args.put("processInstId", processInstId);
        ProcessInstResponse instResponse = client.getOpenApiClient()
                .exec(AwsConstant.PROCESS_INST_GET, args, ProcessInstResponse.class);
        if (Objects.isNull(instResponse.getData())) {
            throw new ApiException("流程实例不存在");
        }
        return instResponse;
    }

    /**
     * 获得流程状态
     *
     * @param processInstId 流程实例ID
     */
    private AwsControlStateEnum processControlState(String processInstId) throws ApiException {
        Assert.notNull(processInstId, "processInstId入参不得为空");
        log.info("processInstId: {}", processInstId);

        ProcessInstResponse instResponse = getProcess(processInstId);
        String controlState = instResponse.getData().getControlState();
        final AwsControlStateEnum awsControlStateEnum = AwsControlStateEnum.getByDesc(controlState);
        log.info("流程状态:{}", awsControlStateEnum);
        return awsControlStateEnum;
    }

    /**
     * 通过Id获得流程实例的审批留言记录
     *
     * @param processInstId 流程实例ID
     */
    private TaskCommentModelsGetResponse commentsGet(String processInstId) throws ApiException {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(1);
        args.put("processInstId", processInstId);
        TaskCommentModelsGetResponse response = client.getOpenApiClient()
                .exec(AwsConstant.PROCESS_COMMENTS_GET, args, TaskCommentModelsGetResponse.class);
        log.info("流程实例的审批留言记录: {}", JSON.toJSONString(response));
        return response;
    }

    /**
     * 获得审批留言的最后一条记录的审核动作名 如果最后一条是 提交 那么再往上查询一条 确认是不是撤销重办，如果是，则返回撤销重办，否则则按最后一条返回
     *
     * @param processInstId 流程实例ID
     */
    private AwsActionEnum getAwsActionEnum(String processInstId) {
        final TaskCommentModelsGetResponse response = commentsGet(processInstId);
        final List<TaskComment> data = response.getData();
        if (CollectionUtils.isNotEmpty(data)) {
            final TaskComment taskComment = data.get(data.size() - 1);
            AwsActionEnum awsActionEnum = AwsActionEnum
                    .getByDesc(taskComment.getActionName());
            if (AwsActionEnum.SUBMIT == awsActionEnum && data.size() > 2) {
                // 往上查查询一条
                final TaskComment taskComment1 = data.get(data.size() - 2);
                if (AwsActionEnum.UNDO_REDO == AwsActionEnum
                        .getByDesc(taskComment1.getActionName())) {
                    log.info("审核动作名为:{}", AwsActionEnum.RE_SUBMIT);
                    return AwsActionEnum.RE_SUBMIT;
                }
            }
            //为了避免上层业务做过多的修改，这边将终止算作拒绝
            if (awsActionEnum == AwsActionEnum.TERMINATE) {
                awsActionEnum = AwsActionEnum.DISAGREE;
            }
            log.info("审核动作名为:{}", awsActionEnum);
            return awsActionEnum;
        }
        return null;
    }

    /**
     * 流程第一个节点ID
     *
     * @param processInstId 流程实例ID
     */
    private ProcessInstance processInstGet(String processInstId) throws ApiException {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(1);
        args.put("processInstId", processInstId);
        ProcessInstResponse r = client.getOpenApiClient()
                .exec(AwsConstant.PROCESS_INST_GET, args, ProcessInstResponse.class);
        log.info("流程第一个节点实例：{}", JSON.toJSONString(r));
        return r.getData();
    }

    /**
     * 尚未结束的流程重置流程到第一个节点
     *
     * @param processInstId 流程实例ID
     */
    private void restart(String processInstId) throws ApiException {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(1);
        args.put("processInstId", processInstId);
        TaskInstGetResponse r = client.getOpenApiClient()
                .exec(AwsConstant.PROCESS_RESTART, args, TaskInstGetResponse.class);
        log.info("尚未结束的流程重置流程到第一个节点: {}", JSON.toJSONString(r));
    }

    /**
     * 已经结束的流程重置流程到第一个节点
     *
     * @param processInstId 流程实例ID
     */
    private void processInstIdAntiAudit(String processInstId, String startActivityId,
                                        String createUser) throws ApiException {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(6);
        args.put("processInstId", processInstId);
        args.put("targetActivityId", startActivityId);
        args.put("isClearHistory", true);
        args.put("uid", getLoginName());
        args.put("targetUID", createUser);
        args.put("reactivateReason", "反审核");
        TaskInstGetResponse r = client.getOpenApiClient()
                .exec(AwsConstant.PROCESS_REACTIVATE, args, TaskInstGetResponse.class);
        log.info("已经结束的流程重启到第一个节点: {}", JSON.toJSONString(r));
    }

    /**
     * 查询历史记录最近的一条
     *
     * @param processInstId 流程实例ID
     */
    private HistoryTaskInstance lastTaskHistoryQuery(String processInstId) throws ApiException {
        Map<String, Object> args = Maps.newHashMapWithExpectedSize(1);
        TaskQueryModel taskQueryModel = new TaskQueryModel();
        taskQueryModel.setProcessInstId(processInstId);
        args.put("tqm", taskQueryModel);
        HisTaskInstsGetResponse r = client.getOpenApiClient()
                .exec(AwsConstant.TASK_HISTORY_QUERY, args, HisTaskInstsGetResponse.class);
        log.info("查询历史任务: {}", JSON.toJSONString(r));
        final List<HistoryTaskInstance> data = r.getData();
        if (CollectionUtils.isNotEmpty(data)) {
            return data.get(0);
        }
        return null;
    }

    private String jwtLoginName(String loginName) {
        Map<String, Object> payload = new HashMap<>();
        //签发时间
        payload.put(JWTPayload.ISSUED_AT, new Date());
        //过期时间
        payload.put(JWTPayload.EXPIRES_AT, DateUtil.offsetMinute(new Date(), 1));
        //载荷
        payload.put("loginName", loginName);
        String key = "erp";
        return JWTUtil.createToken(payload, key.getBytes());
    }

    /**
     * 验证当前账户是否是提交者
     *
     * @param processInstId 流程实例ID
     * @return true:是提交者
     */
    private Boolean checkPermission(String processInstId) {
        final ProcessInstResponse process = getProcess(processInstId);
        final String loginName = getLoginName();
        return Objects.equals(process.getData().getCreateUser(), loginName);
    }

    /**
     * 获得登录名
     */
    private String getLoginName() {
        String loginName = UserContext.getLoginName();
        if (StringUtil.isBlank(loginName)) {
            loginName = "admin";
        }
        return loginName;
    }

    @Data
    static class ProcessInstIdCacheKey {

        private final PurchaseTypeEnum typeEnum;
        private final Long businessId;
    }

    @Override
    public List<Long> getTodoList(Integer type, String loginName) {
        final TaskQueryModel queryModel = new TaskQueryModel();
        queryModel.setTarget(loginName);
        final List<TaskInstance> taskInstances = taskQuery(queryModel);
        if (taskInstances.isEmpty()) {
            return Collections.emptyList();
        }
        final List<String> processInstIds = taskInstances.stream()
                .map(TaskInstance::getProcessInstId)
                .distinct()
                .collect(Collectors.toList());
        if (PurchaseTypeEnum.OUT_STOCK_PAYABLE.getValue().equals(type)) {
            return iStockOutOrderService.lambdaQuery()
                    .in(StockOutOrder::getWorkbenchProcessId, processInstIds)
                    .list().stream().map(StockOutOrder::getId).collect(Collectors.toList());
        } else if (PurchaseTypeEnum.PURCHASE_ORDER.getValue().equals(type)) {
            return iPurchaseOrderService.lambdaQuery()
                    .in(PurchaseOrder::getWorkbenchProcessId, processInstIds)
                    .list().stream().map(PurchaseOrder::getId).collect(Collectors.toList());
        } else {
            return new LinkedList<>();
        }


//        final List<AwsBusinessLog> awsBusinessLogs = awsBusinessLogService.lambdaQuery()
//                .in(AwsBusinessLog::getProcessId, processInstIds)
//                .eq(AwsBusinessLog::getType, type)
//                .list();
//        if (awsBusinessLogs.isEmpty()) {
//            return Collections.emptyList();
//        }
//        return awsBusinessLogs.stream()
//                .map(AwsBusinessLog::getBusinessId)
//                .collect(Collectors.toList());
    }

}
