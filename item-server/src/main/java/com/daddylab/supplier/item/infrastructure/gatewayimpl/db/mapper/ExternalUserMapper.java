package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExternalUser;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 外部账号管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
public interface ExternalUserMapper extends DaddyBaseMapper<ExternalUser> {

    @Select("select id from external_user where tel = #{tel} order by id desc limit 1")
    Long getIdByTel(@Param("tel") Long tel);

}
