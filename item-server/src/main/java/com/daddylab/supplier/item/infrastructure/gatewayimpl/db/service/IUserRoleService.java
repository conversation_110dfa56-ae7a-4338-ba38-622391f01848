package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.UserRole;

/**
 * <p>
 * 用户角色 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-17
 */
public interface IUserRoleService extends IDaddyService<UserRole> {

    void demo();

}
