package com.daddylab.supplier.item.infrastructure.gatewayimpl.stockInOrder;

import com.daddylab.supplier.item.domain.stockInOrder.gateway.StockInOrderDetailGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockInOrderDetailMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
/**
 * 采购入库明细网关实现
 * <AUTHOR>
 * @date 2022/3/24 17:36
 **/
@Component
public class StockInOrderDetailGatewayGatewayImpl implements StockInOrderDetailGateway {
    @Autowired
    private StockInOrderDetailMapper stockInOrderDetailMapper;
}
