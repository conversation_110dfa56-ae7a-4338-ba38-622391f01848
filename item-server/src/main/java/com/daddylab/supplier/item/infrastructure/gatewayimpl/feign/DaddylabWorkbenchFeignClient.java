package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.DaddylabWorkbenchResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.PurchaseOrderProcessForm;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.StockOutOrderProcessForm;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 老爸工作台。
 */
@FeignClient(name = "daddylabWorkbenchFeignClient", url = "${ms-workbench-server.url:http://api4j.dlab.cn}", configuration = DaddylabWorkbenchFeignConfig.class)
public interface DaddylabWorkbenchFeignClient {

    /**
     * 流程发起(采购订单申请)
     *
     * @param requestParam
     * @return
     */
    @PostMapping(value = "/workbench/open/process/purchaseOrderLaunch")
    DaddylabWorkbenchResponse purchaseOrderLaunch(@RequestBody PurchaseOrderProcessForm requestParam);

    /**
     * 流程发起(退料出库申请单)
     *
     * @param requestParam
     * @return
     */
    @PostMapping(value = "/workbench/open/process/stockOutOrderLaunch")
    DaddylabWorkbenchResponse stockOutOrderLaunch(@RequestBody StockOutOrderProcessForm requestParam);


}
