package com.daddylab.supplier.item.application.drawer.impl;

import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.daddylab.supplier.item.application.drawer.ItemDrawerMarkImageService;
import com.daddylab.supplier.item.application.drawer.ItemDrawerService;
import com.daddylab.supplier.item.application.drawer.converter.ItemDrawerConverter;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.drawer.form.ItemDrawerMarkImageContentForm;
import com.daddylab.supplier.item.domain.drawer.form.ItemDrawerMarkImageForm;
import com.daddylab.supplier.item.domain.drawer.vo.ItemDrawerMarkImageContentVO;
import com.daddylab.supplier.item.domain.drawer.vo.ItemDrawerMarkImageVO;
import com.daddylab.supplier.item.domain.drawer.vo.ItemDrawerVO;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMarkImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMarkImageContent;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerMarkImageContentMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerMarkImageContentService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerMarkImageService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * Class ItemDrawerMarkImageAppServiceImpl @Date 2022/6/1下午2:35 <AUTHOR>
 */
@Service
@Slf4j
public class ItemDrawerMarkImageAppServiceImpl implements ItemDrawerMarkImageService {

  @Autowired private IItemDrawerMarkImageService itemDrawerMarkImageService;

  @Autowired private ItemDrawerMarkImageContentMapper itemDrawerMarkImageContentMapper;
  @Autowired private IItemDrawerMarkImageContentService itemDrawerMarkImageContentService;
  @Autowired private ItemDrawerService itemDrawerService;

  @Autowired
  @Qualifier("UserGatewayCacheImpl")
  private UserGateway userGateway;

  @Transactional
  @Override
  public SingleResponse<Long> add(ItemDrawerMarkImageForm itemDrawerMarkImageForm, Long userId) {
    return SingleResponse.of(save(itemDrawerMarkImageForm, userId, true).getRight());
  }

  /**
   * 保存逻辑
   *
   * @param itemDrawerMarkImageForm
   * @param userId
   * @param isNewRecord
   * @return
   */
  @SuppressWarnings({"unchecked"})
  Pair<Boolean, Long> save(
      ItemDrawerMarkImageForm itemDrawerMarkImageForm, Long userId, Boolean isNewRecord) {
    ItemDrawerVO itemDrawerVO =
        itemDrawerService.getItemDrawerVO(itemDrawerMarkImageForm.getDrawerId());
    if (Objects.isNull(itemDrawerVO)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "抽屉不存在");
    }
    /** 待审核状态才能操作 */
    if (!ItemLaunchStatus.TO_BE_AUDITED.getValue().equals(itemDrawerVO.getItemLaunchStatus())) {
      throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "商品状态仅待审核才可编辑");
    }
    List<ItemDrawerMarkImageContent> allMarkContents =
        itemDrawerMarkImageContentService
            .lambdaQuery()
            .eq(ItemDrawerMarkImageContent::getDrawerId, itemDrawerMarkImageForm.getDrawerId())
            .list();
    ItemDrawerMarkImage itemDrawerMarkImage = new ItemDrawerMarkImage();
    itemDrawerMarkImage.setDrawerId(itemDrawerMarkImageForm.getDrawerId());
    itemDrawerMarkImage.setUrl(Optional.ofNullable(itemDrawerMarkImageForm.getUrl()).orElse(""));
    itemDrawerMarkImage.setMarkMeta(itemDrawerMarkImageForm.getMarkMeta());
    if (!itemDrawerMarkImageService.save(itemDrawerMarkImage)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR);
    }
    final Map<Long, ItemDrawerMarkImageContent> currentMarkContentsMap =
        allMarkContents.stream().collect(Collectors.toMap(Entity::getId, Function.identity()));
    for (ItemDrawerMarkImageContentForm itemDrawerMarkImageContentForm :
        itemDrawerMarkImageForm.getItemDrawerMarkImageContents()) {
      if (Objects.nonNull(itemDrawerMarkImageContentForm.getId())) {
        final ItemDrawerMarkImageContent markImageContentExits =
            currentMarkContentsMap.get(itemDrawerMarkImageContentForm.getId());
        Assert.notNull(markImageContentExits, "标注发生修改，请刷新页面后重试");
        markImageContentExits.setId(null); // 复制一份，不覆盖之前的记录
        markImageContentExits.setMarkImageId(itemDrawerMarkImage.getId());
        if (!Objects.equals(markImageContentExits.getKey(), itemDrawerMarkImageContentForm.getKey())
            || !Objects.equals(
                markImageContentExits.getValue(), itemDrawerMarkImageContentForm.getValue())
            || !Objects.equals(
                markImageContentExits.getType(), itemDrawerMarkImageContentForm.getType())
            || !Objects.equals(
                markImageContentExits.getPosition(), itemDrawerMarkImageContentForm.getPosition())
            || !Objects.equals(
                markImageContentExits.getPidentifier(),
                itemDrawerMarkImageContentForm.getPidentifier())) {
          markImageContentExits.setKey(itemDrawerMarkImageContentForm.getKey());
          markImageContentExits.setValue(itemDrawerMarkImageContentForm.getValue());
          markImageContentExits.setType(itemDrawerMarkImageContentForm.getType());
          markImageContentExits.setPosition(itemDrawerMarkImageContentForm.getPosition());
          markImageContentExits.setPidentifier(itemDrawerMarkImageContentForm.getPidentifier());
          markImageContentExits.setUpdatedAt(DateUtil.currentTime());
          markImageContentExits.setUpdatedUid(UserContext.getUserId());
        }
        itemDrawerMarkImageContentService.save(markImageContentExits);
      } else {
        ItemDrawerMarkImageContent itemDrawerMarkImageContent = new ItemDrawerMarkImageContent();
        itemDrawerMarkImageContent.setDrawerId(itemDrawerMarkImage.getDrawerId());
        itemDrawerMarkImageContent.setMarkImageId(itemDrawerMarkImage.getId());
        itemDrawerMarkImageContent.setKey(itemDrawerMarkImageContentForm.getKey());
        itemDrawerMarkImageContent.setValue(itemDrawerMarkImageContentForm.getValue());
        itemDrawerMarkImageContent.setType(itemDrawerMarkImageContentForm.getType());
        itemDrawerMarkImageContent.setPosition(itemDrawerMarkImageContentForm.getPosition());
        itemDrawerMarkImageContent.setPidentifier(itemDrawerMarkImageContentForm.getPidentifier());

        itemDrawerMarkImageContentService.save(itemDrawerMarkImageContent);
      }
    }
    return Pair.of(true, itemDrawerMarkImage.getId());
  }

  @Transactional
  @Override
  public SingleResponse<Boolean> edit(
      ItemDrawerMarkImageForm itemDrawerMarkImageForm, Long userId) {
    return SingleResponse.of(save(itemDrawerMarkImageForm, userId, false).getLeft());
  }

  @Override
  public SingleResponse<ItemDrawerMarkImageVO> detail(Long drawerId) {
    ItemDrawerMarkImage itemDrawerMarkImage =
        itemDrawerMarkImageService.getOne(
            new LambdaQueryWrapper<ItemDrawerMarkImage>() {
              {
                eq(ItemDrawerMarkImage::getDrawerId, drawerId);
                last("limit 1");
                orderByDesc(ItemDrawerMarkImage::getId);
              }
            });
    if (Objects.isNull(itemDrawerMarkImage)) {
      return SingleResponse.of(null);
    }
    ItemDrawerMarkImageVO itemDrawerMarkImageVO =
        ItemDrawerConverter.INSTANCE.itemDrawerMarkImageToVO(itemDrawerMarkImage);
    List<ItemDrawerMarkImageContent> itemDrawerMarkImageContents =
        itemDrawerMarkImageContentService
            .getBaseMapper()
            .selectList(
                new LambdaQueryWrapper<ItemDrawerMarkImageContent>() {
                  {
                    eq(ItemDrawerMarkImageContent::getMarkImageId, itemDrawerMarkImage.getId());
                  }
                });
    final List<Long> creatorUids =
        itemDrawerMarkImageContents.stream()
            .map(Entity::getCreatedUid)
            .distinct()
            .collect(Collectors.toList());
    final Map<Long, StaffInfo> staffInfoMap = userGateway.batchQueryStaffInfoByIds(creatorUids);
    final List<ItemDrawerMarkImageContentVO> itemDrawerMarkImageContentVOS =
        ItemDrawerConverter.INSTANCE.itemDrawerMarkImageContentToListVO(
            itemDrawerMarkImageContents);
    itemDrawerMarkImageContentVOS.forEach(
        v -> {
          Optional.ofNullable(staffInfoMap.get(v.getMarkUid()))
              .ifPresent(
                  staffInfo -> {
                    v.setMarkUserNick(staffInfo.getNickname());
                    v.setMarkUserName(staffInfo.getUserName());
                  });
        });
    itemDrawerMarkImageVO.setItemDrawerMarkImageContents(itemDrawerMarkImageContentVOS);
    return SingleResponse.of(itemDrawerMarkImageVO);
  }

  @Override
  public Boolean deleteByDrawerId(Long drawerId) {
    List<ItemDrawerMarkImage> itemDrawerMarkImages =
        itemDrawerMarkImageService
            .getBaseMapper()
            .selectList(
                new LambdaQueryWrapper<ItemDrawerMarkImage>() {
                  {
                    eq(ItemDrawerMarkImage::getDrawerId, drawerId);
                  }
                });
    if (itemDrawerMarkImages.isEmpty()) {
      return false;
    }

    // 删除标记内容
    itemDrawerMarkImageContentService
        .getBaseMapper()
        .delete(
            new LambdaQueryWrapper<ItemDrawerMarkImageContent>() {
              {
                eq(ItemDrawerMarkImageContent::getDrawerId, drawerId);
              }
            });
    return itemDrawerMarkImageService.remove(
        new LambdaQueryWrapper<ItemDrawerMarkImage>() {
          {
            eq(ItemDrawerMarkImage::getDrawerId, drawerId);
          }
        });
  }

  @Override
  public boolean clean(Long itemId, Boolean cleanAll, Long userId) {
    final ItemDrawer itemDrawer = itemDrawerService.getItemDrawer(itemId);
    Objects.requireNonNull(itemDrawer, "商品无抽屉数据");
    final Long itemDrawerId = itemDrawer.getId();
    final List<ItemDrawerMarkImage> list =
        itemDrawerMarkImageService
            .lambdaQuery()
            .eq(ItemDrawerMarkImage::getDrawerId, itemDrawerId)
            .select(Entity::getId)
            .orderByDesc(Entity::getId)
            .list();
    if (list.isEmpty()) {
      return false;
    }
    Stream<Long> stream =
        list.stream()
            .sorted(Comparator.comparingLong(Entity::getId).reversed())
            .map(ItemDrawerMarkImage::getId);
    if (!cleanAll) {
      stream = stream.limit(1);
    }
    final Set<Long> removeMarkImageIds = stream.collect(Collectors.toSet());
    if (removeMarkImageIds.isEmpty()) {
      return false;
    }
    itemDrawerMarkImageService.removeByIds(removeMarkImageIds);
    itemDrawerMarkImageContentService
        .lambdaUpdate()
        .in(ItemDrawerMarkImageContent::getMarkImageId, removeMarkImageIds)
        .remove();
    log.info("清除商品详情标记图片及其相关标记内容:{}", removeMarkImageIds);
    return true;
  }
}
