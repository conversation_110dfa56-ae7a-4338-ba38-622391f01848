package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 旺店通退货入库单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtRefundStockInOrderDetails implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 明细ID（主键）
     */
    private Long recId;

    /**
     * 入库单ID
     */
    private Long stockinId;

    /**
     * 数量
     */
    private BigDecimal num;

    /**
     * 总成本=明细数量*成本（以系统配置决定是实际成本/计划成本）
     */
    private BigDecimal totalCost;

    /**
     * 明细备注
     */
    private String remark;

    /**
     * 调整后数量
     */
    private BigDecimal rightNum;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 货品编码
     */
    private String goodsNo;

    /**
     * 商家编码
     */
    private String specNo;

    /**
     * 默认为非残次品
     */
    private Boolean defect;

    /**
     * 单品自定义属性2（来源货品档案）
     */
    private String prop2;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 规格码
     */
    private String specCode;

    /**
     * 品牌编号
     */
    private String brandNo;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 辅助单位
     */
    private String goodsUnit;

    /**
     * 物流名称
     */
    private String logisticsName;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 仓库唯一键
     */
    private Integer warehouseId;

    /**
     * 退换单主键
     */
    private Integer srcOrderId;

    /**
     * 物流公司唯一键
     */
    private Integer logisticsId;

    /**
     * 基本单位
     */
    private String basicUnitName;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 有效期，样例:2020-04-20 00:00:00
     */
    private LocalDateTime expireDate;

    /**
     * 货位
     */
    private String positionNo;

    /**
     * 销售出库单审核时成本价
     */
    private BigDecimal checkedCostPrice;

    /**
     * 当need_sn=true时返回英文逗号分隔的sn
     */
    private String snList;

    @TableField(exist = false)
    List<WdtRefundStockInOrderDetailsRefundOrderDetails> refundOrderDetailList;

}
