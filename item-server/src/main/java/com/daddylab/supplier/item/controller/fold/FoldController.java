package com.daddylab.supplier.item.controller.fold;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.fold.FoldBizService;
import com.daddylab.supplier.item.application.fold.dto.FoldCmd;
import com.daddylab.supplier.item.application.fold.dto.FoldVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @ClassName FoldController.java
 * @description
 * @createTime 2022年06月07日 15:20:00
 */
@Slf4j
@Api(value = "折叠业务API", tags = "折叠业务API")
@RestController
@RequestMapping("/fold")
public class FoldController {
    @Autowired
    private FoldBizService foldBizService;

    @ResponseBody
    @ApiOperation(value = "修改折叠字段")
    @PostMapping("/createOrUpdate")
    public Response update(@RequestBody @Validated List<FoldCmd> cmdList) {
        log.info("折叠业务修改 cmd:{}", cmdList);
        return foldBizService.saveOrUpdate(cmdList);
    }

    @ResponseBody
    @ApiOperation(value = "查看折叠列")
    @GetMapping("/getFold")
    public MultiResponse<FoldVo> getFold(@ApiParam("type") Integer type) {
        return foldBizService.getFold(type);
    }


}
