package com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLockKey;
import lombok.Data;

import javax.annotation.Nullable;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @class PlatformItemSkuSyncInfo.java
 * @description 描述类的作用
 * @date 2024-03-13 17:46
 */
@Data
public class PlatformSkuInventorySyncInfo implements Serializable {

    private static final long serialVersionUID = -4099667374000621216L;

    @DistributedLockKey
    private Platform type;

    /**
     * 平台SKU ID
     */
    @DistributedLockKey
    private String outerSkuId;

    /**
     * 平台商品ID
     */
    private String outerItemId;

    /**
     * 平台商品规格外部编码
     */
    private String outerSkuCode;

    /**
     * 平台商品商家外部编码
     */
    @Nullable
    private String outerItemCode;

    /**
     * 平台商品库存
     */
    private Long stock;

    /**
     * 平台商品状态 0 下架 1 上架
     */
    private Integer status;
}
