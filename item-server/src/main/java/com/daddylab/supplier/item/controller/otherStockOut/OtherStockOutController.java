package com.daddylab.supplier.item.controller.otherStockOut;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.otherStockOut.OtherStockOutBizService;
import com.daddylab.supplier.item.controller.otherStockOut.dto.OtherStockOutQueryPage;
import com.daddylab.supplier.item.controller.otherStockOut.dto.OtherStockOutVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @ClassName OtherStockOutController.java
 * @description
 * @createTime 2022年04月01日 16:22:00
 */
@Slf4j
@Api(value = "其他出库单API", tags = "其他出库单API")
@RestController
@RequestMapping("/otherStockOut")
public class OtherStockOutController {
    @Autowired
    private OtherStockOutBizService otherStockOutBizService;

    @ResponseBody
    @ApiOperation(value = "其他出库单分页")
    @PostMapping("/viewList")
    public PageResponse<OtherStockOutVo> viewList(@RequestBody OtherStockOutQueryPage queryPage) {
        return otherStockOutBizService.queryPage(queryPage);
    }

    @ResponseBody
    @ApiOperation(value = "根据id查询其他出库单详情信息")
    @GetMapping("/getById")
    public SingleResponse<OtherStockOutVo> getById(@ApiParam("其他出库单id") Long id) {
        return otherStockOutBizService.getById(id);
    }
}
