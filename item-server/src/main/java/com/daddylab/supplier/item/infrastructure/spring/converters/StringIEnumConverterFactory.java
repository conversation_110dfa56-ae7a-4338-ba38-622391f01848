package com.daddylab.supplier.item.infrastructure.spring.converters;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.converter.ConverterFactory;

public class StringIEnumConverterFactory implements ConverterFactory<String, IEnum<?>> {
    @Override
    public <T extends IEnum<?>> Converter<String, T> getConverter(Class<T> targetType) {
        return source -> IEnum.getEnumByName(targetType, source);
    }
}
