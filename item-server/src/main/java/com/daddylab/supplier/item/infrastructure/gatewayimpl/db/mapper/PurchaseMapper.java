package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.controller.purchase.dto.PurchaseQueryPage;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseVo;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.PurchasePriceDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Purchase;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 采购确认 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
public interface PurchaseMapper extends DaddyBaseMapper<Purchase> {

    List<Purchase> queryExport(@Param("param") PurchaseQueryPage param);

    Integer countExport(@Param("param") PurchaseQueryPage param);

    /**
     * 批量查询sku价格
     *
     * @return
     */
    List<PurchasePriceDO> getPurchasePrice(@Param("skuCode") String skuCode,
                                           @Param("payTime") Long payTime,
                                           @Param("platformType") Integer platformType);

    /**
     * 设置默认排序
     */
    int setDefaultSort();


//    @Select("select item_sku from purchase where month = #{payMonth} and is_del = 0 ")
//    List<String> haveActivityPriceSkuList(@Param("payMonth") String payMonth);

//    @Select("select count(item_sku) from purchase where month = #{payMonth} and is_del = 0 ")
//    Integer countHaveActivityPriceSkuList(@Param("payMonth") String payMonth);

    List<PurchasePriceDO> getPurchasePriceByCode(@Param("skuCode") String skuCode);

    List<PurchasePriceDO> getActivityQuantityPriceList(@Param("month") String month);


    List<PurchaseVo> page(PurchaseQueryPage purchaseQueryPage);


}
