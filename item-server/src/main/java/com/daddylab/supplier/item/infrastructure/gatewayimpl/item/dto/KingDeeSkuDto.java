package com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/5 2:27 下午
 * @description
 */
@Data
public class KingDeeSkuDto {

    String itemName;

    /**
     * 品类在金蝶的编码 CategoryApiReq.number()
     */
    String categoryKingDeeNum;

    String attrJsonStr;

    String skuCode;

    String procurementCosts;

    /**
     * 供应商在金蝶的编码 ProviderApiReq.number()
     */
    String providerKingDeeNum;

    String buyerKingDeeNum;

    Long skuId;

    String skuKingDeeId;


}
