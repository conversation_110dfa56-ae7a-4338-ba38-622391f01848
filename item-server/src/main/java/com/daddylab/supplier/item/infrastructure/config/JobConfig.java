package com.daddylab.supplier.item.infrastructure.config;

import com.daddylab.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * <AUTHOR> up
 * @date 2022/3/16 5:16 下午
 */
@Data
@Component
@ConfigurationProperties(prefix = "daddylab.job")
public class JobConfig {
    private String adminAddress;
    private String accessToken;
    private String appName;
    private String address;
    private String ip;
    private String port;
    private String logPath;
    private String logRetentionDays;

    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        Assert.notNull(port, "job executor port不得为空");
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddress);
        xxlJobSpringExecutor.setAppname(appName);
        xxlJobSpringExecutor.setAddress(address);
        xxlJobSpringExecutor.setIp(ip);
        xxlJobSpringExecutor.setPort(Integer.parseInt(port));
        xxlJobSpringExecutor.setAccessToken(accessToken);
        xxlJobSpringExecutor.setLogPath(logPath);
        xxlJobSpringExecutor.setLogRetentionDays(Integer.parseInt(logRetentionDays));
        return xxlJobSpringExecutor;
    }

}
