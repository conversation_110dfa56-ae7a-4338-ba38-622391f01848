package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.TaxRate;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

import java.math.BigDecimal;

/**
 * <p>
 * 税率表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
public interface ITaxRateService extends IDaddyService<TaxRate> {

    String kingDeeNo(BigDecimal taxRate);

}
