package com.daddylab.supplier.item.infrastructure.kuaidaoyun;

import com.daddylab.supplier.item.infrastructure.kuaidaoyun.types.KdyBaseRequest;
import com.daddylab.supplier.item.infrastructure.kuaidaoyun.types.KdySubscribeData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 * @since 2024/5/21
 */
@FeignClient(name = "Kdy<PERSON><PERSON>", url = "${kuaidaoyun.uri:http://i.kuaidaoyun.com}")
public interface KdyAPI {
    @RequestMapping(method = RequestMethod.POST, path = "/postids.aspx", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    String subscribe(@RequestBody KdyBaseRequest<KdySubscribeData> request);
}
