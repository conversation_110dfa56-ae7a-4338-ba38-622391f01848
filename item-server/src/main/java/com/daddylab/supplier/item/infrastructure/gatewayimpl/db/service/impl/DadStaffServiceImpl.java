package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DadStaff;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DadStaffMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDadStaffService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 员工表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
@Service
public class DadStaffServiceImpl extends DaddyServiceImpl<DadStaffMapper, DadStaff> implements IDadStaffService {

}
