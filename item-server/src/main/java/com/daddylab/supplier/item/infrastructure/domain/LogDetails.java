package com.daddylab.supplier.item.infrastructure.domain;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 属性别名，同一个实体属性在不同场景下可能有不同的字段名，通过这个注解将字段名不同的统一起来
 *
 * <AUTHOR>
 * @since 2022/6/8
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface LogDetails {

    /**
     * 是否记录值的变化
     */
    boolean valueChange() default true;
}
