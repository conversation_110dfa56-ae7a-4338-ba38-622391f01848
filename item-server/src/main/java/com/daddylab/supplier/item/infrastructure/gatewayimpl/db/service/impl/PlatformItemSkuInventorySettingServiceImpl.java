package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSkuInventorySetting;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemSkuInventorySettingMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemSkuInventorySettingService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 平台商品SKU库存设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Service
public class PlatformItemSkuInventorySettingServiceImpl extends DaddyServiceImpl<PlatformItemSkuInventorySettingMapper, PlatformItemSkuInventorySetting> implements IPlatformItemSkuInventorySettingService {

    @Override
    public int saveOrUpdateBatchByOuterId(Collection<PlatformItemSkuInventorySetting> platformItemSkuInventorySettings) {
        return getDaddyBaseMapper().saveOrUpdateBatchByOuterId(platformItemSkuInventorySettings);
    }

    @Override
    public List<PlatformItemSkuInventorySetting> selectByShopNoAndSkuCode(String shopNo, String outerSkuCode) {
        return lambdaQuery()
                .eq(PlatformItemSkuInventorySetting::getShopNo, shopNo)
                .eq(PlatformItemSkuInventorySetting::getSkuCode, outerSkuCode)
                .list();
    }
}
