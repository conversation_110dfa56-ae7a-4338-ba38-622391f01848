package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Category;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 后台类目 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Repository
public interface CategoryMapper extends DaddyBaseMapper<Category> {

    Category getLeafCategory(@Param("itemNo") String itemNo);

    Category getById(@Param("id") Long id);

    int resetPath(@Param("level") int level);

    int replacePath(@Param("beforePath") String beforePath, @Param("afterPath") String afterPath);
}
