package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.daddylab.supplier.item.application.platformItem.query.PlatformItemPageQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemMapper;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 平台商品（投放到其他平台的商品）商品维度 服务类
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
public interface IPlatformItemService
        extends IDaddylabServicePlus<PlatformItem, PlatformItemMapper> {
    /**
     * 分页查询
     *
     * @param pageQuery 分页查询封装
     * @return 平台商品
     */
    IPage<PlatformItem> pageQuery(PlatformItemPageQuery pageQuery);

    /**
     * 获取平台商品信息
     *
     * @param itemId 系统内部的商品 ID
     * @return List<PlatformItem>
     */
    List<PlatformItem> listByItemId(Long itemId);
    
    /**
     * 更新最后分配库存时间
     * @param platformItemIds 平台商品ID
     * @param lastAllocTime 最后分配库存时间
     */
    void updateLastAllocTime(Collection<Long> platformItemIds, Long lastAllocTime);
    
    /**
     * 更新最后同步库存时间
     * @param platformItemIds 平台商品ID
     * @param syncTime 最后同步库存时间
     */
    void updateLastStockSyncTime(Collection<Long> platformItemIds, Long syncTime);
    
    /**
     * 更新最后同步库存时间
     * @param platformItem 平台商品
     */
    void updateLastStockSyncTime(PlatformItem platformItem);
    
    /**
     * 根据店铺编号查询平台商品
     * @param shopNo 店铺编号
     * @return 平台商品
     */
    List<PlatformItem> listByShopNo(String shopNo);
    
    /**
     * 根据外部平台商品ID查询平台商品
     * @param outerItemIds 外部平台商品ID
     * @return 平台商品
     */
    List<PlatformItem> listByOuterItemId(List<String> outerItemIds);
}
