package com.daddylab.supplier.item.controller.stockout;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.salesOutStock.SalesOutStockBizService;
import com.daddylab.supplier.item.application.salesOutStock.dto.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> up
 * @date 2023年10月23日 9:38 AM
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/salesOutStock")
@Api(value = "库存管理-销售出库单相关API", tags = "库存管理-销售出库单相关API")
public class SalesOutStockController {

    @Resource
    SalesOutStockBizService salesOutStockBizService;

    @ApiOperation(value = "列表查询")
    @PostMapping(value = "/queryPage")
    public PageResponse<SalesOutStockPageVO> queryPage(@RequestBody SalesOutStockPageQuery query) {
        return salesOutStockBizService.queryPage(query);
    }

    @ApiOperation(value = "出库单详情")
    @GetMapping(value = "/view")
    public SingleResponse<SalesOutStockDetailVO> view(@ApiParam("出库单id") Long stockoutId) {
        return salesOutStockBizService.viewDetail(stockoutId);
    }

    //---------

    @ApiOperation(value = "库户昵称下拉选")
    @GetMapping(value = "/buyerNick")
    public MultiResponse<BuyerNameDO> buyerNick(@ApiParam("客户昵称") String name) {
        return salesOutStockBizService.getBuyerNickName(name);
    }

    @ApiOperation(value = "物流公司名称下拉选")
    @PostMapping(value = "/logisticsName")
    public PageResponse<String> logisticsName(@RequestBody LogisticsNamePageQuery query) {
        return salesOutStockBizService.getLogisticsName(query);
    }

}
