package com.daddylab.supplier.item.infrastructure.third.kuaishou;

import com.daddylab.supplier.item.infrastructure.third.base.IShopAuthorizationAware;
import com.daddylab.supplier.item.infrastructure.third.enums.StockChangeTypeEnum;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.domain.query.KsItemPageQuery;
import com.kuaishou.merchant.open.api.domain.item.GetItemListResponseParam;
import com.kuaishou.merchant.open.api.domain.item.ItemGetResponseParam;
import com.kuaishou.merchant.open.api.domain.item.SkuInfoResponseParam;

/**
 * <AUTHOR>
 * @class KuaiShouService.java
 * @description 描述类的作用
 * @date 2024-02-28 15:39
 * @see <a href="https://open.kwaixiaodian.com/zone/new/docs/api?name=integration.item.stock.query&version=1">
 * 快手api文档</a>
 */
public interface KuaiShouService extends IShopAuthorizationAware {


    /**
     * 获取商品分页列表
     *
     * @param pageQuery ItemPageQuery
     * @return com.kuaishou.merchant.open.api.domain.item.GetItemListResponseParam
     * @date 2024/2/29 11:34
     * <AUTHOR>
     */
    GetItemListResponseParam getItemList(KsItemPageQuery pageQuery);

    /**
     * 获取商品详情
     *
     * @param itemId Long (同kwaiItemId)
     * @return com.kuaishou.merchant.open.api.domain.item.ItemGetResponseParam
     * @date 2024/2/29 11:36
     * <AUTHOR>
     */
    ItemGetResponseParam getItemDetail(Long itemId);


    /**
     * 获取sku详情
     *
     * @param itemId Long
     * @param skuId  Long
     * @return com.kuaishou.merchant.open.api.domain.item.SkuInfoResponseParam
     * @date 2024/3/7 10:29
     * <AUTHOR>
     */
    SkuInfoResponseParam getSkuDetail(Long itemId, Long skuId);

    /**
     * 更新库存
     *
     * @param itemId     Long 商品ID(同kwaiItemId)
     * @param skuId      Long skuId(同kwaiSkuId)
     * @param changeType StockChangeTypeEnum
     * @param num        Integer 数量
     * @return Boolean
     * @date 2024/2/29 11:49
     * <AUTHOR>
     */
    Boolean skuStockUpdate(Long itemId, Long skuId, StockChangeTypeEnum changeType, Integer num);

    /**
     * 消息解密
     *
     * @param message String
     * @return java.lang.String
     * @date 2024/2/29 16:23
     * <AUTHOR>
     */
    String decode(String message);



}
