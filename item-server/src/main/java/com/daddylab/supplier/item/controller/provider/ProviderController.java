package com.daddylab.supplier.item.controller.provider;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.application.provider.ProviderBizService;
import com.daddylab.supplier.item.controller.provider.dto.*;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.inject.Inject;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/28 4:19 下午
 * @description
 */
@Slf4j
@Api(value = "ProviderController", tags = "供应商相关API")
@RestController
@RequestMapping("/provider")
public class ProviderController {

  @Autowired private ProviderBizService providerService;

  @Inject private OperateLogBizService operateLogBizService;

  @ResponseBody
  @ApiOperation(value = "新建/编辑供应商")
  @PostMapping("/addOrUpdate")
  @Auth(noAuth = true)
  public Response addOrUpdate(@RequestBody @Validated ProviderCmd cmd) {
    providerService.saveOrUpdate(cmd);
    return Response.buildSuccess();
  }

  @ResponseBody
  @ApiOperation(value = "查看供应商")
  @GetMapping("/view")
  public SingleResponse<ProviderVO> view(Long id) {
    return providerService.queryDetail(id);
  }

  @ResponseBody
  @ApiOperation("删除供应商")
  @GetMapping("/remove")
  public Response remove(@ApiParam("供应商id") Long id) {
    providerService.remove(id);
    return Response.buildSuccess();
  }

  @ResponseBody
  @ApiOperation(value = "查看供应商列表")
  @PostMapping("/viewList")
  public PageResponse<ProviderViewVo> viewList(@RequestBody ProviderPageQuery pageQuery) {
    return providerService.queryPage(pageQuery);
  }

  @ResponseBody
  @ApiOperation(value = "根据供应商名字模糊查询合作伙伴系统")
  @PostMapping("/fuzzyQuery")
  public MultiResponse<PartnerProviderVo> fuzzyQuery(
      @Valid @RequestBody PartnerProviderPageQuery pageQuery) {
    return providerService.fuzzyQuery(pageQuery);
  }

  @ResponseBody
  @PostMapping(value = "/dropDown")
  @ApiOperation("供应商下拉选")
  public MultiResponse<ProviderDropDownVo> dropDown(@RequestBody ProviderDropDownCmd cmd) {
    return providerService.dropDownList(cmd);
  }

  @GetMapping(value = "/operateLogs")
  @ApiOperation("操作日志")
  public Response operateLogs(Long targetId) {
    return operateLogBizService.getOperateLogs(OperateLogTarget.PROVIDER, targetId);
  }

  @ResponseBody
  @PostMapping(value = "/contactList")
  @ApiOperation("联系人查询")
  public Response contactList(String name) {
    return providerService.contactList(name);
  }

  @GetMapping(value = "/queryShopDetail")
  @ApiOperation("查询店铺详情")
  public SingleResponse<ProviderShopDetail> queryShopDetail(String mallShopId) {
    return providerService.queryShopDetail(mallShopId);
  }

  @ResponseBody
  @PostMapping(value = "/batchChangeCharger")
  @ApiOperation("批量修改负责人")
  public SingleResponse<Boolean> batchChangeCharger(@Valid @RequestBody BatchChangeChargerCmd cmd) {
    final Boolean aBoolean = providerService.batchChangeCharger(cmd);
    return SingleResponse.of(aBoolean);
  }
}
