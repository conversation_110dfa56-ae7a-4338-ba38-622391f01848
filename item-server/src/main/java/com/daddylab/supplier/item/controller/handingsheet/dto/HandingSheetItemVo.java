package com.daddylab.supplier.item.controller.handingsheet.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/24 15:29
 * @Description: 盘货表关联的商品信息
 */
@Data
@ApiModel("盘货表商品信息出参")
public class HandingSheetItemVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "盘货表和商品信息的关联关系ID")
    private Long handingSheetItemId;

    @ApiModelProperty(value = "商品编码")
    private String itemNo;

    @ApiModelProperty(value = "商品ID")
    private Long itemId;

    @ApiModelProperty(value = "商品标准名")
    private String itemStandardName;

    @ApiModelProperty(value = "规格编码")
    private String itemSkuNo;

    @ApiModelProperty(value = "规格ID")
    private Long itemSkuId;

    @ApiModelProperty(value = "规格名称")
    private String itemSkuName;

    @ApiModelProperty(value = "日销价")
    private BigDecimal dailyPrice;

    @ApiModelProperty(value = "活动价")
    private BigDecimal activePrice;


    @ApiModelProperty(value = "采购成本")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "单买到手价")
    private BigDecimal arrivalPrice;
    @ApiModelProperty(value = "毛利率（绝对值，比如 0.03 就是 3%）")
    private BigDecimal grossProfitMargin;

    @ApiModelProperty(value = "赠品机制")
    private String giftDescription;

    @ApiModelProperty(value = "赠品编码")
    private String giftCode;

    @ApiModelProperty(value = "淘宝链接")
    private String taobaoLink;

    @ApiModelProperty(value = "抖音链接")
    private String douyinLink;

    @ApiModelProperty(value = "小程序链接")
    private String miniProgramLink;

    @ApiModelProperty(value = "备注")
    private String remark;
}
