package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NuoNuoInvoiceProcessRecord;

import java.util.List;

/**
 * <p>
 * 用户诺诺开票流程记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
public interface INuoNuoInvoiceProcessRecordService extends IDaddyService<NuoNuoInvoiceProcessRecord> {


    List<NuoNuoInvoiceProcessRecord> getByStatus(String orderNo, Integer status);

}
