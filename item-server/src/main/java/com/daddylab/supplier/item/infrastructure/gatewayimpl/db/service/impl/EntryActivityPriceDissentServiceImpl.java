package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceDissent;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.EntryActivityPriceDissentMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IEntryActivityPriceDissentService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 入驻活动价格异议 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-06
 */
@Service
public class EntryActivityPriceDissentServiceImpl extends DaddyServiceImpl<EntryActivityPriceDissentMapper, EntryActivityPriceDissent> implements IEntryActivityPriceDissentService {

}
