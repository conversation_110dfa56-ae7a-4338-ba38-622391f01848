package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.ComposeSkuCmd;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuShortVO;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ComposeSku;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
public interface IComposeSkuService extends IDaddyService<ComposeSku> {
    
    List<ComposeSkuShortVO> getShortVo(Long combinationId);
    
    String getShortVoStr(Long combinationId);
    
    void addByComposeSkuCmd(List<ComposeSkuCmd> skuIdList, Long combinationId);
    
    List<ComposeSku> selectByCombinationId(Long combinationId);
    
    List<ComposeSku> selectByCombinationIds(List<Long> combinationIds);
}
