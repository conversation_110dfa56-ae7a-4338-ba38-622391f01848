package com.daddylab.supplier.item.application.saleItem.vo;

/**
 * <AUTHOR> up
 * @date 2022年09月19日 3:30 PM
 */

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("同步列表返回封装")
public class ThirdSyncVO {

    private Long bdId;

    @ApiModelProperty("渠道类型。1微信。2淘宝。3抖店")
    private String platformType;

    private Long itemId;
    private String itemCode;

    @ApiModelProperty("商品名称")
    private String name;

    @ApiModelProperty("创建时间")
    private Long createdAt;

    @ApiModelProperty("创建人")
    private String createdName;

    @ApiModelProperty("同步状态。1.同步进行中。2.同步完成。-1同步异常")
    private Integer state;

    @ApiModelProperty("同步异常原因")
    private String error;

    @ApiModelProperty("发起同步参数")
    private String syncParam;

}
