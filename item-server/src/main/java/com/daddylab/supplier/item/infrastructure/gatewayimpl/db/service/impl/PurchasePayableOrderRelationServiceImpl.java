package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableOrderRelation;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchasePayableOrderRelationMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchasePayableOrderRelationService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 采购应付关联信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-03
 */
@Service
public class PurchasePayableOrderRelationServiceImpl extends DaddyServiceImpl<PurchasePayableOrderRelationMapper, PurchasePayableOrderRelation> implements IPurchasePayableOrderRelationService {

}
