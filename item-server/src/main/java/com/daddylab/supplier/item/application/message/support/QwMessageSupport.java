package com.daddylab.supplier.item.application.message.support;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.application.message.dto.QwMessageDTO;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.message.wechat.RecipientConfig;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import wjb.open.api.utils.StrUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @class QwMessageSupport.java
 * @description 企微消息装饰类
 * @date 2024-04-11 13:47
 */
@Slf4j
@Component
public class QwMessageSupport {

    @Autowired
    private StaffService staffService;
    @Autowired
    private MsgSender msgSender;
    @Autowired
    private RecipientConfig recipientConfig;

    /**
     * 发送企微消息
     *
     * @param qwMessageDTO QwMessageDTO
     * @date 2024/4/11 13:54
     * <AUTHOR>
     */
    public void send(QwMessageDTO qwMessageDTO) {
        List<Long> fileUserIds = qwMessageDTO.getUserIds().stream()
                .filter(userId -> !"prod".equals(SpringUtil.getActiveProfile()) && recipientConfig.getMessageWhiteList().contains(userId))
                .collect(Collectors.toList());
        if (fileUserIds.isEmpty()) {
            log.info("[企微发送消息] 不在白名单中，不发送消息。qwMessageDTO={}", qwMessageDTO);
            return;
        }
        for (List<Long> subUserIds : ListUtil.partition(fileUserIds, 50)) {
            String qwUserIdsStr = subUserIds.stream().map(staffService::getStaff).map(DadStaffVO::getQwUserId).collect(Collectors.joining("|"));
            if (StrUtils.isEmpty(qwUserIdsStr)) {
                log.info("[企微发送消息] 用户信息不存在，不发送消息。qwMessageDTO={}", qwMessageDTO);
                return;
            }
            WechatMsg wechatMsg = WechatMsg.init();
            wechatMsg.setTitle(qwMessageDTO.getTitle());
            wechatMsg.setContent(qwMessageDTO.getContent());
            wechatMsg.setLink(qwMessageDTO.getLink());
            wechatMsg.setRecipient(qwUserIdsStr);

            if (QwMessageDTO.MessageType.CARD.equals(qwMessageDTO.getMessageType())) {
                msgSender.sendTextCardMsg(wechatMsg);
                return;
            }
            msgSender.sendText(wechatMsg);
        }
    }
}
