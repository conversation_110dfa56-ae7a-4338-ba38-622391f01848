package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Brand;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.BrandMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBrandService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 品牌 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class BrandServiceImpl extends DaddyServiceImpl<BrandMapper, Brand> implements IBrandService {

    @Override
    public Collection<String> brandIdsToNos(List<Long> brandIds) {
        return brandIdsToNosMap(brandIds).values();
    }

    private Map<Long, String> brandIdsToNosMap(List<Long> brandIds) {
        return lambdaQuery().in(Brand::getId, brandIds)
                            .select(Brand::getId, Brand::getSn)
                            .list()
                            .stream()
                            .collect(Collectors.toMap(Brand::getId, Brand::getSn));
    }
}
