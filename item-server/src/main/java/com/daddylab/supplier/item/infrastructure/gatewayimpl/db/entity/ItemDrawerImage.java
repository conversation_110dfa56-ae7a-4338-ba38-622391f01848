package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerImageTypeEnum;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <p>
 * 抽屉图片表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class ItemDrawerImage extends Entity {

    private static final long serialVersionUID = 1L;

    /**
     * 抽屉id
     */
    private Long drawerId;

    /**
     * SKU ID
     */
    private Long skuId;

    /**
     * 商品属性ID
     */
    private Long itemAttrId;

    /**
     * 图片地址
     */
    private String url;

    /**
     * 文件名
     */
    private String filename;

    public void setFilename(String filename) {
        //文件名从URL中解析可能经过 UrlEncoded，这边尝试 decode，该工具类遇到无法解码的字符将会跳过
        if (StrUtil.isNotEmpty(filename)) {
            filename = URLUtil.decode(filename);
        }
        this.filename = filename;
    }

    /**
     * 图片类型 1-抽屉商品 2-抽屉详情页 3-规格图片
     *
     * @see ItemDrawerImageTypeEnum
     */
    private ItemDrawerImageTypeEnum type;

    /**
     * 文件类型 1-图片 2-视频
     */
    private Integer fileType;

    /**
     * 视频首图
     */
    private String firstImage;

    /**
     * 视频拓展类型
     */
    private String ext;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 图片宽度
     */
    private Integer width;

    /**
     * 图片高度
     */
    private Integer height;

    /**
     * 图片比例（宽度/高度）
     */
    private BigDecimal proportion;

    /**
     * 直播话术ID
     */
    private Long liveVerbalTrickId;


}
