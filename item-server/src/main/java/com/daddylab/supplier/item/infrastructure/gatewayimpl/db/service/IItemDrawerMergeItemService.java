package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMergeItem;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 新品商品合并审核包商品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
public interface IItemDrawerMergeItemService extends IDaddyService<ItemDrawerMergeItem> {

}
