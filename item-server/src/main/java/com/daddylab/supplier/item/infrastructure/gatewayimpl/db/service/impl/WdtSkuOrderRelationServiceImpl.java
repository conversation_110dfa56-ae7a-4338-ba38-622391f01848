//package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;
//
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSkuOrderRelation;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtSkuOrderRelationMapper;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtSkuOrderRelationService;
//import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
//import org.springframework.stereotype.Service;
//
//import java.util.LinkedList;
//import java.util.List;
//
///**
// * <p>
// * 服务实现类
// * </p>
// *
// * <AUTHOR>
// * @since 2022-04-15
// */
//@Service
//public class WdtSkuOrderRelationServiceImpl extends DaddyServiceImpl<WdtSkuOrderRelationMapper, WdtSkuOrderRelation> implements IWdtSkuOrderRelationService {
//
//    @Override
//    public void batchAddRelation(Long purchaseOrderId, String operateTime, List<WdtOrderDetailWrapper> wrapperList) {
//        List<WdtSkuOrderRelation> list = new LinkedList<>();
//        for (WdtOrderDetailWrapper wrapper : wrapperList) {
//            WdtSkuOrderRelation relation = new WdtSkuOrderRelation();
//            relation.setSkuCode(wrapper.getSkuCode());
//            relation.setTradeId(wrapper.getTradeId());
//            relation.setPurchaseId(purchaseOrderId);
//            list.add(relation);
//        }
//        this.saveBatch(list);
//    }
//}
