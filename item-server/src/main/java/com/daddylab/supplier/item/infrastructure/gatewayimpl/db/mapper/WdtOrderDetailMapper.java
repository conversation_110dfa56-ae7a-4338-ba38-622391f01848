package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.*;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetail;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 旺店通订单明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
public interface WdtOrderDetailMapper extends DaddyBaseMapper<WdtOrderDetail> {


    @Select("select distinct provider_id from wdt_order_detail_wrapper " +
            "where provider_id != 0 and operate_time = #{operateTime} and is_del = 0 and type = 99 ")
    List<Long> getProviderIdStatisticsList(@Param("operateTime") String operateTime);

    Integer getProviderIdCount(@Param("operateTime") String operateTime);

    @Select("select distinct provider_id from wdt_order_detail_wrapper " +
            "where provider_id != 0 and operate_time = #{operateTime} and is_del = 0 ")
    List<Long> getProviderId(@Param("operateTime") String operateTime);


    /**
     * 批量插入或者已存在重复记录时更新数据
     *
     * @param orderDetails 数据集合
     */
    void saveOrUpdateBatch(@Param("orderDetails") Collection<WdtOrderDetail> orderDetails);

    /**
     * 查询当前原始单号关联的旺店通订单ID
     *
     * @param srcTids 明细ID集合
     */
    List<Long> getRefTradeIdsBySrcTids(@Param("srcTids") Collection<String> srcTids);

    @Delete("delete from wdt_order_detail_wrapper where operate_time = #{time}")
    void deleteExpireWrapper(@Param("time") String operateTime);

    @Delete("delete from wdt_order_detail_wrapper_error where operate_time = #{time}")
    void deleteExpireWrapperError(@Param("time") String operateTime);


    // ------------------------------- 2.0 sql分割线 --------------------------------------


    /**
     * 业务sql
     * 查询wrapper中存在时间优惠价的单品数据。出库明细详情的数量
     * 并且保证其下单时间，平台映射等一一对应
     *
     * @param targetMonth 目标时间
     * @return
     */
    List<ActivityTimeSinglePriceDO> selectStockOutSingleTimePurchase(@Param("targetMonth") String targetMonth);

    /**
     * 业务sql
     * 查询wrapper中存在数量优惠的单品信息
     * <p>
     * favourable_type : 0:货抵款 1:按时间供价 2:当月优惠件数'
     * 2022-11-10: 目前将0也暂时归纳到2的通用逻辑进行处理。
     *
     * @param targetMonth
     * @return
     */
    List<ActivityQuantitySingleDO> selectSingleQuantityPurchase(@Param("targetMonth") String targetMonth);


    /**
     * 业务sql
     * 查询wrapper中存在时间优惠价的组合装。出库明细详情的数量
     *
     * @param targetMonth
     * @return
     */
    List<ActivityTimeSuitePriceDO> selectStockOutSuiteTimePurchase(@Param("targetMonth") String targetMonth);

    /**
     * 查询wrapper中存在数量优惠价的组合装信息
     *
     * @param targetMonth
     * @return
     */
    List<ActivityQuantitySuiteDO> selectSuiteQuantityPurchase(@Param("targetMonth") String targetMonth);

    /**
     * 查询指定sku在历史订单中的发货数量
     *
     * @param skuCode
     * @param tradeStartTime 支付开始时间
     * @param tradeEndTime   支付结束时间
     * @param consignTime    发货截止时间
     * @return
     */
    List<HistoryQuantitySingleDO> selectSingleHistoryQuantityDO(@Param("skuCode") String skuCode,
                                                                @Param("tradeStartTime") String tradeStartTime,
                                                                @Param("tradeEndTime") String tradeEndTime,
                                                                @Param("consignLineTime") String consignTime);

//    List<HistoryQuantitySingleDO> selectSuiteHistoryQuantityDO(@Param("suiteNo") String suiteNo,
//                                                                @Param("tardeStartTime") String tradeStartTime,
//                                                                @Param("tardeEndTime") String tradeEndTime,
//                                                                @Param("consignLineTime") String consignTime);

    List<StatisticsQuantityByTradeNoDO> statisticsCountByTradeNo(@Param("skuCode") String skuCode,
                                                                 @Param("skuCodeList") List<String> skuCodeList,
                                                                 @Param("activityStartTime") Long activityStartTime,
                                                                 @Param("activityEndTime") Long activityEndTime,
                                                                 @Param("operateTime") String operateTime);


    List<WdtOrderDetail> getOneByTradeIdAndSpecNo(@Param("tradeId") Long tradeId, @Param("specNo") String specNo);


    void deleteByTypeAndMonth(@Param("operateTime") String operateTime, @Param("type") Integer type);

    List<Long> selectTradeIdsBySrcTids(@Param("srcTids") Collection<String> srcTids);

    int deleteByTradeIds(@Param("tradeIds") Collection<Long> tradeIds);
}
