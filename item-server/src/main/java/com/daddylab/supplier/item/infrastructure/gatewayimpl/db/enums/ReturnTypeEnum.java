package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ReturnTypeEnum {

    /**
     * 退料类型
     */
    STOCK_RETURN(1, "库存退料"),
    ;


    private final Integer value;
    private final String desc;


    public static String getDesc(Integer type) {
        for(ReturnTypeEnum returnTypeEnum : ReturnTypeEnum.values()) {
            if (returnTypeEnum.getValue().equals(type)) {
                return returnTypeEnum.getDesc();
            }
        }
        return "";
    }

}
