package com.daddylab.supplier.item.controller.item.dto;

import com.alibaba.cola.dto.PageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> up
 * @date 2022年09月23日 10:01 AM
 */
@ApiModel("同步列表筛选")
@EqualsAndHashCode(callSuper = true)
@Data
public class ThirdSyncPageQuery extends PageQuery {

    private static final long serialVersionUID = 6280025559682843202L;

    @ApiModelProperty("平台类型。WE_CHAT,TAO_BAO,DOU_DIAN")
    private PlatformType platformType;

}
