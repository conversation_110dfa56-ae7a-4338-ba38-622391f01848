package com.daddylab.supplier.item.application.platformItem;

import com.daddylab.supplier.item.application.platformItem.config.PlatformItemSyncConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryAllocShop;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryAllocShopStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IInventoryAllocService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IInventoryAllocShopService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class InventorySyncBizServiceImpl implements InventorySyncBizService {
    @Autowired
    private IInventoryAllocService inventoryAllocService;
    @Autowired
    private IPlatformItemService platformItemService;
    @Autowired
    private IInventoryAllocShopService inventoryAllocShopService;
    @Autowired
    private PlatformItemStockSyncBizService platformItemStockSyncBizService;
    @Autowired
    private PlatformItemSyncConfig config;
    @Autowired
    private RedissonClient redissonClient;
    
    @Override
    public void syncToPlatformJob() {
        if (!config.isInventoryAutoUploadEnabled()) {
            log.info("[库存分配][自动同步]已关闭");
            return;
        }
        List<InventoryAllocShop> list = inventoryAllocShopService.lambdaQuery()
                .eq(InventoryAllocShop::getStatus, InventoryAllocShopStatus.ACTIVE).list();
        syncToPlatformJob(list);
    }
    
    @Override
    public void syncToPlatformJob(List<InventoryAllocShop> list) {
        if (!config.isInventoryAutoUploadEnabled()) {
            log.info("[库存分配][自动同步]已关闭");
            return;
        }
        for (InventoryAllocShop inventoryAllocShop : list) {
            RLock lock = redissonClient.getLock("inventorySync:push:" + inventoryAllocShop.getShopNo());
            if (lock.tryLock()) {
                try {
                    List<PlatformItem> platformItems = platformItemService.listByShopNo(inventoryAllocShop.getShopNo());
                    for (PlatformItem platformItem : platformItems) {
                        platformItem.setSyncEnabledByShopSetting(inventoryAllocShop);
                        if (!platformItem.getSyncEnabled()) {
                            continue;
                        }
                        try {
                            platformItemStockSyncBizService.syncStock(platformItem);
                        } catch (Exception e) {
                            log.error("[库存分配][自动同步]同步异常，平台商品ID:{}，错误:{}", platformItem.getId(),
                                    e.getMessage(), e);
                        }
                    }
                } catch (Exception e) {
                    log.error("[库存分配][自动同步]同步异常，店铺:{}，错误:{}", inventoryAllocShop.getShopNo(),
                            e.getMessage(), e);
                } finally {
                    lock.unlock();
                }
            } else {
                log.info("[库存分配][自动同步]未能获取锁，跳过店铺:{}", inventoryAllocShop.getShopNo());
            }
        }
    }
}
