package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ReqData;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ReviewAddHitNumParam;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ReviewThesaurus;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "PartnerReviewThesaurusAPI",
        url = "${partner-review-thesaurus-server.url}",
        configuration = PartnerReviewThesaurusServerConfiguration.class,
        fallbackFactory = PartnerReviewThesaurusAPIFallbackFactory.class
)
public interface PartnerReviewThesaurusAPI {

    /**
     * 审核词库返回全部词条
     */
    @GetMapping(path = "/dpm/bms/openapi/review_thesaurus/all")
    Rsp<List<ReviewThesaurus>> reviewThesaurusAll();

    /**
     * 审核词库返回全部词条
     * @param itemNo P系统商品款号
     */
    @GetMapping(path = "/dpm/bms/openapi/review_thesaurus/all")
    Rsp<List<ReviewThesaurus>> reviewThesaurusAll(@RequestParam("itemNo") String itemNo);

    /**
     * 审核词库增加命中次数
     */
    @PostMapping(path = "/dpm/bms/openapi/review_thesaurus/add_hit_num", consumes = MediaType.APPLICATION_JSON_VALUE)
    @FeignLog(type = FeignLog.PARTNER_SERVER_REVIEW_THESAURUS_ADD_HIT_NUM)
    Rsp<Void> reviewAddHitNum(ReqData<List<ReviewAddHitNumParam>> params);
}
