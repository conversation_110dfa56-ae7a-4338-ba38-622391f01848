package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Shop;

import java.util.Optional;

/**
 * <p>
 * 店铺 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface IShopService extends IDaddyService<Shop> {

    Optional<Shop> getBySn(String shopNo);
    
    Optional<Shop> getByAccountId(String accountId);
}
