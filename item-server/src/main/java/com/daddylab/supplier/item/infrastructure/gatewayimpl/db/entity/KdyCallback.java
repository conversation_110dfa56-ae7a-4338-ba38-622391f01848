package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.KdyLogisticsStatus;
import com.daddylab.supplier.item.types.kuaidaoyun.KdyTrackList;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Optional;

/**
 * <p>
 * 快刀云订阅记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class KdyCallback implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Long isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 物流公司
     */
    private String logisticsCompanyName;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 回调快递状态 2 代签收，1 已签收， 0 未签收， -1 无记录， -3 两天以上无记录（顺丰一天以上无记录）， -4 六天以上未签收（顺丰四天以上未签收）， -5 跟踪记录里面有疑难件关键字（疑难件，留仓库，问题件，退回等）， -6 三天以上没有新的进展， -18 派送中
     */
    private KdyLogisticsStatus status;

    /**
     * 快递跟踪信息的json格式数据
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private KdyTrackList tracklist;

    /**
     * 自定义回调参数
     */
    private String callbackState;

    /**
     * 包裹状态最后更新时间（毫秒！）
     */
    private Long trackTime;

    /**
     * 快递状态更新时间（秒级）
     */
    public Long getTrackTimeSeconds() {
        return Optional.ofNullable(trackTime).map(it -> it / 1000L).orElse(null);
    }
}
