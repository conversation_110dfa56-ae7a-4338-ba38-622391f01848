package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.CommonCalculator;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.ActivityQuantitySingleDO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.ActivityQuantitySuiteDO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto.HistoryQuantitySingleDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 采购价格活动-根据数量优惠。
 *
 * <AUTHOR> up
 * @date 2022年10月21日 3:38 PM
 */
@Slf4j
@Service
public class ActivityQuantityPrice extends BaseProcess implements PriceProcessor {

    @Resource
    CommonCalculator commonCalculator;


    /**
     * @param targetMonth 目标月份
     * @return
     */
    @Override
    public Boolean doPriceProcess(TimeBO targetMonth) {
        log.info("ActivityQuantityPrice doPriceProcess start");

        try {
            singleHandler(targetMonth);
            suiteHandler(targetMonth);
        } catch (Exception e) {
            log.error("ActivityQuantityPrice doPriceProcess error", e);
        }

        log.info("ActivityQuantityPrice doPriceProcess finish");
        return true;
    }

    /**
     * 单品维度处理 数量优惠
     *
     * @param targetMonth
     */
    private void singleHandler(TimeBO targetMonth) {
        // 查询出和单品关联的采购活动价（按数量进行优惠）。
        List<ActivityQuantitySingleDO> list = wdtOrderDetailMapper.selectSingleQuantityPurchase(targetMonth.getOperateMonth());
        if (CollUtil.isEmpty(list)) {
            return;
        }

        list.forEach(activityQuantitySingleDo -> {
            try {

                // 拿发货时间在当月的sku去碰撞活动价数据（按数量优惠），发现存在N条如下数据
                // 比如skuA，发货时间在11月，但是支付时间在10月，并且这个品有个数量50的优惠的采购活动价，活动时间范围是整个10月。
                // 那算skuA价格的时候，就必须考虑10月份的时候，skuA出库了多少。看是否达到50。如果到了50，那这N条数据就不需要走采购活动优惠计算
                // 如果没到50，那差值就必须得走采购活动按优惠计算了，所以，这种情况下，要查询历史订单数据来判断。
                AtomicInteger historyCount = new AtomicInteger(0);
                // 进入if分支条件：此sku的支付时间能和某个数量优惠采购活动匹配，并且这个活动的结束时间已经小于当前月了。是历史了。
                if (activityQuantitySingleDo.getEndTime() <= targetMonth.getMonthStartTime()) {
                    // 历史订单数据筛选条件：1.支付时间在此活动时间范围之内。
                    //                   2.发货时间小于当前时间，已完成历史发货。
                    List<HistoryQuantitySingleDO> historyQuantitySingleDdList = wdtOrderDetailMapper.selectSingleHistoryQuantityDO(
                            activityQuantitySingleDo.getSkuCode(),
                            DateUtil.parseTimeStamp(activityQuantitySingleDo.getStartTime(), "yyyy-MM-dd HH:mm:ss"),
                            DateUtil.parseTimeStamp(activityQuantitySingleDo.getEndTime(), "yyyy-MM-dd HH:mm:ss"),
                            targetMonth.getMonthStart());

                    // 全平台通用
                    if (activityQuantitySingleDo.getPlatformType() == 0) {
                        // 累计所有平台的数据为历史数据
                        BigDecimal historyReduce = historyQuantitySingleDdList.stream().map(HistoryQuantitySingleDO::getSumNum)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        historyCount.set(historyReduce.intValue());
                    }
                    // 做平台对应
                    else {
                        historyQuantitySingleDdList.stream().filter(val -> {
                            Integer erpPurchasePricePlatformType = commonUtil.getErpPurchasePricePlatformType(val.getPlatformId());
                            return erpPurchasePricePlatformType.equals(activityQuantitySingleDo.getPlatformType());
                        }).findFirst().ifPresent(val -> historyCount.set(val.getSumNum().intValue()));
                    }
                    log.info("ActivityQuantityPrice singleHandler historyCount:{},skuCode:{}",
                            historyCount.get(), activityQuantitySingleDo.getSkuCode());
                }

                // 根据历史已处理的结果，来处理当月的情况
                // 活动数量 - 历史数量，仍然大于0，才需要进行价格更新处理。
                int stillNeedUpdateCount = (activityQuantitySingleDo.getNumCost().intValue() - historyCount.get());
                if (stillNeedUpdateCount > 0) {
                    List<Long> updateIds = new LinkedList<>();
                    int count = 0, offset = 0, size = 100;
                    List<WdtOrderDetailWrapper> wrapperList = iWdtOrderDetailWrapperService.lambdaQuery()
                            .eq(WdtOrderDetailWrapper::getSkuCode, activityQuantitySingleDo.getSkuCode())
                            .eq(activityQuantitySingleDo.getPlatformType() != 0, WdtOrderDetailWrapper::getPlatformType, activityQuantitySingleDo.getPlatformType())
                            .eq(WdtOrderDetailWrapper::getType, 1)
                            .between(WdtOrderDetailWrapper::getPayTime, activityQuantitySingleDo.getStartTime(), activityQuantitySingleDo.getEndTime())
                            .orderByAsc(WdtOrderDetailWrapper::getId).last("limit " + offset + "," + size).list();
                    loop:
                    while (CollUtil.isNotEmpty(wrapperList)) {
                        for (WdtOrderDetailWrapper wdtOrderDetailWrapper : wrapperList) {
                            count = count + wdtOrderDetailWrapper.getQuantity();
                            // 数量没有达标，继续添加，数量继续累加
                            if (count < stillNeedUpdateCount) {
                                updateIds.add(wdtOrderDetailWrapper.getId());
                            }
                            // 数量正好达标，退出外部大循环，更新数据就好
                            else if (count == stillNeedUpdateCount) {
                                updateIds.add(wdtOrderDetailWrapper.getId());
                                break loop;
                            } else {
                                // 1.wrapper剩余数量，这部分wrapper的价格不走活动优惠价，wrapper原本的数量更新为此数量
                                int remainQuantity = count - stillNeedUpdateCount;
                                // 2.wrapper补全数量，这部分价格走活动优惠价，并且新建一条wrapper数量。
                                int complementQuantity = wdtOrderDetailWrapper.getQuantity() - remainQuantity;
                                commonUtil.splitWrapper(wdtOrderDetailWrapper, remainQuantity,
                                        complementQuantity, activityQuantitySingleDo.getPriceCost());
                                break loop;
                            }
                        }
                        offset = offset + size;
                        wrapperList = iWdtOrderDetailWrapperService.lambdaQuery()
                                .eq(WdtOrderDetailWrapper::getSkuCode, activityQuantitySingleDo.getSkuCode())
                                .eq(activityQuantitySingleDo.getPlatformType() != 0, WdtOrderDetailWrapper::getPlatformType, activityQuantitySingleDo.getPlatformType())
                                .eq(WdtOrderDetailWrapper::getType, 1)
                                .between(WdtOrderDetailWrapper::getPayTime, activityQuantitySingleDo.getStartTime(), activityQuantitySingleDo.getEndTime())
                                .orderByAsc(WdtOrderDetailWrapper::getId).last("limit " + offset + "," + size).list();
                    }

                    if (CollUtil.isNotEmpty(updateIds)) {
                        boolean update = iWdtOrderDetailWrapperService.lambdaUpdate()
                                .set(WdtOrderDetailWrapper::getPrice, activityQuantitySingleDo.getPriceCost())
                                .in(WdtOrderDetailWrapper::getId, updateIds).update();
                        if (update) {
                            saveSkuPriceLog(activityQuantitySingleDo.getSkuCode(), activityQuantitySingleDo.getPriceCost(),
                                    targetMonth.getOperateMonth(), "单品采购活动价数量优惠");
                        }
                    }
                }
            } catch (Exception e) {
                log.error("ActivityQuantityPrice doPriceProcess singleHandler update sku price fail.val:{}", activityQuantitySingleDo, e);
            }

        });

    }


    /**
     * 组合装维度处理 数量优惠
     *
     * @param timeBO
     */
    private void suiteHandler(TimeBO timeBO) {
        List<ActivityQuantitySuiteDO> list = wdtOrderDetailMapper.selectSuiteQuantityPurchase(timeBO.getOperateMonth());
        if (CollUtil.isEmpty(list)) {
            return;
        }

        // 目前组合装暂时不考虑跨月历史订单的数量对当前活动的影响。
        // 当然，目前这种跨月的数量优惠采购活动也不多，几乎没有。
        list.forEach(activityQuantitySuiteDo ->
        {
            try {
                List<CommonCalculator.SkuPriceUnderSuiteNo> skuCostPriceUnderSuiteNo = commonCalculator.getSkuCostPriceUnderSuiteNo(activityQuantitySuiteDo.getSuiteNo()
                        , activityQuantitySuiteDo.getPriceCost());
                skuCostPriceUnderSuiteNo.forEach(skuPrice -> {
                    int count = activityQuantitySuiteDo.getNumCost().intValue() * skuPrice.getCount();
                    boolean update = iWdtOrderDetailWrapperService.lambdaUpdate().set(WdtOrderDetailWrapper::getPrice, skuPrice.getUnitPrice())
                            .eq(WdtOrderDetailWrapper::getSuiteNo, activityQuantitySuiteDo.getSuiteNo())
                            .eq(WdtOrderDetailWrapper::getSkuCode, skuPrice.getSkuCode())
                            .eq(activityQuantitySuiteDo.getPlatformType() != 0,
                                    WdtOrderDetailWrapper::getPlatformType, activityQuantitySuiteDo.getPlatformType())
                            .eq(WdtOrderDetailWrapper::getType, 2)
                            .between(WdtOrderDetailWrapper::getPayTime, activityQuantitySuiteDo.getStartTime()
                                    , activityQuantitySuiteDo.getEndTime())
                            .orderByAsc(WdtOrderDetailWrapper::getId)
                            .last("limit " + count)
                            .update();
                    if (update) {
                        saveSkuPriceLog(skuPrice.getSkuCode(), skuPrice.getUnitPrice(), timeBO.getOperateMonth(), "组合装采购活动价数量优惠");
                    }
                });
            } catch (Exception e) {
                log.error("ActivityQuantityPrice doPriceProcess suiteHandler update sku price fail.val:{}", activityQuantitySuiteDo, e);
            }
        });


    }

}
