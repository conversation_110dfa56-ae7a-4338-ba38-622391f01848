package com.daddylab.supplier.item.infrastructure.submail;

import com.daddylab.supplier.item.infrastructure.submail.config.AppConfig;
import com.daddylab.supplier.item.infrastructure.submail.lib.MessageSend;
import com.daddylab.supplier.item.infrastructure.submail.lib.MessageXsend;
import com.daddylab.supplier.item.infrastructure.submail.lib.base.SenderWapper;
import com.daddylab.supplier.item.infrastructure.submail.utils.ConfigLoader;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * 赛邮云-短信
 *
 * @Auther: <EMAIL>
 * @version: 1.0.0
 * @Date: 2021/04/16/4:01 下午
 */
@Service
@Slf4j
public class SubMailService {

    public void send(String mobile, String content) throws SubMailException {
        String resStr;
        AppConfig config = ConfigLoader.load(ConfigLoader.ConfigType.Message);
        try {
            MessageSend request = new MessageSend(config);
            request.addTo(mobile);
            request.addContent(SubmailConstants.S_MSG_SEND_TITLE + content);
            resStr = send(request);
        } catch (Exception e) {
            log.error("赛邮云短信发送异常", e);
            throw new SubMailException("赛邮云短信发送异常:" + e.getMessage());
        }
        checkResponse(resStr);
    }

    public void send(String mobile, SMSTemplate smsTemplate, Map<String, Object> params) throws SubMailException {
        AppConfig config = ConfigLoader.load(ConfigLoader.ConfigType.Message);
        String resStr;
        try {
            MessageXsend request = new MessageXsend(config);
            request.addTo(mobile);
            request.setProject(smsTemplate.getValue());
            for (String key : params.keySet()) {
                Object o = params.get(key);
                request.addVar(key, String.valueOf(o));
            }
            resStr = send(request);
        } catch (Exception e) {
            log.error("赛邮云短信发送异常", e);
            throw new SubMailException("赛邮云短信发送异常:" + e.getMessage());
        }
        checkResponse(resStr);
    }

    private void checkResponse(String resStr) throws SubMailException {
        SubMailMsgsendResponse response = JsonUtil.parse(resStr, SubMailMsgsendResponse.class);
        if (Objects.isNull(response)) {
            log.error("赛邮云短信发送失败,响应数据异常,response:{}", resStr);
            throw new SubMailException("赛邮云短信发送失败,响应数据异常:" + resStr);
        }

        if (!Objects.equals(SubmailConstants.S_SUCCESS, response.getStatus())) {
            log.error("赛邮云短信发送失败,response:{}", resStr);
            throw new SubMailException("赛邮云短信发送失败:" + response.getMsg());
        }
    }

    /**
     * 发送
     */
    private String send(SenderWapper send) {
        if (send instanceof MessageSend) {
            return ((MessageSend) send).send();
        }
        if (send instanceof MessageXsend) {
            return ((MessageXsend) send).xsend();
        }
        return null;
    }
}
