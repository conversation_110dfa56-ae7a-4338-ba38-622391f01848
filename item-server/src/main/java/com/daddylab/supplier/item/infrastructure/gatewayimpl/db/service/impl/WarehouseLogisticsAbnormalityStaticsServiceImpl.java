package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WarehouseLogisticsAbnormalityStatics;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WarehouseLogisticsAbnormalityStaticsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWarehouseLogisticsAbnormalityStaticsService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 仓库异常物流信息统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
@Service
public class WarehouseLogisticsAbnormalityStaticsServiceImpl extends DaddyServiceImpl<WarehouseLogisticsAbnormalityStaticsMapper, WarehouseLogisticsAbnormalityStatics> implements IWarehouseLogisticsAbnormalityStaticsService {

}
