package com.daddylab.supplier.item.application.drawer.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.drawer.ItemDrawerMergeBizService;
import com.daddylab.supplier.item.application.drawer.ItemDrawerService;
import com.daddylab.supplier.item.application.drawer.events.ItemDrawerMergeEvent;
import com.daddylab.supplier.item.application.drawer.impl.copySupport.ItemDrawerBaseCopyServiceImpl;
import com.daddylab.supplier.item.application.drawer.impl.copySupport.ItemDrawerLiveVerbalCopyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMerge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMergeItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerMergeItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerMergeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @class ItemDrawerMergeBizServiceImpl.java
 * @description 描述类的作用
 * @date 2024-04-08 15:51
 */
@Service
public class ItemDrawerMergeBizServiceImpl implements ItemDrawerMergeBizService {

    @Autowired
    private IItemDrawerMergeService itemDrawerMergeService;
    @Autowired
    private IItemDrawerMergeItemService itemDrawerMergeItemService;
    @Autowired
    private ItemDrawerService itemDrawerService;
    @Resource
    private ItemDrawerLiveVerbalCopyServiceImpl itemDrawerCopyService;
    @Resource
    private ItemDrawerBaseCopyServiceImpl itemDrawerBaseCopyService;

    @Override
    public ItemDrawerMerge getItemDrawerMerge(Long itemId) {
        ItemDrawerMergeItem itemDrawerMergeItem = getItemDrawerMergeItem(itemId);
        if (Objects.isNull(itemDrawerMergeItem)) {
            return null;
        }
        return itemDrawerMergeService.getById(itemDrawerMergeItem.getMergeId());
    }


    @Override
    public ItemDrawerMerge getItemDrawerMergeById(Long id) {
        return itemDrawerMergeService.getById(id);
    }

    @Override
    public ItemDrawerMergeItem getItemDrawerMergeItem(Long itemId) {
        LambdaQueryWrapper<ItemDrawerMergeItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ItemDrawerMergeItem::getItemId, itemId);
        return itemDrawerMergeItemService.getOne(queryWrapper);
    }

    @Override
    public List<ItemDrawerMergeItem> getItemDrawerMergeItemList(List<Long> itemIds) {
        LambdaQueryWrapper<ItemDrawerMergeItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(ItemDrawerMergeItem::getItemId, itemIds);
        return itemDrawerMergeItemService.list(queryWrapper);
    }

    @Override
    public void doSave(Long itemDrawerId, Long itemId, List<Long> mergeItemIds) {
        ItemDrawerMerge itemDrawerMerge = new ItemDrawerMerge();
        itemDrawerMerge.setItemDrawerId(itemDrawerId);
        itemDrawerMerge.setItemId(itemId);
        itemDrawerMergeService.save(itemDrawerMerge);

        doAddMergeItem(itemDrawerMerge.getId(), mergeItemIds);

        SpringUtil.publishEvent(new ItemDrawerMergeEvent(this, itemDrawerMerge.getId()));
    }

    @Override
    public List<ItemDrawerMergeItem> getItemDrawerMergeItemListByMergeId(Long mergeId) {
        LambdaQueryWrapper<ItemDrawerMergeItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ItemDrawerMergeItem::getMergeId, mergeId);
        return itemDrawerMergeItemService.list(queryWrapper);
    }

    @Override
    public void disbandPackage(ItemDrawerMergeItem itemDrawerMergeItem) {
        //判断当前是否除了自己是否还有其他关联关系
        LambdaQueryWrapper<ItemDrawerMergeItem> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ItemDrawerMergeItem::getMergeId, itemDrawerMergeItem.getMergeId());
        List<ItemDrawerMergeItem> otherItemDrawerMergeItems = itemDrawerMergeItemService.list(queryWrapper);
        for (ItemDrawerMergeItem otherItemDrawerMergeItem : otherItemDrawerMergeItems) {
            itemDrawerMergeItemService.removeById(otherItemDrawerMergeItem.getId());
        }
        itemDrawerMergeService.removeById(itemDrawerMergeItem.getMergeId());
    }

    @Override
    public void removeItemByIds(Long mergeId, List<Long> itemIds) {
        LambdaQueryWrapper<ItemDrawerMergeItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ItemDrawerMergeItem::getMergeId, mergeId)
                    .in(ItemDrawerMergeItem::getItemId, itemIds);
        itemDrawerMergeItemService.remove(queryWrapper);
    }

    @Override
    public void doAddMergeItem(Long mergeId, List<Long> itemIds) {
        final List<ItemDrawerMergeItem> itemDrawerMergeItemsLocal = itemDrawerMergeItemService
                .lambdaQuery().eq(ItemDrawerMergeItem::getMergeId, mergeId).list();
        final Set<Long> itemIdsLocal = itemDrawerMergeItemsLocal.stream()
                                                                .map(ItemDrawerMergeItem::getItemId)
                                                                .collect(Collectors.toSet());
        List<ItemDrawerMergeItem> itemDrawerMergeItems = itemIds
                .stream()
                .filter(v -> !itemIdsLocal.contains(v))
                .map(i -> {
                    ItemDrawerMergeItem itemDrawerMergeItem = new ItemDrawerMergeItem();
                    itemDrawerMergeItem.setMergeId(mergeId);
                    itemDrawerMergeItem.setItemId(i);
                    return itemDrawerMergeItem;
                })
                .collect(Collectors.toList());
        itemDrawerMergeItemService.saveBatch(itemDrawerMergeItems);
    }

    @Override
    @Transactional
    public void doCopy(Long itemDrawerMergeId) {
        ItemDrawerMerge itemDrawerMerge = itemDrawerMergeService.getById(itemDrawerMergeId);
        if (Objects.isNull(itemDrawerMerge)) {
            return;
        }
        List<ItemDrawerMergeItem> itemDrawerMergeItems = getItemDrawerMergeItemListByMergeId(itemDrawerMergeId);
        List<Long> itemIds = itemDrawerMergeItems.stream()
                                                 .map(ItemDrawerMergeItem::getItemId)
                                                 .collect(Collectors.toList());
        itemDrawerService.copyDrawerToItemIds(itemDrawerMerge.getItemDrawerId(), itemIds);
    }

    @Override
    public void doCopyStatus(Long itemDrawerMergeId) {
        ItemDrawerMerge itemDrawerMerge = itemDrawerMergeService.getById(itemDrawerMergeId);
        if (Objects.isNull(itemDrawerMerge)) {
            return;
        }
        List<ItemDrawerMergeItem> itemDrawerMergeItems = getItemDrawerMergeItemListByMergeId(itemDrawerMergeId);
        List<Long> itemIds = itemDrawerMergeItems.stream()
                .map(ItemDrawerMergeItem::getItemId)
                .collect(Collectors.toList());
        itemDrawerService.copyStatus(itemDrawerMerge.getItemId(), itemIds);
    }

    @Override
    public void doCopy(Long itemDrawerMergeId, List<Long> itemIds) {
        ItemDrawerMerge itemDrawerMerge = itemDrawerMergeService.getById(itemDrawerMergeId);
        if (Objects.isNull(itemDrawerMerge)) {
            return;
        }
        itemDrawerService.copyDrawerToItemIds(itemDrawerMerge.getItemDrawerId(), itemIds);
    }

    @Override
    public void update(ItemDrawerMerge itemDrawerMerge) {
        itemDrawerMergeService.updateById(itemDrawerMerge);
    }

    public void copyConsumerWithItem(Long itemId, BiConsumer<ItemDrawer, ItemDrawer> consumer) {
        ItemDrawerMerge itemDrawerMerge = getItemDrawerMerge(itemId);
        if (Objects.isNull(itemDrawerMerge)) {
            return;
        }
        List<ItemDrawerMergeItem> itemDrawerMergeItems = getItemDrawerMergeItemListByMergeId(itemDrawerMerge.getId());
        ItemDrawer itemDrawer = itemDrawerService.getItemDrawer(itemId);
        if (Objects.isNull(itemDrawer)) {
            return;
        }
        itemDrawerMergeItems.stream()
                            .filter(itemDrawerMergeItem -> !itemDrawerMergeItem.getItemId().equals(itemId))
                            .forEach(itemDrawerMergeItem -> {
                                ItemDrawer targetDrawer = itemDrawerService.getItemDrawer(itemDrawerMergeItem.getItemId());
                                if (Objects.isNull(targetDrawer)) {
                                    return;
                                }
                                consumer.accept(itemDrawer, targetDrawer);
                            });
    }


    @Override
    public void copyConsumerWithItemDrawer(Long itemDrawerId, BiConsumer<ItemDrawer, ItemDrawer> consumer) {
        ItemDrawer itemDrawer = itemDrawerService.getById(itemDrawerId);
        if (Objects.isNull(itemDrawer)) {
            return;
        }
        ItemDrawerMerge itemDrawerMerge = getItemDrawerMerge(itemDrawer.getItemId());
        if (Objects.isNull(itemDrawerMerge)) {
            return;
        }
        List<ItemDrawerMergeItem> itemDrawerMergeItems = getItemDrawerMergeItemListByMergeId(itemDrawerMerge.getId());
        itemDrawerMergeItems.stream()
                            .filter(itemDrawerMergeItem -> !itemDrawerMergeItem.getItemId()
                                                                               .equals(itemDrawer.getItemId()))
                            .forEach(itemDrawerMergeItem -> {
                                ItemDrawer targetDrawer = itemDrawerService.getItemDrawer(itemDrawerMergeItem.getItemId());
                                if (Objects.isNull(targetDrawer)) {
                                    return;
                                }
                                consumer.accept(itemDrawer, targetDrawer);
                            });
    }

    @Override
    public List<Long> mainItemIdsToMergeItemIds(Collection<Long> itemIds) {
        if (CollUtil.isEmpty(itemIds)) {
            return Collections.emptyList();
        }
        final List<ItemDrawerMerge> list = itemDrawerMergeService.lambdaQuery()
                                                                 .in(ItemDrawerMerge::getItemId, itemIds)
                                                                 .list();
        if (list.isEmpty()) {
            return new ArrayList<>(itemIds);
        }
        final List<Long> mergeIds = list.stream().map(ItemDrawerMerge::getId).collect(
                Collectors.toList());
        final Set<Long> mergedItemIds = itemDrawerMergeItemService.lambdaQuery()
                                                            .in(ItemDrawerMergeItem::getMergeId, mergeIds)
                                                            .list()
                                                            .stream()
                                                            .map(ItemDrawerMergeItem::getItemId)
                                                            .collect(Collectors.toSet());
        mergedItemIds.addAll(itemIds);
        return new ArrayList<>(mergedItemIds);
    }

    @Override
    public Map<Long, Long> getMainItemIdBatch(Collection<Long> itemIds) {
        if (CollUtil.isEmpty(itemIds)) {
            return Collections.emptyMap();
        }
        final List<ItemDrawerMergeItem> itemDrawerMergeItems = itemDrawerMergeItemService.lambdaQuery()
                                                                                         .in(ItemDrawerMergeItem::getItemId,
                                                                                                 itemIds)
                                                                                         .list();
        if (itemDrawerMergeItems.isEmpty()) {
            return Collections.emptyMap();
        }
        final List<Long> mergeIds = itemDrawerMergeItems.stream()
                                                        .map(ItemDrawerMergeItem::getMergeId)
                                                        .distinct()
                                                        .collect(Collectors.toList());
        final List<ItemDrawerMerge> itemDrawerMerges = itemDrawerMergeService.listByIds(mergeIds);
        final Map<Long, Long> mergeMainItemIdsMap = itemDrawerMerges.stream()
                                                                   .collect(Collectors.toMap(ItemDrawerMerge::getId,
                                                                           ItemDrawerMerge::getItemId));
        final HashMap<Long, Long> map = new HashMap<>();
        for (Long itemId : itemIds) {
            itemDrawerMergeItems.stream()
                                .filter(item -> Objects.equals(item.getItemId(), itemId))
                                .findAny()
                                .map(ItemDrawerMergeItem::getMergeId)
                                .filter(mergeMainItemIdsMap::containsKey)
                                .map(mergeMainItemIdsMap::get)
                                .ifPresent(mainItemId -> map.put(itemId, mainItemId));
        }
        return map;
    }

    @Override
    public void doMerge(Long drawerId, Long itemId, List<Long> itemIds) {
        ItemDrawerMergeItem itemDrawerMergeItem = getItemDrawerMergeItem(itemId);
        if (itemDrawerMergeItem == null) {
            doSave(drawerId, itemId, itemIds);
        } else {
            doAddMergeItem(itemDrawerMergeItem.getMergeId(), itemIds);
        }
    }
}
