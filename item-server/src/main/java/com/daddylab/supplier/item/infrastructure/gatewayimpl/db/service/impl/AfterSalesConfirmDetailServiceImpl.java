package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesConfirmDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AfterSalesConfirmDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesConfirmDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 售后单销退确认单据明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
@Service
public class AfterSalesConfirmDetailServiceImpl extends DaddyServiceImpl<AfterSalesConfirmDetailMapper, AfterSalesConfirmDetail> implements IAfterSalesConfirmDetailService {

}
