package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtLogisticsTrace;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtLogisticsTraceMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtLogisticsTraceService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 旺店通物流轨迹 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-28
 */
@Service
public class WdtLogisticsTraceServiceImpl extends DaddyServiceImpl<WdtLogisticsTraceMapper, WdtLogisticsTrace> implements IWdtLogisticsTraceService {

    @Override
    public Map<String, Long> mapIdByLogisticsNos(Collection<String> logisticsNos) {
        if (logisticsNos == null || logisticsNos.isEmpty()) {
            return Collections.emptyMap();
        }
        return lambdaQuery()
                .in(WdtLogisticsTrace::getLogisticsNo, logisticsNos)
                .select(WdtLogisticsTrace::getId, WdtLogisticsTrace::getLogisticsNo)
                .list()
                .stream()
                .collect(Collectors.toMap(WdtLogisticsTrace::getLogisticsNo,
                                          WdtLogisticsTrace::getId,
                                          Math::max));
    }

    @Override
    public Long mapIdByLogisticsNo(String logisticsNo) {
        return mapIdByLogisticsNos(Collections.singletonList(logisticsNo)).get(logisticsNo);
    }
}
