package com.daddylab.supplier.item.application.stockInOrder;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockInOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockInOrderService;
import com.daddylab.supplier.item.infrastructure.kingdee.dto.KingDeeStockInOrderResp;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2022/6/20 11:08 上午
 */
@Slf4j
@Component
public class QueryKingDeeOrderJob {

    @Autowired
    IStockInOrderService iStockInOrderService;

    @Autowired
    StockInOrderMapper stockInOrderMapper;

    @Autowired
    ReqTemplate reqTemplate;

    /**
     * 每天执行一次
     */
    @XxlJob("queryKingDeeOrderJob")
    public void queryKingDeeOrderJob() {

        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            List<KingDeeStockInOrderResp> noticeNoList = null;
            List<KingDeeStockInOrderResp> kingDeeStockInOrderRespList = new LinkedList<>();

            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            try {
                kingDeeStockInOrderRespList = reqTemplate.queryStockInOrderList(dateStr);
            } catch (Exception e) {
                log.error("查询当天kingDee系统中的入库单编号异常,dateStr:{}", dateStr, e);
                Alert.text(MessageRobotCode.GLOBAL, "查询当天kingDee系统中的入库单编号异常。请稍后再尝试调度");
            }

            if (CollectionUtil.isNotEmpty(kingDeeStockInOrderRespList)) {
                // 查询更新时间在今天，状态为诶已入库，并且是同步到金蝶的erp入库单编号
                LocalDateTime now = LocalDateTime.now();
                long start = now.with(LocalTime.MIN).toEpochSecond(ZoneOffset.of("+8"));
                long end = now.with(LocalTime.MAX).toEpochSecond(ZoneOffset.of("+8"));
                List<String> erpStockInOrderNoList = stockInOrderMapper.selectSyncKingDeeNo(start, end);

                if (CollectionUtil.isEmpty(erpStockInOrderNoList)) {
                    // 金蝶入库单不为空，erp入库单为空。所有的金蝶的入库单都需要手动在erp添加
                    noticeNoList = kingDeeStockInOrderRespList;
                } else {
                    // 在kingDee中存在，但是不存在erp的入库单号，发出通知，需要手动在erp中补偿这些入库单

                    // kingDee中 入库单单号会以activeProfile(大写 如TE,GR)为后缀。
                    String evnPrefix = SpringUtil.getActiveProfile().substring(0, 2).toUpperCase();
                    noticeNoList = kingDeeStockInOrderRespList.stream().filter(res -> res.getCode().endsWith(evnPrefix))
                            .peek(res2 -> res2.setCode(res2.getCode().replace(evnPrefix, "").trim()))
                            .filter(kingDeeStockInOrderResp -> {
                                String code = kingDeeStockInOrderResp.getCode();
                                return !erpStockInOrderNoList.contains(code);
                            }).collect(Collectors.toList());
                }
            }

            if (CollectionUtil.isNotEmpty(noticeNoList)) {
                Alert.text(MessageRobotCode.GLOBAL, "需要手动将以下kingDee入库单补充到erp。" + JsonUtil.toJson(noticeNoList));
            }

        });

    }


}
