package com.daddylab.supplier.item.infrastructure.kuaidaoyun.types;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2024/5/22
 */
@ToString
public class KdySubscribeResponse {
    @Getter
    private final Integer code;
    @Getter
    private final String msg;

    @JsonCreator
    public KdySubscribeResponse(String responseStr) {
        final String[] items = responseStr.split(";");
        this.code = Integer.parseInt(items[0]);
        this.msg = items[1];
    }
}
