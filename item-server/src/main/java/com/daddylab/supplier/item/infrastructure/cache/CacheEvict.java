package com.daddylab.supplier.item.infrastructure.cache;

import com.daddylab.supplier.item.infrastructure.cache.Cache.CacheType;
import com.daddylab.supplier.item.infrastructure.cache.Cache.KeySerialize;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CacheEvict {
    /**
     * 缓存键前缀
     */
    String keyPrefix();

    /**
     * 定义拼接缓存键的时候如何将参数对象序列化
     */
    KeySerialize keySerialize() default KeySerialize.TO_STRING;

    /**
     * 缓存类型
     */
    CacheType cacheType() default CacheType.REDIS;

}
