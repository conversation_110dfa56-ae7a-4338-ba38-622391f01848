package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import java.util.List;
import lombok.Data;

@Data
public class CommonCheckInfo {

    @JsonAlias("item_id")
    private Long itemId;

    @JsonAlias("first_check")
    private CommonCheckInfoDetail firstCheck;

    @JsonAlias("last_check")
    private CommonCheckInfoDetail lastCheck;

    @Data
    public static class CommonCheckInfoDetail {

        @JsonAlias("is_hide")
        private Integer isHide;

        @JsonAlias("project_type")
        private Integer projectType;

        @JsonAlias("agent_company")
        private String agentCompany;

        @JsonAlias("choose_batch_no")
        private String chooseBatchNo;

        @JsonAlias("factory_result")
        private Integer factoryResult;

        @JsonAlias("project_status")
        private Integer projectStatus;

        @JsonAlias("content")
        private String content;

        @JsonAlias("opinion")
        private String opinion;

        @JsonAlias("project_info")
        private List<ProjectInfo> projectInfo;

        @JsonAlias("project_no")
        private String projectNo;

        @JsonAlias("check_result")
        private Integer checkResult;

        @JsonAlias("project_finish_time")
        private Integer projectFinishTime;

        @JsonAlias("project_id")
        private Integer projectId;

        @JsonAlias("name")
        private String name;

        @JsonAlias("files")
        private List<File> files;

        @JsonAlias("report_no")
        private List<String> reportNo;
    }

    @Data
    public static class ProjectInfo {

        @JsonAlias("check_project")
        private String checkProject;

        @JsonAlias("check_result")
        private String checkResult;

        @JsonAlias("check_category")
        private String checkCategory;

        @JsonAlias("check_num")
        private Integer checkNum;

        @JsonAlias("remark")
        private String remark;
    }

    @Data
    public static class File {

        @JsonAlias("img_urls")
        private List<String> imgUrls;

        @JsonAlias("name")
        private String name;

        @JsonAlias("pdf_url")
        private String pdfUrl;

        @JsonAlias("id")
        private Integer id;

        @JsonAlias("status")
        private Integer status;
    }

}