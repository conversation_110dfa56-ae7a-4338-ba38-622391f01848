package com.daddylab.supplier.item.application.platformItemSkuInventory;

import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopAuthorizationService;
import com.daddylab.supplier.item.infrastructure.third.base.IShopAuthorizationAware;
import lombok.NonNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/4/8
 */
@Service
public abstract class PlatformServiceFactoryAbstract<T extends IShopAuthorizationAware> implements PlatformServiceFactory<T>, ApplicationContextAware {
    @Autowired
    IShopAuthorizationService shopAuthorizationService;

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public T getService(String shopNo) {
        final ShopAuthorization shopAuthorization = shopAuthorizationService.getByShopNo(shopNo)
                                                                            .filter(ShopAuthorization::isNotExpired)
                                                                            .orElseThrow(() -> ExceptionPlusFactory.bizException(
                                                                                    ErrorCode.SHOP_AUTHORIZATION_EXPIRED,
                                                                                    String.format("店铺 %s 未授权或已过期", shopNo)));
        return getService(shopAuthorization);
    }

    @Override
    public T getService(ShopAuthorization shopAuthorization) {
        final T service = getService();
        service.setAuthorization(shopAuthorization);
        applicationContext.getAutowireCapableBeanFactory().autowireBean(service);
        if (service instanceof InitializingBean) {
            try {
                ((InitializingBean) service).afterPropertiesSet();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return (T) service;
    }

    protected abstract T getService();


}
