package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import cn.hutool.crypto.digest.DigestUtil;

import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import feign.Request;
import feign.RequestInterceptor;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import java.util.Objects;

public class PartnerServerConfiguration extends DlabConfiguration {

  @Value("${partner-server.api-token}")
  String apiToken = "";

  private static final String FIX_TOKEN = "NHzpDQMp5s7ijb3fvOAd7Xyv";

  @Bean("partnerServerRequestInterceptor")
  public RequestInterceptor requestInterceptor() {
    return template -> {
      final String path = template.path();
      boolean isFixToken = path.contains("/dpm/bms/openapi/generate_oa_url");
      final Long currentTime = DateUtil.currentTime();
      final String rawKey =
          StringUtil.format("{}.{}.{}", apiToken, path, String.valueOf(currentTime));
      final String key = DigestUtil.md5Hex(rawKey);

      if (isFixToken) {
        template.header("Token", FIX_TOKEN);
      } else {
        if (Objects.equals(template.method(), Request.HttpMethod.GET.name())) {
          template.query("timestamp", String.valueOf(currentTime));
        } else {
          // 当前只支持JSON格式数据
          JsonNode jsonNode =
              Objects.isNull(template.body())
                  ? JsonUtil.parse("{}".getBytes())
                  : JsonUtil.parse(template.body());
          if (Objects.nonNull(jsonNode) && jsonNode instanceof ObjectNode) {
            ((ObjectNode) jsonNode).put("timestamp", currentTime);
            template.body(JsonUtil.toJson(jsonNode));
          }
        }
        template.header("token", key);
      }
    };
  }
}
