package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.afterSaleLink.dto.LinkCountDto;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSaleShareLinkUser;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 售后分享链接邀请用户信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
public interface AfterSaleShareLinkUserMapper extends DaddyBaseMapper<AfterSaleShareLinkUser> {

    List<LinkCountDto> countByLinkId(@Param("linkIds") Collection<Long> linkIds);

}
