package com.daddylab.supplier.item.application.tmpJob;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.daddylab.supplier.item.domain.region.gateway.RegionGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WarehouseAfterSalesAddress;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWarehouseAfterSalesAddressService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWarehouseService;
import com.daddylab.supplier.item.infrastructure.utils.ObjectUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MultiValuedMap;
import org.apache.commons.collections4.multimap.HashSetValuedHashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/6/28
 */
@Service
@Slf4j
public class AfterSalesWarehouseAddressJob {

    @Autowired
    private IWarehouseService warehouseService;

    @Autowired
    private IWarehouseAfterSalesAddressService warehouseAfterSalesAddressService;

    @Autowired
    private RegionGateway regionGateway;

    @Data
    public static class ImportAddressRow {
        @ExcelProperty("erp对应发货仓名称")
        private String warehouseName;

        @ExcelProperty("解析结果")
        private String address;
    }

    public void importAddress(InputStream inputStream) {
        // 解析excel
        final List<ImportAddressRow> rows = EasyExcel.read(inputStream)
                                                     .head(ImportAddressRow.class)
                                                     .ignoreEmptyRow(true)
                                                     .sheet(0)
                                                     .doReadSync();
        log.info("AfterSalesWarehouseAddressJob::importAddress rows: {}", rows);
        if (rows.isEmpty()) {
            return;
        }
        final MultiValuedMap<String, Integer> results = new HashSetValuedHashMap<>();
        for (int i = 0; i < rows.size(); i++) {
            final int nr = i + 1;
            final ImportAddressRow row = rows.get(i);
            if (StringUtil.isBlank(row.getWarehouseName()) || StringUtil.isBlank(row.getAddress())) {
                results.put("仓库或地址为空", nr);
                continue;
            }
            final String addressRaw = row.getAddress();
            if (!(addressRaw.startsWith("[") && addressRaw.endsWith("]"))) {
                log.error("AfterSalesWarehouseAddressJob::importAddress address format error: {}", row);
                results.put("地址格式错误", nr);
                continue;
            }
            final List<JSONObject> receiveInfoObjs;
            try {
                receiveInfoObjs = JSON.parseArray(addressRaw, JSONObject.class);
                Objects.requireNonNull(receiveInfoObjs);
            } catch (Exception e) {
                log.error("AfterSalesWarehouseAddressJob::importAddress address format parse json error: {}", row, e);
                results.put("地址格式错误", nr);
                continue;
            }

            final List<Warehouse> warehouses = warehouseService.listByName(row.getWarehouseName());
            if (warehouses.isEmpty()) {
                log.error("AfterSalesWarehouseAddressJob::importAddress warehouse not found: {}", row);
                results.put("仓库不存在", nr);
                continue;
            }

            for (Warehouse warehouse : warehouses) {
                final List<WarehouseAfterSalesAddress> addressList = warehouseAfterSalesAddressService
                        .lambdaQuery().eq(WarehouseAfterSalesAddress::getWarehouseNo, warehouse.getNo()).list();
                for (JSONObject receiveInfo : receiveInfoObjs) {
                    try {
                        final String contact = receiveInfo.getString("联系人");
                        final String phoneNumber = ObjectUtil.defaultIfNull(receiveInfo.getString("电话号码"), "");
                        final String remark = receiveInfo.getString("备注");
                        final JSONObject addressObj = receiveInfo.getJSONObject("收货地址");
                        Objects.requireNonNull(addressObj, "收货地址为空");
                        final String province = addressObj.getString("省份");
                        final String city = addressObj.getString("城市");
                        final String area = addressObj.getString("区县");
                        final String address = addressObj.getString("详细地址");
                        final String fullAddress = Stream.of(province, city, area, address)
                                                         .filter(StringUtil::isNotBlank)
                                                         .collect(Collectors.joining(" "));
                        //判断详细地址是否已存在，存在则跳过
                        if (addressList.stream().anyMatch(warehouseAfterSalesAddress ->
                                Objects.equals(warehouseAfterSalesAddress.getFullAddress(), fullAddress))) {
                            results.put("地址已存在", nr);
                            continue;
                        }
                        final String provinceCode = regionGateway.getCodeByName(province);
                        final String cityCode = regionGateway.getCodeByName(city);
                        final String areaCode = regionGateway.getCodeByName(area);

                        final WarehouseAfterSalesAddress warehouseAfterSalesAddress = new WarehouseAfterSalesAddress();
                        warehouseAfterSalesAddress.setWarehouseNo(warehouse.getNo());
                        warehouseAfterSalesAddress.setFullAddress(fullAddress);
                        warehouseAfterSalesAddress.setContacts(contact);
                        warehouseAfterSalesAddress.setTel(phoneNumber);
                        warehouseAfterSalesAddress.setWarehouseName(warehouse.getName());
                        warehouseAfterSalesAddress.setRemark(remark);
                        warehouseAfterSalesAddress.setProvinceCode(provinceCode);
                        warehouseAfterSalesAddress.setCityCode(cityCode);
                        warehouseAfterSalesAddress.setAreaCode(areaCode);
                        warehouseAfterSalesAddressService.save(warehouseAfterSalesAddress);
                        results.put("导入成功", nr);
                    } catch (Exception e) {
                        results.put("保存异常", nr);
                        log.error("AfterSalesWarehouseAddressJob::importAddress address save error: {}", row, e);
                    }
                }
            }

        }
        log.info("AfterSalesWarehouseAddressJob::importAddress results: {}", results);
    }
}
