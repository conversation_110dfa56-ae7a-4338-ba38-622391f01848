package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderDeliveryTrace;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderDeliveryTraceMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderDeliveryTraceService;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 订单发货跟踪 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Service
public class OrderDeliveryTraceServiceImpl extends DaddyServiceImpl<OrderDeliveryTraceMapper, OrderDeliveryTrace> implements IOrderDeliveryTraceService {

}
