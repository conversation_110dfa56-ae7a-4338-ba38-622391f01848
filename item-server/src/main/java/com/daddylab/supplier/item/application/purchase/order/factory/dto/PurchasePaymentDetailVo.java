package com.daddylab.supplier.item.application.purchase.order.factory.dto;

import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(description = "采购付款详情")
public class PurchasePaymentDetailVo {

    @ApiModelProperty(value = "采购明细数据ID")
    private Long detailId;

    @ApiModelProperty(value = "SKU编码/商品编码")
    private String skuCode;

    @ApiModelProperty(value = "商品名称")
    private String skuName;

    @ApiModelProperty(value = "规格")
    private String specifications;

    @ApiModelProperty(value = "SKU单价")
    private BigDecimal price;

    @ApiModelProperty(value = "SKU数量")
    private Integer quantity;

    @ApiModelProperty(value = "SKU总金额")
    private BigDecimal amount;

    private List<CorpBizTypeDTO> corpBizType;

}
