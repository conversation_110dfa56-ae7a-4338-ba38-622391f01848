package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.daddylab.supplier.item.application.stockSpec.InventoryMonitorAlertVO;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryMonitor;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.InventoryMonitorMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IInventoryMonitorService;
import com.daddylab.supplier.item.types.inventoryMonitor.IInventoryMonitorId;
import com.daddylab.supplier.item.types.inventoryMonitor.InventoryMonitorId;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 库存警戒 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
public class InventoryMonitorServiceImpl extends DaddyServiceImpl<InventoryMonitorMapper, InventoryMonitor> implements IInventoryMonitorService {

    @Override
    public Map<IInventoryMonitorId, InventoryMonitor> selectByMonitorIds(List<IInventoryMonitorId> monitorIds) {
        final LambdaQueryChainWrapper<InventoryMonitor> inventoryMonitorLambdaQueryChainWrapper = lambdaQuery();
        for (IInventoryMonitorId monitorId : monitorIds) {
            inventoryMonitorLambdaQueryChainWrapper.or()
                                                   .eq(InventoryMonitor::getWarehouseNo, monitorId.getWarehouseNo())
                                                   .eq(InventoryMonitor::getSkuNo, monitorId.getSkuNo());
        }
        final List<InventoryMonitor> inventoryMonitors = inventoryMonitorLambdaQueryChainWrapper.list();
        return inventoryMonitors.stream()
                                .collect(Collectors.toMap(monitor -> InventoryMonitorId.of(monitor.getWarehouseNo(),
                                        monitor.getSkuNo()), Function.identity()));
    }

    @Override
    public List<InventoryMonitor> selectByWarehouseNos(List<String> warehouseNos) {
        return lambdaQuery().in(InventoryMonitor::getWarehouseNo, warehouseNos)
                            .eq(InventoryMonitor::getSkuNo, "")
                            .list();
    }

    @Override
    public Optional<InventoryMonitor> selectOneByMonitorId(IInventoryMonitorId monitorId) {
        return Optional.ofNullable(selectByMonitorIds(Collections.singletonList(monitorId)).get(monitorId));
    }

    @Override
    public List<InventoryMonitorAlertVO> selectAbsoluteThresholdAlertList(int limit, Long cursor) {
        return getDaddyBaseMapper().selectAbsoluteThresholdAlertList(limit, cursor);
    }
}
