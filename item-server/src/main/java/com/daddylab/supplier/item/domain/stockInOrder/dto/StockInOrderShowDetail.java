package com.daddylab.supplier.item.domain.stockInOrder.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * <AUTHOR>
 * @date 2022/3/30 18:42
 **/
@Data
@ApiModel("分页查询采购入库列表以及明细返回数据模型")
public class StockInOrderShowDetail {
    /**
     * 采购入库列表以及明细
     */
    @ApiModelProperty(value = "采购入库列表以及明细")
    private StockInOrderAndOrderDetail stockInOrderAndOrderDetail;
    /**
     * 明细开关
     **/
    private Integer showDetail;
}
