package com.daddylab.supplier.item.application.purchasePayable.service;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.purchasePayable.dto.*;
import com.daddylab.supplier.item.application.purchasePayable.query.ApplyOrderQueryPage;
import com.daddylab.supplier.item.application.purchasePayable.query.ApplyPayOrderDetailListQueryPage;
import com.daddylab.supplier.item.application.purchasePayable.query.PurchasePayableQueryPage;
import com.daddylab.supplier.item.application.purchasePayable.query.PurchasePayableVoucherQuery;
import com.daddylab.supplier.item.application.purchasePayable.vo.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableApplyOrder;

import java.util.List;

/**
 * <p>
 * 采购应付表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
public interface PurchasePayableBizService {

    /**
     * 分页查询
     *
     * @param purchasePayableQueryPage 查询参数
     */
    PageResponse<PurchasePayableVO> queryPage(PurchasePayableQueryPage purchasePayableQueryPage);

    /**
     * 应付凭证
     *
     * @param query 查询参数
     */
    SingleResponse<PurchasePayableVoucherVO> voucher(PurchasePayableVoucherQuery query);


    /**
     * 生成采购应付单
     */
    Response create(PurchasePayableCmd cmd);


    /**
     * 采购应付单详情
     *
     * @param id 采购应付单ID
     */
    SingleResponse<PurchasePayableDetailsVO> details(Long id, String no);


    /**
     * 其他应付专用，
     * eg: 如果其他应付审核通过了，生成了应付单，
     * 但是它可以反审核，有可能反审核之后就拒绝了。 所以，如果反审核了  就把之前生成的应付单删除掉。
     * 审核通过之后，重新生成一份新的。
     *
     * @param cmd
     * @return
     */
    Response delete(PurchasePayableCmd cmd);

    // --------------------------------- 申请付款单相关功能接口 ---------------------------


    /**
     * 2023-02-03[七喜]
     * 修正采购应付操作
     *
     * @param saveDto 修正参数。
     *                数据来源为审核通过的付款申请单详情。
     *                这批数据会按照供应商和仓库切割好。providerId和warehouseNo已经是唯一，完成了聚合。
     * @return
     */
    MultiResponse<String> fixPurchasePayData(ArtificialPayOrderSaveDto saveDto);

    /**
     * 创建付款申请单，仅仅是保存数据。
     *
     * @param cmd
     * @return
     */
    SingleResponse<Long> saveApplyOrder(SaveApplyOrderDto cmd);

    /**
     * 编辑付款申请单操作
     *
     * @param cmd 编辑请求参数
     * @return true成功，false失败
     */
    SingleResponse<Boolean> editApplyOrder(EditApplyPayOrderCmd cmd);

    /**
     * 判断 选中的应付单是否 都属于同一个供应商
     *
     * @param applyNoList
     * @return
     */
    SingleResponse<Boolean> checkProvider(List<ApplyPayNoDto> applyNoList);

    /**
     * 根据选中的应付单展示申请付款信息（头信息）
     *
     * @param applyNoList 申请付款的付款单编号集合，只包含看了采购入库和退料出库的应付单
     * @return
     */
    SingleResponse<ApplyPayOrderDetailVO> showApplyOrderDetail(List<ApplyPayNoDto> applyNoList);

    /**
     * 根据选中的应付单展示应付信息（sku列表）
     *
     * @param applyNoList 申请付款的付款单编号集合,分页包装下
     * @return
     */
    MultiResponse<ApplyPayOrderDetailListVO> showApplyOrderDetailList(List<ApplyPayNoDto> applyNoList);

    /**
     * 查询原始单据的关联单号信息
     *
     * @param sourcePayOrderNo
     * @return
     */
    MultiResponse<ApplyPayOrderRelationVO> queryPayOrderRelation(String sourcePayOrderNo);

    PageResponse<PurchasePayableApplyOrder> queryApplyOrder(ApplyOrderQueryPage queryPage);


    SingleResponse<ApplyPayOrderDetailVO> showApplyOrderDetailByApplyNo(String no);

    MultiResponse<SaveApplyOrderDetailDto> showApplyOrderDetailListByApplyNo(ApplyPayOrderDetailListQueryPage queryPage);

    SingleResponse<Boolean> deleteApplyOrderDetailByNo(String no);

    //---------------------


    /**
     * 冲销操作。
     *
     * @param payApplyOrderId 申请付款单ID
     */
    void hedgeHandler(Long payApplyOrderId);

    /**
     * 冲销操作。补偿操作
     *
     * @param payApplyOrderNo          申请付款单no
     * @param sourcePurchasePayOrderNo 原采购应付单编号
     * @return true:成功，false:失败
     */
    SingleResponse<Boolean> compensateHedgeHandler(String payApplyOrderNo, String sourcePurchasePayOrderNo);

    SingleResponse<Boolean> deleteApplyPay(List<String> applyPayOrderNos);

    Response syncApplyPayBill(Long id);

}
