package com.daddylab.supplier.item.application.order.settlement.dto;

import com.alibaba.cola.dto.Command;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OrderSettlementStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年08月25日 10:40 AM
 */
@ApiModel("下载明细/导出系统账单请求参数")
@EqualsAndHashCode(callSuper = true)
@Data
public class ExportSysBillCmd extends Command {
    private static final long serialVersionUID = 955159519805737038L;

    @ApiModelProperty("单据ids")
    private List<Long> ids;

    @ApiModelProperty("结算周期")
    private List<Long> times;

    @ApiModelProperty("仓库编码")
    private List<String> warehouseNos;

    @ApiModelProperty("仓库订单员")
    private List<Long> orderPersonnelIds;

    @ApiModelProperty("状态。WAIT_CONFIRM 待结算。CONFIRMED 已结算")
    private OrderSettlementStatus status;

    private Integer statusVal;
    private List<String> queryWarehouseNos;


}
