package com.daddylab.supplier.item.application.saleItem.vo;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/** 新品商品负责人信息 */
@Data
@ApiModel("新品商品返回实体")
public class NewGoodsPrincipalsInfoPO implements Serializable {
    private static final long serialVersionUID = -1200415708549781152L;

    @ApiModelProperty("商品ID")
    private Long itemId;

    @ApiModelProperty(value = "采购负责人信息")
    private Long buyerId;

    @ApiModelProperty(value = "产品负责人信息")
    private Long principalId;

    @ApiModelProperty(value = "QC负责人")
    private String qcIds;

    @ApiModelProperty("新品商品数量")
    private Integer newGoodsNum;

    public List<Long> getQcIds() {
        return StrUtil.split(qcIds, ",", true, true).stream()
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    @ApiModelProperty(value = "法务负责人信息")
    private Long legalId;
}
