package com.daddylab.supplier.item.infrastructure.schedule;

import cn.hutool.core.util.RuntimeUtil;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RBucket;
import org.redisson.api.RSemaphore;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class ScheduleAspect {
    private final RedissonClient redissonClient;
    private final ConcurrentMap<String, AtomicInteger> acquireFailCounter = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, RSemaphore> activeSemaphores = new ConcurrentHashMap<>();
    private final AtomicBoolean isShutdown = new AtomicBoolean(false);

    @Autowired
    public ScheduleAspect(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
        RuntimeUtil.addShutdownHook(this::shutdown);
    }

    public void shutdown() {
        log.info("ScheduleAspect >> start shutdown");
        isShutdown.compareAndSet(false, true);
        for (Map.Entry<String, RSemaphore> semaphoreEntry : activeSemaphores.entrySet()) {
            semaphoreEntry.getValue().release();
            log.info("ScheduleAspect >> semaphore release, method: " + semaphoreEntry.getKey());
        }
        activeSemaphores.clear();
        log.info("ScheduleAspect >> shutdown finished");
    }

    @Around("@annotation(org.springframework.scheduling.annotation.Scheduled)")
    public Object handle(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        //获取方法签名
        MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
        Method method = methodSignature.getMethod();
        final Class<?> declaringClass = method.getDeclaringClass();
        final String methodId = declaringClass.getName() + "." + method.getName();

        //如果应用已经处于正在关机的状态，就禁止调度新的任务
        if (isShutdown.get()) {
            log.info("ScheduleAspect >> reject for shutdown, method: {}", methodId);
            return null;
        }

        //检查当前任务状态（是否被禁用）
        final String taskInfoKey = RedisKeys.TASK_INFO + methodId;
        final RBucket<TaskInfo> taskInfoBucket = redissonClient.getBucket(taskInfoKey);
        final TaskInfo taskInfo = Optional.ofNullable(taskInfoBucket.get()).orElseGet(TaskInfo::new);
        if (taskInfo.getTaskStatus() == TaskStatus.DISABLE) {
            log.info("ScheduleAspect >> Task disabled, method: {}", methodId);
            return null;
        }

        //获取信号量
        final String semaphoreId = RedisKeys.TASK_SEMAPHORE + methodId;
        final RSemaphore semaphore = activeSemaphores.computeIfAbsent(methodId, k -> redissonClient.getSemaphore(semaphoreId));
        int permit = 1;
        int acquireFailAlarmThreshold = 10;
        final ScheduleSemaphore scheduleSemaphore = method.getAnnotation(ScheduleSemaphore.class);
        if (scheduleSemaphore != null) {
            permit = scheduleSemaphore.value();
            acquireFailAlarmThreshold = scheduleSemaphore.acquireFailAlarmThreshold();
        }
        semaphore.trySetPermits(permit);

        final AtomicInteger counter = acquireFailCounter.computeIfAbsent(methodId, it -> new AtomicInteger());
        if (semaphore.tryAcquire()) {
            log.info("ScheduleAspect >> semaphore acquire, method: {}", methodId);
            counter.set(0);
            taskInfo.setExecuting(true);
            taskInfo.setLastExecuteTime(LocalDateTime.now());
            taskInfo.setLastExecuteTimeEnd(null);
            taskInfoBucket.set(taskInfo);
            try {
                return proceedingJoinPoint.proceed();
            } catch (Throwable throwable) {
                log.error("ScheduleAspect >> exception, method: {}", methodId, throwable);
                Alert.text(MessageRobotCode.GLOBAL, "unknown exception, method:" + methodId
                        + " ex:" + ExceptionUtil.getSimpleStackString(throwable));
            } finally {
                if(null != activeSemaphores.remove(methodId)) {
                    semaphore.release();
                    log.info("ScheduleAspect >> semaphore release, method: {}", methodId);
                } else {
                    log.info("ScheduleAspect >> semaphore released somewhere else, method: {}", methodId);
                }
                taskInfo.setExecuting(false);
                taskInfo.setLastExecuteTimeEnd(LocalDateTime.now());
                taskInfoBucket.set(taskInfo);
            }
        } else {
            log.info("ScheduleAspect >> semaphore can't acquire, method: {}", methodId);
            if (counter.incrementAndGet() >= acquireFailAlarmThreshold) {
                Alert.text(MessageRobotCode.GLOBAL, "当前任务" + methodId + "连续" + acquireFailAlarmThreshold + "次获取信号量失败");
                counter.set(0);
            }
        }
        return null;
    }
}
