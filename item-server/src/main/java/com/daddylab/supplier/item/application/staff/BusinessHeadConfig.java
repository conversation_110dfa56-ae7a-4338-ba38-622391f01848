package com.daddylab.supplier.item.application.staff;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BusinessLine;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/12/12
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "business-head")
@RefreshScope
public class BusinessHeadConfig {
    /**
     * 老板
     */
    private Long boss;
    /**
     * 业务线经理
     */
    private Map<String, List<Long>> businessLineManagers = Collections.emptyMap();

    /**
     * 运营负责人
     */
    private Map<String, List<Long>> operationHeads = Collections.emptyMap();
}
