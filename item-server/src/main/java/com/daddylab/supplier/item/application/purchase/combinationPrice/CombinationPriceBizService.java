package com.daddylab.supplier.item.application.purchase.combinationPrice;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.purchase.combinationPrice.sheet.CombinationStepPriceExcelVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CombinationStepPriceExcel;
import org.springframework.web.multipart.MultipartFile;

public interface CombinationPriceBizService {


  PageResponse<CombinationStepPriceExcelVo> pageQuery2(PurchaseStepPricePageQuery pageQuery);

  void historyDataHandler();

  /**
   * 保存阶梯活动价格 【20231010】只是是体现出sku级别的组合阶梯价格，目前不体现spu纬度的阶梯价格
   * 数据最终还是会同步到PurchaseSingleSkuCombinationPrice实体中。
   *
   * @param cmd
   * @return
   */
  Response save(CombinationStepPriceExcel cmd);

  /**
   * 删除采购阶梯价格数据
   *
   * @param id
   * @return
   */
  Response delete(Long id);


  /**
   * @param priceType 1活动SKU多件供价。2活动SPU多件供价。3日常SKU多件供价。4日常SPU多件供价。
   * @param file
   * @return
   */
  Response importExcel2( MultipartFile file);


  Response export2(PurchaseStepPricePageQuery pageQuery);



}
