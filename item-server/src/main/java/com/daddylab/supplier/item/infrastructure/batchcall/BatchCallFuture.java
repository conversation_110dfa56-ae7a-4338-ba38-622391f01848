package com.daddylab.supplier.item.infrastructure.batchcall;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2021/12/11
 */
@Slf4j
public class BatchCallFuture extends CompletableFuture<Object> {
    final Context context;
    final RequestManager requestManager;
    final Runnable batchCall;
    final Supplier<Object> directCall;
    final long createdAt = System.currentTimeMillis();
    final AtomicBoolean getTimeout = new AtomicBoolean(false);

    public BatchCallFuture(Context context, RequestManager requestManager, Runnable batchCall, Supplier<Object> directCall) {
        this.context = context;
        this.requestManager = requestManager;
        this.batchCall = batchCall;
        this.directCall = directCall;
        register();
    }

    private void register() {
        requestManager.registerRequest(context.getMethod(), context.getArg(), this);
    }

    @Override
    public Object get() throws InterruptedException, ExecutionException {
        while (true) {
            try {
                return get(context.getAnnotation().bufferTime(), TimeUnit.MILLISECONDS);
            } catch (TimeoutException ignored) {
            }
        }
    }

    @Override
    public boolean complete(Object value) {
        log.debug("complete {}", value);
        return super.complete(value);
    }

    @Override
    public Object get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
        try {
            if (getTimeout.get()
                    && !requestManager.isHanding(context.getMethod(), context.getArg())) {
                log.debug("run batch for {}({})", context.getMethod().getName(), context.getArg());
                batchCall.run();
            }
            if (System.currentTimeMillis() > createdAt + context.getAnnotation().expectWaitLessThan()) {
                log.warn("direct call {}({}) because wait exceed expect time", context.getMethod().getName(), context.getArg());
                final Object result = directCall.get();
                complete(result);
            }
            return super.get(timeout, unit);
        } catch (TimeoutException | InterruptedException ex){
            getTimeout.compareAndSet(false, true);
            throw ex;
        }
    }
}
