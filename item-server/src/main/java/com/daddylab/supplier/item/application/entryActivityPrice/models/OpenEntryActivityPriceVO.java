package com.daddylab.supplier.item.application.entryActivityPrice.models;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/11/15
 */
@Data
public class OpenEntryActivityPriceVO {
    private String itemCode;
    private String skuCode;
    private BigDecimal platformCommission;
    private BigDecimal contractSalePrice;
    private Boolean isCombinationItem;
    private List<EntryActivityPrice> entryActivityPrices;

    @Data
    public static class EntryActivityPrice {
        private Long activeStart;
        private Long activeEnd;
        private BigDecimal platformCommission;
        private BigDecimal contractSalePrice;
        private String activityRemark;
    }
}
