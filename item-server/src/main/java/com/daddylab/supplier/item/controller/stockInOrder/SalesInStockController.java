package com.daddylab.supplier.item.controller.stockInOrder;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.salesInStock.SalesInStockBizService;
import com.daddylab.supplier.item.application.salesInStock.dto.SalesInStockDetailCmd;
import com.daddylab.supplier.item.application.salesInStock.dto.SalesInStockDetailVO;
import com.daddylab.supplier.item.application.salesInStock.dto.SalesInStockPageQuery;
import com.daddylab.supplier.item.application.salesInStock.dto.SalesInStockPageVO;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> up
 * @date 2023年10月23日 9:36 AM
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/salesInStock")
@Api(value = "库存管理-销退入库单相关API", tags = "库存管理-销退入库单相关API")
public class SalesInStockController {

    @Resource
    SalesInStockBizService salesInStockBizService;

    @ApiOperation(value = "列表查询")
    @PostMapping(value = "/queryPage")
    @Auth(noAuth = true)
    public PageResponse<SalesInStockPageVO> queryPage(@RequestBody SalesInStockPageQuery query) {
        return salesInStockBizService.queryPage(query);
    }

    @ApiOperation(value = "出库单详情")
    @PostMapping(value = "/view")
    public SingleResponse<SalesInStockDetailVO> view(@RequestBody @Validated SalesInStockDetailCmd cmd) {
        return salesInStockBizService.viewDetail(cmd);
    }

}
