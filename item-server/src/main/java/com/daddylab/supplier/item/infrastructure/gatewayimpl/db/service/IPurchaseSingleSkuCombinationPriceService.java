package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseSingleSkuCombinationPrice;

/**
 * <p>
 * 单sku纬度的组合采购价格信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-21
 */
public interface IPurchaseSingleSkuCombinationPriceService extends IDaddyService<PurchaseSingleSkuCombinationPrice> {


}
