package com.daddylab.supplier.item.domain.purchase.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName favourableType.java
 * @description
 * @createTime 2021年11月19日 21:52:00
 */
@Getter
@AllArgsConstructor
public enum FavourableType implements IEnum<Integer> {
    DEDUCT(0, "货抵款"),
    ACCORDING_TIME(1, "按时间供价"),
    FAVOURABLE_TIME(2, "当月优惠件数");
    final public Integer value;
    final public String desc;
}
