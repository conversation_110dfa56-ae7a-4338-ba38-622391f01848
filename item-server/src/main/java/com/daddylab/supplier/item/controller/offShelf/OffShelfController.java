package com.daddylab.supplier.item.controller.offShelf;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.offShelf.OffShelfBizService;
import com.daddylab.supplier.item.application.offShelf.dto.*;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizServiceImpl;
import com.daddylab.supplier.item.common.domain.dto.GenericIdBody;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@RequestMapping("/offShelf")
@RestController
@Slf4j
@Api(tags = {"商品下架"})
public class OffShelfController {
  @Resource private OffShelfBizService shelfBizService;
  @Autowired private OperateLogBizServiceImpl operateLogBizService;

  @ApiOperation("分页查询")
  @PostMapping("/page")
  public PageResponse<OffShelfPageVO> page(@Validated @RequestBody OffShelfPageQuery query) {
    return shelfBizService.page(query);
  }

  @ApiOperation("导出")
  @PostMapping("/export")
  public SingleResponse<Long> export(@Validated @RequestBody OffShelfPageQuery query) {
    return shelfBizService.export(query);
  }

  @ApiOperation("查看")
  @PostMapping("/view")
  public SingleResponse<OffShelfFormVO> view(@RequestBody @Validated GenericIdBody<Long> cmd) {
    return shelfBizService.view(cmd.getId());
  }

  @ApiOperation("保存")
  @PostMapping("/save")
  public SingleResponse<Long> save(@RequestBody @Validated OffShelfFormCmd cmd) {
    return shelfBizService.save(cmd);
  }

  @PostMapping("/submit")
  @ApiOperation("提交")
  public SingleResponse<Boolean> submit(@RequestBody @Validated GenericIdBody<Long> cmd) {
    return shelfBizService.submit(cmd.getId());
  }

  @PostMapping("/approve")
  @ApiOperation("审批")
  public SingleResponse<Boolean> approve(@RequestBody @Validated OffShelfApproveCmd cmd) {
    return shelfBizService.approve(cmd);
  }

  @PostMapping("/batchApprove")
  @ApiOperation("批量审批")
  public SingleResponse<Map<Long, String>> batchApprove(
      @RequestBody @Validated OffShelfBatchApproveCmd cmd) {
    return shelfBizService.batchApprove(cmd);
  }

  @PostMapping("/delete")
  @ApiOperation("删除")
  public SingleResponse<Boolean> delete(@RequestBody @Validated OffShelfDeleteCmd cmd) {
    return shelfBizService.delete(cmd);
  }

  @PostMapping("/revoke")
  @ApiOperation("撤回")
  public SingleResponse<Boolean> revoke(@RequestBody @Validated OffShelfRevokeCmd cmd) {
    return shelfBizService.revoke(cmd);
  }

  @PostMapping("/batchProcessGetItems")
  @ApiOperation("批量处理反馈获取列表")
  public MultiResponse<OffShelfBatchProcessFeedbackItem> batchProcessGetItems(
      @RequestBody @Validated OffShelfBatchProcessGetItemsCmd cmd) {
    return shelfBizService.batchProcessGetItems(cmd);
  }

  @PostMapping("/processFeedback")
  @ApiOperation("处理反馈")
  public SingleResponse<Boolean> processFeedback(
      @RequestBody @Validated OffShelfProcessFeedbackCmd cmd) {
    return shelfBizService.processFeedback(cmd);
  }

  @PostMapping("/batchProcessFeedback")
  @ApiOperation("批量处理反馈")
  public SingleResponse<Boolean> batchProcessFeedback(
      @RequestBody @Validated OffShelfBatchProcessFeedbackCmd cmd) {
    return shelfBizService.batchProcessFeedback(cmd);
  }

  @PostMapping("/urge")
  @ApiOperation("催办")
  public Response urge(@RequestBody @Validated OffShelfUrgeCmd cmd) {
    return shelfBizService.urge(cmd);
  }

  @GetMapping(value = "/operateLogs")
  @ApiOperation("操作日志")
  public Response operateLogs(Long targetId) {
    return operateLogBizService.getOperateLogs(OperateLogTarget.OFF_SHELF, targetId);
  }

  @PostMapping("/executeOffShelf")
  @ApiOperation("执行下架")
  public Response executeOffShelf(@RequestBody @Validated GenericIdBody<Long> cmd) {
    return shelfBizService.executeOffShelf(cmd.getId());
  }
}
