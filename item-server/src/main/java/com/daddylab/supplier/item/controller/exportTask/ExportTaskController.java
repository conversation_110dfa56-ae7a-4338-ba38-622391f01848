package com.daddylab.supplier.item.controller.exportTask;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.exportTask.ExportTaskBizService;
import com.daddylab.supplier.item.controller.item.dto.ExportTaskVo;
import com.daddylab.supplier.item.controller.item.dto.TaskPageQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11 10:41 上午
 * @description
 */
@Api(value = "导出任务相关api", tags = "导出任务相关api")
@RestController()
@RequestMapping("/exportTask")
public class ExportTaskController {

    @Autowired
    ExportTaskBizService exportTaskBizService;

    @ResponseBody
    @PostMapping("/excelList")
    @ApiOperation("excel列表")
    public PageResponse<ExportTaskVo> excelList(@RequestBody TaskPageQuery pageQuery) {
        return exportTaskBizService.exportList(pageQuery);
    }

    @ResponseBody
    @PostMapping("/excelUrl")
    @ApiOperation("获取excel下载url")
    public SingleResponse<String> excelUrl(@NotNull(message = "taskId不能为空") Long taskId) {
        return exportTaskBizService.getExcelDownloadUrl(taskId);
    }

    @ResponseBody
    @GetMapping("/clearTaskList")
    @ApiOperation("清空列表")
    public Response clearExcelList(String type) {
        exportTaskBizService.clearExportList(type);
        return Response.buildSuccess();
    }





}
