package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 平台商品（投放到其他平台的商品）商品维度 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Repository
public interface PlatformItemMapper extends DaddyBaseMapper<PlatformItem> {
    
    Long countOnSalePlatformItem(@Param("itemId") Long itemId, @Param("platform") Integer platform);
    
    List<String> shopSnList();
}
