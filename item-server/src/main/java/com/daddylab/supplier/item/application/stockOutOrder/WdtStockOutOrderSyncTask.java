package com.daddylab.supplier.item.application.stockOutOrder;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.purchase.PurchaseReturnAPI;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.PurchaseReturnQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.PurchaseReturnQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.PurchaseReturnQueryWithDetailResponse.Order.Detail;
import com.daddylab.supplier.item.application.common.event.StockInOrOutEvent;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockOutOrderState;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockOutOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockOutOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockOutOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockOutOrderDetailService;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * 旺店通退料出库单同步任务
 *
 * <AUTHOR>
 * @since 2022/4/16
 */
@Component
@Slf4j
public class WdtStockOutOrderSyncTask {

    private final WdtGateway wdtGateway;
    private final StockOutOrderMapper stockOutOrderMapper;
    private final IStockOutOrderDetailService iStockOutOrderDetailService;
    private final OperateLogDomainService operateLogDomainService;

    public WdtStockOutOrderSyncTask(WdtGateway wdtGateway,
                                    StockOutOrderMapper stockOutOrderMapper,
                                    IStockOutOrderDetailService iStockOutOrderDetailService,
                                    OperateLogDomainService operateLogDomainService) {
        this.wdtGateway = wdtGateway;
        this.stockOutOrderMapper = stockOutOrderMapper;
        this.iStockOutOrderDetailService = iStockOutOrderDetailService;
        this.operateLogDomainService = operateLogDomainService;
    }

    @XxlJob("WdtStockOutOrderSyncTask")
    public void sync() {
        final PurchaseReturnAPI api = wdtGateway.getAPI(PurchaseReturnAPI.class);
        final LambdaQueryWrapper<StockOutOrder> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper
                .in(StockOutOrder::getState, Arrays.asList(
                        StockOutOrderState.NO_STOCK_OUT.getValue(),
                        StockOutOrderState.PART_OUT.getValue()
                ))
                .eq(StockOutOrder::getIsSyncWdt, 1)
                .isNotNull(StockOutOrder::getWdtStorageNo)
                .ne(StockOutOrder::getWdtStorageNo, "")
        ;
        final List<StockOutOrder> stockOutOrders = stockOutOrderMapper.selectList(queryWrapper);
        int failCount = 0;

        for (StockOutOrder stockOutOrder : stockOutOrders) {
            try {
                final PurchaseReturnQueryWithDetailParams params = new PurchaseReturnQueryWithDetailParams();
                params.setReturnNo(stockOutOrder.getWdtStorageNo());

                final Pager pager = new Pager(10, 0, true);
                final PurchaseReturnQueryWithDetailResponse response = api
                        .queryWithDetail(params, pager);
                final List<PurchaseReturnQueryWithDetailResponse.Order> orders = response
                        .getOrder();
                if (orders.isEmpty()) {
                    continue;
                }
                final PurchaseReturnQueryWithDetailResponse.Order order = orders.get(0);
                //采购退货单状态 10 已取消,20 编辑中,30 待审核,40 已审核,60 已完成
                //已取消
                if (order.getStatus() == 10) {
                    stockOutOrder.setState(StockOutOrderState.CANCEL.getValue());
                    stockOutOrderMapper.updateById(stockOutOrder);
                    operateLogDomainService.addOperatorLog(0L,
                            OperateLogTarget.STOCK_OUT_ORDER, stockOutOrder.getId(), "旺店通侧取消退料出库单");
                    continue;
                }

                //未审核跳过
                if (order.getStatus() < 40) {
                    continue;
                }

                //退货出库量
                final Integer totalReturnQuantity = Optional.ofNullable(order.getGoodsOutCount())
                        .map(BigDecimal::intValue).orElse(0);

                //未出库跳过
                if (totalReturnQuantity == 0) {
                    continue;
                }

                boolean stateChange = false;

                List<StockOutOrderDetail> stockOutOrderDetailList = new ArrayList<>(orders.size());
                for (Detail detail : order.getDetailList()) {
                    // 数量
                    List<StockOutOrderDetail> stockOutOrderDetails = iStockOutOrderDetailService.
                            getDetailByStockIdAndSku(stockOutOrder.getId(), detail.getSpecNo());
                    int returnQuantity = detail.getOutNum().intValue();
                    for (int i = 0; i < stockOutOrderDetails.size(); i++) {
                        final StockOutOrderDetail stockOutOrderDetail = stockOutOrderDetails.get(i);
                        if (returnQuantity <= 0) {
                            break;
                        }
                        //如果存在超量出库的情况，多出来的库存全部分配到最后一个明细上
                        Integer realReturnQuantity;
                        if (i == stockOutOrderDetails.size() - 1) {
                            realReturnQuantity = returnQuantity;
                            returnQuantity = 0;
                        } else if (returnQuantity > stockOutOrderDetail.getReturnQuantity()) {
                            realReturnQuantity = stockOutOrderDetail.getReturnQuantity();
                            returnQuantity -= stockOutOrderDetail.getReturnQuantity();
                        } else {
                            realReturnQuantity = returnQuantity;
                            returnQuantity = 0;
                        }
                        if (!Objects.equals(realReturnQuantity, stockOutOrderDetail.getRealReturnQuantity())) {
                            stockOutOrderDetail.setRealReturnQuantity(realReturnQuantity);
                            stockOutOrderDetail
                                    .setValuationQuantity(stockOutOrderDetail.getRealReturnQuantity());
                            stockOutOrderDetail
                                    .setReplenishQuantity(stockOutOrderDetail.getRealReturnQuantity());
                            stockOutOrderDetail
                                    .setDeductionQuantity(stockOutOrderDetail.getRealReturnQuantity());
                            // 价格
                            stockOutOrderDetail.calculatedAmount();
                            stockOutOrderDetailList.add(stockOutOrderDetail);
                            stateChange = true;
                        }
                    }
                }

                int totalReturnQuantityVal = iStockOutOrderDetailService.lambdaQuery()
                        .eq(StockOutOrderDetail::getStockOutOrderId, stockOutOrder.getId())
                        .select().list().stream().mapToInt(StockOutOrderDetail::getReturnQuantity).sum();

                //采购退货单状态 10 已取消,20 编辑中,30 待审核,40 已审核,60 已完成
                //只要实际出库数据大于我们初始设置的出库数量就算全部出库
                if (order.getGoodsOutCount()
                        .compareTo(NumberUtil.
                                toBigDecimal(totalReturnQuantityVal)) >= 0) {

                    if (Arrays.asList(StockOutOrderState.PART_OUT.getValue(),
                            StockOutOrderState.NO_STOCK_OUT.getValue())
                            .contains(stockOutOrder.getState())) {

                        stockOutOrder.setState(StockOutOrderState.STOCK_OUT.getValue());
                        stockOutOrder.setOutboundTime(DateUtil.currentTime());
                        stockOutOrderMapper.updateById(stockOutOrder);
                        stateChange = true;
                    }
                } else if (Objects.equals(stockOutOrder.getState(),
                        StockOutOrderState.NO_STOCK_OUT.getValue())) {
                    stockOutOrder.setState(StockOutOrderState.PART_OUT.getValue());
                    stockOutOrderMapper.updateById(stockOutOrder);
                    stateChange = true;
                }

                if (!stockOutOrderDetailList.isEmpty()) {
                    for (StockOutOrderDetail stockOutOrderDetail : stockOutOrderDetailList) {
                        iStockOutOrderDetailService.updateById(stockOutOrderDetail);
                    }
                }

                if (StockOutOrderState.STOCK_OUT.getValue().equals(stockOutOrder.getState())) {
                    operateLogDomainService.addOperatorLog(UserContext.getUserId(),
                            OperateLogTarget.STOCK_OUT_ORDER, stockOutOrder.getId(), "系统回传采退出库数量（全部出库）");

                    // 创建应付单
                    Long createdUid = stockOutOrder.getCreatedUid();
                    final StockInOrOutEvent stockInOrOutEvent = StockInOrOutEvent
                            .ofNotice(PurchaseTypeEnum.OUT_STOCK_PAYABLE, stockOutOrder.getId(),
                                    createdUid, createdUid);
                    EventBusUtil.post(stockInOrOutEvent, true);
                } else if (stateChange) {
                    operateLogDomainService.addOperatorLog(UserContext.getUserId(),
                            OperateLogTarget.STOCK_OUT_ORDER, stockOutOrder.getId(), "系统回传采退出库数量");
                }

            } catch (WdtErpException e) {
                failCount++;
                Alert.text(MessageRobotCode.GLOBAL, "同步旺店通采购出库单失败:" + e.getMessage());

                if (failCount > 10) {
                    Alert.text(MessageRobotCode.GLOBAL, "连续多次同步旺店通采购出库单失败:" + e.getMessage());
                    break;
                }
            }
        }
    }

}
