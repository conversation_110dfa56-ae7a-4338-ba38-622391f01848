package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAuditTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerModuleAuditTaskMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerModuleAuditTaskService;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerAuditTaskState;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemLaunchProcessNodeId;

import java.util.Collections;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品库抽屉模块审核 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
@Service
public class ItemDrawerModuleAuditTaskServiceImpl extends
        DaddyServiceImpl<ItemDrawerModuleAuditTaskMapper, ItemDrawerModuleAuditTask> implements
        IItemDrawerModuleAuditTaskService {

    @Override
    public List<ItemDrawerModuleAuditTask> getItemDrawerModulePendingAuditTasks(Long itemId,
            ItemLaunchProcessNodeId nodeId) {
        return lambdaQuery().eq(ItemDrawerModuleAuditTask::getItemId, itemId)
                .eq(ItemDrawerModuleAuditTask::getNode, nodeId.getValue())
                .eq(ItemDrawerModuleAuditTask::getAuditStatus,
                        ItemDrawerAuditTaskState.PENDING).list();
    }

    @Override
    public List<ItemDrawerModuleAuditTask> getItemDrawerModulePendingAuditTasks(Long itemId, ItemAuditType type) {
        return lambdaQuery().eq(ItemDrawerModuleAuditTask::getItemId, itemId)
                .eq(ItemDrawerModuleAuditTask::getType, type)
                .eq(ItemDrawerModuleAuditTask::getAuditStatus,
                        ItemDrawerAuditTaskState.PENDING).list();
    }
}
