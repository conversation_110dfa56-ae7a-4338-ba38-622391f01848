package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 操作日志
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OperateLog extends Entity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 操作实体类型 1:商品
     */
    private OperateLogTarget targetType;

    /**
     * 目标实体ID
     */
    private String targetId;

    /**
     * 日志信息
     */
    private String msg;

    /**
     * 操作相关数据 JSON
     */
    private String data;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @TableField(exist = false)
    private String operatorName;

    /**
     * 操作人花名
     */
    @TableField(exist = false)
    private String operatorNickName;

    /**
     * 删除时间
     */
    private Long deletedAt;

    private int operatorType;
}
