package com.daddylab.supplier.item.domain.exportTask;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;

public interface ExportTaskGateway {

    Long saveOrUpdateExportTask(ExportTask exportTask);

    /**
     * 删除过期时间
     *
     * @param expireLimitDate
     */
    void removeExpireTask(Long expireLimitDate);

    /**
     * 更新进度
     *
     * @param exportTask 导出任务
     */
    void updateProgress(ExportTask exportTask);
}
