package com.daddylab.supplier.item.application.afterSaleLogistics.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Positive;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/23
 */
@Data
public class AfterSaleLogisticsRemarkCmd {
    @Positive
    private Long id;
    private List<Long> ids;

    @NotBlank
    private String remark;
}
