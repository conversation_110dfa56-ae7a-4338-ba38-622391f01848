package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.lang.func.LambdaUtil;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemOptimize;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemOptimizeMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemOptimizeService;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizePersistData;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizeStatus;

import lombok.SneakyThrows;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 商品优化 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Service
public class ItemOptimizeServiceImpl extends DaddyServiceImpl<ItemOptimizeMapper, ItemOptimize>
        implements IItemOptimizeService {

    @Override
    public boolean setStatusById(
            Long id, ItemOptimizeStatus currentStatus, ItemOptimizeStatus updateTo) {
        return lambdaUpdate()
                .set(ItemOptimize::getStatus, currentStatus.getValue())
                .eq(ItemOptimize::getStatus, updateTo.getValue())
                .eq(ItemOptimize::getId, id)
                .update();
    }

    @Override
    public <T> boolean setDataField(Long id, Func1<ItemOptimizePersistData, T> field, T value) {
        final String fieldName = LambdaUtil.getFieldName(field);
        return lambdaUpdate()
                .eq(Entity::getId, id)
                .setSql(String.format("data = json_set(data, '$.%s', %s)", fieldName, value))
                .update();
    }

    @Override
    public <K, V> boolean setDataFieldMapValue(
            Long id, Func1<ItemOptimizePersistData, Map<K, V>> field, K key, V value) {
        final String fieldName = LambdaUtil.getFieldName(field);
        return lambdaUpdate()
                .eq(Entity::getId, id)
                .setSql(
                        String.format(
                                "data = json_set(data, '$.%s.\"%s\"', %s)", fieldName, key, value))
                .update();
    }

    @Override
    @SneakyThrows
    public <T> T getDataField(Long id, Func1<ItemOptimizePersistData, T> field) {
        final String fieldName = LambdaUtil.getFieldName(field);
        final ItemOptimizeMapper daddyBaseMapper = getDaddyBaseMapper();
        ItemOptimizePersistData data = daddyBaseMapper.selectDataField(id, fieldName);
        return field.call(data);
    }

    @Override
    public boolean updateSubmitMeta(Long id, Long userId, Long time, Boolean isReview) {
        return getDaddyBaseMapper().updateSubmitMetadata(id, userId, time, isReview);
    }

    @Override
    public List<ItemOptimize> selectListByPlanId(Long planId) {
        return lambdaQuery().eq(ItemOptimize::getPlanId, planId).list();
    }
}
