package com.daddylab.supplier.item.application.saleItem.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SaleItemLibrarySheet extends ExportSheet {

    @ExcelProperty("商品编码(日常编码)")
    private String itemSkuCode;

    @ExcelProperty("行业类目")
    private String category;

    @ExcelProperty("直播/短视频/社群价格")
    private String platformPrice;

    @ExcelProperty("直播/短视频/社群活动内容")
    private String platformActivityContent;

    @ExcelProperty("直播/短视频/社群成本")
    private String platformCost;

    @ExcelProperty("直播/短视频/社群活动编码")
    private String platformActivityCode;

    @ExcelProperty("特殊备注(仅限某渠道、库存限量等)")
    private String remark;
}
