package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Message;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-21
 */
public interface MessageMapper extends DaddyB<PERSON>Mapper<Message> {

    /**
//     * @param recipientId
//     * @param pushType    {@Linke com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessagePushType}
//     * @param size
//     * @param index
//     * @return
//     */
//    List<Message> pageMessage(@Param("recipientId") Long recipientId, @Param("pushType") Integer pushType,
//                              @Param("offset") Long offset, @Param("size") Integer size);
//
//    Integer pageMessageCount(@Param("recipientId") Long recipientId, @Param("pushType") Integer pushType);
}
