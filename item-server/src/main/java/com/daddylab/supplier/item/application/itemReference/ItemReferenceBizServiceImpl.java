package com.daddylab.supplier.item.application.itemReference;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.StopWatch;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceQuery;
import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceSku;
import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceSkuExportDO;
import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceSpu;
import com.daddylab.supplier.item.application.itemReference.types.ItemReferenceSpuExportDO;
import com.daddylab.supplier.item.application.itemReference.types.SpuSkuRef;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.domain.exportTask.ExportTaskGateway;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.itemStock.gateway.WarehouseStockGateway;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.bizLevelDivision.BizDivisionContext;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BizUnionTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.SpuMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBizLevelDivisionService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/9/4
 */
@Service
@AllArgsConstructor
@Slf4j
public class ItemReferenceBizServiceImpl implements ItemReferenceBizService {

  private final SpuMapper spuMapper;
  private final ExportTaskGateway exportTaskGateway;
  private final FileGateway fileGateway;
  private final WarehouseStockGateway warehouseStockGateway;
  private final UserGateway userGateway;
  private final ProviderGateway providerGateway;
  private final IBizLevelDivisionService bizLevelDivisionService;

  @Override
  public PageResponse<ItemReferenceSpu> spuPageQuery(ItemReferenceQuery query) {
    final long count =
        BizDivisionContext.invoke(
            BizUnionTypeEnum.SPU,
            ctx -> ctx.setBizIdRef("i.id"),
            () -> spuMapper.itemReferenceSpuQueryCount(query));
    final List<ItemReferenceSpu> itemReferenceSpus =
        BizDivisionContext.invoke(
            BizUnionTypeEnum.SPU,
            ctx -> ctx.setBizIdRef("i.id"),
            () -> spuMapper.itemReferenceSpuQuery(query));

    log.info("商品参照表SPU查询:总计{}", itemReferenceSpus.size());

    final int batchSize = 1000;

    final ArrayList<ItemReferenceSpu> itemReferenceSpusBatch = new ArrayList<>();
    for (int i = 0; i < itemReferenceSpus.size(); i++) {
      final ItemReferenceSpu referenceSpus = itemReferenceSpus.get(i);
      itemReferenceSpusBatch.add(referenceSpus);

      if (itemReferenceSpusBatch.size() >= batchSize) {
        log.info("商品参照表SPU查询/外部数据查询:{}/{}", i, itemReferenceSpus.size());
        setUpSpuExtraData(itemReferenceSpusBatch);
        itemReferenceSpusBatch.clear();
      }
    }

    if (!itemReferenceSpusBatch.isEmpty()) {
      setUpSpuExtraData(itemReferenceSpusBatch);
    }

    return PageResponse.of(
        itemReferenceSpus, (int) count, query.getPageSize(), query.getPageIndex());
  }

  private void setUpSpuExtraData(List<ItemReferenceSpu> itemReferenceSpus) {
    final Set<String> itemCodes =
        itemReferenceSpus.stream()
            .map(ItemReferenceSpu::getItemCode)
            .filter(StringUtil::isNotBlank)
            .collect(Collectors.toSet());
    final Map<String, BigDecimal> itemStockSum = warehouseStockGateway.getItemStockSum(itemCodes);

    final List<Long> buyerUidSet =
        itemReferenceSpus.stream().map(ItemReferenceSpu::getBuyerUid).collect(Collectors.toList());
    final Map<Long, StaffInfo> staffInfoMap = userGateway.batchQueryStaffInfoByIds(buyerUidSet);

    final Set<Long> partnerProviderIds =
        itemReferenceSpus.stream()
            .map(ItemReferenceSpu::getPartnerProviderId)
            .collect(Collectors.toSet());
    final Map<Long, PartnerProviderResp> partnerProviderMap =
        providerGateway.partnerBatchQueryByIds(partnerProviderIds);

    final List<Long> itemIds =
        itemReferenceSpus.stream().map(ItemReferenceSpu::getItemId).collect(Collectors.toList());
    final Map<Long, List<CorpBizTypeDTO>> corpBizTypeMap =
        bizLevelDivisionService.getCorpBizType(BizUnionTypeEnum.SPU, itemIds);

    itemReferenceSpus.forEach(
        spu -> {
          final Long stock =
              Optional.ofNullable(itemStockSum.get(spu.getItemCode()))
                  .map(BigDecimal::longValue)
                  .orElse(0L);
          spu.setStock(stock);

          Optional.ofNullable(staffInfoMap.get(spu.getBuyerUid()))
              .ifPresent(
                  v -> {
                    spu.setBuyerName(v.getUserName());
                    spu.setBuyerNick(v.getNickname());
                  });

          spu.setIsBlacklist(
              Optional.ofNullable(spu.getPartnerProviderId())
                  .map(partnerProviderMap::get)
                  .map(PartnerProviderResp::getIsBlacklist)
                  .orElse(0));

          spu.setCorpBizType(
              Optional.ofNullable(spu.getItemId())
                  .map(corpBizTypeMap::get)
                  .orElseGet(Collections::emptyList));
        });
  }

  @Override
  public PageResponse<ItemReferenceSku> skuPageQuery(ItemReferenceQuery query) {
    final long count =
        BizDivisionContext.invoke(
            BizUnionTypeEnum.SPU,
            ctx -> ctx.setBizIdRef("i.id"),
            () -> spuMapper.itemReferenceSkuQueryCount(query));
    final List<ItemReferenceSku> itemReferenceSkus =
        BizDivisionContext.invoke(
            BizUnionTypeEnum.SPU,
            ctx -> ctx.setBizIdRef("i.id"),
            () -> spuMapper.itemReferenceSkuQuery(query));

    log.info("商品参照表SKU查询:总计{}", itemReferenceSkus.size());

    final int batchSize = 1000;

    final ArrayList<ItemReferenceSku> itemReferenceSkusBatch = new ArrayList<>();
    for (int i = 0; i < itemReferenceSkus.size(); i++) {
      final ItemReferenceSku referenceSkus = itemReferenceSkus.get(i);
      itemReferenceSkusBatch.add(referenceSkus);

      if (itemReferenceSkusBatch.size() >= batchSize) {
        log.info("商品参照表SKU查询/外部数据查询:{}/{}", i, itemReferenceSkus.size());
        setUpSkuExtraData(itemReferenceSkusBatch);
        itemReferenceSkusBatch.clear();
      }
    }
    if (!itemReferenceSkusBatch.isEmpty()) {
      setUpSkuExtraData(itemReferenceSkusBatch);
    }

    return PageResponse.of(
        itemReferenceSkus, (int) count, query.getPageSize(), query.getPageIndex());
  }

  private void setUpSkuExtraData(List<ItemReferenceSku> itemReferenceSkus) {
    final Set<String> skuCodes =
        itemReferenceSkus.stream()
            .map(ItemReferenceSku::getSkuCode)
            .filter(StringUtil::isNotBlank)
            .collect(Collectors.toSet());
    Map<String, BigDecimal> stockSum = Collections.emptyMap();
    try {
      stockSum = warehouseStockGateway.getStockSum(skuCodes);
    } catch (Exception e) {
      log.error("[商品参照库]库存查询异常", e);
    }

    final List<Long> buyerUidSet =
        itemReferenceSkus.stream().map(ItemReferenceSku::getBuyerUid).collect(Collectors.toList());
    final Map<Long, StaffInfo> staffInfoMap = userGateway.batchQueryStaffInfoByIds(buyerUidSet);

    final Set<Long> partnerProviderIds =
        itemReferenceSkus.stream()
            .map(ItemReferenceSku::getPartnerProviderId)
            .collect(Collectors.toSet());
    final Map<Long, PartnerProviderResp> partnerProviderMap =
        providerGateway.partnerBatchQueryByIds(partnerProviderIds);

    final List<Long> itemIds =
        itemReferenceSkus.stream().map(ItemReferenceSku::getItemId).collect(Collectors.toList());
    final Map<Long, List<CorpBizTypeDTO>> corpBizTypeMap =
        bizLevelDivisionService.getCorpBizType(BizUnionTypeEnum.SPU, itemIds);

    for (ItemReferenceSku sku : itemReferenceSkus) {
      final Long stock =
          Optional.ofNullable(stockSum.get(sku.getSkuCode())).map(BigDecimal::longValue).orElse(0L);
      sku.setStock(stock);

      Optional.ofNullable(staffInfoMap.get(sku.getBuyerUid()))
          .ifPresent(
              v -> {
                sku.setBuyerName(v.getUserName());
                sku.setBuyerNick(v.getNickname());
              });

      sku.setIsBlacklist(
          Optional.ofNullable(sku.getPartnerProviderId())
              .map(partnerProviderMap::get)
              .map(PartnerProviderResp::getIsBlacklist)
              .orElse(0));

      sku.setCorpBizType(
          Optional.ofNullable(sku.getItemId())
              .map(corpBizTypeMap::get)
              .orElseGet(Collections::emptyList));
    }
  }

  @Override
  public SingleResponse<Long> spuExport(ItemReferenceQuery query) {
    ExportTask exportTask = new ExportTask();
    exportTask.setStatus(ExportTaskStatus.RUNNING);
    exportTask.setName("商品参照表SPU导出-" + DateUtil.formatNow(DatePattern.PURE_DATETIME_PATTERN));
    exportTask.setType(ExportTaskType.ITEM_REFERENCE_SPU);
    final Long taskId = exportTaskGateway.saveOrUpdateExportTask(exportTask);
    exportTask.setId(taskId);
    ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL)
        .execute(
            () -> {
              final StopWatch stopWatch = new StopWatch();
              stopWatch.start();
              log.info("商品参照表SPU导出开始执行 任务ID:{}", exportTask.getId());
              try (final ByteArrayOutputStream byteArrayOutputStream =
                  new ByteArrayOutputStream()) {
                query.setPageIndex(1);
                query.setPageSize(999999);
                final PageResponse<ItemReferenceSpu> itemReferenceSpuPageResponse =
                    spuPageQuery(query);
                EasyExcel.write(byteArrayOutputStream)
                    .useDefaultStyle(false)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("工作表1")
                    .head(ItemReferenceSpuExportDO.class)
                    .doWrite(
                        ItemReferenceExportAssembler.INST.spuListToExportDOList(
                            itemReferenceSpuPageResponse.getData()));
                UploadFileAction action =
                    UploadFileAction.ofInputStream(
                        new ByteArrayInputStream(byteArrayOutputStream.toByteArray()),
                        exportTask.getName() + ".xlsx");
                final String downloadUrl = fileGateway.uploadFile(action).getUrl();
                exportTask.setStatus(ExportTaskStatus.SUCCESS);
                exportTask.setDownloadUrl(downloadUrl);
              } catch (Exception e) {
                log.error("商品参照表SPU导出异常 任务ID:{}", exportTask.getId(), e);
                exportTask.setError(ExceptionUtil.getSimpleStackString(e));
                exportTask.setStatus(ExportTaskStatus.FAIL);
              } finally {
                stopWatch.stop();
                exportTaskGateway.saveOrUpdateExportTask(exportTask);
                log.info(
                    "商品参照表SPU导出执行结束 任务ID:{} 状态:{} 总耗时:{}ms",
                    exportTask.getId(),
                    exportTask.getStatus(),
                    stopWatch.getTotalTimeMillis());
              }
            });
    return SingleResponse.of(taskId);
  }

  @Override
  public SingleResponse<Long> skuExport(ItemReferenceQuery query) {
    ExportTask exportTask = new ExportTask();
    exportTask.setStatus(ExportTaskStatus.RUNNING);
    exportTask.setName("商品参照表SKU导出-" + DateUtil.formatNow(DatePattern.PURE_DATETIME_PATTERN));
    exportTask.setType(ExportTaskType.ITEM_REFERENCE_SKU);
    final Long taskId = exportTaskGateway.saveOrUpdateExportTask(exportTask);
    exportTask.setId(taskId);
    ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL)
        .execute(
            () -> {
              final StopWatch stopWatch = new StopWatch();
              stopWatch.start();
              log.info("商品参照表SKU导出开始执行 任务ID:{}", exportTask.getId());
              try (final ByteArrayOutputStream byteArrayOutputStream =
                  new ByteArrayOutputStream()) {
                query.setPageIndex(1);
                query.setPageSize(999999);
                final PageResponse<ItemReferenceSku> itemReferenceSkuPageResponse =
                    skuPageQuery(query);
                EasyExcel.write(byteArrayOutputStream)
                    .useDefaultStyle(false)
                    .head(ItemReferenceSkuExportDO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("工作表1")
                    .doWrite(
                        ItemReferenceExportAssembler.INST.skuListToExportDOList(
                            itemReferenceSkuPageResponse.getData()));
                UploadFileAction action =
                    UploadFileAction.ofInputStream(
                        new ByteArrayInputStream(byteArrayOutputStream.toByteArray()),
                        exportTask.getName() + ".xlsx");
                final String downloadUrl = fileGateway.uploadFile(action).getUrl();
                exportTask.setStatus(ExportTaskStatus.SUCCESS);
                exportTask.setDownloadUrl(downloadUrl);
              } catch (Exception e) {
                log.error("商品参照表SKU导出异常 任务ID:{}", exportTask.getId(), e);
                exportTask.setError(ExceptionUtil.getSimpleStackString(e));
                exportTask.setStatus(ExportTaskStatus.FAIL);
              } finally {
                stopWatch.stop();
                exportTaskGateway.saveOrUpdateExportTask(exportTask);
                log.info(
                    "商品参照表SKU导出执行结束 任务ID:{} 状态:{} 总耗时:{}ms",
                    exportTask.getId(),
                    exportTask.getStatus(),
                    stopWatch.getTotalTimeMillis());
              }
            });
    return SingleResponse.of(taskId);
  }

  @Override
  @Transactional(rollbackFor = Throwable.class)
  public Response importRef(InputStream inputStream, Integer sheetIndex) {
    final Long userId = UserContext.getUserId();
    List<SpuSkuRef> inputRefs =
        EasyExcel.read(inputStream)
            .headRowNumber(1)
            .head(SpuSkuRef.class)
            .sheet(sheetIndex)
            .doReadSync();
    if (inputRefs.isEmpty()) {
      return Response.buildFailure(ErrorCode.FILE_UPLOAD_ERROR.getCode(), "导入文件空");
    }
    final int totalSize = inputRefs.size();
    log.info("开始导入商品参照表关联，总计：{}", totalSize);
    final StopWatch stopWatch = new StopWatch();
    stopWatch.start();
    final int chunkSize = 1000;
    int counter = 0;
    final ArrayList<SpuSkuRef> refsChunk = new ArrayList<>(chunkSize);
    for (SpuSkuRef inputRef : inputRefs) {
      refsChunk.add(inputRef);
      final int thisChunkSize = refsChunk.size();
      if (thisChunkSize >= chunkSize) {
        log.info("正在处理中 {}+{}/{}", counter, thisChunkSize, totalSize);
        persistRefs(userId, refsChunk);
        counter += thisChunkSize;
        refsChunk.clear();
      }
    }
    if (!refsChunk.isEmpty()) {
      log.info("正在处理中 {}+{}/{}", counter, refsChunk.size(), totalSize);
      persistRefs(userId, refsChunk);
    }
    log.info("处理完成 总计：{}ms", stopWatch.getTotalTimeMillis());
    return Response.buildSuccess();
  }

  private void persistRefs(Long userId, ArrayList<SpuSkuRef> refsChunk) {
    final List<SpuSkuRef> refs =
        refsChunk.stream()
            .filter(v -> StringUtil.isBlank(v.getSkuCode()) || StringUtil.isBlank(v.getItemCode()))
            .collect(Collectors.toList());
    if (refs.isEmpty()) {
      log.warn("维护SPU&SKU参照表关联关系，输入关联对象集合有效记录数为0");
      return;
    }
    final List<String> itemCodesChunk =
        refs.stream()
            .map(SpuSkuRef::getItemCode)
            .filter(StringUtil::isNotBlank)
            .collect(Collectors.toList());
    final List<String> skuCodesChunk =
        refs.stream()
            .map(SpuSkuRef::getSkuCode)
            .filter(StringUtil::isNotBlank)
            .collect(Collectors.toList());
    final List<SpuSkuRef> spuSkuRefs = spuMapper.refBatchQueryBySkuCodes(skuCodesChunk);
    final List<String> beforeItemCodes =
        spuSkuRefs.stream().map(SpuSkuRef::getItemCode).distinct().collect(Collectors.toList());
    spuMapper.saveSpuAndIgnoreIfExist(itemCodesChunk, userId);
    spuMapper.updateItemSkuRef(refsChunk, userId);
    spuMapper.updateSpuState(
        CollectionUtil.union(itemCodesChunk, skuCodesChunk, beforeItemCodes), userId);
  }

  @Override
  @Transactional(rollbackFor = Throwable.class)
  public void syncItemSku(String itemCode, Long userId) {
    final List<String> itemCodes = Collections.singletonList(itemCode);
    spuMapper.saveNewItem(itemCodes, userId);
    spuMapper.saveNewItemSku(itemCodes, userId, spuMapper.getAdjustItemCode(itemCode));
    spuMapper.removeInvalidItemSku(itemCodes, userId);
    spuMapper.updateSpuState(itemCodes, userId);
  }
}
