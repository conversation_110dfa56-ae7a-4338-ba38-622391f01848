package com.daddylab.supplier.item.controller.item.dto;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.PositiveOrZero;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/8 5:35 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("商品分页查询参数")
public class ItemPageQuery extends PageQuery {

  private static final long serialVersionUID = 8723420395304971870L;

  @ApiModelProperty("商品id")
  private Long itemId;

  @ApiModelProperty("商品id列表")
  private List<Long> itemIds;

  @ApiModelProperty("商品名称")
  private String itemName;

  @ApiModelProperty("商品编码")
  private String code;

  @ApiModelProperty("关联款号（其他系统商品数据关联）")
  private String partnerCode;

  @ApiModelProperty("商品sku")
  private String sku;

  @ApiModelProperty("品类id")
  private Long categoryId;

  @ApiModelProperty("品牌id")
  private Long brandId;

  @ApiModelProperty("品牌名称")
  private String brandName;

  @ApiModelProperty("上新开始时间")
  private Long shelfStartTime;

  @ApiModelProperty("上新结束时间")
  private Long shelfEndTime;

  @ApiModelProperty("采购员用户id")
  private Long buyerUserId;

  @ApiModelProperty("供应商id")
  private Long providerId;

  @ApiModelProperty("分页偏移量，不填。")
  private Long offsetVal;

  /** 商品状态0:待上架.1:在售中.2:已下架。3:废弃 */
  @ApiModelProperty("商品状态 0:待上架 1:在售中 2:已下架 3:废弃")
  private Integer itemStatus;

  @ApiModelProperty("商品状态列表")
  private List<Integer> itemStatusList;

  @ApiModelProperty("是否是用于商品上新计划")
  private Boolean useForItemLaunchPlan;

  @ApiModelProperty("是否是用于盘货表的商品选择")
  private Boolean useForHandingSheet;

  @ApiModelProperty("仓库编号")
  private String warehouseNo;

  @ApiModelProperty("合作模式：0供应商 1老爸抽检 2绿色家装 3商家入驻")
  private List<Integer> businessLine = new ArrayList<>();

  @ApiModelProperty("不查询库存")
  private Boolean notQueryStock;

  @ApiModelProperty(
      value = "标签",
      notes = "全部不传，DAD_SAMPLING_INSPECTION 老爸抽检, CUSTOMIZED_PRODUCTS 定制品")
  private String tag;

  @ApiModelProperty("发货渠道 0:仓库发货 1:工厂发货 2:全部")
  @PositiveOrZero(message = "发货渠道参数不合法")
  private Long delivery;

  @ApiModelProperty("是否查询小程序商品状态")
  private Boolean queryMallStatus;

  @ApiModelProperty("合作方")
  private List<Integer> corpType;

  @ApiModelProperty("业务类型")
  private List<Integer> bizType;

  @ApiModelProperty("下架开始时间")
  private Long offShelfStartTime;

  @ApiModelProperty("下架结束时间")
  private Long offShelfEndTime;
}
