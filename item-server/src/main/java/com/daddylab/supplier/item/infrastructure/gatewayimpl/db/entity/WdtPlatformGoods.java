package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 旺店通系统数据-平台商品
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtPlatformGoods implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 旺店通店铺id
     */
    private Integer shopId;

    /**
     * 旺店通店铺编号
     */
    private String shopNo;

    /**
     * 旺店通店铺名称
     */
    private String shopName;

    /**
     * 平台货品名称
     */
    private String goodsName;

    /**
     * 平台规格名称
     */
    private String specName;

    /**
     * 平台规格编码
     */
    private String specOuterId;

    /**
     * 平台货品编码
     */
    private String outerId;

    /**
     * 平台货品ID
     */
    private String goodsId;

    /**
     * 平台规格ID
     */
    private String specId;

    /**
     * rec_id
     */
    private String recId;

    /**
     * 旺店通系统内是否被删除 0正常货品，1平台删除，2手工删除
     */
    private Integer isDeleted;

    /**
     * 平台价格
     */
    private BigDecimal price;

    /**
     * 平台库存
     */
    private BigDecimal stockNum;

    /**
     * 旺店通系统状态 0删除，1在架，2下架
     */
    private Integer status;

    /**
     * 是否自动匹配 1自动匹配，0 手动匹配
     */
    private Integer isAutoMatch;

    /**
     * 货品类型 0未指定，1单品，2组合装
     */
    private Integer matchTargetType;

    /**
     * 占用库存
     */
    private BigDecimal holdStock;

    /**
     * 占用方式 1拍下减库存 2付款减库存
     */
    private Integer holdStockType;

    /**
     * 是否自动上架
     */
    private Integer isAutoListing;

    /**
     * 是否自动下架
     */
    private Integer isAutoDelisting;

    /**
     * 旺店通系统中平台货品最后修改时间, 时间格式 YYYY-MM-DD HH:MM:SS
     */
    private LocalDateTime modified;

    /**
     * 系统商家编码 match_target_type=1时返回的为单品商家编码、match_target_type=2时返回的为组合装商家编码
     */
    private String merchantNo;

    /**
     * 系统货品名称 match_target_type=1时返回的为单品货品名称、match_target_type=2时返回的为组合装货品名称
     */
    private String merchantName;

    /**
     * 系统规格编码 match_target_type=1时返回的为单品规格编码、match_target_type=2时返回为空字符串
     */
    private String merchantCode;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;


}
