package com.daddylab.supplier.item.infrastructure.alert;

import lombok.Builder;

/**
 * <AUTHOR> up
 * @date 2022年07月25日 5:32 PM
 */
@Builder
public class WeChatContent {
    private String api;
    private Long id;
    private String req;
    private String resp;
    private String error;
    private String remark;

    @Override
    public String toString() {
        return "KingDeeTemplate handler异常" +
                "\n" +
                ">api：<font color=\\\"info\\\">" + api + "</font> \n" +
                ">id：<font color=\\\"info\\\">" + id + "</font> \n" +
                ">req：<font color=\\\"info\\\">" + req + "</font> \n" +
                ">resp：<font color=\\\"info\\\">" + resp + "</font> \n" +
                ">error：<font color=\\\"info\\\">" + error + "</font> ";
    }

}
