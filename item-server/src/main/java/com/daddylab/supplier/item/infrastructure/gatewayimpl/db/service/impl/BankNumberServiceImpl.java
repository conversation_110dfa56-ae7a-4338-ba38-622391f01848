package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BankNumber;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.BankNumberMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBankNumberService;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 银行联行号 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@Service
public class BankNumberServiceImpl extends DaddyServiceImpl<BankNumberMapper, BankNumber> implements IBankNumberService {

}
