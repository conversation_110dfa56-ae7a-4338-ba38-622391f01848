package com.daddylab.supplier.item.domain.banniu;

import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Buyer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBuyerService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.third.banniu.starter.BanniuConstants;
import com.daddylab.third.banniu.starter.BanniuProperties;

import lombok.SneakyThrows;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import wjb.open.api.ApiException;
import wjb.open.api.WjbClient;
import wjb.open.api.request.v2.banniu.SellerListRequest;
import wjb.open.api.request.v2.banniu.UserListRequest;
import wjb.open.api.response.v2.banniu.SellerListResponse;
import wjb.open.api.response.v2.banniu.UserListResponse;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/8/25
 */
@Service
public class BanniuCommonServiceImpl implements BanniuCommonService {
    public static final int USERS_RENEW_INTERVAL = 10_000;
    private final WjbClient wjbClient;
    private final BanniuProperties banniuProperties;

    @Resource IBuyerService iBuyerService;

    @Resource
    @Qualifier("UserGatewayCacheImpl")
    UserGateway userGateway;

    public BanniuCommonServiceImpl(
            @Qualifier(BanniuConstants.COMMON_CLIENT_BEAN) WjbClient wjbClient,
            BanniuProperties banniuProperties) {
        this.wjbClient = wjbClient;
        this.banniuProperties = banniuProperties;
    }

    @SneakyThrows
    @Override
    public List<SellerListResponse.Seller> shops() {
        SellerListRequest request = new SellerListRequest();
        SellerListResponse response = wjbClient.execute(request, banniuProperties.getAccessToken());
        return response.getResult();
    }

    @Override
    @SneakyThrows
    public List<UserListResponse.User> users() {
        final UserListRequest userListRequest = new UserListRequest();
        return Optional.ofNullable(
                        wjbClient.execute(userListRequest, banniuProperties.getAccessToken()))
                .map(UserListResponse::getResult)
                .orElseThrow(() -> new com.actionsoft.bpms.api.ApiException("班牛用户列表返回异常"));
    }

    List<UserListResponse.User> usersCache = null;
    long usersCacheTimestamp;

    @Override
    @SneakyThrows
    public Optional<UserListResponse.User> user(long userId) {
        return userCacheGet(v -> v.getId() == userId, false);
    }

    @Override
    @SneakyThrows
    public Optional<UserListResponse.User> user(String user) {
        return userCacheGet(v -> Objects.equals(v.getTitle(), user), false);
    }

    @SneakyThrows
    private Optional<UserListResponse.User> userCacheGet(
            Predicate<UserListResponse.User> userPredicate, boolean fetchLatest) {
        if (usersCache == null || fetchLatest) {
            usersCache = users();
            usersCacheTimestamp = System.currentTimeMillis();
        }
        final Optional<UserListResponse.User> userOptional =
                usersCache.stream().filter(userPredicate).findAny();
        if (!userOptional.isPresent()
                && !fetchLatest
                && System.currentTimeMillis() - usersCacheTimestamp > USERS_RENEW_INTERVAL) {
            return userCacheGet(userPredicate, true);
        }
        return userOptional;
    }

    @Override
    public void syncBuyer() throws ApiException {
        List<UserListResponse.User> users = users();
        for (UserListResponse.User user : users) {
            Object id = user.getId();
            Object title = user.getTitle();

            iBuyerService
                    .lambdaQuery()
                    .eq(Buyer::getName, title.toString())
                    .eq(Buyer::getType, 1)
                    .oneOpt()
                    .orElseGet(
                            () -> {
                                StaffInfo staffInfo =
                                        userGateway.queryStaffInfoByNickname(title.toString());
                                Buyer buyer = new Buyer();
                                buyer.setUserId(
                                        Objects.nonNull(staffInfo) ? staffInfo.getUserId() : 0L);
                                buyer.setName(title.toString());
                                buyer.setKingDeeId("");
                                buyer.setCreatedAt(0L);
                                buyer.setUpdatedAt(0L);
                                buyer.setCreatedUid(0L);
                                buyer.setUpdatedUid(0L);
                                buyer.setIsDel(0);
                                buyer.setKingDeeMapId("");
                                buyer.setKingDeeAccount("");
                                buyer.setThirdId(id.toString());
                                buyer.setType(1);
                                iBuyerService.save(buyer);
                                return null;
                            });
        }
    }

    @Override
    public void syncItem(Item item) throws ApiException {}
}
