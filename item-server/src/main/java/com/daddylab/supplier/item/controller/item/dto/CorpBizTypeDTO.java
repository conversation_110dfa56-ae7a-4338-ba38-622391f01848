package com.daddylab.supplier.item.controller.item.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/13
 */
@Data
public class CorpBizTypeDTO {
  /** 合作方 */
  private Integer corpType;

  /** 业务类型列表 */
  private List<Integer> bizType;

  public boolean containBizType(DivisionLevelValueEnum aBizType) {
    return bizType != null && bizType.stream().anyMatch(it -> it.equals(aBizType.getValue()));
  }
}
