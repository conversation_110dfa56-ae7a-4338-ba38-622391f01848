package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAudit;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;

import java.util.Optional;

/**
 * <p>
 * 商品库抽屉模块审核 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
public interface IItemDrawerModuleAuditService extends IDaddyService<ItemDrawerModuleAudit> {

    ItemDrawerModuleAudit getItemDrawerModuleAuditCreateIfNotExits(ItemAuditType type, Long liveVerbalTrickId, Long itemId);

    ItemDrawerModuleAudit getItemDrawerModuleAuditCreateIfNotExits(ItemAuditType type, Long itemId);

    Optional<ItemDrawerModuleAudit> getItemDrawerModuleAudit(ItemAuditType type, Long itemId);

    Optional<ItemDrawerModuleAudit> getItemDrawerModuleAudit(ItemAuditType type, Long liveVerbalTrickId, Long itemId);


}
