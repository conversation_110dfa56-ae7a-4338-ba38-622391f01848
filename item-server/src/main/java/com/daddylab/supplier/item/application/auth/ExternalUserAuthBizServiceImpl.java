package com.daddylab.supplier.item.application.auth;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.smsAuth.SmsAuthService;
import com.daddylab.supplier.item.infrastructure.common.ExternalUserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExternalUser;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IExternalUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/5/29
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class ExternalUserAuthBizServiceImpl implements ExternalUserAuthBizService {
    public static final String SCOPE = "EXTERNAL_USER";
    private final SmsAuthService smsAuthService;
    private final ExternalUserLoginLogic externalUserLoginLogic;
    private final IExternalUserService externalUserService;

    @Override
    public Response sendCode(String phone) {
        final ExternalUser externalUser = getExternalUser(phone);
        if (externalUser == null || externalUser.getStatus() != 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "用户不存在");
        }
        final String smsCode = smsAuthService.getSmsCode(phone, SCOPE);
        log.info("[ExternalUserAuth]send sms code {} to {}", smsCode, phone);
        return Response.buildSuccess();
    }

    private ExternalUser getExternalUser(String phone) {
        return externalUserService.lambdaQuery().eq(ExternalUser::getTel, phone).one();
    }

    @Override
    public SingleResponse<String> login(String phone, String code) {
        final ExternalUser externalUser = getExternalUser(phone);
        if (externalUser == null || externalUser.getStatus() != 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "用户不存在");
        }
        if (!smsAuthService.verifyCode(phone, code, SCOPE)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.SMS_AUTH_ERROR, "短信验证码错误");
        }
        externalUserLoginLogic.login(String.valueOf(externalUser.getId()));
        return SingleResponse.of(externalUserLoginLogic.getTokenValue());
    }

    @Override
    public boolean checkLogin() {
        try {
            final String loginId = (String) externalUserLoginLogic.getLoginId();
            final long id = Long.parseLong(loginId);
            final ExternalUser externalUser = externalUserService.getById(id);
            if (externalUser == null || externalUser.getStatus() != 0) {
                throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "用户不存在");
            }
            ExternalUserContext.set(externalUser);
            return true;
        } catch (Exception ignored) {
        }
        return false;
    }
}
