package com.daddylab.supplier.item.infrastructure.fileStore;

import static cn.hutool.core.util.URLUtil.getDecodedPath;

import static com.daddylab.supplier.item.infrastructure.utils.MPUtil.limit;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.URLUtil;

import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.ImageStub;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.fileStore.vo.VideoStub;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.File;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IFileService;
import com.daddylab.supplier.item.infrastructure.upyun.exception.FileUploadException;
import com.github.rholder.retry.*;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public abstract class FileGatewayBaseImpl implements FileGateway {

    @Autowired
    private IFileService fileService;

    @Autowired
    private UploadConfig uploadConfig;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public ImageStub uploadImage(UploadFileAction action) {
        return retryTemplate(
                action,
                uploadFileAction ->
                        uploadTemplate(
                                action, FileTypeMapper.INST::imageStubFromFile,
                                this::uploadImageProcess));
    }

    /**
     * 上传图片
     *
     * @param action
     * @param file
     * @return
     */
    protected abstract ImageStub uploadImage0(UploadFileAction action, File file);

    public ImageStub uploadImageProcess(UploadFileAction action, File file) {
        ImageStub fileStub = uploadImage0(action, file);
        fileService.saveOrUpdate(file);
        fileStub.setFileId(file.getId());
        return fileStub;
    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public FileStub uploadFile(UploadFileAction action) {
        return retryTemplate(
                action,
                uploadFileAction -> uploadTemplate(action, FileTypeMapper.INST::fileStubFromFile,
                        this::uploadFileProcess));
    }

    /**
     * 上传文件
     *
     * @param action
     * @param file
     * @return
     */
    protected abstract FileStub uploadFile0(UploadFileAction action, File file);

    public FileStub uploadFileProcess(UploadFileAction action, File file) {
        FileStub fileStub = uploadFile0(action, file);
        fileService.saveOrUpdate(file);
        fileStub.setFileId(file.getId());
        return fileStub;
    }

    @Data
    @AllArgsConstructor
    public static class UploadParam {
        UploadFileAction action;
        File file;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public VideoStub uploadVideo(UploadFileAction action) {
        return retryTemplate(
                action,
                uploadFileAction ->
                        uploadTemplate(
                                action, FileTypeMapper.INST::videoStubFromFile,
                                this::uploadVideoProcess));
    }

    /**
     * 上传视频
     *
     * @param action
     * @param file
     * @return
     */
    protected abstract VideoStub uploadVideo0(UploadFileAction action, File file);

    public VideoStub uploadVideoProcess(UploadFileAction action, File file) {
        VideoStub fileStub = uploadVideo0(action, file);
        fileService.saveOrUpdate(file);
        fileStub.setFileId(file.getId());
        return fileStub;
    }

    private <T extends FileStub> T uploadTemplate(
            UploadFileAction action,
            Function<File, T> stubConverter,
            BiFunction<UploadFileAction, File, T> uploadFunc) {
        final File file = getOrCreateFile(action);
        if (file.getId() != null) {
            return stubConverter.apply(file);
        }
        return uploadFunc.apply(action, file);
    }

    protected File getOrCreateFile(UploadFileAction action) {
        File file = new File();
        if (!uploadConfig.isDisableCache()) {
            final File duplicateFile = getDuplicateFile(action);
            if (duplicateFile != null) {
                log.debug(
                        "upload file duplicate! md5:{} dest:{} file:{}",
                        action.getMd5(),
                        action.getDestPath(),
                        file);
                return file;
            }
        }
        return file;
    }

    protected <T extends FileStub> T retryTemplate(
            UploadFileAction action, Function<UploadFileAction, T> doUpload) {
        try {
            return RetryerBuilder.<T>newBuilder()
                    .retryIfExceptionOfType(FileUploadException.class)
                    .withWaitStrategy(WaitStrategies.fixedWait(2, TimeUnit.SECONDS))
                    .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                    .withRetryListener(
                            new RetryListener() {
                                @Override
                                public <V> void onRetry(Attempt<V> attempt) {
                                    if (attempt.hasException()) {
                                        log.error(
                                                "upload fail, file:{}, retry:{}, delay:{}",
                                                action.getFileName(),
                                                attempt.getAttemptNumber(),
                                                attempt.getDelaySinceFirstAttempt(),
                                                attempt.getExceptionCause());
                                    }
                                }
                            })
                    .build()
                    .wrap(() -> doUpload.apply(action))
                    .call();
        } catch (ExecutionException | RetryException e) {
            log.error(
                    "upload fail finally, file:{}, last exception:{}",
                    action.getFileName(),
                    e.getCause().getMessage(),
                    e.getCause());
            throw new FileUploadException(
                    String.format("upload fail, file:%s", action.getFileName()));
        }
    }

    private File getDuplicateFile(UploadFileAction action) {
        return fileService
                .lambdaQuery()
                .eq(File::getPath, action.getDestPath())
                .or()
                .eq(File::getMd5, action.getMd5())
                .orderByDesc(File::getId)
                .last(limit(1))
                .one();
    }

    @Override
    public Map<String, File> fileQueryBatchByUrls(Collection<String> fileUrls) {
        if (CollectionUtil.isEmpty(fileUrls)) {
            return Collections.emptyMap();
        }
        final Map<String, String> pathMap =
                fileUrls.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        urlStr -> getDecodedPath(URLUtil.url(urlStr)),
                                        (a, b) -> a));
        final Map<String, File> fileMap =
                fileService.lambdaQuery().in(File::getPath, pathMap.values()).list().stream()
                        .collect(Collectors.toMap(File::getPath, Function.identity()));
        return fileUrls.stream()
                .filter(
                        urlStr ->
                                Objects.nonNull(pathMap.get(urlStr))
                                        && fileMap.containsKey(pathMap.get(urlStr)))
                .collect(
                        Collectors.toMap(
                                Function.identity(),
                                urlStr -> fileMap.get(pathMap.get(urlStr)),
                                (a, b) -> a));
    }

    @Override
    public Map<Long, File> fileQueryBatchByIds(Collection<Long> fileIds) {
        if (CollectionUtil.isEmpty(fileIds)) {
            return Collections.emptyMap();
        }
        return fileService.listByIds(fileIds).stream()
                .collect(Collectors.toMap(File::getId, Function.identity()));
    }

}
