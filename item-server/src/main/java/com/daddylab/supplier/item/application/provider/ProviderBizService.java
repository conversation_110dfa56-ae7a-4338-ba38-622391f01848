package com.daddylab.supplier.item.application.provider;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.controller.compensate.RestartSyncProviderCmd;
import com.daddylab.supplier.item.controller.provider.dto.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/9 3:09 下午
 * @description
 */
public interface ProviderBizService {

    /**
     * 新建或者更新 供应商
     *
     * @param cmd
     */
    Long saveOrUpdate(ProviderCmd cmd);

    /**
     * 分页查询 供应商列表
     *
     * @param pageQuery
     * @return
     */
    PageResponse<ProviderViewVo> queryPage(ProviderPageQuery pageQuery);

    /**
     * 查询某个供应商详情
     *
     * @param id 供应商id
     * @return
     */
    SingleResponse<ProviderVO> queryDetail(Long id);

    void remove(Long id);

    /**
     * 批量查询供应商名称
     */
    SingleResponse<Map<Long, String>> batchQueryProviderName(List<Long> providerIds);

    List<Provider> batchQueryProvider(List<Long> providerIds);

    /**
     * 供应商下拉选
     *
     * @param cmd 模糊查询，非必填
     * @return 供应商名字, 供应商id
     */
    MultiResponse<ProviderDropDownVo> dropDownList(ProviderDropDownCmd cmd);

    /**
     * 查询合作伙伴系统，模糊查询供应商信息
     *
     * @param pageQuery
     * @return
     */
    MultiResponse<PartnerProviderVo> fuzzyQuery(PartnerProviderPageQuery pageQuery);

    /**
     * 查询 供应商联系人
     *
     * @param name
     * @return
     */
    MultiResponse<String> contactList(String name);

    /**
     * 根据名称查询供应商手机号
     *
     * @param name 供应商name
     * @return
     */
    String queryPhoneByName(String name);

    /**
     * 根据名称查询供应商
     *
     * @param name 供应商名称
     * @return 供应商
     */
    List<Provider> queryProviderByName(String name);

    void restartSyncProvider(RestartSyncProviderCmd cmd);

    /**
     * 查询店铺详情
     *
     * @param mallShopId
     * @return
     */
    SingleResponse<ProviderShopDetail> queryShopDetail(String mallShopId);


    /**
     * 更新或者新增 供应商的银行相关信息
     *
     * @param id
     * @param cardNo
     * @param bank
     */
    void saveOrUpdateBankInfo(Long id, String cardNo, String bank, String bankNo);

    Boolean batchChangeCharger(BatchChangeChargerCmd cmd);

}
