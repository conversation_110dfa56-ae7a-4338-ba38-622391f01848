package com.daddylab.supplier.item.application.bank;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.types.bank.BankNumberQuery;
import com.daddylab.supplier.item.types.bank.BankSimpleInfo;

/**
 * <AUTHOR>
 * @since 2023/11/20
 */
public interface BankNumberBizService {
    Response sync(BankNumberSyncCmd cmd);

    PageResponse<BankSimpleInfo> querySimpleInfo(BankNumberQuery query);
}
