package com.daddylab.supplier.item.controller.dev;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSalesForwarding.AfterSalesForwardingRegisterBizService;
import com.daddylab.supplier.item.application.afterSalesForwarding.types.FixDataRequest;
import com.daddylab.supplier.item.application.drawer.ItemDrawerLinkIdExtractTask;
import com.daddylab.supplier.item.application.drawer.impl.ItemLaunchPlanRecoverService;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.item.tasks.ImportItemPriceTask;
import com.daddylab.supplier.item.application.item.tasks.ImportItemPropsTask;
import com.daddylab.supplier.item.application.item.tasks.ImportItemStdNameTask;
import com.daddylab.supplier.item.application.itemSync.ItemSyncTbExternalTaskQueryTask;
import com.daddylab.supplier.item.application.itemSync.ItemSyncTbExternalTaskStartTask;
import com.daddylab.supplier.item.application.payment.PaymentOrderBizService;
import com.daddylab.supplier.item.application.recognitionTask.ReviewThesaurusRefreshTask;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.tmpJob.ProviderHandoverTask;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.dto.GenericIdsBody;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.smsAuth.SmsAuthService;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.KdyLogisticMapping;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemCodeRefService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IKdyLogisticMappingService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtStockSpecService;
import com.daddylab.supplier.item.infrastructure.submail.SMSTemplate;
import com.daddylab.supplier.item.infrastructure.submail.SubMailException;
import com.daddylab.supplier.item.infrastructure.submail.SubMailService;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.stockSpec.StockSpecSaveBatchEvent;
import com.google.common.collect.ImmutableMap;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/12/31
 */
@Api()
@Slf4j
@RestController
@RequestMapping("/dev")
public class DevController {

  @PostMapping(value = "/redis")
  public SingleResponse<String> redis(@RequestBody Map<String, String> params) {
    String value = "";
    if (StringUtil.equalsIgnoreCase(params.get("type"), "set")) {
      RedisUtil.set(params.get("key"), params.get("value"));
    }
    if (StringUtil.equalsIgnoreCase(params.get("type"), "del")) {
      RedisUtil.del(params.get("key"));
    }
    if (StringUtil.equalsIgnoreCase(params.get("type"), "get")) {
      value = RedisUtil.get(params.get("key"));
    }
    if (StringUtil.equalsIgnoreCase(params.get("type"), "ttl")) {
      value = RedisUtil.getExpire(params.get("key")).toString();
    }
    return SingleResponse.of(value);
  }

  @PostMapping(value = "/test")
  public SingleResponse<String> test(Map<String, String> params) throws SubMailException {
    ApplicationContextUtil.getBean(SubMailService.class).send("18072885161", "测试塞邮云");
    ApplicationContextUtil.getBean(SubMailService.class)
        .send(
            "18072885161",
            SMSTemplate.COMMON_CODE,
            ImmutableMap.of("code", "666666", "time", "10分钟"));
    return SingleResponse.of("params:" + params.toString());
  }

  @PostMapping(value = "/newGoods/importExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @ApiOperation("新品商品导入Excel")
  public Response newGoodsImportExcel(
      @ApiParam(name = "file", value = "文件", required = true) @RequestParam("file")
          MultipartFile file) {
    try {
      ApplicationContextUtil.getBean(NewGoodsBizService.class).importExcel(file.getInputStream());
      return Response.buildSuccess();
    } catch (IOException e) {
      throw ExceptionPlusFactory.bizException(ErrorCode.FILE_UPLOAD_ERROR, "导入Excel失败，读取文件异常");
    }
  }

  @PostMapping(value = "/itemPrice/importExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @ApiOperation("后端商品导入日销价")
  public Response itemPriceImportExcel(
      @ApiParam(name = "file", value = "文件", required = true) @RequestParam("file")
          MultipartFile file,
      @RequestParam(value = "sheetNo", defaultValue = "0") Integer sheetNo) {
    try {
      ApplicationContextUtil.getBean(ImportItemPriceTask.class)
          .importExcel(file.getInputStream(), sheetNo);
      return Response.buildSuccess();
    } catch (IOException e) {
      throw ExceptionPlusFactory.bizException(ErrorCode.FILE_UPLOAD_ERROR, "导入Excel失败，读取文件异常");
    }
  }

  @PostMapping(
      value = "/item/standardName/importExcel",
      consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @ApiOperation("商品导入标准名")
  public Response itemStandardNameImportExcel(
      @ApiParam(name = "file", value = "文件", required = true) @RequestParam("file")
          MultipartFile file,
      @RequestParam(value = "sheetNo", defaultValue = "0") Integer sheetNo) {
    try {
      ApplicationContextUtil.getBean(ImportItemStdNameTask.class)
          .importExcel(file.getInputStream(), sheetNo);
      return Response.buildSuccess();
    } catch (IOException e) {
      throw ExceptionPlusFactory.bizException(ErrorCode.FILE_UPLOAD_ERROR, "导入Excel失败，读取文件异常");
    }
  }

  //    @PostMapping(value = "/item/", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  //    @ApiOperation("导入Excel")
  //    @Auth(noAuth = true)
  //    public Response itemImportExcel(@RequestParam("file") MultipartFile file) {
  //        try {
  //
  // ApplicationContextUtil.getBean(ItemBizService.class).importExcel(file.getInputStream());
  //            return Response.buildSuccess();
  //        } catch (IOException e) {
  //            throw ExceptionPlusFactory.bizException(ErrorCode.FILE_UPLOAD_ERROR,
  // "导入Excel失败，读取文件异常");
  //        }
  //    }

  //    @PostMapping(value = "/item/providerExcel", consumes =
  // MediaType.MULTIPART_FORM_DATA_VALUE)
  //    @ApiOperation("物料供应商excel信息补偿erp数据[帮财务填坑]")
  //    @Auth(noAuth = true)
  //    public Response providerExcel(@RequestParam("file") MultipartFile file) {
  //        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
  //            try {
  //
  // ApplicationContextUtil.getBean(ItemBizService.class).importExcelForProvider2(file.getInputStream());
  //                Alert.text(MessageRobotCode.GLOBAL, "物料供应商excel信息补偿erp数据[帮财务填坑]。执行完成");
  //            } catch (IOException e) {
  //                throw ExceptionPlusFactory.bizException(ErrorCode.FILE_UPLOAD_ERROR,
  // "导入Excel失败，读取文件异常");
  //            }
  //        });
  //        return Response.buildSuccess();
  //    }

  //    @GetMapping(value = "/item/updateSkuProvider")
  //    @ApiOperation("将erpSku的供应商同步到kingDee[帮财务填坑]")
  //    @Auth(noAuth = true)
  //    public Response updateSkuProvider() {
  //        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
  //            ApplicationContextUtil.getBean(ItemBizService.class).updateSkuProvider();
  //            Alert.text(MessageRobotCode.GLOBAL, "物料供应商excel信息补偿erp数据[帮财务填坑]。执行完成");
  //
  //        });
  //        return Response.buildSuccess();
  //    }

  @PostMapping(value = "/item/modifyItemCategory")
  @ApiOperation("后端商品修改商品类目")
  public Response itemModifyItemCategory(Long itemId, Long newCategoryId) {
    ApplicationContextUtil.getBean(CategoryGateway.class).modifyItemCategory(itemId, newCategoryId);
    return Response.buildSuccess();
  }

  //    @GetMapping(value = "/syncItemProviderAndBuyer")
  //    public Response syncItemProviderAndBuyer(@Param("type") Integer type) {
  //
  //        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
  //
  //            IItemSkuService iItemService = SpringUtil.getBean(IItemSkuService.class);
  //            int pageIndex = 0;
  //            int pageSize = 200;
  //            ReqJsonUtil reqJsonUtil = SpringUtil.getBean(ReqJsonUtil.class);
  //            ReqTemplate reqTemplate = SpringUtil.getBean(ReqTemplate.class);
  //            Integer count = iItemService.lambdaQuery().ne(ItemSku::getKingDeeId, "")
  //                    .isNotNull(ItemSku::getKingDeeId)
  //                    .select().count();
  //            int pageTotal = count % pageSize == 0 ? (count / pageSize) : (count / pageSize) +
  // 1;
  //
  //            while (pageIndex <= pageTotal) {
  //                PageHelper.startPage(pageIndex, pageSize).doSelectPageInfo(() ->
  // iItemService.lambdaQuery().ne(ItemSku::getKingDeeId, "")
  //                        .isNotNull(ItemSku::getKingDeeId)
  //                        .select().list()).getList().forEach(val -> {
  //                    ItemSku itemSku = (ItemSku) val;
  //                    try {
  //                        log.info("---- itemSkuId:{} -----", itemSku.getId());
  //                        String commonReqJson =
  // reqJsonUtil.commonReqJson(itemSku.getKingDeeId(), "BD_MATERIAL");
  //                        reqTemplate.unAudit(commonReqJson);
  //                        String updateSkuProviderAndBuyerJson =
  // reqJsonUtil.getUpdateSkuProviderAndBuyerJson(itemSku, type);
  //                        reqTemplate.save(updateSkuProviderAndBuyerJson);
  //                    } catch (Exception e) {
  //                        log.error("syncItemProviderAndBuyer 处理异常。itemSkuId:{}",
  // itemSku.getItemId(), e);
  //                    }
  //                });
  //
  //                pageIndex = pageIndex + 1;
  //            }
  //
  //        });
  //
  //        return Response.buildSuccess();
  //    }

  @PostMapping(value = "/item/itemSyncTbExternalTaskStartTask")
  public Response itemSyncTbExternalTaskStartTask() {
    ApplicationContextUtil.getBean(ItemSyncTbExternalTaskStartTask.class).run();
    return Response.buildSuccess();
  }

  @PostMapping(value = "/item/itemSyncTbExternalTaskQueryTask")
  public Response itemSyncTbExternalTaskQueryTask() {
    ApplicationContextUtil.getBean(ItemSyncTbExternalTaskQueryTask.class).run();
    return Response.buildSuccess();
  }

  @PostMapping(value = "/itemDrawerLinkIdExtractTask")
  public Response itemDrawerLinkIdExtractTask() {
    ApplicationContextUtil.getBean(ItemDrawerLinkIdExtractTask.class).doTask();
    return Response.buildSuccess();
  }

  @PostMapping(value = "/initializeItemCodeRef")
  public SingleResponse<Integer> initializeItemCodeRef() {
    final int initializedCount =
        ApplicationContextUtil.getBean(IItemCodeRefService.class).refresh();
    return SingleResponse.of(initializedCount);
  }

  @PostMapping(value = "/item/props/importExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @ApiOperation("商品属性导入")
  public SingleResponse<String> importItemProps(
      @ApiParam(name = "file", value = "文件", required = true) @RequestParam("file")
          MultipartFile file) {
    try {
      final String resultPath =
          ApplicationContextUtil.getBean(ImportItemPropsTask.class).doTask(file.getInputStream());
      return SingleResponse.of(resultPath);

    } catch (IOException e) {
      throw ExceptionPlusFactory.bizException(ErrorCode.FILE_UPLOAD_ERROR, "导入Excel失败，读取文件异常");
    }
  }

  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class PaymentOrderWithdrawThenReSubmitCmd extends GenericIdsBody<Long> {

    private String comment = "系统操作";
    private int sleepSecondsBeforeSubmit = 15;
  }

  @PostMapping(value = "/paymentOrderWithdrawThenReSubmit")
  public Response paymentOrderWithdrawThenReSubmit(
      @RequestBody PaymentOrderWithdrawThenReSubmitCmd cmd) {
    final PaymentOrderBizService bean =
        ApplicationContextUtil.getBean(PaymentOrderBizService.class);
    for (Long id : cmd.getIds()) {
      try {
        final Response withdraw = bean.withdraw(id, cmd.getComment());
        if (withdraw.isSuccess()) {
          log.info("【付款单撤回重新提交】撤回成功:{}", id);
        } else {
          log.error("【付款单撤回重新提交】撤回失败:{}:{}", id, withdraw.getErrMessage());
        }
      } catch (Exception e) {
        log.error("【付款单撤回重新提交】撤回失败:{}", id, e);
      }
    }
    ThreadUtil.sleep(cmd.getSleepSecondsBeforeSubmit() * 1000);
    for (Long id : cmd.getIds()) {
      try {
        final Response submit = bean.submit(id);
        if (submit.isSuccess()) {
          log.info("【付款单撤回重新提交】重新提交成功:{}", id);
        } else {
          log.error("【付款单撤回重新提交】重新提交失败:{}:{}", id, submit.getErrMessage());
        }
      } catch (Exception e) {
        log.error("【付款单撤回重新提交】重新提交失败:{}", id, e);
      }
    }
    return Response.buildSuccess();
  }

  @PostMapping(value = "/itemLaunchPlanRecover")
  public Response itemLaunchPlanRecover(@RequestBody @Validated GenericIdsBody<Long> body) {
    SpringUtil.getBean(ItemLaunchPlanRecoverService.class).recoverRef(body.getIds());
    return Response.buildSuccess();
  }

  @PostMapping(value = "/kdy/logisticMapping")
  public Response kdyLogisticMapping(@RequestBody List<KdyLogisticMapping> mappings) {
    SpringUtil.getBean(IKdyLogisticMappingService.class).saveBatch(mappings);
    return Response.buildSuccess();
  }

  @Data
  public static class StockSpecSaveBatchEventCmd {

    private List<Long> saveModelIds = Collections.emptyList();
    private List<Long> updateModelIds = Collections.emptyList();
  }

  @PostMapping(value = "/stockSpec/saveBatchEvent")
  public Response triggerStockSpecSaveBatchEvent(@RequestBody StockSpecSaveBatchEventCmd cmd) {
    final IWdtStockSpecService bean = SpringUtil.getBean(IWdtStockSpecService.class);
    final StockSpecSaveBatchEvent event = new StockSpecSaveBatchEvent();
    Optional.ofNullable(cmd.getSaveModelIds())
        .filter(CollUtil::isNotEmpty)
        .map(bean::listByIds)
        .ifPresent(event::setSaveModels);
    Optional.ofNullable(cmd.getUpdateModelIds())
        .filter(CollUtil::isNotEmpty)
        .map(bean::listByIds)
        .ifPresent(event::setUpdateModels);
    EventBusUtil.post(event);
    return Response.buildSuccess();
  }

  @ResponseBody
  @ApiOperation(value = "转寄登记数据修复")
  @PostMapping("/afterSales/forwardingRegister/fixData")
  public Response fixData(@RequestBody @Validated FixDataRequest cmd) {
    return SpringUtil.getBean(AfterSalesForwardingRegisterBizService.class).fixData(cmd);
  }

  @ApiOperation(value = "供应商交接")
  @PostMapping(value = "/provider/handover", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public Response providerHandover(
      @ApiParam(name = "file", value = "文件", required = true) @RequestParam("file")
          MultipartFile file) {
    try {
      SpringUtil.getBean(ProviderHandoverTask.class).doTask(file.getInputStream());
      return Response.buildSuccess();
    } catch (Exception e) {
      log.error("[供应商交接]处理异常", e);
      return Response.buildFailure(
          ErrorCode.FILE_UPLOAD_ERROR.getCode(), "供应商交接，处理异常:" + e.getMessage());
    }
  }

  @ApiOperation(value = "供应商交接补偿")
  @PostMapping(value = "/provider/handover/offset", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public Response providerHandoverOffset(
      @ApiParam(name = "file", value = "文件", required = true) @RequestParam("file")
          MultipartFile file) {
    try {
      SpringUtil.getBean(ProviderHandoverTask.class).taskOffset(file.getInputStream());
      return Response.buildSuccess();
    } catch (Exception e) {
      log.error("[供应商交接补偿]处理异常", e);
      return Response.buildFailure(
          ErrorCode.FILE_UPLOAD_ERROR.getCode(), "供应商交接补偿，处理异常:" + e.getMessage());
    }
  }

  @PostMapping(value = "/item/importExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @ApiOperation("后端商品导入Excel")
  public Response itemImportExcel(
      @ApiParam(name = "file", value = "文件", required = true) @RequestParam("file")
          MultipartFile file) {
    try {
      SpringUtil.getBean(ItemBizService.class).importExcel(file.getInputStream());
      return Response.buildSuccess();
    } catch (IOException e) {
      throw ExceptionPlusFactory.bizException(ErrorCode.FILE_UPLOAD_ERROR, "导入Excel失败，读取文件异常");
    }
  }
}
