package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayApplyAuditStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 申请付款单审核流节点审核详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PayApplyAuditProcessNode implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 创建者（关联入库单/出库单/其他应付的创建人）
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 审核节点id
     */
    private Long nodeId;

    /**
     * 审核角色名称
     */
    private String roleName;

    /**
     * 1:审核通过。-1:审核拒绝。0：未处理
     */
    private PayApplyAuditStatus auditStatus;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 审核意见
     */
    private String opinion;

    private Integer autoAudit;


}
