package com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/25 3:48 下午
 * @description
 */
public interface IDaddyService<T> extends IService<T> {

    default boolean removeByIdWithTime(Serializable id) {
        return SqlHelper.retBool(this.getDaddyBaseMapper().deleteByIdWithTime(id));
    }

    default boolean removeByMapWithTime(Map<String, Object> columnMap) {
        Assert.notEmpty(columnMap, "error: columnMap must not be empty");
        return SqlHelper.retBool(this.getDaddyBaseMapper().deleteByMapWithTime(columnMap));
    }

    default boolean removeWithTime(Wrapper<T> queryWrapper) {
        return SqlHelper.retBool(this.getDaddyBaseMapper().deleteWithTime(queryWrapper));
    }

    default boolean removeByIdsWithTime(Collection<? extends Serializable> idList) {
        return !CollectionUtils.isEmpty(idList) && SqlHelper.retBool(this.getDaddyBaseMapper().deleteBatchIdsWithTime(idList));
    }

    DaddyBaseMapper<T> getDaddyBaseMapper();

    default List<T> selectBatchIds(List<Long> ids) {
        return this.getDaddyBaseMapper().selectBatchIds(ids);
    }
}
