package com.daddylab.supplier.item.infrastructure.gatewayimpl.auth;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.auth.PermRefreshManager;
import com.daddylab.supplier.item.domain.auth.entity.SysMenu;
import com.daddylab.supplier.item.domain.auth.entity.SysResource;
import com.daddylab.supplier.item.domain.auth.enums.ResourceType;
import com.daddylab.supplier.item.domain.auth.factories.MenuFactory;
import com.daddylab.supplier.item.domain.auth.trans.AuthAssembler;
import com.daddylab.supplier.item.domain.auth.types.SimpleRole;
import com.daddylab.supplier.item.domain.auth.types.UserAccessControlInfo;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CasbinRule;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Resource;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Role;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.UserRole;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICasbinRuleService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IResourceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IRoleService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IUserRoleService;
import com.daddylab.supplier.item.infrastructure.userInfoCrypto.UserInfoCrypto;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/3/30
 */
@Slf4j
@Service("UserContextGatewayDbImpl")
public class UserContextGatewayDbImpl extends UserContextGatewayImplBase {
    private final IResourceService resourceService;
    private final ICasbinRuleService casbinRuleService;
    private final IUserRoleService userRoleService;
    private final IRoleService roleService;
    private final UserGateway userGateway;
    private final Long systemId;
    @Autowired
    private StaffService staffService;
    @Autowired
    private UserInfoCrypto userInfoCrypto;

    public UserContextGatewayDbImpl(PermRefreshManager permRefreshManager,
                                    IResourceService resourceService,
                                    ICasbinRuleService casbinRuleService,
                                    IUserRoleService userRoleService,
                                    IRoleService roleService,
                                    UserGateway userGateway,
                                    @Value("${org.casbin.dom:6}") Long systemId,
                                    RedissonClient redissonClient) {
        super(permRefreshManager, redissonClient);
        this.resourceService = resourceService;
        this.casbinRuleService = casbinRuleService;
        this.userRoleService = userRoleService;
        this.roleService = roleService;
        this.userGateway = userGateway;
        this.systemId = systemId;
    }

    UserContext.UserInfo getUserContext0(long userId) {
        UserContext.UserInfo userinfo;
        final UserContext.UserInfo.UserInfoBuilder builder = UserContext.UserInfo.builder();
        builder.userId(userId);


        //查询当前用户名称
//        final StaffInfo staffInfo = userGateway.queryStaffInfoById(userId);
        DadStaffVO staffInfo = staffService.getStaff(userId);
        if (staffInfo == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未查询到当前身份的用户信息");
        }

        //真实姓名
        builder.userName(staffInfo.getRealName());
        //花名
        builder.nickname(staffInfo.getNickname());
        //邮件地址
        builder.mail(staffInfo.getEmail());
        //登录名
        builder.loginName(staffInfo.getLoginName());
        //手机号
        builder.mobileNum(userInfoCrypto.decrypt(staffInfo.getMobile()));

        //查询用户有权限访问的资源
        final UserAccessControlInfo userAccessControlInfo = getUserAcInfo(userId);
        builder.resourceCodes(userAccessControlInfo.getCodesOfResourcesOwned());
        builder.roles(userAccessControlInfo.getRoles());
        builder.roleIds(userAccessControlInfo.getRoles().stream().map(SimpleRole::getRoleId).collect(Collectors.toList()));
        builder.menus(userAccessControlInfo.getMenus());
        builder.queryTime(LocalDateTime.now());
        builder.deptId(userAccessControlInfo.getDeptId());
        builder.dept(userAccessControlInfo.getDept());
        builder.topDeptId(userAccessControlInfo.getTopDeptId());
        builder.topDept(userAccessControlInfo.getTopDept());

        userinfo = builder.build();
        return userinfo;
    }

    private UserAccessControlInfo getUserAcInfo(Long userId) {
        final UserAccessControlInfo userAccessControlInfo = new UserAccessControlInfo(userId);

        //用户所拥有的角色
        LambdaQueryWrapper<UserRole> userRoleQuery = Wrappers.lambdaQuery();
        userRoleQuery.eq(UserRole::getUserId, userId).eq(UserRole::getSystemId, systemId);
        final List<UserRole> roleList = userRoleService.list(userRoleQuery);

        if (CollUtil.isEmpty(roleList)) {
            return userAccessControlInfo;
        }

        //CasbinRule表中角色有前缀r
        final List<Long> roleIds = roleList.stream().map(UserRole::getRoleId)
                .collect(Collectors.toList());

        //获取角色名称
        final Map<Long, String> roleNameMap = roleService.lambdaQuery()
                .in(Role::getId, roleIds).select(Role::getName, Role::getId).list()
                .stream().collect(Collectors.toMap(Role::getId, Role::getName));

        //构造角色
        final List<SimpleRole> roles = roleIds.stream().map(it -> new SimpleRole(it, roleNameMap.getOrDefault(it, "")))
                .collect(Collectors.toList());

        userAccessControlInfo.setRoles(roles);

        //CasbinRule表中角色有前缀r
        final List<String> casbinRoleIds = roleList.stream().map(userRole -> "r" + userRole.getRoleId())
                .collect(Collectors.toList());

        //角色配置的资源
        LambdaQueryWrapper<CasbinRule> policyQuery = Wrappers.lambdaQuery();
        policyQuery.eq(CasbinRule::getPtype, "p")
                .eq(CasbinRule::getV1, systemId)
                .in(CasbinRule::getV0, casbinRoleIds);
        final List<CasbinRule> casbinRules = casbinRuleService.list(policyQuery);
        if (CollUtil.isEmpty(casbinRules)) {
            return userAccessControlInfo;
        }

        final List<Long> pageResources = new ArrayList<>();
        final List<String> apiResources = new ArrayList<>();
        for (CasbinRule casbinRule : casbinRules) {
            if (ReUtil.isMatch("^\\d+$", casbinRule.getV2())) {
                pageResources.add(Long.parseLong(casbinRule.getV2()));
            } else {
                apiResources.add(casbinRule.getV2());
            }
        }

        //查询当前系统全部资源
        LambdaQueryWrapper<Resource> resourceQuery = Wrappers.lambdaQuery();
        resourceQuery.eq(Resource::getSystemId, systemId);
        final List<Resource> resourceList = resourceService.list(resourceQuery);
        if (CollUtil.isEmpty(resourceList)) {
            return userAccessControlInfo;
        }

        final List<Resource> resourcesOwned = resourceList.stream()
                .filter(it -> pageResources.contains(it.getId()) || apiResources.contains(it.getApiUrl()))
                .collect(Collectors.toList());

        final List<SysResource> sysResourcesOwned = AuthAssembler.INSTANCE.dbResourcesToSysResources(resourcesOwned);
        final Map<ResourceType, List<String>> codesOfResourcesOwned = extractCodesGroupingByType(sysResourcesOwned);
        userAccessControlInfo.setCodesOfResourcesOwned(codesOfResourcesOwned);

        final ImmutableList<String> frontResourcesType = ImmutableList.of(ResourceType.PAGE.getValue(),
                ResourceType.LEFT_MENU.getValue(), ResourceType.BUTTON.getValue(), ResourceType.MENU.getValue());
        final List<Resource> frontResources = resourceList.stream()
                .filter(it -> frontResourcesType.contains(it.getType()))
                .collect(Collectors.toList());
        final List<SysResource> sysResources = AuthAssembler.INSTANCE.dbResourcesToSysResources(frontResources);
        final List<SysMenu> menus = MenuFactory.buildMenuList(systemId, codesOfResourcesOwned, sysResources);
        userAccessControlInfo.setMenus(menus);

        return userAccessControlInfo;
    }


}
