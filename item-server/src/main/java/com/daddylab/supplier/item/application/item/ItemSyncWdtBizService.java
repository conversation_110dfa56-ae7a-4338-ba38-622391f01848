package com.daddylab.supplier.item.application.item;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.cola.exception.BizException;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.goods.GoodsAPI;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsPushGoodsInfo;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsPushSpecInfo;
import com.daddylab.mall.wdtsdk.apiv2.purchase.ProviderGoodsAPI;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.ProviderGoodsUploadPurchaseProviderGoods;
import com.daddylab.supplier.item.application.criticalPush.ManagedPush;
import com.daddylab.supplier.item.application.criticalPush.SourceType;
import com.daddylab.supplier.item.application.criticalPush.TargetType;
import com.daddylab.supplier.item.application.warehouse.WarehouseGateway;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.brand.entity.BrandEntity;
import com.daddylab.supplier.item.domain.brand.gateway.BrandGateway;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemImageGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemProcurementGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.GoodsType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.SkuSplitType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBaseUnitService;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2022/5/10
 */
@Service
@Slf4j
public class ItemSyncWdtBizService {

    private final ItemGateway itemGateway;
    private final ItemSkuGateway itemSkuGateway;
    private final BrandGateway brandGateway;
    private final CategoryGateway categoryGateway;
    private final WdtGateway wdtGateway;
    private final IBaseUnitService baseUnitService;
    private final ItemProcurementGateway itemProcurementGateway;
    private final WarehouseGateway warehouseGateway;
    private final ItemImageGateway itemImageGateway;
    private final ProviderGateway providerGateway;

    public ItemSyncWdtBizService(ItemGateway itemGateway,
            ItemSkuGateway itemSkuGateway,
            BrandGateway brandGateway,
            CategoryGateway categoryGateway,
            WdtGateway wdtGateway,
            IBaseUnitService baseUnitService,
            ItemProcurementGateway itemProcurementGateway,
            WarehouseGateway warehouseGateway,
            ItemImageGateway itemImageGateway, ProviderGateway providerGateway) {
        this.itemGateway = itemGateway;
        this.itemSkuGateway = itemSkuGateway;
        this.brandGateway = brandGateway;
        this.categoryGateway = categoryGateway;
        this.wdtGateway = wdtGateway;
        this.baseUnitService = baseUnitService;
        this.itemProcurementGateway = itemProcurementGateway;
        this.warehouseGateway = warehouseGateway;
        this.itemImageGateway = itemImageGateway;
        this.providerGateway = providerGateway;
    }

    /**
     * 直接推送商品到旺店通，不经过代理类包装
     *
     * @param itemIds 商品ID集合
     */
    public void syncItemToWdtByIds(List<Long> itemIds) {
        final List<Item> items = itemGateway.getItemBatchByIds(itemIds);
        if (CollectionUtil.isEmpty(items)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未找到商品记录");
        }
        for (Item item : items) {
            log.info("[商品批量同步至旺店通]同步商品:{}", item.getId());
            try {
                syncItemToWdtInternal(item);
            } catch (Exception e) {
                if (e instanceof BizException) {
                    final BizException be = (BizException) e;
                    if (Objects.equals(be.getErrCode(), ErrorCode.GATEWAY_BUSY.getCode())) {
                        ThreadUtil.sleep(15000);
                    }
                }
                log.error("[商品批量同步至旺店通]同步异常,商品:{}", item.getId(), e);
            }
        }
    }

    /**
     * 直接推送商品到旺店通，不经过代理类包装
     *
     * @param itemCodes 商品编号集合
     */
    public void syncItemToWdtByCodes(List<String> itemCodes) {
        final List<Item> items = itemGateway.getItemBatchByCodes(itemCodes);
        if (CollectionUtil.isEmpty(items)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未找到商品记录");
        }
        for (Item item : items) {
            log.info("[商品批量同步至旺店通]同步商品:{}", item.getId());
            try {
                syncItemToWdtInternal(item);
            } catch (Exception e) {
                if (e instanceof BizException) {
                    final BizException be = (BizException) e;
                    if (Objects.equals(be.getErrCode(), ErrorCode.GATEWAY_BUSY.getCode())) {
                        ThreadUtil.sleep(15000);
                    }
                }
                log.error("[商品批量同步至旺店通]同步异常,商品:{}", item.getId(), e);
            }
        }
    }

    /**
     * 直接推送商品到旺店通，不经过代理类包装
     *
     * @param itemId 商品ID
     */
    public void syncItemToWdt(Long itemId) {
        final Item item = itemGateway.getItem(itemId);
        if (item == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未找到商品记录");
        }
        syncItemToWdtInternal(item);
    }

    /**
     * 直接推送商品到旺店通，不经过代理类包装
     *
     * @param itemCode 商品编码
     */
    public void syncItemToWdt(String itemCode) {
        final Item item = itemGateway.getItem(itemCode);
        if (item == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未找到商品记录");
        }
        syncItemToWdtInternal(item);
    }

    /**
     * 直接推送商品到旺店通，不经过代理类包装
     *
     * @param item 商品
     */
    public void syncItemToWdt(Item item) {
        syncItemToWdtInternal(item);
    }

    /**
     * 推送后端商品到旺店通，经过代理类包装
     *
     * @param itemId 商品ID
     */
    @ManagedPush(sourceType = SourceType.ITEM, targetType = TargetType.WDT, async = true)
    public void syncItemToWdtManaged(Long itemId) {
        final Item item = itemGateway.getItem(itemId);
        if (item == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未找到商品记录");
        }
        syncItemToWdtInternal(item);
    }

    private void syncItemToWdtInternal(Item item) {
        final Long itemId = item.getId();
        final GoodsAPI api = wdtGateway.getAPI(GoodsAPI.class, wdtGateway.randomConfigIndex(), true);

        final Optional<ItemProcurement> itemProcurementOpt = Optional
                .ofNullable(itemProcurementGateway
                        .getProcurementByItemId(itemId));

        final Optional<BrandEntity> brandEntityOpt = Optional.ofNullable(item.getBrandId())
                .map(brandGateway::getBrand);

        final Optional<Category> categoryOpt = Optional.ofNullable(item.getCategoryId())
                .map(categoryGateway::getById);

        final String unitName = itemProcurementOpt.map(ItemProcurement::getBaseUnitId)
                .map(baseUnitService::getById).map(
                        BaseUnit::getName).orElse(null);

        final GoodsPushGoodsInfo goodsInfo = new GoodsPushGoodsInfo();
        goodsInfo.setGoodsNo(item.getSupplierCode());
        goodsInfo.setGoodsName(item.getName());
        goodsInfo.setClassName(categoryOpt.map(Category::getName).orElse(""));
        goodsInfo.setBrandName(brandEntityOpt.map(BrandEntity::getName).orElse(""));
        goodsInfo.setUnitName(unitName);
        goodsInfo.setAuxUnitName(null);
        goodsInfo.setFlagName(null);
        goodsInfo.setGoodsType(1);
        goodsInfo.setShortName(item.getShortName());
        goodsInfo.setAlias(null);
        goodsInfo.setOrigin(null);
        goodsInfo.setRemark(null);

        //仓库
        goodsInfo.setProp1(Optional.ofNullable(item.getWarehouseNo())
                .map(warehouseNo -> warehouseGateway.getWarehouse(warehouseNo)
                        .map(Warehouse::getName).orElseThrow(
                                () -> ExceptionPlusFactory
                                        .bizException(ErrorCode.DATA_NOT_FOUND,
                                                "商品关联的仓库不存在，itemId:" + item.getId())))
                .orElse(null));
        //税率编码
        final String taxRateCode = itemProcurementOpt.map(ItemProcurement::getTaxRateCode)
                .orElse(null);
        goodsInfo.setProp2(taxRateCode);
        //税率
        final BigDecimal taxRate =
                itemProcurementOpt.map(ItemProcurement::getRate)
                        .map(v -> NumberUtil.mul(v, 100).setScale(0, RoundingMode.UNNECESSARY))
                        .orElse(null);
        goodsInfo.setProp3(taxRate != null ? taxRate.toPlainString() : null);
        goodsInfo.setProp4(null);
        goodsInfo.setProp5(null);
        goodsInfo.setProp6(null);
        goodsInfo.setAutoCreateBc(true);

        final List<ItemSku> skuList = itemSkuGateway.getSkuList(itemId);
        if (skuList.isEmpty()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "商品无规格");
        }

        final String itemMainImg = itemImageGateway.getItemMainImgUrl(itemId);

        final ArrayList<GoodsPushSpecInfo> specInfoList = new ArrayList<>();
        for (ItemSku itemSku : skuList) {
            final GoodsPushSpecInfo goodsPushSpecInfo = new GoodsPushSpecInfo();
            goodsPushSpecInfo.setSpecNo(itemSku.getSupplierCode());
            goodsPushSpecInfo.setBarcode(itemSku.getBarCode());
            goodsPushSpecInfo.setSpecName(itemSku.getSpecifications());
            goodsPushSpecInfo.setWmsProcessMask(0);
            goodsPushSpecInfo.setGoodsLabel(Optional.ofNullable(itemSku.getGoodsType())
                                                    .filter(v -> v != GoodsType.DEFAULT)
                                                    .map(GoodsType::getDesc)
                                                    .orElse(null));
            goodsPushSpecInfo.setSnType(0);
            goodsPushSpecInfo.setIsSingleBatch(0);
            goodsPushSpecInfo.setLowestPrice(itemSku.getCostPrice());
            goodsPushSpecInfo.setRetailPrice(itemSku.getSalePrice());
            goodsPushSpecInfo.setWholesalePrice(null);
            goodsPushSpecInfo.setMemberPrice(itemSku.getSalePrice());
            goodsPushSpecInfo.setMarketPrice(itemSku.getSalePrice());
            goodsPushSpecInfo.setValidityDays(null);
            goodsPushSpecInfo.setSalesDays(null);
            goodsPushSpecInfo.setReceiveDays(null);
            goodsPushSpecInfo.setWeight(null);
            goodsPushSpecInfo.setHeight(null);
            goodsPushSpecInfo.setLength(null);
            goodsPushSpecInfo.setWidth(null);
            goodsPushSpecInfo.setTaxRate(taxRate);
            //大件类型 默认0, 0非大件1普通大件2独立大件（不可和小件一起发) 3按箱规拆分 -1非单发件
            int largeType = mapLargeType(itemSku.getSplitType());
            goodsPushSpecInfo.setLargeType(largeType);
            goodsPushSpecInfo.setProp1(null);
            goodsPushSpecInfo.setProp2(null);
            goodsPushSpecInfo.setProp3(null);
            goodsPushSpecInfo.setProp4(null);
            goodsPushSpecInfo.setProp5(null);
            goodsPushSpecInfo.setProp6(null);
            goodsPushSpecInfo.setIsLowerCost(false);
            goodsPushSpecInfo.setImgUrl(itemMainImg);
            goodsPushSpecInfo.setRemark(null);
            goodsPushSpecInfo.setSaleScore(null);
            goodsPushSpecInfo.setPackScore(null);
            goodsPushSpecInfo.setPickScore(null);
            goodsPushSpecInfo.setSortScore(null);
            goodsPushSpecInfo.setScanScore(null);
            goodsPushSpecInfo.setSupplyScore(null);
            goodsPushSpecInfo.setShelveScore(null);
            goodsPushSpecInfo.setStockinScore(null);
            goodsPushSpecInfo.setInpsectScore(null);
            goodsPushSpecInfo.setTaxCode(null);
            goodsPushSpecInfo.setUnitName(unitName);
            goodsPushSpecInfo.setAuxUnitName(null);

            specInfoList.add(goodsPushSpecInfo);
        }

        try {
            api.push(goodsInfo, specInfoList);
        } catch (WdtErpException exception) {
            if (WdtExceptions.isLimitExceed(exception)) {
                throw ExceptionPlusFactory
                        .bizException(ErrorCode.GATEWAY_BUSY, exception.getMessage());
            }
            throw ExceptionPlusFactory
                    .bizException(ErrorCode.GATEWAY_ERROR, exception.getMessage());
        }
        itemGateway.setIsWdtSynced(itemId);

        if (Objects.nonNull(item.getProviderId())) {
            final Provider provider = providerGateway.getById(item.getProviderId());
            final ProviderGoodsAPI providerGoodsAPI = wdtGateway.getAPI(ProviderGoodsAPI.class);
            final ArrayList<ProviderGoodsUploadPurchaseProviderGoods> purchaseProviderGoodsList = new ArrayList<>();
            skuList.forEach(sku -> {
                final ProviderGoodsUploadPurchaseProviderGoods providerGoodsUploadPurchaseProviderGoods = new ProviderGoodsUploadPurchaseProviderGoods();
                providerGoodsUploadPurchaseProviderGoods.setProviderNo(provider.getProviderNo());
                providerGoodsUploadPurchaseProviderGoods.setSpecNo(sku.getSupplierCode());
                providerGoodsUploadPurchaseProviderGoods.setPrice(sku.getCostPrice());
                purchaseProviderGoodsList.add(providerGoodsUploadPurchaseProviderGoods);
            });
            try {
                providerGoodsAPI.upload(purchaseProviderGoodsList);
            } catch (WdtErpException e) {
                Alert.text(MessageRobotCode.GLOBAL, "推送供应商商品价格业务异常：" + e.getMessage());
            }
        }
    }

    private static int mapLargeType(SkuSplitType splitType) {
        int largeType = 0;
        switch (splitType) {
            case NO_SINGLE:
                largeType = -1;
                break;
            case NORMAL_LARGE:
                largeType = 1;
                break;
            case INDEPENDENCE_LARGE:
                largeType = 2;
                break;
            case SPLIT_BY_BOX:
                largeType = 3;
                break;
        }
        return largeType;
    }

    /**
     * 以单品形式同步
     *
     * @param api          旺店通货品API
     * @param goodsInfo    货品信息
     * @param specInfoList 规格信息
     */
    private void pushAsSingleItem(GoodsAPI api, GoodsPushGoodsInfo goodsInfo,
            ArrayList<GoodsPushSpecInfo> specInfoList) {
        for (GoodsPushSpecInfo goodsPushSpecInfo : specInfoList) {
            goodsInfo.setGoodsNo(goodsPushSpecInfo.getSpecNo());
            try {
                api.push(goodsInfo, Collections.singletonList(goodsPushSpecInfo));
            } catch (WdtErpException e) {
                throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, e.getMessage());
            }
        }
    }
}
