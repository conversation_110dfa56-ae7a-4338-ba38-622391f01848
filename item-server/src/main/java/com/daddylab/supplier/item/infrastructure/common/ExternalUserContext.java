package com.daddylab.supplier.item.infrastructure.common;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExternalUser;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/5/29
 */
public class ExternalUserContext {
    protected static final ThreadLocal<ExternalUser> USER_HOLDER = new ThreadLocal<>();

    public static void set(ExternalUser user) {
        USER_HOLDER.set(user);
    }

    public static ExternalUser get() {
        return USER_HOLDER.get();
    }

    public static Optional<ExternalUser> getOptional() {
        return Optional.ofNullable(USER_HOLDER.get());
    }

    public static void remove() {
        USER_HOLDER.remove();
    }
}
