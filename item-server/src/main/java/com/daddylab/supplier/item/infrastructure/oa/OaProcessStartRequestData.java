package com.daddylab.supplier.item.infrastructure.oa;

import lombok.Data;

import javax.annotation.Nullable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@Data
public class OaProcessStartRequestData {
    /** 模板编号，参见表单正文流程模板编号 */
    private String templateCode;

    /** 是否为待发：0:新建-发送；1:新建-保存待发 */
    private String draft;

    /** 未设置取模板设置的标题 */
    @Nullable private String subject;

    /** 协同标题区附件，Long型List，值为附件的Id。Id是附件接口响应结果中fileUrl字段的值。 */
    @Nullable private List<Long> attachments;

    /** 协同公文的id */
    @Nullable private String relateDoc;

    /** 表单data参数 */
    @Nullable private Object data;

}
