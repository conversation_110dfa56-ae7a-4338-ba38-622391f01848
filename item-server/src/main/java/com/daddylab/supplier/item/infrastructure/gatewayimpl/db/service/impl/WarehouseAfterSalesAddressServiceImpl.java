package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WarehouseAfterSalesAddress;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WarehouseAfterSalesAddressMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IProviderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWarehouseAfterSalesAddressService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 仓库售后退回地址 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
@Service
public class WarehouseAfterSalesAddressServiceImpl extends DaddyServiceImpl<WarehouseAfterSalesAddressMapper, WarehouseAfterSalesAddress> implements IWarehouseAfterSalesAddressService {

    @Resource
    IProviderService iProviderService;

    @Override
    public List<String> warehouseNo(Long partnerProviderId) {
        if (Objects.isNull(partnerProviderId)) {
            return new LinkedList<>();
        }

        return this.lambdaQuery().eq(WarehouseAfterSalesAddress::getPartnerProviderId, partnerProviderId)
                .orderByDesc(WarehouseAfterSalesAddress::getId).select().list()
                .stream().map(WarehouseAfterSalesAddress::getWarehouseNo).collect(Collectors.toList());
    }
}
