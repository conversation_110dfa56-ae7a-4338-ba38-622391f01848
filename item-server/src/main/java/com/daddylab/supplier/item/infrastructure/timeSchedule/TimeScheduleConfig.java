package com.daddylab.supplier.item.infrastructure.timeSchedule;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.TimeScheduleType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nonnull;
import java.time.Duration;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/7/1
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class TimeScheduleConfig {
    @Nonnull
    private TimeScheduleType type;
    @Nonnull
    private String businessId;
    @Nonnull
    private Integer syncFrequency;
    @Builder.Default
    private Integer startTimeOffset = 0;
    @Builder.Default
    private final Duration tolerateOfOverdue = Duration.ofMinutes(10);
    private String logPrefix;

    public String getLogPrefix() {
        return Optional.ofNullable(logPrefix).orElseGet(() -> type.getDesc());
    }
}
