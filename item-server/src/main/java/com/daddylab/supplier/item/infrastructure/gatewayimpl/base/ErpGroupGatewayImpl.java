package com.daddylab.supplier.item.infrastructure.gatewayimpl.base;

import com.daddylab.supplier.item.domain.base.ErpGroupGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ErpGroup;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IGroupService;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> up
 * @date 2022/5/26 2:41 下午
 */
@Service
public class ErpGroupGatewayImpl implements ErpGroupGateway {

    public static final int CACHE_SECONDS = 60 * 30;
    public static final int CACHE_MAXIMUM_SIZE = 100;

    @Autowired
    IGroupService iGroupService;

    LoadingCache<Long, Optional<ErpGroup>> cache = Caffeine.newBuilder()
            .expireAfterAccess(CACHE_SECONDS, TimeUnit.SECONDS)
            .maximumSize(CACHE_MAXIMUM_SIZE)
            .build(this::getErpGroup0);

    @Override
    public Optional<ErpGroup> id(Long id) {
        return cache.get(id);
    }

    private Optional<ErpGroup> getErpGroup0(Long id){
        return iGroupService.lambdaQuery().eq(ErpGroup::getId, id)
                .select().oneOpt();
    }
}
