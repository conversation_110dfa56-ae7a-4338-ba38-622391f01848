package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderSettlementForm;

/**
 * <p>
 * 订单结算表单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
public interface IOrderSettlementFormService extends IDaddyService<OrderSettlementForm> {

    OrderSettlementForm getByNo(String no);
}
