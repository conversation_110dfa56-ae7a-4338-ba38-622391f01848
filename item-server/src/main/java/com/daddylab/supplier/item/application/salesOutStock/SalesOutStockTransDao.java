package com.daddylab.supplier.item.application.salesOutStock;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.SkuUnitDto;
import com.daddylab.supplier.item.application.salesOutStock.dto.WdtStockOutDto;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2024年01月18日 5:32 PM
 */
@Service
@AllArgsConstructor
public class SalesOutStockTransDao {

    final IWdtSaleStockOutOrderService iWdtSaleStockOutOrderService;
    final IItemSkuService iItemSkuService;
    final IItemService itemService;
    final IWdtSaleStockOutOrderDetailsService iWdtSaleStockOutOrderDetailsService;
    final IWdtOrderService iWdtOrderService;
    final IWarehouseService iWarehouseService;

    @Transactional(rollbackFor = Exception.class)
    public void deleteWdtData(WdtStockOutDto wdtStockOutDataDto) {
        Long orderDbId = wdtStockOutDataDto.getOrderDbId();
        iWdtSaleStockOutOrderService.removeByIdWithTime(orderDbId);

        List<Long> detailDbIdList = wdtStockOutDataDto.getDetailDbIdList();
        if (CollUtil.isNotEmpty(detailDbIdList)) {
            iWdtSaleStockOutOrderDetailsService.removeByIdsWithTime(detailDbIdList);
        }

        iWdtOrderService.removeByIdWithTime(wdtStockOutDataDto.getWdtOrderId());
    }

    @Transactional(rollbackFor = Exception.class)
    public WdtStockOutDto buildWdtData(String warehouseNo, List<SkuUnitDto> details, Map<String, Integer> salesStockOutMap) {
        WdtStockOutDto wdtDataNoDto = new WdtStockOutDto();

        // SGCK + 年 + 月 + 日 + 00001
        String orderNo = "SGCK" + DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN) + "00" + RandomUtil.randomNumbers(3);
        Long stockOutId = DateUtil.currentTime();
        String tradeNo = "JY" + stockOutId;

        List<String> skuCodes = details.stream().map(SkuUnitDto::getSkuCode).collect(Collectors.toList());
        Map<String, ItemSku> skuCodeAndItemIdMap = iItemSkuService.lambdaQuery()
                .in(ItemSku::getSkuCode, skuCodes).list()
                .stream().collect(Collectors.toMap(ItemSku::getSkuCode, v -> v, (a, b) -> b));

        WdtSaleStockOutOrder wdtSaleStockOutOrder = new WdtSaleStockOutOrder();
        wdtSaleStockOutOrder.setStockoutId(stockOutId);
        wdtSaleStockOutOrder.setOrderNo(orderNo);
        wdtSaleStockOutOrder.setWarehouseNo(warehouseNo);
        iWarehouseService.queryWarehouseByNo(warehouseNo).ifPresent(val -> wdtSaleStockOutOrder.setWarehouseName(val.getName()));
        wdtSaleStockOutOrder.setLogisticsName(StrUtil.EMPTY);
        wdtSaleStockOutOrder.setLogisticsNo(StrUtil.EMPTY);
        wdtSaleStockOutOrder.setShopName(StrUtil.EMPTY);
        wdtSaleStockOutOrder.setConsignTime(LocalDateTime.now());
        wdtSaleStockOutOrder.setStatus(110);
        wdtSaleStockOutOrder.setTradeNo(tradeNo);
//        BigDecimal bigDecimal = details.stream().map(OrderSettlementDetail::getFinalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
//        wdtSaleStockOutOrder.setReceivable(bigDecimal);
//        iWdtSaleStockOutOrderService.save(wdtSaleStockOutOrder);

        WdtOrder wdtOrder = new WdtOrder();
        wdtOrder.setTradeNo(tradeNo);
        iWdtOrderService.save(wdtOrder);

        List<WdtSaleStockOutOrderDetails> detailsList = details.stream().map(val -> {
            WdtSaleStockOutOrderDetails wdtSaleStockOutOrderDetails = new WdtSaleStockOutOrderDetails();
            wdtSaleStockOutOrderDetails.setStockoutId(stockOutId);
            wdtSaleStockOutOrderDetails.setSpecNo(val.getSkuCode());
            String skuCode = val.getSkuCode();
            ItemSku itemSku = skuCodeAndItemIdMap.get(skuCode);
            Item item = itemService.getById(itemSku.getItemId());
            wdtSaleStockOutOrderDetails.setGoodsName(item.getName());
            wdtSaleStockOutOrderDetails.setGoodsNo(item.getCode());
            wdtSaleStockOutOrderDetails.setSellPrice(val.getPrice());
//            Integer settlementQuantity = Objects.nonNull(val.getSettlementQuantity()) ? val.getSettlementQuantity() : 0;
//            Integer temporaryQuantity = Objects.nonNull(val.getTemporaryQuantity()) ? val.getTemporaryQuantity() : 0;
//            BigDecimal cc = new BigDecimal(settlementQuantity - temporaryQuantity);
            wdtSaleStockOutOrderDetails.setGoodsCount(new BigDecimal(val.getNum()));
            wdtSaleStockOutOrderDetails.setUnitName(itemSku.getUnit());
//            BigDecimal totalAmount = cc.multiply(val.getSettlementAmount());
            wdtSaleStockOutOrderDetails.setTotalAmount(new BigDecimal(val.getNum()).multiply(val.getPrice()).setScale(2, RoundingMode.HALF_UP));
            wdtSaleStockOutOrderDetails.setSpecName(itemSku.getSpecifications());
            salesStockOutMap.put(val.getSkuCode(), val.getNum());
            return wdtSaleStockOutOrderDetails;
        }).collect(Collectors.toList());
        iWdtSaleStockOutOrderDetailsService.saveBatch(detailsList);

        BigDecimal goodsCount = detailsList.stream().map(WdtSaleStockOutOrderDetails::getGoodsCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        wdtSaleStockOutOrder.setGoodsCount(goodsCount);
        BigDecimal receivable = detailsList.stream().map(WdtSaleStockOutOrderDetails::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        wdtSaleStockOutOrder.setReceivable(receivable);
        iWdtSaleStockOutOrderService.save(wdtSaleStockOutOrder);

        List<Long> detailDbIdList = detailsList.stream().map(WdtSaleStockOutOrderDetails::getId).collect(Collectors.toList());

        wdtDataNoDto.setOrderNo(wdtSaleStockOutOrder.getOrderNo());
        wdtDataNoDto.setOrderDbId(wdtSaleStockOutOrder.getId());
        wdtDataNoDto.setDetailDbIdList(detailDbIdList);
        wdtDataNoDto.setWdtOrderId(wdtOrder.getId());
        return wdtDataNoDto;
    }


}
