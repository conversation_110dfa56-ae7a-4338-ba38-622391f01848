package com.daddylab.supplier.item.application.item.itemRunning.cmd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName ItemRunningCmd.java
 * @description
 * @createTime 2022年04月29日 17:00:00
 */
@Data
@ApiModel("运营信息接收实体")
public class ItemRunningCmd {

    @ApiModelProperty("商品Id")
    private Long itemId;

    @ApiModelProperty("平台商品id类型 0:小程序Id 1:会员店Id 2:美妆店Id 3:母婴店Id 4:其他店铺id")
    private Integer type;

    @ApiModelProperty("平台商品id")
    private String platformItemId;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("产品标准名")
    private String standardName;

    @ApiModelProperty("卖点文案")
    private String sellingPoints;

}
