package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/12/21
 */
@AllArgsConstructor
@Getter
public enum ItemCodeRefType implements IIntegerEnum {
    ITEM_CODE(1, "后端商品编码"),
    PROVIDER_SPECIFIED_CODE(2, "后端商品供货指定编码"),
    PSYS_CODE(3, "P系统商品款号"),
    SKU_CODE(4, "后端商品SKU编码"),
    SKU_PROVIDER_SPECIFIED_CODE(5, "后端商品SKU供货指定编码"),
    ITEM_REF_CODE(6, "商品参照库商品编码"),
    ITEM_REF_SKU_CODE(7, "商品参照库商品SKU编码"),
    ;
    @EnumValue
    @JsonValue
    private final Integer value;
    private final String desc;
}
