package com.daddylab.supplier.item.application.purchase.purchaseDissent;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.controller.purchaseDissent.dto.PurchaseDissentCmd;
import com.daddylab.supplier.item.controller.purchaseDissent.dto.PurchaseDissentVo;

/**
 * <AUTHOR>
 * @ClassName PurchaseDissentService.java
 * @description
 * @createTime 2021年11月17日 14:00:00
 */
public interface PurchaseDissentBizService {

    /**
     * 查询异议
     * @param id
     * @return
     */
    MultiResponse<PurchaseDissentVo> getDissent(Long id);

    /**
     * 新增异议
     * @param cmd
     * @return
     */
    Response add(String token,PurchaseDissentCmd cmd);

    /**
     * 修改异议
     *
     * @param cmd
     */
    Response update(String token,PurchaseDissentCmd cmd);


}
