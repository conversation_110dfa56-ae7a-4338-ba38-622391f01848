//package com.daddylab.supplier.item.application.message.wechat;
//
//import cn.hutool.core.util.IdUtil;
//import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
//import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQConsumerBase;
//import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQProducer;
//import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQConsumerGroup;
//import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQTopic;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.rocketmq.common.message.MessageExt;
//import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
//import org.jetbrains.annotations.Nullable;
//import org.redisson.api.RedissonClient;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.core.env.StandardEnvironment;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
///**
// * <AUTHOR> up
// * @date 2022/6/7 10:54 上午
// */
//@Slf4j
//@Component
//@RocketMQMessageListener(consumerGroup = MQConsumerGroup.WECHAT_DELAY_MSG, topic = MQTopic.WECHAT_DELAY_MSG, consumeThreadMax = 1)
//public class DelayMsgConsumer extends RocketMQConsumerBase<List<Long>> {
//
//    @Autowired
//    RocketMQProducer rocketMqProducer;
//
//    @Autowired
//    MsgSender msgSender;
//
//    public DelayMsgConsumer(RedissonClient redissonClient, StandardEnvironment environment, RefreshConfig refreshConfig) {
//        super(redissonClient, environment, refreshConfig);
//    }
//
//    @Override
//    protected void handle(MessageExt msg, @Nullable List<Long> body) {
//        if (CollectionUtils.isNotEmpty(body)) {
//            int size = body.size();
//            if (size <= 50) {
//                msgSender.sendByIdList(body);
//            } else {
//                List<Long> longs = body.subList(0, 50);
//                msgSender.sendByIdList(longs);
//
//                List<Long> longs1 = body.subList(50, size);
//                rocketMqProducer.asyncSend(longs1, MQTopic.WECHAT_DELAY_MSG, "msg_id", IdUtil.fastSimpleUUID(), 5);
//            }
//        }
//    }
//
//}
