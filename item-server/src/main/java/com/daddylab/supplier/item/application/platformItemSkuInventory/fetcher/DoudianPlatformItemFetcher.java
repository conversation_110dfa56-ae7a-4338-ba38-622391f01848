package com.daddylab.supplier.item.application.platformItemSkuInventory.fetcher;

import com.daddylab.supplier.item.application.platformItemSkuInventory.PlatformItemSkuSyncService;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.dataFetch.CustomFetcher;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/15
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class DoudianPlatformItemFetcher implements CustomFetcher {

    private final ObjectProvider<List<PlatformItemSkuSyncService>> platformItemSkuSyncServiceProvider;

    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.DOUDIAN_PLATFORM_ITEM;
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, RunContext runContext) {
        platformItemSkuSyncServiceProvider.ifAvailable(platformItemSkuSyncServices -> {
            platformItemSkuSyncServices.stream().filter(service -> service.defaultType() == Platform.DOUDIAN)
                                       .forEach(service -> {
                                           try {
                                               service.incrementSync(startTime, endTime);
                                           } catch (UnsupportedOperationException e) {
                                               log.warn("[抖店平台商品][时间增量同步]暂不支持");
                                           }
                                       });
        });
    }
}