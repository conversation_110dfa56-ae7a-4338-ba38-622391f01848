package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderDeliveryTrace;
import com.daddylab.supplier.item.types.CountGroup;

import java.util.List;

/**
 * <p>
 * 订单发货跟踪 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
public interface OrderDeliveryTraceMapper extends DaddyBaseMapper<OrderDeliveryTrace> {

    List<CountGroup<Long>> countByOrderPersonal();

}
