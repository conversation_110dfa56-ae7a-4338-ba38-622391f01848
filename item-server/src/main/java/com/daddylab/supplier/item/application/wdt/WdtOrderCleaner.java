//package com.daddylab.supplier.item.application.wdt;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.collection.ListUtil;
//import cn.hutool.core.util.StrUtil;
//import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
//import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
//import com.daddylab.supplier.item.domain.purchase.enums.PurchaseType;
//import com.daddylab.supplier.item.domain.wdt.consts.PlatformMap;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.*;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetail;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemPriceType;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemPriceMapper;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseMapper;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderMapper;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderDetailService;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtOrderDetailWrapperService;
//import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.math.BigDecimal;
//import java.util.*;
//import java.util.concurrent.atomic.AtomicInteger;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR> up
// * @date 2022/4/15 2:39 下午
// */
//@Slf4j
//@Service
//public class WdtOrderCleaner {
//
//    @Autowired
//    WdtOrderMapper wdtOrderMapper;
//
//    @Autowired
//    ItemPriceMapper itemPriceMapper;
//
//    @Autowired
//    PurchaseMapper purchaseMapper;
//
//    @Autowired
//    ItemSkuGateway itemSkuGateway;
//
//    @Autowired
//    IWdtOrderDetailService iWdtOrderDetailService;
//
//    @Autowired
//    ProviderGateway providerGateway;
//
//    @Autowired
//    IWdtOrderDetailWrapperService iWdtOrderDetailWrapperService;
//
//    private Optional<WdtOrderDetail> getSuiteNo(Long suiteTradeId) {
//        return iWdtOrderDetailService.lambdaQuery().select().eq(WdtOrderDetail::getTradeId, suiteTradeId)
//                .last("limit 1").oneOpt();
//    }
//
//    /**
//     * 根据组合商品订单，清洗除组合商品信息
//     *
//     * @param tradeIdSet
//     * @param operateTime
//     * @return
//     */
//    public void completionSuiteWrapper(Set<Long> tradeIdSet, String operateTime) {
//        if (CollectionUtils.isEmpty(tradeIdSet)) {
//            return;
//        }
//
////        List<WdtOrderDetailWrapper> saveList = new LinkedList<>();
//        AtomicInteger count = new AtomicInteger();
//
//        tradeIdSet.forEach(tradeId -> {
//
//            Optional<WdtOrderDetail> optional = getSuiteNo(tradeId);
//            if (!optional.isPresent()) {
//                return;
//            }
//            List<Long> wdtOrderDetailDbIdList = wdtOrderMapper.getWodBdIdByTradeId(tradeId);
//            if (CollectionUtils.isEmpty(wdtOrderDetailDbIdList)) {
//                return;
//            }
//            String suiteNo = optional.get().getSuiteNo();
//
////            log.info("-------组合装处理 count:{} -------", count.getAndIncrement());
//
//            // 检查此组合商品skuCode是否满足拆分条件。即此组合商品是否可以查到采购活动价格。组合商品的支付时间和平台均一致，随便捞取一个
//            Long dbId = wdtOrderDetailDbIdList.get(0);
//            WdtOrderDetailDO example = wdtOrderMapper.getByDetailDbId(dbId);
//            BigDecimal activityPrice = getActivityPrice(suiteNo, DateUtil.toEpochSecond(example.getPayTime()), PlatformMap.mapPlatform(example.getPlatformId()).getValue());
//            boolean split = BigDecimal.ZERO.compareTo(activityPrice) == 0;
//
//            // 如果拆分组合商品。
//            if (split) {
//                List<WdtOrderDetailDO> wdtOrderDetailList = wdtOrderMapper.orderSingleDetailListByDbIds(wdtOrderDetailDbIdList);
//                completionSingleWrapper(wdtOrderDetailList, operateTime);
//            }
//            // 如果不拆分组合商品
//            else {
//                // 能找到此组合商品的采购活动价格，判断其下子订单供应商是否都一样。如果都一样，就不拆，如果不一样继续拆。
//                List<Long> sameProvider = wdtOrderMapper.isSameProvider(wdtOrderDetailDbIdList);
//                boolean finalSuite = CollectionUtils.isNotEmpty(sameProvider) && sameProvider.size() == 1;
//
//                // 最终确定是组合商品，采购活动价格能查到 && 子单的供应商都一致。
//                if (finalSuite) {
//                    WdtOrderDetailWrapper suiteWrapper = WdtOrderDetailWrapper.getSuiteInstance(example, operateTime);
//                    // 填充供应商
//                    List<SkuCodeProviderDO> providerDOList = providerGateway.getIdBySkuCode(ListUtil.of(example.getSpecNo()));
//                    if (CollectionUtils.isNotEmpty(providerDOList)) {
//                        suiteWrapper.setProviderId(providerDOList.get(0).getProviderId());
//                    }
//                    // 填充仓库
//                    List<SkuWarehouseNoDO> skuWarehouseDoList = itemSkuGateway.getWarehouseNoList(ListUtil.of(example.getSpecNo()));
//                    if (CollectionUtils.isNotEmpty(skuWarehouseDoList)) {
//                        suiteWrapper.setWarehouseNo(skuWarehouseDoList.get(0).getWarehouseNo());
//                    }
//                    // 填充价格，
//                    suiteWrapper.setPrice(activityPrice);
//                    // 关联子单的dbId
//                    suiteWrapper.setSuiteDbId(StrUtil.join(",", wdtOrderDetailDbIdList));
//                    // 采购数量-退款数量
//                    int i = suiteWrapper.getNum() - suiteWrapper.getRefundNum();
//                    if (i > 0) {
//                        suiteWrapper.setQuantity(i);
//                        iWdtOrderDetailWrapperService.save(suiteWrapper);
//                        log.info("-------wdt订单包装，组合装新增 count:{} -------", count.getAndIncrement());
//                    }
//                }
//                // 因为子单供应商不一致。继续拆分成单品
//                else {
//                    List<WdtOrderDetailDO> wdtOrderDetailList = wdtOrderMapper.orderSingleDetailListByDbIds(wdtOrderDetailDbIdList);
//                    completionSingleWrapper(wdtOrderDetailList, operateTime);
//                }
//            }
//
////            if (saveList.size() > 500) {
////                iWdtOrderDetailWrapperService.saveBatch(saveList);
////                saveList.clear();
////            }
//
//        });
//
////        if (CollectionUtils.isNotEmpty(saveList)) {
////            iWdtOrderDetailWrapperService.saveBatch(saveList);
////        }
//
//    }
//
//
//
//
//    /**
//     * 根据 单品商品的wdt订单，清洗除单品商品信息
//     *
//     * @param wdtOrderDetailDOList
//     * @param operateTime
//     * @return 报错的wdt订单信息
//     */
//    public List<Long> completionSingleWrapper(List<WdtOrderDetailDO> wdtOrderDetailDOList, String operateTime) {
//        List<WdtOrderDetailWrapper> continuedToBeSaved = new LinkedList<>();
//
//        List<Long> errorWdtTradeIdList = new LinkedList<>();
//        wdtOrderDetailDOList.forEach(wdtOrderDetailDO -> {
//            try {
//                WdtOrderDetailWrapper instance = WdtOrderDetailWrapper.getSingleInstance(wdtOrderDetailDO, operateTime);
//
//                Boolean isErpItemCenterData = itemSkuGateway.verifySkuCode(instance.getSkuCode());
//                if (isErpItemCenterData) {
//                    BigDecimal price = price0(instance.getSkuCode(), instance.getPayTime(), instance.getPlatformType());
//                    instance.setPrice(price);
//                    // 订单数量-退款数量 = 采购数量
//                    int i = instance.getNum() - instance.getRefundNum();
//                    // 采购数量大于0，才进行处理。
//                    if (i > 0) {
//                        instance.setQuantity(i);
//                        continuedToBeSaved.add(instance);
//                    }
//                }
//            } catch (Exception e) {
//                log.error("wdt订单信息生成商品信息包装类异常", e);
//                errorWdtTradeIdList.add(wdtOrderDetailDO.getTradeId());
//            }
//        });
//
//        if (CollectionUtils.isNotEmpty(continuedToBeSaved)) {
//            List<String> skuCodeList = continuedToBeSaved.stream().map(WdtOrderDetailWrapper::getSkuCode).collect(Collectors.toList());
//            // sku对应的仓库编号
//            List<SkuWarehouseNoDO> skuWarehouseDoList = itemSkuGateway.getWarehouseNoList(skuCodeList);
//            Map<String, SkuWarehouseNoDO> warehouseNoMap = skuWarehouseDoList.stream().collect(Collectors.toMap(SkuWarehouseNoDO::getSkuCode, a -> a, (k1, k2) -> (k1)));
//
//            // sku是否是赠品
//            List<SkuIsGiftDO> skuIsGiftDoList = itemSkuGateway.getIsGiftList(skuCodeList);
//            Map<String, SkuIsGiftDO> isGiftMap = skuIsGiftDoList.stream().collect(Collectors.toMap(SkuIsGiftDO::getSkuCode, a -> a, (k1, k2) -> (k1)));
//
//            // 供应商信息
//            List<SkuCodeProviderDO> skuProviderList = providerGateway.getIdBySkuCode(skuCodeList);
//            Map<String, SkuCodeProviderDO> skuProviderMap = skuProviderList.stream().collect(Collectors.toMap(SkuCodeProviderDO::getSkuCode, a -> a, (k1, k2) -> (k1)));
//
//            continuedToBeSaved.forEach(wrapper -> {
//                // 如果这个sku存在仓库编号。取sku中的仓库编号,来覆盖原先订单中的仓库编号
//                SkuWarehouseNoDO skuWarehouseNoDO = warehouseNoMap.get(wrapper.getSkuCode());
//                if (Objects.nonNull(skuWarehouseNoDO)) {
//                    if (StringUtils.isNotBlank(skuWarehouseNoDO.getWarehouseNo())) {
//                        wrapper.setWarehouseNo(skuWarehouseNoDO.getWarehouseNo());
//                    }
//                }
//
//                SkuIsGiftDO skuIsGiftDO = isGiftMap.get(wrapper.getSkuCode());
//                if (Objects.nonNull(skuIsGiftDO)) {
//                    wrapper.setIsGift(skuIsGiftDO.getIsGift());
//                }
//
//                // 供应商信息
//                SkuCodeProviderDO skuCodeProviderDO = skuProviderMap.get(wrapper.getSkuCode());
//                if (Objects.nonNull(skuCodeProviderDO)) {
//                    wrapper.setProviderId(skuCodeProviderDO.getProviderId());
//                }
//            });
//
//            iWdtOrderDetailWrapperService.saveBatch(continuedToBeSaved);
//            log.info("-------wdt订单包装，单品新增 count:{} -------", continuedToBeSaved.size());
//        }
//
//        return errorWdtTradeIdList;
//    }
//
//    private BigDecimal price0(String skuCode, Long payTime, Integer platformType) {
//        BigDecimal price = getActivityPrice(skuCode, payTime, platformType);
//        // 1.如果对应的采购活动价格为0，则去取后端商品sku的价格。
//        if (BigDecimal.ZERO.compareTo(price) == 0) {
//            // 2.继续去查此sku在商品中心价格
//            price = getItemCenterPrice(skuCode, payTime);
//        }
//        return price;
//    }
//
//    /**
//     * 查询活动价格。
//     *
//     * @param skuCode
//     * @param payTime
//     * @param platformType
//     * @return
//     */
//    private BigDecimal getActivityPrice(String skuCode, Long payTime, Integer platformType) {
//        // 根据skuCode + 下单时间 + 平台 查询采购活动表中的价格。
//        List<PurchasePriceDO> purchasePriceByActiveType = purchaseMapper.getPurchasePrice(skuCode, payTime, platformType);
//        return activityTypePriceTaker(purchasePriceByActiveType);
//    }
//
//    /**
//     * 根据平台的采购活动类型进行取值。
//     *
//     * @return 商品价格取值
//     */
//    private BigDecimal activityTypePriceTaker(List<PurchasePriceDO> purchasePriceList) {
//        if (CollectionUtils.isEmpty(purchasePriceList)) {
//            return BigDecimal.ZERO;
//        }
//
//        Map<Integer, PurchasePriceDO> activeTypeMap = purchasePriceList.stream()
//                .collect(Collectors.toMap(PurchasePriceDO::getActiveType, v -> v, (a, b) -> a));
//
//        // 直播价格
//        PurchasePriceDO liveValue = activeTypeMap.get(PurchaseType.LIVE.value);
//        if (Objects.nonNull(liveValue)) {
//            return liveValue.getPriceCost();
//        }
//        // 大促价格
//        PurchasePriceDO saleValue = activeTypeMap.get(PurchaseType.SALE.value);
//        if (Objects.nonNull(saleValue)) {
//            return saleValue.getPriceCost();
//        }
//        // 无，常规价格
//        PurchasePriceDO noValue = activeTypeMap.get(PurchaseType.NOT_HAVE.value);
//        if (Objects.nonNull(noValue)) {
//            return noValue.getPriceCost();
//        }
//        return BigDecimal.ZERO;
//    }
//
//
//    /**
//     * 查询 sku在商品中心价格
//     * 1.如果sku对应的价格存在，就直接去
//     * 2.如果sku对应的价格不存在，则取sku对应的商品在最近成本价。
//     *
//     * @param skuCode
//     * @return
//     */
//    private BigDecimal getItemCenterPrice(String skuCode, Long targetTime) {
//        BigDecimal price;
//        ItemSku itemSku = itemSkuGateway.getBySkuCode(skuCode);
//        if (Objects.nonNull(itemSku)) {
//            if (itemSku.getCostPrice().compareTo(BigDecimal.ZERO) > 0) {
//                price = itemSku.getCostPrice();
//                return price;
//            }
//        }
//
//        List<BigDecimal> priceList = itemPriceMapper.getPrice(skuCode, ItemPriceType.PROCUREMENT.getValue(), targetTime);
//        if (CollUtil.isNotEmpty(priceList)) {
//            return priceList.get(0);
//        } else {
//            return BigDecimal.ZERO;
//        }
//    }
//
//
//}
