package com.daddylab.supplier.item.domain.brand.dto;

import com.daddylab.supplier.item.common.enums.EnableStatusEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@ApiModel(value = "创建或者更新品牌")
public class CreateOrUpdateBrandCmd {
    /**
     * id
     */
    private Long id;

    /**
     * 品牌编号（后端自动生成，前端不传）
     */
    @ApiModelProperty(hidden = true)
    private String sn;

    /**
     * 品牌名称
     */
    @NotNull(message = "请输入名牌名称")
    @Size(min = 1, max = 50, message = "名牌名称长度应该在{min}-{max}个字符")
    private String name;

    /**
     * 品牌LOGO
     */
    @Size(message = "品牌LOGO长度应该在{min}-{max}个字符", max = 512)
    private String logo;

    /**
     * 所属供应商ID
     */
    @NotNull(message = "请选择所属供应商")
    @NotEmpty(message = "请选择所属供应商")
    private List<Long> providers;

    /**
     * 状态 0:停用 1:正常
     */
    @ApiModelProperty("状态 OFF:停用 ON:正常")
    private EnableStatusEnum status;

    /**
     * 合作模式
     */
    @ApiModelProperty("合作模式（业务线）多选")
    private List<Integer> businessLine;
}
