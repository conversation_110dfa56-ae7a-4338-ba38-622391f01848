package com.daddylab.supplier.item.application.criticalPush;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 推送管理注解 ！！请注意，该注解依赖SpringAOP，所以不能加在private方法上！！
 *
 * <AUTHOR>
 * @since 2022/4/25
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ManagedPush {

    SourceType sourceType();

    TargetType targetType();

    /**
     * 方法的第几个入参是ID字段（默认第一个）
     */
    int idArgIndex() default 0;

    /**
     * 如果ID字段是入参的嵌套属性，需要指定属性名
     */
    String idArgProp() default "";

    /**
     * 是否异步推送
     */
    boolean async() default false;
}
