package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.UserVisitDaily;
import java.time.LocalDateTime;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 用户每日访问记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
public interface UserVisitDailyMapper extends DaddyBaseMapper<UserVisitDaily> {

    /**
     * 每日访问记录
     *
     * @param userId 用户ID
     * @param time   访问时间
     * @return 1:新增访问记录 2:更新访问记录
     */
    int visitDaily(@Param("userId") long userId, @Param("time") LocalDateTime time);
}
