package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OffShelfFeedback;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;

/**
 * <p>
 * 下架管理-下架反馈 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface OffShelfFeedbackMapper extends DaddyBaseMapper<OffShelfFeedback> {

}
