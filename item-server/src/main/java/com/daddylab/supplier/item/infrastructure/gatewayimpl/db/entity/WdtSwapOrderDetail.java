package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 旺店通换出单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtSwapOrderDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 退换单号
     */
    private String refundNo;

    /**
     * 原始换出单号
     */
    private String tid;

    /**
     * 原始子单号
     */
    private String oid;

    /**
     * 货品类型
     */
    private Integer targetType;

    /**
     * 换出货品id
     */
    private Integer targetId;

    /**
     * 是否残次品
     */
    private Boolean defect;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 货品编号
     */
    private String goodsNo;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 规格码
     */
    private String specCode;

    /**
     * 商家编码,换出货品为组合装则此编码为组合装的商家编码
     */
    private String merchantNo;

    /**
     * 零售价
     */
    private BigDecimal price;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 数量
     */
    private BigDecimal num;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除时间
     */
    private Long deletedAt;


}
