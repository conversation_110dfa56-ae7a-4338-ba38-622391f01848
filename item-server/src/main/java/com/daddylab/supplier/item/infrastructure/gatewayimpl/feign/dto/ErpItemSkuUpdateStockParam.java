package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.enums.StockTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @class ErpItemSkuUpdateStock.java
 * @description 更新库存
 * @date 2024-03-04 10:51
 */
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
@Data
@ApiModel(value = "ERP商品更新库存", description = "ERP商品更新库存")
public class ErpItemSkuUpdateStockParam implements Serializable {

    @NotNull(message = "skuId不能为空")
    @ApiModelProperty("skuId")
    private Long skuId;

    @NotNull(message = "操作类型不能为空")
    @ApiModelProperty("操作类型")
    private StockTypeEnum type;

    @NotNull(message = "库存数量不能为空")
    @ApiModelProperty("库存数量")
    private Integer num;
}
