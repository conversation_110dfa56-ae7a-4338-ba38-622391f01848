package com.daddylab.supplier.item.controller.item.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/26 8:57 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("商品简单搜索请求封装")
public class ItemSimpleViewCmd extends Command {


    private static final long serialVersionUID = 8135266620469280369L;
    @ApiModelProperty("商品名称 or 商品编号 or 商品id")
    @NotBlank(message = "搜索内容不得为空")
    private String query;

    @ApiModelProperty("1:商品名称。2 商品编号。3 商品id")
    @NotNull(message = "搜索类型不得能为空")
    private Integer type;

}
