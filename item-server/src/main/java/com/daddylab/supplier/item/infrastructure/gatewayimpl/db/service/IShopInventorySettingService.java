/*
package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopInventorySetting;

*/
/**
 * <p>
 * 店铺库存设置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 *//*

public interface IShopInventorySettingService extends IDaddyService<ShopInventorySetting> {

    ShopInventorySetting getByShopId(Long shopId);
}
*/
