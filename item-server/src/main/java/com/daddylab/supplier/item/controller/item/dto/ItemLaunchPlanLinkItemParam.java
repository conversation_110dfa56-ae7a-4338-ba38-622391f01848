package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Date: 2022/5/31 16:05
 * @Description: 请描述下这个类
 */
@Data
@ApiModel("商品上新计划关联的商品信息参数")
public class ItemLaunchPlanLinkItemParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "上新计划和商品关联关系ID（新增时不需要，修改时需要）")
    private Long planItemRefId;

    /**
     * 商品ID（item#id）
     */
    @ApiModelProperty(value = "商品ID")
    private Long itemId;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    /**
     * 类目
     */
    @ApiModelProperty(value = "类目")
    private String categoryPath;

    /**
     * 采购员user_id
     */
    @ApiModelProperty(value = "采购员user_id")
    private Long buyerUserId;

    /**
     * 采购员姓名
     */
    @ApiModelProperty(value = "采购员姓名")
    private String buyerUserName;

    /**
     * 产品负责人user_id
     */
    @ApiModelProperty(value = "产品负责人user_id")
    private Long principalUserId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "序号")
    private Integer sortNo;

    @ApiModelProperty(value = "首页文案")
    private String homeCopy;

    @ApiModelProperty(value = "上新价")
    @Size(max = 200)
    private String launchPrice;

    @ApiModelProperty(value = "商品类型。1：内部，2：外包")
    private Integer itemType;

    @ApiModelProperty(value = "共享盘链接")
    @Size(max = 300)
    private String shareDiskLink;

}
