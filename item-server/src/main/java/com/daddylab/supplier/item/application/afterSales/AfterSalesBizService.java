package com.daddylab.supplier.item.application.afterSales;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesSendOnInfo;

/**
 * <AUTHOR> up
 * @date 2022年10月17日 9:56 AM
 */
public interface AfterSalesBizService {

    /**
     * 异常件信息登记
     *
     * @param cmd
     * @return
     */
    SingleResponse<Boolean> saveAbnormalInfo(AfterSalesAbnormalCmd cmd);

    /**
     * 删除异常件信息
     *
     * @param id
     * @return
     */
    SingleResponse<Boolean> removeAbnormalInfo(Long id);

    /**
     * 异常件关联退还单号
     *
     * @param cmd 关联的退换单编号
     * @return
     */
    SingleResponse<Boolean> relatedAbnormalInfo(RelateCmd cmd) throws Exception;

    /**
     * 异常件信息分页查询
     *
     * @param afterSalesPageQuery
     * @return
     */
    PageResponse<AfterSalesPageVO> pageQueryAbnormalInfo(AfterSalesPageQuery afterSalesPageQuery);

    /**
     * 查询转寄信息
     *
     * @param abnormalInfoId 异常件
     * @return
     */
    SingleResponse<AfterSalesSendOnInfo> getSendOnInfo(Long abnormalInfoId,String returnOrderNo);

    /**
     * 保存异常件转寄信息
     *
     * @param cmd
     * @return
     */
    SingleResponse<Boolean> saveSendOnInfo(AfterSalesSendOnCmd cmd);

    /**
     * 异常件信息导出
     *
     * @param pageQuery
     * @return
     */
    SingleResponse<Boolean> exportAbnormalInfo(AfterSalesPageQuery pageQuery);

}
