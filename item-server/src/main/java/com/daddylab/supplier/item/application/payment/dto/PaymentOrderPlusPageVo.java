//package com.daddylab.supplier.item.application.payment.dto;
//
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PaymentOrderStatus;
//import io.swagger.annotations.ApiModel;
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Data;
//
//import java.math.BigDecimal;
//
///**
// * <AUTHOR> up
// * @date 2023年11月22日 2:07 PM
// */
//@Data
//@ApiModel("付款单简配版列表返回")
//public class PaymentOrderPlusPageVo {
//
//    @ApiModelProperty("付款单ID")
//    private Long id;
//
//    @ApiModelProperty("付款单号")
//    private String no;
//
//    @ApiModelProperty("合作模式")
//    private Integer businessLine;
//
//    @ApiModelProperty("采购类型。0：标准采购，1：工厂代发")
//    private Integer purchaseType;
//
//    private Long applyTime;
//
//    @ApiModelProperty("申请日期")
//    private String applyTimeStr;
//
//    @ApiModelProperty("付款用途。0：采购付款（默认），1：预付款")
//    private Integer payPurpose;
//
//    @ApiModelProperty("付款比例。百分比整数")
//    private Integer payProportions;
//
//    @ApiModelProperty("应付金额")
//    private BigDecimal rightAmount;
//
//    @ApiModelProperty("申请付款金额")
//    private BigDecimal applyAmount;
//
//    @ApiModelProperty("其他金额")
//    private BigDecimal otherChargeback;
//
//    @ApiModelProperty("实际付款金额")
//    private BigDecimal realPayAmount;
//
//    @ApiModelProperty("收款单位")
//    private String payeeUnitStr;
//
//    private Long payeeUnit;
//
//    @ApiModelProperty("付款状态")
//    private PaymentOrderStatus status;
//
//    private Long expectedPayTime;
//
//    @ApiModelProperty("期望付款日期")
//    private String expectedPayTimeStr;
//
//    @ApiModelProperty("付款组织")
//    private String payOrgStr;
//
//    private String payOrg;
//
//}
