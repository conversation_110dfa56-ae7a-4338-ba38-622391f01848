package com.daddylab.supplier.item.application.stockSpec;

import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.bigdecimal.Decimal;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryMode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.types.stockSpec.ShopAvailableStockQuery;
import com.daddylab.supplier.item.types.stockSpec.ShopAvailableStockSpecVO;
import com.daddylab.supplier.item.types.stockSpec.ShopRealWarehouseStockSpecVO;
import com.daddylab.supplier.item.types.stockSpec.ShopVirtualWarehouseStockSpecVO;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/3/15
 * @deprecated
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ShopStockSpecBizServiceImpl implements ShopStockSpecBizService {
    final StockSpecBizService stockSpecBizService;
//    final IVirtualWarehouseInventoryGoodsService virtualWarehouseInventoryGoodsService;
//    final IVirtualWarehouseInventoryService virtualWarehouseInventoryService;
//    final IShopInventoryGoodsService shopInventoryGoodsService;
//    final IShopInventoryService shopInventoryService;
    final IWdtStockSpecService wdtStockSpecService;

    @Mapper
    interface ShopStockSpecAssembler {
        ShopStockSpecAssembler INSTANCE = org.mapstruct.factory.Mappers.getMapper(ShopStockSpecAssembler.class);

        ShopAvailableStockSpecVO to(ShopRealWarehouseStockSpecVO shopRealWarehouseStockSpecVO);

        ShopAvailableStockSpecVO to(ShopVirtualWarehouseStockSpecVO shopRealWarehouseStockSpecVO);
    }

    @Override
    public List<ShopAvailableStockSpecVO> shopAvailableStockQuery(ShopAvailableStockQuery query) {
        query.validate();

        final ArrayList<Check> checks = new ArrayList<>();
        final List<ShopRealWarehouseStockSpecVO> shopRealWarehouseStockSpecVOS = wdtStockSpecService
                .getDaddyBaseMapper().shopRealWarehouseStockQuery(query);
        final ArrayList<ShopAvailableStockSpecVO> shopAvailableStockSpecVOS = shopRealWarehouseStockSpecVOS
                .stream().map(shopRealWarehouseStockSpecVO -> {
                    checks.add(check(shopRealWarehouseStockSpecVO));
                    final ShopAvailableStockSpecVO shopAvailableStockSpecVO =
                            ShopStockSpecAssembler.INSTANCE.to(shopRealWarehouseStockSpecVO);
                    shopAvailableStockSpecVO.setShopAvailableStock(getShopAvailableStock(shopRealWarehouseStockSpecVO));
                    return shopAvailableStockSpecVO;
                }).collect(Collectors.toCollection(ArrayList::new));

        final List<ShopVirtualWarehouseStockSpecVO> shopVirtualWarehouseStockSpecVOS = wdtStockSpecService
                .getDaddyBaseMapper().shopVirtualWarehouseStockQuery(query);
        shopAvailableStockSpecVOS.addAll(
                shopVirtualWarehouseStockSpecVOS
                        .stream()
                        .map(shopVirtualWarehouseStockSpecVO -> {
                            checks.add(check(shopVirtualWarehouseStockSpecVO));
                            final ShopAvailableStockSpecVO shopAvailableStockSpecVO = ShopStockSpecAssembler.INSTANCE.to(
                                    shopVirtualWarehouseStockSpecVO);
                            shopAvailableStockSpecVO.setShopAvailableStock(
                                    getShopAvailableStock(shopVirtualWarehouseStockSpecVO));
                            return shopAvailableStockSpecVO;
                        })
                        .collect(Collectors.toList())
        );
        noticeErrors(query, checks);
        if (!query.isIncludeDefects()) {
            return shopAvailableStockSpecVOS.stream().filter(v -> !v.getDefect()).collect(Collectors.toList());
        }
        return shopAvailableStockSpecVOS;
    }


    public Check check(ShopRealWarehouseStockSpecVO spec) {
        final HashSet<String> errors = new HashSet<>();

        if (spec.getShopInventoryMode() == InventoryMode.LOCK) {
            if (spec.getSkuInventoryLockNum().compareTo(BigDecimal.ZERO) <= 0) {
                errors.add("店铺SKU库存锁定值为0");
            }
        }

        if (spec.getSkuInventoryRatio() == null) {
            errors.add("库存SKU库存占比配置异常");
        }

        final Integer inventoryRatio = Stream
                .of(spec.getSkuInventoryRatio(), spec.getInventoryRatio())
                .filter(Objects::nonNull)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("库存占比配置异常"));

        //计算正确的二级占比
        Integer rightInventoryRatio = inventoryRatio;
        if (spec.getShopInventoryMode() == InventoryMode.SHARED) {
            rightInventoryRatio = Decimal.of(inventoryRatio)
                                         .divide(Decimal.of(100)
                                                        .subtract(spec.getAvailableStockLockRatio()))
                                         .multiply(100)
                                         .asInteger();
        }

        //兜底策略，如果二级占比异常，则重新计算二级占比
        final Integer skuInventoryRatio2 = spec.getSkuInventoryRatio2();
        if ((skuInventoryRatio2 == null || !skuInventoryRatio2.equals(rightInventoryRatio))) {
            errors.add("二级占比配置异常");
            if (skuInventoryRatio2 == null || skuInventoryRatio2 < inventoryRatio) {
                spec.setSkuInventoryRatio2(rightInventoryRatio);
            }
        }
        return new Check(spec, errors);
    }

    /**
     * @return 店铺占用的仓内可用库存（实仓）
     */
    public BigDecimal getShopAvailableStock(ShopRealWarehouseStockSpecVO spec) {
        //店铺库存占比
        final Decimal inventoryRatio = Stream
                .of(spec.getSkuInventoryRatio2(), spec.getSkuInventoryRatio(), spec.getInventoryRatio())
                .filter(Objects::nonNull)
                .map(Decimal::of)
                .map(v -> v.divide(100))
                .findFirst()
                .orElseGet(Decimal::of);

        //共享模式，可用库存需扣除锁定库存
        BigDecimal availableStock;
        if (spec.getShopInventoryMode() == InventoryMode.LOCK) {
            availableStock = spec.getAvailableStock();
        } else {
            availableStock = Decimal.of(spec.getAvailableStock())
                                    .subtract(spec.getAvailableStockLockNum())
                                    .asBigDecimal();
        }

        //如果店铺库存模式为锁定库存，直接返回库存锁定值
        if (spec.getShopInventoryMode() == InventoryMode.LOCK) {
            if (spec.getSkuInventoryLockNum().compareTo(BigDecimal.ZERO) > 0) {
                return spec.getSkuInventoryLockNum();
            }
        }

        //可用库存=店铺库存占比*可用库存
        return Decimal.of(availableStock).multiply(inventoryRatio).scale(0).asBigDecimal();
    }


    private Check check(ShopVirtualWarehouseStockSpecVO spec) {
        final HashSet<String> errors = new HashSet<>();

        if (spec.getShopInventoryMode() == InventoryMode.LOCK) {
            if (spec.getSkuInventoryLockNum().compareTo(BigDecimal.ZERO) <= 0) {
                errors.add("店铺SKU库存锁定值为0");
            }
        }

        if (spec.getWarehouseInventoryMode() == InventoryMode.LOCK) {
            if (spec.getVirtualSkuInventoryLockNum().compareTo(BigDecimal.ZERO) <= 0) {
                errors.add("虚拟仓SKU库存锁定值为0");
            }
        }

        if (spec.getSkuInventoryRatio() == null) {
            errors.add("库存SKU库存占比配置异常");
        }

        Integer inventoryRatio = Stream.of(spec.getSkuInventoryRatio(), spec.getInventoryRatio())
                                       .filter(Objects::nonNull).findFirst().orElse(null);
        if (inventoryRatio == null) {
            errors.add("库存SKU库存占比配置异常");
        }

        //计算正确的二级占比
        Integer rightInventoryRatio = inventoryRatio;
        if (spec.getShopInventoryMode() == InventoryMode.SHARED) {
            rightInventoryRatio = Decimal.of(inventoryRatio)
                                         .divide(Decimal.of(100)
                                                        .subtract(spec.getAvailableStockLockRatio()))
                                         .multiply(100)
                                         .asInteger();
        }

        final Integer skuInventoryRatio2 = spec.getSkuInventoryRatio2();
        if ((skuInventoryRatio2 == null || !skuInventoryRatio2.equals(rightInventoryRatio))) {
            errors.add("二级占比配置异常");
            if (skuInventoryRatio2 == null || skuInventoryRatio2 < inventoryRatio) {
                spec.setSkuInventoryRatio2(rightInventoryRatio);
            }
        }

        Integer virtualSkuInventoryRatio = Stream.of(spec.getVirtualSkuInventoryRatio(),
                                                     spec.getVirtualInventoryRatio())
                                                 .filter(Objects::nonNull)
                                                 .findFirst().orElse(null);
        if (virtualSkuInventoryRatio == null) {
            errors.add("虚拟仓SKU库存占比配置异常");
        }

        Integer rightVirtualSkuInventoryRatio = virtualSkuInventoryRatio;
        if (spec.getWarehouseInventoryMode() == InventoryMode.SHARED) {
            rightVirtualSkuInventoryRatio = Decimal.of(virtualSkuInventoryRatio)
                                                   .divide(Decimal.of(100)
                                                                  .subtract(spec.getRealAvailableStockLockRatio()))
                                                   .multiply(100)
                                                   .asInteger();
        }

        final Integer virtualSkuInventoryRatio2 = spec.getVirtualSkuInventoryRatio2();
        if (virtualSkuInventoryRatio2 == null
                || !virtualSkuInventoryRatio2.equals(rightVirtualSkuInventoryRatio)) {
            errors.add("虚拟仓SKU二级占比配置异常");
            if (virtualSkuInventoryRatio2 == null || virtualSkuInventoryRatio2 < virtualSkuInventoryRatio) {
                spec.setVirtualSkuInventoryRatio2(rightVirtualSkuInventoryRatio);
            }
        }
        return new Check(spec, errors);
    }

    /**
     * @return 店铺占用的仓内可用库存（虚拟仓）
     */
    public BigDecimal getShopAvailableStock(ShopVirtualWarehouseStockSpecVO spec) {
        final InventoryMode shopInventoryMode = spec.getShopInventoryMode();
        final InventoryMode warehouseInventoryMode = spec.getWarehouseInventoryMode();

        //店铺维度SKU库存锁定值
        final BigDecimal skuInventoryLockNum = spec.getSkuInventoryLockNum();

        //如果店铺库存模式为锁定库存，直接返回库存锁定值
        if (shopInventoryMode == InventoryMode.LOCK) {
            if (skuInventoryLockNum != null && skuInventoryLockNum.compareTo(BigDecimal.ZERO) > 0) {
                return skuInventoryLockNum;
            }
        }

        //店铺库存占比
        final Decimal shopInventoryRatio = Stream
                .of(spec.getSkuInventoryRatio2(), spec.getSkuInventoryRatio(), spec.getInventoryRatio())
                .filter(Objects::nonNull)
                .map(Decimal::of)
                .map(v -> v.divide(100))
                .findFirst()
                .orElseGet(Decimal::of);

        //虚拟仓维度SKU库存锁定值
        final BigDecimal virtualSkuInventoryLockNum = spec.getVirtualSkuInventoryLockNum();

        //虚拟仓库存占比
        final Decimal virtualInventoryRatio = Stream
                .of(spec.getVirtualSkuInventoryRatio2(),
                    spec.getVirtualSkuInventoryRatio(),
                    spec.getVirtualInventoryRatio())
                .filter(Objects::nonNull)
                .map(Decimal::of)
                .map(v -> v.divide(100))
                .findFirst()
                .orElseGet(Decimal::of);

        //虚拟仓的可用库存
        Decimal virtualWarehouseAvailableStock;
        //如果虚拟仓为锁定库存模式，且锁定值有效，则直接以锁定值作为虚拟仓的可用库存
        if (warehouseInventoryMode == InventoryMode.LOCK && virtualSkuInventoryLockNum != null && virtualSkuInventoryLockNum
                .compareTo(BigDecimal.ZERO) > 0) {
            virtualWarehouseAvailableStock = Decimal.of(virtualSkuInventoryLockNum);
        } else {
            if (warehouseInventoryMode == InventoryMode.SHARED) {
                virtualWarehouseAvailableStock = Decimal.of(spec.getAvailableStock())
                                                        .subtract(spec.getAvailableStockLockNum())
                                                        .multiply(virtualInventoryRatio);
            } else {
                virtualWarehouseAvailableStock = Decimal.of(spec.getAvailableStock())
                                                        .multiply(virtualInventoryRatio);
            }
        }
        //店铺的可用库存=虚拟仓可用库存*店铺库存占比
        Decimal shopAvailableStock;
        if (shopInventoryMode == InventoryMode.LOCK) {
            shopAvailableStock = Decimal.of(virtualWarehouseAvailableStock)
                                        .multiply(shopInventoryRatio);
        } else {
            //如果店铺是库存共享模式，店铺可用库存=(虚拟仓可用库存-虚拟仓被锁定值)*店铺库存占比
            shopAvailableStock = Decimal.of(virtualWarehouseAvailableStock)
                                        .subtract(virtualSkuInventoryLockNum)
                                        .multiply(shopInventoryRatio);
        }
        return shopAvailableStock.scale(0).asBigDecimal();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Check {
        private Object spec;
        private Collection<String> errors;

        public boolean hasErrors() {
            return !errors.isEmpty();
        }
    }

    private Cache<String, Boolean> noticeThrottle = Caffeine.newBuilder()
                                                            .expireAfterWrite(10, TimeUnit.MINUTES)
                                                            .maximumSize(1024)
                                                            .build();

    private void noticeErrors(ShopAvailableStockQuery query, Collection<Check> checks) {
        final List<Check> checkErrors = checks.stream().filter(Check::hasErrors).collect(Collectors.toList());
        if (!checkErrors.isEmpty()) {
            final String key = String.format("%s:%s:%s", query.getShopNo(), query.getGoodsNos(), query.getSpecNos());
            noticeThrottle.asMap().computeIfAbsent(key, k -> {
                log.error("库存配置异常，Q:{}，E:{}", query, checkErrors);
                try {
                    Alert.text(MessageRobotCode.NOTICE,
                               String.format("库存配置异常，S:%s，GC:%s，SC:%s，E:%s",
                                             query.getShopNo(),
                                             query.getGoodsNos(),
                                             query.getSpecNos(),
                                             checkErrors.stream()
                                                        .flatMap(v -> v.getErrors().stream())
                                                        .collect(Collectors.toSet())));
                    return true;
                } catch (Exception ignored) {
                }
                return null;
            });

        }
    }
}
