package com.daddylab.supplier.item.infrastructure.third.consumer;

import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Shop;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopService;
import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQGroup;
import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQTopic;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.impl.KuaiShouServiceFactory;
import com.daddylab.supplier.item.infrastructure.third.redbook.RedBookService;
import com.daddylab.supplier.item.infrastructure.third.redbook.domain.dto.RedBookMessageDTO;
import com.daddylab.supplier.item.infrastructure.third.redbook.impl.RedBookServiceFactory;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @class KSConsumer.java
 * @description 小红书回调消息消费
 * @date 2024-02-29 17:10
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MQTopic.THIRD_CALLBACK_MESSAGE_RED_BOOK_TOPIC, consumerGroup = MQGroup.THIRD_CALLBACK_MESSAGE_RED_BOOK_GROUP,
        messageModel = MessageModel.CLUSTERING)
public class RedBookCallbackMessageConsumer implements RocketMQListener<RedBookMessageDTO> {
    @Autowired
    private RedBookServiceFactory redBookServiceFactory;
    @Autowired
    private IShopService shopService;
    
    @Override
    public void onMessage(RedBookMessageDTO message) {
        log.debug("[小红书回调消息] 接收到消息 message={}", message);
        Shop shop = shopService.getByAccountId(message.getSellerId()).orElse(null);
        if (shop == null) {
            log.error("[小红书回调消息] 未找到店铺 sellerId={}", message.getSellerId());
            Alert.text(MessageRobotCode.GLOBAL, "小红书回调消息处理异常，未找到店铺 sellerId=" + message.getSellerId());
            return;
        }
        RedBookService service = redBookServiceFactory.getService(shop.getSn());
        service.handle(message);
    }
}
