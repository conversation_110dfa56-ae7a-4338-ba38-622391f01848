package com.daddylab.supplier.item.domain.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
public class StaffDropDownItem implements Serializable {
    @ApiModelProperty(name = "员工ID")
    private Long staffId;

    @ApiModelProperty(name = "员工姓名")
    private String staffName;

    @ApiModelProperty(name = "员工花名")
    private String staffNickName;

    @ApiModelProperty(name = "部门")
    private String dept;



    public StaffDropDownItem(Long staffId, String staffName, String staffNickName) {
        this.staffId = staffId;
        this.staffName = staffName;
        this.staffNickName = staffNickName;
    }

    public static StaffDropDownItem of(Long staffId, String staffName,  String staffNickName) {
        return new StaffDropDownItem(staffId, staffName, staffNickName);
    }


}
