package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DadListItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DadListItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDadListItemService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 老爸清单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Service
@DS("dpm")
public class DadListItemServiceImpl extends DaddyServiceImpl<DadListItemMapper, DadListItem> implements IDadListItemService {

}
