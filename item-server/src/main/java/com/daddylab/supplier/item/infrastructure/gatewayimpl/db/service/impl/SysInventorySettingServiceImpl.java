package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.SysInventorySetting;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.SysInventorySettingMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ISysInventorySettingService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 系统库存同步配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
public class SysInventorySettingServiceImpl extends DaddyServiceImpl<SysInventorySettingMapper, SysInventorySetting> implements ISysInventorySettingService {

    @Override
    public SysInventorySetting getSysInventorySetting() {
        final SysInventorySetting setting = getById(1);
        if (setting != null) {
            return setting;
        }
        final SysInventorySetting sysInventorySetting = new SysInventorySetting();
        sysInventorySetting.setId(1L);
        sysInventorySetting.setSyncFrequency(24 * 3600);
        sysInventorySetting.setPermitShopSyncFreqSetting(0);
        sysInventorySetting.setSyncAfterModifyRadio(1);
        save(sysInventorySetting);
        return sysInventorySetting;
    }

}
