package com.daddylab.supplier.item.application.drawer;

import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.URLUtil;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerImageTypeEnum;
import com.daddylab.supplier.item.domain.fileStore.constant.FileType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.File;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IFileService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerImageService;
import com.daddylab.supplier.item.infrastructure.upyun.gateway.MediaMeta;
import com.daddylab.supplier.item.infrastructure.upyun.gateway.UpyunGateway;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.BiConsumer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

/**
 * <AUTHOR>
 * @since 2022/11/15
 */
@AllArgsConstructor
@Service
@Slf4j
public class ItemDrawerImageMetaQueryTask {

    private final IItemDrawerImageService itemDrawerImageService;
    private final UpyunGateway upyunGateway;
    private final IFileService fileService;

    @XxlJob("ItemDrawerImageMetaQueryTask")
    @Trace(operationName = "ItemDrawerImageMetaQueryTask")
    public void doTask() {
        final List<ItemDrawerImage> list = itemDrawerImageService.lambdaQuery()
                .ne(ItemDrawerImage::getUrl, "").eq(ItemDrawerImage::getFileType, 1)
                .eq(ItemDrawerImage::getProportion, 0).list();

        final int size = list.size();
        log.info("商品抽屉图片元数据获取任务开始执行 总计={} tid={}", size, TraceContext.traceId());

        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        final ExecutorService executorService = Executors.newFixedThreadPool(50);
        final CountDownLatch countDownLatch = new CountDownLatch(size);
        int i = 0;
        for (ItemDrawerImage itemDrawerImage : list) {
            i++;
            final String url = itemDrawerImage.getUrl();
            log.info("{}/{}:put id={} drawerId={} url={}", i, size, itemDrawerImage.getId(),
                    itemDrawerImage.getDrawerId(), url);
            int finalI = i;
            final BiConsumer<Void, Throwable> whenHandleComplete = (r, e) -> {
                countDownLatch.countDown();
                if (e != null) {
                    log.error("{}/{}:error id={} drawerId={} url={}", finalI, size,
                            itemDrawerImage.getId(), itemDrawerImage.getDrawerId(), url, e);
                } else {
                    log.info("{}/{}:done id={} drawerId={} url={}", finalI, size,
                            itemDrawerImage.getId(), itemDrawerImage.getDrawerId(), url);
                }
            };
            CompletableFuture.runAsync(() -> handle(itemDrawerImage, finalI, size), executorService)
                    .whenComplete(whenHandleComplete);
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            stopWatch.stop();
            log.info("商品抽屉图片元数据获取任务被打断 时长={}ms", stopWatch.getTotalTimeMillis());
            return;
        } finally {
            executorService.shutdown();
        }
        stopWatch.stop();
        log.info("商品抽屉图片元数据获取任务执行结束 时长={}ms", stopWatch.getTotalTimeMillis());
    }

    private void handle(ItemDrawerImage itemDrawerImage, int i, int size) {
        final String url = itemDrawerImage.getUrl();
        if (StringUtil.isBlank(url)) {
            log.error("{}/{}: url is blank", i, size);
            return;
        }
        final String decodedUrl = URLUtil.encode(URLUtil.decode(url));
        final String path = URLUtil.getPath(decodedUrl);
        final String fileNamePrefix = FileNameUtil.getPrefix(path);
        final String fileNameSuffix = FileNameUtil.getSuffix(path);
        final Map<String, List<String>> fileInfo = upyunGateway.getFileInfo(url);
        final MediaMeta mediaMeta = upyunGateway.getMediaMeta(url);
        log.info("id={} drawerId={} url={} 图片文件信息={} 图片元数据={}", itemDrawerImage.getId(),
                itemDrawerImage.getDrawerId(), url, fileInfo, mediaMeta);
        if (mediaMeta == null) {
            log.error("url={} 获取图片元信息失败", url);
            return;
        }

        final File file;
        final Optional<File> fileSamePathOptional = fileService.lambdaQuery()
                .eq(File::getPath, path).oneOpt();
        if (!fileSamePathOptional.isPresent()) {
            file = new File();
            file.setType(FileType.IMAGE);
            file.setPath(path);
            file.setName(fileNamePrefix);
            file.setExt(fileNameSuffix);
            Optional.ofNullable(fileInfo).map(v -> v.get("content-type")).map(v -> v.get(0))
                    .ifPresent(file::setMime);
            file.setUrl(url);
            Optional.ofNullable(fileInfo).map(v -> v.get("content-md5")).map(v -> v.get(0))
                    .ifPresent(file::setMd5);
            Optional.ofNullable(fileInfo).map(v -> v.get("x-upyun-file-size"))
                    .map(v -> v.get(0))
                    .map(Integer::parseInt)
                    .ifPresent(file::setSize);
            file.setWidth(mediaMeta.getWidth());
            file.setHeight(mediaMeta.getHeight());
            file.setDuration(0);
            file.setCoverId(0L);
            file.setCoverUrl("");
            file.setMeta(JsonUtil.toJson(mediaMeta));
            file.setUpyun(true);
            file.setAliOss(false);
            fileService.save(file);
        } else {
            file = fileSamePathOptional.get();
        }

        itemDrawerImage.setWidth(file.getWidth());
        itemDrawerImage.setHeight(file.getHeight());
        itemDrawerImage.setProportion(file.getProportion());
        //如果是商品主图，且比例为1比1，则将类型修改为【商品主图（其他尺寸）】
        if (itemDrawerImage.getType() == ItemDrawerImageTypeEnum.ITEM
                && BigDecimal.ONE.compareTo(file.getProportion()) != 0) {
            log.info("抽屉图片类型从{}修改为商品主图（其他尺寸） id={} drawerId={} url={}",
                    itemDrawerImage.getType().getDesc(), itemDrawerImage.getId(),
                    itemDrawerImage.getDrawerId(), itemDrawerImage.getUrl());
            itemDrawerImage.setType(ItemDrawerImageTypeEnum.OTHER_ITEM);
        }
        itemDrawerImageService.updateById(itemDrawerImage);
    }
}
