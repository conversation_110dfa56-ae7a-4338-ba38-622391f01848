package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.EntryActivityPriceItemMapper;
import java.util.Collection;
import java.util.List;

/**
 * 入驻活动价格(商品纬度) 服务类
 *
 * <AUTHOR>
 * @since 2024-10-04
 */
public interface IEntryActivityPriceItemService extends IDaddyService<EntryActivityPriceItem> {
  @Override
  EntryActivityPriceItemMapper getBaseMapper();

  List<EntryActivityPriceSku> listConfirmedSkuBySkuIds(Collection<Long> skuIds);
}
