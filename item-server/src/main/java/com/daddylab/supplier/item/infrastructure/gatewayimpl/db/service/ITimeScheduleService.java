package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.TimeSchedule;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.TimeScheduleType;

import java.time.LocalDateTime;

/**
 * <p>
 * 时间调度表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
public interface ITimeScheduleService extends IDaddyService<TimeSchedule> {

    TimeSchedule getTimeSchedule(TimeScheduleType timeScheduleType, String businessId, LocalDateTime scheduleTime);
}
