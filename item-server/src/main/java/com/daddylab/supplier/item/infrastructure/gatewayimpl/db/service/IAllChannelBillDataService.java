package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AllChannelBillData;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 全渠道开票数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
public interface IAllChannelBillDataService extends IDaddyService<AllChannelBillData> {

}
