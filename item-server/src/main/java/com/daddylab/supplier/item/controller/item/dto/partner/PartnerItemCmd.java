package com.daddylab.supplier.item.controller.item.dto.partner;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/27 4:22 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("查修合作伙伴系统的商品，请求参数")
public class PartnerItemCmd extends Command {


    private static final long serialVersionUID = -307649302333506682L;

    @ApiModelProperty("搜索类型 1商品编号 2商品名称")
    private Integer searchType;

    @ApiModelProperty("搜索内容")
    private String context;

    @ApiModelProperty("页数")
    private Integer pageIndex;

    @ApiModelProperty("条数")
    private Integer pageSize;
}
