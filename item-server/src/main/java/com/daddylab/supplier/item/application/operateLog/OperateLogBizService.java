package com.daddylab.supplier.item.application.operateLog;

import com.alibaba.cola.dto.MultiResponse;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;

import java.util.List;

public interface OperateLogBizService {

    MultiResponse<OperateLog> getOperateLogs(OperateLogTarget target, Object targetId);
    MultiResponse<OperateLog> getOperateLogs(List<OperateLogTarget> target, Object targetId);
}
