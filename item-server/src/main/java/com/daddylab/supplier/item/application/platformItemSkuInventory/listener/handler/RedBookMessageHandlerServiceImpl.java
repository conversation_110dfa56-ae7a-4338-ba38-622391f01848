package com.daddylab.supplier.item.application.platformItemSkuInventory.listener.handler;

import com.daddylab.supplier.item.application.platformItemSkuInventory.PlatformItemSkuSyncService;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSkuInventory;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Shop;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemSkuInventoryService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemSkuService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopAuthorizationService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopService;
import com.daddylab.supplier.item.infrastructure.third.annotations.RedBookHandlerMapping;
import com.daddylab.supplier.item.infrastructure.third.enums.RedBookMessageEnum;
import com.daddylab.supplier.item.infrastructure.third.redbook.IRedBookMessageHandlerService;
import com.daddylab.supplier.item.infrastructure.third.redbook.domain.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @class RedBookMessageHandlerServiceImpl.java
 * @description 消息处理handler
 * @date 2024-03-01 15:55
 */
@Slf4j
@Service
public class RedBookMessageHandlerServiceImpl implements IRedBookMessageHandlerService {
    @Autowired List<PlatformItemSkuSyncService> platformItemSkuSyncServiceList;
    
    private void syncSku(ShopAuthorization shopAuthorization, String skuId) {
        platformItemSkuSyncServiceList.stream().filter(service -> service.defaultType() == Platform.XIAOHONGSHU)
                .forEach(service -> {
                    service.syncSku(shopAuthorization.getSn(), skuId);
                });
    }
    
    private void syncItem(ShopAuthorization shopAuthorization, String itemId) {
        platformItemSkuSyncServiceList.stream().filter(service -> service.defaultType() == Platform.XIAOHONGSHU)
                .forEach(service -> {
                    service.syncItem(shopAuthorization.getSn(), itemId);
                });
    }
    
    @RedBookHandlerMapping(RedBookMessageEnum.SKU_BUY_ABLE)
    public void skuBuyAble(ShopAuthorization shopAuthorization, SkuBuyAbleMessageDTO skuBuyAbleMessage) {
        log.info("[小红书][商品上下架事件触发] skuBuyAbleMessage={}", skuBuyAbleMessage);
        syncSku(shopAuthorization, skuBuyAbleMessage.getSkuId());
    }
    
    @RedBookHandlerMapping(RedBookMessageEnum.SKU_CREATE)
    public void skuCreate(ShopAuthorization shopAuthorization, SkuCreateMessageDTO skuCreateMessage) {
        log.info("[小红书][商品sku创建事件触发] skuCreateMessage={}", skuCreateMessage);
        syncSku(shopAuthorization, skuCreateMessage.getSkuId());
    }
    
    
    @RedBookHandlerMapping(RedBookMessageEnum.SKU_DELETE)
    public void skuDelete(ShopAuthorization shopAuthorization, SkuDeleteMessageDTO deleteMessage) {
        log.info("[小红书][商品删除事件触发] deleteMessage={}", deleteMessage);
        syncSku(shopAuthorization, deleteMessage.getSkuId());
    }
    
    @RedBookHandlerMapping(RedBookMessageEnum.ITEM_AUDIT_REJECT)
    public void itemAuditReject(ShopAuthorization shopAuthorization, ItemAuditRejectMessageDTO itemAuditRejectMessage) {
        log.info("[小红书][商品状态已驳回事件触发] itemAuditRejectMessage={}", itemAuditRejectMessage);
        syncItem(shopAuthorization, itemAuditRejectMessage.getItemId());
    }
    
    @RedBookHandlerMapping(RedBookMessageEnum.ITEM_DEFAULT)
    @Override
    public void handler(RedBookMessageDTO redBookMessageDTO) {
        log.debug("[小红书][公共消息事件处理方法] redBookMessageDTO={}", redBookMessageDTO);
    }
}
