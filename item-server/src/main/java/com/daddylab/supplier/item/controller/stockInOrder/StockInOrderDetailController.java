package com.daddylab.supplier.item.controller.stockInOrder;

import com.daddylab.supplier.item.application.stockInOrder.StockInOrderDetailBizService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 采购入库明细控制类
 * <AUTHOR>
 * @date 2022/3/24 16:44
 **/
@Slf4j
@RestController
@RequestMapping("/stockInOrderDetail")
@Api(value = "采购入库明细相关api", tags = "采购入库明细相关API")
public class StockInOrderDetailController {
    @Autowired
    private StockInOrderDetailBizService stockInOrderDetailBizService;

}
