package com.daddylab.supplier.item.application.drawer;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMerge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMergeItem;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @class ItemDrawerMergeBizService.java
 * @description 描述类的作用
 * @date 2024-04-08 15:51
 */
public interface ItemDrawerMergeBizService {

    /**
     * 查询商品关联包
     *
     * @param itemId Long
     * @return com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMerge
     * @date 2024/4/8 18:01
     * <AUTHOR>
     */
    ItemDrawerMerge getItemDrawerMerge(Long itemId);

    ItemDrawerMerge getItemDrawerMergeById(Long id);

    /**
     * 判断商品是都存在关联关系
     *
     * @param itemId Long
     * @return com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMergeItem
     * @date 2024/4/9 11:28
     * <AUTHOR>
     */
    ItemDrawerMergeItem getItemDrawerMergeItem(Long itemId);
    /**
     * 批量获取关联审核商品
     *
     * @param itemIds List<Long>
     * @return java.util.List<com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMerge>
     * @date 2024/4/9 11:22
     * <AUTHOR>
     */
    List<ItemDrawerMergeItem> getItemDrawerMergeItemList(List<Long> itemIds);

    /**
     * 获取历史包存在的关联商品
     *
     * @param mergeId Long
     * @return java.util.List<com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMergeItem>
     * @date 2024/4/9 11:50
     * <AUTHOR>
     */
    List<ItemDrawerMergeItem> getItemDrawerMergeItemListByMergeId(Long mergeId);

    /**
     * 关联
     *
     * @param itemDrawerId Long 当前的抽屉ID
     * @param itemId Long 当前商品ID
     * @param mergeItemIds List<Long> 关联的商品ID
     * @date 2024/4/9 11:44
     * <AUTHOR>
     */
    void doSave(Long itemDrawerId, Long itemId, List<Long> mergeItemIds);

    /**
     * 解散包
     *
     * @param itemDrawerMergeItem ItemDrawerMergeItem
     * @date 2024/4/9 14:34
     * <AUTHOR>
     */
    void disbandPackage(ItemDrawerMergeItem itemDrawerMergeItem);
    /**
     * 移除包商品
     *
     * @param mergeId Long
     * @param itemIds List<Long>
     * @date 2024/4/9 14:34
     * <AUTHOR>
     */
    void removeItemByIds(Long mergeId, List<Long> itemIds);

    /**
     *
     *
     * @param mergeId Long 包ID
     * @param itemIds List<Long> 新增的商品ID
     * @date 2024/4/9 14:37
     * <AUTHOR>
     */
    void doAddMergeItem(Long mergeId, List<Long> itemIds);

    /**
     * 处理
     *
     * @param itemDrawerMergeId Long
     * @date 2024/4/9 15:39
     * <AUTHOR>
     */
    void doCopy(Long itemDrawerMergeId);

    void doCopyStatus(Long itemDrawerMergeId);

    /**
     * 复制部分商品
     *
     * @param itemDrawerMergeId java.lang.Long
     * @param itemIds java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2024/4/23 10:29
     */
    void doCopy(Long itemDrawerMergeId, List<Long> itemIds);

    void update(ItemDrawerMerge itemDrawerMerge);

    /**
     * 根据商品copy
     *
     * @param itemId java.lang.Long
     * @param consumer java.util.function.BiConsumer<com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer
     * @return void
     * <AUTHOR>
     * @date 2024/4/22 16:53
     */
    public void copyConsumerWithItem(Long itemId, BiConsumer<ItemDrawer, ItemDrawer> consumer);

    /**
     * 根据抽屉ID进行copy
     *
     * @param itemDrawerId java.lang.Long
     * @param consumer java.util.function.BiConsumer<com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer
     * <AUTHOR>
     * @date 2024/4/22 16:54
     */
    public void copyConsumerWithItemDrawer(Long itemDrawerId, BiConsumer<ItemDrawer, ItemDrawer> consumer);

    List<Long> mainItemIdsToMergeItemIds(Collection<Long> itemIds);
    Map<Long, Long> getMainItemIdBatch(Collection<Long> itemIds);

    /**
     * 合并
     * @param drawerId
     * @param itemId
     * @param itemIds
     */
    void doMerge(Long drawerId, Long itemId, List<Long> itemIds);
}
