package com.daddylab.supplier.item.application.item.itemBatchProc.handlers;

import com.alibaba.cola.dto.PageResponse;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.item.ItemSyncWdtBizService;
import com.daddylab.supplier.item.application.item.itemBatchProc.ItemBatchProcHandler;
import com.daddylab.supplier.item.application.warehouse.WarehouseGateway;
import com.daddylab.supplier.item.controller.item.dto.ItemPageQuery;
import com.daddylab.supplier.item.controller.item.dto.ItemPageVo;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemBatchProc;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.itemBatchProc.ItemBatchProcType;
import com.daddylab.supplier.item.types.itemBatchProc.ModifyWarehouseCmd;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/11/17
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class ModifyWarehouseHandlerImpl implements ItemBatchProcHandler {
    private final ItemBizService itemBizService;
    private final ItemGateway itemGateway;
    private final WarehouseGateway warehouseGateway;
    private final OperateLogDomainService operateLogDomainService;
    private final ItemSyncWdtBizService itemSyncWdtBizService;

    @Override
    public void handle(ItemBatchProc batchProc, Synchronize synchronize) {
        final ModifyWarehouseCmd cmd = JsonUtil.parse(batchProc.getCmd(), ModifyWarehouseCmd.class);
        Objects.requireNonNull(cmd, "命令解析异常");

        final Warehouse warehouse =
                warehouseGateway.getWarehouse(cmd.getWarehouseNo()).orElse(null);
        Objects.requireNonNull(warehouse, "仓库信息查询异常");

        int totalPage = -1;
        int pageIndex = 1;
        final ItemPageQuery query = cmd.getQuery();
        query.setPageSize(100);
        while (true) {
            query.setPageIndex(pageIndex);
            final PageResponse<ItemPageVo> itemPageVoPageResponse = itemBizService.queryPage(query);
            if (totalPage == -1) {
                totalPage = itemPageVoPageResponse.getTotalPages();
            }
            final List<ItemPageVo> data = itemPageVoPageResponse.getData();
            if (data.isEmpty()) {
                break;
            }
            for (ItemPageVo itemPageVo : data) {
                final Long itemId = itemPageVo.getId();
                final Item item = itemGateway.getItem(itemId);
                if (itemGateway.setWarehouse(itemId, warehouse.getNo())) {
                    item.setWarehouseNo(warehouse.getNo());

                    operateLogDomainService.addOperatorLog(
                            UserContext.getUserId(),
                            OperateLogTarget.ITEM,
                            itemId,
                            String.format(
                                    "【商品批量处理】仓库从 %s 修改为 %s",
                                    item.getWarehouseNo(), warehouse.getNo()),
                            new Object[] {
                                new DiffUtil.ChangePropertyObj(
                                        "buyerId", item.getWarehouseNo(), warehouse.getNo())
                            });
                    synchronize.log(Level.INFO, "商品 %s 仓库修改完成", itemId);

                    try {
                        itemSyncWdtBizService.syncItemToWdt(item);
                        synchronize.log(Level.INFO, "商品 %s 已同步至旺店通", itemId);

                    } catch (Exception e) {
                        synchronize.log(Level.WARN, "商品 %s 推送旺店通失败:{}", e.getMessage());
                        log.error("【商品批量处理】商品 {} 推送旺店通失败", itemId, e);
                    }
                } else {
                    synchronize.log(Level.WARN, "商品 %s 仓库无需变更", itemId);
                }
            }
            synchronize.setProcess(totalPage == 0 ? 100 : (pageIndex / totalPage) * 100);
            pageIndex++;
        }
    }

    @Override
    public boolean isSupport(ItemBatchProc batchProc) {
        return ItemBatchProcType.MODIFY_WAREHOUSE.getValue().equals(batchProc.getType());
    }
}
