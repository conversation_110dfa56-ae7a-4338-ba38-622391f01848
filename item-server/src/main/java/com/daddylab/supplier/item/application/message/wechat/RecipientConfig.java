package com.daddylab.supplier.item.application.message.wechat;

import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * <AUTHOR> up
 * @date 2022/6/4 10:59 上午
 */
@Data
@Component
@ConfigurationProperties(prefix = "qyweixin.recipient")
@RefreshScope
public class RecipientConfig {

    private Map<String, Map<String, List<String>>> businessLineRecipients;

    private String newGoodLink;
    private String itemPlanLink;
    private String itemDrawerLink;

    private List<Long> messageWhiteList;

    //    private String itemDetailLink;

    /** 法务负责人（登录名） */
    private String legal;

    public List<String> getLegalList() {
        return StringUtil.splitTrim(legal, ",");
    }

    /** 淘系负责人 */
    private String taobao;

    public List<String> getTaobaoList() {
        return StringUtil.splitTrim(taobao, ",");
    }

    public List<String> getTaobaoList(Integer businessLine) {
        return getFromBusinessLineRecipients(
                businessLine, "taobao", () -> StringUtil.splitTrim(taobao, ","));
    }

    /** 平台运营负责人 */
    private String platform;

    public List<String> getPlatformList() {
        return StringUtil.splitTrim(platform, ",");
    }

    /** 待修改阶段共同修改的协同用户（因历史原因，符号名为五月天） */
    private List<String> mayday;

    public List<String> getMayday(Integer businessLine) {
        return getFromBusinessLineRecipients(businessLine, "mayday", this::getMayday);
    }

    private List<String> xiaogao;

    public List<String> getXiaogao(Integer businessLine) {
        return getFromBusinessLineRecipients(businessLine, "xiaogao", this::getXiaogao);
    }

    /** 商品上新，待审核->待修改。指定接收者h花名 */
    //    private String auditToModify;
    //
    //    public List<String> getAuditToModify() {
    //        return StringUtil.splitTrim(this.auditToModify, ",");
    //    }

    /** 待上架/已上架商品发生修改。指定接收者花名 */
    private String shelfItemInfoChange;

    public List<String> getShelfItemInfoChange() {
        return StringUtil.splitTrim(this.shelfItemInfoChange, ",");
    }

    private List<String> getFromBusinessLineRecipients(
            Integer businessLine, String group, Supplier<List<String>> defaultSupplier) {
        return Optional.ofNullable(businessLineRecipients)
                .map(v -> v.get(businessLine.toString()))
                .map(v -> v.get(group))
                .orElseGet(defaultSupplier);
    }
}
