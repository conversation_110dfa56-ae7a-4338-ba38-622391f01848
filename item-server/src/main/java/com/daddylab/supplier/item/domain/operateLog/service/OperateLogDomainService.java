package com.daddylab.supplier.item.domain.operateLog.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.LoginType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExternalUser;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IExternalUserService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.operateLog.TypedTargetId;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import javax.inject.Named;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OperateLogDomainService {

    @Inject
    OperateLogGateway operateLogGateway;
    @Inject
    @Named("UserGatewayCacheImpl")
    private UserGateway userGatewayCacheImpl;

    @Autowired
    private IExternalUserService externalUserService;

    public boolean batchAddOperateLog(List<OperateLog> operateLogs) {
        return operateLogGateway.batchAddOperateLog(operateLogs);
    }

    public void addOperatorLog(Long operatorId, OperateLogTarget target, List<Object> targetIds,
                               String msg) {
        if (StringUtil.isBlank(msg)) {
            return;
        }
        operateLogGateway.addOperatorLog(operatorId, target, targetIds, msg, null);
    }


    public void addOperatorLog(Long operatorId, OperateLogTarget target, Object targetId,
            String msg) {
        if (StringUtil.isBlank(msg)) {
            return;
        }
        operateLogGateway.addOperatorLog(operatorId, target, targetId, msg, null);
    }

    public void addOperatorLog(Long operatorId, OperateLogTarget target, Object targetId,
                               List<String> msgs, Object data) {
        if (CollectionUtil.isEmpty(msgs)) {
            return;
        }
        operateLogGateway.addOperatorLog(operatorId, target, targetId, msgs, data);
    }

    public void addOperatorLog(Long operatorId, OperateLogTarget target, Object targetId, String msg,
            Object data) {
        if (StringUtil.isBlank(msg)) {
            return;
        }
        operateLogGateway.addOperatorLog(operatorId, target, targetId, msg, data);
    }

    public void addOperatorLog(LoginType loginType, Long operatorId, OperateLogTarget target, Object targetId, String msg,
                               Object data) {
        if (StringUtil.isBlank(msg)) {
            return;
        }
        operateLogGateway.addOperatorLog(loginType, operatorId, target, targetId, msg, data);
    }

    public List<OperateLog> getOperateLogs(OperateLogTarget target, List<Object> targetIds) {
        if (CollUtil.isEmpty(targetIds)) {
            return Collections.emptyList();
        }
        final List<OperateLog> operateLogs = operateLogGateway.getOperateLogs(target, targetIds);
        return getOperateLogs(operateLogs);
    }

    public List<OperateLog> getOperateLogs(OperateLogTarget target, Object targetId) {
        if (Objects.isNull(targetId) || targetId instanceof Collection && ((Collection<?>) targetId).isEmpty()) {
            return Collections.emptyList();
        }
        final List<OperateLog> operateLogs = operateLogGateway.getOperateLogs(target, targetId);
        return getOperateLogs(operateLogs);
    }

    public List<OperateLog> getMultiTargetOperateLogs(List<TypedTargetId> targetIds) {
        if (CollectionUtil.isEmpty(targetIds)) {
            return Collections.emptyList();
        }
        final List<OperateLog> operateLogs = operateLogGateway.getMultiTargetOperateLogs(targetIds);
        return getOperateLogs(operateLogs);

    }

    public List<OperateLog> getOperateLogs(List<OperateLogTarget> target, Object targetId) {
        if (Objects.isNull(targetId)) {
            return Collections.emptyList();
        }
        final List<OperateLog> operateLogs = operateLogGateway.getOperateLogs(target, targetId);
        return getOperateLogs(operateLogs);
    }

    @NonNull
    private List<OperateLog> getOperateLogs(List<OperateLog> operateLogs) {
        final List<Long> operatorIds = operateLogs.stream()
                                                  .filter(v -> v.getOperatorType() == LoginType.DEFAULT.ordinal())
                                                  .map(OperateLog::getOperatorId)
                                                  .filter(NumberUtil::isPositive)
                                                  .distinct()
                                                  .collect(Collectors.toList());
        final Map<Long, StaffInfo> staffInfoMap = !operatorIds.isEmpty()
                ? userGatewayCacheImpl.batchQueryStaffInfoByIds(operatorIds)
                : Collections.emptyMap();
        final List<Long> externalOperatorIds = operateLogs.stream()
                                                  .filter(v -> v.getOperatorType() == LoginType.EXTERNAL_USER.ordinal())
                                                  .map(OperateLog::getOperatorId)
                                                  .filter(NumberUtil::isPositive)
                                                  .distinct()
                                                  .collect(Collectors.toList());
        final Map<Long, ExternalUser> externalUserMap = !externalOperatorIds.isEmpty() ? externalUserService.listByIds(externalOperatorIds)
                                                                                  .stream()
                                                                                  .collect(Collectors.toMap(
                                                                                          ExternalUser::getId,
                                                                                          Function.identity())) : Collections.emptyMap();
        return operateLogs
                .stream().peek(operateLog -> {
                    if (operateLog.getOperatorType() == LoginType.EXTERNAL_USER.ordinal()) {
                        operateLog.setOperatorName(Optional.ofNullable(externalUserMap.get(operateLog.getOperatorId()))
                                .map(ExternalUser::getName)
                                .orElse(""));
                    } else {
                        operateLog.setOperatorName(
                                Optional.ofNullable(staffInfoMap.get(operateLog.getOperatorId()))
                                        .map(StaffInfo::getUserName)
                                        .orElse("系统自动处理"));
                    }
                })
                .peek(operateLog -> {
                    if (operateLog.getOperatorType() == LoginType.EXTERNAL_USER.ordinal()) {
                        operateLog.setOperatorNickName(Optional.ofNullable(externalUserMap.get(operateLog.getOperatorId()))
                                .map(ExternalUser::getName)
                                .orElse(""));
                    } else {
                        operateLog.setOperatorNickName(
                                Optional.ofNullable(staffInfoMap.get(operateLog.getOperatorId()))
                                        .map(StaffInfo::getNickname)
                                        .orElse("系统自动处理"));
                    }
                })
                .collect(Collectors.toList());
    }
}
