package com.daddylab.supplier.item.application.platformItem.data;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class WarehouseInventoryDetailItemVO {
    private String warehouseName;
    private String warehouseNo;
    private String skuCode;
    private BigDecimal allocableStock;
    private BigDecimal availableStock;
    private String allocableStockCalculation;
    private BigDecimal warnStock;
    private List<WarehouseInventoryDetailItemComposeSkuVO> composeSkus;
}
