package com.daddylab.supplier.item.infrastructure.gatewayimpl.item;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.domain.item.gateway.ItemExpressGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemExpressTemplate;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemExpressTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 11:55 上午
 * @description
 */
@Service
public class ItemExpressGatewayImpl implements ItemExpressGateway {

    @Autowired
    IItemExpressTemplateService iItemExpressTemplateService;

    @Override
    public void saveOrUpdateBatchExpress(List<ItemExpressTemplate> list) {
        iItemExpressTemplateService.saveOrUpdateBatch(list);
    }

    @Override
    public List<ItemExpressTemplate> getExpressByItemId(Long itemId) {
        QueryWrapper<ItemExpressTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ItemExpressTemplate::getItemId, itemId);
        return iItemExpressTemplateService.list(queryWrapper);
    }

    @Override
    public void removeExpress(List<Long> idList) {
        iItemExpressTemplateService.removeByIds(idList);
    }

    @Override
    public ItemExpressTemplate getExpressTemplateByItemId(Long itemId) {
        QueryWrapper<ItemExpressTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ItemExpressTemplate::getItemId, itemId);
        return iItemExpressTemplateService.getOne(queryWrapper);
    }
}
