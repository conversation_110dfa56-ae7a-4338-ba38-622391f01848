/*
package com.daddylab.supplier.item.application.virtualWarehouse;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.stockSpec.StockSpecBizService;
import com.daddylab.supplier.item.application.virtualWarehouse.dto.*;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.VirtualWarehouseTransMapper;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.EditSkuRatioDto;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.types.stockSpec.StockSpecQuery;
import com.daddylab.supplier.item.types.stockSpec.StockSpecVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

*/
/**
 * <AUTHOR> up
 * @date 2024年03月01日 3:53 PM
 *//*

@Service
@Slf4j
@RequiredArgsConstructor
public class StockConfigDetailBizServiceImpl implements StockConfigDetailBizService {

    final StockSpecBizService stockSpecBizService;
    final IVirtualWarehouseInventoryGoodsService iVirtualWarehouseInventoryGoodsService;
    final IShopInventoryGoodsService iShopInventoryGoodsService;
    final IVirtualWarehouseInventoryService iVirtualWarehouseInventoryService;
    final IVirtualWarehouseService iVirtualWarehouseService;
    final IShopInventoryService iShopInventoryService;
    final IShopService iShopService;
    final IWarehouseGoodsInventoryStaticsService iWarehouseGoodsInventoryStaticsService;
    final IWarehouseService iWarehouseService;
    final ShopGateway shopGateway;
    final VirtualWarehouseBizService virtualWarehouseBizService;
    final IWdtStockSpecService iWdtStockSpecService;
    final OperateLogGateway operateLogGateway;

    private final static BigDecimal ONE_HUNDRED = new BigDecimal(100);

    private StockSpecQuery convert(StockConfigDetailPageQuery pageQuery) {
        StockSpecQuery stockSpecQuery = new StockSpecQuery();
        stockSpecQuery.setWarehouseIds(pageQuery.getWarehouseIds());
        stockSpecQuery.setWarehouseNos(pageQuery.getWarehouseNos());
        stockSpecQuery.setSkuCodes(pageQuery.getSkuCodes());
        stockSpecQuery.setSpuCodes(pageQuery.getSpuCodes());
        stockSpecQuery.setBrandIds(pageQuery.getBrandIds());
        stockSpecQuery.setItemNames(pageQuery.getItemNames());
        stockSpecQuery.setCategoryIds(pageQuery.getCategoryIds());
        stockSpecQuery.setBusinessLine(pageQuery.getBusinessLine());
        stockSpecQuery.setPageIndex(pageQuery.getPageIndex());
        stockSpecQuery.setPageSize(pageQuery.getPageSize());
        return stockSpecQuery;
    }

    private Integer getSkuDbInventory(String skuCode, String warehouseNo, Long virtualWarehouseId) {
        Optional<VirtualWarehouseInventoryGoods> virtualWarehouseInventoryGoods = iVirtualWarehouseInventoryGoodsService.lambdaQuery()
                .eq(VirtualWarehouseInventoryGoods::getWarehouseNo, warehouseNo)
                .eq(VirtualWarehouseInventoryGoods::getSkuNo, skuCode)
                .eq(VirtualWarehouseInventoryGoods::getVirtualWarehouseId, virtualWarehouseId)
                .oneOpt();
        if (virtualWarehouseInventoryGoods.isPresent()) {
            return virtualWarehouseInventoryGoods.get().getInventoryRatio();
        }
        Optional<VirtualWarehouseInventory> inventoryOptional = iVirtualWarehouseInventoryService.lambdaQuery()
                .eq(VirtualWarehouseInventory::getWarehouseNo, warehouseNo)
                .eq(VirtualWarehouseInventory::getVirtualWarehouseId, virtualWarehouseId).oneOpt();
        return inventoryOptional.get().getInventoryRatio();
    }

    private Integer getWarehouseDbInventory(String warehouseNo, Long virtualWarehouseId) {
        Optional<VirtualWarehouseInventory> inventoryOptional = iVirtualWarehouseInventoryService.lambdaQuery()
                .eq(VirtualWarehouseInventory::getWarehouseNo, warehouseNo)
                .eq(VirtualWarehouseInventory::getVirtualWarehouseId, virtualWarehouseId).oneOpt();
        return inventoryOptional.get().getInventoryRatio();
    }

    public MultiResponse<SkuRatioPrompt> checkVirtualWarehouseSkuRatio0(VwStockConfigDetailCmd vwStockConfigDetailCmd) {
        String warehouseNo = vwStockConfigDetailCmd.getWarehouseNo();
        List<String> skuCodes = iWdtStockSpecService.lambdaQuery().eq(WdtStockSpec::getWarehouseNo, warehouseNo).eq(WdtStockSpec::getDefect, 0)
                .select(WdtStockSpec::getSpecNo).list().stream().map(WdtStockSpec::getSpecNo).collect(Collectors.toList());
        if (CollUtil.isEmpty(skuCodes)) {
            return MultiResponse.buildSuccess();
        }
        List<WarehouseGoodsInventoryStatics> staticsList = iWarehouseGoodsInventoryStaticsService.lambdaQuery()
                .eq(WarehouseGoodsInventoryStatics::getWarehouseNo, warehouseNo)
                .in(WarehouseGoodsInventoryStatics::getSkuNo, skuCodes).list();
        Long virtualWarehouseId = vwStockConfigDetailCmd.getVirtualWarehouseId();
        boolean isAdd = Objects.isNull(virtualWarehouseId);
        Integer type = vwStockConfigDetailCmd.getType();
        boolean statusChange = Objects.nonNull(type);
        boolean stopToRun = false;
        boolean runToStop = false;
        if (statusChange) {
            runToStop = type == 1;
            stopToRun = type == 2;
        }
        Integer reqInventoryParam = vwStockConfigDetailCmd.getInventoryRatio();

        List<SkuRatioPrompt> skuRatioPromptList = new LinkedList<>();
        for (WarehouseGoodsInventoryStatics staticsDto : staticsList) {
            Integer staticsInventory = staticsDto.getInventoryRatio();
            int thisSkuHadInventory = 0;
            int reqInventory = 0;
            String skuNo = staticsDto.getSkuNo();

            if (isAdd) {
                reqInventory = reqInventoryParam;
            } else {
                VirtualWarehouse virtualWarehouse = iVirtualWarehouseService.getById(virtualWarehouseId);
                boolean isStopWarehouse = virtualWarehouse.getStatus() == 1;
                if (isStopWarehouse) {
                    if (!statusChange) {
                        return MultiResponse.buildSuccess();
                    } else {
                        if (stopToRun) {
                            thisSkuHadInventory = getSkuDbInventory(skuNo, warehouseNo, virtualWarehouseId);
                            if (Objects.isNull(reqInventoryParam)) {
                                reqInventoryParam = getWarehouseDbInventory(warehouseNo, virtualWarehouseId);
                            }
                        } else {
                            // 状态非法，停用状态的仓库，怎么会监听到【运行->停用】的状态变化。
                            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "状态非法，停用状态的仓库，怎么会监听到【运行->停用】的状态变化？");
                        }
                    }
                } else {
                    if (!statusChange) {
                        thisSkuHadInventory = getSkuDbInventory(skuNo, warehouseNo, virtualWarehouseId);
                        if (Objects.isNull(reqInventoryParam)) {
                            reqInventoryParam = getWarehouseDbInventory(warehouseNo, virtualWarehouseId);
                        }
                    } else {
                        if (runToStop) {
                            return MultiResponse.buildSuccess();
                        } else {
                            // 状态非法，运行状态的仓库，怎么会监听到【停用->运行】的状态变化。
                            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "状态非法，运行状态的仓库，怎么会监听到【停用->运行】的状态变化？");
                        }
                    }
                }
            }

            int otherWarehouseSkuSum = staticsInventory - thisSkuHadInventory;
            int thisRemain = 100 - otherWarehouseSkuSum;
            if (reqInventory > thisRemain) {
                SkuRatioPrompt prompt = new SkuRatioPrompt();
                prompt.setSkuCode(skuNo);
                prompt.setStatics(otherWarehouseSkuSum);
                prompt.setRemain(thisRemain);
                skuRatioPromptList.add(prompt);
            }
        }

        if (CollUtil.isNotEmpty(skuRatioPromptList)) {
            return MultiResponse.of(skuRatioPromptList);
        }
        return MultiResponse.buildSuccess();

    }

    @Override
    public MultiResponse<SkuRatioPrompt> checkSkuRatio(VwStockConfigDetailCmd vwStockConfigDetailCmd) {
        String warehouseNo = vwStockConfigDetailCmd.getWarehouseNo();
        Integer reqInventoryRatio = vwStockConfigDetailCmd.getInventoryRatio();
        Assert.notNull(reqInventoryRatio, "仓库请求占比不得为空");
        Long virtualWarehouseId = vwStockConfigDetailCmd.getVirtualWarehouseId();

        List<String> skuCodes = iWdtStockSpecService.lambdaQuery().eq(WdtStockSpec::getWarehouseNo, warehouseNo).eq(WdtStockSpec::getDefect, 0)
                .select(WdtStockSpec::getSpecNo).list().stream().map(WdtStockSpec::getSpecNo).collect(Collectors.toList());
        if (CollUtil.isEmpty(skuCodes)) {
            return MultiResponse.buildSuccess();
        }
        List<WarehouseGoodsInventoryStatics> staticsList = iWarehouseGoodsInventoryStaticsService.lambdaQuery()
                .eq(WarehouseGoodsInventoryStatics::getWarehouseNo, warehouseNo)
                .in(WarehouseGoodsInventoryStatics::getSkuNo, skuCodes).list();
        List<String> thisWarehouseSkuCodes = staticsList.stream().map(WarehouseGoodsInventoryStatics::getSkuNo).collect(Collectors.toList());


        Map<String, VirtualWarehouseInventoryGoods> skuInventoryMap = new HashMap<>();
        VirtualWarehouse virtualWarehouse = null;
        Optional<VirtualWarehouseInventory> virtualWarehouseInventoryOpt = Optional.empty();
        List<VirtualWarehouseInventoryGoods> skuInventoryList = new ArrayList<>();
        if (Objects.nonNull(virtualWarehouseId)) {
            virtualWarehouseInventoryOpt = iVirtualWarehouseInventoryService.lambdaQuery()
                    .eq(VirtualWarehouseInventory::getWarehouseNo, warehouseNo)
                    .eq(VirtualWarehouseInventory::getVirtualWarehouseId, virtualWarehouseId).oneOpt();
            skuInventoryList = iVirtualWarehouseInventoryGoodsService.lambdaQuery()
                    .eq(VirtualWarehouseInventoryGoods::getWarehouseNo, warehouseNo)
                    .in(CollUtil.isNotEmpty(thisWarehouseSkuCodes), VirtualWarehouseInventoryGoods::getSkuNo, thisWarehouseSkuCodes)
                    .eq(VirtualWarehouseInventoryGoods::getVirtualWarehouseId, virtualWarehouseId).list();
            virtualWarehouse = iVirtualWarehouseService.getById(virtualWarehouseId);
            if (CollUtil.isNotEmpty(skuInventoryList)) {
                skuInventoryMap = skuInventoryList.stream().collect(Collectors.toMap(VirtualWarehouseInventoryGoods::getSkuNo, Function.identity(), (a, b) -> a));
            }
        }

        List<SkuRatioPrompt> msgList = new LinkedList<>();
        if (CollUtil.isNotEmpty(staticsList)) {
            for (WarehouseGoodsInventoryStatics statics : staticsList) {

                int thisSkuWarehouseRatio = 0;
                // 编辑处理，计算 此SKU + 此仓库 在此虚拟仓的占比
                if (Objects.nonNull(virtualWarehouseId)) {
                    VirtualWarehouseInventoryGoods virtualWarehouseInventoryGoods = skuInventoryMap.get(statics.getSkuNo());
                    // 虚拟仓正常状态
                    if (virtualWarehouse.getStatus() == 0) {
                        if (Objects.nonNull(virtualWarehouseInventoryGoods)) {
                            thisSkuWarehouseRatio = virtualWarehouseInventoryGoods.getInventoryRatio();
                        } else {
                            if (virtualWarehouseInventoryOpt.isPresent()) {
                                thisSkuWarehouseRatio = virtualWarehouseInventoryOpt.get().getInventoryRatio();
                            }
                        }
                    }
                    // 虚拟仓停用状态
                    if (virtualWarehouse.getStatus() == 1) {
                        return MultiResponse.buildSuccess();
                    }
                }

                int otherWarehouseSkuSum = statics.getInventoryRatio() - thisSkuWarehouseRatio;
                int thisRemain = 100 - otherWarehouseSkuSum;
                if (thisRemain < reqInventoryRatio) {
                    SkuRatioPrompt prompt = new SkuRatioPrompt();
                    prompt.setSkuCode(statics.getSkuNo());
                    prompt.setStatics(otherWarehouseSkuSum);
                    prompt.setRemain(thisRemain);
                    msgList.add(prompt);
                }
            }
            if (CollUtil.isNotEmpty(msgList)) {
                return MultiResponse.of(msgList);
            }
        }

        return MultiResponse.buildSuccess();
    }


    private Integer getShopSkuRatio(String skuCode, String warehouseNo, Long shopId) {
        Optional<ShopInventoryGoods> shopInventoryGoods = iShopInventoryGoodsService.lambdaQuery()
                .eq(ShopInventoryGoods::getShopId, shopId)
                .eq(ShopInventoryGoods::getWarehouseNo, warehouseNo)
                .eq(ShopInventoryGoods::getSkuNo, skuCode).oneOpt();
        if (shopInventoryGoods.isPresent()) {
            return shopInventoryGoods.get().getInventoryRatio();
        }

        Optional<ShopInventory> shopInventory = iShopInventoryService.lambdaQuery()
                .eq(ShopInventory::getShopId, shopId)
                .eq(ShopInventory::getWarehouseNo, warehouseNo).oneOpt();
        if (shopInventory.isPresent()) {
            return shopInventory.get().getInventoryRatio();
        }

        return 0;
    }

    public MultiResponse<SkuRatioPrompt> checkShopSkuRatio(ShopStockConfigDetailCmd shopStockConfigDetailCmd) {
        String warehouseNo = shopStockConfigDetailCmd.getWarehouseNo();
        Optional<Warehouse> warehouseOptional = iWarehouseService.queryWarehouseByNo(warehouseNo);
        Assert.isTrue(warehouseOptional.isPresent());
        Warehouse warehouse = warehouseOptional.get();
        boolean isVw = warehouse.getIsVirtualWarehouse() == 1;
        Long shopId = shopStockConfigDetailCmd.getShopId();

        List<WarehouseGoodsInventoryStatics> staticsList = iWarehouseGoodsInventoryStaticsService.lambdaQuery().eq(WarehouseGoodsInventoryStatics::getWarehouseNo, warehouseNo).list();

        List<SkuRatioPrompt> list = new LinkedList<>();
        for (WarehouseGoodsInventoryStatics staticsDto : staticsList) {
            Integer staticsInventory = staticsDto.getInventoryRatio();
            int thisSkuHadInventory;
            int reqInventory = 0;
            String skuNo = staticsDto.getSkuNo();

            thisSkuHadInventory = getShopSkuRatio(skuNo, warehouseNo, shopId);
            int otherInventorySum = staticsInventory - thisSkuHadInventory;
            int remain = 100 - otherInventorySum;
            if (Objects.nonNull(shopStockConfigDetailCmd.getInventoryRatio())) {
                reqInventory = shopStockConfigDetailCmd.getInventoryRatio();
            }
            if (reqInventory > remain) {
                SkuRatioPrompt skuRatioPrompt = new SkuRatioPrompt();
                skuRatioPrompt.setSkuCode(skuNo);
                skuRatioPrompt.setStatics(otherInventorySum);
                skuRatioPrompt.setRemain(remain);
                list.add(skuRatioPrompt);
            }
        }

        if (CollUtil.isNotEmpty(list)) {
            return MultiResponse.of(list);
        }

        return MultiResponse.buildSuccess();
    }


    @Override
    public PageResponse<StockConfigDetailPageVO> page(StockConfigDetailPageQuery pageQuery) {
        org.springframework.util.Assert.notNull(pageQuery.getQueryType(), "库存明细查询分类参数不得为空");
        int queryType = pageQuery.getQueryType();
        StockSpecQuery stockSpecQuery = convert(pageQuery);
        List<String> queryWarehouseNos = stockSpecQuery.getWarehouseNos();

        if (1 == queryType) {
            VwStockConfigDetailCmd vwStockConfigDetailCmd = pageQuery.getVwStockConfigDetailCmd();
            Integer entryType = vwStockConfigDetailCmd.getEntryType();
            if (1 == entryType) {
                Long virtualWarehouseId = vwStockConfigDetailCmd.getVirtualWarehouseId();
                Assert.notNull(virtualWarehouseId, "虚拟仓ID不得为空");
                VirtualWarehouse virtualWarehouse = iVirtualWarehouseService.getById(virtualWarehouseId);
                List<VirtualWarehouseInventory> warehouseNos = getWarehouseNos(virtualWarehouseId);
                List<String> reqWarehouseNos = warehouseNos.stream().map(VirtualWarehouseInventory::getWarehouseNo).collect(Collectors.toList());
                if (CollUtil.isEmpty(queryWarehouseNos)) {
                    stockSpecQuery.setWarehouseNos(reqWarehouseNos);
                }
                stockSpecQuery.setBusinessLine(ListUtil.of(virtualWarehouse.getBusinessLine()));
            } else if (2 == entryType) {
                String warehouseNo = vwStockConfigDetailCmd.getWarehouseNo();
                Assert.hasText(warehouseNo, "虚拟仓编码不得为空");
                if (CollUtil.isEmpty(queryWarehouseNos)) {
                    stockSpecQuery.setWarehouseNos(ListUtil.of(warehouseNo));
                } else {
                    queryWarehouseNos.addAll(ListUtil.of(warehouseNo));
                    stockSpecQuery.setWarehouseNos(queryWarehouseNos);
                }
                if (Objects.isNull(vwStockConfigDetailCmd.getBusinessLine())) {
                    stockSpecQuery.setBusinessLine(ListUtil.of(0, 1, 2, 3));
                } else {
                    stockSpecQuery.setBusinessLine(ListUtil.of(vwStockConfigDetailCmd.getBusinessLine()));
                }
            } else {
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "entryType is wrong");
            }
            PageResponse<StockSpecVO> pageResponse = stockSpecBizService.query(stockSpecQuery);
            org.springframework.util.Assert.state(pageResponse.isSuccess(), "查询仓库数据异常");
            if (pageResponse.getTotalCount() == 0) {
                return ResponseFactory.emptyPage();
            }

            return page1(vwStockConfigDetailCmd, pageResponse);
        }

        if (2 == queryType) {
            ShopStockConfigDetailCmd shopStockConfigDetailCmd = pageQuery.getShopStockConfigDetailCmd();
            Assert.hasText(shopStockConfigDetailCmd.getWarehouseNo(), "仓库编码不得为空");
            Long shopId = shopStockConfigDetailCmd.getShopId();
            Shop shop = iShopService.getById(shopId);
            Assert.notNull(shop, "店铺ID非法");
            String warehouseNo = shopStockConfigDetailCmd.getWarehouseNo();
            Optional<Warehouse> warehouseOpt = iWarehouseService.queryWarehouseByNo(warehouseNo);
            Assert.isTrue(warehouseOpt.isPresent(), "此仓库编码非法。" + warehouseNo);
            Warehouse warehouse = warehouseOpt.get();
            List<VirtualWarehouseInventory> inventoryList = new LinkedList<>();

            if (warehouse.getIsVirtualWarehouse() == 1) {
                inventoryList = iVirtualWarehouseInventoryService.lambdaQuery()
                        .eq(VirtualWarehouseInventory::getVirtualWarehouseNo, warehouse.getNo()).list();
                org.springframework.util.Assert.isTrue(CollUtil.isNotEmpty(inventoryList), "此虚拟仓下没有任何实仓配置。" + warehouse.getNo());
                List<String> realWarehouseNoList = inventoryList.stream().map(VirtualWarehouseInventory::getWarehouseNo).collect(Collectors.toList());
                queryWarehouseNos.addAll(realWarehouseNoList);
            } else {
                queryWarehouseNos.add(warehouse.getNo());
            }

            stockSpecQuery.setBusinessLine(ListUtil.of(0, 1, 2, 3));
            PageResponse<StockSpecVO> pageResponse = stockSpecBizService.query(stockSpecQuery);
            org.springframework.util.Assert.state(pageResponse.isSuccess());
            if (pageResponse.getTotalCount() == 0) {
                return ResponseFactory.emptyPage();
            }

            return page2(shopStockConfigDetailCmd, pageResponse);
        }

        throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "this is a wrong way,please choose again");
    }


    private List<VirtualWarehouseInventory> getWarehouseNos(Long virtualWarehouseId) {
        return iVirtualWarehouseInventoryService.lambdaQuery()
                .eq(VirtualWarehouseInventory::getVirtualWarehouseId, virtualWarehouseId)
                .select(VirtualWarehouseInventory::getWarehouseNo).list();
    }


    private PageResponse<StockConfigDetailPageVO> page1(VwStockConfigDetailCmd cmd, PageResponse<StockSpecVO> stockSpecVoPageResponse) {
        Long virtualWarehouseId = cmd.getVirtualWarehouseId();
        List<StockSpecVO> data = stockSpecVoPageResponse.getData();
        List<StockConfigDetailPageVO> stockConfigDetailPageVos = VirtualWarehouseTransMapper.INSTANCE.stockSpecToStockConfigDetails(data);
        List<String> thisPageSkuCodes = stockConfigDetailPageVos.stream().map(StockConfigDetailPageVO::getSkuCode).collect(Collectors.toList());

        List<VirtualWarehouseInventoryGoods> virtualWarehouseInventoryGoods = iVirtualWarehouseInventoryGoodsService.lambdaQuery()
                .eq(VirtualWarehouseInventoryGoods::getVirtualWarehouseId, virtualWarehouseId)
                .in(VirtualWarehouseInventoryGoods::getSkuNo, thisPageSkuCodes)
                .list();

//        List<WarehouseGoodsInventoryStatics> staticsList = iWarehouseGoodsInventoryStaticsService.lambdaQuery()
//                .in(WarehouseGoodsInventoryStatics::getSkuNo, thisPageSkuCodes)
//                .in(WarehouseGoodsInventoryStatics::getWarehouseNo, cmd.g`etWarehouseNo()).list();
//        Map<String, Integer> staticsMap = staticsList.stream()
//                .collect(Collectors.toMap(WarehouseGoodsInventoryStatics::getSkuNo, WarehouseGoodsInventoryStatics::getInventoryRatio));

        for (StockConfigDetailPageVO vo : stockConfigDetailPageVos) {
            // 虚拟仓新建
            if (Objects.isNull(virtualWarehouseId)) {
//                Integer skuRatioVal = staticsMap.get(vo.getSkuCode());
                Integer skuRatioVal = cmd.getInventoryRatio();
                vo.setInventoryRatio(skuRatioVal);
                BigDecimal skuRatio = new BigDecimal(vo.getInventoryRatio()).divide(ONE_HUNDRED, 2, RoundingMode.DOWN);
                vo.setAvailableStockVirtual(vo.getAvailableStock().multiply(skuRatio).setScale(0, RoundingMode.HALF_UP));
                vo.setAvailableSendStockVirtual(vo.getAvailableSendStock().multiply(skuRatio).setScale(0, RoundingMode.HALF_UP));
            }
            // 编辑情况下
            else {
                if (Objects.nonNull(cmd.getInventoryRatio())) {
                    vo.setInventoryRatio(cmd.getInventoryRatio());
                    BigDecimal mulVal = new BigDecimal(vo.getInventoryRatio()).divide(ONE_HUNDRED, 2, RoundingMode.DOWN);
                    vo.setAvailableStockVirtual(vo.getAvailableStock().multiply(mulVal).setScale(0, RoundingMode.HALF_UP));
                    vo.setAvailableSendStockVirtual(vo.getAvailableSendStock().multiply(mulVal).setScale(0, RoundingMode.HALF_UP));
                } else {
                    Optional<VirtualWarehouseInventoryGoods> skuMatch = virtualWarehouseInventoryGoods.stream()
                            .filter(val -> val.getSkuNo().equals(vo.getSkuCode()) && val.getWarehouseNo().equals(vo.getWarehouseNo())).findFirst();
                    if (skuMatch.isPresent()) {
                        int skuInventoryRatio = skuMatch.get().getInventoryRatio();
                        BigDecimal skuRatio = new BigDecimal(skuInventoryRatio).divide(ONE_HUNDRED, 2, RoundingMode.DOWN);
                        vo.setInventoryRatio(skuInventoryRatio);
                        vo.setAvailableStockVirtual(vo.getAvailableStock().multiply(skuRatio).setScale(0, RoundingMode.HALF_UP));
                        vo.setAvailableSendStockVirtual(vo.getAvailableSendStock().multiply(skuRatio).setScale(0, RoundingMode.HALF_UP));
                    } else {
                        Optional<VirtualWarehouseInventory> virtualWarehouseInventoryOpt = iVirtualWarehouseInventoryService.lambdaQuery()
                                .eq(VirtualWarehouseInventory::getVirtualWarehouseId, virtualWarehouseId)
                                .eq(VirtualWarehouseInventory::getWarehouseNo, vo.getWarehouseNo()).oneOpt();
                        Assert.isTrue(virtualWarehouseInventoryOpt.isPresent(), "仓库配置信息为空，" + vo.getWarehouseNo());

                        vo.setInventoryRatio(virtualWarehouseInventoryOpt.get().getInventoryRatio());
                        BigDecimal mulVal = new BigDecimal(vo.getInventoryRatio()).divide(ONE_HUNDRED, 2, RoundingMode.DOWN);
                        vo.setAvailableStockVirtual(vo.getAvailableStock().multiply(mulVal).setScale(0, RoundingMode.HALF_UP));
                        vo.setAvailableSendStockVirtual(vo.getAvailableSendStock().multiply(mulVal).setScale(0, RoundingMode.HALF_UP));
                    }
                }
            }
        }
        return PageResponse.of(stockConfigDetailPageVos, stockSpecVoPageResponse.getTotalCount(),
                stockSpecVoPageResponse.getPageSize(), stockSpecVoPageResponse.getPageSize());

    }

    private PageResponse<StockConfigDetailPageVO> page2(ShopStockConfigDetailCmd cmd, PageResponse<StockSpecVO> stockSpecVoPageResponse) {
        // 仓库编码可实仓可虚拟仓
        String warehouseNo = cmd.getWarehouseNo();
        Long shopId = cmd.getShopId();
        Integer reqWarehouseInventory = cmd.getInventoryRatio();
        Optional<Warehouse> warehouseOpt = iWarehouseService.queryWarehouseByNo(warehouseNo);
        Assert.isTrue(warehouseOpt.isPresent(), "仓库编码非法" + warehouseNo);
        Warehouse warehouse = warehouseOpt.get();
        boolean isVw = warehouse.getIsVirtualWarehouse() == 1;
        Map<String, BigDecimal> realWarehouseInventoryMap = new HashMap<>(8);
        if (isVw) {
            realWarehouseInventoryMap = iVirtualWarehouseInventoryService.lambdaQuery().eq(VirtualWarehouseInventory::getVirtualWarehouseNo, warehouse.getNo()).list()
                    .stream().collect(Collectors.toMap(VirtualWarehouseInventory::getWarehouseNo,
                            virtualWarehouseInventory -> NumberUtil.toBigDecimal(virtualWarehouseInventory.getInventoryRatio()).divide(ONE_HUNDRED, 2, RoundingMode.DOWN)));
        }

        List<StockSpecVO> data = stockSpecVoPageResponse.getData();
        List<StockConfigDetailPageVO> stockConfigDetailPageVos = VirtualWarehouseTransMapper.INSTANCE.stockSpecToStockConfigDetails(data);
        List<String> thisPageSkuCodes = stockConfigDetailPageVos.stream().map(StockConfigDetailPageVO::getSkuCode).collect(Collectors.toList());

        List<ShopInventoryGoods> shopInventoryGoodsList = iShopInventoryGoodsService.lambdaQuery()
                .eq(ShopInventoryGoods::getShopId, shopId)
                .eq(ShopInventoryGoods::getWarehouseNo, warehouseNo)
                .in(ShopInventoryGoods::getSkuNo, thisPageSkuCodes)
                .list();

        for (StockConfigDetailPageVO vo : stockConfigDetailPageVos) {
            Optional<ShopInventory> shopInventoryOpt = iShopInventoryService.lambdaQuery()
                    .eq(ShopInventory::getShopId, shopId)
                    .eq(ShopInventory::getWarehouseNo, warehouseNo).oneOpt();
            if (!shopInventoryOpt.isPresent()) {
                Assert.notNull(reqWarehouseInventory, "新添加的仓库，库存占比不得为空");
                vo.setInventoryRatio(reqWarehouseInventory);
            } else {
                ShopInventory shopInventory = shopInventoryOpt.get();
                // 仓库纬度占比发生变，直接取新的占比值
                if (!shopInventory.getInventoryRatio().equals(reqWarehouseInventory)) {
                    vo.setInventoryRatio(reqWarehouseInventory);
                }
                Optional<ShopInventoryGoods> matchSkuOpt = shopInventoryGoodsList.stream()
                        .filter(val -> val.getSkuNo().equals(vo.getSkuCode()))
                        .findFirst();
                if (matchSkuOpt.isPresent()) {
                    vo.setInventoryRatio(matchSkuOpt.get().getInventoryRatio());
                } else {
                    vo.setInventoryRatio(shopInventory.getInventoryRatio());
                }
            }

            // 此店铺中虚拟仓的占比
//            BigDecimal vwRatio = null;
//            if (isVw) {
//                if (!shopInventoryOpt.isPresent()) {
//                    Assert.notNull(reqWarehouseInventory, "新添加的仓库，库存占比不得为空");
//                    vwRatio = new BigDecimal(reqWarehouseInventory).divide(ONE_HUNDRED, 2, RoundingMode.DOWN);
//                } else {
//                    vwRatio = new BigDecimal(shopInventoryOpt.get().getInventoryRatio()).divide(ONE_HUNDRED, 2, RoundingMode.DOWN);
//                }
//            }

            // sku占比
            BigDecimal mulVal = new BigDecimal(vo.getInventoryRatio()).divide(ONE_HUNDRED, 2, RoundingMode.DOWN);

            BigDecimal finalAvailableStock = vo.getAvailableStock();
            BigDecimal finalAvailableSendStock = vo.getAvailableSendStock();

            // 虚拟仓
            if (isVw) {
                BigDecimal realInVwRatio = realWarehouseInventoryMap.get(vo.getWarehouseNo());
                if (Objects.nonNull(realInVwRatio)) {
                    // 仓库数据为实仓数据*实仓在虚拟仓的占比
                    finalAvailableStock = finalAvailableStock.multiply(realInVwRatio).setScale(0, RoundingMode.HALF_UP);
                    finalAvailableSendStock = finalAvailableSendStock.multiply(realInVwRatio).setScale(0, RoundingMode.HALF_UP);
                }
            }
            vo.setAvailableStock(finalAvailableStock);
            vo.setAvailableSendStock(finalAvailableSendStock);
            vo.setAvailableStockVirtual(vo.getAvailableStock().multiply(mulVal).setScale(0, RoundingMode.HALF_UP));
            vo.setAvailableSendStockVirtual(vo.getAvailableSendStock().multiply(mulVal).setScale(0, RoundingMode.HALF_UP));

            if (isVw) {
                vo.setIsVirtualWarehouse(1);
            } else {
                vo.setIsVirtualWarehouse(0);
            }
        }

        return PageResponse.of(stockConfigDetailPageVos, stockSpecVoPageResponse.getTotalCount(), stockSpecVoPageResponse.getPageSize(), stockSpecVoPageResponse.getPageSize());
    }

    public static void main(String[] args) {
        BigDecimal bigDecimal = new BigDecimal(868);
        BigDecimal divide = new BigDecimal(10).divide(new BigDecimal(100).setScale(2, RoundingMode.DOWN));
        System.out.println(divide);
        System.out.println(bigDecimal.multiply(divide).setScale(0, RoundingMode.DOWN));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response updateStockConfigDetail(List<StockConfigDetailDto> cmdList) {

        List<StockConfigDetailDto> virtualWarehouseList = cmdList.stream().filter(val -> val.getSaveType() == 1).collect(Collectors.toList());
        List<StockConfigDetailDto> shopList = cmdList.stream().filter(val -> val.getSaveType() == 2).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(virtualWarehouseList)) {
            StockConfigDetailDto dto = virtualWarehouseList.get(0);
            Long vwId = dto.getRootId();
            String upperWarehouseNo = dto.getWarehouseNo();
            VirtualWarehouse virtualWarehouse = iVirtualWarehouseService.getById(vwId);
            boolean isStop = virtualWarehouse.getStatus() == 1;

            List<VirtualWarehouseInventoryGoods> addList1 = new LinkedList<>();
            List<VirtualWarehouseInventoryGoods> updateList1 = new LinkedList<>();
            List<EditSkuRatioDto> editSkuRatioDtoList = new LinkedList<>();
            List<String> logList = new LinkedList<>();

            for (StockConfigDetailDto stockConfigDetailDto : virtualWarehouseList) {
                Optional<VirtualWarehouseInventoryGoods> inventoryGoodsOpt = iVirtualWarehouseInventoryGoodsService.lambdaQuery()
                        .eq(VirtualWarehouseInventoryGoods::getWarehouseNo, upperWarehouseNo)
                        .eq(VirtualWarehouseInventoryGoods::getSkuNo, stockConfigDetailDto.getSkuCode())
                        .eq(VirtualWarehouseInventoryGoods::getVirtualWarehouseId, virtualWarehouse.getId())
                        .oneOpt();
                if (inventoryGoodsOpt.isPresent()) {
                    VirtualWarehouseInventoryGoods oldOne = inventoryGoodsOpt.get();
                    if (!oldOne.getInventoryRatio().equals(stockConfigDetailDto.getInventoryRatio())) {
                        if (!isStop) {
                            EditSkuRatioDto editSkuRatioDto = new EditSkuRatioDto();
                            editSkuRatioDto.setWarehouseNo(upperWarehouseNo);
                            editSkuRatioDto.setSkuNo(stockConfigDetailDto.getSkuCode());
                            editSkuRatioDto.setNewVal(stockConfigDetailDto.getInventoryRatio());
                            editSkuRatioDto.setOldVal(oldOne.getInventoryRatio());
                            editSkuRatioDtoList.add(editSkuRatioDto);
                        }
                        oldOne.setInventoryRatio(stockConfigDetailDto.getInventoryRatio());
                        updateList1.add(oldOne);
                        String msg = StrUtil.format("sku：{}-{}占比修改，从{}%改为{}%", oldOne.getWarehouseNo(), oldOne.getSkuNo(), oldOne.getInventoryRatio(), stockConfigDetailDto.getInventoryRatio());
                        logList.add(msg);
                    }
                } else {
                    Optional<VirtualWarehouseInventory> optional = iVirtualWarehouseInventoryService.lambdaQuery()
                            .eq(VirtualWarehouseInventory::getVirtualWarehouseId, vwId)
                            .eq(VirtualWarehouseInventory::getWarehouseNo, upperWarehouseNo)
                            .oneOpt();
                    Assert.isTrue(optional.isPresent(), "虚拟仓下库存配置不包含此仓库，" + upperWarehouseNo);
                    VirtualWarehouseInventory inventory = optional.get();
                    if (!isStop) {
                        EditSkuRatioDto editSkuRatioDto = new EditSkuRatioDto();
                        editSkuRatioDto.setWarehouseNo(upperWarehouseNo);
                        editSkuRatioDto.setSkuNo(stockConfigDetailDto.getSkuCode());
                        editSkuRatioDto.setNewVal(stockConfigDetailDto.getInventoryRatio());
                        editSkuRatioDto.setOldVal(inventory.getInventoryRatio());
                        editSkuRatioDtoList.add(editSkuRatioDto);
                    }
                    VirtualWarehouseInventoryGoods goods = new VirtualWarehouseInventoryGoods();
                    goods.setWarehouseNo(stockConfigDetailDto.getWarehouseNo());
                    goods.setSpuNo(stockConfigDetailDto.getSpuCode());
                    goods.setSkuNo(stockConfigDetailDto.getSkuCode());
                    goods.setInventoryRatio(stockConfigDetailDto.getInventoryRatio());
                    goods.setVirtualWarehouseId(vwId);
                    goods.setVirtualWarehouseNo(virtualWarehouse.getNo());
                    goods.setVirtualWarehouseInventoryId(inventory.getId());
                    iVirtualWarehouseInventoryGoodsService.save(goods);
                    String msg = StrUtil.format("sku：{}-{}占比修改，从{}%改为{}%", stockConfigDetailDto.getSkuCode(), stockConfigDetailDto.getSkuCode(), inventory.getInventoryRatio(), stockConfigDetailDto.getInventoryRatio());
                    logList.add(msg);
                }
            }

            iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(editSkuRatioDtoList);
            if (CollUtil.isNotEmpty(addList1)) {
                iVirtualWarehouseInventoryGoodsService.saveBatch(addList1);
            }
            if (CollUtil.isNotEmpty(updateList1)) {
                iVirtualWarehouseInventoryGoodsService.updateBatchById(updateList1);
            }
            if (CollUtil.isNotEmpty(logList)) {
                operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.VIRTUAL_WAREHOUSE, virtualWarehouse.getId(), StrUtil.join("。", logList), null);
            }
        }


        if (CollUtil.isNotEmpty(shopList)) {

            StockConfigDetailDto dto = shopList.get(0);
            Long shopId = dto.getRootId();
            String upperWarehouseNo = dto.getUpperWarehouseNo();
            Optional<ShopInventory> shopInventoryOpt = iShopInventoryService.lambdaQuery()
                    .eq(ShopInventory::getShopId, shopId)
                    .eq(ShopInventory::getWarehouseNo, upperWarehouseNo).oneOpt();
            Assert.isTrue(shopInventoryOpt.isPresent(), "店铺下库存配置不包含此仓库，" + upperWarehouseNo);
            ShopInventory shopInventory = shopInventoryOpt.get();
            Optional<Warehouse> warehouseOpt = iWarehouseService.queryWarehouseByNo(upperWarehouseNo);
            Assert.state(warehouseOpt.isPresent(), "仓库编码非法" + upperWarehouseNo);

            List<ShopInventoryGoods> addList2 = new LinkedList<>();
            List<ShopInventoryGoods> updateList2 = new LinkedList<>();
            List<EditSkuRatioDto> editSkuRatioDtoList = new LinkedList<>();
            List<String> logList = new LinkedList<>();

            for (StockConfigDetailDto stockConfigDetailDto : shopList) {
                Optional<ShopInventoryGoods> inventoryGoodsOpt = iShopInventoryGoodsService.lambdaQuery()
                        .eq(ShopInventoryGoods::getWarehouseNo, upperWarehouseNo)
                        .eq(ShopInventoryGoods::getSkuNo, stockConfigDetailDto.getSkuCode())
                        .eq(ShopInventoryGoods::getShopId, shopId)
                        .oneOpt();
                if (inventoryGoodsOpt.isPresent()) {
                    ShopInventoryGoods oldOne = inventoryGoodsOpt.get();
                    Integer oldInventory = oldOne.getInventoryRatio();
                    if (!oldInventory.equals(stockConfigDetailDto.getInventoryRatio())) {
                        EditSkuRatioDto editSkuRatioDto = new EditSkuRatioDto();
                        editSkuRatioDto.setWarehouseNo(upperWarehouseNo);
                        editSkuRatioDto.setSkuNo(stockConfigDetailDto.getSkuCode());
                        editSkuRatioDto.setNewVal(stockConfigDetailDto.getInventoryRatio());
                        editSkuRatioDto.setOldVal(oldOne.getInventoryRatio());
                        editSkuRatioDtoList.add(editSkuRatioDto);
                        oldOne.setInventoryRatio(stockConfigDetailDto.getInventoryRatio());
                        updateList2.add(oldOne);
                        String msg = StrUtil.format("sku：{}-{}占比修改，从{}%改为{}%", oldOne.getWarehouseNo(), oldOne.getSkuNo(), oldInventory, stockConfigDetailDto.getInventoryRatio());
                        logList.add(msg);
                    }
                } else {
                    EditSkuRatioDto editSkuRatioDto = new EditSkuRatioDto();
                    editSkuRatioDto.setWarehouseNo(upperWarehouseNo);
                    editSkuRatioDto.setSkuNo(stockConfigDetailDto.getSkuCode());
                    editSkuRatioDto.setNewVal(stockConfigDetailDto.getInventoryRatio());
                    editSkuRatioDto.setOldVal(shopInventory.getInventoryRatio());
                    editSkuRatioDtoList.add(editSkuRatioDto);
                    ShopInventoryGoods goods = new ShopInventoryGoods();
                    goods.setShopId(shopId);
                    goods.setWarehouseNo(upperWarehouseNo);
                    goods.setSpuNo(stockConfigDetailDto.getSpuCode());
                    goods.setSkuNo(stockConfigDetailDto.getSkuCode());
                    goods.setInventoryRatio(stockConfigDetailDto.getInventoryRatio());
                    goods.setShopInventoryId(shopInventory.getId());
                    addList2.add(goods);
                    String msg = StrUtil.format("sku：{}-{}占比修改，从{}%改为{}%", upperWarehouseNo, stockConfigDetailDto.getSkuCode(), shopInventory.getInventoryRatio(), stockConfigDetailDto.getInventoryRatio());
                    logList.add(msg);
                }
            }

            iWarehouseGoodsInventoryStaticsService.addBatchSkuRatio(editSkuRatioDtoList);
            if (CollUtil.isNotEmpty(addList2)) {
                iShopInventoryGoodsService.saveBatch(addList2);
            }
            if (CollUtil.isNotEmpty(updateList2)) {
                iShopInventoryGoodsService.updateBatchById(updateList2);
            }
            if (CollUtil.isNotEmpty(logList)) {
                operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.SHOP, shopId, StrUtil.join("。", logList), null);
            }
        }

        return Response.buildSuccess();
    }
}
*/
