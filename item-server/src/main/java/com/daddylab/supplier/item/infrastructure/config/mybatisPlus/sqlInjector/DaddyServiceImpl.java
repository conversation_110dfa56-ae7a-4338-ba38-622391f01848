package com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.io.Serializable;
import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/25 3:49 下午
 * @description
 */
public class DaddyServiceImpl<M extends DaddyBaseMapper<T>, T> extends ServiceImpl<M, T> implements IDaddyService<T> {

    @Override
    public boolean removeByIdWithTime(Serializable id) {
        return IDaddyService.super.removeByIdWithTime(id);
    }

    @Override
    public boolean removeByMapWithTime(Map<String, Object> columnMap) {
        return IDaddyService.super.removeByMapWithTime(columnMap);
    }

    @Override
    public boolean removeWithTime(Wrapper<T> queryWrapper) {
        return IDaddyService.super.removeWithTime(queryWrapper);
    }

    @Override
    public boolean removeByIdsWithTime(Collection<? extends Serializable> idList) {
        return IDaddyService.super.removeByIdsWithTime(idList);
    }

    @Override
    public M getDaddyBaseMapper() {
        return super.getBaseMapper();
    }
}
