package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.PropertyName;

import java.io.Serializable;

/**
 * <p>
 * 采购订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PurchaseOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @DiffIgnore
    private Long id;

    @TableField(fill = FieldFill.INSERT)
    @DiffIgnore
    private Long createdAt;

    @TableField(fill = FieldFill.UPDATE)
    @DiffIgnore
    private Long updatedAt;

    @TableField(fill = FieldFill.INSERT)
    @DiffIgnore
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    @DiffIgnore
    private Long updatedUid;

    @DiffIgnore
    private Long deletedAt;

    @TableLogic
    @DiffIgnore
    private Integer isDel;

    /**
     * 采购单号
     */
    @DiffIgnore
    private String no;

    /**
     * 采购日期
     */
    @PropertyName("采购日期")
    private Long purchaseDate;

    /**
     * 供应商id
     */
    @PropertyName("供应商")
    private Long providerId;

    /**
     * 采购类型。1：标准采购，2：工厂采购
     */
    @DiffIgnore
    private Integer type;

    /**
     * 入库数量
     */
    @DiffIgnore
    private Integer stockQuantity;

    /**
     * 采购状态。1:待提交、2:待审核、3:审核通过、4:审核拒绝、5:撤回审核、6:待完结、7:已完结、8:已关闭
     * <p>
     * {@link com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PurchaseOrderState}
     */
    @PropertyName("采购状态")
    private Integer state;

    /**
     * 采购员id
     */
    @PropertyName("采购员")
    private Long buyerUserId;

    /**
     * 付款方式。1:款到发货、2:预付10%、3:预付20%、4:预付30%、5:预付40%、6:预付50%、7:预付60%、8:货到付款、9:月结
     */
    @PropertyName("付款方式")
    private Integer payType;

    /**
     * 采购入库状态。1：待提交、2：待入库、3：已入库、4：已取消
     */
    @DiffIgnore
    private Integer stockInState;

    /**
     * 合同编号
     */
    @PropertyName("合同编号")
    private String contractNo;

    @PropertyName("合同编号")
    @DiffIgnore
    private Long contractId;

    /**
     * 采购组织id
     */
    @PropertyName("采购组织")
    private Long organizationId;

    /**
     * 采购部门
     */
    @PropertyName("采购部门")
    private Long departmentId;

    /**
     * 采购组id
     */
    @PropertyName("采购组")
    private Long groupId;

    /**
     * 到货方式。1一次性到货。2分批到货
     */
    @PropertyName("到货方式")
    private Integer arrivalType;

    /**
     * 备注
     */
    @DiffIgnore
    private String remark;

    /**
     * 采购总数量
     */
    @PropertyName("采购总数量")
    @DiffIgnore
    private Integer totalPurchaseQuantity;

    /**
     * 采购总金额(税前)
     */
    @PropertyName("采购总金额(税前)")
    @DiffIgnore
    private String totalPurchaseTaxAmount;

    /**
     * 采购总金额(税后)
     */
    @PropertyName("采购总金额(税后)")
    @DiffIgnore
    private String totalPurchaseAmount;

    /**
     * 总税额
     */
    @PropertyName("总税额")
    @DiffIgnore
    private String totalTaxAmount;

    /**
     * 来源。0 手动生成。1 系统生成。
     */
    @DiffIgnore
    private Integer source;

    /**
     * 合作模式。0:电商。1:老爸抽检。2:绿色家装。3:商家入驻
     */
    private Integer businessLine;

    /**
     * 此单据被冲销之后，对应的修正数据后的采购单id
     */
    @DiffIgnore
    private Long overrideOrderId;

    /**
     * 此单据被冲销之后，对应的修正数据后的采购单编码
     */
    @DiffIgnore
    private String overrideOrderNo;

    /**
     * 此单据关联的下游出入库单据是否生成过逆向数据（冲销流程一换，为了不重复处理的中间态标识）
     */
    @DiffIgnore
    private Integer hadReverse;


    /**
     * 老爸工作台流程ID
     */
    @DiffIgnore
    private String workbenchProcessId;

    /**
     * OA盖章流程ID
     */
    private String oaSealProcessId;

}
