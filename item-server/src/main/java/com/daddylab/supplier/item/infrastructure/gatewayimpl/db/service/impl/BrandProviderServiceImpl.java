package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BrandProvider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.BrandProviderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBrandProviderService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 品牌供应商 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
@Service
public class BrandProviderServiceImpl extends DaddyServiceImpl<BrandProviderMapper, BrandProvider> implements IBrandProviderService {

}
