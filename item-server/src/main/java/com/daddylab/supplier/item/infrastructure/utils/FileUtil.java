package com.daddylab.supplier.item.infrastructure.utils;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.URLUtil;
import org.apache.commons.io.input.BOMInputStream;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.Charset;

public class FileUtil {
    public static String getSuffix(String uri) {
        return cn.hutool.core.io.FileUtil.getSuffix(uri);
    }

    public static String getName(String uri) {
        return cn.hutool.core.io.FileUtil.getName(uri);
    }

    public static String ensureSuffix(String filename, String suffix) {
        suffix = StringUtil.startWith(suffix, ".") ? suffix : "." + suffix;
        return StringUtil.endWith(filename, suffix) ? filename : filename + suffix;
    }

    public static String getNameWithoutSuffix(String uri) {
        return cn.hutool.core.io.FileUtil.getPrefix(uri);
    }

    public static String getPathDir(String uri) {
        String pathSeparator = "/";
        String schemeSeparator = "//";

        String path = uri;

        //检查是否包含协议头，有的话需要移除
        final int indexOfScheme = path.indexOf(schemeSeparator);
        if (indexOfScheme > -1) {
            final int firstIndexOfPSAfterScheme = path
                    .indexOf(pathSeparator, indexOfScheme + schemeSeparator.length());
            if (firstIndexOfPSAfterScheme > -1) {
                path = path.substring(firstIndexOfPSAfterScheme);
            } else {
                path = path.substring(indexOfScheme + schemeSeparator.length());
            }
        }

        //不包含路径分隔符则没有继续检查的必要
        if (!path.contains(pathSeparator)) {
            return "";
        }

        //检查是否有参数，有的话需要移除
        final int indexOfQuestionMark = path.indexOf("?");
        if (indexOfQuestionMark > -1) {
            path = path.substring(0, indexOfQuestionMark);
        }

        final int lastIndexOfPS = path.lastIndexOf("/");
        if (lastIndexOfPS > -1) {
            path = path.substring(0, lastIndexOfPS);
        }
        return path + "/";
    }

    public static String getPath(String uri) {
        return URLUtil.getPath(uri);
    }

    public static InputStream getInputStream(File file) throws IORuntimeException {
        return IoUtil.toStream(file);
    }

    public static BufferedInputStream getBufferedInputStream(File file) throws IORuntimeException {
        return IoUtil.toBuffered(IoUtil.toStream(file));
    }

    public static String getMimeType(String uri) {
        return cn.hutool.core.io.FileUtil.getMimeType(uri);
    }

    public static BufferedReader getBufferedReader(File file, Charset charset) {
        final InputStream inputStream = getInputStream(file);
        return IoUtil.getReader(inputStream, charset);
    }

    public static OutputStream getBufferedOutputStream(File file) {
        return cn.hutool.core.io.FileUtil.getOutputStream(file);
    }

    public static BufferedOutputStream getBufferedOutputStream(String path) {
        return cn.hutool.core.io.FileUtil.getOutputStream(path);
    }

    public static BufferedWriter getWriter(String path, Charset charset, boolean isAppend) {
        return cn.hutool.core.io.FileUtil.getWriter(path, charset, isAppend);
    }

    public static BufferedWriter getWriter(File file, Charset charset, boolean isAppend) {
        return cn.hutool.core.io.FileUtil.getWriter(file, charset, isAppend);
    }

    public static File file(String path) {
        return new File(path);
    }

    public static File file(File parent, String path) {
        return cn.hutool.core.io.FileUtil.file(parent, path);
    }

    public static File touch(String path) {
        return cn.hutool.core.io.FileUtil.touch(path);
    }

    public static File touch(File file) {
        return cn.hutool.core.io.FileUtil.touch(file);
    }

    public static void move(File source, File dest, boolean override) {
        cn.hutool.core.io.FileUtil.move(source, dest, override);
    }

    public static void move(String source, String dest, boolean override) {
        cn.hutool.core.io.FileUtil.move(file(source), file(dest), override);
    }

    public static byte[] readBytes(File file) {
        return IOUtil.readBytes(getInputStream(file));
    }

    public static BOMInputStream getBOMInputStream(File file) {
        return new BOMInputStream(getInputStream(file));
    }

    public static MultipartFile fileToMultipartFile(File file) {
        return new CustomMultipartFile(file);
    }
}
