package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtSaleStockOutOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtSaleStockOutOrderService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 旺店通销售出库单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Service
public class WdtSaleStockOutOrderServiceImpl extends DaddyServiceImpl<WdtSaleStockOutOrderMapper, WdtSaleStockOutOrder> implements IWdtSaleStockOutOrderService {

}
