package com.daddylab.supplier.item.infrastructure.config;

import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.util.Arrays;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.NonNull;
import org.redisson.Redisson;
import org.redisson.api.NameMapper;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.ReadMode;
import org.redisson.config.SubscriptionMode;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/14 9:42 上午
 * @description
 */
@Configuration
@Data
public class RedissonConfig {

    @Value("${spring.redis.host:}")
    private String host;

    @Value("${spring.redis.cluster.nodes:}")
    private String nodes;

    @Value("${spring.redis.password}")
    private String password;

    @Bean(destroyMethod = "shutdown")
    public RedissonClient redissonClient() {
        final String prefix = ApplicationContextUtil.getDataIsolationPrefix();
        if (StringUtil.isNotBlank(nodes)) {
            return getClusterRedissonClient(prefix);
        }
        return getRedissonSingleClient(prefix);
    }

    @NonNull
    private RedissonClient getClusterRedissonClient(String prefix) {
        String[] cluster = nodes.split(",");
        String addresses = Arrays.stream(cluster)
                .map(c -> "redis://" + c)
                .collect(Collectors.joining(","));
        Config config = new Config();
        config.useClusterServers().addNodeAddress(addresses.split(","))
                .setReadMode(ReadMode.MASTER)
                .setSubscriptionMode(SubscriptionMode.MASTER)
                .setMasterConnectionPoolSize(10)
                .setMasterConnectionMinimumIdleSize(1)
                .setPingConnectionInterval(1000)
                .setRetryAttempts(1)
                .setRetryInterval(1000)
                .setCheckSlotsCoverage(false)
                .setPassword(password)
                .setNameMapper(new NameMapper() {
                    @Override
                    public String map(String name) {
                        return prefix + name;
                    }

                    @Override
                    public String unmap(String name) {
                        return StringUtil.trimStart(name, prefix);
                    }
                })
        ;

        return Redisson.create(config);
    }

    public RedissonClient getRedissonSingleClient(final String prefix) {
        Config config = new Config();
        config.useSingleServer()
                .setAddress(host)
                .setPingConnectionInterval(1000)
                .setRetryAttempts(3)
                .setRetryInterval(1000)
                .setPassword(password)
                .setNameMapper(new NameMapper() {
                    @Override
                    public String map(String name) {
                        return prefix + name;
                    }

                    @Override
                    public String unmap(String name) {
                        return StringUtil.trimStart(name, prefix);
                    }
                })
        ;

        return Redisson.create(config);
    }

}
