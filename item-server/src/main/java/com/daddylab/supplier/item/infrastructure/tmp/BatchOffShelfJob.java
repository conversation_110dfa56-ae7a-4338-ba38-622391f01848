package com.daddylab.supplier.item.infrastructure.tmp;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsDownRequest;
import com.daddylab.supplier.item.domain.item.service.ItemDomainService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataBackup;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DataBackupMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemMapper;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.vavr.collection.HashMultimap;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl.ItemServiceImpl;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.MultiSet;
import org.apache.commons.collections4.multiset.HashMultiSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class BatchOffShelfJob {

    @Autowired
    private ItemMapper itemMapper;

    @Autowired
    private DataBackupMapper dataBackupMapper;

    @Autowired
    private ItemDomainService itemDomainService;

    @Autowired
    private NewGoodsBizService newGoodsBizService;

    @Autowired
    private ItemServiceImpl itemService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Data
    public static class OffShelfExcelDTO {
        @ExcelProperty("商品编码")
        private String itemCode;

        @ExcelProperty("商品名称")
        private String itemName;
    }

    /**
     * 批量下架商品
     * @param file Excel文件
     * @return 处理结果
     */
    public void processOffShelf(MultipartFile file) {
        try {
            // 读取Excel文件
            List<OffShelfExcelDTO> excelData = EasyExcel.read(file.getInputStream())
                    .head(OffShelfExcelDTO.class)
                    .sheet()
                    .doReadSync();

            // 记录处理结果
            HashMultiSet<String> results = new HashMultiSet<>();


            // 批量下架处理
            for (OffShelfExcelDTO excelItem : excelData) {
                try {
                    Item item = itemService.getByMixedCode(excelItem.getItemCode());
                    if (item == null) {
                        log.error("商品编码[%s]不存在", excelItem.getItemCode());
                        results.add("商品编码不存在");
                        continue;
                    }

                    // 备份数据
                    DataBackup backup = new DataBackup();
                    backup.setKey1("item");
                    backup.setKey2(String.valueOf(item.getId()));
                    backup.setBackupData(objectMapper.writeValueAsString(item));
                    backup.setCreatedAt(DateUtil.currentTime());
                    dataBackupMapper.insert(backup);

                    NewGoodsDownRequest request = new NewGoodsDownRequest();
                    request.setItemId(item.getId());
                    request.setDownFrameTime(DateUtil.currentTime());
                    request.setDownFrameReason("运营批量下架");
                    newGoodsBizService.down(request);

                    log.info("商品下架成功，商品编码：{}", excelItem.getItemCode());
                    results.add("商品下架成功");
                } catch (Exception e) {
                    log.error("下架商品失败，商品编码：{}", excelItem.getItemCode(), e);
                    results.add("下架失败：" + e.getMessage());
                }
            }

        } catch (IOException e) {
            log.error("处理Excel文件失败", e);
            throw new RuntimeException("处理Excel文件失败：" + e.getMessage());
        }
    }

    /**
     * 还原备份数据
     * @param backupId 备份ID
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public String restoreBackup(Long backupId) {
        try {
            // 查询备份数据
            DataBackup backup = dataBackupMapper.selectById(backupId);
            if (backup == null) {
                return "备份数据不存在";
            }

            // 还原数据
            Item item = objectMapper.readValue(backup.getBackupData(), Item.class);
            itemMapper.updateById(item);

            return "还原成功";
        } catch (Exception e) {
            log.error("还原备份数据失败", e);
            throw new RuntimeException("还原备份数据失败：" + e.getMessage());
        }
    }
} 