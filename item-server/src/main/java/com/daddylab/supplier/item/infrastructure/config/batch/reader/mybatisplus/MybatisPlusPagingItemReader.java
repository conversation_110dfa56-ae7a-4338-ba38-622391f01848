package com.daddylab.supplier.item.infrastructure.config.batch.reader.mybatisplus;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mybatis.spring.batch.MyBatisPagingItemReader;
import org.springframework.batch.item.database.AbstractPagingItemReader;
import org.springframework.util.Assert;

import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;

import static org.springframework.util.ClassUtils.getShortName;

/**
 * Class  PagingItemReader
 * 自定义分页
 *
 * @date 2021/11/8上午9:51
 * <AUTHOR>
 */
public class MybatisPlusPagingItemReader<T> extends AbstractPagingItemReader<T> {

    private Function<IPage<T>, IPage<T>> function;


    public MybatisPlusPagingItemReader() {
        setName(getShortName(MyBatisPagingItemReader.class));
    }

    /**
     * Check mandatory properties.
     *
     * @see org.springframework.beans.factory.InitializingBean#afterPropertiesSet()
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        super.afterPropertiesSet();
    }

    @Override
    protected void doReadPage() {
        Assert.notNull(function, "baseMapper不能为空");
        if (results == null) {
            results = new CopyOnWriteArrayList<>();
        } else {
            results.clear();
        }
        IPage<T> iPage = function.apply(new Page<>(getPage() + 1, getPageSize()));
        if (iPage != null) {
            if (iPage.getTotal() > results.size()) {
                results.addAll(iPage.getRecords());
            }
        }
    }

    @Override
    protected void doJumpToPage(int itemIndex) {
        // Not Implemented
    }

    public void setFunction(Function<IPage<T>, IPage<T>> function) {
        this.function = function;
    }
}
