package com.daddylab.supplier.item.application.message.event;

import com.daddylab.supplier.item.domain.message.dto.MsgFillObj;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessageOperationType;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/27 11:46 上午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformWarnMessageEvent extends AbstractMessageEvent {

    String platformItemId;

    String goodsId;

    String warnMessage;

    private PlatformWarnMessageEvent() {
    }

    public static PlatformWarnMessageEvent build(String platformItemId, String goodsId, String warnMessage) {
        PlatformWarnMessageEvent event = new PlatformWarnMessageEvent();
        event.setOperationType(MessageOperationType.WARN_PLATFORM_ITEM);
        event.setPlatformItemId(platformItemId);
        event.setWarnMessage(warnMessage);
        event.setGoodsId(goodsId);
        return event;
    }

    @Override
    public String getPushLink() {
        final String defaultUrl = "https://erp-ol.daddylab.com/erp/commodity-management/platform-goods/detail?id=";
        return ApplicationContextUtil.getProperty("router.platform-item-detail-view", defaultUrl) + platformItemId;
    }

    @Override
    public List<MsgFillObj> getFillObjList() {
        MsgFillObj o1 = new MsgFillObj();
        o1.setIndex(0);
        o1.setVal(this.goodsId);
        o1.setLink(getPushLink());

        MsgFillObj o2 = new MsgFillObj();
        o2.setIndex(1);
        o2.setVal(this.warnMessage);
        o2.setColor("#FF0000");

        return Arrays.asList(o1, o2);
    }

    @Override
    public String getEmailSubject() {
        return "平台商品预警";
    }
}
