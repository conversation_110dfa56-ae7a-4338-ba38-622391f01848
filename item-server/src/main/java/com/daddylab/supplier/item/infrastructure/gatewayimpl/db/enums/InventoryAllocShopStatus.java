package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/** 1:启用 2:禁用 3:平台无法同步 */
@Getter
@AllArgsConstructor
public enum InventoryAllocShopStatus implements IEnum<Integer> {
  ACTIVE(1, "启用"),
  FORBIDDEN(2, "禁用"),
  UNABLE(3, "平台无法同步");

  @EnumValue
  private final Integer value;
  private final String desc;

  public static InventoryAllocShopStatus of(Integer value) {
    for (InventoryAllocShopStatus stateEnum : InventoryAllocShopStatus.values()) {
      if (stateEnum.getValue().equals(value)) {
        return stateEnum;
      }
    }
    return null;
  }
}
