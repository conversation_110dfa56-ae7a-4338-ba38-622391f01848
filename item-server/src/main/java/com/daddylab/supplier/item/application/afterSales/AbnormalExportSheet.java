package com.daddylab.supplier.item.application.afterSales;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> up
 * @date 2022年10月18日 6:19 PM
 */
@EqualsAndHashCode
@Data
@ContentRowHeight(16)
@HeadRowHeight(20)
public class AbnormalExportSheet extends ExportSheet {

    @ColumnWidth(50)
    @ExcelProperty("物流单号")
    private String logisticsNo;

    @ColumnWidth(50)
    @ExcelProperty("物流名称")
    private String logisticsName;

    @ColumnWidth(50)
    @ExcelProperty("商品名称")
    private String itemName;

    @ColumnWidth(50)
    @ExcelProperty("数量")
    private Integer quantity;

    @ColumnWidth(50)
    @ExcelProperty("状态")
    private String stateStr;

    @ExcelIgnore
    private Integer state;

    @ColumnWidth(50)
    @ExcelProperty("创建时间")
    private String createdAtStr;

    @ColumnWidth(100)
    @ExcelProperty("备注")
    private String remark;


}
