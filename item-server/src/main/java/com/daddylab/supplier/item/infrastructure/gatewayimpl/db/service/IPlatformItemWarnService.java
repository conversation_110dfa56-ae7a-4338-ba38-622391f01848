package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemWarn;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemWarnMapper;

/**
 * <p>
 * 平台商品预警 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
public interface IPlatformItemWarnService extends IDaddylabServicePlus<PlatformItemWarn, PlatformItemWarnMapper> {

    PlatformItemWarn createPlatformItemWarn(PlatformItemWarnType platformItemWarnType, Long platformItemId, Long platformSkuId);

}
