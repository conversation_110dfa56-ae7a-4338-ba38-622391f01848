package com.daddylab.supplier.item.application.offShelf;


import com.daddylab.supplier.item.application.process.ProcessBizService;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OffShelfFeedback;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OffShelfInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OffShelfStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOffShelfFeedbackService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOffShelfInfoService;
import com.daddylab.supplier.item.types.process.ProcessBusinessType;
import java.util.Arrays;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEntityEvent;
import org.flowable.engine.delegate.event.AbstractFlowableEngineEventListener;
import org.flowable.engine.delegate.event.FlowableActivityEvent;
import org.flowable.engine.delegate.event.FlowableCancelledEvent;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/12/8
 */
@Component
@Slf4j
public class OffShelfProcessListener extends AbstractFlowableEngineEventListener {
    @Resource
    public ProcessBizService processBizService;
    @Resource
    public IOffShelfInfoService offShelfInfoService;
    @Resource
    public IOffShelfFeedbackService offShelfFeedbackService;
    @Resource
    OffShelfBizService offShelfBizService;
    @Resource
    OperateLogDomainService operateLogDomainService;

    @Override
    protected void activityStarted(FlowableActivityEvent event) {
        final String processInstanceId = event.getProcessInstanceId();
        final ProcessInstance processInstance = processBizService.processInstance(processInstanceId);
        if (processInstance != null) {
            log.info("下架流程事件:{}",
                     Arrays.toString(new Object[]{event.getType(), event.getActivityId(), event.getActivityName(), event.getProcessInstanceId(), processInstance.getBusinessKey()}));
            ProcessBusinessType.OFF_SHELF.getBusinessId(processInstance.getBusinessKey()).ifPresent(businessId -> {
                final OffShelfInfo offShelfInfo = offShelfInfoService.getById(businessId);
                if (offShelfInfo != null) {
                    if ("结束事件".equals(event.getActivityName())) {
                        operateLogDomainService.addOperatorLog(0L,
                                                               OperateLogTarget.OFF_SHELF,
                                                               offShelfInfo.getId(),
                                                               "流程结束");
                        offShelfInfo.setStatus(OffShelfStatus.FINISH);
                        offShelfInfoService.updateById(offShelfInfo);
                        offShelfBizService.executeOffShelf(offShelfInfo.getId());
                    }
                    if ("拒绝事件".equals(event.getActivityName())) {
                        offShelfInfo.setStatus(OffShelfStatus.REFUSE);
                        offShelfInfoService.updateById(offShelfInfo);
                        offShelfBizService.noticeRefused(offShelfInfo.getId());
                    }
                    if ("待处理".equals(event.getActivityName())) {
                        if (offShelfInfo.getStatus() != OffShelfStatus.WAIT_HANDLE) {
                            operateLogDomainService.addOperatorLog(0L,
                                                                   OperateLogTarget.OFF_SHELF,
                                                                   offShelfInfo.getId(),
                                                                   "流程前进到'待处理'");
                            offShelfInfo.setStatus(OffShelfStatus.WAIT_HANDLE);
                            offShelfInfoService.updateById(offShelfInfo);
                            offShelfBizService.noticeToProcess(offShelfInfo.getId());
                        }
                    }
                }
            });
        }
    }

    @Override
    protected void processCancelled(FlowableCancelledEvent event) {
        for (OffShelfInfo offShelfInfo : offShelfInfoService.listByProcessInstId(event.getProcessInstanceId())) {
            offShelfBizService.noticeRevoked(offShelfInfo.getId());
        }
    }

    @Override
    protected void activityCompleted(FlowableActivityEvent event) {
        super.activityCompleted(event);
    }

    @Override
    protected void taskCreated(FlowableEngineEntityEvent event) {
        final String processInstanceId = event.getProcessInstanceId();
        final Object entity = event.getEntity();

        final ProcessInstance processInstance = processBizService.processInstance(processInstanceId);
        if (processInstance == null) {
            return;
        }
        if (!(entity instanceof TaskEntity)) {
            return;
        }
        final Task task = (TaskEntity) entity;
        ProcessBusinessType.OFF_SHELF.getBusinessId(processInstance.getBusinessKey()).ifPresent(businessId -> {
            log.info("下架流程事件:{}",
                     Arrays.toString(new Object[]{event.getType(), processInstanceId, task.getId(), task.getName(), task.getTaskDefinitionKey(), processInstance.getBusinessKey()}));
            if ("待处理".equals(task.getName())) {
                final Long processBindingId = processBizService
                        .getVariable(event.getExecutionId(), "processBindingId", Long.class);
                if (processBindingId != null) {
                    final OffShelfFeedback feedback = offShelfFeedbackService.getById(processBindingId);
                    if (feedback != null) {
                        feedback.setTaskId(task.getId());
                        offShelfFeedbackService.updateById(feedback);
                        task.setAssignee(feedback.getProcessUid().toString());
                        processBizService.saveTask(task);
                    }
                }
            }
        });

    }


}
