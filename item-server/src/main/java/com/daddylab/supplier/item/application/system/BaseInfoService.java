package com.daddylab.supplier.item.application.system;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.common.enums.DetailModelType;
import com.daddylab.supplier.item.controller.common.dto.*;
import com.daddylab.supplier.item.domain.user.dto.StaffDropDownItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.types.baseinfo.ResponsiblePersonQuery;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizePlanDropDown;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizePlanQuery;
import com.daddylab.supplier.item.types.warehouse.WarehouseVO;
import com.daddylab.supplier.item.types.warehouse.WarehouseViewVO;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> up
 * @date 2022/3/24 6:24 下午
 */
public interface BaseInfoService {

    MultiResponse<BaseUnit> pageUnit(@RequestBody UnitPageQuery query);

    PageResponse<WarehouseVO> pageWarehouseVo(@RequestBody WarehousePageQuery stockPageQuery);

    SingleResponse<WarehouseViewVO> viewWarehouse(Long warehouseId, String warehouseName);

    SingleResponse<Boolean> exportWarehouse(@RequestBody WarehousePageQuery pageQuery);

    PageResponse<Organization> pageOrganization(@RequestBody OrganizationPageQuery stockPageQuery);

    PageResponse<ErpGroup> pageGroup(@RequestBody GroupPageQuery stockPageQuery);

    MultiResponse<TaxRate> listTaxRate();

    MultiResponse<Reason> reasonList(Integer reasonType);

    Boolean isOpenDetailModel(Integer type);

    void setOpenDetailModel(DetailModelType type, Boolean state);

    BaseUnit ById(Long id);

    /**
     * 上传附件文件到oss，返回file表的ID
     *
     * @param cmd
     * @return
     */
    SingleResponse<Long> uploadAdditional(AdditionalCmd cmd);

    SingleResponse<Additional> downloadAdditional(Long id);

    PageResponse<Additional> pageQueryAdditional(AdditionalPageQuery pageQuery);

    void deleteAdditional(Long id);

    PageResponse<ItemOptimizePlanDropDown> itemOptimizePlan(ItemOptimizePlanQuery query);

    /**
     * @param type 类型 1 采购负责人 2 QC负责人 3 技术负责人
     * @param name 花名过滤
     */
    MultiResponse<StaffDropDownItem> responsiblePersonQuery(ResponsiblePersonQuery query);

    /**
     * 获取发票抬头信息
     *
     * @return
     */
    MultiResponse<String> invoiceTitle(String param);

    SingleResponse<Object> getValByKey(String key);

    PageResponse<ExternalUserVo> externalUserPage(ExternalUserPageQuery query);

    SingleResponse<Boolean> delExternalUser(Long id);

    SingleResponse<Boolean> updateExternalUser(ExternalUserCmd cmd);

    SingleResponse<Boolean> addExternalUser(ExternalUserCmd cmd);




}
