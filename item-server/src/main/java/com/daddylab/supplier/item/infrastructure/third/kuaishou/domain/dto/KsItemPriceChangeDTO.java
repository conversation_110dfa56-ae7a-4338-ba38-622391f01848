package com.daddylab.supplier.item.infrastructure.third.kuaishou.domain.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @class KsItemCreate.java
 * @description 描述类的作用
 * @date 2024-03-01 17:53
 */
@NoArgsConstructor
@Data
public class KsItemPriceChangeDTO {

    /**
     * 快手商品id
     */
    private Long itemId;
    /**
     * 商家id
     */
    private Integer sellerId;
    /**
     * 价格，单位：元
     */
    private BigDecimal price;
    /**
     * 原价格
     */
    private BigDecimal beforePrice;
    /**
     * 价格变更时间
     */
    private Long updateTime;
}
