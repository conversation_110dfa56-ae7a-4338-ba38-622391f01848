package com.daddylab.supplier.item.application.item.combinationItem.dto.vo;

import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.controller.item.dto.ItemAttrDto;
import com.daddylab.supplier.item.controller.item.dto.SkuExtraPriceDto;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.GoodsType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/7 10:03 上午
 * @description
 */
@Data
@ApiModel("sku构成明细列表（不带价格）")
public class ComposeSkuVO {

    @ApiModelProperty("商品skuId")
    protected Long skuId;

    @ApiModelProperty("商品skuCode")
    protected String skuCode;

    protected String specialSkuCode;

    @ApiModelProperty("商品id")
    protected Long itemId;

    @ApiModelProperty("商品采购人 user_id")
    protected Long itemBuyerUserId;

    @ApiModelProperty("商品采购人 user_name")
    protected String itemBuyerUserName;

    @ApiModelProperty("商品采购人 花名")
    protected String itemBuyerNickName;

    @ApiModelProperty("商品编码")
    protected String itemCode;

    @ApiModelProperty("商品名称")
    protected String itemName;

    @ApiModelProperty("商品品牌")
    protected String brandName;

    @ApiModelProperty("商品规格")
    protected List<ItemAttrDto> specifications;

    @ApiModelProperty("商品品类")
    protected String category;

    @ApiModelProperty("商品主图")
    protected String itemImage;

    @ApiModelProperty("sku库存")
    protected Long stockCount;

    @ApiModelProperty("仓库编号")
    protected String warehouseNo;

    @ApiModelProperty("仓库")
    protected String warehouseName;

    @ApiModelProperty("税率")
    protected BigDecimal taxRate;

    @ApiModelProperty("是否赠品")
    protected Integer isGift;

    @ApiModelProperty("条形码")
    protected String barCode;

    @ApiModelProperty("sku单位")
    protected String unit;

    @ApiModelProperty("成本金额占比")
    private BigDecimal costProportion;

    @ApiModelProperty("销售金额占比")
    private BigDecimal salesProportion;

    // ------------------------------------------------------

    @ApiModelProperty("构成组合商品的此单品sku的数量")
    protected Integer count;


    /**
     * 平台佣金
     */
    @ApiModelProperty("平台佣金")
    private BigDecimal platformCommission;

    @ApiModelProperty(value = "平台佣金历史记录", notes = "平台佣金历史记录")
    private List<SkuExtraPriceDto> platformCommissions;

    /**
     * 合同销售价
     */
    @ApiModelProperty("合同销售价")
    private BigDecimal contractSalePrice;

    @ApiModelProperty(value = "合同销售价", notes = "合同销售价历史记录")
    private List<SkuExtraPriceDto> contractSalePrices;

    @ApiModelProperty("商品状态。0:待上架 1:在售中 2:已下架 3:已删除(已废弃)")
    private Integer status;

//    @ApiModelProperty("业务类型列表")
//    private List<Integer> bizTypeList;

    @ApiModelProperty("单品业务类型")
    private List<CorpBizTypeDTO> corpBizType;


}
