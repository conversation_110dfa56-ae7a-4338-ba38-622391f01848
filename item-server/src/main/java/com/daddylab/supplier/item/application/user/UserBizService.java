package com.daddylab.supplier.item.application.user;

import com.alibaba.cola.dto.MultiResponse;
import com.daddylab.supplier.item.domain.user.dto.StaffDropBuyerQuery;
import com.daddylab.supplier.item.domain.user.dto.StaffDropDownItem;
import com.daddylab.supplier.item.domain.user.dto.StaffDropDownQuery;

public interface UserBizService {
    MultiResponse<StaffDropDownItem> staffDropDownList(StaffDropDownQuery query);

    MultiResponse<StaffDropDownItem> staffDropDownBuyerList(StaffDropBuyerQuery query);
}
