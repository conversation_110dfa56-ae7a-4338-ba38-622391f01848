package com.daddylab.supplier.item.infrastructure.accessControl;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;

import java.util.List;

/**
 * 员工访问控制相关信息（角色、部门）
 */
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Data
public class StaffAcInfo {
    /**
     * 员工ID
     */
    private Long staffId;

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 系统角色列表
     */
    private List<SysRoles> sysRoles;

    /**
     * 角色名称列表
     */
    private List<String> roles;

    /**
     * 性别 0男 1女
     */
    private Integer sex;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 真实姓名
     */
    private String realname;

    /**
     * 花名
     */
    private String nickname;

    /**
     * 入职日期
     */
    private Long indate;

    /**
     * 职位ID
     */
    private Long postId;

    /**
     * 职位
     */
    private String postName;

    /**
     * 岗位
     */
    private String position;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门
     */
    private String dept;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 状态 0:离职；1:在职
     */
    private Integer status;

    /**
     * 登录名
     */
    private String loginName;

    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    @Data
    public static class SysRoles {
        private Long sysId;
        private String sysName;
        private List<Role> roles;
    }
}