package com.daddylab.supplier.item.controller.open.nuonuo;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.nuonuo.NuoNuoBizService;
import com.daddylab.supplier.item.application.nuonuo.dto.InvoiceCmd;
import com.daddylab.supplier.item.application.nuonuo.dto.InvoiceNewCmd;
import com.daddylab.supplier.item.application.nuonuo.dto.VerifyOrderNoCmd;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2024年08月01日 9:57 AM
 */
@Slf4j
@Api(value = "对外开票接口", tags = "对外开票接口")
@RestController
@RequestMapping("/open/c/api/invoice")
public class NuoNuoInvoiceController {


    @Resource
    NuoNuoBizService nuoNuoBizService;

    @ResponseBody
    @ApiOperation(value = "开票订单验证")
    @PostMapping("/verifyOrderNo")
    public Response verifyOrderNo(@RequestBody VerifyOrderNoCmd cmd) {
        return nuoNuoBizService.verifyOrderNoList(cmd.getOrderNoList());
    }

    @ResponseBody
    @ApiOperation(value = "开票请求")
    @PostMapping("/requestInvoiceNew")
    public Response requestInvoiceNew(@RequestBody InvoiceNewCmd cmd) {
        return nuoNuoBizService.requestInvoiceNew(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "请求开票金额")
    @PostMapping("/requestInvoiceAmount")
    public SingleResponse<BigDecimal> requestInvoiceAmount(@RequestBody InvoiceCmd cmd) {
        return nuoNuoBizService.calculateInvoicedAmount(cmd.getOrderNoList());
    }


    @ResponseBody
    @ApiOperation(value = "开票请求(重试)")
    @GetMapping("/requestInvoiceNewRetry")
    @Auth(noAuth = true)
    public Response requestInvoiceNewRetry(String orderNo) {
        return nuoNuoBizService.requestInvoiceNewReTry(orderNo);
    }

//    @ResponseBody
//    @ApiOperation(value = "全额冲红")
//    @PostMapping("/creditNoteApply")
//    public Response creditNoteApply(@RequestBody InvoiceCmd cmd) {
//        return nuoNuoBizService.creditNoteApply(cmd.getOrderNoList());
//    }
//
//    @ResponseBody
//    @ApiOperation(value = "全额冲红(重试)")
//    @PostMapping("/creditNoteApplyRetry")
//    @Auth(noAuth = true)
//    public Response creditNoteApplyRetry(@RequestBody InvoiceCmd cmd) {
//        return nuoNuoBizService.creditNoteApply(cmd.getOrderNoList());
//    }


}
