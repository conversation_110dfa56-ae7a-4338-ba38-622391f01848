package com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto;

import com.daddylab.supplier.item.application.psys.PsysCategoryConfig;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

import static cn.hutool.core.util.ObjectUtil.defaultIfNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/27 4:11 下午
 * @description
 */
@Data
public class PartnerItemResp implements Serializable {
    private static final long serialVersionUID = 849111274064707632L;

    /** 商品id */
    @ApiModelProperty("商品id")
    private Integer id;

    /** 商品款号 */
    @ApiModelProperty("商品款号")
    private String itemNo;

    /** 商品图片 */
    @ApiModelProperty("商品图片")
    private String itemImg;

    /** 名称 */
    @ApiModelProperty("名称")
    private String name;

    /** 商品类型 1 日化 2 食品 3 纺织鞋类 4 玩具 5 电器 6轻工百货 */
    private Integer type;

    /** 品牌 */
    @ApiModelProperty("品牌")
    private String brand;

    /** 账号id */
    @ApiModelProperty("账号id")
    private Integer partnerId;

    /** 供应商id */
    @ApiModelProperty("供应商id")
    private Integer organizationId;

    /** 供应商名称 */
    @ApiModelProperty("供应商名称")
    private String organizationName;

    /** 供应商社会信用代码 */
    @ApiModelProperty("供应商社会信用代码")
    private String organizationNo;

    /** 供应商 联系人 */
    @ApiModelProperty("供应商联系人")
    private String uName;

    private String mobile;
    private String province;
    private String city;
    private String area;
    private String addr;

    /** 采购负责人 */
    @ApiModelProperty("采购负责人")
    private Integer purchaseId;

    /** 采购负责人名字 */
    @ApiModelProperty("采购负责人名字")
    private String purchaseName;

    @JsonAlias("qc_ids")
    private List<Long> qcIds;

    @JsonAlias("qc_names")
    private List<String> qcNames;

    @JsonAlias("skill_ids")
    private List<Long> skillIds;

    @JsonAlias("skill_names")
    private List<String> skillNames;

    @JsonAlias("is_fast_up")
    private Integer isFastUp;

    /** moduleType  0供应商(电商选品) 1老爸抽检 2绿色家装 3商家入驻 */
    @JsonAlias("moduleType")
    private Integer moduleType;

    @JsonAlias("first_category_id")
    private Integer firstCategoryId;

    @JsonAlias("second_category_id")
    private Integer secondCategoryId;

    /** 产品备案链接 */
    @ApiModelProperty("产品备案链接")
    @JsonAlias("product_filing_link")
    private List<String> productFilingLink;

    /** 备案或注册编号 */
    @ApiModelProperty("备案或注册编号")
    @JsonAlias("product_filing_no")
    private String productFilingNo;

    /** 产品备案功效 */
    @ApiModelProperty("产品备案功效")
    @JsonAlias("product_filing_efficiency")
    private List<String> productFilingEfficiency;

    /** 防晒指数 */
    @ApiModelProperty("防晒指数")
    @JsonAlias("sun_protection_exponent")
    private String sunProtectionExponent;

    /** PM值 */
    @ApiModelProperty("PM值")
    @JsonAlias("pm_value")
    private String pmValue;

    /** 适用人员 */
    @ApiModelProperty("适用人员")
    @JsonAlias("applicable_person")
    private List<String> applicablePerson;

    /** 适用肤质 */
    @ApiModelProperty("适用肤质（旧）")
    @JsonAlias("applicable_skin_type")
    private String applicableSkinType;

    /** 适用肤质 */
    @ApiModelProperty("适用肤质")
    @JsonAlias("applicable_skin_type_new")
    private List<String> applicableSkinTypeNew;

    /** 是否为特殊用途化妆品 */
    @ApiModelProperty("是否为特殊用途化妆品")
    @JsonAlias("is_special_use_cosmetics")
    private String isSpecialUseCosmetics;

    /** 备案人/境内责任人 或者 注册人/境内责任人 */
    @JsonAlias("domestic_responsible_person")
    private String domesticResponsiblePerson;

    /** 备案人/境内责任人地址 或者 注册人/境内责任人地址 */
    @JsonAlias("domestic_responsible_person_addr")
    private String domesticResponsiblePersonAddr;

    /** 商品执行标准 */
    @ApiModelProperty("商品执行标准")
    @JsonAlias("item_standard")
    private List<PsysItemStandard> itemStandard;

    /** 工厂名称 */
    @ApiModelProperty("工厂名称")
    @JsonAlias("company_name")
    private String companyName;

    /** 工厂地址 */
    @ApiModelProperty("工厂地址")
    @JsonAlias("factory_address")
    private String factoryAddress;

    /** 商品成分 */
    @ApiModelProperty("商品成分")
    @JsonAlias("compositions")
    private List<PsysCompositions> compositions;

    /** 成分黑名单信息 */
    @ApiModelProperty("成分黑名单信息")
    @JsonAlias("compositions_info")
    private List<PsysCompositionCheckInfo> compositionsInfo;

    /** 是否黑名单 0否 1是 */
    @JsonAlias("is_blacklist")
    private Integer isBlacklist;

    /**
     * 合作模式 1电商 2电商-商家入驻 4绿色家装
     */
    @JsonAlias("cooperate_mode")
    private List<Integer> cooperateMode;

    /**
     * 是否为老爸抽检
     */
    @JsonAlias("has_dad_check")
    private Boolean hasDadCheck;

    /** 是否带有【美妆、护肤】的商品参数 */
    public boolean isBeautyAndSkincareCategory(PsysCategoryConfig config) {
        return config.isBeautyAndSkincareCategory(
                new int[] {
                    defaultIfNull(firstCategoryId, 0),
                    defaultIfNull(secondCategoryId, 0)
                });
    }

    public Integer isDadCheck() {
        if (hasDadCheck != null) {
            return hasDadCheck ? 1 : 0;
        }
        return moduleType == 1 ? 1 : 0;
    }

    /**
     * 	ItemCooperatorAll             = 0 // 电商
     * 	ItemCooperatorSupplier        = 1 // 电商
     * 	ItemCooperatorGreenHome       = 2 // 绿色家装
     * 	ItemCooperatorDadCheck        = 4 // 老爸抽检
     * 	ItemCooperatorEvaluateScience = 8 // 评测科普
     */
    @ApiModelProperty(value = "合作方", notes = "1:电商,2:绿色家装,4:老爸抽检,8:评测科普")
    private List<Integer> cooperator;

    /**
     * ItemBusinessTypeSupplierCooperate           = 1   // 电商-商品合作
     * 	ItemBusinessTypeSupplierMerchantSettlement  = 2   // 电商-商家入驻
     * 	ItemBusinessTypeSupplierDadList             = 4   // 电商-老爸清单
     * 	ItemBusinessTypeSupplierDadCheck            = 8   // 电商-老爸抽检
     * 	ItemBusinessTypeGreenHomeCooperate          = 16  // 绿色家装-商品合作
     * 	ItemBusinessTypeGreenHomeMerchantSettlement = 32  // 绿色家装-商家入驻
     * 	ItemBusinessTypeGreenHomeDadList            = 64  // 绿色家装-老爸清单
     * 	ItemBusinessTypeGreenHomeDadCheck           = 128 // 绿色家装-老爸抽检
     * 	ItemBusinessTypeCps                         = 256 // CPS合作品
     */
    @ApiModelProperty(value = "业务类型",notes = "1:电商-商品合作,2:电商-商家入驻,4:电商-老爸清单,8:电商-老爸抽检,16:绿色家装-商品合作,32:绿色加装-商家入驻,64:绿色家装-老爸清单,128:绿色家装-老爸抽检,256:CPS合作品")
    @JsonProperty(value = "business_type")
    private List<Integer> businessType;
}
