package com.daddylab.supplier.item.infrastructure.rocketmq;

import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.LogUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.StringJoiner;
import java.util.concurrent.TimeUnit;
import javax.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.common.message.MessageClientIDSetter;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQPushConsumerLifecycleListener;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ResolvableType;
import org.springframework.core.env.StandardEnvironment;

@Slf4j
public abstract class RocketMQConsumerBase<T> implements RocketMQListener<MessageExt>,
        RocketMQPushConsumerLifecycleListener {

    private static final String MSG_CONSUMED_PREFIX = "msg:consumed:";

    protected final RedissonClient redissonClient;
    protected final StandardEnvironment environment;
    protected final RefreshConfig refreshConfig;
    protected Type msgBodyType;
    protected RocketMQMessageListenerAnnotationWrapper annotation;
    private boolean init = false;

    public RocketMQConsumerBase(RedissonClient redissonClient,
            StandardEnvironment environment,
            RefreshConfig refreshConfig) {
        this.redissonClient = redissonClient;
        this.environment = environment;
        this.refreshConfig = refreshConfig;
    }

    /**
     * 需要对消费者做一些定制化操作的，在子类重写这个方法
     *
     * @param consumer DefaultMQPushConsumer
     */
    @Override
    public void prepareStart(DefaultMQPushConsumer consumer) {

    }

    @Override
    @SuppressWarnings("unchecked")
    public final void onMessage(MessageExt msg) {
        tryInit();
        log(msg, Level.INFO, "Received");

        final RBucket<Object> bucket = redissonClient
                .getBucket(MSG_CONSUMED_PREFIX + msg.getMsgId());
        if (bucket.isExists()) {
            log(msg, Level.WARN, "Repeat");
            return;
        }
        final String bodyStr = msg.getBody() == null ? "" : new String(msg.getBody());
        T msgBody;
        try {
            if (msgBodyType instanceof Class
                    && ((Class<?>) msgBodyType).isAssignableFrom(byte[].class)) {
                msgBody = (T) msg.getBody();
            } else if (msgBodyType instanceof Class
                    && ((Class<?>) msgBodyType).isAssignableFrom(String.class)) {
                msgBody = (T) bodyStr;
            } else if (msgBodyType instanceof Class
                    && ((Class<?>) msgBodyType).isAssignableFrom(Void.class)) {
                msgBody = null;
            } else {
                msgBody = JsonUtil.parse(bodyStr, msgBodyType);
            }
        } catch (Exception e) {
            log(msg, Level.ERROR, "BodyErr", "body={}", bodyStr, e);
            return;
        }
        try {
            handle(msg, msgBody);
            log(msg, Level.INFO, "Success");
            if (refreshConfig.getMqRepeatConsumeCheckTime() > 0) {
                bucket.trySetAsync(true, refreshConfig.getMqRepeatConsumeCheckTime(),
                        TimeUnit.SECONDS).exceptionally(e -> {
                    log(msg, Level.ERROR, "FlagErr", null, e);
                    return false;
                });
            }
        } catch (Throwable e) {
            log(msg, Level.ERROR, "HandleErr", "body={}", bodyStr, e);
            throw new RuntimeException("消费异常，通知上游重试，异常原因：" + e.getMessage());
        }
    }

    private void log(MessageExt msg, Level level, String logMsg) {
        log(msg, level, logMsg, null);
    }

    private void log(MessageExt msg, Level level, String logMsg, String appendFormat,
            Object... vars) {
        StringJoiner format = new StringJoiner(",");
        format.add("MQConsumer {},topic={},tags={},msgId={},offsetMsgId={},msgKeys={}");
        if (StringUtil.isNotBlank(appendFormat)) {
            format.add(appendFormat);
        }
        final List<Object> varsList = new ArrayList<>(Arrays.asList(logMsg
                , msg.getTopic(), msg.getTags(), MessageClientIDSetter.getUniqID(msg),
                msg.getMsgId(), msg.getKeys()));
        if (vars.length > 0) {
            varsList.addAll(Arrays.asList(vars));
        }
        LogUtil.log(log, level, format.toString(), varsList.toArray());
    }

    private void tryInit() {
        if (!init) {
            synchronized (this) {
                if (!init) {
                    final Class<?> clazz = this.getClass();
                    final ResolvableType resolvableType = ResolvableType.forClass(this.getClass());
                    msgBodyType = resolvableType.getSuperType().getGeneric(0).getType();
                    RocketMQMessageListener annotation = clazz
                            .getAnnotation(RocketMQMessageListener.class);
                    this.annotation = new RocketMQMessageListenerAnnotationWrapper(annotation);
                    init = true;
                }
            }
        }
    }

    /**
     * 收到消息后的处理逻辑
     *
     * @param msg  消息
     * @param body 消息体反序列化对象
     */
    protected abstract void handle(MessageExt msg, @Nullable T body);

    class RocketMQMessageListenerAnnotationWrapper {

        RocketMQMessageListener annotation;

        public RocketMQMessageListenerAnnotationWrapper(
                RocketMQMessageListener annotation) {
            this.annotation = annotation;
        }

        public String getConsumerGroup() {
            return environment.resolvePlaceholders(annotation.consumerGroup());
        }
    }
}
