package com.daddylab.supplier.item.controller.purchasePayable;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.purchasePayable.applyPayAudit.AuditManager;
import com.daddylab.supplier.item.application.purchasePayable.applyPayAudit.dto.AuditNodeCmd;
import com.daddylab.supplier.item.application.purchasePayable.applyPayAudit.dto.AuditProcessNodVo;
import com.daddylab.supplier.item.application.purchasePayable.dto.ApplyPayNoDto;
import com.daddylab.supplier.item.application.purchasePayable.dto.EditApplyPayOrderCmd;
import com.daddylab.supplier.item.application.purchasePayable.dto.SaveApplyOrderDetailDto;
import com.daddylab.supplier.item.application.purchasePayable.dto.SaveApplyOrderDto;
import com.daddylab.supplier.item.application.purchasePayable.query.ApplyOrderQueryPage;
import com.daddylab.supplier.item.application.purchasePayable.query.ApplyPayOrderDetailListQueryPage;
import com.daddylab.supplier.item.application.purchasePayable.query.PurchasePayableQueryPage;
import com.daddylab.supplier.item.application.purchasePayable.query.PurchasePayableVoucherQuery;
import com.daddylab.supplier.item.application.purchasePayable.service.PurchasePayableBizService;
import com.daddylab.supplier.item.application.purchasePayable.vo.*;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableApplyOrder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Api(value = "采购应付单相关API", tags = "采购应付单相关API")
@RestController
@RequestMapping("/purchase/payable")
public class PurchasePayableController {

    @Autowired
    private PurchasePayableBizService purchasePayableBizService;

    @ResponseBody
    @ApiOperation(value = "分页查询采购应付")
    @PostMapping("/page")
    public PageResponse<PurchasePayableVO> queryPage(@RequestBody @Validated PurchasePayableQueryPage queryPage) {
        return purchasePayableBizService.queryPage(queryPage);
    }


    @ResponseBody
    @ApiOperation(value = "应付凭证")
    @PostMapping("/voucher")
    public SingleResponse<PurchasePayableVoucherVO> voucher(@RequestBody @Validated PurchasePayableVoucherQuery query) {
        return purchasePayableBizService.voucher(query);
    }

    @ResponseBody
    @ApiOperation(value = "采购应付单明细")
    @GetMapping("/details")
    public SingleResponse<PurchasePayableDetailsVO> details(@RequestParam(value = "id", required = false) Long id
            , @RequestParam(value = "no", required = false) String no) {
        return purchasePayableBizService.details(id, no);
    }


    // -------------------------------------------------- --------------------------------------------------

    @ResponseBody
    @ApiOperation(value = "保存申请付款单（包括明细）")
    @PostMapping("/saveApplyOrder")
    public SingleResponse<Long> saveApplyOrder(@RequestBody @Validated SaveApplyOrderDto cmd) {
        return purchasePayableBizService.saveApplyOrder(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "编辑申请付款单（包括明细）")
    @PostMapping("/editApplyOrder")
    public SingleResponse<Boolean> editApplyOrder(@RequestBody @Validated EditApplyPayOrderCmd cmd) {
        return purchasePayableBizService.editApplyOrder(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "申请付款单tab列表")
    @PostMapping("/queryApplyOrder")
    public PageResponse<PurchasePayableApplyOrder> queryApplyOrder(@RequestBody ApplyOrderQueryPage queryPage) {
        return purchasePayableBizService.queryApplyOrder(queryPage);
    }

    @ResponseBody
    @ApiOperation(value = "根据申请付款单编号删除数据")
    @GetMapping("/deleteApplyOrderDetailByNo")
    public SingleResponse<Boolean> deleteApplyOrderDetailByNo(@RequestParam("applyNo") String applyNo) {
        return purchasePayableBizService.deleteApplyOrderDetailByNo(applyNo);
    }

    @ResponseBody
    @ApiOperation(value = "根据申请付款单编号查询申请付款信息（头信息）")
    @GetMapping("/applyOrderDetailByNo")
    public SingleResponse<ApplyPayOrderDetailVO> applyOrderDetailByNo(@RequestParam("applyNo") String applyNo) {
        return purchasePayableBizService.showApplyOrderDetailByApplyNo(applyNo);
    }

    @ResponseBody
    @ApiOperation(value = "根据申请付款单编号查询申请付款信息（sku列表）")
    @PostMapping("/applyOrderDetailListByNo")
    public MultiResponse<SaveApplyOrderDetailDto> applyOrderDetailListByNo(@RequestBody ApplyPayOrderDetailListQueryPage queryPage) {
        return purchasePayableBizService.showApplyOrderDetailListByApplyNo(queryPage);
    }

    // --------------------------------------------------------------

    @ResponseBody
    @ApiOperation(value = "根据选中的应付单检查供应商信息")
    @PostMapping("/checkProvider")
    public SingleResponse<Boolean> checkProvider(@RequestBody @Validated List<ApplyPayNoDto> applyNoList) {
        return purchasePayableBizService.checkProvider(applyNoList);
    }

    @ResponseBody
    @ApiOperation(value = "根据选中的应付单展示申请付款信息（头信息）")
    @PostMapping("/applyOrderDetail")
    public SingleResponse<ApplyPayOrderDetailVO> applyOrderDetail(@RequestBody @Validated List<ApplyPayNoDto> applyNoList) {
        return purchasePayableBizService.showApplyOrderDetail(applyNoList);
    }


    @ResponseBody
    @ApiOperation(value = "根据选中的应付单展示申请付款信息（sku列表）")
    @PostMapping("/applyOrderDetailList")
    public MultiResponse<ApplyPayOrderDetailListVO> applyOrderDetailList(@RequestBody @Validated List<ApplyPayNoDto> applyNoList) {
        return purchasePayableBizService.showApplyOrderDetailList(applyNoList);
    }

    @ResponseBody
    @ApiOperation(value = "应付单关联单据列表")
    @GetMapping("/payOrderRelation")
    public MultiResponse<ApplyPayOrderRelationVO> payOrderRelation(
            @RequestParam("no") @ApiParam(value = "应付单编号", name = "no") String no) {
        return purchasePayableBizService.queryPayOrderRelation(no);
    }


    // ---------------- 申请付款审核相关接口 --------------------

    @Resource
    private AuditManager auditManager;

    @ResponseBody
    @ApiOperation(value = "创建付款申请单审核流程")
    @GetMapping("/createAuditTask")
    public SingleResponse<Boolean> createAuditTask(
            @RequestParam("id") @ApiParam(value = "付款申请单id", name = "id") Long id,
            @RequestParam("amount") @ApiParam(value = "申请付款金额", name = "amount") BigDecimal amount) {
        auditManager.createTask(id, amount, UserContext.getUserId());
        return SingleResponse.of(true);
    }

    @ResponseBody
    @ApiOperation(value = "获取审核流程信息")
    @GetMapping("/getAuditTaskStream")
    public MultiResponse<AuditProcessNodVo> getAuditTaskStream(
            @RequestParam("id") @ApiParam(value = "付款申请单id", name = "id") Long id) {
        List<AuditProcessNodVo> auditStream = auditManager.getAuditStream(id);
        return MultiResponse.of(auditStream);
    }

    @ResponseBody
    @ApiOperation(value = "节点审核操作")
    @PostMapping("/auditNode")
    public SingleResponse<Boolean> auditNode(@RequestBody @Validated AuditNodeCmd cmd) {
        auditManager.auditNode(cmd, UserContext.getUserId());
        return SingleResponse.of(true);
    }

    @ResponseBody
    @ApiOperation(value = "回退到指定审核节点")
    @GetMapping("/rollbackDedicatedNodes")
    public SingleResponse<Boolean> rollbackDedicatedNodes(
            @RequestParam("id") @ApiParam(value = "付款申请单id", name = "id") Long id,
            @RequestParam("nodeId") @ApiParam(value = "要回退的指定节点id", name = "nodeId") Long nodeId) {
        auditManager.rollbackDedicatedNodes(id, nodeId, UserContext.getUserId());
        return SingleResponse.of(true);
    }

    @ResponseBody
    @ApiOperation(value = "审核流退回到发起人")
    @GetMapping("/rollbackInitiator")
    public SingleResponse<Boolean> rollbackInitiator(
            @RequestParam("id") @ApiParam(value = "付款申请单id", name = "id") Long id) {
        auditManager.rollbackInitiator(id, UserContext.getUserId());
        return SingleResponse.of(true);
    }

//    @PostMapping(value = "/readPayExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
//    @ApiOperation("读取excel创建付款申请单")
//    @Auth(noAuth = true)
//    public Response singleSku(@RequestParam("file") MultipartFile file) {
//        return null;
//    }


    // --------------------------------  付款申请后置处理 ------------------------------------

    @ResponseBody
    @ApiOperation(value = "补偿发起申请付款流程")
    @Auth(noAuth = true)
    @GetMapping("/payJob")
    public SingleResponse<Boolean> payJob(
            @RequestParam("no1") @ApiParam(value = "付款申请单编号", name = "no1") String no1,
            @RequestParam("no2") @ApiParam(value = "原采购应付单编号", name = "no2") String no2) {
        return purchasePayableBizService.compensateHedgeHandler(no1, no2);
    }

    @ResponseBody
    @ApiOperation(value = "删除付款申请单")
    @Auth(noAuth = true)
    @GetMapping("/deleteApplyPay")
    public SingleResponse<Boolean> deleteApplyPay(String nos) {
        return purchasePayableBizService.deleteApplyPay(ListUtil.of(nos.split(",")));
    }

    @ResponseBody
    @ApiOperation(value = "手动将付款申请单同步到金蝶")
    @Auth(noAuth = true)
    @GetMapping("/syncApplyPay")
    public Response syncApplyPay(Long id) {
        return purchasePayableBizService.syncApplyPayBill(id);
    }
}
