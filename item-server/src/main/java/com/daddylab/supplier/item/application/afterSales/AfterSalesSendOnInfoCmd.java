package com.daddylab.supplier.item.application.afterSales;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022年10月17日 11:17 AM
 */
@ApiModel("登记异常件转寄信息封装")
public class AfterSalesSendOnInfoCmd extends Command {

    private static final long serialVersionUID = -8884108237989111820L;
    @ApiModelProperty("物流编号")
    private String logisticsNo;

    @ApiModelProperty("物流名称")
    private String logisticsName;

    @ApiModelProperty("商品名称")
    private String itemName;

    @ApiModelProperty("重量")
    private String weight;

    @ApiModelProperty("图片数量")
    private List<String> imageUrls;

    @ApiModelProperty("备注")
    private String remark;

}
