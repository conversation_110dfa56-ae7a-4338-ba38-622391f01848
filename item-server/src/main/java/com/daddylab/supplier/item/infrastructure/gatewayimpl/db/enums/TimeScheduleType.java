package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/4/1
 */
@Getter
@AllArgsConstructor
public enum TimeScheduleType implements IIntegerEnum {
    PLATFORM_INVENTORY_UPLOAD(1, "平台库存上传"),
    WDT_STOCK_SPEC_RT_APPLY(2, "旺店通实时库存APPLY"),
    ;
    private final Integer value;
    private final String desc;
}
