package com.daddylab.supplier.item.domain.fileStore.vo;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ImageStub extends FileStub {
    /**
     * 宽度
     */
    Integer width;

    /**
     * 高度
     */
    Integer height;

    public static ImageStubBuilder<?, ?> builder(String url) {
        return new ImageStubBuilderImpl().url(url);
    }
}
