//package com.daddylab.supplier.item.application.provider.event;
//
//import cn.hutool.extra.spring.SpringUtil;
//import com.daddylab.supplier.item.application.common.event.KingDeeEvent;
//import com.daddylab.supplier.item.common.ExceptionPlusFactory;
//import com.daddylab.supplier.item.infrastructure.config.KingDeeConfig;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;
//import com.daddylab.supplier.item.infrastructure.kingdee.dto.ProviderApiReq;
//import lombok.Builder;
//import lombok.Getter;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @date 2021/11/3 11:53 上午
// * @description
// */
//@Getter
//@Builder
//public class ProviderKingDeeEvent {
//
//    private Provider provider;
//
//    private KingDeeEvent.CRUD crud;
//
//    public ProviderApiReq buildReq() {
//
//        if (KingDeeEvent.CRUD.UPDATE.equals(crud)) {
//            return ProviderApiReq.getUpdateReq(provider, kingDeeConfig.getCreateOrgId(), kingDeeConfig.getUseOrgId());
//        }
//        if (KingDeeEvent.CRUD.ADD.equals(crud)) {
//            return ProviderApiReq.getAddReq(provider, kingDeeConfig.getCreateOrgId(), kingDeeConfig.getUseOrgId());
//        }
//        if (KingDeeEvent.CRUD.DEL.equals(crud)) {
//            return ProviderApiReq.getDelReq(provider, kingDeeConfig.getCreateOrgId(), kingDeeConfig.getUseOrgId());
//        }
//        throw ExceptionPlusFactory.bizException("供应商事件构造，操作类型非法");
//    }
//
//}
