package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 平台商品预警类型
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@AllArgsConstructor
public enum PlatformItemWarnType implements IEnum<Integer> {
    /**
     * 未匹配到后端SKU
     */
    SKU_NOT_MATCH(1, "未匹配到后端SKU"),
    /**
     * 平台库存低于或等于安全库存
     */
    STOCK_ALERT(2, "平台库存低于或等于安全库存"),
    ;
    @EnumValue
    private final Integer value;
    private final String desc;

}
