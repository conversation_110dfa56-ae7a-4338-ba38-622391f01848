package com.daddylab.supplier.item.controller.purchase;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.purchase.combinationPrice.CombinationPriceBizService;
import com.daddylab.supplier.item.application.purchase.combinationPrice.PurchaseStepPricePageQuery;
import com.daddylab.supplier.item.application.purchase.combinationPrice.sheet.CombinationStepPriceExcelVo;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CombinationStepPriceExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> up
 * @date 2023年10月08日 2:03 PM
 */
@Slf4j
@Api(value = "采购阶梯价格相关API", tags = "采购阶梯价格相关API")
@RestController
@RequestMapping("/purchaseStepPrice")
public class PurchaseStepPriceController {

  @Resource CombinationPriceBizService combinationPriceBizService;

  @ResponseBody
  @ApiOperation(value = "搜索查看采购阶梯价列表")
  @PostMapping("/pageQuery")
  public PageResponse<CombinationStepPriceExcelVo> viewList(
      @RequestBody PurchaseStepPricePageQuery purchaseQueryPage) {
    //    purchaseQueryPage.setBusinessLine(
    //        UserPermissionJudge.filterBusinessLinePerm(purchaseQueryPage.getBusinessLine()));
    return combinationPriceBizService.pageQuery2(purchaseQueryPage);
  }

  @ResponseBody
  @ApiOperation(value = "保存采购阶梯价")
  @PostMapping("/save")
  public Response save(@RequestBody @Validated CombinationStepPriceExcel excel) {
    return combinationPriceBizService.save(excel);
  }

  @ApiOperation(value = "删除采购阶梯价格")
  @GetMapping("/delete")
  public Response delete(@RequestParam(value = "id") Long id) {
    return combinationPriceBizService.delete(id);
  }

  @ResponseBody
  @ApiOperation(value = "导入采购阶梯价文件")
  @PostMapping("/importExcel")
  public Response importExcel(
      @ApiParam(name = "file", value = "文件", required = true) @RequestParam("file")
          MultipartFile file
      //      @ApiParam(
      //              name = "priceType",
      //              value = "1活动SKU多件供价。2活动SPU多件供价。3日常SKU多件供价。4日常SPU多件供价。",
      //              required = true)
      //          @RequestParam("priceType")
      //          PriceType priceType
      ) {
    return combinationPriceBizService.importExcel2(file);
  }

  @ResponseBody
  @ApiOperation(value = "导出采购阶梯价文件")
  @PostMapping("/exportExcel")
  public Response exportExcel(@RequestBody PurchaseStepPricePageQuery query) {
    return combinationPriceBizService.export2(query);
  }

  @ResponseBody
  @ApiOperation(value = "历史数据处理")
  @PostMapping("/historyData")
  @Auth(noAuth = true)
  public Response exportExcel() {
    combinationPriceBizService.historyDataHandler();
    return Response.buildSuccess();
  }
}
