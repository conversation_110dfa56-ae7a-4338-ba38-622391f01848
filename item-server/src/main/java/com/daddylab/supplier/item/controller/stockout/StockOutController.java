package com.daddylab.supplier.item.controller.stockout;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuVO;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.application.stockOutOrder.StockOutOrderBizService;
import com.daddylab.supplier.item.controller.stockout.dto.*;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderQuery;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockOutOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockOutOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/23 17:07
 * @description 退料出库
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/stockOutOrder")
@Api(value = "退料出库相关API", tags = "退料出库相关API")
public class StockOutController {

    private final StockOutOrderBizService stockOutOrderBizService;

    private final OperateLogBizService operateLogBizService;

    @ApiOperation(value = "出库单列表")
    @PostMapping(value = "/queryStockOutOrder")
    public PageResponse<StockOutOrderVO> queryStockOutOrder(@RequestBody StockOutOrderQuery stockOutOrderQuery) {
        stockOutOrderQuery.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(stockOutOrderQuery.getBusinessLine()));
        return stockOutOrderBizService.pageStockOutOrder(stockOutOrderQuery);
    }

    @ApiOperation(value = "新增出库单")
    @PostMapping(value = "/saveStockOutOrder")
    public Response saveStockOutOrder(@RequestBody @Validated StockOutOrderCmd stockOutOrderCmd) {
        return stockOutOrderBizService.createOrUpdateStockOutOrder(stockOutOrderCmd);
    }

    @ApiOperation(value = "删除出库单")
    @GetMapping(value = "/deleteStockOutOrder")
    public Response deleteStockOutOrder(@ApiParam("出库单id") Long stockOutOrderId) {
        return stockOutOrderBizService.deleteStockOutOrder(stockOutOrderId);
    }

    @ApiOperation(value = "出库单详情")
    @GetMapping(value = "/getStockOutOrder")
    public SingleResponse<StockOutOrderViewVO> getStockOutOrder(@RequestParam @ApiParam("出库单id") Long stockOutOrderId) {
        return stockOutOrderBizService.getStockOutOrder(stockOutOrderId);
    }

    @ApiOperation(value = "取消出库单")
    @PostMapping(value = "/cancel")
    public Response cancelStockInOrder(@Validated @RequestBody StockOutOrderIdDTO stockOutOrderIdDTO) {
        return stockOutOrderBizService.cancelStockOutOrder(stockOutOrderIdDTO);
    }

    @ApiOperation(value = "撤回出库单")
    @PostMapping(value = "/revocation")
    public Response revocationStockOutOrder(@Validated @RequestBody StockOutOrderIdDTO stockOutOrderIdDTO) {
        return stockOutOrderBizService.revocationStockOutOrder(stockOutOrderIdDTO);
    }

    @ApiOperation(value = "导出")
    @PostMapping(value = "/export")
    public Response exportExcel(@RequestBody StockOutOrderQuery stockOutOrderQuery) {
        stockOutOrderQuery.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(stockOutOrderQuery.getBusinessLine()));
        return stockOutOrderBizService.exportExcel(stockOutOrderQuery);
    }

    @ApiOperation(value = "操作记录")
    @GetMapping(value = "/getLog")
    public MultiResponse<OperateLog> getOperateLogs(@RequestParam @ApiParam("出库单id") Long targetId) {
        return operateLogBizService.getOperateLogs(OperateLogTarget.STOCK_OUT_ORDER, targetId);
    }

    @ApiOperation(value = "手动出库")
    @GetMapping(value = "/manual")
    public Response manualStockOutOrder(@RequestParam @ApiParam("出库单id") Long stockOutOrderId) {
        return stockOutOrderBizService.manualStockOutOrder(stockOutOrderId);
    }

    @ResponseBody
    @ApiOperation(value = "商品库存列表")
    @PostMapping("/skuPage")
    public PageResponse<ComposeSkuVO> skuPage(@RequestBody ComposeSkuPageQuery query) {
        return stockOutOrderBizService.pageSku(query);
    }

    @ResponseBody
    @ApiOperation(value = "sku剩余可出库库存")
    @PostMapping("/remindStockOutNum")
    public MultiResponse<RemindOutQuantityVO> remindStockOutNum(@RequestBody @Validated StockOutQuantityCmd cmd) {
        List<RemindOutQuantityVO> remindOutQuantityVoList = stockOutOrderBizService.queryRemindStockOutQuantity(cmd);
        return MultiResponse.of(remindOutQuantityVoList);
    }

    @ResponseBody
    @ApiOperation(value = "待办列表")
    @PostMapping("/getTodoList")
    public MultiResponse<Long> getTodoList() {
        return stockOutOrderBizService.getTodoList();
    }


    @GetMapping("/syncWdt")
    @Auth(noAuth = true)
    @ApiOperation(value = "出库单同步WDT脚本接口")
    public Response syncWdt(Long stockOutOrderId) {
        final IStockOutOrderService iStockOutOrderService = SpringUtil.getBean(IStockOutOrderService.class);
        final StockOutOrder stockOutOrder = iStockOutOrderService.getById(stockOutOrderId);
        stockOutOrderBizService.stockOutOrderSyncWDT(stockOutOrder);
        return Response.buildSuccess();
    }
}

