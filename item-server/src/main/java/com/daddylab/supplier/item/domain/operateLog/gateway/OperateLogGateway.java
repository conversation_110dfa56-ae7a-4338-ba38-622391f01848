package com.daddylab.supplier.item.domain.operateLog.gateway;

import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.infrastructure.common.LoginType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.types.operateLog.TypedTargetId;

import java.util.Collection;
import java.util.List;

public interface OperateLogGateway {
    boolean batchAddOperateLog(List<OperateLog> operateLogs);

    void addOperatorLog(LoginType loginType, Long operatorId, OperateLogTarget target, Object targetId, String msg, Object data);
    void addOperatorLog(Long operatorId, OperateLogTarget target, Object targetId, String msg, Object data);
    void addOperatorLog(Long operatorId, OperateLogTarget target, Object targetId, List<String> msgs, Object data);

    void addOperatorLog(Long operatorId, OperateLogTarget target, List<Object> targetIds, String msg, Object data);

    List<OperateLog> getOperateLogs(OperateLogTarget target, Object targetId);
    List<OperateLog> getOperateLogs(List<OperateLogTarget> target, Object targetId);
    List<OperateLog> getOperateLogs(OperateLogTarget target, Collection<Object> targetIds);

    List<OperateLog> getMultiTargetOperateLogs(List<TypedTargetId> targetIds);

}
