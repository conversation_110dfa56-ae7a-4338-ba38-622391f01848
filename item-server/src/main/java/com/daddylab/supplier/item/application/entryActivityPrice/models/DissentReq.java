package com.daddylab.supplier.item.application.entryActivityPrice.models;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/10/4
 */
@Data
public class DissentReq {
    @NotBlank
    private String token;

    @ApiModelProperty(value = "异议ID")
    private Long id;

    @ApiModelProperty(value = "存在异议的商品", required = true)
    @NotNull
    private Long priceItemId;

    @ApiModelProperty(value = "异议说明", required = true)
    @NotBlank
    private String dissentNote;
}
