package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.daddylab.supplier.item.domain.itemStock.enums.StockChangeType;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品库存变动记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemStockChangeLog extends Entity {
    /**
     * 商品库存ID
     */
    private Long skuId;

    /**
     * 变动类型 -1:出库 0:覆盖 1:入库
     */
    private StockChangeType type;

    /**
     * 变动数量（入库为正数，出库为负数）
     */
    @TableField("`change`")
    private Integer change;

    /**
     * 变动前库存
     */
    @TableField("`before`")
    private Integer before;

    /**
     * 变动后库存
     */
    @TableField("`after`")
    private Integer after;
}
