package com.daddylab.supplier.item.application.aws.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 审批流状态
 */
@Getter
@AllArgsConstructor
public enum AwsControlStateEnum implements IEnum<Integer> {

    //运行中
    ACTIVE (0,"active"),

    //流程正常结束（审核通过）
    END (1,"end"),

//    //终止 （审核拒绝）
//    TERMINATE (-1,"terminate"),

    ;
    private final Integer value;
    private final String desc;


    public static AwsControlStateEnum getByDesc(String desc){
        for (AwsControlStateEnum awsControlStateEnum: values()) {
            if(Objects.equals(awsControlStateEnum.getDesc(), desc)){
                return awsControlStateEnum;
            }
        }
        return null;
    }
}
