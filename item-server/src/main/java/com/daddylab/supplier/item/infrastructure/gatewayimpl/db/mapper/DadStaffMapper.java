package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DadStaff;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import feign.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 员工表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
@DS("authDb")
@Repository
public interface DadStaffMapper extends DaddyBaseMapper<DadStaff> {

    String queryNickNameByLoginName(@Param("loginName") String loginName);

}
