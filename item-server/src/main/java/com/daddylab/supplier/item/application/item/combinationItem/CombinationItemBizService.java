package com.daddylab.supplier.item.application.item.combinationItem;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.CombinationItemSaveCmd;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.ComposeSkuCmd;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.ProportionCmd;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.ResetProportionBO;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.CombinationItemNamePageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.CombinationItemPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/7 10:00 上午
 * @description
 */
public interface CombinationItemBizService {

    /**
     * 查询sku明细
     *
     * @param query
     * @return
     */
    PageResponse<ComposeSkuVO> pageSku(ComposeSkuPageQuery query);

    /**
     * 保存组合商品
     *
     * @param cmd
     * @return
     */
    SingleResponse<Boolean> save(CombinationItemSaveCmd cmd);


    /**
     * 组合商品详情
     *
     * @param id
     * @return
     */
    SingleResponse<CombinationItemWithPriceVO> get(Long id, Boolean isEdit);

    SingleResponse<Boolean> delete(Long id);

    SingleResponse<CombinationItemPriceVO> getPrice(Long id);

    /**
     * 组合商品分页查询
     *
     * @param query
     * @return
     */
    PageResponse<CombinationPageVO> pageItem(CombinationItemPageQuery query);

    PageResponse<CombinationItemNameVO> pageItemName(CombinationItemNamePageQuery pageQuery);

    void export(CombinationItemPageQuery query);

    SingleResponse<Map<String, BigDecimal>> calculatePrice(List<ComposeSkuCmd> list);

    /**
     * 计算单品的销售金额占比和成本占比接口。
     *
     * @param cmd 这里入参是一个组合装下，所有构成单品的集合。
     *            需要在页面智能填充显示，所以接口里不存在IO操作，只有内存计算，
     */
    List<ProportionVO> calculateProportion(List<ProportionCmd> cmd);

    /**
     * 当后端商品发生价格变动时，检查此sku是否是构成单品，如果是组合装的构成单品，需要重新计算金额占比
     *
     * @param resetProportionBOList
     */
    void resetProportion(List<ResetProportionBO> resetProportionBOList);

    Map<String,List<String>> getRepeatCode();

    Integer getBusinessLine(Long id);

}
