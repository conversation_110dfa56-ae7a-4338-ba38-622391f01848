package com.daddylab.supplier.item.application.afterSalesRegister;


import cn.hutool.core.date.DateUtil;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesRegister;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023/8/22
 */
@Mapper
public interface AfterSalesRegisterConverter {
    AfterSalesRegisterConverter SHARE = Mappers.getMapper(AfterSalesRegisterConverter.class);

    @Mapping(target = "settleTimeStart", ignore = true)
    @Mapping(target = "settleTimeEnd", ignore = true)
    AfterSalesRegisterPageVO po2pageVo(AfterSalesRegister po);

    default String localDateTime2string(LocalDateTime localDateTime) {
        return DateUtil.formatLocalDateTime(localDateTime);
    }
}
