package com.daddylab.supplier.item.application.platformItem.query;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PlatformItemQuery extends PageQuery {

    @ApiModelProperty("外部平台商品ID")
    private String outerItemId;

    @ApiModelProperty("店铺编码")
    private String shopNo;

    @ApiModelProperty("商品ID")
    private Long itemId;

}
