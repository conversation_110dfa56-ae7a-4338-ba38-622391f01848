package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtPreStockInOrderDetails;

/**
 * <p>
 * 旺店通预入库单明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
public interface IWdtPreStockInOrderDetailsService extends IDaddyService<WdtPreStockInOrderDetails> {

}
