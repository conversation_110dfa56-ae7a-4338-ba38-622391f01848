package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ReqData;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ReviewAddHitNumParam;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ReviewThesaurus;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;

import feign.hystrix.FallbackFactory;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/12/15
 */
@Slf4j
@Component
public class PartnerReviewThesaurusAPIFallbackFactory
        implements FallbackFactory<PartnerReviewThesaurusAPI> {

    @Override
    public PartnerReviewThesaurusAPI create(Throwable cause) {
        return new PartnerReviewThesaurusAPI() {
            @Override
            public Rsp<List<ReviewThesaurus>> reviewThesaurusAll() {
                final Rsp<List<ReviewThesaurus>> rsp = new Rsp<>();
                rsp.setCode(1);
                rsp.setFlag(false);
                rsp.setMsg("请求P系统审查词库异常");
                rsp.setData(null);
                return rsp;
            }

            @Override
            public Rsp<List<ReviewThesaurus>> reviewThesaurusAll(String itemNo) {
                return reviewThesaurusAll();
            }

            @Override
            public Rsp<Void> reviewAddHitNum(ReqData<List<ReviewAddHitNumParam>> params) {
                final Rsp<Void> rsp = new Rsp<>();
                rsp.setCode(1);
                rsp.setFlag(false);
                rsp.setMsg("请求增加P系统审查词库命中次数异常");
                rsp.setData(null);
                return rsp;
            }
        };
    }
}
