package com.daddylab.supplier.item.application.auth;

import cn.dev33.satoken.config.SaTokenConfig;
import cn.dev33.satoken.stp.StpLogic;
import com.daddylab.supplier.item.infrastructure.common.LoginType;

/**
 * <AUTHOR>
 * @since 2024/5/29
 */
public class ExternalUserLoginLogic extends StpLogic {
    private final SaTokenConfig config;

    /**
     * 初始化StpLogic, 并指定账号类型
     *
     * @param loginType 账号体系标识
     */
    public ExternalUserLoginLogic(SaTokenConfig config) {
        super(LoginType.EXTERNAL_USER.getValue());
        this.config = config;
    }

    @Override
    public SaTokenConfig getConfig() {
        return config;
    }
}
