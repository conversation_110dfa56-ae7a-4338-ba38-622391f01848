package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.MessageRobot;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.MessageRobotMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IMessageRobotService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 消息机器人维护表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
@Service
public class MessageRobotServiceImpl extends DaddyServiceImpl<MessageRobotMapper, MessageRobot> implements IMessageRobotService {

}
