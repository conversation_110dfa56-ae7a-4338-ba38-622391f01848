package com.daddylab.supplier.item.application.refundPreStockInOrder;

import cn.hutool.core.collection.CollectionUtil;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.PreStockinAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.PreStockinSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.PreStockinSearchResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.PreStockinSearchResponse.Order.Detail;
import com.daddylab.supplier.item.common.trans.TimeCompatibilityTransMapper;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtPreStockInOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtPreStockInOrderDetails;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.google.common.collect.Lists;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/8/18
 */
@Component
@Slf4j
public class WdtRefundPreStockInOrderFetcher implements PageFetcher {

    private final WdtGateway wdtGateway;
    private final WdtRefundPreStockInOrderRepository wdtRefundPreStockInOrderRepository;
    private final WdtConfig wdtConfig;


    public WdtRefundPreStockInOrderFetcher(WdtGateway wdtGateway,
            WdtRefundPreStockInOrderRepository wdtRefundPreStockInOrderRepository,
            WdtConfig wdtConfig) {
        this.wdtGateway = wdtGateway;
        this.wdtRefundPreStockInOrderRepository = wdtRefundPreStockInOrderRepository;
        this.wdtConfig = wdtConfig;
    }

    @Override
    public int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
        return query(startTime, endTime, 1, 1, true).getTotalCount();
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, long pageIndex, long pageSize,
            RunContext runContext) {
        final PreStockinSearchResponse response = query(startTime, endTime,
                ((int) pageSize),
                ((int) pageIndex), false);
        handleResponse(response);
    }


    private PreStockinSearchResponse query(
            LocalDateTime startTime, LocalDateTime endTime, int pageSize, int pageNo,
            boolean calcTotal) {
        final PreStockinSearchResponse response = new PreStockinSearchResponse();
        AtomicInteger totalCount = new AtomicInteger();
        List<PreStockinSearchResponse.Order> orders = Collections.synchronizedList(
                Lists.newArrayList());
        CompletableFuture<?>[] futures = new CompletableFuture[wdtConfig.getConfigs().size()];
        for (int i = 0; i < wdtConfig.getConfigs().size(); i++) {
            int configIdx = i;
            futures[i] = CompletableFuture.runAsync(() -> {
                try {
                    final Pager pager = new Pager(pageSize, pageNo - 1, calcTotal);
                    final PreStockinAPI api = wdtGateway.getQimenAPI(PreStockinAPI.class, configIdx,
                            true);
                    final PreStockinSearchParams params = new PreStockinSearchParams();
                    params.setMtFrom(DateUtil.format(startTime));
                    params.setMtTo(DateUtil.format(endTime));
                    final PreStockinSearchResponse responseThisCall = api.search(params, pager);
                    if (CollectionUtil.isEmpty(responseThisCall.getOrder())) {
                        return;
                    }
                    totalCount.addAndGet(responseThisCall.getTotalCount());
                    orders.addAll(responseThisCall.getOrder());
                } catch (Throwable e) {
                    throw WdtExceptions.wrapFetchException(e, fetchDataType());
                }
            });
        }
        CompletableFuture.allOf(futures).join();
        response.setOrder(orders);
        response.setTotalCount(totalCount.intValue());
        return response;
    }

    private void handleResponse(PreStockinSearchResponse response) {
        if (CollectionUtil.isEmpty(response.getOrder())) {
            return;
        }

        final List<WdtPreStockInOrder> wdtPreStockInOrders = response.getOrder().stream()
                .map(Assembler.INST::orderToPo).collect(Collectors.toList());

        for (WdtPreStockInOrder wdtPreStockInOrder : wdtPreStockInOrders) {
            wdtRefundPreStockInOrderRepository.saveOrUpdateWdtPreStockInOrderWithDetails(
                    wdtPreStockInOrder);
        }
    }


    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.WDT_PRE_STOCK_IN_ORDER;
    }

    /**
     * <AUTHOR>
     * @since 2022/4/18
     */
    @Mapper(uses = {TimeCompatibilityTransMapper.class})
    public interface Assembler {

        Assembler INST = Mappers.getMapper(Assembler.class);

        @Mapping(target = "id", ignore = true)
        @Mapping(target = "detailList", expression = "java(detailListToWdtPreStockInOrderDetailsList(order.getDetailList(), order.getStockinNo()))")
        WdtPreStockInOrder orderToPo(PreStockinSearchResponse.Order order);

        default List<WdtPreStockInOrderDetails> detailListToWdtPreStockInOrderDetailsList(
                List<Detail> list, String stockinNo) {
            if (list == null) {
                return null;
            }

            List<WdtPreStockInOrderDetails> list1 = new ArrayList<WdtPreStockInOrderDetails>(
                    list.size());
            for (Detail detail : list) {
                list1.add(orderDetailsToPo(detail, stockinNo));
            }

            return list1;
        }

        @Mapping(target = "id", ignore = true)
        WdtPreStockInOrderDetails orderDetailsToPo(
                PreStockinSearchResponse.Order.Detail orderDetail, String stockinNo);


    }
}
