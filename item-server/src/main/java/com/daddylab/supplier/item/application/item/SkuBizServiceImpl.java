//package com.daddylab.supplier.item.application.item;
//
//import com.alibaba.cola.dto.PageResponse;
//import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
//import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuVO;
//import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuWithPriceVO;
//import com.daddylab.supplier.item.common.trans.SkuTransMapper;
//import com.daddylab.supplier.item.controller.item.dto.ItemAttrDto;
//import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ComposeSkuDO;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuAttrRefDO;
//import lombok.extern.slf4j.Slf4j;
//import org.assertj.core.util.Lists;
//import org.springframework.stereotype.Service;
//
//import java.util.LinkedList;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR> up
// * @date 2022/3/28 1:48 下午
// */
//@Service
//@Slf4j
//public class SkuBizServiceImpl implements SkuBizService{
//
//    @Override
//    public PageResponse<ComposeSkuVO> pageSku(ComposeSkuPageQuery query) {
//        log.info("combinationItemBizService pageSku param:{}", query);
//        final List<ComposeSkuDO> skuCombinationDos = iCombinationItemService.listSkuDetail(query);
//
//        final List<Long> skuIdList = skuCombinationDos.stream().map(ComposeSkuDO::getSkuId).collect(Collectors.toList());
//        Map<Long, List<SkuAttrRefDO>> attrMap = getSkuAttrRefMap(skuIdList);
//
//        List<ComposeSkuVO> voList = new LinkedList<>();
//        skuCombinationDos.forEach(skuCombinationDO -> {
//            final Long skuId = skuCombinationDO.getSkuId();
//            final List<SkuAttrRefDO> orDefault = attrMap.getOrDefault(skuId, Lists.emptyList());
//            final List<ItemAttrDto> itemAttrDtoList = SkuTransMapper.INSTANCE.toAttrDtos(orDefault);
//
//            if (UserPermissionJudge.canViewComposerSkuPrice()) {
//                final ComposeSkuWithPriceVO skuListVO = SkuTransMapper.INSTANCE.toVoWithPrice(skuCombinationDO);
//                skuListVO.setSpecifications(itemAttrDtoList);
//                voList.add(skuListVO);
//            } else {
//                final ComposeSkuVO skuListBaseVO = SkuTransMapper.INSTANCE.toVoNoPrice(skuCombinationDO);
//                skuListBaseVO.setSpecifications(itemAttrDtoList);
//                voList.add(skuListBaseVO);
//            }
//        });
//
//        final Integer count = iCombinationItemService.countSkuDetail(query);
//        return PageResponse.of(voList, count, query.getPageSize(), query.getPageIndex());
//    }
//}
