package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemStock;

/**
 * <p>
 * 商品库存 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
public interface IItemStockService extends IDaddyService<ItemStock> {

    Long getSkuStockNum(String skuCode);

}
