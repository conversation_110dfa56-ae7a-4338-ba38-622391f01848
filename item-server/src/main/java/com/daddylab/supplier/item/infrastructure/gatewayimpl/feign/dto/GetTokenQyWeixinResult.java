package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Author: <PERSON>
 * @Date: 2022/6/1 15:33
 * @Description: 获取企微 token 结果封装
 */
@Data
public class GetTokenQyWeixinResult extends BaseQyWeixinResult {
    @JsonProperty("access_token")
    private String accessToken;
    @JsonProperty("expires_in")
    private Long expiresIn;
}
