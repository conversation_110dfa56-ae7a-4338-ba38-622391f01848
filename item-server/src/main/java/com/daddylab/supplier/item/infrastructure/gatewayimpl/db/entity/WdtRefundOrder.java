package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 旺店通退换单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtRefundOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    List<WdtRefundOrderDetail> details;

    @TableField(exist = false)
    List<WdtRefundOrderAmountDetail> amountDetails;

    @TableField(exist = false)
    WdtSwapOrder swapOrder;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 退换单id
     */
    private Integer refundId;
    /**
     * 原始单号
     */
    private String tidList;
    /**
     * 退换单号
     */
    private String refundNo;
    /**
     * 备注
     */
    private String remark;
    /**
     * 1:退款;2:退货;3:换货;4:退款不退货
     */
    private Integer type;
    /**
     * 0:未入库;1:待入库;2:部分入库;3:全部入库;4:终止入库
     */
    private Integer stockinStatus;
    /**
     * 标记名称
     */
    private String flagName;
    /**
     * 退回货品数量
     */
    private Integer returnGoodsCount;
    /**
     * 退款订单中收件人电话
     */
    private String receiverTelno;
    /**
     * 退款订单中收件人姓名
     */
    private String receiverName;
    /**
     * 修改时间
     */
    private LocalDateTime modified;
    /**
     * 便签数量
     */
    private Integer noteCount;
    /**
     * 店铺编号
     */
    private String shopNo;
    /**
     * 0:API抓单;1:手工建单;2:导入
     */
    private Integer fromType;
    /**
     * 创建时间
     */
    private LocalDateTime created;
    /**
     * 使用结算时间查询返回结算时间, 否则返回空字符串
     */
    private LocalDateTime settleTime;
    /**
     * 审核时间
     */
    private LocalDateTime checkTime;
    /**
     * 退货物流单号
     */
    private String returnLogisticsNo;
    /**
     * 系统订单号列表
     */
    private String tradeNoList;
    /**
     * 担保退款金额
     */
    private BigDecimal guaranteeRefundAmount;
    /**
     * 退回货品金额
     */
    private BigDecimal returnGoodsAmount;
    /**
     * 物流公司名称
     */
    private String returnLogisticsName;
    /**
     * 退换说明
     */
    private String reasonName;
    /**
     * 退款原因
     */
    private String refundReason;
    /**
     * 客户网名
     */
    private String buyerNick;
    /**
     * 制单员姓名
     */
    private String operatorName;
    /**
     * 实际退款金额
     */
    private BigDecimal actualRefundAmount;
    /**
     * 驳回原因
     */
    private String revertReasonName;
    /**
     * 退回仓库编号
     */
    private String returnWarehouseNo;
    /**
     * 非担保退款金额
     */
    private BigDecimal directRefundAmount;
    /**
     * 收款金额，买家支付给卖家
     */
    private BigDecimal receiveAmount;
    /**
     * 客户姓名
     */
    private String customerName;
    /**
     * 分销商昵称，若无值则字段不返回
     */
    private String fenxiaoNickName;
    /**
     * 10:已取消;20:待审核;30:已审核;40已推送:80:已结算;85:待过账;86:已过账;87:成本确认;90:已完成
     */
    private Integer status;
    /**
     * 店铺id
     */
    private Integer shopId;
    /**
     * 订单id
     */
    private Integer tradeId;
    /**
     * 删除时间
     */
    private Long deletedAt;
    /**
     * 平台id，见附件
     */
    private Integer platformId;

}
