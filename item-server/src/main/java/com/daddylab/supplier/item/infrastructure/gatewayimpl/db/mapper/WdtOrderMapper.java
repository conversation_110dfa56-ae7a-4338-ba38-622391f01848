package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.purchase.order.factory.dto.SkuTradeBO;
import com.daddylab.supplier.item.controller.test.dto.WdtOrderLogisticDto;
import com.daddylab.supplier.item.domain.order.OrderSensitiveInfo;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.OrderWarehouseVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.WdtOrderDetailDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrder;
import com.daddylab.supplier.item.types.order.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 旺店通订单数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Repository
public interface WdtOrderMapper extends DaddyBaseMapper<WdtOrder> {

    /**
     * 查询单个商品wdt订单详情
     *
     * @param startDt 订单发货开始时间
     * @param endDt   订单发货结束时间
     * @param offset  分页偏移量
     * @param size    分页大小
     * @return
     */
    List<WdtOrderDetailDO> purchaseOrderList(@Param("startDt") String startDt,
                                             @Param("endDt") String endDt,
                                             @Param("eliminateWarehouseNos") List<String> eliminateWarehouseNos,
                                             @Param("offset") Integer offset,
                                             @Param("size") Integer size);

    Integer purchaseOrderCount(@Param("startDt") String startDt,
                               @Param("endDt") String endDt,
                               @Param("eliminateWarehouseNos") List<String> eliminateWarehouseNos);


    List<WdtOrderDetailDO> wmsStockOutDetailList(@Param("skuCode") String skuCode,
                                                 @Param("startDt") String startDt,
                                                 @Param("endDt") String endDt,
                                                 @Param("eliminateWarehouseNos") List<String> eliminateWarehouseNos,
                                                 @Param("externalShopNos") List<String> externalShopNos,
                                                 @Param("tradeNo") String tradeNo);

    @Deprecated
    List<WdtOrderDetailDO> wmsStockOutDetailList2(@Param("skuCode") String skuCode, @Param("startDt") String startDt,
                                                  @Param("endDt") String endDt, @Param("eliminateWarehouseNos") List<String> eliminateWarehouseNos);

    List<SkuTradeBO> wmsStockOutTradeDetailList(@Param("tradeNoList") List<String> tradeNoList
            , @Param("eliminateWarehouseNos") List<String> eliminateWarehouseNos, @Param("specNo") String specNo);


    WdtOrderDetailDO getByDetailDbId(@Param("id") Long id);

    List<WdtOrderDetailDO> orderSingleDetailListByDbIds(@Param("idList") List<Long> idList);

    List<Long> isSameProvider(@Param("idList") List<Long> idList);

    /**
     * 查询单个商品wdt订单 计数
     *
     * @param startDt 订单发货开始时间
     * @param endDt   订单发货结束时间
     * @return
     */
    Integer orderSingleDetailCount(@Param("startDt") String startDt, @Param("endDt") String endDt
            , @Param("eliminateWarehouseNos") List<String> eliminateWarehouseNos);

    /**
     * 查询组合商品wdt订单详情
     *
     * @param startDt
     * @param endDt
     * @return
     */
    List<Long> orderSuiteDetailList(@Param("startDt") String startDt, @Param("endDt") String endDt
            , @Param("eliminateWarehouseNos") List<String> eliminateWarehouseNos);

    @Select("SELECT id FROM wdt_order_detail WHERE trade_id = #{tradeId} AND (deleted_at = 0 OR deleted_at IS NULL)")
    List<Long> getWodBdIdByTradeId(@Param("tradeId") Long tradeId);

//    /**
//     * 查询组合商品wdt订单 计数
//     *
//     * @param startDt 订单发货开始时间
//     * @param endDt   订单发货结束时间
//     * @return
//     */
//    Integer orderSuiteDetailCount(@Param("startDt") String startDt, @Param("endDt") String endDt);

//    /**
//     * 单品的的退款数量
//     *
//     * @param startDt
//     * @param endDt
//     * @return
//     */
//    @Select("select IFNULL(wod.refund_num, 0) from wdt_order_detail\n" +
//            "wod inner join wdt_order wo on wod.trade_id = wo.trade_id \n" +
//            "where wod.refund_status = 5 and wo.consign_time > #{startDt} \n" +
//            "  and wo.consign_time < #{endDt} and wod.spec_no = #{specNo} ")
//    Integer refundNumWithSingle(@Param("startDt") String startDt, @Param("endDt") String endDt, @Param("specNo") String specNo);
//
//    /**
//     * 组合商品的的退款数量
//     *
//     * @param startDt
//     * @param endDt
//     * @param suiteNo
//     * @return
//     */
//    @Select("select IFNULL(wod.refund_num, 0) from wdt_order_detail\n" +
//            "wod inner join wdt_order wo on wod.trade_id = wo.trade_id \n" +
//            "where wod.refund_status = 5 and wo.consign_time > #{startDt} \n" +
//            "  and wo.consign_time < #{endDt} and wod.suite_no = #{suiteNo} ")
//    Integer refundNumWithSuite(@Param("startDt") String startDt, @Param("endDt") String endDt, @Param("suiteNo") String suiteNo);


    /**
     * 根据组合商品编码查询子商品
     *
     * @param suiteNo
     * @return
     */
    List<WdtOrderDetailDO> orderDetailBySuiteNo(String suiteNo);

    /**
     * 累计sku数量，根据编号和价格
     *
     * @param operateTime 操作时间。
     * @return
     */
    List<String> getGroupSkuCode(String operateTime);

    /**
     * 订单查询计数（不查询订单明细）
     */
    Long queryOrderCountWithoutDetailsCond(@Param("query") OrderQuery orderQuery);

    /**
     * 订单查询计数（包含对订单明细的查询）
     */
    Long queryOrderCountWithDetailsCond(@Param("query") OrderQuery orderQuery);

    /**
     * 查询满足条件的订单ID（查询订单明细条件）
     */
    List<Long> queryOrderIdsWithDetailsCond(@Param("query") OrderQuery orderQuery);

    /**
     * 查询满足条件的订单ID（不查询订单明细条件）
     */
    List<Long> queryOrderIdsWithoutDetailsCond(@Param("query") OrderQuery orderQuery);

    /**
     * 订单查询（根据ID返回订单基础信息）
     */
    List<OrderBaseInfo> queryOrderByIds(@Param("ids") Collection<Long> ids);

    /**
     * 订单退款商品查询
     *
     * @param orderNo 订单号
     * @deprecated 改为复用refundOrder模块的方法
     */
    @Deprecated
    List<OrderRefundDetail> orderRefundItemsQuery(@Param("orderNo") String orderNo);

    /**
     * 订单详情查询（不带退款商品）
     *
     * @param orderNo 订单号
     */
    OrderDetail orderDetailQuery(@Param("orderNo") String orderNo);

    /**
     * 查询采购单 订单明细
     *
     * @param query
     * @return
     */
    List<PurchaseOrderTabVO> purchaseTabQuery(@Param("query") PurchaseOrderTabQuery query);

    Integer purchaseTabCount(@Param("query") PurchaseOrderTabQuery query);

    /**
     * 批量插入或者已存在重复记录时更新数据
     *
     * @param orders 数据集合
     */
    void saveOrUpdateBatch(@Param("orders") Collection<WdtOrder> orders);

    /**
     * 检查范围内的订单是否已经没有关联明细，没有关联明细认为是已经失效的订单（订单可能发生合单，合单后明细ID不变但是可能被关联到其他的订单下）
     *
     * @param tradeIds 旺店通的订单ID
     */
    int cleanOrders(@Param("tradeIds") Set<Long> tradeIds, @Param("currentTime") Long currentTime);

    /**
     * 查询已经被删除的旺店通订单ID
     *
     * @param tradeIds 旺店通订单ID
     * @return
     */
    List<Long> selectDeletedTradeIdList(@Param("tradeIds") Set<Long> tradeIds);

    List<OrderSensitiveInfo> queryOrderSensitiveInfoBatchBySrcOrderNo(List<String> srcOrderNos);

    /**
     * 根据原始订单号 查询wdt_order表id
     *
     * @param srcTids
     * @return
     */
    List<Long> selectIdBySrcTids(@Param("srcTids") Collection<String> srcTids, @Param("platformId") Integer platformId);

    /**
     * 根据原始订单号查询旺店通订单ID
     *
     * @param srcTid
     * @return
     */
    List<WdtOrder> listBySrcTid(@Param("srcTid") String srcTid);

    List<String> tradeNos(@Param("wdtIds") Collection<Long> wdtIds);

    /**
     * 根据订单号查询发货仓
     *
     * @param orderNos 订单号
     * @return
     */
    List<OrderWarehouseVO> selectOrderWarehouseByOrderNos(Collection<String> orderNos);

    List<OrderDetailPrimaryIdObj> selectOrderDetailPrimaryIdObjs(@Param("tradeIds") Collection<Long> tradeIds);

    int deleteByTradeIds(@Param("tradeIds") Collection<Long> tradeIds);


    List<WdtOrderLogisticDto> queryWdtOrderLogisticSheet(@Param("start") String start, @Param("end") String end);
}
