package com.daddylab.supplier.item.controller.file;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.fileStore.vo.VideoStub;
import com.daddylab.supplier.item.infrastructure.oss.OssGateway;
import com.daddylab.supplier.item.infrastructure.upyun.gateway.UpyunGateway;
import io.swagger.annotations.ApiParam;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RequestMapping("/file")
@RestController
public class FileController {
    @Autowired
    private FileGateway fileGateway;

    @Autowired
    private UpyunGateway upyunGateway;

    @Autowired
    private OssGateway ossGateway;

    @PostMapping(value = "/uploadImage", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public SingleResponse<FileStub> uploadImage(
            @ApiParam(name = "file", value = "图片", required = true) @RequestParam("file")
            MultipartFile file) {
        final FileStub fileStub = fileGateway.uploadImage(UploadFileAction.ofMultipartFile(file));
        return SingleResponse.of(fileStub);
    }

    @PostMapping(value = "/uploadFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public SingleResponse<FileStub> uploadFile(@ApiParam(name = "file", value = "文件", required = true)
                                               @RequestParam("file") MultipartFile file,
                                               @ApiParam(name = "filename", value = "指定文件名", hidden = true)
                                               @RequestParam("filename") String filename) {
        final FileStub fileStub = fileGateway.uploadFile(UploadFileAction.ofMultipartFile(file, filename));
        return SingleResponse.of(fileStub);
    }

    @PostMapping(value = "/uploadVideo", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public SingleResponse<VideoStub> uploadVideo(@ApiParam(name = "file", value = "文件", required = true)
                                                 @RequestParam("file") MultipartFile file,
                                                 @ApiParam(name = "filename", value = "指定文件名", hidden = true)
                                                 @RequestParam("filename") String filename) {
        final VideoStub fileStub = fileGateway.uploadVideo(UploadFileAction.ofMultipartFile(file, filename));
        return SingleResponse.of(fileStub);
    }

    @GetMapping(value = "/getUploadSign")
    public SingleResponse<Map<String, String>> getUploadSign(@RequestParam("path") String path) {
        return SingleResponse.of(upyunGateway.getFormUploadSign(path));
    }

    @PostMapping(value = "/getUploadSign", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public SingleResponse<Map<String, String>> getUploadSignPOST(@RequestParam("path") String path) {
        return SingleResponse.of(upyunGateway.getFormUploadSign(path));
    }

    @GetMapping(value = "/getOssUploadSign")
    public SingleResponse<String> getOssUploadSign(
            @RequestParam("params") Map<String, String> params) {
        return SingleResponse.of(ossGateway.sign(params));
    }

    @GetMapping(value = "/getAuthorizedUrl")
    public SingleResponse<String> getAuthorizedUrl(@RequestParam("url") String url) {
        return SingleResponse.of(fileGateway.getAuthorizedUrl(url));
    }

}
