package com.daddylab.supplier.item.domain.drawer.vo;

import cn.hutool.core.collection.CollectionUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.util.List;
import lombok.Data;

@Data
public class ItemDrawerAttrImages {

    private Long attrId;
    private String attrName;
    private List<ItemDrawerAttrImage> itemAttrs;
    private Boolean imagesAdded;

    public void calcImagesAdded() {
        imagesAdded = !CollectionUtil.isEmpty(itemAttrs) && itemAttrs.stream()
                .anyMatch(v -> StringUtil.isNotBlank(v.getUrl()));
    }

}