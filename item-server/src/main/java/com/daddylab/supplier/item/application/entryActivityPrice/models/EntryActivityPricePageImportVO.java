package com.daddylab.supplier.item.application.entryActivityPrice.models;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/10/6
 */
@Data
public class EntryActivityPricePageImportVO {
    @ExcelProperty("*商品SKU")
    private String skuCode;
    @ExcelProperty("*活动时间")
    private String activeTime;
    @ExcelProperty("*合同销售价")
    private String contractSalePrice;
    @ExcelProperty("*平台佣金")
    private String platformCommission;
    @ExcelProperty("商品编码")
    private String itemCode;
    @ExcelProperty("商品名称")
    private String itemName;
    @ExcelProperty("活动备注")
    private String activityRemark;
    @ExcelProperty("状态")
    private String status;
}
