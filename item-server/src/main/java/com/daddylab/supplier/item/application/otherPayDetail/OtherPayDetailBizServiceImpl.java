package com.daddylab.supplier.item.application.otherPayDetail;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.daddylab.supplier.item.domain.otherPayDetail.enums.PayType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherPayDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOtherPayDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName OtherPayDetailBizServiceImpl.java
 * @description
 * @createTime 2022年03月25日 15:05:00
 */
@Slf4j
@Service
public class OtherPayDetailBizServiceImpl implements OtherPayDetailBizService{

    @Autowired
    private IOtherPayDetailService iOtherPayDetailService;

    @Override
    public void insert(List<OtherPayDetail> otherPayDetails, Long otherPayIds) {
        otherPayDetails.forEach(otherPayDetail -> {
            otherPayDetail.setOtherPayId(otherPayIds);
            iOtherPayDetailService.save(otherPayDetail);
        });
    }

    @Override
    public void update(List<OtherPayDetail> otherPayDetails, Long otherPayId) {
        LambdaQueryWrapper<OtherPayDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OtherPayDetail::getOtherPayId,otherPayId);
        List<OtherPayDetail> list = iOtherPayDetailService.list(queryWrapper);
        List<Long> ids = list.stream().map(OtherPayDetail::getId).collect(Collectors.toList());
        iOtherPayDetailService.removeByIds(ids);

        otherPayDetails.forEach(otherPayDetail -> {
            otherPayDetail.setOtherPayId(otherPayId);
            iOtherPayDetailService.save(otherPayDetail);
        });

    }

    @Override
    public List<OtherPayDetail> getById(Long orderPayId) {
        LambdaQueryWrapper<OtherPayDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OtherPayDetail::getOtherPayId,orderPayId);
        return iOtherPayDetailService.list(queryWrapper);
    }

    @Override
    public OtherPayDetail getTotal(Long orderPayId) {
        LambdaQueryWrapper<OtherPayDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OtherPayDetail::getOtherPayId,orderPayId)
                .eq(OtherPayDetail::getPayType, PayType.TOTAL.getValue());
        return iOtherPayDetailService.getOne(queryWrapper);
    }
}
