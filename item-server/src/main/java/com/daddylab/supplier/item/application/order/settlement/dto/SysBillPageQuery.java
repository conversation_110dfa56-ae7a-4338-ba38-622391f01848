package com.daddylab.supplier.item.application.order.settlement.dto;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OrderSettlementStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年08月08日 5:27 PM
 */
@ApiModel("新增结算页面/系统账单数据请求参数")
@Data
public class SysBillPageQuery extends PageQuery {

    private static final long serialVersionUID = 2088512863625596553L;

    /**
     * 表示多个结算月
     * 1号0点，10位时间戳
     */
    @ApiModelProperty("结算周期")
    private List<Long> times;

    @ApiModelProperty("仓库编码")
    private List<String> warehouseNos;

    @ApiModelProperty("仓库订单员")
    private List<Long> orderPersonnelIds;

    @ApiModelProperty("状态。WAIT_CONFIRM 待结算。CONFIRMED 已结算")
    private OrderSettlementStatus status;

    @ApiModelProperty("已选中的账单ids")
    private List<Long> selectBillIds;

    private Integer statusVal;
    private List<String> queryWarehouseNos;

    @ApiModelProperty("合作模式")
    private List<Integer> businessLine;

}
