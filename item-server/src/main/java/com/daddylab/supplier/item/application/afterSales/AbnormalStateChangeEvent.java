package com.daddylab.supplier.item.application.afterSales;

import lombok.Value;

/**
 * <AUTHOR>
 * @since 2022/11/2
 */
@Value
public class AbnormalStateChangeEvent {

    Long id;

    /**
     * 删除时间
     */
    Long deletedAt;

    /**
     * 创建时间
     */
    Long createdAt;

    /**
     * 创建者
     */
    Long createdUid;

    /**
     * 更新时间
     */
    Long updatedAt;

    /**
     * 更新者
     */
    Long updatedUid;

    /**
     * 商品名称
     */
    String itemName;

    /**
     * 物流名称
     */
    String logisticsName;

    /**
     * 物流单号
     */
    String logisticsNo;

    /**
     * 数量
     */
    Integer quantity;

    /**
     * 状态
     */
    Integer state;

    /**
     * 状态
     */
    Integer prevState;

    /**
     * 备注
     */
    String remark;

    /**
     * 关联的退换单号
     */
    String returnOrderNo;

    /**
     * P系统供应商id
     */
    Long partnerProviderId;

    /**
     * 供应商名称
     */
    String partnerProviderName;

    /**
     * 统一社会信用代码
     */
    String unifySocialCreditCode;

    /**
     * 仓库编号
     */
    String warehouseNo;


}
