package com.daddylab.supplier.item.domain.drawer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.javers.core.metamodel.annotation.PropertyName;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @class SimpleItemVo.java
 * @description 描述类的作用
 * @date 2024-04-08 17:27
 */
@Data
@ApiModel(value = "SimpleItemVo", description = "SimpleItemVo-简单商品信息")
public class SimpleItemVo implements Serializable {

    @ApiModelProperty("商品ID")
    private Long itemId;

    @ApiModelProperty("商品名称")
    private String name;

    @ApiModelProperty("商品图片地址")
    private String image;

    @ApiModelProperty("商品编码")
    private String code;

    @ApiModelProperty("商品状态")
    private Integer status;

    @PropertyName("商品下架时间")
    private Long downFrameTime;

    @PropertyName("商品下架原因")
    private String downFrameReason;
}
