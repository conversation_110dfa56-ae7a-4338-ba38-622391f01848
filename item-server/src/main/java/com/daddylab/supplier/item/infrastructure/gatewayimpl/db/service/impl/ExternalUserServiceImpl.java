package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExternalUser;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ExternalUserMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IExternalUserService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 外部账号管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@Service
public class ExternalUserServiceImpl extends DaddyServiceImpl<ExternalUserMapper, ExternalUser> implements IExternalUserService {

}
