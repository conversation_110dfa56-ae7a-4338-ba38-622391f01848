package com.daddylab.supplier.item.application.platformItemSkuInventory.converter;

import com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params.PlatformSkuSyncInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSkuInventory;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.assembler.CommonAssembler;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @class PlatformItemSkuInventoryConverter.java
 * @description 类型转换类
 * @date 2024-03-14 10:46
 */
@Mapper(uses = {
        CommonAssembler.class,
}, imports = {DateUtil.class})
public interface PlatformItemSkuInventoryConverter {

    PlatformItemSkuInventoryConverter INSTANCE = Mappers.getMapper(PlatformItemSkuInventoryConverter.class);

    @Mappings({
            @Mapping(target = "lastUpdateTime", expression = "java(cn.hutool.core.date.DateUtil.currentSeconds())"),
            @Mapping(target = "type", expression = "java(params.getPlatform().getValue())"),
    })
    PlatformItemSkuInventory paramToInventory(PlatformSkuSyncInfo params);

    List<PlatformItemSkuInventory> paramsToInventories(List<PlatformSkuSyncInfo> params);


    @Mapping(target = "updatedAt", source = "itemUpdateTime")
    @Mapping(target = "createdAt", source = "itemCreateTime")
    @Mapping(target = "modified", expression = "java(DateUtil.toLocalDateTime(params.getItemUpdateTime()))")
    PlatformItem paramToPlatformItem(PlatformSkuSyncInfo params);

    @Mapping(target = "updatedAt", source = "skuCreateTime")
    @Mapping(target = "createdAt", source = "skuCreateTime")
    @Mapping(target = "hidden", source = "hidden")
    PlatformItemSku paramToPlatformItemSku(PlatformSkuSyncInfo params);

    default PlatformItemStatus map(Integer value) {
        if (value == 0) {
            return PlatformItemStatus.SALE_OUT;
        } else if (value == 1) {
            return PlatformItemStatus.ON_SALE;
        } else {
            return PlatformItemStatus.WAIT_SALE;
        }
        //0下架 1上架（在售中）2 待上架（未上架）

//        return value == 0 ? PlatformItemStatus.SALE_OUT : PlatformItemStatus.ON_SALE;
    }
}
