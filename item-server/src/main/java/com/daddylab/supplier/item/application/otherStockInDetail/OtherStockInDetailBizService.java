package com.daddylab.supplier.item.application.otherStockInDetail;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockInDetail;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OtherStockInDetailService.java
 * @description
 * @createTime 2022年03月30日 17:02:00
 */
public interface OtherStockInDetailBizService {

    /**
     * 根据itemId查询订单
     * @param itemId
     * @return
     */
    List<Long> getByItemId(Long itemId);

    /**
     * 根据brandId查询订单
     * @param brandId
     * @return
     */
    List<Long> getByBrandId(Long brandId);

    /**
     * 根据id查详情列表
     * @param id
     * @return
     */
    List<OtherStockInDetail> getByOrderId(Long id);


    /**
     * 查询交集主订单id
     * @param sku
     * @param code
     * @param itemId
     * @param brandId
     * @return
     */
    List<Long> getIdsByItem(Long sku, String code, Long itemId, Long brandId, Collection<Integer> businessLines);

}
