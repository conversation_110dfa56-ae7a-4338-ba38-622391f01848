package com.daddylab.supplier.item.domain.exportTask.dto.item;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/19 5:00 下午
 * @description
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
public class ExportSkuNoPriceDto extends ExportSheet {
    @ExcelIgnore
    private Long id;

    @ExcelProperty("商品sku")
    private String sku;

    @ExcelProperty("商品名称")
    private String name;

    @ExcelProperty("商品品类")
    private String category;

    @ExcelProperty("商品品牌")
    private String brand;

    @ExcelProperty("商品编码")
    private String code;

    @ExcelProperty("供应商")
    private String provider;

    @ExcelProperty("采购员")
    private String buyer;

    @ExcelProperty("发货渠道")
    private String delivery;
}
