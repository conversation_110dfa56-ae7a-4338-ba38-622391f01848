package com.daddylab.supplier.item.application.payment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentApplyOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentApplyOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPaymentApplyOrderDetailService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPaymentApplyOrderService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR> up
 * @date 2023年11月21日 2:04 PM
 */
@Service
public class PaymentApplyDao {

    @Resource
    IPaymentApplyOrderService iPaymentApplyOrderService;

    @Resource
    IPaymentApplyOrderDetailService iPaymentApplyOrderDetailService;

    @Resource
    OperateLogGateway operateLogGateway;

    @Transactional(rollbackFor = Exception.class)
    public Long addNewOne(PaymentApplyOrder order, List<PaymentApplyOrderDetail> detailList) {
        iPaymentApplyOrderService.save(order);
        Long id = order.getId();

        if (CollUtil.isNotEmpty(detailList)) {
            detailList.forEach(val -> val.setPaymentApplyOrderId(id));
            iPaymentApplyOrderDetailService.saveBatch(detailList);
        }

        operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.PAYMENT_APPLY_ORDER, id, "付款申请单新增", null);

        return id;
    }


    @Transactional(rollbackFor = Exception.class)
    public Long updateOne(Long id, PaymentApplyOrder newOne, List<PaymentApplyOrderDetail> newDetails, List<Long> oldDetailIds,
                          String orderLog, String detailLog) {
        if (CollUtil.isNotEmpty(oldDetailIds)) {
            iPaymentApplyOrderDetailService.removeByIds(oldDetailIds);
        }

        iPaymentApplyOrderService.updateById(newOne);

        if (CollUtil.isNotEmpty(newDetails)) {
            newDetails.forEach(val -> {
                val.setPaymentApplyOrderNo(newOne.getNo());
                val.setPaymentApplyOrderId(id);
            });
            iPaymentApplyOrderDetailService.saveBatch(newDetails);
        }
        if (StrUtil.isNotBlank(orderLog)) {
            operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.PAYMENT_APPLY_ORDER, id, orderLog, null);
        }
        if (StrUtil.isNotBlank(detailLog)) {
            operateLogGateway.addOperatorLog(UserContext.getUserId(), OperateLogTarget.PAYMENT_APPLY_ORDER, id, detailLog, null);
        }

        return newOne.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void remove(Long id){
        iPaymentApplyOrderService.removeById(id);
        iPaymentApplyOrderDetailService.lambdaUpdate().eq(PaymentApplyOrderDetail::getPaymentApplyOrderId,id).remove();
    }

}
