package com.daddylab.supplier.item.infrastructure.oa.constants;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 枚举，采购付款 1989795444839369416，预付款 -9094489876982182275
 *
 * <AUTHOR>
 * @since 2023/11/23
 */
@RequiredArgsConstructor
@Getter
public enum PaymentPurpose implements IEnum<Long> {
    PROCUREMENT_PAYMENT(1989795444839369416L, "采购付款"),
    ADVANCE_PAYMENT(-9094489876982182275L, "预付款"),
    ;
    @JsonValue private final Long value;
    private final String desc;
}
