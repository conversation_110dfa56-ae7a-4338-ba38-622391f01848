package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsAbnormalityLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderLogisticsAbnormalityLogMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsAbnormalityLogService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 订单物流异常记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Service
public class OrderLogisticsAbnormalityLogServiceImpl extends DaddyServiceImpl<OrderLogisticsAbnormalityLogMapper, OrderLogisticsAbnormalityLog> implements IOrderLogisticsAbnormalityLogService {

    @Override
    public void newNonExceptionMsgLog(Long abnormalityId, int logType, String msg) {
        final OrderLogisticsAbnormalityLog orderLogisticsAbnormalityLog = new OrderLogisticsAbnormalityLog();
        orderLogisticsAbnormalityLog.setAbnormalityId(abnormalityId);
        orderLogisticsAbnormalityLog.setLogType(logType);
        orderLogisticsAbnormalityLog.setMsg(msg);
        save(orderLogisticsAbnormalityLog);
    }
}
