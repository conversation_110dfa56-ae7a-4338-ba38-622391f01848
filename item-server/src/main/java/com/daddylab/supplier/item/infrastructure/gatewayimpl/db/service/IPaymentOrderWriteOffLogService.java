package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentOrderWriteOffLog;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 付款单冲销流程日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-18
 */
public interface IPaymentOrderWriteOffLogService extends IDaddyService<PaymentOrderWriteOffLog> {

}
