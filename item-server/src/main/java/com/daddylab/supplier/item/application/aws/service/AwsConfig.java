package com.daddylab.supplier.item.application.aws.service;

import java.util.Map;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2022/9/13
 */
@Data
@EnableConfigurationProperties(AwsConfig.class)
@ConfigurationProperties(prefix = "aws")
@Configuration
public class AwsConfig {

    private String accessKey;
    private String secret;
    private String apiServer;
    private String environment;
    private String url;
    private Map<String, Integer> versions;

    public Integer getVersion(String type) {
        if (versions != null) {
            return versions.getOrDefault(type, 0);
        }
        return 0;
    }

}
