package com.daddylab.supplier.item.application.goodsSn;

import com.alibaba.cola.dto.MultiResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtGoodsSnMapper;
import com.daddylab.supplier.item.types.goodsSn.OpenGoodsSnDto;
import com.daddylab.supplier.item.types.goodsSn.OpenGoodsSnQuery;
import java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/2/13
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class OpenGoodsSnServiceImpl implements OpenGoodsSnService {

    private final WdtGoodsSnMapper wdtGoodsSnMapper;

    @Override
    public MultiResponse<OpenGoodsSnDto> query(OpenGoodsSnQuery query) {
        if (query.isEmpty()) {
            return MultiResponse.of(Collections.emptyList());
        }
        return MultiResponse.of(wdtGoodsSnMapper.openGoodsSnQuery(query));
    }
}
