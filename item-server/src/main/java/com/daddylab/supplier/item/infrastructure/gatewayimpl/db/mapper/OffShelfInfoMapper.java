package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.offShelf.dto.OffShelfPageQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OffShelfInfo;

import java.util.List;

/**
 * <p>
 * 下架管理-下架流程信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface OffShelfInfoMapper extends DaddyBaseMapper<OffShelfInfo> {

    List<OffShelfInfo> page(OffShelfPageQuery query);
}
