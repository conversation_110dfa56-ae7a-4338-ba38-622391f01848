package com.daddylab.supplier.item.application.nuonuo.dto;

import com.daddylab.supplier.item.common.domain.dto.Command;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InvoiceTitleType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InvoiceType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年07月26日 10:20 AM
 */
@Data
public class InvoiceNewCmd extends Command {


    private static final long serialVersionUID = -3265225676079276177L;

    @ApiModelProperty("订单编号")
    @Size(min = 1, message = "订单编号不得为空")
    @Size(max = 50, message = "最多可支持50个订单同时开具发票，请分开提交申请！")
    private List<String> orderNoList;

    @ApiModelProperty("发票类型")
    @NotNull(message = "发票类型不得为空")
    private InvoiceType invoiceType;

    @ApiModelProperty("发票抬头类型")
    @NotNull(message = "发票抬头类型不得为空")
    private InvoiceTitleType invoiceTitleType;

    @ApiModelProperty("发票抬头")
    @NotBlank(message = "发票抬头不得为空")
    private String invoiceTitle;

    @ApiModelProperty("税号")
    private String taxCode;

    @ApiModelProperty("开户银行")
    private String bank;

    @ApiModelProperty("开户银行账号")
    private String bankNo;

    @ApiModelProperty("企业地址")
    private String companyAddress;

    @ApiModelProperty("企业电话")
    private String companyPhone;


    @ApiModelProperty("邮箱")
    @NotBlank(message = "邮箱不得为空")
    private String mailAddress;


}
