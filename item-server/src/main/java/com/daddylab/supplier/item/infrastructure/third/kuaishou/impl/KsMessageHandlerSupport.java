package com.daddylab.supplier.item.infrastructure.third.kuaishou.impl;

import cn.hutool.core.util.ReflectUtil;
import com.daddylab.supplier.item.infrastructure.third.annotations.KsHandlerMapping;
import com.daddylab.supplier.item.infrastructure.third.enums.KsMessageEnum;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.IKsMessageHandlerService;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.domain.dto.KsMessageDTO;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.method.HandlerMethod;

import java.lang.reflect.Method;
import java.util.EnumMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @class RedBookMessageHandlerServiceImpl.java
 * @description 消息调度类
 * @date 2024-03-01 15:42
 */
@Slf4j
@Component
public class KsMessageHandlerSupport implements InitializingBean, ApplicationContextAware {

    private final EnumMap<KsMessageEnum, HandlerMethod> enumsMethods = new EnumMap<>(KsMessageEnum.class);

    private ApplicationContext applicationContext;

    /**
     * 消息调度
     *
     * @param ksMessageDTO KsMessageDTO
     * @date 2024/3/1 15:59
     * <AUTHOR>
     */
    public void dispatcher(KsMessageDTO ksMessageDTO) {
        KsMessageEnum ksMessageEnum = KsMessageEnum.ofValue(ksMessageDTO.getEvent());
        HandlerMethod handlerMethod = null;
        if (!Objects.isNull(ksMessageEnum)) {
            handlerMethod = enumsMethods.get(ksMessageEnum);
        }
        if (Objects.isNull(handlerMethod)) {
            handlerMethod = enumsMethods.get(KsMessageEnum.ITEM_DEFAULT);
        }
        if (Objects.isNull(handlerMethod)) {
            return;
        }
        String dataJson = StringEscapeUtils.unescapeJava(ksMessageDTO.getInfo());

        Object bean = handlerMethod.getBean();
        Method method = handlerMethod.getMethod();
        Object[] args = new Object[method.getParameterCount()];
        Class<?>[] argTypes = method.getParameterTypes();
        try {
            for (int i = 0; i < args.length; i++) {
                if (argTypes[i].isAssignableFrom(KsMessageEnum.class)) {
                    args[i] = ksMessageEnum;
                } else if (argTypes[i].isAssignableFrom(KsMessageDTO.class)) {
                    args[i] = ksMessageDTO;
                } else if (argTypes[i].isAssignableFrom(Map.class)) {
                    args[i] = JsonUtil.parse(dataJson, Map.class);
                } else {
                    args[i] = JsonUtil.parse(dataJson, argTypes[i]);
                }
            }
            ReflectionUtils.invokeMethod(method, bean, args);
        } catch (Exception e) {
            log.error("【调用方法失败】errMsg={}, beanName={}, method={}, args={}", e.getMessage(), bean.getClass().getName(), method.getName(), args, e);
        }
    }




    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, IKsMessageHandlerService> beansOfType = applicationContext.getBeansOfType(IKsMessageHandlerService.class);
        beansOfType.forEach((name, bean) -> {
            Method[] methods = ReflectUtil.getMethods(bean.getClass(), method -> Objects.nonNull(AnnotationUtils.findAnnotation(method, KsHandlerMapping.class)));
            for (Method method : methods) {
                KsHandlerMapping annotation = AnnotationUtils.findAnnotation(method, KsHandlerMapping.class);
                if (Objects.isNull(annotation)) {
                    continue;
                }
                enumsMethods.put(annotation.value(), new HandlerMethod(bean, method));
            }
        });
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
