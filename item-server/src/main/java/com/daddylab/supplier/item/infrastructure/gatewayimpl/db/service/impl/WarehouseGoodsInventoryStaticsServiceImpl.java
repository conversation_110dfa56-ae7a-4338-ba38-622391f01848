/*
package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.EditSkuRatioDto;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WarehouseGoodsInventoryStatics;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WarehouseGoodsInventoryStaticsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WarehouseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLock;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

*/
/**
 * <p>
 * 实仓中SKU的库存占比统计 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02
 *//*

@Service
@AllArgsConstructor
public class WarehouseGoodsInventoryStaticsServiceImpl extends DaddyServiceImpl<WarehouseGoodsInventoryStaticsMapper, WarehouseGoodsInventoryStatics> implements IWarehouseGoodsInventoryStaticsService {

    final IWarehouseService iWarehouseService;
    final IVirtualWarehouseInventoryGoodsService iVirtualWarehouseInventoryGoodsService;
    final IWdtStockSpecService iWdtStockSpecService;
    final IVirtualWarehouseInventoryService iVirtualWarehouseInventoryService;
    final IShopInventoryGoodsService iShopInventoryGoodsService;
    final IShopInventoryService iShopInventoryService;
    final static String EDIT_SKU_RATIO_KEY = "editSkuRatio";
    final WarehouseMapper warehouseMapper;


    @Override
    @DistributedLock(value = EDIT_SKU_RATIO_KEY)
    public Response addBatchSkuRatio(List<EditSkuRatioDto> editSkuRatioDtoList) {
        if (CollUtil.isEmpty(editSkuRatioDtoList)) {
            return Response.buildSuccess();
        }

        EditSkuRatioDto editSkuRatioDto = editSkuRatioDtoList.get(0);
        String warehouseNo = editSkuRatioDto.getWarehouseNo();

        List<WarehouseGoodsInventoryStatics> addList = new LinkedList<>();
        List<WarehouseGoodsInventoryStatics> updateList = new LinkedList<>();

        List<WarehouseGoodsInventoryStatics> thisWarehouseStaticsList = this.lambdaQuery().eq(WarehouseGoodsInventoryStatics::getWarehouseNo, warehouseNo).list();
        if (CollUtil.isEmpty(thisWarehouseStaticsList)) {
            List<WarehouseGoodsInventoryStatics> collect = editSkuRatioDtoList.stream().map(val -> {
                WarehouseGoodsInventoryStatics statics = new WarehouseGoodsInventoryStatics();
                statics.setWarehouseNo(warehouseNo);
                statics.setSkuNo(val.getSkuNo());
                Integer newVal = Objects.nonNull(val.getNewVal()) ? val.getNewVal() : 0;
                Integer oldVal = Objects.nonNull(val.getOldVal()) ? val.getOldVal() : 0;
                statics.setInventoryRatio(Math.max(0, (newVal - oldVal)));
                return statics;
            }).collect(Collectors.toList());
            addList.addAll(collect);
        } else {
            Map<String, WarehouseGoodsInventoryStatics> oldSkuRatioEntityMap = thisWarehouseStaticsList.stream()
                    .collect(Collectors.toMap(WarehouseGoodsInventoryStatics::getSkuNo, Function.identity()));

            for (EditSkuRatioDto editDto : editSkuRatioDtoList) {
                String skuNo = editDto.getSkuNo();
                int d = editDto.getNewVal() - editDto.getOldVal();
                WarehouseGoodsInventoryStatics oldSkuRatioEntity = oldSkuRatioEntityMap.get(skuNo);

                if (Objects.nonNull(oldSkuRatioEntity)) {
                    if (d != 0) {
                        Integer oldSkuRatio = oldSkuRatioEntity.getInventoryRatio();
                        int newSkuRatio = oldSkuRatio + d;
                        if (newSkuRatio > 100) {
                            int otherSkuSum = oldSkuRatio - editDto.getOldVal();
                            String error = "库存占比异常。仓库编码：" + warehouseNo + "，SKU编码：" + editDto.getSkuNo() + "，累计库存占比：" + otherSkuSum + "%，剩余可用占比：" + (100 - otherSkuSum) + "%";
                            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, error);
                        } else {
                            oldSkuRatioEntity.setInventoryRatio(Math.max(0, newSkuRatio));
                            updateList.add(oldSkuRatioEntity);
                        }
                    }
                } else {
                    WarehouseGoodsInventoryStatics newStatics = new WarehouseGoodsInventoryStatics();
                    newStatics.setWarehouseNo(warehouseNo);
                    newStatics.setSkuNo(skuNo);
                    newStatics.setInventoryRatio(Math.max(0, d));
                    addList.add(newStatics);
                }
            }
        }

        if (CollUtil.isNotEmpty(addList)) {
            this.saveBatch(addList.stream().distinct().collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(updateList)) {
            this.updateBatchById(updateList.stream().distinct().collect(Collectors.toList()));
        }

        return Response.buildSuccess();
    }

    @Override
    @DistributedLock
    public Response addWarehouseRatio(String warehouseNo, Integer newVal, Integer oldVal) {
        Optional<Warehouse> warehouseOpt = iWarehouseService.queryWarehouseByNo(warehouseNo);
        if(!warehouseOpt.isPresent()){
            return Response.buildSuccess();
        }
        Warehouse warehouse = warehouseOpt.get();
        int d = newVal - oldVal;
        if (d == 0) {
            return Response.buildSuccess();
        }

        int newRatio = warehouse.getInventoryRatio() + d;
        if (newRatio > 100) {
            int otherSum = warehouse.getInventoryRatio() - oldVal;
            throw ExceptionPlusFactory.bizException("库存占比异常。" + warehouseNo + "仓库编码，累计库存占比：" + otherSum + "，" + "剩余可用占比：" + (100 - otherSum));
        } else {
            warehouse.setInventoryRatio(Math.max(0, newRatio));
            iWarehouseService.updateById(warehouse);
        }

        return Response.buildSuccess();
    }


}

*/
