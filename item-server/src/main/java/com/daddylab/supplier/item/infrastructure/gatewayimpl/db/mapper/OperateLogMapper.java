package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 操作日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Repository
public interface OperateLogMapper extends DaddyBaseMapper<OperateLog> {


    void copyOperateLogs(@Param("type") OperateLogTarget type, @Param("sourceId") Long sourceId, @Param("targetId") Long targetId);
}
