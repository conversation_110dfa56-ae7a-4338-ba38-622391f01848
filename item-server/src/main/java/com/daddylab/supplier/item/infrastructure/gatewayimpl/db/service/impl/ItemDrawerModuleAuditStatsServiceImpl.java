package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAuditStats;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerModuleAuditStatsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerModuleAuditStatsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品库抽屉模块审核统计数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
@Service
public class ItemDrawerModuleAuditStatsServiceImpl extends DaddyServiceImpl<ItemDrawerModuleAuditStatsMapper, ItemDrawerModuleAuditStats> implements IItemDrawerModuleAuditStatsService {

    @Override
    public ItemDrawerModuleAuditStats getModuleAuditStatsCreateIfNotExists(ItemAuditType type, Long itemId, Integer round) {
        return lambdaQuery()
                .eq(ItemDrawerModuleAuditStats::getItemId, itemId)
                .eq(ItemDrawerModuleAuditStats::getType, type)
                .eq(ItemDrawerModuleAuditStats::getRound, round).oneOpt()
                .orElseGet(() -> {
                    final ItemDrawerModuleAuditStats v = new ItemDrawerModuleAuditStats();
                    v.setItemId(itemId);
                    v.setRound(round);
                    save(v);
                    return v;
                });
    }
}
