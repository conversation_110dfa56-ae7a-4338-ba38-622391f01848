package com.daddylab.supplier.item.application.afterSales;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022年10月17日 10:57 AM
 */
@Data
@ApiModel("售后异常件信息登记参数")
public class AfterSalesAbnormalCmd extends Command {

    private static final long serialVersionUID = 5064969266607194698L;
    @ApiModelProperty("异常件id,更新必填")
    private Long abnormalInfoId;

    @ApiModelProperty("物流编号")
    @NotNull(message = "物流编号不得为空")
    private String logisticsNo;

    @ApiModelProperty("物流名称")
    @NotNull(message = "物流名称不得为空")
    private String logisticsName;

    @ApiModelProperty("登记仓库")
    @NotNull(message = "登记仓库不得为空")
    private String warehouseNo;

    @ApiModelProperty("商品名称")
    @NotNull(message = "商品名称不得为空")
    private String itemName;

    @ApiModelProperty("数量")
    @Max(value = 999999999, message = "数量太大，不支持")
    @NotNull(message = "数量不得为空")
    private Integer quantity;

    @ApiModelProperty("图片")
    private List<String> imageUrls;

    @ApiModelProperty("备注")
    private String remark;

//    @ApiModelProperty("P系统token，P系统中发起请求中不得为空")
//    private String partnerToken;
//
//    @ApiModelProperty("前端忽略")
//    private PartnerProviderResp partnerProviderResp;


}
