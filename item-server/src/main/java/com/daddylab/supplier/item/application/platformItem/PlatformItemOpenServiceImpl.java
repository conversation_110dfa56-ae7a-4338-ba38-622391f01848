package com.daddylab.supplier.item.application.platformItem;

import cn.hutool.core.collection.CollectionUtil;
import com.daddylab.supplier.item.application.platformItem.converter.PlatformItemConverter;
import com.daddylab.supplier.item.controller.item.ArkSailorItemBizService;
import com.daddylab.supplier.item.controller.open.PlatformItemOpenQuery;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.platformItem.service.PlatformItemSyncService;
import com.daddylab.supplier.item.domain.platformItem.vo.PlatformItemOpenVO;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.domain.wdt.service.PlatformItemFetchService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class PlatformItemOpenServiceImpl implements PlatformItemOpenService {

    @Autowired IPlatformItemService platformItemService;
    @Autowired IPlatformItemSkuService platformItemSkuService;
    @Autowired IItemService itemService;

    @Override
    public List<PlatformItemOpenVO> openListQuery(PlatformItemOpenQuery query) {
        if (CollectionUtil.isEmpty(query.getItemNos())) {
            return Collections.emptyList();
        }
        // 查询item
        List<Item> items =
                itemService.lambdaQuery().in(Item::getPartnerProviderItemSn, query).list();
        if (items.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Long, String> itemPartnerItemSnMap =
                items.stream()
                        .collect(Collectors.toMap(Item::getId, Item::getPartnerProviderItemSn));
        List<Long> itemIds = items.stream().map(Item::getId).collect(Collectors.toList());
        List<PlatformItem> platformItems =
                platformItemService.lambdaQuery().in(PlatformItem::getItemId, itemIds).list();
        if (CollectionUtil.isEmpty(platformItems)) {
            return Collections.emptyList();
        }
        // 对象转换
        List<PlatformItemOpenVO> platformItemOpenVOS =
                PlatformItemConverter.INSTANCE.platformItemListToPlatformItemVOList(platformItems);
        List<Long> platformItemIds =
                platformItems.stream().map(PlatformItem::getId).collect(Collectors.toList());

        // 查询平台商品sku
        List<PlatformItemSku> platformItemSkus =
                platformItemSkuService
                        .lambdaQuery()
                        .in(PlatformItemSku::getPlatformItemId, platformItemIds)
                        .list();
        Map<Long, List<PlatformItemSku>> platformItemSkuMap =
                platformItemSkus.stream()
                        .collect(Collectors.groupingBy(PlatformItemSku::getPlatformItemId));
        for (PlatformItemOpenVO platformItemOpenVO : platformItemOpenVOS) {
            platformItemOpenVO.setPartnerProviderItemSn(
                    itemPartnerItemSnMap.getOrDefault(platformItemOpenVO.getItemId(), ""));
            platformItemOpenVO.setPlatformItemSkus(
                    platformItemSkuMap.getOrDefault(
                            platformItemOpenVO.getId(), Collections.emptyList()));
        }
        return platformItemOpenVOS;
    }
}
