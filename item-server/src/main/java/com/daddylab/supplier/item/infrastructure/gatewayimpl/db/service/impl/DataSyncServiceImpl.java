package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataSync;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DataSyncMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDataSyncService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 数据同步记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-04
 */
@Service
public class DataSyncServiceImpl extends DaddyServiceImpl<DataSyncMapper, DataSync> implements IDataSyncService {

}
