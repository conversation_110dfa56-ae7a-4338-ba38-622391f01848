package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.application.stockSpec.InventoryMonitorAlertVO;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryMonitor;
import com.daddylab.supplier.item.types.inventoryMonitor.IInventoryMonitorId;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <p>
 * 库存警戒 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface IInventoryMonitorService extends IDaddyService<InventoryMonitor> {

    /**
     * @param monitorIds
     */
    Map<IInventoryMonitorId, InventoryMonitor> selectByMonitorIds(List<IInventoryMonitorId> monitorIds);

    List<InventoryMonitor> selectByWarehouseNos(List<String> warehouseNos);


    Optional<InventoryMonitor> selectOneByMonitorId(IInventoryMonitorId monitorId);

    List<InventoryMonitorAlertVO> selectAbsoluteThresholdAlertList(int limit, Long cursor);
}
