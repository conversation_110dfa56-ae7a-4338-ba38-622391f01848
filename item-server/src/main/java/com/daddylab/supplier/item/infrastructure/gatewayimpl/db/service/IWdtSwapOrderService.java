package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSwapOrder;

/**
 * <p>
 * 旺店通换出订单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
public interface IWdtSwapOrderService extends IDaddyService<WdtSwapOrder> {

}
