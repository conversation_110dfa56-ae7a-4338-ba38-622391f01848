package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/24 14:44
 * @Description: 盘货表活动类型
 */
@Getter
@AllArgsConstructor
public enum HandingSheetActivityTypeEnum implements IEnum<Integer> {
    /**
     * 循环满满减
     */
    LOOP_FULL_REDUCTION(1, "循环满减"),
    /**
     * 阶梯满减
     */
    LADDER_FULL_REDUCTION(2, "阶梯满减"),
    ;

    private Integer value;
    private String desc;

    public static HandingSheetActivityTypeEnum of(Integer value) {
        for (HandingSheetActivityTypeEnum typeEnum : HandingSheetActivityTypeEnum.values()) {
            if (typeEnum.getValue().equals(value)) {
                return typeEnum;
            }
        }
        return null;
    }
}
