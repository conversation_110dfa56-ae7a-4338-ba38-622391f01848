package com.daddylab.supplier.item.application.purchasePayable.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年02月03日 11:54 AM
 */
@Data
@ApiModel("手动保存采购入库单详情请求参数")
public class ArtificialPayOrderDetailSaveDto {

    /**
     * skuCode
     */
    private String skuCode;

    /**
     * 修正后的单价，价税合计
     */
    private BigDecimal price;

    /**
     * 修正后的数量
     */
    private Integer fixedQuantity;

    /**
     * 修正后的总金额
     */
    private BigDecimal fixedTotalAmount;




//    /**
//     * 旧的数量
//     */
//    private Integer oldQuantity;
//
//    /**
//     * 旧的金额
//     */
//    private BigDecimal oldTotalAmount;


}
