package com.daddylab.supplier.item.application.recognitionTask;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerRecognitionTask;
import com.daddylab.supplier.item.types.recognitionTask.ItemDrawerRecognitionTaskStatus;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/12/8
 */
public interface ItemRecognitionTaskGateway {

    List<ItemDrawerRecognitionTask> getWaitImageCvRecognitionTasks();

    void updateTaskCvResult(ItemDrawerRecognitionTask itemDrawerRecognitionTask,
            String content,
            String cvResult,
            ItemDrawerRecognitionTaskStatus status, List<String> logs);
}
