package com.daddylab.supplier.item.application.order.settlement.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年01月25日 10:09 AM
 */
@Data
@ApiModel("刷新结算时间请求参数")
public class RefreshDataCmd extends Command {

    @ApiModelProperty("明细 ID数组")
    List<Long> detailIds;
}
