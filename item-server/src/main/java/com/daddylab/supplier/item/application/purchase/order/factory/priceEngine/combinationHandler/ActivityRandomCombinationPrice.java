package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.combinationHandler;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.BaseProcess;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.PriceProcessor;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseRandomSkuCombinationPrice;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Semaphore;

/**
 * 活动价-同spu下，sku随机，任意数量组合价。
 * 只处理单品
 *
 * <AUTHOR> up
 * @date 2022年10月21日 5:16 PM
 */
@Service
@Slf4j
public class ActivityRandomCombinationPrice extends BaseProcess implements PriceProcessor {

    private final Semaphore semaphore = new Semaphore(4);

    @Override
    public Boolean doPriceProcess(TimeBO timeBO) {
        log.info("ActivityRandomCombinationPrice doPriceProcess start");

        List<PurchaseRandomSkuCombinationPrice> list = getRandomCombinationPriceByType(timeBO, 2);
        if (CollUtil.isEmpty(list)) {
            log.info("ActivityRandomCombinationPrice doPriceProcess finish.purchase price is empty");
            return true;
        }

        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        list.forEach(val -> ThreadUtil.execute(PoolEnum.PURCHASE_POOL, () -> {
            try {
                semaphore.acquire();
                log.info("ActivityRandomCombinationPrice doPriceProcess start skuCode:{}", val.getCode());
                randomCombinationPriceHandler(val, timeBO.getOperateMonth(), 2);
                log.info("ActivityRandomCombinationPrice doPriceProcess finish skuCode:{}", val.getCode());
            } catch (Exception e) {
                log.error("ActivityRandomCombinationPrice doPriceProcess fail price:{}", JsonUtil.toJson(val), e);
            } finally {
                countDownLatch.countDown();
                semaphore.release();
            }
        }));

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("[{}] ActivityRandomCombinationPrice doPriceProcess await InterruptedException", timeBO.getOperateMonth(), e);
        }

        log.info("ActivityRandomCombinationPrice doPriceProcess finish");
        return true;
    }
}
