package com.daddylab.supplier.item.infrastructure.config;

import com.google.common.collect.Lists;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/21
 */
@Configuration
@ConfigurationProperties(prefix = "processor")
public class ProcessorConfig {
    /**
     * QC主管
     */
    private List<Long> qcSupervisors = Lists.newArrayList(7505357L);
}
