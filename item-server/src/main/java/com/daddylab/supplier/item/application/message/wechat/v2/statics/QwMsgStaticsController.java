package com.daddylab.supplier.item.application.message.wechat.v2.statics;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> up
 * @date 2024年05月17日 9:53 AM
 */
@Slf4j
@RestController
@RequestMapping("/qwMsgStatics")
public class QwMsgStaticsController {


    @Resource
    StaticsMsgBizService staticsMsgBizService;


    @ResponseBody
    @GetMapping("/statics")
    @Auth(noAuth = true)
    SingleResponse<Boolean> statics(String msgCode) {
         staticsMsgBizService.handle(msgCode);
         return SingleResponse.of(true);
    }


}
