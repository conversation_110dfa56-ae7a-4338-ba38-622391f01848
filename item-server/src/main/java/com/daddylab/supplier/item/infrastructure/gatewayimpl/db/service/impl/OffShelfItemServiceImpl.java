package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OffShelfItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OffShelfItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOffShelfItemService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 下架管理-下架流程商品信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Service
public class OffShelfItemServiceImpl extends DaddyServiceImpl<OffShelfItemMapper, OffShelfItem> implements IOffShelfItemService {

}
