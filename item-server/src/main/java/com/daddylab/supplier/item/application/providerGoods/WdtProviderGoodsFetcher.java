package com.daddylab.supplier.item.application.providerGoods;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.purchase.ProviderGoodsAPI;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.ProviderGoodsQueryDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.ProviderGoodsQueryDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.ProviderGoodsQueryDetailResponse.Details;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtProviderGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtProviderGoodsMapper;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import java.util.ArrayList;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/5/15
 */
@Component
public class WdtProviderGoodsFetcher implements PageFetcher {

    private final WdtGateway wdtGateway;
    private final WdtProviderGoodsMapper wdtProviderGoodsMapper;

    public WdtProviderGoodsFetcher(WdtGateway wdtGateway,
            WdtProviderGoodsMapper wdtProviderGoodsMapper) {
        this.wdtGateway = wdtGateway;
        this.wdtProviderGoodsMapper = wdtProviderGoodsMapper;
    }

    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.WDT_PROVIDER_GOODS;
    }

    @Override
    public int getTotal() {
        return query(1, 1, true).getTotalCount();
    }

    @Override
    public void fetch(long pageIndex, long pageSize, RunContext runContext) {
        final ProviderGoodsQueryDetailResponse response = query((int) pageIndex, (int) pageSize,
                false);
        if (CollUtil.isEmpty(response.getDetails())) {
            return;
        }
        final ArrayList<WdtProviderGoods> wdtProviderGoodsList = new ArrayList<>();
        for (Details detail : response.getDetails()) {
            final WdtProviderGoods wdtProviderGoods = ASSEMBLER.INST.apiDoToPo(detail);
            final Long currentTime = DateUtil.currentTime();
            wdtProviderGoods.setCreatedAt(currentTime);
            wdtProviderGoods.setCreatedUid(0L);
            wdtProviderGoods.setUpdatedAt(currentTime);
            wdtProviderGoods.setUpdatedUid(0L);
            wdtProviderGoodsList.add(wdtProviderGoods);
        }
        wdtProviderGoodsMapper.saveOrUpdateBatch(wdtProviderGoodsList);
    }

    private ProviderGoodsQueryDetailResponse query(int pageNo, int pageSize, boolean calcTotal) {
        try {
            final ProviderGoodsAPI api = wdtGateway.getAPI(ProviderGoodsAPI.class);
            final ProviderGoodsQueryDetailParams params = new ProviderGoodsQueryDetailParams();
            final Pager pager = new Pager(pageSize, pageNo - 1, calcTotal);
            return api.queryDetail(params, pager);
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapFetchException(e, fetchDataType());
        }
    }

    @Mapper
    interface ASSEMBLER {

        ASSEMBLER INST = Mappers.getMapper(ASSEMBLER.class);

        @Mapping(target = "id", ignore = true)
        @Mapping(target = "updatedAt", ignore = true)
        @Mapping(target = "updatedUid", ignore = true)
        @Mapping(target = "createdAt", ignore = true)
        @Mapping(target = "createdUid", ignore = true)
        @Mapping(target = "isDel", ignore = true)
        WdtProviderGoods apiDoToPo(Details details);
    }
}
