package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSwapOrder;
import java.util.Collection;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 旺店通换出订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Repository
public interface WdtSwapOrderMapper extends DaddyBaseMapper<WdtSwapOrder> {
    /**
     * 批量插入或者已存在重复记录时更新数据
     *
     * @param orders 数据集合
     */
    void saveOrUpdateBatch(@Param("orders") Collection<WdtSwapOrder> orders);
}
