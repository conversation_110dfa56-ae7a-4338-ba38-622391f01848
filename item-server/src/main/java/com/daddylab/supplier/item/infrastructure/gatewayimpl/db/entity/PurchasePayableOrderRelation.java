package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayOrderRelateType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 采购应付关联信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PurchasePayableOrderRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 创建者（关联入库单/出库单/其他应付的创建人）
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 采购应付单原编码
     */
    @ApiModelProperty("采购应付单原编码")
    private String resourceNo;

    @ApiModelProperty("关联生成的出入库单")
    private String relateStockOrderNo;

    @ApiModelProperty("关联类型。ALL_HEDGE(原单全部冲销);FIXED(人工修正重新生成)")
    private PayOrderRelateType type;


}
