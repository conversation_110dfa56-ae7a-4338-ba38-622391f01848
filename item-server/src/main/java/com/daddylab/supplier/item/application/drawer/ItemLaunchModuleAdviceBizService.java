package com.daddylab.supplier.item.application.drawer;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchModuleAdvice;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerModuleId;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/11/14
 */
public interface ItemLaunchModuleAdviceBizService {

    Map<ItemDrawerModuleId, List<ItemLaunchModuleAdvice>> getModuleAdvices(Long itemId, Integer round);

    Map<ItemDrawerModuleId, Map<Integer, List<ItemLaunchModuleAdvice>>> getModuleAdvicesAllRound(Long itemId);

    Map<Integer,List<ItemLaunchModuleAdvice>> getModuleAdvicesAllRoundByLiveVerbalTrickId(Long itemId,Long verbalTrickId);

}
