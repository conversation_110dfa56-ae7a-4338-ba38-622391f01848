package com.daddylab.supplier.item.infrastructure.easyexcel.converters;

import cn.hutool.core.util.ClassUtil;
import com.alibaba.excel.converters.bigdecimal.BigDecimalStringConverter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SafeBigDecimalConverter extends BigDecimalStringConverter {
    public SafeBigDecimalConverter() {
        super();
    }

    @Override
    public Class<BigDecimal> supportJavaTypeKey() {
        return super.supportJavaTypeKey();
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return super.supportExcelTypeKey();
    }

    @Override
    public BigDecimal convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        try {
            if (cellData.getType() == CellDataTypeEnum.STRING) {
                return super.convertToJavaData(cellData, contentProperty, globalConfiguration);
            } else if (cellData.getType() == CellDataTypeEnum.NUMBER) {
                cellData.setStringValue(cellData.getNumberValue().toPlainString());
                return super.convertToJavaData(cellData, contentProperty, globalConfiguration);
            } else {
                throw new IllegalArgumentException("当前数据转换器不支持该单元格数据格式:" + cellData.getType());
            }
        } catch (Exception e) {
            log.warn("单元格数据转化为Java类型异常 异常:{} 值:{} 属性:{}", e, cellData.getStringValue(), getPropertyDesc(contentProperty));
            return null;
        }
    }

    @Override
    public WriteCellData<?> convertToExcelData(BigDecimal value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        try {
            return super.convertToExcelData(value, contentProperty, globalConfiguration);
        } catch (Exception e) {
            log.warn("Java数据类型转化为单元格数据异常 异常:{} 值:{} 属性:{}", e, value, getPropertyDesc(contentProperty));
            return null;
        }
    }

    private String getPropertyDesc(ExcelContentProperty contentProperty) {
        final Field field = contentProperty.getField();
        final Class<?> declaringClass = field.getDeclaringClass();
        final String className = ClassUtil.getClassName(declaringClass, true);
        return className + "@" + field.getName();
    }
}
