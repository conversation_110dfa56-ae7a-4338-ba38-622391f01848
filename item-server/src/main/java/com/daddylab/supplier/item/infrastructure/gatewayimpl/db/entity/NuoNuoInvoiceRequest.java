package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InvoiceTitleType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InvoiceType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 用户诺诺开票请求参数
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class NuoNuoInvoiceRequest implements Serializable {


    private static final long serialVersionUID = -7895222931634974087L;
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 发票类型
     */
    private InvoiceType invoiceType;

    /**
     * 发票抬头类型
     */
    private InvoiceTitleType invoiceTitleType;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * g税号
     */
    private String taxCode;

    /**
     * 开户银行
     */
    private String bank;

    /**
     * 开户银行账号
     */
    private String bankNo;

    /**
     * 公司名称
     */
    private String companyAddress;

    /**
     * 企业电话
     */
    private String companyPhone;

    /**
     * 邮件
     */
    private String mailAddress;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic(value = "0", delval = "id")
    private Long isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

//    /**
//     * 请求状态
//     * 0 默认，初始状态
//     * 1 开票请求中
//     * 2 开票请求结束，终态
//     */
//    private Integer status;

    // -------------  expand sys param -----------------



}
