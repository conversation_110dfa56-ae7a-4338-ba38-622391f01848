package com.daddylab.supplier.item.application.afterSalesForwarding;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.job.extend.autoRegister.XxlJobAutoRegister;
import com.daddylab.supplier.item.application.afterSaleLink.AfterSaleShareLinkBizService;
import com.daddylab.supplier.item.application.afterSales.AfterSalesWarehouseBizService;
import com.daddylab.supplier.item.application.afterSales.WarehouseAfterSalesAddressVO;
import com.daddylab.supplier.item.application.afterSalesForwarding.types.*;
import com.daddylab.supplier.item.application.refundOrder.RefundOrderService;
import com.daddylab.supplier.item.application.warehouse.WarehouseGateway;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.ResponseAssert;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.domain.exportTask.ExportManager;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.common.CompositeUserContext;
import com.daddylab.supplier.item.infrastructure.common.LoginType;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSaleForwardingNeedNotForwardTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSaleForwardingRegisterStatusEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.GoodsType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.oss.OssGateway;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderBaseInfo;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderDetail;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderQuery;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderWithDetails;

import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validator;

import lombok.extern.slf4j.Slf4j;
import org.javers.core.diff.Diff;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2024/5/24
 */
@Slf4j
@Service
public class AfterSalesForwardingRegisterBizServiceImpl
        implements AfterSalesForwardingRegisterBizService {
    @Autowired
    private IAfterSaleForwardingRegisterService afterSaleForwardingRegisterService;
    
    @Autowired
    private IWdtOrderService wdtOrderService;
    
    @Autowired
    private IWdtSaleStockOutOrderService wdtSaleStockOutOrderService;
    
    @Autowired
    private IWdtOrderDetailService wdtOrderDetailService;
    
    @Autowired
    private IWdtRefundOrderService wdtRefundOrderService;
    
    @Autowired
    private IWdtRefundOrderDetailService wdtRefundOrderDetailService;
    
    @Autowired
    private WarehouseGateway warehouseGateway;
    
    @Autowired
    private OperateLogDomainService operateLogDomainService;
    
    @Autowired
    private ExportManager exportManager;
    
    @Autowired
    private AfterSaleShareLinkBizService afterSaleShareLinkBizService;
    
    @Autowired
    private Validator validator;
    
    @Autowired
    IWarehouseService iWarehouseService;
    
    @Autowired
    IWarehouseAfterSalesAddressService iWarehouseAfterSalesAddressService;
    
    @Autowired
    private AfterSalesWarehouseBizService afterSalesWarehouseBizService;
    
    @Resource
    FileGateway fileGateway;
    
    @Resource
    OssGateway ossGateway;
    
    @Resource
    IFileService iFileService;
    
    @Autowired
    IExternalUserService externalUserService;
    
    @Autowired
    private IItemSkuService itemSkuService;
    
    @Autowired
    private IItemService itemService;
    @Autowired
    private ShopGateway shopGateway;
    @Autowired
    private RefundOrderService refundOrderService;
    
    @Override
    public MultiResponse<AfterSalesForwardingRegisterFormItemVO> completion(
            AfterSalesForwardingRegisterCompletionCmd cmd) {
        final String deliveryNo = cmd.getDeliveryNo();
        final List<RefundOrderWithDetails> refundOrderWithDetails =
                getWdtRefundOrderDetails(deliveryNo);
        if (refundOrderWithDetails.isEmpty()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "该物流单号未找到关联退换单数据");
        }
        final ArrayList<AfterSalesForwardingRegisterFormItemVO> formItemVOs = new ArrayList<>();
        final List<WdtRefundOrderDetail> list =
                refundOrderWithDetails.stream()
                        .flatMap(v -> v.getDetails().stream())
                        .collect(Collectors.toList());
        for (WdtRefundOrderDetail detail : list) {
            final AfterSalesForwardingRegisterFormItemVO formItemVO =
                    new AfterSalesForwardingRegisterFormItemVO();
            formItemVO.setDeliveryNo(deliveryNo);
            formItemVO.setItemName(detail.getGoodsName());
            formItemVO.setItemNum(detail.getNum().intValue());
            formItemVO.setSkuCode(detail.getSpecNo());
            formItemVO.setSkuName(detail.getSpecName());
            formItemVO.setIntact(true);
            formItemVO.setAbnormalDescription("");
            formItemVO.setAffectsSalesVouchers(new AffectsSalesVouchers());
            formItemVO.setOrderNo(detail.getTid());
            final Optional<WdtOrder> wdtOrderOptional =
                    wdtOrderService.lambdaQuery().eq(WdtOrder::getTradeNo, detail.getTradeNo()).oneOpt();
            formItemVO.setStockoutNo(wdtOrderOptional.map(WdtOrder::getStockoutNo).orElse(""));
            final String warehouseNo = wdtOrderOptional.map(WdtOrder::getWarehouseNo).orElse("");
            formItemVO.setStockoutWarehouse(warehouseNo);
            final Optional<Warehouse> warehouse = warehouseGateway.getWarehouse(warehouseNo);
            warehouse.ifPresent(w -> formItemVO.setStockoutWarehouse(w.getName()));
            formItemVO.setStockoutWarehouseNo(warehouseNo);
            formItemVO.setShopNo(wdtOrderOptional.map(WdtOrder::getShopNo).orElse(""));
            formItemVO.setShopName(wdtOrderOptional.map(WdtOrder::getShopName).orElse(""));
            formItemVOs.add(formItemVO);
        }
        return MultiResponse.of(formItemVOs);
    }
    
    private List<RefundOrderWithDetails> getWdtRefundOrderDetails(String deliveryNo) {
        if (StringUtil.isBlank(deliveryNo)) {
            return Collections.emptyList();
        }
        final List<WdtRefundOrder> wdtRefundOrders =
                wdtRefundOrderService
                        .lambdaQuery()
                        .eq(WdtRefundOrder::getReturnLogisticsNo, deliveryNo)
                        .list();
        if (wdtRefundOrders.isEmpty()) {
            return Collections.emptyList();
        }
        final Set<String> refundNos =
                wdtRefundOrders.stream().map(WdtRefundOrder::getRefundNo).collect(Collectors.toSet());
        final List<WdtRefundOrderDetail> wdtRefundOrderDetails =
                wdtRefundOrderDetailService
                        .lambdaQuery()
                        .in(WdtRefundOrderDetail::getRefundNo, refundNos)
                        .list();
        return wdtRefundOrders.stream()
                .map(
                        refundOrder -> {
                            final RefundOrderWithDetails refundOrderWithDetails = new RefundOrderWithDetails();
                            refundOrderWithDetails.setRefundOrder(refundOrder);
                            final List<WdtRefundOrderDetail> orderDetails =
                                    wdtRefundOrderDetails.stream()
                                            .filter(v -> v.getRefundNo().equals(refundOrder.getRefundNo()))
                                            .collect(Collectors.toList());
                            refundOrderWithDetails.setDetails(orderDetails);
                            return refundOrderWithDetails;
                        })
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<Boolean> save(AfterSalesForwardingRegisterSaveCmd cmd) {
        for (AfterSalesForwardingRegisterFormItemVO formItemVO : cmd.getRegisterList()) {
            saveInternal(formItemVO);
        }
        final Set<String> deliveryNoList =
                cmd.getRegisterList().stream()
                        .map(AfterSalesForwardingRegisterFormItemVO::getDeliveryNo)
                        .collect(Collectors.toSet());
        autoHandle(deliveryNoList);
        return SingleResponse.of(true);
    }
    
    private void saveInternal(AfterSalesForwardingRegisterFormItemVO formItemVO) {
        saveInternal(formItemVO, "");
    }
    
    private void saveInternal(AfterSalesForwardingRegisterFormItemVO formItemVO, String logPrefix) {
        AfterSaleForwardingRegister afterSaleForwardingRegister =
                AfterSalesForwardingAssembler.INSTANCE.afterSaleForwardingRegisterFormItemVoToPo(
                        formItemVO);
        afterSaleForwardingRegister.setOperatorId(CompositeUserContext.getUserId());
        afterSaleForwardingRegister.setOperatorType(CompositeUserContext.getLoginType().ordinal());
        
        final Optional<AfterSaleForwardingRegister> afterSaleForwardingRegister0 =
                Optional.ofNullable(formItemVO.getId())
                        .filter(NumberUtil::isPositive)
                        .map(afterSaleForwardingRegisterService::getById);
        final Optional<AfterSalesForwardingRegisterFormItemVO> afterSalesForwardingRegisterFormItemVO0 =
                afterSaleForwardingRegister0.map(
                        AfterSalesForwardingAssembler.INSTANCE::afterSaleForwardingRegisterPoToFormItemVo);
        Optional<WdtSaleStockOutOrder> wdtSaleStockOutOrder =
                Optional.ofNullable(formItemVO.getStockoutNo())
                        .map(
                                stockoutNo ->
                                        wdtSaleStockOutOrderService
                                                .lambdaQuery()
                                                .eq(WdtSaleStockOutOrder::getOrderNo, stockoutNo)
                                                .one());
        afterSaleForwardingRegister.setShopNo(
                wdtSaleStockOutOrder.map(WdtSaleStockOutOrder::getShopNo).orElse(""));
        if (afterSalesForwardingRegisterFormItemVO0.isPresent()) {
            // 转寄单号【待转寄】状态下初次填写，跳转到【已转寄】状态
            final String deliveryNo = afterSaleForwardingRegister.getForwardingDeliveryNo();
            boolean setToBeForwarded = false;
            if (StringUtil.isNotBlank(deliveryNo)
                    && afterSaleForwardingRegister0
                    .filter(v -> v.getStatus() == AfterSaleForwardingRegisterStatusEnum.TO_BE_FORWARD)
                    .isPresent()) {
                afterSaleForwardingRegister.setStatus(AfterSaleForwardingRegisterStatusEnum.FORWARDED);
                setToBeForwarded = true;
            }
            final AfterSalesForwardingRegisterFormItemVO oldFormItemVO =
                    afterSalesForwardingRegisterFormItemVO0.get();
            final Diff diff = DiffUtil.diff(oldFormItemVO, formItemVO);
            final String diffLog =
                    DiffUtil.getVerboseDiffLog(diff, AfterSalesForwardingRegisterFormItemVO.class, logPrefix);
            afterSaleForwardingRegisterService.updateById(afterSaleForwardingRegister);
            final StringBuilder msg =
                    new StringBuilder(
                            String.format(
                                    "%s修改了商品 %s %s，%s；",
                                    Optional.ofNullable(logPrefix).orElse(""),
                                    Optional.ofNullable(afterSaleForwardingRegister.getSkuCode()).orElse(""),
                                    Optional.ofNullable(afterSaleForwardingRegister.getItemName()).orElse(""),
                                    diffLog));
            if (setToBeForwarded) {
                msg.append("【待转寄】状态下填写转寄单号，切换到【已转寄】状态");
            }
            operateLogDomainService.addOperatorLog(
                    CompositeUserContext.getLoginType(),
                    CompositeUserContext.getUserId(),
                    OperateLogTarget.AFTER_SALE_FORWARDING,
                    afterSaleForwardingRegister.getDeliveryNo(),
                    msg.toString(),
                    diff);
        } else {
            afterSaleForwardingRegisterService.save(afterSaleForwardingRegister);
            operateLogDomainService.addOperatorLog(
                    CompositeUserContext.getLoginType(),
                    CompositeUserContext.getUserId(),
                    OperateLogTarget.AFTER_SALE_FORWARDING,
                    afterSaleForwardingRegister.getDeliveryNo(),
                    String.format(
                            "%s新增了商品 %s %s",
                            Optional.ofNullable(logPrefix).orElse(""),
                            Optional.ofNullable(afterSaleForwardingRegister.getSkuCode()).orElse(""),
                            Optional.ofNullable(afterSaleForwardingRegister.getItemName()).orElse("")),
                    afterSaleForwardingRegister);
        }
    }
    
    @Override
    public PageResponse<AfterSalesForwardingRegisterPageVO> query(
            AfterSalesForwardingRegisterQuery query) {
        final LambdaQueryChainWrapper<AfterSaleForwardingRegister> queryWrapper =
                afterSaleForwardingRegisterService.lambdaQuery();
        queryWrapper.in(
                CollectionUtil.isNotEmpty(query.getIds()),
                AfterSaleForwardingRegister::getId,
                query.getIds());
        queryWrapper.eq(
                StringUtil.isNotBlank(query.getDeliveryNo()),
                AfterSaleForwardingRegister::getDeliveryNo,
                query.getDeliveryNo());
        queryWrapper.like(
                StringUtil.isNotBlank(query.getItemName()),
                AfterSaleForwardingRegister::getItemName,
                query.getItemName());
        queryWrapper.eq(
                StringUtil.isNotBlank(query.getSkuCode()),
                AfterSaleForwardingRegister::getSkuCode,
                query.getSkuCode());
        queryWrapper.ge(
                Objects.nonNull(query.getRegTimeStart()),
                AfterSaleForwardingRegister::getCreatedAt,
                query.getRegTimeStart());
        queryWrapper.le(
                Objects.nonNull(query.getRegTimeEnd()),
                AfterSaleForwardingRegister::getCreatedAt,
                query.getRegTimeEnd());
        if (query.getOrderNo() != null) {
            if (Arrays.asList("空", "无").contains(query.getOrderNo().trim())) {
                queryWrapper.isNull(AfterSaleForwardingRegister::getOrderNo);
            } else {
                queryWrapper.eq(AfterSaleForwardingRegister::getOrderNo, query.getOrderNo());
            }
        }
        if (query.getStockoutNo() != null) {
            if (Arrays.asList("空", "无").contains(query.getStockoutNo().trim())) {
                queryWrapper.isNull(AfterSaleForwardingRegister::getStockoutNo);
            } else {
                queryWrapper.eq(AfterSaleForwardingRegister::getStockoutNo, query.getStockoutNo());
            }
        }
        if (query.getForwardingDeliveryNo() != null) {
            if (Arrays.asList("空", "无").contains(query.getForwardingDeliveryNo().trim())) {
                queryWrapper.isNull(AfterSaleForwardingRegister::getForwardingDeliveryNo);
            } else {
                queryWrapper.eq(
                        AfterSaleForwardingRegister::getForwardingDeliveryNo, query.getForwardingDeliveryNo());
            }
        }
        if (query.getStockoutWarehouseNo() != null) {
            if (Arrays.asList("空", "无").contains(query.getStockoutWarehouseNo().trim())) {
                queryWrapper.isNull(AfterSaleForwardingRegister::getStockoutWarehouseNo);
            } else {
                queryWrapper.eq(
                        AfterSaleForwardingRegister::getStockoutWarehouseNo, query.getStockoutWarehouseNo());
            }
        }
        queryWrapper.eq(
                Objects.nonNull(query.getStatus()),
                AfterSaleForwardingRegister::getStatus,
                query.getStatus());
        queryWrapper.eq(
                Objects.nonNull(query.getOperatorId()),
                AfterSaleForwardingRegister::getOperatorId,
                query.getOperatorId());
        queryWrapper.eq(
                Objects.nonNull(query.getOperatorType()),
                AfterSaleForwardingRegister::getOperatorType,
                query.getOperatorType());
        queryWrapper.like(
                StringUtil.isNotBlank(query.getCsRemark()),
                AfterSaleForwardingRegister::getCsRemark,
                query.getCsRemark());
        queryWrapper.eq(
                query.getNeedNotForwardType() != null && query.getNeedNotForwardType() > 0,
                AfterSaleForwardingRegister::getNeedNotForwardType,
                query.getNeedNotForwardType());
        
        if (StrUtil.isNotBlank(query.getAddressInfo())) {
            final String addressInfoSearchKey = query.getAddressInfo().trim();
            if (ReUtil.isMatch("\\d+", addressInfoSearchKey)) {
                queryWrapper.apply(
                        "MATCH(`forwarding_address`) AGAINST({0} IN BOOLEAN MODE)", addressInfoSearchKey);
            } else {
                queryWrapper.apply(
                        "MATCH(`forwarding_address`) AGAINST({0} IN NATURAL LANGUAGE MODE)",
                        addressInfoSearchKey);
            }
        }
        if (StrUtil.isNotBlank(query.getAbnormalDescription())) {
            queryWrapper.apply(
                    "MATCH(`abnormal_description`) AGAINST({0} IN BOOLEAN MODE)",
                    query.getAbnormalDescription());
        }
        if (StrUtil.isNotBlank(query.getShopNo())) {
            queryWrapper.eq(AfterSaleForwardingRegister::getShopNo, query.getShopNo());
        }
        
        queryWrapper.orderByDesc(AfterSaleForwardingRegister::getCreatedAt);
        final IPage<AfterSaleForwardingRegister> page = query.getPage();
        final IPage<AfterSaleForwardingRegister> resPage = queryWrapper.page(page);
        
        Map<String, String> refundOrderNoMap = new HashMap<>(query.getPageSize());
        Map<String, BigDecimal> refundOrderAmountMap = new HashMap<>(query.getPageSize());
        final List<String> srcOrderNosList =
                resPage.getRecords().stream()
                        .map(AfterSaleForwardingRegister::getOrderNo)
                        .distinct()
                        .collect(Collectors.toList());
        RefundOrderQuery refundQuery = new RefundOrderQuery();
        refundQuery.setSrcOrderNoList(srcOrderNosList);
        final PageResponse<RefundOrderBaseInfo> refundOrderBaseInfoPageResponse =
                refundOrderService.refundOrderQuery(refundQuery);
        refundOrderBaseInfoPageResponse
                .getData()
                .forEach(
                        v -> {
                            final String refundOrderNo = v.getRefundOrderNo();
                            final SingleResponse<RefundOrderDetail> detail =
                                    refundOrderService.refundOrderDetail(refundOrderNo);
                            refundOrderNoMap.put(
                                    detail.getData().getSrcOrderNos(), detail.getData().getRefundOrderNo());
                            refundOrderAmountMap.put(
                                    detail.getData().getSrcOrderNos(), detail.getData().getTotalRefundAmount());
                        });
        
        return ResponseFactory.ofPage(
                page,
                v -> {
                    final AfterSalesForwardingRegisterPageVO vo = new AfterSalesForwardingRegisterPageVO();
                    vo.setId(v.getId());
                    vo.setDeliveryNo(v.getDeliveryNo());
                    vo.setStatus(v.getStatus().getValue());
                    vo.setItemName(v.getItemName());
                    vo.setItemNum(v.getItemNum());
                    vo.setSkuCode(v.getSkuCode());
                    vo.setSkuName(v.getSkuName());
                    vo.setIntact(v.getIntact());
                    vo.setAbnormalDescription(v.getAbnormalDescription());
                    vo.setAffectsSalesVouchers(
                            StringUtil.isNotBlank(v.getAffectsSalesVouchers())
                                    ? JsonUtil.parse(v.getAffectsSalesVouchers(), AffectsSalesVouchers.class)
                                    : new AffectsSalesVouchers());
                    vo.setCsRemark(v.getCsRemark());
                    vo.setOrderNo(v.getOrderNo());
                    vo.setStockoutNo(v.getStockoutNo());
                    vo.setStockoutWarehouse(v.getStockoutWarehouse());
                    vo.setStockoutWarehouseNo(v.getStockoutWarehouseNo());
                    warehouseGateway
                            .warehouseId(v.getStockoutWarehouseNo())
                            .ifPresent(vo::setStockoutWarehouseId);
                    vo.setForwardingAddress(v.getForwardingAddress());
                    if (StringUtil.isNotBlank(v.getForwardingAddress())) {
                        String[] split = v.getForwardingAddress().split("，");
                        if (split.length >= 3) {
                            vo.setForwardingReceiver(split[0]);
                            vo.setForwardingMobile(split[1]);
                            vo.setForwardingAddressDetail(split[2]);
                        }
                    }
                    vo.setForwardingDeliveryNo(v.getForwardingDeliveryNo());
                    vo.setWeight(v.getWeight());
                    vo.setCartonModel(v.getCartonModel());
                    vo.setRegisterTime(v.getCreatedAt());
                    vo.setOperatorId(v.getOperatorId());
                    vo.setOperatorType(v.getOperatorType());
                    vo.setAutoHandle(v.getAutoHandle());
                    vo.setShopNo(v.getShopNo());
                    shopGateway.getByShopNo(v.getShopNo()).ifPresent(shop -> vo.setShopName(shop.getName()));
                    vo.setNeedNotForwardType(v.getNeedNotForwardType().getValue());
                    if (v.getOperatorId() == 0) {
                        vo.setOperatorNick("【系统】");
                    } else {
                        if (v.getOperatorType() == LoginType.EXTERNAL_USER.ordinal()) {
                            final ExternalUser externalUser = externalUserService.getById(v.getOperatorId());
                            if (externalUser != null) {
                                vo.setOperatorNick(externalUser.getName());
                            }
                        } else {
                            final StaffBrief staffBrief = StaffAssembler.INST.toStaffBrief(v.getOperatorId());
                            if (staffBrief != null) {
                                vo.setOperatorNick(staffBrief.getNickname());
                            }
                        }
                    }
                    vo.setRefundOrderNo(refundOrderNoMap.get(v.getOrderNo()));
                    vo.setRefundOrderAmount(refundOrderAmountMap.get(v.getOrderNo()));
                    return vo;
                });
    }
    
    @Override
    public Response importExcel(InputStream inputStream) {
        final List<AfterSalesForwardingRegisterImportExcelVO> rows =
                EasyExcel.read(inputStream)
                        .ignoreEmptyRow(true)
                        .headRowNumber(2)
                        .head(AfterSalesForwardingRegisterImportExcelVO.class)
                        .sheet(0)
                        .doReadSync();
        if (rows.isEmpty()) {
            return Response.buildSuccess();
        }
        for (AfterSalesForwardingRegisterImportExcelVO row : rows) {
            final Set<ConstraintViolation<AfterSalesForwardingRegisterImportExcelVO>> violations =
                    validator.validate(row);
            if (!violations.isEmpty()) {
                throw new ConstraintViolationException(violations);
            }
        }
        for (AfterSalesForwardingRegisterImportExcelVO row : rows) {
            AfterSalesForwardingRegisterFormItemVO formItemVO =
                    AfterSalesForwardingAssembler.INSTANCE.excelToFormItemObj(row);
            formItemVO.setAffectsSalesVouchers(new AffectsSalesVouchers());
            saveInternal(formItemVO, "导入Excel，");
        }
        return Response.buildSuccess();
    }
    
    @Override
    public Response exportExcel(AfterSalesForwardingRegisterQuery query) {
        exportManager.export(
                ExportTaskType.AFTER_SALES_FORWARDING,
                AfterSalesForwardingRegisterExcelVO.class,
                pageIndex -> {
                    query.setPageIndex(pageIndex);
                    query.setPageSize(100);
                    return ResponseFactory.ofPage(
                            query(query), AfterSalesForwardingAssembler.INSTANCE::pageToExcelObj);
                },
                (task, excelWriterBuilder) -> task.setParams(JsonUtil.toJson(query)));
        return Response.buildSuccess();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response noNeedToForward(AfterSalesForwardingRegisterNoNeedToForwardCmd cmd) {
        final AfterSaleForwardingNeedNotForwardTypeEnum needNotForwardTypeEnum =
                IEnum.getEnumOptByValue(
                                AfterSaleForwardingNeedNotForwardTypeEnum.class, cmd.getNeedNotForwardType())
                        .orElseThrow(
                                () -> ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "无需转寄类型不合法"));
        updateStatusBatch(
                cmd.getIds(),
                AfterSaleForwardingRegisterStatusEnum.NO_NEED_TO_FORWARD,
                null,
                e -> {
                    e.setCsRemark(cmd.getCsRemark());
                    e.setNeedNotForwardType(needNotForwardTypeEnum);
                });
        return Response.buildSuccess();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response toForward(AfterSalesForwardingRegisterToForwardCmd cmd) {
        final List<AfterSaleForwardingRegister> afterSaleForwardingRegisters =
                afterSaleForwardingRegisterService.listByIds(cmd.getIds());
        for (AfterSaleForwardingRegister afterSaleForwardingRegister : afterSaleForwardingRegisters) {
            if (StringUtil.isBlank(afterSaleForwardingRegister.getOrderNo())
                    || StringUtil.isBlank(afterSaleForwardingRegister.getStockoutNo())
                    || StringUtil.isBlank(afterSaleForwardingRegister.getForwardingAddress())) {
                throw ExceptionPlusFactory.bizException(
                        ErrorCode.OPERATION_REJECT, "请完善转寄字段（订单编号（原始）、出库单号、转寄地址）信息！");
            }
        }
        updateStatusBatch(cmd.getIds(), AfterSaleForwardingRegisterStatusEnum.TO_BE_FORWARD);
        return Response.buildSuccess();
    }
    
    @Override
    public Response reportAnomalies(AfterSalesForwardingRegisterReportAnomaliesCmd cmd) {
        updateStatusBatch(cmd.getIds(), AfterSaleForwardingRegisterStatusEnum.TO_BE_HANDE, "上报异常，");
        return Response.buildSuccess();
    }
    
    @Override
    public PageResponse<AfterSalesForwardingRegisterPageVO> shareLinkQuery(
            AfterSalesForwardingRegisterShareQuery query) {
        final SingleResponse<AfterSaleShareLink> checkTokenResponse =
                afterSaleShareLinkBizService.checkToken(query.getToken());
        ResponseAssert.assertJust(checkTokenResponse);
        String param = checkTokenResponse.getData().getLinkParams().toString();
        final AfterSalesForwardingRegisterQuery afterSalesForwardingRegisterQuery =
                JSON.parseObject(param, AfterSalesForwardingRegisterQuery.class);
        Assert.notNull(afterSalesForwardingRegisterQuery, "链接参数解析异常");
        afterSalesForwardingRegisterQuery.setPageIndex(query.getPageIndex());
        afterSalesForwardingRegisterQuery.setPageSize(query.getPageSize());
        return query(afterSalesForwardingRegisterQuery);
    }
    
    @Override
    public SingleResponse<String> shareLinkQueryExport(AfterSalesForwardingRegisterShareQuery query) {
        query.setPageSize(9999);
        query.setPageIndex(1);
        final PageResponse<AfterSalesForwardingRegisterPageVO> pageResponse = shareLinkQuery(query);
        Assert.state(pageResponse.isSuccess(), "数据查询异常");
        final List<AfterSalesForwardingRegisterPageVO> data = pageResponse.getData();
        Assert.state(CollUtil.isNotEmpty(data), "没有可以导出的数据");
        
        final List<AfterSalesForwardingRegisterPageSheet> sheetDataList =
                data.stream()
                        .map(
                                val -> {
                                    AfterSalesForwardingRegisterPageSheet sheet =
                                            new AfterSalesForwardingRegisterPageSheet();
                                    sheet.setDeliveryNo(val.getDeliveryNo());
                                    sheet.setRegisterTime(DateUtil.format(val.getRegisterTime()));
                                    sheet.setItemName(val.getItemName());
                                    sheet.setSkuCode(val.getSkuCode());
                                    sheet.setSkuName(val.getSkuName());
                                    sheet.setItemNum(val.getItemNum());
                                    sheet.setIntact(val.getIntact() ? "是" : "否");
                                    sheet.setAbnormalDescription(val.getAbnormalDescription());
                                    sheet.setAffectsSalesVouchers(val.getAffectsSalesVouchers().toString());
                                    sheet.setCsRemark(val.getCsRemark());
                                    final AfterSaleForwardingNeedNotForwardTypeEnum needNotForwardTypeEnum =
                                            IEnum.getEnumByValue(
                                                    AfterSaleForwardingNeedNotForwardTypeEnum.class,
                                                    val.getNeedNotForwardType());
                                    sheet.setNeedNotForwardType(
                                            needNotForwardTypeEnum != null ? needNotForwardTypeEnum.getDesc() : "");
                                    sheet.setOrderNo(val.getOrderNo());
                                    sheet.setStockoutNo(val.getStockoutNo());
                                    sheet.setStockoutWarehouse(val.getStockoutWarehouse());
                                    sheet.setForwardingAddress(val.getForwardingAddress());
                                    sheet.setForwardingReceiver(val.getForwardingReceiver());
                                    sheet.setForwardingMobile(val.getForwardingMobile());
                                    sheet.setForwardingAddressDetail(val.getForwardingAddressDetail());
                                    sheet.setForwardingDeliveryNo(val.getForwardingDeliveryNo());
                                    sheet.setWeight(val.getWeight());
                                    sheet.setCartonModel(val.getCartonModel());
                                    return sheet;
                                })
                        .collect(Collectors.toList());
        
        File file = null;
        try {
            file = new File(System.currentTimeMillis() + ".xlsx");
            EasyExcel.write(file, AfterSalesForwardingRegisterPageSheet.class)
                    .sheet("售后物流转寄")
                    .doWrite(sheetDataList);
            
            UploadFileAction action = UploadFileAction.ofFile(file);
            FileStub fileStub = fileGateway.uploadFile(action);
            com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.File sysFile =
                    iFileService.getById(fileStub.getFileId());
            String url = ossGateway.generatePresignedUrl(true, sysFile.getPath());
            return SingleResponse.of(url);
        } catch (Exception e) {
            log.error("导出售后物流转寄异常", e);
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "导出售后物流转寄失败");
        } finally {
            FileUtil.del(file);
        }
    }
    
    private void updateStatusBatch(List<Long> ids, AfterSaleForwardingRegisterStatusEnum statusEnum) {
        updateStatusBatch(ids, statusEnum, null, null);
    }
    
    private void updateStatusBatch(
            List<Long> ids, AfterSaleForwardingRegisterStatusEnum statusEnum, String logPrefix) {
        updateStatusBatch(ids, statusEnum, logPrefix, null);
    }
    
    private void updateStatusBatch(
            List<Long> ids,
            AfterSaleForwardingRegisterStatusEnum statusEnum,
            String logPrefix,
            Consumer<AfterSaleForwardingRegister> entityHook) {
        final List<AfterSaleForwardingRegister> afterSaleForwardingRegisters =
                afterSaleForwardingRegisterService.listByIds(ids);
        for (AfterSaleForwardingRegister afterSaleForwardingRegister : afterSaleForwardingRegisters) {
            final AfterSaleForwardingRegister entity = new AfterSaleForwardingRegister();
            entity.setId(afterSaleForwardingRegister.getId());
            entity.setStatus(statusEnum);
            entity.setOperatorId(CompositeUserContext.getUserId());
            entity.setOperatorType(CompositeUserContext.getLoginType().ordinal());
            if (entityHook != null) {
                entityHook.accept(entity);
            }
            operateLogDomainService.addOperatorLog(
                    CompositeUserContext.getLoginType(),
                    CompositeUserContext.getUserId(),
                    OperateLogTarget.AFTER_SALE_FORWARDING,
                    afterSaleForwardingRegister.getDeliveryNo(),
                    String.format(
                            "%s商品 %s %s 标记为[%s]",
                            Optional.ofNullable(logPrefix).orElse(""),
                            afterSaleForwardingRegister.getSkuCode(),
                            afterSaleForwardingRegister.getItemName(),
                            statusEnum.getDesc()),
                    MapBuilder.create().put("id", afterSaleForwardingRegister.getId()).build());
            afterSaleForwardingRegisterService.updateById(entity);
        }
    }
    
    @Override
    public Response delete(Long id) {
        afterSaleForwardingRegisterService
                .lambdaUpdate()
                .eq(AfterSaleForwardingRegister::getId, id)
                .remove();
        return Response.buildSuccess();
    }
    
    @Override
    @XxlJob(value = "afterSalesForwardingRegister::autoHandle")
    @XxlJobAutoRegister(cron = "0 0 2 * * ? *", author = "徵乌", jobDesc = "售后物流转寄自动处理")
    public Response autoHandle() {
        long id = 0;
        while (true) {
            final List<AfterSaleForwardingRegister> list =
                    afterSaleForwardingRegisterService
                            .lambdaQuery()
                            .eq(
                                    AfterSaleForwardingRegister::getStatus,
                                    AfterSaleForwardingRegisterStatusEnum.TO_BE_HANDE)
                            .gt(AfterSaleForwardingRegister::getId, id)
                            .orderByAsc(AfterSaleForwardingRegister::getId)
                            .last(MPUtil.limit(100))
                            .list();
            if (list.isEmpty()) {
                break;
            }
            id = list.get(list.size() - 1).getId();
            autoHandleRegisterList(list);
        }
        log.info("[售后物流转寄][自动处理] 完成");
        return Response.buildSuccess();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response fixData(FixDataRequest request) {
        for (String deliveryNo : request.getDeliveryNos()) {
            try {
                final AfterSalesForwardingRegisterCompletionCmd cmd =
                        new AfterSalesForwardingRegisterCompletionCmd();
                cmd.setDeliveryNo(deliveryNo);
                
                final MultiResponse<AfterSalesForwardingRegisterFormItemVO> completionResponse =
                        completion(cmd);
                ResponseAssert.assertJust(completionResponse);
                final List<AfterSalesForwardingRegisterFormItemVO> completionResponseData =
                        completionResponse.getData();
                
                final List<AfterSaleForwardingRegister> list =
                        afterSaleForwardingRegisterService
                                .lambdaQuery()
                                .eq(AfterSaleForwardingRegister::getDeliveryNo, deliveryNo)
                                .list();
                
                if (list.isEmpty()) {
                    for (AfterSalesForwardingRegisterFormItemVO datum : completionResponseData) {
                        datum.setAbnormalDescription("系统补偿");
                        saveInternal(datum, "【系统补偿】");
                    }
                } else {
                    if (list.size() == 1 && completionResponseData.size() == 1) {
                        final AfterSalesForwardingRegisterFormItemVO completionItemVO =
                                completionResponseData.get(0);
                        final AfterSaleForwardingRegister po = list.get(0);
                        
                        po.setOrderNo(completionItemVO.getOrderNo());
                        po.setSkuCode(completionItemVO.getSkuCode());
                        po.setSkuName(completionItemVO.getSkuName());
                        po.setOrderNo(completionItemVO.getOrderNo());
                        po.setStockoutNo(completionItemVO.getStockoutNo());
                        po.setStockoutWarehouse(completionItemVO.getStockoutWarehouse());
                        po.setStockoutWarehouseNo(completionItemVO.getStockoutWarehouseNo());
                        po.setForwardingDeliveryNo(completionItemVO.getForwardingDeliveryNo());
                        final AfterSalesForwardingRegisterFormItemVO formItemVO =
                                AfterSalesForwardingAssembler.INSTANCE.afterSaleForwardingRegisterPoToFormItemVo(
                                        po);
                        saveInternal(formItemVO, "【系统补偿】");
                    } else {
                        for (AfterSaleForwardingRegister afterSaleForwardingRegister : list) {
                            if (afterSaleForwardingRegister.getAbnormalDescription() != null
                                    &&
                                    !afterSaleForwardingRegister.getAbnormalDescription().contains("【异常状态标记】")) {
                                final AfterSaleForwardingRegister updateObj = new AfterSaleForwardingRegister();
                                updateObj.setId(afterSaleForwardingRegister.getId());
                                updateObj.setAbnormalDescription(
                                        afterSaleForwardingRegister.getAbnormalDescription() + "【异常状态标记】");
                                afterSaleForwardingRegisterService.updateById(updateObj);
                            }
                        }
                        for (AfterSalesForwardingRegisterFormItemVO datum : completionResponseData) {
                            datum.setAbnormalDescription("系统补偿");
                            saveInternal(datum, "【系统补偿】");
                        }
                    }
                }
            } catch (Exception e) {
                log.error("[售后物流转寄][数据修复] 修复异常 deliveryNo:{}", deliveryNo, e);
            }
        }
        return Response.buildSuccess();
    }
    
    private void autoHandle(Collection<String> deliveryNoList) {
        if (CollectionUtil.isEmpty(deliveryNoList)) {
            return;
        }
        final List<AfterSaleForwardingRegister> list =
                afterSaleForwardingRegisterService
                        .lambdaQuery()
                        .in(AfterSaleForwardingRegister::getDeliveryNo, deliveryNoList)
                        .list();
        autoHandleRegisterList(list);
    }
    
    private void autoHandleRegisterList(List<AfterSaleForwardingRegister> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        final Map<String, List<AfterSaleForwardingRegister>> group =
                list.stream().collect(Collectors.groupingBy(AfterSaleForwardingRegister::getDeliveryNo));
        group.forEach(this::autoHandle);
    }
    
    private void autoHandle(String deliveryNo, List<AfterSaleForwardingRegister> registers) {
        for (AfterSaleForwardingRegister register : registers) {
            if (register.getStatus() != AfterSaleForwardingRegisterStatusEnum.TO_BE_HANDE) {
                continue;
            }
            String stockoutWarehouseNo = register.getStockoutWarehouseNo();
            if (StringUtil.isBlank(stockoutWarehouseNo)) {
                operateLogDomainService.addOperatorLog(LoginType.DEFAULT, 0L, OperateLogTarget.AFTER_SALE_FORWARDING,
                        deliveryNo, "[售后物流转寄][自动处理]没有出库单号，跳过自动处理", null);
                continue;
            }
            final boolean hasNonIntact = registers.stream().anyMatch(v -> !v.getIntact());
            if (hasNonIntact) {
                operateLogDomainService.addOperatorLog(LoginType.DEFAULT, 0L, OperateLogTarget.AFTER_SALE_FORWARDING,
                        deliveryNo, "[售后物流转寄][自动处理]有破损商品，跳过自动处理", null);
                continue;
            }
            final List<RefundOrderWithDetails> refundOrderWithDetailsList =
                    getWdtRefundOrderDetails(deliveryNo);
            if (refundOrderWithDetailsList.isEmpty()) {
                operateLogDomainService.addOperatorLog(LoginType.DEFAULT, 0L, OperateLogTarget.AFTER_SALE_FORWARDING,
                        deliveryNo, "[售后物流转寄][自动处理]未找到售后单，跳过自动处理", null);
                continue;
            }
            WdtRefundOrder refundOrder = null;
            WdtRefundOrderDetail refundOrderDetail = null;
            for (RefundOrderWithDetails refundOrderWithDetails : refundOrderWithDetailsList) {
                for (WdtRefundOrderDetail detail : refundOrderWithDetails.getDetails()) {
                    if (detail.getSpecNo().equals(register.getSkuCode())) {
                        refundOrder = refundOrderWithDetails.getRefundOrder();
                        refundOrderDetail = detail;
                        break;
                    }
                }
            }
            if (refundOrderDetail == null) {
                operateLogDomainService.addOperatorLog(LoginType.DEFAULT, 0L, OperateLogTarget.AFTER_SALE_FORWARDING,
                        deliveryNo,
                        String.format("[售后物流转寄][自动处理]未找到包含该商品SKU[%s]的售后单，跳过自动处理",
                                register.getSkuCode()), null);
                continue;
            }
            String refundNo = refundOrder.getRefundNo();
            if (refundOrderDetail.getRefundNum().intValue() != register.getItemNum()) {
                operateLogDomainService.addOperatorLog(LoginType.DEFAULT, 0L, OperateLogTarget.AFTER_SALE_FORWARDING,
                        deliveryNo,
                        String.format("[售后物流转寄][自动处理]与售后单[%s]商品SKU[%s]退款数量不一致，跳过自动处理",
                                refundNo, register.getSkuCode()), null);
                continue;
            }
            final String skuCode = register.getSkuCode();
            final ItemSku itemSku = itemSkuService.getByMixCode(skuCode).orElse(null);

            // 检查是否为指定的自有仓库，如果是则设置为无需转寄-自有仓库
            final Optional<Warehouse> warehouseOptional = warehouseGateway.getWarehouse(stockoutWarehouseNo);
            if (warehouseOptional.isPresent()) {
                final String warehouseName = warehouseOptional.get().getName();
                if (isOwnWarehouse(warehouseName)) {
                    register.setStatus(AfterSaleForwardingRegisterStatusEnum.NO_NEED_TO_FORWARD);
                    register.setNeedNotForwardType(AfterSaleForwardingNeedNotForwardTypeEnum.OWN_WAREHOUSE);
                    register.setAutoHandle(true);
                    afterSaleForwardingRegisterService.updateById(register);
                    operateLogDomainService.addOperatorLog(LoginType.DEFAULT, 0L, OperateLogTarget.AFTER_SALE_FORWARDING,
                            deliveryNo,
                            String.format("[售后物流转寄][自动处理] 出库仓库[%s]为自有仓库，无需转寄", warehouseName), null);
                    continue;
                }
            }

            final List<WarehouseAfterSalesAddressVO> warehouseAfterSalesAddressList =
                    afterSalesWarehouseBizService.getWarehouseAfterSalesAddressList(stockoutWarehouseNo);
            if (warehouseAfterSalesAddressList.isEmpty()) {
                operateLogDomainService.addOperatorLog(LoginType.DEFAULT, 0L, OperateLogTarget.AFTER_SALE_FORWARDING,
                        deliveryNo, String.format("[售后物流转寄][自动处理] 出库仓库[%s] 未找到仓库地址，跳过自动处理",
                                stockoutWarehouseNo), null);
                continue;
            }
            if (warehouseAfterSalesAddressList.size() > 1) {
                operateLogDomainService.addOperatorLog(LoginType.DEFAULT, 0L, OperateLogTarget.AFTER_SALE_FORWARDING,
                        deliveryNo, String.format("[售后物流转寄][自动处理] 出库仓库[%s] 存在多个仓库地址，跳过自动处理",
                                stockoutWarehouseNo), null);
                continue;
            }
            if (itemSku != null && itemSku.getGoodsType() != null
                    && Arrays.asList(
                            GoodsType.FRESH,
                            GoodsType.APPLIANCE,
                            GoodsType.FURNITURE,
                            GoodsType.INSTALLATION_REQUIRED,
                            GoodsType.SHORT_GUARANTEE,
                            GoodsType.NO_INTERCEPT)
                    .contains(itemSku.getGoodsType())) {
                register.setStatus(AfterSaleForwardingRegisterStatusEnum.NO_NEED_TO_FORWARD);
                register.setNeedNotForwardType(AfterSaleForwardingNeedNotForwardTypeEnum.LARGE_CARGO);
                register.setAutoHandle(true);
                afterSaleForwardingRegisterService.updateById(register);
                operateLogDomainService.addOperatorLog(LoginType.DEFAULT, 0L, OperateLogTarget.AFTER_SALE_FORWARDING,
                        deliveryNo,
                        String.format("[售后物流转寄][自动处理] 商品SKU[%s] 商品类型为[%s]，无需转寄", skuCode,
                                itemSku.getGoodsType().getDesc()), null);
                continue;
            }
            BigDecimal refundAmount = refundOrderDetail.getRefundAmount();
            //订单退款金额*20%-转寄运费的差值【默认转寄运费每单5元】，当差值低于5元的时候系统不做推送转寄地址和转寄处理
            //refundAmount * 0.2 - 5 < 5 === refundAmount * 0.2 < 10 === refundAmount < 50
            if (refundAmount != null
                    && refundAmount.compareTo(BigDecimal.ZERO) > 0
                    && refundAmount.compareTo(new BigDecimal(50)) < 0) {
                register.setStatus(AfterSaleForwardingRegisterStatusEnum.NO_NEED_TO_FORWARD);
                register.setNeedNotForwardType(AfterSaleForwardingNeedNotForwardTypeEnum.AMOUNT_LT5);
                register.setAutoHandle(true);
                afterSaleForwardingRegisterService.updateById(register);
                operateLogDomainService.addOperatorLog(LoginType.DEFAULT, 0L, OperateLogTarget.AFTER_SALE_FORWARDING,
                        deliveryNo, String.format(
                                "[售后物流转寄][自动处理] 商品SKU[%s] 退款金额（%s） * 20%% - 转寄运费的差值（默认5元）< 5元，无需转寄",
                                skuCode, refundAmount), null);
                continue;
            }
            final WarehouseAfterSalesAddressVO addressVO = warehouseAfterSalesAddressList.get(0);
            register.setForwardingAddress(
                    String.format(
                            "%s，%s， %s %s",
                            addressVO.getContacts(),
                            addressVO.getTel(),
                            addressVO.getAddressCodeStr(),
                            addressVO.getFullAddress()));
            register.setStatus(AfterSaleForwardingRegisterStatusEnum.TO_BE_FORWARD);
            register.setAutoHandle(true);
            afterSaleForwardingRegisterService.updateById(register);
            operateLogDomainService.addOperatorLog(LoginType.DEFAULT, 0L, OperateLogTarget.AFTER_SALE_FORWARDING,
                    deliveryNo,
                    String.format("[售后物流转寄][自动处理]商品[%s]自动填写转寄地址，标记为待转寄",
                            register.getSkuCode()),
                    MapBuilder.create().put("id", register.getId()).build());
        }
        log.info("[售后物流转寄][自动处理] 包裹[{}]处理完成", deliveryNo);
    }

    /**
     * 判断是否为自有仓库
     * 自有仓库包括：上海磐石云仓WMS仓、绿色家装杭州顺丰仓、绿色家装杭州顺丰冷库仓
     *
     * @param warehouseName 仓库名称
     * @return 是否为自有仓库
     */
    private boolean isOwnWarehouse(String warehouseName) {
        if (StringUtil.isBlank(warehouseName)) {
            return false;
        }

        // 定义自有仓库名称列表
        final List<String> ownWarehouses = Arrays.asList(
                "上海磐石云仓WMS仓",
                "绿色家装杭州顺丰仓",
                "绿色家装杭州顺丰冷库仓"
        );

        return ownWarehouses.contains(warehouseName);
    }
}
