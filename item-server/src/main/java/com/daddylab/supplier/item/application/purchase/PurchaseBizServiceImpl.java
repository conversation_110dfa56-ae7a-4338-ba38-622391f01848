package com.daddylab.supplier.item.application.purchase;

import static com.daddylab.supplier.item.application.purchase.PurchaseConst.MONTH_PATTERN;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.application.provider.ProviderBizService;
import com.daddylab.supplier.item.application.purchase.purchaseDissent.PurchaseDissentBizService;
import com.daddylab.supplier.item.application.purchase.purchaseTable.PurchaseTableBizService;
import com.daddylab.supplier.item.application.shortlink.ShortLinkService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.trans.PurchaseTransMapper;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.item.dto.CorpBizListDto;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseCmd;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseQueryPage;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseVo;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.exportTask.ExportDomainService;
import com.daddylab.supplier.item.domain.exportTask.ExportEntity;
import com.daddylab.supplier.item.domain.exportTask.ExportTaskGateway;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseDetail;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseItem;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseOperate;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseSheet;
import com.daddylab.supplier.item.domain.purchase.enums.PurchaseConfirm;
import com.daddylab.supplier.item.domain.purchase.enums.PurchaseStatus;
import com.daddylab.supplier.item.domain.purchase.gateway.PurchaseGateway;
import com.daddylab.supplier.item.domain.purchase.service.PurchaseDomainService;
import com.daddylab.supplier.item.domain.smsAuth.SmsAuthService;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.functions.Function4;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffListQuery;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * <AUTHOR> @ClassName PurchaseBizServiceImpl.java
 * @description 采购确认
 * @createTime 2021年11月11日 14:54:00
 */
@Slf4j
@Service
public class PurchaseBizServiceImpl implements PurchaseBizService {

  @Autowired PartnerFeignClient partnerFeignClient;
  @Autowired ProviderGateway providerGateway;
  @Autowired ExportDomainService exportDomainService;
  @Autowired private PurchaseDomainService purchaseDomainService;
  @Autowired private IPurchaseService iPurchaseService;
  @Autowired private PurchaseGateway purchaseGateway;
  @Autowired private ProviderBizService providerBizService;
  @Inject private ExportTaskGateway exportTaskGateway;
  @Inject private FileGateway fileGateway;

  @Value("${files.import-purchase-template}")
  private String excelTemplateUrl;

  @Inject private OperateLogDomainService operateLogDomainService;
  @Autowired private PurchaseTableBizService purchaseTableBizService;

  @Value("${router.path}")
  private String routerPath;

  @Autowired private SmsAuthService smsAuthService;
  @Autowired private ShortLinkService shortLinkService;
  @Autowired private ItemGateway itemGateway;
  @Autowired private PurchaseDissentBizService purchaseDissentBizService;

  @Autowired
  @Qualifier("UserGatewayCacheImpl")
  private UserGateway userGateway;

  @Autowired private ItemSkuGateway itemSkuGateway;
  @Autowired private PurchaseMapper purchaseMapper;
  @Autowired private ICombinationItemService iCombinationItemService;
  @Autowired private IItemSkuService iItemSkuService;
  @Autowired private IItemService iItemService;

  @Autowired private IBizLevelDivisionService iBizLevelDivisionService;

  @Override
  public PageResponse<PurchaseVo> queryPage(PurchaseQueryPage purchaseQueryPage) {
    purchaseQueryPage.setCurrentUserId(UserContext.getUserId());
    boolean showAll = UserContext.hasPermission("/purchase/viewList/all");
    purchaseQueryPage.setShowAll(showAll);

    //        QueryWrapper<Purchase> queryWrapper = setParams(purchaseQueryPage);
    //        Page<Purchase> pageReq = new Page<>(purchaseQueryPage.getPageIndex(),
    //                purchaseQueryPage.getPageSize());
    //        Page<Purchase> page = iPurchaseService.page(pageReq, queryWrapper);

    final PageInfo<PurchaseVo> pageInfo =
        PageHelper.startPage(purchaseQueryPage.getPageIndex(), purchaseQueryPage.getPageSize())
            .doSelectPageInfo(() -> purchaseMapper.page(purchaseQueryPage));

    if (CollectionUtils.isEmpty(pageInfo.getList())) {
      return PageResponse.of(
          Collections.emptyList(),
          0,
          purchaseQueryPage.getPageSize(),
          purchaseQueryPage.getPageIndex());
    }

    // 合作方/业务层级处理
    final Set<String> itemSkuSet =
        pageInfo.getList().stream()
            .map(PurchaseVo::getItemSku)
            .filter(val -> !val.startsWith("MU"))
            .collect(Collectors.toSet());
    final Map<String, List<CorpBizTypeDTO>> skuDivsionMap =
        iBizLevelDivisionService.queryBySkuCode(itemSkuSet);
    final Set<String> combinationCodeSet =
        pageInfo.getList().stream()
            .map(PurchaseVo::getItemSku)
            .filter(val -> val.startsWith("MU"))
            .collect(Collectors.toSet());
    final Map<String, CorpBizListDto> combinationDivisonMap =
        iBizLevelDivisionService.queryByCombinationCode(combinationCodeSet);

    pageInfo
        .getList()
        .forEach(
            purchaseVo -> {
              // 查询采购人员
              StaffInfo staffInfo = userGateway.queryStaffInfoById(purchaseVo.getBuyerId());
              if (Objects.nonNull(staffInfo)) {
                purchaseVo.setBuyerName(staffInfo.getNickname());
              } else {
                purchaseVo.setBuyerName("");
              }

              // 返回短链
              String path = shortLinkService.generate(routerPath + "?code=" + purchaseVo.getMd5());
              purchaseVo.setShortLink(path);

              // 合作方/业务层级处理
              if (purchaseVo.getItemSku().startsWith("MU")) {
                final CorpBizListDto corpBizListDto =
                    combinationDivisonMap.get(purchaseVo.getItemSku());
                if (Objects.nonNull(corpBizListDto)) {
                  purchaseVo.setCorpType(corpBizListDto.getCorpType());
                  purchaseVo.setBizType(corpBizListDto.getBizType());
                }
              } else {
                final List<CorpBizTypeDTO> resList = skuDivsionMap.get(purchaseVo.getItemSku());
                if (CollectionUtils.isNotEmpty(resList)) {
                  purchaseVo.setCorpType(
                      resList.stream()
                          .map(CorpBizTypeDTO::getCorpType)
                          .collect(Collectors.toList()));
                  purchaseVo.setBizType(
                      resList.stream()
                          .map(CorpBizTypeDTO::getBizType)
                          .flatMap(List::stream)
                          .collect(Collectors.toList()));
                  purchaseVo.setCorpBizType(resList);
                }
              }
            });

    return PageResponse.of(
        pageInfo.getList(),
        (int) pageInfo.getTotal(),
        purchaseQueryPage.getPageSize(),
        purchaseQueryPage.getPageIndex());
  }

  @Override
  public Response delete(Long id) {
    purchaseDomainService.deletePurchase(id);
    return Response.buildSuccess();
  }

  @Override
  @Transactional
  public Response createOrUpdatePurchase(PurchaseCmd cmd) {
    Purchase purchase = purchaseDomainService.updatePurchase(cmd);

    //        Integer businessLineByCode = getBusinessLineByCode(purchase.getItemSku());
    //        purchase.setBusinessLine(businessLineByCode);
    iPurchaseService.updateById(purchase);

    return Response.buildSuccess();
  }

  @Override
  @Transactional
  public Response doSort(List<Long> ids) {
    List<Purchase> purchases =
        iPurchaseService
            .lambdaQuery()
            .select(Purchase::getId, Purchase::getSort)
            .in(Purchase::getId, ids)
            .orderByDesc(Purchase::getSort)
            .orderByDesc(Purchase::getId)
            .list();
    if (ids.size() != purchases.size()) {
      return Response.buildFailure(
          ErrorCode.DATA_NOT_FOUND.getCode(), "未找到指定采购记录，记录可能发生变更，请刷新页面后重试");
    }

    final Map<Long, Long> sortMap =
        purchases.stream().collect(Collectors.toMap(Purchase::getId, Purchase::getSort));
    final Map<Long, Purchase> purchaseMap =
        purchases.stream().collect(Collectors.toMap(Purchase::getId, Function.identity()));

    for (int i = 0; i < ids.size(); i++) {
      Long id = ids.get(i);
      Purchase purchase = purchaseMap.get(id);
      Purchase purchaseThisPos = purchases.get(i);
      Long toSort = sortMap.get(purchaseThisPos.getId());
      purchase.setSort(toSort);
    }

    iPurchaseService.updateBatchById(purchases);
    return Response.buildSuccess();
  }

  @Override
  public MultiResponse<PurchaseDetail> getPurchaseDetail(Long purchaseId) {
    long now = System.currentTimeMillis();
    ArrayList<Purchase> purchases = new ArrayList<>();
    ArrayList<PurchaseDetail> purchaseDetails = new ArrayList<>();
    Assert.notNull(purchaseId, "采购记录id入参不得为空");
    Purchase purchase = iPurchaseService.getById(purchaseId);
    if (Objects.isNull(purchase)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查不到该记录");
    }
    purchases.add(purchase);
    QueryWrapper<Purchase> queryWrapper = new QueryWrapper<>();
    queryWrapper
        .lambda()
        .ne(Purchase::getId, purchase.getId())
        .eq(Purchase::getMonth, purchase.getMonth())
        .eq(Purchase::getStatus, PurchaseStatus.NO_CONFIRM)
        .eq(
            StringUtils.isNotBlank(purchase.getProvider()),
            Purchase::getProvider,
            purchase.getProvider());
    List<Purchase> list = iPurchaseService.list(queryWrapper);
    if (CollectionUtils.isNotEmpty(list)) {
      purchases.addAll(list);
    }
    for (Purchase purchaseData : purchases) {
      PurchaseDetail purchaseDetail = PurchaseTransMapper.INSTANCE.DetailToDo(purchaseData);
      String starTime = DateUtil.format(purchaseDetail.getStartTime());
      String endTime = DateUtil.format(purchaseDetail.getEndTime());
      purchaseDetail.setActiveTime(starTime + "~" + endTime);
      purchaseDetails.add(purchaseDetail);
    }
    return MultiResponse.of(purchaseDetails);
  }

  @Override
  public MultiResponse<PurchaseDetail> getStayConfirm(String idStr) {

    String[] ids = idStr.split(",");
    ArrayList<PurchaseDetail> purchaseDetails = new ArrayList<>();
    for (String id : ids) {
      Assert.notNull(id, "采购记录id入参不得为空");
      Purchase purchaseData = iPurchaseService.getById(Long.parseLong(id));
      if (Objects.isNull(purchaseData)) {
        throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查不到该记录");
      }
      PurchaseDetail purchaseDetail = PurchaseTransMapper.INSTANCE.DetailToDo(purchaseData);
      String starTime = DateUtil.format(purchaseDetail.getStartTime());
      String endTime = DateUtil.format(purchaseDetail.getEndTime());
      purchaseDetail.setActiveTime(starTime + "~" + endTime);
      purchaseDetails.add(purchaseDetail);
    }
    return MultiResponse.of(purchaseDetails);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Response initiate(List<Long> ids) {
    Purchase purchaseData = iPurchaseService.getById(ids.get(0));
    if (Objects.isNull(purchaseData)) {
      throw ExceptionPlusFactory.bizException(
          ErrorCode.DATA_NOT_FOUND, "未查询到ID为" + ids.get(0) + "的采购价格记录");
    }
    String encrypt = purchaseData.getMd5();
    for (Long id : ids) {
      Purchase purchase = iPurchaseService.getById(id);
      if (Objects.isNull(purchase)) {
        throw ExceptionPlusFactory.bizException(
            ErrorCode.DATA_NOT_FOUND, "未查询到ID为" + id + "的采购价格记录");
      }
      // 采购记录修改为已发起确认
      purchase.setIsConfirm(PurchaseConfirm.NO_Edit.getValue());
      purchase.setMd5(encrypt);
      purchase.setStatus(PurchaseStatus.NO_CONFIRM);
      iPurchaseService.updateById(purchase);
    }
    return Response.buildSuccess();
  }

  @Override
  public SingleResponse<PurchaseDetail> getAccess(Long id) {
    Assert.notNull(id, "采购记录id入参不得为空");
    Purchase purchase = iPurchaseService.getById(id);
    if (Objects.isNull(purchase)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查不到该记录");
    }
    PurchaseDetail purchaseDetail = PurchaseTransMapper.INSTANCE.DetailToDo(purchase);
    String path = shortLinkService.generate(routerPath + "?code=" + purchase.getMd5());
    purchaseDetail.setPath(path);

    String starTime = DateUtil.format(purchaseDetail.getStartTime());
    String endTime = DateUtil.format(purchaseDetail.getEndTime());

    purchaseDetail.setActiveTime(starTime + "~" + endTime);
    MultiResponse<com.daddylab.supplier.item.controller.purchaseDissent.dto.PurchaseDissentVo>
        dissent = purchaseDissentBizService.getDissent(id);
    purchaseDetail.setPurchaseDissentVoList(dissent.getData());

    return SingleResponse.of(purchaseDetail);
  }

  @Override
  public MultiResponse<PurchaseDetail> getDetailByCode(String code, String token) {
    QueryWrapper<Purchase> queryWrapper = new QueryWrapper<>();
    queryWrapper.lambda().eq(Purchase::getMd5, code).eq(Purchase::getToken, token);
    List<Purchase> list = iPurchaseService.list(queryWrapper);
    if (CollectionUtils.isNotEmpty(list)) {
      List<PurchaseDetail> purchaseDetails = PurchaseTransMapper.INSTANCE.DetailToDos(list);
      for (PurchaseDetail purchaseDetail : purchaseDetails) {
        String starTime = DateUtil.format(purchaseDetail.getStartTime());
        String endTime = DateUtil.format(purchaseDetail.getEndTime());
        purchaseDetail.setActiveTime(starTime + "~" + endTime);
        MultiResponse<com.daddylab.supplier.item.controller.purchaseDissent.dto.PurchaseDissentVo>
            dissent = purchaseDissentBizService.getDissent(purchaseDetail.getId());
        purchaseDetail.setPurchaseDissentVoList(dissent.getData());
      }
      return MultiResponse.of(purchaseDetails);
    }
    return MultiResponse.of(new ArrayList<>());
  }

  @Override
  public Response reset(Long id) {
    purchaseDomainService.resetPurchase(id);
    return Response.buildSuccess();
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public long exportExcel(PurchaseQueryPage purchaseQueryPage) {
    // 合作方权限获取改造。fixme
    //
    // purchaseQueryPage.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(purchaseQueryPage.getBusinessLine()));
    //        Assert.isTrue(CollUtil.isNotEmpty(purchaseQueryPage.getBusinessLine()), "无数据权限，无法导出");

    //        Integer exportTotal = iPurchaseService.countExport(purchaseQueryPage);
    //        Assert.isTrue(exportTotal > 0, "查询数量为0，无法导出");

    purchaseQueryPage.setPageSize(9999);
    final PageResponse<PurchaseVo> purchaseVoPageResponse = queryPage(purchaseQueryPage);
    int exportTotal = purchaseVoPageResponse.getTotalCount();
    Assert.isTrue(exportTotal > 0, "查询数量为0，无法导出");

    purchaseQueryPage.setCurrentUserId(UserContext.getUserId());
    boolean showAll = UserContext.hasPermission(GlobalConstant.PURCHASE_SHOW_ALL_URI);
    purchaseQueryPage.setShowAll(showAll);

    // 初始化一条导出任务
    final ExportTask exportTask = ExportEntity.buildEntity(ExportTaskType.PURCHASE);
    exportTask.setId(exportTaskGateway.saveOrUpdateExportTask(exportTask));

    ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL)
        .execute(
            () -> {
              try {
                File excelFile = new File(exportTask.getName() + ".xlsx");
                final List<PurchaseSheet> rList =
                    purchaseVoPageResponse.getData().stream()
                        .map(
                            val -> {
                              PurchaseSheet purchaseSheet = new PurchaseSheet();
                              purchaseSheet.setMonth(val.getMonth());
                              String corpTypeStr = "";
                              if (val.getItemSku().startsWith("MU")) {
                                if (CollUtil.isNotEmpty(val.getCorpType())) {
                                  corpTypeStr =
                                      val.getCorpType().stream()
                                          .map(
                                              v ->
                                                  IEnum.getEnumByValue(
                                                          DivisionLevelValueEnum.class, v)
                                                      .getDesc())
                                          .collect(Collectors.joining(","));
                                }
                              } else {
                                if (CollUtil.isNotEmpty(val.getCorpBizType())) {
                                  corpTypeStr =
                                      val.getCorpBizType().stream()
                                          .map(
                                              v ->
                                                  IEnum.getEnumByValue(
                                                          DivisionLevelValueEnum.class,
                                                          v.getCorpType())
                                                      .getDesc())
                                          .collect(Collectors.joining(","));
                                }
                              }

                              purchaseSheet.setCoryType(corpTypeStr);
                              purchaseSheet.setItemSku(val.getItemSku());
                              purchaseSheet.setItemName(val.getItemName());
                              purchaseSheet.setItemSpecs(val.getItemSpecs());
                              purchaseSheet.setSpecsCount(val.getSpecsCount());
                              purchaseSheet.setPurchaseArea(
                                  0 == val.getPurchaseArea() ? "工厂" : "仓库");
                              purchaseSheet.setProvider(val.getProvider());
                              purchaseSheet.setBuyerName(val.getBuyerName());
                              purchaseSheet.setUsualPrice(
                                  Objects.isNull(val.getUsualPrice())
                                      ? ""
                                      : val.getUsualPrice().toString());
                              purchaseSheet.setIsActive(1 == val.getIsActive() ? "是" : "否");
                              // 0:货抵款 1:按时间供价 2:当月优惠件数
                              purchaseSheet.setFavourableType(
                                  0 == val.getFavourableType()
                                      ? "抵货款"
                                      : (1 == val.getFavourableType() ? "按时间供价" : "当月优惠件数"));
                              purchaseSheet.setPlatformType(
                                  IEnum.getEnumByValue(Platform.class, val.getPlatformType())
                                      .getDesc());
                              // 0:大促 1:直播 2:无
                              purchaseSheet.setActiveType(val.getActiveType() == 0 ? "大促" : "直播");
                              purchaseSheet.setStartTime(DateUtil.format(val.getStartTime()));
                              purchaseSheet.setEndTime(DateUtil.format(val.getEndTime()));
                              purchaseSheet.setOrderCount(val.getOrderCount());
                              purchaseSheet.setFinalCount(val.getFinalCount());
                              purchaseSheet.setPriceCost(
                                  Objects.isNull(val.getPriceCost())
                                      ? ""
                                      : val.getPriceCost().toString());
                              purchaseSheet.setNumCost(
                                  Objects.isNull(val.getNumCost())
                                      ? ""
                                      : val.getNumCost().toString());
                              purchaseSheet.setContent(val.getContent());
                              purchaseSheet.setMark(val.getMark());
                              // 0:待确认 1:已确认 2:存在异议
                              purchaseSheet.setStatus(
                                  val.getStatus() == 0
                                      ? "待确认"
                                      : (1 == val.getStatus() ? "已确认" : "存在异议"));
                              return purchaseSheet;
                            })
                        .collect(Collectors.toList());
                EasyExcel.write(excelFile, PurchaseSheet.class)
                        .sheet("Sheet1")
                        .doWrite(rList);

                final String ossUrl =
                    SpringUtil.getBean(ExportDomainService.class).uploadFile(excelFile);
                exportTask.setStatus(ExportTaskStatus.SUCCESS);
                exportTask.setDownloadUrl(ossUrl);
              } catch (Exception e) {
                log.error("导出采购异常", e);
                exportTask.setStatus(ExportTaskStatus.FAIL);
                exportTask.setError(e.getMessage());
              } finally {
                exportTaskGateway.saveOrUpdateExportTask(exportTask);
              }
            });
    return exportTask.getId();
  }

  @Deprecated
  //    private QueryWrapper<Purchase> setParams(PurchaseQueryPage purchaseQueryPage) {
  //        QueryWrapper<Purchase> queryWrapper = new QueryWrapper<>();
  //        queryWrapper.lambda()
  //                .like(StringUtils.isNotBlank(purchaseQueryPage.getItemName()),
  //                        Purchase::getItemName, purchaseQueryPage.getItemName())
  //                .eq(StringUtils.isNotBlank(purchaseQueryPage.getItemSku()),
  // Purchase::getItemSku,
  //                        purchaseQueryPage.getItemSku())
  //                .like(StringUtils.isNotBlank(purchaseQueryPage.getProvider()),
  //                        Purchase::getProvider, purchaseQueryPage.getProvider())
  //                .eq(Objects.nonNull(purchaseQueryPage.getFavourableType()),
  //                        Purchase::getFavourableType, purchaseQueryPage.getFavourableType())
  //                .eq(Objects.nonNull(purchaseQueryPage.getIsActive()), Purchase::getIsActive,
  //                        purchaseQueryPage.getIsActive())
  //                .eq(Objects.nonNull(purchaseQueryPage.getPlatformType()),
  // Purchase::getPlatformType,
  //                        purchaseQueryPage.getPlatformType())
  //                .eq(Objects.nonNull(purchaseQueryPage.getActiveType()), Purchase::getActiveType,
  //                        purchaseQueryPage.getActiveType())
  //                .eq(Objects.nonNull(purchaseQueryPage.getStatus()), Purchase::getStatus,
  //                        purchaseQueryPage.getStatus())
  //                .eq(Objects.nonNull(purchaseQueryPage.getMonth()), Purchase::getMonth,
  //                        purchaseQueryPage.getMonth())
  //                .orderByDesc(Purchase::getSort);
  //        if ((!purchaseQueryPage.getShowAll()) && Objects
  //                .nonNull(purchaseQueryPage.getCurrentUserId())) {
  //            queryWrapper.lambda().eq(Purchase::getBuyerId,
  // purchaseQueryPage.getCurrentUserId());
  //        } else if (purchaseQueryPage.getShowAll() && Objects
  //                .nonNull(purchaseQueryPage.getBuyerId())) {
  //            queryWrapper.lambda().eq(Purchase::getBuyerId, purchaseQueryPage.getBuyerId());
  //        }
  //
  //        // 【20230918 七喜 增加合作模式筛选条件】
  //
  // purchaseQueryPage.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(purchaseQueryPage.getBusinessLine()));
  //        queryWrapper.lambda().in(CollUtil.isNotEmpty(purchaseQueryPage.getBusinessLine()),
  //                Purchase::getBusinessLine, purchaseQueryPage.getBusinessLine());
  //        return queryWrapper;
  //    }

  @Override
  public String getExcelTemplateUrl() {
    return StringUtil.isNotBlank(excelTemplateUrl)
        ? excelTemplateUrl
        : "https://cdn-test.daddylab.com/Upload/supplier/item/采购导入模板-1652943858213.xlsx";
  }

  @Override
  public PurchaseOperate importExcel(InputStream inputStream) {
    final List<PurchaseSheet> exportItems =
        EasyExcel.read(inputStream)
            // Excel模板数据从第三行开始
            .headRowNumber(3)
            .head(PurchaseSheet.class)
            .sheet()
            .doReadSync();
    if (exportItems.isEmpty()) {
      throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "导入Excel文件为空");
    }
    // 第一列为空的行认为是空行，直接过滤掉
    final List<PurchaseSheet> exportItemsFiltered =
        exportItems.stream()
            .filter(row -> StringUtil.isNotBlank(row.getMonth()))
            .collect(Collectors.toList());
    log.info("导入采购价格，文件读取成功，总行数：{}", exportItemsFiltered.size());
    handleImportItems(exportItemsFiltered);
    log.info("导入采购价格，导入成功，总行数：{}", exportItemsFiltered.size());
    PurchaseOperate purchaseOperate = new PurchaseOperate();
    purchaseOperate.setCount(exportItems.size());
    return purchaseOperate;
  }

  @Override
  public void updateStatus(Long id) {
    Purchase purchase = iPurchaseService.getById(id);
    if (Objects.isNull(purchase)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查不到该记录");
    }
    purchase.setStatus(PurchaseStatus.DISSENT);
    iPurchaseService.updateById(purchase);
  }

  @Override
  public Response agree(String token, Long id, String code) {
    checkToken(token, code);
    Assert.notNull(id, "采购id入参不得为空");
    Purchase purchase = iPurchaseService.getById(id);
    if (Objects.isNull(purchase)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查不到该记录,操作失败");
    }
    purchase.setStatus(PurchaseStatus.AL_CONFIRM);
    iPurchaseService.updateById(purchase);
    return Response.buildSuccess();
  }

  @Override
  public SingleResponse<String> getMobileByCode(String code) {
    String mobile = getMobile(code);
    return SingleResponse.of(StringUtil.maskMobile(mobile));
  }

  @Override
  public Response getSmsCode(String code) {
    String mobile = getMobile(code);
    smsAuthService.getSmsCode(mobile);
    return Response.buildSuccess();
  }

  @Override
  public SingleResponse<String> checkCode(String code, String verifyCode) {
    String mobile = getMobile(code);
    boolean b = smsAuthService.verifyCode(mobile, verifyCode);
    String random = RandomStringUtils.random(16, true, true);
    if (true) {
      List<Purchase> listByCode = getListByCode(code);
      if (StringUtils.isNotBlank(listByCode.get(0).getToken())) {
        return SingleResponse.of(listByCode.get(0).getToken());
      } else {
        listByCode.forEach(
            purchase -> {
              purchase.setToken(random);
              iPurchaseService.updateById(purchase);
            });
        return SingleResponse.of(random);
      }
    } else {
      throw ExceptionPlusFactory.bizException(ErrorCode.SMS_AUTH_ERROR, "短信验证错误");
    }
  }

  @Override
  public void dealActiveItem() {
    try {
      // 获取上个月的纯活动商品
      String lastMonth = DateUtil.lastMonthDate(MONTH_PATTERN);
      PurchaseTable purchaseTable = purchaseTableBizService.selectDataByMonth(lastMonth);

      QueryWrapper<Purchase> queryWrapper = new QueryWrapper<>();
      queryWrapper.lambda().eq(Purchase::getIsActive, 1).eq(Purchase::getMonth, lastMonth);
      List<Purchase> list = iPurchaseService.list(queryWrapper);
      log.info("开始处理上月纯活动商品,总共有 {}个 ", list.size());

      if (CollectionUtils.isNotEmpty(list) && Objects.nonNull(purchaseTable)) {
        for (Purchase purchase : list) {
          purchase.setStatus(PurchaseStatus.NO_CONFIRM);
          purchase.setIsConfirm(0);
          purchase.setStartTime(0L);
          purchase.setEndTime(0L);
          purchase.setToken("");
          String random = RandomStringUtils.random(8, true, true);
          purchase.setMd5(random);
          purchase.setMonth(DateUtil.format(System.currentTimeMillis() / 1000, MONTH_PATTERN));
          iPurchaseService.save(purchase);
        }
        purchaseTableBizService.updateIsDeal(purchaseTable.getId());
      }
    } catch (Exception e) {
      if (log.isErrorEnabled()) {
        log.error("【处理上个月的纯活动商品】list", e);
      }
    }
  }

  @Override
  public Response checkToken(String token, String code) {
    if (StringUtils.isEmpty(token)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.ILLEGAL_TOKEN, "token失效或不存在");
    }
    token = StringUtil.trimStart(token, "Bearer ");
    QueryWrapper<Purchase> queryWrapper = new QueryWrapper<>();
    queryWrapper.lambda().eq(Purchase::getToken, token).eq(Purchase::getMd5, code);
    List<Purchase> list = iPurchaseService.list(queryWrapper);
    if (CollectionUtils.isEmpty(list)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.ILLEGAL_TOKEN, "token失效或不存在");
    }
    return Response.buildSuccess();
  }

  @Override
  public MultiResponse<String> getProviderList(String month, String provider) {
    QueryWrapper<Purchase> queryWrapper = new QueryWrapper<>();
    queryWrapper
        .lambda()
        .like(StringUtils.isNotBlank(provider), Purchase::getProvider, provider)
        .eq(StringUtils.isNotBlank(month), Purchase::getMonth, month);
    List<Purchase> list = iPurchaseService.list(queryWrapper);
    List<String> collect = list.stream().map(Purchase::getProvider).collect(Collectors.toList());
    List<String> purchaseList = collect.stream().distinct().collect(Collectors.toList());
    return MultiResponse.of(purchaseList);
  }

  @Override
  public SingleResponse<PurchaseItem> getDataByItemSku(String itemSku) {
    PurchaseItem purchaseBySku = itemGateway.getPurchaseBySku(itemSku);
    if (Objects.nonNull(purchaseBySku)) {
      if (StringUtil.isNotBlank(purchaseBySku.getProviderSpecifiedCode())) {
        purchaseBySku.setSpuCode(purchaseBySku.getProviderSpecifiedCode());
      }
      purchaseBySku.setItemAttr(itemSkuGateway.getSkuAttrListStr(itemSku));

      // 合作方 + 业务层级 新增返回
      final Map<Long, List<CorpBizTypeDTO>> longListMap =
          iBizLevelDivisionService.queryByItemId(ListUtil.of(purchaseBySku.getItemId()));
      purchaseBySku.setCorpBizType(longListMap.get(purchaseBySku.getItemId()));

      //            if (CollUtil.isNotEmpty(longListMap)) {
      //                final List<CorpBizTypeDTO> corpBizTypeDTOS =
      // longListMap.get(purchaseBySku.getItemId());

      //                if (CollUtil.isNotEmpty(corpBizTypeDTOS)) {
      //
      // purchaseBySku.setCorpType(corpBizTypeDTOS.stream().map(CorpBizTypeDTO::getCorpType).collect(Collectors.toList()));
      //
      // purchaseBySku.setBizType(corpBizTypeDTOS.stream().map(CorpBizTypeDTO::getBizType).flatMap(List::stream).collect(Collectors.toList()));
      //                }
      //            }

    }
    return SingleResponse.of(purchaseBySku);
  }

  private void handleImportItems(List<PurchaseSheet> exportItems) {
    final ArrayList<String> errors = new ArrayList<>();
    int i = 3;
    for (PurchaseSheet exportItem : exportItems) {
      i++;
      final StringJoiner stringJoiner = new StringJoiner("、");
      if (StringUtils.isEmpty(exportItem.getMonth())
          || StringUtils.isEmpty(exportItem.getItemSku())
          || StringUtils.isEmpty(exportItem.getItemName())) {
        stringJoiner.add("必填字段为空（月份、商品SKU、商品名称）");
      }
      if (!ReUtil.isMatch("\\d{4}/\\d{2}", exportItem.getMonth())) {
        stringJoiner.add("月份格式错误，正确格式为 yyyy/MM");
      }
      if (!checkShelfTime(exportItem.getStartTime()) || !checkShelfTime(exportItem.getEndTime())) {
        stringJoiner.add("活动开始结束时间格式错误");
      }
      if (!checkPrice(exportItem.getUsualPrice())) {
        stringJoiner.add("日常供价无优惠格式错误");
      }
      if (!checkPrice(exportItem.getPriceCost())) {
        stringJoiner.add("按价格优惠结算成本格式错误");
      }
      if (!checkPrice(exportItem.getNumCost())) {
        stringJoiner.add("按数量优惠结算成本格式错误");
      }
      final String error = stringJoiner.toString();
      if (!error.isEmpty()) {
        errors.add(StringUtil.format("第{}行数据有误:{}", i, error));
      }
    }
    if (!errors.isEmpty()) {
      final int maxShowErrorsNum = 20;
      throw ExceptionPlusFactory.bizException(
          ErrorCode.VERIFY_PARAM,
          "采购价格批量导入失败，数据校验未通过，请参照以下错误信息核对数据：<br/>"
              + errors.stream()
                  .limit(maxShowErrorsNum)
                  .map(v -> StringUtil.format(" - {}", v))
                  .collect(Collectors.joining("<br/>"))
              + (errors.size() > maxShowErrorsNum ? "..." : ""));
    }
    final Set<String> months =
        exportItems.stream().map(PurchaseSheet::getMonth).collect(Collectors.toSet());
    final Map<String, Boolean> statusMap = purchaseTableBizService.getEditableStatusBatch(months);
    List<String> uneditableMonths = new ArrayList<>();
    for (String month : months) {

      if (!statusMap.get(month)) {
        uneditableMonths.add(month);
      }
    }
    if (CollUtil.isNotEmpty(uneditableMonths)) {
      throw ExceptionPlusFactory.bizException(
          ErrorCode.VERIFY_PARAM, "月份 " + Joiner.on(',').join(uneditableMonths) + " 不允许编辑");
    }
    final ArrayList<OperateLog> operateLogBuf = new ArrayList<>(exportItems.size());
    final Function4<Long, OperateLogTarget, Long, String, OperateLog> operateLogBuild =
        (Long operatorId, OperateLogTarget target, Long targetId, String msg) -> {
          final OperateLog operateLog = new OperateLog();
          operateLog.setTargetType(target);
          operateLog.setTargetId(String.valueOf(targetId));
          operateLog.setMsg(msg);
          operateLog.setOperatorId(operatorId);
          return operateLog;
        };
    for (PurchaseSheet exportItem : exportItems) {

      Purchase purchase = PurchaseTransMapper.INSTANCE.purchaseSheetDbToVo(exportItem);

      //            Integer businessLineByCode = getBusinessLineByCode(purchase.getItemSku());
      //            purchase.setBusinessLine(businessLineByCode);

      StaffListQuery staffListQuery = new StaffListQuery();
      if (StringUtils.isNotBlank(exportItem.getBuyerName())) {
        staffListQuery.setNickname(exportItem.getBuyerName());
        staffListQuery.setStatus(1);
        staffListQuery.setPageSize(10);
        staffListQuery.setPageIndex(1);
        List<StaffInfo> staffInfos = userGateway.queryStaffList(staffListQuery);
        if (CollectionUtils.isNotEmpty(staffInfos)) {
          purchase.setBuyerId(staffInfos.get(0).getUserId());
        } else {
          purchase.setBuyerId(0L);
        }
      } else {
        purchase.setBuyerId(0L);
      }

      // 新增采购记录
      purchaseGateway.saveDo(purchase);

      operateLogBuf.add(
          operateLogBuild.apply(
              UserContext.getUserId(), OperateLogTarget.PURCHASE, purchase.getId(), "通过文件导入新增了采购"));
    }

    // 排序
    purchaseMapper.setDefaultSort();

    // 记录操作日志
    if (!operateLogBuf.isEmpty()) {
      operateLogDomainService.batchAddOperateLog(operateLogBuf);
      operateLogBuf.clear();
    }
  }

  private boolean checkPrice(String val) {
    try {
      new BigDecimal(val);
      return true;
    } catch (Exception e) {
      return false;
    }
  }

  private Boolean checkShelfTime(String time) {
    try {
      DateUtil.parseCompatibility(time);
      return true;
    } catch (Exception e) {
      return false;
    }
  }

  //    private static Boolean judgeTime(Long time) {
  //        final LocalDateTime now = DateUtil.toLocalDateTime(time);
  //        final LocalDateTime firstDayOfMonth = DateUtil.getFirstDayOfMonth();
  //        final LocalDateTime lastSecondOfFirstDayOfNextMonth =
  // DateUtil.toLastSecondOfDay(firstDayOfMonth.plusMonths(1));
  //        return now.isAfter(firstDayOfMonth) && now.isBefore(lastSecondOfFirstDayOfNextMonth);
  //    }

  private List<Purchase> getNameByCode(String code) {
    QueryWrapper<Purchase> queryWrapper = new QueryWrapper<>();
    queryWrapper.lambda().eq(Purchase::getMd5, code);
    List<Purchase> list = iPurchaseService.list(queryWrapper);
    if (CollectionUtils.isNotEmpty(list)) {
      return list;
    } else {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "查询不到相关记录");
    }
  }

  private String getMobile(String code) {
    //        PartnerPurchaseReq partnerPurchaseReq = new PartnerPurchaseReq();
    //        List<Purchase> purchases = getNameByCode(code);
    //        final String providerName = purchases.get(0).getProvider();
    //        partnerPurchaseReq.setOrganizationName(providerName);
    //        List<String> itemNames =
    // purchases.stream().map(Purchase::getItemName).collect(Collectors.toList());
    //        partnerPurchaseReq.setItemNames(JsonUtil.toJson(itemNames));
    //        partnerPurchaseReq.setPageSize(10);
    //        //调用合作伙伴接口获取手机号
    //        Rsp<List<PartnerPurchaseResp>> listRsp =
    // partnerFeignClient.mobileQuery(partnerPurchaseReq);
    //
    //        //如果根据商品无法精确查询到手机号，就直接去查供应商的管理员手机号
    //        if (CollectionUtils.isEmpty(listRsp.getData())) {
    //            final PartnerProviderPageQuery partnerPageQuery = new PartnerProviderPageQuery();
    //            partnerPageQuery.setName(providerName);
    //
    //            final List<PartnerProviderResp> partnerProviderRespList =
    // providerGateway.partnerQuery(partnerPageQuery);
    //            if (partnerProviderRespList.isEmpty()) {
    //                throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND,
    // "查询不到相关手机号");
    //            }
    //            return partnerProviderRespList.get(0).getMobile();
    //        }
    //        return
    // listRsp.getData().stream().map(PartnerPurchaseResp::getMobile).distinct().count() > 1
    //                ? listRsp.getData().get(0).getMainMobile() :
    // listRsp.getData().get(0).getMobile();
    return "***********";
  }

  private List<Purchase> getListByCode(String code) {
    QueryWrapper<Purchase> queryWrapper = new QueryWrapper<>();
    queryWrapper.lambda().eq(Purchase::getMd5, code);
    return iPurchaseService.list(queryWrapper);
  }

  private Integer getBusinessLineByCode(String code) {
    if (code.startsWith("MU")) {
      return iBizLevelDivisionService.getCoryType(null, code, BizUnionTypeEnum.COMBINATION);
    }

    Optional<ItemSku> itemSkuOptional =
        iItemSkuService.lambdaQuery().eq(ItemSku::getSkuCode, code).oneOpt();
    if (itemSkuOptional.isPresent()) {
      Long itemId = itemSkuOptional.get().getItemId();
      return iBizLevelDivisionService.getCoryType(itemId, null, BizUnionTypeEnum.SPU);
    }

    return 0;

    //        boolean isCombinationItem = code.startsWith("MU");
    //        if (isCombinationItem) {
    //            Optional<CombinationItem> optionalCombinationItem = iCombinationItemService
    //                    .lambdaQuery().eq(CombinationItem::getCode, code).oneOpt();
    //            if (optionalCombinationItem.isPresent()) {
    //                CombinationItem combinationItem = optionalCombinationItem.get();
    //                return combinationItem.getBusinessLine();
    //            }
    //        } else {
    //            Optional<ItemSku> itemSkuOptional =
    // iItemSkuService.lambdaQuery().eq(ItemSku::getSkuCode, code).oneOpt();
    //            if (itemSkuOptional.isPresent()) {
    //                Long itemId = itemSkuOptional.get().getItemId();
    //                Item item = iItemService.getById(itemId);
    //                if (Objects.nonNull(item)) {
    //                    return item.getBusinessLine();
    //                }
    //            }
    //        }
    //        return 0;
  }

  @Override
  public SingleResponse<Boolean> clearHistoryDataBusinessLine() {
    //        List<Purchase> purchaseList =
    // iPurchaseService.lambdaQuery().isNull(Purchase::getBusinessLine).list();
    //        for (Purchase purchase : purchaseList) {
    //            String code = purchase.getItemSku();
    //            Integer businessLineByCode = getBusinessLineByCode(code);
    //            purchase.setBusinessLine(businessLineByCode);
    //            iPurchaseService.updateById(purchase);
    //        }
    return SingleResponse.of(true);
  }

  //    @Override
  //    public void checkBusinessLineUpdate(String code, Integer newBusinessLine) {
  //        List<Purchase> list = iPurchaseService.lambdaQuery().eq(Purchase::getItemSku,
  // code).list();
  //        if (CollUtil.isEmpty(list)) {
  //            return;
  //        }
  //        iPurchaseService.lambdaUpdate().set(Purchase::getBusinessLine, newBusinessLine)
  //                .eq(Purchase::getItemSku, code).update();
  //    }
}
