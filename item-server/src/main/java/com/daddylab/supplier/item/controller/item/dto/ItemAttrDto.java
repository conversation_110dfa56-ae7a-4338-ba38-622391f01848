package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/20 3:45 下午
 * @description
 */
@Data
@ApiModel("属性列表")
public class ItemAttrDto {

    @ApiModelProperty("table itemAttr DB id")
    Long itemAttrDbId;

    @ApiModelProperty("item_sku id")
    Long id;

    @ApiModelProperty("类目属性id")
    Long attrId;

    @ApiModelProperty("属性值")
    @NotBlank(message = "sku属性值不得为空")
    String value;

    @ApiModelProperty("属性名字")
    String name;
}
