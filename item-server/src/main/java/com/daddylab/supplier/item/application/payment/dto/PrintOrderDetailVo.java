package com.daddylab.supplier.item.application.payment.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2024年04月23日 2:42 PM
 */
@Data
public class PrintOrderDetailVo {

    @ApiModelProperty("序号")
    private String sort;

    @ApiModelProperty("源单类型")
    private String sourceOrderType;

    @ApiModelProperty("源单编号")
    private String sourceOrderNo;

    @ApiModelProperty("付款用途")
    private String payPurpose;

    @ApiModelProperty("应付金额")
    private String paymentAmount;

    @ApiModelProperty("申请付款金额")
    private String applyAmount;

    @ApiModelProperty("对方银行账号")
    private String targetBankNo;

    @ApiModelProperty("对方开户行")
    private String targetBank;


}
