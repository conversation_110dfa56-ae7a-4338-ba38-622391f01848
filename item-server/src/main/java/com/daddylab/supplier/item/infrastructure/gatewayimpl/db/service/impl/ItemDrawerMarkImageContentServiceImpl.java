package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMarkImageContent;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerMarkImageContentMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerMarkImageContentService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 标注图片内容 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Service
public class ItemDrawerMarkImageContentServiceImpl extends DaddyServiceImpl<ItemDrawerMarkImageContentMapper, ItemDrawerMarkImageContent> implements IItemDrawerMarkImageContentService {

}
