package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffIgnore;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单发货跟踪
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OrderDeliveryTrace implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /**
     * 是否已删除
     */
    @TableLogic
    @DiffIgnore
    protected Integer isDel;

    /**
     * 原始单号
     */
    private String orderNo;

    /**
     * 订单编号（旺店通）
     */
    private String tradeNo;

    /**
     * 店铺编号
     */
    private String shopNo;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 平台（内部定义）
     */
    private Platform platform;

    /**
     * 发货仓库编号
     */
    private String warehouseNo;

    /**
     * 发货仓库
     */
    private String warehouseName;

    /**
     * 订单员ID
     */
    private Long orderPersonnelId;

    /**
     * 审核时间
     */
    private LocalDateTime checkTime;


}
