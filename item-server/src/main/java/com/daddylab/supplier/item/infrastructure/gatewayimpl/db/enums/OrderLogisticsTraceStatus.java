package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <img src="doc/物流跟踪.md" alt="状态图"/>
 *
 * <AUTHOR>
 * @since 2024/5/22
 */
@AllArgsConstructor
@Getter
public enum OrderLogisticsTraceStatus implements IIntegerEnum {
    NOT_DELIVERY(0, "待发货", ""),

    //快刀云流程
    KDY_WAIT_SUBSCRIBE(1, "快刀云待订阅", ""),
    KDY_SUBSCRIBED(2, "快刀云已订阅", ""),
    KDY_SUBSCRIBE_FAIL(3, "快刀云订阅失败", ""),
    KDY_SUBSCRIBE_DELAY(4, "快刀云延迟订阅", ""),
    KDY_CALLBACK(5, "快刀云已回调", ""),
    KDY_CALLBACK_TIMEOUT(6, "快刀云回调超时", ""),

    //自研商城回调流程
    MALL_WAIT_CALLBACK(10, "自研商城待回调", "来自小程序的订单优先由小程序端负责处理订阅逻辑"),
    MALL_CALLBACK(11, "自研商城已回调", ""),
    MALL_CALLBACK_TIMEOUT(12, "自研商城回调超时", "长时间未收到小程序回传的物流轨迹"),

    //旺店通流程
    WDT_WAIT_CALLBACK(20, "旺店通待回调", "符合条件的订单走旺店通查询物流轨迹"),
    WDT_CALLBACK(21, "旺店通已回调", ""),
    ;
    private final Integer value;
    private final String desc;
    private final String note;


}
