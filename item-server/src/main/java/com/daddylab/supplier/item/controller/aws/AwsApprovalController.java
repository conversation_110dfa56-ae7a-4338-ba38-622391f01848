package com.daddylab.supplier.item.controller.aws;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.aws.service.AwsApprovalFlowService;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Api(value = "炎黄盈动审批流公共接口API", tags = "炎黄盈动审批流公共接口API")
@RestController
@RequestMapping("/aws/approval")
public class AwsApprovalController {

    @Autowired
    private AwsApprovalFlowService awsApprovalFlowService;


    @ApiOperation(value = "审批流url")
    @GetMapping("/url")
    public SingleResponse<String> getUrl(@RequestParam(value = "businessId") Long businessId,
                                         @RequestParam(value = "typeEnum") PurchaseTypeEnum typeEnum){
        return SingleResponse.of(awsApprovalFlowService.getUrl(businessId, typeEnum));
    }



    /**
     * 给信息组调用
     * @param processInstId 流程实例ID
     * @return
     */
    @GetMapping("/notice")
    @Auth(noAuth = true)
    public Response notice(@RequestParam(value = "processInstId") String processInstId){
        awsApprovalFlowService.notice(processInstId);
        return Response.buildSuccess();
    }


    @ApiOperation(value = "验证节点是否变化")
    @GetMapping("/checkTask")
    public SingleResponse<Boolean> checkTask(@RequestParam(value = "businessId") Long businessId,
                                             @RequestParam(value = "typeEnum") PurchaseTypeEnum typeEnum,
                                             @RequestParam(value = "taskInsId") String taskInsId){
        return SingleResponse.of(awsApprovalFlowService.checkTask(businessId, typeEnum, taskInsId));
    }




    @ApiOperation(value = "是否隐藏按钮")
    @GetMapping("/hideButton")
    public SingleResponse<Boolean> hideButton(@RequestParam(value = "businessId") Long businessId,
                                              @RequestParam(value = "typeEnum") PurchaseTypeEnum typeEnum){
        return SingleResponse.of(awsApprovalFlowService.hideButton(businessId, typeEnum));
    }
}
