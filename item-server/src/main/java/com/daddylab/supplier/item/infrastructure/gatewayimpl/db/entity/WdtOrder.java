package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 旺店通订单数据
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单唯一键
     */
    private Long tradeId;

    /**
     * 订单号
     */
    private String tradeNo;

    /**
     * 平台ID（请点击 平台代码表 查看对应关系 ）
     */
    private Integer platformId;

    /**
     * 仓库类型: 1、普通仓库,大于1为委外仓库(如京东仓储,物流宝等)
     */
    private Integer warehouseType;

    /**
     * 原始单号，如果有多个，以","分隔，且以增序排列,不重复,过长将被裁剪
     */
    private String srcTids;

    /**
     * 平台支付帐号, 淘系平台不返回
     */
    private String payAccount;

    /**
     * 订单状态: 4 线下退款 5已取消 6 待确认订单, 导入放入这个状态  待转预订单(待审核) 7待确认订单,导入时先放到这个状态（此状态不占用库存,可删除,离开这个状态就不能删除了) 10待付款 12待尾款 15等未付 16延时审核 19预订单前处理 20前处理(赠品，合并，拆分) 21委外前处理 23 异常预订单 24 换货预订单 25待处理预订单 26 待激活预订单 27待分配预订单 30待客审 35待财审 55已审核 95已发货 96 成本确认（待录入计划成本，订单结算时有货品无计划成本） 101 已过账 110已完成
     */
    private Integer tradeStatus;

    /**
     * 订单类型: 1、网店销售 2、线下订单 3、售后换货 4、批发业务 7、现款销售 8、分销 101、自定义类型一 102、自定义类型二 103、自定义类型三 104、自定义类型四 105、自定义类型五 106、自定义类型六
     */
    private Integer tradeType;

    /**
     * 发货条件: 1、款到发货 2、货到付款(包含部分货到付款) 3、分期付款
     */
    private Integer deliveryTerm;

    /**
     * 京东几环
     */
    private String receiverRing;

    /**
     * 冻结原因
     */
    private String freezeReason;

    /**
     * 退款状态: 0、无退款 1、申请退款 2、部分退款 3、全部退款
     */
    private Integer refundStatus;

    /**
     * 分销类别: 0、非分销订单 1、代销 2、经销
     */
    private Integer fenxiaoType;

    /**
     * 分销商名
     */
    private String fenxiaoNick;

    /**
     * 下单时间 （毫 秒级时间戳，例如：1631861379000）
     */
    private LocalDateTime tradeTime;

    /**
     * 支付时间，例如：2020-10-19 00:00:00
     */
    private LocalDateTime payTime;

    /**
     * 发货时间，订单未发货不返回该字 段（毫秒级时间戳，例如：1631861379000）
     */
    private LocalDateTime consignTime;

    /**
     * 买家网名, 淘系平台不返回
     */
    private String buyerNick;

    /**
     * 收货人姓名, 淘系平台不返回
     */
    private String receiverName;

    /**
     * 省份id，可参考 城市代码表
     */
    private Integer receiverProvince;

    /**
     * 城市id， 可参考 城市代码表
     */
    private Integer receiverCity;

    /**
     * 地区id， 可参考 城市代码表
     */
    private Integer receiverDistrict;

    /**
     * 收件人地址, 淘系平台不返回
     */
    private String receiverAddress;

    /**
     * 手机, 淘系平台不返回
     */
    private String receiverMobile;

    /**
     * 固话, 淘系平台不返回
     */
    private String receiverTelno;

    /**
     * 邮编
     */
    private String receiverZip;

    /**
     * 省市区空格分隔
     */
    private String receiverArea;

    /**
     * 大头笔
     */
    private String receiverDtb;

    /**
     * 配送时间,例如： 2020-10-19 00:00:00
     */
    private LocalDateTime toDeliverTime;

    /**
     * 异常订单原因（位运算）: 2、修改地址 4、修改发票 8、更换仓库 16、修改备注 32、更换货品 128、拦截赠品 256、拦截换货 512、买家留言变化 1024、拦截平台已发货
     */
    private Integer badReason;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 客户备注
     */
    private String buyerMessage;

    /**
     * 客服备注
     */
    private String csRemark;

    /**
     * 标旗（1 红、2 黄、3 绿、4 蓝、5 紫 ）
     */
    private Integer remarkFlag;

    /**
     * 打印备注
     */
    private String printRemark;

    /**
     * 货品种类数
     */
    private BigDecimal goodsTypeCount;

    /**
     * 货品总数
     */
    private BigDecimal goodsCount;

    /**
     * 货品总额
     */
    private BigDecimal goodsAmount;

    /**
     * 邮费
     */
    private BigDecimal postAmount;

    /**
     * 其他收费
     */
    private BigDecimal otherAmount;

    /**
     * 优惠金额
     */
    private BigDecimal discount;

    /**
     * 应收金额
     */
    private BigDecimal receivable;

    /**
     * 货到付款金额
     */
    private BigDecimal codAmount;

    /**
     * 买家COD费用
     */
    private BigDecimal extCodFee;

    /**
     * 货款预估成本
     */
    private BigDecimal goodsCost;

    /**
     * 预估邮费成本
     */
    private BigDecimal postCost;

    /**
     * 预估称重kg
     */
    private BigDecimal weight;

    /**
     * 预估利润
     */
    private BigDecimal profit;

    /**
     * 税额
     */
    private BigDecimal tax;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 佣金
     */
    private BigDecimal commission;

    /**
     * 发票类别: 0、不需要 1、普通发票 2、普通增值税发票 3、专用增值税发票
     */
    private Integer invoiceType;

    /**
     * 发票标题
     */
    private String invoiceTitle;

    /**
     * 发票内容
     */
    private String invoiceContent;

    /**
     * 业务员
     */
    private String salesmanName;

    /**
     * 审核员
     */
    private String checkerName;

    /**
     * 财审员
     */
    private String fcheckerName;

    /**
     * 签出员
     */
    private String checkouterName;

    /**
     * 出库单号
     */
    private String stockoutNo;

    /**
     * 标记名称
     */
    private String flagName;

    /**
     * 订单来源: 1、API抓单 2、手工建单 3、导入 4、复制订单 5、接口推送 6、补发订单 7、PDA选货开单
     */
    private Integer tradeFrom;

    /**
     * 单一货品商家编码,多种货品为空,组合装时为组合装编码
     */
    private String singleSpecNo;

    /**
     * 原货品数量
     */
    private BigDecimal rawGoodsCount;

    /**
     * 原货品种类数
     */
    private Integer rawGoodsTypeCount;

    /**
     * 币种
     */
    private String currency;

    /**
     * 发票ID，目前只设0-1，1表示已开发票
     */
    private Integer invoiceId;

    /**
     * 版本号
     */
    private Integer versionId;

    /**
     * 修改时间， 例如： 2020-10-19 00:00:00
     */
    private LocalDateTime modified;

    /**
     * 创建时间 （毫秒级时间戳，例如：1631861379000）
     */
    private LocalDateTime created;

    /**
     * 审核时间
     */
    private LocalDateTime checkTime;

    /**
     * 证件类别
     */
    private Integer idCardType;

    /**
     * 证件号码
     */
    private String idCard;

    /**
     * 店铺编号
     */
    private String shopNo;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺备注
     */
    private String shopRemark;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 买家姓名
     */
    private String customerName;

    /**
     * 客户编码
     */
    private String customerNo;

    /**
     * 物流名称
     */
    private String logisticsName;

    /**
     * 物流公司编号
     */
    private String logisticsCode;

    /**
     * 物流类型名称
     */
    private String logisticsTypeName;

    /**
     * 预计发货时间
     */
    private LocalDateTime delayToTime;

    /**
     * 订单标签
     */
    private String tradeLabel;

    /**
     * 删除时间
     */
    private Long deletedAt;

    @TableField(exist = false)
    private List<WdtOrderDetail> orderDetails;
}
