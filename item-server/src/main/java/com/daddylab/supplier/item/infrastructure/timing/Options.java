package com.daddylab.supplier.item.infrastructure.timing;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2021/12/13
 */
@Data
@Accessors(chain = true)
public class Options {
    /**
     * 不传默认取方法名加参数
     */
    private String name = "";

    /**
     * 时间单位，默认毫秒
     */
    private TimeUnit unit = TimeUnit.MILLISECONDS;

    /**
     * 默认输出到标准输出
     */
    private Output output = Output.LOG;

    /**
     * 如果输出到日志，默认的日志级别是DEBUG
     */
    private Level level = Level.DEBUG;
}
