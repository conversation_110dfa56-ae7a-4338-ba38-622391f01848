package com.daddylab.supplier.item.application.shortlink.feign;

import com.daddylab.supplier.item.application.shortlink.feign.dto.Result;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class ShortLinkFallbackFactory implements FallbackFactory<ShortLinkFeignClient> {
    @Override
    public ShortLinkFeignClient create(Throwable cause) {
        final Map<String, String> fallbackLinks = new HashMap<>();

        return params -> {
            log.error("短链服务异常", cause);
            final String shortUrl = fallbackLinks.get(params.getLongUrl());
            final boolean flag = StringUtil.isNotBlank(shortUrl);
            final Result<String> result = new Result<>();
            result.setFlag(flag);
            result.setMsg(flag ? "短链服务降级" : "短链服务异常");
            result.setCode(0);
            result.setData(shortUrl != null ? shortUrl : "");
            return result;
        };
    }
}
