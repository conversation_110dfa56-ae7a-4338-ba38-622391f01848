package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Date: 2022/6/3 16:27
 * @Description: 请描述下这个类
 */
@Data
@ApiModel("上新计划名称下拉框选择")
public class PlanNameDropDownVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "上新计划ID")
    private Long id;

    @ApiModelProperty(value = "上新计划名称")
    private String name;
}
