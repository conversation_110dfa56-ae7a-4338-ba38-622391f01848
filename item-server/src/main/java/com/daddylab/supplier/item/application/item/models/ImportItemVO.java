package com.daddylab.supplier.item.application.item.models;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/4/10
 */
@Data
public class ImportItemVO {
    @ExcelProperty("商品编码")
    private String itemCode;

    @ExcelProperty("采购员")
    private String buyer;

    @ExcelProperty("合作方")
    private String corpType;

    @ExcelProperty("商品名称")
    private String name;

    @ExcelProperty("商品简称")
    private String shortName;

    @ExcelProperty("P系统款号")
    private String partnerProviderItemSn;

    @ExcelProperty("供货指定编码")
    private String providerSpecifiedCode;

    @ExcelProperty("同步P系统")
    private String syncPsys;

    @ExcelProperty("同步金蝶")
    private String syncKingDee;

    @ExcelProperty("同步旺店通")
    private String syncWdt;

}
