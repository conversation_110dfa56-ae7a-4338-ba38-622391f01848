package com.daddylab.supplier.item.application.item;

import com.daddylab.supplier.item.application.drawer.ItemDrawerService;
import com.daddylab.supplier.item.application.item.event.ItemLaunchStatusChangeEvent;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.domain.platformItem.service.PlatformItemSyncService;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchPlan;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchStats;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemLaunchPlanService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemLaunchStatsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.google.common.eventbus.Subscribe;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/8/2
 */
@EventBusListener
@Slf4j
@RequiredArgsConstructor
public class ItemLaunchStatusListener {
    private final IItemLaunchStatsService itemLaunchStatsService;

    private final ItemDrawerService itemDrawerService;

    private final OperateLogGateway operateLogGateway;
    private final PlatformItemSyncService platformItemSyncService;
    private final IItemService itemService;
    private final IItemLaunchPlanService itemLaunchPlanService;

    @Subscribe
    public void onItemLaunchStatusChange(ItemLaunchStatusChangeEvent event) {
        final Long itemId = event.getItemId();
        final ItemLaunchStatus currentLaunchStatus = event.getFromStatus();
        final ItemLaunchStatus itemLaunchStatus = event.getToStatus();
        final Long operatorId = event.getOperatorId();
        final Long currentTimeEpoch = DateUtil.currentTime();
        final Optional<ItemLaunchStats> itemLaunchStatsOptional = itemLaunchStatsService.lambdaQuery()
                .eq(ItemLaunchStats::getItemId, itemId).oneOpt();
        final ItemLaunchStats itemLaunchStats = itemLaunchStatsOptional.orElseGet(() -> {
            final ItemLaunchStats newItemLaunchStats = new ItemLaunchStats();
            newItemLaunchStats.setItemId(itemId);
            return newItemLaunchStats;
        });

        final StaffBrief staffBrief = StaffAssembler.INST.toStaffBrief(operatorId);
        final Item item = itemService.getById(itemId);
        final ItemLaunchPlan plan = itemLaunchPlanService.getPlanByItemId(itemId);

        //上新状态变更增加操作记录
        operateLogGateway.addOperatorLog(operatorId, OperateLogTarget.NEW_GOODS_SPU, itemId,
                StringUtil.format("商品上新状态变更【{}】-> 【{}】", currentLaunchStatus.getDesc(),
                        itemLaunchStatus.getDesc()), null);

        try {
            //待选择 -> 待完善
            if (currentLaunchStatus == ItemLaunchStatus.TO_BE_SELECTED
                    && itemLaunchStatus == ItemLaunchStatus.TO_BE_IMPROVED) {
                itemLaunchStats.startToBeImprove(currentTimeEpoch);
            }
            //待完善 -> 待设计
            if (currentLaunchStatus == ItemLaunchStatus.TO_BE_IMPROVED
                    && itemLaunchStatus == ItemLaunchStatus.TO_BE_DESIGNED) {
                itemLaunchStats.startToBeDesign(currentTimeEpoch, operatorId);

                //EventBusUtil.post(MsgEvent.of(Collections.singletonList(itemId), MsgEventType.IMPROVE_TO_DESIGN), true);

                itemDrawerService.noticeToBeDesigned(itemId, operatorId);

            }
            //待设计 -> 待审核
            else if (currentLaunchStatus == ItemLaunchStatus.TO_BE_DESIGNED
                    && itemLaunchStatus == ItemLaunchStatus.TO_BE_AUDITED) {
                itemLaunchStats.startToBeLegalAudit(currentTimeEpoch, operatorId);

//                EventBusUtil.post(MsgEvent
//                        .of(Collections.singletonList(itemId), MsgEventType.DESIGN_TO_AUDIT_LEGAL), true);
                itemDrawerService.noticeToLegal(itemId, operatorId);
            }
            //待审核 -> 待修改
            else if (currentLaunchStatus == ItemLaunchStatus.TO_BE_AUDITED
                    && itemLaunchStatus == ItemLaunchStatus.TO_BE_UPDATED) {

                itemLaunchStats.startToBeUpdate(currentTimeEpoch, operatorId);

//                EventBusUtil.post(MsgEvent.of(Collections.singletonList(itemId), MsgEventType.AUDIT_TO_MODIFY), true);
                itemDrawerService.noticeToUpdate(itemId, operatorId);

                // 检查上新计划中的首页文案，如果不为空，将文案数据同步
                itemDrawerService.updateHomeCopy(itemId);
            }
            //待修改 -> 待上架
            else if (currentLaunchStatus == ItemLaunchStatus.TO_BE_UPDATED
                    && itemLaunchStatus == ItemLaunchStatus.TO_BE_RELEASED) {

                itemLaunchStats.startToBeRelease(currentTimeEpoch, operatorId);

                try {
                    platformItemSyncService.syncWdtPlatformGoodsByItemId(itemId);
                } catch (Exception e) {
                    log.error("商品上新状态变更触发平台商品同步异常，itemId={}", itemId, e);
                }
            }
            //待上架 -> 已上架
            else if (currentLaunchStatus == ItemLaunchStatus.TO_BE_RELEASED
                    && itemLaunchStatus == ItemLaunchStatus.HAS_BE_RELEASED) {

                itemLaunchStats.released(currentTimeEpoch);
            }
        } finally {

            itemLaunchStatsService.saveOrUpdate(itemLaunchStats);
        }

    }
}
