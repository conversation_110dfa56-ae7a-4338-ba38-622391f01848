package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.MessageConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessageOperationType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.MessageConfigMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IMessageConfigService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-21
 */
@Service
public class MessageConfigServiceImpl extends DaddyServiceImpl<MessageConfigMapper, MessageConfig> implements IMessageConfigService {

    @Override
    public Optional<MessageConfig> getByOperationType(MessageOperationType operationType, Boolean canEffect) {
        QueryWrapper<MessageConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(MessageConfig::getOperationType, operationType)
                .eq(MessageConfig::getCanEffect, canEffect ? 1 : 0).orderByDesc(MessageConfig::getCreatedAt);
        final List<MessageConfig> list = this.list(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return Optional.empty();
        } else {
            return Optional.of(list.get(0));
        }
    }
}
