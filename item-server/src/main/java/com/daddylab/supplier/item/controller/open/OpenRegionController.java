package com.daddylab.supplier.item.controller.open;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.region.AddressParseResult;
import com.daddylab.supplier.item.application.region.RegionBizService;
import com.daddylab.supplier.item.domain.region.vo.Region;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/open/region")
@RestController
@Api(value = "区域相关API开放接口", tags = {"区域相关API开放接口"})
public class OpenRegionController {
    @Autowired
    private RegionBizService regionBizService;

    @GetMapping(value = "/getRegionList")
    MultiResponse<Region> getRegionList(@ApiParam(name = "parentCode", value = "上级区域编码")
                                        @RequestParam(value = "parentCode", required = false) String parentCode) {
        return regionBizService.getRegionList(parentCode);
    }

    @ApiOperation(value = "获取大区列表")
    @GetMapping(value = "/getBigRegionList")
    MultiResponse<Region> getBigRegionList() {
        return regionBizService.getBigRegionList();
    }
    
    @ApiOperation(value = "地址识别")
    @GetMapping(value = "/parseAddress")
    public SingleResponse<AddressParseResult> parseAddress(@RequestParam String address) {
        return regionBizService.parseAddress(address);
    }

}
