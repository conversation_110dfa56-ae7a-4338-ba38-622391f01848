package com.daddylab.supplier.item.infrastructure.flowable;

import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.flowable.common.rest.util.RestUrlBuilder;

/**
 * <AUTHOR>
 * @since 2023/10/26
 */
public class RestResponseFactory extends org.flowable.rest.service.api.RestResponseFactory {
    public static RestResponseFactory INST = new RestResponseFactory(JsonUtil.MAPPER);

    public RestResponseFactory(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    protected RestUrlBuilder createUrlBuilder() {
        return RestUrlBuilder.usingBaseUrl("/");
    }
}
