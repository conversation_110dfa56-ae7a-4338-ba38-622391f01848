package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AllChannelBillOrderData;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AllChannelBillOrderDataMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAllChannelBillOrderDataService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 全渠道开票主订单数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-20
 */
@Service
public class AllChannelBillOrderDataServiceImpl extends DaddyServiceImpl<AllChannelBillOrderDataMapper, AllChannelBillOrderData> implements IAllChannelBillOrderDataService {

}
