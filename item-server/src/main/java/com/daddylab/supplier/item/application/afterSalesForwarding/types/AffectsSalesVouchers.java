package com.daddylab.supplier.item.application.afterSalesForwarding.types;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.javers.core.metamodel.annotation.Value;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/27
 */
@Data
@Value
@AllArgsConstructor(staticName = "of")
@NoArgsConstructor
public class AffectsSalesVouchers {
    @ApiModelProperty(value = "销售凭证图片")
    private List<Voucher> images = new ArrayList<>();

    @ApiModelProperty(value = "销售凭证视频")
    private List<Voucher> videos = new ArrayList<>();

    @Data
    @AllArgsConstructor(staticName = "of")
    @NoArgsConstructor
    public static class Voucher {
        private String name;
        private String url;

        public String toString() {
            return "[名称:" + name + ",地址:" + url + "]";
        }
    }

    @Override
    public String toString() {
        return "销售凭证图片:" + images + ",销售凭证视频:" + videos;
    }
}
