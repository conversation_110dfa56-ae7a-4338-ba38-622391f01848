package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsTrace;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.CloseReasonType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OrderLogisticsTraceStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderLogisticsTraceMapper;

import java.util.Collection;
import java.util.List;

/**
 * 订单物流跟踪记录 服务类
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
public interface IOrderLogisticsTraceService
    extends IDaddylabServicePlus<OrderLogisticsTrace, OrderLogisticsTraceMapper> {

  boolean lock(OrderLogisticsTrace orderLogisticsTrace);

  boolean updateStatusAndUnlock(OrderLogisticsTrace orderLogisticsTrace);

  long maxId();

  List<OrderLogisticsTrace> listByTraceStatus(OrderLogisticsTraceStatus status, int num);

  boolean updateTraceStatusByIds(List<Long> ids, OrderLogisticsTraceStatus traceStatus);

  /**
   * 扫描开启的跟踪列表
   *
   * @param trackTime 过滤跟踪时间小于该时间的，传 null 表示不过滤
   * @param cursor 游标
   * @param limit 返回数量
   * @param traceIds
   * @return 跟踪列表
   */
  List<OrderLogisticsTrace> scanOpen(
      Long trackTime, Long cursor, int limit, Collection<Long> traceIds);

  boolean openTrace(Long traceId);

  boolean closeTrace(Long traceId, CloseReasonType closeReason);
}
