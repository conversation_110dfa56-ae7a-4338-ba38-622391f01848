package com.daddylab.supplier.item.application.platformItem;

import com.daddylab.supplier.item.application.platformItem.model.PlatformItemSkuStat;

/**
 * <AUTHOR>
 * @since 2022/1/7
 */
public interface PlatformItemDao {
    /**
     * 统计平台商品SKU
     * @param platformItemId 平台商品ID
     * @return PlatformItemSkuStat
     */
    PlatformItemSkuStat statSku(long platformItemId);

    /**
     * 统计指定后端商品在售平台商品数量（SPU维度）
     * @param itemId 后端商品ID
     * @return
     */
    long countOnSalePlatformItem(long itemId);

}
