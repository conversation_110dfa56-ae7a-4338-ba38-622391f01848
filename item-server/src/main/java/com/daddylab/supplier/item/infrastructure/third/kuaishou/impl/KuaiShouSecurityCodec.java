package com.daddylab.supplier.item.infrastructure.third.kuaishou.impl;

import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.infrastructure.third.config.KuaiShouConfig;
import com.kuaishou.merchant.open.api.KsMerchantApiException;
import com.kuaishou.merchant.open.api.common.utils.PlatformEventSecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/4/10
 */
@Component
@Slf4j
public class KuaiShouSecurityCodec {
    @Autowired
    KuaiShouConfig kuaiShouConfig;


    /**
     * 回调消息解密
     *
     * @param message String
     * @return java.lang.String
     * @date 2024/2/29 16:25
     * <AUTHOR>
     */
    public String decodeMessage(String message) {
        try {
            return PlatformEventSecurityUtil.decode(message, kuaiShouConfig.getMessageSign());
        } catch (KsMerchantApiException e) {
            log.error("[快手消息解密] 失败.msg={}", e.getErrorMsg());
        }
        return StrUtil.EMPTY;
    }
}
