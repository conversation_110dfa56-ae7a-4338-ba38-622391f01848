package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 旺店通退货入库单明细与退换单明细的关联关系
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtRefundStockInOrderDetailsRefundOrderDetails implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 入库单ID
     */
    private Long stockinId;

    /**
     * 退换单明细id
     */
    private Integer refundOrderId;

    /**
     * 入库单明细id
     */
    private Integer stockinOrderDetailId;

    /**
     * 价格（退换单明细价格字段）
     */
    private BigDecimal price;

    /**
     * 商家编码
     */
    private String specNo;


}
