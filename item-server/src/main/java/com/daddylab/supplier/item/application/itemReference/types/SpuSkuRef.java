package com.daddylab.supplier.item.application.itemReference.types;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("商品参照表导入关联关系")
public class SpuSkuRef {

    @ApiModelProperty("SPU编码")
    @ExcelProperty(value = "SPU编码", index = 0)
    private String itemCode;

    @ApiModelProperty("SKU编码")
    @ExcelProperty(value = "SKU编码", index = 1)
    private String skuCode;

}