package com.daddylab.supplier.item.application.purchasePayable.vo;

import com.daddylab.supplier.item.controller.stockout.dto.StockOutOrderDetailVO;
import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderDetailForPurchasePayableVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherPayDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("采购应付单详情返回结果")
public class PurchasePayableDetailsVO extends PurchasePayableVO implements Serializable {

    private static final long serialVersionUID = -2747087868184137048L;

    @ApiModelProperty(value = "入库明细)")
    private List<StockInOrderDetailForPurchasePayableVO> stockInOrderDetails;

    @ApiModelProperty(value = "出库明细")
    private List<StockOutOrderDetailVO> stockOutOrderDetailList;

    @ApiModelProperty(value = "其他应付单详情")
    private List<OtherPayDetail> otherPayDetails;
}
