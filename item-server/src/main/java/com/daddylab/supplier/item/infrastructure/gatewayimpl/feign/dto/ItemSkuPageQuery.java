package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;
import com.google.common.collect.Lists;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.Positive;
import java.io.Serializable;
import java.util.List;

@Data
public class ItemSkuPageQuery implements Serializable {
    private static final long serialVersionUID = 6870300955287532306L;

    @ApiModelProperty(value = "店铺 ID", hidden = true)
    private Long shopId;

    @ApiModelProperty(value = "当前页数")
    @Positive(message = "当前页数必须为正整数")
    private Long current = 1L;

    @ApiModelProperty(value = "每页显示数量")
    private Long size = 10L;

    private List<Long> skuIds;

    private Long itemId;

    public static ItemSkuPageQuery of(Long current, Long size) {
        ItemSkuPageQuery itemSkuPageQuery = new ItemSkuPageQuery();
        itemSkuPageQuery.setCurrent(current);
        itemSkuPageQuery.setSize(size);
        return itemSkuPageQuery;
    }

}
