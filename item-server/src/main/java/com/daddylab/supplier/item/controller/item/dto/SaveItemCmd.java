package com.daddylab.supplier.item.controller.item.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/18 10:12 上午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("添加商品请求参数封装")
public class SaveItemCmd extends Command {
    private static final long serialVersionUID = 8473698509600746819L;

    @ApiModelProperty("商品id")
    private Long itemId;

    @Valid
    @ApiModelProperty("图片url")
    List<ItemImageDto> imageList;

    @NotBlank(message = "商品名称不得为空")
    @ApiModelProperty("商品名称")
    @Length(max = 100, message = "商品名字不得超过100")
    String itemName;

    @Positive(message = "商品品类不得为空")
    @ApiModelProperty("品类id")
    Long categoryId;

    @Positive(message = "商品品牌不得为空")
    @ApiModelProperty("品牌id")
    Long brandId;

    @ApiModelProperty("关联供应商系统商品款号")
    String partnerProviderItemSn;

    @ApiModelProperty("P系统技术类目")
    private Integer partnerSysType;

    @ApiModelProperty("关联供应商系统业务线。0供应商 1老爸抽检 2绿色家装 3商家入驻")
    List<Integer> businessLine;

    @Valid
    @ApiModelProperty("sku属性设置")
    List<ItemSkuListDto> skuList;

    @Positive(message = "供应商不得为空")
    @ApiModelProperty("供应商id")
    Long providerId;

    @Positive(message = "采购员不得为空")
    @ApiModelProperty("采购员userId")
    Long buyerUserId;

//    @ApiModelProperty("采购员名字")
//    String buyerUserName = "sys";

    @ApiModelProperty("采购员 花名")
    String buyerNickName;

    @NotBlank(message = "发货仓库不得为空")
    @ApiModelProperty("发货仓库No")
    String warehouseNo;

    @Positive(message = "基本单位不得为空")
    @ApiModelProperty("基本单位Id")
    Long baseUnitId;

    @NotNull(message = "基本单位不得为空")
    @ApiModelProperty("基本单位")
    String baseUnitName;

    @NotNull(message = "商品税率不得为空")
    @ApiModelProperty("商品税率")
    BigDecimal rate;

    @NotNull(message = "商品采购税率不得为空")
    @ApiModelProperty("商品采购税率")
    BigDecimal purchaseRate;

    @ApiModelProperty("税率编码")
    @Length(max = 50, message = "税率编码不得超过50个字符")
    String taxRateCode;

    @NotNull(message = "赠品判断不得为空")
    @ApiModelProperty("是否为赠品，1 为赠品。0 不为赠品")
    Integer isGift;

    @ApiModelProperty("如果此商品为赠品，关联到的主商品id")
    Long parentItemId;

    @ApiModelProperty("如果此商品为赠品，关联到的主商品code")
    String parentCode;

    @NotNull(message = "发货渠道不得为空")
    @ApiModelProperty("发货渠道。0.仓库发货。1.工厂发货")
    List<Integer> delivery;

    @ApiModelProperty("快递模板")
    private ExpressDto expresses;

    @ApiModelProperty("商品状态 0:待上架 1:在售中 2:已下架（永久）3:废弃")
    @NotNull
    private Integer status;

    @ApiModelProperty("状态备注")
    private String statusReason;

    @ApiModelProperty("采购，物流信息相关id")
    private Long procurementId;

    // --------------- 价格信息 --------------------

    @Valid
    @ApiModelProperty("采购成本list")
    List<ProcurementPriceDto> procurementPrices;

    @ApiModelProperty("日常售价list")
    @Valid
    List<SalesPriceDto> salesPrices;

    // ------------------ 指定编码 ---------------------

    @ApiModelProperty("供货商指定编码")
    String specialCode;

    // ------------------ 是否同步 ---------------------

//    @ApiModelProperty("是否同步 是:true 否:false")
//    Boolean confirm = false;

//    @ApiModelProperty("前端请无视")
//    Long buyerId;
//    @ApiModelProperty("前端请无视")
//    String buyerKingDeeId;

    @ApiModelProperty(value = "商品标签", notes = "DAD_SAMPLING_INSPECTION 老爸抽检, CUSTOMIZED_PRODUCTS 定制品")
    List<String> tags;

    @ApiModelProperty("商品简称")
    private String shortName;

    @ApiModelProperty("合作方业务类型")
    private List<CorpBizTypeDTO> corpBizType;

    @ApiModelProperty("下架时间")
    private Long downFrameTime;

    @ApiModelProperty("下架理由")
    private String downFrameReason;

}
