package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceSku;

import java.util.List;

/**
 * <p>
 * 入驻活动价格(SKU纬度) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-04
 */
public interface IEntryActivityPriceSkuService extends IDaddyService<EntryActivityPriceSku> {

    List<EntryActivityPriceSku> findTimeIntersectionSkuList(String skuCode, Long activeStart, Long activeEnd);
}
