package com.daddylab.supplier.item.infrastructure.tree;

import cn.hutool.core.comparator.CompareUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;

/**
 * 请注意由于子节点持有父节点的引用，请在序列化或者生成toString方法时务必注意要将getParent,getRoot等方法排除，否则会导致无限递归
 * @param <T> 主键类型
 * @param <C> 实现类类型
 */
public interface Tree<T, C extends Tree<T, C>> {
    /**
     * 获取ID
     *
     * @return ID
     */
    T getId();

    /**
     * 设置ID
     *
     * @param id ID
     */
    void setId(T id);

    /**
     * 获取父节点ID
     *
     * @return 父节点ID
     */
    T getParentId();

    /**
     * 设置parentId
     *
     * @param parentId ID
     */
    void setParentId(T parentId);

    @SuppressWarnings("unchecked")
    @JsonIgnore
    default void getRoot() {
        C root = (C) this;
        while (root.getParent() != null) {
            root = root.getParent();
        }
    }

    /**
     * 获取节点标签名称
     *
     * @return 节点标签名称
     */
    String getName();

    /**
     * 获取节点标签名称
     *
     * @param name 节点名称
     */
    void setName(String name);

    /**
     * @return 返回子节点列表
     */
    List<C> getChildren();

    /**
     * 设置当前树节点的子节点列表
     *
     * @param nodes 子节点列表
     */
    void setChildren(List<C> nodes);

    /**
     * 获取排序
     *
     * @return 排序
     */
    Integer getSort();

    /**
     * 设置排序
     */
    void setSort(Integer sort);

    @SuppressWarnings({"rawtypes"})
    default int compareTo(Tree tree) {
        if (null == tree) {
            return 1;
        }
        final Integer sort = this.getSort();
        final Integer sort1 = tree.getSort();
        return CompareUtil.compare(sort, sort1);
    }

    /**
     * @return 父节点
     */
    @JsonIgnore
    C getParent();

    /**
     * 设置parentId
     *
     * @param parent 父节点
     */
    void setParent(C parent);

    /**
     * 新增子节点
     *
     * @param tree 子节点
     */
    default void addChild(C tree) {
        getChildren().add(tree);
    }

    /**
     * 移除子节点
     *
     * @param tree 子节点
     */
    default void removeChild(C tree) {
        getChildren().remove(tree);
    }
}
