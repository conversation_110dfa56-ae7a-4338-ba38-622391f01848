package com.daddylab.supplier.item.domain.message.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/23 2:14 下午
 * @description
 */
@Data
@ApiModel("消息填充对象")
public class MsgFillObj {

    /**
     * 填充值 在模板中的下标
     */
    @ApiModelProperty("下标")
    Integer index;

    /**
     * 填充值
     */
    @ApiModelProperty("填充值")
    String val;

    /**
     * 此填充值关联的链接
     */
    @ApiModelProperty("链接")
    String link;

    @ApiModelProperty("填充字体颜色。css颜色属性。")
    String color;


}
