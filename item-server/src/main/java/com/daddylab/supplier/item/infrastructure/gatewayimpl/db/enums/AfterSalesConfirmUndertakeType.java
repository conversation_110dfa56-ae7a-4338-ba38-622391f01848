package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 承担类型 1:按百分比 2:按金额
 *
 * <AUTHOR>
 * @since 2022/10/20
 */
@Getter
@AllArgsConstructor
public enum AfterSalesConfirmUndertakeType implements IEnum<Integer> {

    /**
     * 1:按百分比
     */
    PERCENTAGE(1, "按百分比"),
    /**
     * 2:按金额
     */
    AMOUNT(2, "按金额"),
    ;
    @EnumValue
    private final Integer value;
    private final String desc;
}
