package com.daddylab.supplier.item.application.afterSalesForwarding.types;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/5/30
 */
@EqualsAndHashCode(callSuper = true)
@Data()
public class AfterSalesForwardingRegisterShareQuery extends PageQuery {
    private static final long serialVersionUID = 3655310513024859580L;
    private Long shareLinkId;
    private String token;
}
