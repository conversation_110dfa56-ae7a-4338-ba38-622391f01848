package com.daddylab.supplier.item.application.purchase.order.factory.dto;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.Getter;

/**
 * wdt订单清洗结果的数据类型
 *
 * <AUTHOR> up
 * @date 2022年08月19日 11:39 AM
 */
@Getter
public enum WrapperType implements IEnum<Integer> {
    /**
     * wdt订单清洗结果的数据类型
     */
    STOCK_OUT_SINGLE(1, "出库单品"),
    STOCK_OUT_COMBINATION(2, "来自组合装的出库单品"),
    STOCK_IN_REFUND(-1, "销售退货入库货品"),
    STOCK_IN_PRE(-2, "预入库商品"),

//    REFUND_SINGLE(-3, "退换管理单品"),
//    REFUND_COMBINATION(-4, "退换管理组合装"),

    ERROR(-9, "异常情况"),
    TOTAL(99, "日常价汇总信息");

//    ACTIVITY_DISCOUNT(98, "优惠汇总信息");

    @EnumValue
    private Integer value;
    private String desc;

    WrapperType(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
