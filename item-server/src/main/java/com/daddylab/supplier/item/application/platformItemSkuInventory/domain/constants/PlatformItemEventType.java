package com.daddylab.supplier.item.application.platformItemSkuInventory.domain.constants;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/4/2
 */
@AllArgsConstructor
@Getter
public enum PlatformItemEventType implements IIntegerEnum {
    ITEM_CHANGE(1, "商品变更"),
    STOCK_CHANGE(2, "库存变更"),
    ;
    private final Integer value;
    private final String desc;

}
