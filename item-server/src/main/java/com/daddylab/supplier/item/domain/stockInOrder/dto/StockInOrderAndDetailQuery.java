package com.daddylab.supplier.item.domain.stockInOrder.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.poi.hpsf.Decimal;
/**
 * <AUTHOR>
 * @date 2022/4/2 17:43
 **/
@Data
@ApiModel("查询炎黄盈动需求字段")
public class StockInOrderAndDetailQuery {
    @ApiModelProperty(value = "商品sku")
    private String itemSkuCode;
    @ApiModelProperty(value = "商品名称")
    private String itemName;
    @ApiModelProperty(value = "规格名称")
    private String specifications;
    @ApiModelProperty(value = "申请数量 = 采购数量")
    private Integer purchaseQuantity;
    @ApiModelProperty(value = "应付数量 = 入库数量")
    private Integer totalReceiptQuantity;
    @ApiModelProperty(value = "税额")
    private Decimal taxQuota;
    @ApiModelProperty(value = "税后金额")
    private Decimal afterTaxAmount;
}
