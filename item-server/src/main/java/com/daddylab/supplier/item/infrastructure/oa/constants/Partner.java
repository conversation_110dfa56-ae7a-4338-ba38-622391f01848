package com.daddylab.supplier.item.infrastructure.oa.constants;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 枚举，电商：9005488124879391011 绿色家装：937470142167662875
 *
 * <AUTHOR>
 * @since 2025/03/24
 */
@RequiredArgsConstructor
@Getter
public enum Partner implements IEnum<Long> {
  ECOMMERCE(9005488124879391011L, "电商"),
  GREEN_HOME(937470142167662875L, "绿色家装"),
  ;
  @JsonValue private final Long value;
  private final String desc;
}
