package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.JobHandle;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.JobHandleMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IJobHandleService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Service
public class JobHandleServiceImpl extends DaddyServiceImpl<JobHandleMapper, JobHandle> implements IJobHandleService {

}
