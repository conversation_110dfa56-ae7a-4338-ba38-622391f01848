package com.daddylab.supplier.item.application.item.combinationItem.dto.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/12 3:51 下午
 * @description
 */
@Data
@ApiModel("组合商品价格")
public class CombinationItemPriceVO{

    @ApiModelProperty("采购成本")
    private List<OneTypePriceVO> procurementPrices;

    @ApiModelProperty("日常销售价格")
    private List<OneTypePriceVO> salesPrices;

    @ApiModelProperty("合同销售价格历史")
    private List<OneTypePriceVO> contractSalePrices;

    @ApiModelProperty("平台佣金历史")
    private List<OneTypePriceVO> platformCommissions;
}
