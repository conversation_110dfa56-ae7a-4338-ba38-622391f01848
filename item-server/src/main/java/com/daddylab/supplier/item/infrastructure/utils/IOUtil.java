package com.daddylab.supplier.item.infrastructure.utils;

import cn.hutool.core.io.IoUtil;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import org.apache.commons.io.input.BOMInputStream;

public class IOUtil {
    public static byte[] readBytes(InputStream inputStream) {
        return IoUtil.readBytes(inputStream, true);
    }

    public static byte[] readBytes(InputStream inputStream, boolean isClose) {
        return IoUtil.readBytes(inputStream, isClose);
    }

    public static FileInputStream toInputStream(File file) {
        return IoUtil.toStream(file);
    }

    public static BufferedInputStream toBuffered(InputStream inputStream) {
        return IoUtil.toBuffered(inputStream, Integer.MAX_VALUE);
    }

    /**
     * 将输入流包装为可以自动过滤BOM头的BOMInputStream
     */
    public static BOMInputStream toBOMInputStream(InputStream inputStream) {
        return new BOMInputStream(inputStream);
    }
}
