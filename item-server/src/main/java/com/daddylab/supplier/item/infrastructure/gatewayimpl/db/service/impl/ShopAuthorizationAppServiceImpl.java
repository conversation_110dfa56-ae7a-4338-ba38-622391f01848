package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorizationApp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ShopAuthorizationAppMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopAuthorizationAppService;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <p>
 * 店铺授权应用 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
@Service
public class ShopAuthorizationAppServiceImpl extends DaddyServiceImpl<ShopAuthorizationAppMapper, ShopAuthorizationApp> implements IShopAuthorizationAppService {

    @Override
    public Optional<ShopAuthorizationApp> getByShopNo(String shopNo) {
        return lambdaQuery().eq(ShopAuthorizationApp::getShopSn, shopNo).oneOpt();
    }
}
