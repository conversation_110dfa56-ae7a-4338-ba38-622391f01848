package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsAbnormalityCs;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderLogisticsAbnormalityCsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsAbnormalityCsService;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 订单物流异常客服表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Service
public class OrderLogisticsAbnormalityCsServiceImpl
    extends DaddyServiceImpl<OrderLogisticsAbnormalityCsMapper, OrderLogisticsAbnormalityCs>
    implements IOrderLogisticsAbnormalityCsService {

  @Override
  public List<OrderLogisticsAbnormalityCs> listByAbnormalityId(Long abnormalityId) {
    if (abnormalityId == null || abnormalityId == 0) {
      return Collections.emptyList();
    }
    return lambdaQuery().eq(OrderLogisticsAbnormalityCs::getAbnormalityId, abnormalityId).list();
  }

  @Override
  public List<OrderLogisticsAbnormalityCs> listByAbnormalityIds(List<Long> abnormalityIds) {
    if (CollectionUtil.isEmpty(abnormalityIds)) {
      return Collections.emptyList();
    }
    return lambdaQuery().in(OrderLogisticsAbnormalityCs::getAbnormalityId, abnormalityIds).list();
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void distribute(Long abnormalId, List<Long> csUserIds) {
    distribute(abnormalId, csUserIds, false);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void distribute(Long abnormalId, List<Long> csUserIds, boolean init) {
    final List<OrderLogisticsAbnormalityCs> csList = listByAbnormalityId(abnormalId);
    if (init && !csList.isEmpty()) {
      return;
    }
    final Set<Long> csUserIdsExits =
        csList.stream().map(OrderLogisticsAbnormalityCs::getCsUserId).collect(Collectors.toSet());
    final LinkedList<Long> csUserIdsToAdd = new LinkedList<>(csUserIds);
    csUserIdsToAdd.removeAll(csUserIdsExits);
    if (!csUserIdsToAdd.isEmpty()) {
      final List<OrderLogisticsAbnormalityCs> csPoList =
          csUserIdsToAdd.stream()
              .map(
                  id -> {
                    final OrderLogisticsAbnormalityCs orderLogisticsAbnormalityCs =
                        new OrderLogisticsAbnormalityCs();
                    orderLogisticsAbnormalityCs.setAbnormalityId(abnormalId);
                    orderLogisticsAbnormalityCs.setCsUserId(id);
                    return orderLogisticsAbnormalityCs;
                  })
              .collect(Collectors.toList());
      saveBatch(csPoList);
    }
    for (OrderLogisticsAbnormalityCs abnormalityCs : csList) {
      if (!csUserIds.contains(abnormalityCs.getCsUserId())) {
        removeById(abnormalityCs.getId());
      }
    }
  }
}
