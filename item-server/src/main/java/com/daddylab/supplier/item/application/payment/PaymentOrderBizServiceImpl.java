package com.daddylab.supplier.item.application.payment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.oa.OaProcessService;
import com.daddylab.supplier.item.application.order.settlement.OrderSettlementBizService;
import com.daddylab.supplier.item.application.order.settlement.dto.SettlementOrderPageQuery;
import com.daddylab.supplier.item.application.order.settlement.dto.SettlementOrderPageVo;
import com.daddylab.supplier.item.application.order.settlement.sys.DetailStaticDo;
import com.daddylab.supplier.item.application.payment.dto.*;
import com.daddylab.supplier.item.application.payment.wrietoff.WriteOffBizService;
import com.daddylab.supplier.item.application.provider.ProviderBizService;
import com.daddylab.supplier.item.application.purchase.order.OrderMap;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizService;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizServiceImpl;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderConfig;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.domain.auth.enums.ResourceType;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.diff.CollChange;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuNameDo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemSkuMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PaymentApplyOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLock;
import com.daddylab.supplier.item.infrastructure.oa.OaFormAttachment;
import com.daddylab.supplier.item.infrastructure.oa.OaProcessStartResponse;
import com.daddylab.supplier.item.infrastructure.oa.OaResponse;
import com.daddylab.supplier.item.infrastructure.oa.OaUploadAttachmentResponse;
import com.daddylab.supplier.item.infrastructure.oa.cgfk.OaCgfkData;
import com.daddylab.supplier.item.infrastructure.oa.cgfk.OaCgfkDetail;
import com.daddylab.supplier.item.infrastructure.oa.cgfk.OaCgfkInvoice;
import com.daddylab.supplier.item.infrastructure.oa.cgfk.OaCgfkMainForm;
import com.daddylab.supplier.item.infrastructure.oa.constants.Partner;
import com.daddylab.supplier.item.infrastructure.oa.constants.PaymentPurpose;
import com.daddylab.supplier.item.infrastructure.oa.constants.PaymentStatus;
import com.daddylab.supplier.item.infrastructure.oa.constants.ProcurementType;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.daddylab.supplier.item.types.oa.OaCgfkCallback;
import com.daddylab.supplier.item.types.process.ProcessBusinessType;
import com.daddylab.supplier.item.types.process.ProcessType;
import com.fasterxml.jackson.databind.JsonNode;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import feign.FeignException;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.javers.core.ChangesByObject;
import org.javers.core.diff.Diff;
import org.javers.core.diff.changetype.*;
import org.javers.core.metamodel.object.GlobalId;
import org.javers.core.metamodel.object.InstanceId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> up
 * @date 2023年11月20日 3:18 PM
 */
@Service
@Slf4j
public class PaymentOrderBizServiceImpl implements PaymentOrderBizService {

  public static final String OA_PROCESS_TERMINAL = "TERMINAL";
  @Resource IPaymentApplyOrderService iPaymentApplyOrderService;

  @Resource IProviderService iProviderService;

  @Autowired ProviderGateway providerGateway;

  @Autowired IBankAccountService bankAccountService;

  @Resource
  @Qualifier("UserGatewayCacheImpl")
  UserGateway userGateway;

  @Autowired StaffService staffService;

  @Resource IPaymentApplyOrderDetailService iPaymentApplyOrderDetailService;

  @Resource IPurchaseOrderService iPurchaseOrderService;

  @Resource IPurchaseOrderDetailService iPurchaseOrderDetailService;

  @Resource IItemService iItemService;

  @Resource IOrderSettlementFormService iOrderSettlementFormService;

  @Resource IOrderSettlementDetailService iOrderSettlementDetailService;

  @Resource ItemSkuMapper itemSkuMapper;

  @Resource IItemSkuService iItemSkuService;

  @Resource PaymentApplyOrderMapper paymentApplyOrderMapper;

  @Resource PaymentApplyDao paymentApplyDao;

  @Resource KingDeeTemplate kingDeeTemplate;

  @Resource IFileService iFileService;

  @Autowired OaProcessService oaProcessService;

  @Autowired PaymentApplyConfig paymentApplyConfig;

  @Autowired IProcessInstRefService processInstRefService;

  @Autowired IOaCallbackService oaCallbackService;

  @Autowired RefreshConfig refreshConfig;

  @Autowired FileGateway fileGateway;

  @Autowired OperateLogGateway operateLogGateway;

  @Autowired ProviderBizService providerBizService;

  @Resource IExportTaskService iExportTaskService;

  @Resource PurchaseOrderBizService purchaseOrderBizService;

  @Resource OrderSettlementBizService orderSettlementBizService;

  @Resource WriteOffBizService writeOffBizService;

  @Resource IPaymentOrderWriteOffLogService iPaymentOrderWriteOffLogService;

  @Resource IBizLevelDivisionService iBizLevelDivisionService;
  @Resource PurchaseOrderConfig purchaseOrderConfig;

  /** 付款单锁定状态。 1.在锁定状态下，不允许更改单据信息 2.单据划归到已申请 */
  public static ImmutableList<PaymentOrderStatus> LOCKED_STATUS_LIST =
      ImmutableList.of(
          PaymentOrderStatus.WAIT_AUDIT,
          PaymentOrderStatus.WAIT_PAY,
          PaymentOrderStatus.FINISH,
          PaymentOrderStatus.SYNC_KING_DEE_ERROR);

  @Override
  public PageResponse<PaymentPageVo> page(PaymentPageQuery pageQuery) {
    if (CollectionUtil.isEmpty(pageQuery.getBusinessLine())) {
      return ResponseFactory.emptyPage();
    }
    List<Long> matchIdList = new LinkedList<>();
    if (StrUtil.isNotBlank(pageQuery.getRelatedNo())) {
      matchIdList =
          iPaymentApplyOrderDetailService
              .lambdaQuery()
              .eq(PaymentApplyOrderDetail::getApplyRelatedNo, pageQuery.getRelatedNo())
              .list()
              .stream()
              .map(PaymentApplyOrderDetail::getPaymentApplyOrderId)
              .collect(Collectors.toList());
      if (CollUtil.isEmpty(matchIdList)) {
        return ResponseFactory.emptyPage();
      }
    }

    boolean viewAll;
    if (Objects.equals(pageQuery.getViewAll(), 1)) {
      viewAll = true;
    } else {
      viewAll =
          UserContext.hasPermission(GlobalConstant.PAYMENT_APPLY_VIEW_ADMIN_API, ResourceType.API);
    }

    List<Long> finalMatchIdList = matchIdList;
    PageInfo<PaymentApplyOrder> paymentApplyOrderPageInfo =
        PageHelper.startPage(pageQuery.getPageIndex(), pageQuery.getPageSize())
            .doSelectPageInfo(
                () -> {
                  iPaymentApplyOrderService
                      .lambdaQuery()
                      .eq(
                          StrUtil.isNotBlank(pageQuery.getNo()),
                          PaymentApplyOrder::getNo,
                          pageQuery.getNo())
                      .eq(
                          Objects.nonNull(pageQuery.getPurchaseType()),
                          PaymentApplyOrder::getPurchaseType,
                          pageQuery.getPurchaseType())
                      .ge(
                          Objects.nonNull(pageQuery.getApplyTimeStart()),
                          PaymentApplyOrder::getApplyTime,
                          pageQuery.getApplyTimeStart())
                      .le(
                          Objects.nonNull(pageQuery.getApplyTimeEnd()),
                          PaymentApplyOrder::getApplyTime,
                          pageQuery.getApplyTimeEnd())
                      .in(
                          CollUtil.isNotEmpty(pageQuery.getPayeeUnit()),
                          PaymentApplyOrder::getPayeeUnit,
                          pageQuery.getPayeeUnit())
                      .eq(
                          StrUtil.isNotBlank(pageQuery.getPayOrg()),
                          PaymentApplyOrder::getPayOrg,
                          pageQuery.getPayOrg())
                      .ge(
                          Objects.nonNull(pageQuery.getExpectedPayTimeStart()),
                          PaymentApplyOrder::getExpectedPayTime,
                          pageQuery.getExpectedPayTimeStart())
                      .le(
                          Objects.nonNull(pageQuery.getExpectedPayTimeEnd()),
                          PaymentApplyOrder::getExpectedPayTime,
                          pageQuery.getExpectedPayTimeEnd())
                      .in(
                          CollUtil.isNotEmpty(pageQuery.getBuyerId()),
                          PaymentApplyOrder::getBuyerId,
                          pageQuery.getBuyerId())
                      .in(
                          CollUtil.isNotEmpty(finalMatchIdList),
                          PaymentApplyOrder::getId,
                          finalMatchIdList)
                      .in(
                          CollUtil.isNotEmpty(pageQuery.getIds()),
                          PaymentApplyOrder::getId,
                          pageQuery.getIds())
                      .and(
                          !viewAll,
                          ww ->
                              ww.eq(PaymentApplyOrder::getCreatedUid, UserContext.getUserId())
                                  .or()
                                  .eq(PaymentApplyOrder::getBuyerId, UserContext.getUserId()))
                      .eq(
                          Objects.nonNull(pageQuery.getStatus()),
                          PaymentApplyOrder::getStatus,
                          pageQuery.getStatus())
                      .and(
                          Objects.nonNull(pageQuery.getFinishPayStartTime())
                              && Objects.nonNull(pageQuery.getFinishPayEndTime()),
                          ww ->
                              ww.eq(PaymentApplyOrder::getStatus, PaymentOrderStatus.FINISH)
                                  .ge(
                                      PaymentApplyOrder::getLastAuditTime,
                                      pageQuery.getFinishPayStartTime())
                                  .le(
                                      PaymentApplyOrder::getLastAuditTime,
                                      pageQuery.getFinishPayEndTime()))
                      .in(
                          CollectionUtil.isNotEmpty(pageQuery.getBusinessLine()),
                          PaymentApplyOrder::getBusinessLine,
                          pageQuery.getBusinessLine())
                      .orderByDesc(PaymentApplyOrder::getId)
                      .list();
                });
    if (paymentApplyOrderPageInfo.getSize() == 0) {
      return ResponseFactory.emptyPage(pageQuery);
    }

    Set<Long> payeeUnitSet =
        paymentApplyOrderPageInfo.getList().stream()
            .map(PaymentApplyOrder::getPayeeUnit)
            .collect(Collectors.toSet());
    final List<Provider> providers =
        iProviderService.lambdaQuery().in(Provider::getId, payeeUnitSet).list();
    Map<Long, String> providerMap =
        providers.stream()
            .collect(Collectors.toMap(Provider::getId, Provider::getName, (a, b) -> a));
    List<Long> buyerUserIdSet =
        paymentApplyOrderPageInfo.getList().stream()
            .map(PaymentApplyOrder::getBuyerId)
            .distinct()
            .collect(Collectors.toList());
    Map<Long, StaffInfo> longStaffInfoMap = userGateway.batchQueryStaffInfoByIds(buyerUserIdSet);
    final Set<Long> partnerProviderIds =
        providers.stream().map(Provider::getPartnerProviderId).collect(Collectors.toSet());
    final Map<Long, PartnerProviderResp> longPartnerProviderRespMap =
        providerGateway.partnerBatchQueryByIds(partnerProviderIds);

    List<Long> creatorUserIdSet =
        paymentApplyOrderPageInfo.getList().stream()
            .map(PaymentApplyOrder::getCreatedUid)
            .distinct()
            .collect(Collectors.toList());
    Map<Long, StaffInfo> creatorMap = userGateway.batchQueryStaffInfoByIds(creatorUserIdSet);

    List<PaymentPageVo> collect =
        paymentApplyOrderPageInfo.getList().stream()
            .map(
                val -> {
                  PaymentPageVo paymentPageVo = PaymentApplyTransMapper.INSTANCE.dbToVo(val);
                  paymentPageVo.setPayeeUnitStr(
                      providerMap.getOrDefault(paymentPageVo.getPayeeUnit(), StrUtil.EMPTY));
                  final Integer payeeUnitIsBlacklist =
                      Optional.ofNullable(
                              longPartnerProviderRespMap.get(paymentPageVo.getPayeeUnit()))
                          .map(PartnerProviderResp::getIsBlacklist)
                          .orElse(0);
                  paymentPageVo.setPayeeUnitIsBlacklist(payeeUnitIsBlacklist);
                  StaffInfo staffInfo = longStaffInfoMap.get(paymentPageVo.getBuyerId());
                  if (Objects.nonNull(staffInfo)) {
                    paymentPageVo.setBuyerName(staffInfo.getNickname());
                  }
                  StaffInfo creator = creatorMap.get(val.getCreatedUid());
                  if (Objects.nonNull(creator)) {
                    paymentPageVo.setApplyUser(creator.getNickname());
                  }

                  List<PaymentApplyOrderDetail> orderDetails =
                      iPaymentApplyOrderDetailService
                          .lambdaQuery()
                          .eq(PaymentApplyOrderDetail::getPaymentApplyOrderId, val.getId())
                          .list();
                  // 应付金额
                  BigDecimal totalRightAmount =
                      orderDetails.stream()
                          .map(PaymentApplyOrderDetail::getRightAmount)
                          .reduce(BigDecimal.ZERO, BigDecimal::add);
                  paymentPageVo.setRightAmount(totalRightAmount);
                  // 申请付款金额
                  BigDecimal totalApplyAmount =
                      orderDetails.stream()
                          .map(PaymentApplyOrderDetail::getApplyAmount)
                          .reduce(BigDecimal.ZERO, BigDecimal::add);
                  paymentPageVo.setApplyAmount(totalApplyAmount);
                  paymentPageVo.setFinishPayTime(
                      DateUtil.format(val.getLastAuditTime(), DatePattern.NORM_DATETIME_PATTERN));
                  return paymentPageVo;
                })
            .collect(Collectors.toList());

    return PageResponse.of(
        collect,
        (int) paymentApplyOrderPageInfo.getTotal(),
        pageQuery.getPageSize(),
        pageQuery.getPageIndex());
  }

  @Override
  public SingleResponse<PaymentApplyOrderViewVo> view(Long id) {
    PaymentApplyOrder paymentApplyOrder = iPaymentApplyOrderService.getById(id);
    Assert.notNull(paymentApplyOrder, "id非法");

    checkViewPermission(paymentApplyOrder);

    return viewProcess(paymentApplyOrder);
  }

  private void checkViewPermission(PaymentApplyOrder paymentApplyOrder) {
    boolean viewData =
        UserContext.hasPermission(GlobalConstant.PAYMENT_APPLY_VIEW_ADMIN_API, ResourceType.API);
    if (!viewData) {
      boolean b1 = paymentApplyOrder.getCreatedUid().equals(UserContext.getUserId());
      boolean b2 = paymentApplyOrder.getBuyerId().equals(UserContext.getUserId());
      Assert.state(b1 || b2, "你没有管理员角色权限，因此你只能查看自己创建或者负责采购的付款单信息");
    }
  }

  private SingleResponse<PaymentApplyOrderViewVo> viewProcess(PaymentApplyOrder paymentApplyOrder) {
    PaymentApplyOrderViewVo paymentApplyOrderVo =
        PaymentApplyTransMapper.INSTANCE.dbToViewVo(paymentApplyOrder);
    Long tradeUnit = paymentApplyOrderVo.getTradeUnit();
    Provider provider = iProviderService.getById(tradeUnit);
    if (Objects.nonNull(provider)) {
      paymentApplyOrderVo.setTradeUnitStr(provider.getName());
      paymentApplyOrderVo.setPayeeUnitStr(provider.getName());

      final Optional<PartnerProviderResp> partnerProviderResp =
          Optional.ofNullable(provider.getPartnerProviderId())
              .filter(NumberUtil::isPositive)
              .flatMap(providerGateway::partnerQueryById);
      final Integer isBlacklist =
          partnerProviderResp.map(PartnerProviderResp::getIsBlacklist).orElse(0);
      paymentApplyOrderVo.setTradeUnitIsBlacklist(isBlacklist);
      paymentApplyOrderVo.setPayeeUnitIsBlacklist(isBlacklist);
    }

    CompletableFuture<Void> userInfoFuture =
        CompletableFuture.runAsync(
            () -> {
              List<Long> userIds =
                  ListUtil.of(paymentApplyOrderVo.getBuyerId(), paymentApplyOrderVo.getCreatedUid())
                      .stream()
                      .distinct()
                      .collect(Collectors.toList());
              Map<Long, StaffInfo> longStaffInfoMap = userGateway.batchQueryStaffInfoByIds(userIds);
              StaffInfo buyerStaffInfo = longStaffInfoMap.get(paymentApplyOrderVo.getBuyerId());
              if (Objects.nonNull(buyerStaffInfo)) {
                paymentApplyOrderVo.setBuyerName(buyerStaffInfo.getNickname());
                paymentApplyOrderVo.setBuyerDept(buyerStaffInfo.getDept());
              }
              StaffInfo createdStaffInfo =
                  longStaffInfoMap.get(paymentApplyOrderVo.getCreatedUid());
              if (Objects.nonNull(createdStaffInfo)) {
                paymentApplyOrderVo.setCreatedUser(createdStaffInfo.getNickname());
              }
            },
            ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL));

    CompletableFuture<Void> additionalFuture =
        CompletableFuture.runAsync(
            () -> {
              String additionalIds = paymentApplyOrder.getAdditionalIds();
              if (StrUtil.isNotBlank(additionalIds)) {
                List<Long> additionalIdList =
                    Arrays.stream(additionalIds.split(StrUtil.COMMA))
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
                List<File> additionalList =
                    iFileService.lambdaQuery().in(File::getId, additionalIdList).list();
                List<AdditionalVo> additionalVoList =
                    additionalList.stream()
                        .map(
                            file -> {
                              AdditionalVo additionalVo = new AdditionalVo();
                              additionalVo.setName(file.getName());
                              additionalVo.setPath(file.getPath());
                              additionalVo.setFileId(file.getId());
                              return additionalVo;
                            })
                        .collect(Collectors.toList());
                paymentApplyOrderVo.setAdditionalList(additionalVoList);
              }
            },
            ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL));

    CompletableFuture.allOf(userInfoFuture, additionalFuture).join();

    String oaPrefix =
        "https://oa.dlab.cn/seeyon/collaboration/collaboration.do?method=summary&openFrom=listPending&affairId=";
    // https://oa.dlab.cn/seeyon/collaboration/collaboration.do?method=summary&openFrom=listPending&affairId=6950523196196866246
    paymentApplyOrderVo.setOaProcessUrl(oaPrefix + paymentApplyOrder.getOaProcessAffairId());

    // 拼接审核流信息
    PaymentOrderStatus status = paymentApplyOrder.getStatus();
    List<OaAuditNode> oaAuditNodeList = new LinkedList<>();
    Long createdUid = paymentApplyOrder.getCreatedUid();
    DadStaffVO staff = staffService.getStaff(createdUid);
    String createUser = Objects.isNull(staff) ? "nobody" : staff.getNickname();

    switch (status) {
      case WAIT_SUBMIT:
        oaAuditNodeList.add(getWaitSubmitNode(paymentApplyOrder, createUser));
        break;
      case WAIT_AUDIT:
      case WAIT_PAY:
      case AUDIT_REJECT:
        oaAuditNodeList.add(getWaitSubmitNode(paymentApplyOrder, createUser));
        oaAuditNodeList.add(getWaitAuditNode(paymentApplyOrder, createUser));
        oaAuditNodeList.add(getLastAuditNode(paymentApplyOrder, false));
        break;
      case FINISH:
      case PAY_ERROR:
      case SYNC_KING_DEE_ERROR:
        oaAuditNodeList.add(getWaitSubmitNode(paymentApplyOrder, createUser));
        oaAuditNodeList.add(getWaitAuditNode(paymentApplyOrder, createUser));
        oaAuditNodeList.add(getLastAuditNode(paymentApplyOrder, true));
        oaAuditNodeList.add(getFinishedNode(paymentApplyOrder));
        break;
      default:
        break;
    }
    paymentApplyOrderVo.setOaAuditNodes(oaAuditNodeList);

    return SingleResponse.of(paymentApplyOrderVo);
  }

  private OaAuditNode getWaitSubmitNode(PaymentApplyOrder order, String createUser) {

    OaAuditNode waitSubmitOaAuditNode = new OaAuditNode();
    waitSubmitOaAuditNode.setName(createUser);
    waitSubmitOaAuditNode.setTime(
        DateUtil.parseTimeStamp(order.getCreatedAt(), DatePattern.NORM_DATETIME_PATTERN));
    waitSubmitOaAuditNode.setStatus(PaymentOrderStatus.WAIT_SUBMIT.getDesc());
    return waitSubmitOaAuditNode;
  }

  private OaAuditNode getWaitAuditNode(PaymentApplyOrder order, String createUser) {
    OaAuditNode waitAuditOaAuditNode = new OaAuditNode();
    waitAuditOaAuditNode.setName(createUser);
    waitAuditOaAuditNode.setTime(
        DateUtil.parseTimeStamp(order.getApplyTime(), DatePattern.NORM_DATETIME_PATTERN));
    waitAuditOaAuditNode.setStatus(PaymentOrderStatus.WAIT_AUDIT.getDesc());
    return waitAuditOaAuditNode;
  }

  private OaAuditNode getLastAuditNode(PaymentApplyOrder order, boolean isWaitPay) {
    Long uid = order.getLastAuditUid();
    DadStaffVO staff = staffService.getStaff(uid);
    String user = Objects.isNull(staff) ? "" : staff.getNickname();

    OaAuditNode waitPayOaAuditNode = new OaAuditNode();
    waitPayOaAuditNode.setName(user);
    waitPayOaAuditNode.setTime(
        DateUtil.parseTimeStamp(order.getLastAuditTime(), DatePattern.NORM_DATETIME_PATTERN));
    final PaymentOrderStatus status = order.getStatus();
    waitPayOaAuditNode.setStatus(
        isWaitPay
            ? PaymentOrderStatus.WAIT_PAY.getDesc()
            : (status == PaymentOrderStatus.WAIT_AUDIT ? "审核中" : status.getDesc()));
    return waitPayOaAuditNode;
  }

  private OaAuditNode getFinishedNode(PaymentApplyOrder order) {
    Long uid = order.getPaymentCashier();
    DadStaffVO staff = staffService.getStaff(uid);
    String user = Objects.isNull(staff) ? "nobody" : staff.getNickname();

    OaAuditNode waitPayOaAuditNode = new OaAuditNode();
    waitPayOaAuditNode.setName(user);
    waitPayOaAuditNode.setTime(
        DateUtil.parseTimeStamp(order.getPaymentCallbackTime(), DatePattern.NORM_DATETIME_PATTERN));
    waitPayOaAuditNode.setStatus(order.getStatus().getDesc());
    return waitPayOaAuditNode;
  }

  @Override
  public MultiResponse<PaymentApplyDetailVo> viewDetail(Long id) {
    PaymentApplyOrder paymentApplyOrder = iPaymentApplyOrderService.getById(id);
    Assert.notNull(paymentApplyOrder, "id非法");

    checkViewPermission(paymentApplyOrder);

    return viewDetailProcess(id, paymentApplyOrder);
  }

  private MultiResponse<PaymentApplyDetailVo> viewDetailProcess(
      Long id, PaymentApplyOrder paymentApplyOrder) {
    List<PaymentApplyDetailVo> resList = new LinkedList<>();

    List<PaymentApplyOrderDetail> paymentApplyOrderDetails =
        iPaymentApplyOrderDetailService
            .lambdaQuery()
            .eq(PaymentApplyOrderDetail::getPaymentApplyOrderId, id)
            .list();
    Integer detailSource = paymentApplyOrder.getDetailSource();

    for (PaymentApplyOrderDetail paymentApplyOrderDetail : paymentApplyOrderDetails) {

      PaymentDetailBaseInfoVo baseInfoVo = new PaymentDetailBaseInfoVo();
      baseInfoVo.setId(paymentApplyOrderDetail.getId());
      baseInfoVo.setNo(paymentApplyOrderDetail.getApplyRelatedNo());
      baseInfoVo.setRightAmount(paymentApplyOrderDetail.getRightAmount());
      baseInfoVo.setApplyAmount(paymentApplyOrderDetail.getApplyAmount());
      baseInfoVo.setApplyAmountRemark(paymentApplyOrderDetail.getApplyAmountRemark());

      // 标准采购-来源采购单的明细
      if (0 == detailSource) {
        final PurchaseOrder purchaseOrder =
            iPurchaseOrderService.getById(paymentApplyOrderDetail.getApplyRelatedId());
        Assert.notNull(purchaseOrder, "采购单id非法");
        baseInfoVo.setRelateOrderId(purchaseOrder.getId());
        baseInfoVo.setDateStr(DateUtil.format(purchaseOrder.getPurchaseDate(), "yyyy-MM"));

        // 如果是预付款的单子，直接展示采购单明细
        if (OrderMap.PAY_PROPORTION.containsKey(purchaseOrder.getPayType())) {
          baseInfoVo.setOrderAmount(new BigDecimal(purchaseOrder.getTotalPurchaseAmount()));
          List<PurchaseListViewVo> purchaseViewList = getPurchaseViewList(paymentApplyOrderDetail);
          PaymentApplyDetailPurchaseVo vo = new PaymentApplyDetailPurchaseVo();
          vo.setPurchaseListViewVoList(purchaseViewList);
          vo.setBaseInfoVo(baseInfoVo);
          resList.add(vo);
          continue;
        }

        // 此采购单下，所有SKU的实际出入库单价，总额，数量。
        final Map<String, PurchaseOrderBizServiceImpl.SkuStockDto> skuRealStockInMap =
            purchaseOrderBizService.skuRealStockInMap(purchaseOrder.getId());
        final Map<String, PurchaseOrderBizServiceImpl.SkuStockDto> skuRealStockOutMap =
            purchaseOrderBizService.skuRealStockOutMap(purchaseOrder.getId());
        // 此采购单已经付款金额（包括待审核等锁定状态）
        final BigDecimal alreadyPayAmount =
            purchaseOrderBizService.alreadyPayAmount(purchaseOrder.getId());
        // 判断此采购单最大付款金额
        final BigDecimal orderAmount =
            skuRealStockInMap.values().stream()
                .map(PurchaseOrderBizServiceImpl.SkuStockDto::getTotalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .subtract(
                    skuRealStockOutMap.values().stream()
                        .map(PurchaseOrderBizServiceImpl.SkuStockDto::getTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
        baseInfoVo.setOrderAmount(orderAmount);

        // 已付金额大于采购单应付总金额。
        boolean allPay = alreadyPayAmount.compareTo(orderAmount) >= 0;
        if (allPay) {
          String purchaseDetailIdStr = paymentApplyOrderDetail.getRelateDetailId();
          Assert.hasText(purchaseDetailIdStr, "采购单明细id不得为空");
          List<Long> purchaseDetailIdList =
              Stream.of(purchaseDetailIdStr.split(StrUtil.COMMA))
                  .map(Long::valueOf)
                  .collect(Collectors.toList());
          Assert.notEmpty(purchaseDetailIdList, "采购单明细id非法");
          final List<PurchaseOrderDetail> relatedSkuDetailList =
              iPurchaseOrderDetailService
                  .lambdaQuery()
                  .in(PurchaseOrderDetail::getId, purchaseDetailIdList)
                  .list();
          Set<Long> itemIdSet =
              relatedSkuDetailList.stream()
                  .map(PurchaseOrderDetail::getItemId)
                  .collect(Collectors.toSet());
          Map<Long, String> itemIdAndNameMap = new HashMap<>(8);
          if (CollUtil.isNotEmpty(itemIdSet)) {
            itemIdAndNameMap =
                iItemService.lambdaQuery().in(Item::getId, itemIdSet).list().stream()
                    .collect(Collectors.toMap(Item::getId, Item::getName, (a, b) -> a));
          }

          List<PurchaseListViewVo> purchaseViewList = new ArrayList<>();
          for (PurchaseOrderDetail val : relatedSkuDetailList) {
            final PurchaseOrderBizServiceImpl.SkuStockDto inDto =
                skuRealStockInMap.get(val.getItemSkuCode());
            final PurchaseOrderBizServiceImpl.SkuStockDto outDto =
                skuRealStockOutMap.get(val.getItemSkuCode());

            Integer inQuantity = Objects.isNull(inDto) ? 0 : inDto.getQuantity();
            Integer outQuantity = Objects.isNull(outDto) ? 0 : outDto.getQuantity();
            final int gapQuantity = inQuantity - outQuantity;
            if (gapQuantity == 0) continue;
            BigDecimal inAmount = Objects.isNull(inDto) ? BigDecimal.ZERO : inDto.getTotalAmount();
            BigDecimal outAmount =
                Objects.isNull(outDto) ? BigDecimal.ZERO : outDto.getTotalAmount();
            final BigDecimal gapAmount = inAmount.subtract(outAmount);
            BigDecimal price =
                gapAmount.divide(new BigDecimal(Math.abs(gapQuantity)), 2, RoundingMode.HALF_UP);

            PurchaseListViewVo vo = new PurchaseListViewVo();
            vo.setId(val.getId());
            vo.setSkuCode(val.getItemSkuCode());
            vo.setItemName(itemIdAndNameMap.getOrDefault(val.getItemId(), ""));
            vo.setSpecifications(val.getSpecifications());
            vo.setPrice(price);
            vo.setQuantity(inQuantity - outQuantity);
            vo.setAmount(inAmount.subtract(outAmount).setScale(2, RoundingMode.HALF_UP));
            purchaseViewList.add(vo);
          }

          PaymentApplyDetailPurchaseVo vo = new PaymentApplyDetailPurchaseVo();
          vo.setPurchaseListViewVoList(purchaseViewList);
          vo.setBaseInfoVo(baseInfoVo);
          resList.add(vo);
        } else {
          List<PurchaseListViewVo> purchaseViewList = getPurchaseViewList(paymentApplyOrderDetail);
          PaymentApplyDetailPurchaseVo vo = new PaymentApplyDetailPurchaseVo();
          vo.setPurchaseListViewVoList(purchaseViewList);
          vo.setBaseInfoVo(baseInfoVo);
          resList.add(vo);
        }
      }

      if (1 == detailSource) {
        final OrderSettlementForm orderSettlementForm =
            iOrderSettlementFormService.getByNo(paymentApplyOrderDetail.getApplyRelatedNo());
        baseInfoVo.setRelateOrderId(orderSettlementForm.getId());
        String staticInfo = orderSettlementForm.getStaticInfo();
        DetailStaticDo detailStaticDo = JsonUtil.parse(staticInfo, DetailStaticDo.class);
        assert detailStaticDo != null;
        baseInfoVo.setOrderAmount(detailStaticDo.getTotalFinalAmount());
        LocalDateTime localDateTime =
            DateUtil.parseTimeStamp(orderSettlementForm.getSettlementStartDate());
        baseInfoVo.setDateStr(
            DateUtil.polymerizationTime(Collections.singletonList(localDateTime)));

        List<SettlementListViewVo> settlementViewList =
            getSettlementViewList(paymentApplyOrderDetail);
        PaymentApplyDetailSettlementVo vo = new PaymentApplyDetailSettlementVo();
        vo.setBaseInfoVo(baseInfoVo);
        vo.setSettlementListViewVoList(settlementViewList);
        resList.add(vo);
      }
    }

    return MultiResponse.of(resList);
  }

  @Deprecated
  private List<PurchaseListViewVo> getPurchaseViewList(
      PaymentApplyOrderDetail paymentApplyOrderDetails) {
    List<PurchaseListViewVo> resList = new LinkedList<>();

    String relateDetailId = paymentApplyOrderDetails.getRelateDetailId();
    if (StrUtil.isBlank(relateDetailId)) {
      return resList;
    }
    List<Long> relateDetailIds =
        Arrays.stream(relateDetailId.split(StrUtil.COMMA))
            .map(Long::valueOf)
            .collect(Collectors.toList());
    List<PurchaseOrderDetail> purchaseOrderDetailList =
        iPurchaseOrderDetailService
            .lambdaQuery()
            .in(PurchaseOrderDetail::getId, relateDetailIds)
            .list();
    if (CollUtil.isEmpty(purchaseOrderDetailList)) {
      return resList;
    }

    Set<Long> itemIdSet =
        purchaseOrderDetailList.stream()
            .map(PurchaseOrderDetail::getItemId)
            .collect(Collectors.toSet());
    Map<Long, String> itemIdAndNameMap = new HashMap<>(8);
    if (CollUtil.isNotEmpty(itemIdSet)) {
      itemIdAndNameMap =
          iItemService.lambdaQuery().in(Item::getId, itemIdSet).list().stream()
              .collect(Collectors.toMap(Item::getId, Item::getName, (a, b) -> a));
    }

    final Set<String> skuCodeSet =
        purchaseOrderDetailList.stream()
            .map(PurchaseOrderDetail::getItemSkuCode)
            .collect(Collectors.toSet());
    final Map<String, List<CorpBizTypeDTO>> divisionMap =
        iBizLevelDivisionService.queryBySkuCode(skuCodeSet);

    for (PurchaseOrderDetail purchaseOrderDetail : purchaseOrderDetailList) {
      PurchaseListViewVo vo = new PurchaseListViewVo();
      vo.setId(purchaseOrderDetail.getId());
      vo.setSkuCode(purchaseOrderDetail.getItemSkuCode());
      vo.setItemName(itemIdAndNameMap.getOrDefault(purchaseOrderDetail.getItemId(), "UNKNOWN"));
      vo.setSpecifications(purchaseOrderDetail.getSpecifications());
      vo.setPrice(purchaseOrderDetail.getTaxPrice());
      vo.setQuantity(purchaseOrderDetail.getPurchaseQuantity());
      vo.setAmount(purchaseOrderDetail.getTotalPriceTax());
      vo.setCorpBizType(divisionMap.get(purchaseOrderDetail.getItemSkuCode()));
      resList.add(vo);
    }

    return resList;
  }

  private List<SettlementListViewVo> getSettlementViewList(
      PaymentApplyOrderDetail paymentApplyOrderDetails) {
    List<SettlementListViewVo> resList = new LinkedList<>();

    String relateDetailId = paymentApplyOrderDetails.getRelateDetailId();
    if (StrUtil.isBlank(relateDetailId)) {
      return resList;
    }
    List<Long> relateDetailIds =
        Arrays.stream(relateDetailId.split(StrUtil.COMMA))
            .map(Long::valueOf)
            .collect(Collectors.toList());
    List<OrderSettlementDetail> orderSettlementDetails =
        iOrderSettlementDetailService
            .lambdaQuery()
            .in(OrderSettlementDetail::getId, relateDetailIds)
            .list();
    if (CollUtil.isEmpty(orderSettlementDetails)) {
      return resList;
    }

    Map<String, String> skuCodeAndNameMap = new HashMap<>(8);
    Map<String, String> skuCodeAnSpecificationsMap = new HashMap<>(8);
    Set<String> skuCodes =
        orderSettlementDetails.stream()
            .map(OrderSettlementDetail::getSkuCode)
            .collect(Collectors.toSet());
    if (CollUtil.isNotEmpty(skuCodes)) {
      skuCodeAndNameMap =
          itemSkuMapper.selectSkuName(skuCodes).stream()
              .collect(Collectors.toMap(SkuNameDo::getSkuCode, SkuNameDo::getName));
      skuCodeAnSpecificationsMap =
          iItemSkuService.lambdaQuery().in(ItemSku::getSkuCode, skuCodes).list().stream()
              .collect(
                  Collectors.toMap(
                      ItemSku::getSkuCode,
                      itemSku ->
                          Objects.isNull(itemSku.getSpecifications())
                              ? StrUtil.EMPTY
                              : itemSku.getSpecifications()));
    }

    final Set<String> skuCodeSet = skuCodeAndNameMap.keySet();
    final Map<String, List<CorpBizTypeDTO>> divisionMap =
        iBizLevelDivisionService.queryBySkuCode(skuCodeSet);

    for (OrderSettlementDetail orderSettlementDetail : orderSettlementDetails) {
      SettlementListViewVo vo = new SettlementListViewVo();
      vo.setId(orderSettlementDetail.getId());
      vo.setSkuCode(orderSettlementDetail.getSkuCode());
      vo.setItemName(skuCodeAndNameMap.getOrDefault(orderSettlementDetail.getSkuCode(), "UNKNOWN"));
      vo.setSpecifications(
          skuCodeAnSpecificationsMap.getOrDefault(orderSettlementDetail.getSkuCode(), "UNKNOWN"));
      vo.setTemporarilyPrice(orderSettlementDetail.getTemporaryPrice());
      vo.setTemporarilyQuantity(orderSettlementDetail.getTemporaryQuantity());
      vo.setPrice(orderSettlementDetail.getSettlementPrice());
      vo.setQuantity(orderSettlementDetail.getSettlementQuantity());
      vo.setAmount(orderSettlementDetail.getFinalAmount());
      vo.setCorpBizType(divisionMap.get(orderSettlementDetail.getSkuCode()));
      resList.add(vo);
    }
    return resList;
  }

  @Override
  @DistributedLock
  public SingleResponse<Long> save(PaymentApplyOrderSaveCmd cmd) {
    boolean isAdd = Objects.isNull(cmd.getId());
    Long id;

    dataVersionCheck(cmd.getDetailSource(), cmd.getDetailSaveCmdList());

    if (isAdd) {
      id = add(cmd);
    } else {
      id = update(cmd);
    }
    return SingleResponse.of(id);
  }

  private void dataVersionCheck(
      Integer detailSource, List<PaymentApplyOrderDetailSaveCmd> detailSaveCmdList) {
    if (detailSource == 1) {
      List<Long> reqApplyIds =
          detailSaveCmdList.stream()
              .map(PaymentApplyOrderDetailSaveCmd::getApplyRelatedId)
              .collect(Collectors.toList());
      if (CollUtil.isNotEmpty(reqApplyIds)) {
        Map<Long, PaymentApplyOrderDetailSaveCmd> reqApplyVersionMap =
            detailSaveCmdList.stream()
                .collect(
                    Collectors.toMap(PaymentApplyOrderDetailSaveCmd::getApplyRelatedId, v -> v));
        List<OrderSettlementForm> orderSettlementForms =
            iOrderSettlementFormService
                .lambdaQuery()
                .in(OrderSettlementForm::getId, reqApplyIds)
                .list();
        Map<Long, Integer> saveVersionMap =
            orderSettlementForms.stream()
                .collect(
                    Collectors.toMap(
                        OrderSettlementForm::getId,
                        orderSettlementForm ->
                            Objects.isNull(orderSettlementForm.getVersion())
                                ? 1
                                : orderSettlementForm.getVersion()));
        for (Long applyId : reqApplyIds) {
          PaymentApplyOrderDetailSaveCmd reqOne = reqApplyVersionMap.get(applyId);
          if (Objects.nonNull(reqOne.getVersion())) {
            boolean sameVersion = Objects.equals(reqOne.getVersion(), saveVersionMap.get(applyId));
            if (!sameVersion) {
              throw ExceptionPlusFactory.bizException(
                  ErrorCode.SYS_ERROR,
                  StrUtil.format("结算单：{}发生了更新，请重新选择此单据获以取最新数据", reqOne.getApplyRelatedNo()));
            }
          }
        }
      }
    }
  }

  @DistributedLock
  private Long add(PaymentApplyOrderSaveCmd cmd) {

    Integer detailSource = cmd.getDetailSource();
    // 如果单据中存在审核拒绝、付款异常的单据，也不允许发起新的付款申请，提示已存在付款单据，请编辑原始单据或删除后在添加
    if (1 == detailSource) {
      List<PaymentApplyOrderDetailSaveCmd> detailSaveCmdList = cmd.getDetailSaveCmdList();
      for (PaymentApplyOrderDetailSaveCmd paymentApplyOrderDetailSaveCmd : detailSaveCmdList) {
        String relatedNo = paymentApplyOrderDetailSaveCmd.getApplyRelatedNo();
        SettlementOrderPageQuery pageQuery = new SettlementOrderPageQuery();
        pageQuery.setPaymentOrderStatusList(
            ListUtil.of(
                PaymentOrderStatus.PAY_ERROR,
                PaymentOrderStatus.AUDIT_REJECT,
                PaymentOrderStatus.SYNC_KING_DEE_ERROR));
        pageQuery.setNo(relatedNo);
        PageResponse<SettlementOrderPageVo> response =
            orderSettlementBizService.settlementPageQuery(pageQuery);
        Assert.isTrue(response.isSuccess(), "新增付款单,单据状态检查异常");
        Assert.isTrue(response.getTotalCount() == 0, relatedNo + "已存在付款单,请编辑原付款单或者将其删除");
      }
    }

    PaymentApplyOrder paymentApplyOrder = PaymentApplyTransMapper.INSTANCE.cmdToEntity(cmd);
    String noPrefix = getNoPrefix();
    paymentApplyOrder.setNo(getNoPrefix());
    if (CollUtil.isNotEmpty(cmd.getAdditionalId())) {
      String join = StrUtil.join(StrUtil.COMMA, cmd.getAdditionalId());
      paymentApplyOrder.setAdditionalIds(join);
    }

    List<PaymentApplyOrderDetailSaveCmd> detailSaveCmdList = cmd.getDetailSaveCmdList();
    List<PaymentApplyOrderDetail> allDetails =
        getDetailByDetailCmd(
            noPrefix,
            paymentApplyOrder.getDetailSource(),
            detailSaveCmdList,
            Collections.emptyList());

    return paymentApplyDao.addNewOne(paymentApplyOrder, allDetails);
  }

  private String getNoPrefix() {
    String s = DateUtil.parseTimeStamp(DateUtil.currentTime(), "yyyyMMddHH");
    String sysPrefix = ApplicationContextUtil.isActiveProfile("prod") ? "FKD" : "FKDT";
    String prefix = sysPrefix + s;
    String maxNo = paymentApplyOrderMapper.getMaxNo(prefix);
    if (StrUtil.isBlank(maxNo)) {
      return prefix + "01";
    } else {
      int i = Integer.parseInt(maxNo.replaceAll(prefix, ""));
      int ni = i + 1;
      if (ni < 10) {
        return prefix + "0" + ni;
      } else {
        return prefix + ni;
      }
    }
  }

  private List<PaymentApplyOrderDetail> getDetailByDetailCmd(
      String orderNo,
      Integer detailSource,
      List<PaymentApplyOrderDetailSaveCmd> detailSaveCmdList,
      List<PaymentApplyOrderDetail> oldDetailList) {
    List<PaymentApplyOrderDetail> allDetails = new LinkedList<>();

    final Map<Long, PaymentApplyOrderDetail> oldDetailsMap =
        oldDetailList.stream()
            .collect(Collectors.toMap(PaymentApplyOrderDetail::getId, Function.identity()));
    for (PaymentApplyOrderDetailSaveCmd detailSaveCmd : detailSaveCmdList) {
      List<Long> relateDetailIdList = detailSaveCmd.getRelateDetailIdList();
      if (CollUtil.isNotEmpty(relateDetailIdList)) {
        final PaymentApplyOrderDetail oldDetail = oldDetailsMap.get(detailSaveCmd.getId());
        PaymentApplyOrderDetail detail =
            oldDetail != null
                ? PaymentApplyTransMapper.INSTANCE.copy(oldDetail)
                : new PaymentApplyOrderDetail();
        detail.setId(detailSaveCmd.getId());
        detail.setPaymentApplyOrderNo(orderNo);
        detail.setDetailSource(detailSource);
        detail.setApplyRelatedNo(detailSaveCmd.getApplyRelatedNo());
        detail.setApplyRelatedId(detailSaveCmd.getApplyRelatedId());
        detail.setRelateDetailId(StrUtil.join(StrUtil.COMMA, relateDetailIdList));
        detail.setApplyAmount(detailSaveCmd.getApplyAmount());
        detail.setApplyAmountRemark(detailSaveCmd.getApplyAmountRemark());
        detail.setRightAmount(detailSaveCmd.getRightAmount());
        allDetails.add(detail);
      }
    }
    return allDetails;
  }

  @DistributedLock
  private Long update(PaymentApplyOrderSaveCmd cmd) {
    Long id = cmd.getId();
    PaymentApplyOrder oldOne = iPaymentApplyOrderService.getById(id);
    Assert.notNull(oldOne, "id非法");

    // 创建人/采购员。只可以编辑自己创建的付款单，管理员可以编辑全部。
    boolean isAdmin =
        UserContext.hasPermission(GlobalConstant.PAYMENT_APPLY_EDIT_ADMIN_API, ResourceType.API);
    if (!isAdmin) {
      boolean b1 = oldOne.getCreatedUid().equals(UserContext.getUserId());
      boolean b2 = oldOne.getBuyerId().equals(UserContext.getUserId());
      Assert.state(b1 || b2, "你没有管理员角色，只能编辑自己创建或者负责采购的付款单数据");
    }

    // 付款单付款用途发生了改变
    boolean payPurposeChange = !Objects.equals(oldOne.getPayPurpose(), cmd.getPayPurpose());
    if (payPurposeChange && cmd.getDetailSource() == 0) {
      final List<PaymentApplyOrderDetailSaveCmd> detailSaveCmdList = cmd.getDetailSaveCmdList();
      for (PaymentApplyOrderDetailSaveCmd detailSaveCmd : detailSaveCmdList) {
        final String relatedNo = detailSaveCmd.getApplyRelatedNo();
        final PayPurposeEnum payPurpose = purchaseOrderBizService.getPayPurpose(relatedNo);
        if (!PayPurposeEnum.ALL.equals(payPurpose)) {
          Assert.state(
              payPurpose.getValue().equals(cmd.getPayPurpose()),
              StringUtil.format("【{}】已进行采购付款，不允许进行预付款，请移除该采购单号，再继续进行预付款！", relatedNo));
        }
      }
    }

    PaymentOrderStatus status = oldOne.getStatus();
    Preconditions.checkArgument(!LOCKED_STATUS_LIST.contains(status), "此单据状态已经锁定，不允许发生更改");

    PaymentApplyOrder newOne = PaymentApplyTransMapper.INSTANCE.copy(oldOne);
    PaymentApplyTransMapper.INSTANCE.copyFromCmd(newOne, cmd);
    List<PaymentApplyOrderDetail> oldDetailList =
        iPaymentApplyOrderDetailService
            .lambdaQuery()
            .eq(PaymentApplyOrderDetail::getPaymentApplyOrderId, id)
            .list();
    convertDetailSource(oldDetailList);
    List<PaymentApplyOrderDetail> newDetailList =
        getDetailByDetailCmd(
            newOne.getNo(), newOne.getDetailSource(), cmd.getDetailSaveCmdList(), oldDetailList);
    convertDetailSource(newDetailList);

    PaymentCompareBo oldCompareOne = PaymentApplyTransMapper.INSTANCE.entityToCompareBo(oldOne);
    fillCompareBo(
        oldOne.getTradeUnit(),
        oldOne.getPayeeUnit(),
        oldOne.getBuyerId(),
        oldOne.getAdditionalIds(),
        oldCompareOne);
    PaymentCompareBo newCompareOne = PaymentApplyTransMapper.INSTANCE.entityToCompareBo(newOne);
    fillCompareBo(
        newOne.getTradeUnit(),
        newOne.getPayeeUnit(),
        newOne.getBuyerId(),
        newOne.getAdditionalIds(),
        newCompareOne);
    Diff diff = DiffUtil.diff(oldCompareOne, newCompareOne);
    String orderDiffLog =
        DiffUtil.getVerboseDiffLog(
            diff,
            PaymentCompareBo.class,
            Collections.emptyList(),
            Collections.emptyMap(),
            StrUtil.EMPTY);

    List<String> detailLogs = new LinkedList<>();
    if (!Objects.equals(oldOne.getDetailSource(), newOne.getDetailSource())) {
      String o = PaymentApplyTrans.getDetailSource(oldOne.getDetailSource());
      String n = PaymentApplyTrans.getDetailSource(newOne.getDetailSource());
      detailLogs.add(StrUtil.format("付款明细列表数据来源改变，由【{}】改为【{}】，付款明细列表数据重置", o, n));
    }
    Optional<CollChange<PaymentApplyOrderDetail>> detailCollChange =
        DiffUtil.diffList(oldDetailList, newDetailList, PaymentApplyOrderDetail.class);
    if (detailCollChange.isPresent()) {
      Diff diff1 = detailCollChange.get().getDiff();
      String changeLog = detailDiffLog(diff1);
      detailLogs.add(changeLog);
    }

    paymentApplyDao.updateOne(
        cmd.getId(),
        newOne,
        newDetailList,
        oldDetailList.stream().map(PaymentApplyOrderDetail::getId).collect(Collectors.toList()),
        orderDiffLog,
        StrUtil.join(StrUtil.LF, detailLogs));

    return cmd.getId();
  }

  @NonNull
  public static String detailDiffLog(Diff diff) {
    StringBuilder changeLog = new StringBuilder();
    final List<ChangesByObject> changesByObjects = diff.groupByObject();
    if (!changesByObjects.isEmpty()) {
      for (ChangesByObject changesByObject : changesByObjects) {
        final GlobalId globalId = changesByObject.getGlobalId();
        if (globalId instanceof InstanceId) {
          final InstanceId instanceId = (InstanceId) globalId;
          final List<NewObject> newObjects = changesByObject.getNewObjects();
          for (NewObject newObject : newObjects) {
            changeLog.append("新增了 ").append(newObject.getAffectedLocalId()).append("；");
          }
          final List<ObjectRemoved> objectsRemoved = changesByObject.getObjectsRemoved();
          if (!objectsRemoved.isEmpty()) {
            for (ObjectRemoved objectRemoved : objectsRemoved) {
              changeLog.append("移除了 ").append(objectRemoved.getAffectedLocalId()).append("；");
            }
            continue;
          }
          @SuppressWarnings("rawtypes")
          final List<PropertyChange> propertyChanges = changesByObject.getPropertyChanges();
          if (!propertyChanges.isEmpty()) {
            final StringJoiner stringJoiner =
                new StringJoiner("，", "将 " + instanceId.getCdoId() + " ", "");
            for (PropertyChange<?> propertyChange : propertyChanges) {
              if (propertyChange instanceof ValueChange) {
                if (propertyChange instanceof InitialValueChange) {
                  stringJoiner.add(
                      String.format(
                          "“%s” 初始化为 “%s”",
                          propertyChange.getPropertyName(), propertyChange.getRight()));
                } else {
                  final ValueChange valueChange = (ValueChange) propertyChange;
                  final Optional<Object> affectedObject = valueChange.getAffectedObject();
                  if (affectedObject.isPresent()
                      && affectedObject.get() instanceof PaymentApplyOrderDetail) {
                    stringJoiner.add(
                        String.format(
                            "“%s” 从 “%s” 修改为 “%s”",
                            valueChange.getPropertyName(),
                            valueChange.getLeft(),
                            valueChange.getRight()));
                  }
                }
              }
            }
            changeLog.append(stringJoiner).append("；");
          }
        }
      }
    }
    return changeLog.toString();
  }

  private void convertDetailSource(List<PaymentApplyOrderDetail> list) {
    list.forEach(
        val -> {
          if (StrUtil.isBlank(val.getDetailSourceStr()) && Objects.nonNull(val.getDetailSource())) {
            val.setDetailSourceStr(1 == val.getDetailSource() ? "采购单" : "结算单");
          }
        });
  }

  private void fillCompareBo(
      Long tradeUnit, Long payeeUnit, Long buyerId, String additionalIds, PaymentCompareBo bo) {
    Provider tradeProvider = iProviderService.getById(tradeUnit);
    if (Objects.nonNull(tradeProvider)) {
      bo.setTradeUnit(tradeProvider.getName());
    } else {
      bo.setTradeUnit(StrUtil.EMPTY);
    }

    Provider payeeProvider = iProviderService.getById(payeeUnit);
    if (Objects.nonNull(payeeProvider)) {
      bo.setPayeeUnit(payeeProvider.getName());
    } else {
      bo.setPayeeUnit(StrUtil.EMPTY);
    }

    StaffInfo staffInfo = userGateway.queryStaffInfoById(buyerId);
    if (Objects.nonNull(staffInfo)) {
      bo.setBuyer(staffInfo.getNickname());
    } else {
      bo.setBuyer(StrUtil.EMPTY);
    }

    String additional =
        iFileService.lambdaQuery().in(File::getId, additionalIds).list().stream()
            .map(File::getName)
            .sorted()
            .collect(Collectors.joining(StrUtil.COMMA));
    bo.setAdditional(additional);
  }

  private Response syncKingDee(Long id) {
    return kingDeeTemplate.handler(ApiEnum.SAVE_PAY_BILL, id, null);
  }

  @Override
  public Response sync(Long id) {
    PaymentApplyOrder paymentApplyOrder = iPaymentApplyOrderService.getById(id);
    Assert.notNull(paymentApplyOrder, "id非法");
    //        Assert.state(
    //                paymentApplyOrder.getStatus().equals(PaymentOrderStatus.SYNC_KING_DEE_ERROR),
    //                "单据状态非法,单据状态必须是【金蝶同步异常】");
    Response response = syncKingDee(id);
    if (response.isSuccess()) {
      paymentApplyOrder.setStatus(PaymentOrderStatus.FINISH);
      iPaymentApplyOrderService.updateById(paymentApplyOrder);
      operateLogGateway.addOperatorLog(
          0L, OperateLogTarget.PAYMENT_APPLY_ORDER, id, "付款单重新推送完成", null);
    } else {
      operateLogGateway.addOperatorLog(
          0L, OperateLogTarget.PAYMENT_APPLY_ORDER, id, response.getErrMessage(), null);
    }
    return response;
  }

  @Override
  public SingleResponse<Boolean> remove(Long id) {
    paymentApplyDao.remove(id);
    return SingleResponse.of(true);
  }

  @Override
  @DistributedLock
  public Response submit(Long id) {

    final PaymentApplyOrder paymentApplyOrder = iPaymentApplyOrderService.getById(id);
    Assert.notNull(paymentApplyOrder, "单据查询异常");
    final PaymentOrderStatus status = paymentApplyOrder.getStatus();
    if (status != PaymentOrderStatus.WAIT_SUBMIT
        && status != PaymentOrderStatus.PAY_ERROR
        && status != PaymentOrderStatus.AUDIT_REJECT
        && status != PaymentOrderStatus.SYNC_KING_DEE_ERROR
        && status != PaymentOrderStatus.ROLLBACK_AUDIT) {
      return Response.buildFailure(ErrorCode.OPERATION_REJECT.getCode(), "单据当前状态不可提交");
    }
    final List<PaymentApplyOrderDetail> details =
        iPaymentApplyOrderDetailService
            .lambdaQuery()
            .eq(PaymentApplyOrderDetail::getPaymentApplyOrderId, paymentApplyOrder.getId())
            .list();

    Boolean advancePayment = paymentApplyOrder.getPayPurpose() == 1;
    // 付款单是否是预付款
    check(details, advancePayment);

    final Long createdUid = paymentApplyOrder.getCreatedUid();
    final StaffInfo createUser = userGateway.queryStaffInfoById(createdUid);
    Assert.notNull(createUser, "单据关联用户信息查询异常");

    try {
      final OaCgfkData formData = getOaCgfkForm(id, paymentApplyOrder, details);
      log.info("formData data:{}", JsonUtil.toJson(formData.getMainForm()));
      final OaResponse<OaProcessStartResponse> oaResponse =
          oaProcessService.processStart(
              createUser.getLoginName(),
              paymentApplyConfig.getOaProcessTemplateCode(),
              false,
              null,
              Collections.emptyList(),
              formData);
      if (!Objects.equals(0, oaResponse.getCode())) {
        throw ExceptionPlusFactory.bizException(
            ErrorCode.GATEWAY_ERROR, "OA流程创建失败:" + oaResponse.getMessage());
      }
      final JsonNode appBusinessData = JsonUtil.parse(oaResponse.getData().getAppBussinessData());
      if (Objects.isNull(appBusinessData)) {
        operateLogGateway.addOperatorLog(
            UserContext.getUserId(),
            OperateLogTarget.PAYMENT_APPLY_ORDER,
            id,
            "OA流程创建响应数据异常",
            null);
        throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "OA流程创建响应数据异常");
      } else {
        operateLogGateway.addOperatorLog(
            UserContext.getUserId(), OperateLogTarget.PAYMENT_APPLY_ORDER, id, "OA流程创建成功", null);
      }
      final String summaryId = appBusinessData.get("summaryId").asText();
      final String affairId = appBusinessData.get("affairId").asText();

      processInstRefService.saveProcessInstRef(
          id, summaryId, ProcessBusinessType.PAYMENT_APPLY, ProcessType.OA, oaResponse);

      final PaymentApplyOrder updateModel = new PaymentApplyOrder();
      updateModel.setId(paymentApplyOrder.getId());
      updateModel.setOaProcessSummaryId(summaryId);
      updateModel.setOaProcessAffairId(affairId);
      updateModel.setStatus(PaymentOrderStatus.WAIT_AUDIT);
      updateModel.setApplyTime(DateUtil.currentTime());
      updateModel.setSubmitUid(UserContext.getUserId());
      iPaymentApplyOrderService.updateById(updateModel);

      return Response.buildSuccess();
    } catch (FeignException.FeignServerException | IOException e) {
      log.error("付款申请提交异常.id:{}", id, e);
      throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "与OA服务通信异常，请稍后重试");
    }
  }

  public void check(List<PaymentApplyOrderDetail> details, Boolean advancePayment) {
    for (PaymentApplyOrderDetail detail : details) {
      String relatedNo = detail.getApplyRelatedNo();
      Integer detailSource = detail.getDetailSource();
      String relateDetailId = detail.getRelateDetailId();
      if (StrUtil.isBlank(relateDetailId)) {
        continue;
      }

      // 采购单
      if (detailSource == 0) {
        final SingleResponse<PaymentDetailAmountVO> rightPayAmount =
            purchaseOrderBizService.getRightPayAmount(
                detail.getApplyRelatedId(),
                Arrays.stream(relateDetailId.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList()),
                advancePayment);
        Assert.notNull(rightPayAmount, "采购单应付金额查询异常");
        PaymentDetailAmountVO payAmountData = rightPayAmount.getData();
        BigDecimal rightAmount = payAmountData.getRightAmount();
        BigDecimal orderAmount = payAmountData.getOrderAmount();
        if (advancePayment) {
          boolean b = detail.getApplyAmount().compareTo(rightAmount) <= 0;
          Assert.isTrue(
              b,
              StrUtil.format(
                  "采购单编码：{}，采购付款金额不得大于剩余实际可付款金额，此采购单剩余实际可付款金额：{}", relatedNo, rightAmount));
        } else {
          BigDecimal maxExtraAmount =
              orderAmount.multiply(
                  new BigDecimal(purchaseOrderConfig.getPaymentExtraPercent())
                      .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
          BigDecimal maxRightAmount = rightAmount.add(maxExtraAmount);
          Assert.isTrue(
              detail.getApplyAmount().compareTo(maxRightAmount) <= 0,
              StrUtil.format(
                  "采购单编码：{}，采购付款金额不得大于剩余实际可付款金额的{}%，此采购单剩余实际可付款金额：{}，最多可申请：{}",
                  relatedNo,
                  purchaseOrderConfig.getPaymentExtraPercent() + 100,
                  rightAmount,
                  maxRightAmount));
        }
      }
      if (detailSource == 1) {
        OrderSettlementForm orderSettlementForm = iOrderSettlementFormService.getByNo(relatedNo);
        String staticInfo = orderSettlementForm.getStaticInfo();
        DetailStaticDo staticDo = JsonUtil.parse(staticInfo, DetailStaticDo.class);
        assert staticDo != null;
        BigDecimal totalPurchaseAmount =
            staticDo.getTotalFinalAmount().setScale(2, RoundingMode.HALF_UP);
        BigDecimal appliedPaymentAmountByRelatedNo =
            iPaymentApplyOrderService.getAppliedPaymentAmountByRelatedNo(ListUtil.of(relatedNo));
        BigDecimal applyAmount = detail.getApplyAmount();
        BigDecimal subtract = totalPurchaseAmount.subtract(appliedPaymentAmountByRelatedNo);
        if (applyAmount.compareTo(subtract) > 0) {
          String error =
              StrUtil.format(
                  "结算单编码:{},总金额:{},目前已申请付款金额:{},剩余可申请付款金额:{},付款金额不得大于剩余可申请付款金额",
                  relatedNo,
                  totalPurchaseAmount.setScale(2, RoundingMode.DOWN),
                  appliedPaymentAmountByRelatedNo.setScale(2, RoundingMode.DOWN),
                  subtract.setScale(2, RoundingMode.DOWN));
          throw ExceptionPlusFactory.bizException(ErrorCode.API_REQ_ERROR, error);
        }
      }
    }
  }

  public static void main(String[] args) {

    final int i = new BigDecimal("-10").compareTo(new BigDecimal("-1"));
    System.out.println(i);
  }

  /** for unit test */
  public OaCgfkData getOaCgfkForm(Long id) throws IOException {
    final PaymentApplyOrder paymentApplyOrder = iPaymentApplyOrderService.getById(id);
    final List<PaymentApplyOrderDetail> details =
        iPaymentApplyOrderDetailService
            .lambdaQuery()
            .eq(PaymentApplyOrderDetail::getPaymentApplyOrderId, paymentApplyOrder.getId())
            .list();
    return getOaCgfkForm(id, paymentApplyOrder, details);
  }

  @NonNull
  private OaCgfkData getOaCgfkForm(
      Long id, PaymentApplyOrder paymentApplyOrder, List<PaymentApplyOrderDetail> details)
      throws IOException {
    final OaCgfkData formData = new OaCgfkData();
    formData.setMainForm(assembleOaMainForm(paymentApplyOrder));
    formData.setDetails(assembleOaDetails(id, details));
    resolveAttachments(paymentApplyOrder, formData);
    return formData;
  }

  private OaCgfkMainForm assembleOaMainForm(PaymentApplyOrder paymentApplyOrder) {
    final PaymentOrderStatus status = paymentApplyOrder.getStatus();
    final OaCgfkMainForm mainForm = new OaCgfkMainForm();
    mainForm.setBelongCompany(paymentApplyOrder.getPurchaseOrg());
    mainForm.setPayer(paymentApplyOrder.getPayOrg());
    mainForm.setProcessNo(paymentApplyOrder.getNo());
    final Provider provider = iProviderService.getById(paymentApplyOrder.getPayeeUnit());
    mainForm.setPayee(provider.getName());
    mainForm.setPayeeBank(paymentApplyOrder.getPayeeBank());
    mainForm.setPayeeBankAccount(paymentApplyOrder.getPayeeBankCardNo());
    mainForm.setPayeeBankNo(paymentApplyOrder.getPayeeBankNo());
    mainForm.setProcurementType(
        paymentApplyOrder.getPurchaseType() == 0
            ? ProcurementType.STANDARD_PROCUREMENT
            : ProcurementType.FACTORY_REPLACEMENT_SHIPMENT);
    final Long buyerId = paymentApplyOrder.getBuyerId();
    final DadStaffVO buyerStaffVO = staffService.getStaff(buyerId);
    mainForm.setBuyer(buyerStaffVO.getNickname());
    mainForm.setBuyerOaId(String.valueOf(buyerStaffVO.getOaId()));
    mainForm.setNatureOfPayment("货款");
    mainForm.setRemark(paymentApplyOrder.getRemark());
    mainForm.setPaymentPurpose(
        paymentApplyOrder.getPayPurpose() == 0
            ? PaymentPurpose.PROCUREMENT_PAYMENT
            : PaymentPurpose.ADVANCE_PAYMENT);
    mainForm.setPaymentRatio(
        DecimalUtil.divide(
            BigDecimal.valueOf(paymentApplyOrder.getPayProportions()), BigDecimal.valueOf(100)));
    mainForm.setTotalRequestedPaymentAmount(paymentApplyOrder.getTotalApplyAmount());
    mainForm.setDeductionsFromOtherAmounts(paymentApplyOrder.getOtherChargeback());
    mainForm.setEnv(ApplicationContextUtil.getActiveProfile());
    // 如果支付失败重新提交，则跳过中间所有审核节点（直接到财务打款）
    if (PaymentOrderStatus.PAY_ERROR == status) {
      mainForm.setCurrentNode(6);
    } else {
      // 审核拒绝，再次提交，直接跳到上次的节点。，
      if (Objects.nonNull(paymentApplyOrder.getRejectIndex())
          && paymentApplyOrder.getRejectIndex() > 0) {
        mainForm.setCurrentNode(paymentApplyOrder.getRejectIndex());
      }
    }
    // 合作方
    final Integer businessLine = paymentApplyOrder.getBusinessLine();
    if (DivisionLevelValueEnum.C_DECORATION.getValue() == businessLine) {
      mainForm.setPartner(Partner.GREEN_HOME);
    } else {
      mainForm.setPartner(Partner.ECOMMERCE);
    }
    return mainForm;
  }

  private void resolveAttachments(PaymentApplyOrder paymentApplyOrder, OaCgfkData formData)
      throws IOException {
    final List<String> additionalIds = StrUtil.splitTrim(paymentApplyOrder.getAdditionalIds(), ",");
    final Map<Long, File> filesMap =
        fileGateway.fileQueryBatchByIds(
            additionalIds.stream().map(Long::parseLong).collect(Collectors.toList()));
    final ArrayList<OaCgfkInvoice> formInvoices = Lists.newArrayList();
    for (File value : filesMap.values()) {
      final OaUploadAttachmentResponse oaUploadAttachmentResponse =
          oaProcessService.uploadAttachment(
              UserContext.getLoginName(), fileGateway.getAuthorizedUrl(value.getUrl()));
      final OaCgfkInvoice oaCgfkInvoice = new OaCgfkInvoice();
      oaCgfkInvoice.setBillAttachment(oaUploadAttachmentResponse.getAtts().get(0).getFileUrl());
      formInvoices.add(oaCgfkInvoice);
    }

    formData.setInvoices(formInvoices);
    final int[] sort = {0};
    final List<OaFormAttachment> thirdAttachments =
        formInvoices.stream()
            .map(
                v -> {
                  final OaFormAttachment oaFormAttachment = new OaFormAttachment();
                  oaFormAttachment.setSubReference(v.getBillAttachment());
                  oaFormAttachment.setFileUrl(v.getBillAttachment());
                  oaFormAttachment.setSort(++sort[0]);
                  return oaFormAttachment;
                })
            .collect(Collectors.toList());
    formData.setThirdAttachments(thirdAttachments);
  }

  private ArrayList<OaCgfkDetail> assembleOaDetails(
      Long id, List<PaymentApplyOrderDetail> details) {
    final Integer detailSource =
        details.stream()
            .map(PaymentApplyOrderDetail::getDetailSource)
            .findAny()
            .orElseThrow(
                () -> ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "付款单明细数为空"));

    final Function<String, Stream<? extends Long>> idToLongStreamFunction =
        v -> StrUtil.splitTrim(v, ",").stream().map(Long::valueOf);
    final Set<Long> relateDetailIds =
        details.stream()
            .map(PaymentApplyOrderDetail::getRelateDetailId)
            .flatMap(idToLongStreamFunction)
            .collect(Collectors.toSet());

    final ArrayList<OaCgfkDetail> formDetails = Lists.newArrayList();
    if (detailSource == 0) {
      final List<PurchaseOrderDetail> purchaseOrderDetails =
          iPurchaseOrderDetailService.listByIds(relateDetailIds);
      final Map<Long, PurchaseOrderDetail> purchaseOrderDetailsMap =
          purchaseOrderDetails.stream()
              .collect(Collectors.toMap(PurchaseOrderDetail::getId, Function.identity()));

      for (PaymentApplyOrderDetail detail : details) {
        final OaCgfkDetail oaCgfkDetail = new OaCgfkDetail();
        oaCgfkDetail.setPurchaseOrderNo(detail.getApplyRelatedNo());
        if (detail.getDetailSource() == 0) {
          oaCgfkDetail.setPurchaseOrderNo(
              String.format(
                  "<a href=\"%s\" target=\"_blank\">%s</a>",
                  refreshConfig.getDomain()
                      + "/commodity-purchase/purchase-order/review?id="
                      + detail.getApplyRelatedId(),
                  detail.getApplyRelatedNo()));
        } else {
          oaCgfkDetail.setPurchaseOrderNo(
              String.format(
                  "<a href=\"%s\" target=\"_blank\">%s</a>",
                  refreshConfig.getDomain()
                      + "/order-management/settlement/review?id="
                      + detail.getApplyRelatedId(),
                  detail.getApplyRelatedNo()));
        }
        oaCgfkDetail.setPayableAmount(detail.getRightAmount());
        oaCgfkDetail.setRequestPaymentAmount(detail.getApplyAmount());
        final int[] numCounter = {0, 0};
        idToLongStreamFunction
            .apply(detail.getRelateDetailId())
            .map(purchaseOrderDetailsMap::get)
            .forEach(
                v -> {
                  numCounter[0] += 1;
                  numCounter[1] += v.getPurchaseQuantity();
                });
        oaCgfkDetail.setItemNum(numCounter[0]);
        oaCgfkDetail.setSkuNum(numCounter[1]);
        oaCgfkDetail.setPurchaseDetails(
            String.format(
                "<a href=\"%s\" target=\"_blank\">&ERP付款单详情页链接</a>",
                refreshConfig.getDomain() + "/commodity-purchase/payment-order/detail?id=" + id));

        Long purchaseOrderId = detail.getApplyRelatedId();
        PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(purchaseOrderId);
        oaCgfkDetail.setCycleDesc(DateUtil.format(purchaseOrder.getPurchaseDate(), "yyyy-MM"));

        formDetails.add(oaCgfkDetail);
      }
    } else if (detailSource == 1) {
      final List<OrderSettlementDetail> orderSettlementDetails =
          iOrderSettlementDetailService.listByIds(relateDetailIds);
      final Map<Long, OrderSettlementDetail> orderSettlementDetailsMap =
          orderSettlementDetails.stream()
              .collect(Collectors.toMap(OrderSettlementDetail::getId, Function.identity()));

      for (PaymentApplyOrderDetail detail : details) {
        final OaCgfkDetail oaCgfkDetail = new OaCgfkDetail();
        if (detail.getDetailSource() == 0) {
          oaCgfkDetail.setPurchaseOrderNo(
              String.format(
                  "<a href=\"%s\" target=\"_blank\">%s</a>",
                  refreshConfig.getDomain()
                      + "/commodity-purchase/purchase-order/review?id="
                      + detail.getApplyRelatedId(),
                  detail.getApplyRelatedNo()));
        } else {
          oaCgfkDetail.setPurchaseOrderNo(
              String.format(
                  "<a href=\"%s\" target=\"_blank\">%s</a>",
                  refreshConfig.getDomain()
                      + "/order-management/settlement/review?id="
                      + detail.getApplyRelatedId(),
                  detail.getApplyRelatedNo()));
        }
        oaCgfkDetail.setPayableAmount(detail.getRightAmount());
        oaCgfkDetail.setRequestPaymentAmount(detail.getApplyAmount());
        final int[] numCounter = {0, 0};
        idToLongStreamFunction
            .apply(detail.getRelateDetailId())
            .map(orderSettlementDetailsMap::get)
            .forEach(
                v -> {
                  numCounter[0] += 1;
                  numCounter[1] += v.getSettlementQuantity();
                });
        oaCgfkDetail.setItemNum(numCounter[0]);
        oaCgfkDetail.setSkuNum(numCounter[1]);
        oaCgfkDetail.setPurchaseDetails(
            String.format(
                "<a href=\"%s\" target=\"_blank\">&ERP付款单详情页链接</a>",
                refreshConfig.getDomain() + "/commodity-purchase/payment-order/detail?id=" + id));

        OrderSettlementForm orderSettlementForm =
            iOrderSettlementFormService.getById(detail.getApplyRelatedId());
        if (Objects.isNull(orderSettlementForm)) {
          oaCgfkDetail.setCycleDesc(StrUtil.EMPTY);
        } else {
          LocalDateTime localDateTime =
              DateUtil.parseTimeStamp(orderSettlementForm.getSettlementStartDate());
          oaCgfkDetail.setCycleDesc(
              DateUtil.polymerizationTime(Collections.singletonList(localDateTime)));
        }

        formDetails.add(oaCgfkDetail);
      }
    } else {
      throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "付款单关联单据来源类型异常");
    }
    return formDetails;
  }

  @Override
  public Response oaCallback(OaCgfkCallback callback) {
    oaCallbackService.saveCallback(
        ProcessBusinessType.PAYMENT_APPLY, callback.getProcessInstId(), callback);

    if (paymentApplyConfig.isSuppressOaCallback()) {
      return Response.buildFailure(ErrorCode.OPERATION_REJECT.getCode(), "系统配置为暂停接收OA状态回调");
    }

    Predicate<String> stringFilter =
        v -> StringUtil.isNotBlank(v) && !StringUtil.equalsAnyIgnoreCase(v, "null");
    final HashMap<Integer, String> callbackStatusMap = new HashMap<>();
    callbackStatusMap.put(1, "流程提交");
    callbackStatusMap.put(2, "流程回退");
    callbackStatusMap.put(3, "流程结束");
    callbackStatusMap.put(4, "流程撤销");
    callbackStatusMap.put(5, "流程终止");
    final String nodeName =
        Optional.ofNullable(callback.getNodeName())
            .filter(stringFilter)
            .map(v -> StringUtil.wrap(v, "【", "】"))
            .orElse("");
    if (StrUtil.isEmpty(nodeName)) {
      return Response.buildFailure(ErrorCode.VERIFY_PARAM.getCode(), "节点名称不能为空");
    }
    final String callbackStatusDesc = callbackStatusMap.get(callback.getStatus());

    final String processNo = callback.getProcessInstId();
    final PaymentApplyOrder paymentApplyOrder = iPaymentApplyOrderService.getByNo(processNo);
    Assert.notNull(paymentApplyOrder, "付款单数据查询异常");
    final PaymentOrderStatus status = paymentApplyOrder.getStatus();
    // 仅【待审核】、【待付款】需要状态同步
    if (status != PaymentOrderStatus.WAIT_AUDIT && status != PaymentOrderStatus.WAIT_PAY) {
      return Response.buildSuccess();
    }
    final String userLoginName = callback.getUserLoginName();
    final StaffBrief staffBrief =
        staffService
            .getStaffByLoginName(userLoginName)
            .orElseThrow(
                () -> ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "审批用户信息查询异常"));
    final Long currentTime = DateUtil.currentTime();
    final PaymentStatus paymentStatus =
        callback.getPaymentStatus() == null
            ? null
            : IEnum.getEnumOptByValue(PaymentStatus.class, callback.getPaymentStatus())
                .orElseThrow(
                    () -> ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "付款状态不合法"));

    final Long id = paymentApplyOrder.getId();
    final PaymentApplyOrder updateModel = new PaymentApplyOrder();
    updateModel.setId(id);

    // 付款失败
    if (paymentStatus != null
        && Arrays.asList(
                PaymentStatus.PAYMENT_DELETED,
                PaymentStatus.PAYMENT_DOES_NOT_EXIST,
                PaymentStatus.PAYMENT_HAS_BEEN_BACK,
                PaymentStatus.PAYMENT_FAILED)
            .contains(paymentStatus)) {
      operateLogGateway.addOperatorLog(
          staffBrief.getUserId(),
          OperateLogTarget.PAYMENT_APPLY_ORDER,
          id,
          String.format("OA状态回传：付款失败（%s）", paymentStatus.getDesc()),
          null);
      updateModel.setStatus(PaymentOrderStatus.PAY_ERROR);
      updateModel.setPaymentCallbackTime(currentTime);
      updateModel.setPaymentStatus(paymentStatus.ordinal());
      updateModel.setPaymentCashier(staffBrief.getUserId());
      final boolean updateSuccess = updateOrder(updateModel);

      if (updateSuccess) {
        // 终止OA流程
        oaProcessService.processStop(
            paymentApplyOrder.getOaProcessAffairId(), userLoginName, "ERP｜因付款失败终止");
        operateLogGateway.addOperatorLog(
            staffBrief.getUserId(), OperateLogTarget.PAYMENT_APPLY_ORDER, id, "终止OA流程成功", null);
      }
    }

    // 支付成功（银行付款成功 或者 出纳手动制单付款成功）
    if (Objects.equals(callback.getPaymentStatus(), PaymentStatus.PAYMENT_SUCCESSFUL.getValue())) {
      operateLogGateway.addOperatorLog(
          staffBrief.getUserId(), OperateLogTarget.PAYMENT_APPLY_ORDER, id, "OA状态回传：付款成功", null);

      updateModel.setPaymentCallbackTime(currentTime);
      updateModel.setPaymentStatus(paymentStatus != null ? paymentStatus.ordinal() : -1);
      updateModel.setPaymentCashier(staffBrief.getUserId());

      updateModel.setOurAccountName(callback.getOurAccountName());
      updateModel.setOurAccountNumber(callback.getOurAccountNumber());
      updateModel.setOurBankName(callback.getOurBankName());

      // OA 流程处理完成，同步到金蝶
      if (SpringUtil.getActiveProfile().equals("gray")
          || SpringUtil.getActiveProfile().equals("prod")) {
        try {
          updateModel.setStatus(PaymentOrderStatus.FINISH);
          final boolean update = updateOrder(updateModel);
          if (update) {
            if ("杭州老爸电商科技有限公司".equals(paymentApplyOrder.getPayOrg().trim())) {
              Response response = syncKingDee(id);
              if (response.isSuccess()) {
                operateLogGateway.addOperatorLog(
                    0L, OperateLogTarget.PAYMENT_APPLY_ORDER, id, "数据同步金蝶成功", null);
              } else {
                updateModel.setStatus(PaymentOrderStatus.SYNC_KING_DEE_ERROR);
                updateOrder(updateModel);
                operateLogGateway.addOperatorLog(
                    0L,
                    OperateLogTarget.PAYMENT_APPLY_ORDER,
                    id,
                    "数据同步金蝶失败:" + response.getErrMessage(),
                    null);
              }
            }
          }
        } catch (Exception e) {
          updateModel.setStatus(PaymentOrderStatus.SYNC_KING_DEE_ERROR);
          updateOrder(updateModel);
          operateLogGateway.addOperatorLog(
              0L,
              OperateLogTarget.PAYMENT_APPLY_ORDER,
              id,
              "数据同步金蝶，处理过程异常:" + e.getMessage(),
              null);
        }
      } else {
        updateModel.setStatus(PaymentOrderStatus.FINISH);
        updateOrder(updateModel);
      }

      // 保存收款单位的银行账户信息
      providerBizService.saveOrUpdateBankInfo(
          paymentApplyOrder.getPayeeUnit(),
          paymentApplyOrder.getPayeeBankCardNo(),
          paymentApplyOrder.getPayeeBank(),
          paymentApplyOrder.getPayeeBankNo());
    }

    // 出纳审核通过切换到【待支付】
    if (1 == callback.getStatus()) {
      if (callback.getNodeName() != null && callback.getNodeName().contains("出纳审核")) {
        updateModel.setStatus(PaymentOrderStatus.WAIT_PAY);
      }
      // 是否业务财务审核通过
      boolean businessFinancialAudit =
          callback.getNodeName() != null && callback.getNodeName().contains("业务财务审核");
      if (businessFinancialAudit) {
        updateModel.setFinancialAuditTime(DateUtil.currentTime());
      }

      updateModel.setLastAuditStatus(1);
      updateModel.setLastAuditUid(staffBrief.getUserId());
      updateModel.setLastAuditTime(currentTime);
      updateOrder(updateModel);
      operateLogGateway.addOperatorLog(
          staffBrief.getUserId(),
          OperateLogTarget.PAYMENT_APPLY_ORDER,
          id,
          String.format("【%s】审核通过", callback.getNodeName()),
          null);

      if (businessFinancialAudit) {
        boolean mockSync =
            SpringUtil.getActiveProfile().equals("local")
                || SpringUtil.getActiveProfile().equals("test");
        writeOffBizService.mainProcess(updateModel.getId(), false, mockSync);
      }
    }

    // 流程提交1，流程回退2，流程结束3，流程撤销4，流程终止5
    // 任意节点回退、撤销、终止都算审核拒绝
    if (Arrays.asList(2, 4, 5).contains(callback.getStatus())) {
      updateModel.setStatus(PaymentOrderStatus.AUDIT_REJECT);
      updateModel.setLastAuditStatus(2);
      updateModel.setLastAuditUid(staffBrief.getUserId());
      updateModel.setLastAuditTime(currentTime);

      String msg = "";
      if (Objects.nonNull(callback.getSubState())) {
        if (15 == callback.getSubState()
            || 16 == callback.getSubState()
            || 17 == callback.getSubState()
            || 18 == callback.getSubState()) {
          msg = "审核跳跃开启";
          updateModel.setRejectIndex(getRejectIndex(callback.getNodeName()));
        }
        if (7 == callback.getSubState()) {
          msg = "审核重新开始";
        }
      } else {
        msg = "OA拒绝操作，类型返回值为空";
      }

      final boolean update = updateOrder(updateModel);

      if (update) {
        operateLogGateway.addOperatorLog(
            staffBrief.getUserId(),
            OperateLogTarget.PAYMENT_APPLY_ORDER,
            id,
            String.format("%s%s", nodeName, callbackStatusDesc),
            null);

        if (Arrays.asList(2, 4, 5).contains(callback.getStatus())) {
          try {
            // 终止OA流程
            oaProcessService.processStop(
                paymentApplyOrder.getOaProcessAffairId(), userLoginName, "ERP｜因流程驳回终止");
            operateLogGateway.addOperatorLog(
                staffBrief.getUserId(),
                OperateLogTarget.PAYMENT_APPLY_ORDER,
                id,
                StrUtil.isBlank(msg) ? "审核拒绝，终止OA流程成功" : "审核拒绝，终止OA流程成功。" + msg,
                null);
          } catch (Exception e) {
            log.error("【付款单OA回调处理】流程终止失败 {} id:{}", JsonUtil.toJson(callback), id);
          }

          // 如果存在发票附件，删除附件
          //                    updateModel.setAdditionalIds("");
          iPaymentApplyOrderService.updateById(updateModel);
        }
      }
    }
    return Response.buildSuccess();
  }

  private Integer getRejectIndex(String nodeName) {
    nodeName = nodeName.replaceAll("模拟", "").trim();
    nodeName = nodeName.replaceAll("请款人", "").trim();
    switch (nodeName) {
      case "部门经理":
        return 1;
      case "部门分管领导":
        return 2;
      case "业务财务审核":
        return 3;
      case "财务经理审核":
        return 4;
      case "总裁审核":
        return 5;
      case "出纳审核":
        return 6;
      default:
        return 0;
    }
  }

  private boolean updateOrder(PaymentApplyOrder updateModel) {
    return updateOrder(updateModel, null);
  }

  private boolean updateOrder(PaymentApplyOrder updateModel, PaymentOrderStatus statusEq) {
    final LambdaQueryWrapper<PaymentApplyOrder> updateWrapper = Wrappers.lambdaQuery();
    updateWrapper
        .eq(PaymentApplyOrder::getId, updateModel.getId())
        .eq(statusEq != null, PaymentApplyOrder::getStatus, statusEq);
    return iPaymentApplyOrderService.update(updateModel, updateWrapper);
  }

  @Override
  public Response export(PaymentPageQuery pageQuery) {
    String fileName = DateUtil.currentTime() + ".xlsx";
    java.io.File file = new java.io.File(fileName);
    ExportTask task = initExportTask(fileName);

    int viewAll =
        UserContext.hasPermission(GlobalConstant.PAYMENT_APPLY_VIEW_ADMIN_API, ResourceType.API)
            ? 1
            : 0;
    pageQuery.setViewAll(viewAll);
    pageQuery.setBusinessLine(
        UserPermissionJudge.filterBusinessLinePerm(pageQuery.getBusinessLine()));

    ThreadUtil.execute(
        PoolEnum.COMMON_POOL,
        () -> {
          PageResponse<PaymentPageVo> page;
          if (CollUtil.isNotEmpty(pageQuery.getIds())) {
            PaymentPageQuery exportQuery = new PaymentPageQuery();
            exportQuery.setViewAll(pageQuery.getViewAll());
            exportQuery.setIds(pageQuery.getIds());
            exportQuery.setBusinessLine(pageQuery.getBusinessLine());
            exportQuery.setPageSize(9999);
            page = page(exportQuery);
          } else {
            pageQuery.setPageSize(9999);
            page = page(pageQuery);
          }
          if (page.getTotalPages() <= 0) {
            task.setStatus(ExportTaskStatus.FAIL);
            task.setError("数据为空");
            iExportTaskService.updateById(task);
          } else {
            try {
              final List<PaymentExportDto> exportDtoList = convertExportData(page.getData());
              EasyExcel.write(file, PaymentExportDto.class).sheet("付款单").doWrite(exportDtoList);

              UploadFileAction uploadFileAction = UploadFileAction.ofFile(file);
              FileStub fileStub = fileGateway.uploadFile(uploadFileAction);

              task.setDownloadUrl(fileStub.getUrl());
              task.setStatus(ExportTaskStatus.SUCCESS);
            } catch (Exception e) {
              log.error("付款申请导出异常", e);
              task.setError(e.getMessage());
              task.setStatus(ExportTaskStatus.FAIL);
            } finally {
              FileUtil.del(file);
              iExportTaskService.updateById(task);
            }
          }
        });

    return Response.buildSuccess();
  }

  private List<PaymentExportDto> convertExportData(List<PaymentPageVo> dataList) {
    List<PaymentExportDto> exportDtoList = new LinkedList<>();

    for (PaymentPageVo pageVo : dataList) {
      final Long id = pageVo.getId();
      final PaymentApplyOrder paymentApplyOrder = iPaymentApplyOrderService.getById(id);
      if (Objects.isNull(paymentApplyOrder)) continue;

      final List<PaymentApplyOrderDetail> detailList =
          iPaymentApplyOrderDetailService.getByPaymentApplyOrderId(id);
      final Map<String, BigDecimal> otherChargebackMap =
          otherChargeback(
              paymentApplyOrder.getTotalApplyAmount(),
              paymentApplyOrder.getOtherChargeback(),
              detailList);
      for (PaymentApplyOrderDetail detail : detailList) {
        final PaymentExportDto paymentExportDto =
            PaymentApplyTransMapper.INSTANCE.voToExportDto(pageVo);
        paymentExportDto.setRelateNo(detail.getApplyRelatedNo());
        paymentExportDto.setRightAmount(detail.getRightAmount().toString());
        paymentExportDto.setApplyAmount(detail.getApplyAmount());
        paymentExportDto.setRealPayAmount(detail.getApplyAmount());
        paymentExportDto.setOtherChargeback(
            otherChargebackMap.getOrDefault(detail.getApplyRelatedNo(), BigDecimal.ZERO));
        exportDtoList.add(paymentExportDto);
      }
    }

    return exportDtoList;
  }

  /**
   * 获取其他金额计算
   *
   * @return
   */
  private Map<String, BigDecimal> otherChargeback(
      BigDecimal totalAmount,
      BigDecimal otherChargeback,
      List<PaymentApplyOrderDetail> detailList) {
    Map<String, BigDecimal> res = new HashMap<>(detailList.size());
    BigDecimal sum = BigDecimal.ZERO;
    for (int i = 0; i < detailList.size(); i++) {
      if (i != detailList.size() - 1) {
        BigDecimal b1 =
            detailList.get(i).getApplyAmount().divide(totalAmount, 2, RoundingMode.HALF_UP);
        BigDecimal b2 = b1.multiply(otherChargeback).setScale(2, RoundingMode.HALF_UP);
        sum = sum.add(b2);
        res.put(detailList.get(i).getApplyRelatedNo(), b2);
      } else {
        res.put(detailList.get(i).getApplyRelatedNo(), otherChargeback.subtract(sum));
      }
    }
    return res;
  }

  private ExportTask initExportTask(String fileName) {
    ExportTask task = new ExportTask();
    task.setName(fileName);
    task.setStatus(ExportTaskStatus.RUNNING);
    task.setType(ExportTaskType.PAYMENT_APPLY_ORDER);
    iExportTaskService.save(task);
    return task;
  }

  @Override
  public SingleResponse<PaymentDetailAmountVO> refreshRightAmount(RefreshRightAmountCmd cmd) {
    if (cmd.getDetailSource() == 0) {
      return purchaseOrderBizService.getRightPayAmount(
          cmd.getRelatedOrderId(), cmd.getChooseDetailIds(), cmd.getAdvancePayment());
      //            return purchaseOrderBizService.getRightPayAmount(cmd.getRelatedOrderId(),
      // cmd.getChooseDetailIds());
    }
    if (cmd.getDetailSource() == 1) {
      return orderSettlementBizService.getPayAmount(
          cmd.getRelatedOrderId(), cmd.getChooseDetailIds());
    }
    throw ExceptionPlusFactory.bizException(ErrorCode.API_REQ_ERROR, "不支持的明细类型");
  }

  @Override
  public SingleResponse<String> getPaymentInfoByRelatedNo(String relatedNo) {
    List<Dict> resList = new LinkedList<>();

    List<PaymentApplyOrderDetail> list =
        iPaymentApplyOrderDetailService
            .lambdaQuery()
            .eq(PaymentApplyOrderDetail::getApplyRelatedNo, relatedNo)
            .list();
    for (PaymentApplyOrderDetail detail : list) {
      Long paymentApplyOrderId = detail.getPaymentApplyOrderId();
      PaymentApplyOrder paymentApplyOrder = iPaymentApplyOrderService.getById(paymentApplyOrderId);

      String no = paymentApplyOrder.getNo();
      PaymentOrderStatus status = paymentApplyOrder.getStatus();
      BigDecimal applyAmount = detail.getApplyAmount();
      BigDecimal detailRightAmount = detail.getRightAmount();
      String relatedNo1 = detail.getApplyRelatedNo();

      Dict dict =
          Dict.create()
              .set("no", no)
              .set("status", status)
              .set("relatedNo1", relatedNo1)
              .set("applyAmount", applyAmount)
              .set("rightAmount", detailRightAmount);
      resList.add(dict);
    }

    return SingleResponse.of(JsonUtil.toJson(resList));
  }

  @Override
  public Response withdraw(Long id, String comment) {
    final PaymentApplyOrder paymentApplyOrder = iPaymentApplyOrderService.getById(id);
    Assert.notNull(paymentApplyOrder, "单据查询异常");
    final PaymentOrderStatus status = paymentApplyOrder.getStatus();
    if (status != PaymentOrderStatus.WAIT_AUDIT) {
      return Response.buildFailure(ErrorCode.OPERATION_REJECT.getCode(), "仅待审核状态下可以进行该操作");
    }
    final Long createdUid = paymentApplyOrder.getCreatedUid();
    final StaffInfo createUser = userGateway.queryStaffInfoById(createdUid);
    Assert.notNull(createUser, "单据关联用户信息查询异常");

    Assert.isTrue(StringUtil.isNotBlank(paymentApplyOrder.getOaProcessAffairId()), "单据OA审批流ID异常");
    try {
      oaProcessService.processStop(
          paymentApplyOrder.getOaProcessAffairId(), createUser.getLoginName(), comment);
    } catch (Exception e) {
      throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "与OA服务通信异常，请稍后重试");
    }

    final PaymentApplyOrder updateModel = new PaymentApplyOrder();
    updateModel.setId(paymentApplyOrder.getId());
    updateModel.setOaProcessSummaryId("");
    updateModel.setOaProcessAffairId("");
    updateModel.setStatus(PaymentOrderStatus.WAIT_SUBMIT);
    updateModel.setApplyTime(0L);
    updateModel.setLastAuditTime(0L);
    updateModel.setLastAuditUid(0L);
    updateModel.setLastAuditStatus(0);
    iPaymentApplyOrderService.updateById(updateModel);

    operateLogGateway.addOperatorLog(
        UserContext.getUserId(), OperateLogTarget.PAYMENT_APPLY_ORDER, id, "撤回流程:" + comment, null);
    return Response.buildSuccess();
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Response deleteByFormIds(List<Long> ids) {
    QueryWrapper<PaymentApplyOrderDetail> queryWrapper = new QueryWrapper<>();
    queryWrapper.in("payment_apply_order_id", ids);
    iPaymentApplyOrderDetailService.removeWithTime(queryWrapper);
    boolean b = iPaymentApplyOrderService.removeByIdsWithTime(ids);
    if (b) {
      for (Long id : ids) {
        operateLogGateway.addOperatorLog(
            UserContext.getUserId(), OperateLogTarget.PAYMENT_APPLY_ORDER, id, "删除付款申请单", null);
      }
    }
    return Response.buildSuccess();
  }

  @Override
  public SingleResponse<Boolean> updateBuyer(Long id, Long userId) {
    iPaymentApplyOrderService
        .lambdaUpdate()
        .set(PaymentApplyOrder::getBuyerId, userId)
        .eq(PaymentApplyOrder::getId, id)
        .update();
    return SingleResponse.of(true);
  }

  // ---------------------------

  @Override
  public MultiResponse<PaymentOrderWriteOffLog> getWriteOffLog(Long id) {
    List<PaymentOrderWriteOffLog> list =
        iPaymentOrderWriteOffLogService
            .lambdaQuery()
            .eq(PaymentOrderWriteOffLog::getPaymentApplyOrderId, id)
            .orderByAsc(PaymentOrderWriteOffLog::getId)
            .list();
    return MultiResponse.of(list);
  }

  @Override
  public MultiResponse<String> manualWriteOff(Long id) {
    String activeProfile = SpringUtil.getActiveProfile();
    boolean mockSync = activeProfile.equals("test") || activeProfile.equals("local");

    PaymentApplyOrder paymentApplyOrder = iPaymentApplyOrderService.getById(id);
    Assert.notNull(paymentApplyOrder, "付款单 ID 非法");
    if (Objects.isNull(paymentApplyOrder.getFinancialAuditTime())) {
      paymentApplyOrder.setFinancialAuditTime(DateUtil.currentTime());
    }
    iPaymentApplyOrderService.updateById(paymentApplyOrder);

    writeOffBizService.mainProcess(id, true, mockSync);
    MultiResponse<PaymentOrderWriteOffLog> writeOffLog = getWriteOffLog(id);
    List<String> collect =
        writeOffLog.getData().stream()
            .map(PaymentOrderWriteOffLog::getTraceMsg)
            .collect(Collectors.toList());
    return MultiResponse.of(collect);
  }

  @Override
  public Response rollbackSku(Long formId) {
    iOrderSettlementDetailService
        .lambdaUpdate()
        .set(OrderSettlementDetail::getHadWriteOff, 0)
        .eq(OrderSettlementDetail::getFormId, formId)
        .update();
    return Response.buildSuccess();
  }

  // ------------------------------

  @Override
  public MultiResponse<PrintOrderVo> print(PrintCmd cmd) {
    final List<Long> paymentOrderIds = cmd.getPaymentOrderIds();
    if (CollUtil.isEmpty(paymentOrderIds)) {
      return MultiResponse.of(Collections.emptyList());
    }
    List<CompletableFuture<PrintOrderVo>> completableFutureList =
        paymentOrderIds.stream()
            .map(
                id ->
                    CompletableFuture.supplyAsync(
                        () -> getPrintVo(id), ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL)))
            .collect(Collectors.toList());
    CompletableFuture<?>[] array = completableFutureList.toArray(new CompletableFuture<?>[0]);
    CompletableFuture.allOf(array).join();

    final List<PrintOrderVo> printOrderVoList =
        completableFutureList.stream()
            .map(
                completableFuture -> {
                  PrintOrderVo printOrderVo = null;
                  try {
                    printOrderVo = completableFuture.get(5, TimeUnit.SECONDS);
                  } catch (Exception e) {
                    log.error("获取付款单打印信息异常", e);
                  }
                  return printOrderVo;
                })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

    return MultiResponse.of(printOrderVoList);
  }

  private PrintOrderVo getPrintVo(Long id) {
    try {
      PaymentApplyOrder paymentApplyOrder = iPaymentApplyOrderService.getById(id);
      Assert.notNull(paymentApplyOrder, "id非法." + id);

      SingleResponse<PaymentApplyOrderViewVo> view = viewProcess(paymentApplyOrder);
      PaymentApplyOrderViewVo viewData = view.getData();

      PrintOrderVo printOrderVo = new PrintOrderVo();
      printOrderVo.setOrderType("采购付款申请");
      printOrderVo.setOrderNo(viewData.getNo());
      printOrderVo.setApplyDate(viewData.getApplyTimeStr());
      printOrderVo.setTradeObjectType("供应商");
      printOrderVo.setTradeObject(viewData.getTradeUnitStr());
      printOrderVo.setPurchaseOrg("杭州老爸电商科技有限公司");
      printOrderVo.setPurchaseDept(viewData.getBuyerDept());
      printOrderVo.setPurchaseBuyer(viewData.getBuyerName());
      printOrderVo.setInitiator(viewData.getCreatedUser());
      printOrderVo.setApplyAmount(viewData.getTotalApplyAmount().toString());
      printOrderVo.setRemark(viewData.getRemark());

      Integer purchaseType = viewData.getPurchaseType();
      String sourceOrderType = 0 == purchaseType ? "标准采购" : "工厂代发";

      List<PrintOrderDetailVo> printOrderDetailVos = new LinkedList<>();
      BigDecimal rightAmountTotal = BigDecimal.ZERO;
      MultiResponse<PaymentApplyDetailVo> paymentApplyDetailVoMultiResponse =
          viewDetailProcess(id, paymentApplyOrder);
      for (int i = 0; i < paymentApplyDetailVoMultiResponse.getData().size(); i++) {
        final PaymentApplyDetailVo paymentApplyDetailVo =
            paymentApplyDetailVoMultiResponse.getData().get(i);
        final PaymentDetailBaseInfoVo baseInfoVo = paymentApplyDetailVo.getBaseInfoVo();

        PrintOrderDetailVo printOrderDetailVo = new PrintOrderDetailVo();
        printOrderDetailVo.setSort(String.valueOf(i + 1));
        printOrderDetailVo.setSourceOrderType(sourceOrderType);
        printOrderDetailVo.setSourceOrderNo(baseInfoVo.getNo());
        printOrderDetailVo.setPayPurpose(viewData.getPayPurposeStr());
        printOrderDetailVo.setPaymentAmount(baseInfoVo.getRightAmount().toString());
        printOrderDetailVo.setApplyAmount(baseInfoVo.getApplyAmount().toString());
        printOrderDetailVo.setTargetBankNo(viewData.getPayeeBank());
        printOrderDetailVo.setTargetBank(viewData.getPayeeBankCardNo());
        printOrderDetailVos.add(printOrderDetailVo);

        rightAmountTotal = rightAmountTotal.add(baseInfoVo.getRightAmount());
      }
      printOrderVo.setPaymentAmount(rightAmountTotal.toString());
      printOrderVo.setOrderDetailVos(printOrderDetailVos);

      final List<OaCallback> oaCallbacks =
          oaCallbackService
              .lambdaQuery()
              .eq(OaCallback::getBusinessId, viewData.getNo())
              .orderByAsc(OaCallback::getId)
              .list();
      final List<PrintOrderAuditVo> printOrderAuditVoList =
          oaCallbacks.stream()
              .map(this::getPrintOrderAuditVo)
              .filter(Objects::nonNull)
              .collect(Collectors.toList());
      printOrderVo.setAuditVoList(printOrderAuditVoList);

      return printOrderVo;
    } catch (Exception e) {
      log.error("查询付款单打印信息异常。id：{}", id, e);
      return null;
    }
  }

  public PrintOrderAuditVo getPrintOrderAuditVo(OaCallback oaCallback) {
    String callback = oaCallback.getCallback();
    if (StrUtil.isBlank(callback)) {
      return null;
    }
    OaCgfkCallback oaCgfkCallback = JsonUtil.parse(callback, OaCgfkCallback.class);
    if (Objects.isNull(oaCgfkCallback)) {
      return null;
    }
    final String step = getStepByNodeName(oaCgfkCallback.getNodeName());
    if (StrUtil.isBlank(step)) {
      return null;
    }
    PrintOrderAuditVo printOrderAuditVo = new PrintOrderAuditVo();
    printOrderAuditVo.setStep(step);
    printOrderAuditVo.setAdvice(getAdvice(oaCgfkCallback.getStatus()));
    printOrderAuditVo.setApprover(
        userGateway.queryNikcNameByLoginName(oaCgfkCallback.getUserLoginName()));
    printOrderAuditVo.setTime(DateUtil.format(oaCallback.getCreatedAt()));
    return printOrderAuditVo;
  }

  private String getStepByNodeName(String nodeName) {
    if (StrUtil.isBlank(nodeName)) {
      return StrUtil.EMPTY;
    }
    nodeName = nodeName.replaceAll("模拟", "").trim();
    nodeName = nodeName.replaceAll("请款人", "").trim();
    switch (nodeName) {
      case "部门主管":
        return "部门主管";
      case "部门经理":
        return "部门经理";
      case "部门分管领导":
        return "一级分管领导";
      case "业务财务审核":
        return "会计";
      case "财务经理审核":
        return "财务经理";
      case "总裁审核":
        return "总裁";
      case "出纳审核":
        return "出纳";
      default:
        return StrUtil.EMPTY;
    }
  }

  private String getAdvice(Integer status) {
    if (Objects.isNull(status)) {
      return "状态为空";
    }
    // 流程提交1，流程回退2，流程结束3，流程撤销4，流程终止5
    switch (status) {
      case 1:
        return "审核通过";
      case 2:
        return "审核回退";
      case 3:
        return "审核结束";
      case 4:
        return "审核撤销";
      case 5:
        return "审核终止";
      default:
        return "unknown status:" + status;
    }
  }

  @Override
  public Response rollbackAudit(Long id) {
    PaymentApplyOrder paymentApplyOrder = iPaymentApplyOrderService.getById(id);
    Assert.notNull(paymentApplyOrder, "付款单ID非法");
    StaffInfo staffInfo = userGateway.queryStaffInfoById(UserContext.getUserId());
    Assert.notNull(staffInfo, "当前用户信息查询失败");
    PaymentOrderStatus status = paymentApplyOrder.getStatus();
    Assert.state(PaymentOrderStatus.WAIT_AUDIT.equals(status), "只有待审核的单据才允许撤回");
    if (Objects.nonNull(paymentApplyOrder.getSubmitUid()) && paymentApplyOrder.getSubmitUid() > 0) {
      Assert.state(
          UserPermissionJudge.isAdmin()
              || paymentApplyOrder.getSubmitUid().equals(UserContext.getUserId()),
          "只有管理员和单据提交者才允许撤回审核");
    }

    oaProcessService.processStop(
        paymentApplyOrder.getOaProcessAffairId(), staffInfo.getLoginName(), "ERP｜主动撤回审核");
    paymentApplyOrder.setStatus(PaymentOrderStatus.ROLLBACK_AUDIT);
    iPaymentApplyOrderService.updateById(paymentApplyOrder);

    String msg = StrUtil.format("主动撤回【{}】审核", paymentApplyOrder.getOaProcessAffairId());
    operateLogGateway.addOperatorLog(
        UserContext.getUserId(), OperateLogTarget.PAYMENT_APPLY_ORDER, id, msg, null);

    return Response.buildSuccess();
  }
}
