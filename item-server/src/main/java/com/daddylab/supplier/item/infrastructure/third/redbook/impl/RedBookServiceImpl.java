package com.daddylab.supplier.item.infrastructure.third.redbook.impl;

import com.daddylab.supplier.item.infrastructure.third.convert.RedBookConverter;
import com.daddylab.supplier.item.infrastructure.third.redbook.RedBookService;
import com.daddylab.supplier.item.infrastructure.third.redbook.domain.dto.RedBookMessageDTO;
import com.daddylab.supplier.item.infrastructure.third.redbook.domain.query.RedBookItemPageQuery;
import com.daddylab.supplier.item.infrastructure.third.redbook.domain.query.SkuPageQuery;
import com.diffplug.common.base.Errors;
import com.xiaohongshu.fls.opensdk.entity.inventory.request.GetSkuStockRequest;
import com.xiaohongshu.fls.opensdk.entity.inventory.request.IncSkuStockRequest;
import com.xiaohongshu.fls.opensdk.entity.inventory.request.SyncSkuStockRequest;
import com.xiaohongshu.fls.opensdk.entity.inventory.response.SkuStockResponse;
import com.xiaohongshu.fls.opensdk.entity.product.request.v3.GetDetailSkuRequest;
import com.xiaohongshu.fls.opensdk.entity.product.request.v3.GetItemInfoRequest;
import com.xiaohongshu.fls.opensdk.entity.product.request.v3.SearchItemListRequest;
import com.xiaohongshu.fls.opensdk.entity.product.response.v3.GetDetailSkuListResponse;
import com.xiaohongshu.fls.opensdk.entity.product.response.v3.GetItemInfoResponse;
import com.xiaohongshu.fls.opensdk.entity.product.response.v3.SearchItemListResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @class RedBookServiceImpl.java
 * @description 描述类的作用
 * @date 2024-02-28 10:56
 */
@Slf4j
public class RedBookServiceImpl extends RedBookAbstract implements RedBookService {

    @Override
    public SearchItemListResponse getItemPageList(RedBookItemPageQuery itemPageQuery) {
        SearchItemListRequest searchItemListRequest = new SearchItemListRequest();
        searchItemListRequest.setPageNo(itemPageQuery.getPageNo());
        searchItemListRequest.setPageSize(itemPageQuery.getPageSize());
        SearchItemListRequest.ItemSearchParam itemSearchParam = RedBookConverter.INSTANCE.itemPageQueryToItemSearchParam(
                itemPageQuery);
        searchItemListRequest.setSearchParam(itemSearchParam);
        return executeApiResponse(searchItemListRequest,
                Errors.rethrow().wrap(() -> productClient.execute(searchItemListRequest, getAccessToken())));
    }

    @Override
    public GetDetailSkuListResponse getSkuPageList(SkuPageQuery pageQuery) {
        GetDetailSkuRequest detailSkuRequest = RedBookConverter.INSTANCE.skuPageQueryToGetDetailSkuRequest(pageQuery);
        return executeApiResponse(detailSkuRequest,
                Errors.rethrow().wrap(() -> productClient.execute(detailSkuRequest, getAccessToken())));
    }

    @Override
    public GetItemInfoResponse getItemInfo(String itemId) {
        GetItemInfoRequest getItemInfoRequest = new GetItemInfoRequest();
        getItemInfoRequest.setItemId(itemId);
        getItemInfoRequest.setPageNo(1);
        getItemInfoRequest.setPageSize(100);
        return executeApiResponse(getItemInfoRequest,
                Errors.rethrow().wrap(() -> productClient.execute(getItemInfoRequest, getAccessToken())));
    }

    @Override
    public SkuStockResponse getSkuStock(String skuId) {
        GetSkuStockRequest getSkuStockRequest = new GetSkuStockRequest();
        getSkuStockRequest.setSkuId(skuId);
        return executeApiResponse(getSkuStockRequest,
                Errors.rethrow().wrap(() -> inventoryClient.execute(getSkuStockRequest, getAccessToken())));
    }

    @Override
    public SkuStockResponse syncSkuStock(String skuId, Integer num) {
        SyncSkuStockRequest skuSyncStockRequest = new SyncSkuStockRequest();
        skuSyncStockRequest.setSkuId(skuId);
        skuSyncStockRequest.setQty(num);
        return executeApiResponse(skuSyncStockRequest,
                Errors.rethrow().wrap(() -> inventoryClient.execute(skuSyncStockRequest, getAccessToken())));
    }

    @Override
    public SkuStockResponse incSkuStock(String skuId, Integer num) {
        IncSkuStockRequest incSkuStockRequest = new IncSkuStockRequest();
        incSkuStockRequest.setSkuId(skuId);
        incSkuStockRequest.setQty(num);
        return executeApiResponse(incSkuStockRequest,
                Errors.rethrow().wrap(() -> inventoryClient.execute(incSkuStockRequest, getAccessToken())));
    }

    @Override
    public void handle(RedBookMessageDTO message) {
        redBookMessageHandlerSupport.dispatcher(this, message);
    }

}
