package com.daddylab.supplier.item.controller.test.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2024年09月19日 11:43 AM
 */
@Data
public class WdtOrderLogisticDto {

    @ExcelProperty("原始订单号")
    private String srcTids;

    @ExcelProperty("旺店通订单号")
    private String tradeNo;

    @ExcelProperty("店铺名称")
    private String shopName;

    @ExcelProperty("支付时间")
    private String payTime;

    @ExcelProperty("发货时间")
    private String consignTime;

    @ExcelProperty("签约时间")
    private String signingTime;

    @ExcelProperty("交易状态")
    private String tradeStatusStr;

    @ExcelIgnore
    private Integer tradeStatus;

    @ExcelProperty("仓库编码")
    private String warehouseNo;

    @ExcelProperty("仓库名称")
    private String  warehouseName;

    @ExcelProperty("物流名称")
    private String logisticsName;

    @ExcelProperty("物流编码")
    private String logisticsNo;

    @ExcelProperty("物流轨迹")
    private String tracklistStr;

    @ExcelIgnore
    private String tracklist;



}
