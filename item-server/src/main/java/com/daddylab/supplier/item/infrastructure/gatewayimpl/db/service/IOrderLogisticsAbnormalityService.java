package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.application.afterSaleLogistics.dto.LogisticExceptionRes;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.AbnormalStatus;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.ErpLogisticsStatus;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsAbnormality;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsAbnormalityLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsTrace;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.CloseReasonType;
import java.util.Optional;

/**
 * 订单物流异常记录 服务类
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
public interface IOrderLogisticsAbnormalityService
    extends IDaddyService<OrderLogisticsAbnormality> {

  Optional<OrderLogisticsAbnormality> getByTraceId(Long traceId);

  Optional<OrderLogisticsAbnormality> getActivateByTraceId(Long traceId);

  OrderLogisticsAbnormality addAbnormality(
      OrderLogisticsTrace orderLogisticsTrace,
      AbnormalStatus abnormalityStatus,
      ErpLogisticsStatus logisticsStatus,
      LogisticExceptionRes logisticExceptionResult);

  OrderLogisticsAbnormality updateAbnormality(
      OrderLogisticsAbnormality orderLogisticsAbnormality,
      OrderLogisticsTrace orderLogisticsTrace,
      AbnormalStatus abnormalityStatus,
      ErpLogisticsStatus logisticsStatus,
      LogisticExceptionRes logisticExceptionResult);

  Long addAbnormalityLog(Long abnormalityId, LogisticExceptionRes exceptionResult);


  Optional<OrderLogisticsAbnormalityLog> getLog(Long logId);

  void closeAbnormal(
      OrderLogisticsAbnormality abnormality,
      OrderLogisticsTrace orderLogisticsTrace,
      CloseReasonType closeReasonType);

  void addAbnormalitySysLog(OrderLogisticsAbnormality abnormality, String msg);

  void closeAbnormal(OrderLogisticsTrace orderLogisticsTrace, CloseReasonType closeReasonType);
}
