package com.daddylab.supplier.item.infrastructure.submail;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 短信模板类型
 *
 * <AUTHOR> <EMAIL>
 * @version : 1.0.0
 * @date : 2021/1/24 9:05 下午
 */
@Getter
@AllArgsConstructor
public enum SMSTemplate implements IEnum<String> {

    /**
     * 公共
     */
    //code  time
    COMMON_CODE( "qOmlm2", "公共短信验证码"),
    //code  time
    SIGN_IN( "iq48L4", "短信验证码登录"),
    //code  time
    FORGET_PASSWORD( "Ee12U3", "忘记密码"),
    ;

    private final String value;
    private final String desc;
}
