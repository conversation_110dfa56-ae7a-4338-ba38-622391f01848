package com.daddylab.supplier.item.application.third.impl.pdd;

import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopAuthorizationService;
import com.daddylab.supplier.item.infrastructure.third.config.PddConfig;
import com.pdd.pop.sdk.http.PopBaseHttpResponse;
import com.pdd.pop.sdk.http.PopHttpClient;
import com.pdd.pop.sdk.http.api.pop.request.PddGoodsQuantityUpdateRequest;
import com.pdd.pop.sdk.http.api.pop.response.PddGoodsQuantityUpdateResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/4/18
 */
@Service
@Slf4j
public class PddStockServiceImpl implements PddStockService {
    @Autowired
    private PddConfig pddConfig;

    @Autowired
    private IShopAuthorizationService shopAuthorizationService;

    @Override
    public void goodsQuantityUpdate(String shopNo, Long goodsId, Long skuId, Long quantity, Integer updateType) {
        try {
            final ShopAuthorization authorization = shopAuthorizationService.getByShopNo(shopNo)
                                                                            .filter(ShopAuthorization::isNotExpired)
                                                                            .orElseThrow(() -> ExceptionPlusFactory.bizException(
                                                                                    ErrorCode.SHOP_AUTHORIZATION_EXPIRED,
                                                                                    String.format("店铺 %s 未授权或已过期",
                                                                                            shopNo)));
            final PopHttpClient httpClient = pddConfig.getHttpClient();
            final PddGoodsQuantityUpdateRequest pddGoodsQuantityUpdateRequest = new PddGoodsQuantityUpdateRequest();
            pddGoodsQuantityUpdateRequest.setGoodsId(goodsId);
            pddGoodsQuantityUpdateRequest.setQuantity(quantity);
            pddGoodsQuantityUpdateRequest.setSkuId(skuId);
            pddGoodsQuantityUpdateRequest.setUpdateType(updateType);
            final PddGoodsQuantityUpdateResponse response = httpClient.syncInvoke(pddGoodsQuantityUpdateRequest,
                    authorization.getAccessToken());
            final PopBaseHttpResponse.ErrorResponse errorResponse = response.getErrorResponse();
            if (errorResponse != null) {
                throw new PddException(errorResponse.getErrorMsg(), errorResponse);
            }
            log.info("[拼多多][库存更新][店铺 {}][商品 {}][SKU {}][更新方式 {}][库存数 {}]更新成功",
                    shopNo, goodsId, skuId, updateType, quantity);
        } catch (Exception e) {
            log.error("[拼多多][库存更新][店铺 {}][商品 {}][SKU {}][更新方式 {}][库存数 {}]更新异常:{}",
                    shopNo, goodsId, skuId, updateType, quantity, e.getMessage(), e);
            throw ExceptionPlusFactory.bizException(ErrorCode.PDD_ERROR, "拼多多库存更新异常:" + e.getMessage());
        }
    }
}
