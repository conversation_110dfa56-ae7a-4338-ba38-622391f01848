package com.daddylab.supplier.item.domain.platformItem.service.event;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/4/3
 */
@Data
@NoArgsConstructor
public class PlatformItemUpdateEvent {
    private PlatformItem platformItem;
    private PlatformItemSku platformItemSku;
    private boolean isNewItem;
    private boolean isNewSku;
    private boolean outerSkuCodeChanged;
    private boolean stockChanged;
}
