package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtStockSpec;
import com.daddylab.supplier.item.types.stockSpec.ShopAvailableStockQuery;
import com.daddylab.supplier.item.types.stockSpec.ShopRealWarehouseStockSpecVO;
import com.daddylab.supplier.item.types.stockSpec.ShopVirtualWarehouseStockSpecVO;
import com.daddylab.supplier.item.types.stockSpec.WarehouseStockSpecStatistic;
import com.daddylab.supplier.item.types.stockStatistic.WdtStockSpecStatisticQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface WdtStockSpecMapper extends DaddyBaseMapper<WdtStockSpec> {
    
    List<WarehouseStockSpecStatistic> statistics(@Param("query") WdtStockSpecStatisticQuery query);
    
    /**
     * 店铺实仓库存查询
     *
     * @deprecated
     */
    List<ShopRealWarehouseStockSpecVO> shopRealWarehouseStockQuery(@Param("query") ShopAvailableStockQuery query);
    
    /**
     * 店铺虚拟仓库存查询
     *
     * @deprecated
     */
    List<ShopVirtualWarehouseStockSpecVO> shopVirtualWarehouseStockQuery(@Param("query") ShopAvailableStockQuery query);
}
