package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 采购付款申请单详情
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PurchasePayableApplyOrderDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 创建者（关联入库单/出库单/其他应付的创建人）
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 付款请单编号
     */
    private String applyOrderNo;

    /**
     * 付款申请单id
     */
    private Long applyOrderId;

    /**
     * 应付单编号
     */
    private String purchasePayOrderNo;

    private Long relationOrderId;

    private Integer relationOrderType;

    /**
     * sku编号
     */
    private String skuCode;

    /**
     * 修正数量
     */
    private Integer fixedQuantity;

    /**
     * 修正金额
     */
    private BigDecimal fixedTotalAmount;

    private String specifications;

    private String itemName;

    private Integer quantity;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 含税价格，税前价格
     */
    private BigDecimal withTaxPrice;

    /**
     * 不含税价格，税后价格
     */
    private BigDecimal withoutTaxPrice;

    /**
     * 总税额
     */
    private BigDecimal taxTotalAmount;

    /**
     * 价税合计
     */
    private BigDecimal withTaxTotalAmount;

    private String warehouseNo;

    private Long providerId;

    private String errorLog;

}
