/*
package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopInventoryGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ShopInventoryGoodsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopInventoryGoodsService;
import org.springframework.stereotype.Service;

*/
/**
 * <p>
 * 店铺分配库存信息（商品纬度） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 *//*

@Service
public class ShopInventoryGoodsServiceImpl extends DaddyServiceImpl<ShopInventoryGoodsMapper, ShopInventoryGoods> implements IShopInventoryGoodsService {

}
*/
