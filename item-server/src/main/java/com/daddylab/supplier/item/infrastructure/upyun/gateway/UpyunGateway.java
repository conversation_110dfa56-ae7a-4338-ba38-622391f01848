package com.daddylab.supplier.item.infrastructure.upyun.gateway;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.upyun.config.UpyunConfig;
import com.daddylab.supplier.item.infrastructure.upyun.exception.FileGetException;
import com.daddylab.supplier.item.infrastructure.upyun.exception.FileUploadException;
import com.daddylab.supplier.item.infrastructure.upyun.types.UploadFileResult;
import com.daddylab.supplier.item.infrastructure.upyun.types.UploadImageResult;
import com.daddylab.supplier.item.infrastructure.upyun.types.UploadVideoResult;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.upyun.RestManager;
import com.upyun.RestManager.PARAMS;
import com.upyun.UpException;
import com.upyun.UpYunUtils;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class UpyunGateway {

    /**
     * 当上传路径中存在某些特殊字符时，无法正常上传，这边将其替换掉
     */
    public static final List<Character> UNSAFE_CHARS = ImmutableList.<Character>builder()
            .add('%')
            .build();
    private final String AUTHORIZATION = "Authorization";
    private final String DATE = "Date";
    private final String METHOD_HEAD = "HEAD";
    private final String METHOD_GET = "GET";
    private final String METHOD_PUT = "PUT";
    private final String METHOD_POST = "POST";
    private final String METHOD_DELETE = "DELETE";
    private final UpyunConfig config;
    private RestManager restManager = null;
    private OkHttpClient httpClient;
    private int timeout = 30;

    public UpyunGateway(UpyunConfig config) {
        this.config = config;
    }

    private RestManager getRestManager() {
        if (restManager == null) {
            synchronized (this) {
                if (restManager == null) {
                    restManager = new RestManager(config.getBucketName(), config.getOperatorName(),
                            config.getOperatorPwd());
                    restManager.setApiDomain(config.getApiDomain());
                }
            }
        }
        return restManager;
    }

    public UploadImageResult uploadImage(String destPath, InputStream inputStream,
                                         String gmkerlThumb) {
        Response response = null;
        try {
            final byte[] dataBytes = IOUtil.readBytes(inputStream, false);
            inputStream = new ByteArrayInputStream(dataBytes);

            Map<String, String> params = new HashMap<>();
            //计算文件哈希值
            params.put(RestManager.PARAMS.CONTENT_MD5.getValue(), UpYunUtils.md5(dataBytes));
            //图片处理参数
            if (StringUtil.isNotEmpty(gmkerlThumb)) {
                params.put(RestManager.PARAMS.X_GMKERL_THUMB.getValue(), gmkerlThumb);
            }

            final String filePathEncoded = encode(destPath);
            log.info("上传图片至又拍云 路径={} 编码后路径={} 参数={} 数据大小={}B", destPath, filePathEncoded, params,
                    dataBytes.length);
            response = getRestManager().writeFile(filePathEncoded, inputStream, params);
            if (!response.isSuccessful()) {
                throw new FileUploadException("文件上传失败，又拍云返回逻辑异常：" + response);
            }
            return getUploadImageResult(filePathEncoded, response);
        } catch (UpException e) {
            log.error("上传图片至又拍云失败 路径={}" + e.getMessage(), destPath, e);
            throw new FileUploadException("文件上传失败，又拍云返回异常", e);
        } catch (IOException e) {
            log.error("上传图片至又拍云失败 路径={}" + e.getMessage(), destPath, e);
            throw new FileUploadException("文件上传失败，IO异常", e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    private String encode(String path) {
        final Charset charset = StandardCharsets.UTF_8;
        final StringBuilder rewrittenPath = new StringBuilder(path.length());
        ByteArrayOutputStream buf = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(buf, charset);

        int c;
        for (int i = 0; i < path.length(); i++) {
            c = path.charAt(i);

            // convert to external encoding before hex conversion
            try {
                writer.write((char) c);
                writer.flush();
            } catch (IOException e) {
                buf.reset();
                continue;
            }

            if (UNSAFE_CHARS.contains((char) c)) {
                byte[] ba = buf.toByteArray();
                for (byte toEncode : ba) {
                    // Converting each byte in the buffer
                    rewrittenPath.append('%');
                    HexUtil.appendHex(rewrittenPath, toEncode, false);
                }
            } else {
                rewrittenPath.append((char) c);
            }

            buf.reset();
        }
        return rewrittenPath.toString();
    }

    @NonNull
    private UploadImageResult getUploadImageResult(String destPath, Response response) {
        Headers headers = response.headers();
        final UploadImageResult uploadImageResult = new UploadImageResult();
        uploadImageResult.setUrl(config.getUpyunBaseUrl() + destPath);
        uploadImageResult.setPath(destPath);
        Optional.ofNullable(headers.get("x-upyun-width")).map(Integer::valueOf)
                .ifPresent(uploadImageResult::setHeight);
        Optional.ofNullable(headers.get("x-upyun-height")).map(Integer::valueOf)
                .ifPresent(uploadImageResult::setWidth);
        Optional.ofNullable(headers.get("x-upyun-content-length")).map(Integer::valueOf)
                .ifPresent(uploadImageResult::setSize);
        Optional.ofNullable(headers.get("x-upyun-content-type"))
                .ifPresent(uploadImageResult::setContentType);
        Optional.ofNullable(headers.get("x-upyun-file-type"))
                .ifPresent(uploadImageResult::setFileType);
        Optional.ofNullable(headers.get("x-request-id")).ifPresent(uploadImageResult::setRequestId);
        Optional.ofNullable(headers.get("x-request-path"))
                .ifPresent(uploadImageResult::setRequestPath);
        Optional.ofNullable(headers.get("etag")).ifPresent(uploadImageResult::setEtag);
        return uploadImageResult;
    }

    /**
     * 上传文件到又拍云
     *
     * @param inputStream 输入流
     * @param destPath    目标路径
     * @return
     */
    public UploadFileResult uploadFile(InputStream inputStream, String destPath) {
        return uploadFile(inputStream, destPath, null);
    }

    /**
     * 上传文件到又拍云
     *
     * @param inputStream 输入流
     * @param destPath    目标路径
     * @param md5         md5 校验md5值
     * @return
     */
    public UploadFileResult uploadFile(InputStream inputStream, String destPath, String md5) {

        UploadFileResult fileResult = new UploadFileResult();

        Response response = null;
        try {
            final String filePathEncoded = encode(destPath);
            String fileUri = config.getUpyunBaseUrl() + filePathEncoded;
            if (StrUtil.isBlank(md5)) {
                response = getRestManager().writeFile(filePathEncoded, inputStream, null);
            } else {
                final HashMap<String, String> params = new HashMap<>();
                params.put(PARAMS.CONTENT_MD5.getValue(), md5);
                response = getRestManager().writeFile(filePathEncoded, inputStream, params);
            }

            Headers headers = response.headers();
            String size = headers.get("x-upyun-content-length");
            log.debug("upload file response:" + response + "\n" + headers);

            if (!response.isSuccessful()) {
                log.error("【UpyunUtil】文件上传失败!! response:{}", JsonUtil.objToStr(response));
                throw new FileUploadException("文件上传失败");
            }

            fileResult.setPath(filePathEncoded);
            fileResult.setUrl(fileUri);

            if (StringUtils.isNotBlank(size)) {
                fileResult.setSize(Integer.valueOf(size));
            }
            String contentType = headers.get("x-upyun-content-type");
            fileResult.setContentType(contentType);
        } catch (Exception e) {
            log.error("【UpyunUtil】上传文件失败!!", e);
            throw new FileUploadException("文件上传失败，又拍云返回异常", e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return fileResult;
    }

    public UploadVideoResult uploadVideo(InputStream inputStream, String destPath, String md5) {
        final UploadFileResult uploadFileResult = uploadFile(inputStream, destPath, md5);
        log.info("uploadVideo uploadFileResult:{}", JsonUtil.toJson(uploadFileResult));
        final String videoFirstFrame = getVideoFirstFrame(uploadFileResult.getPath());
        final UploadVideoResult uploadVideoResult = new UploadVideoResult();
        uploadVideoResult.setUrl(uploadFileResult.getUrl());
        uploadVideoResult.setPath(uploadFileResult.getPath());
        uploadVideoResult.setSize(uploadFileResult.getSize());
        uploadVideoResult.setVideoFirstFrame(videoFirstFrame);
        uploadVideoResult.setContentType(uploadFileResult.getContentType());
        return uploadVideoResult;
    }


    /**
     * 获取视频首帧
     *
     * @param source 原视频
     * @return 图片快照地址
     */
    public String getVideoFirstFrame(String source) {
        return getVideoFirstFrame(source,
                FileUtil.getPathDir(source) +
                        FileUtil.getNameWithoutSuffix(source) + "-SNAP.jpg");
    }

    /**
     * 获取视频首帧
     *
     * @param source 原视频
     * @param saveAs 图片快照保存地址
     * @return 图片快照地址
     */
    public String getVideoFirstFrame(String source, String saveAs) {
        return getVideoSnapshot(source, saveAs, "00:00:00");
    }

    /**
     * @param source 原视频
     * @param point  快照时间点
     * @return 图片快照地址
     */
    public String getVideoSnapshot(String source, String point) {
        return getVideoSnapshot(source,
                FileUtil.getPathDir(source) + FileUtil.getNameWithoutSuffix(source) + "-SNAP-" + point
                        + ".jpg", point);
    }

    /**
     * @param source 原视频
     * @param saveAs 图片快照保存地址
     * @param point  快照时间点
     * @return 图片快照地址
     */
    public String getVideoSnapshot(String source, String saveAs, String point) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("source", source);
        params.put("save_as", saveAs);
        params.put("point", point);

        final RequestBody body = RequestBody
                .create(MediaType.get("application/json"), JsonUtil.toJson(params));
        final Response response = request(METHOD_POST, "/snapshot", body, null);
        final Map<String, String> responseMap = parseResponseBody(response,
                new TypeReference<Map<String, String>>() {
                });
        String imageUri = responseMap.get("save_as");
        if (StringUtil.isBlank(imageUri)) {
            throw new FileUploadException("又拍云获取视频首帧异常，" + responseMap.get("msg"));
        }
        return config.getUpyunBaseUrl() + imageUri;
    }

    @NotNull
    private <T> T parseResponseBody(Response response, TypeReference<T> typeReference) {
        try {
            final ResponseBody body = response.body();
            if (body == null) {
                throw new FileUploadException("读取又拍云响应异常:响应体为空");
            }
            return JsonUtil.parse(body.string(), typeReference);
        } catch (IOException e) {
            throw new FileUploadException("读取又拍云响应异常:" + e.getMessage(), e);
        }
    }

    public OkHttpClient getHttpClient() {
        if (httpClient == null) {
            this.httpClient = new OkHttpClient.Builder()
                    .connectTimeout(timeout, TimeUnit.SECONDS)
                    .readTimeout(timeout, TimeUnit.SECONDS)
                    .writeTimeout(timeout, TimeUnit.SECONDS)
                    .build();
        }
        return httpClient;
    }

    private Response request(String method, String path, RequestBody body,
                             Map<String, String> params) {

        try {
            String date = UpYunUtils.getGMTDate();

            // 获取链接
            String url = config.getEdAuto() + UpYunUtils.formatPath(config.getBucketName(), path);

            String sign = UpYunUtils
                    .sign(method, date, HttpUrl.get(url).encodedPath(), config.getOperatorName(),
                            UpYunUtils.md5(config.getOperatorPwd()),
                            params == null ? null : params.get(
                                    PARAMS.CONTENT_MD5.getValue()));

            Request.Builder builder = new Request.Builder()
                    .url(url)
                    .header(DATE, date)
                    .header(AUTHORIZATION, sign)
                    .header("User-Agent", UpYunUtils.VERSION)
                    .method(method, body);

            if (params != null) {
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    builder.header(entry.getKey(), entry.getValue());
                }
            }
            return getHttpClient().newCall(builder.build()).execute();
        } catch (UpException e) {
            throw new FileUploadException("又拍云异常:" + e.getMessage(), e);
        } catch (IOException e) {
            throw new FileUploadException("网络请求异常:" + e.getMessage(), e);
        }
    }

    public Map<String, String> getRestfulUploadSign(String path) {
        try {
            final HashMap<String, String> signParams = new HashMap<>();
            signParams.put("method", "POST");
            String date = UpYunUtils.getGMTDate();
            signParams.put("date", date);
            signParams.put("path", path);
            String url =
                    getRestManager().getApiDomain() + UpYunUtils.formatPath(config.getBucketName(),
                            path);
            signParams.put("url", url);
            String sign = UpYunUtils
                    .sign(METHOD_POST, date, HttpUrl.get(url).encodedPath(),
                            config.getOperatorName(),
                            UpYunUtils.md5(config.getOperatorPwd()), null);
            signParams.put("sign", sign);
            signParams.put("cdnUrl", config.getUpyunBaseUrl() + path);
            return signParams;
        } catch (UpException e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM,
                    "签名计算异常，" + e.getMessage());
        }
    }

    public Map<String, String> getFormUploadSign(String path) {
        try {
            //处理 path，在文件名称后增加毫秒时间戳，避免文件名称重复导致文件覆盖，注意需保留原先的文件扩展名
            path = ensureDistinctPath(path);
            final HashMap<String, String> policydata = new HashMap<>();
            policydata.put("bucket", config.getBucketName());
            policydata.put("save-key", path);
            policydata.put("expiration", String.valueOf(DateUtil.currentTime() + 600));
            String date = UpYunUtils.getGMTDate();
            policydata.put("date", date);
            final String policyJson = JsonUtil.toJson(policydata);
            final String policy = Base64.encode(policyJson);
            String uri = "/" + config.getBucketName();
            final HashMap<String, String> signParams = new HashMap<>();
            final String method = METHOD_POST;
            signParams.put("method", method);
            signParams.put("date", date);
            signParams.put("path", path);
            String url =
                    getRestManager().getApiDomain() + UpYunUtils.formatPath(config.getBucketName(),
                            path);
            signParams.put("url", url);
            String sign = UpYunUtils
                    .sign(method, date, uri, config.getOperatorName(),
                            UpYunUtils.md5(config.getOperatorPwd()), policy);
            signParams.put("sign", sign);
            signParams.put("cdnUrl", config.getUpyunBaseUrl() + path);
            signParams.put("policy", policy);
            signParams.put("uploadUrl", getRestManager().getApiDomain() + uri);
            return signParams;
        } catch (UpException e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM,
                    "签名计算异常，" + e.getMessage());
        }
    }

    @org.jetbrains.annotations.NotNull
    private static String ensureDistinctPath(String path) {
        int indexOfDot = path.lastIndexOf(".");
        if (indexOfDot > -1) {
            String filenameWithoutExt = path.substring(0, indexOfDot);
            String ext = path.substring(indexOfDot);
            path = filenameWithoutExt + "-" + DateUtil.currentTimeMillis() + ext;
        } else {
            path = path + "-" + DateUtil.currentTimeMillis();
        }
        return path;
    }

    public Map<String, List<String>> getFileInfo(String url) {
        try {
            url = URLUtil.encode(URLUtil.decode(url));
            String path = url;
            if (StringUtil.startWith(url, "http")) {
                path = URLUtil.getPath(url);
            }
            final Response response = getRestManager().getFileInfo(path);
            if (response.code() != 200) {
                return null;
            }
            return response.headers().toMultimap();
        } catch (UpException e) {
            throw new FileUploadException("又拍云异常:" + e.getMessage(), e);
        } catch (IOException e) {
            throw new FileUploadException("网络请求异常:" + e.getMessage(), e);
        }
    }

    /**
     * 从又拍云接口获取文件元信息
     *
     * @param url 链接
     * @return 文件不存在返回null
     */
    public MediaMeta getMediaMeta(String url) {
        final String urlString = url + "!/info";
        final String responseContent;
        try (HttpResponse response = HttpRequest.get(urlString).timeout(timeout * 1000)
                .execute()) {
            if (response.getStatus() != 200) {
                return null;
            }
            responseContent = response.body();
            return JsonUtil.parse(responseContent, MediaMeta.class);
        } catch (Exception e) {
            throw new FileGetException(e.getMessage(), e);
        }
    }


}
