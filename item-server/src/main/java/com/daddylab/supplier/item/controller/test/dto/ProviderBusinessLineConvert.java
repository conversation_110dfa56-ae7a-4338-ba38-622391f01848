package com.daddylab.supplier.item.controller.test.dto;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.metadata.data.WriteCellData;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> up
 * @date 2024年01月17日 10:23 AM
 */
public class ProviderBusinessLineConvert implements Converter<String> {

    private static final Map<Integer, String> map = new HashMap<>(4);

    static {
        map.put(0, "电商");
        map.put(1, "老爸抽检");
        map.put(2, "绿色家装");
        map.put(3, "商家入驻");
    }

    @Override
    public WriteCellData<?> convertToExcelData(WriteConverterContext<String> context) throws Exception {
        String value = context.getValue();
        List<String> ll = new LinkedList<>();
        // 0电商 1老爸抽检 2绿色家装 3商家入驻
        String[] split = value.split(StrUtil.COMMA);
        for (String s : split) {
            ll.add(map.get(Integer.valueOf(s)));
        }
        return new WriteCellData<>(StrUtil.join(StrUtil.COMMA, ll));
    }
}
