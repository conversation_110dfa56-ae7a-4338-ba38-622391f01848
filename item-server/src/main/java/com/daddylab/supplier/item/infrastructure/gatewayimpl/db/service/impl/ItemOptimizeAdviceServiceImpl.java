package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemOptimizeAdvice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemOptimizeAdviceMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemOptimizeAdviceService;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品优化建议 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Service
public class ItemOptimizeAdviceServiceImpl extends DaddyServiceImpl<ItemOptimizeAdviceMapper, ItemOptimizeAdvice> implements IItemOptimizeAdviceService {

}
