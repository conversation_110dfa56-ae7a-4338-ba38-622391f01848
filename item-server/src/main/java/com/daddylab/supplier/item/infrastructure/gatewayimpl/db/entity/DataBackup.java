package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 数据备份表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataBackup implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 是否删除：0否1是
     */
    @TableLogic(value = "0", delval = "id")
    private Long isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;


    /**
     * 备份数据
     */
    private String backupData;

    /** key1 */
    private String key1;

    /** key2 */
    private String key2;

    /** key3 */
    private String key3;

}
