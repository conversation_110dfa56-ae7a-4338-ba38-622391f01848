/*
package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.VirtualWarehouse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.VirtualWarehouseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IVirtualWarehouseService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

*/
/**
 * <p>
 * 虚拟仓 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 *//*

@Service
@RequiredArgsConstructor
public class VirtualWarehouseServiceImpl extends DaddyServiceImpl<VirtualWarehouseMapper, VirtualWarehouse> implements IVirtualWarehouseService {

    @Resource
    VirtualWarehouseMapper virtualWarehouseMapper;

    @Override
    public String getNewNo() {
        String no = GlobalConstant.VIRTUAL_WAREHOUSE_CODE_PREFIX + "0001";
        final String prefix = GlobalConstant.VIRTUAL_WAREHOUSE_CODE_PREFIX;
        final Long id = virtualWarehouseMapper.getLatestId();
        if (Objects.nonNull(id)) {
            long v = (id + 1);
            if (id > 9999) {
                no = prefix + v;
            } else {
                no = prefix + String.format("%04d", v);
            }
        }
        return no;
    }
}
*/
