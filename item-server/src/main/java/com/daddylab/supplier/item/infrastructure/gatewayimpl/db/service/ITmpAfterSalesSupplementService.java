package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.TmpAfterSalesSupplement;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 2月份客服登记差异表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-26
 */
public interface ITmpAfterSalesSupplementService extends IDaddyService<TmpAfterSalesSupplement> {

}
