package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderReq;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.purchase.dto.PartnerPurchaseReq;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.purchase.dto.PartnerPurchaseResp;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.types.partner.CooperateModeNotify;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/27 4:17 下午
 * @description
 */
@FeignClient(name = "PartnerFeignClient", url = "${partner-server.url}"
        , configuration = PartnerServerConfiguration.class
        , fallbackFactory = PartnerFeignClientFallbackFactory.class)
public interface PartnerFeignClient {

    /**
     * 供应商查询
     *
     * @param req
     * @return
     */
    @PostMapping(path = "/dpm/bms/center/organization/search")
    String providerQuery(@RequestBody PartnerProviderReq req);

    /**
     * 商品查询
     *
     * @param req
     * @return
     */
    @GetMapping(path = "/dpm/bms/center/item/search")
    Rsp<List<PartnerItemResp>> itemQuery(@SpringQueryMap PartnerItemReq req);

    /**
     * 手机号查询
     *
     * @param req
     * @return
     */
    @GetMapping(path = "/dpm/bms/center/mobile/search")
    Rsp<List<PartnerPurchaseResp>> mobileQuery(@SpringQueryMap PartnerPurchaseReq req);

    /**
     * 合同查询
     */
    @PostMapping(path = "/dpm/bms/cooperate/contract/select")
    Rsp<List<PartnerContract>> contractQuery(@RequestBody PartnerContractQuery query);


    /**
     * 负责人列表
     *
     * @param req
     * @return
     */
    @GetMapping(path = "/dpm/bms/center/charge-person")
    Rsp<List<PartnerPersonResp>> personIdsQuery(@SpringQueryMap PartnerPersonReq req);


    /**
     * 根据商品获取获取检测报告信息
     *
     * @param partnerItemId 合作伙伴商品id
     * @return
     */
    @GetMapping(path = "/dpm/bms/openapi/erp/check-info")
    Rsp<CheckInfoDTO> getCheckInfo(@RequestParam("item_id") Long partnerItemId);


    /**
     * 获取检测报告详情
     *
     * @param checkId
     * @return
     */
    @GetMapping(path = "/dpm/bms/openapi/erp/check-detail")
    Rsp<CheckDetailDTO> getCheckDetail(@RequestParam("check_id") Long checkId);

    /**
     * 商品检测报告【新版】
     *
     * @param partnerItemId P系统商品ID
     * @return 响应数据模型
     */
    @GetMapping(path = "/dpm/bms/openapi/erp/common_check_info")
    Rsp<CommonCheckInfo> commonCheckInfo(@RequestParam("item_id") Long partnerItemId);


    @PostMapping(path = "/dpm/bms/center/item/sync_charge_person")
    Rsp<Object> notifyBuryOrQcChange(@RequestBody PartnerSyncChargePersonReqWrapper reqWrapper);

    @PostMapping(path = "/dpm/bms/center/item/sync_business")
    Rsp<Object> syncCooperateMode(@RequestBody CooperateModeNotify notify);

    @PostMapping(path = "/dpm/bms/center/item/get_partners_by_org_id")
    Rsp<List<PartnerContacts>> getPartnerContactList(@RequestBody GetPartnerListReq req);

    @PostMapping(path = "/dpm/bms/center/item/get_partners_by_item_no")
    Rsp<PartnerContacts> getPartnerContactListByItemNo(@RequestBody GetPartnersByItemNoReq req);

    /**
     * 生成OA跳转URL
     * @param req
     * @return
     */
    @PostMapping(path = "/dpm/bms/openapi/generate_oa_url")
    Rsp<String> generateOaUrl(@RequestBody OaGenerateUrlReq req);



}
