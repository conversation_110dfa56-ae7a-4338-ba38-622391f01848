package com.daddylab.supplier.item.infrastructure.config;

import cn.hutool.core.util.ReflectUtil;
import org.redisson.RedissonPermitExpirableSemaphore;
import org.redisson.command.CommandAsyncExecutor;

/**
 * <AUTHOR>
 * @since 2022/5/13
 */
public class PermitExpirableSemaphore extends RedissonPermitExpirableSemaphore {

    public PermitExpirableSemaphore(CommandAsyncExecutor commandExecutor,
            String name) {
        super(commandExecutor, name);
        ReflectUtil.setFieldValue(this, "timeoutName", suffixName(getRawName(), "timeout"));
    }
}
