package com.daddylab.supplier.item.controller.itemOptimize;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.itemOptimize.ItemOptimizePlanBizService;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.common.domain.dto.IdCmd;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.types.itemOptimize.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/itemOptimize/plan")
@Api(value = "商品优化计划相关API", tags = "商品优化计划")
public class ItemOptimizePlanController {

    @Autowired private ItemOptimizePlanBizService itemOptimizePlanBizService;
    @Autowired private OperateLogBizService operateLogBizService;

    @ApiOperation("商品优化计划查询")
    @PostMapping("/query")
    public PageResponse<ItemOptimizePlanBaseInfo> query(
            @Validated @RequestBody ItemOptimizePlanQuery query) {
        query.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(query.getBusinessLine()));
        return itemOptimizePlanBizService.query(query);
    }

    @ApiOperation("商品优化计划明细查询")
    @PostMapping("/queryDetail")
    public SingleResponse<ItemOptimizePlanDetail> queryDetail(
            @Validated @RequestBody ItemOptimizePlanDetailQuery query) {
        return itemOptimizePlanBizService.queryDetail(query);
    }

    @ApiOperation("商品优化计划保存")
    @PostMapping("/save")
    public SingleResponse<Long> save(@Validated @RequestBody ItemOptimizePlanSaveCmd saveCmd) {
        saveCmd.setCurrentUid(UserContext.getUserId());
        return itemOptimizePlanBizService.save(saveCmd);
    }

    @ApiOperation("删除计划")
    @PostMapping("/delete")
    public Response delete(@Validated @RequestBody IdCmd cmd) {
        return itemOptimizePlanBizService.delete(cmd.getId());
    }

    @ApiOperation("平台商品数据匹配")
    @PostMapping("/match")
    public SingleResponse<ItemOptimizePlanItem> match(
            @Validated @RequestBody ItemOptimizePlanItemMatchQuery query) {
        return itemOptimizePlanBizService.match(query);
    }

    @GetMapping(value = "/operateLogs")
    @ApiOperation("操作日志")
    public Response operateLogs(Long targetId) {
        return operateLogBizService.getOperateLogs(OperateLogTarget.ITEM_OPTIMIZE_PLAN, targetId);
    }
}
