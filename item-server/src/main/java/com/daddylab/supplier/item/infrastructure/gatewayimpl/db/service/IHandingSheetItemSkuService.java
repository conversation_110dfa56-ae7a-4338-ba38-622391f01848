package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetItemSku;

import java.util.List;
import java.util.Optional;

/**
 * 盘货表SKU纬度数据（V2.4.5新版盘货表） 服务类
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
public interface IHandingSheetItemSkuService extends IDaddyService<HandingSheetItemSku> {

    default List<HandingSheetItemSku> selectByHandingSheetId(Long handingSheetId) {
        return lambdaQuery().eq(HandingSheetItemSku::getHandingSheetId, handingSheetId).list();
    }

    int getToBeConfirmedItemNum(Long handingSheetId);

    Optional<Long> getHandingSheetItemSpuId(Long handingSheetItemSkuId);

    int countItemSpuByHandingSheetId(Long handingSheetId);
}
