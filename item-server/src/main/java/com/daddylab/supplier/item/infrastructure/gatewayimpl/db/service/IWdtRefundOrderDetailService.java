package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrderDetail;

/**
 * <p>
 * 旺店通退换单明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
public interface IWdtRefundOrderDetailService extends IDaddyService<WdtRefundOrderDetail> {

}
