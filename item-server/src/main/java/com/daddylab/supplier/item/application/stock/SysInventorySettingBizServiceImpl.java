package com.daddylab.supplier.item.application.stock;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.common.trans.SysInventorySettingAssembler;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.SysInventorySetting;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ISysInventorySettingService;
import com.daddylab.supplier.item.types.stock.SysInventorySettingVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/3/6
 */
@Service
public class SysInventorySettingBizServiceImpl implements SysInventorySettingBizService {
    @Resource
    private ISysInventorySettingService sysInventorySettingService;

    @Override
    public Response save(SysInventorySettingVO vo) {
        final SysInventorySetting setting = sysInventorySettingService.getSysInventorySetting();
        final SysInventorySetting updateModel = SysInventorySettingAssembler.INST.vo2po(vo);
        updateModel.setId(setting.getId());
        sysInventorySettingService.updateById(updateModel);
        return Response.buildSuccess();
    }

    @Override
    public SingleResponse<SysInventorySettingVO> view() {
        final SysInventorySetting setting = sysInventorySettingService.getSysInventorySetting();
        SysInventorySettingVO vo = SysInventorySettingAssembler.INST.po2vo(setting);
        return SingleResponse.of(vo);
    }


}
