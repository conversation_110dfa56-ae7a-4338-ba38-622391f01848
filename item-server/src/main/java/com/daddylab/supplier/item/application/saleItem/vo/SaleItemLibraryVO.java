package com.daddylab.supplier.item.application.saleItem.vo;

import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.platformItem.data.PlatformItemDetail;
import com.daddylab.supplier.item.application.saleItem.ShopSnData;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;
import lombok.Data;

@Data
@ApiModel("销售商品库商品返回结果")
public class SaleItemLibraryVO implements Serializable {

    private static final long serialVersionUID = -2336427737556490867L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("商品SKU编码")
    private String itemSkuCode;

    @ApiModelProperty("会员店商品ID")
    private String memberStoreItemId;

    @ApiModelProperty("小程序商品ID")
    private String appletsItemId;

    @ApiModelProperty("美妆店商品ID")
    private String beautyStoreItemId;

    @ApiModelProperty("母婴店商品ID")
    private String maternityBabyItemId;

    @ApiModelProperty("小红书商品ID")
    private String wotoBuyItemId;

    @ApiModelProperty("抖音商品ID")
    private String tikTokItemId;

    @ApiModelProperty("直播/短视频/社群价格")
    private String platformPrice;

    @ApiModelProperty("直播/短视频/社群活动内容")
    private String platformActivityContent;

    @ApiModelProperty("直播/短视频/社群成本")
    private String platformCost;

    @ApiModelProperty("直播/短视频/社群活动编码")
    private String platformActivityCode;

    @ApiModelProperty("特殊备注（仅限某渠道、库存限量等）")
    private String remark;

    @ApiModelProperty("商品名称")
    private String goodsName;
    @ApiModelProperty("商品状态")
    private PlatformItemStatus itemStatus;

    @ApiModelProperty("商品链接")
    private String itemLink;

    @ApiModelProperty("行业类目")
    private String category;

    @ApiModelProperty("规格")
    private String specification;

    @ApiModelProperty("是否能48小时发货")
    private String shipmentAging;

    @ApiModelProperty("日销价")
    private BigDecimal dailyPrice;

    @ApiModelProperty("日常活动内容")
    private String activeContent;

    @ApiModelProperty("采购人")
    private String buyerName;

    @ApiModelProperty("商品ID")
    private Long itemId;

    @ApiModelProperty("商品Code")
    private String itemCode;

    @ApiModelProperty("商品类型 0：后端商品，1：组合商品")
    private Integer skuType;


    public void setSaleItemLibraryVO(SaleItemLibraryVO saleItemLibraryVO, Long itemId, String itemCode, NewGoods newGoods) {
        if (StringUtil.isNotBlank(itemSkuCode)) {
            final String sub = StrUtil.sub(itemSkuCode, 0, 2);
            if (Objects.equals("MU", sub)) {
                saleItemLibraryVO.setSkuType(1);
            } else {
                saleItemLibraryVO.setSkuType(0);
            }
            if(Objects.nonNull(itemId)){
                saleItemLibraryVO.setItemId(itemId);
            }
            if(Objects.nonNull(itemCode)){
                saleItemLibraryVO.setItemCode(itemCode);
            }
        }
//        if(Objects.nonNull(newGoods)){
//            if(Objects.nonNull(newGoods.getShipmentAging())){
//                saleItemLibraryVO.setShipmentAging(newGoods.getShipmentAging());
//            }
//            if(Objects.nonNull(newGoods.getDailyPrice())){
//                saleItemLibraryVO.setDailyPrice(newGoods.getDailyPrice());
//            }
//            if(Objects.nonNull(newGoods.getActiveContent())){
//                saleItemLibraryVO.setActiveContent(newGoods.getActiveContent());
//            }
//            if(Objects.nonNull(newGoods.getBuyerName())){
//                saleItemLibraryVO.setBuyerName(newGoods.getBuyerName());
//            }
//        }
    }



    public void setPlatformItemId(PlatformItemDetail platformItemDetail, SaleItemLibraryVO saleItemLibraryVO, ShopSnData shopSnData, String saleItemLibraryItemLink){
        if(StringUtil.equals(platformItemDetail.getShopNo(), shopSnData.getMemberStoreSn())){
            saleItemLibraryVO.setMemberStoreItemId(platformItemDetail.getOuterItemId());
        }else if(StringUtil.equals(platformItemDetail.getShopNo(), shopSnData.getAppletsSn())){
            saleItemLibraryVO.setAppletsItemId(platformItemDetail.getOuterItemId());
        }else if(StringUtil.equals(platformItemDetail.getShopNo(), shopSnData.getBeautyStoreSn())){
            saleItemLibraryVO.setBeautyStoreItemId(platformItemDetail.getOuterItemId());
        }else if(StringUtil.equals(platformItemDetail.getShopNo(), shopSnData.getMaternityBabySn())){
            saleItemLibraryVO.setMaternityBabyItemId(platformItemDetail.getOuterItemId());
        }else if(StringUtil.equals(platformItemDetail.getShopNo(), shopSnData.getWotoBuySn())){
            saleItemLibraryVO.setWotoBuyItemId(platformItemDetail.getOuterItemId());
        }else if(StringUtil.equals(platformItemDetail.getShopNo(), shopSnData.getTikTokSn())){
            saleItemLibraryVO.setTikTokItemId(platformItemDetail.getOuterItemId());
        }
        if(StringUtil.isNotBlank(saleItemLibraryVO.getMemberStoreItemId())){
            saleItemLibraryVO.setItemLink(String.format(saleItemLibraryItemLink, saleItemLibraryVO.getMemberStoreItemId()));
            saleItemLibraryVO.setItemStatus(platformItemDetail.getStatus());
            saleItemLibraryVO.setGoodsName(platformItemDetail.getGoodsName());
        }
    }
}
