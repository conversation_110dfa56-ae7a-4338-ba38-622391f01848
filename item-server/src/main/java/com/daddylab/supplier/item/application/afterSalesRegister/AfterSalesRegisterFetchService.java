package com.daddylab.supplier.item.application.afterSalesRegister;

import java.time.YearMonth;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2023/8/21
 */
public interface AfterSalesRegisterFetchService {
    /**
     * 拉取指定月份的【客服售后登记表】数据
     *
     * @param month 月份
     */
    CompletableFuture<Void> fetchBanniu(YearMonth month);

    /**
     * 拉取老爸商城维权管理数据
     * @param month 月份
     */
    CompletableFuture<Void> fetchMall(YearMonth month);
}
