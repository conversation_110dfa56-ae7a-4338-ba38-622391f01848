package com.daddylab.supplier.item.controller.platformItem;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/9
 */
@Data
public class PlatformItemSyncCmd {
    @NotEmpty(message = "ID不能为空")
    @ApiModelProperty(name = "ids", required = true)
    List<String> ids;

    @ApiModelProperty("平台")
    @NotNull(message = "平台不得为空")
    Platform platform;
}
