package com.daddylab.supplier.item.infrastructure.submail.utils;

import java.text.SimpleDateFormat;

/**
 *
 * @Auther: <EMAIL>
 * @version: 1.0.0
 * @Date: 2021/04/16/4:01 下午
 */
public class UnixStamp {
	 /**
     * 日期格式字符串转换成时间戳
     *
     * @param dateStr 字符串日期
     * @param format   如：yyyy-MM-dd HH:mm:ss
     *
     * @return
     */
    public static String date2TimeStamp(String dateStr, String format) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return String.valueOf(sdf.parse(dateStr).getTime() / 1000);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 取得当前时间戳（精确到秒）
     *
     * @return nowTimeStamp
     */
    public static String getNowTimeStamp() {
        long time = System.currentTimeMillis();
        String nowTimeStamp = String.valueOf(time / 1000);
        return nowTimeStamp;
    }

    public static void main(String[] args) {
    	String time=date2TimeStamp("2017-08-15 10:01:11","yyyy-MM-dd HH:mm:ss");

    	System.out.println(time);


	}

}
