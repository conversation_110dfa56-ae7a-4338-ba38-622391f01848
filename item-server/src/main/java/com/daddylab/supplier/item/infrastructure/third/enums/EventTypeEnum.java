package com.daddylab.supplier.item.infrastructure.third.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 变更事件枚举
 */
@AllArgsConstructor
@Getter
public enum EventTypeEnum implements IEnum<String> {

    /**
     * 1-新增 2-编辑 3-失效 4-删除
     */
    CREATED("新增"),
    UPDATED("编辑"),
    INVALID( "失效"),
    DELETED("删除"),
    ;

    @EnumValue
    private String value;
}
