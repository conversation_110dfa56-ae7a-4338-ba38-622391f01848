package com.daddylab.supplier.item.application.visitStat;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class VisitStateManager {

    private final LoadingCache<LocalDate, Map<Long, Boolean>> visited = Caffeine.newBuilder()
            .maximumSize(1)
            .build(ld -> {
                final Cache<Long, Boolean> stateDaily = Caffeine.newBuilder()
                        .expireAfterWrite(Duration.ofMinutes(1))
                        .build();
                return stateDaily.asMap();
            });

    public void visit(Long userId, LocalDateTime visitTime,
            BiConsumer<Long, LocalDateTime> visitRecorder) {
        if (Objects.nonNull(userId)) {
            Objects.requireNonNull(visited.get(visitTime.toLocalDate()))
                    .computeIfAbsent(userId, k -> {
                        visitRecorder.accept(k, visitTime);
                        return true;
                    });
        }
    }

}
