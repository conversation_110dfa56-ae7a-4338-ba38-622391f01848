package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ITaxRateService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.TaxRate;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.TaxRateMapper;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 税率表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@Service
public class TaxRateServiceImpl extends DaddyServiceImpl<TaxRateMapper, TaxRate> implements ITaxRateService {

    @Override
    public String kingDeeNo(BigDecimal taxRate) {
        if(Objects.isNull(taxRate)){
            return "SL02_SYS";
        }
        LambdaQueryWrapper<TaxRate> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TaxRate::getValue,taxRate);
        List<TaxRate> list = this.list(wrapper);
        if(CollectionUtils.isEmpty(list)){
            return "SL02_SYS";
        }else{
            return list.get(0).getKingDeeNo();
        }
    }
}
