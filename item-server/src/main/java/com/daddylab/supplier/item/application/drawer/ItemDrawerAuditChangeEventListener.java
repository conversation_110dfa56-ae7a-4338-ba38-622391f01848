package com.daddylab.supplier.item.application.drawer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.application.drawer.impl.ItemDrawerAppServiceImpl;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.message.QyMsgSendUtils;
import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.domain.item.gateway.ItemProcurementGateway;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MsgTemplateCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.daddylab.supplier.item.types.itemDrawer.ItemLaunchAuditStatusChangeEvent;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemLaunchProcessNodeId;
import com.google.common.eventbus.Subscribe;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Slf4j
@EventBusListener(value = "itemDrawerAuditChangeEventListener")
public class ItemDrawerAuditChangeEventListener {

    private final ItemBizService itemBizService;
    private final IItemLaunchStatsService itemLaunchStatsService;
    private final IItemDrawerModuleAuditStatsService itemDrawerModuleAuditStatsService;
    private final OperateLogDomainService operateLogDomainService;
    private final IItemDrawerService itemDrawerService;
    private final IItemDrawerLiveVerbalService iItemDrawerLiveVerbalService;
    private final INewGoodsService iNewGoodsService;
    private final UserGateway userGateway;
    private final IItemService iItemService;
    private final ItemProcurementGateway itemProcurementGateway;
    private final StaffService staffService;
    private final IItemLaunchPlanService itemLaunchPlanService;


    @Subscribe
    public void listener(ItemLaunchAuditStatusChangeEvent event) {
        Long itemId = event.getItemId();

        switch (event.getType()) {

            case ITEM_MATERIAL:
                forItemMaterial(event, itemId);
                break;
            case LIVE_VERBAL_TRICK:
                forLiveVerbalTrick(event, itemId);
                break;
        }

        SpringUtil.getBean(ItemDrawerService.class).copyStatusInMerge(itemId);
    }

    private void qwLiveSpeechAuditQc(Long itemId, String itemCode, String itemName) {
        Map<String, Object> paramMap = new HashMap<>(8);

        Map<Long, String> qcIdMap = itemProcurementGateway.getQcIdList(Collections.singletonList(itemId));
        String qcIdStr = qcIdMap.getOrDefault(itemId, null);
        if (StringUtil.isBlank(qcIdStr)) {
            return;
        }
        final List<Long> qcIds = StringUtil.splitTrim(qcIdStr, ",")
                                           .stream()
                                           .map(Long::parseLong)
                                           .collect(Collectors.toList());
        if (qcIds.isEmpty()) {
            return;
        }
        final List<DadStaffVO> qcUsers = staffService.getStaffList(qcIds);

        paramMap.put("QC负责人", qcUsers.stream().map(DadStaffVO::getQwUserId).collect(Collectors.joining(",")));

        String legalNickName = "未知法务";
        List<NewGoods> newGoodsList = iNewGoodsService.lambdaQuery().eq(NewGoods::getItemId, itemId).select().list();
        if (CollUtil.isNotEmpty(newGoodsList)) {
            Long legalId = newGoodsList.get(0).getLegalId();
            StaffInfo staffInfo = userGateway.queryStaffInfoById(legalId);
            if (Objects.nonNull(staffInfo)) {
                legalNickName = staffInfo.getNickname();
            }
        }
        paramMap.put("法务负责人花名", legalNickName);

        paramMap.put("商品编码", itemCode);
        paramMap.put("商品名称", itemName);
        paramMap.put("商品ID", itemId);

        final ItemLaunchPlan plan = itemLaunchPlanService.getPlanByItemId(itemId);
        final String launchDateStr = Optional.ofNullable(plan)
                                             .map(ItemLaunchPlan::getLaunchTime)
                                             .map(DateUtil::formatDate)
                                             .orElse("?");
        paramMap.put("上新计划日期", launchDateStr);

        QyMsgSendUtils.send(RemindType.IMMEDIATELY_REMINDER, MsgTemplateCode.LIVE_VERBAL_TO_QC, paramMap);
    }

    private void qwQcPass(Long itemId, String itemCode, String itemName, Long submitUserId) {
        Map<String, Object> paramMap = new HashMap<>(8);

        paramMap.put("商品编码", itemCode);
        paramMap.put("商品名称", itemName);

        Optional<StaffBrief> staffBrief = staffService.getStaffBrief(submitUserId);
        if (!staffBrief.isPresent()) {
            log.error("直播话术「待QC审核」->「审核完成」,接受者为空");
            return;
        }
        paramMap.put("提交人", staffBrief.get().getQwUserId());
        paramMap.put("商品ID", itemId);
        final ItemLaunchPlan plan = itemLaunchPlanService.getPlanByItemId(itemId);
        final String launchDateStr = Optional.ofNullable(plan)
                                             .map(ItemLaunchPlan::getLaunchTime)
                                             .map(DateUtil::formatDate)
                                             .orElse("?");
        paramMap.put("上新计划日期", launchDateStr);

        QyMsgSendUtils.send(RemindType.IMMEDIATELY_REMINDER, MsgTemplateCode.LIVE_VERBAL_COMPLETED, paramMap);
    }

    private void qwQcReject(Long itemId, String itemCode, String itemName, Long submitUserId) {
        Map<String, Object> paramMap = new HashMap<>(8);

        paramMap.put("商品编码", itemCode);
        paramMap.put("商品名称", itemName);

        Optional<StaffBrief> staffBrief = staffService.getStaffBrief(submitUserId);
        if (!staffBrief.isPresent()) {
            log.error("直播话术「待QC审核」->「待修改」,接受者为空");
            return;
        }
        paramMap.put("提交人", staffBrief.get().getQwUserId());
        paramMap.put("商品ID", itemId);
        final ItemLaunchPlan plan = itemLaunchPlanService.getPlanByItemId(itemId);
        final String launchDateStr = Optional.ofNullable(plan)
                                             .map(ItemLaunchPlan::getLaunchTime)
                                             .map(DateUtil::formatDate)
                                             .orElse("?");
        paramMap.put("上新计划日期", launchDateStr);

        QyMsgSendUtils.send(RemindType.IMMEDIATELY_REMINDER, MsgTemplateCode.LIVE_VERBAL_TO_UPDATE, paramMap);
    }


    private void forLiveVerbalTrick(ItemLaunchAuditStatusChangeEvent event, Long itemId) {
        final Optional<Long> liveVerbalTrickIdOpt =
                Optional.ofNullable(event.getLiveVerbalTrickId()).filter(NumberUtil::isPositive);
        final Optional<ItemDrawerLiveVerbal> itemDrawerLiveVerbal = liveVerbalTrickIdOpt.map(
                iItemDrawerLiveVerbalService::getById);
        final Optional<ItemDrawer> itemDrawer = itemDrawerService.getByItemId(itemId);
        Item item = iItemService.getById(itemId);
        final boolean hasOldData = itemDrawer.filter(v -> StringUtil.isNotEmpty(v.getLiveVerbalTrick())).isPresent();
        final String liveVerbalTrickName = itemDrawerLiveVerbal.map(liveVerbal -> iItemDrawerLiveVerbalService.getName(
                hasOldData,
                liveVerbal)).orElse("话术1");
        final Long liveVerbalTrickId = liveVerbalTrickIdOpt.orElse(0L);
        // 操作日志
        switch (event.getNodeId()) {
            case LEGAL:
                operateLogDomainService.addOperatorLog(
                        event.getProcessorId(),
                        OperateLogTarget.NEW_GOODS_SPU,
                        event.getItemId(),
                        String.format("法务审核%s【直播话术（%s）】", event.isPass() ? "通过" : "拒绝", liveVerbalTrickName));
                // 2023-01-09[七喜]直播话术法务审核通过，发送消息提醒QC审核
//                EventBusUtil.post(
//                        MsgEvent.ofLiveVerbalTrickAuditCompleted(itemId, MsgEventType.LIVE_SPEECH_AUDIT_QC, liveVerbalTrickId, liveVerbalTrickName));
//                break;
                try {
                    qwLiveSpeechAuditQc(itemId, item.getCode(), itemDrawer.get().getStandardName());
                } catch (Exception e) {
                    log.error("[企微消息提醒]直播话术法务审核通过，QC待处理提醒异常 itemId={} liveVerbalTrickId={}",
                            itemId,
                            liveVerbalTrickId,
                            e);

                }
                break;
            case QC:
                operateLogDomainService.addOperatorLog(
                        event.getProcessorId(),
                        OperateLogTarget.NEW_GOODS_SPU,
                        event.getItemId(),
                        String.format("QC审核%s【直播话术（%s）】", event.isPass() ? "通过" : "拒绝", liveVerbalTrickName));

                try {
                    // QC 审核完成。「商品SPU商品名称」直播话术【审核完成】，请查看详情！
                    long submitId;
                    if (!NumberUtil.isPositive(event.getLiveVerbalTrickId())) {
                        submitId = itemDrawer.get().getCreatedUid();
                    } else {
                        submitId = itemDrawerLiveVerbal.get().getCreatedUid();
                    }
                    if (event.isPass()) {
                        qwQcPass(itemId, item.getCode(), itemDrawer.get().getStandardName(), submitId);
                    }
                    // QC审核拒绝。「商品SPU商品名称」直播话术【审核拒绝】，请查看并修改
                    else {
                        qwQcReject(itemId, item.getCode(), itemDrawer.get().getStandardName(), submitId);
                    }
                } catch (Exception e) {
                    log.error("[企微消息提醒]直播话术QC审核通过，通知审核完成异常 itemId={} liveVerbalTrickId={}",
                            itemId,
                            liveVerbalTrickId,
                            e);
                }
                break;
            // 2023-01-10[七喜]直播话术QC审核通过，发送消息提醒审核完成
//                EventBusUtil.post(
//                        MsgEvent.ofLiveVerbalTrickAuditCompleted(itemId, MsgEventType.LIVE_SPEECH, liveVerbalTrickId, liveVerbalTrickName));
//                break;
            default:
        }
        // 同步直播话术状态到抽屉表（兼容老逻辑）
        if (!NumberUtil.isPositive(event.getLiveVerbalTrickId())) {
            itemDrawerService
                    .lambdaUpdate()
                    .eq(ItemDrawer::getItemId, itemId)
                    .set(ItemDrawer::getLiveVerbalTrickStatus, event.getToStatus())
                    .update();
        } else {
            // 直播话术新表更新状态
            iItemDrawerLiveVerbalService
                    .lambdaUpdate()
                    .eq(ItemDrawerLiveVerbal::getItemId, itemId)
                    .eq(ItemDrawerLiveVerbal::getId, event.getLiveVerbalTrickId())
                    .set(ItemDrawerLiveVerbal::getLiveVerbalTrickStatus, event.getToStatus())
                    .update();
        }
    }

    private void forItemMaterial(ItemLaunchAuditStatusChangeEvent event, Long itemId) {
        //审核日志
        auditLog(event);

        //流程是否完结
        final boolean processIsEnd = event.getToStatus() == ItemAuditStatus.FINISHED;

        //统计商品上新流程审核信息
        try {
            statItemLaunchAudit(event);
        } catch (Exception e) {
            log.error("[统计商品上新流程审核信息统计]异常 itemId={}", itemId, e);
            Alert.text(MessageRobotCode.NOTICE,
                    String.format("统计商品上新流程审核信息统计异常 itemId=%s event=%s", itemId,
                            JsonUtil.toJson(event)));
        }

        // 审核通过【现在审核通过后的下一个流程为待修改】
        if (processIsEnd) {
            itemBizService.updateLaunchStatus(itemId, ItemLaunchStatus.TO_BE_UPDATED);

            //2022-11-30[七喜]抽屉信息审核完成后，发出直播话术审核完成企微通知
//            if (event.getAuditModuleIds().contains(ItemDrawerModuleId.LIVE_VERBAL_TRICK)) {
//                EventBusUtil.post(
//                        MsgEvent.of(Collections.singletonList(itemId), MsgEventType.LIVE_SPEECH),
//                        true);
//            }
        } else if (event.getNodeId() == ItemLaunchProcessNodeId.LEGAL) {
            //审核流程未完结、法务审核通过，变更为待QC审核
            if (itemBizService.updateAuditStatus(itemId, ItemAuditStatus.WAIT_QC_AUDIT)) {

                //再触发一次待审核消息推送
//                EventBusUtil.post(MsgEvent.of(Collections.singletonList(itemId),
//                                MsgEventType.DESIGN_TO_AUDIT_QC),
//                        true);
                itemMaterialsToQC(itemId);
            }
        }
    }

    private void itemMaterialsToQC(Long itemId) {
        final Optional<Item> item = Optional.ofNullable(iItemService.getById(itemId));
        Map<String, Object> paramMap = new HashMap<>(8);

        Map<Long, String> qcIdMap = itemProcurementGateway.getQcIdList(Collections.singletonList(itemId));
        String qcIdStr = qcIdMap.getOrDefault(itemId, null);
        if (StringUtil.isBlank(qcIdStr)) {
            return;
        }
        final List<Long> qcIds = StringUtil.splitTrim(qcIdStr, ",")
                                             .stream()
                                             .map(Long::parseLong)
                                             .collect(Collectors.toList());
        if (qcIds.isEmpty()) {
            return;
        }
        final List<DadStaffVO> qcUsers = staffService.getStaffList(qcIds);

        paramMap.put("QC负责人", qcUsers.stream().map(DadStaffVO::getQwUserId).collect(Collectors.joining(",")));

        String legalNickName = "未知法务";
        List<NewGoods> newGoodsList = iNewGoodsService.lambdaQuery().eq(NewGoods::getItemId, itemId).select().list();
        if (CollUtil.isNotEmpty(newGoodsList)) {
            Long legalId = newGoodsList.get(0).getLegalId();
            StaffInfo staffInfo = userGateway.queryStaffInfoById(legalId);
            if (Objects.nonNull(staffInfo)) {
                legalNickName = staffInfo.getNickname();
            }
        }
        paramMap.put("法务负责人花名", legalNickName);

        final String itemCode = item.map(Item::getCode).orElse("?");
        final String itemName = item.map(Item::getName).orElse("?");
        paramMap.put("商品编码", itemCode);
        paramMap.put("商品名称", itemName);
        paramMap.put("itemId", itemId);

        final ItemLaunchPlan plan = itemLaunchPlanService.getPlanByItemId(itemId);
        final String launchDateStr = Optional.ofNullable(plan)
                                             .map(ItemLaunchPlan::getLaunchTime)
                                             .map(DateUtil::formatDate)
                                             .orElse("?");
        paramMap.put("上新计划日期", launchDateStr);

        QyMsgSendUtils.send(RemindType.IMMEDIATELY_REMINDER, MsgTemplateCode.ITEM_LAUNCH_TO_QC, paramMap);
    }

    private void auditLog(ItemLaunchAuditStatusChangeEvent event) {
        switch (event.getNodeId()) {
            case LEGAL:
                operateLogDomainService.addOperatorLog(event.getProcessorId(),
                        OperateLogTarget.NEW_GOODS_SPU,
                        event.getItemId(), "法务审核通过【商品资料】");
                break;
            case QC:
                operateLogDomainService.addOperatorLog(event.getProcessorId(),
                        OperateLogTarget.NEW_GOODS_SPU,
                        event.getItemId(), "QC审核通过【商品资料】");
                break;
        }
    }

    private void statItemLaunchAudit(ItemLaunchAuditStatusChangeEvent event) {
        final Long itemId = event.getItemId();
        final List<ItemDrawerModuleAuditStats> moduleAuditStatsList = itemDrawerModuleAuditStatsService.lambdaQuery()
                .eq(ItemDrawerModuleAuditStats::getItemId, event.getItemId())
                .eq(ItemDrawerModuleAuditStats::getType, event.getType())
                .eq(ItemDrawerModuleAuditStats::getRound, event.getRound()).list();
        if (moduleAuditStatsList.isEmpty()) {
            final ItemDrawerModuleAuditStats stats = new ItemDrawerModuleAuditStats();
            stats.setItemId(itemId);
            stats.setRound(event.getRound());
            stats.setType(event.getType());
            moduleAuditStatsList.add(stats);
        }
        for (ItemDrawerModuleAuditStats moduleAuditStats : moduleAuditStatsList) {
            if (event.getNodeId() == ItemLaunchProcessNodeId.LEGAL) {
                itemLaunchStatsService.auditStat(itemId,
                        event.getProcessorId(),
                        event.getAuditStartAt(),
                        event.getAuditAt(),
                        null, null, null
                );

                moduleAuditStats.setLegalAuditUid(event.getProcessorId());
                moduleAuditStats.setLegalAuditStartTime(event.getAuditStartAt());
                moduleAuditStats.setLegalAuditEndTime(event.getAuditAt());
                moduleAuditStats.setLegalAuditCostTime(
                        NumberUtil.sub(event.getAuditAt(), event.getAuditStartAt()).longValue());
                itemDrawerModuleAuditStatsService.saveOrUpdate(moduleAuditStats);
            }
            if (event.getNodeId() == ItemLaunchProcessNodeId.QC) {
                itemLaunchStatsService.auditStat(itemId,
                        null, null, null,
                        event.getProcessorId(),
                        event.getAuditStartAt(),
                        event.getAuditAt()
                );

                moduleAuditStats.setQcAuditUid(event.getProcessorId());
                moduleAuditStats.setQcAuditStartTime(event.getAuditStartAt());
                moduleAuditStats.setQcAuditEndTime(event.getAuditAt());
                moduleAuditStats.setQcAuditCostTime(
                        NumberUtil.sub(event.getAuditAt(), event.getAuditStartAt()).longValue());
                itemDrawerModuleAuditStatsService.saveOrUpdate(moduleAuditStats);
            }
            if (event.getNodeId() == ItemLaunchProcessNodeId.MODIFY) {
                moduleAuditStats.setModifyUid(event.getProcessorId());
                moduleAuditStats.setModifyStartTime(event.getAuditStartAt());
                moduleAuditStats.setModifyEndTime(event.getAuditAt());
                moduleAuditStats.setModifyCostTime(
                        NumberUtil.sub(event.getAuditAt(), event.getAuditStartAt()).longValue());
                itemDrawerModuleAuditStatsService.saveOrUpdate(moduleAuditStats);
            }
        }
    }

}
