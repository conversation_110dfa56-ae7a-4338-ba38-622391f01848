package com.daddylab.supplier.item.infrastructure.kingdee.util;

import com.daddylab.supplier.item.infrastructure.kingdee.dto.*;
import com.daddylab.supplier.item.infrastructure.kingdee.req.SkuPriceCategoryReq;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> up
 * @date 2022/3/21 11:49 上午
 */
public interface ReqTemplate {

    Boolean unAudit(String reqJson) throws Exception;

    Boolean submit(String reqJson) throws Exception;

    Boolean aduit(String reqJson) throws Exception;

    Boolean delete(String reqJson) throws Exception;

    String groupSave(String reqJson) throws Exception;

    Boolean groupDelete(String reqJson) throws Exception;

    String save(String reqJson) throws Exception;

    String save2(String reqJson) throws Exception;

    KingDeeResp saveWithRes(String reqJson) throws Exception;

    String groupInfo(String reqJson) throws Exception;

    // ------------------------- 以下是业务相关处理 ----------------------------

    Optional<KingDeeProviderResp> queryProvider(String providerName);

    List<KingDeeSkuResp> querySkuList(Long startRow, Long limit) throws Exception;

    List<KingDeeSkuResp> queryZeroStockCostPriceSkuList(Long startRow, Long limit) throws Exception;

    List<KingDeeSkuResp> querySkuProviderList(Long startRow, Long limit) throws Exception;

    String querySkuUnit(String skuCode) throws Exception;

    Optional<KingDeeSkuResp> querySkuByNo(String skuCodeNo);

    Boolean warehouseExist(String warehouseNo);

    Boolean saveWarehouse(String name, String no) throws Exception;

    Boolean updateSkuProvider(String skuKingDeeId, String providerNo) throws Exception;

    /**
     * @param dateStr yyyy/MM/dd
     * @return
     * @throws Exception
     */
    List<KingDeeStockInOrderResp> queryStockInOrderList(String dateStr) throws Exception;

    Optional<KingDeeStockInOrderResp> queryStockInOrderByNo(String orderNo);

    Optional<KingDeeStockOutOrderResp> queryStockOutOrderByNo(String orderNo);

    SkuPriceCategoryReq querySkuPriceCategory(String skuKingDeeId);

    Boolean deleteSkuPriceCategory(String billNo) throws Exception;

    Boolean unAuditSkuPriceCategory(String billNo) throws Exception;

    Map<String, String> queryProviderBankInfo(String providerNo) throws Exception;

    SubmitAndAuditRes submitAndAuditStockOrder(String formId, List<String> nos);
}
