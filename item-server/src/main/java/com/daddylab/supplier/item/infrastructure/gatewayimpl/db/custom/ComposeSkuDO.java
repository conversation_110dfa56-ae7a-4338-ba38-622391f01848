package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 1.组合商品构成 sku字段明细
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/7 8:42 下午
 * @description
 */
@Data
public class ComposeSkuDO {

    private Long skuId;

    protected String skuCode;

    protected String specialSkuCode;

    protected String barCode;

    protected Long itemId;

    protected String itemCode;

    protected String itemName;

    protected String brandName;

    protected String category;

    protected Integer stockCount;

    private BigDecimal procurement;

    private BigDecimal sales;

    private String itemImage;

    private String warehouseNo;

    private String warehouseName;

    private Integer isGift;

    private BigDecimal taxRate;

    private String unit;

    /**
     * 平台佣金
     */
    private BigDecimal platformCommission;

    /**
     * 合同销售价
     */
    private BigDecimal contractSalePrice;

    private Integer status;



}
