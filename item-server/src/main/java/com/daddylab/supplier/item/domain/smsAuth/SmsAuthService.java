package com.daddylab.supplier.item.domain.smsAuth;

import cn.hutool.core.util.RandomUtil;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.submail.SMSTemplate;
import com.daddylab.supplier.item.infrastructure.submail.SubMailException;
import com.daddylab.supplier.item.infrastructure.submail.SubMailService;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.google.common.collect.ImmutableMap;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class SmsAuthService {
    private static final int CAPTCHA_TIMEOUT_MINUTES = 15;

    private static final String CACHE_KEY_PREFIX = "SMS_AUTH_";

    @Autowired
    private SubMailService subMailService;

    public String getSmsCode(String mobile) {
        return getSmsCode(mobile, "");
    }

    public String getSmsCode(String mobile, String scope) {
        final String code = RandomUtil.randomNumbers(4);
        final int captchaTimeoutMinutes = CAPTCHA_TIMEOUT_MINUTES;
        final int timeoutSecond = captchaTimeoutMinutes * 60;

        if (!ApplicationContextUtil.isActiveProfile("prod", "gray")) {
            Alert.text(MessageRobotCode.NOTICE,
                    String.format("发送验证码 %s -> %s from %s",
                            code,
                            mobile,
                            ApplicationContextUtil.getActiveProfile()));
        } else {
            try {
                subMailService.send(mobile, SMSTemplate.COMMON_CODE, ImmutableMap.of("code", code, "time", captchaTimeoutMinutes + "分钟"));
            } catch (SubMailException e) {
                throw ExceptionPlusFactory.bizException(ErrorCode.SMS_AUTH_ERROR, "验证码发送失败");
            }
        }

        final String ck = key(mobile, scope);
        RedisUtil.set(ck, code, timeoutSecond, TimeUnit.SECONDS);
        return code;
    }

    @NonNull
    private static String key(String mobile, String scope) {
        return Stream.of(CACHE_KEY_PREFIX + mobile, scope)
                     .filter(StringUtil::isNotBlank)
                     .collect(Collectors.joining("_"));
    }

    public boolean verifyCode(String mobile, String smsCode) {
        return verifyCode(mobile, smsCode, "");
    }

    public boolean verifyCode(String mobile, String smsCode, String scope) {
        if (StringUtil.isBlank(mobile) || StringUtil.isBlank(smsCode)) {
            return false;
        }
        final String ck = key(mobile, scope);
        final String smsCodeRight = RedisUtil.get(ck);
        if (StringUtil.isBlank(smsCodeRight)) {
            return false;
        }
        if (StringUtil.equals(smsCodeRight, smsCode)) {
            RedisUtil.del(ck);
            return true;
        }
        return false;
    }
}
