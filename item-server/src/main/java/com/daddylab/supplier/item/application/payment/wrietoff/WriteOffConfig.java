package com.daddylab.supplier.item.application.payment.wrietoff;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> up
 * @date 2024年03月07日 2:46 PM
 */
@EnableConfigurationProperties(WriteOffConfig.class)
@ConfigurationProperties(prefix = "write-off")
@Component
@RefreshScope
@Data
public class WriteOffConfig {

    private String processInitiation;

    public Boolean isInitiation() {
        return StrUtil.isNotBlank(this.processInitiation) && "1".equals(this.processInitiation);
    }
}
