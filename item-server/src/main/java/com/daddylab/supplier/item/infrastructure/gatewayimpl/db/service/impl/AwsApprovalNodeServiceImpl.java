package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AwsApprovalNode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AwsApprovalNodeMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAwsApprovalNodeService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 炎黄盈动节点审批数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
@Service
public class AwsApprovalNodeServiceImpl extends DaddyServiceImpl<AwsApprovalNodeMapper, AwsApprovalNode> implements IAwsApprovalNodeService {

}
