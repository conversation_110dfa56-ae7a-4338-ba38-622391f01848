package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsVo;
import com.daddylab.supplier.item.controller.item.dto.*;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseItem;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.config.ItemOutlinkProperties;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemBaseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

//import com.daddylab.supplier.item.domain.exportTask.dto.item.ExportItemOtherDto;

/**
 * <p>
 * 商品 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class ItemServiceImpl extends DaddyServiceImpl<ItemMapper, Item> implements IItemService {

    @Autowired
    ItemMapper itemMapper;

    @Autowired
    @Qualifier("UserGatewayCacheImpl")
    private UserGateway userGateway;

    @Autowired
    private ItemOutlinkProperties itemOutlinkProperties;

    @Autowired
    private IPlatformItemService platformItemService;

    @Override
    public List<NameDropDownVo> dropDownList(String name, Integer offset, Integer size, Boolean useForItemLaunchPlan, Long providerId) {
        return itemMapper.dropDownList(name, offset, size, useForItemLaunchPlan, providerId);
    }

    @Override
    public List<ItemPageVo> queryPage(ItemPageQuery itemPageQuery) {
        return itemMapper.queryPage(itemPageQuery);
    }

    @Override
    public Integer queryPageCount(ItemPageQuery itemPageQuery) {
        return itemMapper.queryPageCount(itemPageQuery);
    }

    @Override
    public ItemBaseDO queryDetailBase(Long itemId) {
        return itemMapper.queryDetailBase(itemId);
    }

    @Override
    public ItemBaseDO queryDetailBaseWithImage(Long itemId) {
        return itemMapper.queryDetailBaseWithImage(itemId);
    }

    @Override
    public List<ItemSimpleViewVo> queryItemSimple(ItemSimpleViewCmd cmd) {
        return itemMapper.queryItemSimple(cmd);
    }

    @Override
    public Integer countExportSku(ExportCmd cmd) {
        return itemMapper.countExportSku(cmd);
    }

    @Override
    public PurchaseItem getPurchaseBySku(String itemSku) {
        return itemMapper.getPurchaseBySku(itemSku);
    }

    @Override
    public NewGoodsVo getNewGoodsById(Long id) {
        return itemMapper.getNewGoodsById(id);
    }

    @Override
    public List<Item> selectBatchByIds(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return itemMapper.selectBatchIds(ids);
    }

    @Override
    public Map<Long, ItemBuyerDto> getItemsBuyerMap(List<Long> itemIds) {
        if (CollectionUtil.isEmpty(itemIds)) {
            return new HashMap<>();
        }

        // 批量查询商品采购人
        List<ItemBuyerDto> itemBuyerDtos = itemMapper.selectBatchBuyersByItemIds(itemIds);

        Map<Long, ItemBuyerDto> map = new HashMap<>((int) (itemBuyerDtos.size() / 0.75 + 1));
        for (ItemBuyerDto dto : itemBuyerDtos) {
            map.put(dto.getItemId(), dto);
        }
        return map;
    }

    @Override
    public List<ItemBuyerDto> selectBatchBuyersByItemIds(List<Long> itemIds) {
        if (CollectionUtil.isEmpty(itemIds)) {
            return new ArrayList<>();
        }
        return itemMapper.selectBatchBuyersByItemIds(itemIds);
    }

    @Override
    public Map<Long, ItemPrincipalDto> getItemsPrincipalMap(List<Long> itemIds) {
        if (CollectionUtil.isEmpty(itemIds)) {
            return new HashMap<>();
        }

        List<ItemPrincipalDto> dtos = itemMapper.selectBatchPrincipalUsersByItemIds(itemIds);
        List<Long> userIds = dtos.stream()
                .map(ItemPrincipalDto::getPrincipalUserId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, StaffInfo> staffInfoMap = userGateway.batchQueryStaffInfoByIds(userIds);

        Map<Long, ItemPrincipalDto> map = new HashMap<>((int) (dtos.size() / 0.75 + 1));
        for (ItemPrincipalDto dto : dtos) {
            StaffInfo staffInfo = staffInfoMap.get(dto.getPrincipalUserId());
            dto.setPrincipalUserName(Optional.ofNullable(staffInfo).map(StaffInfo::getUserName).orElse(""));
            dto.setPrincipalUserNickName(Optional.ofNullable(staffInfo).map(StaffInfo::getNickname).orElse(""));
            map.put(dto.getItemId(), dto);
        }
        return map;
    }

    @Override
    public Map<Long, ItemWithLaunchPlanDto> getItemDtoMap(List<Long> itemIds) {
        if (CollectionUtil.isEmpty(itemIds)) {
            return new HashMap<>();
        }

        // 批量查询
        List<ItemWithLaunchPlanDto> dtos = itemMapper.selectBatchDtosByItemIds(itemIds);

        Map<Long, ItemWithLaunchPlanDto> map = new HashMap<>((int) (dtos.size() / 0.75 + 1));
        for (ItemWithLaunchPlanDto dto : dtos) {
            map.put(dto.getItemId(), dto);
        }
        return map;
    }

    @Override
    public Map<String, Item> getNoToItemMap(List<String> itemNos) {
        if (CollectionUtil.isEmpty(itemNos)) {
            return new HashMap<>(1);
        }

        QueryWrapper<Item> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(Item::getCode, itemNos);
        List<Item> items = itemMapper.selectList(queryWrapper);

        Map<String, Item> map = new HashMap<>((int) (items.size() / 0.75 + 1));
        for (Item item : items) {
            map.put(item.getCode(), item);
        }
        return map;
    }

    @Override
    public boolean reAudit(Long itemId) {
        return lambdaUpdate().eq(Item::getId, itemId)
                .set(Item::getLaunchStatus, ItemLaunchStatus.TO_BE_AUDITED)
                .set(Item::getAuditStatus, ItemAuditStatus.WAIT_LEGAL_AUDIT).update();
    }

    @Override
    public boolean updateLaunchStatusById(Long itemId, ItemLaunchStatus launchStatus) {
        Item updateItem = new Item();
        updateItem.setLaunchStatus(launchStatus.getValue());

        //变更商品上新状态大于待审核，同时将审核状态也变更为已完成
        if (launchStatus.getValue() > ItemLaunchStatus.TO_BE_AUDITED.getValue()) {
            updateItem.setAuditStatus(ItemAuditStatus.FINISHED.getValue());
        }
        //商品状态变更为待审核，将审核状态修改为待法务审核
        else if (Objects.equals(launchStatus.getValue(), ItemLaunchStatus.TO_BE_AUDITED.getValue())) {
            updateItem.setAuditStatus(ItemAuditStatus.WAIT_LEGAL_AUDIT.getValue());
        }

        // 更新时，launchStatus 只能前进不能后退（除了【待修改】变为【待上架】的场景，因为历史原因，待上架的值比待修改要小）
        // Ver1.6.3(2022-08-08):商品上新流程变更，现在【待修改】之后直接就是【待上架】
        QueryWrapper<Item> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(Item::getId, itemId)
                .and(q -> q.lt(Item::getLaunchStatus, launchStatus.getValue()).or()
                        .eq(launchStatus == ItemLaunchStatus.TO_BE_RELEASED, Item::getLaunchStatus,
                                ItemLaunchStatus.TO_BE_UPDATED));
        return itemMapper.update(updateItem, queryWrapper) > 0;
    }

    @Override
    public ItemLaunchStatus getItemLaunchStatus(Long itemId) {
        final LambdaQueryWrapper<Item> query = Wrappers.lambdaQuery();
        query.eq(Item::getId, itemId);
        query.select(Item::getLaunchStatus);
        final Item item = itemMapper.selectOne(query);
        return item != null ? IEnum.getEnumByValue(ItemLaunchStatus.class, item.getLaunchStatus()) : null;
    }

    @Override
    public String getPartnerProviderItemSn(Long itemId) {
        return lambdaQuery().eq(Item::getId, itemId)
                .select(Item::getPartnerProviderItemSn).oneOpt()
                .map(Item::getPartnerProviderItemSn).orElse(null);
    }

    @Override
    public ItemSkuDetailVo getItemSkuDetail(Long itemId) {
        ItemSkuDetailVo vo = itemMapper.selectItemBaseInfo(itemId);
        ItemOutlinkDto itemOutlink = getItemOutlink(itemId);
        vo.setDouyinLink(itemOutlink.getDouyinLink());
        vo.setTaobaoLink(itemOutlink.getTabaoLink());
        vo.setMiniProgramLink(itemOutlink.getMiniProgramLink());

        // 商品的规格详情
        List<ItemSkuDetailVo.SkuDetail> skuDetails = itemMapper.selectSkuDetails(itemId);
        vo.setSkuDetails(skuDetails);
        return vo;
    }

    @Override
    public ItemOutlinkDto getItemOutlink(Long itemId) {
        List<PlatformItem> platformItems = platformItemService.listByItemId(itemId);
        ItemOutlinkDto itemOutlinkDto = new ItemOutlinkDto();
        for (PlatformItem platformItem : platformItems) {
            String outerItemId = platformItem.getOuterItemId();
            if (Objects.equals(platformItem.getPlatform(), Platform.DOUDIAN)) {
                itemOutlinkDto.setDouyinLink(String.format(itemOutlinkProperties.getBaseDouyinLink(), outerItemId));
                continue;
            }
            if (Objects.equals(platformItem.getPlatform(), Platform.TAOBAO)) {
                itemOutlinkDto.setTabaoLink(String.format(itemOutlinkProperties.getBaseTaobaoLink(), outerItemId));
                continue;
            }
            if (Objects.equals(platformItem.getPlatform(), Platform.LAOBASHOP)) {
                itemOutlinkDto.setMiniProgramLink(String.format(itemOutlinkProperties.getBaseMiniProgramLink(), outerItemId));
            }
        }
        return itemOutlinkDto;
    }

    @Override
    public Item getByCode(String code) {
        if (!StringUtils.hasText(code)) {
            return null;
        }
        QueryWrapper<Item> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Item::getCode, code);
        return itemMapper.selectOne(queryWrapper);
    }

    @Override
    public Item getByMixedCode(String code) {
        if (!StringUtils.hasText(code)) {
            return null;
        }
        QueryWrapper<Item> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .lambda()
                .eq(Item::getProviderSpecifiedCode, code)
                .or()
                .eq(Item::getCode, code)
                .last("limit 1");
        return itemMapper.selectOne(queryWrapper);
    }

    @Override
    public List<Item> getBatchByMixedCode(List<String> codes) {
        if (CollUtil.isEmpty(codes)) {
            return Collections.emptyList();
        }
        return lambdaQuery()
                .in(Item::getProviderSpecifiedCode, codes)
                .or()
                .in(Item::getCode, codes).list();
    }

    @Override
    public String getItemCategoryPath(String itemNo) {
        return itemMapper.getItemCategoryPath(itemNo);
    }

    @Override
    public List<AttrDto> selectItemAttrDtosByItemId(Long id) {
        if (id == null || id <= 0) {
            return new ArrayList<>();
        }
        return itemMapper.selectItemAttrDtosByItemId(id);
    }

    @Override
    public boolean updateProvider(long itemId, long providerId) {
        final Item updateModel = new Item();
        updateModel.setId(itemId);
        updateModel.setProviderId(providerId);
        return updateById(updateModel);
    }

    //    @Override
//    public ExportItemOtherDto queryOtherItemExportDto(Long itemId) {
//        return itemMapper.queryOtherItemExportDto(itemId);
//    }
}
