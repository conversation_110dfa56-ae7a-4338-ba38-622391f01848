package com.daddylab.supplier.item.application.purchasePayable.applyPayAudit.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayApplyAuditStatus;
import lombok.Builder;
import lombok.Getter;

/**
 * 审核节点对象
 *
 * <AUTHOR> up
 * @date 2023年02月16日 10:06 AM
 */
@Builder
@Getter
public class AuditNodeBo {

    /**
     * 节点角色。
     */
    private NodeRoleType nodeRoleType;

    /**
     * 审批意见
     * 默认为空
     */
    private String opinion;


    private PayApplyAuditStatus auditStatus;

    private Integer autoAudit;

}
