package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Date: 2022/5/31 09:45
 * @Description: 请描述下这个类
 */
@Data
@ApiModel("商品上新计划分页查询返回对象")
public class ItemLaunchPlanPageVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "计划ID")
    private Long id;

    @ApiModelProperty(value = "编号")
    private String no;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "上新时间（时间戳，单位秒）")
    private Long launchTime;

    @ApiModelProperty(value = "商品数量")
    private Integer itemNum;

    @ApiModelProperty(value = "已上架商品数量")
    private Long itemOnNum;

    @ApiModelProperty(value = "创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "创建人花名")
    private String creator;

    @ApiModelProperty(value = "创建人真名")
    private String creatorRealName;

    @ApiModelProperty("是否提交")
    private Integer isSubmit;

    @ApiModelProperty("合作模式")
    private Integer businessLine;
}
