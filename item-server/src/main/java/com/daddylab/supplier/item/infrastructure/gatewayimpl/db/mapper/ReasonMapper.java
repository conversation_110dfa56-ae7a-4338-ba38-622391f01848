package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Reason;
import com.daddylab.supplier.item.types.reason.enums.ReasonType;
import java.util.Optional;

/**
 * <p>
 * 原因管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
public interface ReasonMapper extends DaddyBaseMapper<Reason> {

    default Long id(ReasonType reasonType, String reason) {
        final LambdaQueryWrapper<Reason> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Reason::getType, reasonType.getValue())
                .eq(Reason::getReason, reason).select(Reason::getId);
        return Optional.ofNullable(selectOne(wrapper)).map(Reason::getId).orElse(null);
    }

    default long addReason(ReasonType reasonType, String reason) {
        final Reason reasonPo = new Reason();
        reasonPo.setType(reasonType.getValue());
        reasonPo.setReason(reason);
        insert(reasonPo);
        return reasonPo.getId();
    }
}
