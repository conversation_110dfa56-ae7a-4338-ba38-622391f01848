package com.daddylab.supplier.item.application.aws.service;

import com.actionsoft.bpms.api.ApiException;
import com.actionsoft.sdk.service.model.HistoryTaskInstance;
import com.actionsoft.sdk.service.model.TaskInstance;
import com.actionsoft.sdk.service.model.TaskQueryModel;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import org.apache.commons.collections4.Predicate;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

public interface AwsApprovalFlowService {

    /**
     * 创建并启动流程实例，提交者 默认提交
     * @param businessId  业务ID
     * @param type  业务类型 {@link PurchaseTypeEnum}
     * @param title 流程标题
     * @param list 绑定的表名
     * @return String  流程实例Id
     */
    String createAndStart(@NotNull Long businessId, @NotNull PurchaseTypeEnum type, @NotNull String title,
                          @NotNull List<String> list) throws ApiException;

    /**
     * 创建、绑定流程数据，并启动流程实例
     * @param businessId 业务ID
     * @param type 业务类型 {@link PurchaseTypeEnum}
     * @param title 流程标题
     * @param data 绑定数据 (绑定BO名称, (K, V))
     * @return processInstId
     * @throws ApiException
     */
    String createAndStartWithData(Long businessId, PurchaseTypeEnum type, String title,
            Map<String, Map<String, Object>> data) throws ApiException;

    /**
     * 审批流url
     * @param businessId  业务ID
     * @param type  业务类型 {@link PurchaseTypeEnum}
     * @return url
     */
    String getUrl(@NotNull Long businessId, @NotNull PurchaseTypeEnum type);

    /**
     * 获取流程审批链接
     *
     * @param businessId  业务单据ID
     * @param type        业务单据类型
     * @param taskChecker 任务选择器（某些流程同一环节设置了并签，存在多个任务同时存在，需要根据用户身份返回不同的任务）
     * @param isFinished  流程是否已经结束（默认情况下直接根据流程是否已经走到最后一个节点来判断，但是可能存在页面刷新时，AWS
     *                    事件通知还未处理完的情况，所以可能需要使用外部的业务状态来判断流程是否结束）
     * @return 流程审批链接
     */
    String getUrl(Long businessId, PurchaseTypeEnum type,
            Predicate<TaskInstance> taskChecker, Boolean isFinished);

    /**
     * 审批被拒绝 或者流程正常结束，被调用，通业务方
     * @param processInstId 流程实例ID
     */
    void notice(String processInstId);


    /**
     * 流程重置（尚未结束的流程跳转到到第一个节点）
     * @param processInstId 流程实例Id
     */
    void processRestart(@NotNull String processInstId) throws ApiException;

    /**
     * 判断审批节点是否有变化
     * @param businessId  业务ID
     * @param typeEnum    业务类型
     * @param taskInsId   节点ID
     * @return true 有变化 false 无变化
     */
    Boolean checkTask(Long businessId, PurchaseTypeEnum typeEnum, String taskInsId);


    /**
     * 反审核（已经结束的流程 跳转到到第二个节点）
     * @param type {@link PurchaseTypeEnum}
     * @param processInstId 流程实例Id
     */
    void antiAudit(@NotNull PurchaseTypeEnum type, @NotNull String processInstId) throws ApiException;


    /**
     *  提交者 提交
     * @param processInstId 流程实例ID
     */
    void submit(@NotNull String processInstId) throws ApiException;


    /**
     * 是否隐藏按钮
     * @param businessId 业务ID
     * @param typeEnum  类型
     * @return true 隐藏 false 不隐藏
     */
    Boolean hideButton(Long businessId, PurchaseTypeEnum typeEnum);


    /**
     * 终止一个流程
     * @param processInstId 流程实例ID
     * @return true 成功 false 失败
     */
   Boolean processTerminate(@NotNull String processInstId) throws ApiException;

    /**
     * 任务移交给指定用户
     *
     * @param taskId 任务ID
     * @param targetUid 目标用户ID
     * @return 是否移交成功
     * @throws ApiException 异常
     */
    public boolean taskDelegate(String taskId, String uid, String targetUid, String reason) throws ApiException;

    /**
     * 当前活跃任务查询
     * @param processInstId 流程实例ID
     * @return 任务实例列表
     * @throws ApiException
     */
    List<TaskInstance> taskQuery(String processInstId) throws ApiException;

    /**
     * 当前活跃任务查询
     * @param queryModel 任务查询模型
     * @return 任务实例列表
     * @throws ApiException
     */
    List<TaskInstance> taskQuery(TaskQueryModel queryModel) throws ApiException;

    /**
     * 历史任务查询（已完成的节点）
     */
    List<HistoryTaskInstance> historyTaskQuery(String processInstId) throws ApiException;

    void boFieldUpdate(String boName, String processInstId, String fieldName, Object value);

    /**
     * 检查流程是否处理挂起状态
     *
     * @param processInstId 流程实例ID
     */
    boolean processSuspendCheck(@NotNull String processInstId) throws ApiException;

    /**
     * 挂起流程
     * @param processInstId 流程实例ID
     */
    void processSuspend(@NotNull String processInstId) throws ApiException;

    /**
     * 流程从挂起中恢复
     * @param processInstId 流程实例ID
     */
    void processResume(@NotNull String processInstId) throws ApiException;

    /**
     * 获取待办列表
     * @param type 业务类型
     * @param loginName 登录名
     * @return 业务ID列表
     */
    List<Long> getTodoList(Integer type, String loginName);


}
