package com.daddylab.supplier.item.domain.category.service;

import com.daddylab.supplier.item.application.criticalPush.ManagedPush;
import com.daddylab.supplier.item.application.criticalPush.SourceType;
import com.daddylab.supplier.item.application.criticalPush.TargetType;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.controller.category.dto.CategoryCmd;
import com.daddylab.supplier.item.domain.category.entity.CategoryEntity;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Category;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CategoryMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICategoryService;
import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/26 6:32 下午
 * @description
 */
@Service
@Slf4j
public class CategoryDomainService {

    @Autowired
    CategoryGateway categoryGateway;

//    @Autowired
//    KingDeeGateway kingDeeGateway;

    @Autowired
    ICategoryService iCategoryService;

    @Autowired
    KingDeeTemplate kingDeeTemplate;

    /**
     * 添加品类
     *
     * <AUTHOR>
     * @date 2021/9/27 10:20 上午
     * @description 添加品类
     * @retrun void
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addCategory(CategoryCmd categoryCmd) {
        CategoryEntity entity = CategoryEntity.buildEntity(categoryCmd, categoryGateway);
        Category category = entity.getCategory();
        iCategoryService.save(category);
        (((CategoryDomainService) AopContext.currentProxy())).updateToKingDee(category);
        iCategoryService.updateById(category);
        return category.getId();
    }

    @ManagedPush(sourceType = SourceType.CATEOGRY, targetType = TargetType.KINGDEE, idArgProp = "id")
    protected void updateToKingDee(Category category) {
//        KingDeeRemoteTemplate remoteTemplate = SpringUtil.getBean(KingDeeRemoteTemplate.class);
//        KingDeeResponse response;
//        if (StringUtils.isBlank(category.getKingDeeId())) {
//            response = remoteTemplate.addCategory(category);
//        } else {
//            response = remoteTemplate.updateCategory(category);
//        }
//        if (response.getSuccess()) {
//            category.setKingDeeId(response.getId());
//        } else {
//            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, response.getMsg());
//        }
        kingDeeTemplate.handler(ApiEnum.GROUP_SAVE_CATEGORY, category.getId(), category.getKingDeeId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCategory(Long id, String name) {
        final Category category = iCategoryService.getById(id);
        Assert.notNull(category, "id非法，品类id查询为空");
        Assert.isTrue(StringUtils.isNotBlank(name) && name.length() < 10, "名字应小于10字符");

        final String beforePath = category.getPath();
        category.setName(name);
        if (category.getParentId() != null && category.getParentId() > 0) {
            final Category parentCategory = iCategoryService.getById(category.getParentId());
            Assert.notNull(parentCategory, "数据完整性异常，当前类目的上级类目已被删除");
            category.setPath(String.format("%s/%s", parentCategory.getPath(), name));
        } else {
            category.setPath(name);
        }

        try {
            iCategoryService.updateById(category);
        } catch (DuplicateKeyException e) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DUPLICATE_DATA, "类目名称重复");
        }

        final String afterPath = category.getPath();
        final CategoryMapper categoryMapper = iCategoryService.getDaddyBaseMapper();
        categoryMapper.replacePath(StringUtil.ensureSuffix(beforePath, "/"), StringUtil.ensureSuffix(afterPath, "/"));

        if (StringUtils.isNotBlank(category.getKingDeeId())) {
            (((CategoryDomainService) AopContext.currentProxy())).updateToKingDee(category);
        }
    }

    public void deleteCategory(Long id) {
        Assert.notNull(id, "品类id入参不得为空");
//        如果存在和此品类关联的商品，不允许删除
        if (categoryGateway.canDelete(id)) {

            final Category category = iCategoryService.getById(id);
            if (StringUtils.isNotBlank(category.getKingDeeId())) {
                kingDeeTemplate.handler(ApiEnum.GROUP_DELETE_CATEGORY, category.getId(), category.getKingDeeId());
//                kingDeeGateway.deleteCategory(category);
            }

            categoryGateway.removeById(id);
            return;
        }
        throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "此品类存在关联的商品，无法删除");

    }


}
