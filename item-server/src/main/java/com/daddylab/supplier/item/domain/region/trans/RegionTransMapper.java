package com.daddylab.supplier.item.domain.region.trans;

import com.daddylab.supplier.item.domain.region.enums.RegionLevel;
import com.daddylab.supplier.item.domain.region.vo.Region;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.RegionRelevant;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper()
public interface RegionTransMapper {
    RegionTransMapper INSTANCE = Mappers.getMapper(RegionTransMapper.class);

    @Mapping(target = "parentId", source = "PId")
    @Mapping(target = "parentCode", source = "PCode")
    @Mapping(target = "children", ignore = true)
    @Mapping(target = "parent", ignore = true)
    Region toRegion(RegionRelevant regionRelevant);

    default RegionLevel toRegionLevel(Integer value) {
        return IEnum.getEnumByValue(RegionLevel.class, value);
    }
}
