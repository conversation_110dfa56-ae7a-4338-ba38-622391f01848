package com.daddylab.supplier.item.infrastructure.gatewayimpl.item;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.domain.item.gateway.ItemOperationGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemOperator;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemOperatorService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 11:41 上午
 * @description
 */
@Service
public class ItemOperationGatewayImpl implements ItemOperationGateway {

    @Autowired
    IItemOperatorService iItemOperatorService;

    @Override
    public List<ItemOperator> getItemOperation(Long itemId) {
        QueryWrapper<ItemOperator> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ItemOperator::getItemId, itemId);
        return iItemOperatorService.list(wrapper);
    }

    @Override
    public void saveOrUpdateOperation(ItemOperator itemOperator) {
        iItemOperatorService.saveOrUpdate(itemOperator);
    }

    @Override
    public void saveOrUpdateBatchOperation(List<ItemOperator> itemOperator) {
        iItemOperatorService.saveOrUpdateBatch(itemOperator);
    }

    @Override
    public List<Long> getRunnerIdList(Long itemId) {
        QueryWrapper<ItemOperator> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ItemOperator::getItemId, itemId);
        final List<ItemOperator> list = iItemOperatorService.list(wrapper);
        return list.stream().map(ItemOperator::getId).collect(Collectors.toList());
    }

    @Override
    public void removeRunner(List<Long> runnerId) {
        iItemOperatorService.removeByIdsWithTime(runnerId);
    }

    @Override
    public List<String> getNamesByUserId(Long userId) {
        return iItemOperatorService.list(new LambdaQueryWrapper<ItemOperator>()
                .eq(ItemOperator::getOperatorId,userId)).stream().map(ItemOperator::getName).collect(Collectors.toList());
    }

    @Override
    public List<ItemOperator> getList() {
        return iItemOperatorService.list();
    }

    @Override
    public List<ItemOperator> getList(String name) {
        LambdaQueryWrapper<ItemOperator> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(name),ItemOperator::getName,name);
        return iItemOperatorService.list(queryWrapper);
    }

}
