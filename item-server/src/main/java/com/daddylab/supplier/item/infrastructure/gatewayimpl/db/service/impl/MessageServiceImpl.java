package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Message;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.MessageMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IMessageService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-21
 */
@Service
public class MessageServiceImpl extends DaddyServiceImpl<MessageMapper, Message> implements IMessageService {

}
