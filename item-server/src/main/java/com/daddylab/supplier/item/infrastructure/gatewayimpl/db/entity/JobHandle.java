package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class JobHandle implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;

    /**
     * 是否删除：0否1是
     */
    @TableLogic(value = "0", delval = "id")
    private Long isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * JOB
     */
    private String job;

    /**
     * SCOPE
     */
    private String scope;

    /**
     * 记录ID
     */
    private String recId;

    /**
     * 字段1
     */
    private String v1;

    /**
     * 字段2
     */
    private String v2;

    /**
     * 字段3
     */
    private String v3;

    /**
     * 字段4
     */
    private String v4;

    /**
     * 字段5
     */
    private String v5;

    /**
     * 字段6
     */
    private String v6;

    /**
     * 字段7
     */
    private String v7;

    /**
     * 字段8
     */
    private String v8;

    /**
     * 字段9
     */
    private String v9;

    /**
     * 字段10
     */
    private String v10;
    private String v11;
    private String v12;
    private String v13;
    private String v14;
    private String v15;
    private String v16;
    private String v17;
    private String v18;
    private String v19;
    private String v20;
    private String v21;
    private String v22;
    private String v23;
    private String v24;

    /**
     * 唯一键
     */
    public String getUniqueKey() {
        return job + "-" + scope + "-" + recId;
    }

}
