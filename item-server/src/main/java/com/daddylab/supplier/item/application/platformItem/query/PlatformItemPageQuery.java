package com.daddylab.supplier.item.application.platformItem.query;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.PositiveOrZero;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("平台商品查询")
public class PlatformItemPageQuery extends PageQuery {
    private static final long serialVersionUID = 8757714705888091197L;

    @PositiveOrZero(message = "平台商品ID应该是一个正整数")
    @ApiModelProperty("平台商品ID")
    Long platformItemId;

    @ApiModelProperty("平台商品ID（批量查询）")
    List<Long> platformItemIds;

    @ApiModelProperty("外部平台商品ID")
    String outerItemId;

    @ApiModelProperty("外部平台商品ID（批量查询）")
    Collection<String> outerItemIds;

    @PositiveOrZero(message = "商品ID应该是一个正整数")
    @ApiModelProperty("商品ID")
    Long itemId;

    @PositiveOrZero(message = "店铺ID应该是一个正整数")
    @ApiModelProperty("店铺ID")
    Long shopId;

    @ApiModelProperty("店铺编号")
    String shopNo;

    @ApiModelProperty("平台")
    Platform platform;

    @ApiModelProperty("平台（多选）")
    List<Platform> platforms;

    @Size(max = 50, message = "SKU编码长度应该在{min}-{max}个字符之间")
    @ApiModelProperty("商品编码")
    String itemCode;

    @Size(max = 50, message = "SKU编码长度应该在{min}-{max}个字符之间")
    @ApiModelProperty("SKU编码")
    String skuCode;

    @ApiModelProperty("类目ID")
    @PositiveOrZero(message = "类目ID应该是一个正整数")
    Long categoryId;

    @ApiModelProperty("商品状态")
    PlatformItemStatus status;

    @ApiModelProperty("商品状态（多选）")
    List<PlatformItemStatus> statusIn;

    @ApiModelProperty("合作方筛选（多选）")
    private List<Integer> businessLine = new ArrayList<>();

    @ApiModelProperty("商品名称")
    private String itemName;

    /** 是否打开同步开关 */
    @ApiModelProperty("是否打开同步开关")
    private Boolean syncEnabled;

    /** 是否打开库存锁定开关 */
    @ApiModelProperty("是否打开库存锁定开关")
    private Boolean lockEnabled;

    @ApiModelProperty(value = "搜索关键字", notes = "平台商品ID或者平台商品名称")
    private String keyword;
}
