package com.daddylab.supplier.item.application.stockSpec;

import com.daddylab.mall.wdtsdk.apiv2.wms.dto.StockSpecSearch2Response;
import com.daddylab.supplier.item.common.trans.BusinessLineAssembler;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtStockSpec;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtStockSpecRt;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.assembler.CommonAssembler;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.types.stockSpec.AvailableStockSpecVO;
import com.daddylab.supplier.item.types.stockSpec.StockSpecExportVO;
import com.daddylab.supplier.item.types.stockSpec.StockSpecVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/2/29
 */

@Mapper(uses = {
        CommonAssembler.class, BusinessLineAssembler.class
}, imports = {
        NumberUtil.class
})
public interface WdtStockSpecAssembler {
    WdtStockSpecAssembler INST = Mappers.getMapper(WdtStockSpecAssembler.class);

    @Mapping(target = "num7Days", source = "num7days")
    @Mapping(target = "num14Days", source = "num14days")
    @Mapping(target = "availableStock", ignore = true)
    WdtStockSpec apiModelToPersistModel(StockSpecSearch2Response.Detail detail);

    WdtStockSpecRt poToRtPo(WdtStockSpec value);

    WdtStockSpec rtPoToPo(WdtStockSpecRt value);

    @Mapping(target = "unit", ignore = true)
    @Mapping(target = "spuName", source = "goodsName")
    @Mapping(target = "spuCode", source = "goodsNo")
    @Mapping(target = "skuName", source = "specName")
    @Mapping(target = "skuCode", source = "specNo")
    @Mapping(target = "firstSalesTime", ignore = true)
    @Mapping(target = "categoryName", ignore = true)
    @Mapping(target = "categoryId", ignore = true)
//    @Mapping(target = "businessLine", ignore = true)
    @Mapping(target = "brandId", ignore = true)
    @Mapping(target = "barCode", ignore = true, source = "barcode")
    @Mapping(target = "availableStock", source = "availableStock")
    @Mapping(target = "alertStock", ignore = true)
    StockSpecVO persistModelToStockSpecVO(WdtStockSpec stockSpec);

    @Mapping(target = "defect", expression = "java(commonAssembler.booleanToString(vo.getDefect()))")
    @Mapping(target = "refundNum", expression = "java(NumberUtil.add(vo.getRefundExchNum(),vo.getRefundOnwayNum()))")
    StockSpecExportVO stockSpecVOToStockSpecExportVO(StockSpecVO vo);

    @Mapping(target = "warnStock", ignore = true)
    @Mapping(target = "ofInventoryRatio", ignore = true)
    @Mapping(target = "allocableStock", ignore = true)
    @Mapping(target = "spuName", source = "goodsName")
    @Mapping(target = "spuCode", source = "goodsNo")
    @Mapping(target = "skuName", source = "specName")
    @Mapping(target = "skuCode", source = "specNo")
    AvailableStockSpecVO persistModelToAvailableStockSpecVO(WdtStockSpec t);
}
