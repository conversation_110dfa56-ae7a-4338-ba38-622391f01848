package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.stockSpec.InventoryMonitorAlertVO;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryMonitor;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 库存警戒 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
public interface InventoryMonitorMapper extends DaddyBaseMapper<InventoryMonitor> {

  List<InventoryMonitorAlertVO> selectAbsoluteThresholdAlertList(
      @Param("limit") int limit, @Param("cursor") Long cursor);
}
