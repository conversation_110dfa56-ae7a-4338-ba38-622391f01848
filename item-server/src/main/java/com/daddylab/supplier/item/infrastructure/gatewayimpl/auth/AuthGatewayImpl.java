package com.daddylab.supplier.item.infrastructure.gatewayimpl.auth;

import com.daddylab.supplier.item.domain.auth.gateway.AuthGateway;
import com.daddylab.supplier.item.domain.auth.types.TicketSession;
import com.daddylab.supplier.item.infrastructure.authorize.AuthFeignClient;
import com.daddylab.supplier.item.infrastructure.authorize.ServiceValidateRst;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class AuthGatewayImpl implements AuthGateway {
    private final AuthFeignClient authFeignClient;

    @Override
    public TicketSession ticketValidate(String service, String ticket) {
        final Rsp<ServiceValidateRst> serviceValidateRstRsp = authFeignClient.serviceValidate(service, ticket);
        if (serviceValidateRstRsp.getFlag()) {
            return new TicketSession(serviceValidateRstRsp.getData().getUserId(), serviceValidateRstRsp.getData().getSessionId());
        }
        return null;
    }
}
