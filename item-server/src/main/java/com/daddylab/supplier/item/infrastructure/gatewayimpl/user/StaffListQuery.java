package com.daddylab.supplier.item.infrastructure.gatewayimpl.user;

import com.fasterxml.jackson.annotation.JsonProperty;
import feign.Param;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class StaffListQuery {
    @JsonProperty("page_size")
    @Param("page_size")
    @ApiModelProperty("pageSize")
    @NotNull private Integer pageSize = 10;

    @JsonProperty("page_index")
    @Param("page_index")
    @ApiModelProperty("pageIndex")
    @NotNull private Integer pageIndex = 1;

    /**
     * 真实姓名
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 花名
     */
    @ApiModelProperty("花名")
    private String nickname;

    @ApiModelProperty("手机号")
    private String mobile;

    /**
     * 0为离职，1为在职，-1为全部
     */
    @ApiModelProperty("0为离职，1为在职，-1为全部")
    private Integer status = 1;

    @ApiModelProperty("部门名称")
    private String dept;

    @ApiModelProperty("部门名称（多选）")
    private List<String> depts;

    @ApiModelProperty("部门名称（多选）非递归，与上面的部门查询条件是或的关系")
    private List<String> exactDepts;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户ids")
    private List<Long> userIds;

}
