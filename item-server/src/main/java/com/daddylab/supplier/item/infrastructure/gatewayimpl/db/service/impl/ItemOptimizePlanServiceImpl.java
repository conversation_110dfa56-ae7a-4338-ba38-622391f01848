package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemOptimizePlan;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemOptimizePlanMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemOptimizePlanService;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品优化计划 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Service
public class ItemOptimizePlanServiceImpl extends DaddyServiceImpl<ItemOptimizePlanMapper, ItemOptimizePlan> implements IItemOptimizePlanService {


}
