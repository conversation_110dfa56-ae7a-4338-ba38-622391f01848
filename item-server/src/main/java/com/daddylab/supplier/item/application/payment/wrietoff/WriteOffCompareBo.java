package com.daddylab.supplier.item.application.payment.wrietoff;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年01月16日 2:38 PM
 */
@Data
public class WriteOffCompareBo {

    private String skuCode;

    private BigDecimal price;

    private Integer quantity;

    /**
     * 1.入库单
     * 2.出库单
     * 3.结算单
     */
    private Integer type;

    /**
     * 出入库单明细 ID
     */
    private List<Long> id;

    private Integer key;



}
