package com.daddylab.supplier.item.application.purchase.order.factory;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.order.settlement.sys.OrderSettlementSysService;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.RestartWrapperCmd;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.BaseProcess;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.PriceEngineManager;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.PurchaseBillService;
import com.daddylab.supplier.item.application.stockInOrder.StockInOrderServiceBizImpl;
import com.daddylab.supplier.item.common.ExternalShopWdtNo;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.OtherStockInOutConfig;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.test.DateScriptService;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseSingleSkuCombinationPrice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseSingleSkuCombinationPriceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockInOrderService;
import com.daddylab.supplier.item.infrastructure.limit.RateLimit;
import com.daddylab.supplier.item.infrastructure.timing.StopWatch;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static cn.hutool.core.text.CharSequenceUtil.isNotBlank;
import static com.daddylab.supplier.item.common.GlobalConstant.*;

/**
 * 系统采购单修锅接口
 *
 * <AUTHOR> up
 * @date 2022年09月02日 12:35 AM
 */
@Slf4j
@RestController
@RequestMapping("/purchaseOrderCompensation")
@Api(hidden = true)
public class CompensationController {

    @Resource
    CommonUtil commonUtil;

    @Resource
    DateScriptService dateScriptService;

    @Autowired
    WdtStockOrderHandler wdtStockOrderHandler;

    @Resource
    OtherStockInOutConfig otherStockInOutConfig;

    @Resource
    ExternalShopWdtNo externalShopWdtNo;

    @Resource
    WdtOrderDetailMapper wdtOrderDetailMapper;


    // ---------------------------------- 五部曲开始 --------------------------------------------------------

    /**
     * 第一步
     * 从wdt原始订单数据中，洗出wdtOrderDetailWrapper
     *
     * @param targetMoth
     * @return
     * @throws InterruptedException
     */
    @GetMapping(value = "/clearWdtOrder")
    @Auth(noAuth = true)
    public Response test(@RequestParam("month") String month,
                         @RequestParam("test") Boolean isTest) throws InterruptedException {

        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            WdtStockOrderHandler bean1 = SpringUtil.getBean(WdtStockOrderHandler.class);
            TimeBO timeBO = TimeBO.of(month);
            bean1.runClean(timeBO);
        });

        return Response.buildSuccess();
    }

    /**
     * 第二部，启动价格引擎
     * 更新wdtOrderDetailWrapper中的数据价格，根据采购活动价，组合价等等
     *
     * @param targetMoth
     * @return
     * @throws InterruptedException
     */
    @GetMapping(value = "/updatePrice")
    @Auth(noAuth = true)
    public Response test2(@RequestParam("targetMoth") String targetMoth) {

        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            // 价格清洗 处理链路开始。
            PriceEngineManager bean1 = SpringUtil.getBean(PriceEngineManager.class);
            bean1.start(targetMoth);
            commonUtil.remind("202311", ListUtil.of(GlobalConstant.SEVEN_UP), "价格清洗链路完成");
        });
        return Response.buildSuccess();
    }

    @GetMapping(value = "/updateRefundPrice")
    @Auth(noAuth = true)
    public Response updateRefundPrice(@RequestParam("targetMoth") String targetMoth) {
        return Response.buildFailure(ErrorCode.API_RESP_ERROR.getCode(), "功能暂时不存在");

//        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
//            // 价格清洗 处理链路开始。
//            PriceEngineManager bean1 = SpringUtil.getBean(PriceEngineManager.class);
//            bean1.updateRefundWrapperPrice(targetMoth);
//        });
//
//        return Response.buildSuccess();
    }


    /**
     * 第三部，进行数据归纳统计
     * 如果目标月份存在统计数据，会直接物理删除
     * 根据供应商，仓库对wdtOrderDetailWrapper数据进行聚合统计
     *
     * @param targetMoth
     * @return
     * @throws InterruptedException
     */
    @GetMapping(value = "/runStatistics")
    @Auth(noAuth = true)
    public Response test3(@RequestParam("targetMoth") String targetMoth) throws InterruptedException {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            try {
                ErpPurchaseOrderHandler bean1 = SpringUtil.getBean(ErpPurchaseOrderHandler.class);
                bean1.runStatistics(targetMoth);
            } catch (Exception e) {
                log.error("test2 error", e);
            }
        });
        return Response.buildSuccess();
    }

    /**
     * 第四部，生成erp采购订单
     * 1.删除已经生成的采购单，入库单，出库单，
     * 2.根据wdt_order_detail_wrapper中type=99的统计数据，生成采购单。
     *
     * @param targetMoth 需要修补的目标月份
     * @return
     */
    @GetMapping(value = "/purchaseOrderGenerate")
    @Auth(noAuth = true)
    public Response test4(@RequestParam("targetMoth") String targetMoth) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            try {
                ErpPurchaseOrderHandler bean1 = SpringUtil.getBean(ErpPurchaseOrderHandler.class);
                bean1.purchaseOrderGenerate(TimeBO.of(targetMoth));
            } catch (Exception e) {
                log.error("test2 error", e);
            }
        });
        return Response.buildSuccess();
    }

    /**
     * 第五步，
     * 根据采购订单表的数据，生成采购入库单或者退料出库单，并且分别将两种数据推送到金蝶
     *
     * @param targetMoth
     * @return
     */
    @GetMapping(value = "/stockOrderGenerate")
    @Auth(noAuth = true)
    @RateLimit(key = "stockOrderGenerate", methodDesc = "根据采购订单表的数据，生成采购入库单或者退料出库单", time = 600, count = 1)
    public Response stockOrderGenerate(@RequestParam("targetMoth") String targetMoth) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            try {
//                dateScriptService.clearSkuUnit();

                ErpPurchaseOrderHandler bean1 = SpringUtil.getBean(ErpPurchaseOrderHandler.class);
                bean1.erpStockInOrOutOrderGenerator(TimeBO.of(targetMoth), null);
                commonUtil.remindStockOrder(targetMoth, ListUtil.of("yuntun.cj", "shengwei.fu"));
            } catch (Exception e) {
                log.error("test2 error", e);
            }
        });
        return Response.buildSuccess();
    }

    @GetMapping(value = "/remind")
    @Auth(noAuth = true)
    public Response remind() {
        commonUtil.remindPurchaseOrder("202308", ListUtil.of(GlobalConstant.SEVEN_UP));
        commonUtil.remindStockOrder("202308", ListUtil.of(GlobalConstant.SEVEN_UP));
//        commonUtil.remainBill("202308", "http://www.baidu.com", ListUtil.of(GlobalConstant.SEVEN_UP));
        commonUtil.remainOrderSettlement("2023", ListUtil.of(GlobalConstant.SEVEN_UP));
        return Response.buildSuccess();
    }

    @GetMapping(value = "/preStockIn")
    @Auth(noAuth = true)
    public Response preStockIn(@RequestParam("targetMoth") String targetMoth) {
        WdtStockOrderHandler wdtStockOrderHandler = SpringUtil.getBean(WdtStockOrderHandler.class);
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> wdtStockOrderHandler.preStockInRun(TimeBO.of(targetMoth)));
        return Response.buildSuccess();
    }

//    @GetMapping(value = "/refundStockIn")
//    @Auth(noAuth = true)
//    public Response refundStockIn(@RequestParam("targetMoth") String targetMoth, @RequestParam("skuCode") String skuCode) {
//        WdtStockOrderHandler wdtStockOrderHandler = SpringUtil.getBean(WdtStockOrderHandler.class);
//
//        TimeBO timeBo = TimeBO.of(targetMoth);
//        wdtStockOrderHandler.refundStockInIdHandlerPlus(
//                timeBo.getOperateMonth(), "2024-01-01 00:00:00", "2024-02-01 00:00:00",
//                otherStockInOutConfig.getWarehouseNos(), externalShopWdtNo.getShopNos(), skuCode
//        );
//        return Response.buildSuccess();
//    }

    @GetMapping(value = "/refundStockIn")
    @Auth(noAuth = true)
    public Response refundStockIn2(@RequestParam("targetMoth") String targetMoth) {
        WdtStockOrderHandler wdtStockOrderHandler = SpringUtil.getBean(WdtStockOrderHandler.class);
        TimeBO timeBo = TimeBO.of(targetMoth);
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> wdtStockOrderHandler.mainRefundStockInRun(timeBo, null));
        return Response.buildSuccess();
    }

    @GetMapping(value = "/deleteData")
    @Auth(noAuth = true)
    public Response deleteData(@RequestParam("targetMoth") String targetMoth, @RequestParam("type") Integer type) {
        wdtOrderDetailMapper.deleteByTypeAndMonth(targetMoth, type);
        return Response.buildSuccess();
    }


    // ---------------------------------- 五部曲结束 --------------------------------------------------------

    /**
     * 根据指定的采购单编码，，重新生成出入库单，并且投同步到金蝶
     *
     * @param purchaseOrderNos
     * @return
     */
    @PostMapping(value = "/stockOrderCompensate")
    @Auth(noAuth = true)
    public Response stockOrderCompensate(String purchaseOrderNos) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            try {
                ErpPurchaseOrderHandler bean1 = SpringUtil.getBean(ErpPurchaseOrderHandler.class);
                List<String> strings = Arrays.asList(purchaseOrderNos.split(","));
                bean1.erpStockInOrOutOrderGeneratorCompensate(strings);
            } catch (Exception e) {
                log.error("stockOrderCompensate error", e);
            }
        });
        return Response.buildSuccess();
    }

    @GetMapping(value = "/getComposeSkuPrice")
    @Auth(noAuth = true)
    public Response getComposeSkuPrice(String code, BigDecimal costPrice) {
        CommonCalculator bean1 = SpringUtil.getBean(CommonCalculator.class);
        List<CommonCalculator.SkuPriceUnderSuiteNo> skuCostPriceUnderSuiteNo = bean1.getSkuCostPriceUnderSuiteNo(code, costPrice);
        return MultiResponse.of(skuCostPriceUnderSuiteNo);
    }

    @GetMapping(value = "createBill")
    @Auth(noAuth = true)
    @RateLimit(key = "createBill", methodDesc = "生成采购对账总账单", time = 1800, count = 1)
    public Response createBill(String operateTime) {
        PurchaseBillService bean1 = SpringUtil.getBean(PurchaseBillService.class);
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            String billDownloadUrl = bean1.createBill(operateTime);
            stopWatch.stop();
            long total = stopWatch.getTotal(TimeUnit.MINUTES);
            log.info("---- CompensationController createBill ---- url:{}", billDownloadUrl);
            commonUtil.remainBill(operateTime, billDownloadUrl, ListUtil.of(GlobalConstant.SEVEN_UP, GlobalConstant.YUN_TUN), total);
        });
        return Response.buildSuccess();
    }


    @Resource
    IPurchaseSingleSkuCombinationPriceService purchaseSingleSkuCombinationPriceService;

    @Resource
    BaseProcess baseProcess;

    @GetMapping(value = "dailySingleCombinationPrice")
    @Auth(noAuth = true)
    public Response dailySingleCombinationPrice(String code, String time) {
        List<PurchaseSingleSkuCombinationPrice> list = purchaseSingleSkuCombinationPriceService.lambdaQuery()
                .eq(PurchaseSingleSkuCombinationPrice::getPriceType, 1)
                .eq(PurchaseSingleSkuCombinationPrice::getSource, 1)
                .eq(isNotBlank(code), PurchaseSingleSkuCombinationPrice::getCode, code)
                .select().list();
        for (PurchaseSingleSkuCombinationPrice purchaseSingleSkuCombinationPrice : list) {
            baseProcess.singleCombinationPriceHandler(purchaseSingleSkuCombinationPrice, time, 1);
        }
        return Response.buildSuccess();
    }

    @Resource
    OrderSettlementSysService orderSettlementSysService;

    /**
     * 补偿生成自动采购结算订单
     *
     * @param no    采购单号
     * @param month yyyyMM,
     */
    @GetMapping(value = "settlement")
    @Auth(noAuth = true)
//    @RateLimit(key = "settlement", methodDesc = "生成采购结算订单", time = 600, count = 1)
    public void settlement(@RequestParam("no") String no, @RequestParam("targetMoth") String month) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            orderSettlementSysService.autoCreateOrderSettlementInfo(TimeBO.of(month), no);

            commonUtil.remind(month,
                    ListUtil.of(SEVEN_UP, YUN_TUN, AI_MI),
                    "采购待结算订单生成完毕。");

        });
    }

    @GetMapping(value = "settlementCheck")
    @Auth(noAuth = true)
    public void settlementCheck(@RequestParam("targetMoth") String month) {
        orderSettlementSysService.supplementData(TimeBO.of(month));
    }

    @PostMapping(value = "/restartWrapper")
    @Auth(noAuth = true)
    public Response restartWrapper(@RequestBody RestartWrapperCmd cmd) {
        wdtStockOrderHandler.restartWdtOrderDetailWrapper(cmd);
        return Response.buildSuccess();
    }


    @Autowired
    IStockInOrderService iStockInOrderService;

    @Autowired
    StockInOrderServiceBizImpl stockInOrderServiceBiz;

    @GetMapping(value = "stockInOrder")
    @Auth(noAuth = true)
    public void stockInOrder(@RequestParam("start") Long start, @RequestParam("end") Long end, @RequestParam("no") String no) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            List<StockInOrder> list;
            if (StrUtil.isNotBlank(no)) {
                list = iStockInOrderService.lambdaQuery()
                        .eq(StockInOrder::getNo, no).list();
            } else {
                list = iStockInOrderService.lambdaQuery()
                        .between(StockInOrder::getReceiptTime, start, end)
                        .eq(StockInOrder::getState, 3).list();
            }

            log.info("同步入库单总数.size:{}", list.size());

            for (StockInOrder stockInOrder : list) {
                try {
                    stockInOrderServiceBiz.pushKingDee(stockInOrder);
                    ThreadUtil.sleep(3000);
                } catch (Exception e) {
                    log.error("同步入库单失败", e);
                }
            }
        });
    }


}
