package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtGoodsSn;
import com.daddylab.supplier.item.types.goodsSn.OpenGoodsSnDto;
import com.daddylab.supplier.item.types.goodsSn.OpenGoodsSnQuery;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * ERP序列号管理页面的数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
public interface WdtGoodsSnMapper extends DaddyBaseMapper<WdtGoodsSn> {

    int saveGoodsSnBatch(@Param("items") Collection<WdtGoodsSn> items);

    List<OpenGoodsSnDto> openGoodsSnQuery(OpenGoodsSnQuery query);
}
