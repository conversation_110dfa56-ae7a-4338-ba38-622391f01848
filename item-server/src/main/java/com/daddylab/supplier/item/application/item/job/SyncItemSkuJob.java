//package com.daddylab.supplier.item.application.item.job;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.StrUtil;
//import com.daddylab.job.core.handler.annotation.XxlJob;
//import com.daddylab.supplier.item.common.enums.PoolEnum;
//import com.daddylab.supplier.item.common.util.ThreadUtil;
//import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
//import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
//import com.daddylab.supplier.item.infrastructure.kingdee.dto.KingDeeSkuResp;
//import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.LinkedList;
//import java.util.List;
//import java.util.Optional;
//import java.util.concurrent.atomic.AtomicInteger;
//
//
///**
// * <AUTHOR> up
// * @date 2022/5/23 2:06 下午
// */
//@Slf4j
//@Service
//public class SyncItemSkuJob {
//
//    @Autowired
//    IItemSkuService iItemSkuService;
//
//    @Autowired
//    KingDeeTemplate kingDeeTemplate;
//
//    @Autowired
//    ReqTemplate reqTemplate;
//
//    @Autowired
//    ItemSkuGateway itemSkuGateway;
//
//    @XxlJob("syncItemSkuJob")
//    public void syncItemSkuJob() {
//
//        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
//
//            AtomicInteger count = new AtomicInteger();
//            List<ItemSku> list = new LinkedList<>();
//
//            iItemSkuService.lambdaQuery()
//                    .eq(ItemSku::getKingDeeId, "")
//                    .or().isNull(ItemSku::getKingDeeId)
//                    .select().list()
//                    .forEach(itemSku -> {
//                        Optional<KingDeeSkuResp> kingDeeSkuResp = reqTemplate.querySkuByNo(itemSku.getSkuCode());
//                        kingDeeSkuResp.ifPresent(val -> {
//                            if (StrUtil.isNotBlank(val.getId())) {
//                                count.getAndIncrement();
//                                log.info("补充erp的物料的金蝶id。已完成数据：" + count.get());
//
//                                itemSku.setKingDeeId(val.getId());
//                                list.add(itemSku);
//                            }
//                        });
//                    });
//
//            if (CollUtil.isNotEmpty(list)) {
//                iItemSkuService.updateBatchById(list);
//            }
//
//
////            PageInfo<ItemSku> pageInfo = PageHelper.startPage(index, size).doSelectPageInfo(() -> iItemSkuService.list(wrapper));
////            XxlJobHelper.log("等待同步的sku数量。{}", pageInfo.getTotal());
////
////            int pageNum = pageInfo.getPages();
////
////            while (pageNum > 0) {
////                List<Long> errorId = new LinkedList<>();
////
////                for (ItemSku record : pageInfo.getList()) {
////                    try {
////                        // 金蝶中已经存在此物料,覆盖。
////                        Optional<KingDeeSkuResp> kingDeeSkuResp = reqTemplate.querySkuByNo(record.getSkuCode());
////                        kingDeeSkuResp.ifPresent(var -> {
////                            itemSkuGateway.setSkuKingDeeId(record.getId(), var.getId());
////                        });
////                        // 金蝶中此物料不存在。
////                        kingDeeSkuResp.orElseGet(() -> {
////                            kingDeeTemplate.handler(ApiEnum.SAVE_SKU, record.getId(), "");
////                            return null;
////                        });
////                    } catch (Exception e) {
////                        log.info("将sku同步到kingDee异常,id:{},error:{}", record.getId(), e.getMessage(), e);
////                        errorId.add(record.getId());
////                    }
////                }
////
////                if (CollectionUtils.isNotEmpty(errorId)) {
////                    String s = StrUtil.join(",", errorId);
//////                    Alert.text(MessageRobotCode.GLOBAL, "以下skuId同步到KingDee异常。" + s);
////                    XxlJobHelper.log("以下skuId同步到KingDee异常,{}", s);
////                }
////
////                pageNum = pageNum - 1;
////
////                index = index + 1;
////                pageInfo = PageHelper.startPage(index, size).doSelectPageInfo(() -> iItemSkuService.list(wrapper));
////            }
////
////            stopWatch.stop();
////            XxlJobHelper.log("同步sku到kingDee流程结束。耗时:{}", stopWatch.getTotalTimeSeconds());
////
//        });
//    }
//
//}
