package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NuoNuoInvoiceRequest;

import java.util.List;

/**
 * <p>
 * 用户诺诺开票请求参数 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
public interface INuoNuoInvoiceRequestService extends IDaddyService<NuoNuoInvoiceRequest> {

    List<NuoNuoInvoiceRequest> getByOrderNo(String orderNo);
}
