package com.daddylab.supplier.item.application.platformItem.config;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.google.common.collect.Lists;

import java.util.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/3/25
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "platform-item-sync")
public class PlatformItemSyncConfig {
  /**
   * 是否禁用平台商品SKU变更消息消费者
   */
  private boolean disablePlatformSkuMessageConsumer;
  /**
   * 是否启用系统按时自动上传库存到平台
   */
  private boolean inventoryAutoUploadEnabled;
  /**
   * 是否启用上传白名单，开启后只有在白名单中的商品才允许上传库存
   */
  private boolean whiteListEnabled = true;
  /**
   * 白名单商品配置
   */
  private List<Long> whiteList = Lists.newArrayList();
  /**
   * 白名单平台，配置到白名单中的平台将不受白名单商品配置的约束
   */
  private List<Platform> whiteListPlatforms = Lists.newArrayList();
  /**
   * 是否启用MOCK上传，开启后不会真正上传库存，此项配置开启的情况下，其他安全配置（白名单）不会生效
   */
  private boolean mockUpload = true;
  /**
   * 同步批量大小
   */
  private int chunkSize = 50;
  /**
   * 老爸商城店铺ID
   */
  private Long defaultMallShopId = 10004L;
  /**
   * 老爸商城店铺编号
   */
  private String defaultMallShopNo = "K0004";
  /**
   * 需要同步商品信息的店铺列表
   */
  private List<Long> mallShopSyncList = Lists.newArrayList(10004L, 10002L);
  /** 是否在全量同步完成后检查清理无效的SKU */
  private boolean clearInvalidSku;
  /** 商品类型权重配置 */
  private List<ItemTypeWeightConfig> itemWeightConfigs;
  /** 低库存预警Webhook */
  private Map<String, String> lowInventoryAlertWebhook = new HashMap<>();
  /** 低库存预警Webhook默认值 */
  private String lowInventoryAlertWebhookDefault = "4d24020c-dca4-4dfd-87e4-84ef7c3c3b03";
  
  public ItemTypeWeightConfig getItemTypeWeightConfig(PlatformItem platformItem) {
    if (platformItem.getPlatform() != Platform.LAOBASHOP) {
      for (ItemTypeWeightConfig config : itemWeightConfigs) {
        if (config.isAsDefault()) {
          return config;
        }
      }
    }
    for (ItemTypeWeightConfig config : itemWeightConfigs) {
      String goodsName = platformItem.getGoodsName();
      if (config.getKeywords().stream().anyMatch(goodsName::contains)) {
        return config;
      }
    }
    // 如果没有匹配到类型，返回默认类型
    for (ItemTypeWeightConfig config : itemWeightConfigs) {
      if (config.isAsDefault()) {
        return config;
      }
    }
    throw new IllegalArgumentException("未找到匹配的商品类型");
  }
  
  public int getInventoryAllocWeight(PlatformItem platformItem) {
    return getItemTypeWeightConfig(platformItem).getWeight();
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Item {
    private Platform platform;
    private String itemId;
    private String skuId;
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class ItemTypeWeightConfig {
    private String type;
    private List<String> keywords;
    private int weight;
    private boolean asDefault;
  }
}
