package com.daddylab.supplier.item.infrastructure.batchcall;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.set.SynchronizedSet;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2021/12/11
 */
@Slf4j
public class RequestManager {
    final private ConcurrentHashMap<Method, Map<Object, RequestFutures>> requests = new ConcurrentHashMap<>();
    final private ConcurrentHashMap<Method, Set<Object>> handing = new ConcurrentHashMap<>();
    final private ConcurrentHashMap<Method, ReentrantLock> locks = new ConcurrentHashMap<>();

    private Map<Object, RequestFutures> byMethod(Method method) {
        return requests.computeIfAbsent(method, (k) -> Maps.newLinkedHashMap());
    }

    @SuppressWarnings("unchecked")
    private Set<Object> handingByMethod(Method method) {
        return handing.computeIfAbsent(method, (k) -> SynchronizedSet.decorate(Sets.newHashSet()));
    }

    public void registerRequest(Method method, Object request, CompletableFuture<Object> future) {
        withLock(method, () -> {
            byMethod(method).computeIfAbsent(request, (k) -> new RequestFutures()).register(future);
        });
    }

    @NonNull
    private ReentrantLock getLock(Method method) {
        return locks.computeIfAbsent(method, k -> new ReentrantLock());
    }

    private synchronized void withLock(Method method, Runnable callback) {
        getLock(method).lock();
        try {
            callback.run();
        } finally {
            getLock(method).unlock();
        }
    }

    private synchronized <T> T withLock(Method method, Supplier<T> callback) {
        final ReentrantLock lock = getLock(method);
        lock.lock();
        try {
            return callback.get();
        } finally {
            lock.unlock();
        }
    }

    public synchronized boolean isHanding(Method method, Object request) {
        return withLock(method, () -> handingByMethod(method).contains(request));
    }

    public synchronized boolean isRegister(Method method, Object request) {
        return withLock(method, () -> isHanding(method, request) || byMethod(method).containsKey(request));
    }

    public void complete(Method method, List<RequestEntry> requests, Map<Object, Object> results) {
        for (RequestManager.RequestEntry requestEntry : requests) {
            final Object request = requestEntry.getRequest();
            final Object result = results.get(request);

            for (CompletableFuture<Object> future : requestEntry.getRequestFutures().getFutures()) {
                future.complete(result);
            }
            handingByMethod(method).remove(request);
        }
    }

    public void completeExceptionally(Method method, List<RequestEntry> requests, Throwable ex) {
        if (!(ex instanceof RuntimeException)) {
            if (ex instanceof InvocationTargetException) {
                ex = ex.getCause();
            } else {
                ex = new RuntimeException("调用目标方法异常", ex);
            }
        }
        for (RequestEntry requestEntry : requests) {
            final Object request = requestEntry.getRequest();

            for (CompletableFuture<Object> future : requestEntry.getRequestFutures().getFutures()) {
                future.completeExceptionally(ex);
            }
            handingByMethod(method).remove(request);
        }
    }

    public List<RequestEntry> dequeue(Method method, int num) {
        return withLock(method, () -> {
            final Iterator<Map.Entry<Object, RequestFutures>> iterator = byMethod(method).entrySet().iterator();
            final List<RequestEntry> returns = Lists.newArrayListWithCapacity(num);
            for (int i = 0; i < num && iterator.hasNext(); i++) {
                final Map.Entry<Object, RequestFutures> entry = iterator.next();
                final RequestEntry requestEntry = new RequestEntry(entry.getKey(), entry.getValue());
                returns.add(requestEntry);
                handingByMethod(method).add(requestEntry.getRequest());
                iterator.remove();
            }
            return returns;
        });
    }

    public int requestSize(Method method) {
        return byMethod(method).size();
    }

    @EqualsAndHashCode(of = "request")
    @Getter
    @AllArgsConstructor
    static class RequestEntry {
        private final Object request;
        private final RequestFutures requestFutures;
    }

    public static class RequestFutures {
        private final List<CompletableFuture<Object>> futures = Lists.newArrayList();

        public synchronized void register(CompletableFuture<Object> future) {
            this.futures.add(future);
        }

        public synchronized List<CompletableFuture<Object>> getFutures() {
            return futures;
        }
    }
}
