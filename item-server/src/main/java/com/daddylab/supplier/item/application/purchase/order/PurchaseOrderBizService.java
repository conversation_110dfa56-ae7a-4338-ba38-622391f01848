package com.daddylab.supplier.item.application.purchase.order;

import com.actionsoft.sdk.service.model.TaskInstance;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuVO;
import com.daddylab.supplier.item.application.payment.dto.PaymentDetailAmountVO;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.PurchasePaymentDetailVo;
import com.daddylab.supplier.item.controller.purchase.dto.order.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentApplyOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentApplyOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayPurposeEnum;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 采购订单
 *
 * <AUTHOR> up
 * @date 2022/3/25 9:50 上午
 */
public interface PurchaseOrderBizService {

  /**
   * 采购订单分页查询
   *
   * @param query 入参
   * @return 分页查询相应
   */
  PageResponse<PurchaseOrderPageVO> page(PurchaseOrderPageQuery query);

  /**
   * 采购单付款条件
   *
   * @return
   */
  SingleResponse<Map<Integer, String>> payCondition();

  /**
   * 采购业态商品列表
   *
   * @param query
   * @return
   */
  PageResponse<ComposeSkuVO> pageSku(ComposeSkuPageQuery query);

  /**
   * 保存采购分页订单
   *
   * @param cmd 入参
   * @return 响应
   */
  SingleResponse<Long> save(PurchaseOrderCmd cmd);

  SingleResponse<Long> sysSave(PurchaseOrderCmd cmd);

  /**
   * 查看采购订单明细
   *
   * @param purchaseId
   * @return
   */
  SingleResponse<PurchaseOrderViewVO> view(Long purchaseId);

  PageResponse<PurchaseOrderDetailVO> pageView(PurchaseOrderDetailPageQuery query);

  /**
   * 付款申请单中-采购明细分页查询
   *
   * @param pageQuery
   * @return
   */
  PageResponse<PurchasePaymentDetailVo> paymentDetailPage(PurchasePaymentDetailPageQuery pageQuery);

  SingleResponse<Boolean> delete(Long purchaseId);

  /**
   * 采购订单相关日志
   *
   * @param purchaseId
   * @return
   */
  MultiResponse<OperateLog> logList(Long purchaseId);

  /**
   * 导出采购订单列表
   *
   * @param query
   * @return
   */
  SingleResponse<Boolean> export(PurchaseOrderPageQuery query);

  SingleResponse<Boolean> exportContact(Long id);

  /**
   * 提交采购订单 开启一个审核流
   *
   * @param purchaseId 主订单id
   * @return
   */
  SingleResponse<Boolean> submit(Long purchaseId);

  /**
   * 审核撤回
   *
   * @param purchaseId
   * @return
   */
  SingleResponse<Boolean> withdrawAudit(Long purchaseId);

  void noticePurchaseOrderCurrentProcessors(
      PurchaseOrder purchaseOrder, List<TaskInstance> taskInstances);

  void toDoSummaryReminder(String qwUserId, List<Long> purchaseOrderIds);

  /**
   * 催办
   *
   * @param purchaseId
   * @return
   */
  SingleResponse<Boolean> urgent(Long purchaseId);

  /**
   * 状态更新 com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PurchaseOrderState
   *
   * @param purchaseOrderId
   * @param purchaseStatus
   * @return
   */
  void updateState(Long purchaseOrderId, Integer purchaseStatus);

  /**
   * 获取此采购订单的应付金额 入库单-出库单 - 已申请的付款单金额
   *
   * @param id
   * @return
   */
  SingleResponse<PaymentDetailAmountVO> getRightPayAmount(Long id, List<Long> choosedDetailIdList);

  SingleResponse<PaymentDetailAmountVO> getRightPayAmount(
      Long id, List<Long> choosedDetailIdList, Boolean advancePayment);

  BigDecimal alreadyPayAmount(Long purchaseId);

  Map<String, PurchaseOrderBizServiceImpl.SkuStockDto> skuRealStockInMap(Long purchaseOrderId);

  Map<String, PurchaseOrderBizServiceImpl.SkuStockDto> skuRealStockOutMap(Long purchaseOrderId);

  Response exportDetail(List<Long> purchaseOrderIdList);

  void taskTimeoutAutoAgreeReminder(PurchaseOrder purchaseOrder, TaskInstance taskInstance);

  /**
   * 根据付款单生成一笔新完结状态的采购订单
   *
   * @param order
   * @param paymentApplyOrderDetailList
   * @return
   */
  PurchaseOrder generateByPaymentApplyOrder(
      PaymentApplyOrder order, List<PaymentApplyOrderDetail> paymentApplyOrderDetailList);

  /**
   * 修改明细的税率信息
   *
   * @param updateTaxDataCmd
   * @return
   */
  SingleResponse<Boolean> updateTaxData(UpdateTaxDataCmd updateTaxDataCmd);

  /**
   * 获取待办列表
   *
   * @return 待办单据ID
   */
  MultiResponse<Long> getTodoList();

  /**
   * 采购订单审核流，状态回调
   *
   * @param cmd
   * @return
   */
  Response callBackOrderState(CallBackOrderStateCmd cmd);

  SingleResponse<Boolean> isExistStockOrder(PurchaseOrderIdCmd cmd);

  /**
   * 已完结的采购单进行出入库时，需要进行一波数量校验 采购单关联到的实际入库-数量不得大于采购单的1.1倍。
   *
   * @param purchaseId
   * @return
   */
  SingleResponse<Boolean> completedQuantityVerification(Long purchaseId);

  PayPurposeEnum getPayPurpose(String purchaseOrderNo);

  /**
   * 发起OA盖章流程
   *
   * @param purchaseOrderId 采购单ID
   */
  void startOASealProcess(Long purchaseOrderId);

  SingleResponse<String> oaSealUrl(Long purchaseOrderId);

  SingleResponse<Boolean> containsCustomizedProducts(Long purchaseOrderId);


}
