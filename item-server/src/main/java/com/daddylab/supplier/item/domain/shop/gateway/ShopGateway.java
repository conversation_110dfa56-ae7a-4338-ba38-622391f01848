package com.daddylab.supplier.item.domain.shop.gateway;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.domain.shop.dto.ShopDropDownItem;
import com.daddylab.supplier.item.domain.shop.dto.ShopDropDownQuery;
import com.daddylab.supplier.item.domain.shop.dto.ShopQuery;
import com.daddylab.supplier.item.domain.shop.entity.ShopEntity;
import com.daddylab.supplier.item.domain.shop.enums.ShopStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Shop;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopPrincipal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface ShopGateway {
    int countShop();

    ShopEntity createOrUpdateShop(ShopEntity shop);

    void createOrUpdateShop(Shop shop);

    boolean addShopPrincipals(Long shopId, List<Long> shopPrincipals);

    boolean removeShopPrincipals(Long shopId, List<Long> shopPrincipals);

    List<Shop> queryShopList(ShopQuery shopQuery);

    Page<Shop> pageQueryShopList(ShopQuery shopQuery);

    List<ShopPrincipal> queryShopPrincipals(Long shopId);

    List<ShopPrincipal> batchQueryShopPrincipals(List<Long> shopIds);

    ShopEntity getShopEntity(Long id);

    void updateShopStatus(Long id, ShopStatus status);

    @Deprecated
    void deleteShop(Long id);

    List<ShopDropDownItem> dropDownList(ShopDropDownQuery query);

    boolean setKingDeeId(Long shopId, String kingDeeId);

    Map<String, Shop> batchQueryShopBySn(Collection<String> shopNos);

    Optional<Shop> queryShopBySn(String shopNo);

    Shop queryShopById(Long shopId);

    Optional<Shop> queryShopByIdOpt(Long shopId);

    /**
     * 保存店铺库存相关设置
     *
     * @param shopId
     * @param cmd
     * @return
     */
//    Boolean saveShopInventorySetting(Long shopId, String shopNo, CreateOrUpdateShopCmd cmd);

    List<Long> selectPrincipalShopIds(Long userId);

//    /**
//     * 保存编辑店铺纬度的库存占比
//     *
//     * @param isVw
//     * @param shopId
//     * @param shopInventory
//     * @param thisWarehouseGoodsList
//     */
//    void saveShopWarehouseGoodsInventory(Boolean isVw, Long shopId, ShopInventory shopInventory, List<InventoryGoodsSaveCmd> thisWarehouseGoodsList);

    Optional<Shop> getByShopNo(String shopNo);

//    Response preDataProcessing(Long shopId);
    
    Optional<Shop> cacheGetByShopNo(String shopNo);
    
    Optional<Shop> cacheGetByMallShopId(Long mallShopId);
    
    
}
