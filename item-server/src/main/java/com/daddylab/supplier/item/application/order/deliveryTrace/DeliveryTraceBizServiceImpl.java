package com.daddylab.supplier.item.application.order.deliveryTrace;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.exportTask.ExportTaskGateway;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderDeliveryTrace;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderDeliveryTraceService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.types.order.DeliveryTracePageQuery;
import com.daddylab.supplier.item.types.order.OrderDeliveryTracePageVO;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/22
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DeliveryTraceBizServiceImpl implements DeliveryTraceBizService {
    private final IOrderDeliveryTraceService orderDeliveryTraceService;
    private final StaffService staffService;
    private final ExportTaskGateway exportTaskGateway;
    private final FileGateway fileGateway;

    @Override
    public PageResponse<OrderDeliveryTracePageVO> pageQuery(DeliveryTracePageQuery query) {
        final LambdaQueryChainWrapper<OrderDeliveryTrace> queryWrapper =
                orderDeliveryTraceService.lambdaQuery();
        final IPage<OrderDeliveryTrace> page = query.getPage();
        queryWrapper
                .eq(
                        StrUtil.isNotEmpty(query.getWarehouseNo()),
                        OrderDeliveryTrace::getWarehouseNo,
                        query.getWarehouseNo())
                .eq(
                        ObjectUtil.isNotEmpty(query.getOrderPersonnelId()),
                        OrderDeliveryTrace::getOrderPersonnelId,
                        query.getOrderPersonnelId())
                .page(page);
        final List<Long> userIds =
                page.getRecords().stream()
                        .map(OrderDeliveryTrace::getOrderPersonnelId)
                        .distinct()
                        .collect(Collectors.toList());
        final Map<Long, DadStaffVO> staffMap = staffService.getStaffMap(userIds);
        return ResponseFactory.ofPage(
                page,
                po -> {
                    final OrderDeliveryTracePageVO orderDeliveryTracePageVO =
                            OrderDeliveryTraceConverter.SHARE.po2pageVO(po);
                    orderDeliveryTracePageVO.setPlatform(po.getPlatform().getValue());
                    Optional.ofNullable(staffMap.get(po.getOrderPersonnelId()))
                            .ifPresent(orderDeliveryTracePageVO::setOrderPersonnel);
                    return orderDeliveryTracePageVO;
                });
    }

    @Override
    public SingleResponse<Boolean> export(DeliveryTracePageQuery query) {
        ExportTask exportTask = new ExportTask();
        exportTask.setStatus(ExportTaskStatus.RUNNING);
        exportTask.setName("订单发货跟踪-" + DateUtil.formatNow(DatePattern.PURE_DATETIME_PATTERN));
        exportTask.setType(ExportTaskType.ORDER_DELIVERY_TRACE);
        final Long taskId = exportTaskGateway.saveOrUpdateExportTask(exportTask);
        exportTask.setId(taskId);

        ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL)
                .execute(
                        () -> {
                            try (final ByteArrayOutputStream byteArrayOutputStream =
                                    new ByteArrayOutputStream()) {
                                query.setPageSize(999999);
                                final PageResponse<OrderDeliveryTracePageVO> response =
                                        pageQuery(query);
                                EasyExcel.write(
                                                byteArrayOutputStream,
                                                OrderDeliveryTracePageVO.class)
                                        .sheet("工作表1")
                                        .doWrite(response.getData());

                                UploadFileAction action =
                                        UploadFileAction.ofInputStream(
                                                new ByteArrayInputStream(
                                                        byteArrayOutputStream.toByteArray()),
                                                exportTask.getName() + ".xlsx");
                                final String downloadUrl = fileGateway.uploadFile(action).getUrl();

                                exportTask.setStatus(ExportTaskStatus.SUCCESS);
                                exportTask.setDownloadUrl(downloadUrl);
                            } catch (Exception e) {
                                log.error("订单发货跟踪异常", e);
                                exportTask.setError(e.getMessage());
                                exportTask.setStatus(ExportTaskStatus.FAIL);
                                exportTask.setError(e.getMessage());
                            } finally {
                                exportTaskGateway.saveOrUpdateExportTask(exportTask);
                            }
                        });
        return SingleResponse.of(true);
    }
}
