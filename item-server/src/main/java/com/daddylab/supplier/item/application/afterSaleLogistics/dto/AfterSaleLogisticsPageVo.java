package com.daddylab.supplier.item.application.afterSaleLogistics.dto;

import com.alibaba.cola.dto.PageResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> up
 * @date 2024年05月23日 9:44 AM
 */
@Data
@ApiModel("订单物流异常查询返回封装")
public class AfterSaleLogisticsPageVo {

  @ApiModelProperty("发货异常数量")
  private Integer consignExceptionNum;

  @ApiModelProperty("发货异常下属系列异常")
  private List<ExceptionDto> consignExceptionList;

  @ApiModelProperty("揽收异常数量")
  private Integer receiveExceptionNum;

  @ApiModelProperty("揽收异常下属系列异常")
  private List<ExceptionDto> receiveExceptionList;

  @ApiModelProperty("发运异常数量")
  private Integer sendExceptionNum;

  @ApiModelProperty("发运异常下属系列异常")
  private List<ExceptionDto> sendExceptionList;

  @ApiModelProperty("派件异常数量")
  private Integer distributeExceptionNum;

  @ApiModelProperty("派件异常下属系列异常")
  private List<ExceptionDto> distributeExceptionList;

  @ApiModelProperty("异常件异常数量")
  private Integer difficultExceptionNum;

  @ApiModelProperty("异常件下属系列异常")
  private List<ExceptionDto> difficultExceptionList;

  @ApiModelProperty("分页数据")
  private PageResponse<AfterSaleLogisticsPageVoItem> pageData;

}
