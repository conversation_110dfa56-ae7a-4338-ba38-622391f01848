//package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;
//
//import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSkuOrderRelation;
//
//import java.util.List;
//
///**
// * <p>
// *  服务类
// * </p>
// *
// * <AUTHOR>
// * @since 2022-04-15
// */
//public interface IWdtSkuOrderRelationService extends IDaddyService<WdtSkuOrderRelation> {
//
//    void batchAddRelation(Long purchaseOrderId, String operateTime, List<WdtOrderDetailWrapper> wrapperList);
//}
