package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.CombinationItemType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.PropertyName;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * @since 2022-01-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CombinationItem implements Serializable {

    private static final long serialVersionUID = -1086441622383650457L;

    @TableId(value = "id", type = IdType.AUTO)
    @DiffIgnore
    private Long id;

    @DiffIgnore
    private Long deletedAt;

    @TableField(fill = FieldFill.INSERT)
    @DiffIgnore
    private Long createdAt;

    @TableField(fill = FieldFill.INSERT)
    @DiffIgnore
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    @DiffIgnore
    private Long updatedAt;

    @TableField(fill = FieldFill.UPDATE)
    @DiffIgnore
    private Long updatedUid;

    @TableLogic
    @DiffIgnore
    private Integer isDel;

    /**
     * 组合商品名字
     */
    @PropertyName("组合商品名称")
    private String name;

    /**
     * 组合商品编码
     */
    @PropertyName("组合商品编码")
    @DiffIgnore
    private String code;

    /**
     * 组合商品条码
     */
    @PropertyName("组合商品条码")
    private String barCode;

    /**
     * 采购成本。10.2
     */
    @PropertyName("采购成本")
    private BigDecimal procurementPrice;

    /**
     * 日常销售价格。10.2
     */
    @PropertyName("销售价格")
    private BigDecimal salesPrice;

    /**
     * 重量。4位小数
     */
    @PropertyName("重量")
    private BigDecimal weight;

    /**
     * 0：普通件。1：普通大件。2：独立大件
     */
    @PropertyName("属性")
    private CombinationItemType type;

    /**
     * 唯一code
     */
    @DiffIgnore
    private String uniqueCode;

    @DiffIgnore
    private Integer isCustomProcurement;

    @DiffIgnore
    private Integer isCustomSales;

    /**
     * 业务线（废弃）
     */
    @PropertyName("业务线")
    @Deprecated
    private Integer businessLine;

    /**
     * 平台佣金
     */
    @PropertyName("平台佣金")
    private BigDecimal platformCommission;

    /**
     * 合同销售价
     */
    @PropertyName("合同销售价")
    private BigDecimal contractSalePrice;

    @PropertyName("供应商ID")
    private Long providerId;
}
