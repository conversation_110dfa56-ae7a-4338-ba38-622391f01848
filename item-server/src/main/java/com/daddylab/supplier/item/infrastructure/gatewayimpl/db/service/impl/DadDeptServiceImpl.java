package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DadDept;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DadDeptMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDadDeptService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 部门表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
@DS("authDb")
@Service
public class DadDeptServiceImpl extends DaddyServiceImpl<DadDeptMapper, DadDept> implements IDadDeptService {

}
