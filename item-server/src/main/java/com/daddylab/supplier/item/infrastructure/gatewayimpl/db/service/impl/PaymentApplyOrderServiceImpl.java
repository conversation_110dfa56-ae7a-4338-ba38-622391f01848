package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.application.payment.PaymentOrderBizServiceImpl;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentApplyOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentApplyOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PaymentOrderStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PaymentApplyOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PaymentApplyOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPaymentApplyOrderDetailService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPaymentApplyOrderService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 采购管理-付款申请单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@Service
public class PaymentApplyOrderServiceImpl extends DaddyServiceImpl<PaymentApplyOrderMapper, PaymentApplyOrder> implements IPaymentApplyOrderService {

    @Resource
    PaymentApplyOrderDetailMapper paymentApplyOrderDetailMapper;

    @Override
    public BigDecimal getAppliedPaymentAmountByRelatedNo(Collection<String> relatedNos) {
        if (CollUtil.isEmpty(relatedNos)) {
            return BigDecimal.ZERO;
        }

        return paymentApplyOrderDetailMapper.getSumApplyAmount(relatedNos,
                PaymentOrderBizServiceImpl.LOCKED_STATUS_LIST.stream().map(PaymentOrderStatus::getValue).collect(Collectors.toList()));
    }



    @Override
    public List<Long> getMatchedRelatedIdByStatus(Collection<PaymentOrderStatus> statusList, Integer detailSource) {
        List<Integer> collect = statusList.stream().map(PaymentOrderStatus::getValue).collect(Collectors.toList());
        return paymentApplyOrderDetailMapper.getMatchedRelatedIdByStatus(detailSource, collect);
    }
}
