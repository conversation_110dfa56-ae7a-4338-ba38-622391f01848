package com.daddylab.supplier.item.domain.exportTask;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> up
 * @date 2022/4/20 10:05 上午
 */
@AllArgsConstructor
@Getter
public enum ExportType {
    /**
     * 不要黄线
     */
    PURCHASE_ORDER_DETAIL("queryPurchaseDetail", "countPurchaseDetailCount", "采购订单sku明细导出"),
    COMBINATION_ITEM("queryCombinationItem", "countCombinationItem", "组合商品明细导出"),

    STOCK_OUT_ORDER("queryStockOutOrder", "stockOutOrderCount", "退料出库商品sku明细导出"),

    SALE_ITEM_LIBRARY("queryExportSaleItemLibrary", "countExportSaleItemLibrary", "销售商品库导出"),

    NEW_GOODS("queryExportNewGoods", "countNewGoods", "新品商品库导出"),
    STOCK_IN_ORDER_DETAIL("queryStockInOrder", "stockInOrderCount", "采购入库明细导出"),

    BACK_ITEM("queryItem", "exportItemAndSkuCount", "后端商品"),

    BACK_ITEM_SKU("querySku", "exportItemAndSkuCount", "后端商品sku"),

    AFTER_SALES_ABNORMAL("queryAfterSalesAbnormalList", "countAfterSalesAbnormalList", "售后信息异常件导出");

    private final String queryMethod;
    private final String countMethod;
    private final String desc;
}
