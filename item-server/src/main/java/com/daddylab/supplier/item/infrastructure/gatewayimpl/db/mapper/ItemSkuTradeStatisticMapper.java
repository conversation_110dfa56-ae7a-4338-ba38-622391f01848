package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSkuTradeStatistic;

/**
 * <p>
 * 商品销售统计 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@DS("bigdata")
public interface ItemSkuTradeStatisticMapper extends DaddyBaseMapper<ItemSkuTradeStatistic> {

}
