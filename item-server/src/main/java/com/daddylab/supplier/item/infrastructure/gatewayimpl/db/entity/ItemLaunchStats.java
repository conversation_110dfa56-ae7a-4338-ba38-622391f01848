package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import static com.daddylab.supplier.item.infrastructure.utils.NumberUtil.isPositive;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品上新统计数据
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemLaunchStats implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品ID
     */
    private Long itemId;

    /**
     * 待完善环节处理人
     */
    private Long toBeImproveProcessorUid;

    /**
     * 待完善环节开始时间
     */
    private Long toBeImproveStartTime;

    /**
     * 待完善环节结束时间
     */
    private Long toBeImproveEndTime;

    /**
     * 待完善环节花费时间
     */
    private Long toBeImproveCostTime;

    /**
     * 待选择 到 待晚上
     *
     * @param time 时间
     */
    public void startToBeImprove(Long time) {
        this.toBeImproveStartTime = time;
    }

    /**
     * 待设计环节处理人
     */
    private Long toBeDesignProcessorUid;

    /**
     * 待设计环节开始时间
     */
    private Long toBeDesignStartTime;

    /**
     * 待设计环节结束时间
     */
    private Long toBeDesignEndTime;

    /**
     * 待设计环节花费时间
     */
    private Long toBeDesignCostTime;

    /**
     * 待完善 到 待设计
     *
     * @param time        时间
     * @param processorId 经办人
     */
    public void startToBeDesign(Long time, Long processorId) {
        this.toBeDesignStartTime = time;
        this.toBeImproveEndTime = time;
        this.toBeImproveProcessorUid = processorId;
        this.toBeImproveCostTime = this.toBeImproveEndTime - this.toBeImproveStartTime;
    }

    /**
     * 待法务审核环节处理人
     */
    private Long toBeLegalAuditProcessorUid;

    /**
     * 待法务审核环节开始时间
     */
    private Long toBeLegalAuditStartTime;

    /**
     * 待法务审核环节结束时间
     */
    private Long toBeLegalAuditEndTime;

    /**
     * 待法务审核环节花费时间
     */
    private Long toBeLegalAuditCostTime;

    /**
     * 从 待设计 进入 待法务审核 阶段
     *
     * @param time        时间
     * @param processorId 经办人
     */
    public void startToBeLegalAudit(Long time, Long processorId) {
        this.toBeLegalAuditStartTime = time;
        this.toBeDesignEndTime = time;
        this.toBeDesignProcessorUid = processorId;
        this.toBeDesignCostTime = this.toBeDesignEndTime - this.toBeDesignStartTime;
    }

    /**
     * 待QC审核环节处理人
     */
    private Long toBeQcAuditProcessorUid;

    /**
     * 待QC审核环节开始时间
     */
    private Long toBeQcAuditStartTime;

    /**
     * 待QC审核环节结束时间
     */
    private Long toBeQcAuditEndTime;

    /**
     * 待QC审核环节花费时间
     */
    private Long toBeQcAuditCostTime;

    /**
     * 从 待法务审核 进入 待QC审核 阶段
     *
     * @param time        时间
     * @param processorId 经办人
     */
    public void startToBeQcAudit(Long time, Long processorId) {
        this.toBeQcAuditStartTime = time;
        this.toBeLegalAuditEndTime = time;
        this.toBeLegalAuditProcessorUid = processorId;
        this.toBeLegalAuditCostTime = this.toBeLegalAuditEndTime - this.toBeLegalAuditStartTime;
    }

    /**
     * 待修改环节处理人
     */
    private Long toBeUpdateProcessorUid;

    /**
     * 待修改环节开始时间
     */
    private Long toBeUpdateStartTime;

    /**
     * 待修改环节结束时间
     */
    private Long toBeUpdateEndTime;

    /**
     * 待修改环节花费时间
     */
    private Long toBeUpdateCostTime;

    /**
     * 待上架环节处理人
     */
    private Long toBeReleaseProcessorUid;

    /**
     * 从 待QC审核 进入 待更新 阶段
     *
     * @param time        时间
     * @param processorId 经办人
     */
    public void startToBeUpdate(Long time, Long processorId) {
        this.toBeUpdateStartTime = time;
    }

    /**
     * 待上架环节开始时间
     */
    private Long toBeReleaseStartTime;

    /**
     * 待上架环节结束时间
     */
    private Long toBeReleaseEndTime;

    /**
     * 待上架环节花费时间
     */
    private Long toBeReleaseCostTime;

    /**
     * 从 待更新 进入 待上架 阶段
     *
     * @param time        时间
     * @param processorId 经办人
     */
    public void startToBeRelease(Long time, Long processorId) {
        this.toBeReleaseStartTime = time;
        this.toBeUpdateEndTime = time;
        this.toBeUpdateProcessorUid = processorId;
        this.toBeUpdateCostTime = this.toBeUpdateEndTime - this.toBeUpdateStartTime;
    }

    /**
     * 已上架
     */
    public void released(Long time) {
        this.toBeReleaseEndTime = time;
        this.launchTime = time;
        this.toBeReleaseCostTime = this.toBeReleaseEndTime - this.toBeReleaseStartTime;
        this.costTime = this.launchTime - this.toBeImproveStartTime;
    }

    /**
     * 上架时间
     */
    private Long launchTime;

    /**
     * 上新总花费时间
     */
    private Long costTime;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 修正审核统计信息
     */
    public void setAuditStat(
            Long legalProcessorId, Long legalAuditStartTime,
            Long legalAuditEndTime, Long qcProcessorId, Long qcAuditStartTime,
            Long qcAuditEndTime
    ) {
        if (isPositive(legalProcessorId)) {
            this.toBeLegalAuditProcessorUid = legalProcessorId;
            this.toBeLegalAuditStartTime = legalAuditStartTime;
            this.toBeLegalAuditEndTime = legalAuditEndTime;
            this.toBeLegalAuditCostTime =
                    isPositive(toBeLegalAuditEndTime) && isPositive(toBeLegalAuditStartTime) ?
                            toBeLegalAuditEndTime - toBeLegalAuditStartTime : 0;
        }

        if (isPositive(qcProcessorId)) {
            this.toBeQcAuditProcessorUid = qcProcessorId;
            this.toBeQcAuditStartTime = qcAuditStartTime;
            this.toBeQcAuditEndTime = qcAuditEndTime;
            this.toBeQcAuditCostTime =  isPositive(toBeQcAuditEndTime) && isPositive(toBeQcAuditStartTime) ?
                    toBeQcAuditEndTime - toBeQcAuditStartTime : 0;
        }
    }

}
