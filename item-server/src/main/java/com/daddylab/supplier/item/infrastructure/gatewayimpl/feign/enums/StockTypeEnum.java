package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @class StockTypeEnum.java
 * @description 描述类的作用
 * @date 2024-03-04 10:59
 */
@AllArgsConstructor
@Getter
public enum StockTypeEnum implements IEnum<String> {
    INCREASE("增加"),
    DECREASE("减少"),
    EDIT("改为"),
    ;
    private final String value;

}
