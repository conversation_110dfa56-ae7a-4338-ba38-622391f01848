package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.domain.shop.dto.ShopDropDownItem;
import com.daddylab.supplier.item.domain.shop.dto.ShopDropDownQuery;
import com.daddylab.supplier.item.domain.shop.dto.ShopQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Shop;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 店铺 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Repository
public interface ShopMapper extends DaddyBaseMapper<Shop> {
    List<Shop> queryShopList(ShopQuery shopQuery);

    int countShopList(ShopQuery shopQuery);

    List<ShopDropDownItem> dropDownList(ShopDropDownQuery shopQuery);

}
