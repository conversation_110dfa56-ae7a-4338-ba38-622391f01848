package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/13
 */
@Data
@EqualsAndHashCode(of = "value")
public class CascadedDivisionLevel {
    private final DivisionLevelValueEnum value;
    private List<CascadedDivisionLevel> subValues;

    public CascadedDivisionLevel(Integer value) {
        this.value = IEnum.getEnumByValue(DivisionLevelValueEnum.class, value);
    }

    public CascadedDivisionLevel(DivisionLevelValueEnum value) {
        this.value = value;
    }

    public CascadedDivisionLevel(
            DivisionLevelValueEnum value, List<CascadedDivisionLevel> subValues) {
        this.value = value;
        if (subValues != null) {
            this.subValues = new ArrayList<>(subValues);
        }
    }

    public CascadedDivisionLevel addSubValue(CascadedDivisionLevel subValue) {
        if (this.subValues == null) {
            this.subValues = new ArrayList<>();
        }
        subValues.add(subValue);
        return this;
    }

    public CascadedDivisionLevel addSubValue(DivisionLevelValueEnum subValue) {
        if (this.subValues == null) {
            this.subValues = new ArrayList<>();
        }
        subValues.add(new CascadedDivisionLevel(subValue));
        return this;
    }

    public CascadedDivisionLevel addSubValue(Integer subValue) {
        if (this.subValues == null) {
            this.subValues = new ArrayList<>();
        }
        subValues.add(new CascadedDivisionLevel(IEnum.getEnumByValue(DivisionLevelValueEnum.class, subValue)));
        return this;
    }
}
