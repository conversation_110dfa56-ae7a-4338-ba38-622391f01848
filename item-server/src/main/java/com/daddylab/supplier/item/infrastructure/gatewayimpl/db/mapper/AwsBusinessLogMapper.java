package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AwsBusinessLog;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 炎黄盈动业务日志 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
public interface AwsBusinessLogMapper extends DaddyBaseMapper<AwsBusinessLog> {

    /**
     * 获取流程业务绑定记录（忽略逻辑删除）
     * @param processInstId 流程实例ID
     */
    AwsBusinessLog getByProcessInstId(@Param("processInstId") String processInstId);
}
