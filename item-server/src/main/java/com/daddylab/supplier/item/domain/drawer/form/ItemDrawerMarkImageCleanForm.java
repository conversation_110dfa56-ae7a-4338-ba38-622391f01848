package com.daddylab.supplier.item.domain.drawer.form;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
public class ItemDrawerMarkImageCleanForm implements Serializable {

    private static final long serialVersionUID = 51124111497446252L;

    @ApiModelProperty(name = "商品ID")
    private Long itemId;

    @ApiModelProperty(name = "清空全部（否则只清除最后一条）")
    private Boolean cleanAll;
}
