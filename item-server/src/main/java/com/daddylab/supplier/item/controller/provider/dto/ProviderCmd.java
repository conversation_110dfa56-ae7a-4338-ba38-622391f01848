package com.daddylab.supplier.item.controller.provider.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.PropertyName;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/28 10:58 上午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("供应商新增实体")
public class ProviderCmd extends Command {

    private static final long serialVersionUID = 6461979704925910496L;
    @ApiModelProperty(value = "id。有值：更新，无值：新增", example = "1")
    private Long id;

    @ApiModelProperty(value = "供应商编号")
    private String providerNo;

    @ApiModelProperty(value = "合作系统中的供应商id")
    private String partnerProviderId;

    @ApiModelProperty("供应商名称")
    @NotBlank(message = "供应商名称不得为空")
    private String name;

    @ApiModelProperty("社会统一信息代码")
    @NotBlank(message = "社会统一信息代码不得为空")
    private String unifySocialCreditCodes;

    @ApiModelProperty(value = "供应商类型。1自营。2代发。3商家入驻")
    @NotNull(message = "供应商类型不得为空")
    private Integer type;

    @ApiModelProperty("联系人")
    private String contact;

    @ApiModelProperty("联系方式")
    @Length(max = 11, message = "联系方式不得大于11位")
    private String contactMobile;

    @ApiModelProperty("省份code")
    private String provinceCode;

    @ApiModelProperty("城市code")
    private String cityCode;

    @ApiModelProperty("区县code")
    private String areaCode;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("状态，1:合作。2:停用")
    private Integer status = 1;

    @ApiModelProperty("发票类型(1:增值税专用发票 2:普通发票)")
    private Integer invoiceType;

    @ApiModelProperty("结算币别(1:人民币 2:美金)")
    @NotNull(message = "结算币别不得为空")
    private Integer currency = 1;

    @ApiModelProperty("税分类(1:一般纳税人 2:小规模纳税人)")
    @NotNull(message = "税分类不得为空")
    private Integer taxType = 1;

    @ApiModelProperty("税率")
    private BigDecimal taxRate;

    @Valid
    @ApiModelProperty("银行账户")
    @NotEmpty(message = "银行账户信息不得为空")
    private List<BankAccountVO> bankAccountVOList;

    @PropertyName("负责人")
    private Long mainChargerUserId;

    @PropertyName("次要负责人")
    private Long secondChargerUserId;

    /**
     * 商家后台，店铺ID
     */
    @DiffIgnore
    @ApiModelProperty("商家后台，店铺ID")
    private String mallShopId;

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate.setScale(6, RoundingMode.UP);
    }

    private String codeStr;

    @ApiModelProperty("合作方")
    private List<Integer> corpType;
}
