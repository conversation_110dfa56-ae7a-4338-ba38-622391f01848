package com.daddylab.supplier.item.application.purchasePayable.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.format.FastDateFormat;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.ExceptionFactory;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.application.organazation.OrganizationService;
import com.daddylab.supplier.item.application.otherPay.OtherPayBizService;
import com.daddylab.supplier.item.application.provider.ProviderBizService;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizService;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderFeatureService;
import com.daddylab.supplier.item.application.purchase.order.factory.ErpPurchaseOrderHandler;
import com.daddylab.supplier.item.application.purchasePayable.PurchasePayableConstant;
import com.daddylab.supplier.item.application.purchasePayable.dto.*;
import com.daddylab.supplier.item.application.purchasePayable.enums.PurchasePayableTimeTypeEnum;
import com.daddylab.supplier.item.application.purchasePayable.query.ApplyOrderQueryPage;
import com.daddylab.supplier.item.application.purchasePayable.query.ApplyPayOrderDetailListQueryPage;
import com.daddylab.supplier.item.application.purchasePayable.query.PurchasePayableQueryPage;
import com.daddylab.supplier.item.application.purchasePayable.query.PurchasePayableVoucherQuery;
import com.daddylab.supplier.item.application.purchasePayable.service.PurchasePayableBizService;
import com.daddylab.supplier.item.application.purchasePayable.vo.*;
import com.daddylab.supplier.item.application.stockInOrder.StockInOrderBizService;
import com.daddylab.supplier.item.application.stockOutOrder.StockOutOrderBizService;
import com.daddylab.supplier.item.application.warehouse.WarehouseBizService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.common.trans.PurchasePayableTransMapper;
import com.daddylab.supplier.item.common.trans.StockInOrderTransMapper;
import com.daddylab.supplier.item.controller.otherPay.dto.OtherPayVo;
import com.daddylab.supplier.item.controller.provider.dto.ProviderVO;
import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderCmd;
import com.daddylab.supplier.item.controller.stockout.dto.StockOutOrderCmd;
import com.daddylab.supplier.item.controller.stockout.dto.StockOutOrderDetailVO;
import com.daddylab.supplier.item.controller.stockout.dto.StockOutOrderViewVO;
import com.daddylab.supplier.item.domain.auth.enums.ResourceType;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetail;
import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderDetailForPurchasePayableVO;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchasePayableApplyOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqJsonUtil;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 采购应付表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Service
@Slf4j
public class PurchasePayableBizServiceImpl implements PurchasePayableBizService {

    /**
     * 采购应付单号后续递增部分
     */
    private static final String PURCHASE_PAYABLE_REDIS_KEY = "purchasePayableNo:incrementPart";
    @Autowired
    private IPurchasePayableOrderService purchasePayableOrderService;
    @Autowired
    private ProviderBizService providerService;
    @Autowired
    private ProviderGateway providerGateway;
    @Autowired
    private StockInOrderBizService stockInOrderBizService;
    @Autowired
    private StockOutOrderBizService stockOutOrderBizService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private OtherPayBizService otherPayBizService;
    @Autowired
    private WarehouseBizService warehouseBizService;

    @Resource
    private IStockInOrderService iStockInOrderService;

    @Resource
    private IStockInOrderDetailService iStockInOrderDetailService;

    @Resource
    private IStockOutOrderService iStockOutOrderService;

    @Resource
    private IStockOutOrderDetailService iStockOutOrderDetailService;

    @Resource
    private IPurchaseOrderService iPurchaseOrderService;

    @Resource
    PurchaseOrderMapper purchaseOrderMapper;

    @Resource
    private IPurchaseOrderDetailService iPurchaseOrderDetailService;

    @Resource
    private PurchaseOrderBizService purchaseOrderBizService;

    @Resource
    PurchaseOrderFeatureService purchaseOrderFeatureService;

    @Resource
    IPurchasePayableOrderRelationService iPurchasePayableOrderRelationService;

    @Resource
    IPurchasePayableApplyOrderService iPurchasePayableApplyOrderService;

    @Resource
    IPurchasePayableApplyOrderDetailService iPurchasePayableApplyOrderDetailService;

    @Resource
    PurchasePayableApplyOrderDetailMapper purchasePayableApplyOrderDetailMapper;

    @Resource
    IPurchasePayableOrderService iPurchasePayableOrderService;

    @Resource
    IItemSkuService iItemSkuService;

    @Resource
    ReqTemplate kingDeeReqTemplate;

    @Resource
    KingDeeTemplate kingDeeTemplate;

    @Resource
    ReqJsonUtil kingDeeJsonUtil;

    @Resource
    ItemSkuGateway itemSkuGateway;

    @Resource
    IItemService itemService;

    @Resource
    IAdditionalService iAdditionalService;


    @Resource
    IPayApplyAuditProcessService iPayApplyAuditProcessService;

    @Resource
    IPayApplyAuditProcessNodeService iPayApplyAuditProcessNodeService;

    @Override
    public PageResponse<PurchasePayableVO> queryPage(PurchasePayableQueryPage queryPage) {
        log.info("分页查询采购应付 queryPage:{}", queryPage);
        queryPage.setBusinessLine(UserPermissionJudge.filterBusinessLinePerm(queryPage.getBusinessLine()));

//        if (!UserContext
//                .hasPermission(GlobalConstant.PURCHASE_PAYABLE_ORDER_URI, ResourceType.API)) {
//            queryPage.setBuyerId(UserContext.getUserId());
//        }
//
//        // 是否有查看系统冲销单的权限
//        queryPage.setSeeHedge(UserContext.hasPermission(GlobalConstant.VIEW_PAYMENT_WRITE_OFF, ResourceType.API));

        getRelatedOrderNo(queryPage.getPurchaseId(), queryPage);

        QueryWrapper<PurchasePayableOrder> queryWrapper = setPageParams(queryPage);
        Page<PurchasePayableOrder> pageReq = new Page<>(queryPage.getPageIndex(),
                queryPage.getPageSize());
        Page<PurchasePayableOrder> page = purchasePayableOrderService.page(pageReq, queryWrapper);
        List<PurchasePayableVO> purchasePayableVOList = PurchasePayableTransMapper.INSTANCE
                .listToVO(page.getRecords());

        if (CollectionUtil.isNotEmpty(purchasePayableVOList)) {
            final List<Long> providerIds = purchasePayableVOList.stream().map(PurchasePayableVO::getProviderId).collect(Collectors.toList());
            final List<Provider> providers = providerGateway.queryBatchByIds(providerIds);
            final Map<Long, Provider> providersMap = providers.stream().collect(Collectors.toMap(Entity::getId, Function.identity()));
            final List<Long> partnerProviderIds = providers.stream().map(Provider::getPartnerProviderId).collect(Collectors.toList());
            final Map<Long, PartnerProviderResp> longPartnerProviderRespMap = providerGateway.partnerBatchQueryByIds(partnerProviderIds);

            purchasePayableVOList.forEach(
                    purchasePayableVO -> {
                        purchasePayableVO.setTypeName(
                                PurchaseTypeEnum.getNameByCode(purchasePayableVO.getType()));

                        final Long providerId = purchasePayableVO.getProviderId();
                        final Provider provider = providersMap.get(providerId);
                        String providerName = provider.getName();
                        purchasePayableVO.setProviderName(providerName);

                        final Long partnerProviderId = provider.getPartnerProviderId();
                        purchasePayableVO.setIsBlacklist(0);
                        if (partnerProviderId != null && partnerProviderId > 0) {
                            final Optional<PartnerProviderResp> partnerProviderResp =
                                    Optional.ofNullable(
                                            longPartnerProviderRespMap.get(partnerProviderId));
                            partnerProviderResp.ifPresent(
                                    v -> purchasePayableVO.setIsBlacklist(v.getIsBlacklist()));
                        }

                        String organizationName =
                                getOrganizationName(purchasePayableVO.getOrganizationId());
                        purchasePayableVO.setOrganizationName(organizationName);

                        // 应付单是否允许生成付款申请单判断
                        boolean canApply = true;
                        // 已经生成付款申请单的sku的数量，并且付款申请单的状态不为 处理异常
                        Integer applySkuCode =
                                purchasePayableApplyOrderDetailMapper
                                        .countSkuCountByPurchasePayableOrderNo(
                                                purchasePayableVO.getNo());
                        Integer skuCode = 0;
                        if (purchasePayableVO
                                .getType()
                                .equals(PurchaseTypeEnum.IN_STOCK_PAYABLE.getValue())) {
                            // 入库单sku数量
                            skuCode =
                                    iStockInOrderDetailService
                                            .lambdaQuery()
                                            .eq(
                                                    StockInOrderDetail::getStockInOrderId,
                                                    purchasePayableVO.getRelatedOrderId())
                                            .count();
                        }
                        if (purchasePayableVO
                                .getType()
                                .equals(PurchaseTypeEnum.OUT_STOCK_PAYABLE.getValue())) {
                            // 处库单sku数量
                            skuCode =
                                    iStockOutOrderDetailService
                                            .lambdaQuery()
                                            .eq(
                                                    StockOutOrderDetail::getStockOutOrderId,
                                                    purchasePayableVO.getRelatedOrderId())
                                            .count();
                        }
                        if (applySkuCode >= skuCode) {
                            canApply = false;
                        }
                        purchasePayableVO.setCanApplyPay(canApply);

                        // 关联的出入库单类型是否是冲销单据
//                        Integer count =
//                                iPurchasePayableOrderRelationService
//                                        .lambdaQuery()
//                                        .eq(
//                                                PurchasePayableOrderRelation::getRelateStockOrderNo,
//                                                purchasePayableVO.getRelatedOrderNo())
//                                        .count();
//                        if (count > 0) {
//                            purchasePayableVO.setIsHedgeData(1);
//                        } else {
//                            purchasePayableVO.setIsHedgeData(0);
//                        }
                    });
        }
        return PageResponse
                .of(purchasePayableVOList, Convert.toInt(page.getTotal()), queryPage.getPageSize(),
                        queryPage.getPageIndex());
    }


    @Override
    public SingleResponse<PurchasePayableVoucherVO> voucher(PurchasePayableVoucherQuery query) {
        log.info("应付凭证 queryPage:{}", query);

        if (!UserContext
                .hasPermission(GlobalConstant.PURCHASE_PAYABLE_ORDER_URI, ResourceType.API)) {
            query.setBuyerId(UserContext.getUserId());
        }

        QueryWrapper<PurchasePayableOrder> queryWrapper = setVoucherParams(query);
        List<PurchasePayableOrder> purchasePayableOrderList = purchasePayableOrderService
                .list(queryWrapper);
        if (CollectionUtil.isNotEmpty(purchasePayableOrderList)) {
            final PurchasePayableVoucherVO purchasePayableVoucherVO = convertToVO(
                    purchasePayableOrderList);
            return SingleResponse.of(purchasePayableVoucherVO);
        }
        return SingleResponse.of(null);
    }


    @Override
    public Response create(PurchasePayableCmd cmd) {
        Assert.notNull(cmd.getType(), "采购类型不得为空");
        Assert.notNull(cmd.getRelatedOrderId(), "关联单Id不得为空");

        QueryWrapper<PurchasePayableOrder> queryWrapper = set(cmd.getRelatedOrderId(),
                cmd.getType());
        List<PurchasePayableOrder> purchasePayableOrderList = purchasePayableOrderService
                .list(queryWrapper);
        if (CollectionUtil.isNotEmpty(purchasePayableOrderList)) {
            log.error("生成采购应付单重复调用");
            return Response.buildSuccess();
        }

        PurchasePayableOrder purchasePayable = PurchasePayableTransMapper.INSTANCE.cmdToDO(cmd);
        purchasePayable
                .setNo(createNo(Objects.requireNonNull(PurchaseTypeEnum.getByCode(cmd.getType()))));
        if (Objects.equals(PurchaseTypeEnum.IN_STOCK_PAYABLE.getValue(), cmd.getType())) {
            assembleStockInOrder(cmd.getRelatedOrderId(), purchasePayable);

        } else if (Objects.equals(PurchaseTypeEnum.OUT_STOCK_PAYABLE.getValue(), cmd.getType())) {
            assembleStockOutOrder(cmd.getRelatedOrderId(), purchasePayable);

        } else if (Objects.equals(PurchaseTypeEnum.OTHER_PAYABLE.getValue(), cmd.getType())) {
            assembleOtherPay(cmd.getRelatedOrderId(), purchasePayable);
        } else {
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "类型不存在");
        }
        try {
            purchasePayableOrderService.save(purchasePayable);
        } catch (Exception e) {
            log.error("purchasePayableOrderService save error.purchasePayable:{}", JsonUtil.toJson(purchasePayable), e);
            return Response.buildFailure(ErrorCode.FILE_ERROR.getCode(), "保存付款单失败");
        }
        return Response.buildSuccess();
    }


    @Override
    public SingleResponse<PurchasePayableDetailsVO> details(Long id, String no) {
        log.info("采购应付单明细 id:{},no:{}", id, no);

        PurchasePayableDetailsVO purchasePayableDetailsVO = new PurchasePayableDetailsVO();
        PurchasePayableOrder purchasePayableOrder;
        if (Objects.nonNull(id)) {
            purchasePayableOrder = purchasePayableOrderService.getById(id);
            Assert.notNull(purchasePayableOrder, "采购应付单查询为空，id错误");
        } else if (StrUtil.isNotBlank(no)) {
            Optional<PurchasePayableOrder> optional = purchasePayableOrderService.lambdaQuery()
                    .eq(PurchasePayableOrder::getNo, no).oneOpt();
            Assert.isTrue(optional.isPresent(), "采购应付单查询为空，编号错误。" + no);
            purchasePayableOrder = optional.get();
        } else {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "采购应付单查询参数异常，id或者编码均为空");
        }

        BeanUtil.copyProperties(purchasePayableOrder, purchasePayableDetailsVO);
        purchasePayableDetailsVO.setTypeName(PurchaseTypeEnum.getNameByCode(purchasePayableOrder.getType()));

        final Long providerId = purchasePayableOrder.getProviderId();
        final Provider provider = providerGateway.getById(providerId);
        purchasePayableDetailsVO.setProviderName(provider.getName());
        final Long partnerProviderId = provider.getPartnerProviderId();
        purchasePayableDetailsVO.setIsBlacklist(0);
        if (partnerProviderId != null && partnerProviderId > 0) {
            providerGateway
                    .partnerQueryById(partnerProviderId)
                    .ifPresent(v -> purchasePayableDetailsVO.setIsBlacklist(v.getIsBlacklist()));
        }
        String organizationName = getOrganizationName(purchasePayableOrder.getOrganizationId());
        if (StringUtils.isNotBlank(organizationName)) {
            purchasePayableDetailsVO.setOrganizationName(organizationName);
        }

        if (Objects.equals(PurchaseTypeEnum.IN_STOCK_PAYABLE.getValue(),
                purchasePayableOrder.getType())) {
            StockInOrderAndOrderDetail stockInOrderAndOrderDetail = stockInOrderBizService
                    .stockInOrderAndOrderDetailQuery(purchasePayableOrder.getRelatedOrderId());
            if (Objects.nonNull(stockInOrderAndOrderDetail)) {
                List<StockInOrderDetail> stockInOrderDetails = stockInOrderAndOrderDetail
                        .getStockInOrderDetails();
                List<StockInOrderDetailForPurchasePayableVO> stockInOrderDetailForPurchasePayableVOS = StockInOrderTransMapper.INSTANCE
                        .listDoToVo(stockInOrderDetails);
                if (CollectionUtil.isNotEmpty(stockInOrderDetailForPurchasePayableVOS)) {
                    stockInOrderDetailForPurchasePayableVOS.forEach(e -> {
                        Optional<Warehouse> warehouse = warehouseBizService
                                .getWarehouse(e.getWarehouseNo());
                        e.setWarehouseName(
                                warehouse.isPresent() ? warehouse.get().getName() : null);
                    });
                }
                purchasePayableDetailsVO
                        .setStockInOrderDetails(stockInOrderDetailForPurchasePayableVOS);
            }
        }

        if (Objects.equals(PurchaseTypeEnum.OUT_STOCK_PAYABLE.getValue(),
                purchasePayableOrder.getType())) {
            StockOutOrderViewVO stockOutOrderViewVO = stockOutOrderBizService
                    .getStockOutOrder(purchasePayableOrder.getRelatedOrderId()).getData();
            if (Objects.nonNull(stockOutOrderViewVO)) {
                purchasePayableDetailsVO.setStockOutOrderDetailList(
                        stockOutOrderViewVO.getStockOutOrderDetailList());
            }
        }

        if (Objects.equals(PurchaseTypeEnum.OTHER_PAYABLE.getValue(),
                purchasePayableOrder.getType())) {
            OtherPayVo otherPayVo = otherPayBizService
                    .getById(purchasePayableOrder.getRelatedOrderId()).getData();
            if (Objects.nonNull(otherPayVo)) {
                purchasePayableDetailsVO.setOtherPayDetails(otherPayVo.getOtherPayDetails());
            }
        }
        return SingleResponse.of(purchasePayableDetailsVO);
    }


    @Override
    public Response delete(PurchasePayableCmd cmd) {
        if (Objects.equals(PurchaseTypeEnum.OTHER_PAYABLE.getValue(), cmd.getType())) {
            QueryWrapper<PurchasePayableOrder> queryWrapper = set(cmd.getRelatedOrderId(),
                    cmd.getType());
            List<PurchasePayableOrder> otherPayList = purchasePayableOrderService
                    .list(queryWrapper);
            if (CollectionUtil.isNotEmpty(otherPayList)) {
                PurchasePayableOrder otherPayPurchasePayableOrder = otherPayList.stream()
                        .findFirst().get();
                purchasePayableOrderService.removeById(otherPayPurchasePayableOrder.getId());
            }
        }
        return Response.buildSuccess();
    }


    private void getRelatedOrderNo(Long purchaseId, PurchasePayableQueryPage queryPage) {
        if (Objects.nonNull(purchaseId)) {
            List<String> relatedOrderNoList = Lists.newArrayList();
            //查询关联单据（入库单、出库单）
            SingleResponse<List<StockInOrder>> singleResponse = stockInOrderBizService
                    .stockInOrderList(purchaseId);
            List<StockInOrder> stockInOrders = singleResponse.getData();
            if (CollectionUtil.isNotEmpty(stockInOrders)) {
                stockInOrders.forEach(e -> relatedOrderNoList.add(e.getNo()));
            }
            List<StockOutOrder> stockOutOrders = stockOutOrderBizService
                    .getStockOutOrderByPurchaseOrderId(purchaseId);
            if (CollectionUtil.isNotEmpty(stockOutOrders)) {
                stockOutOrders.forEach(e -> relatedOrderNoList.add(e.getNo()));
            }
            queryPage.setRelatedOrderNoList(relatedOrderNoList);
        }
    }


    /**
     * 查询供应商信息
     *
     * @param providerId 供应商ID
     * @deprecated
     */
    @Deprecated
    private String getProviderName(Long providerId) {
        ProviderVO providerVO = providerService.queryDetail(providerId).getData();
        return Optional.ofNullable(providerVO).map(ProviderVO::getName).orElse(null);
    }


    /**
     * 查询采购组织
     *
     * @param organizationId 采购组织ID
     */
    private String getOrganizationName(Long organizationId) {
        Organization organization = organizationService.getById(organizationId).getData();
        return Optional.ofNullable(organization).map(Organization::getName).orElse(null);
    }


    /**
     * 业务转换
     */
    private PurchasePayableVoucherVO convertToVO(
            List<PurchasePayableOrder> purchasePayableOrderList) {
        PurchasePayableVoucherVO purchasePayableVoucherVO = new PurchasePayableVoucherVO();

        PurchasePayableOrder purchasePayableOrder = purchasePayableOrderList.stream().findFirst()
                .orElseThrow(() -> ExceptionPlusFactory
                        .bizException(ErrorCode.SYS_ERROR, "输入的采购应付单数据为空，无法正常生成采购凭证数据，请确认程序逻辑"));
        String providerName = getProviderName(purchasePayableOrder.getProviderId());
        if (StringUtils.isNotBlank(providerName)) {
            purchasePayableVoucherVO.setProviderName(providerName);
        }
        String organizationName = getOrganizationName(purchasePayableOrder.getOrganizationId());
        if (StringUtils.isNotBlank(organizationName)) {
            purchasePayableVoucherVO.setOrganizationName(organizationName);
        }

        purchasePayableVoucherVO.setCreateDate(DateUtil.currentTime());

        int countTotalItemQuantity = purchasePayableOrderList.stream()
                .mapToInt(PurchasePayableOrder::getTotalItemQuantity).sum();
        purchasePayableVoucherVO.setTotalItemQuantity(countTotalItemQuantity);

        BigDecimal countTotalPriceTax = purchasePayableOrderList.stream()
                .map(PurchasePayableOrder::getTotalPriceTax)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        purchasePayableVoucherVO.setTotalPriceTax(countTotalPriceTax);

        BigDecimal countTotalAfterTaxItemAmount = purchasePayableOrderList.stream()
                .map(PurchasePayableOrder::getTotalAfterTaxItemAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        purchasePayableVoucherVO.setTotalAfterTaxItemAmount(countTotalAfterTaxItemAmount);

        BigDecimal countTotalTaxAmount = purchasePayableOrderList.stream()
                .map(PurchasePayableOrder::getTotalTaxAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        purchasePayableVoucherVO.setTotalTaxAmount(countTotalTaxAmount);

        List<PurchasePayableVoucherItemDetailsVO> tempVoucherItemDetailsList = Lists.newArrayList();
        tempStockInOrderList(purchasePayableOrderList, tempVoucherItemDetailsList);
        tempStockOutOrderList(purchasePayableOrderList, tempVoucherItemDetailsList);
        calculate(tempVoucherItemDetailsList, purchasePayableVoucherVO);
        return purchasePayableVoucherVO;
    }


    private void tempStockInOrderList(List<PurchasePayableOrder> purchasePayableOrderList,
                                      List<PurchasePayableVoucherItemDetailsVO> tempVoucherItemDetailsList) {
        List<PurchasePayableOrder> tempStockInOrderList = purchasePayableOrderList.stream()
                .filter(e -> Objects
                        .equals(PurchaseTypeEnum.IN_STOCK_PAYABLE.getValue(), e.getType()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(tempStockInOrderList)) {
            tempStockInOrderList.forEach(e -> {
                StockInOrderAndOrderDetail stockInOrderAndOrderDetail = stockInOrderBizService
                        .stockInOrderAndOrderDetailQuery(e.getRelatedOrderId());
                if (Objects.nonNull(stockInOrderAndOrderDetail)) {
                    List<StockInOrderDetail> stockInOrderDetails = stockInOrderAndOrderDetail
                            .getStockInOrderDetails();
                    if (CollectionUtil.isNotEmpty(stockInOrderDetails)) {
                        stockInOrderDetails.forEach(stockInOrderDetail -> {
                            PurchasePayableVoucherItemDetailsVO purchasePayableVoucherItemDetailsVO = new PurchasePayableVoucherItemDetailsVO();
                            BeanUtil.copyProperties(stockInOrderDetail,
                                    purchasePayableVoucherItemDetailsVO);
                            purchasePayableVoucherItemDetailsVO
                                    .setAppleQuantity(stockInOrderDetail.getPurchaseQuantity());
                            purchasePayableVoucherItemDetailsVO.setPayableQuantity(
                                    stockInOrderDetail.getRealReceiptQuantity());
                            purchasePayableVoucherItemDetailsVO
                                    .setType(PurchaseTypeEnum.IN_STOCK_PAYABLE.getValue());
                            tempVoucherItemDetailsList.add(purchasePayableVoucherItemDetailsVO);
                        });
                    }
                }
            });
        }
    }


    private void tempStockOutOrderList(List<PurchasePayableOrder> purchasePayableOrderList,
                                       List<PurchasePayableVoucherItemDetailsVO> tempVoucherItemDetailsList) {
        List<PurchasePayableOrder> tempStockOutOrderList = purchasePayableOrderList.stream()
                .filter(e -> Objects
                        .equals(PurchaseTypeEnum.OUT_STOCK_PAYABLE.getValue(), e.getType()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(tempStockOutOrderList)) {
            tempStockOutOrderList.forEach(e -> {
                StockOutOrderViewVO stockOutOrderViewVO = stockOutOrderBizService
                        .getStockOutOrder(e.getRelatedOrderId()).getData();
                if (Objects.nonNull(stockOutOrderViewVO)) {
                    List<StockOutOrderDetailVO> stockOutOrderDetailList = stockOutOrderViewVO
                            .getStockOutOrderDetailList();
                    if (CollectionUtil.isNotEmpty(stockOutOrderDetailList)) {
                        stockOutOrderDetailList.forEach(stockOutOrderDetail -> {
                            PurchasePayableVoucherItemDetailsVO purchasePayableVoucherItemDetailsVO = new PurchasePayableVoucherItemDetailsVO();
                            BeanUtil.copyProperties(stockOutOrderDetail,
                                    purchasePayableVoucherItemDetailsVO);
                            purchasePayableVoucherItemDetailsVO
                                    .setAppleQuantity(stockOutOrderDetail.getReturnQuantity());
                            purchasePayableVoucherItemDetailsVO.setPayableQuantity(
                                    stockOutOrderDetail.getRealReturnQuantity());
                            purchasePayableVoucherItemDetailsVO
                                    .setType(PurchaseTypeEnum.OUT_STOCK_PAYABLE.getValue());
                            tempVoucherItemDetailsList.add(purchasePayableVoucherItemDetailsVO);
                        });
                    }
                }
            });
        }
    }


    private void calculate(List<PurchasePayableVoucherItemDetailsVO> tempVoucherItemDetailsList,
                           PurchasePayableVoucherVO purchasePayableVoucherVO) {
        List<PurchasePayableVoucherItemDetailsVO> voucherItemDetailsList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(tempVoucherItemDetailsList)) {
            Map<String, List<PurchasePayableVoucherItemDetailsVO>> tempMap = tempVoucherItemDetailsList
                    .stream().collect(Collectors
                            .groupingBy(PurchasePayableVoucherItemDetailsVO::getItemSkuCode));
            tempMap.forEach((k, v) -> {
                PurchasePayableVoucherItemDetailsVO voucherItemDetailsVO = new PurchasePayableVoucherItemDetailsVO();
                voucherItemDetailsVO.setType(v.get(0).getType());
                voucherItemDetailsVO.setItemSkuCode(k);
                voucherItemDetailsVO.setItemName(v.get(0).getItemName());
                voucherItemDetailsVO.setSpecifications(v.get(0).getSpecifications());

                int countInStockAppleQuantity = v.stream().filter(vo -> Objects
                                .equals(PurchaseTypeEnum.IN_STOCK_PAYABLE.getValue(), vo.getType()))
                        .mapToInt(PurchasePayableVoucherItemDetailsVO::getAppleQuantity).sum();
                int countOutStockAppleQuantity = v.stream().filter(vo -> Objects
                                .equals(PurchaseTypeEnum.OUT_STOCK_PAYABLE.getValue(), vo.getType()))
                        .mapToInt(PurchasePayableVoucherItemDetailsVO::getAppleQuantity).sum();
                voucherItemDetailsVO
                        .setAppleQuantity(countInStockAppleQuantity - countOutStockAppleQuantity);

                int countTnStockPayableQuantity = v.stream().filter(vo -> Objects
                                .equals(PurchaseTypeEnum.IN_STOCK_PAYABLE.getValue(), vo.getType()))
                        .mapToInt(PurchasePayableVoucherItemDetailsVO::getPayableQuantity).sum();
                int countOutStockPayableQuantity = v.stream().filter(vo -> Objects
                                .equals(PurchaseTypeEnum.OUT_STOCK_PAYABLE.getValue(), vo.getType()))
                        .mapToInt(PurchasePayableVoucherItemDetailsVO::getPayableQuantity).sum();
                voucherItemDetailsVO.setPayableQuantity(
                        countTnStockPayableQuantity - countOutStockPayableQuantity);
                voucherItemDetailsList.add(voucherItemDetailsVO);
            });
            purchasePayableVoucherVO.setKindQuantity(voucherItemDetailsList.size());
        }
        purchasePayableVoucherVO.setList(voucherItemDetailsList);
    }


    /**
     * 创建应付单号
     *
     * @param typeEnum 应付类型
     */
    private String createNo(PurchaseTypeEnum typeEnum) {
        String value;
        switch (typeEnum) {
            case IN_STOCK_PAYABLE:
                value = PurchasePayableConstant.RK;
                break;
            case OUT_STOCK_PAYABLE:
                value = PurchasePayableConstant.CK;
                break;
            default:
                value = PurchasePayableConstant.QT;
        }
        return PurchasePayableConstant.YF + value + PurchasePayableConstant.NUMBER
                + getCurrentDate() + incrementPart();
    }


    /**
     * 获取应付单自增部分
     */
    private String incrementPart() {
        if (StringUtils.isBlank(RedisUtil.get(PURCHASE_PAYABLE_REDIS_KEY))) {
            long currentTime = DateUtil.currentTime();
            long nextTime = DateUtil.toTime(DateUtil.toLastSecondOfDay(DateUtil.now()));
            long difference = nextTime - currentTime;
            RedisUtil.set(PURCHASE_PAYABLE_REDIS_KEY, 1, difference, TimeUnit.SECONDS);
            return String.format("%02d", 1);
        }
        int incrementPart = RedisUtil.increment(PURCHASE_PAYABLE_REDIS_KEY, 1).intValue();
        if (String.valueOf(incrementPart).length() < 2) {
            return String.format("%02d", incrementPart);
        }
        return String.valueOf(incrementPart);
    }


    /**
     * 获得当前月日
     */
    private String getCurrentDate() {
        return FastDateFormat.getInstance("MMdd").format(new Date());
    }


    private void assembleStockInOrder(Long relatedOrderId, PurchasePayableOrder purchasePayable) {
        StockInOrderAndOrderDetail stockInOrderAndOrderDetail = stockInOrderBizService
                .stockInOrderAndOrderDetailQuery(relatedOrderId);
        if (Objects.isNull(stockInOrderAndOrderDetail)) {
            return;
        }
        purchasePayable.setType(PurchaseTypeEnum.IN_STOCK_PAYABLE.getValue());
        purchasePayable.setRelatedOrderId(relatedOrderId);
        purchasePayable.setProviderId(stockInOrderAndOrderDetail.getProviderId());
        purchasePayable.setOrganizationId(stockInOrderAndOrderDetail.getPurchaseOrganizationId());
        purchasePayable.setBizDate(stockInOrderAndOrderDetail.getReceiptTime());
        purchasePayable.setRelatedOrderNo(stockInOrderAndOrderDetail.getNo());
        final Integer totalItemRealStockInQuantity = stockInOrderAndOrderDetail.getStockInOrderDetails().stream()
                .map(StockInOrderDetail::getRealReceiptQuantity)
                .reduce(0, Integer::sum);
        purchasePayable.setTotalItemQuantity(
                totalItemRealStockInQuantity);

        List<StockInOrderDetail> stockInOrderDetails = stockInOrderAndOrderDetail
                .getStockInOrderDetails();
        Map<String, List<StockInOrderDetail>> collectMap = stockInOrderDetails.stream()
                .collect(Collectors.groupingBy(StockInOrderDetail::getItemSkuCode));
        BigDecimal totalPriceTax = stockInOrderDetails.stream()
                .map(StockInOrderDetail::getTotalPriceTax).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal afterTaxAmount = stockInOrderDetails.stream()
                .map(StockInOrderDetail::getAfterTaxAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal taxQuota = stockInOrderDetails.stream().map(StockInOrderDetail::getTaxQuota)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        purchasePayable.setKindQuantity(collectMap.isEmpty() ? 0 : collectMap.size());
        purchasePayable.setTotalPriceTax(totalPriceTax);
        purchasePayable.setTotalAfterTaxItemAmount(afterTaxAmount);
        purchasePayable.setTotalTaxAmount(taxQuota);
    }


    private void assembleStockOutOrder(Long relatedOrderId, PurchasePayableOrder purchasePayable) {
        StockOutOrderViewVO stockOutOrderViewVO = stockOutOrderBizService
                .getStockOutOrder(relatedOrderId).getData();
        if (Objects.isNull(stockOutOrderViewVO)) {
            return;
        }
        purchasePayable.setType(PurchaseTypeEnum.OUT_STOCK_PAYABLE.getValue());
        purchasePayable.setRelatedOrderId(relatedOrderId);
        purchasePayable.setProviderId(stockOutOrderViewVO.getProviderId());
        purchasePayable.setOrganizationId(stockOutOrderViewVO.getPurchaseOrganizationId());
        purchasePayable.setBizDate(stockOutOrderViewVO.getApprovalAt());
        purchasePayable.setRelatedOrderNo(stockOutOrderViewVO.getNo());
        final Integer totalItemRealStockOutQuantity = stockOutOrderViewVO.getStockOutOrderDetailList().stream()
                .map(StockOutOrderDetailVO::getRealReturnQuantity)
                .reduce(0, Integer::sum);
        purchasePayable.setTotalItemQuantity(totalItemRealStockOutQuantity);

        List<StockOutOrderDetailVO> stockOutOrderDetailList = stockOutOrderViewVO
                .getStockOutOrderDetailList();
        Map<String, List<StockOutOrderDetailVO>> collect = stockOutOrderDetailList.stream()
                .collect(Collectors.groupingBy(StockOutOrderDetailVO::getItemSkuCode));
        BigDecimal totalPriceTax = stockOutOrderDetailList.stream()
                .map(StockOutOrderDetailVO::getTotalPriceTax)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal afterTaxAmount = stockOutOrderDetailList.stream()
                .map(StockOutOrderDetailVO::getAfterTaxAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal taxQuota = stockOutOrderDetailList.stream()
                .map(StockOutOrderDetailVO::getTaxQuota).reduce(BigDecimal.ZERO, BigDecimal::add);
        purchasePayable.setKindQuantity(collect.isEmpty() ? 0 : collect.size());
        purchasePayable.setTotalPriceTax(totalPriceTax);
        purchasePayable.setTotalAfterTaxItemAmount(afterTaxAmount);
        purchasePayable.setTotalTaxAmount(taxQuota);
    }


    private void assembleOtherPay(Long relatedOrderId, PurchasePayableOrder purchasePayable) {
        OtherPayVo otherPayVo = otherPayBizService.getById(relatedOrderId).getData();
        if (Objects.isNull(otherPayVo)) {
            return;
        }
        purchasePayable.setType(PurchaseTypeEnum.OTHER_PAYABLE.getValue());
        purchasePayable.setProviderId(otherPayVo.getProviderId());
        purchasePayable.setOrganizationId(otherPayVo.getOrganizationId());
        purchasePayable.setBizDate(otherPayVo.getAuditedAt());
        purchasePayable.setRelatedOrderNo(otherPayVo.getPayNo());
        purchasePayable.setTotalItemQuantity(0);
        purchasePayable.setKindQuantity(0);
        purchasePayable.setTotalPriceTax(otherPayVo.getPayTotal());
        purchasePayable.setTotalAfterTaxItemAmount(otherPayVo.getPayTotal());
        purchasePayable.setTotalTaxAmount(PurchasePayableConstant.DEFAULT_BIG_DECIMAL);
        purchasePayable.setRelatedOrderId(relatedOrderId);
    }


    private QueryWrapper<PurchasePayableOrder> setPageParams(PurchasePayableQueryPage queryPage) {
        // 采购订单三波权限
        // 1.只能看到自己创建的
        // 2.能看到全部业务人员创建订单
        // 3.能看到系统创建订单
        Long userId = UserContext.getUserId();
        boolean seeAllOrder = UserContext.hasPermission(GlobalConstant.PURCHASE_PAYABLE_ORDER_URI, ResourceType.API);
        boolean seeSysOrder = UserContext.hasPermission(GlobalConstant.VIEW_PAYMENT_WRITE_OFF, ResourceType.API);

        QueryWrapper<PurchasePayableOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(StringUtils.isNotBlank(queryPage.getNo()), PurchasePayableOrder::getNo,
                        queryPage.getNo())
                .and(Objects.nonNull(queryPage.getBuyerId()), qw -> {
                    qw.eq(PurchasePayableOrder::getBuyerId, queryPage.getBuyerId())
                            .or()
                            .eq(PurchasePayableOrder::getCreatedUid, queryPage.getBuyerId());
                })
                .eq(Objects.nonNull(queryPage.getType()), PurchasePayableOrder::getType,
                        queryPage.getType())
                .eq(Objects.nonNull(queryPage.getProviderId()), PurchasePayableOrder::getProviderId,
                        queryPage.getProviderId())
                .eq(Objects.nonNull(queryPage.getOrganizationId()),
                        PurchasePayableOrder::getOrganizationId, queryPage.getOrganizationId())
                .in(CollectionUtil.isNotEmpty(queryPage.getRelatedOrderNoList()),
                        PurchasePayableOrder::getRelatedOrderNo, queryPage.getRelatedOrderNoList())
                .between(Objects.nonNull(queryPage.getQueryStartDate()) && Objects
                                .nonNull(queryPage.getQueryEndDate()),
                        PurchasePayableOrder::getBizDate, queryPage.getQueryStartDate(),
                        queryPage.getQueryEndDate())
                .between(Objects.equals(PurchasePayableTimeTypeEnum.CURRENT_MONTH.getValue(),
                                queryPage.getPurchasePayableTimeType()),
                        PurchasePayableOrder::getCreatedAt, DateUtil.getFirstDayTimeOfMonth(),
                        DateUtil.currentTime())
                // 添加合作模式筛选
                .in(CollUtil.isNotEmpty(queryPage.getBusinessLine())
                        , PurchasePayableOrder::getBusinessLine, queryPage.getBusinessLine())
                .eq(!seeAllOrder && !seeSysOrder, PurchasePayableOrder::getCreatedUid, userId)
                .in(!seeAllOrder && seeSysOrder, PurchasePayableOrder::getCreatedUid, ListUtil.of(userId, 0L))
                .ne(seeAllOrder && !seeSysOrder, PurchasePayableOrder::getCreatedUid, 0L)
//                // 冲销查看的权限
//                .ne(!queryPage.getSeeHedge(),
//                        PurchasePayableOrder::getStatus, PurchasePayableOrderStatus.FIXED_ORDER)
                .orderByDesc(PurchasePayableOrder::getCreatedAt);

            return queryWrapper;
    }


    private QueryWrapper<PurchasePayableOrder> setVoucherParams(PurchasePayableVoucherQuery query) {
        QueryWrapper<PurchasePayableOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(Objects.nonNull(query.getProviderId()), PurchasePayableOrder::getProviderId,
                        query.getProviderId())
                .and(Objects.nonNull(query.getBuyerId()), qw -> qw
                        .eq(PurchasePayableOrder::getBuyerId, query.getBuyerId())
                        .or()
                        .eq(PurchasePayableOrder::getCreatedUid, query.getBuyerId()))
                .eq(Objects.nonNull(query.getOrganizationId()),
                        PurchasePayableOrder::getOrganizationId, query.getOrganizationId())
                .between(Objects.nonNull(query.getQueryDate()), PurchasePayableOrder::getCreatedAt,
                        query.getQueryDate()
                        , DateUtil.toTime(DateUtil.getLastDayOfMonth(
                                DateUtil.toLocalDateTime(query.getQueryDate()))))
                .eq(Objects.nonNull(query.getType()), PurchasePayableOrder::getType,
                        query.getType());
        return queryWrapper;
    }


    private QueryWrapper<PurchasePayableOrder> set(Long relatedOrderId, Integer type) {
        QueryWrapper<PurchasePayableOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(Objects.nonNull(relatedOrderId), PurchasePayableOrder::getRelatedOrderId,
                        relatedOrderId)
                .eq(Objects.nonNull(type), PurchasePayableOrder::getType, type);
        return queryWrapper;
    }


    @Override
    public MultiResponse<String> fixPurchasePayData(ArtificialPayOrderSaveDto saveDto) {
        List<ArtificialPayOrderDetailSaveDto> detailList = saveDto.getDetailSaveDtoList();
        if (CollUtil.isEmpty(detailList)) {
            return MultiResponse.buildFailure(ErrorCode.BUSINESS_OPERATE_ERROR.getCode(), "付款申请单列表详情查询为空");
        }

        // 冲销完成，根据人工修正的数据重新生成出入库单
        // 因为其他同学写的创建出入库单的内部详细逻辑晦涩难懂，稳妥起见，生成一笔辅助采购单，走原来逻辑
        PurchaseOrderCmd purchaseOrderCmd = PurchaseOrderCmd.forFixedPayOrder(saveDto.getProviderId(), saveDto.getDetailSaveDtoList(),
                DateUtil.currentTime(), saveDto.getWarehouseNo());
        SingleResponse<Long> response = purchaseOrderBizService.sysSave(purchaseOrderCmd);
        if (!response.isSuccess()) {
            return MultiResponse.buildFailure(ErrorCode.BUSINESS_OPERATE_ERROR.getCode(),
                    "根据修正参数生成辅助采购订单异常。" + response.getErrMessage());
        }

        // 将辅助采购单状态手动更新为已完结。
        iPurchaseOrderService.lambdaUpdate().set(PurchaseOrder::getState, 7).eq(PurchaseOrder::getId, response.getData())
                .update();
        PurchaseOrder newPurchaseOrder = iPurchaseOrderService.getById(response.getData());
        List<PurchaseOrderDetail> newPurchaseOrderDetailList = iPurchaseOrderDetailService.lambdaQuery()
                .eq(PurchaseOrderDetail::getPurchaseOrderId, response.getData()).list();

        // 根据上一步生成的辅助采购订单，生成对应的出入库单，并且同步到金蝶，这步骤新生成的出入库单就是根据手动修正的数量和金额来生成的。
        MultiResponse<String> stringMultiResponse = purchaseOrderFeatureService
                .stockOrderGenerationByPurchaseOrder(newPurchaseOrder, newPurchaseOrderDetailList);
        if (!stringMultiResponse.isSuccess()) {
            iPurchaseOrderService.removeById(newPurchaseOrder.getId());
            iPurchaseOrderDetailService.removeByIdsWithTime(newPurchaseOrderDetailList.stream()
                    .map(PurchaseOrderDetail::getId).collect(Collectors.toList()));
        }
        return stringMultiResponse;
    }

    private PurchaseOrder getOldPurchaseOrder(Long id) {
        return purchaseOrderMapper.getById(id);
    }

    /**
     * 冲销原本的采购应付数据
     * 第一次暂估货款是负数，那申请付款时冲减暂估就是采购入库。
     * 第一次暂估货款的是正数，那申请付款时冲减暂估就是采购退料出库。
     *
     * @param oldPurchasePayNo 采购单号
     */
    private SingleResponse<String> hedgeOldPayData(PurchasePayableOrder oldPurchasePayNoOrder, String oldPurchasePayNo, List<ChooseSkuDo> chooseSkuCodes) {
        Map<String, BigDecimal> chooseSkuMap = chooseSkuCodes.stream().collect(Collectors.toMap(ChooseSkuDo::getSkuCode, ChooseSkuDo::getPrice));

        // 原本是采购入库的数据 生成退料出库单进行冲销
        if (Objects.equals(oldPurchasePayNoOrder.getType(), 1)) {

            Long oldStockInOrderId = oldPurchasePayNoOrder.getRelatedOrderId();
            Optional<StockInOrder> optional = iStockInOrderService.lambdaQuery().eq(StockInOrder::getId, oldStockInOrderId).oneOpt();
            Assert.isTrue(optional.isPresent(), "冲销原本的采购应付数据-原应付单:" + oldPurchasePayNo + "关联的入库单编号非法");
            long oldPurchaseOrderId = optional.get().getPurchaseOrderId();
            PurchaseOrder oldPurchaseOrder = getOldPurchaseOrder(oldPurchaseOrderId);
            Assert.notNull(oldPurchaseOrder, "冲销原本的采购应付数据-根据原应付单关联的入库单单号，无法查到采购订单");

            List<StockInOrderDetail> oldStockInOrderDetailList = iStockInOrderDetailService.lambdaQuery()
                    .eq(StockInOrderDetail::getStockInOrderId, oldStockInOrderId)
                    .in(StockInOrderDetail::getItemSkuCode, chooseSkuCodes.stream().map(ChooseSkuDo::getSkuCode).collect(Collectors.toList()))
                    .list().stream().filter(val -> {
                        String itemSkuCode = val.getItemSkuCode();
                        BigDecimal decimal = chooseSkuMap.get(itemSkuCode);
                        return Objects.nonNull(decimal) && val.getTaxPrice().compareTo(decimal) == 0;
                    }).collect(Collectors.toList());
            Map<String, Long> itemCodeAndIdMap = new HashMap<>();
            List<String> skuCodeList = oldStockInOrderDetailList.stream().map(StockInOrderDetail::getItemSkuCode).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(skuCodeList)) {
                itemCodeAndIdMap = iItemSkuService.lambdaQuery().in(ItemSku::getSkuCode, skuCodeList)
                        .select(ItemSku::getSkuCode, ItemSku::getItemId).list()
                        .stream().collect(Collectors.toMap(ItemSku::getSkuCode, ItemSku::getItemId, (a, b) -> b));
            }
            if (CollUtil.isEmpty(itemCodeAndIdMap)) {
                log.info("hedgeOldPayData，不需要冲销。应付单编码:{}", oldPurchasePayNoOrder.getNo());
                return SingleResponse.of("");
            }

            // 准备好数据，生成逆向出库单
            StockOutOrderCmd stockOutOrderCmd = StockOutOrderCmd.ofByStockInOrder(oldPurchaseOrder, itemCodeAndIdMap,
                    oldStockInOrderDetailList, "废弃冲销流程产生单据");
            // 如果冲销出库单 同步到金蝶失败，删除业务生成的出库单据
            SingleResponse<String> stockOutOrderNoRes = stockOutOrderBizService.syncStockOutOrder(stockOutOrderCmd, false);
            if (!stockOutOrderNoRes.isSuccess()) {
                String no = stockOutOrderNoRes.getData();
                List<StockOutOrder> list1 = iStockOutOrderService.lambdaQuery().eq(StockOutOrder::getNo, no).list();
                if (CollUtil.isNotEmpty(list1)) {
                    StockOutOrder stockOutOrder = list1.get(0);
                    Long id = stockOutOrder.getId();

                    iStockOutOrderService.removeById(id);
                    QueryWrapper<StockOutOrderDetail> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("stock_out_order_id", id);
                    iStockOutOrderDetailService.removeWithTime(queryWrapper);
                }
            }
            return stockOutOrderNoRes;
        }

        // 针对出库单，生成逆向的入库单进行冲销
        if (Objects.equals(oldPurchasePayNoOrder.getType(), 2)) {

            Long oldStockOutOrderId = oldPurchasePayNoOrder.getRelatedOrderId();
            Optional<StockOutOrder> optional = iStockOutOrderService.lambdaQuery().eq(StockOutOrder::getId, oldStockOutOrderId).oneOpt();
            Assert.isTrue(optional.isPresent(), "冲销原本的采购应付数据-退料出库单编码非法");
            StockOutOrder oldStockOutOrder = optional.get();
            long oldPurchaseOrderId = optional.get().getPurchaseOrderId();
            PurchaseOrder oldPurchaseOrder = getOldPurchaseOrder(oldPurchaseOrderId);
            Assert.notNull(oldPurchaseOrder, "冲销原本的采购应付数据-根据原应付单关联的出库单单号，无法查到采购订单");
            List<StockOutOrderDetail> stockOutOrderDetailList = iStockOutOrderDetailService.lambdaQuery()
                    .eq(StockOutOrderDetail::getStockOutOrderId, oldStockOutOrder.getId())
                    .in(StockOutOrderDetail::getItemSkuCode, chooseSkuCodes.stream().map(ChooseSkuDo::getSkuCode).collect(Collectors.toList()))
                    .list().stream().filter(val -> {
                        String itemSkuCode = val.getItemSkuCode();
                        BigDecimal decimal = chooseSkuMap.get(itemSkuCode);
                        return Objects.nonNull(decimal) && val.getTaxPrice().compareTo(decimal) == 0;
                    }).collect(Collectors.toList());

            if (CollUtil.isEmpty(stockOutOrderDetailList)) {
                log.info("hedgeOldPayData，不需要冲销。应付单编码:{}", oldPurchasePayNoOrder.getNo());
                return SingleResponse.of("");
            }

            // 生成货款金额相反，其他数据镜像的采购入库单，创建。
            StockInOrderAndOrderDetail detail =
                    StockInOrderAndOrderDetail.ofStockOutOrder(
                            oldPurchaseOrder,
                            oldStockOutOrder,
                            stockOutOrderDetailList,
                            providerGateway
                                    .partnerQueryByProviderId(oldPurchaseOrder.getProviderId())
                                    .orElse(null), "冲销废弃流程产生单据");
            SingleResponse<String> stockInOrderRes = stockInOrderBizService.createStockInOrder(detail, true, false);
            // 业务回滚
            if (!stockInOrderRes.isSuccess()) {
                String no = stockInOrderRes.getData();
                List<StockInOrder> list1 = iStockInOrderService.lambdaQuery().eq(StockInOrder::getNo, no).list();
                if (CollUtil.isNotEmpty(list1)) {
                    StockInOrder stockInOrder = list1.get(0);
                    Long id = stockInOrder.getId();

                    iStockInOrderService.removeById(id);
                    QueryWrapper<StockInOrderDetail> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("stock_in_order_id", id);
                    iStockInOrderDetailService.removeWithTime(queryWrapper);
                }
            }
            return stockInOrderRes;

        }

        return SingleResponse.buildFailure(ErrorCode.API_RESP_ERROR.getCode(), "原采购应付单类型异常，无法冲销");
    }


    @Override
    public PageResponse<PurchasePayableApplyOrder> queryApplyOrder(ApplyOrderQueryPage queryPage) {

        PageInfo<PurchasePayableApplyOrder> pageInfo = PageHelper.startPage(queryPage.getPageIndex(), queryPage.getPageSize())
                .doSelectPageInfo(
                        () -> iPurchasePayableApplyOrderService.lambdaQuery()
                                .eq(Objects.nonNull(queryPage.getProviderId()),
                                        PurchasePayableApplyOrder::getProviderId, queryPage.getProviderId())
                                .eq(Objects.nonNull(queryPage.getStatus()),
                                        PurchasePayableApplyOrder::getStatus, queryPage.getStatus())
                                .eq(StrUtil.isNotBlank(queryPage.getNo()),
                                        PurchasePayableApplyOrder::getNo, queryPage.getNo())
                                .orderByDesc(PurchasePayableApplyOrder::getId)
                                .list()
                );
        List<PurchasePayableApplyOrder> list = pageInfo.getList();
        return PageResponse.of(list, (int) pageInfo.getTotal(), pageInfo.getPageSize(), pageInfo.getPageNum());
    }

    @Override
    public SingleResponse<ApplyPayOrderDetailVO> showApplyOrderDetail(List<ApplyPayNoDto> applyNoList) {
        ApplyPayOrderDetailVO vo = new ApplyPayOrderDetailVO();

        List<String> payOrderNoList = applyNoList.stream().map(ApplyPayNoDto::getPurchasePayOrderNo).collect(Collectors.toList());
        List<PurchasePayableOrder> list = purchasePayableOrderService.lambdaQuery().in(PurchasePayableOrder::getNo, payOrderNoList)
                .list();
        vo.setProviderId(list.get(0).getProviderId());
        SingleResponse<ProviderVO> response = providerService.queryDetail(list.get(0).getProviderId());
        if (Objects.isNull(response.getData())) {
            vo.setProviderName("");
        } else {
            vo.setProviderName(response.getData().getName());
        }

        return SingleResponse.of(vo);
    }

    @Override
    public SingleResponse<Boolean> checkProvider(List<ApplyPayNoDto> applyNoList) {
        List<String> payOrderNoList = applyNoList.stream().map(ApplyPayNoDto::getPurchasePayOrderNo).collect(Collectors.toList());
        List<PurchasePayableOrder> list = purchasePayableOrderService.lambdaQuery().in(PurchasePayableOrder::getNo, payOrderNoList)
                .list();
        if (CollUtil.isEmpty(list)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "应付单编号集合异常，查不到应付单数据");
        }
        long count = list.stream().map(PurchasePayableOrder::getProviderId).collect(Collectors.toSet()).size();
        Assert.isTrue(count == 1, "请保证所选应付单的供应商一致");

        return SingleResponse.of(true);
    }

    @Override
    public SingleResponse<ApplyPayOrderDetailVO> showApplyOrderDetailByApplyNo(String no) {
        Optional<PurchasePayableApplyOrder> optional = iPurchasePayableApplyOrderService.lambdaQuery()
                .eq(PurchasePayableApplyOrder::getNo, no).oneOpt();
        if (optional.isPresent()) {
            PurchasePayableApplyOrder purchasePayableApplyOrder = optional.get();
            ApplyPayOrderDetailVO vo = new ApplyPayOrderDetailVO();
            vo.setProviderId(purchasePayableApplyOrder.getProviderId());
            vo.setProviderName(purchasePayableApplyOrder.getProviderName());
            vo.setSourcePayTotalAmount(purchasePayableApplyOrder.getSourcePayTotalAmount());
            vo.setApplyDate(purchasePayableApplyOrder.getApplyDate());
            vo.setApplyNo(purchasePayableApplyOrder.getNo());
            vo.setStatus(purchasePayableApplyOrder.getStatus());

            Long additionalId = purchasePayableApplyOrder.getAdditionalId();
            if (Objects.nonNull(additionalId)) {
                Additional additional = iAdditionalService.getById(additionalId);
                if (Objects.nonNull(additional)) {
                    vo.setAdditionalId(additional.getId());
                    vo.setAdditionalName(additional.getName());
                    vo.setAdditionalUrl(additional.getDownloadUrl());
                }
            }

            return SingleResponse.of(vo);
        }
        return SingleResponse.buildFailure(ErrorCode.SYS_ERROR.getCode(), "编号异常，查出信息为空");
    }

    @Override
    public MultiResponse<SaveApplyOrderDetailDto> showApplyOrderDetailListByApplyNo(ApplyPayOrderDetailListQueryPage queryPage) {
        List<PurchasePayableApplyOrderDetail> list = iPurchasePayableApplyOrderDetailService.lambdaQuery()
                .eq(PurchasePayableApplyOrderDetail::getApplyOrderNo, queryPage.getApplyOrderNo())
                .orderByAsc(PurchasePayableApplyOrderDetail::getPurchasePayOrderNo).list();
//        PageInfo<PurchasePayableApplyOrderDetail> pageInfo = PageHelper.startPage(queryPage.getPageIndex(), queryPage.getPageSize())
//                .doSelectPageInfo(
//                        () -> iPurchasePayableApplyOrderDetailService.lambdaQuery()
//                                .eq(PurchasePayableApplyOrderDetail::getApplyOrderNo, queryPage.getApplyOrderNo())
//                                .orderByAsc(PurchasePayableApplyOrderDetail::getPurchasePayOrderNo).list()
//                );
//        List<PurchasePayableApplyOrderDetail> list = pageInfo.getList();
        if (CollUtil.isEmpty(list)) {
            return MultiResponse.of(new ArrayList<>());
        }

        List<SaveApplyOrderDetailDto> collect = list.stream().map(val -> {
            SaveApplyOrderDetailDto dto = new SaveApplyOrderDetailDto();
            dto.setFixedQuantity(val.getFixedQuantity());
            if (val.getFixedQuantity() < 0) {
                dto.setFixedTotalAmount(val.getFixedTotalAmount().negate());
            } else {
                dto.setFixedTotalAmount(val.getFixedTotalAmount());
            }
            dto.setPurchasePayOrderNo(val.getPurchasePayOrderNo());
            dto.setSkuCode(val.getSkuCode());
            dto.setSpecifications(val.getSpecifications());
            dto.setItemName(val.getItemName());
            dto.setQuantity(val.getQuantity());
            dto.setTaxRate(val.getTaxRate());
            dto.setWithTaxPrice(val.getWithTaxPrice());
            dto.setWithoutTaxPrice(val.getWithoutTaxPrice());
            dto.setTaxTotalAmount(val.getTaxTotalAmount());
            dto.setWithTaxTotalAmount(val.getWithTaxTotalAmount());
            dto.setErrorLog(val.getErrorLog());
            dto.setId(val.getId());
            return dto;
        }).collect(Collectors.toList());
        return MultiResponse.of(collect);
    }

    @Data
    static
    class StockOrderDto {
        /**
         * 应付单来源类型 出入库单
         */
        private String type;

        /**
         * 出入库单明细id
         */
        private Long detailId;

        /**
         * 出入库单d
         */
        private Long orderId;

        /**
         * 原始应付单号
         */
        private String sourcePayNo;
    }

    /**
     * 如果此应付单下某个sku已经生成过 申请应付单，那么这sku需要过滤。
     * 获取应付单编号-需要过滤的skuCodeList 映射
     *
     * @param purchasePayOrderNo
     * @return
     */
    private Map<String, List<String>> filterSkuCodeMap(List<String> purchasePayOrderNo) {
        // 剔除已经生成付款申请单的数据
        Map<String, List<String>> filterMap = new HashMap<>(16);
        List<PurchasePayableApplyOrderDetail> list = iPurchasePayableApplyOrderDetailService.lambdaQuery()
                .in(PurchasePayableApplyOrderDetail::getPurchasePayOrderNo, purchasePayOrderNo)
                .like(PurchasePayableApplyOrderDetail::getErrorLog, "payableApplyOrder handler success")
                .select(PurchasePayableApplyOrderDetail::getPurchasePayOrderNo, PurchasePayableApplyOrderDetail::getSkuCode)
                .list();
        if (CollUtil.isNotEmpty(list)) {
            list.stream().collect(Collectors.groupingBy(PurchasePayableApplyOrderDetail::getPurchasePayOrderNo))
                    .forEach((k, v) -> filterMap.put(k, v.stream().map(PurchasePayableApplyOrderDetail::getSkuCode).collect(Collectors.toList())));
        }
        return filterMap;
    }

    /**
     * 应付单关联编号是入库单。
     * 根据入库单明细展示数据
     *
     * @param stockInOrderNoList
     * @param relationNoAndPayNoMap
     * @param payNoAndSkuCodeListMap
     * @return
     */
    private List<ApplyPayOrderDetailListVO> stockInOrderHandler(List<String> stockInOrderNoList, Map<String, String> relationNoAndPayNoMap
            , Map<String, List<String>> payNoAndSkuCodeListMap) {
        List<ApplyPayOrderDetailListVO> targetList = new LinkedList<>();

        if (CollUtil.isNotEmpty(stockInOrderNoList)) {
            // 根据编号查询出全部入库单数据。
            List<StockInOrder> stockInOrderList = iStockInOrderService.lambdaQuery().in(StockInOrder::getNo, stockInOrderNoList).list();
            if (CollUtil.isEmpty(stockInOrderList)) {
                return targetList;
            }
            // orderId-orderNo Map 映射
            Map<Long, String> idAndNoMap = stockInOrderList.stream().collect(Collectors.toMap(StockInOrder::getId, StockInOrder::getNo));
            // 根据orderId查询全部detail信息
            List<Long> stockInOrderIdList = stockInOrderList.stream().map(StockInOrder::getId).collect(Collectors.toList());
            List<StockInOrderDetail> allDetailList = iStockInOrderDetailService.lambdaQuery().in(StockInOrderDetail::getStockInOrderId, stockInOrderIdList).list();

            // 根据入库单id一路分割，一路映射，最终从detail中剔除不需要展示的skuCode
            allDetailList.stream().collect(Collectors.groupingBy(StockInOrderDetail::getStockInOrderId))
                    .forEach((stockInOrderId, list) -> {
                        String stockInOrderNo = idAndNoMap.get(stockInOrderId);
                        if (StrUtil.isNotBlank(stockInOrderNo)) {
                            String payOrderNo = relationNoAndPayNoMap.get(stockInOrderNo);
                            if (StrUtil.isNotBlank(payOrderNo)) {

                                List<String> filterSkuCodeList = payNoAndSkuCodeListMap.getOrDefault(payOrderNo, new LinkedList<>());
                                List<StockInOrderDetail> matchedDetailList = list.stream()
                                        .filter(val -> !filterSkuCodeList.contains(val.getItemSkuCode()))
                                        .collect(Collectors.toCollection(LinkedList::new));

                                // 在根据选中的detailList转换目标数据结构
                                List<ApplyPayOrderDetailListVO> collect = matchedDetailList.stream().map(stockInOrderDetail -> {
                                    ApplyPayOrderDetailListVO vo = new ApplyPayOrderDetailListVO();
                                    vo.setPurchasePayOrderNo(payOrderNo);
                                    vo.setRelationOrderId(stockInOrderDetail.getStockInOrderId());
                                    vo.setRelationOrderType(1);
                                    vo.setSkuCode(stockInOrderDetail.getItemSkuCode());
                                    vo.setSpecifications(stockInOrderDetail.getSpecifications());
                                    vo.setItemName(stockInOrderDetail.getItemName());
                                    vo.setQuantity(stockInOrderDetail.getRealReceiptQuantity());
                                    vo.setTaxRate(stockInOrderDetail.getTaxRate());
                                    vo.setWithTaxPrice(stockInOrderDetail.getTaxPrice());
                                    vo.setWithoutTaxPrice(stockInOrderDetail.getAfterTaxPrice());
                                    vo.setTaxTotalAmount(stockInOrderDetail.getTaxQuota());
                                    vo.setWithTaxTotalAmount(stockInOrderDetail.getTotalPriceTax());
                                    vo.setWarehouseNo(stockInOrderDetail.getWarehouseNo());
                                    return vo;
                                }).collect(Collectors.toList());
                                targetList.addAll(collect);
                            }
                        }

                    });
        }

        return targetList;
    }


    /**
     * 处理同入库单处理
     *
     * @param stockOutOrderNoList
     * @param relationNoAndPayNoMap
     * @param payNoAndSkuCodeListMap
     * @return
     */
    private List<ApplyPayOrderDetailListVO> stockOutOrderHandler(List<String> stockOutOrderNoList, Map<String, String> relationNoAndPayNoMap
            , Map<String, List<String>> payNoAndSkuCodeListMap) {
        List<ApplyPayOrderDetailListVO> targetList = new LinkedList<>();

        if (CollUtil.isNotEmpty(stockOutOrderNoList)) {
            List<StockOutOrder> stockOutOrderList = iStockOutOrderService.lambdaQuery().in(StockOutOrder::getNo, stockOutOrderNoList).list();
            if (CollUtil.isEmpty(stockOutOrderList)) {
                return targetList;
            }
            Map<Long, String> idAndNoMap = stockOutOrderList.stream().collect(Collectors.toMap(StockOutOrder::getId, StockOutOrder::getNo));

            // 根据orderId查询全部detail信息
            List<Long> stockOutOrderIdList = stockOutOrderList.stream().map(StockOutOrder::getId).collect(Collectors.toList());
            List<StockOutOrderDetail> allDetailList = iStockOutOrderDetailService.lambdaQuery().in(StockOutOrderDetail::getStockOutOrderId, stockOutOrderIdList).list();

            // 根据入库单id一路分割，一路映射，最终从detail中剔除不需要展示的skuCode
            allDetailList.stream().collect(Collectors.groupingBy(StockOutOrderDetail::getStockOutOrderId))
                    .forEach((stockOutOrderId, list) -> {
                        String stockOutOrderNo = idAndNoMap.get(stockOutOrderId);
                        if (StrUtil.isNotBlank(stockOutOrderNo)) {
                            String payOrderNo = relationNoAndPayNoMap.get(stockOutOrderNo);
                            if (StrUtil.isNotBlank(payOrderNo)) {

                                List<String> filterSkuCodeList = payNoAndSkuCodeListMap.getOrDefault(payOrderNo, new LinkedList<>());
                                List<StockOutOrderDetail> matchedDetailList = list.stream()
                                        .filter(val -> !filterSkuCodeList.contains(val.getItemSkuCode()))
                                        .collect(Collectors.toCollection(LinkedList::new));

                                // 在根据选中的detailList转换目标数据结构
                                List<ApplyPayOrderDetailListVO> collect = matchedDetailList.stream().map(stockOutOrderDetail -> {
                                    ApplyPayOrderDetailListVO vo = new ApplyPayOrderDetailListVO();
                                    vo.setPurchasePayOrderNo(payOrderNo);
                                    vo.setRelationOrderId(stockOutOrderDetail.getStockOutOrderId());
                                    vo.setRelationOrderType(2);
                                    vo.setSkuCode(stockOutOrderDetail.getItemSkuCode());
                                    vo.setSpecifications(stockOutOrderDetail.getSpecifications());
                                    vo.setItemName(stockOutOrderDetail.getItemName());
                                    vo.setQuantity(stockOutOrderDetail.getRealReturnQuantity());
                                    vo.setTaxRate(stockOutOrderDetail.getTaxRate());
                                    vo.setWithTaxPrice(stockOutOrderDetail.getTaxPrice());
                                    vo.setWithoutTaxPrice(stockOutOrderDetail.getAfterTaxPrice());
                                    vo.setTaxTotalAmount(stockOutOrderDetail.getTaxQuota());
                                    vo.setWithTaxTotalAmount(stockOutOrderDetail.getTotalPriceTax());
                                    vo.setWarehouseNo(stockOutOrderDetail.getWarehouseNo());
                                    return vo;
                                }).collect(Collectors.toList());
                                targetList.addAll(collect);
                            }
                        }

                    });
        }

        return targetList;
    }


    public MultiResponse<ApplyPayOrderDetailListVO> showApplyOrderDetailList(List<ApplyPayNoDto> noDtoList) {
        // 应付单关联的出入库单编号-应付单编号 映射
        Map<String, String> relationNoAndPayNoMap = noDtoList.stream()
                .collect(Collectors.toMap(ApplyPayNoDto::getRelationNo, ApplyPayNoDto::getPurchasePayOrderNo));
        List<String> relationList = noDtoList.stream().map(ApplyPayNoDto::getRelationNo).collect(Collectors.toList());

        // 已经生成了申请应付，需要剔除不展示的skuCode， 映射
        List<String> purchasePayOrderNos = noDtoList.stream()
                .map(ApplyPayNoDto::getPurchasePayOrderNo).collect(Collectors.toList());
        Map<String, List<String>> payNoAndSkuCodeListMap = filterSkuCodeMap(purchasePayOrderNos);

        List<String> stockInOrderNoList = relationList.stream().filter(val -> val.startsWith("CGRK")).collect(Collectors.toList());
        List<ApplyPayOrderDetailListVO> list1 = stockInOrderHandler(stockInOrderNoList, relationNoAndPayNoMap
                , payNoAndSkuCodeListMap);

        List<String> stockOutOrderNoList = relationList.stream().filter(val -> val.startsWith("CGCK")).collect(Collectors.toList());
        List<ApplyPayOrderDetailListVO> list2 = stockOutOrderHandler(stockOutOrderNoList, relationNoAndPayNoMap
                , payNoAndSkuCodeListMap);

        List<ApplyPayOrderDetailListVO> targetList = new LinkedList<>(list1);
        targetList.addAll(list2);

        return MultiResponse.of(targetList);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<Long> saveApplyOrder(SaveApplyOrderDto cmd) {
        List<SaveApplyOrderDetailDto> detailCmdList = cmd.getDetailCmdList();
        Assert.notEmpty(detailCmdList, "sku申请列表不得为空");

        PurchasePayableApplyOrder one = new PurchasePayableApplyOrder();
        one.setNo("YFSQ-" + DateUtil.currentTime());
        one.setApplyDate(DateUtil.currentTime());
        one.setProviderId(cmd.getProviderId());
        one.setProviderName(cmd.getProviderName());
        one.setRemark(cmd.getRemark());
        one.setStatus(PayableApplyOrderStatus.WAIT_AUDIT);
        one.setSourcePayTotalAmount(cmd.getSourcePayTotalAmount());
        one.setFixedTotalAmount(cmd.getFixedTotalAmount());
        one.setAdditionalId(cmd.getAdditionalId());
        iPurchasePayableApplyOrderService.save(one);

        List<SaveApplyOrderDetailDto> collect1 = cmd.getDetailCmdList().stream()
                .filter(val -> Objects.equals(val.getIsCustomize(), 1)).collect(Collectors.toList());
        Map<String, Long> customizeMap = collect1.stream()
                .collect(Collectors.groupingBy(SaveApplyOrderDetailDto::getSkuCode, Collectors.counting()));
        customizeMap.forEach((k, v) -> {
            if (v > 1) {
                throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "保存参数异常。自定义sku重复，skuCode:" + k);
            }
        });

        List<PurchasePayableApplyOrderDetail> collect = cmd.getDetailCmdList().stream().map(val -> {
            PurchasePayableApplyOrderDetail detail = new PurchasePayableApplyOrderDetail();
            detail.setApplyOrderNo(one.getNo());
            detail.setApplyOrderId(one.getId());
            detail.setProviderId(one.getProviderId());
            detail.setSkuCode(val.getSkuCode());
            detail.setFixedQuantity(val.getFixedQuantity());
            detail.setFixedTotalAmount(val.getFixedTotalAmount().abs());
            // 自定义sku
            if (Objects.equals(val.getIsCustomize(), 1)) {
                List<ItemSku> list = iItemSkuService.lambdaQuery().eq(ItemSku::getSkuCode, val.getSkuCode()).last("limit 1").list();
                if (CollUtil.isEmpty(list)) {
                    throw ExceptionFactory.bizException(ErrorCode.SYS_ERROR.getCode(), "sku不存在。skuCode:" + val.getSkuCode());
                }
                ItemSku itemSku = list.get(0);
                Item item = itemService.getById(itemSku.getItemId());
                detail.setItemName(Objects.isNull(item) ? "" : item.getName());
                detail.setWarehouseNo(itemSku.getWarehouseNo());

            } else {
                detail.setPurchasePayOrderNo(val.getPurchasePayOrderNo());
                detail.setRelationOrderId(val.getRelationOrderId());
                detail.setRelationOrderType(val.getRelationOrderType());

                detail.setSpecifications(val.getSpecifications());
                detail.setItemName(val.getItemName());
                detail.setQuantity(val.getQuantity());
                detail.setTaxRate(val.getTaxRate());
                detail.setWithTaxPrice(val.getWithTaxPrice());
                detail.setWithoutTaxPrice(val.getWithoutTaxPrice());
                detail.setTaxTotalAmount(val.getTaxTotalAmount());
                detail.setWithTaxTotalAmount(val.getWithTaxTotalAmount());
                detail.setWarehouseNo(val.getWarehouseNo());
            }
            return detail;
        }).collect(Collectors.toList());
        iPurchasePayableApplyOrderDetailService.saveBatch(collect);
        return SingleResponse.of(one.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<Boolean> editApplyOrder(EditApplyPayOrderCmd cmd) {
        PurchasePayableApplyOrder payableApplyOrder = iPurchasePayableApplyOrderService.getById(cmd.getApplyOrderId());
        Assert.notNull(payableApplyOrder, "付款申请单查询为空，id非法");
        Assert.isTrue(payableApplyOrder.getStatus().equals(PayableApplyOrderStatus.WAIT_AUDIT), "只有待审核状态的单据才能编辑");

        boolean updateOrder = payableApplyOrder.getFixedTotalAmount().compareTo(cmd.getOrderFixedTotalAmount()) != 0
                || !Objects.equals(cmd.getAdditionalId(), payableApplyOrder.getAdditionalId());
        if (updateOrder) {
            payableApplyOrder.setFixedTotalAmount(cmd.getOrderFixedTotalAmount());
            payableApplyOrder.setAdditionalId(cmd.getAdditionalId());
            iPurchasePayableApplyOrderService.updateById(payableApplyOrder);
        }

        for (EditApplyPayOrderDetailCmd detailCmd : cmd.getDetailCmdList()) {
            iPurchasePayableApplyOrderDetailService.lambdaUpdate()
                    .eq(PurchasePayableApplyOrderDetail::getId, detailCmd.getDetailId())
                    .set(PurchasePayableApplyOrderDetail::getFixedQuantity, detailCmd.getFixedQuantity())
                    .set(PurchasePayableApplyOrderDetail::getFixedTotalAmount, detailCmd.getFixedTotalAmount().abs())
                    .update();
        }
        return SingleResponse.of(true);
    }

    @Override
    public MultiResponse<ApplyPayOrderRelationVO> queryPayOrderRelation(String sourcePayOrderNo) {
        List<PurchasePayableOrderRelation> list = iPurchasePayableOrderRelationService.lambdaQuery()
                .eq(PurchasePayableOrderRelation::getResourceNo, sourcePayOrderNo)
                .orderByDesc(PurchasePayableOrderRelation::getId)
                .list();
        if (CollUtil.isEmpty(list)) {
            return MultiResponse.of(new LinkedList<>());
        }

        List<String> stockOrderNoList = list.stream().map(PurchasePayableOrderRelation::getRelateStockOrderNo).collect(Collectors.toList());
        if (CollUtil.isEmpty(stockOrderNoList)) {
            return MultiResponse.of(new LinkedList<>());
        }
        Map<String, String> stockOrderNoAndPayableNoMap = iPurchasePayableOrderService.lambdaQuery()
                .in(PurchasePayableOrder::getRelatedOrderNo, stockOrderNoList).list()
                .stream().collect(Collectors.toMap(PurchasePayableOrder::getRelatedOrderNo, PurchasePayableOrder::getNo, (a, b) -> a));

        return MultiResponse.of(list.stream().map(val -> {
            ApplyPayOrderRelationVO vo = new ApplyPayOrderRelationVO();
            vo.setRelatePurchasePayableOrderNo(stockOrderNoAndPayableNoMap.getOrDefault(val.getRelateStockOrderNo(), ""));
            vo.setId(val.getId());
            vo.setCreatedAt(val.getCreatedAt());
            vo.setUpdatedAt(val.getUpdatedAt());
            vo.setCreatedUid(val.getCreatedUid());
            vo.setUpdatedUid(val.getUpdatedUid());
            vo.setDeletedAt(val.getDeletedAt());
            vo.setIsDel(val.getIsDel());
            vo.setResourceNo(val.getResourceNo());
            vo.setRelateStockOrderNo(val.getRelateStockOrderNo());
            vo.setType(val.getType());
            return vo;
        }).collect(Collectors.toList()));
    }


    @Override
    public SingleResponse<Boolean> deleteApplyOrderDetailByNo(String no) {
        Optional<PurchasePayableApplyOrder> optional = iPurchasePayableApplyOrderService.lambdaQuery()
                .eq(PurchasePayableApplyOrder::getNo, no).oneOpt();
        if (optional.isPresent()) {
            PurchasePayableApplyOrder purchasePayableApplyOrder = optional.get();

            PayableApplyOrderStatus status = purchasePayableApplyOrder.getStatus();
            if (PayableApplyOrderStatus.HAD_AUDIT.equals(status) || PayableApplyOrderStatus.FINISH.equals(status)) {
                return SingleResponse.buildFailure(ErrorCode.SYS_ERROR.getCode(), "审核完成状态，处理完成状态。不支持删除操作");
            }

            iPurchasePayableApplyOrderService.removeByIdWithTime(purchasePayableApplyOrder.getId());

            QueryWrapper<PurchasePayableApplyOrderDetail> wrapper = new QueryWrapper<>();
            wrapper.in("apply_order_no", no);
            iPurchasePayableApplyOrderDetailService.removeWithTime(wrapper);

            return SingleResponse.of(true);
        }
        return SingleResponse.buildFailure(ErrorCode.SYS_ERROR.getCode(), "编号非法");
    }

    // ------------------------------ 冲销处理 new -----------------------------
    // 之前的冲销版本是根据付款申请单进行处理的。新版本根据付款单进行处理

    @Resource
    IPaymentApplyOrderService iPaymentApplyOrderService;
    @Resource
    IPaymentApplyOrderDetailService iPaymentApplyOrderDetailService;

    @Resource
    IOrderSettlementFormService iOrderSettlementFormService;

    @Resource
    ErpPurchaseOrderHandler erpPurchaseOrderHandler;


    // ------------------------------ 冲销处理 ------------------------------

    @Override
    public void hedgeHandler(Long payApplyOrderId) {
        // 查询已经审核完成的付款申请单
        List<PurchasePayableApplyOrder> list = iPurchasePayableApplyOrderService.lambdaQuery()
                .eq(Objects.nonNull(payApplyOrderId), PurchasePayableApplyOrder::getId, payApplyOrderId)
                .list();
        if (CollUtil.isEmpty(list)) {
            log.error("冲销操作异常，申请付款单id非法或者状态为非过审状态。查询数据为空。payApplyOrderId:{}", payApplyOrderId);
            return;
        }

        PurchasePayableApplyOrder payableApplyOrder = list.get(0);
        List<ArtificialPayOrderSaveDto> paramList = createdParam(payableApplyOrder);

        // 处理过程日志暂时不记录入库，打好日志标记，暂时查log找问题。
        if (CollUtil.isNotEmpty(paramList)) {
            boolean isOk = handler00(paramList);
            String activeProfile = SpringUtil.getActiveProfile();
            boolean needSync = ("gray".equals(activeProfile) || "prod".equals(activeProfile));

            if (isOk) {
                if (needSync) {
                    // 将处理完成的付款申请单同步到金蝶
                    Response syncResp = kingDeeTemplate.handler(ApiEnum.SAVE_PAY_BILL, payableApplyOrder.getId(), null);
                    if (syncResp.isSuccess()) {
                        payableApplyOrder.setStatus(PayableApplyOrderStatus.FINISH);
                    }
                    if (!syncResp.isSuccess()) {
                        log.error("付款申请单冲销三部曲处理OK，但是同步到金蝶打款单异常，id:{},error:{}",
                                payableApplyOrder.getId(), syncResp.getErrMessage());
                        payableApplyOrder.setStatus(PayableApplyOrderStatus.ERROR);
                    }
                } else {
                    payableApplyOrder.setStatus(PayableApplyOrderStatus.FINISH);
                }
            } else {
                log.error("申请付款单冲销三部曲处理异常。ID:{}", payableApplyOrder.getId());
                payableApplyOrder.setStatus(PayableApplyOrderStatus.ERROR);
            }
            iPurchasePayableApplyOrderService.updateById(payableApplyOrder);
        }
    }

    /**
     * 构建冲销参数
     *
     * @param payableApplyOrder 付款申请单
     */
    private List<ArtificialPayOrderSaveDto> createdParam(PurchasePayableApplyOrder payableApplyOrder) {
        // 查询出付款申请单的详情。
        Long payableApplyOrderId = payableApplyOrder.getId();
        List<PurchasePayableApplyOrderDetail> detailList = iPurchasePayableApplyOrderDetailService.lambdaQuery()
                .eq(PurchasePayableApplyOrderDetail::getApplyOrderId, payableApplyOrderId).list();

        List<ArtificialPayOrderSaveDto> resList = new ArrayList<>();

        // 针对新增自定义sku发生付款申请的情况，
        List<PurchasePayableApplyOrderDetail> collect1 = detailList.stream()
                .filter(val -> Objects.isNull(val.getPurchasePayOrderNo())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect1)) {
            ArtificialPayOrderSaveDto saveDto = new ArtificialPayOrderSaveDto();
            saveDto.setPayableApplyOrderNo(payableApplyOrder.getNo());
            saveDto.setSourcePurchasePayNo(null);
            saveDto.setWarehouseNo(collect1.get(0).getWarehouseNo());
            saveDto.setProviderId(collect1.get(0).getProviderId());
            saveDto.setDetailSaveDtoList(collect1.stream().map(val -> {
                ArtificialPayOrderDetailSaveDto detailSaveDto = new ArtificialPayOrderDetailSaveDto();
                detailSaveDto.setSkuCode(val.getSkuCode());
                detailSaveDto.setPrice(val.getWithTaxPrice());
                detailSaveDto.setFixedQuantity(val.getFixedQuantity());
                detailSaveDto.setFixedTotalAmount(val.getFixedTotalAmount());
                return detailSaveDto;
            }).collect(Collectors.toList()));
            resList.add(saveDto);
        }

        // 正常业务场景
        List<PurchasePayableApplyOrderDetail> collect2 = detailList.stream()
                .filter(val -> Objects.nonNull(val.getPurchasePayOrderNo())).collect(Collectors.toList());
        // 应付单来源于入库单和出库单
        // 出入库单来源于采购订单，采购订单已经按照供应商+仓库编号完成切割
        collect2.stream().collect(Collectors.groupingBy(PurchasePayableApplyOrderDetail::getPurchasePayOrderNo))
                .forEach((purchasePayOrderNo, list) -> {
                    Long providerId2 = list.get(0).getProviderId();
                    String warehouseNo2 = list.get(0).getWarehouseNo();

                    ArtificialPayOrderSaveDto saveDto2 = new ArtificialPayOrderSaveDto();
                    saveDto2.setPayableApplyOrderNo(payableApplyOrder.getNo());
                    saveDto2.setSourcePurchasePayNo(purchasePayOrderNo);
                    saveDto2.setWarehouseNo(warehouseNo2);
                    saveDto2.setProviderId(providerId2);
                    saveDto2.setDetailSaveDtoList(list.stream().map(val -> {
                        ArtificialPayOrderDetailSaveDto detailSaveDto = new ArtificialPayOrderDetailSaveDto();
                        detailSaveDto.setSkuCode(val.getSkuCode());
                        detailSaveDto.setPrice(val.getWithTaxPrice());
                        detailSaveDto.setFixedQuantity(val.getFixedQuantity());
                        detailSaveDto.setFixedTotalAmount(val.getFixedTotalAmount());
                        return detailSaveDto;
                    }).collect(Collectors.toList()));
                    resList.add(saveDto2);
                });

        return resList;


    }

    @Data
    @EqualsAndHashCode
    private static class ChooseSkuDo {
        private String skuCode;
        private BigDecimal price;
    }

    public Boolean handler00(List<ArtificialPayOrderSaveDto> list) {
        AtomicBoolean allSuccessful = new AtomicBoolean(true);

        for (ArtificialPayOrderSaveDto val : list) {

            List<PurchasePayableOrder> orderList = iPurchasePayableOrderService.lambdaQuery()
                    .eq(PurchasePayableOrder::getNo, val.getSourcePurchasePayNo()).list();
            // 之前的采购应付单数据
            // 针对付款申请单添加自定义sku的情况，自定义sku绑定的原采购应付单数据是不存在的。
            PurchasePayableOrder oldPurchasePayNoOrder = CollUtil.isEmpty(orderList) ? null : orderList.get(0);
            List<ChooseSkuDo> chooseSkuCodes = val.getDetailSaveDtoList().stream().map(detail -> {
                ChooseSkuDo skuDo = new ChooseSkuDo();
                skuDo.setSkuCode(detail.getSkuCode());
                skuDo.setPrice(detail.getPrice());
                return skuDo;
            }).collect(Collectors.toList());

            HedgeOldStockOrderChain hedgeOldStockOrderChain = new HedgeOldStockOrderChain(val, oldPurchasePayNoOrder, chooseSkuCodes);
            GenerateNewStockOrderChain generateNewStockOrderChain = new GenerateNewStockOrderChain(val, oldPurchasePayNoOrder, chooseSkuCodes);
            GenerateSalesOrderChain generateSalesOrderChain = new GenerateSalesOrderChain(val, oldPurchasePayNoOrder, chooseSkuCodes);

            hedgeOldStockOrderChain.setNextChain(generateNewStockOrderChain);
            generateNewStockOrderChain.setNextChain(generateSalesOrderChain);
            generateSalesOrderChain.setNextChain(null);

            Boolean chainRes = hedgeOldStockOrderChain.handle();

            if (!chainRes) {
                allSuccessful.set(false);
            }
        }

        return allSuccessful.get();
    }


    private abstract class AbstractHedgeChain {
        ArtificialPayOrderSaveDto val;
        PurchasePayableOrder oldPurchasePayNoOrder;
        List<ChooseSkuDo> chooseSkuCodes;

        AbstractHedgeChain nextChain;

        public AbstractHedgeChain(ArtificialPayOrderSaveDto val, PurchasePayableOrder oldPurchasePayNoOrder, List<ChooseSkuDo> chooseSkuCodes) {
            this.val = val;
            this.oldPurchasePayNoOrder = oldPurchasePayNoOrder;
            this.chooseSkuCodes = chooseSkuCodes;
        }

        public void setNextChain(AbstractHedgeChain chain) {
            this.nextChain = chain;
        }

        /**
         * 冲销具体处理流程。
         *
         * @return true:继续下一个阶段的执行。false:到此流程为止，不再继续。
         */
        abstract Boolean handle();


        void saveLog(String payableApplyOrderNo, String sourcePurchasePayNo, String error) {
            iPurchasePayableApplyOrderDetailService.lambdaUpdate().set(PurchasePayableApplyOrderDetail::getErrorLog, error)
                    .eq(PurchasePayableApplyOrderDetail::getApplyOrderNo, payableApplyOrderNo)
                    .eq(PurchasePayableApplyOrderDetail::getPurchasePayOrderNo, sourcePurchasePayNo)
                    .update();
        }

        void saveRelation(String oldPurchasePayableOrderNo, List<String> hedgeStockOrderNos, PayOrderRelateType type) {
            if (StrUtil.isBlank(oldPurchasePayableOrderNo)) {
                return;
            }
            List<PurchasePayableOrderRelation> collect = hedgeStockOrderNos.stream().map(val -> {
                PurchasePayableOrderRelation relation = new PurchasePayableOrderRelation();
                relation.setResourceNo(oldPurchasePayableOrderNo);
                relation.setRelateStockOrderNo(val);
                relation.setType(type);
                return relation;
            }).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(collect)) {
                iPurchasePayableOrderRelationService.saveBatch(collect);
            }
        }
    }

    private class HedgeOldStockOrderChain extends AbstractHedgeChain {

        public HedgeOldStockOrderChain(ArtificialPayOrderSaveDto val, PurchasePayableOrder oldPurchasePayNoOrder,
                                       List<ChooseSkuDo> chooseSkuCodes) {
            super(val, oldPurchasePayNoOrder, chooseSkuCodes);
        }

        @Override
        Boolean handle() {
            boolean res;

            if (Objects.isNull(oldPurchasePayNoOrder)) {
                res = true;
            } else {
                // 1.冲销旧数据(逆向生成出入库单，推送金蝶)
                try {
                    SingleResponse<String> hedgeOldPayDataRes = hedgeOldPayData(oldPurchasePayNoOrder, val.getSourcePurchasePayNo(), chooseSkuCodes);
                    if (hedgeOldPayDataRes.isSuccess()) {
                        // 1.1 保存冲销单和原采购应付单的关联关系
                        saveRelation(val.getSourcePurchasePayNo(), Collections.singletonList(hedgeOldPayDataRes.getData())
                                , PayOrderRelateType.ALL_HEDGE);
                        res = true;
                    } else {
                        String error = StrUtil.format(
                                "HedgeOldStockOrderChain fail. " +
                                        "payableApplyOrderNo:{},sourcePurchasePayNo:{},hedgeOldPayDataResStockNo:{},error:{}"
                                , val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo(), hedgeOldPayDataRes.getData(),
                                hedgeOldPayDataRes.getErrMessage()
                        );
                        saveLog(val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo(), error);
                        res = false;
                    }
                } catch (Exception e) {
                    String error = StrUtil.format(
                            "HedgeOldStockOrderChain Exception," +
                                    "payableApplyOrderNo:{},sourcePurchasePayNo:{}"
                            , val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo());
                    saveLog(val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo(), error);
                    log.error(error, e);
                    res = false;
                }
            }

            if (res) {
                if (Objects.nonNull(this.nextChain)) {
                    return this.nextChain.handle();
                }
            }

            return res;
        }
    }

    private class GenerateNewStockOrderChain extends AbstractHedgeChain {

        public GenerateNewStockOrderChain(ArtificialPayOrderSaveDto val, PurchasePayableOrder oldPurchasePayNoOrder,
                                          List<ChooseSkuDo> chooseSkuCodes) {
            super(val, oldPurchasePayNoOrder, chooseSkuCodes);
        }

        @Override
        Boolean handle() {
            boolean res;
            // 1.1 针对修正金额是0的数据，不创建对应的出入库单
            List<ArtificialPayOrderDetailSaveDto> detailList = val.getDetailSaveDtoList().stream()
                    .filter(val1 -> val1.getFixedQuantity() != 0).collect(Collectors.toList());
            if (CollUtil.isEmpty(detailList)) {
                res = true;
            } else {
                try {
                    val.setDetailSaveDtoList(detailList);
                    // 2.根据修正数据重新生成推送出入库单（后续金蝶会自动根据推送的出库单生成应付单）
                    MultiResponse<String> fixRes = fixPurchasePayData(val);
                    if (fixRes.isSuccess()) {
                        saveRelation(val.getSourcePurchasePayNo(), fixRes.getData(), PayOrderRelateType.FIXED);
                        res = true;
                    } else {
                        String error = StrUtil.format(
                                "generateNewStockOrderChain fail. " +
                                        "payableApplyOrderNo:{},sourcePurchasePayNo:{},fixStockOrderRes:{},error:{}"
                                , val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo(), fixRes.getData(), fixRes.getErrMessage()
                        );
                        saveLog(val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo(), error);
                        res = false;
                    }
                } catch (Exception e) {
                    String error = StrUtil.format(
                            "generateNewStockOrderChain Exception. " +
                                    "payableApplyOrderNo:{},sourcePurchasePayNo:{}"
                            , val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo());
                    saveLog(val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo(), error);
                    log.error(error, e);
                    res = false;
                }
            }

            if (res) {
                if (Objects.nonNull(this.nextChain)) {
                    return this.nextChain.handle();
                }
            }

            return res;
        }
    }

    private class GenerateSalesOrderChain extends AbstractHedgeChain {

        public GenerateSalesOrderChain(ArtificialPayOrderSaveDto val, PurchasePayableOrder oldPurchasePayNoOrder,
                                       List<ChooseSkuDo> chooseSkuCodes) {
            super(val, oldPurchasePayNoOrder, chooseSkuCodes);
        }

        @Override
        Boolean handle() {
            boolean res;

            // 3.根据修复数据和冲销的数量差额，生成对应的销售退货单或者是销售出库单，并且同步给金蝶
            // （采购入库+采购退货）-（销售出库+销售退货）= 0
            // 结算数（带正负号）-暂估数（带正负号），结果是负数，就补销售退货单，结果是正数，就补销售出库单
            try {
                SingleResponse<Boolean> response = salesOrderHandler(oldPurchasePayNoOrder, chooseSkuCodes, val.getDetailSaveDtoList());
                if (!response.isSuccess()) {
                    String error = StrUtil.format(
                            "payableApplyOrder handler fail. " +
                                    "salesOrderHandler fail,payableApplyOrderNo:{},sourcePurchasePayNo:{},error:{}"
                            , val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo(), response.getErrMessage()
                    );
                    saveLog(val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo(), error);
                    res = false;
                } else {
                    res = true;
                }
            } catch (Exception e) {
                String error = StrUtil.format(
                        "generateSalesOrderChain Exception. " +
                                "payableApplyOrderNo:{},sourcePurchasePayNo:{}"
                        , val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo());
                log.error(error, e);
                saveLog(val.getPayableApplyOrderNo(), val.getSourcePurchasePayNo(), error);
                res = false;
            }

            if (res) {
                if (Objects.nonNull(this.nextChain)) {
                    return this.nextChain.handle();
                }
            }
            return res;
        }
    }


    private SingleResponse<Boolean> salesOrderHandler(PurchasePayableOrder oldPurchasePayNoOrder, List<ChooseSkuDo> chooseSkuCodes,
                                                      List<ArtificialPayOrderDetailSaveDto> detailList) {

        Map<String, Integer> skuCodeAndCountMap = new HashMap<>(16);
        Map<String, Integer> salesStockOutMap = new HashMap<>(16);
        Map<String, Integer> salesReturnGoodMap = new HashMap<>(16);

        if (Objects.nonNull(oldPurchasePayNoOrder)) {
            String relatedOrderNo = oldPurchasePayNoOrder.getRelatedOrderNo();
            Long relatedOrderId = oldPurchasePayNoOrder.getRelatedOrderId();

            Map<String, BigDecimal> chooseSkuMap = chooseSkuCodes.stream().collect(Collectors.toMap(ChooseSkuDo::getSkuCode, ChooseSkuDo::getPrice));

            if (relatedOrderNo.startsWith("CGCK")) {
                skuCodeAndCountMap = iStockOutOrderDetailService.lambdaQuery()
                        .in(StockOutOrderDetail::getStockOutOrderId, relatedOrderId)
                        .in(StockOutOrderDetail::getItemSkuCode, chooseSkuCodes.stream().map(ChooseSkuDo::getSkuCode).collect(Collectors.toList()))
                        .list().stream().filter(val -> {
                            String itemSkuCode = val.getItemSkuCode();
                            BigDecimal decimal = chooseSkuMap.get(itemSkuCode);
                            return Objects.nonNull(decimal) && val.getTaxPrice().compareTo(decimal) == 0;
                        })
                        .collect(Collectors.toMap(StockOutOrderDetail::getItemSkuCode,
                                stockOutOrderDetail -> stockOutOrderDetail.getRealReturnQuantity().equals(0) ?
                                        stockOutOrderDetail.getRealReturnQuantity() :
                                        -stockOutOrderDetail.getRealReturnQuantity()));
            }
            if (relatedOrderNo.startsWith("CGRK")) {
                skuCodeAndCountMap = iStockInOrderDetailService.lambdaQuery()
                        .in(StockInOrderDetail::getStockInOrderId, relatedOrderId)
                        .in(StockInOrderDetail::getItemSkuCode, chooseSkuCodes.stream().map(ChooseSkuDo::getSkuCode).collect(Collectors.toList()))
                        .list().stream().filter(val -> {
                            String itemSkuCode = val.getItemSkuCode();
                            BigDecimal decimal = chooseSkuMap.get(itemSkuCode);
                            return Objects.nonNull(decimal) && val.getTaxPrice().compareTo(decimal) == 0;
                        })
                        .collect(Collectors.toMap(StockInOrderDetail::getItemSkuCode, StockInOrderDetail::getRealReceiptQuantity));
            }
        }

        // 针对新增的自定义的sku的申请付款，这批数据的原采购应付单据是空的

        Map<String, Integer> finalSkuCodeAndCountMap = skuCodeAndCountMap;
        detailList.forEach(detailSaveDto -> {
            Integer integer = finalSkuCodeAndCountMap.getOrDefault(detailSaveDto.getSkuCode(), 0);
            if (Objects.nonNull(integer)) {
                int i = detailSaveDto.getFixedQuantity() - integer;
                if (i < 0) {
                    salesReturnGoodMap.put(detailSaveDto.getSkuCode(), i);
                    log.info("salesOrderHandler salesReturnGoodMap:{}", JSONUtil.toJsonStr(salesReturnGoodMap));
                }
                if (i > 0) {
                    salesStockOutMap.put(detailSaveDto.getSkuCode(), i);
                    log.info("salesOrderHandler salesStockOutMap:{}", JSONUtil.toJsonStr(salesStockOutMap));
                }
            }
        });

        // 推送到金蝶销售出库单
        if (CollUtil.isNotEmpty(salesStockOutMap)) {
            try {
                String jsonParam = kingDeeJsonUtil.saveSaleOutStock(salesStockOutMap, DateUtil.currentTime(), RandomUtil.randomNumbers(8));
                kingDeeReqTemplate.save(jsonParam);
                return SingleResponse.of(true);
            } catch (Exception e) {
                log.error("salesOrderHandler salesStockOutMap pushKingDee error", e);
                return SingleResponse.buildFailure(ErrorCode.API_REQ_ERROR.getCode(), "推送销售出库单异常。" + e.getMessage());
            }
        }
        // 推送到金蝶销售退货单
        if (CollUtil.isNotEmpty(salesReturnGoodMap)) {
            try {
                String jsonParam = kingDeeJsonUtil.saveSaleReturnStock(salesReturnGoodMap, DateUtil.currentTime(), RandomUtil.randomNumbers(8));
                kingDeeReqTemplate.save(jsonParam);
                return SingleResponse.of(true);
            } catch (Exception e) {
                log.error("salesOrderHandler salesReturnGoodMap pushKingDee error", e);
                return SingleResponse.buildFailure(ErrorCode.API_REQ_ERROR.getCode(), "推送销售退货单异常。" + e.getMessage());
            }
        }

        return SingleResponse.of(true);
    }

    private Boolean hadHedgeBefore(String sourcePurchasePayableOrderNo) {
        Integer count = iPurchasePayableOrderRelationService.lambdaQuery()
                .eq(PurchasePayableOrderRelation::getResourceNo, sourcePurchasePayableOrderNo)
                .eq(PurchasePayableOrderRelation::getType, PayOrderRelateType.ALL_HEDGE)
                .count();
        return count > 0;
    }

    /**
     * 保存错误信息
     *
     * @param payableApplyOrderNo 申请付款单编号
     * @param sourcePurchasePayNo 原采购应付单编号
     * @param error               错误信息
     */
    private void saveLog(String payableApplyOrderNo, String sourcePurchasePayNo, String error) {
        iPurchasePayableApplyOrderDetailService.lambdaUpdate().set(PurchasePayableApplyOrderDetail::getErrorLog, error)
                .eq(PurchasePayableApplyOrderDetail::getApplyOrderNo, payableApplyOrderNo)
                .eq(PurchasePayableApplyOrderDetail::getPurchasePayOrderNo, sourcePurchasePayNo)
                .update();
    }

    private void saveRelation(String oldPurchasePayableOrderNo, List<String> hedgeStockOrderNos, PayOrderRelateType type) {
        if (StrUtil.isBlank(oldPurchasePayableOrderNo)) {
            return;
        }
        List<PurchasePayableOrderRelation> collect = hedgeStockOrderNos.stream().map(val -> {
            PurchasePayableOrderRelation relation = new PurchasePayableOrderRelation();
            relation.setResourceNo(oldPurchasePayableOrderNo);
            relation.setRelateStockOrderNo(val);
            relation.setType(type);
            return relation;
        }).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(collect)) {
            iPurchasePayableOrderRelationService.saveBatch(collect);
        }
    }

    @Override
    public SingleResponse<Boolean> compensateHedgeHandler(String payApplyOrderNo, String sourcePurchasePayOrderNo) {
        List<PurchasePayableApplyOrder> list = iPurchasePayableApplyOrderService.lambdaQuery()
                .eq(PurchasePayableApplyOrder::getNo, payApplyOrderNo).list();
        if (CollUtil.isEmpty(list)) {
            throw ExceptionFactory.bizException(ErrorCode.API_RESP_ERROR.getCode(), "申请应付单编号非法");
        }
        PurchasePayableApplyOrder payableApplyOrder = list.get(0);

        List<PurchasePayableApplyOrderDetail> detailList = iPurchasePayableApplyOrderDetailService.lambdaQuery()
                .eq(PurchasePayableApplyOrderDetail::getApplyOrderId, payableApplyOrder.getId())
                .eq(PurchasePayableApplyOrderDetail::getPurchasePayOrderNo, sourcePurchasePayOrderNo)
                .list();
        if (CollUtil.isEmpty(detailList)) {
            throw ExceptionFactory.bizException(ErrorCode.API_RESP_ERROR.getCode(), "申请应付单明细查询为空");
        }

        ArtificialPayOrderSaveDto saveDto = new ArtificialPayOrderSaveDto();
        saveDto.setPayableApplyOrderNo(payableApplyOrder.getNo());
        saveDto.setSourcePurchasePayNo(sourcePurchasePayOrderNo);
        saveDto.setWarehouseNo(detailList.get(0).getWarehouseNo());
        saveDto.setProviderId(payableApplyOrder.getProviderId());
        saveDto.setDetailSaveDtoList(detailList.stream().map(val -> {
            ArtificialPayOrderDetailSaveDto detailSaveDto = new ArtificialPayOrderDetailSaveDto();
            detailSaveDto.setSkuCode(val.getSkuCode());
            detailSaveDto.setFixedQuantity(val.getFixedQuantity());
            detailSaveDto.setFixedTotalAmount(val.getFixedTotalAmount());
            return detailSaveDto;
        }).collect(Collectors.toList()));

        return SingleResponse.of(handler00(Collections.singletonList(saveDto)));
    }


    @Override
    public SingleResponse<Boolean> deleteApplyPay(List<String> applyPayOrderNos) {
        // 付款申请单
        List<PurchasePayableApplyOrder> list = iPurchasePayableApplyOrderService.lambdaQuery()
                .in(PurchasePayableApplyOrder::getNo, applyPayOrderNos).list();
        if (CollUtil.isEmpty(list)) {
            return SingleResponse.of(true);
        }
        // 删除付款申请单
        iPurchasePayableApplyOrderService.removeByIdsWithTime(list.stream().map(PurchasePayableApplyOrder::getId).collect(Collectors.toList()));

        // 付款申请单详情
        List<PurchasePayableApplyOrderDetail> list1 = iPurchasePayableApplyOrderDetailService.lambdaQuery()
                .in(PurchasePayableApplyOrderDetail::getApplyOrderNo, applyPayOrderNos).list();
        // 删除付款申请单详情
        iPurchasePayableApplyOrderDetailService.removeByIdsWithTime(list1.stream().map(PurchasePayableApplyOrderDetail::getId).collect(Collectors.toList()));

        // 付款申请单详情关联到的原采购应付单编号
        List<String> collect = list1.stream().map(PurchasePayableApplyOrderDetail::getPurchasePayOrderNo).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            // 和原采购应付单有关联关系的单据（即冲销原单据产生的数据和重新生成的单据）
            List<PurchasePayableOrderRelation> list2 = iPurchasePayableOrderRelationService.lambdaQuery()
                    .in(PurchasePayableOrderRelation::getResourceNo, collect).list();
            // 删除和原采购应付单有关联关系的单据
            iPurchasePayableOrderRelationService.removeByIdsWithTime(list2.stream().map(PurchasePayableOrderRelation::getId).collect(Collectors.toList()));

            // 和原采购应付单的关联单据编号
            List<String> collect1 = list2.stream().map(PurchasePayableOrderRelation::getRelateStockOrderNo).collect(Collectors.toList());
            // 关联入库单信息
            List<StockInOrder> list3 = iStockInOrderService.lambdaQuery().in(StockInOrder::getNo, collect1).list();
            if (CollUtil.isNotEmpty(list3)) {
                List<Long> collect2 = list3.stream().map(StockInOrder::getId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(collect2)) {
                    // 删除关联入库单
                    iStockInOrderService.removeByIdsWithTime(collect2);
                    // 删除关联入库单详情
                    iStockInOrderDetailService.lambdaUpdate()
                            .set(StockInOrderDetail::getIsDel, 1).set(StockInOrderDetail::getDeletedAt, DateUtil.currentTime())
                            .in(StockInOrderDetail::getStockInOrderId, collect2).update();
                }

            }
            // 关联出库单信息
            List<StockOutOrder> list5 = iStockOutOrderService.lambdaQuery().in(StockOutOrder::getNo, collect1).list();
            if (CollUtil.isNotEmpty(list5)) {
                List<Long> collect2 = list5.stream().map(StockOutOrder::getId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(collect2)) {
                    // 删除关联出库单
                    iStockOutOrderService.removeByIdsWithTime(collect2);
                    // 关联出库单详情
                    iStockOutOrderDetailService.lambdaUpdate()
                            .set(StockOutOrderDetail::getIsDel, 1).set(StockOutOrderDetail::getDeletedAt, DateUtil.currentTime())
                            .in(StockOutOrderDetail::getStockOutOrderId, collect2).update();
                }
            }

            // 关联的出入库单产生的冲销采购应付单
            List<String> collect2 = list3.stream().map(StockInOrder::getNo).collect(Collectors.toList());
            List<String> collect3 = list5.stream().map(StockOutOrder::getNo).collect(Collectors.toList());
            iPurchasePayableOrderRelationService.lambdaUpdate()
                    .set(PurchasePayableOrderRelation::getIsDel, 1).set(PurchasePayableOrderRelation::getDeletedAt, DateUtil.currentTime())
                    .in(PurchasePayableOrderRelation::getRelateStockOrderNo, ListUtil.of(collect2, collect3))
                    .update();
        }

        // 付款申请单审核流程
        List<PayApplyAuditProcess> list2 = iPayApplyAuditProcessService.lambdaQuery()
                .in(PayApplyAuditProcess::getApplyOrderId, list.stream().map(PurchasePayableApplyOrder::getId).collect(Collectors.toList()))
                .list();
        iPayApplyAuditProcessService.removeByIdsWithTime(list2.stream().map(PayApplyAuditProcess::getId).collect(Collectors.toList()));
        // 付款申请单审核流程具体节点
        List<PayApplyAuditProcessNode> list3 = iPayApplyAuditProcessNodeService.lambdaQuery()
                .in(PayApplyAuditProcessNode::getNodeId, list2.stream().map(PayApplyAuditProcess::getNodeId).collect(Collectors.toList()))
                .list();
        iPayApplyAuditProcessNodeService.removeByIdsWithTime(list3.stream().map(PayApplyAuditProcessNode::getId).collect(Collectors.toList()));

        return SingleResponse.of(true);
    }

    @Override
    public Response syncApplyPayBill(Long id) {
        Response handler = kingDeeTemplate.handler(ApiEnum.SAVE_PAY_BILL, id, null);
        return handler;
    }

    public static void main(String[] args) {

    }

}
