package com.daddylab.supplier.item.application.entryActivityPrice;

import com.daddylab.supplier.item.application.entryActivityPrice.models.OpenEntryActivityPriceQuery;
import com.daddylab.supplier.item.application.entryActivityPrice.models.OpenEntryActivityPriceVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/15
 */
public interface OpenEntryActivityPriceService {
    List<OpenEntryActivityPriceVO> query(OpenEntryActivityPriceQuery query);
}
