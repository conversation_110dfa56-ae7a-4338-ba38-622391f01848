package com.daddylab.supplier.item.application.afterSaleLogistics.domain;

import com.daddylab.supplier.item.application.afterSaleLogistics.enums.ErpLogisticsStatus;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/23
 */
@Data
public class LogisticsTraceData {
  private ErpLogisticsStatus status;
  private List<LogisticsTraceItem> trackList;
  private Long trackTime;
  private String receiverAddress;
}
