package com.daddylab.supplier.item.application.afterSalesForwarding.types;

import com.daddylab.supplier.item.common.domain.dto.Command;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/27
 */
@Data
public class AfterSalesForwardingRegisterReportAnomaliesCmd extends Command {
    private static final long serialVersionUID = -7044564877120092024L;

    @NotEmpty
    private List<Long> ids;
}
