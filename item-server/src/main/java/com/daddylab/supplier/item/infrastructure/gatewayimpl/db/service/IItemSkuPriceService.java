package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSkuPrice;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 商品sku价格 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-07
 */
public interface IItemSkuPriceService extends IDaddyService<ItemSkuPrice> {

    List<ItemSkuPrice> listPriceBySkuIds(Integer type, Collection<Long> skuIds);
}
