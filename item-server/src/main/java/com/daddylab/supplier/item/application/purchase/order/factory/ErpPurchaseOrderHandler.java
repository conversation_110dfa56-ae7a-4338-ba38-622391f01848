package com.daddylab.supplier.item.application.purchase.order.factory;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizService;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.WrapperType;
import com.daddylab.supplier.item.application.stockInOrder.StockInOrderBizService;
import com.daddylab.supplier.item.application.stockOutOrder.StockOutOrderBizService;
import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderCmd;
import com.daddylab.supplier.item.controller.stockout.dto.StockOutOrderCmd;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailWrapperMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2022年08月19日 11:04 AM
 */
@Service
@Slf4j
public class ErpPurchaseOrderHandler {

    @Resource
    WdtOrderDetailMapper wdtOrderDetailMapper;

    @Resource
    IWdtOrderDetailWrapperService iWdtOrderDetailWrapperService;

    @Resource
    WdtOrderDetailWrapperMapper wdtOrderDetailWrapperMapper;

    @Resource
    PurchaseOrderMapper purchaseOrderMapper;

    @Resource
    IPurchaseOrderService iPurchaseOrderService;

    @Resource
    StockInOrderBizService stockInOrderBizService;

    @Resource
    IStockInOrderService iStockInOrderService;

    @Resource
    IStockInOrderDetailService iStockInOrderDetailService;

    @Resource
    StockOutOrderBizService stockOutOrderBizService;

    @Resource
    IStockOutOrderService iStockOutOrderService;

    @Resource
    IStockOutOrderDetailService iStockOutOrderDetailService;

    @Resource
    IPurchaseOrderDetailService iPurchaseOrderDetailService;

    @Resource
    IPurchasePayableOrderService iPurchasePayableOrderService;

    @Resource
    IOrderSettlementDetailService iOrderSettlementDetailService;

    @Resource
    IOrderSettlementFormService iOrderSettlementFormService;

    @Resource
    ProviderGateway providerGateway;


    public void runStatistics(String targetMonth) {

        // 物理删除旧的目标月份的统计数据
        wdtOrderDetailWrapperMapper.deleteOldStatisticsData(targetMonth);

        Integer providerIdCount = wdtOrderDetailMapper.getProviderIdCount(targetMonth);
        log.info("--------【{}】根据供应商汇总统计wdt订单数据，供应商的数量汇总：{} -------", targetMonth, providerIdCount);
        if (providerIdCount == 0) {
            return;
        }

        List<Long> providerIdList = wdtOrderDetailMapper.getProviderId(targetMonth);
        AtomicReference<Integer> count = new AtomicReference<>(0);

        providerIdList.forEach(providerId -> {
            try {
                singleProviderStatistics(providerId, targetMonth);
            } catch (Exception e) {
                log.error("--------【{}】根据供应商汇总统计wdt订单数据异常，providerId：{} -------", targetMonth, providerId, e);
            } finally {
                count.set(count.get() + 1);
                log.info("--------【{}】根据供应商汇总统计wdt订单数据，已执行数量：{} -------", targetMonth, count.get());
            }
        });
        log.info("--------【{}】根据供应商汇总统计wdt订单数据全部结束 -------", targetMonth);

    }


    /**
     * 单个供应商，统计逻辑处理
     *
     * @param providerId
     * @param targetMonth
     */
    public void singleProviderStatistics(Long providerId, String targetMonth) {
        List<String> skuCodes = wdtOrderDetailWrapperMapper.selectStaticSkuCode(providerId, targetMonth);
        skuCodes.forEach(skuCode -> {
            try {
                List<WdtOrderDetailWrapper> skuResult = skuWmsStatics(providerId, skuCode, targetMonth);
                // save statics info
                if (CollUtil.isNotEmpty(skuResult)) {
                    iWdtOrderDetailWrapperService.saveBatch(skuResult);
                }
                // create purchase order by statics info

            } catch (Exception e) {
                log.error("------ skuCode:{}，处理汇总异常 ------ ", skuCode, e);
            }
        });
    }

    private List<WdtOrderDetailWrapper> skuWmsStatics(Long providerId, String skuCode, String targetMonth) {

        // 根据sku,捞出这个供应商在这个周期内，所有的wrapper数据。
        List<WdtOrderDetailWrapper> wrapperList = wdtOrderDetailWrapperMapper.selectStaticInfo(providerId, targetMonth, skuCode);
        if (CollUtil.isEmpty(wrapperList)) {
            return null;
        }

        List<WdtOrderDetailWrapper> resultList = new LinkedList<>();

        // 1.根据仓库编号切分
        wrapperList.stream().collect(Collectors.groupingBy(WdtOrderDetailWrapper::getWarehouseNo)).forEach((warehouse, warehouseList) -> {

            // 2.根据价格切分
            warehouseList.stream().collect(Collectors.groupingBy(WdtOrderDetailWrapper::getPrice)).forEach((price, priceList) -> {

                // 3.根据业务线切分
                priceList.stream().collect(Collectors.groupingBy(WdtOrderDetailWrapper::getBusinessLine)).forEach((businessLine, businessLineList) -> {

                    // 在根据数据类型聚合，统计各个类型数据的数量
                    AtomicInteger totalSum = new AtomicInteger();
                    AtomicInteger deliverSum = new AtomicInteger(0);
                    AtomicInteger refundSum = new AtomicInteger();
                    businessLineList.stream().collect(Collectors.groupingBy(WdtOrderDetailWrapper::getType)).forEach((type, typeList) -> {
                        int thisTypeQuantitySum = typeList.stream().mapToInt(WdtOrderDetailWrapper::getQuantity).sum();
                        if (Objects.equals(type, WrapperType.STOCK_OUT_SINGLE.getValue())
                                || Objects.equals(type, WrapperType.STOCK_OUT_COMBINATION.getValue())) {
                            totalSum.set(totalSum.get() + thisTypeQuantitySum);
                            deliverSum.set(deliverSum.get() + thisTypeQuantitySum);
                        }
                        if (Objects.equals(type, WrapperType.STOCK_IN_REFUND.getValue())
                                || Objects.equals(type, WrapperType.STOCK_IN_PRE.getValue())) {
                            totalSum.set(totalSum.get() - thisTypeQuantitySum);
                            refundSum.set(refundSum.get() + thisTypeQuantitySum);
                        }
                    });

                    WdtOrderDetailWrapper resultOne = new WdtOrderDetailWrapper();
                    resultOne.setPrice(price);
                    resultOne.setQuantity(totalSum.get());
                    resultOne.setSkuCode(skuCode);
                    resultOne.setProviderId(providerId);
                    resultOne.setOperateTime(targetMonth);
                    resultOne.setType(WrapperType.TOTAL.getValue());
                    resultOne.setWarehouseNo(warehouse);
                    resultOne.setDeliverNum(deliverSum.get());
                    resultOne.setRefundNum(refundSum.get());
                    resultOne.setBusinessLine(businessLine);
                    resultList.add(resultOne);

                });
            });
        });

        return resultList;
    }


    // ---------------------------------------- 手动分割线 ---------------------------------------------------------------


//    @Data
//    public static class TimeStampBO {
//        String targetMonth;
//        Long start;
//        Long end;
//
//        public static TimeStampBO of(String targetMonth) {
//            LocalDateTime targetMonthDt = DateUtil.parse(targetMonth, "yyyyMM");
//            Long start = DateUtil.getFirstDayOfMonth(targetMonthDt).atZone(ZoneId.systemDefault()).toEpochSecond();
//            Long end = DateUtil.getFirstDayOfMonth(targetMonthDt).plusMonths(1).atZone(ZoneId.systemDefault()).toEpochSecond();
//
//            TimeStampBO one = new TimeStampBO();
//            one.setTargetMonth(targetMonth);
//            one.setStart(start);
//            one.setEnd(end);
//
//            return one;
//        }
//    }

    public static void main(String[] args) {
        LocalDateTime localDateTime = DateUtil.parse("202307", "yyyyMM");
        LocalDateTime lastDayOfMonth = DateUtil.getLastDayOfMonth(localDateTime);
        long epochSecond = lastDayOfMonth.toInstant(ZoneOffset.of("+8")).getEpochSecond();
        System.out.println(epochSecond);
    }

    public void purchaseOrderGenerate(TimeBO timeBo) {

        try {
            processingInvalidData(timeBo.getMonthStartTime(), timeBo.getMonthEndTime());
        } catch (Exception e) {
            log.error("purchaseOrderGenerate delete old data fail", e);
            throw e;
        }

        AtomicInteger count = new AtomicInteger(0);
        // 计算targetMonth月份的最后一天的0点，这个时间作为采购单的生成时间。
        long epochSecond = timeBo.getPurchaseDateTime();
        wdtOrderDetailMapper.getProviderIdStatisticsList(timeBo.getOperateMonth())
                .forEach(providerId -> {
                    // 以供应商为维度，开始生成系统采购单。
                    List<WdtOrderDetailWrapper> providerList =
                            wdtOrderDetailWrapperMapper.selectTotalStaticInfo(providerId, timeBo.getOperateMonth());
                    if (CollUtil.isEmpty(providerList)) {
                        return;
                    }

                    // 再根据仓库切分，根据金蝶规则，同一采购单下，不允许出现重复的仓库编号
                    Map<String, List<WdtOrderDetailWrapper>> warehouseNoMap = providerList.stream()
                            .collect(Collectors.groupingBy(WdtOrderDetailWrapper::getWarehouseNo));
                    warehouseNoMap.forEach((warehouseNo, warehouseNoList) -> {
                        // 再根据wrapper的合作模式做拆分
                        Map<Integer, List<WdtOrderDetailWrapper>> businessLineMap =
                                warehouseNoList.stream().collect(Collectors.groupingBy(WdtOrderDetailWrapper::getBusinessLine));
                        businessLineMap.forEach((businessLine, businessLineList) -> {
                            try {
                                // 生成系统采购单请求参数
                                PurchaseOrderCmd purchaseOrderCmd = PurchaseOrderCmd.sysPurchaseCmdBuild(providerId,
                                        businessLine, businessLineList, epochSecond, warehouseNo);
                                SingleResponse<Long> response = SpringUtil.getBean(PurchaseOrderBizService.class)
                                        .sysSave(purchaseOrderCmd);
                                if (!response.isSuccess()) {
                                    log.error("系统采购单生成失败，{},{}", providerId, response.getErrMessage());
                                }
                            } catch (Exception e) {
                                log.error("系统采购单生成异常,providerId:{}", providerId, e);
                            } finally {
                                count.getAndIncrement();
                                log.info("【{}】供应商系统采购单生成已处理数量累计。count:{}", timeBo.getOperateMonth(), count.get());
                            }
                        });
                    });
                });

        log.info("【{}】供应商系统采购单全部生成完毕 ", timeBo.getOperateMonth());
    }

    /**
     * 处理作废数据，检查删除数据
     * 1.采购订单
     * 2.出入库单及下游的应付单
     * 3.采购结算单
     *
     * @param startTime
     * @param endTime
     */
    private void processingInvalidData(Long startTime, Long endTime) {

        List<PurchaseOrder> purchaseOrders = iPurchaseOrderService.lambdaQuery()
                .eq(PurchaseOrder::getType, 2)
                .eq(PurchaseOrder::getSource, 1)
                .between(PurchaseOrder::getPurchaseDate, startTime, endTime)
                .select(PurchaseOrder::getId, PurchaseOrder::getNo).list();

        // 删除入库单和出库单和关联的应付单数据
        List<Long> removedPurchaseOrderIdList = purchaseOrders.stream().map(PurchaseOrder::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(removedPurchaseOrderIdList)) {
            List<Long> stockInOrderIdList = iStockInOrderService.lambdaQuery()
                    .in(StockInOrder::getPurchaseOrderId, removedPurchaseOrderIdList)
                    .select(StockInOrder::getId).list().stream().map(StockInOrder::getId).collect(Collectors.toList());
            iStockInOrderService.removeByIdsWithTime(stockInOrderIdList);

            List<Long> stockOutOrderIdList = iStockOutOrderService.lambdaQuery()
                    .in(StockOutOrder::getPurchaseOrderId, removedPurchaseOrderIdList)
                    .select(StockOutOrder::getId).list().stream().map(StockOutOrder::getId).collect(Collectors.toList());
            iStockOutOrderService.removeByIdsWithTime(stockOutOrderIdList);

            List<Long> removeIdList = new LinkedList<>(stockInOrderIdList);
            removeIdList.addAll(stockOutOrderIdList);
            QueryWrapper<PurchasePayableOrder> removeWrapper = new QueryWrapper<>();
            removeWrapper.in("related_order_id", removeIdList);
            if (CollUtil.isNotEmpty(removeIdList)) {
                iPurchasePayableOrderService.removeWithTime(removeWrapper);
            }
        }

        // 删除和此采购订单关联的 结算单/结算明细
        List<String> purchaseOrderNos = purchaseOrders.stream().map(PurchaseOrder::getNo).collect(Collectors.toList());
        if (CollUtil.isEmpty(purchaseOrderNos)) {
            return;
        }
        List<Long> settlementFormIds = iOrderSettlementFormService.lambdaQuery()
                .in(OrderSettlementForm::getPurchaseOrderNo, purchaseOrderNos)
                .and(ww -> ww.isNull(OrderSettlementForm::getNo).or().eq(OrderSettlementForm::getNo, ""))
                .select(OrderSettlementForm::getId).list()
                .stream().map(OrderSettlementForm::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(settlementFormIds)) {
            iOrderSettlementFormService.removeByIds(settlementFormIds);
            iOrderSettlementDetailService.lambdaUpdate()
                    .in(OrderSettlementDetail::getFormId, settlementFormIds).remove();
        }

        // 最后删除采购单。
        purchaseOrderMapper.deleteSysOrder(startTime, endTime);

    }


    // -------------------------------------------- 手动分割线 ------------------------------------------------------

    @Resource
    ILogService iLogService;

    public void erpStockInOrOutOrderGenerator(TimeBO timeBO, List<Long> providerIdList) {

        AtomicInteger count = new AtomicInteger();
        iPurchaseOrderService.lambdaQuery().eq(PurchaseOrder::getType, 2)
                .eq(PurchaseOrder::getSource, 1)
                .between(PurchaseOrder::getPurchaseDate, timeBO.getMonthStartTime(), timeBO.getMonthEndTime())
                .in(CollUtil.isNotEmpty(providerIdList), PurchaseOrder::getProviderId, providerIdList)
                .orderByDesc(PurchaseOrder::getId).list()
                .forEach(purchaseOrder -> {
                    List<Log> logList = providerOrderConvertStockOrder(purchaseOrder, false);
                    iLogService.saveBatch(logList);
                    count.getAndIncrement();
                    log.info("系统采购单终章，出库单/入库单处理。累计数量:{}", count.get());
                });
    }


    public List<Log> providerOrderConvertStockOrder(PurchaseOrder purchaseOrder, Boolean mockSync) {

        List<Log> logList = new LinkedList<>();
        List<PurchaseOrderDetail> detailList = iPurchaseOrderDetailService.getDetailList(purchaseOrder.getId());
        List<PurchaseOrderDetail> stockInList = detailList.stream().filter(val -> val.getPurchaseQuantity() > 0).collect(Collectors.toList());
        List<PurchaseOrderDetail> stockOutList = detailList.stream().filter(val -> val.getPurchaseQuantity() < 0).collect(Collectors.toList());

        // 采购数量大于0，生成erp的入库单，同步到进的的采购入库单
        if (CollUtil.isNotEmpty(stockInList)) {
            Log log1 = new Log();
            log1.setReq(purchaseOrder.getNo());
            try {
                // 删除和次采购单相关的入库单数据
                List<StockInOrder> list = iStockInOrderService.lambdaQuery().eq(StockInOrder::getPurchaseOrderId, purchaseOrder.getId()).list();
                if (CollUtil.isNotEmpty(list)) {
                    List<Long> stockInOrderIds = list.stream().map(StockInOrder::getId).collect(Collectors.toList());
                    iStockInOrderService.removeByIdsWithTime(stockInOrderIds);

                    QueryWrapper<StockInOrderDetail> wrapper = new QueryWrapper<>();
                    wrapper.in("stock_in_order_id", stockInOrderIds);
                    iStockInOrderDetailService.removeWithTime(wrapper);
                }

                // 创建采购入库单，并且将这个采购入库单同步到金蝶。
                StockInOrderAndOrderDetail detail =
                        StockInOrderAndOrderDetail.ofPurchaseOrder(
                                purchaseOrder,
                                stockInList,
                                false,
                                providerGateway
                                        .partnerQueryByProviderId(purchaseOrder.getProviderId())
                                        .orElse(null));
                SingleResponse<String> response = stockInOrderBizService.createStockInOrder(detail, true, mockSync);
                if (!response.isSuccess()) {
                    log1.setType(-98);
                    log1.setError(response.getErrMessage());
                } else {
                    log1.setResp(response.getData());
                    log1.setType(98);
                }
            } catch (Exception e) {
                log.error("入库单同步处理异常，purchaseOrderNo:{}", purchaseOrder.getNo(), e);
                log1.setType(-98);
                log1.setError("入库单同步处理异常。" + e.getMessage());
            }
            logList.add(log1);
        }
        // 采购数量小于0，生成erp的出库单，同步到金蝶的退料出库单。
        if (CollUtil.isNotEmpty(stockOutList)) {
            Log log2 = new Log();
            log2.setReq(purchaseOrder.getNo());
            try {
                // 删除和次采购单相关的出库单数据
                List<StockOutOrder> list = iStockOutOrderService.lambdaQuery().eq(StockOutOrder::getPurchaseOrderId, purchaseOrder.getId()).list();
                if (CollUtil.isNotEmpty(list)) {
                    List<Long> stockOutOrderIds = list.stream().map(StockOutOrder::getId).collect(Collectors.toList());
                    iStockOutOrderService.removeByIdsWithTime(stockOutOrderIds);

                    QueryWrapper<StockOutOrderDetail> wrapper = new QueryWrapper<>();
                    wrapper.in("stock_out_order_id", stockOutOrderIds);
                    iStockOutOrderDetailService.removeWithTime(wrapper);
                }

                StockOutOrderCmd stockOutOrderCmd = StockOutOrderCmd.of(purchaseOrder, stockOutList);
                SingleResponse<String> response1 = stockOutOrderBizService.syncStockOutOrder(stockOutOrderCmd, mockSync);
                if (!response1.isSuccess()) {
                    log2.setType(-99);
                    log2.setError(response1.getErrMessage());
                } else {
                    log2.setType(99);
                    log2.setResp(response1.getData());
                }
            } catch (Exception e) {
                log.error("出库单同步处理异常，purchaseOrderNo:{}", purchaseOrder.getNo(), e);
                log2.setType(-99);
                log2.setError("出库单同步处理异常。" + e.getMessage());
            }
            logList.add(log2);
        }
        try {
            TimeUnit.SECONDS.sleep(1);
        } catch (Exception e) {
            // ignore
        }
        return logList;
    }


    // ---------------------------------- 手动分割线 ----------------------------------------------------

    /**
     * 从error log中捞出 生成采购出入库单异常单的采购订单编码，
     * 重新生成采购出入库单（同步金蝶）
     */
    public void erpStockInOrOutOrderGeneratorCompensate(List<String> purchaseOrderNoList) {
        iPurchaseOrderService.lambdaQuery().in(PurchaseOrder::getNo, purchaseOrderNoList)
                .select().list()
                .forEach(val -> {
                    // 补充，先处理下SKU的单位同步问题。
                    List<Log> logs = providerOrderConvertStockOrder(val, false);
                    iLogService.saveBatch(logs);
                });
    }


    // ---------------------------------- 手动分割线 ----------------------------------------------------


}
