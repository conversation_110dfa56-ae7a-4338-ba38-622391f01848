package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatMsg;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WechatMsgMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWechatMsgService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-03
 */
@Service
public class WechatMsgServiceImpl extends DaddyServiceImpl<WechatMsgMapper, WechatMsg> implements IWechatMsgService {

    @Override
    public boolean updateState(List<Long> ids, int from, int to) {
        final int updateSuccessNum = getDaddyBaseMapper().updateState(ids, from, to);
        if (updateSuccessNum < ids.size()) {
            throw new RuntimeException(String.format("企微消息状态批量更新失败 ids=%s from=%s to=%s", ids.stream().map(
                    Objects::toString).collect(Collectors.joining(",")), from, to));
        }
        return true;
    }
}
