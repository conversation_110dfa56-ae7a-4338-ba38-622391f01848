package com.daddylab.supplier.item.application.nuonuo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.nuonuo.dto.InvoiceNewCmd;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AllChannelBillData;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AllChannelBillOrderData;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NuoNuoInvoiceProcessRecord;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NuoNuoInvoiceRequest;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAllChannelBillDataService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAllChannelBillOrderDataService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INuoNuoInvoiceProcessRecordService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INuoNuoInvoiceRequestService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.nuonuo.NuoNuoOpenGateway;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2024年07月26日 11:26 AM
 */

@AllArgsConstructor
@Slf4j
@Service
public class NuoNuoBizServiceImpl implements NuoNuoBizService {

    private final INuoNuoInvoiceRequestService iNuoNuoInvoiceRequestService;
    private final NuoNuoOpenGateway nuoNuoOpenGateway;
    private final INuoNuoInvoiceProcessRecordService iNuoNuoInvoiceProcessRecordService;
    private final RedissonClient redissonClient;
    private final IAllChannelBillDataService iAllChannelBillDataService;
    private final IAllChannelBillOrderDataService iAllChannelBillOrderDataService;

    private static List<String> WHITE_PLATFORM_LIST = new LinkedList<>();

    static {
        WHITE_PLATFORM_LIST.add("快手");
        WHITE_PLATFORM_LIST.add("淘宝");
        WHITE_PLATFORM_LIST.add("抖店");
        WHITE_PLATFORM_LIST.add("微信");
        WHITE_PLATFORM_LIST.add("小红书");
        WHITE_PLATFORM_LIST.add("有赞");
        WHITE_PLATFORM_LIST.add("拼多多");
        WHITE_PLATFORM_LIST.add("快团团");
        WHITE_PLATFORM_LIST.add("网易严选");
        WHITE_PLATFORM_LIST.add("自研电商");
        WHITE_PLATFORM_LIST.add("微信视频号");
    }

    private static List<String> WHITE_SHOP_LIST = new LinkedList<>();

    static {
        WHITE_SHOP_LIST.add("老爸评测(快手小店)");
        WHITE_SHOP_LIST.add("老爸评测会员店");
        WHITE_SHOP_LIST.add("老爸评测小店");
        WHITE_SHOP_LIST.add("微信视频号小店");
        WHITE_SHOP_LIST.add("视频号小店老爸会员商城");
        WHITE_SHOP_LIST.add("老爸评测的店");
        WHITE_SHOP_LIST.add("老爸良心推荐");
        WHITE_SHOP_LIST.add("老爸评测");
        WHITE_SHOP_LIST.add("老爸美妆专营店");
        WHITE_SHOP_LIST.add("老爸家居生活专营店");
        WHITE_SHOP_LIST.add("老爸美妆专营店");
        WHITE_SHOP_LIST.add("Daddylab推荐-团长端");
        WHITE_SHOP_LIST.add("Daddylab推荐-供应商端");
        WHITE_SHOP_LIST.add("老爸评测");
        // 电商灰度测试店铺
        WHITE_SHOP_LIST.add("杭州老爸标准技术集团");
    }


    /**
     * 请求参数转为开票请求数据
     *
     * @param orderNo 订单号
     * @param cmd     一般请求参数
     * @return
     */
    private NuoNuoInvoiceRequest buildRequest(String orderNo, InvoiceNewCmd cmd) {
        NuoNuoInvoiceRequest request = new NuoNuoInvoiceRequest();
        request.setOrderNo(orderNo);
        request.setInvoiceType(cmd.getInvoiceType());
        request.setInvoiceTitleType(cmd.getInvoiceTitleType());
        request.setInvoiceTitle(cmd.getInvoiceTitle());
        request.setTaxCode(cmd.getTaxCode());
        request.setBank(cmd.getBank());
        request.setBankNo(cmd.getBankNo());
        request.setCompanyAddress(cmd.getCompanyAddress());
        request.setCompanyPhone(cmd.getCompanyPhone());
        request.setMailAddress(cmd.getMailAddress());
        return request;
    }

    @Override
    public Response verifyOrderNoList(List<String> orderNoList) {
        Assert.state(CollUtil.isNotEmpty(orderNoList), "开票订单号不得为空");
        orderNoList = orderNoList.stream().distinct().collect(Collectors.toList());

        List<AllChannelBillData> allChannelBillDataList = iAllChannelBillDataService.lambdaQuery()
                .in(AllChannelBillData::getOrderId, orderNoList)
                .list();
        // 抖音的订单会加一个A的后缀
        if (CollUtil.isEmpty(allChannelBillDataList)) {
            orderNoList = orderNoList.stream()
                    .map(val -> val + "A").collect(Collectors.toList());
            allChannelBillDataList = iAllChannelBillDataService.lambdaQuery()
                    .in(AllChannelBillData::getOrderId, orderNoList)
                    .list();
        }
        final Map<String, List<AllChannelBillData>> billDataListMap = allChannelBillDataList.stream()
                .collect(Collectors.groupingBy(AllChannelBillData::getOrderId));
        final List<NuoNuoInvoiceProcessRecord> successRecords = iNuoNuoInvoiceProcessRecordService.lambdaQuery()
                .eq(NuoNuoInvoiceProcessRecord::getStatus, 1)
                .in(NuoNuoInvoiceProcessRecord::getOrderNo, orderNoList)
                .list();
        final Set<String> historyReqOrderNoSet = successRecords.stream()
                .map(NuoNuoInvoiceProcessRecord::getOrderNo).collect(Collectors.toSet());
        final List<AllChannelBillOrderData> orderDataList = iAllChannelBillOrderDataService.lambdaQuery()
                .in(AllChannelBillOrderData::getOrderId, orderNoList)
                .list();
        final Set<String> orderDataOrderNoSet = orderDataList.stream()
                .map(AllChannelBillOrderData::getOrderId).collect(Collectors.toSet());

        for (String reqOrderNo : orderNoList) {
            if (!billDataListMap.containsKey(reqOrderNo)) {
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR,
                        StrUtil.format("此订单系统无法开票，请联系客服处理。订单号：{}", reqOrderNo));
            }
            List<AllChannelBillData> allChannelBillData = billDataListMap.get(reqOrderNo);

            final long illegalPlatformCount = allChannelBillData.stream()
                    .filter(val -> !WHITE_PLATFORM_LIST.contains(val.getPlatName())).count();
            if (illegalPlatformCount > 0) {
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR,
                        StrUtil.format("目前不支持此订单开票，平台不支持。订单号：{}", reqOrderNo));
            }
            final long illegalShopCount = allChannelBillData.stream()
                    .filter(val -> !WHITE_SHOP_LIST.contains(val.getShopName())).count();
            if (illegalShopCount > 0) {
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR,
                        StrUtil.format("目前不支持此订单开票，店铺不支持。订单号：{}", reqOrderNo));
            }
            // 1.存在开票金额。2.没有全额退款
            final long canInvoiceCount = allChannelBillData.stream()
                    .filter(val -> {
                        final BigDecimal invoiceAmt = Objects.isNull(val.getInvoiceAmt()) ? BigDecimal.ZERO : val.getInvoiceAmt();
                        boolean b1 = invoiceAmt.compareTo(BigDecimal.ZERO) > 0;
                        boolean b2 = invoiceAmt.compareTo(val.getPostFee()) != 0;
                        return b1 && b2;
                    }).count();
            if (canInvoiceCount <= 0) {
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR,
                        StrUtil.format("当前订单下，没有可以开票的订单明细。订单号：{}", reqOrderNo));
            }
            // 正在退款中的数量
            final long inRefundCount = allChannelBillData.stream()
                    .filter(val -> val.getIsRefund() == 1).count();
            if (inRefundCount > 0) {
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR,
                        StrUtil.format("当前订单未完成，不支持开票，请联系客服处理！订单号：{}", reqOrderNo));
            }

            final long taxCodeIsEmptyCount = allChannelBillData.stream()
                    .filter(val -> StrUtil.isEmpty(val.getTaxClassCode())).count();
            if (taxCodeIsEmptyCount > 0) {
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR,
                        StrUtil.format("开票异常：无法开票，请联系人工客服处理！订单号：{}", reqOrderNo));
            }
            // 订单状态，0:未知,1:待支付,2:定金已支付,3:已支付,4:部分已发货,5:全部已发货,6:已完成,7:已取消
            long unFinishedCount = allChannelBillData.stream()
                    .filter(val ->
                            val.getOrderStatus() != 6).count();
            if (unFinishedCount > 0) {
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR,
                        StrUtil.format("当前订单状态未完成，请完成后再申请！订单号：{}", reqOrderNo));
            }

            allChannelBillData.stream().map(
                            val -> Objects.isNull(val.getInvoiceAmt()) ? BigDecimal.ZERO : val.getInvoiceAmt())
                    .reduce(BigDecimal::add)
                    .ifPresent(val -> {
                        if (val.compareTo(BigDecimal.ZERO) == 0) {
                            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR,
                                    StrUtil.format("当前订单实付金额为0，无法开票，请联系人工客服处理！订单号：{}", reqOrderNo));
                        }
                    });

            if (historyReqOrderNoSet.contains(reqOrderNo) || historyReqOrderNoSet.contains(reqOrderNo + "A")) {
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR,
                        StrUtil.format("当前订单已开具发票，请查看收票邮箱是否收到，若未收到，请联系客服查询发票或重新线下开具！订单号：{}", reqOrderNo));
            }

            boolean thisOrderNoHaveInvoiceAmount = orderDataOrderNoSet.contains(reqOrderNo) || orderDataOrderNoSet.contains(reqOrderNo + "A");
            if (!thisOrderNoHaveInvoiceAmount) {
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR,
                        StrUtil.format("当前订单计算开票金额失败，请联系客服！订单号：{}", reqOrderNo));
            }

        }

        final long count1 = allChannelBillDataList.stream().map(AllChannelBillData::getShopName).distinct().count();
        Assert.state(count1 == 1, "仅支持同一个店铺下的订单进行批量申请，不同店铺订单请分开提交申请！");

        return Response.buildSuccess();
    }


    /**
     * 开具发票
     *
     * @param cmd
     * @return
     */
    @Override
    public Response requestInvoiceNew(InvoiceNewCmd cmd) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {

            for (String orderNo : cmd.getOrderNoList()) {
                final RLock lock = redissonClient.getLock("invoice_" + orderNo);
                lock.lock();
                try {
                    List<AllChannelBillData> list = iAllChannelBillDataService.lambdaQuery()
                            .eq(AllChannelBillData::getOrderId, orderNo)
                            .list();
                    if (CollUtil.isEmpty(list)) {
                        orderNo = orderNo + "A";
                        list = iAllChannelBillDataService.lambdaQuery()
                                .eq(AllChannelBillData::getOrderId, orderNo)
                                .list();
                    }
                    Assert.state(CollUtil.isNotEmpty(list), "订单明细查询为空。orderNo:" + orderNo);

                    BigDecimal definitelyCorrect = null;
                    final List<AllChannelBillOrderData> orderDataList = iAllChannelBillOrderDataService.lambdaQuery()
                            .in(AllChannelBillOrderData::getOrderId, ListUtil.of(orderNo, orderNo + "A"))
                            .orderByDesc(AllChannelBillOrderData::getId).list();
                    if (CollUtil.isNotEmpty(orderDataList)) {
                        definitelyCorrect = orderDataList.get(0).getInvoiceAmt();
                    }
                    Assert.notNull(definitelyCorrect, "订单开票金额计算失败");

                    list = list.stream()
                            .filter(val -> {
                                BigDecimal amount = Objects.isNull(val.getInvoiceAmt()) ? BigDecimal.ZERO : val.getInvoiceAmt();
                                boolean b1 = amount.compareTo(BigDecimal.ZERO) > 0;
                                boolean b2 = val.getIsRefund() == 0;
                                return b1 && b2;
                            })
                            .collect(Collectors.toList());

                    final NuoNuoInvoiceRequest nuoNuoInvoiceRequest = buildRequest(orderNo, cmd);
                    iNuoNuoInvoiceRequestService.save(nuoNuoInvoiceRequest);
                    iNuoNuoInvoiceProcessRecordService.save(NuoNuoInvoiceProcessRecord.buildRecord(orderNo, 0, "开票参数构建完成"));

                    nuoNuoOpenGateway.requestBillingNew(nuoNuoInvoiceRequest, list, definitelyCorrect);

                } catch (Exception e) {
                    log.error("处理开票请求异常", e);
                    iNuoNuoInvoiceProcessRecordService.save(NuoNuoInvoiceProcessRecord.buildRecord(orderNo, -10, e.getMessage()));
                } finally {
                    lock.unlock();
                }
            }

        });

        return Response.buildSuccess();
    }


    /**
     * 针对已请求过开具的数据，进行重试
     *
     * @param orderNo 订单编号
     * @return
     */
    @Override
    public Response requestInvoiceNewReTry(String orderNo) {
        final List<NuoNuoInvoiceRequest> list1 = iNuoNuoInvoiceRequestService.lambdaQuery()
                .eq(NuoNuoInvoiceRequest::getOrderNo, orderNo)
                .orderByDesc(NuoNuoInvoiceRequest::getId)
                .list();
        Assert.state(CollUtil.isNotEmpty(list1), "开票请求参数不得为空");

        final List<AllChannelBillData> list2 = iAllChannelBillDataService.lambdaQuery()
                .eq(AllChannelBillData::getOrderId, orderNo)
                .list();
        Assert.state(CollUtil.isNotEmpty(list2), "开票订单列表数据不得为空");

        final List<AllChannelBillOrderData> list3 = iAllChannelBillOrderDataService.lambdaQuery()
                .eq(AllChannelBillOrderData::getOrderId, orderNo)
                .list();
        Assert.state(CollUtil.isNotEmpty(list3), "开票主订单数据不得为空");

        nuoNuoOpenGateway.requestBillingNew(list1.get(0), list2, list3.get(0).getInvoiceAmt());

        return Response.buildSuccess();
    }


    // -----------------------------------------------------------------------------------------------------------------

    /**
     * 全额冲红
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public Response creditNoteApply(Long startTime, Long endTime) {
        final List<AllChannelBillData> list = iAllChannelBillDataService.lambdaQuery()
                .between(AllChannelBillData::getRefundFinishTime, startTime, endTime)
                .list();
        return creditNoteApply0(list);
    }

    private Response creditNoteApply0(List<AllChannelBillData> list) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL,
                () -> list.stream().collect(Collectors.groupingBy(AllChannelBillData::getOrderId))
                        .forEach((orderNo, orderList) -> {
                            boolean allRefund = orderList.stream()
                                    .filter(val -> Objects.nonNull(val.getRefundFinishTime()) && 0L != val.getRefundFinishTime())
                                    .count() == orderList.size();
                            final List<NuoNuoInvoiceRequest> invoiceRequestList = iNuoNuoInvoiceRequestService.getByOrderNo(orderNo);
                            final List<NuoNuoInvoiceProcessRecord> recordList = iNuoNuoInvoiceProcessRecordService.getByStatus(orderNo, 1);
                            boolean access = CollUtil.isNotEmpty(invoiceRequestList) &&
                                    CollUtil.isNotEmpty(recordList) &&
                                    allRefund;
                            // 全额退款 && 此订单存在开票成功的记录
                            if (access) {
                                nuoNuoOpenGateway.requestCreditNoteApply(orderNo);
                            }
                        }));
        return Response.buildSuccess();
    }

//    @Override
//    public Response creditNoteApply(List<String> orderNoList) {
//        final List<AllChannelBillData> list = iAllChannelBillDataService.lambdaQuery()
//                .in(AllChannelBillData::getOrderId, orderNoList)
//                .list();
//
//        return creditNoteApply0(list);
//
//    }


    public SingleResponse<BigDecimal> calculateInvoicedAmount(List<String> orderNoList) {
        if (CollUtil.isEmpty(orderNoList)) {
            return SingleResponse.of(BigDecimal.ZERO);
        }
        final List<String> collect = orderNoList.stream().map(val -> val + "A").collect(Collectors.toList());
        orderNoList.addAll(collect);

        final List<AllChannelBillOrderData> list = iAllChannelBillOrderDataService.lambdaQuery()
                .in(AllChannelBillOrderData::getOrderId, orderNoList)
                .list();
        Assert.state(CollUtil.isNotEmpty(list), "开票金额查询失败");

        final Optional<BigDecimal> reduce = list.stream().map(
                        val -> Objects.isNull(val.getInvoiceAmt()) ? BigDecimal.ZERO : val.getInvoiceAmt())
                .reduce(BigDecimal::add);
        if (reduce.isPresent()) {
            return SingleResponse.of(reduce.get());
        }
        throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "开票金额计算失败");
    }

}
