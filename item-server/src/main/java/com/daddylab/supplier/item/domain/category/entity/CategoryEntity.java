package com.daddylab.supplier.item.domain.category.entity;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.CategoryTransMapper;
import com.daddylab.supplier.item.controller.category.dto.CategoryCmd;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Category;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 品类实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/26 5:33 下午
 * @description
 */
@Data
public class CategoryEntity {

    /**
     * 品类
     */
    private Category category;

    /**
     * 父品类
     */
    private Category parentCategory;

    /**
     * 品类名字 只能 中文，英文，数字的组合。长度不得大于10。
     */
    private static final String MATCH = "^[\\u4E00-\\u9FA5A-Za-z0-9]+$";

    private static final Integer NAME_LEN = 10;

    private final static Integer TOP_LEVEL = 5;
    private final static Integer BOTTOM_LEVEL = 1;

    private void verify(Category category, CategoryGateway categoryGateway) {
        int level = category.getLevel();
        String name = category.getName();
        Long parentId = category.getParentId();

        if (level < BOTTOM_LEVEL || level > TOP_LEVEL) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "level,范围1~5");
        }
        if (StringUtils.isBlank(name)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "品类名称不得为空");
        }
        // 2022-11-29[七喜]wdt品类名称唯一，erp做兼容，物料名称全局唯一，不在限制同等级
        final Boolean nameRepeat = categoryGateway.isNameRepeat(name, null);
        if (nameRepeat) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "品类名称不得为重复");
        }
        final boolean match = ReUtil.isMatch(MATCH, name);
        if (!match) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "品类名称只能包含中文，英文，数字");
        }
        if (name.length() > NAME_LEN) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "品类名称长度必须10个字符以内");
        }

//        非根目录
        if (1 != level) {
            if (Objects.isNull(parentId)) {
                throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "添加非顶级品类时，父品类id不为空");
            }
            final Category parentCategory = categoryGateway.getById(parentId);
            if (Objects.isNull(parentCategory)) {
                throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "parentId非法，查询为空");
            }

            final Category rootCategory = getRootCategory(parentId, categoryGateway);
            category.setRootId(rootCategory.getId());
            category.setShortName(rootCategory.getShortName());
        }
//        根目录
        if (1 == level) {
            category.setRootId(0L);
            category.setParentId(0L);
            category.setShortName(getShortName(name, categoryGateway));
        }

        final String pathStr = getPathStr(name, parentId, categoryGateway);
        category.setPath(pathStr);

    }

    public static CategoryEntity buildEntity(CategoryCmd cmd, CategoryGateway categoryGateway) {
        CategoryEntity categoryEntity = new CategoryEntity();
        final Category category = CategoryTransMapper.INSTANCE.cmdToDo(cmd);
        categoryEntity.setCategory(category);
        categoryEntity.verify(category, categoryGateway);
        categoryEntity.setParentCategory(categoryGateway.getById(category.getParentId()));
        category.setKingDeeNum(category.getShortName() + RandomUtil.randomNumbers(6));
        return categoryEntity;
    }

    private Category getRootCategory(Long parentId, CategoryGateway categoryGateway) {
        Category parCategory = categoryGateway.getById(parentId);
        while (Objects.nonNull(parCategory) && Objects.nonNull(parCategory.getParentId()) && parCategory.getParentId() != 0) {
            parCategory = categoryGateway.getById(parCategory.getParentId());
        }
        return parCategory;
    }

    /**
     * 拼接路径
     *
     * @param currentName      当前品类的名字
     * @param parentCategoryId 当前品类的父品类id
     * @param categoryGateway
     * @return
     */
    private String getPathStr(String currentName, Long parentCategoryId, CategoryGateway categoryGateway) {

        if (Objects.nonNull(parentCategoryId) && 0 != parentCategoryId) {
            Category parentCategory = categoryGateway.getById(parentCategoryId);
            return parentCategory.getPath() + "/" + currentName;
        }
        return currentName;
    }

    /**
     * 获取品类简称
     *
     * @param name
     * @return
     */
    public String getShortName(String name, CategoryGateway categoryGateway) {
        final String firstLetter = PinyinUtil.getFirstLetter(name, "-");
        final String[] strings = firstLetter.split("-");

        for (String str : strings) {
            final String s = str.toUpperCase();
            if (!categoryGateway.isShotNameRepeat(s)) {
                return s;
            }
        }

//        如果全部拼音的首字母全部相同，首字母按序累加
        final String s = strings[0].toUpperCase();
        int i = 0;
        Boolean shotNameRepeat = categoryGateway.isShotNameRepeat(s + i);
        while (shotNameRepeat) {
            i++;
            shotNameRepeat = categoryGateway.isShotNameRepeat(s + i);
        }
        return s + i;
    }

    public static void main(String[] args) {
        String lastMaxSkuCode = "F1G12345601";
        final Map<String, String> skuCodePrefix = getSkuCodePrefix("F1", "C", lastMaxSkuCode);
        System.out.println(JsonUtil.objToStr(skuCodePrefix));
    }

    private static Map<String, String> getSkuCodePrefix(String categoryAbb, String delivery, String lastMaxSkuCode) {
        Map<String, String> map = new HashMap<>(2);

        if (StringUtil.isNotBlank(lastMaxSkuCode)) {
            final String substring = lastMaxSkuCode.substring(1);
            int deliveryIndex = !substring.contains("C") ? lastMaxSkuCode.indexOf("G") : lastMaxSkuCode.indexOf("C");
            final String sixNum = substring.substring(deliveryIndex, deliveryIndex + 6);
            final String lastIndex = substring.substring(deliveryIndex + 6);
            final String prefix = lastMaxSkuCode.substring(0, lastMaxSkuCode.length() - lastIndex.length());
            map.put("prefix", prefix);
            map.put("index", lastIndex);
            return map;
        }

        map.put("prefix", categoryAbb + delivery + RandomUtil.randomString(6));
        map.put("index", "00");
        return map;
    }


}
