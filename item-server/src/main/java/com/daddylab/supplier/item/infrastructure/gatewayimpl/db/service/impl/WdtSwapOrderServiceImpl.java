package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSwapOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtSwapOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtSwapOrderService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 旺店通换出订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Service
public class WdtSwapOrderServiceImpl extends DaddyServiceImpl<WdtSwapOrderMapper, WdtSwapOrder> implements IWdtSwapOrderService {

}
