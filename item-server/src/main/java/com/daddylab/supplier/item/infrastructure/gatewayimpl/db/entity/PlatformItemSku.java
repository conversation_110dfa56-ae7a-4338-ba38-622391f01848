package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 平台商品（投放到其他平台的商品）SKU维度
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Data
@EqualsAndHashCode(callSuper = false, of = {"shopNo", "outerItemId", "outerSkuId"})
public class PlatformItemSku implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 平台商品ID
     */
    private Long platformItemId;

    /**
     * 平台货品ID
     */
    private String outerItemId;

    /**
     * 平台货品编码
     */
    private String outerItemCode;

    /**
     * 平台规格ID
     */
    private String outerSkuId;

    /**
     * 平台规格编码
     */
    private String outerSkuCode;

    /**
     * 匹配到的我们自己的ITEM ID
     */
    private Long itemId;

    /**
     * 匹配到的我们自己的SKU ID
     */
    private Long skuId;

    /**
     * sku系统编码
     */
    private String skuCode;

    /**
     * 0删除，1在架，2下架
     */
    private PlatformItemStatus status;

    /**
     * 平台售价
     */
    private BigDecimal price;

    /**
     * 平台库存
     */
    private Integer stock;

    /**
     * 平台规格名称
     */
    private String specName;

    /**
     * 平台商品最后修改时间（旺店通返回）
     */
    private LocalDateTime modified;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;

    /**
     * 删除时间
     */
    private Long deletedAt;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 店铺编号
     */
    private String shopNo;

    /**
     * 平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城
     */
    private Platform platform;


    /**
     * 商品匹配 0:未匹配到 1:匹配到后端商品 2:匹配到组合装
     */
    private Integer itemMatch;

    /**
     * 匹配到的组合装ID
     */
    private Long combinationItemId;

    /**
     * 最后同步时间
     */
    private Long lastSyncTime;
    
    /**
     * 最后同步库存
     */
    private Integer lastSyncStock;
    
    /**
     * 是否修改平台规格编码
     */
    @TableField(exist = false)
    private boolean outerSkuCodeModified;
    
    /**
     * 库存是否发生变化
     */
    @TableField(exist = false)
    private boolean stockChanged;
    
    /**
     * 是否隐藏
     */
    @TableField(exist = false)
    private boolean hidden;
}
