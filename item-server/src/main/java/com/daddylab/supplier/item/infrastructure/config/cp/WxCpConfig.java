package com.daddylab.supplier.item.infrastructure.config.cp;

import com.daddylab.supplier.item.infrastructure.config.QyWeixinDaddyErpProperties;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpRedissonConfigImpl;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 企业微信
 *
 * @Date 2022/5/30下午9:03
 * <AUTHOR>
 */
@Configuration
public class WxCpConfig {

    @Autowired
    private RedissonClient redissonClient;

    @Bean
    public WxCpService wxCpService(QyWeixinDaddyErpProperties wxCpProperties) {
        WxCpRedissonConfigImpl wxCpConfigStorage = new WxCpRedissonConfigImpl(redissonClient);
        wxCpConfigStorage.setCorpId(wxCpProperties.getCorpid());
        wxCpConfigStorage.setCorpSecret(wxCpProperties.getCorpsecret());
        wxCpConfigStorage.setAgentId(wxCpProperties.getAgentid());
        WxCpServiceImpl wxCpService = new WxCpServiceImpl();
        wxCpService.setWxCpConfigStorage(wxCpConfigStorage);
        return wxCpService;
    }
}
