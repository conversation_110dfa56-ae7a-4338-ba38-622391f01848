package com.daddylab.supplier.item.domain.fileStore.gateway;

import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.ImageStub;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.fileStore.vo.VideoStub;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.File;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;

public interface FileGateway {
    ImageStub uploadImage(UploadFileAction action);

    FileStub uploadFile(UploadFileAction action);

    VideoStub uploadVideo(UploadFileAction action);

    Map<String, File> fileQueryBatchByUrls(Collection<String> fileUrls);

    default Map<Long, File> fileQueryBatchByIds(Collection<Long> fileIds) {
        throw new UnsupportedOperationException();
    }

    default String getAuthorizedUrl(String url) {
        return url;
    }

    default Optional<File> fileQueryByUrl(String fileUrl) {
        return Optional.ofNullable(
                fileQueryBatchByUrls(Collections.singleton(fileUrl)).get(fileUrl));
    }
}
