package com.daddylab.supplier.item.controller.item.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/26 9:34 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("获取skuCode请求")
public class SkuCodeCmd extends Command {


    private static final long serialVersionUID = -7510477172956551659L;

    @ApiModelProperty("品类id")
    @NotNull(message = "品类id不为空")
    private Long categoryId;

    @ApiModelProperty("发货渠道。工厂是G，仓库是C")
    @NotBlank(message = "发货渠道不为空")
    private String delivery;

    @ApiModelProperty("sku数量")
    @NotNull(message = "sku数量不为空")
    private Integer count;

}
