package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Positive;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/9/16 13:47
 * @Description: 请描述下这个类
 */
@Data
@ApiModel("ItemSyncMiniProgramParam-商品同步到小程序入参")
public class ItemSyncMiniProgramParam implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final Integer SYNC_TYPE_EDIT = 1;
    public static final Integer SYNC_TYPE_NEW = 2;
    public static final Integer SYNC_TYPE_OVERRIDE = 3;

    @ApiModelProperty(value = "商品编码")
    @NotBlank(message = "商品编码不能为空")
    private String itemNo;

    @ApiModelProperty(value = "小程序商品ids")
    private List<Long> miniItemIds;

    @ApiModelProperty("SKU数量")
    private Integer skuNum;

    @ApiModelProperty("平台商品数量")
    private Integer itemNum;

    @ApiModelProperty("同步类型（1-更新，2-创建，3-全量更新）")
    private Integer syncType;

    @ApiModelProperty("卖家类目ID")
    private Long sellerCategoryId;

    @Positive(message = "小程序店铺ID不能为空")
    @ApiModelProperty("小程序店铺ID")
    private Long mallShopId;
}
