package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 回调快递状态 2 代签收，1 已签收， 0 未签收， -1 无记录， -3 两天以上无记录（顺丰一天以上无记录）， -4 六天以上未签收（顺丰四天以上未签收）， -5
 * 跟踪记录里面有疑难件关键字（疑难件，留仓库，问题件，退回等）， -6 三天以上没有新的进展， -18 派送中
 *
 * <AUTHOR>
 * @since 2024/12/18
 */
@RequiredArgsConstructor
@Getter
@ApiModel(value = "快刀云物流状态")
public enum KdyLogisticsStatus implements IIntegerEnum {
  // 2 代签收
  @ApiModelProperty(value = "代签收")
  COUNTER_SIGNED(2, "代签收"),
  // 1 已签收
  @ApiModelProperty(value = "已签收")
  SIGNED(1, "已签收"),
  // 0 未签收
  @ApiModelProperty(value = "未签收")
  UNSIGNED(0, "未签收"),
  // -1 无记录
  @ApiModelProperty(value = "无记录")
  NONE(-1, "无记录"),
  // -3 两天以上无记录（顺丰一天以上无记录）
  @ApiModelProperty(value = "两天以上无记录（顺丰一天以上无记录）")
  NO_RECORD_AFTER_TWO_DAYS(-3, "两天以上无记录（顺丰一天以上无记录）"),
  // -4 六天以上未签收（顺丰四天以上未签收）
  @ApiModelProperty(value = "六天以上未签收（顺丰四天以上未签收）")
  UNSIGNED_AFTER_SIX_DAYS(-4, "六天以上未签收（顺丰四天以上未签收）"),
  // -5 跟踪记录里面有疑难件关键字（疑难件，留仓库，问题件，退回等）
  @ApiModelProperty(value = "跟踪记录里面有疑难件关键字（疑难件，留仓库，问题件，退回等）")
  PROBLEM(-5, "跟踪记录里面有疑难件关键字（疑难件，留仓库，问题件，退回等）"),
  // -6 三天以上没有新的进展
  @ApiModelProperty(value = "三天以上没有新的进展")
  NO_PROGRESS_AFTER_THREE_DAYS(-6, "三天以上没有新的进展"),
  // -18 派送中
  @ApiModelProperty(value = "派送中")
  DELIVERING(-18, "派送中"),
  ;
  private final Integer value;
  private final String desc;
}
