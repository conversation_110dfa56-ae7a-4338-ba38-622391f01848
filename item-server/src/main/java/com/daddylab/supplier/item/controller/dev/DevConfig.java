package com.daddylab.supplier.item.controller.dev;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.Data;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/31
 */
@EnableConfigurationProperties(DevConfig.DevProperties.class)
@Configuration
public class DevConfig implements WebMvcConfigurer {
    DevProperties devProperties;

    @Autowired
    public DevConfig(DevProperties devProperties) {
        this.devProperties = devProperties;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new HandlerInterceptor() {
            @Override
            public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
                final String devAuth = request.getHeader("DEV_AUTH");
                return StringUtil.isNotBlank(devAuth) && CollUtil.contains(devProperties.getPasswords(), DigestUtil.md5Hex(devAuth));
            }
        }).order(Ordered.HIGHEST_PRECEDENCE).addPathPatterns("/dev/**");
    }

    @ConfigurationProperties(prefix = "dev")
    @RefreshScope
    @Data
    public static class DevProperties {
        /**
         * 访问开发接口的密码
         */
        List<String> passwords;
    }
}