package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSkuInventory;

import java.util.List;

import java.util.List;

/**
 * <p>
 * 平台商品SKU库存表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-13
 */
public interface IPlatformItemSkuInventoryService extends IDaddyService<PlatformItemSkuInventory> {
    /**
     * 获取唯一的sku
     *
     * @param type Platform
     * @param skuId String
     * @return com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSkuInventory
     * @date 2024/3/13 16:30
     * <AUTHOR>
     */
    PlatformItemSkuInventory getSkuInventory(Platform type, String skuId);

    /**
     * 根据商品获取一个数据
     *
     * @param type Platform
     * @param outItemId String
     * @return com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSkuInventory
     * @date 2024/3/15 15:24
     * <AUTHOR>
     */
    List<PlatformItemSkuInventory> getItemSkuInventory(Platform type, String outItemId);

    /**
     * 获取某个类型的数据数量
     *
     * @param type Platform
     * @return java.lang.Long
     * @date 2024/3/14 17:04
     * <AUTHOR>
     */
    Long getSkuInventoryPlatformCount(Platform type);

    /**
     * 获取指定平台商品ID下全部SKU的库存
     * @param type 平台
     * @param outerItemId 平台商品ID
     * @return List[PlatformItemSkuInventory]
     */
    List<PlatformItemSkuInventory> getSkuInventories(Platform type, String outerItemId);
}
