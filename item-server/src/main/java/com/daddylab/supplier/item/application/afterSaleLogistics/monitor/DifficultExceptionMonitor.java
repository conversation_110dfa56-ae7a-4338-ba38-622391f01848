package com.daddylab.supplier.item.application.afterSaleLogistics.monitor;

import com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsException;
import com.daddylab.supplier.item.application.afterSaleLogistics.LogisticsRootException;
import com.daddylab.supplier.item.application.afterSaleLogistics.domain.LogisticsTraceData;
import com.daddylab.supplier.item.application.afterSaleLogistics.dto.LogisticExceptionRes;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.ErpLogisticsStatus;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/12/30
 */
@Component
@Order(MonitorOrder.DifficultExceptionMonitor)
public class DifficultExceptionMonitor implements OrderLogisticsExceptionMonitor {
  @Override
  public LogisticExceptionRes process(ProcessContext context) {
    LogisticExceptionRes exceptionRes = new LogisticExceptionRes();
    exceptionRes.setRootException(LogisticsRootException.NORMAL);
    // 疑难件
    final LogisticsTraceData logisticsTraceData = context.getLogisticsTraceData();
    if (logisticsTraceData != null) {
      if (context.getRevisedLogisticsStatus() == ErpLogisticsStatus.DIFFICULT) {
        exceptionRes.setSubException(LogisticsException.KNOTTY);
        exceptionRes.setRootException(LogisticsRootException.DIFFICULT_EXCEPTION);
        return exceptionRes;
      }
    }
    return exceptionRes;
  }
}
