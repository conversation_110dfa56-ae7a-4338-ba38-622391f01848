package com.daddylab.supplier.item.application.item.combinationItem.dto.vo;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.CombinationItemType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/25 5:34 下午
 * @description
 */
@Data
public class CombinationItemWithPriceVO {

    protected Long id;

    @ApiModelProperty("名字")
    protected String name;

    @ApiModelProperty("组合商品编码")
    protected String code;

    @ApiModelProperty("组合商品条码")
    protected String barCode;

    @ApiModelProperty("重量")
    protected BigDecimal weight;

    @ApiModelProperty("属性。0：普通件。1：普通大件。2：独立大件")
    protected CombinationItemType type;

    @ApiModelProperty("sku明细列表")
    protected List<ComposeSkuVO> composeSkuList;

    protected Long createdUid;
    protected String createdName;
    protected Long createdAt;

    protected Long updatedUid;
    protected String updatedName;
    protected Long updatedAt;

    @ApiModelProperty("采购成本")
    private List<OneTypePriceVO> procurementPrices;

    @ApiModelProperty("日常销售价格")
    private List<OneTypePriceVO> salesPrices;


    @ApiModelProperty("合同销售价格历史")
    private List<OneTypePriceVO> contractSalePrices;

    @ApiModelProperty("平台佣金历史")
    private List<OneTypePriceVO> platformCommissions;


    @ApiModelProperty("合作模式（业务线）")
    private Integer businessLine;

    /**
     * 平台佣金
     */
    @ApiModelProperty("平台佣金")
    private BigDecimal platformCommission;

    /**
     * 合同销售价
     */
    @ApiModelProperty("合同销售价")
    private BigDecimal contractSalePrice;

    @ApiModelProperty("合作方")
    private List<Integer> corpType;

    @ApiModelProperty("业务类型")
    private List<Integer> bizType;

    @ApiModelProperty("供应商ID")
    private Long providerId;

    @ApiModelProperty("供应商名称")
    private String providerName;
}
