package com.daddylab.supplier.item.domain.brand.dto;

import cn.hutool.core.collection.CollUtil;

import com.daddylab.supplier.item.common.enums.EnableStatusEnum;
import com.daddylab.supplier.item.common.trans.CommaSerialTransMapper;
import com.daddylab.supplier.item.domain.brand.vo.BrandProviderVo;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BrandListItem implements Serializable {
  /** id */
  private Long id;

  /** 品牌编号 */
  private String sn;

  /** 品牌名称 */
  private String name;

  /** 供应商列表 */
  private List<BrandProviderVo> providers;

  /** 状态 0:停用 1:正常 */
  private EnableStatusEnum status;

  /** 合作模式 */
  private List<Integer> businessLine;

  public List<Integer> getBusinessLine() {
    if (businessLine == null) {
      return CommaSerialTransMapper.INSTANCE.strToIntegerList(this.businessLineStr);
    }
    return this.businessLine;
  }

  private String businessLineStr;

  @ApiModelProperty("是否黑名单 0否 1是")
  private Integer isBlacklist;
}
