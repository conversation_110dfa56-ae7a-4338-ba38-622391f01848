package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.PropertyName;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 财务信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class FinanceInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @DiffIgnore
    private Long id;

    @TableField(fill = FieldFill.INSERT)
    @DiffIgnore
    private Long createdAt;

    @TableField(fill = FieldFill.INSERT)
    @DiffIgnore
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    @DiffIgnore
    private Long updatedAt;

    @TableField(fill = FieldFill.UPDATE)
    @DiffIgnore
    private Long updatedUid;

    @DiffIgnore
    private Long deletedAt;

    @TableLogic
    @DiffIgnore
    private Integer isDel;

    /**
     * 供应商id
     */
    @DiffIgnore
    private Long providerId;

    /**
     * 发票类型
     */
    @PropertyName("发票类型")
    private Integer invoiceType;

    /**
     * 结算币别
     */
    @PropertyName("结算币别")
    private Integer currency;

    /**
     * 税分类
     */
    @PropertyName("税分类")
    private Integer taxType;

    /**
     * 默认税率
     */
    @PropertyName("默认税率")
    private BigDecimal taxRate;


}
