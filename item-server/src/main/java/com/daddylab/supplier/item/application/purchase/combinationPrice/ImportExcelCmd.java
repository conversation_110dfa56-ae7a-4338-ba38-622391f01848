package com.daddylab.supplier.item.application.purchase.combinationPrice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> up
 * @date 2023年10月12日 10:10 AM
 */
@Data
@ApiModel("采购阶梯供价导入请求参数")
public class ImportExcelCmd {

    @ApiModelProperty("1:sku日常阶梯价。2:sku活动阶梯价")
    @NotNull(message = "导入文件类型不得为空")
    private Integer priceType;

    @ApiModelProperty("Excel文件")
    @NotNull(message = "导入文件不得为空")
    private MultipartFile file;

}
