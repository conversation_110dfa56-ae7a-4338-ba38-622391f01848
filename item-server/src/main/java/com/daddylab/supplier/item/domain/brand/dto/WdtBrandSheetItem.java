package com.daddylab.supplier.item.domain.brand.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode
@Data
@ContentRowHeight(16)
@HeadRowHeight(20)
public class WdtBrandSheetItem {
    @ColumnWidth(50)
    @ExcelProperty("品牌编号")
    private String sn;

    @ColumnWidth(50)
    @ExcelProperty("品牌名称")
    private String name;

    @ColumnWidth(50)
    @ExcelProperty("是否停用")
    private String isDisable;

    @ColumnWidth(50)
    @ExcelProperty("合作方")
    private String businessLine;

    public String getSn() {
        return filter(sn);
    }

    public String getName() {
        return filter(name);
    }

    public String getIsDisable() {
        return filter(isDisable);
    }

    /**
     * 旺店通导出的CSV有点特殊，里头某些值会有多余一些符号包装，这边给过滤一下
     * e.g. ="00346"
     */
    private String filter(String string) {
        if (StringUtil.startWith(string, "=\"") && StringUtil.endWith(string, "\"")) {
            string = string.substring(2, string.length() - 1);
        }
        return string;
    }
}
