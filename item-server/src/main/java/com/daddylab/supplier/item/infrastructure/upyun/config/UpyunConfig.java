package com.daddylab.supplier.item.infrastructure.upyun.config;

import lombok.Data;
import lombok.ToString;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Data
@ToString
@Configuration
public class UpyunConfig {
    @Value("${upyun.bucket-name}")
    private String bucketName;
    @Value("${upyun.operator-name}")
    private String operatorName;
    @Value("${upyun.operator-pwd}")
    private String operatorPwd;
    @Value("${upyun.upyun-base-url}")
    private String upyunBaseUrl;
    @Value("${upyun.hmac-sha-1-algorithm}")
    private String hmacSha1Algorithm;
    @Value("${upyun.ed-auto}")
    private String edAuto;
    @Value("${upyun.api-domain:https://v1.api.upyun.com}")
    private String apiDomain;

    @Value("${upload.default-dir}")
    private String defaultDir;
    @Value("${upload.public-dir}")
    private String publicDir;

}
