package com.daddylab.supplier.item.controller.handingsheet.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/29 15:46
 * @Description: 请描述下这个类
 */
@Data
@HeadRowHeight(20)
@ContentRowHeight(16)
@EqualsAndHashCode(callSuper = true)
public class HandingSheetItemExportVo extends ExportSheet implements Serializable {
    private static final long serialVersionUID = -1L;

    @ExcelProperty(value = "商品编码")
    @ColumnWidth(50)
    private String itemNo;

    @ExcelProperty(value = "产品标准名")
    @ColumnWidth(50)
    private String itemStandardName;

    @ExcelProperty(value = "规格编码")
    @ColumnWidth(50)
    private String itemSkuNo;

    @ExcelProperty(value = "规格名称")
    @ColumnWidth(50)
    private String itemSkuName;

    @ExcelProperty(value = "日销价")
    @ColumnWidth(50)
    private BigDecimal dailyPrice;

    @ExcelProperty(value = "活动价")
    @ColumnWidth(50)
    private BigDecimal activePrice;

    @ExcelProperty(value = "到手价")
    @ColumnWidth(50)
    private BigDecimal arrivalPrice;

    @ExcelProperty(value = "采购成本")
    @ColumnWidth(50)
    private BigDecimal costPrice;

    @ExcelProperty(value = "毛利率")
    @ColumnWidth(50)
    private BigDecimal grossProfitMargin;

    @ExcelProperty(value = "赠品机制")
    @ColumnWidth(50)
    private String giftDescription;

    @ExcelProperty(value = "赠品编码")
    @ColumnWidth(50)
    private String giftCode;

    @ExcelProperty(value = "淘宝链接")
    @ColumnWidth(50)
    private String taobaoLink;

    @ExcelProperty(value = "抖音链接")
    @ColumnWidth(50)
    private String douyinLink;

    @ExcelProperty(value = "小程序链接")
    @ColumnWidth(50)
    private String miniProgramLink;

    @ExcelProperty(value = "备注")
    @ColumnWidth(50)
    private String remark;
}