package com.daddylab.supplier.item.controller.otherStockIn.dto;

import com.daddylab.supplier.item.controller.otherStockInDetail.dto.OtherStockInDetailVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OtherStockInVo.java
 * @description
 * @createTime 2022年03月31日 11:55:00
 */
@Data
@ApiModel("其他入库单返回实体")
public class OtherStockInVo implements Serializable {

    private static final long serialVersionUID = 2473036143568924080L;

    @ApiModelProperty(value = "入库单id")
    private Long id;

    @ApiModelProperty(value = "入库单号")
    private String orderNo;

    @ApiModelProperty(value = "仓库 1袋鼠仓库(海销) 废弃⚠️")
    private Integer warehouse;

    @ApiModelProperty(value = "仓库编号")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "其他入库原因 0:全部 1:无 2:工厂WMS仓入库 3:调拨入库 4:退货入库 5:搬仓入库 6:退货异常入库（无主件）")
    private Integer otherReason;

    @ApiModelProperty(value = "其他入库原因名称")
    private String otherReasonName;

    @ApiModelProperty(value = "货品数量")
    private Integer itemCount;

    @ApiModelProperty(value = "货品种类数")
    private Integer kindCount;

    @ApiModelProperty(value = "标记名称")
    private String markName;

    @ApiModelProperty(value = "物流公司")
    private Integer logisticsId;

    @ApiModelProperty(value = "物流单号")
    private String logisticsNo;

    @ApiModelProperty(value = "状态 0:全部 1:待处理 2:已取消 3:编辑中 4:待审核 5:待质检 6:质检待确认 7:已完成")
    private Integer status;

    @ApiModelProperty(value = "制单人")
    private String operatorName;

    @ApiModelProperty(value = "创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "审核人")
    private String auditUid;

    @ApiModelProperty(value = "其他入库单详情")
    private List<OtherStockInDetailVo> stockInDetailVos;

    private Integer businessLine;

}
