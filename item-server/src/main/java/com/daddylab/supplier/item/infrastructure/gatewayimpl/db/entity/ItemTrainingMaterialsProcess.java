package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.daddylab.supplier.item.types.itemTrainingMaterials.ItemTrainingMaterialsProcessData;
import com.daddylab.supplier.item.types.itemTrainingMaterials.ItemTrainingMaterialsStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 新品商品培训资料处理流程
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemTrainingMaterialsProcess implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除：0否1是
     */
    @TableLogic
    private Integer isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 商品培训材料ID
     */
    private Long itemTrainingMaterialsId;

    /**
     * 状态 0 待提交 1 已提交 2 待法务审核 3 待QC审核 4 待修改 5 已完成
     */
    private ItemTrainingMaterialsStatus status;

    /**
     * 提交人
     */
    private Long submitUid;

    /**
     * 提交时间
     */
    private Long submitAt;

    /**
     * 当前流程实例ID
     */
    private String processInstId;

    /**
     * 商品培训资料流程模型数据
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ItemTrainingMaterialsProcessData processData;


}
