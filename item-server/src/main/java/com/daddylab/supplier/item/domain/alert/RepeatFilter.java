package com.daddylab.supplier.item.domain.alert;

import lombok.extern.slf4j.Slf4j;

import org.redisson.RedissonBloomFilter;
import org.redisson.api.RBloomFilter;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class RepeatFilter implements IAlerter.Filter {
    RedissonClient redissonClient;

    @Autowired
    public RepeatFilter(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private RedissonBloomFilter<String> getBloomFilter(IAlerter alert) {
        final RedissonBloomFilter<String> bloomFilter =
                (RedissonBloomFilter)
                        redissonClient.getBloomFilter("AlertRepeatFilter:" + alert.toString());
        bloomFilter.tryInit(1_000_000, 0.03);
        return bloomFilter;
    }

    @Override
    public List<IAlerter.Message> filter(IAlerter alert, List<IAlerter.Message> message) {
        final ArrayList<IAlerter.Message> filtered = new ArrayList<>();
        final RedissonBloomFilter<String> bloomFilter = getBloomFilter(alert);
        if (bloomFilter.remainTimeToLive() < 0) {
            bloomFilter.expire(10, TimeUnit.MINUTES);
        }
        for (IAlerter.Message oneMessage : message) {
            final String hash = String.valueOf(oneMessage.getContent().hashCode());
            final StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            final boolean contains = bloomFilter.contains(hash);
            stopWatch.stop();
            log.info(
                    "BloomFilter contains, result:{}, timeConsumed:{}ms, count:{}",
                    contains,
                    stopWatch.getLastTaskTimeMillis(),
                    bloomFilter.count());
            if (contains) {
                log.info("内容短时间内重复发送，过滤此条消息:" + oneMessage.getContent());
                continue;
            }
            bloomFilter.add(hash);
            filtered.add(oneMessage);
        }
        return filtered;
    }
}
