package com.daddylab.supplier.item.infrastructure.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ResourceLoader;

/**
 * <AUTHOR>
 * @since 2023/9/21
 */
@SuppressWarnings("SpringBootBootstrapConfigurationInspection")
@ConditionalOnMissingBean(LocalPropertySourceLocator.class)
@Configuration
public class LocalPropertySourceBootstrapConfiguration {
    @Bean
    public LocalPropertySourceLocator localPropertySourceLocator(ResourceLoader resourceLoader) {
        return new LocalPropertySourceLocator(resourceLoader);
    }
}
