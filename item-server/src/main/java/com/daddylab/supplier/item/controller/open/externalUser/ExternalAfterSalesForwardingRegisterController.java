package com.daddylab.supplier.item.controller.open.externalUser;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSaleLink.AfterSaleShareLinkBizService;
import com.daddylab.supplier.item.application.afterSaleLink.dto.AfterSaleShareLinkCmd;
import com.daddylab.supplier.item.application.afterSales.WarehouseAfterSalesAddressVO;
import com.daddylab.supplier.item.application.afterSalesForwarding.AfterSalesForwardingRegisterBizService;
import com.daddylab.supplier.item.application.afterSalesForwarding.types.*;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.application.system.BaseInfoService;
import com.daddylab.supplier.item.application.warehouse.WarehouseGateway;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.types.warehouse.WarehouseViewVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/13
 */
@RestController
@Api(value = "[外部系统]售后转寄登记API", tags = "[外部系统]售后转寄登记API")
@RequestMapping("/external/afterSales/forwardingRegister")
@RequiredArgsConstructor
@Validated
public class ExternalAfterSalesForwardingRegisterController {

    private final AfterSalesForwardingRegisterBizService afterSalesForwardingRegisterBizService;
    private final AfterSaleShareLinkBizService afterSaleShareLinkBizService;
    private final WarehouseGateway warehouseGateway;
    private final BaseInfoService baseInfoService;
    private final OperateLogBizService operateLogBizService;

    @ResponseBody
    @ApiOperation(value = "查询补全表单")
    @PostMapping("/completion")
    public MultiResponse<AfterSalesForwardingRegisterFormItemVO> completion(@Validated @RequestBody AfterSalesForwardingRegisterCompletionCmd cmd) {
        return afterSalesForwardingRegisterBizService.completion(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "保存登记")
    @PostMapping("/save")
    public SingleResponse<Boolean> save(@RequestBody @Validated AfterSalesForwardingRegisterSaveCmd cmd) {
        return afterSalesForwardingRegisterBizService.save(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "查询转寄登记")
    @PostMapping("/query")
    public PageResponse<AfterSalesForwardingRegisterPageVO> query(@RequestBody AfterSalesForwardingRegisterQuery query) {
        return afterSalesForwardingRegisterBizService.query(query);
    }

    @ResponseBody
    @ApiOperation(value = "[分享链接]查询转寄登记")
    @PostMapping("/shareLinkQuery")
    @Auth(noAuth = true)
    public PageResponse<AfterSalesForwardingRegisterPageVO> shareLinkQuery(@RequestBody AfterSalesForwardingRegisterShareQuery query) {
        return afterSalesForwardingRegisterBizService.shareLinkQuery(query);
    }

    @ResponseBody
    @ApiOperation(value = "[分享链接]查询转寄登记数据导出")
    @PostMapping("/shareLinkQueryExport")
    @Auth(noAuth = true)
    public SingleResponse<String> shareLinkQueryExport(@RequestBody AfterSalesForwardingRegisterShareQuery query) {
        return afterSalesForwardingRegisterBizService.shareLinkQueryExport(query);
    }

    @ResponseBody
    @ApiOperation(value = "导入")
    @PostMapping("/importExcel")
    public Response importExcel(@ApiParam(name = "file", value = "文件", required = true) @RequestParam("file")
                                MultipartFile file) throws IOException {
        return afterSalesForwardingRegisterBizService.importExcel(file.getInputStream());
    }

    @ResponseBody
    @ApiOperation(value = "导出")
    @PostMapping("/exportExcel")
    public Response exportExcel(@RequestBody AfterSalesForwardingRegisterQuery query) {
        return afterSalesForwardingRegisterBizService.exportExcel(query);
    }

    @ResponseBody
    @ApiOperation(value = "标记记录状态为无需转寄")
    @PostMapping("/noNeedToForward")
    public Response noNeedToForward(@RequestBody AfterSalesForwardingRegisterNoNeedToForwardCmd cmd) {
        return afterSalesForwardingRegisterBizService.noNeedToForward(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "标记记录状态为需转寄")
    @PostMapping("/toForward")
    public Response toForward(@RequestBody AfterSalesForwardingRegisterToForwardCmd cmd) {
        return afterSalesForwardingRegisterBizService.toForward(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "报告异常")
    @PostMapping("/reportAnomalies")
    public Response reportAnomalies(@RequestBody AfterSalesForwardingRegisterReportAnomaliesCmd cmd) {
        return afterSalesForwardingRegisterBizService.reportAnomalies(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "[分享链接]发送短信")
    @PostMapping("/shareLink/sendCode")
    public Response sendCode(@RequestBody AfterSalesForwardingRegisterShareSendCodeCmd cmd) {
        return afterSaleShareLinkBizService.sendCode(cmd.getShareLinkId(), cmd.getPhone());
    }

    @ResponseBody
    @ApiOperation(value = "[分享链接]获取访问令牌")
    @PostMapping("/shareLink/token")
    public Response token(@RequestBody AfterSalesForwardingRegisterShareTokenCmd cmd) {
        return afterSaleShareLinkBizService.token(cmd.getShareLinkId(), cmd.getPhone(), cmd.getCode());
    }

    @ResponseBody
    @ApiOperation(value = "[分享链接]仓库下拉选")
    @GetMapping("/shareLink/warehouseList")
    public Response shareLinkWarehouseList(@RequestBody AfterSalesForwardingShareWarehouseCmd warehouseCmd) {
        return warehouseGateway.shareQueryWarehouseByName(warehouseCmd);
    }

    @ResponseBody
    @ApiOperation(value = "仓库下拉选")
    @GetMapping("/warehouseList")
    public Response warehouseList(String warehouseName) {
        return warehouseGateway.queryWarehouseByName(warehouseName);
    }

    @ResponseBody
    @ApiOperation(value = "转寄地址")
    @PostMapping("/warehouseAddress")
    public MultiResponse<WarehouseAfterSalesAddressVO> warehouseAddress(String warehouseName) {
        SingleResponse<WarehouseViewVO> response = baseInfoService.viewWarehouse(null, warehouseName);
        if (response.isSuccess()) {
            List<WarehouseAfterSalesAddressVO> afterSalesAddressInfoList = response.getData().getAfterSalesAddressInfoList();
            return MultiResponse.of(afterSalesAddressInfoList);
        }
        return MultiResponse.buildFailure(ErrorCode.SYSTEM_BUSY.getCode(), response.getErrMessage());
    }

    @ResponseBody
    @ApiOperation(value = "操作记录")
    @GetMapping("/log")
    public MultiResponse<OperateLog> log(@ApiParam(name = "id", value = "快递单号", required = true) String id) {
        return operateLogBizService.getOperateLogs(OperateLogTarget.AFTER_SALE_FORWARDING, id);
    }


    @ResponseBody
    @ApiOperation(value = "保存分享链接信息")
    @PostMapping("/linkSave")
    public SingleResponse<String> shareLinkSave(@RequestBody AfterSaleShareLinkCmd cmd) {
        return afterSaleShareLinkBizService.saveLink(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "删除")
    @GetMapping("/delete")
    public Response delete(@ApiParam(name = "id", value = "登记记录ID", required = true) Long id) {
        return afterSalesForwardingRegisterBizService.delete(id);
    }
}
