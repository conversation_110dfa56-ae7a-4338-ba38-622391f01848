package com.daddylab.supplier.item.controller.provider.dto;

import com.alibaba.cola.dto.PageQuery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/28 10:41 上午
 * @description
 */
@Data
@ApiModel("供应商分页查询")
public class ProviderPageQuery extends PageQuery {

    private static final long serialVersionUID = 4503255255098420448L;
    @ApiModelProperty("供应商名称")
    private String name;

    @ApiModelProperty(value = "状态。0全部。1合作。2停止", example = "0")
    private Integer status;

    @ApiModelProperty(value = "类型。0全部。1自营。2代发", example = "0")
    private Integer type;

    @ApiModelProperty("联系人名字")
    private String contact;

    @ApiModelProperty("负责人")
    private Long mainChargerUserId;

    @ApiModelProperty("次要负责人")
    private Long secondChargerUserId;

    @ApiModelProperty("合作方")
    private List<Integer> corpType;

    @ApiModelProperty("业务类型")
    private List<Integer> bizType;
}
