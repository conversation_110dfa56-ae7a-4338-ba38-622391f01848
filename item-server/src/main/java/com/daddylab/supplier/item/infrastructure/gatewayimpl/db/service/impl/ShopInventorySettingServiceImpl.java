/*
package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopInventorySetting;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ShopInventorySettingMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopInventorySettingService;
import org.springframework.stereotype.Service;

*/
/**
 * <p>
 * 店铺库存设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 *//*

@Service
public class ShopInventorySettingServiceImpl extends DaddyServiceImpl<ShopInventorySettingMapper, ShopInventorySetting> implements IShopInventorySettingService {

    @Override
    public ShopInventorySetting getByShopId(Long shopId) {
        return lambdaQuery().eq(ShopInventorySetting::getShopId, shopId).one();
    }
}
*/
