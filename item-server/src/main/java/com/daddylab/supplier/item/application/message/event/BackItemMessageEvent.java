package com.daddylab.supplier.item.application.message.event;

import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.message.dto.MsgFillObj;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessageOperationType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 后端商品消息实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/22 2:35 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BackItemMessageEvent extends AbstractMessageEvent {

    private String operatorName;

    private String operationId;

    private Long itemId;

    private String itemNo;

    private String itemName;

    private BackItemMessageEvent() {

    }

    public static BackItemMessageEvent build(Long itemId, String itemName, String itemNo, Optional<UserContext.UserInfo> userInfoOptional) {
        BackItemMessageEvent event = new BackItemMessageEvent();
        userInfoOptional.ifPresent(userInfo -> {
            event.setOperatorName(userInfo.getNickname());
            event.setOperationId(String.valueOf(userInfo.getUserId()));
        });
        event.setItemId(itemId);
        event.setItemNo(itemNo);
        event.setItemName(itemName);
        event.setOperationType(MessageOperationType.ADD_BACK_ITEM);
        return event;
    }

    public static BackItemMessageEvent buildEditBackItemEvent(Long itemId, String itemName, String itemNo, Optional<UserContext.UserInfo> userInfoOptional) {
        BackItemMessageEvent event = new BackItemMessageEvent();
        userInfoOptional.ifPresent(userInfo -> {
            event.setOperatorName(userInfo.getNickname());
            event.setOperationId(String.valueOf(userInfo.getUserId()));
        });
        event.setItemId(itemId);
        event.setItemNo(itemNo);
        event.setItemName(itemName);
        event.setOperationType(MessageOperationType.EDIT_BACK_ITEM);
        return event;
    }

    @Override
    public String getPushLink() {
        if (MessageOperationType.ADD_BACK_ITEM.equals(operationType)) {
            return SpringUtil.getProperty("router.item-view") + itemId;
        }
        if (MessageOperationType.EDIT_BACK_ITEM.equals(operationType)) {
            return SpringUtil.getProperty("router.item-view") + itemId;
        }
        throw ExceptionPlusFactory.bizException(ErrorCode.MESSAGE_ERROR, "消息配置，操作类型异常");
    }

    @Override
    public List<MsgFillObj> getFillObjList() {
        MsgFillObj obj1 = new MsgFillObj();
        obj1.setIndex(0);
        obj1.setVal(this.operatorName);
        obj1.setLink("");

        String link = SpringUtil.getProperty("router.item-view") + itemId;
        MsgFillObj obj2 = new MsgFillObj();
        obj2.setIndex(1);
        obj2.setVal(this.itemNo);
        obj2.setLink(link);

        MsgFillObj obj3 = new MsgFillObj();
        obj3.setIndex(2);
        obj3.setVal(this.itemName);
        obj3.setLink(link);

        return Arrays.asList(obj1, obj2, obj3);
    }

    @Override
    public String getEmailSubject() {
        if (MessageOperationType.ADD_BACK_ITEM.equals(operationType)) {
            return "后端商品新增";
        }
        if (MessageOperationType.EDIT_BACK_ITEM.equals(operationType)) {
            return "后端商品编辑";
        }
        return "后端商品变化";
    }
}
