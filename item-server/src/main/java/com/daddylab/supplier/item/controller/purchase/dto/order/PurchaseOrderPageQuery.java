package com.daddylab.supplier.item.controller.purchase.dto.order;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PaymentOrderStatus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2022/3/25 9:52 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("采购订单分页查询请求实体")
public class PurchaseOrderPageQuery extends PageQuery {

    private static final long serialVersionUID = 5041630501799794927L;
    @ApiModelProperty(value = "采购单号")
    private String orderNo;

    @ApiModelProperty(value = "供应商id")
    private Long providerId;

    @ApiModelProperty(value = "采购类型。0：全部。1：标准采购，2：工厂采购")
    private Integer type;

    @ApiModelProperty(value = "付款条件。0：全部。1:款到发货、2:预付10%、3:预付20%、4:预付30%、5:预付40%、6:预付50%、7:预付60%、8:货到付款、9:月结")
    private Integer payMode;

    @ApiModelProperty(value = "采购状态。0：全部。1:待提交、2:待审核、3:审核通过、4:审核拒绝、5:撤回审核、6:待完结、7:已完结、8:已关闭")
    private Integer state;

    @ApiModelProperty(value = "采购状态。0：全部。1:待提交、2:待审核、3:审核通过、4:审核拒绝、5:撤回审核、6:待完结、7:已完结、8:已关闭")
    private List<Integer> stateList;

    @ApiModelProperty(value = "入库状态。0：全部。1：待提交、2：待入库、3：已入库、4：已取消")
    private Integer stockInState;

    @ApiModelProperty(value = "采购员id")
    private Long buyerUserId;

    @ApiModelProperty(value = "采购开始日期")
    private Long startDt;

    @ApiModelProperty(value = "采购结束日期")
    private Long startEnd;

    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    @ApiModelProperty(value = "应付单号")
    private String payOrderNo;

    @ApiModelProperty(value = "商品skuCode")
    private String skuCode;

    @ApiModelProperty(value = "商品id")
    private Long itemId;

    @ApiModelProperty(value = "采购组织")
    private Long organizationId;

    @ApiModelProperty(value = "采购组")
    private Long groupId;

    @ApiModelProperty(value = "预计入仓仓库编号")
    private String warehouseNo;

    @ApiModelProperty(value = "明细模式")
    private Boolean isDetailMode = false;


    @ApiModelProperty(value = "合作模式。0:电商。1:老爸抽检。2:绿色家装。3:商家入驻")
    private List<Integer> businessLine;

    @ApiModelProperty(value = "付款申请状态")
    private PaymentOrderStatus paymentOrderStatus;

    private List<Long> matchedPaymentStatusIdList;

    @ApiModelProperty("采购单IDs")
    private List<Long> ids;

    private List<Long> createdUidList;
    private List<Long> neCreatedUidList;




}
