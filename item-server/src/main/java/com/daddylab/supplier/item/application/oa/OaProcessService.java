package com.daddylab.supplier.item.application.oa;

import com.daddylab.supplier.item.infrastructure.oa.OaProcessStartResponse;
import com.daddylab.supplier.item.infrastructure.oa.OaResponse;
import com.daddylab.supplier.item.infrastructure.oa.OaUploadAttachmentResponse;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
public interface OaProcessService {

    OaResponse<OaProcessStartResponse> processStart(
            String loginName,
            String templateCode,
            boolean draft,
            String subject,
            List<String> attachments,
            Object formData);


    boolean processCancel(String summaryId, String affairId, String loginName);

    boolean processStop(String affairId, String loginName, String comment);

    OaUploadAttachmentResponse uploadAttachment(String loginName, String fileUrl) throws IOException;
}
