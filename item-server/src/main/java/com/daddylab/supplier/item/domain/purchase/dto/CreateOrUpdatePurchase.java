package com.daddylab.supplier.item.domain.purchase.dto;

import com.alibaba.cola.dto.Command;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName PurchaseCmd.java
 * @description 采购确认
 * @createTime 2021年11月11日 14:19:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("采购确认新增实体")
public class CreateOrUpdatePurchase extends Command {

    private static final long serialVersionUID = -2487947877551369277L;


    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private Long itemId;

    /**
     * 商品skuid
     */
    @ApiModelProperty(value = "商品skuid")
    private Long itemSkuId;

    /**
     * 商品sku
     */
    @ApiModelProperty(value = "商品sku")
    private String itemSku;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    /**
     * 商品规格
     */
    @ApiModelProperty(value = "商品规格")
    private String itemSpecs;

    /**
     * 采购地点
     */
    @ApiModelProperty(value = "采购地点")
    private String purchaseArea;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private Long providerId;

    /**
     * 采购负责人
     */
    @ApiModelProperty(value = "采购负责人")
    private Long buyerId;

    /**
     * 日常供价(无优惠)
     */
    @ApiModelProperty(value = "日常供价(无优惠)")
    private BigDecimal usualPrice;

    /**
     * 规格数量
     */
    @ApiModelProperty(value = "规格数量")
    private Long specsCount;

    /**
     * 是否纯活动商品 0:否 1:是
     */
    @ApiModelProperty(value = "是否纯活动商品 0:否 1:是")
    private Integer isActive;

    /**
     * 优惠类型 0:货抵款 1:按时间供价 2:当月优惠件数
     */
    @ApiModelProperty(value = "优惠类型 0:货抵款 1:按时间供价 2:当月优惠件数")
    private Integer favourableType;

    /**
     * 平台名称 0:ALL 1:淘宝 2:抖店 3:小红书 4:自研电商 5:有赞 6:快手
     */
    @ApiModelProperty(value = "平台名称 0:ALL 1:淘宝 2:抖店 3:小红书 4:自研电商 5:有赞 6:快手")
    private Integer platformType;

    /**
     * 方式 0:大促 1:直播 2:无
     */
    @ApiModelProperty(value = "方式 0:大促 1:直播 2:无")
    private Integer activeType;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    private Long startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    private Long endTime;

    /**
     * 订单拍下份数
     */
    @ApiModelProperty(value = "订单拍下份数")
    private Long orderCount;

    /**
     * 实发单品数量
     */
    @ApiModelProperty(value = "实发单品数量")
    private Long finalCount;

    /**
     * 按价格优惠结算成本/元
     */
    @ApiModelProperty(value = "按价格优惠结算成本/元")
    private BigDecimal priceCost;

    /**
     * 按数量优惠结算成本/元
     */
    @ApiModelProperty(value = "按数量优惠结算成本/元")
    private BigDecimal numCost;

    /**
     * 供价优惠内容
     */
    @ApiModelProperty(value = "供价优惠内容")
    private String content;

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明")
    private String mark;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;

}
