package com.daddylab.supplier.item.application.payment;

import com.daddylab.supplier.item.application.payment.dto.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentApplyOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentApplyOrderDetail;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年11月20日 3:34 PM
 */
@Mapper
public interface PaymentApplyTransMapper {

  PaymentApplyTransMapper INSTANCE = Mappers.getMapper(PaymentApplyTransMapper.class);

  @Mappings({
    @Mapping(
        target = "applyTimeStr",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getExpectedPayTimeStr("
                + "order.getApplyTime(),cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN))"),
    @Mapping(
        target = "expectedPayTimeStr",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getExpectedPayTimeStr("
                + "order.getExpectedPayTime(),cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN))"),
    @Mapping(
        target = "payOrgStr",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getPayOrgStr(order.getPayOrg()))"),
    @Mapping(target = "payeeUnitStr", ignore = true),
    @Mapping(target = "buyerName", ignore = true)
  })
  PaymentPageVo dbToVo(PaymentApplyOrder order);

  @Mappings({
    @Mapping(
        target = "purchaseOrgStr",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getPurchaseOrgStr(order.getPurchaseOrg()))"),
    @Mapping(
        target = "payOrgStr",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getPayOrgStr(order.getPayOrg()))"),
    @Mapping(
        target = "payPurposeStr",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getPayPurpose(order.getPayPurpose()))"),
    @Mapping(
        target = "payProportionsStr",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getPayProportions(order.getPayProportions()))"),
    @Mapping(
        target = "expectedPayTimeStr",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getExpectedPayTimeStr("
                + "order.getExpectedPayTime(),cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN))"),
    @Mapping(
        target = "applyTimeStr",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getExpectedPayTimeStr("
                + "order.getApplyTime(),cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN))"),
    @Mapping(target = "createdUser", ignore = true)
  })
  PaymentApplyOrderViewVo dbToViewVo(PaymentApplyOrder order);

  @Mappings({
    @Mapping(
        target = "additionalIds",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.listToStr(cmd.getAdditionalId()))")
  })
  PaymentApplyOrder cmdToEntity(PaymentApplyOrderSaveCmd cmd);

  @Mappings({
    @Mapping(
        target = "additionalIds",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.listToStr(cmd.getAdditionalId()))")
  })
  PaymentApplyOrder copyFromCmd(
      @MappingTarget PaymentApplyOrder order, PaymentApplyOrderSaveCmd cmd);

  PaymentApplyOrder copy(PaymentApplyOrder order);

  @Mappings({
    @Mapping(
        target = "businessLine",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getBusinessLine(order.getBusinessLine()))"),
    @Mapping(
        target = "purchaseType",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getPurchaseType(order.getPurchaseType()))"),
    @Mapping(
        target = "currencyType",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getCurrencyType(order.getCurrencyType()))"),
    @Mapping(
        target = "purchaseOrg",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getPurchaseOrgStr(order.getPurchaseOrg()))"),
    @Mapping(
        target = "payOrg",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getPayOrgStr(order.getPayOrg()))"),
    @Mapping(
        target = "expectedPayTime",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getExpectedPayTimeStr("
                + "order.getExpectedPayTime(),cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN))"),
    @Mapping(
        target = "detailSource",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getDetailSource(order.getDetailSource()))"),
    @Mapping(
        target = "payPurpose",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getPayPurpose(order.getPayPurpose()))"),
    @Mapping(target = "tradeUnit", ignore = true),
    @Mapping(target = "payeeUnit", ignore = true),
    @Mapping(target = "buyer", ignore = true),
    @Mapping(target = "additional", ignore = true)
  })
  PaymentCompareBo entityToCompareBo(PaymentApplyOrder order);

  @Mappings({
    @Mapping(
        target = "payPurpose",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getPayPurpose(vo.getPayPurpose()))"),
    @Mapping(
        target = "currencyType",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getCurrencyType(vo.getCurrencyType()))"),
    @Mapping(
        target = "purchaseType",
        expression =
            "java(com.daddylab.supplier.item.application.payment.PaymentApplyTrans.getPurchaseType(vo.getPurchaseType()))"),
    @Mapping(target = "status", expression = "java(vo.getStatus().getDesc())"),
    @Mapping(
        target = "businessLine",
        expression =
            "java(com.daddylab.supplier.item.infrastructure.enums.IEnum.getEnumByValue("
                + "com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BusinessLine.class,vo.getBusinessLine()).getDesc())"),
    @Mapping(
        target = "applyTimeStr2",
        expression =
            "java(com.daddylab.supplier.item.infrastructure.utils.DateUtil.parseTimeStamp(vo.getApplyTime(),com.daddylab.supplier.item.infrastructure.utils.DateUtil.DEFAULT_TIME))"),
    @Mapping(target = "purchaseOrg", source = "payOrgStr"),
    @Mapping(target = "realPayAmount", ignore = true),
    @Mapping(target = "otherChargeback", ignore = true),
  })
  PaymentExportDto voToExportDto(PaymentPageVo vo);

  List<PaymentExportDto> voToExportDtos(List<PaymentPageVo> voList);

  PaymentApplyOrderDetail copy(PaymentApplyOrderDetail oldDetail);
}
