package com.daddylab.supplier.item.infrastructure.kuaidaoyun.configs;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/5/21
 */
@ConfigurationProperties(prefix = "kuaidaoyun")
@Configuration
@RefreshScope
@Data
public class KdyConfig {
    private String uri = "http://i.kuaidaoyun.com";
    private String account;
    private String secret;
    private boolean ignoreSignErr;
}
