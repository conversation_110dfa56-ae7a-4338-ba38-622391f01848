package com.daddylab.supplier.item.application.refundOrder;

import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse.Order;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrderAmountDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSwapOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSwapOrderDetail;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import java.time.LocalDateTime;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2022/5/28
 */
@Mapper
public interface RefundOrderAssembler {

    RefundOrderAssembler INST = Mappers.getMapper(RefundOrderAssembler.class);

    @Mapping(target = "id", ignore = true)
    WdtRefundOrder apiOrderDtoToPo(Order order);

    @Mapping(target = "id", ignore = true)
    WdtRefundOrderDetail apiOrderDetailDtoToPo(Order.Detail detail, String refundNo);

    @Mapping(target = "id", ignore = true)
    WdtRefundOrderAmountDetail apiOrderAmountDetailDtoToPo(Order.AmountDetail amountDetail,
            String refundNo);

    @Mapping(target = "id", ignore = true)
    WdtSwapOrder apiSwapOrderDtoToPo(Order.SwapOrder swapOrder, String refundNo);

    @Mapping(target = "id", ignore = true)
    WdtSwapOrderDetail apiSwapOrderDetailDtoToPo(
            Order.SwapOrder.SwapOrderDetail swapOrderDetail, String refundNo, String tid);

    default LocalDateTime toLocalDateTime(String time) {
        return DateUtil.parseCompatibility(time);
    }
}
