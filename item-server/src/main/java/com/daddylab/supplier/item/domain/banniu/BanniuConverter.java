package com.daddylab.supplier.item.domain.banniu;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import wjb.open.api.model.banniu.OptionVo;
import wjb.open.api.response.mini.MiniColumnListResponse;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/9/7
 */
@Mapper
public interface BanniuConverter {
    BanniuConverter SHARED = Mappers.getMapper(BanniuConverter.class);

    default List<JSONObject> convertData(
            List<MiniColumnListResponse.ColumnOption> columnOptions,
            List<Map<Integer, String>> rawDataList) {
        final ArrayList<JSONObject> resultList = new ArrayList<>();
        for (Map<Integer, String> rawData : rawDataList) {
            final JSONObject data = new JSONObject();
            data.put("id", rawData.get(-1));
            for (Map.Entry<Integer, String> valueEntry : rawData.entrySet()) {
                final Optional<MiniColumnListResponse.ColumnOption> columnOptionOptional =
                        columnOptions.stream()
                                .filter(v -> v.getColumn_id().equals(valueEntry.getKey()))
                                .findFirst();
                if (!columnOptionOptional.isPresent()) {
                    continue;
                }
                final MiniColumnListResponse.ColumnOption columnOption = columnOptionOptional.get();
                final List<OptionVo> valueOptions = columnOption.getOptions();
                String rawValue = valueEntry.getValue();
                final Object[] resultValue = {rawValue};
                switch (columnOption.getType()) {
                    case "RADIO":
                    case "SELECT":
                        if (CollUtil.isNotEmpty(valueOptions)) {
                            valueOptions.stream()
                                    .filter(option -> option.getId().toString().equals(rawValue))
                                    .findFirst()
                                    .ifPresent(
                                            valueOption -> resultValue[0] = valueOption.getTitle());
                        }
                        break;
                    case "RELATION":
                        final List<MiniColumnListResponse.MiniTableColumnRelationVo>
                                relationOptions = columnOption.getRelation_options();
                        if (CollUtil.isNotEmpty(relationOptions)) {
                            final ArrayList<String> relationValues = new ArrayList<>();
                            final List<String> relationIds = StrUtil.split(rawValue, ",");
                            for (String relationId : relationIds) {
                                relationOptions.stream()
                                        .filter(
                                                relationOption ->
                                                        relationId.equals(
                                                                relationOption.getId().toString()))
                                        .findFirst()
                                        .map(
                                                MiniColumnListResponse.MiniTableColumnRelationVo
                                                        ::getTitle)
                                        .ifPresent(relationValues::add);
                            }
                            resultValue[0] = String.join(",", relationValues);
                        }
                        break;
                    case "CHILDFORMS":
                        final List<Map<Integer, String>> rawValueMaps =
                                JSON.parseObject(
                                        rawValue,
                                        new TypeReference<List<Map<Integer, String>>>() {});
                        final List<MiniColumnListResponse.ColumnOption> sonColumnBos =
                                columnOption.getSon_column_bos();
                        resultValue[0] = convertData(sonColumnBos, rawValueMaps);
                        break;
                }
                data.put(columnOption.getName(), resultValue[0]);
            }
            resultList.add(data);
        }
        return resultList;
    }
}
