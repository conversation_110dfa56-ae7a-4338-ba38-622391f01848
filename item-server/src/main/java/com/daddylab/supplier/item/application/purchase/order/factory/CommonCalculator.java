package com.daddylab.supplier.item.application.purchase.order.factory;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.QuantityCombinedPriceBO;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.QuantityCombinedPriceVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CombinationItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ComposeSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICombinationItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IComposeSkuService;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 价格计算器
 *
 * <AUTHOR> up
 * @date 2022年08月16日 10:36 AM
 */
@Slf4j
@Component
public class CommonCalculator {

    @Resource
    ICombinationItemService iCombinationItemService;

    @Resource
    IComposeSkuService icomposeSkuService;

    static Comparator<Integer> cmp = new Comparator<Integer>() {
        public int compare(Integer a, Integer b) {
            return b - a;
        }
    };

    /**
     * 阶梯部分计算。
     *
     * @param targetNum 目标数量
     * @param nums      组合价格阶梯分布。（1，2，3，4，5）。必须存在1.
     * @return {1:分布x1个，2:分布x2个}
     */
    public static Map<Integer, Integer> ladderDistribution(Integer targetNum, Collection<Integer> nums) {
        Map<Integer, Integer> resMap = new HashMap<>(nums.size());

        AtomicInteger reqNum = new AtomicInteger(targetNum);
        // 由大到小排列
        nums.stream().sorted(cmp).forEach(num -> {
            int i = reqNum.get() / num;
            if (i > 0) {
                reqNum.set(reqNum.get() - (i * num));
                resMap.put(num, i);
            }
        });
        return resMap;
    }

    /**
     * 根据阶梯价格表 计算总价
     *
     * @param reqPriceBoList 阶梯价格表
     * @param reqQuantity    请求数量
     * @return 凑满请求数量，并且价格累计达到最大。贪心，只求每一步的最优。
     */
    public static QuantityCombinedPriceVO stagedPrice0(List<QuantityCombinedPriceBO> reqPriceBoList, Integer reqQuantity) {
        if (CollUtil.isEmpty(reqPriceBoList)) {
            return new QuantityCombinedPriceVO();
        }
        Optional<QuantityCombinedPriceBO> one = reqPriceBoList.stream().filter(val -> val.getQuantity().equals(1)).findAny();
        if (!one.isPresent()) {
            log.error("阶梯价格计算异常。没有数量为1的价格。priceInfo:{}", JsonUtil.toJson(reqPriceBoList));
            return new QuantityCombinedPriceVO();
        }

        AtomicReference<Integer> reqRef = new AtomicReference<>(reqQuantity);
        AtomicReference<BigDecimal> amountSum = new AtomicReference<>(BigDecimal.ZERO);
        List<QuantityCombinedPriceVO.SpecVO> resultList = reqPriceBoList.stream()
                .sorted(Comparator.comparing(QuantityCombinedPriceBO::getQuantity).reversed())
                .map(val -> {
                    if (val.getQuantity() == 0) {
                        return null;
                    }
                    int specCount = reqRef.get() / val.getQuantity();
                    if (specCount > 0) {
                        amountSum.set(amountSum.get().add(val.getPrice().multiply(new BigDecimal(specCount))));
                        reqRef.set(reqRef.get() - specCount * val.getQuantity());
                        return new QuantityCombinedPriceVO.SpecVO(val, specCount);
                    }
                    return null;
                }).filter(Objects::nonNull).collect(Collectors.toList());

        QuantityCombinedPriceVO vo = new QuantityCombinedPriceVO();
        vo.setReqQuantity(reqQuantity);
        vo.setSpecList(resultList);
        vo.setGreedMaxAmount(amountSum.get());
        return vo;
    }


    /**
     * 计算组合装下单品的均摊成本价格
     * 计算公式 = 单品的成本金额占比 * 组合装成本价。
     *
     * @param combinationItemCode      组合装编码
     * @param combinationItemCostPrice 组合装成本
     * @return SkuPriceUnderSuiteNo 组合装下各个单品的数据
     */
    public List<SkuPriceUnderSuiteNo> getSkuCostPriceUnderSuiteNo(String combinationItemCode, BigDecimal combinationItemCostPrice) {
        combinationItemCostPrice = Objects.isNull(combinationItemCostPrice) ? BigDecimal.ZERO : combinationItemCostPrice;
        CombinationItem combinationItem = iCombinationItemService.lambdaQuery().eq(CombinationItem::getCode, combinationItemCode)
                .orderByDesc(CombinationItem::getId).select().one();
        if (Objects.isNull(combinationItem)) {
            return new LinkedList<>();
        }

        List<SkuPriceUnderSuiteNo> result = new LinkedList<>();
        BigDecimal finalCombinationItemCostPrice = combinationItemCostPrice;
        icomposeSkuService.lambdaQuery().eq(ComposeSku::getCombinationId, combinationItem.getId())
                .select().list()
                .forEach(composeSku -> {
                    BigDecimal costProportion;
                    if (Objects.isNull(composeSku.getCostProportion())) {
                        log.error("系统采购单，组合装均摊价格计算，单品成本金额占比比例为空。combinationItemId:{},composeSkuCode:{}",
                                composeSku.getCombinationId(), composeSku.getSkuCode());
                        costProportion = BigDecimal.ZERO;
                    } else {
                        costProportion = composeSku.getCostProportion();
                    }
                    BigDecimal skuNewPrice = finalCombinationItemCostPrice.multiply(costProportion)
                            .setScale(6, RoundingMode.HALF_UP);
                    SkuPriceUnderSuiteNo vo = new SkuPriceUnderSuiteNo();
                    vo.setSkuCode(composeSku.getSkuCode());
                    vo.setUnitPrice(skuNewPrice.divide(new BigDecimal(composeSku.getCount()), 6, RoundingMode.HALF_UP));
                    vo.setCount(composeSku.getCount());
                    result.add(vo);
                });
        return result;
    }

    /**
     * 组合装下，成本均摊之后，下属单品的成本结果，封装
     */
    @Data
    static
    public class SkuPriceUnderSuiteNo {
        private String skuCode;
        private BigDecimal unitPrice;
        private Integer count;
    }

    public static void main(String[] args) {
        QuantityCombinedPriceBO bo1 = new QuantityCombinedPriceBO();
        bo1.setCode("A");
        bo1.setQuantity(1);
        bo1.setPrice(new BigDecimal("69"));

        QuantityCombinedPriceBO bo2 = new QuantityCombinedPriceBO();
        bo2.setCode("B");
        bo2.setQuantity(2);
        bo2.setPrice(new BigDecimal("110"));

        List<QuantityCombinedPriceBO> list = ListUtil.of(bo1, bo2);

        QuantityCombinedPriceVO quantityCombinedPriceVO = stagedPrice0(list, 5);
        System.out.println(JSONUtil.toJsonStr(quantityCombinedPriceVO));


//        List<Integer> of = ListUtil.of(1, 2, 3);
//        Map<Integer, Integer> integerIntegerMap = ladderDistribution(9, of);
//        System.out.println(JsonUtil.toJson(integerIntegerMap));

//        String s = "1,2,3,4,5,6";
//        List<String> natureList = new ArrayList<>(Arrays.asList(s.split(",")));
//        List<String> strings = natureList.subList(0, 2);
//        System.out.println(JsonUtil.toJson(strings));
//        natureList.removeAll(strings);
//        System.out.println(JsonUtil.toJson(natureList));


    }

}
