package com.daddylab.supplier.item.infrastructure.gatewayimpl.base;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BaseUnit;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBaseUnitService;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> up
 * @date 2022/5/25 9:42 下午
 */
@Service
public class BaseUnitGatewayImpl implements com.daddylab.supplier.item.domain.base.BaseUnitGateway {

    public static final int CACHE_SECONDS = 60 * 30;
    public static final int CACHE_MAXIMUM_SIZE = 100;

    @Autowired
    IBaseUnitService iBaseUnitService;

    LoadingCache<String, Optional<BaseUnit>> cache = Caffeine.newBuilder()
            .expireAfterAccess(CACHE_SECONDS, TimeUnit.SECONDS)
            .maximumSize(CACHE_MAXIMUM_SIZE)
            .build(this::getBaseUnit0);

    @Override
    public Optional<BaseUnit> kingDeeNo(String unitName) {
        cache.invalidate(unitName);
        return cache.get(unitName);
    }

    public Optional<BaseUnit> getBaseUnit0(String unitName){
        return iBaseUnitService.lambdaQuery().eq(BaseUnit::getName, unitName)
                .select().oneOpt();
    }
}
