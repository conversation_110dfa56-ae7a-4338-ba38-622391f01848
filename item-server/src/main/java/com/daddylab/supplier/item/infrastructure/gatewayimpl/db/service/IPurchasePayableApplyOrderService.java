package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchasePayableApplyOrder;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 采购付款申请单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-14
 */
public interface IPurchasePayableApplyOrderService extends IDaddyService<PurchasePayableApplyOrder> {

}
