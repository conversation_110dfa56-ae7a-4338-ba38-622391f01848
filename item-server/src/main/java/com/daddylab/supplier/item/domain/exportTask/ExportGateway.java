package com.daddylab.supplier.item.domain.exportTask;

import com.daddylab.supplier.item.application.afterSales.AbnormalExportSheet;
import com.daddylab.supplier.item.application.afterSales.AfterSalesPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.CombinationItemPageQuery;
import com.daddylab.supplier.item.application.saleItem.dto.NewGoodsSheet;
import com.daddylab.supplier.item.application.saleItem.dto.SaleItemLibrarySheet;
import com.daddylab.supplier.item.application.saleItem.query.NewGoodsQueryPage;
import com.daddylab.supplier.item.application.saleItem.query.SaleItemLibraryQueryPage;
import com.daddylab.supplier.item.controller.item.dto.ExportCmd;
import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderPageQuery;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseQueryPage;
import com.daddylab.supplier.item.domain.exportTask.dto.combinationItem.ComposerSkuAllSheet;
import com.daddylab.supplier.item.domain.exportTask.dto.item.ExportItemDto;
import com.daddylab.supplier.item.domain.exportTask.dto.item.ExportItemNoPriceDto;
import com.daddylab.supplier.item.domain.exportTask.dto.item.ExportSkuDto;
import com.daddylab.supplier.item.domain.exportTask.dto.item.ExportSkuNoPriceDto;
import com.daddylab.supplier.item.domain.exportTask.dto.purchase.PurchaseOrderDetailSheet;
import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetailQuery;
import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetailSheet;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderQuery;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderSheet;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Purchase;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/2 10:04 上午
 * @description
 */
public interface ExportGateway {

    List<ExportSkuDto> querySku(ExportCmd cmd);

    List<ExportSkuNoPriceDto> querySkuNoPrice(ExportCmd cmd);

    List<ExportItemDto> queryItem(ExportCmd cmd);

    List<ExportItemNoPriceDto> queryItemNoPrice(ExportCmd cmd);

    Integer exportItemAndSkuCount(ExportCmd cmd);

    List<Purchase> queryPurchase(PurchaseQueryPage page);


    List<StockInOrderAndOrderDetailSheet> queryStockInOrder(StockInOrderAndOrderDetailQuery query);

    Integer stockInOrderCount(StockInOrderAndOrderDetailQuery stockInOrderAndOrderDetailQuery);

    List<StockOutOrderSheet> queryStockOutOrder(StockOutOrderQuery query);

    Integer stockOutOrderCount(StockOutOrderQuery query);

    // -------------- 以下为改造完成导出------------

    Integer countPurchaseDetailCount(PurchaseOrderPageQuery query);

    List<PurchaseOrderDetailSheet> queryPurchaseDetail(PurchaseOrderPageQuery query);

    Integer countCombinationItem(CombinationItemPageQuery query);

    List<ComposerSkuAllSheet> queryCombinationItem(CombinationItemPageQuery query);

    Integer countExportSaleItemLibrary(SaleItemLibraryQueryPage query);

    List<SaleItemLibrarySheet> queryExportSaleItemLibrary(SaleItemLibraryQueryPage query);

    Integer countNewGoods(NewGoodsQueryPage query);

    List<AbnormalExportSheet> queryAfterSalesAbnormalList(AfterSalesPageQuery pageQuery);

    Integer countAfterSalesAbnormalList(AfterSalesPageQuery pageQuery);

}
