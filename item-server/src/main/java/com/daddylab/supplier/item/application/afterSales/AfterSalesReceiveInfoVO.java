package com.daddylab.supplier.item.application.afterSales;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesReceiveResult;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesReceiveState;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Collection;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/25
 */
@Data
@ApiModel("确认收货信息VO")
public class AfterSalesReceiveInfoVO {


    /**
     * 退货仓库编号
     */
    @ApiModelProperty("退货仓库编号")
    private String returnWarehouseNo;

    /**
     * 退货仓库名称
     */
    @ApiModelProperty("退货仓库名称")
    private String returnWarehouse;

    /**
     * 收货时间
     */
    @ApiModelProperty("收货时间")
    private Long receiveTime;

    /**
     * 收货结果 1:货品完好 2:微损可入库 3:破损不可入库 4:数量不一致 5:商品不一致
     */
    @ApiModelProperty("GOOD 货品完好 SLIGHT 轻微可入库 BROKEN 破损不可入库 QUANTITY_WRONG 数量不一致 ITEM_WRONG 商品不一致")
    private AfterSalesReceiveResult receiveResult;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 状态 1:待收货 2:已收货 3:已确认
     */
    @ApiModelProperty("WAIT 待收货 RECEIVED 已收货 CONFIRMED 已确认")
    private AfterSalesReceiveState state;

    /**
     * 转寄图片
     */
    @ApiModelProperty("转寄图片")
    private Collection<String> images;
}
