package com.daddylab.supplier.item.domain.dataFetch;

/**
 * <AUTHOR>
 * @since 2022/4/29
 */
public class FetchInterruptedException extends FetchException {

    private static final long serialVersionUID = 1L;

    public FetchInterruptedException(FetchSegment fetchSegment) {
        super(fetchSegment);
    }

    public FetchInterruptedException(FetchDataType fetchDataType) {
        super(fetchDataType);
    }

    public FetchInterruptedException(String message,
            FetchDataType fetchDataType) {
        super(message, fetchDataType);
    }

    public FetchInterruptedException(Throwable cause,
            FetchDataType fetchDataType) {
        super(cause, fetchDataType);
    }

    public FetchInterruptedException(FetchSegment fetchSegment, String message) {
        super(fetchSegment, message);
    }

    public FetchInterruptedException(FetchDataType fetchDataType, String message) {
        super(fetchDataType, message);
    }


    @Override
    public String getMessage() {
        return "拉取被打断:" + super.getMessage();
    }
}
