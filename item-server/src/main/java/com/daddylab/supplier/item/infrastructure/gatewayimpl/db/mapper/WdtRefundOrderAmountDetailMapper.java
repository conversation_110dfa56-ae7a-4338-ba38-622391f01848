package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrderAmountDetail;
import java.util.Collection;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 旺店通退换金额明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Repository
public interface WdtRefundOrderAmountDetailMapper extends
        DaddyBaseMapper<WdtRefundOrderAmountDetail> {

    /**
     * 批量插入或者已存在重复记录时更新数据
     *
     * @param details 数据集合
     */
    void saveOrUpdateBatch(@Param("details") Collection<WdtRefundOrderAmountDetail> details);
}
