package com.daddylab.supplier.item.application.order;

import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrder;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import java.util.List;
import java.util.Set;
import javax.annotation.Nullable;

/**
 * <AUTHOR>
 * @since 2022/5/27
 */
public interface WdtOrderDataSyncService {

    /**
     * 根据原始单号保存或更新指定旺店通订单数据
     *
     * @param srcTids 旺店通交易单号
     */
    void saveOrUpdateBatchSpecifiedOrdersBySrcTids(List<String> srcTids);

    /**
     * 根据旺店通订单查询接口返回结果来更新库里的数据
     *
     * @param response 旺店通订单查询接口响应数据
     */
    void saveOrUpdateBatchByApiQueryResponse(TradeQueryQueryWithDetailResponse response);

    /**
     * 保存或者更新给定持久化对象
     *
     * @param wdtOrderPOs 旺店通订单持久化对象集合
     */
    void saveOrUpdateBatchByApiQueryResponse(List<WdtOrder> wdtOrderPOs);

    /**
     * 清理无效订单
     * @param tradeIds 旺店通订单ID（可以为空，为空遍历全表）
     * @return 清理订单数量
     */
    default int cleanOrders(@Nullable Set<Long> tradeIds) {
        return cleanOrders(tradeIds, DateUtil.currentTime());
    }

    /**
     * 清理无效订单
     * @param tradeIds 旺店通订单ID（可以为空，为空遍历全表）
     * @param currentTime 当前时间（标记逻辑删除时间）
     * @return 清理订单数量
     */
    int cleanOrders(@Nullable Set<Long> tradeIds, Long currentTime);
}
