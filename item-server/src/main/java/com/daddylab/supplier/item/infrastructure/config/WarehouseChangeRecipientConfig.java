package com.daddylab.supplier.item.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年05月08日 11:21 AM
 */
@ConfigurationProperties(prefix = "warehouse-change-recipient")
@Component
@RefreshScope
@Data
public class WarehouseChangeRecipientConfig {

    private String loginName;

}
