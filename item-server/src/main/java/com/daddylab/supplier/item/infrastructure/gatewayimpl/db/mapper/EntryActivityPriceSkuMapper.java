package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EntryActivityPriceSku;

/**
 * <p>
 * 入驻活动价格(SKU纬度) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-04
 */
public interface EntryActivityPriceSkuMapper extends DaddyBaseMapper<EntryActivityPriceSku> {

}
