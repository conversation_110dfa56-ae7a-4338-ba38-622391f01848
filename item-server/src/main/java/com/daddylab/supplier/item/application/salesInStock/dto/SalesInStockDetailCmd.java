package com.daddylab.supplier.item.application.salesInStock.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> up
 * @date 2023年10月23日 11:58 AM
 */
@Data
@ApiModel("查看销推入库单详情请求参数")
public class SalesInStockDetailCmd {

    @ApiModelProperty("入库单id")
    @NotNull(message = "入库单id不得为空")
    private Long stockinId;

    @ApiModelProperty("入库单号")
    @NotEmpty(message = "入库单好不得为空")
    private String orderNo;

    @ApiModelProperty("退换单号")
    @NotEmpty(message = "退换单好不得为空")
    private String refundNo;
}
