package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OaCallback;
import com.daddylab.supplier.item.types.oa.OaCgfkCallback;
import com.daddylab.supplier.item.types.process.ProcessBusinessType;

/**
 * <p>
 * OA回调数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
public interface IOaCallbackService extends IDaddyService<OaCallback> {

    void saveCallback(ProcessBusinessType businessType, String businessId, OaCgfkCallback callback);

}
