package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR> up
 * @date 2022年10月18日 10:13 AM
 */
@FeignClient(name = "PartnerOpenFeignClient", url = "${partner-open-server.url}", fallbackFactory = PartnerOpenFallbackFactory.class)
public interface PartnerOpenFeignClient {

    /**
     * 供应商查询，根据p系统的token。
     *
     * @param partnerToken
     * @return
     */
    @GetMapping(path = "/dpm/app/user/organization")
    @FeignLog(type = FeignLog.PARTNER_SERVER)
    Rsp<PartnerProviderResp> providerQueryByToken(@RequestHeader("Authorization") String partnerToken);

}
