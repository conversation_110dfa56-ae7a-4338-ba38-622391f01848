package com.daddylab.supplier.item.controller.test.job;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 补偿sku的金蝶id为空的情况
 * <AUTHOR> up
 * @date 2022年12月02日 9:48 AM
 */
@Component
@Slf4j
public class SkuKingDeeIdFillingCompensate {

    @Resource
    IItemSkuService iItemSkuService;

    @Resource
    IItemService iItemService;

    @XxlJob("skuKingDeeIdFillingCompensate")
    public void run(){
        // 查询sku中金蝶id为空的数据
        iItemSkuService.lambdaQuery().isNull(ItemSku::getKingDeeId).or().eq(ItemSku::getKingDeeId,"")
                .select().list().forEach(val->{

                });
    }


}
