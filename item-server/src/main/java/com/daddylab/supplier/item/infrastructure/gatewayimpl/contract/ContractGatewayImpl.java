package com.daddylab.supplier.item.infrastructure.gatewayimpl.contract;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.contract.ContractGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerContract;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerContractQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerFeignClient;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.types.contract.ContractDropdownItem;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/4/1
 */
@Service
@RequiredArgsConstructor
public class ContractGatewayImpl implements ContractGateway {

    private final PartnerFeignClient partnerFeignClient;

    @Override
    public List<ContractDropdownItem> contractDropdownList(String contractNo, Long partnerProviderId, Integer status) {
        final PartnerContractQuery query = new PartnerContractQuery();
        if (StringUtils.isNotBlank(contractNo)) {
            query.setContractNo(contractNo);
        }
        if (Objects.nonNull(partnerProviderId)) {
            query.setOrganizationId(partnerProviderId);
        }
        if (Objects.nonNull(status)) {
            query.setStatus(status);
        }
        final Rsp<List<PartnerContract>> listRsp = partnerFeignClient.contractQuery(query);
        if (!listRsp.isSuccess()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "从P系统查询合同下拉列表失败");
        }
        if (CollUtil.isEmpty(listRsp.getData())) {
            return Collections.emptyList();
        }
        return listRsp.getData().stream().map(ContractAssembler.INST::partnerContractToContractDropdownItem)
                .collect(Collectors.toList());
    }

    @Mapper
    interface ContractAssembler {
        ContractAssembler INST = Mappers.getMapper(ContractAssembler.class);

        ContractDropdownItem partnerContractToContractDropdownItem(PartnerContract partnerContract);
    }
}
