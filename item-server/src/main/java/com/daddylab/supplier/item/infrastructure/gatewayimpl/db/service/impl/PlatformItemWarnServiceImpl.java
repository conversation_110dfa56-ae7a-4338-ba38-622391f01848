package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemWarn;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemWarnMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemWarnService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 平台商品预警 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Service
public class PlatformItemWarnServiceImpl extends DaddyServiceImpl<PlatformItemWarnMapper, PlatformItemWarn> implements IPlatformItemWarnService {

    @Override
    public PlatformItemWarn createPlatformItemWarn(PlatformItemWarnType platformItemWarnType, Long platformItemId, Long platformSkuId) {
        final PlatformItemWarn platformItemWarn = new PlatformItemWarn();
        platformItemWarn.setPlatformItemId(platformItemId);
        platformItemWarn.setPlatformItemSkuId(platformSkuId);
        platformItemWarn.setWarnType(platformItemWarnType);
        platformItemWarn.setStatus(PlatformItemWarnStatus.UNHANDLED);
        save(platformItemWarn);
        return platformItemWarn;
    }
}
