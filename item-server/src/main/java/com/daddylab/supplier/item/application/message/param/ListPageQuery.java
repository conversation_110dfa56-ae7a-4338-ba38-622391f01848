package com.daddylab.supplier.item.application.message.param;

import com.alibaba.cola.dto.PageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MessagePushType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 3:08 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("消息列表请求")
public class ListPageQuery extends PageQuery {

    private static final long serialVersionUID = -3441755649214202775L;

    @ApiModelProperty("消息接收者id，非必填")
    private Long recipientId;

    @ApiModelProperty(value = "消息推送类型。默认系统消息")
    private MessagePushType pushType = MessagePushType.DEFAULT;
}
