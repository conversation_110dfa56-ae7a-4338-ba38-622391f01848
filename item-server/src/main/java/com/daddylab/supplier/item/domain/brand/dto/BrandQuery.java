package com.daddylab.supplier.item.domain.brand.dto;

import com.alibaba.cola.dto.PageQuery;
import com.daddylab.supplier.item.common.enums.EnableStatusEnum;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;

@Data
public class BrandQuery extends PageQuery {
    /**
     * id
     */
    @Positive(message = "ID不合法")
    private Long id;

    /**
     * 品牌编号
     */
    @Size(message = "品牌编号长度应当在[{min},{max}]范围内", max = 20)
    private String sn;

    /**
     * 品牌名称
     */
    @Size(message = "品牌名称长度应当在[{min},{max}]范围内", max = 50)
    private String name;

    /**
     * 所属供应商ID
     */
    @Positive(message = "供应商ID不合法")
    private Long providerId;

    /**
     * 状态 0:停用 1:正常
     */
    private EnableStatusEnum status;

    @ApiModelProperty("合作模式（业务线）筛选（多选）")
    private List<Integer> businessLine = new ArrayList<>();
}
