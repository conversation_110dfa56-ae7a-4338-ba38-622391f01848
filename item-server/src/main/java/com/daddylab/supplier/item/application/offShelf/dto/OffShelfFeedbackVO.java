package com.daddylab.supplier.item.application.offShelf.dto;

import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/9
 */
@Data
public class OffShelfFeedbackVO {
    @ApiModelProperty("反馈ID")
    private Long feedbackId;

    @ApiModelProperty("处理人ID")
    private Long processUid;

    @ApiModelProperty("处理人")
    private StaffBrief processUser;

    @ApiModelProperty("处理时间")
    private Long processTime;

    /**
     * 任务ID
     */
    @ApiModelProperty("任务ID")
    private String taskId;

    /**
     * 下架反馈 1已下架 2未下架 3没有上架
     */
    @ApiModelProperty(value = "下架反馈", notes = "1已下架 2未下架 3没有上架")
    private Integer feedback;

    /**
     * 下架链接
     */
    @ApiModelProperty("下架链接")
    private String link;

    /**
     * 备注说明
     */
    @ApiModelProperty("备注说明")
    private String remark;

}
