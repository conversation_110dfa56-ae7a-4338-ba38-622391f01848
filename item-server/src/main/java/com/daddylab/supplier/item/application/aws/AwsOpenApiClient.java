package com.daddylab.supplier.item.application.aws;

import com.actionsoft.bpms.api.OpenApiClient;
import com.daddylab.supplier.item.application.aws.service.AwsProcessClient;
import com.daddylab.supplier.item.application.aws.service.impl.AwsProcessClientImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@RefreshScope
public class AwsOpenApiClient {

    @Value("${aws.accessKey}")
    private String accessKey;

    @Value("${aws.secret}")
    private String secret;

    @Value("${aws.apiServer}")
    private String apiServer;

    public OpenApiClient getOpenApiClient() {
        return new OpenApiClient(apiServer, accessKey, secret);
    }

    public AwsProcessClient getAwsProcessClient() {
        return new AwsProcessClientImpl(getOpenApiClient());
    }


}
