package com.daddylab.supplier.item.controller.otherPay.dto;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @ClassName OtherPayQueryPage.java
 * @description
 * @createTime 2022年03月24日 15:48:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("其他应付单分页查询请求实体")
public class OtherPayQueryPage extends PageQuery {

    private static final long serialVersionUID = 5249977327656472433L;

    @ApiModelProperty(value = "应付单号")
    private String payNo;

    @ApiModelProperty(value = "供应商id")
    private Long providerId;

    @ApiModelProperty(value = "应付年")
    private Long payYear;

    @ApiModelProperty(value = "应付月")
    private Long payMonth;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "采购员id")
    private Long buyerId;

    private Boolean showAll;

    private Long userId;

}
