package com.daddylab.supplier.item.application.aws.event;

import cn.hutool.core.lang.Assert;

import com.actionsoft.sdk.service.model.AbstTaskInstanceModel;
import com.actionsoft.sdk.service.model.HistoryTaskInstance;
import com.actionsoft.sdk.service.model.TaskInstance;
import com.daddylab.supplier.item.application.aws.enums.AwsActionEnum;
import com.daddylab.supplier.item.application.aws.enums.AwsControlStateEnum;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.message.wechat.MsgEvent;
import com.daddylab.supplier.item.application.message.wechat.MsgEventType;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.otherPay.OtherPayBizService;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizService;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.application.stockInOrder.StockInOrderBizService;
import com.daddylab.supplier.item.application.stockOutOrder.StockOutOrderBizService;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.domain.otherPay.enums.OtherPayStatus;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockOutOrderState;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.LogEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PurchaseOrderState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.google.common.eventbus.Subscribe;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> up
 * @date 2022/5/5 1:44 下午
 */
@Slf4j
@EventBusListener(value = "awsChangeEventListener", mode = EventBusListener.MODE.ASYNC)
public class AwsChangeEventListener {

    @Autowired
    PurchaseOrderBizService purchaseOrderBizService;

    @Autowired
    StockOutOrderBizService stockOutOrderBizService;

    @Autowired
    IPurchaseOrderService iPurchaseOrderService;

    @Autowired
    OtherPayBizService otherPayBizService;

    @Autowired
    IOtherPayService iOtherPayService;

    @Autowired
    StockInOrderBizService stockInOrderBizService;

    @Autowired
    private IItemService itemService;

    @Autowired
    private ItemBizService itemBizService;

    @Autowired
    private IItemDrawerService itemDrawerService;

    @Autowired
    private INewGoodsService newGoodsService;

    @Autowired
    ILogService logService;

    @Autowired
    private IItemLaunchStatsService itemLaunchStatsService;

    @Autowired
    private StaffService staffService;

    @Autowired
    private MsgSender msgSender;

    @Autowired
    private RefreshConfig refreshConfig;

    @Autowired
    private ProviderGateway providerGateway;

    @Subscribe
    public void listener(AwsChangeEvent awsChangeEvent) {
        log.info("aws通知事件响应数据，awsChangeEvent:{}", JsonUtil.toJson(awsChangeEvent));
        Long businessId = awsChangeEvent.getBusinessId();
        PurchaseTypeEnum type = awsChangeEvent.getType();
        AwsControlStateEnum awsControlStateEnum = awsChangeEvent.getAwsControlStateEnum();
        AwsActionEnum awsActionEnum = awsChangeEvent.getAwsActionEnum();

        Log log = new Log();
        log.setResp(JsonUtil.toJson(awsChangeEvent));
        log.setType(LogEnum.AWS_SERVER.getValue());
        logService.save(log);

        // 商品库审核（流程改为自己实现，后续移除掉这边的逻辑）
        updateItemLaunchState(businessId, type, awsControlStateEnum, awsActionEnum, awsChangeEvent);
        // 采购订单
        updatePurchaseOrderState(businessId, type, awsControlStateEnum, awsActionEnum, awsChangeEvent);
        // 退料出库
        updateStockOutOrderState(businessId, type, awsControlStateEnum, awsActionEnum);
        //其他应付
        updateOtherPay(businessId, type, awsControlStateEnum, awsActionEnum);
    }

    private void updateItemLaunchState(Long businessId, PurchaseTypeEnum type, AwsControlStateEnum awsControlStateEnum, AwsActionEnum awsActionEnum, AwsChangeEvent awsChangeEvent) {
        if (PurchaseTypeEnum.ITEM_LIBRARY_AUDIT.equals(type)) {
            ItemDrawer itemDrawer = itemDrawerService.getById(businessId);
            Assert.notNull(itemDrawer, "businessId非法，查询商品抽屉为空");
            Long itemId = itemDrawer.getItemId();

            //流程是否完结
            final boolean processIsEnd = AwsControlStateEnum.END.equals(awsControlStateEnum);

            //统计商品上新流程审核信息
            final ItemLaunchStats itemLaunchStats = statItemLaunchAudit(awsChangeEvent, itemId);

            //有审核完结时间说明对应审核节点完成
            final boolean legalAuditEnd = NumberUtil.isPositive(
                    itemLaunchStats.getToBeLegalAuditEndTime());

            //法务负责人ID
            final Long legalId = itemLaunchStats.getToBeLegalAuditProcessorUid();
            if (!Objects.isNull(legalId)) {
                newGoodsService.lambdaUpdate().eq(NewGoods::getItemId, itemId)
                        .set(NewGoods::getLegalId, legalId).update();
            }

            // 审核通过【现在审核通过后的下一个流程为待修改】
            if (processIsEnd) {
                itemBizService.updateLaunchStatus(itemId, ItemLaunchStatus.TO_BE_UPDATED);
            } else if (legalAuditEnd) {
                //审核流程未完结、法务审核通过，变更为待QC审核
                if (itemBizService.updateAuditStatus(itemId, ItemAuditStatus.WAIT_QC_AUDIT)) {

                    //再触发一次待审核消息推送
                    EventBusUtil.post(MsgEvent
                            .of(Collections.singletonList(itemId), MsgEventType.DESIGN_TO_AUDIT_QC), true);
                }
            }


        }
    }

    private ItemLaunchStats statItemLaunchAudit(AwsChangeEvent awsChangeEvent, Long itemId) {
        final Optional<TaskInstance> activeLegalTaskOptional = awsChangeEvent.getTaskInstances().stream()
                .filter(v -> v.getTitle().contains("法务") && !v.getControlState()
                        .equals("delete")).findFirst();
        final Optional<HistoryTaskInstance> legalTaskOptional = awsChangeEvent.getHistoryTaskInstances()
                .stream().filter(v -> v.getTitle().contains("法务") && !v.getControlState()
                        .equals("delete")).findFirst();
        final String legalProcessorLoginName = activeLegalTaskOptional.map(TaskInstance::getTarget)
                .orElse(legalTaskOptional.map(TaskInstance::getTarget).orElse(null));

        final Optional<TaskInstance> activeQcTaskOptional = awsChangeEvent.getTaskInstances()
                .stream().filter(v -> v.getTitle().contains("QC") && !v.getControlState()
                        .equals("delete")).findFirst();
        final Optional<HistoryTaskInstance> qcTaskOptional = awsChangeEvent.getHistoryTaskInstances()
                .stream().filter(v -> v.getTitle().contains("QC") && !v.getControlState()
                        .equals("delete")).findFirst();
        final String qcProcessorLoginName = activeQcTaskOptional.map(TaskInstance::getTarget)
                .orElse(qcTaskOptional.map(TaskInstance::getTarget).orElse(null));

        final List<DadStaffVO> staffList = staffService.getStaffListByLoginName(
                Stream.of(legalProcessorLoginName, qcProcessorLoginName).filter(Objects::nonNull).collect(
                        Collectors.toList()));

        final Long legalProcessorId = staffList.stream()
                .filter(v -> v.getLoginName().equals(legalProcessorLoginName)).findFirst()
                .map(DadStaffVO::getUserId).orElse(0L);

        final Long qcProcessorId = staffList.stream()
                .filter(v -> v.getLoginName().equals(qcProcessorLoginName)).findFirst()
                .map(DadStaffVO::getUserId).orElse(0L);

        Function<Timestamp, Long> timestampToLongFunc = timestamp -> timestamp.toInstant().getEpochSecond();
        return itemLaunchStatsService.auditStat(itemId,
                legalProcessorId,
                activeLegalTaskOptional.map(TaskInstance::getBeginTime).map(timestampToLongFunc)
                        .orElse(legalTaskOptional.map(AbstTaskInstanceModel::getBeginTime)
                                .map(timestampToLongFunc).orElse(0L)),
                legalTaskOptional.map(HistoryTaskInstance::getEndTime).map(timestampToLongFunc)
                        .orElse(0L),
                qcProcessorId,
                activeQcTaskOptional.map(TaskInstance::getBeginTime).map(timestampToLongFunc)
                        .orElse(qcTaskOptional.map(AbstTaskInstanceModel::getBeginTime)
                                .map(timestampToLongFunc).orElse(0L)),
                qcTaskOptional.map(HistoryTaskInstance::getEndTime).map(timestampToLongFunc)
                        .orElse(0L)
        );
    }

    private void updatePurchaseOrderState(Long businessId, PurchaseTypeEnum type, AwsControlStateEnum awsControlStateEnum, AwsActionEnum awsActionEnum, AwsChangeEvent awsChangeEvent) {
        if (PurchaseTypeEnum.PURCHASE_ORDER.equals(type)) {
            PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(businessId);
            Assert.notNull(purchaseOrder, "businessId非法，查询采购单位空");

            // 流程完结
            if (AwsControlStateEnum.END.equals(awsControlStateEnum)) {
                // 如果订单状态是待提交，这个时候将订单状态更新，待提交->待审核。@todo:??这个逻辑好奇怪
                if (PurchaseOrderState.WAIT_SUBMIT.getValue().equals(purchaseOrder.getState())) {
                    purchaseOrderBizService.updateState(businessId, PurchaseOrderState.WAIT_CHECK.getValue());
                }
                // 如果订单状态是待审核，这个时候将订单状态更新，待审核->审核通过
                if (PurchaseOrderState.WAIT_CHECK.getValue().equals(purchaseOrder.getState())) {
                    purchaseOrderBizService.updateState(businessId, PurchaseOrderState.PASS_AUDIT.getValue());

                    // 企微通知采购单流程审核通过
                    noticePurchaseOrderProcessComplete(purchaseOrder, true);
                }
                //  如果订单状态是审核拒绝，这个时候将订单状态更新，审核拒绝->待提交。
                if (PurchaseOrderState.REJECT_AUDIT.getValue().equals(purchaseOrder.getState())) {
                    purchaseOrderBizService.updateState(businessId, PurchaseOrderState.WAIT_SUBMIT.getValue());
                }

                return;
            }

            // 审核通过（流程未结束）
            if (Objects.equals(awsActionEnum, AwsActionEnum.AGREE)) {
                purchaseOrderBizService.noticePurchaseOrderCurrentProcessors(
                        purchaseOrder, awsChangeEvent.getTaskInstances());
            }

            // 审核拒绝
            if (Objects.equals(awsActionEnum, AwsActionEnum.DISAGREE)) {
                // 如果订单状态是待审核，审核拒绝状态
                if (PurchaseOrderState.WAIT_CHECK.getValue().equals(purchaseOrder.getState())) {
                    purchaseOrderBizService.updateState(businessId, PurchaseOrderState.REJECT_AUDIT.getValue());

                    // 企微通知采购单流程审核通过
                    noticePurchaseOrderProcessComplete(purchaseOrder, false);
                }
                return;
            }
            // 撤销审核
            if (Objects.equals(awsActionEnum, AwsActionEnum.RE_SUBMIT)) {
                // 如果订单状态是撤回审核，这个时候将订单状态更新，撤回审核->待审核
                if (PurchaseOrderState.WITHDRAW_AUDIT.getValue().equals(purchaseOrder.getState())) {
                    purchaseOrderBizService.updateState(businessId, PurchaseOrderState.WAIT_CHECK.getValue());
                }
                if (PurchaseOrderState.REJECT_AUDIT.getValue().equals(purchaseOrder.getState())) {
                    purchaseOrderBizService.updateState(businessId, PurchaseOrderState.WAIT_CHECK.getValue());
                }
            }
        }
    }



    public void noticePurchaseOrderProcessComplete(PurchaseOrder purchaseOrder, boolean passOrRefuse) {
        final Long createdUid = purchaseOrder.getCreatedUid();
        final Optional<Provider> providerOptional = Optional.ofNullable(providerGateway.getById(purchaseOrder.getProviderId()));

        final Long buyerUserId = purchaseOrder.getBuyerUserId();
        final DadStaffVO buyerUser = staffService.getStaff(buyerUserId);
        Assert.notNull(buyerUser, "【采购单】流程消息提醒异常，关联采购员用户信息查询异常");

        final DadStaffVO createdUser = staffService.getStaff(createdUid);
        Assert.notNull(createdUser, "【采购单】流程消息提醒异常，创建用户信息查询异常");

        final WechatMsg wechatMsg = new WechatMsg();
        final String status = passOrRefuse ? "审核通过" : "审核拒绝";
        wechatMsg.setTitle(
                String.format(
                        "%s %s 采购单 %s %s",
                        createdUser.getNickname(),
                        providerOptional.map(Provider::getName).orElse("?"),
                        purchaseOrder.getNo(), status));
        wechatMsg.setContent(String.format("您提交的采购单%s，请查看", status));
        final String link = refreshConfig.getDomain() + String.format(
                "/commodity-purchase/purchase-order/review?id=%s",
                purchaseOrder.getId());
        wechatMsg.setLink(link);
        wechatMsg.setRecipient(
                Stream.of(createdUser, buyerUser)
                        .map(DadStaffVO::getQwUserId)
                        .distinct()
                        .collect(Collectors.joining(",")));
        wechatMsg.setType(1);
        msgSender.send(wechatMsg);
    }


    private void updateStockOutOrderState(Long businessId, PurchaseTypeEnum type, AwsControlStateEnum awsControlStateEnum, AwsActionEnum awsActionEnum) {
        if (PurchaseTypeEnum.OUT_STOCK_PAYABLE.equals(type)) {
            // 审核通过
            if (AwsControlStateEnum.END.equals(awsControlStateEnum)) {
                stockOutOrderBizService.updateState(businessId, StockOutOrderState.NO_STOCK_OUT.getValue());
            }
            // 审核拒绝
            if (AwsActionEnum.DISAGREE.equals(awsActionEnum)) {
                stockOutOrderBizService.updateState(businessId, StockOutOrderState.REJECT.getValue());
            }
            // 撤回重办
            if (AwsActionEnum.RE_SUBMIT.equals(awsActionEnum)) {
                stockOutOrderBizService.updateState(businessId, StockOutOrderState.NO_AUDIT.getValue());
            }
        }
    }

    private void updateOtherPay(Long businessId, PurchaseTypeEnum type, AwsControlStateEnum awsControlStateEnum, AwsActionEnum awsActionEnum) {
        if (PurchaseTypeEnum.OTHER_PAYABLE.equals(type)) {
            OtherPay otherPay = iOtherPayService.getById(businessId);
            // 审核通过
            Assert.notNull(otherPay, "businessId非法，查询其他应付为空");

            // 审核通过
            if (AwsControlStateEnum.END.equals(awsControlStateEnum)) {
                // 如果订单状态是待提交，这个时候将订单状态更新，待提交->待审核。
                if (OtherPayStatus.NO_COMMIT.getValue().equals(otherPay.getStatus())) {
                    otherPayBizService.updateState(businessId, OtherPayStatus.WAIT_AUDIT.getValue());
                    return;
                }
                // 如果订单状态是待审核，这个时候将订单状态更新，待审核->审核通过
                if (OtherPayStatus.WAIT_AUDIT.getValue().equals(otherPay.getStatus())) {
                    otherPayBizService.updateState(businessId, OtherPayStatus.FINISH.getValue());
                    return;
                }
                //  如果订单状态是审核拒绝，这个时候将订单状态更新，审核拒绝->待提交。
                if (OtherPayStatus.REFUSE.getValue().equals(otherPay.getStatus())) {
                    otherPayBizService.updateState(businessId, OtherPayStatus.NO_COMMIT.getValue());
                    return;
                }
                // 如果订单状态是撤回审核，这个时候将订单状态更新，撤回审核->待审核
                if (OtherPayStatus.REVOCATION.getValue().equals(otherPay.getStatus())) {
                    otherPayBizService.updateState(businessId, OtherPayStatus.WAIT_AUDIT.getValue());
                    return;
                }
            }

            // 审核拒绝
            if (Objects.equals(awsActionEnum, AwsActionEnum.DISAGREE)) {
                // 如果订单状态是待审核，审核拒绝状态
                otherPayBizService.updateState(businessId, OtherPayStatus.REFUSE.getValue());
            }
            // 撤销重办
            if (Objects.equals(awsActionEnum, AwsActionEnum.RE_SUBMIT)) {
                // 如果订单状态是待审核，审核拒绝状态
                if (OtherPayStatus.REVOCATION.getValue().equals(otherPay.getStatus())) {
                    otherPayBizService.updateState(businessId, OtherPayStatus.WAIT_AUDIT.getValue());
                }
            }

        }
    }

}
