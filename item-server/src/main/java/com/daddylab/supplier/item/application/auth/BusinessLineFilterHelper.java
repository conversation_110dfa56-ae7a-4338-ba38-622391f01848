package com.daddylab.supplier.item.application.auth;

import cn.hutool.core.collection.CollectionUtil;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/19
 */
public class BusinessLineFilterHelper {
    public static void setBusinessLinesFilter(
            QueryWrapper<?> queryWrapper, List<Integer> businessLine) {
        if (CollectionUtil.isNotEmpty(businessLine)) {
            queryWrapper.and(
                    q -> {
                        for (Integer line : businessLine) {
                            q.or().eq("is_business_line" + line, 1);
                        }
                    });
        }
    }
}
