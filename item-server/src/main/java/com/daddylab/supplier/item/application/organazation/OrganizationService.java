package com.daddylab.supplier.item.application.organazation;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Organization;
import com.daddylab.supplier.item.types.DropdownItem;
import com.daddylab.supplier.item.types.DropdownQuery;

/**
 * <AUTHOR>
 * @since 2022/4/1
 */
public interface OrganizationService {
    PageResponse<DropdownItem<Long>> orgDropdownQuery(DropdownQuery query);

    /**
     * 通过ID查询
     * @param id
     * @return
     */
    SingleResponse<Organization> getById(Long id);
}
