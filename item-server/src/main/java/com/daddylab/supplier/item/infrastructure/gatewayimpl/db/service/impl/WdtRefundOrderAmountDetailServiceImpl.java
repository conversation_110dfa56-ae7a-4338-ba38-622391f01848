package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrderAmountDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundOrderAmountDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtRefundOrderAmountDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 旺店通退换金额明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Service
public class WdtRefundOrderAmountDetailServiceImpl extends DaddyServiceImpl<WdtRefundOrderAmountDetailMapper, WdtRefundOrderAmountDetail> implements IWdtRefundOrderAmountDetailService {

}
