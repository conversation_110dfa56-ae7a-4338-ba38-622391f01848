package com.daddylab.supplier.item.application.itemSync;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.itemSync.types.ItemSyncTbParam;
import com.daddylab.supplier.item.application.saleItem.vo.ThirdSyncVO;
import com.daddylab.supplier.item.controller.item.dto.ThirdSyncPageQuery;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/9/20
 */
public interface ThirdPlatformSyncBizService {

    Response syncTb(List<ItemSyncTbParam> params);

    PageResponse<ThirdSyncVO> syncList(ThirdSyncPageQuery pageQuery);

}
