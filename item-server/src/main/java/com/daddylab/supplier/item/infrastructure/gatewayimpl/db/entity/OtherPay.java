package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 其他应付
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class OtherPay implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 应付单号
     */
    private String payNo;

    /**
     * 应付年
     */
    private Long payYear;

    /**
     * 应付月
     */
    private Long payMonth;

    /**
     * 供应商id
     */
    private Long providerId;

    /**
     * 应付总金额
     */
    private BigDecimal payTotal;

    /**
     * 状态 0:已完成 1:待提交 2:待审核 3:已拒绝 4:已撤回
     */
    private Integer status;

    /**
     * 采购员id
     */
    private Long buyerId;

    /**
     * 采购组织id
     */
    private Long organizationId;

    /**
     * 凭证
     */
    private String voucher;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审批流程id
     */
    private String approvalId;

    /**
     * 审核状态
     */
    private Integer auditedStatus;

    /**
     * 审核人
     */
    private Integer auditedUid;

    /**
     * 审核通过日期
     */
    private Long auditedAt;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否删除，不能做业务。这是软删除标记
     */
    @TableLogic
    private Integer isDel;

    private Long deletedAt;


}
