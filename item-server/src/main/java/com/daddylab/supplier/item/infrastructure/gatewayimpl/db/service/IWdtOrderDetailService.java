package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetail;
import java.util.List;

/**
 * <p>
 * 旺店通订单明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
public interface IWdtOrderDetailService extends IDaddyService<WdtOrderDetail> {


    List<WdtOrderDetail> listByTradeIds(List<Long> wdtTradeIds);

    List<WdtOrderDetail> listBySrcTid(String srcTid);
}
