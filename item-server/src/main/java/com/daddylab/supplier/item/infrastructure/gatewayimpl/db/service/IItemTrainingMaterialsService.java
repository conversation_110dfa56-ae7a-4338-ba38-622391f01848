package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemTrainingMaterials;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemTrainingMaterialsMapper;

/**
 * <p>
 * 新品商品培训资料 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
public interface IItemTrainingMaterialsService extends IDaddylabServicePlus<ItemTrainingMaterials, ItemTrainingMaterialsMapper> {

    ItemTrainingMaterials getByItemId(Long itemId);
}
