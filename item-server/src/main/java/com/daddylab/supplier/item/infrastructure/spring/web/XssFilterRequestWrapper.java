package com.daddylab.supplier.item.infrastructure.spring.web;

import cn.hutool.core.io.IoUtil;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.utils.HTMLFilterFactory;
import com.daddylab.supplier.item.infrastructure.utils.HtmlUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import lombok.NonNull;
import org.springframework.http.MediaType;

/**
 * <AUTHOR>
 * @since 2022/12/9
 */
public class XssFilterRequestWrapper extends HttpServletRequestWrapper {

    private final MediaType[] mediaTypes;

    /**
     * Constructs a request object wrapping the given request.
     *
     * @param request    The request to wrap
     * @param mediaTypes mediaTypes
     * @throws IllegalArgumentException if the request is null
     */
    public XssFilterRequestWrapper(HttpServletRequest request, MediaType[] mediaTypes) {
        super(request);
        this.mediaTypes = mediaTypes;
    }

    @Override
    public String[] getParameterValues(String name) {
        return Optional.ofNullable(super.getParameterValues(name))
                .map(values -> Arrays.stream(values).map(this::filter).toArray(String[]::new))
                .orElse(null);
    }

    @Override
    public String getParameter(String name) {
        return Optional.ofNullable(super.getParameter(name)).map(this::filter).orElse(null);
    }

    private String filter(String raw) {
        return HTMLFilterFactory.extented().filter(raw);
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        return Optional.ofNullable(super.getParameterMap()).map(parameterMap -> {
            final Set<Entry<String, String[]>> entries = parameterMap.entrySet();
            return entries.stream().collect(Collectors.toMap(Entry::getKey,
                    entry -> Arrays.stream(entry.getValue()).map(this::filter)
                            .toArray(String[]::new)));
        }).orElse(null);
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        if (isHandleMediaType()) {
            final Charset charset = Optional.ofNullable(getCharacterEncoding())
                    .map(Charset::forName)
                    .orElse(Charset.defaultCharset());
            final String bodyStr = IoUtil.read(super.getInputStream(), charset);
            if (StringUtil.isBlank(bodyStr)) {
                return new ByteArrayServletInputStream(bodyStr.getBytes(charset));
            }
            final byte[] bytes;
            if (MediaType.APPLICATION_JSON.equalsTypeAndSubtype(getReqMediaType())) {
                final JsonNode bodyJsonNode;
                try {
                    bodyJsonNode = Objects.requireNonNull(JsonUtil.parse(bodyStr));
                } catch (Exception e) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "请求报文非有效JSON格式");
                }
                final JsonNode filteredJsonNode = JsonUtil.mapRecursive(bodyJsonNode, this::filterNode);
                bytes = filteredJsonNode.toString().getBytes(charset);
            } else {
                bytes = filter(bodyStr).getBytes(charset);
            }
            return new ByteArrayServletInputStream(bytes);
        }
        return super.getInputStream();
    }

    private JsonNode filterNode(JsonNode node) {
        if (node.isTextual()) {
            final String text = node.asText();
            if (HtmlUtil.containHtmlTag(text)) {
                return JsonNodeFactory.instance.textNode(filter(text));
            }
        }
        return node;
    }

    private boolean isHandleMediaType() {
        return Arrays.stream(mediaTypes)
                .anyMatch(v -> v.equalsTypeAndSubtype(getReqMediaType()));
    }

    @NonNull
    private MediaType getReqMediaType() {
        return Optional.ofNullable(this.getContentType())
                .filter(StringUtil::isNotBlank)
                .map(MediaType::parseMediaType)
                .orElse(MediaType.APPLICATION_JSON); //未传默认作为 application/json 处理
    }
}
