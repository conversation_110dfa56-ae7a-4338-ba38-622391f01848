package com.daddylab.supplier.item.application.platformItem.data;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/3/14
 */
@Data
@ApiModel("组合装组成商品")
public class PlatformItemDetailSkuComposeItem {
    @ApiModelProperty("商品SKU")
    String skuCode;
    @ApiModelProperty("规格名称")
    String skuName;
    @ApiModelProperty("数量")
    Integer num;
    @ApiModelProperty("同步库存")
    Integer syncStock;
    @ApiModelProperty("可用库存")
    Integer availableStock;
}
