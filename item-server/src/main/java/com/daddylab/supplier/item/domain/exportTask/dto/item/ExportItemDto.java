package com.daddylab.supplier.item.domain.exportTask.dto.item;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 导出excel,根据商品维度导出
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/25 3:15 下午
 * @description
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
public class ExportItemDto extends ExportSheet {

    @ExcelIgnore
    private Long itemId;

    @ExcelIgnore
    private Long createdUid;

    @ExcelProperty("商品编码")
    private String code;

    @ExcelProperty("商品名称")
    private String name;

    @ExcelProperty("商品品类")
    private String category;

    @ExcelProperty("商品品牌")
    private String brand;

    @ExcelProperty("sku数量")
    private Integer skuCount;

    @ExcelProperty("采购成本")
    private String procurement;

    @ExcelProperty("其他采购成本")
    private String otherProcurement;

    @ExcelProperty("日常销售价")
    private String salesPrice;

    @ExcelProperty("划线价")
    private String linePrice;

    @ExcelProperty("活动价格")
    private String activityPrice;

    @ExcelProperty("渠道最低价")
    private String lowestPrice;

    @ExcelProperty("其他销售价格")
    private String otherPrice;

    @ExcelProperty("供应商")
    private String provider;

    @ExcelProperty("采购员")
    private String buyer;

    @ExcelProperty("发货渠道")
    private String delivery;

    @ExcelProperty("预计上架时间")
    private String estimateSaleTime;

    @ExcelProperty("主要运营负责人")
    private String mainRunner;

    @ExcelProperty("其他运营负责人")
    private String otherRunner;

    @ExcelProperty("商品款号")
    String partnerProviderItemSn;

    @ExcelIgnore
    private Integer status;

    @ExcelProperty("商品状态")
    private String statusStr;

    @ExcelProperty("合作方/业务类型")
    private String corpBizType;

    @ExcelProperty("采购税率")
    private String purchaseRate;

    @ExcelProperty("销售税率")
    private String rate;

    @ExcelProperty("商品下架时间")
    private String downFrameTimeStr;

    @ExcelIgnore private Long downFrameTime;

    @ExcelProperty("商品下架原因")
    private String downFrameReason;



}
