package com.daddylab.supplier.item.application.itemOptimize;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.message.QyMsgSendUtils;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.application.process.ProcessBizService;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsPrincipalsInfo;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MsgTemplateCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemOptimizeAdviceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemOptimizePlanService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemOptimizeService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IProcessInstRefService;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizePersistData;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizeStatus;
import com.daddylab.supplier.item.types.process.ProcessBusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.common.engine.api.delegate.event.FlowableEngineEntityEvent;
import org.flowable.engine.delegate.event.AbstractFlowableEngineEventListener;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2023/10/23
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ItemOptimizeTaskEventListener extends AbstractFlowableEngineEventListener {
    private final IItemOptimizeService itemOptimizeService;
    private final IProcessInstRefService processInstRefService;
    private final OperateLogDomainService operateLogDomainService;
    private final MsgSender msgSender;
    private final IItemOptimizeAdviceService itemOptimizeAdviceService;
    private final IItemOptimizePlanService itemOptimizePlanService;

    @Autowired private RefreshConfig refreshConfig;
    @Autowired private NewGoodsBizService newGoodsBizService;
    @Autowired private ProcessBizService processBizService;

    @Override
    protected void processCreated(FlowableEngineEntityEvent event) {
        final ExecutionEntity entity = (ExecutionEntity) event.getEntity();
        final String processInstanceId = entity.getProcessInstanceId();
        ProcessBusinessType.ITEM_OPTIMIZE
                .getBusinessId(entity.getProcessInstanceBusinessKey())
                .ifPresent(
                        businessId -> {
                            log.info(
                                    "ItemOptimizeTaskEventListener processCreated processInstId:{}", processInstanceId);
                            final ItemOptimize updateModel = new ItemOptimize();
                            updateModel.setId(businessId);
                            updateModel.setProcessInstId(processInstanceId);
                            updateModel.setStatus(ItemOptimizeStatus.SUBMITTED.getValue());
                            itemOptimizeService.updateById(updateModel);

                            final ProcessInstRef processInstRef = new ProcessInstRef();
                            processInstRef.setProcessInstId(processInstanceId);
                            processInstRef.setType(ProcessBusinessType.ITEM_OPTIMIZE.getValue());
                            processInstRef.setBusinessId(businessId);

                            processInstRefService.save(processInstRef);
                        });
    }

    @Override
    protected void processCompleted(FlowableEngineEntityEvent event) {
        final String processInstanceId = event.getProcessInstanceId();
        getItemOptimize(processInstanceId)
                .ifPresent(
                        itemOptimize -> {
                            log.info(
                                    "ItemOptimizeTaskEventListener processCompleted processInstId:{}",
                                    processInstanceId);
                            setStatus(ItemOptimizeStatus.COMPLETED, itemOptimize.getId());
                        });
    }

    @Override
    protected void taskCreated(FlowableEngineEntityEvent event) {
        final String processInstanceId = event.getProcessInstanceId();
        final Task task = (Task) event.getEntity();
        final String activityName = task.getName();

        final Optional<ItemOptimize> itemOptimizeOptional = getItemOptimize(processInstanceId);
        itemOptimizeOptional.ifPresent(
                itemOptimize -> {
                    log.info(
                            "ItemOptimizeTaskEventListener taskCreated processInstId:{} activity:{} {}",
                            processInstanceId,
                            activityName,
                            Arrays.toString(new Object[] {task.getId(), task.getAssignee(), task.getOwner()}));

                    switch (activityName) {
                        case "QC审核":
                            setStatus(ItemOptimizeStatus.PENDING_QC_REVIEW, itemOptimize.getId());
                            qcAutoClaim(task, itemOptimize);
                            break;
                        case "法务审核":
                            setStatus(ItemOptimizeStatus.PENDING_LEGAL_REVIEW, itemOptimize.getId());
                            legalAutoClaim(task, itemOptimize);
                            break;
                        case "链接产品修改":
                            setStatus(ItemOptimizeStatus.TO_BE_MODIFIED, itemOptimize.getId());
                            break;
                        case "已完成":
                            setStatus(ItemOptimizeStatus.COMPLETED, itemOptimize.getId());
                            break;
                    }
                });
    }

    private void qcAutoClaim(Task task, ItemOptimize itemOptimize) {
        try {
            final Optional<StaffBrief> assignedUser = Optional.ofNullable(itemOptimize.getData())
                                                              .map(ItemOptimizePersistData::getQcUsers)
                                                              .filter(users -> !users.isEmpty())
                                                              .map(users -> users.get(0));
            final Supplier<Optional<StaffBrief>> assignedUserFromNewGoodsGetter = () -> Optional
                    .ofNullable(newGoodsBizService.getNewGoodsPrincipalsInfo(itemOptimize.getItemId()))
                    .map(SingleResponse::getData)
                    .map(NewGoodsPrincipalsInfo::getQcUsers)
                    .filter(users -> !users.isEmpty())
                    .map(users -> users.get(0));
            Stream.of(() -> assignedUser, assignedUserFromNewGoodsGetter).map(Supplier::get).filter(Optional::isPresent)
                  .map(Optional::get).findFirst().ifPresent(user -> {
                      final Long userId = user.getUserId();
                      processBizService.claim(userId, task.getId());
                      log.info("[商品优化][QC自动认领]自动分配成功 userId={} taskId={}",
                              userId,
                              task.getId());
                  });
        } catch (Exception e) {
            log.error("[商品优化][QC自动认领]处理异常 taskId={}", task.getId(), e);
        }
    }

    private void legalAutoClaim(Task task, ItemOptimize itemOptimize) {
        try {
            final Optional<StaffBrief> assignedUser = Optional.ofNullable(itemOptimize.getData())
                                                              .map(ItemOptimizePersistData::getLegalUsers)
                                                              .filter(users -> !users.isEmpty())
                                                              .map(users -> users.get(0));
            final Supplier<Optional<StaffBrief>> assignedUserFromNewGoodsGetter = () -> Optional
                    .ofNullable(newGoodsBizService.getNewGoodsPrincipalsInfo(itemOptimize.getItemId()))
                    .map(SingleResponse::getData)
                    .map(NewGoodsPrincipalsInfo::getLegalUsers)
                    .filter(users -> !users.isEmpty())
                    .map(users -> users.get(0));
            Stream.of(() -> assignedUser, assignedUserFromNewGoodsGetter).map(Supplier::get).filter(Optional::isPresent)
                  .map(Optional::get).findFirst().ifPresent(user -> {
                      final Long userId = user.getUserId();
                      processBizService.claim(userId, task.getId());
                      log.info("[商品优化][法务自动认领]自动分配成功 userId={} taskId={}",
                              userId,
                              task.getId());
                  });
        } catch (Exception e) {
            log.error("[商品优化][法务自动认领]处理异常 taskId={}", task.getId(), e);
        }
    }

    @Override
    protected void taskCompleted(FlowableEngineEntityEvent event) {
        final String processInstanceId = event.getProcessInstanceId();
        final Task task = (Task) event.getEntity();
        final String activityName = task.getName();
        final Optional<ItemOptimize> itemOptimizeOptional = getItemOptimize(processInstanceId);
        itemOptimizeOptional.ifPresent(
                itemOptimize -> {
                    log.info(
                            "ItemOptimizeTaskEventListener taskCompleted processInstId:{} activity:{} {}",
                            processInstanceId,
                            activityName,
                            Arrays.toString(new Object[] {task.getId(), task.getAssignee(), task.getOwner()}));

                    final Long assignee = Long.parseLong(task.getAssignee());
                    final StaffBrief assigneeUser = StaffAssembler.INST.toStaffBrief(assignee);

                    switch (activityName) {
                        case "QC审核":
                            notifyLegal(itemOptimize, assigneeUser, task);
                            break;
                        case "法务审核":
                            notifyModify(itemOptimize, assigneeUser, task);
                    }
                });
    }


    private void notifyModify(ItemOptimize itemOptimize, StaffBrief assigneeUser, Task task) {
        final WechatMsg wechatMsg = new WechatMsg();
        wechatMsg.setTitle(
                String.format(
                        "商品优化计划中 %s 法务 %s 已审核，请查看后处理！",
                        itemOptimize.getLinkTitle(), assigneeUser.getNickname()));
        final List<ItemOptimizeAdvice> advices =
                itemOptimizeAdviceService
                        .lambdaQuery()
                        .eq(ItemOptimizeAdvice::getItemOptimizeId, itemOptimize.getId())
                        .eq(ItemOptimizeAdvice::getNodeId, task.getId())
                        .list();
        wechatMsg.setContent(
                advices.stream()
                        .map(ItemOptimizeAdvice::getAdviceContent)
                        .collect(Collectors.joining("\n")));
        final String url =
                String.format(
                        refreshConfig.getDomain()
                                + "/operation-management/optimize-item/list?id=%s",
                        itemOptimize.getId());
        wechatMsg.setLink(url);
        wechatMsg.setRecipient(
                itemOptimize.getData().getModifyUsers().stream()
                        .map(StaffBrief::getQwUserId)
                        .collect(Collectors.joining(",")));
        wechatMsg.setType(1);

//        msgSender.send(wechatMsg);
        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("id", itemOptimize.getId());
        variables.put("法务负责人花名", assigneeUser.getNickname());
        variables.put("链接产品标题", itemOptimize.getLinkTitle());
        variables.put("修改负责人", itemOptimize.getData().getModifyUsers().stream()
                                                .map(StaffBrief::getQwUserId)
                                                .collect(Collectors.joining(",")));
        QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.ITEM_OPTIMIZE_TO_UPDATE, variables);
    }

    private void notifyLegal(ItemOptimize itemOptimize, StaffBrief assigneeUser, Task task) {
        final WechatMsg wechatMsg = new WechatMsg();
        wechatMsg.setTitle(
                String.format(
                        "商品优化计划中 %s QC %s 已审核，请查看后处理！",
                        itemOptimize.getLinkTitle(), assigneeUser.getNickname()));
        final List<ItemOptimizeAdvice> advices =
                itemOptimizeAdviceService
                        .lambdaQuery()
                        .eq(ItemOptimizeAdvice::getItemOptimizeId, itemOptimize.getId())
                        .eq(ItemOptimizeAdvice::getNodeId, task.getId())
                        .list();
        wechatMsg.setContent(
                advices.stream()
                        .map(ItemOptimizeAdvice::getAdviceContent)
                        .collect(Collectors.joining("\n")));
        final String url =
                String.format(
                        refreshConfig.getDomain()
                                + "/operation-management/optimize-item/list?id=%s",
                        itemOptimize.getId());
        wechatMsg.setLink(url);
        wechatMsg.setRecipient(
                itemOptimize.getData().getLegalUsers().stream()
                        .map(StaffBrief::getQwUserId)
                        .collect(Collectors.joining(",")));
        wechatMsg.setType(1);

//        msgSender.send(wechatMsg);

        final HashMap<String, Object> variables = new HashMap<>();
        variables.put("id", itemOptimize.getId());
        variables.put("QC负责人花名", assigneeUser.getNickname());
        variables.put("链接产品标题", itemOptimize.getLinkTitle());
        variables.put("法务负责人", itemOptimize.getData().getLegalUsers().stream()
                                                .map(StaffBrief::getQwUserId)
                                                .collect(Collectors.joining(",")));
        QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.ITEM_OPTIMIZE_TO_LEGAL, variables);
    }

    private void notifyQc(ItemOptimize itemOptimize, ItemOptimizePlan plan) {
        final StaffBrief submitUser = StaffAssembler.INST.toStaffBrief(itemOptimize.getSubmitUid());

        final WechatMsg wechatMsg = new WechatMsg();
        wechatMsg.setTitle(
                String.format("%s 已提交 %s 商品优化", submitUser.getNickname(), plan.getPlanName()));

        wechatMsg.setContent("请查看后处理");
        final String url =
                String.format(
                        refreshConfig.getDomain()
                                + "/operation-management/optimize-item/list?id=%s",
                        itemOptimize.getId());
        wechatMsg.setLink(url);
        wechatMsg.setRecipient(
                itemOptimize.getData().getQcUsers().stream()
                        .map(StaffBrief::getQwUserId)
                        .collect(Collectors.joining(",")));
        wechatMsg.setType(1);

        msgSender.send(wechatMsg);
    }

    private Optional<ItemOptimize> getItemOptimize(String processInstanceId) {
        return itemOptimizeService
                .lambdaQuery()
                .eq(ItemOptimize::getProcessInstId, processInstanceId)
                .oneOpt();
    }

    private void setStatus(ItemOptimizeStatus status, Long id) {
        itemOptimizeService
                .lambdaUpdate()
                .set(ItemOptimize::getStatus, status.getValue())
                .eq(ItemOptimize::getId, id)
                .update();
        operateLogDomainService.addOperatorLog(
                UserContext.getUserId(),
                OperateLogTarget.ITEM_OPTIMIZE,
                id,
                String.format("流程状态更新至【%s】", status.getDesc()));
    }
}
