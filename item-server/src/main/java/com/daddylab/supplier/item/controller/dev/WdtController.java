package com.daddylab.supplier.item.controller.dev;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.item.ItemSyncWdtBizService;
import com.daddylab.supplier.item.application.refundOrder.WdtRefundOrderDataSyncService;
import com.daddylab.supplier.item.common.domain.dto.GenericIdsBody;
import com.daddylab.supplier.item.common.domain.dto.IdsCmd;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2021/12/31
 */

@Api(hidden = true)
@Slf4j
@RestController
@RequestMapping("/dev/wdt")
public class WdtController {

    @PostMapping(value = "/pushItem")
    public SingleResponse<Void> pushItem(Long itemId) {
        final ItemSyncWdtBizService bean = ApplicationContextUtil
                .getBean(ItemSyncWdtBizService.class);
        bean.syncItemToWdt(itemId);
        return SingleResponse.of(null);
    }

    @PostMapping(value = "/pushItemByIds")
    public SingleResponse<Void> pushItemByIds(@RequestBody IdsCmd params) {
        final ItemSyncWdtBizService bean = ApplicationContextUtil
                .getBean(ItemSyncWdtBizService.class);
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> bean.syncItemToWdtByIds(params.getIds()));
        return SingleResponse.of(null);
    }

    @PostMapping(value = "/pushItemByCodes")
    public SingleResponse<Void> pushItemByCodes(@RequestBody GenericIdsBody<String> params) {
        final ItemSyncWdtBizService bean = ApplicationContextUtil
                .getBean(ItemSyncWdtBizService.class);
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> bean.syncItemToWdtByCodes(params.getIds()));
        return SingleResponse.of(null);
    }

    @PostMapping(value = "/refundOrder/saveOrUpdateBatchSpecifiedByRefundNo")
    public Response refundOrderSaveOrUpdateBatchSpecifiedByRefundNo(
            @RequestBody GenericIdsBody<String> params) {
        if (CollectionUtil.isEmpty(params.getIds())) {
            return Response.buildFailure(ErrorCode.VERIFY_PARAM.getCode(), "传入的退还单号为空");
        }
        final WdtRefundOrderDataSyncService bean = ApplicationContextUtil
                .getBean(WdtRefundOrderDataSyncService.class);
        for (String id : params.getIds()) {
            try {
                bean.saveOrUpdateBatchSpecifiedByRefundNo(id);
            } catch (Exception e) {
                log.error("查询旺店通接口更新本地退换单数据异常，退还单号：{}，异常信息：{}", id, e.getMessage(), e);
            }
        }
        return Response.buildSuccess();
    }
}
