package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/9/19 10:50
 * @Description: 商品匹配小程序 feign 请求参数
 */
@Data
public class ItemSyncMiniProgramMatchParamV2 implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long shopId;
    private List<ItemSyncMiniProgramMatchParam> syncMatchForms;
}
