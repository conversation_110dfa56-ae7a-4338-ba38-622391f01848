package com.daddylab.supplier.item.application.dataFetch;

import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.domain.dataFetch.DataFetchManager;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2022/4/29
 */
@Component
@Slf4j
public class DataFetchManagerTask {

    @Autowired
    private DataFetchManager dataFetchManager;

    @XxlJob("DataFetchManager.pause")
    public void pause() {
        dataFetchManager.pause();
    }

    @XxlJob("DataFetchManager.resume")
    public void resume() {
        dataFetchManager.resume();
    }

    @XxlJob("DataFetchManager.reFetch")
    public void reFetch() {
        String jobParam = XxlJobHelper.getJobParam();
        LocalDateTime firstDayOfMonth = DateUtil.getFirstDayOfMonth();
        LocalDateTime lastDayOfMonth = DateUtil.getLastDayOfMonth();
        final FetchDataType fetchDataType = IEnum.getEnumByValue(FetchDataType.class, Integer.valueOf(jobParam));
        dataFetchManager.reFetch(fetchDataType, firstDayOfMonth, lastDayOfMonth);
    }
}
