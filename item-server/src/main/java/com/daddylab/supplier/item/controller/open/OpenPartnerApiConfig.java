package com.daddylab.supplier.item.controller.open;

import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.util.Assert;
import org.springframework.web.cors.CorsUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR> up
 * @date 2022年10月19日 5:33 PM
 */
@Slf4j
@Configuration
public class OpenPartnerApiConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new HandlerInterceptor() {
            @Override
            public boolean preHandle(@NonNull HttpServletRequest request,
                                     @NonNull HttpServletResponse response, @NonNull Object handler) {
                if (CorsUtils.isPreFlightRequest(request)) {
                    return true;
                }
                // 构造供P系统应商信息上下文
                String partnerToken = request.getHeader("Authorization");
                log.debug("[OpenPartnerApiConfig] the provider from partner system.token:{}", partnerToken);
                Assert.hasText(partnerToken, "P系统token不得为空。pToken:" + partnerToken);
                partnerToken = StringUtil.trimStart(partnerToken, "Bearer ");
                PartnerProviderResp resp = SpringUtil.getBean(ProviderGateway.class).partnerQuery(partnerToken);
                Assert.notNull(resp.getId(), "P系统供应商id不得为空。pToken:" + partnerToken);
                Assert.hasText(resp.getOrganizationNo(), "P系统供应商社会统一信用编码不得为空。pToken:" + partnerToken);
                Assert.hasText(resp.getOrganizationName(), "P系统供应商名称不得为空。pToken:" + partnerToken);
                PartnerProviderContext.setHolder(resp);
                log.debug("[OpenPartnerApiConfig] the provider from partner system.resp:{}", resp);
                return true;
            }

            @Override
            public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
                HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
                PartnerProviderContext.remove();
            }
        }).order(Ordered.HIGHEST_PRECEDENCE).addPathPatterns("/open/partner/**");
    }


}
