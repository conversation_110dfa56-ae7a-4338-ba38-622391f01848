package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseTable;

/**
 * <p>
 * 采购权限 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
public interface IPurchaseTableService extends IDaddyService<PurchaseTable> {

}
