package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年04月22日 2:25 PM
 */
@Data
public class StockOutOrderProcessForm {


    private Integer isPushWdt;
    private Long loginUserId;
    private String no;
    private String processInstId;
    private String purchaseOrderNo;
    private String purchaseOrg;
    private String remark;
    private Integer returnMode;
    private Integer returnType;
    private String stockOutOrg;
    private String providerName;

    /**
     * 业务线 1-电商 2-绿色家装 3-商家入驻
     */
    private Integer businessLine;

    private List<StockOutOrderProcessDetailFormList> stockOutOrderProcessDetailFormList;
}
