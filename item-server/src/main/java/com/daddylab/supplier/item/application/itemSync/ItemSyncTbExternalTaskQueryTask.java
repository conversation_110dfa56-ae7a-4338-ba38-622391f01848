package com.daddylab.supplier.item.application.itemSync;

import cn.hutool.core.util.ReUtil;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.domain.winrobot.WinrobotTokenManager;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ThirdPlatformSync;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ThirdPlatformSyncExternalTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ThirdPlatformSyncExternalTaskState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ThirdPlatformSyncExternalTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ThirdPlatformSyncState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IThirdPlatformSyncExternalTaskService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IThirdPlatformSyncService;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.winrobot360.Winrobot360API;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.GenericRsp;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.JobDataListItem;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TaskQueryParam;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TaskQueryRspData;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TaskState;
import com.google.common.collect.ImmutableList;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.skywalking.apm.toolkit.trace.Trace;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/9/21
 */
@Component
@AllArgsConstructor
@Slf4j
public class ItemSyncTbExternalTaskQueryTask {

    private final IThirdPlatformSyncService thirdPlatformSyncService;
    private final IThirdPlatformSyncExternalTaskService thirdPlatformSyncExternalTaskService;
    private final WinrobotTokenManager winrobotTokenManager;
    private final Winrobot360API winrobot360API;


    @XxlJob("ItemSyncTbExternalTaskQueryTask")
    @Trace(operationName = "ItemSyncTbExternalTaskQueryTask")
    public void run() {
        final List<ThirdPlatformSyncExternalTask> thirdPlatformSyncExternalTasks = thirdPlatformSyncExternalTaskService.lambdaQuery()
                .eq(ThirdPlatformSyncExternalTask::getState,
                        ThirdPlatformSyncExternalTaskState.WAITING)
                .eq(ThirdPlatformSyncExternalTask::getType,
                        ThirdPlatformSyncExternalTaskType.WINROBOT)
                .list();
        final int taskSize = thirdPlatformSyncExternalTasks.size();
        log.info("商品同步淘宝/影刀任务查询: 任务开始 待处理数量={}", taskSize);
        final String token = winrobotTokenManager.token();
        for (int i = 0; i < taskSize; i++) {
            final ThirdPlatformSyncExternalTask thirdPlatformSyncExternalTask = thirdPlatformSyncExternalTasks.get(
                    i);
            log.info("商品同步淘宝/影刀任务查询: 正在处理 {}/{} syncId={} taskId={}", i + 1, taskSize,
                    thirdPlatformSyncExternalTask.getSyncId(),
                    thirdPlatformSyncExternalTask.getTaskId());

            final TaskQueryParam param = new TaskQueryParam();
            param.setTaskUuid(thirdPlatformSyncExternalTask.getTaskId());
            final GenericRsp<TaskQueryRspData> taskQuery = winrobot360API.taskQuery(
                    token, param);
            if (!taskQuery.getSuccess()) {
                log.error("商品同步淘宝/影刀任务查询: 查询失败，失败原因：{} syncId={}", taskQuery.getMsg(),
                        thirdPlatformSyncExternalTask.getSyncId());
                break;
            }
            final TaskQueryRspData data = taskQuery.getData();
            final TaskState taskState = TaskState.valueOf(data.getStatus().toUpperCase());
            final Optional<JobDataListItem> taskJob = data.getJobDataList().stream().findFirst();
            switch (taskState) {
                case FINISH:
                    thirdPlatformSyncExternalTask.setState(
                            ThirdPlatformSyncExternalTaskState.FINISH);
                    break;
                case STOPPED:
                    thirdPlatformSyncExternalTask.setState(
                            ThirdPlatformSyncExternalTaskState.ERROR);
                    thirdPlatformSyncExternalTask.setError("任务被手动停止");
                    break;
                case ERROR:
                    thirdPlatformSyncExternalTask.setState(
                            ThirdPlatformSyncExternalTaskState.ERROR);
                    thirdPlatformSyncExternalTask.setError(
                            taskJob.map(JobDataListItem::getRemark).orElse(""));
                    break;
                default:
                    log.info("商品同步淘宝/影刀任务查询: 影刀任务未完成 syncId={} task={}",
                            thirdPlatformSyncExternalTask.getSyncId(), data);
                    continue;
            }
            log.info("商品同步淘宝/影刀任务查询: 影刀任务完成 syncId={} task={}",
                    thirdPlatformSyncExternalTask.getSyncId(), data);

            //外部任务标记完成状态
            final boolean isError = thirdPlatformSyncExternalTask.getState()
                    == ThirdPlatformSyncExternalTaskState.ERROR;
            thirdPlatformSyncExternalTask.setData(JsonUtil.toJson(data));
            thirdPlatformSyncExternalTaskService.updateById(thirdPlatformSyncExternalTask);

            //更新同步任务状态
            String publicError = "同步失败";
            if (isError) {
                final String error = thirdPlatformSyncExternalTask.getError();
                for (Pair<String, String> entry : ImmutableList.<Pair<String, String>>builder()
                        .add(Pair.of("超时", "任务超时"))
                        .add(Pair.of("高级编辑", "详情为高级编辑模式不支持修改详情"))
                        .add(Pair.of("元素|存在|找到", "淘宝后台编辑页面可能修改"))
                        .build()) {
                    if (ReUtil.contains(entry.getLeft(), error)) {
                        publicError = entry.getRight();
                        break;
                    }
                }
            }
            thirdPlatformSyncService.lambdaUpdate()
                    .set(ThirdPlatformSync::getError, publicError)
                    .set(ThirdPlatformSync::getState, isError
                            ? ThirdPlatformSyncState.ERROR : ThirdPlatformSyncState.FINISH)
                    .eq(ThirdPlatformSync::getId, thirdPlatformSyncExternalTask.getSyncId())
                    .update();
        }
        log.info("商品同步淘宝/影刀任务查询: 处理完成");


    }
}
