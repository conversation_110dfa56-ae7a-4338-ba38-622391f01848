package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import com.fasterxml.jackson.databind.ObjectMapper;

import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/27
 */
public class CustomMappingJackson2HttpMessageConverter extends MappingJackson2HttpMessageConverter {
    public CustomMappingJackson2HttpMessageConverter(ObjectMapper om, List<MediaType> mediaTypes) {
        super(om);
        setSupportedMediaTypes(mediaTypes);
    }
}
