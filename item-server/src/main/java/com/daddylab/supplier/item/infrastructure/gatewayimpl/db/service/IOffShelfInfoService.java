package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.application.offShelf.dto.OffShelfPageQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OffShelfInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OffShelfInfoMapper;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <p>
 * 下架管理-下架流程信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface IOffShelfInfoService extends IDaddylabServicePlus<OffShelfInfo, OffShelfInfoMapper> {

    Long getMaxId();

    List<OffShelfInfo> listByProcessInstId(String processInstanceId);

    /**
     * 请注意返回的OffShelfPageVO的itemList字段是null
     * @param query 查询参数
     * @return 分页结果
     */
    PageInfo<OffShelfInfo> page(OffShelfPageQuery query);
}
