package com.daddylab.supplier.item.application.itemOptimize;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.func.LambdaUtil;
import cn.hutool.core.map.MapBuilder;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.daddylab.supplier.item.application.message.QyMsgSendUtils;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.application.process.ProcessBizService;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.ErrorChecker;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.bizLevelDivision.BizDivisionContext;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BizUnionTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MsgTemplateCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemOptimizeParticipantMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl.BizLevelDivisionServiceImpl;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLock;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.daddylab.supplier.item.types.item.PsysType;
import com.daddylab.supplier.item.types.itemOptimize.*;
import com.daddylab.supplier.item.types.process.*;
import com.daddylab.supplier.item.types.process.assembler.TaskAssembler;
import com.google.common.collect.Lists;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.flowable.common.engine.api.FlowableObjectNotFoundException;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.javers.core.diff.Diff;
import org.javers.core.diff.changetype.container.CollectionChange;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/10/20
 */
@Service
@RequiredArgsConstructor
public class ItemOptimizeBizServiceImpl implements ItemOptimizeBizService {
  public static final HashMap<Object, Function<Object, String>> LOG_PRINTERS = new HashMap<>();

  static {
    LOG_PRINTERS.put(
        "psysType",
        v -> IEnum.getEnumOptByValue(PsysType.class, v).map(PsysType::getDesc).orElse("-"));
    final Function<Object, String> timePrinter =
        v -> Optional.ofNullable(v).map(vv -> DateUtil.format((Long) vv)).orElse("-");
    LOG_PRINTERS.put("modifyTime", timePrinter);
    LOG_PRINTERS.put("modifiedTime", timePrinter);
    LOG_PRINTERS.put(
        "modified", v -> Optional.ofNullable(v).map(vv -> (boolean) v ? "是" : "否").orElse("-"));
    LOG_PRINTERS.put(
        "platform",
        v -> IEnum.getEnumOptByValue(Platform.class, v).map(Platform::getDesc).orElse("-"));
  }

  private final IItemOptimizeService itemOptimizeService;
  private final IItemOptimizePlanService itemOptimizePlanService;
  private final IItemOptimizeParticipantService itemOptimizeParticipantService;
  private final StaffService staffService;
  private final IItemOptimizeAdviceService itemOptimizeAdviceService;
  private final IItemService itemService;
  private final OperateLogDomainService operateLogDomainService;

  private final MsgSender msgSender;

  @Autowired private ItemOptimizePlanBizService itemOptimizePlanBizService;
  @Autowired private ProcessBizService processBizService;
  @Autowired private ProcessEngine processEngine;
  @Autowired private RefreshConfig refreshConfig;
  @Autowired private ItemOptimizeConfig itemOptimizeConfig;
  @Autowired private IBizLevelDivisionService bizLevelDivisionService;

  @Override
  public PageResponse<ItemOptimizePlanItem> query(ItemOptimizeQuery query) {
    final IPage<ItemOptimize> page = query.getPage();
    final LambdaQueryChainWrapper<ItemOptimize> queryChainWrapper =
        itemOptimizeService.lambdaQuery().eq(ItemOptimize::getPlanStatus, 1);
    queryChainWrapper.orderByDesc(ItemOptimize::getId);
    final HashSet<ItemOptimizeParticipant> participantsFilter =
        getItemOptimizeParticipantsFilter(query);
    if (!participantsFilter.isEmpty()) {
      final ItemOptimizeParticipantMapper itemOptimizeParticipantMapper =
          (ItemOptimizeParticipantMapper) itemOptimizeParticipantService.getDaddyBaseMapper();
      List<ItemOptimizeParticipant> participantRelations =
          itemOptimizeParticipantMapper.selectBatchByTypeAndUid(participantsFilter);
      if (participantRelations.isEmpty()) {
        return ResponseFactory.emptyPage();
      }
      queryChainWrapper.in(
          ItemOptimize::getId,
          participantRelations.stream()
              .map(ItemOptimizeParticipant::getItemOptimizeId)
              .collect(Collectors.toList()));
    }

    if (!UserContext.hasPermission(GlobalConstant.ITEM_OPTIMIZE_PLAN_ALL)) {
      final Set<Long> planIdsCreatedBySelf = getPlanIdsCreatedBySelf();
      if (planIdsCreatedBySelf.isEmpty()) {
        return ResponseFactory.emptyPage();
      }
      queryChainWrapper.in(ItemOptimize::getPlanId, planIdsCreatedBySelf);
    }

    queryChainWrapper.eq(
        ObjectUtil.isNotEmpty(query.getStatus()), ItemOptimize::getStatus, query.getStatus());
    queryChainWrapper.eq(
        ObjectUtil.isNotEmpty(query.getOuterItemId()),
        ItemOptimize::getOuterItemId,
        query.getOuterItemId());
    queryChainWrapper.like(
        ObjectUtil.isNotEmpty(query.getLinkTitle()),
        ItemOptimize::getLinkTitle,
        query.getLinkTitle());
    queryChainWrapper.eq(
        ObjectUtil.isNotEmpty(query.getPsysType()), ItemOptimize::getPsysType, query.getPsysType());
    queryChainWrapper.eq(
        ObjectUtil.isNotEmpty(query.getPlanId()), ItemOptimize::getPlanId, query.getPlanId());

    //        final List<Integer> corpType =
    // UserPermissionJudge.filterCorpType(query.getCorpType());
    final List<ItemOptimizePlan> relatedPlans =
        itemOptimizePlanService
            .lambdaQuery()
            .select(ItemOptimizePlan::getId)
            //                        .in(ItemOptimizePlan::getBusinessLine, corpType)
            .eq(
                NumberUtil.isPositive(query.getPlanId()),
                ItemOptimizePlan::getId,
                query.getPlanId())
            .eq(
                ObjectUtil.isNotEmpty(query.getPlanNo()),
                ItemOptimizePlan::getPlanNo,
                query.getPlanNo())
            .list();
    if (relatedPlans.isEmpty()) {
      return ResponseFactory.emptyPage();
    }
    final Set<Long> relatedPlanIds =
        relatedPlans.stream().map(ItemOptimizePlan::getId).collect(Collectors.toSet());
    queryChainWrapper.in(ItemOptimize::getPlanId, relatedPlanIds);

    final Set<DivisionLevelValueEnum> selectedScopes =
        DivisionLevelValueEnum.valueOfSet(query.getCorpType(), query.getBizType());
    BizDivisionContext.invoke(
        BizUnionTypeEnum.SPU,
        ctx -> ctx.setBizIdRef("item_optimize.item_id").setSelectedScopes(selectedScopes),
        () -> queryChainWrapper.page(page));
    if (page.getRecords().isEmpty()) {
      return ResponseFactory.emptyPage();
    }
    final Set<Long> planIds =
        page.getRecords().stream().map(ItemOptimize::getPlanId).collect(Collectors.toSet());
    final List<ItemOptimizePlan> itemOptimizePlans = itemOptimizePlanService.listByIds(planIds);
    final Map<Long, ItemOptimizePlan> itemOptimizePlansMap =
        itemOptimizePlans.stream()
            .collect(Collectors.toMap(ItemOptimizePlan::getId, Function.identity()));
    final List<Long> itemIds =
        page.getRecords().stream()
            .map(ItemOptimize::getItemId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
    final Map<Long, List<CorpBizTypeDTO>> corpBizTypeMap =
        bizLevelDivisionService.getCorpBizType(BizUnionTypeEnum.SPU, itemIds);
    return ResponseFactory.ofPage(
        page,
        v -> {
          final ItemOptimizePlanItem itemOptimizePlanItem =
              ItemOptimizeAssembler.INST.itemOptimizeToItemOptimizePlanItem(v);
          itemOptimizePlanItem.setPlan(
              ItemOptimizeAssembler.INST.itemOptimizePlanToItemOptimizePlanBaseInfo(
                  itemOptimizePlansMap.get(v.getPlanId())));
          itemOptimizePlanItem.setCanRollback(checkCanRollback(v, false));
          final List<CorpBizTypeDTO> corpBizType =
              corpBizTypeMap.getOrDefault(v.getItemId(), Collections.emptyList());
          itemOptimizePlanItem.setCorpBizType(corpBizType);
          return itemOptimizePlanItem;
        });
  }

  @NonNull
  private static HashSet<ItemOptimizeParticipant> getItemOptimizeParticipantsFilter(
      ItemOptimizeQuery query) {
    final HashSet<ItemOptimizeParticipant> participantsFilter = new HashSet<>();
    if (NumberUtil.isPositive(query.getBuyerUserId())) {
      final ItemOptimizeParticipant itemOptimizeParticipant = new ItemOptimizeParticipant();
      itemOptimizeParticipant.setType(ParticipantType.BUYER.getValue());
      itemOptimizeParticipant.setUid(query.getBuyerUserId());
      participantsFilter.add(itemOptimizeParticipant);
    }
    if (NumberUtil.isPositive(query.getQcUserId())) {
      final ItemOptimizeParticipant itemOptimizeParticipant = new ItemOptimizeParticipant();
      itemOptimizeParticipant.setType(ParticipantType.QC.getValue());
      itemOptimizeParticipant.setUid(query.getQcUserId());
      participantsFilter.add(itemOptimizeParticipant);
    }
    if (NumberUtil.isPositive(query.getLegalUserId())) {
      final ItemOptimizeParticipant itemOptimizeParticipant = new ItemOptimizeParticipant();
      itemOptimizeParticipant.setType(ParticipantType.LEGAL.getValue());
      itemOptimizeParticipant.setUid(query.getLegalUserId());
      participantsFilter.add(itemOptimizeParticipant);
    }
    if (NumberUtil.isPositive(query.getModifyUserId())) {
      final ItemOptimizeParticipant itemOptimizeParticipant = new ItemOptimizeParticipant();
      itemOptimizeParticipant.setType(ParticipantType.MODIFY.getValue());
      itemOptimizeParticipant.setUid(query.getModifyUserId());
      participantsFilter.add(itemOptimizeParticipant);
    }
    return participantsFilter;
  }

  @NonNull
  private Set<Long> getPlanIdsCreatedBySelf() {
    return itemOptimizePlanService
        .lambdaQuery()
        .eq(ItemOptimizePlan::getCreatedUid, UserContext.getUserId())
        .select(ItemOptimizePlan::getId)
        .list()
        .stream()
        .map(ItemOptimizePlan::getId)
        .collect(Collectors.toSet());
  }

  @Override
  public SingleResponse<ItemOptimizeDrawer> drawer(Long query) {
    final ItemOptimize itemOptimize = itemOptimizeService.getById(query);

    final ItemOptimizeDrawer itemOptimizeDrawer = new ItemOptimizeDrawer();
    itemOptimizeDrawer.setProcessNodes(getProcessNodes(itemOptimize));

    final ItemOptimizePlanItem itemOptimizePlanItem =
        ItemOptimizeAssembler.INST.itemOptimizeToItemOptimizePlanItem(itemOptimize);
    final ItemOptimizePlan plan = itemOptimizePlanService.getById(itemOptimize.getPlanId());
    itemOptimizePlanItem.setPlan(
        ItemOptimizeAssembler.INST.itemOptimizePlanToItemOptimizePlanBaseInfo(plan));
    itemOptimizePlanItem.setCanRollback(checkCanRollback(itemOptimize, false));
    itemOptimizeDrawer.setItem(itemOptimizePlanItem);

    Optional.ofNullable(itemOptimizePlanItem.getItemId())
        .filter(itemId -> itemId != 0)
        .map(itemService::getById)
        .ifPresent(itemOptimizeDrawer::setBackendItem);

    return SingleResponse.of(itemOptimizeDrawer);
  }

  private Response save(
      ItemOptimizeSaveCmd saveCmd, BiConsumer<ItemOptimize, ItemOptimize> preFilter) {
    final Long id = saveCmd.getId();
    final ItemOptimize itemOptimize = itemOptimizeService.getById(id);
    final ItemOptimize originalItemOptimize =
        ItemOptimizeAssembler.INST.copyItemOptimize(itemOptimize);
    preFilter.accept(originalItemOptimize, itemOptimize);
    ItemOptimizeAssembler.INST.updateItemOptimizeByCmd(itemOptimize, saveCmd);
    final ItemOptimizePersistData originalData = originalItemOptimize.getData();
    final ItemOptimizePersistData data = itemOptimize.getData();
    final Diff dataDiff = DiffUtil.diff(originalData, data);
    final boolean outerItemIdModified =
        saveCmd.getOuterItemId() != null
            && !Objects.equals(originalItemOptimize.getOuterItemId(), saveCmd.getOuterItemId());
    final boolean itemCodeModified =
        saveCmd.getItemCode() != null
            && !Objects.equals(originalItemOptimize.getItemCode(), saveCmd.getItemCode());

    if (outerItemIdModified || itemCodeModified || dataDiff.hasChanges()) {
      handleParticipantsChange(id, dataDiff);

      final LambdaUpdateChainWrapper<ItemOptimize> updateChainWrapper =
          itemOptimizeService
              .lambdaUpdate()
              .set(ItemOptimize::getData, data)
              .set(ItemOptimize::getV, saveCmd.getV() + 1)
              .eq(ItemOptimize::getV, saveCmd.getV())
              .eq(ItemOptimize::getId, id);

      if (outerItemIdModified || itemCodeModified) {
        final ItemOptimizePlanItemMatchQuery matchQuery = new ItemOptimizePlanItemMatchQuery();
        matchQuery.setId(saveCmd.getOuterItemId());
        matchQuery.setPlatform(itemOptimize.getPlatform());
        matchQuery.setItemCode(itemOptimize.getItemCode());

        final SingleResponse<ItemOptimizePlanItem> match =
            itemOptimizePlanBizService.match(matchQuery);
        ErrorChecker.checkAndThrowIfError(match);
        final Optional<ItemOptimizePlanItem> matchData = Optional.ofNullable(match.getData());
        updateChainWrapper
            .set(ItemOptimize::getOuterItemId, saveCmd.getOuterItemId())
            .set(
                ItemOptimize::getPlatformItemId,
                matchData.map(ItemOptimizePlanItem::getPlatformItemId).orElse(0L))
            .set(ItemOptimize::getItemId, matchData.map(ItemOptimizePlanItem::getItemId).orElse(0L))
            .set(
                ItemOptimize::getItemCode,
                matchData.map(ItemOptimizePlanItem::getItemCode).orElse(""));
      }

      final boolean update = updateChainWrapper.update();
      if (!update) {
        throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "数据修改冲突，请刷新页面后重试");
      }
      final ArrayList<String> otherProps = new ArrayList<>();
      if (outerItemIdModified) {
        otherProps.add(
            String.format(
                "将“平台商品ID”从“%s”修改为“%s”",
                originalItemOptimize.getOuterItemId(), saveCmd.getOuterItemId()));
      }
      if (itemCodeModified) {
        otherProps.add(
            String.format(
                "将“商品编码（SPU）”从“%s”修改为“%s”",
                originalItemOptimize.getItemCode(), saveCmd.getItemCode()));
      }

      final String diffLog =
          DiffUtil.getVerboseDiffLog(
              dataDiff, ItemOptimizePersistData.class, otherProps, LOG_PRINTERS, "");
      operateLogDomainService.addOperatorLog(
          UserContext.getUserId(),
          OperateLogTarget.ITEM_OPTIMIZE,
          id,
          diffLog,
          DiffUtil.toJson(MapBuilder.create().put("diff", dataDiff).build()));
    }

    // 【待修改】状态保存一次后即不可撤回
    if (Objects.equals(itemOptimize.getStatus(), ItemOptimizeStatus.TO_BE_MODIFIED.getValue())) {
      itemOptimizeService.setDataField(id, ItemOptimizePersistData::getDisableRollback, true);
    }

    if (saveCmd.isSubmit()) {
      if (Objects.equals(itemOptimize.getStatus(), ItemOptimizeStatus.TO_BE_SUBMITTED.getValue())) {
        submit(itemOptimize);
      } else if (Objects.equals(
          itemOptimize.getStatus(), ItemOptimizeStatus.COMPLETED.getValue())) {
        submit(itemOptimize, true);
      } else if (Objects.equals(
          itemOptimize.getStatus(), ItemOptimizeStatus.TO_BE_MODIFIED.getValue())) {
        final TaskService taskService = processEngine.getTaskService();
        final Task task =
            taskService
                .createTaskQuery()
                .processInstanceId(itemOptimize.getProcessInstId())
                .taskName("链接产品修改")
                .singleResult();
        if (task != null) {
          taskService.setAssignee(task.getId(), String.valueOf(UserContext.getUserId()));
          taskService.complete(task.getId());
        }
      }
    }
    return Response.buildSuccess();
  }

  @Override
  @DistributedLock(searchKey = DistributedLock.SearchKeyStrategy.PARAMETER_PROPERTY, msg = "请勿重复提交")
  @Transactional()
  public Response save(ItemOptimizeSaveCmd saveCmd) {
    return save(saveCmd, (l, r) -> {});
  }

  @Override
  @DistributedLock(searchKey = DistributedLock.SearchKeyStrategy.PARAMETER_PROPERTY, msg = "请勿重复提交")
  @Transactional()
  public Response modifyResponsiblePersons(ModifyResponsiblePersonCmd modifyCmd) {
    final ItemOptimizeSaveCmd itemOptimizeSaveCmd = new ItemOptimizeSaveCmd();
    itemOptimizeSaveCmd.setId(modifyCmd.getId());
    itemOptimizeSaveCmd.setV(modifyCmd.getV());

    final ItemOptimizeSaveCmdData data = new ItemOptimizeSaveCmdData();
    itemOptimizeSaveCmd.setData(data);
    final Long userId = UserContext.getUserId();

    return save(
        itemOptimizeSaveCmd,
        (l, r) -> {
          final ItemOptimizeStatus itemOptimizeStatus =
              IEnum.getEnumByValue(ItemOptimizeStatus.class, r.getStatus());
          switch (itemOptimizeStatus) {
            case PENDING_QC_REVIEW:
              if (l.getData().getQcUsers().stream()
                  .noneMatch(user -> Objects.equals(user.getUserId(), userId))) {
                throw ExceptionPlusFactory.bizException(
                    ErrorCode.OPERATION_REJECT, "仅当前QC负责人可以修改负责人");
              }
              data.setQcUserIds(modifyCmd.getUserIds());
              break;
            case PENDING_LEGAL_REVIEW:
              if (l.getData().getLegalUsers().stream()
                  .noneMatch(user -> Objects.equals(user.getUserId(), userId))) {
                throw ExceptionPlusFactory.bizException(
                    ErrorCode.OPERATION_REJECT, "仅当前法务负责人可以修改负责人");
              }
              data.setLegalUserIds(modifyCmd.getUserIds());
              break;
            default:
              throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前状态不允许修改负责人");
          }
        });
  }

  private void submit(ItemOptimize itemOptimize) {
    submit(itemOptimize, false);
  }

  private void submit(ItemOptimize itemOptimize, boolean isReview) {
    itemOptimizeService.updateSubmitMeta(
        itemOptimize.getId(), UserContext.getUserId(), DateUtil.currentTime(), isReview);

    operateLogDomainService.addOperatorLog(
        UserContext.getUserId(),
        OperateLogTarget.ITEM_OPTIMIZE,
        itemOptimize.getId(),
        isReview ? "发起复审" : "提交审核");

    startProcess(itemOptimize);

    if (isReview) {
      noticeReview(UserContext.getUserId(), itemOptimize);
    }
  }

  private void noticeReview(Long operatorId, ItemOptimize itemOptimize) {
    final ItemOptimizePlan plan = itemOptimizePlanService.getById(itemOptimize.getPlanId());
    final StaffBrief submitUser = StaffAssembler.INST.toStaffBrief(plan.getSubmitUid());
    final HashMap<String, Object> variables = new HashMap<>();
    variables.put("提交人花名", submitUser.getNickname());
    variables.put("链接产品标题", itemOptimize.getLinkTitle());
    variables.put("计划名称", plan.getPlanName());
    variables.put("状态", ItemOptimizeStatus.PENDING_QC_REVIEW.getDesc());
    variables.put("id", itemOptimize.getId());
    variables.put("status", ItemOptimizeStatus.PENDING_QC_REVIEW.getValue());
    QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.ITEM_OPTIMIZE_REVIEW, variables);
  }

  private void handleParticipantsChange(Long id, Diff diff) {
    final Map<String, ParticipantType> participantFields = new HashMap<>();
    participantFields.put(
        LambdaUtil.getFieldName(ItemOptimizePersistData::getBuyerUsers), ParticipantType.BUYER);
    participantFields.put(
        LambdaUtil.getFieldName(ItemOptimizePersistData::getQcUsers), ParticipantType.QC);
    participantFields.put(
        LambdaUtil.getFieldName(ItemOptimizePersistData::getLegalUsers), ParticipantType.LEGAL);
    participantFields.put(
        LambdaUtil.getFieldName(ItemOptimizePersistData::getModifyTime), ParticipantType.MODIFY);

    for (CollectionChange<?> collectionChange : diff.getChangesByType(CollectionChange.class)) {
      final String propertyName = collectionChange.getPropertyName();
      final ParticipantType participantType = participantFields.get(propertyName);
      if (participantType != null) {
        @SuppressWarnings("unchecked")
        final List<StaffBrief> addedValues = (List<StaffBrief>) collectionChange.getAddedValues();
        if (!addedValues.isEmpty()) {
          itemOptimizeParticipantService.addParticipants(
              id,
              addedValues.stream().map(StaffBrief::getUserId).collect(Collectors.toList()),
              participantType);
        }

        @SuppressWarnings("unchecked")
        final List<StaffBrief> removedValues =
            (List<StaffBrief>) collectionChange.getRemovedValues();
        if (!removedValues.isEmpty()) {
          itemOptimizeParticipantService.removeParticipants(
              id,
              removedValues.stream().map(StaffBrief::getUserId).collect(Collectors.toList()),
              participantType);
        }
      }
    }
  }

  @Override
  public MultiResponse<ItemOptimize> addBatch(
      List<ItemOptimizePlanSaveCmdItem> items, int planStatus) {
    final List<ItemOptimize> itemOptimizes =
        ItemOptimizeAssembler.INST.itemOptimizePlanSaveCmdItemListToItemOptimizeList(items);
    itemOptimizes.forEach(itemOptimize -> itemOptimize.setPlanStatus(planStatus));
    for (ItemOptimize itemOptimize : itemOptimizes) {
      final ItemOptimizePlanItemMatchQuery matchQuery = new ItemOptimizePlanItemMatchQuery();
      matchQuery.setId(itemOptimize.getOuterItemId());
      matchQuery.setPlatform(itemOptimize.getPlatform());
      matchQuery.setItemCode(itemOptimize.getItemCode());

      final SingleResponse<ItemOptimizePlanItem> matchResult =
          itemOptimizePlanBizService.match(matchQuery);
      ErrorChecker.checkAndThrowIfError(matchResult);

      final Optional<ItemOptimizePlanItem> matchData = Optional.ofNullable(matchResult.getData());
      itemOptimize.setPlatformItemId(
          matchData.map(ItemOptimizePlanItem::getPlatformItemId).orElse(0L));
      itemOptimize.setItemId(matchData.map(ItemOptimizePlanItem::getItemId).orElse(0L));

      itemOptimizeService.save(itemOptimize);
      itemOptimizeParticipantService.addParticipants(
          itemOptimize.getId(),
          itemOptimize.getData().getBuyerUsers().stream()
              .map(StaffBrief::getUserId)
              .collect(Collectors.toList()),
          ParticipantType.BUYER);
      itemOptimizeParticipantService.addParticipants(
          itemOptimize.getId(),
          itemOptimize.getData().getQcUsers().stream()
              .map(StaffBrief::getUserId)
              .collect(Collectors.toList()),
          ParticipantType.QC);
      itemOptimizeParticipantService.addParticipants(
          itemOptimize.getId(),
          itemOptimize.getData().getLegalUsers().stream()
              .map(StaffBrief::getUserId)
              .collect(Collectors.toList()),
          ParticipantType.LEGAL);
      itemOptimizeParticipantService.addParticipants(
          itemOptimize.getId(),
          itemOptimize.getData().getModifyUsers().stream()
              .map(StaffBrief::getUserId)
              .collect(Collectors.toList()),
          ParticipantType.MODIFY);
      operateLogDomainService.addOperatorLog(
          UserContext.getUserId(),
          OperateLogTarget.ITEM_OPTIMIZE,
          itemOptimize.getId(),
          "新增商品优化",
          itemOptimize);
      if (planStatus == 1) {
        submit(itemOptimize);
      }
    }
    return MultiResponse.of(itemOptimizes);
  }

  private void startProcess(ItemOptimize itemOptimize) {
    final RuntimeService runtimeService = processEngine.getRuntimeService();
    if (StringUtil.isNotBlank(itemOptimize.getProcessInstId())) {
      try {
        runtimeService.deleteProcessInstance(itemOptimize.getProcessInstId(), "发起复审");
      } catch (FlowableObjectNotFoundException ignored) {
      }
    }
    runtimeService
        .createProcessInstanceBuilder()
        .processDefinitionKey(itemOptimizeConfig.getProcessDefKey())
        .businessKey(ProcessBusinessType.ITEM_OPTIMIZE.getBusinessKey(itemOptimize.getId()))
        .name("商品优化流程:" + itemOptimize.getId())
        .assignee(itemOptimize.getCreatedUid().toString())
        .variables(
            MapBuilder.<String, Object>create()
                .put("qc_candidates", processParticipants(itemOptimize.getData().getQcUsers()))
                .put(
                    "legal_candidates", processParticipants(itemOptimize.getData().getLegalUsers()))
                .put(
                    "modifier_candidates",
                    processParticipants(itemOptimize.getData().getLegalUsers()))
                .build())
        .start();
  }

  public void setStatus(Long id, ItemOptimizeStatus currentStatus, ItemOptimizeStatus updateTo) {
    itemOptimizeService
        .lambdaUpdate()
        .set(ItemOptimize::getStatus, currentStatus.getValue())
        .eq(ItemOptimize::getStatus, updateTo.getValue())
        .eq(ItemOptimize::getId, id)
        .update();
  }

  private String processParticipants(List<StaffBrief> users) {
    return users.stream()
        .map(StaffBrief::getUserId)
        .map(Object::toString)
        .collect(Collectors.joining(","));
  }

  private List<ProcessNode> getProcessNodes(ItemOptimize itemOptimize) {
    final ArrayList<ProcessNode> processNodes = Lists.newArrayList();
    final ProcessNode initNode = new ProcessNode();
    initNode.setTitle(itemOptimize.getStatus() == 0 ? "待提交" : "已提交");
    initNode.setProcessors(
        Lists.newArrayList(
            staffService.getStaffBrief(itemOptimize.getCreatedUid(), true).orElse(null)));
    initNode.setOk(itemOptimize.getStatus() > 0);
    initNode.setCompleteTime(
        NumberUtil.isPositive(itemOptimize.getLastSubmitAt())
            ? itemOptimize.getLastSubmitAt()
            : itemOptimize.getSubmitAt());
    initNode.setTasks(Collections.emptyList());
    initNode.setHisTasks(Collections.emptyList());
    initNode.setIsCurrentNode(itemOptimize.getStatus() == 0);
    processNodes.add(initNode);

    final List<ItemOptimizeAdvice> advices =
        itemOptimizeAdviceService
            .lambdaQuery()
            .eq(ItemOptimizeAdvice::getProcessInstId, itemOptimize.getProcessInstId())
            .list();

    final List<HistoricTaskInstance> historicTaskInstances =
        getProcessHisTasks(itemOptimize.getProcessInstId());

    final List<StaffBrief> qcUsers = itemOptimize.getData().getQcUsers();
    final List<ProcessTask> tasksForQc =
        getProcessTasks(historicTaskInstances, "QC审核", qcUsers, advices);
    final ProcessNode qcNode = new ProcessNode("待QC审核", tasksForQc);
    qcNode.setIsCurrentNode(itemOptimize.getStatus() == 2);
    processNodes.add(qcNode);

    final List<StaffBrief> legalUsers = itemOptimize.getData().getLegalUsers();
    final List<ProcessTask> tasksForLegal =
        getProcessTasks(historicTaskInstances, "法务审核", legalUsers, advices);
    final ProcessNode legalNode = new ProcessNode("待法务审核", tasksForLegal);
    legalNode.setIsCurrentNode(itemOptimize.getStatus() == 3);
    processNodes.add(legalNode);

    final List<StaffBrief> modifyUsers = itemOptimize.getData().getModifyUsers();
    final List<ProcessTask> tasksForModify =
        getProcessTasks(historicTaskInstances, "链接产品修改", modifyUsers, advices);
    final ProcessNode modifyNode = new ProcessNode("待修改");
    modifyNode.setProcessors(modifyUsers);
    modifyNode.setIsCurrentNode(itemOptimize.getStatus() == 4);
    modifyNode.setCompleteTime(
        tasksForModify.stream()
            .map(ProcessTask::getCompleteTime)
            .max(Comparator.comparing(Function.identity()))
            .orElse(0L));
    processNodes.add(modifyNode);

    final ProcessNode endNode = new ProcessNode("审核完成", Collections.emptyList());
    endNode.setIsCurrentNode(itemOptimize.getStatus() == 5);
    endNode.setCompleteTime(modifyNode.getCompleteTime());
    processNodes.add(endNode);

    return processNodes;
  }

  @NonNull
  private static List<ProcessTask> getProcessTasks(
      List<HistoricTaskInstance> historicTaskInstances,
      String name,
      List<StaffBrief> candidates,
      List<ItemOptimizeAdvice> advices) {
    return historicTaskInstances.stream()
        .filter(task -> task.getName().equals(name))
        .map(TaskAssembler.INST::historicTaskToProcessTask)
        .peek(
            task -> {
              task.setCandidates(candidates);
              if (task.isCompleted()) {
                task.setComments(getComments(advices, task));
              }
            })
        .collect(Collectors.toList());
  }

  @NonNull
  private static List<Comment> getComments(List<ItemOptimizeAdvice> advices, ProcessTask task) {
    return advices.stream()
        .filter(advice -> advice.getNodeId().equals(task.getId()))
        .map(
            advice -> {
              final Comment comment = new Comment();
              comment.setContent(advice.getAdviceContent());
              comment.setTime(advice.getCreatedAt());

              return comment;
            })
        .collect(Collectors.toList());
  }

  private List<HistoricTaskInstance> getProcessHisTasks(String processInstId) {
    return Optional.ofNullable(processInstId)
        .filter(StringUtil::isNotEmpty)
        .map(
            id ->
                processEngine
                    .getHistoryService()
                    .createHistoricTaskInstanceQuery()
                    .processInstanceId(processInstId)
                    .taskWithoutDeleteReason()
                    .list())
        .orElseGet(Collections::emptyList);
  }

  private List<Task> getProcessTaskList(String processInstId) {
    return Optional.ofNullable(processInstId)
        .filter(StringUtil::isNotEmpty)
        .map(
            id ->
                processEngine
                    .getTaskService()
                    .createTaskQuery()
                    .processInstanceId(processInstId)
                    .list())
        .orElseGet(Collections::emptyList);
  }

  @Override
  public Response delete(Long id) {
    final ItemOptimize itemOptimize = itemOptimizeService.getById(id);
    if (itemOptimize != null
        && Objects.equals(
            itemOptimize.getStatus(), ItemOptimizeStatus.TO_BE_SUBMITTED.getValue())) {
      itemOptimizeService.removeByIdWithTime(id);
      itemOptimizePlanBizService.recount(itemOptimize.getPlanId());
      operateLogDomainService.addOperatorLog(
          UserContext.getUserId(), OperateLogTarget.ITEM_OPTIMIZE, itemOptimize.getId(), "删除商品优化");
    }
    return Response.buildSuccess();
  }

  @Override
  public Response urge(Long reminderUid, TaskUrgeCmd cmd) {
    final String reminderNickname =
        staffService.getStaffBrief(reminderUid).map(StaffBrief::getNickname).orElse("");
    final ItemOptimize itemOptimize = itemOptimizeService.getById(cmd.getBusinessId());
    final ItemOptimizePersistData data = itemOptimize.getData();
    final Optional<ItemOptimizeStatus> itemOptimizeStatus =
        IEnum.getEnumOptByValue(ItemOptimizeStatus.class, itemOptimize.getStatus());
    final String itemOptimizeStatusDesc =
        itemOptimizeStatus
            .map(ItemOptimizeStatus::getDesc)
            .orElseThrow(() -> ExceptionPlusFactory.bizException(ErrorCode.STATE_ERR));

    final StaffBrief staffBrief =
        staffService
            .getStaffBrief(cmd.getProcessorUid())
            .filter(v -> StringUtil.isNotBlank(v.getQwUserId()))
            .orElseThrow(
                () ->
                    ExceptionPlusFactory.bizException(
                        ErrorCode.DATA_NOT_FOUND, "未查询到该用户的企业微信用户ID，无法催办"));
    final String title = String.format("%s 提醒您审核商品优化信息，请查看后处理！", reminderNickname);
    final String description = String.format("%s %s", data.getLinkTitle(), itemOptimizeStatusDesc);
    final String url =
        String.format(
            refreshConfig.getDomain() + "/operation-management/optimize-item/list?id=%s",
            cmd.getBusinessId());

    final WechatMsg wechatMsg = new WechatMsg();
    wechatMsg.setTitle(title);
    wechatMsg.setContent(description);
    wechatMsg.setLink(url);
    wechatMsg.setRecipient(staffBrief.getQwUserId());
    wechatMsg.setType(1);

    //        msgSender.send(wechatMsg);
    final HashMap<String, Object> variables = new HashMap<>();
    variables.put("id", itemOptimize.getId());
    variables.put("催办人花名", reminderNickname);
    variables.put(
        "状态",
        IEnum.getEnumOptByValue(ItemOptimizeStatus.class, itemOptimize.getStatus())
            .map(ItemOptimizeStatus::getDesc)
            .orElse("?"));
    variables.put("链接产品标题", itemOptimize.getLinkTitle());
    variables.put("被通知人", staffBrief.getQwUserId());
    QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.ITEM_OPTIMIZE_URGING, variables);
    return Response.buildSuccess();
  }

  @Override
  public Response complete(TaskCompleteCmd cmd) {
    Task task =
        processBizService
            .task(cmd.getTaskId())
            .orElseThrow(() -> ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND));
    final ItemOptimize itemOptimize =
        itemOptimizeService
            .lambdaQuery()
            .eq(ItemOptimize::getProcessInstId, task.getProcessInstanceId())
            .select(Entity::getId)
            .oneOpt()
            .orElseThrow(() -> ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "流程异常"));
    processBizService.forceComplete(UserContext.getUserId(), task.getId(), cmd.getComment());
    final ItemOptimizeAdvice itemOptimizeAdvice = new ItemOptimizeAdvice();
    itemOptimizeAdvice.setItemOptimizeId(itemOptimize.getId());
    itemOptimizeAdvice.setProcessInstId(task.getProcessInstanceId());
    itemOptimizeAdvice.setNodeId(task.getId());
    itemOptimizeAdvice.setNodeKey(task.getName());
    itemOptimizeAdvice.setModule("");
    itemOptimizeAdvice.setAdviceTarget("");
    itemOptimizeAdvice.setAdviceContent(cmd.getComment());
    itemOptimizeAdviceService.save(itemOptimizeAdvice);
    return Response.buildSuccess();
  }

  @Override
  public void planSubmit(Long planId) {
    final List<ItemOptimize> itemOptimizes =
        itemOptimizeService.lambdaQuery().eq(ItemOptimize::getPlanId, planId).list();
    if (!itemOptimizes.isEmpty()) {
      itemOptimizeService
          .lambdaUpdate()
          .eq(ItemOptimize::getPlanId, planId)
          .eq(ItemOptimize::getStatus, ItemOptimizeStatus.TO_BE_SUBMITTED.getValue())
          .set(ItemOptimize::getPlanStatus, 1)
          .update();
      for (ItemOptimize itemOptimize : itemOptimizes) {
        submit(itemOptimize);

        operateLogDomainService.addOperatorLog(
            UserContext.getUserId(),
            OperateLogTarget.ITEM_OPTIMIZE,
            itemOptimize.getId(),
            "提交了优化计划");
      }
    }
  }

  @Override
  @Transactional
  public Response rollback(RollbackCmd cmd) {
    final ItemOptimize itemOptimize = itemOptimizeService.getById(cmd.getItemOptimizeId());
    Objects.requireNonNull(itemOptimize, "商品优化查询异常");

    final ItemOptimizePersistData data = itemOptimize.getData();
    if (ObjectUtil.defaultIfNull(data.getDisableRollback(), false)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "下个流程人编辑后不可以撤回");
    }

    final String processInstId = itemOptimize.getProcessInstId();
    final List<Task> tasks = processBizService.tasksByProcInstId(processInstId);
    if (tasks.isEmpty()) {
      throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前流程状态异常，无法撤回");
    }
    final Task task = tasks.get(0);
    if (task.getClaimTime() != null) {
      throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前事项已被经办人认领，无法撤回");
    }

    checkCanRollback(itemOptimize, true);

    operateLogDomainService.addOperatorLog(
        UserContext.getUserId(), OperateLogTarget.ITEM_OPTIMIZE, cmd.getItemOptimizeId(), "流程撤回");

    final ItemOptimizeStatus itemOptimizeStatus =
        IEnum.getEnumByValue(ItemOptimizeStatus.class, itemOptimize.getStatus());
    final Response rollback;
    switch (itemOptimizeStatus) {
      case PENDING_QC_REVIEW:
        rollback = processBizService.terminal(UserContext.getUserId(), processInstId, "用户手动撤回");
        resetStatusToBeSubmitted(itemOptimize);
        break;
      case TO_BE_MODIFIED:
        rollback = processBizService.moveTo(UserContext.getUserId(), processInstId, "法务审核");
        break;
      case COMPLETED:
        rollback = processBizService.moveTo(UserContext.getUserId(), processInstId, "链接产品修改");
        break;
      case PENDING_LEGAL_REVIEW:
        rollback = processBizService.moveTo(UserContext.getUserId(), processInstId, "QC审核");
        break;
      default:
        throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前状态不允许撤回");
    }
    if (!rollback.isSuccess()) {
      throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "流程撤回异常");
    }
    return rollback;
  }

  private void resetStatusToBeSubmitted(ItemOptimize itemOptimize) {
    final ItemOptimizeStatus updateStatusTo = ItemOptimizeStatus.TO_BE_SUBMITTED;
    final ItemOptimize updateModel = new ItemOptimize();
    updateModel.setId(itemOptimize.getId());
    updateModel.setStatus(updateStatusTo.getValue());
    updateModel.setProcessInstId("");
    itemOptimizeService.updateById(updateModel);

    operateLogDomainService.addOperatorLog(
        UserContext.getUserId(),
        OperateLogTarget.ITEM_OPTIMIZE,
        itemOptimize.getId(),
        String.format("流程状态更新至【%s】", updateStatusTo.getDesc()));
  }

  private boolean checkCanRollback(ItemOptimize itemOptimize, boolean throwException) {
    final ItemOptimizeStatus itemOptimizeStatus =
        IEnum.getEnumByValue(ItemOptimizeStatus.class, itemOptimize.getStatus());
    final ItemOptimizePersistData data = itemOptimize.getData();
    final Long userId = UserContext.getUserId();
    switch (itemOptimizeStatus) {
      case PENDING_QC_REVIEW:
      case COMPLETED:
        final ItemOptimizePlan plan = itemOptimizePlanService.getById(itemOptimize.getPlanId());
        Assert.notNull(plan, "商品优化计划数据异常");

        if (data != null
            && data.getModifyUsers() != null
            && data.getModifyUsers().stream().noneMatch(v -> Objects.equals(v.getUserId(), userId))
            && !Objects.equals(plan.getCreatedUid(), userId)) {
          if (throwException) {
            throw ExceptionPlusFactory.bizException(
                ErrorCode.OPERATION_REJECT,
                String.format("【%s】状态下，仅上新计划创建人及修改负责人可撤回", itemOptimizeStatus.getDesc()));
          }
          return false;
        }
        break;
      case PENDING_LEGAL_REVIEW:
        if (data.getQcUsers().stream().noneMatch(v -> Objects.equals(v.getUserId(), userId))) {
          if (throwException) {
            throw ExceptionPlusFactory.bizException(
                ErrorCode.OPERATION_REJECT, "QC审核状态下，仅QC负责人可撤回");
          }
          return false;
        }
        break;
      case TO_BE_MODIFIED:
        if (data.getLegalUsers().stream().noneMatch(v -> Objects.equals(v.getUserId(), userId))) {
          if (throwException) {
            throw ExceptionPlusFactory.bizException(
                ErrorCode.OPERATION_REJECT, "QC审核状态下，仅QC负责人可撤回");
          }
          return false;
        }
        break;

      default:
        if (throwException) {
          throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前状态不允许撤回");
        }
        return false;
    }
    return true;
  }
}
