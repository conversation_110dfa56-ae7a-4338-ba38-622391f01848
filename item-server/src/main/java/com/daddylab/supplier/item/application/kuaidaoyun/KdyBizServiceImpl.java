package com.daddylab.supplier.item.application.kuaidaoyun;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.KdyCallback;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.KdySubscribe;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.KdyLogisticsStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IKdyCallbackService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IKdySubscribeService;
import com.daddylab.supplier.item.infrastructure.kuaidaoyun.KdyAPI;
import com.daddylab.supplier.item.infrastructure.kuaidaoyun.configs.KdyConfig;
import com.daddylab.supplier.item.infrastructure.kuaidaoyun.errors.KdyError;
import com.daddylab.supplier.item.infrastructure.kuaidaoyun.types.KdyBaseRequest;
import com.daddylab.supplier.item.infrastructure.kuaidaoyun.types.KdySubscribeData;
import com.daddylab.supplier.item.infrastructure.kuaidaoyun.types.KdySubscribeResponse;
import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQProducer;
import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQTopic;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.infrastructure.utils.HtmlUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.kuaidaoyun.KdyCallbackEvent;
import com.daddylab.supplier.item.types.kuaidaoyun.KdyCallbackRequest;
import com.daddylab.supplier.item.types.kuaidaoyun.KdyTrackList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validator;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/5/22
 */
@Service
@Slf4j
public class KdyBizServiceImpl implements KdyBizService {
    @Autowired
    private IKdySubscribeService kdySubscribeService;

    @Autowired
    private IKdyCallbackService kdyCallbackService;

    @Autowired
    private KdyAPI kdyAPI;

    @Autowired
    private KdyConfig kdyConfig;

    @Autowired
    RocketMQProducer rocketMQProducer;

    @Autowired
    private Validator validator;

    @Override
    public long subscribe(KdySubscribeData subscribeData) throws BizException {
        final Set<ConstraintViolation<KdySubscribeData>> violations = validator.validate(subscribeData);
        if (!violations.isEmpty()) {
            throw new ConstraintViolationException(violations);
        }
        final KdySubscribe kdySubscribe = new KdySubscribe();
        kdySubscribe.setLogisticsCompanyName(subscribeData.getCompany());
        kdySubscribe.setLogisticsNo(subscribeData.getDeliveryNo());
        kdySubscribe.setCallbackState(subscribeData.getState());
        kdySubscribe.setCheckMobile(subscribeData.getCheckMobile());

        try {
            final KdyBaseRequest<KdySubscribeData> request = new KdyBaseRequest<>(kdyConfig, subscribeData);
            final String subscribeResult = kdyAPI.subscribe(request);
            final KdySubscribeResponse kdySubscribeResponse = new KdySubscribeResponse(subscribeResult);
            if (kdySubscribeResponse.getCode() == 0) {
                kdySubscribe.setStatus(1);
                kdySubscribe.setBatchId(kdySubscribeResponse.getMsg());
            } else {
                throw new KdyError(kdySubscribeResponse.getCode(), kdySubscribeResponse.getMsg());
            }
            kdySubscribeService.save(kdySubscribe);
            return kdySubscribe.getId();
        } catch (Exception e) {
            String outerErr = "";
            kdySubscribe.setStatus(2);
            if (e instanceof KdyError) {
                kdySubscribe.setErr(String.format("code=%s msg=%s", ((KdyError) e).getCode(), e.getMessage()));
            } else {
                kdySubscribe.setErr(ExceptionUtil.getSimpleStackString(e));
            }
            kdySubscribeService.save(kdySubscribe);
            throw ExceptionPlusFactory.bizException(ErrorCode.KDY_ERROR, "快刀云订阅异常 " + outerErr);
        }
    }

    @Override
    public String callback(KdyCallbackRequest request, boolean checkSign) {
        if (checkSign) {
            final boolean signValid = checkCallbackSign(request);
            if (!signValid) {
                log.debug("[快刀云回调]签名验证未通过:{}", request);
                if (!kdyConfig.isIgnoreSignErr()) {
                    return "签名验证未通过";
                }
            }
        }

        //发送消息到mq
        rocketMQProducer.syncSend(request, MQTopic.KUAIDAOYUN_CALLBACK_TOPIC);
        return "0";
    }

    @Override
    public void doCallback(KdyCallbackRequest request) {
        filterRequest(request);
        final KdyCallback kdyCallback = kdyCallbackService.selectByLogisticsNo(request.getOutid())
                                                          .orElseGet(KdyCallback::new);
        kdyCallback.setLogisticsCompanyName(request.getCompanyname());
        kdyCallback.setLogisticsNo(request.getOutid());
        try {
            kdyCallback.setStatus(IEnum.getEnumByValue(KdyLogisticsStatus.class, Integer.parseInt(request.getStatus())));
        } catch (NumberFormatException ignored) {
        }
        final KdyTrackList tracklist = JsonUtil.parse(request.getTracklist(), KdyTrackList.class);
        kdyCallback.setTracklist(tracklist);
        kdyCallback.setCallbackState(request.getState());
        if (tracklist != null && tracklist.getUpdateDate() != null) {
            final long updateTime = Long.parseLong(tracklist.getUpdateDate().substring(6, 19));
            kdyCallback.setTrackTime(updateTime);
        }
        if (kdyCallback.getId() != null) {
            kdyCallbackService.updateById(kdyCallback);
        } else {
            kdyCallbackService.save(kdyCallback);
        }
        EventBusUtil.post(KdyCallbackEvent.of(kdyCallback), true);
    }

    private static void filterRequest(KdyCallbackRequest request) {
        request.setTracklist(HtmlUtil.unescape(request.getTracklist()));
    }

    @Override
    public SingleResponse<KdyCallback> getCallback(Long id) {
        final KdyCallback callbackPO = kdyCallbackService.getById(id);
        if (callbackPO == null) {
            return SingleResponse.of(null);
        }
        return SingleResponse.of(callbackPO);
    }

    /**
     * MD5(secret+"companyname="+companyname+"&outid="+outid+"&status="+status+"&tracklist="+tracklist+secret)
     */
    private boolean checkCallbackSign(KdyCallbackRequest request) {
        final String secret = kdyConfig.getSecret();
        final StringBuilder sb = new StringBuilder();
        sb.append(secret);
        sb.append("companyname=").append(request.getCompanyname());
        sb.append("&outid=").append(request.getOutid());
        sb.append("&status=").append(request.getStatus());
        sb.append("&tracklist=").append(request.getTracklist());
        sb.append(secret);
        final String sign = DigestUtil.md5Hex(sb.toString()).toUpperCase();
        return Objects.equals(request.getSign(), sign);
    }


}
