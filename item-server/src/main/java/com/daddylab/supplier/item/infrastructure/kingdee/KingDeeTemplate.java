package com.daddylab.supplier.item.infrastructure.kingdee;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.stockInOrder.StockInOrderBizService;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.alert.WeChatContent;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockOutOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockInOrderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockOutOrderService;
import com.daddylab.supplier.item.infrastructure.kingdee.dto.KingDeeSkuResp;
import com.daddylab.supplier.item.infrastructure.kingdee.dto.KingDeeStockInOrderResp;
import com.daddylab.supplier.item.infrastructure.kingdee.dto.KingDeeStockOutOrderResp;
import com.daddylab.supplier.item.infrastructure.kingdee.req.SkuPriceCategoryReq;
import com.daddylab.supplier.item.infrastructure.kingdee.util.HttpUtil;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqJsonUtil;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
import com.daddylab.supplier.item.infrastructure.kingdee.util.RespUtil;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR> up
 * @date 2022/4/7 5:42 下午
 */
@Component
@Slf4j
public class KingDeeTemplate {

    @Autowired
    ReqTemplate reqTemplate;

    @Autowired
    ReqJsonUtil reqJsonUtil;

    @Autowired
    HttpUtil httpUtil;

    @Autowired
    RefreshConfig refreshConfig;

    @Autowired
    ItemSkuGateway itemSkuGateway;

    @Autowired
    IStockInOrderService iStockInOrderService;

    @Autowired
    StockInOrderBizService stockInOrderBizService;

    private final static String TIME_OUT_FLAG = "timed-out and no fallback available";

    @Autowired
    IStockOutOrderService iStockOutOrderService;

    /**
     * 同步item
     *
     * @param itemId
     */
    public void syncItem(Long itemId) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            String activeProfile = SpringUtil.getActiveProfile();
            boolean handleSkuPriceCategory = activeProfile.equals("gray") || activeProfile.equals("prod");

            List<ItemSku> skuList = itemSkuGateway.getSkuList(itemId);
            for (ItemSku itemSku : skuList) {
                if (StringUtil.isBlank(itemSku.getKingDeeId())) {
                    handler(ApiEnum.SAVE_SKU, itemSku.getId(), "");
                } else {
                    handler(ApiEnum.UPDATE_SKU, itemSku.getId(), itemSku.getKingDeeId());
                }

                if (!handleSkuPriceCategory) {
                    continue;
                }
                String kingDeeId = itemSku.getKingDeeId();
                if (StringUtil.isBlank(kingDeeId)) {
                    continue;
                }
                if (BigDecimal.ZERO.compareTo(itemSku.getCostPrice()) == 0
                        || BigDecimal.ZERO.compareTo(itemSku.getPurchaseTaxRate()) == 0) {
                    log.info("sku成本为零或者采购税率为零，金蝶采购价格目录不处理，skuCode:{}", itemSku.getSkuCode());
                    continue;
                }
                SkuPriceCategoryReq skuPriceCategoryReq = reqTemplate.querySkuPriceCategory(kingDeeId);
                log.info("sku采购价格目录数据，金蝶查询返回。skuCode:{},resp:{}", itemSku.getSkuCode(), JsonUtil.toJson(skuPriceCategoryReq));
                // 此sku的采购价格目录不存在，直接上传。
                if (Objects.isNull(skuPriceCategoryReq)) {
                    handler(ApiEnum.SAVE_PRICE_CATEGORY, itemSku.getId(), "");
                    log.info("sku采购价格目录新增到金蝶完成，skuCode:{}", itemSku.getSkuCode());
                    continue;
                }
                if ("CGJM000001".equals(skuPriceCategoryReq.getBillNo())) {
                    log.info("sku为历史单据中的数据，暂时不处理，skuCode:{}", itemSku.getSkuCode());
                    continue;
                }
                // 如果金蝶上存的sku的数据和目前erp系统中的数据，在 成本加和采购税率上不一致。将金蝶数据删除，重新推（此金蝶API暂时不支持直接更新）
                BigDecimal erpTaxRate = itemSku.getPurchaseTaxRate().multiply(new BigDecimal(100));
                boolean update = (skuPriceCategoryReq.getPrice().compareTo(itemSku.getCostPrice()) != 0)
                        || (skuPriceCategoryReq.getTaxRate().compareTo(erpTaxRate) != 0);
                if (update) {
                    try {
                        log.info("sku的成本或者采购税率发生变化，删除旧的金蝶数据，插入新的数据。skuCode:{}", itemSku.getSkuCode());
                        reqTemplate.unAuditSkuPriceCategory(skuPriceCategoryReq.getBillNo());
                        reqTemplate.deleteSkuPriceCategory(skuPriceCategoryReq.getBillNo());
                        handler(ApiEnum.SAVE_PRICE_CATEGORY, itemSku.getId(), "");
                    } catch (Exception e) {
                        log.error("删除+新增sku采购价格目录信息，操作异常，skuCode:{}", itemSku.getSkuCode(), e);
                    }
                } else {
                    log.info("sku成本或者采购税率没有发生变化，采购价格目录不处理，skuCode:{}", itemSku.getSkuCode());
                }
            }
        });
    }


    /**
     * 同步sku
     *
     * @param itemSku
     */
    public Response syncSku(ItemSku itemSku) {
        if (StringUtil.isBlank(itemSku.getKingDeeId())) {
            return handler(ApiEnum.SAVE_SKU, itemSku.getId(), "");
        } else {
            return handler(ApiEnum.UPDATE_SKU, itemSku.getId(), itemSku.getKingDeeId());
        }
    }


    private static final String SAVE_FLAG1 = "SAVE";
    private static final String SAVE_FLAG2 = "GROUP_SAVE";
    private static final String UPDATE_FLAG = "UPDATE";
    private static final String DELETE_FLAG1 = "DELETE";
    private static final String DELETE_FLAG2 = "GROUP_DELETE";

    /**
     * 允许跳过反审核，直接提交标志
     */
    private static final String DIRECT_SUBMIT_FLAG = "数据未提交审核";

    /**
     * mock标志
     */
    private static final String MOCK_SYNC_FLAG = "0";

    /**
     * 处理金蝶同步消息
     *
     * @param kingDeeApiEnum
     * @param id
     * @param kingDeeId
     * @throws Exception
     */
    public Response handler(ApiEnum kingDeeApiEnum, Long id, String kingDeeId) {
        if (refreshConfig.getKingDeeSync().equals(MOCK_SYNC_FLAG)) {
            mockHandler(kingDeeApiEnum, id);
            return SingleResponse.of(true);
        }

        String name = kingDeeApiEnum.name();
        // 删除和修改已提交的数据，必须先进行反审核
        boolean needUnAudit = name.startsWith(DELETE_FLAG1) || name.startsWith(DELETE_FLAG2) || name.startsWith(UPDATE_FLAG);
        if (name.equals("UPDATE_SKU")) {
            needUnAudit = false;
        }
        if (needUnAudit) {
            Assert.notBlank(kingDeeId, "需要进行金蝶反审核，同步id不得为空");
            String unAuditJson = ReflectUtil.invoke(reqJsonUtil, "commonReqJson", kingDeeId, kingDeeApiEnum.getFormId());
            boolean finishUnAudit;
            String errorMsg = "";
            try {
                finishUnAudit = reqTemplate.unAudit(unAuditJson);
            } catch (Exception e) {
                if (e.getMessage().contains(DIRECT_SUBMIT_FLAG)) {
                    // 可能此条数据已经在金蝶系统里面反操作过了。
                    log.error("因为此数据金蝶状态非已审核。所以反审核异常，但是数据可以直接提交。");
                    finishUnAudit = true;
                } else {
                    log.error("因为此数据金蝶状态是已审核，更新/删除数据前，反审核不允许失败。", e);
                    errorMsg = e.getMessage();
                    finishUnAudit = false;
                }
            }
            if (!finishUnAudit) {
                WeChatContent.WeChatContentBuilder builder = WeChatContent.builder();
                builder.api(kingDeeApiEnum.getDesc()).id(id).error(errorMsg)
                        .remark("金蝶反审核失败（只有删除和更新操作需要反审核，请确保此数据对应的kingDeeId在金蝶云星空中存在");
                sendError(builder.build());
                SingleResponse.buildFailure(ErrorCode.THIRD_PARAM_ERROR.getCode(), "KingDee反审核失败");
            }
        }

//        WeChatContent.WeChatContentBuilder builder = WeChatContent.builder();
        Response response = requestHandler(kingDeeApiEnum, id);
//        if (!response.isSuccess()) {
//            // 发送错误信息
//            sendError(builder.build());
//        }
        return response;
    }

    private void sendError(WeChatContent weChatContent) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> Alert.markdown(MessageRobotCode.GLOBAL, weChatContent.toString()));
    }

    private Response requestHandler(ApiEnum kingDeeApiEnum, Long id) {
        WeChatContent.WeChatContentBuilder builder = WeChatContent.builder();
        String name = kingDeeApiEnum.name();
        String reqJson = "";
        builder.api(kingDeeApiEnum.getDesc()).id(id);
        Object resObj;

        try {
            reqJson = reqJsonUtil.invoke(kingDeeApiEnum, id);
        } catch (Exception e) {
            log.error("生成kingDee同步参数异常，id:{}", id, e);
            builder.req(reqJson).error("生成同步参数异常," + e.getMessage());
            sendError(builder.build());
            return SingleResponse.buildFailure(ErrorCode.THIRD_PARAM_ERROR.getCode(), e.getMessage());
        }

        try {
            resObj = httpUtil.invoke(kingDeeApiEnum, reqJson);
        } catch (Exception e) {
//            if (e instanceof HystrixRuntimeException) {
//                log.error("请求kingDee同步接口，金蝶响应超时，请手动检查是否同步成功。id:{}，api:{}", id, kingDeeApiEnum.getDesc(), e);
//                return SingleResponse.of(true);
//            } else {
            builder.req(reqJson).error("请求kingDee同步接口,响应异常，" + e.getMessage());
            sendError(builder.build());
            return SingleResponse.buildFailure(ErrorCode.THIRD_PARAM_ERROR.getCode(), e.getMessage());
//            }
        }

        try {
            if (name.equals(ApiEnum.SAVE_PRICE_CATEGORY.name())) {
                return SingleResponse.of(true);
            }
            if (name.startsWith(DELETE_FLAG1) || name.startsWith(DELETE_FLAG2)) {
                RespUtil.invoke(kingDeeApiEnum, id);
            }
            if (name.startsWith(SAVE_FLAG1) || name.startsWith(SAVE_FLAG2)) {
                RespUtil.invoke(kingDeeApiEnum, id, resObj.toString());
            }
        } catch (Exception e) {
            log.error("后续处理异常，id:{},resp:{}", id, resObj, e);
            builder.resp(resObj.toString()).error("后续处理异常，" + e.getMessage());
            sendError(builder.build());
            return SingleResponse.buildFailure(ErrorCode.THIRD_PARAM_ERROR.getCode(), e.getMessage());
        }

        return SingleResponse.of(true);
    }

    /**
     * 实测发现金蝶有些接口的响应时间非常长，超过了熔断器的超时等待时间。
     * 主动去查询kingDee,不再等待响应
     */
    private Boolean timeOutHandler(ApiEnum kingDeeApiEnum, Long id) {
        try {
            AtomicBoolean flag = new AtomicBoolean(false);
            if (ApiEnum.SAVE_SKU.equals(kingDeeApiEnum)) {
                Optional<ItemSku> itemSkuOptional = itemSkuGateway.getByItemSkuId(id);
                itemSkuOptional.ifPresent(itemSku -> {
                    Optional<KingDeeSkuResp> optional = reqTemplate.querySkuByNo(itemSku.getSkuCode());
                    optional.ifPresent(kingDeeSkuResp -> {
                        if (StringUtil.isNotBlank(kingDeeSkuResp.getId())) {
                            itemSkuGateway.setSkuKingDeeId(id, kingDeeSkuResp.getId());
                            flag.set(true);
                        }
                    });
                });
            }

            if (ApiEnum.SAVE_STOCK_IN_ORDER.equals(kingDeeApiEnum)) {
                Optional<StockInOrder> stockInOrderOptional = iStockInOrderService.lambdaQuery().eq(StockInOrder::getId, id).select().oneOpt();
                stockInOrderOptional.ifPresent(stockInOrder -> {
                    Optional<KingDeeStockInOrderResp> optional = reqTemplate.queryStockInOrderByNo(stockInOrder.getNo());
                    optional.ifPresent(kingDeeResp -> {
                        if (StringUtil.isNotBlank(kingDeeResp.getId())) {
                            stockInOrder.setKingDeeId(kingDeeResp.getId());
                            iStockInOrderService.updateById(stockInOrder);
                            flag.set(true);
                        }
                    });
                });
            }

            if (ApiEnum.SAVE_STOCK_OUT_ORDER.equals(kingDeeApiEnum)) {
                Optional<StockOutOrder> optional = iStockOutOrderService.lambdaQuery().eq(StockOutOrder::getId, id).select().oneOpt();
                optional.ifPresent(stockOutOrder -> {
                    Optional<KingDeeStockOutOrderResp> optional1 = reqTemplate.queryStockOutOrderByNo(stockOutOrder.getNo());
                    optional1.ifPresent(kingDeeResp -> {
                        if (StringUtil.isNotBlank(kingDeeResp.getId())) {
                            stockOutOrder.setKingDeeId(kingDeeResp.getId());
                            iStockOutOrderService.updateById(stockOutOrder);
                            flag.set(true);
                        }
                    });
                });
            }
            return flag.get();
        } catch (Exception e) {
            throw e;
        }

    }


    private void mockHandler(ApiEnum kingDeeApiEnum, Long targetId) {
        log.info("金蝶同步mock,api:{},targetId:{}", kingDeeApiEnum.name(), targetId);
    }

    public static void main(String[] args) {
        BigDecimal b1 = new BigDecimal("13.000000");
        BigDecimal b2 = new BigDecimal("13.00");
        System.out.println(b1.compareTo(b2));
    }

    private void syncSkuUnit(String skuCode){

    }


}
