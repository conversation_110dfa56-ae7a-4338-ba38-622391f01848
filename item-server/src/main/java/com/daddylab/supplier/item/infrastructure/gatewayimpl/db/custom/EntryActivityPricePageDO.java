package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/10/6
 */
@Data
public class EntryActivityPricePageDO {
    private Long id;
    private String month;
    private String itemCode;
    private Long itemId;
    private String skuCode;
    private Long skuId;
    private String itemName;
    private String imgUrl;
    private String provider;
    private Long providerId;
    private Long buyerUid;
    private Integer status;

    /**
     * 合作方层级
     */
    private String coryTypeVal;
    /**
     * 业务类型层级
     */
    private String bizTypeVal;
}
