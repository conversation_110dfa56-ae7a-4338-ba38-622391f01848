package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryAllocShopStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 平台商品（投放到其他平台的商品）商品维度
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Data
@EqualsAndHashCode(callSuper = false, of = "id")
public class PlatformItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 平台货品ID
     */
    private String outerItemId;

    /**
     * 平台货品编码
     */
    private String outerItemCode;

    /**
     * 匹配到的我们自己的商品ID
     */
    private Long itemId;

    /**
     * 商品编码
     */
    private String itemCode;

    /**
     * 商品品类ID
     */
    private Long categoryId;

    /**
     * 匹配到的我们自己的店铺ID
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺编号
     */
    private String shopNo;

    /**
     * 平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城
     */
    private Platform platform;

    /**
     * 平台商品类型 1:自营 2:代销
     */
    private PlatformItemType type;

    /**
     * {@link PlatformItemStatus}
     */
    private PlatformItemStatus status;

    /**
     * sku数量
     */
    private Integer skuNum;

    /**
     * 平台售价
     */
    private BigDecimal price;

    /**
     * 平台库存
     */
    private Integer stock;

    /**
     * 平台货品名称
     */
    private String goodsName;

    /**
     * 平台商品最后修改时间（旺店通返回）
     */
    private LocalDateTime modified;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 平台商品规格
     */
    @TableField(exist = false)
    private List<PlatformItemSku> platformItemSkus;

    /**
     * 是否开启库存同步（可能为null，代表未设置，应该使用店铺的设置）
     */
    private Boolean syncEnabled;
    
    /**
     * 结合店铺的设置，判断是否开启库存同步
     * @param inventoryAllocShop 库存分配店铺设置
     */
    public void setSyncEnabledByShopSetting(InventoryAllocShop inventoryAllocShop) {
        this.syncEnabled = inventoryAllocShop != null
                && inventoryAllocShop.getStatus() != InventoryAllocShopStatus.FORBIDDEN
                && (syncEnabled == null || syncEnabled);
    }
    
    /**
     * 是否开启库存锁定
     */
    private Boolean lockEnabled;
    
    /**
     * 首次上架时间
     */
    private Long firstListingTime;
    
    /**
     * 首销时间
     */
    private Long firstSalesTime;
    
    /**
     * 下架时间
     */
    private Long unListingTime;
    
    /**
     * 最后分配库存时间
     */
    private Long lastStockAllocTime;
    
    /**
     * 最后同步库存时间
     */
    private Long lastStockSyncTime;


}
