package com.daddylab.supplier.item.application.saleItem.vo;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/4/17
 */
@Data
public class NewGoodsDownRequest {
    @ApiModelProperty("商品id")
    @NotNull
    private Long itemId;

    @ApiModelProperty("下架时间")
    @NotNull
    private Long downFrameTime;

    @ApiModelProperty("下架理由")
    @NotNull
    private String downFrameReason;

    @ApiModelProperty(value = "是否同时下架合并商品", notes = "默认下架")
    private Boolean downMergeItem = true;
}
