package com.daddylab.supplier.item.controller.provider.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ProviderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11 10:18 上午
 * @description
 */
@Data
@ApiModel("供应商下拉返回")
public class ProviderDropDownVo implements Serializable {


    private static final long serialVersionUID = -858573573301148946L;

    @ApiModelProperty("供应商名字")
    private String name;

    @ApiModelProperty("供应商id")
    private Long id;
    /**
     * 状态 1:合作 2:停用
     */
    @ApiModelProperty("状态")
    private ProviderStatus status;

    @ApiModelProperty("标准")
    private String standard;

    @ApiModelProperty("税率")
    private BigDecimal rate;

    @ApiModelProperty("供应商负责人用户ID")
    private Long chargerUserId;

    @ApiModelProperty("供应商负责人用户名称")
    private String chargerUserName;

    @ApiModelProperty("供应商关联的P系统ID。0标识为无关联，无需处理")
    private Long partnerProviderId;


    private Long mainChargerUserId;
    private Long secondChargerUserId;
}
