package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetailWrapper;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
public interface IWdtOrderDetailWrapperService extends IDaddyService<WdtOrderDetailWrapper> {

    /**
     * 根据 操作时间和skuCode查询 wdt订单装包类
     *
     * @param skuCodeList skuCodeList
     * @param operateTime 曹组时间 yyyyMM
     * @return
     */
    List<WdtOrderDetailWrapper> getListBySkuCode(List<String> skuCodeList, String operateTime);

    void append(WdtOrderDetailWrapper wrapper);
}
