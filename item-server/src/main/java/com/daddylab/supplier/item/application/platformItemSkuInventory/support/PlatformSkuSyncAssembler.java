package com.daddylab.supplier.item.application.platformItemSkuInventory.support;

import com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params.PlatformSkuInventorySyncInfo;
import com.daddylab.supplier.item.application.platformItemSkuInventory.domain.params.PlatformSkuSyncInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/4/2
 */
@Mapper
public interface PlatformSkuSyncAssembler {
    PlatformSkuSyncAssembler INSTANCE = Mappers.getMapper(PlatformSkuSyncAssembler.class);

    PlatformSkuInventorySyncInfo platformSkuSyncInfoToPlatformSkuInventorySyncInfo(PlatformSkuSyncInfo value);
}
