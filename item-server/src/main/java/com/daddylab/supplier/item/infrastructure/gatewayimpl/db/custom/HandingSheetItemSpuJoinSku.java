package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.daddylab.supplier.item.types.handingsheet.HandingSheetItemSkuPersistData;
import com.daddylab.supplier.item.types.handingsheet.HandingSheetItemSpuPersistData;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 盘货表关联的商品（V2.4.5新版盘货表）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class HandingSheetItemSpuJoinSku implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 盘货表 SPU ID
     */
    private Long handingSheetSpuId;

    /**
     * 盘货表ID
     */
    private Long handingSheetId;

    /**
     * 主平台
     */
    private Integer platform;

    /**
     * 主平台商品ID
     */
    private String outerItemId;

    /**
     * 外部商品编码
     */
    private String itemCode;

    /**
     * 后端商品 ID
     */
    private Long itemId;

    /**
     * 盘货表商品SPU纬度数据模型
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private HandingSheetItemSpuPersistData spuData;

    /**
     * 盘货表 SKU ID
     */
    private Long handingSheetItemSkuId;

    /**
     * 外部SKU编码
     */
    private String skuCode;

    /**
     * 后端商品SKU ID
     */
    private Long skuId;

    /**
     * 是否审核通过
     */
    private Integer isPassed;

    /**
     * 盘货表商品SKU纬度数据模型
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private HandingSheetItemSkuPersistData skuData;

}
