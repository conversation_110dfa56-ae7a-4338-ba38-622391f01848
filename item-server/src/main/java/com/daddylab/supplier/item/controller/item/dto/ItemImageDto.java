package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/20 5:15 下午
 * @description
 */
@Data
@ApiModel("图片属性封装")
public class ItemImageDto {

    @ApiModelProperty("id，图片id。新建为空，编辑必填。")
    private Long id;

    @ApiModelProperty("图片url")
    @NotBlank(message = "图片url不得为空")
    private String imageUrl;

//    @ApiModelProperty("图片排序")
//    @NotNull(message = "图片排序不得为空")
//    Integer sort;

    @ApiModelProperty("图片类型。1:普通商品图片 2:运营商品图片")
    @NotNull(message = "图片类型不得为空")
    private Integer type;

    @ApiModelProperty("是否主图。1:yes。0:no")
    @NotNull(message = "是否是主图，判断不得为空。")
    private Integer isMain;

    private Long itemId;


}
