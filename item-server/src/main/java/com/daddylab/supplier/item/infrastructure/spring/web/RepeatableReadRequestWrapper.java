package com.daddylab.supplier.item.infrastructure.spring.web;

import cn.hutool.core.io.FastByteArrayOutputStream;
import cn.hutool.core.io.IoUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import org.springframework.http.MediaType;

/**
 * <AUTHOR>
 * @since 2022/10/17
 */
public class RepeatableReadRequestWrapper extends HttpServletRequestWrapper {

    private FastByteArrayOutputStream requestBody;
    private boolean isHandle;

    /**
     * Constructs a request object wrapping the given request.
     *
     * @param request The request to wrap
     */
    public RepeatableReadRequestWrapper(HttpServletRequest request) {
        this(request, new MediaType[0]);
    }

    /**
     * Constructs a request object wrapping the given request.
     *
     * @param request The request to wrap
     * @param types   mime types to handle
     */
    public RepeatableReadRequestWrapper(HttpServletRequest request, MediaType... types) {
        super(request);
        if (isHandleMediaType(types)) {
            try {
                this.requestBody = new FastByteArrayOutputStream();
                IoUtil.read(request.getInputStream()).writeTo(this.requestBody);
                this.isHandle = true;
            } catch (IOException ignored) {
            }
        }
    }

    private Set<MediaType> initDefaultTypes(MediaType[] types) {
        Set<MediaType> typesToHandle = new HashSet<>();
        typesToHandle.add(MediaType.APPLICATION_XML);
        typesToHandle.add(MediaType.APPLICATION_JSON);
        typesToHandle.addAll(Arrays.asList(types));
        return typesToHandle;
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        if (isHandle) {
            return new ByteArrayServletInputStream(requestBody.toByteArray());
        }
        return super.getInputStream();
    }

    public boolean isHandle() {
        return isHandle;
    }

    private boolean isHandleMediaType(MediaType[] types) {
        Set<MediaType> typesToHandle = initDefaultTypes(types);
        return typesToHandle.stream()
                .anyMatch(v -> v.equalsTypeAndSubtype(
                        Optional.ofNullable(this.getContentType())
                                .filter(StringUtil::isNotBlank)
                                .map(MediaType::parseMediaType)
                                .orElse(MediaType.APPLICATION_JSON) //未传默认作为 application/json 处理
                ));
    }
}
