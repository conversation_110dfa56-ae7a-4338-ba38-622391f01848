package com.daddylab.supplier.item.infrastructure.exceptions;

import com.alibaba.cola.exception.BizException;
import com.daddylab.supplier.item.common.enums.ErrorCode;

/**
 * <AUTHOR>
 * @since 2024/4/12
 */
public class BizExceptionWithData extends BizException {
    private static final long serialVersionUID = 4310421249584918461L;

    private Object data;

    public void setData(Object data) {
        this.data = data;
    }

    public Object getData() {
        return data;
    }

    public BizExceptionWithData(ErrorCode errorCode) {
        super(errorCode.getCode(), errorCode.getMsg());
    }

    public BizExceptionWithData(ErrorCode errorCode, String errMsg) {
        super(errorCode.getCode(), errMsg);
    }

}
