package com.daddylab.supplier.item.application.saleItem.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName NewGoodsSheet.java
 * @description
 * @createTime 2022年04月20日 17:38:00
 */
@Data
@HeadRowHeight(20)
@ContentRowHeight(16)
@EqualsAndHashCode(callSuper = true)
public class NewGoodsSheet extends ExportSheet implements Serializable {
    private static final long serialVersionUID = -752114031599220390L;

    @ExcelProperty("商品类型")
    @ColumnWidth(50)
    private String itemType;

    @ExcelProperty("会员店ID")
    @ColumnWidth(50)
    private String vipShopId;

    @ExcelProperty("小程序ID")
    @ColumnWidth(50)
    private String wechatId;

    @ExcelProperty("抖店ID")
    @ColumnWidth(50)
    private String douDianId;

    @ExcelProperty("小红书ID")
    @ColumnWidth(50)
    private String miniRedBookId;

    @ExcelProperty(value = "采购品名")
    @ColumnWidth(50)
    private String name;

    @ExcelProperty(value = "SPU编码")
    @ColumnWidth(50)
    private String itemCode;

    @ExcelProperty(value = "SKU编码")
    @ColumnWidth(50)
    private String skuCode;

    @ExcelProperty(value = "品类")
    @ColumnWidth(50)
    private String category;

    @ColumnWidth(50)
    @ExcelProperty(value = "合作模式")
    @ExcelIgnore
    private String businessLine;

    @ExcelProperty(value = "合作方-业务类型")
    @ColumnWidth(50)
    private String corpBizType;

    @ExcelProperty(value = "品牌")
    @ColumnWidth(50)
    private String brand;

    @ExcelProperty(value = "采购负责人")
    @ColumnWidth(50)
    private String buyer;

    @ExcelProperty(value = "产品负责人")
    @ColumnWidth(50)
    private String principal;

    @ExcelProperty(value = "QC负责人")
    @ColumnWidth(50)
    private String qcs;

    @ExcelProperty(value = "产品标准名")
    @ColumnWidth(50)
    private String standardName;

    @ExcelProperty(value = "上架日期")
    @ColumnWidth(50)
    private String shelfTime;

    @ExcelProperty(value = "商品状态")
    @ColumnWidth(50)
    private String status;

    @ExcelProperty(value = "直播话术")
    @ColumnWidth(50)
    private String liveVerbalTrickStatus;

    @ExcelProperty(value = "新品活动周期")
    @ColumnWidth(50)
    private String activePeriod;

    @ExcelProperty(value = "颜色/规格")
    @ColumnWidth(50)
    private String specs;

    @ExcelProperty(value = "采购成本")
    private String costPrice;

    @ColumnWidth(50)
    @ExcelProperty(value = "划线价")
    private String linePrice;

    @ColumnWidth(50)
    @ExcelProperty(value = "产品日销价")
    private String dailyPrice;

    @ColumnWidth(50)
    @ExcelProperty(value = "日常活动机制")
    private String dailyActivities;

    @ColumnWidth(50)
    @ExcelProperty(value = "S级/新品活动价")
    private String activePrice;

    @ColumnWidth(50)
    @ExcelProperty(value = "S级/新品活动机制")
    private String activeContent;

//    @ColumnWidth(50)
//    @ExcelProperty(value = "运营意见反馈")
//    private String runFeedback;

    //    @ColumnWidth(50)
//    @ExcelProperty(value = "新品直播机制")
    @ExcelIgnore
    private String liveActive;

    //    @ExcelProperty("A级活动售价")
//    @ColumnWidth(50)
    @ExcelIgnore
    private String aLevelActivityPrice;

    //    @ExcelProperty("A级活动赠品/多件机制")
//    @ColumnWidth(50)
    @ExcelIgnore
    private String aLevelActivityGift;

    //    @ExcelProperty("A级活动直播价")
//    @ColumnWidth(50)
    @ExcelIgnore
    private String aLevelActivityLivePrice;

    //    @ExcelProperty("A级活动直播赠品/多件机制")
//    @ColumnWidth(50)
    @ExcelIgnore
    private String aLevelActivityLiveGift;

    //    @ExcelProperty("S级大促售价")
//    @ColumnWidth(50)
    @ExcelIgnore
    private String sLevelPromotePrice;

    //    @ExcelProperty("S级大促机制")
//    @ColumnWidth(50)
    @ExcelIgnore
    private String sLevelPromoteRule;

    @ExcelProperty("S级一口价/直播价")
    @ColumnWidth(50)
    private String sLevelPromoteLivePrice;

    @ExcelProperty("S级一口价活动机制/直播机制")
    @ColumnWidth(50)
    private String sLevelPromoteLiveRule;


//    @ColumnWidth(50)
//    @ExcelProperty(value = "是否参与满减")
//    private String isReduce;
//
//    @ExcelProperty(value = "是否叠加券")
//    @ColumnWidth(50)
//    private String isCoupon;
//
//    @ColumnWidth(50)
//    @ExcelProperty(value = "渠道活动最低价")
//    private String channelLowest;

    @ColumnWidth(50)
    @ExcelProperty(value = "发货类型")
    private String shipmentType;

    @ColumnWidth(50)
    @ExcelProperty(value = "发货地")
    private String shipmentArea;

    @ColumnWidth(50)
    @ExcelProperty(value = "48小时发货")
    private String shipmentAging;

    @ColumnWidth(50)
    @ExcelProperty(value = "物流")
    private String logistics;

    @ColumnWidth(50)
    @ExcelProperty(value = "快递模板")
    private String expressTemplate;

    @ExcelProperty(value = "是否支持7天无理由退换")
    @ColumnWidth(50)
    private String noReason;

    @ColumnWidth(50)
    @ExcelProperty(value = "备注")
    private String remark;

    @ColumnWidth(50)
    @ExcelProperty(value = "技术类目")
    private String partnerSysType;

    @ColumnWidth(50)
    @ExcelProperty(value = "单买到手价")
    private BigDecimal singleBuyPrice;

    @ColumnWidth(50)
    @ExcelProperty(value = "培训资料")
    private String materialsStatus;

    @ExcelProperty(value = "下架时间")
    private String downFrameTime;

    @ExcelProperty(value = "下架理由")
    private String downFrameReason;


}
