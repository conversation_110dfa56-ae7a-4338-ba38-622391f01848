package com.daddylab.supplier.item.application.afterSalesForwarding.types;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @since 2024/5/24
 */
@Data
public class AfterSalesForwardingRegisterImportExcelVO {

    /**
     * 快递单号
     */
    @ApiModelProperty(value = "快递单号")
    @ExcelProperty("* 快递单号")
    @NotBlank()
    private String deliveryNo;

    /**
     * 登记时间
     */
    @ApiModelProperty(value = "登记时间")
    @ExcelProperty("* 登记时间")
    @NotBlank()
    private String registerTime;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    @ExcelProperty("* 商品名称")
    @NotBlank()
    private String itemName;

    /**
     * 商品数量
     */
    @ApiModelProperty(value = "退回数量")
    @ExcelProperty("* 退回数量")
    @NotBlank()
    @Pattern(regexp = "^[0-9]*$", message = "必须为数字")
    private String itemNum;

    /**
     * 是否完好
     */
    @ApiModelProperty(value = "是否完好")
    @ExcelProperty("* 是否完好")
    @NotBlank()
    private String intact;

    /**
     * 异常说明
     */
    @ApiModelProperty(value = "异常说明")
    @ExcelProperty("异常说明")
    private String abnormalDescription;
}
