package com.daddylab.supplier.item.domain.itemStock.vo;

import com.alibaba.cola.dto.DTO;
import com.daddylab.supplier.item.domain.itemStock.enums.StockChangeType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ItemStockChange implements Serializable {
    //库存变动类型
    private StockChangeType type;
    //变动数量（入库为正数，出库为负数）
    private Integer change;
    //库存变动时间
    private LocalDateTime changedAt;
    //变更用户ID
    private Long operatorId;
}
