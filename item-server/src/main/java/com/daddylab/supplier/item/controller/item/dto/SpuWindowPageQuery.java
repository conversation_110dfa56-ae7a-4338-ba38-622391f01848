package com.daddylab.supplier.item.controller.item.dto;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年11月04日 4:56 PM
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SpuWindowPageQuery extends PageQuery {

    private static final long serialVersionUID = 2957448448277563155L;
    private String name;

    private String code;

    private Long brandId;

    private String sNo;

    private Long categoryId;

    private Long providerId;

    private Integer status;

    private List<Integer> businessLines;


}
