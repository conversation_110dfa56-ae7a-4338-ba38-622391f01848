package com.daddylab.supplier.item.application.purchase.combinationPrice;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> up
 * @date 2023年04月04日 10:46 AM
 */
@Data
@ApiModel("sku组合价分页茶轩")
public class PricePageQuery extends PageQuery {

    private static final long serialVersionUID = 7903390709069681613L;
    @ApiModelProperty("sku编码")
    private String skuCode;

    @ApiModelProperty("价格类型。1：日常。2：活动")
    private Integer priceType;

    @ApiModelProperty("true:单品sku维度。false:跨sku,spu纬度")
    @NotNull(message = "价格类型选择不得为空")
    private Boolean isSingle;
}


