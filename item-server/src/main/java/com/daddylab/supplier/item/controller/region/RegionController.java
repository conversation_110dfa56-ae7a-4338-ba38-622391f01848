package com.daddylab.supplier.item.controller.region;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.region.RegionBizService;
import com.daddylab.supplier.item.domain.region.vo.Region;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/region")
@RestController
@Api(value = "RegionController", tags = "区域相关API")
public class RegionController {
    @Autowired
    private RegionBizService regionBizService;

    @GetMapping(value = "/getRegionList")
    MultiResponse<Region> getRegionList(@ApiParam(name = "parentCode", value = "上级区域编码")
                                        @RequestParam(value = "parentCode", required = false) String parentCode) {
        return regionBizService.getRegionList(parentCode);
    }

    @ApiOperation(value = "获取大区列表")
    @GetMapping(value = "/getBigRegionList")
    MultiResponse<Region> getBigRegionList() {
        return regionBizService.getBigRegionList();
    }

    @PostMapping(value = "/clearRegionTreeCache")
    @Auth("/PERMISSION_OF_ADMINISTRATOR")
    Response clearRegionTreeCache() {
        return regionBizService.clearRegionTreeCache();
    }
}
