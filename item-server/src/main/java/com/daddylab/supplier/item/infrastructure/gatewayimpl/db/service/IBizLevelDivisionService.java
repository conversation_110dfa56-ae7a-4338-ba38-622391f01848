package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.controller.item.dto.CorpBizListDto;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CascadedDivisionLevel;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.BizLevelDivision;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BizUnionTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 业务层级关联表 服务类
 *
 * <AUTHOR>
 * @since 2025-02-06
 */
public interface IBizLevelDivisionService extends IDaddyService<BizLevelDivision> {

    boolean containValue(@Param("type") BizUnionTypeEnum type, @Param("bizId") Long bizId, @Param("value") DivisionLevelValueEnum value);

    boolean saveCascadedLevels(BizUnionTypeEnum type, Long bizId, List<CascadedDivisionLevel> values, List<DivisionLevelEnum> divisionLevels);

    boolean saveCascadedLevels(BizUnionTypeEnum type, Long bizId, String bizCode, List<CascadedDivisionLevel> values, List<DivisionLevelEnum> divisionLevels);

    boolean savePlainLevels(BizUnionTypeEnum type, Long bizId, List<DivisionLevelValueEnum> values, List<DivisionLevelEnum> divisionLevels);

    boolean savePlainLevels(BizUnionTypeEnum type, Long bizId, String bizCode, List<DivisionLevelValueEnum> values, List<DivisionLevelEnum> divisionLevels);

    boolean updateBizCode(BizUnionTypeEnum type, Long bizId, String bizCode);

    List<CascadedDivisionLevel> getCascadedLevels(BizUnionTypeEnum type, Long bizId);

    Map<Long, List<CascadedDivisionLevel>> getCascadedLevels(BizUnionTypeEnum type, List<Long> bizIds);

    List<CorpBizTypeDTO> getCorpBizType(BizUnionTypeEnum type, Long bizId);

    Map<Long, List<CorpBizTypeDTO>> getCorpBizType(BizUnionTypeEnum type, List<Long> bizIds);

    Map<Long, Set<DivisionLevelValueEnum>> getCorpType(BizUnionTypeEnum type, List<Long> bizIds);

    List<BizLevelDivision> selectByTypeAndBizId(BizUnionTypeEnum type, Long bizId);

    List<BizLevelDivision> selectByTypeAndBizId(BizUnionTypeEnum type, Long bizId, Long parentId);

    List<BizLevelDivision> selectByTypeAndBizId(BizUnionTypeEnum type, Long bizId, Long parentId, List<DivisionLevelEnum> divisionLevels);

    List<BizLevelDivision> selectByTypeAndBizIds(BizUnionTypeEnum type, List<Long> bizIds);

    Map<Long, List<BizLevelDivision>> mapByBizIds(
            BizUnionTypeEnum bizUnionTypeEnum, Collection<Long> bizIds);

    /**
     * 查询后端商品的单品处理
     *
     * @param itemIds
     * @return
     */
    Map<Long, List<CorpBizTypeDTO>> queryByItemId(Collection<Long> itemIds);


    Map<String, List<CorpBizTypeDTO>> queryBySkuCode(Collection<String> skuCodes);

    Map<String, CorpBizListDto> queryByCombinationCode(Collection<String> skuCodes);


    Map<String, String> queryBizTypeBySkuCode(Collection<String> skuCodes);

    Map<Long, String> queryBizTypeByItemId(Collection<Long> itemIds);

    /**
     * 获取合作方
     *
     * @param bizId
     * @param bizCode
     * @param bizUnionTypeEnum
     * @return
     */
    Integer getCoryType(Long bizId, String bizCode, BizUnionTypeEnum bizUnionTypeEnum);


    /**
     * 数据转换处理
     *
     * @param size
     * @param stringListMap
     * @param <K>
     * @return
     */
    <K> Map<K, String> convertToStr(Integer size, Map<K, List<CorpBizTypeDTO>> stringListMap);

    // ------------------------- 历史数据清洗脚本 --------------------------

    void historyDataHandler();
}
