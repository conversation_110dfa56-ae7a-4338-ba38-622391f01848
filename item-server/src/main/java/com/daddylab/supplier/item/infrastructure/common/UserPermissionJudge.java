package com.daddylab.supplier.item.infrastructure.common;

import cn.hutool.core.collection.CollUtil;

import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.domain.auth.enums.ResourceType;
import com.daddylab.supplier.item.domain.auth.types.SimpleRole;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BusinessLine;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * init
 * log:2022-01-13:
 * 后端商品价格相关权限（1）
 * 1.查看后端商品价格权限（接口层面）
 * 2.编辑后端商品价格权限（特殊权限，代码逻辑特殊处理）
 * 3.判断用户是否有指定特殊编码的权限
 * 导出数据是否带价格和编辑后端商品价格权限挂钩
 * <p>
 * 组合商品价格权限 （2）
 * 1.编辑组合商品的价格权限  == 组合商品编辑权限
 * 2.查看组合商品的价格权限 （接口层面）
 * 3.单品的价格查看权限 == 查看后端商品价格权限（1.1）
 * 导出数据是否带价格权限和 查看组合商品的权限一致。
 * <p>
 * -----------------------------------------
 * <p>
 * log:
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/13 11:34 上午
 * @description 用户权限判定
 */
@Slf4j
public class UserPermissionJudge {

    /**
     * 编辑后端商品价格的特殊权限
     *
     * @return
     */
    public static Boolean canEditBackItemPrice() {
        return UserContext.hasPermission(GlobalConstant.SAVE_PRICE_RIGHT);
    }

    /**
     * 是否有指定 后端商品特殊编码和sku特殊编码的权限
     *
     * @return
     */
    public static Boolean canAssignBackItemCodeAndSkuCode() {
        return UserContext.hasPermission(GlobalConstant.SAVE_ASSIGN_CODE_RIGHT);
    }

    /**
     * 查看 后端商品的价格权限。
     *
     * @return
     */
    public static Boolean canViewBackItemPrice() {
        return UserContext.hasPermission(GlobalConstant.VIEW_ITEM_PRICE_RIGHT_URI, ResourceType.API);
    }


    /**
     * 查看 组合商品下 单品sku的价格 == canViewBackItemPrice
     *
     * @return
     */
    public static Boolean canViewComposerSkuPrice() {
        return canViewBackItemPrice();
    }

    /**
     * 查看组合商品的价格权限
     *
     * @return
     */
    public static Boolean canViewCombinationItemPrice() {
        return UserContext.hasPermission(GlobalConstant.VIEW_COMPOSE_SKU_PRICE_URI, ResourceType.API);
    }

    /**
     * 只有 系统管理员、财务主管、财务、采购主管、订单库存人员可查看所有采购单的
     *
     * @return
     */
    private static final List<String> LIST = new LinkedList<>();

    static {
        LIST.add("管理员");
        LIST.add("财务主管");
        LIST.add("财务");
        LIST.add("采购主管");
        LIST.add("订单库存管理员");
    }

    /**
     * 判断用户是否可以看到全部采购订单
     *
     * @return
     */
    public static Boolean canViewAllPurchaseOrder() {
        AtomicBoolean canView = new AtomicBoolean(false);
        Optional<UserContext.UserInfo> userInfo = UserContext.getUserInfo();
        userInfo.ifPresent(userInfo1 -> {
            if (CollectionUtils.isNotEmpty(userInfo1.getRoles())) {
                List<String> roles = userInfo1.getRoles().stream().map(SimpleRole::getRoleName).collect(Collectors.toList());
                canView.set(roles.stream().anyMatch(LIST::contains));
            }
            boolean showAll = UserContext.hasPermission(GlobalConstant.PURCHASE_ORDER_SHOW_ALL);
            canView.set(showAll);
        });
        return canView.get();
    }

    /**
     * 判断用户仅仅只包含一个角色，并且这个角色是财务主管
     *
     * @return
     */
    public static Boolean isFinancialExecutive() {
        AtomicBoolean match = new AtomicBoolean(false);
        Optional<UserContext.UserInfo> userInfo = UserContext.getUserInfo();
        userInfo.ifPresent(userInfo1 -> {
            if (CollectionUtils.isNotEmpty(userInfo1.getRoles())) {
                List<String> roles = userInfo1.getRoles().stream().map(SimpleRole::getRoleName).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(roles) && roles.size() == 1 && roles.stream().anyMatch(v -> v.contains("财务主管"))) {
                    match.set(true);
                }
            }
        });
        return match.get();
    }


    /**
     * 判断是否是采购员
     */
    public static Boolean isBuyer() {
        AtomicBoolean canView = new AtomicBoolean(false);
        Optional<UserContext.UserInfo> userInfo = UserContext.getUserInfo();
        userInfo.ifPresent(userInfo1 -> {
            if (CollectionUtils.isNotEmpty(userInfo1.getRoles())) {
                List<String> roles = userInfo1.getRoles().stream().map(SimpleRole::getRoleName).collect(Collectors.toList());
                if (roles.contains("采购")) {
                    canView.set(true);
                }
            }
        });
        return canView.get();
    }

    /**
     * 判断是否是产品负责人（运营）
     */
    public static Boolean isPrincipal() {
        return hasRole("运营") || hasRole("产品");
    }


    /**
     * 判断用户是否是法务
     *
     * @return
     */
    public static Boolean isLegal() {
        return hasPermission("法务");
    }


    /**
     * 是否拥有某个角色的权限
     *
     * @param roleName
     * @return
     */
    static boolean hasPermission(String roleName) {
        return UserContext.getUserInfo().map(info -> info.getRoles().stream().anyMatch(simpleRole1 ->
                simpleRole1.getRoleName().contains(roleName) || simpleRole1.getRoleName().contains("管理员"))).orElse(false);
    }

    public static boolean hasRole(String roleName) {
        return UserContext.getUserInfo().map(info -> info.getRoles().stream().anyMatch(simpleRole1 ->
                simpleRole1.getRoleName().contains(roleName) || simpleRole1.getRoleName().contains("管理员"))).orElse(false);
    }

    public static boolean isAdmin() {
        return UserContext.getUserInfo().map(info -> info.getRoles().stream().anyMatch(simpleRole1 ->
                simpleRole1.getRoleName().contains("管理员"))).orElse(false);
    }

    /**
     * 判断是否有api权限
     *
     * @param api
     * @return
     */
    public static boolean hasApiPermission(String api) {
        return isAdmin() || UserContext.getUserInfo().map(userInfo -> {
            List<String> apis = userInfo.getResourceCodes().get(ResourceType.API);
            if (apis.isEmpty()) {
                return false;
            }
            return apis.contains(api);
        }).orElse(false);
    }

    public static List<BusinessLine> getUserBusinessLine() {
        return UserContext.getUserInfo().map(userInfo -> {
            List<BusinessLine> ll = new LinkedList<>();
            boolean b1 = userInfo.hasPermission("/" + BusinessLine.DIAN_SHANG.name(), ResourceType.API);
            if (b1) {
                ll.add(BusinessLine.DIAN_SHANG);
            }
//            boolean b2 = userInfo.hasPermission("/" + BusinessLine.CHOU_JIAN.name(), ResourceType.API);
//            if (b2) {
//                ll.add(BusinessLine.DIAN_SHANG);
//            }
            boolean b3 = userInfo.hasPermission("/" + BusinessLine.LV_SE_JIA_ZHUANG.name(), ResourceType.API);
            if (b3) {
                ll.add(BusinessLine.LV_SE_JIA_ZHUANG);
            }
//            boolean b4 = userInfo.hasPermission("/" + BusinessLine.SHANG_JIA_RU_ZHU.name(), ResourceType.API);
//            if (b4) {
//                ll.add(BusinessLine.DIAN_SHANG);
//            }
            return ll.stream().distinct().collect(Collectors.toList());
        }).orElse(new ArrayList<>());
    }

    public static List<Integer> getUserBusinessLineValues() {
        return getUserBusinessLine().stream().map(BusinessLine::getValue).collect(Collectors.toList());
    }

    public static List<Integer> filterBusinessLinePerm(Collection<Integer> businessLine) {
        if (businessLine == null) {
            businessLine = new ArrayList<>();
        }

        List<Integer> userBusinessLineValues = getUserBusinessLineValues();
        if (userBusinessLineValues.isEmpty()) {
            businessLine.clear();
        } else if (CollUtil.isEmpty(businessLine)) {
            businessLine.addAll(userBusinessLineValues);
        } else {
            final List<Integer> filteredLines =
                    businessLine.stream()
                            .filter(userBusinessLineValues::contains)
                            .collect(Collectors.toList());
            businessLine.clear();
            businessLine.addAll(filteredLines);
        }
        return (List<Integer>) businessLine;
    }

    public static List<Integer> filterCorpType(Collection<Integer> corpType) {
        if (corpType == null) {
            corpType = new ArrayList<>();
        }

        final List<Integer> divisionLevelPermissions = getDivisionLevelPermissions().stream()
                                                                                    .map(DivisionLevelValueEnum::getValue)
                                                                                    .collect(
                                                                                            Collectors.toList());
        if (divisionLevelPermissions.isEmpty()) {
            corpType.clear();
        } else if (CollUtil.isEmpty(corpType)) {
            corpType.addAll(divisionLevelPermissions);
        } else {
            final List<Integer> filteredLines =
                    corpType.stream()
                                .filter(divisionLevelPermissions::contains)
                                .collect(Collectors.toList());
            corpType.clear();
            corpType.addAll(filteredLines);
        }
        return (List<Integer>) corpType;
    }


    public static Set<DivisionLevelValueEnum> getDivisionLevelPermissions() {
        return UserContext.getUserInfo().map(userInfo -> {
            Set<DivisionLevelValueEnum> permValues = new TreeSet<>();
            boolean legacyEcommercePerm = userInfo.hasPermission("/" + BusinessLine.DIAN_SHANG.name(), ResourceType.API);
            boolean legacyChouJianPerm = userInfo.hasPermission("/" + BusinessLine.CHOU_JIAN.name(), ResourceType.API);
            boolean legacySjrzPerm = userInfo.hasPermission("/" + BusinessLine.SHANG_JIA_RU_ZHU.name(), ResourceType.API);
            if (legacyEcommercePerm || legacyChouJianPerm || legacySjrzPerm) {
                permValues.add(DivisionLevelValueEnum.C_E_COMMERCE);
            }
            boolean legacyLsjzPerm = userInfo.hasPermission("/" + BusinessLine.LV_SE_JIA_ZHUANG.name(), ResourceType.API);
            if (legacyLsjzPerm) {
                permValues.add(DivisionLevelValueEnum.C_DECORATION);
            }
            for (DivisionLevelValueEnum valueEnum : DivisionLevelValueEnum.values()) {
                if (userInfo.hasPermission(valueEnum.name(), ResourceType.API)) {
                    permValues.add(valueEnum);
                }
            }
            return permValues;
        }).orElseGet(TreeSet::new);
    }
    
    public static List<Integer> getCorpTypePermissions() {
        return getDivisionLevelPermissions().stream()
                .filter(v -> v.getDivisionLevel() == DivisionLevelEnum.COOPERATION)
                .map(DivisionLevelValueEnum::getValue)
                .collect(Collectors.toList());
    }

    public static boolean canViewHedge(){
        return UserPermissionJudge.isAdmin() || UserPermissionJudge.hasRole("财务");
    }



    public static void main(String[] args) {
//        List<Integer> userBusinessLineValues = new ArrayList<>();
//        userBusinessLineValues.add(2);
//        ll.addAll(userBusinessLineValues);
//        System.out.println(JsonUtil.toJson(ll));
        List<Integer> param = new ArrayList<>();
        param.add(0);
        Collection<Integer> integers = filterBusinessLinePerm(param);
        System.out.println("res:" + JsonUtil.toJson(integers));
    }

}
