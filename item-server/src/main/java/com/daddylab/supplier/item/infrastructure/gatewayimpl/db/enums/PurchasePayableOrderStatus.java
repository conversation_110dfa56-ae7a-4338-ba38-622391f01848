package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PurchasePayableOrderStatus implements IEnum<Integer> {

    /**
     * 消除黄线
     */
    DEFAULT(0, "默认/正常"),
    WRITE_OFF(1, "原单据作废（冲销流程触发）"),
    REVERSE_FIXED(2, "冲销逆向单据"),
    FIXED_ORDER(3,"冲销,新生成的修正的单据")
    ;

    @EnumValue
    private final Integer value;
    private final String desc;
}
