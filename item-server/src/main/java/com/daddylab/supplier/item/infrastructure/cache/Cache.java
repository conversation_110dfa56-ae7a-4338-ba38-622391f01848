package com.daddylab.supplier.item.infrastructure.cache;

import cn.hutool.crypto.digest.DigestUtil;

import lombok.AllArgsConstructor;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Cache {
    /**
     * 缓存键前缀
     */
    String keyPrefix() default "";

    /**
     * 定义拼接缓存键的时候如何将参数对象序列化
     */
    KeySerialize keySerialize() default KeySerialize.TO_STRING;

    /**
     * 缓存超时时间
     */
    long timeout();

    /**
     * 缓存超时时间单位（默认为秒）
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /**
     * 缓存类型
     */
    CacheType cacheType() default CacheType.REDIS;

    @AllArgsConstructor
    enum KeySerialize {
        /**
         * 空
         */
        EMPTY(v -> ""),

        /**
         * 调用对象的toString()方法
         */
        TO_STRING(Object::toString),

        /**
         * 先toString(), 然后哈希
         */
        TO_STRING_HASH(o -> DigestUtil.md5Hex(o.toString())),
        ;

        final Function<Object, String> transformer;

        String apply(Object object) {
            return transformer.apply(object);
        }
    }

    enum CacheType {
        /**
         * Redis
         */
        REDIS,

        /**
         * Caffeine
         */
        CAFFEINE,
    }
}
