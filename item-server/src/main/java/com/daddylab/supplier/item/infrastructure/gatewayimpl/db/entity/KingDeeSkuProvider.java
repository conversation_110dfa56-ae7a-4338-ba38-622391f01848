package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class KingDeeSkuProvider implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * erp skuCode
     */
    private String skuCode;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * erp供应商编号 == KingDee供应商Number
     */
    private String providerNo;


}
