package com.daddylab.supplier.item.infrastructure.kingdee.util;

import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.config.KingDeeConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.KingDeeFeignClient;
import com.daddylab.supplier.item.infrastructure.kingdee.dto.*;
import com.daddylab.supplier.item.infrastructure.kingdee.req.SkuPriceCategoryReq;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> up
 * @date 2022/3/21 1:42 下午
 */
@Slf4j
@Service
public class ReqTemplateImpl implements ReqTemplate {

    @Autowired
    KingDeeFeignClient kingDeeFeignClient;

    private static final String SESSION = "kingDee_session";

    /**
     * 获取 金蝶的登录session
     *
     * @return
     */
    public String getSessionId() {
        final String s = RedisUtil.get(SESSION);
        if (StringUtil.isNotBlank(s)) {
            return s;
        }

        final KingDeeConfig kingDeeConfig = SpringUtil.getBean("kingDeeConfig", KingDeeConfig.class);

        long timestamp = System.currentTimeMillis() / 1000;
        String dbId = kingDeeConfig.getAcctId();
        String userName = kingDeeConfig.getUserName();
        String appId = kingDeeConfig.getAppId();
        String appSecret = kingDeeConfig.getAppSec();
        String[] arr = new String[]{dbId, userName, appId, appSecret, String.valueOf(timestamp)};
        String sign;
        try {
            sign = getSha256(arr);
        } catch (NoSuchAlgorithmException e) {
            log.error("金蝶登录请求参数加密失败", e);
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "金蝶登录请求参数加密失败");
        }
        Object[] loginInfo = new Object[]{dbId, userName, appId, String.valueOf(timestamp), sign, 2052};
        Map<String, Object> params = new HashMap<>();
        params.put("parameters", loginInfo);
        String loginResult = kingDeeFeignClient.loginBySign(JsonUtil.toJson(params));

//        Map<String, Object> loginReq = new HashMap<>(4);
//        loginReq.put("acctID", kingDeeConfig.getAcctId());
//        loginReq.put("username", kingDeeConfig.getUserName());
//        loginReq.put("password", kingDeeConfig.getPassword());
//        loginReq.put("lcid", kingDeeConfig.getLcId());
//        final String loginResult = kingDeeFeignClient.login(JsonUtil.toJson(loginReq));

        Map map = JsonUtil.parseJsonStr(loginResult, Map.class);
        final Object sessionId = MapUtils.getObject(map, "KDSVCSessionId");

        if (Objects.isNull(sessionId)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "获取金蝶登录session失败");
        }

        final String s1 = sessionId.toString();
        log.info("--------------- kingDee sessionId-------------:{}", s1);
        RedisUtil.set(SESSION, s1, 60, TimeUnit.MINUTES);
        return s1;
    }


    private static String getSha256(String[] input) throws NoSuchAlgorithmException {
        Arrays.sort(input);
        //SHA1加密的话改成MessageDigest.getInstance("SHA-1");
        MessageDigest sha256 = MessageDigest.getInstance("SHA-256");
        for (String str : input) {
            sha256.update(str.getBytes(StandardCharsets.UTF_8));
        }
        byte[] hashBytes = sha256.digest();
        StringBuilder hashString = new StringBuilder();
        for (byte b : hashBytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hashString.append('0');
            }
            hashString.append(hex);
        }
        return hashString.toString();
    }


    private KingDeeResp parseResultStr(String resultStr) throws Exception {
        KingDeeResp resp = new KingDeeResp();

        final JsonNode jsonNode = new ObjectMapper().readTree(resultStr);
        final JsonNode result = jsonNode.get("Result");
        final JsonNode responseStatus = result.get("ResponseStatus");

        verify(responseStatus);

        final JsonNode successEntity = responseStatus.get("SuccessEntitys");
        if (successEntity.isArray()) {
            final JsonNode infoNode = successEntity.get(0);
            resp.setId(Optional.of(String.valueOf(infoNode.get("Id").asLong())).orElse(""));
            resp.setNumber(Optional.ofNullable(infoNode.get("Number").asText()).orElse(""));
        }
        return resp;
    }

    private void verify(JsonNode responseStatus) {
        final JsonNode errors = responseStatus.get("Errors");
        if (errors.isArray()) {
            if (!errors.isEmpty()) {
                throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "金蝶业务异常," + JsonUtil.toJson(errors));
            }
        }
    }

    @Override
    public Boolean unAudit(String reqJson) throws Exception {
        String resp = kingDeeFeignClient.unAudit(reqJson, getSessionId());
        parseResultStr(resp);
        return true;
    }

    @Override
    public Boolean submit(String reqJson) throws Exception {
        String resp = kingDeeFeignClient.sumbit(reqJson, getSessionId());
        parseResultStr(resp);
        return true;

    }

    @Override
    public Boolean aduit(String reqJson) throws Exception {
        String resp = kingDeeFeignClient.audit(reqJson, getSessionId());
        parseResultStr(resp);
        return true;

    }

    @Override
    public Boolean delete(String reqJson) throws Exception {
        String resp = kingDeeFeignClient.delete(reqJson, getSessionId());
        parseResultStr(resp);
        return true;
    }

    @Override
    public String groupSave(String reqJson) throws Exception {
        String resultStr = kingDeeFeignClient.saveGroup(reqJson, getSessionId());
        return parseResultStr(resultStr).getId();
    }

    @Override
    public Boolean groupDelete(String reqJson) throws Exception {
        String s = kingDeeFeignClient.deleteGroup(reqJson, getSessionId());
        parseResultStr(s);
        return true;
    }

    @Override
    public String save(String reqJson) throws Exception {
        String resp = kingDeeFeignClient.save(reqJson, getSessionId());
        return parseResultStr(resp).getId();
    }

    @Override
    public String save2(String reqJson) throws Exception {
        return kingDeeFeignClient.save(reqJson, getSessionId());
    }


    @Override
    public KingDeeResp saveWithRes(String reqJson) throws Exception {
        String resp = kingDeeFeignClient.save(reqJson, getSessionId());
        return parseResultStr(resp);
    }

    @Override
    public List<KingDeeSkuResp> querySkuList(Long startRow, Long limit) throws Exception {
        String querySkuJson = ReqJsonUtil.getQuerySkuJson(startRow, limit);
        String resp = kingDeeFeignClient.queryBill(querySkuJson, getSessionId());

        List<KingDeeSkuResp> list = new LinkedList<>();
        final JsonNode jsonNode = new ObjectMapper().readTree(resp);
        if (jsonNode.isArray()) {
            for (JsonNode node : jsonNode) {
                KingDeeSkuResp sku = new KingDeeSkuResp();
                sku.setCode(node.get(0).asText());
                sku.setName(node.get(1).asText());
                sku.setBuyerKingDeeId(node.get(2).asLong());
                list.add(sku);
            }
        }
        return list;
    }

    @Override
    public List<KingDeeSkuResp> queryZeroStockCostPriceSkuList(Long startRow, Long limit) throws Exception {
        String querySkuJson = ReqJsonUtil.getZeroStockCostPriceSku(startRow, limit);
        String resp = kingDeeFeignClient.queryBill(querySkuJson, getSessionId());

        List<KingDeeSkuResp> list = new LinkedList<>();
        final JsonNode jsonNode = new ObjectMapper().readTree(resp);
        if (jsonNode.isArray()) {
            for (JsonNode node : jsonNode) {
                KingDeeSkuResp sku = new KingDeeSkuResp();
                sku.setCode(node.get(0).asText());
                sku.setCostPrice(node.get(1).asText());
                list.add(sku);
            }
        }
        return list;
    }

    @Override
    public List<KingDeeSkuResp> querySkuProviderList(Long startRow, Long limit) throws Exception {
        String querySkuJson = ReqJsonUtil.getQuerySkuProviderJson(startRow, limit);
        String resp = kingDeeFeignClient.queryBill(querySkuJson, getSessionId());

        List<KingDeeSkuResp> list = new LinkedList<>();
        final JsonNode jsonNode = new ObjectMapper().readTree(resp);
        if (jsonNode.isArray()) {
            for (JsonNode node : jsonNode) {
                KingDeeSkuResp sku = new KingDeeSkuResp();
                sku.setCode(node.get(0).asText());
                sku.setName(node.get(1).asText());
                sku.setProviderNo(node.get(2).asText());
                list.add(sku);
            }
        }
        return list;
    }

    @Override
    public Optional<KingDeeSkuResp> querySkuByNo(String skuCodeNo) {
        String reqJson = ReqJsonUtil.getQuerySkuByNoJson(skuCodeNo);
        String resp = kingDeeFeignClient.queryBill(reqJson, getSessionId());

        System.out.printf(resp);

        if (StringUtil.isNotBlank(resp) && !"[]".equals(resp)) {
            KingDeeSkuResp res = new KingDeeSkuResp();
            resp = resp.replace("[", "");
            resp = resp.replaceAll("]", "");
            String[] split = resp.split(",");
            res.setCode(split[0].replaceAll("\"", ""));
            res.setName(split[1].replaceAll("\"", ""));
            res.setId(split[2]);
            return Optional.of(res);
        }
        return Optional.empty();
    }

    @Override
    public String groupInfo(String reqJson) throws Exception {
        String resp = kingDeeFeignClient.queryGroupInfo(reqJson, getSessionId());
        log.info("resp:{}", resp);
        return null;
    }

    @Override
    public Optional<KingDeeProviderResp> queryProvider(String providerName) {
        String reqJson = ReqJsonUtil.getQueryProvider(providerName);
        String resp = kingDeeFeignClient.queryBill(reqJson, getSessionId());

        // [["G00S05948","测试000115",107312]]
        if (StringUtil.isNotBlank(resp) && !"[]".equals(resp)) {
            KingDeeProviderResp res = new KingDeeProviderResp();
            resp = resp.replace("[", "");
            resp = resp.replaceAll("]", "");
            String[] split = resp.split(",");
            res.setKingDeeNo(split[0].replaceAll("\"", ""));
            res.setKingDeeId(split[1]);
            return Optional.of(res);
        }
        return Optional.empty();
    }

    @Override
    public Boolean warehouseExist(String warehouseNo) {
        String reqJson = ReqJsonUtil.getQueryWarehouse(warehouseNo);
        String resp = kingDeeFeignClient.queryBill(reqJson, getSessionId());
        return !StringUtil.isNotBlank(resp) || !"[]".equals(resp);
    }

    @Override
    public Boolean saveWarehouse(String name, String no) throws Exception {
        String reqJson = ReqJsonUtil.getSaveWarehouse(name, no);
        String resp = kingDeeFeignClient.save(reqJson, getSessionId());

        parseResultStr(resp);
        return true;
    }

    @Override
    public Boolean updateSkuProvider(String skuKingDeeId, String providerNo) throws Exception {
        String reqJson = ReqJsonUtil.onlyUpdateSkuProvider(skuKingDeeId, providerNo);
        String resp = kingDeeFeignClient.save(reqJson, getSessionId());
        parseResultStr(resp);
        return true;
    }

    @Override
    public List<KingDeeStockInOrderResp> queryStockInOrderList(String dateStr) throws Exception {
        String reqJson = ReqJsonUtil.getStockInOrderByTime(dateStr);
        String resp = kingDeeFeignClient.queryBill(reqJson, getSessionId());

        System.out.println(resp);

        // [["CGRK2022061601TE","C",100146],["CGRK2022061602TE","C",100147]]
        List<KingDeeStockInOrderResp> list = new LinkedList<>();
        final JsonNode jsonNode = new ObjectMapper().readTree(resp);
        if (jsonNode.isArray()) {
            for (JsonNode node : jsonNode) {
                if ("C".equals(node.get(1).asText())) {
                    KingDeeStockInOrderResp res = new KingDeeStockInOrderResp();
                    res.setCode(node.get(0).asText());
                    res.setState(node.get(1).asText());
                    res.setId(node.get(2).asText());
                    list.add(res);
                }
            }
        }
        return list;
    }

    @Override
    public Optional<KingDeeStockInOrderResp> queryStockInOrderByNo(String billNo) {
        String reqJson = ReqJsonUtil.getStockInOrderByBillNo(billNo);
        String resp = kingDeeFeignClient.queryBill(reqJson, getSessionId());

        // [["CGRK2022061403TE","C",107340]]
        if (StringUtil.isNotBlank(resp) && !"[]".equals(resp)) {
            KingDeeStockInOrderResp res = new KingDeeStockInOrderResp();
            resp = resp.replace("[", "");
            resp = resp.replaceAll("]", "");
            String[] split = resp.split(",");
            res.setCode(split[0].replaceAll("\"", ""));
            res.setId(split[2]);
            return Optional.of(res);
        }
        return Optional.empty();
    }

    @Override
    public Optional<KingDeeStockOutOrderResp> queryStockOutOrderByNo(String orderNo) {
        String reqJson = ReqJsonUtil.getStockOutOrderByBillNo(orderNo);
        String resp = kingDeeFeignClient.queryBill(reqJson, getSessionId());

        System.out.println(resp);

        // [["CGRK2022061403TE","C",107340]]
        if (StringUtil.isNotBlank(resp) && !"[]".equals(resp)) {
            KingDeeStockOutOrderResp res = new KingDeeStockOutOrderResp();
            resp = resp.replace("[", "");
            resp = resp.replaceAll("]", "");
            String[] split = resp.split(",");
            res.setCode(split[0].replaceAll("\"", ""));
            res.setId(split[2]);
            return Optional.of(res);
        }
        return Optional.empty();
    }

    @Override
    public String querySkuUnit(String skuCode) {
        String reqJson = ReqJsonUtil.querySkuKingDeeInfo(skuCode);
        String resp = kingDeeFeignClient.queryBill(reqJson, getSessionId());
        // [[100532]]

        if (StringUtil.isNotBlank(resp) && !"[]".equals(resp)) {
            try {
                return extractData(resp);
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }


    @Override
    public SkuPriceCategoryReq querySkuPriceCategory(String skuKingDeeId) {
        String reqJson = ReqJsonUtil.querySkuPriceCategoryReqJson(skuKingDeeId);
        String resp = kingDeeFeignClient.queryBill(reqJson, getSessionId());
        if (StringUtil.isNotBlank(resp) && !"[]".equals(resp)) {
            resp = resp.replace("[", "");
            resp = resp.replaceAll("]", "");
            resp = resp.replaceAll("\\\\", "").replaceAll("\"", "");
            String[] split = resp.split(",");
            SkuPriceCategoryReq req = new SkuPriceCategoryReq();
            req.setBillNo(split[0]);
            req.setKingDeeId(split[1]);
            req.setPrice(new BigDecimal(split[2]));
            req.setTaxRate(new BigDecimal(split[3]));
            return req;
        }
        return null;
    }

    @Override
    public Boolean deleteSkuPriceCategory(String billNo) throws Exception {
        String reqJson = ReqJsonUtil.deleteSkuPriceCategory(billNo);
        String resp = kingDeeFeignClient.delete(reqJson, getSessionId());
        parseResultStr(resp);
        return true;
    }

    @Override
    public Boolean unAuditSkuPriceCategory(String billNo) throws Exception {
        String reqJson = ReqJsonUtil.unAuditSkuPriceCategory(billNo);
        String resp = kingDeeFeignClient.unAudit(reqJson, getSessionId());
        parseResultStr(resp);
        return true;
    }

    public static String extractData(String str) {
        String regex = "[\\d]+";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        StringBuilder sb = new StringBuilder();
        while (matcher.find()) {
            sb.append(matcher.group());
        }
        return sb.toString();
    }

//    public static void main(String[] args) {
//        String resp = "[[\"CGJM000001\",896201,8.2000000000,3.000000]]";
//        if (StringUtil.isNotBlank(resp) && !"[]".equals(resp)) {
//            resp = resp.replace("[", "");
//            resp = resp.replaceAll("]", "");
//            resp = resp.replaceAll("\\\\", "").replaceAll("\"", "");
//            System.out.println(resp);
//
//            String[] split = resp.split(",");
//            SkuPriceCategoryReq req = new SkuPriceCategoryReq();
//            req.setBillNo(split[0]);
//            req.setKingDeeId(split[1]);
//            req.setPrice(new BigDecimal(split[2]));
//            req.setTaxRate(new BigDecimal(split[3]));
//            System.out.println(JsonUtil.toJson(req));
//
//        }

//        String res = "[[\"105801201010000348\",\"内蒙古银行股份有限公司兴安盟分行营业部\"]]";
//        String data = extractData(res);
//        System.out.println(data);
//    }

    @Override
    public Map<String, String> queryProviderBankInfo(String providerNo) throws Exception {
        String queryProviderBankInfo = ReqJsonUtil.getQueryProviderBankInfo(providerNo);
        String resp = kingDeeFeignClient.queryBill(queryProviderBankInfo, getSessionId());

        Map<String, String> map = new HashMap<>(2);

        if (StringUtil.isNotBlank(resp) && !"[]".equals(resp)) {
            KingDeeStockInOrderResp res = new KingDeeStockInOrderResp();
            resp = resp.replace("[", "");
            resp = resp.replaceAll("]", "");
            String[] split = resp.split(",");
            if (split.length == 2) {
                map.put("bankCode", split[0].replaceAll("\"", ""));
                map.put("bankName", split[1].replaceAll("\"", ""));
            }
        }

        return map;
    }

    @Override
    public SubmitAndAuditRes submitAndAuditStockOrder(String formId, List<String> nos) {
        SubmitAndAuditRes res = new SubmitAndAuditRes();

        try {
            String submitParam = ReqJsonUtil.submitStockOrder(nos, formId);
            log.info("submitAndAuditStockOrder param:{}", submitParam);
            String submitResJson = kingDeeFeignClient.sumbit(submitParam, getSessionId());
            log.info("submitAndAuditStockOrder submitRes:{},nos:{}", submitResJson, JsonUtil.toJson(nos));
            Boolean submitSuccess = resolveSubmitAndAuditResJson(submitResJson);

            if (submitSuccess) {
                String auditParam = ReqJsonUtil.auditStockOrder(nos, formId);
                String auditResJson = kingDeeFeignClient.audit(auditParam, getSessionId());
                log.info("submitAndAuditStockOrder auditRes:{},nos:{}", auditResJson, JsonUtil.toJson(nos));
                Boolean auditSuccess = resolveSubmitAndAuditResJson(auditResJson);
                if (auditSuccess) {
                    res.setIsSuccess(true);
                    res.setMsg("提交审核完成");
                } else {
                    res.setIsSuccess(false);
                    res.setMsg("提交成功，审核失败,[" + auditResJson + "]");
                }
            } else {
                res.setIsSuccess(false);
                res.setMsg("提交失败,[" + submitResJson + "]");
            }
        } catch (Exception e) {
            log.error("提交审核流程异常.no:{}", JsonUtil.toJson(nos), e);
            res.setIsSuccess(false);
            res.setMsg("提交审核异常." + e.getMessage());
        }

        return res;
    }

    /**
     * {
     * "Result": {
     * "ResponseStatus": {
     * "ErrorCode": 500,
     * "IsSuccess": false,
     * "Errors": [
     * {
     * "FieldName": null,
     * "Message": "编码值或内码值必须传其一",
     * "DIndex": 0
     * }
     * ],
     * "SuccessEntitys": [],
     * "SuccessMessages": [],
     * "MsgCode": 8
     * }
     * }
     * }
     *
     * @param resJson
     * @return
     */
    private Boolean resolveSubmitAndAuditResJson(String resJson) {
        JsonNode parse = JsonUtil.parse(resJson);
        assert parse != null;
        JsonNode result = parse.get("Result");
        JsonNode responseStatus = result.get("ResponseStatus");
        return responseStatus.get("IsSuccess").asBoolean(false);
    }
}
