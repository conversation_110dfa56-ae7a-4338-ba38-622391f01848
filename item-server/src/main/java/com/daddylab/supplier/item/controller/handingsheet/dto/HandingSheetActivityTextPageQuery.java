package com.daddylab.supplier.item.controller.handingsheet.dto;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/24 14:04
 * @Description: 店铺活动富文本内容分页查询参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("店铺活动富文本内容分页查询参数")
public class HandingSheetActivityTextPageQuery extends PageQuery {
    @ApiModelProperty("盘货表ID")
    private Long handingSheetId;
}
