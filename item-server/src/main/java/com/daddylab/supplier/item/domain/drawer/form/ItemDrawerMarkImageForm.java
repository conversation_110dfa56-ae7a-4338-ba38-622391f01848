package com.daddylab.supplier.item.domain.drawer.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * Class  ItemDrawerForm
 *
 * @Date 2022/5/31下午4:18
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ItemDrawerMarkImageForm", description = "商品抽屉标记图片Form")
public class ItemDrawerMarkImageForm implements Serializable {

    private static final long serialVersionUID = 51124111497446252L;
    @ApiModelProperty(value = "标记id(新增时不传)")
    private Long id;

    @NotNull(message = "抽屉id不能为空")
    @ApiModelProperty(value = "抽屉id")
    private Long drawerId;

    /**
     * 拟废弃，兼容性考虑，暂时保留
     */
    @ApiModelProperty(value = "合成图片地址")
    private String url;

    /**
     * 标注元数据
     */
    @ApiModelProperty(value = "标注元数据")
    private String markMeta;

    @Valid
    @NotEmpty(message = "标注内容不能为空")
    @ApiModelProperty(value = "标注内容")
    private List<ItemDrawerMarkImageContentForm> itemDrawerMarkImageContents;
}
