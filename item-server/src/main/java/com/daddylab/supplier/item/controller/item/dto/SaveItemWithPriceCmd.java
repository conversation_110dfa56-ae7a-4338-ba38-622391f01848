package com.daddylab.supplier.item.controller.item.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/21 2:58 下午
 * @description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveItemWithPriceCmd extends Command {

    private static final long serialVersionUID = -7064794285716674035L;

    @Valid
    private SaveItemCmd saveItemCmd;

    @ApiModelProperty("采购成本list")
    @Valid
    List<ProcurementPriceDto> procurementPrices;

    @ApiModelProperty("日常售价list")
    @Valid
    List<SalesPriceDto> salesPrices;
}
