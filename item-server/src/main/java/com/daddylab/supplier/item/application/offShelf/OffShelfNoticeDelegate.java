package com.daddylab.supplier.item.application.offShelf;

import com.daddylab.supplier.item.types.process.ProcessBusinessType;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
@Service
@Slf4j
public class OffShelfNoticeDelegate implements JavaDelegate {
    @Resource
    OffShelfBizService offShelfBizService;

    @Override
    public void execute(DelegateExecution execution) {
        log.info("下架流程事件|通知代理:{}",
                 Arrays.toString(new Object[]{execution.getProcessInstanceId(), execution.getEventName(), execution.getCurrentActivityId(), execution.getProcessInstanceBusinessKey()}));

        ProcessBusinessType.OFF_SHELF.getBusinessId(execution.getProcessInstanceBusinessKey()).ifPresent(id -> {
            if ("noticeApplyTask".equals(execution.getCurrentActivityId())) {
                offShelfBizService.noticeToApprove(id);
            }
            if ("noticeFinishTask".equals(execution.getCurrentActivityId())) {
                offShelfBizService.noticeFinished(id);
            }
        });

    }
}
