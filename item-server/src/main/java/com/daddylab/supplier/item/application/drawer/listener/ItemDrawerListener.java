package com.daddylab.supplier.item.application.drawer.listener;

import com.daddylab.supplier.item.application.drawer.ItemDrawerMergeBizService;
import com.daddylab.supplier.item.application.drawer.events.ItemDrawerMergeEvent;
import com.daddylab.supplier.item.infrastructure.events.SaveEvent;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerLiveVerbal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @class ItemDrawerListener.java
 * @description 描述类的作用
 * @date 2024-04-09 15:15
 */
@Slf4j
@Component
public class ItemDrawerListener {

    @Autowired
    private ItemDrawerMergeBizService itemDrawerMergeBizService;

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void save(SaveEvent<ItemDrawer> saveEvent) {
        log.info("[抽屉变更事件] 抽屉变更事件触发，model={}", saveEvent.getModel());
    }


    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void copy(ItemDrawerMergeEvent itemDrawerMergeEvent) {
        log.info("[抽屉变更事件] 抽屉复制数据逻辑出发，model={}", itemDrawerMergeEvent);
        itemDrawerMergeBizService.doCopy(itemDrawerMergeEvent.getItemDrawerMergeId());
    }
}
