package com.daddylab.supplier.item.infrastructure.third.mallItem.domain.dto;

import com.daddylab.supplier.item.infrastructure.third.enums.EventTypeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @class MallItemChangeDTO.java
 * @description 描述类的作用
 * @date 2024-03-06 10:00
 */
@NoArgsConstructor
@Data
public class MallItemChangeDTO implements Serializable {
    private static final long serialVersionUID = 7684014826685483544L;
    /**
     * type
     */
    private EventTypeEnum type;
    /**
     * 变更时间
     */
    private Long time;
    /**
     * 商品ID
     */
    @JsonProperty("item_id")
    private Long itemId;
    /**
     * skuIds
     */
    @JsonProperty("sku_ids")
    private List<Long> skuIds;
}
