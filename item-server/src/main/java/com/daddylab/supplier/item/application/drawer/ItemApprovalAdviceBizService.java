package com.daddylab.supplier.item.application.drawer;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.domain.drawer.vo.ItemApprovalAdvicesVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemApprovalAdviceType;

/**
 * <AUTHOR>
 * @since 2022/10/28
 */
public interface ItemApprovalAdviceBizService {

    /**
     * 新增审批意见
     *
     * @param itemId  商品ID
     * @param type    审批意见类型
     * @param content 意见内容
     */
    SingleResponse<Long> add(Long itemId, ItemApprovalAdviceType type, String content);

    /**
     * 移除审批意见
     *
     * @param adviceId 审批意见ID
     */
    Response remove(Long adviceId);

    /**
     * 查询商品审批意见视图模型
     * @param itemId 商品ID
     */
    SingleResponse<ItemApprovalAdvicesVO> getAllByItemId(Long itemId);

    /**
     * 删除某个商品全部的审批意见
     *
     * @param itemId 商品ID
     */
    Response deleteAllByItemId(Long itemId);
}
