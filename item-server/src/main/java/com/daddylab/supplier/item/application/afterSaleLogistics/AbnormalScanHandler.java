package com.daddylab.supplier.item.application.afterSaleLogistics;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSaleLogistics.domain.LogisticsTraceData;
import com.daddylab.supplier.item.application.afterSaleLogistics.domain.LogisticsTraceItem;
import com.daddylab.supplier.item.application.afterSaleLogistics.dto.LogisticExceptionRes;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.AbnormalStatus;
import com.daddylab.supplier.item.application.afterSaleLogistics.enums.ErpLogisticsStatus;
import com.daddylab.supplier.item.application.afterSaleLogistics.monitor.ProcessContext;
import com.daddylab.supplier.item.common.ResponseAssert;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsAbnormality;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsTrace;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.CloseReasonType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OrderLogisticsTraceStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsAbnormalityService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsTraceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtRefundOrderDetailService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtRefundOrderService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/12/31
 */
@Service
@Slf4j
public class AbnormalScanHandler {

  @Resource private IOrderLogisticsAbnormalityService abnormalityService;

  @Autowired private RedissonClient redissonClient;

  @Autowired private LogisticsExceptionBizService logisticsExceptionBizService;

  @Autowired @Lazy private AfterSaleLogisticsBizService afterSaleLogisticsBizService;

  @Autowired private AfterSaleLogisticsConfig afterSaleLogisticsConfig;

  @Autowired IOrderLogisticsTraceService orderLogisticsTraceService;

  @Autowired private AfterSaleLogisticsConfig config;

  @Autowired private IWdtRefundOrderService wdtRefundOrderService;

  public void handle(OrderLogisticsTrace trace) {
    final long currentTime = DateUtil.currentTime();
    final long trackTimeoutTime = currentTime - config.getTrackTimeout();
    if (trace.getCloseReason() != null && trace.getCloseReason() != CloseReasonType.NONE) {
      closeAbnormalTrace(trace, trace.getCloseReason());
      return;
    }
    if (NumberUtil.isPositive(trace.getPayTime()) && trace.getPayTime() < trackTimeoutTime
        || trace.getTrackTime() < trackTimeoutTime) {
      closeAbnormalTrace(trace, CloseReasonType.HANDLE_TIMEOUT);
      return;
    }
    if (trace.getLogisticsName() != null
        && Stream.of("顺丰", "丰网").anyMatch(trace.getLogisticsName()::contains)) {
      closeAbnormalTrace(trace, CloseReasonType.SF);
      return;
    }
    if (StringUtil.isNotBlank(trace.getStockoutWarehouseNo())
        && config.getBondedWarehouseCode() != null
        && config.getBondedWarehouseCode().contains(trace.getStockoutWarehouseNo())) {
      closeAbnormalTrace(trace, CloseReasonType.WAREHOUSE_FILTER);
      return;
    }

    // 在【旺店通】中是否存在状态非【已取消】退换管理订单，则直接关闭
    final List<WdtRefundOrder> wdtRefundOrders = wdtRefundOrderService.listByTid(trace.getSrcOrderNo());
    final boolean isRefundOrderExits = wdtRefundOrders.stream().anyMatch(it -> it.getStatus() != 10);
    if (isRefundOrderExits) {
      closeAbnormalTrace(trace, CloseReasonType.ORDER_REFUND);
      return;
    }

    LogisticsTraceData logisticsTraceData = null;
    boolean hasCallback = trace.getCallbackId() != null && trace.getCallbackId() > 0;
    if (!hasCallback) {
      hasCallback = afterSaleLogisticsBizService.matchLogisticsTrace(trace);
    }
    if (hasCallback) {
      final SingleResponse<LogisticsTraceData> trackListResponse =
          afterSaleLogisticsBizService.getTrackList(trace.getTraceSource(), trace.getCallbackId());
      logisticsTraceData = ResponseAssert.assertJust(trackListResponse);
    } else {
      if (trace.getTrackTime() < DateUtil.currentTime() - config.getCallbackTimeout()
          && Arrays.asList(
                  OrderLogisticsTraceStatus.KDY_SUBSCRIBED,
                  OrderLogisticsTraceStatus.KDY_CALLBACK_TIMEOUT,
                  OrderLogisticsTraceStatus.MALL_WAIT_CALLBACK,
                  OrderLogisticsTraceStatus.MALL_CALLBACK_TIMEOUT,
                  OrderLogisticsTraceStatus.WDT_WAIT_CALLBACK)
              .contains(trace.getTraceStatus())) {
        closeAbnormalTrace(trace, CloseReasonType.CALLBACK_TIMEOUT);
        return;
      }
    }
    handle(trace, logisticsTraceData);
  }

  public void handle(OrderLogisticsTrace trace, LogisticsTraceData logisticsTraceData) {
    final ErpLogisticsStatus erpLogisticsStatus = toErpLogisticsStatus(trace, logisticsTraceData);
    final Long epochSecond = DateUtil.currentTime();
    final ProcessContext context =
        new ProcessContext(epochSecond, trace, logisticsTraceData, erpLogisticsStatus);
    final LogisticExceptionRes exceptionResult =
        logisticsExceptionBizService.exceptionHandler(context);
    final Optional<OrderLogisticsAbnormality> abnormalityOptional =
        abnormalityService.getByTraceId(trace.getId());
    if (exceptionResult.isException()) {
      abnormalHandler(trace, exceptionResult, erpLogisticsStatus);
    } else {
      abnormalityOptional.ifPresent(
          abnormality -> {
            if (abnormality.getAbnormalStatus() != AbnormalStatus.CLOSED) {
              abnormalityService.closeAbnormal(abnormality, trace, CloseReasonType.RECOVER);
            }
          });
    }
  }

  private void closeAbnormalTrace(OrderLogisticsTrace trace, CloseReasonType closeReasonType) {
    closeAbnormalTrace(
        trace,
        closeReasonType,
        abnormality -> abnormality.getAbnormalStatus() == AbnormalStatus.WAIT_FOLLOW);
  }

  private void closeAbnormalTrace(
      OrderLogisticsTrace trace,
      CloseReasonType closeReasonType,
      Predicate<OrderLogisticsAbnormality> predicate) {
    trace.setCloseReason(closeReasonType);
    trace.setOpenTrace(0);
    orderLogisticsTraceService.updateById(trace);
    abnormalityService
        .getByTraceId(trace.getId())
        .ifPresent(
            abnormality -> {
              if (predicate.test(abnormality)) {
                abnormalityService.closeAbnormal(abnormality, trace, closeReasonType);
              }
            });
  }

  private void abnormalHandler(
      OrderLogisticsTrace orderLogisticsTrace,
      LogisticExceptionRes logisticExceptionResult,
      ErpLogisticsStatus logisticsStatus) {
    boolean isNormal =
        logisticExceptionResult.getRootException().equals(LogisticsRootException.NORMAL);
    if (isNormal) {
      return;
    }
    final Optional<OrderLogisticsAbnormality> abnormalityExist =
        abnormalityService.getByTraceId(orderLogisticsTrace.getId());
    abnormalityExist.ifPresent(
        abnormality0 -> {
          // 如果手动关闭告警，则不再继续处理
          if (Objects.equals(abnormality0.getActivateWarning(), 0)) {
            return;
          }
          final boolean sameException = isSameException(abnormality0, logisticExceptionResult);
          AbnormalStatus abnormalityStatus = abnormality0.getAbnormalStatus();
          if (abnormalityStatus != AbnormalStatus.FOLLOWING) {
            abnormalityStatus = AbnormalStatus.WAIT_FOLLOW;
          }
          if (!sameException) {
            final Long abnormalityLogId =
                abnormalityService.addAbnormalityLog(abnormality0.getId(), logisticExceptionResult);
            abnormality0.setLastExceptionLogId(abnormalityLogId);
            abnormalityService.updateAbnormality(
                abnormality0,
                orderLogisticsTrace,
                abnormalityStatus,
                logisticsStatus,
                logisticExceptionResult);
            handleStatisticData(orderLogisticsTrace);
          } else {
            abnormalityService.updateAbnormality(
                abnormality0,
                orderLogisticsTrace,
                abnormalityStatus,
                logisticsStatus,
                logisticExceptionResult);
          }
        });
    abnormalityExist.orElseGet(
        () -> {
          final OrderLogisticsAbnormality abnormality =
              abnormalityService.addAbnormality(
                  orderLogisticsTrace,
                  AbnormalStatus.WAIT_FOLLOW,
                  logisticsStatus,
                  logisticExceptionResult);
          final Long abnormalityLogId =
              abnormalityService.addAbnormalityLog(abnormality.getId(), logisticExceptionResult);
          abnormality.setLastExceptionLogId(abnormalityLogId);
          abnormalityService.updateById(abnormality);
          handleStatisticData(orderLogisticsTrace);
          return abnormality;
        });
  }

  private void handleStatisticData(OrderLogisticsTrace orderLogisticsTrace) {
    if (StringUtil.isNotBlank(orderLogisticsTrace.getStockoutWarehouseNo())) {
      String key =
          "logistic_"
              + orderLogisticsTrace.getStockoutWarehouseNo()
              + "_"
              + DateUtil.format(LocalDateTime.now(), DateUtil.DEFAULT_DATE);
      RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
      atomicLong.incrementAndGet();
      atomicLong.expire(30, TimeUnit.DAYS);
    }
  }

  private boolean isSameException(
      @NotNull OrderLogisticsAbnormality abnormality,
      LogisticExceptionRes logisticExceptionResult) {
    return Objects.equals(
            logisticExceptionResult.getRootException().getValue(), abnormality.getAbnormalType())
        && Objects.equals(
            logisticExceptionResult.getSubException().getValue(), abnormality.getAbnormalType2());
  }

  public ErpLogisticsStatus toErpLogisticsStatus(
      OrderLogisticsTrace trace, LogisticsTraceData traceData) {
    if (traceData != null) {
      for (LogisticsTraceItem logisticsTraceItem :
          traceData.getTrackList().stream()
              .sorted(Comparator.comparing(LogisticsTraceItem::getTrackDate))
              .collect(Collectors.toList())) {
        if (afterSaleLogisticsConfig
            .getDifficultShortStatus()
            .contains(logisticsTraceItem.getShortStatus())) {
          return ErpLogisticsStatus.DIFFICULT;
        }
        if (afterSaleLogisticsConfig
                .getReceiveShortStatus()
                .contains(logisticsTraceItem.getShortStatus())
            || afterSaleLogisticsConfig.getReceiveShortStatus().stream()
                .anyMatch(val -> logisticsTraceItem.getTrackStatus().contains(val))) {
          return ErpLogisticsStatus.WAIT_FOR_PICKUP;
        }
      }
      return traceData.getStatus();
    }
    if (NumberUtil.isPositive(trace.getConsignTime())) {
      return ErpLogisticsStatus.SENT;
    }
    if (NumberUtil.isPositive(trace.getPickingUpTime())) {
      return ErpLogisticsStatus.PICKUP;
    }
    if (NumberUtil.isPositive(trace.getSendingTime())) {
      return ErpLogisticsStatus.IN_TRANSIT;
    }
    if (NumberUtil.isPositive(trace.getSigningTime())) {
      return ErpLogisticsStatus.SIGNED;
    }
    return ErpLogisticsStatus.NONE;
  }
}
