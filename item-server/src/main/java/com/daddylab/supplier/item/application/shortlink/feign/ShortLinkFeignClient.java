package com.daddylab.supplier.item.application.shortlink.feign;

import com.daddylab.supplier.item.application.shortlink.feign.dto.GenerateParams;
import com.daddylab.supplier.item.application.shortlink.feign.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(name = "ShortLinkFeignClient", url = "${shortlink-server.url}"
        , fallbackFactory = ShortLinkFallbackFactory.class)
public interface ShortLinkFeignClient {
    @RequestMapping(value = "/generate", method = RequestMethod.POST)
    Result<String> generate(@RequestBody GenerateParams params);
}
