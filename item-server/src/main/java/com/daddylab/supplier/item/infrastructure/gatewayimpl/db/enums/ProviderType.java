package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 供应商类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/28 10:32 上午
 * @description
 */
@Getter
@AllArgsConstructor
public enum ProviderType implements IEnum<Integer> {

    /**
     * 消除黄线
     */
    ALL(0, "全部"),
    /**
     * 自营
     */
    SELF_SUPPORT(1, "自营"),
    /**
     * 代发
     */
    SUBSTITUTE(2, "代发"),

    /**
     * 多店，商家入驻。
     */
    SETTLED(3,"商家入驻")

    ;

    @EnumValue
    private final Integer value;

    private final String desc;
    }
