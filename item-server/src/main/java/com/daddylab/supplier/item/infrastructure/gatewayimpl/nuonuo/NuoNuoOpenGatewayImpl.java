package com.daddylab.supplier.item.infrastructure.gatewayimpl.nuonuo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AllChannelBillData;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NuoNuoInvoiceProcessRecord;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NuoNuoInvoiceRequest;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAllChannelBillDataService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INuoNuoInvoiceProcessRecordService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INuoNuoInvoiceRequestService;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import io.jsonwebtoken.lang.Assert;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import nuonuo.open.sdk.NNOpenSDK;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2024年07月25日 2:50 PM
 */
@Slf4j
@AllArgsConstructor
@Service
public class NuoNuoOpenGatewayImpl implements NuoNuoOpenGateway {

    private final NuoNuoConfig nuoNuoConfig;
    private IAllChannelBillDataService iAllChannelBillDataService;
    private INuoNuoInvoiceProcessRecordService iNuoNuoInvoiceProcessRecordService;
    private INuoNuoInvoiceRequestService iNuoNuoInvoiceRequestService;

    private static final String NUO_NUO_TOKEN_KEY = "nuo_nuo_token";
    private static final String BILL_NEW_METHOD = "nuonuo.OpeMplatform.requestBillingNew";
    private static final String RED_CONFIRM_METHOD = "nuonuo.OpeMplatform.saveInvoiceRedConfirm";
    private static final String QUERY_INVOICE_RESULT = "nuonuo.OpeMplatform.queryInvoiceResult";

    /**
     * 获取诺税通访问令牌
     *
     * @return
     */
    private String accessToken() {
        if (StrUtil.isNotBlank(nuoNuoConfig.getDefaultToken())) {
            return nuoNuoConfig.getDefaultToken();
        }

        String token = RedisUtil.get(NUO_NUO_TOKEN_KEY);
        if (StrUtil.isNotBlank(token)) {
            return token;
        }

        String tokenResponse = StrUtil.EMPTY;
        try {
            tokenResponse = NNOpenSDK.getIntance().getMerchantToken(nuoNuoConfig.getAppKey(), nuoNuoConfig.getAppSecret());
            TokenAccessRes resDto = JsonUtil.parse(tokenResponse, TokenAccessRes.class);
            assert resDto != null;
            RedisUtil.set(NUO_NUO_TOKEN_KEY, resDto.getAccessToken(), 23, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("refresh nuo nuo accessToken fail,resJson:{}", tokenResponse, e);
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "票务系统访问令牌处理异常");
        }

        return token;
    }

    /**
     * {"access_token":"db053467b226bdf976b9cc0qsz0xdrvs","expires_in":86400}
     * {"access_token":"db053467b226bdf976b9cc0ujosxynts","expires_in":86400}
     *
     * @param args
     */


    /**
     * 构建开票接口请求body
     *
     * @param request      用户端开票请求体
     * @param billDataList 订单列表
     * @return 请求体JSON
     */
    private String requestBillingNewContent(NuoNuoInvoiceRequest request,
                                            List<AllChannelBillData> billDataList,
                                            BigDecimal definitelyCorrect) {

        Map<String, Object> orderMap = new HashMap<>(128);

        // 购买方名称
        orderMap.put("buyerName", request.getInvoiceTitle());
        // 购买方税号
        orderMap.put("buyerTaxNum", request.getTaxCode());
        // 销售方税号,电话,地址
        orderMap.put("salerTaxNum", nuoNuoConfig.getSaleTaxNum());
        orderMap.put("salerTel", nuoNuoConfig.getSalePhone());
        orderMap.put("salerAddress", nuoNuoConfig.getSaleAddress());
        // 订单号
        orderMap.put("orderNo", request.getOrderNo());
        // 订单时间
        orderMap.put("invoiceDate", DateUtil.format(DateUtil.now()));
        // 开票员（数电票时需要传入和开票登录账号对应的开票员姓名）
        orderMap.put("clerk", nuoNuoConfig.getClerk());
        // 推送方式：-1,不推送;0,邮箱;1,手机（默认）;2,邮箱、手机
        orderMap.put("pushMode", "0");
        orderMap.put("email", request.getMailAddress());
        // 开票类型：1:蓝票;2:红票 （数电票冲红请对接数电快捷冲红接口）
        orderMap.put("invoiceType", "1");
        // 发票种类。详请见 InvoiceType
        orderMap.put("invoiceLine", request.getInvoiceType().name());
        // 分机号（只能为空或者数字）
        orderMap.put("extensionNumber", nuoNuoConfig.getExtensionNumber());
        // 对购方税号校验（ 0-不校验 1-校验，仅对数电票有效，未传时则取企业配置的值；注：若开启校验，当购方税号未能在电子税局中找到时 则会开票失败）
        orderMap.put("taxNumVerifyFlag", "0");
        // 对购方名称校验（ 0-不校验 1-校验，仅对数电普票（电子）有效，未传时则取企业配置的值；若开启校验，当开具非自然人标记的数电普票（电子）时，
        // 将限制对于“购买方名称长度小于等于4位”的发票的开具）
        orderMap.put("naturalPersonVerifyFlag", "0");
        // 业务方自定义字段
        orderMap.put("dField1", "dField1");
        orderMap.put("dField2", "dField2");
        orderMap.put("dField3", "dField3");
        orderMap.put("dField4", "dField4");
        orderMap.put("dField5", "dField5");

        // 全额退款的明细特征，运费 = 开票金额，这批数据不需要开票，但是要把这批数据的运费金额累计起来，算到第一笔可以开票明细的开票金额上。
        AtomicReference<BigDecimal> discardInvoiceAmountSum = new AtomicReference<>(BigDecimal.ZERO);
        billDataList.stream()
                .filter(val -> val.getPostFee().compareTo(val.getInvoiceAmt()) == 0)
                .map(AllChannelBillData::getInvoiceAmt).reduce(BigDecimal::add)
                .ifPresent(discardInvoiceAmountSum::set);
        // 剔除全额退款的数据，开票金额从大到小，倒序。
        billDataList = billDataList.stream()
                .filter(val -> val.getPostFee().compareTo(val.getInvoiceAmt()) != 0)
                .sorted(Comparator.comparing(AllChannelBillData::getInvoiceAmt).reversed())
                .collect(Collectors.toList());
        Assert.state(CollUtil.isNotEmpty(billDataList), "剔除全额退款的数据后，没有可以开票的订单明细");

        List<InvoiceDetail> invoiceDetailArray = new LinkedList<>();
        for (int i = 0; i < billDataList.size(); i++) {
            if (i == 0) {
                billDataList.get(i).setInvoiceAmt(billDataList.get(i).getInvoiceAmt().add(discardInvoiceAmountSum.get()));
                invoiceDetailArray.addAll(buildInvoiceDetail(billDataList.get(i)));
            } else {
                invoiceDetailArray.addAll(buildInvoiceDetail(billDataList.get(i)));
            }
        }

        // 金额修正处理
        final Optional<BigDecimal> reduce = invoiceDetailArray.stream()
                .map(val -> new BigDecimal(val.getTaxIncludedAmount()))
                .reduce(BigDecimal::add);
        if (reduce.isPresent()) {
            // 开票金额累计和绝对开票金额 值不相等
            if (reduce.get().compareTo(definitelyCorrect) != 0) {
                // 拿第一条数据（开票金额最大）去做金额修正
                invoiceDetailArray.stream().filter(val ->
                                val.getInvoiceLineProperty().equals("0") || val.getInvoiceLineProperty().equals("2"))
                        .findFirst().ifPresent(invoiceDetail -> {
                            final BigDecimal otherSum = reduce.get().subtract(new BigDecimal(invoiceDetail.getTaxIncludedAmount()));
                            final BigDecimal newAmount = definitelyCorrect.subtract(otherSum).setScale(2, RoundingMode.HALF_UP);

                            invoiceDetail.setTaxIncludedAmount(newAmount.toString());
                            final BigDecimal taxAmount = getTaxAmount(newAmount, new BigDecimal(invoiceDetail.getTaxRate()));
                            invoiceDetail.setTax(taxAmount.toString());
                            invoiceDetail.setTaxExcludedAmount(newAmount.subtract(taxAmount).setScale(2,
                                    RoundingMode.HALF_UP).toString());
                        });
            }
        }

        orderMap.put("invoiceDetail", invoiceDetailArray);

        Map<String, Object> map = new HashMap<>(2);
        map.put("order", orderMap);
        return JsonUtil.toJson(map);
    }

    public List<InvoiceDetail> buildInvoiceDetail(AllChannelBillData billData) {
        List<InvoiceDetail> resList = new LinkedList<>();

        final BigDecimal discountAmount = billData.getTotalAmount().subtract(billData.getRealAmount());
        boolean haveDiscount = discountAmount.compareTo(BigDecimal.ZERO) > 0;
        // 订单金额 - 售后金额
        BigDecimal rfdAmount = Objects.isNull(billData.getRfdAmount()) ? BigDecimal.ZERO : billData.getRfdAmount();
        BigDecimal invoiceAmt = billData.getTotalAmount().subtract(rfdAmount).setScale(2, RoundingMode.HALF_UP);

        InvoiceDetail invoiceDetail = new InvoiceDetail();
        invoiceDetail.setGoodsName(billData.getGoodsName().length() > 90
                ? billData.getGoodsName().substring(0, 90) : billData.getGoodsName());
        invoiceDetail.setGoodsCode(billData.getTaxClassCode());
        invoiceDetail.setSelfCode(billData.getId().toString());
        invoiceDetail.setUnit(billData.getUnit());
        invoiceDetail.setWithTaxFlag("1");
        invoiceDetail.setNum(billData.getGoodsNum().toString());

        if (billData.getTaxRate().compareTo(BigDecimal.ZERO) == 0) {
            invoiceDetail.setFavouredPolicyFlag("03");
        } else {
            invoiceDetail.setFavouredPolicyFlag("0");
        }

        invoiceDetail.setTaxRate(billData.getTaxRate().setScale(2, RoundingMode.HALF_UP).toString());
        BigDecimal taxAmount = getTaxAmount(invoiceAmt, billData.getTaxRate());
        invoiceDetail.setTax(taxAmount.toString());
        invoiceDetail.setTaxExcludedAmount(invoiceAmt.subtract(taxAmount).setScale(2, RoundingMode.HALF_UP).toString());
        invoiceDetail.setTaxIncludedAmount(invoiceAmt.setScale(2, RoundingMode.HALF_UP).toString());
        // 发票行性质,正常行;1,折扣行;2,被折扣行。红票只有正常行
        invoiceDetail.setInvoiceLineProperty(haveDiscount ? "2" : "0");

        resList.add(invoiceDetail);

        if (haveDiscount) {
            InvoiceDetail invoiceDetail2 = new InvoiceDetail();
            invoiceDetail2.setGoodsName(billData.getGoodsName().length() > 90
                    ? billData.getGoodsName().substring(0, 90) : billData.getGoodsName());
            invoiceDetail2.setGoodsCode(billData.getTaxClassCode());
            invoiceDetail2.setSelfCode(billData.getId().toString());
            invoiceDetail2.setWithTaxFlag("1");

            if (billData.getTaxRate().compareTo(BigDecimal.ZERO) == 0) {
                invoiceDetail2.setFavouredPolicyFlag("03");
            } else {
                invoiceDetail2.setFavouredPolicyFlag("0");
            }

            invoiceDetail2.setTaxRate(billData.getTaxRate().setScale(2, RoundingMode.HALF_UP).toString());
            BigDecimal taxAmount2 = getTaxAmount(discountAmount, billData.getTaxRate());
            invoiceDetail2.setTax(taxAmount2.negate().toString());
            invoiceDetail2.setTaxExcludedAmount(discountAmount.subtract(taxAmount2).negate().setScale(2, RoundingMode.HALF_UP).toString());
            invoiceDetail2.setTaxIncludedAmount(discountAmount.negate().setScale(2, RoundingMode.HALF_UP).toString());
            invoiceDetail2.setInvoiceLineProperty("1");

            resList.add(invoiceDetail2);
        }

        return resList;
    }

    /**
     * 税额等于含税金额/（1+税率）*税率
     *
     * @param amt
     * @param taxRate
     * @return
     */
    private static BigDecimal getTaxAmount(BigDecimal amt, BigDecimal taxRate) {
        final BigDecimal divide = amt.divide(BigDecimal.ONE.add(taxRate), 6, RoundingMode.HALF_UP);
        return divide.multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    public NuoNuoInvoiceProcessRecord requestBillingNew(NuoNuoInvoiceRequest request,
                                                        List<AllChannelBillData> billDataList,
                                                        BigDecimal definitelyCorrect) {

        // 增加开票拦截名单，根据单号拦截模拟开票结果。
        if (nuoNuoConfig.getFilterOrderNo().contains(request.getOrderNo())) {
            NuoNuoInvoiceProcessRecord mockResp = NuoNuoInvoiceProcessRecord.buildRecord(request.getOrderNo(), 1, "模拟开票");
            Alert.text(MessageRobotCode.INVOICE_NOTIFY, StrUtil.format("[诺诺开票成功] 订单号:{}", request.getOrderNo()));
            return mockResp;
        }

        String requestStr = "";
        String responseStr = "";
        NuoNuoInvoiceProcessRecord nuoNuoInvoiceProcessRecord = null;

        try {
            requestStr = requestBillingNewContent(request, billDataList, definitelyCorrect);

            NNOpenSDK sdk = NNOpenSDK.getIntance();
            String taxNum = nuoNuoConfig.getSaleTaxNum();
            String appKey = nuoNuoConfig.getAppKey();
            String appSecret = nuoNuoConfig.getAppSecret();
            String token = accessToken();
            String url = nuoNuoConfig.getUrl();
            String sendId = UUID.randomUUID().toString().replace("-", "");
            responseStr = sdk.sendPostSyncRequest(url, sendId, appKey, appSecret, token, taxNum, BILL_NEW_METHOD, requestStr);
            log.info("requestBillingNew res:{},orderNo:{}", responseStr, request.getOrderNo());
            final String blueInvoiceNum = NuoNuoResponse.getBlueInvoiceNum(responseStr);

            if (StringUtils.isBlank(blueInvoiceNum)) {
                final String s = buildErrorMsg(requestStr, responseStr, "");
                nuoNuoInvoiceProcessRecord = NuoNuoInvoiceProcessRecord.buildRecord(request.getOrderNo(), -1, s);
            } else {
                nuoNuoInvoiceProcessRecord = NuoNuoInvoiceProcessRecord.buildRecord(request.getOrderNo(), 1, blueInvoiceNum);
                Alert.text(MessageRobotCode.INVOICE_NOTIFY, StrUtil.format("[诺诺开票成功] 订单号:{}", request.getOrderNo()));
            }
        } catch (Exception e) {
            log.error("requestBillingNew error.requestId:{},orderNo:{}", request.getId(), request.getOrderNo(), e);

            final String s = buildErrorMsg(requestStr, responseStr, e.getMessage());
            nuoNuoInvoiceProcessRecord = NuoNuoInvoiceProcessRecord.buildRecord(request.getOrderNo(), -1, s);

            Alert.text(MessageRobotCode.INVOICE_NOTIFY, StrUtil.format("[诺诺开票异常] 订单号:{},错误:{}", request.getOrderNo(), responseStr));
        } finally {
            iNuoNuoInvoiceProcessRecordService.save(nuoNuoInvoiceProcessRecord);
        }

        return nuoNuoInvoiceProcessRecord;
    }

    private String buildErrorMsg(String req, String resp, String e) {
        Map<String, Object> errorMap = new HashMap<>(4);
        errorMap.put("requestStr", req);
        errorMap.put("responseStr", resp);
        errorMap.put("error", e);
        return JsonUtil.toJson(errorMap);
    }

    // ----------------------------------------------------------------------------------------------------------------


    private String requestCreditNoteApplyContent(NuoNuoInvoiceRequest request, NuoNuoInvoiceResultDto nuoNuoInvoiceResultDto) {

        Map<String, Object> paramMap = new HashMap<>(128);
        paramMap.put("billId", request.getOrderNo());
        paramMap.put("blueInvoiceLine", request.getInvoiceType().name());
        paramMap.put("orderNo", request.getOrderNo());
        // 申请方（录入方）身份： 0 销方 1 购方
        paramMap.put("applySource", "0");
        // 对应蓝字数电票号码（数电普票、数电专票、数纸普票、数纸专票都需要传，蓝票是增值税发票时不传）
        paramMap.put("blueElecInvoiceNumber", nuoNuoInvoiceResultDto.getElectronicInvoiceNumber());
        // 销方 税号,名称
        paramMap.put("sellerTaxNo", nuoNuoConfig.getSaleTaxNum());
        paramMap.put("sellerName", nuoNuoConfig.getSaleName());
        // 购方名称
        paramMap.put("buyerName", request.getInvoiceTitle());
        paramMap.put("buyerTaxNo", request.getTaxCode());
        // 冲红原因： 1销货退回 2开票有误 3服务中止 4销售折让
        paramMap.put("redReason", "1");
        // 分机号
        paramMap.put("extensionNumber", nuoNuoConfig.getExtensionNumber());

        List<Map<String, Object>> detailMapList = new LinkedList<>();
        for (NuoNuoInvoiceItemDto invoiceItemDto : nuoNuoInvoiceResultDto.getInvoiceItemList()) {
            Map<String, Object> detailMap = new HashMap<>(64);

            detailMap.put("blueDetailIndex", 1);
            detailMap.put("num", "-" + invoiceItemDto.getNum());
            detailMap.put("price", invoiceItemDto.getItemPrice());
            detailMap.put("selfCode", invoiceItemDto.getItemSelfCode());
            detailMap.put("goodsName", invoiceItemDto.getGoodsName());
            detailMap.put("invoiceLineProperty", "0");
            detailMap.put("withTaxFlag", 1);
            detailMap.put("taxRate", invoiceItemDto.getTaxRate());
            detailMap.put("unit", invoiceItemDto.getItemUnit());
            detailMap.put("goodsCode", invoiceItemDto.getGoodsCode());
            detailMapList.add(detailMap);
        }
        paramMap.put("detail", detailMapList);
        return JsonUtil.toJson(paramMap);
    }


    @Override
    public NuoNuoInvoiceProcessRecord requestCreditNoteApply(String orderNo) {
        String requestStr = "";
        String responseStr = "";
        NuoNuoInvoiceProcessRecord nuoNuoInvoiceProcessRecord = null;

        final List<NuoNuoInvoiceRequest> invoiceRequestList = iNuoNuoInvoiceRequestService.getByOrderNo(orderNo);
        final List<NuoNuoInvoiceProcessRecord> recordList = iNuoNuoInvoiceProcessRecordService.getByStatus(orderNo, 1);
        boolean access = CollUtil.isNotEmpty(invoiceRequestList) && CollUtil.isNotEmpty(recordList);
        if (!access) {
            final NuoNuoInvoiceProcessRecord record = NuoNuoInvoiceProcessRecord.buildRecord(orderNo, -3, "冲红前置拦截，没有C端开票请求数据和诺诺开票成功记录");
            iNuoNuoInvoiceProcessRecordService.save(record);
            return record;
        }

        try {
            final NuoNuoInvoiceProcessRecord record = recordList.get(0);
            final NuoNuoInvoiceRequest request = invoiceRequestList.get(0);

            // 查询发票详情获取数电号码
            final NuoNuoInvoiceProcessRecord queryInvoiceResultRecord = queryInvoiceResult(ListUtil.of(record.getRecord()), request.getOrderNo());
            if (queryInvoiceResultRecord.getStatus() < 0) {
                throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "冲红前置操作，查询发票数电号码失败");
            }
            final NuoNuoInvoiceResultDto nuoNuoInvoiceResultDto = NuoNuoInvoiceResultDto.of(queryInvoiceResultRecord.getRecord());

            requestStr = requestCreditNoteApplyContent(request, nuoNuoInvoiceResultDto);

            NNOpenSDK sdk = NNOpenSDK.getIntance();
            String taxNum = nuoNuoConfig.getSaleTaxNum();
            String appKey = nuoNuoConfig.getAppKey();
            String appSecret = nuoNuoConfig.getAppSecret();
            String token = accessToken();
            String url = nuoNuoConfig.getUrl();
            String sendId = UUID.randomUUID().toString().replace("-", "");
            log.info("requestCreditNoteApply req param:{}", requestStr);
            responseStr = sdk.sendPostSyncRequest(url, sendId, appKey, appSecret, token, taxNum, RED_CONFIRM_METHOD, requestStr);
            log.info("requestCreditNoteApply resp:{}", responseStr);

            final Boolean success = NuoNuoResponse.isSuccess(responseStr);
            if (success) {
                nuoNuoInvoiceProcessRecord = NuoNuoInvoiceProcessRecord.buildRecord(request.getOrderNo(), 3, "冲红成功");
                Alert.text(MessageRobotCode.INVOICE_NOTIFY, StrUtil.format("[诺诺全额冲红成功] 订单号:{}", request.getOrderNo()));
            } else {
                final String s = buildErrorMsg(requestStr, responseStr, "");
                nuoNuoInvoiceProcessRecord = NuoNuoInvoiceProcessRecord.buildRecord(request.getOrderNo(), -3, s);
                Alert.text(MessageRobotCode.INVOICE_NOTIFY, StrUtil.format("[诺诺全额冲红失败] 订单号:{}", request.getOrderNo()));
            }
        } catch (Exception e) {
            log.error("requestCreditNoteApply error.orderNo:{}", orderNo, e);
            final String s = buildErrorMsg(requestStr, responseStr, e.getMessage());
            nuoNuoInvoiceProcessRecord = NuoNuoInvoiceProcessRecord.buildRecord(orderNo, -3, s);
            Alert.text(MessageRobotCode.INVOICE_NOTIFY, StrUtil.format("[诺诺全额冲红异常] 订单号:{}", orderNo));
        } finally {
            iNuoNuoInvoiceProcessRecordService.save(nuoNuoInvoiceProcessRecord);
        }

        return nuoNuoInvoiceProcessRecord;
    }


    @Override
    public NuoNuoInvoiceProcessRecord queryInvoiceResult(List<String> serialNos, String orderNo) {
        String requestStr = "";
        String responseStr = "";
        NuoNuoInvoiceProcessRecord nuoNuoInvoiceProcessRecord = null;

        final List<NuoNuoInvoiceProcessRecord> recordList = iNuoNuoInvoiceProcessRecordService.getByStatus(orderNo, 2);
        if (CollUtil.isNotEmpty(recordList) && StrUtil.isNotBlank(recordList.get(0).getRecord())) {
            nuoNuoInvoiceProcessRecord = NuoNuoInvoiceProcessRecord.buildRecord(orderNo, 2, recordList.get(0).getRecord());
            return nuoNuoInvoiceProcessRecord;
        }

        try {
            Map<String, Object> paramMap = new HashMap<>(2);
            paramMap.put("serialNos", serialNos);
            paramMap.put("isOfferInvoiceDetail", "1");
            requestStr = JsonUtil.toJson(paramMap);

            NNOpenSDK sdk = NNOpenSDK.getIntance();
            String taxNum = nuoNuoConfig.getSaleTaxNum();
            String appKey = nuoNuoConfig.getAppKey();
            String appSecret = nuoNuoConfig.getAppSecret();
            String token = accessToken();
            String url = nuoNuoConfig.getUrl();
            String sendId = UUID.randomUUID().toString().replace("-", "");
            log.info("queryInvoiceResult req:{}", requestStr);
            responseStr = sdk.sendPostSyncRequest(url, sendId, appKey, appSecret, token, taxNum, QUERY_INVOICE_RESULT, requestStr);
            log.info("queryInvoiceResult resp:{}", responseStr);

            if (NuoNuoResponse.isSuccess(responseStr)) {
                nuoNuoInvoiceProcessRecord = NuoNuoInvoiceProcessRecord.buildRecord(orderNo, 2, responseStr);
            } else {
                final String s = buildErrorMsg(requestStr, responseStr, "");
                nuoNuoInvoiceProcessRecord = NuoNuoInvoiceProcessRecord.buildRecord(orderNo, -2, s);
            }
        } catch (Exception e) {
            log.error("queryInvoiceResult error.orderNo:{}", orderNo, e);
            final String s = buildErrorMsg(requestStr, responseStr, e.getMessage());
            nuoNuoInvoiceProcessRecord = NuoNuoInvoiceProcessRecord.buildRecord(orderNo, -2, s);
        } finally {
            iNuoNuoInvoiceProcessRecordService.save(nuoNuoInvoiceProcessRecord);
        }
        return nuoNuoInvoiceProcessRecord;
    }
}

