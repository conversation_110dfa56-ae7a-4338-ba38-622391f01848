package com.daddylab.supplier.item.controller.compensate;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.provider.ProviderBizService;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 补偿接口，
 * 主要补偿金蝶同步的数据
 *
 * <AUTHOR> up
 * @date 2022/6/21 4:06 下午
 */
@RestController
@RequestMapping("/compensateKingDee")
public class CompensateKingDee {

    @Autowired
    ItemBizService itemBizService;

    @Autowired
    ProviderBizService providerBizService;

    /**
     * 推送sku到物料
     *
     * @param restartSyncSkuCmd
     * @return
     */
//    @ResponseBody
//    @PostMapping("/sku")
//    @Auth(noAuth = true)
//    public SingleResponse<Boolean> sku(@RequestBody RestartSyncSkuCmd restartSyncSkuCmd) {
//        itemBizService.restartSyncSku(restartSyncSkuCmd);
//        return SingleResponse.of(true);
//    }

    /**
     * 推送供应商到物料
     *
     * @param restartSyncProviderCmd
     * @return
     */
    @ResponseBody
    @PostMapping("/provider")
    @Auth(noAuth = true)
    public SingleResponse<Boolean> provider(@RequestBody RestartSyncProviderCmd restartSyncProviderCmd) {
        providerBizService.restartSyncProvider(restartSyncProviderCmd);
        return SingleResponse.of(true);
    }



}
