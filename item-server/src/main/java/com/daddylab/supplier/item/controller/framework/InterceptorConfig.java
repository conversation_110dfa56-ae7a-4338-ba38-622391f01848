package com.daddylab.supplier.item.controller.framework;

import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/22 0:03 上午
 */
@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        String[] excludePathPatterns = {"/dev/**", "/swagger-ui.html", "/purchase/h5/**",
                "/purchaseDissent/**", "/swagger-resources/**", "/open/**", "/external/**"};

        //登录拦截器
        registry.addInterceptor(getJcasbinAuthzInterceptor()).addPathPatterns("/**")
                .excludePathPatterns(excludePathPatterns);

        //访问记录拦截器
        registry.addInterceptor(ApplicationContextUtil.getBean(VisitStatInterceptor.class))
                .addPathPatterns("/**").excludePathPatterns(excludePathPatterns);
        WebMvcConfigurer.super.addInterceptors(registry);
    }

    @Bean
    public JcasbinAuthzInterceptor getJcasbinAuthzInterceptor() {
        return new JcasbinAuthzInterceptor();
    }
}
