package com.daddylab.supplier.item.controller.otherPay.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherPayDetail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OtherPayVo.java
 * @description
 * @createTime 2022年03月24日 15:50:00
 */
@Data
@ApiModel("应付单返回实体")
public class OtherPayVo implements Serializable {

    private static final long serialVersionUID = 1923142904599827697L;

    @ApiModelProperty(value = "应付单id")
    private Long id;

    @ApiModelProperty(value = "应付单号")
    private String payNo;

    @ApiModelProperty(value = "应付年")
    private Long payYear;

    @ApiModelProperty(value = "应付月")
    private Long payMonth;

    @ApiModelProperty(value = "供应商id")
    private Long providerId;

    @ApiModelProperty(value = "供应商")
    private String providerName;


    @ApiModelProperty("是否黑名单 0否 1是")
    private Integer isBlacklist;

    @ApiModelProperty(value = "应付总金额")
    private BigDecimal payTotal;

    @ApiModelProperty(value = "应付类型，0.合计。1.运费。2.售后。3.抖音扣款。4.商家处罚。5.其他")
    private List<Integer> payType;

    @ApiModelProperty(value = "状态 0:已完成 1:待提交 2:待审核 3:已拒绝 4:已撤回")
    private Integer status;

    @ApiModelProperty(value = "采购员id")
    private Long buyerId;

    @ApiModelProperty(value = "采购员")
    private String buyerName;

    @ApiModelProperty(value = "其他应付单详情")
    private List<OtherPayDetail> otherPayDetails;

    @ApiModelProperty(value = "创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "创建人id")
    private Long createdUid;

    @ApiModelProperty(value = "创建人名称")
    private String createdName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "采购组织id")
    private Long organizationId;

    @ApiModelProperty(value = "采购组织名称")
    private String organizationName;

    @ApiModelProperty(value = "审核通过日期")
    private Long auditedAt;

    @ApiModelProperty(value = "凭证list")
    private List<OtherImageVo> imageList;

    @ApiModelProperty(value = "是否可编辑 0:可编辑")
    private Integer isEdit;
}
