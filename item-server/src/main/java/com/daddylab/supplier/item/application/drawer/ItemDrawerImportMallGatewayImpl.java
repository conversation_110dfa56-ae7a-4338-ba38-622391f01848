package com.daddylab.supplier.item.application.drawer;

import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerImageFileTypeEnum;
import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerImageTypeEnum;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CategoryAttr;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemAttr;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Sku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DataSource;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICategoryAttrService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemAttrService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerImageService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ISkuService;
import com.daddylab.supplier.item.infrastructure.utils.FileUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Lists;
import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/9/29
 */
@Service
@AllArgsConstructor
public class ItemDrawerImportMallGatewayImpl implements ItemDrawerImportMallGateway {

    private final IItemDrawerService itemDrawerService;
    private final IItemService itemService;
    private final IItemDrawerImageService itemDrawerImageService;
    private final IItemAttrService itemAttrService;
    private final ICategoryAttrService categoryAttrService;
    private final ISkuService skuService;

    private final LoadingCache<String, Item> itemLoadingCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(1))
            .build(this::queryItem);

    private final LoadingCache<String, List<Item>> itemWiderLoadingCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(1))
            .build(this::queryItemWider);

    private final LoadingCache<Long, ItemDrawer> itemDrawerLoadingCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(1))
            .build(this::queryOrCreateItemDrawer);

    private final LoadingCache<String, Map<String, Long>> itemAttrsMapLoadingCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(1))
            .build(this::getItemAttrsMap);

    private Item queryItem(String itemCode) {
        return itemService.lambdaQuery().eq(Item::getCode, itemCode).one();
    }

    private List<Item> queryItemWider(String itemCode) {
        final Item item = queryItem(itemCode);
        if (Objects.nonNull(item)) {
            return Collections.singletonList(item);
        }
        final List<Sku> skus = skuService.lambdaQuery().eq(Sku::getItemCode, itemCode).list();
        if (skus.isEmpty()) {
            return Collections.emptyList();
        }
        final List<String> skuCodes = skus.stream().map(Sku::getSkuCode)
                .collect(Collectors.toList());
        return itemService.lambdaQuery().in(Item::getCode, skuCodes).list();
    }

    private Optional<Item> cacheGetItem(String itemCode) {
        return Optional.ofNullable(itemLoadingCache.get(itemCode));
    }

    @Override
    public boolean isMatchItem(String itemCode, Long itemId) {
        return cacheGetItemWider(itemCode).stream()
                .anyMatch(item -> Objects.equals(itemId, item.getId())
                        && item.getSource() == DataSource.WDT);
    }

    @Override
    public List<Long> getItemIds(String itemCode) {
        return cacheGetItemWider(itemCode).stream().map(Entity::getId).collect(Collectors.toList());
    }

    private List<Item> cacheGetItemWider(String itemCode) {
        return Optional.ofNullable(itemWiderLoadingCache.get(itemCode))
                .orElseGet(Collections::emptyList);
    }

    public Optional<Long> getItemId(String itemCode) {
        return cacheGetItem(itemCode).map(Entity::getId);
    }

    private Optional<ItemDrawer> cacheGetItemDrawer(Long itemId) {
        return Optional.ofNullable(itemDrawerLoadingCache.get(itemId));
    }

    private ItemDrawer queryOrCreateItemDrawer(Long itemId) {
        return itemDrawerService.lambdaQuery().eq(ItemDrawer::getItemId, itemId).oneOpt()
                .orElseGet(() -> {
                    final ItemDrawer newOne = new ItemDrawer();
                    newOne.setItemId(itemId);
                    newOne.setStandardName("");
                    newOne.setTbTitle("");
                    newOne.setMiniTitle("");
                    newOne.setHomeCopy("");
                    newOne.setShelfTime(0L);
                    newOne.setDouLink("");
                    newOne.setTbLink("");
                    itemDrawerService.save(newOne);
                    return newOne;
                });
    }

    @Override
    public Optional<Long> getItemDrawerId(Long itemId) {
        return cacheGetItemDrawer(itemId).map(Entity::getId);
    }

    private List<ItemAttr> queryItemAttrs(Long itemId) {
        return itemAttrService.lambdaQuery()
                .eq(ItemAttr::getItemId, itemId).list();
    }

    private List<CategoryAttr> queryCategoryAttrs(Long categoryId) {
        return categoryAttrService.lambdaQuery()
                .eq(CategoryAttr::getCategoryId, categoryId).list();

    }

    private Map<String, Long> getItemAttrsMap(String itemCode) {
        if (!cacheGetItem(itemCode).isPresent()) {
            return Collections.emptyMap();
        }
        final Item item = cacheGetItem(itemCode).get();
        final Long itemId = item.getId();

        final Map<Long, String> mapAttrIdToName = queryCategoryAttrs(itemId).stream()
                .collect(Collectors.toMap(CategoryAttr::getId, CategoryAttr::getName));

        return queryItemAttrs(itemId).stream()
                .filter(itemAttr -> !Objects.isNull(mapAttrIdToName.get(itemAttr.getAttrId())))
                .collect(Collectors.toMap(itemAttr -> buildAttrKey(
                                mapAttrIdToName.get(itemAttr.getAttrId()), itemAttr.getAttrValue()),
                        ItemAttr::getId));
    }

    private String buildAttrKey(String attrName, String attrValue) {
        return StringUtil.format("{}:{}", attrName, attrValue);
    }

    @Override
    public Optional<Long> mapAttrValueToId(String itemCode, String attrName, String attrValue) {
        return Optional.ofNullable(
                Objects.requireNonNull(itemAttrsMapLoadingCache.get(itemCode), "逻辑异常，此处商品属性映射不应为空")
                        .get(buildAttrKey(attrName, attrValue)));
    }

    private void saveBatchItemDrawerImages(Long itemDrawerId, ItemDrawerImageTypeEnum type,
            ItemDrawerImageFileTypeEnum fileType, List<Image> images
    ) {
        List<ItemDrawerImage> imgList = Lists.newArrayList();
        for (Image image : images) {
            final ItemDrawerImage itemDrawerImage = new ItemDrawerImage();
            itemDrawerImage.setDrawerId(itemDrawerId);
            itemDrawerImage.setUrl(image.getUrl());
            itemDrawerImage.setFilename(Optional.ofNullable(image.getFileName())
                    .orElse(FileUtil.getName(image.getUrl())));
            itemDrawerImage.setType(type);
            itemDrawerImage.setFileType(fileType.getValue());
            itemDrawerImage.setExt(FileUtil.getSuffix(image.getUrl()));
            itemDrawerImage.setSort((long) (imgList.size() + 1));
            itemDrawerImage.setItemAttrId(image.getItemAttrId());
            imgList.add(itemDrawerImage);
        }
        itemDrawerImageService.saveBatch(imgList);
    }

    @Override
    public void saveBatchItemDrawerAttrImages(Long itemDrawerId, List<Image> images) {
        saveBatchItemDrawerImages(itemDrawerId, ItemDrawerImageTypeEnum.ATTR,
                ItemDrawerImageFileTypeEnum.IMAGE, images);
    }

    @Override
    public void saveBatchItemDrawerMainImages(Long itemDrawerId, List<Image> images) {
        saveBatchItemDrawerImages(itemDrawerId, ItemDrawerImageTypeEnum.ITEM,
                ItemDrawerImageFileTypeEnum.IMAGE, images);
    }

    @Override
    public void saveBatchItemDrawerMainVideo(Long itemDrawerId, String url, String fileName,
            String cover) {
        final ItemDrawerImage itemDrawerImage = new ItemDrawerImage();
        itemDrawerImage.setDrawerId(itemDrawerId);
        itemDrawerImage.setUrl(url);
        itemDrawerImage.setFilename(Optional.ofNullable(fileName)
                .filter(StringUtil::isNotBlank)
                .orElse(FileUtil.getName(url)));
        itemDrawerImage.setType(ItemDrawerImageTypeEnum.MAIN_IMAGE_VIDEO);
        itemDrawerImage.setFileType(ItemDrawerImageFileTypeEnum.VIDEO.getValue());
        itemDrawerImage.setExt(FileUtil.getSuffix(url));
        itemDrawerImage.setFirstImage(cover);
        itemDrawerImageService.save(itemDrawerImage);
    }

    @Override
    public void saveBatchItemDrawerDetailImages(Long itemDrawerId, List<Image> images) {
        saveBatchItemDrawerImages(itemDrawerId, ItemDrawerImageTypeEnum.DETAIL,
                ItemDrawerImageFileTypeEnum.IMAGE, images);
    }

    @Override
    public boolean hasItemDrawerImage(Long itemDrawerId, ItemDrawerImageTypeEnum typeEnum) {
        return itemDrawerImageService.lambdaQuery().eq(ItemDrawerImage::getDrawerId, itemDrawerId)
                .eq(ItemDrawerImage::getType, typeEnum)
                .last("LIMIT 1").oneOpt().isPresent();
    }

    @Override
    public void updateItemDrawerVideoCover(Long itemDrawerId, String url, String cover) {
        itemDrawerImageService.lambdaUpdate().eq(ItemDrawerImage::getDrawerId, itemDrawerId)
                .eq(ItemDrawerImage::getType, ItemDrawerImageTypeEnum.MAIN_IMAGE_VIDEO)
                .eq(ItemDrawerImage::getUrl, url).set(ItemDrawerImage::getFirstImage, cover)
                .update();
    }
}
