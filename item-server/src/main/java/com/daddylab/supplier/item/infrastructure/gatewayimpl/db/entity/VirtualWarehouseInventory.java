/*
package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryMode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

*/
/**
 * <p>
 * 虚拟仓库存信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 *//*

@Data
@EqualsAndHashCode(callSuper = false)
public class VirtualWarehouseInventory implements Serializable {

    private static final long serialVersionUID = 1L;

    */
/**
     * id
     *//*

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    */
/**
     * 创建时间createAt
     *//*

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    */
/**
     * 创建人updateUser
     *//*

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    */
/**
     * 更新时间updateAt
     *//*

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    */
/**
     * 更新人updateUser
     *//*

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    */
/**
     * 是否已删除
     *//*

    @TableLogic
    private Integer isDel;

    */
/**
     * 删除时间
     *//*

    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    */
/**
     * 虚拟仓ID
     *//*

    private Long virtualWarehouseId;

    */
/**
     * 虚拟仓编号
     *//*

    private String virtualWarehouseNo;

    */
/**
     * 实体仓仓库编号
     *//*

    private String warehouseNo;

    */
/**
     * 实体仓库名称
     *//*

    private String warehouseName;

    */
/**
     * 库存占比
     *//*

    private Integer inventoryRatio;

    private Integer version;

    private InventoryMode inventoryMode;

}
*/
