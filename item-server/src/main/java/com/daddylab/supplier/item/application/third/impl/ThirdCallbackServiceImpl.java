package com.daddylab.supplier.item.application.third.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.third.ThirdCallbackService;
import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQProducer;
import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQTopic;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.domain.dto.KsMessageDTO;
import com.daddylab.supplier.item.infrastructure.third.kuaishou.impl.KuaiShouSecurityCodec;
import com.daddylab.supplier.item.infrastructure.third.redbook.domain.dto.RedBookMessageDTO;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @class ThirdCallbackServiceImpl.java
 * @description 描述类的作用
 * @date 2024-02-29 14:40
 */
@Slf4j
@Service
public class ThirdCallbackServiceImpl implements ThirdCallbackService {

    @Autowired
    private RocketMQProducer rocketMQProducer;

    @Autowired
    KuaiShouSecurityCodec kuaiShouSecurityCodec;

    @Override
    public Map<String, Object> ksMessageCallback(String privateMessage) {
        String decodeMessage = kuaiShouSecurityCodec.decodeMessage(privateMessage);
        log.info("[快手回调消息] privateMessage={}, decodeMessage={}", privateMessage, decodeMessage);
        if (StrUtil.isEmpty(decodeMessage)) {
            return ksResult();
        }
        KsMessageDTO ksMessageDTO = JsonUtil.parse(decodeMessage, KsMessageDTO.class);
        if (Objects.isNull(ksMessageDTO)) {
            return ksResult();
        }
        //发送消息到mq
        rocketMQProducer.syncSend(ksMessageDTO, MQTopic.THIRD_CALLBACK_MESSAGE_KS_TOPIC);
        return ksResult();
    }


    private Map<String, Object> ksResult() {
        return MapUtil.of("result", 1);
    }


    @Override
    public Map<String, Object> redBookMessageCallback(String message) {
        log.info("[小红书回调消息] message={}", message);
        if (StrUtil.isEmpty(message)) {
            return redBookResult();
        }
        List<RedBookMessageDTO> redBookMessageDTOList = JsonUtil.parseList(message, RedBookMessageDTO.class);
        if (Objects.isNull(redBookMessageDTOList)) {
            return redBookResult();
        }
        for (RedBookMessageDTO redBookMessageDTO : redBookMessageDTOList) {
            rocketMQProducer.syncSend(redBookMessageDTO, MQTopic.THIRD_CALLBACK_MESSAGE_RED_BOOK_TOPIC);
        }
        return redBookResult();
    }

    private Map<String, Object> redBookResult() {
        Map<String, Object> response = MapUtil.newHashMap();
        response.put("success", true);
        response.put("error_code", 0);
        response.put("error_msg", "");
        return response;
    }
}
