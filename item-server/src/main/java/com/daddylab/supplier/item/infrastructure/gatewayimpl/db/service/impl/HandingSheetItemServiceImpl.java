package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetActivityEvent;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.HandingSheetActivityTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.HandingSheetItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.HandingSheetItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 盘货表关联的商品 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
@Service
public class HandingSheetItemServiceImpl extends DaddyServiceImpl<HandingSheetItemMapper, HandingSheetItem> implements HandingSheetItemService {
    @Autowired
    private HandingSheetItemMapper handingSheetItemMapper;


    @Override
    public Long getItemNumByHandingSheetId(Long handingSheetId) {
        if (handingSheetId == null || handingSheetId <= 0) {
            return 0L;
        }
        return handingSheetItemMapper.countItemNumByHandingSheetId(handingSheetId);
    }

    @Override
    public List<HandingSheetItem> listBySheetId(Long handingSheetId) {
        if (handingSheetId == null || handingSheetId <= 0) {
            return new ArrayList<>();
        }
        QueryWrapper<HandingSheetItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(HandingSheetItem::getHandingSheetId, handingSheetId);
        return handingSheetItemMapper.selectList(queryWrapper);
    }

    @Override
    public Map<Long, HandingSheetItem> getMap(Long handingSheetId) {
        List<HandingSheetItem> handingSheetItems = listBySheetId(handingSheetId);
        return getMap(handingSheetItems);
    }

    @Override
    public Map<Long, HandingSheetItem> getMap(List<HandingSheetItem> handingSheetItems) {
        if (CollectionUtil.isEmpty(handingSheetItems)) {
            return new HashMap<>();
        }
        Map<Long, HandingSheetItem> map = new HashMap<>((int) (handingSheetItems.size() / 0.75 + 1));
        for (HandingSheetItem handingSheetItem : handingSheetItems) {
            map.put(handingSheetItem.getId(), handingSheetItem);
        }
        return map;
    }

    @Override
    public int deleteBatchIds(List<Long> ids) {
        return handingSheetItemMapper.deleteBatchIds(ids);
    }

    @Override
    public long selectPageListCount(QueryWrapper<HandingSheetItem> queryWrapper) {
        return handingSheetItemMapper.selectPageListCount(queryWrapper);
    }

    @Override
    public IPage<HandingSheetItem> selectPageList(Page<HandingSheetItem> page, QueryWrapper<HandingSheetItem> queryWrapper) {
        return handingSheetItemMapper.selectPageList(page, queryWrapper);
    }

    @Override
    public long selectPageListCountForAudit(QueryWrapper<HandingSheetItem> queryWrapper) {
        return handingSheetItemMapper.selectPageListCountForAudit(queryWrapper);
    }

    @Override
    public IPage<HandingSheetItem> selectPageListForAudit(Page<HandingSheetItem> page, QueryWrapper<HandingSheetItem> queryWrapper) {
        return handingSheetItemMapper.selectPageListForAudit(page, queryWrapper);
    }

    @Override
    public BigDecimal calcArrivalPrice(BigDecimal activePrice, List<HandingSheetActivityEvent> activityEvents, Integer actType) {
        if (activePrice == null || activePrice.compareTo(BigDecimal.ZERO) < 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "活动价必须不能为负数");
        }
        if (CollectionUtil.isEmpty(activityEvents)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "请填写活动力度！");
        }
        for (HandingSheetActivityEvent activityEvent : activityEvents) {
            BigDecimal reachedAmount = activityEvent.getReachedAmount();
            BigDecimal reducedAmount = activityEvent.getReducedAmount();
            if (reachedAmount == null || reducedAmount == null) {
                throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "请填写活动力度！");
            }
            if (reachedAmount.compareTo(BigDecimal.ZERO) <= 0 || reducedAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "活动力度中的价格非法");
            }
        }
        if (activePrice.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        HandingSheetActivityTypeEnum actTypeEnum = HandingSheetActivityTypeEnum.of(actType);
        BigDecimal arrivalPrice = activePrice;
        if (Objects.equals(actTypeEnum, HandingSheetActivityTypeEnum.LOOP_FULL_REDUCTION)) {
            // 对于「循环满减来说」只有一条记录
            HandingSheetActivityEvent activityEvent = activityEvents.get(0);
            BigDecimal reachedAmount = activityEvent.getReachedAmount();
            BigDecimal reducedAmount = activityEvent.getReducedAmount();
            if (activePrice.compareTo(reachedAmount) >= 0) {
                BigDecimal divide = activePrice.divide(reachedAmount, 0, RoundingMode.DOWN);
                BigDecimal subtract = activePrice.subtract(reducedAmount.multiply(divide));
                arrivalPrice = subtract.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : subtract;
            }
        } else if (Objects.equals(actTypeEnum, HandingSheetActivityTypeEnum.LADDER_FULL_REDUCTION)) {
            // 按 ReachedAmount 从大到小排序
            List<HandingSheetActivityEvent> sortedActivityEvents = activityEvents.stream()
                    .sorted(Comparator.comparing(HandingSheetActivityEvent::getReachedAmount).reversed())
                    .collect(Collectors.toList());
            for (HandingSheetActivityEvent activityEvent : sortedActivityEvents) {
                BigDecimal reachedAmount = activityEvent.getReachedAmount();
                BigDecimal reducedAmount = activityEvent.getReducedAmount();
                if (activePrice.compareTo(reachedAmount) >= 0) {
                    BigDecimal subtract = activePrice.subtract(reducedAmount);
                    arrivalPrice = subtract.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : subtract;
                    break;
                }
            }
        } else {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "无法识别的活动类型，无法计算到手价");
        }
        return arrivalPrice;
    }

    public static void main(String[] args) {
        BigDecimal a = new BigDecimal("6.5");
        BigDecimal b = new BigDecimal("3.2");
        BigDecimal divide = a.divide(b, 0, RoundingMode.DOWN);
        System.out.println(divide);
    }

    @Override
    public List<HandingSheetItem> listByHandingSheetIdAndBuyerUid(Long handingSheetId, Long buyerUid) {
        return handingSheetItemMapper.selectListByHandingSheetIdAndBuyerUid(handingSheetId, buyerUid);
    }

    @Override
    public List<Boolean> listPassedColumnByHandingSheetId(Long handingSheetId) {
        return handingSheetItemMapper.listPassedColumnByHandingSheetId(handingSheetId);
    }
}
