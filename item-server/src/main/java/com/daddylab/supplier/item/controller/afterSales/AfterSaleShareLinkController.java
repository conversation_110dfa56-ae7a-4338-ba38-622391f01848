package com.daddylab.supplier.item.controller.afterSales;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSaleLink.AfterSaleShareLinkBizService;
import com.daddylab.supplier.item.application.afterSaleLink.dto.*;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> up
 * @date 2024年05月13日 4:06 PM
 */
@Slf4j
@RestController
@RequestMapping("/afterSaleShareLink")
@Api(value = "售后信息分享相关API", tags = "售后信息分享相关API")
@AllArgsConstructor
public class AfterSaleShareLinkController {

    private final AfterSaleShareLinkBizService afterSaleShareLinkBizService;

    private final OperateLogBizService operateLogBizService;



    @ResponseBody
    @ApiOperation(value = "保存链接信息")
    @PostMapping("/save")
    public SingleResponse<String> save(@RequestBody AfterSaleShareLinkCmd cmd) {
        return afterSaleShareLinkBizService.saveLink(cmd);
    }

    @ResponseBody
    @ApiOperation(value = "查询链接信息")
    @PostMapping("/query")
    public PageResponse<AfterSaleShareLinkVO> query(@RequestBody AfterSaleShareLinkQuery query) {
        return afterSaleShareLinkBizService.pageQuery(query);
    }

    @ResponseBody
    @ApiOperation(value = "查看链接信息详情")
    @GetMapping("/view")
    public SingleResponse<AfterSaleShareLinkViewVO> view(Long id) {
        return afterSaleShareLinkBizService.view(id);
    }


    @ResponseBody
    @ApiOperation(value = "删除链接")
    @GetMapping("/delete")
    public SingleResponse<Boolean> delete(Long id) {
        return afterSaleShareLinkBizService.delete(id);
    }


    @ResponseBody
    @ApiOperation(value = "操作记录")
    @GetMapping("/log")
    public MultiResponse<OperateLog> log(Long id) {
        return operateLogBizService.getOperateLogs(OperateLogTarget.AFTER_SALE_SHARE_LINK, id);
    }

    @ResponseBody
    @ApiOperation(value = "分享人列表")
    @GetMapping("/creator")
    public MultiResponse<LinkCreatorDto> creator() {
        return afterSaleShareLinkBizService.linkCreator();
    }



}
