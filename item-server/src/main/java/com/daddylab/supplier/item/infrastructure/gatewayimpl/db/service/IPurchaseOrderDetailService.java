package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrderDetail;

import java.util.List;

/**
 * <p>
 * 采购订单明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
public interface IPurchaseOrderDetailService extends IDaddyService<PurchaseOrderDetail> {

    /**
     * 根据采购订单id,
     *
     * @param query
     * @return
     */
    List<PurchaseOrderDetail> getDetailList(Long purchaseOrderId);

    Boolean haveDetailList(Long purchaseOrderId);


    List<String> getSkuCodeList(Long purchaseOrderId);

}
