package com.daddylab.supplier.item.application.saleItem.query;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("销售商品库商品分页查询请求")
public class SaleItemLibraryQueryPage extends PageQuery {

    private static final long serialVersionUID = -2314576715085451440L;

    @ApiModelProperty(value = "日常编码")
    private String itemSkuCode;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品ID")
    private String itemId;

    @ApiModelProperty(value = "采购人")
    private String buyerName;
}
