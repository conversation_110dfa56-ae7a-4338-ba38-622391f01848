package com.daddylab.supplier.item.application.purchase.combinationPrice;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年04月04日 11:52 AM
 */
@Data
public class PurchasePriceVo {

    /**
     * id
     */
    private Long id;

    /**
     * 编码，英文逗号隔开
     */
    private String code;


    private List<PriceDetailDto> priceDetailDtoList;

    /**
     * erp 的平台枚举值
     * 0:ALL 1:淘宝 2:抖店 3:小红书 4:自研电商 5:有赞 6:快手
     */
    private Integer platform;


    /**
     * 1:日常价。2 活动价
     */
    private Integer priceType;

    /**
     * 时间范围 开始时间
     */
    private Long startTime;

    /**
     * 时间范围 结束时间
     */
    private Long endTime;

}
