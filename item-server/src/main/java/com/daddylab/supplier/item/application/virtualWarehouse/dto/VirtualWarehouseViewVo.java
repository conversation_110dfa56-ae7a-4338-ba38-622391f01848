/*
package com.daddylab.supplier.item.application.virtualWarehouse.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryMode;
import lombok.Data;

import java.util.List;

*/
/**
 * <AUTHOR> up
 * @date 2024年03月06日 3:52 PM
 *//*

@Data
public class VirtualWarehouseViewVo {

    private Long id;

    private String no;

    private String name;

    private Integer status;

    private Integer businessLine;

    private String description;

    private Long updatedUid;

    private String updatedName;

    private String updatedAt;

    private Integer warehouseQuantity;

    private List<VirtualWarehousePageDetailVo> detailVoList;

    private InventoryMode inventoryMode;



}
*/
