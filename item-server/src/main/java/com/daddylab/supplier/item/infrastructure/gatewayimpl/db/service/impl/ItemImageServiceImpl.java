package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.daddylab.ark.sailor.common.enums.YesNoEnum;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemImageMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemImageService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品图片 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class ItemImageServiceImpl extends DaddyServiceImpl<ItemImageMapper, ItemImage> implements IItemImageService {


    @Override
    public Map<Long, String> getItemMainImageToMap(List<Long> itemIds) {
        LambdaQueryWrapper<ItemImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ItemImage::getItemId, itemIds)
                    .eq(ItemImage::getIsMain, YesNoEnum.YES.getValue());
        return baseMapper.selectList(queryWrapper)
                         .stream()
                         .collect(Collectors.toMap(ItemImage::getItemId, ItemImage::getImageUrl, (k1, k2) -> k1));
    }

    @Override
    public ItemImage getItemMainImage(Long itemId) {
        return lambdaQuery().eq(ItemImage::getIsMain, YesNoEnum.YES.getValue()).last("limit 1").one();
    }
}
