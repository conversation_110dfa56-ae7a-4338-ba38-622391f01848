package com.daddylab.supplier.item.controller.handingsheet.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/24 15:26
 * @Description: 盘货表关联的活动力度信息
 */
@Data
@ApiModel("盘货表活动力度出参")
public class HandingSheetActivityEventVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "满x元")
    private BigDecimal reachedAmount;

    @ApiModelProperty(value = "满y元")
    private BigDecimal reducedAmount;
}
