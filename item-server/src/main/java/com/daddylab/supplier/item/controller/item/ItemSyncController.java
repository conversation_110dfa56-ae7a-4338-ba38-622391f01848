package com.daddylab.supplier.item.controller.item;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.ark.sailor.item.domain.vo.category.CategoryListVO;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.itemSync.ThirdPlatformSyncBizService;
import com.daddylab.supplier.item.application.itemSync.types.ItemSyncTbParam;
import com.daddylab.supplier.item.application.saleItem.dto.DouDianSyncCmd;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.vo.ThirdSyncVO;
import com.daddylab.supplier.item.controller.item.dto.ItemMatchMiniProgramParam;
import com.daddylab.supplier.item.controller.item.dto.ItemMatchMiniProgramVo;
import com.daddylab.supplier.item.controller.item.dto.ItemSyncMiniProgramParam;
import com.daddylab.supplier.item.controller.item.dto.ThirdSyncPageQuery;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.types.item.MiniProgramCategoryListQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @Author: Wang Xiang
 * @Date: 2022/9/21 09:29
 * @Description: 请描述下这个类
 */
@Api(value = "商品同步相关API", tags = "商品同步相关API")
@RestController()
@RequestMapping("/item/sync")
@Slf4j
public class ItemSyncController {

    @Autowired
    ItemBizService itemBizService;

    @Autowired
    private ThirdPlatformSyncBizService thirdPlatformSyncBizService;

    @Resource
    NewGoodsBizService newGoodsBizService;

    @Resource
    RefreshConfig refreshConfig;

    @ResponseBody
    @ApiOperation("商品匹配小程序查询")
    @PostMapping("/itemMatchMiniProgram")
    public PageResponse<ItemMatchMiniProgramVo> itemMatchMiniProgram(
            @RequestBody @Valid ItemMatchMiniProgramParam param) {
        if (refreshConfig.getEnableMultiShopVersion()) {
            return itemBizService.itemMatchMiniProgram(param);
        } else {
            return itemBizService.itemMatchMiniProgramV1(param);
        }
    }

    @ResponseBody
    @ApiOperation("商品同步小程序")
    @PostMapping("/itemSyncMiniProgram")
    public SingleResponse<Boolean> itemSyncMiniProgram(
            @RequestBody List<ItemSyncMiniProgramParam> params) {
        return itemBizService.itemSyncMiniProgram(params);
    }

    @ApiOperation(value = "查询卖家类目列表", notes = "查询卖家类目列表", httpMethod = "GET")
    @GetMapping("/seller-category/list")
    @Auth("/item/sync/itemSyncMiniProgram")
    public MultiResponse<CategoryListVO> getMiniProgramSellerCategoryList(@Validated MiniProgramCategoryListQuery categoryQuery) {
        return itemBizService.getMiniProgramCategoryList(categoryQuery);
    }


    @ApiOperation("商品同步淘宝")
    @PostMapping("/syncTb")
    public Response syncTb(@Validated @RequestBody List<ItemSyncTbParam> params) {
        return thirdPlatformSyncBizService.syncTb(params);
    }

    @ResponseBody
    @PostMapping(value = "/douDianSync")
    @ApiOperation(value = "新品商品抖店同步")
    public Response douDianSync(@RequestBody List<DouDianSyncCmd> list) {
        newGoodsBizService.douDianSync(list);
        return Response.buildSuccess();
    }


    @ResponseBody
    @PostMapping(value = "/thirdSyncList")
    @ApiOperation(value = "同步列表")
    public PageResponse<ThirdSyncVO> thirdSyncList(
            @RequestBody @Validated ThirdSyncPageQuery thirdSyncPageQuery) {
        log.info("商品第三方同步列表查询 {}", thirdSyncPageQuery);
        return thirdPlatformSyncBizService.syncList(thirdSyncPageQuery);
    }


}
