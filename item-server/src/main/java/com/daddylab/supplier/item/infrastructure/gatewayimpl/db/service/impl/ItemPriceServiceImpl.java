package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemPrice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemPriceMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemPriceService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品价格 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class ItemPriceServiceImpl extends DaddyServiceImpl<ItemPriceMapper, ItemPrice> implements IItemPriceService {

}
