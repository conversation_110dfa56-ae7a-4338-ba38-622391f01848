package com.daddylab.supplier.item.application.payment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BusinessLine;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PaymentOrderStatus;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2023年11月20日 4:41 PM
 */
public class PaymentApplyTrans {


    public static String statusDesc(PaymentOrderStatus status) {
        return status.getDesc();
    }

    public static String getPurchaseOrgStr(String purchaseOrg) {
        /*if ("103".equals(purchaseOrg)) {
            return "杭州老爸电商科技有限公司";
        }
        return "UNKNOWN组织";*/
        return purchaseOrg;
    }

    public static String getPayOrgStr(String payOrg) {
        /*if ("103".equals(payOrg)) {
            return "杭州老爸电商科技有限公司";
        }
        return "UNKNOWN组织";*/
        return payOrg;
    }

    public static String getPayPurpose(Integer payPurpose) {
        if (0 == payPurpose) {
            return "采购付款";
        }
        if (1 == payPurpose) {
            return "预付款";
        }
        return "未知付款用途";
    }

    public static String getPayProportions(Integer payProportions) {
        return payProportions + "%";
    }

    public static String getExpectedPayTimeStr(Long expectedPayTime, String pattern) {

        return DateUtil.parseTimeStamp(expectedPayTime, pattern);
    }

    public static String getBusinessLine(Integer businessLine) {
        BusinessLine enumByValue = IEnum.getEnumByValue(BusinessLine.class, businessLine);
        return enumByValue.getDesc();
    }

    public static String getPurchaseType(Integer purchaseType) {
        if (0 == purchaseType) {
            return "标准采购";
        }
        if (1 == purchaseType) {
            return "工厂代发";
        }
        return "未知采购类型";
    }

    public static String getCurrencyType(Integer currencyType) {
        if (0 == currencyType) {
            return "人民币";
        }
        if (1 == currencyType) {
            return "美元";
        }
        return "未知币别";
    }

    public static String getDetailSource(Integer detailSource) {
        if (0 == detailSource) {
            return "采购单";
        }
        if (1 == detailSource) {
            return "结算单";
        }
        return "未知付款明细类型";
    }

    public static String getLockedStatusStr() {
        return PaymentOrderBizServiceImpl.LOCKED_STATUS_LIST.stream()
                .map(PaymentOrderStatus::getDesc).collect(Collectors.joining(StrUtil.COMMA));
    }

    public static String listToStr(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return StrUtil.EMPTY;
        }
        return StrUtil.join(StrUtil.COMMA, ids);
    }


}
