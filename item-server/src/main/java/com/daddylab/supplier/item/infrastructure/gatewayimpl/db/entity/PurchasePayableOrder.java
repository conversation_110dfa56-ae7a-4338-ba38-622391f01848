package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PurchasePayableOrderStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 采购应付表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PurchasePayableOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    private Long deletedAt;

    @TableLogic
    private Integer isDel;

    /**
     * 应付单号
     */
    private String no;

    /**
     * 应付类型。1采购入库应付、2采退出库应付、3其他应付
     */
    private Integer type;

    /**
     * 供应商id
     */
    private Long providerId;

    /**
     * 业务日期(指的是采购入库单、采退出库单下单日期)
     */
    private Long bizDate;

    /**
     * 商品种类数量
     */
    private Integer kindQuantity;

    /**
     * 商品总数
     */
    private Integer totalItemQuantity;

    /**
     * 价税合计
     */
    private BigDecimal totalPriceTax;

    /**
     * 采购组织id
     */
    private Long organizationId;

    /**
     * 关联单据(指采购入库单号/采退出库单号)
     */
    private String relatedOrderNo;

    /**
     * 关联单据id
     */
    private Long relatedOrderId;

    /**
     * 税后金额
     */
    private BigDecimal totalAfterTaxItemAmount;

    /**
     * 税额
     */
    private BigDecimal totalTaxAmount;

    /**
     * 关联入库单/出库单/其他应付单的采购员ID
     */
    private Long buyerId;

    private Integer businessLine;

//    /**
//     * 作废字段，新增 status 字段来表示多状态
//     */
//    @Deprecated
//    private Integer isHedge;

    /**
     * 状态
     */
    private PurchasePayableOrderStatus status;
}
