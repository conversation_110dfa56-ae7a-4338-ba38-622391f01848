package com.daddylab.supplier.item.controller.item.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Date: 2022/5/31 14:31
 * @Description: 请描述下这个类
 */
@Data
@ApiModel("商品上新计划基础信息")
public class ItemLaunchPlanBaseVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "计划ID")
    private Long planId;
    @ApiModelProperty(value = "计划名称")
    private String planName;
    @ApiModelProperty(value = "上新时间（时间戳，单位秒）")
    private Long launchTime;
    @ApiModelProperty("合作模式")
    private Integer businessLine;
}
