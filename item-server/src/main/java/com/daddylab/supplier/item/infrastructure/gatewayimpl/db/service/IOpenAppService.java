package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OpenApp;

/**
 * <p>
 * 开放平台应用（仅部分平台店铺授权到了多个应用需使用此表，其它平台配置还是在配置中心维护） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
public interface IOpenAppService extends IDaddyService<OpenApp> {

}
