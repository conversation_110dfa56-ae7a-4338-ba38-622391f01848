package com.daddylab.supplier.item.application.purchasePayable.query;

import com.alibaba.cola.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;

/**
 * <AUTHOR> up
 * @date 2023年02月06日 4:45 PM
 */
@ApiModel("申请付款详情明细列表")
@Data
public class ApplyPayOrderDetailListQueryPage extends PageQuery {

    private static final long serialVersionUID = 2220810361507706877L;
    @ApiModelProperty("付款申请单编号")
    @Valid
    String applyOrderNo;

}
