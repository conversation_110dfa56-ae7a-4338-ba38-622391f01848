package com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2022年11月16日 2:21 PM
 */
@Data
public class ActivityQuantitySingleDO {

    /**
     * 组合装编码
     */
    private String skuCode;

    /**
     * 优惠价格
     */
    private BigDecimal priceCost;

    /**
     * 优惠数量
     */
    private BigDecimal numCost;

    /**
     * erp活动开始结束时间
     */
    private Long startTime;
    private Long endTime;

    /**
     * erp平台
     */
    private Integer platformType;
}
