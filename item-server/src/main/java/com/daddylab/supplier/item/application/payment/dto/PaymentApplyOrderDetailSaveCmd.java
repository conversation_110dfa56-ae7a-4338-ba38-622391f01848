package com.daddylab.supplier.item.application.payment.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年12月01日 10:21 AM
 */
@Data
@ApiModel("付款申请单-明细保存请求封装")
public class PaymentApplyOrderDetailSaveCmd {

    @ApiModelProperty("明细ID")
    private Long id;

    @ApiModelProperty("关联付款明细单据的编码。结算单编码/采购单编码")
    private String applyRelatedNo;

    @ApiModelProperty("关联付款明细单据的id。结算单id/采购单id")
    private Long applyRelatedId;

    @ApiModelProperty("关联付款明细单据版本号")
    private Integer version;

    @ApiModelProperty("应付金额")
    private BigDecimal rightAmount;

    @ApiModelProperty("申请付款金额")
    private BigDecimal applyAmount;

    @ApiModelProperty("申请付款金额备注")
    private String applyAmountRemark;

    @ApiModelProperty("关联付款明细单据的明细列表ID。结算单明细ID/采购单明细ID")
    private List<Long> relateDetailIdList;

}
