package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(of = "id")
public class ReviewThesaurus {

    /**
     * ID
     */
    private Integer id;

	/**
	 * 词条名称
	 */
    private String name;

    /**
     * 类别
     */
    private String category;

    /**
     * 适用类型 1 限制使用 2 禁止使用
     */
    private Integer useType;

    /**
     * 命中内容
     */
    private List<String> context;
}