package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.TimeSchedule;

/**
 * <p>
 * 时间调度表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-01
 */
public interface TimeScheduleMapper extends DaddyBaseMapper<TimeSchedule> {

}
