package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 用户诺诺开票流程记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class NuoNuoInvoiceProcessRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 状态
     * <0 异常状态。-1.开票异常。-2:查询发票异常，-3.红冲异常,-9.订单重复开票，构建开票请求参数失败
     * =0 默认状态
     * >0 正常状态。1：开票完成。2:查询发票详情、3：红冲完成。
     */
    private Integer status;

    /**
     * 记录
     * 当状态为1时。诺税通发票流水号
     * 当状态为2时。诺税通发票详情接口返回JSON
     * 当状态为3时。冲红成功
     */
    private String record;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic(value = "0", delval = "id")
    private Long isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    public static NuoNuoInvoiceProcessRecord buildRecord(String orderNo, Integer status, String record) {

        NuoNuoInvoiceProcessRecord recordOne = new NuoNuoInvoiceProcessRecord();
        recordOne.setOrderNo(orderNo);
        recordOne.setStatus(status);
        recordOne.setRecord(record);
        return recordOne;
    }


}
