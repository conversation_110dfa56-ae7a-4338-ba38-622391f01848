package com.daddylab.supplier.item.controller.open.externalUser;

import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.auth.ExternalUserAuthBizService;
import com.daddylab.supplier.item.infrastructure.common.ExternalUserContext;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2024/5/29
 */
@Configuration
@AllArgsConstructor
public class ExternalUserAPIConfig implements WebMvcConfigurer {
    private final ExternalUserAuthBizService externalUserAuthBizService;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new HandlerInterceptor() {
                    @Override
                    public boolean preHandle(@NonNull HttpServletRequest request,
                                             @NonNull HttpServletResponse response, @NonNull Object handler) throws IOException {
                        ExternalUserContext.set(null);
                        if (externalUserAuthBizService.checkLogin()) {
                            return true;
                        }
                        Response errorResp = Response.buildFailure(String.valueOf(HttpServletResponse.SC_UNAUTHORIZED),
                                "账号授权已过期");
                        response.setContentType("application/json");
                        response.getWriter().write(JsonUtil.toJson(errorResp));
                        return false;
                    }
                })
                .order(Ordered.HIGHEST_PRECEDENCE)
                .addPathPatterns("/external/**")
                .excludePathPatterns("/external/auth/**")
                .excludePathPatterns("/external/afterSales/forwardingRegister/shareLink/**")
                .excludePathPatterns("/external/afterSales/forwardingRegister/shareLinkQuery")
        ;
    }
}
