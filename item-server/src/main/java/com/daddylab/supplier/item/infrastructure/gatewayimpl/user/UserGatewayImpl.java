package com.daddylab.supplier.item.infrastructure.gatewayimpl.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.departments.DepartmentGateway;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.accessControl.AccessControlAPI;
import com.daddylab.supplier.item.infrastructure.accessControl.StaffAcInfo;
import com.daddylab.supplier.item.infrastructure.accessControl.StaffAcInfoPageQuery;
import com.daddylab.supplier.item.infrastructure.config.LvSeJiaZhuangBuyerConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DadStaffMapper;
import com.daddylab.supplier.item.infrastructure.goclient.Rows;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.inject.Inject;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UserGatewayImpl implements UserGateway {
    private static List<StaffInfo> mockStaffInfos = null;

    @Inject
    private UserFeignClient userFeignClient;

    @Autowired
    private AccessControlAPI accessControlAPI;

    @Autowired
    private DepartmentGateway departmentGateway;

    @Resource
    LvSeJiaZhuangBuyerConfig lvSeJiaZhuangBuyerConfig;

    @Resource
    DadStaffMapper dadStaffMapper;

    public static List<StaffInfo> getMockStaffInfos() {
        if (mockStaffInfos == null) {
            loadStaffInfo();
        }
        return mockStaffInfos;
    }

    /**
     * 是否不去请求用户服务获取数据（本地环境无法请求到K8S环境内的POD，另外现在开发服务环境有问题也无法请求到）
     */
    private boolean isDoNotToQueryUserService() {
        return ApplicationContextUtil.isActiveProfile("dev", "test") && StringUtil.isNotBlank(RedisUtil.get("notQueryUserService"));
    }

    @Override
    //@BatchCall(batchClass = UserGateway.class, batchMethod = "batchQueryStaffInfoByIds")
    public StaffInfo queryStaffInfoById(Long userId) {
        return batchQueryStaffInfoByIds(Collections.singletonList(userId)).get(userId);
    }

    @Override
    public StaffInfo queryStaffInfoByNickname(String nickname) {
        final StaffListQuery staffListQuery = new StaffListQuery();
        staffListQuery.setNickname(nickname);
        return queryStaffList(staffListQuery).stream().findFirst().orElse(null);
    }

    @Override
    public Map<Long, StaffInfo> batchQueryStaffInfoByIds(List<Long> userIds) {
        log.debug("batchQueryStaffInfoByIds:{}", userIds);
        //如果是本地环境调用用户中心接口失败则直接从文件里头拿一个MOCK数据
        if (isDoNotToQueryUserService()) {
            return getMockStaffInfos().stream()
                    .filter(staffInfo -> userIds.contains(staffInfo.getUserId()))
                    .collect(Collectors.toMap(StaffInfo::getUserId, Function.identity()));
        }
        final int MAX_SIZE = 49;
        if (userIds.size() > MAX_SIZE) {
            return CollUtil.split(userIds, MAX_SIZE).parallelStream()
                    .map(this::batchQueryStaffInfoByIds0).flatMap(v -> v.entrySet().stream())
                    .collect(Collectors.toMap(Entry::getKey, Entry::getValue, (a, b) -> a));
        } else {
            return batchQueryStaffInfoByIds0(userIds);
        }
    }

    @NonNull
    private Map<Long, StaffInfo> batchQueryStaffInfoByIds0(List<Long> userIds) {
        if (CollectionUtil.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        final StaffListQuery query = new StaffListQuery();
        query.setUserIds(userIds);
        query.setPageSize(userIds.size());
        query.setStatus(-1);
        final List<StaffInfo> staffInfos = queryStaffList(query);
        if (CollUtil.isEmpty(staffInfos)) {
            return Collections.emptyMap();
        }
        return staffInfos.stream()
                .collect(Collectors.toMap(StaffInfo::getUserId, Function.identity()));
    }


    /**
     * @param query 查询条件
     * @return 员工信息
     */
    @Override
    public List<StaffInfo> queryStaffList(StaffListQuery query) {
        if (isDoNotToQueryUserService()) {
            return mockGetStaffInfos(query);
        }
        final StaffAcInfoPageQuery staffAcQuery = new StaffAcInfoPageQuery(query.getPageIndex(), query.getPageSize());
        final ArrayList<Long> deptIds = new ArrayList<>();
        if (StringUtil.isNotBlank(query.getDept())) {
            deptIds.addAll(departmentGateway.idAndDescendantIds(query.getDept()));
        }
        if (CollUtil.isNotEmpty(query.getDepts())) {
            for (String dept : query.getDepts()) {
                deptIds.addAll(departmentGateway.idAndDescendantIds(dept));
            }
        }
        if (CollUtil.isNotEmpty(query.getExactDepts())) {
            for (String dept : query.getExactDepts()) {
                deptIds.add(departmentGateway.id(dept));
            }
        }

        staffAcQuery.setDeptIds(deptIds.stream().distinct().collect(Collectors.toList()));
        if (StringUtil.isNotBlank(query.getMobile())) {
            staffAcQuery.setMobile(query.getMobile());
        }
        if (StringUtil.isNotBlank(query.getName())) {
            staffAcQuery.setRealname(query.getName());
        }
        if (StringUtil.isNotBlank(query.getNickname())) {
            staffAcQuery.setNickname(query.getNickname());
        }
        if (query.getStatus() != null && query.getStatus() > -1) {
            staffAcQuery.setStatus(query.getStatus());
        }
        if (Objects.nonNull(query.getUserId())) {
            staffAcQuery.setUserId(query.getUserId());
        }
        if (CollUtil.isNotEmpty(query.getUserIds())) {
            staffAcQuery.setUserIds(query.getUserIds());
        }
        final Rsp<Rows<StaffAcInfo>> response = accessControlAPI.staffAcInfoPageQuery(staffAcQuery);
        if (!response.isSuccess()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "查询用户列表失败");
        }
        List<StaffAcInfo> rows = response.getData().getRows();

        return UserTransMapper.INSTANCE.staffAcInfoListToStaffInfoList(rows);
    }


    private static void loadStaffInfo() {
        final String staffInfo = ResourceUtil.readUtf8Str("staff_info.json");
        mockStaffInfos = JsonUtil.parse(staffInfo, new TypeReference<List<StaffInfo>>() {
        });
    }

    @NonNull
    private List<StaffInfo> mockGetStaffInfos(StaffListQuery query) {

        return getMockStaffInfos().stream()
                .filter(staffInfo -> {
                    boolean filter = true;
                    if (StringUtil.isNotBlank(query.getName())) {
                        filter = staffInfo.getUserName().contains(query.getName());
                    }
                    return filter;
                })
                .skip(query.getPageIndex() - 1)
                .limit(query.getPageSize())
                .collect(Collectors.toList());
    }

    @Override
    public String queryNikcNameByLoginName(String loginName) {
        return dadStaffMapper.queryNickNameByLoginName(loginName);
    }
}
