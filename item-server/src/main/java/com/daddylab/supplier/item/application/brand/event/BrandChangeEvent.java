package com.daddylab.supplier.item.application.brand.event;

import com.daddylab.supplier.item.application.common.event.EntityChangeEvent;
import com.daddylab.supplier.item.common.domain.EntityChange;
import org.javers.core.diff.Diff;

public class BrandChangeEvent <T> extends EntityChangeEvent<T> {
   

    public BrandChangeEvent(Long operatorId, Long targetId, EntityChange<T> entityChange) {
        super(operatorId, targetId, entityChange);
    }

    public static <T> BrandChangeEvent<T> ofNew(Long operatorId, Long targetId, T newEntity) {
        return new BrandChangeEvent<>(operatorId, targetId, EntityChange.ofAdd(newEntity));
    }

    public static <T> EntityChangeEvent<T> ofUpdate(Long operatorId, Long targetId, Diff diff, T oldEntity, T newEntity) {
        return new BrandChangeEvent<>(operatorId, targetId, EntityChange.ofUpdate(diff, oldEntity, newEntity));
    }

    public static <T> EntityChangeEvent<T> ofUpdate(Long operatorId, Long targetId, EntityChange<T> entityChange) {
        return new BrandChangeEvent<>(operatorId, targetId, entityChange);
    }
}
