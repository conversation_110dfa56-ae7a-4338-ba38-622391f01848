package com.daddylab.supplier.item.infrastructure.gatewayimpl.item;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.application.category.CategoryBizService;
import com.daddylab.supplier.item.application.itemTag.ItemTagBizService;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsVo;
import com.daddylab.supplier.item.common.ErrorChecker;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.ItemTransMapper;
import com.daddylab.supplier.item.controller.category.dto.CategoryTree;
import com.daddylab.supplier.item.controller.category.dto.CategoryTreeNode;
import com.daddylab.supplier.item.controller.item.dto.ItemAttrDto;
import com.daddylab.supplier.item.controller.item.dto.ItemSkuSpecVo;
import com.daddylab.supplier.item.controller.item.dto.ItemSpecPageQuery;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemCmd;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerPersonCmd;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.item.gateway.*;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemBaseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuAttrRefDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BizUnionTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemImageType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBizLevelDivisionService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemProcurementService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemReq;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerPersonReq;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerPersonResp;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/9 10:48 上午
 * @description
 */
@Service
@Slf4j
public class ItemGatewayImpl implements ItemGateway {

  @Autowired ItemMapper itemMapper;

  @Autowired IItemService iItemService;

  @Autowired ItemPriceGateway itemPriceGateway;

  @Autowired ItemSkuGateway itemSkuGateway;

  @Autowired PartnerFeignClient partnerFeignClient;

  @Autowired ItemImageGateway itemImageGateway;

  @Autowired ItemOperationGateway itemOperationGateway;

  @Autowired ItemExpressGateway itemExpressGateway;

  @Autowired BuyerGateway buyerGateway;

  @Autowired ItemProcurementGateway itemProcurementGateway;

  @Autowired IItemProcurementService itemProcurementService;

  @Autowired IItemSkuService iItemSkuService;

  @Autowired private CategoryBizService categoryBizService;

  @Resource CategoryGateway categoryGateway;

  @Autowired private ItemTagBizService itemTagBizService;

  @Resource IBizLevelDivisionService iBizLevelDivisionService;

  @Override
  public ItemBaseDO getBaseInfo(Long itemId) {
    Assert.notNull(itemId, "itemId不得为空");
    final ItemBaseDO itemBaseDo = itemMapper.queryDetailBase(itemId);
    Assert.notNull(itemBaseDo, "item基础信息查询为空，itemId非法。itemId:" + itemId);
    final SingleResponse<CategoryTree> categoryTreeResponse = categoryBizService.categoryTree();
    ErrorChecker.checkAndThrowIfError(categoryTreeResponse);
    final CategoryTree categoryTree = categoryTreeResponse.getData();
    final CategoryTreeNode categoryTreeNode =
        categoryTree.getNodeMap().get(itemBaseDo.getCategoryId());
    Assert.notNull(categoryTreeNode, "商品类目查询为空");
    itemBaseDo.setProps(categoryTreeNode.getAllProps());

    Long categoryId = itemBaseDo.getCategoryId();
    List<Category> upperCategoryList = categoryGateway.getUpperCategoryList(categoryId);
    String categoryIdPath =
        upperCategoryList.stream()
            .map(val -> String.valueOf(val.getId()))
            .collect(Collectors.joining("/"));
    itemBaseDo.setCategoryIdPath(categoryIdPath);

    itemBaseDo.setTags(itemTagBizService.listByItemId(itemId));
    return itemBaseDo;
  }

  @Override
  public ItemBaseDO getBaseInfoWithImage(Long itemId) {
    return itemMapper.queryDetailBaseWithImage(itemId);
  }

  @Override
  public List<ItemBaseDO> getBaseInfoWithImage(List<Long> itemIds) {
    return itemMapper.queryDetailBaseWithImageByIds(itemIds);
  }

  @Override
  public List<ItemPrice> getPriceList(Long itemId) {
    return itemPriceGateway.getPriceList(itemId);
  }

  @Override
  public List<ItemSku> getSkuList(Long itemId) {
    return itemSkuGateway.getSkuList(itemId);
  }

  @Override
  public void removeSku(List<Long> skuIdList) {
    iItemSkuService.removeByIdsWithTime(skuIdList);
  }

  @Override
  public Optional<String> getLatestSkuCode(Long itemId) {
    return itemSkuGateway.getLatestSkuCode(itemId);
  }

  @Override
  public List<SkuAttrRefDO> getSkuAttrList(List<Long> skuIdList) {
    return itemSkuGateway.getSkuAttrList(skuIdList);
  }

  @Override
  public List<ItemImage> getImageListByType(Long itemId, ItemImageType itemImageType) {
    return itemImageGateway.getImageListByType(itemId, itemImageType);
  }

  @Override
  public List<String> getImageUrlList(Long itemId, ItemImageType itemImageType) {
    if (NumberUtil.isPositive(itemId)) {
      final List<ItemImage> itemImages = itemImageGateway.getImageListByType(itemId, itemImageType);
      return itemImages.stream().map(ItemImage::getImageUrl).collect(Collectors.toList());
    }
    return Collections.emptyList();
  }

  @Override
  public List<ItemOperator> getItemOperation(Long itemId) {
    return itemOperationGateway.getItemOperation(itemId);
  }

  @Override
  public void saveOrUpdateOperation(ItemOperator itemOperator) {
    itemOperationGateway.saveOrUpdateOperation(itemOperator);
  }

  @Override
  public void saveOrUpdateBatchOperation(List<ItemOperator> itemOperator) {
    itemOperationGateway.saveOrUpdateBatchOperation(itemOperator);
  }

  @Override
  public Item getItem(Long itemId) {
    return iItemService.getById(itemId);
  }

  @Override
  public Item getItem(String itemNo) {
    return iItemService.lambdaQuery().eq(Item::getCode, itemNo).one();
  }

  @Override
  public Long getProviderIdBySkuCode(String skuCode) {
    return itemMapper.selectProviderId(skuCode);
  }

  @Override
  public List<Item> getItemBatchByIds(List<Long> itemIds) {
    return iItemService.selectBatchByIds(itemIds);
  }

  @Override
  public List<Item> getItemBatchByCodes(List<String> itemCodes) {
    return iItemService
        .lambdaQuery()
        .in(Item::getCode, itemCodes)
        .or()
        .in(Item::getProviderSpecifiedCode, itemCodes)
        .list();
  }

  @Override
  public List<Item> getItemBatchByPsysCode(List<String> psysCodes) {
    return iItemService.lambdaQuery().in(Item::getPartnerProviderItemSn, psysCodes).list();
  }

  @Override
  public Map<Long, String> getItemNameMap(List<Long> idList) {
    if (CollectionUtils.isNotEmpty(idList)) {
      List<Item> items = iItemService.listByIds(idList);
      if (CollectionUtils.isNotEmpty(items)) {
        return items.stream().collect(Collectors.toMap(Item::getId, Item::getName, (a, b) -> a));
      }
    }
    return new HashMap<>();
  }

  @Override
  @Deprecated
  public void saveOrUpdateBatchPrice(List<ItemPrice> list) {
    itemPriceGateway.saveOrUpdateBatchPrice(list);
  }

  @Override
  public void removePriceWithIds(List<Long> itemPriceIds) {
    itemPriceGateway.removePriceWithIds(itemPriceIds);
  }

  @Override
  public void saveOrUpdateBatchImage(List<ItemImage> list) {
    itemImageGateway.saveOrUpdateBatchImage(list);
  }

  @Override
  public void removeImages(List<Long> itemImageIds) {
    itemImageGateway.removeImages(itemImageIds);
  }

  @Override
  public void saveOrUpdateBatchExpress(List<ItemExpressTemplate> list) {
    itemExpressGateway.saveOrUpdateBatchExpress(list);
  }

  @Override
  public List<ItemExpressTemplate> getExpressByItemId(Long itemId) {
    return itemExpressGateway.getExpressByItemId(itemId);
  }

  @Override
  public ItemExpressTemplate getExpressTemplateByItemId(Long itemId) {
    return itemExpressGateway.getExpressTemplateByItemId(itemId);
  }

  @Override
  public void saveOrUpdateProcurement(ItemProcurement itemProcurement) {
    itemProcurementGateway.saveOrUpdateProcurement(itemProcurement);
  }

  @Override
  public ItemProcurement getProcurementByItemId(Long itemId) {
    return itemProcurementGateway.getProcurementByItemId(itemId);
  }

  @Override
  public Long saveItemSkuReturnId(ItemSku itemSku) {
    return itemSkuGateway.saveItemSkuReturnId(itemSku);
  }

  @Override
  public void updateSkuAttr(ItemAttrDto itemAttrDto) {
    itemSkuGateway.updateItemAttr(itemAttrDto);
  }

  @Override
  public void saveOrUpdateBatchItemSku(List<ItemSku> list) {
    itemSkuGateway.saveOrUpdateBatchItemSku(list);
  }

  @Override
  public Long saveItemAttrReturnId(ItemAttr itemAttr) {
    return itemSkuGateway.saveItemAttrReturnId(itemAttr);
  }

  @Override
  public void saveBatchItemSkuAttrRef(List<ItemSkuAttrRef> list) {
    itemSkuGateway.saveBatchItemSkuAttrRef(list);
  }

  @Override
  public Long saveItem(Item item) {
    iItemService.saveOrUpdate(item);
    return item.getId();
  }

  @Override
  public Optional<String> getLatestItemCode() {
    return Optional.ofNullable(itemMapper.getLatestItemCode());
  }

  @Override
  public Boolean isRepeatPartnerProviderItemSn(String partnerProviderItemSn) {
    QueryWrapper<Item> queryWrapper = new QueryWrapper<>();
    queryWrapper.lambda().eq(Item::getPartnerProviderItemSn, partnerProviderItemSn);
    return iItemService.count(queryWrapper) > 0;
  }

  @Override
  public Boolean isRepeat(String itemName) {
    Assert.hasText(itemName, "商品名字不得为空");

    QueryWrapper<Item> queryWrapper = new QueryWrapper<>();
    queryWrapper.lambda().eq(Item::getName, itemName);
    return iItemService.count(queryWrapper) > 0;
  }

  @Override
  public List<PartnerItemResp> partnerQuery(PartnerItemCmd cmd) {
    if (StringUtil.isBlank(cmd.getContext())) {
      return Collections.emptyList();
    }

    final PartnerItemReq partnerItemReq = ItemTransMapper.INSTANCE.partnerCmdToReq(cmd);
    log.info("查询合作伙伴系统参数,req:{}", JsonUtil.objToStr(partnerItemReq));

    Rsp<List<PartnerItemResp>> listRsp;
    try {
      listRsp = partnerFeignClient.itemQuery(partnerItemReq);
    } catch (Exception e) {
      log.error("查询合作伙伴系统商品信息异常", e);
      throw ExceptionPlusFactory.bizException(
          ErrorCode.ITEM_BIZ_ERROR, "查询合作伙伴系统商品信息异常," + e.getMessage());
    }
    if (listRsp != null && listRsp.getFlag()) {
      return Optional.ofNullable(listRsp.getData()).orElseGet(Collections::emptyList);
    }
    throw ExceptionPlusFactory.bizException(
        ErrorCode.ITEM_BIZ_ERROR, "查询合作伙伴系统商品信息返回失败," + listRsp.getMsg());
  }

  @Override
  public Optional<PartnerItemResp> partnerQueryByCode(String code) {
    final PartnerItemCmd partnerItemCmd = new PartnerItemCmd();
    partnerItemCmd.setSearchType(1);
    partnerItemCmd.setContext(code);
    partnerItemCmd.setPageIndex(1);
    partnerItemCmd.setPageSize(10);
    final List<PartnerItemResp> partnerItemResps = partnerQuery(partnerItemCmd);
    return partnerItemResps.isEmpty() ? Optional.empty() : Optional.of(partnerItemResps.get(0));
  }

  @Override
  public boolean existBrandAssociativeItem(Long brandId) {
    return iItemService.lambdaQuery().eq(Item::getBrandId, brandId).last("LIMIT 1").count() > 0;
  }

  @Override
  public Long saveOrUpdateBuyer(Long userId, String userName) {
    return buyerGateway.saveOrUpdateBuyer(userId, userName);
  }

  @Override
  public void fillingSkuKingDeeId(Long skuId, String kingDeeId) {
    itemSkuGateway.setSkuKingDeeId(skuId, kingDeeId);
  }

  @Override
  public Integer getSkuCount(Long itemId) {
    return itemSkuGateway.getSkuCount(itemId);
  }

  @Override
  public List<Long> getRunnerIdList(Long itemId) {
    return itemOperationGateway.getRunnerIdList(itemId);
  }

  @Override
  public void removeRunner(List<Long> runnerId) {
    itemOperationGateway.removeRunner(runnerId);
  }

  @Override
  public List<Item> getSonItem(Long itemId) {
    QueryWrapper<Item> wrapper = new QueryWrapper<>();
    wrapper.lambda().eq(Item::getParentItemId, itemId);
    return iItemService.list(wrapper);
  }

  @Override
  public Boolean isSkuCodeRepeat(String skuCode) {
    return itemSkuGateway.verifySkuCode(skuCode);
  }

  @Override
  public Boolean isSpecialSkuCodeRepeat(String specialCode) {
    return itemSkuGateway.isSpecialSkuCodeRepeat(specialCode);
  }

  @Override
  public Boolean isSkuBarCodeRepeat(String barCode) {
    return itemSkuGateway.isSkuBarCodeRepeat(barCode);
  }

  @Override
  public Boolean isSpecialItemCodeRepeat(String specialCode) {
    QueryWrapper<Item> queryWrapper = new QueryWrapper<>();
    queryWrapper.lambda().eq(Item::getProviderSpecifiedCode, specialCode);
    return iItemService.count(queryWrapper) > 0;
  }

  @Override
  public Optional<ItemSku> getByItemSkuId(Long id) {
    return itemSkuGateway.getByItemSkuId(id);
  }

  @Override
  public void removeExpress(List<Long> idList) {
    itemExpressGateway.removeExpress(idList);
  }

  @Override
  public PurchaseItem getPurchaseBySku(String itemSku) {
    return iItemService.getPurchaseBySku(itemSku);
  }

  @Override
  public NewGoodsVo getNewGoodsById(Long id) {
    return iItemService.getNewGoodsById(id);
  }

  @Override
  public void setIsWdtSynced(Long itemId) {
    iItemService.lambdaUpdate().set(Item::getIsWdtSynced, true).eq(Item::getId, itemId).update();
  }

  @Override
  public List<PartnerPersonResp> partnerPersonQuery(PartnerPersonCmd cmd) {
    log.info("查询合作伙伴系统参数,req:{}", JsonUtil.objToStr(cmd.getRole_type()));

    Rsp<List<PartnerPersonResp>> listRsp;
    PartnerPersonReq partnerPersonReq = new PartnerPersonReq();
    partnerPersonReq.setRole_type(cmd.getRole_type());
    try {
      listRsp = partnerFeignClient.personIdsQuery(partnerPersonReq);
    } catch (Exception e) {
      log.error("查询合作伙伴员工信息异常", e);
      throw ExceptionPlusFactory.bizException(
          ErrorCode.ITEM_BIZ_ERROR, "查询合作伙伴系统员工信息异常," + e.getMessage());
    }
    if (listRsp.getFlag()) {
      return listRsp.getData();
    }
    throw ExceptionPlusFactory.bizException(
        ErrorCode.ITEM_BIZ_ERROR, "查询合作伙伴系统员工信息返回失败," + listRsp.getMsg());
  }

  @Override
  public PageResponse<ItemSkuSpecVo> getListBySpec(ItemSpecPageQuery query) {
    return itemSkuGateway.getListBySpec(query);
  }

  @Override
  public String getItemCodeByItemId(Long itemId) {
    return iItemService
        .lambdaQuery()
        .eq(Item::getId, itemId)
        .select(Item::getCode)
        .oneOpt()
        .map(Item::getCode)
        .orElse(null);
  }

  @Override
  public List<Item> getItemList() {
    return iItemService.list();
  }

  @Override
  public boolean setWarehouse(Long itemId, String warehouseNo) {
    final Item item = new Item();
    item.setId(itemId);
    item.setWarehouseNo(warehouseNo);
    final boolean update = iItemService.updateById(item);
    if (!update) {
      return false;
    }
    itemProcurementService
        .lambdaUpdate()
        .eq(ItemProcurement::getItemId, itemId)
        .set(ItemProcurement::getWarehouseNo, warehouseNo)
        .update();
    iItemSkuService
        .lambdaUpdate()
        .eq(ItemSku::getItemId, itemId)
        .set(ItemSku::getWarehouseNo, warehouseNo)
        .update();
    return true;
  }

  @Override
  public Integer getBusinessLine(Long itemId) {
    final List<Integer> levelValList =
        iBizLevelDivisionService
            .lambdaQuery()
            .eq(BizLevelDivision::getBizId, itemId)
            .eq(BizLevelDivision::getType, BizUnionTypeEnum.SPU)
            .eq(BizLevelDivision::getLevel, DivisionLevelEnum.COOPERATION)
            .list()
            .stream()
            .map(val -> val.getLevelVal().getValue())
            .collect(Collectors.toList());

    if (CollectionUtil.isNotEmpty(levelValList)) {
      //            if (levelValList.size() > 1) {
      //                return 0;
      //            }
      return levelValList.get(0);
    }
    return 0;
  }

  @Override
  public Map<String, Item> queryItemBatchBySkuCodes(Collection<String> skuCodes) {
    if (CollectionUtils.isEmpty(skuCodes)) return new HashMap<>();
    final List<ItemSku> itemSkuList =
        iItemSkuService
            .lambdaQuery()
            .in(ItemSku::getSkuCode, skuCodes)
            .or()
            .in(ItemSku::getProviderSpecifiedCode, skuCodes)
            .list();
    Map<String, Long> skuCodeAndItemIdMap = new HashMap<>(skuCodes.size());
    for (String skuCode : skuCodes) {
      itemSkuList.stream()
          .filter(
              val ->
                  val.getSkuCode().equals(skuCode)
                      || val.getProviderSpecifiedCode().equals(skuCode))
          .findFirst()
          .ifPresent(val -> skuCodeAndItemIdMap.put(skuCode, val.getItemId()));
    }
    if (MapUtils.isEmpty(skuCodeAndItemIdMap)) return new HashMap<>();
    final Map<Long, Item> itemMap =
        iItemService.lambdaQuery().in(Item::getId, skuCodeAndItemIdMap.values()).list().stream()
            .collect(Collectors.toMap(Item::getId, Function.identity()));

    Map<String, Item> resMap = new HashMap<>(skuCodes.size());
    skuCodeAndItemIdMap.forEach(
        (skuCode, itemId) -> {
          final Item item = itemMap.get(itemId);
          resMap.put(skuCode, item);
        });

    return resMap;
  }
}
