package com.daddylab.supplier.item.application.message;

import com.google.common.collect.Lists;
import java.util.Collections;
import java.util.List;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2025/1/15
 */
@ConfigurationProperties(prefix = "qy-msg")
@RefreshScope
@Data
@Configuration
public class QyMsgConfig {
  /** 白名单用户id列表 */
  private List<String> whiteList = Collections.emptyList();

  /**
   * 消息真实发送环境
   */
  private List<String> realSendEnvs = Lists.newArrayList("prod");
}
