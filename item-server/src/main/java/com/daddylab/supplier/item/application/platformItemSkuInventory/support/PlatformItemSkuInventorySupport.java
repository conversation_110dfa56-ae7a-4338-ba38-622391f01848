package com.daddylab.supplier.item.application.platformItemSkuInventory.support;

import com.daddylab.supplier.item.application.platformItemSkuInventory.PlatformItemSkuSyncService;
import com.daddylab.supplier.item.application.platformItemSkuInventory.domain.dto.PlatformItemChangeEvent;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemSkuInventoryService;
import lombok.NonNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @class PlatformItemSkuInventorySupport.java
 * @description 描述类的作用
 * @date 2024-03-14 14:28
 */
@Component
public class PlatformItemSkuInventorySupport implements SmartInitializingSingleton, ApplicationContextAware {

    private ApplicationContext applicationContext;
    private static final Map<Platform, PlatformItemSkuSyncService> PLATFORM_ENUM_PLATFORM_ITEM_SKU_SYNC_SERVICE_MAP = new ConcurrentHashMap<>();
    @Autowired
    private IPlatformItemSkuInventoryService platformItemSkuInventoryService;

    /**
     * 调度消息
     *
     * @param platformItemChangeEvent PlatformItemSkuInventory
     * @date 2024/3/14 14:47
     * <AUTHOR>
     */
    public void handler(PlatformItemChangeEvent platformItemChangeEvent) {
        Platform  platform = platformItemChangeEvent.getPlatform();
        if (Objects.isNull(platformItemChangeEvent.getPlatform())) {
            return;
        }
        PlatformItemSkuSyncService platformItemSkuSyncService = PLATFORM_ENUM_PLATFORM_ITEM_SKU_SYNC_SERVICE_MAP.get(platform);
        if (Objects.isNull(platformItemSkuSyncService)) {
            return;
        }
        platformItemSkuSyncService.incrementSync(Collections.singletonList(platformItemChangeEvent));
    }


    @Override
    public void afterSingletonsInstantiated() {
        Map<String, PlatformItemSkuSyncService> beansOfType = applicationContext.getBeansOfType(PlatformItemSkuSyncService.class);
        beansOfType.forEach((name, bean) -> {
            PLATFORM_ENUM_PLATFORM_ITEM_SKU_SYNC_SERVICE_MAP.put(bean.defaultType(), bean);
        });
    }

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
