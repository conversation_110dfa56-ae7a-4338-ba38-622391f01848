package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemOptimizeParticipant;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 商品优化参与者 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface ItemOptimizeParticipantMapper extends DaddyBaseMapper<ItemOptimizeParticipant> {

    List<ItemOptimizeParticipant> selectBatchByTypeAndUid(Collection<ItemOptimizeParticipant> participantsFilter);
}
