package com.daddylab.supplier.item.infrastructure.third.annotations;

import com.daddylab.supplier.item.infrastructure.third.enums.KsMessageEnum;
import com.daddylab.supplier.item.infrastructure.third.enums.RedBookMessageEnum;

import java.lang.annotation.*;

/**
 *
 * @date 2024/3/1 15:56
 * <AUTHOR>
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface KsHandlerMapping {
    KsMessageEnum value();
}
