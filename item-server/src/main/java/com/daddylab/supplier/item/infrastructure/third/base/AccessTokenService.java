package com.daddylab.supplier.item.infrastructure.third.base;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.domain.third.dto.AccessTokenForm;
import com.daddylab.supplier.item.infrastructure.third.enums.AccessTokenTypeEnum;

/**
 * <AUTHOR>
 * @class AccessTokenService.java
 * @description 描述类的作用
 * @date 2024-02-27 17:54
 */
public interface AccessTokenService {

    /**
     * 设置保存accessToken
     *
     * @param tokenEnum AccessTokenEnum
     * @param accessToken String
     * @return com.alibaba.cola.dto.SingleResponse<java.lang.Boolean>
     * @date 2024/2/27 17:59
     * <AUTHOR>
     */
    SingleResponse<Boolean> saveAccessToken(AccessTokenTypeEnum tokenEnum, AccessTokenForm accessToken);

    /**
     * 获取accessToken
     *
     * @param accessTokenEnum AccessTokenEnum
     * @return java.lang.String
     * @date 2024/2/27 17:48
     * <AUTHOR>
     */
    String getAccessToken(AccessTokenTypeEnum accessTokenEnum);
}
