package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAuditStats;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;

/**
 * <p>
 * 商品库抽屉模块审核统计数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
public interface IItemDrawerModuleAuditStatsService extends IDaddyService<ItemDrawerModuleAuditStats> {

    ItemDrawerModuleAuditStats getModuleAuditStatsCreateIfNotExists(ItemAuditType type, Long itemId, Integer round);
}
