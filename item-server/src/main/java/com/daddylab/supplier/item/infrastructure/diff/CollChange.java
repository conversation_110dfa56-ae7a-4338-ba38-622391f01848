package com.daddylab.supplier.item.infrastructure.diff;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.javers.core.diff.Diff;

/**
 * <AUTHOR>
 * @since 2022/8/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CollChange<T> implements Serializable {

    private static final long serialVersionUID = -481997961594526318L;
    private Collection<T> left;
    private Collection<T> right;
    private List<T> addedValues;
    private List<T> removedValues;
    private List<Pair<T, T>> updatedValues;
    private Diff diff;
}
