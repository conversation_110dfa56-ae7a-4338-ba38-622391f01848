package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockSyncDirection;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockSyncStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 库存同步记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StockSyncRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间createAt
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 创建人updateUser
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新时间updateAt
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 更新人updateUser
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 是否已删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 删除时间
     */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /**
     * 平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城 8:拼多多...
     */
    private Platform platform;

    /**
     * 同步方向 0:从平台下载 1:上传到平台
     */
    private StockSyncDirection syncDirection;

    /**
     * 同步状态 0:失败 1:成功
     */
    private StockSyncStatus syncStatus;

    /**
     * 消息备注
     */
    private String msg;

    /**
     * 平台商品ID
     */
    private String outerItemId;

    /**
     * 平台商品SKU ID
     */
    private String outerSkuId;

    /**
     * 商品SPU编码
     */
    private String itemCode;

    /**
     * 商品SKU编码
     */
    private String skuCode;

    /**
     * 同步前库存
     */
    private Integer beforeStock;

    /**
     * 同步库存
     */
    private Integer stock;


}
