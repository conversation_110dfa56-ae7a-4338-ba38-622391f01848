package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetStaff;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.HandingSheetStaffMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.HandingSheetStaffService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 盘货表关联的职员 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
@Service
public class HandingSheetStaffServiceImpl extends DaddyServiceImpl<HandingSheetStaffMapper, HandingSheetStaff>
        implements HandingSheetStaffService {
    @Autowired
    private HandingSheetStaffMapper handingSheetStaffMapper;

    @Override
    public List<HandingSheetStaff> listBySheetId(Long handingSheetId) {
        if (handingSheetId == null || handingSheetId <= 0) {
            return new ArrayList<>();
        }
        QueryWrapper<HandingSheetStaff> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(HandingSheetStaff::getHandingSheetId, handingSheetId);
        return handingSheetStaffMapper.selectList(queryWrapper);
    }

    @Override
    public void deleteBySheetId(Long handingSheetId) {
        handingSheetStaffMapper.deleteBySheetId(handingSheetId);
    }
}
