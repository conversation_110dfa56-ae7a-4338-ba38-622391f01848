package com.daddylab.supplier.item.application.afterSalesRegister;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;

import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AfterSalesRegisterPageQuery extends PageQuery {
    private static final long serialVersionUID = -557438339287981682L;

    @ApiModelProperty(value = "订单号")
    String orderNo;

    @ApiModelProperty(value = "商品编码")
    String itemCode;

    @ApiModelProperty(value = "商品名称")
    String itemName;

    @ApiModelProperty(value = "店铺名称")
    String shopName;

    @ApiModelProperty(value = "付款时间")
    Long payTimeStart;

    @ApiModelProperty(value = "付款时间")
    Long payTimeEnd;

    @ApiModelProperty(value = "发货仓")
    String warehouse;

    @ApiModelProperty(value = "发货仓（多选）")
    List<String> warehouseNos;

    @ApiModelProperty(value = "结算周期（起始时间）")
    Long settleTimeStart;

    @ApiModelProperty(value = "结算周期（结束时间）")
    Long settleTimeEnd;

    @ApiModelProperty(value = "结算周期（区间多选）")
    Long[] settleDurations;

    @ApiModelProperty(value = "订单员用户ID（多选）")
    List<Long> orderPersonnelIds;

//    private List<Integer> businessLine;

    @ApiModelProperty(value = "合作方")
    private List<Integer> corpType;

    @ApiModelProperty(value = "业务类型")
    private List<Integer> bizType;
}
