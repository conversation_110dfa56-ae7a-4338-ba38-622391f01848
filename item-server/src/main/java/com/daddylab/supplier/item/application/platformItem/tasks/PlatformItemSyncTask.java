package com.daddylab.supplier.item.application.platformItem.tasks;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.message.event.PlatformWarnMessageEvent;
import com.daddylab.supplier.item.domain.platformItem.service.PlatformItemSyncService;
import com.daddylab.supplier.item.domain.platformItem.service.SyncResult;
import com.daddylab.supplier.item.domain.platformItem.service.SyncStatus;
import com.daddylab.supplier.item.domain.wdt.dataSync.DataSyncContext;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataSync;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtPlatformGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DataSyncStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DataSyncType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDataSyncService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtPlatformGoodsService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/12/31
 * @deprecated 已废弃
 */
@Component
@Slf4j
public class PlatformItemSyncTask {
    final private IWdtPlatformGoodsService wdtPlatformGoodsService;
    final private IDataSyncService dataSyncService;
    final private PlatformItemSyncService platformItemSyncService;

    RefreshConfig refreshConfig;

    @Autowired
    public PlatformItemSyncTask(
            IWdtPlatformGoodsService wdtPlatformGoodsService,
            IDataSyncService dataSyncService,
            PlatformItemSyncService platformItemSyncService) {
        this.wdtPlatformGoodsService = wdtPlatformGoodsService;
        this.dataSyncService = dataSyncService;
        this.platformItemSyncService = platformItemSyncService;
    }

    @Autowired
    public void setRefreshConfig(RefreshConfig refreshConfig) {
        this.refreshConfig = refreshConfig;
    }

    private boolean isDisablePlatformItemSyncMessage() {
        final Boolean disablePlatformItemSyncMessage = refreshConfig.getDisablePlatformItemSyncMessage();
        return disablePlatformItemSyncMessage != null && disablePlatformItemSyncMessage;
    }

    private boolean isDisablePlatformItemSync() {
        final Boolean disablePlatformItemSync = refreshConfig.getDisablePlatformItemSync();
        return disablePlatformItemSync != null && disablePlatformItemSync;
    }

    /**
     * 清洗从旺店通拉取回来的数据，将清洗后的数据同步到我们自己的平台商品表（半个小时一次，时间跟）
     */
//    @Scheduled(cron = "0 15/30 * * * ?")
    @XxlJob("PlatformItemSyncTask")
    public void doTask() {
        if (isDisablePlatformItemSync()) {
            log.info("平台商品同步已被禁用");
            return;
        }

        DataSync syncRecord = null;
        LocalDateTime syncStart;
        LocalDateTime syncEnd;

        //查询之前是否有同步记录
        final DataSync lastDataSync = getLastDataSync();
        if (Objects.nonNull(lastDataSync)) {
            //如果已经有同步中的记录，忽略本次执行
            if (lastDataSync.getStatus() == DataSyncStatus.SYNCING) {
                log.info("平台商品同步任务已在执行中");
                return;
            }
            if (lastDataSync.getStatus() == DataSyncStatus.NOT_START
                    || lastDataSync.getStatus() == DataSyncStatus.SYNC_ERROR) {
                //如果同步记录状态是未开始或者异常需要重新同步这个时间段（长时间未同步成功可以手动把同步中修改为未开始或者同步异常，使其强制重新同步）
                syncRecord = lastDataSync;
                syncStart = lastDataSync.getSyncRangeStart();
                syncEnd = lastDataSync.getSyncRangeEnd();
            } else {
                //上一次同步是成功的，则将同步的开始时间限定为上一次同步范围的结束时间
                syncStart = lastDataSync.getSyncRangeEnd();
                syncEnd = LocalDateTime.now();
            }
        } else {
            //没有上一次同步记录就全量同步
            syncStart = null;
            syncEnd = LocalDateTime.now();
        }
        //是否是首次同步（首次同步不触发消息推送）
        final boolean isFirstSync = lastDataSync == null;
        final boolean disablePlatformItemSyncMessage = isDisablePlatformItemSyncMessage();
        boolean triggerMessage = !isFirstSync && !disablePlatformItemSyncMessage;

        //如果不复用上一次的同步记录，则新建本次的同步记录
        if (Objects.isNull(syncRecord)) {
            syncRecord = saveSyncRecord(syncStart, syncEnd);
        }

        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        final DataSyncContext dataSyncContext = new DataSyncContext();

        final LambdaQueryChainWrapper<WdtPlatformGoods> wdtPlatformGoodsQuery = wdtPlatformGoodsService.lambdaQuery();
        if (Objects.nonNull(syncStart)) {
            wdtPlatformGoodsQuery.ge(WdtPlatformGoods::getUpdatedAt, DateUtil.toTime(syncStart));
        }
        if (Objects.nonNull(syncEnd)) {
            wdtPlatformGoodsQuery.le(WdtPlatformGoods::getUpdatedAt, DateUtil.toTime(syncEnd));
        }

        final List<WdtPlatformGoods> wdtPlatformGoods = wdtPlatformGoodsQuery.list();
        for (WdtPlatformGoods wdtPlatformGood : wdtPlatformGoods) {
            if (isDisablePlatformItemSync()) {
                log.info("因为配置变更为禁止平台商品同步所以中断执行");
                dataSyncContext.setStatus(DataSyncStatus.SYNC_INTERRUPT);
                break;
            }
            savePlatformItem(dataSyncContext, wdtPlatformGood, triggerMessage);
        }
        dataSyncContext.setStatus(DataSyncStatus.SYNC_SUCCESS);

        //更新同步记录状态
        syncRecord.setSuccessNum(dataSyncContext.getSuccessNum());
        syncRecord.setFailNum(dataSyncContext.getErrorNum());
        syncRecord.setWarnNum(dataSyncContext.getWarnNum());
        syncRecord.setSkipNum(dataSyncContext.getSkipNum());
        syncRecord.setStatus(dataSyncContext.getStatus());
        dataSyncService.updateById(syncRecord);

        stopWatch.stop();
        log.info("同步平台商品数据完成，耗时{}ms，context:{}", stopWatch.getTotalTimeMillis(), dataSyncContext);
    }

    private void savePlatformItem(DataSyncContext context, WdtPlatformGoods wdtPlatformGood, Boolean triggerEvent) {
        try {
            final SyncResult syncResult = platformItemSyncService.syncWdtPlatformGoods(wdtPlatformGood);
            final SyncStatus syncStatus = syncResult.getSyncStatus();
            final Optional<PlatformItem> platformItemOpt = Optional.ofNullable(syncResult.getPlatformItem());
            switch (syncStatus) {
                case SUCCESS:
                    context.addSuccessNum();
                    break;
                case ERROR_ITEM_NO_MATCH:
                    log.warn("后端商品未匹配到，data = {}", wdtPlatformGood);
                    context.addWarnNum();
                    if (triggerEvent) {
                        triggerMessage(syncResult.getGoodsId(), platformItemOpt.map(PlatformItem::getId).orElse(0L));
                    }
                    break;
                case ERROR_NO_GOODS_ID:
                    log.warn("没有外部平台商品ID，忽略，data = {}", wdtPlatformGood);
                    context.addSkipNum();
                    break;
                case ERROR_NO_SPEC_ID:
                    log.warn("没有外部平台商品SKU ID，忽略，data = {}", wdtPlatformGood);
                    context.getSkipNum();
                    break;
                default:
                    log.warn("未定义的结果，data = {}", wdtPlatformGood);
                    context.addWarnNum();
            }
        } catch (Exception ex) {
            log.error("同步平台商品异常，data = {}", wdtPlatformGood, ex);
            context.addErrorNum();
        }
    }

    private void triggerMessage(String goodsId, Long platformItemId) {
        try {
            PlatformWarnMessageEvent event = PlatformWarnMessageEvent.build(
                    String.valueOf(platformItemId), goodsId, PlatformItemWarnType.SKU_NOT_MATCH.getDesc());
            EventBusUtil.post(event);
        } catch (Exception e) {
            log.error("触发平台商品警告消息事件时遇到异常 goodsId={}", platformItemId, e);
        }
    }

    private DataSync getLastDataSync() {
        final LambdaQueryWrapper<DataSync> dataSyncQuery = Wrappers.lambdaQuery();
        dataSyncQuery.eq(DataSync::getDataType, DataSyncType.PLATFORM_ITEM);
        dataSyncQuery.orderByDesc(DataSync::getId);
        dataSyncQuery.last("LIMIT 1");
        return dataSyncService.getOne(dataSyncQuery);
    }

    private DataSync saveSyncRecord(LocalDateTime syncTimeStart, LocalDateTime syncTimeEnd) {
        final DataSync currentDataSync = new DataSync();
        currentDataSync.setDataType(DataSyncType.PLATFORM_ITEM);
        currentDataSync.setSyncRangeStart(syncTimeStart);
        currentDataSync.setSyncRangeEnd(syncTimeEnd);
        currentDataSync.setStatus(DataSyncStatus.SYNCING);
        dataSyncService.save(currentDataSync);
        return currentDataSync;
    }


}
