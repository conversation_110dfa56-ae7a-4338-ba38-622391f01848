package com.daddylab.supplier.item.application.payment.wrietoff;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.DoProcessRes;
import com.daddylab.supplier.item.application.payment.wrietoff.dto.ProcessRequest;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizService;
import com.daddylab.supplier.item.application.purchase.order.factory.ErpPurchaseOrderHandler;
import com.daddylab.supplier.item.application.purchasePayable.dto.ArtificialPayOrderDetailSaveDto;
import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderCmd;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PurchaseOrderState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockInState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockOutOrderState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 主流程，生成订正之后的采购订单，由新的采购订单生成新出入库单并且推送金蝶。
 * 原采购订单状态改为已完结，打上冲销标识，保存旧新采购订单的关联关系
 * 回滚流程。
 *
 * <AUTHOR> up
 * @date 2024年01月19日 2:54 PM
 */
@Slf4j
@Component
@Order(2)
public class GenerateFixedPurchaseProcess implements WriteOffProcess {

    final PurchaseOrderBizService purchaseOrderBizService;
    final IPurchaseOrderService iPurchaseOrderService;
    final ErpPurchaseOrderHandler erpPurchaseOrderHandler;
    final ILogService iLogService;
    final IPaymentOrderWriteOffLogService iPaymentOrderWriteOffLogService;
    final OperateLogGateway operateLogGateway;
    final IStockInOrderService iStockInOrderService;
    final IStockOutOrderService iStockOutOrderService;
    final IPurchasePayableOrderService iPurchasePayableOrderService;
    final IOrderSettlementDetailService iOrderSettlementDetailService;

    @Autowired
    public GenerateFixedPurchaseProcess(PurchaseOrderBizService purchaseOrderBizService, IPurchaseOrderService iPurchaseOrderService,
                                        ErpPurchaseOrderHandler erpPurchaseOrderHandler, ILogService iLogService,
                                        IPaymentOrderWriteOffLogService iPaymentOrderWriteOffLogService, OperateLogGateway operateLogGateway,
                                        IStockInOrderService iStockInOrderService, IStockOutOrderService iStockOutOrderService,
                                        IPurchasePayableOrderService iPurchasePayableOrderService, IOrderSettlementDetailService iOrderSettlementDetailService) {
        this.purchaseOrderBizService = purchaseOrderBizService;
        this.iPurchaseOrderService = iPurchaseOrderService;
        this.erpPurchaseOrderHandler = erpPurchaseOrderHandler;
        this.iLogService = iLogService;
        this.iPaymentOrderWriteOffLogService = iPaymentOrderWriteOffLogService;
        this.operateLogGateway = operateLogGateway;
        this.iStockInOrderService = iStockInOrderService;
        this.iStockOutOrderService = iStockOutOrderService;
        this.iPurchasePayableOrderService = iPurchasePayableOrderService;
        this.iOrderSettlementDetailService = iOrderSettlementDetailService;
    }

    private ProcessRequest processRequest;

    public void setProcessRequest(ProcessRequest processRequest) {
        this.processRequest = processRequest;
    }

    @Override
    public Boolean doProcess() {
        List<PurchaseOrder> purchaseOrderList = processRequest.getPurchaseOrderList();
        long hadReverseCount = purchaseOrderList.stream().filter(val -> val.getHadReverse().equals(1)).count();
        boolean canContinue = purchaseOrderList.size() != 0 && hadReverseCount == purchaseOrderList.size();
        if (!canContinue) {
            PaymentOrderWriteOffLog stepLog = processRequest.getStepLog();
            stepLog.setTraceMsg("没有完成逆向单据处理的采购订单,此流程直接结束");
            iPaymentOrderWriteOffLogService.save(stepLog);
            return false;
        }

        List<String> oldPurchaseOrderNos = purchaseOrderList.stream().map(PurchaseOrder::getNo).collect(Collectors.toList());
        List<Long> oldPurchaseOrderIds = purchaseOrderList.stream().map(PurchaseOrder::getId).collect(Collectors.toList());
        DoProcessRes<Long> doProcessRes = doProcess0(purchaseOrderList, oldPurchaseOrderNos, oldPurchaseOrderIds);
        List<String> traceLog = doProcessRes.getLogs();
        Boolean isSuccess = doProcessRes.getIsSuccess();
        if (!isSuccess) {
            String rollbackLog = rollback(doProcessRes.getData(), oldPurchaseOrderNos);
            traceLog.add(rollbackLog);
        }
        PaymentOrderWriteOffLog stepLog = processRequest.getStepLog();
        stepLog.setTraceMsg(StrUtil.join(StrUtil.CR, traceLog));
        iPaymentOrderWriteOffLogService.save(stepLog);
        return isSuccess;
    }

    public DoProcessRes<Long> doProcess0(List<PurchaseOrder> purchaseOrderList, List<String> thisPurchaseOrderNos, List<Long> thisPurchaseOrderIds) {
        DoProcessRes<Long> doProcessRes = new DoProcessRes<>();
        OrderSettlementForm orderSettlementForm = processRequest.getOrderSettlementForm();
        List<OrderSettlementDetail> orderSettlementFormDetails =
                iOrderSettlementDetailService.lambdaQuery()
                        .eq(OrderSettlementDetail::getFormId, orderSettlementForm.getId()).list();
        Boolean mockSync = processRequest.getMockSync();
        List<String> traceLogList = new LinkedList<>();
        boolean isSuccess = true;

        Set<Long> oldOverrideOrderIds = purchaseOrderList.stream().map(PurchaseOrder::getOverrideOrderId).collect(Collectors.toSet());
        Set<String> oldOverrideOrderNos = purchaseOrderList.stream().map(PurchaseOrder::getOverrideOrderNo).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(oldOverrideOrderIds)) {
            for (Long oldId : oldOverrideOrderIds) {
                String msg = StrUtil.format("此单据为冲销修正采购订单,原采购订单编码:{},原采购订单重新冲销,生成新的修正采购单,此单据作废", thisPurchaseOrderNos);
                operateLogGateway.addOperatorLog(0L, OperateLogTarget.PURCHASE_ORDER, oldId, msg, null);
            }
            String oldStockInOrderNos = iStockInOrderService.lambdaQuery().in(StockInOrder::getPurchaseOrderId, oldOverrideOrderIds)
                    .select(StockInOrder::getNo).list().stream().map(StockInOrder::getNo).collect(Collectors.joining(StrUtil.COMMA));
            String oldStockOutOrderNos = iStockOutOrderService.lambdaQuery().in(StockOutOrder::getPurchaseOrderId, oldOverrideOrderIds)
                    .select(StockOutOrder::getNo).list().stream().map(StockOutOrder::getNo).collect(Collectors.joining(StrUtil.COMMA));
            String msg = StrUtil.format("这批采购单:{}再次执行冲销流程,原先生成的冲销修正采购订单作废删除,oldFixNos:{}." +
                            "但是关联的下游出入库单需要金蝶上手动删除!!!待手动删除的单据编码.oldStockInNo:{},oldStockOutNo:{}",
                    thisPurchaseOrderNos, oldOverrideOrderNos, oldStockInOrderNos, oldStockOutOrderNos);
            traceLogList.add(msg);
            // 删除原本修正采购订单的上下游关联数据
            oldOverrideOrderNos.forEach(no -> {
                // 删除此采购订单下游单据，出入库单，应付单
                iPurchaseOrderService.deleteRelatedOrders(no);
                // 删除此采购订单
                iPurchaseOrderService.removeByIdWithTime(no);
            });
            // fixme 这就存在一个很麻烦的问题，需要手动去金蝶删除(已生成金蝶的下游单据，接口无法删除)这笔采购订单生成的出入库单和销售入库单和销售退货单。
        }

        try {
            // 将这一整批采购订单全部作废，有结算单数据创建一个新的采购订单
            List<ArtificialPayOrderDetailSaveDto> detailSaveDtoList = orderSettlementFormDetails
                    .stream().map(val -> {
                        ArtificialPayOrderDetailSaveDto artificialPayOrderDetailSaveDto = new ArtificialPayOrderDetailSaveDto();
                        artificialPayOrderDetailSaveDto.setSkuCode(val.getSkuCode());
                        artificialPayOrderDetailSaveDto.setPrice(val.getSettlementPrice());
                        artificialPayOrderDetailSaveDto.setFixedQuantity(val.getSettlementQuantity());
                        return artificialPayOrderDetailSaveDto;
                    }).collect(Collectors.toList());
            String warehouseNo = orderSettlementForm.getWarehouseNo0();
            PurchaseOrderCmd cmd = PurchaseOrderCmd.forFixedPayOrder(orderSettlementForm.getSProviderId(),
                    detailSaveDtoList, DateUtil.currentTime(), warehouseNo);
            SingleResponse<Long> overridePurchaseOrderRes = purchaseOrderBizService.sysSave(cmd);
            Long fixedPurchaseOrderId = overridePurchaseOrderRes.getData();
            doProcessRes.setData(fixedPurchaseOrderId);
            PurchaseOrder fixedPurchaseOrder = iPurchaseOrderService.getById(fixedPurchaseOrderId);
            // 旧的采购订单打上冲销标识，记录关联关系
            markOldPurchaseOrder(fixedPurchaseOrderId, fixedPurchaseOrder.getNo(), thisPurchaseOrderIds);
            // 标识新生成的采购订单
            markNewPurchaseOrder(fixedPurchaseOrderId, StrUtil.join(StrUtil.COMMA, thisPurchaseOrderNos));
            String traceLog = StrUtil.format(
                    "生成新的采购订单生成完毕,旧的采购订单编码列表:{},新生成的采购订单编码:{}",
                    StrUtil.join(StrUtil.COMMA, thisPurchaseOrderNos), fixedPurchaseOrder.getNo());
            traceLogList.add(traceLog);

            // 根据新的采购订单生成对应的出入库单并且同步到金蝶
            List<Log> logList = erpPurchaseOrderHandler.providerOrderConvertStockOrder(fixedPurchaseOrder, mockSync);
            iLogService.saveBatch(logList);
            boolean allSyncSuccess = logList.stream().filter(val -> val.getType() > 0).count() == logList.size();
            String respNos = logList.stream().map(Log::getResp).collect(Collectors.joining(StrUtil.COMMA));
            if (!allSyncSuccess) {
                String traceLog2 = StrUtil.format(
                        "新的采购订单完成,金蝶创建下游出入库单据异常,newPurchaseOrder:{},log:{}", fixedPurchaseOrder.getNo(), JsonUtil.toJson(logList));
                traceLogList.add(traceLog2);
                isSuccess = false;
            } else {
                String traceLog3 = StrUtil.format(
                        "成新的采购订单完成,金蝶创建下游出入库单据完成,newPurchaseOrder:{},下游出入库单no:{}", fixedPurchaseOrder.getNo(), respNos);
                traceLogList.add(traceLog3);

                // 当处理成功后，添加旧单据操作日志
                addOldOrderLog(thisPurchaseOrderIds, fixedPurchaseOrder.getNo());

                // 同步到金蝶成功，记录下需要生成的销售出库单和销退入库单信息
                logList.stream().filter(val -> val.getType() == 98).findFirst()
                        .ifPresent(val -> {
                            processRequest.getSyncStockInOrderNos().add(val.getResp());
                            iStockInOrderService.lambdaUpdate()
                                    .set(StockInOrder::getHedgeStatus, StockInState.FIX_ORDER)
                                    .eq(StockInOrder::getNo, val.getResp()).update();
                            iPurchasePayableOrderService.markWriteOffWithStockOrder(val.getResp());
                        });
                logList.stream().filter(val -> val.getType() == 99).findFirst()
                        .ifPresent(val -> {
                            processRequest.getSyncStockOutOrderNos().add(val.getResp());
                            iStockOutOrderService.lambdaUpdate()
                                    .set(StockOutOrder::getHedgeStatus, StockOutOrderState.FIX_ORDER)
                                    .eq(StockOutOrder::getNo, val.getResp()).update();
                            iPurchasePayableOrderService.markWriteOffWithStockOrder(val.getResp());
                        });
            }
            doProcessRes.setIsSuccess(isSuccess);
            doProcessRes.setLogs(traceLogList);
            return doProcessRes;
        } catch (Exception e) {
            log.error("生成新的采购订单处理异常", e);
            doProcessRes.setIsSuccess(false);
            doProcessRes.setLogs(Collections.singletonList("生成新的采购订单处理异常." + e.getMessage()));
            return doProcessRes;
        }
    }

    private void addOldOrderLog(List<Long> oldIds, String newNo) {
        for (Long oldId : oldIds) {
            operateLogGateway.addOperatorLog(0L, OperateLogTarget.PURCHASE_ORDER, oldId,
                    "此单据已经对冲作废，已生成新的采购订单，编码:" + newNo, null);
        }
    }

    private void markOldPurchaseOrder(Long newId, String newNo, List<Long> oldIds) {
        iPurchaseOrderService.lambdaUpdate()
                .set(PurchaseOrder::getState, PurchaseOrderState.FINISHED.getValue())
                .set(PurchaseOrder::getOverrideOrderId, newId)
                .set(PurchaseOrder::getOverrideOrderNo, newNo)
                .in(PurchaseOrder::getId, oldIds).update();

    }

    private void markNewPurchaseOrder(Long newId, String oldNos) {
        // 完结新的采购订单
        iPurchaseOrderService.lambdaUpdate()
                .set(PurchaseOrder::getState, PurchaseOrderState.FINISH_FIXED.getValue())
                .set(PurchaseOrder::getRemark, "冲销流程修正单据，原关联单据。" + oldNos)
                .eq(PurchaseOrder::getId, newId)
                .update();
    }

    /**
     * 数据回滚。
     *
     * @return
     */
    public String rollback(Long newOrderId, List<String> oldOrderNos) {
        if (Objects.nonNull(newOrderId)) {
            PurchaseOrder fixedPurchaseOrder = iPurchaseOrderService.getById(newOrderId);
            if (Objects.nonNull(fixedPurchaseOrder)) {
                // 删除此采购订单下游单据，出入库单，应付单
                iPurchaseOrderService.deleteRelatedOrders(fixedPurchaseOrder.getNo());
                // 删除此采购订单
                iPurchaseOrderService.removeByIdWithTime(fixedPurchaseOrder.getId());
                // 原采购订单状态恢复
                iPurchaseOrderService.lambdaUpdate()
                        .set(PurchaseOrder::getHadReverse, 1)
                        .set(PurchaseOrder::getOverrideOrderId, null)
                        .set(PurchaseOrder::getOverrideOrderNo, StrUtil.EMPTY)
                        .eq(PurchaseOrder::getOverrideOrderId, newOrderId)
                        .update();
                return StrUtil.format("新采购单回滚流程结束,删除新采购订单关联数据.newId:{},oldNos:{}",
                        newOrderId, StrUtil.join(StrUtil.COMMA, oldOrderNos));
            }
        }
        return StrUtil.format("还没有生成新采购订单流程就异常了,回滚结束.newId:{}", newOrderId);
    }


}
