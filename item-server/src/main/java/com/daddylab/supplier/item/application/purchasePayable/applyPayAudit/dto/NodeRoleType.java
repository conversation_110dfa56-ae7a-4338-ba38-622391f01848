package com.daddylab.supplier.item.application.purchasePayable.applyPayAudit.dto;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> up
 * @date 2023年02月16日 10:25 AM
 */
@Getter
@AllArgsConstructor
public enum NodeRoleType implements IEnum<Integer> {

    /**
     *
     */
    DEPARTMENT_MANAGER(1, "本部门主管"),

    BUSINESS_GROUP_VP(2, "事业群负责人"),

    FINANCIAL_ACCOUNTING(3, "财务会计"),

    FINANCIAL_MANAGER(4, "财务经理"),

    FINAL_BOSS(5, "最终BOSS"),

    CASHIER(6, "出纳");


    private final Integer value;
    private final String desc;

}
