package com.daddylab.supplier.item.application.afterSales.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSales.*;
import com.daddylab.supplier.item.application.refundOrder.RefundOrderService;
import com.daddylab.supplier.item.common.ErrorChecker;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.controller.open.PartnerProviderContext;
import com.daddylab.supplier.item.domain.exportTask.ExportDomainService;
import com.daddylab.supplier.item.domain.exportTask.ExportEntityPlus;
import com.daddylab.supplier.item.domain.exportTask.ExportType;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.AfterSalesLogisticsDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.AfterSalesPageDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesAbnormalState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesImageType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.AfterSalesAbnormalInfoMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderDetail;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2022年10月18日 10:25 AM
 */
@Service
@Slf4j
public class AfterSalesBizServiceImpl implements AfterSalesBizService {

    @Resource
    IAfterSalesInfoImageService iAfterSalesInfoImageService;

    @Resource
    IWarehouseAfterSalesAddressService iWarehouseAfterSalesAddressService;

    @Resource
    IAfterSalesAbnormalInfoService iAfterSalesAbnormalInfoService;

    @Resource
    AfterSalesAbnormalInfoMapper afterSalesAbnormalInfoMapper;

    @Resource
    RefundOrderService refundOrderService;

    @Resource
    IAfterSalesSendOnInfoService iAfterSalesSendOnInfoService;

    @Resource
    AfterSalesWarehouseBizService afterSalesWarehouseBizService;

    @Resource
    IAfterSalesAbnormalInfoService afterSalesAbnormalInfoService;

    @Resource
    IWarehouseService warehouseService;

    @Resource
    ExportDomainService exportDomainService;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public SingleResponse<Boolean> saveAbnormalInfo(AfterSalesAbnormalCmd cmd) {
        if (Objects.isNull(cmd.getAbnormalInfoId())) {
            AfterSalesAbnormalInfo abnormalInfo = new AfterSalesAbnormalInfo();
            abnormalInfo.setItemName(cmd.getItemName());
            abnormalInfo.setLogisticsName(cmd.getLogisticsName());
            abnormalInfo.setLogisticsNo(cmd.getLogisticsNo());
            abnormalInfo.setQuantity(cmd.getQuantity());
            abnormalInfo.setState(AfterSalesAbnormalState.WAIT_HANDLE.getValue());
            abnormalInfo.setRemark(cmd.getRemark());
            abnormalInfo.setPartnerProviderId(PartnerProviderContext.getId());
            abnormalInfo.setPartnerProviderName(PartnerProviderContext.getName());
            abnormalInfo.setUnifySocialCreditCode(PartnerProviderContext.getSocialCreditCode());
            abnormalInfo.setWarehouseNo(cmd.getWarehouseNo());

            iAfterSalesAbnormalInfoService.save(abnormalInfo);
            iAfterSalesInfoImageService.saveImageByType(abnormalInfo.getId(), cmd.getImageUrls(), AfterSalesImageType.ABNORMAL);

            // 进行自动关联逻辑判断
            AfterSalesAbnormalInfo one = iAfterSalesAbnormalInfoService.lambdaQuery().eq(AfterSalesAbnormalInfo::getLogisticsNo, cmd.getLogisticsNo())
                    .orderByAsc(AfterSalesAbnormalInfo::getId).last("limit 1").one();
            if (Objects.nonNull(one)) {
                if (StrUtil.isNotBlank(one.getReturnOrderNo())) {
                    RelateCmd cmd1 = new RelateCmd();
                    cmd1.setReturnOrderNo(one.getReturnOrderNo());
                    cmd1.setAbnormalInfoIds(Collections.singletonList(abnormalInfo.getId()));
                    try {
                        relatedAbnormalInfo(cmd1);
                    } catch (Exception e) {
                        log.error("登记异常件，自动售后关联处理异常，cmd:{}", JsonUtil.toJson(cmd1), e);
                        throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "异常件自动售后关联异常");
                    }
                }
            }

            return SingleResponse.of(true);
        } else {
            AfterSalesAbnormalInfo oleOne = iAfterSalesAbnormalInfoService.getById(cmd.getAbnormalInfoId());
            Assert.notNull(oleOne, "异常件信息id非法，查询为空。id:" + cmd.getAbnormalInfoId());
            // 检查下异常件的状态
            Assert.state(Objects.equals(AfterSalesAbnormalState.WAIT_HANDLE.getValue(), oleOne.getState()), "只有待处理状态才允许编辑，请刷新");

            // 异常件信息，一改全改（物流单号和物流名称）
            oleOne.setItemName(cmd.getItemName());
            oleOne.setLogisticsName(cmd.getLogisticsName());
            oleOne.setLogisticsNo(cmd.getLogisticsNo());
            oleOne.setQuantity(cmd.getQuantity());
            oleOne.setRemark(cmd.getRemark());
            oleOne.setWarehouseNo(cmd.getWarehouseNo());
            iAfterSalesAbnormalInfoService.updateById(oleOne);

            iAfterSalesInfoImageService.removeThenInsertNewImageByType(oleOne.getId(), cmd.getImageUrls(), AfterSalesImageType.ABNORMAL);
            return SingleResponse.of(true);
        }
    }

    @Override
    public SingleResponse<Boolean> removeAbnormalInfo(Long id) {
        AfterSalesAbnormalInfo oleOne = iAfterSalesAbnormalInfoService.getById(id);
        Assert.state(Objects.equals(AfterSalesAbnormalState.WAIT_HANDLE.getValue(), oleOne.getState()), "只有待处理状态才允许删除，请刷新");
        return SingleResponse.of(iAfterSalesAbnormalInfoService.removeById(id));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public SingleResponse<Boolean> relatedAbnormalInfo(RelateCmd cmd) throws Exception{
        String returnOrderNo = cmd.getReturnOrderNo();
        SingleResponse<RefundOrderDetail> refundOrderDetailSingleResponse = refundOrderService.refundOrderDetail(returnOrderNo);
        Assert.state(refundOrderDetailSingleResponse.isSuccess(), "退换单查询异常。退换单号：" + returnOrderNo);
        Assert.notNull(refundOrderDetailSingleResponse.getData(), "退换单查询为空。退换单号：" + returnOrderNo);

        final RefundOrderDetail refundOrderDetailData = refundOrderDetailSingleResponse.getData();
        //售后类型 1:退款;2:退货;3:换货;4:退款不退货;6:保价退款（只有退货或换货才能转寄）
        if (refundOrderDetailData.getType() != 2 && refundOrderDetailData.getType() != 3) {
            throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "只有类型为退货或换货的售后单才能进行关联异常件");
        }
        final ImmutableMap<Integer, String> checkStatus = ImmutableMap.of(10, "已取消", 90, "已完成");
        String statusLabel;
        if (null != (statusLabel = checkStatus.get(refundOrderDetailData.getStatus()))) {
            throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR,
                    StringUtil.format("售后单状态{}，无法关联", statusLabel));
        }

        // 关联到的退换单的仓库信息
        String returnWarehouseNo = refundOrderDetailSingleResponse.getData().getReturnWarehouseNo();
        Assert.hasText(returnWarehouseNo, "关联退换单的仓库编号查询为空");
        AtomicReference<String> senderName = new AtomicReference<>("");
        AtomicReference<String> senderTel = new AtomicReference<>("");
        AtomicReference<String> senderAddress = new AtomicReference<>("");
        WarehouseAfterSalesAddress warehouseAfterSalesAddress = iWarehouseAfterSalesAddressService.lambdaQuery()
                .eq(WarehouseAfterSalesAddress::getWarehouseNo, returnWarehouseNo)
                .orderByDesc(WarehouseAfterSalesAddress::getId).last("limit 1").one();
        if (Objects.nonNull(warehouseAfterSalesAddress)) {
            senderName.set(warehouseAfterSalesAddress.getWarehouseName());
            senderTel.set(warehouseAfterSalesAddress.getTel());
            senderName.set(warehouseAfterSalesAddress.getFullAddress());
        }
        if (StrUtil.isEmpty(senderName.get()) || StrUtil.isEmpty(senderTel.get()) || StrUtil.isEmpty(senderAddress.get())) {
            Optional<Warehouse> warehouse = warehouseService.queryWarehouseByNo(returnWarehouseNo);
            warehouse.ifPresent(val -> {
                senderName.set(val.getName());
                senderTel.set(val.getPhone());
                senderAddress.set(val.getFullAddress());
            });
        }

        List<AbnormalStateChangeEvent> abnormalEvents = Lists.newArrayList();
        cmd.getAbnormalInfoIds().forEach(abnormalInfoId -> {
            try {
                AfterSalesAbnormalInfo abnormalInfo = iAfterSalesAbnormalInfoService.getById(abnormalInfoId);
                Assert.notNull(abnormalInfo, "异常件信息查询为空。异常件id：" + abnormalInfoId);
                Assert.isTrue(StrUtil.isEmpty(abnormalInfo.getReturnOrderNo()), "此异常件信息已经关联了退换单。异常件id：" + abnormalInfoId);
                abnormalInfo.setReturnOrderNo(returnOrderNo);
                final Integer prevState = abnormalInfo.getState();

                // 查询此异常单供应商对应的仓库列表
                List<String> partnerProviderWarehouseNos = iWarehouseAfterSalesAddressService.warehouseNo(abnormalInfo.getPartnerProviderId());
                // 对应成功，直接处理掉。
                if (partnerProviderWarehouseNos.contains(returnWarehouseNo)) {
                    abnormalInfo.setState(AfterSalesAbnormalState.HAD_HANDLE.getValue());
                }
                // 对应不上，走转寄流程
                else {
                    abnormalInfo.setState(AfterSalesAbnormalState.WAIT_SEND_NO.getValue());
                    // 初始化一份转寄单
                    AfterSalesSendOnInfo sendOnInfo = new AfterSalesSendOnInfo();
                    sendOnInfo.setAbnormalInfoId(abnormalInfoId);
                    sendOnInfo.setReturnOrderNo(returnOrderNo);
                    sendOnInfo.setSenderNo(returnWarehouseNo);
                    sendOnInfo.setSender(senderName.get());
                    sendOnInfo.setSenderTel(senderTel.get());
                    sendOnInfo.setSenderAddress(senderAddress.get());
                    iAfterSalesSendOnInfoService.save(sendOnInfo);
                }
                iAfterSalesAbnormalInfoService.updateById(abnormalInfo);
                abnormalEvents.add(AbnormalInfoTypeMapper.INST.abnormalInfoToEvent(abnormalInfo, prevState));
            } catch (Exception e) {
                log.error("关联操作异常", e);
                throw e;
            }
        });

        EventBusUtil.post(new RefundOrderRelatedEvent(returnOrderNo, abnormalEvents), true);
        return SingleResponse.of(true);
    }

    @Override
    public SingleResponse<AfterSalesSendOnInfo> getSendOnInfo(Long abnormalInfoId, String returnOrderNo) {
        if (Objects.nonNull(abnormalInfoId)) {
            AfterSalesSendOnInfo one = iAfterSalesSendOnInfoService.lambdaQuery().eq(AfterSalesSendOnInfo::getAbnormalInfoId, abnormalInfoId)
                    .orderByDesc(AfterSalesSendOnInfo::getId).last("limit 1").one();
            return SingleResponse.of(one);
        }
        if (StrUtil.isNotBlank(returnOrderNo)) {
            AfterSalesSendOnInfo one = iAfterSalesSendOnInfoService.lambdaQuery().eq(AfterSalesSendOnInfo::getReturnOrderNo, returnOrderNo)
                    .orderByDesc(AfterSalesSendOnInfo::getId).last("limit 1").one();
            if (one != null) {
                return SingleResponse.of(one);
            }
            final AfterSalesSendOnInfo sendOnInfo = createSendOnInfoByRefundOrder(
                    returnOrderNo);
            return SingleResponse.of(sendOnInfo);
        }
        throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "参数异常，查询为空");
    }

    public AfterSalesSendOnInfo createSendOnInfoByRefundOrder(String returnOrderNo) {
        final SingleResponse<RefundOrderDetail> refundOrderDetail = refundOrderService.refundOrderDetail(
                returnOrderNo);
        ErrorChecker.checkAndThrowIfError(refundOrderDetail, Objects::nonNull,
                ErrorCode.DATA_NOT_FOUND, "退换单异常");
        final RefundOrderDetail refundOrderDetailData = refundOrderDetail.getData();
        //售后类型 1:退款;2:退货;3:换货;4:退款不退货;6:保价退款（只有退货或换货才能转寄）
        if (refundOrderDetailData.getType() != 2 && refundOrderDetailData.getType() != 3) {
            throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "只有类型为退货或换货的售后单才能进行转寄操作");
        }
        final String returnWarehouseNo = refundOrderDetailData.getReturnWarehouseNo();
        if (StringUtil.isBlank(returnWarehouseNo)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "售后单未关联退回仓库");
        }
        final WarehouseAfterSalesAddressVO warehouseAfterSalesAddressVO = afterSalesWarehouseBizService.getWarehouseAfterSalesAddressVO(
                returnWarehouseNo).orElseThrow(
                () -> ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR,
                        "售后单关联退回仓库未配置退回地址信息"));
        final AfterSalesSendOnInfo afterSalesSendOnInfo = new AfterSalesSendOnInfo();
        afterSalesSendOnInfo.setAbnormalInfoId(0L);
        afterSalesSendOnInfo.setReturnOrderNo(returnOrderNo);
        afterSalesSendOnInfo.setLogisticsName("");
        afterSalesSendOnInfo.setLogisticsNo("");
        afterSalesSendOnInfo.setWeight("");
        afterSalesSendOnInfo.setRemark("");
        afterSalesSendOnInfo.setSender(warehouseAfterSalesAddressVO.getWarehouseName());
        afterSalesSendOnInfo.setSenderTel(warehouseAfterSalesAddressVO.getTel());
        afterSalesSendOnInfo.setSenderAddress(warehouseAfterSalesAddressVO.getFullAddress());
        afterSalesSendOnInfo.setSenderNo(warehouseAfterSalesAddressVO.getWarehouseNo());
        afterSalesSendOnInfo.setFromWarehouseNo("");
        afterSalesSendOnInfo.setFromTel("");
        iAfterSalesSendOnInfoService.save(afterSalesSendOnInfo);
        return afterSalesSendOnInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SingleResponse<Boolean> saveSendOnInfo(AfterSalesSendOnCmd cmd) {
        AfterSalesSendOnInfo afterSalesSendOnInfo = iAfterSalesSendOnInfoService.getById(cmd.getSendOnInfoId());
        Assert.notNull(afterSalesSendOnInfo, "转寄单信息查询为空，id:" + cmd.getSendOnInfoId());

        //如果没有关联的异常单信息，则说明是在退换列表发起的快捷转寄
        if (NumberUtil.isZeroOrNull(afterSalesSendOnInfo.getAbnormalInfoId())) {
            return fastSendOn(cmd, afterSalesSendOnInfo);
        }
        AfterSalesAbnormalInfo abnormalInfo = iAfterSalesAbnormalInfoService.getById(
                afterSalesSendOnInfo.getAbnormalInfoId());
        Assert.notNull(abnormalInfo, "异常件信息查询为空。id:" + cmd.getAbnormalInfoId());

        // 查询此转寄单的异常件信息，查询同一物流单下的数据，一键全部转寄
        String logisticsNo = abnormalInfo.getLogisticsNo();
        iAfterSalesAbnormalInfoService.lambdaQuery()
                .eq(AfterSalesAbnormalInfo::getLogisticsNo, logisticsNo)
                .select().list()
                .forEach(val -> {
                    // 查询各个异常件的转寄信息
                    if (Objects.equals(AfterSalesAbnormalState.WAIT_SEND_NO.getValue(), val.getState())) {
                        AfterSalesSendOnInfo one = iAfterSalesSendOnInfoService.lambdaQuery().eq(AfterSalesSendOnInfo::getAbnormalInfoId, val.getId())
                                .orderByDesc(AfterSalesSendOnInfo::getId).last("limit 1").one();
                        if (Objects.nonNull(one)) {
                            one.setLogisticsName(cmd.getLogisticsName());
                            one.setLogisticsNo(cmd.getLogisticsNo());
                            one.setWeight(cmd.getWeight());
                            one.setRemark(cmd.getRemark());
                            one.setFromTel(cmd.getFromTel());
                            one.setFromWarehouseNo(abnormalInfo.getWarehouseNo());
                            iAfterSalesSendOnInfoService.updateById(one);
                            iAfterSalesInfoImageService.saveImageByType(one.getId(), cmd.getImageUrls(), AfterSalesImageType.SEND_ON);
                        }
                    }
                    val.setState(AfterSalesAbnormalState.HAD_SEND_NO.getValue());
                    iAfterSalesAbnormalInfoService.updateById(val);
                });
        return SingleResponse.of(true);
    }

    @NonNull
    private SingleResponse<Boolean> fastSendOn(AfterSalesSendOnCmd cmd,
            AfterSalesSendOnInfo afterSalesSendOnInfo) {
        //没有寄送方仓库编号需要补全
        if (StringUtil.isBlank(afterSalesSendOnInfo.getFromWarehouseNo()) && StringUtil.isBlank(
                cmd.getFromWarehouseNo())) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "请选择寄送方仓库");
        }
        afterSalesSendOnInfo.setLogisticsName(cmd.getLogisticsName());
        afterSalesSendOnInfo.setLogisticsNo(cmd.getLogisticsNo());
        afterSalesSendOnInfo.setWeight(cmd.getWeight());
        afterSalesSendOnInfo.setRemark(cmd.getRemark());
        afterSalesSendOnInfo.setFromWarehouseNo(cmd.getFromWarehouseNo());
        afterSalesSendOnInfo.setFromTel(cmd.getFromTel());
        iAfterSalesSendOnInfoService.updateById(afterSalesSendOnInfo);
        iAfterSalesInfoImageService.saveImageByType(afterSalesSendOnInfo.getId(),
                cmd.getImageUrls(), AfterSalesImageType.SEND_ON);
        return SingleResponse.of(true);
    }

    @Override
    public SingleResponse<Boolean> exportAbnormalInfo(AfterSalesPageQuery pageQuery) {
        ExportTask exportTask = ExportEntityPlus.save(ExportTaskType.AFTER_SALES_ABNORMAL);
        exportTask.setPId(pageQuery.getPartnerProviderId());
        exportDomainService.export(pageQuery, exportTask, ExportType.AFTER_SALES_ABNORMAL,
                AbnormalExportSheet.class, (Function<AbnormalExportSheet, ExportSheet>) sheet -> {
                    AfterSalesAbnormalState state = IEnum.getEnumByValue(AfterSalesAbnormalState.class, sheet.getState());
                    sheet.setStateStr(state.getDesc());
                    return sheet;
                });
        return SingleResponse.of(true);
    }

    @Override
    public PageResponse<AfterSalesPageVO> pageQueryAbnormalInfo(AfterSalesPageQuery pageQuery) {
        // 根据物流单号聚合。创建时间倒序。
        PageInfo<AfterSalesLogisticsDO> logisticsPageInfo = PageHelper.startPage(pageQuery.getPageIndex(), pageQuery.getPageSize())
                .doSelectPageInfo(() -> afterSalesAbnormalInfoMapper.pageQueryOrderByCreatedAt(pageQuery));
        if (CollUtil.isEmpty(logisticsPageInfo.getList())) {
            return PageResponse.of(new LinkedList<>(), 0, pageQuery.getPageSize(), pageQuery.getPageIndex());
        }

        // 查询出一页物流单号对应的所有异常件信息
        List<String> allLogisticsNoList = logisticsPageInfo.getList().stream().map(AfterSalesLogisticsDO::getLogisticsNo)
                .collect(Collectors.toList());
        List<AfterSalesPageDO> afterSalesPageDOList = afterSalesAbnormalInfoMapper.pageQueryOrderByLogisticsNo(allLogisticsNoList);
        Map<String, List<AfterSalesPageDO>> logisticsNoMap = afterSalesPageDOList
                .stream().collect(Collectors.groupingBy(AfterSalesPageDO::getLogisticsNo));

        List<AfterSalesPageVO> resultList = logisticsPageInfo.getList().stream().map(logisticsDo -> {
            AfterSalesPageVO vo = new AfterSalesPageVO();
            vo.setLogisticsNo(logisticsDo.getLogisticsNo());

            // 下属列表已创建时间倒序，一些信息取第一条为主。
            List<AfterSalesPageDO> thisNoList = logisticsNoMap.get(logisticsDo.getLogisticsNo());
            List<AfterSalesPageDO> sortedList = thisNoList.stream()
                    .sorted(Comparator.comparing(AfterSalesPageDO::getCreatedAtStr).reversed()).collect(Collectors.toList());
            AfterSalesPageDO afterSalesPageQueryDO = sortedList.get(0);

            vo.setWarehouseName(afterSalesPageQueryDO.getWarehouseName());
            vo.setProviderName(afterSalesPageQueryDO.getProviderName());
            vo.setCreatedAt(afterSalesPageQueryDO.getCreatedAtStr());
            // 聚合 状态取最后一条。
            vo.setGroupState(sortedList.get(thisNoList.size() - 1).getState());
            vo.setLineList(sortedList.stream().map(val -> {
                AfterSalesLinePageVO lineVo = new AfterSalesLinePageVO();
                lineVo.setAbnormalInfoId(val.getId());
                lineVo.setItemName(val.getItemName());
                lineVo.setQuantity(val.getQuantity());
                lineVo.setAbnormalState(val.getState());
                List<String> imageList = StrUtil.isEmpty(val.getImages()) ? new LinkedList<>()
                        : Arrays.asList(val.getImages().split(","));
                lineVo.setImageUrls(imageList);
                lineVo.setRemark(val.getRemark());
                lineVo.setLogisticsName(val.getLogisticsName());
                lineVo.setWarehouseNo(val.getWarehouseNo());
                lineVo.setWarehouseName(val.getWarehouseName());
                return lineVo;
            }).collect(Collectors.toList()));
            return vo;
        }).collect(Collectors.toList());

        return PageResponse.of(resultList, (int) logisticsPageInfo.getTotal(), pageQuery.getPageSize(), pageQuery.getPageIndex());
    }
}
