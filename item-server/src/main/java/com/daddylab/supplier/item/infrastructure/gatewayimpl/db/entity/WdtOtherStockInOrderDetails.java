package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 旺店通其他入库单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WdtOtherStockInOrderDetails implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 入库单ID
     */
    private Long stockinId;

    /**
     * 数量
     */
    private BigDecimal goodsCount;

    /**
     * 总成本（存货成本*数量）
     */
    private BigDecimal totalCost;

    /**
     * 备注
     */
    private String remark;

    /**
     * 调整后数量
     */
    private BigDecimal rightNum;

    /**
     * 单位
     */
    private String goodsUnit;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 明细id(主键)
     */
    private Integer recId;

    /**
     * 生产日期
     */
    private LocalDateTime productionDate;

    /**
     * 有效期
     */
    private LocalDateTime expireDate;

    /**
     * 货品名称
     */
    private String goodsName;

    /**
     * 货品编号
     */
    private String goodsNo;

    /**
     * 商家编码
     */
    private String specNo;

    /**
     * 自定义属性2（货品档案单品自定义属性2）
     */
    private String prop2;

    /**
     * 规格名称
     */
    private String specName;

    /**
     * 规格码
     */
    private String specCode;

    /**
     * 品牌编号
     */
    private String brandNo;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * true：残次品 false：正品
     */
    private Boolean defect;


}
