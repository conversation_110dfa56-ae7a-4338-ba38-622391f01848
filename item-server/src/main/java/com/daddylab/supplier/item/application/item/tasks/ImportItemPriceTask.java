package com.daddylab.supplier.item.application.item.tasks;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemPrice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemPriceRang;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemPriceType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemPriceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil.ChangePropertyObj;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MultiMapUtils;
import org.apache.commons.collections4.SetValuedMap;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/8/17
 */
@Component
@Slf4j
public class ImportItemPriceTask {

    public static final Function<ItemPrice, String> ITEM_PRICE_TO_STRING_FUNCTION = itemPrice -> {
        if (itemPrice.getType().equals(ItemPriceType.CUSTOMIZE)) {
            return itemPrice.getCustomName();
        }
        return itemPrice.getType().getDesc();
    };

    @Data
    @HeadRowHeight(20)
    @ContentRowHeight(16)
    @EqualsAndHashCode(of = {"skuCode"})
    public static class ImportRow {

        @ExcelProperty(value = "SKU编码")
        @ColumnWidth(50)
        private String skuCode;

        @ExcelProperty(value = "产品日销价")
        @ColumnWidth(50)
        private String dailyPrice;

    }

    private final IItemService itemService;
    private final IItemPriceService itemPriceService;

    private final IItemSkuService itemSkuService;
    private final OperateLogDomainService operateLogDomainService;

    public ImportItemPriceTask(IItemService itemService, IItemPriceService itemPriceService,
            IItemSkuService itemSkuService, OperateLogDomainService operateLogDomainService) {
        this.itemService = itemService;
        this.itemPriceService = itemPriceService;
        this.itemSkuService = itemSkuService;
        this.operateLogDomainService = operateLogDomainService;
    }

    public void importExcel(InputStream inputStream, Integer sheetNo) {
        List<ImportRow> sheetRowsRaw = EasyExcel.read(inputStream)
                .headRowNumber(1)
                .head(ImportRow.class).sheet(sheetNo).doReadSync();
        final List<ImportRow> sheetRows = sheetRowsRaw.stream().distinct()
                .collect(Collectors.toList());

        log.info("开始处理商品价格导入，总计:{}", sheetRows.size());
        final SetValuedMap<String, Object> runStat = MultiMapUtils.newSetValuedHashMap();

        for (int i = 0; i < sheetRows.size(); i++) {
            final ImportRow sheetRow = sheetRows.get(i);
            if (Objects.isNull(sheetRow.getSkuCode()) && Objects.isNull(sheetRow.getDailyPrice())) {
                continue;
            }

            final String skuCode = sheetRow.getSkuCode();
            if (StringUtil.isBlank(skuCode)) {
                log.error("第{}行规格编码为空", i + 2);
                runStat.put("规格编码为空", skuCode);
                continue;
            }
            final String dailyPriceStr = sheetRow.getDailyPrice();
            if (StringUtil.isBlank(dailyPriceStr)) {
                log.error("导入日销价为空 规格编码:{}", skuCode);
                runStat.put("日销价为空", skuCode);
                continue;
            }

            BigDecimal dailyPrice;
            BigDecimal linePrice;
            try {
                dailyPrice = new BigDecimal(dailyPriceStr).setScale(2, RoundingMode.HALF_EVEN);
                linePrice = NumberUtil.mul(dailyPrice, BigDecimal.valueOf(1.25))
                        .setScale(2, RoundingMode.HALF_EVEN);
            } catch (NumberFormatException e) {
                log.error("导入日销价格式无效 划线价:{} 规格编码:{}", sheetRow.getDailyPrice(),
                        skuCode);
                runStat.put("日销价格式错误", skuCode);
                continue;
            }

            final ItemSku itemSku = getItemSku(sheetRow).orElse(null);
            if (itemSku == null) {
                runStat.put("规格编码无效", skuCode);
                log.error("规格编码无效 规格编码:{}", skuCode);
                continue;
            }
            final Long itemId = itemSku.getItemId();
            final ArrayList<String> changeLogs = new ArrayList<>();
            final ArrayList<ChangePropertyObj> changeDetails = new ArrayList<>();
            if (!runStat.containsMapping("商品价格已处理", itemId) && !runStat.containsMapping("商品价格无需处理",
                    itemId) && !runStat.containsMapping("商品编码无效", itemId)) {
                final Item item = getItem(itemId).orElse(null);
                if (item == null) {
                    log.error("商品规格关联的商品ID无效！商品ID:" + itemId + " 规格编码:" + itemSku.getSkuCode());
                    runStat.put("商品编码无效", itemId);
                } else {
                    final List<ItemPrice> itemPrices = getItemPrices(itemId);
                    boolean priceUpdated = setPriceForType(ItemPriceType.DAILY, dailyPrice, item,
                            itemPrices, changeLogs, changeDetails);
                    priceUpdated = setPriceForType(ItemPriceType.LINE, linePrice, item,
                            itemPrices, changeLogs, changeDetails) || priceUpdated;
                    if (priceUpdated) {
                        runStat.put("商品价格已处理", itemId);
                    } else {
                        runStat.put("商品价格无需处理", itemId);
                        log.info("商品价格无需处理 商品编码:{}", item.getCode());
                    }
                }
            }
            //规格销售价为空才做修改
            if (NumberUtil.isZeroOrNull(itemSku.getSalePrice())) {
                itemSku.setSalePrice(dailyPrice);
                itemSkuService.updateById(itemSku);
                final String msg = StringUtil.format("修改规格'{}'日销价", itemSku.getSkuCode());
                changeLogs.add(msg);
                changeDetails.add(new ChangePropertyObj("日销价", null, dailyPrice));
                log.info(StringUtil.format("修改规格（{}）日销价为{}", itemSku.getSkuCode(),
                        dailyPrice));
                runStat.put("规格日销价已处理", itemSku.getSkuCode());
            } else {
                log.info("规格日销价无需处理 规格编号:{}", itemSku.getSkuCode());
                runStat.put("规格日销价无需处理", itemSku.getSkuCode());
            }
            if (!changeLogs.isEmpty()) {
                operateLogDomainService.addOperatorLog(0L, OperateLogTarget.ITEM_PRICE, itemId,
                        "系统导入商品价格:" + String.join("、", changeLogs), changeDetails);

            }

        }
        log.info("商品价格导入处理完成 {}", runStat);
    }

    private boolean setPriceForType(ItemPriceType priceType, BigDecimal price, Item item,
            List<ItemPrice> itemPrices,
            List<String> changeLogs, ArrayList<ChangePropertyObj> changeDetails) {
        final ItemPrice itemPrice = itemPrices.stream()
                .filter(ip -> ip.getType() == priceType).findFirst()
                .orElseGet(() -> newItemPrice(item.getId(), priceType));
        if (NumberUtil.isZeroOrNull(itemPrice.getPrice())) {
            itemPrice.setPrice(price);
            itemPriceService.saveOrUpdate(itemPrice);
            final String priceDesc = ITEM_PRICE_TO_STRING_FUNCTION.apply(itemPrice);
            log.info("商品编码:{} 修改商品{}为'{}'", item.getCode(), priceDesc, price);
            final String msg = StringUtil.format("修改商品{}", priceDesc);
            changeLogs.add(msg);
            changeDetails.add(new ChangePropertyObj(priceDesc, null, price));
            return true;
        }
        return false;
    }

    @NonNull
    private ItemPrice newItemPrice(Long itemId, ItemPriceType type) {
        final ItemPrice itemPrice = new ItemPrice();
        itemPrice.setItemId(itemId);
        itemPrice.setType(type);
        itemPrice.setStartTime(1640966400L);//2022-01-01 00:00:00
        itemPrice.setEndTime(4070880000L);//2099-01-01 00:00:00
        itemPrice.setCustomName(type.getDesc());
        itemPrice.setRang(type == ItemPriceType.PROCUREMENT ? ItemPriceRang.PROCUREMENT
                : ItemPriceRang.SALES);
        if (ItemPriceType.CUSTOMIZE.equals(itemPrice.getType())) {
            itemPrice.setIsMain(0);
        } else {
            itemPrice.setIsMain(1);
        }
        return itemPrice;
    }

    private List<ItemPrice> getItemPrices(Long itemId) {
        final LambdaQueryWrapper<ItemPrice> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ItemPrice::getItemId, itemId);
        queryWrapper.in(ItemPrice::getType,
                Arrays.asList(
                        ItemPriceType.DAILY.getValue(),
                        ItemPriceType.LINE.getValue()
                ));
        return itemPriceService.list(queryWrapper);
    }

    @NonNull
    private Optional<Item> getItem(Long itemId) {
        return Optional.ofNullable(itemService.getById(itemId));
    }

    private Optional<ItemSku> getItemSku(ImportRow sheetRow) {
        return itemSkuService.lambdaQuery()
                .eq(ItemSku::getSkuCode, sheetRow.getSkuCode()).oneOpt();
    }
}
