package com.daddylab.supplier.item.infrastructure.third.base;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.third.dto.AccessTokenForm;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ShopAuthorization;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopAuthorizationService;
import com.daddylab.supplier.item.infrastructure.third.consts.ThirdRedisConstants;
import com.daddylab.supplier.item.infrastructure.third.enums.AccessTokenTypeEnum;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @class AccessTokenServiceImpl.java
 * @description token 基础服务
 * @date 2024-02-27 17:57
 */
@Service
public class AccessTokenServiceImpl implements AccessTokenService {

    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private IShopAuthorizationService shopAuthorizationService;

    @Override
    public SingleResponse<Boolean> saveAccessToken(AccessTokenTypeEnum typeEnum, AccessTokenForm tokenForm) {
        final long currentTime = DateUtil.currentTime();
        String shopSn = tokenForm.getShopSn();

        String accessToken = tokenForm.getAccessToken();
        redisTemplate.opsForValue()
                     .set(String.format(ThirdRedisConstants.THIRD_ACCESS_TOKEN_KEY, typeEnum, shopSn), accessToken);

        final ShopAuthorization shopAuthorization = new ShopAuthorization();
        shopAuthorization.setAccessToken(accessToken);
        switch (typeEnum) {

            case RED_BOOK: {
                final Platform platform = Platform.XIAOHONGSHU;
                shopAuthorization.setPlatform(platform);
                shopAuthorization.setSn(shopSn);

                if (tokenForm.getData() != null) {
                    JSONObject tokenData = tokenForm.getData();
                    shopAuthorization.setAccessToken(tokenData.getString("accessToken"));
                    shopAuthorization.setExpiredAt(tokenData.getLongValue("accessTokenExpiresAt") / 1000);
                    shopAuthorization.setRefreshToken(tokenData.getString("refreshToken"));
                    shopAuthorization.setRefreshTokenExpiredAt(tokenData.getLongValue("refreshTokenExpiresAt") / 1000);
                    shopAuthorization.setTokenData(JSON.toJSONString(tokenData));
                }
                shopAuthorizationService.saveOrUpdateShopAuthorization(shopAuthorization);
            }
            break;
            case KUAI_SHOU: {
                final Platform platform = Platform.KUAISHOU;
                shopAuthorization.setPlatform(platform);
                shopAuthorization.setSn(shopSn);

                if (tokenForm.getData() != null) {
                    JSONObject tokenData = tokenForm.getData();
                    shopAuthorization.setAccessToken(tokenData.getString("access_token"));
                    shopAuthorization.setExpiredAt(tokenData.getLongValue("expires_in") + currentTime);
                    shopAuthorization.setRefreshToken(tokenData.getString("refresh_token"));
                    shopAuthorization.setRefreshTokenExpiredAt(tokenData.getLongValue("refresh_token_expires_in") + currentTime);
                    shopAuthorization.setTokenData(JSON.toJSONString(tokenData));
                }
                shopAuthorizationService.saveOrUpdateShopAuthorization(shopAuthorization);
            }
            break;
        }
        return SingleResponse.of(Boolean.TRUE);
    }

    @Override
    public String getAccessToken(AccessTokenTypeEnum accessTokenTypeEnum) {
        return redisTemplate.opsForValue()
                            .get(String.format(ThirdRedisConstants.THIRD_ACCESS_TOKEN_KEY, accessTokenTypeEnum));
    }
}
