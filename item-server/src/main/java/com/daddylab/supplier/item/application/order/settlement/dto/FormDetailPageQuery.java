package com.daddylab.supplier.item.application.order.settlement.dto;

import com.daddylab.supplier.item.common.domain.dto.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2023年08月09日 3:29 PM
 */
@ApiModel("结算单明细查询参数")
@EqualsAndHashCode(callSuper = true)
@Data
public class FormDetailPageQuery extends PageQuery {

    private static final long serialVersionUID = -8307700253248356578L;

    @ApiModelProperty("id")
    private List<Long> ids;

    public static FormDetailPageQuery buildExportQuery(List<Long> ids, Integer pageIndex, Integer pageSize){
        FormDetailPageQuery query = new FormDetailPageQuery();
        query.setIds(ids);
        query.setPageIndex(pageIndex);
        query.setPageSize(pageSize);
        return query;
    }
}
