package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OffShelfFeedback;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OffShelfFeedbackMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOffShelfFeedbackService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 下架管理-下架反馈 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Service
public class OffShelfFeedbackServiceImpl extends DaddyServiceImpl<OffShelfFeedbackMapper, OffShelfFeedback> implements IOffShelfFeedbackService {

}
