package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OaCallback;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OaCallbackMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOaCallbackService;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.oa.OaCgfkCallback;
import com.daddylab.supplier.item.types.process.ProcessBusinessType;

import org.springframework.stereotype.Service;

/**
 * OA回调数据 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-24
 */
@Service
public class OaCallbackServiceImpl extends DaddyServiceImpl<OaCallbackMapper, OaCallback>
        implements IOaCallbackService {

    @Override
    public void saveCallback(
            ProcessBusinessType businessType, String businessId, OaCgfkCallback callback) {
        final OaCallback entity = new OaCallback();
        entity.setBusinessType(businessType.getValue());
        entity.setBusinessId(businessId);
        entity.setCallback(JsonUtil.toJson(callback));
        save(entity);
    }
}
