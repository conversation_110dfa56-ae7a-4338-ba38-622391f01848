package com.daddylab.supplier.item.domain.exportTask.dto.purchase;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> up
 * @date 2022/3/30 5:38 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseOrderDetailSheet extends ExportSheet {
    @ExcelProperty(value = "采购单号")
    protected String no;

    @ExcelProperty(value = "采购日期")
    protected String purchaseDate;

    @ExcelProperty(value = "采购类型")
    protected String purchaseType;

    @ExcelProperty(value = "组织")
    protected String organization;

    @ExcelProperty(value = "合作方")
    protected String corpType;

    @ExcelProperty(value = "业务类型")
    protected String bizType;

    @ExcelProperty(value = "合同编号")
    protected String contractNo;

    @ExcelProperty(value = "供应商")
    protected String providerName;

    @ExcelProperty(value = "到货方式")
    protected String arrivalType;

    @ExcelProperty(value = "付款方式")
    protected String payType;

    @ExcelProperty(value = "采购部门")
    protected String department;

    @ExcelProperty(value = "采购组")
    protected String erpGroup;

    @ExcelProperty(value = "采购员")
    protected String buyerName;

    @ExcelProperty(value = "商品sku")
    protected String itemSkuCode;

    @ExcelProperty(value = "商品名称")
    protected String itemName;

    @ExcelProperty(value = "规格名称")
    protected String specifications;

    @ExcelProperty(value = "采购数量")
    protected String purchaseQuantity;

    @ExcelProperty(value = "单价（税前）")
    protected String taxPrice;

    @ExcelProperty(value = "税率%")
    protected String taxRate;

    @ExcelProperty(value = "税额")
    protected String taxQuota;

    @ExcelProperty(value = "税前金额")
    protected String totalPriceTax;

    @ExcelProperty(value = "税后单价")
    protected String afterTaxPrice;

    @ExcelProperty(value = "税后金额")
    protected String afterTaxAmount;

    @ExcelProperty(value = "是否赠品")
    protected String gift;

    @ExcelProperty(value = "仓库")
    protected String warehouse;

    @ExcelProperty(value = "备注")
    protected String remark;


    @ExcelIgnore
    protected Long departmentId;

    @ExcelIgnore
    protected Integer businessLine;

    @ExcelIgnore
    protected Long itemId;

}
