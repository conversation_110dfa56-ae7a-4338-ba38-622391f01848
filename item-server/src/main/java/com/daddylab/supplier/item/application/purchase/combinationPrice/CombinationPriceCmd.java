package com.daddylab.supplier.item.application.purchase.combinationPrice;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年10月08日 2:38 PM
 */
@Data
public class CombinationPriceCmd extends Command {

    private Long id;

    /**
     * 商品sku
     */
    @ApiModelProperty(value = "商品sku")
    @NotEmpty(message = "商品SKU不得为空")
    private String itemSku;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    /**
     * 商品规格
     */
    @ApiModelProperty(value = "商品规格")
    private String itemSpecs;

    /**
     * 采购地点
     */
    @ApiModelProperty(value = "采购地点0:工厂 1:仓库")
    private Integer purchaseArea;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String provider;

    /**
     * 采购负责人
     */
    @ApiModelProperty(value = "采购负责人")
    private Long buyerId;

    /**
     * 日常供价(无优惠)
     */
    @ApiModelProperty(value = "日常供价(无优惠)")
    private BigDecimal usualPrice;

    /**
     * 规格数量
     */
    @ApiModelProperty(value = "规格数量")
    private Long specsCount;

    /**
     * 是否纯活动商品 0:否 1:是
     */
    @ApiModelProperty(value = "是否纯活动商品 0:否 1:是")
    private Integer isActive;

    /**
     * 优惠类型 0:货抵款 1:按时间供价 2:当月优惠件数
     */
    @ApiModelProperty(value = "优惠类型 0:货抵款 1:按时间供价 2:当月优惠件数")
    private Integer favourableType;

    /**
     * 平台名称 0:ALL 1:淘宝 2:抖店 3:小红书 4:自研电商 5:有赞 6:快手
     */
    @ApiModelProperty(value = "平台名称 0:ALL 1:淘宝 2:抖店 3:小红书 4:自研电商 5:有赞 6:快手")
    private Integer platformType;

    /**
     * 方式 0:大促 1:直播 2:无
     */
    @ApiModelProperty(value = "方式 0:大促 1:直播 2:无")
    private Integer activeType;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    private Long startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    private Long endTime;

    /**
     * 订单拍下份数
     */
    @ApiModelProperty(value = "订单拍下份数")
    private Long orderCount;

    /**
     * 实发单品数量
     */
    @ApiModelProperty(value = "实发单品数量")
    @NotNull(message = "实发单品数量不得为空")
    private Long finalCount;

    /**
     * 按价格优惠结算成本/元
     */
    @ApiModelProperty(value = "按价格优惠结算成本/元")
    private BigDecimal priceCost;

    /**
     * 按数量优惠结算成本/元
     */
    @ApiModelProperty(value = "按数量优惠结算成本/元")
    private BigDecimal numCost;

    /**
     * 供价优惠内容
     */
    @ApiModelProperty(value = "供价优惠内容")
    private String content;

    /**
     * 备注说明
     */
    @ApiModelProperty(value = "备注说明")
    private String mark;


    @ApiModelProperty(value = "合作模式。0:电商。1:老爸抽检。2:绿色家装。3:商家入驻")
    private Integer businessLine;


    /**
     * 0:采购活动价。1:sku日常阶梯价。2:sku活动阶梯价。3:spu日常阶梯价。4:spu活动阶梯价
     */
    @ApiModelProperty(value = "1:sku日常阶梯价。2:sku活动阶梯价")
    @NotNull(message = "价格类型不得为空")
    private Integer priceType;

}
