package com.daddylab.supplier.item.infrastructure.accessControl;

import com.daddylab.supplier.item.domain.auth.PermRefreshManager;
import com.daddylab.supplier.item.domain.auth.listeners.PolicyUpdatedListener;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQConsumerBase;
import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQConsumerGroup;
import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQTopic;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import java.time.LocalDateTime;
import javax.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.redisson.api.RedissonClient;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/2/18
 */
@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = MQConsumerGroup.REFRESH_PERMISSION, topic = MQTopic.CASBIN_POLICY_UPDATED, consumeThreadMax = 1)
public class RefreshPermissionConsumer extends RocketMQConsumerBase<Void> implements
        PolicyUpdatedListener {

    private final PermRefreshManager permRefreshManager;

    public RefreshPermissionConsumer(RedissonClient redissonClient,
            StandardEnvironment environment,
            RefreshConfig refreshConfig,
            PermRefreshManager permRefreshManager) {
        super(redissonClient, environment, refreshConfig);
        this.permRefreshManager = permRefreshManager;
    }

    @Override
    public void handlePolicyUpdated(long updateTimestamp) {
        final LocalDateTime lastRefreshTime = DateUtil.millisToLocalDateTime(updateTimestamp);
        permRefreshManager.setLastRefreshTime(lastRefreshTime);
    }

    @Override
    protected void handle(MessageExt msg, @Nullable Void body) {
        final long lastRefreshTime = msg.getBornTimestamp();
        log.info("接收到权限系统变更消息，权限刷新时间:{}，msg={} body={}", lastRefreshTime, msg, body);
        handlePolicyUpdated(lastRefreshTime);
    }

    @Override
    public void prepareStart(DefaultMQPushConsumer consumer) {
        //consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_TIMESTAMP);
        //consumer.setConsumeTimestamp(UtilAll.timeMillisToHumanString3(System.currentTimeMillis()));
    }
}
