package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.GetTokenQyWeixinResult;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.SendMsgQyWeixinResult;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.SendQyWeixinMsgParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: <PERSON>
 * @Date: 2022/6/1 15:31
 * @Description: 调用企业微信的 Client
 */
@FeignClient(url = "https://qyapi.weixin.qq.com/cgi-bin", name = "QyWeixinFeignClient")
public interface QyWeixinFeignClient {

    /**
     * 获取 token
     *
     * @param corpid corpid
     * @param corpsecret corpsecret
     * @return GetTokenQyWeixinResult
     */
    @RequestMapping(path = "/gettoken", method = RequestMethod.GET)
    GetTokenQyWeixinResult getToken(@RequestParam("corpid") String corpid,
                                    @RequestParam("corpsecret") String corpsecret);

    /**
     * 发送企业微信消息
     *
     * @param accessToken accessToken
     * @param param param
     * @return SendMsgQyWeixinResult
     */
    @RequestMapping(path = "/message/send", method = RequestMethod.POST)
    SendMsgQyWeixinResult sendMessage(@RequestParam("access_token") String accessToken,
                                      @RequestBody SendQyWeixinMsgParam param);
}
