package com.daddylab.supplier.item.application.order.settlement.sys;

import cn.hutool.core.collection.CollUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderSettlementDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderSettlementForm;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderSettlementDetailService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderSettlementFormService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2023年08月22日 1:51 PM
 */
@Service
@Slf4j
public class OrderSettlementSysDao {

    @Resource
    IOrderSettlementFormService iOrderSettlementFormService;

    @Resource
    IOrderSettlementDetailService iOrderSettlementDetailService;

    @Transactional(rollbackFor = Exception.class)
    public void systemGenerate(OrderSettlementForm form, Collection<OrderSettlementDetail> details) {
        iOrderSettlementFormService.save(form);
        Long id = form.getId();

        if (CollUtil.isNotEmpty(details)) {
            iOrderSettlementDetailService.saveBatch(
                    details.stream().peek(val -> val.setFormId(id)).collect(Collectors.toList()));
        }
    }
}
