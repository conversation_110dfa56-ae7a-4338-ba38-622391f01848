package com.daddylab.supplier.item.domain.brand.gateway;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.common.domain.EntityChange;
import com.daddylab.supplier.item.common.enums.EnableStatusEnum;
import com.daddylab.supplier.item.domain.brand.dto.BrandDropDownItem;
import com.daddylab.supplier.item.domain.brand.dto.BrandDropDownQuery;
import com.daddylab.supplier.item.domain.brand.dto.BrandListItem;
import com.daddylab.supplier.item.domain.brand.dto.BrandQuery;
import com.daddylab.supplier.item.domain.brand.entity.BrandEntity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Brand;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface BrandGateway {
    Long id(String sn);

    boolean existSameName(String name, Long selfId);

    boolean existSameName(String name);

    boolean existSn(String sn);

    boolean existRelateBrandWithProvider(Long providerId);

    boolean removeRelateBrandForProvider(Long providerId);

    Long existSameNameThenReturnId(String name);

    Brand existSameNameThenReturnPo(String name);

    Map<String, Brand> batchQueryBrandByName(List<String> names);

    Map<Long, Brand> batchQueryBrandById(Collection<Long> ids);

    Map<String, Brand> batchQueryBrandByCode(List<String> codes);

    boolean batchSaveOrUpdateBrandPo(List<Brand> brandPos);

    boolean updatePo(Brand brand);

    boolean updateEntity(EntityChange<BrandEntity> entityChange);

    boolean createEntity(BrandEntity brand);

    Long getBrandId(String name);
    Long syncWdtBrand(String name);

    List<BrandListItem> queryBrandList(BrandQuery brandQuery);

    Page<BrandListItem> pageQueryBrandList(BrandQuery brandQuery);

    BrandEntity getBrand(Long id);

    void updateBrandStatus(Long id, EnableStatusEnum status);

    void deleteBrand(Long id);

    List<BrandDropDownItem> dropDownList(BrandDropDownQuery query);

    int getNextSerialNum();

    Brand getBrandById(Long id);

    Map<Long, String> names(List<Long> ids);
}
