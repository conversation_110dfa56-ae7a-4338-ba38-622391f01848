package com.daddylab.supplier.item.domain.contract;

import com.daddylab.supplier.item.types.contract.ContractDropdownItem;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/4/1
 */
public interface ContractGateway {
    /**
     * 合同下拉列表
     *
     * @param contractNo 合同编号
     */
    List<ContractDropdownItem> contractDropdownList(String contractNo, Long partnerProviderId, Integer status);
}
