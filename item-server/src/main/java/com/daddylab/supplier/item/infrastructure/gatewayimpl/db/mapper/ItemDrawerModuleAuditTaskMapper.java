package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerModuleAuditTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 商品库抽屉模块审核 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-11
 */
public interface ItemDrawerModuleAuditTaskMapper extends DaddyBaseMapper<ItemDrawerModuleAuditTask> {

    List<ItemDrawerModuleAuditTask> selectByItemIdAndRound(@Param("type") ItemAuditType type, @Param("round") Integer round, @Param("itemId") Long itemId);

    int undelete(Collection<Long> ids);
}
