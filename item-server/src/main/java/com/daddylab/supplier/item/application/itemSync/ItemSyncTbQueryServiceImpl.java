package com.daddylab.supplier.item.application.itemSync;

import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerImageTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchPlan;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerImageService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemLaunchPlanService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INewGoodsService;
import com.daddylab.supplier.item.infrastructure.utils.FileUtil;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.EditTbGoodsParam;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/9/21
 */
@Service
@Slf4j
@AllArgsConstructor
public class ItemSyncTbQueryServiceImpl implements ItemSyncTbQueryService {

    /**
     * 同步淘宝主图数量限制
     */
    public static final int MAIN_IMAGE_NUM_LIMIT = 20;

    public static final ImmutableMap<String, String> TYPE_MAPPING = ImmutableMap.<String, String>builder()
            .put("jpeg", "jpg").build();
    private final INewGoodsService newGoodsService;
    private final IItemDrawerService itemDrawerService;
    private final IItemDrawerImageService itemDrawerImageService;
    private final IItemLaunchPlanService itemLaunchPlanService;


    @Override
    public EditTbGoodsParam editTbGoodsParamQuery(Long itemId, String tbGoodsId) {
        final EditTbGoodsParam editTbGoodsParam = new EditTbGoodsParam();
        final Optional<ItemDrawer> itemDrawerOpt = itemDrawerService.lambdaQuery()
                .eq(ItemDrawer::getItemId, itemId)
                .oneOpt();
        if (!itemDrawerOpt.isPresent()) {
            return editTbGoodsParam;
        }
        final ItemDrawer itemDrawer = itemDrawerOpt.get();
        final Long itemDrawerId = itemDrawer.getId();

        final Optional<ItemLaunchPlan> plan = Optional.ofNullable(
                itemLaunchPlanService.getPlanByItemId(itemId));

        final List<ItemDrawerImage> itemDrawerImages = itemDrawerImageService.lambdaQuery()
                .eq(ItemDrawerImage::getDrawerId, itemDrawerId)
                .in(ItemDrawerImage::getType,
                        Arrays.asList(ItemDrawerImageTypeEnum.ITEM, ItemDrawerImageTypeEnum.DETAIL))
                .list();

        final Map<ItemDrawerImageTypeEnum, List<ItemDrawerImage>> itemDrawerImagesGroup = itemDrawerImages.stream()
                .collect(Collectors.groupingBy(ItemDrawerImage::getType));

        final LinkedHashMap<String, String> mainPics = Maps.newLinkedHashMap();
        final List<ItemDrawerImage> mainImages = itemDrawerImagesGroup.getOrDefault(
                ItemDrawerImageTypeEnum.ITEM, Collections.emptyList());
        if (!mainImages.isEmpty()) {
            final List<ItemDrawerImage> mainImagesSortedList = mainImages.stream()
                    .sorted(Comparator.comparing(ItemDrawerImage::getSort))
                    .collect(Collectors.toList());
            for (int i = 0; i < mainImagesSortedList.size() && i < MAIN_IMAGE_NUM_LIMIT; i++) {
                final ItemDrawerImage itemDrawerImage = mainImagesSortedList.get(i);
                final String url = itemDrawerImage.getUrl();
                final String suffix = FileUtil.getSuffix(url);
                mainPics.put("主图-" + (i + 1) + "." + TYPE_MAPPING.getOrDefault(suffix, suffix), url);
            }
        }

        final List<ItemDrawerImage> detailImages = itemDrawerImagesGroup.getOrDefault(
                ItemDrawerImageTypeEnum.DETAIL, Collections.emptyList());
        final LinkedHashMap<String, String> detailPics = Maps.newLinkedHashMap();
        if (!detailImages.isEmpty()) {
            final List<ItemDrawerImage> detailImagesSortedList = detailImages.stream()
                    .sorted(Comparator.comparing(ItemDrawerImage::getSort))
                    .collect(Collectors.toList());
            for (int i = 0; i < detailImagesSortedList.size(); i++) {
                final ItemDrawerImage itemDrawerImage = detailImagesSortedList.get(i);
                final String url = itemDrawerImage.getUrl();
                final String suffix = FileUtil.getSuffix(url);
                detailPics.put("详情-" + (i + 1) + "." + TYPE_MAPPING.getOrDefault(suffix, suffix), url);
            }
        }

        editTbGoodsParam.setPlanTime(plan.map(ItemLaunchPlan::getPlanName).orElse(null));
        editTbGoodsParam.setSpId(tbGoodsId);
        editTbGoodsParam.setSpName(itemDrawer.getTbTitle());
        editTbGoodsParam.setMainPic(mainPics);
        editTbGoodsParam.setDetailPic(detailPics);

        return editTbGoodsParam;
    }
}
