package com.daddylab.supplier.item.application.drawer.impl.copySupport;

import com.daddylab.supplier.item.application.drawer.ItemDrawerCopyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @class ItemDrawerImageCopyServiceImpl.java
 * @description 描述类的作用
 * @date 2024-04-09 16:35
 */
@Service
public class ItemDrawerImageCopyServiceImpl implements ItemDrawerCopyService {

    @Autowired
    private IItemDrawerImageService itemDrawerImageService;

    @Override
    public void handler(ItemDrawer sourceDrawer, ItemDrawer targetDrawer) {
        handler0(sourceDrawer, targetDrawer, Boolean.FALSE);
    }

    public void handler0(ItemDrawer sourceDrawer, ItemDrawer targetDrawer, Boolean isLiveVerbal) {
        List<ItemDrawerImage> targetDrawerImagesList = itemDrawerImageService.queryDrawerImages(targetDrawer.getId(), isLiveVerbal);
        if (!targetDrawerImagesList.isEmpty()) {
            List<Long> itemDrawImageIds = targetDrawerImagesList.stream().map(ItemDrawerImage::getId).collect(Collectors.toList());
            itemDrawerImageService.removeByIds(itemDrawImageIds);
        }
        List<ItemDrawerImage> sourceDrawerImagesList = itemDrawerImageService.queryDrawerImages(sourceDrawer.getId(), isLiveVerbal);
        if (!sourceDrawerImagesList.isEmpty()) {
            List<ItemDrawerImage> newDrawerImages = sourceDrawerImagesList.stream()
                    .peek(itemDrawerImage -> itemDrawerImage.setDrawerId(targetDrawer.getId()))
                    .collect(Collectors.toList());
            itemDrawerImageService.saveBatch(newDrawerImages);
        }
    }
}
