package com.daddylab.supplier.item.controller.item.dto.detail;

import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13 11:24 上午
 * @description
 */
@Data
@ApiModel("商品详情-基础信息返回封装")
public class ItemDetailBaseVo {

    @ApiModelProperty("商品名字")
    private String name;
    @ApiModelProperty("商品编码")
    private String code;
    @ApiModelProperty("供货商指定编码")
    private String specialCode;
    @ApiModelProperty("关联供应商商品款号")
    private String partnerCode;
    @ApiModelProperty("关联供应商业务线")
    private Integer businessLine;
    @ApiModelProperty("关联供应商业务线(多选)")
    private List<Integer> businessLines;
    @ApiModelProperty("商品品类路径")
    private String categoryPath;
    @ApiModelProperty("商品拓展属性")
    private List<String> props;
    @ApiModelProperty("商品品类id")
    private Long categoryId;
    @ApiModelProperty("商品品类id路径（高级到低级）")
    private String categoryIdPath;
    @ApiModelProperty("商品品牌")
    private String brandName;
    @ApiModelProperty("商品品牌id")
    private Long brandId;
    @ApiModelProperty("供应商")
    private String providerName;
    @ApiModelProperty("供应商Id")
    private Long providerId;
    @ApiModelProperty("P系统供应商ID")
    private Long partnerProviderId;
    @ApiModelProperty("是否黑名单")
    private Integer isBlacklist;
    @ApiModelProperty("采购员")
    private String buyerName;
    @ApiModelProperty("采购员UserId")
    private String buyerUserId;
    @ApiModelProperty("采购负责人花名")
    protected String buyerNickName;
    @ApiModelProperty("发货渠道")
    private String delivery;
    @ApiModelProperty("商品状态,")
    private Integer itemStatus;
    @ApiModelProperty("商品状态理由")
    private String statusReason;

    @ApiModelProperty("是否是赠品")
    private Integer isGift;
    @ApiModelProperty("关联主商品id")
    private Long parentItemId;
    @ApiModelProperty("关联主商品code")
    private String parentCode;

    private List<SonItemVO> sonItemList;

    @ApiModelProperty(value = "商品标签", notes = "DAD_SAMPLING_INSPECTION 老爸抽检, CUSTOMIZED_PRODUCTS 定制品")
    List<String> tags;

    @ApiModelProperty("商品简称")
    private String shortName;

    @ApiModelProperty("合作方业务类型")
    private List<CorpBizTypeDTO> corpBizType;

    @ApiModelProperty("商品下架时间")
    private Long downFrameTime;

    @ApiModelProperty("商品下架原因")
    private String downFrameReason;




}
