package com.daddylab.supplier.item.application.purchasePayable.vo;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayableApplyOrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> up
 * @date 2023年02月06日 3:41 PM
 */
@Data
@ApiModel("申请付款页面详情")
public class ApplyPayOrderDetailVO {

    private Long providerId;

    private String providerName;

    private BigDecimal sourcePayTotalAmount;

    private Long applyDate;

    private String applyNo;

    private PayableApplyOrderStatus status;

    @ApiModelProperty("附件id")
    private Long additionalId;

    @ApiModelProperty("附件url")
    private String additionalUrl;

    @ApiModelProperty("附件名称")
    private String additionalName;

//    private BigDecimal applyPayTotalAmount;


}
