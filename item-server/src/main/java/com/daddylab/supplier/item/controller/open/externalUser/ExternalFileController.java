package com.daddylab.supplier.item.controller.open.externalUser;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.infrastructure.oss.OssGateway;
import com.daddylab.supplier.item.infrastructure.upyun.gateway.UpyunGateway;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RequestMapping("/external/file")
@RestController
public class ExternalFileController {
    @Autowired
    private FileGateway fileGateway;

    @Autowired
    private UpyunGateway upyunGateway;

    @Autowired
    private OssGateway ossGateway;

    @GetMapping(value = "/getUploadSign")
    public SingleResponse<Map<String, String>> getUploadSign(@RequestParam("path") String path) {
        return SingleResponse.of(upyunGateway.getFormUploadSign(path));
    }

    @GetMapping(value = "/getOssUploadSign")
    public SingleResponse<String> getOssUploadSign(
            @RequestParam("params") Map<String, String> params) {
        return SingleResponse.of(ossGateway.sign(params));
    }

}
