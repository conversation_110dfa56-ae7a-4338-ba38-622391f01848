package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemOptimize;
import com.daddylab.supplier.item.types.itemOptimize.ItemOptimizePersistData;

import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 商品优化 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface ItemOptimizeMapper extends DaddyBaseMapper<ItemOptimize> {

    ItemOptimizePersistData selectDataField(@Param("id") Long id, @Param("fieldName") String fieldName);

    boolean updateSubmitMetadata(
            @Param("id") Long id,
            @Param("userId") Long userId,
            @Param("time") Long time,
            @Param("isReview") Boolean isReview);
}
