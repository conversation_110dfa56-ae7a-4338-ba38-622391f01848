package com.daddylab.supplier.item.application.handingsheet;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.daddylab.ark.sailor.item.domain.query.item.ItemPageQuery;
import com.daddylab.ark.sailor.item.domain.vo.item.ItemSkuVO;
import com.daddylab.supplier.item.application.message.QyMsgSendUtils;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.application.platformItem.PlatformItemBizService;
import com.daddylab.supplier.item.application.platformItem.data.PlatformItemListItem;
import com.daddylab.supplier.item.application.platformItem.query.PlatformItemPageQuery;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsAssembler;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.ErrorChecker;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.ResponseAssert;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.trans.CommonEnumTransMapper;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.common.trans.TimeTransMapper;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.handingsheet.dto.HandingSheetItemPageQuery;
import com.daddylab.supplier.item.controller.handingsheet.dto.HandingSheetParam;
import com.daddylab.supplier.item.controller.item.ArkSailorItemBizService;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.exportTask.ExportTaskGateway;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.item.gateway.BuyerGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.config.HandingSheetProperties;
import com.daddylab.supplier.item.infrastructure.config.ItemOutlinkProperties;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.events.SaveEvent;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.HandingSheetItemSpuJoinSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.HandingSheetItemSpuMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLock;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.daddylab.supplier.item.types.handingsheet.*;
import com.daddylab.supplier.item.types.item.ItemListWithSkuVO;
import com.daddylab.supplier.item.types.operateLog.TypedTargetId;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.yulichang.toolkit.MPJWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.multimap.HashSetValuedHashMap;
import org.javers.core.diff.Diff;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Nullable;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.daddylab.supplier.item.infrastructure.utils.NumberUtil.isPositive;
import static com.daddylab.supplier.item.infrastructure.utils.ObjectUtil.setValueIfNotEmpty;

/**
 * <AUTHOR>
 * @since 2024/1/22
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HandingSheetV2BizServiceImpl implements HandingSheetV2BizService {
    private final IHandingSheetItemSpuService handingSheetItemSpuService;
    private final IHandingSheetItemSkuService handingSheetItemSkuService;
    private final IItemService itemService;
    private final IItemSkuService itemSkuService;
    private final IItemProcurementService itemProcurementService;
    private final BuyerGateway buyerGateway;
    private final INewGoodsService newGoodsService;
    private final ICategoryService categoryService;
    private final PlatformItemBizService platformItemBizService;
    private final IPlatformItemSkuService platformItemSkuService;
    private final ItemOutlinkProperties itemOutlinkProperties;
    private final HandingSheetService handingSheetService;
    private final OperateLogDomainService operateLogDomainService;
    private final IItemDrawerService itemDrawerService;
    private final ArkSailorItemBizService arkSailorItemBizService;
    private final ExportTaskGateway exportTaskGateway;
    private final FileGateway fileGateway;
    private final RefreshConfig refreshConfig;
    private final HandingSheetProperties handingSheetProperties;
    private final MsgSender msgSender;
    private final HandingSheetStaffService handingSheetStaffService;
    private final UserGateway userGateway;
    private final IDadStaffService iDadStaffService;
    private final StaffService staffService;

    public static final Map<Object, Function<Object, String>> LOG_PRINTERS = new HashMap<>();

    static {
        LOG_PRINTERS.put(
                "platforms",
                v -> {
                    final List<Integer> v1 = (List<Integer>) v;
                    return v1.stream().map(Platform::of).filter(Objects::nonNull).map(Platform::getDesc).collect(
                            Collectors.joining("、"));
                });
    }

    @Override
    @SneakyThrows
    public void excelTemplate(List<Integer> platforms, HttpServletResponse response) {
        final ServletOutputStream outputStream = response.getOutputStream();
        final List<Platform> platformEnums = toPlatformEnums(platforms);
        final List<List<String>> head = exportHeaders(platformEnums);
        EasyExcel.write(outputStream).head(head).sheet("工作表1").doWrite(Collections.emptyList());
    }

    private void exportItems(HandingSheetItemPageQuery query, OutputStream outputStream) throws IOException {
        final Long handingSheetId = query.getHandingSheetId();
        final HandingSheet handingSheet = handingSheetService.getById(handingSheetId);
        Objects.requireNonNull(handingSheet, "未找到盘货表记录");

        Assert.isTrue(StringUtil.isNotBlank(handingSheet.getPlatform()), "盘货表未选择平台");
        final List<Platform> platforms = StringUtil.split(handingSheet.getPlatform(), ",")
                .stream()
                .map(Integer::parseInt)
                .map(Platform::of)
                .collect(Collectors.toList());

        final PageResponse<HandingSheetItemVO> itemQueryResponse = handingSheetItemQuery(query);
        ErrorChecker.checkAndThrowIfError(itemQueryResponse);
        final List<HandingSheetItemVO> data = itemQueryResponse.getData();

        final List<List<String>> head = exportHeaders(platforms);
        EasyExcel.write(outputStream).head(head).sheet("工作表1").doWrite(exportData(data));
    }

    private List<List<String>> exportData(List<HandingSheetItemVO> data) {
        final List<List<String>> exportData = new ArrayList<>();
        for (HandingSheetItemVO sku : data) {
            final ArrayList<String> exportRowData = new ArrayList<>();

            final List<HandingSheetItemPlatformItem> platformItems = sku.getPlatformItems();
            for (int i = 0; i < platformItems.size(); i++) {
                final HandingSheetItemPlatformItem platformItem = platformItems.get(i);
                exportRowData.add(platformItem.getOuterItemId());
                if (i == 0) {
                    exportRowData.add(platformItem.getGoodsName());
                }
            }
            exportRowData.add(sku.getKeyProductOperationFeedback());
            exportRowData.add(sku.getItemCode());
            exportRowData.add(sku.getSkuCode());
            exportRowData.add(sku.getSpecifications());
            exportRowData.add(CommonEnumTransMapper.INSTANCE.platformIdListToNameStr(sku.getPlatforms()));
            exportRowData.add(sku.getCategoryPath());

            final Optional<HandingSheetItemVO> skuBizFields = Optional.of(sku);

            // 单件是否包邮
            exportRowData.add(skuBizFields.map(HandingSheetItemVO::getIsFreeShippingForSingleItem)
                    .map(v -> v ? "是" : "否")
                    .orElse(""));
            // 产品日销价/SKU原价
            exportRowData.add(skuBizFields.map(HandingSheetItemVO::getDailyPriceOrOriginalPrice)
                    .map(BigDecimal::toPlainString)
                    .orElse(""));
            // 日常活动机制
            exportRowData.add(skuBizFields.map(HandingSheetItemVO::getDailyActivities).orElse(""));
            // S级活动价
            exportRowData.add(skuBizFields.map(HandingSheetItemVO::getSLevelActivePrice)
                    .map(BigDecimal::toPlainString)
                    .orElse(""));
            // S级活动机制
            exportRowData.add(skuBizFields.map(HandingSheetItemVO::getSLevelActiveContent).orElse(""));
            // S级直播价
            exportRowData.add(skuBizFields.map(HandingSheetItemVO::getSLevelPromoteLivePrice).orElse(""));
            // S级直播机制
            exportRowData.add(skuBizFields.map(HandingSheetItemVO::getSLevelPromoteLiveRule).orElse(""));
            exportRowData.add(skuBizFields.map(HandingSheetItemVO::getBuyer).map(StaffBrief::getNickname).orElse(""));
            exportRowData.add(skuBizFields.map(HandingSheetItemVO::getCostPrice)
                    .map(BigDecimal::toPlainString)
                    .orElse(""));
            exportRowData.add(skuBizFields.map(HandingSheetItemVO::getShipmentType).map(v -> {
                if (v == 1) {
                    return "工厂发货";
                }
                return "仓库发货";
            }).orElse(""));
            exportRowData.add(skuBizFields.map(HandingSheetItemVO::getRemark).orElse(""));
            exportData.add(exportRowData);
        }
        return exportData;
    }

    private List<List<String>> exportHeaders(List<Platform> platforms) {
        final ArrayList<String> headers = new ArrayList<>();
        for (int i = 0; i < platforms.size(); i++) {
            final Platform platform = platforms.get(i);
            headers.add(String.format("* 商品id（%s）", platform.getDesc()));
            if (i == 0) {
                headers.add(String.format("* 商品标题（%s）", platform.getDesc()));
            }
        }
        headers.addAll(Arrays.asList("重点品运营反馈",
                "* SPU编码",
                "* SKU编码",
                "规格名称",
                "所属平台",
                "商品类目",
                "单件是否包邮",
                "产品日销价",
                "日常活动机制",
                "S级/新品活动价",
                "S级/新品活动机制",
                "S级一口价/直播价",
                "S级一口价活动机制/直播机制",
                "采购员",
                "采购成本",
                "发货类型",
                "备注"));
        return headers.stream().map(Collections::singletonList).collect(Collectors.toList());
    }


    @Override
    public List<HandingSheetItemVO> importExcel(@Nullable Long handingSheetId,
                                                List<Integer> platforms,
                                                InputStream inputStream) {
        final Long currentUserId = UserContext.getUserId();
        final boolean hasAllDataPermission = UserContext.hasPermission(GlobalConstant.HANDING_SHEET_ITEM_ALL);

        final List<HandingSheetImportModel> objects = EasyExcel.read(inputStream)
                .head(HandingSheetImportModel.class)
                .doReadAllSync();
        log.debug("[导入盘货表]共读取 {} 行数据", objects.size());
        final List<Platform> platformEnums = toPlatformEnums(platforms);
        final Platform mainPlatform = platformEnums.get(0);
        checkImportObjects(mainPlatform, objects);

        final Map<String, HandingSheetImportModel> objectsMap = objects
                .stream()
                .filter(item -> StringUtil.isNotBlank(item.getOuterItemId(mainPlatform)) && StringUtil.isNotBlank(item.getSkuNo()))
                .collect(Collectors.toMap(
                        v -> v.getOuterItemId(mainPlatform)
                                + ":" + v.getSkuNo(),
                        Function.identity(), (a, b) -> b));
        final List<HandingSheetItemVO> currentItems = new ArrayList<>();
        if (isPositive(handingSheetId)) {

            final HandingSheetItemPageQuery query = new HandingSheetItemPageQuery();
            query.setHandingSheetId(handingSheetId);
            query.setRefreshPrice(false);
            query.setCurrentUserId(currentUserId);
            query.setHasAllDataPermission(hasAllDataPermission);
            query.setPageSize(99999);

            final PageResponse<HandingSheetItemVO> response = handingSheetItemQuery(query);
            ErrorChecker.checkAndThrowIfError(response);

            currentItems.addAll(response.getData());
            log.debug("[导入盘货表]当前盘货表已有 {} 行数据", currentItems.size());
        }

        //自动补全导入表格中未填写可带出的数据
        setRefDataForImportModels(objects);

        //更新已存在的数据
        for (HandingSheetItemVO viewItem : currentItems) {
            HandingSheetImportModel importObject = null;
            try {
                if (StringUtil.isBlank(viewItem.getSkuCode())) {
                    continue;
                }
                final String uniqueCode = viewItem.getOuterItemId() + ":" + viewItem.getSkuCode();
                importObject = objectsMap.get(uniqueCode);

                if (importObject != null) {
                    setViewItemByImportObject(viewItem, importObject);
                    log.debug(
                            "[导入盘货表]更新行数据，outerItemId:{} skuCode:{} handingSheetSpuId:{} handingSheetSpuId={} import:{}",
                            viewItem.getOuterItemId(),
                            viewItem.getSkuCode(),
                            viewItem.getHandingSheetSpuId(),
                            viewItem.getHandingSheetSkuId(),
                            importObject.getIndex());
                }
            } catch (Exception e) {
                final String rowIndex = Optional.ofNullable(importObject)
                        .map(HandingSheetImportModel::getIndex)
                        .map(Object::toString)
                        .orElse("?");
                log.error("导入盘货表异常，第{}行参数异常，盘货表ID:{}", handingSheetId, rowIndex, e);
                throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM,
                        String.format("导入盘货表异常，第%s行数据格式有误，请检查导入数据格式是否合法: %s",
                                rowIndex,
                                e.getMessage()));
            }
        }

        final Set<String> skuCodes = objects.stream().map(HandingSheetImportModel::getSkuNo).collect(Collectors.toSet());
        final List<NewGoods> newGoodsList = newGoodsService.selectBatchBySkuCodes(skuCodes);
        final Map<String, NewGoods> newGoodsMapBySkuCode = newGoodsList.stream()
                .collect(Collectors.toMap(NewGoods::getSkuCode,
                        Function.identity(),
                        (a, b) -> a));

        //处理新增记录
        final HashMap<String, Long> tempIdsMap = new HashMap<>();
        L1:
        for (HandingSheetImportModel object : objects) {
            final String mainOuterItemId = object.getOuterItemId(mainPlatform);
            Long handingSheetSpuId = null;
            final HandingSheetItemVO handingSheetItemVO = new HandingSheetItemVO();
            int insertIndex = -1;
            for (int i = 0; i < currentItems.size(); i++) {
                final HandingSheetItemVO item = currentItems.get(i);
                final boolean group = Objects.equals(item.getOuterItemId(), mainOuterItemId);
                final boolean isSameSku = Objects.equals(item.getSkuCode(), object.getSkuNo());
                if (group) {
                    if (isSameSku) {
                        continue L1;
                    }
                    insertIndex = i;
                    handingSheetSpuId = item.getHandingSheetSpuId();
                } else if (insertIndex > -1) {
                    break;
                }
            }
            if (insertIndex > -1) {
                currentItems.add(insertIndex, handingSheetItemVO);
            } else {
                currentItems.add(handingSheetItemVO);
            }
            if (Objects.isNull(handingSheetSpuId)) {
                if (tempIdsMap.containsKey(mainOuterItemId)) {
                    handingSheetSpuId = tempIdsMap.get(mainOuterItemId);
                } else {
                    handingSheetSpuId = generateTempId();
                    tempIdsMap.put(mainOuterItemId, handingSheetSpuId);
                }
            }
            handingSheetItemVO.setHandingSheetId(handingSheetId);
            handingSheetItemVO.setHandingSheetSpuId(handingSheetSpuId);
            final Long handingSheetSkuId = generateTempId();
            handingSheetItemVO.setHandingSheetSkuId(handingSheetSkuId);

            final NewGoods newGoods = newGoodsMapBySkuCode.get(object.getSkuNo());
            if (newGoods != null) {
                refreshPriceFromNewGoods(handingSheetItemVO, newGoods);
            }

            try {
                setViewItemByImportObject(platforms, platformEnums, object, handingSheetItemVO);
            } catch (Exception e) {
                final String rowIndex = String.valueOf(object.getIndex());
                log.error("导入盘货表异常，第{}行参数异常，盘货表ID:{}", handingSheetId, rowIndex, e);
                throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM,
                        String.format("导入盘货表异常，第%s行数据格式有误，请检查导入数据格式是否合法: %s",
                                rowIndex,
                                e.getMessage()));
            }
        }

        checkDuplicateData(currentItems);
        if (isPositive(handingSheetId) && handingSheetProperties.isDirectSaveAfterImport()) {
            final HandingSheetSaveItemsCmd cmd = new HandingSheetSaveItemsCmd();
            cmd.setItems(currentItems);
            cmd.setHandingSheetId(handingSheetId);
            cmd.setCurrentUserId(currentUserId);
            cmd.setHasAllDataPermission(hasAllDataPermission);

            ((HandingSheetV2BizService) AopContext.currentProxy()).saveItems(cmd);
        }
        return currentItems;
    }

    private void refreshPriceFromNewGoods(List<HandingSheetItemVO> currentItems) {
        final Set<String> skuCodes = currentItems.stream()
                .map(HandingSheetItemVO::getSkuCode)
                .collect(Collectors.toSet());
        final List<NewGoods> newGoodsList = newGoodsService.selectBatchBySkuCodes(skuCodes);
        final Map<String, NewGoods> newGoodsMapBySkuCode = newGoodsList.stream()
                .collect(Collectors.toMap(NewGoods::getSkuCode,
                        Function.identity(),
                        (a, b) -> a));
        for (HandingSheetItemVO currentItem : currentItems) {
            Optional.ofNullable(newGoodsMapBySkuCode.get(currentItem.getSkuCode()))
                    .ifPresent(newGoods -> {
                        refreshPriceFromNewGoods(currentItem, newGoods);
                    });
        }
    }

    private void setRefDataForImportModels(List<HandingSheetImportModel> importModels) {
        final List<String> skuCodes = importModels.stream()
                .map(HandingSheetImportModel::getSkuNo)
                .filter(StringUtil::isNotBlank)
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(skuCodes)) {
            return;
        }
        final List<ItemSku> itemSkus = itemSkuService.selectByMixCodes(skuCodes);
        if (CollectionUtil.isEmpty(itemSkus)) {
            return;
        }
        final Map<String, ItemSku> itemSkuMap = itemSkus.stream()
                .collect(Collectors.toMap(ItemSku::getSupplierCode,
                        Function.identity()));
        final List<Long> itemIds = itemSkus.stream().map(ItemSku::getItemId).collect(Collectors.toList());
        final List<Item> items = itemService.listByIds(itemIds);
        if (CollectionUtil.isEmpty(items)) {
            return;
        }
        final List<ItemProcurement> itemProcurements = itemProcurementService.lambdaQuery()
                .in(ItemProcurement::getItemId, itemIds)
                .list();
        final Map<Long, ItemProcurement> itemProcurementMap = itemProcurements.stream()
                .collect(Collectors.toMap(ItemProcurement::getItemId,
                        Function.identity()));
        final Map<Long, Item> itemMap = items.stream().collect(Collectors.toMap(Item::getId, Function.identity()));
        final List<Long> categoryIds = items.stream().map(Item::getCategoryId).collect(Collectors.toList());
        final List<Category> categories = categoryService.listByIds(categoryIds);
        final Map<Long, Category> categoriesMap = categories.stream()
                .collect(Collectors.toMap(Category::getId, Function.identity()));
        for (HandingSheetImportModel importModel : importModels) {
            final String skuNo = importModel.getSkuNo();
            if (StringUtil.isBlank(skuNo)) {
                continue;
            }
            final Optional<ItemSku> itemSkuOptional = Optional.ofNullable(itemSkuMap.get(skuNo));
            final Optional<Item> itemOptional = itemSkuOptional.map(ItemSku::getItemId).map(itemMap::get);
            final Optional<Category> categoryOptional = itemOptional.map(Item::getCategoryId).map(categoriesMap::get);
            if (StringUtil.isBlank(importModel.getSpuNo())) {
                importModel.setSpuNo(itemOptional.map(Item::getCode).orElse(""));
            }
            if (StringUtil.isBlank(importModel.getCategory())) {
                importModel.setCategory(categoryOptional.map(Category::getPath).orElse(""));
            }
            final Optional<ItemProcurement> itemProcurement = itemSkuOptional.map(ItemSku::getItemId)
                    .map(itemProcurementMap::get);
            if (StringUtil.isBlank(importModel.getBuyer())) {
                final Optional<StaffBrief> buyerUser = itemProcurement
                        .map(ItemProcurement::getBuyerId)
                        .flatMap(
                                buyerGateway::buyer)
                        .map(Buyer::getUserId)
                        .map(StaffAssembler.INST::toStaffBrief);
                importModel.setBuyer(buyerUser.map(StaffBrief::getNickname).orElse(""));
            }
        }
    }

    private void checkDuplicateData(List<HandingSheetItemVO> currentItems) {
        if (!handingSheetProperties.isReportImportDuplicate()) {
            return;
        }
        final Map<Long, List<HandingSheetItemVO>> itemsGroupByHandingSpuId = currentItems.stream()
                .collect(Collectors.groupingBy(
                        HandingSheetItemVO::getHandingSheetSpuId));
        final List<Map.Entry<Long, List<HandingSheetItemVO>>> duplicateData = itemsGroupByHandingSpuId.entrySet()
                .stream()
                .filter(items -> items.getValue()
                        .stream()
                        .map(HandingSheetItemVO::getOuterItemId)
                        .distinct()
                        .count() > 1)
                .collect(
                        Collectors.toList());
        if (!duplicateData.isEmpty()) {
            log.error("【盘货表导入重复项目检测】导入数据生成的临时ID存在重复！");
            for (Map.Entry<Long, List<HandingSheetItemVO>> duplicateDatum : duplicateData) {
                for (HandingSheetItemVO handingSheetItemVO : duplicateDatum.getValue()) {
                    log.error("【盘货表导入重复项目检测】handingSheetSpuId:{} handingSheetSkuId:{} outerItemId:{} skuCode:{}",
                            duplicateDatum.getKey(),
                            handingSheetItemVO.getHandingSheetSkuId(),
                            handingSheetItemVO.getOuterItemId(),
                            handingSheetItemVO.getSkuCode());
                }
                log.warn("【盘货表导入重复项目检测】----");
            }
        }
    }

    private void setViewItemByImportObject(List<Integer> platforms,
                                           List<Platform> platformEnums,
                                           HandingSheetImportModel object,
                                           HandingSheetItemVO handingSheetItemVO) {
        final ArrayList<HandingSheetItemPlatformItem> platformItems = Lists.newArrayList();
        for (Platform platformEnum : platformEnums) {
            final HandingSheetItemPlatformItem handingSheetItemPlatformItem = new HandingSheetItemPlatformItem();
            handingSheetItemPlatformItem.setPlatform(platformEnum.getValue());

            final String outerItemId = object.getOuterItemId(platformEnum);
            handingSheetItemPlatformItem.setOuterItemId(outerItemId);
            handingSheetItemPlatformItem.setGoodsName(object.getOuterItemTitle(platformEnum));
            handingSheetItemPlatformItem.setLink(itemOutlinkProperties.getLink(platformEnum, outerItemId));

            platformItems.add(handingSheetItemPlatformItem);
        }
        handingSheetItemVO.setPlatformItems(platformItems);
        handingSheetItemVO.setPlatforms(platforms);
        handingSheetItemVO.setItemCode(object.getSpuNo());
        handingSheetItemVO.setSkuCode(object.getSkuNo());

        setViewItemByImportObject(handingSheetItemVO, object);
    }

    private void setViewItemByImportObject(HandingSheetItemVO viewItem, HandingSheetImportModel importObject) {
        setValueIfNotEmpty("规格名称", viewItem,
                HandingSheetItemVO::setSpecifications,
                importObject.getSkuName());
        setValueIfNotEmpty("商品类目（后端）", viewItem,
                HandingSheetItemVO::setCategoryPath,
                importObject.getCategory());
        setValueIfNotEmpty("所属平台",
                viewItem,
                HandingSheetItemVO::setPlatforms,
                importObject.getPlatforms(),
                CommonEnumTransMapper.INSTANCE::platformNameListStrToPlatformIdList);
        setValueIfNotEmpty("重点品运营反馈", viewItem,
                HandingSheetItemVO::setKeyProductOperationFeedback,
                importObject.getKeyProductOperationFeedback());
        setValueIfNotEmpty("单件是否包邮", viewItem,
                HandingSheetItemVO::setIsFreeShippingForSingleItem,
                importObject.getIsFreeShippingForSingleItem(),
                v -> {
                    v = StringUtil.trim(v);
                    return "是".equals(v) || "包邮".equals(v);
                });
        setValueIfNotEmpty("产品日销价", viewItem,
                HandingSheetItemVO::setDailyPriceOrOriginalPrice,
                importObject.getDailyPriceOrOriginalPrice(),
                this::parsePrice);
        setValueIfNotEmpty("日常活动机制",
                viewItem,
                HandingSheetItemVO::setDailyActivities,
                importObject.getDailyActivities());
        setValueIfNotEmpty("S级/新品活动价", viewItem,
                HandingSheetItemVO::setSLevelActivePrice,
                importObject.getSLevelActivePrice(),
                this::parsePrice);
        setValueIfNotEmpty("S级/新品活动机制", viewItem,
                HandingSheetItemVO::setSLevelActiveContent,
                importObject.getSLevelActiveContent());
        setValueIfNotEmpty("S级一口价/直播价", viewItem,
                HandingSheetItemVO::setSLevelPromoteLivePrice,
                importObject.getSLevelPromoteLivePrice());
        setValueIfNotEmpty("S级一口价活动机制/直播机制", viewItem,
                HandingSheetItemVO::setSLevelPromoteLiveRule,
                importObject.getSLevelPromoteLiveRule());
        setValueIfNotEmpty("采购员", viewItem,
                HandingSheetItemVO::setBuyer,
                importObject.getBuyer(),
                StaffAssembler.INST::toStaffBrief);
        setValueIfNotEmpty("采购成本", viewItem,
                HandingSheetItemVO::setCostPrice,
                importObject.getCostPrice(),
                this::parsePrice);
        setValueIfNotEmpty("发货类型", viewItem,
                HandingSheetItemVO::setShipmentType,
                importObject.getShipmentType(),
                v -> Objects.requireNonNull(IEnum.getEnumByDesc(ShipmentType.class, v),
                        "未定义的发货类型:" + v).getValue());
        setValueIfNotEmpty("备注", viewItem, HandingSheetItemVO::setRemark, importObject.getRemark());
    }

    private BigDecimal parsePrice(String val) {
        try {
            return new BigDecimal(val);
        } catch (Throwable ignored) {
        }
        return null;
    }


    private static void checkImportObjects(Platform mainPlatform, List<HandingSheetImportModel> objects) {
        for (int i = 0; i < objects.size(); i++) {
            final HandingSheetImportModel object = objects.get(i);
            object.setIndex(i + 1);
            try {
                if (StringUtil.isBlank(object.getOuterItemId(mainPlatform))) {
                    throw new IllegalArgumentException(String.format("盘货表导入商品异常，第%s行商品id（%s）为空", i,
                            mainPlatform.getDesc()));
                }
            } catch (Exception e) {
                throw new IllegalArgumentException("盘货表导入商品异常:" + e.getMessage(), e);
            }
        }
    }

    @Override
    public PageResponse<HandingSheetItemVO> handingSheetItemQuery(HandingSheetItemPageQuery query) {
        query.validate();
        final HandingSheetItemSpuMapper mapper = handingSheetItemSpuService.getDaddyBaseMapper();
        final IPage<HandingSheetItemSpuJoinSku> page = query.getPage();
        final MPJLambdaWrapper<HandingSheetItemSpuJoinSku> queryWrapper = MPJWrappers.lambdaJoin();
        final Long handingSheetId = query.getHandingSheetId();
        queryWrapper.innerJoin(HandingSheetItemSku.class,
                HandingSheetItemSku::getHandingSheetItemSpuId,
                HandingSheetItemSpu::getId);
        setSelectForHandingSheetItemSpuJoinSku(queryWrapper);
        queryWrapper.eq(Objects.nonNull(handingSheetId), HandingSheetItemSpu::getHandingSheetId, handingSheetId);
        queryWrapper.eq(Objects.nonNull(query.getIsPassed()),
                HandingSheetItemSku::getIsPassed,
                Boolean.TRUE.equals(query.getIsPassed()) ? 1 : 0);
        queryWrapper.eq(HandingSheetItemSku::getIsDel, 0);
        queryWrapper.in(CollectionUtil.isNotEmpty(query.getSkuCodes()),
                HandingSheetItemSku::getSkuCode,
                query.getSkuCodes());
        queryWrapper.orderByAsc(HandingSheetItemSpu::getId);
        queryWrapper.innerJoin(HandingSheet.class, HandingSheet::getId, HandingSheetItemSku::getHandingSheetId);
        queryWrapper.eq(HandingSheet::getIsDel, 0);
        if (!query.isHasAllDataPermission()) {
            if (!NumberUtil.isPositive(query.getCurrentUserId())
                    || NumberUtil.isPositive(query.getBuyerUserId()) && !Objects.equals(query.getBuyerUserId(),
                    query.getCurrentUserId())) {
                return ResponseFactory.emptyPage();
            }
            queryWrapper.eq(HandingSheetItemSku::getBuyerId, query.getBuyerUserId());
        } else if (query.getBuyerUserId() != null) {
            queryWrapper.eq(HandingSheetItemSku::getBuyerId, query.getBuyerUserId());
        }
        mapper.selectJoinPage(page, HandingSheetItemSpuJoinSku.class, queryWrapper);
        if (page.getRecords().isEmpty()) {
            return ResponseFactory.emptyPage();
        }

        final List<String> skuCodes = page.getRecords()
                .stream()
                .map(HandingSheetItemSpuJoinSku::getSkuCode)
                .collect(Collectors.toList());

        final Map<String, NewGoods> newGoodsMapBySkuCode = new HashMap<>();
        if (Boolean.TRUE.equals(query.getRefreshPrice())) {
            final List<NewGoods> newGoods = newGoodsService.selectBatchBySkuCodes(skuCodes);
            newGoodsMapBySkuCode.putAll(newGoods.stream()
                    .collect(Collectors.toMap(NewGoods::getSkuCode,
                            Function.identity(),
                            (a, b) -> a)));
        }

        return ResponseFactory.ofPage(page, spuJoinSku -> {
            final HandingSheetItemVO itemView = new HandingSheetItemVO();
            itemView.setHandingSheetId(spuJoinSku.getHandingSheetId());
            itemView.setHandingSheetSpuId(spuJoinSku.getHandingSheetSpuId());
            final HandingSheetItemSpuPersistData data = spuJoinSku.getSpuData();
            final List<HandingSheetItemPlatformItem> platformItems = data.getPlatformItems();
            itemView.setPlatformItems(platformItems);
            HandingSheetAssembler.INST.copyBizFields(itemView, data.getBizFields());
            itemView.setItemCode(spuJoinSku.getItemCode());
            itemView.setHandingSheetSkuId(spuJoinSku.getHandingSheetItemSkuId());
            final Long skuId = spuJoinSku.getSkuId();
            itemView.setSkuCode(spuJoinSku.getSkuCode());
            HandingSheetAssembler.INST.copyBizFields(itemView, spuJoinSku.getSkuData().getBizFields());
            itemView.setIsPassed(spuJoinSku.getIsPassed() != 0);
            final String skuCode = itemView.getSkuCode();
            if (Boolean.TRUE.equals(query.getRefreshPrice()) && newGoodsMapBySkuCode.containsKey(skuCode)) {
                final NewGoods newGoods = newGoodsMapBySkuCode.get(skuCode);
                refreshPriceFromNewGoods(itemView, newGoods);
            }
            return itemView;
        });
    }

    private static void setSelectForHandingSheetItemSpuJoinSku(MPJLambdaWrapper<HandingSheetItemSpuJoinSku> queryWrapper) {
        queryWrapper.selectAs(HandingSheetItemSpu::getId, HandingSheetItemSpuJoinSku::getHandingSheetSpuId);
        queryWrapper.selectAs(HandingSheetItemSpu::getHandingSheetId, HandingSheetItemSpuJoinSku::getHandingSheetId);
        queryWrapper.selectAs(HandingSheetItemSpu::getPlatform, HandingSheetItemSpuJoinSku::getPlatform);
        queryWrapper.selectAs(HandingSheetItemSpu::getOuterItemId, HandingSheetItemSpuJoinSku::getOuterItemId);
        queryWrapper.selectAs(HandingSheetItemSpu::getItemId, HandingSheetItemSpuJoinSku::getItemId);
        queryWrapper.selectAs(HandingSheetItemSpu::getItemCode, HandingSheetItemSpuJoinSku::getItemCode);
        queryWrapper.selectAs(HandingSheetItemSpu::getData, HandingSheetItemSpuJoinSku::getSpuData);
        queryWrapper.selectAs(HandingSheetItemSku::getId, HandingSheetItemSpuJoinSku::getHandingSheetItemSkuId);
        queryWrapper.selectAs(HandingSheetItemSku::getSkuCode, HandingSheetItemSpuJoinSku::getSkuCode);
        queryWrapper.selectAs(HandingSheetItemSku::getSkuId, HandingSheetItemSpuJoinSku::getSkuId);
        queryWrapper.selectAs(HandingSheetItemSku::getIsPassed, HandingSheetItemSpuJoinSku::getIsPassed);
        queryWrapper.selectAs(HandingSheetItemSku::getData, HandingSheetItemSpuJoinSku::getSkuData);
    }

    @Override
    public SingleResponse<Boolean> syncBackPriceToNewGoods(Long id) {
        final Long currentUserId = UserContext.getUserId();
        final HandingSheetItemPageQuery query = new HandingSheetItemPageQuery();
        query.setHandingSheetId(id);
        query.setHasAllDataPermission(true);

        final PageResponse<HandingSheetItemVO> response = handingSheetItemQuery(query);
        ErrorChecker.checkAndThrowIfError(response);

        final List<HandingSheetItemVO> data = response.getData();
        final List<String> skuCodes = data.stream().map(HandingSheetItemVO::getSkuCode).collect(Collectors.toList());

        final List<NewGoods> newGoodsList = newGoodsService.selectBatchBySkuCodes(skuCodes);
        final Map<String, NewGoods> newGoodsMap = newGoodsList.stream().collect(Collectors.toMap(NewGoods::getSkuCode,
                Function.identity(),
                (a, b) -> a));

        for (HandingSheetItemVO sheetItemVO : data) {
            final NewGoods newGoods = newGoodsMap.get(sheetItemVO.getSkuCode());
            if (Objects.nonNull(newGoods)) {
                final NewGoods newGoodsOriginal = NewGoodsAssembler.INST.copy(newGoods);
                refreshPriceToNewGoods(sheetItemVO, newGoods);
                newGoodsService.updateById(newGoods);
                final Diff diff = DiffUtil.diff(newGoodsOriginal, newGoods);
                final String diffLog = DiffUtil.getVerboseDiffLog(diff, NewGoods.class, "【盘货表价格同步】");
                operateLogDomainService.addOperatorLog(currentUserId,
                        OperateLogTarget.NEW_GOODS,
                        newGoods.getId(),
                        diffLog,
                        diff);
            }
        }
        return SingleResponse.of(true);
    }

    @Override
    @Transactional
    public Long saveOrEdit(HandingSheetParam param) {
        final Long currentUserId = UserContext.getUserId();

        // 先调用老服务的逻辑来更新盘货表的基础信息
        final Long handingSheetId = handingSheetService.saveOrEdit(param);

        // 更新新版盘货表商品明细表
        final HandingSheetSaveItemsCmd cmd = new HandingSheetSaveItemsCmd();
        cmd.setItems(param.getSheetItems());
        cmd.setCurrentUserId(currentUserId);
        cmd.setHandingSheetId(handingSheetId);
        cmd.setHasAllDataPermission(UserContext.hasPermission(GlobalConstant.HANDING_SHEET_ITEM_ALL));

        ((HandingSheetV2BizService) AopContext.currentProxy()).saveItems(cmd);

        // 编辑修改了盘货表信息,消息通知全体活动成员
        final Map<Long, DadStaffVO> staffMap = staffService.getStaffMap(param.getStaffUids());
        final HandingSheet handingSheet = handingSheetService.getById(handingSheetId);
        String handingSheetName = Objects.nonNull(handingSheet) ? handingSheet.getSheetName() : "盘货表ID：" + handingSheetId;

        staffMap.values().forEach(dadStaffVO -> {
            Map<String, Object> variables = new HashMap<>(8);
            variables.put("采购员", UserContext.getNickName());
            variables.put("活动成员", dadStaffVO.getQwUserId());
            variables.put("盘货表名称", handingSheetName);
            variables.put("商品数量", param.getSheetItems().size());
            variables.put("sheetId", handingSheetId);
            QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.HANDING_SHEET_UPDATE, variables);
        });

        return handingSheetId;
    }

    @Override
    @Transactional
    @DistributedLock(searchKey = DistributedLock.SearchKeyStrategy.PARAMETER_PROPERTY, waitTime = 0, leaseTime = 180)
    public void saveItems(HandingSheetSaveItemsCmd cmd) {
        final Long handingSheetId = cmd.getHandingSheetId();
        final Long currentUserId = cmd.getCurrentUserId();

        final HandingSheetItemPageQuery query = new HandingSheetItemPageQuery();
        query.setHandingSheetId(handingSheetId);
        query.setCurrentUserId(currentUserId);
        query.setHasAllDataPermission(cmd.isHasAllDataPermission());
        query.setPageSize(9999);

        final PageResponse<HandingSheetItemVO> response = handingSheetItemQuery(query);
        ErrorChecker.checkAndThrowIfError(response);

        final List<HandingSheetItemVO> itemsOriginal = response.getData();
        final Map<Long, HandingSheetItemVO> itemsOriginalMap = itemsOriginal.stream().collect(Collectors.toMap(
                HandingSheetItemVO::getHandingSheetSkuId,
                Function.identity()));

        final HashMap<Long, Long> frontIdToHandingSheetSpuIdMap = new HashMap<>();

        final List<HandingSheetItemVO> itemsAfterModify = cmd.getItems();
        final List<HandingSheetItemVO> itemsAfterModifyNotDeleted = itemsAfterModify.stream()
                .filter(item -> !item.isDeleted())
                .collect(
                        Collectors.toList());
        final HashSet<Long> alreadyUpdateSpuIds = new HashSet<>();

        if (!handingSheetProperties.isTolerateForDuplicateSku()) {

            final List<HandingSheetItemSpu> allHandingSheetItemSpus = handingSheetItemSpuService.selectByHandingSheetId(
                    handingSheetId);
            final Map<String, Long> outerItemIdToHandingSheetSpuIdMap = allHandingSheetItemSpus
                    .stream()
                    .filter(item -> StringUtil.isNotBlank(item.getOuterItemId()))
                    .collect(Collectors.toMap(
                            HandingSheetItemSpu::getOuterItemId,
                            HandingSheetItemSpu::getId));
            final HashSet<String> duplicateOuterItemIds = new HashSet<>();
            for (HandingSheetItemVO itemAfterModify : itemsAfterModifyNotDeleted) {
                if (outerItemIdToHandingSheetSpuIdMap.containsKey(
                        itemAfterModify.getOuterItemId())) {
                    if (!NumberUtil.isPositive(itemAfterModify.getHandingSheetSpuId())) {
                        duplicateOuterItemIds.add(itemAfterModify.getOuterItemId());
                    } else if (!Objects.equals(outerItemIdToHandingSheetSpuIdMap.get(itemAfterModify.getOuterItemId()),
                            itemAfterModify.getHandingSheetSpuId())) {
                        duplicateOuterItemIds.add(itemAfterModify.getOuterItemId());
                    }
                }
            }
            final List<String> duplicateOuterItemIdsNotBlank = duplicateOuterItemIds.stream()
                    .filter(StringUtil::isNotBlank)
                    .collect(Collectors.toList());
            if (!duplicateOuterItemIdsNotBlank.isEmpty()) {
                throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT,
                        "平台商品ID存在重复:" + String.join(",",
                                duplicateOuterItemIdsNotBlank));
            }
            final Map<Long, List<HandingSheetItemVO>> itemsGroupByHandingSpuId = itemsAfterModifyNotDeleted.stream()
                    .collect(
                            Collectors.groupingBy(
                                    HandingSheetItemVO::getHandingSheetSpuId));
            itemsGroupByHandingSpuId.forEach((handingSheetSpuId, items) -> {
                final Map<String, List<HandingSheetItemVO>> itemsGroupBySkuCode = items.stream()
                        .filter(v -> StringUtil.isNotBlank(
                                v.getSkuCode()))
                        .collect(Collectors.groupingBy(
                                HandingSheetItemVO::getSkuCode));
                final List<Map.Entry<String, List<HandingSheetItemVO>>> duplicateRecords = itemsGroupBySkuCode
                        .entrySet()
                        .stream().filter(v -> StringUtil.isNotBlank(v.getKey()) && v.getValue().size() > 1)
                        .collect(Collectors.toList());
                if (!duplicateRecords.isEmpty()) {
                    final String outerItemId = items.get(0).getOuterItemId();
                    final String duplicateSkuCodes = duplicateRecords.stream()
                            .map(Map.Entry::getKey)
                            .filter(StringUtil::isNotBlank)
                            .distinct()
                            .collect(Collectors.joining(","));
                    if (StringUtil.isNotEmpty(duplicateSkuCodes)) {
                        throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT,
                                String.format("SPU【%s】下的SKU编码重复: %s",
                                        outerItemId,
                                        duplicateSkuCodes));
                    }
                }
            });
        }

        for (HandingSheetItemVO itemAfterModify : itemsAfterModify) {
            Optional.ofNullable(frontIdToHandingSheetSpuIdMap.get(itemAfterModify.getHandingSheetSpuId()))
                    .ifPresent(itemAfterModify::setHandingSheetSpuId);

            Long handingSheetSpuId = itemAfterModify.getHandingSheetSpuId();
            Long handingSheetSkuId = itemAfterModify.getHandingSheetSkuId();

            boolean isCreateSpu = !isPositive(handingSheetSpuId);
            boolean isCreateSku = !isPositive(handingSheetSkuId);

            if (itemAfterModify.isDeleted()) {
                if (isPositive(handingSheetSkuId)) {
                    handingSheetItemSkuService.removeByIdWithTime(handingSheetSkuId);
                    operateLogDomainService.addOperatorLog(currentUserId,
                            OperateLogTarget.HANDING_SHEET_ITEM,
                            handingSheetSkuId,
                            "删除了盘货表商品",
                            itemAfterModify);
                }
            } else {
                final HandingSheetItemVO itemOriginal = itemsOriginalMap.get(handingSheetSkuId);

                if (isCreateSpu || !alreadyUpdateSpuIds.contains(handingSheetSpuId)) {
                    final HandingSheetItemSpu handingSheetItemSpu = new HandingSheetItemSpu();
                    Optional.ofNullable(handingSheetSpuId)
                            .filter(NumberUtil::isPositive)
                            .ifPresent(handingSheetItemSpu::setId);
                    handingSheetItemSpu.setHandingSheetId(handingSheetId);
                    handingSheetItemSpu.setPlatform(itemAfterModify.getPlatform());
                    handingSheetItemSpu.setOuterItemId(itemAfterModify.getOuterItemId());

                    final Optional<Item> itemOptional = Optional.ofNullable(itemService.getByMixedCode(itemAfterModify.getItemCode()));
                    handingSheetItemSpu.setItemCode(itemAfterModify.getItemCode());
                    handingSheetItemSpu.setItemId(itemOptional.map(Entity::getId).orElse(0L));

                    final HandingSheetItemSpuPersistData spuData = new HandingSheetItemSpuPersistData();
                    spuData.setPlatformItems(itemAfterModify.getPlatformItems());

                    final HandingSheetItemSpuBizFields spuBizFields = new HandingSheetItemSpuBizFields();
                    spuBizFields.setKeyProductOperationFeedback(itemAfterModify.getKeyProductOperationFeedback());

                    spuData.setBizFields(spuBizFields);

                    handingSheetItemSpu.setData(spuData);
                    handingSheetItemSpuService.saveOrUpdate(handingSheetItemSpu);

                    handingSheetSpuId = handingSheetItemSpu.getId();
                    alreadyUpdateSpuIds.add(handingSheetSpuId);

                    final HandingSheetItemSpuVO itemSpuAfterModify = HandingSheetAssembler.INST.handingSheetItemVOToHandingSheetItemSpuVO(
                            itemAfterModify);
                    itemSpuAfterModify.setPlatformItems(itemAfterModify.getPlatformItems());
                    if (isCreateSpu) {
                        frontIdToHandingSheetSpuIdMap.put(itemAfterModify.getHandingSheetSpuId(), handingSheetSpuId);
                        operateLogDomainService.addOperatorLog(currentUserId,
                                OperateLogTarget.HANDING_SHEET_ITEM_SPU,
                                handingSheetSpuId,
                                "新增盘货表商品",
                                itemSpuAfterModify);
                    } else {
                        final HandingSheetItemSpuVO itemSpuOriginal = HandingSheetAssembler.INST.handingSheetItemVOToHandingSheetItemSpuVO(
                                itemOriginal);
                        itemSpuOriginal.setPlatformItems(itemOriginal.getPlatformItems());

                        final Diff diff = DiffUtil.diff(itemSpuOriginal, itemSpuAfterModify);
                        if (diff.hasChanges()) {
                            final String diffLog = DiffUtil.getVerboseDiffLog(diff,
                                    HandingSheetItemSpuVO.class,
                                    Collections.emptyList(),
                                    Collections.emptyMap(),
                                    "");
                            operateLogDomainService.addOperatorLog(currentUserId,
                                    OperateLogTarget.HANDING_SHEET_ITEM_SPU,
                                    handingSheetSpuId,
                                    diffLog,
                                    diff);
                        }
                    }
                }

                final HandingSheetItemSku handingSheetItemSku = new HandingSheetItemSku();
                handingSheetItemSku.setId(handingSheetSkuId);
                handingSheetItemSku.setHandingSheetId(handingSheetId);
                handingSheetItemSku.setHandingSheetItemSpuId(handingSheetSpuId);

                final Optional<ItemSku> itemSkuOptional = itemSkuService.getByMixCode(itemAfterModify.getSkuCode());
                handingSheetItemSku.setSkuCode(itemAfterModify.getSkuCode());
                handingSheetItemSku.setSkuId(itemSkuOptional.map(ItemSku::getId).orElse(0L));

                final HandingSheetItemSkuPersistData handingSheetItemSkuPersistData = new HandingSheetItemSkuPersistData();
                final HandingSheetItemSkuBizFields handingSheetItemSkuBizFields = HandingSheetAssembler.INST.handingSheetItemVOToHandingSheetItemSkuBizFields(
                        itemAfterModify);
                handingSheetItemSkuPersistData.setBizFields(handingSheetItemSkuBizFields);
                handingSheetItemSku.setData(handingSheetItemSkuPersistData);
                handingSheetItemSkuService.saveOrUpdate(handingSheetItemSku);
                handingSheetSkuId = handingSheetItemSku.getId();

                if (isCreateSku) {
                    operateLogDomainService.addOperatorLog(currentUserId,
                            OperateLogTarget.HANDING_SHEET_ITEM,
                            handingSheetSkuId,
                            "新增盘货表商品",
                            itemAfterModify);
                } else {
                    final Diff diff = DiffUtil.diff(itemOriginal, itemAfterModify);
                    if (diff.hasChanges()) {
                        final String diffLog = DiffUtil.getVerboseDiffLog(diff,
                                HandingSheetItemVO.class,
                                Collections.emptyList(),
                                LOG_PRINTERS,
                                "");
                        operateLogDomainService.addOperatorLog(currentUserId,
                                OperateLogTarget.HANDING_SHEET_ITEM,
                                handingSheetSkuId,
                                diffLog,
                                diff);
                    }
                }
            }
        }

        final List<Long> invalidSpuIds = handingSheetItemSpuService.selectInvalidSpuIds(handingSheetId);
        if (!invalidSpuIds.isEmpty()) {
            handingSheetItemSpuService.removeByIdsWithTime(invalidSpuIds);
        }
    }

    @Override
    public void confirm(HandingSheetConfirmCmd cmd) {
        final List<Long> handingSheetItemSkuIds = cmd.getHandingSheetItemSkuIds();
        final List<HandingSheetItemSku> unConfirmedSkus = handingSheetItemSkuService.listByIds(handingSheetItemSkuIds)
                .stream()
                .filter(sku -> sku.getIsPassed() == 0)
                .collect(Collectors.toList());
        if (unConfirmedSkus.isEmpty()) {
            return;
        }

        final List<Long> handingSheetIds = unConfirmedSkus.stream()
                .map(HandingSheetItemSku::getHandingSheetId)
                .distinct().collect(Collectors.toList());
        if (handingSheetIds.size() > 1) {
            throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "批量确认的SKU需同属一张盘货表");
        }
        final Long handingSheetId = handingSheetIds.get(0);

        final Map<Long, List<HandingSheetItemSku>> unConfirmedSkusGroupBySpu = unConfirmedSkus.stream()
                .collect(Collectors.groupingBy(
                        HandingSheetItemSku::getHandingSheetItemSpuId));

        final Long currentUserId = UserContext.getUserId();
        unConfirmedSkusGroupBySpu.forEach((spuId, skus) -> {

            final ArrayList<HandingSheetItemSku> updateSkuModels = new ArrayList<>();
            final ArrayList<Long> updateSkuIds = new ArrayList<>();
            for (HandingSheetItemSku sku : skus) {
                final Long handingSheetItemSkuId = sku.getId();
                final HandingSheetItemSku updateSkuModel = new HandingSheetItemSku();
                updateSkuModel.setId(handingSheetItemSkuId);
                updateSkuModel.setIsPassed(1);
                updateSkuModels.add(updateSkuModel);
                updateSkuIds.add(handingSheetItemSkuId);
            }

            final boolean update = handingSheetItemSkuService.updateBatchById(updateSkuModels);
            if (update) {
                operateLogDomainService.addOperatorLog(currentUserId,
                        OperateLogTarget.HANDING_SHEET_ITEM_SPU,
                        updateSkuIds,
                        "确认");
            }
        });


        // 采购确认了盘货表信息,消息通知全体活动成员
        final List<Long> buyUidList = unConfirmedSkusGroupBySpu
                .values().stream().flatMap(List::stream)
                .collect(Collectors.toList()).stream().map(HandingSheetItemSku::getBuyerId).collect(Collectors.toList());
        final String buyerName = userGateway.batchQueryStaffInfoByIds(buyUidList)
                .values().stream().map(StaffInfo::getNickname).collect(Collectors.joining(StrUtil.COMMA));
        final List<Long> uidList = handingSheetStaffService.listBySheetId(handingSheetId)
                .stream().map(HandingSheetStaff::getUserId).collect(Collectors.toList());
        final Map<Long, DadStaffVO> staffMap = staffService.getStaffMap(uidList);
        final HandingSheet handingSheet = handingSheetService.getById(handingSheetId);
        String handingSheetName = Objects.nonNull(handingSheet) ? handingSheet.getSheetName() : "盘货表ID：" + handingSheetId;

        staffMap.values().forEach(dadStaffVO -> {
            Map<String, Object> variables = new HashMap<>(8);
            variables.put("采购员", buyerName);
            variables.put("活动成员", dadStaffVO.getQwUserId());
            variables.put("盘货表名称", handingSheetName);
            variables.put("商品数量", unConfirmedSkusGroupBySpu.size());
            variables.put("sheetId", handingSheetId);
            QyMsgSendUtils.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.HANDING_SHEET_CONFIRM, variables);
        });


        if (handingSheetItemSkuService.getToBeConfirmedItemNum(handingSheetId) == 0) {
            final HandingSheet handingSheetU = new HandingSheet();
            handingSheetU.setId(handingSheetId);
            handingSheetU.setState(3);

            handingSheetService.updateById(handingSheetU);
            operateLogDomainService.addOperatorLog(currentUserId,
                    OperateLogTarget.HANDING_SHEET,
                    handingSheetId,
                    "全部确认，盘货表状态变更为已过审");
        }

    }

    @Data
    private static class BringOutSelectMainItemResult {
        private Item item;
        private String itemCode;
    }

    private BringOutSelectMainItemResult bringOutSelectMainItem(HandingSheetItemBringOutCmd cmd) {
        final BringOutSelectMainItemResult bringOutSelectMainItemResult = new BringOutSelectMainItemResult();
        final Integer platform = cmd.getPlatform();
        final Platform platformEnum = IEnum.getEnumByValue(Platform.class, cmd.getPlatform());
        final String outerItemId = cmd.getOuterItemId();
        if (Objects.equals(platform, Platform.LAOBASHOP.getValue())) {
            final ItemListWithSkuVO itemListWithSkuVO = getMallItem(outerItemId);

            final String message = "盘货表选择商品，小程序商品查询异常";
            Assert.notNull(itemListWithSkuVO, message);
            Assert.notNull(itemListWithSkuVO.getItem(), message);

            final String itemNo = itemListWithSkuVO.getItem().getItemNo();
            bringOutSelectMainItemResult.setItemCode(itemNo);

            final Optional<Item> itemOptional = Optional.ofNullable(itemNo)
                    .filter(StringUtil::isNotEmpty)
                    .map(itemService::getByMixedCode);
            itemOptional.ifPresent(item -> {
                bringOutSelectMainItemResult.setItem(item);
                bringOutSelectMainItemResult.setItemCode(item.getSupplierCode());
            });
            return bringOutSelectMainItemResult;
        }

        final PlatformItemPageQuery mainPlatformItemQuery = new PlatformItemPageQuery();
        mainPlatformItemQuery.setPlatform(platformEnum);
        mainPlatformItemQuery.setOuterItemId(outerItemId);

        final PageResponse<PlatformItemListItem> mainPlatformItemQueryResponse = platformItemBizService.pageQuery(
                mainPlatformItemQuery);
        ErrorChecker.checkAndThrowIfError(mainPlatformItemQueryResponse);

        if (mainPlatformItemQueryResponse.getData().isEmpty()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND,
                    "盘货表选择商品，未找到匹配的平台商品数据");
        }

        final PlatformItemListItem platformItemListItem = mainPlatformItemQueryResponse.getData().get(0);
        final Optional<Item> itemOptional = Optional.ofNullable(itemService.getById(platformItemListItem.getItemId()));
        itemOptional.ifPresent(item -> {
            bringOutSelectMainItemResult.setItem(item);
            bringOutSelectMainItemResult.setItemCode(item.getSupplierCode());
        });
        return bringOutSelectMainItemResult;
    }

    private ItemListWithSkuVO getMallItem(String outerItemId) {
        return Objects.requireNonNull(mallItemsCache.get(outerItemId), "选择的小程序商品数据查询异常");
    }

    private final LoadingCache<String, ItemListWithSkuVO> mallItemsCache = Caffeine.newBuilder().expireAfterWrite(
            Duration.ofSeconds(60)).build(this::queryMallItem);

    private ItemListWithSkuVO queryMallItem(String outerItemId) {
        final ItemPageQuery itemPageQuery = new ItemPageQuery();
        itemPageQuery.setItemId(Long.parseLong(outerItemId));
        final PageResponse<ItemListWithSkuVO> itemListWithSkuVOPageResponse = arkSailorItemBizService.itemWithSkuPageQuery(
                itemPageQuery);
        ErrorChecker.checkAndThrowIfError(itemListWithSkuVOPageResponse);
        if (CollectionUtil.isEmpty(itemListWithSkuVOPageResponse.getData())) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "选择的小程序商品数据查询异常");
        }
        return itemListWithSkuVOPageResponse.getData().get(0);
    }

    @Override
    public MultiResponse<HandingSheetItemVO> bringOut(HandingSheetItemBringOutCmd cmd) {
        final Integer platform = cmd.getPlatform();
        final String outerItemId = cmd.getOuterItemId();
        final List<Integer> selectedPlatforms = cmd.getSelectedPlatforms();

        final HandingSheetItemVO commonSpuView = new HandingSheetItemVO();
        commonSpuView.setHandingSheetSpuId(generateTempId());

        final BringOutSelectMainItemResult bringOutSelectMainItemResult = bringOutSelectMainItem(cmd);
        final Long itemId = Optional.ofNullable(bringOutSelectMainItemResult.getItem()).map(Entity::getId).orElse(0L);

        final String itemMixCode = bringOutSelectMainItemResult.getItemCode();
        commonSpuView.setItemCode(itemMixCode);

        final List<PlatformItemListItem> platformItemList = getPlatformItemListForItem(itemId, selectedPlatforms);
        final List<HandingSheetItemPlatformItem> selectedSheetPlatformItemList = selectedSheetPlatformItemList(cmd,
                platformItemList);
        commonSpuView.setPlatformItems(selectedSheetPlatformItemList);

        commonSpuView.setKeyProductOperationFeedback("");

        final Set<String> relatedSkuCodes = new LinkedHashSet<>();
        final HashSetValuedHashMap<String, Platform> skuCodeToPlatformsMap = new HashSetValuedHashMap<>();

        if (Objects.equals(platform, Platform.LAOBASHOP.getValue())) {
            final ItemListWithSkuVO mallItem = getMallItem(outerItemId);
            for (ItemSkuVO skus : mallItem.getSkus()) {
                final String skuNo = skus.getSkuNo();
                if (StringUtil.isNotBlank(skuNo)) {
                    relatedSkuCodes.add(skuNo);
                    skuCodeToPlatformsMap.put(skuNo, Platform.LAOBASHOP);
                }
            }
        }

        final List<String> relatedOuterItemIds = getRelatedOuterItemIds(commonSpuView);
        final List<PlatformItemSku> relatedPlatformSkus = getRelatedPlatformSkus(relatedOuterItemIds);
        relatedPlatformSkus.forEach(platformSku -> {
            final String skuCode = platformSku.getOuterSkuCode();
            if (StringUtil.isNotBlank(skuCode)
                    && platformSku.getPlatform() != null
                    && selectedPlatforms.contains(platformSku.getPlatform().getValue())) {
                skuCodeToPlatformsMap.put(skuCode, platformSku.getPlatform());
                relatedSkuCodes.add(skuCode);
            }
        });

        final ArrayList<HandingSheetItemVO> handingSheetSkusViews = bringOutHandingSheetItemVOList(commonSpuView,
                relatedSkuCodes,
                skuCodeToPlatformsMap);
        return MultiResponse.of(handingSheetSkusViews);
    }

    private static Long generateTempId() {
        return -System.nanoTime();
    }

    @Override
    public SingleResponse<HandingSheetItemSkuBringOutVO> bringOutForSkuCode(String skuCode) {
        return bringOutForSkuCode(skuCode, Collections.emptyList());
    }

    @Override
    public SingleResponse<HandingSheetItemSkuBringOutVO> bringOutForSkuCode(String skuCode, List<Integer> platforms) {
        final HandingSheetItemSkuBringOutVO vo = new HandingSheetItemSkuBringOutVO();
        vo.setSkuId(0L);
        vo.setSkuCode(skuCode);
        vo.setSpecifications("");
        vo.setPlatforms(Lists.newArrayList());
        vo.setCategoryId(0L);
        vo.setCategoryPath("");

        final List<ItemSku> itemSkus = itemSkuService.selectByMixCodes(Collections.singleton(skuCode));
        if (!itemSkus.isEmpty()) {
            final ItemSku itemSku = itemSkus.get(0);

            final Item item = itemService.getById(itemSku.getItemId());
            Assert.notNull(item, "商品SKU数据异常，未找到关联后端商品");

            final List<PlatformItemListItem> platformItemListForItem = getPlatformItemListForItem(item.getId(),
                    platforms);
            final List<Integer> platformIds = platformItemListForItem.stream()
                    .map(PlatformItemListItem::getPlatform)
                    .map(Platform::getValue)
                    .distinct()
                    .collect(Collectors.toList());

            vo.setSkuId(itemSku.getId());
            vo.setSkuCode(itemSku.getSkuCode());
            vo.setSpecifications(itemSku.getSpecifications());
            vo.setPlatforms(platformIds);

            final Optional<Category> category = Optional.ofNullable(categoryService.getById(item.getCategoryId()));
            vo.setCategoryId(category.map(Category::getId).orElse(0L));
            vo.setCategoryPath(category.map(Category::getPath).orElse(""));
        }

        return SingleResponse.of(vo);
    }

    @NonNull
    private ArrayList<HandingSheetItemVO> bringOutHandingSheetItemVOList(HandingSheetItemVO commonSpuView,
                                                                         Set<String> relatedSkuCodes,
                                                                         HashSetValuedHashMap<String, Platform> skuCodeToPlatformsMap) {
        final ArrayList<HandingSheetItemVO> handingSheetSkusViews = new ArrayList<>();
        final List<ItemSku> itemSkus = itemSkuService.selectByMixCodes(relatedSkuCodes);
        final HashMap<String, ItemSku> codeToItemSkuMap = new HashMap<>();
        for (ItemSku sku : itemSkus) {
            codeToItemSkuMap.put(sku.getSkuCode(), sku);
            if (StringUtil.isNotBlank(sku.getProviderSpecifiedCode())) {
                codeToItemSkuMap.put(sku.getProviderSpecifiedCode(), sku);
            }
        }
        final List<Long> itemIds = itemSkus.stream().map(ItemSku::getItemId).distinct().collect(Collectors.toList());
        final List<ItemProcurement> itemProcurements = CollectionUtil.isNotEmpty(itemIds)
                ? itemProcurementService.lambdaQuery()
                .in(ItemProcurement::getItemId, itemIds)
                .list()
                : Collections.emptyList();
        final Map<Long, ItemProcurement> itemProcurementMap = itemProcurements.stream().collect(Collectors.toMap(
                ItemProcurement::getItemId,
                Function.identity()));
        final List<NewGoods> newGoodsList = CollectionUtil.isNotEmpty(itemIds) ? newGoodsService.lambdaQuery().in(
                NewGoods::getItemId,
                itemIds).list() : Collections.emptyList();
        final Map<String, NewGoods> newGoodsMap = newGoodsList.stream().collect(Collectors.toMap(NewGoods::getSkuCode,
                Function.identity()));
        relatedSkuCodes.stream().sorted().forEach(skuCode -> {
            final Optional<ItemSku> itemSku = Optional.ofNullable(codeToItemSkuMap.get(skuCode));
            final HandingSheetItemVO handingSheetItemSkuView = HandingSheetAssembler.INST.copy(commonSpuView);
            handingSheetItemSkuView.setHandingSheetSkuId(generateTempId());
            handingSheetItemSkuView.setSkuCode(itemSku.map(ItemSku::getSupplierCode).orElse(skuCode));
            handingSheetItemSkuView.setSpecifications(itemSku.map(ItemSku::getSpecifications).orElse(""));
            handingSheetItemSkuView.setPlatforms(skuCodeToPlatformsMap.get(skuCode)
                    .stream()
                    .map(Platform::getValue)
                    .collect(Collectors.toList()));
            final Optional<Category> category = itemSku.map(ItemSku::getCategoryId).map(categoryService::getById);
            handingSheetItemSkuView.setCategoryPath(category.map(Category::getPath).orElse(""));
            final Optional<ItemProcurement> itemProcurement = itemSku.map(ItemSku::getItemId)
                    .map(itemProcurementMap::get);
            handingSheetItemSkuView.setIsFreeShippingForSingleItem(null);
            handingSheetItemSkuView.setDailyPriceOrOriginalPrice(BigDecimal.ZERO);
            handingSheetItemSkuView.setDailyActivities("");
            handingSheetItemSkuView.setSLevelActivePrice(BigDecimal.ZERO);
            handingSheetItemSkuView.setSLevelActiveContent("");
            handingSheetItemSkuView.setSLevelPromoteLivePrice("");
            handingSheetItemSkuView.setSLevelPromoteLiveRule("");
            final Optional<StaffBrief> buyerUser = itemProcurement
                    .map(ItemProcurement::getBuyerId)
                    .flatMap(
                            buyerGateway::buyer)
                    .map(Buyer::getUserId)
                    .map(StaffAssembler.INST::toStaffBrief);
            handingSheetItemSkuView.setBuyer(buyerUser.orElse(null));
            handingSheetItemSkuView.setCostPrice(itemSku.map(ItemSku::getCostPrice).orElse(null));
            final NewGoods newGoods = newGoodsMap.get(skuCode);
            if (newGoods != null) {
                refreshPriceFromNewGoods(handingSheetItemSkuView, newGoods);
            } else {
                Integer shipmentType = convertShipmentType(itemProcurement
                        .map(ItemProcurement::getDelivery)
                        .orElse(""));
                handingSheetItemSkuView.setShipmentType(shipmentType);
            }
            handingSheetItemSkuView.setRemark("");

            handingSheetSkusViews.add(handingSheetItemSkuView);
        });

        if (handingSheetSkusViews.isEmpty()) {
            handingSheetSkusViews.add(commonSpuView);
        }
        return handingSheetSkusViews;
    }

    private void refreshPriceFromNewGoods(HandingSheetItemVO handingSheetItemSkuView, NewGoods newGoods) {
        handingSheetItemSkuView.setShipmentType(newGoods.getShipmentType());
        // 产品日销价
        handingSheetItemSkuView.setDailyPriceOrOriginalPrice(newGoods.getDailyPrice());
        handingSheetItemSkuView.setDailyActivities(newGoods.getDailyActivities());
        // 新品活动价
        handingSheetItemSkuView.setSLevelActivePrice(newGoods.getActivePrice());
        handingSheetItemSkuView.setSLevelActiveContent(newGoods.getActiveContent());
        final String sLevelPromoteLivePrice = newGoods.getSLevelPromoteLivePrice();
        final String liveActive = newGoods.getLiveActive();
        if (liveActive.equals(sLevelPromoteLivePrice)) {
            handingSheetItemSkuView.setSLevelPromoteLivePrice(sLevelPromoteLivePrice);
        } else {
            handingSheetItemSkuView.setSLevelPromoteLivePrice(liveActive + " " + sLevelPromoteLivePrice);
        }
        handingSheetItemSkuView.setSLevelPromoteLiveRule(newGoods.getSLevelPromoteLiveRule());
    }

    @TransactionalEventListener(fallbackExecution = true)
    @Transactional(rollbackFor = Exception.class)
    public void onNewGoodsSaveEvent(SaveEvent<NewGoods> saveEvent) {
        final NewGoods newGoods = saveEvent.getModel();
        log.info("[盘货表][监听新品商品保存]{}", newGoods);
        final String skuCode = newGoods.getSkuCode();
        final HandingSheetItemPageQuery query = new HandingSheetItemPageQuery();
        query.setHasAllDataPermission(true);
        query.setSkuCodes(Collections.singletonList(skuCode));

        final PageResponse<HandingSheetItemVO> response = handingSheetItemQuery(query);
        ResponseAssert.assertJust(response);

        final List<HandingSheetItemVO> handingSheetItems = response.getData();
        if (handingSheetItems.isEmpty()) {
            return;
        }

        final Long currentTime = DateUtil.currentTime();
        final Map<Long, List<HandingSheetItemVO>> handingSheetItemsGroups = handingSheetItems.stream()
                .collect(Collectors.groupingBy(
                        HandingSheetItemVO::getHandingSheetId));
        for (Map.Entry<Long, List<HandingSheetItemVO>> group : handingSheetItemsGroups.entrySet()) {
            final Long handingSheetId = group.getKey();
            final HandingSheet handingSheet = handingSheetService.getById(handingSheetId);
            if (handingSheet == null) {
                continue;
            }
            if (!isActive(currentTime, handingSheet)) {
                log.warn("[盘货表][监听新品商品保存][更新盘货表]盘货表不在活动时间内 handingSheetId={} skuCode={}",
                        handingSheetId,
                        skuCode);
                continue;
            }

            final List<HandingSheetItemVO> sheetItemVOS = group.getValue();
            for (HandingSheetItemVO handingSheetItem : sheetItemVOS) {
                refreshPriceFromNewGoods(handingSheetItem, newGoods);
            }
            final HandingSheetSaveItemsCmd cmd = new HandingSheetSaveItemsCmd();
            cmd.setItems(sheetItemVOS);
            cmd.setHandingSheetId(handingSheetId);
            cmd.setCurrentUserId(saveEvent.getOperatorId());
            cmd.setHasAllDataPermission(true);

            saveItems(cmd);
            log.info("[盘货表][监听新品商品保存][更新盘货表]{}", cmd);
        }
    }

    private static boolean isActive(Long currentTime, HandingSheet handingSheet) {
        return currentTime > handingSheet.getStartTime() && currentTime < handingSheet.getEndTime();
    }

    private void refreshPriceToNewGoods(HandingSheetItemVO handingSheetItemSkuView, NewGoods newGoods) {
        newGoods.setShipmentType(handingSheetItemSkuView.getShipmentType());
        newGoods.setDailyPrice(handingSheetItemSkuView.getDailyPriceOrOriginalPrice());
        newGoods.setDailyActivities(handingSheetItemSkuView.getDailyActivities());
        newGoods.setActivePrice(handingSheetItemSkuView.getSLevelActivePrice());
        newGoods.setActiveContent(handingSheetItemSkuView.getSLevelActiveContent());
        newGoods.setSLevelPromoteLivePrice(handingSheetItemSkuView.getSLevelPromoteLivePrice());
        newGoods.setSLevelPromoteLiveRule(handingSheetItemSkuView.getSLevelPromoteLiveRule());
    }

    @NonNull
    private List<PlatformItemSku> getRelatedPlatformSkus(List<String> relatedOuterItemIds) {
        return Optional.of(relatedOuterItemIds)
                .filter(CollectionUtil::isNotEmpty)
                .map(v -> platformItemSkuService.lambdaQuery()
                        .in(PlatformItemSku::getOuterItemId, relatedOuterItemIds)
                        .list())
                .orElseGet(Collections::emptyList);
    }

    @NonNull
    private static List<String> getRelatedOuterItemIds(HandingSheetItemVO view) {
        return view.getPlatformItems()
                .stream()
                .map(HandingSheetItemPlatformItem::getOuterItemId)
                .filter(StringUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    @NonNull
    private List<HandingSheetItemPlatformItem> selectedSheetPlatformItemList(HandingSheetItemBringOutCmd cmd,
                                                                             List<PlatformItemListItem> platformItemList) {
        final Map<Integer, PlatformItemListItem> platformToPlatformItemMap = platformItemList.stream().collect(
                Collectors.toMap(v -> v.getPlatform().getValue(), Function.identity(), (a, b) -> a));
        final List<Integer> selectedPlatforms = cmd.getSelectedPlatforms();
        final ArrayList<HandingSheetItemPlatformItem> handingSheetItemPlatformItems = new ArrayList<>();
        for (int i = 0; i < selectedPlatforms.size(); i++) {
            final Integer platformVal = selectedPlatforms.get(i);
            final Platform platform = IEnum.getEnumByValue(Platform.class, platformVal);
            final PlatformItemListItem platformItemListItem = platformToPlatformItemMap.get(platformVal);
            final HandingSheetItemPlatformItem handingSheetItemPlatformItem = new HandingSheetItemPlatformItem();
            handingSheetItemPlatformItem.setPlatform(platformVal);
            handingSheetItemPlatformItem.setOuterItemId("");
            handingSheetItemPlatformItem.setLink("");

            if (platformItemListItem != null) {
                handingSheetItemPlatformItem.setOuterItemId(platformItemListItem.getOuterItemId());
                handingSheetItemPlatformItem.setGoodsName(platformItemListItem.getGoodsName());
                handingSheetItemPlatformItem.setLink(itemOutlinkProperties.getLink(platformItemListItem.getPlatform(),
                        platformItemListItem.getOuterItemId()));
            }
            if (i == 0) {
                final String outerItemId = cmd.getOuterItemId();
                handingSheetItemPlatformItem.setOuterItemId(outerItemId);
                handingSheetItemPlatformItem.setLink(itemOutlinkProperties.getLink(platform, outerItemId));
                if (Objects.equals(platformVal, Platform.LAOBASHOP.getValue())) {
                    final ItemListWithSkuVO mallItem = getMallItem(outerItemId);
                    handingSheetItemPlatformItem.setGoodsName(mallItem.getItem().getName());
                }
            }
            handingSheetItemPlatformItems.add(handingSheetItemPlatformItem);
        }
        return handingSheetItemPlatformItems;
    }

    private List<PlatformItemListItem> getPlatformItemListForItem(Long itemId, List<Integer> selectedPlatforms) {
        if (Objects.isNull(itemId) || itemId == 0) {
            return Collections.emptyList();
        }
        final PlatformItemPageQuery otherPlatformItemQuery = new PlatformItemPageQuery();
        otherPlatformItemQuery.setItemId(itemId);

        if (CollectionUtil.isNotEmpty(selectedPlatforms)) {
            otherPlatformItemQuery.setPlatforms(toPlatformEnums(selectedPlatforms));
        }

        final PageResponse<PlatformItemListItem> otherPlatformItemQueryResponse = platformItemBizService.pageQuery(
                otherPlatformItemQuery);
        ErrorChecker.checkAndThrowIfError(otherPlatformItemQueryResponse);
        return otherPlatformItemQueryResponse.getData();
    }

    @NonNull
    private static List<Platform> toPlatformEnums(List<Integer> selectedPlatforms) {
        return selectedPlatforms.stream().map(Platform::of).collect(Collectors.toList());
    }

    private static Integer convertShipmentType(String delivery) {
        Integer shipmentType = null;
        if (delivery.contains("1")) {
            shipmentType = 1;
        } else if (delivery.contains("0")) {
            shipmentType = 2;
        }
        return shipmentType;
    }

    @Override
    public void copyInfo(Long handingSheetId, Long sourceId, List<Long> targetIds) {
        final Long currentUserId = UserContext.getUserId();
        final HashSet<Long> allIds = new HashSet<>(targetIds);
        allIds.add(sourceId);
        final List<HandingSheetItemSku> handingSheetItemSkus = handingSheetItemSkuService.listByIds(allIds);
        final Map<Long, HandingSheetItemSku> handingSheetItemSkusMap = handingSheetItemSkus.stream()
                .collect(Collectors.toMap(
                        HandingSheetItemSku::getId,
                        Function.identity()));
        final HandingSheetItemSku sourceSku = handingSheetItemSkusMap.get(sourceId);
        Assert.notNull(sourceSku, "盘货表，复制信息失败，选中商品查询异常");
        final HandingSheetItemSkuPersistData sourceData = sourceSku.getData();
        final HandingSheetItemSkuBizFields sourceDataBizFields = sourceData.getBizFields();
        for (Long targetId : targetIds) {
            final HandingSheetItemSku targetSku = handingSheetItemSkusMap.get(targetId);
            final HandingSheetItemSkuPersistData targetSkuData = targetSku.getData();
            final HandingSheetItemSkuBizFields targetSkuBizFields = targetSkuData.getBizFields();
            targetSku.setData(sourceData);
            final Diff diff = DiffUtil.diff(sourceDataBizFields, targetSkuBizFields);
            final String diffLog = DiffUtil.getVerboseDiffLog(diff, HandingSheetItemSkuBizFields.class, "【信息复制】");
            handingSheetItemSkuService.updateById(targetSku);
            operateLogDomainService.addOperatorLog(currentUserId,
                    OperateLogTarget.HANDING_SHEET_ITEM,
                    targetId,
                    Arrays.asList(String.format("信息复制 %s 同步给 %s",
                            sourceSku.getSkuCode(),
                            targetSku.getSkuCode()), diffLog),
                    Collections.singletonList(diff));
        }
    }

    @Override
    public MultiResponse<HandingSheetItemVO> refreshItemView(List<HandingSheetItemVO> views) {
        if (CollectionUtil.isEmpty(views)) {
            return MultiResponse.of(views);
        }
        final Set<String> relatedSkuCodes = new LinkedHashSet<>();
        final HashSetValuedHashMap<String, Platform> skuCodeToPlatformsMap = new HashSetValuedHashMap<>();

        final HandingSheetItemVO commonSpuView = views.get(0);
        final List<String> relatedOuterItemIds = getRelatedOuterItemIds(commonSpuView);
        final List<PlatformItemSku> relatedPlatformSkus = getRelatedPlatformSkus(relatedOuterItemIds);
        relatedPlatformSkus.forEach(platformSku -> {
            final String skuCode = platformSku.getOuterSkuCode();
            skuCodeToPlatformsMap.put(skuCode, platformSku.getPlatform());
            relatedSkuCodes.add(skuCode);
        });

        final ArrayList<HandingSheetItemVO> newViews = bringOutHandingSheetItemVOList(commonSpuView,
                relatedSkuCodes,
                skuCodeToPlatformsMap);

        final List<String> skuCodesOriginal = views.stream().map(HandingSheetItemVO::getSkuCode).distinct().collect(
                Collectors.toList());
        final List<String> skuCodesNewView = newViews.stream().map(HandingSheetItemVO::getSkuCode).distinct().collect(
                Collectors.toList());
        final Collection<String> needRemovedSkuCodes = CollectionUtil.subtract(skuCodesOriginal, skuCodesNewView);
        final Collection<String> needAddedSkuCodes = CollectionUtil.subtract(skuCodesNewView, skuCodesOriginal);
        for (HandingSheetItemVO view : views) {
            if (needRemovedSkuCodes.contains(view.getSkuCode()) && !ObjectUtil.isDirty(view,
                    Arrays.asList(
                            "specifications",
                            "platforms",
                            "categoryPath",
                            "shipmentType",
                            "isFreeShippingForSingleItem",
                            "dailyPriceOrOriginalPrice",
                            "dailyActivities",
                            "sLevelActivePrice",
                            "sLevelActiveContent",
                            "sLevelPromoteLivePrice",
                            "sLevelPromoteLiveRule",
                            "costPrice",
                            "remark"))) {
                view.setDeleted(true);
            }
        }
        for (HandingSheetItemVO newView : newViews) {
            if (needAddedSkuCodes.contains(newView.getSkuCode())) {
                views.add(newView);
            }
        }
        return MultiResponse.of(views);

    }

    @Override
    public MultiResponse<OperateLog> operateLogs(Long targetId, Integer type) {
        if (type == 2) {
            return MultiResponse.of(operateLogDomainService.getMultiTargetOperateLogs(Arrays.asList(
                    new TypedTargetId(
                            OperateLogTarget.HANDING_SHEET_ITEM,
                            targetId),
                    new TypedTargetId(
                            OperateLogTarget.HANDING_SHEET_ITEM_SPU,
                            handingSheetItemSkuService.getHandingSheetItemSpuId(
                                            targetId)
                                    .orElse(0L))
            )));
        }
        return MultiResponse.of(operateLogDomainService.getOperateLogs(OperateLogTarget.HANDING_SHEET, targetId));
    }

    @Override
    public void exportItemInfoExcel(HandingSheetItemPageQuery queryPage) {
        ExportTask exportTask = new ExportTask();
        exportTask.setStatus(ExportTaskStatus.RUNNING);
        exportTask.setName("盘货表关联商品导出-" + DateUtil.formatNow(DatePattern.PURE_DATETIME_PATTERN));
        exportTask.setType(ExportTaskType.HANDING_SHEET_ITEMS);
        final Long taskId = exportTaskGateway.saveOrUpdateExportTask(exportTask);
        exportTask.setId(taskId);

        queryPage.setCurrentUserId(UserContext.getUserId());
        queryPage.setHasAllDataPermission(UserContext.hasPermission(GlobalConstant.HANDING_SHEET_ITEM_ALL));

        ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
            try (final ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
                queryPage.setPageSize(999999);

                exportItems(queryPage, byteArrayOutputStream);

                // 上传文件
                UploadFileAction action = UploadFileAction.ofInputStream(new ByteArrayInputStream(byteArrayOutputStream.toByteArray()),
                        exportTask.getName() + ".xlsx");
                final String downloadUrl = fileGateway.uploadFile(action).getUrl();
                log.info("盘货表商品导出成功，下载链接：{}", downloadUrl);
                // 更新导出任务
                exportTask.setStatus(ExportTaskStatus.SUCCESS);
                exportTask.setDownloadUrl(downloadUrl);
            } catch (Exception e) {
                log.error("盘货表关联商品导出异常", e);
                exportTask.setError(e.getMessage());
                exportTask.setStatus(ExportTaskStatus.FAIL);
                exportTask.setError(e.getMessage());
            } finally {
                exportTaskGateway.saveOrUpdateExportTask(exportTask);
            }
        });
    }

    @Override
    public void toBeConfirmNoticeSummary() {
        final List<HandingSheet> list = handingSheetService.lambdaQuery()
                .eq(HandingSheet::getState,
                        HandingSheetStateEnum.TO_BE_AUDITED.getValue())
                .list();
        for (HandingSheet handingSheet : list) {
            final StaffBrief creatorUser = StaffAssembler.INST.toStaffBrief(handingSheet.getCreatedUid());
            final String submitAtFormatted = Optional.ofNullable(handingSheet.getSubmitAt())
                    .map(TimeTransMapper.INSTANCE::timestampToFormattedString)
                    .orElse("");

            final List<HandingSheetItemSku> handingSheetItemSkus = handingSheetItemSkuService.lambdaQuery()
                    .eq(HandingSheetItemSku::getHandingSheetId,
                            handingSheet.getId())
                    .list();

            final Map<StaffBrief, List<HandingSheetItemSku>> sheetItemsGroupByBuyer = handingSheetItemSkus
                    .stream()
                    .filter(item -> Objects.nonNull(getBuyerFromSheetItemSku(item)))
                    .collect(Collectors.groupingBy(this::getBuyerFromSheetItemSku));

            for (Map.Entry<StaffBrief, List<HandingSheetItemSku>> entry : sheetItemsGroupByBuyer.entrySet()) {
                final StaffBrief buyer = entry.getKey();
                if (Objects.isNull(buyer)) {
                    continue;
                }
                final List<HandingSheetItemSku> skus = entry.getValue();
                long itemCount = skus.size();

                final StaffBrief staffBrief = StaffAssembler.INST.toStaffBrief(buyer.getUserId());
                if (Objects.isNull(staffBrief)) {
                    continue;
                }
                String qwUserId = staffBrief.getQwUserId();
                if (!StringUtils.hasText(qwUserId)) {
                    log.warn("【盘货表审核汇总提醒】盘货表 {} 用户 {} 未找到企微用户ID，无法发送消息",
                            handingSheet.getId(),
                            staffBrief);
                    continue;
                }

                try {
                    final WechatMsg wechatMsg = new WechatMsg();
                    wechatMsg.setTitle(String.format("%s %s 提交的 %s 盘货表中商品待您审核",
                            creatorUser.getNickname(),
                            submitAtFormatted,
                            handingSheet.getSheetName()));
                    wechatMsg.setContent(String.format("共 %s 个商品待您确认！", itemCount));
                    wechatMsg.setLink(refreshConfig.getDomain() +
                            String.format("/operation-management/inventory/edit?id=%s&state=-1",
                                    handingSheet.getId()));

                    wechatMsg.setRecipient(qwUserId);
                    wechatMsg.setType(1);
                    msgSender.send(wechatMsg);

                    log.info("【盘货表审核汇总提醒】盘货表 {} 用户 {} 发送成功 商品数量={}",
                            handingSheet.getId(),
                            staffBrief, itemCount);
                } catch (Exception e) {
                    log.error("【盘货表审核汇总提醒】盘货表 {} 用户 {} 发送异常 商品数量={}",
                            handingSheet.getId(),
                            staffBrief, itemCount, e);
                }
            }
        }
    }

    private StaffBrief getBuyerFromSheetItemSku(HandingSheetItemSku item) {
        return Optional.ofNullable(item.getData())
                .map(HandingSheetItemSkuPersistData::getBizFields)
                .map(HandingSheetItemSkuBizFields::getBuyer).orElse(null);
    }
}
