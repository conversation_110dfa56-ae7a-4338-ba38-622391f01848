package com.daddylab.supplier.item.infrastructure.gatewayimpl.nuonuo;

import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年07月31日 1:57 PM
 */
@Data
@Slf4j
public class NuoNuoInvoiceResultDto {

    /**
     * 诺诺 数电发票号
     */
    private String electronicInvoiceNumber;

    private List<NuoNuoInvoiceItemDto> invoiceItemList;

    public static NuoNuoInvoiceResultDto of(String json) {
        json = json.replace("\\\"", "\"");
        ObjectMapper objectMapper = new ObjectMapper();
        NuoNuoInvoiceResultDto nuoNuoInvoiceResultDto = new NuoNuoInvoiceResultDto();
        try {
            JsonNode rootNode = objectMapper.readTree(json);
            JsonNode resultNode = rootNode.path("result").get(0);

            String allElectronicInvoiceNumber = resultNode.path("allElectronicInvoiceNumber").asText();
            nuoNuoInvoiceResultDto.setElectronicInvoiceNumber(allElectronicInvoiceNumber);

            JsonNode invoiceItemsNode = resultNode.path("invoiceItems");
            Iterator<JsonNode> elements = invoiceItemsNode.elements();
            List<NuoNuoInvoiceItemDto> itemDtoList = new LinkedList<>();
            while (elements.hasNext()) {
                JsonNode itemNode = elements.next();

                NuoNuoInvoiceItemDto nuoNuoInvoiceItemDto = new NuoNuoInvoiceItemDto();
                nuoNuoInvoiceItemDto.setBlueDetailIndex(itemNode.path("itemIndex").asInt());
                nuoNuoInvoiceItemDto.setGoodsName(itemNode.path("itemName").asText());
                nuoNuoInvoiceItemDto.setNum(itemNode.path("itemNum").asText());
                nuoNuoInvoiceItemDto.setTaxRate(itemNode.path("itemTaxRate").asText());
                nuoNuoInvoiceItemDto.setItemPrice(itemNode.path("itemPrice").asText());
                nuoNuoInvoiceItemDto.setGoodsCode(itemNode.path("itemCode").asText());
                nuoNuoInvoiceItemDto.setIsIncludeTax(itemNode.path("isIncludeTax").asBoolean());
                nuoNuoInvoiceItemDto.setItemUnit(itemNode.path("itemUnit").asText());
                nuoNuoInvoiceItemDto.setItemSelfCode(itemNode.path("itemSelfCode").asText());
                itemDtoList.add(nuoNuoInvoiceItemDto);
            }
            nuoNuoInvoiceResultDto.setInvoiceItemList(itemDtoList);

        } catch (IOException e) {
            log.error("解析开票详情出数据异常",e);
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "解析开票详情出数据异常");
        }

        return nuoNuoInvoiceResultDto;
    }

    public static void main(String[] args) {
        String json = "{\\\"code\\\":\\\"E0000\\\",\\\"describe\\\":\\\"获取成功\\\",\\\"result\\\":[{\\\"additionalElementList\\\":[],\\\"additionalElementName\\\":\\\"\\\",\\\"address\\\":\\\"\\\",\\\"allElectronicInvoiceNumber\\\":\\\"20882407311002040785\\\",\\\"bankAccount\\\":\\\"\\\",\\\"buyerManagerName\\\":\\\"\\\",\\\"checker\\\":\\\"李四**********\\\",\\\"clerk\\\":\\\"旺仔\\\",\\\"clerkId\\\":\\\"\\\",\\\"createTime\\\":*************,\\\"deptId\\\":\\\"\\\",\\\"digitAccount\\\":\\\"***********\\\",\\\"emailNotifyStatus\\\":\\\"4\\\",\\\"extensionNumber\\\":\\\"923\\\",\\\"imgUrls\\\":\\\"https://inv.jss.com.cn/fp2/iav4gjwPJF4ynfSyd8Y9MP7vTdUHmBAY-kk7zUvixtJRTt_8G0LoILLTHgd3gBLH1zMfm43aVlAOCsbfBzIMLw.jpg\\\",\\\"invoiceDate\\\":*************,\\\"invoiceType\\\":\\\"1\\\",\\\"listFlag\\\":\\\"0\\\",\\\"listName\\\":\\\"\\\",\\\"managerCardNo\\\":\\\"\\\",\\\"managerCardType\\\":\\\"\\\",\\\"naturalPersonFlag\\\":0,\\\"notifyEmail\\\":\\\"<EMAIL>\\\",\\\"ofdUrl\\\":\\\"https://inv.jss.com.cn/fp2/iav4gjwPJF4ynfSyd8Y9MC_cCL0XM4SO15IVDwFcyPHqKlfSNpLr-CzSX2xQJ63K7bzAvKWqXbyU5vW12FtJiA.ofd\\\",\\\"oldInvoiceCode\\\":\\\"\\\",\\\"oldInvoiceNo\\\":\\\"\\\",\\\"orderAmount\\\":\\\"100.00\\\",\\\"payee\\\":\\\"张三\\\",\\\"phone\\\":\\\"\\\",\\\"phoneNotifyStatus\\\":\\\"4\\\",\\\"productOilFlag\\\":0,\\\"proxyInvoiceFlag\\\":\\\"0\\\",\\\"redReason\\\":\\\"\\\",\\\"remark\\\":\\\"开具红字增值税专用发票信息表编号ehyk1GNvZNJXX91B\\\",\\\"requestSrc\\\":\\\"0\\\",\\\"saleName\\\":\\\"航信培训企业199\\\",\\\"salerAccount\\\":\\\"浙江桐庐农村商业银行股份有限公司城南支行***************\\\",\\\"salerAddress\\\":\\\"杭州市上城区\\\",\\\"salerTaxNum\\\":\\\"***************\\\",\\\"salerTel\\\":\\\"0571-********\\\",\\\"specificFactor\\\":0,\\\"stateUpdateTime\\\":*************,\\\"telephone\\\":\\\"\\\",\\\"terminalNumber\\\":\\\"\\\",\\\"updateTime\\\":*************,\\\"serialNo\\\":\\\"24073110020403101967\\\",\\\"orderNo\\\":\\\"ORD003\\\",\\\"status\\\":\\\"2\\\",\\\"statusMsg\\\":\\\"开票完成（最终状态）\\\",\\\"failCause\\\":\\\"\\\",\\\"pdfUrl\\\":\\\"https://inv.jss.com.cn/fp2/iav4gjwPJF4ynfSyd8Y9MErojV6-1-wuC9lEaS-YcvtQIDalncjlitljOspVAcHWZ5Cou9fXZ40Xqej4wo1bmA.pdf\\\",\\\"pictureUrl\\\":\\\"nnfp.jss.com.cn/9Nmkib63C2-195qM\\\",\\\"invoiceTime\\\":*************,\\\"invoiceCode\\\":\\\"\\\",\\\"invoiceNo\\\":\\\"20882407311002040785\\\",\\\"exTaxAmount\\\":\\\"88.50\\\",\\\"taxAmount\\\":\\\"11.50\\\",\\\"payerName\\\":\\\"杭州老爸电商科技有限公司\\\",\\\"payerTaxNo\\\":\\\"339901999999198\\\",\\\"invoiceKind\\\":\\\"电子发票(普通发票)\\\",\\\"checkCode\\\":\\\"\\\",\\\"qrCode\\\":\\\"01,32,,20882407311002040785,100.00,20240731,,D682\\\",\\\"machineCode\\\":\\\"\\\",\\\"cipherText\\\":\\\"\\\",\\\"invoiceItems\\\":[{\\\"dField1\\\":\\\"dField1\\\",\\\"dField2\\\":\\\"dField2\\\",\\\"dField3\\\":\\\"dField3\\\",\\\"dField4\\\":\\\"dField4\\\",\\\"dField5\\\":\\\"dField5\\\",\\\"deduction\\\":\\\"0.00\\\",\\\"immediateTaxReturnType\\\":\\\"\\\",\\\"itemCodeAbb\\\":\\\"计算机外部设备\\\",\\\"itemIndex\\\":1,\\\"itemSelfCode\\\":\\\"1090511030000000000\\\",\\\"itemName\\\":\\\"Goods3\\\",\\\"itemUnit\\\":\\\"PCS\\\",\\\"itemPrice\\\":\\\"100.000000000000000000\\\",\\\"itemTaxRate\\\":\\\"0.13\\\",\\\"itemNum\\\":\\\"1.000000000000000000\\\",\\\"itemAmount\\\":\\\"100.00\\\",\\\"itemTaxAmount\\\":\\\"11.50\\\",\\\"itemSpec\\\":\\\"\\\",\\\"itemCode\\\":\\\"1090511030000000000\\\",\\\"isIncludeTax\\\":\\\"true\\\",\\\"invoiceLineProperty\\\":\\\"0\\\",\\\"zeroRateFlag\\\":\\\"\\\",\\\"favouredPolicyName\\\":\\\"\\\"}]}]}\n";

        final NuoNuoInvoiceResultDto of = NuoNuoInvoiceResultDto.of(json);
        System.out.println(JsonUtil.toJson(of));

    }
}
