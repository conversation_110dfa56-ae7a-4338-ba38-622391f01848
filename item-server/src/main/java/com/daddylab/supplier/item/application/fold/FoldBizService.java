package com.daddylab.supplier.item.application.fold;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.fold.dto.FoldCmd;
import com.daddylab.supplier.item.application.fold.dto.FoldVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Fold;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName FoldBizService.java
 * @description
 * @createTime 2022年06月07日 15:21:00
 */
public interface FoldBizService {

    /**
     * 新增修改折叠
     * @param cmds
     * @return
     */
    Response saveOrUpdate(List<FoldCmd> cmds);


    /**
     * 查询折叠字段
     * @param type
     * @return
     */
    MultiResponse<FoldVo> getFold(Integer type);


    /**
     * 查询折叠字段
     * @param type
     * @return
     */
    List<Fold> getFoldByType(Integer type);
}
