package com.daddylab.supplier.item.application.message.wechat;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.application.message.wechat.v2.statics.StaticsMsgBizService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWechatMsgService;
import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 定时发送企微消息。
 * 1人1分钟限流50条企微消息。
 *
 * <AUTHOR> up
 * @date 2022/6/4 3:26 下午
 */
@Slf4j
@Service
public class SendWeChatMsgJob {

    @Autowired
    IWechatMsgService iWechatMsgService;

    @Autowired
    MsgSender msgSender;

    @Autowired
    RocketMQProducer rocketMqProducer;

    @Resource
    StaticsMsgBizService staticsMsgBizService;

    @XxlJob("sendStaticsWeChatMsgJob")
    public void run() {
        staticsMsgBizService.handle();
//        ThreadUtil.execute(PoolEnum.JOB_POOL, () -> {
//            // 获取参数  TEN / FOURTEEN
//            String params = XxlJobHelper.getJobParam();
////            String params = "ONE_DAY";
//            String[] methodParams = params.split(",");
//            String methodParam = methodParams[0];
//            Assert.notNull(methodParam, "执行参数不得为空");
//            Assert.state("TEN".equals(methodParam) || "FOURTEEN".equals(methodParam) || "ONE_DAY".equals(methodParam), "执行参数非法");
//
//            // 计算时间
//            LocalDateTime nowTime = LocalDateTime.now();
//            long startDt;
//            long endDt;
//            if ("TEN".equals(methodParam)) {
//                startDt = DateUtil.toEpochSecond(nowTime.plusHours(-20));
//                endDt = DateUtil.toTime(nowTime);
//            }
//            // 时间点是14点，那么更新时间范围是当前时间往前推4个小时。
//            else if ("FOURTEEN".equals(methodParam)) {
//                startDt = DateUtil.toEpochSecond(nowTime.plusHours(-4));
//                endDt = DateUtil.toTime(nowTime);
//            } else {
//                LocalDateTime start = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
//                startDt = DateUtil.toEpochSecond(start);
//                LocalDateTime end = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
//                endDt = DateUtil.toEpochSecond(end);
//            }
//
//            // 需要发送企微消息的信息列表
//            // 限频，1人/1分钟/50条信息能保证收到，实测可能收到51~57不等，并且接口不会报错。这边取整。
//            List<WechatMsg> list = getList(startDt, endDt);
//            Map<String, List<WechatMsg>> recipientMap = list.stream().collect(Collectors.groupingBy(WechatMsg::getRecipient));
//
//            recipientMap.forEach((recipient, msgList) -> {
//                int size = msgList.size();
//                if (size <= 50) {
//                    msgSender.sendByList(msgList);
//                } else {
//                    List<WechatMsg> wechatMsgList = msgList.subList(0, 50);
//                    msgSender.sendByList(wechatMsgList);
//
//                    List<WechatMsg> wechatMsgList1 = msgList.subList(50, size);
//                    List<Long> idList = wechatMsgList1.stream().map(WechatMsg::getId).collect(Collectors.toList());
//                    rocketMqProducer.asyncSend(idList, MQTopic.WECHAT_DELAY_MSG, "msg_id", IdUtil.fastSimpleUUID(), 5);
//                }
//            });
//        });
    }

//    private List<WechatMsg> getList(Long start, Long end) {
//            return iWechatMsgService.lambdaQuery().between(WechatMsg::getCreatedAt, start, end)
//                    .in(WechatMsg::getType, ListUtil.of(RemindType.TO_DO_REMINDER.getValue(), RemindType.CHANGE_NOTICE.getValue()))
//                    .eq(WechatMsg::getState, 0).list();
//    }

}
