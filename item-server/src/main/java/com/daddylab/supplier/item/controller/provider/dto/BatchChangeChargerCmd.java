package com.daddylab.supplier.item.controller.provider.dto;

import com.alibaba.cola.dto.Command;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2025年04月14日 10:11 AM
 */
@Data
@ApiModel("批量修改供应商负责人信息")
public class BatchChangeChargerCmd extends Command {

  private static final long serialVersionUID = 1336328907803584770L;

  @ApiModelProperty("供应商ID列表")
  private List<Long> providerIds;

  @ApiModelProperty("负责人ID")
  private Long chargerUserId;

  @ApiModelProperty("负责人类型。1主要负责人，2次要负责人")
  @NotNull(message = "负责人类型不能为空")
  private Integer chargerType;
}
