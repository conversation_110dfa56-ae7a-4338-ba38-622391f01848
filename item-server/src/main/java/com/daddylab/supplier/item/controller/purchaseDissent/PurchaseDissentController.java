package com.daddylab.supplier.item.controller.purchaseDissent;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.purchase.purchaseDissent.PurchaseDissentBizService;
import com.daddylab.supplier.item.controller.purchaseDissent.dto.PurchaseDissentCmd;
import com.daddylab.supplier.item.controller.purchaseDissent.dto.PurchaseDissentVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @ClassName PurchaseDissentController.java
 * @description 采购异议
 * @createTime 2021年11月17日 13:50:00
 */
@Slf4j
@Api(value = "采购异议相关API", tags = "采购异议相关API")
@RestController
@RequestMapping("/purchaseDissent/h5")
public class PurchaseDissentController {
    @Autowired
    private PurchaseDissentBizService purchaseDissentBizService;

    @ResponseBody
    @ApiOperation(value = "查看异议")
    @GetMapping("/getDissent")
    public MultiResponse<PurchaseDissentVo> getDissent(@ApiParam("采购id") Long id) {
        return purchaseDissentBizService.getDissent(id);
    }

    @ResponseBody
    @ApiOperation("异议创建")
    @PostMapping(value = "/create")
    public Response create(@RequestHeader("Authorization") String token ,@RequestBody @Validated PurchaseDissentCmd cmd) {
        return purchaseDissentBizService.add(token,cmd);
    }

    @ResponseBody
    @ApiOperation(value = "修改异议")
    @PostMapping("/update")
    public Response update(@RequestHeader("Authorization") String token ,@RequestBody @Validated PurchaseDissentCmd cmd) {
        return purchaseDissentBizService.update(token,cmd);
    }


}
