package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderSettlementDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderSettlementDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderSettlementDetailService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 订单结算/订货明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Service
public class OrderSettlementDetailServiceImpl extends DaddyServiceImpl<OrderSettlementDetailMapper, OrderSettlementDetail> implements IOrderSettlementDetailService {

}
