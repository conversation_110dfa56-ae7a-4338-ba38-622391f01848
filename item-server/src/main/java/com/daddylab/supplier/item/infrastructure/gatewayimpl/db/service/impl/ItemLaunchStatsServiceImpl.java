package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemLaunchStats;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemLaunchStatsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemLaunchStatsService;
import java.util.Collection;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品上新统计数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-29
 */
@Service
public class ItemLaunchStatsServiceImpl extends
        DaddyServiceImpl<ItemLaunchStatsMapper, ItemLaunchStats> implements
        IItemLaunchStatsService {

    @Override
    public ItemLaunchStats auditStat(Long itemId, Long legalProcessorId, Long legalAuditStartTime,
            Long legalAuditEndTime, Long qcProcessorId, Long qcAuditStartTime,
            Long qcAuditEndTime) {
        final ItemLaunchStats itemLaunchStats = getItemLaunchStats(itemId);
        itemLaunchStats.setAuditStat(legalProcessorId, legalAuditStartTime, legalAuditEndTime,
                qcProcessorId, qcAuditStartTime, qcAuditEndTime);
        saveOrUpdate(itemLaunchStats);
        return itemLaunchStats;
    }

    @Override
    public ItemLaunchStats getItemLaunchStats(Long itemId) {
        final Optional<ItemLaunchStats> itemLaunchStatsOptional = lambdaQuery()
                .eq(ItemLaunchStats::getItemId, itemId).oneOpt();
        return itemLaunchStatsOptional.orElseGet(() -> {
            final ItemLaunchStats newItemLaunchStats = new ItemLaunchStats();
            newItemLaunchStats.setItemId(itemId);
            return newItemLaunchStats;
        });
    }

    @Override
    public void fixLaunchTime(Long itemId, Long launchTime) {
        final ItemLaunchStats itemLaunchStats = getItemLaunchStats(itemId);
        itemLaunchStats.released(launchTime);
        updateById(itemLaunchStats);
    }

    @Override
    public boolean delStats(Long itemId) {
        final LambdaQueryWrapper<ItemLaunchStats> removeStmt = Wrappers.lambdaQuery();
        removeStmt.eq(ItemLaunchStats::getItemId, itemId);
        return removeWithTime(removeStmt);
    }

    @Override
    public boolean delStatsBatch(Collection<Long> itemIds) {
        final LambdaQueryWrapper<ItemLaunchStats> removeStmt = Wrappers.lambdaQuery();
        removeStmt.in(ItemLaunchStats::getItemId, itemIds);
        return removeWithTime(removeStmt);
    }
}
