package com.daddylab.supplier.item.application.platformItem.data;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/27
 */
@Data
@ApiModel("平台商品详情")
public class PlatformItemDetail {

    /**
     * id
     */
	@ApiModelProperty("id")
    private Long id;

    /**
     * 平台商品名称
     */
    @ApiModelProperty("平台商品名称")
    private String goodsName;

    /**
     * 商品图片
     */
    @ApiModelProperty("商品图片")
    private List<String> itemImages;

    /**
     * 商品编码
     */
	@ApiModelProperty("商品编码")
    private String itemCode;

    /**
     * 匹配到的我们自己的商品ID
     */
	@ApiModelProperty("匹配到的我们自己的商品ID")
    private Long itemId;

    /**
     * 店铺名称
     */
	@ApiModelProperty("店铺名称")
    private String shopName;

    /**
     * 匹配到的我们自己的店铺ID
     */
	@ApiModelProperty("匹配到的我们自己的店铺ID")
    private Long shopId;

    /**
     * 平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城
     */
	@ApiModelProperty("平台")
    private Platform platform;

    /**
     * 商品类型
     */
    @ApiModelProperty("商品类型")
	private PlatformItemType itemType;

    /**
     * 平台ICON
     */
    @ApiModelProperty("平台ICON")
    private String platformIcon;

    /**
     * 1:在售 0:已下架
     */
	@ApiModelProperty("1:在售 0:已下架")
    private PlatformItemStatus status;

    /**
     * 外部平台商品ID
     */
	@ApiModelProperty("外部平台商品ID")
    private String outerItemId;

    /**
     * 外部平台商品编码
     */
	@ApiModelProperty("外部平台商品编码")
    private String outerItemCode;

    /**
     * 平台售价
     */
	@ApiModelProperty("平台售价")
    private BigDecimal price;

    /**
     * 平台库存
     */
	@ApiModelProperty("平台库存")
    private Integer stock;

    /**
     * sku数量
     */
	@ApiModelProperty("sku数量")
    private Integer skuNum;

    /**
     * 类目ID
     */
	@ApiModelProperty("类目ID")
    private Long categoryId;

    /**
     * 类目的完整路径名称
     */
	@ApiModelProperty("类目的完整路径名称")
    private String[] categoryPath;

    /**
     * 品牌ID
     */
    @ApiModelProperty("品牌ID")
    private Long brandId;

    /**
     * 品牌名称
     */
    @ApiModelProperty("品牌名称")
    private String brandName;

    /**
     * 最后修改时间
     */
    @ApiModelProperty("最后修改时间")
    private String updatedAt;

    /**
     * 首次上架时间
     */
    @ApiModelProperty("首次上架时间")
    private String firstListingTime;

    /**
     * 首销时间
     */
    @ApiModelProperty("首销时间")
    private String firstSalesTime;
    
    /**
     * 下架时间
     */
    @ApiModelProperty("下架时间")
    private String unListingTime;

    /**
     * 可用库存
     */
    @ApiModelProperty("可用库存")
    private Integer availableStock;

    /**
     * SKU
     */
	@ApiModelProperty("SKU")
    @Valid
    private List<PlatformItemDetailSku> skus;

    /**
     * 店铺编号
     */
    private String shopNo;

    /**
     * 是否开启库存同步
     */
    private Boolean syncEnabled; /**
     
     * 店铺是否开启库存同步
     */
    private Boolean shopSyncEnabled;

    /**
     * 是否开启库存锁定
     */
    private Boolean lockEnabled;
    
    /**
     * 最后分配库存时间
     */
    private Long lastStockAllocTime;
    
    /**
     * 最后同步库存时间
     */
    private Long lastStockSyncTime;
    
    /**
     * 链接类型（日常|活动|积分）
     */
    private String linkType;
    

}
