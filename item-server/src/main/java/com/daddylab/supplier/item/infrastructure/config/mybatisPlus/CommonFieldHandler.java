package com.daddylab.supplier.item.infrastructure.config.mybatisPlus;

import com.daddylab.supplier.item.infrastructure.common.ExternalUserContext;
import com.daddylab.supplier.item.infrastructure.common.LoginType;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExternalUser;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/14 10:50 上午
 * @description
 */
@Component
public class CommonFieldHandler extends BaseMetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "createdAt", DateUtil::currentTime, Long.class);
        this.strictInsertFill(metaObject, "createdUid", UserContext::getUserId, Long.class);
        this.strictInsertFill(metaObject, "updatedAt", DateUtil::currentTime, Long.class);
        this.strictInsertFill(metaObject, "updatedUid", UserContext::getUserId, Long.class);
        this.strictInsertFill(
                metaObject, "createdAt", () -> DateUtil.currentTime().intValue(), Integer.class);
        this.strictInsertFill(
                metaObject, "createdUid", () -> UserContext.getUserId().intValue(), Integer.class);
        this.strictInsertFill(
                metaObject, "updatedAt", () -> DateUtil.currentTime().intValue(), Integer.class);
        this.strictInsertFill(
                metaObject, "updatedUid", () -> UserContext.getUserId().intValue(), Integer.class);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        forceUpdateFill(metaObject, "updatedAt", DateUtil::currentTime, Long.class);
        forceUpdateFill(metaObject, "updatedUid", UserContext::getUserId, Long.class);
        this.strictInsertFill(
                metaObject, "updatedAt", () -> DateUtil.currentTime().intValue(), Integer.class);
        this.strictInsertFill(
                metaObject, "updatedUid", () -> UserContext.getUserId().intValue(), Integer.class);
    }

    private static long getContextUserId() {
        final Long userId = UserContext.getUserId();
        if (NumberUtil.isPositive(userId)) {
            return userId;
        }
        final Long externalUserId = ExternalUserContext.getOptional().map(ExternalUser::getId).orElse(null);
        if (NumberUtil.isPositive(externalUserId)) {
            return externalUserId;
        }
        return 0L;
    }

    private int getLoginType() {
        final Long userId = UserContext.getUserId();
        if (NumberUtil.isPositive(userId)) {
            return LoginType.DEFAULT.ordinal();
        }
        final Long externalUserId = ExternalUserContext.getOptional().map(ExternalUser::getId).orElse(null);
        if (NumberUtil.isPositive(externalUserId)) {
            return LoginType.EXTERNAL_USER.ordinal();
        }
        return LoginType.DEFAULT.ordinal();
    }
}
