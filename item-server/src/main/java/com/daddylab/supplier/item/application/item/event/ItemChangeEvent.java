package com.daddylab.supplier.item.application.item.event;


import com.daddylab.supplier.item.application.common.event.EntityChangeEvent;
import com.daddylab.supplier.item.common.domain.EntityChange;
import org.javers.core.diff.Diff;

/**
 * @deprecated 不再使用
 */
public class ItemChangeEvent<T> extends EntityChangeEvent<T> {
    public ItemChangeEvent(Long operatorId, Long targetId, EntityChange<T> entityChange) {
        super(operatorId, targetId, entityChange);
    }

    public static <T> ItemChangeEvent<T> ofNew(Long operatorId, Long targetId, T newEntity) {
        return new ItemChangeEvent<>(operatorId, targetId, EntityChange.ofAdd(newEntity));
    }

    public static <T> EntityChangeEvent<T> ofUpdate(Long operatorId, Long targetId, Diff diff, T oldEntity, T newEntity) {
        return new ItemChangeEvent<>(operatorId, targetId, EntityChange.ofUpdate(diff, oldEntity, newEntity));
    }

    public static <T> EntityChangeEvent<T> ofUpdate(Long operatorId, Long targetId, EntityChange<T> entityChange) {
        return new ItemChangeEvent<>(operatorId, targetId, entityChange);
    }
}
