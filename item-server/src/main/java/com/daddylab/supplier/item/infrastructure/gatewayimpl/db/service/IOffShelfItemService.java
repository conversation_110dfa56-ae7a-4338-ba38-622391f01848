package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddylabServicePlus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OffShelfItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OffShelfItemMapper;

/**
 * <p>
 * 下架管理-下架流程商品信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
public interface IOffShelfItemService extends IDaddylabServicePlus<OffShelfItem, OffShelfItemMapper> {

}
