package com.daddylab.supplier.item.infrastructure.config.mybatisPlus;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

@MappedJdbcTypes({JdbcType.TINYINT, JdbcType.INTEGER, JdbcType.VARCHAR})
public class IEnumTypeHandler<E extends IEnum<?>> extends BaseTypeHandler<E> {

    private final Class<E> type;

    public IEnumTypeHandler(Class<E> type) {
        this.type = type;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, E parameter, JdbcType jdbcType)
            throws SQLException {
        final Object parameterValue = parameter.getValue();
        if (jdbcType != null) {
            ps.setObject(i, parameterValue, jdbcType.TYPE_CODE);
        }
        if (parameterValue instanceof Integer) {
            ps.setInt(i, ((Integer) parameterValue));
        }
        if (parameterValue instanceof String) {
            ps.setString(i, ((String) parameterValue));
        }
    }

    @Override
    public E getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Object v = type.isPrimitive() ? rs.getObject(columnName, type) : rs.getObject(columnName);
        return v == null ? null : IEnum.getEnumByValue(type, v);
    }

    @Override
    public E getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object v = type.isPrimitive() ? rs.getObject(columnIndex, type) : rs.getObject(columnIndex);
        return v == null ? null : IEnum.getEnumByValue(type, v);
    }

    @Override
    public E getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Object v = type.isPrimitive() ? cs.getObject(columnIndex, type) : cs.getObject(columnIndex);
        return v == null ? null : IEnum.getEnumByValue(type, v);
    }
}
