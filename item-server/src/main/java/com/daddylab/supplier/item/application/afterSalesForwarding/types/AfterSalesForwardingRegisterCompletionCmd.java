package com.daddylab.supplier.item.application.afterSalesForwarding.types;

import com.daddylab.supplier.item.common.domain.dto.Command;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2024/5/27
 */
@Data
public class AfterSalesForwardingRegisterCompletionCmd extends Command {
    private static final long serialVersionUID = 2227603858394706117L;

    @ApiModelProperty(value = "物流单号")
    @NotBlank(message = "物流单号不能为空")
    private String deliveryNo;
}
