package com.daddylab.supplier.item.domain.brand.dto;

import com.daddylab.supplier.item.common.enums.EnableStatusEnum;
import com.daddylab.supplier.item.domain.brand.vo.BrandProviderVo;

import lombok.Data;

import org.javers.core.metamodel.annotation.DiffIgnore;
import org.javers.core.metamodel.annotation.Entity;
import org.javers.core.metamodel.annotation.Id;
import org.javers.core.metamodel.annotation.PropertyName;

import java.io.Serializable;
import java.util.List;

@Entity
@Data
public class BrandDetail implements Serializable {
    /**
     * id
     */
    @PropertyName("ID")
    @Id
    protected Long id;

    /**
     * 品牌编号
     */
    @PropertyName("品牌编号")
    @DiffIgnore
    private String sn;

    /**
     * 品牌名称
     */
    @PropertyName("品牌名称")
    private String name;

    /**
     * 品牌LOGO
     */
    @PropertyName("LOGO")
    private String logo;

    /**
     * 所属供应商
     */
    @PropertyName("所属供应商")
    private List<BrandProviderVo> providers;

    /**
     * 状态 0:停用 1:正常
     */
    @PropertyName("状态")
    private EnableStatusEnum status;

    /**
     * 业务线
     */
    @PropertyName("合作模式")
    private List<Integer> businessLine;
}
