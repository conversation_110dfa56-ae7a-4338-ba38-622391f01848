package com.daddylab.supplier.item.application.shop;

import static com.daddylab.supplier.item.application.shop.InventoryAllocShopBizServiceImpl.EDITE_KEY;

import com.daddylab.supplier.item.application.shop.dto.ShopExecutionDto;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQConsumerBase;
import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQConsumerGroup;
import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQTopic;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.redisson.api.RReadWriteLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> up
 * @date 2025年06月25日 3:23 PM
 */
@Slf4j
@Component
@RocketMQMessageListener(
    consumerGroup = MQConsumerGroup.SHOP_STOCK_CONSUMER,
    topic = MQTopic.SHOP_STOCK_TOPIC,
    consumeThreadMax = 2)
public class ShopStockConsumer extends RocketMQConsumerBase<ShopExecutionDto> {

  @Autowired InventoryAllocShopBizServiceImpl inventoryAllocShopBizService;

  public ShopStockConsumer(
      RedissonClient redissonClient, StandardEnvironment environment, RefreshConfig refreshConfig) {
    super(redissonClient, environment, refreshConfig);
  }

  @Override
  protected void handle(MessageExt msg, @Nullable ShopExecutionDto body) {
    if (Objects.isNull(body)) return;

    RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(EDITE_KEY);
    RLock writeLock = readWriteLock.writeLock();
    try {
      writeLock.lock(20, TimeUnit.SECONDS);
      inventoryAllocShopBizService.nextHandler(body.getSignal(),body.getTargetTime());
    } catch (Exception e) {
      log.error("库存同步店铺任务执行异常", e);
    } finally {
      writeLock.unlock();
    }
  }
}
