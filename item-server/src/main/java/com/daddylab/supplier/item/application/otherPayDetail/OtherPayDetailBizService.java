package com.daddylab.supplier.item.application.otherPayDetail;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherPayDetail;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OtherPayDetailBizService.java
 * @description
 * @createTime 2022年03月25日 15:05:00
 */
public interface OtherPayDetailBizService {

    /**
     * 新增详情
     * @param otherPayDetails
     * @param otherPayIds
     */
    void insert(List<OtherPayDetail> otherPayDetails, Long otherPayIds);

    /**
     * 修改详情
     * @param otherPayDetails
     * @param otherPayId
     */
    void update(List<OtherPayDetail> otherPayDetails, Long otherPayId);

    /**
     * 根据id查详情
     * @param orderPayId
     * @return
     */
    List<OtherPayDetail> getById(Long orderPayId);

    /**
     * 获取总计
     * @param orderPayId
     * @return
     */
    OtherPayDetail getTotal(Long orderPayId);

}
