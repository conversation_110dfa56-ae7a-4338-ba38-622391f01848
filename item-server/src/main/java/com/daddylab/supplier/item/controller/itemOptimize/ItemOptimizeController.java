package com.daddylab.supplier.item.controller.itemOptimize;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.itemOptimize.ItemOptimizeBizService;
import com.daddylab.supplier.item.application.operateLog.OperateLogBizService;
import com.daddylab.supplier.item.application.process.ProcessBizService;
import com.daddylab.supplier.item.common.domain.dto.IdCmd;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.types.itemOptimize.*;
import com.daddylab.supplier.item.types.process.TaskClaimCmd;
import com.daddylab.supplier.item.types.process.TaskUrgeCmd;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/itemOptimize/")
@Api(value = "商品优化相关API", tags = "商品优化相关API")
public class ItemOptimizeController {

    @Autowired private ItemOptimizeBizService itemOptimizeBizService;
    @Autowired private ProcessBizService processBizService;
    @Autowired private OperateLogBizService operateLogBizService;

    @ApiOperation("商品优化项目查询")
    @PostMapping("/query")
    public PageResponse<ItemOptimizePlanItem> query(
            @Validated @RequestBody ItemOptimizeQuery query) {
        return itemOptimizeBizService.query(query);
    }

    @ApiOperation("商品优化抽屉")
    @PostMapping("/drawer")
    public SingleResponse<ItemOptimizeDrawer> drawer(
            @Validated @RequestBody IdCmd id) {
        return itemOptimizeBizService.drawer(id.getId());
    }

    @ApiOperation("保存商品优化抽屉")
    @PostMapping("/saveDrawer")
    public Response saveDrawer(
            @Validated @RequestBody ItemOptimizeSaveCmd saveCmd) {
        return itemOptimizeBizService.save(saveCmd);
    }

    @ApiOperation(value = "修改负责人", notes = "仅QC、法务可在其对应状态下修改对应负责人")
    @PostMapping("/modifyResponsiblePersons")
    public Response modifyResponsiblePersons(
            @Validated @RequestBody ModifyResponsiblePersonCmd saveCmd) {
        return itemOptimizeBizService.modifyResponsiblePersons(saveCmd);
    }

    @ApiOperation("删除商品优化")
    @PostMapping("/delete")
    public Response delete(@Validated @RequestBody IdCmd cmd) {
        return itemOptimizeBizService.delete(cmd.getId());
    }

    @GetMapping(value = "/operateLogs")
    @ApiOperation("操作日志")
    public Response operateLogs(Long targetId) {
        return operateLogBizService.getOperateLogs(OperateLogTarget.ITEM_OPTIMIZE, targetId);
    }


    @ApiOperation("催办")
    @PostMapping("/urge")
    public Response urge(@Valid @RequestBody TaskUrgeCmd cmd) {
        return itemOptimizeBizService.urge(UserContext.getUserId(), cmd);
    }

    @ApiOperation("商品优化流程任务认领")
    @PostMapping("/audit/claim")
    public Response claim(@Validated @RequestBody TaskClaimCmd cmd) {
        return processBizService.claim(UserContext.getUserId(), cmd.getTaskId());
    }

    @ApiOperation("商品优化流程任务提交")
    @PostMapping("/audit/complete")
    public Response complete(@Validated @RequestBody TaskCompleteCmd cmd) {
        return itemOptimizeBizService.complete(cmd);
    }

    @ApiOperation("商品优化流程撤回")
    @PostMapping("/audit/rollback")
    public Response rollback(@Validated @RequestBody RollbackCmd cmd) {
        return itemOptimizeBizService.rollback(cmd);
    }

}
