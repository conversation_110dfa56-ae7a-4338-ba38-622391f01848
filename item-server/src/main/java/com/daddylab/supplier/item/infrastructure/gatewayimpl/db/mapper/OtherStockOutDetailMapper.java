package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherStockOutDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 其他出库单详情 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
@Repository
public interface OtherStockOutDetailMapper extends DaddyBaseMapper<OtherStockOutDetail> {

    List<Long> getIdsByItem(@Param("skuCode") String skuCode, @Param("code") String code,
                            @Param("itemId") Long itemId, @Param("brandId") Long brandI,
                            @Param("businessLine") Collection<Integer> businessLine);
}
