package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSaleStockOutOrderDetails;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtSaleStockOutOrderDetailsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtSaleStockOutOrderDetailsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 旺店通销售出库单详情 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Service
public class WdtSaleStockOutOrderDetailsServiceImpl extends DaddyServiceImpl<WdtSaleStockOutOrderDetailsMapper, WdtSaleStockOutOrderDetails> implements IWdtSaleStockOutOrderDetailsService {

}
