package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtStockSpec;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtStockSpecMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtStockSpecService;
import com.daddylab.supplier.item.types.stockSpec.WarehouseStockSpecStatistic;
import com.daddylab.supplier.item.types.stockStatistic.WdtStockSpecStatisticQuery;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.daddylab.supplier.item.infrastructure.utils.MPUtil.limit;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Service
public class WdtStockSpecServiceImpl extends DaddyServiceImpl<WdtStockSpecMapper, WdtStockSpec> implements IWdtStockSpecService {

    @Override
    public Optional<WdtStockSpec> selectOneByWarehouseNoAndSpecNo(String warehouseNo, String skuNo) {
        return lambdaQuery().eq(WdtStockSpec::getWarehouseNo, warehouseNo)
                            .eq(WdtStockSpec::getSpecNo, skuNo)
                            .eq(WdtStockSpec::getDefect,0)
                            .last(limit(1)).oneOpt();
    }

    @Override
    public Map<String, WarehouseStockSpecStatistic> statistics(WdtStockSpecStatisticQuery query) {
        return statisticsGroupByWarehouse(getDaddyBaseMapper().statistics(query));
    }

    private static Map<String, WarehouseStockSpecStatistic> statisticsGroupByWarehouse(List<WarehouseStockSpecStatistic> statistics) {
        return statistics.stream()
                         .collect(Collectors.toMap(
                                 WarehouseStockSpecStatistic::getWarehouseNo,
                                 Function.identity()));
    }
}
