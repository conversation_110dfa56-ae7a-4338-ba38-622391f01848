package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerMergeItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerMergeItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerMergeItemService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 新品商品合并审核包商品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Service
public class ItemDrawerMergeItemServiceImpl extends DaddyServiceImpl<ItemDrawerMergeItemMapper, ItemDrawerMergeItem> implements IItemDrawerMergeItemService {

}
