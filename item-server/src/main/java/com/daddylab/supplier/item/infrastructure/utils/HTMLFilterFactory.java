package com.daddylab.supplier.item.infrastructure.utils;

import cn.hutool.http.HTMLFilter;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Sets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/12/9
 */
public class HTMLFilterFactory {

    /**
     * 返回一个 HTMLFilter 构造器
     *
     * @return HTMLFilter 实例构造器
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 构造一个拓展了一些常用标签的 HTMLFilter
     *
     * @return HTMLFilter 实例
     */
    public static HTMLFilter extented() {
        return builder()
                .allowTag("p", ImmutableList.of("style"))
                .allowTag("span", ImmutableList.of("style"))
                .allowTag("a", ImmutableList.of("href", "target", "style"))
                .allowTag("img",
                        ImmutableList.of("src", "width", "height", "alt", "style", "data-href"))
                .encodeQuotes(false)
                .build();
    }

    public static class Builder {

        /**
         * set of allowed html elements, along with allowed attributes for each element
         **/
        private final Map<String, List<String>> vAllowed;

        /**
         * html elements which must always be self-closing (e.g. "&lt;img /&gt;")
         **/
        private final Set<String> vSelfClosingTags;

        /**
         * html elements which must always have separate opening and closing tags (e.g.
         * "&lt;b&gt;&lt;/b&gt;")
         **/
        private final Set<String> vNeedClosingTags;

        /**
         * set of disallowed html elements
         **/
        private final Set<String> vDisallowed;

        /**
         * attributes which should be checked for valid protocols
         **/
        private final Set<String> vProtocolAtts;

        /**
         * allowed protocols
         **/
        private final Set<String> vAllowedProtocols;

        /**
         * tags which should be removed if they contain no content (e.g. "&lt;b&gt;&lt;/b&gt;" or
         * "&lt;b /&gt;")
         **/
        private final Set<String> vRemoveBlanks;

        /**
         * entities allowed within html markup
         **/
        private final Set<String> vAllowedEntities;

        /**
         * flag determining whether comments are allowed in input String.
         */
        private boolean stripComment;

        private boolean encodeQuotes;

        /**
         * flag determining whether to try to make tags when presented with "unbalanced" angle
         * brackets (e.g. "&lt;b text &lt;/b&gt;" becomes "&lt;b&gt; text &lt;/g&gt;"). If set to
         * false, unbalanced angle brackets will be html escaped.
         */
        private boolean alwaysMakeTags;

        public Builder allowTag(String tag) {
            return allowTag(tag, Collections.emptyList());
        }

        public Builder allowTag(String tag, List<String> allowedAttrs) {
            vAllowed.put(tag, allowedAttrs);
            return this;
        }

        public Builder disallowTag(String tag) {
            vDisallowed.add(tag);
            return this;
        }

        public Builder protocolAttr(String attr) {
            vProtocolAtts.add(attr);
            return this;
        }

        public Builder allowProtocol(String protocol) {
            vAllowedProtocols.add(protocol);
            return this;
        }

        public Builder selfClosingTag(String tag) {
            vSelfClosingTags.add(tag);
            return this;
        }

        public Builder needClosingTags(String tag) {
            vNeedClosingTags.add(tag);
            return this;
        }

        public Builder removeTagIfBlanks(String tag) {
            vRemoveBlanks.add(tag);
            return this;
        }

        public Builder allowEntity(String entity) {
            vAllowedEntities.add(entity);
            return this;
        }

        public Builder stripComment(Boolean stripComment) {
            this.stripComment = stripComment;
            return this;
        }

        public Builder encodeQuotes(Boolean encodeQuotes) {
            this.encodeQuotes = encodeQuotes;
            return this;
        }

        public Builder alwaysMakeTags(Boolean alwaysMakeTags) {
            this.alwaysMakeTags = alwaysMakeTags;
            return this;
        }

        public Builder() {
            vAllowed = new HashMap<>();
            final ArrayList<String> a_atts = new ArrayList<>();
            a_atts.add("href");
            a_atts.add("target");
            vAllowed.put("a", a_atts);

            final ArrayList<String> img_atts = new ArrayList<>();
            img_atts.add("src");
            img_atts.add("width");
            img_atts.add("height");
            img_atts.add("alt");
            vAllowed.put("img", img_atts);

            final ArrayList<String> no_atts = new ArrayList<>();
            vAllowed.put("b", no_atts);
            vAllowed.put("strong", no_atts);
            vAllowed.put("i", no_atts);
            vAllowed.put("em", no_atts);

            vSelfClosingTags = Sets.newHashSet("img");
            vNeedClosingTags = Sets.newHashSet("a", "b", "strong", "i", "em");
            vDisallowed = Sets.newHashSet();
            vAllowedProtocols = Sets.newHashSet("http", "mailto", "https"); // no ftp.
            vProtocolAtts = Sets.newHashSet("src", "href");
            vRemoveBlanks = Sets.newHashSet("a", "b", "strong", "i", "em");
            vAllowedEntities = Sets.newHashSet("amp", "gt", "lt", "quot");
            stripComment = true;
            encodeQuotes = true;
            alwaysMakeTags = true;
        }

        public HTMLFilter build() {
            final ImmutableMap<String, Object> config = ImmutableMap.<String, Object>builder()
                    .put("vAllowed", vAllowed)
                    .put("vSelfClosingTags", vSelfClosingTags.toArray(new String[0]))
                    .put("vNeedClosingTags", vNeedClosingTags.toArray(new String[0]))
                    .put("vDisallowed", vDisallowed.toArray(new String[0]))
                    .put("vAllowedProtocols", vAllowedProtocols.toArray(new String[0]))
                    .put("vProtocolAtts", vProtocolAtts.toArray(new String[0]))
                    .put("vRemoveBlanks", vRemoveBlanks.toArray(new String[0]))
                    .put("vAllowedEntities", vAllowedEntities.toArray(new String[0]))
                    .put("stripComment", stripComment)
                    .put("encodeQuotes", encodeQuotes)
                    .put("alwaysMakeTags", alwaysMakeTags).build();
            return new HTMLFilter(config);
        }
    }


}
