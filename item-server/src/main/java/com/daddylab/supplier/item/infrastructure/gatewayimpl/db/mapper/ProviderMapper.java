package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.controller.provider.dto.ProviderDropDownVo;
import com.daddylab.supplier.item.controller.provider.dto.ProviderVO;
import com.daddylab.supplier.item.controller.test.dto.ProviderExportInfoDto;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ProviderDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuCodeProviderDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 供应商 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Repository
public interface ProviderMapper extends <PERSON><PERSON><PERSON><PERSON>apper<Provider> {

    /**
     * 提供供应商下拉选。
     *
     * @param name 名字
     * @param offset 偏移值
     * @param size 页大小
     * @param businessLine
     */
    List<ProviderDropDownVo> dropDownList(
            @Param(value = "name") String name,
            @Param("offset") Integer offset,
            @Param("size") Integer size,
            @Param("businessLine") List<Integer> businessLine);

    /**
     * 根据skuCode列表查询出对应的providerId
     *
     * @param skuCodeList
     * @return
     */
    List<SkuCodeProviderDO> getListBySkuCodeList(@Param("list") List<String> skuCodeList);

    /**
     * 根据id获取供应商信息
     *
     * @param id 供应商id
     * @return 供应商信息
     */
    ProviderVO getProviderById(Long id);

    /**
     * 根据供应商编号获取供应商PO（忽略逻辑删除字段，即已经被逻辑删除的仍然可以被查出来）
     * @param providerNo 供应商编号
     * @return 供应商PO
     */
    Provider getProviderPOByNoIncludeLogicDeleted(String providerNo);

    /**
     * 检查skuCode是否属于目标provider
     *
     * @param providerId  供应商id
     * @param skuCodeList 请求skuCodeList
     * @return 即在入参codeList中又属于目标provider的skuCode
     */
    List<String> verifySkuCodeInTargetProvider(@Param("providerId") Long providerId, @Param("list") List<String> skuCodeList);


    @Select("select id,provider_no,name\n" +
            "from provider\n" +
            "where id in (\n" +
            "    select provider_id\n" +
            "    from item\n" +
            "    where id = (select item_id\n" +
            "                from item_sku\n" +
            "                where sku_code = #{skuCode}\n" +
            "    )\n" +
            ")")
    ProviderDO selectBySkuCode(@Param("skuCode") String skuCode);

    List<ProviderExportInfoDto> getProviderBankInfoDtoList();

}
