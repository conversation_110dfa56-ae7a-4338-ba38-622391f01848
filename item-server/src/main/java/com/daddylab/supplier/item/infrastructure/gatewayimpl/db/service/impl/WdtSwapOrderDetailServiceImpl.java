package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSwapOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtSwapOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtSwapOrderDetailService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 旺店通换出单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Service
public class WdtSwapOrderDetailServiceImpl extends DaddyServiceImpl<WdtSwapOrderDetailMapper, WdtSwapOrderDetail> implements IWdtSwapOrderDetailService {

}
