package com.daddylab.supplier.item.infrastructure.utils;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;

import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.CodingErrorAction;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class StringUtil extends StrUtil {

    /**
     * 配置一个短链接缓存
     */
    private static final LoadingCache<String, MessageFormat> messageFormatCache = Caffeine
            .newBuilder()
            .maximumSize(10000)
            .expireAfterAccess(Duration.ofMinutes(60))
            .build(AutoNumberMessageFormat::new);

    public static boolean isNotBlank(CharSequence str) {
        return StrUtil.isNotBlank(str);
    }

    public static boolean isBlank(CharSequence str) {
        return StrUtil.isBlank(str);
    }

    /**
     * 格式化字符串（在 MessageFormat 的基础上拓展了参数占位符自动编号能力，使得可以直接使用 {} 作为占位符而无需显示指定参数下标）
     *
     * @param pattern 模式
     * @param params  参数填充
     * @return 格式化后的字符串
     * @see DecimalFormat
     * @see MessageFormat
     */
    public static String format(String pattern, Object... params) {
        return Objects.requireNonNull(messageFormatCache.get(pattern)).format(params);
    }

    /**
     * Truncates a string to the number of characters that fit in X bytes avoiding multi byte
     * characters being cut in half at the cut off point. Also handles surrogate pairs where 2
     * characters in the string is actually one literal character.
     * <p>
     * Based on: <a href="http://www.jroller.com/holy/entry/truncating_utf_string_to_the">...</a>
     */
    public static String truncateByMaxBytes(String s, int maxBytes, Charset charset) {
        if (s == null) {
            return null;
        }
        CharsetDecoder decoder = charset.newDecoder();
        byte[] sba = s.getBytes(charset);
        if (sba.length <= maxBytes) {
            return s;
        }
        // Ensure truncation by having byte buffer = maxBytes
        ByteBuffer bb = ByteBuffer.wrap(sba, 0, maxBytes);
        CharBuffer cb = CharBuffer.allocate(maxBytes);
        // Ignore an incomplete character
        decoder.onMalformedInput(CodingErrorAction.IGNORE);
        decoder.decode(bb, cb, true);
        decoder.flush(cb);
        return new String(cb.array(), 0, cb.position());
    }

    /**
     * 按照字节数来截取字串，默认采用UTF-8字符集
     *
     * @param s        原字符串
     * @param maxBytes 截取的最大字节数
     * @return 返回不超过指定字节长度的字串，末尾不完整的字节不会被返回
     */
    public static String truncateByMaxBytes(String s, int maxBytes) {
        return truncateByMaxBytes(s, maxBytes, StandardCharsets.UTF_8);
    }

    /**
     * 如果两个字符串相同，或者都是null，则返回true
     */
    public static boolean equals(String a, String b) {
        return StrUtil.equals(a, b);
    }

    /**
     * 如果两个字符串相同，或者都是null，则返回true
     */
    public static boolean equalsIgnoreCase(String a, String b) {
        return StrUtil.equalsIgnoreCase(a, b);
    }

    /**
     * 是否以指定字符串开头
     */
    public static boolean startWith(String str, String prefix) {
        return StrUtil.startWith(str, prefix);
    }

    /**
     * 是否以指定字符开头
     */
    public static boolean startWith(String str, char prefix) {
        return StrUtil.startWith(str, prefix);
    }

    /**
     * 是否以指定字符串开头
     */
    public static boolean startWithIgnoreCase(String str, String prefix) {
        return StrUtil.startWithIgnoreCase(str, prefix);
    }

    /**
     * 是否以指定字符串结尾
     */
    public static boolean endWith(String str, String suffix) {
        return StrUtil.endWith(str, suffix);
    }

    public static String ensureSuffix(String str, String suffix) {
        return StringUtil.endWith(str, suffix) ? str : str + suffix;
    }

    /**
     * 是否以指定字符串其中之一结尾
     */
    public static boolean endWithAny(String str, String... suffix) {
        return Arrays.stream(suffix).anyMatch(s -> StrUtil.endWith(str, s));
    }

    /**
     * 是否以指定字符串结尾
     */
    public static boolean endWithIgnoreCase(CharSequence str, CharSequence suffix) {
        return StrUtil.endWith(str, suffix, true);
    }

    /**
     * 移除字符串头尾空格
     */
    public static String trim(String str) {
        return StrUtil.trim(str);
    }

    /**
     * 移除字符串头部空格
     */
    public static String trimStart(String str) {
        return StrUtil.trimStart(str);
    }

    /**
     * 移除字符串头部空格
     */
    public static String trimEnd(String str) {
        return StrUtil.trimEnd(str);
    }

    /**
     * 移除字符串头部前缀
     */
    public static String trimStart(String string, String prefix) {
        int start = 0;
        int end = string.length();

        if (string.startsWith(prefix)) {
            start = prefix.length();
        }

        return end <= start ? "" : string.substring(start, end);
    }

    /**
     * 移除字符串尾部前缀
     */
    public static String trimEnd(String string, String suffix) {
        int start = 0;
        int end = string.length();

        if (string.endsWith(suffix)) {
            end -= suffix.length();
        }

        return end <= start ? "" : string.substring(start, end);
    }

    /**
     * 移除字符串前缀和后缀
     */
    public static String trim(String string, String prefix, String suffix) {
        int start = 0;
        int end = string.length();
        if (string.startsWith(prefix)) {
            start = prefix.length();
        }

        if (string.endsWith(suffix)) {
            end -= suffix.length();
        }

        return end <= start ? "" : string.substring(start, end);
    }

    /**
     * 移除字符串两侧的指定字符
     */
    public static String trim(String string, char... chs) {
        if (chs.length == 0) return string;
        return StrUtil.trim(string, 0, it -> ArrayUtil.contains(chs, it));
    }

    /**
     * 移除字符串头部的指定字符
     */
    public static String trimStart(String string, char... chs) {
        if (chs.length == 0) return string;
        return StrUtil.trim(string, -1, it -> ArrayUtil.contains(chs, it));
    }

    /**
     * 移除字符串尾部的指定字符
     */
    public static String trimEnd(String string, char... chs) {
        if (chs.length == 0) return string;
        return StrUtil.trim(string, -1, it -> ArrayUtil.contains(chs, it));
    }

    /**
     * 指定字符串是否在字符串中出现过
     *
     * @param str       字符串
     * @param searchStr 被查找的字符串
     * @return 是否包含
     * @since 5.1.1
     */
    public static boolean contain(String str, String searchStr) {
        return StrUtil.contains(str, searchStr);
    }

    public static List<Integer> splitToIntegerList(CharSequence str, CharSequence separator) {
        return splitTrim(str, separator).stream().map(Integer::parseInt).collect(Collectors.toList());
    }

    public static String maskMobile(String phone) {
        if (phone == null || phone.length() < 4) {
            return "****";
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }

    public static class AutoNumberMessageFormat extends MessageFormat {

        private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\{}");
        private static final Pattern SINGLE_QUOTE_PATTERN = Pattern.compile("[^']'[^']");
        private static final long serialVersionUID = 0L;

        public AutoNumberMessageFormat(String pattern) {
            super(autoNumberPlaceholder(pattern));
        }

        public AutoNumberMessageFormat(String pattern, Locale locale) {
            super(autoNumberPlaceholder(pattern), locale);
        }

        private static String autoNumberPlaceholder(String pattern) {
            final Matcher matcher0 = SINGLE_QUOTE_PATTERN.matcher(pattern);
            final StringBuffer resultPattern0 = new StringBuffer(pattern.length());
            while (matcher0.find()) {
                matcher0.appendReplacement(resultPattern0, matcher0.group().replace("'", "''"));
            }
            matcher0.appendTail(resultPattern0);
            pattern = resultPattern0.toString();
            final Matcher matcher = PLACEHOLDER_PATTERN.matcher(pattern);
            final StringBuffer resultPattern = new StringBuffer(pattern.length());
            int index = 0;
            while (matcher.find()) {
                matcher.appendReplacement(resultPattern, "{" + (index++) + "}");
            }
            matcher.appendTail(resultPattern);
            return resultPattern.toString();
        }
    }

}
