package com.daddylab.supplier.item.controller.stockout.dto;

import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.StockOutOrderState;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/13 14:03
 * @description StockOutOrderViewVO
 */
@Data
@ApiModel(value = "出库详情")
public class StockOutOrderViewVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "出库单号")
    private String no;

    @ApiModelProperty(value = "采购组织id")
    private Long purchaseOrganizationId;

    @ApiModelProperty(value = "采购组织名称")
    private String purchaseOrganizationName;

    @ApiModelProperty(value = "退料组织id")
    private Long organizationId;

    @ApiModelProperty(value = "退料组织")
    private String organizationName;

    @ApiModelProperty(value = "退料类型(1:库存退料)")
    private Integer returnType;

    @ApiModelProperty(value = "退料方式(1.退料补料。2.退料并扣款)")
    private Integer returnMode;

    @ApiModelProperty(value = "关联采购单id")
    private Long purchaseOrderId;

    @ApiModelProperty(value = "关联采购单号")
    private String purchaseOrderNo;

    @ApiModelProperty(value = "创建人")
    private String createdName;

    @ApiModelProperty(value = "创建时间")
    private Long createdAt;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否同步网点通.0不同步。1同步")
    private Integer isSyncWdt;

    /**
     * 实际退料出库数量
     */
    @ApiModelProperty(value = "交货(出库)总数量")
    private Integer totalReturnQuantity;

    @ApiModelProperty(value = "审核操作时间")
    private Long approvalAt;

    @ApiModelProperty(value = "审核流程id")
    private String approvalId;

    @ApiModelProperty(value = "出库明细")
    private List<StockOutOrderDetailVO> stockOutOrderDetailList;

    @ApiModelProperty(value = "供应商ID")
    private Long providerId;

    @ApiModelProperty(value = "出库状态:1待提交、2待审核、3待出库、4已出库、5已取消、6审核拒绝、7撤回审核")
    private Integer state;

    @ApiModelProperty(value = "同步状态: 2:同步旺店通失败、3:同步金蝶失败")
    private Integer syncState;

    @ApiModelProperty(value = "同步失败信息")
    private String syncMsg;

    private Long createdUid;

    private Integer businessLine;

    @ApiModelProperty(value = "操作类型 0默认，REVERSE_FIXED：逆向对冲单据，WRITE_OFF：原单据作废")
    private StockOutOrderState hedgeStatus;

    private String workbenchProcessId;

    private Long outboundTime;



}
