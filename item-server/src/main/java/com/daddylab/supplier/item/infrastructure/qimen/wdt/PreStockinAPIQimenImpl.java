package com.daddylab.supplier.item.infrastructure.qimen.wdt;

import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.PreStockinAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.PreStockinCancelResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.PreStockinCreateExtResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.PreStockinCreateExtSpec;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.PreStockinCreateExtStockinOrder;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.PreStockinSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.PreStockinSearchResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.daddylab.supplier.item.infrastructure.qimen.QimenConfig;
import com.daddylab.supplier.item.infrastructure.qimen.QimenWdtPreStockinAPI;
import com.qimencloud.api.QimenCloudClient;
import com.qimencloud.api.scene3ldsmu02o9.request.WdtWmsStockinPrestockinSearchRequest.Params;
import com.qimencloud.api.scene3ldsmu02o9.response.WdtWmsStockinPrestockinSearchResponse;
import com.taobao.api.ApiException;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/17
 */
public class PreStockinAPIQimenImpl extends WdtAPIQimenImplBase implements PreStockinAPI {

    QimenWdtPreStockinAPI qimenAPI;

    public PreStockinAPIQimenImpl(QimenCloudClient qimenClient,
            QimenConfig qimenConfig,
            WdtConfig wdtConfig,
            int configIndex
    ) {
        super(qimenClient, qimenConfig, wdtConfig, configIndex);
    }

    @Override
    protected void initApiInstance(QimenCloudClient qimenClient, QimenConfig qimenConfig,
            WdtConfig wdtConfig, int configIndex) {
        qimenAPI = new QimenWdtPreStockinAPI(qimenClient, qimenConfig, wdtConfig, configIndex);
    }

    @Override
    public PreStockinCreateExtResponse createExt(PreStockinCreateExtStockinOrder StockinOrder,
            List<PreStockinCreateExtSpec> specList) throws WdtErpException {
        throw new UnsupportedOperationException("暂未实现");
    }

    @Override
    public PreStockinSearchResponse search(PreStockinSearchParams params, Pager pager)
            throws WdtErpException {
        try {

            final Params qimenParams = new Params();
            qimenParams.setBarcode(params.getBarcode());
            qimenParams.setBuyerNick(params.getBuyerNick());
            qimenParams.setCtFrom(params.getCtFrom());
            qimenParams.setCtTo(params.getCtTo());
            qimenParams.setGoodsName(params.getGoodsName());
            qimenParams.setGoodsNo(params.getGoodsNo());
            qimenParams.setLogisticsNo(params.getLogisticsNo());
            qimenParams.setMtFrom(params.getMtFrom());
            qimenParams.setMtTo(params.getMtTo());
            qimenParams.setRemark(params.getRemark());
            qimenParams.setSenderMobile(params.getSenderMobile());
            qimenParams.setSenderName(params.getSenderName());
            qimenParams.setShortName(params.getShortName());
            qimenParams.setSpecNo(params.getSpecNo());
            qimenParams.setStatus(params.getStatus() != null ? Long.valueOf(params.getStatus()) : null);
            qimenParams.setStockinNo(params.getStockinNo());
            qimenParams.setWarehouseNo(params.getWarehouseNo());

            final WdtWmsStockinPrestockinSearchResponse response = qimenAPI
                    .wmsStockinPrestockinSearch(qimenParams, pager.getPageNo() + 1,
                            pager.getPageSize());
            return checkAndReturnData(response, PreStockinSearchResponse.class);
        } catch (ApiException exception) {
            throw transformException(exception);
        }
    }

    @Override
    public PreStockinCancelResponse cancel(String preStockinNo) throws WdtErpException {
        throw new UnsupportedOperationException("暂未实现");
    }
}
