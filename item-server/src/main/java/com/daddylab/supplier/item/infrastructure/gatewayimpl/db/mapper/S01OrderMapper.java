package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.S01Order;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 淘宝订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@DS("rpaProd")
public interface S01OrderMapper extends DaddyBaseMapper<S01Order> {

    List<String> selectBuyerName(@Param("name") String name, @Param("size") Integer size);

}
