package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 数据推送记录
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataPush implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 被推送的数据来源类型
     */
    private Integer sourceType;

    /**
     * 被推送的数据来源ID
     */
    private Long sourceId;

    /**
     * 数据被推送到哪
     */
    private Integer targetType;

    /**
     * 状态 0:等待推送 1:推送成功 2:推送失败（多次失败后可能被标记为推送失败）
     */
    private Integer state;

    /**
     * 失败次数
     */
    private Integer failCount;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedAt;

    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /**
     * 更新人id
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updatedUid;

    /**
     * 逻辑删除
     */
    @TableLogic
    private Integer isDel;

    /**
     * 是否是异步推送 0:同步 1:异步
     */
    private Integer async;

}
