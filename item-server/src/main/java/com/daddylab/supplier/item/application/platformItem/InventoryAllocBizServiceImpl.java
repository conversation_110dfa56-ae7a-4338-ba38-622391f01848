package com.daddylab.supplier.item.application.platformItem;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.message.QyMsgSendService;
import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.application.platformItem.config.PlatformItemSyncConfig;
import com.daddylab.supplier.item.application.stockSpec.StockSpecBizService;
import com.daddylab.supplier.item.domain.messageRobot.gateway.MessageRobotGateway;
import com.daddylab.supplier.item.domain.platformItem.vo.ListOuterSkuCodeQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.MsgTemplateCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemSkuStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLock;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.stockSpec.AvailableStockSpecVO;
import com.daddylab.supplier.item.types.stockSpec.ShopSpecAllocableStockVO;
import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.Value;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class InventoryAllocBizServiceImpl implements InventoryAllocBizService {
    
    @Autowired
    IInventoryAllocService inventoryAllocService;
    @Autowired
    IInventoryAllocShopService inventoryAllocShopService;
    @Autowired
    IPlatformItemSkuService platformItemSkuService;
    @Autowired
    IPlatformItemService platformItemService;
    @Autowired
    PlatformItemSyncConfig platformItemSyncConfig;
    @Autowired
    IWdtStockSpecRtService wdtStockSpecRtService;
    @Autowired
    IWdtStockSpecService wdtStockSpecService;
    @Autowired
    IInventoryMonitorService inventoryMonitorService;
    @Autowired
    ICombinationItemService combinationItemService;
    @Autowired
    IComposeSkuService composeSkuService;
    @Autowired
    private StockSpecBizService stockSpecBizService;

    
    @Override
    @DistributedLock(value = "allocInventory", msg = "库存分配任务正在执行中，请稍后再试")
    public void allocInventory() {
        allocInventory(null);
    }
    
    @Override
    @DistributedLock(value = "allocInventory", msg = "库存分配任务正在执行中，请稍后再试")
    public void allocInventory(Collection<String> skuCodes) {
        StopWatch watch = new StopWatch("库存分配");
        
        List<InventoryAllocShop> allocShops = getInventoryAllocShops();
        log.info( "[库存分配]待分配店铺查询完成，共计={}", allocShops.size());
        
        if (allocShops.isEmpty()) {
            log.info("[库存分配]待分配店铺为空，跳过分配");
            return;
        }
        
        List<String> allOuterSkuCodes;
        if (skuCodes != null) {
            allOuterSkuCodes = new ArrayList<>(skuCodes);
        } else {
            watch.start("查询所有在售链接商品SKU");
            List<String> shopNos = allocShops.stream().map(InventoryAllocShop::getShopNo).collect(Collectors.toList());
            allOuterSkuCodes = getAllOnSaleOuterSkuCodes(shopNos);
            watch.stop();
            log.info(
                    "[库存分配]查询到所有在售链接商品SKU，去重后共计={}，time={}ms",
                    allOuterSkuCodes.size(),
                    watch.getLastTaskTimeMillis());
        }
        
        watch.start("按编码分配库存");
        Flux.fromIterable(allOuterSkuCodes)
                .window(50)
                .concatMap(batch -> batch.flatMap(skuCode -> allocInventory(skuCode, allocShops)))
                .blockLast();
        watch.stop();
        log.info(
                "[库存分配]库存分配完成，分配耗时={}ms，总耗时={}ms",
                watch.getLastTaskTimeMillis(),
                watch.getTotalTimeMillis());
    }
    
    // =================================================================
    // Core Allocation Logic
    // =================================================================
    
    private Mono<Void> allocInventory(String skuCode, List<InventoryAllocShop> InventoryAllocShops) {
        log.trace("[库存分配]开始分配商品SKU:{}", skuCode);
        CombinationItem combinationItem = combinationItemService.getByItemCode(skuCode);
        if (combinationItem != null) {
            CombinationItemAllocContext context =
                    new CombinationItemAllocContext().setSkuCode(skuCode)
                            .setInventoryAllocShops(InventoryAllocShops).setCombinationItem(combinationItem);
            return allocCombinationItemInventory(context);
        } else {
            RegularItemAllocContext context =
                    new RegularItemAllocContext().setSkuCode(skuCode)
                            .setInventoryAllocShops(InventoryAllocShops);
            return allocRegularItemInventory(context);
        }
    }
    
    /**
     * 分配普通商品库存
     */
    private Mono<Void> allocRegularItemInventory(RegularItemAllocContext context) {
        String skuCode = context.getSkuCode();
        // 查询在售的平台商品SKU
        List<PlatformItemSku> platformItemSkus = queryOnSalePlatformItemSkus(skuCode);
        if (platformItemSkus.isEmpty()) {
            log.trace("[库存分配][分配普通商品库存]商品SKU={}，未找到关联的在售平台商品SKU，跳过分配", skuCode);
            return Mono.empty();
        }
        
        // 2. 查询相关组合装商品
        List<Long> platformItemIds =
                platformItemSkus.stream()
                        .map(PlatformItemSku::getPlatformItemId)
                        .collect(Collectors.toList());
        List<PlatformItem> platformItems = platformItemService.listByIds(platformItemIds);
        
        // 查询套装内包含此单品的组合装
        List<CombinationItem> relatedCombinationItems = combinationItemService.listBySkuCode(skuCode);
        List<PlatformItem> relatedCombinationPiList = Collections.emptyList();
        List<PlatformItemSku> relatedCombinationPisList = Collections.emptyList();
        
        if (!relatedCombinationItems.isEmpty()) {
            List<String> relatedCombinationCodes =
                    relatedCombinationItems.stream()
                            .map(CombinationItem::getCode)
                            .collect(Collectors.toList());
            relatedCombinationPisList =
                    platformItemSkuService
                            .lambdaQuery()
                            .in(PlatformItemSku::getOuterSkuCode, relatedCombinationCodes)
                            .eq(PlatformItemSku::getStatus, PlatformItemSkuStatus.ON_SALE.getValue())
                            .list();
            if (!relatedCombinationPisList.isEmpty()) {
                List<Long> relatedCombinationPiIds =
                        relatedCombinationPisList.stream()
                                .map(PlatformItemSku::getPlatformItemId)
                                .collect(Collectors.toList());
                relatedCombinationPiList = platformItemService.listByIds(relatedCombinationPiIds);
            }
        }
        
        Set<PlatformItem> allPlatformItems = new HashSet<>();
        allPlatformItems.addAll(platformItems);
        allPlatformItems.addAll(relatedCombinationPiList);
        
        setSyncEnabledByShopSetting(context.getInventoryAllocShops(), allPlatformItems);
        Map<Long, PlatformItem> platformItemMap =
                allPlatformItems.stream()
                        .collect(Collectors.toMap(PlatformItem::getId, Function.identity()));
        
        context.setPlatformItemSkus(platformItemSkus).setPlatformItemSkus1(relatedCombinationPisList)
                .setPlatformItemMap(platformItemMap);
        
        List<String> calculations = new ArrayList<>();
        context.setCalculations(calculations);
        
        List<InventoryAlloc> inventoryAllocs = inventoryAllocService.listBySkuCode(skuCode);
        context.setInventoryAllocs(inventoryAllocs);
        
        List<ShopSpecAllocableStockVO> shopSpecAllocableStocks =
                getShopSpecAllocableStocks(Collections.singletonList(skuCode), context.getInventoryAllocs());
        context.setShopSpecAllocableStocks(shopSpecAllocableStocks);
        
        BigDecimal lockStock = CollectionUtil.isEmpty(context.getInventoryAllocs()) ? BigDecimal.ZERO :
                new BigDecimal(context.getInventoryAllocs().stream().filter(v ->
                                v.getLockEnabled() && v.getEffectiveNum() != null).mapToInt(InventoryAlloc::getEffectiveNum)
                        .sum());
        context.setLockStock(lockStock);
        if (lockStock.compareTo(BigDecimal.ZERO) > 0) {
            log.debug("[库存分配][分配普通商品库存]商品SKU={}，存在锁定库存={}", skuCode, lockStock);
        }
        
        List<InventoryAlloc> newInventoryAllocs = new ArrayList<>();
        for (InventoryAllocShop InventoryAllocShop : context.getInventoryAllocShops()) {
            RegularItemAllocInventoryAllocShop regularItemAllocInventoryAllocShop =
                    new RegularItemAllocInventoryAllocShop(context, InventoryAllocShop);
            String shopNo = InventoryAllocShop.getShopNo();
            List<PlatformItemSku> shopOnSaleSkus = regularItemAllocInventoryAllocShop.getPlatformItemSkus();
            if (shopOnSaleSkus.isEmpty()) {
                log.trace("[库存分配][分配普通商品库存]商品SKU={}，店铺{}，未找到关联的平台商品SKU，跳过分配", skuCode,
                        shopNo);
                continue;
            }
            List<InventoryAlloc> inventoryAllocsForShop = allocStockByWeight(regularItemAllocInventoryAllocShop);
            newInventoryAllocs.addAll(inventoryAllocsForShop);
        }
        List<InventoryAlloc> inventoryAllocs0 =
                context.getInventoryAllocs().stream().filter(v -> StringUtil.isBlank(v.getSuiteNo()))
                        .collect(Collectors.toList());
        saveOrUpdateInventoryAllocs(inventoryAllocs0, newInventoryAllocs);
        return Mono.empty();
    }
    
    /**
     * 分配组合装商品库存
     */
    private Mono<Void> allocCombinationItemInventory(CombinationItemAllocContext context) {
        String skuCode = context.getSkuCode();
        List<InventoryAlloc> inventoryAllocs = inventoryAllocService.listBySkuCode(skuCode);
        Set<Long> inventoryAllocPlatformItemSkuIdsSet =
                inventoryAllocs.stream().map(InventoryAlloc::getPlatformItemSkuId).collect(Collectors.toSet());
        List<InventoryAlloc> inventoryAllocsWithDetail =
                inventoryAllocService.listByPlatformItemSkuId(inventoryAllocPlatformItemSkuIdsSet);
        context.setInventoryAllocs(inventoryAllocsWithDetail);
        CombinationItem combinationItem = context.getCombinationItem();
        List<ComposeSku> composeSkus = composeSkuService.selectByCombinationId(combinationItem.getId());
        context.setComposeSkus(composeSkus);
        List<String> composeSkuCodes = composeSkus.stream().map(ComposeSku::getSkuCode).collect(Collectors.toList());
        context.setComposeSkuCodes(composeSkuCodes);
        if (composeSkus.isEmpty()) {
            log.warn("[库存分配][分配组合装商品库存]商品SKU={}，组合装单品列表为空，跳过分配", skuCode);
            return Mono.empty();
        }
        
        // 查询组合装在售的平台商品SKU
        List<PlatformItemSku> platformItemSkus = queryOnSalePlatformItemSkus(skuCode);
        context.setPlatformItemSkus(platformItemSkus);
        if (platformItemSkus.isEmpty()) {
            log.trace("[库存分配][分配组合装商品库存]商品SKU={}，组合装在售的平台商品SKU列表为空，跳过分配", skuCode);
            return Mono.empty();
        }
        
        // 查询组合装套内单品作为独立链接在售的平台商品SKU信息
        List<PlatformItemSku> platformItemSkusForComposeSkus =
                platformItemSkuService.lambdaQuery().in(PlatformItemSku::getOuterSkuCode, composeSkuCodes)
                        .eq(PlatformItemSku::getStatus, PlatformItemSkuStatus.ON_SALE.getValue()).list();
        context.setPlatformItemSkus1(platformItemSkusForComposeSkus);
        
        // 查询包含当前组合装内单品的其他组合装
        Map<String, List<CombinationItem>> otherRelatedCombinationItems =
                combinationItemService.mapListBySkuCode(composeSkuCodes);
        List<String> otherRelatedCombinationItemCodes =
                otherRelatedCombinationItems.values().stream().flatMap(Collection::stream)
                        .filter(ci -> !Objects.equals(ci.getId(), combinationItem.getId()))
                        .map(CombinationItem::getCode)
                        .distinct()
                        .collect(Collectors.toList());
        List<PlatformItemSku> platformItemSkusForOtherRelatedCombination =
                otherRelatedCombinationItemCodes.isEmpty() ? Collections.emptyList() :
                        platformItemSkuService.lambdaQuery()
                                .in(PlatformItemSku::getOuterSkuCode, otherRelatedCombinationItemCodes)
                                .eq(PlatformItemSku::getStatus, PlatformItemSkuStatus.ON_SALE.getValue()).list();
        HashMap<String, List<PlatformItemSku>> platformItemSkus2 = new HashMap<>();
        otherRelatedCombinationItems.forEach((sc, items) -> {
            platformItemSkus2.put(sc, items.stream().filter(item -> !item.getCode().equals(skuCode)).flatMap(
                    item -> platformItemSkusForOtherRelatedCombination.stream()
                            .filter(pis -> pis.getOuterSkuCode().equals(item.getCode()))).collect(Collectors.toList()));
        });
        context.setPlatformItemSkus2(platformItemSkus2);
        
        Set<Long> allRelatedPlatformItemIds =
                Stream.of(platformItemSkus, platformItemSkusForComposeSkus, platformItemSkusForOtherRelatedCombination)
                        .flatMap(Collection::stream)
                        .map(PlatformItemSku::getPlatformItemId)
                        .collect(Collectors.toSet());
        
        List<PlatformItem> allRelatedPlatformItems = platformItemService.listByIds(allRelatedPlatformItemIds);
        setSyncEnabledByShopSetting(context.getInventoryAllocShops(), allRelatedPlatformItems);
        Map<Long, PlatformItem> platformItemMap =
                allRelatedPlatformItems.stream()
                        .collect(Collectors.toMap(PlatformItem::getId, Function.identity()));
        context.setPlatformItemMap(platformItemMap);
        
        List<ShopSpecAllocableStockVO> shopSpecAllocableStocks = getShopSpecAllocableStocks(composeSkuCodes);
        context.setShopSpecAllocableStocks(shopSpecAllocableStocks);
        
        List<InventoryAlloc> newInventoryAllocs = new ArrayList<>();
        for (InventoryAllocShop InventoryAllocShop : context.getInventoryAllocShops()) {
            CombinationItemAllocInventoryAllocShop allocInventoryAllocShop =
                    new CombinationItemAllocInventoryAllocShop(context, InventoryAllocShop);
            List<InventoryAlloc> shopAllocs = allocCombinationStockByWeight(allocInventoryAllocShop);
            newInventoryAllocs.addAll(shopAllocs);
        }
        
        saveOrUpdateInventoryAllocs(context.getInventoryAllocs(), newInventoryAllocs);
        return Mono.empty();
    }
    
    private static void setSyncEnabledByShopSetting(List<InventoryAllocShop> inventoryAllocShops,
                                                    Collection<PlatformItem> platformItems) {
        for (PlatformItem platformItem : platformItems) {
            for (InventoryAllocShop inventoryAllocShop : inventoryAllocShops) {
                if (platformItem.getShopNo().equals(inventoryAllocShop.getShopNo())) {
                    platformItem.setSyncEnabledByShopSetting(inventoryAllocShop);
                    break;
                }
            }
        }
    }
    
    private List<InventoryAlloc> allocStockByWeight(
            RegularItemAllocInventoryAllocShop context) {
        WeightCalculation weightCalculation = calculateSkuWeight(context);
        List<InventoryAlloc> inventoryAllocs = new ArrayList<>();
        BigDecimal shopAllocableStock =
                context.getShopSpecAllocableStocks().stream().map(ShopSpecAllocableStockVO::getAllocableStock)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
        ArrayList<String> calculations = new ArrayList<>();
        calculations.add("## 店铺可用库存计算");
        for (ShopSpecAllocableStockVO shopSpecAllocableStock : context.getShopSpecAllocableStocks()) {
            calculations.addAll(shopSpecAllocableStock.getCalculations());
        }
        calculations.add("## 平台商品库存分配");
        BigDecimal remainingShopStock = shopAllocableStock;
        PlatformItemSku lastSku = context.getPlatformItemSkus().get(context.getPlatformItemSkus().size() - 1);
        for (PlatformItemSku platformItemSku : context.getPlatformItemSkus()) {
            Optional<WeightCalculation.WeightItem> weightItemOptional = weightCalculation.get(platformItemSku.getId());
            if (!weightItemOptional.isPresent()) {
                continue;
            }
            WeightCalculation.WeightItem allocWeight = weightItemOptional.get();
            BigDecimal allocStock;
            List<String> contextCalculations = context.getContextCalculations();
            contextCalculations.addAll(calculations);
            if (shopAllocableStock.compareTo(BigDecimal.ZERO) <= 0) {
                allocStock = BigDecimal.ZERO;
                contextCalculations.add("平台商品分配库存 = 0");
            } else {
                if (platformItemSku == lastSku) {
                    allocStock = remainingShopStock;
                } else {
                    allocStock =
                            allocWeight.getPercent().multiply(shopAllocableStock).setScale(0, RoundingMode.HALF_UP);
                    remainingShopStock = remainingShopStock.subtract(allocStock);
                }
                contextCalculations.add(
                        String.format("平台商品分配库存 = 权重占比 * 可用库存 = (%s) * %s = %s", allocWeight,
                                shopAllocableStock, allocStock));
            }
            
            InventoryAlloc inventoryAlloc =
                    createInventoryAlloc(platformItemSku, allocStock, contextCalculations);
            inventoryAllocs.add(inventoryAlloc);
        }
        return inventoryAllocs;
    }
    
    private WeightCalculation calculateSkuWeight(RegularItemAllocInventoryAllocShop context) {
        Map<Long, PlatformItem> platformItemMap = context.getParentContext().getPlatformItemMap();
        List<PlatformItemSku> skuList =
                Stream.of(context.getPlatformItemSkus(), context.getPlatformItemSkus1())
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());
        WeightCalculation weightCalculation = new WeightCalculation();
        for (PlatformItemSku sku : skuList) {
            PlatformItem platformItem = platformItemMap.get(sku.getPlatformItemId());
            if (!platformItem.getSyncEnabled()) {
                log.debug("[库存分配][分配普通商品库存]平台商品 {} {} 未开启库存同步，跳过分配",
                        platformItem.getShopNo(),
                        platformItem.getId());
                continue;
            }
            if (platformItem.getLockEnabled()) {
                log.debug("[库存分配][分配普通商品库存]平台商品 {} {} 被锁定，跳过分配", platformItem.getShopNo(),
                        platformItem.getId());
                continue;
            }
            int inventoryAllocWeight = platformItemSyncConfig.getInventoryAllocWeight(platformItem);
            weightCalculation.add(sku.getId(), inventoryAllocWeight);
            log.trace("[库存分配][分配普通商品库存]平台商品 {} 权重 = {}", Arrays.toString(
                    new Object[]{platformItem.getShopNo(), platformItem.getId(), sku.getId(), sku.getOuterSkuCode(),
                            platformItem.getGoodsName()}), inventoryAllocWeight);
        }
        context.setWeightCalculation(weightCalculation);
        return weightCalculation;
    }
    
    // =================================================================
    // Combination Item Allocation Helpers
    // =================================================================
    
    private List<InventoryAlloc> allocCombinationStockByWeight(CombinationItemAllocInventoryAllocShop context) {
        List<ComposeSku> composeSkus = context.getParentContext().getComposeSkus();
        Map<Long, PlatformItem> platformItemMap = context.getParentContext().getPlatformItemMap();
        List<ShopSpecAllocableStockVO> shopSpecAllocableStocks = context.getShopSpecAllocableStocks();
        List<InventoryAlloc> inventoryAllocs = new ArrayList<>();
        List<PlatformItemSku> platformItemSkus = context.getPlatformItemSkus();
        for (PlatformItemSku platformItemSku : platformItemSkus) {
            String skuCode = platformItemSku.getOuterSkuCode();
            PlatformItem platformItem = platformItemMap.get(platformItemSku.getPlatformItemId());
            if (!platformItem.getSyncEnabled()) {
                log.debug("[库存分配][分配组合装商品库存]平台商品 {} {} 未开启库存同步，跳过分配",
                        platformItem.getShopNo(), platformItem.getId());
                continue;
            }
            if (platformItem.getLockEnabled()) {
                log.debug("[库存分配][分配组合装商品库存]平台商品 {} {} 被锁定，跳过分配", platformItem.getShopNo(),
                        platformItem.getId());
                continue;
            }
            BigDecimal pisCombinationAllocStock = BigDecimal.ZERO;
            WeightCalculation weightCalculation = new WeightCalculation();
            ArrayList<String> calculations = new ArrayList<>(context.getContextCalculations());
            calculations.add("## 组合装平台商品库存分配");
            for (ComposeSku composeSku : composeSkus) {
                String composeSkuCode = composeSku.getSkuCode();
                Stream.of(platformItemSkus, context.getPlatformItemSkus1(),
                                context.getPlatformItemSkus2().getOrDefault(composeSkuCode, Collections.emptyList()))
                        .flatMap(Collection::stream).distinct().collect(Collectors.toList()).forEach(pis -> {
                            PlatformItem thisPlatformItem = Objects.requireNonNull(platformItemMap.get(pis.getPlatformItemId()),
                                    "平台商品数据异常:" + pis.getPlatformItemId());
                            if (!thisPlatformItem.getLockEnabled() && thisPlatformItem.getSyncEnabled()) {
                                int inventoryAllocWeight = platformItemSyncConfig.getInventoryAllocWeight(thisPlatformItem);
                                weightCalculation.add(pis.getId(), inventoryAllocWeight);
                            }
                        });
                WeightCalculation.WeightItem weightItem = weightCalculation.get(platformItemSku.getId()).orElse(null);
                if (weightItem == null) {
                    continue;
                }
                BigDecimal shopSpecAllocableStockTotal =
                        shopSpecAllocableStocks.stream().filter(v -> v.getSkuCode().equals(composeSkuCode))
                                .map(ShopSpecAllocableStockVO::getAllocableStock)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal thisPisComposeSkuAvailableStock =
                        shopSpecAllocableStockTotal.multiply(weightItem.getPercent()).setScale(0, RoundingMode.DOWN);
                calculations.add(
                        String.format(
                                "组合装单品[%s]分配库存 = 权重占比 * 单品在当前店铺的可用库存（已减去锁定库存） = (%s) * %s = %s",
                                composeSkuCode, weightItem, shopSpecAllocableStockTotal,
                                thisPisComposeSkuAvailableStock));
                if (pisCombinationAllocStock.equals(BigDecimal.ZERO) ||
                        pisCombinationAllocStock.compareTo(thisPisComposeSkuAvailableStock) > 0) {
                    pisCombinationAllocStock = thisPisComposeSkuAvailableStock;
                }
            }
            calculations.add(String.format("组合装平台商品最终分配库存[%s] = MIN(组合装套内单品分配库存) = %s", skuCode,
                    pisCombinationAllocStock));
            InventoryAlloc inventoryAlloc =
                    createInventoryAlloc(platformItemSku, pisCombinationAllocStock, calculations);
            inventoryAllocs.add(inventoryAlloc);
            for (ComposeSku composeSku : composeSkus) {
                String composeSkuCode = composeSku.getSkuCode();
                BigDecimal composeSkuAllocStock =
                        pisCombinationAllocStock.multiply(new BigDecimal(composeSku.getCount()))
                                .setScale(0, RoundingMode.HALF_UP);
                List<String> composeSkuCalculations = new ArrayList<>();
                composeSkuCalculations.add(
                        String.format("组合装单品[%s]分配库存 = 组合装分配库存 * 套装包含的单品数量 = %s * %s = %s",
                                composeSkuCode, pisCombinationAllocStock, composeSku.getCount(),
                                composeSkuAllocStock));
                InventoryAlloc inventoryAlloc1 = createSuiteComposeSkuInventoryAlloc(
                        platformItemSku, composeSkuCode, composeSkuAllocStock, composeSkuCalculations);
                inventoryAllocs.add(inventoryAlloc1);
            }
        }
        return inventoryAllocs;
    }
    
    // =================================================================
    // Context Query
    // =================================================================
    
    private List<InventoryAllocShop> getInventoryAllocShops() {
        return inventoryAllocShopService.list();
    }
    
    private List<String> getAllOnSaleOuterSkuCodes(List<String> shopNos) {
        ListOuterSkuCodeQuery listOuterSkuCodeQuery = new ListOuterSkuCodeQuery();
        listOuterSkuCodeQuery.setPlatformItemSkuStatus(
                Lists.newArrayList(PlatformItemSkuStatus.ON_SALE.getValue()));
        listOuterSkuCodeQuery.setShopNos(shopNos);
        return platformItemSkuService.listOuterSkuCode(listOuterSkuCodeQuery);
    }
    
    private List<PlatformItemSku> queryOnSalePlatformItemSkus(String skuCode) {
        return platformItemSkuService
                .lambdaQuery()
                .eq(PlatformItemSku::getOuterSkuCode, skuCode)
                .eq(PlatformItemSku::getStatus, PlatformItemSkuStatus.ON_SALE.getValue())
                .list();
    }
    
    
    public List<ShopSpecAllocableStockVO> getShopSpecAllocableStocks(Collection<String> specNos) {
        if (CollectionUtil.isEmpty(specNos)) {
            return Collections.emptyList();
        }
        List<InventoryAlloc> inventoryAllocs =
                inventoryAllocService.lambdaQuery().in(InventoryAlloc::getSkuCode, specNos).list();
        return getShopSpecAllocableStocks(specNos, inventoryAllocs);
    }
    
    public List<ShopSpecAllocableStockVO> getShopSpecAllocableStocks(Collection<String> specNos,
                                                                     List<InventoryAlloc> inventoryAllocs) {
        List<AvailableStockSpecVO> availableStockSpecVOS = stockSpecBizService.getAvailableStocks(specNos);
        List<InventoryAllocShop> inventoryAllocShops = inventoryAllocShopService.lambdaQuery().list();
        BigDecimal totalInventoryWeights =
                new BigDecimal(inventoryAllocShops.stream().mapToInt(InventoryAllocShop::getInventoryWeight).sum());
        InventoryAllocShop lastInventoryAllocShop = inventoryAllocShops.get(inventoryAllocShops.size() - 1);
        ArrayList<ShopSpecAllocableStockVO> shopSpecAllocableStockVOS = new ArrayList<>();
        List<Long> platformItemIds =
                inventoryAllocs.stream().map(InventoryAlloc::getPlatformItemId).collect(Collectors.toList());
        List<PlatformItem> platformItems =
                platformItemIds.isEmpty() ? Collections.emptyList() : platformItemService.listByIds(platformItemIds);
        Map<Long, PlatformItem> platformItemMap =
                platformItems.stream().collect(Collectors.toMap(PlatformItem::getId, Function.identity()));
        for (String specNo : specNos) {
            BigDecimal totalAllocableStock = availableStockSpecVOS.stream()
                    .filter(v -> StrUtil.isNotBlank(v.getSkuCode()))
                    .filter(v -> v.getSkuCode().equals(specNo))
                    .map(AvailableStockSpecVO::getAllocableStock).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal lockedNum = CollectionUtil.isEmpty(inventoryAllocs) ? BigDecimal.ZERO :
                    new BigDecimal(inventoryAllocs.stream()
                            .filter(v -> {
                                PlatformItem platformItem = platformItemMap.get(v.getPlatformItemId());
                                return platformItem.getLockEnabled() && v.getEffectiveNum() != null &&
                                        specNo.equals(v.getSkuCode());
                            })
                            .mapToInt(InventoryAlloc::getEffectiveNum).sum());
            BigDecimal totalAllocableStockSubtractLocked = totalAllocableStock.subtract(lockedNum);
            BigDecimal remainStock = totalAllocableStock;
            for (InventoryAllocShop inventoryAllocShop : inventoryAllocShops) {
                ArrayList<String> calculations = new ArrayList<>();
                BigDecimal inventoryWeight = new BigDecimal(inventoryAllocShop.getInventoryWeight());
                BigDecimal shopAllocableStock =
                        totalAllocableStockSubtractLocked.divide(totalInventoryWeights, 6, RoundingMode.HALF_UP)
                                .multiply(inventoryWeight).setScale(0, RoundingMode.HALF_UP);
                if (lastInventoryAllocShop == inventoryAllocShop) {
                    shopAllocableStock = remainStock;
                } else {
                    remainStock = remainStock.subtract(shopAllocableStock);
                }
                calculations.add(
                        String.format(
                                "店铺[%s]: 可分配库存 = (总库存 - 锁定库存) * (店铺权重 / 总权重) = (%s - %s) * (%s / %s) = %s",
                                inventoryAllocShop.getShopNo(),
                                totalAllocableStock, lockedNum, inventoryWeight, totalInventoryWeights,
                                shopAllocableStock));
                ShopSpecAllocableStockVO shopSpecAllocableStockVO = new ShopSpecAllocableStockVO();
                shopSpecAllocableStockVO.setShopNo(inventoryAllocShop.getShopNo());
                shopSpecAllocableStockVO.setSkuCode(specNo);
                shopSpecAllocableStockVO.setAllocableStock(shopAllocableStock);
                shopSpecAllocableStockVO.setCalculations(calculations);
                shopSpecAllocableStockVOS.add(shopSpecAllocableStockVO);
            }
        }
        return shopSpecAllocableStockVOS;
    }
    
    // =================================================================
    // Utility Methods
    // =================================================================
    
    @Data
    private static class WeightCalculation {
        private Integer totalWeight = 0;
        private List<WeightItem> weights = new ArrayList<>();
        private WeightItem last;
        
        public WeightCalculation add(Long platformItemSkuId, Integer weight) {
            WeightItem weightItem = new WeightItem();
            weightItem.setPlatformItemSkuId(platformItemSkuId);
            weightItem.setWeight(weight);
            weights.add(weightItem);
            totalWeight += weight;
            last = weightItem;
            return this;
        }
        
        public Optional<WeightItem> get(Long platformItemSkuId) {
            return weights.stream().filter(v -> v.getPlatformItemSkuId().equals(platformItemSkuId)).findFirst();
        }
        
        @Data
        private class WeightItem {
            private Long platformItemSkuId;
            private Integer weight;
            
            @Override
            public String toString() {
                return String.format("%s / %s = %s", weight, totalWeight, getPercent().toPlainString());
            }
            
            public BigDecimal getPercent() {
                if (this == last) {
                    return BigDecimal.ONE.subtract(weights.stream().filter(v -> v != last).map(WeightItem::getPercent)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                }
                return new BigDecimal(weight).divide(new BigDecimal(totalWeight), 6, RoundingMode.HALF_UP);
            }
        }
    }
    
    private InventoryAlloc createInventoryAlloc(
            PlatformItemSku platformItemSku,
            BigDecimal allocStock,
            List<String> calculations) {
        InventoryAlloc inventoryAlloc = new InventoryAlloc();
        inventoryAlloc.setPlatformItemId(platformItemSku.getPlatformItemId());
        inventoryAlloc.setPlatformItemSkuId(platformItemSku.getId());
        inventoryAlloc.setShopNo(platformItemSku.getShopNo());
        inventoryAlloc.setSkuCode(platformItemSku.getOuterSkuCode());
        inventoryAlloc.setCalcNum(allocStock.intValue());
        inventoryAlloc.setEffectiveNum(allocStock.intValue());
        inventoryAlloc.setLastAllocTime(DateUtil.currentTime());
        inventoryAlloc.setCalculation(String.join(";", calculations));
        log.trace(
                "[库存分配]库存分配记录: platformItemId={}, platformItemSkuId={}, shopNo={}, skuCode={}, allocStock={}, calculations={}",
                inventoryAlloc.getPlatformItemId(), inventoryAlloc.getPlatformItemSkuId(), inventoryAlloc.getShopNo(),
                inventoryAlloc.getSkuCode(),
                allocStock, String.join("; ", calculations));
        return inventoryAlloc;
    }
    
    private InventoryAlloc createSuiteComposeSkuInventoryAlloc(
            PlatformItemSku platformItemSku,
            String composeSkuCode,
            BigDecimal allocStock,
            List<String> calculations) {
        InventoryAlloc inventoryAlloc = new InventoryAlloc();
        inventoryAlloc.setPlatformItemId(platformItemSku.getPlatformItemId());
        inventoryAlloc.setPlatformItemSkuId(platformItemSku.getId());
        inventoryAlloc.setShopNo(platformItemSku.getShopNo());
        inventoryAlloc.setSkuCode(composeSkuCode);
        inventoryAlloc.setSuiteNo(platformItemSku.getOuterSkuCode());
        inventoryAlloc.setCalcNum(allocStock.intValue());
        inventoryAlloc.setEffectiveNum(allocStock.intValue());
        inventoryAlloc.setLastAllocTime(DateUtil.currentTime());
        inventoryAlloc.setCalculation(String.join("; ", calculations));
        log.trace(
                "[库存分配]库存分配记录: platformItemId={}, platformItemSkuId={}, shopNo={}, skuCode={}, suiteNo={}, allocStock={}, calculations={}",
                inventoryAlloc.getPlatformItemId(), inventoryAlloc.getPlatformItemSkuId(), inventoryAlloc.getShopNo(),
                inventoryAlloc.getSkuCode(), inventoryAlloc.getSuiteNo(),
                allocStock, String.join(";", calculations));
        
        return inventoryAlloc;
    }
    
    @Value
    public static class InventoryAllocKey {
        /**
         * 平台商品SkuID
         */
        Long platformItemSkuId;
        /**
         * SKU编码
         */
        String skuCode;
        /**
         * 组合装编码
         */
        String suiteNo;
        
        public static InventoryAllocKey of(InventoryAlloc inventoryAlloc) {
            return new InventoryAllocKey(inventoryAlloc.getPlatformItemSkuId(), inventoryAlloc.getSkuCode(),
                    inventoryAlloc.getSuiteNo() == null ? "" : inventoryAlloc.getSuiteNo());
        }
    }
    
    private void saveOrUpdateInventoryAllocs(List<InventoryAlloc> inventoryAllocs0,
                                             List<InventoryAlloc> inventoryAllocs) {
        Map<InventoryAllocKey, InventoryAlloc> existsMap =
                inventoryAllocs0.stream().collect(Collectors.toMap(InventoryAllocKey::of, Function.identity()));
        List<InventoryAlloc> toUpdate = new ArrayList<>();
        List<InventoryAlloc> toSave = new ArrayList<>();
        List<InventoryAlloc> toRemove = new ArrayList<>();
        for (InventoryAlloc inventoryAlloc : inventoryAllocs) {
            InventoryAllocKey key = InventoryAllocKey.of(inventoryAlloc);
            InventoryAlloc existsOne = existsMap.get(key);
            if (existsOne != null) {
                existsOne.setLastAllocTime(inventoryAlloc.getLastAllocTime());
                existsOne.setCalcNum(inventoryAlloc.getCalcNum());
                existsOne.setEffectiveNum(inventoryAlloc.getEffectiveNum());
                existsOne.setCalculation(inventoryAlloc.getCalculation());
                existsOne.setSkuCode(inventoryAlloc.getSkuCode());
                existsOne.setSuiteNo(inventoryAlloc.getSuiteNo());
                toUpdate.add(existsOne);
            } else {
                toSave.add(inventoryAlloc);
            }
        }
        Map<InventoryAllocKey, InventoryAlloc> newMap =
                inventoryAllocs.stream().collect(Collectors.toMap(InventoryAllocKey::of, Function.identity()));
        List<Long> platformItemIds0 = inventoryAllocs0.stream().map(InventoryAlloc::getPlatformItemId).distinct()
                .collect(Collectors.toList());
        List<PlatformItem> platformItems0 =
                platformItemIds0.isEmpty() ? Collections.emptyList() : platformItemService.listByIds(platformItemIds0);
        Map<Long, PlatformItem> platformItem0Map =
                platformItems0.stream().collect(Collectors.toMap(PlatformItem::getId, Function.identity()));
        inventoryAllocShopService.setPlatformItemListSyncEnabled(platformItems0);
        for (InventoryAlloc inventoryAlloc : inventoryAllocs0) {
            PlatformItem platformItem = platformItem0Map.get(inventoryAlloc.getPlatformItemId());
            if (!newMap.containsKey(InventoryAllocKey.of(inventoryAlloc))) {
                if (platformItem.getLockEnabled()) {
                    continue;
                }
                if (!platformItem.getSyncEnabled()) {
                    continue;
                }
                toRemove.add(inventoryAlloc);
            }
        }
        if (!toUpdate.isEmpty()) {
            inventoryAllocService.updateBatchById(toUpdate);
            log.info("[库存分配]更新库存分配记录: {}",
                    toUpdate.stream().map(InventoryAlloc::getId).collect(Collectors.toList()));
        }
        if (!toSave.isEmpty()) {
            inventoryAllocService.saveBatch(toSave);
            log.info("[库存分配]新增库存分配记录: {}", toSave.size());
        }
        if (!toRemove.isEmpty()) {
            inventoryAllocService.removeByIdsWithTime(
                    toRemove.stream().map(InventoryAlloc::getId).collect(Collectors.toList()));
            log.info("[库存分配]删除库存分配记录: {}",
                    toRemove.stream().map(InventoryAlloc::getId).collect(Collectors.toList()));
        }
        
        Set<Long> platformItemIds =
                inventoryAllocs.stream().map(InventoryAlloc::getPlatformItemId).collect(Collectors.toSet());
        platformItemService.updateLastAllocTime(platformItemIds, DateUtil.currentTime());
    }
    
    
    // =================================================================
    // Inner Data Classes
    // =================================================================
    
    /**
     * 组合装库存分配上下文
     */
    @Data
    @Accessors(chain = true)
    private static class CombinationItemAllocContext {
        /**
         * 本次分配执行时已存在的库存分配记录
         */
        private List<InventoryAlloc> inventoryAllocs;
        /**
         * 组合装编码
         */
        private String skuCode;
        /**
         * 店铺权重信息
         */
        private List<InventoryAllocShop> InventoryAllocShops;
        /**
         * 组合装
         */
        private CombinationItem combinationItem;
        /**
         * 当前组合装内包含的单品
         */
        private List<ComposeSku> composeSkus;
        /**
         * 当前组合装内包含的单品编码
         */
        private List<String> composeSkuCodes;
        /**
         * 当前组合装作为独立链接在售的SKU
         */
        private List<PlatformItemSku> platformItemSkus;
        /**
         * 当前组合装内单品作为独立链接在售的SKU
         */
        private List<PlatformItemSku> platformItemSkus1;
        /**
         * 当前组合装内单品被包含在其他组合装内进行销售的SKU
         */
        private Map<String, List<PlatformItemSku>> platformItemSkus2;
        /**
         * 上述所有平台商品的映射
         */
        private Map<Long, PlatformItem> platformItemMap;
        /**
         * 店铺可分配库存信息
         */
        private List<ShopSpecAllocableStockVO> shopSpecAllocableStocks;
        /**
         * 可分配库存计算过程
         */
        private List<String> calculations;
    }
    
    /**
     * 组合装库存分配上下文
     */
    @Getter
    @Accessors(chain = true)
    private static class CombinationItemAllocInventoryAllocShop {
        /**
         * 父级上下文
         */
        private final CombinationItemAllocContext parentContext;
        /**
         * 当前店铺权重信息
         */
        private final InventoryAllocShop currentInventoryAllocShop;
        /**
         * 当前组合装作为独立链接在售的SKU
         */
        private final List<PlatformItemSku> platformItemSkus;
        /**
         * 当前组合装内单品作为独立链接在售的SKU
         */
        private final List<PlatformItemSku> platformItemSkus1;
        /**
         * 当前组合装内单品被包含在其他组合装内进行销售的SKU
         */
        private final Map<String, List<PlatformItemSku>> platformItemSkus2;
        /**
         * 可分配库存计算过程
         */
        private @Setter List<String> calculations;
        /**
         * 店铺可分配库存信息
         */
        private final List<ShopSpecAllocableStockVO> shopSpecAllocableStocks;
        
        public CombinationItemAllocInventoryAllocShop(CombinationItemAllocContext parentContext,
                                                      InventoryAllocShop currentInventoryAllocShop) {
            this.parentContext = parentContext;
            this.currentInventoryAllocShop = currentInventoryAllocShop;
            this.platformItemSkus = parentContext.platformItemSkus.stream()
                    .filter(sku -> sku.getShopNo().equals(currentInventoryAllocShop.getShopNo()))
                    .collect(Collectors.toList());
            this.platformItemSkus1 = parentContext.platformItemSkus1.stream()
                    .filter(sku -> sku.getShopNo().equals(currentInventoryAllocShop.getShopNo()))
                    .collect(Collectors.toList());
            this.platformItemSkus2 = new HashMap<>();
            parentContext.platformItemSkus2.forEach((k, v) -> this.platformItemSkus2.put(k,
                    v.stream().filter(sku -> sku.getShopNo().equals(currentInventoryAllocShop.getShopNo()))
                            .collect(Collectors.toList())));
            this.shopSpecAllocableStocks = parentContext.shopSpecAllocableStocks.stream()
                    .filter(v -> v.getShopNo().equals(currentInventoryAllocShop.getShopNo()))
                    .collect(Collectors.toList());
        }
        
        public List<String> getContextCalculations() {
            ArrayList<String> contextCalculations = new ArrayList<>();
            if (parentContext != null && CollectionUtil.isNotEmpty(parentContext.getCalculations())) {
                contextCalculations.addAll(parentContext.getCalculations());
            }
            if (this.calculations != null) {
                contextCalculations.addAll(this.calculations);
            }
            return contextCalculations;
        }
    }
    
    /**
     * 普通商品库存分配上下文
     */
    @Data
    @Accessors(chain = true)
    private static class RegularItemAllocContext {
        /**
         * 本次分配执行时已存在的库存分配记录
         */
        private List<InventoryAlloc> inventoryAllocs;
        /**
         * 商品SKU编码
         */
        private String skuCode;
        /**
         * 店铺权重信息
         */
        private List<InventoryAllocShop> InventoryAllocShops;
        /**
         * 普通商品在售的平台商品SKU
         */
        private List<PlatformItemSku> platformItemSkus;
        /**
         * 包含此商品SKU的组合装在售的平台商品SKU
         */
        private List<PlatformItemSku> platformItemSkus1;
        /**
         * 平台商品映射
         */
        private Map<Long, PlatformItem> platformItemMap;
        /**
         * 店铺可分配库存信息
         */
        private List<ShopSpecAllocableStockVO> shopSpecAllocableStocks;
        /**
         * 锁定库存
         */
        private BigDecimal lockStock;
        /**
         * 可分配库存计算过程
         */
        private List<String> calculations;
    }
    
    /**
     * 普通商品库存分配上下文
     */
    @Data
    @Accessors(chain = true)
    private static class RegularItemAllocInventoryAllocShop {
        private RegularItemAllocContext parentContext;
        private InventoryAllocShop inventoryAllocShop;
        /**
         * 普通商品在售的平台商品SKU
         */
        private List<PlatformItemSku> platformItemSkus;
        /**
         * 包含此商品SKU的组合装在售的平台商品SKU
         */
        private List<PlatformItemSku> platformItemSkus1;
        /**
         * 店铺可分配库存信息
         */
        private List<ShopSpecAllocableStockVO> shopSpecAllocableStocks;
        /**
         * 店铺可分配库存计算过程
         */
        private List<String> calculations;
        /**
         * 计算平台商品规格权重分配
         */
        private WeightCalculation weightCalculation;
        
        public RegularItemAllocInventoryAllocShop(RegularItemAllocContext parentContext,
                                                  InventoryAllocShop inventoryAllocShop) {
            this.parentContext = parentContext;
            this.inventoryAllocShop = inventoryAllocShop;
            this.platformItemSkus = parentContext.platformItemSkus.stream()
                    .filter(sku -> sku.getShopNo().equals(inventoryAllocShop.getShopNo()))
                    .collect(Collectors.toList());
            this.platformItemSkus1 = parentContext.platformItemSkus1.stream()
                    .filter(sku -> sku.getShopNo().equals(inventoryAllocShop.getShopNo()))
                    .collect(Collectors.toList());
            this.shopSpecAllocableStocks = parentContext.shopSpecAllocableStocks.stream()
                    .filter(v -> v.getShopNo().equals(inventoryAllocShop.getShopNo()))
                    .collect(Collectors.toList());
        }
        
        public List<String> getContextCalculations() {
            ArrayList<String> contextCalculations = new ArrayList<>();
            if (parentContext != null) {
                contextCalculations.addAll(parentContext.getCalculations());
            }
            if (this.calculations != null) {
                contextCalculations.addAll(this.calculations);
            }
            return contextCalculations;
        }
    }
}
