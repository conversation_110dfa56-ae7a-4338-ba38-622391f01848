package com.daddylab.supplier.item.application.afterSaleLogistics;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSaleLogistics.dto.AbnormalScanCmd;

/**
 * <AUTHOR>
 * @since 2024/12/25
 */
public interface AbnormalScanService {

    /**
     * 异常扫描
     *
     * @param cmd 异常扫描命令
     * @return 响应(异步任务ID)
     */
    SingleResponse<String> abnormalScan(AbnormalScanCmd cmd);
}
