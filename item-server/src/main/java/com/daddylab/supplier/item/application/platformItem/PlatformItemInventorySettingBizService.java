package com.daddylab.supplier.item.application.platformItem;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.platformItem.data.PlatformItemDetail;
import com.daddylab.supplier.item.application.platformItem.data.PlatformItemInventorySettingVO;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSkuInventorySetting;
import com.daddylab.supplier.item.types.platformItem.AutoInventoryRatioCmd;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/15
 */
@Deprecated
public interface PlatformItemInventorySettingBizService {
    PlatformItemInventorySettingVO getPlatformItemInventorySettingVO(Platform platform, String shopNo, String outerItemId);

    List<PlatformItemSkuInventorySetting> getPlatformItemSkuInventorySettings(Platform platform, String shopNo, String outerItemId);

    void syncInventorySettingViewObject(PlatformItemDetail viewObject);

    SingleResponse<Integer> autoInventoryRatio(AutoInventoryRatioCmd cmd);
}

