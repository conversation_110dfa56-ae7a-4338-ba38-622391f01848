package com.daddylab.supplier.item.application.item.itemRunning.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName ItemRunningVo.java
 * @description
 * @createTime 2022年04月29日 16:59:00
 */
@Data
@ApiModel("运营信息")
public class ItemRunningVo {

    @ApiModelProperty("平台商品id类型 0:小程序Id 1:会员店Id 2:美妆店Id 3:母婴店Id 4:其他店铺id")
    private Integer type;

    @ApiModelProperty("平台商品id")
    private String platformItemId;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("产品标准名")
    private String standardName;

    @ApiModelProperty("卖点文案")
    private String sellingPoints;

}
