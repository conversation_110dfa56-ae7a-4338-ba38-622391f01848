package com.daddylab.supplier.item.domain.otherPay.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName OrderPayStatus.java
 * @description 其他应付订单状态枚举
 * @createTime 2022年03月25日 16:14:00
 */
@Getter
@AllArgsConstructor
public enum OtherPayStatus implements IEnum<Integer> {
    FINISH(0, "已完成"),
    NO_COMMIT(1, "待提交"),
    WAIT_AUDIT(2, "待审核"),
    REFUSE(3, "已拒绝"),
    REVOCATION(4, "已撤回");
    final public Integer value;
    final public String desc;

    OtherPayStatus(Integer value, String name, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 获取枚举值
     *
     * @return
     */
    public static OtherPayStatus of(Integer value) {
        for (OtherPayStatus otherPayStatus : OtherPayStatus.values()) {
            if (otherPayStatus.getValue().equals(value)) {
                return otherPayStatus;
            }
        }
        return null;
    }

    public static boolean isEdit(Integer state) {
        return state.equals(OtherPayStatus.FINISH.getValue())
                || state.equals(OtherPayStatus.WAIT_AUDIT.getValue());
    }
}
