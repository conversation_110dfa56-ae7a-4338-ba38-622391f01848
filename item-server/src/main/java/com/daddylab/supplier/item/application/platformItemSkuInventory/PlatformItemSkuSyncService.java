package com.daddylab.supplier.item.application.platformItemSkuInventory;

import com.daddylab.supplier.item.application.platformItemSkuInventory.domain.dto.PlatformItemChangeEvent;
import com.daddylab.supplier.item.domain.common.enums.Platform;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @class PlatformItemSyncService.java
 * @description 平台商品同步服务类
 * @date 2024-03-14 09:11
 */
public interface PlatformItemSkuSyncService {

    /**
     * 全量同步(初始化数据)
     *
     * @date 2024/3/14 09:21
     * <AUTHOR>
     */
    void fullDoseSync();

    /**
     * 增量同步（事件监听）
     *
     * @param events List<PlatformItemSkuInventory>
     * @date 2024/3/14 14:36
     * <AUTHOR>
     */
    void incrementSync(List<PlatformItemChangeEvent> events);

    /**
     * 增量同步（时间范围）
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    void incrementSync(LocalDateTime startTime, LocalDateTime endTime) throws UnsupportedOperationException;

    /**
     * 指定平台商品ID，同步单个商品
     * @param outerItemId 平台商品ID
     */
    boolean syncItem(String shopNo, String outerItemId);
    
    /**
     * 指定平台商品SKU ID，同步单个SKU
     * @param outerSkuId 平台商品SKU ID
     */
    boolean syncSku(String shopNo, String outerSkuId);

    Platform defaultType();
}
