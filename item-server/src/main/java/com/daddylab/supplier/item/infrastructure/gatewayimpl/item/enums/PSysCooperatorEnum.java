package com.daddylab.supplier.item.infrastructure.gatewayimpl.item.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ItemCooperatorAll             = 0 // 电商
 * ItemCooperatorSupplier        = 1 // 电商
 * ItemCooperatorGreenHome       = 2 // 绿色家装
 * ItemCooperatorDadCheck        = 4 // 老爸抽检
 * ItemCooperatorEvaluateScience = 8 // 评测科普
 *
 * <AUTHOR>
 * @since 2025/2/17
 */
@AllArgsConstructor
@Getter
public enum PSysCooperatorEnum implements IIntegerEnum {
    ItemCooperatorAll(0, "全部"),
    ItemCooperatorSupplier(1, "电商"),
    ItemCooperatorGreenHome(2, "绿色家装"),
    ItemCooperatorDadCheck(4, "老爸抽检"),
    ItemCooperatorEvaluateScience(8, "评测科普");
    private final Integer value;
    private final String desc;

    public static PSysCooperatorEnum valueOf(Integer value) {
        for (PSysCooperatorEnum item : PSysCooperatorEnum.values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
}
