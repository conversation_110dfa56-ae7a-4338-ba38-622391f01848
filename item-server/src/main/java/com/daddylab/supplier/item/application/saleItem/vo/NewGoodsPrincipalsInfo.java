package com.daddylab.supplier.item.application.saleItem.vo;

import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/** 新品商品负责人信息 */
@Data
@ApiModel("新品商品返回实体")
public class NewGoodsPrincipalsInfo implements Serializable {
    private static final long serialVersionUID = -1200415708549781152L;
    @ApiModelProperty(value = "商品ID")
    private Long itemId = 0L;

    @ApiModelProperty(value = "采购负责人信息")
    private List<StaffBrief> buyerUsers = Collections.emptyList();

    @ApiModelProperty(value = "产品负责人信息")
    private List<StaffBrief> principalUsers = Collections.emptyList();

    @ApiModelProperty(value = "QC负责人")
    private List<StaffBrief> qcUsers = Collections.emptyList();

    @ApiModelProperty(value = "法务负责人信息")
    private List<StaffBrief> legalUsers = Collections.emptyList();

    @ApiModelProperty("新品商品数量")
    private Integer newGoodsNum = 0;

    public static NewGoodsPrincipalsInfo empty() {
        return new NewGoodsPrincipalsInfo();
    }
}
