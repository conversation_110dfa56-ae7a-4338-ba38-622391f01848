package com.daddylab.supplier.item.infrastructure.third.redbook;

import com.xiaohongshu.fls.opensdk.entity.oauth.response.GetAccessTokenResponse;
import com.xiaohongshu.fls.opensdk.entity.oauth.response.RefreshTokenResponse;

/**
 * <AUTHOR>
 * @since 2024/4/9
 */
public interface RedBookOAuthService {

    String getAuthorizationUrl(String redirectUri, String state);

    GetAccessTokenResponse getAccessToken(String code);

    RefreshTokenResponse refreshToken(String refreshToken);

}
