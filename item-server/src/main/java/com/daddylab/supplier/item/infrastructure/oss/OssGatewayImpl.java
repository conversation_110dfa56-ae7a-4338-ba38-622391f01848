package com.daddylab.supplier.item.infrastructure.oss;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.*;
import com.daddylab.supplier.item.infrastructure.oss.models.OssProcess;
import com.daddylab.supplier.item.infrastructure.oss.models.SaveAs;
import com.daddylab.supplier.item.infrastructure.oss.models.VideoSnapshots;
import lombok.NonNull;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/10/11
 */
@Service
public class OssGatewayImpl implements OssGateway {
    private final OssConfig ossConfig;

    public OssGatewayImpl(OssConfig ossConfig) {
        this.ossConfig = ossConfig;
    }

    @Override
    public String put(boolean isPrivate, String path, InputStream input) {
        final OSS ossClient = ossConfig.getOssClient();
        final String bucketName = getBucketName(isPrivate);
        final String objectPath = normalizeObjectPath(path, true);
        ossClient.putObject(bucketName, objectPath, input);
        return getObjectURL(isPrivate, objectPath);
    }

    private String getObjectURL(boolean isPrivate, String path) {
        return URLUtil.encode(
                isPrivate ? ossConfig.getPrivateUrl() + path : ossConfig.getPublicUrl() + path);
    }

    @Override
    public ObjectMetadata metadata(boolean isPrivate, String path) {
        return ossConfig
                .getOssClient()
                .getObjectMetadata(getBucketName(isPrivate), normalizeObjectPath(path, false));
    }

    @Override
    public Map<String, String> imageInfo(boolean isPrivate, String path) {
        final GetObjectRequest getObjectRequest =
                new GetObjectRequest(getBucketName(isPrivate), normalizeObjectPath(path, false));
        getObjectRequest.setProcess("image/info");
        final OSSObject ossObject = ossConfig.getOssClient().getObject(getObjectRequest);
        final String content = IoUtil.read(ossObject.getObjectContent(), StandardCharsets.UTF_8);
        final JSONObject contentJsonObject = JSON.parseObject(content);
        final HashMap<String, String> imageInfo = new HashMap<>();
        for (Map.Entry<String, Object> entry : contentJsonObject.entrySet()) {
            if (entry.getValue() instanceof JSONObject) {
                final JSONObject value = (JSONObject) entry.getValue();
                imageInfo.put(entry.getKey(), value.getString("value"));
            }
        }
        return imageInfo;
    }

    @Override
    public void delete(boolean isPrivate, String path) {
        ossConfig
                .getOssClient()
                .deleteObject(getBucketName(isPrivate), normalizeObjectPath(path, false));
    }

    @Override
    public String generatePresignedUrl(boolean isPrivate, String path) {
        return generatePresignedUrl(
                isPrivate,
                normalizeObjectPath(path, false),
                ossConfig.getDefaultSignExpireSeconds());
    }

    @Override
    public String generatePresignedUrl(boolean isPrivate, String path, int expireSeconds) {
        return generatePresignedUrl(
                isPrivate,
                normalizeObjectPath(path, false),
                ossConfig.getDefaultSignExpireSeconds(), null);
    }

    @Override
    public String generatePresignedUrl(boolean isPrivate, String path, int expireSeconds, String responseFileName) {
        final GeneratePresignedUrlRequest generatePresignedUrlRequest =
                new GeneratePresignedUrlRequest(
                        getBucketName(isPrivate), normalizeObjectPath(path, false), HttpMethod.GET);
        if (StrUtil.isNotBlank(responseFileName)) {
            final ResponseHeaderOverrides responseHeaders = new ResponseHeaderOverrides();
            final String contentDisposition = String.format("attachment; filename=\"%s\"", responseFileName);
            responseHeaders.setContentDisposition(contentDisposition);
            generatePresignedUrlRequest.setResponseHeaders(responseHeaders);
        }
        generatePresignedUrlRequest.setExpiration(getExpireDate(expireSeconds));
        return ossConfig
                .getOssClient()
                .generatePresignedUrl(generatePresignedUrlRequest)
                .toString();
    }

    @Override
    public void videoSnapshots(boolean isPrivate, String path, String saveAs) {
        videoSnapshots(
                isPrivate,
                path,
                VideoSnapshots.builder().build(),
                SaveAs.builder().o(saveAs).build());
    }

    @Override
    public void videoSnapshots(
            boolean isPrivate, String path, VideoSnapshots videoSnapshots, SaveAs saveAs) {
        final String bucketName = getBucketName(isPrivate);
        final OssProcess process = OssProcess.command(videoSnapshots).and(saveAs);
        final String key = normalizeObjectPath(path, false);
        final AsyncProcessObjectRequest asyncProcessObjectRequest =
                new AsyncProcessObjectRequest(bucketName, key, process.toString());
        ossConfig.getOssClient().asyncProcessObject(asyncProcessObjectRequest);
    }

    @NonNull
    private static DateTime getExpireDate(int expireSeconds) {
        return DateUtil.date(LocalDateTime.now().plusSeconds(expireSeconds));
    }

    @Override
    public String sign(Map<String, String> params) {
        final OSS ossClient = ossConfig.getOssClient();
        final PolicyConditions conds = new PolicyConditions();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            conds.addConditionItem(entry.getKey(), entry.getValue());
        }
        final String postPolicy =
                ossClient.generatePostPolicy(
                        getExpireDate(ossConfig.getDefaultSignExpireSeconds()), conds);
        return ossClient.calculatePostSignature(postPolicy);
    }

    @NonNull
    private String normalizeObjectPath(String path, boolean prefix) {
        if (path.startsWith("http")) {
            path = URLUtil.getPath(path);
        }
        String objectPath = prefix ? ossConfig.getPrefixDir() + path : path;
        objectPath = removeRedundantSlash(objectPath);
        if (objectPath.startsWith("/")) {
            objectPath = objectPath.substring(1);
        }
        return objectPath;
    }

    @NonNull
    private static String removeRedundantSlash(String objectPath) {
        return objectPath.replaceAll("/+", "/");
    }

    private String getBucketName(boolean isPrivate) {
        return isPrivate ? ossConfig.getPrivateBucket() : ossConfig.getPublicBucket();
    }

//    @Override
//    public String getPrivateFileDownloadUrl(String url) {
//        String s = url.replaceAll(ossConfig.getPrivateUrl(), "");
//        return generatePresignedUrl(true, s);
//    }
}
