package com.daddylab.supplier.item.application.wiki;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/7/24
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wiki")
@RefreshScope
public class WikiProperties {
    private String token = "Bearer OTQ4MDkwMTc1OTc4Oi85h7YayUzOnBcF0M8Nz3Cv46W+";
}
