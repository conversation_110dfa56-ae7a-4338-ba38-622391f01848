package com.daddylab.supplier.item.application.order;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.apiv2.sales.TradeQueryAPI;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailResponse;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import java.time.Duration;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/5/4
 */
@Component
@Slf4j
public class WdtOrderFetcher implements PageFetcher {

    private final WdtGateway wdtGateway;
    private final WdtOrderDataSyncService wdtOrderDataSyncService;
    private final RefreshConfig refreshConfig;

    public WdtOrderFetcher(WdtGateway wdtGateway,
            WdtOrderDataSyncService wdtOrderDataSyncService,
            RefreshConfig refreshConfig) {
        this.wdtGateway = wdtGateway;
        this.wdtOrderDataSyncService = wdtOrderDataSyncService;
        this.refreshConfig = refreshConfig;
    }

    @Override
    public int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
        return query(startTime, endTime, 1, 1, true).getTotalCount();
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, long pageIndex, long pageSize,
            RunContext runContext) {
        final Duration duration = LocalDateTimeUtil.between(startTime, endTime);
        if (duration.isNegative() || duration.isZero()
                || duration.compareTo(Duration.ofHours(1)) > 0) {
            throw new IllegalArgumentException("拉取时间最大范围为一个小时");
        }
        final TradeQueryQueryWithDetailResponse response = query(startTime, endTime,
                ((int) pageSize),
                ((int) pageIndex), false);
        handleResponse(response);
    }


    private TradeQueryQueryWithDetailResponse query(
            LocalDateTime startTime, LocalDateTime endTime, int pageSize, int pageNo,
            boolean calcTotal) {
        try {
            final Pager pager = new Pager(pageSize, pageNo - 1, calcTotal);
            final TradeQueryAPI api;
            if (refreshConfig.getFetchWdtOrderByQimen()) {
                api = wdtGateway.getQimenAPI(TradeQueryAPI.class);
            } else {
                api = wdtGateway.getAPI(TradeQueryAPI.class);
            }
            final TradeQueryQueryWithDetailParams params = new TradeQueryQueryWithDetailParams();
            params.setStartTime(DateUtil.format(startTime));
            params.setEndTime(DateUtil.format(endTime));
            return api.queryWithDetail(params, pager);
        } catch (Throwable e) {
            throw WdtExceptions.wrapFetchException(e, fetchDataType());
        }
    }

    private void handleResponse(TradeQueryQueryWithDetailResponse response) {
        wdtOrderDataSyncService.saveOrUpdateBatchByApiQueryResponse(response);
    }

    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.WDT_ORDER;
    }
}
