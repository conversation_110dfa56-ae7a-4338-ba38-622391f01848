package com.daddylab.supplier.item.application.reason;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Reason;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IReasonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @ClassName ReasonBizServiceImpl.java
 * @description
 * @createTime 2022年05月16日 16:55:00
 */
@Slf4j
@Service
public class ReasonBizServiceImpl implements ReasonBizService{
    @Autowired
    private IReasonService reasonService;

    @Override
    public Reason getById(Long id) {
        Assert.notNull(id, "原因管理Id入参不得为空");
        return reasonService.getById(id);
    }
}
