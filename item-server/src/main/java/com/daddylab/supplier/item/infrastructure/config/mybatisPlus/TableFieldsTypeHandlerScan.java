package com.daddylab.supplier.item.infrastructure.config.mybatisPlus;

import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * 扫描指定命名空间下的实体类，将指定了 typeHandler 的字段，自动注册到 Mybatis Configuration type register
 * <AUTHOR>
 * @since 2023/11/7
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
@Import(TypeHandlerRegistrar.class)
public @interface TableFieldsTypeHandlerScan {
    String[] value() default {};
}
