package com.daddylab.supplier.item.application.category;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/9
 */
@Configuration
@Data
@ConfigurationProperties(prefix = "category-extend")
@RefreshScope
public class CategoryExtendConfig {
    /** 类目拓展属性 */
    public Map<String, List<String>> getProps() {
        return configs.stream().collect(Collectors.toMap(Extend::getName, Extend::getProps));
    }

    private List<Extend> configs;

    @Data
    public static class Extend {
        String name;
        List<String> props = Collections.emptyList();
    }
}
