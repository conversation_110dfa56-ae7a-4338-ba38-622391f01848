package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndDetailQuery;
import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetailQuery;
import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndOrderDetailSheet;
import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.StockInOrderDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.StockInOrderQueryDto;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrderDetail;
import com.daddylab.supplier.item.types.stockInOrder.StockInOrderDetailApplyQuantity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 采购入库mapper
 *
 * <AUTHOR>
 * @date 2022/3/24 17:37
 **/
@Repository
public interface StockInOrderMapper extends DaddyBaseMapper<StockInOrder> {
    /**
     * 根据单号查询入库单id
     *
     * @param no
     * @return int
     * <AUTHOR>
     * @date 2022/3/30 18:46
     **/
    long selectByNo(String no);

    /**
     * 查询入库单总数
     *
     * @param stockInOrderQuery
     * @return int
     * <AUTHOR>
     * @date 2022/3/30 18:46
     **/
    int queryCount(StockInOrderQuery stockInOrderQuery);

    /**
     * 查询入库单列表
     *
     * @param stockInOrderQuery
     * @return java.util.List<com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder>
     * <AUTHOR>
     * @date 2022/3/30 18:47
     **/
    List<StockInOrder> queryStockInOrderList(StockInOrderQuery stockInOrderQuery);

    List<StockInOrderQueryDto> queryStockInOrderList2(StockInOrderQuery stockInOrderQuery);
    Integer countStockInOrderList2(StockInOrderQuery stockInOrderQuery);

    /**
     * 根据id更新采购入库单删除状态
     *
     * @param id
     * @return int
     * <AUTHOR>
     * @date 2022/4/1 10:27
     **/
    int updateByStockInOrderId(Long id);

    /**
     * 更新旺店通单号
     *
     * @param id
     * @param wdtPurchaseOrderNo 旺店通采购单号
     */
    int updateWdtPurchaseOrderNo(@Param("id") Long id, @Param("wdtPurchaseOrderNo") String wdtPurchaseOrderNo);

    /**
     * 导出查询
     *
     * @param query
     * @return java.util.List<com.daddylab.supplier.item.domain.stock.dto.StockInOrderAndOrderDetailSheet>
     * <AUTHOR>
     * @date 2022/4/1 16:37
     **/
    List<StockInOrderAndOrderDetailSheet> queryExport(StockInOrderAndOrderDetailQuery query);

    /**
     * 查询炎黄盈动需要的字段
     *
     * @param no
     * @return com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderAndDetailQuery
     * <AUTHOR>
     * @date 2022/4/2 17:54
     **/
    List<StockInOrderAndDetailQuery> selectStockInOrderAndDetailByNo(String no);

    /**
     * 根据订单号查询采购入库单
     *
     * @param no
     * @return com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder
     * <AUTHOR>
     * @date 2022/4/6 13:59
     **/
    StockInOrder selectStockInOrderByNo(String no);

    /**
     * 根据采购订单id查询出入库单列表
     *
     * @param id
     * @return
     */
    List<StockInOrderDetail> getListByPurchaseOrderId(@Param("id") Long id);

    /**
     * 根据采购单id,skuCode,采购单状态,商品是否赠品 查询出申请入库数量
     *
     * @param purchaseOrderId
     * @param skuCode
     * @param isGift
     * @return
     */
    Integer getApplyQuantity(@Param("id") Long purchaseOrderId, @Param("skuCode") String skuCode, @Param("isGift") Boolean isGift);

    /**
     * 根据采购单id 查询出申请入库数量【批量】
     *
     * @param purchaseOrderId 采购单ID
     */
    List<StockInOrderDetailApplyQuantity> getApplyQuantities(@Param("id") Long purchaseOrderId);


    /**
     * 查询已经完成的上个月的入库单
     *
     * @param startTime 开始时间
     * @param endTIme   结束时间
     * @return
     */
    List<StockInOrder> listFinishStockInOrder(@Param("startTime") Long startTime, @Param("endTIme") Long endTIme);

    /**
     * 计算已入库状态的入库单的入库总数量
     *
     * @param purchaseOrderIdList
     * @return
     */
    List<StockInOrderDO> sumReceiptQuantity(@Param("list") List<Long> purchaseOrderIdList);

    /**
     * @param purchaseOrderIdList
     * @return
     */
    List<StockInOrderDO> sumHadAndWillReceiptQuantity(@Param("list") List<Long> purchaseOrderIdList);

    /**
     * 查询导出总数
     *
     * @param stockInOrderAndOrderDetailQuery
     * @return java.lang.Integer
     **/
    Integer selectExportCount(StockInOrderAndOrderDetailQuery stockInOrderAndOrderDetailQuery);

    @Update("update stock_in_order set is_del = 0 where no = #{no}")
    void makeUpStockInOrder(@Param("no") String no);

    List<String> selectSyncKingDeeNo(@Param("startDt") Long startDt,@Param("endDt")Long endDt);
}
