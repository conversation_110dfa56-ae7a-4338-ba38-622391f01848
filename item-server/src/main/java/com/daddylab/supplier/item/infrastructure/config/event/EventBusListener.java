package com.daddylab.supplier.item.infrastructure.config.event;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/30 11:32 上午
 * @description
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface EventBusListener {

    /**
     * 会传递给@Component作为组件名称
     */
    String value() default "";

    /**
     * sync:事件发布和监听为同一线程
     * async:事件发布为A线程，事件的监听处理托管给 eventExecutor
     * @deprecated 不再生效，现在都会同时注册到同步和异步的BUS
     */
    MODE mode() default MODE.SYNC;

    enum MODE {
        SYNC,
        ASYNC
    }

}
