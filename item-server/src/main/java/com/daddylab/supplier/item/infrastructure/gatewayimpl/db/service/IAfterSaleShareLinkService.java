package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSaleShareLink;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 售后分享链接信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-11
 */
public interface IAfterSaleShareLinkService extends IDaddyService<AfterSaleShareLink> {

    String getNewOnNo();
}
