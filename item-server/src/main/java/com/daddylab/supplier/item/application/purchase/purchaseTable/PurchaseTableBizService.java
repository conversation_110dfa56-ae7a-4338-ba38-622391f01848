package com.daddylab.supplier.item.application.purchase.purchaseTable;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.controller.purchaseTable.dto.PurchaseTableCmd;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseTable;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName PurchaseTableBizService.java
 * @description 采购权限
 * @createTime 2021年11月16日 21:13:00
 */
public interface PurchaseTableBizService {

    /**
     * 根据月份查当前权限
     * @param month
     * @return
     */
    SingleResponse<Integer> selectStatusByMonth(String month);

    /**
     * 批量获取指定月份是否可以编辑（True是可以编辑）
     */
    Map<String, Boolean> getEditableStatusBatch(Set<String> months);

    /**
     * 新增月份
     * @param purchaseTable
     */
    void insert (PurchaseTable purchaseTable);


    /**
     * 查询有数据的月份数据
     * @return
     */
    MultiResponse<String>getMonth();

    /**
     * 编辑权限
     * @param cmd
     */
    Response update(PurchaseTableCmd cmd);


    /**
     * 查询上个月未处理的数据
     * @param month
     * @return
     */
    PurchaseTable selectDataByMonth(String month);

    /**
     * 定时跑完修改是否处理状态
     */
    void updateIsDeal(Long id);

    /**
     * 查询
     * @param month
     * @return
     */
    PurchaseTable selectOne(String month);
}
