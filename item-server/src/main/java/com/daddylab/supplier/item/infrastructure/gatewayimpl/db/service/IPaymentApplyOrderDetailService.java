package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentApplyOrderDetail;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 采购管理-付款申请单-付款明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
public interface IPaymentApplyOrderDetailService extends IDaddyService<PaymentApplyOrderDetail> {

    /**
     * 根据关联单号，查询次关联单号下，已经发生付款申请的明细ID列表
     *
     * @param applyRelatedNos
     * @return
     */
    List<Long> getAppliedDetailIdList(Collection<String> applyRelatedNos);

    List<PaymentApplyOrderDetail> getByPaymentApplyOrderId(Long paymentOrderId);



}
