package com.daddylab.supplier.item.infrastructure.gatewayimpl.user;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.DlabConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(name = "ms-user-new-server", url = "${user-server.url}",
        configuration = {DlabConfiguration.class}, fallbackFactory = UserFeignClientFallbackFactory.class)
public interface UserFeignClient {
    /**
     * 获取员工信息根据用户IDs
     */
    @RequestMapping(value = "/user/internal/get-staff-infos", method = RequestMethod.POST)
    Response<Users> getStaffInfos(@RequestBody StaffInfoQuery query);

    /**
     * 根据姓名/性别/在职情况等条件模糊搜索员工信息（uid，姓名，性别，号码）
     */
    @Deprecated
    @RequestMapping(value = "/user/internal/dad_staff/list", method = RequestMethod.GET)
    Response<RowsData<StaffListItem>> staffList(@SpringQueryMap StaffListQuery query);
}
