package com.daddylab.supplier.item.infrastructure.config.batch.reader;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.batch.item.database.AbstractPagingItemReader;
import org.springframework.batch.item.support.AbstractItemCountingItemStreamItemReader;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Class  AbstractIdPagingItemReader
 *
 * @Date 2021/11/8上午9:39
 * <AUTHOR>
 */
public abstract class AbstractIdPagingItemReader<T extends BaseModelInterface> extends AbstractItemCountingItemStreamItemReader<T> implements InitializingBean {

    protected Log logger = LogFactory.getLog(this.getClass());
    private volatile boolean initialized = false;
    private int pageSize = 10;
    private volatile int current = 0;
    private volatile int page = 0;
    protected volatile List<T> results;
    private final Object lock = new Object();
    private T maxT;

    public AbstractIdPagingItemReader() {
        this.setName(ClassUtils.getShortName(AbstractPagingItemReader.class));
    }

    public int getPage() {
        return this.page;
    }

    public int getPageSize() {
        return this.pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Assert.isTrue(this.pageSize > 0, "pageSize must be greater than zero");
    }

    @Override
    @Nullable
    protected T doRead() throws Exception {
        synchronized(this.lock) {
            if (this.results == null || this.current >= this.pageSize) {
                if (this.logger.isDebugEnabled()) {
                    this.logger.debug("Reading page " + this.getPage());
                }
                this.doReadPage();
                ++this.page;
                if (this.current >= this.pageSize) {
                    this.current = 0;
                }
            }
            int next = this.current++;
            T currentT = next < this.results.size() ? this.results.get(next) : null;
            // 升序或者降序
            if (Objects.nonNull(currentT) && (Objects.isNull(maxT) || maxT.getId() < currentT.getId())) {
                maxT = currentT;
            }
            return currentT;
        }
    }

    protected abstract void doReadPage();

    @Override
    protected void doOpen() throws Exception {
        Assert.state(!this.initialized, "Cannot open an already opened ItemReader, call close first");
        this.initialized = true;
    }

    @Override
    protected void doClose() throws Exception {
        synchronized(this.lock) {
            this.initialized = false;
            this.current = 0;
            this.page = 0;
            this.results = null;
        }
    }

    @Override
    protected void jumpToItem(int itemIndex) throws Exception {
        synchronized(this.lock) {
            this.page = itemIndex / this.pageSize;
            this.current = itemIndex % this.pageSize;
        }

        this.doJumpToPage(itemIndex);
        if (this.logger.isDebugEnabled()) {
            this.logger.debug("Jumping to page " + this.getPage() + " and index " + this.current);
        }

    }

    protected abstract void doJumpToPage(int var1);


    /**
     * 获取最大的实体id
     *
     * @return
     */
    public Long getMaxId() {
        return Optional.ofNullable(maxT).map(T::getId).orElse(0L);
    }

    public T getMaxT() {
        return maxT;
    }
}
