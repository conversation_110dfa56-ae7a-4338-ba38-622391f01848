package com.daddylab.supplier.item.application.afterSaleLogistics;

import com.daddylab.supplier.item.application.kuaidaoyun.KdyBizService;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQConsumerBase;
import com.daddylab.supplier.item.types.kuaidaoyun.KdyCallbackRequest;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;

/**
 * <AUTHOR>
 * @since 2024/5/23
 */
@RocketMQMessageListener(nameServer = "${mall-kuaidaoyun-callback.nameServer}", topic = "${mall-kuaidaoyun-callback.topic}", consumerGroup = "${mall-kuaidaoyun-callback.consumerGroup}", consumeThreadMax = 1)
@Service
public class MallKuaidaoyunCallbackConsumer extends RocketMQConsumerBase<KdyCallbackRequest> {
    @Autowired
    private KdyBizService kdyBizService;

    public MallKuaidaoyunCallbackConsumer(RedissonClient redissonClient, StandardEnvironment environment, RefreshConfig refreshConfig) {
        super(redissonClient, environment, refreshConfig);
    }

    @Override
    protected void handle(MessageExt msg, @Nullable KdyCallbackRequest body) {
        kdyBizService.callback(body, false);
    }
}
