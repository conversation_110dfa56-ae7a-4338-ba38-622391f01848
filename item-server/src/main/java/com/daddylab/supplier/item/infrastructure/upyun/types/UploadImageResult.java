package com.daddylab.supplier.item.infrastructure.upyun.types;

import lombok.Data;

@Data
public class UploadImageResult {
    /**
     * 文件url
     */
    private String url;
    /**
     * 文件路径
     */
    private String path;
    /**
     * 高度
     */
    private Integer height;
    /**
     * 宽度
     */
    private Integer width;
    /**
     * 大小(字节)
     */
    private Integer size;
    /**
     * 内容类型 eg.image/jpeg
     */
    private String contentType;
    /**
     * 文件类型 eg.JPEG
     */
    private String fileType;
    /**
     * 请求ID
     */
    private String requestId;
    /**
     * etag
     */
    private String etag;
    /**
     * 请求路径
     */
    private String requestPath;
    /**
     * 文件MD5
     */
    private String md5;

}
