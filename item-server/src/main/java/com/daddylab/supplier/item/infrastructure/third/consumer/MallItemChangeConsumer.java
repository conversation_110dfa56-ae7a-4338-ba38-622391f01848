package com.daddylab.supplier.item.infrastructure.third.consumer;

import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQGroup;
import com.daddylab.supplier.item.infrastructure.rocketmq.enums.MQTopic;
import com.daddylab.supplier.item.infrastructure.third.mallItem.MallItemService;
import com.daddylab.supplier.item.infrastructure.third.mallItem.domain.dto.MallItemChangeDTO;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @class KSConsumer.java
 * @description 商品变更消息消费
 * @date 2024-02-29 17:10
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MQTopic.THIRD_MALL_ITEM_CHANGE_TOPIC, consumerGroup = MQGroup.THIRD_MALL_ITEM_CHANGE_GROUP,
        messageModel = MessageModel.CLUSTERING)
public class MallItemChangeConsumer implements RocketMQListener<MallItemChangeDTO> {

    @Autowired
    MallItemService mallItemService;

    @Override
    public void onMessage(MallItemChangeDTO message) {
        log.info("[自研商城商品变更事件] 接受到 message={}", message);
        try {
            mallItemService.handler(message);
        } catch (Exception e) {
            if (ApplicationContextUtil.isActiveProfile("prod", "gray")) {
                throw e;
            }
        }
    }
}
