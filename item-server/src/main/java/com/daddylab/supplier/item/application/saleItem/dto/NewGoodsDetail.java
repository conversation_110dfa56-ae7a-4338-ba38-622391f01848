package com.daddylab.supplier.item.application.saleItem.dto;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName NewsGoodsDetail.java
 * @description
 * @createTime 2022年04月20日 16:47:00
 */
@Data
@ApiModel("新品商品详情")
public class NewGoodsDetail extends NewGoods {

    @Data
    @ApiModel("运营数据封装")
    public static class ProduceDetail{
        @ApiModelProperty(value = "id")
        private Long id;

        @ApiModelProperty(value = "预计上架日期")
        private Long shelfTime;

        @ApiModelProperty(value = "产品标准名")
        private String standardName;

        @ApiModelProperty(value = "卖点文案8-10个字")
        private String sellingPoints;

//        @ApiModelProperty(value = "小程序id")
//        private String miniId;
//
//        @ApiModelProperty(value = "会员店id")
//        private String membership;
//
//        @ApiModelProperty(value = "美妆店id")
//        private String beautyId;
//
//        @ApiModelProperty(value = "母婴店id")
//        private String infantId;

    }

    @Data
    @ApiModel("采购数据封装")
    public static class BuyerDetail{
        @ApiModelProperty(value = "id")
        private Long id;

        @ApiModelProperty(value = "sku编码")
        private String skuCode;

        @ApiModelProperty(value = "商品名称")
        private String name;

        @ApiModelProperty(value = "颜色/规格")
        private String specs;

        @ApiModelProperty(value = "品类名称")
        private String categoryName;

        @ApiModelProperty(value = "品牌名称")
        @NotNull(message = "品牌名称不得为空")
        private String brandName;

        @ApiModelProperty(value = "制作人(运营)")
        private String producerName;

        @ApiModelProperty(value = "采购人名称")
        private String buyerName;

        @ApiModelProperty(value = "孕妇能不能用(限日化类产品) 0:可以 1:不能 2:其他")
        private String isExpectant;

        @ApiModelProperty(value = "敏感肌能不能用(限日化类产品)")
        private String isSensitive;

        @ApiModelProperty(value = "几岁以上能吃(限食品类产品)")
        private String age;

        @ApiModelProperty(value = "新品活动结束周期")
        @NotNull(message = "新品活动结束周期不得为空")
        private String activePeriod;

        @ApiModelProperty(value = "是否支持7天无理由退换")
        private String noReason;

        @ApiModelProperty(value = "划线价")
        private BigDecimal linePrice;

        @ApiModelProperty(value = "产品日销价")
        private BigDecimal dailyPrice;

        @ApiModelProperty(value = "产品活动价")
        private BigDecimal activePrice;

        @ApiModelProperty(value = "新品活动机制")
        private String activeContent;

        @ApiModelProperty(value = "渠道活动最低价")
        private BigDecimal channelLowest;

        @ApiModelProperty(value = "新品直播机制")
        private String liveActive;

        @ApiModelProperty(value = "必须参加满减(200-20,最大为199-25)")
        private String isReduce;

        @ApiModelProperty(value = "以不低于最低价为限,(公司承担折扣)")
        private String isLowest;

        @ApiModelProperty(value = "仓库/工厂发货")
        private String shipmentType;

        @ApiModelProperty(value = "发货地")
        private String shipmentArea;

        @ApiModelProperty(value = "发货时效")
        private String shipmentAging;

        @ApiModelProperty(value = "物流")
        private String logistics;

        @ApiModelProperty(value = "快递模板")
        private String expressTemplate;

        @ApiModelProperty(value = "备注")
        private String remark;
    }




}
