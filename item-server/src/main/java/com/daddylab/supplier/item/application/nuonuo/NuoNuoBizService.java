package com.daddylab.supplier.item.application.nuonuo;

import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.nuonuo.dto.InvoiceNewCmd;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2024年07月26日 11:19 AM
 */
public interface NuoNuoBizService {

    Response verifyOrderNoList(List<String> orderNoList);

    /**
     * 诺税通 开票接口
     *
     * @param cmd
     */
    Response requestInvoiceNew(InvoiceNewCmd cmd);

    /**
     * 计算开票金额
     *
     * @param orderNoList
     * @return
     */
    SingleResponse<BigDecimal> calculateInvoicedAmount(List<String> orderNoList);

    /**
     * 诺税通 开票接口 try again
     *
     * @param orderNo
     * @return
     */
    Response requestInvoiceNewReTry(String orderNo);


    /**
     * 冲红
     *
     * @return
     */
    Response creditNoteApply(Long startTime, Long endTime);

//    Response creditNoteApply(List<String> orderNoList);




}
