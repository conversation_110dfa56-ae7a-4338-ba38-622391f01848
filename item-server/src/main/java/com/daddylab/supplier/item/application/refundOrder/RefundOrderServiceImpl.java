package com.daddylab.supplier.item.application.refundOrder;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSales.AfterSalesConfirmDetailInfoVO;
import com.daddylab.supplier.item.application.afterSales.AfterSalesEditReturnLogisticsCmd;
import com.daddylab.supplier.item.application.afterSales.AfterSalesSendOnInfoVO;
import com.daddylab.supplier.item.application.afterSales.AfterSalesWarehouseBizService;
import com.daddylab.supplier.item.application.afterSales.WarehouseAfterSalesAddressVO;
import com.daddylab.supplier.item.application.order.OrderService;
import com.daddylab.supplier.item.common.ErrorChecker;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.item.gateway.ItemImageGateway;
import com.daddylab.supplier.item.domain.order.OrderSensitiveInfo;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesReturnLogisticsInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.AfterSalesReceiveState;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesConfirmService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesReceiveService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesReturnLogisticsInfoService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IAfterSalesSendOnInfoService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtRefundOrderService;
import com.daddylab.supplier.item.infrastructure.userInfoCrypto.UserInfoCrypto;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderBaseInfo;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderDetail;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderItem;
import com.daddylab.supplier.item.types.refundOrder.RefundOrderQuery;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/4/17
 */
@Service
@Slf4j
public class RefundOrderServiceImpl implements RefundOrderService {

  @Autowired private WdtRefundOrderMapper wdtRefundOrderMapper;

  @Autowired private IWdtRefundOrderService wdtRefundOrderService;

  @Autowired private ItemImageGateway itemImageGateway;
  @Autowired private OrderService orderService;

  @Autowired private IAfterSalesReceiveService afterSalesReceiveService;

  @Autowired private IAfterSalesSendOnInfoService afterSalesSendOnInfoService;

  @Autowired private IAfterSalesConfirmService afterSalesConfirmService;

  @Resource private WdtGateway wdtGateway;

  @Resource private IAfterSalesReturnLogisticsInfoService afterSalesReturnLogisticsInfoService;

  @Resource private AfterSalesWarehouseBizService afterSalesWarehouseBizService;

  @Resource private RefundOrderEditReturnLogisticsTxService refundOrderEditReturnLogisticsTxService;

  @Resource private UserInfoCrypto userInfoCrypto;

  @NonNull
  private String getOrderQueryKey(RefundOrderQuery query) {
    return "REFUND_ORDER_COUNT:" + DigestUtil.md5Hex(query.toString());
  }

  @Override
  public PageResponse<RefundOrderBaseInfo> refundOrderQuery(RefundOrderQuery query) {
    final Integer total =
        RedisUtil.loadingCache(
            getOrderQueryKey(query), Integer.class, 60, TimeUnit.SECONDS, () -> count(query));
    if (total <= 0) {
      return ResponseFactory.emptyPage();
    }
    List<Long> refundOrderIds;
    if (query.getWithOrderDetailsCond()) {
      refundOrderIds = wdtRefundOrderMapper.queryRefundOrderIdsWithDetailsCond(query);
    } else {
      refundOrderIds = wdtRefundOrderMapper.queryRefundOrderIdsWithoutDetailsCond(query);
    }
    if (refundOrderIds.isEmpty()) {
      return ResponseFactory.emptyPage();
    }
    final List<RefundOrderBaseInfo> refundOrderBaseInfos =
        wdtRefundOrderMapper.queryRefundOrderByIds(refundOrderIds);
    final Set<Long> itemIds =
        refundOrderBaseInfos.stream()
            .map(RefundOrderBaseInfo::getItemDetails)
            .flatMap(v -> v.stream().map(RefundOrderItem::getItemId).filter(Objects::nonNull))
            .collect(Collectors.toSet());
    final Map<Long, String> mainImgUrls =
        !itemIds.isEmpty()
            ? itemImageGateway.batchGetItemMainImgUrls(itemIds)
            : Collections.emptyMap();
    if (!mainImgUrls.isEmpty()) {
      refundOrderBaseInfos.forEach(
          refundOrderBaseInfo ->
              refundOrderBaseInfo
                  .getItemDetails()
                  .forEach(orderItem -> compensateItemMainImg(mainImgUrls, orderItem)));
    }
    final List<String> refundOrderNos =
        refundOrderBaseInfos.stream()
            .map(RefundOrderBaseInfo::getRefundOrderNo)
            .collect(Collectors.toList());
    // 查询售后收货状态
    final Map<String, AfterSalesReceiveState> afterSalesReceiveStateMap =
        afterSalesReceiveService.queryReceiveStateBatch(refundOrderNos);
    refundOrderBaseInfos.forEach(
        refundOrderBaseInfo ->
            Optional.ofNullable(
                    afterSalesReceiveStateMap.getOrDefault(
                        refundOrderBaseInfo.getRefundOrderNo(), AfterSalesReceiveState.WAIT))
                .ifPresent(refundOrderBaseInfo::setReceiveState));
    // 查询是否有本地退货信息记录
    final List<AfterSalesReturnLogisticsInfo> afterSalesReturnLogisticsInfos =
        afterSalesReturnLogisticsInfoService
            .lambdaQuery()
            .in(AfterSalesReturnLogisticsInfo::getReturnOrderNo, refundOrderNos)
            .list();
    final Map<String, AfterSalesReturnLogisticsInfo> afterSalesReturnLogisticsInfoMap =
        afterSalesReturnLogisticsInfos.stream()
            .collect(
                Collectors.toMap(
                    AfterSalesReturnLogisticsInfo::getReturnOrderNo,
                    Function.identity(),
                    (a, b) -> a));
    final Set<String> returnWarehouseNos =
        afterSalesReturnLogisticsInfos.stream()
            .map(AfterSalesReturnLogisticsInfo::getReturnWarehouseNo)
            .collect(Collectors.toSet());
    final Map<String, WarehouseAfterSalesAddressVO> warehouseAfterSalesAddressVOMap =
        returnWarehouseNos.isEmpty()
            ? Collections.emptyMap()
            : afterSalesWarehouseBizService
                .getWarehouseAfterSalesAddressVO(returnWarehouseNos)
                .stream()
                .collect(
                    Collectors.toMap(
                        WarehouseAfterSalesAddressVO::getWarehouseNo,
                        Function.identity(),
                        (a, b) -> a));
    refundOrderBaseInfos.forEach(
        refundOrderBaseInfo ->
            Optional.ofNullable(
                    afterSalesReturnLogisticsInfoMap.get(refundOrderBaseInfo.getRefundOrderNo()))
                .ifPresent(
                    afterSalesReturnLogisticsInfo -> {
                      Optional.ofNullable(afterSalesReturnLogisticsInfo.getReturnWarehouseNo())
                          .filter(StringUtil::isNotBlank)
                          .ifPresent(
                              v -> {
                                refundOrderBaseInfo.setReturnWarehouseNo(v);
                                refundOrderBaseInfo.setReturnWarehouseName(
                                    Optional.ofNullable(warehouseAfterSalesAddressVOMap.get(v))
                                        .map(WarehouseAfterSalesAddressVO::getWarehouseName)
                                        .orElse(v));
                              });
                      Optional.ofNullable(afterSalesReturnLogisticsInfo.getReturnLogisticsName())
                          .filter(StringUtil::isNotBlank)
                          .ifPresent(refundOrderBaseInfo::setReturnLogisticsName);
                      Optional.ofNullable(afterSalesReturnLogisticsInfo.getReturnLogisticsNo())
                          .filter(StringUtil::isNotBlank)
                          .ifPresent(refundOrderBaseInfo::setReturnLogisticsNo);
                    }));
    return ResponseFactory.ofPage(
        refundOrderBaseInfos, total, query.getPageIndex(), query.getPageSize());
  }

  private int count(RefundOrderQuery query) {
    if (query.getWithOrderDetailsCond()) {
      return wdtRefundOrderMapper.queryRefundOrderCountWithDetailsCond(query);
    } else {
      return wdtRefundOrderMapper.queryRefundOrderCountWithoutDetailsCond(query);
    }
  }

  private void compensateItemMainImg(Map<Long, String> mainImgUrls, RefundOrderItem orderItem) {
    orderItem.setItemMainImg(
        Optional.ofNullable(orderItem.getItemMainImg())
            .orElseGet(() -> mainImgUrls.getOrDefault(orderItem.getItemId(), "")));
  }

  @Override
  public SingleResponse<RefundOrderDetail> refundOrderDetail(String refundOrderNo) {
    final RefundOrderDetail data = wdtRefundOrderMapper.queryRefundOrderDetail(refundOrderNo);
    if (data == null) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "退换单不存在");
    }
    final List<RefundOrderItem> swapItems = wdtRefundOrderMapper.querySwapItems(refundOrderNo);
    data.setSwapItemDetails(swapItems);

    final HashSet<Long> itemIds = new HashSet<>();
    data.getReturnItemDetails().stream()
        .map(RefundOrderItem::getItemId)
        .filter(Objects::nonNull)
        .forEach(itemIds::add);
    swapItems.stream()
        .map(RefundOrderItem::getItemId)
        .filter(Objects::nonNull)
        .forEach(itemIds::add);
    final Map<Long, String> itemMainImgUrls =
        !itemIds.isEmpty()
            ? itemImageGateway.batchGetItemMainImgUrls(itemIds)
            : Collections.emptyMap();
    if (!itemMainImgUrls.isEmpty()) {
      data.getReturnItemDetails()
          .forEach(orderItem -> compensateItemMainImg(itemMainImgUrls, orderItem));
      data.getSwapItemDetails()
          .forEach(orderItem -> compensateItemMainImg(itemMainImgUrls, orderItem));
    }
    fillOrderSensitiveInfo(data);
    fillOrderAfterSalesInfo(data);
    encryptSensitiveInfo(data);
    return SingleResponse.of(data);
  }

  private void encryptSensitiveInfo(RefundOrderDetail data) {
    String buyerMobile = data.getBuyerMobile();
    data.setBuyerMobile(StringUtil.maskMobile(buyerMobile));
    data.setBuyerMobileCipher(userInfoCrypto.encrypt(buyerMobile));
    String returnTel = data.getReturnTel();
    data.setReturnTel(StringUtil.maskMobile(returnTel));
    data.setReturnTelCipher(userInfoCrypto.encrypt(returnTel));
  }

  @Override
  public Response editReturnLogistics(AfterSalesEditReturnLogisticsCmd cmd) {
    final SingleResponse<RefundOrderDetail> refundOrderDetailResp =
        refundOrderDetail(cmd.getReturnOrderNo());
    ErrorChecker.checkAndThrowIfError(
        refundOrderDetailResp,
        ErrorCode.DATA_NOT_FOUND,
        "退换单不存在",
        Objects::nonNull,
        ErrorCode.DATA_NOT_FOUND,
        "退换单不存在");
    final RefundOrderDetail refundOrderDetail = refundOrderDetailResp.getData();
    return refundOrderEditReturnLogisticsTxService.editReturnLogistics(cmd, refundOrderDetail);
  }

  private void fillOrderAfterSalesInfo(RefundOrderDetail refundOrderDetail) {
    final String refundOrderNo = refundOrderDetail.getRefundOrderNo();
    final Optional<AfterSalesReturnLogisticsInfo> afterSalesReturnLogisticsInfoOptional =
        afterSalesReturnLogisticsInfoService
            .lambdaQuery()
            .eq(AfterSalesReturnLogisticsInfo::getReturnOrderNo, refundOrderNo)
            .oneOpt();
    if (afterSalesReturnLogisticsInfoOptional.isPresent()) {
      final AfterSalesReturnLogisticsInfo afterSalesReturnLogisticsInfo =
          afterSalesReturnLogisticsInfoOptional.get();
      Optional.ofNullable(afterSalesReturnLogisticsInfo.getFullAddress())
          .filter(StringUtil::isNotBlank)
          .ifPresent(refundOrderDetail::setReturnAddress);
      Optional.ofNullable(afterSalesReturnLogisticsInfo.getContacts())
          .filter(StringUtil::isNotBlank)
          .ifPresent(refundOrderDetail::setReturnContacts);
      Optional.ofNullable(afterSalesReturnLogisticsInfo.getTel())
          .filter(StringUtil::isNotBlank)
          .ifPresent(refundOrderDetail::setReturnTel);
      Optional.ofNullable(afterSalesReturnLogisticsInfo.getReturnWarehouseNo())
          .filter(StringUtil::isNotBlank)
          .ifPresent(
              v -> {
                refundOrderDetail.setReturnWarehouseNo(v);
                refundOrderDetail.setReturnWarehouseName(
                    afterSalesWarehouseBizService
                        .getWarehouseAfterSalesAddressVO(v)
                        .map(WarehouseAfterSalesAddressVO::getWarehouseName)
                        .orElse(v));
              });
      Optional.ofNullable(afterSalesReturnLogisticsInfo.getReturnLogisticsName())
          .filter(StringUtil::isNotBlank)
          .ifPresent(refundOrderDetail::setReturnLogisticsName);
      Optional.ofNullable(afterSalesReturnLogisticsInfo.getReturnLogisticsNo())
          .filter(StringUtil::isNotBlank)
          .ifPresent(refundOrderDetail::setReturnLogisticsNo);
    } else {
      afterSalesWarehouseBizService
          .getWarehouseAfterSalesAddressVO(refundOrderDetail.getReturnWarehouseNo())
          .ifPresent(
              v -> {
                refundOrderDetail.setReturnAddress(v.getFullAddress());
                refundOrderDetail.setReturnContacts(v.getContacts());
                refundOrderDetail.setReturnTel(v.getTel());
              });
    }
    final List<AfterSalesSendOnInfoVO> afterSalesSendOnInfoVOS =
        afterSalesSendOnInfoService.getAfterSalesSendOnInfoVOS(refundOrderNo).stream()
            .filter(v -> StringUtil.isNotBlank(v.getLogisticsNo()))
            .sorted(Comparator.comparing(AfterSalesSendOnInfoVO::getSendOnTime))
            .collect(Collectors.toList());
    refundOrderDetail.setSendOnInfos(afterSalesSendOnInfoVOS);
    afterSalesReceiveService
        .getAfterSalesReceiveInfoVO(refundOrderNo)
        .ifPresent(refundOrderDetail::setReceiveInfo);
    afterSalesConfirmService
        .getAfterSalesConfirmInfoVO(refundOrderNo)
        .ifPresent(refundOrderDetail::setConfirmInfo);
    final List<AfterSalesConfirmDetailInfoVO> afterSalesConfirmDetailInfoVOs =
        afterSalesConfirmService.getAfterSalesConfirmDetailInfoVOs(refundOrderNo);
    final Map<Integer, AfterSalesConfirmDetailInfoVO> afterSalesConfirmDetailInfoVOsMap =
        afterSalesConfirmDetailInfoVOs.stream()
            .collect(
                Collectors.toMap(AfterSalesConfirmDetailInfoVO::getRecId, Function.identity()));
    for (RefundOrderItem returnItemDetail : refundOrderDetail.getReturnItemDetails()) {
      Optional.ofNullable(afterSalesConfirmDetailInfoVOsMap.get(returnItemDetail.getRecId()))
          .ifPresent(
              v -> {
                returnItemDetail.setUndertakeType(v.getUndertakeType());
                returnItemDetail.setUndertakeAmount(v.getUndertakeAmount());
              });
    }
  }

  private void fillOrderSensitiveInfo(RefundOrderDetail refundOrderDetail) {
    final List<String> srcOrderNos = StrUtil.splitTrim(refundOrderDetail.getSrcOrderNos(), ',');
    if (!srcOrderNos.isEmpty()) {
      final String srcOrderNo = srcOrderNos.get(0);
      if (StrUtil.isNotBlank(srcOrderNo)) {
        final Map<String, OrderSensitiveInfo> orderSensitiveInfoMap =
            orderService.queryOrderSensitiveInfoBatchBySrcOrderNo(
                Collections.singletonList(srcOrderNo));
        final OrderSensitiveInfo orderSensitiveInfo = orderSensitiveInfoMap.get(srcOrderNo);
        if (orderSensitiveInfo != null) {
          final String receiverPhone = orderSensitiveInfo.getReceiverPhone();
          refundOrderDetail.setBuyerMobile(StringUtil.maskMobile(receiverPhone));
          refundOrderDetail.setBuyerName(orderSensitiveInfo.getReceiverName());
        }
      }
    }
  }
}
