package com.daddylab.supplier.item.controller.item;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * <AUTHOR> up
 * @date 2022年11月21日 2:51 PM
 */
@Data
@ApiModel("商品上新计划列表详情，首页文案和上新价编辑参数封装")
public class ItemLaunchPlanTextParam {

    @ApiModelProperty(value = "共享盘链接")
    private String shareDiskLink;

    @ApiModelProperty(value = "首页文案")
    private String homeCopy;

    @ApiModelProperty(value = "上新价")
    @Size(max = 100)
    private String launchPrice;

    @ApiModelProperty(value = "商品ID")
    private Long itemId;

    @ApiModelProperty(value = "计划ID")
    private Long planId;
}
