package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/6/27
 */
@AllArgsConstructor
@Getter
public enum InventoryMode implements IIntegerEnum {
    //库存模式 0 共享, 1 锁定
    SHARED(0, "共享"),
    LOCK(1, "锁定");

    private final Integer value;
    private final String desc;
}
