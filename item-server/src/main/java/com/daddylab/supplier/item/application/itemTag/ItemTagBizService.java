package com.daddylab.supplier.item.application.itemTag;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/6/18
 */
public interface ItemTagBizService {
    void addTag(Long itemId, String tagId);

    void removeTag(Long itemId, String tagId);

    void setItemTag(Long itemId, Collection<String> tagIds);

    List<String> listByItemId(Long itemId);

    Map<Long, List<String>> listByItemIds(Collection<Long> itemIds);
}
