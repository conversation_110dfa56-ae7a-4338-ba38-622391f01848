package com.daddylab.supplier.item.application.platformItem.data;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/3/14
 */
@Data
@ApiModel("平台商品同步库存命令")
public class PlatformItemSyncStockCmd {
    @ApiModelProperty("平台商品ID（外）")
    @NotBlank
    private String outerItemId;

    @ApiModelProperty("平台")
    @NotNull
    private Platform platform;
}
