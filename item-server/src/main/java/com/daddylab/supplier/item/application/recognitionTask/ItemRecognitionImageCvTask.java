package com.daddylab.supplier.item.application.recognitionTask;

import cn.hutool.core.io.FastByteArrayOutputStream;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.URLUtil;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerRecognitionTask;
import com.daddylab.supplier.item.infrastructure.timing.StopWatch;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.ExceptionUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.recognitionTask.ItemDrawerRecognitionTaskStatus;
import com.diffplug.common.base.Errors;
import com.google.common.collect.Lists;
import com.microsoft.azure.cognitiveservices.vision.computervision.models.Line;
import com.microsoft.azure.cognitiveservices.vision.computervision.models.ReadOperationResult;

import hu.akarnokd.rxjava3.interop.RxJavaInterop;

import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Flowable;
import io.reactivex.rxjava3.core.Single;
import io.reactivex.rxjava3.schedulers.Schedulers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.list.SynchronizedList;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Semaphore;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import javax.imageio.ImageIO;

/**
 * 商品上新抽屉图片CV识别任务
 *
 * <AUTHOR>
 * @since 2022/12/5
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ItemRecognitionImageCvTask {

    public static final int HEIGHT_LIMIT = 10000;

    private final ItemRecognitionTaskGateway itemRecognitionTaskGateway;
    private final CvGateway cvGateway;
    private final RefreshConfig refreshConfig;

    @XxlJob("ItemRecognitionImageCvTask")
    public void doTask() throws InterruptedException {
        final List<ItemDrawerRecognitionTask> itemDrawerRecognitionTasks =
                itemRecognitionTaskGateway.getWaitImageCvRecognitionTasks();
        log.info("抽屉图片CV开始 total={}", itemDrawerRecognitionTasks.size());
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        final CountDownLatch countDownLatch = new CountDownLatch(itemDrawerRecognitionTasks.size());
        final Semaphore semaphore = new Semaphore(refreshConfig.getCvSemaphore());
        for (ItemDrawerRecognitionTask itemDrawerRecognitionTask : itemDrawerRecognitionTasks) {
            if (semaphore.tryAcquire()) {
                try {
                    handleTask(countDownLatch, itemDrawerRecognitionTask);
                } finally {
                    semaphore.release();
                }
            }
        }
        countDownLatch.await();
        stopWatch.stop();
        log.info(
                "抽屉图片CV完成 total={} time={}ms",
                itemDrawerRecognitionTasks.size(),
                stopWatch.getTotalTimeMillis());
    }

    private void handleTask(
            CountDownLatch countDownLatch, ItemDrawerRecognitionTask itemDrawerRecognitionTask) {
        final String imageUrl = itemDrawerRecognitionTask.getImageUrl();
        cvGateway
                .imageVision(imageUrl)
                .subscribe(
                        result -> {
                            final Set<ReadOperationResult> readOperationResults =
                                    Collections.singleton(result);
                            final String cvResultJson = JsonUtil.toJson(readOperationResults);
                            final String cvText = extractCvContent(readOperationResults);
                            itemRecognitionTaskGateway.updateTaskCvResult(
                                    itemDrawerRecognitionTask,
                                    cvText,
                                    cvResultJson,
                                    ItemDrawerRecognitionTaskStatus.WAIT_CHECK,
                                    null);
                            countDownLatch.countDown();
                        },
                        e -> {
                            final List<String> logs =
                                    SynchronizedList.decorate(
                                            Lists.newArrayList(DateUtil.formatNow()));
                            // 图片超出微软云限制的最大长度，需切割图片
                            if (Optional.ofNullable(e)
                                    .map(Throwable::getMessage)
                                    .orElse("")
                                    .contains("InvalidImageDimension")) {
                                log.warn(
                                        "图片尺寸不符合微软云要求 taskId:{} url:{} msg:{}",
                                        itemDrawerRecognitionTask.getId(),
                                        imageUrl,
                                        e.getMessage());
                                logs.add("图片尺寸不符合微软云要求：" + e.getMessage());
                                splitThenCv(imageUrl, logs)
                                        .flatMapSingle(
                                                data ->
                                                        RxJavaInterop.toV3Single(
                                                                cvGateway.imageVision(data)))
                                        .toList()
                                        .doFinally(countDownLatch::countDown)
                                        .subscribe(
                                                result -> {
                                                    final String cvResultJson =
                                                            JsonUtil.toJson(result);
                                                    final String cvText = extractCvContent(result);
                                                    itemRecognitionTaskGateway.updateTaskCvResult(
                                                            itemDrawerRecognitionTask,
                                                            cvText,
                                                            cvResultJson,
                                                            ItemDrawerRecognitionTaskStatus
                                                                    .WAIT_CHECK,
                                                            logs);
                                                },
                                                errOfSplitThenCv -> {
                                                    log.error(
                                                            "图片CV异常 taskId:{} url:{} :{}",
                                                            itemDrawerRecognitionTask.getId(),
                                                            imageUrl,
                                                            errOfSplitThenCv.getMessage(),
                                                            errOfSplitThenCv);
                                                    logs.add(
                                                            ExceptionUtil.getMessage(
                                                                    errOfSplitThenCv));
                                                    itemRecognitionTaskGateway.updateTaskCvResult(
                                                            itemDrawerRecognitionTask,
                                                            null,
                                                            null,
                                                            ItemDrawerRecognitionTaskStatus.FAIL,
                                                            logs);
                                                });
                            } else {
                                log.error(
                                        "图片CV识别异常 taskId:{} url:{} msg:{}",
                                        itemDrawerRecognitionTask.getId(),
                                        imageUrl,
                                        ExceptionUtil.getMessage(e),
                                        e);
                                logs.add("CV异常:" + ExceptionUtil.getSimpleStackString(e, 3));
                                itemRecognitionTaskGateway.updateTaskCvResult(
                                        itemDrawerRecognitionTask,
                                        null,
                                        null,
                                        ItemDrawerRecognitionTaskStatus.FAIL,
                                        logs);
                                countDownLatch.countDown();
                            }
                        });
    }

    @lombok.NonNull
    private String extractCvContent(Collection<ReadOperationResult> readOperationResults) {
        return readOperationResults.stream()
                .flatMap(
                        result ->
                                result.analyzeResult().readResults().stream()
                                        .flatMap(
                                                readResult ->
                                                        readResult.lines().stream()
                                                                .map(Line::text)))
                .collect(Collectors.joining(""));
    }

    private @NonNull Flowable<byte[]> splitThenCv(String imageUrl, List<String> logs) {
        try {
            final Single<BufferedImage> bufferedImageSingle = readBufferedImage(imageUrl);
            return bufferedImageSingle
                    .toFlowable()
                    .flatMap(image -> splitThenCv(imageUrl, image, logs));
        } catch (Throwable ex) {
            return Flowable.error(ex);
        }
    }

    private Flowable<byte[]> splitThenCv(String imageUrl, BufferedImage image, List<String> logs) {
        final String suffix = FileNameUtil.getSuffix(imageUrl);
        final int width = image.getWidth();
        final int height = image.getHeight();
        final int type = image.getType();
        logs.add("图片尺寸：(" + width + "px" + ", " + height + "px) 后缀：" + suffix);
        if (height < 50) {
            logs.add("图片高度小于50，填充图片高度");
            final Flowable<byte[]> flowable =
                    Flowable.generate(
                            emitter -> {
                                try (final FastByteArrayOutputStream os =
                                        new FastByteArrayOutputStream()) {
                                    final BufferedImage bufferedImage =
                                            new BufferedImage(width, 50, type);
                                    final Graphics2D graphics = bufferedImage.createGraphics();
                                    graphics.drawImage(
                                            image, 0, 0, width, height, 0, 0, width, height, null);
                                    //noinspection BlockingMethodInNonBlockingContext
                                    ImageIO.write(bufferedImage, suffix, os);
                                    final String tmpImageId = IdUtil.simpleUUID();
                                    final String tmpImagePath =
                                            "/tmp/recognitionTask/" + tmpImageId + "." + suffix;
                                    FileUtil.writeBytes(os.toByteArray(), tmpImagePath);
                                    logs.add("填充图片写入临时文件：" + tmpImagePath);
                                    emitter.onNext(os.toByteArray());
                                    emitter.onComplete();
                                } catch (Exception exception) {
                                    emitter.onError(exception);
                                }
                            });
            return flowable.subscribeOn(Schedulers.io());
        }
        List<Pair<Integer, Integer>> lines = Lists.newLinkedList();
        L1:
        for (int hi = 0; hi < height; hi++) {
            int rgb0 = -1;
            for (int wi = 0; wi < width; wi++) {
                final int rgb = image.getRGB(wi, hi);
                if (wi == 0) {
                    rgb0 = rgb;
                } else if (rgb0 != rgb) {
                    continue L1;
                }
            }
            final Pair<Integer, Integer> lastLine =
                    lines.size() > 0 ? lines.get(lines.size() - 1) : null;
            if (lastLine != null && lastLine.getRight() + 1 == hi) {
                lines.set(lines.size() - 1, Pair.of(lastLine.getLeft(), hi));
            } else {
                lines.add(Pair.of(hi, hi));
            }
        }
        logs.add(
                "图片可切割位置："
                        + StringUtil.format(
                                "[{}]",
                                lines.stream()
                                        .map(Pair::toString)
                                        .collect(Collectors.joining(", "))));
        final int numCutPoint = (int) Math.floor(height / ((float) HEIGHT_LIMIT));
        List<Pair<Integer, Integer>> cutLines = Lists.newLinkedList();
        for (int i = 0; i < numCutPoint; i++) {
            int maxY = HEIGHT_LIMIT * (i + 1);
            for (int j = 0; j < lines.size(); j++) {
                final Pair<Integer, Integer> range = lines.get(j);
                if (range.getLeft() > maxY) {
                    final Pair<Integer, Integer> prevRange = lines.get(j - 1);
                    if (prevRange.getRight() > maxY) {
                        cutLines.add(Pair.of(prevRange.getLeft(), maxY));
                    } else {
                        cutLines.add(prevRange);
                    }
                    break;
                }
            }
        }
        logs.add(
                "切割位置:"
                        + StringUtil.format(
                                "[{}]",
                                lines.stream()
                                        .map(Pair::toString)
                                        .collect(Collectors.joining(", "))));
        Function<Pair<Integer, Integer>, Integer> fAvgY =
                range ->
                        NumberUtil.div(NumberUtil.add(range.getLeft(), range.getRight()), 2)
                                .intValue();
        //noinspection SuspiciousNameCombination
        cutLines.add(Pair.of(height, height));
        final Iterator<BufferedImage> iterator =
                IntStream.range(0, cutLines.size())
                        .mapToObj(
                                i -> {
                                    int y0 = i > 0 ? fAvgY.apply(cutLines.get(i - 1)) : 0;
                                    final Integer y1 = fAvgY.apply(cutLines.get(i));
                                    final int h = y1 - y0;
                                    final BufferedImage bufferedImage =
                                            new BufferedImage(width, h, type);
                                    final Graphics2D graphics = bufferedImage.createGraphics();
                                    graphics.drawImage(
                                            image,
                                            0,
                                            0,
                                            image.getWidth(),
                                            h,
                                            0,
                                            y0,
                                            width,
                                            y1,
                                            null);
                                    final String msg =
                                            StringUtil.format(
                                                    "切割图片{}：[({},{}),({},{})]",
                                                    i + 1,
                                                    0,
                                                    y0,
                                                    width,
                                                    y1);
                                    logs.add(msg);
                                    return bufferedImage;
                                })
                        .iterator();
        final Flowable<byte[]> flowable =
                Flowable.generate(
                        emitter -> {
                            if (iterator.hasNext()) {
                                try (final FastByteArrayOutputStream os =
                                        new FastByteArrayOutputStream()) {
                                    final BufferedImage bufferedImage = iterator.next();
                                    //noinspection BlockingMethodInNonBlockingContext
                                    ImageIO.write(bufferedImage, suffix, os);
                                    final String tmpImageId = IdUtil.simpleUUID();
                                    final String tmpImagePath =
                                            "/tmp/recognitionTask/" + tmpImageId + "." + suffix;
                                    FileUtil.writeBytes(os.toByteArray(), tmpImagePath);
                                    logs.add("切割图片写入临时文件：" + tmpImagePath);
                                    emitter.onNext(os.toByteArray());
                                } catch (Exception exception) {
                                    emitter.onError(exception);
                                }
                            } else {
                                emitter.onComplete();
                            }
                        });
        return flowable.subscribeOn(Schedulers.io());
    }

    private @NonNull Single<BufferedImage> readBufferedImage(String imageUrl) {
        return Single.fromCompletionStage(
                CompletableFuture.supplyAsync(
                        Errors.rethrow()
                                .wrap(
                                        () -> {
                                            final InputStream stream =
                                                    URLUtil.getStream(URLUtil.url(imageUrl));
                                            return ImageIO.read(stream);
                                        })));
    }
}
