package com.daddylab.supplier.item.application.purchasePayable.applyPayAudit.dto;

import com.alibaba.cola.dto.Command;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayApplyAuditStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> up
 * @date 2023年02月17日 6:08 PM
 */
@Data
public class AuditNodeCmd extends Command {

    @NotNull(message = "节点id不得为空")
    @ApiModelProperty("节点id")
    private Long nodeId;

    @NotNull
    @ApiModelProperty("审核操作。PASS|REJECT")
    private PayApplyAuditStatus status;

    @ApiModelProperty("审核意见")
    private String opinion;
}
