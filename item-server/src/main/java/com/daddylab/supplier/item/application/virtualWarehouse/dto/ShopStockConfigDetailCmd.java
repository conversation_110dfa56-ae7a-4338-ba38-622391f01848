/*
package com.daddylab.supplier.item.application.virtualWarehouse.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

*/
/**
 * <AUTHOR> up
 * @date 2024年03月11日 2:16 PM
 *//*

@Data
@ApiModel("店铺-库存明细查询")
public class ShopStockConfigDetailCmd {

    @NotNull
    private Long shopId;

    @ApiModelProperty("仓库编码，实仓或者虚拟仓")
    @NotBlank
    private String warehouseNo;

    @ApiModelProperty("仓库库存占，可以为空")
    private Integer inventoryRatio;

    private Boolean shopToRun = false;

    private Boolean runToStop = false;

}
*/
