package com.daddylab.supplier.item.application.drawer.impl.copySupport;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.drawer.ItemDrawerCopyService;
import com.daddylab.supplier.item.application.drawer.converter.ItemDrawerConverter;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerLiveVerbalService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerModuleAuditService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerModuleAuditStatsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemDrawerModuleAuditTaskService;
import com.google.common.collect.Streams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @class ItemDrawerImageCopyServiceImpl.java
 * @description 描述类的作用
 * @date 2024-04-09 16:35
 */
@Service
@Slf4j
public class ItemDrawerModuleAuditCopyServiceImpl implements ItemDrawerCopyService {

    @Autowired
    private IItemDrawerModuleAuditService itemDrawerModuleAuditService;
    @Autowired
    private IItemDrawerModuleAuditStatsService itemDrawerModuleAuditStatsService;
    @Autowired
    private IItemDrawerModuleAuditTaskService itemDrawerModuleAuditTaskService;
    @Autowired
    private IItemDrawerLiveVerbalService itemDrawerLiveVerbalService;
    @Resource
    private ItemDrawerImageCopyServiceImpl itemDrawerImageCopyService;

    @Override
    public void handler(ItemDrawer sourceDrawer, ItemDrawer targetDrawer) {
        // [直播话术记录]目标抽屉原本的记录删除
        List<ItemDrawerLiveVerbal> targetLiveVerbals = itemDrawerLiveVerbalService.getByItemId(targetDrawer.getItemId());
        List<Long> deleteIds = targetLiveVerbals.stream().map(ItemDrawerLiveVerbal::getId).collect(Collectors.toList());
        itemDrawerLiveVerbalService.removeByIdsWithTime(deleteIds);

        // [直播话术记录]从源抽屉复制
        List<ItemDrawerLiveVerbal> sourceLiveVerbals = itemDrawerLiveVerbalService.getByItemId(sourceDrawer.getItemId());
        final List<ItemDrawerLiveVerbal> newSourceLiveVerbals = ItemDrawerConverter.INSTANCE.copyItemDrawerLiveVerbalList(
                sourceLiveVerbals);
        newSourceLiveVerbals.stream().peek(liveVerbal -> {
            liveVerbal.setItemDrawerId(targetDrawer.getId());
            liveVerbal.setItemId(targetDrawer.getItemId());
        }).collect(Collectors.toList());
        itemDrawerLiveVerbalService.saveBatch(newSourceLiveVerbals);

        // [直播话术记录]建立新老记录的ID映射
        final Map<Long, Long> liveVerbalsIdsMap = Streams.zip(sourceLiveVerbals.stream(),
                                                                 newSourceLiveVerbals.stream(),
                                                                 (a, b) -> new Long[]{a.getId(), b.getId()})
                                                         .collect(Collectors.toMap(v -> v[0], v -> v[1]));

        // 拷贝状态
        copyAuditStatus(sourceDrawer, targetDrawer, liveVerbalsIdsMap);

        // 直播话术的图片信息拷贝
        itemDrawerImageCopyService.handler0(sourceDrawer, targetDrawer, Boolean.TRUE);

        // [ModuleAuditTask] 删除
        List<ItemDrawerModuleAuditTask> itemDrawerModuleAuditTaskList = getItemDrawerModuleAuditTask(targetDrawer.getItemId());
        if (!itemDrawerModuleAuditTaskList.isEmpty()) {
            List<Long> auditTaskIds = itemDrawerModuleAuditTaskList.stream()
                                                                   .map(ItemDrawerModuleAuditTask::getId)
                                                                   .collect(Collectors.toList());
            itemDrawerModuleAuditTaskService.removeByIdsWithTime(auditTaskIds);
        }
        // [ModuleAuditTask] 新增
        List<ItemDrawerModuleAuditTask> sourceItemDrawerModuleAuditTaskList = getItemDrawerModuleAuditTask(sourceDrawer.getItemId());
        if (!sourceItemDrawerModuleAuditTaskList.isEmpty()) {
            final ArrayList<ItemDrawerModuleAuditTask> moduleAuditTasks = new ArrayList<>();
            for (ItemDrawerModuleAuditTask auditTask : sourceItemDrawerModuleAuditTaskList) {
                auditTask.setItemId(targetDrawer.getItemId());
                if (auditTask.getLiveVerbalTrickId() != null) {
                    final Long newLiveVerbalTrickId = liveVerbalsIdsMap.get(auditTask.getLiveVerbalTrickId());
                    if (newLiveVerbalTrickId != null) {
                        auditTask.setLiveVerbalTrickId(newLiveVerbalTrickId);
                    } else {
                        log.error(
                                "[抽屉复制][直播话术审核任务]直播话术ID映射失败 sourceAuditTaskId={} liveVerbalsIdsMap={} sourceItemId={} targetItemId={}",
                                auditTask.getId(),
                                liveVerbalsIdsMap,
                                sourceDrawer.getItemId(),
                                targetDrawer.getItemId());
                    }
                }
                moduleAuditTasks.add(auditTask);
            }
            itemDrawerModuleAuditTaskService.saveBatch(moduleAuditTasks);
        }
    }


    /**
     * copy状态
     *
     * @param sourceDrawer      com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer
     * @param targetDrawer      com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawer
     * @param liveVerbalsIdsMap
     * <AUTHOR>
     * @date 2024/4/22 18:27
     */
    public void copyAuditStatus(ItemDrawer sourceDrawer, ItemDrawer targetDrawer, Map<Long, Long> liveVerbalsIdsMap) {
        // [ModuleAudit] 删除
        List<ItemDrawerModuleAudit> itemDrawerModuleAudits = getItemDrawerModuleAudit(targetDrawer.getItemId());
        if (!itemDrawerModuleAudits.isEmpty()) {
            List<Long> auditIds = itemDrawerModuleAudits.stream()
                                                        .map(ItemDrawerModuleAudit::getId)
                                                        .collect(Collectors.toList());
            itemDrawerModuleAuditService.removeByIdsWithTime(auditIds);
        }
        // [ModuleAudit] 新增
        List<ItemDrawerModuleAudit> sourceItemDrawerModuleAudits = getItemDrawerModuleAudit(sourceDrawer.getItemId());
        if (!sourceItemDrawerModuleAudits.isEmpty()) {
            List<ItemDrawerModuleAudit> newModuleAudits = sourceItemDrawerModuleAudits
                    .stream()
                    .peek(audit -> {
                        audit.setItemId(targetDrawer.getItemId());
                        if (audit.getLiveVerbalTrickId() != null) {
                            final Long newLiveVerbalTrickId = liveVerbalsIdsMap.get(audit.getLiveVerbalTrickId());
                            if (newLiveVerbalTrickId != null) {
                                audit.setLiveVerbalTrickId(newLiveVerbalTrickId);
                            } else {
                                log.error(
                                        "[抽屉复制][直播话术审核主记录]直播话术ID映射失败 sourceAuditId={} liveVerbalsIdsMap={} sourceItemId={} targetItemId={}",
                                        audit.getId(),
                                        liveVerbalsIdsMap,
                                        sourceDrawer.getItemId(),
                                        targetDrawer.getItemId());
                            }
                        }
                    })
                    .collect(Collectors.toList());
            itemDrawerModuleAuditService.saveBatch(newModuleAudits);
        }


        // [ModuleAuditStats] 删除
        List<ItemDrawerModuleAuditStats> itemDrawerModuleAuditStats = getItemDrawerModuleAuditStats(targetDrawer.getItemId());
        if (!itemDrawerModuleAuditStats.isEmpty()) {
            List<Long> auditStatsIds = itemDrawerModuleAuditStats.stream()
                                                                 .map(ItemDrawerModuleAuditStats::getId)
                                                                 .collect(Collectors.toList());
            itemDrawerModuleAuditStatsService.removeByIdsWithTime(auditStatsIds);
        }
        // [ModuleAuditStats] 新增
        List<ItemDrawerModuleAuditStats> sourceItemDrawerModuleAuditStatsList = getItemDrawerModuleAuditStats(
                sourceDrawer.getItemId());
        if (!sourceItemDrawerModuleAuditStatsList.isEmpty()) {
            List<ItemDrawerModuleAuditStats> newSourceItemDrawerModuleAuditStatsList = sourceItemDrawerModuleAuditStatsList.stream()
                                                                                                                           .peek(sourceItemDrawerModuleAuditStats -> sourceItemDrawerModuleAuditStats.setItemId(
                                                                                                                                   targetDrawer.getItemId()))
                                                                                                                           .collect(
                                                                                                                                   Collectors.toList());
            itemDrawerModuleAuditStatsService.saveBatch(newSourceItemDrawerModuleAuditStatsList);
        }
    }


    List<ItemDrawerModuleAudit> getItemDrawerModuleAudit(Long itemId) {
        LambdaQueryWrapper<ItemDrawerModuleAudit> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ItemDrawerModuleAudit::getItemId, itemId);
        return itemDrawerModuleAuditService.list(queryWrapper);
    }

    List<ItemDrawerModuleAuditStats> getItemDrawerModuleAuditStats(Long itemId) {
        LambdaQueryWrapper<ItemDrawerModuleAuditStats> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ItemDrawerModuleAuditStats::getItemId, itemId);
        return itemDrawerModuleAuditStatsService.list(queryWrapper);
    }

    List<ItemDrawerModuleAuditTask> getItemDrawerModuleAuditTask(Long itemId) {
        LambdaQueryWrapper<ItemDrawerModuleAuditTask> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ItemDrawerModuleAuditTask::getItemId, itemId);
        return itemDrawerModuleAuditTaskService.list(queryWrapper);
    }

}
