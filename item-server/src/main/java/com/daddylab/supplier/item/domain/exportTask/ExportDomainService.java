package com.daddylab.supplier.item.domain.exportTask;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.PageQuery;
import com.daddylab.supplier.item.application.bizLevelDivision.BizLevelDivisionConvert;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.CombinationItemPageQuery;
import com.daddylab.supplier.item.application.saleItem.dto.NewGoodsSheet;
import com.daddylab.supplier.item.application.saleItem.query.NewGoodsQueryPage;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.ExportTransMapper;
import com.daddylab.supplier.item.common.trans.PurchaseTransMapper;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.controller.item.dto.ExportCmd;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseQueryPage;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import com.daddylab.supplier.item.domain.exportTask.dto.item.ExportItemDto;
import com.daddylab.supplier.item.domain.exportTask.dto.item.ExportItemNoPriceDto;
import com.daddylab.supplier.item.domain.exportTask.dto.item.ExportSkuDto;
import com.daddylab.supplier.item.domain.exportTask.dto.item.ExportSkuNoPriceDto;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseSheet;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderQuery;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderSheet;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.bizLevelDivision.BizDivisionContext;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Purchase;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBizLevelDivisionService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IExportTaskService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import java.io.File;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * 1.0.0 导出数据，暂时一律游标查询
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/2 9:56 上午
 * @description
 */
@Slf4j
@Service
public class ExportDomainService {

  @Autowired ExportGateway exportGateway;

  @Autowired FileGateway fileGateway;

  @Autowired
  @Qualifier("UserGatewayCacheImpl")
  UserGateway userGateway;

  @Autowired IExportTaskService iExportTaskService;

  @Autowired
  @Qualifier("exportExecutor")
  public ThreadPoolTaskExecutor threadPoolExecutor;

  @Autowired private IBizLevelDivisionService bizLevelDivisionService;

  @Autowired IBizLevelDivisionService iBizLevelDivisionService;

  public void export(
      PageQuery pageQuery,
      ExportTask task,
      ExportType exportType,
      Class<? extends ExportSheet> clazz) {
    export(pageQuery, task, exportType, clazz, null);
  }

  /**
   * 入口
   *
   * @param pageQuery
   * @param task
   * @param exportType
   * @param clazz
   * @param function
   */
  public <T> void export(
      PageQuery pageQuery,
      ExportTask task,
      ExportType exportType,
      Class<? extends ExportSheet> clazz,
      Function<T, ExportSheet> function) {
    Integer count = ReflectUtil.invoke(exportGateway, exportType.getCountMethod(), pageQuery);
    Assert.state(count > 0, "查询数据量为空，无法导出");

    threadPoolExecutor.execute(
        () -> {
          try {
            String downloadUrl =
                export0(pageQuery, task, count, exportType.getQueryMethod(), clazz, function);
            task.setStatus(ExportTaskStatus.SUCCESS);
            task.setDownloadUrl(downloadUrl);
          } catch (Exception e) {
            log.error("导出异常", e);
            task.setError(e.getMessage());
            task.setStatus(ExportTaskStatus.FAIL);
            task.setError(e.getMessage());
          } finally {
            iExportTaskService.updateById(task);
          }
        });
  }

  private static final Integer PAGE_SIZE = 65535;

  /**
   * 导出封装
   *
   * @param pageQuery 分页查询参数
   * @param task 初始化导出任务
   * @param exportCount 导出数量
   * @param queryMethod ExportGateway中的数据查询
   * @param clazz 目标数据数据结构
   * @param function 结果转换函数
   * @return 又拍云地址
   */
  private <T> String export0(
      PageQuery pageQuery,
      ExportTask task,
      Integer exportCount,
      String queryMethod,
      Class<? extends ExportSheet> clazz,
      Function<T, ? extends ExportSheet> function) {

    long cycles =
        (exportCount % PAGE_SIZE) == 0
            ? (exportCount / PAGE_SIZE)
            : ((exportCount / PAGE_SIZE) + 1);

    ExportEntityPlus<T> entity = new ExportEntityPlus<>();
    entity.init(task, clazz, threadPoolExecutor.getThreadPoolExecutor());

    List<Callable<List<T>>> queryRunnableList = new LinkedList<>();
    for (int i = 1; i < cycles + 1; i++) {
      pageQuery.setPageIndex(i);
      pageQuery.setPageSize(PAGE_SIZE);
      if (pageQuery instanceof CombinationItemPageQuery) {
        CombinationItemPageQuery c = (CombinationItemPageQuery) pageQuery;
        c.setOffsetVal((long) (i - 1) * PAGE_SIZE);
        Callable<List<T>> callable = () -> ReflectUtil.invoke(exportGateway, queryMethod, c);
        queryRunnableList.add(callable);
      } else {
        Callable<List<T>> callable =
            () -> ReflectUtil.invoke(exportGateway, queryMethod, pageQuery);
        queryRunnableList.add(callable);
      }
    }
    entity.setCallableList(queryRunnableList);

    if (Objects.isNull(function)) {
      entity.export();
    } else {
      entity.export(function);
    }
    return uploadFile(entity.getExcelFile());
  }

  /**
   * 上传excel文件到又拍云
   *
   * @param tempFile
   * @return
   */
  public String uploadFile(File tempFile) {
    try {
      UploadFileAction action = UploadFileAction.ofFile(tempFile);
      return fileGateway.uploadFile(action).getUrl();
    } catch (Exception e) {
      log.error("上传又拍云异常", e);
      throw ExceptionPlusFactory.bizException(
          ErrorCode.ITEM_BIZ_ERROR, "上传又拍云异常，" + e.getMessage());
    } finally {
      if (!tempFile.delete()) {
        log.debug("缓存文件删除失败，" + tempFile.getAbsolutePath());
      }
    }
  }

  /**
   * 导出sku
   *
   * @param cmd
   * @param withPrice 是否带价格信息
   * @return
   */
  @SuppressWarnings("SimplifyStreamApiCallChains")
  @Deprecated
  public String exportSku(
      ExportCmd cmd,
      Boolean withPrice,
      Long createdUid,
      ExportTask exportTask,
      Integer exportCount) {
    int pageSize = 65535;
    long cycles =
        (exportCount % pageSize) == 0 ? (exportCount / pageSize) : ((exportCount / pageSize) + 1);

    ExportEntity<ExportSkuDto, ExportSkuDto> exportEntity2 =
        new ExportEntity<>(exportTask, exportCount, ExportSkuDto.class);
    // 构造查询线程集合
    List<Callable<List<ExportSkuDto>>> queryRunnableList = new LinkedList<>();
    for (int i = 0; i < cycles; i++) {
      ExportCmd exportCmd = ExportTransMapper.INSTANCE.itemExportCmdToCmd(cmd);
      exportCmd.setOffsetVal((long) i * pageSize);
      exportCmd.setSize(pageSize);
      QuerySkuExportDataCallable callable =
          new QuerySkuExportDataCallable(exportGateway, exportCmd);
      queryRunnableList.add(callable);
    }
    exportEntity2.setQueryCallableList(queryRunnableList);
    exportEntity2.export2(
        list -> {
          final List<Long> itemIds =
              list.stream().map(ExportSkuDto::getItemId).collect(Collectors.toList());
          final Map<Long, List<CorpBizTypeDTO>> corpBizTypeMap =
              bizLevelDivisionService.getCorpBizType(BizUnionTypeEnum.SPU, itemIds);
          return list.stream()
              .map(
                  exportSkuDto -> {
                    exportSkuDto.setStatusStr(
                        IEnum.getEnumByValue(ItemStatus.class, exportSkuDto.getStatus()).getDesc());
                    if (!withPrice && !Objects.equals(exportSkuDto.getCreatedUid(), createdUid)) {
                      exportSkuDto.setCostPrice("");
                      exportSkuDto.setSalePrice("");
                    }
                    final String platformCommission = exportSkuDto.getPlatformCommission();
                    final String contractSalePrice = exportSkuDto.getContractSalePrice();
                    exportSkuDto.setPlatformCommission(StrUtil.EMPTY);
                    exportSkuDto.setContractSalePrice(StrUtil.EMPTY);
                    final List<CorpBizTypeDTO> corpBizTypeDtos =
                        corpBizTypeMap.get(exportSkuDto.getItemId());
                    if (corpBizTypeDtos != null) {
                      final String corpBizTypeDescStr =
                          BizLevelDivisionConvert.INSTANCE.corpBizTypeListToDescStr(
                              corpBizTypeDtos);
                      exportSkuDto.setCorpBizType(corpBizTypeDescStr);
                      if (corpBizTypeDtos.stream()
                          .anyMatch(
                              corpBizTypeDTO ->
                                  corpBizTypeDTO.containBizType(
                                      DivisionLevelValueEnum.B_MERCHANT_ENTER))) {
                        exportSkuDto.setContractSalePrice(contractSalePrice);
                        if (platformCommission != null) {
                          exportSkuDto.setPlatformCommission(platformCommission + "%");
                        }
                      }
                    }
                    // 货品类型
                    exportSkuDto.setGoodsTypeStr(
                        IEnum.getEnumByValue(GoodsType.class, exportSkuDto.getGoodsType())
                            .getDesc());

                    exportSkuDto.setDownFrameTimeStr(
                        DateUtil.format(exportSkuDto.getDownFrameTime()));

                    return exportSkuDto;
                  })
              .collect(Collectors.toList());
        });
    return uploadFile(exportEntity2.getExcelFile());
  }

  private static class QuerySkuExportDataCallable implements Callable<List<ExportSkuDto>> {
    private final ExportGateway exportGateway;
    private final ExportCmd cmd;

    public QuerySkuExportDataCallable(ExportGateway exportGateway, ExportCmd cmd) {
      this.exportGateway = exportGateway;
      this.cmd = cmd;
    }

    @Override
    public List<ExportSkuDto> call() {
      return BizDivisionContext.invoke(
          BizUnionTypeEnum.SPU, c -> c.setBizIdRef("tmp.id"), () -> exportGateway.querySku(cmd));
    }
  }

  private static class QuerySkuNoPriceExportDataCallable
      implements Callable<List<ExportSkuNoPriceDto>> {
    private final ExportGateway exportGateway;
    private final ExportCmd cmd;

    public QuerySkuNoPriceExportDataCallable(ExportGateway exportGateway, ExportCmd cmd) {
      this.exportGateway = exportGateway;
      this.cmd = cmd;
    }

    @Override
    public List<ExportSkuNoPriceDto> call() {
      return exportGateway.querySkuNoPrice(cmd);
    }
  }

  /**
   * 导出商品，
   *
   * @param cmd
   * @param withPrice
   * @return
   */
  @Deprecated
  public String exportItem(
      ExportCmd cmd,
      Boolean withPrice,
      Long createdUid,
      ExportTask exportTask,
      Integer exportCount) {

    int pageSize = 65535;
    long cycles = (exportCount % 65535) == 0 ? (exportCount / 65535) : ((exportCount / 65535) + 1);

    ExportEntity<ExportItemDto, ExportItemDto> exportEntity2 =
        new ExportEntity<>(exportTask, exportCount, ExportItemDto.class);
    // 构造查询线程集合
    List<Callable<List<ExportItemDto>>> queryRunnableList = new LinkedList<>();
    for (int i = 0; i < cycles; i++) {
      ExportCmd exportCmd = ExportTransMapper.INSTANCE.itemExportCmdToCmd(cmd);
      exportCmd.setOffsetVal((long) i * pageSize);
      exportCmd.setSize(pageSize);
      QueryItemExportDataCallable callable =
          new QueryItemExportDataCallable(exportGateway, exportCmd);
      queryRunnableList.add(callable);
    }
    exportEntity2.setQueryCallableList(queryRunnableList);
    exportEntity2.export2(
        list -> {
          final List<Long> itemIds =
              list.stream().map(ExportItemDto::getItemId).collect(Collectors.toList());
          final Map<Long, List<CorpBizTypeDTO>> corpBizTypeMap =
              bizLevelDivisionService.getCorpBizType(BizUnionTypeEnum.SPU, itemIds);
          return list.stream()
              .map(
                  exportItemDto -> {
                    // 如果当前用户没有查看价格的权限，并且商品并不是此用户创建的。
                    if (!withPrice && !Objects.equals(exportItemDto.getCreatedUid(), createdUid)) {
                      exportItemDto.setProcurement("");
                      exportItemDto.setOtherProcurement("");
                      exportItemDto.setSalesPrice("");
                      exportItemDto.setLinePrice("");
                      exportItemDto.setActivityPrice("");
                      exportItemDto.setLowestPrice("");
                      exportItemDto.setOtherPrice("");
                    }
                    exportItemDto.setStatusStr(
                        IEnum.getEnumOptByValue(ItemStatus.class, exportItemDto.getStatus())
                            .map(ItemStatus::getDesc)
                            .orElse(""));
                    final List<CorpBizTypeDTO> corpBizTypeDtos =
                        corpBizTypeMap.get(exportItemDto.getItemId());
                    if (corpBizTypeDtos != null) {
                      exportItemDto.setCorpBizType(
                          BizLevelDivisionConvert.INSTANCE.corpBizTypeListToDescStr(
                              corpBizTypeDtos));
                    }
                    exportItemDto.setDownFrameTimeStr(
                        DateUtil.format(exportItemDto.getDownFrameTime()));

                    return exportItemDto;
                  })
              .collect(Collectors.toList());
        });
    return uploadFile(exportEntity2.getExcelFile());
  }

  private static class QueryItemExportDataCallable implements Callable<List<ExportItemDto>> {
    private final ExportGateway exportGateway;
    private final ExportCmd cmd;

    public QueryItemExportDataCallable(ExportGateway exportGateway, ExportCmd cmd) {
      this.exportGateway = exportGateway;
      this.cmd = cmd;
    }

    @Override
    public List<ExportItemDto> call() {
      return BizDivisionContext.invoke(
          BizUnionTypeEnum.SPU, c -> c.setBizIdRef("i.id"), () -> exportGateway.queryItem(cmd));
    }
  }

  private static class QueryItemNoPriceExportDataCallable
      implements Callable<List<ExportItemNoPriceDto>> {
    private final ExportGateway exportGateway;
    private final ExportCmd cmd;

    public QueryItemNoPriceExportDataCallable(ExportGateway exportGateway, ExportCmd cmd) {
      this.exportGateway = exportGateway;
      this.cmd = cmd;
    }

    @Override
    public List<ExportItemNoPriceDto> call() {
      return exportGateway.queryItemNoPrice(cmd);
    }
  }

  /**
   * 采购导出
   *
   * @param
   * @return
   */
  @Deprecated
  public String exportPurchase(
      PurchaseQueryPage queryPage, ExportTask exportTask, Integer exportCount) {

    int pageSize = 3000;
    long cycles = (exportCount % 3000) == 0 ? (exportCount / 3000) : ((exportCount / 3000) + 1);

    ExportEntity<Purchase, PurchaseSheet> exportEntity2 =
        new ExportEntity<>(exportTask, exportCount, PurchaseSheet.class);
    // 构造查询线程集合
    List<Callable<List<Purchase>>> queryRunnableList = new LinkedList<>();
    for (int i = 0; i < cycles; i++) {
      PurchaseQueryPage purchaseQueryPage =
          ExportTransMapper.INSTANCE.purchaseQueryPageToQuery(queryPage);
      purchaseQueryPage.setOffsetVal((long) i * pageSize);
      purchaseQueryPage.setPageSize(pageSize);
      QueryPurchaseDataCallable callable =
          new QueryPurchaseDataCallable(exportGateway, purchaseQueryPage);
      queryRunnableList.add(callable);
    }
    exportEntity2.setQueryCallableList(queryRunnableList);
    exportEntity2.export(getPurchaseFunc());

    return uploadFile(exportEntity2.getExcelFile());
  }

  /**
   * 退料出库导出
   *
   * @param query 查询条件
   * @param exportTask 导出任务
   * @param exportCount 导出数量
   * @return 地址
   */
  @Deprecated
  public String exportStockOutOrder(
      StockOutOrderQuery query, ExportTask exportTask, Integer exportCount) {
    int pageSize = 3000;
    long cycles = (exportCount % 3000) == 0 ? (exportCount / 3000) : ((exportCount / 3000) + 1);

    ExportEntity<StockOutOrderSheet, StockOutOrderSheet> exportEntity =
        new ExportEntity<>(exportTask, exportCount, StockOutOrderSheet.class);
    // 构造查询线程集合
    List<Callable<List<StockOutOrderSheet>>> queryRunnableList = new LinkedList<>();
    for (int i = 0; i < cycles; i++) {
      StockOutOrderQuery stockOutOrderQuery =
          ExportTransMapper.INSTANCE.stockOutOrderQueryToQuery(query);
      stockOutOrderQuery.setOffsetVal(i * pageSize);
      stockOutOrderQuery.setPageSize(pageSize);
      QueryStockOutOrderDataCallable callable =
          new QueryStockOutOrderDataCallable(exportGateway, stockOutOrderQuery);
      queryRunnableList.add(callable);
    }
    exportEntity.setQueryCallableList(queryRunnableList);
    exportEntity.export(getStockOutOrderFunc());

    return uploadFile(exportEntity.getExcelFile());
  }

  private static class QueryStockOutOrderDataCallable
      implements Callable<List<StockOutOrderSheet>> {
    private final ExportGateway exportGateway;
    private final StockOutOrderQuery query;

    public QueryStockOutOrderDataCallable(ExportGateway exportGateway, StockOutOrderQuery query) {
      this.exportGateway = exportGateway;
      this.query = query;
    }

    @Override
    public List<StockOutOrderSheet> call() {
      return exportGateway.queryStockOutOrder(query);
    }
  }

  private static class QueryPurchaseDataCallable implements Callable<List<Purchase>> {
    private final ExportGateway exportGateway;
    private final PurchaseQueryPage query;

    public QueryPurchaseDataCallable(ExportGateway exportGateway, PurchaseQueryPage query) {
      this.exportGateway = exportGateway;
      this.query = query;
    }

    @Override
    public List<Purchase> call() {
      return exportGateway.queryPurchase(query);
    }
  }

  private Function<Purchase, PurchaseSheet> getPurchaseFunc() {
    return o -> {
      final PurchaseSheet purchaseSheet = PurchaseTransMapper.INSTANCE.purchaseDbToSheet(o);
      StaffInfo staffInfo = userGateway.queryStaffInfoById(purchaseSheet.getBuyerId());
      purchaseSheet.setBuyerName(Objects.nonNull(staffInfo) ? staffInfo.getNickname() : "");

      //      final String itemSku = purchaseSheet.getItemSku();

      //      final String coryTypeStr =
      //          Arrays.stream(StringUtil.splitToInt(o.getCoryTypeStr(), ","))
      //              .boxed()
      //              .collect(Collectors.toList())
      //              .stream()
      //              .map(val -> IEnum.getEnumByValue(DivisionLevelValueEnum.class, val).getDesc())
      //              .collect(Collectors.joining(","));
      //      purchaseSheet.setCoryType(coryTypeStr);

      return purchaseSheet;
    };
  }

  private Function<StockOutOrderSheet, StockOutOrderSheet> getStockOutOrderFunc() {
    return o -> {
      o.setIsGift("1".equals(o.getIsGift()) ? "是" : "否");
      o.setReturnType(ReturnTypeEnum.getDesc(Integer.valueOf(o.getReturnType())));
      o.setReturnMode(ReturnModeEnum.getDesc(Integer.valueOf(o.getReturnMode())));
      return o;
    };
  }

  //    /**
  //     * 组合商品导出
  //     *
  //     * @param combinationItemPageQuery
  //     * @return
  //     */
  //    @SuppressWarnings(value = "uncheck")
  //    @Deprecated
  //    public String exportCombinationItem(CombinationItemPageQuery combinationItemPageQuery,
  //                                        Boolean canViewComposerSkuPrice, Boolean
  // canViewCombinationItemPrice,
  //                                        ExportTask exportTask, Integer exportCount) {
  //
  //        int pageSize = 3000;
  //        long cycles = (exportCount % 3000) == 0 ? (exportCount / 3000) : ((exportCount / 3000) +
  // 1);
  //
  //        ExportEntity combinationExportEntity = getCombinationExportEntity(exportTask,
  // exportCount, canViewComposerSkuPrice, canViewCombinationItemPrice);
  //        // 构造查询线程集合
  //        List<Callable<List<ComposerSkuAllSheet>>> queryRunnableList = new LinkedList<>();
  //        for (int i = 0; i < cycles; i++) {
  //            CombinationItemPageQuery query =
  // ExportTransMapper.INSTANCE.combinationItemExportQueryToQuery(combinationItemPageQuery);
  //            query.setOffsetVal((long) i * pageSize);
  //            query.setPageSize(pageSize);
  //            QueryCombinationItemDataCallable callable = new
  // QueryCombinationItemDataCallable(exportGateway, query);
  //            queryRunnableList.add(callable);
  //        }
  //        combinationExportEntity.setQueryCallableList(queryRunnableList);
  //        combinationExportEntity.export(getCombinationExportFunction(canViewComposerSkuPrice,
  // canViewCombinationItemPrice));
  //        return uploadFile(combinationExportEntity.getExcelFile());
  //
  //    }
  //
  //    private static class QueryCombinationItemDataCallable implements
  // Callable<List<ComposerSkuAllSheet>> {
  //        private final ExportGateway exportGateway;
  //        private final CombinationItemPageQuery query;
  //
  //        public QueryCombinationItemDataCallable(ExportGateway exportGateway,
  // CombinationItemPageQuery query) {
  //            this.exportGateway = exportGateway;
  //            this.query = query;
  //        }
  //
  //        @Override
  //        public List<ComposerSkuAllSheet> call() {
  //            return exportGateway.queryCombinationItem(query);
  //        }
  //    }
  //
  //
  //    private ExportEntity getCombinationExportEntity(ExportTask exportTask, Integer exportCount,
  //                                                    Boolean canViewComposerSkuPrice, Boolean
  // canViewCombinationItemPrice) {
  //        if (!canViewComposerSkuPrice && !canViewCombinationItemPrice) {
  //            return new ExportEntity<ComposerSkuAllSheet, ComposeSkuBaseSheet>(exportTask,
  // exportCount, ComposeSkuBaseSheet.class);
  //        }
  //        if (canViewComposerSkuPrice && canViewCombinationItemPrice) {
  //            return new ExportEntity<ComposerSkuAllSheet, ComposerSkuAllSheet>(exportTask,
  // exportCount, ComposerSkuAllSheet.class);
  //        }
  //        if (canViewComposerSkuPrice) {
  //            return new ExportEntity<ComposerSkuAllSheet,
  // ComposerSkuWithSkuPriceSheet>(exportTask, exportCount, ComposerSkuWithSkuPriceSheet.class);
  //        }
  //        return new ExportEntity<ComposerSkuAllSheet, ComposerSkuWithItemPriceSheet>(exportTask,
  // exportCount, ComposerSkuWithItemPriceSheet.class);
  //    }
  //
  //    private Function getCombinationExportFunction(Boolean canViewComposerSkuPrice, Boolean
  // canViewCombinationItemPrice) {
  //
  //        if (!canViewComposerSkuPrice && !canViewCombinationItemPrice) {
  //            return (Function<ComposerSkuAllSheet, ComposeSkuBaseSheet>) o ->
  // CombinationItemTransMapper.INSTANCE.allSheetToBaseSheet(o);
  //        }
  //        if (canViewComposerSkuPrice && canViewCombinationItemPrice) {
  //            return (Function<ComposerSkuAllSheet, ComposerSkuAllSheet>) o -> o;
  //        }
  //        if (canViewComposerSkuPrice) {
  //            return (Function<ComposerSkuAllSheet, ComposerSkuWithSkuPriceSheet>) o ->
  // CombinationItemTransMapper.INSTANCE.allSheetToSkuPriceSheet(o);
  //        }
  //        return (Function<ComposerSkuAllSheet, ComposerSkuWithItemPriceSheet>) o ->
  // CombinationItemTransMapper.INSTANCE.allSheetToItemPriceSheet(o);
  //    }

}
