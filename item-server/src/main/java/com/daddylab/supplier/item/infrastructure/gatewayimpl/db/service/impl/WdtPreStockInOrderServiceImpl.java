package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtPreStockInOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtPreStockInOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtPreStockInOrderService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 旺店通预入库单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-17
 */
@Service
public class WdtPreStockInOrderServiceImpl extends DaddyServiceImpl<WdtPreStockInOrderMapper, WdtPreStockInOrder> implements
        IWdtPreStockInOrderService {

}
