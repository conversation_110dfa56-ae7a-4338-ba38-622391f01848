package com.daddylab.supplier.item.application.nuonuo;

import cn.hutool.core.util.StrUtil;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.job.extend.autoRegister.XxlJobAutoRegister;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NuoNuoInvoiceProcessRecord;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INuoNuoInvoiceProcessRecordService;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * <AUTHOR> up
 * @date 2024年07月27日 4:19 PM
 */
@Component
@Slf4j
public class NuoNuoInvoiceBizJob {

    @Resource
    INuoNuoInvoiceProcessRecordService iNuoNuoInvoiceProcessRecordService;
    @Resource
    NuoNuoBizService nuoNuoBizService;

    /**
     * 每隔2个小时，监控异常数量
     */
    @XxlJob("NuoNuoInvoiceBizJob:errorScan")
    @XxlJobAutoRegister(cron = "0 0 0/1 * * ? ", author = "七喜", jobDesc = "[诺说通]异常记录扫描")
    public void errorScan() {
        final Integer count = iNuoNuoInvoiceProcessRecordService.lambdaQuery()
                .lt(NuoNuoInvoiceProcessRecord::getStatus, 0)
                .count();
        if (count > 0) {
            String text = StrUtil.format("诺税通开票流程，存在:{}条异常记录，请及时处理。统计时间:{}", count, DateUtil.format(DateUtil.now()));
            Alert.text(MessageRobotCode.GLOBAL, text);
        }
    }

    @XxlJob("NuoNuoInvoiceBizJob:creditNoteScan")
    @XxlJobAutoRegister(cron = "0 0 6 * * ?", author = "七喜", jobDesc = "[诺说通]全额冲红扫描")
    public void creditNoteScan() {
        final LocalDate localDate = LocalDate.now().plusDays(-1);

        LocalDateTime startOfToday = localDate.atStartOfDay();
        LocalDateTime startOfTomorrow = localDate.plusDays(1).atStartOfDay();
        long startOfTodayTimestamp = startOfToday.toEpochSecond(ZoneOffset.ofHours(0));
        long startOfTomorrowTimestamp = startOfTomorrow.toEpochSecond(ZoneOffset.ofHours(0));

        nuoNuoBizService.creditNoteApply(startOfTodayTimestamp, startOfTomorrowTimestamp);
    }
}
