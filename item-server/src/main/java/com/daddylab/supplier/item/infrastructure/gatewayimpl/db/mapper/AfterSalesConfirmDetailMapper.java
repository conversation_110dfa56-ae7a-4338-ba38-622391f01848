package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesConfirmDetail;

/**
 * <p>
 * 售后单销退确认单据明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
public interface AfterSalesConfirmDetailMapper extends DaddyBaseMapper<AfterSalesConfirmDetail> {

}
