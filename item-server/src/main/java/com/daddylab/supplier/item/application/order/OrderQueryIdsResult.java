package com.daddylab.supplier.item.application.order;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/6/2
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrderQueryIdsResult {

    Long total;

    List<Long> ids;

    public static OrderQueryIdsResult of(Long total, List<Long> ids) {
        final OrderQueryIdsResult orderQueryIdsResult = new OrderQueryIdsResult();
        orderQueryIdsResult.setTotal(total);
        orderQueryIdsResult.setIds(ids);
        return orderQueryIdsResult;
    }
}
