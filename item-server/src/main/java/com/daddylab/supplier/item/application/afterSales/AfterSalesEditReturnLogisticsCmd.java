package com.daddylab.supplier.item.application.afterSales;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/21
 */
@Data
@ApiModel("编辑退货物流命令参数封装")
public class AfterSalesEditReturnLogisticsCmd {

    /**
     * 退换单号
     */
    @ApiModelProperty("退换单号")
    @NotBlank(message = "退换单号不能为空")
    private String returnOrderNo;

    @ApiModelProperty("退回物流")
    @NotBlank(message = "退回物流不能为空")
    String returnLogisticsName;

    @ApiModelProperty("退回物流单号")
    @NotBlank(message = "退回物流单号不能为空")
    String returnLogisticsNo;

    @ApiModelProperty("退货仓库编号")
    @NotBlank(message = "退货仓库编号不能为空")
    String returnWarehouseNo;

    /**
     * 联系地址
     */
    @ApiModelProperty("联系地址")
    @NotBlank(message = "联系地址不能为空")
    private String fullAddress;

    /**
     * 退货联系人
     */
    @ApiModelProperty("退货联系人")
    @NotBlank(message = "退货联系人不能为空")
    private String contacts;

    /**
     * 退货联系电话
     */
    @ApiModelProperty("退货联系电话")
    @NotBlank(message = "退货联系电话不能为空")
    private String tel;

}
