package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.daddylab.supplier.item.infrastructure.domain.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 标注图片
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ItemDrawerMarkImage extends Entity {

    private static final long serialVersionUID = 1L;

    /**
     * 抽屉id
     */
    private Long drawerId;

    /**
     * 标注图片
     */
    private String url;

    /**
     * 标注元数据
     */
    private String markMeta;

}
