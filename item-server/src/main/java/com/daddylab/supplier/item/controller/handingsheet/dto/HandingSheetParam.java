package com.daddylab.supplier.item.controller.handingsheet.dto;

import com.daddylab.supplier.item.types.handingsheet.HandingSheetItemVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/24 14:23
 * @Description: 增加/修改盘货表参数
 */
@Data
@ApiModel("增加或编辑盘货表参数")
public class HandingSheetParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "盘货表主键ID（新增时不传，编辑时传参）")
    private Long id;

    @ApiModelProperty(value = "表格名称")
    @NotBlank(message = "商品名称不能为空")
    private String name;

    @ApiModelProperty(value = "活动开始时间（时间戳，单位秒）")
    @NotNull(message = "活动开始时间不能为空")
    private Long startTime;

    @ApiModelProperty(value = "活动结束时间（时间戳，单位秒）")
    @NotNull(message = "活动结束时间不能为空")
    private Long endTime;

    @ApiModelProperty(value = "是否在审核")
    private Boolean audit;

    /**
     * {@link com.daddylab.supplier.item.domain.common.enums.Platform}
     */
    @ApiModelProperty(value = "所属平台（0-其他，1-淘宝，2-有赞，3-抖店，4-快手小店，5-小红书，7-小程序）")
    @NotEmpty(message = "所属平台不能为空")
    private List<Integer> platformVals;

    @ApiModelProperty(value = "活动标签")
    private String label;

    @ApiModelProperty(value = "活动成员 uid（职员的 uid）")
    @NotEmpty(message = "活动成员不能为空")
    private List<Long> staffUids;

    @ApiModelProperty(value = "附件 json 格式", example = "[{\"1\":\"www.baidu.com\"},{\"2\":\"www.ailibaba.com\"}]")
    private String attachments;

    /**
     * {@link com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.HandingSheetActivityTypeEnum}
     */
    @ApiModelProperty(value = "活动类型（1-循环满减，2-阶梯满减）")
    @NotNull(message = "活动类型必填")
    private Integer activityType;

    @ApiModelProperty(value = "关联的活动力度")
    @NotEmpty(message = "活动力度必填")
    private List<HandingSheetActivityEventParam> activityEvents;

    @ApiModelProperty(value = "关联的商品信息")
    @Null(message = "itemInfos 字段已在新版本中被废除，请使用新的")
    private List<HandingSheetItemParam> itemInfos;

    @JsonIgnore
    private boolean useNewVersionView = true;

    @ApiModelProperty(value = "关联的商品信息")
    @NotEmpty(message = "关联的商品信息必填")
    private List<HandingSheetItemVO> sheetItems;
}
