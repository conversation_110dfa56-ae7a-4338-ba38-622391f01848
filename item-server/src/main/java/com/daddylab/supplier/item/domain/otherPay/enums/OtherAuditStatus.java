package com.daddylab.supplier.item.domain.otherPay.enums;

import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName OrderAuditStatus.java
 * @description 其他应付订单审核状态枚举
 * @createTime 2022年04月11日 14:35:00
 */
@Getter
@AllArgsConstructor
public enum OtherAuditStatus implements IEnum<Integer> {
    FINISH(0, "已完成"),
    NO_COMMIT(1, "待提交"),
    WAIT_AUDIT(2, "待审核"),
    AUDIT_PASS(3, "审核通过"),
    REFUSE(4, "审核拒绝");
    final public Integer value;
    final public String desc;


    OtherAuditStatus(Integer value, String name, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 获取枚举值
     *
     * @return
     */
    public static OtherAuditStatus of(Integer value) {
        for (OtherAuditStatus otherAuditStatus : OtherAuditStatus.values()) {
            if (otherAuditStatus.getValue().equals(value)) {
                return otherAuditStatus;
            }
        }
        return null;
    }
}
