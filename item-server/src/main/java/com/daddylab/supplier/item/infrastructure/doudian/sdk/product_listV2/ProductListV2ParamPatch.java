package com.daddylab.supplier.item.infrastructure.doudian.sdk.product_listV2;

import com.doudian.open.annotation.OpField;
import com.doudian.open.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/4/2
 */
public class ProductListV2ParamPatch extends com.doudian.open.api.product_listV2.param.ProductListV2Param {
    @SerializedName("product_id")
    @OpField(
            required = false,
            desc = "商品id，最大支持传入100个；",
            example = "3600137140018749665"
    )
    @Getter
    @Setter
    private List<Long> productId;
}
