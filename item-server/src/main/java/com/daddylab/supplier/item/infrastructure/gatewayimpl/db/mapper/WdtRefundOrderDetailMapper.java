package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.daddylab.supplier.item.application.purchase.order.factory.dto.WdtRefundManageDO;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyBaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrderDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 旺店通退换单明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-06
 */
@Repository
public interface WdtRefundOrderDetailMapper extends DaddyBaseMapper<WdtRefundOrderDetail> {

    /**
     * 批量插入或者已存在重复记录时更新数据
     *
     * @param details 数据集合
     */
    void saveOrUpdateBatch(@Param("details") Collection<WdtRefundOrderDetail> details);


    List<WdtRefundManageDO> selectRefundList(@Param("startDt") String startDt, @Param("endDt") String endDt,
                                             @Param("eliminateWarehouseNos") List<String> eliminateWarehouseNos,
                                             @Param("externalShopNos") List<String> externalShopNos);

}
