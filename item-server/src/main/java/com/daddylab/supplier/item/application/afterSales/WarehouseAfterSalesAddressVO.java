package com.daddylab.supplier.item.application.afterSales;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 仓库售后退回地址
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WarehouseAfterSalesAddressVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     **/
    @ApiModelProperty("id")
    private Long id;

    /**
     * 仓库编号
     **/
    @ApiModelProperty("仓库编号")
    private String warehouseNo;

    /**
     * 退回地址
     **/
    @ApiModelProperty("退回地址")
    private String fullAddress;

    /**
     * 退货联系人
     **/
    @ApiModelProperty("退货联系人")
    private String contacts;

    /**
     * 退货联系电话
     **/
    @ApiModelProperty("退货联系电话")
    private String tel;

    /**
     * P系统供应商ID
     **/
    @ApiModelProperty("P系统供应商ID")
    private Long partnerProviderId;

    /**
     * 仓库名称
     **/
    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("省区域码")
    private String provinceCode;

    @ApiModelProperty("市区域码")
    private String cityCode;

    @ApiModelProperty("区县区域码")
    private String areaCode;

    @ApiModelProperty("前端不传")
    private String addressCodeStr;


}
