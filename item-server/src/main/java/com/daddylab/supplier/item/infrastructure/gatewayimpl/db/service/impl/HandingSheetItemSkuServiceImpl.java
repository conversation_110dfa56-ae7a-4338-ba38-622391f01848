package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.HandingSheetItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.HandingSheetItemSkuMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IHandingSheetItemSkuService;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <p>
 * 盘货表SKU纬度数据（V2.4.5新版盘货表） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Service
public class HandingSheetItemSkuServiceImpl extends DaddyServiceImpl<HandingSheetItemSkuMapper, HandingSheetItemSku> implements IHandingSheetItemSkuService {

    @Override
    public int getToBeConfirmedItemNum(Long handingSheetId) {
        return lambdaQuery().eq(HandingSheetItemSku::getHandingSheetId, handingSheetId)
                            .eq(HandingSheetItemSku::getIsPassed, 0)
                            .count();
    }

    @Override
    public Optional<Long> getHandingSheetItemSpuId(Long handingSheetItemSkuId) {
        return lambdaQuery()
                .eq(HandingSheetItemSku::getId, handingSheetItemSkuId)
                .select(HandingSheetItemSku::getHandingSheetItemSpuId)
                .oneOpt()
                .map(HandingSheetItemSku::getHandingSheetItemSpuId);
    }

    @Override
    public int countItemSpuByHandingSheetId(Long handingSheetId) {
        return getDaddyBaseMapper().countItemSpuByHandingSheetId(handingSheetId);

    }


}
