package com.daddylab.supplier.item.infrastructure.config;

import com.daddylab.supplier.item.application.auth.ExternalUserLoginLogic;
import com.daddylab.supplier.item.domain.auth.gateway.LoginGateway;
import com.daddylab.supplier.item.infrastructure.skywalking.TraceFilter;
import com.daddylab.supplier.item.infrastructure.spring.web.CustomRequestLoggingFilter;
import com.daddylab.supplier.item.infrastructure.spring.web.XssConfig;
import com.daddylab.supplier.item.infrastructure.spring.web.XssFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;

import static org.springframework.boot.web.reactive.filter.OrderedWebFilter.REQUEST_WRAPPER_FILTER_MAX_ORDER;

/**
 * <AUTHOR>
 * @since 2022/7/13
 */
@Configuration
public class FilterConfig {

    public static final int LOGGING_FILTER_ORDER = REQUEST_WRAPPER_FILTER_MAX_ORDER - 105 + 1;

    @Bean
    public FilterRegistrationBean<TraceFilter> requestTraceFilter() {
        FilterRegistrationBean<TraceFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new TraceFilter());
        registration.addUrlPatterns("/*");
        registration.setName("traceFilter");
        registration.setOrder(FilterRegistrationBean.HIGHEST_PRECEDENCE);
        return registration;
    }

    @Bean
    public FilterRegistrationBean<Filter> requestLoggingFilter(LoginGateway loginGateway, ExternalUserLoginLogic externalUserLoginLogic) {
        final CustomRequestLoggingFilter filter = new CustomRequestLoggingFilter();
        filter.setLoginGateway(loginGateway);
        filter.setExternalUserLoginLogic(externalUserLoginLogic);
        FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<>();
        registration.setFilter(filter);
        registration.addUrlPatterns("/*");
        registration.setName("requestLoggingFilter");
        //必须在 OrderedRequestContextFilter 的后面
        registration.setOrder(LOGGING_FILTER_ORDER);
        return registration;
    }

    @Bean
    public FilterRegistrationBean<Filter> xssFilter(XssConfig xssConfig) {
        final XssFilter filter = new XssFilter(xssConfig);
        FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<>();
        registration.setFilter(filter);
        registration.addUrlPatterns("/*");
        registration.setName("xssFilter");
        registration.setOrder(LOGGING_FILTER_ORDER + 1);
        return registration;
    }

}
