package com.daddylab.supplier.item.domain.itemStock.enums;

import com.daddylab.supplier.item.infrastructure.enums.IIntegerEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum StockChangeType implements IIntegerEnum {
    /**
     * 入库
     */
    STOCK_IN(1, "入库"),
    /**
     * 重置库存
     */
    STOCK_SET(0, "设置"),
    /**
     * 出库
     */
    STOCK_OUT(-1, "出库"),
    ;
    final private Integer value;
    final private String desc;
}
