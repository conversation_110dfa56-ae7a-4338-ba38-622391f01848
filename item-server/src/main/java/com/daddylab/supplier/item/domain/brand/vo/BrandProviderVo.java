package com.daddylab.supplier.item.domain.brand.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(of = "providerId")
public class BrandProviderVo {
    Long providerId;
    String providerName;
    Long partnerProviderId;
    Integer isBlacklist;

    @Override
    public String toString() {
        final StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(providerName).append("-").append(providerId);
        if (isBlacklist != null && isBlacklist == 1) {
            stringBuilder.append("【黑】");
        }
        return stringBuilder.toString();
    }
}
