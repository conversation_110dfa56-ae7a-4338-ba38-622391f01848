package com.daddylab.supplier.item.application.platformItem.tasks;

import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.domain.dataFetch.PageFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.service.PlatformItemFetchService;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/12/31
 */
@Component
@Slf4j
public class WdtPlatformItemFetcher implements PageFetcher {

    final private PlatformItemFetchService platformItemFetchService;

    public WdtPlatformItemFetcher(
            PlatformItemFetchService platformItemFetchService
    ) {
        this.platformItemFetchService = platformItemFetchService;
    }

    @Override
    public int getTotal(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return platformItemFetchService
                    .count(startTime, endTime);
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapFetchException(e, fetchDataType());
        }
    }

    @Override
    public void fetch(LocalDateTime startTime, LocalDateTime endTime, long pageIndex, long pageSize,
            RunContext runContext) {
        try {
            platformItemFetchService.fetch(startTime, endTime, (int) pageIndex, (int) pageSize);
        } catch (WdtErpException e) {
            throw WdtExceptions.wrapFetchException(e, fetchDataType());
        }
    }


    @Override
    public FetchDataType fetchDataType() {
        return FetchDataType.WDT_PLATFORM_GOODS;
    }
}
