package com.daddylab.supplier.item.application.toDoList;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.ark.sailor.item.common.base.vo.Result;
import com.daddylab.ark.sailor.item.domain.vo.item.ItemListVO;
import com.daddylab.ark.sailor.item.domain.vo.item.ItemSkuVO;
import com.daddylab.ark.sailor.item.domain.vo.item.SkuSpecVO;
import com.daddylab.ark.sailor.item.enums.ItemShelfStatusEnum;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.platformItem.PlatformItemBizService;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.item.dto.ItemAttrDto;
import com.daddylab.supplier.item.controller.item.dto.ItemSkuListDto;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailBaseVo;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailImageVo;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailVo;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.infrastructure.fileStore.StorageService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DadListItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DadListItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDadListItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemCodeRefService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IProviderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.ArkSailorItemFeignClient;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR> up
 * @date 2024年10月11日 4:45 PM
 */
@Service
@Slf4j
public class ImportItemExcelService {

    @Resource
    IDadListItemService iDadListItemService;
    @Resource
    DadListItemMapper dadListItemMapper;
    @Resource
    ItemGateway itemGateway;
    @Resource
    PlatformItemBizService platformItemService;
    @Resource
    ItemBizService itemBizService;
    @Resource
    IProviderService iProviderService;
    @Resource
    IPlatformItemService iPlatformItemService;
    @Resource
    ArkSailorItemFeignClient arkSailorItemFeignClient;

    @Resource
    IItemCodeRefService iItemCodeRefService;
//    @Resource
//    FileGateway fileGateway;

    private String regex = "[\\(（\\[【].*?[\\)）\\]】]";

    public void deleteAllData() {
        dadListItemMapper.deleteAllData();
    }

    public void run(InputStream inputStream) {

        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            try {
                List<ItemInfoDto> sheetRowsRaw = EasyExcel.read(inputStream)
                        .headRowNumber(1)
                        .head(ItemInfoDto.class).sheet(0).doReadSync();
                List<ItemInfoDto> sheetRows = sheetRowsRaw.stream().distinct().collect(Collectors.toList());
                log.info("老爸清单商品数据导入开始，总计{}行，去重后{}行", sheetRowsRaw.size(), sheetRows.size());

                List<DadListItem> list = new LinkedList<>();
                for (ItemInfoDto itemInfoDto : sheetRowsRaw) {

                    try {
                        final DadListItem convert = convert(itemInfoDto);
                        if (Objects.nonNull(convert)) {
                            list.add(convert);
                            log.info("老爸清单list code:{},size:{}", itemInfoDto.getErpCode(), list.size());
                        }
                    } catch (Exception e) {
                        log.error("老爸清单商品数据导入异常.param:{}", JsonUtil.toJson(itemInfoDto), e);
                    }
                }
                log.info("老爸清单商品数据导入完成，总计{}行，去重后{}行", sheetRowsRaw.size(), sheetRows.size());

                if (CollUtil.isEmpty(list)) {
                    return;
                }
                iDadListItemService.saveBatch(list);
            } catch (Exception e) {
                log.error("老爸清单相关接口-导入商品Excel error", e);
            }
        });
    }

    private DadListItem convert(ItemInfoDto itemInfoDto) {
        if (StrUtil.isBlank(itemInfoDto.getErpCode())) {
            return null;
        }

        Item item;
        item = itemGateway.getItem(itemInfoDto.getErpCode());
        if (Objects.isNull(item)) {
            item = itemGateway.getItem(itemInfoDto.getErpCode() + "01");
            if (Objects.isNull(item)) {
                item = itemGateway.getItem(itemInfoDto.getErpCode() + "02");
                if (Objects.isNull(item)) {
                    return null;
                }
            }
        }

//        final List<ItemCodeRef> itemCodeRefList = iItemCodeRefService.lambdaQuery()
//                .eq(ItemCodeRef::getCode, itemInfoDto.getErpCode())
//                .orderByDesc(ItemCodeRef::getId)
//                .last("limit 1")
//                .list();
//        if (CollUtil.isNotEmpty(itemCodeRefList)) {
//            final Long itemId = itemCodeRefList.get(0).getItemId();
//            item = itemGateway.getItem(itemId);
//        }
        final String erpCode = itemInfoDto.getErpCode();

        ItemDetailVo itemDetailVo;
        ItemDetailBaseVo itemDetailBaseVo = null;
        List<ItemDetailImageVo> itemDetailImageVoList = null;
        List<ItemSkuListDto> itemDetailSkuVoList = null;
        if (Objects.nonNull(item)) {
            SingleResponse<ItemDetailVo> itemDetailVoSingleResponse = itemBizService.queryDetail(item.getId());
            if (itemDetailVoSingleResponse.isSuccess()) {
                itemDetailVo = itemDetailVoSingleResponse.getData();
                if (Objects.nonNull(itemDetailVo)) {
                    itemDetailBaseVo = itemDetailVo.getItemDetailBaseVo();
                    itemDetailImageVoList = itemDetailVo.getItemDetailImageVoList();
                    itemDetailSkuVoList = itemDetailVo.getItemDetailSkuVoList();
                }
            }
        }

        DadListItem dadListItem = new DadListItem();
        dadListItem.setPItemNo(itemInfoDto.getPSnNo());
        dadListItem.setErpItemId(item.getId());
        dadListItem.setErpItemCode(erpCode);
        dadListItem.setMallItemId(0L);
//            dadListItem.setModuleType();

        String secondCategoryName = "";
        String categoryPath = "";
        if (Objects.nonNull(itemDetailBaseVo)) {
            categoryPath = itemDetailBaseVo.getCategoryPath();
            if (StrUtil.isNotBlank(categoryPath)) {
                final String[] split = categoryPath.split("/");
                if (split.length >= 2) {
                    secondCategoryName = split[1];
                }
            }
        }
        dadListItem.setSecondCategoryName(secondCategoryName);
        dadListItem.setCategory(categoryPath);

//        String name = CollUtil.isNotEmpty(list1) ? list1.get(0).getGoodsName() : "";
        String name = item.getName();
        if (StrUtil.isNotBlank(name)) {
            if (name.contains("测试商品")) {
                return null;
            }
            name = name.replaceAll(regex, "");
        }
        dadListItem.setName(name);
        dadListItem.setBrand(Objects.nonNull(itemDetailBaseVo) ? itemDetailBaseVo.getBrandName() : "");

        List<String> imageList = new LinkedList<>();

//        if (CollUtil.isEmpty(imageList)) {
        if (CollUtil.isNotEmpty(itemDetailImageVoList)) {
            final Optional<ItemDetailImageVo> first = itemDetailImageVoList.stream().filter(val -> val.getIsMain() == 1).findFirst();
            first.ifPresent(itemDetailImageVo -> imageList.add(itemDetailImageVo.getImageUrl()));

            final List<String> collect = itemDetailImageVoList.stream().filter(val -> val.getIsMain() != 1).map(ItemDetailImageVo::getImageUrl).collect(toList());
            imageList.addAll(collect);
        }
//        }
//        final List<String> collect = imageList.stream().map(val -> {
//            try {
//                return watermark(val);
//            } catch (Exception e) {
//                log.error("添加水印失败。URL:{}", val, e);
//                return val;
//            }
//        }).collect(toList());
        dadListItem.setItemImg(JsonUtil.toJson(imageList));

        String specInfo = "";
//        if (mallItemId != 0) {
//            final Result<List<ItemSkuVO>> listResult = arkSailorItemFeignClient.itemSkuQueryByItemIds(ListUtil.of(mallItemId));
//            log.info("listResult:{}", listResult);
//            if (listResult.getCode() == 200 && CollUtil.isNotEmpty(listResult.getData())) {
//                specInfo = getSpecInfo0(listResult.getData());
//            }
//        }
//        if (StrUtil.isBlank(specInfo)) {
        specInfo = getSpecInfo(itemDetailSkuVoList);
//        }
        dadListItem.setSpecInfo(specInfo);
//            dadListItem.setTechnicalSummary();
//            dadListItem.setComposition();
//            dadListItem.setCommonCheckProjectId();
//            dadListItem.setLastCheckAt();
//            dadListItem.setProjectInfo();
        dadListItem.setOrganizationName(Objects.nonNull(itemDetailBaseVo) ? itemDetailBaseVo.getProviderName() : "");
        Long providerId = Objects.nonNull(itemDetailBaseVo) ? itemDetailBaseVo.getProviderId() : 0L;
        final Provider provider = iProviderService.getById(providerId);
        dadListItem.setOrganizationNo(Objects.nonNull(provider) ? provider.getUnifySocialCreditCodes() : "");
//            dadListItem.setOrganizationRole();
        dadListItem.setProvince(Objects.nonNull(provider) ? provider.getProvinceCode() : "");
        dadListItem.setCity(Objects.nonNull(provider) ? provider.getCityCode() : "");
        dadListItem.setArea(Objects.nonNull(provider) ? provider.getAreaCode() : "");
        dadListItem.setAddr(Objects.nonNull(provider) ? provider.getAddress() : "");
        dadListItem.setPartner(Objects.nonNull(provider) ? provider.getContact() : "");

        int m = 1;
        if (StrUtil.isNotBlank(itemInfoDto.getProcess())) {
            if (itemInfoDto.getProcess().contains("抽检")) {
                m = 2;
            }
        }
        dadListItem.setModuleType(m);

        final List<PlatformItem> platformItemList = iPlatformItemService.lambdaQuery()
                .likeRight(PlatformItem::getItemCode, erpCode)
                .eq(PlatformItem::getPlatform, Platform.LAOBASHOP)
                .list();
        if (CollUtil.isNotEmpty(platformItemList)) {
            final List<Long> mallIdList = platformItemList.stream().map(val -> Long.parseLong(val.getOuterItemId())).collect(toList());
            com.daddylab.ark.sailor.item.domain.query.item.ItemPageQuery pageQuery = new com.daddylab.ark.sailor.item.domain.query.item.ItemPageQuery();
            pageQuery.setItemIds(mallIdList);
            final Result<Page<ItemListVO>> pageResult = arkSailorItemFeignClient.itemPageQuery(pageQuery);
            if (pageResult.getCode() == 200 && pageResult.getData().getTotal() > 0) {
                final Optional<ItemListVO> onSaleOne = pageResult.getData().getRecords().stream()
                        .filter(val -> val.getShelfStatus().equals(ItemShelfStatusEnum.ON_SHELF.getValue()))
                        .findFirst();
                onSaleOne.ifPresent(itemListVO -> dadListItem.setMallItemId(itemListVO.getId()));
            }
        }

        return dadListItem;
    }

    private String getSpecInfo0(List<ItemSkuVO> itemSkuVOS) {
        if (CollUtil.isEmpty(itemSkuVOS)) {
            return "";
        }

        Set<String> attrList = new HashSet<>();
        List<Specs> valList = new LinkedList<>();

        for (ItemSkuVO itemSkuVO : itemSkuVOS) {
            if (CollUtil.isEmpty(itemSkuVO.getSkuSpecList())) {
                continue;
            }
            final List<String> collect = itemSkuVO.getSkuSpecList().stream().map(SkuSpecVO::getSpecName).collect(toList());
            attrList.addAll(collect);

            final List<String> collect2 = itemSkuVO.getSkuSpecList().stream().map(SkuSpecVO::getSpecValue).collect(toList());
            Specs specs = new Specs();
            specs.setAttrValue(collect2);
            valList.add(specs);
        }

        SpecInfo specInfo = new SpecInfo();
        specInfo.setAttrs(attrList);
        specInfo.setSpecs(valList);

        return JsonUtil.toJson(specInfo);
    }

    private String getSpecInfo(List<ItemSkuListDto> itemDetailSkuVoList) {
        if (CollUtil.isEmpty(itemDetailSkuVoList)) {
            return "";
        }

        final List<ItemAttrDto> attrList = itemDetailSkuVoList.get(0).getAttrList();
        if (CollUtil.isEmpty(attrList)) {
            return "";
        }
        final List<String> attrs = attrList.stream().map(ItemAttrDto::getName).collect(toList());

        List<Specs> specsList = new LinkedList<>();
        for (ItemSkuListDto itemSkuListDto : itemDetailSkuVoList) {
            Specs specs = new Specs();
            final List<String> collect = itemSkuListDto.getAttrList().stream().map(ItemAttrDto::getValue).collect(toList());
            specs.setAttrValue(collect);

            specsList.add(specs);
        }

        SpecInfo specInfo = new SpecInfo();
        specInfo.setAttrs(attrs);
        specInfo.setSpecs(specsList);

        return JsonUtil.toJson(specInfo);
    }

    @Data
    private static class SpecInfo {
        private Collection<String> attrs;

        private Collection<Specs> specs;
    }

    @Data
    private static class Specs {
        private List<String> attrValue;
    }

//    public static void main(String[] args) {
//        SpecInfo specInfo = new SpecInfo();
//        specInfo.setAttrs(ListUtil.of("颜色", "规格"));
//
//        Specs specs1 = new Specs();
//        specs1.setAttrValue(ListUtil.of("红色", "大号"));
//        Specs specs2 = new Specs();
//        specs2.setAttrValue(ListUtil.of("蓝色", "小号"));
//        specInfo.setSpecs(ListUtil.of(specs1, specs2));
//
//        System.out.println(JsonUtil.toJson(specInfo));
//
//    }

    public static void main(String[] args) {
        String input = "【工厂发货】涂抹式高保湿修护玻尿酸原液 14瓶*2ml/盒";
        String result = removeBracketsAndContent(input);
        System.out.println(result); // 输出: 这是一个测试和一些文字还有更多的内容
    }

    public static String removeBracketsAndContent(String input) {
        // 定义正则表达式，匹配三种括号及其中的内容
        String regex = "[\\(（\\[【].*?[\\)）\\]】]";

        // 使用replaceAll方法去除匹配的部分
        return input.replaceAll(regex, "");
    }


    @Resource
    ResourceLoader resourceLoader;

    @Autowired
    @Qualifier(StorageService.UPYUN)
    FileGateway fileGatewayUnyunImpl;

    public String watermark(String imgUrl) throws Exception {
        // 从 URL 加载原图
        URL url = new URL(imgUrl);
        BufferedImage originalImage = ImageIO.read(url);

        // 从 resources 加载水印图片
        org.springframework.core.io.Resource watermarkResource = resourceLoader.getResource("classpath:dlab_logo.png");
        InputStream watermarkStream = watermarkResource.getInputStream();

        // 加载原图和水印图片
        BufferedImage watermarkImage = ImageIO.read(watermarkStream);

        // 计算水印图片的新尺寸，原图长宽的1/9
        int watermarkWidth = originalImage.getWidth() / 9;
        int watermarkHeight = originalImage.getHeight() / 9;

        // 缩放水印图片到新尺寸
        Image scaledWatermark = watermarkImage.getScaledInstance(watermarkWidth, watermarkHeight, Image.SCALE_SMOOTH);

        // 创建一个图形环境在原图上绘制水印
        Graphics2D g2d = (Graphics2D) originalImage.getGraphics();

        // 设置透明度，如果需要可以设置半透明水印
        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 1.0f));

        // 在左上角绘制水印
        g2d.drawImage(scaledWatermark, 20, 20, null);

        g2d.dispose();

        File tempFile = File.createTempFile("watermarked_image", ".png");
        // 将加完水印的图片写入临时文件
        ImageIO.write(originalImage, "png", tempFile);
        // 返回临时文件的路径或者你可以将该文件上传到OSS
//        tempFile.getAbsolutePath();

        final UploadFileAction uploadFileAction = UploadFileAction.ofFile(tempFile);
        final FileStub fileStub = fileGatewayUnyunImpl.uploadFile(uploadFileAction);
        FileUtil.del(tempFile);

        return fileStub.getUrl();

//        String projectDir = System.getProperty("user.dir");
//        // 创建一个文件在当前项目目录下
//        File file = new File(projectDir + File.separator + System.currentTimeMillis() + ".jpg");
//        file.setReadable(true);
//        file.setWritable(true);
//        // 保存带水印的图片
//        ImageIO.write(originalImage, "jpg", file);
//
//        final UploadFileAction uploadFileAction = UploadFileAction.ofFile(file);
//        final FileStub fileStub = fileGatewayUnyunImpl.uploadFile(uploadFileAction);
//
//        FileUtil.del(file);
//
//        return fileStub.getUrl();

    }
}
