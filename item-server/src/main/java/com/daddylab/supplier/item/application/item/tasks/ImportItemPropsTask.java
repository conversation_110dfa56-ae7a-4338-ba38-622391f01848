package com.daddylab.supplier.item.application.item.tasks;

import static cn.hutool.poi.excel.ExcelUtil.colNameToIndex;

import cn.hutool.core.date.DatePattern;
import cn.hutool.poi.excel.cell.CellUtil;

import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;

import lombok.*;
import lombok.extern.slf4j.Slf4j;

import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/12/20
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ImportItemPropsTask {

    public static void main(String[] args) throws IOException {
        final String input = "/Users/<USER>/Desktop/供应链商品参数数据初始化【backup2】.xlsx";
        final ImportItemPropsTask importItemPropsTask =
                new ImportItemPropsTask(
                        new ItemPropsGateway() {
                            @Override
                            public Collection<Long> searchItem(
                                    Collection<String> generalizedCodes) {
                                return Arrays.asList(1L, 2L);
                            }

                            @Override
                            public boolean updateProps(
                                    Collection<Long> itemIds, Map<String, Object> props) {
                                return false;
                            }
                        });
        final String resultPath =
                importItemPropsTask.doTask(Files.newInputStream(Paths.get(input)));
        System.out.println(resultPath);
    }

    public interface ItemPropsGateway {
        Collection<Long> searchItem(Collection<String> generalizedCodes);

        boolean updateProps(Collection<Long> itemIds, Map<String, Object> props);
    }

    private final ItemPropsGateway itemPropsGateway;

    @Autowired private FileGateway fileGateway;

    @SneakyThrows
    public String doTask(InputStream inputStream) {
        final int cellIndexOfSpuCode = colNameToIndex("C");
        final int cellIndexOfPSysCode = colNameToIndex("D");
        final int cellIndexOfShelfLife = colNameToIndex("O");
        final int cellIndexOfNetContent = colNameToIndex("P");
        final int cellIndexOfResult = colNameToIndex("W");

        final String taskName =
                "import-item-props-results-"
                        + DateUtil.formatNow(DatePattern.PURE_DATETIME_PATTERN);
        final String resultFileName = taskName + ".xlsx";
        final Path resultPath = Paths.get("/tmp/" + resultFileName);
        final Path tempFile = Files.createFile(resultPath);
        try (final Workbook workbook = WorkbookFactory.create(inputStream);
                final OutputStream outputStream = Files.newOutputStream(tempFile); ) {
            final Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                throw new RuntimeException("工作表读取异常");
            }
            final Font fontOfHeader = workbook.createFont();
            fontOfHeader.setBold(true);

            final CellStyle cellStyleOfHeader = workbook.createCellStyle();
            cellStyleOfHeader.setAlignment(HorizontalAlignment.CENTER);
            cellStyleOfHeader.setVerticalAlignment(VerticalAlignment.CENTER);
            cellStyleOfHeader.setBorderRight(BorderStyle.THIN);
            cellStyleOfHeader.setBorderLeft(BorderStyle.THIN);
            cellStyleOfHeader.setBorderBottom(BorderStyle.THIN);
            cellStyleOfHeader.setBorderTop(BorderStyle.THIN);
            cellStyleOfHeader.setFont(fontOfHeader);

            final CellStyle cellStyleOfSuccess = workbook.createCellStyle();
            cellStyleOfSuccess.cloneStyleFrom(cellStyleOfHeader);

            final CellStyle cellStyleOfErr = workbook.createCellStyle();
            cellStyleOfErr.cloneStyleFrom(cellStyleOfHeader);
            cellStyleOfErr.setFillBackgroundColor(IndexedColors.RED.getIndex());
            cellStyleOfErr.setFillForegroundColor(IndexedColors.RED.getIndex());
            cellStyleOfErr.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            final CellStyle cellStyleOfWarn = workbook.createCellStyle();
            cellStyleOfWarn.cloneStyleFrom(cellStyleOfHeader);
            cellStyleOfWarn.setFillBackgroundColor(IndexedColors.YELLOW.getIndex());
            cellStyleOfWarn.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
            cellStyleOfWarn.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            Row header = null;
            for (int i = 0; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (header == null) {
                    header = row;
                    final Cell resultCell = header.createCell(cellIndexOfResult);
                    resultCell.setCellValue("处理结果");
                    resultCell.setCellStyle(cellStyleOfHeader);

                    continue;
                }
                final String spuCode =
                        CellUtil.getCellValue(row.getCell(cellIndexOfSpuCode)).toString();
                final String pSysCode =
                        CellUtil.getCellValue(row.getCell(cellIndexOfPSysCode)).toString();
                final String shelfLife =
                        CellUtil.getCellValue(row.getCell(cellIndexOfShelfLife)).toString();
                final String netContent =
                        CellUtil.getCellValue(row.getCell(cellIndexOfNetContent)).toString();
                log.info("【供应链商品参数数据初始化】处理中 | {} | {} | {} | {} |", spuCode, pSysCode, shelfLife, netContent);

                final Cell resultCell = row.createCell(cellIndexOfResult, CellType.BLANK);
                if (isBlank(spuCode) && isBlank(pSysCode)) {
                    resultCell.setCellValue("商品编码（SPU）和商品款号（P系统）为空");
                    resultCell.setCellStyle(cellStyleOfWarn);
                } else if (isBlank(shelfLife) && isBlank(netContent)) {
                    resultCell.setCellValue("保质期和净含量为空");
                    resultCell.setCellStyle(cellStyleOfWarn);
                } else {
                    final ArrayList<String> errors = new ArrayList<>();
                    final Collection<Long> itemIds =
                            itemPropsGateway.searchItem(Arrays.asList(spuCode, pSysCode));
                    if (itemIds.isEmpty()) {
                        resultCell.setCellValue("匹配不到后端商品");
                        resultCell.setCellStyle(cellStyleOfWarn);
                    } else {
                        final HashMap<String, Object> props = new HashMap<>();
                        if (isInvalidPropValue(shelfLife)) {
                            errors.add("保质期的格式不合法");
                        } else {
                            props.put("shelfLife", shelfLife);
                        }
                        if (isInvalidPropValue(netContent)) {
                            errors.add("净含量的格式不合法");
                        } else {
                            props.put("netContent", netContent);
                        }
                        if (!errors.isEmpty() && props.isEmpty()) {
                            resultCell.setCellValue(String.join("\n", errors));
                            resultCell.setCellStyle(cellStyleOfErr);
                        } else {
                            if (itemPropsGateway.updateProps(itemIds, props)) {
                                if (!errors.isEmpty()) {
                                    resultCell.setCellValue(
                                            "更新成功但存在问题:\n" + StringUtil.join("\n", errors));
                                    resultCell.setCellStyle(cellStyleOfWarn);
                                } else {
                                    resultCell.setCellValue("处理完成");
                                    resultCell.setCellStyle(cellStyleOfSuccess);
                                }
                            } else {
                                resultCell.setCellStyle(cellStyleOfErr);
                                if (!errors.isEmpty()) {
                                    resultCell.setCellValue(
                                            "更新属性失败，且存在以下问题:\n" + StringUtil.join("\n", errors));
                                } else {
                                    resultCell.setCellValue("更新属性失败");
                                }
                            }
                        }
                    }
                }
            }
            sheet.setColumnWidth(cellIndexOfResult, 64 * 256);
            workbook.write(outputStream);
            log.info("【供应链商品参数数据初始化】完成，处理结果已写入至文件:{}", resultPath);

            if (fileGateway != null) {
                UploadFileAction action =
                        UploadFileAction.ofInputStream(
                                Files.newInputStream(resultPath), resultFileName);
                final String downloadUrl = fileGateway.uploadFile(action).getUrl();
                log.info("【供应链商品参数数据初始化】处理结果已上传至:{}", downloadUrl);
                return downloadUrl;
            }
            return resultPath.toString();
        } catch (Exception e) {
            throw new RuntimeException("【供应链商品参数数据初始化】" + e.getMessage(), e);
        }
    }

    private static boolean isBlank(String value) {
        return StringUtil.isBlank(value) || "#N/A".equals(value);
    }

    private static boolean isInvalidPropValue(String value) {
        return value.contains("\n") || value.contains("，");
    }
}
