package com.daddylab.supplier.item.application.stockOutOrder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.purchase.PurchaseReturnAPI;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.PurchaseReturnCreateOrderDetail;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.PurchaseReturnCreateOrderOrderInfo;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.PurchaseReturnCreateOrderResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.StockSpecAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.dto.StockSpecSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.dto.StockSpecSearchResponse;
import com.daddylab.supplier.item.application.aws.event.AwsChangeEventListener;
import com.daddylab.supplier.item.application.aws.service.AwsApprovalFlowService;
import com.daddylab.supplier.item.application.common.event.StockInOrOutEvent;
import com.daddylab.supplier.item.application.criticalPush.ManagedPush;
import com.daddylab.supplier.item.application.criticalPush.SourceType;
import com.daddylab.supplier.item.application.criticalPush.TargetType;
import com.daddylab.supplier.item.application.item.combinationItem.CombinationItemBizService;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuVO;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.provider.ProviderBizService;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizService;
import com.daddylab.supplier.item.application.purchasePayable.dto.PurchasePayableCmd;
import com.daddylab.supplier.item.application.purchasePayable.service.PurchasePayableBizService;
import com.daddylab.supplier.item.application.stockInOrder.StockInOrderBizService;
import com.daddylab.supplier.item.application.system.BaseInfoService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.enums.DetailModelType;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PurchaseTypeEnum;
import com.daddylab.supplier.item.common.trans.StockOutOrderTransMapper;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.controller.provider.dto.ProviderVO;
import com.daddylab.supplier.item.controller.purchase.dto.order.CallBackOrderStateCmd;
import com.daddylab.supplier.item.controller.stockout.dto.*;
import com.daddylab.supplier.item.domain.auth.enums.ResourceType;
import com.daddylab.supplier.item.domain.daddylabWorkbench.DaddylabWorkbenchGateway;
import com.daddylab.supplier.item.domain.exportTask.ExportDomainService;
import com.daddylab.supplier.item.domain.exportTask.ExportType;
import com.daddylab.supplier.item.domain.exportTask.dto.ExportSheet;
import com.daddylab.supplier.item.domain.itemStock.gateway.WarehouseStockGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.stockInOrder.dto.StockInOrderDetailVo;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderQuery;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderSheet;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderStockQuery;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.domain.wdt.WdtExceptions;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserPermissionJudge;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.kingdee.dto.SubmitAndAuditRes;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @date 2022/3/24 17:55
 * @description StockOutOrderServiceImpl
 */
@Slf4j
@Service
public class StockOutOrderBizServiceImpl implements StockOutOrderBizService {

  @Autowired private StockOutOrderMapper stockOutOrderMapper;
  @Autowired private IStockOutOrderService iStockOutOrderService;
  @Autowired private IStockOutOrderDetailService iStockOutOrderDetailService;
  @Autowired private StockOutOrderDetailMapper stockOutOrderDetailMapper;
  @Autowired private OperateLogDomainService operateLogDomainService;
  @Autowired private ExportDomainService exportDomainService;

  @Autowired
  @Qualifier("awsApprovalFlowServiceImpl")
  private AwsApprovalFlowService awsApprovalFlowService;

  @Autowired private StockInOrderBizService stockInOrderBizService;
  @Autowired private BaseInfoService baseInfoService;
  @Autowired private WdtGateway wdtGateway;
  @Autowired private ProviderBizService providerBizService;
  @Resource private WarehouseMapper warehouseMapper;

  @Autowired
  @Qualifier("UserGatewayCacheImpl")
  private UserGateway userGateway;

  @Autowired private WarehouseStockGateway warehouseStockGateway;
  @Autowired IPurchaseOrderDetailService iPurchaseOrderDetailService;
  @Autowired PurchaseOrderMapper purchaseOrderMapper;
  @Autowired KingDeeTemplate kingDeeTemplate;
  @Autowired private StockInOrderDetailMapper stockInOrderDetailMapper;
  @Autowired CombinationItemBizService combinationItemBizService;
  @Autowired IItemStockService iItemStockService;
  @Resource PurchasePayableBizService purchasePayableBizService;
  @Resource IPurchaseOrderService iPurchaseOrderService;
  @Autowired private ProviderGateway providerGateway;
  @Resource ReqTemplate reqTemplate;

  @Resource DaddylabWorkbenchGateway daddylabWorkbenchGateway;

  @Resource IAwsBusinessLogService iAwsBusinessLogService;

  @Resource AwsChangeEventListener awsChangeEventListener;

  @Resource IOrganizationService iOrganizationService;

  @Resource IBizLevelDivisionService iBizLevelDivisionService;

  @Resource PurchaseOrderBizService purchaseOrderBizService;

  private void setPermission(StockOutOrderQuery query) {
    // 采购订单三波权限
    // 1.只能看到自己创建的
    // 2.能看到全部业务人员创建订单
    // 3.能看到系统创建订单
    boolean seeAllOrder =
        UserContext.hasPermission(GlobalConstant.STOCK_OUT_ORDER_ALL_URI, ResourceType.API);
    boolean seeSysOrder =
        UserContext.hasPermission(GlobalConstant.VIEW_STOCK_OUT_ORDER_WRITE_OFF, ResourceType.API);
    if (!seeAllOrder && !seeSysOrder) {
      query.setCreatedUidList(ListUtil.of(UserContext.getUserId()));
    }
    if (!seeAllOrder && seeSysOrder) {
      query.setCreatedUidList(ListUtil.of(UserContext.getUserId(), 0L));
    }
    if (seeAllOrder && !seeSysOrder) {
      query.setNeCreatedUidList(ListUtil.of(0L));
    }
  }

  @Override
  public PageResponse<StockOutOrderVO> pageStockOutOrder(StockOutOrderQuery stockOutOrderQuery) {
    //        // 数据权限查询
    //        boolean showAll = UserContext.hasPermission(GlobalConstant.STOCK_OUT_ORDER_ALL_URI);
    //        if (!showAll) {
    //            stockOutOrderQuery.setCreatedUid(UserContext.getUserId());
    //        }
    stockOutOrderQuery.setOffsetVal(stockOutOrderQuery.getOffset());
    baseInfoService.setOpenDetailModel(
        DetailModelType.STOCK_OUT_ORDER, stockOutOrderQuery.getShowDetail());

    //         是否有查看系统冲销单据的权限
    //
    // stockOutOrderQuery.setSeeHedge(UserContext.hasPermission(GlobalConstant.VIEW_STOCK_OUT_ORDER_WRITE_OFF, ResourceType.API));
    setPermission(stockOutOrderQuery);

    List<StockOutOrderVO> stockOutOrderVOList;
    List<StockOutOrderDetailVO> stockOutOrderDetailVOList;
    // 以明细为主查询列表
    if (StringUtils.isNotBlank(stockOutOrderQuery.getItemSkuCode())
        || StringUtils.isNotBlank(stockOutOrderQuery.getItemName())
        || Objects.nonNull(stockOutOrderQuery.getWarehouseNo())) {
      stockOutOrderDetailVOList = stockOutOrderDetailMapper.queryDetail(stockOutOrderQuery, null);
      if (stockOutOrderDetailVOList.isEmpty()) {
        return PageResponse.of(
            new ArrayList<>(),
            0,
            stockOutOrderQuery.getPageSize(),
            stockOutOrderQuery.getPageIndex());
      }

      List<Long> idList =
          stockOutOrderDetailVOList.stream()
              .map(StockOutOrderDetailVO::getStockOutOrderId)
              .distinct()
              .collect(Collectors.toList());
      stockOutOrderQuery.setIdList(idList);
      stockOutOrderVOList = stockOutOrderMapper.pageQuery(stockOutOrderQuery);
    } else {
      // 正常查询出库列表
      stockOutOrderVOList = stockOutOrderMapper.pageQuery(stockOutOrderQuery);
      if (stockOutOrderVOList.isEmpty()) {
        return PageResponse.of(
            new ArrayList<>(),
            0,
            stockOutOrderQuery.getPageSize(),
            stockOutOrderQuery.getPageIndex());
      }

      List<Long> idList =
          stockOutOrderVOList.stream()
              .map(StockOutOrderVO::getId)
              .distinct()
              .collect(Collectors.toList());
      stockOutOrderDetailVOList = stockOutOrderDetailMapper.queryDetail(stockOutOrderQuery, idList);
    }

    Integer pageCount = stockOutOrderMapper.pageCount(stockOutOrderQuery);
    if (stockOutOrderQuery.getShowDetail()) {
      for (StockOutOrderVO stockOutOrderVO : stockOutOrderVOList) {
        stockOutOrderVO.setStockOutOrderDetailList(
            stockOutOrderDetailVOList.stream()
                .filter(
                    stockOutOrderDetailVO ->
                        stockOutOrderDetailVO.getStockOutOrderId().equals(stockOutOrderVO.getId()))
                .collect(Collectors.toList()));
      }
    } else {
      stockOutOrderVOList.forEach(
          stockOutOrderVO -> stockOutOrderVO.setStockOutOrderDetailList(new ArrayList<>()));
    }
    List<Long> uIdList =
        stockOutOrderVOList.stream()
            .map(StockOutOrderVO::getCreatedUid)
            .collect(Collectors.toList());
    final Map<Long, StaffInfo> longStaffInfoMap = userGateway.batchQueryStaffInfoByIds(uIdList);

    List<Long> partnerProviderIds =
        stockOutOrderVOList.stream()
            .map(StockOutOrderVO::getPartnerProviderId)
            .collect(Collectors.toList());
    final Map<Long, PartnerProviderResp> longPartnerProviderRespMap =
        providerGateway.partnerBatchQueryByIds(partnerProviderIds);

    for (StockOutOrderVO stockOutOrderVO : stockOutOrderVOList) {
      final StaffInfo createdInfo =
          longStaffInfoMap.getOrDefault(stockOutOrderVO.getCreatedUid(), null);
      stockOutOrderVO.setCreatedName(Objects.isNull(createdInfo) ? "" : createdInfo.getNickname());

      // 累计实际出库数量
      // !!! 为啥这个字段 都没有有处理啊。
      stockOutOrderVO.setTotalReturnQuantity(getRealTotalReturnQuantity(stockOutOrderVO.getId()));

      final Optional<PartnerProviderResp> partnerProviderResp =
          Optional.ofNullable(
              longPartnerProviderRespMap.get(stockOutOrderVO.getPartnerProviderId()));
      stockOutOrderVO.setIsBlacklist(
          partnerProviderResp.map(PartnerProviderResp::getIsBlacklist).orElse(0));
    }

    return PageResponse.of(
        stockOutOrderVOList,
        pageCount,
        stockOutOrderQuery.getPageSize(),
        stockOutOrderQuery.getPageIndex());
  }

  @Override
  public SingleResponse<String> syncStockOutOrder(
      StockOutOrderCmd stockOutOrderCmd, Boolean mockSync) {
    StockOutOrder stockOutOrder = StockOutOrderTransMapper.INSTANCE.toEntity(stockOutOrderCmd);
    // 字段初始化
    initStockOutOrder(stockOutOrder, stockOutOrderCmd.getStockOutOrderDetailList());
    int sum =
        stockOutOrderCmd.getStockOutOrderDetailList().stream()
            .mapToInt(StockOutOrderDetailCmd::getReturnQuantity)
            .sum();
    stockOutOrder.setTotalReturnQuantity(sum);
    iStockOutOrderService.save(stockOutOrder);

    // 保存明细
    List<StockOutOrderDetailCmd> stockOutOrderDetailList;
    if (stockOutOrderCmd.getStockOutOrderDetailList() != null
        && !stockOutOrderCmd.getStockOutOrderDetailList().isEmpty()) {
      stockOutOrderDetailList = stockOutOrderCmd.getStockOutOrderDetailList();
      saveStockOutOrderDetailList(stockOutOrderDetailList, stockOutOrder);
    }

    if (mockSync) {
      return SingleResponse.of(stockOutOrder.getNo());
    }

    SingleResponse<String> syncRes = SingleResponse.of(stockOutOrder.getNo());
    if (iOrganizationService.isSyncKingDeeOrg(stockOutOrder.getPurchaseOrganizationId())) {
      syncRes = stockOutOrderSyncKingDee(stockOutOrder);
    }
    if (!syncRes.isSuccess()) {
      // 删除出库单
      iStockOutOrderService.removeById(stockOutOrder.getId());
      iStockOutOrderDetailService
          .lambdaUpdate()
          .eq(StockOutOrderDetail::getStockOutOrderId, stockOutOrder.getId())
          .remove();
    } else {
      PurchaseOrder purchaseOrder =
          iPurchaseOrderService.getById(stockOutOrder.getPurchaseOrderId());
      if (Objects.nonNull(purchaseOrder)) {
        PurchasePayableCmd cmd = new PurchasePayableCmd();
        cmd.setType(PurchaseTypeEnum.OUT_STOCK_PAYABLE.getValue());
        cmd.setRelatedOrderId(stockOutOrder.getId());
        cmd.setBuyerId(purchaseOrder.getBuyerUserId());
        cmd.setCreatedUid(purchaseOrder.getCreatedUid());
        purchasePayableBizService.create(cmd);
      }
    }

    return syncRes;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Response createOrUpdateStockOutOrder(StockOutOrderCmd stockOutOrderCmd) {

    // [20230919 七喜] 新增合作模式权限判断
    Long purchaseOrderId = stockOutOrderCmd.getPurchaseOrderId();
    if (!Objects.equals(purchaseOrderId, 0L)) {
      PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(purchaseOrderId);
      Integer businessLine = purchaseOrder.getBusinessLine();
      Assert.isTrue(
          UserPermissionJudge.getUserBusinessLineValues().contains(businessLine),
          "出库单不支持多种合作模式混合出库");
    }

    StockOutOrder stockOutOrder = StockOutOrderTransMapper.INSTANCE.toEntity(stockOutOrderCmd);
    // 字段初始化
    if (Objects.isNull(stockOutOrderCmd.getId())) {

      // 校验商品数量
      verifyStateAndStock(stockOutOrderCmd);
      initStockOutOrder(stockOutOrder, stockOutOrderCmd.getStockOutOrderDetailList());
      iStockOutOrderService.save(stockOutOrder);
      addOperateLog(stockOutOrder.getId(), "【提交】采退出库单。" + stockOutOrder.getNo());
    } else {
      StockOutOrder stockOutOrderById = iStockOutOrderService.getById(stockOutOrderCmd.getId());
      if (stockOutOrderById == null) {
        throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未查询到退料出库单，无法修改");
      }
      Boolean financialExecutive = UserPermissionJudge.isFinancialExecutive();
      if (financialExecutive) {
        Assert.state(
            stockOutOrderById.getCreatedUid().equals(UserContext.getUserId()),
            "当前用户角色只是财务主管。财务主管不允许编辑他人出库单");
      }

      if (!StockOutOrderState.isEdit(stockOutOrderById.getState())) {
        throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前数据状态不可该操作");
      }
      // 校验商品数量
      verifyStateAndStock(stockOutOrderCmd);
      stockOutOrder.setApprovalId(stockOutOrderById.getApprovalId());
      iStockOutOrderService.updateById(stockOutOrder);

      LambdaQueryWrapper<StockOutOrderDetail> queryWrapper = Wrappers.lambdaQuery();
      queryWrapper.eq(StockOutOrderDetail::getStockOutOrderId, stockOutOrderCmd.getId());
      iStockOutOrderDetailService.remove(queryWrapper);
      addOperateLog(stockOutOrder.getId(), "修改了采退出库单。" + stockOutOrderById.getNo());
    }
    // 保存明细
    if (stockOutOrderCmd.getStockOutOrderDetailList() != null
        && !stockOutOrderCmd.getStockOutOrderDetailList().isEmpty()) {
      List<StockOutOrderDetailCmd> stockOutOrderDetailList =
          stockOutOrderCmd.getStockOutOrderDetailList();
      saveStockOutOrderDetailList(stockOutOrderDetailList, stockOutOrder);
    }
    // 创建并提交审核流程
    if (stockOutOrderCmd.getState().equals(StockOutOrderState.NO_AUDIT.getValue())) {
      createAwsApproval(stockOutOrder);
    }
    return Response.buildSuccess();
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Response deleteStockOutOrder(Long id) {
    Assert.notNull(id, "退料出库id入参不得为空");

    StockOutOrder stockOutOrder = iStockOutOrderService.getById(id);
    if (Objects.isNull(stockOutOrder)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未查询到退料出库单，无法删除");
    }
    if (!StockOutOrderState.NO_SUBMIT.getValue().equals(stockOutOrder.getState())) {
      throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前数据状态不可该操作");
    }
    // 删除出库单
    iStockOutOrderService.removeById(id);
    // 删除出库单明细
    LambdaQueryWrapper<StockOutOrderDetail> queryWrapper = Wrappers.lambdaQuery();
    queryWrapper.eq(StockOutOrderDetail::getStockOutOrderId, id);
    iStockOutOrderDetailService.remove(queryWrapper);
    return Response.buildSuccess();
  }

  @Override
  public SingleResponse<StockOutOrderViewVO> getStockOutOrder(Long id) {
    Assert.notNull(id, "退料出库id入参不得为空");

    StockOutOrderViewVO stockOutOrderViewVO = stockOutOrderMapper.getStockOutOrderById(id);
    if (Objects.isNull(stockOutOrderViewVO)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未查询到退料出库单");
    }

    String nickname = "";
    try {
      StaffInfo staffInfo = userGateway.queryStaffInfoById(stockOutOrderViewVO.getCreatedUid());
      nickname = staffInfo.getNickname();
    } catch (Exception e) {
      if (SpringUtil.getActiveProfile().equals("test")) {
        nickname = "test";
      }
    }

    stockOutOrderViewVO.setCreatedName(nickname);
    List<StockOutOrderDetailVO> stockOutOrderDetailVOList =
        stockOutOrderDetailMapper.getByStockOutOrderId(id);

    final List<String> skuCodeList =
        stockOutOrderDetailVOList.stream()
            .map(StockOutOrderDetailVO::getItemSkuCode)
            .collect(Collectors.toList());
    final Map<String, List<CorpBizTypeDTO>> divisionMap =
        iBizLevelDivisionService.queryBySkuCode(skuCodeList);
    stockOutOrderDetailVOList.forEach(
        val ->
            val.setCorpBizType(
                divisionMap.getOrDefault(val.getItemSkuCode(), Collections.emptyList())));

    stockOutOrderViewVO.setStockOutOrderDetailList(stockOutOrderDetailVOList);

    stockOutOrderViewVO.setTotalReturnQuantity(getRealTotalReturnQuantity(id));

    return SingleResponse.of(stockOutOrderViewVO);
  }

  /**
   * 计算出库单实际单的出库数量
   *
   * @param stockOutId
   * @return
   */
  private Integer getRealTotalReturnQuantity(Long stockOutId) {
    List<StockOutOrderDetail> list =
        iStockOutOrderDetailService
            .lambdaQuery()
            .eq(StockOutOrderDetail::getStockOutOrderId, stockOutId)
            .select()
            .list();
    if (CollectionUtils.isNotEmpty(list)) {
      return list.stream()
          .mapToInt(
              value ->
                  Objects.isNull(value.getRealReturnQuantity()) ? 0 : value.getRealReturnQuantity())
          .sum();
    } else {
      return 0;
    }
  }

  @Override
  public Response cancelStockOutOrder(StockOutOrderIdDTO stockOutOrderIdDTO) {
    StockOutOrder stockOutOrder =
        iStockOutOrderService.getById(stockOutOrderIdDTO.getStockOutOrderId());
    if (Objects.isNull(stockOutOrder)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未查询到退料出库单，无法操作");
    }
    if (!StockOutOrderState.NO_STOCK_OUT.getValue().equals(stockOutOrder.getState())) {
      throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前数据状态不可该操作");
    }
    if (StrUtil.isNotBlank(stockOutOrder.getWdtStorageNo())) {
      try {
        final PurchaseReturnAPI api = wdtGateway.getAPI(PurchaseReturnAPI.class);
        api.cancelOrder(stockOutOrder.getWdtStorageNo());
      } catch (WdtErpException e) {
        throw WdtExceptions.wrapBizException(e);
      }
    }
    stockOutOrder.setState(StockOutOrderState.CANCEL.getValue());
    iStockOutOrderService.updateById(stockOutOrder);
    addOperateLog(stockOutOrder.getId(), "取消了采退出库单");

    return Response.buildSuccess();
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Response revocationStockOutOrder(StockOutOrderIdDTO stockOutOrderIdDTO) {
    StockOutOrder stockOutOrder =
        iStockOutOrderService.getById(stockOutOrderIdDTO.getStockOutOrderId());
    if (Objects.isNull(stockOutOrder)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未查询到退料出库单，无法操作");
    }
    if (!StockOutOrderState.NO_AUDIT.getValue().equals(stockOutOrder.getState())) {
      throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前数据状态不可该操作");
    }
    // 调用炎黄撤回
    if (Objects.nonNull(stockOutOrder.getApprovalId())) {
      awsApprovalFlowService.processRestart(stockOutOrder.getApprovalId());
    }
    stockOutOrder.setState(StockOutOrderState.REVOCATION.getValue());
    stockOutOrder.setApprovalAt(DateUtil.currentTime());
    iStockOutOrderService.updateById(stockOutOrder);
    addOperateLog(stockOutOrder.getId(), "撤回了采退出库单");

    return Response.buildSuccess();
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Response exportExcel(StockOutOrderQuery stockOutOrderQuery) {
    stockOutOrderQuery.setBusinessLine(
        UserPermissionJudge.filterBusinessLinePerm(stockOutOrderQuery.getBusinessLine()));
    Assert.isTrue(CollUtil.isNotEmpty(stockOutOrderQuery.getBusinessLine()), "无数据权限，无法导出");
    setPermission(stockOutOrderQuery);

    ExportTask exportTask = new ExportTask();
    exportTask.setStatus(ExportTaskStatus.RUNNING);
    exportTask.setName(
        ExportTaskType.STOCK_OUT_ORDER.getDesc()
            + "-"
            + DateUtil.formatNow(DatePattern.PURE_DATETIME_PATTERN));
    exportTask.setType(ExportTaskType.STOCK_OUT_ORDER);
    IExportTaskService exportTaskService = SpringUtil.getBean(IExportTaskService.class);
    exportTaskService.save(exportTask);

    exportDomainService.export(
        stockOutOrderQuery,
        exportTask,
        ExportType.STOCK_OUT_ORDER,
        StockOutOrderSheet.class,
        (Function<StockOutOrderSheet, ExportSheet>)
            stockOutOrderSheet -> {
              Integer businessLine = stockOutOrderSheet.getBusinessLine();
              Optional<BusinessLine> opt =
                  IEnum.getEnumOptByValue(BusinessLine.class, businessLine);
              opt.ifPresent(v -> stockOutOrderSheet.setBusinessLineStr(v.getDesc()));
              return stockOutOrderSheet;
            });
    return Response.buildSuccess();
  }

  @Override
  public Response manualStockOutOrder(Long stockOutOrderId) {
    StockOutOrder stockOutOrder = iStockOutOrderService.getById(stockOutOrderId);
    if (Objects.isNull(stockOutOrder)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未查询到退料出库单，无法操作");
    }
    if (!stockOutOrder.getCreatedUid().equals(UserContext.getUserId())) {
      throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "无法操作当前退料出库单");
    }
    if (!stockOutOrder.getState().equals(StockOutOrderState.NO_STOCK_OUT.getValue())
        || !stockOutOrder.getIsSyncWdt().equals(0)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.OPERATION_REJECT, "当前数据状态不可该操作");
    }

    final LambdaQueryWrapper<StockOutOrderDetail> detailQuery = Wrappers.lambdaQuery();
    detailQuery.eq(StockOutOrderDetail::getStockOutOrderId, stockOutOrder.getId());
    final List<StockOutOrderDetail> stockOutOrderDetails =
        stockOutOrderDetailMapper.selectList(detailQuery);
    syncLocal(stockOutOrder, stockOutOrderDetails);

    return Response.buildSuccess();
  }

  @Override
  public void updateState(Long stockOutOrderId, Integer state) {
    StockOutOrder stockOutOrder = iStockOutOrderService.getById(stockOutOrderId);
    Assert.notNull(stockOutOrder, "id非法，退料出库查询为空");
    log.info("审核出库单id: {} 状态改变为: {}", stockOutOrderId, state);

    if (state.equals(stockOutOrder.getState())) {
      return;
    }
    stockOutOrder.setState(state);

    // 状态变更为待出库时推送至旺店通 和 金蝶
    if (StockOutOrderState.NO_STOCK_OUT.getValue().equals(state)) {
      stockOutOrderSyncWDT(stockOutOrder);
    }
    iStockOutOrderService.updateById(stockOutOrder);
  }

  @Override
  public List<StockOutOrder> getStockOutOrderByPurchaseOrderId(Long purchaseOrderId) {
    LambdaQueryWrapper<StockOutOrder> queryWrapper = Wrappers.lambdaQuery();
    queryWrapper.eq(StockOutOrder::getPurchaseOrderId, purchaseOrderId);
    return iStockOutOrderService.list(queryWrapper);
  }

  @Override
  public PageResponse<ComposeSkuVO> pageSku(ComposeSkuPageQuery query) {
    Assert.notNull(query.getPurchaseOrderId(), "关联的采购单不得为空");

    Long purchaseOrderId = query.getPurchaseOrderId();
    List<StockInOrderDetailVo> stockInOrderDetailVoList =
        stockInOrderDetailMapper.getDetailByPurchaseOrderId(query);
    Assert.state(CollectionUtils.isNotEmpty(stockInOrderDetailVoList), "关联的采购单还未入库，不允许进行退料出库");

    List<ComposeSkuVO> list = new ArrayList<>();
    Integer isSyncWdt = query.getIsSyncWdt();

    // 计算sku可出库数量 start
    //        List<StockOutQuantityCmd.StockCmd> stockQueryCmdList = new LinkedList<>();
    //        stockInOrderDetailVoList.forEach(vo -> {
    //            StockOutQuantityCmd.StockCmd listCmd = new StockOutQuantityCmd.StockCmd();
    //            listCmd.setSkuCode(vo.getItemSkuCode());
    //            listCmd.setWarehouseNo(vo.getWarehouseNo());
    //            listCmd.setIsGift(vo.getIsGift());
    //            stockQueryCmdList.add(listCmd);
    //        });
    //        StockOutQuantityCmd cmd = new StockOutQuantityCmd();
    //        cmd.setDetailVOList(stockQueryCmdList);
    //        cmd.setIsSyncWdt(query.getIsSyncWdt());
    //        cmd.setPurchaseOrderId(query.getPurchaseOrderId());
    //        List<RemindOutQuantityVO> remindOutQuantityVOS = queryRemindStockOutQuantity(cmd);
    // 计算商品可出库数量 end

    for (StockInOrderDetailVo stockInOrderDetailVo : stockInOrderDetailVoList) {
      String itemSkuCode = stockInOrderDetailVo.getItemSkuCode();
      String warehouseNo = stockInOrderDetailVo.getWarehouseNo();
      query.setSkuCodeList(Collections.singletonList(itemSkuCode));
      query.setWithPrice(true);
      PageResponse<ComposeSkuVO> composeSkuVOPageResponse =
          combinationItemBizService.pageSku(query);
      List<ComposeSkuVO> composeSkuVOList = composeSkuVOPageResponse.getData();

      if (composeSkuVOList.isEmpty()) {
        continue;
      }
      ComposeSkuVO composeSkuVO = composeSkuVOList.get(0);

      //            // 填充sku 库存
      //            Optional<RemindOutQuantityVO> quantityVO =
      // remindOutQuantityVOS.stream().filter(vo ->
      // vo.getSkuCode().equals(stockInOrderDetailVo.getItemSkuCode()))
      //                    .findFirst();
      //            int stockOutNum = quantityVO.isPresent() ?
      // quantityVO.get().getRemindStockOutNum() : 0;
      //            composeSkuVO.setStockCount((long) stockOutNum);

      if (query.getIsSyncWdt() == 1) {
        // 旺店通库存
        Long wdtStockNum = queryStock(itemSkuCode, warehouseNo);
        composeSkuVO.setStockCount(wdtStockNum);
      } else {
        StockOutOrderStockQuery stockQuery =
            buildStockQuery(null, purchaseOrderId, itemSkuCode, warehouseNo, isSyncWdt, null);
        Integer inStock = stockInOrderBizService.getStockInOrderSkuStock(stockQuery);
        composeSkuVO.setStockCount((long) inStock);
      }

      composeSkuVO.setWarehouseName(stockInOrderDetailVo.getStock());
      composeSkuVO.setWarehouseNo(warehouseNo);
      list.add(composeSkuVO);
    }

    Integer count = stockInOrderDetailMapper.getDetailCountByPurchaseOrderId(purchaseOrderId);
    return PageResponse.of(list, count, query.getPageSize(), query.getPageIndex());
  }

  /** 同步旺店通 */
  @Override
  public void stockOutOrderSyncWDT(StockOutOrder stockOutOrder) {
    final LambdaQueryWrapper<StockOutOrderDetail> detailQuery = Wrappers.lambdaQuery();
    detailQuery.eq(StockOutOrderDetail::getStockOutOrderId, stockOutOrder.getId());
    final List<StockOutOrderDetail> stockOutOrderDetails =
        stockOutOrderDetailMapper.selectList(detailQuery);

    boolean syncKingDeeAccess = true;
    if (stockOutOrder.getIsSyncWdt() == 1) {
      final StockOutOrderSyncState wdtSyncResStatus =
          stockOutOrderSyncWDT(stockOutOrder, stockOutOrderDetails);
      stockOutOrder.setSyncState(wdtSyncResStatus.getValue());
      syncKingDeeAccess = StockOutOrderSyncState.SUCCESS.equals(wdtSyncResStatus);
    } else {
      boolean isPurchaseOrder =
          stockOutOrder.getPurchaseOrderId() != null && stockOutOrder.getPurchaseOrderNo() != null;
      if (isPurchaseOrder) {
        syncLocal(stockOutOrder, stockOutOrderDetails);
      }
    }

    if (syncKingDeeAccess) {
      // 同步金蝶
      SingleResponse<String> createRes = stockOutOrderSyncKingDee(stockOutOrder);
      operateLogGateway.addOperatorLog(
          0L,
          OperateLogTarget.STOCK_OUT_ORDER,
          stockOutOrder.getId(),
          StrUtil.format("退料出库单同步至金蝶,response:{}", JsonUtil.toJson(createRes)),
          null);
      // 如果是业务人员创建的单据，当在金蝶创建单据成功之后，自动进行提交审核处理
      if (stockOutOrder.getCreatedUid() > 0 && createRes.isSuccess()) {
        final SubmitAndAuditRes res =
            reqTemplate.submitAndAuditStockOrder(
                ApiEnum.SAVE_STOCK_OUT_ORDER.getFormId(), ListUtil.of(stockOutOrder.getNo()));
        operateLogGateway.addOperatorLog(
            0L,
            OperateLogTarget.STOCK_OUT_ORDER,
            stockOutOrder.getId(),
            StrUtil.format("退料出库单提交至金蝶,response:{}", JsonUtil.toJson(res)),
            null);
        stockOutOrder.setSyncState(StockOutOrderSyncState.SUCCESS.getValue());
      } else {
        stockOutOrder.setSyncState(StockOutOrderSyncState.KING_DEE_ERROR.getValue());
      }
    }

    stockOutOrderMapper.updateById(stockOutOrder);
  }

  /**
   * 同步旺店通
   *
   * @param stockOutOrder 退料出库单
   */
  @ManagedPush(
      sourceType = SourceType.PURCHASE_RETURN_STOCK_OUT_ORDER,
      targetType = TargetType.KINGDEE)
  public SingleResponse<String> stockOutOrderSyncKingDee(StockOutOrder stockOutOrder) {
    boolean syncAccess =
        stockOutOrder.getPurchaseOrganizationId().equals(29L)
            || stockOutOrder.getPurchaseOrganizationId().equals(4L);
    if (syncAccess) {
      Response response =
          kingDeeTemplate.handler(ApiEnum.SAVE_STOCK_OUT_ORDER, stockOutOrder.getId(), "");
      if (!response.isSuccess()) {
        operateLogDomainService.addOperatorLog(
            0L, OperateLogTarget.STOCK_ORDER, stockOutOrder.getId(), "同步金蝶失败，请手动处理");
        return SingleResponse.buildFailure(
            ErrorCode.API_RESP_ERROR.getCode(), response.getErrMessage());
      }
      return SingleResponse.of(stockOutOrder.getNo());
    } else {
      return SingleResponse.of(stockOutOrder.getNo());
    }
  }

  /*private void deleteStockOutOrder(StockOutOrder stockOutOrder){
      iStockOutOrderDetailService.lambdaUpdate()
              .eq(StockOutOrderDetail::getStockOutOrderId,stockOutOrder.getId())
              .remove();
      iStockOutOrderService.removeById(stockOutOrder.getId());
  }*/

  /**
   * 同步旺店通
   *
   * @param stockOutOrder 退料出库单
   * @param stockOutOrderDetailList 退料出库明细列表
   */
  @ManagedPush(sourceType = SourceType.PURCHASE_RETURN_STOCK_OUT_ORDER, targetType = TargetType.WDT)
  public StockOutOrderSyncState stockOutOrderSyncWDT(
      StockOutOrder stockOutOrder, List<StockOutOrderDetail> stockOutOrderDetailList) {
    try {

      //            if (StrUtil.isNotBlank(stockOutOrder.getWdtStorageNo())) {
      //                log.info("当前采购出库单已同步至旺店通，跳过推送：{}", stockOutOrder.getNo());
      //                return;
      //            }

      final PurchaseReturnAPI api = wdtGateway.getAPI(PurchaseReturnAPI.class);
      final PurchaseReturnCreateOrderOrderInfo params = new PurchaseReturnCreateOrderOrderInfo();
      params.setOuterNo(stockOutOrder.getNo());

      final SingleResponse<ProviderVO> providerResp =
          providerBizService.queryDetail(stockOutOrder.getProviderId());
      final ProviderVO provideData = providerResp.getData();
      if (!providerResp.isSuccess() || Objects.isNull(provideData)) {
        throw ExceptionPlusFactory.bizException(
            providerResp.getErrCode(), providerResp.getErrMessage());
      }
      params.setProviderNo(provideData.getProviderNo());
      if (StrUtil.isNotBlank(provideData.getContact())) {
        params.setContact(provideData.getContact());
      }
      if (StrUtil.isNotBlank(provideData.getContactMobile())) {
        params.setTelno(provideData.getContactMobile());
      }

      final String warehouseNo = stockOutOrderDetailList.get(0).getWarehouseNo();
      QueryWrapper<Warehouse> queryWrapper = new QueryWrapper<>();
      queryWrapper.eq("no", warehouseNo);
      final Warehouse warehouse = warehouseMapper.selectOne(queryWrapper);
      if (warehouse == null) {
        throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "未找到指定仓库：" + warehouseNo);
      }
      params.setWarehouseNo(warehouse.getNo());

      final List<PurchaseReturnCreateOrderDetail> purchaseReturnCreateOrderDetails =
          new ArrayList<>();
      final Map<String, PurchaseReturnCreateOrderDetail> purchaseReturnCreateOrderDetailsMap =
          new HashMap<>();
      for (StockOutOrderDetail stockOutOrderDetail : stockOutOrderDetailList) {
        PurchaseReturnCreateOrderDetail purchaseDetail =
            purchaseReturnCreateOrderDetailsMap.get(stockOutOrderDetail.getItemSkuCode());
        if (purchaseDetail == null) {
          purchaseDetail = new PurchaseReturnCreateOrderDetail();
          purchaseDetail.setSpecNo(stockOutOrderDetail.getItemSkuCode());
          purchaseDetail.setNum(new BigDecimal(stockOutOrderDetail.getReturnQuantity()));
          purchaseDetail.setTaxRate(stockOutOrderDetail.getTaxRate());
          purchaseDetail.setPrice(stockOutOrderDetail.getTaxPrice());
          purchaseDetail.setUnitName("");
          purchaseDetail.setDefect(false);
          purchaseDetail.setRemark(stockOutOrderDetail.getRemark());
          purchaseReturnCreateOrderDetails.add(purchaseDetail);
          purchaseReturnCreateOrderDetailsMap.put(
              stockOutOrderDetail.getItemSkuCode(), purchaseDetail);
        } else {
          purchaseDetail.setNum(
              purchaseDetail.getNum().add(new BigDecimal(stockOutOrderDetail.getReturnQuantity())));
        }
      }

      final boolean isCheck = true;
      final PurchaseReturnCreateOrderResponse createOrderResponse =
          api.createOrder(params, purchaseReturnCreateOrderDetails, isCheck);
      operateLogGateway.addOperatorLog(
          0L,
          OperateLogTarget.STOCK_OUT_ORDER,
          stockOutOrder.getId(),
          StrUtil.format("退料出库单同步至旺店通采购管理订单，res:{}", JsonUtil.toJson(createOrderResponse)),
          null);
      stockOutOrderMapper.updateWdtPurchaseOrderNo(stockOutOrder.getId(), stockOutOrder.getNo());
      return createOrderResponse.getStatus() == 0
          ? StockOutOrderSyncState.SUCCESS
          : StockOutOrderSyncState.WDT_ERROR;
    } catch (WdtErpException e) {
      stockOutOrder.setState(StockOutOrderState.NO_STOCK_OUT.getValue());
      stockOutOrder.setSyncState(StockOutOrderSyncState.WDT_ERROR.getValue());
      stockOutOrder.setSyncMsg(e.getMessage());
      iStockOutOrderService.updateById(stockOutOrder);
      operateLogGateway.addOperatorLog(
          0L,
          OperateLogTarget.STOCK_OUT_ORDER,
          stockOutOrder.getId(),
          StrUtil.format("退料出库单同步至旺店通采购管理订单异常，{}", e.getMessage()),
          null);
      throw WdtExceptions.wrapBizException(e);
    }
  }

  /** 同步本地数量、价格 */
  @Transactional(rollbackFor = Exception.class)
  public void syncLocal(
      StockOutOrder stockOutOrder, List<StockOutOrderDetail> stockOutOrderDetailList) {
    // 同步数量
    for (StockOutOrderDetail stockOutOrderDetail : stockOutOrderDetailList) {
      stockOutOrderDetail.setRealReturnQuantity(stockOutOrderDetail.getReturnQuantity());
      Integer realReturnQuantity = stockOutOrderDetail.getRealReturnQuantity();
      stockOutOrderDetail.setValuationQuantity(realReturnQuantity);
      stockOutOrderDetail.setReplenishQuantity(realReturnQuantity);
      stockOutOrderDetail.setDeductionQuantity(realReturnQuantity);
    }
    iStockOutOrderDetailService.updateBatchById(stockOutOrderDetailList);
    stockOutOrder.setState(StockOutOrderState.STOCK_OUT.getValue());
    int count = stockOutOrderDetailMapper.getTotalReturnQuantity(stockOutOrder.getId());
    stockOutOrder.setTotalReturnQuantity(count);
    stockOutOrder.setOutboundTime(DateUtil.currentTime());
    iStockOutOrderService.updateById(stockOutOrder);

    // 创建应付单
    Long createdUid = stockOutOrder.getCreatedUid();
    final StockInOrOutEvent stockInOrOutEvent =
        StockInOrOutEvent.ofNotice(
            PurchaseTypeEnum.OUT_STOCK_PAYABLE, stockOutOrder.getId(), createdUid, createdUid);
    EventBusUtil.post(stockInOrOutEvent, true);
  }

  /**
   * 校验采购单状态和入库商品库存
   *
   * @param stockOutOrderCmd 出库单
   */
  public void verifyStateAndStock(StockOutOrderCmd stockOutOrderCmd) {
    List<StockOutOrderDetailCmd> stockOutOrderDetailList =
        stockOutOrderCmd.getStockOutOrderDetailList();
    // 提交外 或者 （不为null 或者 不为空）
    boolean isState = !stockOutOrderCmd.getState().equals(StockOutOrderState.NO_SUBMIT.getValue());
    boolean isNull = stockOutOrderDetailList != null && !stockOutOrderDetailList.isEmpty();
    if (isState || isNull) {
      if (stockOutOrderDetailList == null || stockOutOrderDetailList.isEmpty()) {
        throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "请添加商品明细");
      }
      String warehouseNo = stockOutOrderDetailList.get(0).getWarehouseNo();
      List<StockOutOrderDetailCmd> detailList =
          stockOutOrderDetailList.stream()
              .filter(
                  stockOutOrderDetailCmd ->
                      !warehouseNo.equals(stockOutOrderDetailCmd.getWarehouseNo()))
              .collect(Collectors.toList());
      if (!detailList.isEmpty()) {
        throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "出库商品的仓库必须相同");
      }
      // 仓库一致性
      if (stockOutOrderCmd.getPurchaseOrderId() != null
          && stockOutOrderCmd.getPurchaseOrderNo() != null) {
        List<StockInOrderDetail> stockInOrderDetailList =
            stockInOrderBizService.getHasStockInOrderDetail(
                stockOutOrderCmd.getPurchaseOrderId(),
                stockOutOrderDetailList.get(0).getItemSkuCode());
        if (stockInOrderDetailList.isEmpty()) {
          throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "关联的采购单还未入库，不允许进行退料出库");
        }
        List<StockInOrderDetail> filterDetailList =
            stockInOrderDetailList.stream()
                .filter(
                    stockInOrderDetail -> warehouseNo.equals(stockInOrderDetail.getWarehouseNo()))
                .collect(Collectors.toList());
        if (filterDetailList.isEmpty()) {
          throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "出库商品的仓库要和入库仓库一致");
        }
      }
      Long purchaseOrderId = stockOutOrderCmd.getPurchaseOrderId();
      final Set<String> skuCodes =
          stockOutOrderDetailList.stream()
              .map(StockOutOrderDetailCmd::getItemSkuCode)
              .collect(Collectors.toSet());
      final Map<String, BigDecimal> stockSum = warehouseStockGateway.getStockSum(skuCodes);
      // 数量校验
      for (StockOutOrderDetailCmd stockOutOrderDetailCmd : stockOutOrderDetailList) {
        List<StockOutOrderDetailCmd> stockOutOrderDetailCmds =
            stockOutOrderDetailList.stream()
                .filter(s -> s.getItemSkuCode().equals(stockOutOrderDetailCmd.getItemSkuCode()))
                .collect(Collectors.toList());
        int returnSum =
            stockOutOrderDetailCmds.stream()
                .mapToInt(StockOutOrderDetailCmd::getReturnQuantity)
                .sum();

        boolean rejectStockOut;
        Integer isSyncWdt = stockOutOrderCmd.getIsSyncWdt();
        String itemSkuCode = stockOutOrderDetailCmd.getItemSkuCode();
        Integer isGift = stockOutOrderDetailCmd.getIsGift();
        if (stockOutOrderCmd.getPurchaseOrderId() != null
            && stockOutOrderCmd.getPurchaseOrderNo() != null) {
          StockOutOrderStockQuery stockQuery =
              buildStockQuery(
                  stockOutOrderCmd.getId(),
                  purchaseOrderId,
                  itemSkuCode,
                  warehouseNo,
                  isSyncWdt,
                  isGift);

          // sku 已出库单待审核+待出库+已出库 出库数量
          Optional<Integer> outSumOptional =
              Optional.ofNullable(stockOutOrderMapper.getStockOutOrderItemSum(stockQuery));
          // sku 部分入库+已入库的实际入库数量
          Integer inStock = stockInOrderBizService.getStockInOrderSkuStock(stockQuery);
          // 关联采购单 && 同步到wdt
          if (isSyncWdt == 1) {
            // sku 在wdt库存数量
            Long wdtSkuStockNum = queryStock(itemSkuCode, warehouseNo);
            long min = Math.min(inStock, wdtSkuStockNum);
            // rejectStockOut:returnSum > 实际可出库数量
            rejectStockOut = returnSum > (min - outSumOptional.orElse(0));
          }
          // 关联代购单 && 不同步到wdt
          else {
            // rejectStockOut:returnSum > 入库数量 - 已出库数量
            rejectStockOut = returnSum > (inStock - outSumOptional.orElse(0));
          }
        } else {
          long itemTotalStock =
              Optional.ofNullable(stockSum.get(stockOutOrderDetailCmd.getItemSkuCode()))
                  .map(BigDecimal::longValue)
                  .orElse(0L);
          rejectStockOut = returnSum > itemTotalStock;
        }

        verifyStock(stockOutOrderDetailCmd, rejectStockOut);
      }
    }
  }

  /** 明细异常 */
  public void verifyStock(StockOutOrderDetailCmd stockOutOrderDetailCmd, boolean rejectStockOut) {
    //        String itemSkuCode = stockOutOrderDetailCmd.getItemSkuCode();
    if (stockOutOrderDetailCmd.getReturnQuantity() <= 0) {
      throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "出库商品的申请退料数量不能为0");
    }
    //        if (rejectStockOut) {
    //            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "出库商品SKU为" +
    // itemSkuCode + "的申请退料数量大于库存，不能出库");
    //        }
  }

  /** 保存出库单明细 */
  public void saveStockOutOrderDetailList(
      List<StockOutOrderDetailCmd> stockOutOrderDetailCmdList, StockOutOrder stockOutOrder) {
    List<StockOutOrderDetail> stockOutOrderDetailList = new ArrayList<>();
    // 从采购单出库单拿商品价格和税率
    if (stockOutOrder.getPurchaseOrderId() != null && stockOutOrder.getPurchaseOrderNo() != null) {
      for (StockOutOrderDetailCmd stockOutOrderDetailCmd : stockOutOrderDetailCmdList) {

        //                Long purchaseOrderId = stockOutOrder.getPurchaseOrderId();
        //                String itemSkuCode = stockOutOrderDetailCmd.getItemSkuCode();
        //                PurchaseOrderDetail purchaseOrderDetail =
        // getPurchaseOrderDetail(purchaseOrderId, itemSkuCode);
        //                if (purchaseOrderDetail != null) {
        //                    stockOutOrderDetailCmd.setTaxPrice(purchaseOrderDetail.getTaxPrice());
        //                    stockOutOrderDetailCmd.setTaxRate(purchaseOrderDetail.getTaxRate());
        //                } else {
        //                    stockOutOrderDetailCmd.setTaxPrice(BigDecimal.ZERO);
        //                    stockOutOrderDetailCmd.setTaxRate(BigDecimal.ZERO);
        //                }

        StockOutOrderDetail stockOutOrderDetail =
            StockOutOrderTransMapper.INSTANCE.toDetail(stockOutOrderDetailCmd);
        stockOutOrderDetail.setStockOutOrderId(stockOutOrder.getId());
        initStockOutOrderDetail(stockOutOrderDetail);
        stockOutOrderDetailList.add(stockOutOrderDetail);
      }
    } else {
      // 商品出库单拿商品价格和税率
      for (StockOutOrderDetailCmd stockOutOrderDetailCmd : stockOutOrderDetailCmdList) {

        StockOutOrderDetail stockOutOrderDetail =
            StockOutOrderTransMapper.INSTANCE.toDetail(stockOutOrderDetailCmd);
        stockOutOrderDetail.setStockOutOrderId(stockOutOrder.getId());
        initStockOutOrderDetail(stockOutOrderDetail);
        stockOutOrderDetailList.add(stockOutOrderDetail);
      }
    }
    iStockOutOrderDetailService.saveBatch(stockOutOrderDetailList);
  }

  /** 获取采购详情 */
  public PurchaseOrderDetail getPurchaseOrderDetail(Long purchaseOrderId, String itemSkuCode) {
    LambdaQueryWrapper<PurchaseOrderDetail> queryWrapper = Wrappers.lambdaQuery();
    queryWrapper.eq(PurchaseOrderDetail::getPurchaseOrderId, purchaseOrderId);
    queryWrapper.eq(PurchaseOrderDetail::getItemSkuCode, itemSkuCode);
    List<PurchaseOrderDetail> purchaseOrderDetailList =
        iPurchaseOrderDetailService.list(queryWrapper);
    if (purchaseOrderDetailList.isEmpty()) {
      throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "采购单商品不存在");
    }
    return purchaseOrderDetailList.get(0);
  }

  /** 字段初始化 */
  public void initStockOutOrder(
      StockOutOrder stockOutOrder, List<StockOutOrderDetailCmd> stockOutOrderDetailList) {
    stockOutOrder.setNo(stockOutOrder.getNo() != null ? stockOutOrder.getNo() : regenerateNo());
    stockOutOrder.setRemark(stockOutOrder.getRemark() != null ? stockOutOrder.getRemark() : "");
    stockOutOrder.setSyncState(0);
    stockOutOrder.setSyncMsg("");
  }

  /** 明细字段初始化 */
  public void initStockOutOrderDetail(StockOutOrderDetail stockOutOrderDetail) {
    stockOutOrderDetail.setRemark(
        stockOutOrderDetail.getRemark() != null ? stockOutOrderDetail.getRemark() : "");
    stockOutOrderDetail.setSpecifications(
        stockOutOrderDetail.getSpecifications() != null
            ? stockOutOrderDetail.getSpecifications()
            : "");
    stockOutOrderDetail.setReturnReason(
        stockOutOrderDetail.getReturnReason() != null ? stockOutOrderDetail.getReturnReason() : "");
    stockOutOrderDetail.calculatedAmount();
  }

  /** 构建库存查询对象 */
  public StockOutOrderStockQuery buildStockQuery(
      Long id,
      Long purchaseOrderId,
      String itemSkuCode,
      String warehouseNo,
      Integer isSyncWdt,
      Integer isGift) {
    StockOutOrderStockQuery stockOutOrderStockQuery = new StockOutOrderStockQuery();
    stockOutOrderStockQuery.setPurchaseOrderId(purchaseOrderId);
    stockOutOrderStockQuery.setItemSkuCode(itemSkuCode);
    stockOutOrderStockQuery.setWarehouseNo(warehouseNo);
    stockOutOrderStockQuery.setIsSyncWdt(isSyncWdt);
    stockOutOrderStockQuery.setStockOutOrderId(id);
    stockOutOrderStockQuery.setIsGift(isGift);
    return stockOutOrderStockQuery;
  }

  /** 创建并提交审核流程 */
  private void createAwsApproval(StockOutOrder stockOutOrder) {
    // 审核流提交到老爸工作台
    SingleResponse<String> singleResponse =
        daddylabWorkbenchGateway.syncStockOutOrder(stockOutOrder.getId(), UserContext.getUserId());
    if (!singleResponse.isSuccess()) {
      throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, singleResponse.getErrMessage());
    }
    if (Objects.isNull(stockOutOrder.getWorkbenchProcessId())) {
      String processId = singleResponse.getData();
      stockOutOrder.setApprovalId(processId);
      stockOutOrder.setWorkbenchProcessId(processId);
      stockOutOrder.setApprovalAt(DateUtil.currentTime());
      iStockOutOrderService.updateById(stockOutOrder);
    }
  }

  /** 获取采购类型 */
  private boolean isPurchaseOrderType(Long purchaseOrderId) {
    PurchaseOrder purchaseOrder = purchaseOrderMapper.selectById(purchaseOrderId);
    if (purchaseOrder != null) {
      // 采购类型 1 标准采购，2 工厂采购
      return purchaseOrder.getType() != 2;
    }
    return true;
  }

  public List<StockSpecSearchResponse.Data> queryStockByList(
      List<String> skuCode, String warehouseNo) throws BizException {
    StockSpecSearchParams params = new StockSpecSearchParams();
    params.setSpecNos(skuCode);
    params.setWarehouseNo(warehouseNo);

    final StockSpecAPI stockAPI = getWdtGateway().getAPI(StockSpecAPI.class);
    final int pageSize = skuCode.size();
    int pageNo = 0;
    final Pager pager = new Pager(pageSize, pageNo, true);

    StockSpecSearchResponse response;
    try {
      response = stockAPI.search(params, pager);
      return response.getData();
    } catch (WdtErpException e) {
      log.error("wdt实时库存查询异常,skuCode:{},warehouseNo:{}", JsonUtil.toJson(skuCode), warehouseNo, e);
      throw ExceptionPlusFactory.bizException(ErrorCode.API_RESP_ERROR, "旺店通实时库存查询异常");
    }
  }

  /** 旺店通查询库存 */
  public Long queryStock(String skuCode, String warehouseNo) throws BizException {
    StockSpecSearchParams params = new StockSpecSearchParams();
    params.setSpecNos(ListUtil.of(skuCode));
    params.setWarehouseNo(warehouseNo);

    final StockSpecAPI stockAPI = getWdtGateway().getAPI(StockSpecAPI.class);
    final int pageSize = 1;
    int pageNo = 0;
    final Pager pager = new Pager(pageSize, pageNo, true);

    StockSpecSearchResponse response;
    try {
      response = stockAPI.search(params, pager);
    } catch (WdtErpException e) {
      log.error("wdt实时库存查询异常,skuCode:{},warehouseNo:{}", skuCode, warehouseNo, e);
      throw ExceptionPlusFactory.bizException(ErrorCode.API_RESP_ERROR, "旺店通实时库存查询异常");
    }

    AtomicReference<Long> res = new AtomicReference<>(0L);
    response.getData().stream()
        .filter(var -> var.getWarehouseNo().equals(warehouseNo))
        .findFirst()
        .ifPresent(var2 -> res.set(var2.getStockNum().longValue()));
    return res.get();
  }

  @NonNull
  private WdtGateway getWdtGateway() {
    return wdtGateway;
  }

  /**
   * 添加业务日志
   *
   * @param stockOutOrderId 出库id
   * @param msg 日志信息
   */
  public void addOperateLog(Long stockOutOrderId, String msg) {
    operateLogDomainService.addOperatorLog(
        UserContext.getUserId(), OperateLogTarget.STOCK_OUT_ORDER, stockOutOrderId, msg);
  }

  /**
   * 生成出库单号
   *
   * @return 单号
   */
  public String regenerateNo() {
    String newNo = "CGCK" + DateUtil.formatNow("yyMM");
    Long increment = RedisUtil.increment(newNo, 1);
    String order = String.format("%06d", increment);
    return newNo + order;
  }

  @Override
  public List<RemindOutQuantityVO> queryRemindStockOutQuantity(StockOutQuantityCmd cmd) {
    List<RemindOutQuantityVO> list = new LinkedList<>();
    List<StockOutQuantityCmd.StockCmd> detailVOList = cmd.getDetailVOList();

    detailVOList.stream()
        // 根据warehouseNo,进行skuCode聚合
        .collect(Collectors.groupingBy(StockOutQuantityCmd.StockCmd::getWarehouseNo))
        .forEach(
            (warehouseNo, stockCmdList) -> {

              // 查询erp 除了已取消的，剩下都算到出库数量。
              Map<String, Integer> erpStockOutNumMap = new HashMap<>(stockCmdList.size());
              // erp 部分入库+已入库 的实际数量
              Map<String, Integer> erpStockInNumMap = new HashMap<>(stockCmdList.size());

              stockCmdList.forEach(
                  val -> {
                    StockOutOrderStockQuery stockQuery =
                        buildStockQuery(
                            null,
                            cmd.getPurchaseOrderId(),
                            val.getSkuCode(),
                            warehouseNo,
                            cmd.getIsSyncWdt(),
                            val.getIsGift());
                    Integer integer0 =
                        Optional.ofNullable(stockOutOrderMapper.getStockOutOrderItemSum(stockQuery))
                            .orElse(0);
                    Integer integer1 =
                        Optional.of(stockInOrderBizService.getStockInOrderSkuStock(stockQuery))
                            .orElse(0);
                    if (erpStockOutNumMap.containsKey(val.getSkuCode())) {
                      erpStockOutNumMap.put(
                          val.getSkuCode(), erpStockOutNumMap.get(val.getSkuCode()) + integer0);
                      erpStockInNumMap.put(
                          val.getSkuCode(), erpStockOutNumMap.get(val.getSkuCode()) + integer1);
                    } else {
                      erpStockOutNumMap.put(val.getSkuCode(), integer0);
                      erpStockInNumMap.put(val.getSkuCode(), integer1);
                    }
                  });

              // 同步wdt
              if (cmd.getIsSyncWdt() == 1) {
                // 查询wdt库存
                final List<StockSpecSearchResponse.Data> data =
                    queryStockByList(
                        stockCmdList.stream()
                            .map(StockOutQuantityCmd.StockCmd::getSkuCode)
                            .collect(Collectors.toList()),
                        warehouseNo);
                Map<String, Integer> wdtStockInNumMap = new HashMap<>(stockCmdList.size());
                data.stream()
                    .collect(Collectors.groupingBy(StockSpecSearchResponse.Data::getSpecNo))
                    .forEach(
                        (skuCode, valList) ->
                            wdtStockInNumMap.put(
                                skuCode,
                                valList.stream()
                                    .mapToInt(val -> val.getStockNum().intValue())
                                    .sum()));

                wdtStockInNumMap.forEach(
                    (skuCode, stockNum) -> {
                      // 入库数量取wdt库存和erp库存中的小的值。
                      Integer orDefault = erpStockInNumMap.getOrDefault(skuCode, 0);
                      Integer orDefault1 = wdtStockInNumMap.getOrDefault(skuCode, 0);
                      int stockInNum = NumberUtil.min(orDefault, orDefault1);
                      int r =
                          Math.max((stockInNum - erpStockOutNumMap.getOrDefault(skuCode, 0)), 0);
                      RemindOutQuantityVO vo = new RemindOutQuantityVO(skuCode, r);
                      list.add(vo);
                    });
              }
              // 不同步wdt
              else {
                // 计算剩余可出库数量。
                erpStockInNumMap.forEach(
                    (skuCode, stockNum) -> {
                      RemindOutQuantityVO vo =
                          new RemindOutQuantityVO(
                              skuCode, stockNum - erpStockOutNumMap.getOrDefault(skuCode, 0));
                      list.add(vo);
                    });
              }
            });
    return list;
  }

  @Override
  public MultiResponse<Long> getTodoList() {
    final List<Long> todoList =
        awsApprovalFlowService.getTodoList(
            PurchaseTypeEnum.OUT_STOCK_PAYABLE.getValue(), UserContext.getLoginName());
    if (todoList.isEmpty()) {
      return MultiResponse.of(todoList);
    }
    final List<Long> todoAuditList =
        iStockOutOrderService
            .lambdaQuery()
            //                .eq(StockOutOrder::getState,
            //                        StockOutOrderState.NO_AUDIT)
            .in(StockOutOrder::getId, todoList)
            .select(StockOutOrder::getId)
            .list()
            .stream()
            .map(StockOutOrder::getId)
            .collect(Collectors.toList());
    return MultiResponse.of(todoAuditList);
  }

  @Autowired private RefreshConfig refreshConfig;

  @Autowired private MsgSender msgSender;

  @Resource OperateLogGateway operateLogGateway;

  @Override
  public void toDoSummaryReminder(String qwUserId, List<Long> purchaseOrderIds) {
    final WechatMsg wechatMsg = new WechatMsg();
    wechatMsg.setTitle(String.format("【评测ERP】中共 %s 个采购-退料出库待审核处理！", purchaseOrderIds.size()));
    wechatMsg.setContent("您有一个新的待办，请尽快处理");
    final String link =
        refreshConfig.getDomain()
            + String.format(
                "/commodity-purchase/purchase-stock-out/list?ids=%s",
                purchaseOrderIds.stream().map(Object::toString).collect(Collectors.joining(",")));
    wechatMsg.setLink(link);
    wechatMsg.setRecipient(qwUserId);
    wechatMsg.setType(1);
    msgSender.send(wechatMsg);
  }

  @Override
  public Response callBackOrderState(CallBackOrderStateCmd cmd) {
    log.info("stockOutOrder callBackOrderState param:{}", JsonUtil.toJson(cmd));
    List<AwsBusinessLog> list =
        iAwsBusinessLogService
            .lambdaQuery()
            .eq(AwsBusinessLog::getProcessId, cmd.getProcessInstId())
            .eq(AwsBusinessLog::getType, PurchaseTypeEnum.OUT_STOCK_PAYABLE.getValue())
            .orderByDesc(AwsBusinessLog::getId)
            .list();
    if (CollUtil.isEmpty(list)) {
      return Response.buildFailure(ErrorCode.PROCESS_ERROR.getCode(), "审核数据查询为空");
    }
    AwsBusinessLog awsBusinessLog = list.get(0);
    Long businessId = awsBusinessLog.getBusinessId();

    String callBackLog = "老爸工作台回调，";
    // O:废弃/删除。1:审核拒绝。2:审核完成。3:审核撤销。4:审核同意
    if (cmd.getCallBackState() == 0) {
      iStockOutOrderService
          .lambdaUpdate()
          .eq(StockOutOrder::getId, businessId)
          .set(StockOutOrder::getWorkbenchProcessId, StrUtil.EMPTY)
          .set(StockOutOrder::getApprovalId, StrUtil.EMPTY)
          .set(StockOutOrder::getApprovalAt, 0L)
          .set(StockOutOrder::getState, StockOutOrderState.NO_SUBMIT.getValue())
          .update();
      callBackLog = callBackLog + "审核废弃/删除";
    }
    if (cmd.getCallBackState() == 1) {
      iStockOutOrderService
          .lambdaUpdate()
          .eq(StockOutOrder::getId, businessId)
          .set(StockOutOrder::getState, StockOutOrderState.REJECT.getValue())
          .update();
      callBackLog = callBackLog + "审核拒绝";
    }
    if (cmd.getCallBackState() == 3) {
      iStockOutOrderService
          .lambdaUpdate()
          .eq(StockOutOrder::getId, businessId)
          .set(StockOutOrder::getState, StockOutOrderState.NO_SUBMIT.getValue())
          .update();
      callBackLog = callBackLog + "审核撤销";
    }
    if (cmd.getCallBackState() == 2) {
      final StockOutOrder stockOutOrder = iStockOutOrderService.getById(businessId);
      if (Objects.nonNull(stockOutOrder)) {

        stockOutOrderSyncWDT(stockOutOrder);
        stockOutOrder.setState(StockOutOrderState.NO_STOCK_OUT.getValue());
        iStockOutOrderService.updateById(stockOutOrder);
        callBackLog = callBackLog + "审核同意";
      } else {
        callBackLog = callBackLog + "审核同意,但是单据ID非法";
      }
    }

    operateLogGateway.addOperatorLog(
        0L, OperateLogTarget.STOCK_OUT_ORDER, businessId, callBackLog, null);

    return Response.buildSuccess();
  }
}
