package com.daddylab.supplier;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.daddylab.supplier.item.application.afterSalesRegister.AfterSalesRegisterFetchServiceImpl;
import com.daddylab.supplier.item.domain.banniu.BanniuCommonServiceImpl;
import com.daddylab.supplier.item.domain.banniu.BanniuConverter;
import com.daddylab.supplier.item.domain.banniu.BanniuMiniServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.AfterSalesRegister;
import com.daddylab.supplier.item.infrastructure.utils.FileUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.banniu.Condition;
import com.daddylab.supplier.item.types.banniu.MiniTaskQuery;
import com.daddylab.supplier.item.types.banniu.SearchType;
import com.daddylab.supplier.item.types.banniu.ShippingWarehouseTask;
import com.daddylab.third.banniu.starter.BanniuProperties;
import com.google.common.collect.ImmutableMap;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MultiMapUtils;
import org.apache.commons.collections4.SetValuedMap;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import wjb.open.api.ApiException;
import wjb.open.api.WjbClient;
import wjb.open.api.WjbDefaultClient;
import wjb.open.api.request.mini.MiniColumnListRequest;
import wjb.open.api.request.mini.MiniProjectListRequest;
import wjb.open.api.response.mini.MiniColumnListResponse;
import wjb.open.api.response.mini.MiniProjectListResponse;
import wjb.open.api.response.mini.MiniQueryTaskListResponse;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/11
 */
@Slf4j
public class BanniuTests {

    public static class BanniuConfig {
        public static String getAppkey() {
            return System.getenv("banniu_appkey");
        }

        public static String getSecret() {
            return System.getenv("banniu_secret");
        }

        public static String getToken() {
            return System.getenv("banniu_token");
        }

        public static WjbClient getMiniClient() {
            return new WjbDefaultClient(
                    "https://open.bytenew.com/gateway/api/miniAPI",
                    getAppkey(),
                    getSecret(),
                    10000);
        }

        public static WjbClient getCommonClient() {
            return new WjbDefaultClient(
                    "https://open.bytenew.com/gateway/api/bnAPI2", getAppkey(), getSecret(), 10000);
        }

        public static BanniuProperties getProperties() {
            final BanniuProperties banniuProperties = new BanniuProperties();
            banniuProperties.setAppkey(BanniuConfig.getAppkey());
            banniuProperties.setAppSecret(BanniuConfig.getSecret());
            banniuProperties.setAccessToken(BanniuConfig.getToken());
            banniuProperties.setHttpTimeout(10000);
            return banniuProperties;
        }
    }

    @Test
    public void test() throws ApiException, IOException {
        final WjbClient defaultClient = BanniuConfig.getMiniClient();
        final MiniProjectListRequest miniProjectListRequest = new MiniProjectListRequest();
        final MiniProjectListResponse miniProjectListResponse =
                defaultClient.execute(miniProjectListRequest, BanniuConfig.getToken());
        System.out.println(miniProjectListResponse);
        final Optional<List<MiniProjectListResponse.Project>> optionalProjects =
                Optional.ofNullable(miniProjectListResponse)
                        .map(MiniProjectListResponse::getResult)
                        .filter(v -> !v.isEmpty());
        Assertions.assertTrue(optionalProjects.isPresent(), "查询结果异常");
        final List<MiniProjectListResponse.Project> projects = optionalProjects.get();

        final List<String[]> sheetsToHandle =
                Arrays.asList(
                        new String[] {"舆情团队", "普通舆情登记表", "8872"},
                        new String[] {"舆情团队", "高危舆情登记表", ""},
                        new String[] {"客服团队", "售前登记表"},
                        new String[] {"售后团队", "售后工单对接表"},
                        new String[] {"售后团队", "VIP售后登记表"},
                        new String[] {"售后团队", "（新）售后登记表"},
                        new String[] {"售后团队", "呼入电话登记表"});
        ((Logger) log).setLevel(Level.INFO);

        final HashMap<Object, MiniProjectListResponse.Project> projectMap = new HashMap<>();
        for (String[] sheet : sheetsToHandle) {
            log.info("matching: " + Arrays.toString(sheet));
            final ArrayList<Pair<MiniProjectListResponse.Project, Double>> matchProjects =
                    new ArrayList<>();
            for (MiniProjectListResponse.Project project : projects) {
                final double similar = StrUtil.similar(sheet[1], project.getName());
                log.debug(
                        "match project score: "
                                + similar
                                + " "
                                + Arrays.toString(sheet)
                                + " "
                                + project.getName());
                if (similar >= 0.5) {
                    matchProjects.add(Pair.of(project, similar));
                }
            }
            if (!matchProjects.isEmpty()) {
                final Comparator<Pair<MiniProjectListResponse.Project, Double>> comparing =
                        Comparator.comparing(Pair::getValue);
                final Pair<MiniProjectListResponse.Project, Double> matchProjectItem =
                        matchProjects.stream().max(comparing).get();
                final MiniProjectListResponse.Project project = matchProjectItem.getKey();
                final Double score = matchProjectItem.getValue();
                log.info(
                        "match project, score: "
                                + score
                                + " "
                                + Arrays.toString(sheet)
                                + " "
                                + JsonUtil.toJson(project));
                projectMap.put(sheet, project);
            } else {
                log.warn("no match: " + Arrays.toString(sheet));
            }
        }

        //        final List<String> columnList =
        //                Arrays.asList(
        //                        "结算周期",
        //                        "订单号",
        //                        "付款时间",
        //                        "店铺",
        //                        "商品编码",
        //                        "商品名称",
        //                        "商品规格",
        //                        "售后数量",
        //                        "发货仓",
        //                        "问题描述(选项1级标题)",
        //                        "问题描述(选项2级标题)",
        //                        "问题描述(选项3级标题)",
        //                        "售后处理意见",
        //                        "工厂/仓库承担运费",
        //                        "工厂/仓库承担货款",
        //                        "工厂/仓库承担其他补偿",
        //                        "体验基金（优惠券）",
        //                        "体验基金（现金）",
        //                        "体验金承担原因(选项1级标题)",
        //                        "体验金承担原因(选项2级标题)",
        //                        "体验金承担原因(选项3级标题)"
        //                );

        final List<String> columnList = Arrays.asList("相关图片");

        final HashMap<MiniProjectListResponse.Project, List<MiniColumnListResponse.ColumnOption>>
                allColumnListMap = new HashMap<>();

        final SetValuedMap<String, String> enumStatMap = MultiMapUtils.newSetValuedHashMap();
        for (Map.Entry<Object, MiniProjectListResponse.Project> entry : projectMap.entrySet()) {
            final MiniColumnListRequest miniColumnListRequest = new MiniColumnListRequest();
            final MiniProjectListResponse.Project project = entry.getValue();
            miniColumnListRequest.setProjectId(project.getProject_id());
            final MiniColumnListResponse columnListResponse =
                    defaultClient.execute(miniColumnListRequest, BanniuConfig.getToken());
            allColumnListMap.put(project, columnListResponse.getResult());
            columnListResponse.getResult().stream()
                    .map(MiniColumnListResponse.ColumnOption::getBehavior_type)
                    .forEach(v -> enumStatMap.put("behavior_type", String.valueOf(v)));
            columnListResponse.getResult().stream()
                    .map(MiniColumnListResponse.ColumnOption::getType)
                    .forEach(v -> enumStatMap.put("type", String.valueOf(v)));
        }

        log.info("enum stats:{}", enumStatMap);

        final String path = "/Users/<USER>/Desktop/test.json";
        final BufferedWriter writer = FileUtil.getWriter(path, StandardCharsets.UTF_8, false);
        writer.write(
                JsonUtil.toJson(
                        allColumnListMap.entrySet().stream()
                                .map(v -> ImmutableMap.of("key", v.getKey(), "value", v.getValue()))
                                .collect(Collectors.toList())));
        writer.close();

        final SetValuedMap<String, String> matchMap = MultiMapUtils.newSetValuedHashMap();
        final Comparator<Pair<MiniColumnListResponse.ColumnOption, Double>> comparator4column =
                Comparator.comparing(Pair::getValue);
        for (String rawColumn : columnList) {
            String column = cleanName(rawColumn);
            final ArrayList<Pair<MiniColumnListResponse.ColumnOption, Double>> similarColumns =
                    new ArrayList<>();
            final HashMap<MiniColumnListResponse.ColumnOption, MiniProjectListResponse.Project>
                    columnOptionProjectHashMap = new HashMap<>();
            for (Map.Entry<
                            MiniProjectListResponse.Project,
                            List<MiniColumnListResponse.ColumnOption>>
                    columnsEntry : allColumnListMap.entrySet()) {
                final MiniProjectListResponse.Project project = columnsEntry.getKey();
                final List<MiniColumnListResponse.ColumnOption> columnOptions =
                        columnsEntry.getValue();
                for (MiniColumnListResponse.ColumnOption columnOption : columnOptions) {

                    final String columnOptionName = cleanName(columnOption.getName());
                    final double score = StrUtil.similar(columnOptionName, column);
                    if (score > 0.2) {
                        log.debug(
                                "列组件匹配 {} {}/{} <-> {} :{}",
                                project.getProject_id(),
                                project.getName(),
                                columnOption.getName(),
                                rawColumn,
                                score);
                    }
                    if (score >= 0.6) {
                        similarColumns.add(Pair.of(columnOption, score));
                        columnOptionProjectHashMap.put(columnOption, project);
                    }
                }
            }
            if (similarColumns.isEmpty()) {
                log.warn("column:{} 未找到匹配列", column);
            } else {
                final List<Pair<MiniColumnListResponse.ColumnOption, Double>> matchColumnOptions =
                        similarColumns.stream()
                                .sorted(comparator4column.reversed())
                                .collect(Collectors.toList());
                for (Pair<MiniColumnListResponse.ColumnOption, Double> columnOptionEntry :
                        matchColumnOptions) {
                    final MiniColumnListResponse.ColumnOption columnOption =
                            columnOptionEntry.getKey();
                    final Double score = columnOptionEntry.getValue();
                    final MiniProjectListResponse.Project project =
                            columnOptionProjectHashMap.get(columnOptionEntry.getKey());
                    matchMap.put(rawColumn, columnOption.getName());
                    log.info(
                            "column:{} 找到匹配列 score:{} project:{} columnOption:{}",
                            column,
                            score,
                            new Object[] {project.getProject_id(), project.getName()},
                            new Object[] {columnOption.getColumn_id(), columnOption.getName()});
                }
            }
            log.info("{}", JsonUtil.toJson(matchMap.asMap()));
        }
    }

    @NonNull
    private static String cleanName(String column) {
        column = column.replace("(", "（");
        column = column.replace(")", "）");
        return column.replaceAll("（.+）", "");
    }

    @Test
    public void testConvertData() throws ApiException {
        final WjbClient miniClient = BanniuConfig.getMiniClient();
        final WjbClient commonClient = BanniuConfig.getCommonClient();
        final BanniuProperties banniuProperties = BanniuConfig.getProperties();
        final BanniuMiniServiceImpl banniuMiniService =
                new BanniuMiniServiceImpl(miniClient, banniuProperties);
        final BanniuCommonServiceImpl banniuCommonService =
                new BanniuCommonServiceImpl(commonClient, banniuProperties);
        final MiniProjectListResponse.Project project =
                banniuMiniService.projects().stream()
                        .filter(v -> v.getProject_id().equals(8060))
                        .findFirst()
                        .orElseThrow(() -> new RuntimeException("班牛工作表查询异常"));
        final List<MiniColumnListResponse.ColumnOption> columnOptions =
                banniuMiniService.columnOptions(project.getProject_id());

        final MiniTaskQuery query = new MiniTaskQuery();
        query.setProjectId(project.getProject_id());
        query.setPageNum(1);
        query.setPageSize(10);
        final ArrayList<Condition> conditionColumn = new ArrayList<>();
        conditionColumn.add(new Condition("订单号", SearchType.EQ, "20230809514787282719392598"));
        query.setConditionColumn(conditionColumn);

        final MiniQueryTaskListResponse.ResultMap tasks = banniuMiniService.tasks(query);

        final AfterSalesRegisterFetchServiceImpl afterSalesRegisterFetchService =
                new AfterSalesRegisterFetchServiceImpl();
        afterSalesRegisterFetchService.setBanniuMiniService(banniuMiniService);
        afterSalesRegisterFetchService.setBanniuCommonService(banniuCommonService);
        final List<Map<Integer, String>> result = tasks.getResult();
        final List<JSONObject> data = BanniuConverter.SHARED.convertData(columnOptions, result);
        System.out.println(JSON.toJSONString(data));
        Assertions.assertEquals(data.size(), result.size());
        data.stream()
                .limit(10)
                .forEach(
                        datum -> {
                            final AfterSalesRegister afterSalesRegister =
                                    afterSalesRegisterFetchService.convert2po(
                                            datum, project.getName(), YearMonth.now());
                            System.out.println(JsonUtil.toJson(afterSalesRegister));
                            Assertions.assertNotNull(afterSalesRegister.getOrderNo());
                        });
    }

    @Test
    public void projects() throws ApiException {
        final WjbClient miniClient = BanniuConfig.getMiniClient();
        final WjbClient commonClient = BanniuConfig.getCommonClient();
        final BanniuProperties banniuProperties = BanniuConfig.getProperties();
        final BanniuMiniServiceImpl banniuMiniService =
                new BanniuMiniServiceImpl(miniClient, banniuProperties);
        final BanniuCommonServiceImpl banniuCommonService =
                new BanniuCommonServiceImpl(commonClient, banniuProperties);
        final Map<String, List<MiniProjectListResponse.Project>> projectsMap =
                banniuMiniService.projects().stream()
                        .collect(Collectors.groupingBy(MiniProjectListResponse.Project::getName))
                        .entrySet()
                        .stream()
                        .filter(e -> e.getValue().size() > 1)
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        System.out.println(JSON.toJSONString(projectsMap));
    }

    @Test
    public void taskCreate() throws ApiException {
        final WjbClient miniClient = BanniuConfig.getMiniClient();
        final WjbClient commonClient = BanniuConfig.getCommonClient();
        final BanniuProperties banniuProperties = BanniuConfig.getProperties();
        final BanniuMiniServiceImpl banniuMiniService =
                new BanniuMiniServiceImpl(miniClient, banniuProperties);
        final ShippingWarehouseTask data = new ShippingWarehouseTask();
        data.setCreatorUserId(192010020);
        data.setCreateTime(LocalDateTime.now());
        data.setUpdateTime(LocalDateTime.now());
        data.setCompleteUserId(192010020);
        data.setCompleteTime(LocalDateTime.now());
        data.setShelfTime(LocalDateTime.now());
        data.setBuyerUser("采购员姓名");
        data.setCategory("测试分类1");
        data.setItemName("商品名称166");
        data.setItemSpec("商品规格6166");
        data.setItemCode("66666");
        data.setWarehouse("袋鼠仓");
        data.setSpuCode("12366601");
        data.setSpuName("SPU名称66116");
        data.setOrderUser("呜呜");

        banniuMiniService.taskCreate(14250, 192010020, data);
    }
}
