package com.daddylab.supplier;

import com.daddylab.supplier.item.application.criticalPush.CriticalPushException;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQConsumerBase;
import javax.annotation.Nullable;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.redisson.api.RedissonClient;
import org.springframework.core.env.StandardEnvironment;

/**
 * <AUTHOR>
 * @since 2022/5/5
 */
public class Slf4jTest {

    @Test
    public void test() {
        final RedissonClient redissonClient = Mockito.mock(RedissonClient.class, Mockito.RETURNS_DEEP_STUBS);
        final RocketMQConsumerBase<String> consumerBase = new RocketMQConsumerBase<String>(redissonClient,
                new StandardEnvironment(), new RefreshConfig()) {

            @Override
            protected void handle(MessageExt msg, @Nullable String body) {
                throw new CriticalPushException(1L, "第几次报错");
            }
        };
        final MessageExt msg = new MessageExt();
        msg.setKeys("test");
        msg.setTopic("test");
        msg.setTags("test");
        msg.setKeys("test");
        msg.setBody("test".getBytes());

        consumerBase.onMessage(msg);
    }

}
