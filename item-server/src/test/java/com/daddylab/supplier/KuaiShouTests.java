package com.daddylab.supplier;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.kuaishou.merchant.open.api.KsMerchantApiException;
import com.kuaishou.merchant.open.api.client.AccessTokenKsMerchantClient;
import com.kuaishou.merchant.open.api.client.oauth.OauthCredentialKsClient;
import com.kuaishou.merchant.open.api.common.HttpRequestMethod;
import com.kuaishou.merchant.open.api.common.dto.KsResponseDTO;
import com.kuaishou.merchant.open.api.domain.item.ShelfItemInfoResponseParam;
import com.kuaishou.merchant.open.api.request.KsCommonRequest;
import com.kuaishou.merchant.open.api.request.item.OpenItemListGetRequest;
import com.kuaishou.merchant.open.api.response.item.OpenItemListGetResponse;
import com.kuaishou.merchant.open.api.response.oauth.KsCredentialResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Map;

/**
 * <AUTHOR>
 * @class KuaiShouTests.java
 * @description 描述类的作用
 * @date 2024-02-28 11:05
 */
@Slf4j
public class KuaiShouTests {
    private final static String APP_URL = "https://openapi.kwaixiaodian.com";
    private final static String APP_KEY = "ks655273749019569416";
    private final static String SIGN_SECRET = "b0dc71181f5a5e592312cf5cb84eb0e9";
    private final static String APP_SECRET = "w36SAfBXFlD4bsI7J3V-Nw";



    @Test
    public void test() {
        AccessTokenKsMerchantClient accessTokenKsMerchantClient = new AccessTokenKsMerchantClient(APP_URL, APP_KEY, SIGN_SECRET);
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("pageSize", 20);
        params.put("pageNumber", 1);
        String accessToken = "ChFvYXV0aC5hY2Nlc3NUb2tlbhJg4oGbPsNOGk5BD3HXto0Gvi6Sg7tK_ey_1NKLgbnH4JVXLx9GuxPgAdachvV2aLixCMXDggLpnkFOW-Z7sBWqT5Rk5FSefoS-rgDRO-lnvPFg4lpm4P6VTjlLJrlDIMLTGhIgQfvkqD5FqqWtQ2y0WGxSDjAiIFh4qnAewJX5eYQ9Or9fev-jV7URxqWk5AjtkciFWQlkKAUwAQ";
        // 授权开放api
        OpenItemListGetRequest openItemListGetRequest = new OpenItemListGetRequest();
        openItemListGetRequest.setAccessToken(accessToken);

//        KsCommonRequest ksCommonRequest = new KsCommonRequest("open.item.list.get", accessToken, params, HttpRequestMethod.GET);
        try {
            ShelfItemInfoResponseParam item = null;
            OpenItemListGetResponse execute = accessTokenKsMerchantClient.execute(openItemListGetRequest);
            if (execute.isSuccess() && execute.getData().getItems().length != 0) {
                item = execute.getData().getItems()[0];
            }
            log.info("[快手接口] 商品查询返回 item={}", JSON.toJSONString(execute.getData()));
            // 查询货品
        } catch (KsMerchantApiException e) {
            e.printStackTrace();
        }
    }



    @Test
    public void test1() {
        OauthCredentialKsClient oauthCredentialKsClient = new OauthCredentialKsClient(APP_KEY, APP_SECRET);
        // 生成AccessToken
        try {
            KsCredentialResponse response = oauthCredentialKsClient.getAccessToken();
            System.out.println(JSON.toJSONString(response));
            AccessTokenKsMerchantClient accessTokenKsMerchantClient = new AccessTokenKsMerchantClient(APP_URL, APP_KEY, SIGN_SECRET);
            Map<String, Object> params = MapUtil.newHashMap();
            params.put("pageSize", 20);
            params.put("pageNumber", 1);
            String accessToken = response.getAccessToken();
            // 授权开放api
            KsCommonRequest ksCommonRequest = new KsCommonRequest("open.item.list.get", accessToken, params, HttpRequestMethod.GET);
            try {
                KsResponseDTO execute = accessTokenKsMerchantClient.execute(ksCommonRequest);
                log.info("[快手接口] 商品查询返回 execute= {}", JSON.toJSONString(execute));
            } catch (KsMerchantApiException e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
