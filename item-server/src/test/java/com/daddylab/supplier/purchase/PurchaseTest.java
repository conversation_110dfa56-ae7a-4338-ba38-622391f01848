package com.daddylab.supplier.purchase;

import cn.hutool.core.lang.Dict;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.ItemApplication;
import com.daddylab.supplier.item.application.aws.event.AwsChangeEventListener;
import com.daddylab.supplier.item.application.aws.service.AwsApprovalFlowService;
import com.daddylab.supplier.item.application.entryActivityPrice.EntryActivityPriceService;
import com.daddylab.supplier.item.application.item.combinationItem.CombinationItemBizService;
import com.daddylab.supplier.item.application.order.OrderService;
import com.daddylab.supplier.item.application.platformItem.InventoryAllocBizService;
import com.daddylab.supplier.item.application.provider.ProviderBizService;
import com.daddylab.supplier.item.application.provider.SyncProviderJob;
import com.daddylab.supplier.item.application.purchase.PurchaseBizService;
import com.daddylab.supplier.item.application.purchase.combinationPrice.CombinationPriceBizService;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizService;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizServiceImpl;
import com.daddylab.supplier.item.application.purchasePayable.service.PurchasePayableBizService;
import com.daddylab.supplier.item.application.shop.InventoryAllocShopBizServiceImpl;
import com.daddylab.supplier.item.application.stockInOrder.StockInOrderBizService;
import com.daddylab.supplier.item.application.stockOutOrder.StockOutOrderBizServiceImpl;
import com.daddylab.supplier.item.application.stockSpec.StockSpecBizService;
import com.daddylab.supplier.item.application.system.BaseInfoService;
import com.daddylab.supplier.item.application.warehouse.WarehouseGateway;
import com.daddylab.supplier.item.controller.common.BaseInfoController;
import com.daddylab.supplier.item.controller.common.job.SyncWarehouseJob;
import com.daddylab.supplier.item.controller.purchase.PurchaseOrderController;
import com.daddylab.supplier.item.controller.purchase.dto.order.*;
import com.daddylab.supplier.item.controller.stock.StockSpecController;
import com.daddylab.supplier.item.controller.stockInOrder.StockInOrderController;
import com.daddylab.supplier.item.controller.test.DateScriptService;
import com.daddylab.supplier.item.domain.category.job.SyncCategoryJob;
import com.daddylab.supplier.item.domain.contract.ContractGateway;
import com.daddylab.supplier.item.domain.exportTask.job.RemoveOldExportTaskJob;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.infrastructure.bpm.BpmClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl.ItemLaunchPlanServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.BpmFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerFeignClient;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQProducer;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.Data;
import lombok.NoArgsConstructor;
import okhttp3.*;
import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR> up
 * @date 2022/3/25 11:05 下午
 */
@SpringBootTest(classes = {ItemApplication.class})
public class PurchaseTest {

  @Autowired PurchaseOrderBizService purchaseOrderBizService;

  @Autowired IWarehouseService iWarehouseService;

  @Autowired OrderService orderService;

  @Autowired ISaleItemLibraryService saleItemLibraryService;

  @Autowired IPurchaseOrderService iPurchaseOrderService;

  @Autowired CombinationItemBizService combinationItemBizService;

  @Autowired IAwsApprovalNodeService awsApprovalNodeService;

  @Autowired IStockInOrderService iStockInOrderService;

  @Autowired StockInOrderBizService stockInOrderBizService;

  //    @Autowired
  //    WdtBizService wdtBizService;

  @Autowired ContractGateway contractGateway;

  @Autowired PartnerFeignClient partnerFeignClient;

  @Autowired CombinationItemMapper combinationItemMapper;

  @Autowired BaseInfoController baseInfoController;

  @Autowired AwsApprovalFlowService awsApprovalFlowService;

  @Autowired StockInOrderMapper stockInOrderMapper;

  @Autowired StockInOrderController stockInOrderController;

  @Autowired AwsChangeEventListener awsChangeEventListener;

  @Autowired PurchaseOrderController purchaseOrderController;

  @Autowired PurchaseOrderDetailMapper purchaseOrderDetailMapper;

  //    @Autowired
  //    FactoryCleaningOrderJob factoryCleaningOrderJob;
  //
  //    @Autowired
  //    FactoryProcessJob factoryProcessJob;
  @Autowired KingDeeTemplate kingDeeTemplate;

  @Autowired WarehouseGateway warehouseGateway;

  @Autowired ItemGateway itemGateway;

  @Autowired IWarehouseService warehouseService;

  @Autowired SyncProviderJob syncProvider;

  //    @Autowired
  //    SyncItemSkuJob syncItemSkuJon;

  @Autowired SyncCategoryJob syncCategoryJob;

  @Autowired ProviderBizService providerBizService;

  @Autowired RemoveOldExportTaskJob removeOldExportTaskJob;

  @Autowired BaseInfoService baseInfoService;

  @Autowired ReqTemplate reqTemplate;

  @Autowired PurchasePayableBizService purchasePayableBizService;

  @Autowired SyncWarehouseJob syncWarehouseJob;

  @Autowired ItemLaunchPlanItemRefMapper itemLaunchPlanItemRefMapper;

  @Autowired StockOutOrderBizServiceImpl stockOutOrderBizService;

  @Autowired StockSpecBizService stockSpecBizService;

  @Autowired DateScriptService dateScriptService;

  @Autowired CombinationPriceBizService combinationPriceBizService;

  @Autowired RocketMQProducer rocketMQProducer;

  @Autowired IShopService iShopService;

  private void testKingDeeRootCategory() throws Exception {
    Dict data = Dict.create().set("FormId", "BD_MATERIAL");
    Dict req = Dict.create().set("data", data).set("formid", "BD_MATERIAL");

    reqTemplate.groupInfo(JsonUtil.toJson(req));
  }

  @Autowired ItemLaunchPlanServiceImpl iItemLaunchPlanService;

  @Autowired RedissonClient redissonClient;

  //    @Autowired
  //    WdtOrderCleaner wdtOrderCleaner;

  @Autowired IWdtOrderDetailWrapperService iWdtOrderDetailWrapperService;

  @Autowired IItemService iItemService;

  @Autowired IBizLevelDivisionService iBizLevelUnionService;

  @Autowired ShopMapper shopMapper;

  @Resource EntryActivityPriceItemMapper entryActivityPriceItemMapper;

  @Resource EntryActivityPriceService entryActivityPriceService;

  @Resource PurchaseBizService purchaseBizService;

  @Resource StockSpecController stockSpecController;

  @Resource BpmClient bpmClient;

  @Resource PurchaseOrderBizServiceImpl purchaseOrderBizServiceImpl;

  @Resource BpmFeignClient bpmFeignClient;
  @Resource InventoryAllocShopBizServiceImpl inventoryAllocShopBizService;

  @Resource IInventoryAllocShopService iInventoryAllocShopService;

  private static final OkHttpClient client =
      new OkHttpClient.Builder()
          .connectTimeout(30, TimeUnit.SECONDS) // 连接超时
          .writeTimeout(30, TimeUnit.SECONDS) // 写入超时
          .readTimeout(30, TimeUnit.SECONDS) // 读取超时
          .build();

  public static void main(String[] args) {}

  @NoArgsConstructor
  @Data
  public static class MsgBizDto {

    private String topic;
    private Integer flag;
    private PropertiesDTO properties;
    private String body;
    private Object transactionId;
    private String keys;
    private String tags;
    private Integer delayTimeLevel;
    private Boolean waitStoreMsgOK;
    private Object buyerId;

    @NoArgsConstructor
    @Data
    public static class PropertiesDTO {
      private Long expectTime;
      private String keys;
      private String source;
      private String tags;
    }
  }

  @Autowired InventoryAllocBizService inventoryAllocBizService;

  @Autowired IOperateLogService iOperateLogService;

  @Autowired ItemMapper itemMapper;

  @Test
  public void demoTest() throws Exception {
    purchaseOrderBizService.startOASealProcess(3017L);
    
    
//    ExportCmd cmd = new ExportCmd();
//    cmd.setCode("1000314");
//    cmd.setOffsetVal(0L);
//    cmd.setSize(10);
//    final List<ExportItemDto> exportItemDtos = itemMapper.queryExportItem(cmd);
//    System.out.println(JsonUtil.toJson(exportItemDtos));

    //    Map<String, Object> reqMap = new HashMap<>(2);
    //    reqMap.put("affairId", "4506392357031332872");
    //    reqMap.put("member", "ban.dai");
    //    final String bpmToken = bpmClient.getUserOaToken("ban.dai");
    //    final Boolean res = bpmFeignClient.stop(JsonUtil.toJson(reqMap), bpmToken);
    //    System.out.println(res);

    //    stockSpecBizService.runInventoryMonitor();
    //    final List<OperateLog> list = iOperateLogService
    //            .lambdaQuery()
    //            .eq(OperateLog::getTargetType, INVENTORY_ALLOC_SHOP)
    //            .eq(OperateLog::getData, ShopExecutionSignal.STOCK_ALLOC.name())
    //            .orderByDesc(OperateLog::getCreatedAt)
    //            .list();
    //    System.out.println(1);

    //    InventoryAllocShopQuery query = new InventoryAllocShopQuery();
    //    query.setPageIndex(1);
    //    query.setPageSize(10);
    //    final PageResponse<InventoryAllocShopVo> page = inventoryAllocShopBizService.page(query);
    //    System.out.println(JsonUtil.toJson(page));

    //    final List<Shop> list = iShopService.lambdaQuery().list();
    //    for (Shop shop : list) {
    //      InventoryAllocShop vv = new InventoryAllocShop();
    //      vv.setShopId(shop.getId());
    //      vv.setShopNo(shop.getSn());
    //      vv.setInventoryWeight(RandomUtil.randomInt(2));
    //      vv.setStatus(InventoryAllocShopStatus.ACTIVE);
    //      vv.setWarnThreshold(RandomUtil.randomInt(2));
    //      iInventoryAllocShopService.save(vv);
    //    }

    //    System.out.println(bpmClient.getUserOaToken("leda"));

    //    inventoryAllocShopBizService.sendNextMsg(
    //        DateUtil.currentTime(), ShopExecutionSignal.STOCK_ALLOC);

    //    long randomDelay = RandomUtil.randomLong(60 * 1000, (1 + 1) * 60 * 1000);
    //    long expectTime = DateUtil.currentTimeMillis() + randomDelay;
    //    String msgKey = RandomUtil.randomString(8);
    //    MsgBizDto.PropertiesDTO propertiesDTO = new MsgBizDto.PropertiesDTO();
    //    propertiesDTO.setExpectTime(expectTime);
    //    MsgBizDto msgBizDto = new MsgBizDto();
    //    msgBizDto.setTopic("mock_biz_topic");
    //    msgBizDto.setProperties(propertiesDTO);
    //    Provider provider = new Provider();
    //    provider.setId(999L);
    //    String body =
    //        new String(
    //            Base64.getEncoder().encode(JsonUtil.toJson(provider).getBytes()),
    //            StandardCharsets.UTF_8);
    //    msgBizDto.setBody(body);
    //    msgBizDto.setKeys(msgKey);
    //    msgBizDto.setTags("mock_biz_tag");
    //
    //    rocketMQProducer.syncSend(msgBizDto, "test_delay-timer_receive_task", "", "");

    //    ShopDropDownQuery query = new ShopDropDownQuery();
    //    query.setPageIndex(1);
    //    query.setPageSize(10);
    //    final List<ShopDropDownItem> shopDropDownItems = shopMapper.dropDownList(query);
    //    System.out.println(JsonUtil.toJson(shopDropDownItems));

    //    final PurchaseOrder order = iPurchaseOrderService.getById("3009L");
    //    final SingleResponse<String> stringSingleResponse =
    // purchaseOrderBizService.oaSealUrl(3009L);
    //    System.out.println(JsonUtil.toJson(stringSingleResponse));

    //    purchaseOrderBizService.startOASealProcess(3009L);

    //    PartnerContractQuery query = new PartnerContractQuery();
    //    query.setContractNo("DKCG2021-108");
    ////    query.setOrganizationId(3907L);
    //    final Rsp<List<PartnerContract>> listRsp = partnerFeignClient.contractQuery(query);
    //    System.out.println(JsonUtil.toJson(listRsp));

    //    PartnerContractQuery query = new PartnerContractQuery();
    //    query.setOrganizationId(3907L);
    //    final Rsp<List<PartnerContract>> listRsp2 = partnerFeignClient.contractQuery(query);
    //    System.out.println(JsonUtil.toJson(listRsp2));

  }

  @Test
  public void export() throws Exception {
    PurchaseOrderPageQuery query = new PurchaseOrderPageQuery();
    query.setOrderNo("CG2204180006");
    query.setProviderId(0L);
    query.setType(0);
    query.setPayMode(0);
    query.setState(0);
    query.setStockInState(0);
    query.setBuyerUserId(0L);
    query.setStartDt(0L);
    query.setStartEnd(0L);
    query.setContractNo("");
    query.setPayOrderNo("");
    query.setItemId(10282L);
    query.setPageIndex(0);
    query.setPageSize(0);
    purchaseOrderBizService.export(query);
    System.in.read();
  }

  private List<PurchaseOrderDetailCmd> getAddDetailList() {
    PurchaseOrderDetailCmd c1 = new PurchaseOrderDetailCmd();
    c1.setItemSkuCode("BM20170907749");
    c1.setItemId(10282L);
    c1.setSpecifications("测试规格001");
    c1.setBarCode("");
    c1.setUnit("件");
    c1.setPurchaseQuantity(100);
    c1.setTaxPrice(new BigDecimal("19"));
    c1.setTaxRate(new BigDecimal("0.13"));
    c1.setIsGift(0);
    c1.setWarehouseNo("2");
    c1.setRemark("大河向东流啊");

    PurchaseOrderDetailCmd c2 = new PurchaseOrderDetailCmd();
    c2.setItemSkuCode("RTLTAO046900F");
    c2.setItemId(10555L);
    c2.setSpecifications("测试规格001");
    c2.setBarCode("");
    c2.setUnit("件");
    c2.setPurchaseQuantity(200);
    c2.setTaxPrice(new BigDecimal("39"));
    c2.setTaxRate(new BigDecimal("0.17"));
    c2.setIsGift(0);
    c2.setWarehouseNo("2");
    c2.setRemark("猴哥，猴哥，你真了不得。");

    PurchaseOrderDetailCmd c3 = new PurchaseOrderDetailCmd();
    c3.setItemSkuCode("RTLTAO046900F");
    c3.setItemId(10555L);
    c3.setSpecifications("测试规格001");
    c3.setBarCode("");
    c3.setUnit("件");
    c3.setPurchaseQuantity(10);
    c3.setTaxPrice(new BigDecimal("39"));
    c3.setTaxRate(new BigDecimal("0.01"));
    c3.setIsGift(1);
    c3.setWarehouseNo("2");
    c3.setRemark("呔，你这个赠品猴");

    return Arrays.asList(c1, c2, c3);
  }

  @Test
  public void testAdd() {
    PurchaseOrderCmd cmd = new PurchaseOrderCmd();
    cmd.setType(1);
    cmd.setProviderId(71L);
    cmd.setOrganizationId(0L);
    cmd.setContractNo("demo0001");
    cmd.setBuyerUserId(2L);
    cmd.setDepartmentId(0L);
    cmd.setGroupId(0L);
    cmd.setArrivalType(1);
    cmd.setPayType(1);
    cmd.setPurchaseDate(DateUtil.currentTime());
    cmd.setRemark("这是一条长长的天路啊，啊啊啊啊啊啊啊啊啊。");
    cmd.setDetailCmdList(getAddDetailList());

    purchaseOrderBizService.save(cmd);
  }

  private List<PurchaseOrderDetailCmd> getEditDetailList() {
    PurchaseOrderDetailCmd c1 = new PurchaseOrderDetailCmd();
    c1.setId(14L);
    c1.setItemSkuCode("BM20170907749");
    c1.setItemId(10282L);
    c1.setSpecifications("测试规格001");
    c1.setBarCode("");
    c1.setUnit("件");
    c1.setPurchaseQuantity(100);
    c1.setTaxPrice(new BigDecimal("19"));
    c1.setTaxRate(new BigDecimal("0.13"));
    c1.setIsGift(0);
    c1.setWarehouseNo("2");
    c1.setRemark("大河向东流啊");

    PurchaseOrderDetailCmd c2 = new PurchaseOrderDetailCmd();
    c2.setId(15L);
    c2.setItemSkuCode("RTLTAO046900F");
    c2.setItemId(10555L);
    c2.setSpecifications("测试规格002");
    c2.setBarCode("");
    c2.setUnit("件");
    c2.setPurchaseQuantity(200);
    c2.setTaxPrice(new BigDecimal("39"));
    c2.setTaxRate(new BigDecimal("0.17"));
    c2.setIsGift(0);
    c1.setWarehouseNo("2");
    c2.setRemark("猴哥，猴哥，你真了不得。");

    PurchaseOrderDetailCmd c3 = new PurchaseOrderDetailCmd();
    c3.setId(16L);
    c3.setItemSkuCode("RTLTAO046900F");
    c3.setItemId(10555L);
    c3.setSpecifications("测试规格003");
    c3.setBarCode("");
    c3.setUnit("件");
    c3.setPurchaseQuantity(10);
    c3.setTaxPrice(new BigDecimal("39"));
    c3.setTaxRate(new BigDecimal("0.01"));
    c3.setIsGift(1);
    c1.setWarehouseNo("2");
    c3.setRemark("呔，你这个赠品猴");

    PurchaseOrderDetailCmd c4 = new PurchaseOrderDetailCmd();
    c4.setItemSkuCode("NLTYM9003080S");
    c4.setItemId(10664L);
    c4.setSpecifications("测试规格004");
    c4.setBarCode("");
    c4.setUnit("件");
    c4.setPurchaseQuantity(150);
    c4.setTaxPrice(new BigDecimal("15"));
    c4.setTaxRate(new BigDecimal("0.09"));
    c4.setIsGift(0);
    c1.setWarehouseNo("2");
    c4.setRemark("一滚长江东逝水，浪花淘尽英雄");

    return Arrays.asList(c1, c2, c3, c4);
  }

  @Test
  public void testEdit() {
    PurchaseOrderCmd cmd = new PurchaseOrderCmd();
    cmd.setId(7L);
    cmd.setType(1);
    cmd.setProviderId(71L);
    cmd.setOrganizationId(1L);
    cmd.setContractNo("编辑之后demo0001");
    cmd.setBuyerUserId(2L);
    cmd.setDepartmentId(1L);
    cmd.setGroupId(1L);
    cmd.setArrivalType(1);
    cmd.setPayType(1);
    cmd.setPurchaseDate(DateUtil.currentTime());
    cmd.setRemark("编制之后：这是一条长长的天路啊，啊啊啊啊啊啊啊啊啊。");
    cmd.setDetailCmdList(getEditDetailList());
    purchaseOrderBizService.save(cmd);
  }

  @Test
  public void testPage() throws IOException {
    PurchaseOrderPageQuery query = new PurchaseOrderPageQuery();
    //        query.setOrderNo("CG2203300003");
    //        query.setWarehouseNo("1");
    //        query.setOrganizationId(9L);
    //        query.setGroupId(10L);
    //        query.setProviderId(0L);
    //        query.setType(0);
    //        query.setPayMode(0);
    //        query.setState(0);
    //        query.setStockState(0);
    //        query.setBuyerUserId(0L);
    //        query.setStartDt(0L);
    //        query.setStartEnd(0L);
    //        query.setContractNo("");
    //        query.setPayOrderNo("sda12321");
    //        query.setSkuCode("RTLTAO046900F");
    //        query.setItemId(10282L);
    //        query.setIsDetailMode(false);
    query.setPageIndex(1);
    query.setPageSize(10);

    PageResponse<PurchaseOrderPageVO> page = purchaseOrderBizService.page(query);
    System.out.println(JsonUtil.toJson(page));
  }

  @Test
  public void viewTest() {
    SingleResponse<PurchaseOrderViewVO> view = purchaseOrderBizService.view(7L);
    System.out.println(JsonUtil.toJson(view));
  }

  @Test
  public void saveTest() {

    ApplicationContext bean = SpringUtil.getBean(ApplicationContext.class);
    System.out.println(Arrays.toString(bean.getEnvironment().getActiveProfiles()));

    //        PurchaseOrderPageQuery query = new PurchaseOrderPageQuery();
    //        query.setOrderNo("CG2203300003");
    //        query.setWarehouseNo("1");
    //        query.setOrganizationId(9L);
    //        query.setPageIndex(0);
    //        query.setPageSize(10);
    //
    //        PageResponse<PurchaseOrderPageVO> page = purchaseOrderBizService.page(query);
    //        Console.log(page);

  }
}
