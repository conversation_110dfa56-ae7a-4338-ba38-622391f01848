package com.daddylab.supplier.purchase;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.daddylab.supplier.item.application.afterSaleLink.AfterSaleShareLinkBizService;
import com.daddylab.supplier.item.application.afterSaleLogistics.AfterSaleLogisticsConfig;
import com.daddylab.supplier.item.application.afterSales.AfterSalesWarehouseBizService;
import com.daddylab.supplier.item.application.afterSalesRegister.AfterSalesRegisterBizService;
import com.daddylab.supplier.item.application.exportTask.ExportTaskBizService;
import com.daddylab.supplier.item.application.item.SuiteFetcher;
import com.daddylab.supplier.item.application.order.settlement.OrderSettlementBizService;
import com.daddylab.supplier.item.application.order.settlement.dto.*;
import com.daddylab.supplier.item.application.order.settlement.sys.DetailStaticDo;
import com.daddylab.supplier.item.application.order.settlement.sys.OrderSettlementSysService;
import com.daddylab.supplier.item.application.payment.PaymentOrderBizService;
import com.daddylab.supplier.item.application.payment.PaymentOrderBizServiceImpl;
import com.daddylab.supplier.item.application.payment.wrietoff.WriteOffBizService;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizService;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.PurchaseBillService;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.RefundStockInPrice;
import com.daddylab.supplier.item.application.salesOutStock.SalesOutStockBizService;
import com.daddylab.supplier.item.application.stockOutOrder.StockOutOrderBizService;
import com.daddylab.supplier.item.application.system.BaseInfoService;
//import com.daddylab.supplier.item.application.virtualWarehouse.StockConfigDetailBizService;
import com.daddylab.supplier.item.application.warehouse.WarehouseBizService;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.controller.test.DateScriptService;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.daddylabWorkbench.DaddylabWorkbenchGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemProcurementGateway;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderSettlementDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderSettlementForm;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OrderSettlementStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrderSettlementFormMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailWrapperMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundStockInOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.DaddylabWorkbenchFeignClient;
import com.daddylab.supplier.item.infrastructure.oss.OssConfig;
import com.daddylab.supplier.item.infrastructure.oss.OssGateway;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.*;

/**
 * <AUTHOR> up
 * @date 2023年08月10日 4:15 PM
 */
@SpringBootTest(classes = {com.daddylab.supplier.item.ItemApplication.class},
        properties = {
                "spring.profiles.active:test"})
//@ExtendWith(SpringExtension.class)
//@SpringBootTest(classes = ItemApplication.class)
//@ActiveProfiles("test")
@Slf4j
public class PurchaseSettlementTest {

    @Resource
    OrderSettlementBizService orderSettlementBizService;

    @Resource
    IOrderSettlementFormService iOrderSettlementFormService;

    @Resource
    IOrderSettlementDetailService iOrderSettlementDetailService;

    @Resource
    BaseInfoService baseInfoService;

    @Resource
    AfterSalesWarehouseBizService afterSalesWarehouseBizService;

    @Resource
    ExportTaskBizService exportTaskBizService;

    @Resource
    OrderSettlementFormMapper orderSettlementFormMapper;

    @Resource
    RedissonClient redissonClient;

    @Resource
    IWarehouseService iWarehouseService;

    @Resource
    IPurchaseOrderService iPurchaseOrderService;

    @Resource
    OrderSettlementSysService orderSettlementSysService;

    @Resource
    DateScriptService dateScriptService;

    @Resource
    PurchaseBillService purchaseBillService;

    @Resource
    WriteOffBizService writeOffBizService;

    @Resource
    SalesOutStockBizService salesOutStockBizService;

    @Resource
    PaymentOrderBizService paymentOrderBizService;

    @Resource
    PurchaseOrderBizService purchaseOrderBizService;

    @Resource
    RefundStockInPrice refundStockInPrice;

//    @Resource
//    StockConfigDetailBizService stockConfigDetailBizService;

    @Resource
    ICombinationItemService iCombinationItemService;

    @Resource
    SuiteFetcher suiteFetcher;

    @Resource
    ShopGateway shopGateway;

    @Resource
    DaddylabWorkbenchGateway daddylabWorkbenchGateway;

    @Resource
    IOaCallbackService oaCallbackService;

    @Resource
    PaymentOrderBizServiceImpl paymentOrderBizServiceImpl;

    @Resource
    DaddylabWorkbenchFeignClient daddylabWorkbenchFeignClient;

    @Resource
    CategoryGateway categoryGateway;

    @Resource
    StockOutOrderBizService stockOutOrderBizService;

    @Resource
    IAfterSaleShareLinkUserService afterSaleShareLinkUserService;

    @Resource
    AfterSaleShareLinkBizService afterSaleShareLinkBizService;

    @Resource
    ItemProcurementGateway itemProcurementGateway;


    @Resource
    WarehouseBizService warehouseBizService;

    @SneakyThrows
    @Test
    public void test0() {
//        final Map<Long, Long> buyerUidList = itemProcurementGateway.getBuyerUidList(ListUtil.of(363555L));
//        System.out.println(JsonUtil.toJson(buyerUidList));

//        StockConfigDetailDto stockConfigDetailDto = new StockConfigDetailDto();
//        stockConfigDetailDto.setSaveType(2);
//        stockConfigDetailDto.setRootId(2313L);
//        stockConfigDetailDto.setWarehouseNo("CK000121");
//        stockConfigDetailDto.setSkuCode("SPEC0004");
//        stockConfigDetailDto.setSpuCode("0004");
//        stockConfigDetailDto.setInventoryRatio(19);
//        stockConfigDetailBizService.updateStockConfigDetail(ListUtil.of(stockConfigDetailDto));

//        StockConfigDetailPageQuery stockConfigDetailPageQuery = new StockConfigDetailPageQuery();
//        VwStockConfigDetailCmd cmd = new VwStockConfigDetailCmd();
//        cmd.setEntryType(1);
//        cmd.setVirtualWarehouseId(7L);
////        cmd.setWarehouseNo();
////        cmd.setInventoryRatio();
//        stockConfigDetailPageQuery.setQueryType(1);
//        stockConfigDetailPageQuery.setVwStockConfigDetailCmd(cmd);
//        PageResponse<StockConfigDetailPageVO> page = stockConfigDetailBizService.page(stockConfigDetailPageQuery);
//        System.out.println(JsonUtil.toJson(page));

//        CombinationItem byId = iCombinationItemService.getById(146L);
//        suiteFetcher.fixComposeSkuProportion(byId);
//        shopGateway.getShopEntity(2307L);


//        final List<OaCallback> oaCallbacks = oaCallbackService.lambdaQuery().eq(OaCallback::getBusinessId, "FKD202402261501").orderByAsc(OaCallback::getId).list();
//        final List<PrintOrderAuditVo> printOrderAuditVoList = oaCallbacks.stream()
//                .map(val->{
//                    return paymentOrderBizServiceImpl.getPrintOrderAuditVo(val);
//                }).filter(Objects::nonNull).collect(Collectors.toList());
//        System.out.println(JsonUtil.toJson(printOrderAuditVoList));

//        purchaseOrderBizService.submit(2856L);

//        {\"processInstId\":\"01150a90-9ba8-429e-9d35-daac18d032eb\",\"callBackState\":3}
//        afterSaleShareLinkUserService.lambdaUpdate().eq(AfterSaleShareLinkUser::getLinkId,1).remove();

//
//        OaCgfkCallback oaCgfkCallback = new OaCgfkCallback();
//        oaCgfkCallback.setProcessInstId("FKDT202406251101");
//        oaCgfkCallback.setNodeName("模拟银行付款中");
//        oaCgfkCallback.setUserLoginName("yulin.du");
//        oaCgfkCallback.setStatus(2);
//        oaCgfkCallback.setPaymentStatus(null);
//        SpringUtil.getBean(PaymentOrderBizService.class).oaCallback(oaCgfkCallback);

//        String ss = "{\\\"processInstId\\\":\\\"\\\",\\\"nodeName\\\":\\\"\\u6A21\\u62DF\\u94F6\\u884C\\u4ED8\\u6B3E\\u4E2D\\\",\\\"userLoginName\\\":\\\"yulin.du\\\",\\\n" +
//                "         * \"status\\\":2,\\\"paymentStatus\\\":null}";

//        JinRenFeignClient jinRenFeignClient = SpringUtil.getBean(JinRenFeignClient.class);
//        JinRenRequestWrapper jinRenRequestConvert = SpringUtil.getBean(JinRenRequestWrapper.class);
//        JinRenResponseDto jinRenResponseDto = jinRenFeignClient.makeOutInvoice(jinRenRequestConvert.convertFromDto(invoiceReqDto));
//        System.out.println(JsonUtil.toJson(jinRenResponseDto));


//        SpringUtil.getBean(RocketMQProducer.class).asyncSend("test0001", "jinRen_invoice_callback", "jinRen",
//                String.valueOf(System.currentTimeMillis()),
//                sendResult -> {
//                    // 更新订单同步状态 收到开票回调 ------> 通知电商后台成功
//                    log.info("通知 success");
//                }, throwable -> {
//                    // 更新订单同步状态 收到开票回调 ------> 通知电商后台失败
//                    log.error("通知 success");
//                });
//        System.in.read();

//        final ShopBizService shopBizService = SpringUtil.getBean(ShopBizService.class);
//        ShopQuery shopQuery = new ShopQuery();
//        shopQuery.setBusinessLine(ListUtil.of(0,1,2,3));
//        final PageResponse<ShopListItem> shopListItemPageResponse = shopBizService.pageQueryShopList(shopQuery);
//        System.out.println(JsonUtil.toJson(shopListItemPageResponse));

        /*final VirtualWarehouseBizService virtualWarehouseBizService = SpringUtil.getBean(VirtualWarehouseBizService.class);
        VirtualWarehousePageQuery pageQuery = new VirtualWarehousePageQuery();
        pageQuery.setInventoryMode(InventoryMode.LOCK);
        final PageResponse<VirtualWarehousePageVo> page = virtualWarehouseBizService.page(pageQuery);
        System.out.println(JsonUtil.toJson(page));
        */

//        SkuStockChangeDto skuStockChangeDto = new SkuStockChangeDto();
//        skuStockChangeDto.setSkuCode("xunicang0529");
//        skuStockChangeDto.setSpuCode("xunicang0529");
//        skuStockChangeDto.setStockNum(210);
//
//        warehouseBizService.stockSkuChangeHandler("wdtapi3-test"ListUtil.of(skuStockChangeDto),1);

        final List<String> filterGoodsNos = SpringUtil.getBean(AfterSaleLogisticsConfig.class).getFilterGoodsNos();
        System.out.println(JsonUtil.toJson(filterGoodsNos));

        final WdtRefundStockInOrderMapper bean = SpringUtil.getBean(WdtRefundStockInOrderMapper.class);
        bean.selectPreStockInList("1","2",ListUtil.of("1","2"));

//        UploadFileAction action = UploadFileAction.ofFile(excelFile);
//        FileStub fileStub = fileGateway.uploadFile(action);
        OssConfig ossConfig = SpringUtil.getBean(OssConfig.class);
//        String url = fileStub.getUrl();
        String url = "https://daddyoss-private.oss-cn-hangzhou.aliyuncs.com/Upload/tech/20241015/101910.pdf?Expires=1729066691&OSSAccessKeyId=LTAI5t7qeNHcvUXKwcab3gWp&Signature=WkM1xVfxtbVemKZjsnVP7jarROA%3D";
        String path = url.replaceAll(ossConfig.getPrivateUrl(), "");
        String resUrl = SpringUtil.getBean(OssGateway.class).generatePresignedUrl(true, path);
        System.out.println(resUrl);
    }

    public void lockTest() {
        RLock lock = redissonClient.getLock("lockTest");
        ThreadUtil.concurrencyTest(10, () -> {
            try {
                boolean b = lock.tryLock();
                if (b) {
                    System.out.println(Thread.currentThread().getName() + ",get ok");
                    TimeUnit.MINUTES.sleep(2);
                } else {
                    System.out.println(Thread.currentThread().getName() + ",get fail");
                }
            } catch (Exception e) {
                log.error("fail", e);
            } finally {
                lock.unlock();
            }
        });
    }

    private void buildMockData() {
        List<PurchaseOrder> list = iPurchaseOrderService.lambdaQuery().orderByDesc(PurchaseOrder::getId).last("limit 15").list();
        list.forEach(purchaseOrder -> {
            TimeBO timeBO = TimeBO.of("202307");
            orderSettlementSysService.autoCreateOrderSettlementInfo(timeBO, purchaseOrder.getNo());
        });
    }

    private void viewFormInfoTest() {
        FormDetailCmd cmd = new FormDetailCmd();
        cmd.setIsAdd(false);
        cmd.setIds(ListUtil.of(62L));
        System.out.println(JsonUtil.toJson(orderSettlementBizService.viewFormInfo(cmd)));
    }

    private void settlementPageQueryTest() {
        SettlementOrderPageQuery query = new SettlementOrderPageQuery();
        query.setPageSize(10);
        query.setPageIndex(1);
//        query.setOrderPersonnelId(7735309L);
//        List<Long> of = ListUtil.of(1690819200L, 1690819200L);
//        List<Long> of2 = ListUtil.of(1685548800L, 1690819200L);
//        List<List<Long>> of1 = ListUtil.of(of,of2);
//        query.setTimes(of1);
        orderSettlementBizService.settlementPageQuery(query);
    }

    private void sysBillPageQueryTest() {
        SysBillPageQuery query = new SysBillPageQuery();
        query.setStatus(OrderSettlementStatus.WAIT_CONFIRM);
        query.setPageSize(20);
        query.setPageIndex(1);
        query.setStatus(OrderSettlementStatus.WAIT_CONFIRM);
//        query.setWarehouseNos(ListUtil.of("wdtapi3-test"));

//        List<Long> of = ListUtil.of(1690819200L, 1690819200L);
//        List<Long> of2 = ListUtil.of(1685548800L, 1690819200L);
//        List<List<Long>> of1 = ListUtil.of(of,of2);
////        query.setTimes(of1);
//        query.setStatusVal(OrderSettlementStatus.WAIT_CONFIRM.getValue());
//        query.setWarehouseNos(ListUtil.of("CK000121", "CK000121"));
//        orderSettlementBizService.sysBillPageQuery(query).getData();
//        orderSettlementFormMapper.selectBill(query);
        List<SysBillPageVo> data = orderSettlementBizService.sysBillPageQuery(query).getData();
        System.out.println("-- test --");
        System.out.println(JsonUtil.toJson(data));
    }


//    private void exportAllSettlementInfo() {
//        orderSettlementBizService.exportSettlement(152L);
//    }

    private void exportBillDataTest() {
        ExportSysBillCmd cmd = new ExportSysBillCmd();
        cmd.setIds(ListUtil.of(152L));
        orderSettlementBizService.exportBillData(cmd);
    }

    @Resource
    WdtRefundOrderMapper wdtRefundOrderMapper;

    @Resource
    AfterSalesRegisterBizService afterSalesRegisterBizService;

    public void parallelExcelWrite() throws InterruptedException {
        File file = new File("demo.xlxs");
        ExcelWriter writer = EasyExcel.write(file).build();

        ThreadUtil.execute(() -> {
            WriteSheet writeSheet = EasyExcel.writerSheet("run1").build();
            writer.write(ListUtil.of("r11", "r12"), writeSheet);
        });

        ThreadUtil.execute(() -> {
            WriteSheet writeSheet = EasyExcel.writerSheet("run2").build();
            writer.write(ListUtil.of("r21", "r22"), writeSheet);
        });

        TimeUnit.SECONDS.sleep(5);

        writer.finish();
        System.out.println("------ end ----------");
    }


    public void excelDemo() throws Exception {
        // 创建固定线程数的 ExecutorService
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        // 创建 CompletableFuture 列表，每个 CompletableFuture 对应一个 sheet 写入任务
        List<CompletableFuture<Void>> completableFutures = new ArrayList<>();
        for (int i = 1; i <= 5; i++) {
            final int sheetNumber = i;
            CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                // 写入数据到 sheet
                ExcelWriter excelWriter = EasyExcel.write("output.xlsx").build();
                WriteSheet writeSheet = EasyExcel.writerSheet(sheetNumber, "Sheet " + sheetNumber).build();
                // 使用 excelWriter 和 writeSheet 将数据写入 sheet、
                excelWriter.write(new ArrayList<>(), writeSheet);
                // ...
                excelWriter.finish(); // 关闭流并保存文件
            }, executorService);
            completableFutures.add(completableFuture);
        }
        // 等待所有 CompletableFuture 完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0]));
        allFutures.get(); // 等待所有 sheet 写入完成
        // 关闭 ExecutorService
        executorService.shutdown();
    }


    public static void main(String[] args) throws Exception {
        System.out.println(File.separator);
    }

    @Resource
    IWdtOrderDetailWrapperService wrapperService;

    @Resource
    WdtOrderDetailWrapperMapper wdtOrderDetailWrapperMapper;

    public void writeBillDemo() {
//        List<String> warehouseNos = ListUtil.of("WH2023031402");
//        List<String> operateTimes = ListUtil.of("202308");
//        List<WrapperType> wrapperTypes = ListUtil.of(WrapperType.STOCK_OUT_SINGLE, WrapperType.STOCK_OUT_COMBINATION);
//
//        List<Integer> types = wrapperTypes.stream().map(WrapperType::getValue).collect(Collectors.toList());
//
//        List<WdtOrderDetailWrapper> subList = wdtOrderDetailWrapperMapper.selectList(warehouseNos, operateTimes,
//                0, 10, types);

//
//        System.out.println(JsonUtil.toJson(subList));
//

        String a = "this is a demo";


    }


    @SneakyThrows
    public void testThread() {
        int size = 2;
        CountDownLatch countDownLatch = new CountDownLatch(size);
        Semaphore semaphore = new Semaphore(6);

        for (int i = 0; i < size; i++) {
            com.daddylab.supplier.item.common.util.ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
                try {
                    semaphore.acquire();
                    TimeUnit.SECONDS.sleep(3);
                    log.info("thread:{}.doing", Thread.currentThread().getName());
                    TimeUnit.SECONDS.sleep(2);
                    log.info("thread:{}.finish", Thread.currentThread().getName());
                } catch (Exception e) {
                    log.error("处理失败");
                } finally {
                    semaphore.release();
                    countDownLatch.countDown();
                }
            });
        }


        countDownLatch.await();
        log.info("all thread finished");
    }


//        settlementPageQueryTest();


//        SysBillPageQuery query2 = new SysBillPageQuery();
//        query2.setStatus(OrderSettlementStatus.WAIT_CONFIRM);
//        query2.setPageSize(10);
//        query2.setPageIndex(2);
//        List<SysBillPageVo> data2 = orderSettlementBizService.sysBillPageQuery(query2).getData();
//        System.out.println("tttt2:" + data2.size());


//        List<String> orderPersonnelNickName = iWarehouseService.getOrderPersonnelNickName("CK000121");
//        System.out.println(JsonUtil.toJson(orderPersonnelNickName));

//        lockTest();

//        SettlementOrderPageQuery query = new SettlementOrderPageQuery();
//        query.setWarehouseNo("CK000120");
//        PageResponse<SettlementOrderPageVo> settlementOrderPageVoPageResponse = orderSettlementBizService.settlementPageQuery(query);
//        System.out.println(JsonUtil.toJson(settlementOrderPageVoPageResponse));


//        TaskPageQuery query = new TaskPageQuery();
//        query.setType(ExportTaskType.SETTLEMENT_DETAIL);
//        PageResponse<ExportTaskVo> exportTaskVoPageResponse = exportTaskBizService.exportList(query);
//        System.out.println(JsonUtil.toJson(exportTaskVoPageResponse));

//        ExportSysBillCmd cmd = new ExportSysBillCmd();
//        cmd.setIds(ListUtil.of(2L));
//        orderSettlementBizService.exportBillData(cmd);
//        System.in.read();


//        String ss = "{\"warehouseNo\":\"CK000121\",\"fullAddress\":\"浙江省杭州市萧山区瓜沥镇长联村杭州和润实业内\",\"contacts\":\"颜建森（老爸评测）\",\"tel\":\"13777495636\",\"orderPersonnelIds\":[8092845]}";
//        EditWarehouseAfterSalesAddressCmd cmd = JsonUtil.parse(ss,EditWarehouseAfterSalesAddressCmd.class);
//        afterSalesWarehouseBizService.editWarehouseAfterSalesInfo(cmd);


//        FormDetailCmd cmd = JsonUtil.parse("{\"ids\":[2],\"isAdd\":true}", FormDetailCmd.class);
//        FormDetailVo formDetailVo = orderSettlementBizService.viewFormInfo(cmd);
//        System.out.println(JsonUtil.toJson(formDetailVo));

//        SettlementOrderPageQuery query = new SettlementOrderPageQuery();
//        query.setPageSize(10);
//        query.setPageSize(1);
//        orderSettlementBizService.settlementPageQuery(query);

//        File file = new File("/Users/<USER>/Desktop/工厂采购结算单导入模版t01.xlsx");
//        FileInputStream fileInputStream = new FileInputStream(file);
//        orderSettlementBizService.importDetailExcel(fileInputStream,6L);


    private void saveDemo() {
        String ss = "{\n" +
                "    \"pProviderId\": 20299,\n" +
                "    \"sProviderId\": 5954,\n" +
                "    \"ids\": [\n" +
                "        1\n" +
                "    ],\n" +
                "    \"removeIds\": [],\n" +
                "    \"detailList\": [\n" +
                "        {   \n" +
                "            \"settlementStartDate\":**********,\n" +
                "            \"settlementEndDate\":**********,\n" +
                "            \"id\": 2,\n" +
                "            \"cycle\": \"2023-08-01~2023-08-31\",\n" +
                "            \"skuCode\": \"W100021301\",\n" +
                "            \"itemName\": \"薯条多店测试商品0808\",\n" +
                "            \"specifications\": \"颜色:1|规格:1\",\n" +
                "            \"unit\": \"栋\",\n" +
                "            \"deliverQuantity\": 4,\n" +
                "            \"temporaryPrice\": 14,\n" +
                "            \"temporaryQuantity\": 2,\n" +
                "            \"currentMonthRefundQuantity\": 4,\n" +
                "            \"crossMonthRefundQuantity\": 5,\n" +
                "            \"settlementPrice\": 15,\n" +
                "            \"settlementQuantity\": -5,\n" +
                "            \"settlementAmount\": \"-75.00\",\n" +
                "            \"afterSalesCost\": \"12\",\n" +
                "            \"finalAmount\": \"-63.00\",\n" +
                "            \"remark\": null,\n" +
                "            \"source\": null,\n" +
                "            \"fakeId\": 2\n" +
                "        }\n" +
                "    ],\n" +
                "    \"detailStaticDo\": {\n" +
                "        \"totalSettlementAmount\": -15,\n" +
                "        \"totalFinalAmount\": -3,\n" +
                "        \"tsDeliverQuantity\": 14,\n" +
                "        \"tsCurrentMonthRefundQuantity\": 6,\n" +
                "        \"tsCrossMonthRefundQuantity\": 8,\n" +
                "        \"tsSettlementQuantity\": 0,\n" +
                "        \"totalSettlementQuantity\": 0,\n" +
                "        \"tsSettlementAmount\": -15,\n" +
                "        \"tsFinalAmount\": -3\n" +
                "    }\n" +
                "}";
        SettlementSaveCmd cmd = JsonUtil.parse(ss, SettlementSaveCmd.class);
        orderSettlementBizService.save(cmd);
    }


    private void createTestData() {
        OrderSettlementForm form = new OrderSettlementForm();
        form.setSettlementStartDate(1688140800L);
        form.setSettlementEndDate(**********L);
        form.setPProviderId(20292L);
        form.setSProviderId(20292L);
        form.setWarehouseNo("CK000120");
        form.setStatus(OrderSettlementStatus.WAIT_CONFIRM);
        form.setPurchaseOrderNo("CG2308170001");
        form.setTemporaryQuantity(3);
        form.setSettlementQuantity(3);
        DetailStaticDo staticDo = new DetailStaticDo();
        staticDo.setTsTemporaryQuantity(0);
        staticDo.setTsDeliverQuantity(0);
        staticDo.setTsCurrentMonthRefundQuantity(0);
        staticDo.setTsCrossMonthRefundQuantity(0);
        staticDo.setTsSettlementQuantity(0);
        staticDo.setTsSettlementAmount(new BigDecimal("0"));
        staticDo.setTsFinalAmount(new BigDecimal("0"));
        staticDo.setTsRemark("");
        staticDo.setTransportAmount(new BigDecimal("0"));
        staticDo.setTransportFinalAmount(new BigDecimal("0"));
        staticDo.setTransportRemark("");
        staticDo.setAfterSalesAmount(new BigDecimal("0"));
        staticDo.setAfterSalesFinalAmount(new BigDecimal("0"));
        staticDo.setAfterSalesRemark("");
        staticDo.setOtherAmount(new BigDecimal("0"));
        staticDo.setOtherFinalAmount(new BigDecimal("0"));
        staticDo.setOtherRemark("");
        staticDo.setTotalSettlementQuantity(0);
        staticDo.setTotalSettlementAmount(new BigDecimal("0"));
        staticDo.setTotalFinalAmount(new BigDecimal("0"));
        staticDo.setTotalRemark("");
        staticDo.setTotalTemporaryQuantity(0);
        form.setStaticInfo(JsonUtil.toJson(staticDo));
        iOrderSettlementFormService.save(form);

        Long formId = form.getId();
        List<OrderSettlementDetail> detailList = new LinkedList<>();
        for (int i = 0; i < 3; i++) {
            OrderSettlementDetail detail = new OrderSettlementDetail();
            detail.setFormId(formId);
            detail.setFormNo("");
            detail.setSettlementStartDate(0L);
            detail.setSettlementEndDate(0L);
            detail.setSkuCode("10002010" + i);
            detail.setTemporaryPrice(new BigDecimal("0"));
            detail.setTemporaryQuantity(0);
            detail.setDeliverQuantity(0);
            detail.setCurrentMonthRefundQuantity(0);
            detail.setCrossMonthRefundQuantity(0);
            detail.setSettlementQuantity(0);
            detail.setSettlementPrice(new BigDecimal("0"));
            detail.setSettlementAmount(new BigDecimal("0"));
            detail.setAfterSalesCost(new BigDecimal("0"));
            detail.setFinalAmount(new BigDecimal("0"));
            detail.setRemark("");
            detail.setSource("");
            detailList.add(detail);
        }
        iOrderSettlementDetailService.saveBatch(detailList);

    }

    private void exportDetailExcel() throws Exception {
        orderSettlementBizService.exportDetailExcel(1L);
        int read = System.in.read();
    }

    private void importDetailExcel() throws Exception {
        File file = new File("/Users/<USER>/Downloads/GCJS202312000834_老爸评测【苏州多淘电子商务有限公司】订货明细单_j6b1.xlsx");
        FileInputStream fileInputStream = new FileInputStream(file);
        orderSettlementBizService.importDetailExcel(fileInputStream, 1L);
    }


}


