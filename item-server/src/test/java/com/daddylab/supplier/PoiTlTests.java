package com.daddylab.supplier;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;

import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/12/6
 */
public class PoiTlTests {
    @Test
    public void html2word() throws IOException {
        final HashMap<String, Object> model = new HashMap<>();
        model.put("采购单号", "CG2323123");
        model.put("采购组织", "老爸评测");
        model.put("到货方式", "一次性到货");
        model.put("采购日期", "2023-10-31");
        model.put("采购部门", "/");
        model.put("付款方式", "月结");
        model.put("供应商", "金华市出赞玩具公司");
        model.put("采购员", "徵乌");
        model.put("价税合计", "¥31.20");
        final ArrayList<Map<String, Object>> details = new ArrayList<>();
        details.add(
                new HashMap<String, Object>() {
                    {
                        put("商品SKU", "W123123");
                        put("商品名称", "玩具");
                        put("规格名称", "128G");
                        put("单位", "个");
                        put("数量", "999");
                        put("单价", "999");
                        put("金额", "999*999");
                        put("价税合计", "999*999");
                        put("备注", "");
                    }
                });
        model.put("商品明细", details);
        model.put("创建人", "徵乌");
        model.put("创建时间", "2023-12-10");

        final Configure config =
                Configure.builder().bind("商品明细", new LoopRowTableRenderPolicy()).build();
        XWPFTemplate template =
                XWPFTemplate.compile("/Users/<USER>/Desktop/ERP/采购订单导出模板.docx", config)
                        .render(model);
        template.writeAndClose(
                Files.newOutputStream(Paths.get("/Users/<USER>/Desktop/ERP/output.docx")));
    }
}
