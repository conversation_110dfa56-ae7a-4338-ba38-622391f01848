package com.daddylab.supplier;

import com.daddylab.supplier.item.application.platformItem.config.PlatformItemSyncConfig;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.bigdecimal.Decimal;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.junit.jupiter.api.Test;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.env.YamlPropertySourceLoader;
import org.springframework.core.env.CompositePropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.yaml.snakeyaml.Yaml;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.time.Duration;
import java.util.*;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2024/3/29
 */
public class CommonTests {
    @Test
    public void decimalFormat() {
        final BigDecimal value =  BigDecimal.valueOf(0.00000f);
        final DecimalFormat decimalFormat = new DecimalFormat("###",
                DecimalFormatSymbols.getInstance(Locale.getDefault()));
        decimalFormat.setRoundingMode(RoundingMode.HALF_UP);
        final String format = decimalFormat.format(value);
        System.out.println(format);

    }


    @Test
    public void test1() throws IOException {
        final YamlPropertySourceLoader yamlPropertySourceLoader = new YamlPropertySourceLoader();
        final DefaultResourceLoader defaultResourceLoader = new DefaultResourceLoader();
        final Resource resource = defaultResourceLoader.getResource("application.yml");
        final List<PropertySource<?>> propertySources =
                yamlPropertySourceLoader.load("test-properties", resource);
        final CompositePropertySource compositePropertySource =
                new CompositePropertySource("test-properties");
        propertySources.forEach(compositePropertySource::addPropertySource);
        final StandardEnvironment standardEnvironment = new StandardEnvironment();
        standardEnvironment.getPropertySources().addFirst(compositePropertySource);
        final Binder binder = Binder.get(standardEnvironment);
        final PlatformItemSyncConfig config = binder.bindOrCreate("test-platform-item", PlatformItemSyncConfig.class);
        System.out.println(JsonUtil.toJson(config));


    }

    @Test
    public void test2() throws JsonProcessingException {
        int skuInventoryRatio2 = 7;
        int inventoryRatio = 6;
        //店铺库存占比
        final Decimal result = Stream.of(skuInventoryRatio2, inventoryRatio)
                                             .filter(Objects::nonNull)
                                             .map(Decimal::of)
                                             .map(v -> v.divide(100))
                                             .findFirst()
                                             .orElseGet(Decimal::of);
        System.out.println(result);
    }
}
