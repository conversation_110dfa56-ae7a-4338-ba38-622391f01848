package com.daddylab.supplier;

import com.daddylab.mall.wdtsdk.apiv2.statistic.dto.SearchLogisticsTraceResponse;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import java.util.List;
import lombok.Getter;
import lombok.ToString;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2023/11/17
 */
public class JacksonTests {
  @ToString
  public static class TestBean {
    @Getter private String a;
    @Getter private String b;
  }

  @Test
  public void testPrivateAccess() {
    final ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.configure(MapperFeature.CAN_OVERRIDE_ACCESS_MODIFIERS, false);

    final TestBean test;
    try {
      test = objectMapper.readValue("{\"a\":\"a\",\"b\":\"b\"}", TestBean.class);
      System.out.println(test);
      Assertions.assertNotNull(test);
      Assertions.assertNotNull(test.a);
      Assertions.assertNotNull(test.b);
    } catch (JsonProcessingException e) {
      Assertions.assertNotNull(e.getCause());
      Assertions.assertTrue(e.getCause() instanceof IllegalAccessException);
    }
  }

  @Test
  public void test() throws JsonProcessingException {
    final ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
    final List<SearchLogisticsTraceResponse.DetailListItem> detailListItems =
        objectMapper.readValue(
            "[{\"trace_id\": 24066670, \"accept_time\": \"2024-11-23 14:46:58\", \"trace_status\": 2, \"accept_station\": \"【乌市新市区城北网点】的刘红青（18129485564）已取件，投诉电话：0991-2208734\"}, {\"trace_id\": 24066670, \"accept_time\": \"2024-11-23 14:47:08\", \"trace_status\": 3, \"accept_station\": \"快件离开【乌市新市区城北网点】已发往【乌市转运中心】\"}, {\"trace_id\": 24066670, \"accept_time\": \"2024-11-24 02:14:01\", \"trace_status\": 3, \"accept_station\": \"快件到达【乌市转运中心】\"}, {\"trace_id\": 24066670, \"accept_time\": \"2024-11-24 02:18:55\", \"trace_status\": 3, \"accept_station\": \"快件离开【乌市转运中心】已发往【西安转运中心】\"}, {\"trace_id\": 24066670, \"accept_time\": \"2024-11-28 05:08:43\", \"trace_status\": 3, \"accept_station\": \"快件到达【西安转运中心】\"}, {\"trace_id\": 24066670, \"accept_time\": \"2024-11-28 05:17:11\", \"trace_status\": 3, \"accept_station\": \"快件离开【西安转运中心】已发往【杭州转运中心A1】\"}]",
            new TypeReference<List<SearchLogisticsTraceResponse.DetailListItem>>() {});
    System.out.println(detailListItems);
    System.out.println(JsonUtil.toJson(detailListItems));
  }
}
