package com.daddylab.supplier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import cn.hutool.core.io.FileUtil;

import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.*;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.observables.ConnectableObservable;
import io.reactivex.rxjava3.observers.TestObserver;
import io.reactivex.rxjava3.plugins.RxJavaPlugins;
import io.reactivex.rxjava3.schedulers.TestScheduler;
import io.reactivex.rxjava3.subjects.PublishSubject;

import lombok.extern.slf4j.Slf4j;

import org.hamcrest.CoreMatchers;
import org.hamcrest.MatcherAssert;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @since 2022/11/29
 */
@Slf4j
public class RxJavaTest {

    @Test
    public void connectableObservable() throws InterruptedException {
        String[] result = {""};
        ConnectableObservable<Long> connectable =
                Observable.interval(200, TimeUnit.MILLISECONDS).publish();
        connectable.subscribe(
                i -> {
                    log.info("subscribe: " + i);
                    result[0] += i;
                });
        log.info(result[0]);

        connectable.connect();
        Thread.sleep(500);
        log.info(result[0]);
        Thread.sleep(500);
        log.info(result[0]);
    }

    @Test
    public void publishSubject() {
        AtomicInteger subscriber1 = new AtomicInteger();
        AtomicInteger subscriber2 = new AtomicInteger();
        final Observer<Integer> observer1 =
                new Observer<Integer>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        log.info("Subscriber1 onSubscribe: ");
                    }

                    @Override
                    public void onNext(Integer value) {
                        log.info("Subscriber1 onNext: " + value);
                        subscriber1.addAndGet(value);
                    }

                    @Override
                    public void onError(Throwable e) {
                        log.info("Subscriber1 error");
                    }

                    @Override
                    public void onComplete() {
                        log.info("Subscriber1 completed");
                    }
                };
        final Observer<Integer> observer2 =
                new Observer<Integer>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        log.info("Subscriber2 onSubscribe: ");
                    }

                    @Override
                    public void onNext(Integer value) {
                        log.info("Subscriber2 onNext: " + value);
                        subscriber1.addAndGet(value);
                    }

                    @Override
                    public void onError(Throwable e) {
                        log.info("Subscriber2 error");
                    }

                    @Override
                    public void onComplete() {
                        log.info("Subscriber2 completed");
                    }
                };

        PublishSubject<Integer> subject = PublishSubject.create();
        subject.subscribe(observer1);
        subject.onNext(1);
        subject.onNext(2);
        subject.onNext(3);
        subject.subscribe(observer2);
        subject.onNext(4);
        subject.onComplete();
        subject.subscribe(observer2);
        subject.onNext(5);

        log.info("subscriber1 + subscriber2: {}", subscriber1.get() + subscriber2.get());
    }

    @Test
    public void using() {
        String[] result = {""};
        Observable<Character> values =
                Observable.using(
                        () -> "MyResource",
                        r ->
                                Observable.create(
                                        o -> {
                                            for (Character c : r.toCharArray()) {
                                                log.info("resource using: " + c);
                                                o.onNext(c);
                                            }
                                            log.info("resource use complete");
                                            o.onComplete();
                                        }),
                        r -> System.out.println("Disposed: " + r));
        values.subscribe(
                v -> {
                    log.info("onNext: " + v);
                    result[0] += v;
                },
                e -> {
                    log.error("onError: " + e.getMessage());
                    result[0] += e;
                },
                () -> {
                    log.info("onComplete");
                });
        assertTrue(result[0].equals("MyResource"));
    }

    @Test
    public void flatMap() {
        List<String> actualOutput = new ArrayList<>();
        TestScheduler scheduler = new TestScheduler();
        List<String> keywordToSearch = Arrays.asList("b", "bo", "boo", "book", "books");

        Observable.fromIterable(keywordToSearch)
                .doOnNext(v -> log.info("searching: " + v))
                .flatMap(
                        s ->
                                Observable.just(s + " FirstResult", s + " SecondResult")
                                        .delay(30, TimeUnit.SECONDS, scheduler)
                                        .doOnNext(v -> log.info("searched:" + v)))
                .toList()
                .doOnSuccess(
                        s -> {
                            log.info("search success: " + s);
                            actualOutput.addAll(s);
                        })
                .subscribe();

        scheduler.advanceTimeBy(1, TimeUnit.MINUTES);

        MatcherAssert.assertThat(
                actualOutput,
                CoreMatchers.hasItems(
                        "b FirstResult",
                        "b SecondResult",
                        "boo FirstResult",
                        "boo SecondResult",
                        "bo FirstResult",
                        "bo SecondResult",
                        "book FirstResult",
                        "book SecondResult",
                        "books FirstResult",
                        "books SecondResult"));
    }

    @Test
    public void whenRetryWhenForMultipleTimesOnError_thenResumed() {
        TestObserver testObserver = new TestObserver();
        long before = System.currentTimeMillis();
        Observable.error(
                        () -> {
                            log.info("return error");
                            return new RuntimeException("some bug");
                        })
                .retryWhen(
                        throwableObservable ->
                                throwableObservable
                                        .zipWith(
                                                Observable.range(1, 3),
                                                (throwable, integer) -> {
                                                    log.info("zipWith:{},{}", throwable, integer);
                                                    return integer;
                                                })
                                        .flatMap(
                                                integer -> {
                                                    log.info("flatMap:{}", integer);
                                                    return Observable.timer(
                                                            integer, TimeUnit.SECONDS);
                                                }))
                .blockingSubscribe(testObserver);

        testObserver.assertNoErrors();
        testObserver.assertComplete();
        testObserver.assertNoValues();
        long secondsElapsed = (System.currentTimeMillis() - before) / 1000;
        assertEquals(6, secondsElapsed, "6 seconds should elapse");
    }

    @Test
    public void flowableGenerate() {
        final Flowable<Object> flowable =
                Flowable.generate(
                        () -> new InputStreamReader(FileUtil.getInputStream("/tmp/t")),
                        (inputStream, output) -> {
                            try {
                                final char[] chars = new char[1];
                                int abyte = inputStream.read(chars);
                                if (abyte < 0) {
                                    output.onComplete();
                                } else {
                                    output.onNext(new String(chars));
                                }
                            } catch (IOException ex) {
                                output.onError(ex);
                            }
                            return inputStream;
                        },
                        inputStream -> {
                            try {
                                inputStream.close();
                            } catch (IOException ex) {
                                RxJavaPlugins.onError(ex);
                            }
                        });
        flowable.subscribe(v -> log.info("{}", v), v -> log.error("error", v));
        flowable.blockingSubscribe();
    }

    @Test
    public void fromCompletionStageReturnVoid() {
        Completable.fromCompletionStage(
                        CompletableFuture.runAsync(
                                () -> {
                                    log.info("run");
                                }))
                .doOnError(
                        e -> {
                            log.error("err", e);
                        })
                .retryWhen(
                        throwableObservable -> {
                            final AtomicInteger count = new AtomicInteger();
                            return throwableObservable.flatMap(
                                    e -> {
                                        final int retryNum = count.incrementAndGet();
                                        if (retryNum > 3) {
                                            log.warn("The number of retries exceeded the limit");
                                            return Flowable.error(e);
                                        }
                                        log.warn("retry {}", retryNum);
                                        return Flowable.timer(1000, TimeUnit.MILLISECONDS);
                                    });
                        })
                .blockingSubscribe();
    }
}
