package com.daddylab.supplier.framework;

import com.daddylab.supplier.item.infrastructure.utils.FileUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import io.github.swagger2markup.GroupBy;
import io.github.swagger2markup.Language;
import io.github.swagger2markup.Swagger2MarkupConfig;
import io.github.swagger2markup.Swagger2MarkupConverter;
import io.github.swagger2markup.builder.Swagger2MarkupConfigBuilder;
import io.github.swagger2markup.markup.builder.MarkupLanguage;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.charset.Charset;
import java.nio.file.Paths;

/**
 * 生成swagger离线文档。启动application，执行test
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11 1:45 下午
 * @description
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@Slf4j
public class ApiTest {

    /**
     * 生成Markdown格式文档,并汇总成一个文件
     *
     * @throws Exception
     */
    @Test
    public void generateMarkdownDocsToFile() throws Exception {
        //    输出Markdown到单文件
        Swagger2MarkupConfig config = new Swagger2MarkupConfigBuilder()
                .withMarkupLanguage(MarkupLanguage.MARKDOWN)
                .withOutputLanguage(Language.ZH)
                .withPathsGroupedBy(GroupBy.TAGS)
                .withGeneratedExamples()
                .withoutInlineSchema()
                .build();

        final String generatedDocPath = "./docs/markdown/generated/itemServer-apiDoc";
        Swagger2MarkupConverter.from(new URL("http://127.0.0.1:8080/supplier/item/v2/api-docs"))
                .withConfig(config)
                .build()
                .toFile(Paths.get(generatedDocPath));

        processGeneratedFile(generatedDocPath + ".md");
    }

    private void processGeneratedFile(String generatedDocPath) throws IOException {
        final String processingFilePath = generatedDocPath + ".processing";
        final File processingFile = FileUtil.file(processingFilePath);
        try (final BufferedReader bufferedReader = FileUtil.getBufferedReader(new File(generatedDocPath), Charset.defaultCharset());
             final BufferedWriter processingFileWriter = FileUtil.getWriter(processingFile, Charset.defaultCharset(), false);
        ) {
            final String prefix = "<a name=\"";
            final String suffix = "\"></a>";
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                if (line.startsWith(prefix) && line.endsWith(suffix)) {
                    final String name = line.substring(prefix.length(), line.length() - suffix.length());
                    processingFileWriter.write(StringUtil.format("<a id=\"{}\" name=\"{}\"></a>", name, name) + System.lineSeparator());
                } else {
                    processingFileWriter.write(line + System.lineSeparator());
                }
            }
        }
        FileUtil.move(processingFile, FileUtil.file(generatedDocPath), true);
    }
}
