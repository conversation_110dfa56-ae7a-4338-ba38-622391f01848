package com.daddylab.supplier.framework;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.RepeatedTest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static java.time.Duration.ofSeconds;
import static org.junit.jupiter.api.Assertions.*;

/**
 * junit5 测试demo
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/29 9:46 上午
 * @description
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
public class Junit5Test {

//    @BeforeEach：在每个单元测试方法执行前都执行一遍
//    @BeforeAll：在每个单元测试方法执行前执行一遍（只执行一次）
//    @DisplayName("商品入库测试")：用于指定单元测试的名称
//    @Disabled：当前单元测试置为无效，即单元测试时跳过该测试
//    @RepeatedTest(n)：重复性测试，即执行n次
//    @ParameterizedTest：参数化测试，
//    @ValueSource(ints = {1, 2, 3})：参数化测试提供数据
//    @TestInstance(TestInstance.Lifecycle.PER_CLASS) 设置整个test class 为一个生命周期
//    @Order(1) 执行顺序





    @Test
    @DisplayName("测试断言equals")
    void testEquals() {
        assertTrue(3 < 4);
    }

    @Test
    @DisplayName("测试断言超时")
    void testTimeOut() {
        String actualResult = assertTimeout(ofSeconds(2), () -> {
            Thread.sleep(1000);
            return "a result";
        });
        System.out.println(actualResult);
    }

    @Test
    @DisplayName("测试组合断言")
    void testAll() {
        assertAll("测试item商品下单",
                () -> {
                    //模拟用户余额扣减
                    assertTrue(1 < 2, "余额不足");
                },
                () -> {
                    //模拟item数据库扣减库存
                    assertTrue(3 < 4);
                },
                () -> {
                    //模拟交易流水落库
                    assertNotNull(new Object());
                }
        );
    }

    @RepeatedTest(3)
    @DisplayName("重复测试")
    void repeatedTest() {
        System.out.println("调用");
    }

    @ParameterizedTest
    @ValueSource(ints = {1, 2, 3})
    @DisplayName("参数化测试")
    void paramTest(int a) {
        assertTrue(a > 0 && a < 4);
    }

    @SpringBootTest
    @AutoConfigureMockMvc
    @DisplayName("Junit5单元测试")
    public class MockTest {
        //....
        @Nested
        @DisplayName("内嵌订单测试")
        class OrderTestClas {
            @Test
            @DisplayName("取消订单")
            void cancelOrder() {
                int status = -1;
                System.out.println("取消订单成功,订单状态为:"+status);
            }
        }
    }

}
