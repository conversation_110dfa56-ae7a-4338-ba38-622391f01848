package com.daddylab.supplier.framework;

import com.alibaba.cloud.nacos.NacosConfigAutoConfiguration;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.CommonFieldHandler;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.MybatisPlusConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Configuration
@Import({
        // 加载 Nacos 配置
        NacosConfigAutoConfiguration.class,
        // MybatisPlus 提供的动态数据源配置（可配置多数据源切换）
        DynamicDataSourceAutoConfiguration.class,
        // Spring 数据源自动配置
        DataSourceAutoConfiguration.class,
        // MybatisPlus 自动配置
        MybatisPlusAutoConfiguration.class,
        // MybatisPlus 自定义配置
        MybatisPlusConfig.class,
        // MybatisPlus 自定义字段注入器
        CommonFieldHandler.class,
        // 加载 MybatisPlusJoin 相关配置类
        com.github.yulichang.interceptor.MPJInterceptor.class,
        com.github.yulichang.config.InterceptorConfig.class,
        com.github.yulichang.config.MappingConfig.class,
        com.github.yulichang.toolkit.SpringContentUtils.class,
})
@MapperScan("com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper")
@EnableTransactionManagement
public class BaseConfiguration {

}
