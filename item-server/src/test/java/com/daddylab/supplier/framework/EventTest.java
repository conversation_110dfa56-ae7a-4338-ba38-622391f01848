package com.daddylab.supplier.framework;

import com.google.common.eventbus.AsyncEventBus;
import com.google.common.eventbus.EventBus;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/30 2:33 下午
 * @description
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
public class EventTest {

    @Resource(name = "syncEventBus")
    private EventBus syncEventBus;

    @Resource(name = "asyncEventBus")
    private AsyncEventBus asyncEventBus;

    @Test
    public void test() {

        syncEventBus.post(11);

        asyncEventBus.post("async hello world");
    }


}
