package com.daddylab.supplier.framework;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.mail.MailException;
import org.springframework.mail.MailPreparationException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessagePreparator;

import javax.mail.internet.MimeMessage;
import java.util.Properties;

/**
 * junit 容器是和spring 容器相互独立的。
 * The code of MockMailSender
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/7 8:11 下午
 * @description
 */
@TestConfiguration
public class TestConfigForMail {

    @Bean
    public JavaMailSender mailSender() {
        final JavaMailSenderImpl sender = new MockMailSender();
        return sender;
    }

    private class MockMailSender extends JavaMailSenderImpl {
        @Override
        public void send(final MimeMessagePreparator mimeMessagePreparator) throws MailException {
            final MimeMessage mimeMessage = createMimeMessage();
            try {
                mimeMessagePreparator.prepare(mimeMessage);
                final String content = (String) mimeMessage.getContent();
                final Properties javaMailProperties = getJavaMailProperties();
                javaMailProperties.setProperty("mailContent", content);
            } catch (final Exception e) {
                throw new MailPreparationException(e);
            }
        }
    }
}
