package com.daddylab.supplier.framework;

import cn.hutool.http.HTMLFilter;
import com.daddylab.supplier.item.infrastructure.utils.HTMLFilterFactory;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2022/12/9
 */
public class XssFilterTest {

    @Test
    public void htmlFilter() {
        final HTMLFilter htmlFilter = HTMLFilterFactory.extented();
        final String input = "<p>直播话术 审核123123<img src=\"https://cdn-test.daddylab.com/Upload/supplier/item/image-*************.png\" alt=\"image-*************.png\" /></p>";
        final String filtered = htmlFilter.filter(input);
        Assertions.assertEquals(input, filtered);
    }

    @Test
    public void jsonFilter() {
        final HTMLFilter htmlFilter = HTMLFilterFactory.extented();
        final String input = "{\"name\":\"创新-奶卡\",\"account\":\"asdad\",\"platform\":\"LAOBASHOP\",\"status\":\"PREPARING\",\"link\":\"<p class=\"test\">直播话术 审核123123<img src=\"https://cdn-test.daddylab.com/Upload/supplier/item/image-*************.png\" alt=\"image-*************.png\" /></p>\",\"sn\":\"S200\",\"logo\":\"\",\"principals\":[],\"id\":2313}";
        final String filtered = htmlFilter.filter(input);
        final String expected = "{\"name\":\"创新-奶卡\",\"account\":\"asdad\",\"platform\":\"LAOBASHOP\",\"status\":\"PREPARING\",\"link\":\"<p>直播话术 审核123123<img src=\"https://cdn-test.daddylab.com/Upload/supplier/item/image-*************.png\" alt=\"image-*************.png\" /></p>\",\"sn\":\"S200\",\"logo\":\"\",\"principals\":[],\"id\":2313}";
        Assertions.assertEquals(expected, filtered);
    }

    @Test
    public void jsonFilter1() {
        final HTMLFilter htmlFilter = HTMLFilterFactory.extented();
        final String input = "{\"id\":618,\"itemId\":363512,\"attrImages\":[{\"attrId\":599,\"attrName\":\"颜色\",\"itemAttrs\":[{\"id\":25448,\"attrValue\":\"/\",\"url\":null}],\"imagesAdded\":false},{\"attrId\":602,\"attrName\":\"规格\",\"itemAttrs\":[{\"id\":25449,\"attrValue\":\"/\",\"url\":null}],\"imagesAdded\":false}],\"liveVerbalTrick\":\"<p><span style=\\\"color: rgba(0, 0, 0, 0.85); background-color: rgb(242, 242, 242); font-size: 14px;\\\">dfsdf；454？？？；@#¥%……&amp;*（（）&amp;……%¥#111</span></p><p><img src=\\\"https://cdn-test.daddylab.com/Upload/supplier/item/image-1670915516869.jpg\\\" alt=\\\"image-1670915516869.jpg\\\" data-href=\\\"\\\" style=\\\"\\\"/></p>\",\"modules\":[]}";
        final JsonNode jsonNode = JsonUtil.parse(input);
        final JsonNode jsonNode1 = JsonUtil.mapRecursive(jsonNode, v -> {
            if (v.isTextual()) {
                final String textNode = v.asText();
                return JsonNodeFactory.instance.textNode(htmlFilter.filter(textNode));
            }
            return v;
        });
        System.out.println(jsonNode1.toPrettyString());

    }


}
