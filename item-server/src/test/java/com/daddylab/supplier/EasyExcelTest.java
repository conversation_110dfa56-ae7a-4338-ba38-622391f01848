package com.daddylab.supplier;

import com.alibaba.excel.EasyExcel;
import com.daddylab.supplier.item.application.saleItem.dto.NewGoodsSheet;
import com.google.common.collect.ImmutableList;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/6/3
 */
public class EasyExcelTest {

    @Test
    public void dynamicHeadWrite() {
        String fileName = "/Users/<USER>/Desktop/" + System.currentTimeMillis() + ".xlsx";
        EasyExcel.write(fileName)
                // 这里放入动态头
                .head(head()).sheet("模板")
                // 当然这里数据也可以用 List<List<String>> 去传入
                .doWrite(data());
    }

    private List<NewGoodsSheet> data() {
        final NewGoodsSheet newGoodsSheet = new NewGoodsSheet();
        newGoodsSheet.setSkuCode("skuCode");
        newGoodsSheet.setName("name");
        newGoodsSheet.setSpecs("specs");
        newGoodsSheet.setCategory("category");
        newGoodsSheet.setBrand("brand");
        newGoodsSheet.setShelfTime("shelfTime");
        newGoodsSheet.setStandardName("standardName");
        newGoodsSheet.setActivePeriod("activePeriod");
        newGoodsSheet.setNoReason("noReason");
        newGoodsSheet.setLinePrice("linePrice");
        newGoodsSheet.setDailyPrice("dailyPrice");
        newGoodsSheet.setActivePrice("ActivePrice");
        newGoodsSheet.setActiveContent("ActiveContent");
//        newGoodsSheet.setChannelLowest("ChannelLowest");
        newGoodsSheet.setLiveActive("LiveActive");
//        newGoodsSheet.setIsReduce("IsReduce");
        newGoodsSheet.setShipmentType("ShipmentType");
        newGoodsSheet.setShipmentArea("ShipmentArea");
        newGoodsSheet.setShipmentAging("ShipmentAging");
        newGoodsSheet.setLogistics("Logistics");
        newGoodsSheet.setExpressTemplate("ExpressTemplate");
        newGoodsSheet.setRemark("Remark");
        newGoodsSheet.setStatus("Status");
//        newGoodsSheet.setIsCoupon("IsCoupon");
        newGoodsSheet.setBuyer("Buyer");
        newGoodsSheet.setPrincipal("Principal");
        newGoodsSheet.setQcs("Qcs");

        return ImmutableList.<NewGoodsSheet>builder().add(newGoodsSheet).build();
    }

    private List<List<String>> head() {
        List<List<String>> list = new ArrayList<List<String>>();
        List<String> head0 = new ArrayList<String>();
        head0.add("字符串" + System.currentTimeMillis());
        List<String> head1 = new ArrayList<String>();
        head1.add("数字" + System.currentTimeMillis());
        List<String> head2 = new ArrayList<String>();
        head2.add("日期" + System.currentTimeMillis());
        list.add(head0);
        list.add(head1);
        list.add(head2);
        return list;
    }


}
