package com.daddylab.supplier;

import com.daddylab.supplier.item.infrastructure.third.config.PddConfig;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.pdd.pop.sdk.http.PopHttpClient;
import com.pdd.pop.sdk.http.api.pop.request.PddGoodsDetailGetRequest;
import com.pdd.pop.sdk.http.api.pop.request.PddGoodsListGetRequest;
import com.pdd.pop.sdk.http.api.pop.request.PddGoodsQuantityUpdateRequest;
import com.pdd.pop.sdk.http.api.pop.response.PddGoodsDetailGetResponse;
import com.pdd.pop.sdk.http.api.pop.response.PddGoodsListGetResponse;
import com.pdd.pop.sdk.http.api.pop.response.PddGoodsQuantityUpdateResponse;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2024/4/17
 */
public class PddTests {

    public static final String MEIZHUANG_TOKEN = "c870954b2df741d8ac71c227f9a99b889fea6fe5";
    public static final String JIAJU_TOKEN = "bb49990cd2794b46b1b82e86ddd5e89ea8bbf1b7";

    @Test
    public void itemListQuery() throws Exception {
        final PddConfig pddConfig = new PddConfig();
        final PopHttpClient httpClient = pddConfig.getHttpClient();

        final PddGoodsListGetRequest request = new PddGoodsListGetRequest();
        request.setPage(1);
        request.setPageSize(10);
        final PddGoodsListGetResponse pddGoodsListGetResponse = httpClient.syncInvoke(request,
                JIAJU_TOKEN);
        System.out.println(pddGoodsListGetResponse);
        System.out.println(pddGoodsListGetResponse.getGoodsListGetResponse());
        System.out.println(JsonUtil.toJson(pddGoodsListGetResponse));


    }

    @Test
    public void itemDetailQuery() throws Exception {
        final PddConfig pddConfig = new PddConfig();
        final PopHttpClient httpClient = pddConfig.getHttpClient();


        final PddGoodsDetailGetRequest request = new PddGoodsDetailGetRequest();
        request.setGoodsId(582699176346L);

        final PddGoodsDetailGetResponse response = httpClient.syncInvoke(request,
                MEIZHUANG_TOKEN);
        System.out.println(JsonUtil.toJson(response));


    }

    @Test
    public void stockUpdate() throws Exception {
        final PddConfig pddConfig = new PddConfig();
        final PopHttpClient httpClient = pddConfig.getHttpClient();


        final PddGoodsQuantityUpdateRequest pddGoodsQuantityUpdateRequest = new PddGoodsQuantityUpdateRequest();
        pddGoodsQuantityUpdateRequest.setGoodsId(582699176346L);
        pddGoodsQuantityUpdateRequest.setQuantity(1L);
        pddGoodsQuantityUpdateRequest.setSkuId(1552981335465L);
        pddGoodsQuantityUpdateRequest.setUpdateType(2);

        final PddGoodsQuantityUpdateResponse response = httpClient.syncInvoke(
                pddGoodsQuantityUpdateRequest,
                MEIZHUANG_TOKEN);
        System.out.println(JsonUtil.toJson(response));


    }
}
