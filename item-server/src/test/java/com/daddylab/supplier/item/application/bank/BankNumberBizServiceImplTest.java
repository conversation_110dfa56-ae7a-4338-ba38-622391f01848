package com.daddylab.supplier.item.application.bank;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 2023/11/21
 */
@SpringBootTest
class BankNumberBizServiceImplTest {
    @Autowired
    private BankNumberBizService bankNumberBizService;

    @Test
    public void sync() {
        final BankNumberSyncCmd cmd = new BankNumberSyncCmd();
        cmd.setSkipUpdate(true);
        bankNumberBizService.sync(cmd);
    }
}
