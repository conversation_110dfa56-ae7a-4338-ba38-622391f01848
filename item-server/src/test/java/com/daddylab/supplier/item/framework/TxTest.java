package com.daddylab.supplier.item.framework;

import com.daddylab.supplier.framework.TestConfigForMail;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/8 5:07 下午
 * @description
 */
@SpringBootTest
@ActiveProfiles("test")
@Import(TestConfigForMail.class)
public class TxTest {


}
