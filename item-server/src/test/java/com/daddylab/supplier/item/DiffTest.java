package com.daddylab.supplier.item;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.daddylab.supplier.item.application.afterSalesForwarding.types.AffectsSalesVouchers;
import com.daddylab.supplier.item.application.afterSalesForwarding.types.AfterSalesForwardingRegisterFormItemVO;
import com.daddylab.supplier.item.application.payment.PaymentOrderBizServiceImpl;
import com.daddylab.supplier.item.application.saleItem.dto.NewGoodsCmd;
import com.daddylab.supplier.item.common.enums.EnableStatusEnum;
import com.daddylab.supplier.item.domain.brand.entity.BrandEntity;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.shop.dto.CreateOrUpdateShopCmd;
import com.daddylab.supplier.item.domain.shop.entity.ShopEntity;
import com.daddylab.supplier.item.domain.shop.enums.ShopStatus;
import com.daddylab.supplier.item.domain.shop.trans.ShopTransMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PaymentApplyOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrderDetail;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil.ObjListDiff;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.javers.core.Changes;
import org.javers.core.Javers;
import org.javers.core.JaversBuilder;
import org.javers.core.diff.Change;
import org.javers.core.diff.Diff;
import org.javers.core.diff.ListCompareAlgorithm;
import org.javers.core.diff.changetype.PropertyChange;
import org.javers.core.diff.changetype.ValueChange;
import org.javers.core.diff.changetype.map.EntryAdded;
import org.javers.core.diff.changetype.map.EntryRemoved;
import org.javers.core.diff.changetype.map.EntryValueChange;
import org.javers.core.diff.changetype.map.MapChange;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.*;

@SpringBootTest
@Slf4j
public class DiffTest {

    public static void main(String[] args) {
        final ArrayList<PaymentApplyOrderDetail> details1 = new ArrayList<>();
        final ArrayList<PaymentApplyOrderDetail> details2 = new ArrayList<>();

        final PaymentApplyOrderDetail d1 = new PaymentApplyOrderDetail();
        d1.setId(1L);
        d1.setPaymentApplyOrderId(1000L);
        d1.setPaymentApplyOrderNo("CKFK1000");
        d1.setDetailSource(0);
        d1.setApplyRelatedNo("CK001");
        d1.setRightAmount(new BigDecimal("100"));
        d1.setApplyAmount(new BigDecimal("100"));
        d1.setApplyAmountRemark("oo");
        d1.setRelateDetailId("1024");

        final PaymentApplyOrderDetail d2 = new PaymentApplyOrderDetail();
        d2.setId(2L);
        d2.setPaymentApplyOrderId(1000L);
        d2.setPaymentApplyOrderNo("CKFK1001");
        d2.setDetailSource(0);
        d2.setApplyRelatedNo("CK002");
        d2.setRightAmount(new BigDecimal("66"));
        d2.setApplyAmount(new BigDecimal("66"));
        d2.setApplyAmountRemark("pp");
        d2.setRelateDetailId("1025");

        final PaymentApplyOrderDetail d2g = new PaymentApplyOrderDetail();
        d2g.setId(2L);
        d2g.setPaymentApplyOrderId(1000L);
        d2g.setPaymentApplyOrderNo("CKFK1001");
        d2g.setDetailSource(0);
        d2g.setApplyRelatedNo("CK002");
        d2g.setRightAmount(new BigDecimal("66"));
        d2g.setApplyAmount(new BigDecimal("67"));
        d2g.setApplyAmountRemark("ppt");
        d2g.setRelateDetailId("1025");

        details1.add(d1);
        details1.add(d2);

        details2.add(d2g);

        final Javers javers =
                JaversBuilder.javers()
                             .withPrettyPrint(true)
                             .withListCompareAlgorithm(ListCompareAlgorithm.AS_SET)
                             .build();
        final Diff diff = javers.compareCollections(details1, details2, PaymentApplyOrderDetail.class);
        final String s = PaymentOrderBizServiceImpl.detailDiffLog(diff);
        System.out.println(diff);
        System.out.println(s);
    }

    @Test
    @DisplayName("比较算法测试")
    public void diff() {
        final BrandEntity brand1 = new BrandEntity();
        brand1.setId(1L);
        brand1.setSn("123123");
        brand1.setName("测试品牌");
        brand1.setLogo("https://logo.com");
        brand1.setStatus(EnableStatusEnum.ON);

        final BrandEntity brand2 = new BrandEntity();
        brand2.setId(1L);
        brand2.setSn("123123222");
        brand2.setName("测试品666");
        brand2.setLogo("https://logo.com");
        brand2.setStatus(EnableStatusEnum.OFF);

        final Diff diff = DiffUtil.diff(brand1, brand2);
        //        System.out.println(diff);
        //        log.info("-----------");
        //        log.info("diff = {}", diff);
        for (Change change : diff.getChanges()) {
            ((ValueChange) change).getPropertyName();
            System.out.println(
                    change.getCommitMetadata()
                            + ":"
                            + change.getAffectedGlobalId()
                            + ":"
                            + change.getAffectedObject()
                            + change.getAffectedLocalId());
        }

        diff.groupByObject()
            .forEach(
                    it -> {
                        final List<PropertyChange> propertyChanges = it.getPropertyChanges();
                        for (PropertyChange propertyChange : propertyChanges) {
                            ValueChange change = (ValueChange) propertyChange;
                            StringBuilder stringBuilder = new StringBuilder();
                            stringBuilder
                                    .append(change.getPropertyName())
                                    .append("，")
                                    .append(change.getLeft())
                                    .append("->")
                                    .append(change.getRight());
                            System.out.println(stringBuilder);
                        }
                        //            System.out.println(propertyChanges.toString());
                        //            System.out.println(it.toString());
                    });

        //        log.info("----------");
        //        log.info("diff = {}", JsonUtil.objToStr(diff));

    }

    @Test
    @DisplayName("比较算法测试2")
    public void diff1() {
        final ShopEntity shop = new ShopEntity();
        shop.setId(1L);
        shop.setSn("1sn");
        shop.setName("1Name");
        shop.setAccount("1Account");
        shop.setLink("1Link");
        shop.setLogo("2Logo");
        shop.setPlatform(Platform.TAOBAO);
        shop.setStatus(ShopStatus.OPEN);
        shop.setPrincipals(Lists.newArrayList(1L, 2L));

        final CreateOrUpdateShopCmd cmd = new CreateOrUpdateShopCmd();
        cmd.setId(shop.getId());
        cmd.setName("创建店铺测试");
        cmd.setLink("https://www.link.com");
        cmd.setPrincipals(Collections.singletonList(1L));
        cmd.setPlatform(Platform.TAOBAO);
        cmd.setAccount("创建店铺测试");
        cmd.setLogo("https://test.log");
        cmd.setStatus(ShopStatus.OPEN);
        final ShopEntity shop1 = ShopTransMapper.INSTANCE.toShopEntity(cmd);

        final Diff diff = DiffUtil.diff(shop, shop1);
        log.info("diff = {}", diff);
        final String json = JsonUtil.objToStr(diff);
        log.info("diff = {}", json);
        final Diff parsedDiff = JsonUtil.parse(json, Diff.class);
        log.info("parsedDiff = {}", parsedDiff);
    }

    @Test
    @DisplayName("list比较")
    public void testDiff3() {
        String s1 =
                "[\n"
                        + "{\n"
                        + "    \"id\": 31,\n"
                        + "    \"createdAt\": **********,\n"
                        + "    \"updatedAt\": **********,\n"
                        + "    \"createdUid\": 7563904,\n"
                        + "    \"updatedUid\": 0,\n"
                        + "    \"isDel\": 0,\n"
                        + "    \"deletedAt\": null,\n"
                        + "    \"purchaseOrderId\": 31,\n"
                        + "    \"itemSkuCode\": \"10020006\",\n"
                        + "    \"itemId\": 137116,\n"
                        + "    \"specifications\": \"颜色:1,规格:333\",\n"
                        + "    \"barCode\": \"100200\",\n"
                        + "    \"unit\": \"双\",\n"
                        + "    \"purchaseQuantity\": 55,\n"
                        + "    \"taxPrice\": 3.000000,\n"
                        + "    \"taxRate\": 0.130000,\n"
                        + "    \"taxQuota\": 19.500000,\n"
                        + "    \"totalPriceTax\": 150.000000,\n"
                        + "    \"afterTaxPrice\": 2.610000,\n"
                        + "    \"afterTaxAmount\": 130.500000,\n"
                        + "    \"isGift\": 0,\n"
                        + "    \"warehouseNo\": \"CK000290\",\n"
                        + "    \"remark\": \"\"\n"
                        + "},\n"
                        + "{\n"
                        + "    \"id\": 34,\n"
                        + "    \"createdAt\": **********,\n"
                        + "    \"updatedAt\": **********,\n"
                        + "    \"createdUid\": 7563904,\n"
                        + "    \"updatedUid\": 0,\n"
                        + "    \"isDel\": 0,\n"
                        + "    \"deletedAt\": null,\n"
                        + "    \"purchaseOrderId\": 31,\n"
                        + "    \"itemSkuCode\": \"10019803\",\n"
                        + "    \"itemId\": 137110,\n"
                        + "    \"specifications\": \"颜色:蓝色,规格:l\",\n"
                        + "    \"barCode\": \"100198\",\n"
                        + "    \"unit\": \"时\",\n"
                        + "    \"purchaseQuantity\": 2,\n"
                        + "    \"taxPrice\": 3.000000,\n"
                        + "    \"taxRate\": 0.130000,\n"
                        + "    \"taxQuota\": 0.780000,\n"
                        + "    \"totalPriceTax\": 6.000000,\n"
                        + "    \"afterTaxPrice\": 2.610000,\n"
                        + "    \"afterTaxAmount\": 5.220000,\n"
                        + "    \"isGift\": 0,\n"
                        + "    \"warehouseNo\": \"CK000292\",\n"
                        + "    \"remark\": \"\"\n"
                        + "}]";
        List<PurchaseOrderDetail> oldList = JSONObject.parseArray(s1, PurchaseOrderDetail.class);

        String s2 =
                "[\n"
                        + "{\n"
                        + "    \"id\": 34,\n"
                        + "    \"createdAt\": null,\n"
                        + "    \"updatedAt\": **********,\n"
                        + "    \"createdUid\": null,\n"
                        + "    \"updatedUid\": 0,\n"
                        + "    \"isDel\": null,\n"
                        + "    \"deletedAt\": null,\n"
                        + "    \"purchaseOrderId\": 31,\n"
                        + "    \"itemSkuCode\": \"10019803\",\n"
                        + "    \"itemId\": 137110,\n"
                        + "    \"specifications\": \"颜色:蓝色,规格:l\",\n"
                        + "    \"barCode\": \"100198\",\n"
                        + "    \"unit\": \"时\",\n"
                        + "    \"purchaseQuantity\": 2,\n"
                        + "    \"taxPrice\": 3,\n"
                        + "    \"taxRate\": 0.13,\n"
                        + "    \"taxQuota\": 0.780000,\n"
                        + "    \"totalPriceTax\": 6.000000,\n"
                        + "    \"afterTaxPrice\": 2.610000,\n"
                        + "    \"afterTaxAmount\": 5.220000,\n"
                        + "    \"isGift\": 0,\n"
                        + "    \"warehouseNo\": \"CK000292\",\n"
                        + "    \"remark\": \"\"\n"
                        + "},\n"
                        + "{\n"
                        + "    \"id\": 31,\n"
                        + "    \"createdAt\": null,\n"
                        + "    \"updatedAt\": **********,\n"
                        + "    \"createdUid\": null,\n"
                        + "    \"updatedUid\": 0,\n"
                        + "    \"isDel\": null,\n"
                        + "    \"deletedAt\": null,\n"
                        + "    \"purchaseOrderId\": 31,\n"
                        + "    \"itemSkuCode\": \"10020006\",\n"
                        + "    \"itemId\": 137116,\n"
                        + "    \"specifications\": \"颜色:1,规格:333\",\n"
                        + "    \"barCode\": \"100200\",\n"
                        + "    \"unit\": \"厘米\",\n"
                        + "    \"purchaseQuantity\": 50,\n"
                        + "    \"taxPrice\": 3,\n"
                        + "    \"taxRate\": 0.13,\n"
                        + "    \"taxQuota\": 19.500000,\n"
                        + "    \"totalPriceTax\": 150.000000,\n"
                        + "    \"afterTaxPrice\": 2.610000,\n"
                        + "    \"afterTaxAmount\": 130.500000,\n"
                        + "    \"isGift\": 0,\n"
                        + "    \"warehouseNo\": \"CK000290\",\n"
                        + "    \"remark\": \"\"\n"
                        + "}]";
        List<PurchaseOrderDetail> newList = JSONObject.parseArray(s2, PurchaseOrderDetail.class);

        DiffUtil util = DiffUtil.getInstance();
        DiffUtil.ObjListDiff objListDiff =
                util.diffObjList(oldList, newList, PurchaseOrderDetail.class);
        System.out.println(JsonUtil.toJson(objListDiff));

        //        Javers build = JaversBuilder.javers().build();
        ////        Javers build = JaversBuilder.javers()
        ////                .withListCompareAlgorithm(ListCompareAlgorithm.AS_SET)
        ////                .withMappingStyle(MappingStyle.BEAN) //使用Getter方法获取属性
        ////                .build();
        //
        //        Diff diff1 = build.compareCollections(oldList, newList, BrandEntity.class);
        //        System.out.println("######");
        //        System.out.println(diff1);
        //        System.out.println("######");
        //
        //        HashSet<Object> addId = new HashSet<>();
        //        HashSet<Object> removeId = new HashSet<>();
        //        Map<Object, List<ChangePropertyObj>> changeMap = new HashMap<>();
        //        diff1.groupByObject().forEach((it) -> {
        //            it.getNewObjects().forEach((c) -> {
        //                NewObject newObject = c;
        //                addId.add(newObject.getAffectedLocalId());
        //            });
        //            it.getObjectsRemoved().forEach((c) -> {
        //                ObjectRemoved objectRemoved = c;
        //                removeId.add(objectRemoved.getAffectedLocalId());
        //            });
        //            it.getPropertyChanges().forEach((c) -> {
        //                boolean pa = c instanceof ValueChange &&
        // (!addId.contains(c.getAffectedLocalId())) &&
        //                        (!removeId.contains(c.getAffectedLocalId()));
        //                if (pa) {
        //                    Object affectedLocalId = c.getAffectedLocalId();
        //                    List<ChangePropertyObj> orDefault =
        // changeMap.getOrDefault(affectedLocalId, new LinkedList<>());
        //                    ChangePropertyObj property = new
        // ChangePropertyObj(c.getPropertyName(), ((ValueChange) c).getLeft(), ((ValueChange)
        // c).getRight());
        //                    orDefault.add(property);
        //                    changeMap.put(affectedLocalId,orDefault);
        //                }
        //            });
        //        });
        //        System.out.println("add:" + JsonUtil.toJson(addId));
        //        System.out.println("remove:" + JsonUtil.toJson(removeId));
        //        System.out.println(JsonUtil.toJson(changeMap));

    }

    @Test
    @DisplayName("map比较")
    public void testMap() {
        Map<String, Integer> map1 = new HashMap<>();
        map1.put("c1", 1);
        map1.put("c2", 2);

        Map<String, Integer> map2 = new HashMap<>();
        map2.put("c2", 22);
        map2.put("c3", 3);

        final Diff diff = DiffUtil.diff(map1, map2, false);
        final Changes changes = diff.getChanges();
        for (Change change : changes) {
            MapChange mapChange = (MapChange) change;
            final List<EntryAdded> entryAddedChanges = mapChange.getEntryAddedChanges();
            System.out.println(JsonUtil.toJson(entryAddedChanges));
            final List<EntryValueChange> entryValueChanges = mapChange.getEntryValueChanges();
            System.out.println(JsonUtil.toJson(entryValueChanges));
            final List<EntryRemoved> entryRemovedChanges = mapChange.getEntryRemovedChanges();
            System.out.println(JsonUtil.toJson(entryRemovedChanges));
        }
        //        System.out.println(DiffUtil.toJson(diff));

    }

    @Test
    public void testDiffObjList() {
        final NewGoodsCmd newGoodsCmd = new NewGoodsCmd();
        newGoodsCmd.setId(1L);
        newGoodsCmd.setActivePeriodStart(0L);
        newGoodsCmd.setActivePeriodEnd(0L);
        newGoodsCmd.setIsLongTerm(false);

        final NewGoodsCmd newGoodsCmd1 = new NewGoodsCmd();
        newGoodsCmd1.setId(1L);
        newGoodsCmd1.setActivePeriodStart(null);
        newGoodsCmd1.setActivePeriodEnd(2L);
        newGoodsCmd1.setIsLongTerm(false);

        final ObjListDiff objListDiff =
                DiffUtil.getInstance()
                        .diffObjList(
                                Collections.singletonList(newGoodsCmd),
                                Collections.singletonList(newGoodsCmd1),
                                NewGoodsCmd.class,
                                true);
        log.info("{}", objListDiff);
    }

    @Test
    public void testAfterSalesForwardingRegisterFormItemVODiff() {
        final AfterSalesForwardingRegisterFormItemVO obj1 = new AfterSalesForwardingRegisterFormItemVO();
        obj1.setItemName("商品1");
        obj1.setAffectsSalesVouchers(AffectsSalesVouchers.of(ListUtil.of(
                        AffectsSalesVouchers.Voucher.of("123", "http://123")),
                Collections.emptyList()));

        final AfterSalesForwardingRegisterFormItemVO obj2 = new AfterSalesForwardingRegisterFormItemVO();
        obj2.setItemName("商品2");
        obj2.setAffectsSalesVouchers(AffectsSalesVouchers.of(ListUtil.of(
                        AffectsSalesVouchers.Voucher.of("123", "http://123"),
                        AffectsSalesVouchers.Voucher.of("234", "http://234")),
                Collections.emptyList()));

        final Diff diff = DiffUtil.diff(obj1, obj2);
        final String diffLog = DiffUtil.getVerboseDiffLog(diff, AfterSalesForwardingRegisterFormItemVO.class, "", "");
        System.out.println(diffLog);
    }
}
