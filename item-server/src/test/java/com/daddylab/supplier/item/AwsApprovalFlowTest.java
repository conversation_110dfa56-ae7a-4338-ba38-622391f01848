package com.daddylab.supplier.item;

import cn.hutool.core.date.DateUtil;
import cn.hutool.jwt.JWTPayload;
import cn.hutool.jwt.JWTUtil;

import com.actionsoft.sdk.service.model.AbstTaskInstanceModel;
import com.actionsoft.sdk.service.model.HistoryTaskInstance;
import com.actionsoft.sdk.service.model.TaskInstance;
import com.actionsoft.sdk.service.model.TaskQueryModel;
import com.alibaba.fastjson.JSON;
import com.daddylab.supplier.item.application.aws.service.impl.AwsProcessClientImpl;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.google.common.collect.ImmutableMap;

import lombok.NonNull;

import org.junit.Test;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class AwsApprovalFlowTest {

    public static final String accessKey = "aws";

    public static final String secret = "R9LZVhHQWDB3N3";

    public static final String apiServer = "http://172.16.12.189:8088/portal/api";
//    public static final String URL_BASE = "https://p.daddylab.com/portal/r/or?cmd=CLIENT_BPM_FORM_MAIN_PAGE_OPEN&openState=1&oauthName=erpOauth&taskInstId={}&loginName={}&processInstId={}";

//        public static final String apiServer = "https://a.dlab.cn/portal/api";
    public static final String URL_BASE = "http://p.dlab.cn/portal/r/or?cmd=CLIENT_BPM_FORM_MAIN_PAGE_OPEN&openState=1&oauthName=erpOauth&taskInstId={}&loginName={}&processInstId={}";
    public static final String ITEM_LAUNCH_PROCESS_DEF_ID = "obj_99e9394f4b9b408bad6cca04d0182ba7";

    @NonNull
    private AwsProcessClientImpl getAwsProcessClient() {
        return new AwsProcessClientImpl(
                apiServer, accessKey, secret
        );
    }

    @Test
    public void test66() {
        final AwsProcessClientImpl awsProcessClient = getAwsProcessClient();
        final TaskQueryModel model = new TaskQueryModel();
        model.setActiveTask(true);
        model.setProcessDefId("obj_a36c804abf01493cb31e39869dfe95d0");
        final List<TaskInstance> taskInstances = awsProcessClient.taskQuery(model);
        System.out.println(JSON.toJSONString(taskInstances));
    }

    @Test
    public void processCreateAndBindDataThenStart() {
        final AwsProcessClientImpl awsProcessClient = getAwsProcessClient();
        final String processInstId = awsProcessClient.processCreate("obj_a36c804abf01493cb31e39869dfe95d0",
                "采购单审核流程（测试2）", "chengzu.wu");
        System.out.println(processInstId);

        awsProcessClient.boCreate("BO_DLAB_ERP_CGD_NEW", processInstId, ImmutableMap.of(
                "ENV", "test",
                "AMOUNT", 1000001
        ), "chengzu.wu");

        awsProcessClient.processStart(processInstId);
        final List<TaskInstance> taskInstances = getAwsProcessClient().taskQuery(
                processInstId);
        final TaskInstance taskInstance = taskInstances.get(0);
        awsProcessClient.taskCommentCommit(taskInstance.getId(), "chengzu.wu", "提交", "测试", true);
        awsProcessClient.taskComplete(taskInstance.getId(), "chengzu.wu");
        final String url = url(processInstId, "chengzu.wu");
        System.out.println(url);
    }

    @Test
    public void taskComplete() {
        final AwsProcessClientImpl awsProcessClient = getAwsProcessClient();
        String processInstId = "063ea1b9-7804-41d2-9ec1-cc9c4406e4bb";
        final List<TaskInstance> taskInstances = getAwsProcessClient().taskQuery(
                processInstId);
        System.out.println(JSON.toJSONString(taskInstances));
        for (TaskInstance taskInstance : taskInstances) {
            awsProcessClient.taskCommentCommit(taskInstance.getId(), taskInstance.getTarget(), "提交", "超过24小时自动通过", true);
            awsProcessClient.taskComplete(taskInstance.getId(), taskInstance.getTarget());
        }
    }

    @Test
    public void test666() throws IOException {
        String processInstId = "455230a2-5943-4697-a6c6-024b70a2b643";
        final AwsProcessClientImpl awsProcessClient = getAwsProcessClient();
        final List<TaskInstance> taskInstances = awsProcessClient.taskQuery(processInstId);
        System.out.println(JsonUtil.toJson(taskInstances));

        final TaskInstance taskInstance = taskInstances.get(0);
        final String taskInstId = taskInstance.getId();
        final String s = awsProcessClient.taskParticipantsGet("chengzu.wu", processInstId, taskInstId, taskInstance.getActivityDefId());
        System.out.println(s);

//        final String[][] querys = {
//                new String[]{"bindId = ", processInstId}
//        };
//        final List<Map<String, Object>> maps = awsProcessClient.boQuery(
//                "BO_DLAB_ERP_CGD_NEW", querys, "", "", 0, 10);
//        System.out.println(JsonUtil.toJson(maps));
    }

    @Test
    public void url() throws IOException {
//        String processInstId = "fa028fed-8a07-4f1b-8954-a656075bb9f6";
        String processInstId = "3c3db34f-94f6-413d-81fa-60ae9832df64";
        final String url = url(processInstId, "chengzu.wu");
        System.out.println(url);
        Runtime.getRuntime().exec("open " + url);
    }

    public String generateSign(String loginName) {
        Map<String, Object> payload = new HashMap<>();
        //签发时间
        payload.put(JWTPayload.ISSUED_AT, new Date());
        //过期时间
        payload.put(JWTPayload.EXPIRES_AT, DateUtil.offsetMinute(new Date(), 1));
        //载荷
        payload.put("loginName", loginName);
        String key = "erp";
        String sign = JWTUtil.createToken(payload, key.getBytes());
        System.out.println(sign);
        return sign;
    }

    @Test
    public void bindData() {
        final AwsProcessClientImpl awsProcessClient = getAwsProcessClient();
        String processInstId = "d3a4a374-b76d-46bb-8eec-2397ef90c539";
        awsProcessClient.boFieldUpdate("BO_DLAB_ERP_GOODS_LIB", processInstId, "LEGAL",
                "jinbiao.sheng");
    }

    @Test
    public void boQuery() {
        final AwsProcessClientImpl awsProcessClient = getAwsProcessClient();
        final String[] processInstIds = {
                "a3c1b3fa-7973-45d5-a1e8-5ceb1d883f82",
                "63473c7e-e80d-4cca-8789-cddc58ed2a62",
                "0a1770b2-cd5f-4ae9-8599-256efae8385a",
                "eeea8581-c122-4bc1-af6b-ee277452a23f",
                "e41ff1d0-ad76-4a5c-9805-6baf90c81fa6",
                "a9eebba5-ec64-4916-9962-0dba6e510f47",
                "fa028fed-8a07-4f1b-8954-a656075bb9f6",
                "1cc1b845-23a4-4800-a796-5c56775ae424",
                "069c6694-152b-41b9-b5b5-695432aa2306",
                "e3a29535-92a8-418f-8b22-b338ec194ab1",
        };
        for (String processInstId : processInstIds) {
            final String[][] querys = {
                    new String[]{"bindId = ", processInstId}
            };
            final List<Map<String, Object>> maps = awsProcessClient.boQuery(
                    "BO_DLAB_ERP_GOODS_LIB", querys, "", "", 0, 10);
            System.out.println(maps);
        }
    }

    @Test
    public void ternimal() {
        String processInstId = "6adc2c8c-eb7d-46d5-9712-ac547b879463";
        getAwsProcessClient().processTerminate(processInstId, "chengzu.wu");
    }

    public String url(String processInstId, String loginName) {
        String url = URL_BASE;
        final List<TaskInstance> taskInstances = getAwsProcessClient().taskQuery(processInstId);
        System.out.println(JsonUtil.toJson(taskInstances));
        String taskId;
        if (taskInstances.isEmpty()) {
            final List<HistoryTaskInstance> historyTaskInstances = getAwsProcessClient().historyTaskQuery(processInstId);
            final HistoryTaskInstance historyTaskInstance = historyTaskInstances.get(historyTaskInstances.size() - 1);
            taskId = historyTaskInstance.getId();
            String jwtLoginName = generateSign(loginName);
            return StringUtil.format(url, taskId, jwtLoginName, processInstId);
        }
        final Optional<TaskInstance> taskInstance = taskInstances.stream().findFirst();
        taskId = taskInstance.map(AbstTaskInstanceModel::getId)
                .orElseThrow(() -> new IllegalStateException("当前无待办任务"));
        loginName =
                taskInstance
                        .map(TaskInstance::getTarget)
                        .filter(StringUtil::isNotBlank)
                        .orElse(loginName);
        String jwtLoginName = generateSign(loginName);
        return StringUtil.format(url, taskId, jwtLoginName, processInstId);
    }

    @Test
    public void test() {
//        System.out.println(
//                url("e491d8f3-e3f3-42d8-ad31-a284bab54bcb")
//        );
        final String processInstId = "dae204d0-30f3-40c6-9e2f-77d040fc66c7";
        final List<HistoryTaskInstance> historyTaskInstances = getAwsProcessClient().historyTaskQuery(
                processInstId);
        System.out.println(JsonUtil.toJson(historyTaskInstances));
//        final ProcessInstResponse processInstResponse = getAwsProcessClient().processInstGet(
//                processInstId);
//        System.out.println(JsonUtil.toJson(processInstResponse.getData()));

//        final TaskInstance taskInstance = taskInstances.get(0);
//        final String id = taskInstance.getId();
//        final boolean taskDelegate = getAwsProcessClient().taskDelegate(id, "chengzu.wu",
//                "chengzu.wu", "测试任务代理");
//        System.out.println("任务移交" + (taskDelegate ? "成功" : "失败"));
    }

    @Test
    public void taskQuery() {
//        final String processInstId = "a3c1b3fa-7973-45d5-a1e8-5ceb1d883f82";
        final String processInstId = "1cc1b845-23a4-4800-a796-5c56775ae424";
        final List<TaskInstance> taskInstances = getAwsProcessClient().taskQuery(
                processInstId);
        System.out.println(JsonUtil.toJson(taskInstances));
    }

    @Test
    public void hisTaskQuery() {
        final String processInstId = "1cc1b845-23a4-4800-a796-5c56775ae424";
        final List<HistoryTaskInstance> historyTaskInstances = getAwsProcessClient().historyTaskQuery(
                processInstId);
        System.out.println(JsonUtil.toJson(historyTaskInstances));
    }

    @Test
    public void processTerminate() {
        final Boolean result = getAwsProcessClient().processTerminate(
                "e491d8f3-e3f3-42d8-ad31-a284bab54bcb", "chengzu.wu");
        System.out.println(result);
    }

    @Test
    public void processDelegate() {
        final boolean taskDelegate = getAwsProcessClient().taskDelegate(
                "851bfa19-fd2e-422b-b5a0-6be3168939bf", "admin", "chengzu.wu",
                "测试");
        System.out.println(taskDelegate);
    }
}