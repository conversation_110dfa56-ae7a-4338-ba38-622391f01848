package com.daddylab.supplier.item.controller.drawer;

import com.daddylab.supplier.item.application.drawer.ItemDrawerImageMetaQueryTask;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 2022/11/15
 */
@SpringBootTest
class ItemDrawerImageMetaQueryTaskTest {
    @Resource
    ItemDrawerImageMetaQueryTask itemDrawerImageMetaQueryTask;

    @Test
    void doTask() {
        itemDrawerImageMetaQueryTask.doTask();
    }
}