package com.daddylab.supplier.item;

import cn.hutool.core.date.DatePattern;
import com.alibaba.cloud.nacos.NacosConfigAutoConfiguration;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.RefundAPI;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse.Order;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse.Order.AmountDetail;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse.Order.Detail;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse.Order.SwapOrder;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse.Order.SwapOrder.SwapOrderDetail;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.MybatisPlusConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrderAmountDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtRefundOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSwapOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtSwapOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundOrderAmountDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtRefundOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtSwapOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtSwapOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtGatewayImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.assembler.RefundOrderAssembler;
import com.daddylab.supplier.item.infrastructure.qimen.wdt.QimenApiFactory;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

/**
 * <AUTHOR>
 * @since 2022/4/19
 */
@Slf4j
@SpringBootTest(classes = {
        NacosConfigAutoConfiguration.class,
        DynamicDataSourceAutoConfiguration.class,
        DataSourceAutoConfiguration.class,
        MybatisPlusAutoConfiguration.class,
        MybatisPlusConfig.class,
        WdtGatewayImpl.class,
        WdtConfig.class,

        //Mapper类注入
        WdtRefundOrderMapper.class,
        WdtRefundOrderDetailMapper.class,
        WdtSwapOrderMapper.class,
        WdtSwapOrderDetailMapper.class,
        WdtRefundOrderAmountDetailMapper.class
})
@EnableConfigurationProperties(WdtConfig.class)
public class WdtRefundOrderSyncTest {

    @Autowired
    WdtGateway wdtGateway;

    @MockBean
    QimenApiFactory qimenApiFactory;

    @Autowired
    WdtRefundOrderMapper wdtRefundOrderMapper;
    @Autowired
    WdtRefundOrderDetailMapper wdtRefundOrderDetailMapper;
    @Autowired
    WdtSwapOrderMapper wdtSwapOrderMapper;
    @Autowired
    WdtSwapOrderDetailMapper wdtSwapOrderDetailMapper;
    @Autowired
    WdtRefundOrderAmountDetailMapper wdtRefundOrderAmountDetailMapper;


    @DisplayName("同步订单")
    @Test
    public void syncOrder() throws WdtErpException {
        final RefundAPI api = wdtGateway.getAPI(RefundAPI.class);
        final Pager pager = new Pager(100, 0, true);
        final LocalDateTime now = LocalDateTime.now();
        LocalDateTime endTime = now;
        LocalDateTime startTime = now.minusMonths(1);
        final DateTimeFormatter normDatetimeFormatter = DatePattern.NORM_DATETIME_FORMATTER;

        final RefundSearchParams params = new RefundSearchParams();
        params.setModifiedFrom(startTime.format(normDatetimeFormatter));
        params.setModifiedTo(endTime.format(normDatetimeFormatter));
        final RefundSearchResponse refundSearchResponse = api.search(params, pager);
        System.out.println(refundSearchResponse);
        if (!refundSearchResponse.getOrder().isEmpty()) {
            for (Order order : refundSearchResponse.getOrder()) {
                final WdtRefundOrder wdtRefundOrder = RefundOrderAssembler.INST
                        .toWdtRefundOrderPO(order);
                wdtRefundOrderMapper.insert(wdtRefundOrder);

                for (Detail detail : order.getDetailList()) {
                    final WdtRefundOrderDetail wdtRefundOrderDetail = RefundOrderAssembler.INST
                            .toWdtRefundOrderDetailPO(order.getRefundNo(), detail);
                    wdtRefundOrderDetailMapper.insert(wdtRefundOrderDetail);
                }

                for (AmountDetail amountDetail : order.getAmountDetailList()) {
                    final WdtRefundOrderAmountDetail wdtRefundOrderAmountDetail = RefundOrderAssembler.INST
                            .toWdtRefundOrderAmountDetailPO(order.getRefundNo(), amountDetail);
                    wdtRefundOrderAmountDetailMapper.insert(wdtRefundOrderAmountDetail);
                }

                if (order.getSwapOrder() != null) {
                    final SwapOrder swapOrder = order.getSwapOrder();
                    final WdtSwapOrder wdtSwapOrder = RefundOrderAssembler.INST
                            .toWdtSwapOrderPO(order.getRefundNo(), swapOrder);
                    wdtSwapOrderMapper.insert(wdtSwapOrder);

                    for (SwapOrderDetail swapOrderDetail : swapOrder
                            .getSwapOrderDetailList()) {
                        final WdtSwapOrderDetail wdtSwapOrderDetail = RefundOrderAssembler.INST
                                .toWdtSwapOrderDetailPO(order.getRefundNo(), swapOrder.getTid(),
                                        swapOrderDetail);
                        wdtSwapOrderDetailMapper.insert(wdtSwapOrderDetail);
                    }
                }
            }
        }
    }


}
