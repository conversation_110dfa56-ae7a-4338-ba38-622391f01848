package com.daddylab.supplier.item;

import com.daddylab.supplier.item.domain.departments.DepartmentGateway;
import com.daddylab.supplier.item.infrastructure.accessControl.AccessControlAPI;
import com.daddylab.supplier.item.infrastructure.accessControl.AccessControlFallbackFactory;
import com.daddylab.supplier.item.infrastructure.accessControl.Dept;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.department.DepartmentGatewayImpl;
import java.util.Collection;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2022/4/27
 */
public class DepartmentGatewayTest {

    @Test
    @DisplayName("测试递归查询全部子部门")
    public void allIds() {
        final AccessControlAPI accessControlAPI = new AccessControlFallbackFactory().create(null);
        final DepartmentGateway departmentGateway = new DepartmentGatewayImpl(accessControlAPI);
        final Collection<Dept> allDepartments = departmentGateway
                .getSubDepartments(departmentGateway.id("消费者事业部"), true);
        Assertions.assertNotNull(allDepartments, "递归查询全部子部门失败，返回结果为null");
        Assertions.assertFalse(allDepartments.isEmpty(), "递归查询全部子部门为空");
        allDepartments.forEach(dept -> {
            System.out.println(dept.getName() + " id:" + dept.getId());
        });
    }
}
