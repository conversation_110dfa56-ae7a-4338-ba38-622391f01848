package com.daddylab.supplier.item;

import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WechatMsg;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.QyWeixinService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.QyWeixinFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.SendQyWeixinMsgParam;
import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQProducer;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @Author: <PERSON>
 * @Date: 2022/6/3 09:38
 * @Description: 企业微信相关测试
 */
@SpringBootTest(properties = {
        "spring.profiles.active:test"
})
@RunWith(SpringRunner.class)
@Slf4j
public class QyWeixinTest {
    @Autowired
    private QyWeixinService qyWeixinService;
    @Autowired
    private QyWeixinFeignClient qyWeixinFeignClient;
    @Autowired
    RocketMQProducer rocketMQProducer;

    @Test
    public void test_sendMsg_01() {
        String token = qyWeixinService.getTokenWithCache();
        SendQyWeixinMsgParam param = new SendQyWeixinMsgParam();
        SendQyWeixinMsgParam.Text text = new SendQyWeixinMsgParam.Text();
        text.setContent("端午安康！");
        param.setAgentid(1000067);
        param.setMsgtype("text");
        param.setTouser("xiang.wan");

        qyWeixinFeignClient.sendMessage(token, param);
    }

    @SneakyThrows
    @Test
    public void test_sendMsg_02() {
        sendTextCard();

//        List<Long> l = new LinkedList<>();
//        for (int i = 0; i < 70; i++) {
//            l.add((long) i);
//        }
//        rocketMQProducer.asyncSend(l, MQTopic.WECHAT_DELAY_MSG,"msg_id"
//                , IdUtil.fastSimpleUUID(),3);
//        log.info("sendTime:{}", LocalDateTime.now());

//        MsgEvent msgEvent = MsgEvent.ofOperatorChange(ListUtil.of(7724818L),7724818L,ListUtil.of(306040L));
//        EventBusUtil.post(msgEvent,true);
//        System.in.read();

//        sendWeChatMsgJob.run();
//        System.in.read();
    }

    private void sendTextCard() {
        for (int i = 1; i < 2; i++) {
            String token = qyWeixinService.getTokenWithCache();

            String title = i + "「上新计划创建人」已创建「上新日期」的上新计划，请完善对应商品信息";
            String desc = "<div class=\"normal\">" + "共「9」款商品「23」个sku待维护" + "</div>";
            String link = "http://www.baidu.com" + "?itemId=";
            SendQyWeixinMsgParam.TextCard textCard = SendQyWeixinMsgParam.TextCard.of(title, desc, link);

            SendQyWeixinMsgParam param = new SendQyWeixinMsgParam();
            param.setTextCard(textCard);
            param.setAgentid(1000067);
            param.setMsgtype("textcard");
//            param.setTouser("ying.he");
            param.setTouser("shengwei.fu");

            qyWeixinFeignClient.sendMessage(token, param);
        }
    }

    @Test
    public void sendText() {
        String token = qyWeixinService.getTokenWithCache();

        SendQyWeixinMsgParam.Text text = new SendQyWeixinMsgParam.Text();
        String link = "http://www.baidu.com";
        String s = "<a href=" + link + ">你的快递已到，请携带工卡前往邮件中心领取" + "</a>";
        text.setContent(s);

        SendQyWeixinMsgParam param = new SendQyWeixinMsgParam();
        param.setText(text);
        param.setAgentid(1000067);
        param.setMsgtype("text");
//            param.setTouser("ying.he");
        param.setTouser("shengwei.fu");

        qyWeixinFeignClient.sendMessage(token, param);
    }

    @Resource
    MsgSender msgSender;

    @Test
    public void itemPlanSubmitTest() {
        WechatMsg wechatMsg = new WechatMsg();
        wechatMsg.setTitle("❤️❤️❤️亲爱的陛下~~~");
        wechatMsg.setContent("系统采购单已经生成了，请您查验");
        wechatMsg.setRecipient("shengwei.fu");
        wechatMsg.setLink("https://p.daddylab.com/erp/commodity-purchase/purchase-order/list");
        msgSender.send(wechatMsg);

        WechatMsg wechatMsg1 = new WechatMsg();
        wechatMsg1.setTitle("❤️❤️❤️亲爱的陛下~~~");
        wechatMsg1.setContent("系统采购单已经跑完了，请您查验");
        wechatMsg1.setRecipient("yuntun.cj");
        wechatMsg1.setLink("https://p.daddylab.com/erp/commodity-purchase/purchase-order/list");
        msgSender.send(wechatMsg1);
    }
}
