package com.daddylab.supplier.item.application.drawer;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @since 2024/3/13
 */
@SpringBootTest
class NewGoodsAutoCreateTaskTest {
    @Autowired
    private NewGoodsAutoCreateTask newGoodsAutoCreateTask;

    @Test
    void importExcel() throws IOException {
        newGoodsAutoCreateTask.importExcel(Files.newInputStream(Paths.get("/Users/<USER>/Desktop/小程序商品SKU匹配商品中心新品库的SKU.xlsx")));
    }
}