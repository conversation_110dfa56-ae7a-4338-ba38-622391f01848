package com.daddylab.supplier.item.application.recognitionTask;

import java.util.List;
import org.apdplat.word.WordSegmenter;
import org.apdplat.word.segmentation.Word;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2022/12/12
 */
public class WordSegTest {

    @Test
    public void seg() {
//        final Dictionary dictionary = DictionaryFactory.getDictionary();
//        dictionary.add("最");
//        dictionary.add("最高");
//        dictionary.add("最大");
        final List<Word> results = WordSegmenter.segWithStopWords("全国销量第一，国家级产业，最高最强最大,首个首选，最溜溜");
        System.out.println(results);
    }

}
