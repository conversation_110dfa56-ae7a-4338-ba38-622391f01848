package com.daddylab.supplier.item;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.brand.BrandBizService;
import com.daddylab.supplier.item.common.enums.EnableStatusEnum;
import com.daddylab.supplier.item.domain.brand.dto.*;
import com.daddylab.supplier.item.domain.brand.entity.BrandEntity;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest
@ActiveProfiles("test")
@Slf4j
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class BrandTest {

    @Autowired
    private BrandBizService brandBizService;

    private BrandDetail brand;

    @Test
    @Order(1)
    @DisplayName("创建品牌测试")
    public void createBrand() {
        final CreateOrUpdateBrandCmd cmd = new CreateOrUpdateBrandCmd();
        final String name = "单元测试" + RandomUtil.randomNumbers(10);
        cmd.setName(name);
        cmd.setLogo("https://test.log");
        cmd.setStatus(EnableStatusEnum.ON);
        cmd.setProviders(ImmutableList.of(3L, 4L));
        final SingleResponse<BrandDetail> createBrandResponse = brandBizService.createOrUpdateBrand(cmd);
        Assertions.assertTrue(createBrandResponse.isSuccess(), "创建品牌失败");
        this.brand = createBrandResponse.getData();
        log.info("response = {}", this.brand);
    }

    @Test
    @Order(2)
    @DisplayName("获取品牌详情测试")
    public void getBrandDetail() {
        final SingleResponse<BrandEntity> brandDetail = brandBizService.getBrand(brand.getId());
        log.info("response = {}", brandDetail);
    }

    @Test
    @Order(3)
    @DisplayName("更新品牌测试")
    public void updateBrand() {
        final CreateOrUpdateBrandCmd cmd = new CreateOrUpdateBrandCmd();
        cmd.setId(brand.getId());
        cmd.setLogo("https://test.log");
        cmd.setStatus(EnableStatusEnum.ON);
        cmd.setProviders(ImmutableList.of(4L));

        final Response createOrUpdateBrand = brandBizService.createOrUpdateBrand(cmd);

        log.info("response = {}", createOrUpdateBrand);
    }

    @Test
    @Order(4)
    @DisplayName("品牌下拉选测试")
    public void dropDown() {
        final BrandDropDownQuery query = new BrandDropDownQuery();
        query.setName("测试");
        query.setPageIndex(1);

        final MultiResponse<BrandDropDownItem> brandDropDownItemMultiResponse = brandBizService.dropDownList(query);
        log.info("response = {}", brandDropDownItemMultiResponse);
    }

    @Test
    @Order(5)
    @DisplayName("品牌列表查询测试")
    public void brandList() {

        final BrandQuery brandQuery = new BrandQuery();
        brandQuery.setName("单元测试");

        final MultiResponse<BrandListItem> brandListItemMultiResponse = brandBizService.queryBrandList(brandQuery);
        log.info("response = {}", brandListItemMultiResponse);
    }

    @Test
    @Order(6)
    @DisplayName("删除品牌测试")
    public void deleteBrand() {
        final Response response = brandBizService.deleteBrand(2112L);
        System.out.println(response);

//        log.info("response = {}", response);
//
//        final BrandQuery brandQuery = new BrandQuery();
//        brandQuery.setName("品牌测试");
//
//        final MultiResponse<BrandListItem> brandListItemMultiResponse = brandBizService.queryBrandList(brandQuery);
//        log.info("response = {}", brandListItemMultiResponse);
//
//        Assertions.assertFalse(brandListItemMultiResponse.getData().stream()
//                .anyMatch(i -> Objects.equals(brand.getId(), i.getId())), "删除品牌失败");

    }

}
