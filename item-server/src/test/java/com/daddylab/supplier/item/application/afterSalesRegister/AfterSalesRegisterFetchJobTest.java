package com.daddylab.supplier.item.application.afterSalesRegister;

import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.application.afterSaleLogistics.AfterSaleLogisticsBizService;
import com.daddylab.supplier.item.application.afterSaleLogistics.dto.AfterSaleLogisticsBatchWarnQuery;
import org.junit.jupiter.api.Test;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 2023/8/28
 */
@SpringBootTest
class AfterSalesRegisterFetchJobTest {
  @Autowired AfterSalesRegisterFetchJob afterSalesRegisterFetchJob;
  @Autowired
  AfterSaleLogisticsBizService afterSaleLogisticsBizService;

  //    @Test
  //    void fetch() {
  //        final YearMonth month = YearMonth.now().minusMonths(1);
  //        final AfterSalesRegisterFetchJob.Config config = new
  // AfterSalesRegisterFetchJob.Config(month);
  //        config.setSkipFetchBanniu(true);
  ////        config.setSkipFetchMall(true);
  ////        config.setSkipFilter(true);
  //
  //        afterSalesRegisterFetchJob.fetch(config);
  //    }

  @Test
  public void batchList() {
    final RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);
    RAtomicLong atomicLong = redissonClient.getAtomicLong("logistic_CK0001201_20250422");
    atomicLong.incrementAndGet();

    RAtomicLong atomicLong2 = redissonClient.getAtomicLong("logistic_CK0001201_20250422");
    Long currentValue = atomicLong2.get();

    AfterSaleLogisticsBatchWarnQuery query = new AfterSaleLogisticsBatchWarnQuery();
    query.setPageIndex(1);
    query.setPageSize(10);

    int fromIndex = Math.max(Math.max(0, query.getPageIndex() - 1) * query.getPageSize(), 0);
    int toIndex = Math.min(fromIndex + query.getPageSize(), 1645);
    //        return PageResponse.of(
    //                resList.subList(fromIndex, toIndex), resList.size(), query.getPageSize(),
    // query.getPageIndex());

    //        AfterSaleLogisticsBatchWarnQuery query = new AfterSaleLogisticsBatchWarnQuery();
    //        afterSaleLogisticsBizService.batchWarnList(query);
  }

  public static void main(String[] args) {
    AfterSaleLogisticsBatchWarnQuery query = new AfterSaleLogisticsBatchWarnQuery();
    query.setPageIndex(2);
    query.setPageSize(10);

    int fromIndex = Math.max(Math.max(0, query.getPageIndex() - 1) * query.getPageSize(), 0);
    int toIndex = Math.min(fromIndex + query.getPageSize(), 1645);

    System.out.println(11);
  }
}
