package com.daddylab.supplier.item;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.inOrder;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.func.Func;
import cn.hutool.core.util.SerializeUtil;
import com.daddylab.supplier.item.application.criticalPush.BiFunctionCallback;
import com.daddylab.supplier.item.application.criticalPush.CriticalPushManagerImpl;
import com.daddylab.supplier.item.application.criticalPush.PushState;
import com.daddylab.supplier.item.application.criticalPush.SourceType;
import com.daddylab.supplier.item.application.criticalPush.TargetType;
import com.daddylab.supplier.item.application.criticalPush.VarargsFunctionCallback;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataPush;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataPushLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl.DataPushLogServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl.DataPushServiceImpl;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import java.io.Serializable;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InOrder;
import org.mockito.Mockito;

/**
 * <AUTHOR>
 * @since 2022/4/16
 */
@Slf4j
public class CriticalPushManagerTest implements Serializable {

    private static final long serialVersionUID = -5789337594570442800L;

    @Test
    @DisplayName("成功推送")
    public void push() {
        final DataPushServiceImpl dataPushService = mock(DataPushServiceImpl.class,
                Mockito.withSettings().serializable());
        final DataPushLogServiceImpl dataPushLogService = mock(DataPushLogServiceImpl.class);
        final CriticalPushManagerImpl pushManager = new CriticalPushManagerImpl(
                dataPushService,
                dataPushLogService);
        final String arg1 = "kiki";
        final String arg2 = "qiaqia";

        //当内部调用保存推送记录的时候，进行参数检查
        when(dataPushService.save(any())).thenAnswer(invocation -> {
            final DataPush argument = invocation.getArgument(0, DataPush.class);
            log.info("SAVE dataPush:{}", argument);
            if (Objects.isNull(argument.getId())) {
                argument.setId(1L);
                assertEquals(PushState.INIT.getValue(), argument.getState(), "推送记录状态初始状态错误");
            } else {
                assertEquals(PushState.SUCCESS.getValue(), argument.getState(), "推送记录状态未变更为推送成功");
            }
            return true;
        });

        //当内部调用保存推送日志的时候，进行参数检查
        when(dataPushLogService.save(any())).thenAnswer(invocation -> {
            final DataPushLog argument = invocation.getArgument(0, DataPushLog.class);
            log.info("SAVE dataPushLog:{}", argument);
            argument.setId(1L);
            assertEquals(1L, argument.getPushId(), "推送日志记录未记录推送记录ID");
            assertEquals(JsonUtil.toJson(new Object[]{arg1, arg2}), argument.getReq(),
                    "推送日志记录未正确记录请求参数");
            assertEquals(JsonUtil.toJson(hello(arg1, arg2)), argument.getResp(), "推送日志记录未正确记录响应结果");
            return true;
        });

        //实际方法调用
        pushManager.push(SourceType.PURCHASE_STOCK_IN_ORDER, 1L, TargetType.WDT,
                (VarargsFunctionCallback<Object, Object>) delegate -> {
                    delegate.call((Func<Object, Object>) parameters -> hello((String) parameters[0],
                            (String) parameters[1]), 1, 2);
                });

        //校验内部数据保存顺序
        final InOrder inOrder = inOrder(dataPushService, dataPushLogService);
        inOrder.verify(dataPushService).save(any());
        inOrder.verify(dataPushLogService).save(any());
        inOrder.verify(dataPushService).save(any());

    }

    public String hello(String sb, String sb2) {
        return "hello, " + sb + " AND " + sb2;
    }

    @Test
    @DisplayName("测试Lambda表达式的序列化和反序列化")
    public void serializeAndDeserializeLambda() throws Exception {

        final String path = "/tmp/serialize.bin";
        if (FileUtil.exist(path)) {
            final byte[] bytes = FileUtil.readBytes(path);
            final BiFunctionCallback<String, String, String> deserialize = SerializeUtil
                    .deserialize(bytes);
            deserialize.call((executor, arg1, arg2) -> {
                log.info("before execute");
                final String result = executor.apply(arg1, arg2);
                log.info("after execute, result = {}", result);
            });
        } else {
            final byte[] serialize = SerializeUtil.serialize(
                    (BiFunctionCallback<String, String, String>) delegate -> {
                        delegate.call(this::hello, "kiki", "qiaqiaqia");
                    });
            if (serialize == null) {
                throw new RuntimeException("对象不可序列化");
            }
            FileUtil.writeBytes(serialize, path);
        }
    }

}
