package com.daddylab.supplier.item.application.payment;

import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.application.order.settlement.OrderSettlementBizService;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizService;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplateImpl;
import com.daddylab.supplier.item.infrastructure.oa.cgfk.OaCgfkData;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/11/27
 */
@SpringBootTest
class PaymentOrderBizServiceImplTest {
    @Autowired PaymentOrderBizService paymentOrderBizService;

    @Autowired
    OrderSettlementBizService orderSettlementBizService;

    @Autowired
    PurchaseOrderBizService purchaseOrderBizService;


    @Test
    public void submit() {
        paymentOrderBizService.submit(1L);
    }

    @Test
    public void page() throws Exception {
//        paymentOrderBizService.viewDetail(5L);
//        PaymentPageQuery pageQuery = new PaymentPageQuery();
//        pageQuery.setRelatedNo("CG2312070003");
//        paymentOrderBizService.page(pageQuery);
//        paymentOrderBizService.export(ListUtil.of(8L));

//        FormDetailPageQuery query = new FormDetailPageQuery();
//        query.setIds(ListUtil.of(59L,411L));
//        query.setPageSize(9999);
//        orderSettlementBizService.viewFormDetailPage(query);

//        PurchaseOrderPageQuery query = new PurchaseOrderPageQuery();
//        query.setPaymentOrderStatus(PaymentOrderStatus.WAIT_AUDIT);
//        PageResponse<PurchaseOrderPageVO> page = purchaseOrderBizService.page(query);
//        System.out.println(JsonUtil.toJson(page.getData()));

        /*OaCgfkCallback oaCgfkCallback = new OaCgfkCallback();
        oaCgfkCallback.setProcessInstId("FKD202312141401");
        oaCgfkCallback.setNodeName("模拟业务财务审核");
        oaCgfkCallback.setUserLoginName("ying.he");
        oaCgfkCallback.setStatus(1);
        oaCgfkCallback.setPaymentStatus(8881915083509351400L);
        paymentOrderBizService.oaCallback(oaCgfkCallback);*/

//        SettlementOrderPageQuery pageQuery = new SettlementOrderPageQuery();
//        pageQuery.setPayApplyStatus(PayApplyStatus.FINISHED);
//        orderSettlementBizService.settlementPageQuery(pageQuery);

//        paymentOrderBizService.page(new PaymentPageQuery());

        ReqTemplateImpl bean = SpringUtil.getBean(ReqTemplateImpl.class);
        Map<String, String> stringStringMap = bean.queryProviderBankInfo("G0001");
        System.out.println(JsonUtil.toJson(stringStringMap));
    }

    @Test
    void view() {
      }

    @Test
    void viewDetail() {
      }

    @Test
    void save() {
      }

    @Test
    void detailDiffLog() {
      }

    @Test
    void main() {
      }

    @Test
    void sync() {
      }

    @Test
    void remove() {
      }

    @Test
    void check() {
      }

    @Test
    void getOaCgfkForm() throws IOException {
        final OaCgfkData oaCgfkForm = ((PaymentOrderBizServiceImpl) paymentOrderBizService).getOaCgfkForm(79L);
        System.out.println(JsonUtil.toJson(oaCgfkForm));
    }

    @Test
    void oaCallback() {
      }

    @Test
    void export() {
      }

    @Test
    void refreshRightAmount() {
      }

    @Test
    void getPaymentInfoByRelatedNo() {
      }
}
