package com.daddylab.supplier.item;

import com.daddylab.supplier.item.application.criticalPush.ManagedPush;
import com.daddylab.supplier.item.application.criticalPush.SourceType;
import com.daddylab.supplier.item.application.criticalPush.TargetType;
import com.daddylab.supplier.item.controller.category.dto.CategoryCmd;
import java.util.List;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/5/5
 */
@Service
public class CriticalManagedPushTestServiceImpl implements CriticalManagedPushTestService {

    public String managedPushMethod(List<CategoryCmd> cmd, long id) {
        return ((CriticalManagedPushTestServiceImpl) AopContext.currentProxy())
                .managedPushMethod0(cmd, id);
    }

    @ManagedPush(sourceType = SourceType.CATEOGRY, targetType = TargetType.KINGDEE, idArgIndex = 1, async = true)
    protected String managedPushMethod0(List<CategoryCmd> cmd, long id) {
        System.out.println("测试推送方法执行 cmd = " + cmd + " id:" + id);
//        throw new RuntimeException("旺店通调用异常！", new WdtErpException(100, "！！！"));
        return "result";
    }
}
