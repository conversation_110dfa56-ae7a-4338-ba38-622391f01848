package com.daddylab.supplier.item.infrastructure.kuaidaoyun;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.GlobalFeignConfiguration;
import com.daddylab.supplier.item.infrastructure.kuaidaoyun.configs.KdyConfig;
import com.daddylab.supplier.item.infrastructure.kuaidaoyun.types.KdyBaseRequest;
import com.daddylab.supplier.item.infrastructure.kuaidaoyun.types.KdySubscribeData;
import com.daddylab.supplier.item.infrastructure.kuaidaoyun.types.KdySubscribeResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration;
import org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.autoconfigure.RefreshAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;

/**
 * <AUTHOR>
 * @since 2024/5/21
 */
@EnableFeignClients(clients = KdyAPI.class)
@SpringBootTest(classes = {
        KdyAPI.class,
        FeignAutoConfiguration.class,
        ConfigurationPropertiesAutoConfiguration.class,
        KdyConfig.class,
        RefreshAutoConfiguration.class,
        HttpMessageConvertersAutoConfiguration.class,
        GlobalFeignConfiguration.class
}, properties = {
        "spring.cloud.nacos.config.enabled=false",
        "feign.hystrix.enabled=false",
        "spring.profiles.active=test",
        "feign.client.config.default.loggerLevel=FULL",
        "kuaidaoyun.account=daddylab",
        "kuaidaoyun.secret=8f3062d7d4bd2f07e6009b35558aaeb46f4ad62d"
})
class KdyAPITest {

    @Autowired
    private KdyAPI kdyAPI;

    @Autowired
    private KdyConfig kdyConfig;

    @Test
    public void test() {
        final KdySubscribeData data = KdySubscribeData.of("顺丰速运", "***************", "8404", "erp:trace:81");
        final KdyBaseRequest<KdySubscribeData> request = new KdyBaseRequest<>(kdyConfig, data);
        final String subscribeResult = kdyAPI.subscribe(request);
        final KdySubscribeResponse kdySubscribeResponse = new KdySubscribeResponse(subscribeResult);
        System.out.println(kdySubscribeResponse);
    }

}