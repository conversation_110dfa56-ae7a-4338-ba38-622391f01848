package com.daddylab.supplier.item.infrastructure.utils;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2022/12/19
 */
class HtmlUtilTest {

    @Test
    public void containHtmlTag() {
        Assertions.assertFalse(HtmlUtil.containHtmlTag(
                "https://haohuo.jinritemai.com/ecommerce/trade/detail/index.html?id=3588227752794403133&origin_type=604"));
        Assertions.assertTrue(HtmlUtil.containHtmlTag(
                "<p>https://haohuo.jinritemai.com/ecommerce/trade/detail/index.html?id=3588227752794403133&origin_type=604"));
        Assertions.assertFalse(HtmlUtil.containHtmlTag(
                "<红色苹果128G，热售中！>https://haohuo.jinritemai.com/ecommerce/trade/detail/index.html?id=3588227752794403133&origin_type=604"));
        Assertions.assertTrue(HtmlUtil.containHtmlTag(
                "<红色苹果128G，热售中！><p class=\"hello world\" bold>https://haohuo.jinritemai.com/ecommerce/trade/detail/index.html?id=3588227752794403133&origin_type=604</p>"));
        Assertions.assertTrue(HtmlUtil.containHtmlTag(
                "<红色苹果128G，热售中！><img href=\"https://haohuo.jinritemai.com/ecommerce/trade/detail/index.html?id=3588227752794403133&origin_type=604\"/>"));
        Assertions.assertTrue(HtmlUtil.containHtmlTag("<br/>"));
        Assertions.assertFalse(HtmlUtil.containHtmlTag("<br//>"));
        Assertions.assertFalse(HtmlUtil.containHtmlTag(""));
        Assertions.assertFalse(HtmlUtil.containHtmlTag(null));
    }

}