package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ReqData;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ReviewAddHitNumParam;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import java.util.ArrayList;
import org.apache.commons.compress.utils.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;

/**
 * <AUTHOR>
 * @since 2022/12/12
 */
@EnableFeignClients(basePackages = "com.daddylab.supplier.item.infrastructure.gatewayimpl.feign")
@SpringBootTest(classes = {
        HttpMessageConvertersAutoConfiguration.class,
        FeignAutoConfiguration.class,
        PartnerReviewThesaurusAPI.class,
        PartnerReviewThesaurusServerConfiguration.class,
        PartnerReviewThesaurusAPIFallbackFactory.class
},properties = {
        "spring.cloud.nacos.config.enabled=false",
        "spring.profiles.active=test",
        "feign.client.config.default.readTimeout=5000",
        "feign.client.config.default.connectTimeout=5000",
        "feign.client.config.default.loggerLevel=FULL",
        "partner-review-thesaurus-server.url=https://api.dlab.cn",
        "partner-review-thesaurus-server.api-token=RxOENnJX#eo4n$Uj"

})
class PartnerReviewThesaurusAPITest {
    @Autowired
    PartnerReviewThesaurusAPI partnerReviewThesaurusAPI;

    @Test
    public void reviewAddHitNum() {
        final ArrayList<ReviewAddHitNumParam> reviewAddHitNumParams = Lists.newArrayList();
        final ReviewAddHitNumParam param = new ReviewAddHitNumParam();
        param.setId(1);
        param.setHitNum(1);
        reviewAddHitNumParams.add(param);
        final Rsp<Void> resp = partnerReviewThesaurusAPI.reviewAddHitNum(
                new ReqData<>(reviewAddHitNumParams));
        System.out.println(resp);
    }
}