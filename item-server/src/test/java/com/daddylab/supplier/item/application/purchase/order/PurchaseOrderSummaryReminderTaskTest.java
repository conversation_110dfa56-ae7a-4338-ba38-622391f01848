package com.daddylab.supplier.item.application.purchase.order;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 2024/1/16
 */
@SpringBootTest
class PurchaseOrderSummaryReminderTaskTest {
    @Autowired PurchaseOrderSummaryReminderTask purchaseOrderSummaryReminderTask;

    @Test
    public void doTask() {
        purchaseOrderSummaryReminderTask.doTask();
    }
}
