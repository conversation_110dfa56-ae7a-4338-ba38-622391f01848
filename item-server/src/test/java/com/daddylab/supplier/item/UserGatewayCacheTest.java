package com.daddylab.supplier.item;

import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffListQuery;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(properties = {"spring.profiles.active=dev"})
@Slf4j
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class UserGatewayCacheTest {
    @Autowired
    @Qualifier("UserGatewayCacheImpl")
    UserGateway userGatewayCacheImpl;

    @Autowired
    UserGateway userGateway;

    @Test
    public void test(){
        final StaffListQuery query = new StaffListQuery();
        query.setPageSize(10);
        query.setPageIndex(1);
        query.setStatus(1);
        final List<StaffInfo> staffInfos = userGatewayCacheImpl.queryStaffList(query);

        log.debug("staffInfos = {}", staffInfos);

        for (StaffInfo staffInfo : staffInfos) {
            final Long userId = staffInfo.getUserId();
            final StaffInfo staffInfo1 = userGatewayCacheImpl.queryStaffInfoById(userId);
            Assertions.assertNotNull(staffInfo1, "查询员工信息失败");
        }

        for (StaffInfo staffInfo : staffInfos) {
            final Long userId = staffInfo.getUserId();
            final StaffInfo staffInfo1 = userGatewayCacheImpl.queryStaffInfoById(userId);
            Assertions.assertNotNull(staffInfo1, "查询员工信息失败");
        }

        //Assertions.assertTrue(userGatewayCacheImpl instanceof UserGatewayCacheOps, "UserGateway未实现UserGatewayCacheOps");
        //((UserGatewayCacheOps) userGatewayCacheImpl).clearAllCache();
    }

    @Test
    public void test1(){
        final StaffInfo staffInfo = userGateway.queryStaffInfoById(7724818L);
        System.out.println("staffInfo = " + staffInfo);
    }

    @Test
    public void test2(){
        StaffListQuery query = new StaffListQuery();
//        query.setPageSize(10);
//        query.setPageIndex(0);
        query.setName("傅圣维");
//        query.setMobile("");
//        query.setStatus(0);
        final List<StaffInfo> staffInfos = userGateway.queryStaffList(query);
        System.out.println("staffInfo = " + staffInfos);
    }

    @Test
    public void test3() {
        StaffListQuery staffListQuery = new StaffListQuery();
        staffListQuery.setPageIndex(1);
        staffListQuery.setPageSize(1);
        staffListQuery.setNickname("小高");
        staffListQuery.setDept("消费者事业部");
        List<StaffInfo> staffInfos = userGateway.queryStaffList(staffListQuery);
        System.out.println("-----------");
    }
}
