package com.daddylab.supplier.item.application.drawer;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;

/**
 * <AUTHOR>
 * @since 2022/9/27
 */
@MockitoSettings
class ItemDrawerImportMallServiceTest {

    @Mock
    ItemDrawerImportMallGateway gateway;

    @Test
    void importItemImageAndVideo() {
        when(gateway.getItemDrawerId(anyLong())).thenReturn(Optional.of(1L));
        when(gateway.getItemIds(anyString())).thenReturn(Collections.singletonList(1L));
        when(gateway.isMatchItem(anyString(), anyLong())).thenReturn(true);
        when(gateway.hasItemDrawerImage(anyLong(), any())).thenReturn(false);
        final ItemDrawerImportMallServiceImpl itemDrawerImportMallService = new ItemDrawerImportMallServiceImpl(
                gateway);
        Assertions.assertDoesNotThrow(() -> {
            itemDrawerImportMallService.importItemImageAndVideo(
                    Files.newInputStream(Paths.get("/Users/<USER>/Downloads/item.csv")));
        }, "导入遇到异常");
    }

    @DisplayName("导入商品详情图片")
    @Test
    public void importItemDetailImage() {
        when(gateway.getItemDrawerId(anyLong())).thenReturn(Optional.of(1L));
        when(gateway.getItemIds(anyString())).thenReturn(Collections.singletonList(1L));
        when(gateway.isMatchItem(anyString(), anyLong())).thenReturn(true);
        when(gateway.hasItemDrawerImage(anyLong(), any())).thenReturn(false);
        final ItemDrawerImportMallServiceImpl itemDrawerImportMallService = new ItemDrawerImportMallServiceImpl(
                gateway);
        Assertions.assertDoesNotThrow(() -> {
            itemDrawerImportMallService.importItemDetailImage(
                    Files.newInputStream(Paths.get("/Users/<USER>/Downloads/item_detail.csv")));
        }, "导入遇到异常");

    }


    @Test
    void importAttrImage() {
        when(gateway.getItemDrawerId(anyLong())).thenReturn(Optional.of(1L));
        when(gateway.getItemIds(anyString())).thenReturn(Collections.singletonList(1L));
        when(gateway.isMatchItem(anyString(), anyLong())).thenReturn(true);
        when(gateway.hasItemDrawerImage(anyLong(), any())).thenReturn(false);
        when(gateway.mapAttrValueToId(anyString(), anyString(), anyString())).thenReturn(
                Optional.of(1L));
        final ItemDrawerImportMallServiceImpl itemDrawerImportMallService = new ItemDrawerImportMallServiceImpl(
                gateway);
        Assertions.assertDoesNotThrow(() -> {
            itemDrawerImportMallService.importAttrImage(
                    Files.newInputStream(Paths.get("/Users/<USER>/Downloads/spec_image.csv")));
        }, "导入遇到异常");
    }

    @Test
    void importVideoCover() {
        when(gateway.getItemDrawerId(anyLong())).thenReturn(Optional.of(1L));
        when(gateway.getItemIds(anyString())).thenReturn(Collections.singletonList(1L));
        when(gateway.isMatchItem(anyString(), anyLong())).thenReturn(true);
        final ItemDrawerImportMallServiceImpl itemDrawerImportMallService = new ItemDrawerImportMallServiceImpl(
                gateway);
        Assertions.assertDoesNotThrow(() -> {
            itemDrawerImportMallService.importVideoCover(
                    Files.newInputStream(Paths.get("/Users/<USER>/Downloads/item.csv")));
        }, "导入遇到异常");
    }
}