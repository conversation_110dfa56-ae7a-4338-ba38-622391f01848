package com.daddylab.supplier.item.application.visitStat;

import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2022/8/29
 */
@Slf4j
class VisitStateManagerTest {

    @DisplayName("当个用户同日多次访问")
    @Test
    public void visitTest1() {
        final VisitStateManager visitStateManager = new VisitStateManager();
        AtomicInteger count = new AtomicInteger();
        visitStateManager.visit(1L, DateUtil.parse("2022-08-29 00:00:00"), (userId, visitTime) -> {
            count.incrementAndGet();
            log.info("记录用户每日访问记录 {} {}", userId, visitTime);
        });
        visitStateManager.visit(1L, DateUtil.parse("2022-08-29 00:00:01"), (userId, visitTime) -> {
            count.incrementAndGet();
            log.info("记录用户每日访问记录 {} {}", userId, visitTime);
        });
        Assertions.assertEquals(1, count.get(), "访问状态控制异常");
    }

    @DisplayName("单个用户跨天访问")
    @Test
    public void visitTest2() {
        final VisitStateManager visitStateManager = new VisitStateManager();
        AtomicInteger count = new AtomicInteger();
        visitStateManager.visit(1L, DateUtil.parse("2022-08-29 23:59:59"), (userId, visitTime) -> {
            count.incrementAndGet();
            log.info("记录用户每日访问记录 {} {}", userId, visitTime);
        });
        visitStateManager.visit(1L, DateUtil.parse("2022-08-30 00:00:00"), (userId, visitTime) -> {
            count.incrementAndGet();
            log.info("记录用户每日访问记录 {} {}", userId, visitTime);
        });
        Assertions.assertEquals(2, count.get(), "访问状态控制异常");
    }

    @DisplayName("多个用户同日多次访问")
    @Test
    public void visitTest3() {
        final VisitStateManager visitStateManager = new VisitStateManager();
        AtomicInteger count = new AtomicInteger();
        visitStateManager.visit(1L, DateUtil.parse("2022-08-29 00:00:00"), (userId, visitTime) -> {
            count.incrementAndGet();
            log.info("记录用户每日访问记录 {} {}", userId, visitTime);
        });
        visitStateManager.visit(2L, DateUtil.parse("2022-08-29 01:00:00"), (userId, visitTime) -> {
            count.incrementAndGet();
            log.info("记录用户每日访问记录 {} {}", userId, visitTime);
        });
        visitStateManager.visit(2L, DateUtil.parse("2022-08-29 09:00:00"), (userId, visitTime) -> {
            count.incrementAndGet();
            log.info("记录用户每日访问记录 {} {}", userId, visitTime);
        });
        visitStateManager.visit(4L, DateUtil.parse("2022-08-29 23:00:00"), (userId, visitTime) -> {
            count.incrementAndGet();
            log.info("记录用户每日访问记录 {} {}", userId, visitTime);
        });

        Assertions.assertEquals(3, count.get(), "访问状态控制异常");
    }

    @DisplayName("单个用户并发访问")
    @Test
    public void visitTest4() throws InterruptedException {
        final VisitStateManager visitStateManager = new VisitStateManager();
        AtomicInteger count = new AtomicInteger();
        final int num = 10;
        final CountDownLatch countDownLatch = new CountDownLatch(num);

        for (int i = 0; i < num; i++) {
            CompletableFuture.runAsync(() -> {
                visitStateManager.visit(1L, DateUtil.parse("2022-08-29 00:00:00"),
                        (userId, visitTime) -> {
                            count.incrementAndGet();
                            log.info("记录用户每日访问记录 {} {}", userId, visitTime);
                        });
            }).thenRun(countDownLatch::countDown);
        }
        countDownLatch.await();
        Assertions.assertEquals(1, count.get(), "访问状态控制异常");
    }
}