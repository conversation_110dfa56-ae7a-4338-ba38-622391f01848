//package com.daddylab.supplier.item.application.shipinghao;
//
//import com.daddylab.supplier.item.application.shipinghao.service.WechatProductSyncService;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//
///**
// * 微信商品增量同步测试
// *
// * <AUTHOR>
// * @date 2024-12-19
// */
//@Slf4j
//@SpringBootTest
//public class IncrementalSyncTest {
//
//    @Autowired
//    private WechatProductSyncService wechatProductSyncService;
//
//    /**
//     * 测试增量同步 - 不提供lastUpdateTime（默认24小时）
//     */
//    @Test
//    public void testIncrementalSyncWithoutTime() {
//        String type = "daddylab"; // 或者 "member"，根据您的配置
//
//        log.info("开始测试增量同步（不提供更新时间）...");
//        boolean result = wechatProductSyncService.syncIncrementalProducts(type, null);
//        log.info("增量同步测试结果: {}", result);
//    }
//
//    /**
//     * 测试增量同步 - 指定lastUpdateTime（最近1小时）
//     */
//    @Test
//    public void testIncrementalSyncWithOneHour() {
//        String type = "daddylab"; // 或者 "member"，根据您的配置
//        Long oneHourAgo = System.currentTimeMillis() - 60 * 60 * 1000L; // 1小时前
//
//        log.info("开始测试增量同步（1小时前）...");
//        boolean result = wechatProductSyncService.syncIncrementalProducts(type, oneHourAgo);
//        log.info("增量同步测试结果: {}", result);
//    }
//
//    /**
//     * 测试增量同步 - 指定lastUpdateTime（最近1天）
//     */
//    @Test
//    public void testIncrementalSyncWithOneDay() {
//        String type = "daddylab"; // 或者 "member"，根据您的配置
//        Long oneDayAgo = System.currentTimeMillis() - 24 * 60 * 60 * 1000L; // 1天前
//
//        log.info("开始测试增量同步（1天前）...");
//        boolean result = wechatProductSyncService.syncIncrementalProducts(type, oneDayAgo);
//        log.info("增量同步测试结果: {}", result);
//    }
//
//    /**
//     * 测试增量同步 - 指定lastUpdateTime（很久以前，应该同步所有商品）
//     */
//    @Test
//    public void testIncrementalSyncWithOldTime() {
//        String type = "daddylab"; // 或者 "member"，根据您的配置
//        Long oldTime = System.currentTimeMillis() - 365 * 24 * 60 * 60 * 1000L; // 1年前
//
//        log.info("开始测试增量同步（1年前）...");
//        boolean result = wechatProductSyncService.syncIncrementalProducts(type, oldTime);
//        log.info("增量同步测试结果: {}", result);
//    }
//}