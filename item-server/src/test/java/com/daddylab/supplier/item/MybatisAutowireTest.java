package com.daddylab.supplier.item;

import com.daddylab.supplier.item.domain.brand.entity.BrandEntity;
import com.daddylab.supplier.item.domain.brand.gateway.BrandGateway;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
public class MybatisAutowireTest {

    @Autowired
    BrandGateway brandGateway;

    @Test
    public void autowire(){
        final BrandEntity brand = brandGateway.getBrand(1L);
        System.out.println("brand = " + brand);
    }

}
