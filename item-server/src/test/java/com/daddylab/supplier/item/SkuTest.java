package com.daddylab.supplier.item;

import com.alibaba.cola.dto.PageResponse;
import com.daddylab.supplier.item.application.item.combinationItem.CombinationItemBizService;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuVO;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.framework.TestConfigForMail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuAttrRefDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/7 7:01 下午
 * @description
 */
@SpringBootTest()
@ActiveProfiles("test")
@Import(TestConfigForMail.class)
@WebAppConfiguration
public class SkuTest {

    @Autowired
    IItemSkuService iItemSkuService;

    @Autowired
    ItemSkuGateway itemSkuGateway;

    @Autowired
    CombinationItemBizService combinationItemBizService;

    @Test
    @DisplayName("sku属性")
    public void testAttr(){
        final List<SkuAttrRefDO> skuAttrList = itemSkuGateway.getSkuAttrList(Collections.singletonList(1511L));
        System.out.println(JsonUtil.toJson(skuAttrList));
    }

    @Test
    @DisplayName("组合商品，sku单个名字列表")
    public void test1(){
        ComposeSkuPageQuery query = new ComposeSkuPageQuery();
        query.setSkuCode("");
        query.setItemId(0L);
        query.setItemCode("");
        query.setCategoryId(516L);
        query.setBrandId(2L);
        query.setPageIndex(0);
        query.setPageSize(10);

        final PageResponse<ComposeSkuVO> skuListBaseVOPageResponse = combinationItemBizService.pageSku(query);
        System.out.println(JsonUtil.toJson(skuListBaseVOPageResponse));
    }
}
