package com.daddylab.supplier.item;

import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQConsumerBase;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.redisson.api.RedissonClient;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.env.StandardEnvironment;

import javax.annotation.Nullable;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @since 2022/2/17
 */
@Slf4j
public class MqConsumerBaseTest {

    @NonNull
    private static RedissonClient getRedissonClientMock() {
        return Mockito.mock(RedissonClient.class);
    }

    @Test
    public void voidConsumer() {
        final RefreshConfig refreshConfig = new RefreshConfig();
        refreshConfig.setMqRepeatConsumeCheckTime(600);

        final StandardEnvironment environment = new StandardEnvironment();
        final HashMap<String, Object> source = new HashMap<>();
        source.put("spring.profiles.active", "test");
        environment.getPropertySources().addFirst(new MapPropertySource("mockProperty", source));
        System.out.println("source = " + environment.resolvePlaceholders("${spring.profiles.active}_trade"));
        final OperateLog operateLog = new OperateLog();
        operateLog.setId(1L);
        operateLog.setTargetType(OperateLogTarget.ITEM);
        operateLog.setTargetId("1");
        operateLog.setMsg("测试消费");
        final String json = JsonUtil.toJson(operateLog);
        final RocketMQConsumerBase<Void> testConsumer = new RocketMQConsumerBase<Void>(getRedissonClientMock(), environment, refreshConfig) {
            @Override
            protected void handle(MessageExt msg, @Nullable Void body) {
                Assertions.assertNull(body);
            }
        };
        final MessageExt msg = new MessageExt();
        msg.setMsgId("7F00000100067DAF6ECC50A822E54755");
        msg.setBrokerName("test");
        msg.setKeys("afterSuccessPay_trade:421838");
        msg.setTopic("prod_trade");
        msg.setTags("prod_afterSuccessPay_trade");
        msg.setBody(json.getBytes());
        testConsumer.onMessage(msg);
    }

    @Test
    public void bytesConsumer() {
        final RefreshConfig refreshConfig = new RefreshConfig();
        refreshConfig.setMqRepeatConsumeCheckTime(600);

        final StandardEnvironment environment = new StandardEnvironment();
        final HashMap<String, Object> source = new HashMap<>();
        source.put("spring.profiles.active", "test");
        environment.getPropertySources().addFirst(new MapPropertySource("mockProperty", source));
        System.out.println("source = " + environment.resolvePlaceholders("${spring.profiles.active}_trade"));
        final OperateLog operateLog = new OperateLog();
        operateLog.setId(1L);
        operateLog.setTargetType(OperateLogTarget.ITEM);
        operateLog.setTargetId("1");
        operateLog.setMsg("测试消费");
        final String json = JsonUtil.toJson(operateLog);
        final RocketMQConsumerBase<byte[]> testConsumer = new RocketMQConsumerBase<byte[]>(getRedissonClientMock(), environment, refreshConfig) {
            @Override
            protected void handle(MessageExt msg, @Nullable byte[] body) {
                assert body != null;
                final String jsonStr = new String(body);
                log.info("msg={} bodyStr={}", msg, jsonStr);
                Assertions.assertEquals(jsonStr, json);
            }
        };
        final MessageExt msg = new MessageExt();
        msg.setMsgId("0000000007F00000100067DAF6ECC50A822E54755");
        msg.setBrokerName("test");
        msg.setKeys("afterSuccessPay_trade:421838");
        msg.setTopic("prod_trade");
        msg.setTags("prod_afterSuccessPay_trade");
        msg.setBody(json.getBytes());
        testConsumer.onMessage(msg);
    }

    @Test
    public void GenericConsumer() {
        final RefreshConfig refreshConfig = new RefreshConfig();
        refreshConfig.setMqRepeatConsumeCheckTime(600);

        final StandardEnvironment environment = new StandardEnvironment();
        final HashMap<String, Object> source = new HashMap<>();
        source.put("spring.profiles.active", "test");
        environment.getPropertySources().addFirst(new MapPropertySource("mockProperty", source));
        System.out.println("source = " + environment.resolvePlaceholders("${spring.profiles.active}_trade"));
        final OperateLog operateLog = new OperateLog();
        operateLog.setId(1L);
        operateLog.setTargetType(OperateLogTarget.ITEM);
        operateLog.setTargetId("1");
        operateLog.setMsg("测试消费");
        final String json = JsonUtil.toJson(operateLog);
        final RocketMQConsumerBase<OperateLog> testConsumer = new RocketMQConsumerBase<OperateLog>(getRedissonClientMock(), environment, refreshConfig) {
            @Override
            protected void handle(MessageExt msg, @Nullable OperateLog body) {
                log.info("body={},msg={}", body, msg);
                final byte[] rawBody = msg.getBody();
                Assertions.assertNotNull(rawBody);
                Assertions.assertNotNull(body);
                Assertions.assertEquals(json, JsonUtil.toJson(body));
                Assertions.assertEquals(operateLog, body);
                Assertions.assertArrayEquals(rawBody, JsonUtil.toJson(body).getBytes());
            }
        };
        final MessageExt msg = new MessageExt();
        msg.setMsgId("0000000007F00000100067DAF6ECC50A822E54755");
        msg.setBrokerName("test");
        msg.setKeys("afterSuccessPay_trade:421838");
        msg.setTopic("prod_trade");
        msg.setTags("prod_afterSuccessPay_trade");
        msg.setBody(json.getBytes());
        testConsumer.onMessage(msg);
    }

}
