package com.daddylab.supplier.item;

import cn.hutool.core.util.URLUtil;
import com.daddylab.supplier.item.domain.itemSync.DouLink;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2022/11/15
 */
public class LinkTest {

    @Test
    public void douLinkTest() {
        System.out.println(
                new DouLink(
                        "haohuo.jinritemai.com/ecommerce/trade/detail/index.html?id=3581979923847470754&origin_type=604")
                        .getGoodsId());
    }

    @Test
    public void getPath() {
        final String url = "https://cdn.daddylab.com/Upload/supplier/item/image/TXG1239%E8%AF%A6%E6%83%85 %E7%9C%8B%E5%9B%BE%E7%8E%8B-1668132766504.jpg";
        final String path = URLUtil.getPath(
                URLUtil.encode(url));
        System.out.println(path);
    }
}
