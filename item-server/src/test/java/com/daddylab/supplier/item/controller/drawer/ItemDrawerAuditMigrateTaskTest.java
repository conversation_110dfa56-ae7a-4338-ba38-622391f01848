package com.daddylab.supplier.item.controller.drawer;

import com.daddylab.supplier.item.application.drawer.ItemDrawerAuditMigrateTask;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 2022/11/23
 */
@SpringBootTest
class ItemDrawerAuditMigrateTaskTest {
    @Resource
    ItemDrawerAuditMigrateTask itemDrawerAuditMigrateTask;

    @Test
    void doTask() {
        itemDrawerAuditMigrateTask.doTask();
    }

}