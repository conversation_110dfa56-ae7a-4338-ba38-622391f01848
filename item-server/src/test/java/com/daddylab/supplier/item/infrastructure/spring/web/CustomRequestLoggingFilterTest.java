package com.daddylab.supplier.item.infrastructure.spring.web;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.auth.AuthAppService;
import com.daddylab.supplier.item.domain.auth.gateway.LoginGateway;
import com.daddylab.supplier.item.infrastructure.config.FilterConfig;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.UserVisitDailyMapper;
import com.daddylab.supplier.item.infrastructure.spring.web.CustomRequestLoggingFilterTest.HelloController;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoSettings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/10/14
 */
@WebMvcTest(controllers = HelloController.class, properties = {
        "spring.cloud.nacos.config.enabled=false",
        "logging.level.web.api.hello=debug"
})
@Import(value = {SpringUtil.class, HelloController.class, FilterConfig.class})
@MockitoSettings
class CustomRequestLoggingFilterTest {

    @RestController
    public static class HelloController {

        @PostMapping(value = "/hello", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
        public String hello(@RequestParam Map<String, String> params) {
            return JsonUtil.toJson(params);
        }

        @PostMapping(value = "/error", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
        public Response error(@RequestParam Map<String, String> params) {
            throw new RuntimeException("异常异常");
        }
    }

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AuthAppService authAppService;

    @MockBean
    private LoginGateway loginGateway;

    @MockBean
    RefreshConfig refreshConfig;

    @MockBean
    UserVisitDailyMapper userVisitDailyMapper;

    @Test
    void hello() throws Exception {
        Mockito.when(loginGateway.getLoginId()).thenReturn(1L);
        Mockito.when(loginGateway.isLogin()).thenReturn(true);
        final LinkedMultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("p1", "p1v");
        params.add("p2", "p2v");
        final LinkedMultiValueMap<String, String> urlParams = new LinkedMultiValueMap<>();
        urlParams.add("name", "world");
        final Map<String, String> allParams = Stream.of(params, urlParams)
                .flatMap(v -> v.entrySet().stream()).collect(
                        Collectors.toMap(Entry::getKey,
                                v -> v.getValue().stream().findAny().orElse(""), (a, b) -> a));
        String token = "vF_32b5KhHbtZUU9S_Vj9Eb56ILbiuzS6P__";
        final String responseBody = mockMvc.perform(
                        post("/hello")
                                .queryParams(urlParams)
                                .params(params)
                                .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                                .header("Authorization", "BEARER " + token)
                ).andDo(print())
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        Assertions.assertEquals(
                allParams,
                JsonUtil.parse(responseBody, Map.class)
        );
    }

    @Test
    void error() throws Exception {
        Mockito.when(loginGateway.getLoginId()).thenReturn(1L);
        Mockito.when(loginGateway.isLogin()).thenReturn(true);
        final LinkedMultiValueMap<String, String> urlParams = new LinkedMultiValueMap<>();
        urlParams.add("name", "world");
        final Map<String, String> allParams = Stream.of(urlParams)
                .flatMap(v -> v.entrySet().stream()).collect(
                        Collectors.toMap(Entry::getKey,
                                v -> v.getValue().stream().findAny().orElse(""), (a, b) -> a));
        String token = "vF_32b5KhHbtZUU9S_Vj9Eb56ILbiuzS6P__";
        final String responseBody = mockMvc.perform(
                        post("/error")
                                .queryParams(urlParams)
                                .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                                .header("Authorization", "BEARER " + token)
                ).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
        System.out.println(responseBody);
    }
}