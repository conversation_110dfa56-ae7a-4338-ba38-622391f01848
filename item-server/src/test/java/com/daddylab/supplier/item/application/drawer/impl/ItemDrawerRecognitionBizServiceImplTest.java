package com.daddylab.supplier.item.application.drawer.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditType;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerModuleId;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 2022/12/6
 */
@SpringBootTest
class ItemDrawerRecognitionBizServiceImplTest {

    @Resource
    ItemDrawerRecognitionBizServiceImpl itemDrawerRecognitionBizService;

    @Test
    public void createRecognitionTasks() {
        itemDrawerRecognitionBizService.createRecognitionTasks(ItemAuditType.ITEM_MATERIAL, 363507L,
                3, ItemDrawerModuleId.values());
    }
}