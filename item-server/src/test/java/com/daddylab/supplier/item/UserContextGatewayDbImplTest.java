package com.daddylab.supplier.item;

import com.daddylab.supplier.item.domain.auth.gateway.UserContextGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Slf4j
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@TestPropertySource(properties = {
        "spring.profiles.active:dev"
})
@SpringBootTest
public class UserContextGatewayDbImplTest {
    @Autowired
    @Qualifier("UserContextGatewayDbImpl")
    UserContextGateway userContextGatewayDbImpl;

    @Test
    @DisplayName("测试从DB查询用户登录上下文信息")
    public void testUserContextGatewayDbImpl() {
        final long userId = 7571297L;
        final UserContext.UserInfo userContext = userContextGatewayDbImpl.getUserContext(userId, true);
        log.debug("userContext:" + JsonUtil.toJson(userContext));
        assertNotNull(userContext);
        assertEquals(userContext.getUserId(), userId);
    }
}
