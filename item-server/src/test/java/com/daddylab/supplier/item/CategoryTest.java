package com.daddylab.supplier.item;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.controller.category.CategoryController;
import com.daddylab.supplier.item.controller.category.dto.CategoryCmd;
import com.daddylab.supplier.item.controller.category.dto.CategoryQueryCmd;
import com.daddylab.supplier.item.controller.category.dto.CategoryVo;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.category.service.CategoryDomainService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CategoryAttr;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/29 2:33 下午
 * @description
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("test")
public class CategoryTest {

    @Autowired
    private CategoryGateway categoryGateway;

    @Autowired
    CategoryDomainService categoryDomainService;

    @Autowired
    CategoryController categoryController;


    @Test
    public void testList(){
        CategoryQueryCmd cmd = new CategoryQueryCmd();
        cmd.setLevel(1);
        final MultiResponse<CategoryVo> categoryVoMultiResponse = categoryController.viewList(cmd);
        System.out.println(JsonUtil.toJson(categoryVoMultiResponse));
    }


    @Test
    @DisplayName("创建根品类")
    public void test() {
        CategoryCmd cmd = new CategoryCmd();
        cmd.setLevel(1);
        cmd.setName("try");
//        cmd.setShortName("F");
//        cmd.setParentId(0L);
        categoryController.add(cmd);
    }

    @Test
    @DisplayName("创建品类")
    public void test2() {
        CategoryCmd cmd = new CategoryCmd();
        cmd.setLevel(3);
        cmd.setName("大三2");
        cmd.setParentId(341L);
        System.out.println(JsonUtil.objToStr(categoryController.add(cmd)));
    }

    @Test
    @DisplayName("编辑品类")
    public void updateCategory(){
        final Response kingdee0d12 = categoryController.update(258L, "kingdeeC一");
    }

    @Test
    @DisplayName("查看品类")
    public void test1(){
        CategoryQueryCmd query = new CategoryQueryCmd();
        query.setName("大一");
        query.setLevel(1);
        final MultiResponse<CategoryVo> categoryVoMultiResponse = categoryController.viewList(query);
        System.out.println(JsonUtil.objToStr(categoryVoMultiResponse));
    }

    @Test
    @DisplayName("修改品类")
    public void test3(){
        categoryController.update(96L,"kingdee3");
    }

    @Test
    @DisplayName("删除品类")
    public void test4(){
        categoryController.delete(511L);
    }

    @Test
    @DisplayName("添加品类属性")
    public void test5(){
        categoryController.addAttr(2L,"属性3");
    }

    @Test
    @DisplayName("查询属性")
    public void test6(){
        final MultiResponse<CategoryAttr> multiResponse = categoryController.viewAttrList(3L);
        System.out.println(JsonUtil.objToStr(multiResponse));
    }

}
