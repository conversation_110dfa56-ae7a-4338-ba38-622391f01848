package com.daddylab.supplier.item.infrastructure.cv;

import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.microsoft.azure.cognitiveservices.vision.computervision.ComputerVisionClient;
import com.microsoft.azure.cognitiveservices.vision.computervision.models.ReadOperationResult;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.Test;
import rx.Single;

/**
 * <AUTHOR>
 * @since 2022/11/28
 */
class CVUtilTest {

    @Test
    public void imageVision() throws ExecutionException, InterruptedException {
        final ComputerVisionConfig computerVisionConfig = new ComputerVisionConfig();
        computerVisionConfig.setSubscriptionKey("78df0f545e2d4322a514c1a306245ce6");
        computerVisionConfig.setEndpoint("https://daddylab-test.cognitiveservices.azure.cn/");
        final ComputerVisionClient computerVisionClient = computerVisionConfig.computerVisionClient();
//        final String url = "https://cdn.daddylab.com/Upload/supplier/item/image/%E8%88%92%E7%BC%93%E5%8E%BB%E5%B1%91%E6%B4%97%E5%8F%91%E9%9C%B2-1669364435095.jpg";
        final String url = "https://cdn-test.daddylab.com/Upload/supplier/item/image/红心火龙果详情页-1661243964655-1668479381478.jpg";
        final Single<ReadOperationResult> imageVision = CVUtil.imageVision(computerVisionClient,
                url);
        imageVision.subscribe(v -> {
            System.out.println("onNext:" + JsonUtil.toJson(v));
        }, e -> {
            System.out.println("onError:" + e);
        });
        imageVision.toCompletable().await();


    }
}