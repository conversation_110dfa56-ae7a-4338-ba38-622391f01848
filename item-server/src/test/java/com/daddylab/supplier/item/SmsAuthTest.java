package com.daddylab.supplier.item;

import com.daddylab.supplier.item.application.purchase.PurchaseBizService;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseCmd;
import com.daddylab.supplier.item.domain.smsAuth.SmsAuthService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest
@ActiveProfiles("test")
@Slf4j
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class SmsAuthTest {

    @Autowired
    private SmsAuthService smsAuthService;
    @Autowired
    private PurchaseBizService purchaseBizService;



    @Test
    @Order(1)
    @DisplayName("验证码正确")
    public void smsAuthTrue() {
        final String mobile = "18072885161";
        final String smsCode = smsAuthService.getSmsCode(mobile);
        final boolean verifyCode = smsAuthService.verifyCode(mobile, smsCode);
        Assertions.assertTrue(verifyCode, "验证码验证结果与预期不一致");
    }

    @Test
    @Order(2)
    @DisplayName("验证码错误")
    public void smsAuthFalse() {
        final String mobile = "18072885161";
        final String smsCode = smsAuthService.getSmsCode(mobile);
        final boolean verifyCode = smsAuthService.verifyCode(mobile, "66666666");
        Assertions.assertFalse(verifyCode, "验证码验证结果与预期不一致");

        final boolean verifyCode1 = smsAuthService.verifyCode("18072885160", "66666666");
        Assertions.assertFalse(verifyCode1, "验证码验证结果与预期不一致");
    }

    @Test
    public void test1(){
        PurchaseCmd purchaseCmd = new PurchaseCmd();
        purchaseCmd.setMonth("2021/11");
        purchaseCmd.setItemSku("SG00025901");
        purchaseCmd.setItemName("好吃到妙蛙种子都说妙的妙脆角");
        purchaseCmd.setProvider("风味");
        purchaseCmd.setBuyerId(7203941L);
        purchaseBizService.createOrUpdatePurchase(purchaseCmd);
    }


}
