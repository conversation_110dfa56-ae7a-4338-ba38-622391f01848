package com.daddylab.supplier.item.application.platformItemSkuInventory.support;

import com.daddylab.supplier.item.ItemApplication;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatAccessTokenDto;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductListDto;
import com.daddylab.supplier.item.application.shipinghao.service.WechatApiService;
import com.daddylab.supplier.item.application.shop.ShopAuthorizationBizService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 视频号商品同步测试类 用于在测试环境启动实际的同步流程
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@SpringBootTest(classes = {ItemApplication.class})
public class ShiPingHaoItemSyncTest {

  @Autowired private WechatVideoItemSyncServiceImpl shiPingHaoItemSyncService;

  @Autowired private WechatApiService wechatApiService;

  @Autowired ShopAuthorizationBizService shopAuthorizationBizService;

  /** 测试next_key分页逻辑 验证微信API的游标分页是否正确工作 */
  @Test
  public void testNextKeyPagination() {
    log.info("开始测试next_key分页逻辑");

    try {
      String type = "daddylab";
      int pageSize = 5; // 使用较小的页面大小便于测试
      String nextKey = null;
      int totalFetched = 0;
      int pageCount = 0;

      // 获取访问令牌
      WechatAccessTokenDto accessToken = wechatApiService.getAccessToken(type);
      if (accessToken == null || !accessToken.isSuccess()) {
        log.error("获取访问令牌失败，类型: {}", type);
        return;
      }

      log.info("成功获取访问令牌，类型: {}", type);

      // 分页获取数据，最多获取3页作为测试
      while (pageCount < 3) {
        pageCount++;
        log.info("正在获取第{}页数据，next_key: {}", pageCount, nextKey);

        WechatProductListDto productList =
            wechatApiService.getProductListWithNextKey(
                type, accessToken.getAccessToken(), pageSize, nextKey);

        if (productList == null
            || productList.getProductIds() == null
            || productList.getProductIds().isEmpty()) {
          log.info("第{}页没有更多数据，结束分页", pageCount);
          break;
        }

        int currentPageSize = productList.getProductIds().size();
        totalFetched += currentPageSize;

        log.info(
            "第{}页获取成功，商品数量: {}, 累计获取: {}, 总数: {}",
            pageCount,
            currentPageSize,
            totalFetched,
            productList.getTotalNum());

        // 打印商品ID
        for (String productId : productList.getProductIds()) {
          log.info("  商品ID: {}", productId);
        }

        // 更新next_key
        nextKey = productList.getNextKey();
        log.info("下一页next_key: {}", nextKey);

        // 如果没有next_key，说明已经是最后一页
        if (nextKey == null || nextKey.isEmpty()) {
          log.info("没有next_key，已到达最后一页");
          break;
        }
      }

      log.info("next_key分页测试完成，总共获取{}页，{}个商品", pageCount, totalFetched);

    } catch (Exception e) {
      log.error("next_key分页测试失败", e);
      throw e;
    }
  }

  /** 测试全量同步 启动实际的同步流程，将视频号平台的商品信息拉回来 */
  @Test
  public void testFullSync() {

    shopAuthorizationBizService.authorize("K0002");
    shopAuthorizationBizService.authorize("K0340");

    log.info("开始执行视频号商品全量同步测试");

    try {
      // 启动全量同步
      shiPingHaoItemSyncService.fullDoseSync();

      log.info("视频号商品全量同步调用完成，但批处理作业可能仍在后台运行");
      
      // 等待一段时间让批处理作业完成
      // 注意：这是一个简单的等待方式，实际生产环境建议使用更精确的等待机制
      log.info("等待批处理作业完成...");
      Thread.sleep(60000); // 等待60秒，根据实际数据量调整
      
      log.info("等待完成，测试结束");

    } catch (InterruptedException e) {
      log.error("测试等待被中断", e);
      Thread.currentThread().interrupt();
      throw new RuntimeException("测试被中断", e);
    } catch (Exception e) {
      log.error("视频号商品全量同步执行失败", e);
      throw e;
    }
  }

  /** 
   * 测试全量同步（改进版）
   * 通过监控JobExecution状态来等待作业完成
   */
  @Test
  public void testFullSyncWithMonitoring() {
    shopAuthorizationBizService.authorize("K0002");
    shopAuthorizationBizService.authorize("K0340");

    log.info("开始执行视频号商品全量同步测试（监控版）");

    try {
      // 在单独线程中执行同步
      Thread syncThread = new Thread(() -> {
        try {
          shiPingHaoItemSyncService.fullDoseSync();
        } catch (Exception e) {
          log.error("同步线程执行失败", e);
        }
      });
      
      syncThread.start();
      
      // 等待同步线程完成，最多等待10分钟
      syncThread.join(600000); // 600秒 = 10分钟
      
      if (syncThread.isAlive()) {
        log.warn("同步作业超时，强制停止等待");
        syncThread.interrupt();
      } else {
        log.info("同步作业已完成");
      }

    } catch (InterruptedException e) {
      log.error("测试被中断", e);
      Thread.currentThread().interrupt();
    } catch (Exception e) {
      log.error("视频号商品全量同步测试失败", e);
      throw new RuntimeException("全量同步测试失败", e);
    }
  }


  /** 测试单个商品同步 同步指定的单个商品 */
  @Test
  public void testSyncSingleItem() {
    log.info("开始执行视频号单个商品同步测试");

    try {
      // 这里需要替换为实际的店铺编号和商品ID
      String shopNo = "test-shop";
      String outerItemId = "10000248893987"; // 替换为实际的商品ID

      // 执行单个商品同步
      shiPingHaoItemSyncService.syncItem(shopNo, outerItemId);

      log.info("视频号单个商品同步执行完成，商品ID: {}", outerItemId);

    } catch (Exception e) {
      log.error("视频号单个商品同步执行失败", e);
      throw e;
    }
  }

    /**
     * 测试简单单线程同步（推荐使用，避免并发问题）
     */
    @Test
    public void testSimpleSingleThreadSync() {
        log.info("开始测试简单单线程同步...");
        try {
            shiPingHaoItemSyncService.executeSimpleSingleThreadSync();
            log.info("简单单线程同步测试完成");
        } catch (Exception e) {
            log.error("简单单线程同步测试失败", e);
            throw e;
        }
    }
}
