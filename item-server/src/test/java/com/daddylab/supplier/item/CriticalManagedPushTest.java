package com.daddylab.supplier.item;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.CriticalManagedPushTest.TestConfiguration;
import com.daddylab.supplier.item.application.criticalPush.AsyncCriticalPushManagerImpl;
import com.daddylab.supplier.item.application.criticalPush.CriticalPushConsumer;
import com.daddylab.supplier.item.application.criticalPush.CriticalPushManagerImpl;
import com.daddylab.supplier.item.application.criticalPush.PushManagerAspect;
import com.daddylab.supplier.item.application.criticalPush.PushState;
import com.daddylab.supplier.item.controller.category.dto.CategoryCmd;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.config.ThreadConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataPush;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataPushLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl.DataPushLogServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl.DataPushServiceImpl;
import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQProducer;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Objects;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageClientExt;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Answers;
import org.mockito.junit.jupiter.MockitoSettings;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.aop.AopAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/4/25
 */
@Slf4j
@SpringBootTest(properties = {
        "spring.cloud.nacos.config.enabled=false"
}, classes = {
        TestConfiguration.class,
        AopAutoConfiguration.class,
        PushManagerAspect.class,
        CriticalManagedPushTest.class,
        AsyncCriticalPushManagerImpl.class,
        CriticalPushManagerImpl.class,
        CriticalPushConsumer.class,
        CriticalManagedPushTestServiceImpl.class,
        SpringUtil.class,
        ThreadConfig.class
})
@MockitoSettings
@Component
public class CriticalManagedPushTest implements Serializable {

    private static final long serialVersionUID = -4933435229357971738L;
    final CountDownLatch terminalSignal = new CountDownLatch(1);
    final ExecutorService executorService = Executors.newFixedThreadPool(1);
    final BlockingQueue<MessageExt> queue = new LinkedBlockingQueue<>();
    @Autowired
    DataPushServiceImpl dataPushService;
    @Autowired
    DataPushLogServiceImpl dataPushLogService;
    @Autowired
    RocketMQProducer rocketMQProducer;
    @Autowired
    CriticalManagedPushTestService testService;
    @Autowired
    CriticalPushConsumer criticalPushConsumer;

    @Test
    @DisplayName("@ManagedPush注解切面测试")
    public void managedPushAspect() {
        mockMqSend();

        mockConsumer();

        final CategoryCmd cmd = new CategoryCmd();
        cmd.setParentId(1L);
        cmd.setName("测试");
        cmd.setLevel(1);

        final ArrayList<CategoryCmd> cmdList = new ArrayList<>();
        cmdList.add(cmd);

        AtomicReference<DataPush> dataPushRef = new AtomicReference<>();
        AtomicReference<DataPushLog> dataPushLogRef = new AtomicReference<>();

        //当内部调用保存推送记录的时候，进行参数检查
        when(dataPushService.save(any())).thenAnswer(invocation -> {
            final DataPush dataPush = invocation.getArgument(0, DataPush.class);
            if (Objects.isNull(dataPush.getId())) {
                dataPush.setId(1L);
            }
            dataPushRef.set(dataPush);
            log.info("SAVE dataPush:{}", dataPushRef.get());
            return true;
        });

        when(dataPushService.getById(any())).thenAnswer(invocation -> {
            final Object id = invocation.getArgument(0);
            log.info("GET dataPush:{}", id);
            return dataPushRef.get();
        });

        //当内部调用保存推送日志的时候，进行参数检查
        when(dataPushLogService.save(any())).thenAnswer(invocation -> {
            final DataPushLog dataPushLog = invocation.getArgument(0, DataPushLog.class);
            dataPushLogRef.set(dataPushLog);
            dataPushLogRef.get().setId(1L);
            log.info("SAVE dataPushLog:{}", dataPushLogRef.get());
            return true;
        });

        final String result = testService.managedPushMethod(cmdList, 100);
        assertNull(result, "切面方法执行结果后应该是NULL");

        try {
            terminalSignal.await();

            assertEquals(1L, dataPushLogRef.get().getPushId(), "推送日志记录未记录推送记录ID");
            assertEquals(JsonUtil.toJson(new Object[]{cmdList, 100}), dataPushLogRef.get().getReq(),
                    "推送日志记录未正确记录请求参数");
            assertEquals(PushState.SUCCESS.getValue(), dataPushRef.get().getState(),
                    "推送记录状态未变更为推送成功");
            assertEquals("result", dataPushLogRef.get().getResp(), "推送日志记录未正确记录响应结果");

            executorService.shutdown();
            final boolean awaitTermination = executorService.awaitTermination(60, TimeUnit.SECONDS);
            if (!awaitTermination) {
                System.out.println("线程池未成功关闭");
            }
        } catch (InterruptedException ignored) {
        }
    }

    private void mockConsumer() {
        executorService.execute(() -> {
            while (true) {
                try {
                    final MessageExt msg = queue.take();
                    criticalPushConsumer.onMessage(msg);
                    break;
                } catch (InterruptedException ignored) {
                } finally {
                    terminalSignal.countDown();
                }
            }
        });
    }

    private void mockMqSend() {
        when(rocketMQProducer.syncSend(any(), anyString(), anyString(), anyString()))
                .thenAnswer(invocation -> {
                    try {
                        final Object[] arguments = invocation.getArguments();
                        final String json = JsonUtil.toJson(arguments[0]);
                        log.info("发送MQ消息 参数:{}", arguments);
                        final MessageClientExt msg = new MessageClientExt();
                        msg.setBody(json.getBytes());
                        msg.setTopic(((String) arguments[1]));
                        msg.setTags(((String) arguments[2]));
                        msg.setKeys(((String) arguments[3]));
                        queue.add(msg);
                        log.info("MQ消息发送成功:{}", msg);
                    } catch (Exception e) {
                        throw new RuntimeException("消息消费异常", e);
                    }
                    return true;
                });
    }

    @Configuration
    @EnableAspectJAutoProxy(exposeProxy = true)
    public static class TestConfiguration {

        @MockBean
        DataPushServiceImpl dataPushService;

        @MockBean
        DataPushLogServiceImpl dataPushLogService;

        @MockBean
        RocketMQProducer rocketMQProducer;

        @MockBean
        RefreshConfig refreshConfig;

        @MockBean(answer = Answers.RETURNS_MOCKS)
        RedissonClient redissonClient;
    }


}
