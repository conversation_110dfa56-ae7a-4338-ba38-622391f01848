package com.daddylab.supplier.item;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.otherStockIn.OtherStockInBizService;
import com.daddylab.supplier.item.application.otherStockInDetail.OtherStockInDetailBizService;
import com.daddylab.supplier.item.application.purchase.PurchaseBizService;
import com.daddylab.supplier.item.application.purchase.order.PurchaseOrderBizService;
import com.daddylab.supplier.item.application.purchase.purchaseTable.PurchaseTableBizService;
import com.daddylab.supplier.item.application.saleItem.ShopSnData;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.shortlink.ShortLinkService;
import com.daddylab.supplier.item.application.warehouse.WarehouseBizService;
import com.daddylab.supplier.item.common.OtherStockInOutConfig;
import com.daddylab.supplier.item.controller.item.dto.ItemSpecPageQuery;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseQueryPage;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OtherStockInDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDadStaffService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffListQuery;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName ShortLingTest.java
 * @description
 * @createTime 2021年11月18日 11:04:00
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class ShortLingTest {

    @Autowired
    private ShortLinkService shortLinkService;
    @Autowired
    private PurchaseBizService purchaseBizService;
    @Autowired
    private PartnerFeignClient partnerFeignClient;
    @Autowired
    private com.daddylab.supplier.item.domain.smsAuth.SmsAuthService smsAuthService;
    @Autowired
    private OtherStockInDetailMapper otherStockInDetailMapper;
    @Autowired
    private ShopSnData shopSnData;
    @Autowired
    private OtherStockInBizService otherStockInBizService;
    @Autowired
    @Qualifier("UserGatewayCacheImpl")
    private UserGateway userGateway;
    @Autowired
    private ItemSkuGateway itemSkuGateway;
    @Autowired
    private NewGoodsBizService newGoodsBizService;
    @Autowired
    private ItemBizService itemBizService;
    @Autowired
    private OtherStockInOutConfig otherStockInOutConfig;
    @Autowired
    private WarehouseBizService warehouseBizService;
    @Autowired
    private OtherStockInDetailBizService otherStockInDetailBizService;
    @Autowired
    private PurchaseOrderBizService purchaseOrderBizService;
    @Autowired
    private PurchaseTableBizService purchaseTableBizService;
    @Autowired
    private ItemGateway itemGateway;
    @Autowired
    private IDadStaffService dadStaffService;

    @Test
    public void test1() {
        String path = "http:dqwdqwfqwfwqff/code/12/23/35/46";
        String generate = shortLinkService.generate(path);
        System.out.println(generate);
    }

    @Test
    public void test2() {
//        MultiResponse<PurchaseDetail> yrqcMprs6wIR48bk = purchaseBizService.getDetailByCode("1614XwRF", "YrqcMprs6wIR48bk");
//        System.out.println(1111);
//
//        SingleResponse<String> stringSingleResponse = purchaseBizService.checkCode("1614XwRF", "1111");
//        System.out.println(
//                111
//        );
//
//        SingleResponse<PurchaseDetail> access = purchaseBizService.getAccess(63L);
//        System.out.println(1);

        purchaseBizService.exportExcel(new PurchaseQueryPage());

//        SingleResponse<String> crsj7NtV = purchaseBizService.getMobileByCode("crsj7NtV");


//        PartnerPurchaseReq partnerPurchaseReq = new PartnerPurchaseReq();
//        partnerPurchaseReq.setOrganizationName("大兴安");
//        ArrayList<String> strings = new ArrayList<>();
//        strings.add("有机野生蔓越莓原浆");
//        partnerPurchaseReq.setItemNames(JsonUtil.toJson(strings));
//        partnerPurchaseReq.setPageSize(10);
//        //调用合作伙伴接口获取手机号
//        Rsp<List<PartnerPurchaseResp>> listRsp = partnerFeignClient.mobileQuery(partnerPurchaseReq);
//        System.out.println(listRsp);

    }

    @Test
    public void test3() {
        String smsCode = smsAuthService.getSmsCode("15527354534");
        System.out.println(smsCode);
    }

    @Test
    public void test4() {
        boolean b = smsAuthService.verifyCode("19817161446", "");
        if (b){
            System.out.println("校验通过");
        }

//        Response wrDhbQr6 = purchaseBizService.getSmsCode("QNmEq6Q3");
//        System.out.println();

//        Response wrDhbQr6 = purchaseBizService.checkCode("QNmEq6Q3","0841");
//        System.out.println(wrDhbQr6);

        SingleResponse<String> qNmEq6Q3 = purchaseBizService.getMobileByCode("QNmEq6Q3");
        System.out.println(
                111
        );
    }

    @Test
    public void test5(){

        File file = new File("D:\\采购导入模板 -1126.xlsx");
        MultipartFile multipartFile = fileToMultipartFile(file);

        try {
            purchaseBizService.importExcel(multipartFile.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static MultipartFile fileToMultipartFile(File file) {
        FileItem fileItem = createFileItem(file);
        MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
        return multipartFile;
    }

    private static FileItem createFileItem(File file) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        FileItem item = factory.createItem("textField", "text/plain", true, file.getName());
        int bytesRead = 0;
        byte[] buffer = new byte[8192];
        try {
            FileInputStream fis = new FileInputStream(file);
            OutputStream os = item.getOutputStream();
            while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            fis.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return item;
    }

    @Test
    public void test6(){

//        /**
//         * 处理上个月的纯活动商品
//         */
        purchaseBizService.dealActiveItem();

//        purchaseBizService.exportExcel(new PurchaseQueryPage());

    }

    @Test
    public void test7(){

        PurchaseQueryPage purchaseQueryPage = new PurchaseQueryPage();
        long l = purchaseBizService.exportExcel(purchaseQueryPage);



//        PurchaseQueryPage purchaseQueryPage = new PurchaseQueryPage();
//        purchaseQueryPage.setBuyerId(7571297L);
//        purchaseBizService.exportExcel(purchaseQueryPage);

    }

    @Test
    public void test9(){
        String no = "YF-QT00"+ DateUtil.formatNow("yyyyMMdd");
        Long increment = RedisUtil.increment(no, 1);
        String format = "";
        format = String.format("%02d", increment);
        no = no + format;

//        SingleResponse<PurchaseItem> dataByItemSku = purchaseBizService.getDataByItemSku("08070101");
//        System.out.println(dataByItemSku.getData());
    }

    @Test
    public void test10(){
        boolean sup0MT067070M = check(null, null, null, null);
        System.out.println(sup0MT067070M);
    }

    private boolean check(String sku, String code, String name, Long itemId) {
        if (StringUtils.isNotBlank(sku) || StringUtils.isNotBlank(code) || StringUtils.isNotBlank(name) || Objects.nonNull(itemId)) {
            return true;
        } else {
            return false;
        }
    }

    @Test
    public void test11(){
        ArrayList<String> nos = Lists.newArrayList();
        if (Objects.nonNull(shopSnData.getMemberStoreSn())){
            nos.add(shopSnData.getMemberStoreSn());
        }
        if (Objects.nonNull(shopSnData.getAppletsSn())){
            nos.add(shopSnData.getAppletsSn());
        }
        if (Objects.nonNull(shopSnData.getBeautyStoreSn())){
            nos.add(shopSnData.getBeautyStoreSn());
        }
        if (Objects.nonNull(shopSnData.getMaternityBabySn())){
            nos.add(shopSnData.getMaternityBabySn());
        }
        if (Objects.nonNull(shopSnData.getWotoBuySn())){
            nos.add(shopSnData.getWotoBuySn());
        }
        if (Objects.nonNull(shopSnData.getTikTokSn())){
            nos.add(shopSnData.getTikTokSn());
        }
        System.out.println(nos);

    }

    @Test
    public void test12(){
        otherStockInBizService.getById(1L);
    }

    @Test
    public void test13(){
        StaffListQuery staffListQuery = new StaffListQuery();
        staffListQuery.setStatus(1);
        staffListQuery.setPageIndex(1);
        staffListQuery.setPageSize(10);
        List<String> productNames = userGateway.queryStaffList(staffListQuery).stream().map(StaffInfo::getNickname).collect(Collectors.toList());
        System.out.println(JSON.toJSONString(productNames));
            //查当前查看人的花名
//            StaffInfo staffInfo = userGateway.queryStaffInfoById(userId);
    }

    @Test
    public void test14(){
        String skuAttrListStr = itemSkuGateway.getSkuAttrListStr("100000801");
        System.out.println(skuAttrListStr);
    }

    @Test
    public void test15(){
//        newGoodsBizService.detailBySkuCode("te10021801");
//        newGoodsBizService.itemSync(10674L);
//        itemBizService.queryRunningDetail(137116L);

//        NewGoodsQueryPage newGoodsQueryPage = new NewGoodsQueryPage();
//        newGoodsQueryPage.setPageSize(10);
//        newGoodsQueryPage.setPageIndex(1);
//        newGoodsBizService.queryPage(newGoodsQueryPage);

//        itemBizService.queryDetail(137169L);

//        OtherStockInQueryPage queryPage = new OtherStockInQueryPage();
//        queryPage.setPageIndex(1);
//        queryPage.setPageSize(10);
//        otherStockInBizService.queryPage(queryPage);

//        List<String> warehouseNos = otherStockInOutConfig.getWarehouseNos();
//        ArrayList<Warehouse> list = com.google.common.collect.Lists.newArrayList();
//        warehouseNos.forEach( warehouseNo ->{
//            Optional<Warehouse> warehouse = warehouseBizService.getWarehouse(warehouseNo);
//            warehouse.ifPresent(list::add);
//        });
//        System.out.println(list);

//        otherStockInDetailBizService.getIdsByItem(11096L,null,null,null);

//        itemBizService.queryPrice(137242L);

//        PurchaseTable purchaseTable = new PurchaseTable();
//        purchaseTable.setMonth("2022/05");
//        purchaseTableBizService.insert(purchaseTable);

//        newGoodsBizService.itemSync(137285L);

//        List<ItemSku> skuList = itemSkuGateway.getSkuList(137289L);
//        List<String> codes = skuList.stream().map(ItemSku::getSkuCode).collect(Collectors.toList());
//        otherStockInBizService.getById(154L);
        Item item = new Item();
//        Optional<String> latestItemCode = itemGateway.getLatestItemCode();

//        String activeProfile = SpringUtil.getActiveProfile();
//        String latestSkuCode = activeProfile.substring(0, 2) + IdUtil.simpleUUID();
        String latestSkuCode = "te92ae09129a8d4f2f9885f89f0eab";

        String substring = latestSkuCode.substring(0, 2);
        latestSkuCode = latestSkuCode.substring(2);

        Long increment = RedisUtil.increment(latestSkuCode, 1);
        String format = String.format("%02d", increment);
        latestSkuCode = substring + latestSkuCode + format;
        System.out.println(latestSkuCode);

    }

    public static class YesOrNo {
        @JsonValue
        private boolean yesOrNo;

        public YesOrNo(int yesOrNo) {
            this.yesOrNo = yesOrNo > 0;
        }

        public YesOrNo(boolean yesOrNo) {
            this.yesOrNo = yesOrNo;
        }

        @Override
        public String toString() {
            return yesOrNo ? "是" : "否";
        }
    }

    @Data
    public static class Cmd {
        public YesOrNo isOk;
    }

    @Test
    public  void test16() {
//        NewGoodsCmd newGoods = new NewGoodsCmd();
//        newGoods.setId(1L);
//        newGoods.setNoReason(1);
//        newGoods.setActivePeriodStart(11112L);
//
//        NewGoodsCmd newGoods1 = new NewGoodsCmd();
//        newGoods1.setId(1L);
//        newGoods1.setActivePeriodStart(1111L);
//
////        final Diff diff = DiffUtil.diff(newGoods, newGoods1);
////        System.out.println(DiffUtil.toJson(diff));
//        DiffUtil.ObjListDiff objListDiff = DiffUtil.getInstance().diffObjList(Collections.singletonList(newGoods), Collections.singletonList(newGoods1), NewGoodsCmd.class);
//
//        System.out.println(objListDiff);

//        NewGoodsQueryPage newGoodsQueryPage = new NewGoodsQueryPage();
//        newGoodsQueryPage.setSkuCode("lo100001701");
//        newGoodsBizService.queryPage(newGoodsQueryPage);

        ItemSpecPageQuery itemSpecPageQuery = new ItemSpecPageQuery();
        itemSpecPageQuery.setSpec("白色");
        itemSpecPageQuery.setPageIndex(1);
        itemSpecPageQuery.setPageSize(10);
        itemBizService.getSpecList(itemSpecPageQuery);

    }
}
