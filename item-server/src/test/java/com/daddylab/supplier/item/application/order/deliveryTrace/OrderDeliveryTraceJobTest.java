package com.daddylab.supplier.item.application.order.deliveryTrace;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 2023/8/30
 */
@SpringBootTest
class OrderDeliveryTraceJobTest {
    @Autowired OrderDeliveryTraceJob orderDeliveryTraceJob;

    @Test
    public void filter() {
        orderDeliveryTraceJob.filter();
    }
}
