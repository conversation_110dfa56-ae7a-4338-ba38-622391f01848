package com.daddylab.supplier.item;

import cn.hutool.core.io.file.FileReader;
import cn.hutool.core.io.file.FileWriter;
import com.daddylab.supplier.item.application.message.MessageBizService;
import com.daddylab.supplier.item.application.provider.SyncProviderJob;
import com.daddylab.supplier.item.application.stockInOrder.QueryKingDeeOrderJob;
import com.daddylab.supplier.item.controller.category.CategoryController;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.infrastructure.config.KingDeeConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IKingDeeSkuPriceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchasePayableApplyOrderService;
import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.kingdee.dto.KingDeeSkuResp;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqJsonUtil;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQProducer;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/22 6:54 下午
 * @description
 */
@SpringBootTest(properties = {
        "spring.profiles.active:test"
})
@Slf4j
public class KingDeeTest {

    @Autowired
    CategoryGateway categoryGateway;

    @Autowired
    ProviderGateway providerGateway;

    @Autowired
    ShopGateway shopGateway;

    @Autowired
    CategoryController categoryController;

    @Autowired
    RocketMQProducer rocketMQProducer;

    @Resource
    KingDeeTemplate kingDeeTemplate;

    @Autowired
    ReqTemplate reqTemplate;

    @Autowired
    ReqJsonUtil ReqJsonUtil;

    @Autowired
    IKingDeeSkuPriceService iKingDeeSkuPriceService;

    @Autowired
    QueryKingDeeOrderJob queryKingDeeOrderJob;

    @Autowired
    SyncProviderJob syncProviderJob;

    @Autowired
    MessageBizService messageBizService;

    @Autowired
    IPurchasePayableApplyOrderService iPurchasePayableApplyOrderService;

    @Test
    public void demoTest() {
//        NewGoodsBizService bean = SpringUtil.getBean(NewGoodsBizService.class);
//        NewGoodsQueryPage page = new NewGoodsQueryPage();
//        PageResponse<Dict> dictPageResponse = bean.queryPageGroupBySpu(page);


//        ReqTemplate reqTemplate1 = SpringUtil.getBean(ReqTemplate.class);
//        Optional<KingDeeProviderResp> ss = reqTemplate1.queryProvider("浙江友耐家居用品有限公司");
//        System.out.println(JsonUtil.toJson(ss.get()));

//        PurchasePayableApplyOrder byId = iPurchasePayableApplyOrderService.getById(102L);
//        System.out.println(ReqJsonUtil.savePayBill(byId));

//        kingDeeTemplate.handler(ApiEnum.SAVE_PAY_BILL, 102L, null, false);

    }


    @Data
    @AllArgsConstructor
    private class Res {
        String code;
        String name;
    }

    private void wdtSubKingDee() {
        String wdtJson = "/Users/<USER>/Desktop/wdt_goods.json";
        String kingDeeJson = "/Users/<USER>/Downloads/zz_project/supplier-item/target/test-classes/kingDeeSku2.json";

        List<Res> wdtList = new LinkedList<>();
        FileReader fileReader1 = new FileReader(wdtJson);
        String result1 = fileReader1.readString();
        JsonNode parse1 = JsonUtil.parse(result1);
        for (JsonNode jsonNode : parse1) {
            String goodsNo = jsonNode.get("goodsNo").asText();
            String goodsName = jsonNode.get("goodsName").asText();
            Res res = new Res(goodsNo, goodsName);
            wdtList.add(res);
        }
        List<String> wdtCodeList = wdtList.stream().map(Res::getCode).collect(Collectors.toList());

        List<Res> kingDeeList = new LinkedList<>();
        FileReader fileReader2 = new FileReader(kingDeeJson);
        String result2 = fileReader2.readString();
        JsonNode parse2 = JsonUtil.parse(result2);
        for (JsonNode jsonNode : parse2) {
            String goodsNo = jsonNode.get("code").asText();
            String goodsName = jsonNode.get("name").asText();
            Res res = new Res(goodsNo, goodsName);
            kingDeeList.add(res);
        }
        List<String> kingDeeCodeList = kingDeeList.stream().map(Res::getCode).collect(Collectors.toList());

        // 存在wdt,但是不存在kingDee
        List<String> collect1 = wdtCodeList.stream().filter(v -> !kingDeeCodeList.contains(v)).collect(Collectors.toList());
        // 存在kingDee,但是不存在wdt
        List<String> collect2 = kingDeeCodeList.stream().filter(v -> !wdtCodeList.contains(v)).collect(Collectors.toList());

        // 存在wdt,但是不存在kingDee
        FileWriter writer1 = new FileWriter("wdtSubKingDee.json");
        List<Res> resList1 = wdtList.stream().filter(v -> collect1.contains(v.getCode())).collect(Collectors.toList());
        writer1.append(JsonUtil.toJson(resList1));
        System.out.println(resList1.size());

        // 存在kingDee,但是不存在wdt
        FileWriter writer2 = new FileWriter("kingDeeSubWdt.json");
        List<Res> resList2 = kingDeeList.stream().filter(v -> collect2.contains(v.getCode())).collect(Collectors.toList());
        writer2.append(JsonUtil.toJson(resList2));
        System.out.println(resList2.size());

    }

    private final static String TIME_OUT_FLAG = "timed-out and no fallback available";

    @Autowired
    KingDeeConfig kingDeeConfig;

    public void querySKu() throws Exception {


        int i = 0;
        long startRow = 0L;
        long limit = 10L;
        List<KingDeeSkuResp> list = reqTemplate.querySkuProviderList(startRow, limit);

        System.out.println(JsonUtil.toJson(list));

//        while (CollectionUtils.isNotEmpty(list)) {
//            List<KingDeeSkuPrice> list1 = new LinkedList<>();
//            for (KingDeeSkuResp kingDeeSkuResp : list) {
////                list1.add(skuPrice);
//            }
//            iKingDeeSkuPriceService.saveBatch(list1);
//
//            i = i + 1;
//            startRow = i * limit;
//            list = reqTemplate.querySkuList(startRow, limit);
//        }


    }

    private void write(List<KingDeeSkuResp> list, FileWriter writer) {
        writer.append(JsonUtil.toJson(list));
    }


    @Test
    public void testSyncMq() {
//        kingDeeTemplate.sendSyncMq(ApiEnum.SAVE_SKU, 0L, "");
    }

    /**
     * 同步入库单
     */
    @Test
    public void stockInOrderSync() {
//        KingDeeReqUtil.getReqJson(KingDeeApiEnum.SAVE_STOCK_IN_ORDER,)
//        kingDeeTemplate.saveStockInOrder();
    }

    /**
     * 同步供应商
     */
    @Test
    public void syncProvider() throws Exception {
        String reqJson = ReqJsonUtil.invoke(ApiEnum.SAVE_PROVIDER, 306L);
        reqTemplate.save(reqJson);
    }

    @Test
    public void syncRootCategory() throws Exception {
        String reqJson = ReqJsonUtil.saveRootCategory("1001", "erp根分组目录");
        System.out.println(reqTemplate.groupSave(reqJson));
    }

    /**
     * 同步物料分组
     */
    @Test
    public void syncCategory() throws Exception {
//        String reqJson = KingDeeReqUtil.getReqJson(KingDeeApiEnum.SAVE_CATEGORY, 106L);
//        System.out.println(kingDeeTemplate.saveCategory(reqJson, 106L));
        String reqJson = ReqJsonUtil.invoke(ApiEnum.GROUP_SAVE_CATEGORY, 105L);
        System.out.println(reqTemplate.groupSave(reqJson));
    }


    /**
     * 同步物料
     */
    @Test
    public void syncSku() throws Exception {

//        String reqJson = ReqJsonUtil.invoke(ApiEnum.SAVE_SKU, 12045L);
//        System.out.println(HttpUtil.invoke(ApiEnum.SAVE_SKU, reqJson));
//        MqDto mqDto = new MqDto(9511L, "100637");
        kingDeeTemplate.handler(ApiEnum.UPDATE_SKU, 9511L, "100637");
    }


    /**
     * 同步采料入库单
     */
    @org.junit.jupiter.api.Test
    public void syncStockInOrder() throws Exception {
//        Response kingDeeResponse = ((StockInOrderServiceBizImpl) AopContext.currentProxy()).pushKingDee(stockInOrder);

        // 同步
        // 1.保存。2提交。3.审核。

        // 修改。
        // 1.反审核,2.保存。3.提交，4.审核。

//        String s = ReqJsonUtil.operationReqJson("100010", ApiEnum.SAVE_STOCK_IN_ORDER.getFormId());
//        kingDeeTemplate.unAudit(s);
        kingDeeTemplate.handler(ApiEnum.SAVE_STOCK_IN_ORDER, 916L, "");
////         100009
//        kingDeeTemplate.submit(s);
//        kingDeeTemplate.aduit(s);

    }

    /**
     * 删除采购入库单
     *
     * @throws Exception
     */
    @Test
    public void deleteStockInOrder() throws Exception {
//        MqDto mqDto = new MqDto(40L, "100018");
        kingDeeTemplate.handler(ApiEnum.DELETE_STOCK_IN_ORDER, 40L, "100018");
    }


    @Test
    public void syncStockOutOrder() throws Exception {

//        MqDto mqDto = new MqDto(111L, "");
        kingDeeTemplate.handler(ApiEnum.SAVE_STOCK_OUT_ORDER, 111L, "");

//        String reqJson = ReqJsonUtil.invoke(ApiEnum.SAVE_STOCK_OUT_ORDER, 111L);
//        System.out.println(HttpUtil.invoke(ApiEnum.SAVE_STOCK_OUT_ORDER, reqJson));
    }


}
