package com.daddylab.supplier.item;

import com.daddylab.supplier.item.controller.item.dto.ItemLaunchPlanPageQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemLaunchPlanService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Author: <PERSON>
 * @Date: 2022/5/31 11:00
 * @Description: 请描述下这个类
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class ItemLaunchPlanServiceTest {
    @Autowired
    private IItemLaunchPlanService iItemLaunchPlanService;

    @Test
    public void test_pageQuery() {
        ItemLaunchPlanPageQuery pageQuery = new ItemLaunchPlanPageQuery();
        pageQuery.setPageIndex(1);
        pageQuery.setPageSize(10);
        pageQuery.setName("王翔测试");

        iItemLaunchPlanService.pageQuery(pageQuery);
    }
}
