package com.daddylab.supplier.item;

import cn.hutool.core.lang.Console;
import com.daddylab.supplier.item.infrastructure.separator.CommonSeparatorTemplate;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import java.util.Set;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR> up
 * @date 2022年11月28日 11:33 AM
 */
@SpringBootTest(properties = {
        "spring.profiles.active:test"
})
@Slf4j
public class CommonSeparatorTemplateTest {

    @Resource
    CommonSeparatorTemplate commonSeparatorTemplate;

    @Test
    public void test(){


        long currentTimeMillis = System.currentTimeMillis();
        Set<String> strings = commonSeparatorTemplate.seg("的是否11个Java开源中文分词器使用方法和分词效果对比老爸评测");
        System.out.println(JsonUtil.toJson(strings));
        Console.log("耗时:{}",System.currentTimeMillis()-currentTimeMillis);



        long currentTimeMillis2 = System.currentTimeMillis();
        Set<String> strings2 = commonSeparatorTemplate.seg("自定义用户词库为一个或多个文件夹或文件，可以使用绝对路径或相对路径老爸评测");
        System.out.println(JsonUtil.toJson(strings2));
        Console.log("耗时2:{}",System.currentTimeMillis()-currentTimeMillis2);
    }
}
