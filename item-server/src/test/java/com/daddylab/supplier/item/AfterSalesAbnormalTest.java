package com.daddylab.supplier.item;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.afterSales.AfterSalesAbnormalCmd;
import com.daddylab.supplier.item.application.afterSales.AfterSalesBizService;
import com.daddylab.supplier.item.application.afterSales.AfterSalesPageQuery;
import com.daddylab.supplier.item.application.afterSales.AfterSalesPageVO;
import com.daddylab.supplier.item.application.item.combinationItem.CombinationItemBizService;
import com.daddylab.supplier.item.application.order.settlement.sys.OrderSettlementSysService;
import com.daddylab.supplier.item.application.purchase.order.factory.CommonUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.PurchaseBillService;
import com.daddylab.supplier.item.application.saleItem.query.NewGoodsQueryPage;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsGroupVO;
import com.daddylab.supplier.item.controller.item.dto.ItemPageQuery;
import com.daddylab.supplier.item.controller.item.dto.ItemPageVo;
import com.daddylab.supplier.item.controller.test.DateScriptService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;

import lombok.extern.slf4j.Slf4j;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR> up
 * @date 2022年10月19日 2:51 PM
 */
@SpringBootTest(properties = {
        "spring.profiles.active:test"
})
@Slf4j
public class AfterSalesAbnormalTest {

    @Resource
    AfterSalesBizService afterSalesBizService;

    @Resource
    OrderSettlementSysService orderSettlementSysService;

    @Resource
    CommonUtil commonUtil;

    @Resource
    DateScriptService dateScriptService;

    @Resource
    PurchaseBillService purchaseBillService;

    @Test
    public void test0() throws IOException {
//        commonUtil.remindPurchaseOrder("202308", ListUtil.of(GlobalConstant.SEVEN_UP));
//        commonUtil.remindStockOrder("202308", ListUtil.of(GlobalConstant.SEVEN_UP));
//        commonUtil.remainBill("202308", "http://www.baidu.com", ListUtil.of(GlobalConstant.SEVEN_UP));
//        commonUtil.remainOrderSettlement("2023", ListUtil.of(GlobalConstant.SEVEN_UP));

//        dateScriptService.skuPriceAxis();

        purchaseBillService.createBill("202211");
        System.in.read();

    }

    @DisplayName("导出测试")
    @Test
    public void export() throws IOException {
        afterSalesBizService.exportAbnormalInfo(new AfterSalesPageQuery());
        System.in.read();
    }

    @Test
    public void editAbnormal() {
        String s = "{\"logisticsNo\":\"222\",\"logisticsName\":\"中111\",\"itemName\":\"栗子\",\"remark\":null,\"abnormalInfoId\":135,\"quantity\":4,\"imageUrls\":[\"https://cdn-test.daddylab.com/Upload/dpm/20221101/064408/lcekmmpuqph4kfda.jpg\",\"https://cdn-test.daddylab.com/Upload/dpm/20221101/064414/3f7qb5ak1253x7t6.jpeg\"]}";
        AfterSalesAbnormalCmd cmd = JsonUtil.parseJsonStr(s,AfterSalesAbnormalCmd.class);
        afterSalesBizService.saveAbnormalInfo(cmd);
    }

    @DisplayName("异常件信息登记")
    @Test
    public void saveAbnormal() {
        Dict d1 = Dict.create().set("no", "SF00012").set("name", "顺丰");
        Dict d2 = Dict.create().set("no", "SF0023012").set("name", "顺丰");
        Dict d3 = Dict.create().set("no", "ST23212").set("name", "申通");
        Dict d4 = Dict.create().set("no", "ST0023111").set("name", "申通");
        Dict d5 = Dict.create().set("no", "ZT0020014").set("name", "中通");
        Dict d6 = Dict.create().set("no", "ZT039007").set("name", "中通");
        List<Dict> dicts = Arrays.asList(d1, d2, d3, d4, d5, d6);
        PartnerProviderResp partnerProviderResp = new PartnerProviderResp();
        partnerProviderResp.setAddr("浙江杭州xxx区xxx路xxx街道xxx号");
        partnerProviderResp.setArea("上城区");
        partnerProviderResp.setCity("杭州");
        partnerProviderResp.setId(9999);
        partnerProviderResp.setMobile("188888886666");
        partnerProviderResp.setOrganizationName("人类同性繁殖研究所");
        partnerProviderResp.setOrganizationNo("998998998");
        partnerProviderResp.setPartnerId(8888);
        partnerProviderResp.setProvince("浙江");
        partnerProviderResp.setUName("猥琐张");

        for (int k = 0; k < 20; k++) {
            AfterSalesAbnormalCmd cmd = new AfterSalesAbnormalCmd();
            int i = RandomUtil.randomInt(6);
            cmd.setLogisticsNo(dicts.get(i).getStr("no"));
            cmd.setLogisticsName(dicts.get(i).getStr("name"));
            cmd.setItemName("商品名称-" + RandomUtil.randomString(4));
            cmd.setQuantity(RandomUtil.randomInt(100));
            List<String> imageList = new LinkedList<>();
            int i1 = RandomUtil.randomInt(5);
            for (int j = 0; j < i1; j++) {
                imageList.add("www." + RandomUtil.randomString(8) + ".com");
            }
            cmd.setImageUrls(imageList);
            cmd.setRemark("备注-" + RandomUtil.randomString(20));
//            cmd.setPartnerProviderResp(partnerProviderResp);
            SingleResponse<Boolean> booleanSingleResponse = afterSalesBizService.saveAbnormalInfo(cmd);
            log.info("-----------");
            System.out.println(JsonUtil.toJson(booleanSingleResponse));
        }
    }


    @DisplayName("异常件列表查询")
    @Test
    public void abnormalPage() {
        AfterSalesPageQuery query = new AfterSalesPageQuery();
        query.setLogisticsNo("*********");
//        query.setLogisticsName("通");
//        query.setItemName("");
//        query.setCreatedAtStart();
//        query.setCreatedAtEnd();
//        query.setWarehouseNo("hzlb3-test");
//        query.setReceiveStatus();
//        query.setPartnerProviderId(6658L);
//        query.setPartnerToken();
//        query.setAbnormalState(AfterSalesAbnormalState.WAIT_HANDLE);
        query.setPageIndex(1);
        query.setPageSize(10);
        PageResponse<AfterSalesPageVO> afterSalesPageVOPageResponse = afterSalesBizService.pageQueryAbnormalInfo(query);
        System.out.println(JSONUtil.toJsonStr(afterSalesPageVOPageResponse));
    }

    @DisplayName("保存转寄单")
    @Test
    public void saveSendOnInfo() {
//        String s = "{\"logisticsNo\":\"222\",\"logisticsName\":\"中111\",\"itemName\":\"栗子\",\"remark\":null,\"abnormalInfoId\":135,\"quantity\":4,\"imageUrls\":[\"https://cdn-test.daddylab.com/Upload/dpm/20221101/064408/lcekmmpuqph4kfda.jpg\",\"https://cdn-test.daddylab.com/Upload/dpm/20221101/064414/3f7qb5ak1253x7t6.jpeg\"]}";
//
//        AfterSalesSendOnCmd cmd = JsonUtil.parseJsonStr(s, AfterSalesSendOnCmd.class);
//        afterSalesBizService.saveSendOnInfo(cmd);
    }


    @Resource
    NewGoodsBizService newGoodsBizService;

    @Test
    public void demo() {
        NewGoodsQueryPage page = new NewGoodsQueryPage();
        page.setPageSize(10);
        page.setPageIndex(0);
        PageResponse<NewGoodsGroupVO> response = newGoodsBizService.queryPageGroupBySpu(page);
        System.out.println(JSONUtil.toJsonStr(response));
    }

    @Resource
    CombinationItemBizService combinationBizService;

    @Test
    public void demoCombination(){
        combinationBizService.get(2467L,null);
    }


    @Resource
    ItemMapper itemMapper;

    @Test
    public void testItemPage(){
        ItemPageQuery query = new ItemPageQuery();
        query.setBusinessLine(Collections.singletonList(1));
        query.setShelfStartTime(0L);
        query.setShelfEndTime(0L);
        query.setOffsetVal(0L);
        query.setPageSize(5);
        System.out.println(JsonUtil.toJson(query));
        List<ItemPageVo> itemPageVos = itemMapper.queryPage(query);
        System.out.println(JsonUtil.toJson(itemPageVos));


    }
}
