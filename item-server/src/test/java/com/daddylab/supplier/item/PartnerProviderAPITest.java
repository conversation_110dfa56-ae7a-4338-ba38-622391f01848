package com.daddylab.supplier.item;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerOpenFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;

/**
 * <AUTHOR>
 * @since 2022/10/26
 */
@EnableFeignClients(clients = {PartnerOpenFeignClient.class})
@SpringBootTest(
        classes = {
                HttpMessageConvertersAutoConfiguration.class,
                ConfigurationPropertiesBindingPostProcessor.class,
                FeignAutoConfiguration.class,
                PartnerOpenFeignClient.class,
        },
        properties = {
                "spring.cloud.nacos.config.enabled=false",
                "spring.profiles.active=test",
                "feign.client.config.default.loggerLevel=FULL",
                "partner-open-server.url=goapi.dlab.cn"
        }
)
@Slf4j
class PartnerProviderAPITest {

    @Autowired
    private PartnerOpenFeignClient partnerOpenFeignClient;



    @Test
    public void testEditGoodsTask() {
        final Rsp<PartnerProviderResp> partnerProviderRespRsp = partnerOpenFeignClient.providerQueryByToken(
                "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.n7vs0e0nRBFeDiloQJ5Ap5rAohQDD-RlE924XcENnws");
        System.out.println(partnerProviderRespRsp);


    }


}