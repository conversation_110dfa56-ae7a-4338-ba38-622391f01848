package com.daddylab.supplier.item;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.fastjson.JSONObject;
import com.daddylab.supplier.item.application.item.SuiteFetcher;
import com.daddylab.supplier.item.application.item.combinationItem.CombinationBizServiceImpl;
import com.daddylab.supplier.item.application.item.combinationItem.CombinationItemBizService;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.ProportionCmd;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.CombinationItemPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.CombinationPageVO;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ProportionVO;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.controller.item.ItemLaunchPlanTextParam;
import com.daddylab.supplier.item.domain.exportTask.ExportDomainService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CombinationDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CombinationItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CombinationItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICombinationItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl.ItemLaunchPlanServiceImpl;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/13 6:16 下午
 * @description
 */
@SpringBootTest(properties = {
        "spring.profiles.active:test"
})
@Slf4j
public class CombinationTest {


    @Autowired
    ExportDomainService exportDomainService;

    @Autowired
    CombinationItemMapper combinationItemMapper;

    @Resource
    CombinationItemBizService combinationItemBizService;

    @Resource
    ICombinationItemService iCombinationItemService;

    @Resource
    SuiteFetcher suiteFetcher;



//    @Test
//    public void testExport() {
//        CombinationItemPageQuery query = new CombinationItemPageQuery();
//        query.setType(CombinationItemType.COMMON);
//        exportDomainService.exportCombinationItem(query, ComposerSkuAllSheet.class);
//    }

    @Test
    public void testItemPage() {
        CombinationItemPageQuery query = new CombinationItemPageQuery();
//        query.setType(CombinationItemType.COMMON);
        query.setStartTime((1642070208L - 100));
        query.setEndTime((1642070208L + 100));
        final List<CombinationDO> combinationDOS = combinationItemMapper.listItem(query);
        System.out.println(JsonUtil.toJson(combinationDOS));

    }

    @Test
    public void testDiff() {
        final Map<String, Integer> saveSkuCodeCountMap = new HashMap<>();
        final Map<String, Integer> reqSkuCodeCountMap = new HashMap<>();
        reqSkuCodeCountMap.put("demo001", 1);
        reqSkuCodeCountMap.put("demo002", 3);
//        final ComposerSkuOperationEvent event = ComposerSkuOperationEvent.getEvent(saveSkuCodeCountMap, reqSkuCodeCountMap, 1L, UserContext.getUserId());
//        System.out.println(event.getMsg());
//        EventBusUtil.post(event, true);
    }

    public static void main(String[] args) {
        ProportionCmd c1 = new ProportionCmd();
        c1.setSkuCode("100216801");
        c1.setCount(1);
        c1.setCostPrice(new BigDecimal("12"));
        c1.setSalePrice(new BigDecimal("0"));

        ProportionCmd c2 = new ProportionCmd();
        c2.setSkuCode("10018908");
        c2.setCount(2);
        c2.setCostPrice(new BigDecimal("45"));
        c2.setSalePrice(new BigDecimal("0"));

        CombinationBizServiceImpl nn = new CombinationBizServiceImpl();
        List<ProportionVO> proportionVOS = nn.calculateProportion(ListUtil.of(c1, c2));
        System.out.println(JsonUtil.toJson(proportionVOS));
    }

    @Test
    public Long calculateProportionTest() {
        String ss = "[{\"costPrice\":120,\"count\":2,\"salePrice\":800,\"skuCode\":\"10018909\"},{\"costPrice\":12,\"count\":2,\"salePrice\":888,\"skuCode\":\"10018908\"}]";
        List<ProportionCmd> proportionCmds = JSONObject.parseArray(ss, ProportionCmd.class);
        List<ProportionVO> proportionVOS = combinationItemBizService.calculateProportion(proportionCmds);
        System.out.println(JSONUtil.toJsonStr(proportionVOS));
        return null;
    }

    @Test
    public void demo() throws InterruptedException {
        CountDownLatch countDownLatch = new CountDownLatch(50);
        AtomicReference<Long> count = new AtomicReference<>(0L);
        ThreadUtil.concurrencyTest(50, () -> {
            Long aLong = calculateProportionTest();
            System.out.println(aLong);
            count.set(count.get() + aLong);
            countDownLatch.countDown();
        });

        countDownLatch.await();
        System.out.println(new BigDecimal(count.get()).divide(new BigDecimal(100), 6, RoundingMode.HALF_UP));
    }

    @Test
    public void proportionTest() {
//        String s = "[{\\\"skuCode\\\":\\\"100014201\\\",\\\"oldCostPrice\\\":234.0\n" +
//                "            00000,\\\"newCostPrice\\\":2314.000000,\\\"oldSalesPrice\\\":889.000000,\\\"newSalesPrice\\\":8891.000000}]";
//        ResetProportionBO bo = new ResetProportionBO();
//        bo.setSkuCode("W100021815");
//        bo.setOldCostPrice(new BigDecimal("0"));
//        bo.setNewCostPrice(new BigDecimal("0"));
//        bo.setOldSalesPrice(new BigDecimal("112.000000"));
//        bo.setNewSalesPrice(new BigDecimal("0"));
//        List<ResetProportionBO> singleton = Collections.singletonList(bo);
//        combinationItemBizService.resetProportion(singleton);

        CombinationItem byId = iCombinationItemService.getById(146L);
        suiteFetcher.fixComposeSkuProportion(byId);
    }


    @Autowired
    ItemLaunchPlanServiceImpl itemLaunchPlanService;

    @Test
    public void editTextTest() {
        ItemLaunchPlanTextParam param = new ItemLaunchPlanTextParam();
        param.setHomeCopy("999998");
        param.setItemId(363504L);
        param.setPlanId(374L);
        itemLaunchPlanService.editItemText(param);
    }

    @Test
    public void queryTest(){
        CombinationItemPageQuery query = new CombinationItemPageQuery();
        query.setSkuCode("1232421");
        PageResponse<CombinationPageVO> combinationPageVOPageResponse = combinationItemBizService.pageItem(query);
        System.out.println(JsonUtil.toJson(combinationPageVOPageResponse));
    }

    @Test
    public void testThread() throws IOException, InterruptedException {
        CountDownLatch countDownLatch = new CountDownLatch(20);
        Semaphore semaphore = new Semaphore(2);
        for (int i = 0; i < 20; i++) {
            int finalI = i;
            com.daddylab.supplier.item.common.util.ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
                try {
                    semaphore.acquire();
                    String name = Thread.currentThread().getName();
                    TimeUnit.SECONDS.sleep(2);
                    System.out.println(name + ",id:" + finalI + " finish");
                }catch (Exception e){
                    e.printStackTrace();
                }finally {
                    semaphore.release();
                    countDownLatch.countDown();
                }
            });
        }
        System.out.println("提前错误触发");

        countDownLatch.await();
        System.out.println("全部处理完毕");
        System.in.read();

    }
}
