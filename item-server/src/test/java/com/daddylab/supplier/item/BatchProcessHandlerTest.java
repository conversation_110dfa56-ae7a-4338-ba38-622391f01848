//package com.daddylab.supplier.item;
//
//import com.daddylab.supplier.item.controller.item.dto.ItemPageQuery;
//import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemMapper;
//import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
//import com.daddylab.supplier.item.types.itemBatchProc.ModifyProviderCmd;
//import org.junit.jupiter.api.Test;
//import org.springframework.batch.core.Job;
//import org.springframework.batch.core.JobParameters;
//import org.springframework.batch.core.JobParametersBuilder;
//import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
//import org.springframework.batch.core.launch.JobLauncher;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR> up
// * @date 2023年12月26日 4:52 PM
// */
//@SpringBootTest(properties = "spring.profiles.active:test")
//@EnableBatchProcessing
//public class BatchProcessHandlerTest {
//
//    @Resource
//    private JobLauncher jobLauncher;
//
//    @Autowired
//    @Qualifier(value = "providerJob0")
//    Job providerJob0;
//
//    @Autowired
//    ItemMapper itemMapper;
//
//    @Test
//    public void demo() throws Exception {
//        ModifyProviderCmd cmd = new ModifyProviderCmd();
//        ItemPageQuery itemPageQuery = new ItemPageQuery();
//        itemPageQuery.setItemId(363604L);
//        itemPageQuery.setBusinessLine(null);
//        itemPageQuery.setPageSize(99);
//        itemPageQuery.setOffsetVal(0L);
//        cmd.setQuery(itemPageQuery);
//        cmd.setProviderId(0L);
//
//        JobParameters jobParameters = new JobParametersBuilder()
//                .addString("param", JsonUtil.toJson(cmd))
//                .toJobParameters();
//        jobLauncher.run(providerJob0, jobParameters);
//    }
//
//}
