package com.daddylab.supplier.item.application.tmpJob;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @since 2024/3/20
 */
@SpringBootTest
class ModifyCategory240320JobTest {

    @Autowired
    private ModifyCategory240320Job modifyCategory240320Job;

    @Test
    void handle() throws IOException {

        modifyCategory240320Job.handle(Files.newInputStream(Paths.get("/Users/<USER>/Desktop/ERP/ERP数据处理/商品类目整理/erp商品归类test.xlsx")));

    }
}