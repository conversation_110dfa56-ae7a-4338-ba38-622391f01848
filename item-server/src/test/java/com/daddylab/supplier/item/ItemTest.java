package com.daddylab.supplier.item;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.framework.TestConfigForMail;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.item.ItemController;
import com.daddylab.supplier.item.controller.item.dto.*;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailPriceVo;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailRunningVo;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailVo;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemCmd;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.service.ItemDomainService;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemBaseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuAttrRefDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemAttr;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemImageType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemPriceType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICategoryService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemAttrService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemResp;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11 2:08 下午
 * @description
 */
@SpringBootTest(properties = {
        "spring.profiles.active:local",
        "logging.level.com.alibaba.cloud.nacos:debug"
})
@Import(TestConfigForMail.class)
public class ItemTest {

    @Autowired
    ItemBizService itemBizService;

    @Autowired
    ItemMapper itemMapper;

    @Autowired
    ItemGateway itemGateway;

    @Autowired
    private ItemDomainService itemDomainService;

    @Autowired
    ItemController itemController;

    @Autowired
    PartnerFeignClient partnerFeignClient;

    @Autowired
    private IItemService itemService;

    @Autowired
    private PlatformItemMapper platformItemMapper;

    @Autowired
    private ICategoryService categoryService;

    @Test
    public void test_platformItem() {
        PlatformItem platformItem = platformItemMapper.selectById(1);
        System.out.println("-------");
    }

    @Test
    public void test_updateLaunchStatus() {
        boolean update = itemService.updateLaunchStatusById(10267L, ItemLaunchStatus.TO_BE_DESIGNED);
        System.out.println(update);
    }

    @Test
    public void updateSKuTest(){
        ItemAttrDto itemAttrDto = new ItemAttrDto();
        itemAttrDto.setItemAttrDbId(2185L);
        itemAttrDto.setValue("M");
        itemGateway.updateSkuAttr(itemAttrDto);
    }




    @Test
    @DisplayName("查看运营信息")
    public void testRunning() {
        final SingleResponse<ItemDetailRunningVo> itemDetailRunningVoSingleResponse = itemController.viewRunning(121L);
        System.out.println(JsonUtil.toJson(itemDetailRunningVoSingleResponse));
    }

    @Test
    @DisplayName("编辑运营信息")
    public void testUpdateRunning() {
        EditRunningCmd cmd = new EditRunningCmd();
        cmd.setItemId(480L);
//        cmd.setItemId(127L);
//        cmd.setEstimateSaleTime(0L);
//        cmd.setRunningImageList(Lists.newArrayList());
//
//        RunningDto dto = new RunningDto();
//        dto.setIsHead(0L);
//        RunnerDto runner = new RunnerDto();
//        runner.setUserId(12132131L);
//        runner.setName("测试");
//        dto.setOperatorIdList(Collections.singletonList(runner));
//        dto.setCustomName("god");
//
//        cmd.setRunnerList(Collections.singletonList(dto));

        itemController.saveRunning(cmd);
    }


    @Autowired
    IItemAttrService iItemAttrService;

    @Test
    @DisplayName("商品采购员下拉选")
    public void test1() {
        ItemAttr itemAttr = new ItemAttr();
        itemAttr.setId(2510L);
        itemAttr.setAttrValue("黑色111");
        iItemAttrService.updateById(itemAttr);
    }

//    @Test
//    @DisplayName("商品名称下拉选")
//    public void test2() {
//        final MultiResponse<NameDropDownVo> nameDropDownVoMultiResponse = itemBizService.nameDropDownList("儿童", 0, 20);
//        System.out.println(JsonUtil.toJson(nameDropDownVoMultiResponse));
//    }

    @Test
    @DisplayName("商品分页查询")
    public void test3() {
        ItemPageQuery pageQuery = new ItemPageQuery();
        pageQuery.setPageIndex(1);
        pageQuery.setPageSize(10);
        pageQuery.setCode("100136");
//        pageQuery.setBrandId(8L);
//        pageQuery.setBuyerUserId(49419L);
        final PageResponse<ItemPageVo> itemPageVoPageResponse = itemController.pageQuery(pageQuery);
        System.out.println(JsonUtil.objToStr(itemPageVoPageResponse));
    }

    @Test
    @DisplayName("查询商品详情-基础信息部分")
    public void test4() {
//        final ItemBaseDo itemBaseDo = itemMapper.selectDetailBase(13L);
    }


    @SneakyThrows
    @Test
    @DisplayName("构建商品")
    public void test5() {
        SaveItemCmd saveCmd = new SaveItemCmd();
        saveCmd.setItemName("测试商品12电饭锅第是的撒范德水电费大萨达撒地方大师范生方式方法多少多少非法所得第三方第三方水电费水电费大幅度发方法是对方是打发第三方大幅度萨发放到撒旦法范德萨发生发斯蒂芬顺丰到付对方到底是大大大大三个故事梗概防守打法广告费申达股份刚发的广泛地时光飞逝广东省更广泛的规范的广泛的闪光点时光飞逝大股东股份的的非官方打广告");
        saveCmd.setBrandId(8L);
        saveCmd.setCategoryId(1L);
//        saveCmd.setSpecifiedCode("测试供货商制定编码0122");
//        saveCmd.setItemId(22L);

        List<ItemImageDto> imageList = new LinkedList<>();
        ItemImageDto dto = new ItemImageDto();
        dto.setImageUrl("www.baidu.com");
        dto.setIsMain(1);
//        dto.setSort(1);
        dto.setType(1);
        imageList.add(dto);
        saveCmd.setImageList(imageList);

        List<ItemSkuListDto> itemSkuListDtos = new LinkedList<>();
        ItemSkuListDto itemSkuListDto = new ItemSkuListDto();
        itemSkuListDto.setId(11L);
        itemSkuListDto.setProcurement("199.00");
        itemSkuListDto.setSales("200.00");
//        itemSkuListDto.setSpecifiedSkuCode("特工商制定sku001");

//        itemSkuListDto.setSkuCode("系统skuCode");
//
        List<ItemAttrDto> list = new LinkedList<>();
        ItemAttrDto itemAttrDto = new ItemAttrDto();
        itemAttrDto.setId(1L);
        itemAttrDto.setValue("10m");
        ItemAttrDto itemAttrDto2 = new ItemAttrDto();
        itemAttrDto2.setId(2L);
        itemAttrDto2.setValue("12m");
        list.add(itemAttrDto);
        list.add(itemAttrDto2);
        itemSkuListDto.setAttrList(list);
        itemSkuListDtos.add(itemSkuListDto);
        saveCmd.setSkuList(itemSkuListDtos);

        saveCmd.setProcurementPrices(createProcurementPrices());
//
        saveCmd.setSalesPrices(createSalesPrices());

        saveCmd.setProviderId(1L);
        saveCmd.setBuyerUserId(2L);
//        saveCmd.setBuyerUserName("测试名字");
        saveCmd.setIsGift(0);
        saveCmd.setDelivery(Arrays.asList(1, 2));
//        saveCmd.setExpresses(expresses());

        itemController.saveBase(saveCmd);

//        itemController.addItem(saveCmd);
//        itemController.addWithPrice(saveCmd);
    }

    private List<ExpressDto> expresses() {
        List<ExpressDto> expressDtos = new LinkedList<>();

        ExpressDto expressDto = new ExpressDto();
        expressDto.setFromArea("新疆");
        expressDto.setExpressCompany("顺丰");
        expressDto.setFreeArea("江浙沪");
        expressDto.setChargeArea("内蒙");
        expressDto.setRemark("测试");
        expressDtos.add(expressDto);

        return expressDtos;
    }

    private List<ProcurementPriceDto> createProcurementPrices() {
        List<ProcurementPriceDto> procurementPrices = new LinkedList<>();
        ProcurementPriceDto procurementPriceDto = new ProcurementPriceDto();
        procurementPriceDto.setName("采购价格");
        procurementPriceDto.setType(ItemPriceType.PROCUREMENT.getValue());
        procurementPriceDto.setRemark("采购价格备注测试");
        procurementPriceDto.setVal("90.00");
        procurementPriceDto.setIsLongTerm(1);
        procurementPriceDto.setStartTime(1634881878L);
        procurementPriceDto.setEndTime(1637560278L);
        procurementPrices.add(procurementPriceDto);
        return procurementPrices;
    }

    private List<SalesPriceDto> createSalesPrices() {
        List<SalesPriceDto> salesPrices = new LinkedList<>();

        SalesPriceDto salesPriceDto = new SalesPriceDto();
        salesPriceDto.setName("日常售价1");
        salesPriceDto.setType(ItemPriceType.DAILY.getValue());
        salesPriceDto.setVal("10.00");
        salesPrices.add(salesPriceDto);

        SalesPriceDto salesPriceDto2 = new SalesPriceDto();
        salesPriceDto2.setName("划线价格");
        salesPriceDto2.setType(ItemPriceType.LINE.getValue());
        salesPriceDto2.setVal("12.00");
        salesPrices.add(salesPriceDto2);

        SalesPriceDto salesPriceDto3 = new SalesPriceDto();
        salesPriceDto3.setName("渠道价");
        salesPriceDto3.setType(ItemPriceType.CHANNEL_LOWEST.getValue());
        salesPriceDto3.setVal("13.00");
        salesPrices.add(salesPriceDto3);

        SalesPriceDto salesPriceDto4 = new SalesPriceDto();
        salesPriceDto4.setName("活动价");
        salesPriceDto4.setType(ItemPriceType.ACTIVITY.getValue());
        salesPriceDto4.setVal("14.00");
        salesPrices.add(salesPriceDto4);

        SalesPriceDto salesPriceDto5 = new SalesPriceDto();
        salesPriceDto5.setName("自定义动价");
        salesPriceDto5.setType(ItemPriceType.CUSTOMIZE.getValue());
        salesPriceDto5.setVal("16.00");
        salesPrices.add(salesPriceDto5);

        SalesPriceDto salesPriceDto6 = new SalesPriceDto();
        salesPriceDto6.setName("自定义动价22");
        salesPriceDto6.setType(ItemPriceType.CUSTOMIZE.getValue());
        salesPriceDto6.setVal("16.00");
        salesPrices.add(salesPriceDto6);

        return salesPrices;
    }

    @Test
    @DisplayName("查看商品")
    public void test6() {
        final SingleResponse<ItemDetailVo> view = itemController.viewBase(576L);
        System.out.println(JsonUtil.objToStr(view));
    }

    @Test
    @DisplayName("查看商品价格")
    public void test9() {
        final SingleResponse<ItemDetailPriceVo> itemDetailPriceVoSingleResponse = itemController.viewPrice(541L);
        System.out.println(JsonUtil.toJson(itemDetailPriceVoSingleResponse.getData()));
    }

    @Test
    @DisplayName("编辑商品价格")
    public void test10() {
//        itemController.updatePrice();
    }


    @Test
    @DisplayName("查询商品基础信息")
    public void test7() {
        final ItemBaseDO itemBaseDo = itemMapper.queryDetailBase(576L);
        System.out.println(JsonUtil.toJson(itemBaseDo));
    }

    @Test
    @DisplayName("查询商品sku")
    public void test8() {
        final List<ItemSku> skuList = itemGateway.getSkuList(22L);
        final List<Long> skuIdList = skuList.stream().map(ItemSku::getId).collect(Collectors.toList());
        final List<SkuAttrRefDO> skuAttrRefList = itemGateway.getSkuAttrList(skuIdList);
        System.out.println(JsonUtil.objToStr(skuAttrRefList));
    }


    @Test
    @DisplayName("测试图片Dto->Db")
    public void test12() {
//        List<ItemImageDto> imageList = new LinkedList<>();
//        ItemImageDto dto = new ItemImageDto();
//        dto.setImageUrl("www.baidu.com");
//        dto.setIsMain(1);
////        dto.setSort(1);
//        dto.setType(1);
//        dto.setItemId(101L);
//        imageList.add(dto);
//
//        final List<ItemImage> itemImages = ItemTransMapper.INSTANCE.imageDtoToImageDbs(imageList);
//        System.out.println(JsonUtil.objToStr(itemImages));

        final ItemImageType enumByValue = IEnum.getEnumByValue(ItemImageType.class, 2);
        System.out.println(JsonUtil.toJson(enumByValue));
    }

    @Test
    @DisplayName("编辑商品价格")
    public void test13() {
        EditPriceCmd cm = new EditPriceCmd();
        cm.setItemId(22L);
        cm.setProcurementPrices(createProcurementPrices());
        cm.setSalesPrices(createSalesPrices());

//        itemController.updatePrice(cm);


//        final List<ProcurementPriceDto> procurementPrices = createProcurementPrices();
//        for (ProcurementPriceDto procurementPrice : procurementPrices) {
//            System.out.println(ItemTransMapper.INSTANCE.procurementPricesDtoToDb(procurementPrice));
//        }

    }


    @Test
    @DisplayName("测试老爸会员合作伙伴系统")
    public void test14() {
        PartnerItemCmd cmd = new PartnerItemCmd();
        cmd.setSearchType(2);
        cmd.setContext("测试");
        cmd.setPageIndex(1);
        cmd.setPageSize(10);
        final List<PartnerItemResp> partnerItemRespList = itemGateway.partnerQuery(cmd);

//        final MultiResponse<PartnerItemVo> partnerItemVoMultiResponse = itemController.queryPartnerItem(cmd);


//        final PartnerItemReq partnerItemReq = ItemTransMapper.INSTANCE.partnerCmdToReq(cmd);
//
//        final Rsp<List<PartnerItemResp>> listRsp = partnerFeignClient.itemQuery(partnerItemReq);
        System.out.println(JsonUtil.objToStr(partnerItemRespList));

    }

    @Test
    @DisplayName("导出测试")
    public void test15() throws IOException {
        ExportCmd cmd = new ExportCmd();
        cmd.setLatitude("item");
        final Response response = itemController.exportItem(cmd);
        System.in.read();
    }

    @Test
    @DisplayName("金蝶规格测试")
    public void test() {
        final List<SkuAttrRefDO> skuAttrList = itemGateway.getSkuAttrList(Collections.singletonList(772L));
        List<String> list = new LinkedList<>();
        for (SkuAttrRefDO skuAttrRefDo : skuAttrList) {
            list.add(skuAttrRefDo.getAttrName() + ":" + skuAttrRefDo.getAttrValue());
        }
        System.out.println(StringUtils.join(list, ";"));
    }
//
//    @Test
//    @DisplayName("其他价格查询测试")
//    public void otherPrice() {
//        final ExportItemOtherDto exportItemOtherDto = itemMapper.queryOtherItemExportDto(181L);
//        System.out.println(JsonUtil.toJson(exportItemOtherDto));
//    }


    @Test
    @DisplayName("商品列表查询测试")
    public void test16() {
        ItemPageQuery query = new ItemPageQuery();
        query.setItemName("测试");
        final PageResponse<ItemPageVo> itemPageVoPageResponse = itemController.pageQuery(query);
        System.out.println(JsonUtil.objToStr(itemPageVoPageResponse));
    }

    @Test
    @DisplayName("创建临时文件")
    public void test17() throws IOException {
        for (int i = 0; i < 5; i++) {
            ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
                Path temp = null;
                try {
                    temp = Files.createTempFile("temp", ".xlsx");
                    System.out.println(temp.getFileName());
                } catch (IOException e) {
                    e.printStackTrace();
                } finally {
                    try {
                        Files.deleteIfExists(temp);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            });
        }
        System.in.read();

    }


//    @Autowired
//    KingDeeGateway kingDeeGateway;

//    @Test
//    @DisplayName("同步sku到金蝶")
//    public void addKingDee() {
//        final Item item = itemGateway.getItem(45L);
//        final List<KingDeeSkuDto> kingDeeItemDtoList = itemDomainService.buildKingDeeItemDto(item, null);
//        for (KingDeeSkuDto kingDeeSkuDto : kingDeeItemDtoList) {
//            final String skuKingDeeId = kingDeeGateway.saveSku(kingDeeSkuDto);
//            itemGateway.fillingSkuKingDeeId(kingDeeSkuDto.getSkuId(), skuKingDeeId);
//        }
//    }

    @Test
    public void testProvider() {

    }

    @Test
    public void testViewRunning() {
        final SingleResponse<ItemDetailRunningVo> itemDetailRunningVoSingleResponse = itemController.viewRunning(57L);
        System.out.println(JsonUtil.toJson(itemDetailRunningVoSingleResponse));
    }

    @Test
    @DisplayName("item简易查询")
    public void testSimpleQuery() {
        ItemSimpleViewCmd cmd = new ItemSimpleViewCmd();
        cmd.setType(3);
        cmd.setQuery("69");
        final MultiResponse<ItemSimpleViewVo> itemSimpleViewVoMultiResponse = itemController.simpleQuery(cmd);
        System.out.println(JsonUtil.toJson(itemSimpleViewVoMultiResponse));
    }

    @Test
    @DisplayName("测试sku")
    public void testSKu() throws Exception {
        SaveItemCmd saveCmd = new SaveItemCmd();

        List<ItemSkuListDto> itemSkuListDtos = new LinkedList<>();
        ItemSkuListDto itemSkuListDto = new ItemSkuListDto();
        itemSkuListDto.setProcurement("199.00");
        itemSkuListDto.setSales("200.00");
        itemSkuListDto.setSpecifiedSkuCode("特工商制定sku");
        itemSkuListDto.setId(22L);

//        itemSkuListDto.setSkuCode("系统skuCode");
//
        List<ItemAttrDto> list = new LinkedList<>();
        ItemAttrDto itemAttrDto = new ItemAttrDto();
        itemAttrDto.setId(10L);
        itemAttrDto.setValue("10m");
        ItemAttrDto itemAttrDto2 = new ItemAttrDto();
        itemAttrDto2.setId(2L);
        itemAttrDto2.setValue("12m");
        list.add(itemAttrDto);
        list.add(itemAttrDto2);
        itemSkuListDto.setAttrList(list);
        itemSkuListDtos.add(itemSkuListDto);
        saveCmd.setSkuList(itemSkuListDtos);

        itemController.saveBase(saveCmd);

    }

    @Test
    @DisplayName("导出列表测试")
    public void testExportList() {
        for (int i = 0; i < 5; i++) {
            final Long demo = RedisUtil.increment("demo2", 1);
            System.out.println(demo);
        }

    }

//    @Test
//    @DisplayName("编辑商品，测试消息发送")
//    @SneakyThrows
//    public void testEditItem(){
//        SaveItemCmd cmd = new SaveItemCmd();
//        cmd.setItemId(480L);
//        cmd.setItemName("编辑商品，测试消息发送");
//        itemController.saveBase(cmd);
//        System.in.read();
//    }

    @Test
    public void test_itemSync() {
        List<ItemSyncMiniProgramParam> params = new ArrayList<>();
        ItemSyncMiniProgramParam param = new ItemSyncMiniProgramParam();
        param.setItemNo("DE1000002");
        param.setMiniItemIds(Arrays.asList(1L, 2L, 3L));
        params.add(param);

        itemBizService.itemSyncMiniProgram(params);
    }

    @Test
    public void test_itemSync_match() {
        ItemMatchMiniProgramParam param = new ItemMatchMiniProgramParam();
        ItemMatchMiniProgramParam.ItemMatchMiniProgramInternalParam internalParam = new ItemMatchMiniProgramParam.ItemMatchMiniProgramInternalParam();
        internalParam.setItemNo("DE1000002");
        internalParam.setItemStandardName("MAO游园惊梦9色眼影盘（E01游园邂逅盘）");
        internalParam.setSkuNos(Arrays.asList("DE1000002001564"));
        param.setParams(Arrays.asList(internalParam));
        itemBizService.itemMatchMiniProgram(param);
    }

    @Test
    public void test_batchItemQuery() {
        Map<String, Item> noToItemMap = itemService.getNoToItemMap(Arrays.asList("wangdiantong", "space_vegetables"));
        System.out.println("--------");
    }

    @Test
    public void test_categoryList() {
        List<String> list = categoryService.getCategoryNames("DE1000002");
        System.out.println(list);
    }

    public static void main(String[] args) {
        List<String> list1 = new ArrayList<>();
        System.out.println(list1.get(0));
    }
}
