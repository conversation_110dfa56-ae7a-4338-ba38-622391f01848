package com.daddylab.supplier.item;

import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.config.ThreadConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.itemStock.WarehouseStockGatewayWdtApiImpl;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@SpringBootTest(properties = {
        "spring.profiles.active=test",
        "logging.level.WdtAPILogProxy=debug"
})
@Slf4j
public class WarehouseStockGatewayTest {

    public static final List<String> SKU_CODES = Arrays.asList(
            "30500033",
            "all",
            "12345",
            "daba1",
            "sjdfs",
            "scsmb",
            "110101101001",
            "110201100101",
            "wttAABCD",
            "wttAABCDE",
            "wtt",
            "6910001212358",
            "160001",
            "SP001-01",
            "XXDSNTEST",
            "6921734974021",
            "6921734974022",
            "ceshibaozhuang2",
            "6921734974023",
            "1308728608223055960",
            "1280346859126472739",
            "1309009033306755123",
            "1309010047313301529",
            "GC2231",
            "GC2232",
            "GC2233",
            "GC2234",
            "GC2235",
            "GC2236",
            "GC1231",
            "GC1232",
            "GC1233",
            "GC1234",
            "GC1235",
            "GC1236",
            "202009210259001",
            "202009210259002",
            "GC123456",
            "GC234567",
            "GC8999",
            "GC11000",
            "GC22000",
            "1324188193230667863",
            "202011193",
            "1330746827439874101",
            "ceshibaozhuang",
            "1331141034524549212",
            "lz11",
            "1.01.001.0001",
            "1340945991696162895",
            "6913225488797",
            "BC00000101",
            "BC00000102",
            "1331133815447887935",
            "10020006",
            "10019204",
            "SSURAC006080F",
            "10020904",
            "te10026001",
            "te10026402",
            "te10026401",
            "te10027002",
            "te10027201",
            "te10027202",
            "te10027301",
            "te10027302",
            "wttwtt",
            "10020901",
            "10020902",
            "10020903",
            "te10023901",
            "te10025701",
            "te10025702",
            "te10025703",
            "te10025704",
            "te10025901",
            "te10027401",
            "recook:68",
            "recook:36",
            "1100000016",
            "snceshi001",
            "BM001",
            "6948043439163",
            "hcf3",
            "E160",
            "test20210913",
            "100700903",
            "fkc",
            "506010500002034",
            "6956745102896",
            "6956745102889",
            "wjw555",
            "001010004",
            "0020028",
            "0050029",
            "0050033",
            "0421",
            "te100271011",
            "6971716393035",
            "wangdiantong",
            "08070103",
            "potato",
            "111",
            "500",
            "400",
            "2541",
            "3344",
            "te10026501",
            "te10027501",
            "testGoods002_spec_001",
            "xiaowanzi01",
            "TE1000009000556",
            "hcf",
            "TE1000055009552",
            "PT0005421",
            "1000055009555",
            "1000054008763"
    );

    @Autowired
    private WdtGateway wdtGateway;
    @Autowired
    private ItemSkuGateway itemSkuGateway;

    @Test
    @DisplayName("测试获取SKU总库存")
    public void testGetSkuStockSum() throws InterruptedException {
        final ThreadConfig threadConfig = new ThreadConfig();
        final ThreadPoolTaskScheduler taskScheduler = threadConfig.taskScheduler();
        final ThreadPoolTaskExecutor taskExecutor = threadConfig.commonExecutor();
        final WarehouseStockGatewayWdtApiImpl warehouseStockGateway = new WarehouseStockGatewayWdtApiImpl(
                wdtGateway, itemSkuGateway, taskScheduler, taskExecutor);

        final ExecutorService executorService = Executors.newFixedThreadPool(100);
        for (String skuCode : SKU_CODES) {
            executorService.execute(() -> {
                final Long subStartTimeMillis = DateUtil.currentTimeMillis();
                log.info("开始查询 {} 开始时间:{}", skuCode, subStartTimeMillis);
                BigDecimal stockSum;
                try {
                    stockSum = warehouseStockGateway.getStockSum(skuCode);
                } catch (Exception e) {
                    log.error("查询异常 {}", skuCode, e);
                    return;
                }
                final Long subEndTimeMillis = DateUtil.currentTimeMillis();
                log.info("查询完成 {} 开始时间:{} 结束时间:{} 耗时:{} 查询结果:{}", skuCode, subStartTimeMillis,
                        subEndTimeMillis, subEndTimeMillis - subStartTimeMillis, stockSum);
            });
        }
        executorService.shutdown();
        while (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
            log.info("等待线程池关闭");
        }
    }
}
