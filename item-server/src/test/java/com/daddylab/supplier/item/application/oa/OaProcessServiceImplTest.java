package com.daddylab.supplier.item.application.oa;

import com.daddylab.supplier.item.infrastructure.oa.*;
import com.daddylab.supplier.item.infrastructure.oa.cgfk.OaCgfkData;
import com.daddylab.supplier.item.infrastructure.oa.cgfk.OaCgfkDetail;
import com.daddylab.supplier.item.infrastructure.oa.cgfk.OaCgfkInvoice;
import com.daddylab.supplier.item.infrastructure.oa.cgfk.OaCgfkMainForm;
import com.daddylab.supplier.item.infrastructure.oa.constants.PaymentPurpose;
import com.daddylab.supplier.item.infrastructure.oa.constants.ProcurementType;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.google.common.collect.Lists;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration;
import org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/27
 */
@EnableFeignClients(clients = OaClient.class)
@SpringBootTest(
        classes = {
            OaClient.class,
            FeignAutoConfiguration.class,
            WebMvcAutoConfiguration.class,
            JacksonAutoConfiguration.class,
            HttpMessageConvertersAutoConfiguration.class,
        },
        properties = {
            "spring.cloud.nacos.config.enabled=false",
            "feign.hystrix.enabled=false",
            "spring.profiles.active=test",
            "feign.client.config.default.loggerLevel=FULL",
        })
class OaProcessServiceImplTest {
    @Autowired OaClient oaClient;

    private OaUploadAttachmentResponse uploadAttachment(String url) throws IOException {
        final OaProcessServiceImpl oaProcessService = new OaProcessServiceImpl(oaClient);
        return oaProcessService.uploadAttachment("chengzu.wu", url);
    }

    @Test
    public void cgfkProcessStart() throws IOException {
        final OaProcessServiceImpl oaProcessService = new OaProcessServiceImpl(oaClient);
        final OaCgfkData formData = new OaCgfkData();
        final OaCgfkMainForm mainForm = new OaCgfkMainForm();
        mainForm.setBelongCompany("老爸评测");
        mainForm.setPayer("老爸评测ERP");
        mainForm.setProcessNo("TESTCGFK1219001");
        mainForm.setPayee("老爸评测IT");
        mainForm.setPayeeBank("招商银行股份有限公司杭州分行营业部");
        mainForm.setPayeeBankAccount("****************");
        mainForm.setProcurementType(ProcurementType.STANDARD_PROCUREMENT);
        mainForm.setBuyer("徵乌");
        mainForm.setNatureOfPayment("货款");
        mainForm.setRemark("备注备注");
        mainForm.setPaymentPurpose(PaymentPurpose.PROCUREMENT_PAYMENT);
        mainForm.setPaymentRatio(new BigDecimal("0.8"));
        mainForm.setTotalRequestedPaymentAmount(new BigDecimal("100"));
        mainForm.setDeductionsFromOtherAmounts(new BigDecimal("0"));
        mainForm.setEnv("test");
        mainForm.setCurrentNode(0);

        formData.setMainForm(mainForm);
        final ArrayList<OaCgfkDetail> details = Lists.newArrayList();
        final OaCgfkDetail detail1 = new OaCgfkDetail();
        detail1.setPurchaseOrderNo("TESTCGFK1219001");
        detail1.setPayableAmount(new BigDecimal("100"));
        detail1.setRequestPaymentAmount(new BigDecimal("100"));
        detail1.setSkuNum(1);
        detail1.setItemNum(1);
        detail1.setPurchaseDetails(
                String.format(
                        "<a href=\"%s\">&ERP付款单详情页链接</a>",
                        "http://p.dlab.cn/erp/operation-management/optimize-item/list?id=" + 1));

        details.add(detail1);
        formData.setDetails(details);
        final ArrayList<OaCgfkInvoice> formInvoices = Lists.newArrayList();
        final OaCgfkInvoice invoice = new OaCgfkInvoice();
        final OaUploadAttachmentResponse oaUploadAttachmentResponse =
                uploadAttachment(
                        "https://cdn-test.daddylab.com/Upload/supplier/item/image/1728974241257566208.jpeg");
        invoice.setBillAttachment(oaUploadAttachmentResponse.getAtts().get(0).getFileUrl());

        formInvoices.add(invoice);
        formData.setInvoices(formInvoices);

        final int[] sort = {0};
        final List<OaFormAttachment> thirdAttachments =
                formInvoices.stream()
                        .map(
                                v -> {
                                    final OaFormAttachment oaFormAttachment =
                                            new OaFormAttachment();
                                    oaFormAttachment.setSubReference(v.getBillAttachment());
                                    oaFormAttachment.setFileUrl(v.getBillAttachment());
                                    oaFormAttachment.setSort(++sort[0]);
                                    return oaFormAttachment;
                                })
                        .collect(Collectors.toList());
        formData.setThirdAttachments(thirdAttachments);

        final OaResponse<OaProcessStartResponse> oaProcessStartResponseOaResponse =
                oaProcessService.processStart(
                        "chengzu.wu",
                        "CGFK_TEST",
                        false,
                        null,
                        Collections.emptyList(),
                        formData);
        System.out.println(JsonUtil.toJson(oaProcessStartResponseOaResponse));
    }
}
