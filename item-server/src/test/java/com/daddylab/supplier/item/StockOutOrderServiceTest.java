package com.daddylab.supplier.item;

import com.alibaba.cola.dto.PageResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuVO;
import com.daddylab.supplier.item.application.stockOutOrder.StockOutOrderBizService;
import com.daddylab.supplier.item.controller.stockout.dto.StockOutOrderVO;
import com.daddylab.supplier.item.domain.stockOutOrder.StockOutOrderQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockOutOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockOutOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockOutOrderDetailService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockOutOrderService;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@ActiveProfiles("test")
class StockOutOrderServiceTest {

    @Autowired
    private StockOutOrderBizService stockOutOrderBizService;

    @Autowired
    private IStockOutOrderService iStockOutOrderService;

    @Autowired
    private IStockOutOrderDetailService iStockOutOrderDetailService;

    @Test
    @DisplayName("出库分页")
    public void page() {
        StockOutOrderQuery stockOutOrderQuery = new StockOutOrderQuery();
        PageResponse<StockOutOrderVO> stockOutOrderPage = stockOutOrderBizService.pageStockOutOrder(stockOutOrderQuery);
        for (StockOutOrderVO stockOutOrderVO : stockOutOrderPage.getData()) {
            System.out.println(stockOutOrderVO);
        }
    }

    @Test
    void getNoByPurchaseOrderId() {
        List<StockOutOrder> stockOutOrderList = stockOutOrderBizService.getStockOutOrderByPurchaseOrderId(2000L);
        System.out.println(stockOutOrderList);
    }

    @Test
    public void test1() {
        // 同步本地
        Long id = 21L;
        StockOutOrder stockOutOrder = iStockOutOrderService.getById(id);
        stockOutOrder.setState(4);

        LambdaQueryWrapper<StockOutOrderDetail> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StockOutOrderDetail::getStockOutOrderId, id);
        List<StockOutOrderDetail> stockOutOrderDetailList = iStockOutOrderDetailService.list(queryWrapper);
        stockOutOrderDetailList.forEach(stockOutOrderDetail -> stockOutOrderDetail.setRealReturnQuantity(10));
        // 出库商品数量
        // 实际出库总数量
        int count = stockOutOrderDetailList.stream().mapToInt(StockOutOrderDetail::getRealReturnQuantity).sum();
        stockOutOrder.setTotalReturnQuantity(count);
        // 同步数量
        for (StockOutOrderDetail stockOutOrderDetail : stockOutOrderDetailList) {
            stockOutOrderDetail.setRealReturnQuantity(stockOutOrderDetail.getRealReturnQuantity());
            stockOutOrderDetail.setValuationQuantity(stockOutOrderDetail.getRealReturnQuantity());
            stockOutOrderDetail.setDeductionQuantity(stockOutOrderDetail.getRealReturnQuantity());
            stockOutOrderDetail.setReplenishQuantity(stockOutOrderDetail.getRealReturnQuantity());
            stockOutOrderDetail.calculatedAmount();
            System.out.println(stockOutOrderDetail);
        }
    }

    @Test
    public void test10() {
        ComposeSkuPageQuery composeSkuPageQuery = new ComposeSkuPageQuery();
        composeSkuPageQuery.setPurchaseOrderId(2026L);
        PageResponse<ComposeSkuVO> composeSkuVOPageResponse = stockOutOrderBizService.pageSku(composeSkuPageQuery);
        for (ComposeSkuVO datum : composeSkuVOPageResponse.getData()) {
            System.out.println("商品:" + datum.getItemName() + "    仓库:" + datum.getWarehouseName() +  "    库存数量: " + datum.getStockCount());
        }
    }

    @Test
    public void test11() {
        stockOutOrderBizService.updateState(528L, 3);
    }
}