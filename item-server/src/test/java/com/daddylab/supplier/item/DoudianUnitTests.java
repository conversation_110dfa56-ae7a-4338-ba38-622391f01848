package com.daddylab.supplier.item;

import com.daddylab.supplier.item.infrastructure.doudian.DoudianConfig;
import com.doudian.open.api.sku_syncStockBatch.SkuSyncStockBatchRequest;
import com.doudian.open.api.sku_syncStockBatch.SkuSyncStockBatchResponse;
import com.doudian.open.api.sku_syncStockBatch.param.SkuSyncListItem;
import com.doudian.open.api.sku_syncStockBatch.param.SkuSyncStockBatchParam;
import com.doudian.open.core.AccessToken;
import com.doudian.open.core.GlobalConfig;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/25
 */
public class DoudianUnitTests {
    @Test
    public void testCreateToken() {
        final DoudianConfig doudianConfig = new DoudianConfig();
        doudianConfig.setAppKey("6938083378398823936");
        doudianConfig.setAppSecret("e6507757-fc17-4ddb-941f-fe88a3715c31");
        doudianConfig.setShopId("6162043");
        doudianConfig.setHttpClientReadTimeout("1000");

        GlobalConfig.initAppKey(doudianConfig.getAppKey());
        GlobalConfig.initAppSecret(doudianConfig.getAppSecret());
        GlobalConfig.initHttpClientReadTimeout(Integer.valueOf(doudianConfig.getHttpClientReadTimeout()));

//        final AccessToken token = AccessTokenBuilder.build(doudianConfig.getShopId());
//        System.out.println(JSON.toJSONString(token));
        SkuSyncStockBatchRequest request = new SkuSyncStockBatchRequest();
        SkuSyncStockBatchParam param = request.getParam();
        param.setProductId(3621848852962910974L);
        param.setIncremental(false);

        List<SkuSyncListItem> skuSyncListItemList = new ArrayList<>();
        SkuSyncListItem skuSyncListItem = new SkuSyncListItem();
        skuSyncListItem.setSkuId(1768480948277280L);
        skuSyncListItem.setSkuType(0L);
        skuSyncListItem.setStockNum(2L);
        skuSyncListItem.setStepStockNum(0L);
        skuSyncListItemList.add(skuSyncListItem);

        SkuSyncListItem skuSyncListItem1 = new SkuSyncListItem();
        skuSyncListItem1.setSkuId(3392340101400066L);
        skuSyncListItem1.setSkuType(0L);
        skuSyncListItem1.setStockNum(2L);
        skuSyncListItem1.setStepStockNum(0L);
        skuSyncListItemList.add(skuSyncListItem1);
        param.setSkuSyncList(skuSyncListItemList);

        String token = "bbdaf79d-8e7c-4e90-8a78-acae558e9204";
        final SkuSyncStockBatchResponse skuSyncStockBatchResponse = request.execute(AccessToken.wrap(token, ""));
        System.out.println(skuSyncStockBatchResponse);
    }
}
