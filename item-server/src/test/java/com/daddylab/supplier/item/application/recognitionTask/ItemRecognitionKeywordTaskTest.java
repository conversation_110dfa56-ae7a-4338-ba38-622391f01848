package com.daddylab.supplier.item.application.recognitionTask;

import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 2022/12/6
 */
@SpringBootTest
class ItemRecognitionKeywordTaskTest {
    @Resource
    ItemRecognitionKeywordTask itemRecognitionKeywordTask;

    @Test
    public void doTask() throws InterruptedException {
        itemRecognitionKeywordTask.doTask();
    }

}