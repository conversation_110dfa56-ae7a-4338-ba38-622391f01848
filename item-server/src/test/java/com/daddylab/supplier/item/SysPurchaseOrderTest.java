package com.daddylab.supplier.item;

import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.PriceEngineManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR> up
 * @date 2022年10月24日 11:41 AM
 */
@SpringBootTest(properties = {
        "spring.profiles.active:test"
})
@Slf4j
public class SysPurchaseOrderTest {

    @Resource
    PriceEngineManager engineManager;

    @DisplayName("控制器测试")
    @Test
    public void testManager(){
        engineManager.start("ddd");
    }


}
