package com.daddylab.supplier.item;

import com.daddylab.supplier.item.infrastructure.userInfoCrypto.Config;
import com.daddylab.supplier.item.infrastructure.userInfoCrypto.UserInfoCryptoImpl;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2022/5/26
 */
public class UserInfoCryptoTest {

    @Test
    public void cryptoTest() {
        final Config config = new Config();
        config.setSecurityKey("0123456789123456");
        config.setIvKey("0123456789123456");
        config.setEncryptMode("AES/CTR/PKCS5Padding");

        final UserInfoCryptoImpl userInfoCrypto = new UserInfoCryptoImpl(config);
        final String clearText = "18072885161";
        final String cipherText = userInfoCrypto.encrypt(clearText);
        final String clearTextDecrypted = userInfoCrypto.decrypt(cipherText);

        System.out.println("cipherText = " + cipherText);
        System.out.println("clearTextDecrypted = " + clearTextDecrypted);

        Assertions.assertNotEquals(cipherText, clearText, "加密失败");
        Assertions.assertEquals(clearText, clearTextDecrypted, "解密后的内容不正确");
    }

}
