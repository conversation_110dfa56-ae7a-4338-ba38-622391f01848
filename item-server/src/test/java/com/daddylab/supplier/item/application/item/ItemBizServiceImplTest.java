package com.daddylab.supplier.item.application.item;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import cn.hutool.core.io.FileUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ItemSyncMiniProgramRequest;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import java.io.File;
import java.nio.charset.Charset;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2022/10/13
 */
@Slf4j
class ItemBizServiceImplTest {

    @Test
    public void testFilterInvalidAttr1() throws JsonProcessingException {
        final ItemBizServiceImpl itemBizService = new ItemBizServiceImpl();
        final List<File> testCases = FileUtil.loopFiles("testFilterInvalidAttr/").stream().sorted(
                Comparator.comparing(File::getName)).collect(Collectors.toList());
        final ObjectMapper objectMapper = new ObjectMapper();
        final ObjectWriter objectWriter = objectMapper.writerWithDefaultPrettyPrinter();
        for (File testCase : testCases) {
            final String testCaseJson = FileUtil.readString(testCase, Charset.defaultCharset());
            final JsonNode jsonNode = objectMapper.readTree(testCaseJson);
            final String caseDescription = jsonNode.get("case").asText();
            final ItemSyncMiniProgramRequest input = objectMapper.readValue(
                    jsonNode.get("input").toString(), ItemSyncMiniProgramRequest.class);
            final ItemSyncMiniProgramRequest output = objectMapper.readValue(
                    Optional.ofNullable(jsonNode.get("output")).filter(v -> !v.isNull())
                            .map(JsonNode::toString)
                            .orElse(jsonNode.get("input").toString()),
                    ItemSyncMiniProgramRequest.class);
            assertNotNull(input);
            itemBizService.filterInvalidAttr(input);
            assertEquals(objectWriter.writeValueAsString(output),
                    objectWriter.writeValueAsString(input),
                    StringUtil.format("过滤结果与预设值不一致 case:{} {}", caseDescription,
                            testCase.getPath()));
            log.info("case pass: {} {}", caseDescription, testCase.getPath());
        }

    }

}