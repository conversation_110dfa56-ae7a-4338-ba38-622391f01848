package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import com.alibaba.cloud.nacos.NacosConfigAutoConfiguration;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.daddylab.supplier.item.application.platformItem.data.PlatformItemInventorySettingType;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.CommonFieldHandler;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.MybatisPlusConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSkuInventorySetting;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @since 2024/8/1
 */
@SpringBootTest(properties = {
        "spring.profiles.active=test",
        "logging.level.com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemSkuInventorySettingMapper=debug"
}, classes = {
        // 加载 Nacos 配置
        NacosConfigAutoConfiguration.class,
        // MybatisPlus 提供的动态数据源配置（可配置多数据源切换）
        DynamicDataSourceAutoConfiguration.class,
        // Spring 数据源自动配置
        DataSourceAutoConfiguration.class,
        // MybatisPlus 自动配置
        MybatisPlusAutoConfiguration.class,
        // MybatisPlus 自定义配置
        MybatisPlusConfig.class,
        // MybatisPlus 自定义字段注入器
        CommonFieldHandler.class,
        // 加载 MybatisPlusJoin 相关配置类
        com.github.yulichang.interceptor.MPJInterceptor.class,
        com.github.yulichang.config.InterceptorConfig.class,
        com.github.yulichang.config.MappingConfig.class,
        com.github.yulichang.toolkit.SpringContentUtils.class,
        // 业务类加载
        PlatformItemSkuInventorySettingMapper.class,
        PlatformItemSkuInventorySettingMapperTest.class,
})
class PlatformItemSkuInventorySettingMapperTest {
    @Autowired
    PlatformItemSkuInventorySettingMapper mapper;

    @Test
    public void test() {

        final ArrayList<PlatformItemSkuInventorySetting> list = new ArrayList<PlatformItemSkuInventorySetting>();
        final PlatformItemSkuInventorySetting setting = new PlatformItemSkuInventorySetting();
        setting.setSkuCode("666");
        setting.setPlatform(Platform.LAOBASHOP);
        setting.setShopNo("K0004");
        setting.setOuterItemId("6666");
        setting.setOuterSkuId("66666");
        setting.setInventoryRatio(100);
        setting.setLockNum(0);
        setting.setSafetyThreshold(-1);
        setting.setType(PlatformItemInventorySettingType.LOCK_INVENTORY);
        setting.setCombinationCode("");

        list.add(setting);
        mapper.saveOrUpdateBatchByOuterId(list);


    }

}