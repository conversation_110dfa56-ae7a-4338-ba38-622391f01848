package com.daddylab.supplier.item;

import static org.mockito.Answers.RETURNS_DEEP_STUBS;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.daddylab.supplier.item.domain.dataFetch.*;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.CommonFieldHandler;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.MybatisPlusConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DataFetch;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.DataFetchMapper;
import java.time.LocalDateTime;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Optional;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * <AUTHOR>
 * @since 2022/4/29
 */
@Slf4j
@SpringBootTest(properties = {
        "spring.profiles.active=dev",
        "spring.datasource.url=jdbc:h2:mem:supplier;DB_CLOSE_DELAY=-1;MODE=MySQL;",
        "spring.datasource.schema=classpath:data_fetch_schema.sql",
}, classes = {
        DataFetchManagerTest.class,
//        Configuration.class,

        DynamicDataSourceAutoConfiguration.class,
        DataSourceAutoConfiguration.class,
        MybatisPlusAutoConfiguration.class,
        MybatisPlusConfig.class,
        CommonFieldHandler.class,
        JdbcTemplateAutoConfiguration.class,
})
@AutoConfigureTestDatabase
@MockitoSettings(strictness = Strictness.LENIENT)
public class DataFetchManagerTest implements ApplicationContextAware {

    public static final String TEST_FETCHER = "testFetcher";
    public static final String EARLIEST_TIME_LIMIT = "2011-01-01 00:00:00";
    public static final int FETCH_SEGMENT_LIMIT_PER_DISPATCH = 100;
    Configs configs;

    ApplicationContext applicationContext;

    @Autowired
    private DataFetchMapper dataFetchMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @BeforeEach
    public void beforeAll() {
        final Configs configs = new Configs();
        final LinkedHashSet<FetcherConfig> fetcherConfigs = new LinkedHashSet<>();
        configs.setFetcherConfigs(fetcherConfigs);
        fetcherConfigs.add(new FetcherConfig()
                .setFetchDataType(FetchDataType.WDT_PLATFORM_GOODS)
                .setFetchSegmentLimitPerDispatch(FETCH_SEGMENT_LIMIT_PER_DISPATCH)
                .setStartLimit(EARLIEST_TIME_LIMIT)
        );
        this.configs = configs;
    }

    @Test
    @DisplayName("测试待拉取段查询方法")
    public void testFetchSegments() {
        final DataFetch dataFetch = new DataFetch();
        dataFetch.setId(1L);
        dataFetch.setDataType(FetchDataType.WDT_ORDER.getValue());
        dataFetch.setFetchPoint(LocalDateTime.now());
        dataFetch.setStatus(0);
        dataFetchMapper.insert(dataFetch);
        final DataFetch dataFetch1 = new DataFetch();
        dataFetch1.setId(2L);
        dataFetch1.setDataType(FetchDataType.WDT_ORDER.getValue());
        dataFetch1.setFetchPoint(LocalDateTime.now().minusHours(1));
        dataFetch1.setStatus(0);
        dataFetchMapper.insert(dataFetch1);
        final List<FetchSegment> waitFetchSegments = dataFetchMapper
                .getWaitFetchSegments(FetchDataType.WDT_ORDER, false, 10, 0, LocalDateTime.now());
        System.out.println(waitFetchSegments);
    }

    private Long getWaitCount() {
        final Long waitCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM data_fetch WHERE data_type = ? AND `status` = ?",
                new Object[]{
                        FetchDataType.WDT_PLATFORM_GOODS.getValue(),
                        FetchStatus.WAIT.getValue()
                }, Long.class);
        return Optional.ofNullable(waitCount).map(v -> v - 1).orElse(0L);
    }

    @NonNull
    private DataFetchManagerImpl getDataFetchManager() {
        final RedissonClient redissonClient = mock(RedissonClient.class, RETURNS_DEEP_STUBS);
        redissonStub(redissonClient);
        return new DataFetchManagerImpl(redissonClient,
                                        dataFetchMapper,
                                        configs,
                                        applicationContext.getBeansOfType(Fetcher.class).values(),
                                        applicationContext.getBeansOfType(FetcherFactory.class).values());
    }

    private void redissonStub(RedissonClient redissonClient) {
        final RLock redissonClientLock = redissonClient.getLock(anyString());
        when(redissonClientLock.tryLock()).thenAnswer(
                invocation -> {
                    log.info("加锁成功");
                    return true;
                });
        doAnswer(invocation -> {
            log.info("解锁成功");
            return null;
        }).when(redissonClientLock).unlock();
    }

//    @org.springframework.context.annotation.Configuration
//    public static class Configuration {
//
//        @Bean(TEST_FETCHER)
//        public Fetcher fetcher() {
//            final Fetcher fetcher = mock(Fetcher.class);
//            when(fetcher.canFetch(any(), any())).thenAnswer(invocation -> CallbackResult.NOT_EMPTY);
//            when(fetcher.fetch(any(), any(), any())).thenReturn(CallbackResult.SUCCESS);
//            when(fetcher.fetchDataType()).thenReturn(FetchDataType.WDT_PLATFORM_GOODS);
//            return fetcher;
//        }
//    }

}
