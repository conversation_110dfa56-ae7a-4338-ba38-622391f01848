package com.daddylab.supplier.item;

import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.batchcall.BatchCallAspect;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffListQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.UserGatewayImpl;
import com.daddylab.supplier.item.infrastructure.timing.Options;
import com.daddylab.supplier.item.infrastructure.timing.Output;
import com.daddylab.supplier.item.infrastructure.timing.StopWatch;
import com.daddylab.supplier.item.infrastructure.timing.TimingAspect;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.aop.AopAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@EnableFeignClients(basePackages = "com.daddylab.supplier.item.infrastructure.gatewayimpl.user")
@SpringBootTest(classes = {UserGatewayImpl.class, FeignAutoConfiguration.class, SpringUtil.class,
        EnableAspectJAutoProxy.class, BatchCallAspect.class, TimingAspect.class, AopAutoConfiguration.class})
@Slf4j
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class BatchCallTest {
    @Autowired
    UserGateway userGateway;

    @Test
    public void test1() {
        final StaffListQuery query = new StaffListQuery();
        query.setPageSize(22);
        query.setPageIndex(1);
        query.setStatus(1);
        final List<StaffInfo> staffInfos = userGateway.queryStaffList(query);

        log.debug("staffInfos = {}", staffInfos);

        final StopWatch totalStopWatch = new StopWatch("查询用户测试", new Options().setOutput(Output.LOG));
        totalStopWatch.start("查询用户");

        ExecutorService service = Executors.newFixedThreadPool(10);
        for (StaffInfo staffInfo : staffInfos) {
            service.execute(
                    () -> {
                        final StopWatch stopWatch = new StopWatch();
                        stopWatch.start();

                        final Long userId = staffInfo.getUserId();
                        try {
                            final StaffInfo staffInfo1 = userGateway.queryStaffInfoById(userId);
                            stopWatch.stop();
                            System.out.println(userId + " 查询时间:" + stopWatch.getTotalTimeMillis() + "ms, staffInfo = " + staffInfo1);
                        } catch (Exception e) {
                            log.error("查询员工信息异常: userId:" + userId + " " + e.getMessage());
                        }
                    });
        }
        service.shutdown();
        while (true) {
            try {
                if (service.awaitTermination(Integer.MAX_VALUE, TimeUnit.SECONDS)) {
                    break;
                }
            } catch (InterruptedException ignored) {
            }
        }
        totalStopWatch.stop();
        System.out.println(totalStopWatch.prettyPrint());
    }
}
