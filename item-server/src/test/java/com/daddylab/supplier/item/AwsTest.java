package com.daddylab.supplier.item;

import cn.hutool.core.collection.CollectionUtil;
import com.actionsoft.bpms.api.OpenApiClient;
import com.actionsoft.bpms.api.common.ApiResponse;
import com.actionsoft.sdk.service.model.TaskQueryModel;
import com.actionsoft.sdk.service.response.BoolResponse;
import com.actionsoft.sdk.service.response.MapResponse;
import com.actionsoft.sdk.service.response.StringResponse;
import com.actionsoft.sdk.service.response.process.ProcessInstResponse;
import com.actionsoft.sdk.service.response.process.TaskCommentModelsGetResponse;
import com.actionsoft.sdk.service.response.task.HisTaskInstsGetResponse;
import com.actionsoft.sdk.service.response.task.TaskInstGetResponse;
import com.actionsoft.sdk.service.response.task.TaskInstsGetResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;

public class AwsTest {

    public static final String  accessKey = "erp";

    public static final String  secret ="@yaJAyq982fu";

    public static final String  apiServer = "http://172.16.12.189:8088/portal/api";

    /**
     * 创建流程实例
     */
    @Test
    public void create(){
        String apiMethod = "process.create";

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("processDefId", "obj_46b3bd164ca74a51a9595f6e39a6e37a");
        map.put("uid", "admin");
        map.put("title","采购入库审批潇洒测试");
        OpenApiClient client = new OpenApiClient(apiServer, accessKey, secret);
        ProcessInstResponse response = client.exec(apiMethod, map, ProcessInstResponse.class);
        System.out.println(JSON.toJSONString(response));
        // 流程ID
        System.out.println("流程ID === " + response.getData().getId());
    }



    /**
     * 绑定数据
     */
    @Test
    public void BoCreate(){
        String apiMethod = "bo.create";
        Map<String, Object> args = new HashMap<>();
        args.put("boName", "BO_EU_APPROVAL");  //BO表名称

        Map<String,Object> recordData = new HashMap<>();
        recordData.put("SQR", "潇洒");
        recordData.put("SQRI", new Date());
        args.put("recordData", recordData);
        args.put("bindId", "7e0b0868-6497-4a40-9556-bfe9209d52b7"); // 流程ID
        args.put("uid", "admin");  //记录创建人
        OpenApiClient client = new OpenApiClient(apiServer, accessKey, secret);
        StringResponse response = client.exec(apiMethod, args, StringResponse.class);
        System.out.println(JSON.toJSONString(response));
    }



    /**
     *  通过ID启动流程
     */

    @Test
    public void start(){
        String apiMethod = "process.start";
        Map<String, Object> args = new HashMap<>();
        args.put("processInstId", "a3273d05-b86e-4209-906b-05b1239a687f");
        OpenApiClient client = new OpenApiClient(apiServer, accessKey, secret);
        ApiResponse response = client.exec(apiMethod, args, ApiResponse.class);
        System.out.println(JSON.toJSONString(response));
    }



    /**
     * 判断流程实例是否已结束
     */
    @Test
    public void processEndCheck(){
        String apiMethod = "process.end.check";
        Map<String, Object> args = new HashMap<>();
        args.put("processInstId", "3a1c8305-8653-41e1-8c80-08ca6ad7f49a");
        OpenApiClient client = new OpenApiClient(apiServer, accessKey, secret);
        BoolResponse r = client.exec(apiMethod, args, BoolResponse.class);
        System.out.println(JSON.toJSONString(r));
    }



    /**
     * 查询任务
     */
    @Test
    public void taskQuery(){
        String apiMethod = "task.query";
        Map<String, Object> args = new HashMap<>();
        TaskQueryModel taskQueryModel = new TaskQueryModel();
        taskQueryModel.setProcessInstId("ee5c31aa-a1a4-4eb6-a86f-cc18803ef977");
        args.put("tqm", taskQueryModel);
        OpenApiClient client = new OpenApiClient(apiServer, accessKey, secret);
        TaskInstsGetResponse r = client.exec(apiMethod, args, TaskInstsGetResponse.class);
        if(CollectionUtil.isNotEmpty(r.getData())){
            System.out.println(JSONObject.toJSONString(r.getData(), SerializerFeature.PrettyFormat));
            System.out.println("taskInstId ============： " + r.getData().get(0).getId());
        }else {
            System.out.println(r.getData());
        }
    }

    /**
     * 查询历史任务
     */
    @Test
    public void taskHistoryQuery(){
        String apiMethod = "task.history.query";
        Map<String, Object> args = new HashMap<>();
        JSONObject json = new JSONObject();
        json.put("processInstId", "ee5c31aa-a1a4-4eb6-a86f-cc18803ef977");
        args.put("tqm", json.toJSONString());
        OpenApiClient client = new OpenApiClient(apiServer, accessKey, secret);
        HisTaskInstsGetResponse r = client.exec(apiMethod, args, HisTaskInstsGetResponse.class);
        System.out.println(JSON.toJSONString(r));
    }


    /**
     * 为任务提交审批留言
     */
    @Test
    public void taskCommit(){
        String apiMethod = "task.comment.commit";
        Map<String, Object> args = new HashMap<>();
        args.put("taskInstId", "1421f1df-e6a4-491e-b81a-22cc27b487ad");
        args.put("user", "ying.he"); //执行人账户
        args.put("actionName", "同意");
        args.put("commentMsg", "通过接口提交");
        args.put("isIgnoreDefaultSetting", false);
        OpenApiClient client = new OpenApiClient(apiServer, accessKey, secret);
        BoolResponse response = client.exec(apiMethod, args, BoolResponse.class);
        System.out.println(JSON.toJSONString(response));
    }


    /**
     * 提交任务
     */
    @Test
    public void taskComplete(){
        String apiMethod = "task.complete";
        Map<String, Object> args = new HashMap<>();
        args.put("taskInstId", "3433707e-f60e-46f3-b120-a4a850eaeadb");
        args.put("uid", "admin"); //一个合法的AWS登录账户名
        OpenApiClient client = new OpenApiClient(apiServer, accessKey, secret);
        MapResponse r = client.exec(apiMethod, args, MapResponse.class);
        System.out.println(JSON.toJSONString(r));
    }



    /**
     * 通过Id获得流程实例的审批留言记录
     */
    @Test
    public void commentsGet(){
        String apiMethod = "process.comments.get";
        Map<String, Object> args = new HashMap<>();
        args.put("processInstId", "bb0658a1-bc08-4a95-b6b8-6aee2ad9aede8");
        OpenApiClient client = new OpenApiClient(apiServer, accessKey, secret);
        TaskCommentModelsGetResponse response = client.exec(apiMethod, args, TaskCommentModelsGetResponse.class);
        System.out.println(JSON.toJSONString(response));
    }


    /**
     * 通过id流程复活，重新激活已结束的流程实例
     */
    @Test
    public void processReactivate(){
        String apiMethod = "process.reactivate";
        Map<String, Object> args = new HashMap<String, Object>();
        args.put("processInstId", "3a1c8305-8653-41e1-8c80-08ca6ad7f49a");
        args.put("targetActivityId", "obj_c9c38a44ef4000011514170022801e56");
        args.put("isClearHistory", true);
        args.put("uid", "admin");
        args.put("targetUID", "潇洒");
        args.put("reactivateReason", "测试回滚");
        OpenApiClient client = new OpenApiClient(apiServer, accessKey, secret);
        TaskInstGetResponse r = client.exec(apiMethod, args, TaskInstGetResponse.class);
        System.out.println(JSON.toJSONString(r));
    }

    /**
     * 流程实例
     */
    @Test
    public void processInstGe(){
        String apiMethod = "process.inst.get";
        Map<String, Object> args = new HashMap<String, Object>();
        args.put("processInstId", "6cbdf15c-0ba2-4dfe-878f-de95aa727b68");
        OpenApiClient client = new OpenApiClient(apiServer, accessKey, secret);
        ProcessInstResponse r = client.exec(apiMethod, args, ProcessInstResponse.class);
        System.out.println(JSON.toJSONString(r));
    }



    /**
     * 通过id挂起流程实例
     */
    @Test
    public void processSuspend() {
        String apiMethod = "process.suspend";
        Map<String, Object> args = new HashMap<>();
        args.put("processInstId", "6cbdf15c-0ba2-4dfe-878f-de95aa727b68");
        OpenApiClient client = new OpenApiClient(apiServer, accessKey, secret);
        BoolResponse r = client.exec(apiMethod, args, BoolResponse.class);
        System.out.println(JSON.toJSONString(r));
    }

    /**
     * 通过id恢复被挂起流程实例
     */
    @Test
    public void processResume() {
        String apiMethod = "process.resume";
        Map<String, Object> args = new HashMap<>();
        args.put("processInstId","6cbdf15c-0ba2-4dfe-878f-de95aa727b68");
        OpenApiClient client = new OpenApiClient(apiServer, accessKey, secret);
        BoolResponse r = client.exec(apiMethod, args, BoolResponse.class);
        System.out.println(JSON.toJSONString(r));
    }


    /**
     * 通过ID终止一个流程
     */
    @Test
    public void processTerminate() {
        String apiMethod = "process.terminate";
        Map<String, Object> args = new HashMap<>();
        args.put("processInstId", "6cbdf15c-0ba2-4dfe-878f-de95aa727b68");
        args.put("uid", "admin");
        OpenApiClient client = new OpenApiClient(apiServer, accessKey, secret);
        ApiResponse r = client.exec(apiMethod, args, ApiResponse.class);
        System.out.println(JSON.toJSONString(r));
    }


    @Test
    public void getName(){
        String apiMethod = "org.user.names.get.id";
        Map<String, Object> args = new HashMap<String, Object>();
        args.put("uids", "xiaosa.wsw");
        OpenApiClient client = new OpenApiClient(apiServer, accessKey, secret);
        StringResponse r = client.exec(apiMethod, args, StringResponse.class);
        System.out.println(r.getData());
    }
}