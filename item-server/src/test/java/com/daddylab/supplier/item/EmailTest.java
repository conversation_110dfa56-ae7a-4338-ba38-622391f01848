package com.daddylab.supplier.item;

import cn.hutool.core.date.DateUtil;
import com.daddylab.supplier.item.infrastructure.email.EmailService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mail.javamail.JavaMailSender;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/28 5:21 下午
 * @description
 */
@SpringBootTest(properties = {
        "spring.profiles.active:test"
})
@Slf4j
public class EmailTest {

    @Autowired
    EmailService emailService;

    @Autowired
    JavaMailSender javaMailSender;

    @Test()
    public void testMail() {
//        System.out.println("demo");
        emailService.sendUrlMail("<EMAIL>", "demo" + DateUtil.date().toDateStr(), "demo content", "http://www.baidu.com");
    }
}
