package com.daddylab.supplier.item;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.nacos.NacosConfigAutoConfiguration;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.apiv2.sales.TradeQueryAPI;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailResponse.Order;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailResponse.Order.Detail;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.MybatisPlusConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtOrderDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtOrderMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtGatewayImpl;
import com.daddylab.supplier.item.infrastructure.qimen.wdt.QimenApiFactory;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

/**
 * <AUTHOR>
 * @since 2022/4/19
 */
@Slf4j
@SpringBootTest(properties = {
        "spring.profiles.active=test"
}, classes = {
        NacosConfigAutoConfiguration.class,
        DynamicDataSourceAutoConfiguration.class,
        DataSourceAutoConfiguration.class,
        MybatisPlusAutoConfiguration.class,
        MybatisPlusConfig.class,
        WdtGatewayImpl.class,
        WdtConfig.class,

        //Mapper类注入
        WdtOrderMapper.class,
        WdtOrderDetailMapper.class
})
@EnableConfigurationProperties(WdtConfig.class)
public class WdtOrderSyncTest {

    @Autowired
    WdtGateway wdtGateway;

    @Autowired
    WdtOrderMapper wdtOrderMapper;

    @Autowired
    WdtOrderDetailMapper wdtOrderDetailMapper;

    @MockBean
    QimenApiFactory qimenApiFactory;


    @DisplayName("同步订单")
    @Test
    public void syncOrder() throws WdtErpException {
        final TradeQueryAPI api = wdtGateway.getAPI(TradeQueryAPI.class);
        final Pager pager = new Pager(100, 0, true);
        final LocalDateTime now = LocalDateTime.now();
        LocalDateTime endTime = now;
        LocalDateTime startTime = now.minusHours(1);
        final DateTimeFormatter normDatetimeFormatter = DatePattern.NORM_DATETIME_FORMATTER;
        final DateTimeFormatter toDeliveryDatetimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH-mm");

        while (true) {
            if (startTime.isBefore(parseTime("2021-04-01 00:00:00",
                    normDatetimeFormatter))) {
                break;
            }
            final TradeQueryQueryWithDetailParams params = new TradeQueryQueryWithDetailParams();
            params.setStartTime(normDatetimeFormatter.format(startTime));
            params.setEndTime(normDatetimeFormatter.format(endTime));
            final TradeQueryQueryWithDetailResponse tradeQueryQueryWithDetailResponse = api
                    .queryWithDetail(params, pager);
            if (!tradeQueryQueryWithDetailResponse.getOrder().isEmpty()) {
                for (Order order : tradeQueryQueryWithDetailResponse.getOrder()) {
                    final WdtOrder wdtOrder = new WdtOrder();
                    wdtOrder.setTradeId(order.getTradeId());
                    wdtOrder.setTradeNo(order.getTradeNo());
                    wdtOrder.setPlatformId(order.getPlatformId());
                    wdtOrder.setWarehouseType(order.getWarehouseType());
                    wdtOrder.setSrcTids(order.getSrcTids());
                    wdtOrder.setPayAccount(order.getPayAccount());
                    wdtOrder.setTradeStatus(order.getTradeStatus());
                    wdtOrder.setTradeType(order.getTradeType());
                    wdtOrder.setDeliveryTerm(order.getDeliveryTerm());
                    wdtOrder.setReceiverRing(order.getReceiverRing());
                    wdtOrder.setFreezeReason(order.getFreezeReason());
                    wdtOrder.setRefundStatus(order.getRefundStatus());
                    wdtOrder.setFenxiaoType(order.getFenxiaoType());
                    wdtOrder.setFenxiaoNick(order.getFenxiaoNick());
                    wdtOrder.setTradeTime(
                            parseTime(order.getTradeTime(), normDatetimeFormatter));
                    wdtOrder.setPayTime(parseTime(order.getPayTime(),
                            normDatetimeFormatter));
                    wdtOrder.setConsignTime(
                            parseTime(order.getConsignTime(), normDatetimeFormatter));
                    wdtOrder.setBuyerNick(order.getBuyerNick());
                    wdtOrder.setReceiverName(order.getReceiverName());
                    wdtOrder.setReceiverProvince(order.getReceiverProvince());
                    wdtOrder.setReceiverCity(order.getReceiverCity());
                    wdtOrder.setReceiverDistrict(order.getReceiverDistrict());
                    wdtOrder.setReceiverAddress(order.getReceiverAddress());
                    wdtOrder.setReceiverMobile(order.getReceiverMobile());
                    wdtOrder.setReceiverTelno(order.getReceiverTelno());
                    wdtOrder.setReceiverZip(order.getReceiverZip());
                    wdtOrder.setReceiverArea(order.getReceiverArea());
                    wdtOrder.setReceiverDtb(order.getReceiverDtb());
                    wdtOrder.setToDeliverTime(
                            parseTime(order.getToDeliverTime(), toDeliveryDatetimeFormatter));
                    wdtOrder.setBadReason(order.getBadReason());
                    wdtOrder.setLogisticsNo(order.getLogisticsNo());
                    wdtOrder.setBuyerMessage(order.getBuyerMessage());
                    wdtOrder.setCsRemark(order.getCsRemark());
                    wdtOrder.setRemarkFlag(order.getRemarkFlag());
                    wdtOrder.setPrintRemark(order.getPrintRemark());
                    wdtOrder.setGoodsTypeCount(order.getGoodsTypeCount());
                    wdtOrder.setGoodsCount(order.getGoodsCount());
                    wdtOrder.setGoodsAmount(order.getGoodsAmount());
                    wdtOrder.setPostAmount(order.getPostAmount());
                    wdtOrder.setOtherAmount(order.getOtherAmount());
                    wdtOrder.setDiscount(order.getDiscount());
                    wdtOrder.setReceivable(order.getReceivable());
                    wdtOrder.setCodAmount(order.getCodAmount());
                    wdtOrder.setExtCodFee(order.getExtCodFee());
                    wdtOrder.setGoodsCost(order.getGoodsCost());
                    wdtOrder.setPostCost(order.getPostCost());
                    wdtOrder.setWeight(order.getWeight());
                    wdtOrder.setProfit(order.getProfit());
                    wdtOrder.setTax(order.getTax());
                    wdtOrder.setTaxRate(order.getTaxRate());
                    wdtOrder.setCommission(order.getCommission());
                    wdtOrder.setInvoiceType(order.getInvoiceType());
                    wdtOrder.setInvoiceTitle(order.getInvoiceTitle());
                    wdtOrder.setInvoiceContent(order.getInvoiceContent());
                    wdtOrder.setSalesmanName(order.getSalesmanName());
                    wdtOrder.setCheckerName(order.getCheckerName());
                    wdtOrder.setFcheckerName(order.getFcheckerName());
                    wdtOrder.setCheckouterName(order.getCheckouterName());
                    wdtOrder.setStockoutNo(order.getStockoutNo());
                    wdtOrder.setFlagName(order.getFlagName());
                    wdtOrder.setTradeFrom(order.getTradeFrom());
                    wdtOrder.setSingleSpecNo(order.getSingleSpecNo());
                    wdtOrder.setRawGoodsCount(order.getRawGoodsCount());
                    wdtOrder.setRawGoodsTypeCount(order.getRawGoodsTypeCount());
                    wdtOrder.setCurrency(order.getCurrency());
                    wdtOrder.setInvoiceId(order.getInvoiceId());
                    wdtOrder.setVersionId(order.getVersionId());
                    wdtOrder.setModified(
                            parseTime(order.getModified(), normDatetimeFormatter));
                    wdtOrder.setCreated(parseTime(order.getCreated(),
                            normDatetimeFormatter));
                    wdtOrder.setIdCardType(order.getIdCardType());
                    wdtOrder.setIdCard(order.getIdCard());
                    wdtOrder.setShopNo(order.getShopNo());
                    wdtOrder.setShopName(order.getShopName());
                    wdtOrder.setShopRemark(order.getShopRemark());
                    wdtOrder.setWarehouseNo(order.getWarehouseNo());
                    wdtOrder.setCustomerName(order.getCustomerName());
                    wdtOrder.setCustomerNo(order.getCustomerNo());
                    wdtOrder.setLogisticsName(order.getLogisticsName());
                    wdtOrder.setLogisticsCode(order.getLogisticsCode());
                    wdtOrder.setLogisticsTypeName(order.getLogisticsTypeName());
                    wdtOrder.setDelayToTime(
                            parseTime(order.getDelayToTime(), normDatetimeFormatter));
                    wdtOrder.setTradeLabel(order.getTradeLabel());
                    System.out.println(wdtOrder);
//                    wdtOrderMapper.insert(wdtOrder);

                    for (Detail detail : order.getDetailList()) {
                        final WdtOrderDetail wdtOrderDetail = new WdtOrderDetail();
                        wdtOrderDetail.setTradeId(detail.getTradeId());
                        wdtOrderDetail.setRecId(detail.getRecId());
                        wdtOrderDetail.setPlatformId(detail.getPlatformId());
                        wdtOrderDetail.setSrcOid(detail.getSrcOid());
                        wdtOrderDetail.setSrcTid(detail.getSrcTid());
                        wdtOrderDetail.setGiftType(detail.getGiftType());
                        wdtOrderDetail.setRefundStatus(detail.getRefundStatus());
                        wdtOrderDetail.setGuaranteeMode(detail.getGuaranteeMode());
                        wdtOrderDetail.setPlatformStatus(detail.getPlatformStatus());
                        wdtOrderDetail.setDeliveryTerm(detail.getDeliveryTerm());
                        wdtOrderDetail.setNum(detail.getNum());
                        wdtOrderDetail.setPrice(detail.getPrice());
                        wdtOrderDetail.setRefundNum(detail.getRefundNum());
                        wdtOrderDetail.setOrderPrice(detail.getOrderPrice());
                        wdtOrderDetail.setSharePrice(detail.getSharePrice());
                        wdtOrderDetail.setAdjust(detail.getAdjust());
                        wdtOrderDetail.setDiscount(detail.getDiscount());
                        wdtOrderDetail.setShareAmount(detail.getShareAmount());
                        wdtOrderDetail.setTaxRate(detail.getTaxRate());
                        wdtOrderDetail.setGoodsName(detail.getGoodsName());
                        wdtOrderDetail.setGoodsNo(detail.getGoodsNo());
                        wdtOrderDetail.setSpecName(detail.getSpecName());
                        wdtOrderDetail.setSpecNo(detail.getSpecNo());
                        wdtOrderDetail.setSpecCode(detail.getSpecCode());
                        wdtOrderDetail.setSuiteNo(detail.getSuiteNo());
                        wdtOrderDetail.setSuiteName(detail.getSuiteName());
                        wdtOrderDetail.setSuiteNum(detail.getSuiteNum());
                        wdtOrderDetail.setSuiteAmount(detail.getSuiteAmount());
                        wdtOrderDetail.setSuiteDiscount(detail.getSuiteDiscount());
                        wdtOrderDetail.setApiGoodsName(detail.getApiGoodsName());
                        wdtOrderDetail.setApiSpecName(detail.getApiSpecName());
                        wdtOrderDetail.setApiGoodsId(detail.getApiGoodsId());
                        wdtOrderDetail.setApiSpecId(detail.getApiSpecId());
                        wdtOrderDetail.setCommission(detail.getCommission());
                        wdtOrderDetail.setGoodsType(detail.getGoodsType());
                        wdtOrderDetail.setFromMask(detail.getFromMask());
                        wdtOrderDetail.setRemark(detail.getRemark());
                        wdtOrderDetail.setModified(
                                parseTime(detail.getModified(), normDatetimeFormatter));
                        wdtOrderDetail.setCreated(
                                parseTime(detail.getCreated(), normDatetimeFormatter));
                        wdtOrderDetail.setProp2(detail.getProp2());
                        wdtOrderDetail.setWeight(detail.getWeight());
                        System.out.println(wdtOrderDetail);
//                        wdtOrderDetailMapper.insert(wdtOrderDetail);
                    }

                }
            }
            endTime = endTime.minusHours(1);
            startTime = endTime.minusHours(1);
        }
    }

    private LocalDateTime parseTime(String time, DateTimeFormatter datetimeFormatter) {
        try {
            return StrUtil.isBlank(time) ? null
                    : (time.matches("\\d{13}") ? DateUtil.millisToLocalDateTime(time)
                            : LocalDateTime.parse(time, datetimeFormatter));
        } catch (NumberFormatException | DateTimeParseException e) {
            log.error("日期格式解析出错 日期:{}", time, e);
            return null;
        }
    }

}
