package com.daddylab.supplier.item;

import com.daddylab.supplier.item.domain.itemStock.enums.StockChangeType;
import com.daddylab.supplier.item.domain.itemStock.gateway.ItemStockGateway;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest
@ActiveProfiles("test")
@Slf4j
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class ItemStockTest {

    @Autowired
    private ItemStockGateway itemStockGateway;

    @Test
    @Order(1)
    @DisplayName("新增库存变更日志")
    public void addStockChangeLog() {
        itemStockGateway.addStockChangeLog(1, StockChangeType.STOCK_SET, 1, 0, 1);
    }
}
