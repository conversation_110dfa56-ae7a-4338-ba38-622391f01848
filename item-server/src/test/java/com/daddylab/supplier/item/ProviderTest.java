package com.daddylab.supplier.item;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.daddylab.supplier.item.application.provider.ProviderBizService;
import com.daddylab.supplier.item.controller.provider.ProviderController;
import com.daddylab.supplier.item.controller.provider.dto.*;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IProviderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/28 6:39 下午
 * @description
 */
@SpringBootTest(properties = {
        "spring.profiles.active:test"
})
@RunWith(SpringRunner.class)
@Slf4j
public class ProviderTest {
    @Autowired
    private ProviderBizService providerBizService;

    @Autowired
    private IProviderService iProviderService;

    @Autowired
    private ProviderController providerController;

    @Autowired
    ProviderGateway providerGateway;





    @Test
    @Order(1)
    @DisplayName("伪造一条供应商数据")
    public void test1() {
        String s = "{\"name\":\"国内供应商\",\"unifySocialCreditCodes\":\"32\",\"provinceCode\":\"\",\"cityCode\":\"\",\"areaCode\":\"\",\"type\":1,\"status\":1,\"contact\":\"\",\"contactMobile\":\"\",\"address\":\"\",\"partnerProviderId\":null,\"invoiceType\":1,\"currency\":1,\"taxType\":1,\"taxRate\":0.13,\"bankAccountVOList\":[{\"id\":\"1\",\"bankCard\":\"天地银行\",\"bankDeposit\":\"********\",\"description\":\"描述测试\"}],\"mainChargerUserId\":7734922,\"mainChargerName\":\"孩孩\",\"secondChargerUserId\":7734922,\"secondChargerName\":\"孩孩\",\"id\":14035,\"codeStr\":\"\",\"isFromPartner\":false,\"providerNo\":\"lwl003\"}";
        ProviderCmd cmd = JsonUtil.parse(s, ProviderCmd.class);
        providerBizService.saveOrUpdate(cmd);

    }

    @Test
    @Order(2)
    @DisplayName("供应商单个查询测试")
    public void view() {
//        final Provider provider = providerBizService.queryDetail(3L);
//        if(provider.getStatus().equals(ProviderStatus.COOPERATION)){
//            System.out.println("OOK");
//        }

        final SingleResponse<ProviderVO> view = providerController.view(3L);
        System.out.println(JsonUtil.objToStr(view));
    }


    @Test
    @DisplayName("供应商名字下拉选")
    public void test() {
        ProviderDropDownCmd cmd = new ProviderDropDownCmd();
        cmd.setName("demo");
        cmd.setPageIndex(1);
        cmd.setPageSize(10);
        providerBizService.dropDownList(cmd);
    }



    @Test
    @DisplayName("查看供应商详情")
    public void test4(){
        final SingleResponse<ProviderVO> view = providerController.view(57L);
        System.out.println(JsonUtil.objToStr(view));
    }

    @Test
    @DisplayName("合作伙伴系统查询")
    public void test5(){
        PartnerProviderPageQuery query = new PartnerProviderPageQuery();
        query.setName("军康宝");
        query.setPageIndex(1);
        query.setPageSize(50);
//        final List<PartnerProviderResp> partnerProviderResps = providerGateway.partnerQuery(query);
//        System.out.println(JsonUtil.objToStr(partnerProviderResps));

        final MultiResponse<PartnerProviderVo> partnerProviderVoMultiResponse = providerController.fuzzyQuery(query);
        System.out.println(JsonUtil.objToStr(partnerProviderVoMultiResponse));

    }


    @Test
    @DisplayName("将供应商添加到金蝶")
    public void test6(){
        ProviderCmd cmd = new ProviderCmd();
//        cmd.setId(24L);
//        cmd.setPartnerProviderId("");
        cmd.setName("SysDemo103");
        cmd.setUnifySocialCreditCodes("12312312003232");
        cmd.setType(1);
        cmd.setContact("god father2");
        cmd.setContactMobile("110");
        cmd.setProvinceCode("330100");
        cmd.setCityCode("330100");
        cmd.setAreaCode("330100");
        cmd.setAddress("测试地址");
        cmd.setStatus(1);

        providerController.addOrUpdate(cmd);
        // {"Result":{"ResponseStatus":{"IsSuccess":true,"Errors":[],"SuccessEntitys":[{"Id":2789817,"Number":"kingDeeTest01","DIndex":0}],"SuccessMessages":[],"MsgCode":0}}}


    }

    @Test
    @DisplayName("删除金蝶的供应商")
    public void kingDeeDel(){
        providerController.remove(67L);
    }

    public static void main(String[] args) {
        String json = "{\"flag\":true,\"msg\":\"success\",\"code\":0,\"data\":[{\"id\":6577,\"partnerId\":7168,\"organizationName\":\"弘康（珠海）旅游咨询有限公司\",\"organizationNo\":\"555588\",\"uName\":\"44\",\"mobile\":\"13222222233\",\"addressCascadeList\":[\"110000\",\"110100\",\"110101\"],\"province\":\"北京市\",\"city\":\"市辖区\",\"area\":\"东城区\",\"addr\":\"654\"}]}\n";
        JSONObject jsonObject = JSONObject.parseObject(json);
        final Object data = jsonObject.get("data");
        final List<PartnerProviderResp> partnerProviderResps = JSONArray.parseArray(data.toString(), PartnerProviderResp.class);
        for (PartnerProviderResp partnerProviderResp : partnerProviderResps) {
            System.out.println("11");
        }
    }
}
