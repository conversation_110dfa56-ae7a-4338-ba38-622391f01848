package com.daddylab.supplier.item;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.shop.ShopBizService;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.shop.dto.*;
import com.daddylab.supplier.item.domain.shop.enums.ShopStatus;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;

@SpringBootTest
@Slf4j
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class ShopTest {

    @Autowired
    private ShopBizService shopBizService;

    private ShopDetail shopDetail;

    @Test
    @Order(1)
    @DisplayName("创建店铺测试")
    public void createShop() {
        final CreateOrUpdateShopCmd cmd = new CreateOrUpdateShopCmd();
        cmd.setName("单元测试" + RandomUtil.randomNumbers(10));
        cmd.setLink("https://www.link.com");
        cmd.setPrincipals(Arrays.asList(
                1L,
                2L
        ));
        cmd.setPlatform(Platform.TAOBAO);
        cmd.setAccount("创建店铺测试");
        cmd.setLogo("https://test.log");
        cmd.setStatus(ShopStatus.OPEN);

        final SingleResponse<ShopDetail> createShopResponse = shopBizService.createOrUpdateShop(cmd);
        Assertions.assertTrue(createShopResponse.isSuccess(), "创建店铺失败");
        this.shopDetail = createShopResponse.getData();
        log.info("response = {}", this.shopDetail);
    }

    @Test
    @Order(2)
    @DisplayName("获取店铺详情测试")
    public void getShopDetail() {
        final SingleResponse<ShopDetail> shopDetail = shopBizService.getShopDetail(this.shopDetail.getId());
        log.info("response = {}", shopDetail);
    }

    @Test
    @Order(3)
    @DisplayName("更新店铺测试")
    public void updateShop() {
        final CreateOrUpdateShopCmd cmd = new CreateOrUpdateShopCmd();
        cmd.setId(shopDetail.getId());
        cmd.setName(shopDetail.getName() + "Updated");
        cmd.setLink(shopDetail.getLink() + "Updated");
        cmd.setPrincipals(Arrays.asList(
                1L
        ));
        cmd.setPlatform(Platform.TAOBAO);
        cmd.setAccount("更新店铺测试");
        cmd.setLogo("https://test.log.updated");
        cmd.setStatus(ShopStatus.CLOSED);

        final Response createOrUpdateShop = shopBizService.createOrUpdateShop(cmd);

        log.info("response = {}", createOrUpdateShop);
    }

    @Test
    @Order(4)
    @DisplayName("店铺下拉选测试")
    public void dropDown() {
        final ShopDropDownQuery query = new ShopDropDownQuery();
        query.setName("单元测试");
        query.setPageIndex(1);

        final MultiResponse<ShopDropDownItem> shopDropDownItemMultiResponse = shopBizService.dropDownList(query);
        log.info("response = {}", shopDropDownItemMultiResponse);
    }

    @Test
    @Order(5)
    @DisplayName("店铺列表查询测试")
    public void shopList() {

        final ShopQuery shopQuery = new ShopQuery();
        shopQuery.setSn("S200");

        final MultiResponse<ShopListItem> shopListItemMultiResponse = shopBizService.queryShopList(shopQuery);
        log.info("response = {}", shopListItemMultiResponse);
    }

    @Test
    @Order(6)
    @DisplayName("删除店铺测试")
    public void deleteShop() {
        final Response response = shopBizService.deleteShop(shopDetail.getId());

        log.info("response = {}", response);

    }

//    @Autowired
//    KingDeeGateway kingDeeGateway;

    @Autowired
    ShopGateway shopGateway;

    @Autowired
    IShopService iShopService;

    @Test
    @DisplayName("添加店铺到金蝶")
    public void addKingDee(){
//        final Shop shop = iShopService.getById(48L);
//        final ShopApiReq updateReq = ShopApiReq.getUpdateReq(shop);
//        final String s = kingDeeGateway.addOrUpdateShop(updateReq);
//        System.out.println(s);
    }



}
