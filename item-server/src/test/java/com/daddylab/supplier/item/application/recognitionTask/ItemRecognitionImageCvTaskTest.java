package com.daddylab.supplier.item.application.recognitionTask;

import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerChangeEnum;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.cv.CVUtil;
import com.daddylab.supplier.item.infrastructure.cv.ComputerVisionConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerRecognitionTask;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerModuleId;
import com.daddylab.supplier.item.types.recognitionTask.ItemDrawerRecognitionTaskStatus;
import com.google.common.collect.Lists;
import com.microsoft.azure.cognitiveservices.vision.computervision.ComputerVisionClient;
import com.microsoft.azure.cognitiveservices.vision.computervision.models.AnalyzeResults;
import com.microsoft.azure.cognitiveservices.vision.computervision.models.Line;
import com.microsoft.azure.cognitiveservices.vision.computervision.models.ReadOperationResult;
import com.microsoft.azure.cognitiveservices.vision.computervision.models.ReadResult;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import rx.Single;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/12/6
 */
class ItemRecognitionImageCvTaskTest {

    @Test
    public void doTask() throws InterruptedException {

        final ComputerVisionConfig computerVisionConfig = new ComputerVisionConfig();
        computerVisionConfig.setSubscriptionKey("78df0f545e2d4322a514c1a306245ce6");
        computerVisionConfig.setEndpoint("https://daddylab-test.cognitiveservices.azure.cn/");
        final ComputerVisionClient computerVisionClient = computerVisionConfig.computerVisionClient();
        final CvGatewayImpl cvGateway = new CvGatewayImpl(computerVisionClient);
        final ItemRecognitionTaskGateway itemRecognitionTaskGateway = Mockito.mock(
                ItemRecognitionTaskGateway.class);
        final List<ItemDrawerRecognitionTask> list = Lists.newArrayList();
        final ItemDrawerRecognitionTask itemDrawerRecognitionTask = new ItemDrawerRecognitionTask();
        itemDrawerRecognitionTask.setId(1L);
        itemDrawerRecognitionTask.setModule(ItemDrawerModuleId.LIVE_VERBAL_TRICK.getValue());
        itemDrawerRecognitionTask.setField(ItemDrawerChangeEnum.LIVE_VERBAL_TRICK.getValue());
        itemDrawerRecognitionTask.setItemId(1L);
        itemDrawerRecognitionTask.setRound(1);
        itemDrawerRecognitionTask.setDrawerImageId(0L);
        itemDrawerRecognitionTask.setImageUrl(
                "https://cdn.daddylab.com/Upload/supplier/item/image/1706213892325736448.jpg");
        itemDrawerRecognitionTask.setImageType(null);
        itemDrawerRecognitionTask.setStatus(ItemDrawerRecognitionTaskStatus.WAIT_CHECK);
        itemDrawerRecognitionTask.setContent("");
        list.add(itemDrawerRecognitionTask);
        list.add(itemDrawerRecognitionTask);
        list.add(itemDrawerRecognitionTask);
        Mockito.when(itemRecognitionTaskGateway.getWaitImageCvRecognitionTasks()).thenReturn(list);
        Mockito.doAnswer(invocation -> {
                    System.out.println("updateById:" + invocation.toString());
                    return true;
                }).when(itemRecognitionTaskGateway)
                .updateTaskCvResult(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
                        Mockito.any());
        final RefreshConfig refreshConfig = new RefreshConfig();
        final ItemRecognitionImageCvTask itemRecognitionImageCvTask = new ItemRecognitionImageCvTask(
                itemRecognitionTaskGateway, cvGateway, refreshConfig);
        itemRecognitionImageCvTask.doTask();
    }

    @Test
    public void demo1() {
        final ComputerVisionConfig computerVisionConfig = new ComputerVisionConfig();
        computerVisionConfig.setSubscriptionKey("78df0f545e2d4322a514c1a306245ce6");
        computerVisionConfig.setEndpoint("https://daddylab-test.cognitiveservices.azure.cn/");
        final ComputerVisionClient computerVisionClient = computerVisionConfig.computerVisionClient();

        String url = "https://mmbiz.qpic.cn/mmbiz_jpg/ia9qibpibkqssgT6Xfh5vELFZQI5PWGLsQJkVIQAZohibg4iaPhDGj0JDOo4BZOGjZ4kqcqEyShtakz0kiczMTSS9cdg/640?wx_fmt=jpeg&wxfrom=5&wx_lazy=1&wx_co=1";
        Single<ReadOperationResult> readOperationResultSingle = CVUtil.imageVision(computerVisionClient, url);
        System.out.println(JsonUtil.toJson(readOperationResultSingle));
    }

    @Test
    public void ocrImage() throws IOException {
        final ComputerVisionConfig computerVisionConfig = new ComputerVisionConfig();
        computerVisionConfig.setSubscriptionKey("78df0f545e2d4322a514c1a306245ce6");
        computerVisionConfig.setEndpoint("https://daddylab-test.cognitiveservices.azure.cn/");
        final ComputerVisionClient computerVisionClient = computerVisionConfig.computerVisionClient();
        final CvGatewayImpl cvGateway = new CvGatewayImpl(computerVisionClient);

        String imageUrl = "https://cdn.daddylab.com/Upload/ecbms/20221031/093304/jrctpznx7bnib7c8";
        cvGateway.imageVision(imageUrl).subscribe(readOperationResult -> {
            StringBuilder stringBuilder = new StringBuilder();

            final Set<ReadOperationResult> readOperationResults = Collections.singleton(readOperationResult);
            readOperationResults.forEach(val -> {
                AnalyzeResults analyzeResults = val.analyzeResult();
                List<ReadResult> readResults = analyzeResults.readResults();
                readResults.forEach(readResult -> {
                    List<Line> lines = readResult.lines();
                    lines.forEach(line -> {
                        String text = line.text();
                        stringBuilder.append(text);
                    });
                });

            });
            System.out.println(JsonUtil.toJson(readOperationResult));
            System.out.println(stringBuilder);
        });
        System.in.read();

    }

}