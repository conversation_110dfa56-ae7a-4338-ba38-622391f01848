package com.daddylab.supplier.item.application.platformItem.tasks;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 2024/4/25
 */
@SpringBootTest
class CheckPlatformItemInvalidTaskTest {
    @Autowired
    private CheckPlatformItemInvalidTask task;

    @Test
    public void test() {
        task.check();
    }

}