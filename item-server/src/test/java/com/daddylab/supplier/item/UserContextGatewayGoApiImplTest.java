package com.daddylab.supplier.item;

import cn.hutool.core.io.FileUtil;
import com.daddylab.supplier.item.domain.auth.PermRefreshManager;
import com.daddylab.supplier.item.domain.departments.DepartmentGateway;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.accessControl.*;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.auth.UserContextGatewayGoApiImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.department.DepartmentGatewayImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.UserGatewayImpl;
import com.daddylab.supplier.item.infrastructure.goclient.Rows;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.mockito.Mockito;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.test.context.TestPropertySource;

import java.nio.charset.StandardCharsets;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@Slf4j
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@TestPropertySource(properties = {
        "spring.profiles.active:dev"
})
@TestConfiguration
public class UserContextGatewayGoApiImplTest {

    @Test
    @DisplayName("测试从DB查询用户登录上下文信息")
    public void testUserContextGatewayDbImpl() {
        final long userId = 21L;

        PermRefreshManager permRefreshManager = Mockito.mock(PermRefreshManager.class);
        when(permRefreshManager.isNeedRefreshPerm(any())).thenReturn(true);

        UserGateway userGateway = Mockito.mock(UserGatewayImpl.class);
        final StaffInfo staffInfo = new StaffInfo();
        staffInfo.setUserId(userId);
        when(userGateway.queryStaffInfoById(userId)).thenReturn(staffInfo);

        long systemId = 6L;

        AccessControlAPI accessControlAPI = Mockito.mock(AccessControlAPI.class);
        final Rsp<List<CasbinPolicy>> policiesRsp = JsonUtil.parse(FileUtil.readString("policy.json", StandardCharsets.UTF_8),
                new TypeReference<Rsp<List<CasbinPolicy>>>() {
                });
        when(accessControlAPI.casbinPolicies(systemId)).thenReturn(policiesRsp);

        final Rsp<List<Resource>> resourcesRsp = JsonUtil.parse(FileUtil.readString("resources.json", StandardCharsets.UTF_8),
                new TypeReference<Rsp<List<Resource>>>() {
                });
        when(accessControlAPI.resourcesQuery(systemId)).thenReturn(resourcesRsp);

        final Rsp<List<Dept>> deptsRsp = JsonUtil.parse(FileUtil.readString("depts.json", StandardCharsets.UTF_8),
                new TypeReference<Rsp<List<Dept>>>() {
                });
        when(accessControlAPI.dept()).thenReturn(deptsRsp);

        final Rsp<Rows<StaffAcInfo>> acUserInfoRsp = JsonUtil.parse(FileUtil.readString("acUserInfo.json", StandardCharsets.UTF_8),
                new TypeReference<Rsp<Rows<StaffAcInfo>>>() {
                });
        when(accessControlAPI.staffAcInfoPageQuery(any())).thenReturn(acUserInfoRsp);

        final RBucket bucket = mock(RBucket.class);
        final RedissonClient redissonClient = mock(RedissonClient.class);
        when(redissonClient.getBucket(any())).thenReturn(bucket);

        final DepartmentGateway departmentGateway = new DepartmentGatewayImpl(accessControlAPI);

        final UserContextGatewayGoApiImpl userContextGatewayGoApi = new UserContextGatewayGoApiImpl(
                permRefreshManager,
                userGateway,
                accessControlAPI,
                systemId,
                redissonClient,
                departmentGateway);

        final UserContext.UserInfo userContext = userContextGatewayGoApi.getUserContext(userId, true);
        log.debug("userContext:" + JsonUtil.toJson(userContext));
        assertNotNull(userContext);
        assertEquals(userContext.getUserId(), userId);
    }
}
