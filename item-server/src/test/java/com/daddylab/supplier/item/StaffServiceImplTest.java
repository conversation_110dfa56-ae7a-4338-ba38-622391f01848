package com.daddylab.supplier.item;

import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffListQuery;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest
public class StaffServiceImplTest {

    @Autowired
    private StaffService staffService;


    @Test
    public void test1() {
        List<DadStaffVO> staffList = staffService.getStaffList(Arrays.asList(7505356L, 22L));
        System.out.println(

        );
    }
}
