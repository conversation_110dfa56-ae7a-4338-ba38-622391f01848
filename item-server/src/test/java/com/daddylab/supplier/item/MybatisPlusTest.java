package com.daddylab.supplier.item;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.nacos.NacosConfigAutoConfiguration;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.CommonFieldHandler;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.MybatisPlusConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemPrice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtStockSpec;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemPriceType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ProviderType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemPriceMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtStockSpecMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemPriceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtStockSpecService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl.ItemPriceServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl.WdtStockSpecServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@SpringBootTest(properties = {
        "spring.profiles.active=test",
        "logging.level.com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemPriceMapper=debug",
        "logging.level.com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.WdtStockSpecMapper=debug"
}, classes = {
        // 加载 Nacos 配置
        NacosConfigAutoConfiguration.class,
        // MybatisPlus 提供的动态数据源配置（可配置多数据源切换）
        DynamicDataSourceAutoConfiguration.class,
        // Spring 数据源自动配置
        DataSourceAutoConfiguration.class,
        // MybatisPlus 自动配置
        MybatisPlusAutoConfiguration.class,
        // MybatisPlus 自定义配置
        MybatisPlusConfig.class,
        // MybatisPlus 自定义字段注入器
        CommonFieldHandler.class,
        // 加载 MybatisPlusJoin 相关配置类
        com.github.yulichang.interceptor.MPJInterceptor.class,
        com.github.yulichang.config.InterceptorConfig.class,
        com.github.yulichang.config.MappingConfig.class,
        com.github.yulichang.toolkit.SpringContentUtils.class,
        // 业务类加载
        ItemPriceMapper.class,
        ItemPriceServiceImpl.class,
        MybatisPlusTest.class,
        WdtStockSpecServiceImpl.class,
        WdtStockSpecMapper.class
})
@Slf4j
public class MybatisPlusTest {

    @Autowired
    IItemPriceService itemPriceService;

    @Test
    public void testEnum() {
        final Optional<String> enumValueFieldName = MybatisEnumTypeHandler
                .findEnumValueFieldName(ProviderType.class);
        enumValueFieldName.ifPresent(System.out::println);

    }

    @Autowired
    IWdtStockSpecService wdtStockSpecService;

    @Test
    public void testWdtStockSpecInsertId() {
        final WdtStockSpec stockSpec = new WdtStockSpec();
        stockSpec.setId(49179L);
        stockSpec.setRecId(6666L);
        stockSpec.setDefect(0);
        stockSpec.setStockNum(new BigDecimal("0"));
        stockSpec.setWmsSyncStock(new BigDecimal("0"));
        stockSpec.setWmsStockDiff(new BigDecimal("0"));
        stockSpec.setSpecNo("");
        stockSpec.setSpecId(0L);
        stockSpec.setGoodsNo("");
        stockSpec.setGoodsName("");
        stockSpec.setSpecCode("");
        stockSpec.setBrandName("");
        stockSpec.setSpecName("");
        stockSpec.setBarcode("");
        stockSpec.setUnpayNum(new BigDecimal("0"));
        stockSpec.setSubscribeNum(new BigDecimal("0"));
        stockSpec.setOrderNum(new BigDecimal("0"));
        stockSpec.setSendingNum(new BigDecimal("0"));
        stockSpec.setPurchaseNum(new BigDecimal("0"));
        stockSpec.setTransferNum(new BigDecimal("0"));
        stockSpec.setToPurchaseNum(new BigDecimal("0"));
        stockSpec.setPurchaseArriveNum(new BigDecimal("0"));
        stockSpec.setWmsPreemptyStock(new BigDecimal("0"));
        stockSpec.setWeight(new BigDecimal("0"));
        stockSpec.setImgUrl("");
        stockSpec.setWarehouseNo("");
        stockSpec.setWarehouseId(0L);
        stockSpec.setWarehouseName("");
        stockSpec.setWarehouseType(0);
        stockSpec.setAvailableSendStock(new BigDecimal("0"));
        stockSpec.setAvailableStock(new BigDecimal("0"));
        stockSpec.setCreated(LocalDateTime.now());
        stockSpec.setModified(LocalDateTime.now());
        stockSpec.setPartPaidNum(new BigDecimal("0"));
        stockSpec.setRefundExchNum(new BigDecimal("0"));
        stockSpec.setRefundNum(new BigDecimal("0"));
        stockSpec.setRefundOnwayNum(new BigDecimal("0"));
        stockSpec.setReturnExchNum(new BigDecimal("0"));
        stockSpec.setReturnNum(new BigDecimal("0"));
        stockSpec.setReturnOnwayNum(new BigDecimal("0"));
        stockSpec.setToTransferNum(new BigDecimal("0"));
        stockSpec.setWmsPreemptyDiff(new BigDecimal("0"));
        stockSpec.setWmsSyncTime(LocalDateTime.now());
        stockSpec.setRemark("");
        stockSpec.setLockNum(new BigDecimal("0"));
        stockSpec.setFlagId(0L);
        stockSpec.setFlagName("");
        stockSpec.setBrandNo("");
        stockSpec.setToOtherOutNum(new BigDecimal("0"));
        stockSpec.setToProcessOutNum(new BigDecimal("0"));
        stockSpec.setToProcessInNum(new BigDecimal("0"));
        stockSpec.setLastPdTime(LocalDateTime.now());
        stockSpec.setLastInoutTime(LocalDateTime.now());
        stockSpec.setStatus(0);
        stockSpec.setTodayNum(new BigDecimal("0"));
        stockSpec.setNum7Days(new BigDecimal("0"));
        stockSpec.setNum14Days(new BigDecimal("0"));
        stockSpec.setNumMonth(new BigDecimal("0"));
        stockSpec.setNumAll(new BigDecimal("0"));
        stockSpec.setCostPrice(new BigDecimal("0"));

        wdtStockSpecService.save(stockSpec);

    }

    @Test
    public void testItemPriceQuery() {
        final LambdaQueryWrapper<ItemPrice> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ItemPrice::getItemId, 306051L);
        queryWrapper.in(ItemPrice::getType,
                Arrays.asList(ItemPriceType.PROCUREMENT.getValue(),
                        ItemPriceType.DAILY.getValue()));
        final List<ItemPrice> itemPrices = itemPriceService.list(queryWrapper);
        final ItemPrice itemProcurementPrice = itemPrices.stream()
                .filter(ip -> ip.getType() == ItemPriceType.PROCUREMENT).findFirst()
                .orElseGet(() -> {
                    final ItemPrice itemPrice = new ItemPrice();
                    itemPrice.setItemId(306051L);
                    itemPrice.setType(ItemPriceType.PROCUREMENT);
                    itemPrice.setStartTime(1640966400L);
                    itemPrice.setEndTime(4070880000L);
                    itemPrice.setCustomName(ItemPriceType.PROCUREMENT.getDesc());
                    return itemPrice;
                });
        if (StrUtil.isBlank(itemProcurementPrice.getCustomName())) {
            itemProcurementPrice.setCustomName(ItemPriceType.PROCUREMENT.getDesc());
        }
        log.info("itemPrice = {}", itemPrices);
        log.info("itemProcurementPrice = {}", itemProcurementPrice);
    }
}
