package com.daddylab.supplier.item;

import static org.assertj.core.api.Assertions.assertThat;

import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.infrastructure.winrobot360.Winrobot360API;
import com.daddylab.supplier.item.infrastructure.winrobot360.Winrobot360Config;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.EditTbGoodsParam;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.GenericRsp;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TaskQueryParam;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TaskQueryRspData;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TaskStartParam;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TaskStartRspData;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TaskState;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TokenCreateRspData;
import com.daddylab.supplier.item.infrastructure.winrobot360.types.TypeFactory;
import com.google.common.collect.Maps;
import java.time.LocalDateTime;
import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;

/**
 * <AUTHOR>
 * @since 2022/9/16
 */
@EnableFeignClients(clients = {Winrobot360API.class})
@SpringBootTest(
        classes = {
                HttpMessageConvertersAutoConfiguration.class,
                ConfigurationPropertiesBindingPostProcessor.class,
                Winrobot360Config.class,
                FeignAutoConfiguration.class,
                Winrobot360API.class,
        },
        properties = {
                "spring.cloud.nacos.config.enabled=false",
                "spring.profiles.active=test",
                "feign.client.config.default.loggerLevel=BASIC",
                "winrobot360.url=https://api.winrobot360.com",
                "winrobot360.accessKeyId=6pvSyxBeD1btU5m0@platform",
                "winrobot360.accessKeySecret=X5VDqbSJKrZNTfFPcv9YRQGCdtMeyE4W",
                "winrobot360.scheduleUuid=503f4e9e-8f66-442e-9909-716bded60fb5",
                "winrobot360.editTbGoodsRobotUuid=73f86f37-aa8e-4464-8df6-85236386a85c",
        }
)
@Slf4j
class Winrobot360APITest {

    @Autowired
    private Winrobot360API winrobot360API;

    @Autowired
    private Winrobot360Config config;

    @Test
    public void testEditGoodsTask() {
        final GenericRsp<TokenCreateRspData> tokenCreateRsp = winrobot360API.tokenCreate(
                config.getAccessKeyId(), config.getAccessKeySecret());
        log.info("创建 token 响应: {}", tokenCreateRsp);

        Assertions.assertTrue(tokenCreateRsp.getSuccess(), "创建 token 失败");
        Assertions.assertNotNull(tokenCreateRsp.getData(), "创建 token 返回数据为空");
        Assertions.assertNotNull(tokenCreateRsp.getData().getAccessToken(), "创建 token 返回数据为空");

        final String accessToken = "BEARER " + tokenCreateRsp.getData().getAccessToken();

        final EditTbGoodsParam editTbGoodsParam = new EditTbGoodsParam();
        editTbGoodsParam.setPlanTime("9月第三周IT测试");
        editTbGoodsParam.setSpId("683332893770");
        editTbGoodsParam.setSpName("假发片");

        final HashMap<String, String> mainPic = Maps.newHashMap();
        mainPic.put("主图-1",
                "https://cdn-test.daddylab.com/Upload/supplier/item/image/2761-1663574541044.jpg");
        mainPic.put("主图-2",
                "https://cdn-test.daddylab.com/Upload/supplier/item/image/2762-1663574590262.jpg");
        mainPic.put("主图-3",
                "https://cdn-test.daddylab.com/Upload/supplier/item/image/2763-1663574607648.jpg");
        mainPic.put("主图-4",
                "https://cdn-test.daddylab.com/Upload/supplier/item/image/2764-1663574625033.jpg");
        mainPic.put("主图-5",
                "https://cdn-test.daddylab.com/Upload/supplier/item/image/2765-1663574659701.jpg");
        editTbGoodsParam.setMainPic(mainPic);

        final HashMap<String, String> detailPic = Maps.newHashMap();
        detailPic.put("详情-1",
                "https://cdn-test.daddylab.com/Upload/supplier/item/image/canvas-1663729074241.jpeg");
        editTbGoodsParam.setDetailPic(detailPic);

        final TaskStartParam taskStartParam = TypeFactory.buildTaskStartParam(
                config.getScheduleUuid(), config.getEditTbGoodsRobotUuid(), editTbGoodsParam);

        log.info("任务启动参数: {}", JsonUtil.toJson(taskStartParam));
        final GenericRsp<TaskStartRspData> taskStartRsp = winrobot360API.taskStart(
                accessToken, taskStartParam);
        assertThat(taskStartRsp.getSuccess()).isEqualTo(true);
        assertThat(taskStartRsp.getData()).isNotNull();
        assertThat(taskStartRsp.getData().getTaskUuid()).isNotBlank();
        log.info("任务启动响应: {}", taskStartRsp);

        final String taskUuid = taskStartRsp.getData().getTaskUuid();

        int timeout = 300;
        boolean isTimeout = false;
        final LocalDateTime startTime = LocalDateTime.now();

        while (true) {
            final TaskQueryParam taskQueryParam = new TaskQueryParam();
            taskQueryParam.setTaskUuid(taskUuid);
            final GenericRsp<TaskQueryRspData> taskQueryRsp = winrobot360API.taskQuery(
                    accessToken, taskQueryParam);

            assertThat(taskQueryRsp.getSuccess()).isTrue();
            assertThat(taskQueryRsp.getData()).isNotNull();

            log.info("任务查询响应: {}", taskQueryRsp);

            final String status = taskQueryRsp.getData().getStatus();
            if (!StringUtil.equalsIgnoreCase(status,
                    TaskState.RUNNING.name())) {
                log.info("任务完成 结果: {}", status);
                break;
            }
            if (LocalDateTime.now().isAfter(startTime.plusSeconds(timeout))) {
                isTimeout = true;
                break;
            }
            ThreadUtil.sleep(10000);
        }

        Assertions.assertFalse(isTimeout);

    }

    @Test
    public void testTaskQuery() {

        String accessToken = "BEARER 6d196047-ec8a-445e-8cf6-1d30715c384b";
        String taskUuid = "d76d0e74-0188-4336-974e-ab1977432aaf";
        final TaskQueryParam taskQueryParam = new TaskQueryParam();
        taskQueryParam.setTaskUuid(taskUuid);
        final GenericRsp<TaskQueryRspData> taskQueryRsp = winrobot360API.taskQuery(
                accessToken, taskQueryParam);
        log.info("任务查询响应: {}", taskQueryRsp);
        assertThat(taskQueryRsp.getSuccess()).isTrue();
        assertThat(taskQueryRsp.getData()).isNotNull();
    }
}