package com.daddylab.supplier.item;

import com.alibaba.cola.dto.MultiResponse;
import com.daddylab.supplier.item.application.brand.BrandBizService;
import com.daddylab.supplier.item.domain.brand.dto.BrandDropDownItem;
import com.daddylab.supplier.item.domain.brand.dto.BrandDropDownQuery;
import com.daddylab.supplier.item.domain.region.entity.RegionTree;
import com.daddylab.supplier.item.domain.region.service.RegionDomainService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.lang.instrument.Instrumentation;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11 2:08 下午
 * @description
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
@Slf4j
public class RegionTest {

    @Autowired
    private RegionDomainService regionDomainService;

    @Test
    @DisplayName("获取区域树")
    public void getRegionTree() {
        final RegionTree regionTree = regionDomainService.getRegionTree();
        log.info("regionTree = {}", regionTree);

        final RegionTree regionTree1 = regionDomainService.getRegionTree();
        final boolean isSameInstance = regionTree1 == regionTree;
        log.info("isSameInstance = {}", isSameInstance);
    }


}
