package com.daddylab.supplier.item;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.common.trans.ItemTransMapper;
import com.daddylab.supplier.item.controller.item.dto.ExportCmd;
import com.daddylab.supplier.item.controller.item.dto.ExportTaskVo;
import com.daddylab.supplier.item.controller.purchase.PurchaseController;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseQueryPage;
import com.daddylab.supplier.item.domain.exportTask.ExportDomainService;
import com.daddylab.supplier.item.domain.exportTask.ExportGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ExportTask;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IExportTaskService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/2 10:45 上午
 * @description
 */
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class ExportTest {

    @Autowired
    ExportDomainService exportDomain;

    @Autowired
    ExportGateway exportGateway;

    @Autowired
    IExportTaskService exportTaskService;

//    @Test
//    @DisplayName("导出sku测试")
//    public void exportSkuWithoutPrice() {
//        System.out.println(exportDomain.exportSku(new ExportCmd(), true));
//    }

    @Test
    @DisplayName("导出商品")
    public void exportItem() {
        // 1638428855483 1638428858844
//        System.out.println(exportDomain.exportItem(new ExportCmd(), false));
    }

    @Test
    @DisplayName("查询商品sku导出数据量")
    public void queryExportCount() {
        ExportCmd cmd = new ExportCmd();
        cmd.setLatitude("item");
        cmd.setSku("10016902");
        System.out.println(exportGateway.exportItemAndSkuCount(cmd));
    }

    @Autowired
    PurchaseController purchaseController;

    @Test
    @DisplayName("采购导出")
    @SneakyThrows
    public void exportPurchase(){
        purchaseController.exportPurchaseList(new PurchaseQueryPage());
        System.in.read();
    }

    public static void main(String[] args) {
        System.out.println(DateUtil.format(0L));

    }

    @Test
    @DisplayName("导出列表测试")
    public void testList(){
        final LambdaQueryWrapper<ExportTask> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.orderByDesc(ExportTask::getCreatedAt);
        queryWrapper.eq(ExportTask::getCreatedUid, 7571297);
        Page<ExportTask> pageReq = new Page<>(0, 10);
        final Page<ExportTask> page = exportTaskService.page(pageReq, queryWrapper);
        System.out.println("page:"+JsonUtil.toJson(page.getRecords()));
        final List<ExportTaskVo> exportTaskVos = ItemTransMapper.INSTANCE.exportTaskDbToVos(page.getRecords());
        for (ExportTaskVo exportTaskVo : exportTaskVos) {
            final String name = exportTaskVo.getName();
            if (StringUtil.isNotBlank(exportTaskVo.getName())) {
                if (name.indexOf("-") > 0) {
                    final String[] split = name.split("-");
                    System.out.println(JsonUtil.toJson(split));
                    String s = DateUtil.parseTimeStamp(Long.parseLong(split[1]), DateUtil.DEFAULT_FORMAT);
                    exportTaskVo.setName(split[0] + "-" + s);
                }
            }
        }
        System.out.println(JsonUtil.toJson(exportTaskVos));
    }



}
