package com.daddylab.supplier.item;

import org.junit.jupiter.api.Test;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2024/4/16
 */
public class CommonTests {

    @Test
    public void test() {
        String s = "@mingzi @ni woshishui";

        final String regex = "@[^\\s]+";
        final Pattern pattern = Pattern.compile(regex);
        final Matcher matcher = pattern.matcher(s);
        while (matcher.find()) {
            System.out.println(matcher.group(0));
        }
        System.out.println(matcher.replaceAll(""));


    }
}
