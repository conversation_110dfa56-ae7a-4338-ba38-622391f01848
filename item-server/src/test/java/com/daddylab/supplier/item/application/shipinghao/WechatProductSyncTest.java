package com.daddylab.supplier.item.application.shipinghao;

import static org.junit.jupiter.api.Assertions.*;

import com.daddylab.supplier.item.ItemApplication;
import com.daddylab.supplier.item.application.platformItemSkuInventory.support.WechatVideoItemSyncServiceImpl;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductDetailDto;
import com.daddylab.supplier.item.application.shipinghao.dto.WechatProductListDto;
import com.daddylab.supplier.item.application.shipinghao.service.WechatProductSyncService;
import com.daddylab.supplier.item.application.shop.ShopAuthorizationBizService;
import com.daddylab.supplier.item.domain.wechatProductDetail.gateway.WechatProductDetailGateway;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 微信商品同步测试类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@SpringBootTest(classes = {ItemApplication.class})
public class WechatProductSyncTest {

  @Autowired private WechatProductSyncService wechatProductSyncService;

  @Autowired private WechatProductDetailGateway wechatProductDetailGateway;

  @Autowired
  WechatVideoItemSyncServiceImpl shiPingHaoItemSyncService;

  @Autowired ShopAuthorizationBizService shopAuthorizationBizService;

  /** 测试全量同步 启动实际的同步流程，将视频号平台的商品信息拉回来 */
  @Test
  public void testFullSync() {

    shopAuthorizationBizService.authorize("K0002");
    shopAuthorizationBizService.authorize("K0340");

    log.info("开始执行视频号商品全量同步测试");

    try {
      // 启动全量同步
      shiPingHaoItemSyncService.fullDoseSync();

      log.info("视频号商品全量同步执行完成");

    } catch (Exception e) {
      log.error("视频号商品全量同步执行失败", e);
      throw e;
    }
  }

  /** 测试获取商品列表 */
  @Test
  public void testGetProductList() {
    log.info("开始测试获取商品列表");

    // 测试daddylab类型
    String type = "daddylab";
    WechatProductListDto productList = wechatProductSyncService.getProductList(type, 0, 5);

    if (productList != null) {
      log.info("获取商品列表成功，类型: {}, 总数: {}", type, productList.getTotalNum());
      if (productList.getProductIds() != null) {
        log.info("商品ID列表数量: {}", productList.getProductIds().size());
        for (String productId : productList.getProductIds()) {
          log.info("商品ID: {}", productId);

          WechatProductDetailDto productDetail =
              wechatProductSyncService.getProductDetail(type, productId);
          log.info("product:{}", JsonUtil.toJson(productDetail));
        }
      }
    } else {
      log.error("获取商品列表失败，类型: {}", type);
    }

    // 测试member类型
    type = "member";
    WechatProductListDto productListMember = wechatProductSyncService.getProductList(type, 0, 5);

    if (productListMember != null) {
      log.info("获取商品列表成功，类型: {}, 总数: {}", type, productListMember.getTotalNum());
      if (productListMember.getProductIds() != null) {
        log.info("商品ID列表数量: {}", productListMember.getProductIds().size());
        for (String productId : productListMember.getProductIds()) {
          log.info("商品ID: {}", productId);

          WechatProductDetailDto productDetail =
              wechatProductSyncService.getProductDetail(type, productId);
          log.info("获取商品详情成功，类型: {}, 标题: {}", type, productDetail.getProduct().getTitle());
          log.info("商品ID: {}", productDetail.getProduct().getProductId());
          log.info("商品状态: {}", productDetail.getProduct().getStatus());
          log.info("商品类型: {}", productDetail.getProduct().getProductType());
        }
      }
    } else {
      log.error("获取商品列表失败，类型: {}", type);
    }

    // 测试最大限制参数
    log.info("测试最大限制参数（limit=30）");
    WechatProductListDto productListMax =
        wechatProductSyncService.getProductList("daddylab", 0, 30);

    if (productListMax != null) {
      log.info("获取商品列表（最大限制）成功，总数: {}", productListMax.getTotalNum());
      if (productListMax.getProductIds() != null) {
        log.info("返回商品ID数量: {}", productListMax.getProductIds().size());
        // 验证返回数量不超过30
        assertTrue(
            productListMax.getProductIds().size() <= 30,
            "返回的商品ID数量应该不超过30，实际: " + productListMax.getProductIds().size());
      }
    } else {
      log.error("获取商品列表（最大限制）失败");
    }
  }

  /** 测试全量同步商品 */
//  @Test
//  public void testSyncAllProducts() {
//    log.info("开始测试全量同步商品");
//    log.info("全量同步测试跳过（避免调用真实API）");
//    boolean success = wechatProductSyncService.syncAllProducts("daddylab");
//    log.info("全量同步结果: {}", success);
//  }
//
//  /** 测试增量同步商品 */
//  @Test
//  public void testSyncIncrementalProducts() {
//    log.info("开始测试增量同步商品");
//    log.info("增量同步测试跳过（避免调用真实API）");
//    boolean success =
//        wechatProductSyncService.syncIncrementalProducts(
//            "daddylab", System.currentTimeMillis() - 86400000);
//    log.info("增量同步结果: {}", success);
//  }
}
