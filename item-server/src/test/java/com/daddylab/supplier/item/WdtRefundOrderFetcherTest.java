package com.daddylab.supplier.item;

import com.daddylab.supplier.item.application.refundOrder.WdtRefundOrderFetcher;
import com.daddylab.supplier.item.domain.dataFetch.RunContext;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(properties = {
        "spring.profiles.active:test"
})
public class WdtRefundOrderFetcherTest {

    @Autowired
    WdtRefundOrderFetcher wdtRefundOrderFetcher;

    @Test
    public void testWdtRefundOrderFetcher() {
        wdtRefundOrderFetcher.fetch(DateUtil.parse("2022-04-19 11:23:00"),
                DateUtil.parse("2022-05-19 11:23:00"), 1, 100, new RunContext(null, null));
    }


}
