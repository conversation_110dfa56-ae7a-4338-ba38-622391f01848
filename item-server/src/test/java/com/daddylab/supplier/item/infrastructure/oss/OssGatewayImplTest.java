package com.daddylab.supplier.item.infrastructure.oss;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.URLUtil;
import com.aliyun.oss.model.ObjectMetadata;
import com.daddylab.supplier.item.infrastructure.oss.models.OssProcess;
import com.daddylab.supplier.item.infrastructure.oss.models.SaveAs;
import com.daddylab.supplier.item.infrastructure.oss.models.VideoSnapshots;
import lombok.NonNull;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2023/10/12
 */
class OssGatewayImplTest {
  @Test
  public void putPrivateBucket() throws IOException {
    final OssGatewayImpl ossGateway = getOssGateway();
    final Path testLocalFilePath = Paths.get("/Users/<USER>/Desktop/主图1.jpg");
    final String testObjectUrl =
        ossGateway.put(true, "test.jpg", Files.newInputStream(testLocalFilePath));
    System.out.println(testObjectUrl);
    final String testSignedUrl =
        getOssGateway().generatePresignedUrl(true, URLUtil.getPath(testObjectUrl));
    System.out.println(testSignedUrl);
    final CloseableHttpClient httpClient = HttpClients.createDefault();
    final CloseableHttpResponse test1SignedUrlHttpResponse =
        httpClient.execute(new HttpGet(testSignedUrl));
    final byte[] test1SignedUrlHttpResDataBytes =
        EntityUtils.toByteArray(test1SignedUrlHttpResponse.getEntity());
    Assertions.assertTrue(
        Arrays.equals(
            IoUtil.readBytes(Files.newInputStream(testLocalFilePath)),
            test1SignedUrlHttpResDataBytes));
  }

  @Test
  public void putPublicBucket() throws IOException {
    final OssGatewayImpl ossGateway = getOssGateway();
    final Path testLocalFilePath = Paths.get("/Users/<USER>/Desktop/主图1.jpg");
    final String testObjectUrl =
        ossGateway.put(false, "test.jpg", Files.newInputStream(testLocalFilePath));
    System.out.println(testObjectUrl);
    final String path = "/Upload/supplier/item/test.jpg";
    final ObjectMetadata metadata = ossGateway.metadata(false, path);
    System.out.println(metadata);
    ossGateway.delete(false, path);
  }

  @Test
  void generatePresignedUrl() throws IOException {
    // https://daddyoss.oss-cn-hangzhou.aliyuncs.com/Upload/supplier/item/test1.jpg
    // https://daddyoss-private.oss-cn-hangzhou.aliyuncs.com/Upload/supplier/item/settlement_tb5rau.zip?Expires=1701742434&OSSAccessKeyId=LTAI5t7qeNHcvUXKwcab3gWp&Signature=vy7%2BbVBsZMqmcrS1hVeWOhgRI24%3D
    //        final String s =
    //                getOssGateway().generatePresignedUrl(true,
    // "https://daddyoss-private.oss-cn-hangzhou.aliyuncs.com/Upload/supplier/item/Upload/supplier/item/1930078712069058560.xlsx?Expires=1749002201&OSSAccessKeyId=LTAI5t7qeNHcvUXKwcab3gWp&Signature=F1L2jeIDLCLcb7I%2Fq%2BnZv85d4UI%3D");
    //        System.out.println(s);

    final OssGatewayImpl ossGateway = getOssGateway();
    final Path path = Paths.get("/Users/<USER>/Desktop/附件：宁波市户外广告设施设置管理办法1.tif");
    final InputStream inputStream = Files.newInputStream(path);
    System.out.println(path.getFileName().toString());
    final String put =
        ossGateway.put(
            true,
                path.getFileName().toString(),
                inputStream);
    System.out.println(put);
  }

  @NonNull
  private static OssGatewayImpl getOssGateway() {
    final OssConfig ossConfig = new OssConfig();
    ossConfig.setEndpoint("oss-cn-hangzhou.aliyuncs.com");
    ossConfig.setAccessKeyId("LTAI5t7qeNHcvUXKwcab3gWp");
    ossConfig.setAccessKeySecret("******************************");
    ossConfig.setPublicBucket("daddyoss");
    ossConfig.setPrivateBucket("daddyoss-private");
    ossConfig.setPublicUrl("https://daddyoss.oss-cn-hangzhou.aliyuncs.com");
    ossConfig.setPrivateUrl("https://daddyoss-private.oss-cn-hangzhou.aliyuncs.com");
    //        ossConfig.setPublicBucket("daddyoss-test");
    //        ossConfig.setPrivateBucket("daddyoss-private-test");
    //        ossConfig.setPublicUrl("https://daddyoss-test.oss-cn-hangzhou.aliyuncs.com");
    //        ossConfig.setPrivateUrl("https://daddyoss-private-test.oss-cn-hangzhou.aliyuncs.com");
    ossConfig.setPrefixDir("/Upload/supplier/item/");

    return new OssGatewayImpl(ossConfig);
  }

  @Test
  void imageInfo() {
    System.out.println(getOssGateway().imageInfo(true, "/Upload/supplier/item/test.jpg"));
  }

  @Test
  void ossProcessCommand() {
    final OssProcess process =
        OssProcess.command(VideoSnapshots.builder().ss(0).num(1).build())
            .and(SaveAs.builder().b("daddylab-private").o("test").build());
    System.out.println(process);
  }

  @Test
  void videoSnapshots() throws IOException {

    final String path1 = "test_video.mp4";
    final String put =
        getOssGateway()
            .put(
                false,
                path1,
                Files.newInputStream(
                    Paths.get("/Users/<USER>/Pictures/1627452181699023-1659941753868.mov")));
    System.out.println(put);
    getOssGateway()
        .videoSnapshots(false, "/Upload/supplier/item/test_video.mp4", "test_video_snap_1");
  }
}
