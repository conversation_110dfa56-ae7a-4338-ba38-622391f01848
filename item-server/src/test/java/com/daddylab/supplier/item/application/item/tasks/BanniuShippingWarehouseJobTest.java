package com.daddylab.supplier.item.application.item.tasks;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/9/8
 */
@SpringBootTest
class BanniuShippingWarehouseJobTest {
    @Resource BanniuShippingWarehouseJob job;

    @Test
    public void init() {
        job.init();
    }

    @Test
    public void syncAll() {
        final BanniuShippingWarehouseJob.SyncAllParams syncAllParams = new BanniuShippingWarehouseJob.SyncAllParams();
        syncAllParams.setSyncItem(true);
        syncAllParams.setSyncCombinationItem(true);
        syncAllParams.setLimit(500);
        job.syncAll(syncAllParams);
    }
}
