package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper;

import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.junit.BeforeClass;
import org.junit.Test;

/**
 * <AUTHOR> up
 * @date 2022年07月28日 4:33 PM
 */
public class NewGoodsMapperTest {
    private static NewGoodsMapper mapper;

    @BeforeClass
    public static void setUpMybatisDatabase() {
        SqlSessionFactory builder = new SqlSessionFactoryBuilder().build(NewGoodsMapperTest.class.getClassLoader().getResourceAsStream("mybatisTestConfiguration/NewGoodsMapperTestConfiguration.xml"));
        //you can use builder.openSession(false) to not commit to database
        mapper = builder.getConfiguration().getMapper(NewGoodsMapper.class, builder.openSession(true));
    }

    @Test
    public void testCountGroupBySpu() {
    }
}
