package com.daddylab.supplier;

import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.mail.MailProperties;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 * @since 2024/10/28
 */
public class MailTests {
    @Test
    public void sendMail() {
        final MailProperties mailProperties = new MailProperties();
        mailProperties.setHost("smtp.exmail.qq.com");
        mailProperties.setPort(465);
        mailProperties.setUsername("<EMAIL>");
        mailProperties.setPassword("UQuexFGHUKxUcVBk");
        mailProperties.setProtocol("smtp");
        final Map<String, String> properties = mailProperties.getProperties();
        properties.put("mail.smtp.auth", "true");
        properties.put("mail.smtp.ssl.enable", "true");
        properties.put("mail.smtp.ssl.trust", "smtp.exmail.qq.com");
        properties.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        properties.put("mail.smtp.socketFactory.port", "465");
        properties.put("mail.smtp.starttls.enable", "true");
        properties.put("mail.smtp.starttls.required", "true");
        properties.put("mail.smtp.connectiontimeout", "50000");
        properties.put("mail.smtp.timeout", "30000");
        properties.put("mail.smtp.writetimeout", "50000");

        final JavaMailSenderImpl javaMailSender = new JavaMailSenderImpl();
        applyProperties(mailProperties, javaMailSender);

        final SimpleMailMessage simpleMessage = new SimpleMailMessage();
        simpleMessage.setFrom("<EMAIL>");
        simpleMessage.setTo("<EMAIL>");
        simpleMessage.setSubject("入驻活动价格确认2");
        simpleMessage.setText("【老爸评测】老爸评测：尊敬的商家入驻合作供应商，当前合作价格发生变动，请及时查看并确认！ 价格确认请点击：https://l.lb6.co/qu6ufm\n");

        javaMailSender.send(simpleMessage);

    }

    private void applyProperties(MailProperties properties, JavaMailSenderImpl sender) {
        sender.setHost(properties.getHost());
        if (properties.getPort() != null) {
            sender.setPort(properties.getPort());
        }
        sender.setUsername(properties.getUsername());
        sender.setPassword(properties.getPassword());
        sender.setProtocol(properties.getProtocol());
        if (properties.getDefaultEncoding() != null) {
            sender.setDefaultEncoding(properties.getDefaultEncoding().name());
        }
        if (!properties.getProperties().isEmpty()) {
            sender.setJavaMailProperties(asProperties(properties.getProperties()));
        }
    }

    private Properties asProperties(Map<String, String> source) {
        Properties properties = new Properties();
        properties.putAll(source);
        return properties;
    }
}
