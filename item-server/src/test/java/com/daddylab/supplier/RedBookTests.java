package com.daddylab.supplier;

import com.xiaohongshu.fls.opensdk.client.InventoryClient;
import com.xiaohongshu.fls.opensdk.client.OauthClient;
import com.xiaohongshu.fls.opensdk.client.ProductClient;
import com.xiaohongshu.fls.opensdk.entity.BaseResponse;
import com.xiaohongshu.fls.opensdk.entity.inventory.request.GetSkuStockRequest;
import com.xiaohongshu.fls.opensdk.entity.inventory.response.SkuStockResponse;
import com.xiaohongshu.fls.opensdk.entity.oauth.request.GetAccessTokenRequest;
import com.xiaohongshu.fls.opensdk.entity.product.request.v3.GetDetailSkuRequest;
import com.xiaohongshu.fls.opensdk.entity.product.response.v3.GetDetailSkuListResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.junit.Test;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @class RedBookTests.java
 * @description 小红书测试
 * @date 2024-02-27 14:11
 */
@Slf4j
public class RedBookTests {

    private final static String APP_URL = "https://ark.xiaohongshu.com/ark/open_api/v3/common_controller";
    private final static String APP_KEY = "88e37fea483d4875b09e";
    private final static String APP_SECRET = "f518d419f5290c69d1229f9a344e6d10";
    private final static String APP_VERSION = "2.0";

    static String ACCESS_TOKEN = "token-0dff5ca49cd84c47a726816f4d4c50f2-f8d7eaa6b5b843b7a7418acadf44654a";
    @Test
    public void test() {
        OauthClient oauthClient = new OauthClient(APP_URL, APP_KEY,  APP_VERSION, APP_SECRET);
        GetAccessTokenRequest getAccessTokenRequest = new GetAccessTokenRequest();

        GetDetailSkuRequest detailSkuRequest = new GetDetailSkuRequest();
        detailSkuRequest.setPageNo(1);
        detailSkuRequest.setPageSize(20);
        detailSkuRequest.setId("65de96d8be97c300013e858d");
        ProductClient client = new ProductClient(APP_URL, APP_KEY,  APP_VERSION, APP_SECRET);
        try {
            BaseResponse<GetDetailSkuListResponse> execute = client.execute(detailSkuRequest, ACCESS_TOKEN);
            if (!execute.isSuccess()) {
                throw new IllegalArgumentException(execute.toString());
            }
            log.info("[获取到的商品列表信息] itemLists={}", execute.getData().getData());

            List<GetDetailSkuListResponse.Product> product = execute.getData().getData();

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    @Test
    public void test1() {
        String skuId = "";
        //获取库存
        InventoryClient inventoryClient = new InventoryClient(APP_URL, APP_KEY,  APP_VERSION, APP_SECRET);
        GetSkuStockRequest getItemStockRequest = new GetSkuStockRequest();
        getItemStockRequest.setSkuId(skuId);
        BaseResponse<SkuStockResponse> execute1 = null;
        try {
            execute1 = inventoryClient.execute(getItemStockRequest, ACCESS_TOKEN);
        } catch (IOException e) {
            e.printStackTrace();
        }
        assert execute1 != null;
        log.info("[获取到的商品列表信息] stock={}", execute1.getData().getSkuStock());
    }

    @Test
    public void test2() {
       String message = "[{\\\"data\\\":\\\"{\\\\\\\"itemId\\\\\\\":\\\\\\\"65e04cc088bacf0001d00c2b\\\\\\\",\\\\\\\"updateTime\\\\\\\":1709198528000,\\\\\\\"skuId\\\\\\\":\\\\\\\"65e04cc088bacf0001d00c2b\\\\\\\"}\\\",\\\"msgTag\\\":\\\"msg_item_create\\\",\\\"sellerId\\\":\\\"60f7fcef757c640001b8a41a\\\"}]";
        String s = StringEscapeUtils.unescapeJava(message);
        System.out.println(
                s
        );
    }
}
